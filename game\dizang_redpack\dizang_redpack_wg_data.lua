DiZangRedPackWGData = DiZangRedPackWGData or BaseClass()
function DiZangRedPackWGData:__init()
	if DiZangRedPackWGData.Instance then
		error("[DiZangRedPackWGData] Attempt to create singleton twice!")
		return
	end
	DiZangRedPackWGData.Instance = self

    local cfg = ConfigManager.Instance:GetAutoConfig("jizo_gift_cfg_auto")

    self.reward_map_cfg = {}
    for k,v in ipairs(cfg.reward) do
        local data = {
            seq = v.seq,
            start_num = v.start_num,
            finish_num = v.finish_num,
            title = v.title,
            reward_item = v.reward_item,
        }

        local cfg_list = Split(v.task_ids, "|")
        local trans_list = {}
        for i,j in ipairs(cfg_list) do
            trans_list[i] = tonumber(j)
        end

        data.task_id_list = trans_list
        self.reward_map_cfg[v.seq] = data
    end

    self.task_map_cfg = ListToMap(cfg.task, "task_id")
    self.other_cfg = cfg.other[1]

    self.seq = -1
    self.has_fetch = false
    self.accept_time = 0
    self.task_list = {}
    self.record_list = {}
end

function DiZangRedPackWGData:__delete()
    DiZangRedPackWGData.Instance = nil
end

function DiZangRedPackWGData:GetOtherCfgData(key)
    return self.other_cfg[key]
end

function DiZangRedPackWGData:GetTaskCfg(id)
    return self.task_map_cfg[id]
end

function DiZangRedPackWGData:SetInfo(protocol)
    self.seq = protocol.seq
    self.has_fetch = protocol.has_fetch
    self.accept_time = protocol.accept_time
    self.task_list = protocol.task_list
end

function DiZangRedPackWGData:SetRecordInfo(protocol)
    self.record_list = protocol.record_list
end

function DiZangRedPackWGData:SetSingleRecordInfo(protocol)
    table.insert(self.record_list, protocol.record_info)
end

function DiZangRedPackWGData:GetCurSeq()
    return self.seq
end

function DiZangRedPackWGData:GetIsFetch()
    return self.has_fetch == 1
end

function DiZangRedPackWGData:GetStatus()
    return self.accept_time and self.accept_time > 0
end

function DiZangRedPackWGData:GetTaskInfo(id)
    return self.task_list[id]
end

function DiZangRedPackWGData:GetRecordList()
    return self.record_list
end

function DiZangRedPackWGData:GetTimeRemaining()
    local task_time = self:GetOtherCfgData("task_time")
    local remain_time = 0
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    if task_time > 0 then
        local total_time = self.accept_time + task_time
        remain_time = total_time - server_time
    else
        remain_time = TimeUtil.GetTodayRestTime(server_time)
    end

    return remain_time > 0 and remain_time or 0
end

function DiZangRedPackWGData:GetCurRewardCfg()
    return self.reward_map_cfg[self.seq]
end

function DiZangRedPackWGData:GetRemind()
    if self:GetIsFetch() then
        return false
    end

    local cfg = self:GetCurRewardCfg()
    if not cfg then
        return false
    end

    for k, id in pairs(cfg.task_id_list) do
        local info = self:GetTaskInfo(id)
        if info and info.is_finish ~= 1 then
            return false
        end
    end

    return true
end

function DiZangRedPackWGData:CheckShowRedpack()
    local open_level = self:GetOtherCfgData("open_level")
    local open_daily_work_exp = self:GetOtherCfgData("open_daily_work_exp")
    local role_lev = GameVoManager.Instance:GetMainRoleVo().level or 0
    local cur_huoyue_exp = BiZuoWGData.Instance:GetTotalExp() or 0
    if role_lev >= open_level and cur_huoyue_exp >= open_daily_work_exp then
        return true
    end

    return false
end

