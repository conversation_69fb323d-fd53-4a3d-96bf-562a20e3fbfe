------------------------------------------------------------
--骑宠装备tips、对比tips
------------------------------------------------------------
MountPetEquipCompareTip = MountPetEquipCompareTip or BaseClass(SafeBaseView)

function MountPetEquipCompareTip:__init()
	self.view_layer = UiLayer.Pop
	self.view_name = "MountPetEquipCompareTip"
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/shenshou_ui_prefab", "layout_itemtip")

	self.equip_data_1 = nil
	self.equip_data_2 = nil
end

function MountPetEquipCompareTip:__delete()

end

function MountPetEquipCompareTip:ReleaseCallBack()
	if self.mount_pet_equip_tip1 then
        self.mount_pet_equip_tip1:DeleteMe()
     	self.mount_pet_equip_tip1 = nil
   	end

   	if self.mount_pet_equip_tip2 then
        self.mount_pet_equip_tip2:DeleteMe()
     	self.mount_pet_equip_tip2 = nil
   	end
	self.equip_data_list = nil
    self.item_cell1 = nil
    self.item_cell2 = nil
end

function MountPetEquipCompareTip:LoadCallBack()
	self.mount_pet_equip_tip1 = BaseTip.New(self.node_list["base_tip_root_1"])
	self.item_cell1 = self.mount_pet_equip_tip1:GetItemCell()
	self.mount_pet_equip_tip1:SetHideScrollBar(true)
	self.mount_pet_equip_tip2 = BaseTip.New(self.node_list["base_tip_root_2"])
	self.item_cell2 = self.mount_pet_equip_tip2:GetItemCell()
    self.mount_pet_equip_tip2:SetHideScrollBar(true)
    self:InitGetWayBtn()
end

function MountPetEquipCompareTip:InitGetWayBtn()
    local btn = self.mount_pet_equip_tip2:GetGetWayBtn()
    if btn then
        btn.button:AddClickListener(BindTool.Bind(self.OnClickOpenGetWayPanel, self))
    end
end

function MountPetEquipCompareTip:OnClickOpenGetWayPanel()
    local obj = self.mount_pet_equip_tip2:GetTipsObjByType("get_way")
    if obj then
        obj:SetActive(not obj.activeSelf)
    end
    local is_active = obj and obj.activeSelf
    self.mount_pet_equip_tip2:SetGetWayRootActive(is_active)
    self.mount_pet_equip_tip1:SetGetWayRootActive(is_active)
end

--类型，职业
function MountPetEquipCompareTip:GetEquipTitleNormalInfo(data)
    local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
    local base_cfg, equip_type = MountLingChongEquipWGData.Instance:GetMountLingChongEquipData(data.item_id)
	if not item_cfg or not base_cfg then
		return nil
	end
    local info = {}
    info.equip_type_str = string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE, Language.F2Tip.MountPetEquipType[equip_type])
    local limit_sex = item_cfg.limit_sex or 0
    local rich_type_text = Language.Common.ProfName[limit_sex][item_cfg.limit_prof]
    info.prof_type_str = string.format(Language.Tip.ZhuangBeiProf, TIPS_COLOR.SOCRE, rich_type_text)
	return info
end

--基础属性 {{attr_name = "", attr_value = ""}}
function MountPetEquipCompareTip:GetBaseInfo(data)
    local base_cfg, equip_type = MountLingChongEquipWGData.Instance:GetMountLingChongEquipData(data.item_id)
	if not base_cfg then
		return nil
    end

    local strength_cfg
    if data.strengthen_level and data.strengthen_level > 0 then
        strength_cfg = MountLingChongEquipWGData.Instance:GetStrengthCfgByLevel(equip_type, base_cfg.part, data.strengthen_level)
    end

    local info = {}
    for k = 0, 4 do --最多五条
        if base_cfg["attr_type_"..k] then
            local data = {}
            local attr_name = EquipmentWGData.Instance:GetAttrStrByAttrId(base_cfg["attr_type_"..k])
            data.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_name, true)
            local attr_value = base_cfg["attr_value_"..k] or 0
			local add_str = ""
            if attr_value ~= 0 then
                if strength_cfg ~= nil then
                    add_str = string.format(Language.F2Tip.AttrEnhance, COLOR3B.DEFAULT_NUM, strength_cfg["attr_value_"..k])
                end

                data.attr_value = attr_value
				data.add_str = add_str
                info[#info+1] = data
            end
        end
    end

    return info
end

--星级属性 {{attr_name = "", attr_value = ""}}
function MountPetEquipCompareTip:GetStarInfo(data)
    local base_cfg, equip_type = MountLingChongEquipWGData.Instance:GetMountLingChongEquipData(data.item_id)
    local attr, equip_add_percent = MountLingChongEquipWGData.Instance:GetStarAttrByLevel(equip_type, base_cfg.part, data.star_level)
	if not base_cfg or IsEmptyTable(attr) then
		return nil
	end
    local info = {}
    for k, v in pairs(attr) do
        if v.attr_type then
            local data = {}
            local attr_name = EquipmentWGData.Instance:GetAttrStrByAttrId(v.attr_type)
            data.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_name)
            local attr_value = v.attr_value or 0
            if attr_value ~= 0 then
                data.attr_value = AttributeMgr.PerAttrValue(attr_name, attr_value)
                info[#info+1] = data
            end
        end
    end

    if equip_add_percent ~= 0 then
        local attr_name = Language.MountPetEquip.EquipAddPercent
        local attr_value = equip_add_percent/100 .. "%"
        local data = {}
        data.attr_name = attr_name
        data.attr_value = attr_value
        info[#info+1] = data
    end
    return info
end

function MountPetEquipCompareTip:GetQualityEnhanceInfo(data)
    local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
    local base_cfg, equip_type = MountLingChongEquipWGData.Instance:GetMountLingChongEquipData(data.item_id)
	if not item_cfg or not base_cfg then
		return nil
	end
    local info = {}
    for k = 0, 4 do --最多五条
        if base_cfg.precentage and base_cfg.precentage ~= 0 then
            info.addition_str = string.format(Language.F2Tip.MountPetEquipEnhance, ITEM_COLOR[item_cfg.color], base_cfg.precentage/100 .. "%")
        end
    end
    return info
end

--等级，评分
function MountPetEquipCompareTip:SetEquipNormalInfo1(pingfen_1, common_pingfen_1)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.my_data.item_id)
	local normal_attr_info = {}
	local str_limit_level = RoleWGData.GetLevelString(item_cfg.limit_level)
	normal_attr_info[1] = {name = Language.F2Tip.LvName, label = str_limit_level}
	normal_attr_info[2] = {name = Language.F2Tip.PingFen, label = math.ceil(pingfen_1)}
    --normal_attr_info[3] = {name = Language.F2Tip.CompPingFen, label = math.ceil(common_pingfen_1)}
	self.mount_pet_equip_tip1:SetNormalAttribute(normal_attr_info)
end

--等级，评分
function MountPetEquipCompareTip:SetEquipNormalInfo2(pingfen_2, common_pingfen_2)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.equip_data.item_id)
	local normal_attr_info = {}
	local str_limit_level = RoleWGData.GetLevelString(item_cfg.limit_level)
	normal_attr_info[1] = {name = Language.F2Tip.LvName, label = str_limit_level}
	normal_attr_info[2] = {name = Language.F2Tip.PingFen, label = math.ceil(pingfen_2)}
    --normal_attr_info[3] = {name = Language.F2Tip.CompPingFen, label = math.ceil(common_pingfen_2)}
	self.mount_pet_equip_tip2:SetNormalAttribute(normal_attr_info)
end

--SetStarAttribute 星级属性
--SetMountPetEquipQualityAdditionInfo 品质加成
function MountPetEquipCompareTip:OnFlush()
	self.my_data = {}
    self.is_show_compare = false
    if self.fromView == ItemTip.FROM_MOUNTEQUIP_EQUIP then --来自穿戴的，可能显示星级，强化
        self.node_list["base_tip_root_1"]:SetActive(false)
    else
        if self.equip_data then
            local base_cfg, show_type = MountLingChongEquipWGData.Instance:GetMountLingChongEquipData(self.equip_data.item_id)
            self.my_data = MountLingChongEquipWGData.Instance:GetEquipInfoByPart(show_type, base_cfg and base_cfg.part) or {}
            if self.my_data and self.my_data.item_id and self.my_data.item_id > 0 then
                self.node_list["base_tip_root_1"]:SetActive(true)
                self.is_show_compare = true
            else
                self.node_list["base_tip_root_1"]:SetActive(false)
            end
        else
            self.node_list["base_tip_root_1"]:SetActive(false)
        end
    end
	self:FlushTipsShow2()
	self:FlushTipsShow1()
end

function MountPetEquipCompareTip:SetEquipData(data,fromView,param_t,item_pos_x,btn_callback_event)
	self.equip_data = data
	self.fromView = fromView
	self.param_t = param_t
	self.item_pos_x = item_pos_x
	self.btn_callback_event = btn_callback_event
	self:Open()
end

function MountPetEquipCompareTip:FlushTipsShow1()
	if IsEmptyTable(self.my_data) or not self.my_data.item_id then return end
     --设置物品cell
    self.mount_pet_equip_tip1:Reset()
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.my_data.item_id)
	if not item_cfg or not item_cfg.name then return end

    if self.fromView ~= ItemTip.FROM_MOUNTEQUIP_EQUIP then --来自穿戴的，可能显示星级，强化
        self.mount_pet_equip_tip1:SetTopLeftIcon("a1_biaoqian_zbz")
    end

    if self.item_cell1 then
        self.item_cell1:SetData(self.my_data)
        self.item_cell1:SetLeftTopImg(self.my_data.star_level or 0) --星级，只会出现在已经装备了的

        local base_cfg, show_type = MountLingChongEquipWGData.Instance:GetMountLingChongEquipData(self.equip_data.item_id)
        local is_up = MountLingChongEquipWGData.Instance:IsBetterThanCurWearing(show_type, self.my_data.item_id)
        self.item_cell1:SetUpFlagIconVisible(is_up)

        if self.my_data.strengthen_level and self.my_data.strengthen_level > 0 then
            self.item_cell1:SetRightBottomTextVisible(true)
            self.item_cell1:SetRightBottomColorText("+" .. self.my_data.strengthen_level)
        else
            self.item_cell1:SetRightBottomColorText("")
        end
        self.item_cell1:SetLeftTopTextVisible(false)
    end

	local str = ""
	str = item_cfg.name
	self.mount_pet_equip_tip1:SetItemName(ToColorStr(str, ITEM_COLOR[item_cfg.color]))
	self.mount_pet_equip_tip1:SetTopColorBg(item_cfg.color or 0)
    self.mount_pet_equip_tip1:SetOrnamentImage(item_cfg.color or 0, item_cfg.special_border or 0)

    local info = self:GetEquipTitleNormalInfo(self.my_data)
    self.mount_pet_equip_tip1:SetEquipSocre(info.equip_type_str)
	self.mount_pet_equip_tip1:SetSyntheticalSocre(info.prof_type_str)
    local info = self:GetBaseInfo(self.my_data)
    self.mount_pet_equip_tip1:SetBaseAttribute(info)

    --星级属性
    if self.my_data.star_level and self.my_data.star_level > 0 then
        local star_info = self:GetStarInfo(self.my_data)
        self.mount_pet_equip_tip1:SetStarAttribute(star_info)
    end

    local enhance_info = self:GetQualityEnhanceInfo(self.my_data)
    if not IsEmptyTable(enhance_info) then
        self.mount_pet_equip_tip1:SetMountPetEquipQualityAdditionInfo(enhance_info)
    end

    --套装属性
    local suit_info = MountLingChongEquipWGData.Instance:GetSuitInfoByItemId(self.my_data.item_id)
    self.mount_pet_equip_tip1:SetXianqSuitAttribute(suit_info)

    local common_pingfen_1, pingfen_1 = MountLingChongEquipWGData.Instance:GetScoreByData(self.my_data)
    self:SetEquipNormalInfo1(pingfen_1, common_pingfen_1)
    local capa = MountLingChongEquipWGData.Instance:GetCapabilityByPartData(self.my_data)
    local capa_info = {}
    capa_info.capability = capa
    self.mount_pet_equip_tip1:SetCapabilityPanel(capa_info)
	self:ShowItemGetDesc(item_cfg,1)
end

function MountPetEquipCompareTip:FlushTipsShow2()
    if IsEmptyTable(self.equip_data) or not self.equip_data.item_id then return end
	self.mount_pet_equip_tip2:Reset()
    --设置物品cell
    if self.fromView == ItemTip.FROM_MOUNTEQUIP_EQUIP then --来自穿戴的，可能显示星级，强化
        self.mount_pet_equip_tip2:SetTopLeftIcon("a1_biaoqian_zbz")
    end

	local item_cfg = ItemWGData.Instance:GetItemConfig(self.equip_data.item_id)
    if self.item_cell2 then
        self.item_cell2:SetData(self.equip_data)
        self.item_cell2:SetLeftTopImg(self.equip_data.star_level or 0) --星级，只会出现在已经装备了的
        local base_cfg, show_type = MountLingChongEquipWGData.Instance:GetMountLingChongEquipData(self.equip_data.item_id)
        local is_up = MountLingChongEquipWGData.Instance:IsBetterThanCurWearing(show_type, self.equip_data.item_id)
        self.item_cell2:SetUpFlagIconVisible(is_up)

        if self.equip_data.strengthen_level and self.equip_data.strengthen_level > 0 then
            self.item_cell2:SetRightBottomTextVisible(true)
            self.item_cell2:SetRightBottomColorText("+" .. self.equip_data.strengthen_level)
        else
            self.item_cell2:SetRightBottomColorText("")
        end
    end

	local str = ""
	str = item_cfg.name
	self.mount_pet_equip_tip2:SetItemName(ToColorStr(str, ITEM_COLOR[item_cfg.color]))
	self.mount_pet_equip_tip2:SetTopColorBg(item_cfg.color or 0)
    self.mount_pet_equip_tip2:SetOrnamentImage(item_cfg.color or 0, item_cfg.special_border or 0)

    --套装属性
    local suit_info = MountLingChongEquipWGData.Instance:GetSuitInfoByItemId(self.equip_data.item_id)
    self.mount_pet_equip_tip2:SetXianqSuitAttribute(suit_info)

    local info = self:GetEquipTitleNormalInfo(self.equip_data)
    self.mount_pet_equip_tip2:SetEquipSocre(info.equip_type_str)
    self.mount_pet_equip_tip2:SetSyntheticalSocre(info.prof_type_str)
    local info1 = self:GetBaseInfo(self.equip_data)
    self.mount_pet_equip_tip2:SetBaseAttribute(info1)
     --星级属性
    if self.equip_data.star_level and self.equip_data.star_level > 0 then
        local star_info = self:GetStarInfo(self.equip_data)
        self.mount_pet_equip_tip2:SetStarAttribute(star_info)
    end

    local enhance_info = self:GetQualityEnhanceInfo(self.equip_data)
    if not IsEmptyTable(enhance_info) then
        self.mount_pet_equip_tip2:SetMountPetEquipQualityAdditionInfo(enhance_info)
    end

    local common_pingfen_2, pingfen_2 = MountLingChongEquipWGData.Instance:GetScoreByData(self.equip_data)
    self:SetEquipNormalInfo2(pingfen_2, common_pingfen_2)
    local capa = MountLingChongEquipWGData.Instance:GetCapabilityByPartData(self.equip_data)
    local capa_info = {}
    capa_info.capability = capa
    self.mount_pet_equip_tip2:SetCapabilityPanel(capa_info)
    --设置按钮
    local handle_types = TipWGData.Instance:GetOperationLabelByType(self.equip_data, self.from_view)
	local btn_info_list = {}

	if not IsEmptyTable(handle_types) then
		for i, v in ipairs(handle_types) do
			local temp = {}
			temp.btn_name = Language.Tip.ButtonLabel[v]
			temp.btn_click = BindTool.Bind2(self.OperationClickHandler, self, v)
			table.insert(btn_info_list, 1, temp)
		end
	end

	if self.btn_callback_event then
		for i,v in ipairs(self.btn_callback_event) do
			local temp = {}
			temp.btn_name = v.btn_text
			temp.btn_red = v.btn_red or 0
			temp.btn_click = BindTool.Bind2(self.OperationClickHandler, self, i)
			btn_info_list[#btn_info_list + 1] = temp
		end
	end

    if not IsEmptyTable(btn_info_list) then
	    self.mount_pet_equip_tip2:SetBtnsClick(btn_info_list)
    end
    
    self:ShowItemGetDesc(item_cfg,2)
	if not IsEmptyTable(self.equip_data) and self.equip_data.item_id then
		self:FlushGetWayView()
	end
end

function MountPetEquipCompareTip:FlushGetWayView()
	local data_lits = TipWGData.Instance:GetGetWayList(self.equip_data.item_id)
	self.data_lits = data_lits
	local path_list = {}
	for i,data in ipairs(data_lits) do
		if data.ShowType == ItemTipGoods.ShowType.Type2 then
            local temp = {label = data.cfg.open_name, btn_click = BindTool.Bind(self.OnClickGoToOpenPanel, self, i)}
			path_list[#path_list + 1] = temp
		end
    end
    if #path_list > 0 then
		self.mount_pet_equip_tip2:SetGetWayPanel({path_list = path_list, is_on = self.from_view == ItemTip.FROM_MOUNTEQUIP_BAG
																	and self.from_view == ItemTip.FROM_MOUNTEQUIP_EQUIP})
	end
end

function MountPetEquipCompareTip:OnClickGoToOpenPanel(index)
	local data = self.data_lits[index]
	if not data or data.ShowType ~= ItemTipGoods.ShowType.Type2 or not data.cfg then
		return
	end

	local open_panel = data.cfg.open_panel
	local open_panel_table = Split(data.cfg.open_panel,"#")
	if open_panel == GuideModuleName.Compose then
		FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, nil, {is_stuff = false, item_id = data.item_id})
	elseif open_panel == "guaji" then  						--挂机按钮点击去挂机
		TaskWGData.Instance:GuaJiGetMonster()
	elseif open_panel == "GuildJuanXian" then
		local isnot_join_guild = RoleWGData.Instance.role_vo.guild_id == 0
		if isnot_join_guild then
			FunOpen.Instance:OpenViewByName(GuideModuleName.Guild, TabIndex.guild_guildlist)
			TipWGCtrl.Instance:ShowSystemMsg(Language.Common.CanNotEnterNoGuild)
		else
			GuildWGCtrl.Instance:OpenGuildJuanXinaView()
		end
	elseif open_panel_table[1] == "shop" then
		local tab_index = 0
		if open_panel_table[2] then
			tab_index = ShopWGData.ShowTypeToIndex[open_panel_table[2]]
		else
			tab_index = ShopWGData.Instance:GetItemSellTabIndex(data.item_id)
		end
		ShopWGCtrl.Instance:ShopJumpToItemByIDAndTabIndex(data.item_id,tab_index)
		--ViewManager.Instance:Open(GuideModuleName.Shop, tab_index, "select_item_id", {item_id = data.item_id})
	elseif open_panel == "boss_xiezhu" then
		BossAssistWGCtrl.Instance:Open()
	else
		FunOpen.Instance:OpenViewNameByCfg(open_panel)
	end

	local close_event = TipWGCtrl.Instance:GetCloseOtherView()
	if nil ~= close_event then
		close_event()
		TipWGCtrl.Instance:CloseOtherView(nil)
	end
	self:Close()
end

--显示获得途径
function MountPetEquipCompareTip:ShowItemGetDesc(item_cfg,index)
	local info_list = {}
	if (item_cfg.get_msg and 0 < string.len(item_cfg.get_msg)) or not item_cfg.get_way or 0 == string.len(item_cfg.get_way) then
		local get_msg = item_cfg.get_msg
		if not get_msg or 0 == string.len(get_msg) then
			return
		end
		if item_cfg.is_display_role ~= 0 then
			info_list.desc = ToColorStr(string.format(Language.Common.TipsBrackets, get_msg), TIPS_COLOR.GETWAY_VALUE)
		else
			info_list.desc = ToColorStr(string.format(Language.Common.TipsBrackets, get_msg), TIPS_COLOR.GETWAY_VALUE)
		end
	elseif (item_cfg.get_way_des and 0 < string.len(item_cfg.get_way_des)) then
		local get_msg = item_cfg.get_way_des
		if not get_msg or 0 == string.len(get_msg) then
			return
		end
		info_list.desc = ToColorStr(string.format(Language.Common.TipsBrackets, get_msg), TIPS_COLOR.GETWAY_VALUE)
	end
	if item_cfg.xiushici and item_cfg.xiushici ~= "" then
		info_list.xiushici = item_cfg.xiushici
    end
	if not IsEmptyTable(info_list) then
		info_list.title = Language.F2Tip.GetWayStr
		self["mount_pet_equip_tip"..index]:SetGetWayDesc(info_list)
	end
end

function MountPetEquipCompareTip:OperationClickHandler(index)
    if index == ItemTip.QUICK_ADDITEM then
		local cur_data_conf = ItemWGData.Instance:GetItemConfig(self.equip_data.item_id)
		local gm_additem_num = 10
		if cur_data_conf and cur_data_conf.pile_limit and cur_data_conf.pile_limit == 999 then
			gm_additem_num = 999
		end
        SysMsgWGCtrl.SendGmCommand("additem", self.equip_data.item_id .." ".. gm_additem_num .. " " .. 0)
        self:Close()
    else
        if self.btn_callback_event and self.btn_callback_event[index] then
            self.btn_callback_event[index].callback()
            self:Close()
        end
    end
end
