----------------------------------------------------
-- 角色信息展示带装备，带展示。如人物面板上的
----------------------------------------------------
require("game/hidden_weapon/utils/hidden_weapon_role_info_handler")

RoleInfoView = RoleInfoView or BaseClass(BaseRender)
local XIAOGUI_ITEM_ID = {10100,10200}
function RoleInfoView:__init(parent)
	self.node_list = parent
	self.is_main_role = true
	self.view = self.node_list
	self.is_show_role_capability = true
	self.select_role_equip_body_seq = -1

	self.equip_grid = {}
	--装备网格
	local color = Color.New(0,0,0,0)
	for i = 0, GameEnum.EQUIP_INDEX_XIANZHUO do
		self.equip_grid[i] = DiamondItemCell.New(self.node_list["ph_equip_cell_" .. i])
		if self.node_list["img_add_cell_" .. i] then
			self.node_list["img_add_cell_" .. i].button:AddClickListener(BindTool.Bind(self.OnClickImgAdd, self, i))
		end
		self.equip_grid[i]:SetItemTipFrom(ItemTip.FROM_BAG_EQUIP)
		self.equip_grid[i]:SetClickCallBack(BindTool.Bind1(self.SelectCellCallBack, self))

		if i == GameEnum.EQUIP_INDEX_WUQI then
			self.equip_grid[i]:SetItemIconLocalScale(0.7)
		end


		-- self.equip_grid[i].bg_img:SetActive(false)

		self.equip_grid[i]:SetButtonComp(true)
		self.equip_grid[i]:SetRightTopImageTextActive(false)
		-- self.equip_grid[i].root_node.image.color = color
		-- self.equip_grid[i]:SetPartName(Language.Bag.TabBoxs[i])
    end

	self.xiao_gui_list = {}
	self.xiao_gui_lock = {}
	for i = 1, 2 do
		if self.node_list["img_add_cell_1" .. i] then
			self.node_list["img_add_cell_1" .. i].button:AddClickListener(BindTool.Bind(self.OnClickImgAdd, self, 10+i))
		end
		self.xiao_gui_list[i] = DiamondItemCell.New(self.node_list["ph_equip_cell_xiaogui_" .. i])
		-- self.xiao_gui_list[i].root_node.rect:SetAsFirstSibling()
		-- self.xiao_gui_list[i].root_node.image.color = color
		self.xiao_gui_list[i]:SetRightTopImageTextActive(false)
		-- self.xiao_gui_item.bg_img:SetActive(false)
		self.xiao_gui_list[i]:SetItemTipFrom(ItemTip.FROM_PUTON_XIAOGUI)
		self.xiao_gui_list[i]:SetClickCallBack(BindTool.Bind1(self.SelectXiaoGuiCallBack, self))
		self.xiao_gui_list[i]:SetItemIcon(ResPath.GetCommonIcon("a3_zb_1"..i))		--守护小鬼图片11		经验小鬼图片12
		self.xiao_gui_list[i]:SetItemIconLocalScale(0.7)
		self.xiao_gui_lock[self.xiao_gui_list[i]] = false
	end

	self.node_list.remind:SetActive(false)

	self.equip_data_change_fun = BindTool.Bind1(self.OnEquipDataChange, self)
	self.equip_datalist_change_fun = BindTool.Bind1(self.OnEquipDataListChange, self)
	-- self.item_data_change_fun = BindTool.Bind1(self.OnItemDataChange,self)
	self.xiaogui_data_change_fun = BindTool.Bind1(self.OnXiaoGuiDataChange, self)

	EquipWGData.Instance:NotifyDataChangeCallBack(self.equip_data_change_fun)
	EquipWGData.Instance:NotifyDataChangeCallBack(self.equip_datalist_change_fun,true)
	-- ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_fun)
	EquipWGData.Instance:NotifyXiaoGuiChangeCallBack(self.xiaogui_data_change_fun)

	self:BindGlobalEvent(ObjectEventType.MAIN_ROLE_APPERANCE_CHANGE, BindTool.Bind1(self.ApperanceChange, self))

	self:SetGridStyle()
	self:SetRoleData()
end

local virtul_item_id = {
	-- [6] = 90627,
	-- [8] = 90625,
	-- [9] = 90626,
	[10] = 90628,
	[11] = 10100,
	[12] = 10200,
}

-- 仙器虚拟物品
RoleInfoView.xianqi_virtul_item_id = {
	[90625] = true, 		-- 仙戒
	[90626] = true,			-- 仙镯
	[90627] = true,			-- 仙坠
}

function RoleInfoView:ParentViewOpenCallBack()

end

function RoleInfoView:OnClickImgAdd(index)
	if index == 11 or index == 12 then
		for i=1,#self.xiao_gui_list do
			local data = self.xiao_gui_list[i]:GetData()
			if not IsEmptyTable(data) then
				local xiaogui_cfg = EquipmentWGData.Instance:GetGuardCfgByItemID(data.item_id)
				if xiaogui_cfg then
					if xiaogui_cfg.impguard_type == 1 then
						TipWGCtrl.Instance:OpenItemTipGetWay({item_id = virtul_item_id[12]})
					else
						TipWGCtrl.Instance:OpenItemTipGetWay({item_id = virtul_item_id[11]})
					end
					return
				end
			end
		end
	end

	local target_item_id = EquipBodyWGData.Instance:GetCurEquipShowTip(self.select_role_equip_body_seq, index)
	
	if target_item_id > 0 then
		TipWGCtrl.Instance:OpenItem({item_id = target_item_id})
		return
	end

	if virtul_item_id[index] then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = virtul_item_id[index]})
	end
end

function RoleInfoView:PlayBtnGroupAni(btn_group_list, ison, max_move, min_move, delay_time)
	if nil == btn_group_list then
		return
	end

	local len = GetTableLen(btn_group_list)
	local time = len * 0.15
	local move_dis = ison and -1 or 1
	local max_move = max_move
	local delay_time = delay_time or 0
	local num = 1
	for i = len, 1, -1 do
		local btn_root = btn_group_list[i]
		if btn_root and btn_root.gameObject.activeSelf then
			local min_move = min_move
			if nil == min_move then
				min_move = max_move - 100 * num
			end
			num = num + 1
			time = time - 0.1
			local move_vaule = move_dis == 1 and max_move or min_move
			local tween = btn_root.rect:DOAnchorPosX(move_vaule, time)
			tween:SetEase(DG.Tweening.Ease.OutBack)
			tween:SetDelay(delay_time)
			tween:OnUpdate(function()
				btn_root.canvas_group.alpha = ((max_move - min_move) - (btn_root.rect.anchoredPosition.x - min_move)) / (max_move - min_move)
			end)
		end
	end
end

function RoleInfoView:__delete()
	for k,v in pairs(self.equip_grid) do
		v:DeleteMe()
	end
	self.equip_grid = {}
	self.xiao_gui_lock = {}

	if self.equip_cell_xiaogui then
		self.equip_cell_xiaogui:DeleteMe()
		self.equip_cell_xiaogui = nil
	end

	if self.role_display then
		self.role_display:DeleteMe()
		self.role_display = nil
	end

	for k,v in pairs(self.xiao_gui_list) do
		v:DeleteMe()
	end
	self.xiao_gui_list = {}

	if EquipWGData.Instance then
		EquipWGData.Instance:UnNotifyDataChangeCallBack(self.equip_data_change_fun)
		EquipWGData.Instance:UnNotifyDataChangeCallBack(self.equip_datalist_change_fun)
		EquipWGData.Instance:UnNotifyXiaoGuiChangeCallBack(self.xiaogui_data_change_fun)
	end

	-- if ItemWGData.Instance then
	-- 	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_fun)
	-- end
end

function RoleInfoView:GetView()
	return self.view
end

--设置人物数据
function RoleInfoView:SetRoleData()
	self:OnEquipDataListChange()
	local imp_guard_info = EquipWGData.Instance:GetmpGuardInfo()
	local is_huanhua = false
	for i=1,#imp_guard_info.item_wrapper do
		local data = imp_guard_info.item_wrapper[i]
		if data and data.item_id and data.item_id > 0 then
			-- 免费守护和守护存在imp_type类型一致的问题，无法通过协议信息区分，直接使用配置中的守护信息
			local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(data.item_id)
			local temp_guard_id = xiaogui_cfg.appe_image_id
			is_huanhua = temp_guard_id == GameVoManager.Instance:GetMainRoleVo().guard_id
			data.pos = i - 1
			self.xiao_gui_list[i]:SetFlushCallBack(
			function ()
				-- self.xiao_gui_list[i]:SetPartName("")
				self.xiao_gui_list[i]:SetButtonComp(true)
			end)
			self.xiao_gui_list[i]:SetData(data)
			self.node_list["img_add_cell_1" .. i]:SetActive(false)
			if is_huanhua then
				-- self.xiao_gui_list[i]:SetTopLeftFlag(true, ResPath.GetCommonImages("a3_ty_yhh1"))
				local bundle, asset = ResPath.GetLoadingPath("a3_ty_yhh3")
				self.xiao_gui_list[i]:SetTopLeftFlag(true, bundle, asset)
			else
				self.xiao_gui_list[i]:SetTopLeftFlag(false)
			end
			self.xiao_gui_list[i]:SetLockImgEnable(false)
		else
			self.xiao_gui_list[i]:SetFlushCallBack(
			function ()
				self.xiao_gui_list[i]:SetItemIconValue(true)
				self.xiao_gui_list[i]:SetTopLeftFlag(false)
				-- if self.node_list.remind then
				-- 	self.node_list.remind:SetActive(EquipWGData.Instance:GetImpguardRemindEnable())
				-- end
				if imp_guard_info['used_imp_type_'..i] >= 0 then
					self.xiao_gui_list[i]:SetItemIcon(ResPath.GetCommonIcon("a3_zb_1"..i))
					self.xiao_gui_lock[self.xiao_gui_list[i]] = false
					-- self.xiao_gui_list[i]:SetPartName(Language.Bag.ShouHu)
					self.node_list["img_add_cell_1" .. i]:SetActive(true)
					self.xiao_gui_list[i]:SetLockImgEnable(false)
				else
					self.xiao_gui_lock[self.xiao_gui_list[i]] = true
					self.xiao_gui_list[i]:SetLockImgEnable(true)
					-- self.xiao_gui_list[i]:SetPartName(Language.Role.JieSuo)
					self.node_list["img_add_cell_1" .. i]:SetActive(false)
				end
				self.xiao_gui_list[i]:SetButtonComp(true)
			end)
			self.xiao_gui_list[i]:SetData(nil)
		end
	end
end

function RoleInfoView:SetGridStyle()
	if not self.equip_grid then
		return
	end
	local celllist = {}
	for i = 0, COMMON_CONSTS.MAX_EQUIPMENT_GRID_NUM - 1 do
		if self.equip_grid[i] then
			self.equip_grid[i]:SetItemIcon(self.GetEquipBg(i))  -- 设置物品图标
			self:SetImagAddState(i, true)
		end
	end
end

function RoleInfoView.GetEquipBg(i)
	return ResPath.GetEquipIcon(i, EquipIcon.RoleBag)
end

function RoleInfoView:GetEquipGrid()
	return self.equip_grid
end

-- 小鬼数据变化
function RoleInfoView:OnXiaoGuiDataChange(item_id,index,reason)
	local imp_guard_info = EquipWGData.Instance:GetmpGuardInfo()
	local is_huanhua = false
	for i=1,#imp_guard_info.item_wrapper do
		local data = imp_guard_info.item_wrapper[i]
		if data and data.item_id and data.item_id > 0 then
			-- 免费守护和守护存在imp_type类型一致的问题，无法通过协议信息区分，直接使用配置中的守护信息判断幻化，其他保持不变
			local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(data.item_id)
			local temp_guard_id = xiaogui_cfg.appe_image_id
			is_huanhua = temp_guard_id == GameVoManager.Instance:GetMainRoleVo().guard_id
			data.pos = i - 1
			self.xiao_gui_list[i]:SetFlushCallBack(
			function ()
				-- self.xiao_gui_list[i]:SetPartName("")
				self.xiao_gui_list[i]:SetButtonComp(true)
			end)
			self.xiao_gui_list[i]:SetData(data)
			self.node_list["img_add_cell_1" .. i]:SetActive(false)
			if is_huanhua then
				-- self.xiao_gui_list[i]:SetTopLeftFlag(true, ResPath.GetCommonImages("a3_ty_yhh1"))
				local bundle, asset = ResPath.GetLoadingPath("a3_ty_yhh3")
				self.xiao_gui_list[i]:SetTopLeftFlag(true, bundle, asset)
			else
				self.xiao_gui_list[i]:SetTopLeftFlag(false)
			end
			self.xiao_gui_list[i]:SetLockImgEnable(false)
		else
			self.xiao_gui_list[i]:SetFlushCallBack(
			function ()
				self.xiao_gui_list[i]:SetItemIconValue(true)
				self.xiao_gui_list[i]:SetTopLeftFlag(false)
				-- if self.node_list.remind then
				-- 	self.node_list.remind:SetActive(EquipWGData.Instance:GetImpguardRemindEnable())
				-- end
				if imp_guard_info['used_imp_type_'..i] >= 0 then
					self.xiao_gui_list[i]:SetItemIcon(ResPath.GetCommonIcon("a3_zb_1"..i))
					self.xiao_gui_lock[self.xiao_gui_list[i]] = false
					-- self.xiao_gui_list[i]:SetPartName(Language.Bag.ShouHu)
					self.node_list["img_add_cell_1" .. i]:SetActive(true)
					self.xiao_gui_list[i]:SetLockImgEnable(false)
				else
					self.xiao_gui_lock[self.xiao_gui_list[i]] = true
					self.xiao_gui_list[i]:SetLockImgEnable(true)
					-- self.xiao_gui_list[i]:SetPartName(Language.Role.JieSuo)
					self.node_list["img_add_cell_1" .. i]:SetActive(false)
				end
				self.xiao_gui_list[i]:SetButtonComp(true)
			end)
			self.xiao_gui_list[i]:SetData(nil)
		end
	end
	-- 刷新下背包数据
	ViewManager.Instance:FlushView(GuideModuleName.Bag, TabIndex.rolebag_bag_all, "itemdata_list_change")
end

function RoleInfoView:SetImagAddState(i, state)
	if self.node_list["img_add_cell_" .. i] then
		self.node_list["img_add_cell_" .. i]:SetActive(state)
    end
end

--主角身上的装备变化
function RoleInfoView:OnEquipDataChange(item_id, index, reason)
	if self.select_role_equip_body_seq < 0 then
		return
	end

	local equip_pos_index = EquipmentWGData.GetEquipPartByEquipBodyIndex(index) -- index % 15

	if self.equip_grid[equip_pos_index] == nil then
		return
	end

	-- local target_equip_body_index = equip_pos_index + self.select_role_equip_body_seq * 15
	-- local suit_info = EquipmentWGData.Instance:GetSuitInfoByPartType(target_equip_body_index)
	local target_equip_body_index = EquipmentWGData.EquipBodySeqAndPartToEquipBodyIndex(self.select_role_equip_body_seq, equip_pos_index)
	local wear_data = EquipWGData.Instance:GetGridData(target_equip_body_index)

	if wear_data and wear_data.item_id > 0 then
		self.equip_grid[equip_pos_index]:SetFlushCallBack(
		function ()
			self.equip_grid[equip_pos_index]:SetRightTopImageTextActive(true)
			-- self.equip_grid[k]:SetPartName('')
		end)
		self.equip_grid[equip_pos_index]:SetData(wear_data)

		-- if suit_info >= 0 then
		-- 	local asset_name = "a3_equip_suit_" .. suit_info
		-- 	local bundle, asset = ResPath.GetLoadingPath(asset_name)
		-- 	self.equip_grid[equip_pos_index]:SetEqSuitIcon(bundle, asset, true)
		-- end
		self:SetImagAddState(equip_pos_index, false)
	else
		self.equip_grid[equip_pos_index]:ClearData()
		self.equip_grid[equip_pos_index]:SetRightTopImageTextActive(false)
		self.equip_grid[equip_pos_index]:SetItemIcon(RoleInfoView.GetEquipBg(equip_pos_index))
		self.equip_grid[equip_pos_index]:SetButtonComp(true)
		self:SetImagAddState(equip_pos_index, true)
	end
end

--主角身上的列表装备变化
function RoleInfoView:OnEquipDataListChange()
	-- local suit_all_info = EquipmentWGData.Instance:GetSuitAllInfo()
	local data_list = EquipWGData.Instance:GetDataList()
	for k,v in pairs(data_list) do
		self:OnEquipDataChange(v.item_id, v.index, 0)
	end
end

--主角外观变化
function RoleInfoView:ApperanceChange()
	if self.role_display then
		self.role_display:Reset(Scene.Instance:GetMainRole())
	end
end

--选择格子
function RoleInfoView:SelectCellCallBack(cell)
	if cell == nil or cell:GetData() == nil then
		return
	end
	local cell_data = cell:GetData()
	if cell:GetName() == GRID_TYPE_EQUIP then			--打开tip, 提示脱下装备
		TipWGCtrl.Instance:OpenItem(cell_data, ItemTip.FROM_BAG_EQUIP, {fromIndex = cell.index})
	end
end

--点击小鬼
function RoleInfoView:SelectXiaoGuiCallBack(cell)
	if cell == nil or cell:GetData() == nil then
		local imp_guard_info = EquipWGData.Instance:GetmpGuardInfo()
		if self.xiao_gui_lock[cell] then
			local other_cfg = EquipWGData.Instance:GetImpguardOtherConfig()
			local transfer = CommonDataManager.GetDaXie(other_cfg.transfer)
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Gurad.OpenLimit, transfer))
		end
	end

	if cell:GetName() == GRID_TYPE_EQUIP then
		TipWGCtrl.Instance:OpenItem(cell:GetData(), ItemTip.FROM_BAG_XIAOGUI)
	end
end


function RoleInfoView:GetCellByEquipType(index)
	return self.equip_grid:GetCell(index)
end

function RoleInfoView:SetIsShowRoleCapability(value)
	self.is_show_role_capability = value
end

function RoleInfoView:SetSelectRoleEquipBodySeq(seq)
	self.select_role_equip_body_seq = seq
end