local CheckProtocol = {}
local msg_list = {}
local check_index = 1
local ready_monitor_time = 0
local is_monitor_ing = false

-- 需要无视掉的协议号(需要经主程同意，随便加的，主程负连带责任！！！)
local FILTER_PROTOCOL = {
	[9001] = true,
	[9051] = true,
	[8733] = true,
	[4864] = true,
	[5499] = true,
	[5516] = true,
	[6552] = true,
	[9303] = true,
}

function CheckProtocol:OnSendMsg(msg_type)
	if not self:IsCheckMsg(msg_type) then
		return
	end

	table.insert(msg_list, {msg_type = msg_type, time = GlobalUnityTime, is_recv = false})
end

function CheckProtocol:OnReceiveMsg(msg_type)
	if not self:IsCheckMsg(msg_type) then
		return
	end

	table.insert(msg_list, {msg_type = msg_type, time = GlobalUnityTime, is_recv = true})
end

function CheckProtocol:IsCheckMsg(msg_type)
	if msg_type <= 2500 then
		return false
	end

	if not is_monitor_ing then
		return false
	end

	if FILTER_PROTOCOL[msg_type] then
		return false
	end
	return true
end

function CheckProtocol:Update(now_time, elapse_time)
	if #msg_list >= 20 then
		self:Statics()
		check_index = math.max(check_index + 20, #msg_list)
	end

	if not is_monitor_ing and nil ~= ViewManager and ViewManager.Instance and ViewManager.Instance:IsOpen(ViewName.Main) then
		ready_monitor_time = ready_monitor_time + elapse_time
		if ready_monitor_time >= 10 then
			is_monitor_ing = true
		end
	end
end

function CheckProtocol:Statics()
	self:StaticsContinuousRecMsg()
end

-- 统计同个协议一段时间内的下发次数
function CheckProtocol:StaticsContinuousRecMsg()
	local msg_type = 0
	local c_count = 0
	local result_t = {}

	for i=check_index, #msg_list do
		local v = msg_list[i]

		if v.is_recv then
			if msg_type == v.msg_type and i > 1 and v.time - msg_list[i - 1].time <= 2 then
				c_count = c_count + 1
				if c_count >= 3 then
					result_t[msg_type] = c_count
				end
			else
				msg_type = v.msg_type
				c_count = 0
			end
		end
	end

	for k,v in pairs(result_t) do
		print_error(string.format("服务器在短时间内连续下发同个协议，代码设计可能有问题！，协议号%s, 次数%s", k, v))
	end
end

function CheckProtocol:OutputLog()
	self:Statics()

	local content = ""
	for _, v in ipairs(msg_list) do
		content = content .. string.format("msg_type=%s, time=%s, is_recv=%s", v.msg_type, v.time, v.is_recv) .. "\n"
	end
	local file_path = UnityEngine.Application.dataPath .. "/../temp/protocol_log.txt"
	local f = assert(io.open(file_path,'w'))
	f:write(content)
	f:close()
end

return CheckProtocol