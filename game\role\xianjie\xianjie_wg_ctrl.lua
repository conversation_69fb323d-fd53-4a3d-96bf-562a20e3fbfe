require("game/role/xianjie/xianjie_wg_data")
XianjieWGCtrl = XianjieWGCtrl or BaseClass(BaseWGCtrl)
function XianjieWGCtrl:__init()
	if XianjieWGCtrl.Instance then
		ErrorLog("[XianjieWGCtrl] Attemp to create a singleton twice !")
	end
	XianjieWGCtrl.Instance = self
	self.data = XianjieData.New()

	self:RegisterAllProtocols()
end

function XianjieWGCtrl:__delete()
	XianjieWGCtrl.Instance = nil

	self.data:DeleteMe()
	self.data = nil
end

function XianjieWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCShengWangInfo, "OnShengWangInfo")
	self:RegisterProtocol(SCXianJieViewChange, "OnShengWangViewChange")

	self:RegisterProtocol(CSShengWangOpera)
	-- Remind.Instance:RegisterOneRemind(RemindId.xianjie_up, BindTool.Bind1(self.CheckXunBaoRemind, self), true)
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.MainuiOpenCreate, self))
end

function XianjieWGCtrl:MainuiOpenCreate()
	XianjieWGCtrl.SendReqShengWangInfo()
end

-- function XianjieWGCtrl:CheckXunBaoRemind(remind_id)
-- 	if remind_id == RemindId.xianjie_up then
-- 		return self.data:GetXianjieRemind()
-- 	end
-- 	return 0
-- end


function XianjieWGCtrl:OnShengWangInfo(protocol)
	self.data:SetShuxingdanlist(protocol.shuxingdan_list)
	self.data:SetShengwangInfo(protocol)
	-- Remind.Instance:DoRemind(RemindId.xianjie_up)
	RoleWGCtrl.Instance:FlushXianjie()
end

function XianjieWGCtrl:OnShengWangViewChange(protocol)
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	if main_role_vo.obj_id == protocol.obj_id then
		RoleWGData.Instance:SetAttr("xianjie_level", protocol.xianjie_level)
	end

	local obj = Scene.Instance:GetObjectByObjId(protocol.obj_id)
	if obj ~= nil and obj:IsRole() then
		obj:SetAttr("xianjie_level", protocol.xianjie_level)
	end
end

-- 声望操作请求
function XianjieWGCtrl.SendShengWangOpera(opera_type, param1)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSShengWangOpera)
	send_protocol.opera_type= opera_type
	send_protocol.param1 = param1 or 0

	send_protocol:EncodeAndSend()
end

-- 请求声望信息（可仅在第一次打开面板时请求）
function XianjieWGCtrl.SendReqShengWangInfo()
	XianjieWGCtrl.SendShengWangOpera(SHENGWANG_OPERA_TYPE.SHENGWANG_OPERA_REQ_INFO)
end

-- 仙阶升级
function XianjieWGCtrl.SendXianJieUpLevel()
	XianjieWGCtrl.SendShengWangOpera(SHENGWANG_OPERA_TYPE.SHENGWANG_OPERA_XIANJIE_UPLEVEL)
end

-- 仙丹升级
function XianjieWGCtrl.SendXianDanUpLevel()
	XianjieWGCtrl.SendShengWangOpera(SHENGWANG_OPERA_TYPE.SHENGWANG_OPERA_XIANDAN_UPLEVEL)
end
