--boss首杀领取奖励展示面板
BossFirstRewardChooseView = BossFirstRewardChooseView or BaseClass(SafeBaseView)
local RENDER_WIDTH = 150 --一个子物体的宽度
local RENDER_HIGHT = 236 --高度

function BossFirstRewardChooseView:__init()
	self.view_style = ViewStyle.Half
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
    self:LoadConfig()
    self.view_name = "BossFirstRewardChooseView"
	self.ten_list_view = nil
	self.bind_func = BindTool.Bind1(self.ItemChangeCallBack, self)
end

function BossFirstRewardChooseView:__delete()

end

function BossFirstRewardChooseView:ReleaseCallBack()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
	if nil ~= self.ten_list_view then
		self.ten_list_view:DeleteMe()
		self.ten_list_view = nil
	end

	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.bind_func)
end

function BossFirstRewardChooseView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_result_panel")
	self:AddViewResource(0, "uis/view/choose_gift_prefab", "choose_gift_view")
	self.cfg = ConfigManager.Instance:GetAutoConfig("equipment_star_auto")
end

function BossFirstRewardChooseView:LoadCallBack()
	ItemWGData.Instance:NotifyDataChangeCallBack(self.bind_func)
	self.ten_list_view = AsyncListView.New(BossChooseGiftRender, self.node_list["listview"])

	if self.node_list.top_title_bg then
		self.node_list.top_title_bg.raw_image:LoadSprite(ResPath.GetRawImagesPNG("a3_gxhd_djhd_ryxz"))
	end
end

function BossFirstRewardChooseView:ItemChangeCallBack(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	self:Flush(0)
end

function BossFirstRewardChooseView:ShowIndexCallBack()
	self:Flush(0)
end

function BossFirstRewardChooseView:OnFlush()
    local _, can_get = BossWGData.Instance:GetIsWorldFirstKilledByBossId(self.boss_id)
    if not can_get then
    	local view_show_index = BossWGData.Instance:GetCurFirstKillShowView()
        local cur_layer = BossWGData.Instance:GetCurFirstkillLayer()
        local boss_cfg = BossWGData.Instance:GetBossFirstKillCanGetRewardListByLayer(view_show_index, cur_layer)
		if boss_cfg and boss_cfg[1] then
			local boss_type = view_show_index == BossViewIndex.WorldBoss and 0 or 1
			BossWGCtrl.Instance:OpenRewardChooseView(boss_cfg[1].boss_id, boss_type, true)
		else
			self:Close()
			return
		end
    end
	self.second_gift_cfg = self.cfg.gift_equipment_star[self.data.id]
	local data = self.data
	if self.data.id then
		data = self.data.item_data
	end

	local count = #data

	--超过7个格子读7个格子的宽度
	if count < 7 and count > 0 then
		local width = RENDER_WIDTH * count
		self.node_list["listview"]:GetComponent(typeof(UnityEngine.RectTransform)).sizeDelta = Vector2(width, RENDER_HIGHT)
	else
		local width = RENDER_WIDTH * 7
		self.node_list["listview"]:GetComponent(typeof(UnityEngine.RectTransform)).sizeDelta = Vector2(width, RENDER_HIGHT)
	end
	self.ten_list_view:SetDataList(data)

end

function BossFirstRewardChooseView:SetData(boss_id, is_open)
    local boss_cfg = BossWGData.Instance:GetBossInfoByBossId(boss_id)
    self.data = ItemWGData.Instance:GetItemListInGift(boss_cfg.world_firstkill_reward)
	self.boss_id = boss_id
	if is_open then
		self:Open()
	end
end

BossChooseGiftRender = BossChooseGiftRender or BaseClass(BaseRender)
function BossChooseGiftRender:__init()

end

function BossChooseGiftRender:__delete()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end

function BossChooseGiftRender:LoadCallBack()
	self.cell = ItemCell.New(self.node_list["cell_parent"])
	self.cell:SetItemTipFrom(ItemTip.FROM_EQUIMENT_HECHENG)
	XUI.AddClickEventListener(self.node_list["btn_choose"], BindTool.Bind1(self.OnClickFuWenTips, self))
	--XUI.AddClickEventListener(self.node_list["shenshou_btn"], BindTool.Bind1(self.OnClickShenShouBtn, self))
end

function BossChooseGiftRender:OnFlush()
	if not self.data then return end
    local item_data = self.data.item_data

	self.node_list["btn_choose"]:SetActive(true)
    if item_data then
		self.cell:SetData({item_id = item_data.id, num = item_data.num,grade_level = 1, is_bind = item_data.is_bind, param = item_data.param})
		local item_cfg = ItemWGData.Instance:GetItemConfig(item_data.id)
		local name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		self.node_list.cell_name.text.text = name
	else
		local temp_data = __TableCopy(self.data)
		temp_data.grade_level = 1
		local temp_cfg = ItemWGData.Instance:GetItemConfig(temp_data.item_id)
		local name = ToColorStr(temp_cfg.name, ITEM_COLOR[temp_cfg.color])
		self.node_list.cell_name.text.text = name
		self.cell:SetData(temp_data)
		if ShenShouWGData.Instance:GetIsShenShouEquip(self.data.item_id) then
			self.cell:SetLeftTopImg(self.data.star_count or 0)
			self.cell:SetLeftTopTextVisible(false)
		end
	end

	self.node_list.shenshou_btn:SetActive(false)
end

function BossChooseGiftRender:OnClickFuWenTips()
    local boss_id, boss_type = BossWGData.Instance:GetCurFirstChoosedData()
    BossWGCtrl.Instance:SendRewardChooseView(boss_id, boss_type, self.data.item_id)
end
