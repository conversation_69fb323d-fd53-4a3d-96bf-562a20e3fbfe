﻿using Nirvana;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public static class GPUInstancingOrderGroup
{
    private static int curOrder = 0;
    private static Dictionary<string, int> groupOrder = new Dictionary<string, int>();

    public static void OnGameStop()
    {
        curOrder = 0;
        groupOrder.Clear();
    }

    public static void RefreshRenderOrder(GameObject gameObject, MeshRenderer[] renderer)
    {
        if (null == renderer)
        {
            return;
        }

        int startOrder = 0;
        bool isNewGroup = false;
        string name = ObjectNameMgr.Instance.GetObjectName(gameObject);
        if (!groupOrder.TryGetValue(name, out startOrder))
        {
            isNewGroup = true;
            startOrder = curOrder;
            groupOrder.Add(name, curOrder);
        }

        for (int i = 0; i < renderer.Length; i++)
        {
            if (null != renderer[i])
            {
                renderer[i].sortingOrder = startOrder + renderer[i].sortingOrder;
            }
        }

        if (isNewGroup)
        {
            curOrder += 3;
            if (curOrder >= 30000)
            {
                curOrder = -30000;
            }
        }
    }
}
