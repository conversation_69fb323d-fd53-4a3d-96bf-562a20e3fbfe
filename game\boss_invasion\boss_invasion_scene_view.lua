BOSSInvasionSceneView = BOSSInvasionSceneView or BaseClass(SafeBaseView)

function BOSSInvasionSceneView:__init()
	self.is_safe_area_adapter = true
	self:AddViewResource(0, "uis/view/boss_invasion_ui_prefab", "layout_boss_invasion_scene_view")
end

function BOSSInvasionSceneView:OpenCallBack()
	if self.obj and self:IsLoadedIndex(0) then
        self.obj:SetActive(true)
    end

	if self.player_root_obj and self:IsLoadedIndex(0) then
        self.player_root_obj:SetActive(true)
    end
end

function BOSSInvasionSceneView:LoadCallBack()
    MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))
	
	if not self.hurt_list then
		self.hurt_list = AsyncListView.New(BOSSInvasionSceneHurtItemCellRender, self.node_list.hurt_list)
		self.hurt_list:SetStartZeroIndex(false)
	end

	XUI.AddClickEventListener(self.node_list["answer_item1"], BindTool.Bind(self.OnClickAnswerItem,self, 1))
	XUI.AddClickEventListener(self.node_list["answer_item2"], BindTool.Bind(self.OnClickAnswerItem,self, 2))
	XUI.AddClickEventListener(self.node_list["btn_get_reward"], BindTool.Bind(self.OnClickGetRewardBtn,self))
	XUI.AddClickEventListener(self.node_list["layout_nolonger_tips"], BindTool.Bind(self.OnClickCheckBox,self))
	XUI.AddClickEventListener(self.node_list["btn_active_priivilege"], BindTool.Bind(self.OnClickActivePrivilegeBtn,self))

	-- if not self.gather_enter_visible then
	-- 	self.gather_enter_visible = GlobalEventSystem:Bind(ObjectEventType.VISIBLE_OBJ_ENTER_GATHER, BindTool.Bind(self.OnGatherEnterVisible, self))
	-- end

	if not self.role_pos_change then
		self.role_pos_change = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_POS_CHANGE, BindTool.Bind1(self.OnMainRolePosChangeFunc, self))
	end

	self.select_answer_id_cache = -1
	self.old_yushou_value = 0
	self.old_xiuwei_value = 0
	self.old_tongbi_value = 0

	self.node_list.desc_answer_time_tip.text.text = Language.BOSSInvasion.WaitQuestionTip
	self.node_list.desc_boss_born_tip.text.text = Language.BOSSInvasion.BossBornStr
end

function BOSSInvasionSceneView:InitCallBack()
	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()
	if self.node_list["boss_invasion_task_root"] then
		self.obj = self.node_list["boss_invasion_task_root"].gameObject
		self.obj.transform:SetParent(parent.gameObject.transform)
		self.obj.transform.localPosition = Vector3(0, 0, 0)
		self.obj.transform.localScale = Vector3.one
	end

    if self.is_out_fb then
		self.obj:SetActive(false)
    end

    self.is_out_fb = nil

	local player_info_parent = mainui_ctrl:GetPlayerInfoWidgetRoot()

	if self.node_list["root_buy_privilege_tip"] then
		self.player_root_obj = self.node_list["root_buy_privilege_tip"].gameObject
		self.player_root_obj.transform:SetParent(player_info_parent.gameObject.transform)
		self.player_root_obj.transform.localPosition = Vector3(0, 0, 0)
		self.player_root_obj.transform.localScale = Vector3.one
		self.player_root_obj.transform:SetAsFirstSibling()
	end

	if self.is_out_fb then
		self.player_root_obj:SetActive(false)
	end
end

function BOSSInvasionSceneView:CloseCallBack()
	self.is_out_fb = true

    if self.obj then
        self.obj:SetActive(false)
    end

	if self.player_root_obj then
        self.player_root_obj:SetActive(false)
    end
end

function BOSSInvasionSceneView:ReleaseCallBack()
	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end

	if self.player_root_obj then
        ResMgr:Destroy(self.player_root_obj)
        self.player_root_obj = nil
    end

	if self.hurt_list then
		self.hurt_list:DeleteMe()
		self.hurt_list = nil
	end

	if self.role_pos_change then
		GlobalEventSystem:UnBind(self.role_pos_change)
		self.role_pos_change = nil
	end

	if CountDownManager.Instance:HasCountDown("boss_invasion_wait_question") then
		CountDownManager.Instance:RemoveCountDown("boss_invasion_wait_question")
	end

	if CountDownManager.Instance:HasCountDown("boss_invasion_question_time") then
		CountDownManager.Instance:RemoveCountDown("boss_invasion_question_time")
	end

	if CountDownManager.Instance:HasCountDown("boss_invasion_wait_boss_born") then
		CountDownManager.Instance:RemoveCountDown("boss_invasion_wait_boss_born")
	end
	
    -- if self.gather_enter_visible then
	-- 	GlobalEventSystem:UnBind(self.gather_enter_visible)
	-- 	self.gather_enter_visible = nil
	-- end

	self.select_answer_id_cache = nil
end

function BOSSInvasionSceneView:ShowIndexCallBack()
	if self.obj then
        self.obj:SetActive(true)
    end
end

function BOSSInvasionSceneView:OnFlush(param_t, index)
    for k,v in pairs(param_t) do
		if k == "all" then
			self:OnFlushView()
		elseif k == "answer_suc" then
			self:PlayAnswerSucEffect()
		elseif k == "show_get_effect" then
			self:PlayGetEffect()
		elseif k == "first_affectrd_by_skill" then
			self:ShowBuyPrivilgeTip()
        end
    end

	-- ViewManager.Instance:Open(GuideModuleName.NovicePopDialogView, nil, "all", {data = {[1] = {trigger_param_1 = 100, word = "没想到师祖的的药连灵骑都能用。", id = 3}}})  -- 功能引导表
end

function BOSSInvasionSceneView:OnFlushView()
	local status = BOSSInvasionWGData.Instance:GetCurActStatus()
	local has_rank_list = BOSSInvasionWGData.Instance:IsHasHurtRankList()
	local is_gather_flag = BOSSInvasionWGData.Instance:IsGatherFlag()
	local can_get_end_reward = BOSSInvasionWGData.Instance:CanGetBossEndReward()
	local gather_time_enough = BOSSInvasionWGData.Instance:GetCanGatherFlag()

	local is_get_privilege = BOSSInvasionWGData.Instance:IsGetPrivilege()
	if is_get_privilege then
		self.node_list.root_buy_privilege_tip:CustomSetActive(false)
	end

	self.node_list.answer_task_root:CustomSetActive(status == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS or status == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS_END)
	-- or status == CROSS_BOSS_STRIKE_STATUS.STATUS_QUESTION_SHOW
	-- or status == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS
	-- or status == CROSS_BOSS_STRIKE_STATUS.STATUS_WAIT_BOSS)

	self.node_list.boss_reward_root:CustomSetActive(status == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS_END)
	self.node_list.boss_born_root:CustomSetActive(status == CROSS_BOSS_STRIKE_STATUS.STATUS_WAIT_BOSS)
	self.node_list.answer_start_root:CustomSetActive(status == CROSS_BOSS_STRIKE_STATUS.STATUS_WAIT_QUESTION)
	self.node_list.answer_top_root:CustomSetActive(status == CROSS_BOSS_STRIKE_STATUS.STATUS_QUESTION or status == CROSS_BOSS_STRIKE_STATUS.STATUS_QUESTION_SHOW)
	self.node_list.btn_get_reward:CustomSetActive(status == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS_END and not is_gather_flag and can_get_end_reward and gather_time_enough)
	self.node_list.answer_down_root:CustomSetActive(status == CROSS_BOSS_STRIKE_STATUS.STATUS_QUESTION or status == CROSS_BOSS_STRIKE_STATUS.STATUS_QUESTION_SHOW)
	self.node_list.act_end_root:CustomSetActive(status == CROSS_BOSS_STRIKE_STATUS.STATUS_END)
	self.node_list.hurt_root:CustomSetActive((status == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS_END or status == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS) and has_rank_list)


	if status == CROSS_BOSS_STRIKE_STATUS.STATUS_WAIT_QUESTION then
		self:FlushWaitQuestionInfo()
	elseif status == CROSS_BOSS_STRIKE_STATUS.STATUS_QUESTION or status == CROSS_BOSS_STRIKE_STATUS.STATUS_QUESTION_SHOW then
		self:FlushAnswerQuestionInfo(status)
		self:FlushTaskMsgInfo(status)
	elseif status == CROSS_BOSS_STRIKE_STATUS.STATUS_WAIT_BOSS then
		self:FlushWaitBossInfo()
	elseif status == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS then
		self:FlushAnswerQuestionInfo(status)
		self:FlushTaskMsgInfo(status)
		self:FlushBossInfo()
	elseif status == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS_END then
		self:FlushBossRewardInfo()
		self:FlushTaskMsgInfo(status)
	elseif status == CROSS_BOSS_STRIKE_STATUS.STATUS_END then
		self:FlushEndInfo()
		self:FlushTaskMsgInfo(status)
		self:FlushHurtInfo()
	end
end

-- 等待答题
function BOSSInvasionSceneView:FlushWaitQuestionInfo()
	local next_status_time = BOSSInvasionWGData.Instance:GetNextStatusTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()

	if next_status_time > server_time then
		if CountDownManager.Instance:HasCountDown("boss_invasion_wait_question") then
			CountDownManager.Instance:RemoveCountDown("boss_invasion_wait_question")
		end

		self.node_list.desc_answer_time.text.text = TimeUtil.FormatSecondDHM9(math.ceil(next_status_time - server_time))
		CountDownManager.Instance:AddCountDown("boss_invasion_wait_question", 
		function(elapse_time, total_time)
			if self.node_list.desc_answer_time then
				self.node_list.desc_answer_time.text.text = TimeUtil.FormatSecondDHM9(math.ceil(total_time - elapse_time))
			end
		end, 
		function ()
			if self.node_list.desc_answer_time then
				self.node_list.desc_answer_time = ""
			end
		end,
		next_status_time, nil, 1)
	else
		self.node_list.desc_answer_time.text.text = ""
		if CountDownManager.Instance:HasCountDown("boss_invasion_wait_question") then
			CountDownManager.Instance:RemoveCountDown("boss_invasion_wait_question")
		end
	end
end

-- task面板信息
function BOSSInvasionSceneView:FlushTaskMsgInfo(status)
	local is_cross = BOSSInvasionWGData.Instance:IsCrossServer()
	local sign_str = is_cross and Language.BOSSInvasion.CrossState[1] or Language.BOSSInvasion.CrossState[0]
	self.node_list.desc_tip.text.text = string.format(Language.BOSSInvasion.SceneTaskAnswerTip, sign_str)

	local get_exp = BOSSInvasionWGData.Instance:GetCurExp()
	self.node_list.desc_answer_get_exp.text.text = string.format(Language.BOSSInvasion.AnswerGetExp, CommonDataManager.ConverNumberToThousand2(get_exp))

	local is_get_privilege = BOSSInvasionWGData.Instance:IsGetPrivilege()
	local other_cfg = BOSSInvasionWGData.Instance:GetOtherCfg()

	local beast_exp = BOSSInvasionWGData.Instance:GetBeastExp()
	local cur_boss_color_cfg = BOSSInvasionWGData.Instance:GetCurBossColorCfg()
	local max_beast_exp = is_get_privilege and (cur_boss_color_cfg.max_beast_exp * (1 + other_cfg.rmb_max_beast_exp_add / 10000)) or cur_boss_color_cfg.max_beast_exp
	max_beast_exp = CommonDataManager.ConverNumberToThousand2(max_beast_exp)
	-- self.node_list.desc_yushou_str.text.text = string.format(Language.BOSSInvasion.YuShouExp, CommonDataManager.ConverNumberToThousand2(beast_exp), max_beast_exp)
	
	self.node_list.desc_yushou_title.text.text = Language.BOSSInvasion.YuShouExpTitle

	if beast_exp == 0 then
		self.node_list.desc_yushou_str.text.text = beast_exp
	else
		if self.old_yushou_value ~= beast_exp then
			local text_obj = self.node_list.desc_yushou_str:GetComponent(typeof(TMPro.TextMeshProUGUI))
	
			local complete_fun = function()
				if self.node_list.desc_yushou_str then
					self.node_list.desc_yushou_str.text.text = beast_exp
				end
			end
			local update_fun = function(num)
				if self.node_list.desc_yushou_str then
					self.node_list.desc_yushou_str.text.text = math.ceil(num)
				end
			end
			UITween.DONumberTo(text_obj, self.old_yushou_value, beast_exp, 1, update_fun, complete_fun)
			self.node_list.desc_yushou_str.transform:SetLocalScale(1,1,1)
			self.node_list.desc_yushou_str.transform:DOPunchScale(Vector3(0.3, 0.3, 0.3), 0.5)
			self.old_yushou_value = beast_exp
		end
	end

	self.node_list.desc_yushou_extra.text.text = string.format(Language.BOSSInvasion.YuShouExpExtra, max_beast_exp)

	local cur_xiuwei = BOSSInvasionWGData.Instance:GetCurXiuWei()
	local max_xiuwei = is_get_privilege and (cur_boss_color_cfg.max_xiuwei * (1 + other_cfg.rmb_max_xiuwei_add / 10000)) or cur_boss_color_cfg.max_xiuwei
	max_xiuwei = CommonDataManager.ConverNumberToThousand2(max_xiuwei)
	-- self.node_list.desc_xiuwei_str.text.text = string.format(Language.BOSSInvasion.XiuWeiStr, CommonDataManager.ConverNumberToThousand2(cur_xiuwei), max_xiuwei)
	self.node_list.desc_xiuwei_title.text.text = Language.BOSSInvasion.XiuWeiStrTitle

	if cur_xiuwei == 0 then
		self.node_list.desc_xiuwei_str.text.text = cur_xiuwei
	else
		if self.old_xiuwei_value ~= cur_xiuwei then
			local text_obj = self.node_list.desc_xiuwei_str:GetComponent(typeof(TMPro.TextMeshProUGUI))
	
			local complete_fun = function()
				if self.node_list.desc_xiuwei_str then
					self.node_list.desc_xiuwei_str.text.text = cur_xiuwei
				end
			end
			local update_fun = function(num)
				if self.node_list.desc_xiuwei_str then
					self.node_list.desc_xiuwei_str.text.text = math.ceil(num)
				end
			end
			UITween.DONumberTo(text_obj, self.old_xiuwei_value, cur_xiuwei, 1, update_fun, complete_fun)
			self.node_list.desc_xiuwei_str.transform:SetLocalScale(1,1,1)
			self.node_list.desc_xiuwei_str.transform:DOPunchScale(Vector3(0.3, 0.3, 0.3), 0.5)
			self.old_xiuwei_value = cur_xiuwei
		end
	end

	self.node_list.desc_xiuwei_extra.text.text = string.format(Language.BOSSInvasion.YuShouExpExtra, max_xiuwei)

	local cur_tongbi = BOSSInvasionWGData.Instance:GetCurCoin()
	-- self.node_list.desc_tongqian_str.text.text = string.format(Language.BOSSInvasion.TongQianStr, CommonDataManager.ConverNumberToThousand2(cur_tongbi))--, CommonDataManager.ConverNumberToThousand2(cur_boss_color_cfg.max_coin))
	self.node_list.desc_tongqian_title.text.text = Language.BOSSInvasion.TongQianStrTitle

	if cur_tongbi == 0 then
		self.node_list.desc_tongqian_str.text.text = cur_tongbi
	else
		if self.old_tongbi_value ~= cur_tongbi then
			local text_obj = self.node_list.desc_tongqian_str:GetComponent(typeof(TMPro.TextMeshProUGUI))
	
			local complete_fun = function()
				if self.node_list.desc_tongqian_str then
					self.node_list.desc_tongqian_str.text.text = cur_tongbi
				end
			end
			local update_fun = function(num)
				if self.node_list.desc_tongqian_str then
					self.node_list.desc_tongqian_str.text.text = math.ceil(num)
				end
			end
			UITween.DONumberTo(text_obj, self.old_tongbi_value, cur_tongbi, 1, update_fun, complete_fun)
			self.node_list.desc_tongqian_str.transform:SetLocalScale(1,1,1)
			self.node_list.desc_tongqian_str.transform:DOPunchScale(Vector3(0.3, 0.3, 0.3), 0.5)
			self.old_tongbi_value = cur_tongbi
		end
	end

	local right_answer_time = BOSSInvasionWGData.Instance:GetCurRightAnswerTimes()
	local cur_boss_cfg = BOSSInvasionWGData.Instance:GetCurBossCfg()
	local add_hurt = (right_answer_time * cur_boss_cfg.answer_add_hurt_per) / 100
	self.node_list.desc_zengyi_str.text.text = string.format(Language.BOSSInvasion.ShangHaiZengYiStr, add_hurt)
end

-- 答题中
function BOSSInvasionSceneView:FlushAnswerQuestionInfo(status)
	local question_num = BOSSInvasionWGData.Instance:GetCurQuestionNum()
	local cur_boss_cfg = BOSSInvasionWGData.Instance:GetCurBossCfg()
	self.node_list.desc_answer_id.text.text = string.format(Language.BOSSInvasion.AnswerId, question_num, cur_boss_cfg.question_num)

	local cur_question_cfg = BOSSInvasionWGData.Instance:GetCurQuestionCfg()
	self.node_list.desc_answer_content.text.text = cur_question_cfg.question
	self.node_list.answer_desc1.text.text = cur_question_cfg.answer1
	self.node_list.answer_desc2.text.text = cur_question_cfg.answer2

	for i = 1, 2 do
		local type = cur_question_cfg["answer_type" .. i]

		local bundle, asset = ResPath.GetCommonButton(type == 1 and "a3_ty_btn_4" or "a3_ty_btn_5")
		self.node_list["answer_item" .. i].image:LoadSprite(bundle, asset)

		local effect_bundle, effect_asset = ResPath.GetUIEffect(type == 1 and "UI_dati_kuang_lanse" or "UI_dati_kuang_jinse")
		self.node_list["answer_effect" .. i]:ChangeAsset(effect_bundle, effect_asset)
	end

	local right_answer_time = BOSSInvasionWGData.Instance:GetCurRightAnswerTimes()
	local add_hurt = (right_answer_time * cur_boss_cfg.answer_add_hurt_per) / 100
	self.node_list.desc_answer_add.text.text = string.format(Language.BOSSInvasion.RightAnswerAddHurtStr, right_answer_time, add_hurt)

	local next_status_time = BOSSInvasionWGData.Instance:GetNextStatusTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()

	for i = 1, 2 do
		self.node_list["gou" .. i]:CustomSetActive(status == CROSS_BOSS_STRIKE_STATUS.STATUS_QUESTION_SHOW and cur_question_cfg.right_answer == i)
		self.node_list["cha" .. i]:CustomSetActive(status == CROSS_BOSS_STRIKE_STATUS.STATUS_QUESTION_SHOW and cur_question_cfg.right_answer ~= i)
	end

	if status == CROSS_BOSS_STRIKE_STATUS.STATUS_QUESTION_SHOW then
		for i = 1, 2 do
			self.node_list["answer_effect" .. i]:CustomSetActive(false)
		end
	else
		self:OnMainRolePosChangeFunc()
	end

	if next_status_time > server_time then
		if CountDownManager.Instance:HasCountDown("boss_invasion_question_time") then
			CountDownManager.Instance:RemoveCountDown("boss_invasion_question_time")
		end

		self.node_list.desc_answer_count_dowm_time.text.text = string.format(Language.BOSSInvasion.AnswerTimeStr, math.floor(next_status_time - server_time))
		CountDownManager.Instance:AddCountDown("boss_invasion_question_time", 
		function(elapse_time, total_time)
			if self.node_list.desc_answer_count_dowm_time then
				self.node_list.desc_answer_count_dowm_time.text.text = string.format(Language.BOSSInvasion.AnswerTimeStr, math.floor(total_time - elapse_time))
			end
		end, 
		function ()
			if self.node_list.desc_answer_count_dowm_time then
				self.node_list.desc_answer_count_dowm_time.text.text = ""
			end
		end,
		next_status_time, nil, 1)
	else
		if self.node_list.desc_answer_count_dowm_time then
			self.node_list.desc_answer_count_dowm_time.text.text = ""
		end

		if CountDownManager.Instance:HasCountDown("boss_invasion_question_time") then
			CountDownManager.Instance:RemoveCountDown("boss_invasion_question_time")
		end
	end
end

-- 等待boss刷新
function BOSSInvasionSceneView:FlushWaitBossInfo()
	local next_status_time = BOSSInvasionWGData.Instance:GetNextStatusTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()

	if next_status_time > server_time then
		if CountDownManager.Instance:HasCountDown("boss_invasion_wait_boss_born") then
			CountDownManager.Instance:RemoveCountDown("boss_invasion_wait_boss_born")
		end

		self.node_list.desc_boss_born_time.text.text = TimeUtil.FormatSecondDHM9(math.ceil(next_status_time - server_time))
		CountDownManager.Instance:AddCountDown("boss_invasion_wait_boss_born", 
		function(elapse_time, total_time)
			if self.node_list.desc_boss_born_time then
				self.node_list.desc_boss_born_time.text.text = TimeUtil.FormatSecondDHM9(math.ceil(total_time - elapse_time))
			end

			if self.node_list.desc_boss_born_time_str then
				self.node_list.desc_boss_born_time_str.text.text = math.ceil(total_time - elapse_time)
			end

			if self.node_list.boss_born_time_root then
				self.node_list.boss_born_time_root.rect.localScale = Vector3(3,3,1)
				self.node_list.boss_born_time_root.rect:DOScale(Vector3(1,1,1), 0.3)
				EffectManager.Instance:PlayAtTransform("effects/prefab/ui/ui_daojishi_1_prefab", "UI_daojishi_1", self.node_list.boss_born_time_effect_root.transform)
			end
		end, 
		function ()
			if self.node_list.desc_boss_born_time then
				self.node_list.desc_boss_born_time.text.text = "0"
			end

			if self.node_list.desc_boss_born_time_str then
				self.node_list.desc_boss_born_time_str.text.text = "0"
			end
		end,
		next_status_time, nil, 1)
	else
		self.node_list.desc_boss_born_time.text.text = "0"
		self.node_list.desc_boss_born_time_str.text.text = "0"

		if CountDownManager.Instance:HasCountDown("boss_invasion_wait_boss_born") then
			CountDownManager.Instance:RemoveCountDown("boss_invasion_wait_boss_born")
		end
	end
end

-- 与boss战斗时间
function BOSSInvasionSceneView:FlushBossInfo()
	self:FlushHurtInfo()
end

-- boss被击杀 领奖时间
function BOSSInvasionSceneView:FlushBossRewardInfo()
	self:FlushHurtInfo()

	local winner_uuid, hurt_top_usid, hurt_top_guild = BOSSInvasionWGData.Instance:GetWinnerInfo()
	local is_cross = BOSSInvasionWGData.Instance:IsCrossServer()
	local sign_str = ""
	local is_same_team = false

	if is_cross then
		local server_id = RoleWGData.Instance:GetOriginServerId()
		sign_str = Language.BOSSInvasion.CrossState[1]

		local my_plat_type = RoleWGData.Instance:GetPlatType()
		is_same_team = (server_id == hurt_top_usid.temp_low and my_plat_type == hurt_top_usid.temp_high)
	else
		local guild_id = RoleWGData.Instance.role_vo.guild_id
		sign_str = Language.BOSSInvasion.CrossState[0]
		is_same_team = (guild_id > 0 and guild_id == hurt_top_guild)
	end

	local get_reward_str = is_same_team and Language.BOSSInvasion.BossEndCanGetReward or Language.BOSSInvasion.BossEndCanNotGetReward
	local reward_str = string.format(get_reward_str, sign_str)

	local is_get_reward = BOSSInvasionWGData.Instance:IsGatherFlag()
	if is_get_reward then
		reward_str = Language.BOSSInvasion.IsGatherFlag
	end

	local my_uuid = RoleWGData.Instance:GetUUid()
	if winner_uuid == my_uuid then
		self.node_list.desc_boss_reward_tip.text.text = string.format(Language.BOSSInvasion.BossEndWinnerTip, is_get_reward and Language.BOSSInvasion.IsGatherFlag or Language.BOSSInvasion.NotGatherFlag)
	else
		local hurt_top_name = BOSSInvasionWGData.Instance:GetHurtTopName()
		self.node_list.desc_boss_reward_tip.text.text = string.format(Language.BOSSInvasion.BossEndRewardTip, hurt_top_name, sign_str, reward_str)

		-- BrowseWGCtrl.Instance:BrowRoelInfo(winner_uuid.temp_low, function (protocol)
		-- 	self.node_list.desc_boss_reward_tip.text.text = string.format(Language.BOSSInvasion.BossEndRewardTip, protocol.role_name, sign_str, reward_str)
		-- end, winner_uuid.temp_high, true)
	end

	local gather_time = BOSSInvasionWGData.Instance:GetHasGatherTime()
	local boss_color_cfg = BOSSInvasionWGData.Instance:GetCurBossColorCfg()
	self.node_list.desc_get_reward_btn_tip.text.text = string.format(Language.BOSSInvasion.DescGetRewardBtnTip, boss_color_cfg.gather_times - gather_time, boss_color_cfg.gather_times)
end

function BOSSInvasionSceneView:FlushHurtInfo()
	local has_rank_list = BOSSInvasionWGData.Instance:IsHasHurtRankList()
	if has_rank_list then
		local hurt_data_list = BOSSInvasionWGData.Instance:GetHurtRankList()
		self.hurt_list:SetDataList(hurt_data_list)
	
		local my_rank_id, my_damage = BOSSInvasionWGData.Instance:GetMyHurtIdAndDamage()
		self.node_list["my_rank"].text.text = my_rank_id > 0 and string.format(Language.BOSSInvasion.RankRewardRankStr, my_rank_id) or Language.BOSSInvasion.RankRewardNoMyRank
		self.node_list["my_damate"].text.text = CommonDataManager.ConverExpByThousand(my_damage)
	end
end

-- 活动结束
function BOSSInvasionSceneView:FlushEndInfo()
	
end

function BOSSInvasionSceneView:ShowBuyPrivilgeTip()
	local is_get_privilege = BOSSInvasionWGData.Instance:IsGetPrivilege()
	local active_flag = self:GetBuyPrivilgeTipFlag()

	if not is_get_privilege and active_flag == 0 then
		self.node_list.root_buy_privilege_tip:CustomSetActive(true)
	else
		self.node_list.root_buy_privilege_tip:CustomSetActive(false)
	end
end

function BOSSInvasionSceneView:OnClickActivePrivilegeBtn()
	BOSSInvasionWGCtrl.Instance:OpenPrivilegeView()
	-- self:SetBuyPrivilgeTipFlag()
end

function BOSSInvasionSceneView:OnClickCheckBox()
	local is_visible = self.node_list["img_nohint_hook"]:GetActive()
	self.node_list["img_nohint_hook"]:SetActive(not is_visible)
	self.node_list.root_buy_privilege_tip:CustomSetActive(false)
	self:SetBuyPrivilgeTipFlag()
end

function BOSSInvasionSceneView:SetBuyPrivilgeTipFlag()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local is_visible = self.node_list["img_nohint_hook"]:GetActive()
	return PlayerPrefsUtil.SetInt("buy_privilege_tip_flag" .. role_id .. cur_day, is_visible and 1 or 0)
end

function BOSSInvasionSceneView:GetBuyPrivilgeTipFlag()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	return PlayerPrefsUtil.GetInt("buy_privilege_tip_flag" .. role_id .. cur_day, 0)
end

function BOSSInvasionSceneView:OnClickAnswerItem(answer_id)
	local status = BOSSInvasionWGData.Instance:GetCurActStatus()

	if status ~= status == CROSS_BOSS_STRIKE_STATUS.STATUS_QUESTION then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BOSSInvasion.NotInQuestionTime)
		return
	end

	local main_role = Scene.Instance:GetMainRole()

	if main_role then
		local cur_boss_cfg = BOSSInvasionWGData.Instance:GetCurBossCfg()
		local pos_cfg = cur_boss_cfg["answer".. answer_id .."_pos"]
		if pos_cfg then
			local pos_list = string.split(pos_cfg, ",")
			local pos_x, pos_y = pos_list[1], pos_list[2]
			local role_pos_x, role_pos_y = main_role:GetLogicPos()

			local distance = GameMath.GetDistance(pos_x, pos_y, role_pos_x, role_pos_y, true)
			local cur_question_cfg = BOSSInvasionWGData.Instance:GetCurQuestionCfg()
			GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)

			if self.select_answer_id_cache ~= answer_id or distance > cur_boss_cfg.answer_range then
				GuajiWGCtrl.Instance:SetMoveToPosCallBack(function()
					GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
				end)
				GuajiWGCtrl.Instance:MoveToPos(cur_boss_cfg.scene_id, pos_x, pos_y)

				SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.BOSSInvasion.GoToAnswerArea, cur_question_cfg["answer" .. answer_id]))
			else
				SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.BOSSInvasion.AnswerIsInArea, cur_question_cfg["answer" .. answer_id]))
			end

			self.select_answer_id_cache = answer_id
		end
	end
end

function BOSSInvasionSceneView:OnClickGetRewardBtn()
	local is_gather_flag = BOSSInvasionWGData.Instance:IsGatherFlag()
	
	if not is_gather_flag then
		local cur_boss_cfg = BOSSInvasionWGData.Instance:GetCurBossCfg()

		if cur_boss_cfg then
			local pos_list = string.split(cur_boss_cfg.monster_pos, ",")
			local pos_x, pos_y = pos_list[1], pos_list[2]

			local color_cfg = BOSSInvasionWGData.Instance:GetCurBossColorCfg()
			if color_cfg then
				local target_gather_item = nil

				local gather_list = Scene.Instance:GetGatherList()
				if not IsEmptyTable(gather_list) then
					for k,v in pairs(gather_list) do
						if v.gather_config.id == color_cfg.gather_id then
							target_gather_item = v
							break
						end
					end
				end

				if target_gather_item then
					GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
					MoveCache.SetEndType(MoveEndType.Gather)
					MoveCache.param1 = target_gather_item.gather_config.id
					MoveCache.target_obj = target_gather_item
					GuajiCache.target_obj_id = target_gather_item.gather_config.id
				end

				local scene_id = Scene.Instance:GetSceneId()
				GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_x, pos_y, 4)
			end
		end
	end
end

function BOSSInvasionSceneView:PlayAnswerSucEffect()
	local bundle_name, asset_name = ResPath.GetEffectUi("UI_dati_lihua")
    EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["effect_root"].transform, 5)
end

function BOSSInvasionSceneView:OnMainRolePosChangeFunc()
	local scene_type = Scene.Instance:GetSceneType()
	
    if scene_type == SceneType.BOSS_INVASION then
		local status = BOSSInvasionWGData.Instance:GetCurActStatus()

		if status == CROSS_BOSS_STRIKE_STATUS.STATUS_QUESTION then
			local cur_boss_cfg = BOSSInvasionWGData.Instance:GetCurBossCfg()
			local main_role = Scene.Instance:GetMainRole()
			local role_pos_x, role_pos_y = main_role:GetLogicPos()
	
			for i = 1, 2 do
				local pos_cfg = cur_boss_cfg["answer".. i .."_pos"]
				local pos_list = string.split(pos_cfg, ",")
				local pos_x, pos_y = pos_list[1], pos_list[2]
				local distance = GameMath.GetDistance(pos_x, pos_y, role_pos_x, role_pos_y, true)
	
				self.node_list["answer_effect" .. i]:CustomSetActive(distance < cur_boss_cfg.answer_range)
			end
		end
	end
end

function BOSSInvasionSceneView:PlayGetEffect()
	local cur_boss_color_cfg = BOSSInvasionWGData.Instance:GetCurBossColorCfg()
	local max_xiuwei = cur_boss_color_cfg.max_xiuwei
	local cur_xiuwei = BOSSInvasionWGData.Instance:GetCurXiuWei()
	local is_max_xiuwei = cur_xiuwei >= max_xiuwei

	local move_effect_name = is_max_xiuwei and "UI_bosslaixi_guanglizi_jinse" or "UI_bosslaixi_guanglizi_lanse"
	local move_end_effect_name = is_max_xiuwei and "UI_bosslaixi_guanglizibz_jinse" or "UI_bosslaixi_guanglizibz_lanse"

	local move_bundle, move_asset = ResPath.GetEffect(move_effect_name)
	local move_end_bundle, move_end_asset = ResPath.GetEffect(move_end_effect_name)
	EffectManager.Instance:PlayAtTransform(move_end_bundle, move_end_asset, self.node_list.reward_effect_start_root.transform)

	TipWGCtrl.Instance:ShowFlyEffectManager("BOSSInvasionSceneView", move_bundle, move_asset, self.node_list.reward_effect_start_root, self.node_list.reward_effect_end_root,
		DG.Tweening.Ease.OutCubic, 1, function ()
			EffectManager.Instance:PlayAtTransform(move_end_bundle, move_end_asset, self.node_list.reward_effect_bz_root.transform)
		end, nil, 1, 20, nil, false, false, nil, 200)
end

-------------------------------------BOSSInvasionSceneHurtItemCellRender---------------------------------
BOSSInvasionSceneHurtItemCellRender = BOSSInvasionSceneHurtItemCellRender or BaseClass(BaseRender)

function BOSSInvasionSceneHurtItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
		return
	end

	self.node_list.num.text.text = self.data.rank >= 3 and self.data.rank or ""
	self.node_list.name.text.text = self.data.name
	self.node_list.damage.text.text = CommonDataManager.ConverExpByThousand(self.data.hurt)
	-- self.node_list.per_bg.slider.value = self.data.hurt / BOSSInvasionWGData.Instance:GetMaxHurtValue()

	local asset = self.data.rank < 4 and "a3_hurt_list_bg_" .. self.data.rank or "a3_hurt_list_bg_4"
	local bg_bundle, bg_asset = ResPath.GetCommonImages(asset)
	self.node_list.fill.image:LoadSprite(bg_bundle, bg_asset)

	self.node_list.icon:CustomSetActive(self.data.rank < 4)

	if self.data.rank < 4 then
		self.node_list.icon.image:LoadSprite(ResPath.GetCommonImages("a3_hurt_list_rank_" .. self.index))
	end
end