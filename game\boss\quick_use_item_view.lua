QuickItemUseView = QuickItemUseView or BaseClass(SafeBaseView)

function QuickItemUseView:__init()
    self.calc_active_close_ui_volume = false
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_third3_panel")
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_quick_use")
    self:SetMaskBg(true, true)
    self.item_data_event = BindTool.Bind1(self.ItemDataChangeCallback, self)
end

function QuickItemUseView:ReleaseCallBack()
    self.list = nil
    if ItemWGData.Instance ~= nil then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
	end
    if self.cell then
        self.cell:DeleteMe()
        self.cell = nil
    end
end

function QuickItemUseView:ItemDataChangeCallback(item_id, index, reason, put_reason, old_num, new_num)
    if self.list and self.list.consume_item and item_id == self.list.consume_item then
        self:Flush()
    end
end

function QuickItemUseView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.Boss.TimesAdd
    self.node_list["layout_commmon_third_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
    self.node_list["layout_commmon_third_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
    self.cell = ItemCell.New(self.node_list["cell"])
    self.node_list["Button"].button:AddClickListener(BindTool.Bind(self.OnClickUse, self))
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
end

function QuickItemUseView:ShowIndexCallBack()
    self:Flush()
end

function QuickItemUseView:SetData(list)
    self.list = list
end

function QuickItemUseView:OnFlush()
    local item = self.list
    if self.list.is_add_xianli then
        self.node_list.title_view_name.text.text = Language.Boss.XianliAdd
    end
    local num = ItemWGData.Instance:GetItemNumInBagById(item.consume_item)
    self.cell:SetData({item_id = item.consume_item})
    local color = num > 0 and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
    self.cell:SetRightBottomColorText(ToColorStr(num .. "/" .. 1, color))
    self.cell:SetRightBottomTextVisible(true)
    local cfg = ItemWGData.Instance:GetItemConfig(item.consume_item)
    self.node_list["name"].text.text = ToColorStr(cfg.name ,ITEM_COLOR[cfg.color])
    --ChangeToQualityText(self.node_list["name"], cfg.color)

    self.node_list["desc"].text.text = item.desc
end

function QuickItemUseView:OnClickUse()
    local num = ItemWGData.Instance:GetItemNumInBagById(self.list.consume_item)
    if num > 0 then
        if self.list.is_add_xianli then
            local item_index = ItemWGData.Instance:GetItemIndex(self.list.consume_item)
            if item_index == -1 then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.NewAppearance.NotItemTop)
                return        
            end
            BagWGCtrl.Instance:SendUseItem(item_index, 1)
        else
            FuBenWGCtrl.Instance:SendDecBossTired(self.list.dec_tired_type)
        end
    else
        if self.list.is_add_xianli then
            TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.list.consume_item})
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.Prop_No_Enough)
        end
    end
end
