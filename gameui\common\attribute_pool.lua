AttributePool = AttributePool or {}

-- Attribute已经被搞成一个特大表，在计算时却被大量创建。这里优化成一个临时属性表，下一帧将被回收重复利用
-- 使用者不可以对此对象做缓存处理
local liveing_list = {}
local wait_release_list = {}
local attribute_pool = {}
local attribute_keys = nil
local attribute_debug_list = {}

local mt = {}
mt.__newindex = function (t, k, v)
	attribute_keys = AttributePool.__GetAttributeKeys()
	if nil == attribute_keys[k] then
 		print_error("Attribute是重用结构体，不要为Attribute额外增加字段，以免造成不可预料BUG", k, v)
	else
		-- 因为一些隐藏的角落会把key直接设为nil，不能直接拦截
		rawset(t, k, v)
	end
end

mt.__add = function (a, b)
	local b_type = type(b)
	if b_type == "number" then
		for key, _ in pairs(a) do
			a[key] = a[key] + b
		end
	elseif b_type == "table" then
		local va, vb = 0, 0
		for key, _ in pairs(a) do
			va = a[key] or 0
			vb = b[key] or 0
			a[key] = va + vb
		end
	end

	return a
end

mt.__sub = function (a, b)
	local b_type = type(b)
	if b_type == "number" then
		for key, _ in pairs(a) do
			a[key] = a[key] - b
		end
	elseif b_type == "table" then
		local va, vb = 0, 0
		for key, _ in pairs(a) do
			va = a[key] or 0
			vb = b[key] or 0
			a[key] = va - vb
		end
	end

	return a
end

mt.__mul = function (a, b)
	local b_type = type(b)
	if b_type == "number" then
		for key, _ in pairs(a) do
			a[key] = a[key] * b
		end
	elseif b_type == "table" then
		local va, vb = 0, 0
		for key, _ in pairs(a) do
			va = a[key] or 0
			vb = b[key] or 0
			a[key] = va * vb
		end
	end

	return a
end

mt.__div = function (a, b)
	local b_type = type(b)
	if b_type == "number" and b ~= 0 then
		for key, _ in pairs(a) do
			a[key] = a[key] / b
		end
	elseif b_type == "table" then
		local va, vb = 0, 0
		for key, _ in pairs(a) do
			va = a[key] or 0
			vb = b[key] or 0
			if vb > 0 then
				a[key] = va / vb
			end
		end
	end

	return a
end



function AttributePool.__Attribute()
	local attribute = AttributeMgr.__Attribute()
	setmetatable(attribute, mt)
	return attribute
end

-- 分配一个干净的数据
function AttributePool.AllocAttribute()
	local attribute = nil
	if #attribute_pool > 0 then
		attribute = table.remove(attribute_pool, #attribute_pool)
	else
		attribute = AttributePool.__Attribute()
	end

	table.insert(liveing_list, attribute)

	if IS_DEBUG_BUILD or IS_LOCLA_WINDOWS_DEBUG_EXE then
		local info = debug.getinfo(3, "Sln")
		if attribute_debug_list[info.short_src] == nil then
			attribute_debug_list[info.short_src] = {}
		end

		if attribute_debug_list[info.short_src][info.currentline] == nil then
			attribute_debug_list[info.short_src][info.currentline] = 1
		else
			attribute_debug_list[info.short_src][info.currentline] = attribute_debug_list[info.short_src][info.currentline] + 1
		end
	end

	return attribute
end

-- 分配一个数据没有被清理过的，需要依赖于逻辑代码允许脏数据
-- 比如外部只需要要拿key，或者会做数据覆盖操作
-- 之所有返回原生的key，是因为其他地方用把key为直接设为nil。导致结构体被破坏(无奈)
function AttributePool.AllocDirtyAttribute()
	local attribute = nil
	if #wait_release_list > 0 then
		attribute = table.remove(wait_release_list, #wait_release_list)
	elseif #attribute_pool > 0 then
		attribute = table.remove(attribute_pool, #attribute_pool)
	else
		attribute = AttributePool.__Attribute()
	end

	table.insert(liveing_list, attribute)

	if IS_DEBUG_BUILD or IS_LOCLA_WINDOWS_DEBUG_EXE then
		local info = debug.getinfo(3, "Sln")
		if attribute_debug_list[info.short_src] == nil then
			attribute_debug_list[info.short_src] = {}
		end

		if attribute_debug_list[info.short_src][info.currentline] == nil then
			attribute_debug_list[info.short_src][info.currentline] = 1
		else
			attribute_debug_list[info.short_src][info.currentline] = attribute_debug_list[info.short_src][info.currentline] + 1
		end
	end

	return attribute, AttributePool.__GetAttributeKeys()
end

function AttributePool.__GetAttributeKeys()
	if nil == attribute_keys then
		attribute_keys = {}
		local attriute = AttributeMgr.__Attribute()
		for k,v in pairs(attriute) do
			attribute_keys[k] = k
		end
	end

	return attribute_keys
end

function AttributePool.Update(now_time, elapse_time)
	if #liveing_list > 200 then
		print_error(string.format("[AttributePool]在一帧里创建了%s个Attribute,通知主程检查什么原因，是不是代码太烂！！！", #liveing_list))

		if IS_DEBUG_BUILD or IS_LOCLA_WINDOWS_DEBUG_EXE then
			for k,v in pairs(attribute_debug_list) do
				if v ~= nil then
					print_error("[AttributePool] 具体报错脚本日志: 	", k)
					for k1, v1 in pairs(v) do
						print_error("[AttributePool] 具体行数，次数: 	", k1, v1)
					end
				end
			end
		end
	end

	if #attribute_pool > 200 then
		liveing_list = {}
		wait_release_list = {}
		return
	end

	if #liveing_list > 0 then
		for i,v in ipairs(liveing_list) do
			table.insert(wait_release_list, v)
		end
		liveing_list = {}
		attribute_debug_list = {}
	end

	-- 每帧清理N个并放入池
	if #wait_release_list > 0 then
		local count = math.ceil(#wait_release_list / 60)
		for i = 1, count do
			local attribute = table.remove(wait_release_list, #wait_release_list)
			if nil == attribute then
				break
			end

			local keys = AttributePool.__GetAttributeKeys()
			for k,v in pairs(keys) do
				attribute[k] = 0
			end
			table.insert(attribute_pool, attribute)
		end
	end
end

-- 清理一下所有的数据还原成一个干净的数据类型
function AttributePool.ResetAttribute(attribute)
	if not attribute then
		return
	end

	local keys = AttributePool.__GetAttributeKeys()
	for k,v in pairs(keys) do
		attribute[k] = 0
	end
end

-- 分配一个数据不会进入池子，自己模块处理好销毁，
-- 主要用于一帧之内频繁使用做战力计算等，可以全部使用这一个对象去计算，不用一直开辟内存
function AttributePool.CreateAttribute()
	local attribute = AttributePool.__Attribute()
	return attribute
end