MainUIGuardGuide = MainUIGuardGuide or BaseClass(BaseRender)

function MainUIGuardGuide:__init()
	self.show_item = nil
	self.node_list["icon"].button:AddClickListener(BindTool.Bind(self.OnClickIcon, self))
	self.xiaogui_data_change_fun = BindTool.Bind1(self.OnXiaoGuiDataChange, self)
	EquipWGData.Instance:NotifyXiaoGuiChangeCallBack(self.xiaogui_data_change_fun)

	if nil == self.item_data_change_callback then
		self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
	end

	self.is_frist_open = true
	self:OnXiaoGuiDataChange()
end

function MainUIGuardGuide:__delete()
	if nil ~= self.model_view then
		self.model_view:DeleteMe()
		self.model_view = nil
	end
	if self.xiaogui_data_change_fun then
		EquipWGData.Instance:NotifyXiaoGuiChangeCallBack(self.xiaogui_data_change_fun)
		self.xiaogui_data_change_fun = nil
	end
	if self.item_data_change_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
		self.item_data_change_callback = nil
	end

	self.show_item = nil
	self.weae_guard_info = nil
	self.temp_xiaogui_id = nil
end

function MainUIGuardGuide:OnClickIcon()
	if self.xiaogui_cfg then
		if self.xiaogui_cfg.product_id == 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Equip.XiaoGuiMaxLevel)
		else
			RoleBagWGCtrl.Instance:OpenGuardComposeView(self.xiaogui_cfg.product_id)
		end
	end
end

-- 物品变化
function MainUIGuardGuide:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_item_id == 0 or EquipmentWGData.GetXiaoGuiCfg(change_item_id) then
		self:OnXiaoGuiDataChange()
	end
end

-- 小鬼数据变化
function MainUIGuardGuide:OnXiaoGuiDataChange()
	local scene_type = Scene.Instance:GetSceneType()
	local scene_cfg = FuBenWGData.GetFbSceneConfig(scene_type)
	local scene_logic = Scene.Instance:GetSceneLogic()
	if not scene_cfg or scene_cfg.scene_guard_type == 0 or scene_logic:ForceHideGuard() then
		self.view:SetActive(false)
		return
	end

	self.view:SetActive(true)
	self.node_list.effect:SetActive(false)
	self.node_list.arrow:SetActive(false)
	local guard_info = EquipWGData.Instance:GetmpGuardInfo()		--装备格子上的信息
	self.weae_guard_info = nil

	for i=1,2 do
		local data = guard_info.item_wrapper[i]
		if data and data.item_id and data.item_id > 0 then
			local cfg = EquipmentWGData.GetXiaoGuiCfg(data.item_id)
			if cfg.impguard_type == scene_cfg.scene_guard_type then
				self.weae_guard_info = data
				break
			end
		end
	end

	self.xiaogui_cfg = nil
	self.xiaogui_equip_info = nil
	local temp_cfg = nil
	local xiaogui_list = ItemWGData.Instance:GetXiaoGuiList(scene_cfg.scene_guard_type)
	--背包内有小鬼
	if next(xiaogui_list) then
		for k,v in pairs(xiaogui_list) do
			local cfg = EquipmentWGData.GetXiaoGuiCfg(v.item_id)
			if self.weae_guard_info then 					--装备了小鬼
				local weae_cfg = EquipmentWGData.GetXiaoGuiCfg(self.weae_guard_info.item_id)
				if cfg.color > weae_cfg.color then
					self.xiaogui_equip_info = v
					self.xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(cfg.item_id)
					temp_cfg = EquipmentWGData.GetXiaoGuiCfg(cfg.item_id)
				else
					if nil ~= temp_cfg then
						self.xiaogui_cfg = temp_cfg
					else
						self.xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(self.weae_guard_info.item_id)
					end
				end
			else
				if self.xiaogui_cfg == nil then
					self.xiaogui_equip_info = v
					self.xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(v.item_id)
				else
					if cfg.color > self.xiaogui_cfg.color then
						self.xiaogui_equip_info = v
						self.xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(cfg.item_id)
					end
				end
			end
		end
	else
		if self.weae_guard_info then --背包内没有小鬼，装备着小鬼
			self.xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(self.weae_guard_info.item_id)
		else
			-- local show_item = EquipmentWGData.Instance:GetDefultGuardComposeCfg()
			-- self.xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(show_item[scene_cfg.scene_guard_type])
		end
	end

	if not self.xiaogui_cfg or self.xiaogui_cfg.product_id == 0 then
		self.view:SetActive(false)
		return
	end

	if self.weae_guard_info then
		self.temp_xiaogui_id = self.weae_guard_info.item_id == 10101 and 10100 or self.weae_guard_info.item_id
		self.node_list.effect:SetActive(self.xiaogui_cfg.item_id ~= self.temp_xiaogui_id)
		if self.xiaogui_cfg.item_id == self.temp_xiaogui_id then
			if self.xiaogui_cfg.product_id == 0 then
				--不能合成
				self.node_list.arrow:SetActive(false)
			else
				--可以合成
				self.node_list.arrow:SetActive(self.is_frist_open)
			end
		end
	else
		self.node_list.effect:SetActive(true)
        self.temp_xiaogui_id = nil
        --没有穿戴守护，显示箭头
        self.node_list.arrow:SetActive(true)
	end

	self.node_list.icon.image:LoadSprite(ResPath.GetMainUIIcon("a3_ui_shusj_icon_" .. self.xiaogui_cfg.impguard_type))
	if not self.xiaogui_cfg or not self.xiaogui_cfg.addition then return end
	self.node_list.Text.text.text = self.xiaogui_cfg.addition
end
