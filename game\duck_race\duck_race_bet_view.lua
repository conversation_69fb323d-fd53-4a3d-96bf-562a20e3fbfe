-- 下注子面板
function DuckRaceView:BetLoadCallBack()
	self.duck_info_obj_list = {}
	self.node_list["duck_prefab"]:SetActive(false)
	XUI.AddClickEventListener(self.node_list["fetch_btn"], BindTool.Bind(self.OnClickFetchBtn, self))
	XUI.AddClickEventListener(self.node_list["play_coin_icon"], BindTool.Bind(self.OnClickPlayCoin, self))
end

function DuckRaceView:BetReleaseCallBack()
	if self.duck_info_obj_list then
		for k,v in pairs(self.duck_info_obj_list) do
			v:DeleteMe()
		end
		self.duck_info_obj_list = {}
	end
end


function DuckRaceView:BetOnFlush(param_list)
	-- 所有鸭子的信息
	local all_duck_info = DuckRaceWGData.Instance:GetAllDuckInfo()
	if not IsEmptyTable(all_duck_info) then
		for duck_index = 0, #all_duck_info do
			if self.duck_info_obj_list[duck_index] == nil then
				local go = ResMgr:Instantiate(self.node_list["duck_prefab"].gameObject)
				go:SetActive(true)
				go.transform:SetParent(self.node_list["duck_info_" .. duck_index].transform, false)
				self.duck_info_obj_list[duck_index] = BetDuckInfo.New(go)
			end
			local obj = self.duck_info_obj_list[duck_index]
			obj:SetActive(true)
			obj:SetData(all_duck_info[duck_index])
		end
	else	
		for k,v in pairs(self.duck_info_obj_list) do
			v:SetActive(false)
		end
	end

	-- 可领取的应援币
	self.node_list["free_coin_count"].text.text = DuckRaceWGData.Instance:GetOtherCfg().can_fetch_play_coin_per_round
	-- 可领取面板
	self.node_list["fetch_bottom"]:SetActive(DuckRaceWGData.Instance:GetCanFetchPlayCoin())
end

-- 点击领取应援币
function DuckRaceView:OnClickFetchBtn()
	DuckRaceWGCtrl.Instance:SendFetchPlayCoin()
end

---------------------------------------------------------------------------------
BetDuckInfo = BetDuckInfo or BaseClass(BaseGridRender)
function BetDuckInfo:__init()
	XUI.AddClickEventListener(self.node_list["bet_btn"], BindTool.Bind(self.OnClickBet, self))
	XUI.AddClickEventListener(self.node_list["follow_btn"], BindTool.Bind(self.OnClickFollow, self))
	XUI.AddClickEventListener(self.node_list["details_btn"], BindTool.Bind(self.OnClickDetails, self))
	XUI.AddClickEventListener(self.node_list["coin_icon"], BindTool.Bind(self.OnClickPlayCoin, self))
	if not self.duck_model then
		self.duck_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["duck_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.S,
			can_drag = true,
		}

		self.duck_model:SetRenderTexUI3DModel(display_data)
		-- self.duck_model:SetUI3DModel(self.node_list["duck_model"].transform, self.node_list["duck_model"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
	end
end

function BetDuckInfo:__delete()
	if self.duck_model then
		self.duck_model:DeleteMe()
		self.duck_model = nil
	end
end

function BetDuckInfo:OnFlush()
	if self.data then
		local duck_cfg = DuckRaceWGData.Instance:GetDuckCfg(self.data.duck_id) 					-- 根据鸭子id获得鸭子配置
		local other_cfg = DuckRaceWGData.Instance:GetOtherCfg()
		if not duck_cfg then
			print_error("没有找到小鸭配置, 请检查配置，duck_id:", self.data.duck_id)
			return
		end
		
		local other_cfg = DuckRaceWGData.Instance:GetOtherCfg()
		local bet_count = DuckRaceWGData.Instance:GetMyBetCountByDuckIndex(self.data.index) 										-- 本人对当前鸭子的下注数目
		self.node_list["duck_index"].image:LoadSprite(ResPath.GetDuckRaceImg("a2_naem_bg_" .. self.data.index))
		self.node_list["tili_value"].text.text = duck_cfg.tili 																	-- 鸭子体力值
		self.node_list["xingfen_value"].text.text = duck_cfg.xingfen 															-- 鸭子兴奋值
		self.node_list["reward_gold"].text.text = math.floor(self.data.peilv * other_cfg.coin_num)								-- 预计可获得的金币数目
		self.node_list["bet_value"].text.text = bet_count  																		-- 下注数目
		local monster_cfg = BossWGData.Instance:GetMonsterInfo(DuckRaceWGData.Instance:GetDuckMonsterId(self.data.index))
		self.node_list["duck_name"].text.text = string.format(Language.DuckRace.DuckName[self.data.index], monster_cfg.name)

		local bundle, asset = ResPath.GetMonsterModel(monster_cfg.resid)
		local old_bundle, old_asset = self.duck_model:GetCurAssetPath()
		if old_bundle ~= bundle or old_asset ~= asset then
			self.duck_model:SetMainAsset(bundle, asset)
			--self.duck_model:PlayMonsterAction(false)
		end

		-- 下注占比
		local total_bet_count= DuckRaceWGData.Instance:GetAllDuckBetCount() 														-- 总下注次数
		local single_duck_bet_count = self.data.bet_count 																		-- 所有人对该鸭子的下注次数
		if total_bet_count == 0 then
			self.node_list["bet_percent"].text.text = "0%"
		else
			local percent = single_duck_bet_count / total_bet_count * 100
			self.node_list["bet_percent"].text.text = string.format(Language.DuckRace.Percent, percent - percent % 0.1)
		end
		
		self.node_list["follow_btn"]:SetActive(DuckRaceWGData.Instance:GetPlayCoin() <= 0)
		self.node_list["bet_btn"]:SetActive(DuckRaceWGData.Instance:GetPlayCoin() > 0)
	end
	self.node_list["bet_panel"]:SetActive(not DuckRaceWGData.Instance:GetCanFetchPlayCoin())
end

-- 点击下注
function BetDuckInfo:OnClickBet()
	DuckRaceWGCtrl.Instance:SendBetOneDuck(self.data.index)
end

-- 点击跟随助威
function BetDuckInfo:OnClickFollow()
	local bet_count = DuckRaceWGData.Instance:GetMyBetCountByDuckIndex(self.data.index) 										-- 本人对当前鸭子的下注数目
	if bet_count <= 0 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.DuckRace.Reason3)
		return
	end
	
	DuckRaceWGData.Instance:SetFollowDuckIndex(self.data.index)
	ViewManager.Instance:Close(GuideModuleName.DuckRace)
end

-- 点击详情
function BetDuckInfo:OnClickDetails()
	DuckRaceWGCtrl.Instance:OpenDuckDetailsTips(self.data)
end

-- 点击应援币
function BetDuckInfo:OnClickPlayCoin()
	local item_id = DuckRaceWGData.Instance:GetOtherCfg().play_coin_item
	TipWGCtrl.Instance:OpenItem({item_id = tonumber(item_id)}, ItemTip.FROM_NORMAL, nil)
end