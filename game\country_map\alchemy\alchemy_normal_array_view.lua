AlchemyNormalArrayView = AlchemyNormalArrayView or BaseClass(SafeBaseView)

function AlchemyNormalArrayView:__init()
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(true)

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(2, -4), sizeDelta = Vector2(1102, 590)})
	self:AddViewResource(0, "uis/view/country_map_ui/alchemy_prefab", "layout_alchemy_normal_array")
end

function AlchemyNormalArrayView:__delete()

end

function AlchemyNormalArrayView:LoadCallBack()
    self.node_list["title_view_name"].text.text = Language.CountryAlchemy.TitleName2

	if not self.cur_item_list then
		self.cur_item_list = {}
		for i = 1, 7 do
			self.cur_item_list[i] = NormalArrayItem.New(self.node_list["cur_pellet_item_" .. i])
			self.cur_item_list[i]:SetIndex(i)
		end
	end

	if not self.next_item_list then
		self.next_item_list = {}
		for i = 1, 7 do
			self.next_item_list[i] = NormalArrayItem.New(self.node_list["next_pellet_item_" .. i])
			self.next_item_list[i]:SetIndex(i)
		end
	end

	XUI.AddClickEventListener(self.node_list["level_up_btn"], BindTool.Bind(self.OnClickLevelUp, self))
	self.cur_level = 0
end

function AlchemyNormalArrayView:CloseCallBack()

end

function AlchemyNormalArrayView:ReleaseCallBack()
	self.cur_level = nil

	if self.cur_item_list then
		for k, v in pairs(self.cur_item_list) do
			v:DeleteMe()
		end

		self.cur_item_list = nil
	end

	if self.next_item_list then
		for k, v in pairs(self.next_item_list) do
			v:DeleteMe()
		end

		self.next_item_list = nil
	end
end

function AlchemyNormalArrayView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then

		end
	end
	
	self:FlushPanelInfo()
end

function AlchemyNormalArrayView:FlushPanelInfo()
	self.cur_level = AlchemyWGData.Instance:GetNormalArrayLevel()
	local max_level = AlchemyWGData.Instance:GetNormalArrayCfgMaxLevel()
	self.node_list.zreo_level_panle:SetActive(self.cur_level == 0)
	self.node_list.cur_level_panle:SetActive(self.cur_level ~= 0)
	self.node_list.next_level_panle:SetActive(self.cur_level < max_level)
	self.node_list.level_up_btn:SetActive(self.cur_level < max_level)
	self.node_list.max_level:SetActive(self.cur_level >= max_level)

	if self.cur_level ~= 0 then  --不是0级
		local cur_level_cfg = AlchemyWGData.Instance:GetNormalArrayCfgByLevel(self.cur_level)
		self.node_list["cur_normal_array_bg"].raw_image:LoadSprite(ResPath.GetRawImagesPNG("a2_ldf_fz_" .. self.cur_level))
		self.node_list.cur_rate_text.text.text = string.format(Language.CountryAlchemy.DanDoubleStr, cur_level_cfg.double_rate / 100)
		self.node_list.cur_name.text.text = cur_level_cfg.name
		local cur_pellet_list = Split(cur_level_cfg.added_pellet, "|")
		for i, v in ipairs(self.cur_item_list) do
			if cur_pellet_list[i] then
				self.node_list["cur_pellet_item_" .. i]:SetActive(true)
				local cur_cell_data = {}
				local cur_item_info = Split(cur_pellet_list[i], ":")
				cur_cell_data.item_id = tonumber(cur_item_info[1])
				cur_cell_data.gailv = tonumber(cur_item_info[2])
				v:SetData(cur_cell_data)
			else
				v:SetData(nil)
			end
		end
	end

	if self.cur_level < max_level then -- 没满级
		local next_level_cfg = AlchemyWGData.Instance:GetNormalArrayCfgByLevel(self.cur_level + 1)
		self.node_list["next_normal_array_bg"].raw_image:LoadSprite(ResPath.GetRawImagesPNG("a2_ldf_fz_" .. self.cur_level + 1))
		self.node_list.next_rate_text.text.text = string.format(Language.CountryAlchemy.DanDoubleStr, next_level_cfg.double_rate / 100)
		local btn_str
		if next_level_cfg.condition_type == 1 then --等级类型
	        btn_str = string.format(Language.CountryAlchemy.ArrayBuyStr1, next_level_cfg.condition_value)
	  	elseif next_level_cfg.condition_type == 2 then --元宝类型
	        btn_str = string.format(Language.CountryAlchemy.ArrayBuyStr2, next_level_cfg.condition_value)
	 	elseif next_level_cfg.condition_type == 3 then  --灵玉类型
	 		btn_str = string.format(Language.CountryAlchemy.ArrayBuyStr3, next_level_cfg.condition_value)
	 	elseif next_level_cfg.condition_type == 4 then  --直购类型
	 		local price = RoleWGData.GetPayMoneyStr(next_level_cfg.condition_value, next_level_cfg.rmb_type, next_level_cfg.rmb_seq)
	 		btn_str = string.format(Language.CountryAlchemy.ArrayBuyStr4, price)
	  	end
	  	self.node_list.up_condition_text.text.text = btn_str

		self.node_list.next_name.text.text = next_level_cfg.name
		local next_pellet_list = Split(next_level_cfg.added_pellet, "|")
		for i, v in ipairs(self.next_item_list) do
			if next_pellet_list[i] then
				self.node_list["next_pellet_item_" .. i]:SetActive(true)
				local next_cell_data = {}
				local next_item_info = Split(next_pellet_list[i], ":")
				next_cell_data.item_id = tonumber(next_item_info[1])
				next_cell_data.gailv = tonumber(next_item_info[2])
				v:SetData(next_cell_data)
			else
				v:SetData(nil)
			end
		end
	end
end


function AlchemyNormalArrayView:OnClickLevelUp()
	local next_level_cfg = AlchemyWGData.Instance:GetNormalArrayCfgByLevel(self.cur_level + 1)
	if not next_level_cfg then
		return
	end

	local text_dec = ""
	local is_enough_lv
    local is_enough_money
    if next_level_cfg.condition_type == 1 then --等级类型
        text_dec = string.format(Language.CountryAlchemy.ArrayBuyTips1, next_level_cfg.condition_value)
        is_enough_lv = RoleWGData.Instance.role_vo.level >= next_level_cfg.condition_value
  	elseif next_level_cfg.condition_type == 2 then --元宝类型
        text_dec = string.format(Language.CountryAlchemy.ArrayBuyTips2, next_level_cfg.condition_value)
        is_enough_money = RoleWGData.Instance:GetIsEnoughAllGold(next_level_cfg.condition_value)
 	elseif next_level_cfg.condition_type == 3 then  --灵玉类型
 		text_dec = string.format(Language.CountryAlchemy.ArrayBuyTips3, next_level_cfg.condition_value)
 		is_enough_money = RoleWGData.Instance:GetIsEnoughUseGold(next_level_cfg.condition_value)
 	elseif next_level_cfg.condition_type == 4 then  --直购类型
 		local price = RoleWGData.GetPayMoneyStr(next_level_cfg.condition_value, next_level_cfg.rmb_type, next_level_cfg.rmb_seq)
 		text_dec = string.format(Language.CountryAlchemy.ArrayBuyTips4, price)
  	end

    local ok_func = function ()
        if next_level_cfg.condition_type == 1 then
 			if is_enough_lv then
				AlchemyWGCtrl.Instance:SenSAlchemyReq(ALCHEMY_OPERATE_TYPE.NORMAL_ARRAY_UP)
				ViewManager.Instance:Close(GuideModuleName.AlchemyNormalArrayView)
            else
            	SysMsgWGCtrl.Instance:ErrorRemind(Language.CountryAlchemy.NoEnoughlv)
            end
        elseif next_level_cfg.condition_type == 2 or next_level_cfg.condition_type == 3 then
            if is_enough_money then
            	AlchemyWGCtrl.Instance:SenSAlchemyReq(ALCHEMY_OPERATE_TYPE.NORMAL_ARRAY_UP)
            	ViewManager.Instance:Close(GuideModuleName.AlchemyNormalArrayView)
            else
               VipWGCtrl.Instance:OpenTipNoGold()
            end
        elseif next_level_cfg.condition_type == 4 then
        	RechargeWGCtrl.Instance:Recharge(next_level_cfg.condition_value, next_level_cfg.rmb_type, next_level_cfg.rmb_seq)
        	ViewManager.Instance:Close(GuideModuleName.AlchemyNormalArrayView)
        end
    end

    TipWGCtrl.Instance:OpenAlertTips(text_dec, ok_func)
end

------------------------ 法阵物品item ------------------------
NormalArrayItem = NormalArrayItem or BaseClass(BaseRender)
function NormalArrayItem:__init()
	self.item_cell = ItemCell.New(self.node_list["item_cell"])
end

function NormalArrayItem:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function NormalArrayItem:OnFlush()
	self:FlushLockState()
	if self.data == nil then
		return
	end

	self.item_cell:SetData({item_id = self.data.item_id})
	local gailv = self.data.gailv / 100
	self.node_list.add_text.text.text = string.format(Language.CountryAlchemy.AddedStr, gailv)
end

function NormalArrayItem:FlushLockState()
	local is_no_data = IsEmptyTable(self.data)
	self.node_list.lock:SetActive(is_no_data)
	self.node_list.text_bg:SetActive(not is_no_data)
end