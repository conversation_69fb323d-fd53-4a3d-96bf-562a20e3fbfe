CatExploreView = CatExploreView or BaseClass(SafeBaseView)

function CatExploreView:__init()
    self.view_layer = UiLayer.Normal
    self.view_style = ViewStyle.Half
    self:SetMaskBg()

    self.default_index = TabIndex.cat_explore

    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
    self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar_Activity")
    self:AddViewResource(TabIndex.cat_explore, "uis/view/cat_explore_ui_prefab", "layout_cat_explore")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")

end

function CatExploreView:__delete()
end

function CatExploreView:ReleaseCallBack()
    if self.task_list then
        self.task_list:DeleteMe()
        self.task_list = nil
    end

    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

    if self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end

    if self.step_reward_list then
        for k, v in pairs(self.step_reward_list) do
            v:DeleteMe()
        end
        self.step_reward_list = nil
    end

    if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end

    if CountDownManager.Instance:HasCountDown("cat_ecplore_time") then
        CountDownManager.Instance:RemoveCountDown("cat_ecplore_time")
    end
end

function CatExploreView:OnClickToEnd()
    self.node_list.mid_panel.scroll_rect.horizontalNormalizedPosition = 1
end

function CatExploreView:OpenCallBack()
    self.old_step = 0
    CatExploreWGCtrl.Instance:ReqCatExploreInfo(OA_CAT_VENTURE_OPERTE_TYPE.INFO)
end

function CatExploreView:ShowIndexCallBack(index)
    if index == TabIndex.cat_explore then
	    ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.CatExploreView, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CAT_VENTURE)
    end
end

function CatExploreView:LoadCallBack()
    if not self.money_bar then
        self.money_bar = MoneyBar.New()
        local bundle, asset = ResPath.GetWidgets("MoneyBar")
        local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
        self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
    end

    self:InitTabbar()

    XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind(self.OnClickClose, self))
end

function CatExploreView:InitTabbar()
    local remind_tab = { {RemindName.CatExplore} }
	local toggle_name_list = { Language.CatExplore.Title }

	

    if not self.tabbar then
        self.tabbar = Tabbar.New(self.node_list, "VerticalTabbar_Activity")
	    self.tabbar:SetVerTabbarCellName("VerticalTabbarCell_Activity2")
	    self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))

        self.tabbar:Init(toggle_name_list, nil, ResPath.CommonBundleName,
		nil, remind_tab)
		-- self.tabbar:Init(Language.Field1v1.ZQXZTabGroup, nil, "uis/view/most_venerable_ui_prefab")
	end


	-- self.tabbar:JumpToVerPrecent(1)
end

function CatExploreView:InitCatExploreView()

    self.node_list.text_end.text.text = Language.CatExplore.EndStr
    
    if nil == self.task_list then
        self.task_list = AsyncListView.New(CatExploreTaskCell, self.node_list.task_list)
    end

    if self.step_reward_list == nil then
        self.step_reward_list = {}
        for i = 0, CatExploreWGData.MAX_STEP_REWARD_COUNT do
            self.step_reward_list[i] = CatExploreStepRewardCell.New(self.node_list["content"]:FindObj("step_reward_" .. i))
            self.step_reward_list[i]:SetIndex(i)
        end
    end

    self.node_list.mid_panel.scroll_rect.onValueChanged:AddListener(BindTool.Bind(self.UpdateAnimation, self))
    self.node_list.task_list.scroll_rect.onValueChanged:AddListener(BindTool.Bind(self.UpdateTaskAnimation, self))

    
    XUI.AddClickEventListener(self.node_list.btn_to_end, BindTool.Bind1(self.OnClickToEnd,self))
    XUI.AddClickEventListener(self.node_list["go_step_btn"], BindTool.Bind(self.OnClickStepBtn, self))
    self.node_list["step_icon"].button:AddClickListener(BindTool.Bind(self.ShowItemTips, self))
end

function CatExploreView:LoadIndexCallBack(index)
    local bg_res = "a3_yjxx_bg_1"
	if index == TabIndex.cat_explore then
        self.is_first = true
		self:InitCatExploreView()
	end

    local bundle, asset = ResPath.GetRawImagesPNG(bg_res)
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)
end

function CatExploreView:OnFlush(param_t, index)
    if index == TabIndex.cat_explore  then
        for k,v in pairs(param_t) do
            if k == "all" then
                self.is_first = true
                self:FlushAllView()
            elseif k == "flush_task" then
                self:FlushRightView()
            elseif k == "flush_cat_step" then
                self:FlushStepView()
                self:FlushCostView()
            elseif k == "flush_cost" then
               self:FlushCostView()
            end
        end
	end
    
end

function CatExploreView:FlushAllView()
    self:FlushMidView()
    self:FlushRightView()
    self:FlushCostView()
    self:FlushStepView()
    self:FlushTimeCount()
end

function CatExploreView:FlushMidView()
    local reward_cfg = CatExploreWGData.Instance:GetAllStepRewardCfg()
    for i, v in ipairs(self.step_reward_list) do
        v:SetData(reward_cfg[i])
    end

    local other_cfg = CatExploreWGData.Instance:GetOtherCfg()
    local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.cost_item_id)
    if item_cfg then
        self.node_list["step_icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
    end
end

function CatExploreView:FlushCostView()
    local cur_step = CatExploreWGData.Instance:GetCurStep()
    local other_cfg = CatExploreWGData.Instance:GetOtherCfg()
    local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.cost_item_id)
    local cost_num = CatExploreWGData.Instance:GetStepCostNum(cur_step + 1)
    local str = item_num .. "/" .. cost_num
    local color = item_num >= cost_num and COLOR3B.C6 or COLOR3B.C10
    self.node_list.consume_count.text.text = ToColorStr(str, color)
    local is_remind = CatExploreWGData.Instance:ShowStepBtnRemind()
    self.node_list.step_remind:SetActive(is_remind)
end

function CatExploreView:CountJumpPos()
    local cur_step = CatExploreWGData.Instance:GetCurStep()
    if cur_step < 6 then
        return 0
    end
    local pos_x = cur_step/ (CatExploreWGData.MAX_STEP_REWARD_COUNT-1) * 1
    return pos_x
end

function CatExploreView:FlushStepView()
    local cur_step = CatExploreWGData.Instance:GetCurStep()

    if cur_step == self.old_step or self.is_first then
        self.is_first = false
        local pos = self.step_reward_list[cur_step]:GetPos()
        self.node_list.img_role.rect.localPosition = Vector2(pos.x-10, pos.y+80)

        local position = self.node_list.mid_panel.scroll_rect.horizontalNormalizedPosition
        local pos_x = self:CountJumpPos()
        if math.abs(pos_x - position) > 0.1 then
            self.node_list.mid_panel.scroll_rect.horizontalNormalizedPosition = pos_x
        end

        self.old_step = cur_step

        return
    end

    local start_step = self.old_step == 0 and 1 or self.old_step
    for i = start_step, cur_step do
        self.step_reward_list[i]:Flush()
    end

    

    --TODO 人物图片动画
    -- 先判断目标在不在是视野内
    if cur_step > 0  then
        local position = self.node_list.mid_panel.scroll_rect.horizontalNormalizedPosition
        local pos_x = self:CountJumpPos()
        if math.abs(pos_x - position) > 0.1 then
            self.node_list.mid_panel.scroll_rect.horizontalNormalizedPosition = pos_x
        end
        
        local star_pos = self.step_reward_list[cur_step-1]:GetPos()
        local end_pos = self.step_reward_list[cur_step]:GetPos()
        UITween.CleanAllMoveToShowPanel(GuideModuleName.CatExploreView)
        -- UITween.DONumberTo()
        self.is_tweening =true
        UITween.MoveToShowPanel(GuideModuleName.CatExploreView, self.node_list.img_role, 
        Vector2(star_pos.x-10, star_pos.y+80), Vector2(end_pos.x-10, end_pos.y+80), 1,nil,function ()
            CatExploreWGCtrl.Instance:ReqCatExploreInfo(OA_CAT_VENTURE_OPERTE_TYPE.STEP_REWARD, cur_step)
            self.is_tweening =false
        end)
    end


    self.old_step = cur_step

end

function CatExploreView:FlushTimeCount()
    local time, next_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CAT_VENTURE)
    if time > 0 then
        if CountDownManager.Instance:HasCountDown("cat_ecplore_time") then
            CountDownManager.Instance:RemoveCountDown("cat_ecplore_time")
        end

        CountDownManager.Instance:AddCountDown("cat_ecplore_time", 
            BindTool.Bind(self.FinalUpdateTimeCallBack, self), 
            BindTool.Bind(self.OnComplete, self), 
            nil, time, 1)
    else
        self:OnComplete()
    end
end

function CatExploreView:FinalUpdateTimeCallBack(now_time, total_time)
    local time = math.ceil(total_time - now_time)
    local time_str = TimeUtil.FormatSecondDHM8(time)
    self.node_list["act_time"].text.text = string.format(Language.CatExplore.ActivityTime, time_str)
end

function CatExploreView:OnComplete()
    self.node_list.act_time.text.text = ""
end


function CatExploreView:FlushRightView()
    local all_task_data = CatExploreWGData.Instance:GetAllTaskList()
    self.task_list:SetDataList(all_task_data)
end

function CatExploreView:OnClickStepBtn()
    if self.is_tweening then
        UITween.CleanAllMoveToShowPanel(GuideModuleName.CatExploreView)
        local cur_step = CatExploreWGData.Instance:GetCurStep()
        local is_get = CatExploreWGData.Instance:GetRewardStateBystep(cur_step)
        if not is_get then
            CatExploreWGCtrl.Instance:ReqCatExploreInfo(OA_CAT_VENTURE_OPERTE_TYPE.STEP_REWARD, cur_step)
            self.is_tweening = false
        end
        return
    end
    local cur_step = CatExploreWGData.Instance:GetCurStep()
    if cur_step >= CatExploreWGData.MAX_STEP_REWARD_COUNT  then
        TipWGCtrl.Instance:ShowSystemMsg(Language.CatExplore.AllBuy)
        return
    end

    local other_cfg = CatExploreWGData.Instance:GetOtherCfg()
    local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.cost_item_id)
    if not item_cfg then
        print_error("=====物品id不存在===", other_cfg.cost_item_id)
        return
    end

    local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.cost_item_id)
    local cost_num = CatExploreWGData.Instance:GetStepCostNum(cur_step + 1)
    if item_num >= cost_num then
        CatExploreWGCtrl.Instance:ReqCatExploreInfo(OA_CAT_VENTURE_OPERTE_TYPE.STEP)
    else
        if not self.alert then
            self.alert = Alert.New()
        end

        self.alert:ClearCheckHook()
        self.alert:SetShowCheckBox(true, "cat_explore")
        self.alert:SetCheckBoxDefaultSelect(false)
        local name = ""
        if item_cfg ~= nil then
            name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
        end

        local cost = other_cfg.cost_gold * (cost_num - item_num)
        local str = string.format(Language.CatExplore.CostStr, name, cost)
        self.alert:SetLableString(str)
        local ok_func = function ()
            local have_enough = RoleWGData.Instance:GetIsEnoughUseGold(cost)
            if have_enough then
                CatExploreWGCtrl.Instance:ReqCatExploreInfo(OA_CAT_VENTURE_OPERTE_TYPE.STEP)
            else
                UiInstanceMgr.Instance:ShowChongZhiView()
            end
        end

        self.alert:SetOkFunc(ok_func)
        self.alert:Open()
    end

end

function CatExploreView:ShowItemTips()
     local other_cfg = CatExploreWGData.Instance:GetOtherCfg()
    local show_id = other_cfg.cost_item_id
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = show_id})
end

-- 根据Scroll的进度更新按钮显示
function CatExploreView:UpdateAnimation(pos)
    if pos then
        self.node_list.btn_to_end:CustomSetActive(pos.x<0.95)
    end
end

function CatExploreView:UpdateTaskAnimation(pos)
    if pos then
        self.node_list.img_left:CustomSetActive(pos.x>0.1)
        self.node_list.img_right:CustomSetActive(pos.x<0.9)
    end
end

function CatExploreView:OnClickClose()
	if self.is_tweening then
        UITween.CleanAllMoveToShowPanel(GuideModuleName.CatExploreView)
        local cur_step = CatExploreWGData.Instance:GetCurStep()
        local is_get = CatExploreWGData.Instance:GetRewardStateBystep(cur_step)
        if not is_get then
            CatExploreWGCtrl.Instance:ReqCatExploreInfo(OA_CAT_VENTURE_OPERTE_TYPE.STEP_REWARD, cur_step)
            self.is_tweening = false
        end
	end
    self:Close()
end

--------------------------------任务格子----------------
CatExploreTaskCell = CatExploreTaskCell or BaseClass(BaseRender)

function CatExploreTaskCell:__init()
    self.reward_item = ItemCell.New(self.node_list["cell_pos"])

    self.node_list["go_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGo,self))
    self.node_list["get_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGetTaskReward,self))
end
function CatExploreTaskCell:__delete()
    if self.reward_item then
        self.reward_item:DeleteMe()
        self.reward_item = nil
    end
end

function CatExploreTaskCell:OnFlush()
    if not self.data then
        return
    end

    self.node_list.go_btn:SetActive(self.data.status == REWARD_STATE_TYPE.UNDONE)
    self.node_list.get_btn:SetActive(self.data.status == REWARD_STATE_TYPE.CAN_FETCH)
    self.node_list.have_get:SetActive(self.data.status == REWARD_STATE_TYPE.FINISH)

    self.node_list.target_text:SetActive(self.data.task_type ~= 2 and self.data.task_type ~= 3)
    local str = self.data.des
    local num = self.data.item_list[0].num
    self.node_list.desc_text.text.text = str
    -- self.node_list.desc_text.text.text = string.format(Language.CatExplore.Desc, str, num)
    local count_str = self.data.progress_num_flag .. "/" .. self.data.target
    self.node_list.target_text.text.text = count_str

    local item_id = self.data.item_list[0].item_id
    -- local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
    -- if item_cfg then
    --     self.node_list["icon_img"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
    -- end
    self.reward_item:SetData({item_id = item_id})
    self.reward_item:SetRightBottomColorText(num)
    self.reward_item:SetRightBottomTextVisible(true)
end

function CatExploreTaskCell:OnClickGo()
    if self.data.open_panel ~= "" then
        FunOpen.Instance:OpenViewNameByCfg(self.data.open_panel)
    end
end

function CatExploreTaskCell:OnClickGetTaskReward()
    CatExploreWGCtrl.Instance:ReqCatExploreInfo(OA_CAT_VENTURE_OPERTE_TYPE.TASK_REWARD, self.data.task_id)
end

-------------------步数奖励格子-------------------
CatExploreStepRewardCell = CatExploreStepRewardCell or BaseClass(BaseRender)

function CatExploreStepRewardCell:__init()
    self.reward_item = ItemCell.New(self.node_list["cell_pos"])
    self.node_list["get_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGetStepReward, self))
end

function CatExploreStepRewardCell:__delete()
    if self.reward_item then
        self.reward_item:DeleteMe()
        self.reward_item = nil
    end
end

function CatExploreStepRewardCell:OnFlush()
    if not self.data then
        return
    end

    local item_id = self.data.item.item_id
    self.reward_item:SetData({item_id = item_id})
    self.reward_item:SetRightBottomColorText(self.data.item.num)
    self.reward_item:SetRightBottomTextVisible(true)
    local cur_step = CatExploreWGData.Instance:GetCurStep()
    local is_get = CatExploreWGData.Instance:GetRewardStateBystep(self.data.step)
    self.node_list.remind:SetActive(not is_get and (cur_step >= self.data.step))
    self.node_list.get_btn:SetActive(not is_get and (cur_step >= self.data.step))
    self.reward_item:SetLingQuVisible(is_get)
    -- self.node_list.mao_zy_hl:SetActive(cur_step >= self.data.step)
    XUI.SetGraphicGrey(self.node_list.maoline_img, cur_step < self.data.step)
    -- self.node_list.step_cout.text.text = self.data.step .. "m"
end

function CatExploreStepRewardCell:GetPos()
    return self.node_list.self_pos.rect.localPosition
end

function CatExploreStepRewardCell:OnClickGetStepReward()
    if not self.data then
        return
    end

    local is_get = CatExploreWGData.Instance:GetRewardStateBystep(self.data.step)  
    if is_get then
        TipWGCtrl.Instance:ShowSystemMsg(Language.CatExplore.IsGetReward)
        return
    end

    local cur_step = CatExploreWGData.Instance:GetCurStep()
    if cur_step >= self.data.step then
         CatExploreWGCtrl.Instance:ReqCatExploreInfo(OA_CAT_VENTURE_OPERTE_TYPE.STEP_REWARD, self.data.step)
    else
        TipWGCtrl.Instance:ShowSystemMsg(Language.CatExplore.NotStep)
    end
end