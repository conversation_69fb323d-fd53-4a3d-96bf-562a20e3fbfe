BossOfferWGData = BossOfferWGData or BaseClass()

function BossOfferWGData:__init()
    if BossOfferWGData.Instance ~= nil then
        <PERSON><PERSON><PERSON><PERSON><PERSON>("[BossOfferWGData] Attemp to create a singleton twice !")
    end
    BossOfferWGData.Instance = self

    self.boss_offer_cfg = ConfigManager.Instance:GetAutoConfig("rabossxuanshang_auto")
    self.grade_cfg = ListToMapList(self.boss_offer_cfg.grade_cfg, "grade_id")
    self.boss_cfg = ListToMapList(self.boss_offer_cfg.boss_guide, "grade_id")
    RemindManager.Instance:Register(RemindName.BossXuanShang, BindTool.Bind(self.GetAwakenRemind, self))
    self.grade_id = 0
    self.grade_reward_fetch_flag = {}
    self.kill_count = {}
    self.active_skill_flag = 0
end

function BossOfferWGData:__delete()
    BossOfferWGData.Instance = nil
    RemindManager.Instance:UnRegister(RemindName.BossXuanShang)
end

function BossOfferWGData:GetBossListByGrade(grade)
    if grade == nil then return end
    local boss_data = {}
    local boss_vo = {}
    --print_error(self.grade_cfg[grade][1].boss_id, self.boss_cfg[grade][1].boss_id_1)
    for i = 1, 5 do
        if self.boss_cfg[grade] and self.boss_cfg[grade][1]["boss_id_"..i] > 0 then
           boss_data = BossWGData.Instance:GetBossOfferCfg(self.grade_cfg[grade][1].boss_id, self.boss_cfg[grade][1]["boss_id_"..i])
           table.insert(boss_vo, boss_data) 
        end
    end
    return boss_vo
end

function BossOfferWGData:GetFirstBossCfgByGrade(grade)
    if self.boss_cfg[grade] then
        return self.boss_cfg[grade][1].boss_id_1
    end
end

function BossOfferWGData:GetBossCfgCountByGrade(grade)
    return self.grade_cfg[grade][1].kill_count
end

function BossOfferWGData:GetCfgByGrade(grade)
     return self.grade_cfg[grade][1]
end

function BossOfferWGData:GetBossKillCount(grade)
    if self.kill_count[grade] then
        return self.kill_count[grade].kill_count
    end

    return 0
end

function BossOfferWGData:GetBossGradeID()
    return self.grade_id
end

function BossOfferWGData:GetRewardFlag(grade)
    if self.kill_count[grade] and self.kill_count[grade].kill_count == nil then return end
    return self.kill_count[grade].kill_count >= self.grade_cfg[grade][1].kill_count
end

function BossOfferWGData:SetBossOfferInfo(protocol)
    self.grade_id = protocol.grade_id
    self.grade_reward_fetch_flag = bit:d2b_two(protocol.grade_reward_fetch_flag)
    self.kill_count = protocol.kill_count
    self.active_skill_flag = protocol.active_skill_flag
    if self.grade_id > #self.grade_cfg then
        self.grade_id = #self.grade_cfg
        self.kill_count[self.grade_id].kill_count = self.grade_cfg[self.grade_id][1].kill_count
    end
   --[[ if not IsEmptyTable(self.grade_cfg) then
        for k, v in pairs(self.grade_cfg) do
            if k == self.grade_id then
                self.grade_id = self.grade_id
                return
            else
                self.grade_id = #self.grade_cfg
                return
            end
        end
    end--]]
    --print_error(self.grade_id, self.grade_reward_fetch_flag, self.kill_count,  self.active_skill_flag)
end

function BossOfferWGData:GetOtherCfg()
    return self.boss_offer_cfg.other
end

--所有挡是否完成
function BossOfferWGData:GetBossOfferAwakenIsCompleted()
    if self.active_skill_flag == 1 then
        return true
    end

    return false
end

function BossOfferWGData:GetAwakenRemind()
    if self.grade_reward_fetch_flag[self.grade_id - 1] and self.grade_reward_fetch_flag[self.grade_id - 1] == 0 and self.kill_count[self.grade_id].kill_count >= self.grade_cfg[self.grade_id][1].kill_count then
        return 1
    end

    return 0
end

