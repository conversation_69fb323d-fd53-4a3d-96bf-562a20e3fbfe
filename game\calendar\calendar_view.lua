CalendarView = CalendarView or BaseClass(SafeBaseView)
--local COUNT = 5 --每次打开最多显示5个

local HEIGHT_VALUE = 104
local HEIGHT_SPACE_VALUE = -1
local HEIGHT_MAX_VALUE = 420
function CalendarView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/calendar_prefab", "layout_calendar_notice")
end

function CalendarView:__delete()
end

function CalendarView:OpenCallBack()
	CalendarWGData.Instance:SetOpened(true)

	RemindManager.Instance:Fire(RemindName.Calendar)
end

function CalendarView:LoadCallBack()
	self.calendar_item_list = {}
	self.cur_calendar_item_list = {}
end

function CalendarView:ShowIndexCallBack()
end

function CalendarView:ReleaseCallBack()
    if self.calendar_item_list then
    	for i,v in ipairs(self.calendar_item_list) do
    		v:DeleteMe()
    	end
    	self.calendar_item_list = {}
    end

	if self.cur_calendar_item_list then
    	for i,v in ipairs(self.cur_calendar_item_list) do
    		v:DeleteMe()
    	end
    	self.cur_calendar_item_list = nil
    end
end


function CalendarView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "pos" then
			-- self.node_list["group"].transform.position = v.world_pos + Vector3(-19, -3, 0)
		end
	end
	-- self:FlushAllItem()
	self:FlushAllTodayItem()
end

function CalendarView:OnClickOpen(item)
end


function CalendarView:FlushAllItem()
	for i,v in ipairs(self.calendar_item_list) do
		v:SetActive(false)
	end

	local calendar_cfg = CalendarWGData.Instance:GetCalendarActivityCfg()
	for i,v in ipairs(calendar_cfg) do
		if not self.calendar_item_list[i] then
			local go = ResMgr:Instantiate(self.node_list["calendar_item_prefab"].gameObject)
			go:SetActive(true)
			go.transform:SetParent(self.node_list["group"].transform, false)
			self.calendar_item_list[i] = CalendarItem.New(go)
		end
		self.calendar_item_list[i]:SetActive(true)
		self.calendar_item_list[i]:SetData(v)
	end

	-- self.node_list["sell_empty_tips"]:SetActive(IsEmptyTable(calendar_cfg))
end

function CalendarView:FlushAllTodayItem()
	local calendar_cfg = CalendarWGData.Instance:GetCalendarActivityNoticeCfg()
	for i,v in ipairs(calendar_cfg) do
		if not self.cur_calendar_item_list[i] then
			local go = ResMgr:Instantiate(self.node_list["calendar_item_prefab"].gameObject)
			go.transform:SetParent(self.node_list["content"].transform, false)
			self.cur_calendar_item_list[i] = CalendarItem.New(go)
		end

		self.cur_calendar_item_list[i]:SetActive(true)
		self.cur_calendar_item_list[i]:SetData(v)
	end
	
	local count = math.ceil(#calendar_cfg / 3)
	local y_value = count * HEIGHT_VALUE + (count - 1) * HEIGHT_SPACE_VALUE
	y_value = math.max(HEIGHT_VALUE, y_value)
	y_value = math.min(y_value, HEIGHT_MAX_VALUE)
	RectTransform.SetSizeDeltaXY(self.node_list.today_list.rect, 270, y_value)
end

function CalendarView:OnClickAct(item)
	self:Close()
end

-------------------------------------------------所有活动item------------------------------------------------------
CalendarItem = CalendarItem or BaseClass(BaseRender)
function CalendarItem:__init()
	XUI.AddClickEventListener(self.node_list["go_btn"], BindTool.Bind(self.OnClickAct, self))
end

function CalendarItem:OnFlush()
	-- 图标
	local bundle, asset = ResPath.GetF2MainUIImage(self.data.cfg.zjm_zp_btn_name)
	self.node_list["act_icon"].image:LoadSprite(bundle, asset)

	-- 时间
	self.node_list["open_time"].text.text = self.data.str
end


function CalendarItem:OnClickAct()
	CalendarWGCtrl.Instance:OnClickAct(self.data)
	ViewManager.Instance:Close(GuideModuleName.CalendarView)
end


-------------------------------------------------今日活动item----------------------------------------------------------
CalendarNoticeItem = CalendarNoticeItem or BaseClass(BaseRender)
function CalendarNoticeItem:__init()
	XUI.AddClickEventListener(self.node_list["go_btn"], BindTool.Bind(self.OnClickAct, self))
end

function CalendarNoticeItem:OnFlush()
	-- 图标
	local bundle, asset = ResPath.GetF2MainUIImage(self.data.cfg.zjm_zp_btn_name)
	self.node_list["act_icon"].image:LoadSprite(bundle, asset, function()
		self.node_list["act_icon"].image:SetNativeSize()
	end)

	-- 时间
	local color = self.data.is_openning and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
	self.node_list["open_time"].text.text = ToColorStr(self.data.str, color)

	self.node_list["openning_label"]:SetActive(self.data.is_openning)
	self.node_list["close_label"]:SetActive(not self.data.is_openning)
end

function CalendarNoticeItem:OnClickAct()
	CalendarWGCtrl.Instance:OnClickAct(self.data)
	ViewManager.Instance:Close(GuideModuleName.CalendarView)
end
