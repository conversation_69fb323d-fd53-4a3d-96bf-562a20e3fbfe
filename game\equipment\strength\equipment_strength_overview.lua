EquipmentStrengthOverview = EquipmentStrengthOverview or BaseClass(SafeBaseView)

function EquipmentStrengthOverview:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 14),sizeDelta = Vector2(1080, 614)})
	self:AddViewResource(0, "uis/view/equipment_ui_prefab", "layout_strength_overview")
end

function EquipmentStrengthOverview:LoadCallBack()
    if not self.strength_equip_body_list then
        self.strength_equip_body_list = AsyncListView.New(SthEquipBodyOverviewItemCellRender, self.node_list.strength_equip_body_list)
    end

    self.node_list.title_view_name.text.text = Language.Equip.StrengthOverviewTitle
end

function EquipmentStrengthOverview:ReleaseCallBack()
    if self.strength_equip_body_list then
        self.strength_equip_body_list:DeleteMe()
        self.strength_equip_body_list = nil
    end
end

function EquipmentStrengthOverview:OnFlush()
    local data_list = EquipBodyWGData.Instance:GetTotalEquipBodyDataList()
    self.strength_equip_body_list:SetDataList(data_list)
end


---------------------------------SthEquipBodyOverviewItemCellRender------------------------------------
SthEquipBodyOverviewItemCellRender = SthEquipBodyOverviewItemCellRender or BaseClass(BaseRender)

function SthEquipBodyOverviewItemCellRender:LoadCallBack()
	if not self.equip_qh_list then
	    self.equip_qh_list = {}
	    for part = 0, GameEnum.EQUIP_INDEX_XIANZHUO do
	        self.equip_qh_list[part] = SthEquipBodyOverviewEquipCellRender.New(self.node_list["item_" .. part])
	        self.equip_qh_list[part]:SetIndex(part)
            self.equip_qh_list[part]:SetClickCallBack(BindTool.Bind(self.OnClickStrengthListCallBack, self))
	    end
    end

    self.default_select_part = -1
    self.equip_body_seq_cache = -1
    self.default_select_part_data = {}

    XUI.AddClickEventListener(self.node_list["btn_to_cuihuo"], BindTool.Bind(self.OnClickToCuiHuoBtn, self)) 
    XUI.AddClickEventListener(self.node_list["btn_strebgth"], BindTool.Bind(self.OnClickStrengthBtn, self)) 
end

function SthEquipBodyOverviewItemCellRender:__delete()
    if self.equip_qh_list then
        for k, v in pairs(self.equip_qh_list) do
            v:DeleteMe()
        end

        self.equip_qh_list = nil
    end

    self.default_select_part = nil
    self.equip_body_seq_cache = nil
    self.default_select_part_data = nil
end

function SthEquipBodyOverviewItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    if self.equip_body_seq_cache ~= self.data.seq then
        self.default_select_part = -1
        self.default_select_part_data = {}
    end

    self.equip_body_seq_cache = self.data.seq

    local unlock = EquipBodyWGData.Instance:IsEquipBodyUnLock(self.data.seq)
    local is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyWearEquip(self.data.seq)

    self.node_list.strength_cell:CustomSetActive(unlock and is_wear_equip)
    self.node_list.unlock:CustomSetActive(not unlock or not is_wear_equip)
    self.node_list.btn_strebgth:CustomSetActive(unlock and is_wear_equip)
    self.node_list.name.text.text = self.data.name

    if unlock and is_wear_equip then
        local equip_data_list = EquipmentWGData.Instance:GetRoleEquipStrengthListData(self.data.seq)
        for k,v in pairs(self.equip_qh_list) do
            v:SetData(equip_data_list[k])
        end

        self:SetDefaultSelect(equip_data_list)
    else
        self.node_list.desc_tip.text.text = not unlock and Language.RoleEquipBody.EquipBodyLock or Language.RoleEquipBody.EquipBodyNotWearEquip
    end

    -- local total_level = EquipmentWGData.Instance:GetTotalStrengthLevel(self.data.seq) + EquipmentWGData.Instance:GetTotalXianQiStrengthLevel(self.data.seq)
    -- self.node_list.desc_btn_strebgth.text.text = string.format(Language.Equip.StrengthOverviewLevel, total_level)
end

function SthEquipBodyOverviewItemCellRender:SetDefaultSelect(equip_data_list)
    if self.default_select_part < 0 and not IsEmptyTable(equip_data_list) then
        for k, v in pairs(equip_data_list) do
            if not IsEmptyTable(v) then
                if v.item_id > 0 then
                    self:OnClickStrengthListCallBack(self.equip_qh_list[k], true)
                    return
                end
            end
        end
    end
    
    if self.default_select_part < 0 then
        self.node_list.desc_btn_strebgth.text.text = string.format(Language.Equip.StrengthOverviewLevel, 0)
    end
end

function SthEquipBodyOverviewItemCellRender:OnClickStrengthListCallBack(cell, is_default_select)
    if nil == cell or IsEmptyTable(cell.data) then
        return 
    end

    local equip_part = cell.index 
    local data = cell.data

    -- 策划hlj要显示类型的总等级
    local equip_big_type = EquipmentWGData.GetEquipSuitTypeByPartType(equip_part)
    local cur_total_level = 0

    if equip_big_type == GameEnum.EQUIP_BIG_TYPE_NORMAL then
        cur_total_level = EquipmentWGData.Instance:GetTotalStrengthLevel(self.data.seq)
    else
        cur_total_level = EquipmentWGData.Instance:GetTotalXianQiStrengthLevel(self.data.seq)
    end

    -- self.node_list.desc_btn_strebgth.text.text = string.format(Language.Equip.StrengthOverviewLevel, data.strengthen_level)
    self.node_list.desc_btn_strebgth.text.text = string.format(Language.Equip.StrengthOverviewLevel, cur_total_level)

    if self.default_select_part == equip_part then
        if not is_default_select then
            TipWGCtrl.Instance:OpenItem({item_id = data.item_id})
        end
    end

    self:SetSelectEquip(cell.index)
    self.default_select_part = equip_part
    self.default_select_part_data = data
end

function SthEquipBodyOverviewItemCellRender:SetSelectEquip(equip_part)
    if self.equip_qh_list then
        for k, v in pairs(self.equip_qh_list) do
            v:OnSelectChange(v.index == equip_part)
        end
    end
end

-- 跳转对应淬火
function SthEquipBodyOverviewItemCellRender:OnClickToCuiHuoBtn()
    local unlock = EquipBodyWGData.Instance:IsEquipBodyUnLock(self.data.seq)
    local is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyWearEquip(self.data.seq)

    if not unlock or not is_wear_equip then
        local str = not unlock and Language.RoleEquipBody.EquipBodyLock or Language.RoleEquipBody.EquipBodyNotWearEquip
        SysMsgWGCtrl.Instance:ErrorRemind(str)
        return
    end

    if unlock and is_wear_equip then
        EquipmentWGCtrl.Instance:Flush(TabIndex.equipment_strength, "strength_change_to_equip_body", {equip_body_seq = self.data.seq, selct_part_data = self.default_select_part_data})
        EquipmentWGCtrl.Instance:CloseStrengthOverviewView()
    end
end

-- 打开淬火加成
function SthEquipBodyOverviewItemCellRender:OnClickStrengthBtn()
    RoleWGCtrl.Instance:SetEquipTextData(ROLE_EQUIP_ATTR_TIPS.STREHGTH_TIP, self.data.seq)
	RoleWGCtrl.Instance:OpenEquipAttr()
    -- EquipmentWGCtrl.Instance:CloseStrengthOverviewView()
end

---------------------------------SthEquipBodyOverviewEquipCellRender------------------------------------
SthEquipBodyOverviewEquipCellRender = SthEquipBodyOverviewEquipCellRender or BaseClass(BaseRender)

function SthEquipBodyOverviewEquipCellRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.item_node)
	self.item_cell:SetIsShowTips(false)

	self.node_list.select_img:SetActive(false)
    -- self.node_list.block_click:SetActive(false)
    self.node_list.remind:SetActive(false)
	self:OnSelectChange(false)
	XUI.AddClickEventListener(self.node_list.block_click, BindTool.Bind(self.OnClick, self))
end

function SthEquipBodyOverviewEquipCellRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function SthEquipBodyOverviewEquipCellRender:OnSelectChange(is_select)
    if self.node_list.select_img then
        self.node_list.select_img:CustomSetActive(is_select)
    end
end

function SthEquipBodyOverviewEquipCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.item_cell:ClearData()
		self.item_cell:SetItemIcon(ResPath.GetEquipIcon(self.index))
		return
	end

	self.item_cell:SetData(self.data)
    self.item_cell:SetRightTopImageTextActive(false)
	self:OtherFlush()
end

function SthEquipBodyOverviewEquipCellRender:OtherFlush()
	local level = self.data.strengthen_level or 0

	if level > 0 then
		self.node_list.level_text.text.text = "+" .. level
	else
		self.node_list.level_text.text.text = Language.Equip.NoStrengthen
	end
end

function SthEquipBodyOverviewEquipCellRender:NoDataFlush()
	self.node_list.level_text.text.text = ""
    self:OnSelectChange(false)
end