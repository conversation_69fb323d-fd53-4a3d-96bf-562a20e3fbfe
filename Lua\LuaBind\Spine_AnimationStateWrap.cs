﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Spine_AnimationStateWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(Spine.AnimationState), typeof(System.Object));
		<PERSON><PERSON>RegFunction("AssignEventSubscribersFrom", AssignEventSubscribersFrom);
		<PERSON><PERSON>unction("AddEventSubscribersFrom", AddEventSubscribersFrom);
		<PERSON><PERSON>RegFunction("Update", Update);
		L.RegFunction("Apply", Apply);
		<PERSON><PERSON>RegFunction("ApplyEventTimelinesOnly", ApplyEventTimelinesOnly);
		<PERSON><PERSON>RegFunction("ClearTracks", ClearTracks);
		<PERSON><PERSON>unction("ClearTrack", ClearTrack);
		L.RegFunction("SetAnimation", SetAnimation);
		<PERSON>.RegFunction("AddAnimation", AddAnimation);
		<PERSON><PERSON>RegFunction("SetEmptyAnimation", SetEmptyAnimation);
		<PERSON><PERSON>unction("AddEmptyAnimation", AddEmptyAnimation);
		<PERSON><PERSON>RegFunction("SetEmptyAnimations", SetEmptyAnimations);
		<PERSON><PERSON>RegFunction("ClearNext", ClearNext);
		L.RegFunction("GetCurrent", GetCurrent);
		L.RegFunction("ClearListenerNotifications", ClearListenerNotifications);
		L.RegFunction("ToString", ToString);
		L.RegFunction("New", _CreateSpine_AnimationState);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("TimeScale", get_TimeScale, set_TimeScale);
		L.RegVar("Data", get_Data, set_Data);
		L.RegVar("Tracks", get_Tracks, null);
		L.RegVar("Start", get_Start, set_Start);
		L.RegVar("Interrupt", get_Interrupt, set_Interrupt);
		L.RegVar("End", get_End, set_End);
		L.RegVar("Dispose", get_Dispose, set_Dispose);
		L.RegVar("Complete", get_Complete, set_Complete);
		L.RegVar("Event", get_Event, set_Event);
		L.RegFunction("TrackEntryEventDelegate", Spine_AnimationState_TrackEntryEventDelegate);
		L.RegFunction("TrackEntryDelegate", Spine_AnimationState_TrackEntryDelegate);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateSpine_AnimationState(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				Spine.AnimationStateData arg0 = (Spine.AnimationStateData)ToLua.CheckObject<Spine.AnimationStateData>(L, 1);
				Spine.AnimationState obj = new Spine.AnimationState(arg0);
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: Spine.AnimationState.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AssignEventSubscribersFrom(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject<Spine.AnimationState>(L, 1);
			Spine.AnimationState arg0 = (Spine.AnimationState)ToLua.CheckObject<Spine.AnimationState>(L, 2);
			obj.AssignEventSubscribersFrom(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddEventSubscribersFrom(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject<Spine.AnimationState>(L, 1);
			Spine.AnimationState arg0 = (Spine.AnimationState)ToLua.CheckObject<Spine.AnimationState>(L, 2);
			obj.AddEventSubscribersFrom(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Update(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject<Spine.AnimationState>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.Update(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Apply(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject<Spine.AnimationState>(L, 1);
			Spine.Skeleton arg0 = (Spine.Skeleton)ToLua.CheckObject<Spine.Skeleton>(L, 2);
			bool o = obj.Apply(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ApplyEventTimelinesOnly(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject<Spine.AnimationState>(L, 1);
				Spine.Skeleton arg0 = (Spine.Skeleton)ToLua.CheckObject<Spine.Skeleton>(L, 2);
				bool o = obj.ApplyEventTimelinesOnly(arg0);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 3)
			{
				Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject<Spine.AnimationState>(L, 1);
				Spine.Skeleton arg0 = (Spine.Skeleton)ToLua.CheckObject<Spine.Skeleton>(L, 2);
				bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
				bool o = obj.ApplyEventTimelinesOnly(arg0, arg1);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Spine.AnimationState.ApplyEventTimelinesOnly");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearTracks(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject<Spine.AnimationState>(L, 1);
			obj.ClearTracks();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearTrack(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject<Spine.AnimationState>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.ClearTrack(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetAnimation(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 4 && TypeChecker.CheckTypes<string, bool>(L, 3))
			{
				Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject<Spine.AnimationState>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				string arg1 = ToLua.ToString(L, 3);
				bool arg2 = LuaDLL.lua_toboolean(L, 4);
				Spine.TrackEntry o = obj.SetAnimation(arg0, arg1, arg2);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<Spine.Animation, bool>(L, 3))
			{
				Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject<Spine.AnimationState>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				Spine.Animation arg1 = (Spine.Animation)ToLua.ToObject(L, 3);
				bool arg2 = LuaDLL.lua_toboolean(L, 4);
				Spine.TrackEntry o = obj.SetAnimation(arg0, arg1, arg2);
				ToLua.PushObject(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Spine.AnimationState.SetAnimation");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddAnimation(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 5 && TypeChecker.CheckTypes<string, bool, float>(L, 3))
			{
				Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject<Spine.AnimationState>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				string arg1 = ToLua.ToString(L, 3);
				bool arg2 = LuaDLL.lua_toboolean(L, 4);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 5);
				Spine.TrackEntry o = obj.AddAnimation(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 5 && TypeChecker.CheckTypes<Spine.Animation, bool, float>(L, 3))
			{
				Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject<Spine.AnimationState>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				Spine.Animation arg1 = (Spine.Animation)ToLua.ToObject(L, 3);
				bool arg2 = LuaDLL.lua_toboolean(L, 4);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 5);
				Spine.TrackEntry o = obj.AddAnimation(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Spine.AnimationState.AddAnimation");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetEmptyAnimation(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject<Spine.AnimationState>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			Spine.TrackEntry o = obj.SetEmptyAnimation(arg0, arg1);
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddEmptyAnimation(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject<Spine.AnimationState>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
			Spine.TrackEntry o = obj.AddEmptyAnimation(arg0, arg1, arg2);
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetEmptyAnimations(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject<Spine.AnimationState>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.SetEmptyAnimations(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearNext(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject<Spine.AnimationState>(L, 1);
			Spine.TrackEntry arg0 = (Spine.TrackEntry)ToLua.CheckObject<Spine.TrackEntry>(L, 2);
			obj.ClearNext(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetCurrent(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject<Spine.AnimationState>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			Spine.TrackEntry o = obj.GetCurrent(arg0);
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearListenerNotifications(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject<Spine.AnimationState>(L, 1);
			obj.ClearListenerNotifications();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ToString(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject<Spine.AnimationState>(L, 1);
			string o = obj.ToString();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_TimeScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.AnimationState obj = (Spine.AnimationState)o;
			float ret = obj.TimeScale;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index TimeScale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Data(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.AnimationState obj = (Spine.AnimationState)o;
			Spine.AnimationStateData ret = obj.Data;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Data on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Tracks(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.AnimationState obj = (Spine.AnimationState)o;
			Spine.ExposedList<Spine.TrackEntry> ret = obj.Tracks;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Tracks on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Start(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Spine.AnimationState.TrackEntryDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Interrupt(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Spine.AnimationState.TrackEntryDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_End(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Spine.AnimationState.TrackEntryDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Dispose(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Spine.AnimationState.TrackEntryDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Complete(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Spine.AnimationState.TrackEntryDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Event(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Spine.AnimationState.TrackEntryEventDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_TimeScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.AnimationState obj = (Spine.AnimationState)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.TimeScale = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index TimeScale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Data(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.AnimationState obj = (Spine.AnimationState)o;
			Spine.AnimationStateData arg0 = (Spine.AnimationStateData)ToLua.CheckObject<Spine.AnimationStateData>(L, 2);
			obj.Data = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Data on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Start(IntPtr L)
	{
		try
		{
			Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject(L, 1, typeof(Spine.AnimationState));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Spine.AnimationState.Start' can only appear on the left hand side of += or -= when used outside of the type 'Spine.AnimationState'");
			}

			if (arg0.op == EventOp.Add)
			{
				Spine.AnimationState.TrackEntryDelegate ev = (Spine.AnimationState.TrackEntryDelegate)arg0.func;
				obj.Start += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Spine.AnimationState.TrackEntryDelegate ev = (Spine.AnimationState.TrackEntryDelegate)arg0.func;
				obj.Start -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Interrupt(IntPtr L)
	{
		try
		{
			Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject(L, 1, typeof(Spine.AnimationState));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Spine.AnimationState.Interrupt' can only appear on the left hand side of += or -= when used outside of the type 'Spine.AnimationState'");
			}

			if (arg0.op == EventOp.Add)
			{
				Spine.AnimationState.TrackEntryDelegate ev = (Spine.AnimationState.TrackEntryDelegate)arg0.func;
				obj.Interrupt += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Spine.AnimationState.TrackEntryDelegate ev = (Spine.AnimationState.TrackEntryDelegate)arg0.func;
				obj.Interrupt -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_End(IntPtr L)
	{
		try
		{
			Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject(L, 1, typeof(Spine.AnimationState));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Spine.AnimationState.End' can only appear on the left hand side of += or -= when used outside of the type 'Spine.AnimationState'");
			}

			if (arg0.op == EventOp.Add)
			{
				Spine.AnimationState.TrackEntryDelegate ev = (Spine.AnimationState.TrackEntryDelegate)arg0.func;
				obj.End += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Spine.AnimationState.TrackEntryDelegate ev = (Spine.AnimationState.TrackEntryDelegate)arg0.func;
				obj.End -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Dispose(IntPtr L)
	{
		try
		{
			Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject(L, 1, typeof(Spine.AnimationState));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Spine.AnimationState.Dispose' can only appear on the left hand side of += or -= when used outside of the type 'Spine.AnimationState'");
			}

			if (arg0.op == EventOp.Add)
			{
				Spine.AnimationState.TrackEntryDelegate ev = (Spine.AnimationState.TrackEntryDelegate)arg0.func;
				obj.Dispose += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Spine.AnimationState.TrackEntryDelegate ev = (Spine.AnimationState.TrackEntryDelegate)arg0.func;
				obj.Dispose -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Complete(IntPtr L)
	{
		try
		{
			Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject(L, 1, typeof(Spine.AnimationState));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Spine.AnimationState.Complete' can only appear on the left hand side of += or -= when used outside of the type 'Spine.AnimationState'");
			}

			if (arg0.op == EventOp.Add)
			{
				Spine.AnimationState.TrackEntryDelegate ev = (Spine.AnimationState.TrackEntryDelegate)arg0.func;
				obj.Complete += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Spine.AnimationState.TrackEntryDelegate ev = (Spine.AnimationState.TrackEntryDelegate)arg0.func;
				obj.Complete -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Event(IntPtr L)
	{
		try
		{
			Spine.AnimationState obj = (Spine.AnimationState)ToLua.CheckObject(L, 1, typeof(Spine.AnimationState));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Spine.AnimationState.Event' can only appear on the left hand side of += or -= when used outside of the type 'Spine.AnimationState'");
			}

			if (arg0.op == EventOp.Add)
			{
				Spine.AnimationState.TrackEntryEventDelegate ev = (Spine.AnimationState.TrackEntryEventDelegate)arg0.func;
				obj.Event += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Spine.AnimationState.TrackEntryEventDelegate ev = (Spine.AnimationState.TrackEntryEventDelegate)arg0.func;
				obj.Event -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Spine_AnimationState_TrackEntryEventDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<Spine.AnimationState.TrackEntryEventDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<Spine.AnimationState.TrackEntryEventDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Spine_AnimationState_TrackEntryDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<Spine.AnimationState.TrackEntryDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<Spine.AnimationState.TrackEntryDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

