using DG.Tweening;
using UnityEngine;
using UnityEngine.Experimental.Rendering.Universal;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

[RequireComponent(typeof(Volume))]
public class VolumeController : MonoBehaviour
{
    private static int newMotionBlurCount = 0;
    private Volume volume;
    private VolumeProfile profile;

    private Bloom bloom;
    private DepthOfField depthOfField;
    private ColorAdjustments colorAdjustments;
    private ColorCurves colorCurves;
    private ColorLookup colorLookup;
    private Vignette vignette;
    private FilmGrain filmGrain;
    private LensDistortion lensDistortion;
    private LiftGammaGain liftGammaGain;
    private MotionBlur motionBlur;
    private RadialBlur radialBlur;
    private PaniniProjection paniniProjection;
    private ShadowsMidtonesHighlights shadowsMidtonesHighlights;
    private SplitToning splitToning;
    private Tonemapping tonemapping;
    private WhiteBalance whiteBalance;
    private ChannelMixer channelMixer;
    private ChromaticAberration chromaticAberration;
    private NewMotionBlur newMotionBlur;
    private NewMotionBlurRenderPassFeature newMotionBlurRenderPassFeature;

    private Sequence activeRadialBlurSequence;
    private void Start()
    {
        newMotionBlurRenderPassFeature = NewMotionBlurRenderPassFeature.Instance;
    }

    private void Awake()
    {
        volume = GetComponent<Volume>();
        if (volume == null)
        {
            Debug.LogError("Volume object is null.");
            return;
        }

        profile = volume.profile;
        if (profile == null)
        {
            Debug.LogError("Volume profile is null. Please create one in the Volume component.");
            return;
        }
        
        InitOverridesBind();
    }

    void OnDestroy()
    {
        CleanRadialBlurSequence();
        if (newMotionBlur != null)
        {
            newMotionBlurCount--;
        }

        if (newMotionBlurCount <= 0 && newMotionBlurRenderPassFeature != null)
        {
            newMotionBlurRenderPassFeature.SetActive(false);
        }
    }

    private void InitOverridesBind()
    {
        profile.TryGet(out bloom);
        profile.TryGet(out depthOfField);
        profile.TryGet(out colorAdjustments);
        profile.TryGet(out colorCurves);
        profile.TryGet(out colorLookup);
        profile.TryGet(out vignette);
        profile.TryGet(out filmGrain);
        profile.TryGet(out lensDistortion);
        profile.TryGet(out liftGammaGain);
        profile.TryGet(out motionBlur);
        profile.TryGet(out radialBlur);
        profile.TryGet(out paniniProjection);
        profile.TryGet(out shadowsMidtonesHighlights);
        profile.TryGet(out splitToning);
        profile.TryGet(out tonemapping);
        profile.TryGet(out whiteBalance);
        profile.TryGet(out channelMixer);
        profile.TryGet(out chromaticAberration);
        profile.TryGet(out newMotionBlur);

        if(newMotionBlur != null)
        {
            newMotionBlurCount++;
        }
    }

    public Volume GetVolume()
    {
        return volume;
    }

    public void SetVolumeEnable(bool enabled)
    {
        if (this.volume == null) return;
        volume.enabled = enabled;
    }

    public void SetProfile(VolumeProfile profile)
    {
        if (this.volume == null) return;
        if (profile == null) return;
        this.profile = profile;
        this.InitOverridesBind();
    }

    public Bloom GetBloom()
    {
        return bloom;
    }

    public void SetBloomEnabled(bool enabled)
    {
        if (bloom == null)
        {
            return;
        }

        bloom.active = enabled;
    }

    public DepthOfField GetDepthOfField()
    {
        return depthOfField;
    }

    public void SetDepthOfFieldEnabled(bool enabled)
    {
        if (depthOfField == null)
        {
            return;
        }

        depthOfField.active = enabled;
    }

    public Vignette GetVignette()
    {
        return vignette;
    }

    public void SetVignetteEnabled(bool enabled)
    {
        if (vignette == null)
        {
            return;
        }

        vignette.active = enabled;
    }

    public ColorAdjustments GetColorAdjustments()
    {
        return colorAdjustments;
    }

    public void SetColorAdjustmentsEnabled(bool enabled)
    {
        if (colorAdjustments == null)
        {
            return;
        }

        colorAdjustments.active = enabled;
    }

    public ColorCurves GetColorCurves()
    {
        return colorCurves;
    }

    public void SetColorCurvesEnabled(bool enabled)
    {
        if (colorCurves == null)
        {
            return;
        }

        colorCurves.active = enabled;
    }

    public ColorLookup GetColorLookup()
    {
        return colorLookup;
    }

    public void SetColorLookupEnabled(bool enabled)
    {
        if (colorLookup == null)
        {
            return;
        }

        colorLookup.active = enabled;
    }

    public FilmGrain GetFilmGrain()
    {
        return filmGrain;
    }

    public void SetFilmGrainEnabled(bool enabled)
    {
        if (filmGrain == null)
        {
            return;
        }

        filmGrain.active = enabled;
    }

    public LensDistortion GetLensDistortion()
    {
        return lensDistortion;
    }

    public void SetLensDistortionEnabled(bool enabled)
    {
        if (lensDistortion == null)
        {
            return;
        }

        lensDistortion.active = enabled;
    }

    public LiftGammaGain GetLiftGammaGain()
    {
        return liftGammaGain;
    }

    public void SetLiftGammaGainEnabled(bool enabled)
    {
        if (liftGammaGain == null)
        {
            return;
        }

        liftGammaGain.active = enabled;
    }

    public MotionBlur GetMotionBlur()
    {
        return motionBlur;
    }

    public void SetMotionBlurEnabled(bool enabled)
    {
        if (motionBlur == null)
        {
            return;
        }

        motionBlur.active = enabled;
    }

    #region RadialBlur 径向模糊

    public RadialBlur GetRadialBlur()
    {
        return radialBlur;
    }
    
    public void SetRadialBlurEnabled(bool enabled)
    {
        if (radialBlur == null)
        {
            return;
        }

        radialBlur.active = enabled;
    }

    private void CleanRadialBlurSequence()
    {
        if (activeRadialBlurSequence != null && activeRadialBlurSequence.IsActive())
        {
            activeRadialBlurSequence.Kill();
        }
    }

    public void DoRadialBlur(Vector3 worldPos, float riseTime, float holdTime, float fallTime, float strength)
    {
        if(Camera.main == null || !Camera.main.enabled)
        {
            return;
        }

        Vector2 screenPos = Camera.main.WorldToViewportPoint(worldPos);
        DoRadialBlur(screenPos, riseTime, holdTime, fallTime, strength);
    }

    /// <summary>
    /// 径向模糊
    /// </summary>
    /// <param name="screenPos">屏幕位置</param>
    /// <param name="riseTime">上升时间</param>
    /// <param name="holdTime">保持时间</param>
    /// <param name="fallTime">下降时间</param>
    /// <param name="strength">模糊强度</param>
    public void DoRadialBlur(Vector2 screenPos, float riseTime = 0, float holdTime = 0, float fallTime = 0, float strength = 0)
    {
        if (radialBlur == null || screenPos == null) return;
        if (riseTime < 0 && holdTime < 0 && fallTime < 0)
        {
            Debug.LogError($"径向模糊传参为负数，上升时间={riseTime}， 保持时间={holdTime}， 下降时间={fallTime} ");
            return;
        }

        strength = Mathf.Clamp(strength, 0, 1);

        CleanRadialBlurSequence();

        radialBlur.center.value = screenPos;
        radialBlur.strength.value = 0;

        activeRadialBlurSequence = DOTween.Sequence();
        activeRadialBlurSequence.Append(DOTween.To(
            () => radialBlur != null ? radialBlur.strength.value : 0,
            v => { if (radialBlur != null) radialBlur.strength.value = v; },
            strength,
            riseTime))
        .AppendInterval(holdTime)
        .Append(DOTween.To(
            () => radialBlur != null ? radialBlur.strength.value : 0,
            v => { if (radialBlur != null) radialBlur.strength.value = v; },
            0,
            fallTime))
        .OnComplete(() => 
        {
            if (radialBlur != null) radialBlur.strength.value = 0;
            activeRadialBlurSequence = null;
        });
    }

    public void StopRadialBlurTweenImmediately()
    {
        if (activeRadialBlurSequence != null)
        {
            activeRadialBlurSequence.Complete(true);
        }
    }

    public void PauseRadialBlurTween()
    {
        if (activeRadialBlurSequence != null)
        {
            activeRadialBlurSequence.Pause();
        }
    }

    public void ResumeRadialBlurTween()
    {
        if (activeRadialBlurSequence != null)
        {
            activeRadialBlurSequence.Play();
        }
    }
    #endregion

    public PaniniProjection GetPaniniProjection()
    {
        return paniniProjection;
    }

    public void SetPaniniProjectionEnabled(bool enabled)
    {
        if (paniniProjection == null)
        {
            return;
        }

        paniniProjection.active = enabled;
    }

    public ShadowsMidtonesHighlights GetShadowsMidtonesHighlights()
    {
        return shadowsMidtonesHighlights;
    }

    public void SetShadowsMidtonesHighlightsEnabled(bool enabled)
    {
        if (shadowsMidtonesHighlights == null)
        {
            return;
        }

        shadowsMidtonesHighlights.active = enabled;
    }

    public SplitToning GetSplitToning()
    {
        return splitToning;
    }

    public void SetSplitToningEnabled(bool enabled)
    {
        if (splitToning == null)
        {
            return;
        }

        splitToning.active = enabled;
    }

    public Tonemapping GetTonemapping()
    {
        return tonemapping;
    }

    public void SetTonemappingEnabled(bool enabled)
    {
        if (tonemapping == null)
        {
            return;
        }

        tonemapping.active = enabled;
    }

    public WhiteBalance GetWhiteBalance()
    {
        return whiteBalance;
    }

    public void SetWhiteBalanceEnabled(bool enabled)
    {
        if (whiteBalance == null)
        {
            return;
        }

        whiteBalance.active = enabled;
    }

    public ChannelMixer GetChannelMixer()
    {
        return channelMixer;
    }

    public void SetChannelMixerEnabled(bool enabled)
    {
        if (channelMixer == null)
        {
            return;
        }

        channelMixer.active = enabled;
    }

    public ChromaticAberration GetChromaticAberration()
    {
        return chromaticAberration;
    }

    public void SetChromaticAberrationEnabled(bool enabled)
    {
        if (chromaticAberration == null)
        {
            return;
        }

        chromaticAberration.active = enabled;
    }
    public NewMotionBlur GetNewMotionBlur()
    {
        return newMotionBlur;
    }

    public void SetNewMotionBlurEnabled(bool enabled)
    {
        if (newMotionBlur == null || newMotionBlurRenderPassFeature == null)
        {
            return;
        }

        newMotionBlur.active = enabled;
        newMotionBlurRenderPassFeature.SetActive(enabled);
    }

    public void SetNewMotionBlurDist(float value)
    {
        if (newMotionBlur == null)
        {
            return;
        }

        newMotionBlur.SampleDist.value = value;
    }

    public void SetNewMotionBlurStrength(float value)
    {
        if (newMotionBlur == null)
        {
            return;
        }

        newMotionBlur.SampleStrength.value = value;
    }

    public void DoNewMotionBlurStrength(float endValue, float duration)
    {
        if (newMotionBlur == null)
        {
            return;
        }
        DOTween.To(
            () => newMotionBlur.SampleStrength.value,
            v =>
            {
                newMotionBlur.SampleStrength.value = v;
            },
            endValue,
            duration);
    }
}
