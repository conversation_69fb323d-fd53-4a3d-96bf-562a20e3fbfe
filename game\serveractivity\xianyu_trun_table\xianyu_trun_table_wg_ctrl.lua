require("game/serveractivity/xianyu_trun_table/xianyu_trun_table_wg_data")
require("game/serveractivity/xianyu_trun_table/xianyu_trun_table_view")
require("game/serveractivity/xianyu_trun_table/xianyu_trun_table_select_view")
require("game/serveractivity/xianyu_trun_table/xianyu_trun_table_get_reward_view")
XianyuTrunTableWGCtrl = XianyuTrunTableWGCtrl or BaseClass(BaseWGCtrl)

function XianyuTrunTableWGCtrl:__init()
	if XianyuTrunTableWGCtrl.Instance then
        error("[XianyuTrunTableWGCtrl]:Attempt to create singleton twice!")
	end
	XianyuTrunTableWGCtrl.Instance = self

	self.data = XianyuTrunTableWGData.New()
	self.view = XianyuTrunTableView.New(GuideModuleName.XianyuTrunTableView)
	self.select_view = XianyuTrunTableSelectView.New()
    self.get_reward_view = XianyuTrunTableGetRewardView.New()

	self:RegisterAllProtocals()

end

function XianyuTrunTableWGCtrl:__delete()
	self.view:DeleteMe()
	self.data:DeleteMe()
	self.select_view:DeleteMe()
	if self.get_reward_view then
		self.get_reward_view:DeleteMe()
		self.get_reward_view = nil
	end

	if self.alert then
		self.alert:DeleteMe()
		self.alert = nil
	end

	XianyuTrunTableWGCtrl.Instance = nil
end

function XianyuTrunTableWGCtrl:RegisterAllProtocals()
	-- -- 注册协议
	-- self:RegisterProtocol(SCRAHappyMondayInfo, "OnSCRAHappyMondayInfo")
	-- self:RegisterProtocol(SCRAHappyMondayDrawResult, "OnSCRAHappyMondayDrawResult")
	-- self:RegisterProtocol(SCRAHappyMondayServerRecord, "OnSCRAHappyMondayServerRecord")
	-- self:RegisterProtocol(SCRAHappyMondayPersonRecord, "OnSCRAHappyMondayPersonRecord")


	self:RegisterProtocol(SCCrossChannelActGoldZhuanpanPersonInfo, "OnSCCrossChannelActGoldZhuanpanPersonInfo")
	self:RegisterProtocol(SCCrossChannelActGoldZhuanpanWorldInfo, "OnSCCrossChannelActGoldZhuanpanWorldInfo")
	self:RegisterProtocol(SCCrossChannelActGoldZhuanpanJiangchi, "OnSCCrossChannelActGoldZhuanpanJiangchi")
	self:RegisterProtocol(SCCrossChannelActGoldZhuanpanResult, "OnSCCrossChannelActGoldZhuanpanResult")
end

function XianyuTrunTableWGCtrl:OnSCCrossChannelActGoldZhuanpanPersonInfo(protocol)
	self.data:SetSCCrossChannelActGoldZhuanpanPersonInfo(protocol)
	if self.view:IsOpen() then
		self.view:FlushPersonal()
	end
end

function XianyuTrunTableWGCtrl:OnSCCrossChannelActGoldZhuanpanWorldInfo(protocol)
	self.data:SetSCCrossChannelActGoldZhuanpanWorldInfo(protocol)
	if self.view:IsOpen() then
		self.view:FlushWorld()
	end
end

function XianyuTrunTableWGCtrl:OnSCCrossChannelActGoldZhuanpanJiangchi(protocol)
	self.data:SetSCCrossChannelActGoldZhuanpanJiangchi(protocol)
	if self.view:IsOpen() and self.view:IsLoaded() then
		self.view:FlushJingChiGlod()
	end
end

function XianyuTrunTableWGCtrl:OnSCCrossChannelActGoldZhuanpanResult(protocol)
	self.data:SetSCCrossChannelActGoldZhuanpanResult(protocol)
	XianyuTrunTableWGData.Instance:SetCurIsTurning(true)
	if self.view:IsOpen() then
		self.view:PlayRollAnimal()
	end
	local jump_anim = XianyuTrunTableWGData.Instance:GetJumpAni()
	if not jump_anim then
		local delay_time = 2
	    GlobalTimerQuest:AddDelayTimer(function ()
	        local data_list = self.data:CaleResultRewardList()
	        self:OpenRewardView(data_list)
	    end, delay_time)
	else
		local data_list = self.data:CaleResultRewardList()
	    self:OpenRewardView(data_list)
	end
end

-- RA_HAPPY_MONDAY_OP = { 						--周一运程操作类型
-- 	RA_HAPPY_MONDAY_OP_REQ_INFO = 1,					-- 请求信息
-- 	RA_HAPPY_MONDAY_OP_FETCH_DRAW_TIMES = 2,			-- 领取抽奖次数，参数1是任务类型
-- 	RA_HAPPY_MONDAY_OP_CHOOSE_REWARD = 3,				-- 选择奖励，param1-4 分别代码四个档位，其比特位为1则表示选择其中的奖励
-- 	RA_HAPPY_MONDAY_OP_DRAW = 4,						-- 进行一次抽奖
-- }

function XianyuTrunTableWGCtrl:SendYunChengRollReq(opera_type,param_1,param_2,param_3)
  	local param_t = {}
  	param_t.activity_type = ACTIVITY_TYPE.CROSS_CHANNEL_ACTIVITY_TYPE_GOLD_ZHUANPAN
  	param_t.opera_type = opera_type or 0
  	param_t.param_1 = param_1 or 0
  	param_t.param_2 = param_2 or 0
  	param_t.param_3 = param_3 or 0
  	ActivityWGCtrl.Instance:SendCSCrossChannelActivityRequest(param_t)
end

function XianyuTrunTableWGCtrl:OpenRewardSelectView()
	if self.select_view then
		self.data:CreateTempSelectList()
		self.select_view:Open()
	end
end


--奖励弹窗
function XianyuTrunTableWGCtrl:OpenRewardView(data)
    self.get_reward_view:SetData(data)
    self.get_reward_view:Open()
end

function XianyuTrunTableWGCtrl:OnSendRoll(draw_type)
	local other_cfg = XianyuTrunTableWGData.Instance:GetOtherCfg()
	local mode_cfg = XianyuTrunTableWGData.Instance:GetDrawModeCfg(draw_type)
	if IsEmptyTable(mode_cfg) then
		SysMsgWGCtrl.Instance:ErrorRemind("检查抽奖配置:" .. draw_type)
		return
	end
	local cost_gold = mode_cfg and mode_cfg.cost_gold or 0

	local func = function ()
		local is_enough = RoleWGData.Instance:GetIsEnoughUseGold(cost_gold)
		if is_enough then
			local op_type = CS_CROSS_CHANNEL_ACTIVITY_GOLD_ZHUANPAN_OP_TYPE.CS_CROSS_CHANNEL_ACTIVITY_GOLD_ZHUANPAN_OP_TYPE_BUY
			local param_1 = mode_cfg.draw_type
			XianyuTrunTableWGCtrl.Instance:SendYunChengRollReq(op_type,param_1)
		else
			UiInstanceMgr.Instance:ShowChongZhiView()
		end
	end

	if not self.alert then
        self.alert = Alert.New()
    end
    self.alert:ClearCheckHook()
    self.alert:SetShowCheckBox(true, "xianyu_trun_table")
    self.alert:SetCheckBoxDefaultSelect(false)
    self.alert:SetCheckBoxText(Language.XianyuTrunTable.DontTip)
    local str = string.format(Language.XianyuTrunTable.ChouJiangStr, cost_gold, mode_cfg.count)

    self.alert:SetLableString(str)
    self.alert:SetOkFunc(func)
    self.alert:Open()
end

function XianyuTrunTableWGCtrl:SetDelayShowGetGold(num)
	local delay_time = XianyuTrunTableWGData.Instance:GetDelayTime()
	TryDelayCall(self,function ()
		local item_cfg = ItemWGData.Instance:GetItemConfig(65534)
		local str = string.format(Language.Bag.GetItemTxt, item_cfg.name, num)
		SysMsgWGCtrl.Instance:ErrorRemind(str)
	end, delay_time, "xianyu_trun_table_delay_get_gold")
end