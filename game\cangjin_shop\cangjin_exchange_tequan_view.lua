function CangJinExchangeView:LoadIndexCallBackTeQuan()
	if not self.tequan_type_list then
		self.tequan_type_list = AsyncListView.New(CangJinExchangeTeQuanCell, self.node_list["tequan_type_list"])
		self.tequan_type_list:SetSelectCallBack(BindTool.Bind(self.OnTeQuan<PERSON><PERSON><PERSON><PERSON><PERSON>, self))
		self.tequan_type_list:SetStartZeroIndex(false)
	end

	if not self.tequan_model_display then
        self.tequan_model_display = OperationActRender.New(self.node_list["tequan_model_display"])
        self.tequan_model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	if not self.tequan_reward_list then
		self.tequan_reward_list = AsyncListView.New(ItemCell, self.node_list["tequan_reward_list"])
		self.tequan_reward_list:SetStartZeroIndex(true)
	end

	if not self.tequan_role_model then
		self.tequan_role_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["tequan_role_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = false,
		}

		self.tequan_role_model:SetRenderTexUI3DModel(display_data)
		-- local node = self.node_list.tequan_role_model
		-- self.tequan_role_model:SetUI3DModel(node.transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.tequan_role_model)
	end
	
	XUI.AddClickEventListener(self.node_list["tequan_reward_btn"], BindTool.Bind(self.OnClickTeQuanReward, self))
	XUI.AddClickEventListener(self.node_list["tequan_attr_choose_btn"], BindTool.Bind(self.OnClickTeQuanChooseAttr, self))

	self.select_tequan_data = nil
	self.select_tequan_type = nil
end

function CangJinExchangeView:ShowIndexCallBackTeQuan()

end

function CangJinExchangeView:ReleaseCallBackTeQuan()
	if self.tequan_type_list then
		self.tequan_type_list:DeleteMe()
		self.tequan_type_list = nil
	end

	if self.tequan_role_model then
		self.tequan_role_model:DeleteMe()
		self.tequan_role_model = nil
	end

	if self.tequan_model_display then
        self.tequan_model_display:DeleteMe()
        self.tequan_model_display = nil
	end

	if self.tequan_reward_list then
        self.tequan_reward_list:DeleteMe()
        self.tequan_reward_list = nil
    end

	self.background_loader = nil
	self.select_tequan_data = nil
	self.select_tequan_type = nil
end

function CangJinExchangeView:OnFlushTeQuan()
    local tequan_info = CangJinShopWGData.Instance:GetShowTeQuanList()
    if self.tequan_type_list then
        self.tequan_type_list:SetDataList(tequan_info)
    end
	
	if not self.select_tequan_type then
		local jump_to_index = 1
		for k, v in ipairs(tequan_info) do
			if v.remind then
				jump_to_index = k
				break
			end
		end
		self.tequan_type_list:JumpToIndex(jump_to_index, 3)
	else
		for k, v in ipairs(tequan_info) do
			if v.seq == self.select_tequan_type then
				self.select_tequan_data = v
				break
			end
		end
	end

	self:FlushLeftPanel()
	self:FlushRightPanel()
end


function CangJinExchangeView:OnTeQuanTypeHandler(item)
	if nil == item or nil == item.data then
		return
	end

	local data = item.data
	if self.select_tequan_type == data.seq then
		return
	end

	self.select_tequan_data = data
	self.select_tequan_type = data.seq
	self:FlushLeftPanel()
	self:FlushRightPanel()
end

function CangJinExchangeView:FlushRightPanel()
	if not self.select_tequan_data then
		return
	end

	local cfg = self.select_tequan_data.cfg
	self.node_list.tequan_desc.text.text = cfg.tequan_desc

	if self.select_tequan_data.act_flag == 0 then
		self.node_list.tequan_reward_btn_str.text.text = string.format(Language.CangJinShopView.TeQuanNeedAct, cfg.consume_score)
		self.tequan_reward_list:SetDataList(cfg.reward_item)
		XUI.SetButtonEnabled(self.node_list["tequan_reward_btn"],  true)
	elseif self.select_tequan_data.act_flag == 1 and cfg.is_can_get == 1 then
		self.tequan_reward_list:SetDataList(cfg.day_reward_item)
		local str = self.select_tequan_data.day_reward_flag == 0 and Language.CangJinShopView.DayCanGet or Language.CangJinShopView.IsGet
		self.node_list.tequan_reward_btn_str.text.text = str
		XUI.SetButtonEnabled(self.node_list["tequan_reward_btn"],  self.select_tequan_data.day_reward_flag == 0)
	else
		self.tequan_reward_list:SetDataList(cfg.reward_item)
		XUI.SetButtonEnabled(self.node_list["tequan_reward_btn"],  false)
		self.node_list.tequan_reward_btn_str.text.text =  Language.CangJinShopView.IsAct
	end

	self.node_list.tequan_reward_red:SetActive(self.select_tequan_data.reward_red)
	self.node_list.tequan_attr_choose_btn:SetActive(cfg.is_can_choose == 1)
	self.node_list.tequan_attr_red:SetActive(self.select_tequan_data.attr_red)
end

function CangJinExchangeView:FlushLeftPanel()
	if not self.select_tequan_data then
		return
	end

	--self:FlushBackground()
	self:FlushTeQuanRoleModel()
	self:FlushShowModel()
end

function CangJinExchangeView:FlushBackground()
	local cfg = self.select_tequan_data.cfg
	local background_id = cfg.back_id or 0
	local back_data = BackgroundWGData.Instance:GetBigDataByID(background_id)
	local asset, bundle = nil, nil
	if background_id ~= 0 and back_data then
		asset, bundle = ResPath.BackgroundShow(back_data.item_id)
	else
		asset, bundle = ResPath.BackgroundShow(background_id)
	end

	if not self.background_loader then
		local background_loader = AllocAsyncLoader(self, "base_tip_back_cell")
		background_loader:SetIsUseObjPool(true)
		background_loader:SetParent(self.node_list["tequan_background_root"].transform)
		self.background_loader = background_loader
	end

	self.background_loader:Load(asset, bundle)
end


function CangJinExchangeView:FlushTeQuanRoleModel()
	local cfg = self.select_tequan_data.cfg
	self.node_list.tequan_role_model:SetActive(cfg.show_role_model == 1)
	if cfg.show_role_model ~= 1 then
		return
	end

	if self.tequan_role_model then
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local ignore_table = {ignore_wing = true, ignore_jianzhen = true, ignore_halo = true, ignore_shouhuan = true, ignore_tail = true, ignore_waist = true}
		self.tequan_role_model:SetModelResInfo(role_vo, ignore_table)
		self.tequan_role_model:PlayRoleAction()
	end
end

function CangJinExchangeView:FlushShowModel()
	local cfg = self.select_tequan_data.cfg
    local display_data = {}
	display_data.should_ani = true
	if cfg.model_show_itemid ~= 0 and cfg.model_show_itemid ~= "" then
		local split_list = string.split(cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = cfg.model_show_itemid
		end
	end
	
	display_data.bundle_name = cfg["model_bundle_name"]
    display_data.asset_name = cfg["model_asset_name"]
    local model_show_type = tonumber(cfg["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1
    display_data.event_trigger_listener_node = self.node_list["EventTriggerListener"]

    self.tequan_model_display:SetData(display_data)
    local scale = cfg["display_scale"]
    Transform.SetLocalScaleXYZ(self.node_list["tequan_model_display"].transform, scale, scale, scale)
    local pos_x, pos_y = 0, 0
	if cfg.display_pos and cfg.display_pos ~= "" then
		local pos_list = string.split(cfg.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	RectTransform.SetAnchoredPositionXY(self.node_list.tequan_model_display.rect, pos_x, pos_y)

	if cfg.rotation and cfg.rotation ~= "" then
		local rotation_tab = string.split(cfg.rotation,"|")
		self.node_list["tequan_model_display"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	end
end

function CangJinExchangeView:OnClickTeQuanReward()
	if not self.select_tequan_data then
		return
	end

	local cfg = self.select_tequan_data.cfg
	if self.select_tequan_data.act_flag == 0 then
		CangJinShopWGCtrl.Instance:SendCangJinShopRequest(CANGJINSHANGPU_OPERA_TYPE.BUY_TEQUAN, cfg.seq)
	elseif self.select_tequan_data.act_flag == 1 and cfg.is_can_get == 1 and self.select_tequan_data.day_reward_flag == 0 then
		CangJinShopWGCtrl.Instance:SendCangJinShopRequest(CANGJINSHANGPU_OPERA_TYPE.FETCH_EVERY_DAY_REWARD, cfg.seq)
	end
end

function CangJinExchangeView:OnClickTeQuanChooseAttr()
	ViewManager.Instance:Open(GuideModuleName.CangJinExchangeAttr)
end
-------------------CangJinExchangeTeQuanCell---------------
CangJinExchangeTeQuanCell = CangJinExchangeTeQuanCell or BaseClass(BaseRender)

function CangJinExchangeTeQuanCell:LoadCallBack()

end

function CangJinExchangeTeQuanCell:__delete()

end

function CangJinExchangeTeQuanCell:OnFlush()
	if not self.data then
		return
	end

    local cfg = self.data.cfg
    self.node_list.nor_name.text.text = cfg.name or ""
    self.node_list.hl_name.text.text = cfg.name or ""
	self.node_list.desc_2.text.text = string.format(Language.CangJinShopView.JiFen, cfg.consume_score)
	self.node_list.remind:SetActive(self.data.remind)
end

function CangJinExchangeTeQuanCell:OnSelectChange(is_select)
	if not self.data then
		return
	end

	self.node_list.nor_img:SetActive(not is_select)
    self.node_list.hl_img:SetActive(is_select)
end
