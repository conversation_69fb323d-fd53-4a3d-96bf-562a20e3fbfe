UltimateBattlefieldGuessView = UltimateBattlefieldGuessView or BaseClass(SafeBaseView)

local ULTIATE_ROUND_TYPE = {
    ROUND_ONE = 0,
    ROUND_TWO = 1,
    ROUND_THREE = 2,
    ROUND_FOUR = 3,
    ROUND_FIVE = 4,
}

--0红1蓝
local ULTIATE_CAMP = {
    CAMP_RED = 0,
    CAMP_BLUE = 1,
}

function UltimateBattlefieldGuessView:__init()
    self:SetMaskBg(false, true)
    self.is_safe_area_adapter = true
    self:AddViewResource(0, "uis/view/country_map_ui/ultimate_battlefield_prefab", "layout_ultimate_guess")
end

function UltimateBattlefieldGuessView:LoadCallBack()
    self:InitGuessRender()
    self:InitButtonListener()
end

function UltimateBattlefieldGuessView:CloseCallBack()
	if Scene.Instance:GetSceneType() == SceneType.CROSS_ULTIMATE_BATTLE then
		local scene_logic = Scene.Instance:GetSceneLogic()
		if scene_logic then
			scene_logic:SetGuaji()
		end
	end
end

function UltimateBattlefieldGuessView:ReleaseCallBack()
    self.curr_stage = nil

    self:ReleaseGuessRender()
    self:CleanTimeDown()
end

-- 初始化Render
function UltimateBattlefieldGuessView:InitGuessRender()
    if not self.shower_1v31 then
        self.shower_1v31 = GuessDisplayRender.New(self.node_list.shower_1v31)
    end

    if not self.shower_2v30 then
        self.shower_2v30 = {}
        for i = 1, 2 do
            self.shower_2v30[i] = GuessDisplayRender.New(self.node_list[string.format("shower_2v30_%d", i)])
        end
    end

    if not self.shower_4v28 then
        self.shower_4v28 = {}
        for i = 1, 4 do
            self.shower_4v28[i] = GuessDisplayRender.New(self.node_list[string.format("shower_4v28_%d", i)])
        end
    end

    if not self.attack_status_8v24_grid then
        self.attack_status_8v24_grid = AsyncBaseGrid.New()
        self.attack_status_8v24_grid:SetStartZeroIndex(false)
        self.attack_status_8v24_grid:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list.attack_status_8v24_grid,
                assetBundle = "uis/view/country_map_ui/ultimate_battlefield_prefab", assetName = "layout_ultimate_guess_item", 
                itemRender = GuessHeadRender})
    end

    if not self.attack_status_16v16_grid then
        self.attack_status_16v16_grid = AsyncBaseGrid.New()
        self.attack_status_16v16_grid:SetStartZeroIndex(false)
        self.attack_status_16v16_grid:CreateCells({col = 4, change_cells_num = 1, list_view = self.node_list.attack_status_16v16_grid,
                assetBundle = "uis/view/country_map_ui/ultimate_battlefield_prefab", assetName = "layout_ultimate_guess_item", 
                itemRender = GuessHeadRender})
    end

    if not self.defense_status_16v16_grid then
        self.defense_status_16v16_grid = AsyncBaseGrid.New()
        self.defense_status_16v16_grid:SetStartZeroIndex(false)
        self.defense_status_16v16_grid:CreateCells({col = 4, change_cells_num = 1, list_view = self.node_list.defense_status_16v16_grid,
                assetBundle = "uis/view/country_map_ui/ultimate_battlefield_prefab", assetName = "layout_ultimate_guess_item", 
                itemRender = GuessHeadRender})
    end

    if not self.defense_status_other_grid then
        self.defense_status_other_grid = AsyncBaseGrid.New()
        self.defense_status_other_grid:SetStartZeroIndex(false)
        self.defense_status_other_grid:CreateCells({col = 3, change_cells_num = 1, list_view = self.node_list.defense_status_other_grid,
                assetBundle = "uis/view/country_map_ui/ultimate_battlefield_prefab", assetName = "layout_ultimate_guess_item", 
                itemRender = GuessHeadRender})
    end
end

-- 释放Render
function UltimateBattlefieldGuessView:ReleaseGuessRender()
    if self.shower_1v31 then
        self.shower_1v31:DeleteMe()
        self.shower_1v31 = nil
    end

    if self.shower_2v30 and #self.shower_2v30 > 0 then
		for _, cell in ipairs(self.shower_2v30) do
			cell:DeleteMe()
			cell = nil
		end

		self.shower_2v30 = nil
	end

    if self.shower_4v28 and #self.shower_4v28 > 0 then
		for _, cell in ipairs(self.shower_4v28) do
			cell:DeleteMe()
			cell = nil
		end

		self.shower_4v28 = nil
	end

    if self.attack_status_8v24_grid then
        self.attack_status_8v24_grid:DeleteMe()
        self.attack_status_8v24_grid = nil
    end

    if self.attack_status_16v16_grid then
        self.attack_status_16v16_grid:DeleteMe()
        self.attack_status_16v16_grid = nil
    end

    if self.defense_status_16v16_grid then
        self.defense_status_16v16_grid:DeleteMe()
        self.defense_status_16v16_grid = nil
    end

    if self.defense_status_other_grid then
        self.defense_status_other_grid:DeleteMe()
        self.defense_status_other_grid = nil
    end
end

-- 初始化按钮
function UltimateBattlefieldGuessView:InitButtonListener()
    XUI.AddClickEventListener(self.node_list.attack_guess_btn, BindTool.Bind2(self.AttackGuessClick, self))
    XUI.AddClickEventListener(self.node_list.defense_guess_btn, BindTool.Bind2(self.DefenseGuessClick, self))
end

--- 设置数据
function UltimateBattlefieldGuessView:SetCurrStage(stage)
    if stage == nil then
        return
    end

    self.curr_stage = stage
end


function UltimateBattlefieldGuessView:OnFlush()
    if self.curr_stage == nil then
        return
    end

    self:FlushStatus()
    self:RefreshTitleMessage()
    self:RefreshGuessRender()
    --刷新倒计时
	local scene_info = UltimateBattlefieldWGData.Instance:GetSceneInfo()
	if not scene_info then
		return
	end

    self:RefreshTimeCountDown(scene_info.stage_guess_end_time)
end

-- 刷新当前的展示节点
function UltimateBattlefieldGuessView:FlushStatus()
    self.node_list.attack_status_1v31:CustomSetActive(self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_FIVE)
    self.node_list.attack_status_2v30:CustomSetActive(self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_FOUR)
    self.node_list.attack_status_4v28:CustomSetActive(self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_THREE)
    self.node_list.attack_status_8v24:CustomSetActive(self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_TWO)
    self.node_list.attack_status_16v16:CustomSetActive(self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_ONE)

    self.node_list.defense_status_16v16:CustomSetActive(self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_ONE)
    self.node_list.defense_status_other:CustomSetActive(self.curr_stage ~= ULTIATE_ROUND_TYPE.ROUND_ONE)
end

-- 刷新头部信息
function UltimateBattlefieldGuessView:RefreshTitleMessage()
    local rank_info = UltimateBattlefieldWGData.Instance:GetScoreRankInfo()
    if not rank_info then
        return
    end

    self.node_list.title_txt.text.text = string.format(Language.UltimateBattlefield.RoundStr, NumberToChinaNumber(rank_info.stage + 1))
    local all_person_num = #rank_info.rank_item_list
    local red_guess_num = 0
    local blue_guess_num = 0
    local left_score = 0
    local right_score = 0

    for _, rank_item in ipairs(rank_info.rank_item_list) do
        if rank_item then    --- 检测是否竞猜
            if rank_item.stage_guess_flag then
                local is_guess = rank_item.stage_guess_flag[rank_info.stage] == 1
                if is_guess then
                    local is_red = rank_item.stage_guess_camp_flag[rank_info.stage] == 0
                    if is_red then
                        red_guess_num = red_guess_num + 1
                    else
                        blue_guess_num = blue_guess_num + 1
                    end
                end
            end

            if rank_item.camp == ULTIATE_CAMP.CAMP_RED then
                left_score = left_score + rank_item.score
            else
                right_score = right_score + rank_item.score
            end
        end
    end

    local left_ratio = red_guess_num / all_person_num
    self.node_list.left_team_number.text.text = self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_ONE and "" or tostring(left_score)
    self.node_list.left_team_ratio.text.text = string.format(Language.UltimateBattlefield.GuessRatio, string.format("%d%%", math.ceil(left_ratio * 100))) 
    self.node_list.left_team_ratio_slider.slider.value = left_ratio
    local right_ratio = blue_guess_num / all_person_num
    self.node_list.right_team_number.text.text = self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_ONE and "" or tostring(right_score)
    self.node_list.right_team_ratio.text.text = string.format(Language.UltimateBattlefield.GuessRatio, string.format("%d%%", math.floor(right_ratio * 100))) 
    self.node_list.right_team_ratio_slider.slider.value = right_ratio
    
    self.node_list.left_guessed:CustomSetActive(false)
    self.node_list.right_guessed:CustomSetActive(false)

    local my_rank_info = UltimateBattlefieldWGData.Instance:GetMyRankInfo()
    if my_rank_info then
        local is_guess = my_rank_info.stage_guess_flag[rank_info.stage] == 1
        self.node_list.attack_guess_btn:CustomSetActive(not is_guess)
        self.node_list.defense_guess_btn:CustomSetActive(not is_guess)

        if is_guess then
            local is_red = my_rank_info.stage_guess_camp_flag[rank_info.stage] == 0
            self.node_list.left_guessed:CustomSetActive(is_red)
            self.node_list.right_guessed:CustomSetActive(not is_red)
        end
    end
end

-- 刷新竞猜数据
function UltimateBattlefieldGuessView:RefreshGuessRender()
    if not self.curr_stage then
        return
    end

    local rank_info = UltimateBattlefieldWGData.Instance:GetScoreRankInfo()
    if not rank_info then
        return
    end

    local attack_data = {}
    local defense_data = {}

    for _, rank_item in ipairs(rank_info.rank_item_list) do
        if rank_item then    --- 检测是否竞猜
            if rank_item.camp == ULTIATE_CAMP.CAMP_RED then
                table.insert(attack_data, rank_item)
            else
                table.insert(defense_data, rank_item)
            end
        end
    end

    table.sort(attack_data, SortTools.KeyUpperSorter("score"))
    table.sort(defense_data, SortTools.KeyUpperSorter("score"))

    if self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_ONE then
        self:RefreshGuessTypeOne(attack_data, defense_data)
    elseif self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_TWO then
        self:RefreshGuessTypeTwo(attack_data, defense_data)
    elseif self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_THREE then
        self:RefreshGuessTypeThree(attack_data, defense_data)
    elseif self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_FOUR then
        self:RefreshGuessTypeFour(attack_data, defense_data)
    elseif self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_FIVE then
        self:RefreshGuessTypeFive(attack_data, defense_data)
    end
end

-- 刷新第一回合（16v16）
function UltimateBattlefieldGuessView:RefreshGuessTypeOne(attack_data, defense_data)
    self.attack_status_16v16_grid:SetDataList(attack_data)
    self.defense_status_16v16_grid:SetDataList(defense_data)
end

-- 刷新第二回合（8v24）
function UltimateBattlefieldGuessView:RefreshGuessTypeTwo(attack_data, defense_data)
    self.attack_status_8v24_grid:SetDataList(attack_data)
    self.defense_status_other_grid:SetDataList(defense_data)
end

-- 刷新第三回合（4v28）
function UltimateBattlefieldGuessView:RefreshGuessTypeThree(attack_data, defense_data)
    if self.shower_4v28 and attack_data then
        for i, shower_cell in ipairs(self.shower_4v28) do
            if shower_cell and attack_data[i] then
                shower_cell:SetData(attack_data[i])
            end
        end
    end

    self.defense_status_other_grid:SetDataList(defense_data)
end

-- 刷新第四回合（2v30）
function UltimateBattlefieldGuessView:RefreshGuessTypeFour(attack_data, defense_data)
    if self.shower_2v30 and attack_data then
        for i, shower_cell in ipairs(self.shower_2v30) do
            if shower_cell and attack_data[i] then
                shower_cell:SetData(attack_data[i])
            end
        end
    end

    self.defense_status_other_grid:SetDataList(defense_data)
end

-- 刷新第五回合（1v31）
function UltimateBattlefieldGuessView:RefreshGuessTypeFive(attack_data, defense_data)
    if attack_data and attack_data[1] then
        self.shower_1v31:SetData(attack_data[1])
    end
    self.defense_status_other_grid:SetDataList(defense_data)
end

------------------------------------------------------
-----------------活动时间倒计时-------------------
function UltimateBattlefieldGuessView:CleanTimeDown()
	if CountDownManager.Instance:HasCountDown("ultimate_battle_field_guess_down") then
		CountDownManager.Instance:RemoveCountDown("ultimate_battle_field_guess_down")
	end
end

function UltimateBattlefieldGuessView:RefreshTimeCountDown(end_time)
	local invalid_time = end_time
	if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
		self:CleanTimeDown()
        local str = ToColorStr(TimeUtil.FormatSecondDHM6(invalid_time - TimeWGCtrl.Instance:GetServerTime()), COLOR3B.GREEN)
		self.node_list["title_guess_time"].text.text = string.format(Language.UltimateBattlefield.GuessTimeTxt, str) 
		CountDownManager.Instance:AddCountDown("ultimate_battle_field_guess_down", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
	end
end

function UltimateBattlefieldGuessView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
        local str = ToColorStr(TimeUtil.FormatSecondDHM6(valid_time), COLOR3B.GREEN)
		self.node_list["title_guess_time"].text.text = string.format(Language.UltimateBattlefield.GuessTimeTxt, str) 
	end
end

function UltimateBattlefieldGuessView:OnComplete()
    self.node_list["title_guess_time"].text.text = ""
	self:Close()
end
------------------------------------------------------
-- 登神队竞猜按钮
function UltimateBattlefieldGuessView:AttackGuessClick()
    UltimateBattlefieldWGCtrl.Instance:RequestStageGuess(ULTIATE_CAMP.CAMP_RED)
end

-- 弑神队竞猜按钮
function UltimateBattlefieldGuessView:DefenseGuessClick()
    UltimateBattlefieldWGCtrl.Instance:RequestStageGuess(ULTIATE_CAMP.CAMP_BLUE)
end

---------------------竞猜人物对象-----------------------
GuessDisplayRender = GuessDisplayRender or BaseClass(BaseRender)
function GuessDisplayRender:LoadCallBack()
    if self.show_model == nil then
		self.show_model = RoleModel.New()
        local display_data = {
			parent_node = self.node_list["display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}

		self.show_model:SetRenderTexUI3DModel(display_data)
		-- self.show_model:SetUI3DModel(self.node_list.display.transform, self.node_list.display.event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
        self.is_show_model = false
	end

end

function GuessDisplayRender:__delete()
    if self.show_model then
        self.show_model:DeleteMe()
        self.show_model = nil
    end

    self.is_show_model = false
end

function GuessDisplayRender:OnFlush()
    if not self.data then return end

    -- 存在上阵，展示
    if self.show_model and (not self.is_show_model) then
        local ignore_table = {ignore_wing = false, ignore_jianzhen = true}
        self.show_model:SetModelResInfo(self.data, ignore_table)
        self.is_show_model = true
        self.show_model:PlayRoleAction(SceneObjAnimator.UiIdle)
    end

    local is_self = UltimateBattlefieldWGData.Instance:CheckIsSelf(self.data.uuid)
    local plat_type = self.data.usid.temp_high
    local server_id = self.data.usid.temp_low
    local name_str = string.format("[s%s]%s", server_id, self.data.name)
    local score_str = self.data.score == 0 and "" or self.data.score
    
    if is_self then
        name_str = ToColorStr(name_str, COLOR3B.GREEN)
        score_str = ToColorStr(score_str, COLOR3B.GREEN)
    end

    self.node_list.player_name.text.text = name_str
    self.node_list.player_score.text.text = score_str
end

---------------------竞猜头像对象-----------------------
GuessHeadRender = GuessHeadRender or BaseClass(BaseRender)
function GuessHeadRender:LoadCallBack()
    if self.head == nil then
		self.head = BaseHeadCell.New(self.node_list.player_head)
	end
end

function GuessHeadRender:__delete()
    if self.head then
        self.head:DeleteMe()
        self.head = nil
    end
end

function GuessHeadRender:OnFlush()
    if not self.data then return end

    local is_self = UltimateBattlefieldWGData.Instance:CheckIsSelf(self.data.uuid)
    local name_str = self.data.name
    local plat_type = self.data.usid.temp_high
    local server_id = self.data.usid.temp_low
    local usid_str = string.format("[s%s]", server_id)
    local score_str = self.data.score == 0 and "" or self.data.score

    local camp = self.data.camp or 0
    local bg_str = string.format("a2_zjzc_qz_%d", camp + 1)
    local bg_di_str = string.format("a2_zjzc_qzmc_%d", camp + 1)

    if is_self then
        bg_str = string.format("a2_zjzc_qz_%d", 3)
        bg_di_str = string.format("a2_zjzc_qzmc_%d", 3)

        name_str = ToColorStr(name_str, COLOR3B.GREEN)
        usid_str = ToColorStr(usid_str, COLOR3B.GREEN)
        score_str = ToColorStr(score_str, COLOR3B.GREEN)
    end

    self.node_list.player_bg.image:LoadSprite(ResPath.GetCountryUltimateImg(bg_str))
    self.node_list.player_name_di.image:LoadSprite(ResPath.GetCountryUltimateImg(bg_di_str))

	self.head:SetData({role_id = self.data.uuid.temp_low, prof = self.data.prof, sex = self.data.sex})
    self.node_list.player_name.text.text = name_str
    self.node_list.player_server.text.text = usid_str
    self.node_list.player_score.text.text = score_str
end
