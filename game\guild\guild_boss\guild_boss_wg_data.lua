GuildBossWGData = GuildBossWGData or BaseClass()

function GuildBossWGData:__init()
	if GuildBossWGData.Instance then
		ErrorLog("[GuildBossWGData]:Attempt to create singleton twice!")
	end
	GuildBossWGData.Instance = self

	local cfg = ConfigManager.Instance:GetAutoConfig("guild_boss_auto")
	self.other_cfg = cfg.other_cfg[1]
	self.drop_cfg = cfg.client_show

	self.guild_boss_reward_cfg = cfg.pass_reward
	--open_times:开启活动次数
	self.guild_boss_status = {
		open_times = 0,
		today_open_num = 0,
		boss_id = self.other_cfg.boss_id,
		uid = 0,
		finish_timestamp = 0,
		star_level = 3,
		is_pass = 0,
		pass_time = 0,
		guild_money = 0,
		rank_count = 0,
		active_is_open = 0,
	}
	self.out_boos_rank_list = {}
	self.cur_show_tips = 0

	self.guild_boss_scene_info = {
		cur_star_num = 0,
		boss_id = 0,
		is_finish = 0,
		is_pass = 0,
		next_star_timestamp = 0,
		finish_timestamp = 0,
		kick_out_timestamp = 0,
		start_timestamp = 0,
		dice_start_time = 0,
		dice_end_time = 0,
		dice_rounds = 0,
		max_dice_rounds = 0,
		dice_round_reward_item = {},
	}

	self.boos_rank_list = {
		user_id = 0,
		user_name = 0,
		hurt_val = 0,
		rank = 0,
		}
	self.my_update_rank = {}
	self.boss_hurt = {}
	self.after_ten_rank_reward_cfg = self:GetAfterTenRankCfg()
	self.guild_boss_reward_info = {}
    self.dice_record_list = {}		--仙盟Boss骰子记录

    self.max_role_info = {
        uid = 0,
        role_name = "",
        dice_num = 0
    }
end

function GuildBossWGData:__delete()
    GuildBossWGData.Instance = nil
    self.week_open_times = nil
end

function GuildBossWGData:SetGuildBossStatus(info)
	local old_finish_timestamp = self.guild_boss_status.finish_timestamp
	if old_finish_timestamp < info.finish_timestamp then
		self:ClearGuildBossSceneInfo()
	end
	self.guild_boss_status.open_times = info.open_times
	self.guild_boss_status.today_open_num = info.guild_boss_finish_flag  --今日是否完成击杀
	self.guild_boss_status.boss_id = info.boss_id
	self.guild_boss_status.uid = info.uid
	self.guild_boss_status.finish_timestamp = info.finish_timestamp --结束时间
	self.guild_boss_status.star_level = info.star_level
	self.guild_boss_status.is_pass = info.is_pass  --是否通过
	self.guild_boss_status.pass_time = info.pass_time --通关时间
	self.guild_boss_status.rank_count = info.rank_count --有效数量
	self.guild_boss_status.guild_money = info.guild_money
	self.out_boos_rank_list = info.rank_list
	self.after_ten_rank_reward_cfg = self:GetAfterTenRankCfg()
	self.guild_boss_status.active_is_open = info.active_is_open --活动是否可开启
end

function GuildBossWGData:GetGuildBossStatus()
	return self.guild_boss_status
end

function GuildBossWGData:GetGuildBossOpenTimes()
    if not self.week_open_times then
        local other_cfg = ConfigManager.Instance:GetAutoConfig("guild_boss_auto").other_cfg[1]
        self.week_open_times = other_cfg.weekly_open_times
    end
    return self.week_open_times
end

--排行榜
function GuildBossWGData:GetGuildBossOutRank()
	local data_list = {}
	--前十名排名数据
	for i,v in ipairs(self.out_boos_rank_list) do
		--取前十名 和 伤害大于0 的
		if i <= 10  then
			table.insert(data_list,v)
		end
	end
	--十名以后的数据
	for i, v in ipairs(self.after_ten_rank_reward_cfg) do
		local data = {}
		data.rank_str = string.format(Language.Guild.SHRankStr, v.rank_min, v.rank_max)
		table.insert(data_list, data)
	end
	return data_list
end


--获取个人排行信息
function GuildBossWGData:MyGuildBossRankInfo()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	for k,v in pairs(self.out_boos_rank_list) do
		if v.game_name == role_vo.name and v.uid == role_vo.role_id then
			return v
		end
	end
	return nil
end


--boss奖励格子数据
function GuildBossWGData:SetGuildBossRewardList(protocol)
	self.guild_boss_reward_info = protocol
end

function GuildBossWGData:GetGuildBossRewardList()
	local guild_boss_reward_info = self.guild_boss_reward_info
	local guild_boss_reward_cfg = self.guild_boss_reward_cfg
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if not IsEmptyTable(guild_boss_reward_info) then
		local star_num = guild_boss_reward_info.star_num
		for k,v in pairs(guild_boss_reward_cfg) do
			if cur_day >= v.opengame_day_min and cur_day <= v.opengame_day and star_num == v.star_num then
				return v.reward_item
			end
		end
	end
	return {}
end

function GuildBossWGData:GetBossRoleRankExp(rank, star_level)
	local rank_cfg = nil
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local cfg = ConfigManager.Instance:GetAutoConfig("guild_boss_auto").rank_reward_cfg
	if cfg then
		for i,v in ipairs(cfg) do
			if v.opengame_day_min <= cur_day and cur_day <= v.opengame_day and v.star_num == star_level and rank >= v.rank_min and rank <= v.rank_max then
				rank_cfg = v
				break
			end
		end
	end

	if rank_cfg then
		local level_cfg = ConfigManager.Instance:GetAutoConfig("role_level_reward_auto").level_reward_list
		if level_cfg then
			local level = RoleWGData.Instance:GetRoleLevel()
			if level_cfg[level] then
				return  rank_cfg.exp_fenshu * level_cfg[level].kill_monster_exp
			end
		end
	end

	return 0
end

function GuildBossWGData:SetCurShowTips(index)
	self.cur_show_tips = index
end

function GuildBossWGData:CurShowTips()
	return self.cur_show_tips
end

function GuildBossWGData:IsGuildBossOpen()
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	local activity_cfg = DailyWGData.Instance:GetActivityConfig(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUILD_BOSS)
	if role_level < activity_cfg.limit_level or role_level >= activity_cfg.level_max then
		return false
	end
	if self.guild_boss_status then
		local is_finish = self.guild_boss_scene_info.is_finish or 0
		return self.guild_boss_status.finish_timestamp > TimeWGCtrl.Instance:GetServerTime() and is_finish == 0
	end
	return false
end

--设置主界面显示图标
function GuildBossWGData:SetGuildBossIcon(info)
    local act_state = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUILD_BOSS)
	if act_state ~= self:IsGuildBossOpen() then
		local status = self:IsGuildBossOpen() and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUILD_BOSS, status, info.finish_timestamp, 0, 0, 1)
        if status == ACTIVITY_STATUS.OPEN then
            ActivityWGCtrl.Instance:OpenActivityNoticeView(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUILD_BOSS)
        end
    end
end

function GuildBossWGData:SetGuildBossSceneInfo(info)
	self.guild_boss_scene_info.cur_star_num = info.cur_star_num
	self.guild_boss_scene_info.boss_id = info.boss_id
	self.guild_boss_scene_info.is_finish = info.is_finish
	self.guild_boss_scene_info.is_pass = info.is_pass
	self.guild_boss_scene_info.next_boss_refresh_timestamp = info.next_boss_refresh_timestamp
	self.guild_boss_scene_info.next_star_timestamp = info.next_star_timestamp
	self.guild_boss_scene_info.finish_timestamp = info.finish_timestamp
	self.guild_boss_scene_info.kick_out_timestamp = info.kick_out_timestamp
	self.guild_boss_scene_info.start_timestamp = info.start_timestamp

	self.guild_boss_scene_info.dice_start_time = info.dice_start_time
	self.guild_boss_scene_info.dice_end_time = info.dice_end_time
	self.guild_boss_scene_info.dice_rounds = info.dice_rounds
	self.guild_boss_scene_info.max_dice_rounds = info.max_dice_rounds
	self.guild_boss_scene_info.dice_round_reward_item = info.dice_round_reward_item
end

function GuildBossWGData:GetGuildBossOutTime()
	return self.guild_boss_scene_info.finish_timestamp
end

function GuildBossWGData:ClearGuildBossSceneInfo()
	self.guild_boss_scene_info = {
		cur_star_num = 0,
		boss_id = 0,
		is_finish = 0,
		is_pass = 0,
		next_star_timestamp = 0,
		finish_timestamp = 0,
		kick_out_timestamp = 0,
		start_timestamp = 0,
		dice_start_time = 0,
		dice_end_time = 0,
		dice_rounds = 0,
		max_dice_rounds = 0,
		dice_round_reward_item = {},
    }
    GuildWGData.Instance:SetHideGuildBossRemind(false)
	self.dice_record_shaizi_num_list = {}
	self:SetMaxDiceRecordEnd(false)
	self:ClearMaxDiceData()
end

function GuildBossWGData:GetGuildBossSceneInfo()
	return self.guild_boss_scene_info
end

function GuildBossWGData:GetGuildBossRewards()
	local drop_cfg = self.drop_cfg
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if drop_cfg then
		for k,v in pairs(drop_cfg) do
			if cur_day >= v.opengame_day_min and cur_day <= v.opengame_day_max and v.star == 3 then
				return v
			end
		end
	end
	return nil
end

--根据物品Id去拿奖励预览界面物品星级
function GuildBossWGData:GetGuildBossRewardStar()
	local reward_cfg = self:GetGuildBossRewards()
	if reward_cfg then
		return reward_cfg.star or 1
	end
	return 1
end

--个人
function GuildBossWGData:SetMyGuildBossPersonRankInfo(info)
	self.my_update_rank.rank = info.rank	--个人排名
	self.my_update_rank.hurt_val = info.hurt_val --个人伤害
end

function GuildBossWGData:GetMyGuildBossPersonRankInfo()
	return self.my_update_rank
end

--全体排行
function GuildBossWGData:SetGuildBossPersonRankInfo(info)
	self.boos_rank_list = info.rank_info_list

	if self.boos_rank_list[1] then
		GlobalEventSystem:Fire(MainUIEventType.SHOW_ASCRIPTION,self.boos_rank_list[1].user_name or "",Language.Common.AscriptionTitle2)
	end
end

function GuildBossWGData:GetGuildBossRankListInfo()
	return self.boos_rank_list
end

--Boos伤害提醒
function GuildBossWGData:SetGuildBossHurtWarnInfo(info)
	self.boss_hurt.boss_id = info.boss_id
	self.boss_hurt.boss_state = info.boss_state

	if info.boss_state == 1 then
		GuajiWGCtrl.Instance:StopGuaji(false, true)
		GuajiCache.target_obj = Scene.Instance:SelectMinDisMonster(GuildBossWGData.Instance:GetMonsterId())
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end
end

--仙盟boss是否处于无敌状态
function GuildBossWGData:IsBossWuDi()
	if self.boss_hurt then
		return self.boss_hurt.boss_state or false
	end

	return false
end

--获取生还小怪id
function GuildBossWGData:GetMonsterId()
	if self.boss_hurt then
		local monster_cfg = ConfigManager.Instance:GetAutoConfig("guild_boss_auto").xiaoguai
		if monster_cfg then
			for i,v in ipairs(monster_cfg) do
				if v.bossid == self.boss_hurt.boss_id then
					local arr = Split(v.monsterid, "|")
					return arr and tonumber(arr[1]) or 0
				end
			end
		end
	end
	return 0
end

function GuildBossWGData:GetGuildBossHurtWarnInfo()
	return self.boss_hurt
end

--前10配置所有
function GuildBossWGData:GetRankRewardCfg(index, star_num)
	local fb_status_cfg = self:GetGuildBossStatus()
	local boss_id = fb_status_cfg.boss_id
	local is_pass = fb_status_cfg.is_pass == 1
	local count = fb_status_cfg.rank_count
	--当前天数
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local star_level
	if count ~= 0 then
		if is_pass then
			star_level = fb_status_cfg.star_level
		else
			star_level = 0
		end
	else
		star_level = 3
	end
	if star_num then
		star_level = star_num
	end
    local real_cfg = ConfigManager.Instance:GetAutoConfig("guild_boss_auto").rank_reward_cfg
	-- local reward_cfg = {}
	if real_cfg then
		for i, v in pairs(real_cfg) do
			if cur_day >= v.opengame_day_min and cur_day <= v.opengame_day and star_level == v.star_num and index <= v.rank_max and index >= v.rank_min then
				-- table.insert(reward_cfg, v)
				return v
			end
		end
	end
	return nil

end

--根据前十个人排名获取物品奖励(指定Index)
function GuildBossWGData:GetPersonItemRewardByRank(index, star_num)
	local get_cfg = self:GetRankRewardCfg(index, star_num)
	local data_list = {}
	if get_cfg then
		for k,v in pairs(get_cfg.item) do

			table.insert(data_list, v)
		end
	end
	return data_list
end

--10名以后奖励配置(所有)
function GuildBossWGData:GetAfterTenRankCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("guild_boss_auto").rank_reward_cfg
	local fb_status_cfg = self:GetGuildBossStatus()
	-- local boss_id = fb_status_cfg.boss_id
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local is_pass = fb_status_cfg.is_pass == 1
	local count = fb_status_cfg.rank_count
	local star_level
	if count ~= 0 then
		if is_pass then
			star_level = fb_status_cfg.star_level
		else
			star_level = -1
		end
	else
		star_level = 3
	end
	local get_cfg = {}
	for k,v in pairs(cfg) do
		if v.rank_min >= 10 and open_day == v.opengame_day and star_level == v.star_num then
			table.insert(get_cfg,v)
		end
	end
	return get_cfg
end

--获取第10名以后的个人排行奖励配置(Index)
function GuildBossWGData:GetAfterTenRankItemRewardList(index)
	local cfg_index = index - 10
	local cfg = self.after_ten_rank_reward_cfg[cfg_index]
	if cfg then
		local data_list = {}
		for i,v in pairs(cfg.item) do
			table.insert(data_list, v)
		end
		return data_list
	end
	return {}
end

--获取个人排行默认列表（没有任何排名，没有数据时使用）
function GuildBossWGData:GetDefaultPersonRankDataList()
	local data_list = {}
	for i = 1, 10 do
		local data = {}
		data.total_hurt = Language.Guild.SHJingQingQiDai
		data.name = Language.Guild.SHJingQingQiDai
		data.is_fake_data = true
		table.insert(data_list, data)
	end
	return data_list
end


--根据bossId获取boss信息
function GuildBossWGData:GetBossCfg()
	local boss_id = self.guild_boss_status.boss_id
	local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[boss_id]
	return monster_cfg
end

--boss提示血量
function GuildBossWGData:GetBossHpTipsCfg()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("guild_boss_auto").other_cfg[1]
	local tips1 = other_cfg.blood_tips_1
	local tips2 = other_cfg.blood_tips_2
	local tips3 = other_cfg.blood_tips_3
	return tips1,tips2,tips3
end

--boss开启消耗(传进来一个本周开启boss次数)
function GuildBossWGData:GetBossOpenNeedMoneyNum(open_num)
    local week_times = GuildBossWGData.Instance:GetGuildBossOpenTimes()
	if open_num < 1 then open_num = 1 end
	if open_num > week_times then open_num = week_times end
	local open_cost = ConfigManager.Instance:GetAutoConfig("guild_boss_auto").open_cost
	for k,v in pairs(open_cost) do
		if v.num == open_num then
			return v.guildmoney_cost
		end
	end
	return 0

end

function GuildBossWGData:GetOtherCfg()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("guild_boss_auto").other_cfg[1]
	return other_cfg
end

function GuildBossWGData:SetSCGuildBossDiceRecord(protocol)
	self.dice_record_list = protocol.dice_record_list
end

function GuildBossWGData:SetSCGuildBossDiceRecordUpdate(protocol)
	local dice_record = protocol.dice_record
	if dice_record then
		self.dice_record_list[dice_record.round] = dice_record
	end
end

function GuildBossWGData:GetDiceRecordList()
	return self.dice_record_list
end

--摇色子是否已结束
function GuildBossWGData:SetMaxDiceRecordEnd(flag)
	self.max_dice_round_end = flag
end

--摇色子是否已结束
function GuildBossWGData:GetMaxDiceRecordEnd()
	return self.max_dice_round_end
end

function GuildBossWGData:GetGuildBossIsPass()
	local is_pass = self.guild_boss_scene_info.is_pass == 1
	local scene_type = Scene.Instance:GetSceneType()
	if is_pass and scene_type == SceneType.GuildBoss then
		return true
	end
	return false
end

function GuildBossWGData:GetShowTipsFlag()
	return self.show_tips_flag
end

function GuildBossWGData:SetShowTipsFlag(flag)
	self.show_tips_flag = flag
end

function GuildBossWGData:SetMaxDiceData(protocol)
    self.max_role_info.uid = protocol.uid
    self.max_role_info.role_name = protocol.role_name
    self.max_role_info.dice_num = protocol.dice_num
end

function GuildBossWGData:GetMaxDiceData()
	return self.max_role_info
end

function GuildBossWGData:ClearMaxDiceData()
	self.max_role_info = {}
end

--是否是新的一轮
function GuildBossWGData:SetDiceRoundShaiZiNum(shaizi_num)
	local scene_info = GuildBossWGData.Instance:GetGuildBossSceneInfo()
	local dice_num = scene_info.dice_rounds or 1
	if self.dice_record_shaizi_num_list == nil then
		self.dice_record_shaizi_num_list = {}
	end
	self.dice_record_shaizi_num_list[dice_num] = shaizi_num
end

function GuildBossWGData:GetDiceRoundShaiZiNum()
	local scene_info = GuildBossWGData.Instance:GetGuildBossSceneInfo()
	local dice_num = scene_info.dice_rounds or 1
	return self.dice_record_shaizi_num_list and self.dice_record_shaizi_num_list[dice_num]
end
