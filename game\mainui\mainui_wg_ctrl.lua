require("game/mainui/mainui_wg_data")
require("game/mainui/mainui_view")
require("game/mainui/mainui_view_task")
require("game/mainui/mainui_view_chat")
require("game/mainui/mainui_view_skill")
require("game/mainui/mainui_base_skill_render")
require("game/mainui/mianui_common_skill_render")
require("game/mainui/mainui_bianshen_render")
require("game/mainui/mainui_sixiang_skill_render")
require("game/mainui/mainui_view_player")
require("game/mainui/mainui_view_bottom")
require("game/mainui/mainui_view_target")
require("game/mainui/mainui_view_activity")
require("game/mainui/mainui_view_activity_btn_aggregation")
require("game/mainui/mainui_view_joystick")
require("game/mainui/mainui_view_attackmode")				--攻击模式界面
require("game/mainui/mainui_view_map")
require("game/mainui/mainui_view_exp")
require("game/mainui/mainui_view_power_change")
require("game/mainui/mainui_view_room") 					--房间信息
require("game/mainui/mainui_strong_menu_view")
require("game/mainui/mainui_strong_get_reward_view")
require("game/mainui/mainui_function_trailer")
-- require("game/mainui/select_camera_mode_view")
require("game/mainui/gurad_invalid_time_view")
require("game/mainui/guard_overdue_view")
require("game/mainui/mainui_guard_guide")
require("game/mainui/mainui_beatk_icon")
require("game/mainui/target_buff_tip")
require("game/mainui/camera_bubble_view")
require("game/mainui/mianui_hide_mode_view")
require("game/mainui/mainui_revenge_panel")
require("game/mainui/mainui_long_zhu_skill")
require("game/mainui/mainui_beasts_skill")
require("game/mainui/mainui_multi_skill_select")
require("game/mainui/mainui_fp_advance_notice")
require("game/mainui/illegality_notcie_view")
require("game/mainui/mainui_func_box_view")

require("game/mainui/mainui_fhd_view")
require("game/mainui/mainui_hd_view")

-- 新增任务小窗口界面
require("game/mainui/main_task_panel/mainui_cross_lingzhu_task_panel")

-- 登录
MainuiWGCtrl = MainuiWGCtrl or BaseClass(BaseWGCtrl)

function MainuiWGCtrl:__init()
	if MainuiWGCtrl.Instance ~= nil then
		print_error("[MainuiWGCtrl] attempt to create singleton twice!")
		return
	end
	MainuiWGCtrl.Instance = self

	self.view = MainUIView.New(GuideModuleName.MainUIView)
	self.attack_mode_view = ActtackModeView.New(GuideModuleName.AttackMode)
	self.data = MainuiWGData.New()
	self.camera_bubble_view = CameraBubbleView.New(GuideModuleName.CameraBubbleView)
	self.gather_bar = GatherBar.New()			-- 采集进度条

	-- self.select_camera_mode_view = SelectCameraModeView.New(GuideModuleName.SelectCameraModeView)
	self.gurad_invalid_time_view = GuradInvalidTimeView.New(GuideModuleName.GuradInvalidTimeView)
	self.guard_overdue_view = GuardOverdueView.New()
	self.target_buff_tip = TargetBuffTip.New()
	self.mian_ui_hide_mode_view = MainUiHideModeView.New()
	self.multi_skill_select_view = MainUiMultiSkillSelectView.New(GuideModuleName.MainUiMultiSkillSelectView)
	self.illegality_notice_view = IllegalityNoticeView.New(GuideModuleName.IllegalityNoticeView)

	self.mode = 0
	self:RegisterAllProtocols()
	self:RegisterAllEvents()
	self.chat_id = 0

	self.normal_mount_flag = true 		--正常状态坐骑按钮标记
	self.special_mount_flag = nil 		--变身状态坐骑按钮标记
	-- self.is_add_jishiqi = true --是否需要添加计时器
	self.is_can_show_guildeffect = false  --是否显示特效
	self.rember_time = 0 --记录上次提醒特效时间
	self.xioagui_flag = 0

	self:BindGlobalEvent(SceneEventType.SCENE_LOADING_STATE_ENTER, BindTool.Bind(self.OnSceneLoadingEnter, self))
	self:BindGlobalEvent(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind(self.OnSceneLoadingEnd, self))
	self:BindGlobalEvent(MainUIEventType.MAIN_TOP_ARROW_CLICK,BindTool.Bind(self.OnTopArrowChange,self))
	self.open_fun_change = GlobalEventSystem:Bind(OpenFunEventType.OPEN_TRIGGER, BindTool.Bind(self.OpenFunEventChange, self))

	self.main_ui_hd_view = MainUIHDView.New("MainUIHDView")
	self.main_ui_fhd_view = MainUIFHDView.New("MainUIFHDView")

	self.shouchong_anim_show_state = true 	--首充新服倒计时 动画可显示状态
end

function MainuiWGCtrl:__delete()
	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.main_ui_hd_view then
		self.main_ui_hd_view:DeleteMe()
		self.main_ui_hd_view = nil
	end

	if self.main_ui_fhd_view then
		self.main_ui_fhd_view:DeleteMe()
		self.main_ui_fhd_view = nil
	end

	if self.attack_mode_view then
		self.attack_mode_view:DeleteMe()
		self.attack_mode_view = nil
	end

	if self.camera_bubble_view then
		self.camera_bubble_view:DeleteMe()
		self.camera_bubble_view = nil
	end

	if self.line_view then
		self.line_view:DeleteMe()
		self.line_view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.activity_hall then
		self.activity_hall:DeleteMe()
		self.activity_hall = nil
	end

	if self.activity_data then
		self.activity_data:DeleteMe()
		self.activity_data = nil
	end

	if self.activity_name then
		self.activity_name:DeleteMe()
		self.activity_name = nil
	end

	if self.collectgarbage_view then
		self.collectgarbage_view:DeleteMe()
		self.collectgarbage_view = nil
	end

	if self.gather_bar then
		self.gather_bar:DeleteMe()
		self.gather_bar = nil
	end

	-- self.select_camera_mode_view:DeleteMe()
    -- self.select_camera_mode_view = nil

	self.gurad_invalid_time_view:DeleteMe()
    self.gurad_invalid_time_view = nil

	self.guard_overdue_view:DeleteMe()
    self.guard_overdue_view = nil

	if self.illegality_notice_view then
		self.illegality_notice_view:DeleteMe()
		self.illegality_notice_view = nil
	end

	self.target_buff_tip:DeleteMe()
    self.target_buff_tip = nil

	if self.count_down ~= nil then
		CountDown.Instance:RemoveCountDown(self.count_down)
		self.count_down = nil
	end
	self.special_mount_flag = nil 		--变身状态坐骑按钮标记
	MainuiWGCtrl.Instance = nil
	-- self.is_add_jishiqi = nil
	self.is_can_show_guildeffect = nil
	self.rember_time = nil
	-- if self.flush_effect_bind then
	-- 	GlobalTimerQuest:CancelQuest(self.flush_effect_bind)
	-- 	self.flush_effect_bind = nil
	-- end

	if self.mian_ui_hide_mode_view then
		self.mian_ui_hide_mode_view:DeleteMe()
		self.mian_ui_hide_mode_view = nil
	end

	if self.multi_skill_select_view ~= nil then
		self.multi_skill_select_view:DeleteMe()
		self.multi_skill_select_view = nil
	end

	if self.delay_power_timer then
		GlobalTimerQuest:CancelQuest(self.delay_power_timer)
		self.delay_power_timer = nil
	end

	if self.open_timer then
		GlobalTimerQuest:CancelQuest(self.open_timer)
		self.open_timer = nil
	end

	if self.open_fun_change then
		GlobalEventSystem:UnBind(self.open_fun_change)
	end

	self.xioagui_flag = nil
	self.shouchong_anim_show_state = nil
	self:CleanMarkerTimer()
	self:CleanBianQiangBubbleTipsTimer()
end

function MainuiWGCtrl:CreateMainCollectgarbageText()
	if self.collectgarbage_view == nil then
		self.collectgarbage_view = MainCollectgarbageText.New()
		self.collectgarbage_view:Open()
	else
		self.collectgarbage_view:Close()
		self.collectgarbage_view:DeleteMe()
		self.collectgarbage_view = nil
	end
end

function MainuiWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCSetAttackMode, "OnSetAttackMode")
	self:RegisterProtocol(CSSetAttackMode)

	-- self:RegisterProtocol(SCModulePreviewRewardInfo, "OnModulePreviewRewardInfo")
	self:RegisterProtocol(CSModulePreviewReq)

	-- self:RegisterProtocol(SCKillMonsterDropGoldBindInfo, "OnKillMonsterDropGoldBindInfo")   -- 6945
	self:RegisterProtocol(SCIllegalityNotcie, "OnSCIllegalityNotcie")
	self:RegisterProtocol(SCRoleOtherRouteInfo, "OnSCRoleOtherRouteInfo")
end

function MainuiWGCtrl:Open()
	self.view:Open()
end

function MainuiWGCtrl:GetView()
	return self.view
end

function MainuiWGCtrl:OpenGuradInvalidTimeView(data)
	if FunctionGuide.Instance:GetIsGuide() then
		return
	end
	self.gurad_invalid_time_view:Open()
end

function MainuiWGCtrl:CheckGuradInvalidFlush()
	if self.gurad_invalid_time_view:IsOpen() then
		self.gurad_invalid_time_view:Flush()
	end
end

function MainuiWGCtrl:CheckLoginFirstOpenView(data)
	if self.open_timer then
		GlobalTimerQuest:CancelQuest(self.open_timer)
		self.open_timer = nil
	end
	self.open_timer = GlobalTimerQuest:AddDelayTimer(function ()
		XiaoGuiWGData.Instance:CheckLoginFirstOpenView()
	end, 2)
end

function MainuiWGCtrl:OpenGuardOverdueView(data,str)
	self.guard_overdue_view:SetDataOpen(data,str)
end

function MainuiWGCtrl:OpenOpenTargetBuffTip(node, data)
	self.target_buff_tip:SetDataOpen(node, data)
end

function MainuiWGCtrl:CloseTargetBuffTip()
	if self.target_buff_tip:IsOpen() then
		self.target_buff_tip:Close()
	end
end

function MainuiWGCtrl:CloseMainUIFuncBoxView()
	self.view:CloseMainUIFuncBoxView()
end

--判断主界面是否初始化完成
function MainuiWGCtrl:IsLoadMainUiView()
	return self.view.is_mainui_view_load
end

function MainuiWGCtrl:GetData()
	return self.data
end

function MainuiWGCtrl:GetTaskView()
	return self.view.task_view
end

function MainuiWGCtrl:DoTask(task_id, task_status, fly_shoes)
	if task_id and task_status then
		self.view:DoTask(task_id, task_status, fly_shoes)
	end
end

function MainuiWGCtrl:GetMenuToggleState()
	return self.view:GetMenuToggleState()
end

function MainuiWGCtrl:GetFightToggleState()
	return self.view:GetFightToggleState()
end

function MainuiWGCtrl:SetShowShield(is_show)
	self.view:SetShowShield(is_show)
end

function MainuiWGCtrl:ChangeBottomOnOffBtnState()
	self:AddInitCallBack(nil, function()
		self.view:ChangeBottomOnOffBtnState()
	end)
end

function MainuiWGCtrl:RegisterAllEvents()
	self:BindGlobalEvent(MainUIEventType.ROLE_SKILL_LIST, BindTool.Bind1(self.UpdateSkillList, self))
	self:BindGlobalEvent(MainUIEventType.ROLE_BEASTS_SKILL_LIST, BindTool.Bind1(self.UpdateBeastsSkillList, self))
	self:BindGlobalEvent(SkillEventType.FLUSH_SKILL_LIST,BindTool.Bind1(self.FlushSkillList, self))
	self:BindGlobalEvent(SettingEventType.FIGHT_MOUNT_SKILL_CG_PLAY,
		BindTool.Bind(self.SettingChange, self, SETTING_TYPE.FIGHT_MOUNT_SKILL_CG_PLAY))
	-- self:BindGlobalEvent(ObjectEventType.BE_SELECT, BindTool.Bind(self.OnClickObj, self))
end

function MainuiWGCtrl:OpenAttackModeView()
	self.attack_mode_view:Open()
end

function MainuiWGCtrl:OnSetAttackMode(protocol)
	local check_fanji_role = nil
	if protocol.is_fanji == 1 then
		check_fanji_role = RevengeWGData.Instance:GetWantRevengeRole()
	end

	if protocol.result == GameEnum.SET_ATTACK_MODE_SUCC then-- 成功
		local mode = protocol.attack_mode
		local main_role = Scene.Instance:GetMainRole()
		local scene_type = Scene.Instance:GetSceneType()

		local is_reset_name = false
		local obj_id = protocol.obj_id
		if obj_id == main_role:GetObjId() then -- 自己的攻击模式改变
			self.mode = mode
			self.view:SetAttackMode(mode)
			main_role:SetAttackMode(mode)
			is_reset_name = true
		end
		--TODO 其他人攻击模式改变,改变vo即可

		self:OnFlushUINameColor(is_reset_name, obj_id)

		if check_fanji_role ~= nil then
			self:TryRevengeRole(check_fanji_role)
		end

		GlobalEventSystem:Fire(SettingEventType.Change_Attack_Mode)
	elseif protocol.result == GameEnum.SET_ATTACK_MODE_SCENE_LIMIT then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.WorldServer.ChangeAttackTip)
	else
		local str = Language.Mainui.AttackMode[protocol.result]
		SysMsgWGCtrl.Instance:ErrorRemind(str)
	end
end

function MainuiWGCtrl:OnFlushUINameColor(is_reset_name, obj_id)
	local role_list = Scene.Instance:GetRoleList()
	for k,v in pairs(role_list) do
		v:UpdateHpVisible()
		if is_reset_name then
			v:ReloadUINameColor()
		elseif not v:IsDeleted() and v:GetObjId() == obj_id then
			v:ReloadUINameColor()
		end
	end
end
function MainuiWGCtrl:OnTaskRefreshActiveCellViews()
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:OnTaskRefreshActiveCellViews()
		end
	end)
end

-- 上方血条位置x
function MainuiWGCtrl:SetTargetViewShakeRootPositionX(x)
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:SetTargetViewShakeRootPositionX(x)
		end
	end)
end

-- 重置血条位置x
function MainuiWGCtrl:ResetTargetViewShakeRootPosition()
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:ResetTargetViewShakeRootPosition()
		end
	end)
end

-- 上方血条位置y
function MainuiWGCtrl:SetTargetViewShakeRootPositionY(y)
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:SetTargetViewShakeRootPositionY(y)
		end
	end)
end

-- 展示变身全屏特效
function MainuiWGCtrl:ShowTianShenScreenEffect(is_visible)
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:ShowTianShenScreenEffect(is_visible)
		end
	end)
end




-- 展示御龙全屏特效
function MainuiWGCtrl:ShowYuLongScreenEffect()
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:ShowYuLongScreenEffect()
		end
	end)
end

function MainuiWGCtrl:ShowMechaScreenEffect()
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:ShowMechaScreenEffect()
		end
	end)
end

function MainuiWGCtrl:UpdateSkillList()
	self:AddInitCallBack(nil, function()
		self.view:UpdateSkillList()
	end)
end

function MainuiWGCtrl:UpdateBeastsSkillList()
	self:AddInitCallBack(nil, function()
		self.view:UpdateBeastsSkillList()
	end)
end

function MainuiWGCtrl:FlushSkillList(is_force)
	self:AddInitCallBack(nil, function()
		self.view:FlushSkillList(is_force)
	end)
end


function MainuiWGCtrl:SettingChange(setting_type, switch)
	self:AddInitCallBack(nil, function()
		self.view:SettingChange(setting_type, switch)
	end)
end

function MainuiWGCtrl:AddNewSkill(skill_index)
	return self.view:AddNewSkill(skill_index)
end

function MainuiWGCtrl:GetAttckMode()
	return self.mode
end

function MainuiWGCtrl:SetButtonModeClick(enable)
	self.view:SetButtonModeClick(enable)
end

function MainuiWGCtrl:SendSetAttackMode(mode, is_fanji)
	local protocol = ProtocolPool.Instance:GetProtocol(CSSetAttackMode)
	protocol.mode = mode or 0
	protocol.is_fanji = is_fanji or 0
	protocol:EncodeAndSend()
end

function MainuiWGCtrl:OnClickObj(obj)
	self:SetTargetObj(obj)
end

function MainuiWGCtrl:SetTargetObj(target_obj)
	-- self.view:SetTargetObj(target_obj)

	local old_obj = MainuiWGData.Instance:GetTargetObj()
	if nil ~= old_obj and target_obj ~= old_obj then
		-- old_obj:CancelSelect()
	end
	MainuiWGData.Instance:SetTargetObj(target_obj)
end

function MainuiWGCtrl:GetSkillButtonPosition()
	return self.view:GetSkillButtonPosition()
end

function MainuiWGCtrl:GetBeastSkillButtonPosition()
	return self.view:GetBeastSkillButtonPosition()
end

function MainuiWGCtrl:ChangeRedPoint(index, state)
	self.view:ChangeRedPoint(index, state)
end

function MainuiWGCtrl:ShowHuSongButton(state)
	if self.view and self.view.chat_view then
		self.view.chat_view:ShowHuSong(state)
	end
end

function MainuiWGCtrl:SetButtonVisible(index, is_show)
	-- self.view:SetButtonVisible(index, is_show)
end


--  按类型添加或删除提示图标
function MainuiWGCtrl:InvateTip(type, repetition_num, callback, param, effect_id, anim_pos)
	if type == nil then
		return
	end

	local fun_is_open = true
	if MAINUI_TIP_TYPE_TO_FUNNAME[type] and repetition_num ~= 0 then
		if type == MAINUI_TIP_TYPE.LIAOYILIAO or type == MAINUI_TIP_TYPE.FRIEND_LIAOYILIAO then
			fun_is_open = FunOpen.Instance:GetFunIsOpened("marry_jiehun") and FunOpen.Instance:GetFunIsOpened("society_friend")
		else
			fun_is_open = FunOpen.Instance:GetFunIsOpened(MAINUI_TIP_TYPE_TO_FUNNAME[type])
		end
	end

	if fun_is_open then
		if self:IsLoadMainUiView() then
			self.view:InvateTip(type, repetition_num, callback, effect_id, param)
		else
			self:AddInitCallBack(nil, function()
				self.view:InvateTip(type, repetition_num, callback, effect_id, param)
			end)
		end
	end
end

function MainuiWGCtrl:ShowRedBtn()
	self:AddInitCallBack(nil, function()
		self.view:ShowRedBtn()
	end)
end
function MainuiWGCtrl:RemoveTipIconByIconObj(tip_icon, is_strong)
	self:AddInitCallBack(nil, function()
		self.view:RemoveTipIcon(tip_icon, is_strong)
	end)
end

-- 自动采集
function MainuiWGCtrl:AutoGather(target, end_type, task_id)
	if self.view and self.view.task_view then
		self.view.task_view:MoveToTarget(target, end_type, task_id)
	end
end

function MainuiWGCtrl:GetCityCombatButtons()
	return self.view:GetCityCombatButtons()
end

function MainuiWGCtrl:SetNewServerBtnState()
	if self.view then
		self.view:SetNewServerBtnState()
	end
end

function MainuiWGCtrl:ChangeFightStateEnable(enable)
	if self.view then
		self.view:ChangeFightStateEnable(enable)
	end
end

function MainuiWGCtrl:ChangeFunctionTrailer()
	if self.view then
		self.view:ChangeFunctionTrailer()
	end
end

function MainuiWGCtrl:OnTaskShrinkToggleChange()
	if self.view and self.view:IsOpen() then
		self.view:OnTaskShrinkToggleChange()
	end
end

--管理主界面聊天小图标显示或隐藏操作(model_name = 归属的界面(根据该界面功能开启进行显示或隐藏), flush_name = 刷新参数, state = 隐藏或显示)
function MainuiWGCtrl:ChangeMainUiChatIconList(model_name, flush_name, state)
	self.data:ChangeMainUiChatIconList(model_name, flush_name, state)
	if not state then
		self.view:Flush(flush_name, {false})
	else
		self:CheckMainUiChatIconVisible(model_name, flush_name)
	end
end

function MainuiWGCtrl:CheckMainUiChatIconVisible(model_name, flush_name)
	local mainui_icon_list = self.data:GetMainUiIconList()
	if model_name then
		local can_see = OpenFunWGData.Instance:CheckIsHide(model_name)
		if can_see then
			self.view:Flush(flush_name, {true})
		end
		return
	end
	for k, v in pairs(mainui_icon_list) do
		for k1 in pairs(v) do
			local can_see = OpenFunWGData.Instance:CheckIsHide(k)
			if can_see then
				self.view:Flush(k1, {true})
			end
		end
	end
end

function MainuiWGCtrl:FlushView(...)
    local func = BindTool.Bind(self.view.Flush, self.view, ...)
    self:AddInitCallBack(nil, func)
end

function MainuiWGCtrl:IsLoaded()
	return self.view:IsLoaded()
end

function MainuiWGCtrl:HadSnapShotBackground()
	return self.view:HadSnapShotBackground()
end

function MainuiWGCtrl:SetIsAutoTaskState(state)
	self.view.task_view:SetAutoTaskState(state)
end

function MainuiWGCtrl:SetTaskAutoState(state)
	self.view.task_view:SetTaskAutoState(state)
end

function MainuiWGCtrl:OnClickGo()
	self.view.task_view:ClickGo()
end


function MainuiWGCtrl:ShowGuildChatRes()
	self.view:ShowGuildChatRes(ChatWGData.Instance:GetRedChat())
end

function MainuiWGCtrl:ShowGuildChatDaTi()
	self.view:ShowGuildChatDaTi(ChatWGData.Instance:GetGuildChatDaTi())
end

function MainuiWGCtrl:FlushActivityRed()
	self.activity_hall:FlushRankActivityRed()
end

function MainuiWGCtrl:CloseActivityHallView()
	if self.activity_hall:IsOpen() then
		self.activity_hall:Close()
	end
end

function MainuiWGCtrl:GetGBWorshipCountDown()
	if self.view then
		local count_down = self.view:GetGBWorshipCountDown()
		return count_down
	end
	return nil
end

function MainuiWGCtrl:SetGBWorshipCountDown(time)
	if self.view then
		self.view:SetGBWorshipCountDown(time)
	end
end

function MainuiWGCtrl:ShowGBWorshipCdmask(value)
	if self.view then
		self.view:ShowGBWorshipCdmask(value)
	end
end

function MainuiWGCtrl:ShowGBWorshipBtn(value)
	if self.view then
		self.view:ShowGBWorshipBtn(value)
	end
end

function MainuiWGCtrl:OnOpenTrigger(trigger_type, value)
	if self.view then
		self.view:OnOpenTrigger(trigger_type, value)
	end
end

function MainuiWGCtrl:IsPauseAutoTask()
	if self.view then
		-- return self.view:IsPauseAutoTask()
	end
	return true
end

function MainuiWGCtrl:OnContinueKillInfo(protocol)
	TipWGCtrl.Instance:OpenDoubleHitView(protocol)
end

function MainuiWGCtrl:SetBiPinTimeCountDown(reset_time_s)
	self.view:SetBiPinTimeCountDown(reset_time_s)
end

function MainuiWGCtrl:ChangeBiPinBtn(can_show)
	self.view:ChangeBiPinBtn(can_show)
end


function MainuiWGCtrl:ClickSwitch()
	self.view:ClickSwitch()
end

function MainuiWGCtrl:ShowWorshipEntrance(state)
	self.view:ShowWorshipEntrance(state)
end

function MainuiWGCtrl:DoTransfer(num)
	self.view:DoTransfer(num)
end

function MainuiWGCtrl:SetTransferBtnVIsible(num)
	self.view:SetTransferBtnVIsible(num)
end


function MainuiWGCtrl:OnResetPosCallBack()
	self.view:OnResetPosCallBack()
end

function MainuiWGCtrl:FlushChargeIcon()
	self.view:FlushChargeIcon()
end

function MainuiWGCtrl:OpenRecharge()
	self.view:OpenRecharge()
end

function MainuiWGCtrl:ChangeHuanZhuangShopBtn(state)
	self.view:ChangeHuanZhuangShopBtn(state)
end

function MainuiWGCtrl:GetRootNode()
	return self.view:GetRootNode()
end

function MainuiWGCtrl:GetRootNodeLayer()
	return self.view:GetRootNode().layer
end

function MainuiWGCtrl:SetShrinkButtonRepoint()
	self.view:SetShrinkButtonRepoint()
end

function MainuiWGCtrl:GetHuanZhuangShopActivity()
	return self.view:GetHuanZhuangShopActivity()
end

function MainuiWGCtrl:SetCrossServerUI( state )
	self:AddInitCallBack(nil, function()
		self.view:SetMianUIPlayerState(state)
		self:SetCrossServerTimeUI(state)
		self.view:SetMianUITargetState(false)
	end)
end

function MainuiWGCtrl:SetMianUIPlayerState(state)
	self:AddInitCallBack(nil, function()
		self.view:SetMianUIPlayerState(state)
	end)
end

function MainuiWGCtrl:SetMianUILeftContentState(state)
	self:AddInitCallBack(nil, function()
		self.view:SetMianUILeftContentState(state)
	end)
end

function MainuiWGCtrl:SetCrossServerTaskUI( state )
	self:AddInitCallBack(nil, function()
		-- self:SetTaskPanelAndTeamActive(state)
		self:SetCrossServerTimeUI(state)
	end)
end

function MainuiWGCtrl:SetMianUITargetState(is_active)
	self.view:AddInitCallBack(nil, function()
		self.view:SetMianUITargetState(is_active)
	end)
end


function MainuiWGCtrl:SetCrossServerTimeUI(state)
	self:AddInitCallBack(nil, function()
		self.view:SetMianUITopButtonState(state)
	end)
end

function MainuiWGCtrl:SetMianUITopButtonStateTwo(state)
	self:AddInitCallBack(nil, function()
		self.view:SetMianUITopButtonStateTwo(state)
	end)
end

--显示隐藏任务栏
-- function MainuiWGCtrl:SetTaskActive( enable )
-- 	self:AddInitCallBack(nil,function ()
-- 		if self.view then
-- 			self.view:SetTaskActive(enable)
-- 		end
-- 	end)
-- end

--显示隐藏任务栏（动画）
-- function MainuiWGCtrl:HideOrShowTaskParent(is_show)
-- 	self:AddInitCallBack(nil,function ()
-- 		if not self.view then
-- 			return
-- 		end
-- 		if is_show then
-- 			self.view:DoShowTaskParent()
-- 		else
-- 			self.view:DoHideTaskParent()
-- 		end
-- 	end)
-- end

-- 显示隐藏收起按钮
-- function MainuiWGCtrl:SetShrinkButtonsActive(enable)
-- 	self:AddInitCallBack(nil,function ()
-- 		if self.view then
-- 			self.view:SetShrinkButtonsActive(enable)
-- 		end
-- 	end)
-- end

--播放顶部按钮动画
function MainuiWGCtrl:PlayTopButtonTween(enable, lock_time, ignore_lock_time)
	self:AddInitCallBack(nil,function ()
		if self.view then
			self.view:PlayTopButtonTween(enable, lock_time, ignore_lock_time)
		end
	end)
end

--播放任务按钮动画
-- function MainuiWGCtrl:PlayTaskButtonTween( enable )
-- 	self:AddInitCallBack(nil,function ()
-- 		if self.view then
-- 			self.view:PlayTaskButtonTween(enable)
-- 		end
-- 	end)
-- end

-- function MainuiWGCtrl:SetEqTargetButtonShow()
-- 	self:AddInitCallBack(nil,function ()
-- 		if self.view then
-- 			self.view:SetEqTargetIcon()
-- 		end
-- 	end)
-- end


-- function MainuiWGCtrl:GetPlayTaskButtonStatus()
-- 	if self.view and self.view:IsLoaded() then
-- 		return self.view:GetPlayTaskButtonStatus()
-- 	end
-- 	return false
-- end

--更改任务按钮名字 尽量不要调用，在mainui_view_task中去根据scene_type去处理
-- function MainuiWGCtrl:ChangeTaskBtnName( str )
-- 	self:AddInitCallBack(nil,function ()
-- 		if self.view then
-- 			self.view:ChangeTaskBtnName(str)
-- 		end
-- 	end)
-- end

--更改组队按钮名字
function MainuiWGCtrl:ChangeTeamBtnName(str, is_team, new_scene_type)
	self:AddInitCallBack(nil,function ()
		if self.view then
			self.view:ChangeTeamBtnName(str, is_team, new_scene_type)
		end
	end)
end

--获取功能开启相关的主界面按钮名
function MainuiWGCtrl:GetAllButtonName()
	local button_list = {}
	if self.view then
		button_list = self.view:GetAllButtonName()
	end
	return button_list
end

-- function MainuiWGCtrl:SetTaskTabButtonsNode(bool)
-- 	self:AddInitCallBack(nil, function()
-- 		if self.view then
-- 			if self.view.is_mainui_view_load then
-- 				self.view.node_list.TabButtons:SetActive(bool)
-- 			end
-- 		end
-- 	end)
-- end

--获取任务列表父物体
function MainuiWGCtrl:GetTaskMaskRootNode(callback)
	if self.view then
		local node = self.view:GetTaskMaskRootNode(callback)
		if not node then
			node = nil
		end
		return node
	end
end

--进入副本时隐藏主界面
--该方法已经在CommonFbLogic中调用
--true == 显示主界面(按钮展开)
--false == 隐藏主界面(按钮点击)
function MainuiWGCtrl:ShowMainuiMenu( enable )
	local view = self.view
	if not view.is_mainui_view_load then
		view:SetNeedShowMainUiMenu(enable)
		return
	end

	if view then
		view:SetBtnLevel(not enable)
		view:SetMainIconAnimation(false)		--主按钮显示为技能动画
		view:PlayTopButtonTween(enable)			--收起上面按钮，必须放到SetMainIconAnimation后面，不然会在展开之后被SetMainIconAnimation收起来
		view:SetLeftBtnGroupShow(enable)
		if enable then
			self:SetFbIconEndCountDown(-1) 		--退出副本时关闭倒计时显示
		end
	end
end

function MainuiWGCtrl:SetToggleMenuIsOn( enable )
	local view = self.view
	if view.is_mainui_view_load then
		view:SetToggleMenuIsOn(enable)
	end
end

function MainuiWGCtrl:GetShrinkButtonToggleIsOn()
	local view = self.view
	if view.is_mainui_view_load then
		return view:GetShrinkButtonToggleIsOn()
	end
end

function MainuiWGCtrl:SetCameraModeBtnPos(pos_x,pos_y)
	local view = self.view
	if view.is_mainui_view_load then
		view:SetCameraModeBtnPos(pos_x,pos_y)
	end
end

--变身状态下坐骑按钮设置
function MainuiWGCtrl:SetBianShenStateZuoqiBtnState( enable )
	self:AddInitCallBack(nil, function ()
		if enable == true then
			self.special_mount_flag = nil
		else
			self.special_mount_flag = false
		end
		self.view:SetZuoqiBtnState(enable and self.normal_mount_flag or false)			--坐骑按钮
	end)
end

function MainuiWGCtrl:ChangeYuLongBtnState()
	self:AddInitCallBack(nil, function ()
		self.view:ChangeYulongBtnState()			--御龙按钮
	end)
end

function MainuiWGCtrl:ChangeMechaBsBtnState()
	self:AddInitCallBack(nil, function ()
		self.view:ChangeMechaBsBtnState()			--变身
	end)
end

--兼容特殊副本
--false == 隐藏任务列表和boss列表
--true  == 显示任务列表和boss列表
--
function MainuiWGCtrl:SetTaskPanel(enable,x,y, is_boss)
	local view = self.view
	if not enable and not view.is_mainui_view_load then
		view:SetNeedTaskPanel(enable,x,y)
		return
	end
	if view then
		-- view:SetTaskButtonTrue()
		if is_boss then
			view:SetTaskListActive(false)			--任务列表
			view:SetBossListActive(enable)			--boss列表
		else
			view:SetTaskListActive(enable)			--任务列表
			view:SetBossListActive(false)			--boss列表
		end
		if x and y then
			view:SetTaskPos22(x,y)
        end
        view:RefreshLeftPanel()
		-- view:SetBtnLevel(not enable)
	end
end

--还原任务列表
--退出副本时调用
function MainuiWGCtrl:ResetTaskPanel()
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:ResetTaskPanel()
			self:SetTaskPanel(true,151,-136.1)
		end
	end)
end

function MainuiWGCtrl:SetJoystickShowState(state)
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:SetJoystickShowState(state)
		end
	end)
end

function MainuiWGCtrl:SetZuoqiBtnShowState(state)
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:SetZuoqiBtnShowState(state)
		end
	end)
end


function MainuiWGCtrl:SetBtnLevel(enable)
	self:AddInitCallBack(nil, function()
		self.view:SetBtnLevel(enable)
	end)
end

function MainuiWGCtrl:AddObjToMainUI(obj, mainui_root_str, sibling_index)
	self:AddInitCallBack(nil, function()
		self.view:AddObjToMainUI(obj, mainui_root_str, sibling_index)
	end)
end

-- 添加按钮到离开按钮旁边  需要自己负责删除
function MainuiWGCtrl:AddBtnToFbIconGroup1Line(obj,sibling_index)
	self:AddInitCallBack(nil, function()
		self.view:AddBtnToFbIconGroup1Line(obj,sibling_index)
	end)
end

-- 添加按钮到离开按钮旁边  需要自己负责删除
function MainuiWGCtrl:AddBtnToFbIconGroup2Line(obj,sibling_index)
	self:AddInitCallBack(nil, function()
		self.view:AddBtnToFbIconGroup2Line(obj,sibling_index)
	end)
end

-- 添加按钮到挂机按钮旁边  需要自己负责删除
function MainuiWGCtrl:AddBtnToGuajiLine(obj, sibling_index)
	self:AddInitCallBack(nil, function()
		self.view:AddBtnToGuajiLine(obj,sibling_index)
	end)
end

-- 添加按钮到离开按钮旁边  需要自己负责删除
function MainuiWGCtrl:AddBtnToFbRightLineGroup(obj,sibling_index)
	self:AddInitCallBack(nil, function()
		self.view:AddBtnToFbRightLineGroup(obj,sibling_index)
	end)
end

-- function MainuiWGCtrl:SetFubenTaskList()
-- 	local function func()
-- 		self:SetTeamContenActive(false)
-- 		self:SetTask(true)
-- 	end
-- 	if self.view.is_mainui_view_load then
-- 		func()
-- 	else
-- 		self:AddInitCallBack(nil, func)
-- 	end
-- end

function MainuiWGCtrl:IsCountDowning()
	if self.view then
		return self.view:IsCountDowning()
	end
	return false
end

--退出副本按钮倒计时
--enable == false 默认参数，倒计时完毕退出副本
--enable == true 只倒计时，不做任何操作
function MainuiWGCtrl:SetFbIconEndCountDown(out_fb_time,enable,title_type, not_show_end_countdown)
	 -- print_error("退出副本按钮倒计时", out_fb_time)
	local function func()
		if self.view then
			self.view:SetFbIconEndCountDown(out_fb_time,enable,title_type, not_show_end_countdown)
		end
	end
	self:AddInitCallBack(nil, func)
end

--设置自动挂机 做兼容处理
function MainuiWGCtrl:SetAutoGuaJi( enable )
	if enable then
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	else
		GuajiWGCtrl.Instance:StopGuaji()
	end
end

--添加初始化回调方法
--@false == 没有初始化mainui
--@true == 已经初始化ui
function MainuiWGCtrl:AddInitCallBack(activity_type,callback)
	if not self.view.is_mainui_view_load then
		self.view:AddInitCallBack(activity_type,callback)
		return false
	else
		callback()
		return true
	end
end

function MainuiWGCtrl:RemoveInitCallBack(callback_key)
	self.view:RemoveInitCallBack(callback_key)
end

-------------ygxd add---------------
--副本模式
function MainuiWGCtrl:SetYGXDMode(enable,x,y,h,w)
	self:AddInitCallBack(nil, function()
		self.view:SetYGXDMode(enable,x,y,h,w)
	end)
end
--副本模式
-- function MainuiWGCtrl:SetTask(enable)
-- 	self:AddInitCallBack(nil, function()
-- 		self.view:SetTask(enable)
-- 	end)
-- end
--开始移动
function MainuiWGCtrl:StartTeamMove(enable)
	self:AddInitCallBack(nil, function()
		self.view:StartTeamMove(enable)
	end)
end
--设置内容
-- function MainuiWGCtrl:SetTeamContenActive(enable)
-- 	self:AddInitCallBack(nil, function()
-- 		self.view:SetTeamContenActive(enable)
-- 	end)
-- end

function MainuiWGCtrl:HideOrShowChatView(bo)
	self:AddInitCallBack(nil, function()
		self.view:HideOrShowChatView(bo)
	end)
end
-- function MainuiWGCtrl:SetTaskPanelAndTeamActive(enable)
-- 	self:AddInitCallBack(nil, function()
-- 		self.view:SetTaskPanelAndTeamActive(enable)
-- 	end)
-- end

-- 特殊需求: 显示和隐藏技能按钮 (如跨服答题不显示常用技能)
-- 默认为true
function MainuiWGCtrl:SetSkillShowState(enable)
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:SetSkillShowState(enable)
		end
	end)
end

function MainuiWGCtrl:GetSkillContent()
    if self.view then
        return self.view:GetSkillContent()
    end
end

function MainuiWGCtrl:SetRightBottomActive(is_active)
	self:AddInitCallBack(nil, function()
		self.view:SetRightBottomActive(is_active)
	end)
end

function MainuiWGCtrl:SetVisibleNormalSkillPanel(is_active)
	self:AddInitCallBack(nil, function()
		self.view:SetVisibleNormalSkillPanel(is_active)
	end)
end

-- function MainuiWGCtrl:IsShowTeamBtn(is_show)
-- 	self:AddInitCallBack(nil, function()
-- 		self.view:IsShowTeamBtn(is_show)
-- 	end)
-- end

function MainuiWGCtrl:SetTeamBtnState(is_show)
	self:AddInitCallBack(nil, function()
		self.view:SetTeamBtnState(is_show)
	end)
end

function MainuiWGCtrl:SetFBNameState(is_show, fb_name_str)
	self:AddInitCallBack(nil, function()
		self.view:SetFBNameState(is_show, fb_name_str)
	end)
end

-- function MainuiWGCtrl:SetFBName(fb_name)
-- 	self:AddInitCallBack(nil, function()
-- 		self.view:SetFBName(fb_name)
-- 	end)
-- end

--更新图片位置
function MainuiWGCtrl:UpdateFunuiPos( enable )

end

function MainuiWGCtrl:SetMenuIconIsOn(isOn)
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:SetMainIconAnimation(isOn)
		end
	end)
end

function MainuiWGCtrl:SetShrinkButtonIsOn(isOn)
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:SetShrinkButtonIsOn(isOn)
		end
	end)
end

function MainuiWGCtrl:ReloadTeamList()
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:ReloadTeamList()
		end
	end)
end


-- function MainuiWGCtrl:SetTaskButtonTrue()
-- 	self:AddInitCallBack(nil,function ()
-- 		if self.view then
-- 			self.view:SetTaskButtonTrue()
-- 		end
-- 	end)
-- end

--通关活动id获取按钮的类
function MainuiWGCtrl:GetActivityButtonByActivityType(act_type)
	if self.view then
		return self.view:GetActivityButtonByActivityType(act_type)
	end
end

function MainuiWGCtrl:ResetLightBoss()
	if self.view then
		self.view:SetCurIndex(0)
	end
end

function MainuiWGCtrl:SetLightBossId(boss_id)
	if self.view then
		self.view:SetCurIndex(boss_id)
	end
end

function MainuiWGCtrl:GetLightBossId()
	local boss_id
	if self.view then
		boss_id = self.view:GetCurIndex()
	end

	return boss_id
end

function MainuiWGCtrl:GetTaskOtherContent()
	return self.view:GetTaskOtherContent()
end

function MainuiWGCtrl:GetPlayerInfoWidgetRoot()
	return self.view:GetPlayerInfoWidgetRoot()
end

function MainuiWGCtrl:GetMainWuHunUIEffectRoot()
	return self.view:GetMainWuHunUIEffectRoot()
end

function MainuiWGCtrl:GetSkillRightOtherContent()
	return self.view:GetSkillRightOtherContent()
end


-- --设置显示伤害面板，要记录状态
-- function MainuiWGCtrl:SetLeftPanelShowContent()
-- 	self:AddInitCallBack(nil, function()
-- 		self.view:SetLeftPanelShowContent()
-- 	end)
-- end

-- --隐藏伤害面板，设置成之前的状态
-- function MainuiWGCtrl:RevertLeftPanelStatus()
-- 	self:AddInitCallBack(nil, function()
-- 		self.view:RevertLeftPanelStatus()
-- 	end)
-- end

function MainuiWGCtrl:SetOtherContents( active )
	self:AddInitCallBack(nil, function()
		self.view:SetOtherContents(active)
	end)
end

function MainuiWGCtrl:SetTaskContents(active)
	self:AddInitCallBack(nil, function()
		self.view:SetTaskContents(active)
	end)
end

--设置显示othercontent，隐藏任务列表taskcontent
function MainuiWGCtrl:SetShowOtherHideTask(active)
	self:AddInitCallBack(nil, function()
        self.view:SetTaskContents(not active)
        self.view:SetOtherContents(active)
	end)
end


function MainuiWGCtrl:AddTempActIcon(act_type, end_time, act_cfg, color, flag, open_time)
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:AddTempActIcon(act_type, end_time, act_cfg, color, flag, open_time)
		end
	end)
end

function MainuiWGCtrl:DelTempActIcon(act_type)
	self:AddInitCallBack(nil,function ()
		if self.view then
			self.view:DelTempActIcon(act_type)
		end
	end)
end

--竞技场/战场修改场景模式
function MainuiWGCtrl:ChangeCameraMode(is_ignore_scene)
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:OnClickCameraMode(is_ignore_scene)
		end
	end)
end

--ShowIndexCallBack调用
function MainuiWGCtrl:CheckCachePower()
	self:AddInitCallBack(nil, function()
		if self.data then
			local data = self.data:GetCacheTable()
			if data and data[1] and not IsEmptyTable(data[1]) then
				MainuiWGCtrl.Instance.view:ShowPowerChange(data[1].value,data[1].old_value)
			end
			self.data:CacheTableClearData()
		end
	end)
end
--创建缓存
function MainuiWGCtrl:CreateCacheTable()
	if self.data then
		self.data:CreateCacheTable()
	end
end
--清空缓存（一般在点击进阶按钮的时候调用）
function MainuiWGCtrl:CacheTableClearData()
	if self.data then
		self.data:CacheTableClearData()
	end
end
--延时播放战斗力飘字
function MainuiWGCtrl:DelayShowCachePower(delay, callback)
	if self.data then
		local data = self.data:GetCacheTable()
		if data and data[1] then
			if self.delay_power_timer then
				GlobalTimerQuest:CancelQuest(self.delay_power_timer)
				self.delay_power_timer = nil
			end

			self.delay_power_timer = GlobalTimerQuest:AddDelayTimer(function()
				data = self.data:GetCacheTable()
				if not data or not data[1] then
					self.delay_power_timer = nil
					self:CacheTableClearData() --播放完 立马置空
				 	return
				end

				self.view:ShowPowerChange(data[1].value, data[1].old_value)
				self:CacheTableClearData() --播放完 立马置空
				self.delay_power_timer = nil

				if callback then
					callback()
				end
			end, delay or 1)
		else
			if callback then
				callback()
			end

			self:CacheTableClearData() --播放完 立马置空
		end
	end
end

function MainuiWGCtrl:CloseLevelFBAlert()
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:CloseLevelFBAlert()
		end
	end)
end

function MainuiWGCtrl:SetMianUITargetPos(pos_x,pos_y)
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:SetMianUITargetPos(pos_x,pos_y)
		end
	end)
end

function MainuiWGCtrl:SetToPositionalWaefareViewBtnState(is_show)
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:SetToPositionalWaefareViewBtnState(is_show)
		end
	end)
end

function MainuiWGCtrl:SetToHonorhallRewardState(is_show)
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:SetToHonorhallRewardState(is_show)
		end
	end)
end

function MainuiWGCtrl:SetToHonorhallShopBtnState(is_show)
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:SetToHonorhallShopBtnState(is_show)
		end
	end)
end

function MainuiWGCtrl:FlushGuildIcon(is_show_answer_tip)
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:FlushGuildIcon(is_show_answer_tip)
		end
	end)
end

function MainuiWGCtrl:GetIsCanShowGuildEffect()
	return self.is_can_show_guildeffect
end

function MainuiWGCtrl:GetJieXiText(content)
	local i, j = 0, 0
	local element_list = {}
	local last_pos = 1
	for loop_count = 1, 100 do
		i, j = string.find(content, "({.-})", j + 1)-- 匹配规则{face;20} {item;26000}
		if nil == i or nil == j then
			if last_pos <= #content then
				table.insert(element_list, {0, string.sub(content, last_pos, -1)})
			end
			break
		else
			if 1 ~= i and last_pos ~= i then
				table.insert(element_list, {0, string.sub(content, last_pos, i - 1)})
			end
			table.insert(element_list, {1, string.sub(content, i, j)})
			last_pos = j + 1
		end
	end
	return element_list
end


function MainuiWGCtrl:GetBtnRoleBagView()
	if self.view and self.view.node_list then
		return self.view.node_list.BtnRoleBagView
	end
end

function MainuiWGCtrl:GetBtnQiFuView()
	if self.view and self.view.node_list then
		return self.view.node_list.BtnQiFuView
	end
end

function MainuiWGCtrl:UpdateBianShenSkillState()
	self.view:UpdateBianShenSkillState()
end

function MainuiWGCtrl:SetMainTopBtnObjEnable(key, enable, no_anim, icon_str)
	local func = function ()
		self.view:SetMainTopBtnObjEnable(key, enable, no_anim, icon_str)
	end
	self:AddInitCallBack(nil, func)
end

function MainuiWGCtrl:GetOneSundryButton(key)
	return self.view:GetOneSundryButton(key)
end

function MainuiWGCtrl:GetOneSundryNode(key)
	return self.view:GetOneSundryNode(key)
end

function MainuiWGCtrl:CheckCollectPanelIsOpenOneAct(data)
	return self.view:CheckCollectPanelIsOpenOneAct(data)
end

function MainuiWGCtrl:SetShowTimeTextState( state )
	self:AddInitCallBack(nil, function()
		self.view:SetShowTimeTextState(state)
	end)
end
function MainuiWGCtrl:SetFbPrepareTimeTextMark( state )
	self.show_text_time_mark = state
end
function MainuiWGCtrl:GetFbPrepareTimeTextMark()
	return self.show_text_time_mark
end

function MainuiWGCtrl:SetFbTimePos( x,y )
	self:AddInitCallBack(nil, function()
		self.view:SetFbTimePos(x,y)
	end)
end
-- function MainuiWGCtrl:FlushActDiscount()
-- 	if self.view then
-- 		self.view:ShowActDiscountBtn(ActDiscountData.Instance:ShowMainUIBtn())
-- 	end
-- end

function MainuiWGCtrl:ChangeMatchState(info)
	self:AddInitCallBack(nil, function()
		self.view:ChangeMatchState(info)
	end)
end

function MainuiWGCtrl:FlushMatchStateInfo()
	self.view:FlushMatchStateInfo()
end

--获取技能系统按钮位置
function MainuiWGCtrl:GetSkillBottomButton()
	return self.view:GetSkillBottomButton()
end

function MainuiWGCtrl:SetModeBtnActive(obj_name,enable)
	self.attack_mode_view:SetModeBtnActive(obj_name,enable)
end

-- 离开野外副本
function MainuiWGCtrl:LevelFB()
	return self.view:LevelFB()
end
function MainuiWGCtrl:FlushKfPKRenPoint()
	self:AddInitCallBack(nil, function()
		self.view:FlushKfPKRenPoint()
	end)
end

-- function MainuiWGCtrl:SetTaskAndTeamCallBack(task_callback, team_callback)
-- 	self.view:SetTaskCallBack(task_callback)
-- 	self.view:SetTeamCallBack(team_callback)
-- end

--设置房间信息显隐
function MainuiWGCtrl:SetRoomActive(is_active)
    self:AddInitCallBack(nil, function()
		self.view:SetRoomActive(is_active)
	end)
end

--刷新房间
function MainuiWGCtrl:FlushRoom()
	self:AddInitCallBack(nil, function()
		self.view:FlushRoom()
		self.view:RoomSceneTypeFlushMemberCount()
	end)
end

--刷新多倍
function MainuiWGCtrl:FlushDuoBei()
	self:AddInitCallBack(nil, function()
		self.view:FlushDuoBeiState()
	end)
end

function MainuiWGCtrl:ClearTimeText()
	self:AddInitCallBack(nil, function()
		self.view:ClearTimeText()
	end)
end

function MainuiWGCtrl:SetFriendRemind()
	self:AddInitCallBack(nil, function()
		self.view:SetFriendRemind()
	end)
end

function MainuiWGCtrl:SetTaskRootNodeActive(is_active)
	self:AddInitCallBack(nil, function()
		self.view:SetTaskRootNodeActive(is_active)
	end)
end

function MainuiWGCtrl:SetXieZhuRemind()
	self:AddInitCallBack(nil, function()
		self.view:ChatCanShowCallHelp()
	end)
end

function MainuiWGCtrl:SetCameraModeBtnState(enable)
	self:AddInitCallBack(nil, function()
		self.view:SetCameraModeBtnState(enable)
	end)
end

function MainuiWGCtrl:SetTransparentMaskActive(is_active)
	self:AddInitCallBack(nil, function()
		self.view:SetTransparentMaskActive(is_active)
	end)
end

function MainuiWGCtrl:UpdateMainRoleHp()
	self:AddInitCallBack(nil, function()
		self.view:UpdateMainRoleHp()
	end)
end

function MainuiWGCtrl:OpenMainUiModeView()
	if self.mian_ui_hide_mode_view then
		self.mian_ui_hide_mode_view:Open()
	end
end

-- function MainuiWGCtrl:RefreshTaskList()
-- 	if self.task_list and self.task_list.scroller then
-- 		self.task_list.scroller:RefreshAndReloadActiveCellViews(true)
-- 	end
-- end

function MainuiWGCtrl:UpdateCameraAutoRotate()
	self:AddInitCallBack(nil, function()
		self.view:SetAutoRotation()
	end)
end

--设置首充新服倒计时提示 显示状态
function MainuiWGCtrl:SetShouChongAnimShowState(flag)
	self.shouchong_anim_show_state = flag
end

function MainuiWGCtrl:GetShouChongAnimShowState()
	return self.shouchong_anim_show_state
end

function MainuiWGCtrl:ActivitySetButtonVisible(act_type, is_show, sort)
	if self.view then
		self:AddInitCallBack(act_type, function ()
			self.view:ActivitySetButtonVisible(act_type, is_show, sort)
		end)
	end
end

function MainuiWGCtrl:SetActBtn(act_type, act_cfg, sort)
	if self.view then
		self:AddInitCallBack(act_type, function ()
			self.view:SetActBtn(act_type, act_cfg, sort)
		end)
	end
end

function MainuiWGCtrl:PlayEffectByRechargeSecond(view_name, start_obj, end_obj, effect_count)
	self.view:PlayEffectByRechargeSecond(view_name, start_obj, end_obj, effect_count)
end

function MainuiWGCtrl:GetFbSceneCfg(scene_type)
	local fb_config = ConfigManager.Instance:GetAutoConfig("fb_scene_config_auto")
    return fb_config.fb_scene_cfg_list[scene_type or Scene.Instance:GetSceneType()] or
    fb_config.fb_scene_cfg_list[SceneType.Common]
end

function MainuiWGCtrl:SetFirstEnterFb(flag)
	self.is_first_enter_fb = flag
end

function  MainuiWGCtrl:GetFirstEnterFb()
	return self.is_first_enter_fb
end

function MainuiWGCtrl:OnSceneLoadingEnter()
	self:AddInitCallBack(nil, function()
		self.view:CloseStrongMenu()
		self.view:CloseStrongGetRewardView()
		self.view:StopAutoRevenge()
		self.view:OnJoystickSceneChangeBegin()
		FightRevengeWGCtrl.Instance:ClearRevengeList()
	end)
end

function MainuiWGCtrl:CloseStrongMenu()
	self:AddInitCallBack(nil, function()
		self.view:CloseStrongMenu()
	end)
end

function MainuiWGCtrl:OnSceneLoadingEnd()
	self:AddInitCallBack(nil, function()
		self.view:FlushCalendar()
		self.view:FlushNewSerRecTip()
		self.view:FlushGodPurchaseTip()
		-- self.view:FlushDragonWildBuyTip()
		self.view:FlushDragonKingTokenTip()
		-- self.view:FlushLevelRechargeTips()
		self.view:FlushVienTianeTianYinTip()
		self.view:FlushSpecialACTPanelState()
	end)
end

function MainuiWGCtrl:FlushTaskView()
	if self.view:IsLoaded() then
		self.view:FlushTaskView()
	end
end

function MainuiWGCtrl:ClsoeTaskCellGuide(task_id)
	if self.view:IsLoaded() then
		self.view:ClsoeTaskCellGuide(task_id)
	end
end

function MainuiWGCtrl:SetXiaoGuiInfoFlag(flag)
	self.xioagui_flag = flag
end

function MainuiWGCtrl:GetXiaoGuiInfoFlag()
	return self.xioagui_flag
end

function MainuiWGCtrl:InsertNeedOpenView(form_type,can_open,fun)
	self.data:InsertNeedOpenView(form_type,can_open,fun)
end

function MainuiWGCtrl:OpenNextView(cur_form_type)
	self.data:OpenNextView(cur_form_type)
end

-- 设置摇杆显示隐藏
function MainuiWGCtrl:SetJoystickShowState(show)
    self.view:SetJoystickShowState(show)
end

-- 控制连接方法
function MainuiWGCtrl:ActiveVIPConnectToTop(isOn)
	self.vip_connect_top = true
	self:AddInitCallBack(nil, function()
		self.view:SetVIPBtnState(isOn)
	end)
end

function MainuiWGCtrl:UnActiveVIPConnectToTop()
	self.vip_connect_top = false
	self:AddInitCallBack(nil, function()
		self.view:SetVIPBtnState(true)
	end)
end

function MainuiWGCtrl:GetVipConnectTop()
	return self.vip_connect_top
end

function MainuiWGCtrl:OnTopArrowChange(ison)
	if MainuiWGCtrl.Instance:GetVipConnectTop() then
		self:AddInitCallBack(nil, function()
			self.view:SetVIPBtnState(ison)
		end)
	end
end

function MainuiWGCtrl:ChangeVipBtnShow(_bool)
	self.view:SetVIPBtnState(_bool)
end

function MainuiWGCtrl:FlushTaskTopPanel()
	if self.view:IsLoaded() then
        self.view:FlushTaskTopPanel()
    else
        self:AddInitCallBack(nil, function()
			self.view:FlushTaskTopPanel()
		end)
	end
end

function MainuiWGCtrl:GetBtnTSSkillBtn()
	if self.view and self.view.node_list then
		return self.view.node_list["bianshen_skill_1"]
	end
end

function MainuiWGCtrl:UpdateBianShenBtnState(flag)
	self:AddInitCallBack(nil, function()
		self.view:UpdateBianShenBtnState(flag)
	end)
end

function MainuiWGCtrl:GetRoleAutoXunluState()
	if self.view ~= nil then
		return self.view:GetRoleAutoXunluState()
	end

	return nil
end

function MainuiWGCtrl:AddRevenge(role)
	self:AddInitCallBack(nil, function ()
		self.view:AddRevenge(role)
	end)
end

function MainuiWGCtrl:RevengeChange()
	self:AddInitCallBack(nil, function ()
		self.view:RevengeChange()
	end)
end

function MainuiWGCtrl:OnMainRoleDead(obj)
	self.view:OnMainRoleDead(obj)
end

function MainuiWGCtrl:OnRoleObjDead(role_id)
	self.view:OnRoleObjDead(role_id)
end

function MainuiWGCtrl:StopAutoRevenge(is_reset, str, role_id, is_check)
	if self.view ~= nil then
		self.view:StopAutoRevenge()
		if role_id ~= nil then
			RevengeWGData.Instance:ClearRoleInList(role_id)
		end

		if is_reset then
			GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end

		self:AddInitCallBack(nil, function()
			if role_id ~= nil then
				self.view:RevengeChange()
			else
				self.view:FlushRevengeState(0)
			end
		end)

		if str ~= nil then
			SysMsgWGCtrl.Instance:ErrorRemind(str)
		end
	end
end

function MainuiWGCtrl:TryRevengeRole(check_role_id)
	self:AddInitCallBack(nil, function()
		self.view:TryRevengeRole(check_role_id)
	end)
end

function MainuiWGCtrl:FlushFindRole()
	self:AddInitCallBack(nil, function()
		self.view:FlushFindRole()
	end)
end

function MainuiWGCtrl:LockCameraAutoRotate()
	if self.view then
		self.view:LockCameraAutoRotate()
	end
end

function MainuiWGCtrl:UnLockCameraAutoRotate()
	self:AddInitCallBack(nil, function()
		self.view:UnLockCameraAutoRotate()
	end)
end

-- is_show为true表示boss信息面板切换到由任务面板
-- function MainuiWGCtrl:ShowTaskPanelInBossScene(is_show)
-- 	if MainuiWGData.Instance:NeedShowBossShrinkBtn() then
-- 		self:AddInitCallBack(nil, function()
-- 			self.view:OnClickBossShrinkBtn(not is_show, true)
-- 		end)
-- 	end
-- end

function MainuiWGCtrl:FlushSitState()
	self:AddInitCallBack(nil, function()
		self.view:Flush(0, "sit_state")
	end)
end

-- function MainuiWGCtrl:PlayAutoSitAni(value)
-- 	self:AddInitCallBack(nil,function ()
-- 		self.view:SetSitStatus(value)
-- 	end)
-- end

function MainuiWGCtrl:SetSkillForceCancle()
	self.view:SetSkillForceCancle()
end

function MainuiWGCtrl:FlushSkillBuffShow()
	self:AddInitCallBack(nil,function ()
		self.view:FlushBuffLayer(false)
	end)
end

function MainuiWGCtrl:TryFlushBuffLayer(buff_type)
	self:AddInitCallBack(nil,function ()
		self.view:TryFlushBuffLayer(buff_type)
	end)
end

function MainuiWGCtrl:CloseBossCardContainer()
	if self.view and self.view.is_mainui_view_load then
		self.view:HideBossRefreshBtns()
	end
end

function MainuiWGCtrl:GetIsShowRang()
	return self.view:GetIsShowRang()
end

--轻功跳跃的按钮显示
function MainuiWGCtrl:SetMainRoleJumpState(flag)
	self:AddInitCallBack(nil, function()
		self.view:OnMainRoleJumpState(flag)
	end)
end

function MainuiWGCtrl:UpdataSiXiangSkillNuQi()
	self:AddInitCallBack(nil, function ()
		self.view:FlushSiXiangSkillProgress()
	end)
end

function MainuiWGCtrl:UpdataHaloSkillProgress()
	self:AddInitCallBack(nil, function ()
		self.view:FlushHaloSkillProgress()
	end)
end

function MainuiWGCtrl:CancelTargetObj(obj_id)
	self:AddInitCallBack(nil,function ()
		self.view:CancelTargetObj(obj_id)
	end)
end

function MainuiWGCtrl:FlushXunLuStates()
	self:AddInitCallBack(nil,function ()
		self.view:FlushXunLuStates()
	end)
end

function MainuiWGCtrl:ClickEnterActivity(act_type)
	self:AddInitCallBack(nil,function ()
		self.view:ClickEnterActivity(act_type)
	end)
end

function MainuiWGCtrl:SetClickFindLatelyGatherBtnShow(value)
	self:AddInitCallBack(nil,function ()
		self.view:SetClickFindLatelyGatherBtnShow(value)
	end)
end

function MainuiWGCtrl:GetLatelyGatherBtnState()
	return self.view:GetLatelyGatherBtnState()
end

function MainuiWGCtrl:GetMergeFunctionVisib(btn_name)
	return self.view:GetMergeFunctionVisib(btn_name)
end

function MainuiWGCtrl:GetLimitGiftBtnTrans()
	-- self:AddInitCallBack(nil,function ()
		return self.view:GetLimitGiftBtnTrans()
	-- end)
end

function MainuiWGCtrl:SetLimitGiftBtnTransActive(enable)
	self:AddInitCallBack(nil,function ()
		return self.view:SetLimitGiftBtnTransActive(enable)
	end)
end

-- function MainuiWGCtrl:SetGuoJiaYuGaoStatus(value,red)
-- 	local func = function ()
-- 		self.view:SetGuoJiaYuGaoStatus(value,red)
-- 	end
-- 	self:AddInitCallBack(nil, func)
-- end

function MainuiWGCtrl:SetChatPrivateUnreadMsgHead()
	self:AddInitCallBack(nil,function ()
		return self.view:SetChatPrivateUnreadMsgHead()
	end)
end

function MainuiWGCtrl:ChangeZuoQiState(flag)
	self:AddInitCallBack(nil, function ()
		return self.view:ChangeZuoQiState(flag)
	end)
end

-- 是否显示了新服充值优势
function MainuiWGCtrl:IsShowNewSerRecTip()
	return self.view:IsShowNewRecTip()
end

function MainuiWGCtrl:SetLongZhuSkillBtnActive(is_active)
	self.view:SetLongZhuSkillBtnActive(is_active)
end

function MainuiWGCtrl:UpdateChatIcon()
	self.view:UpdateChatIcon()
end

function MainuiWGCtrl:TeamCellFlush()
	self.view:TeamCellFlush()
end

function MainuiWGCtrl:InitDistanceChangeCallback()
	self.view:InitDistanceChangeCallback()
end

function MainuiWGCtrl:FlushFPAdvanceNotice()
	local func = function ()
		self.view:FlushFPAdvanceNotice()
	end
	self:AddInitCallBack(nil, func)
end

function MainuiWGCtrl:FlushTaskAdvanceNotice()
	local func = function ()
		self.view:FlushTaskAdvanceNotice()
	end
	self:AddInitCallBack(nil, func)
end

-- 秘笈技能
function MainuiWGCtrl:FlushEsotericaSkill()
	self:AddInitCallBack(nil, function()
		self.view:OnFlushEsotericaSkill()
	end)
end

function MainuiWGCtrl:FlushMainUiActBtnBubbleInfo(act_type, show_bubble, desc)
	local act_btn = self:GetActivityButtonByActivityType(act_type)
	local param_t = {act_type = act_type, show_bubble = show_bubble, desc = desc}
	if not act_btn then
		self.view:AddWaitActivityBtnCreatFlush(act_type, "SetBubbleDesc", param_t)
		return
	end

	act_btn:Flush("SetBubbleDesc", param_t)
end

function MainuiWGCtrl:FlushMainUiActBtnYanYuGeNobleTip()
	local act_btn = self:GetActivityButtonByActivityType(ACTIVITY_TYPE.YanYuGe)
	if not act_btn then
		self.view:AddWaitActivityBtnCreatFlush(ACTIVITY_TYPE.YanYuGe, "YanYuGeNobleTip")
		return
	end

	act_btn:Flush("YanYuGeNobleTip")
end

function MainuiWGCtrl:OnSCIllegalityNotcie(protocol)
	-- if protocol.notice_type == NOTCIE_TYPE.MOVE_CHECK_NOT_PASS then
	-- 	ViewManager.Instance:Open(GuideModuleName.IllegalityNoticeView)
	--  	GuajiWGCtrl.Instance:StopGuaji()
	-- end
	
	-- 心跳包异常
	if protocol.notice_type == NOTCIE_TYPE.HEARTBEAT_ERROR then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Notcie.HeartbeatError)
		GameNet.Instance.custom_disconnect_reason = GameNet.DISCONNECT_REASON_FORBIDDEN
		GameNet.Instance.custom_disconnect_notice_type = DISCONNECT_NOTICE_TYPE.HEARTBEAT_ERROR
	end
end

function MainuiWGCtrl:OnSCRoleOtherRouteInfo(protocol)
	self.data:SetRouteInfo(protocol)
end

-- 设置低于主界面的UI特效
function MainuiWGCtrl:SetUnderMainUIEffectInfo(param_t)
	self:AddInitCallBack(nil, function()
		if self.view then
			if param_t and param_t.is_show then
				self.view:SetUnderMainUIEffect(param_t.effect_bundle, param_t.effect_asset)
			else
				self.view:HideUnderMainUIEffect()
			end
		end
	end)
end

-- 设置境界变身UI特效
function MainuiWGCtrl:SetAransformationMainUIEffect(param_t)
	self:AddInitCallBack(nil, function()
		if self.view then
			if param_t and param_t.is_show then
				self.view:SetAransformationMainUIEffect(param_t.effect_bundle, param_t.effect_asset)
			else
				self.view:HideAransformationMainUIEffect()
			end
		end
	end)
end

function MainuiWGCtrl:OpenFunEventChange(check_all, fun_name, is_open)
    if check_all then
		WelfareWGCtrl.Instance:MainuiOpenCreate()
		self:SetCollectBtnState("BtnLotteryCollect")
		self:SetCollectBtnState("BtnHongHuangCollect")
		self:SetCollectBtnState("BtnQiFuCollect")
	elseif fun_name == FunName.SiXiangCallView or fun_name == FunName.TianShenLingHeDrawView or 
		fun_name == FunName.FiveElementsTreasuryView or fun_name == FunName.ShenJiTianCiView then 
			self:SetCollectBtnState("BtnLotteryCollect")
	elseif fun_name == FunName.HongHuangClassicView or fun_name == FunName.ChaoticVipSpecialView or
		fun_name == FunName.ChaoticPurchaseView or fun_name == FunName.HongHuangGoodCoremonyView then 
			self:SetCollectBtnState("BtnHongHuangCollect")
	elseif fun_name == FunName.QIFU or fun_name == FunName.Welfare or fun_name == FunName.SevenDay then
		if fun_name == FunName.SevenDay then
			WelfareWGCtrl.Instance:MainuiOpenCreate()
		end

		self:SetCollectBtnState("BtnQiFuCollect")
    end

	if self.view then
		self.view:ActivityAggregationPermanpentBtnVisibleChange(fun_name)
	end
end

function MainuiWGCtrl:SetCollectBtnState(sundry_button_str)
	local data = self:GetOneSundryButton(sundry_button_str)
	if not data then
		return
	end

	local is_only_one, one_data, count = self:CheckCollectPanelIsOpenOneAct(data)
	local icon_str = nil

	if is_only_one then
		icon_str = one_data.act_icon
	else
		icon_str = data.defult_icon
	end

	self:SetMainTopBtnObjEnable(sundry_button_str, count > 0, nil, icon_str)
end

-- function MainuiWGCtrl:SetBtnTianxianPavilionStatus(status)
-- 	self.view:SetBtnTianxianPavilionStatus(status)
-- end

--判断破盾后的伤害计算面板
function MainuiWGCtrl:SetTianShenBreakShieldHurt(info)
    self:AddInitCallBack(nil, function()
		self.view:SetTianShenBreakShieldHurt(info)
	end)
end

-- 清除计时器
function MainuiWGCtrl:DoCleanParticleTimerFunc()
	self:CleanMarkerTimer()
	self.clean_particle_time = GlobalTimerQuest:AddDelayTimer(function ()
		if self.view then
			self.view:CleanMoneyAllParticleTimer()
			self.view:CleanAllItemParticleTimer()
		end
    end, 3)
end

function MainuiWGCtrl:CleanMarkerTimer()
	if self.clean_particle_time then
		GlobalTimerQuest:CancelQuest(self.clean_particle_time)
	end
	self.clean_particle_time = nil
end

function MainuiWGCtrl:SetChangeChatState(state)
	self:AddInitCallBack(nil, function()
		self.view:SetChangeChatState(state)
	end)
end

function MainuiWGCtrl:SetSnapShotBackgroundShow(is_show)
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:SetSnapShotBackgroundShow(is_show)
		end
	end)
end

-- 打开变强按钮
function MainuiWGCtrl:SetDataAndOpenStrongMenu(param)
	self.view:SetDataAndOpenStrongMenu(param)
end

function MainuiWGCtrl:SetSpecialACTPanelToggle(is_on)
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:SetSpecialACTPanelToggle(is_on)
		end
	end)
end

function MainuiWGCtrl:PlayCommonSkillBaoDianEffect(skill_id)
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:PlayCommonSkillBaoDianEffect(skill_id)
		end
	end)
end

function MainuiWGCtrl:GetNewBuffAddToAniWaitList(effect_info)
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:GetNewBuffAddToAniWaitList(effect_info)
		end
	end)
end

function MainuiWGCtrl:SetBeastBuffIcon(is_show, element_type, remaining_time)
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:FlushBeastBuffIcon(is_show, element_type, remaining_time)
		end
	end)
end

function MainuiWGCtrl:PlayBeastBuffAni(combo_skill_img, element_param1, element_param2, skill_bundle, skill_asset)
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:PlayBeastComboBuffAni(combo_skill_img, element_param1, element_param2, skill_bundle, skill_asset)
		end
	end)
end

function MainuiWGCtrl:PlayUseEsotericaSkillEffect(index)
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:PlayUseEsotericaSkillEffect(index)
		end
	end)
end

function MainuiWGCtrl:OnClickFollowBtn()
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:OnClickFollowBtn()
		end
	end)
end

function MainuiWGCtrl:OnClickSit()
	if self.view then
		self.view:OnClickSit()
	end
end

function MainuiWGCtrl:RefreshBossItemCellsData()
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:RefreshBossItemCellsData()
		end
	end)
end

function MainuiWGCtrl:FlushSecKillBossPart()
	self:AddInitCallBack(nil, function()
		if self.view then
			self.view:FlushSecKillBossPart()
		end
	end)
end

function MainuiWGCtrl:ChangePosByTransFerEquipCollect(is_open_equip_collect)
	self:AddInitCallBack(nil,function ()
		self.view:ChangePosByTransFerEquipCollect(is_open_equip_collect)
	end)
end

--变强气泡提醒，间隔显示气泡倒计时.
function MainuiWGCtrl:UpdateBianQiangBubbleTips()
	local other_cfg = MainuiWGData.Instance:GetBianQiangBottomOtherCfg()
	local role_level = RoleWGData.Instance:GetRoleLevel()

	if other_cfg.bubble_level_limit >= role_level then
		self:CleanBianQiangBubbleTipsTimer()

		local server_time = TimeWGCtrl.Instance:GetServerTime()
		local key = "bianqiang_bubble_countdown"
		CountDownManager.Instance:AddCountDown(key,
			-- 回调方法
			function()
			end,
			-- 倒计时完成回调方法
			function()
				local data_list = MainuiWGData.Instance:GetBianQiangListCache()
				local red_num = #data_list
				--提升气泡显示条件：x级之前 && 红点>0 && 玩家X秒未点击.
				local is_show = other_cfg.bubble_level_limit >= role_level and red_num > 0
				self:ShowBianQiangBubbleTips(is_show)
				self:CleanBianQiangBubbleTipsTimer()
			end,
			other_cfg.bubble_time + server_time, nil, 1)
	else
		self:CleanBianQiangBubbleTipsTimer()
	end
end

function MainuiWGCtrl:CleanBianQiangBubbleTipsTimer()
	local key = "bianqiang_bubble_countdown"
	if CountDownManager.Instance:HasCountDown(key) then
		CountDownManager.Instance:RemoveCountDown(key)
	end

end

function MainuiWGCtrl:ShowBianQiangBubbleTips(is_show)
	if self.view then
		self.view:ShowBianQiangBubbleTips(is_show)
	end
end

function MainuiWGCtrl:FlushRechargeVolume()
	if self.view then
		self.view:FlushRechargeVolume()
	end
end

function MainuiWGCtrl:SetBossTips(is_show)
	if self.view then
		self.view:SetBossTips(is_show)
	end
end

function MainuiWGCtrl:FlushSpeFbSendFlowerMsgHead()
	self:AddInitCallBack(nil,function ()
		return self.view:FlushSpeFbSendFlowerMsgHead()
	end)
end

function MainuiWGCtrl:InitActivityAggregationBtn()
	self:AddInitCallBack(nil,function ()
		return self.view:InitActivityAggregationBtn()
	end)
end

