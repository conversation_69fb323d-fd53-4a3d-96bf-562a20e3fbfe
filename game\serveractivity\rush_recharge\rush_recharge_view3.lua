
RushRechargeViewThree = RushRechargeViewThree or BaseClass(SafeBaseView)

RushRechargeViewThree.GridCol = 5 		-- render数量

function RushRechargeViewThree:__init()
	self.is_modal = true
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(false)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "activity_common_panel_bg")
	self:AddViewResource(0, "uis/view/rush_recharge_ui_prefab", "layout_rush_recharge3")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "activity_common_panel_title")
end

function RushRechargeViewThree:__delete()
end

function RushRechargeViewThree:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("rush_recharge_timer3") then
		CountDownManager.Instance:RemoveCountDown("rush_recharge_timer3")
	end
	if self.recharge_list_view then
		self.recharge_list_view:DeleteMe()
		self.recharge_list_view = nil
	end
	if self.gold_num then
		self.gold_num:DeleteMe()
		self.gold_num = nil
	end

	if self.reward_cell_list then
		for k,v in pairs(self.reward_cell_list) do
			v:DeleteMe()
		end
		self.reward_cell_list = nil
	end
end

function RushRechargeViewThree:LoadCallBack()
	self.node_list["img_title"].image:LoadSprite(ResPath.GetRushRecharge("1"))
	self.recharge_three_index = 1
	for i = 1,5 do
		self.node_list["toggle_"..i].toggle:AddClickListener(BindTool.Bind(self.GridSelectCallBack, self,i))
	end
	self:CreateRewardCells()
	self.node_list.btn_recharge.button:AddClickListener(BindTool.Bind1(self.OnClickRechargeHandler, self))
	self:GridSelectCallBack(1)
end

function RushRechargeViewThree:GetNumberOfCells()
	local data_list = self:GetRenderDataList()
	return #data_list
end

function RushRechargeViewThree:GridSelectCallBack(index)
	self.recharge_three_index = index
	self.data_list = self:GetRenderDataList()
	local item = self.data_list[self.recharge_three_index]
	self:ToUpNumData()
	local reward_cfg = item.reward_item
	local reward_data_list = ServerActivityWGData.Instance:GetShowRewardListByCfg({reward_cfg}, true)
	for k,v in pairs(self.reward_cell_list) do
		v:SetData(reward_data_list[k])
	end
end

function RushRechargeViewThree:ToUpNumData()
	self.data_list = self:GetRenderDataList()
	local select_data = self.data_list[self.recharge_three_index]
	self.node_list.gold_num.image:LoadSprite(ResPath.GetRushRecharge(""..select_data.charge_value))
end

function RushRechargeViewThree:GetRenderDataList()
	local config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local rand_t = config.jishuchongzhan3
	local jishuchongzhan3 = ServerActivityWGData.Instance:GetRandActivityConfig(rand_t, ACTIVITY_TYPE.JISHUCHONGZHAN_3)

	local data_list = {}
	for i,v in ipairs(jishuchongzhan3) do
		if nil == data_list[1] then
			data_list[1] = v
		else
			table.insert(data_list, v)
		end
	end

	return data_list
end

function RushRechargeViewThree:CreateRewardCells()
	if not self.reward_cell_list then
		self.reward_cell_list = {}
		for i=1,5 do
		self.reward_cell_list[i] = ItemCell.New()
		self.reward_cell_list[i]:SetInstanceParent(self.node_list["ph_cell_"..i])	

		end
	end
end

function RushRechargeViewThree:ShowIndexCallBack(index)
	self:Flush(index)
end
	
function RushRechargeViewThree:OpenCallBack()
end

function RushRechargeViewThree:CloseCallBack()
end

function RushRechargeViewThree:OnFlush(param_t, index)
	if CountDownManager.Instance:HasCountDown("rush_recharge_timer3") then
		CountDownManager.Instance:RemoveCountDown("rush_recharge_timer3")
	end
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.JISHUCHONGZHAN_3)
	if nil == activity_info then
		self.node_list.lbl_leave_tips.text.text = ""
		return
	end
	local mul_time = activity_info.next_time - TimeWGCtrl.Instance:GetServerTime()
	if ACTIVITY_STATUS.OPEN == activity_info.status and mul_time > 0 then
		self:UpdateOpenCountDownTime(1, mul_time)
		CountDownManager.Instance:AddCountDown("rush_recharge_timer3", BindTool.Bind1(self.UpdateOpenCountDownTime, self), BindTool.Bind1(self.CompleteOpenCountDownTime, self), activity_info.next_time, nil, 1)
	else
		self:CompleteOpenCountDownTime()
	end
end

function RushRechargeViewThree:UpdateOpenCountDownTime(elapse_time, total_time)
	if total_time - elapse_time > 0 then
		self.node_list.lbl_leave_tips.text.text = TimeUtil.FormatSecondDHM8(total_time - elapse_time)
	end
end

function RushRechargeViewThree:CompleteOpenCountDownTime()
	self.node_list.lbl_leave_tips.text.text = ""
end


--翻向左
function RushRechargeViewThree:OnClickTrunLeft()
	local cur_page = self.recharge_list_view:GetCurPageIndex()
	if cur_page > 1 then
		self.recharge_list_view:ChangeToPage(cur_page - 1)
	end
end

--翻向右
function RushRechargeViewThree:OnClickTrunRight()
	local cur_page = self.recharge_list_view:GetCurPageIndex()
	local page_count = self.recharge_list_view:GetPageCount()
	if cur_page < page_count then
		self.recharge_list_view:ChangeToPage(cur_page + 1)
	end
end

function RushRechargeViewThree:OnClickRechargeHandler()
	local config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local rand_t = config.jishuchongzhan3
	self.jishuchongzhan3 = ServerActivityWGData.Instance:GetRandActivityConfig(rand_t, ACTIVITY_TYPE.JISHUCHONGZHAN_3)
	local item_data = self.jishuchongzhan3[self.recharge_three_index]
	if not item_data then
		return
	end
end