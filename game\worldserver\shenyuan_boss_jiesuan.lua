local pos_tab = {
    [0] = { --两种奖励都有
        ["layout_cells_list1"] = Vector2(0, 30),
        ["layout_cells_list2"] = Vector2(0, -134),
        ["lbl_title_tips"] = Vector2(0, 122),
        ["bottom_parent"] = Vector2(0, -270),
        ["lbl_fb_itemtips"] = Vector2(-3, 60),
        ["lbl_fb_endtime"] = Vector2(0, -100)
    },
    [1] = { --只有参与奖励或者只有归属奖励
        ["layout_cells_list1"] = Vector2(0, -8),
        ["layout_cells_list2"] = Vector2(0, -134),
        ["lbl_title_tips"] = Vector2(0, 91),
        ["bottom_parent"] = Vector2(0, -205),
        ["lbl_fb_itemtips"] = Vector2(-3, 85),
        ["lbl_fb_endtime"] = Vector2(0, -154)
    }

}
local bg_size = {
    [0] = Vector2(0, 427),
    [1] = Vector2(0, 348),
}

local COUNT_DOWN_TIME = 10

ShenyuanBossJieSuanView = ShenyuanBossJieSuanView or BaseClass(SafeBaseView)

function ShenyuanBossJieSuanView:__init()
    self.view_style = ViewStyle.Half
    self.view_name = "ShenyuanBossJieSuanView"
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:SetMaskBg(false, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_jiesuan_panel")
	self:AddViewResource(0, "uis/view/common_jiesuan_prefab", "layout_shenyuan_win")
    self.fb_end_time = 0
end

function ShenyuanBossJieSuanView:__delete()

end

function ShenyuanBossJieSuanView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("shenyuan_boss_close_timer") then
		CountDownManager.Instance:RemoveCountDown("shenyuan_boss_close_timer")
    end
    
    if self.cell_list1 then
		self.cell_list1:DeleteMe()
		self.cell_list1 = nil
    end

    if self.cell_list2 then
		self.cell_list2:DeleteMe()
		self.cell_list2 = nil
    end
    self.fb_end_time = 0
    self.can_get_box = nil
end

function ShenyuanBossJieSuanView:LoadCallBack()
    self.cell_list1 = AsyncListView.New(ShenyuanListItemRender, self.node_list["layout_cells_list1"])
    self.cell_list2 = AsyncListView.New(ShenyuanListItemRender, self.node_list["layout_cells_list2"])
	self.node_list["btn_sure"].button:AddClickListener(BindTool.Bind(self.OnClinkCloseHandler, self))
end

function ShenyuanBossJieSuanView:OpenCallBack()
	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.ShengLi, nil, true))
end

function ShenyuanBossJieSuanView:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("shenyuan_boss_close_timer") then
		CountDownManager.Instance:RemoveCountDown("shenyuan_boss_close_timer")
    end
    if self.can_get_box == 1 then
        local do_gather = function(gather_list, need_move)
            if not IsEmptyTable(gather_list) then
                local scene_logic = Scene.Instance:GetSceneLogic()
                if scene_logic and scene_logic.GetPickedObj then
                    local pick_obj = scene_logic:GetPickedObj()
                    if pick_obj then
                        scene_logic:DoGatherPickObj()
                    end
                end
            end
        end

        local gather_list = Scene.Instance:GetObjListByType(SceneObjType.GatherObj)
        if not IsEmptyTable(gather_list) then
            do_gather(gather_list, true)
        else
            local scene_id = Scene.Instance:GetSceneId()
            local cfg = BossWGData.Instance:GetShenYuanBossCfgSceneId(scene_id)
            if not IsEmptyTable(cfg) and not IsEmptyTable(cfg[1]) then
                GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
                local boss_cfg = cfg[1]
                local gather_pos = Split(boss_cfg.gather_pos, ",")
                local gather_pos_x = tonumber(gather_pos[1])
                local gather_pos_y = tonumber(gather_pos[2])
                GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
                    local gather_list = Scene.Instance:GetObjListByType(SceneObjType.GatherObj)
                    do_gather(gather_list, false)
                end)
                GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), gather_pos_x, gather_pos_y, 1)
            end
        end
    end
end

function ShenyuanBossJieSuanView:ShowIndexCallBack()
    self.node_list.victory:SetActive(true)
	self:Flush()
end


function ShenyuanBossJieSuanView:OnFlush()
    self.only_one = IsEmptyTable(self.owner_reward_list) or IsEmptyTable(self.join_reward_list)
    local idx = self.only_one and 1 or 0
    local tb_pos = pos_tab[idx]
    for k, v in pairs(tb_pos) do
        self.node_list[k].rect.anchoredPosition = v
    end
    -- self.node_list["common_jiesuan_bg"].rect.sizeDelta = (idx == 0 and Vector2(0, 600) or Vector2(0, 500))
    self.node_list.lbl_fb_itemtips:SetActive(true)
    
    if IsEmptyTable(self.owner_reward_list) and IsEmptyTable(self.join_reward_list) then
        self.node_list.lbl_fb_itemtips:SetActive(false)
        self.node_list.lbl_title_tips.text.text = Language.WorldServer.ShenyuanNoTimes
    else
        if self.only_one then --只有一种
            self.cell_list2:SetDataList({})
            local is_only_join = IsEmptyTable(self.owner_reward_list)
            local data_list = is_only_join and self.join_reward_list or self.owner_reward_list
            self.cell_list1:SetDataList(data_list)
            if is_only_join then
                local str = Language.WorldServer.JoinReward
                self.node_list.lbl_title_tips.text.text = string.format(Language.WorldServer.ShenyuanJoinDes, self.rank, str)
            else
                local str = Language.WorldServer.WinDes[self.reward_index]
                local str1 = Language.WorldServer.PercentWinDes[self.reward_index]
                self.node_list.lbl_title_tips.text.text = string.format(Language.WorldServer.ShenyuanWinDes, self.rank, str)--, str1)
            end
        else
            self.cell_list1:SetDataList(self.owner_reward_list)
            self.cell_list2:SetDataList(self.join_reward_list)
            local str = Language.WorldServer.WinDes[self.reward_index]
            local str1 = Language.WorldServer.PercentWinDes[self.reward_index]
            self.node_list.lbl_title_tips.text.text = string.format(Language.WorldServer.ShenyuanWinDes, self.rank, str)--, str1)
        end
    end
    self.node_list.layout_cells_list2:SetActive(not self.only_one)
    self.node_list.canyu_title:SetActive(not self.only_one)
    -- print_error("当前排名",self.rank,"self.reward_index",self.reward_index, IsEmptyTable(self.owner_reward_list) and "无归属奖励" or  "有归属奖励" ,
    -- IsEmptyTable(self.join_reward_list) and "无参与奖励" or  "有参与奖励" )
	if CountDownManager.Instance:HasCountDown("shenyuan_boss_close_timer") then
		CountDownManager.Instance:RemoveCountDown("shenyuan_boss_close_timer")
	end

	if self.fb_end_time > 0 then
		self:UpdateCountDownTime(1, self.fb_end_time)
		CountDownManager.Instance:AddCountDown("shenyuan_boss_close_timer", BindTool.Bind1(self.UpdateCountDownTime, self), BindTool.Bind1(self.CompleteCountDownTime, self), nil, self.fb_end_time, 1)
    else
        self.node_list.lbl_fb_endtime.text.text = ""
    end

	-- local item_data_list = {}
	-- if self.data_list ~= nil and not IsEmptyTable(self.data_list) then
	-- 	for k, v in pairs(self.data_list) do
	-- 		if v.item_id and v.item_id > 0 and v.num > 0 then
    --             item_data_list[#item_data_list + 1] = v
	-- 		end
	-- 	end
    -- end
    -- item_data_list = self:SortDataList(item_data_list)


end


function ShenyuanBossJieSuanView:UpdateCountDownTime(elapse_time, total_time)
	if total_time - elapse_time > 0 then
        if self.node_list.lbl_fb_endtime then
            self.node_list.lbl_fb_endtime.text.text = string.format(Language.GuildBattleRanked.BattleRankedEndTime, math.floor(total_time - elapse_time))
		end
	end
end

function ShenyuanBossJieSuanView:CompleteCountDownTime()
	self:Close()
end

function ShenyuanBossJieSuanView:SetWinData(protocol)
    self.rank = protocol.rank
    self.reward_index = protocol.reward_index --1:完美奖励 2:精良奖励 3:标准奖励
    self.can_get_box = protocol.can_get_box
    local join_reward_list = {}
    for k, v in pairs(protocol.join_reward_list) do
        if v.item_id > 0 then
            join_reward_list[#join_reward_list + 1] = v
        end
    end
    local owner_reward_list = protocol.owner_reward_list
    self.join_reward_list = self:SortDataList(join_reward_list)
    self.owner_reward_list = self:SortDataList(owner_reward_list)
    self.fb_end_time = COUNT_DOWN_TIME
	if not self:IsOpen() then
		self:Open()
	else
		self:Flush()
	end
end

function ShenyuanBossJieSuanView:SortDataList(data_list)
    local list = {}
	if data_list and not IsEmptyTable(data_list) then
        for k,v in pairs(data_list) do
            if v.item_id and v.item_id > 0 and v.num > 0 then
                local temp = {}
                local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(v.item_id)
                temp.is_equip = 0
	            if big_type and big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
                    temp.is_equip = 1
                    temp.sort_star = v.param and v.param.star_level or 0
                else
                    temp.sort_star = 0
                end
                temp.param = v.param
                temp.color = item_cfg and item_cfg.color or 1
                temp.order = item_cfg.order or 0
                temp.item_id = v.item_id
                temp.is_bind = v.is_bind
                temp.num = v.num
                list[#list + 1 ] = temp
            end
		end
		table.sort(list, SortTools.KeyUpperSorters("color","is_equip","sort_star","order"))
    end
    return list
end

function ShenyuanBossJieSuanView:OnClinkCloseHandler()
	self:Close()
end

-----------------------------------------------------------------
----ShenyuanListItemRender
ShenyuanListItemRender = ShenyuanListItemRender or BaseClass(BaseRender)
function ShenyuanListItemRender:__init()
	self.cells = ItemCell.New(self.node_list["item_pos"])
end

function ShenyuanListItemRender:__delete()
	if self.cells then
		self.cells:DeleteMe()
		self.cells = nil
	end
end

function ShenyuanListItemRender:OnFlush()
	if not self.data then return end
	if self.data.item_id > 0 then
		self.cells:SetData(self.data)
		self.cells:SetUselessModalActive(false)
		--self.cells:SetLeftTopImg(self.data.star)
	end
end
