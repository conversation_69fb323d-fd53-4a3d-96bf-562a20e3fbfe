require("game/premium_gift/premium_gift_wg_data")
require("game/premium_gift/premium_gift_view")

PremiumGiftWGCtrl = PremiumGiftWGCtrl or BaseClass(BaseWGCtrl)

function PremiumGiftWGCtrl:__init()
	if PremiumGiftWGCtrl.Instance ~= nil then
		print_error("[PremiumGiftWGCtrl] attempt to create singleton twice!")
		return
	end

	PremiumGiftWGCtrl.Instance = self
	self.data = PremiumGiftWGData.New()
    self.view = PremiumGiftView.New(GuideModuleName.PremiumGiftView)

    self:RegisterProtocol(SCOAOverflowGiftInfo, "OnSCOAOverflowGiftInfo")
    self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.OnDayChange, self))
end

function PremiumGiftWGCtrl:__delete()
	PremiumGiftWGCtrl.Instance = nil

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end
end

function PremiumGiftWGCtrl:SendRechargeInfo(act_type, opera_type, param_1, param_2, param_3, param_4)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
	protocol.rand_activity_type = act_type
	protocol.opera_type = opera_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol.param_4 = param_4 or 0
	protocol:EncodeAndSend()
end

function PremiumGiftWGCtrl:OnSCOAOverflowGiftInfo(protocol)
	self.data:SetExtinctGiftInfo(protocol)
	RemindManager.Instance:Fire(RemindName.PremiumGift)

	if self.view:IsOpen() then
		self.view:Flush()
	end
end

function PremiumGiftWGCtrl:OnClickExtinctGiftTab(jump_index)
	if self.view:IsOpen() then
		self.view:OnClickSwitch(jump_index)
	end
end

function PremiumGiftWGCtrl:OnDayChange()
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.PREMIUM_GIFT) then
		PremiumGiftWGCtrl.Instance:SendRechargeInfo(ACTIVITY_TYPE.PREMIUM_GIFT, EXTINCT_GIFT_OPERATE_TYPE.INFO)
	end
end