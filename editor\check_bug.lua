local CheckBug = {}
local typeGraphic = typeof(UnityEngine.UI.Graphic)

function CheckBug:OnLuaCall(event, ...)
	local params = {...}

	if event == "push_gameobj_pool" then
		self:OnPushGameObjPool(params[1], params[2])
	end
end

function CheckBug:OnPushGameObjPool(pool, gameobj)
	local graphics = gameobj:GetComponentsInChildren(typeGraphic, true)
	for i = 0, graphics.Length - 1 do
		local graphic = graphics[i]
		if nil ~= graphic.material and graphic.material == ResPoolMgr:TryGetMaterial("misc/material", "UI_NormalGrey") then
			print_error("监测到你回池的对象置灰没有取消，可能是对象池中的对象或者其父节点使用了UI:SetButtonEnabled或UI:SetGraphicGrey 没做取消处理！不懂问主程！！！", gameobj.name)
			GameRoot.AddLuaWarning("【严重Bug】使用了对象池，却没有把置灰效果取消。将引起严重问题，立刻找主程！！！(查看控制台日志)", "High")
		end
	end
end

return CheckBug