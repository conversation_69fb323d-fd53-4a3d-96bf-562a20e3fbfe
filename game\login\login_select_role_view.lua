local role_cell_NUM = 4
local ROLEPOS_ROT_Y =
{
	[0] = {
		[1] = 165,
		[2] = 180,
		[3] = 165,
	},

	[1] = {
		[1] = -200,
		[3] = -200,
	},
}

local ROLE_POS = {
	[0] = {
		[1] = Vector3(-0.3, 0.3, -1.5),
		[2] = Vector3(0, 0.25, -1.5),
		[3] = Vector3(-0.4, 0.25, -1.5),
	},

	[1] = {
		[1] = Vector3(-0.7, 0.2, -2),
		[3] = Vector3(-0.9, 0.25, -2.3),
	},
}

local CAMERA_POS = Vector3(0, 2.25, -3.2)

function LoginView:Init__SelectRole()
	self.select_role_item_click_time = 0
	self.last_select_role_item_index = 0
	self.select_role_id = 0
end

function LoginView:ReleaseCallBack__SelectRole()
	if self.select_role_cell_list then
		for k,v in pairs(self.select_role_cell_list) do
			v:DeleteMe()
		end
	end
	self.select_role_cell_list = nil

	self:DestroyDrawObj()
end

function LoginView:LoadCallBack__SelectRole()
	self.select_role_item_click_time = 0
	self.last_select_role_item_index = 0
	self.select_role_id = 0
	self.select_role_rotate_cache = {x = 0, y = 0, z = 0}

    self.select_role_cell_list = {}
	local role_cell
	for i = 1, role_cell_NUM do
		role_cell = SelectRoleItem.New(self.node_list["role_item" .. i])
		role_cell:AddClickEventListener(BindTool.Bind(self.OnClickContractHandler, self, i))
		role_cell:SetIndex(i)
		role_cell:IsOpen(i <= 2)

		self.select_role_cell_list[i] = role_cell
	end

	-- 选择角色旋转区域
	local event_trigger = self.node_list["select_role_event_trigger"]:GetComponent(typeof(EventTriggerListener))
	event_trigger:AddDragListener(BindTool.Bind(self.OnSelectRoleModelDrag, self))

	self.node_list["open_adventure_btn"].button:AddClickListener(BindTool.Bind(self.OnClickOpenAdventure, self))
end

-- 切换选择角色面板
function LoginView:OnChangeToSelectRole()
	if not self:IsOpen() then
        return
    end

    PlayerPrefsUtil.SetString("agree_abide_protocol", "1")
    self.view_show_status = LoginViewStatus.SelectRole
    self:ChangeViewShowStatus()
    self:Flush(nil, "flush_select_role_view")
end

function LoginView:GetSelectRoleId()
	return self.select_role_id
end

function LoginView:SetSelectRoleId(select_role_id)
	self.select_role_id = select_role_id
end

-- 刷新选择角色面板
function LoginView:FlushSelectRoleView()
	local role_list_ack_info = LoginWGData.Instance:GetRoleListAck()
	local temp_role_list = __TableCopy(role_list_ack_info.role_list)
	-- table.sort(temp_role_list, SortTools.KeyUpperSorter("last_login_time"))
	if role_list_ack_info.count < 3 then
		local plus_sign = {
			role_id = -9999,
			role_name = Language.Login.CreateRole,
			avatar = 0,
			sex = 0,
			prof = 0,
			country = 0,
			level = 0,
			create_time = 0,
			online_time = 0,
			last_logout_time = 0,
			capability = 0,
			-- avatar_timestamp = 0,
			avatar_key_big = 0,
			avatar_key_small = 0
		}
		table.insert(temp_role_list, plus_sign)
	end
	self.select_role_listview_data = temp_role_list

	local select_role_state = UtilU3d.GetCacheData("select_role_state")
	if select_role_state == 1 then
		UtilU3d.CacheData("select_role_state", 0)
		InitWGCtrl:HideLoading()
	end

	local uservo = GameVoManager.Instance:GetUserVo()
	local up_login = 1
	local up_name = PlayerPrefsUtil.GetString("login_account_name")
	if up_name ~= nil and uservo.account_user_id == up_name then
		if PlayerPrefsUtil.GetString("SAVE_UP_LOGIN") ~= nil then
			up_login = tonumber(PlayerPrefsUtil.GetString("SAVE_UP_LOGIN"))
			if up_login == nil or up_login > role_list_ack_info.count then
				up_login = 1
			end
		end
	end

	for k,v in pairs(self.select_role_cell_list) do
		v:SetData(self.select_role_listview_data[k])
	end

	if self.select_role_cell_list[up_login] then
		self:OnClickContractHandler(up_login, self.select_role_cell_list[up_login])
	end
end

function LoginView:ResetSelectBeforeParam()
	self.last_select_role_item_index = nil
end

-- 列表选择回调函数处理
function LoginView:OnClickContractHandler(index, cell)
	if not cell or not cell.data then
		return
	end

    local now_time = Status.NowTime
	if now_time - self.select_role_item_click_time < 1 or self.last_select_role_item_index == index then
		return
	end

	for k,v in pairs(self.select_role_cell_list) do
		v:OnSelectChange(index == k)
	end

	self.select_role_item_click_time = now_time
	self.last_select_role_item_index = index

	local data = cell.data
	if data.role_id > 0 then
		self.select_role_rotate_cache = {x = 0, y = 0, z = 0}
		self:SetSelectRoleId(data.role_id)
		self:FlushSelectRoleModel(data)

		local user_vo = GameVoManager.Instance:GetUserVo()
		user_vo:SetNowRole(data.role_id)
		local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
		mainrole_vo.name = data.role_name
		self:ChangeRight(data.prof % 10, data.sex)
	else
		self:OnChangeToCreate()
	end
end

function LoginView:GetDrawObj(parent)
	if nil == self.draw_obj then
		local pos_node = UnityEngine.GameObject.Find("RolePos")
		if nil == parent and pos_node then
			parent = pos_node.transform
		end

		self.draw_obj = DrawObj.New(self, parent)
		self.draw_obj:SetIsInQueueLoad(false)
		self.draw_obj:SetIsUseObjPool(true)
		self.draw_obj:SetIsHdTexture(true)
		self.draw_obj:SetIsCanWeaponPointAttach(false)
		self.draw_obj:SetIsNeedSyncAnim(true)
        self.draw_obj:ChangeWaitSyncAnimType(SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.ROLE)
	end

	return self.draw_obj
end

function LoginView:DestroyDrawObj()
	if self.draw_obj ~= nil then
		self.draw_obj:DeleteMe()
		self.draw_obj = nil
	end
end

function LoginView:FlushSelectRoleModel(data)
	local prof = data.prof % 10
	local sex = data.sex

	self:HideAllCGInstance()
	self:ClearCGHander()
	self:DestroyDrawObj()
	self:ChangeScene(chuangjue_bundle, chuangjue_asset, function()
		local center = UnityEngine.GameObject.Find("RolePos")
		if not center then
			return
		end

		-- 设置选择已创建角色选择界面模型旋转
		-- local rolepos_rot_y = sex == GameEnum.MALE and 200 or 200
		--2023.05.13 策划自己调的值
		--2024.7.26 策划调整
		local rolepos_rot_y = ROLEPOS_ROT_Y[sex][prof]
		center.transform.localRotation = Quaternion.Euler(0, rolepos_rot_y, 0)
		local camera_obj = UnityEngine.GameObject.Find("Camera")
		if camera_obj then
			camera_obj.transform.localPosition = CAMERA_POS
		end

		local draw_obj = self:GetDrawObj()
		local role_pos = ROLE_POS[sex][prof]
		draw_obj.root.transform.localPosition = role_pos
		draw_obj.root.transform.localRotation = Quaternion.identity
		-- 主角
		local main_part = draw_obj:GetPart(SceneObjPart.Main)
		main_part:RemoveModel()
		-- 改为CastShadow是为了产生倒影
		main_part:SetIsCastShadow(true)
		local appe_data = data.appearance
		local resouce = appe_data.fashion_body
		local role_diy_appearance_data = data.role_diy_appearance
		local bundle, asset = ResPath.GetRoleModel(RoleWGData.GetJobModelId(sex, prof))

		local def_hair_res = RoleWGData.GetPartModelRes(ROLE_SKIN_TYPE.HAIR, appe_data.default_hair_res_id, sex, prof)
		local body_res, face_res, hair_res = RoleWGData.GetShowRoleSkinPartRes(sex, prof, resouce,
								appe_data.default_body_res_id, appe_data.default_face_res_id, appe_data.default_hair_res_id)

		local preset_diy_cfg = RoleDiyAppearanceWGData.Instance:GetPresetDiyCfgBySexAndProf(sex, prof, role_diy_appearance_data.preset_seq or 1)

		local extra_model_data = {
			sex = sex,
			prof = prof,

			role_body_res = body_res,
			role_face_res = face_res,
			role_hair_res = hair_res,

			eye_size = role_diy_appearance_data.eye_size,
            eye_position = role_diy_appearance_data.eye_position,
            eye_shadow_color = role_diy_appearance_data.eye_shadow_color,

            left_pupil_type = role_diy_appearance_data.left_pupil_type,
            left_pupil_size = role_diy_appearance_data.left_pupil_size,
            left_pupil_color = role_diy_appearance_data.left_pupil_color,

            right_pupil_type = role_diy_appearance_data.right_pupil_type,
            right_pupil_size = role_diy_appearance_data.right_pupil_size,
            right_pupil_color = role_diy_appearance_data.right_pupil_color,

            mouth_size = role_diy_appearance_data.mouth_size,
            mouth_position = role_diy_appearance_data.mouth_position,
            mouth_color = role_diy_appearance_data.mouth_color,

            face_decal_id = role_diy_appearance_data.face_decal_id,
            hair_color = role_diy_appearance_data.hair_color or nil,

			eye_angle = preset_diy_cfg and preset_diy_cfg.eye_angle,
			eye_close = preset_diy_cfg and preset_diy_cfg.eye_close,
			eyebrow_angle = preset_diy_cfg and preset_diy_cfg.eyebrow_angle,
			nose_size = preset_diy_cfg and preset_diy_cfg.nose_size,
			nose_angle = preset_diy_cfg and preset_diy_cfg.nose_angle,
			mouth_angle = preset_diy_cfg and preset_diy_cfg.mouth_angle,
			cheek_size = preset_diy_cfg and preset_diy_cfg.cheek_size,
			chin_length = preset_diy_cfg and preset_diy_cfg.chin_length,
		}

		local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByResId(SHIZHUANG_TYPE.BODY, resouce)
	
		if fashion_cfg then
			local is_open_dye = fashion_cfg and fashion_cfg.is_open_dyeing or 0
			local is_show_dye = is_open_dye == 1
			-- 这里加了一个操作，时装标记染色才会使用创角编辑的头发颜色，
			-- 没有染色的直接使用默认材质色，不适用编辑捏脸头发色
			if not is_show_dye then
				extra_model_data.hair_color = nil
			end
		end

		draw_obj:ChangeModel(SceneObjPart.Main, bundle, asset, function()
			self:FakeHideDelayShow()
		end, DRAW_MODEL_TYPE.ROLE, extra_model_data, function(skin_type)
			self:FlushPartColor(skin_type, appe_data, body_res, face_res, hair_res)
		end)
		draw_obj:CrossAction(SceneObjPart.Main, SceneObjAnimator.UiIdle)

		self:ShowSelectRolePartModel(data)
	end)
end

function LoginView:FlushPartColor(skin_type, appe_data, body_res, face_res, hair_res)
    if not appe_data then
        return
    end

    local part_color = appe_data.part_color
    if part_color == nil or IsEmptyTable(part_color) then
        return
    end

    -- 获取染色配置
    local image_cfg = NewAppearanceWGData.Instance:GetFashionCfgByProtocolIndex(SHIZHUANG_TYPE.BODY, appe_data.shizhuang_body)
    if not image_cfg then
        return
    end

    local fashion_dye_cfg = NewAppearanceDyeWGData.Instance:GetConsumeCfgByIndex(image_cfg.index, image_cfg.part_type)
    local main_part = self:GetDrawObj():GetPart(SceneObjPart.Main)
    if (not fashion_dye_cfg) or (main_part == nil) then
        return
    end

    local skin = main_part:GetChangeSkinComponent()
    if skin == nil then
        return
    end

    local server_project_index = appe_data.shizhuang_project_id
    local use_project_index = server_project_index + 1

    if not self.project_cache_data then
		self.project_cache_data = {}
	end

    local change_body_dye_color_fun = function()
        local dye_color_table = {}
        for show_part, color_data in ipairs(part_color) do
            local dye_index_list_data = NewAppearanceDyeWGData.Instance:GetDyeIndexListBySeqPart(fashion_dye_cfg.seq, show_part)
            local index_list = dye_index_list_data and dye_index_list_data.dye_index_list
    
            local color = nil
            if color_data.r ~= 0 or color_data.g ~= 0 or color_data.b ~= 0 or color_data.a ~= 0 then
                color = Color.New(color_data.r / 255, color_data.g / 255, color_data.b / 255, color_data.a / 255)
            end
    
            if show_part ~= HAIR_PART then
                if index_list and color then
                    for _, dye_index in ipairs(index_list) do
                        local dye_color_data = {}
                        dye_color_data.dye_index = dye_index
                        dye_color_data.r = color.r
                        dye_color_data.g = color.g
                        dye_color_data.b = color.b
                        dye_color_data.a = color.a
                        table.insert(dye_color_table, dye_color_data)
                    end
                end
            end
        end
    
        if not IsEmptyTable(dye_color_table) then
            skin:BatchSetPartDyeColor(cjson.encode({dye_list = dye_color_table}))
        end
    end

    local change_hair_dye_color_fun = function()
        local color_data = part_color[HAIR_PART]
        if color_data and (color_data.r ~= 0 or color_data.g ~= 0 or color_data.b ~= 0 or color_data.a ~= 0) then
            main_part:UpdateChangeExtraModelData(HeadCustomizationType.HairColor, color_data)
            main_part:ChangeSkinHeadMatColorByRGBATable(HeadCustomizationType.HairColor, color_data)
        end
    end

    if skin_type == ROLE_SKIN_TYPE.BODY then
        local body_material_id = NewAppearanceDyeWGData.Instance:GetProjectBodyIdCfgBySeq(fashion_dye_cfg.seq, use_project_index)
        if body_material_id ~= 0 and body_res ~= appe_data.default_body_res_id then
            main_part:ChangeRoleProjectMaterials(ROLE_SKIN_TYPE.BODY, body_res, body_material_id, change_body_dye_color_fun)
            self.project_cache_data.body_id = body_material_id
        else
            if self.project_cache_data.body_id and self.project_cache_data.body_id ~= 0 then		-- 有发生改变才会重置
                self.project_cache_data.body_id = 0
                main_part:ResetRoleProjectMaterials(ROLE_SKIN_TYPE.BODY)
            end
            change_body_dye_color_fun()
        end
    elseif skin_type == ROLE_SKIN_TYPE.HAIR then
        local hair_material_id = NewAppearanceDyeWGData.Instance:GetProjectHairIdCfgBySeq(fashion_dye_cfg.seq, use_project_index)
        if hair_material_id ~= 0 and hair_res ~= appe_data.default_hair_res_id then
            main_part:ChangeRoleProjectMaterials(ROLE_SKIN_TYPE.HAIR, hair_res, hair_material_id)
            self.project_cache_data.hair_id = hair_material_id
        else
            if self.project_cache_data.hair_id and self.project_cache_data.hair_id ~= 0 then		-- 有发生改变才会重置
                self.project_cache_data.hair_id = 0
                main_part:ResetRoleProjectMaterials(ROLE_SKIN_TYPE.HAIR)
            end
        end
    elseif skin_type == ROLE_SKIN_TYPE.FACE then
        local face_material_id = NewAppearanceDyeWGData.Instance:GetProjectFaceIdCfgBySeq(fashion_dye_cfg.seq, use_project_index)
        if face_material_id ~= 0 and face_res ~= appe_data.default_face_res_id then
            main_part:ChangeRoleProjectMaterials(ROLE_SKIN_TYPE.FACE, face_res, face_material_id, change_hair_dye_color_fun)
            self.project_cache_data.face_id = face_material_id
        else
            if self.project_cache_data.face_id and self.project_cache_data.face_id ~= 0 then		-- 有发生改变才会重置
                self.project_cache_data.face_id = 0
                main_part:ResetRoleProjectMaterials(ROLE_SKIN_TYPE.FACE)
            end

            change_hair_dye_color_fun()
        end
    end
end

-- 加载部位模型
function LoginView:ShowSelectRolePartModel(data)
	local draw_obj = self:GetDrawObj()
	if draw_obj == nil or draw_obj:IsDeleted() then
		return
	end

	local appearance_data = data.appearance
	local prof = data.prof % 10
	local sex = data.sex

	-- 武器
	local weapon_res_id = 0
	if nil ~= data.shenwu_appeid and 0 ~= data.shenwu_appeid
		or nil ~= appearance_data.fashion_wuqi and 0 ~= appearance_data.fashion_wuqi
		then
		local resource = (nil ~= data.shenwu_appeid and 0 ~= data.shenwu_appeid) and data.shenwu_appeid or appearance_data.fashion_wuqi
		weapon_res_id = RoleWGData.GetFashionWeaponId(sex, prof, resource)
	else
		weapon_res_id = RoleWGData.GetJobWeaponId(sex, prof)
	end

	local wepapon_part = draw_obj:GetPart(SceneObjPart.Weapon)
	wepapon_part:SetIsCastShadow(true)
	local weapon_bundle, weapon_asset = ResPath.GetWeaponModelRes(weapon_res_id)
	draw_obj:ChangeModel(SceneObjPart.Weapon, weapon_bundle, weapon_asset)
	
	-- 羽翼
	local wing_res_id = appearance_data.wing_appeid
	if wing_res_id > 0 then
		local wing_part = draw_obj:GetPart(SceneObjPart.Wing)
		wing_part:SetBool("is_scene", true)
		wing_part:SetIsCastShadow(true)
		wing_part:ChangeModel(ResPath.GetWingModel(wing_res_id))
	end

	-- -- 剑灵
	-- local jianling_res_id = appearance_data.jianzhen_appeid
	-- if jianling_res_id > 0 then
	-- 	local jianling_part = draw_obj:GetPart(SceneObjPart.Jianling)
	-- 	jianling_part:SetIsCastShadow(true)
	-- 	jianling_part:ChangeModel(ResPath.GetJianZhenModel(jianling_res_id))
	-- end

	-- 光环
	-- local halo_res_id = appearance_data.fashion_guanghuan
	-- if halo_res_id > 0 then
	-- 	local halo_part = draw_obj:GetPart(SceneObjPart.Halo)
	-- 	halo_part:SetIsCastShadow(true)
	-- 	halo_part:ChangeModel(ResPath.GetHaloModel(halo_res_id))
	-- end

	-- 腰饰
	local waist_res_id = appearance_data.waist
	if waist_res_id > 0 then
		local waist_part = draw_obj:GetPart(SceneObjPart.Waist)
		waist_part:SetIsCastShadow(true)
		waist_part:ChangeModel(ResPath.GetBeltModel(waist_res_id))
	end

	-- 面饰
	local mask_res_id = appearance_data.mask
	if mask_res_id > 0 then
		local mask_part = draw_obj:GetPart(SceneObjPart.Mask)
		mask_part:SetIsCastShadow(true)
		mask_part:ChangeModel(ResPath.GetMaskModel(mask_res_id))
	end

	-- 手环
	local shouhuan_res_id = appearance_data.shouhuan
	if shouhuan_res_id > 0 then
		local shouhuan_part = draw_obj:GetPart(SceneObjPart.ShouHuan)
		shouhuan_part:SetIsCastShadow(true)
		shouhuan_part:ChangeModel(ResPath.GetShouhuanModel(shouhuan_res_id))
	end

	-- 尾巴
	local tail_res_id = appearance_data.tail
	if tail_res_id > 0 then
		local tail_part = draw_obj:GetPart(SceneObjPart.Tail)
		tail_part:SetIsCastShadow(true)
		tail_part:ChangeModel(ResPath.GetWeibaModel(tail_res_id))
	end

	-- 法宝
	local fabao_res_id = appearance_data.fabao_appeid
	if fabao_res_id > 0 then
		local fabao_part = draw_obj:GetPart(SceneObjPart.BaoJu)
		fabao_part:SetIsCastShadow(true)
		fabao_part:ChangeModel(ResPath.GetFaBaoModel(fabao_res_id))
	end
end

function LoginView:FakeHideDelayShow()
	local draw_obj = self:GetDrawObj()
	if not draw_obj then
		return
	end

	draw_obj:SetScale(0, 0, 0)
	GlobalTimerQuest:AddDelayTimer(function ()
		if not draw_obj then
			return
		end

		draw_obj:TryResetScale()
	end, 0.25)
end

-- 选择角色被拖转动事件
function LoginView:OnSelectRoleModelDrag(data)
	local draw_obj = self:GetDrawObj()
	if draw_obj and draw_obj.root then
		local cache = self.select_role_rotate_cache
		self.select_role_rotate_cache = {x = cache.x, y = -data.delta.x * 0.25 + cache.y, z = cache.z}
		draw_obj.root.transform.localRotation = Quaternion.Euler(
											self.select_role_rotate_cache.x,
											self.select_role_rotate_cache.y,
											self.select_role_rotate_cache.z)
	end
end

-- 开启冒险
function LoginView:OnClickOpenAdventure()
	local select_role_id = self:GetSelectRoleId()
	if select_role_id > 0 then
		LoginWGData.Instance:SetCurrSelectRoleId(select_role_id)
		Scene.Instance:OpenSceneLoading()
		local uservo = GameVoManager.Instance:GetUserVo()
		uservo:SetNowRole(select_role_id)
		LoginWGCtrl.SendRoleReq()
		ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.clickLoginEnterGame)
	else
		self:OnChangeToCreate()
	end
end

---------------------------------
-- 选择多人角色Item
---------------------------------
SelectRoleItem = SelectRoleItem or BaseClass(BaseRender)
function SelectRoleItem:__init()
	self.is_open = false
	self.role_name = self.node_list["role_name"]
	self.role_level = self.node_list["role_level"]
	self.role_add = self.node_list["role_add"]
end

function SelectRoleItem:ReleaseCallBack()
	self.is_open = false
end

function SelectRoleItem:IsOpen(is_open)
	if nil == is_open then
		return self.is_open
	end

	self.is_open = is_open
end

function SelectRoleItem:OnFlush()
	self.node_list["mask_btn"]:SetActive(not self.is_open)
	self.node_list["img_role"]:SetActive(self.is_open)
	self.node_list["no_role_bg"]:SetActive(not self.is_open)
	if not self.data then
		return
	end

    if not self.is_open then
        self.role_add:SetActive(false)
        self.node_list["role_info"]:SetActive(false)
        self.node_list["default_head_icon"]:SetActive(false)
        self.node_list["custom_head_icon"]:SetActive(false)
		self.node_list["normal_bg"]:SetActive(false)
		self.node_list["select_bg"]:SetActive(false)
        return
    end

	local has_role = self.data.role_id > 0
	self.role_add:SetActive(not has_role)
	self.node_list["normal_bg"]:SetActive(not has_role)
	self.node_list["role_info"]:SetActive(has_role)
	self.node_list["img_role"]:SetActive(has_role)
	

	if has_role then
		XUI.UpdateMainRoleHead(self.data.avatar_key_big,
								self.data.avatar_key_small,
		 					   	self.node_list["default_head_icon"],
		 					   	self.node_list["custom_head_icon"],
		 					   	self.data.role_id,
                                self.data.sex,
                                self.data.prof,
		 					   	nil,
		 					   	false,
		 					   	true)
		--self.node_list["default_head_icon"].image.rectTransform.sizeDelta = Vector2.New(120, 120)
		self.role_name.text.text = self.data.role_name
    else
        self.node_list["default_head_icon"]:SetActive(false)
        self.node_list["custom_head_icon"]:SetActive(false)
	end
    
	self:SetRoleLevel(self.data.level)
end

function SelectRoleItem:SetRoleLevel(value)
	local is_vis, level = RoleWGData.Instance:GetDianFengLevel(value)

	local str = level
	if not is_vis then
		str = string.format("Lv.%s", level)
	else
		str = string.format(Language.Role.FeiXianDesc3, level)
	end

	self.role_level.text.text = str
end

function SelectRoleItem:OnSelectChange(is_select)
    if not self.data or not self.is_open then
		return
	end

	local has_role = self.data.role_id > 0

    self.node_list["normal_bg"]:SetActive(not is_select and has_role)
    self.node_list["no_role_bg"]:SetActive(not is_select and not has_role)
    self.node_list["select_bg"]:SetActive(is_select and has_role)
end


