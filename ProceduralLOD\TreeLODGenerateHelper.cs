#if UNITY_EDITOR
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace ProceduralLOD
{
    public class TreeLODGenerateHelper : LODGenerateHelper
    {
        public static readonly string BillboardTreeShader = "JYShaders/StylizedTreeBillboard";
        
        protected override float DefaultHeight(float size, int level)
        {
            float height;

            switch (level)
            {
                case 0:
                    height = (0.1f * size + 42f) * (1f - Mathf.Pow((float)Math.E, -0.05f * size));
                    break;
                case 1:
                    height = (0.1f * size + 12) * (1f - Mathf.Pow((float)Math.E, -0.05f * size));
                    break;
                case 2:
                    height = 0.2f;
                    break;
                default:
                    height = 0.001f;
                    break;
            }

            return height;
        }
        
        protected override int SimplifiedLevels(float size, int triangles)
        {
            if (size > 30)
            {
                if (triangles > 3000)
                {
                    return 2;
                }
            }
            return 1;
        }

        protected override LODObject PrepareLODObject(int lodIndex, GameObject gameObject, Renderer[] rawRenderers)
        {
            if (lodIndex == reductionStrengths.Length)
            {
                LODObject lodObject = new LODObject(lodIndex, gameObject, rawRenderers);
                GameObject batchObj = new GameObject("Billboard");
                batchObj.transform.SetParent(lodObject.gameObject.transform);
                MeshRenderer[] renderers = new MeshRenderer[1];
                MeshRenderer renderer = batchObj.AddComponent<MeshRenderer>();
                MeshFilter meshFilter = batchObj.AddComponent<MeshFilter>();
                renderer.sharedMaterial = new Material(Shader.Find(BillboardTreeShader));
                meshFilter.sharedMesh = Resources.GetBuiltinResource<Mesh>("Quad.fbx");
                renderers[0] = renderer;

                GameObject sample = CommonUtility.CreateSampleObject(gameObject, rawRenderers);
                
                Bounds worldBounds = LODMaterialUtility.BillboardCaptureBounds(rawRenderers, out _);
                Bounds sampleBounds = LODMaterialUtility.BillboardCaptureBounds(sample.GetComponentsInChildren<Renderer>(), out float sampleBoundsMaxSize);
                batchObj.transform.position = worldBounds.center;
                batchObj.transform.localRotation = Quaternion.identity;
                batchObj.transform.localScale = new Vector3(sampleBoundsMaxSize, sampleBoundsMaxSize, sampleBoundsMaxSize);
                DestroyImmediate(sample);
                
                lodObject.batches.SetBatches(renderer);
                
                return lodObject;
            }
            return base.PrepareLODObject(lodIndex, gameObject, rawRenderers);
        }
        
        protected override IEnumerator SimplifyChidren(LODObject lodObject)
        {
            if (lodObject.index != reductionStrengths.Length)
                yield return base.SimplifyChidren(lodObject);
        }
        
        protected override bool SimplifyMaterial(int index, Material sourceMaterial, out Material lodMaterial)
        {
            if (index == reductionStrengths.Length)
            {
                lodMaterial = LODMaterialUtility.MakeMaterialBillboard(this.gameObject,  Shader.Find(BillboardTreeShader));
                return true;
            }
            else
            {
                return base.SimplifyMaterial(index, sourceMaterial, out lodMaterial);
            }
        }

        
    }
}

#endif