ACTRewardListShowView = ACTRewardListShowView or BaseClass(SafeBaseView)

function ACTRewardListShowView:__init()
	self:SetMaskBg(true)
	self.view_name = "ACTRewardListShowView"
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "layout_item_list_show")
	self.close_tween = nil
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
end

function ACTRewardListShowView:LoadCallBack()
    if not self.ph_item_list then
        self.ph_item_list = AsyncListView.New(ItemCell, self.node_list.ph_item_list)
    end
end

function ACTRewardListShowView:ReleaseCallBack()
    if self.ph_item_list then
        self.ph_item_list:DeleteMe()
        self.ph_item_list = nil
    end

    self.title = nil
	self.item_list = nil
end

function ACTRewardListShowView:SetData(title, item_list)
    self.title = title
	self.item_list = item_list
end

function ACTRewardListShowView:OnFlush()
    if nil == self.title or IsEmptyTable(self.item_list) then
        return
    end

    self.node_list.title.text.text = self.title
    self.ph_item_list:SetDataList(self.item_list)
end