-------------------------------- 注意事项 --------------------------------
--修改此代码必须经过主程同意
-------------------------------------------------------------------------

local M = {}

local config =
{
	{fps = 50, max_download_count = 10, max_download_speed = 5000 * 1024, max_write_size = 200 * 1024},
	{fps = 40, max_download_count = 7, max_download_speed = 2000 * 1024, max_write_size = 100 * 1024},
	{fps = 30, max_download_count = 5, max_download_speed = 1000 * 1024, max_write_size = 50 * 1024},
	{fps = 25, max_download_count = 5, max_download_speed = 500 * 1024, max_write_size = 20 * 1024},
	{fps = 20, max_download_count = 3, max_download_speed = 200 * 1024, max_write_size = 15 * 1024},
	{fps = 15, max_download_count = 2, max_download_speed = 100 * 1024, max_write_size = 10 * 1024},
	{fps = 0, max_download_count = 1, max_download_speed = 50 * 1024, max_write_size = 5 * 1024},
}

function M.Init()
	local fpsSampler = GameRoot.Instance:GetFPSSampler()
	if fpsSampler then
		fpsSampler.FPSEvent = fpsSampler.FPSEvent + M.FpsCallBack
	end
	M.cur_cfg = config[1]
end

function M.Delete()
	local fpsSampler = GameRoot.Instance:GetFPSSampler()
	if fpsSampler then
		fpsSampler.FPSEvent = fpsSampler.FPSEvent - M.FpsCallBack
	end
end

function M.FpsCallBack(fps)
	fps = math.max(0, fps)
	for k,v in ipairs(config) do
		if fps >= v.fps then
			M.cur_cfg = v
			break
		end
	end
end

function M.GetCurConfig()
	return M.cur_cfg
end

function M.GetMaxDownloadCount()
	return M.cur_cfg.max_download_count
end

function M.GetMaxDownloadSpeed()
	return M.cur_cfg.max_download_speed
end

function M.GetMaxWriteSize()
	return M.cur_cfg.max_write_size
end

return M