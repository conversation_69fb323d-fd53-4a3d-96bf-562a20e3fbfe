﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class FeatureTest : MonoBehaviour
{
    private Material material;
    private void Awake()
    {
        Renderer renderer = GetComponent<Renderer>();
        material = renderer.sharedMaterial;
    }

    private void OnGUI()
    {
        if (GUILayout.Button("Enable"))
        {
            material.EnableKeyword("ENABLE_CLIP_RECT");
        }

        if (GUILayout.Button("DisEnable"))
        {
            material.DisableKeyword("ENABLE_CLIP_RECT");
        }
    }
}
