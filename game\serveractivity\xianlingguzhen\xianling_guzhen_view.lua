
XianLingGuZhenView = XianLingGuZhenView or BaseClass(SafeBaseView)

local check_item_bg_res_list = {
		[5] = true,
		[6] = true,
		[8] = true,
	}

local LuckyDrawType = {
	SinglePumping = 1,
	TenContinuousPumping = 10,
}

--模型类型定义
local XLGZ_MODEL_TYPE = {
	Spc_Type = 0,--界面专用类型
	Mount = 1,
	Pet = 2,
}

-- local REWARD_MAX_COUNT = 8
function XianLingGuZhenView:__init()
	self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Half
	self.enough_num = false
	self.enough_gold = false
	self.enough_num_ten = false
	self.enough_gold_ten = false
	self.time_require = true
	self.click_roll_seq = -1
	self.is_safe_area_adapter = true
	self.default_index = 10
	self:SetMaskBg()
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
	self:AddViewResource(0, "uis/view/xianling_guzhen_prefab", "layout_xianling_guzhen")
	self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar_Activity")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function XianLingGuZhenView:__delete()

end

function XianLingGuZhenView:ReleaseCallBack()
	if self.xianling_list then
		self.xianling_list:DeleteMe()
		self.xianling_list = nil
	end

	if self.record_list then
		self.record_list:DeleteMe()
		self.record_list = nil
	end

	if self.tween then
		self.tween:Kill()
		self.tween = nil
	end

	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end

	if self.cur_turn_time_quest then
		GlobalTimerQuest:CancelQuest(self.cur_turn_time_quest)
		self.cur_turn_time_quest = nil
	end

	self.last_show_item_id = nil

	if self.reward_model then
		self.reward_model:DeleteMe()
		self.reward_model = nil
	end

	if nil ~= self.big_reward_list then
        for k,v in pairs(self.big_reward_list) do
            v:DeleteMe()
        end
        self.big_reward_list = nil
    end

	self.item_reset_pos_list = nil
	self.model_change_tween_sequence = nil
	self.cur_show_model_id = nil
	--self:KillRewardModelYoyoTween()

	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_change_callback)
	self.item_change_callback = nil

	if self.show_next_quest then
		GlobalTimerQuest:CancelQuest(self.show_next_quest)
		self.show_next_quest = nil
	end

	if self.require_info_delay then
		GlobalTimerQuest:CancelQuest(self.require_info_delay)
		self.require_info_delay = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	self:CancelRollQuest()
	self.time_require = true
	self.click_roll_seq = -1

	-- if self.cambered_list then
	-- 	self.cambered_list:DeleteMe()
	-- 	self.cambered_list = nil
	-- end

	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

	if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.remind_callback then
		RemindManager.Instance:UnBind(self.remind_callback)
		self.remind_callback = nil
	end

	if self.btn_ohdj_tween then
		self.btn_ohdj_tween:Kill()
		self.btn_ohdj_tween = nil
	end

	self.model_show_itemid_cache = nil
end

function XianLingGuZhenView:LoadCallBack()
	self.select_toggle = 1
	self.node_list.title_view_name.text.text = Language.XianLingGuZhen.Title
	local bundle, asset = ResPath.GetRawImagesPNG("a3_jgjf_bg_1")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

	self:InitMoneyBar()
	self:InitTabbar()

	XianLingGuZhenWGCtrl.Instance:SendXianLingRollReq(NEW_CS_RAXIANLINGZHEN_OPERA_TYPE.CS_RA_XIANLINGZHEN_OPERA_TYPE_ALL_INFO)
	self.node_list["note_btn"].button:AddClickListener(BindTool.Bind(self.OnClickNote, self))
	self.node_list["roll_btn"].button:AddClickListener(BindTool.Bind(self.OnClickRoll2, self))
	self.node_list["ten_pump_btn"].button:AddClickListener(BindTool.Bind(self.OnClickTenPump, self))
	self.node_list["consume_btn"].button:AddClickListener(BindTool.Bind(self.OnClickConsume,self))
	self.node_list["consume_btn2"].button:AddClickListener(BindTool.Bind(self.OnClickConsume2,self))
	self.node_list["consume_btn3"].button:AddClickListener(BindTool.Bind(self.OnClickConsume,self))
	self.node_list["consume_btn4"].button:AddClickListener(BindTool.Bind(self.OnClickConsume2,self))
	XUI.AddClickEventListener(self.node_list.probability_show_btn, BindTool.Bind(self.OpenGaiLvView, self))
	XUI.AddClickEventListener(self.node_list["btn_qmfl"], BindTool.Bind(self.OnClickQMFLBtn, self))
    XUI.AddClickEventListener(self.node_list["btn_ohdj"], BindTool.Bind(self.OnClickOHDJBtn, self))
	
	self.seq, self.grade = 0, nil
	self.flirs_open_flag = true
	self.item_reset_pos_list = {}
	self.cur_model_index = 1

	self.xianling_list = AsyncListView.New(XianLingListCell, self.node_list.xianling_list)
	if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list.display)
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	if not self.big_reward_list then
		self.big_reward_list = {}
		local num = self.node_list.show_list.transform.childCount
		for i = 1, num do
            self.big_reward_list[i] = BigRewardListCell.New(self.node_list.show_list:FindObj("show_list_cell" .. i))
        end
	end

	if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(false)
    end

	for i = 1, 2 do
		self.node_list["toggle_" .. i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickToggle, self, i))
	end

	self.record_list = AsyncListView.New(XianLingRecordCell, self.node_list.last_list)
	self.record_list:SetCellSizeDel(BindTool.Bind(self.CellSizeDel, self))
	if nil == self.item_change_callback then
		self.item_change_callback = BindTool.Bind(self.ItemChangeCallBack,self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.item_change_callback)
	end
	XianLingGuZhenWGData.Instance:SaveLastBigReward({})
	if not self.require_info_delay then
		self.require_info_delay = GlobalTimerQuest:AddRunQuest(function ()
			XianLingGuZhenWGCtrl.Instance:SendXianLingRollReq(NEW_CS_RAXIANLINGZHEN_OPERA_TYPE.CS_RA_XIANLINGZHEN_OPERA_TYPE_ALL_INFO)
		end, 10)
		XianLingGuZhenWGCtrl.Instance:SendXianLingRollReq(NEW_CS_RAXIANLINGZHEN_OPERA_TYPE.CS_RA_XIANLINGZHEN_OPERA_TYPE_RECORD)
	end
	self:FlushBigReward()
	self:TryToShowRollMsg()
	self:FlushConsumeIcon()

	self.node_list["toggle_1"].toggle.isOn = true

	local other_remind_list = {RemindName.XianLingGuZhen_QMHB, RemindName.XianLingGuZhen_QMLJ, RemindName.XianLing_GuZhen_DRAW}
    self.remind_callback = BindTool.Bind(self.OtherRemindCallBack, self)
    for k,v in pairs(other_remind_list) do
        RemindManager.Instance:Bind(self.remind_callback, v)
    end

	if self.btn_ohdj_tween then
		self.btn_ohdj_tween:Kill()
		self.btn_ohdj_tween = nil
	end
	self.btn_ohdj_tween = DG.Tweening.DOTween.Sequence()
	UITween.ShakeAnimi(self.node_list.ohdj_icon.transform, self.btn_ohdj_tween)
end

function XianLingGuZhenView:OtherRemindCallBack(remind_name, num)
    if remind_name == RemindName.XianLingGuZhen_QMHB or remind_name == RemindName.XianLingGuZhen_QMLJ or remind_name == RemindName.XianLing_GuZhen_DRAW then
		self:FlushDailyRewardRemind()
    end
end

function XianLingGuZhenView:InitTabbar()
	--local remind_tab = { {RemindName.XianLing_GuZhen} }

	self.type_data_list = XianLingGuZhenWGData.Instance:GetCurSpecialCfgData()	
	local tb_tab_name = {}
	if not IsEmptyTable(self.type_data_list) then
		for key, value in pairs(self.type_data_list) do
			tb_tab_name[value.sort_index] = value.tab_name
		end
	end

	local toggle_name_list = (not IsEmptyTable(tb_tab_name)) and tb_tab_name or Language.XianLingGuZhen.TabTitleGroup --{ Language.XianLingGuZhen.TabTitleGroup }
	local common_path = "uis/view/common_panel_prefab"
	self.tabbar = Tabbar.New(self.node_list, "VerticalTabbar_Activity")
	self.tabbar:SetVerTabbarCellName("VerticalTabbarCell_Activity")
	self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	self.tab_sub = { nil, nil, nil, nil, nil, nil, nil }
	self.tabbar:Init(toggle_name_list, self.tab_sub, "uis/view/xianling_guzhen_prefab", nil, remind_tab, XianLingGuZhenTabbarVerticalCellRender)
	-- self.tabbar:JumpToVerPrecent(1)
end

function XianLingGuZhenView:OnFlush(param_t, index)
	self:FlushTabbarInfo()

	if index > 0 and self.select_tab_index ~= index then
		self.select_tab_index = index
	end
	if not self.select_tab_index or self.select_tab_index <= 0 then return end


	-- self.type_data_list[self.select_tab_index]
	-- self.select_reward_pool_seq

	if self.select_toggle == 1 then
		self.node_list.raffle_panel:CustomSetActive(true)
		self.node_list.task_panel:CustomSetActive(false)
	elseif self.select_toggle == 2 then
		self.node_list.raffle_panel:CustomSetActive(false)
		self.node_list.task_panel:CustomSetActive(true)
	end
	self:OnClickTabCallBack()


	-- if XianLingGuZhenWGData.Instance:GetWaitChangeFlag() then return end

	-- self:FlushSpecialList()

	-- local my_count = XianLingGuZhenWGData.Instance:GetPersonalBuyTime()
	-- local special_cfg = XianLingGuZhenWGData.Instance:GetSpecialRewardCfg()
	-- local big_left_count = XianLingGuZhenWGData.Instance:GetCurBigLeftRewardCount()
	-- if special_cfg and special_cfg.total_weight and big_left_count ~= 0 then
	-- 	local my_gailv = math.floor(my_count / special_cfg.total_weight * 100)
	-- 	self.node_list["gailv_text"].text.text = my_gailv .. "%"
	-- else
	-- 	self.node_list["gailv_text"].text.text = "0%"
	-- end
	-- self.node_list["desc1"].text.text = string.format(Language.XianLingGuZhen.PersonalBuyTime,my_count)
	-- local left_count,limite_count = XianLingGuZhenWGData.Instance:GetCurBigRewardCount()



	-- if big_left_count == 0 then
	-- 	self.node_list["desc2"].text.text = Language.XianLingGuZhen.WorldLeftTime2
	-- else
	-- 	self.node_list["desc2"].text.text = string.format(Language.XianLingGuZhen.WorldLeftTime, left_count, limite_count)
	-- end
	-- self:FlushShowModel()
end

function XianLingGuZhenView:ShowIndexCallBack(index)
	self.last_show_item_id = nil
	local sel_idx = math.floor(index / 10)
	self.select_reward_pool_seq = -1
	if not IsEmptyTable(self.type_data_list) then
		local target_data = self.type_data_list[sel_idx]
		self.select_reward_pool_seq = target_data.reward_pool_seq
		local bundle, asset = ResPath.GetRawImagesPNG(target_data.tab_bg)
		if bundle and asset then
			self.node_list["title_img"].raw_image:LoadSprite(bundle, asset, function()
				self.node_list["title_img"].raw_image:SetNativeSize()
			end)
		end
	end
	XianLingGuZhenWGData.Instance:SetMarkRewardPoolSeq(self.select_reward_pool_seq)
	self:OnClickTabCallBack()

	if self.select_toggle == 1 then
		self:PlayAnim(self.node_list.raffle_panel)
	else
		self:PlayAnim(self.node_list.task_panel)
	end

	ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.XianLingGuZhen, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_XIANLING_ZHEN)
end

function XianLingGuZhenView:OnClickTabCallBack()
	if not self.select_reward_pool_seq then return end

	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end

	self:CacularActivityTime()
	self.time_quest = GlobalTimerQuest:AddRunQuest(function ()
		self:CacularActivityTime()
	end, 1)

	self:FlushDailyRewardRemind()

	self.cur_info_data = XianLingGuZhenWGData.Instance:GetRewardInfoByRewardPoorSeq(self.select_reward_pool_seq)
	if IsEmptyTable(self.cur_info_data) then return end

	local lingxi_getway_data = XianLingGuZhenWGData.Instance:GetLingXiGetWayListData()
	self.xianling_list:SetDataList(lingxi_getway_data)

	-- self.click_roll_seq = XianLingGuZhenWGData.Instance:GetCurBigRewardIndex()
	self:FlushConsumeItem()
	self:FlushItemCellList()
	self:FlushRewardPoorInfo()

	local all_data = XianLingGuZhenWGData.Instance:GetWorldRewardInfoData()
	self.record_list:SetDataList(all_data)

	local is_task_red = XianLingGuZhenWGData.Instance:GetXianLingTaskRed()
	self.node_list.task_remind:SetActive(is_task_red)

	local number_str = XianLingGuZhenWGData.Instance:GetDrawNumberStr(0, self.select_reward_pool_seq)
	self.node_list.number_text.text.text = number_str

end


function XianLingGuZhenView:OpenCallBack()
	XianLingGuZhenWGCtrl.Instance:SendXianLingRollReq(NEW_CS_RAXIANLINGZHEN_OPERA_TYPE.NEW_LINGXI_OPER_TYPE_TASK_INFO)
	-- 特殊需求，首次进入打开欧皇大奖
	local is_first_open = RoleWGData.GetRolePlayerPrefsInt("xianling_guzhen_first_open")
	if is_first_open ~= 1 then
		XianLingGuZhenWGCtrl.Instance:OpenOHDJView()
		RoleWGData.SetRolePlayerPrefsInt("xianling_guzhen_first_open", 1)
	end
end

function XianLingGuZhenView:FlushDailyRewardRemind()
	local remind = false

	-- 每日奖励
	if not XianLingGuZhenWGData.Instance:IsGetDailyReward() then
		remind = true
	end

	-- 全名红包
	local quhb_remind_num = RemindManager.Instance:GetRemind(RemindName.XianLingGuZhen_QMHB)
	if quhb_remind_num > 0 then
		remind = true
	end

	--全名福利
	local qufl_remind_num = RemindManager.Instance:GetRemind(RemindName.XianLingGuZhen_QMLJ)
	if qufl_remind_num > 0 then
		remind = true
	end

	self.node_list.btn_qmfl_remind:CustomSetActive(remind)
end

function XianLingGuZhenView:CancelRollQuest()
	if self.show_next_quest then
		GlobalTimerQuest:CancelQuest(self.show_next_quest)
		self.show_next_quest = nil
		self.node_list["roll_panel"]:SetActive(false)
	end
end

function XianLingGuZhenView:OnClickToggle(index, is_on)
	if self.select_toggle ~= index and is_on then
		self.select_toggle = index

		if index == 1 then
			self:PlayAnim(self.node_list.raffle_panel)
		else
			self:PlayAnim(self.node_list.task_panel)
		end
	end
end

function XianLingGuZhenView:CellSizeDel(data_index)
	local hight = 22
	local spece = 2
	data_index = data_index + 1
	local list = self.record_list:GetDataList()
	local data = list[data_index]
	if not data then 
		return hight
	end

	local msg_str = ""
	local item_data = ItemWGData.Instance:GetItemConfig(data.item_id)
	local match_num_str = (data.draw_number and data.draw_number > 0) and string.format(Language.XianLingGuZhen.LastRewardNumDesc, data.draw_number) or ''
	local str = string.format(Language.XianLingGuZhen.LastRewardDesc, data.name, match_num_str, item_data.name)
	msg_str = str
    self.node_list.TestText.text.text = msg_str
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["TestText"].rect)
	hight = math.ceil(self.node_list["TestText"].rect.rect.height) <= 22 and 22 or math.ceil(self.node_list["TestText"].rect.rect.height) + spece
	return hight
end

function XianLingGuZhenView:PlayAnim(node)
	node:SetActive(false)
    ReDelayCall(self, function()
		node:SetActive(true)
        UITween.CleanAlphaShow(GuideModuleName.XianLingGuZhen)
		UITween.AlphaShow(GuideModuleName.XianLingGuZhen, node, 0.1, 1, 0.35, DG.Tweening.Ease.Linear)
		self:Flush()
    end, 0.1, "XianLingGuZhenView")

	UITween.CleanAllMoveToShowPanel(GuideModuleName.XianLingGuZhen)
	UITween.MoveToShowPanel(GuideModuleName.XianLingGuZhen, node, Vector2(40, 0), Vector2(0, 0), 0.25, DG.Tweening.Ease.Linear)
end

function XianLingGuZhenView:InitMoneyBar()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end

function XianLingGuZhenView:FlushRewardPoorInfo()
	local info_data = XianLingGuZhenWGData.Instance:GetRewardInfoByRewardPoorSeq(self.select_reward_pool_seq)
	if IsEmptyTable(info_data) then
		return
	end

	local cfg = XianLingGuZhenWGData.Instance:GetSpcialRewardCfg(self.select_reward_pool_seq, info_data.cur_reward_seq)
	if IsEmptyTable(cfg) then
		return
	end

	-- 剩余大奖次数
	local big_left_count = info_data.special_reward_count_list[info_data.cur_reward_seq + 1] or 0
	local big_count_limit = cfg.count_limit
	local color = big_left_count > 0 and COLOR3B.D_GREEN or COLOR3B.D_RED
	-- self.node_list.desc_djkc.text.text = ToColorStr(big_left_count .."/".. big_count_limit, color)

	-- 获奖概率
	if cfg.total_weight and big_left_count ~= 0 then
		local my_gailv = math.floor(info_data.role_draw_count / cfg.total_weight * 100)
		self.node_list.desc_hjgl.text.text = my_gailv .. "%"
	else
		self.node_list.desc_hjgl.text.text = "0%"
	end

	self.node_list["desc1"].text.text = string.format(Language.XianLingGuZhen.PersonalBuyTime, info_data.role_draw_count)

	-- 全服剩余
	local left_count, limite_count = XianLingGuZhenWGData.Instance:GetCurBigRewardCount(self.select_reward_pool_seq)
	-- if big_left_count == 0 then
	-- 	self.node_list["desc2"].text.text = Language.XianLingGuZhen.WorldLeftTime2
	-- else
	-- 	self.node_list["desc2"].text.text = Language.XianLingGuZhen.WorldLeftTime..ToColorStr(left_count.."/"..limite_count, COLOR3B.D_GREEN)
	-- end
	self.node_list["desc2"].text.text = string.format(Language.XianLingGuZhen.WorldLeftTime, ToColorStr(left_count.."/"..limite_count, COLOR3B.D_GREEN))
	self.node_list["desc_xianlingguzhen_tip"].text.text = cfg.lottery_description

	local show_big_reward_name_str = ''
	if cfg.model_show_itemid then
		show_big_reward_name_str = ItemWGData.Instance:GetItemName(tonumber(cfg.model_show_itemid))
	end
	self.node_list["desc_name"].text.text = show_big_reward_name_str

	self:FlushModel(cfg)
	self:FlushModelCapVal()
	-- self:FlushShowModel()

	if not self.cur_turn_time_quest then
		self.cur_turn_time_quest = GlobalTimerQuest:AddRunQuest(function ()
			self:CacularTurnTime()
		end, 1)

		self:CacularTurnTime()
	end
end

function XianLingGuZhenView:FlushModel(model_cfg)
	if IsEmptyTable(model_cfg)then
		self.model_show_itemid_cache = nil
		return
	end

	if self.model_show_itemid_cache == model_cfg.model_show_itemid then
		return
	end

	self.model_show_itemid_cache = model_cfg.model_show_itemid
	-- 形象展示
	local display_data = {}
	display_data.should_ani = true
	if model_cfg.model_show_itemid ~= 0 and model_cfg.model_show_itemid ~= "" then
		local split_list = string.split(model_cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = model_cfg.model_show_itemid
		end
	end

	display_data.bundle_name = model_cfg.model_bundle_name
	display_data.asset_name = model_cfg.model_asset_name
	display_data.render_type = model_cfg.model_show_type - 1
	display_data.need_wp_tween = true
	display_data.hide_model_block = false
	display_data.model_click_func = function ()
        TipWGCtrl.Instance:OpenItem({item_id = model_cfg.model_show_itemid})
    end
	local scale = tonumber(model_cfg.model_scale) or 1
	local pos_x, pos_y, pos_z = 0, 0, 0
	if model_cfg.display_pos and model_cfg.display_pos ~= "" then
		local pos_list = string.split(model_cfg.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
		pos_z = tonumber(pos_list[3]) or pos_z
	end
	RectTransform.SetAnchoredPosition3DXYZ(self.node_list.display.rect, pos_x, pos_y, pos_z)

	if model_cfg.model_pos and model_cfg.model_pos ~= "" then
		local pos_list = string.split(model_cfg.model_pos, "|")
		local posx = tonumber(pos_list[1]) or 0
		local posy = tonumber(pos_list[2]) or 0
		local posz = tonumber(pos_list[3]) or 0
		display_data.model_adjust_root_local_position = Vector3(posx, posy, posz)
	end

	if model_cfg.rotation and model_cfg.rotation ~= "" then
		local rot_list = string.split(model_cfg.rotation, "|")
		local rot_x = tonumber(rot_list[1]) or 0
		local rot_y = tonumber(rot_list[2]) or 0
		local rot_z = tonumber(rot_list[3]) or 0
		display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
	end

	if model_cfg.model_scale and model_cfg.model_scale ~= "" then
		display_data.model_adjust_root_local_scale = model_cfg.model_scale
	end

	display_data.model_rt_type = ModelRTSCaleType.L

	-- self.node_list.display.transform:SetLocalScale(scale, scale, scale)

	self.model_display:SetData(display_data)
end

function XianLingGuZhenView:FlushItemCellList()
    local data_list = XianLingGuZhenWGData.Instance:GetCurNormalListData(self.select_reward_pool_seq)
	local tb_reward = {}
	if not IsEmptyTable(data_list) then
		for key, value in ipairs(data_list) do
			if value.reward_item then
				table.insert(tb_reward, value.reward_item[0])
			end
		end
	end
	
	self.reward_list:SetDataList(tb_reward)
    -- local btn_item_list = self.cambered_list:GetRenderList()
    -- for k, item_cell in ipairs(btn_item_list) do
    --     local item_data = data_list[k]
	-- 	print_error(item_data)
    --     item_cell:SetData(item_data)
    -- end
end

function XianLingGuZhenView:TryToShowRollMsg()
	if not self.show_roll_delay then
		self.show_next_quest = GlobalTimerQuest:AddDelayTimer(function ()
			self:TryToShowRollMsg()
		end,10)
	end

	if not self.node_list["roll_panel"] then
		self:CancelRollQuest()
	end
	local info = XianLingGuZhenWGData.Instance:GetNextShowRecord()
	if IsEmptyTable(info) then
		self.node_list["roll_panel"]:SetActive(false)
		return
	end

	self.node_list["roll_panel"]:SetActive(true)
	local item_cfg = ItemWGData.Instance:GetItemConfig(info.item_id)
	self.node_list["roll_text"].rect.anchoredPosition = Vector2(0,1)
	if not item_cfg or not item_cfg.name then
		self.node_list["roll_panel"]:SetActive(false)
		return
	end
	local item_name = ToColorStr(item_cfg.name,ITEM_COLOR[item_cfg.color])
	self.node_list["roll_text"].text.text = string.format(Language.XianLingGuZhen.RollMsg,info.name,item_name)
	local tweener = self.node_list["roll_text"].rect:DOAnchorPosX(-1000, 9)
	tweener:SetEase(DG.Tweening.Ease.Linear)
end

function XianLingGuZhenView:OnClickNote()
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetContent(Language.XianLingGuZhen.RuleDesc, Language.XianLingGuZhen.RuleTitlt)
	end
end

function XianLingGuZhenView:OnClickApplyAdd()
	local cur_special_cfg = XianLingGuZhenWGData.Instance:GetSpecialRewardCfg()
	if cur_special_cfg and cur_special_cfg.seq then
		if XianLingGuZhenWGData.Instance:GetBigRewardHadBeenAdd(cur_special_cfg.seq) then
		else
			XianLingGuZhenWGData.Instance:SetBigRewardBeenAdd(cur_special_cfg.seq)
			XianLingGuZhenWGCtrl.Instance:SendXianLingRollReq(NEW_CS_RAXIANLINGZHEN_OPERA_TYPE.CS_RA_XIANLINGZHEN_OPERA_TYPE_ADD_REWARD_COUNT, cur_special_cfg.seq)
		end
		TipWGCtrl.Instance:ShowSystemMsg(Language.XianLingGuZhen.HaveApply)
	end
end

function XianLingGuZhenView:OnClickConsume()
	local consume_item,consume_gold = XianLingGuZhenWGData.Instance:GetConsumeItemID()
	if IsEmptyTable(consume_item) then
		return
	end
	TipWGCtrl.Instance:OpenItemTipGetWay(consume_item)
end

function XianLingGuZhenView:OnClickConsume2()
	TipWGCtrl.Instance:OpenItemTipGetWay({item_id = COMMON_CONSTS.VIRTUAL_ITEM_GOLD})
end

function XianLingGuZhenView:OnClickRoll()
	local left_count = XianLingGuZhenWGData.Instance:GetCurBigRewardCount(self.select_reward_pool_seq)
	if left_count <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.XianLingGuZhen.NoBigRewardCanNotDraw)
		return
	end

	self:FlushConsumeItem()
	if not self.enough_num and not self.enough_gold then
		VipWGCtrl.Instance:OpenTipNoGold()
		return 
	end

	local empty = ItemWGData.Instance:GetEmptyNum()
	if empty < 4 then
		RoleBagWGData.Instance:SetRoleBagCleanFlag(true)
		RoleBagWGCtrl.Instance:OpenRoleBagCleanTips(KNAPSACK_TYPE.NORMAL)
		return
	end

	local ok_func = function ()
		-- if self.click_roll_seq == XianLingGuZhenWGData.Instance:GetCurBigRewardIndex() then
		-- 	XianLingGuZhenWGCtrl.Instance:SendXianLingRollReq(NEW_CS_RAXIANLINGZHEN_OPERA_TYPE.CS_RA_XIANLINGZHEN_OPERA_TYPE_DRAW, LuckyDrawType.SinglePumping, XianLingGuZhenWGData.Instance:GetCurBigRewardIndex())
		-- else
		-- 	TipWGCtrl.Instance:ShowSystemMsg(Language.XianLingGuZhen.IsAlreadyNextReward)
		-- end

		local info_data = XianLingGuZhenWGData.Instance:GetRewardInfoByRewardPoorSeq(self.select_reward_pool_seq)
		if IsEmptyTable(info_data) then
			TipWGCtrl.Instance:ShowSystemMsg(Language.XianLingGuZhen.IsAlreadyNextReward)
			return
		end

		XianLingGuZhenWGCtrl.Instance:SendXianLingRollReq(NEW_CS_RAXIANLINGZHEN_OPERA_TYPE.CS_RA_XIANLINGZHEN_OPERA_TYPE_DRAW, 
			LuckyDrawType.SinglePumping, info_data.cur_reward_seq, self.select_reward_pool_seq)
	end
	
	if not self.enough_num and self.enough_gold then
		local consume_item,consume_gold = XianLingGuZhenWGData.Instance:GetConsumeItemID()
		if IsEmptyTable(consume_item) then
			return
		end
		-- local str = Language.XianLingGuZhen.UseGlodToTurn
		-- local check_string = "xian_ling_turn"
		-- local chect_text_str = Language.Common.DontTip2
		-- self.click_roll_seq = XianLingGuZhenWGData.Instance:GetCurBigRewardIndex()
		-- TipWGCtrl.Instance:OpenCheckAlertTips(str, ok_func, check_string,chect_text_str)

		local tips_data = {}
		tips_data.item_id = consume_item.item_id
		tips_data.price = consume_gold
		tips_data.draw_count = 1
		tips_data.has_checkbox = true
		tips_data.checkbox_str = string.format("xian_ling_turn")
		TipWGCtrl.Instance:OpenTipsDrawRewardView(tips_data, ok_func, nil)
	else
		-- self.click_roll_seq = XianLingGuZhenWGData.Instance:GetCurBigRewardIndex()
		ok_func()
	end
end

function XianLingGuZhenView:OnClickRoll2()
	local ok_func = function ()
		self:OnClickRoll()
	end

	-- local big_left_count = XianLingGuZhenWGData.Instance:GetCurBigLeftRewardCount(self.select_reward_pool_seq)
	-- if big_left_count == 0 then
	-- 	local str = Language.XianLingGuZhen.NoBigReward
	-- 	local check_string = "xian_ling_no_reward"
	-- 	local chect_text_str = Language.Common.DontTip2
	-- 	XianLingGuZhenWGCtrl.Instance:OpenCheckAlertTips(str, ok_func, check_string,chect_text_str)
	-- else
	-- 	ok_func()
	-- end
	ok_func()
end

--十连抽
function XianLingGuZhenView:OnClickTenPump()
	local ok_func = function ()
		self:OnClickTenPumpFunc()
	end

	-- local big_left_count = XianLingGuZhenWGData.Instance:GetCurBigLeftRewardCount(self.select_reward_pool_seq)
	-- if big_left_count == 0 then
	-- 	local str = Language.XianLingGuZhen.NoBigReward
	-- 	local check_string = "xian_ling_no_reward"
	-- 	local chect_text_str = Language.Common.DontTip2
	-- 	XianLingGuZhenWGCtrl.Instance:OpenCheckAlertTips(str, ok_func, check_string,chect_text_str)
	-- else
	-- 	ok_func()
	-- end
	ok_func()
end

function XianLingGuZhenView:OnClickTenPumpFunc()
	local left_count = XianLingGuZhenWGData.Instance:GetCurBigRewardCount(self.select_reward_pool_seq)
	if left_count <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.XianLingGuZhen.NoBigRewardCanNotDraw)
		return
	end

	self:FlushConsumeItem()
	if not self.enough_num_ten and not self.enough_gold_ten then  
		VipWGCtrl.Instance:OpenTipNoGold()
		return 
	end

	local empty = ItemWGData.Instance:GetEmptyNum()
	if empty < 10 then
		RoleBagWGData.Instance:SetRoleBagCleanFlag(true)
		RoleBagWGCtrl.Instance:OpenRoleBagCleanTips()
		return
	end

	local ok_func = function ()
		-- if self.click_roll_seq == XianLingGuZhenWGData.Instance:GetCurBigRewardIndex() then
		-- 	XianLingGuZhenWGCtrl.Instance:SendXianLingRollReq(NEW_CS_RAXIANLINGZHEN_OPERA_TYPE.CS_RA_XIANLINGZHEN_OPERA_TYPE_DRAW,LuckyDrawType.TenContinuousPumping, XianLingGuZhenWGData.Instance:GetCurBigRewardIndex())
		-- else
		-- 	TipWGCtrl.Instance:ShowSystemMsg(Language.XianLingGuZhen.IsAlreadyNextReward)
		-- end

		local info_data = XianLingGuZhenWGData.Instance:GetRewardInfoByRewardPoorSeq(self.select_reward_pool_seq)
		if IsEmptyTable(info_data) then
			return
		end

		XianLingGuZhenWGCtrl.Instance:SendXianLingRollReq(NEW_CS_RAXIANLINGZHEN_OPERA_TYPE.CS_RA_XIANLINGZHEN_OPERA_TYPE_DRAW,
			LuckyDrawType.TenContinuousPumping, info_data.cur_reward_seq, self.select_reward_pool_seq)
	end

	if not self.enough_num_ten and self.enough_gold_ten then
		local consume_item,consume_gold = XianLingGuZhenWGData.Instance:GetConsumeItemID()
		if IsEmptyTable(consume_item) then
			return
		end

		left_count = left_count >= LuckyDrawType.TenContinuousPumping and LuckyDrawType.TenContinuousPumping or left_count
		local tips_data = {}
		tips_data.item_id = consume_item.item_id
		tips_data.price = consume_gold
		tips_data.draw_count = left_count
		tips_data.has_checkbox = true
		tips_data.checkbox_str = string.format("xian_ling_turn_ten")
		TipWGCtrl.Instance:OpenTipsDrawRewardView(tips_data, ok_func, nil)

		-- --如果有道具会提前消耗道具 再使用仙玉补充
		-- local str = string.format(Language.XianLingGuZhen.UseGlodToTurnTen, consume_gold * (left_count - have_num), left_count)  
		-- local check_string = "xian_ling_turn_ten"
		-- local chect_text_str = Language.Common.DontTip2
		-- self.click_roll_seq = XianLingGuZhenWGData.Instance:GetCurBigRewardIndex()
		-- TipWGCtrl.Instance:OpenCheckAlertTips(str, ok_func, check_string,chect_text_str)
	else
		-- self.click_roll_seq = XianLingGuZhenWGData.Instance:GetCurBigRewardIndex()
		ok_func()
	end
end

function XianLingGuZhenView:OnClickJumpAnim()
	local jump_anim = XianLingGuZhenWGData.Instance:GetJumpAnimFlag()
	XianLingGuZhenWGData.Instance:SetJumpAnimFlag(not jump_anim)
end


function XianLingGuZhenView:FlushBigReward()
	local big_reward = XianLingGuZhenWGData.Instance:GetLasBigReward()
	if big_reward and not IsEmptyTable(big_reward) and self.node_list["last_big_reward"] then
		self.node_list["last_big_reward"].text.text = string.format(Language.XianLingGuZhen.GetBigRewardDesc2,big_reward.name)
		XianLingGuZhenWGData.Instance:SetNextTurnTime(20)
		self.delay_flush_last = false
		self:CancelRollQuest()
		self.show_next_quest = GlobalTimerQuest:AddRunQuest(function ()
			self:ShowNextTurnText()
		end,1)
		self:ShowNextTurnText()
		XianLingGuZhenWGData.Instance:SaveLastBigReward({})
	else
		self.delay_flush_last = false
	end
end

function XianLingGuZhenView:ShowNextTurnText()
	local next_time = XianLingGuZhenWGData.Instance:GetNextTurnTime()
	self.node_list["next_turn_time"].text.text = next_time..Language.XianLingGuZhen.NextTrunTime

	if next_time == 0 then
		self:CancelRollQuest()
		self.delay_flush_last = false
	end
	next_time = next_time - 1
	XianLingGuZhenWGData.Instance:SetNextTurnTime(next_time)
end

function XianLingGuZhenView:CacularTurnTime()
	local end_time = XianLingGuZhenWGData.Instance:GetCurEndTimeByPoolSeq(self.select_reward_pool_seq)
	local now_time = TimeWGCtrl.Instance:GetServerTime()

	if end_time <= now_time then
		if self.cur_turn_time_quest then
			GlobalTimerQuest:CancelQuest(self.cur_turn_time_quest)
			self.cur_turn_time_quest = nil
		end

		if self.cur_turn_time_quest then
			self.cur_turn_time_quest = nil
			self.node_list["left_time"].text.text = "00:00"
			self.node_list["right_time"].text.text = "00:00"
			if not self.time_require then
				self.time_require = true
				XianLingGuZhenWGCtrl.Instance:SendXianLingRollReq(NEW_CS_RAXIANLINGZHEN_OPERA_TYPE.CS_RA_XIANLINGZHEN_OPERA_TYPE_ALL_INFO)
			end
		end
	else
		self.time_require = false

		local left_count = XianLingGuZhenWGData.Instance:GetCurBigRewardCount(self.select_reward_pool_seq)
		if left_count <= 0 then
			self.node_list["right_time"].text.text = Language.XianLingGuZhen.NoBigRewardTimeStr
		else
			self.node_list["right_time"].text.text = TimeUtil.FormatTimeLanguage2(end_time - now_time)
		end

		self.node_list["left_time"].text.text = TimeUtil.FormatSecondDHM2(end_time - now_time)
	end
end

function XianLingGuZhenView:FlushConsumeIcon()
	local consume_item,consume_gold = XianLingGuZhenWGData.Instance:GetConsumeItemID()
	if IsEmptyTable(consume_item) then
		return
	end
	local item_cfg = ItemWGData.Instance:GetItemConfig(consume_item.item_id)
	self.node_list["consume_btn"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
	self.node_list["consume_btn3"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
end

function XianLingGuZhenView:FlushConsumeItem()
	local consume_item, consume_gold = XianLingGuZhenWGData.Instance:GetConsumeItemID()
	if IsEmptyTable(consume_item) then
		return
	end
	local item_cfg = ItemWGData.Instance:GetItemConfig(consume_item.item_id)
	local left_count, limite_count = XianLingGuZhenWGData.Instance:GetCurBigRewardCount(self.select_reward_pool_seq)

	self.node_list["money1"].text.text = consume_gold
	local have_num = ItemWGData.Instance:GetItemNumInBagById(consume_item.item_id)
	local need_num = consume_item.num
	local have_gold = RoleWGData.Instance:GetRoleVo().gold or 0
	self.enough_num = need_num <= have_num
	self.enough_gold = have_gold >= consume_gold

	self.enough_num_ten = need_num * 10 <= have_num
	self.enough_gold_ten = have_gold >= consume_gold * 10

	local enough_num_left = left_count <= have_num
	local color1 = self.enough_num and COLOR3B.C8 or COLOR3B.C10
	local color2 = self.enough_num_ten and COLOR3B.C8 or COLOR3B.C10
	local color3 = enough_num_left and COLOR3B.C8 or COLOR3B.C10

	self.node_list["money2"].text.text = ToColorStr(have_num, color1) .. "/" .. need_num

	if left_count >= LuckyDrawType.TenContinuousPumping or left_count <= 0 then
		self.node_list["money3"].text.text = consume_gold * 10
		self.node_list["money4"].text.text = ToColorStr(have_num, color2) .."/".. (need_num * 10)
		self.node_list.ten_pump_desc.text.text = Language.XianLingGuZhen.TenContinuousPumping
	else
		self.node_list["money3"].text.text = consume_gold * left_count
		self.node_list["money4"].text.text = ToColorStr(have_num, color3) .."/".. left_count
		self.node_list.ten_pump_desc.text.text = string.format(Language.XianLingGuZhen.LuckyDrawNum, NumberToChinaNumber(left_count))
	end

	self.node_list["raffle_remind"]:SetActive((self.enough_num or self.enough_num_ten) and left_count > 0)
	self.node_list["once_remind"]:SetActive(self.enough_num and left_count > 0)
	self.node_list["ten_remind"]:SetActive(self.enough_num_ten and left_count > 0)
end

function XianLingGuZhenView:ItemChangeCallBack(change_item_id)
	local consume_item,consume_gold = XianLingGuZhenWGData.Instance:GetConsumeItemID()
	if IsEmptyTable(consume_item) then
		return
	end
	if consume_item.item_id == change_item_id then
		self:FlushConsumeItem()
	end
end

function XianLingGuZhenView:CacularActivityTime()
	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_XIANLING_ZHEN)
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	if activity_status and activity_status.end_time > now_time then
		self.node_list["time_text"].text.text = TimeUtil.FormatSecondDHM2(activity_status.end_time - now_time)
	else
		if self.time_quest then
			GlobalTimerQuest:CancelQuest(self.time_quest)
			self.time_quest = nil
		end
		self.node_list["time_text"].text.text = Language.XianLingGuZhen.Activity_End
	end
end

function XianLingGuZhenView:OpenGaiLvView()
    local info = XianLingGuZhenWGData.Instance:GetGaiLvInfo()
    XianLingGuZhenWGCtrl.Instance:OpenGaiLvShowView(info)
	--TipWGCtrl.Instance:OpenGaiLvShowView(info)
end

function XianLingGuZhenView:OnClickQMFLBtn()
	XianLingGuZhenWGCtrl.Instance:OpenQMFLView()
end

function XianLingGuZhenView:OnClickOHDJBtn()
	XianLingGuZhenWGCtrl.Instance:OpenOHDJView()
end

function XianLingGuZhenView:FlushModelCapVal()
	if self.delay_flush_last then return end

	-- local cur_left_num = XianLingGuZhenWGData.Instance:GetCurBigLeftRewardCount(self.select_reward_pool_seq)
	-- if cur_left_num <= 0 then
	-- 	self.node_list["common_capability_style"]:SetActive(false)
	-- 	self.node_list["common_max_capability_style"]:SetActive(false)
	-- 	return
	-- end

	local info_data = XianLingGuZhenWGData.Instance:GetRewardInfoByRewardPoorSeq(self.select_reward_pool_seq)
	if IsEmptyTable(info_data) then
		return
	end

	local cfg = XianLingGuZhenWGData.Instance:GetSpcialRewardCfg(self.select_reward_pool_seq, info_data.cur_reward_seq)
	if IsEmptyTable(cfg) then
		return
	end

	if self.cur_show_model_id and self.cur_show_model_id == cfg.reward_item[0].item_id then
		return
	end
	self.cur_show_model_id = cfg.reward_item[0].item_id
	local item_cfg,item_type = ItemWGData.Instance:GetItemConfig(cfg.reward_item[0].item_id)

	-- local power_num = ItemShowWGData.CalculateCapability(cfg.reward_item[0].item_id, true)
	-- local need_get_sys, sys_type, replace_idx, show_cap_type = ItemShowWGData.Instance:GetIsNeedSysAttr(item_cfg.id, item_cfg.sys_attr_cap_location)
	
	local power_num = 0
	if cfg.model_show_itemid and cfg.model_show_itemid ~= 0 and cfg.model_show_itemid ~= "" then
		local split_list = string.split(cfg.model_show_itemid, "|")

		if #split_list > 1 then
			for k, v in pairs(split_list) do
				power_num = power_num + ItemShowWGData.CalculateCapability(tonumber(v), false)
			end
		else
			power_num = power_num + ItemShowWGData.CalculateCapability(cfg.model_show_itemid, false)
		end
	end

	self.node_list["cap_value"].text.text = power_num
	self.node_list["common_capability_style"]:SetActive(true)
	self.node_list["common_max_capability_style"]:SetActive(false)
	-- if sys_type == ITEMTIPS_SYSTEM.MOUNT or
	-- 	sys_type == ITEMTIPS_SYSTEM.LING_CHONG or
	-- 	sys_type == ITEMTIPS_SYSTEM.HUA_KUN or
	-- 	sys_type == ITEMTIPS_SYSTEM.TS_HUANHUA or sys_type == ITEMTIPS_SYSTEM.XIAN_WA then
	-- 	self.node_list["common_max_capability_style"]:SetActive(true)
	-- 	self.node_list["common_capability_style"]:SetActive(false)
	-- 	self.node_list["max_cap_value"].text.text = power_num
	-- else
	-- 	self.node_list["cap_value"].text.text = power_num
	-- 	self.node_list["common_capability_style"]:SetActive(true)
	-- 	self.node_list["common_max_capability_style"]:SetActive(false)
	-- end
end

---------------------------------------------------------------------------------------------------------------

-- --刷新模型展示模型
-- function XianLingGuZhenView:FlushShowModel()
-- 	if self.delay_flush_last then return end

-- 	local cur_special_cfg = XianLingGuZhenWGData.Instance:GetSpecialRewardCfg()
-- 	local cur_left_num = XianLingGuZhenWGData.Instance:GetCurBigLeftRewardCount(self.select_reward_pool_seq)
-- 	if cur_left_num <= 0 then
-- 		self.node_list["common_capability_style"]:SetActive(false)
-- 		self.node_list["common_max_capability_style"]:SetActive(false)
-- 		return
-- 	end

-- 	if not IsEmptyTable(cur_special_cfg) and cur_special_cfg.reward_item then
-- 		if self.cur_show_model_id and self.cur_show_model_id == cur_special_cfg.reward_item[0].item_id then
-- 			return
-- 		end
-- 		self.cur_show_model_id = cur_special_cfg.reward_item[0].item_id
-- 		local item_cfg,item_type = ItemWGData.Instance:GetItemConfig(cur_special_cfg.reward_item[0].item_id)

-- 		local power_num = ItemShowWGData.CalculateCapability(cur_special_cfg.reward_item[0].item_id, true)

-- 		local need_get_sys, sys_type, replace_idx, show_cap_type = ItemShowWGData.Instance:GetIsNeedSysAttr(item_cfg.id, item_cfg.sys_attr_cap_location)
-- 		if sys_type == ITEMTIPS_SYSTEM.MOUNT or
-- 			sys_type == ITEMTIPS_SYSTEM.LING_CHONG or
-- 			sys_type == ITEMTIPS_SYSTEM.HUA_KUN or
-- 			sys_type == ITEMTIPS_SYSTEM.TS_HUANHUA or sys_type == ITEMTIPS_SYSTEM.XIAN_WA then
-- 			self.node_list["common_max_capability_style"]:SetActive(true)
-- 			self.node_list["common_capability_style"]:SetActive(false)
-- 			self.node_list["max_cap_value"].text.text = power_num
-- 		else
-- 			power_num = ItemShowWGData.CalculateCapability(cur_special_cfg.reward_item[0].item_id)
-- 			if power_num == 0 and item_cfg and item_cfg.capability_show > 0 then
-- 				power_num = item_cfg.capability_show
-- 			end

-- 			self.node_list["cap_value"].text.text = power_num
-- 			self.node_list["common_capability_style"]:SetActive(true)
-- 			self.node_list["common_max_capability_style"]:SetActive(false)
-- 		end
-- 	end
-- end

-- function XianLingGuZhenView:FlushSpecialList(refresh)
-- 	if self.flirs_open_flag and not refresh then
-- 		local special_reward_list = XianLingGuZhenWGData.Instance:GetSpecialRewardListData()
-- 		for i,v in ipairs(self.big_reward_list) do
-- 	    	if special_reward_list[i] then
-- 	        	v:SetData(special_reward_list[i])
-- 	        end
--     	end
-- 	else
-- 		for i,v in ipairs(self.big_reward_list) do
-- 	    	v:OnFlush()
--     	end
-- 	end

-- 	local cur_special_cfg = XianLingGuZhenWGData.Instance:GetSpecialRewardCfg()
-- 	if not IsEmptyTable(cur_special_cfg) then
-- 		self:FlushModel(cur_special_cfg)
-- 		local item_data = ItemWGData.Instance:GetItemConfig(cur_special_cfg.reward_item[0].item_id)
-- 		self.node_list.big_reward_name.text.text = item_data.name
-- 	end
-- end

function XianLingGuZhenView:FlushTabbarInfo()
	local ver_cell_list = self.tabbar:GetVerCellList()
    if IsEmptyTable(ver_cell_list) then
        return
    end

	for k, v in pairs(ver_cell_list) do
		if v:GetView() then
			v:FlushRewardInfo()
		end
	end
end

XianLingGuZhenTabbarVerticalCellRender = XianLingGuZhenTabbarVerticalCellRender or BaseClass(VerItemRender)

function XianLingGuZhenTabbarVerticalCellRender:FlushRewardInfo()
	if self.index then
		local type_data_list = XianLingGuZhenWGData.Instance:GetCurSpecialCfgData()
		local target_data = type_data_list[self.index]
		local reward_pool_seq = (type_data_list[self.index] or {}).reward_pool_seq or -1

		if reward_pool_seq >= 0 then
			local info_data = XianLingGuZhenWGData.Instance:GetRewardInfoByRewardPoorSeq(reward_pool_seq)
			if IsEmptyTable(info_data) then
				return
			end
		
			local cfg = XianLingGuZhenWGData.Instance:GetSpcialRewardCfg(reward_pool_seq, info_data.cur_reward_seq)
			if IsEmptyTable(cfg) then
				return
			end
		
			-- 剩余大奖次数
			local big_left_count = info_data.special_reward_count_list[info_data.cur_reward_seq + 1] or 0
			-- local big_count_limit = cfg.count_limit
			-- local color = big_left_count > 0 and COLOR3B.D_GREEN or COLOR3B.D_RED
			local str = string.format(Language.XianLingGuZhen.MaxCountLimit, big_left_count) -- big_left_count .."/".. cfg.count_limit
			self.node_list.desc_reward_count.text.text = str
			self.node_list.desc_reward_count_hl.text.text = str

			local consume_item, _ = XianLingGuZhenWGData.Instance:GetConsumeItemID()
			if IsEmptyTable(consume_item) then
				return
			end
			local have_num = ItemWGData.Instance:GetItemNumInBagById(consume_item.item_id)
			self.node_list["RedPoint"]:SetActive(have_num >= consume_item.num and big_left_count > 0)
		end
	end
end