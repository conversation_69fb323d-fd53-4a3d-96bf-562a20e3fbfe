VientianeTianyinView = VientianeTianyinView or BaseClass(SafeBaseView)

local SLDER_VALUE = { 0.1, 0.3, 0.5, 0.67, 0.85, 1 }

function VientianeTianyinView:__init()
	self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Full

	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_common_panel")
	self:AddViewResource(0, "uis/view/vientiane_tianyin_ui_prefab", "layout_vientiane_tianyin")
end

function VientianeTianyinView:__delete()

end

function VientianeTianyinView:OpenCallBack()
	ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_WANXIAN_TIANYIN,
		OA_WANXIAN_TIANYIN_INFO_TYPE.INFO)
end

function VientianeTianyinView:LoadCallBack()
	self:FlushEndTime()
	if self.all_reward_list == nil then
		self.all_reward_list = {}
		for i = 1, VientianeTianyinData.MAX_REWARD_COUNT do
			self.all_reward_list[i] = VientianeCell.New(self.node_list["suit_list"]:FindObj("tianyin_suit_item" .. i))
			self.all_reward_list[i]:SetIndex(i)
		end
	end

	local bundle, asset = ResPath.GetRawImagesPNG("a2_wxty_bj")
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)

	--加载模型时装
	if nil == self.role_model then
		self.role_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["ph_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = false,
		}
		
		self.role_model:SetRenderTexUI3DModel(display_data)
		-- self.role_model:SetUI3DModel(self.node_list["ph_display"].transform,
		-- 	nil, 1, true, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.role_model)
	end

	XUI.AddClickEventListener(self.node_list["everyday_gift"], BindTool.Bind(self.OnBtnfreeBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_obtain, BindTool.Bind(self.OnClickObtain, self))
end

function VientianeTianyinView:ReleaseCallBack()
	if self.all_reward_list then
		for k, v in pairs(self.all_reward_list) do
			v:DeleteMe()
		end
		self.all_reward_list = nil
	end

	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	if self.lingchong_model then
		self.lingchong_model:DeleteMe()
		self.lingchong_model = nil
	end

	if self.xianwa_model then
		self.xianwa_model:DeleteMe()
		self.xianwa_model = nil
	end

	if self.mount_model then
		self.mount_model:DeleteMe()
		self.mount_model = nil
	end

	if CountDownManager.Instance:HasCountDown("vientiane_end_time") then
		CountDownManager.Instance:RemoveCountDown("vientiane_end_time")
	end

	-- self.footprint_eff_t = nil
	-- self.is_foot_view = nil
	self.body_res_id = nil
	self.mount_res_id = nil
	self.have_foot_print = nil
	self.next_create_footprint_time = nil
end

function VientianeTianyinView:OnFlush()
	self:FlushSuitSlider()
	self:FlushSuitModel()

	local round_cfg = VientianeTianyinData.Instance:GetCurRoundRewardCfg()
	if IsEmptyTable(round_cfg) then
		return
	end

	for k, v in ipairs(self.all_reward_list) do
		v:SetData(round_cfg[k])
	end

	local cur_integral = VientianeTianyinData.Instance:GetCurScore()
	self.node_list.cur_integral.text.text = string.format(Language.Vientiane.CurIntegral, cur_integral)

	local is_buy_free = VientianeTianyinData.Instance:GetShopIsBuyFlag()
	self.node_list.gift_is_get:SetActive(is_buy_free)
	self.node_list.gift_remind:SetActive(not is_buy_free)
end

function VientianeTianyinView:FlushSuitSlider()
	local data_list = VientianeTianyinData.Instance:GetCurRoundRewardCfg()
	if not data_list then
		return
	end

	local cur_location = VientianeTianyinData.Instance:GetCurLocation()
	local cur_value = cur_location - 1
	if cur_location > 1 then
		self.node_list["Suit_Slider"].slider.value = SLDER_VALUE[cur_value]
	end
end

function VientianeTianyinView:FlushEndTime()
	local time, next_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE
	.RAND_ACTIVITY_TYPE_OA_WANXIAN_TIANYIN)
	if CountDownManager.Instance:HasCountDown("vientiane_end_time") then
		CountDownManager.Instance:RemoveCountDown("vientiane_end_time")
	end
	if time > 0 then
		CountDownManager.Instance:AddCountDown("vientiane_end_time",
			BindTool.Bind(self.UpdateCountDown, self),
			BindTool.Bind(self.OnComplete, self),
			nil, time, 1)
	else
		self:OnComplete()
	end
end

function VientianeTianyinView:UpdateCountDown(elapse_time, total_time)
	local time = math.ceil(total_time - elapse_time)
	local time_str = TimeUtil.FormatSecondDHM8(time)
	self.node_list["activity_time"].text.text = string.format(Language.Vientiane.VientianeActTime, time_str)
end

function VientianeTianyinView:OnComplete()
	self.node_list.activity_time.text.text = ""
	self:Close()
end

function VientianeTianyinView:OnBtnfreeBtn()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE
	.RAND_ACTIVITY_TYPE_OA_WANXIAN_TIANYIN)
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		local is_buy_free = VientianeTianyinData.Instance:GetShopIsBuyFlag()
		if is_buy_free then
			TipWGCtrl.Instance:ShowSystemMsg(Language.GoldStoneBuy.AllFreeShopBuy)
			return
		end

		ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_WANXIAN_TIANYIN,
			OA_WANXIAN_TIANYIN_INFO_TYPE.DAY_REWARD)
	end
end

function VientianeTianyinView:OnClickObtain()
	FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge)
end

function VientianeTianyinView:FlushSuitModel()
	if not self.role_model then
		return
	end

	local data_list = VientianeTianyinData.Instance:GetActivationPartList()
	if not data_list then
		return
	end

	--清理掉回调
	-- self:ClearFootEff()
	self.role_model:RemoveAllModel()

	self.node_list["lc_root"]:SetActive(false)
	self.node_list["xw_root"]:SetActive(false)
	self.node_list["mount_root"]:SetActive(false)

	-- self.is_foot_view = false
	self.body_res_id = AppearanceWGData.Instance:GetRoleResId()
	self.mount_res_id = 0
	self.have_foot_print = false
	local has_fashion_show = false
	local res_id, fashion_cfg
	for k, data in pairs(data_list) do
		if data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.BODY then -- 时装大类
			fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
			if fashion_cfg then                                                          -- 时装
				local prof = GameVoManager.Instance:GetMainRoleVo().prof
				self.body_res_id = ResPath.GetFashionModelId(prof, fashion_cfg.resouce)
				has_fashion_show = true
			end
		elseif data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.FOOT then -- 足迹
			self.have_foot_print = true
		end
	end

	local d_body_res, d_hair_res, d_face_res
	local main_role = not has_fashion_show and Scene.Instance:GetMainRole()
	local vo = main_role and main_role:GetVo()
	if not has_fashion_show and vo and vo.appearance then
		if vo.appearance.fashion_body == 0 then
			d_body_res = vo.appearance.default_body_res_id
			d_hair_res = vo.appearance.default_hair_res_id
			d_face_res = vo.appearance.default_face_res_id
		end
	end

	local animation_name = self.have_foot_print and SceneObjAnimator.Move or SceneObjAnimator.Walk
	local extra_role_model_data = {
        d_face_res = d_face_res,
        d_hair_res = d_hair_res,
		d_body_res = d_body_res,
		animation_name = animation_name,
    }
	self.role_model:SetRoleResid(self.body_res_id, nil, extra_role_model_data)
	for k, v in pairs(data_list) do
		self:ShowModelByData(v)
	end

	self:ChangeModelShowScale()
end

function VientianeTianyinView:ShowModelByData(data)
	if IsEmptyTable(data) then
		return
	end
	local res_id, fashion_cfg
	if data.type == WARDROBE_PART_TYPE.FASHION then -- 时装大类
		fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
		if fashion_cfg then
			res_id = fashion_cfg.resouce
			if data.param1 == SHIZHUANG_TYPE.MASK then -- 脸饰
				self.role_model:SetMaskResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.BELT then -- 腰饰
				self.role_model:SetWaistResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.WEIBA then -- 尾巴
				self.role_model:SetTailResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.SHOUHUAN then -- 手环
				self.role_model:SetShouHuanResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.HALO then -- 光环
				self.role_model:SetHaloResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.WING then -- 羽翼
				self.role_model:SetWingResid(res_id, true)
			elseif data.param1 == SHIZHUANG_TYPE.FABAO then -- 法宝
				self.role_model:SetBaoJuResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.JIANZHEN then -- 剑阵
				self.role_model:SetJianZhenResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.SHENBING then -- 武器
				res_id = AppearanceWGData.GetFashionWeaponIdByResViewId(res_id)
				self.role_model:SetWeaponResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.FOOT then -- 足迹
				self.role_model:SetFootTrailModel(res_id)
				self.role_model:SetRotation(MODEL_ROTATION_TYPE.FOOT)

				-- self.is_foot_view = true
				-- self.foot_effect_id = res_id
				-- if not self.use_update then
				-- 	Runner.Instance:AddRunObj(self, 8)
				-- 	self.use_update = true
				-- end
			end
		end
	elseif data.type == WARDROBE_PART_TYPE.LING_CHONG then -- 灵宠
		fashion_cfg = NewAppearanceWGData.Instance:GetLingChongActCfgByImageId(data.param1)
		if fashion_cfg then
			self:SetLingChongModelData(WARDROBE_PART_TYPE.LING_CHONG, fashion_cfg.appe_image_id)
		end
	elseif data.type == WARDROBE_PART_TYPE.XIAN_WA then -- 仙娃
		fashion_cfg = MarryWGData.Instance:GetActiveCfgByTypeAndId(data.param1, data.param2)
		if fashion_cfg then
			self:SetXianWaModelData(fashion_cfg.appe_image_id)
		end
	elseif data.type == WARDROBE_PART_TYPE.MOUNT then -- 坐骑
		fashion_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(data.param1)
		if fashion_cfg then
			self:SetMountModelData(fashion_cfg.appe_image_id)
		end
	elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then -- 化鲲
		fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
		if fashion_cfg then
			self:SetMountModelData(fashion_cfg.active_id)
		end
	end
end

function VientianeTianyinView:SetLingChongModelData(type, res_id)
	self.node_list["lc_root"]:SetActive(true)
	if nil == self.lingchong_model then
		self.lingchong_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["lc_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.S,
			can_drag = false,
		}
		
		self.lingchong_model:SetRenderTexUI3DModel(display_data)
		-- self.lingchong_model:SetUI3DModel(self.node_list["lc_display"].transform,
		-- 	nil, 1, true, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.lingchong_model)
	else
		if self.lingchong_model then
			self.lingchong_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetPetModel(res_id)
	self.lingchong_model:SetMainAsset(bundle, asset, function()
		self.lingchong_model:PlaySoulAction()
	end)

	self.lingchong_model:FixToOrthographic(self.root_node_transform)
end

function VientianeTianyinView:SetXianWaModelData(res_id)
	self.node_list["xw_root"]:SetActive(true)
	if nil == self.xianwa_model then
		self.xianwa_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["xw_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.S,
			can_drag = false,
		}
		
		self.xianwa_model:SetRenderTexUI3DModel(display_data)
		-- self.xianwa_model:SetUI3DModel(self.node_list["xw_display"].transform,
		-- 	nil, 1, true, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.xianwa_model)
	else
		if self.xianwa_model then
			self.xianwa_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetHaiZiModel(res_id)
	self.xianwa_model:SetMainAsset(bundle, asset, function()
		self.xianwa_model:PlaySoulAction()
	end)

	self.xianwa_model:FixToOrthographic(self.root_node_transform)
end

function VientianeTianyinView:SetMountModelData(res_id)
	self.node_list["mount_root"]:SetActive(true)
	if nil == self.mount_model then
		self.mount_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["mount_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = false,
		}
		
		self.mount_model:SetRenderTexUI3DModel(display_data)
		-- self.mount_model:SetUI3DModel(self.node_list["mount_display"].transform,
		-- 	nil, 1, true, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.mount_model)
	else
		if self.mount_model then
			self.mount_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetMountModel(res_id)
	self.mount_model:SetMainAsset(bundle, asset, function()
		self.mount_model:PlayMountAction()
	end)

	self.mount_model:FixToOrthographic(self.root_node_transform)
end

-- function VientianeTianyinView:Update(now_time, elapse_time)
-- 	if not self.is_foot_view then
-- 		return
-- 	end

-- 	if self.next_create_footprint_time == 0 then
-- 		self:CreateFootPrint()
-- 		self.next_create_footprint_time = Status.NowTime + COMMON_CONSTS.FOOTPRINT_CREATE_GAP_TIME
-- 	end

-- 	if self.next_create_footprint_time == nil then --初生时也是位置改变，不播
-- 		self.next_create_footprint_time = 0
-- 	end

-- 	if self.next_create_footprint_time > 0 and now_time >= self.next_create_footprint_time then
-- 		self.next_create_footprint_time = 0
-- 	end

-- 	self:UpdateFootprintPos()
-- end

-- function VientianeTianyinView:CreateFootPrint()
-- 	if nil == self.foot_effect_id then
-- 		return
-- 	end

-- 	if nil == self.footprint_eff_t then
-- 		self.footprint_eff_t = {}
-- 	end

-- 	local pos = self.role_model.draw_obj:GetRoot().transform
-- 	local bundle, asset = ResPath.GetUIFootEffect(self.foot_effect_id)
-- 	EffectManager.Instance:PlayControlEffect(self, bundle, asset,
-- 		Vector3(pos.position.x, pos.position.y, pos.position.z), nil, pos, nil, function(obj)
-- 		if obj then
-- 			if nil ~= obj then
-- 				if self.role_model then
-- 					obj.transform.localPosition = Vector3.zero
-- 					obj:SetActive(false)
-- 					obj:SetActive(true)
-- 					table.insert(self.footprint_eff_t, { obj = obj, role_model = self.role_model })
-- 					self.role_model:OnAddGameobject(obj)
-- 				else
-- 					ResPoolMgr:Release(obj)
-- 				end
-- 			end
-- 		end
-- 	end)

-- 	if #self.footprint_eff_t > 2 then
-- 		local obj = table.remove(self.footprint_eff_t, 1)
-- 		obj.role_model:OnRemoveGameObject(obj.obj)
-- 		if not IsNil(obj.obj) then
-- 			obj.obj:SetActive(false)
-- 		end
-- 	end
-- end

-- function VientianeTianyinView:UpdateFootprintPos()
-- 	if nil == self.footprint_eff_t then
-- 		return
-- 	end

-- 	for k, v in pairs(self.footprint_eff_t) do
-- 		if not IsNil(v.obj) then
-- 			local pos = v.obj.transform.localPosition
-- 			v.obj.transform.localPosition = Vector3(pos.x, pos.y, pos.z - 0.16)
-- 		end
-- 	end
-- end

-- function VientianeTianyinView:ClearFootEff()
-- 	if self.footprint_eff_t ~= nil then
-- 		for k, v in pairs(self.footprint_eff_t) do
-- 			if v.obj ~= nil and not IsNil(v.obj) and v.role_model ~= nil then
-- 				v.role_model:OnRemoveGameObject(v.obj)
-- 				v.obj:SetActive(false)
-- 			end
-- 		end
-- 	end

-- 	self.footprint_eff_t = {}
-- end

function VientianeTianyinView:ChangeModelShowScale()
	local data = VientianeTianyinData.Instance:GetCurRoundModelCfg()
	if IsEmptyTable(data) then
		return
	end

	local pos_str = data.main_pos
	if pos_str and pos_str ~= "" then
		local pos = Split(pos_str, "|")
		RectTransform.SetAnchoredPosition3DXYZ(self.node_list.ph_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
	end

	local rotate_str = data.main_rot
	if rotate_str and rotate_str ~= "" then
		local rot = Split(rotate_str, "|")
		if self.role_model then
			self.role_model:SetRotation(u3dpool.vec3(rot[1] or 0, rot[2] or 0, rot[3] or 0))
		end
	end

	local scale = data.main_scale
	if scale and scale ~= "" then
		RectTransform.SetLocalScale(self.node_list.ph_display.rect, scale)
	end

	--灵宠
	if self.node_list["lc_root"]:GetActive() then
		pos_str = data.pet_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			RectTransform.SetAnchoredPosition3DXYZ(self.node_list.lc_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end

		rotate_str = data.pet_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.lingchong_model then
				self.lingchong_model:SetRotation(u3dpool.vec3(rot[1] or 0, rot[2] or 0, rot[3] or 0))
			end
		end

		scale = data.lc_scale
		if scale and scale ~= "" then
			RectTransform.SetLocalScale(self.node_list.lc_display.rect, scale)
		end
	end

	--仙娃
	if self.node_list["xw_root"]:GetActive() then
		pos_str = data.xw_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			RectTransform.SetAnchoredPosition3DXYZ(self.node_list.xw_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end

		rotate_str = data.xw_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.xianwa_model then
				self.xianwa_model:SetRotation(u3dpool.vec3(rot[1] or 0, rot[2] or 0, rot[3] or 0))
			end
		end

		scale = data.xw_scale
		if scale and scale ~= "" then
			RectTransform.SetLocalScale(self.node_list.xw_display.rect, scale)
		end
	end

	--坐骑
	if self.node_list["mount_display"]:GetActive() then
		pos_str = data.mount_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			RectTransform.SetAnchoredPosition3DXYZ(self.node_list.mount_display.rect, pos[1] or 0, pos[2] or 0,
				pos[3] or 0)
		end

		rotate_str = data.mount_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.mount_model then
				self.mount_model:SetRotation(u3dpool.vec3(rot[1] or 0, rot[2] or 0, rot[3] or 0))
			end
		end

		scale = data.mount_scale
		if scale and scale ~= "" then
			RectTransform.SetLocalScale(self.node_list.mount_display.rect, scale)
		end
	end
end

-------------------VientianeCell-------------------
VientianeCell = VientianeCell or BaseClass(BaseRender)

function VientianeCell:__init()
	self.reward_item = ItemCell.New(self.node_list["tianyin_item"])
	self.node_list["get_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGetReward, self))
end

function VientianeCell:__delete()
	if self.reward_item then
		self.reward_item:DeleteMe()
		self.reward_item = nil
	end
end

function VientianeCell:OnFlush()
	if not self.data then
		return
	end

	local item_id = self.data.item.item_id
	local num = self.data.item.num
	self.reward_item:SetData({ item_id = item_id, num = num })

	local is_get = VientianeTianyinData.Instance:GetRewardStateBySeq(self.data.seq)
	self.node_list.tianyin_is_get:SetActive(is_get)
	self.node_list.tianyin_integral.text.text = self.data.need_score

	local score = VientianeTianyinData.Instance:GetCurScore()
	local need_score = self.data.need_score
	self.node_list.get_btn:SetActive(need_score <= score)

	if not is_get and need_score <= score then
		self.node_list.is_remind:SetActive(true)
	else
		self.node_list.is_remind:SetActive(false)
	end
end

function VientianeCell:OnClickGetReward()
	if not self.data then
		return
	end

	local is_get = VientianeTianyinData.Instance:GetRewardStateBySeq(self.data.seq)
	local score = VientianeTianyinData.Instance:GetCurScore()
	local need_score = self.data.need_score
	if is_get then
		self.node_list.tianyin_is_get:SetActive(true)
		TipWGCtrl.Instance:ShowSystemMsg(Language.Vientiane.VientianeIsGetReward)
		return
	end

	if score >= need_score then
		ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_WANXIAN_TIANYIN,
			OA_WANXIAN_TIANYIN_INFO_TYPE.REWARD, self.data.seq)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.Vientiane.VientianeScoreLack)
	end
end
