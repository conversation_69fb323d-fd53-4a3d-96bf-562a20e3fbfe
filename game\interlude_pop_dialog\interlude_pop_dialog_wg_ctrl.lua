require("game/interlude_pop_dialog/interlude_pop_dialog_wg_data")
require("game/interlude_pop_dialog/interlude_pop_dialog_view")

-- 新手弹窗对话控制
InterludePopDialogWGCtrl = InterludePopDialogWGCtrl or BaseClass(BaseWGCtrl)
function InterludePopDialogWGCtrl:__init()
	if InterludePopDialogWGCtrl.Instance ~= nil then
		ErrorLog("[InterludePopDialogWGCtrl] attempt to create singleton twice!")
		return
	end
	
	InterludePopDialogWGCtrl.Instance = self
	self.data = InterludePopDialogWGData.New()
	self.view = InterludePopDialogView.New(GuideModuleName.InterludePopDialogView)
end

function InterludePopDialogWGCtrl:__delete()
	InterludePopDialogWGCtrl.Instance = nil

	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil
end

-- 关闭当前界面
function InterludePopDialogWGCtrl:CloseInterludePopDialogView()
	if self.view and self.view:IsOpen() then
		self.view:Close()
	end
end

-- 打开当前界面
function InterludePopDialogWGCtrl:OpenInterludePopDialogView(interlude_id)
	self:CloseInterludePopDialogView()
	if self.view then
		self.view:SetInterludeId(interlude_id)
		self.view:Open()
	end
end
