MysteryPointShopView = MysteryPointShopView or BaseClass(SafeBaseView)

function MysteryPointShopView:__init()
    self.view_layer = UiLayer.Normal
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -6), sizeDelta = Vector2(1006, 586)})
    self:AddViewResource(0, "uis/view/mystery_box_ui_prefab", "layout_mystery_point_shop_view")
    self:SetMaskBg(true, true)
end

function MysteryPointShopView:ReleaseCallBack()
    if self.store_grid_list then
        self.store_grid_list:DeleteMe()
        self.store_grid_list = nil
    end
    self.shop_type = nil
end

function MysteryPointShopView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.MysteryBox.StoreTitle
    if not self.store_grid_list then
		self.store_grid_list = AsyncBaseGrid.New()         
        local bundle = "uis/view/mystery_box_ui_prefab"
		local asset = "point_shop_item_render"        
        self.store_grid_list:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list["ph_store_grid"],
        assetBundle = bundle, assetName = asset, itemRender = MysteryShopItemCell})
        self.store_grid_list:SetStartZeroIndex(false)
    end
end

function MysteryPointShopView:OnFlush()
    local data_list = MysteryBoxWGData.Instance:GetShopDataList()
    if IsEmptyTable(data_list) then
        return
    end
    -- print_error(data_list)
    self.store_grid_list:SetDataList(data_list)
    self.node_list.score_text.text.text = MysteryBoxWGData.Instance:GetScore()

    local item_id = MysteryBoxWGData.Instance:GetCoverItemId()
    self.node_list.covert_icon.image:LoadSprite(ResPath.GetItem(item_id))
end

----------------------------------------------------------------------------------
MysteryShopItemCell = MysteryShopItemCell or BaseClass(BaseRender)

function MysteryShopItemCell:__delete()
    if self.item_info then
        self.item_info:DeleteMe()
        self.item_info = nil
    end
end

function MysteryShopItemCell:LoadCallBack()
    self.node_list.btn_buy.button:AddClickListener(BindTool.Bind(self.OnClickBuy, self))
end

function MysteryShopItemCell:OnFlush()
	if not self.data then
		return
    end
    if not self.node_list.btn_buy then
		return
    end
    if not self.item_info then
        self.item_info = ItemCell.New(self.node_list.cell_pos)
    end
   
    self.item_info:SetData(self.data.reward_item[0])
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.reward_item[0].item_id)
    self.node_list.name_text.text.text = item_cfg.name

    local score = MysteryBoxWGData.Instance:GetScore()
    local color = score >= self.data.need_score and COLOR3B.D_GREEN or COLOR3B.D_RED
    self.node_list.price_text.text.text = ToColorStr(self.data.need_score, color)
end

function MysteryShopItemCell:OnClickBuy()
    local score = MysteryBoxWGData.Instance:GetScore()
    local buy_num = MysteryBoxWGData.Instance:GetShopCovertNum(self.data.seq)
    if score >= self.data.need_score and buy_num < self.data.limit then
        local opera_type = MYSTERY_BOX_OPERATE_TYPE.CONVERT
        MysteryBoxWGCtrl.Instance:SendReq(opera_type, self.data.seq)
    end
end