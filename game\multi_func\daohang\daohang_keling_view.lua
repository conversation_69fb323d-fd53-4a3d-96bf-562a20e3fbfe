function MultiFunctionView:ShowDaoHangKeLingCallBack()
    self:RightPanleShowTween(self.node_list.keling_right_tween_root, self.node_list.keling_mid)
end

function MultiFunctionView:LoadDaoHangKeLingCallBack()
    if not self.keling_item_list then
        self.keling_item_list = {}

        for i = 0, 11 do
            self.keling_item_list[i] = DaqoHangKeLingItemRender.New(self.node_list["keling_item" .. i])
        end
    end

    if not self.keling_attr_list then
        self.keling_attr_list = AsyncListView.New(DaqoHangKeLingAttrItemRender, self.node_list.keling_attr_list)
    end

    XUI.AddClickEventListener(self.node_list.btn_keling_onekeywear, BindTool.Bind(self.OnClickKeLingOneKeyWear, self))
    XUI.AddClickEventListener(self.node_list.btn_keling_resonance, BindTool.Bind(self.OnClickKeLingResonance, self))
end

function MultiFunctionView:ReleaseKeLingCallBack()
    if self.keling_item_list then
        for k, v in pairs(self.keling_item_list) do
            v:DeleteMe()
        end

        self.keling_item_list = nil
    end

    if self.keling_attr_list then
        self.keling_attr_list:DeleteMe()
        self.keling_attr_list = nil
    end
end

function MultiFunctionView:OnFlushDaoHangKeLing()
    local remind = MultiFunctionWGData.Instance:GetDaoHangResonanceRemind()
    local data_list = MultiFunctionWGData.Instance:GetDaoHangKelingItemDataList()
    local level, attr_list = MultiFunctionWGData.Instance:GetDaoHangKeLingLevelAndAttr()
    local keling_onekeywear_remind = not IsEmptyTable(MultiFunctionWGData.Instance:GetDaoHangKeLingOneKeyData())

    for i = 0, 11 do
        local data = data_list and data_list[i] or {}
        self.keling_item_list[i]:SetData(data)
    end

    self.keling_attr_list:SetDataList(attr_list)
    self.node_list.btn_keling_resonance_remind:CustomSetActive(remind)
    self.node_list.btn_keling_onekeywear_remind:CustomSetActive(keling_onekeywear_remind)
    self.node_list.keling_current_level.text.text = string.format(Language.Common.LevelNormal, level)

    local cap = MultiFunctionWGData.Instance:GetDaoHangKeLingCap()
    self.node_list.keling_cap_value.text.text = cap or 0
end

function MultiFunctionView:OnClickKeLingOneKeyWear()
    local data_list = MultiFunctionWGData.Instance:GetDaoHangKeLingOneKeyData()

    if IsEmptyTable(data_list) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Charm.DaoHangKeLingCannotInlay)
    else
        MultiFunctionWGCtrl.Instance:OnCSTaiostUseStone(#data_list, data_list)
    end
end

function MultiFunctionView:OnClickKeLingResonance()
    local resonance_data = MultiFunctionWGData.Instance:GetDaoHangKeLingResonanceData()
    MultiFunctionWGCtrl.Instance:ShowResonanceView(resonance_data)
end

---------------------------------------DaqoHangKeLingAttrItemRender------------------------------
DaqoHangKeLingAttrItemRender = DaqoHangKeLingAttrItemRender or BaseClass(BaseRender)

function DaqoHangKeLingAttrItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.attr_name.text.text = self.data.attr_name or ""
    self.node_list.attr_value.text.text = self.data.value_str or ""
end

------------------------------------------DaqoHangKeLingItemRender----------------------------------------
DaqoHangKeLingItemRender = DaqoHangKeLingItemRender or BaseClass(BaseRender)

function DaqoHangKeLingItemRender:__init()
    self.cal_data = {}
end

function DaqoHangKeLingItemRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.keling_item, BindTool.Bind(self.OnClickKeLingItem, self))
end

function DaqoHangKeLingItemRender:__delete()
    self:CancelTween()
    self.cal_data = nil
end

function DaqoHangKeLingItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local can_change = false
    local can_up = false
    local is_open = self.data.is_open == 1
    local item_id = self.data.item_id
    self.node_list.lock:CustomSetActive(not is_open)

    if is_open and item_id > 0 then
        local icon_id = ItemWGData.Instance:GetItemIconByItemId(item_id)
        local bundle, assert = ResPath.GetItem(icon_id)
        self.node_list.icon.image:LoadSprite(bundle, assert, function ()
            self.node_list.icon.image:SetNativeSize()
        end)
    end

    self.cal_data = MultiFunctionWGData.Instance:GetDaoHangKelingInfo(self.data.slot)
    if not IsEmptyTable(self.cal_data) then
        can_change = self.cal_data.can_change
        can_up = self.cal_data.can_up
    end

    self.node_list.icon:CustomSetActive(is_open and item_id > 0)
    self.node_list.effect:CustomSetActive(is_open and (can_change or can_up))
    self.node_list.add:CustomSetActive(is_open and item_id <= 0)
    self:SetBaoShiRemind(is_open and (can_change or can_up))
end

function DaqoHangKeLingItemRender:CancelTween()
    if self.arrow_tweener then
		self.arrow_tweener:Kill()
		self.arrow_tweener = nil
	end
end

function DaqoHangKeLingItemRender:SetBaoShiRemind(enable)
	self.node_list["up_flag"]:SetActive(enable)
    self:CancelTween()

	if enable then
		RectTransform.SetAnchoredPositionXY(self.node_list["up_flag"].rect, 0, 0)
		self.arrow_tweener = self.node_list["up_flag"].gameObject.transform:DOAnchorPosY(10, 0.45)
		self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
		self.arrow_tweener:SetEase(DG.Tweening.Ease.InCubic)
	end
end

function DaqoHangKeLingItemRender:OnClickKeLingItem()
    if IsEmptyTable(self.data) then
        return
    end

    local is_open = self.data.is_open == 1
    if not is_open then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Charm.DaoHangKeLingSlotLock)
        return
    end

    if not IsEmptyTable(self.cal_data) then
        if self.cal_data.can_change then
            if not IsEmptyTable(self.cal_data.change_data_list) then
                MultiFunctionWGCtrl.Instance:OpenDaoHangKeYinInlayStoreView(self.data.slot, self.cal_data.change_data_list)
            end
        elseif self.cal_data.can_up then
            MultiFunctionWGCtrl.Instance:OpenDaoHangKeYinUpStoreView(self.cal_data)
        else
            local item_id = self.data.item_id

            if item_id > 0 then
                TipWGCtrl.Instance:OpenItem({item_id = item_id})
            else
                local type = MultiFunctionWGData.Instance:GetDaoHangKeLingTypeBySlot(self.data.slot)
                local default_cfg = MultiFunctionWGData.Instance:GetDaoHangkeLingTypeDefaultStoreCfg(type)
                TipWGCtrl.Instance:OpenItem({item_id = default_cfg.item_id})
                SysMsgWGCtrl.Instance:ErrorRemind(Language.Charm.DaoHangKeLingCannotInlay)
            end
        end
    end
end