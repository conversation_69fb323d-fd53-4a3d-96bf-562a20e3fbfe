-- 通天降临-奕者谋定

local SCHEME_STATUS =
{
	Ready = 0,  -- 准备
	Open = 1,   -- 开启中
	Result = 2, -- 结算
}

local TEAM_MAX_COUNT = 3

function WorldTreasureView:SchemeReleaseCallBack()
	if self.scheme_reward_list then
		self.scheme_reward_list:DeleteMe()
		self.scheme_reward_list = nil
	end

	if self.scheme_team_list then
		for k, v in pairs(self.scheme_team_list) do
			v:DeleteMe()
		end
		self.scheme_team_list = nil
	end
end

function WorldTreasureView:SchemeLoadCallBack()
	if not self.scheme_team_list then
		self.scheme_team_list = {}
		for i = 0, TEAM_MAX_COUNT - 1 do
			self.scheme_team_list[i] = SchemeCampItem.New(self.node_list["scheme_camp_" .. i])
		end
	end

	if not self.scheme_reward_list then
		self.scheme_reward_list = AsyncListView.New(ItemCell, self.node_list["scheme_reward_list"])
		self.scheme_reward_list:SetStartZeroIndex(true)
	end

	self.node_list["sc_txt_rule_tips"].text.text = Language.WorldTreasure.SchemeRule
end

function WorldTreasureView:SchemeShowIndexCallBack()
	WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_SCHEME_INFO)
end

function WorldTreasureView:SchemeOnFlush(param_t)
	for k, v in pairs(self.scheme_team_list) do
		v:SetData({seq = k})
	end

	local reward_list = WorldTreasureWGData.Instance:GetSchemeRewardList()
	self.scheme_reward_list:SetDataList(reward_list)
end

--------------------------------
-- 奕者谋定阵营Item
--------------------------------
SchemeCampItem = SchemeCampItem or BaseClass(BaseRender)

function SchemeCampItem:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_join"], BindTool.Bind1(self.OnClickJoinBtn, self))
end

function SchemeCampItem:ReleaseCallBack()
end

function SchemeCampItem:OnFlush()
	if not self.data then
		return
	end

	local status = WorldTreasureWGData.Instance:GetSchemeStatus()
	local my_team_seq = WorldTreasureWGData.Instance:GetSchemeMyTeamSeq()
	local is_join_team = my_team_seq ~= -1

	self.node_list["btn_join"]:SetActive(not is_join_team and status ~= SCHEME_STATUS.Result)
	local can_join = status == SCHEME_STATUS.Open and not is_join_team
	XUI.SetButtonEnabled(self.node_list["btn_join"], can_join)
	self.node_list["join_btn_effect"]:SetActive(can_join)

	self.node_list["cur_info"]:SetActive(is_join_team or status == SCHEME_STATUS.Result)

	local team_member_num = WorldTreasureWGData.Instance:GetSchemeMemberNumBySeq(self.data.seq)
	self.node_list["txt_cur_member"].text.text = team_member_num

	local is_win = false
	if status == SCHEME_STATUS.Result then
		is_win = WorldTreasureWGData.Instance:GetSchemeTeamIsWinBySeq(self.data.seq)
		local scale = is_win and 1 or 0.8
		self.node_list["cur_info"].transform.localScale = Vector3(scale, scale, scale)
		self.node_list["mask"]:SetActive(not is_win)
	else
		self.node_list["cur_info"].transform.localScale = Vector3(1, 1, 1)
		self.node_list["mask"]:SetActive(false)
	end
	self.node_list["img_win_flag"]:SetActive(is_win)
	local is_my_team = my_team_seq == self.data.seq
	self.node_list["my_team_flag"]:SetActive(is_my_team)
	self.node_list["camp_effect"]:SetActive(is_win or (status == SCHEME_STATUS.Open and is_my_team))
end

function SchemeCampItem:OnClickJoinBtn()
	WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_SCHEME_CHOOSE_TEAM, self.data.seq)
end
