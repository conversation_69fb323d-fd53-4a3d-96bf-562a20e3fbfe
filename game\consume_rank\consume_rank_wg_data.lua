ConsumeRankWGData = ConsumeRankWGData or BaseClass()
function ConsumeRankWGData:__init()
    if ConsumeRankWGData.Instance then
        Error<PERSON><PERSON>("[ConsumeRankWGData] attempt to create singleton twice!")
        return
    end
    ConsumeRankWGData.Instance =self

    local cfg = ConfigManager.Instance:GetAutoConfig("oa_consume_gold_rank_config_auto")
    self.consume_rank_raward_cfg = ListToMapList(cfg.reward, "grade")
	self.grade_cfg = cfg.grade
	self.jump_cfg = ListToMapList(cfg.jump, "grade")
    self.consume_rank_list = {}

	self.rank_show_list = {}
	RemindManager.Instance:Register(RemindName.LocalConsumeRank, BindTool.Bind(self.IsShowConsumeRankRedPoint, self))
end

function ConsumeRankWGData:__delete()
	self.rank_show_list = nil
    ConsumeRankWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.LocalConsumeRank)
end

-- 获取当前档位排行配置
function ConsumeRankWGData:GetCurGradeRewardCfg()
	local grade = self:GetCurGrade()
	return self.consume_rank_raward_cfg[grade]
end

-- 获取跳转配置
function ConsumeRankWGData:GetCurJumpCfg()
	local grade = self:GetCurGrade()
	return self.jump_cfg[grade]
end

--设置消费榜信息
function ConsumeRankWGData:SetConsumeRankInfo(rank_list)
	self.consume_rank_list = rank_list
end

--设置活动信息
function ConsumeRankWGData:SetConsumeRankBaseInfo(protocol)
	self.consume_num = protocol.consume_num							-- 消费数
	self.daily_reward_flag = protocol.daily_reward_flag				-- 每日奖励标记 0: 未领取 1: 已领取
end

-- 获取自己的消费数
function ConsumeRankWGData:GetSelfConsumeNum()
	return self.consume_num or 0
end

-- 获取每日奖励标记
function ConsumeRankWGData:GetDailyGiftRewardFlag()
	return self.daily_reward_flag or 0
end

-- 获取排行榜信息
function ConsumeRankWGData:GetRankList()
	return self.consume_rank_list
end

-- 获取排行榜信息
function ConsumeRankWGData:GetRankInfoByRank(rank)
	return (self.consume_rank_list or {})[rank]
end

-- 获取自己的排行信息
function ConsumeRankWGData:GetSelfRankInfo()
	local self_data = RankWGData.Instance:GetSelfValueData(RankKind.Person, PROFESS_RANK_TYPE.PERSON_RANK_TYPE_OA_CONSUME_RANK)
	return self_data
end

--获取自己的排名
function ConsumeRankWGData:GetSelfRankNum()
	return self:GetSelfRankInfo().self_rank
end

-- 获取当前档位
function ConsumeRankWGData:GetCurGrade()
	local open_day = ActivityWGData.Instance:GetActivityOpenInServerDay(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CONSUME_RANK)
	local grade = 1
	for k, v in pairs(self.grade_cfg) do
		if open_day >= v.min_open_day and open_day <= v.max_open_day then
			grade = v.grade
			break
		end
	end
	return grade
end

--获取排行列表配置
function ConsumeRankWGData:GetRankShowList()
	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CONSUME_RANK) then
		self.rank_show_list = {}
		return self.rank_show_list
	end

	local rank_show_list = {}
	rank_show_list = self.rank_show_list

	if IsEmptyTable(rank_show_list) then
		local cfg = self:GetCurGradeRewardCfg()
		if cfg then
			local min_rank = cfg[1].min_rank or 1
			local max_rank = cfg[#cfg].max_rank or 0
			for i = min_rank, max_rank do
				for k, v in ipairs(cfg) do
					if v.min_rank <= i and i <= v.max_rank then
						local data = {}
						data.rank = i
						data.reward_item = v.reward_item
						data.reach_value = v.reach_value
						table.insert(rank_show_list, data)
					end
				end
			end
		end
	end

	return rank_show_list
end

--获取上升排行的差值
function ConsumeRankWGData:GetUpRankCondition()
	local my_rank_data = self:GetSelfRankInfo()
	if my_rank_data.self_rank == 1 then -- 已是第一
		return 0
	end

	local rank_list = self:GetRankList()
	local cfg = self:GetCurGradeRewardCfg()

	local target_rank = my_rank_data.self_rank == 0 and cfg[#cfg].max_rank or my_rank_data.self_rank - 1
	local target_value = (rank_list[target_rank] or {}).rank_value
	if not target_value or target_value == 0 then
		for i, v in ipairs(cfg) do
			if target_rank >= v.min_rank and target_rank <= v.max_rank then
				target_value = v.reach_value
				break
			end
		end
		target_value = target_value or cfg[#cfg].reach_value --兼容
	else
		target_value = target_value + 1 -- 高1才能顶下去
	end
	return target_value - my_rank_data.self_value
end

--主界面显示气泡
function ConsumeRankWGData:GetMainUiIsShowConsumeRank()
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "MainUiIsShowConsumeRank")
	if not PlayerPrefsUtil.HasKey(key) then -- 默认显示
		--开服冲榜的气泡，玩家等级200级后默认勾选，如果玩家手动点掉了那就不显示，200级前默认不勾选
		local level = RoleWGData.Instance:GetRoleLevel()
		if level >= 200 then
			PlayerPrefsUtil.SetInt(key, 1)
			return true
		else
			PlayerPrefsUtil.SetInt(key, 0)
			return false
		end
		
	end
	local flag = PlayerPrefsUtil.GetInt(key)
	return flag == 1
end

--设置主界面气泡显示
function ConsumeRankWGData:SetMainUiIsShowConsumeRank(flag)
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "MainUiIsShowConsumeRank")
	PlayerPrefsUtil.SetInt(key, flag)
	ConsumeRankWGCtrl.Instance:RuningRequesFlushConsumeRankInfo()
end

--跨天
function ConsumeRankWGData:OnDayChange()
	self.rank_show_list = {}
end

-- 红点
function ConsumeRankWGData:IsShowConsumeRankRedPoint()
	-- 每日礼包
	if self:GetDailyGiftRewardFlag() == 0 then
		return 1
	end

	return 0
end