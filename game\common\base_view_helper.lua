--[[
Author: error: git config user.name && git config user.email & please set dead value or install git
Date: 2022-09-28 12:47:42
LastEditors: error: git config user.name && git config user.email & please set dead value or install git
LastEditTime: 2022-10-09 20:26:04
FilePath: \Lua\game\common\base_view_helper.lua
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
--]]
local BaseViewHelper = {}

-- 计算要显示showIdnex
function BaseViewHelper:CalcShowIndex(default_index, remind_tab, tab_sub, view_name)
	local red_index = self:CalcIndexInRed(remind_tab, tab_sub)
	if nil ~= red_index then
		return red_index
	end

	local show_index = self:CalcDefaultIndexInFunOpen(default_index, view_name)
	return show_index
end

-- 计算第一个有红点的索引
function BaseViewHelper:CalcIndexInRed(remind_tab, tab_sub)
	local num = 0
	local index = nil
	if not remind_tab or not tab_sub then return index end
	for k,v in ipairs(remind_tab) do
		for k2,v2 in pairs(v) do
			num = RemindManager.Instance:GetRemind(v2)
			if num > 0 then
				if not tab_sub[k] then
					index = k * 10
				elseif not tab_sub[k][k2] then
					index = k * 10 + 1
				else
					index = k * 10 + k2
				end
				return index
			end
		end
	end

	return index
end

-- 计算第一个功能已经开启的索引
function BaseViewHelper:CalcDefaultIndexInFunOpen(default_index, view_name)
	local open_list, un_open_list = FunOpen.Instance:GetTabNameListtByModuleName(view_name)
	local min_tab_index = 100000
	local default_index_is_open = false
	local def_flag = false

	for k,v in pairs(open_list) do
		local temp_tab_index = TabIndex[k] or min_tab_index

		if temp_tab_index == default_index then
			default_index_is_open = true
			def_flag = true
			break
		end

		if temp_tab_index < min_tab_index then --记录最小tab索引
			min_tab_index = temp_tab_index
		end
	end

	if not def_flag then
		default_index_is_open = true
	end

	for i, v in pairs(un_open_list) do--默认索引未开启
		if TabIndex[i] == default_index then
			default_index_is_open = false
			break
		end
	end

	if min_tab_index ~= 100000 then
		if default_index_is_open and 0 < default_index then
			return default_index
		else
		 -- 获取最小的索引
			min_tab_index = min_tab_index --得到tabbar真正的索引，1,2,3..（重要）
			if 10 > default_index then ---???
				default_index = min_tab_index
			end
			return min_tab_index
		end
	end

	return default_index
end

function BaseViewHelper:TryInitViewTween(view_name, index, node_list)
	if TweenManager and TweenManager.Instance then
		TweenManager.Instance:ExecuteViewTween(view_name, index, node_list)
	end
end

function BaseViewHelper:TryShowRuleTips(view_name, show_index, node_list)
	local view_rule = ViewRuleWGData.Instance:GetViewRuleCfg(view_name, show_index)

	if view_rule then
		local rule_tip = RuleTip.Instance
		local rule_title = view_rule.rule_title
		local rule_content = view_rule.rule_content
		
		rule_tip:SetTitle(rule_title)
		rule_tip:SetContent(rule_content, nil, nil, nil, true)
	end
end

return BaseViewHelper
