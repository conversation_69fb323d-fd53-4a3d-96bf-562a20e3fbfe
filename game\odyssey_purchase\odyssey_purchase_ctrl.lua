require("game/odyssey_purchase/odyssey_purchase_data")
require("game/odyssey_purchase/odyssey_purchase_view")

OdysseyPurchaseCtrl = OdysseyPurchaseCtrl or BaseClass(BaseWGCtrl)

function OdysseyPurchaseCtrl:__init()
    if OdysseyPurchaseCtrl.Instance then
		error("[OdysseyPurchaseCtrl]:Attempt to create singleton twice!")
	end

    self.data = OdysseyPurchaseData.New()
    self.view = OdysseyPurchaseView.New(GuideModuleName.OdysseyPurchaseView)
	self:RegisterAllProtocols()
end

function OdysseyPurchaseCtrl:__delete()
    self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil
end

function OdysseyPurchaseCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOAALifeLongLoveRmbInfo, "OnOAALifeLongLoveRmbInfo")
end

function OdysseyPurchaseCtrl:OnOAALifeLongLoveRmbInfo(protocol)
    self.data:SetAllOdysseyInfo(protocol)
	self:FlushView()

	RemindManager.Instance:Fire(RemindName.OdysseyPurchaseRemind)
end

function OdysseyPurchaseCtrl:FlushView()
    ViewManager.Instance:FlushView(GuideModuleName.OdysseyPurchaseView)
end
