-- 灵宠选择同星选择界面
ManaBatchBatchSelect = ManaBatchBatchSelect or BaseClass(SafeBaseView)

function ManaBatchBatchSelect:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
                        {vector2 = Vector2(0, -2), sizeDelta = Vector2(706, 484)})
	self:AddViewResource(0, "uis/view/thunder_mana_ui_prefab", "layout_thunder_mana_batch_select")
    self.view_name = "ManaBatchBatchSelect"
    self:SetMaskBg(true)
end

function ManaBatchBatchSelect:ReleaseCallBack()
	if nil ~= self.batch_item_grid then
		self.batch_item_grid:DeleteMe()
    end
    self.batch_item_grid = nil
    self.show_data = nil
    self.ok_callback = nil
    self.need_num = nil
    self.cur_select_num = nil
end

function ManaBatchBatchSelect:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.ThunderMana.ThunderTitleName[4]
    
    self.batch_item_grid = BeastBatchGrid.New(self)
    self.batch_item_grid:SetStartZeroIndex(false)
    self.batch_item_grid:SetIsShowTips(false)
    self.batch_item_grid:SetNoSelectState(false)
    self.batch_item_grid:SetIsMultiSelect(true)                       --  ,change_cells_num = 2
    self.batch_item_grid:CreateCells({col = 8 ,change_cells_num = 2, cell_count = 32,
    list_view = self.node_list["ph_item_grid"], itemRender = ManaBatchBatchSelectRender})
    self.batch_item_grid:SetSelectCallBack(BindTool.Bind(self.SelectShenShouBagCellCallBack, self))

    self.node_list["btn_sure"].button:AddClickListener(BindTool.Bind(self.OnClickSure, self))
    self.node_list["btn_one_key"].button:AddClickListener(BindTool.Bind(self.OnClickOneKey, self))
end

function ManaBatchBatchSelect:SetOkBack(callback)
	self.ok_callback = callback
end

function ManaBatchBatchSelect:SelectShenShouBagCellCallBack(cell)
	self:FlushAddNum()
end

function ManaBatchBatchSelect:SetData(show_data)
    self.show_data = show_data

    if self:IsLoaded() then
        self:Flush()
    end
end

function ManaBatchBatchSelect:OnFlush()
    self:FlushDataList()
    self:FlushAddNum()
end

-- 刷新列表
function ManaBatchBatchSelect:FlushDataList()
    if not self.show_data then
        return
    end

    local special_select_list = {}

    -- 获取当前特殊选取的
    if self.show_data.special_list then
        for _, special_data in ipairs(self.show_data.special_list) do
            if special_data and special_data.cur_item_id then
                
                if not special_select_list[special_data.cur_item_id] then
                    special_select_list[special_data.cur_item_id] = special_data.cur_item_num
                else
                    special_select_list[special_data.cur_item_id] = special_select_list[special_data.cur_item_id]  + special_data.cur_item_num
                end
            end
        end
    end

    -- 获取当前已经选取的材料列表
    local star_select_list = {}
    if self.show_data.star_list then
        for satr, star_list in pairs(self.show_data.star_list) do
            star_select_list[satr] = {}
    
            for i, star_data in ipairs(star_list) do
                if not star_select_list[satr][star_data.cur_item_id] then
                    star_select_list[satr][star_data.cur_item_id] = star_data.cur_item_num
                else
                    star_select_list[satr][star_data.cur_item_id] = star_select_list[satr][star_data.cur_item_id] + star_data.cur_item_num
                end
            end
        end
    end

    local grid_list = {}
    if self.show_data.is_special then
        local item_num = ThunderManaWGData.Instance:GetItemNumInBagById(self.show_data.cur_item_id)
        local item_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(self.show_data.cur_item_id)
        local special_star = item_cfg and item_cfg.star or 0

        if star_select_list[special_star] and star_select_list[special_star][self.show_data.cur_item_id] then
            item_num = item_num - star_select_list[special_star][self.show_data.cur_item_id]
        end

        local get_num = item_num > 32 and 32 or item_num
        for i = 1, get_num do
            local data = {}
            data.item_id = self.show_data.cur_item_id
            data.num = 1
            data.star_num = 0
            table.insert(grid_list, data) 
        end
    else
        local list = ThunderManaWGData.Instance:GetEquipItemListByStar(self.show_data.star_num, self.show_data.aim_item_id)
        for i, item_id in ipairs(list) do
            local item_num = ThunderManaWGData.Instance:GetItemNumInBagById(item_id)
            
            if special_select_list[item_id]  then
                item_num = item_num - special_select_list[item_id]
            end

            for j = 1, item_num do
                local data = {}
                data.item_id = item_id
                data.num = 1
                data.star_num = self.show_data.star_num
                table.insert(grid_list, data) 

                if #grid_list >= 32 then
                    break
                end
            end

            if #grid_list >= 32 then
                break
            end
        end
    end

    -- 选中已选中的
    local select_num = {}
    local choose_table = self.show_data.is_special and special_select_list or star_select_list[self.show_data.star_num]
    if choose_table ~= nil then
        for index, grid_data in ipairs(grid_list) do
            if choose_table[grid_data.item_id] then
                if select_num[grid_data.item_id] then
                    if select_num[grid_data.item_id] < choose_table[grid_data.item_id] then
                        self.batch_item_grid.select_tab[1][index] = true --选中已选择的万能卡
                        select_num[grid_data.item_id] = select_num[grid_data.item_id] + 1
                    end
                else
                    select_num[grid_data.item_id] = 1
                    self.batch_item_grid.select_tab[1][index] = true --选中已选择的万能卡
                end
            end
        end
    end

    local equip_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(self.show_data.aim_item_id)
    local new_data = {}
    new_data.is_plus = true
    new_data.seq = equip_cfg and equip_cfg.seq or 0
    table.insert(grid_list, new_data)

    if #grid_list <= 0 then
        self.batch_item_grid:CancleAllSelectCell()
    end

    self.batch_item_grid:SetDataList(grid_list)
    self.node_list["ph_item_grid"].scroll_rect.verticalNormalizedPosition = 1
end

-- 刷新数量
function ManaBatchBatchSelect:FlushAddNum()
    if not self.show_data then
        return
    end

	local equip_star_up_list = ThunderManaWGData.Instance:GetEquipComposeCost(self.show_data.aim_item_id)
    local str = ""
    if not equip_star_up_list then
        return
    end

    if self.show_data.is_special then
        for i, data in ipairs(equip_star_up_list) do
            if data.special_item ~= nil then
                self.need_num = data.num or 0
                local item_cfg = ItemWGData.Instance:GetItemConfig(data.special_item)
                local equip_item_star_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(data.special_item)
                local sp_star_num = equip_item_star_cfg and equip_item_star_cfg.star or 0
                local name_str = string.format("%s%s%s", sp_star_num, Language.Common.Star, item_cfg.name)

                if item_cfg then
                    str = string.format(Language.MountPetEquip.ShengPinNeedDes, ITEM_COLOR[item_cfg.color], data.num, name_str)
                end
                break
            end
        end
    elseif self.show_data.star_num ~= nil then
        for i, data in ipairs(equip_star_up_list) do
            if data.star ~= nil and self.show_data.star_num == data.star then
                self.need_num = data.num or 0
                local virtual_cfg = ThunderManaWGData.Instance:GetVirualItemByStar(self.show_data.aim_item_id, data.star)
                local virtual_id = virtual_cfg and virtual_cfg.virtual_item_id or 0
                local item_cfg = ItemWGData.Instance:GetItemConfig(virtual_id)

                if item_cfg then
                    str = string.format(Language.MountPetEquip.ShengPinNeedDes, ITEM_COLOR[item_cfg.color], data.num, item_cfg.name)
                end
                break
            end
        end
    end

    local selected_cells = self.batch_item_grid:GetAllSelectCell()
    self.cur_select_num = #selected_cells
    local color = self.cur_select_num == self.need_num and COLOR3B.GREEN or COLOR3B.RED
    self.node_list.tip_text.text.text = string.format("%s%s", str, string.format(Language.MountPetEquip.HadSelect, color, self.cur_select_num, self.need_num)) 
end

-- 获取数量
function ManaBatchBatchSelect:GetNeedNum()
    return self.need_num
end

-- 一键选取
function ManaBatchBatchSelect:OnClickOneKey()
    if not self.need_num then
        if self.show_data then
            local equip_star_up_list = ThunderManaWGData.Instance:GetEquipComposeCost(self.show_data.aim_item_id)
            local str = ""
            if not equip_star_up_list then
                self.need_num = 0
            else
                if self.show_data.is_special then
                    for i, data in ipairs(equip_star_up_list) do
                        if data.special_item ~= nil then
                            self.need_num = data.num or 0
                            break
                        end
                    end
                elseif self.show_data.star_num ~= nil then
                    for i, data in ipairs(equip_star_up_list) do
                        if data.star ~= nil and self.show_data.star_num == data.star then
                            self.need_num = data.num or 0
                            break
                        end
                    end
                end
            end
        else
            self.need_num = 0
        end
    end

    local item_list = self.batch_item_grid:GetDataList()
    local cur_count = #item_list - 1                        --去掉显示加号的数据
    if cur_count <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.MountPetEquip.UpstarNotEnough)
        return
    end

    if self.need_num > cur_count then
        --数量不够
        self.batch_item_grid:SetMultiSelectEffect(cur_count)
    else
        self.batch_item_grid:SetMultiSelectEffect(self.need_num)
    end
    self:FlushAddNum()
end

-- 关闭回调
function ManaBatchBatchSelect:CloseCallBack()
    if self.batch_item_grid then
        self.batch_item_grid:CancleAllSelectCell()
    end
end

-- 选择完成
function ManaBatchBatchSelect:OnClickSure()
    local data_list = self.batch_item_grid:GetAllSelectCell()
    local assemble_table = {}
    local now_item_table = {}

    for i, v in ipairs(data_list) do
        if now_item_table[v.item_id] then
            now_item_table[v.item_id] = now_item_table[v.item_id] + v.num
        else
            now_item_table[v.item_id] = v.num
        end
    end

    for k, v in pairs(now_item_table) do
        local data = {}
		data.cur_item_id = k
		data.cur_item_num = v
        table.insert(assemble_table, data)
    end

    if self.ok_callback then
        self.ok_callback(self.show_data.is_special, self.show_data.star_num, assemble_table)
    end

    self:Close()
end

-------------------------------------------------------------------------------------------------

ManaBatchBatchSelectRender = ManaBatchBatchSelectRender or BaseClass(ItemCell)
function ManaBatchBatchSelectRender:__init()
	self:UseNewSelectEffect(true)
end

function ManaBatchBatchSelectRender:OnClick()
    self:UseNewSelectEffect(true)
    if self.data then
        if self.data.is_plus then
            local pos = self:GetPos()
            local other_cfg = ThunderManaWGData.Instance:GetOtherCfg()
            local jump_view = ""
            if ThunderManaWGData.ThunderType.ShadyType == self.data.seq then
                jump_view = other_cfg.jump_view
            else
                jump_view = other_cfg.jump_view2
            end
    
            FunOpen.Instance:OpenViewNameByCfg(jump_view)
        else
            ItemCell.OnClick(self)
        end
    end
end

function ManaBatchBatchSelectRender:GetPos()
    if nil ==  self.view then return nil end
    local main_view = ViewManager.Instance:GetView(GuideModuleName.MainUIView)
	if nil == main_view or not main_view:IsOpen() then
		return
	end
    local parent_rect = main_view.root_node.transform:GetComponent(typeof(UnityEngine.RectTransform))
	local x, y = parent_rect.sizeDelta.x / 2, parent_rect.sizeDelta.y / 2
    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, self.view.transform.position)
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, Vector2(0, 0))

    x = local_position_tbl.x
    y = local_position_tbl.y
    return Vector2(x + 220, y)
end


function ManaBatchBatchSelectRender:OnFlush()
    if IsEmptyTable(self.data) then
        self:Reset()
		return
    end
    self:SetIgnoreDataToSelect(self.data.is_plus)
    ItemCell.OnFlush(self)
    if self.data.is_plus then
        self:Reset()
        local bundle, asset = ResPath.GetCommonImages("a2_ty_jia")
        self:SetItemIcon(bundle, asset)
        self:SetButtonComp(true)
        self:SetEffectRootEnable(false)
        ItemCell.SetSelectEffect(self, false)
    end

    if self.data.star_num and self.data.star_num ~= 0 then
        self:SetThunderManaStarMessage(self.data.star_num)
    else
        local equip_item_star_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(self.data.item_id)
        local sp_star_num = equip_item_star_cfg and equip_item_star_cfg.star or 0
        self:SetThunderManaStarMessage(sp_star_num)
    end
end

function ManaBatchBatchSelectRender:SetSelect(is_select, item_call_back)
	if is_select and IsEmptyTable(self.data) then
		return 
    end
    self:UseNewSelectEffect(true)
	ItemCell.SetSelectEffect(self, is_select)	
end