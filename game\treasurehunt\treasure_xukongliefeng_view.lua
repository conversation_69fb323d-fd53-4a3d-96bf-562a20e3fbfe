local  TREASURE_HUN_BOSS_TIP_CFG = {
    [1] = {icon = "a3_ty_tlabel_02", color = "#CBFBFD"},
    [2] = {icon = "a3_ty_tlabel_03", color = "#fffce7"},
    [3] = {icon = "a3_ty_tlabel_01", color = "#fffce7"},
}

TreasureXuKongLieFengView = TreasureXuKongLieFengView or BaseClass(SafeBaseView)

function TreasureXuKongLieFengView:__init()
    self:SetMaskBg(false, true)
    self:SetMaskBgAlpha(0.95)
    self:AddViewResource(0, "uis/view/treasurehunt_ui_prefab", "layout_xukongliefeng_view")
end

function TreasureXuKongLieFengView:SetDataAndOpen(treasure_type)
    self.treasure_type = treasure_type

    if not self:IsOpen() then
        self:Open()
    else
        self:Flush()
    end
end

function TreasureXuKongLieFengView:LoadCallBack()
    if not self.boss_list then
        self.boss_list = AsyncListView.New(TreasureHunBossCell, self.node_list.boss_list)
    end
end

function TreasureXuKongLieFengView:CloseCallBack()
    if self.treasure_type then
        ViewManager.Instance:Open(GuideModuleName.TreasureHunt, TreasureHuntView.TableIndexByMode[self.treasure_type])
    end
end

function TreasureXuKongLieFengView:ReleaseCallBack()
    if self.boss_list then
        self.boss_list:DeleteMe()
        self.boss_list = nil
    end

    self.treasure_type = nil
end

function TreasureXuKongLieFengView:OnFlush(param)
    if not self.treasure_type then
        for k,v in pairs(param) do
            if k == "all" then
                if v.open_param and v.open_param ~= "" and tonumber(v.open_param) > 0 then
                    self.treasure_type = tonumber(v.open_param)
                    break
                end
            end
        end

        self.treasure_type = self.treasure_type or 1
    end

    if self.treasure_type then
        local data_list = TreasureHuntWGData.Instance:GetHuntBossDataListByType(self.treasure_type)
        self.boss_list:SetDataList(data_list)

        local desc_t = TreasureBossWGData.Instance:GetBestFightTimeTreasureSrc(2)
        self.node_list.flush_desc.text.text = desc_t[1] or ""
        self.node_list.flush_next_desc.text.text = desc_t[2] or ""
    end
end

------------------------------TreasureHunBossCell--------------------------
TreasureHunBossCell = TreasureHunBossCell or BaseClass(BaseRender)

function TreasureHunBossCell:__delete()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end

    if self.run_timeer then
        GlobalTimerQuest:CancelQuest(self.run_timeer)
        self.run_timeer = nil
    end
end

function TreasureHunBossCell:LoadCallBack()
    self.node_list.goto_kill.button:AddClickListener(BindTool.Bind1(self.OnClickGoToKill, self))
    self.reward_list = AsyncListView.New(TreasureHunBossRewardCell, self.node_list.reward_list)
    self.reward_list:SetIsDelayFlush(true)
end

function TreasureHunBossCell:OnFlush()
    if self.data and not IsEmptyTable(self.data) then
        self:SetRootPos()

        self.node_list.bg.raw_image:LoadSprite(ResPath.GetRawImagesPNG(self.data.show_bg))

        local drop_desc_bg_index = self.index % 4
        drop_desc_bg_index = drop_desc_bg_index > 0 and drop_desc_bg_index or 1
        local cfg = TREASURE_HUN_BOSS_TIP_CFG[drop_desc_bg_index]

        local bundle, asset = ResPath.GetCommonImages(cfg.icon)
        self.node_list.drop_desc_bg.image:LoadSprite(bundle, asset, function ()
            self.node_list.drop_desc_bg.image:SetNativeSize()
        end)
        self.node_list.drop_rate_desc.text.text = ToColorStr(string.format(Language.TreasureHunt.DropRateDescStr, self.data.mutiple), cfg.color)

        local chestshop_type = self.data.chestshop_type
        local draw_count = TreasureHuntWGData.Instance:GetHuntTotalCountByType(chestshop_type)
        local open_count = self.data.chestshop_num
        self.is_open = draw_count >= open_count

        self.node_list.goto_kill:CustomSetActive(self.is_open)
        self.node_list.flag_lock:CustomSetActive(not self.is_open)

        if self.is_open then
            self.node_list.need_text.text.text = string.format(Language.TreasureHunt.TreasureDrawTime,ToColorStr(open_count,COLOR3B.DEFAULT_NUM).."/"..open_count)
            -- XUI.SetGraphicGrey(self.node_list.goto_kill, false)
        else
            self.node_list.need_text.text.text = string.format(Language.TreasureHunt.TreasureDrawTime,ToColorStr(draw_count,COLOR3B.RED).."/"..open_count)
            -- XUI.SetGraphicGrey(self.node_list.goto_kill, true)
        end
        
        local reward_list_data = TreasureBossWGData.Instance:GetTreasureBossShowItemList(self.data.boss_id)
        self.reward_list:SetDataList(reward_list_data,3)

        local boss_info = TreasureBossWGData.Instance:GetTreasureBossInfoByBossID(self.data.boss_id)
        if (boss_info and boss_info.status == 1) or IsEmptyTable(boss_info) then
            self.node_list.advise_tip.text.text = Language.TreasureHunt.BossDesc
            -- local bundle, asset = ResPath.GetTreasurehuntIcon("a2_xb_ysx")
            -- self.node_list.flush_text.image:LoadSprite(bundle, asset, function()
            --     self.node_list.flush_text.image:SetNativeSize()
            -- end)
            --self.node_list.flush_text.text.text = Language.TreasureHunt.BossHasFlush

            self.node_list.flag_kill:CustomSetActive(false)
        elseif boss_info then
            if self.run_timeer then
                GlobalTimerQuest:CancelQuest(self.run_timeer)
                self.run_timeer = nil
            end
            self.run_timeer = GlobalTimerQuest:AddRunQuest(function()
                self:CacularFlushTime()
            end,1)
            self:CacularFlushTime2()
        end
        -- local need_show_flush = TreasureHuntWGData.Instance:GetOpenSelectEffect()
        -- if need_show_flush then
        --     local best_boss_id = TreasureHuntWGData.Instance:GetBestOpenTreasureBoss()
        --     self.node_list["flush_effect"]:SetActive(self.data.boss_id == best_boss_id)
        -- end    
    else
        self.is_open = false
    end
end

function TreasureHunBossCell:CacularFlushTime2()
    local boss_info = TreasureBossWGData.Instance:GetTreasureBossInfoByBossID(self.data.boss_id)
    local cur_time = TimeWGCtrl.Instance:GetServerTime()
    if boss_info.next_refresh_time and boss_info.next_refresh_time > cur_time then
        local time_str = TreasureBossWGData.Instance:GetNextRefreshTime()
        --self.node_list.flush_text.text.text = time_str..Language.TreasureHunt.BossFlushTip2]
        self.node_list.advise_tip.text.text = time_str..Language.TreasureHunt.BossFlushTip2
        -- local bundle, asset = ResPath.GetTreasurehuntIcon("a2_xb_jisha")
        -- self.node_list.flush_text.image:LoadSprite(bundle, asset, function()
        --     self.node_list.flush_text.image:SetNativeSize()
        -- end)

        self.node_list.flag_kill:CustomSetActive(true)
    end
end

function TreasureHunBossCell:CacularFlushTime()
    local boss_info = TreasureBossWGData.Instance:GetTreasureBossInfoByBossID(self.data.boss_id)
    local cur_time = TimeWGCtrl.Instance:GetServerTime()
    if boss_info.next_refresh_time and boss_info.next_refresh_time > cur_time then

    else
        -- local bundle, asset = ResPath.GetTreasurehuntIcon("a2_xb_ysx")
        -- self.node_list.flush_text.image:LoadSprite(bundle, asset, function()
        --     self.node_list.flush_text.image:SetNativeSize()
        -- end)
        self.node_list.flag_kill:CustomSetActive(false)
         self.node_list.advise_tip.text.text = Language.TreasureHunt.BossDesc
       -- self.node_list.flush_text.text.text = Language.TreasureHunt.BossHasFlush
        if self.run_timeer then
            GlobalTimerQuest:CancelQuest(self.run_timeer)
            self.run_timeer = nil
        end
    end
end

function TreasureHunBossCell:OnClickGoToKill()
    local can_enter, reason = TreasureBossWGData.Instance:CanEnterFightTreasureBoss()

    if not can_enter then
        SysMsgWGCtrl.Instance:ErrorRemind(reason)
        return
    end
    
    if self.data and not IsEmptyTable(self.data) and self.is_open then

        BossWGData.Instance:ClearCurSelectBossID()
        BossWGCtrl.Instance:SendTreasureBossOpera(TREASURE_BOSS_REQ_TYPE.REQ_TYPE_ENTER, self.data.seq)
        --BossWGData.Instance:SetCurSelectBossID(0 , 0, self.data.boss_id)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.TreasureHunt.TreasureTimesNotEnough)
    end
end

function TreasureHunBossCell:SetRootPos()
    if self.index then
        local pos = self.index % 2 == 0 and Vector2(1, 35) or Vector2(1, -44)
        self.node_list.root.rect.anchoredPosition = pos
        self.init_pos = true
    end
end

--------------------------------------TreasureHunBossRewardCell---------------------------------------
TreasureHunBossRewardCell = TreasureHunBossRewardCell or BaseClass(ItemCell)

function TreasureHunBossRewardCell:OnFlush()
    ItemCell.OnFlush(self)
    local show_flag = self:GetIndex() <= self.data.show_num
    local bundle, asset = ResPath.GetLoadingPath("a3_ty_dl_" .. self.data.mutiple)
    self:SetTopLeftFlag(show_flag, bundle, asset)
end