DragonTempleRankInfoView = DragonTempleRankInfoView or BaseClass(SafeBaseView)
function DragonTempleRankInfoView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(888, 550)})
	self:AddViewResource(0, "uis/view/dragon_temple_ui_prefab", "layout_rank_info")
end

function DragonTempleRankInfoView:OpenCallBack()
    DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.RANK_INFO, DRAGON_TEMPLE_OPERATE_RANK_TYPE.LONGHUN)
	DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.RANK_INFO, DRAGON_TEMPLE_OPERATE_RANK_TYPE.MONEYCONTRIBUTE)
end

function DragonTempleRankInfoView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.DragonTemple.RankInfo

    if not self.score_rank_list then
		self.score_rank_list = AsyncListView.New(ScoreRankListItemRender, self.node_list.score_rank_list)
	end

	if not self.contribute_rank_list then
		self.contribute_rank_list = AsyncListView.New(ContributeRankListItemRender, self.node_list.contribute_rank_list)
	end

    self.node_list.score_rank_desc.text.text = Language.DragonTemple.ScoreRankDesc
	self.node_list.contribute_rank_desc.text.text = Language.DragonTemple.ContributeRankDesc
end

function DragonTempleRankInfoView:ReleaseCallBack()
	if self.score_rank_list then
		self.score_rank_list:DeleteMe()
		self.score_rank_list = nil
	end

	if self.contribute_rank_list then
		self.contribute_rank_list:DeleteMe()
		self.contribute_rank_list = nil
	end
end

function DragonTempleRankInfoView:OnFlush()
    self:FlushScoreInfo()
	self:FlushContributionRankfo()
end

function DragonTempleRankInfoView:FlushScoreInfo()
	local data_list, my_rank, donate_gold = DragonTempleWGData.Instance:GetRankInfoByOperateRankType(DRAGON_TEMPLE_OPERATE_RANK_TYPE.LONGHUN)
	local no_data = IsEmptyTable(data_list)
	local my_rank_str = my_rank <= 0 and Language.DragonTemple.NoMyRankData or string.format(Language.DragonTemple.MyRank, my_rank)

	self.score_rank_list:SetDataList(data_list)
	self.node_list.not_score_info:SetActive(no_data)
	self.node_list.my_rank_text.text.text = my_rank_str
	self.node_list.my_score_text.text.text = donate_gold
end

function DragonTempleRankInfoView:FlushContributionRankfo()
	local data_list, my_rank, donate_gold = DragonTempleWGData.Instance:GetRankInfoByOperateRankType(DRAGON_TEMPLE_OPERATE_RANK_TYPE.MONEYCONTRIBUTE)
	local no_data = IsEmptyTable(data_list)
	local my_rank_str = my_rank <= 0 and Language.DragonTemple.NoMyRankData or string.format(Language.DragonTemple.MyRank, my_rank)

	self.node_list.not_contribute_info:SetActive(no_data)
	self.contribute_rank_list:SetDataList(data_list)
	self.node_list.my_contribute_text.text.text = my_rank_str
	self.node_list.my_contribute_text0.text.text = donate_gold
end

--------ScoreRankListItemRender------
ScoreRankListItemRender = ScoreRankListItemRender or BaseClass(BaseRender)
function ScoreRankListItemRender:__init()
end

function ScoreRankListItemRender:__delete()
	if self.reward_item then
		self.reward_item:DeleteMe()
		self.reward_item = nil
	end
end

function ScoreRankListItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local rank_index = self.data.rank
	local is_top_three = rank_index <= 3
	self.node_list.rank:SetActive(not is_top_three)
    self.node_list.img_rank:SetActive(is_top_three)
	if is_top_three then
		self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. rank_index))
		self.node_list.root_bg.image:LoadSprite(ResPath.GetCommonImages("a2_xm_di_dj" .. rank_index))
	else
        self.node_list.root_bg.image:LoadSprite(ResPath.GetCommonImages("a2_ty_xxd_5"))
		self.node_list.root_bg:SetActive(self.index % 2 ~= 0)
	end

	self.node_list.rank.text.text = self.data.rank
	self.node_list.player_name.text.text = self.data.name
	self.node_list.player_score.text.text = self.data.rank_value

	if not self.reward_item then
		self.reward_item = ItemCell.New(self.node_list.reward_item_pos)
	end

	local reward_data = DragonTempleWGData.Instance:GetScoreRankRewardData(self.data.rank)

	if not IsEmptyTable(reward_data) then
		self.reward_item:SetData(reward_data)
	end

	self.node_list.reward_item_pos:SetActive(not IsEmptyTable(reward_data))
end

-----------------ContributeRankListItemRender---------
ContributeRankListItemRender = ContributeRankListItemRender or BaseClass(BaseRender)
function ContributeRankListItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local rank_index = self.data.rank
	local is_top_three = rank_index <= 3
	self.node_list.rank:SetActive(not is_top_three)
	self.node_list.img_rank:SetActive(is_top_three)
	
	if is_top_three then
		self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. rank_index))
		self.node_list.root_bg.image:LoadSprite(ResPath.GetCommonImages("a2_xm_di_dj" .. rank_index))
	else
		self.node_list.root_bg.image:LoadSprite(ResPath.GetCommonImages("a2_ty_xxd_5"))
		self.node_list.root_bg:SetActive(self.index % 2 ~= 0)
	end

	self.node_list.rank.text.text = self.data.rank
	self.node_list.player_server.text.text = self.data.name
	self.node_list.player_contribute.text.text = self.data.rank_value
end