require("game/activity_dragon_secret/activity_dragon_secret_wg_data")
require("game/activity_dragon_secret/activity_dragon_secret_view")
require("game/activity_dragon_secret/activity_dragon_secret_cur_reward_view")
require("game/activity_dragon_secret/activity_dragon_secret_all_reward_view")
require("game/activity_dragon_secret/activity_dragon_reward_results")

ActivityDragonSecretWGCtrl = ActivityDragonSecretWGCtrl or BaseClass(BaseWGCtrl)

function ActivityDragonSecretWGCtrl:__init()
    if ActivityDragonSecretWGCtrl.Instance then
		error("[ActivityPrivilegeBuyWGCtrl]:Attempt to create singleton twice!")
	end

    --单例
    ActivityDragonSecretWGCtrl.Instance = self

    self.view = ActivityDragonSecretView.New(GuideModuleName.DragonSecretView)
    self.data = ActivityDragonSecretWGData.New()
    self.dragon_secret_cur_reward_view = ActicityCurRewardView.New()
    self.dragon_secret_all_reward_view = ActivitySecretAllRewardView.New()
    self.dragon_secret_reward_results_view = DragonRewardResults.New()


    --注册协议
    self:RegisterAllProtocols()
end

function ActivityDragonSecretWGCtrl:__delete()
    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end
    
    if self.view then
        self.view:DeleteMe()
        self.view = nil 
    end

    if self.dragon_secret_cur_reward_view then
        self.dragon_secret_cur_reward_view:DeleteMe()
        self.dragon_secret_cur_reward_view = nil
    end

    if self.dragon_secret_all_reward_view then
        self.dragon_secret_all_reward_view:DeleteMe()
        self.dragon_secret_all_reward_view = nil
    end
    
    if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end

    if self.dragon_secret_reward_results_view then
        self.dragon_secret_reward_results_view:DeleteMe()
        self.dragon_secret_reward_results_view = nil
    end

    ActivityDragonSecretWGCtrl.Instance = nil
end

function ActivityDragonSecretWGCtrl:RegisterAllProtocols()
    --接受协议
    self:RegisterProtocol(SCDragonStoreInfo, "OnSCDragonStoreInfo")

    --抽奖结果的协议
    self:RegisterProtocol(SCDragonStoreResult, "OnSCDragonStoreResult")
end


--打开当前列表
function ActivityDragonSecretWGCtrl:OpenCurRewardView()
    self.dragon_secret_cur_reward_view:Open()
end

--打开全部列表
function ActivityDragonSecretWGCtrl:OpenAllRewardView()
    self.dragon_secret_all_reward_view:Open()
end

function ActivityDragonSecretWGCtrl:OnSCDragonStoreResult(protocol)
    self.data:SetDragonDrawRewardInfo(protocol)
    local data_list = self.data:GetDragonDrawRewardInfo()
    self:OpenLotteryRewardView(data_list.result_item_list)
end

function ActivityDragonSecretWGCtrl:OpenLotteryRewardView(data)
    self.dragon_secret_reward_results_view:SetData(data)
    self.dragon_secret_reward_results_view:Open()
end

--接受协议刷新界面和数据
function ActivityDragonSecretWGCtrl:OnSCDragonStoreInfo(protocol)
    --数据存储
    self.data:SetInfo(protocol)

    --界面刷新
    if self.view then
        self.view:Flush()
    end

    --刷新红点
    RemindManager.Instance:Fire(RemindName.RemindDragonSecret)
end

--使用道具并弹窗
function ActivityDragonSecretWGCtrl:ClickUse(mode_type)
    --拿到奖池物品剩余的总数
    local prize_sum = self.data:GetPrizeSum()
    --按钮1的默认次数
    local draw_number_ten = LOTTER_NUM_TYPE.TEN_NUMBER_DRAWS
    --按钮2的默认次数
    local repeatedly_draw_num = LOTTER_NUM_TYPE.DEFAULT_NUMBER 
    local other_cfg = self.data:GetQiTaCfg()
    --拿到抽奖需要的道具数量
    local lottery_number = 0
    --先判断是单次还是多次
    if mode_type == LOTTER_NUM_TYPE.SINGLE_DRAW then
        --拿到单抽需要的道具数量
        if prize_sum >= draw_number_ten  then
            lottery_number = draw_number_ten
        elseif prize_sum > 0 then
            lottery_number = prize_sum
        else
            TipWGCtrl.Instance:ShowSystemMsg(Language.DragonSecret.DragonNotProps)
        end
    elseif mode_type == LOTTER_NUM_TYPE.REPEATEDLY_DRAW then
        if prize_sum >= repeatedly_draw_num then
            lottery_number = repeatedly_draw_num
        elseif prize_sum > 0 then
            lottery_number = prize_sum
        else
            TipWGCtrl.Instance:ShowSystemMsg(Language.DragonSecret.DragonNotProps)
            lottery_number = prize_sum
        end
    end

   --数量检测
   local item_cfg_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.cost_item_id) --拿到背包中道具的数量
   local consumption_num = other_cfg.cost_item_num * lottery_number
 
   if item_cfg_num >= consumption_num then
       --发送抽奖的协议
       ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.OPERA_ACT_DRAGONSECRET, 2, lottery_number)
   else
        --道具不足的弹窗
        if not self.alert then
            self.alert = Alert.New()
        end

        self.alert:ClearCheckHook()
        self.alert:SetShowCheckBox(true, "dragon_secret")
        self.alert:SetCheckBoxDefaultSelect(false)

        local func = function ()
            --判断差多少个道具
            local consume = other_cfg.cost_gold * (consumption_num - item_cfg_num)
            --检查仙玉
            local enough = RoleWGData.Instance:GetIsEnoughUseGold(consume)

            if enough then
                ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.OPERA_ACT_DRAGONSECRET, 2, lottery_number)
            else
                VipWGCtrl.Instance:OpenTipNoGold()
            end 
        end

        local item_cfg_cell = ItemWGData.Instance:GetItemConfig(other_cfg.cost_item_id)
        local name = ""
        if item_cfg_cell ~= nil then
            name =  ToColorStr(item_cfg_cell.name, ITEM_COLOR[item_cfg_cell.color])
        end
     
        local cost = other_cfg.cost_gold * (consumption_num - item_cfg_num)
        local str = string.format(Language.DragonSecret.DragonCostStr, name, cost)
     
        self.alert:SetLableString(str)
        self.alert:SetOkFunc(func)
        self.alert:Open()
   end
end
