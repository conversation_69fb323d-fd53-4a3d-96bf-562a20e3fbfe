WuHunPrerogativeView = WuHunPrerogativeView or BaseClass(SafeBaseView)

function WuHunPrerogativeView:__init()
	self:SetMaskBg()
	self.view_style = ViewStyle.Full
	self:AddViewResource(0, "uis/view/wuhunzhenshen_prefab", "layout_wuhun_prerogative")
end

function WuHunPrerogativeView:LoadCallBack()
	self.late_lv = -1
	self.panel_idx = 0

	self.free_item_1 = nil
	self.free_item_2 = nil

	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list.prerogative_reward_list)
		self.reward_list:SetStartZeroIndex(true)
	end

	if not self.item_list then
		self.item_list = {}
		for i = 1, 9 do
			self.item_list[i] = WuHunPrerogativeRenderItem.New(
				self.node_list.draw_item_group:FindObj("prerogative_item_" .. i))
		end
	end

	XUI.AddClickEventListener(self.node_list.change_btn, BindTool.Bind(self.ChangePanel, self))
	XUI.AddClickEventListener(self.node_list.buy_btn, BindTool.Bind(self.OnClickBuyBtn, self))
	XUI.AddClickEventListener(self.node_list.rule_tips_btn, BindTool.Bind(self.OpenRuleTips, self))
	XUI.AddClickEventListener(self.node_list.skill_display, BindTool.Bind(self.OpenSkillTips, self))
	XUI.AddClickEventListener(self.node_list.free_box_btn, BindTool.Bind(self.OnClickPrerogativeFreeBox, self))
	XUI.AddClickEventListener(self.node_list.reward_show_btn, BindTool.Bind(self.OpenRewardView, self))
	XUI.AddClickEventListener(self.node_list.draw_probability_show_btn, BindTool.Bind(self.OnClickGaiLvShow, self))
	for i = 1, 2 do
		XUI.AddClickEventListener(self.node_list["draw_btn" .. i], BindTool.Bind(self.OnClickRecord, self, i))
	end

	self:ChangePanel()
end

function WuHunPrerogativeView:ReleaseCallBack()
	if self.free_item_1 then
		self.free_item_1:DeleteMe()
		self.free_item_1 = nil
	end

	if self.free_item_2 then
		self.free_item_2:DeleteMe()
		self.free_item_2 = nil
	end

	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

	if self.item_list then
		for key, value in pairs(self.item_list) do
			value:DeleteMe()
			value = nil
		end
		self.item_list = nil
	end
end

function WuHunPrerogativeView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "SetPanel1" then
			self:SetPanel1()
		elseif k == "SetPanel2" then
			self:SetPanel2()
		end
	end

	self:SetFreeItem()
	self:CheckIsShowNewSkillView()
end

function WuHunPrerogativeView:ChangePanel()
	self.panel_idx = self.panel_idx + 1
	if self.panel_idx > 2 then
		self.panel_idx = 1
	end

	--展示详细界面.
	self.node_list.panel_1:SetActive(self.panel_idx == 1)
	self.node_list.change_image_1:SetActive(self.panel_idx == 1)
	self.node_list.buy_btn:SetActive(self.panel_idx == 1)
	--展示抽奖界面.
	self.node_list.panel_2:SetActive(self.panel_idx == 2)
	self.node_list.change_image_2:SetActive(self.panel_idx == 2)

	self.node_list.change_text.text.text = self.panel_idx == 1 and
		Language.WuHunPrerogative.BtnText1 or Language.WuHunPrerogative.BtnText2

	if self.panel_idx == 1 then
		--展示详细.
		self:SetPanel1()
	elseif self.panel_idx == 2 then
		--展示抽奖.
		self:SetPanel2()
	end
end

function WuHunPrerogativeView:SetPanel1()
	local client_cfg = WuHunWGData.Instance:GetWuhunPrerogativeClientcfg()
	if IsEmptyTable(client_cfg) then
		return
	end

	local lv = WuHunWGData.Instance:GetWuhunPrerogativeLevel()
	local max_lv = WuHunWGData.Instance:GetWuhunPrerogativeMaxLevel()
	local is_draw = WuHunWGData.Instance:GetWuhunPrerogativeIsDraw()

	self.node_list.lock:SetActive(lv <= 0)
	self.node_list.late_Image:SetActive(lv > 0)
	self.node_list.buy_btn:SetActive(self.panel_idx == 1 and lv < max_lv)
	self.node_list.late_group:SetActive(lv < max_lv)
	self.node_list.cur_group:SetActive(lv > 0 and lv < max_lv)
	self.node_list.max_group:SetActive(lv == max_lv)
	self.node_list.skill_text.text.text = client_cfg.wh_skill_name
	self.node_list.change_remind:SetActive(is_draw)

	local prerogative_cfg = WuHunWGData.Instance:GetWuhunPrerogativeCurCfg()
	if IsEmptyTable(prerogative_cfg) then
		return
	end

	local title2 = string.format(Language.WuHunPrerogative.LateTitleText, lv + 1)
	local desc2 = prerogative_cfg.desc2
	if lv <= 0 then
		local client_cfg = WuHunWGData.Instance:GetWuhunPrerogativeClientcfg()
		if IsEmptyTable(client_cfg) then
			return
		end

		desc2 = client_cfg.wh_desc
		title2 = Language.WuHunPrerogative.TitleText
	end

	if lv < max_lv then
		local next_cfg = WuHunWGData.Instance:GetWuhunPrerogativeCfg(lv + 1)
		if IsEmptyTable(next_cfg) then
			return
		end

		local cur_num = next_cfg.price
		local change_buy_price = RoleWGData.GetPayMoneyStr(cur_num)
		self.node_list.buy_price.text.text = change_buy_price
		self.reward_list:SetDataList(next_cfg.reward)

		self.node_list.cur_desc_text.text.text = prerogative_cfg.desc
		self.node_list.late_desc_text.text.text = desc2

		self.node_list.cur_title_text.text.text = string.format(Language.WuHunPrerogative.CurTitleText, lv)
		self.node_list.late_title_text.text.text = title2
	else
		self.node_list.max_text.text.text = prerogative_cfg.desc
		self.node_list.max_title_text.text.text = Language.WuHunPrerogative.MaxTitleText
	end
end

function WuHunPrerogativeView:SetPanel2()
	local item_list = WuHunWGData.Instance:GetWuhunPrerogativeDrawItemList()
	if IsEmptyTable(item_list) then
		return
	end

	for key, value in ipairs(self.item_list) do
		if item_list[key] then
			value:SetData(item_list[key])
		end
	end

	local item_id = WuHunWGData.Instance:GetWuhunPrerogativeDrawCostItem()
	local num = ItemWGData.Instance:GetItemNumInBagById(item_id)
	local cfg = WuHunWGData.Instance:GetWuhunPrerogativeDrawModeCfg()
	if nil == cfg or nil == cfg[1] or nil == cfg[2] then
		return
	end

	local nedd_num = cfg[1].cost_item_num
	local nedd_num2 = cfg[2].cost_item_num
	self.node_list.draw_cost_text1.text.text = string.format(
		num >= nedd_num and Language.WuHunPrerogative.DrawCostText1 or Language.WuHunPrerogative.DrawCostText2,
		num, nedd_num)
	self.node_list.draw_cost_text2.text.text = string.format(
		num >= nedd_num2 and Language.WuHunPrerogative.DrawCostText1 or Language.WuHunPrerogative.DrawCostText2,
		num, nedd_num2)

	self.node_list.draw_remind1:SetActive(num >= nedd_num)
	self.node_list.draw_remind2:SetActive(num >= nedd_num2)

	local bundle, asset = ResPath.GetItem(item_id)
	self.node_list.draw_cost_img1.image:LoadSprite(bundle, asset, function()
		self.node_list.draw_cost_img1.image:SetNativeSize()
	end)

	self.node_list.draw_cost_img2.image:LoadSprite(bundle, asset, function()
		self.node_list.draw_cost_img2.image:SetNativeSize()
	end)
end

function WuHunPrerogativeView:SetFreeItem()
	local prerogative_cfg = WuHunWGData.Instance:GetWuhunPrerogativeCurCfg()
	if IsEmptyTable(prerogative_cfg) and nil == prerogative_cfg.daily_reward then
		return
	end

	if prerogative_cfg.daily_reward[0] then
		if not self.free_item_1 then
			self.free_item_1 = ItemCell.New(self.node_list.item_cell_1)
		end
		self.free_item_1:SetData({
			item_id = prerogative_cfg.daily_reward[0].item_id,
			num = prerogative_cfg.daily_reward[0].num
		})
	end

	if prerogative_cfg.daily_reward[1] then
		if not self.free_item_2 then
			self.free_item_2 = ItemCell.New(self.node_list.item_cell_2)
		end
		self.free_item_2:SetData({
			item_id = prerogative_cfg.daily_reward[1].item_id,
			num = prerogative_cfg.daily_reward[1].num
		})
	end

	local is_activate = WuHunWGData.Instance:CheckWuhunPrerogativeIsActivate()
	local is_can_get = WuHunWGData.Instance:GetWuhunPrerogativeFlag()
	self.node_list.free_box_btn:SetActive(is_activate and is_can_get)
	self.node_list.remind_1:SetActive(is_activate and is_can_get)
	self.node_list.remind_2:SetActive(is_activate and is_can_get)
	self.free_item_1:SetLingQuVisible(is_activate and not is_can_get)
	self.free_item_2:SetLingQuVisible(is_activate and not is_can_get)
end

function WuHunPrerogativeView:OnClickBuyBtn()
	local is_can_buy = WuHunWGData.Instance:CheckWuhunPrerogativeIsCanBuy()
	if not is_can_buy then
		return
	end

	local price, rmb_type, rmb_seq = WuHunWGData.Instance:GetWuhunPrerogativeBuyInfo()
	RechargeWGCtrl.Instance:Recharge(price, rmb_type, rmb_seq)
end

function WuHunPrerogativeView:OpenSkillTips()
	local client_cfg = WuHunWGData.Instance:GetWuhunPrerogativeClientcfg()
	if IsEmptyTable(client_cfg) then
		return
	end

	local prerogative_cfg = WuHunWGData.Instance:GetWuhunPrerogativeCurCfg()
	if IsEmptyTable(prerogative_cfg) then
		return
	end

	local show_data = {
		x = 0,
		y = 0,
		icon = client_cfg.wh_skill_id,
		top_text = client_cfg.wh_skill_name,
		body_text = prerogative_cfg.skill_desc,
		set_pos2 = true,
		show_level = false,
	}

	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

function WuHunPrerogativeView:CheckIsShowNewSkillView()
	if self.late_lv == -1 then
		self.late_lv = WuHunWGData.Instance:GetWuhunPrerogativeLevel()
	end

	local cur_lv = WuHunWGData.Instance:GetWuhunPrerogativeLevel()

	if self.late_lv == 0 and cur_lv == 1 then
		local client_cfg = WuHunWGData.Instance:GetWuhunPrerogativeClientcfg()
		if IsEmptyTable(client_cfg) then
			return
		end

		local prerogative_cfg = WuHunWGData.Instance:GetWuhunPrerogativeCurCfg()
		if IsEmptyTable(prerogative_cfg) then
			return
		end

		local data = {
			name = client_cfg.wh_skill_name,
			desc = prerogative_cfg.skill_desc,
			res_fun = ResPath.GetSkillIconById,
			icon = client_cfg.wh_skill_id
		}

		TipWGCtrl.Instance:ShowGetNewSkillView2(data)
	end

	self.late_lv = cur_lv
end

function WuHunPrerogativeView:OnClickPrerogativeFreeBox()
	local is_activate = WuHunWGData.Instance:CheckWuhunPrerogativeIsActivate()
	local is_can_get = WuHunWGData.Instance:GetWuhunPrerogativeFlag()
	if not is_activate or not is_can_get then
		return
	end

	--发送协议
	WuHunWGCtrl.Instance:SendRoleWuHunOperate(ROLE_OPERA_TYPE.OPER_TYPE_FETCH_DAILY_REWARD)

	local free_list = WuHunWGData.Instance:GetWuhunPrerogativeFreeList()
	if IsEmptyTable(free_list) then
		return
	end

	local item = free_list
	if IsEmptyTable(item) then
		return
	end

	TipWGCtrl.Instance:ShowGetReward(nil, item, false, nil, nil, false)
end

function WuHunPrerogativeView:OnClickRecord(btn_index)
	local item_id = WuHunWGData.Instance:GetWuhunPrerogativeDrawCostItem()
	local num = ItemWGData.Instance:GetItemNumInBagById(item_id)
	local cfg = WuHunWGData.Instance:GetWuhunPrerogativeDrawModeCfg()
	if nil == cfg or nil == cfg[btn_index] then
		return
	end

	cfg = cfg[btn_index]
	if num >= cfg.cost_item_num then
		--发送协议
		WuHunWGCtrl.Instance:SendRoleWuHunOperate(ROLE_OPERA_TYPE.OPER_TYPE_DRAW, cfg.mode)
	else
		TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = item_id })
	end
end

function WuHunPrerogativeView:OpenRewardView()
	WuHunWGCtrl.Instance:OpenWuHunPrerogativeLibrayView()
end

function WuHunPrerogativeView:OnClickGaiLvShow()
	WuHunWGCtrl.Instance:OpenWuHunPrerogativeProbabilityView()
end

function WuHunPrerogativeView:OpenRuleTips()
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetContent(Language.WuHunPrerogative.RuleInfo, Language.WuHunPrerogative.RuleTitle)
	end
end

---------------------------------WuHunPrerogativeRenderItem-----------------------------
WuHunPrerogativeRenderItem = WuHunPrerogativeRenderItem or BaseClass(BaseRender)

function WuHunPrerogativeRenderItem:LoadCallBack()
	if not self.reward_item_cell then
		self.reward_item_cell = ItemCell.New(self.node_list["cell"])
	end
end

function WuHunPrerogativeRenderItem:ReleaseCallBack()
	if self.reward_item_cell then
		self.reward_item_cell:DeleteMe()
		self.reward_item_cell = nil
	end
end

function WuHunPrerogativeRenderItem:OnFlush()
	if self.data == nil then
		return
	end

	self.reward_item_cell:SetData(self.data.item)

	self.node_list.lock:SetActive(self.data.cur_level < self.data.level)
end
