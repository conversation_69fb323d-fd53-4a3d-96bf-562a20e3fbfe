function WorldServerView:DeleteKfBossView()
	if self.kf_gather_render then
		self.kf_gather_render:DeleteMe()
		self.kf_gather_render = nil
	end

	self.kf_cache_show_index = nil
end

function WorldServerView:InitKfBossView()
	self.cur_kflayer = BossWGData.Instance:GetKfLayerLevel()
	self.cur_kfboss_id = 1
	local item_cell = BossKFGatherItemRender.New(self.node_list["kf_gather_render"])
	self.kf_gather_render = item_cell
	self.node_list["kf_gather_render"].button:AddClickListener(function()
		self.kf_gather_render:SetSelect(true)
		self:SelectKFGather(item_cell)
	end)

	self.node_list["btn_kf_add"].button:AddClickListener(
		BindTool.Bind(self.OnClickKfBossAddTimes, self, SceneType.KF_BOSS))

	XUI.AddClickEventListener(self.node_list["kf_boss_text_btn"], BindTool.Bind(self.KFBossConcernBtn, self))
end

function WorldServerView:OnFlushKfBossView()
	local view_index, number_index, card_index = BossWGData.Instance:GetBossTuJianIndex()
	if view_index ~= nil then
		self:TuJianKuaFuJump(view_index, number_index, card_index)
	end
end

function WorldServerView:SelectLayerBtnMH(btn_index)
	local old_index_kf = self.cur_kflayer
	self.cur_kflayer = btn_index
	local falg, need_level = BossWGData.Instance:GetKfLayerIsEnter(self.cur_kflayer)
	need_level = RoleWGData.GetLevelString(need_level)
	if not falg and self.cur_kflayer == 1 then
		self:OnFlushSelectLayer()
		return
	else
		if not falg then
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Boss.EnterWorldBossLevel, need_level))
			if old_index_kf ~= self.cur_kflayer then
				local flag2 = BossWGData.Instance:GetKfLayerIsEnter(old_index_kf)
				if not flag2 then
					old_index_kf = BossWGData.Instance:GetKfLayerLevel()
				end
				self.layer_btn_list:SelectIndex(old_index_kf)
			end
			return
		end
	end
	self:OnFlushSelectLayer()

	BossWGCtrl.Instance:SendCrossBossReq(BossView.KfReqType.ALLINFO, self.cur_kflayer)
end

function WorldServerView:OnFlushSelectLayer()
	self:OnFlushBossList()
	self.kf_gather_render:SetData(BossWGData.Instance:GetKFGatherInfo(self.cur_kflayer))
end

function WorldServerView:SelectKFGather(cell)
	self:CancelBossSelectCell()
	self:BossLsitSelectCallBack(cell)
end

function WorldServerView:SelectKFBoss()
	if not self.select_item_data then
		return
	end
	self.cur_kfboss_id = self.select_item_data.boss_id
	if self.select_item_data.type ~= BossWGData.MonsterType.Gather then
		if self.kf_gather_render then
			self.kf_gather_render:OnSelectChange(false)
		end
	end
	self:FreshSmallElementKFBoss()
end

-- 界面上显示的小部件单独一个函数来操作
function WorldServerView:FreshSmallElementKFBoss()
	local boss_data = self.select_item_data
	if nil == boss_data then return end

	self.node_list["Txt_goto_kill"].text.text = boss_data.type == BossWGData.MonsterType.Gather and
		Language.Boss.BossGoToScene or Language.Common.GotoKillBoss

	self.node_list.kf_boss_txt_tip.text.text = Language.Boss.KFBossTips

	self.node_list.kf_boss_text_btn:SetActive(boss_data.type ~= BossWGData.MonsterType.Gather)
	if boss_data.type ~= BossWGData.MonsterType.Gather then
		local boss_index = BossWGData.Instance:GetCrossBossInfoByBossId(boss_data.boss_id).boss_index
		local concern_info = BossWGData.Instance:GetCrossBossIsConcern(self.cur_kflayer, boss_index)
		local is_concern = concern_info ~= nil and concern_info > 0
		self.node_list.kf_boss_text_btn.text.text = is_concern and Language.Boss.BossResurgenceBtnTips2 or Language.Boss.BossResurgenceBtnTips
	end

	if self:CheckIsShowOtherInfo(boss_data) then
		local new_gather_info = BossWGData.Instance:GetNewKFBossGatherInfo()
		if new_gather_info and new_gather_info[1] then
			local num1 = 1 - (new_gather_info[1] or 0)
			local num2 = 1 - (new_gather_info[2] or 0)
			local num3 = 1 - (new_gather_info[3] or 0)
			local color1 = num1 > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED
			local color2 = num2 > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED
			local color3 = num3 > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED

			self.node_list.text_have_crystal.text.text = string.format(Language.Boss.NewGatherLeftTime, boss_data.boss_name,
				color1, num1, boss_data.boss_name, color2, num2, boss_data.boss_name, color3, num3)
		else
			self.node_list["text_have_crystal"].text.text = Language.Boss.AllHaveReward
		end
	else
		self.node_list["text_have_crystal"].text.text = Language.Boss.AllHaveReward
	end

	self:FlushTodayCrystal()
end

function WorldServerView:FlushTodayCrystal()
	local boss_data = self.select_item_data
	if not self.node_list["text_today_crystal"] or not boss_data then
		return
	end

	local num, _ = BossWGData.Instance:GetTreasureGatherTimes()

	local color = num > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED
	local max_times = BossWGData.Instance:GetTreasureGatherMaxTimes()
	local str = ToColorStr(string.format("%d/%d", num, max_times), color)
	self.node_list["text_today_crystal"].text.text = string.format(Language.Boss.KfTireValue[1], str)
	local tire_value, max_tire_value = BossWGData.Instance:GetCrossBossTire()
	local str_num2 = self:SetStrColor(tire_value, max_tire_value)
	self.node_list["text_today_times"].text.text = string.format(Language.Boss.KfTireValue[2], str_num2)
	self.kf_gather_render:SetData(BossWGData.Instance:GetKFGatherInfo(self.cur_kflayer))
end

function WorldServerView:CheckIsShowOtherInfo(boss_data)
	return boss_data.type == BossWGData.MonsterType.Monster or boss_data.type == BossWGData.MonsterType.Gather
end

function WorldServerView:SetStrColor(tire_value, max_tire_value)
	local color = COLOR3B.DEFAULT_NUM
	if max_tire_value then
		color = tire_value < max_tire_value and color or COLOR3B.D_RED
		local str = string.format("%d/%d", max_tire_value - tire_value, max_tire_value)
		return ToColorStr(str, color)
	else
		color = tire_value > 0 and color or COLOR3B.D_RED
		return ToColorStr(tire_value, color)
	end

end

function WorldServerView:BrowseKfPlayInfo()
	local des, title = Language.Boss.KfPlayDes, Language.Boss.PlayTitle
	local role_tip = RuleTip.Instance
	role_tip:SetTitle(title)
	role_tip:SetContent(des)
end

function WorldServerView:GoToKillMH()
	if not self.select_item_data then
		return
	end
	if Scene.Instance:GetSceneType() == SceneType.KF_BOSS then
		if Scene.Instance:GetSceneId() == self.select_item_data.scene_id then
			GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
			BossWGData.Instance:SetCurSelectBossID(BossWGData.BossType.KF_BOSS, self.cur_kflayer, self.select_item_data.boss_id)
			MainuiWGCtrl.Instance:SetLightBossId(self.select_item_data.boss_id)
			BossWGCtrl.Instance:MoveToBoss(self.select_item_data.boss_id)
			self:Close()
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.OutFubenTip)
		end
		return
	end
	BossWGData.Instance:SetCurSelectBossID(BossWGData.BossType.KF_BOSS, self.cur_kflayer, self.cur_kfboss_id)
	BossWGData.Instance:SetOldBossID(TabIndex.worserv_boss_mh + 100, self.cur_kfboss_id)
	CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_BOSS, self.cur_kflayer)
	BossWGData.Instance:GetSetLastSelectInfo(self.cur_kflayer, self.list_index)
	--BossWGData.Instance:SetCurSelectBossID(BossWGData.BossType.KF_BOSS , self.cur_kflayer, self.cur_kfboss_id)
end

function WorldServerView:GetKFBossDefalutIndex(default_index, list_data)
	default_index = #list_data
	local role_level = RoleWGData.Instance:GetRoleLevel()
	for i, v in ipairs(list_data) do
		if v.boss_level and v.boss_level == role_level then
			default_index = i
			break
		elseif v.boss_level and v.boss_level > role_level then
			default_index = i - 1
			default_index = default_index > 0 and default_index or 1
			break
		end
	end
	return default_index
end

function WorldServerView:KFBossConcernBtn()
	local boss_data = self.select_item_data
	if boss_data.type ~= BossWGData.MonsterType.Gather then
		local boss_index = BossWGData.Instance:GetCrossBossInfoByBossId(boss_data.boss_id).boss_index
		local concern_info = BossWGData.Instance:GetCrossBossIsConcern(self.cur_kflayer, boss_index)
		local is_concern = concern_info ~= nil and concern_info > 0
		local concern_opera = is_concern and BossView.KfReqType.UNCONCERN or BossView.KfReqType.CONCERN
		BossWGCtrl.Instance:SendCrossBossReq(concern_opera, self.cur_kflayer, boss_data.boss_id)
		BossWGData.Instance:SetCrossBossConcern(self.cur_kflayer, boss_data.boss_id, not is_concern, TabIndex.worserv_boss_mh)
	end
end

BossKFGatherItemRender = BossKFGatherItemRender or BaseClass(BaseRender)
function BossKFGatherItemRender:__init()
end

function BossKFGatherItemRender:__delete()
	self:CleanRefreshTime()
end

function BossKFGatherItemRender:CleanRefreshTime()
	if self.refresh_event then
		GlobalTimerQuest:CancelQuest(self.refresh_event)
		self.refresh_event = nil
	end
end

function BossKFGatherItemRender:OnFlush()
	local bundle, asset = ResPath.GetBossIcon("wrod_boss_" .. self.data.big_icon)
	self.node_list["img_boss"].image:LoadSprite(bundle, asset, function()
		self.node_list.img_boss.image:SetNativeSize()
	end)

	self:ShowNormalInfo()
end

function BossKFGatherItemRender:ShowNormalInfo()
	local str_name = self.data.boss_name
	-- 策划说不再显示场景有几个采集点.
	-- if self.data.boss_id == BossWGData.Instance:GetSGGatherShow() then
	-- 	local num = BossWGData.Instance:GetShangGuBossSceneOtherInfo(self.data.layer + 1, self.data.type)
	-- 	num = num or 0
	-- 	str_name = num ~= 0 and string.format("%s（%d）", str_name, num) or str_name
	-- else
	-- 	local layer_num = BossWGData.Instance:GetLayerNumByShowIndex(TabIndex.worserv_boss_hmsy + 1)
	-- 	for layer = 1, layer_num do
	-- 		local gather = BossWGData.Instance:GetKFGatherInfo(layer)
	-- 		if gather.boss_id == self.data.boss_id then
	-- 			local boss_info = BossWGData.Instance:GetCrossBossById(self.data.boss_id)
	-- 			if boss_info and boss_info.left_num then
	-- 				str_name = boss_info.left_num ~= 0 and string.format("%s（%d）", str_name, boss_info.left_num) or str_name
	-- 				break
	-- 			end
	-- 		end
	-- 	end
	-- end

	self.node_list["text_boss_name"].text.text = str_name
	self.node_list.text_boss_lv.text.text = string.format(Language.Boss.LvText, self.data.boss_level)

	self:RefreshRemainTime()
	if self.refresh_event == nil then
		self.refresh_event = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.RefreshRemainTime, self), 1)
	end
end

function BossKFGatherItemRender:RefreshRemainTime()
	local boss_server_info = BossWGData.Instance:GetBossRefreshInfoByBossId(self.data.boss_id)
	if boss_server_info == nil then
		boss_server_info = BossWGData.Instance:GetCrossBossById(self.data.boss_id)
	end

	local time = 0
	if boss_server_info ~= nil then
		time = (boss_server_info.next_refresh_time or 0) - TimeWGCtrl.Instance:GetServerTime()
	end

	local state = time > 1
	if state then
		self.node_list["lbl_time"].text.text = TimeUtil.FormatSecond(time)
		self.node_list["text_boss_state"].text.text = ""
		-- self.node_list["gray_mask"]:SetActive(true)
	else
		self.node_list["lbl_time"].text.text = ""
		self.node_list["text_boss_state"].text.text = Language.Boss.YiShuaxin
		-- self.node_list["gray_mask"]:SetActive(false)
		self:CleanRefreshTime()
	end
end

function BossKFGatherItemRender:OnSelectChange(is_select)
	self.node_list.select_image:SetActive(is_select)
end
