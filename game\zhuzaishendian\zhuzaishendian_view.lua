ZhuZaiShenDianView = ZhuZaiShenDianView or BaseClass(SafeBaseView)


function ZhuZaiShenDianView:__init()
	self.view_style = ViewStyle.Full
	self.default_index = TabIndex.zhuzaishendian

	self:AddViewResource(0, "uis/view/zhuzaishendian_ui_prefab", "layout_zhuzawi_panel_down")
	self:AddViewResource(0, "uis/view/tianshen_prefab", "VerticalTabbar")
	self:AddViewResource(TabIndex.zhuzaishendian, "uis/view/zhuzaishendian_ui_prefab", "layout_zhuzaishendian")
end

function ZhuZaiShenDianView:__delete()
end

function ZhuZaiShenDianView:ReleaseCallBack()
	if self.battle_role_display then
		self.battle_role_display:DeleteMe()
		self.battle_role_display = nil
	end
	if self.role_name_board then
		self.role_name_board:DeleteMe()
		self.role_name_board = nil
	end

	if nil ~= self.item_cells then
		for k, v in pairs(self.item_cells) do
			v:DeleteMe()
		end
		self.item_cells = nil
	end

	if nil ~= self.bzitem_cells then
		for k, v in pairs(self.bzitem_cells) do
			v:DeleteMe()
		end
		self.bzitem_cells = nil
	end

	if self.chengzhu_title then
		self.chengzhu_title:DeleteMe()
		self.chengzhu_title = nil
	end

	if self.role_model_1 then
		self.role_model_1:DeleteMe()
		self.role_model_1 = nil
	end

	if self.mount_model then
		self.mount_model:DeleteMe()
		self.mount_model = nil
	end

	if self.lingchong_model then
		self.lingchong_model:DeleteMe()
		self.lingchong_model = nil
	end

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
end

function ZhuZaiShenDianView:LoadCallBack(index, loaded_times)
	local remind_tab = {
		{ RemindName.ZhuZaiShenDian },
	}
	self.tabbar = Tabbar.New(self.node_list)
	self.tabbar:SetVerTabbarIconStr("zhuzai_view1")
	self.tabbar:SetVerTabbarCellName("TianShen_VerticalTabbarCell")
	self.tabbar:Init(Language.ZhuZaiShenDian.TabGrop, nil, "uis/view/tianshen_prefab", nil, remind_tab)
	--self.tabbar:SetSelectCallback(BindTool.Bind1(self.TabbarClick, self))
	-- self.node_list.title_view_name.text.text = Language.Rank.PaiHangTitle
	-- self.node_list.title_view_name.text.lineSpacing = 1
end

function ZhuZaiShenDianView:ShowIndexCallBack(index)
	if index == TabIndex.zhuzaishendian then
		self:InitzhuzaishendianView()
	end
end

function ZhuZaiShenDianView:InitzhuzaishendianView()
	self.item_cells = {}
	self.node_list.img_ls_remind:SetActive(false)
	self.node_list.img_reward_remingd:SetActive(false)
	for i = 0, 1 do
		self.item_cells[i] = ItemCell.New()
		self.item_cells[i]:SetInstanceParent(self.node_list["ph_rewarditem_" .. i])
		self.node_list["ph_rewarditem_" .. i]:SetActive(false)
	end
	self.bzitem_cells = {}

	for i = 0, 1 do
		self.bzitem_cells[i] = ItemCell.New()
		self.bzitem_cells[i]:SetInstanceParent(self.node_list["ph_item_" .. i])
	end
	--self.node_list.title_view_name.text.text = Language.ZhuZaiShenDian.Name
	self.node_list.btn_chakan.button:AddClickListener(BindTool.Bind(self.OnClickExamine, self))
	self.node_list.btn_reward.button:AddClickListener(BindTool.Bind(self.GoReward, self))
	self.node_list.btn_zhongjie.button:AddClickListener(BindTool.Bind(self.GozhongJie, self))
	self.node_list.btn_reward2.button:AddClickListener(BindTool.Bind(self.GoFenpei, self))
	self.node_list.btn_close_window.button:AddClickListener(BindTool.Bind(self.Close, self))
	--self.node_list.btn_activity_states.button:AddClickListener(BindTool.Bind(self.TipsWar, self))
	-- 人物形象展示
	if self.role_model_1 == nil then
		local role_model_zz = self.node_list.role_model_zz
		self.role_model_1 = RoleModel.New()
		local display_data = {
			parent_node = role_model_zz,
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}
		
		self.role_model_1:SetRenderTexUI3DModel(display_data)
		-- self.role_model_1:SetUI3DModel(role_model_zz.transform, role_model_zz.event_trigger_listener, 1, false,
		-- 	MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.role_model_1)
	end

	if self.lingchong_model == nil then
		local lingchong_model_zz = self.node_list.lingchong_model_zz
		self.lingchong_model = RoleModel.New()
		local display_data = {
			parent_node = lingchong_model_zz,
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.S,
			can_drag = true,
		}
		
		self.lingchong_model:SetRenderTexUI3DModel(display_data)
		-- self.lingchong_model:SetUI3DModel(lingchong_model_zz.transform, lingchong_model_zz.event_trigger_listener, 3,
		-- 	false, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.lingchong_model)
	end

	if self.mount_model == nil then
		local mount_model_zz = self.node_list.mount_model_zz
		self.mount_model = RoleModel.New()
		local display_data = {
			parent_node = mount_model_zz,
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}
		
		self.mount_model:SetRenderTexUI3DModel(display_data)
		-- self.mount_model:SetUI3DModel(mount_model_zz.transform, mount_model_zz.event_trigger_listener, 4, false,
		-- 	MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.mount_model)
	end
end

function ZhuZaiShenDianView:TipsWar()
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetTitle(Language.Guild.BattleTips)
		role_tip:SetContent(Language.Guild.BattleTipContent, nil, nil, nil, true)
	else
		print_error("AppearanceView", "OnClickBtnMLImageChongTip() can not find the get way!")
	end
end

function ZhuZaiShenDianView:OnNorBossTips()
end

function ZhuZaiShenDianView:OnClickExamine()
	ViewManager.Instance:Open(GuideModuleName.GuildWarDuiZhengView)
end

function ZhuZaiShenDianView:GoReward()
	ZhuZaiShenDianWGCtrl.Instance:SendCSGuildBattleRewardReq()
end

function ZhuZaiShenDianView:GozhongJie()
	ZhuZaiShenDianWGCtrl.Instance:OpenZhuZaiRewardView(4)
end

function ZhuZaiShenDianView:GoFenpei()
	ZhuZaiShenDianWGCtrl.Instance:OpenZhuZaiRewardView(3)
end

function ZhuZaiShenDianView:OpenCallBack()
	ZhuZaiShenDianWGCtrl.Instance:SendCSGuildBattleRankInfoReq()
	local guild_id = RoleWGData.Instance.role_vo.guild_id
	GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, guild_id)
	ZhuZaiShenDianWGCtrl.Instance:SendCSGuildBattleRankInfoReq(GUILD_BATTLE_INFO_REQ_TYPE
	.GUILD_BATTLE_INFO_REQ_TYPE_MATCH)
end

function ZhuZaiShenDianView:CloseCallBack()
end

function ZhuZaiShenDianView:FlushZhuZaiShenDian()
	self.node_list.reward_text.text.text = Language.ZhuZaiShenDian.LingQueShiFeng
	self.node_list.btn_reward.button.enabled = true
	XUI.SetButtonEnabled(self.node_list.btn_reward, false)
	local fight_state = GuildWGData.Instance:GetGuildBattleFightState()
	local can_show = fight_state == GUILD_BATTLE_STATE_TYPE.GUILD_BATTLE_STATE_TYPE_ACTIVITY_END or
		fight_state == GUILD_BATTLE_STATE_TYPE.GUILD_BATTLE_STATE_TYPE_SECOND_FIGHT_END

	local uid = ZhuZaiShenDianWGData.Instance:GetMengZhuUid()
	if nil == uid then return end
	local is_have_ower = uid > 0 and can_show
	if is_have_ower then
		BrowseWGCtrl.Instance:BrowRoelInfo(uid, BindTool.Bind(self.GongChengOwnerVoAck, self))
	else
		self.node_list.lbl_mengzhuname.text.text = Language.KuafuGuildBattle.KfNoOccupy
	end

	self.node_list.img_chengzhu_xuwei:SetActive(not is_have_ower)
	self.node_list.effect_zz:SetActive(true)
	self.node_list["title_img"]:SetActive(true)
	self.node_list["layout_t_bg"]:SetActive(is_have_ower)


	local num = {}
	self.node_list.lbl_firstguild.text.text = Language.OpenServer.XuWeiYiDai
	num = ZhuZaiShenDianWGData.Instance:GetGuildId()
	local receive_sign = ZhuZaiShenDianWGData.Instance:GetReceiveFlag()
	local guild_id = RoleWGData.Instance.role_vo.guild_id
	if num and can_show then
		if guild_id == num[1].guild_id and is_have_ower then
			XUI.SetButtonEnabled(self.node_list.btn_reward, true)
		end
		local win_streak = ZhuZaiShenDianWGData.Instance:GetLianShen()
		if win_streak > 0 then
			local name = num[1].guild_name
			self.node_list.lbl_firstguild.text.text = name
		end

		self.node_list.lbl_lianshencishu.text.text = string.format(Language.ZhuZaiShenDian.LianShengStr, win_streak)
	else
		self.node_list.lbl_lianshencishu.text.text = string.format(Language.ZhuZaiShenDian.LianShengStr, 0)
	end
	if receive_sign == 1 and guild_id == num[1].guild_id then
		local color = COLOR3B.WHITE
		self.node_list.reward_text.text.text = ToColorStr(Language.Common.YiLingQu, color)

		local img_bundle, image_name
		img_bundle, image_name = ResPath.GetF2CommonImages("biaoqian_1")
		self.node_list.btn_reward.image:LoadSprite(img_bundle, image_name)
		self.node_list.btn_reward.button.enabled = false
	end

	if receive_sign and num then
		if receive_sign == 0 and guild_id == num[1].guild_id and num[1].guild_id > 0 and is_have_ower then
			self.node_list.img_reward_remingd:SetActive(true)
		else
			self.node_list.img_reward_remingd:SetActive(false)
		end
	end

	self.role_id = GameVoManager.Instance:GetMainRoleVo().role_id
	local bangzhuuid = ZhuZaiShenDianWGData.Instance:GetMengZhuUid()
	local is_active = ZhuZaiShenDianWGData.Instance:IsCanGet()
	local winning_streak = ZhuZaiShenDianWGData.Instance:GetLianShen()
	if is_active and guild_id == num[1].guild_id and self.role_id == bangzhuuid and winning_streak > 1 then
		self.node_list.img_ls_remind:SetActive(true)
	else
		self.node_list.img_ls_remind:SetActive(false)
	end
end

function ZhuZaiShenDianView:OnFlush(param_t, index)
	if index == TabIndex.zhuzaishendian then
		self:FlushZhuZaiShenDian()
	end
end

function ZhuZaiShenDianView:GongChengOwnerVoAck(owner_vo)
	if owner_vo.role_name ~= nil then
		if self.node_list.lbl_mengzhuname and self.node_list.lbl_firstguild then
			self.node_list.lbl_mengzhuname.text.text = Language.ZhuZaiShenDian.Owener .. owner_vo.role_name
		end

		ZhuZaiShenDianWGData.Instance:SetRoleBaseInfoAck(owner_vo)
		if self.role_model_1 then
			local ignore_table = { ignore_wing = true, ignore_jianzhen = true }
			self.role_model_1:SetModelResInfo(ZhuZaiShenDianWGData.Instance:GetRoleBaseInfoAck(), ignore_table)
		end

		if self.mount_model and owner_vo.mountattr and owner_vo.mountattr.used_imageid >= 0 then
			self.mount_model:SetMainAsset(ResPath.GetMountModel(owner_vo.mountattr.used_imageid))
		end

		if self.lingchong_model and owner_vo.lingchong and owner_vo.lingchong.used_imageid >= 0 then
			self.lingchong_model:SetMainAsset(ResPath.GetPetModel(owner_vo.lingchong.used_imageid))
		end
	else
		self.node_list.lbl_mengzhuname.text.text = Language.KuafuGuildBattle.KfNoOccupy
	end
end
