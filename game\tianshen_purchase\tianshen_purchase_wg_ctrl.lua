require("game/tianshen_purchase/tianshen_purchase_view")
require("game/tianshen_purchase/tianshen_purchase_wg_data")
require("game/tianshen_purchase/tianshen_purchase_gift_show_view")

TianShenPurchaseWGCtrl = TianShenPurchaseWGCtrl or BaseClass(BaseWGCtrl)

function TianShenPurchaseWGCtrl:__init()
	if TianShenPurchaseWGCtrl.Instance then
		ErrorLog("[TianShenPurchaseWGCtrl] attempt to create singleton twice!")
		return
	end

	TianShenPurchaseWGCtrl.Instance = self
	self.data = TianShenPurchaseWGData.New()
    self.view = TianShenPurchaseView.New(GuideModuleName.TianShenPurchaseView)
    self.gift_view = TianShenPurchaseGiftShowView.New()
  
    self:RegisterAllProtocols()
end

function TianShenPurchaseWGCtrl:__delete()
	TianShenPurchaseWGCtrl.Instance = nil

	self.view:DeleteMe()
	self.view = nil

    self.data:DeleteMe()
	self.data = nil

	self.gift_view:DeleteMe()
	self.gift_view = nil
end

function TianShenPurchaseWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOATianShenRmbBuyInfo,"OnSCOATianShenRmbBuyInfo")

	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
end

function TianShenPurchaseWGCtrl:ReqTianShenPurchaseInfo(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TIANSHEN_RMB_BUY
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function TianShenPurchaseWGCtrl:OnSCOATianShenRmbBuyInfo(protocol)
	--print_error("========天神直购========",protocol)
	self.data:SetAllInfo(protocol)
	self:CheckNeedCloseAct()
	ViewManager.Instance:FlushView(GuideModuleName.TianShenPurchaseView)
end

--跨天信息
function TianShenPurchaseWGCtrl:OnPassDay()
	self:ReqTianShenPurchaseInfo(OA_TIANSHEN_RMB_BUY_OPERATE_TYPE.INFO)
end

function TianShenPurchaseWGCtrl:CheckNeedCloseAct()
	if TimeWGCtrl.Instance:GetCurOpenServerDay() < 0 then
		-- print_error("FFF====== 时间没下来")
		return
    end 
    
	if self.data:GetCurShopIsAllBuy() then--奖励领取完了,关闭入口
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TIANSHEN_RMB_BUY, ACTIVITY_STATUS.CLOSE)
	end
end

function TianShenPurchaseWGCtrl:ShowGiftView(data_list)
	self.gift_view:Flush(0, "gift_info", {data_list = data_list})
	self.gift_view:Open()
end