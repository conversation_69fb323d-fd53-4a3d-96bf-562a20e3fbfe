WelfareView = WelfareView or BaseClass(SafeBaseView)

WELFARE_TYPE =
{
	-- KUANGHUAN = math.floor(TabIndex.welfare_kuanghuan),			-- 狂欢 （七天登录）
	-- HUOYUE = math.floor(TabIndex.welfare_huoyue / 1000),				-- 活跃
	-- TIME_ACITVITY = math.floor(TabIndex.welfare_time_acitvity / 1000),	-- 限时活动
	QIANDAO = math.floor(TabIndex.welfare_qiandao),				-- 每月签到
	WEKQIANDAO = math.floor(TabIndex.welfare_wekqiandao),       --每周签到
	--WORLD_PAPER = math.floor(TabIndex.world_paper),             -- 世界红包
	-- DAILYFIND = math.floor(TabIndex.welfare_dailyfind),		-- 每日找回
	SHENGJI = math.floor(TabIndex.welfare_upgrade),				-- 等级礼包
	VIPGIFT = math.floor(TabIndex.welfare_vipgift),				-- vip礼包
	-- FRIEND = math.floor(TabIndex.welfare_friend_req),		-- 好友畅玩
	--ONLINEREWARD = math.floor(TabIndex.welfare_online_reward),	-- 在线礼包
	LIBAO = math.floor(TabIndex.welfare_libao),					-- 礼包
	-- ZAIXIAN = math.floor(TabIndex.welfare_zaixian),			-- 在线
	-- GONGGAO = math.floor(TabIndex.welfare_gonggao),				-- 更新公告(策划要求干掉)
	--QIFU = math.floor(TabIndex.welfare_qifu),				-- 祈福
	PHONEBIND = math.floor(TabIndex.welfare_phone_bind),				-- 手机绑定
}

function WelfareView:__init()
	self.view_style = ViewStyle.Full
	self.is_align_right = true						-- 是否向右对齐
	self.need_check_redPoint = true
	self.default_index = TabIndex.welfare_qiandao
	self:SetMaskBg()
	self.is_safe_area_adapter = true

	local bundle_path = "uis/view/welfare_ui_prefab"
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
	self:AddViewResource(WELFARE_TYPE.LIBAO, bundle_path, "layout_libao")
	--self:AddViewResource(WELFARE_TYPE.ONLINEREWARD, bundle_path, "layout_onlinereward_view")
	--self:AddViewResource(0, "uis/view/common_panel_prefab", "HorizontalTabbar")
	self:AddViewResource(WELFARE_TYPE.QIANDAO, bundle_path, "layout_qiandao")
	-- self:AddViewResource(WELFARE_TYPE.WORLD_PAPER, "uis/view/guild_ui_prefab", "layout_red_packet")
	self:AddViewResource(WELFARE_TYPE.WEKQIANDAO, bundle_path, "layout_accumulative_login")
	self:AddViewResource(WELFARE_TYPE.SHENGJI, bundle_path, "layout_sjfuli")
	self:AddViewResource(WELFARE_TYPE.VIPGIFT, bundle_path, "layout_vipgift")
	-- self:AddViewResource(WELFARE_TYPE.GONGGAO, bundle_path, "layout_affiche")
	self:AddViewResource(WELFARE_TYPE.PHONEBIND, bundle_path, "phone_bind_view")
	self:AddViewResource(0, bundle_path, "VerticalTabbar")
	self:AddViewResource(0, bundle_path, "layout_a3_top_panel")

	self.tab_sub = {}
	self.remind_tab = {
		{RemindName.WelfareQianDao},
		--{RemindName.WelfareOnlineGift},
		{RemindName.AccumulativeLogin},
		{RemindName.WelfareLevelGift},
		{RemindName.WelfareVipGift},
		-- {RemindName.WelfareGongGao},
		{},
		{RemindName.WelfarePhoneBind},
	}
end

function WelfareView:__delete()

end

function WelfareView:OpenCallBack()
	if nil ~= self.money_bar then
		self.money_bar:AddAllListen()
	end

	WelfareWGCtrl.Instance:SendOnlineRewardInfo()
	-- AudioManager.Instance:PlayOpenCloseUiEffect()
	local gonggao_info = WelfareWGData.Instance:GetNotifyInfo()
	if not gonggao_info.is_new then
		WelfareWGCtrl.Instance:GetNotifyInfo()
	end

end

function WelfareView:LoadCallBack()

	--动态加载背景
	local bundle, asset = ResPath.GetRawImagesPNG("a3_hdty_bg_1")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

	--功能引导注册
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.Welfare, self.get_guide_ui_event)

	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list, "VerticalTabbar")
		self.tabbar:SetVerTabbarCellName("VerticalTabbarCell1")
		self.tabbar:Init(Language.Welfare.TabGrop, nil, "uis/view/welfare_ui_prefab", nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.OnTabChangeHandler, self))
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	--FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.Welfare, self.get_guide_ui_event)
	FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.Welfare, self.tabbar)

	--WelfareWGCtrl.Instance:RequestPhoneBindInfo()
	self:updatePhoneBindTab()
end

function WelfareView:updatePhoneBindTab()
	if self.tabbar then
		self.tabbar:SetToggleVisible(WELFARE_TYPE.PHONEBIND, WelfareWGCtrl.Instance:IsPhoneBindOpen())
	end
end


function WelfareView:LoadIndexCallBack(index)
	if index == WELFARE_TYPE.DAILYFIND then
		-- self:InitDailyFindView()
	elseif index == WELFARE_TYPE.SHENGJI then
		self:InitUpGradeView()
	elseif index == WELFARE_TYPE.VIPGIFT then
		self:InitVipGiftView()
	elseif index == WELFARE_TYPE.ONLINEREWARD then
		--self:InitOnlineRewardView()
	elseif index == WELFARE_TYPE.QIANDAO then
		self:InitQianDaoView()
	elseif index == WELFARE_TYPE.WEKQIANDAO then
		self:InitWekQianDaoView()
	-- elseif index == WELFARE_TYPE.GONGGAO then
	-- 	self:InitUpdateAfficheView()
	elseif index == WELFARE_TYPE.PHONEBIND then
		self:InitPhoneBind()
	elseif index == WELFARE_TYPE.LIBAO then
		self:InitGiftView()
	end
end

function WelfareView:SetTabVisible(index)
	if nil == self.tabbar then return end
	local seven_day_dataes = WelfareWGData.Instance:GetSevenDayCfg() --七天礼包是否可领取
	for k,v in pairs(seven_day_dataes) do
		if v.status ~= COMPLETE_STATUS.YILINGQU then--表示没有领取完
			return true
		end
	end
	return false
end

function WelfareView:ShowIndexCallBack(index)
	if index == WELFARE_TYPE.QIANDAO then
		self:ShowIndexCallBackQianDao()
	elseif index == WELFARE_TYPE.ONLINEREWARD then
		WelfareWGCtrl.Instance:SendOnlineGiftOperReq(ONLINE_GIFT_OPERA_TYPE.ONLINE_GIFT_OPERA_TYPE_ASK_INFO)
		WelfareWGData.Instance:GetAniCanPlay(true)
	elseif index == WELFARE_TYPE.SHENGJI then
		self:OnFlushUpGrade()
	elseif index == WELFARE_TYPE.PHONEBIND then
		self:RefreshPhoneBind()
	elseif index == WELFARE_TYPE.WEKQIANDAO then
		self:OpenCallBackWekQianDao()
	end
end

function WelfareView:CloseCallBack(is_all)
	if nil ~= self.money_bar then
		self.money_bar:UnAllListen()
	end
end

function WelfareView:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	-- self:DescoryDailyFind()
	--self:DeleteRedPacketView()
	self:DescoryUpGrade()
	self:DescoryGift()
	-- self:DeleteSevenDayView()
	self:DeleteQianDaoView()
	self:DeleteWekQianDaoView()
	self:DeleteUpdateAffiche()
	self:DeleteFriend()
	self:DeleteZaiXianView()
	-- self:DeleteHuoYueView()
	--self:DeleteQiFuView()
	self:DescoryVipGift()
	self:DescoryPhoneBind()	
	--self:DescoryOnlineReward()
	if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.Welfare, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end
end

function WelfareView:OnTabChangeHandler(index)
	WelfareWGCtrl.Instance:SendOnlineRewardInfo()
	self:ChangeToIndex(index)
end

function WelfareView:OnFlush(param_t, index)
	if IS_AUDIT_VERSION then
		print_error("IS_AUDIT_VERSION")
	end

	for k, v in pairs(param_t) do
		if "all" == k then
			if index == WELFARE_TYPE.SHENGJI then
				self:OnFlushUpGrade()
			elseif index == WELFARE_TYPE.VIPGIFT then
				self:OnFlushVipGift()
			elseif index == WELFARE_TYPE.WEKQIANDAO then
				self:OnFlushWekQianDaoView()
			elseif index == WELFARE_TYPE.ONLINEREWARD then
				--self:OnFlushOnlineReward()
			elseif index == WELFARE_TYPE.LIBAO then
				self:OnFlushGift()
			elseif index == WELFARE_TYPE.FRIEND then
				self:FlushFriend()
			elseif index == WELFARE_TYPE.QIANDAO then--签到
				self:FlushQianDaoView()
				self:SetTabVisible()
			elseif index == WELFARE_TYPE.ZAIXIAN then
				self:FlushZaiXianView()
			-- elseif index == WELFARE_TYPE.GONGGAO then
			-- 	self:FlushUpdateAffiche()
			elseif index == WELFARE_TYPE.PHONEBIND then
				self:FlushBindPhone()

			end
		end
		if k == "flush_button_verif_state" then
			self:ChangeButtonEnable()
		end
	end
end

function WelfareView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.Tab then
		local tab_index = math.floor(TabIndex[ui_param])
		if tab_index == self:GetShowIndex() then
			return NextGuideStepFlag
		else
			return self.tabbar:GetToggleByIndex(tab_index)
		end
	elseif ui_name == GuideUIName.CloseBtn then
		return self.node_list.btn_close_window, BindTool.Bind1(self.Close, self)
	end
	return nil, nil
end

--跨服中不可操作
function WelfareView:KuaFuNoCue()
	if IS_ON_CROSSSERVER then
		self.tabbar:SetOtherBtn(WELFARE_TYPE.SHENGJI, true,BindTool.Bind(self.ClickOKWelfareBtn,self))
		self.tabbar:SetOtherBtn(WELFARE_TYPE.VIPGIFT, true,BindTool.Bind(self.ClickOKWelfareBtn,self))
		return
	end
	self.tabbar:SetOtherBtn(WELFARE_TYPE.SHENGJI, false,nil)
	self.tabbar:SetOtherBtn(WELFARE_TYPE.VIPGIFT, false,nil)
end

function WelfareView:ClickOKWelfareBtn()
	SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.KuaFuHint)
end
