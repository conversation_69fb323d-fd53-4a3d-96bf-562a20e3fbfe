MianuiStrongGetRewardView = MianuiStrongGetRewardView or BaseClass(SafeBaseView)

function MianuiStrongGetRewardView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
	self.mask_alpha = 0
	self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
	self:AddViewResource(0, "uis/view/activity_ui_prefab", "layout_strong_get_reward_view")
end

function MianuiStrongGetRewardView:LoadCallBack()
	if not self.strong_reward_item_list then
		self.strong_reward_item_list = AsyncListView.New(StrongMenuGetRewardItemCellRender, self.node_list.strong_reward_item_list)
		self.strong_reward_item_list:SetDefaultSelectIndex(nil)
		self.strong_reward_item_list:SetSelectCallBack(BindTool.Bind(self.OnClickOpen, self))
	end
end

function MianuiStrongGetRewardView:ReleaseCallBack()
	if self.strong_reward_item_list then
		self.strong_reward_item_list:DeleteMe()
		self.strong_reward_item_list = nil
	end
end

function MianuiStrongGetRewardView:OnFlush()
	local data_list = MainuiWGData.Instance:GetBianQiangGetRewardListCache()
	if not data_list then return end
	local _tmp = {}
	for i,v in ipairs(data_list) do
		if v and v.repetition_num > 0 and v.callback ~= nil then
			table.insert(_tmp, v)
		end
	end

	self.strong_reward_item_list:SetDataList(_tmp)
end

function MianuiStrongGetRewardView:OnClickOpen(item)
	if nil == item.data then
		return
	end

	if item.data.callback then
		local flag = item.data.callback(item.data.callback_param or nil)
		
		if not flag then
			MainuiWGCtrl.Instance:RemoveTipIconByIconObj(item.data.tip_type, true)
		end
	end
    
	self:Close()
end

----------------------------------------------OpenServerMenuItem----------------------------------------------
StrongMenuGetRewardItemCellRender = StrongMenuGetRewardItemCellRender or BaseClass(BaseRender)
function StrongMenuGetRewardItemCellRender:OnFlush()
	if self.data then
		local is_special = self.data.tip_type == MAINUI_TIP_TYPE.SHOU_CHONG_TE_HUI
		if not is_special then
			self.node_list["lbl_caozuo_name"].text.text = Language.MainUIIcon[self.data.tip_type]
		else
			self.node_list["lbl_caozuo_name"].text.text = ""
		end
	end
end
