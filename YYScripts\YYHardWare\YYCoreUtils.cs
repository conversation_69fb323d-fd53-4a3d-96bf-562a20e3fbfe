﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

using System.IO;
using System;



namespace YYGot.OffLine
{
    public class YYCoreUtils
    {
        public static int frameId = -1;

        public static int durationS = -1;

        public static float StartTime = 0f;


        private static int ScreenWidth
        {
            get
            {
                if (Screen.width <= Screen.height)
                {
                    return Screen.height;
                }
                return Screen.width;
            }
        }

        private static int ScreenHeight
        {
            get
            {
                if (Screen.width <= Screen.height)
                {
                    return Screen.width;
                }
                return Screen.height;
            }
        }

        public static int GroupWidth
        {
            get
            {
                return (int)((float)ScreenWidth * 0.382000029f * ScreenSizeScale);
            }
        }

        public static float ScreenSizeScale
        {
            get
            {
                if (ScreenHeight < 750)
                {
                    return 1.5f;
                }
                return 1f;
            }
        }

        public static int GroupHeight
        {
            get
            {
                return (int)((float)ScreenHeight * 0.382000029f * 0.5f * ScreenSizeScale);
            }
        }

        private static GUILayoutOption[] GetSuitableOptionInternal(float width, float height, float x, float y)
        {
            if (!(y < 0f))
            {
                if (!(x < 0f))
                {
                    return (GUILayoutOption[])(object)new GUILayoutOption[4]
                    {
                        GUILayout.Width((float)(int)(width * x)),
                        GUILayout.Height((float)(int)(height * y)),
                        GUILayout.ExpandWidth(true),
                        GUILayout.ExpandHeight(true)
                    };
                }
                return (GUILayoutOption[])(object)new GUILayoutOption[2]
                {
                    GUILayout.Height((float)(int)(height * y)),
                    GUILayout.ExpandHeight(true)
                };
            }
            return (GUILayoutOption[])(object)new GUILayoutOption[2]
            {
                GUILayout.Width((float)(int)(width * x)),
                GUILayout.ExpandWidth(true)
            };
        }

        public static GUILayoutOption[] GetSuitableOption(float x, float y)
        {
            return GetSuitableOptionInternal(GroupWidth, GroupHeight, x, y);
        }

        public static Rect GroupRect
        {
            get
            {
                //IL_001d: Unknown result type (might be due to invalid IL or missing references)
                return new Rect((float)(Screen.width - GroupWidth), 0f, (float)GroupWidth, (float)GroupHeight);
            }
        }

        // ///////////////////////////////////////////////////////////////////////
        private static string finalDataPath = "";
        private static string YYDataCenterPath = "/YY-DataCenter";
        private static string configDataPath = "/ConfigData";
        private static string profileDataPath = "/ProfileData";
        public static string FinalDataPath
        {
            get
            {
                if (finalDataPath.Equals(""))
                {
                    YYDataCenterPath = GetExternalStorageDirectory() + YYDataCenterPath;
                    Debug.Log("------------------------------");
                    Debug.Log("YYDataCenterPath is " + YYDataCenterPath);
                    Debug.Log("------------------------------");
                    CheckPath();
                }
                try
                {
                    if (!Directory.Exists(finalDataPath))
                    {
                        Directory.CreateDirectory(finalDataPath);
                    }
                }
                catch (Exception e)
                {
                    Debug.Log("finalDataPath " + e.ToString());
                }
                return finalDataPath;
            }
        }

        private static void CheckPath()
        {
            try
            {
                if (!Directory.Exists(YYDataCenterPath))
                {
                    Directory.CreateDirectory(YYDataCenterPath);
                }
                if (!Directory.Exists(YYDataCenterPath + configDataPath))
                {
                    Directory.CreateDirectory(YYDataCenterPath + configDataPath);
                }
                if (!Directory.Exists(YYDataCenterPath + profileDataPath))
                {
                    Directory.CreateDirectory(YYDataCenterPath + profileDataPath);
                }
                finalDataPath = YYDataCenterPath + profileDataPath + "/" + YYCoreConfig.KEY.Replace(" ", "");
            }
            catch (Exception e)
            {
                Debug.Log("checkPath " + e.ToString());
            }
        }

        private static string GetExternalStorageDirectory()
        {
            string result = "";
            //
            if (Application.platform == RuntimePlatform.WindowsEditor)
            {
                result = Application.dataPath + "/..";  // 测试用
                return result;
            }
            //
            if (Application.platform != RuntimePlatform.OSXEditor && Application.platform != RuntimePlatform.WindowsEditor)
            {
                AndroidJavaClass val = (AndroidJavaClass)(object)new AndroidJavaClass("android.os.Environment");
                AndroidJavaObject val2 = ((AndroidJavaObject)val).CallStatic<AndroidJavaObject>("getExternalStorageDirectory", new object[1]
                {
                    0u
                });
                result = val2.Call<string>("getCanonicalPath", new object[0]);
            }
            return result;
        }

        public static bool ShowLog = true;
        // ////////////////////////////////////////////////
        public static bool VerifyWritePermision()
        {
            try
            {
                AndroidJavaClass val = (AndroidJavaClass)(object)new AndroidJavaClass("android.os.Build$VERSION");
                int @static = ((AndroidJavaObject)val).GetStatic<int>("SDK_INT");
                if (@static < 23)
                {
                    return true;
                }
                if (ShowLog)
                {
                    Debug.Log((object)("VerifyWritePermision SDK_INT : " + @static));
                }
                AndroidJavaClass val2 = (AndroidJavaClass)(object)new AndroidJavaClass("com.unity3d.player.UnityPlayer");
                AndroidJavaObject static2 = ((AndroidJavaObject)val2).GetStatic<AndroidJavaObject>("currentActivity");
                int num = 1;
                int num2 = 0;
                string[] array = new string[2]
                {
                    "android.permission.READ_EXTERNAL_STORAGE",
                    "android.permission.WRITE_EXTERNAL_STORAGE"
                };
                int num3 = static2.Call<int>("checkSelfPermission", new object[1]
                {
                    "android.permission.WRITE_EXTERNAL_STORAGE"
                });
                if (ShowLog)
                {
                    Debug.Log("check 11 is " + num3);
                    Debug.Log((object)("VerifyWritePermision : " + (num3 == num2).ToString()));
                }
                if (num3 != num2)
                {
                    static2.Call("requestPermissions", new object[2]
                    {
                        array,
                        num
                    });
                    if (ShowLog)
                    {
                        Debug.Log((object)"VerifyWritePermision  request  11 : requestPermissions");
                    }
                }
                num3 = static2.Call<int>("checkSelfPermission", new object[1]
                {
                    "android.permission.WRITE_EXTERNAL_STORAGE"
                });
                if (ShowLog)
                {
                    Debug.Log("check 22 is " + num3);
                    Debug.Log((object)("VerifyWritePermision  : " + (num3 == num2).ToString()));
                }
                return num3 == num2;
            }
            catch (Exception e)
            {
                Debug.Log(" is  VerifyWritePermision is " + e.ToString());
            }
            return true;
        }

        public static string GetFrameIdWithExtFilePath(string extension)
        {
            return string.Format("{0}/{1}{2}", FinalDataPath, frameId, extension);
        }


        private static bool _log2File = false;
        private static StreamWriter logSw = null;
        ///           /////////////////////////////////////////////////
        public static void Log(string content)
        {
            Debug.Log((object)content);
            if (_log2File && logSw != null)
            {
                logSw.WriteLine("UWA " + DateTime.Now.ToString("yyyyMMdd HHmmss :"));
                logSw.WriteLine(content);
                logSw.WriteLine("");
            }
        }

        public static string StringReplace(string orc)
        {
            return orc.Replace("\n", "^").Replace("\r", "^").Replace(",", "’");
        }

        public static string[] ReadAllLinesFromFile(string path)
        {
            List<string> list = new List<string>();
            using (StreamReader streamReader = new StreamReader(path))
            {
                string item;
                while ((item = streamReader.ReadLine()) != null)
                {
                    list.Add(item);
                }
            }
            return list.ToArray();
        }

    }

}
