VipActiveView = VipActiveView or BaseClass(SafeBaseView)

function VipActiveView:__init()
	self.view_layer = UiLayer.PopWhite
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/recharge_ui_prefab", "layout_baozhu_active")
end

function VipActiveView:ReleaseCallBack()

end

function VipActiveView:LoadCallBack(index, loaded_times)

end

function VipActiveView:OnFlush()
	local role_vip_level = VipWGData.Instance:GetRoleVipLevel()
	self.node_list.active_text.text.text = string.format(Language.Vip.VipTips_10, role_vip_level)
end