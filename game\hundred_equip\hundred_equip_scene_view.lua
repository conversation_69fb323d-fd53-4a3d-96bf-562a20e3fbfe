HundredEquipSceneView = HundredEquipSceneView or BaseClass(SafeBaseView)

function HundredEquipSceneView:__init()
	self.is_safe_area_adapter = true
	self:AddViewResource(0, "uis/view/hundred_equip_ui_prefab", "layout_equip_scene_view")
end

function HundredEquipSceneView:OpenCallBack()
	if self.obj and self:IsLoadedIndex(0) then
        self.obj:SetActive(true)
    end
end

function HundredEquipSceneView:LoadCallBack()
    MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))

	if not self.rank_list then
		self.rank_list = AsyncListView.New(HundredEquipSceneRankItemCellRender, self.node_list.rank_list)
		self.rank_list:SetStartZeroIndex(false)
	end

	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
		self.reward_list:SetStartZeroIndex(false)
	end
end

function HundredEquipSceneView:InitCallBack()
	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()
	if self.node_list["hundred_equip_task_root"] then
		self.obj = self.node_list["hundred_equip_task_root"].gameObject
		self.obj.transform:SetParent(parent.gameObject.transform)
		self.obj.transform.localPosition = Vector3(0, 0, 0)
		self.obj.transform.localScale = Vector3.one
	end

    if self.is_out_fb then
		self.obj:SetActive(false)
    end

    self.is_out_fb = nil
end

function HundredEquipSceneView:CloseCallBack()
	self.is_out_fb = true

    if self.obj then
        self.obj:SetActive(false)
    end
end

function HundredEquipSceneView:ReleaseCallBack()
	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end

	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end

	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function HundredEquipSceneView:ShowIndexCallBack()
	if self.obj then
        self.obj:SetActive(true)
    end
end

function HundredEquipSceneView:OnFlush(param_t, index)
    for k,v in pairs(param_t) do
		if k == "all" then
			self:OnFlushView()
        end
    end
end

function HundredEquipSceneView:OnFlushView()
	self:FlushRewardList()
	self:FlushRankInfo()
end

-- task面板信息
function HundredEquipSceneView:FlushTaskMsgInfo(cur_wave_num, monster_num)
	if not self.node_list.cur_num_desc or not self.node_list.cur_num_desc then
		return
	end

	self.node_list.cur_num_desc.text.text = string.format(Language.HundredEquip.SceneCurNum, cur_wave_num)
	self.node_list.monster_num_desc.text.text = string.format(Language.HundredEquip.SceneMonsterNum, monster_num)

	self:OnFlushView()
end

function HundredEquipSceneView:FlushRewardList()
	local reward_list =  HundredEquipWGData.Instance:GetFuBenRewardList()
	self.reward_list:SetDataList(reward_list)
end

function HundredEquipSceneView:FlushRankInfo()
	local rank_info_list, my_rank, my_wave = HundredEquipWGData.Instance:GetFuBenRankInfoList()
	self.rank_list:SetDataList(rank_info_list)

    self.node_list.my_rank.text.text = string.format(Language.HundredEquip.MyRank, my_rank or 0)
    self.node_list.my_damate.text.text = string.format(Language.HundredEquip.SceneMyWave, my_wave or 0)
end

-------------------------------------HundredEquipSceneRankItemCellRender---------------------------------
HundredEquipSceneRankItemCellRender = HundredEquipSceneRankItemCellRender or BaseClass(BaseRender)

function HundredEquipSceneRankItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
		return
	end

	self.node_list.num.text.text = self.index
	self.node_list.name.text.text = self.data.name
	self.node_list.damage.text.text = string.format(Language.HundredEquip.SceneRankWave, self.data.wave)
end