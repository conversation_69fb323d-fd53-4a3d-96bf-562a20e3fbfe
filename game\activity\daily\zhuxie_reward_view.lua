ZhuXieRewardView = ZhuXieRewardView or BaseClass(SafeBaseView)

function ZhuXieRewardView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/zhuxie_ui_prefab", "layout_reward_view")
end

function ZhuXieRewardView:ReleaseCallBack()
	if self.cell_list then
		self.cell_list = nil	
	end
end

function ZhuXieRewardView:LoadCallBack()
	self.cell_list = {}
	for i = 1, 3 do
		self.cell_list[i] = ItemCell.New()
		self.cell_list[i]:SetInstanceParent(self.node_list["ph_cell_"..i])
		local item = ActivityWGData.Instance:GetBossInfo(i)
		self.cell_list[i]:SetData(item)
	end
	self.node_list["Btn_nowgo"].button:AddClickListener(BindTool.Bind(self.OnClickGoBoss, self))
end

function ZhuXieRewardView:OnClickGoBoss()
	ActivityWGCtrl.Instance.zhuxie_task_view:OnClick()
	self:Close()
end
