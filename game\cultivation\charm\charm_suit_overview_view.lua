local CHARM_SUIT_BG =
{
    [1] = "a3_tds_2",
    [2] = "a3_tds_2",
    [3] = "a3_tds_3",
    [4] = "a3_tds_4",
    [5] = "a3_tds_5",
    [6] = "a3_tds_6",
    [7] = "a3_tds_7",
    [8] = "a3_tds_8",
}

CharmSuitOverViewView = CharmSuitOverViewView or BaseClass(SafeBaseView)
function CharmSuitOverViewView:__init()
    self:SetMaskBg(true,true)
    --self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -38), sizeDelta = Vector2(782, 494)})
	self:AddViewResource(0, "uis/view/cultivation_ui/charm_prefab", "layout_charm_suit_overview")
end

function CharmSuitOverViewView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.Charm.CharmSuitTitle
    self.charm_suit_attr_list1 = {}
    self.charm_suit_attr_list0 = {}
end

function CharmSuitOverViewView:ReleaseCallBack()
    if not IsEmptyTable(self.charm_suit_attr_list1) then
        for k,v in pairs(self.charm_suit_attr_list1) do
            v:DeleteMe()
        end
        self.charm_suit_attr_list1 = {}
    end

    if not IsEmptyTable(self.charm_suit_attr_list0) then
        for k,v in pairs(self.charm_suit_attr_list0) do
            v:DeleteMe()
        end

        self.charm_suit_attr_list0 = {}
    end
end

function CharmSuitOverViewView:OnFlush()
    for i = 0, 1 do
        local data_list = CultivationWGData.Instance:GetCharmSuitAttrDataList(i)  -- 0 阴  1阳
        local has_suit_attr = not IsEmptyTable(data_list)
        self.node_list["equip_not_attr" .. i]:CustomSetActive(not has_suit_attr)

        local charm_suit_list = i == CultivationWGData.CHARM_SUIT_TYPE.YANG and self.charm_suit_attr_list1 or self.charm_suit_attr_list0

        if has_suit_attr then
            for k, v in pairs(data_list) do

                if not charm_suit_list[k] then
                    local res_async_loader = AllocResAsyncLoader(self, "charm_suit_attr" .. i .. k)
                    res_async_loader:Load("uis/view/cultivation_ui/charm_prefab", "charm_suit_render", nil,
                    function(new_obj)
                        local obj = ResMgr:Instantiate(new_obj)
                        local obj_transform = obj.transform
                        obj_transform:SetParent(self.node_list["charm_suit_attr_list" .. i].transform, false)
                        local item_render = CharmSuitZongLanRender.New(obj)
                        item_render:SetData(v)
                        charm_suit_list[k] = item_render
                    end)
                end
            end
        end
    end

    local cap = CultivationWGData.Instance:GetCharmSuitCap()
    self.node_list.cap_value.text.text = cap
end

------------------------------CharmSuitZongLanRender----------------------------
CharmSuitZongLanRender = CharmSuitZongLanRender or BaseClass(BaseRender)
function CharmSuitZongLanRender:__init()
    self.attr_list = {}
    local attr_num = 5
    local attr_list_obj = self.node_list.attr_list

    for i = 1, attr_num do
        self.attr_list[i] = {}
        local key = "attr_" .. i
        self.attr_list[i].attr_parent = attr_list_obj:FindObj(key)
        self.attr_list[i].attr_name = attr_list_obj:FindObj(key .. "/attr_name")
        self.attr_list[i].attr_value = attr_list_obj:FindObj(key .. "/attr_value")
    end
end

function CharmSuitZongLanRender:__delete()
    self.attr_list = nil
end

function CharmSuitZongLanRender:OnFlush()
    if IsEmptyTable(self.data) then
        self.view:SetActive(false)
        return
    else
        self.view:SetActive(true)
    end

    local title_str = string.format(Language.Charm.CharmSuitAttrTitle, NumberToChinaNumber(self.data.need_order), self.data.need_num)
    self.node_list["charm_set_name"].text.text = title_str
    --self.node_list.need_num.text.text = string.format(Language.Charm.CharmSuitNeedNum, self.data.need_num)
    --self.node_list["suit_name"].text.text = string.format(Language.Charm.CharmSuitName, NumberToChinaNumber(self.data.need_order))
    local bundle, asset = ResPath.GetCultivationImg(CHARM_SUIT_BG[self.data.need_color])
    self.node_list["type_img"].image:LoadSprite(bundle, asset, function ()
        self.node_list["type_img"].image:SetNativeSize()
    end)

    for i = 1, 5 do
        local attr_id = self.data["attr_id" .. i]
        local attr_value = self.data["attr_value" .. i]
        
        if attr_id and attr_value and attr_id > 0 and attr_value > 0 then
            local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_id))
            self.attr_list[i].attr_name.text.text = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str, true)
            self.attr_list[i].attr_value.text.text = "+" .. AttributeMgr.PerAttrValue(attr_str, attr_value)
            self.attr_list[i].attr_parent:SetActive(true)
        else
            self.attr_list[i].attr_parent:SetActive(false)
        end
    end
end