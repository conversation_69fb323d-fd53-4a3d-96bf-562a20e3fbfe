HuanlezadanView = <PERSON><PERSON><PERSON>zadanView or BaseClass(SafeBaseView)
function HuanlezadanView:__init()
	self:SetMaskBg(false,false)
	self:AddViewResource(0, "uis/view/huanlezadan_ui_prefab", "layout_egg_smash")
end

function HuanlezadanView:__delete()
end

function HuanlezadanView:ReleaseCallBack()
	-- 关闭窗口5s后释放
	if CountDownManager.Instance:HasCountDown("activity_remaining_time") then
		CountDownManager.Instance:RemoveCountDown("activity_remaining_time")
	end
	Runner.Instance:RemoveRunObj(self)
	if self.show_cells then
		for k,v in pairs(self.show_cells) do
			v:DeleteMe()
		end
		self.show_cells = nil
	end

	if self.list_buy then
		for k,v in pairs(self.list_buy) do
			if v then v:DeleteMe() end
		end
		self.list_buy = nil
	end

	if self.buy_alert then
		self.buy_alert:DeleteMe()
		self.buy_alert = nil
	end
	if nil ~= self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
	if nil ~= self.record_list then
		self.record_list:DeleteMe()
		self.record_list = nil
	end
	self.has_click_btn_all = nil
	self.has_load_callbakc = nil
	self.need_flush = nil
end

function HuanlezadanView:OpenCallBack()
	--视图一打开就发送请求给服务器
	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.CROSS_PERSON_RANK_TYPE_SMASHED_EGG) then
		return
	end	
	HuanlezadanWGCtrl.Instance:SendReq(RA_CS_CROSS_RA_SMASHED_EGG_INFO.CS_CROSS_RA_SMASHED_EGG_INFO)
	RankWGCtrl.Instance:SendCrossGetRankListReq(CROSS_PERSON_RANK_TYPE.CROSS_PERSON_RANK_TYPE_SMASHED_EGG)
end

function HuanlezadanView:LoadCallBack()
	-- 设置砸蛋福利列表
	self:CreateRewardList()
	-- 设置物品展示列表
	self:CreateShowItemList()
	-- 设置物品购买列表
	self:CreateBuyItemList()
	-- 设置砸蛋记录
	self:CreateRecordList()

	-- -- 按钮
	XUI.AddClickEventListener(self.node_list.btn_buy_all, BindTool.Bind1(self.OnClickBtnBuyAll, self))
	XUI.AddClickEventListener(self.node_list.btn_tips, BindTool.Bind1(self.OnClickBtnTips, self))
	XUI.AddClickEventListener(self.node_list.btn_rank, BindTool.Bind1(self.OnClickOpenQuanFuView, self))
	XUI.AddClickEventListener(self.node_list.item_icon, BindTool.Bind1(self.OnClickItemIcon, self))
	self.node_list.btn_close.button:AddClickListener(BindTool.Bind(self.Close,self))
	self.has_load_callbakc = true
	if self.need_flush then
		self:Flush()
	end
	-- 每帧更新函数
	Runner.Instance:AddRunObj(self)
end

function HuanlezadanView:ShowIndexCallBack(index)
	-- 设置一个定时器记录活动剩余的时间
	if CountDownManager.Instance:HasCountDown("activity_remaining_time") then
		CountDownManager.Instance:RemoveCountDown("activity_remaining_time")
	end
	-- 根据ACTIVITY_TYPE来获取活动状态
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.CROSS_PERSON_RANK_TYPE_SMASHED_EGG) or {}
	-- 判断活动是否开启
	if act_info.status == ACTIVITY_STATUS.OPEN then
		-- 如果活动开启则倒计时
		local next_time = act_info.end_time or 0
		self:UpdateRemainTimeCallBack(TimeWGCtrl.Instance:GetServerTime(), next_time)
		CountDownManager.Instance:AddCountDown("activity_remaining_time", BindTool.Bind1(self.UpdateRemainTimeCallBack, self), BindTool.Bind1(self.CompleteCallBack, self), next_time, nil, 1)	
	else
		-- 否则活动剩余时间设置为0
		self:CompleteCallBack()
	end
end

function HuanlezadanView:CloseCallBack()
end

-- 收到协议刷新
function HuanlezadanView:OnFlush()
	if not self.has_load_callbakc then
		self.need_flush = true
		return
	end
	-- 刷新购买列表数据
	-- self.list_buy:SetDataList(HuanlezadanWGData.Instance:GetBuyItemTable())
	local data = HuanlezadanWGData.Instance:GetBuyItemTable()
	for i=1,#data do
		data[i].index = i
		if self.list_buy[i] then
			self.list_buy[i]:SetData(data[i])
		end
	end
	self.node_list.txt_remindEgg.text.text = (HuanlezadanWGData.Instance:GetAllGold())
	self.reward_list:SetDataList(HuanlezadanWGData.Instance:GetEggRewardItemTable() or {},3)
	self.record_list:SetDataList(HuanlezadanWGData.Instance:GetEggRecordItemTable() or {})
end

function HuanlezadanView:OnClickItemIcon()
	local other_cfg = HuanlezadanWGData.Instance:GetOtherConfig()
	if not other_cfg then return end 
	TipWGCtrl.Instance:OpenItem({item_id = other_cfg.happy_smashed_egg_need_item_id})
end


-- 剩余时间回调
function HuanlezadanView:UpdateRemainTimeCallBack(elapse_time, next_time)
	-- -- elapse_time等于TimeWGCtrl.Instance:GetServerTime()
	-- -- 下一次时间减去当前时间就是活动剩余时间
	local time = next_time - elapse_time
	if self.node_list.label_time then
		if time > 0 then
			local format_time = TimeUtil.FormatSecond(time)		
			self.node_list.label_time.text.text = (format_time)
		end
	end
end

-- 剩余时间结束回调
function HuanlezadanView:CompleteCallBack()
	if self.node_list.label_time then
		self.node_list.label_time.text.text = ("00:00:00")
	end
end

-- 点击问号回调
function HuanlezadanView:OnClickBtnTips()
	-- local str = string.format(Language.Huanlezadan.TipsText)
	RuleTip.Instance:SetContent(Language.Huanlezadan.TipsText, Language.Huanlezadan.TipsTitle)
	-- DescTip.Instance:Open()
end

-- 点击全部回调
function HuanlezadanView:OnClickBtnBuyAll()
	if self.has_click_btn_all then
		return
	end
	local cfg = ServerActivityWGData.Instance:GetCrossRandActivityConfig()
	local cfg_other = cfg.other
	local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg_other[1].happy_smashed_egg_need_item_id)
	local item_price = ShopWGData.GetItemPrice(cfg_other[1].happy_smashed_egg_gift)
	local need_num = HuanlezadanWGData.Instance:GetAllGold()
	local need_gold_num =  need_num * item_price.gold

	if RoleWGData.Instance:GetIsEnoughUseGold(need_gold_num) then
		-- 全部砸开，服务器没有协议，一个一个发给服务器
		local egg_num = HuanlezadanWGData.Instance:GetAllGold()
		if item_num < egg_num then
			ShopWGCtrl.Instance:SendShopBuy(cfg_other[1].happy_smashed_egg_gift, egg_num - item_num, 1, 0)
		end
		-- for i = 0,7 do
		HuanlezadanWGCtrl.Instance:SendReq(RA_CS_CROSS_RA_SMASHED_EGG_INFO.CS_CROSS_RA_SMASHED_ALL_EGG)
		-- end
		self.has_click_btn_all = true
		GlobalTimerQuest:AddDelayTimer(function()
			self.has_click_btn_all = nil
		end,0.5)
	else
		UiInstanceMgr.Instance:ShowChongZhiView()
	end
end
function HuanlezadanView:OnClickOpenQuanFuView()
	HuanlezadanWGCtrl.Instance:OpenQuanFuView()
end


-- 设置物品展示列表
function HuanlezadanView:CreateShowItemList()
	
	-- 获取配置表要展示的物品表数据
	local showItemTable = HuanlezadanWGData.Instance:GetShowItemTable()

	-- 创建所有展示格子
	self.show_cells = {}
	for i = 1, #showItemTable do
		self.show_cells[i] = ItemCell.New(self.node_list.reward)
		self.show_cells[i]:SetData(showItemTable[i].reward_item)
	end
end

-- 设置物品购买列表
function HuanlezadanView:CreateBuyItemList()
	self.list_buy = {}
	for i=1,8 do
		self.list_buy[i] = HuanlezadanItemRender.New(self.node_list["ph_list_buy_item" .. i])
	end
end

-- 在每帧更新函数让展示格子向右移
function HuanlezadanView:Update(now_time, elapse_time)
	local showItemTable = HuanlezadanWGData.Instance:GetShowItemTable()
	for i=1, #showItemTable do
		local pos_x = self.show_cells[i].root_node.rect.anchoredPosition.x
		if pos_x <= (#showItemTable - 1) * 92 + 46 then
			pos_x = pos_x + 1
		else
			pos_x = -46
		end
		self.show_cells[i].root_node.rect.anchoredPosition = Vector2(pos_x,-50)
	end
end

-- 获取购买选择框
function HuanlezadanView:getBuyAlert()
	if self.buy_alert == nil then
		self.buy_alert = Alert.New()
		self.buy_alert:SetShowCheckBox(true)
	end
	return self.buy_alert
end

function HuanlezadanView:CreateRewardList()
	self.reward_list = AsyncListView.New(RewardListItemRender,self.node_list.ph_list_reward)
end

function HuanlezadanView:CreateRecordList()
	self.record_list = AsyncListView.New(RecordListItemRender,self.node_list.ph_list_record)
end


--------------------------------------------
--HuanlezadanItemRender
--------------------------------------------

HuanlezadanItemRender = HuanlezadanItemRender or BaseClass(BaseRender)
function HuanlezadanItemRender:__init()
end

function HuanlezadanItemRender:__delete()
	if self.buy_cell then
		self.buy_cell:DeleteMe()
		self.buy_cell = nil
	end
end

function HuanlezadanItemRender:LoadCallBack()
	self.cache = nil
	self.buy_cell = ItemCell.New(self.node_list.ph_cell1)
	self.buy_cell.root_node.rect.localScale = Vector3(0.7, 0.7, 0.7)
	XUI.AddClickEventListener(self.node_list.img_egg, BindTool.Bind1(self.OnClickEgg, self))
end

function HuanlezadanItemRender:OnFlush()
	if nil == self.data then return end
	if self.cache ~= self.data.isCanSee then
		self.cache = self.data.isCanSee
		local str = self.cache and "egg_2" or "egg_1"
		local bundle,asset = ResPath.GetHuanlezadan(str)
		self.node_list.img_egg.image:LoadSprite(bundle,asset,function()
			self.node_list.img_egg.image:SetNativeSize()
		end)
		if self.cache then
			self.buy_cell:SetData(self.data)
		end
		self.buy_cell:SetActive(self.cache)
	end
end


-- 点击回调
function HuanlezadanItemRender:OnClickEgg()
	if self.data.isCanSee then return end
	local alert = HuanlezadanWGCtrl.Instance.view:getBuyAlert()
	local num = HuanlezadanWGData.Instance:GetEggNeedItemNum()

	alert:SetOkFunc(BindTool.Bind1(self.SendReq, self))
	alert:SetLableString(string.format(Language.Huanlezadan.OnceTips, HuanlezadanWGData.Instance:GetOneGold()))
	if num <= 0 then 
		alert:Open()
	else
		self:SendReq()
	end
end

function HuanlezadanItemRender:SendReq()
	local cfg = ServerActivityWGData.Instance:GetCrossRandActivityConfig()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg.other[1].happy_smashed_egg_need_item_id)
	if item_num <= 0 then
		ShopWGCtrl.Instance:SendShopBuy(cfg.other[1].happy_smashed_egg_gift, 1, 1, 0)
	end
	HuanlezadanWGCtrl.Instance:SendReq(RA_CS_CROSS_RA_SMASHED_EGG_INFO.CS_CROSS_RA_SMASHED_EGG, self.data.index - 1)
end

function HuanlezadanItemRender:CreateSelectEffect()
end



--------------------------------------------
--RewardListItemRender
--------------------------------------------
RewardListItemRender = RewardListItemRender or BaseClass(BaseRender)
function RewardListItemRender:__init()
end

function RewardListItemRender:__delete()
	if self.cell_list then
		for k,v in pairs(self.cell_list) do
			v:DeleteMe()
		end
		self.cell_list = nil
	end
	self.cache = nil
	self.btn_cache = nil
	self.btn_state = nil
end

function RewardListItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_receive"], BindTool.Bind1(self.OnClickBtnReceive, self))
	self.cache = {nil,nil,nil}
	self.btn_cache = nil
	self.btn_state = nil
	self.cell_list = {}
	for i = 1, 3 do
		self.cell_list[i] = ItemCell.New(self.node_list["ph_cell_" .. i])
		self.cell_list[i].root_node.rect.localScale = Vector3(0.7, 0.7, 0.7)
	end
end

function RewardListItemRender:OnFlush()
	if nil == self.data then return end
	local state = {true,true,true}
	for i = #self.data.reward_item + 2 , 3 do
		state[i] = false
	end
	for k,v in pairs(state) do
		if self.cache[k] ~= v then
			self.cell_list[k]:SetActive(v)
			self.cache[k] = v
		end
	end

	for k,v in pairs(self.data.reward_item) do
		self.cell_list[k + 1]:SetData(v)
	end

	local times = HuanlezadanWGData.Instance:GetSCCrossRASmashedEggInfo().cur_total_times
	local color = COLOR3B.GREEN
	-- if times < self.data.times then
	-- 	color = COLOR3B.RED
	-- end7cffb7

	local index_can_fatch = HuanlezadanWGData.Instance:CheckIsFetchedByID(self.index)
	local times_can_fatch = times >= self.data.times

	if self.btn_state ~= index_can_fatch then
		self.node_list.btn_receive:SetActive(not index_can_fatch)
		self.node_list.img_receive:SetActive(index_can_fatch)
		self.btn_state = index_can_fatch
	end

	if self.btn_cache ~= times_can_fatch then
		if not index_can_fatch then
			XUI.SetButtonEnabled(self.node_list.btn_receive,times_can_fatch)
		end
	end

	self.node_list.rich_reward_days.text.text = string.format(Language.Huanlezadan.RewardDays, color, times, self.data.times)
end

function RewardListItemRender:OnClickBtnReceive()
	HuanlezadanWGCtrl.Instance:SendReq(RA_CS_CROSS_RA_SMASHED_EGG_INFO.CS_CROSS_RA_SMASHED_EGG_FETCH_REWARD, self.data.seq)
end

function RewardListItemRender:CreateSelectEffect()
end


--------------------------------------------
--RecordListItemRender
--------------------------------------------
RecordListItemRender = RecordListItemRender or BaseClass(BaseRender)
function RecordListItemRender:__init()
end

function RecordListItemRender:__delete()

end

function RecordListItemRender:LoadCallBack()

end

function RecordListItemRender:OnFlush()
	if nil == self.data then return end
	self.node_list.text_record_1.text.text = string.format(Language.Huanlezadan.TextRecord, self.data.user_name)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.reward_item_id)
	if item_cfg then

		self.node_list.text_record_2.text.text = ToColorStr(item_cfg.name,ITEM_COLOR[item_cfg.color])
		-- self.node_list.text_record_2:setColor(ITEM_COLOR[item_cfg.color])		
	end
	-- self.node_list.text_record_1:setHorizontalAlignment(RichHAlignment.HA_LEFT)
end

function RecordListItemRender:CreateSelectEffect()
end