TianshenShenshiSuccinctTip = TianshenShenshiSuccinctTip or BaseClass(SafeBaseView)

function TianshenShenshiSuccinctTip:__init()
	self.view_name = "TianshenShenshiSuccinctTip"
    self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",{sizeDelta = Vector2(1080, 612)})
	self:AddViewResource(0, "uis/view/tianshen_prefab", "layout_tianshen_shenshi_succinct_tip")
end

function TianshenShenshiSuccinctTip:ReleaseCallBack()
	if self.attr_list and #self.attr_list > 0 then
		for _, attr_cell in ipairs(self.attr_list) do
			attr_cell:DeleteMe()
			attr_cell = nil
		end

		self.attr_list = nil
	end

	self.tianshen_index = nil
end

function TianshenShenshiSuccinctTip:LoadCallBack()
    -- 基础属性
    if self.attr_list == nil then
        self.attr_list = {}
        for i = 1, 8 do
            local attr_obj = self.node_list.attr_list:FindObj(string.format("attr_%d", i))
            if attr_obj then
                local cell = TianshenShenshiSuccinctTipItem.New(attr_obj)
                cell:SetIndex(i)
                self.attr_list[i] = cell
            end
        end
    end

	self.node_list.title_view_name.text.text = Language.TianShen.TSShenShiSuccinctTips6
end

-- 设置属性值
function TianshenShenshiSuccinctTip:SetData(tianshen_index)
	self.tianshen_index = tianshen_index
    self:Open()
end

function TianshenShenshiSuccinctTip:OnFlush(param_t)
    self:FlushRecordList()
end

function TianshenShenshiSuccinctTip:FlushRecordList()
	if not self.tianshen_index then
		return
	end

	local list = TianShenWGData.Instance:GetTianShenShenShiRefineShowList(self.tianshen_index)
	for i, attr_cell in ipairs(self.attr_list) do
		attr_cell:SetVisible(list[i] ~= nil)

		if list[i] then
			attr_cell:SetData(list[i])
		end
	end

	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.attr_scroll.rect)
end

TianshenShenshiSuccinctTipItem = TianshenShenshiSuccinctTipItem or BaseClass(BaseRender)
function TianshenShenshiSuccinctTipItem:OnFlush()
	if not self.data then
		return
	end

	local index = nil
	local level = nil
	local max_value = 0
	local min_value = 0

	for i, aim_data in ipairs(self.data) do
		if aim_data then
			index = aim_data.index
			level = aim_data.need_level
			local base_data, baodi_data = TianShenWGData.Instance:GetTianShenShenShiRefineInterval(aim_data)
			local baodi_max_value = (baodi_data.max_value or 0) / 100
			max_value = max_value + baodi_max_value

			local base_min_value = (base_data.min_value or 0) / 100
			min_value = min_value + base_min_value
		end
	end

	local js_data = TianShenWGData.Instance:GetShenShiJinShengDataByLevel(index, level)
	local need_name = js_data and js_data.lv_name or ""
	local attr_num = #self.data
	local color = ITEM_COLOR[level]
	local lv_str = ToColorStr(Language.TianShen.TSShenShiQulityLv[level - 1], color)
	local normal_str = string.format("a3_ts_succinct_0%d", level)
	self.node_list.title_image.image:LoadSprite(ResPath.GetF2TianShenImage(normal_str))
	self.node_list.title_name.text.text = ToColorStr(need_name, color)
	self.node_list.attr_condition.text.text = string.format(Language.TianShen.TSShenShiSuccinctTips, lv_str)

	local num_str = string.format(Language.TianShen.TSShenShiSuccinctTips2, attr_num)
	local value_str =  string.format(Language.TianShen.TSShenShiSuccinctTips3, min_value, max_value)
	local final_num_str = string.format("%s%s", num_str, value_str)
	self.node_list.attr_value.text.text = final_num_str

	local attr_list = TianShenWGData.Instance:GetColorLimitAttrTab(level)
	local attr_list_new = AttributeMgr.GetAttributteByClass(attr_list)
	self:OutAttrListToStrByClass(attr_list_new)
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.view.rect)
end

-- 属性列表 输出成拼接字符
function TianshenShenshiSuccinctTipItem:OutAttrListToStrByClass(attr_list)
	local attr_desc = ""
	if IsEmptyTable(attr_list) then
		return attr_desc
	end

	local new_attr_list = EquipWGData.GetSortAttrListHaveAddByCfg(attr_list)
	for i = 1, 6 do
		local node_root = self.node_list[string.format("attr_%d", i)]
		if node_root then
			node_root:CustomSetActive(new_attr_list[i] ~= nil)
		end

		if new_attr_list[i] ~= nil then
			local node_attr_name = self.node_list[string.format("attr_name%d", i)]
			local node_attr_value = self.node_list[string.format("attr_value%d", i)]

			if node_attr_name then
				node_attr_name.text.text = Language.Common.AttrNameList[new_attr_list[i].attr_str]
			end
	
			if node_attr_value then
				local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(new_attr_list[i].attr_str)

				local value_str = ""
				if is_per and new_attr_list[i].attr_value > 0 then
					value_str = (math.floor(new_attr_list[i].attr_value) / 100) .. "%"
				else
					value_str = math.floor(new_attr_list[i].attr_value)
				end

				node_attr_value.text.text = value_str
			end
		end
	end
end