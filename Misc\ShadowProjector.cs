﻿using UnityEngine;
using UnityEngine.Rendering;

public class ShadowProjector : MonoBehaviour 
{
    [SerializeField]
    private Transform target;
    [SerializeField]
    public Shader shadowReplaceShader;
    [SerializeField]
    private float offset = 0;

    private Camera lightCamera = null;
    private RenderTexture shadowRt;
    private bool isReplaceShadow = true;
    private Vector3 lightForward = Vector3.zero;
    private string[] cullingMasks;

    public void SetTarget(Transform target)
    {
        this.target = target;
    }

    public void SetIsReplaceShadow(bool isReplaceShadow)
    {
        this.isReplaceShadow = isReplaceShadow;
    }

    public void SetLightForward(Vector3 lightForward)
    {
        this.lightForward = lightForward;
        this.UpdateForward();
    }

    public void SetCullingMasks(string[] cullingMasks)
    {
        this.cullingMasks = cullingMasks;
        this.UpdateCullingMask();
    }

    private void Start ()
    {
        Projector projector = GetComponent<Projector>();
        if(lightCamera == null)
        {
            shadowRt = RenderTexture.GetTemporary(1024, 1024, 0, RenderTextureFormat.ARGB32);
            shadowRt.name = "ShadowProjector";
            shadowRt.filterMode = FilterMode.Bilinear;
            shadowRt.autoGenerateMips = false;

            lightCamera = gameObject.AddComponent<Camera>();
            lightCamera.orthographic = true;
            lightCamera.useOcclusionCulling = false;
            lightCamera.clearFlags = CameraClearFlags.SolidColor;
            lightCamera.backgroundColor = new Color(0,0,0,0);
            lightCamera.farClipPlane = projector.farClipPlane;
            lightCamera.nearClipPlane = projector.nearClipPlane;
            lightCamera.fieldOfView = projector.fieldOfView;
            lightCamera.aspect = projector.aspectRatio;
            lightCamera.orthographicSize = projector.orthographicSize;
            lightCamera.depthTextureMode = DepthTextureMode.None;
            lightCamera.renderingPath = RenderingPath.Forward;
            lightCamera.targetTexture = shadowRt;
            lightCamera.allowHDR = false;
            lightCamera.allowMSAA = false;
            this.UpdateCullingMask();
            if (isReplaceShadow)
            {
                lightCamera.SetReplacementShader(shadowReplaceShader, "RenderType");
            }

            projector.material.SetTexture("_ShadowTex", shadowRt);
        }

        this.UpdateForward();
    }

    private void OnDestroy()
    {
        if (null != shadowRt)
        {
            RenderTexture.ReleaseTemporary(shadowRt);
            shadowRt = null;
        }

        if (null != lightCamera)
        {
            GameObject.Destroy(lightCamera);
            lightCamera = null;
        }
    }

    private void LateUpdate()
    {
        if (null != this.target)
        {
            Vector3 z = transform.forward;
            this.transform.LookAt(transform.position + z, target.up);
            this.transform.position = target.position + this.transform.forward * offset;
        }
    }

    private void UpdateCullingMask()
    {
        if (null == lightCamera) return;

        if (null == cullingMasks)
        {
            cullingMasks = new string[] { "CastShadow" };
        }

        lightCamera.cullingMask = LayerMask.GetMask(cullingMasks);
    }

    private void UpdateForward()
    {
        if (lightForward == Vector3.zero)
        {
            GameObject lightGameObj = GameObject.Find("Main/Weather/DayNightCycle/SunRoot/Sun");
            lightGameObj = lightGameObj == null ? GameObject.Find("Weather/DayNightCycle/SunRoot/Sun") : lightGameObj;
            if (lightGameObj == null)
            {
                //有些场景默认隐藏hero light，要用transform.Find
                GameObject mainObj = GameObject.Find("Main");
                lightGameObj = mainObj.transform.Find("Hero light").gameObject;
            }

            if (null != lightGameObj)
            {
                lightForward = lightGameObj.transform.forward;
            }
            else
            {
                Debug.LogError("not found Hero light");
            }
        }

        this.transform.forward = lightForward;
    }
}
