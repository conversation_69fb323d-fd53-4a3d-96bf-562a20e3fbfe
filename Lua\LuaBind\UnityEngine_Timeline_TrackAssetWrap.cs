﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_Timeline_TrackAssetWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.Timeline.TrackAsset), typeof(UnityEngine.Playables.PlayableAsset));
		<PERSON><PERSON>RegFunction("GetClips", GetClips);
		<PERSON><PERSON>RegFunction("GetChildTracks", GetChildTracks);
		L.RegFunction("CreateCurves", CreateCurves);
		L.RegFunction("CreateTrackMixer", CreateTrackMixer);
		L.RegFunction("CreatePlayable", CreatePlayable);
		<PERSON><PERSON>RegFunction("CreateDefaultClip", CreateDefaultClip);
		<PERSON><PERSON>Function("DeleteClip", DeleteClip);
		L.RegFunction("CreateMarker", CreateMarker);
		<PERSON><PERSON>Function("DeleteMarker", DeleteMarker);
		<PERSON><PERSON>unction("GetMarkers", GetMarkers);
		<PERSON><PERSON>un<PERSON>("GetMarkerCount", GetMarkerCount);
		<PERSON><PERSON>unction("GetMarker", GetMarker);
		<PERSON><PERSON>RegFunction("GatherProperties", GatherProperties);
		L.RegFunction("CanCreateTrackMixer", CanCreateTrackMixer);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("start", get_start, null);
		L.RegVar("end", get_end, null);
		L.RegVar("duration", get_duration, null);
		L.RegVar("muted", get_muted, set_muted);
		L.RegVar("mutedInHierarchy", get_mutedInHierarchy, null);
		L.RegVar("timelineAsset", get_timelineAsset, null);
		L.RegVar("parent", get_parent, null);
		L.RegVar("isEmpty", get_isEmpty, null);
		L.RegVar("hasClips", get_hasClips, null);
		L.RegVar("hasCurves", get_hasCurves, null);
		L.RegVar("isSubTrack", get_isSubTrack, null);
		L.RegVar("outputs", get_outputs, null);
		L.RegVar("curves", get_curves, null);
		L.RegVar("locked", get_locked, set_locked);
		L.RegVar("lockedInHierarchy", get_lockedInHierarchy, null);
		L.RegVar("supportsNotifications", get_supportsNotifications, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetClips(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)ToLua.CheckObject<UnityEngine.Timeline.TrackAsset>(L, 1);
			System.Collections.Generic.IEnumerable<UnityEngine.Timeline.TimelineClip> o = obj.GetClips();
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetChildTracks(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)ToLua.CheckObject<UnityEngine.Timeline.TrackAsset>(L, 1);
			System.Collections.Generic.IEnumerable<UnityEngine.Timeline.TrackAsset> o = obj.GetChildTracks();
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CreateCurves(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)ToLua.CheckObject<UnityEngine.Timeline.TrackAsset>(L, 1);
			string arg0 = ToLua.CheckString(L, 2);
			obj.CreateCurves(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CreateTrackMixer(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)ToLua.CheckObject<UnityEngine.Timeline.TrackAsset>(L, 1);
			UnityEngine.Playables.PlayableGraph arg0 = StackTraits<UnityEngine.Playables.PlayableGraph>.Check(L, 2);
			UnityEngine.GameObject arg1 = (UnityEngine.GameObject)ToLua.CheckObject(L, 3, typeof(UnityEngine.GameObject));
			int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
			UnityEngine.Playables.Playable o = obj.CreateTrackMixer(arg0, arg1, arg2);
			ToLua.PushValue(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CreatePlayable(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)ToLua.CheckObject<UnityEngine.Timeline.TrackAsset>(L, 1);
			UnityEngine.Playables.PlayableGraph arg0 = StackTraits<UnityEngine.Playables.PlayableGraph>.Check(L, 2);
			UnityEngine.GameObject arg1 = (UnityEngine.GameObject)ToLua.CheckObject(L, 3, typeof(UnityEngine.GameObject));
			UnityEngine.Playables.Playable o = obj.CreatePlayable(arg0, arg1);
			ToLua.PushValue(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CreateDefaultClip(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)ToLua.CheckObject<UnityEngine.Timeline.TrackAsset>(L, 1);
			UnityEngine.Timeline.TimelineClip o = obj.CreateDefaultClip();
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DeleteClip(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)ToLua.CheckObject<UnityEngine.Timeline.TrackAsset>(L, 1);
			UnityEngine.Timeline.TimelineClip arg0 = (UnityEngine.Timeline.TimelineClip)ToLua.CheckObject<UnityEngine.Timeline.TimelineClip>(L, 2);
			bool o = obj.DeleteClip(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CreateMarker(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)ToLua.CheckObject<UnityEngine.Timeline.TrackAsset>(L, 1);
			System.Type arg0 = ToLua.CheckMonoType(L, 2);
			double arg1 = (double)LuaDLL.luaL_checknumber(L, 3);
			UnityEngine.Timeline.IMarker o = obj.CreateMarker(arg0, arg1);
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DeleteMarker(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)ToLua.CheckObject<UnityEngine.Timeline.TrackAsset>(L, 1);
			UnityEngine.Timeline.IMarker arg0 = (UnityEngine.Timeline.IMarker)ToLua.CheckObject<UnityEngine.Timeline.IMarker>(L, 2);
			bool o = obj.DeleteMarker(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetMarkers(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)ToLua.CheckObject<UnityEngine.Timeline.TrackAsset>(L, 1);
			System.Collections.Generic.IEnumerable<UnityEngine.Timeline.IMarker> o = obj.GetMarkers();
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetMarkerCount(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)ToLua.CheckObject<UnityEngine.Timeline.TrackAsset>(L, 1);
			int o = obj.GetMarkerCount();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetMarker(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)ToLua.CheckObject<UnityEngine.Timeline.TrackAsset>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Timeline.IMarker o = obj.GetMarker(arg0);
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GatherProperties(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)ToLua.CheckObject<UnityEngine.Timeline.TrackAsset>(L, 1);
			UnityEngine.Playables.PlayableDirector arg0 = (UnityEngine.Playables.PlayableDirector)ToLua.CheckObject<UnityEngine.Playables.PlayableDirector>(L, 2);
			UnityEngine.Timeline.IPropertyCollector arg1 = (UnityEngine.Timeline.IPropertyCollector)ToLua.CheckObject<UnityEngine.Timeline.IPropertyCollector>(L, 3);
			obj.GatherProperties(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CanCreateTrackMixer(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)ToLua.CheckObject<UnityEngine.Timeline.TrackAsset>(L, 1);
			bool o = obj.CanCreateTrackMixer();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_start(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)o;
			double ret = obj.start;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index start on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_end(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)o;
			double ret = obj.end;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index end on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_duration(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)o;
			double ret = obj.duration;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index duration on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_muted(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)o;
			bool ret = obj.muted;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index muted on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_mutedInHierarchy(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)o;
			bool ret = obj.mutedInHierarchy;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index mutedInHierarchy on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_timelineAsset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)o;
			UnityEngine.Timeline.TimelineAsset ret = obj.timelineAsset;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index timelineAsset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_parent(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)o;
			UnityEngine.Playables.PlayableAsset ret = obj.parent;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index parent on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isEmpty(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)o;
			bool ret = obj.isEmpty;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isEmpty on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_hasClips(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)o;
			bool ret = obj.hasClips;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index hasClips on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_hasCurves(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)o;
			bool ret = obj.hasCurves;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index hasCurves on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isSubTrack(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)o;
			bool ret = obj.isSubTrack;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isSubTrack on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_outputs(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)o;
			System.Collections.Generic.IEnumerable<UnityEngine.Playables.PlayableBinding> ret = obj.outputs;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index outputs on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_curves(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)o;
			UnityEngine.AnimationClip ret = obj.curves;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index curves on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_locked(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)o;
			bool ret = obj.locked;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index locked on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_lockedInHierarchy(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)o;
			bool ret = obj.lockedInHierarchy;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index lockedInHierarchy on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_supportsNotifications(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)o;
			bool ret = obj.supportsNotifications;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index supportsNotifications on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_muted(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.muted = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index muted on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_locked(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.TrackAsset obj = (UnityEngine.Timeline.TrackAsset)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.locked = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index locked on a nil value");
		}
	}
}

