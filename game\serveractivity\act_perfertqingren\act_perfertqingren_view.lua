--完美情人

DANMU_TOTAL_LENGTH = 1500

ActivePerfertQingrenView = ActivePerfertQingrenView or BaseClass(SafeBaseView)

function ActivePerfertQingrenView:__init( )
	self:AddViewResource(0, "uis/view/act_tab_view_prefab", "layout_perfert_qingren_view")
	self.order = 3
	self.item_data_event = BindTool.Bind(self.ItemDataChangeCallback, self)
end

function ActivePerfertQingrenView:ReleaseCallBack()
	if self.task_list then
		self.task_list:DeleteMe()
		self.task_list = nil
	end

	self.items = nil
	self.copy_all_list = nil
	if self.cell_list then
		for k,v in pairs(self.cell_list) do
			v:DeleteMe()
		end
		self.cell_list = nil
	end

	if self.first_reward_list_view then
		self.first_reward_list_view:DeleteMe()
		self.first_reward_list_view = nil
	end

	if self.bind_day_pass then
		GlobalEventSystem:UnBind(self.bind_day_pass)
		self.bind_day_pass = nil
	end

	if self.all_reward_list_view then
		self.all_reward_list_view:DeleteMe()
		self.all_reward_list_view = nil
	end

	if self.qingren_pet_cell then
		self.qingren_pet_cell:DeleteMe()
		self.qingren_pet_cell = nil
	end

	if self.display_model then
		self.display_model:DeleteMe()
		self.display_model = nil
	end

	if self.display_model1 then
		self.display_model1:DeleteMe()
		self.display_model1 = nil
	end

	if self.tianyan_miyu_rank_list then
		self.tianyan_miyu_rank_list:DeleteMe()
		self.tianyan_miyu_rank_list = nil
	end

	if CountDownManager.Instance:HasCountDown("quancheng_relian_end_countdown") then
		CountDownManager.Instance:RemoveCountDown("quancheng_relian_end_countdown")
	end

	self.now_show_list = nil
	self.prepare_show_list = nil
	self.cache_show_list = nil
	self.anim_playing = nil
	self.quancheng_task_list = nil
	self.panel_type = nil
	self.is_boy_model = nil
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)

end
function ActivePerfertQingrenView:CloseCallBack()
	self:ResertDanMuTweenPos()
end

function ActivePerfertQingrenView:ItemDataChangeCallback()
	local pet_base_cfg = ActivePerfertQingrenWGData.Instance:GetQuanChengReLianCfgOther()
	local num_nvwa_suipian = ItemWGData.Instance:GetItemNumInBagById(pet_base_cfg.loving_city_convert_image_stuff_id)

	if self.node_list and self.node_list.qingren_pet_cell_process then
		self.node_list.qingren_pet_cell_process.text.text = string.format(Language.Activity.ActivityProcess,num_nvwa_suipian,pet_base_cfg.loving_city_convert_image_stuff_count)
	end

	if self.node_list and self.node_list.quancheng_wawa_get_point then
		self.node_list.quancheng_wawa_get_point:SetActive(num_nvwa_suipian >= pet_base_cfg.loving_city_convert_image_stuff_count)
	end

	GlobalEventSystem:Fire(GLOBELEVENT_OPENSERVER_ACTIVITY.UPDATE_BTN_LIST)
end

function ActivePerfertQingrenView:LoadCallBack()
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)

	self.quancheng_task_list = AsyncListView.New(QuanChengReLianTaskRender,self.node_list["quancheng_ph_task_list"])
	--self.slider
	if nil == self.display_model then
		self.display_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["ph_display_pet"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}

		self.display_model:SetRenderTexUI3DModel(display_data)
	end

	if nil == self.display_model1 then
		self.display_model1 = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["ph_display_biaobai"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}

		self.display_model1:SetRenderTexUI3DModel(display_data)
	
	end

	local pet_base_cfg = ActivePerfertQingrenWGData.Instance:GetQuanChengReLianCfgOther()
	if self.display_model then
		local pet_res_id = pet_base_cfg.loving_city_resource_id or 0
		local boy_cfg = MarryWGData.Instance:GetBabyCfgByImageId(pet_base_cfg.loving_city_resource_id or 0)
		local girl_cfg = MarryWGData.Instance:GetBabyCfgByImageId(pet_base_cfg.loving_city_resource_id2 or 0)
		local boy_baby_active = MarryWGData.Instance:GetBabyActiveByTypeId(boy_cfg.type or -1,boy_cfg.id or -1)
		local girl_baby_active = MarryWGData.Instance:GetBabyActiveByTypeId(girl_cfg.type or -1,girl_cfg.id or -1)
		if boy_baby_active then
			pet_res_id = pet_base_cfg.loving_city_resource_id2 or 0
			local bundle, asset =  ResPath.GetHaiZiModel(pet_res_id)
			self.display_model:SetMainAsset(bundle, asset)
			self.is_boy_model = false
			self.node_list.baby_active:SetActive(girl_baby_active)
		else
			local bundle, asset =  ResPath.GetHaiZiModel(pet_res_id)
			self.display_model:SetMainAsset(bundle, asset)
			self.is_boy_model = true
			self.node_list.baby_active:SetActive(boy_baby_active)
		end
	end

	if self.display_model1 then
		local pet_res_id = pet_base_cfg.love_profess_resource_id or 0
		local bundle, asset =  ResPath.GetHaiZiModel(pet_res_id)
		self.display_model1:SetMainAsset(bundle, asset)
	end

	self.tianyan_miyu_rank_list = AsyncListView.New(TianYanMiYuRankRender,self.node_list.miyu_rank_list)
	self.qingren_pet_cell = ItemCell.New(self.node_list["qingren_pet_cell"])
	XUI.AddClickEventListener(self.node_list["btn_lingqu_xianwa"], BindTool.Bind(self.GetXianWaBtn, self))
	XUI.AddClickEventListener(self.node_list["reward_btn_citylove"], BindTool.Bind(self.OpenCityLoveNumReawrd, self))
	XUI.AddClickEventListener(self.node_list["rank_biaobai_btn"], BindTool.Bind(self.LoveBiaoBaiRankSecondView, self))
	XUI.AddClickEventListener(self.node_list["my_biaobai_btn"], BindTool.Bind(self.SendBiaoBtn, self,1))
	XUI.AddClickEventListener(self.node_list["other_biaobai_btn"], BindTool.Bind(self.SendBiaoBtn, self,2))
	XUI.AddClickEventListener(self.node_list["go_marry_btn"], BindTool.Bind(self.SendBiaoBtn, self,3))
	XUI.AddClickEventListener(self.node_list["biaobai_btn_1"], BindTool.Bind(self.TianYanMiYuBtn, self,1))
	XUI.AddClickEventListener(self.node_list["biaobai_btn_2"], BindTool.Bind(self.TianYanMiYuBtn, self,2))
	XUI.AddClickEventListener(self.node_list["btn_activity_states"], BindTool.Bind(self.ShowTipsInfo, self))
	XUI.AddClickEventListener(self.node_list["btn_lingqu_xianwa_change"], BindTool.Bind(self.ChanegeModelShow, self))

	for i=1,3 do
		XUI.AddClickEventListener(self.node_list["love_btn_"..i], BindTool.Bind(self.ActivityTypePanel, self,i))
	end

	self.node_list.activity_desc_1.text.text = Language.Activity.PerfertLoverDes_1
	self.node_list.activity_desc_2.text.text = Language.Activity.PerfertLoverDes_2
	self.node_list.activity_desc_3.text.text = Language.Activity.PerfertLoverDes_3
	self:ActivityTypePanel(1)
	self.cache_show_list = {}
	self.prepare_show_list = {}
	self.now_show_list = {}
	local bundle = "barrage_group_danmu_1"
	local num = 6

	for i=1,5 do
		self.cache_show_list[i] = {}
		self.prepare_show_list[i] = {}
		self.now_show_list[i] = {}
		if i == 1 or i == 2 then
			bundle = "barrage_group_danmu_1"
		elseif i == 3 or i == 4 then
			bundle = "barrage_group_danmu_2"
		else
			bundle = "barrage_group_danmu_3"
		end

		local res_async_loader = AllocResAsyncLoader(self, bundle .. i)
		res_async_loader:Load("uis/view/act_tab_view_prefab", bundle, nil,
		function(new_obj)
			for m=1, num do
				local obj = ResMgr:Instantiate(new_obj)
				local obj_transform = obj.transform
				obj_transform:SetParent(self.node_list["barrage_group_pos_"..i].transform, false)
				local item_render = nil
				item_render = TianYanMiYuDanMuRender.New(obj)
				table.insert(self.cache_show_list[i],item_render)
			end
		end)
	end
end

function ActivePerfertQingrenView:ChanegeModelShow()
	if self.display_model then
		local pet_base_cfg = ActivePerfertQingrenWGData.Instance:GetQuanChengReLianCfgOther()
		local pet_res_id = 0
		if self.is_boy_model then
			pet_res_id = pet_base_cfg.loving_city_resource_id2 or 0
			local baby_cfg = MarryWGData.Instance:GetBabyCfgByImageId(pet_res_id)
			local bundle, asset =  ResPath.GetHaiZiModel(pet_res_id)
			self.display_model:SetMainAsset(bundle, asset)
			self.is_boy_model = false
		else
			pet_res_id = pet_base_cfg.loving_city_resource_id or 0
			local bundle, asset =  ResPath.GetHaiZiModel(pet_res_id)
			self.display_model:SetMainAsset(bundle, asset)
			 self.is_boy_model = true
		end
		local baby_cfg = MarryWGData.Instance:GetBabyCfgByImageId(pet_res_id)
		local baby_active = MarryWGData.Instance:GetBabyActiveByTypeId(baby_cfg.type or -1,baby_cfg.id or -1)
		self.node_list.baby_active:SetActive(baby_active)
	end
end

function ActivePerfertQingrenView:SetQuanChengLoveBaseInfo()
	local pet_base_cfg = ActivePerfertQingrenWGData.Instance:GetQuanChengReLianCfgOther()
	--local data = pet_base_cfg.loving_city_image_item
	local data1 = {}
	data1.item_id = pet_base_cfg.loving_city_convert_image_stuff_id
	self.qingren_pet_cell:SetData(data1)
	local num_nvwa_suipian = ItemWGData.Instance:GetItemNumInBagById(data1.item_id)
	self.node_list.qingren_pet_cell_process.text.text = string.format(Language.Activity.ActivityProcess,num_nvwa_suipian,pet_base_cfg.loving_city_convert_image_stuff_count)
	local quancheng_material_cfg = ItemWGData.Instance:GetItemConfig(data1.item_id)

	if quancheng_material_cfg then
		local color = ITEM_COLOR[quancheng_material_cfg.color]
		self.node_list.qingren_pet_cell_name.text.text = string.format(Language.ShengXiaoTips.ShengXiaoTips1,color,quancheng_material_cfg.name)
	end

	local love_city_info = ActivePerfertQingrenWGData.Instance:GetLoveCityInfo()
	if nil == love_city_info.total_process then
		return
	end

	local num_flag = bit:d2b(love_city_info.process_reward_flag)
	self.node_list.quancheng_wawa_get_point:SetActive(num_nvwa_suipian >= pet_base_cfg.loving_city_convert_image_stuff_count and num_flag[32] == 0)
	self.node_list.btn_lingqu_xianwa:SetActive(num_flag[32] == 0)
	self.node_list.btn_quancheng_lingqu:SetActive(false)

end
function ActivePerfertQingrenView:ShowIndexCallBack()
	if CountDownManager.Instance:HasCountDown("quancheng_relian_end_countdown") then
		CountDownManager.Instance:RemoveCountDown("quancheng_relian_end_countdown")
	end

	if self.panel_type and self.panel_type == 3 then
		--ActivePerfertQingrenWGCtrl.Instance:CSGetProfessInfo(TIANYAN_MIYU_ENUM.PROFESS_REQ_TYPE_GLOBAL_ALL)
		ActivePerfertQingrenWGData.Instance:SetIsPersonDanMu(false)
	end

	self:UpdateTimeShow()
end
function ActivePerfertQingrenView:UpdateTimeShow()
	local activity_id = self.panel_type == 1 and ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY or ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_BIAOBAI
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(activity_id)

	if nil == activity_info then
		return
	end

	self:UpdataNextTime(TimeWGCtrl.Instance:GetServerTime(), activity_info.next_time)
	CountDownManager.Instance:AddCountDown("quancheng_relian_end_countdown", BindTool.Bind1(self.UpdataNextTime, self), BindTool.Bind1(self.CompleteNextTime, self), activity_info.next_time, nil, 1)
end

function ActivePerfertQingrenView:UpdataNextTime(elapse_time, total_time)
	local have_total_time = total_time - elapse_time
	self.node_list.activity_end_time:SetActive(self.panel_type ~= 3)
	local str = ""
	if self.panel_type == 1 then
		local time_tab = TimeUtil.Format2TableDHM(have_total_time)
		if time_tab.day > 0 then
			str = string.format(Language.Common.TimeStr, time_tab.day, time_tab.hour, time_tab.min)
		else
			if time_tab.hour > 0 then
				str = string.format(Language.Common.TimeStr1, time_tab.hour, time_tab.min)
			else
				str = string.format(Language.Common.TimeStr6, time_tab.min)
			end
		end

		self.node_list.activity_end_time.text.text = string.format(Language.Activity.ActivityEndTimeDesc_4,str)--"已发奖，距离活动结束："..str
		return
	end

	local cfg_info = ActivePerfertQingrenWGData.Instance:GetBiaoBaiEndTimeCfg()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType( ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_BIAOBAI)
	if activity_info == nil then
		return
	end

	local end_hour_time_1 = cfg_info.end_time / 100
	local end_hour_time_2 = cfg_info.end_time % 100
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local reward_start_time = 24 * 60 * 60 * (cfg_info.end_day - 1) + end_hour_time_1*60*60 + end_hour_time_2 * 60 - (TimeWGCtrl.Instance:GetServerTime() - activity_info.start_time)
	local reward_start_time1 = reward_start_time + cfg_info.during_time_s
	local str

	if reward_start_time > 0 then
		local time_tab = TimeUtil.Format2TableDHM(reward_start_time)
		if time_tab.day > 0 then
			str = string.format(Language.Common.TimeStr, time_tab.day, time_tab.hour, time_tab.min)
		else
			if time_tab.hour > 0 then
				str = string.format(Language.Common.TimeStr1, time_tab.hour, time_tab.min)
			else
				str = string.format(Language.Common.TimeStr6, time_tab.min)
			end
		end
		self.node_list.activity_end_time.text.text = string.format(Language.Activity.ActivityEndTimeDesc_1,str) --"距离排名结算："..str
	else
		if reward_start_time1 > 0 then
			local time_tab = TimeUtil.Format2TableDHM(reward_start_time1)
			str = string.format(Language.Common.TimeStr6, time_tab.min)
			self.node_list.activity_end_time.text.text = string.format(Language.Activity.ActivityEndTimeDesc_2,str)--"结算中，距离奖励发放："..str
		else
			local time_tab = TimeUtil.Format2TableDHM(have_total_time)
			if time_tab.day > 0 then
				str = string.format(Language.Common.TimeStr, time_tab.day, time_tab.hour, time_tab.min)
			else
				if time_tab.hour > 0 then
					str = string.format(Language.Common.TimeStr1, time_tab.hour, time_tab.min)
				else
					str = string.format(Language.Common.TimeStr6, time_tab.min)
				end
			end

			self.node_list.activity_end_time.text.text = string.format(Language.Activity.ActivityEndTimeDesc_3,str)--"已发奖，距离活动结束："..str
		end
	end
end

function ActivePerfertQingrenView:CompleteNextTime()

end

function ActivePerfertQingrenView:OnFlush()
	self.node_list["quancheng_group_1"]:SetActive(self.panel_type == 1)
	self.node_list["quancheng_group_2"]:SetActive(self.panel_type == 2)
	self.node_list["quancheng_group_3"]:SetActive(self.panel_type == 3)
	self.node_list.RawImage_bg:SetActive(self.panel_type ~= 3)
	self.node_list.pet_zhanli:SetActive(false)
	-- self.node_list.pet_zhanli:SetActive(self.panel_type ~= 3)
	self.node_list.activity_desc_1:SetActive(self.panel_type == 1)
	self.node_list.activity_desc_2:SetActive(self.panel_type == 2)
	self.node_list.activity_desc_3:SetActive(self.panel_type == 3)

	if self.panel_type == 1 then
		local love_city_tasklist_cfg,loving_city_process_reward =  ActivePerfertQingrenWGData.Instance:GetQuanChengReLianTaskCfg()
		local love_city_info = ActivePerfertQingrenWGData.Instance:GetLoveCityInfo()
		--print_error(love_city_info.total_process)
		if nil == love_city_info.total_process then return end


		local temp_satate_info_list = love_city_info.task_list
		local sort_base_task_cfg = __TableCopy(love_city_tasklist_cfg)
		for k,v in pairs(sort_base_task_cfg) do
			if temp_satate_info_list[k].cur_state == 0 then
				v.sort_index = 1
			elseif temp_satate_info_list[k].cur_state == 1 then
				v.sort_index = 0
			else
				v.sort_index = 2
			end
			v.process = temp_satate_info_list[k].process
		end

		local slider_value = love_city_info.total_process / loving_city_process_reward[#loving_city_process_reward].need_process
		self.node_list.quancheng_all_progress.slider.value = slider_value
		local num_flag = bit:d2b(love_city_info.process_reward_flag)

		for i=1,5 do
			local is_get = num_flag[32 - i]
			if is_get == 0 then
				--if slider_value >= (0.25 * i) then
				if  love_city_info.total_process >= loving_city_process_reward[i].need_process then
					self.node_list["inage_slider_mark_"..i]:SetActive(true)
					self.node_list["inage_slider_mark_gray_"..i]:SetActive(false)
					self.node_list["inage_slider_mark_remind_"..i]:SetActive(true)
					self.node_list["inage_slider_mark_"..i].animator:SetBool("is_shake", true)
					self.node_list["inage_slider_mark_gou_"..i]:SetActive(false)
					self.node_list["inage_slider_bg_"..i]:SetActive(true)
				else
					self.node_list["inage_slider_mark_"..i]:SetActive(false)
					self.node_list["inage_slider_mark_gray_"..i]:SetActive(true)
					self.node_list["inage_slider_mark_remind_"..i]:SetActive(false)
					self.node_list["inage_slider_mark_"..i].animator:SetBool("is_shake", false)
					self.node_list["inage_slider_mark_gou_"..i]:SetActive(false)
					self.node_list["inage_slider_bg_"..i]:SetActive(false)

				end
			else
				self.node_list["inage_slider_mark_"..i]:SetActive(true)
				self.node_list["inage_slider_mark_gray_"..i]:SetActive(false)
				self.node_list["inage_slider_mark_remind_"..i]:SetActive(false)
				self.node_list["inage_slider_mark_"..i].animator:SetBool("is_shake", false)
				self.node_list["inage_slider_mark_gou_"..i]:SetActive(true)
				self.node_list["inage_slider_bg_"..i]:SetActive(false)
			end
		end

		table.sort( sort_base_task_cfg, SortTools.KeyLowerSorters("sort_index","task_id"))
		self.quancheng_task_list:SetDataList(sort_base_task_cfg,3)
		self:SetQuanChengLoveBaseInfo()
	elseif self.panel_type == 2 then
		local rank_role_info1 = ActivePerfertQingrenWGData.Instance:GetLoveBiaoBaiRank()

		if nil == rank_role_info1.count then
			return
		end

		local rank_role_info,my_rank_index,my_rank_score = ActivePerfertQingrenWGData.Instance:SetLoveBiaoBaiRank1()
		local first_rank_value = rank_role_info[1].rank_value
		self.node_list.rank_group:SetActive(first_rank_value > 0)
		self.node_list.no_rank_first:SetActive(first_rank_value <= 0)
		if first_rank_value > 0 then
			local user_id = RoleWGData.Instance:InCrossGetOriginUid()
			if user_id == rank_role_info[1].h_uid then
				self.node_list.first_head_icon_nan_name.text.text =  ToColorStr(rank_role_info[1].h_name, COLOR3B.PURPLE)
			else
				self.node_list.first_head_icon_nan_name.text.text = rank_role_info[1].h_name
			end

			if user_id == rank_role_info[1].w_uid then
				self.node_list.first_head_icon_nv_name.text.text =  ToColorStr(rank_role_info[1].w_name, COLOR3B.PURPLE)
			else
				self.node_list.first_head_icon_nv_name.text.text = rank_role_info[1].w_name
			end

			self.node_list.first_head_icon_score.text.text = string.format(Language.Activity.XuQiuJiFen_1,rank_role_info[1].rank_value)
			XUI.UpdateRoleHead(self.node_list["head_icon_1"], self.node_list["custom_head_icon_1"], rank_role_info[1].w_uid,rank_role_info[1].w_sex,rank_role_info[1].w_prof, false, nil, true)
			XUI.UpdateRoleHead(self.node_list["head_icon_2"], self.node_list["custom_head_icon_2"], rank_role_info[1].h_uid,rank_role_info[1].h_sex,rank_role_info[1].h_prof, false, nil, true)
		end
		local _, _, _, fenshu = ActivePerfertQingrenWGData.Instance:GetLoveBiaoBaiScore()
		self.node_list.myrank_rank_and_score_1.text.text = string.format(Language.Activity.XuQiuJiFen_1,fenshu)

		if my_rank_index then
			self.node_list.myrank_rank_and_score.text.text = string.format(Language.Activity.XuQiuJiFenRank,my_rank_index)
		else
			self.node_list.myrank_rank_and_score.text.text = Language.GuildAnswer.NoRank1
		end
	elseif self.panel_type == 3 then
	end
end

function ActivePerfertQingrenView:UpDateBanLvHeadIcon()
	for i=1,5 do
		self.node_list["custom_head_icon_"..i]:SetActive(false)
		self.node_list["head_icon_"..i]:SetActive(false)
	end

	if nil == self.first_reward_list_view then
		 self.first_reward_list_view = AsyncListView.New(LoveBiaoBaiFirstReaward,self.node_list.first_reward_list)
	end

	if nil == self.all_reward_list_view then
		 self.all_reward_list_view = AsyncListView.New(LoveBiaoBaiAllReaward,self.node_list.rank_reward_list)
	end

	local rank_reward_cfg = ActivePerfertQingrenWGData.Instance:GetBiaoBaiRankCfg()
	local first_data_list = rank_reward_cfg[1].reward_item
	local temp_cell_list = {}
	local temp_data_list = {}

	for i=0,#rank_reward_cfg[1].reward_item do
		table.insert(temp_cell_list,rank_reward_cfg[1].reward_item[i])
	end

	self.first_reward_list_view:SetDataList(temp_cell_list,0)
	for k,v in pairs(rank_reward_cfg) do
		if k > 1 then
			table.insert(temp_data_list,v)
		end
	end

	self.all_reward_list_view:SetDataList(temp_data_list,0)
	local lover_uid = RoleWGData.Instance.role_vo.lover_uid
	local sex = RoleWGData.Instance.role_vo.sex
	local main_vo = GameVoManager.Instance:GetMainRoleVo()
    local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local cutom_head_info = RoleWGData.Instance:GetCustomHeadInfo()

	if cutom_head_info then
		XUI.UpdateMainRoleHead(cutom_head_info.avatar_key_big,
								cutom_head_info.avatar_key_small,
								self.node_list.head_icon_3,
								self.node_list.custom_head_icon_3,
								role_id,
                                main_vo.sex,
                                main_vo.prof,
								nil,
								false,
								true)
	end

	self.node_list.myrank_head_icon_1.text.text = main_vo.name
	self.node_list.wode_banlv_bg:SetActive(lover_uid > 0)
	self.node_list.no_banlv:SetActive(lover_uid <= 0)
	self.node_list.myxianlv_head_icon:SetActive(lover_uid <= 0 and sex == GameEnum.FEMALE)
	self.node_list.myxianlv_head_icon_1:SetActive(lover_uid <= 0 and sex == GameEnum.MALE)
	self.node_list.custom_head_icon_5:SetActive(lover_uid > 0)
	self.node_list.go_marry_btn:SetActive( lover_uid <= 0)
	self.node_list.other_biaobai_btn:SetActive( lover_uid > 0)
	self.node_list.my_biaobai_btn:SetActive( lover_uid > 0)

	if lover_uid > 0 then
		self.node_list["myrank_head_icon_2"].text.text = RoleWGData.Instance.role_vo.lover_name
		BrowseWGCtrl.Instance:BrowRoelInfo(lover_uid, BindTool.Bind1(self.BanLvHeadIconFlush, self))
	end
end

function ActivePerfertQingrenView:BanLvHeadIconFlush(info)
	if not self:IsOpen() or not self:IsLoadedIndex(0) then
		return
	end
	XUI.UpdateMainRoleHead(info.avatar_key_big,
							info.avatar_key_small,
							self.node_list.head_icon_5,
							self.node_list.custom_head_icon_5,
							info.role_id,
							info.sex, info.prof, nil, false, true)
	XUI.UpdateMainRoleHead(info.avatar_key_big,
							info.avatar_key_small,
							self.node_list.head_icon_4,
							self.node_list.custom_head_icon_4,
							info.role_id,
							info.sex, info.prof, nil, false, true)
end

--爱的表白排行榜
function ActivePerfertQingrenView:LoveBiaoBaiRankSecondView()
	--ActivePerfertQingrenWGCtrl.Instance:OpenBiaoBaiSecondView()
end

-- 1 我的表白 2 向TA表白
function ActivePerfertQingrenView:SendBiaoBtn(index)
	if index == 2 then
		ProfessWallWGData.Instance:SetDefaultInfo(nil,true)
		--ViewManager.Instance:Open(GuideModuleName.ChooseProfessView)
	elseif index == 1 then
		ViewManager.Instance:Open(GuideModuleName.Marry,TabIndex.marry_profess_wall)
	elseif index == 3 then
		local call_back = function ()
			ViewManager.Instance:CloseAll()
		end
		MarryWGCtrl.Instance:OpenSelectLoverView(1,call_back)
	end
end

function ActivePerfertQingrenView:GetXianWaBtn()
	--后面加参数只是为了打开面板不让他走底层红点逻辑默认选中红点问题
	ViewManager.Instance:Open(GuideModuleName.Compose, TabIndex.other_compose_kid, nil, {["from"] ="Perfert"})
	-- ActivePerfertQingrenWGCtrl.Instance:SendReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY,
	-- 	PERFERT_QINGREN_ENUM.RA_ACTIVE_LOVING_CITY_OP_TYPE_FETCH_IMAGE)
end

function ActivePerfertQingrenView:ActivityTypePanel(panel_type)
	for i=1,3 do
		self.node_list["select_image_btn_"..i]:SetActive(i == panel_type)
	end

	if self.panel_type == panel_type then
		return
	end

	if self.panel_type == 3 then
		self:ResertDanMuTweenPos()
	end

	self.panel_type = panel_type
	local pet_base_cfg = ActivePerfertQingrenWGData.Instance:GetQuanChengReLianCfgOther()
	if panel_type == 1 then
		local data = pet_base_cfg.loving_city_image_item
		local quancheng_material_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
		self.node_list.pet_zhanli.text.text = quancheng_material_cfg.capability_show
		--ActivePerfertQingrenWGCtrl.Instance:SendReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY,PERFERT_QINGREN_ENUM.RA_ACTIVE_LOVING_CITY_OP_TYPE_INFO)
	elseif panel_type == 2 then
		local data = pet_base_cfg.love_profess_resource_item
		local quancheng_material_cfg = ItemWGData.Instance:GetItemConfig(data)
		self.node_list.pet_zhanli.text.text = quancheng_material_cfg.capability_show
		--ActivePerfertQingrenWGCtrl.Instance:SendReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_BIAOBAI,0)
		--ActivePerfertQingrenWGCtrl.Instance:SendReqBiaoBai(0)
		self:UpDateBanLvHeadIcon()
	elseif panel_type == 3 then
		--ActivePerfertQingrenWGCtrl.Instance:CSGetProfessInfo(TIANYAN_MIYU_ENUM.PROFESS_REQ_TYPE_GLOBAL_ALL)
		ActivePerfertQingrenWGData.Instance:SetIsPersonDanMu(false)
		self:TianYanMiYuBtn(1)
	end
	self:Flush()
	self:UpdateTimeShow()
end

function ActivePerfertQingrenView:OpenCityLoveNumReawrd()
	--ActivePerfertQingrenWGCtrl.Instance:OpenLoveCityHeartNunRewardPanel()
end

function ActivePerfertQingrenView:TianYanMiYuBtn(index)
	self.tianyan_miyu_index = index
	self.node_list.biaobai_btn_sel_image_1:SetActive(index == 1)
	self.node_list.biaobai_btn_sel_image_2:SetActive(index == 2)
	local str = index == 1 and Language.Activity.TitelNameText_1 or Language.Activity.TitelNameText_2
	self.node_list.title_name_text.text.text = str

	if index == 1 then
		RankWGCtrl.Instance:SendGetPersonRankListReq(PersonRankType.PERSON_RANK_TYPE_PROFESS_SCORE, nil)
	elseif index == 2 then
		RankWGCtrl.Instance:SendGetPersonRankListReq(PersonRankType.PERSON_RANK_TYPE_BE_PROFESS_SCORE, nil)
	end

	local is_person_danmu = ActivePerfertQingrenWGData.Instance:GetIsPersonDanMu()
	if is_person_danmu then
		--ActivePerfertQingrenWGCtrl.Instance:CSGetProfessInfo(TIANYAN_MIYU_ENUM.PROFESS_REQ_TYPE_GLOBAL_ALL)
		ActivePerfertQingrenWGData.Instance:SetIsPersonDanMu(false)
	end
end

function ActivePerfertQingrenView:FlushTianYanMiYuRankList()
	if nil == self.tianyan_miyu_rank_list or nil == self.node_list or nil == self.node_list.layout_blank_tip_3 then
		return
	end

	local data_list = {}
	local temp_list = {}
	if nil == self.tianyan_miyu_index or  self.tianyan_miyu_index == 1 then
		data_list = ActivePerfertQingrenWGData.Instance:GetTianYanMiYuRankInfo(PersonRankType.PERSON_RANK_TYPE_PROFESS_SCORE)
	else
		data_list = ActivePerfertQingrenWGData.Instance:GetTianYanMiYuRankInfo(PersonRankType.PERSON_RANK_TYPE_BE_PROFESS_SCORE)
	end

	for k,v in pairs(data_list) do
		if k <= 10 and v.rank_value > 0 then
			table.insert(temp_list,v)
		end
	end

	for k,v in pairs(temp_list) do
		v.type_index = self.tianyan_miyu_index or 1
	end

	self.tianyan_miyu_rank_list:CancelSelect()
	self.tianyan_miyu_rank_list:SetDataList(temp_list,3)
	self.node_list.layout_blank_tip_3:SetActive(#temp_list <= 0)
end

--弹幕显示
function ActivePerfertQingrenView:FlushDanMuShow(protocol) --5s
	--print_error(protocol)
	if nil == self.cache_show_list then return end
	self:ResertDanMuTweenPos()
	 --protocol.profess_count = -1
	 self.items = protocol.items
	self.copy_all_list = DeepCopy(self.items)
	for index_type=1,5 do
	--if protocol.profess_count == -1 then
		local cfg_type_1 = index_type % 2
		local cfg_type_2 = math.floor(index_type / 2)
		local cfg_type = cfg_type_1 + cfg_type_2
		if #self.copy_all_list[cfg_type] > 0 then
			local timer = self.cache_show_list[index_type][1]:SetTextData(self.copy_all_list[cfg_type][1],cfg_type)
			local node = self.cache_show_list[index_type][1]:GetMoveRect(cfg_type)
			local tem_move_timp = ActivePerfertQingrenWGData.Instance:GetDanMuParam(cfg_type)
			local tween = node.rect:DOAnchorPosX(-DANMU_TOTAL_LENGTH, tem_move_timp)
			self.cache_show_list[index_type][1].tween = tween
			table.insert(self.now_show_list[index_type],self.cache_show_list[index_type][1])
			table.remove(self.cache_show_list[index_type],1)
			table.remove(self.copy_all_list[cfg_type],1)

			tween:SetEase(DG.Tweening.Ease.Linear)
			tween:OnComplete(function ()
				self:MoveCallBack(index_type)
			end)

			if #self.copy_all_list[cfg_type] <= 0 then
				self.copy_all_list[cfg_type] = DeepCopy(self.items[cfg_type])
			end

			local timer1 = self.cache_show_list[index_type][1]:SetTextData(self.copy_all_list[cfg_type][1],cfg_type)
			self.cache_show_list[index_type][1].delay_timer = GlobalTimerQuest:AddDelayTimer(function()
				self:PrePareShow(index_type)
			end, timer)

			table.insert(self.prepare_show_list[index_type], self.cache_show_list[index_type][1])
			table.remove(self.cache_show_list[index_type], 1)
			table.remove(self.copy_all_list[cfg_type], 1)

		end
	end

end

function ActivePerfertQingrenView:ResertDanMuTweenPos()
	for i = 1, 5 do
		local cfg_type_1 = i % 2
		local cfg_type_2 = math.floor(i / 2)
		local cfg_type = cfg_type_1 + cfg_type_2

		if not IsEmptyTable(self.prepare_show_list) and self.prepare_show_list[i] then
			for k,v in pairs(self.prepare_show_list[i]) do
				if v.delay_timer then
					GlobalTimerQuest:CancelQuest(v.delay_timer)
					v.delay_timer = nil
				end
				table.insert(self.cache_show_list[i],v)
			end
		end

		for k,v in pairs(self.now_show_list[i]) do
			v.tween:Kill()
			if v.delay_timer then
				GlobalTimerQuest:CancelQuest(v.delay_timer)
				v.delay_timer = nil
			end
			v:GetMoveRect(cfg_type).rect.anchoredPosition = Vector2(0,0)
			table.insert(self.cache_show_list[i],v)
		end

		self.now_show_list[i] = {}
		self.prepare_show_list[i] = {}
	end

end
function ActivePerfertQingrenView:PrePareShow(index)
	local cfg_type_1 = index % 2
	local cfg_type_2 = math.floor(index / 2)
	local cfg_type = cfg_type_1 + cfg_type_2
	if self.prepare_show_list and self.prepare_show_list[index][1] then
		local timer = self.prepare_show_list[index][1]:GetDelayTimer()
		local node = self.prepare_show_list[index][1]:GetMoveRect(cfg_type)
		local tem_move_timp = ActivePerfertQingrenWGData.Instance:GetDanMuParam(cfg_type)
		local tween = node.rect:DOAnchorPosX(-DANMU_TOTAL_LENGTH, tem_move_timp)

		tween:SetEase(DG.Tweening.Ease.Linear)
		tween:OnComplete(function ()
			self:MoveCallBack(index)
		end)

		self.prepare_show_list[index][1].tween = tween
		table.insert(self.now_show_list[index],self.prepare_show_list[index][1])
		table.remove(self.prepare_show_list[index],1)

		if #self.copy_all_list[cfg_type] <= 0 then
			self.copy_all_list[cfg_type] = DeepCopy(self.items[cfg_type])
		end

		if nil == self.cache_show_list[index][1] then
			return
		end

		local timer1 = self.cache_show_list[index][1]:SetTextData(self.copy_all_list[cfg_type][1],cfg_type)
		self.cache_show_list[index][1].delay_timer = GlobalTimerQuest:AddDelayTimer(function()
			self:PrePareShow(index)
		end, timer)

		table.insert(self.prepare_show_list[index],self.cache_show_list[index][1])
		table.remove(self.cache_show_list[index],1)
		table.remove(self.copy_all_list[cfg_type],1)

	end
end
function ActivePerfertQingrenView:MoveCallBack(index)
	local cfg_type_1 = index % 2
	local cfg_type_2 = math.floor(index / 2)
	local cfg_type = cfg_type_1 + cfg_type_2
	self.now_show_list[index][1]:GetMoveRect(cfg_type).rect.anchoredPosition = Vector2(0,0)
	self.now_show_list[index][1]:ResertData()

	if self.now_show_list[index][1].delay_timer then
		GlobalTimerQuest:CancelQuest(self.now_show_list[index][1].delay_timer)
		self.now_show_list[index][1].delay_timer = nil
	end

	table.insert(self.cache_show_list[index],self.now_show_list[index][1])
	table.remove(self.now_show_list[index],1)
end

function ActivePerfertQingrenView:ShowTipsInfo()
	if self.panel_type == 1 then
		RuleTip.Instance:SetContent(Language.Activity.DailyActAfterTips19, Language.Activity.DailyActTips19)
	elseif self.panel_type == 2 then
		RuleTip.Instance:SetContent(Language.Activity.DailyActAfterTips20, Language.Activity.DailyActTips20)
	elseif self.panel_type == 3 then
		RuleTip.Instance:SetContent(Language.Activity.DailyActAfterTips21, Language.Activity.DailyActTips21)
	end
end



--全城热恋任务列表
QuanChengReLianTaskRender = QuanChengReLianTaskRender or BaseClass(BaseRender)
function QuanChengReLianTaskRender:__init()

end
function QuanChengReLianTaskRender:__delete()

end
function QuanChengReLianTaskRender:LoadCallBack()
	self.cell_list = {}
	for i=1,2 do
		local num = i - 1
		self.cell_list[i] = ItemCell.New(self.node_list["ph_cell_"..num])
	end
	XUI.AddClickEventListener(self.node_list["btn_task_lingqu"], BindTool.Bind(self.GetRewardOrGoAct, self))
end
function QuanChengReLianTaskRender:OnFlush()
	if nil == self.data then
		return
	end

	self.node_list.img_ylq:SetActive(self.data.sort_index == 2)
	self.node_list.image_redmind:SetActive(self.data.sort_index == 0)
	self.node_list.btn_image_1:SetActive(self.data.sort_index == 0)
	self.node_list.btn_image_2:SetActive(self.data.sort_index == 1)
	self.node_list["btn_task_lingqu"]:SetActive(self.data.sort_index ~= 2)
	local num_1 =  self.data.process >= self.data.task_process and self.data.task_process or self.data.process
	local is_show_str = self.data.process >= self.data.task_process and string.format(Language.Activity.ActivityProcess1,num_1) or string.format(Language.Activity.ActivityProcess2,num_1)
	self.node_list.task_process.text.text = "" --string.format(Language.Activity.ActivityProcess,self.data.process,self.data.task_process)
	self.node_list.task_name.text.text = self.data.task_name
	self.node_list.task_desc.text.text =   string.format(self.data.task_desc,is_show_str)

	for i=1,5 do
		self.node_list["star_"..i]:SetActive(i <= self.data.reward_process)
	end

	for i=1,2 do
		self.cell_list[i]:SetData(self.data.reward_show[i - 1])
	end
end
function QuanChengReLianTaskRender:GetRewardOrGoAct()
	if self.data.sort_index == 0 then
		--ActivePerfertQingrenWGCtrl.Instance:SendReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY,
		--	PERFERT_QINGREN_ENUM.RA_ACTIVE_LOVING_CITY_OP_TYPE_FETCH_TASK_REWARD,self.data.task_id)
	else
		FunOpen.Instance:OpenViewNameByCfg(self.data.open_panel)

	end
end
---------------------------------------全城热恋数量奖励面板-----------------------------------------------------------------------------
LoveCityHeartNunRewardPanel = LoveCityHeartNunRewardPanel or BaseClass(SafeBaseView)
function LoveCityHeartNunRewardPanel:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/act_tab_view_prefab", "layout_reward_city_love")
end

function LoveCityHeartNunRewardPanel:__delete()

end

function LoveCityHeartNunRewardPanel:ReleaseCallBack()
	if self.reward_list_view then
		self.reward_list_view:DeleteMe()
		self.reward_list_view = nil
	end
end

function LoveCityHeartNunRewardPanel:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.KuafuPVP.ViewName_JieDuanJiangLi
	self:SetSecondView(Vector2(825, 648))
	self:Flush()
end

function LoveCityHeartNunRewardPanel:OnFlush()
	if nil == self.reward_list_view then
		self.reward_list_view = AsyncListView.New(CityLoveNumReWardItemRender, self.node_list["ph_item_list"])
	end

	if self.reward_list_view then
		local love_city_tasklist_cfg,loving_city_process_reward =  ActivePerfertQingrenWGData.Instance:GetQuanChengReLianTaskCfg()
		local love_city_info = ActivePerfertQingrenWGData.Instance:GetLoveCityInfo()
		if nil == love_city_info.total_process then return end
		local temp_satate_info_list = love_city_info.task_list
		local sort_base_numreward_cfg = __TableCopy(loving_city_process_reward)
		local num_flag = bit:d2b(love_city_info.process_reward_flag)
		--数据处理
		local data_list = {}
		for k,v in pairs(sort_base_numreward_cfg) do
			local is_get = num_flag[32 - k]
			if is_get == 0 and  love_city_info.total_process >= v.need_process then
				v.reward_states = 0
			elseif is_get == 0 and  love_city_info.total_process < v.need_process then
				v.reward_states = 1
			elseif is_get == 1 then
				v.reward_states = 2
			end
		end
		table.sort(sort_base_numreward_cfg,SortTools.KeyLowerSorter("reward_states","seq"))
		self.reward_list_view:SetDataList(sort_base_numreward_cfg, 0)
		self.node_list["star_num"].text.text = love_city_info.total_process
	end
end


--热恋数量累积奖励
CityLoveNumReWardItemRender = CityLoveNumReWardItemRender or BaseClass(BaseRender)
function CityLoveNumReWardItemRender:__init()

end

function CityLoveNumReWardItemRender:__delete()

	if self.rewarditem_cells then
		for k,v in pairs(self.rewarditem_cells) do
			v:DeleteMe()
		end
		self.rewarditem_cells = nil
	end
end

function CityLoveNumReWardItemRender:LoadCallBack()
	self.node_list.btn_lingqu.button:AddClickListener(BindTool.Bind(self.OnClickLingQu, self))
	if nil == self.rewarditem_cells then
		self.rewarditem_cells = {}
		for i=1,4 do
			local rewarditem_cells = ItemCell.New()
			rewarditem_cells:SetInstanceParent(self.node_list["ph_show_"..i])
			table.insert(self.rewarditem_cells,rewarditem_cells)
		end
	end
end

function CityLoveNumReWardItemRender:OnFlush()
	if not self.data then return end
	for i=1,4 do
		if self.data.reward_item[i - 1] then
			self.rewarditem_cells[i]:SetData(self.data.reward_item[i - 1])
			self.node_list["ph_show_"..i]:SetActive(true)
		else
			self.node_list["ph_show_"..i]:SetActive(false)
		end

	end
	self.node_list["lbl_lscs"].text.text = self.data.need_process
	self.node_list["btn_lingqu"]:SetActive(self.data.reward_states == XIUZHENZHILU_TASK_STATES.KLQ)
	self.node_list["image_wdc"]:SetActive(self.data.reward_states == XIUZHENZHILU_TASK_STATES.WDC)
	self.node_list["image_ylq"]:SetActive(self.data.reward_states == XIUZHENZHILU_TASK_STATES.YLQ)
	XUI.SetButtonEnabled(self.node_list["btn_lingqu"], self.data.reward_states == XIUZHENZHILU_TASK_STATES.KLQ)

end

function CityLoveNumReWardItemRender:OnClickLingQu()
	--XiuZhenRoadWGCtrl.Instance:SendXiuZhenZhiLuReq(XIUZHENZHILU_OPEN_TYPE.XIUZHENZHILU_OPEN_TYPE_PROGRESS_REWARD,self.data.old_info.progress)
	--ActivePerfertQingrenWGCtrl.Instance:SendReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY,PERFERT_QINGREN_ENUM.RA_ACTIVE_LOVING_CITY_OP_TYPE_FETCH_PROCESS_REWARD,self.data.seq)
end

---------------------------------------------------------------------------------------------------------------------------------------
--爱的表白第一名奖励
LoveBiaoBaiFirstReaward = LoveBiaoBaiFirstReaward or BaseClass(BaseRender)
function LoveBiaoBaiFirstReaward:__init()

end

function LoveBiaoBaiFirstReaward:__delete()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end
function LoveBiaoBaiFirstReaward:LoadCallBack()
	self.cell = ItemCell.New(self.node_list.basecell_pos)
end
function LoveBiaoBaiFirstReaward:OnFlush()
	if nil == self.data then
		return
	end

	self.cell:SetData(self.data)
end

LoveBiaoBaiAllReaward = LoveBiaoBaiAllReaward or BaseClass(BaseRender)
function LoveBiaoBaiAllReaward:__init()

end

function LoveBiaoBaiAllReaward:__delete()
	if self.cell_list then
		self.cell_list:DeleteMe()
		self.cell_list = nil
	end
end
function LoveBiaoBaiAllReaward:LoadCallBack()
	self.cell_list = AsyncListView.New(LoveBiaoBaiFirstReaward,self.node_list.rank_reward_render_list)
end
function LoveBiaoBaiAllReaward:OnFlush()
	if nil == self.data then
		return
	end

	local temp_data_list = {}
	for i=0,#self.data.reward_item do
		table.insert(temp_data_list,self.data.reward_item[i])
	end

	self.cell_list:SetDataList(temp_data_list,0)
	local all_cfg = ActivePerfertQingrenWGData.Instance:GetBiaoBaiRankCfg()
	if self.index == #all_cfg - 1 then
		self.node_list.title_text.text.text = string.format(Language.Activity.BiaoBaiRewardLimitDesc_1,self.data.rank_val_limit)
	else
		self.node_list.title_text.text.text = string.format(Language.Activity.BiaoBaiRewardLimitDesc,self.data.rank_max_client,self.data.rank_min_client,self.data.rank_val_limit)
		--"双方表白积分<color=#df140f>"..self.data.rank_max_client.."-"..self.data.rank_min_client.."</color>名且达到<color=#df140f>"..self.data.rank_val_limit.."</color>分获得"
	end
end


LoveBiaoBaiSecondView = LoveBiaoBaiSecondView or BaseClass(SafeBaseView)
function LoveBiaoBaiSecondView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/act_tab_view_prefab", "layout_reward_city_love_biaobai")
end

function LoveBiaoBaiSecondView:__delete()

end

function LoveBiaoBaiSecondView:ReleaseCallBack()
	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end
end

function LoveBiaoBaiSecondView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Market.WorldRankName
	self:SetSecondView(Vector2(914, 664))
	self.rank_list = AsyncListView.New(BiaoBaiSecondViewRender,self.node_list.ph_item_list)
	self:Flush()
end

function LoveBiaoBaiSecondView:OnFlush()
	local data = ActivePerfertQingrenWGData.Instance:SetLoveBiaoBaiRank1()

	if not data or IsEmptyTable(data) then
		return
	end

	local self_rank_info, rank_num = ActivePerfertQingrenWGData.Instance:GetSelfLoveBiaoBaiRank1()

	if nil == self_rank_info then
		self.node_list.rank_img:SetActive(false)
		self.node_list.rank_num:SetActive(true)
		local _, _, _, rank_value  = ActivePerfertQingrenWGData.Instance:GetLoveBiaoBaiScore()
		self.node_list.rank_num.text.text = Language.Rank.NoRank
		self.node_list.rank_value.text.text = rank_value or 0
	else
		self.node_list.rank_img:SetActive(rank_num <= 3)
		self.node_list.rank_num:SetActive(rank_num > 3)

		if rank_num <= 3 then
			local b, a = ResPath.GetCommonIcon("new_rank_" .. rank_num)
			self.node_list.rank_img.image:LoadSprite(b, a, function()
				XUI.ImageSetNativeSize(self.node_list.rank_img)
			end)
		end

		self.node_list.rank_num.text.text = rank_num or 0
		self.node_list.rank_value.text.text = self_rank_info.rank_value or 0
	end

	self.rank_list:SetDataList(data,3)
end

BiaoBaiSecondViewRender = BiaoBaiSecondViewRender or BaseClass(BaseRender)
function BiaoBaiSecondViewRender:__init()

end

function BiaoBaiSecondViewRender:__delete()

end

function BiaoBaiSecondViewRender:OnFlush()
	if nil == self.data then return end
	if self.data.rank_index <= 3 then
		local b,a = ResPath.GetCommonIcon("new_rank_"..self.data.rank_index)
		self.node_list.rank_image.image:LoadSprite(b,a,function()
			XUI.ImageSetNativeSize(self.node_list.rank_image)
		end)
	end
	self.node_list.img9_reward_bg:SetActive(self.index%2 == 0)
	self.node_list.rank_image:SetActive(self.data.rank_index <= 3)
	self.node_list.rang_text.text.text = self.data.rank_index
	self.node_list.Image_1:SetActive(self.data.rank_value > 0)
	self.node_list.Image_2:SetActive(self.data.rank_value > 0)
	self.node_list.no_rank:SetActive(self.data.rank_value <= 0)

	if self.data.rank_value > 0 then
		local user_id = RoleWGData.Instance:InCrossGetOriginUid()
		self.node_list.score.text.text = self.data.rank_value
		XUI.UpdateRoleHead(self.node_list["head_icon_1"], self.node_list["custom_head_icon_1"], self.data.w_uid,
			self.data.w_sex, self.data.w_prof,false, nil, true)
		XUI.UpdateRoleHead(self.node_list["head_icon_2"], self.node_list["custom_head_icon_2"], self.data.h_uid,
			self.data.h_sex, self.data.h_prof,false, nil, true)
		if user_id == self.data.w_uid then
			self.node_list.name_1.text.text = ToColorStr(self.data.w_name, COLOR3B.PURPLE)
		else
			self.node_list.name_1.text.text = self.data.w_name
		end


		if user_id == self.data.h_uid then
			self.node_list.name_2.text.text =  ToColorStr(self.data.h_name, COLOR3B.PURPLE)
		else
			self.node_list.name_2.text.text = self.data.h_name
		end

	else
		self.node_list.score.text.text = string.format(Language.Activity.XuQiuJiFen,self.data.limit_value) --"需求积分："..self.data.limit_value
	end
end


-------------------------------------------甜言蜜语--------------------------------------------------------------
TianYanMiYuRankRender = TianYanMiYuRankRender or BaseClass(BaseRender)
function TianYanMiYuRankRender:__init()

end

function TianYanMiYuRankRender:__delete()
	if self.call_back then
		self.call_back = nil
	end
end
function TianYanMiYuRankRender:LoadCallBack()
	--XUI.AddClickEventListener(self.node_list["look_btn"], BindTool.Bind(self.LookInfoDanMu, self))
end
function TianYanMiYuRankRender:OnFlush()
	if nil == self.data then return end
	self.node_list.name.text.text = self.data.user_name
	self.node_list.score.text.text = self.data.rank_value
	self.node_list.rank_text.text.text = self.index
	self.node_list.rank_image:SetActive(self.index <= 3)

	if self.index <= 3 then
		local b,a = ResPath.GetCommonIcon("new_rank_"..self.index)
		self.node_list.rank_image.image:LoadSprite(b,a,function()
			XUI.ImageSetNativeSize(self.node_list.rank_image)
		end)
	end

	self.node_list.image_bg:SetActive(self.index % 2 == 0)
end
function TianYanMiYuRankRender:LookInfoDanMu()
	if self.data.type_index == 1 then
		--ActivePerfertQingrenWGCtrl.Instance:CSGetProfessInfo(TIANYAN_MIYU_ENUM.PROFESS_REQ_TYPE_ACTIVE_OTHER,self.data.user_id)
	else
		--ActivePerfertQingrenWGCtrl.Instance:CSGetProfessInfo(TIANYAN_MIYU_ENUM.PROFESS_REQ_TYPE_PASSIVE_OTHER,self.data.user_id)
	end
end
function TianYanMiYuRankRender:OnSelectChange(is_select)

	if is_select then
		local is_vas = self.node_list.select_image:GetActive()
		self.node_list.select_image:SetActive(not is_vas)
		if is_vas then
			--ActivePerfertQingrenWGCtrl.Instance:CSGetProfessInfo(TIANYAN_MIYU_ENUM.PROFESS_REQ_TYPE_GLOBAL_ALL)
			ActivePerfertQingrenWGData.Instance:SetIsPersonDanMu(false)
		else
			if self.data.type_index == 1 then
				--ActivePerfertQingrenWGCtrl.Instance:CSGetProfessInfo(TIANYAN_MIYU_ENUM.PROFESS_REQ_TYPE_ACTIVE_OTHER,self.data.user_id)
			else
				--ActivePerfertQingrenWGCtrl.Instance:CSGetProfessInfo(TIANYAN_MIYU_ENUM.PROFESS_REQ_TYPE_PASSIVE_OTHER,self.data.user_id)
			end
			ActivePerfertQingrenWGData.Instance:SetIsPersonDanMu(true)
		end
	else
		self.node_list.select_image:SetActive(is_select)
	end

end


TianYanMiYuDanMuRender = TianYanMiYuDanMuRender or BaseClass(BaseRender)
function TianYanMiYuDanMuRender:__init()
end

function TianYanMiYuDanMuRender:LoadCallBack()
end

function TianYanMiYuDanMuRender:__delete()
	self.timer = nil
end

function TianYanMiYuDanMuRender:OnFlush()
	if nil == self.data then return end
end

function TianYanMiYuDanMuRender:SetTextData(data,show_type)
	if data.role_id_from then
		if show_type == 3 then
			self.node_list.content_text.text.text = string.format(Language.Activity.TianYanMiYu_2,data.professor,data.be_professor)--data.professor.."对"..data.be_professor.."送出"
		else
			self.node_list.content_text.text.text = string.format(Language.Activity.TianYanMiYu_1,data.professor,data.be_professor,data.content)--data.professor.."对"..data.be_professor.."说："..data.content
		end

	else
		if show_type == 3 then
			if nil == data.profess_type or data.profess_type == TIANYAN_MIYU_ENUM.PROFESS_REQ_TYPE_ACTIVE_OTHER then --表白
				self.node_list.content_text.text.text = string.format(Language.Activity.TianYanMiYu_2,data.professor,data.be_professor)
			 else                           --被表白
			 	self.node_list.content_text.text.text = string.format(Language.Activity.TianYanMiYu_2,data.be_professor,data.professor)
			 end
		else
			--print_error(data.profess_type)
			if nil == data.profess_type or data.profess_type == TIANYAN_MIYU_ENUM.PROFESS_REQ_TYPE_ACTIVE_OTHER then --表白
				self.node_list.content_text.text.text = string.format(Language.Activity.TianYanMiYu_1,data.professor,data.be_professor,data.content)
			 else                           --被表白
				self.node_list.content_text.text.text = string.format(Language.Activity.TianYanMiYu_1,data.be_professor,data.professor,data.content)
			 end
		end

	end
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["content_text"].rect)
	local text_length = self.node_list.content_text.rect.sizeDelta.x
	local add_length = 122
	local item_high = 49
	local total_time,barrage_space = ActivePerfertQingrenWGData.Instance:GetDanMuParam(show_type)

	if show_type == 1 then
		add_length = 122
	elseif show_type == 2 then
		add_length = 100
		item_high = 70
	elseif show_type == 3 then
		add_length = 111
		item_high = 57
	end

	self.node_list.Image_bg.rect.sizeDelta = Vector2(text_length + add_length,item_high)
	local timer = 0
	timer = ((text_length + add_length + barrage_space)/DANMU_TOTAL_LENGTH ) * total_time
	self.timer = timer
	return timer
end

function TianYanMiYuDanMuRender:GetMoveRect(panel_type)
	if self.node_list["barrage_group_danmu_"..panel_type] then
		return self.node_list["barrage_group_danmu_"..panel_type]
	end
end

function TianYanMiYuDanMuRender:GetDelayTimer()
	return self.timer or 0
end

function TianYanMiYuDanMuRender:ResertData()
	self.timer = nil
end
