ConsumeRankThreeWGData = ConsumeRankThreeWGData or BaseClass()

function ConsumeRankThreeWGData:__init()
	if ConsumeRankThreeWGData.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[ConsumeRankThreeWGData] attempt to create singleton twice!")
		return
	end
	ConsumeRankThreeWGData.Instance = self
end

function ConsumeRankThreeWGData:__delete()
	ConsumeRankThreeWGData.Instance = nil
end

function ConsumeRankThreeWGData:GetRewardShowData()
	local cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	if nil == cfg or nil == cfg.consume_gold_rank then return end
	local rand_t = cfg.consume_gold_rank
	local continuous_recharge_cfg = ServerActivityWGData.Instance:GetRandActivityConfig(rand_t, ACTIVITY_TYPE.RAND_CONSUME_GOLD_RANK)
	return continuous_recharge_cfg
end

function ConsumeRankThreeWGData:GetRankListData(act_id)
	local rank_list = ServerActivityWGData.Instance.act_rank_list[act_id]
	if rank_list == nil then return end
	local cfg = self:GetRewardShowData()
	local nb_rank_num = 1
	local rank_data_list = {}
	local my_rank_index = ""
	for i=1,5 do
		rank_data_list[i] = {}
		rank_data_list[i].rank_index = i
		rank_data_list[i].user_name = Language.Common.Undetermined
		rank_data_list[i].rank_value = ""
		rank_data_list[i].reward_item = cfg[i].reward_item
	end


	local function getRankDataByNum(num)
		for _, rank_data in ipairs(cfg) do
			-- 配置  min_rank_pos 从0开始
			if num <= rank_data.rank + 1 then
				return rank_data
			end
		end
	end

	local rank_data = nil
	for k,v in ipairs(rank_list)do
		for up = 1, 5 do
			rank_data = getRankDataByNum(nb_rank_num)
			if rank_data ~= nil then
				if v.rank_value >= rank_data.min_gold then
					rank_data_list[nb_rank_num] = {}
					rank_data_list[nb_rank_num].rank_index = nb_rank_num
					rank_data_list[nb_rank_num].user_name = v.user_name
					rank_data_list[nb_rank_num].rank_value = v.rank_value
					rank_data_list[nb_rank_num].reward_item = cfg[nb_rank_num].reward_item

					nb_rank_num = nb_rank_num + 1
					break
				else
					for i = nb_rank_num, rank_data.rank + 1 do
						rank_data_list[i] = {}
						rank_data_list[i].rank_index = i
						rank_data_list[i].user_name = Language.Common.Undetermined
						rank_data_list[i].rank_value = ""
						rank_data_list[i].reward_item = cfg[i].reward_item
					end
					nb_rank_num = rank_data.rank + 1 + 1
				end
				up = up + 1
			end
		end

		if rank_data_list[nb_rank_num - 1].user_name == RoleWGData.Instance.role_vo.name then
			my_rank_index = nb_rank_num - 1
		end
		if nb_rank_num >= 6 then
			break
		end
	end
	return rank_data_list, my_rank_index
end