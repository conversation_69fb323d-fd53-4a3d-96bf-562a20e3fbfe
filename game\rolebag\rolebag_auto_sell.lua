-- 一键出售
RoleBagAutoSell = RoleBagAutoSell or BaseClass(SafeBaseView)

function RoleBagAutoSell:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/rolebag_ui_prefab", "layout_auto_sell")
end

function RoleBagAutoSell:__delete()

end

function RoleBagAutoSell:ReleaseCallBack()
	if nil ~= self.bag_sell_grid then
		self.bag_sell_grid:DeleteMe()
		self.bag_sell_grid = nil
	end
	if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.Bag, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end
end

function RoleBagAutoSell:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.Common.BagFullToPartOne
	self:SetSecondView(Vector2(900,613))

	self.auto_sell_list = {}
	self:CreateRoleBagAutoSell()
	XUI.AddClickEventListener(self.node_list["btn_sell_item"], BindTool.Bind1(self.OnClickAutoSellItem, self))

	-- local data = SettingWGData.Instance:GetDataByIndex(HOT_KEY.GUAJI_SETTING)
	-- if data then
	-- 	self.setting_sell_flag = bit:d2b(data)
	-- 	self.node_list["img_hook"]:SetActive(1 == self.setting_sell_flag[33 - GUAJI_SETTING_TYPE.GUAJI_SCALE])
	-- endGet
	local flag = SettingWGData.Instance:GetSettingData(SETTING_TYPE.GUAJI_SCALE)
	self.node_list["img_hook"]:SetActive(flag)
	XUI.AddClickEventListener(self.node_list["layout_hook"], BindTool.Bind1(self.OnClickSettingSell, self))
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.Bag, self.get_guide_ui_event)
end

function RoleBagAutoSell:ShowIndexCallBack(index)
	self:Flush()
end

function RoleBagAutoSell:CloseCallBack()
-- 	if 0 ~= #self.setting_sell_flag then
-- 		local data = bit:b2d(self.setting_sell_flag)
-- 		SettingWGCtrl.Instance:SendChangeHotkeyReq(HOT_KEY.GUAJI_SETTING, data)
-- 	end
end

function RoleBagAutoSell:CreateRoleBagAutoSell()
	self.bag_sell_grid = AsyncBaseGrid.New()
	self.bag_sell_grid:CreateCells({col = 8, change_cells_num = 1, list_view = self.node_list["ph_sell_grid"],itemRender = AuToItemCell})
	self.bag_sell_grid:SetSelectCallBack(BindTool.Bind1(self.SelectSellGridCallBack, self))
	self.bag_sell_grid:SetStartZeroIndex(true)
end

function RoleBagAutoSell:OnFlush()
	if self.bag_sell_grid then
		self.auto_sell_list = RoleBagWGData.Instance:GetAutoSellGrid()
		self.bag_sell_grid:SetDataList(self.auto_sell_list, 2)
	end
	 self:FlushZhuanBeiCoin()
end

-- 点击格子回调
function RoleBagAutoSell:SelectSellGridCallBack(cell)
	--self.is_showtip = true   是否显示物品提示

	if not cell
		or not cell:GetData() then return end

	local data = cell:GetData()
	BagWGCtrl.Instance:SendDiscardItem(data.index, data.num, data.item_id, data.num, 0)

	self:Flush()
end

function RoleBagAutoSell:FlushZhuanBeiCoin()
	-- local num = 0
	-- -- local sell_list = self.bag_sell_grid:GetAllCell()
	-- for k,v in pairs(self.bag_sell_grid.data_list) do
	-- 	if data then
	-- 		local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	-- 		if item_cfg then
	--   			num = num + item_cfg.recyclget
	--     	end
	--     end
	-- end
	-- self.node_list["lbl_coin"].text.text = CommonDataManager.ConverMoney(num)

	local num = 0
	for k,v in pairs(self.auto_sell_list) do
		if v then
			local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
			if item_cfg then
	  			num = num + item_cfg.sellprice
	    	end
	    end
	end
	self.node_list.lbl_coin.text.text = CommonDataManager.ConverMoney(num)

end

function RoleBagAutoSell:SettingView()
	FunOpen.Instance:OpenViewByName(GuideModuleName.Setting)
end

function RoleBagAutoSell:OnClickSettingSell()
	local flag = not self.node_list["img_hook"]:GetActive()
	self.node_list["img_hook"]:SetActive(flag)
	-- self.setting_sell_flag[33 - GUAJI_SETTING_TYPE.GUAJI_SCALE] = flag and 1 or 0
	-- local data = bit:b2d(self.setting_sell_flag)
	SettingWGData.Instance:SetSettingData1(SETTING_TYPE.GUAJI_SCALE, flag, true)
	GlobalEventSystem:Fire(SettingEventType.GUAJI_SETTING_CHANGE, GUAJI_SETTING_TYPE.GUAJI_SCALE, flag)
end

-- 点击一键出售
function RoleBagAutoSell:OnClickAutoSellItem()
	-- local sell_list = self.bag_sell_grid:GetDataList()
	-- for k,v in pairs(sell_list) do
	-- 	-- local data = v:GetData()
	-- 	if v then
	-- 		BagWGCtrl.Instance:SendDiscardItem(v.index, v.num, v.item_id, v.num, 0)
	--     end
	-- end
	BagWGCtrl.Instance:RoleBagAutoSell()

	self:Close()
end

function RoleBagAutoSell:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.BagOneKeySellConfirm and self:IsOpen() and self.node_list.btn_sell_item then
		return self.node_list.btn_sell_item, BindTool.Bind1(self.OnClickAutoSellItem,self)
	elseif ui_name == GuideUIName.CloseBtn and self.node_list.btn_close_window then
		return self.node_list.btn_close_window, BindTool.Bind1(self.OnCloseHandler, self)
	end
	return nil, nil
end

--------------------------BagAuToItemCell-------------------
BagAuToItemCell = BagAuToItemCell or BaseClass(BaseGridRender)
BagAuToItemCell.BAG_SHOW_LIST = 8   --背包列数

function BagAuToItemCell:__init()
end

function BagAuToItemCell:__delete()
	if self.cell_tab then
		for k,v in pairs(self.cell_tab) do
			v:DeleteMe()
		end
	end
	self.cell_tab = {}
end

function BagAuToItemCell:LoadCallBack()
	self.cell_tab = {}
	for i = 1, BagAuToItemCell.BAG_SHOW_LIST do
		self.cell_tab[i] = AuToItemCell.New(self.node_list["ItemCell" .. i])
		self.cell_tab[i]:SetClickCallBack(BindTool.Bind(self.ClickCell, self, i))
		self.cell_tab[i]:SetIsShowTips(false)
		-- self.cell_tab[i]:SetItemTipFrom(ItemTip.FROM_BAG)
	end
end

function BagAuToItemCell:OnFlush()
	if nil == self.data then return end
	for i = 1, BagAuToItemCell.BAG_SHOW_LIST  do
		if self.data[i] and self.cell_tab[i] then
			self.cell_tab[i]:SetData(self.data[i])
		end
	end
end

function BagAuToItemCell:ClickCell(i)
	if self.grid_call_back then
	--	self.grid_call_back(self, i)
	end
end

-- function BagAuToItemCell:ClickCell(index)
-- 	if not self.data[index] then return end
	-- BaseGridRender.GridItemOnCLick(self, index)
	-- BaseGridRender.IsSelect(self, index)

-- end


function BagAuToItemCell:ClearAllDatas()
	for i = 1, BagAuToItemCell.BAG_SHOW_LIST do
		self.cell_tab[i]:ClearAllParts()
	end
end

AuToItemCell = AuToItemCell or BaseClass(ItemCell)

function AuToItemCell:__init()
	self:Nodes("select_effect").image.enabled = false
end

function AuToItemCell:OnClick()
	if self.data then
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		if nil == item_cfg then return end
	end
	BaseRender.OnClick(self)
end
