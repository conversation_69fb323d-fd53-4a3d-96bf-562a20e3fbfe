CrossYangLongSiSceneLogic = CrossYangLongSiSceneLogic or BaseClass(CommonFbLogic)

function CrossYangLongSiSceneLogic:__init()

end

function CrossYangLongSiSceneLogic:__delete()
end

function CrossYangLongSiSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

	self.lm_scene_enter_complete = true
	self.update_elaspe_time = 0
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(false)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)
		MainuiWGCtrl.Instance:SetTaskContents(false)
		-- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(false)
		MainuiWGCtrl.Instance:SetShowTimeTextState(false)

		YangLongSiWGCtrl.Instance:OpenTaskView()
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end)

	local view = YangLongSiWGCtrl.Instance:GetTaskView()
	ViewManager.Instance:AddMainUIFuPingChangeList(view)
end

function CrossYangLongSiSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)

	self.lm_scene_enter_complete = false

	MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetOtherContents(false)
	-- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(true)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)

	YangLongSiWGCtrl.Instance:CloseTaskView()
	local view = YangLongSiWGCtrl.Instance:GetTaskView()
	ViewManager.Instance:RemoveMainUIFuPingChangeList(view)
end

-- function CrossYangLongSiSceneLogic:IsRoleEnemy(target_obj, main_role)
-- 	local my_jieyi_id = SwornWGData.Instance:GetMyJieYiId()
-- 	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
-- 	local target_obj_vo = target_obj:GetVo()

-- 	if target_obj_vo.role_id == main_role_vo.role_id then
-- 		return false
-- 	end

-- 	if my_jieyi_id > 0 and target_obj_vo.special_param > 0 and my_jieyi_id == target_obj_vo.special_param then
-- 		return false
-- 	end

-- 	return CommonFbLogic.IsRoleEnemy(self, target_obj, main_role)
-- end

function CrossYangLongSiSceneLogic:Update(now_time, elapse_time)
	if not self.lm_scene_enter_complete then
		return
	end

	BaseSceneLogic.Update(self, now_time, elapse_time)

	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil and main_role:IsDeleted() then
		return
	end

	if now_time > self.update_elaspe_time then
		if GuajiCache.guaji_type == GuajiType.Auto then
			--拾取与反击冲突 策划希望全拾取 
			local obj_list = Scene.Instance:GetObjListByType(SceneObjType.FallItem)
			if not IsEmptyTable(obj_list) then
				local obj_id_list = {}

				-- local is_pb_shouhu = Scene.Instance:GetCurFbSceneCfg().pb_shouhu
    			-- local can_auto = EquipWGData.Instance:CanAutoPick(is_pb_shouhu == 1)
				-- local x, y = main_role:GetLogicPos()
				local server_time = TimeWGCtrl.Instance:GetServerTime()
	
				for k, v in pairs(obj_list)do
					if v:GetVo().owner_role_id <= 0 or v:GetVo().owner_role_id == main_role:GetRoleId() or (v:GetVo().lose_owner_time > 0 and v:GetVo().lose_owner_time <= server_time) then
						table.insert(obj_id_list, v:GetObjId())
					end
				end
	
				if not IsEmptyTable(obj_id_list) then
					self.update_elaspe_time = now_time + 2
					GlobalTimerQuest:AddTimesTimer(function ()
						Scene.ScenePickItem(obj_id_list)
					end, 1, 1)
				end
			end
		end
	end
end

	--获取附近采集物
-- 	local obj_list = Scene.Instance:GetObjListByType(SceneObjType.FallItem)
-- 	if IsEmptyTable(obj_list) then
-- 		return
-- 	end

-- 	for k, v in pairs(obj_list) do
-- 		GuajiWGCtrl.Instance:OnSelectObj(v, SceneTargetSelectType.SCENE)
-- 		return
-- 	end
-- end
-- end