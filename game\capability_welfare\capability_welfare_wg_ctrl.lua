require("game/capability_welfare/capability_welfare_view")
require("game/capability_welfare/capability_welfare_wg_data")

-- 战力福利
CapabilityWelfareWGCtrl = CapabilityWelfareWGCtrl or BaseClass(BaseWGCtrl)

function CapabilityWelfareWGCtrl:__init()
	if CapabilityWelfareWGCtrl.Instance ~= nil then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[CapabilityWelfareWGCtrl] attempt to create singleton twice!")
		return
	end
	CapabilityWelfareWGCtrl.Instance = self

	self.data = CapabilityWelfareWGData.New()
	self.view = CapabilityWelfareView.New(GuideModuleName.CapabilityWelfare)

	self:RegisterAllProtocols()

	-- 天数改变
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.CheckActicityOpen, self))
end

function CapabilityWelfareWGCtrl:__delete()
	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	CapabilityWelfareWGCtrl.Instance = nil
end

function CapabilityWelfareWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCCapabilityWealAllInfo, "OnSCCapabilityWealAllInfo")
	self:RegisterProtocol(CSCapabilityWealFetchReward)
end

-- 领取任务奖励
function CapabilityWelfareWGCtrl:GetRewardReq(seq)
	self:SendDragonTrialOperate(CAPABILITY_WEAL_OPER_TYPE.CAPABILITY_WEAL_OPER_TYPE_TASK_REWARD, seq)
end

-- 领取最终奖励
function CapabilityWelfareWGCtrl:GetFinalRewardReq()
	self:SendDragonTrialOperate(CAPABILITY_WEAL_OPER_TYPE.CAPABILITY_WEAL_OPER_TYPE_FINAL_REWARD)
end

-- 战力福利操作请求
function CapabilityWelfareWGCtrl:SendDragonTrialOperate(opera_type, param1, param2, param3)
	-- print_error("FFFF=====战力福利请求", opera_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCapabilityWealFetchReward)
	protocol.opera_type = opera_type or 0
	protocol.param_1 = param1 or 0
	protocol.param_2 = param2 or 0
	protocol.param_3 = param3 or 0
	protocol:EncodeAndSend()
end

-- 战力福利信息
function CapabilityWelfareWGCtrl:OnSCCapabilityWealAllInfo(protocol)
	-- print_error("战力福利信息", protocol)
	self.data:SetCapabilityWelfareAllInfo(protocol)
	self:CheckActicityOpen()
	ViewManager.Instance:FlushView(GuideModuleName.CapabilityWelfare)
	--MainuiWGCtrl.Instance:FlushTaskTopPanel()
	RemindManager.Instance:Fire(RemindName.CapabilityWelfare)
end

-- 关闭界面
function CapabilityWelfareWGCtrl:CloseCapabilityWelfareView()
	if self.view and self.view:IsOpen() then
		self.view:Close()
	end
end

-- 检查是否开启
function CapabilityWelfareWGCtrl:CheckActicityOpen()
	local status = ACTIVITY_STATUS.CLOSE

    local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.CAPABILITY_WELFARE)
    if not IsEmptyTable(act_cfg) then
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		local close_timestamp = CapabilityWelfareWGData.Instance:GetCloseTimeStamp()
		local role_level = RoleWGData.Instance:GetAttr("level")
		local open_level = CapabilityWelfareWGData.Instance:GetOpenLevel()
		local is_all_get = CapabilityWelfareWGData.Instance:GetIsAllGetTask()
		if role_level >= open_level and server_time < close_timestamp and (not is_all_get) then
			status = ACTIVITY_STATUS.OPEN
		end
    end

    if self.act_status ~= status then
        ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.CAPABILITY_WELFARE, status)
        self.act_status = status
    end
    return status
end