#if UNITY_EDITOR
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace ProceduralLOD
{
    class ProceduralLODSettings
    {
        public const int SupportedMaximumGenerateLevel = 2;
        public const float DefaultMinimumStrength = 60.0f;
        public const float DefaultMaximumStrength = 90.0f;
    }

    [DisallowMultipleComponent]
    public class LODGenerateHelper : MonoBehaviour
    {
        public float[] reductionStrengths = new float[0];
        public float size;

        public Simplifier simplifier
        {
            get
            {
                if (m_Simplifier == null)
                    m_Simplifier = DefaultSimplifier();
                return m_Simplifier;
            }
            set => m_Simplifier = value;
        }

        [SerializeField] private Simplifier m_Simplifier;

        [Obsolete] public SimplifyDescriptor[] descriptors;
        [Obsolete] public SimplifyDescriptor descriptor;

        public static readonly string AUTO_GENERATE_PREFIXED = "Auto Generate LOD ";

        public static void BuildAll()
        {
            List<IEnumerator> taskList = new List<IEnumerator>();

            LODGenerateHelper[] helpers = FindObjectsByType<LODGenerateHelper>(FindObjectsSortMode.None);

            HashSet<string> prefabSet = new HashSet<string>();
            for (int i = 0; i < helpers.Length; i++)
            {
                LODGenerateHelper helper = helpers[i];

                if (PrefabUtility.IsAnyPrefabInstanceRoot(helper.gameObject))
                {
                    string path = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(helper.gameObject);
                    if (prefabSet.Contains(path))
                        continue;
                    else
                        prefabSet.Add(path);
                }

                taskList.Add(helper.Build_Enumerator());
            }

            CommonUtility.RunSequenceCoroutine(taskList.ToArray());
            CommonUtility.onSequenceCompleted += ResetSwitchDistanceAll;
        }

        public static void ResetSwitchDistanceAll()
        {
            LODGenerateHelper[] helpers = FindObjectsByType<LODGenerateHelper>(FindObjectsInactive.Include, FindObjectsSortMode.None);
            for (int i = 0; i < helpers.Length; i++)
            {
                LODGenerateHelper helper = helpers[i];
                helper.ResetSwitchDistance();
            }
        }

        public static void RefreshMaterialAll()
        {
            List<IEnumerator> taskList = new List<IEnumerator>();

            LODGenerateHelper[] helpers = FindObjectsByType<LODGenerateHelper>(FindObjectsSortMode.None);

            HashSet<string> prefabSet = new HashSet<string>();
            for (int i = 0; i < helpers.Length; i++)
            {
                LODGenerateHelper helper = helpers[i];

                if (PrefabUtility.IsAnyPrefabInstanceRoot(helper.gameObject))
                {
                    string path = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(helper.gameObject);
                    if (prefabSet.Contains(path))
                        continue;
                    else
                        prefabSet.Add(path);
                }

                taskList.Add(helper.MakeMaterial_Enumerator());
            }

            CommonUtility.RunSequenceCoroutine(taskList.ToArray());
        }

        public void Build()
        {
            CommonUtility.RunSequenceCoroutine(Build_Enumerator());
        }

        public void Modify(int lodIndex)
        {
            Transform child = transform.Find(AUTO_GENERATE_PREFIXED + lodIndex);
            if (child == null)
            {
                return;
            }

            float strength = reductionStrengths[lodIndex - 1];

            LODModifyHelper modifyHelper = LODModifyHelper.Modify(child.gameObject);
            modifyHelper.onCompleted += OnModifyCompleted;
            modifyHelper.targetSimplifier = simplifier;
            modifyHelper.lodIndex = lodIndex;
            modifyHelper.Setup(strength);
        }

        public void ResetSwitchDistance()
        {
            LODGroup lodGroup = this.GetComponent<LODGroup>();
            if (lodGroup == null)
            {
                return;
            }

            var lods = lodGroup.GetLODs();
            size = CommonUtility.CalculateWorldSize(lods[0].renderers);
            var bias = 3 - lods.Length;
            float lastHeight = 1f;
            try
            {
                for (int i = 0; i < lods.Length; i++)
                {
                    var lod = lods[i];
                    lod.screenRelativeTransitionHeight = DefaultHeight(size, i + bias) * 0.01f;

                    if (i > 0 && lod.screenRelativeTransitionHeight >= lastHeight)
                    {
                        Debug.LogError($"重置LOD距离失败：{lodGroup.name}", lodGroup.gameObject);
                        throw new Exception();
                    }

                    lastHeight = lod.screenRelativeTransitionHeight;
                    lods[i] = lod;
                }

                lodGroup.SetLODs(lods);
            }
            catch (Exception e)
            {
            }
        }

        public void ClearAutoLOD(bool destroyLODGroup = true)
        {
            int childCount = this.transform.childCount;
            for (int i = childCount - 1; i >= 0; i--)
            {
                var child = this.transform.GetChild(i);
                if (child.name.StartsWith(AUTO_GENERATE_PREFIXED))
                {
                    if (PrefabUtility.IsPartOfAnyPrefab(child.gameObject))
                    {
                        string file = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(child.gameObject);
                        GameObject prefab = PrefabUtility.LoadPrefabContents(file);
                        DestroyImmediate(prefab.transform.Find(child.name).gameObject);
                        PrefabUtility.SaveAsPrefabAsset(prefab, file);
                        PrefabUtility.UnloadPrefabContents(prefab);
                    }
                    else
                    {
                        DestroyImmediate(child.gameObject);
                    }
                }
            }

            if (destroyLODGroup)
            {
                LODGroup lodGroup = this.GetComponent<LODGroup>();
                if (lodGroup != null)
                {
                    DestroyImmediate(lodGroup);
                    if (PrefabUtility.IsAnyPrefabInstanceRoot(this.gameObject))
                    {
                        var objectOverrides = PrefabUtility.GetObjectOverrides(this.gameObject, false);
                        foreach (var ovr in objectOverrides)
                        {
                            if (ovr.instanceObject is LODGenerateHelper || ovr.instanceObject is LODGroup)
                                ovr.Apply();
                        }

                        var removedComponents = PrefabUtility.GetRemovedComponents(this.gameObject);
                        foreach (var ovr in removedComponents)
                        {
                            if (ovr.assetComponent is LODGenerateHelper || ovr.assetComponent is LODGroup)
                                ovr.Apply();
                        }
                    }
                }
            }
            
            foreach (var renderer in this.GetComponentsInChildren<Renderer>(true))
            {
                renderer.enabled = true;
            }

            foreach (var ovr in PrefabUtility.GetObjectOverrides(this.gameObject))
            {
                if (ovr.instanceObject is Renderer)
                    ovr.Apply();
            }

        }

        public void RefreshMaterials()
        {
            CommonUtility.RunSequenceCoroutine(MakeMaterial_Enumerator());
        }
        
        private Simplifier DefaultSimplifier()
        {
            Type[] types = Array.FindAll(typeof(Simplifier).Assembly.GetTypes(), t => t.IsSubclassOf(typeof(Simplifier)));
            Type first = types[0];

            var instance = Activator.CreateInstance(first) as Simplifier;

            //兼容旧数据
            //待旧场景完成重新生成后删除
            if (first.Name == "PolyFewSimplifier")
            {
                if (descriptors != null && descriptors.Length > 0)
                {
                    descriptor = descriptors[0];
                    descriptors = new SimplifyDescriptor[0];
                }

                if (descriptor != null)
                {
                    (instance as PolyFewSimplifier).descriptor = descriptor;
                }
            }

            return instance;
        }

        protected virtual float DefaultHeight(float size, int level)
        {
            float height;

            switch (level)
            {
                case 0:
                    height = (0.13f * size + 32f) * (1f - Mathf.Pow((float)Math.E, -0.05f * size));
                    break;
                case 1:
                    height = (0.1f * size + 14) * (1f - Mathf.Pow((float)Math.E, -0.05f * size));
                    break;
                case 2:
                    height = Mathf.Min((0.04f * size + 2.2f) * (1f - Mathf.Pow((float)Math.E, -0.7f * size)), 60);
                    break;
                default:
                    height = 0.001f;
                    break;
            }

            return height;
        }

        protected virtual int SimplifiedLevels(float size, int triangles)
        {
            if (size > 100)
            {
                if (triangles > 1000)
                {
                    return 2;
                }
                else
                {
                    return 1;
                }
            }
            else if (size > 40)
            {
                if (triangles > 1000)
                {
                    return 2;
                }
                else
                {
                    return 1;
                }
            }
            else
            {
                if (triangles > 1000)
                {
                    return 2;
                }
                else if (triangles > 300)
                {
                    return 1;
                }
                else
                {
                    return 0;
                }
            }
        }

        //todo: 做抽象封装MateriSimplifier类
        protected virtual bool SimplifyMaterial(int index, Material sourceMaterial, out Material lodMaterial)
        {
            if (LODMaterialUtility.MakeMaterialLOD(sourceMaterial, out lodMaterial))
            {
                if (index == 2)
                     lodMaterial.EnableKeyword("_BASIC_LIGHTING");
                return true;
            }

            return false;
        }

        //todo: 做抽象封装Batcher类

        #region Batch
        
        protected class LODObject
        {
            public int index = 0;
            public GameObject gameObject;
            public SimplyBatches batches;
            public Renderer[] rawRenderers;
            public bool enableCombineChildren; //合并子网格
            public bool enableHierachyBatch; //材质图集

            public Renderer[] renderers => gameObject != null ? gameObject.GetComponentsInChildren<Renderer>() : rawRenderers;
            
            public LODObject(int lodIndex, GameObject gameObject, Renderer[] rawRenderers)
            {
                this.gameObject = gameObject;
                this.rawRenderers = rawRenderers;
                this.index = lodIndex;
                this.batches = new SimplyBatches(gameObject ? gameObject.transform : null, rawRenderers);
            }
        }

       protected class SimplyBatches
        {
            public Transform parent;
            public List<BatchData> datas = new List<BatchData>();

            public Renderer[] rawRenderers
            {
                get
                {
                    var list = new List<Renderer>();
                    for (int i = 0; i < datas.Count; i++)
                    {
                        list.AddRange(datas[i].rawRenderers);
                    }

                    return list.ToArray();
                }
            }

            public SimplyBatches(Transform parent, Renderer[] renderers)
            {
                this.parent = parent;
                
                for (int i = 0; i < renderers.Length; i++)
                {
                    Renderer renderer = renderers[i];
                    Shader shader = renderer.sharedMaterial.shader;
                    BatchData data = datas.Find(data => data.shader == shader);
                
                    if (data == null)
                    {
                        data = new BatchData(shader);
                        datas.Add(data);
                    }
                    data.rawRenderers.Add(renderer);
                }
            }

            public void SetBatches(params Renderer[] renderers)
            {
                datas.Clear();
                for (int i = 0; i < renderers.Length; i++)
                {
                    Renderer renderer = renderers[i];
                    BatchData billboardBatch = new BatchData(renderer.sharedMaterial.shader);
                    billboardBatch.gameObject = renderer.gameObject;
                    billboardBatch.rawRenderers.Add(renderer);
                    datas.Add(billboardBatch);
                }
            }

            public void Expand()
            {
                for (int i = 0; i < datas.Count; i++)
                {
                    datas[i].Expand(this.parent);
                }
            }
        }

        protected class BatchData
        {
            public GameObject gameObject;
            public Shader shader;
            public List<Renderer> rawRenderers = new List<Renderer>();

            public Renderer[] renderers => gameObject != null ? gameObject.GetComponentsInChildren<Renderer>() : rawRenderers.ToArray();
            public BatchData(Shader shader)
            {
                this.shader = shader;
            }
            
            public void Expand(Transform parent)
            {
                
                //if (renderers.Count > 1)
                {
                    GameObject batchObj = new GameObject("");
                    batchObj.transform.SetParent(parent);
                    batchObj.transform.localPosition = Vector3.zero;
                    batchObj.transform.localRotation = Quaternion.identity;
                    batchObj.transform.localScale = Vector3.one;
                    this.gameObject = batchObj;
                }
                
                for (int i = 0; i < rawRenderers.Count; i++)
                {
                    Renderer rawRenderer = rawRenderers[i];
                    GameObject clone = new GameObject(rawRenderer.name);
                    if (rawRenderer is MeshRenderer mr)
                    {
                        MeshFilter clonemf = clone.AddComponent<MeshFilter>();
                        clonemf.sharedMesh = rawRenderer.GetComponent<MeshFilter>().sharedMesh;
                        MeshRenderer clonemr = clone.AddComponent<MeshRenderer>();
                        clonemr.sharedMaterials = rawRenderer.sharedMaterials;
                    }
                    else if (rawRenderer is SkinnedMeshRenderer smr)
                    {
                        SkinnedMeshRenderer clonesmr = clone.AddComponent<SkinnedMeshRenderer>();
                        clonesmr.sharedMesh = smr.sharedMesh;
                        clonesmr.sharedMaterials = rawRenderer.sharedMaterials;
                    }

                    clone.transform.SetParent(this.gameObject.transform);
                    clone.transform.position = rawRenderer.transform.position;
                    clone.transform.rotation = rawRenderer.transform.rotation;
                    clone.transform.localScale = new Vector3(
                        rawRenderer.transform.lossyScale.x / parent.transform.lossyScale.x,
                        rawRenderer.transform.lossyScale.y / parent.transform.lossyScale.y,
                        rawRenderer.transform.lossyScale.z / parent.transform.lossyScale.z);

                    clone.name = rawRenderer.name;
                }
            }
        }
        
        protected virtual LODObject PrepareLODObject(int lodIndex, GameObject gameObject, Renderer[] rawRenderers)
        {
            LODObject lodObject = new LODObject(lodIndex, gameObject, rawRenderers)
            {
                enableCombineChildren = gameObject != null && rawRenderers.Length > 1,
                //enableHierachyBatch = lodIndex > 1
            };
            if (gameObject != null)
                lodObject.batches.Expand();

            return lodObject;
        }

        protected virtual IEnumerator SimplifyChidren(LODObject lodObject)
        {
            for (int k = 0; k < lodObject.batches.datas.Count; k++)
            {
                var batch = lodObject.batches.datas[k];

                if (batch.gameObject == null)
                {
                    Debug.LogError(lodObject.gameObject, lodObject.gameObject);
                    continue;
                }
                
                yield return simplifier.Simplify(batch.gameObject, reductionStrengths[lodObject.index - 1]);
            }
          
            yield return null;
        }

        #endregion
        
        private IEnumerator Build_Enumerator()
        {
            EditorUtility.DisplayProgressBar("生成 LOD", "清除环境", 0.1F);

            ClearAutoLOD();

            yield return new WaitForEndOfFrame();

            EditorUtility.DisplayProgressBar("生成 LOD", "生成中..", 0.3F);

            Renderer[] rawRenderers = this.GetComponentsInChildren<Renderer>(false);

            size = CommonUtility.CalculateLocalSize(rawRenderers);

            MeshFilter[] filters = rawRenderers.Select(r => r.GetComponent<MeshFilter>()).ToArray();
            int triangles = 0;
            for (int i = 0; i < filters.Length; i++)
            {
                MeshFilter filter = filters[i];
                if (filter.sharedMesh != null)
                    triangles += filter.sharedMesh.triangles.Length / 3;
            }

            int generatedLODLevel = SimplifiedLevels(size, triangles);
            generatedLODLevel = Mathf.Min(generatedLODLevel, ProceduralLODSettings.SupportedMaximumGenerateLevel);
            
            string prefabName = Path.GetFileNameWithoutExtension(PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(this));
            
            if (generatedLODLevel != reductionStrengths.Length)
            {
                float[] newArray = new float[generatedLODLevel];
                float firstStrength = reductionStrengths.Length > 0 ? reductionStrengths[^1] : ProceduralLODSettings.DefaultMinimumStrength;
                for (int i = 0; i < newArray.Length; i++)
                {
                    newArray[i] = i < reductionStrengths.Length ? reductionStrengths[i] : Mathf.Lerp(firstStrength, ProceduralLODSettings.DefaultMaximumStrength, (float)i / (ProceduralLODSettings.SupportedMaximumGenerateLevel - 1));
                }

                reductionStrengths = newArray;
            }

            LODGroup lodGroup = this.GetComponent<LODGroup>();
            LOD[] lods = new LOD[generatedLODLevel + 1];
            if (lodGroup == null)
            {
                lodGroup = this.gameObject.AddComponent<LODGroup>();
            }

            float maximumLightmapScale = 0;
            for (int i = 0; i < rawRenderers.Length; i++)
            {
                if (rawRenderers[i] is MeshRenderer mr)
                    maximumLightmapScale = Mathf.Max(mr.scaleInLightmap, maximumLightmapScale);
            }
            
            for (int i = 0; i < lods.Length; i++)
            {
                GameObject obj = null;

                if (rawRenderers.Length > 1 || i > 0)
                {
                    obj = new GameObject(AUTO_GENERATE_PREFIXED + i);
                    obj.transform.SetParent(this.transform);
                    obj.transform.localPosition = Vector3.zero;
                    obj.transform.localScale = Vector3.one;
                    obj.transform.localRotation = Quaternion.identity;
                }

                LODObject lodObject = PrepareLODObject(i, obj, rawRenderers);

                bool postCombineChildren = false;
                
                if (lodObject.enableCombineChildren && !postCombineChildren)
                {
                    try
                    {
                        MeshRenderer combinedRenderer = MeshCombiner.Build(lodObject.gameObject);
                        combinedRenderer.name = prefabName;
                        CommonUtility.GetMeshByRenderer(combinedRenderer).name = prefabName;
                        lodObject.batches.SetBatches(combinedRenderer);
                    }
                    catch (Exception e)
                    {
                        Debug.LogError("Mesh Combine Error: " + this.name, this);
                        Debug.LogError(e);
                    }
                }
                
                if (i > 0)
                {
                    Mesh[] rawMeshes = lodObject.renderers.Select(r => CommonUtility.GetMeshByRenderer(r)).ToArray();
                    yield return SimplifyChidren(lodObject);
                    Mesh[] meshes = lodObject.renderers.Select(r => CommonUtility.GetMeshByRenderer(r)).ToArray();
                    
                    for (int n = 0; n < rawMeshes.Length; n++)
                    {
                        if (rawMeshes[n] == null)
                            continue;
                        
                        if(!string.IsNullOrEmpty(AssetDatabase.GetAssetPath(meshes[n])))
                            continue;
                        
                        meshes[n].name = rawMeshes[n].name;
                        
                        string rawMeshFile = AssetDatabase.GetAssetPath(rawMeshes[n]);
                        if (rawMeshFile.ToLower().EndsWith(".fbx"))
                        {
                            meshes[n].name = Path.GetFileNameWithoutExtension(rawMeshFile) + "/" + meshes[n].name;
                        }
                    }
                    
                }
                
                for (int k = 0; k < lodObject.batches.datas.Count; k++)
                {
                    BatchData batch = lodObject.batches.datas[k];
                    List<Material> materials = new List<Material>();
                    foreach (var renderer in batch.renderers)
                    {
                        materials.AddRange(renderer.sharedMaterials);
                    }

                    if (lodObject.enableHierachyBatch && HierarchyBatcher.IsSupported(materials.ToArray()))
                    {
                        GameObject batchRoot;
                        string meshName;
                        if (lodObject.batches.datas.Count > 1)
                        {
                            batchRoot = batch.gameObject;
                            meshName = prefabName + "_Batch" + k;
                        }
                        else
                        {
                            batchRoot = lodObject.gameObject;
                            meshName = prefabName;
                        }

                        yield return HierarchyBatcher.BuildBatch(batchRoot);
                        Mesh mesh = CommonUtility.GetMeshByRenderer(batchRoot.GetComponentInChildren<Renderer>());
                        mesh.name = meshName;
                    }
                }
                
                if (lodObject.enableCombineChildren && postCombineChildren)
                {
                    try
                    {
                        MeshRenderer combinedRenderer = MeshCombiner.Build(lodObject.gameObject);
                        combinedRenderer.name = prefabName;
                        CommonUtility.GetMeshByRenderer(combinedRenderer).name = prefabName;
                    }
                    catch (Exception e)
                    {
                        Debug.LogError("Mesh Combine Error: " + this.name, this);
                        Debug.LogError(e);
                    }
                }

                Renderer[] renderers = lodObject != null && lodObject.renderers != null ? lodObject.renderers : new Renderer[0];

                //LOD0 设置静态和scale in lightmap
                if (i == 0)
                {
                    for (int k = 0; k < renderers.Length; k++)
                    {
                        if (renderers[k] is MeshRenderer mr)
                        {
                            StaticEditorFlags flags = GameObjectUtility.GetStaticEditorFlags(mr.gameObject);
                            GameObjectUtility.SetStaticEditorFlags(mr.gameObject, flags | StaticEditorFlags.ContributeGI);  
                            mr.scaleInLightmap = maximumLightmapScale;
                        }
                    }
                }
                
                lods[i] = new LOD()
                {
                    renderers = renderers,
                    screenRelativeTransitionHeight = Mathf.Lerp(1, 0.01F, (float)i / generatedLODLevel)
                };
            }

            if (rawRenderers.Length > 1)
            {
                for (int i = 0; i < rawRenderers.Length; i++)
                {
                    rawRenderers[i].enabled = false;
                }
            }
            
            lodGroup.SetLODs(lods);
            lodGroup.RecalculateBounds();

            EditorUtility.DisplayProgressBar("生成 LOD", "生成LOD网格成功", 0.6F);

            ResetSwitchDistance();

            yield return null;

            EditorUtility.DisplayProgressBar("生成 LOD", "生成LOD材质..", 0.8F);
                
            yield return MakeMaterial_Enumerator();
            
            yield return null;

            CommonUtility.SaveSimplifierAssets(this.gameObject);

            yield return null;

            EditorUtility.DisplayProgressBar("生成 LOD", "保存到预制体..", 0.9F);
            
            EditorUtility.SetDirty(this);
            
            yield return null;
            
            if (PrefabUtility.IsAnyPrefabInstanceRoot(this.gameObject))
            {
                var addedGameObjects = PrefabUtility.GetAddedGameObjects(this.gameObject);
                foreach (var ovr in addedGameObjects)
                {
                    if (ovr.instanceGameObject.name.StartsWith("Auto Generate LOD"))
                        ovr.Apply();
                }
            
                var addedComponents = PrefabUtility.GetAddedComponents(this.gameObject);
                foreach (var ovr in addedComponents)
                {
                    if (ovr.instanceComponent is LODGenerateHelper || ovr.instanceComponent is LODGroup)
                        ovr.Apply();
                }
            
                var objectOverrides = PrefabUtility.GetObjectOverrides(this.gameObject, false);
                foreach (var ovr in objectOverrides)
                {
                    if (ovr.instanceObject is LODGenerateHelper || ovr.instanceObject is LODGroup || ovr.instanceObject is Renderer)
                        ovr.Apply();
                }
            }
            
            Debug.Log($"生成LOD完成：{this.gameObject.name}", this.gameObject);

            EditorUtility.ClearProgressBar();
        }

        private IEnumerator MakeMaterial_Enumerator()
        {
            yield return null;

            try
            {
                LODGroup lodGroup = this.GetComponent<LODGroup>();
                if (lodGroup != null)
                {
                    LOD[] lods = lodGroup.GetLODs();
                    LOD lod0 = lods[0];
                    
                    for (int k = 1; k <= ProceduralLODSettings.SupportedMaximumGenerateLevel; k++)
                    {
                        if (k >= lods.Length)
                            continue;

                        LOD lodi = lods[k];
                        foreach (var lodRenderer in lodi.renderers)
                        {
                            Material[] materials = lodRenderer.sharedMaterials;
                            for (int i = 0; i < materials.Length; i++)
                            {
                                if(SimplifyMaterial(k, materials[i], out var lodMaterial))
                                {
                                    materials[i] = lodMaterial;
                                }
                            }

                            lodRenderer.sharedMaterials = materials;
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }
        
        private IEnumerator OnModifyCompleted(ModifyResult result)
        {
            yield return null;

            reductionStrengths[result.lod - 1] = result.strength;
             EditorUtility.SetDirty(this);
            foreach (var o in PrefabUtility.GetObjectOverrides(this.gameObject))
            {
                if (o.instanceObject is LODGenerateHelper)
                    o.Apply();
            }
            
            yield return null;
            
            Build();
        }
    }
}

#endif