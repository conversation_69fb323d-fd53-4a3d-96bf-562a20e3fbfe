require("game/task/task_follow_render")

--------------------------------------------------------------
--任务跟踪
--------------------------------------------------------------
ZhuXieFollow = ZhuXieFollow or BaseClass(SafeBaseView)

ZhuXieFollow.Task_Type = {
	Gather = 1,
	Monster = 2,
	Role = 3
}

local TaskType_Role = 5

ZhuXieFollow.AutoAcceptTaskIndex = -1

function ZhuXieFollow:__init()
	self:AddViewResource(0, "uis/view/zhuxie_ui_prefab", "ZhuxiePanel")
	self.view_layer = UiLayer.MainUI
	self.act_state = nil
end

function ZhuXieFollow:ReleaseCallBack()
	ActivityWGCtrl.Instance:RemberCurZhuXieID(nil)
	if self.task_list_view then
		self.task_list_view:DeleteMe()
		self.task_list_view = nil
	end

	if self.boss_list_view then
		self.boss_list_view:DeleteMe()
		self.boss_list_view = nil
	end

	self.task_btn_click_callback = nil
    self.boss_btn_click_callback = nil

    self.task_id = nil
    if self.boss_alert then
		self.boss_alert:DeleteMe()
		self.boss_alert = nil
	end
	if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
		self.activity_change_callback = nil
	end

	if self.be_select_event then
		GlobalEventSystem:UnBind(self.be_select_event)
		self.be_select_event = nil
	end

	if self.arrow_click_event then
		GlobalEventSystem:UnBind(self.arrow_click_event)
		self.arrow_click_event = nil
	end

	if self.touch_move_event then
		GlobalEventSystem:UnBind(self.touch_move_event)
		self.touch_move_event = nil
	end

	if self.role_click_move_event then
		GlobalEventSystem:UnBind(self.role_click_move_event)
		self.role_click_move_event = nil
	end

	-- self:ClearCD()
	if CountDownManager.Instance:HasCountDown(self.countdown_str) then
		CountDownManager.Instance:RemoveCountDown(self.countdown_str)
	end

	if CountDownManager.Instance:HasCountDown(self.zhuxie_act_countdown) then
		CountDownManager.Instance:RemoveCountDown(self.zhuxie_act_countdown)
	end
	self:DeleteBossStone()
	self.is_load_succe = nil
	self.monster_cfg = nil
	self.is_show_one = nil
	self.is_show_two = nil

	if self.boss_reward_list then
		for k,v in pairs(self.boss_reward_list) do
			v:DeleteMe()
		end
		self.boss_reward_list = nil
	end

	if self.rank_reward_list then
		for k,v in pairs(self.rank_reward_list) do
			v:DeleteMe()
		end
		self.rank_reward_list = nil
	end

	if self.hurt_list_view then
		self.hurt_list_view:DeleteMe()
		self.hurt_list_view = nil
	end

	if self.animator_paly then
		GlobalTimerQuest:CancelQuest(self.animator_paly)
		self.animator_paly = nil
	end

	if self.zx_tween_sequence then
		self.zx_tween_sequence:Kill()
		self.zx_tween_sequence = nil
	end

	self.is_auto = nil
	self.act_state = nil
	self.boss_head_cache = nil
end

function ZhuXieFollow:LoadCallBack()
	-- self.cell_list = {}
	self.is_first_enter = true
	self.cur_endtime = 0
	self.task_data_list = {}
	self.is_show_tip_1 = false
	self.is_show_tip_2 = false
	self.is_show_tip_3 = false
	XUI.AddClickEventListener(self.node_list["TaskButton"],BindTool.Bind(self.OnClickTask, self))
    XUI.AddClickEventListener(self.node_list["BossButton"],BindTool.Bind(self.OnClickBoss, self))
    -- XUI.AddClickEventListener(self.node_list["ShrinkButtons"],BindTool.Bind(self.OnShrinkToggleChange, self))
    -- self.node_list.btn_go_boss.button:AddClickListener(BindTool.Bind(self.OnClick, self))--去攻击boos
    self.node_list.Boss_Btn.button:AddClickListener(BindTool.Bind(self.OnClick, self))--去攻击boos
    self.node_list["TargetHp"].button:AddClickListener(BindTool.Bind(self.OnClick, self))
    self.node_list["flush_img"].button:AddClickListener(BindTool.Bind(self.OnClick, self))
    -- self.node_list["Btn_item_show"].button:AddClickListener(BindTool.Bind(self.OnClickOpenReward, self))
	self.task_list_view = AsyncListView.New(ZhuXieFollowTask,self.node_list.TaskList)
	self.task_list_view:SetSelectCallBack(BindTool.Bind(self.TaskCellCallBack,self))
	self.hurt_list_view = AsyncListView.New(ZhuXieHurtRankCell,self.node_list.rank_list_view)

	self.arrow_click_event = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind1(self.TopPanelAni, self))
    self.touch_move_event = GlobalEventSystem:Bind(LayerEventType.TOUCH_MOVED, BindTool.Bind(self.StopAutoTask, self))
    self.role_click_move_event = GlobalEventSystem:Bind(LayerEventType.SCENE_CLICK_FLOOR, BindTool.Bind(self.StopAutoTask, self))
	self.be_select_event = GlobalEventSystem:Bind(ObjectEventType.BE_SELECT, BindTool.Bind(self.OnSelectObjHead, self))

	self.node_list["flush_flag"]:SetActive(false)
	self.node_list.effect:SetActive(false)
	self.is_load_succe = true
	if self.auto_task_timer then
		GlobalTimerQuest:CancelQuest(self.auto_task_timer)
		self.auto_task_timer = nil
	end
	self.task_id = 0
	local other_cfg = ConfigManager.Instance:GetAutoConfig("zhuxieconfig_auto").other[1]
	self.max_pos_x = other_cfg.pos_max_x
	self.min_pos_x = other_cfg.pos_min_x
	self.max_pos_y = other_cfg.pos_max_y
	self.min_pos_y = other_cfg.pos_min_y
	if not self.activity_change_callback then
		self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack,self)
		ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)
	end
	self:FlushZhuXieFollowView()
	self:FlushBossInfo()
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
end

--被选中的对象
function ZhuXieFollow:OnSelectObjHead(target_obj, select_type)
	local list = ActivityWGData.Instance:GetZhuXieTaskList()
	if target_obj:GetType() == SceneObjType.GatherObj then
		ActivityWGCtrl.Instance:RemberCurZhuXieID(1)
		ActivityWGCtrl.Instance:ZhuXieFollowViewFlush(0, "select_change")
	elseif target_obj:GetType() == SceneObjType.Monster then
		for k,v in pairs(list) do
			if v.cfg.param_id == target_obj.vo.monster_id then
				ActivityWGCtrl.Instance:RemberCurZhuXieID(v.cfg.task_id)
				ActivityWGCtrl.Instance:ZhuXieFollowViewFlush(0, "select_change")
			end
		end
	elseif target_obj:GetType() == SceneObjType.Role then
		ActivityWGCtrl.Instance:RemberCurZhuXieID(5)
		ActivityWGCtrl.Instance:ZhuXieFollowViewFlush(0, "select_change")
	end
end

function ZhuXieFollow:StopAutoTask()
	ActivityWGCtrl.Instance:RemberCurZhuXieID(nil)
	ActivityWGCtrl.Instance:ZhuXieFollowViewFlush(0, "select_change")
end

function ZhuXieFollow:ClickMoveStopAutoTask(state)
	if state == XunLuStatus.None then
		self:StopAutoTask()
	end
end


function ZhuXieFollow:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "select_change" then
			local all_item = self.task_list_view:GetAllItems()
			for k,v in pairs(all_item) do
				v:AutoChange()
			end
		end
	end
end

function ZhuXieFollow:TopPanelAni( ison )
    if nil == self.node_list.right_parent then
        return
    end
    local max_move ,min_move = 351 , 61
    local move_vaule = ison == true and max_move or min_move
    local tween = self.node_list.right_parent.rect:DOAnchorPosX(move_vaule, 1)
    tween:SetEase(DG.Tweening.Ease.OutBack)
    tween:OnUpdate(function()
        self.node_list.right_parent.canvas_group.alpha = ((max_move - min_move) - (self.node_list.right_parent.rect.anchoredPosition.x - min_move)) / (max_move - min_move)
    end)
end

function ZhuXieFollow:ActivityChangeCallBack(activity_type, status, next_time, open_type)

end


function ZhuXieFollow:ZhuXieAnimation()
	--右边动画
	local right_animator = self.node_list["right_parent"].rect:DOAnchorPosX(61, 1.52)
	right_animator:SetEase(DG.Tweening.Ease.OutBack)
end

--boss刷新动画
function ZhuXieFollow:BossFlushAnimation()
	self.node_list["Boss_head"].transform.anchoredPosition = Vector2(-590,12.9)
	self.node_list["Boss_head"].transform.localScale = Vector3(2,2,2)
	local bossfluah_animator = self.node_list["Boss_head"].rect:DOScale(Vector3(1, 1, 1), 1)

	if self.zx_tween_sequence then
		self.zx_tween_sequence:Kill()
		self.zx_tween_sequence = nil
	end

	local sequence = DG.Tweening.DOTween.Sequence()
	sequence:Join(bossfluah_animator)
	self.animator_paly = GlobalTimerQuest:AddDelayTimer(function ()
		local boss_head_move_animator = self.node_list["Boss_head"].rect:DOAnchorPosX(0, 1)
		boss_head_move_animator:SetEase(DG.Tweening.Ease.Linear)
		self:FlushActivityCountDown()
	end, 1.5)

	self.zx_tween_sequence = sequence
end

function ZhuXieFollow:DestroyTaskView()

	if self.btn_right ~= nil then
		self.btn_right:removeFromParent()
	end
	self.btn_right = nil
	self.max_pos_x = nil
	self.min_pos_x = nil
	self.max_pos_y = nil
	self.min_pos_y = nil
	self:DeleteMe()
end

function ZhuXieFollow:OnClickTask()
	if self.task_btn_click_callback then
        self.task_btn_click_callback()
    end
end

function ZhuXieFollow:OnClickBoss()
	if self.boss_btn_click_callback then
        self.boss_btn_click_callback()
    end
end

--任务按钮点击回调
function ZhuXieFollow:SetTaskCallBack(callback)
    self.task_btn_click_callback = callback
end

--首领按钮点击回调
function ZhuXieFollow:SetBossCallBack(callback)
    self.boss_btn_click_callback = callback
end
--播放按钮点击动画
function ZhuXieFollow:PlayTaskButtonTween(enable)
    self.node_list["ShrinkButtons"].toggle.isOn = enable
end

function ZhuXieFollow:OnClickLeft()
	self.action_timer = GlobalTimerQuest:AddDelayTimer(function()
		self.action_timer = nil
		self.btn_right:setVisible(true)
	end, 0.2)

end

function ZhuXieFollow:OnClickRight()
	self.btn_right:setVisible(false)

end

function ZhuXieFollow:OnClickOpenReward()
	-- ActivityWGCtrl.Instance:OpenZhuXieReward()
	self:OnClick()
end

--更新单个任务条
function ZhuXieFollow:UpdateOneTaskItem(task_id)
	for k,v in pairs(self.task_data_list) do
		if v.cfg.task_id == task_id and self.task_list_view then
			local task_item = self.task_list_view:GetItemAt(k)
			if task_item ~= nil then
				task_item:Flush()
			end
			break
		end
	end
end

--自动点击未完成的任务
function ZhuXieFollow:OnclcikOneTaskItem(task_id)
	local task_flag = false
	for k,v in pairs(self.task_data_list) do
		if v.cfg.task_id == task_id and self.task_list_view then
			local task_info = ActivityWGData.Instance:GetOneZhuXieTaskInfo(task_id)
			if task_info and task_info.is_fetch_reward == 0 then
				task_flag = true
				self:OnCellData(v)
				return
			end
		end
	end

	if not task_flag then
		local boss_info = ActivityWGData.Instance:GetOneZhuXieBossInfo() or {}
		local task_id = ActivityWGData.Instance:GetOneTaskID()

		if boss_info and boss_info.boss_id then
			if boss_info.boss_cur_hp <= 0 and task_id ~= nil then
				GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
				GuajiCache.monster_id = boss_info.boss_id
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Monster)
			else
				local kill_role_task = ActivityWGData.Instance:GetOneZhuXieTaskInfo(5)
				if kill_role_task and kill_role_task.is_fetch_reward == 0 then
					self:FindRoleTarget()
				end
			end
		end
	end
end

--自动选择任务
function ZhuXieFollow:XuanZeTask()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.ZHUXIE)
	if nil ~= activity_info then
		local flag = activity_info.status == ACTIVITY_STATUS.STANDY
		if flag then
			return
		end
	end
	-- 判断boss存在直接挂机
	local boss_info = ActivityWGData.Instance:GetOneZhuXieBossInfo() or {}
	if IsEmptyTable(boss_info) then
		return
	end

	if boss_info.boss_id ~= nil and boss_info.boss_cur_hp > 0 then
		local scene_id = Scene.Instance:GetSceneId()
		local other_cfg = ConfigManager.Instance:GetAutoConfig("zhuxieconfig_auto").other[1]
		if other_cfg ~= nil then
			GuajiWGCtrl.Instance:SetMoveToPosCallBack(function()
				-- GuajiWGCtrl.Instance:StopGuaji()
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end)
			GuajiWGCtrl.Instance:MoveToPos(scene_id, other_cfg.boss_x, other_cfg.boss_y,3)
		end
		return
	end

	if self:CheckFallItem() then
		return
	end

	--判断任务
	local task_id = ActivityWGCtrl.Instance:GetCurZhuXieID()
	--类型是5攻击人物不做操作
	-- if task_id ~= nil and task_id == 5 then
	-- 	return
	-- end
	if task_id == nil then
		--获取一个没做完的任务id（不包括杀人任务）
		task_id = ActivityWGData.Instance:GetOneTaskID()
		if task_id == nil then
			--杀人任务
			local kill_role_task = ActivityWGData.Instance:GetOneZhuXieTaskInfo(5)
			if kill_role_task and kill_role_task.is_fetch_reward == 0 then
				task_id = 5
			else
				--随便给一个任务（不要给采集）
				task_id = 3
			end

		end
		self:OnclcikOneTaskItem(task_id)
		return
	else
		local task_info = ActivityWGData.Instance:GetOneZhuXieTaskInfo(task_id) or {}
		local param_value = task_info.param_value or 0
		local max_value = task_info.max_value or 0
		local is_fetch_reward = task_info.is_fetch_reward or 0
		if (is_fetch_reward == 1 and max_value <= param_value) or is_fetch_reward == 1 then
			task_id = ActivityWGData.Instance:GetOneTaskID()
			if task_id ~= nil then
				self:OnclcikOneTaskItem(task_id)
			else
				local task_id = ActivityWGCtrl.Instance:GetCurZhuXieID()
				task_id = task_id == 1 and 2 or task_id
				self:OnclcikOneTaskItem(task_id)
			end
			return
		end
	end
end

function ZhuXieFollow:CheckFallItem()
	local fall_item_list = Scene.Instance:GetObjListByType(SceneObjType.FallItem)

    if IsEmptyTable(fall_item_list) then
        return false
    end

	local main_role = Scene.Instance:GetMainRole()

	if not main_role then
		return false
	end

	if main_role then
        if main_role:IsRealDead() then
            return
        end
    end

	for k,v in pairs(fall_item_list) do
		if v:GetVo().owner_role_id <= 0 or v:GetVo().owner_role_id == main_role:GetRoleId()
            or (v:GetVo().lose_owner_time > 0 and v:GetVo().lose_owner_time <= TimeWGCtrl.Instance:GetServerTime()) then
            return true
        end
	end

	return false
end


function ZhuXieFollow:FlushZhuXieFollowView()
	self:UpdateTaskListView()
	self:UpdateBossListView()
end

--刷新整个任务列表
function ZhuXieFollow:UpdateTaskListView()
	if not self.is_load_succe then return end
	self:SortTask()
	if self.task_list_view then
		self.task_list_view:SetDataList(self.task_data_list)
		self.task_list_view:JumpToTop()
	end
	local completed_num, all_num = ActivityWGData.Instance:GetZhuXieComptltedTaskNum()
	local color = completed_num >= all_num and COLOR3B.RED or COLOR3B.WHITE
	-- self.node_list.task_btn_text.text.text = string.format(Language.ZhuXie.TaskBtnText,color,completed_num,all_num)
	-- self.node_list.HL_text.text.text = string.format(Language.ZhuXie.TaskBtnText,color,completed_num,all_num)
	-- self:AutoAcceptTask()
	-- self:AutoCompleteTasks()
	self:XuanZeTask()
	self.is_auto = true
	local boss_drop_cfg = ActivityWGData.Instance:GetZhuXieBossCfg().item
	if not boss_drop_cfg then return end

	if not self.boss_reward_list then
		self.boss_reward_list = {}
		for k,v in pairs(boss_drop_cfg) do
			self.boss_reward_list[k] = ItemCell.New()
			-- local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
			self.boss_reward_list[k]:SetInstanceParent(self.node_list["boss_reward_list"])
			self.boss_reward_list[k]:SetData(v)
			local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
			local item_color = item_cfg and item_cfg.color or 0
			self.boss_reward_list[k]:SetTopLeftFlag(item_color > 2, ResPath.GetLoadingPath("a3_ty_bq_z"))
		end
	end
end

function ZhuXieFollow:AutoCompleteTasks()
	if not self.task_list_view or not self.is_auto then return end
	local items = self.task_list_view:GetAllItems()
	if items then
		for k,v in pairs(items) do
			v:AutoCompleteTask()
		end
	end
end


function ZhuXieFollow:SortTask()
	local list1 = {}
	local list2 = {}
	local list3 = {}

	self.task_data_list = ActivityWGData.Instance:GetZhuXieTaskList()
end

function ZhuXieFollow:SetVisible(value)
end

function ZhuXieFollow:GetVisible()
end

function ZhuXieFollow:UpdateBossListView()
	if not self.is_load_succe then return end
	self.countdown_str = "ZhuXieFollowBoss_0" --唯一标识
	self.zhuxie_act_countdown = "zhuxie_act_countdown"
	self:ClearCD()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("zhuxieconfig_auto").other[1]
	local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[other_cfg.boss_id]
	if monster_cfg then
		self.monster_cfg = monster_cfg
		self.node_list.text_name.text.text = monster_cfg.name
	end
	local boss_info = ActivityWGData.Instance:GetOneZhuXieBossInfo() or {}
	local activity_state = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.ZHUXIE)
	self.node_list.right_parent:SetActive(activity_state)-- and boss_info.next_boss_refresh_time < TimeWGCtrl.Instance:GetServerTime()
	if boss_info.boss_id ~= nil then
		local icon = ActivityWGData.Instance:GetBossHeadIcon(boss_info.boss_id)
		if icon ~= nil then
			local asset_name,bundle_name = ResPath.GetBossIcon("wrod_boss_".. icon)
			self.node_list.boss_head_img.image:LoadSprite(asset_name,bundle_name,function ()
				self.node_list.boss_head_img.image:SetNativeSize()
			end)
		end
	end
	self:FlushBossCountDown()
	self:FlushActivityCountDown()

	if boss_info.boss_id == 0 then
		-- self:SetTargetActive(true)
		-- self.node_list.Btn_item_show:SetActive(false)
	else
		-- self:SetTargetActive(false)
		self:SetBossHp(boss_info.boss_cur_hp,boss_info.boss_max_hp,0)
		--self.node_list["flush_flag"]:SetActive(boss_info.boss_cur_hp > 0)
		--self.node_list.effect:SetActive(boss_info.boss_cur_hp > 0)
	end

	local rank_cfg = ActivityWGData.Instance:GetZhuXieBossCfg().rank_item
	if not rank_cfg then return end

	if not self.rank_reward_list then
		self.rank_reward_list = {}
		for k,v in pairs(rank_cfg) do
			self.rank_reward_list[k] = ItemCell.New()
			-- local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
			self.rank_reward_list[k]:SetInstanceParent(self.node_list["rank_reward_list"])
			self.rank_reward_list[k]:SetData(v)
			self.rank_reward_list[k]:SetTopLeftFlag(true, ResPath.GetLoadingPath("a3_ty_bq_z"))
		end
	end
end

function ZhuXieFollow:FlushBossCountDown()
	local boss_info = ActivityWGData.Instance:GetOneZhuXieBossInfo() or {}
	self.node_list.boss_tip:SetActive(false)
	self.node_list.Btn_item_show:SetActive(false)
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.ZHUXIE)

	if activity_info.status == ACTIVITY_STATUS.OPEN and boss_info.next_boss_refresh_time and boss_info.next_boss_refresh_time > TimeWGCtrl.Instance:GetServerTime() then
		CountDownManager.Instance:AddCountDown(self.countdown_str, BindTool.Bind1(self.UpdateCallBack, self), BindTool.Bind1(self.CompleteCallBack, self), boss_info.next_boss_refresh_time, nil, 1)
		self:UpdateCallBack(0, boss_info.next_boss_refresh_time - TimeWGCtrl.Instance:GetServerTime())
		self.is_show_two = false
		self.is_show_one = false
	else
		self.node_list.boss_tip:SetActive(false)
		self:ClearCD()
		self.node_list.Btn_item_show:SetActive(boss_info.boss_cur_hp > 0)
		if boss_info.boss_cur_hp <= 0 then
			self:CreateBossStone()
		end
		self.node_list.text_desc.text.text = Language.TaskFollow.ZhuxieBoss
	end
end

function ZhuXieFollow:FlushActivityCountDown()
	local time

	--如果活动处于准备状态
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.ZHUXIE)
	self.node_list.text_activity_tip.text.text = ""
	self.node_list.activity_tips:SetActive(false)

	local is_need_guaji = false
	if self.act_state == nil and activity_info.status == ACTIVITY_STATUS.OPEN then
		is_need_guaji = true
	end

	if self.act_state ~= nil and self.act_state ~= activity_info.status and activity_info.status == ACTIVITY_STATUS.OPEN then
		is_need_guaji = true
	end

	self.act_state = activity_info.status

	if is_need_guaji then
		if not (GuajiCache.guaji_type == GuajiType.Auto) then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end
	end

	if activity_info.status == ACTIVITY_STATUS.STANDY then
		self.node_list.activity_tips:SetActive(true)
		time = activity_info.next_time
		self.node_list.text_activity_tip.text.text = Language.ZhuXie.ActTips1
	else
		local boss_info = ActivityWGData.Instance:GetOneZhuXieBossInfo() or {}
		if boss_info.next_boss_refresh_time < TimeWGCtrl.Instance:GetServerTime() then
			self.node_list.activity_tips:SetActive(true)
			time = activity_info.next_time
			self.node_list.text_activity_tip.text.text = Language.ZhuXie.ActTips2
		end
	end
	if CountDownManager.Instance:HasCountDown(self.zhuxie_act_countdown) then
		CountDownManager.Instance:RemoveCountDown(self.zhuxie_act_countdown)
	end
	if time then
		CountDownManager.Instance:AddCountDown(self.zhuxie_act_countdown, BindTool.Bind1(self.ActUpdateCallBack, self), BindTool.Bind1(self.ActCompleteCallBack, self), time, nil, 1)
		self:ActUpdateCallBack(0, time - TimeWGCtrl.Instance:GetServerTime())
	end
end

function ZhuXieFollow:ActCompleteCallBack()
	if CountDownManager.Instance:HasCountDown(self.zhuxie_act_countdown) then
		CountDownManager.Instance:RemoveCountDown(self.zhuxie_act_countdown)
	end
	UiInstanceMgr.Instance:DoFBStartDown(3,nil)
	self.node_list.activity_tips:SetActive(false)
end

--活动倒计时刷新
function ZhuXieFollow:ActUpdateCallBack(elapse_time, total_time)
	if not self:IsOpen() then return end
	elapse_time = math.floor(elapse_time)
	total_time = math.floor(total_time)
	self.node_list.activity_count_time.text.text = TimeUtil.FormatSecond(total_time - elapse_time, 2)
end

--BOSS刷新倒计时
function ZhuXieFollow:UpdateCallBack(elapse_time, total_time)
	if not self:IsOpen() then return end

	elapse_time = math.floor(elapse_time)
	total_time = math.floor(total_time)

	local last_time = total_time - elapse_time
	local time_str = TimeUtil.FormatSecond(last_time, 2)

	--self.node_list["flush_flag"]:SetActive((last_time) <= 0)
	--self.node_list.effect:SetActive((last_time) <= 0)
	self.node_list.Btn_item_show:SetActive((last_time) <= 0)
	self:SetTargetActive((last_time) > 0, last_time)
	self.node_list.count_time.text.text = time_str
	self.node_list.time_show.text.text = time_str
	self.node_list.boss_tip:SetActive((last_time) > 0)
	-- self.node_list.right_parent:SetActive((last_time) <= 0)
	if (last_time) <= 0 then
		self:GetIsNeedGuaji()
		self:BossFlushAnimation()
	end
	--记录倒计时
	self.cur_endtime = last_time

end

function ZhuXieFollow:CompleteCallBack() -- 弹出界面，确定是否可以刷新
	self.node_list.BossButton.toggle.isOn = true
	self.node_list.TaskButton.toggle.isOn = false
	self:SetTargetActive(false)
end

function ZhuXieFollow:FlushBossInfo()
	if not self.is_load_succe then return end
	local bosshp_info = ActivityWGData.Instance:GetZhuxieBossHP()
	local hurt_rank_list = ActivityWGData.Instance:GetHurtRankList()

	if not hurt_rank_list then return end
	self.hurt_list_view:SetDataList(hurt_rank_list)
	if bosshp_info.boss_id ~= 0 then
    	local monster_info = BossWGData.Instance:GetMonsterInfo(bosshp_info.boss_id)
    	self.node_list["MonsterName"].text.text = monster_info.name
    	self:SetBossHp(bosshp_info.boss_hp, bosshp_info.max_hp,bosshp_info.is_boss_die)
    end
    local role_name = RoleWGData.Instance:GetRoleVo().role_name
    local rank_num, rank_info = ActivityWGData.Instance:GetHurtRankInfoByName(role_name)
    if rank_info then
	    local percent = ActivityWGData.Instance:GetZhuXieProValue(rank_info.hurt)
		self.node_list.Progress.slider.value = percent
	else
		self.node_list.Progress.slider.value = 0
	end

    if not rank_num or not rank_info then
    	-- self.node_list.rank_num:SetActive(false)
    	-- self.node_list.rank_img:SetActive(false)
    	self.node_list.damage.text.text = "0"
    	--self.node_list.role_name.text.text = Language.GuildAnswer.NoRank1
    else
    	-- self.node_list.rank_num:SetActive(rank_num > 3)
    	-- self.node_list.rank_img:SetActive(rank_num <= 3)
    	-- if rank_num <= 3 then
    	-- 	self.node_list.rank_img.image:LoadSprite(ResPath.GetCommonOthers("rank_num_"..rank_num))
    	-- end
    	--self.node_list.role_name.text.text = rank_info.role_name
    	self.node_list.damage.text.text = CommonDataManager.ConverExpByThousand(rank_info.hurt)
    end
end

function ZhuXieFollow:SetTargetActive(enable, last_time)
	self.node_list["flush_img"]:SetActive(enable)
	--self.node_list["time_img"]:SetActive(enable)
	XUI.SetButtonEnabled(self.node_list["Boss_Btn"], not enable)
	self:SetBossHeadVal(enable, last_time)
end

function ZhuXieFollow:SetBossHeadVal(enable, val)
	if self.boss_head_cache ~= enable or self.boss_head_cache then
		self.boss_head_cache = enable
		if self.boss_head_cache then
			if val == nil then
				return
			end
			local max_time = ActivityWGData.Instance:GetZhuXieBossRefreshTime()
			val = val/max_time
			if val < 0 then
				val = 0
			end
			if val > 1 then
				val = 1
			end
			self.node_list["boss_head_img"].image.material:SetFloat("_Progress", 1 - val)
		else
			self.node_list["boss_head_img"].image.material:SetFloat("_Progress", 1)
		end
	end
end

--怪物死亡
function ZhuXieFollow:SetTargetActiveTwo(enable)

end

function ZhuXieFollow:SetBossHp(hp_value, max_value,is_boss_die)
	if hp_value <= 0 then hp_value = 0 end
	if max_value == 0 then max_value = 1 end
	local percent = hp_value / max_value
	 self:CalcHpPercent(percent)
    if percent <= 0 and self.cur_endtime <= 0 then
    	self:SetTargetActive(false)
    	self.node_list.killed_flag:SetActive(true)
    else
    	self.node_list.killed_flag:SetActive(false)
    end
    if percent <= 0 then
    	self:CreateBossStone()
    else
    	self:DeleteBossStone()
    end

    if percent > 0 and self.cur_endtime <= 0 then
    	if percent * 100 <= 10 then
    		if not self.is_show_tip_3 then
    			self.is_show_tip_3= true
    			-- 危险！首领血量低于10%，将进入狂暴，攻击力翻倍
    			self.node_list.hp_tip.image:LoadSprite(ResPath.GetF2ZhuXieIcon("zx_piaozi1"))
    			self.node_list.hp_tip:SetActive(true)
    			GlobalTimerQuest:AddDelayTimer(function ()
    				self.node_list.hp_tip:SetActive(false)
    			end,2)
    		end
    	elseif percent * 100 <= 40 then
    		if not self.is_show_tip_2 then
    			self.is_show_tip_2 = true
				-- 危险！首领血量低于40%，将释放技能“大杀四方”
    			self.node_list.hp_tip.image:LoadSprite(ResPath.GetF2ZhuXieIcon("zx_piaozi2"))
    			self.node_list.hp_tip:SetActive(true)
    			GlobalTimerQuest:AddDelayTimer(function ()
    				self.node_list.hp_tip:SetActive(false)
    			end,2)
    		end
    	elseif percent * 100 <= 70 then
    		if not self.is_show_tip_1 then
    			self.is_show_tip_1 = true
				-- 危险！首领血量低于70%，将释放技能“大杀四方”
    			self.node_list.hp_tip.image:LoadSprite(ResPath.GetF2ZhuXieIcon("zx_piaozi"))
    			self.node_list.hp_tip:SetActive(true)
    			GlobalTimerQuest:AddDelayTimer(function ()
    				self.node_list.hp_tip:SetActive(false)
    			end,2)
    		end
    	end
    	if self.is_first_enter then
    		self:SetTargetActive(false)
    		self.is_first_enter = false
    	end
    end

    self.node_list["MonsterHp"].slider.value = percent
end

function ZhuXieFollow:CalcHpPercent(percent)
	 if percent < 0.6 and percent > 0.3 and not self.is_show_one then
	 	self.is_show_one = true
	 	if self.monster_cfg then
	 		TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.XueLiangShengYu60, self.monster_cfg.name))
	 	end
	 elseif percent < 0.3 and percent > 0 and not self.is_show_two then
	 	self.is_show_two = true
	 	if self.monster_cfg then
	 		TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.XueLiangShengYu30, self.monster_cfg.name))
	 	end
	 end
end

function ZhuXieFollow:ClearCD()
	if CountDownManager.Instance:HasCountDown(self.countdown_str) then
		CountDownManager.Instance:RemoveCountDown(self.countdown_str)
	end
end

function ZhuXieFollow:OnClick()--去攻击boos
	Scene.Instance:ClearAllOperate()
	self.node_list["flush_flag"]:SetActive(false)
	self.node_list.effect:SetActive(false)
	-- GuajiWGCtrl.Instance:StopGuaji()
	-- GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)

	local other_cfg = ConfigManager.Instance:GetAutoConfig("zhuxieconfig_auto").other[1]

	local scene_id = Scene.Instance:GetSceneId()
	local boss_info = ActivityWGData.Instance:GetOneZhuXieBossInfo() or {}
	local boss_obj = nil
	if boss_info.boss_cur_hp <= 0 then
		GuajiWGCtrl.Instance:ResetMoveCache()
		MoveCache.SetEndType(MoveEndType.Auto)
		GuajiWGCtrl.Instance:SetMoveToPosCallBack(BindTool.Bind1(self.GetIsNeedGuaji, self))
		GuajiWGCtrl.Instance:MoveToPos(scene_id, other_cfg.boss_x, other_cfg.boss_y,3)
		return
	end

	if boss_info.boss_id ~= nil then
		--当前选中的怪已经是目标的时候不在选中
		if GuajiCache.target_obj ~= nil and not GuajiCache.target_obj:IsDeleted() and
			GuajiCache.target_obj:IsBoss() and GuajiCache.target_obj.vo ~= nil and
			GuajiCache.target_obj.vo.monster_id ==  boss_info.boss_id then
			return
		end
		
		GuajiWGCtrl.Instance:ResetMoveCache()
		MoveCache.SetEndType(MoveEndType.FightByMonsterId)
		MoveCache.param1 = boss_info.boss_id
		GuajiCache.monster_id = boss_info.boss_id
		local range = BossWGData.Instance:GetMonsterRangeByid(boss_info.boss_id)
		GuajiWGCtrl.Instance:MoveToPos(scene_id, other_cfg.boss_x, other_cfg.boss_y, range)
	else
		--当前选中的怪已经是目标的时候不在选中
		if GuajiCache.target_obj ~= nil and not GuajiCache.target_obj:IsDeleted() and
			GuajiCache.target_obj:IsBoss() and GuajiCache.target_obj.vo ~= nil and
			GuajiCache.target_obj.vo.monster_id ==  boss_info.boss_id then
			return
		end
		GuajiWGCtrl.Instance:ResetMoveCache()
		MoveCache.SetEndType(MoveEndType.Auto)
		GuajiWGCtrl.Instance:MoveToPos(scene_id, other_cfg.boss_x, other_cfg.boss_y,3)
	end
end


function ZhuXieFollow:GetIsNeedGuaji()
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()
	if x == nil then x = 0 end
	if y == nil then y = 0 end
	local boss_info = ActivityWGData.Instance:GetOneZhuXieBossInfo() or {}
	local boss_cur_hp = boss_info.boss_cur_hp or 0
	if x < self.max_pos_x and x > self.min_pos_x and y > self.min_pos_y and y < self.max_pos_y and boss_cur_hp > 0 then
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	else
		self:AutoCompleteTasks()
	end
end


function ZhuXieFollow:StartGuaJi()
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
end

function ZhuXieFollow:StartAttkBoss()
	 GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Monster)
end


--创建墓碑
function ZhuXieFollow:CreateBossStone()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.ZHUXIE)
	if nil ~= activity_info then
		local flag = activity_info.status == ACTIVITY_STATUS.STANDY
		if flag then
			return
		end
	end
	local boss_info = ActivityWGData.Instance:GetOneZhuXieBossInfo() or {}
	if nil == boss_info.boss_id or self.obj ~= nil then return end
	local other_cfg = ConfigManager.Instance:GetAutoConfig("zhuxieconfig_auto").other[1]
	local stone_vo = GameVoManager.Instance:CreateVo(BossStoneVo)   -- 创建一个VO对象  参数为vo类
	stone_vo.pos_x = other_cfg.boss_x
	stone_vo.pos_y = other_cfg.boss_y
	stone_vo.boss_id = boss_info.boss_id
	stone_vo.obj_id = Scene.Instance:GetSceneClientId()
	self.obj = Scene.Instance:CreateObj(stone_vo, SceneObjType.BossStoneObj)   -- 创建一个场景对象 参数为 vo对象 和场景对象的类型
  	self.obj:SetBossStoneInfo(TimeWGCtrl.Instance:GetServerTime() + 10000)
end

function ZhuXieFollow:DeleteBossStone()
	if self.obj then
		self.obj:DeleteBossStone()
		self.obj = nil
	end

end

function ZhuXieFollow:OnCellData(data)
	if not data then return end
	local cfg = data.cfg
	if not cfg then return end
	local task_id

	local task_info = ActivityWGData.Instance:GetOneZhuXieTaskInfo(cfg.task_id)

	if not task_info or (task_info and task_info.is_fetch_reward == 1) then
		task_id = ActivityWGData.Instance:GetOneTaskID()
	else
		task_id = cfg.task_id
	end

	if not task_id then
		GuajiWGCtrl.Instance:StopGuaji()
		return
	end

	GuajiWGCtrl.Instance:StopGuaji()
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
	ActivityWGCtrl.Instance:RemberCurZhuXieID(cfg.task_id)
	ActivityWGCtrl.Instance:ZhuXieFollowViewFlush(0, "select_change")
	if cfg.task_type ~= ZhuXieFollow.Task_Type.Role then
		self:AutoFindTarget(data)
	else
		self:FindRoleTarget()
	end
end

function ZhuXieFollow:TaskCellCallBack(cell)
	local data = cell:GetData()
	if not data then return end
	self:OnCellData(data)
end

function ZhuXieFollow:FindRoleTarget()
	local kill_role_task = ActivityWGData.Instance:GetOneZhuXieTaskInfo(5)
	if not kill_role_task or not (kill_role_task.is_fetch_reward == 0) then
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
		GuajiWGCtrl.Instance:StopGuaji()

		local main_role = Scene.Instance:GetMainRole()

		if main_role then
			main_role:ChangeToCommonState()
		end
		return
	end
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	if kill_role_task and kill_role_task.is_fetch_reward == 0 then
		local x, y = Scene.Instance:GetMainRole():GetLogicPos()
		local enemy_obj = Scene.Instance:SelectObjHelper(SceneObjType.Role, x, y, 1000000, SelectType.Enemy)
		if nil ~= enemy_obj and not enemy_obj:IsDeleted() and not enemy_obj:IsDead() and Scene.Instance:IsEnemy(enemy_obj) then
			GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, enemy_obj, SceneTargetSelectType.SELECT)
		end
	end
end


-- 自动寻路到目标点,然后干活
function ZhuXieFollow:AutoFindTarget(data)
	if data == nil or next(data) == nil then return end
	local cfg = data.cfg
	if cfg == nil or next(cfg) == nil then return end


	local cur_select_target = SceneObj.select_obj	--当前若已选将不再随机选出--当前若已选将不再随机选出

	if nil ~= cur_select_target and nil ~= cur_select_target.vo and
		not cur_select_target:IsDeleted() then
		if cfg.event == SceneObjType.Monster and cur_select_target.vo.monster_id ~= cfg.param_id then
			cur_select_target = nil
		elseif cfg.event == SceneObjType.GatherObj and cur_select_target.vo.gather_id ~= cfg.param_id then
			cur_select_target = nil
		end
	end

	if nil == cur_select_target then
		if cfg.event == SceneObjType.Monster then
			cur_select_target = Scene.Instance:SelectMinDisMonster(cfg.param_id)	--选择最近的
		elseif cfg.event == SceneObjType.GatherObj then
			cur_select_target = Scene.Instance:SelectMinDisGather(cfg.param_id)	--选择最近的
		end
	end
	local target_obj = {}
	target_obj.x = cfg.x
	target_obj.y = cfg.y
	target_obj.scene = SceneType.ZhuXie
	if nil ~= cur_select_target and nil ~= cur_select_target.vo and not cur_select_target:IsDeleted() and cur_select_target:GetObjId() ~= nil then
		target_obj.obj = cur_select_target
		target_obj.obj_id = cur_select_target:GetObjId()
		target_obj.x, target_obj.y= cur_select_target:GetLogicPos()
		target_obj.id = cur_select_target.id
	end

	local gjc = GuajiWGCtrl.Instance
	gjc:StopGuaji()

	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		main_role:ChangeToCommonState()
	end

	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic ~= nil then
		scene_logic:SetIsAutoTask(false)
	end

	local scene_id = Scene.Instance:GetSceneId()
	if cfg.task_type ~= ZhuXieFollow.Task_Type.Gather then
		MoveCache.SetEndType(MoveEndType.Auto)
		gjc:MoveToPos(scene_id, target_obj.x, target_obj.y,3)
	else--采集
		if scene_logic ~= nil then
			scene_logic:SetIsAutoTask(true)
		end

		MoveCache.SetEndType(MoveEndType.GatherById)
		MoveCache.param1 = cfg.param_id
		gjc:MoveToPos(scene_id, target_obj.x, target_obj.y,3)
	end
end

-------------ZhuXieFollowTask-------------------
---------------------------------

ZhuXieFollowTask = ZhuXieFollowTask or BaseClass(BaseRender)

function ZhuXieFollowTask:__init()
	self.task_status = GameEnum.TASK_STATUS_NONE
	self.task_link_type = "common"
	-- self.cell_list = {}
	self:CreateChild()
	self.value = 0
end

function ZhuXieFollowTask:__delete()
	if self.delay_time then
		GlobalTimerQuest:CancelQuest(self.delay_time)
	end

	if self.cell_list then
		self.cell_list:DeleteMe()
		self.cell_list = nil
	end

	self.start_guaji_event = nil
	self:ClearCountDown()
end

function ZhuXieFollowTask:CreateChild()

	-- for i = 1, 2 do
		self.cell_list = ItemCell.New()
		self.cell_list:SetInstanceParent(self.node_list["item_num_1"])
	-- end
	-- self.node_list.btn_task_bg.button:AddClickListener(BindTool.Bind1(self.OnClick,self))
end

function ZhuXieFollowTask:AutoChange()
	if nil == self.data then
		return
	end

	local cur_task_id = ActivityWGCtrl.Instance:GetCurZhuXieID()
	local flag = cur_task_id == self.data.cfg.task_id
	local task_info = ActivityWGData.Instance:GetOneZhuXieTaskInfo(self.data.cfg.task_id) or {}

	if task_info.is_fetch_reward == 1 or self.data.cfg.task_id == TaskType_Role then
		flag = false
	end

	self.node_list.BtnAuto:SetActive(flag)
	self.node_list.Effect:SetActive(flag)
end


function ZhuXieFollowTask:OnFlush()
	if not self.data then return end
	self.node_list.img_dacheng:SetActive(false)
	local gray_color = self:HasFetchReward() and COLOR3B.GRAY
	local reward_data = ActivityWGData.Instance:GetZhuxieTaskReward(self.data.cfg.task_id)
	self.cell_list:SetData(reward_data.item[0])
	local task_info = ActivityWGData.Instance:GetOneZhuXieTaskInfo(self.data.cfg.task_id) or {}
	local param_value = task_info.param_value or 0
	local max_value = self.data.cfg.param_max_value or 0
	local is_fetch_reward = task_info.is_fetch_reward or 0
	self:AutoChange()

	local cur_task_id = ActivityWGCtrl.Instance:GetCurZhuXieID()
	if self.delay_time then
		GlobalTimerQuest:CancelQuest(self.delay_time)
	end
	-- self.delay_time = GlobalTimerQuest:AddDelayTimer(function ()
	-- 	--类型四采集类型
	-- 	if self.data.cfg.task_type == ZhuXieFollow.Task_Type.Gather and cur_task_id == self.data.cfg.task_id and param_value < max_value then
	-- 		ActivityWGCtrl.Instance:ZhuXieAutoFindTarget(self.data)
	-- 	end
	-- end,0.2)
	self.value = param_value
	local status = ""
	if is_fetch_reward == 1 then
		-- status = Language.Task.task_status[4]--已完成
		self.node_list.rich_desc.text.text = string.format(Language.ZhuXie.TaskQuantity1,self.data.cfg.task_name,param_value,max_value)
		self.node_list.img_dacheng:SetActive(true)

	elseif max_value <= param_value then
		-- status = Language.Task.task_status[3]--完成任务
		self.node_list.rich_desc.text.text = string.format(Language.ZhuXie.TaskQuantity1,self.data.cfg.task_name,param_value,max_value)
		self.node_list.img_dacheng:SetActive(true)
	else
		status = Language.Task.task_status[2]--进行中
		self.node_list.rich_desc.text.text = string.format(Language.ZhuXie.TaskQuantity1,self.data.cfg.task_name,param_value,max_value)
	end

	local time = self.data.double_task_end_time - TimeWGCtrl.Instance:GetServerTime()
	self.node_list.double_flag:SetActive(self.data.is_double == 1 and time > 0 )
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.ZHUXIE)
	if self.data.is_double == 1 and activity_info.status == ACTIVITY_STATUS.OPEN then
		if time > 0 then
			self:ClearCountDown()
			self:ChangeTime(0,time)
			self.count_down = CountDown.Instance:AddCountDown(time, 1, BindTool.Bind(self.ChangeTime, self))
		else
			self.node_list.rich_name.text.text = string.format(Language.ZhuXie.TaskState2,self.data.cfg.item_name,"")
		end
	else
		self:ClearCountDown()
		self.node_list.rich_name.text.text = string.format(Language.ZhuXie.TaskState1,self.data.cfg.item_name,status)
	end
	self:AutoCompleteTask()
end

function ZhuXieFollowTask:ChangeTime(elapse_time, total_time)
	local time = math.floor(total_time - elapse_time)
	if self.node_list.rich_name then
		if time > 0 then
			self.node_list.rich_time:SetActive(true)
			self.node_list.rich_name.text.text = string.format(Language.ZhuXie.TaskState2,self.data.cfg.item_name,"")
       		self.node_list.rich_time.text.text = string.format(Language.ZhuXie.TaskState3,TimeUtil.FormatSecond(time,2))
       	else
       		self.node_list.rich_time:SetActive(false)
       		self.node_list.rich_name.text.text = string.format(Language.ZhuXie.TaskState2,self.data.cfg.item_name,"")
       	end
    end
end

function ZhuXieFollowTask:ClearCountDown()
	-- self.node_list.double_flag:SetActive(false)
	if CountDown.Instance:HasCountDown(self.count_down) then
        CountDown.Instance:RemoveCountDown(self.count_down)
        self.count_down = nil
   	end
end

function ZhuXieFollowTask:IsTaskCanComplete()
	if not self.data then return false end
	local task_info = ActivityWGData.Instance:GetOneZhuXieTaskInfo(self.data.cfg.task_id) or {}

	local param_value = task_info.param_value or 0
	local max_value = self.data.cfg.param_max_value or 0
	local is_fetch_reward = task_info.is_fetch_reward or 0
	if is_fetch_reward == 0 and param_value >= max_value then
		return true
	end
	return false
end

function ZhuXieFollowTask:HasFetchReward()
	if not self.data then return false end
	local task_info = ActivityWGData.Instance:GetOneZhuXieTaskInfo(self.data.cfg.task_id) or {}
	local is_fetch_reward = task_info.is_fetch_reward or 0
	return is_fetch_reward ~= 0
end

function ZhuXieFollowTask:AutoCompleteTask()
	if not self.data then return end
	if self:IsTaskCanComplete() then
		ActivityWGCtrl.Instance:SendZhuXieFetchTaskReward(self.data.cfg.task_id)
	end
end

function ZhuXieFollowTask:CreateSelectEffect()
end

---------------------------------------------------ZhuXieHurtRankCell-----------------------------------------------------
ZhuXieHurtRankCell = ZhuXieHurtRankCell or BaseClass(BaseRender)

function ZhuXieHurtRankCell:__init()
	self.node_list["attack_btn"].button:AddClickListener(BindTool.Bind1(self.OnClickAttackBtn, self))
end

function ZhuXieHurtRankCell:__delete()

end

function ZhuXieHurtRankCell:OnFlush()
	if not self.data then return end
	self.node_list.rank_num:SetActive(self.index > 3)
	self.node_list.rank_img:SetActive(self.index <= 3)
	if self.index <= 3 then
		self.node_list.rank_img.image:LoadSprite(ResPath.GetCommonOthers("rank_num_"..self.index))
	end
	self.node_list.rank_num.text.text = self.index
	self.node_list.role_name.text.text = self.data.role_name
	self.node_list.damage.text.text = CommonDataManager.ConverExpByThousand(self.data.hurt)
	local percent = ActivityWGData.Instance:GetZhuXieProValue(self.data.hurt)
	self.node_list.Progress.slider.value = percent

	local boss_info = ActivityWGData.Instance:GetZhuxieBossHP()

	if not boss_info or IsEmptyTable(boss_info) then
		return
	end

	local role_id = RoleWGData.Instance:GetRoleVo().role_id
	self.node_list["attack_btn"]:SetActive(role_id ~= self.data.role_id and boss_info.boss_hp > 0)
end

function ZhuXieHurtRankCell:OnClickAttackBtn()
	local main_role = Scene.Instance:GetMainRole()

	if not main_role or main_role:IsDeleted() or main_role:IsDead() and not main_role:GetVo() then
		return
	end

	local role_list = Scene.Instance:GetRoleList()

	if IsEmptyTable(role_list) then
		--对方不在攻击范围
		TipWGCtrl.Instance:ShowSystemMsg(Language.ZhuXie.AttackTips1)
		return
	end

	for k,v in pairs(role_list) do
		if not v:IsDeleted() and not v:IsDead() and v.vo ~= nil and v.vo.role_id and v.vo.role_id > 0 then
			if self.data.role_id == v.vo.role_id then

				if Scene.Instance:IsFriend(v) then
					--对方是队友
					if v:IsInSafeArea() then
						--对方处于安全区
						TipWGCtrl.Instance:ShowSystemMsg(Language.ZhuXie.AttackTips4)
					elseif main_role:GetVo().attack_mode == ATTACK_MODE.PEACE then
						--处于和平模式
						TipWGCtrl.Instance:ShowSystemMsg(Language.ZhuXie.AttackTips3)
					elseif main_role:GetVo().attack_mode == ATTACK_MODE.GUILD then
						--处于盟友模式
						TipWGCtrl.Instance:ShowSystemMsg(Language.ZhuXie.AttackTips2)
					end
					return
				end

				--对方是敌人
				if Scene.Instance:IsEnemy(v) then
					GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
					GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
					GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, v, SceneTargetSelectType.SELECT)
				end

				return
			end
		end
	end
	--对方不在攻击范围
	TipWGCtrl.Instance:ShowSystemMsg(Language.ZhuXie.AttackTips1)
end
