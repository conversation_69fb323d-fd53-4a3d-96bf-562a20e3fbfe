GuildPopSetting = GuildPopSetting or BaseClass(SafeBaseView)
--帮派申请设置界面(默认参数)
local MIN_LEVEL = 65
local MIN_ZHANLI = 50000
function GuildPopSetting:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(600, 424)})
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_guild_setting")

	self.ui_min_level = nil
	self.ui_min_zhanli = nil
	self.min_level = ""
	self.min_power = ""

	self.current_setting_model = 1

	self.pop_num_model = {
		level = 1,
		zhanli = 2,
	}
end

function GuildPopSetting:__delete()	
end

function GuildPopSetting:ReleaseCallBack()
	if self.pop_num then
		self.pop_num:DeleteMe()
		self.pop_num = nil
	end
	if self.radiobutton then
		self.radiobutton:DeleteMe()
		self.radiobutton = nil
	end
	if self.min_power then
		self.min_power = nil
	end
	if self.min_level then
		self.min_level = nil
	end
	if self.ui_min_level then
		self.ui_min_level = nil
	end
	if self.ui_min_zhanli then
		self.ui_min_zhanli = nil
	end
end

function GuildPopSetting:LoadCallBack()	
	self.node_list.title_view_name.text.text = Language.Guild.ShenQingSheZhi

	self.ui_min_level = self.node_list.btn_min_level
	self.ui_min_zhanli = self.node_list.btn_min_zhanli
	self.min_level = self.node_list.min_leve_text --最小等级
	self.min_power = self.node_list.min_power_text --最小战力
	self.min_level.text.text = MIN_LEVEL --最小等级
	self.min_power.text.text = MIN_ZHANLI --最小战力
	self.mim_power_num = MIN_ZHANLI
	self.pop_num = NumKeypad.New(GuideModuleName.NumKeypad, 14)
	self.image_list = {}
	for i = 1, 3 do
		self.node_list["toggle_" .. i .. "_1"].button:AddClickListener(BindTool.Bind2(self.OnSelectSettingModelHandler, self,i))
		self.image_list[i] = self.node_list["image_" .. i .. "_1"]
	end
	self:RegisterAllEvent()
end

-- 注册事件
function GuildPopSetting:RegisterAllEvent()
	self.node_list.btn_save.button:AddClickListener(BindTool.Bind1(self.OnSaveSettingHandler, self))
	self.node_list.btn_cancel.button:AddClickListener(BindTool.Bind1(self.OnCancelHandler, self))
	XUI.AddClickEventListener(self.ui_min_level, BindTool.Bind2(self.OnOpenNumViewHandler, self, self.pop_num_model.level))
	XUI.AddClickEventListener(self.ui_min_zhanli, BindTool.Bind2(self.OnOpenNumViewHandler, self, self.pop_num_model.zhanli))
end

-- 选择申请方式事件
function GuildPopSetting:OnSelectSettingModelHandler(index)
	self:CloseToggle(index)
	local setting_model = GuildDataConst.GUILD_SETTING_MODEL
	self.current_setting_model = index
	local guildvo = GuildDataConst.GUILDVO
	local need_level = guildvo.applyfor_need_level
	local need_capability = guildvo.applyfor_need_capability
	need_level = need_level < MIN_LEVEL and MIN_LEVEL or need_level
	need_capability = need_capability < MIN_ZHANLI and MIN_ZHANLI or need_capability
	local model = self.current_setting_model - 1
	if model == setting_model.AUTOPASS and need_capability and need_level then
		self.ui_min_level.button.interactable = true
		self.ui_min_zhanli.button.interactable = true
		self.min_level.text.text = need_level
		self.min_power.text.text = CommonDataManager.ConverExpByThousand(need_capability)
		self.mim_power_num = need_capability
	else		
		self.ui_min_level.button.interactable = false
		self.ui_min_zhanli.button.interactable = false
		self.min_level.text.text = ""
		self.min_power.text.text = ""
	end
end

--toggle_按钮是否显示
function GuildPopSetting:CloseToggle(num)
	self.image_list[num]:SetActive(true)
	for i=1,3 do
		if i ~= num then
			self.image_list[i]:SetActive(false)
		end
	end
end
-- 保存设置事件
function GuildPopSetting:OnSaveSettingHandler()
	local guildvo = GuildDataConst.GUILDVO
	if 0 == guildvo.guild_id then
		return
	end
	local setting_model = GuildDataConst.GUILD_SETTING_MODEL
	local need_capability = 0
	local need_level = 0
	local model = self.current_setting_model - 1
	if model == setting_model.AUTOPASS then
		need_capability = tonumber(self.mim_power_num)
		need_level = tonumber(self.min_level.text.text)
	end
	GuildWGCtrl.Instance:SendSettingGuildReq(guildvo.guild_id, model, need_capability, need_level)
end

-- 取消事件
function GuildPopSetting:OnCancelHandler()
	self:Close()
end

-- 打开数字界面事件
function GuildPopSetting:OnOpenNumViewHandler(model)
	if model == self.pop_num_model.level then
		self.pop_num:SetMaxValue(1000)
	elseif model == self.pop_num_model.zhanli then
		self.pop_num:SetMaxValue(1000000)
		self.pop_num:SetMinUnit(10000)
		self.pop_num:SetIsConverNumber(true)
	end
	self.pop_num:Open()
	self.pop_num:SetOkCallBack(BindTool.Bind2(self.OnOKCallBack, self, model))
end

function GuildPopSetting:OnOKCallBack(model, num)
	if model == self.pop_num_model.level then
		if num < MIN_LEVEL then
			num = MIN_LEVEL
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.MinLevel)
		end
		self.min_level.text.text = CommonDataManager.ConverExpByThousand(num)
	elseif model == self.pop_num_model.zhanli then
		if num < MIN_ZHANLI then
			num = MIN_ZHANLI
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.MinCapability)
		end
		self.mim_power_num = num
		self.min_power.text.text = CommonDataManager.ConverExpByThousand(num)
	end
end

function GuildPopSetting:OpenCallBack()
	local guildvo = GuildDataConst.GUILDVO
	local info_type= GuildDataConst.GUILD_INFO_TYPE
	GuildWGCtrl.Instance:SendGetGuildInfoReq(info_type.GUILD_INFO, guildvo.guild_id)
end

function GuildPopSetting:ShowIndexCallBack()
	self:Flush()
end

-- 初始化界面
function GuildPopSetting:OnFlush()
	local guildvo = GuildDataConst.GUILDVO
	local need_level = guildvo.applyfor_need_level
	local need_capability = guildvo.applyfor_need_capability
	need_level = need_level < MIN_LEVEL and MIN_LEVEL or need_level
	need_capability = need_capability < MIN_ZHANLI and MIN_ZHANLI or need_capability
	self.min_level.text.text = need_level
	self.mim_power_num = need_capability
	self.min_power.text.text = CommonDataManager.ConverExpByThousand(need_capability)
	self:OnSelectSettingModelHandler(guildvo.applyfor_setup + 1)
end

function GuildPopSetting:CloseCallBack()
	GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_INFO, RoleWGData.Instance.role_vo.guild_id)
end


