require("game/task/task_follow_render")

local CUTDOWN_NAME = "yezhanwangcheng_cutdown_turn"
local BUFF_REFRESH = "yezhanwangcheng_cutdown_buff_refresh"
local BUFF_GET = "yezhanwangcheng_cutdown_buff_get"
local MAX_TURN = 3

-- local MAX_CUTDOWN_TIME = 10 -- 开始切换阵营倒计时
-- KuafuYeZhanWangChengFollow.MAX_TURN = 3 -- 一共有多少轮

-- KuafuYeZhanWangChengFollow.Task_Type = {
-- 	Gather = 1,
-- 	Boss = 2,
-- 	Monster = 3,
-- 	Role = 4
-- }

-- KuafuYeZhanWangChengFollow.Task = {
-- 	Gather = 1,
-- 	Boss = 2,
-- 	Monster = 3,
-- 	Role = 4
-- }

KuafuYeZhanWangChengFollow = KuafuYeZhanWangChengFollow or BaseClass(SafeBaseView)

function KuafuYeZhanWangChengFollow:__init()
	self:AddViewResource(0, "uis/view/kuafu_yezhanwangcheng_ui_prefab", "layout_yzwc_task_view")
	self:AddViewResource(0, "uis/view/kuafu_yezhanwangcheng_ui_prefab", "layout_yezhanwangchen_battle_top")
	self:AddViewResource(0, "uis/view/kuafu_yezhanwangcheng_ui_prefab", "layout_yzwc_mainui_rank_view")

	self.next_redistribute_time = 0
	self.active_close = false
	self.is_safe_area_adapter = true
	self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
	self.view_name = "KuafuYeZhanWangChengFollow"
end

function KuafuYeZhanWangChengFollow:LoadCallBack()
	if not self.list_view then
		self.list_view = AsyncListView.New(YeZhanWangChengRankItemRender, self.node_list.yzwc_rank_list)
	end

	if not self.show_top_event then
		self.show_top_event = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind(self.ShowTopEvent, self))
	end

	-- for i = 1, 3 do
	-- 	XUI.AddClickEventListener(self.node_list["zc_result_" .. i], BindTool.Bind(self.OnClickGetResult, self, i))
	-- end

	XUI.AddClickEventListener(self.node_list.btn_gift_see, BindTool.Bind1(self.OnClickSeeGift, self))
	XUI.AddClickEventListener(self.node_list.btn_get_rank, BindTool.Bind1(self.OnClickGetRank, self))
	XUI.AddClickEventListener(self.node_list.zc_result_1, BindTool.Bind(self.OnClickGetResult, self))
	XUI.AddClickEventListener(self.node_list.btn_qiangduo_buff, BindTool.Bind(self.OnClickQiangDuoBuff, self))
	XUI.AddClickEventListener(self.node_list.btn_refresh_buff, BindTool.Bind(self.OnClickRefreshBuff, self))
	XUI.AddClickEventListener(self.node_list.btn_goto_buff, BindTool.Bind(self.OnClickGoToBuff, self))

	-- self.node_list.rich_desc.text.text = Language.YeZhanWangCheng.TipContent
	self.node_list["cir_time"]:SetActive(false)
end

function KuafuYeZhanWangChengFollow:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown(CUTDOWN_NAME) then
		CountDownManager.Instance:RemoveCountDown(CUTDOWN_NAME)
	end

	if CountDownManager.Instance:HasCountDown(BUFF_REFRESH) then
		CountDownManager.Instance:RemoveCountDown(BUFF_REFRESH)
	end

	if CountDownManager.Instance:HasCountDown(BUFF_GET) then
		CountDownManager.Instance:RemoveCountDown(BUFF_GET)
	end

	if self.show_effect_event then
		GlobalTimerQuest:CancelQuest(self.show_effect_event)
		self.show_effect_event = nil
	end

	if self.show_effect_event1 then
		GlobalTimerQuest:CancelQuest(self.show_effect_event1)
		self.show_effect_event1 = nil
	end

	if self.effect_delay then
		GlobalTimerQuest:CancelQuest(self.effect_delay)
		self.effect_delay = nil
	end

	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view = nil
	end

	if nil ~= self.shrinkbuttons_change then
		GlobalEventSystem:UnBind(self.shrinkbuttons_change)
		self.shrinkbuttons_change = nil
	end

	self.next_redistribute_time = 0

	if self.rune_timequest ~= nil then
		GlobalTimerQuest:CancelQuest(self.rune_timequest)
		self.rune_timequest = nil
	end

	if self.delay_redistribute_quest ~= nil then
		GlobalTimerQuest:CancelQuest(self.delay_redistribute_quest)
		self.delay_redistribute_quest = nil
	end

	if self.role_head_cell then
		self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
	end
end

function KuafuYeZhanWangChengFollow:ShowIndexCallBack(index, loaded_times)
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		self:InitCallBack()
	end)
end

function KuafuYeZhanWangChengFollow:InitCallBack()
	local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
	MainuiWGCtrl.Instance:SetOtherContents(true)
	self.node_list["layout_yzwc_task_view_root"].transform:SetParent(parent.transform)
	self.node_list["layout_yzwc_task_view_root"].transform.anchoredPosition = Vector3(0, 0, 0)
	self.node_list["layout_yzwc_task_view_root"].transform.localScale = Vector3(1, 1, 1)
	self:ShowWaterEffect()
	MainuiWGCtrl.Instance:ActiveVIPConnectToTop(false)
end

function KuafuYeZhanWangChengFollow:ShowWaterEffect()
	if not self.show_effect_event then
		local scene_info = KuafuYeZhanWangChengWGData.Instance:NightFightSceneInfo()
		local next_time = scene_info.next_redistribute_time == 0 and scene_info.fight_start_time or scene_info.next_redistribute_time
		local time = next_time - TimeWGCtrl.Instance:GetServerTime()
		self.show_effect_event = GlobalTimerQuest:AddDelayTimer(function()
			self:ShowStageAnim()
			self.show_effect_event = nil
		end, time - 1)

		-- self.show_effect_event1 = GlobalTimerQuest:AddDelayTimer(function()
		-- 	local logic = Scene.Instance:GetSceneLogic()
		-- 	if logic and logic.PlayAllChuShengEffect then
		-- 		logic:PlayAllChuShengEffect()
		-- 	end
		-- 	self.show_effect_event1 = nil
		-- end, time + 1.5)
	end
end

function KuafuYeZhanWangChengFollow:CloseCallBack()
	MainuiWGCtrl.Instance:SetFBNameState(false)

	if self.node_list["layout_yzwc_task_view_root"] then
		self.node_list["layout_yzwc_task_view_root"].transform:SetParent(self.root_node_transform, false)
	end

	if self.rune_timequest ~= nil then
		GlobalTimerQuest:CancelQuest(self.rune_timequest)
		self.rune_timequest = nil
	end
	if self.delay_redistribute_quest ~= nil then
		GlobalTimerQuest:CancelQuest(self.delay_redistribute_quest)
		self.delay_redistribute_quest = nil
	end

	if self.show_effect_event then
		GlobalTimerQuest:CancelQuest(self.show_effect_event)
		self.show_effect_event = nil
	end

	if self.show_effect_event1 then
		GlobalTimerQuest:CancelQuest(self.show_effect_event1)
		self.show_effect_event1 = nil
	end

	if self.show_top_event then
		GlobalEventSystem:UnBind(self.show_top_event)
		self.show_top_event = nil
	end

	self.set_fb_start_down3 = nil
	self.is_show_on = nil

	MainuiWGCtrl.Instance:UnActiveVIPConnectToTop()
end

function KuafuYeZhanWangChengFollow:OnFlush()
	local role_info = KuafuYeZhanWangChengWGData.Instance:GetSetSelfInfo()

	if role_info and role_info.user_key then
		local self_info = KuafuYeZhanWangChengWGData.Instance:GetRewardRoleInfoByUserKey(role_info.user_key)
		self.node_list.label_jifen.text.text = self_info.score or ""
		self.node_list.label_rank_value.text.text = self_info.rank or ""
		-- self.node_list["role_name"].text.text = GameVoManager.Instance:GetMainRoleVo().name
		self.node_list.yzwc_my_rank:SetActive(not IsEmptyTable(self_info))
	end

	local scene_info = KuafuYeZhanWangChengWGData.Instance:NightFightSceneInfo()
	if scene_info then
		local time = scene_info.next_redistribute_time == 0 and scene_info.fight_start_time or scene_info.next_redistribute_time
		self:ShowChuanWen(time)
	end

	-- local cur_turn = KuafuYeZhanWangChengWGData.Instance:GetCurTurn()

	local rank_info = KuafuYeZhanWangChengWGData.Instance:NightFightRankInfo()
	if rank_info.rank_info_list and not IsEmptyTable(rank_info.rank_info_list) then
		self.list_view:SetDataList(rank_info.rank_info_list)
		self.node_list.no_rank_list_flag:SetActive(false)
	else
		self.node_list.no_rank_list_flag:SetActive(true)
	end

	if self.rank_info_list and self.rank_info_list[1] ~= nil and rank_info.rank_info_list[1] and self.rank_info_list[1].user_name ~= rank_info.rank_info_list[1].user_name then
		if Scene.Instance:GetSceneType() ~= SceneType.YEZHANWANGCHENGFUBEN then
			return
		end
		local name = BiZuoWGData.Instance:GetSetverNameFormat(rank_info.rank_info_list[1].user_name)
		TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.BangShouYiZhu, name))
	end

	self.rank_info_list = rank_info.rank_info_list

	local camp_info = KuafuYeZhanWangChengWGData.Instance:GetNightFightSideListInfo()

	for i = 1, 3 do
		self.node_list["num" .. i].text.text = ((camp_info or {})[i] or {}).team_count or 0
		self.node_list["score" .. i].text.text = ((camp_info or {})[i] or {}).score or 0

		local is_tianxuan_buff = ((camp_info or {})[i] or {}).is_tianxuan_buff or 0
		self.node_list["flag_tianxuan_buff" .. i]:CustomSetActive(is_tianxuan_buff == 1)
	end

	self:FlushSpecialBuffBtnState()
end

function KuafuYeZhanWangChengFollow:ShowTopEvent(is_on)
	--self.node_list.fp_move_up_node.transform:DOAnchorPosY(is_on and 380 or 0, 2)
	local start_alpha = is_on and 1 or 0
	local end_alpha = is_on and 0 or 1
	self.node_list.fp_move_up_node.canvas_group:DoAlpha(start_alpha, end_alpha, 0.3)
	self.node_list.fp_move_up_node.canvas_group.blocksRaycasts = not is_on

	if self.is_show_on ~= is_on then
        self.is_show_on = is_on
        if is_on then
            self.node_list["both_move_right_node"].rect:DOAnchorPosX(50, 0.4)
            self.node_list["both_move_right_node"].canvas_group:DoAlpha(1, 0, 0.4)
            self.node_list["both_move_right_node"].canvas_group.blocksRaycasts = false
        else
            self.node_list["both_move_right_node"].rect:DOAnchorPosX(-0, 0.4)
            self.node_list["both_move_right_node"].canvas_group:DoAlpha(0, 1, 0.4)
            self.node_list["both_move_right_node"].canvas_group.blocksRaycasts = true
        end
    end
end

function KuafuYeZhanWangChengFollow:OnBtnTipsClickHandler()
	RuleTip.Instance:SetContent(Language.YeZhanWangCheng.Tips, Language.YeZhanWangCheng.TipsTitle)
end

function KuafuYeZhanWangChengFollow:ChangeCountDown()
	if CountDownManager.Instance:HasCountDown(CUTDOWN_NAME) then
		CountDownManager.Instance:RemoveCountDown(CUTDOWN_NAME)
	end

	local scene_info = KuafuYeZhanWangChengWGData.Instance:NightFightSceneInfo()
	if scene_info.next_redistribute_time - TimeWGCtrl.Instance:GetServerTime() > 0 then
		CountDownManager.Instance:AddCountDown(CUTDOWN_NAME, 
			BindTool.Bind(self.UpdateTimeFunc, self), 
			BindTool.Bind(self.CompleteTimeFunc, self),
			scene_info.next_redistribute_time, 
			nil, 
			0.3)
	end
end

function KuafuYeZhanWangChengFollow:UpdateTimeFunc(elapse_time, total_time)
	local scene_info = KuafuYeZhanWangChengWGData.Instance:NightFightSceneInfo()
	local cur_turn = KuafuYeZhanWangChengWGData.Instance:GetCurTurn()
	-- local time = math.floor(total_time - elapse_time)
	local cur_time = TimeWGCtrl.Instance:GetServerTime()
	local end_time = scene_info.next_redistribute_time
	local is_start = cur_turn > 0
	local time = math.floor(end_time - cur_time)
	self:SetCountDownTime(time >= 0 and time or 0, is_start)
end

function KuafuYeZhanWangChengFollow:CompleteTimeFunc()
	GlobalTimerQuest:AddDelayTimer(function()
		self:Flush()
	end, 1)

	self:CloseTimeBg()
end

function KuafuYeZhanWangChengFollow:ShowChuanWen(next_time)
	-- 轮数发生变化
	if self.next_redistribute_time ~= next_time then
		local redistribute_time, interval = KuafuYeZhanWangChengWGData.Instance:GetRedistributeTime()
		local enter_time_stamp = next_time - redistribute_time
		local delay_time = math.floor(TimeWGCtrl.Instance:GetServerTime() - enter_time_stamp) % interval

		if self.rune_timequest == nil then
			self.rune_timequest = GlobalTimerQuest:InvokeRepeating(function()
				self:ShowRankChuanWen()
			end, interval - delay_time, interval, 99999)
		end

		self.next_redistribute_time = next_time
	end
end

function KuafuYeZhanWangChengFollow:ShowStageAnim()
	local cur_turn = KuafuYeZhanWangChengWGData.Instance:GetCurTurn()
	if cur_turn ~= MAX_TURN then
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
		ViewManager.Instance:Open(GuideModuleName.ZCStageView)
		-- ViewManager.Instance:FlushView(GuideModuleName.ZCStageView, nil, "turn_change", {stage = cur_turn})
	end
end

function KuafuYeZhanWangChengFollow:AddWaterEffectDelay()
	if self.effect_delay then
		GlobalTimerQuest:CancelQuest(self.effect_delay)
		self.effect_delay = nil
	end
	self.effect_delay = GlobalTimerQuest:AddDelayTimer(function()
		if GuajiCache.guaji_type ~= GuajiType.Auto and not TaskWGCtrl.Instance:IsFly() then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end
	end, 1.5)
end

function KuafuYeZhanWangChengFollow:ShowRankChuanWen()
	local rank_info = KuafuYeZhanWangChengWGData.Instance:NightFightRankInfo().rank_info_list
	if rank_info and not IsEmptyTable(rank_info) then
		if Scene.Instance:GetSceneType() ~= SceneType.YEZHANWANGCHENGFUBEN then
			return
		end
		local length = #rank_info
		if length == 1 then
			local name = BiZuoWGData.Instance:GetSetverNameFormat(rank_info[1].user_name)
			TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.JiFenRankOne, name))
		elseif length == 2 then
			local name1 = BiZuoWGData.Instance:GetSetverNameFormat(rank_info[1].user_name)
			local name2 = BiZuoWGData.Instance:GetSetverNameFormat(rank_info[2].user_name)
			TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.JiFenRankOne, name1))
			TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.JiFenRankTwo, name2))
		elseif length >= 3 then
			local name1 = BiZuoWGData.Instance:GetSetverNameFormat(rank_info[1].user_name)
			local name2 = BiZuoWGData.Instance:GetSetverNameFormat(rank_info[2].user_name)
			local name3 = BiZuoWGData.Instance:GetSetverNameFormat(rank_info[3].user_name)
			TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.JiFenRankOne, name1))
			TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.JiFenRankTwo, name2))
			TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.JiFenRankThree, name3))
		end
	end
end

-- 更改显示第几轮
function KuafuYeZhanWangChengFollow:ChangeTurnView(turn)
	local str
	if turn > 0 then
		str = string.format(Language.YeZhanWangCheng.Turn, NumberToChinaNumber(turn))
	else
		str = Language.Activity.ZhunBeiZhong
	end
	self:SetTurnStr(str)
end

function KuafuYeZhanWangChengFollow:SetTurnStr(str)
	MainuiWGCtrl.Instance:SetFBNameState(true, str) -- 夜战王城
end

function KuafuYeZhanWangChengFollow:SetPrepareTime(time)
	if CountDownManager.Instance:HasCountDown(CUTDOWN_NAME) then
		if KuafuYeZhanWangChengWGData.Instance:CheckActIsStart() then
			return
		end
	end
	self:SetCountDownTime(time)
end

function KuafuYeZhanWangChengFollow:SetCountDownTime(time, is_start)
	if self.node_list.next_time_bg then
		if time < 10 then
			self.node_list.next_time_bg:SetActive(false)
			self.node_list["cir_time"]:SetActive(true)
			self.node_list["text_progress_number"].text.text = time
		else
			self.node_list["cir_time"]:SetActive(false)
			self.node_list.next_time_bg:SetActive(true)
			time = TimeUtil.FormatSecond(time, 2)
			local str = is_start and Language.YeZhanWangCheng.TimeCountDown or Language.YeZhanWangCheng.TimeCountDown1
			self.node_list.label_next.text.text = str
			self.node_list.label_next_time.text.text = time
		end
	end
end

function KuafuYeZhanWangChengFollow:OnClickSeeGift()
	KuafuYeZhanWangChengWGCtrl.Instance:OpenSeeGiftView()
end

function KuafuYeZhanWangChengFollow:OnClickGetRank()
	if KuafuYeZhanWangChengWGCtrl.Instance:CheckActiIsOpen() then
		ViewManager.Instance:Open(GuideModuleName.ZCRankView, nil, ZCRankView.FromView.YZWC, {})
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.YeZhanWangCheng.NoRank)
	end
end

function KuafuYeZhanWangChengFollow:OnClickGetResult(i)
	if not self.turn_index then
		return
	end

	local node = self.node_list["zc_result_1"]
	if node then
		node:SetActive(false)
	end
	ViewManager.Instance:Open(GuideModuleName.ZCResultView, nil, ZCResultView.FromView.YZWC, {
		stage = self.turn_index
	})
	self.turn_index = nil
end

function KuafuYeZhanWangChengFollow:ShowResult(turn)
	--[[local node = self.node_list["zc_result_" .. turn]
	if node then
		node:SetActive(true)
	end--]]
	if turn >= MAX_TURN then
		ViewManager.Instance:Open(GuideModuleName.ZCResultView, nil, ZCResultView.FromView.YZWC, {
			stage = MAX_TURN
		})
		self:CloseTimeBg()
	else
		self.turn_index = turn
		self.node_list["zc_result_1"]:SetActive(true)
	end
end

function KuafuYeZhanWangChengFollow:CloseTimeBg()
	if self.node_list["cir_time"] then
		self.node_list["cir_time"]:SetActive(false)
	end
	if self.node_list.next_time_bg then
		self.node_list.next_time_bg:SetActive(false)
	end
end

-- 当buff还未刷新时，需要展示剩余刷新时间  buff刷新后，按钮文字需要变化
-- 若buff还未被拾取，中间展示王冠标记，点击效果为前往拾取
-- 若buff已经被人拾取，中间拾取的玩家头像，点击效果则为攻击拾取buff的玩家
function KuafuYeZhanWangChengFollow:FlushSpecialBuffBtnState()
	local cur_turn = KuafuYeZhanWangChengWGData.Instance:GetCurTurn()

	if cur_turn < 0 then
		self.node_list.btn_refresh_buff:CustomSetActive(false)
		self.node_list.btn_qiangduo_buff:CustomSetActive(false)
		self.node_list.btn_goto_buff:CustomSetActive(false)
		return
	end

	local refresh_time = KuafuYeZhanWangChengWGData.Instance:GetTianXuanBuffRefreshTime()
	local role_uid = KuafuYeZhanWangChengWGData.Instance:GetTianXuanBuffRoleUid()
	local buff_get_time = KuafuYeZhanWangChengWGData.Instance:GetTianXuanBuffGetTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()

	if CountDownManager.Instance:HasCountDown(BUFF_REFRESH) then
		CountDownManager.Instance:RemoveCountDown(BUFF_REFRESH)
	end

	if CountDownManager.Instance:HasCountDown(BUFF_GET) then
		CountDownManager.Instance:RemoveCountDown(BUFF_GET)
	end

	-- 未刷新
	if server_time < refresh_time then
		self.node_list.btn_refresh_buff:CustomSetActive(true)
		self.node_list.btn_qiangduo_buff:CustomSetActive(false)
		self.node_list.btn_goto_buff:CustomSetActive(false)
		self:FlushBuffRefreshState(refresh_time, server_time)
	elseif buff_get_time <= 0 and refresh_time > 0 and server_time >= refresh_time and role_uid <= 0 then
		-- 已经刷新
		self.node_list.btn_refresh_buff:CustomSetActive(false)
		self.node_list.btn_qiangduo_buff:CustomSetActive(false)
		self.node_list.btn_goto_buff:CustomSetActive(true)
	elseif buff_get_time > 0 and server_time >= refresh_time and role_uid > 0 then
		-- 掠夺
		self.node_list.btn_refresh_buff:CustomSetActive(false)
		self.node_list.btn_qiangduo_buff:CustomSetActive(true)
		self.node_list.btn_goto_buff:CustomSetActive(false)

		self:FlushBuffLueDuoState(buff_get_time, server_time)
	end
end

function KuafuYeZhanWangChengFollow:FlushBuffRefreshState(refresh_time, server_time)
	self.node_list.desc_time.text.text = string.format(Language.YeZhanWangCheng.BuffRefreshTimeCountDown, TimeUtil.FormatSecondDHM9(math.floor(refresh_time - server_time)))
	-- self.node_list.refresh_pro.image.fillAmount = 
	CountDownManager.Instance:AddCountDown(BUFF_REFRESH, 
		function (elapse_time, total_time)
			if self.node_list.desc_time then
				local time = math.floor(total_time - elapse_time)
				self.node_list.desc_time.text.text = string.format(Language.YeZhanWangCheng.BuffRefreshTimeCountDown, TimeUtil.FormatSecondDHM9(time))
			end
		end, 
		function ()
			-- if self.node_list.desc_time then
			-- 	self.node_list.desc_time.text.text = ""
			-- end
		end,
	refresh_time, nil, 1)
end

function KuafuYeZhanWangChengFollow:FlushBuffLueDuoState(buff_get_time, server_time)
	local head_data = KuafuYeZhanWangChengWGData.Instance:GetRoleHeadData()

	if not IsEmptyTable(head_data) then
		if not self.role_head_cell then
			self.role_head_cell = BaseHeadCell.New(self.node_list["role_head"])
		end
		self.role_head_cell:SetData(head_data)
		self.node_list["role_head"]:CustomSetActive(true)
	else
		self.node_list["role_head"]:CustomSetActive(false)
	end

	local role_id = RoleWGData.Instance:GetRoleVo().role_id
	local uid = KuafuYeZhanWangChengWGData.Instance:GetTianXuanBuffRoleUid()
	self.node_list.desc_qiangduo.text.text = role_id == uid and Language.YeZhanWangCheng.BuffIsMineGet or Language.YeZhanWangCheng.BuffIsRefreshGoGet

	local tianxuan_buff_time = KuafuYeZhanWangChengWGData.Instance:GetTianXuanBuffTime()
	local get_time = server_time - buff_get_time
	local time_count = tianxuan_buff_time - get_time

	if time_count > 0 then
		self.node_list.img_pro.image.fillAmount = get_time / tianxuan_buff_time

		CountDownManager.Instance:AddCountDown(BUFF_GET, 
		function (elapse_time, total_time)
			if self.node_list.img_pro then
				self.node_list.img_pro.image.fillAmount = (get_time + elapse_time) / tianxuan_buff_time
			end
		end, 
		function ()
			self.node_list.img_pro.image.fillAmount = 1
		end,
		nil, 
		time_count, 
		1)
	end
end

-- 点击夺取BUFF
function KuafuYeZhanWangChengFollow:OnClickQiangDuoBuff()
	local role_id = RoleWGData.Instance:GetRoleVo().role_id
	local uid = KuafuYeZhanWangChengWGData.Instance:GetTianXuanBuffRoleUid()

	if role_id == uid then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.YeZhanWangCheng.BuffIsMineGet)
	else
		local obj = Scene.Instance:GetRoleByRoleId(uid)

		if obj then
			GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
	
			local guaji_state = GuajiCache.guaji_type == GuajiType.Auto
			GuajiWGCtrl.Instance:CancelSelect()
			GuajiWGCtrl.Instance:StopGuaji()
			GuajiWGCtrl.Instance:ClearTemporary()
			MoveCache.SetEndType(MoveEndType.AttackTarget)
			GuajiCache.target_obj = obj
			GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj, SceneTargetSelectType.SCENE)
			if not guaji_state then
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.NotInHorizon)
		end
	end
end

-- 正在刷新buff
function KuafuYeZhanWangChengFollow:OnClickRefreshBuff()
	
end

-- 去获得BUFF
function KuafuYeZhanWangChengFollow:OnClickGoToBuff()
	local pos_x, pos_y = KuafuYeZhanWangChengWGData.Instance:GetBuffPosData()
	local cur_scene_id = Scene.Instance:GetSceneId()
	local scene_type = Scene.Instance:GetSceneType()

	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
	GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end)
	GuajiWGCtrl.Instance:MoveToPos(cur_scene_id, pos_x, pos_y, 0, nil, nil, nil, nil, nil, nil, true)
end

-----------------------------------------------------------------------------
------------------------排行ItemRender---------------------------------------
-----------------------------------------------------------------------------
-- 排行单元
YeZhanWangChengRankItemRender = YeZhanWangChengRankItemRender or BaseClass(BaseRender)
-- function YeZhanWangChengRankItemRender:__init()
-- 	-- self.lbl_rank = self.node_list.lbl_rank
-- 	-- self.lbl_name = self.node_list.lbl_name
-- 	-- self.lbl_time = self.node_list.lbl_time
-- end

-- function YeZhanWangChengRankItemRender:__delete()
-- 	-- self.lbl_rank = nil
-- 	-- self.lbl_name = nil
-- 	-- self.lbl_time = nil
-- end

function YeZhanWangChengRankItemRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.lbl_rank.text.text = self.data.rank >= 4 and self.data.rank or ""
	self.node_list.lbl_name.text.text = BiZuoWGData.Instance:GetSetverNameFormat(self.data.user_name)
	local bg_bundle, bg_asset = ResPath.GetCommonImages("a3_hurt_list_bg_4")

	if self.data.rank < 4 then    
		bg_bundle, bg_asset = ResPath.GetCommonImages("a3_hurt_list_bg_" .. self.data.rank)
		self.node_list["img_rank"]:SetActive(true)
		self.node_list["img_rank"].image:LoadSprite(ResPath.GetCommonImages("a3_hurt_list_rank_" .. self.data.rank))
	else
		self.node_list["img_rank"]:SetActive(false)
    end

	self.node_list["bg"].image:LoadSprite(bg_bundle, bg_asset)
	self.node_list.lbl_time.text.text = self.data.score
	self.node_list.flag_tianxuan_buff:CustomSetActive(self.data.is_tianxuan_buff == 1)

	-- self.node_list.img_rank:SetActive(self.data.rank <= 3)
	-- if self.data.rank <= 3 then
	-- 	self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. self.data.rank))
	-- end
	-- self.lbl_rank:SetActive(self.data.rank > 3)
	-- self.lbl_rank.text.text = self.data.rank

	-- local user_name = BiZuoWGData.Instance:GetSetverNameFormat(self.data.user_name)
	-- -- local color = self.data.is_red_side == 1 and COLOR3B.D_RED or COLOR3B.D_BLUE
	-- self.lbl_name.text.text = user_name -- ToColorStr(user_name,color)
	-- self.lbl_time.text.text = self.data.score

	-- self.node_list.flag_tianxuan_buff:CustomSetActive(self.data.is_tianxuan_buff == 1)
end

function YeZhanWangChengRankItemRender:CreateSelectEffect()

end

---------------------------------------------------------------------------------------------------------------
------------btn_reawrd_view----------查看奖励 Begin
---------------------------------------------------------------------------------------------------------------

SeeYeZhanWangChengRewardView = SeeYeZhanWangChengRewardView or BaseClass(SafeBaseView)

function SeeYeZhanWangChengRewardView:__init()
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(830, 560)})
	self:AddViewResource(0, "uis/view/kuafu_yezhanwangcheng_ui_prefab", "layout_yzwc_reward_preview")
	self.view_layer = UiLayer.Pop
end

function SeeYeZhanWangChengRewardView:__delete()

end

function SeeYeZhanWangChengRewardView:ReleaseCallBack()
	if self.luandou_reward_list1 then
		self.luandou_reward_list1:DeleteMe()
		self.luandou_reward_list1 = nil
	end

	if self.luandou_reward_list2 then
		self.luandou_reward_list2:DeleteMe()
		self.luandou_reward_list2 = nil
	end
end

function SeeYeZhanWangChengRewardView:LoadCallBack()
	--self:SetSecondView(nil, self.node_list["size"])
	self.node_list.title_view_name.text.text = Language.YeZhanWangCheng.RewardPreView
	self.node_list["tabbar_1"].toggle:AddClickListener(BindTool.Bind(self.OnClickbar, self, 1))
	self.node_list["tabbar_2"].toggle:AddClickListener(BindTool.Bind(self.OnClickbar, self, 2))
	self.node_list["tabbar_3"].toggle:AddClickListener(BindTool.Bind(self.OnClickbar, self, 3))
	self.luandou_reward_list1 = AsyncListView.New(SeeYeZhanWangChengRewardItemRender, self.node_list.list_1)
	self.luandou_reward_list2 = AsyncListView.New(SeeYeZhanWangChengRewardItemRender1, self.node_list.list_2)
	self.luandou_reward_list3 = AsyncListView.New(YeZhanWangChengGetRewardItemRender, self.node_list.list_3)
	self.bar_index = 1
end

function SeeYeZhanWangChengRewardView:ShowIndexCallBack()
	self:Flush()
end

function SeeYeZhanWangChengRewardView:OnClickbar(index)
	self.bar_index = index
	self:Flush()
end

function SeeYeZhanWangChengRewardView:OnFlush()
	local role_info = KuafuYeZhanWangChengWGData.Instance:GetSetSelfInfo()
	local rank = ""
	local data
	self.node_list.root_1:SetActive(self.bar_index == 1)
	self.node_list.root_2:SetActive(self.bar_index == 2)
	self.node_list.root_3:SetActive(self.bar_index == 3)
	self.node_list["txt_camp_tip"]:SetActive(self.bar_index == 1)
	self.node_list["txt_per_tip"]:SetActive(self.bar_index == 2)
	self.node_list["txt_count_tip"]:SetActive(self.bar_index == 3)
	if self.bar_index == 2 then
		local self_info = KuafuYeZhanWangChengWGData.Instance:GetRewardRoleInfoByUserKey(role_info.user_key)
		if self_info and self_info.rank then
			rank = string.format(Language.YeZhanWangCheng.Rank3, self_info.rank)
		else
			rank = Language.YeZhanWangCheng.Rank4
		end
		data = KuafuYeZhanWangChengWGData.Instance:GetPersonalReward() or {}
		self.luandou_reward_list2:SetDataList(data)
	elseif self.bar_index == 1 then
		data = KuafuYeZhanWangChengWGData.Instance:GetCampReward() or {}
		self.luandou_reward_list1:SetDataList(data)
	elseif self.bar_index == 3 then
		data = KuafuYeZhanWangChengWGData.Instance:GetCountRewardDataList()
		self.luandou_reward_list3:SetDataList(data)
	end

	self.node_list.txt_rank.text.text = rank
	local enter_count = KuafuYeZhanWangChengWGData.Instance:GetYZWCEnterCount()
	self.node_list.txt_cur_enter_count.text.text = string.format(Language.YeZhanWangCheng.EnterCount, enter_count)

	-- 红点
	local has_red = KuafuYeZhanWangChengWGData.Instance:GetYeZhanWangChengHasReward()
	self.node_list.times_reward_red:SetActive(has_red)
end

-------------------------------------------------------
-- 阵营奖励ItemRender
-------------------------------------------------------
SeeYeZhanWangChengRewardItemRender = SeeYeZhanWangChengRewardItemRender or BaseClass(BaseRender)

function SeeYeZhanWangChengRewardItemRender:__init()
end

function SeeYeZhanWangChengRewardItemRender:__delete()
	if self.rewarditem_cells1 then
		self.rewarditem_cells1:DeleteMe()
		self.rewarditem_cells1 = nil
	end
end

function SeeYeZhanWangChengRewardItemRender:LoadCallBack()
	self.rewarditem_cells1 = AsyncListView.New(ItemCell, self.node_list.reward_list)
end

function SeeYeZhanWangChengRewardItemRender:OnFlush()
	local sort_fun = function(a, b)
		if a and b and a.item_id and b.item_id then
			local a_cfg = ItemWGData.Instance:GetItemConfig(a.item_id)
			local b_cfg = ItemWGData.Instance:GetItemConfig(b.item_id)
			return a_cfg.color > b_cfg.color
		end
	end

	local data = TableCopy(self.data)
	local t = data[0]
	table.insert(data, 1, t)
	table.sort(data, sort_fun)
	self.rewarditem_cells1:SetDataList(data)
	self.node_list["desc"].text.text = self.index
	--self.node_list["Text_1"].text.text = self.data.is_win == 1 and Language.YeZhanWangCheng.WinCamp or Language.YeZhanWangCheng.LoseCamp

	-- local bundle, asset = ResPath.GetRawImagesPNG("a2_zzjd_sbjl_" .. self.index)
	-- self.node_list.root_1.raw_image:LoadSprite(bundle, asset, function()
	-- 	self.node_list.root_1.raw_image:SetNativeSize()
	-- end)
end

function SeeYeZhanWangChengRewardItemRender:CreateSelectEffect()

end

-------------------------ItemRender------------------------------ 查看奖励
SeeYeZhanWangChengRewardItemRender1 = SeeYeZhanWangChengRewardItemRender1 or BaseClass(BaseRender)

function SeeYeZhanWangChengRewardItemRender1:__init()
end

function SeeYeZhanWangChengRewardItemRender1:__delete()
	if self.rewarditem_cells2 then
		self.rewarditem_cells2:DeleteMe()
		self.rewarditem_cells2 = nil
	end
end

function SeeYeZhanWangChengRewardItemRender1:LoadCallBack()
	self.rewarditem_cells2 = AsyncListView.New(ItemCell, self.node_list.reward_list)
end

function SeeYeZhanWangChengRewardItemRender1:OnFlush()
	local sort_fun = function(a, b)
		if a and b and a.item_id and b.item_id then
			local a_cfg = ItemWGData.Instance:GetItemConfig(a.item_id)
			local b_cfg = ItemWGData.Instance:GetItemConfig(b.item_id)
			return a_cfg.color > b_cfg.color
		end
	end

	if self.data.reward_item then
		local data = TableCopy(self.data.reward_item)
		local t = data[0]
		table.insert(data, 1, t)
		table.sort(data, sort_fun)
		self.rewarditem_cells2:SetDataList(data)
	end

	-- self.node_list.img_rank:SetActive(self.index <= 3)
	-- if self.index <= 3 then
	-- 	self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. self.index))
	-- 	self.node_list.bg.image:LoadSprite(ResPath.GetCommonImages("a2_xm_di_dj" .. self.index))
	-- 	self.node_list.bg:SetActive(true)
	-- else
	-- 	self.node_list.bg.image:LoadSprite(ResPath.GetCommonImages("a2_ty_xxd_bt_2"))
	-- 	self.node_list.bg:SetActive(self.index % 2 ~= 0)
	-- end

	if self.data.min_rank then
		local str
		if self.data.min_rank == self.data.max_rank then
			str = self.data.min_rank + 1 --string.format(Language.YeZhanWangCheng.Rank1, self.data.min_rank + 1)
		else
			str = string.format(Language.YeZhanWangCheng.Rank2, self.data.min_rank + 1, self.data.max_rank + 1)
		end
		self.node_list["desc"].text.text = str
	end
end

function SeeYeZhanWangChengRewardItemRender1:CreateSelectEffect()

end
---------------------------查看奖励 End-----------------------------

------------------------------
-- 次数奖励Render
------------------------------ 
YeZhanWangChengGetRewardItemRender = YeZhanWangChengGetRewardItemRender or BaseClass(BaseRender)

function YeZhanWangChengGetRewardItemRender:__init()
end

function YeZhanWangChengGetRewardItemRender:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function YeZhanWangChengGetRewardItemRender:LoadCallBack()
	self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
	XUI.AddClickEventListener(self.node_list.btn_get_reward, BindTool.Bind1(self.OnClickGetRrewardBtn, self))
end

function YeZhanWangChengGetRewardItemRender:OnFlush()
	local sort_fun = function(a, b)
		if a and b and a.item_id and b.item_id then
			local a_cfg = ItemWGData.Instance:GetItemConfig(a.item_id)
			local b_cfg = ItemWGData.Instance:GetItemConfig(b.item_id)
			return a_cfg.color > b_cfg.color
		end
	end
	if self.data.cfg.count_reward then
		local data = TableCopy(self.data.cfg.count_reward)
		local t = data[0]
		table.insert(data, 1, t)
		table.sort(data, sort_fun)
		self.reward_list:SetDataList(data)
	end

	self.node_list["desc"].text.text = self.data.cfg.battle_count

	local enter_count = KuafuYeZhanWangChengWGData.Instance:GetYZWCEnterCount()
	local has_reward = self.data.reward_flag == 1
	self.node_list.reward_flag:SetActive(has_reward)
	self.node_list.btn_get_reward:SetActive(self.data.can_fetch)
end

function YeZhanWangChengGetRewardItemRender:OnClickGetRrewardBtn()
	KuafuYeZhanWangChengWGCtrl.Instance:SendEnterCountReward(self.data.index)
end