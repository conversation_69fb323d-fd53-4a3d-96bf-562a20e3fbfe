local reverse_circle = Vector3(0, 0, -360)

local attr_color_list = {
    [1] = {color = COLOR3B.D_GREEN, icon = "a2_bs_tbls"},
    [2] = {color = COLOR3B.D_BLUE, icon = "a2_bs_tblans"},
    [3] = {color = COLOR3B.D_PURPLE, icon = "a2_bs_tbzs"},
    [4] = {color = COLOR3B.D_ORANGE, icon = "a2_bs_tbcs"},
    [5] = {color = COLOR3B.D_RED, icon = "a2_bs_tbhongs"},
}

function MultiFunctionView:ShowDaoHangKaiGuangCallBack()
    self:RightPanleShowTween(self.node_list.kaiguang_right_tween_root, self.node_list.kaiguang_mid)

    -- local rotate_mode = DG.Tweening.RotateMode.FastBeyond360
	-- if self.tween_kaiguang_circle1 then
    --     self.tween_kaiguang_circle1:Restart()
    -- else
    --     local end_v = self.node_list.kaiguang_circle1.transform.localEulerAngles + reverse_circle
    --     self.tween_kaiguang_circle1 = self.node_list.kaiguang_circle1.transform:DORotate(end_v, self.tween_info.kaiguang_circle1_rotate, rotate_mode)
    --     self.tween_kaiguang_circle1:SetEase(DG.Tweening.Ease.Linear)
    --     self.tween_kaiguang_circle1:SetLoops(-1)
    -- end
end

function MultiFunctionView:LoadDaoHangKaiGuangCallBack()
    if not self.kaiguang_target_item then
        self.kaiguang_target_item = ItemCell.New(self.node_list.kaiguang_target_item)
        self.kaiguang_target_item:SetItemTipFrom(ItemTip.FROM_DAOHANG_EQUIP)
    end

    if not self.kaiguang_equip_list then
        self.kaiguang_equip_list = AsyncListView.New(DaoHangKaiGuangEquipItemRender, self.node_list.kaiguang_equip_list)
        self.kaiguang_equip_list:SetSelectCallBack(BindTool.Bind(self.OnSelectKaiGuangEquipItemHandler, self))
    end

    if not self.kaiguang_slider_list then
        self.kaiguang_slider_list = {}

        for i = 0, 4 do
            self.kaiguang_slider_list[i] = DaoHangKaiGuangSliderItemRender.New(self.node_list["slider_shuxing_" .. i])
        end
    end

    if not self.kaiguang_cost_item then
        self.kaiguang_cost_item = ItemCell.New(self.node_list.kaiguang_cost_item)
    end

    if not self.kaiguang_lucky_item then
        self.kaiguang_lucky_item = ItemCell.New(self.node_list.kaiguang_lucky_item)
    end

    self.daohang_kaiguang_select = -1
    self.daohang_kaiguang_select_slot = -1
	self.daohang_kaiguang_equip_data = {}
    self.daohang_kaiguang_add_cap = false

    self.node_list.kaiguang_lock_toggle.toggle.isOn = MultiFunctionWGData.Instance:GetDaoHangKaiGuangCostState()
    self.node_list.kaiguang_lock_toggle.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickExtraCost, self))

    XUI.AddClickEventListener(self.node_list.btn_kaiguang, BindTool.Bind(self.OnClickKaiGuang, self))
    XUI.AddClickEventListener(self.node_list.btn_kaiguang_accept, BindTool.Bind(self.OnClickKaiGuangAccept, self))
    XUI.AddClickEventListener(self.node_list.btn_kaiguang_back, BindTool.Bind(self.OnClickKaiGuangBack, self))
end

function MultiFunctionView:ReleaseKaiGuangCallBack()
    if self.kaiguang_target_item then
        self.kaiguang_target_item:DeleteMe()
        self.kaiguang_target_item = nil
    end

    if self.kaiguang_equip_list then
        self.kaiguang_equip_list:DeleteMe()
        self.kaiguang_equip_list = nil
    end

    if self.kaiguang_slider_list then
        for k, v in pairs(self.kaiguang_slider_list) do
            v:DeleteMe()
        end

        self.kaiguang_slider_list = nil
    end

    if self.kaiguang_cost_item then
        self.kaiguang_cost_item:DeleteMe()
        self.kaiguang_cost_item = nil
    end

    if self.kaiguang_lucky_item then
        self.kaiguang_lucky_item:DeleteMe()
        self.kaiguang_lucky_item = nil
    end

    self:DeleteDaoHangKaiGuangAnim()
end

function MultiFunctionView:DeleteDaoHangKaiGuangAnim()
    -- if self.tween_kaiguang_circle1 then
    --     self.tween_kaiguang_circle1:Kill()
    --     self.tween_kaiguang_circle1 = nil
    -- end
end

function MultiFunctionView:OnClickExtraCost()
    local state = not MultiFunctionWGData.Instance:GetDaoHangKaiGuangCostState()
    MultiFunctionWGData.Instance:SetDaoHangKaiGuangCostState(state)
    self.node_list.kaiguang_lucky_item:CustomSetActive(state)
end

function MultiFunctionView:OnFlushDaoHangKaiGuang()
    local data_list = MultiFunctionWGData.Instance:GetDaoHangEquipedEquip()
    local select = self:GetDaoHangKaiGuangSelect(data_list)
    local state = MultiFunctionWGData.Instance:GetDaoHangKaiGuangCostState()

    self.kaiguang_equip_list:SetDataList(data_list)
    self.kaiguang_equip_list:JumpToIndex(select)
    self.node_list.kaiguang_lucky_item:CustomSetActive(state)

    local other_cfg = MultiFunctionWGData.Instance:GetDaoHangOtherCfg()
    if not IsEmptyTable(other_cfg) then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.kaiguang_item_id)
        local enough = item_num >= 1
        self.kaiguang_cost_item:SetFlushCallBack(function ()
            local right_text = ToColorStr(item_num .. "/" .. 1, enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
            self.kaiguang_cost_item:SetRightBottomColorText(right_text)
            self.kaiguang_cost_item:SetRightBottomTextVisible(true)
        end)
        self.kaiguang_cost_item:SetData({item_id = other_cfg.kaiguang_item_id})

        local lucky_item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.luck_item_id)
        local enough = lucky_item_num >= 1
        self.kaiguang_lucky_item:SetFlushCallBack(function ()
            local right_text = ToColorStr(lucky_item_num .. "/" .. 1, enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
            self.kaiguang_lucky_item:SetRightBottomColorText(right_text)
            self.kaiguang_lucky_item:SetRightBottomTextVisible(true)
        end)
        self.kaiguang_lucky_item:SetData({item_id = other_cfg.luck_item_id})
    end

    local cap = MultiFunctionWGData.Instance:GetDaoHangKaiGuangCap()
    self.node_list.kaiguang_cap_value.text.text = cap or 0
end

function MultiFunctionView:GetDaoHangKaiGuangSelect(data_list)
    if not IsEmptyTable(self.daohang_kaiguang_equip_data) and self.daohang_kaiguang_select > 0 then
        local remind = MultiFunctionWGData.Instance:GetDaoHangKaiGuangEquipRemindByslot(self.daohang_kaiguang_select_slot)
        local slot_data = MultiFunctionWGData.Instance:GetDaoHangSlotItemDataBySlot(self.daohang_kaiguang_select_slot)
        local has_change_value = slot_data and slot_data.has_kaiguang_change_value or false

        if remind or has_change_value then
            return self.daohang_kaiguang_select
        end
    end

    if not IsEmptyTable(data_list) then
        for k, v in pairs(data_list) do
            if MultiFunctionWGData.Instance:GetDaoHangKaiGuangEquipRemindByslot(v.slot) then
                return k
            end
         end
    end
    
    self.daohang_kaiguang_select = self.daohang_kaiguang_select > 0 and self.daohang_kaiguang_select or 1
    return self.daohang_kaiguang_select
end

function MultiFunctionView:OnClickKaiGuang()
    local other_cfg = MultiFunctionWGData.Instance:GetDaoHangOtherCfg()
    local use_lucky_item = 0

    if not IsEmptyTable(other_cfg) then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.kaiguang_item_id)

        if item_num <= 0 then
            TipWGCtrl.Instance:OpenItem({item_id = other_cfg.kaiguang_item_id})
            return
        end

        if MultiFunctionWGData.Instance:GetDaoHangKaiGuangCostState() then
            use_lucky_item = 1
            local lucky_item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.luck_item_id)

            if lucky_item_num <= 0 then
                TipWGCtrl.Instance:OpenItem({item_id = other_cfg.luck_item_id})
                return
            end
        end

        MultiFunctionWGCtrl.Instance:OnCSTaoistOperate(TAOIST_OPERATE_TYPE.SLOT_KAIGUANG, self.daohang_kaiguang_select_slot, use_lucky_item)
	end
end

function MultiFunctionView:OnClickKaiGuangAccept()
    local ok_func = function(...)
        MultiFunctionWGCtrl.Instance:OnCSTaoistOperate(TAOIST_OPERATE_TYPE.KAIGUANG_RETURN, self.daohang_kaiguang_select_slot, 1)
    end

    if self.daohang_kaiguang_add_cap then
        ok_func()
    else
        TipWGCtrl.Instance:OpenAlertTips(Language.Charm.DaoHangKaiGuangCapDownDesc, ok_func, nil, nil, nil, nil, nil, nil, nil, Language.Charm.DaoHangKaiGuangCapDownConfirm)
    end
end

function MultiFunctionView:OnClickKaiGuangBack()
    MultiFunctionWGCtrl.Instance:OnCSTaoistOperate(TAOIST_OPERATE_TYPE.KAIGUANG_RETURN, self.daohang_kaiguang_select_slot, 0)
end

function MultiFunctionView:OnSelectKaiGuangEquipItemHandler(item)
	if nil == item or nil == item.data then
		return
	end

    self.kaiguang_target_item:SetData(item.data)
    self.daohang_kaiguang_select = item.index
    self.daohang_kaiguang_select_slot = item.data.slot
	self.daohang_kaiguang_equip_data = item.data

    self:FlushKaiGuangRight()
end

function MultiFunctionView:FlushKaiGuangRight()
    local is_max_attr, has_change_value, data_list = self:GetDaoHangSliderDataList()
    local remind = MultiFunctionWGData.Instance:GetDaoHangKaiGuangEquipRemindByslot(self.daohang_kaiguang_select_slot)

    for i = 0, 4 do
        self.kaiguang_slider_list[i]:SetData(data_list[i])
    end

    local other_cfg = MultiFunctionWGData.Instance:GetDaoHangOtherCfg()
    local _, item_color = ItemWGData.Instance:GetItemColor(self.daohang_kaiguang_equip_data.item_id)
    local max_kaiguang_quality = other_cfg.max_kaiguang_quality
    local can_kaiguang = item_color >= other_cfg.max_kaiguang_quality

    self.node_list.cannot_kaiguang:CustomSetActive(not can_kaiguang)
    self.node_list.kaiguang_full_attr_flag:CustomSetActive(can_kaiguang and is_max_attr)
    self.node_list.kaiguang_result:CustomSetActive(can_kaiguang and not is_max_attr and has_change_value)
    self.node_list.kaiguang_cost:CustomSetActive(can_kaiguang and not is_max_attr and not has_change_value)
    self.node_list.btn_kaiguang_remind:CustomSetActive(remind)

    if not can_kaiguang then
        local color_str = ToColorStr(Language.Common.ColorName[max_kaiguang_quality], ITEM_COLOR[max_kaiguang_quality])
        self.node_list.cannot_kaiguang_tip.text.text = string.format(Language.Charm.DaoHangKaiGuangLimit, color_str)
    end

    if not is_max_attr and has_change_value then
        local is_add, cap_desc = MultiFunctionWGData.Instance:GetDaoHangkaiGuangValueChangeDesc(self.daohang_kaiguang_select_slot)
        self.node_list.text_kaiguang_result.text.text = string.format(Language.Charm.DaoHangKaiGuangCapDesc, cap_desc)
        self.daohang_kaiguang_add_cap = is_add
    end
end

function MultiFunctionView:GetDaoHangSliderDataList()
    local is_max_attr = true
    local has_change_value = false
    local data_list = {}
    local quality_cfg = MultiFunctionWGData.Instance:GetDaoHangkaiGuangAttrLimitCfg(self.daohang_kaiguang_equip_data.color)

    for i = 0, 4 do
        local data = {}
        data.slot = self.daohang_kaiguang_select_slot
        data.seq = i
        data.cur_value = (self.daohang_kaiguang_equip_data.per_kaiguang_attr_value_list or {})[i] or 0
        data.next_value = (self.daohang_kaiguang_equip_data.per_kaiguang_attr_value_list_temp or {})[i] or 0
        data.is_add = data.next_value > 0
        data.is_decrease = data.next_value < 0
        data.is_max_attr = data.cur_value >= quality_cfg.limit
        data.is_change = data.next_value ~= 0
        data.limit_value = quality_cfg.limit

        local attr_color_id = MultiFunctionWGData.Instance:GetDaoHangKaiGuangAttrRange(self.daohang_kaiguang_equip_data.color, i + 1)
        data.name_color = (attr_color_list[attr_color_id] or {}).color or COLOR3B.D_WHITE

        if not data.is_max_attr then
            is_max_attr = false
        end

        if data.is_change then
            has_change_value = true
        end

        data_list[i] = data
    end

    return is_max_attr, has_change_value, data_list
end

---------------------------------DaoHangKaiGuangEquipItemRender--------------------------------
DaoHangKaiGuangEquipItemRender= DaoHangKaiGuangEquipItemRender or BaseClass(BaseRender)

function DaoHangKaiGuangEquipItemRender:LoadCallBack()
    if not self.item then
        self.item = ItemCell.New(self.node_list.item)
        self.item:SetItemTipFrom(ItemTip.FROM_DAOHANG_EQUIP)
    end
end

function DaoHangKaiGuangEquipItemRender:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function DaoHangKaiGuangEquipItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local quality = self.data.color
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    local name = item_cfg and item_cfg.name or ""
    local remind = MultiFunctionWGData.Instance:GetDaoHangKaiGuangEquipRemindByslot(self.data.slot)
    
    self.node_list.remind:CustomSetActive(remind)
    self.node_list.text.text.text = name
    self.node_list.text_hl.text.text = name
    self.item:SetData(self.data)

    for i = 0, 4 do
        local value = (self.data.per_kaiguang_attr_value_list or {})[i] or 0
        local attr_color_id = MultiFunctionWGData.Instance:GetDaoHangKaiGuangAttrRange(quality, value)
        local need_load_sprite = attr_color_id > 0 and not IsEmptyTable(attr_color_list[attr_color_id])

        if need_load_sprite then
            local bundel, asset = ResPath.GetMultiFunctionImg(attr_color_list[attr_color_id].icon)
            self.node_list["store_icon" .. i].image:LoadSprite(bundel, asset, function ()
                self.node_list["store_icon" .. i].image:SetNativeSize()
            end)
        end

        self.node_list["store_icon" .. i]:CustomSetActive(need_load_sprite)
    end
end

function DaoHangKaiGuangEquipItemRender:OnSelectChange(is_select)
    self.node_list["bg"]:CustomSetActive(not is_select)
    self.node_list["bg_hl"]:CustomSetActive(is_select)
end

----------------------------------DaoHangKaiGuangSliderItemRender------------------------------
DaoHangKaiGuangSliderItemRender = DaoHangKaiGuangSliderItemRender or BaseClass(BaseRender)

function DaoHangKaiGuangSliderItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local data = self.data
    local is_add = data.is_add
    local is_decrease = data.is_decrease
    local is_change = data.is_change
    local is_max_attr = data.is_max_attr
    local red_slider_value = 0
    local green_slider_value = 0
    local orange_slider_value = 0
    local green_slider_target_value = 0
    local orange_slider_targetA_value = 0
    local value = data.cur_value / data.limit_value
 
    if is_max_attr and not is_change then
        orange_slider_value = 1
    else
        orange_slider_value = value

        if is_change then
            local target_value = (data.cur_value + data.next_value) / data.limit_value

            if is_add then
                green_slider_value = value
                green_slider_target_value = target_value
            else
                red_slider_value = value
                orange_slider_targetA_value = target_value
            end
        end
    end
    
    self.node_list.slider_green.slider.value = green_slider_value
    self.node_list.slider_yellow.slider.value = orange_slider_value
    self.node_list.slider_red.slider.value = red_slider_value

    if green_slider_target_value > 0 then
        self.node_list.slider_green.slider:DOValue(green_slider_target_value, 0.5)
    end

    if orange_slider_targetA_value > 0 then
        self.node_list.slider_yellow.slider:DOValue(orange_slider_targetA_value, 0.5)
    end

    self.node_list.txt_percent_main.text.text = string.format("%0.2f%%", value * 100)
    self.node_list.icon_arrow_up:CustomSetActive(is_add)
    self.node_list.txt_percent1:CustomSetActive(is_add)
    self.node_list.icon_arrow_down:CustomSetActive(is_decrease)
    self.node_list.txt_percent2:CustomSetActive(is_decrease)

    local attr_cfg = MultiFunctionWGData.Instance:GetDaoHangKaiGuangAttrCfg(data.slot, data.seq)
    if not IsEmptyTable(attr_cfg) then
        local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_cfg.attr_id))
        local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str, true)
        attr_name = ToColorStr(attr_name, data.value_color)
        local attr_value = math.ceil(attr_cfg.attr_val * value)
        local value_str  = AttributeMgr.PerAttrValue(attr_str, attr_value)
        local change_value = AttributeMgr.PerAttrValue(attr_str, attr_cfg.attr_val * (math.abs(data.next_value) / data.limit_value))

        self.node_list.txt_base_attr_1.text.text = attr_name
        self.node_list.txt_plus.text.text = value_str

        if is_add then
            self.node_list.txt_percent1.text.text = change_value
        end

        if is_decrease then
            self.node_list.txt_percent2.text.text = change_value
        end
    end
end