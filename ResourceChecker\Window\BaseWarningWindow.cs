﻿#if UNITY_EDITOR
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using Nirvana;
using System;
using UnityEngine.SceneManagement;

public abstract class BaseWarningWindow : EditorWindow
{
    protected string title = "Warning Window";
    protected string notice = string.Empty;
    protected string showText = "太棒啦！未检测出异常";

	private List<CheckObject> list = new List<CheckObject>();
    private UnityEngine.Object selectObj;
    private Vector2 scrollerPos = new Vector2();
    public Action onClose;
    public Action onLostFocus;

    public List<CheckObject> List
    {
        set
        {
            list = value;
            selectObj = null;
            scrollerPos = Vector2.zero;
        }
        get
        {
            return list;
        }
    }

    private void OnGUI()
    {
        // Title
        GUILayout.Space(10f);
        EditorGUILayout.LabelField(
            this.title, GUIWarningWindow.LabelTitleStyle);
        GUILayout.Space(20f);

		var count = list.Count;
		string str = this.notice;
		if (count == 0)
		{
			str = this.showText;
		}

		EditorGUILayout.LabelField(
		str, GUI.skin.label);

		scrollerPos = EditorGUILayout.BeginScrollView(this.scrollerPos);

        if (count > 0)
        {
            EditorGUILayout.LabelField("数量: " + count);
        }

        for (int i = list.Count - 1; i >= 0; --i)
        {
            var obj = list[i];
            GUIStyle style = GUIWarningWindow.LowLevelButton;
            if (obj.warningLevel == WarningLevel.Normal)
                style = GUIWarningWindow.NormalLevelButton;
            else if (obj.warningLevel == WarningLevel.High)
                style = GUIWarningWindow.HighLevelButton;

            if (null != obj.obj)
            {
                if (GUILayout.Button(obj.obj.name + "   " + obj.exception, style))
                {
                    selectObj = obj.obj;
                    if (null != obj.node)
                        PingObj(obj.node);
                    else
                        EditorGUIUtility.PingObject(obj.obj);
                }
            }
            else
            {
                GUILayout.TextArea(obj.exception, style);
            }
        }
        EditorGUILayout.EndScrollView();
    }

    public abstract void Init();

    private void OnDestroy()
    {
        if (null != onClose)
        {
            onClose();
        }
    }

    private void OnLostFocus()
    {
        if (null != onLostFocus)
        {
            onLostFocus();
        }
    }

    protected static GameObject FindInScene(GameObject prefab, GameObject sceneObj)
    {

        var queue = new Queue<Transform>();
        queue.Enqueue(sceneObj.transform);
        while (queue.Count > 0)
        {
            var transform = queue.Dequeue();
            var prefabParent = PrefabUtility.GetPrefabParent(transform.gameObject);
            if (null != prefabParent && prefabParent == prefab)
            {
                return transform.gameObject;
            }
            for (int i = 0; i < transform.childCount; ++i)
            {
                var child = transform.GetChild(i);
                queue.Enqueue(child);
            }
        }
        return null;
    }

    protected static GameObject SearchInGameObject(GameObject root, GameObject target)
    {
        GameObject findObj = null;
        if (null == target.transform.parent)
        {
            findObj = root;
        }
        else
        {
            string path = target.name;
            while (target.transform.parent && target.transform.parent.parent)
            {
                target = target.transform.parent.gameObject;
                path = target.name + "/" + path;
            }
            var findTransform = root.transform.Find(path);
            if (null != findTransform)
                findObj = findTransform.gameObject;
        }
        return findObj;
    }

    protected static void PingObj(GameObject obj)
    {
        var root = obj;
        while (root.transform.parent)
        {
            root = root.transform.parent.gameObject;
        }

        var scene = SceneManager.GetActiveScene();
        var activeObjs = scene.GetRootGameObjects();
        GameObject activeObj = null;
        for (int j = 0; j < activeObjs.Length; ++j)
        {
            if (activeObj = FindInScene(root, activeObjs[j]))
            {
                break;
            }
        }
        if (null != activeObj)
        {
            var target = SearchInGameObject(activeObj, obj);
            EditorGUIUtility.PingObject(target);
        }
        else
        {
            EditorGUIUtility.PingObject(root);
        }
    }
}

public static class GUIWarningWindow
{
    public static GUIStyle LabelTitleStyle { get; private set; }
    public static GUIStyle LowLevelButton { get; private set; }
    public static GUIStyle NormalLevelButton { get; private set; }
    public static GUIStyle HighLevelButton { get; private set; }

    static GUIWarningWindow()
    {
        LabelTitleStyle = new GUIStyle(GUI.skin.label)
        {
            fontSize = 18,
            alignment = TextAnchor.MiddleCenter,
            padding = new RectOffset(0, 0, 0, 0),
        };

        LowLevelButton = new GUIStyle(GUI.skin.button);
        LowLevelButton.normal.background = TextureMaker.Gray(0.27f);
        LowLevelButton.normal.textColor = new Color(0.95f, 0.95f, 0.95f, 1);
        LowLevelButton.hover.background = TextureMaker.Gray(0.35f);
        LowLevelButton.hover.textColor = new Color(0.95f, 0.95f, 0.95f, 1);
        LowLevelButton.active.background = TextureMaker.Gray(0.55f);
        LowLevelButton.active.textColor = new Color(0.95f, 0.95f, 0.95f, 1);

        NormalLevelButton = new GUIStyle(GUI.skin.button);
        NormalLevelButton.normal.background = TextureMaker.Gray(0.27f);
        NormalLevelButton.normal.textColor = new Color(0.95f, 0.95f, 0, 1);
        NormalLevelButton.hover.background = TextureMaker.Gray(0.35f);
        NormalLevelButton.hover.textColor = new Color(0.95f, 0.95f, 0, 1);
        NormalLevelButton.active.background = TextureMaker.Gray(0.55f);
        NormalLevelButton.active.textColor = new Color(0.95f, 0.95f, 0, 1);

        HighLevelButton = new GUIStyle(GUI.skin.button);
        HighLevelButton.normal.background = TextureMaker.Gray(0.27f);
        HighLevelButton.normal.textColor = new Color(0.95f, 0f, 0f, 1);
        HighLevelButton.hover.background = TextureMaker.Gray(0.35f);
        HighLevelButton.hover.textColor = new Color(0.95f, 0f, 0f, 1);
        HighLevelButton.active.background = TextureMaker.Gray(0.55f);
        HighLevelButton.active.textColor = new Color(0.95f, 0f, 0f, 1);
    }
}
#endif