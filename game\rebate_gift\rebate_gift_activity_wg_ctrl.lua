require("game/rebate_gift/rebate_gift_activity_wg_data")
require("game/rebate_gift/rebate_gift_view")
require("game/rebate_gift/rebate_fireworks_view")
require("game/rebate_gift/rebate_fireworks_reward")
require("game/rebate_gift/rebate_fireworks_gailv")
require("game/rebate_gift/rebate_discount_view")

RebateGiftActivityWGCtrl = RebateGiftActivityWGCtrl or BaseClass(BaseWGCtrl)

function RebateGiftActivityWGCtrl:__init()
	if RebateGiftActivityWGCtrl.Instance ~= nil then
		print_error("[RebateGiftActivityWGCtrl] attempt to create singleton twice!")
		return
	end

	RebateGiftActivityWGCtrl.Instance = self
	self.data = RebateGiftActivityWGData.New()
	self.extinct_gift_view = RebateExtinctGiftView.New(GuideModuleName.RebateExtinctGiftView) --绝版赠礼
	self.fire_works_view = RebateFireWorksView.New(GuideModuleName.RebateFireWorksView) --烟花抽奖
	self.fire_works_reward = RebateFireWorksReward.New() --烟花抽奖恭喜获得
	self.fire_works_gailv = RebateFireWorksProbabilityView.New() --烟花抽奖概率展示
	self.discount_view = RebateDiscountView.New(GuideModuleName.RebateDiscountView) --充值立减


	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.OnDayChange, self))
	self:RegisterAllProtocals()
end

function RebateGiftActivityWGCtrl:__delete()
	RebateGiftActivityWGCtrl.Instance = nil

	if self.extinct_gift_view then
		self.extinct_gift_view:DeleteMe()
		self.extinct_gift_view = nil
	end

    if self.fire_works_view then
		self.fire_works_view:DeleteMe()
		self.fire_works_view = nil
	end

	if self.discount_view then
		self.discount_view:DeleteMe()
		self.discount_view = nil
	end

	if self.fire_works_reward then
		self.fire_works_reward:DeleteMe()
		self.fire_works_reward = nil
	end

	if self.fire_works_gailv then
		self.fire_works_gailv:DeleteMe()
		self.fire_works_gailv = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end	

	if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end
end

function RebateGiftActivityWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(SCOARechargeDiscountsInfo, "OnSCOARechargeDiscountsInfo") --充值立减礼包信息
	self:RegisterProtocol(SCOARechargeDiscountsGiftUpdate, "OnSCOARechargeDiscountsGiftUpdate") --充值立减单个礼包信息
	self:RegisterProtocol(SCOAFireworksDrawInfo, "OnSCOAFireworksDrawInfo") --烟花抽奖次数
	self:RegisterProtocol(SCOAFireworksDrawResult, "OnSCOAFireworksDrawResult") --烟花抽奖结果信息
	self:RegisterProtocol(SCOAExtinctGiftInfo, "OnSCOAExtinctGiftInfo") --绝版赠礼活动所有信息
end

--请求信息
function RebateGiftActivityWGCtrl:SendRechargeInfo(act_type, opera_type, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
	protocol.rand_activity_type = act_type
	protocol.opera_type = opera_type
	protocol.param_1 = param1 or 0
	protocol.param_2 = 0
	protocol.param_3 = 0
	protocol:EncodeAndSend()
end

--绝版赠礼活动所有信息
function RebateGiftActivityWGCtrl:OnSCOAExtinctGiftInfo(protocol)
	--print_error("绝版赠礼信息", protocol)
	self.data:SetExtinctGiftInfo(protocol)
	if self.extinct_gift_view:IsOpen() then
		self.extinct_gift_view:Flush()
	end
	RemindManager.Instance:Fire(RemindName.RebateExtinctGift)
end

--充值立减礼包信息
function RebateGiftActivityWGCtrl:OnSCOARechargeDiscountsInfo(protocol)
	--print_error("充值立减礼包信息", protocol)
	self.data:SetDiscountInfo(protocol)
	if self.discount_view:IsOpen() then
		self.discount_view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.RebateDiscount)
end

--充值立减单个礼包信息
function RebateGiftActivityWGCtrl:OnSCOARechargeDiscountsGiftUpdate(protocol)
	--print_error("充值立减单个礼包信息", protocol)
	self.data:SetDiscountOneInfo(protocol)
	self:SendRechargeInfo(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_RECHARGE_DISCOUNTS, RECHARGE_DISCOUNTS_OPERATE_TYPE.INFO)
	if self.discount_view:IsOpen() then
		self.discount_view:Flush()
	end

	local gift_info = self.data:GetDiscountOneInfo()
	if gift_info ~= nil then
		if gift_info.is_random > 0 and gift_info.is_buy < 1 then
			if self.discount_view:IsOpen() then
				self.discount_view:PlayNumberTween(gift_info.random_gold)
			end
		end
	end
	
	RemindManager.Instance:Fire(RemindName.RebateDiscount)
end

--烟花抽奖次数
function RebateGiftActivityWGCtrl:OnSCOAFireworksDrawInfo(protocol)
	--print_error("烟花抽奖次数", protocol)
	self.data:SetFireWorksInfo(protocol)
	if self.fire_works_view:IsOpen() then
		self.fire_works_view:Flush()
	end
end

--烟花抽奖结果信息
function RebateGiftActivityWGCtrl:OnSCOAFireworksDrawResult(protocol)
	--print_error("烟花抽奖结果信息", protocol)
	self.data:SetFireWorksResultInfo(protocol)
	local data_list = self.data:GetFireWorksResultInfo()
	self:OpenRewardView(data_list.record_list)
end

--烟花抽奖恭喜获得界面
function RebateGiftActivityWGCtrl:OpenRewardView(data)
    self.fire_works_reward:SetData(data)
    self.fire_works_reward:Open()
end


---------------------------------烟花抽奖-------------------------------

--使用道具并弹窗
function RebateGiftActivityWGCtrl:ClickUseDrawItem(index, func)
	local cfg = RebateGiftActivityWGData.Instance:GetFireWorksDrawConsumeCfg()
	local mode_cfg = RebateGiftActivityWGData.Instance:GetFireWorksDrawItem()
	local cur_cfg = cfg[index]
	if cur_cfg == nil then
		return
	end
	
	local num = ItemWGData.Instance:GetItemNumInBagById(mode_cfg.cost_item_id)
	--不足弹窗
	if num < cur_cfg.cost_item_num then
		 if not self.alert then
			 self.alert = Alert.New()
		 end
		 self.alert:ClearCheckHook()
		 self.alert:SetShowCheckBox(true, "rebate_fireworks_draw")
		 self.alert:SetCheckBoxDefaultSelect(false)
		 local item_cfg = ItemWGData.Instance:GetItemConfig(mode_cfg.cost_item_id)
		 local name = ""
		 if item_cfg ~= nil then
			 name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		 end

		 local cost = mode_cfg.cost_gold * (cur_cfg.cost_item_num - num)
		 local str = string.format(Language.RebateGiftAct.DrawCostStr, name, cost)
		 self.alert:SetLableString(str)
		 self.alert:SetOkFunc(func)
		 self.alert:Open()
	else
		--使用
		func()
	end
 
 end

--烟花抽奖概率面板
function RebateGiftActivityWGCtrl:OpenGaiLvView()
    self.fire_works_gailv:Open()
end

------------------------------------------------------------------------------

---------------------------------绝版赠礼-------------------------------
function RebateGiftActivityWGCtrl:OnClickExtinctGiftTab(jump_index)
	if self.extinct_gift_view:IsOpen() then
		self.extinct_gift_view:JumpToShowView(jump_index)
	end
end

--天数改变
function RebateGiftActivityWGCtrl:OnDayChange()
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FIREWORKS_DRAW) then
		--请求烟花抽奖信息
		RebateGiftActivityWGCtrl.Instance:SendRechargeInfo(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FIREWORKS_DRAW, FIREWORKS_DRAW_OPERATE_TYPE.INFO)
	end

	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_EXTINCT_GIFT) then
		--请求绝版赠礼信息
		RebateGiftActivityWGCtrl.Instance:SendRechargeInfo(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_EXTINCT_GIFT, EXTINCT_GIFT_OPERATE_TYPE.INFO)
	end

	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_RECHARGE_DISCOUNTS) then
		--请求充值立减信息
		RebateGiftActivityWGCtrl.Instance:SendRechargeInfo(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_RECHARGE_DISCOUNTS, RECHARGE_DISCOUNTS_OPERATE_TYPE.INFO)
	end
end