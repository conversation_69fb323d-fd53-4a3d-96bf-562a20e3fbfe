﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class ActorRenderWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(ActorRender), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>unction("GetRenderList", GetRenderList);
		<PERSON><PERSON>("SetRenderList", SetRenderList);
		<PERSON><PERSON>("UpdateRender", UpdateRender);
		<PERSON><PERSON>RegFunction("GetRenderers", GetRenderers);
		L<PERSON>RegFunction("GetMeshRenderers", GetMeshRenderers);
		<PERSON><PERSON>RegFunction("GetAnimator", GetAnimator);
		<PERSON><PERSON>unction("GetAnimatorCull", GetAnimatorCull);
		<PERSON><PERSON>ction("GetCharacterShadow", GetCharacterShadow);
		L<PERSON>RegFunction("GetSkinnedMeshRenderers", GetSkinnedMeshRenderers);
		<PERSON><PERSON>RegFunction("GetMaterialQuality", GetMaterialQuality);
		<PERSON><PERSON>ction("SetMaterialQuality", SetMaterialQuality);
		<PERSON><PERSON>("SetMaterialKeyword", SetMaterialKeyword);
		<PERSON><PERSON>("SetIsCastShadow", SetIsCastShadow);
		L.RegFunction("SetRendererRenderingLayerMask", SetRendererRenderingLayerMask);
		L.RegFunction("ResetRendererRenderingLayerMask", ResetRendererRenderingLayerMask);
		L.RegFunction("SetIsWaterSurfaceReflection", SetIsWaterSurfaceReflection);
		L.RegFunction("SetIsDisableAllAttachEffects", SetIsDisableAllAttachEffects);
		L.RegFunction("SetMainTexture", SetMainTexture);
		L.RegFunction("ResetMainTexture", ResetMainTexture);
		L.RegFunction("SetIsHdTexture", SetIsHdTexture);
		L.RegFunction("StopAllRenderEffects", StopAllRenderEffects);
		L.RegFunction("SetIsGray", SetIsGray);
		L.RegFunction("SetIsMultiColor", SetIsMultiColor);
		L.RegFunction("PlayDissolveEffect", PlayDissolveEffect);
		L.RegFunction("PlayFadeEffect", PlayFadeEffect);
		L.RegFunction("PlayBlinkEffect", PlayBlinkEffect);
		L.RegFunction("SetDyeingColor", SetDyeingColor);
		L.RegFunction("SetIsLerpProbe", SetIsLerpProbe);
		L.RegFunction("SetIsCulled", SetIsCulled);
		L.RegFunction("SetDefaultRendererGameObjectLayer", SetDefaultRendererGameObjectLayer);
		L.RegFunction("SetRendererGameObjectLayer", SetRendererGameObjectLayer);
		L.RegFunction("ResetRendererGameObjectLayer", ResetRendererGameObjectLayer);
		L.RegFunction("ApplyEffectAttachmentsBySkinType", ApplyEffectAttachmentsBySkinType);
		L.RegFunction("ApplyEffectAttachmentsOnly", ApplyEffectAttachmentsOnly);
		L.RegFunction("ClearEffectAttachmentsBySkinType", ClearEffectAttachmentsBySkinType);
		L.RegFunction("ClearAllEffectGameObjectAttachs", ClearAllEffectGameObjectAttachs);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetRenderList(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			System.Collections.Generic.List<ActorRender.RenderItem> o = obj.GetRenderList();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetRenderList(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			System.Collections.Generic.List<ActorRender.RenderItem> arg0 = (System.Collections.Generic.List<ActorRender.RenderItem>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<ActorRender.RenderItem>));
			obj.SetRenderList(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UpdateRender(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			UnityEngine.Renderer arg0 = (UnityEngine.Renderer)ToLua.CheckObject<UnityEngine.Renderer>(L, 2);
			UnityEngine.Material[] arg1 = ToLua.CheckObjectArray<UnityEngine.Material>(L, 3);
			obj.UpdateRender(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetRenderers(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			UnityEngine.Renderer[] o = obj.GetRenderers();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetMeshRenderers(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			UnityEngine.MeshRenderer[] o = obj.GetMeshRenderers();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetAnimator(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			UnityEngine.Animator o = obj.GetAnimator();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetAnimatorCull(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			AnimatorCull o = obj.GetAnimatorCull();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetCharacterShadow(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			JYCharacterShadow o = obj.GetCharacterShadow();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetSkinnedMeshRenderers(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			UnityEngine.SkinnedMeshRenderer[] o = obj.GetSkinnedMeshRenderers();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetMaterialQuality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			int o = obj.GetMaterialQuality();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetMaterialQuality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SetMaterialQuality(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetMaterialKeyword(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			string arg0 = ToLua.CheckString(L, 2);
			bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
			obj.SetMaterialKeyword(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIsCastShadow(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetIsCastShadow(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetRendererRenderingLayerMask(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
			obj.SetRendererRenderingLayerMask(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResetRendererRenderingLayerMask(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			obj.ResetRendererRenderingLayerMask();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIsWaterSurfaceReflection(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetIsWaterSurfaceReflection(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIsDisableAllAttachEffects(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
				bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
				obj.SetIsDisableAllAttachEffects(arg0);
				return 0;
			}
			else if (count == 3)
			{
				ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
				bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
				obj.SetIsDisableAllAttachEffects(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: ActorRender.SetIsDisableAllAttachEffects");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetMainTexture(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			UnityEngine.Texture arg0 = (UnityEngine.Texture)ToLua.CheckObject<UnityEngine.Texture>(L, 2);
			obj.SetMainTexture(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResetMainTexture(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			obj.ResetMainTexture();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIsHdTexture(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetIsHdTexture(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StopAllRenderEffects(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			obj.StopAllRenderEffects();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIsGray(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetIsGray(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIsMultiColor(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			UnityEngine.Color arg1 = ToLua.ToColor(L, 3);
			obj.SetIsMultiColor(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PlayDissolveEffect(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			obj.PlayDissolveEffect();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PlayFadeEffect(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			System.Action arg2 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 4);
			obj.PlayFadeEffect(arg0, arg1, arg2);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PlayBlinkEffect(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			obj.PlayBlinkEffect();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetDyeingColor(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
				bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
				UnityEngine.Color arg1 = ToLua.ToColor(L, 3);
				obj.SetDyeingColor(arg0, arg1);
				return 0;
			}
			else if (count == 4)
			{
				ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
				bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
				UnityEngine.Color arg1 = ToLua.ToColor(L, 3);
				string arg2 = ToLua.CheckString(L, 4);
				obj.SetDyeingColor(arg0, arg1, arg2);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: ActorRender.SetDyeingColor");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIsLerpProbe(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
				bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
				obj.SetIsLerpProbe(arg0);
				return 0;
			}
			else if (count == 3)
			{
				ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
				bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
				bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
				obj.SetIsLerpProbe(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: ActorRender.SetIsLerpProbe");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIsCulled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetIsCulled(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetDefaultRendererGameObjectLayer(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			obj.SetDefaultRendererGameObjectLayer();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetRendererGameObjectLayer(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SetRendererGameObjectLayer(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResetRendererGameObjectLayer(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			obj.ResetRendererGameObjectLayer();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ApplyEffectAttachmentsBySkinType(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			ActorRender arg0 = (ActorRender)ToLua.CheckObject<ActorRender>(L, 2);
			ChangeSkin.SkinType arg1 = (ChangeSkin.SkinType)ToLua.CheckObject(L, 3, typeof(ChangeSkin.SkinType));
			System.Collections.Generic.Dictionary<string,UnityEngine.Transform> arg2 = (System.Collections.Generic.Dictionary<string,UnityEngine.Transform>)ToLua.CheckObject(L, 4, typeof(System.Collections.Generic.Dictionary<string,UnityEngine.Transform>));
			obj.ApplyEffectAttachmentsBySkinType(arg0, arg1, arg2);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ApplyEffectAttachmentsOnly(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			ActorRender arg0 = (ActorRender)ToLua.CheckObject<ActorRender>(L, 2);
			ChangeSkin.SkinType arg1 = (ChangeSkin.SkinType)ToLua.CheckObject(L, 3, typeof(ChangeSkin.SkinType));
			System.Collections.Generic.Dictionary<string,UnityEngine.Transform> arg2 = (System.Collections.Generic.Dictionary<string,UnityEngine.Transform>)ToLua.CheckObject(L, 4, typeof(System.Collections.Generic.Dictionary<string,UnityEngine.Transform>));
			obj.ApplyEffectAttachmentsOnly(arg0, arg1, arg2);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearEffectAttachmentsBySkinType(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			ChangeSkin.SkinType arg0 = (ChangeSkin.SkinType)ToLua.CheckObject(L, 2, typeof(ChangeSkin.SkinType));
			obj.ClearEffectAttachmentsBySkinType(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearAllEffectGameObjectAttachs(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ActorRender obj = (ActorRender)ToLua.CheckObject<ActorRender>(L, 1);
			obj.ClearAllEffectGameObjectAttachs();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

