﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class UnityEngine_Networking_UnityWebRequest_ResultWrap
{
	public static void Register(LuaState L)
	{
		L.BeginEnum(typeof(UnityEngine.Networking.UnityWebRequest.Result));
		<PERSON><PERSON>("InProgress", get_InProgress, null);
		<PERSON><PERSON>("Success", get_Success, null);
		<PERSON><PERSON>("ConnectionError", get_ConnectionError, null);
		<PERSON><PERSON>("ProtocolError", get_ProtocolError, null);
		<PERSON><PERSON>("DataProcessingError", get_DataProcessingError, null);
		<PERSON><PERSON>("IntToEnum", IntToEnum);
		<PERSON><PERSON>();
		TypeTraits<UnityEngine.Networking.UnityWebRequest.Result>.Check = CheckType;
		StackTraits<UnityEngine.Networking.UnityWebRequest.Result>.Push = Push;
	}

	static void Push(IntPtr L, UnityEngine.Networking.UnityWebRequest.Result arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(UnityEngine.Networking.UnityWebRequest.Result), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InProgress(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Networking.UnityWebRequest.Result.InProgress);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Success(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Networking.UnityWebRequest.Result.Success);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ConnectionError(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Networking.UnityWebRequest.Result.ConnectionError);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ProtocolError(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Networking.UnityWebRequest.Result.ProtocolError);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_DataProcessingError(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Networking.UnityWebRequest.Result.DataProcessingError);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		UnityEngine.Networking.UnityWebRequest.Result o = (UnityEngine.Networking.UnityWebRequest.Result)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

