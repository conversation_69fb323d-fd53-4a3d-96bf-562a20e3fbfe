﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class SceneRuntimeGUI
{
    private Rect windowRect;
    private string sceneName;
    private bool isShowScene;
    private bool isInited = false;

    public void ShowGUI()
    {
        if (!isInited)
        {
            isInited = true;
            RuntimeGUIResMgr.Instance.Startup();
        }
        this.isShowScene = !isShowScene;
    }

    public void OnGUI()
    {
        if (!isShowScene) return;

        windowRect = new Rect((Screen.width - 700) * 0.5f, (Screen.height - 300) * 0.5f, 700, 300);
        windowRect = GUI.Window(0, windowRect, ShowLoginWindow, "场景");
        RuntimeGUIMgr.Instance.GetGUIBlock().ShowRect(windowRect, 1);
    }

    private void ShowLoginWindow(int windowid)
    {
        GUILayout.Space(15);
        GUILayout.BeginHorizontal();

        sceneName = GUILayout.TextField(sceneName);
        if (GUILayout.Button("打开场景"))
        {
            this.OpenScene();
        }
        GUILayout.EndHorizontal();
    }

    private void OpenScene()
    {
        RuntimeGUIResMgr.Instance.LoadScene(sceneName);
    }
 }
