-----------------------------------
-- 百亿补贴-九块九专场
-----------------------------------

function BillionSubsidyView:JKJZCLoadIndexCallBack()
	if not self.jkj_shop_item_grid then
		self.jkj_shop_item_grid = AsyncBaseGrid.New()
		self.jkj_shop_item_grid:SetStartZeroIndex(false)
		self.jkj_shop_item_grid:CreateCells({
			col = 4,
			assetName = "shop_item_render_jkjzc",
			assetBundle = "uis/view/billion_subsidy_ui_prefab",
			list_view = self.node_list["jkj_goods_list"],
			itemRender = JKJShopItemRender,
			change_cells_num = 1,
		})
	end
end

function BillionSubsidyView:JKJZCReleaseCallBack()
	if self.jkj_shop_item_grid then
		self.jkj_shop_item_grid:DeleteMe()
		self.jkj_shop_item_grid = nil
	end
end

function BillionSubsidyView:JKJZCShowIndexCallBack()

end

function BillionSubsidyView:JKJZCCloseCallBack()

end

function BillionSubsidyView:JKJZCOnFlush(param_t, index)
	local data_list = BillionSubsidyWGData.Instance:GetJKJZCShopItemGradeData()
	self.jkj_shop_item_grid:SetDataList(data_list)
end

-------------------------------------------
-- 商品格子
-------------------------------------------
JKJShopItemRender = JKJShopItemRender or BaseClass(BaseRender)
function JKJShopItemRender:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list["item_cell_pos"])
	end

	XUI.AddClickEventListener(self.node_list.btn_add_shop_cart, BindTool.Bind(self.OnClickAddShopCartBtn, self))
end

function JKJShopItemRender:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

	self.cur_price = nil
	self.cur_use_dc = nil
end

function JKJShopItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local cfg = self.data
	local show_item = cfg.reward[0]
	self.item_cell:SetData(show_item)
	self.node_list["item_name"].text.text = ItemWGData.Instance:GetItemName(show_item.item_id)

	local num_in_cart = BillionSubsidyWGData.Instance:GetShopNumInCart(BillionSubsidyWGData.ShopType.JKJZC, self.data.item_seq)
	local buy_count = BillionSubsidyWGData.Instance:GetJKJZCShopBuyCount(cfg.item_seq)
	self.node_list["txt_buy_limit"].text.text = string.format(Language.BillionSubsidy.LimitBuy, buy_count + num_in_cart, cfg.buy_limit)
	-- self.node_list.btn_add_shop_cart:CustomSetActive(buy_count + num_in_cart >= self.data.buy_limit)

	local is_sold_out = buy_count >= cfg.buy_limit
	self.node_list["sold_out_flag"]:SetActive(is_sold_out)
	self.node_list["btn_add_shop_cart"]:SetActive(not is_sold_out and buy_count + num_in_cart < self.data.buy_limit)
	self.node_list["hide_when_soldout"]:SetActive(not is_sold_out)

	-- 可用券
	self.cur_use_dc = nil
	local cur_price = cfg.price
	local free_dc, full_dc, direct_dc = BillionSubsidyWGData.Instance:GetCanUseDiscountTicketBySeq(BillionSubsidyWGData.ShopType.JKJZC, cfg.item_seq)
	if free_dc then --免单券
		cur_price = 0
		self.node_list["free_dc_flag"]:SetActive(true)
		self.node_list["dc_flag"]:SetActive(false)
		self.cur_use_dc = free_dc
	elseif full_dc then --满减券
		cur_price = cur_price - full_dc.reduce_quota
		cur_price = cur_price < 0 and 0 or cur_price
		self.node_list["free_dc_flag"]:SetActive(false)
		self.node_list["dc_flag"]:SetActive(true)
		self.node_list["text_dc_des"].text.text = string.format(Language.BillionSubsidy.FullDCSelectDesc, full_dc.quota_limit, full_dc.reduce_quota)
		self.cur_use_dc = full_dc
	elseif direct_dc then --立减券
		cur_price = cur_price - direct_dc.reduce_quota
		cur_price = cur_price < 0 and 0 or cur_price
		self.node_list["free_dc_flag"]:SetActive(false)
		self.node_list["dc_flag"]:SetActive(true)
		self.node_list["text_dc_des"].text.text = string.format(Language.BillionSubsidy.DirectDCSelectDesc, direct_dc.reduce_quota)
		self.cur_use_dc = direct_dc
	else
		self.node_list["free_dc_flag"]:SetActive(false)
		self.node_list["dc_flag"]:SetActive(false)
	end
	self.cur_price = cur_price

	local is_shop_open = BillionSubsidyWGData.Instance:GetJKJZCShopOpen()
	if not is_shop_open then
		local member_level = BillionSubsidyWGData.Instance:GetJKJZCShopOpenMemberLevel()
		local str = Language.BillionSubsidy.VipNameList[member_level] or Language.BillionSubsidy.ShopItemLockDesc
		self.node_list["price_text"].text.text = string.format(Language.BillionSubsidy.B1G3ShopItemLockDesc, str)
		self.node_list["old_price_text"].text.text = string.format("￥%s", cur_price)
		self.node_list["redline"]:SetActive(false)
	else
		self.node_list["price_text"].text.text = string.format(Language.BillionSubsidy.OnlySale, cur_price)
		local old_price = cfg.show_price
		self.node_list["old_price_text"].text.text = string.format("￥%s", old_price)
		self.node_list["redline"]:SetActive(true)
	end

	if cfg.discount and cfg.discount ~= "" then
		self.node_list["discount"]:SetActive(true)
		local discount_str = string.format(Language.BillionSubsidy.Discount, NumberToChinaNumber(cfg.discount))
		self.node_list["txt_discount"].text.text = discount_str
	else
		self.node_list["discount"]:SetActive(false)
	end
end

function JKJShopItemRender:OnClick()
	if not self.data then
		return
	end

	local is_shop_open = BillionSubsidyWGData.Instance:GetJKJZCShopOpen()
	if not is_shop_open then
		BillionSubsidyWGCtrl.Instance:OpenActiceVipView()
		return
	end
	
	local cfg = self.data
	--[[
	local buy_count = BillionSubsidyWGData.Instance:GetJKJZCShopBuyCount(cfg.item_seq)
	if buy_count >= cfg.buy_limit then
		TipWGCtrl.Instance:ShowSystemMsg(Language.BillionSubsidy.B1G3TodayLimited)
		return
	end

	local use_ticket_type = self.cur_use_dc and self.cur_use_dc.ticket_type
	local use_dc_seq = self.cur_use_dc and self.cur_use_dc.data_seq

	if self.cur_price > 0 then
		RechargeWGCtrl.Instance:Recharge(cfg.price, cfg.rmb_type, cfg.rmb_seq, use_dc_seq or -1)
	else
		BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.USE_FREE_OR_DISCOUNT_TICKET_RMB_BUY,
			cfg.rmb_type, cfg.rmb_seq, use_ticket_type, use_dc_seq or -1)
	end
	]]
	BillionSubsidyWGCtrl.Instance:OpenBuyTipView(BillionSubsidyWGData.ShopType.JKJZC, cfg.item_seq)
end

function JKJShopItemRender:OnClickAddShopCartBtn()
	local is_shop_open = BillionSubsidyWGData.Instance:GetJKJZCShopOpen()
	if not is_shop_open then
		BillionSubsidyWGCtrl.Instance:OpenActiceVipView()
		return
	end

    if IsEmptyTable(self.data) then
        return
    end
    
    local is_add = BillionSubsidyWGCtrl.Instance:AddItemToShopCart(BillionSubsidyWGData.ShopType.JKJZC, self.data.item_seq, 1, self.data.buy_limit, true)
    if is_add then
        local pos, target_pos = BillionSubsidyWGCtrl.Instance:GetNodeInScreenPos(self.node_list.item_cell_pos)
        BillionSubsidyWGCtrl.Instance:PlaySCAddItemAnim(self.data.reward[0].item_id, pos, target_pos)
    end

end