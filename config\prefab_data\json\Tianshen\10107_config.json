{"actorController": {"projectiles": [], "hurts": []}, "actorTriggers": {"effects": [{"triggerEventName": "attack1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10107_skill1", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10107/10107_skill1_prefab", "AssetName": "10107_skill1", "AssetGUID": "77979e1000c303b4cabc4dc471c56a87", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": -2.0, "offsetPosY": 0.0, "offsetPosZ": 2.0, "triggerStopEvent": "[none]", "effectBtnName": "skill1", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10107_skill2", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10107/10107_skill2_prefab", "AssetName": "10107_skill2", "AssetGUID": "57dfd6bd9ead78148b2d0fe187d72df0", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "root", "isAttach": true, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 100.0, "offsetPosY": 0.0, "offsetPosZ": -500.0, "triggerStopEvent": "[none]", "effectBtnName": "skill2", "playerAtPos": false, "ignoreParentScale": true}, {"triggerEventName": "attack3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10107_skill3", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10107/10107_skill3_prefab", "AssetName": "10107_skill3", "AssetGUID": "fbd0e11ff822035479e809c27ef17013", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "root", "isAttach": true, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 200.0, "offsetPosY": 0.0, "offsetPosZ": -500.0, "triggerStopEvent": "[none]", "effectBtnName": "skill3", "playerAtPos": false, "ignoreParentScale": true}, {"triggerEventName": "combo1_1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10107_attack", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10107/10107_attack_prefab", "AssetName": "10107_attack", "AssetGUID": "19b668167c4b57e4ba2d87a7ce6644d4", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": -1.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "rest/begin", "triggerDelay": 0.8, "triggerFreeDelay": 0.0, "effectGoName": "10107_rest", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10107/10107_rest_prefab", "AssetName": "10107_rest", "AssetGUID": "ccc7308865b907547a5ea6073a53d841", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "root", "isAttach": true, "isRotation": false, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "rest", "playerAtPos": false, "ignoreParentScale": false}], "sounds": [{"soundEventName": "combo1_1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10107", "AssetName": "MingJiangcombo_1_1", "AssetGUID": "abc98a3c289f51241b42b8d73b72c732", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_1", "soundBtnName": "combo1", "soundIsMainRole": false}, {"soundEventName": "combo1_2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10107", "AssetName": "MingJiangcombo_1_2", "AssetGUID": "24f6e9fac56f3104f93f1c4380310e01", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_2", "soundBtnName": "combo2", "soundIsMainRole": false}, {"soundEventName": "combo1_3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10107", "AssetName": "MingJiangcombo_1_3", "AssetGUID": "a6f2b05dd05bff4438ef353d04b4fbb3", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_3", "soundBtnName": "combo3", "soundIsMainRole": false}, {"soundEventName": "attack1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10107", "AssetName": "MingJiangattack1", "AssetGUID": "15f88bc25c2dfef479463af0f419812b", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack1", "soundBtnName": "attack1", "soundIsMainRole": false}, {"soundEventName": "attack2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10107", "AssetName": "MingJiangattack2", "AssetGUID": "d3c1a9d4563b69d4eb26b6c8b9f1c5df", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack2", "soundBtnName": "attack2", "soundIsMainRole": false}, {"soundEventName": "attack3/begin", "soundDelay": 0.4, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10107", "AssetName": "MingJiangattack3", "AssetGUID": "c250067465f775b4f93b84f9d71fff0e", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack3", "soundBtnName": "attack3", "soundIsMainRole": false}, {"soundEventName": "combo1_3/begin", "soundDelay": 0.5, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10107", "AssetName": "MingJiangcombo_1_31", "AssetGUID": "fae968f058a04aa43a4d14ea859cad7a", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_31", "soundBtnName": "combo3_1", "soundIsMainRole": false}], "cameraShakes": [{"CameraShakeBtnName": "attack2", "eventName": "attack2/begin", "numberOfShakes": 4, "distance": 1.0, "speed": 500.0, "delay": 0.6, "decay": 0.0}, {"CameraShakeBtnName": "attack3", "eventName": "attack3/begin", "numberOfShakes": 2, "distance": 1.0, "speed": 1000.0, "delay": 1.0, "decay": 0.0}], "radialBlurs": []}}