JingHuaShuiYueWGData = JingHuaShuiYueWGData or BaseClass()

JHSY_SYSTEM_TYPE = {
    Fashion = 0,            --时装
    Mount = 1,              --坐骑
    Pet = 2,                --宠物
    Huakun = 3,             --化坤
    BattleMount = 4,        --战斗坐骑
    Tianshen = 5,           --天神
    Wuhun = 6,              --武魂
    Yushou = 7,             --驭兽
    Tianmu = 8,             --天幕
}

JHSY_REMIND_TYPE = {
    None = 0,
    Active = 1,
    UpStar = 2,
}

function JingHuaShuiYueWGData:__init()
	if JingHuaShuiYueWGData.Instance then
		error("[JingHuaShuiYueWGData] Attempt to create singleton twice!")
		return
	end

    JingHuaShuiYueWGData.Instance = self

    self:InitParam()
    self:InitConfig()

    RemindManager.Instance:Register(RemindName.JingHuaShuiYue, BindTool.Bind(self.GetJingHuaShuiYueRemind, self))
end

function JingHuaShuiYueWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.JingHuaShuiYue)
    JingHuaShuiYueWGData.Instance = nil
end

function JingHuaShuiYueWGData:InitParam()
    self.suit_list = {}
    self.big_type_list = {}
    self.small_type_list = {}
end

function JingHuaShuiYueWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("jing_hua_shui_yue_cfg_auto")
    self.active_cfg = ListToMap(cfg.active, "suit_id", "pos")
    self.suit_cfg = ListToMapList(cfg.suit, "suit_id")
    self.star_cfg = ListToMap(cfg.stat, "suit_id", "level")
    self.yushou_cfg = ListToMap(cfg.yushou, "suit_id", "pos", "level")
    self.big_type_show_cfg = cfg.big_type_show
    self.small_type_show_cfg = cfg.small_type_show

    self:CreateTypeList()
end

--------- 红点相关start ---------
function JingHuaShuiYueWGData:GetJingHuaShuiYueRemind()
    if self.big_type_list then
        for k, v in pairs(self.big_type_list) do
            if self:GetBigTypeRemind(k) then
                return 1
            end
        end
    end

    return 0
end

function JingHuaShuiYueWGData:GetBigTypeRemind(big_type)
    local big_type_data = self:GetBigTypeData(big_type)
    if not IsEmptyTable(big_type_data) then
        for k, v in pairs(big_type_data.small_type_list) do
            if self:GetSingleSuitRemind(v.small_type) then
                return true
            end
        end
    end

    return false
end

function JingHuaShuiYueWGData:GetSingleSuitRemind(suit_id)
    local suit_data = self:GetShowItemList(suit_id)
    local act_num = 0
    local star_enough_num = 0
    if not IsEmptyTable(suit_data) then
        local next_star_level = self:GetSuitStarLevel(suit_id) + 1
        for k, v in pairs(suit_data) do
            if v.is_act then
                act_num = act_num + 1
            end

            if v.star_level >= next_star_level then
                star_enough_num = star_enough_num + 1
            end
        end
    end

    local is_max_act_level = self:IsMaxActiveProgress(suit_id)
    if not is_max_act_level then
        local next_act_num = self:GetNextSuitActiveNum(suit_id)
        if act_num >= next_act_num then
            return true, JHSY_REMIND_TYPE.Active
        end
    end

    local is_max_star = self:IsMaxStarLevel(suit_id)
    if is_max_act_level and not is_max_star then
        if star_enough_num >= #suit_data then
            return true, JHSY_REMIND_TYPE.UpStar
        end
    end

    return false, JHSY_REMIND_TYPE.None
end

--------- 红点相关end ---------

--------- 协议相关start ---------
function JingHuaShuiYueWGData:SetJingHuaShuiYueAllInfo(protocol)
    self.suit_list = protocol.suit_list
end

function JingHuaShuiYueWGData:UpdateActiveInfo(protocol)
    local suit_data = self:GetSuitDataBySuitId(protocol.suit_id)
    suit_data.pos_count = protocol.pos_count
end

function JingHuaShuiYueWGData:UpdateUpStarInfo(protocol)
    local suit_data = self:GetSuitDataBySuitId(protocol.suit_id)
    suit_data.star_level = protocol.star_level
end

function JingHuaShuiYueWGData:UpdatePosInfo(protocol)
    for k, v in pairs(protocol.data_list) do
        local suit_data = self:GetSuitDataBySuitId(v.suit_id)
        suit_data.pos_info[v.pos] = v.pos_level
    end
end

--获得套装信息
function JingHuaShuiYueWGData:GetSuitDataBySuitId(suit_id)
    return self.suit_list[suit_id] or {}
end

--获得单个套装的激活进度
function JingHuaShuiYueWGData:GetSuitActiveProgress(suit_id)
    local suit_data = self:GetSuitDataBySuitId(suit_id)
    return suit_data.pos_count or 0
end

--获得单个套装的星级
function JingHuaShuiYueWGData:GetSuitStarLevel(suit_id)
    local suit_data = self:GetSuitDataBySuitId(suit_id)
    return suit_data.star_level or 0
end

--获得套装内的单个部位信息
function JingHuaShuiYueWGData:GetSuitPosLevel(suit_id, pos)
    local suit_data = self:GetSuitDataBySuitId(suit_id)
    return (suit_data.pos_info or {})[pos] or -1
end

--------- 协议相关end ---------

--激活表，拿判定系统的枚举和对应系统内道具的参数
function JingHuaShuiYueWGData:GetActiveCfgBySuitId(suit_id)
    return self.active_cfg[suit_id] or {}
end

--激活进度表，拿不同的进度的属性
function JingHuaShuiYueWGData:GetSuitCfgBySuitId(suit_id)
    return self.suit_cfg[suit_id] or {}
end

--升星表
function JingHuaShuiYueWGData:GetStarCfgById(suit_id)
    return self.star_cfg[suit_id] or {}
end

function JingHuaShuiYueWGData:GetUpStarLevelCfg(suit_id, is_next)
    local up_star_level = self:GetSuitStarLevel(suit_id)
    local level = is_next and up_star_level + 1 or up_star_level
    return (self.star_cfg[suit_id] or {})[level] or {}
end

--驭兽升星表，驭兽没有星级概念，所以不同星级需要拿不同item_id,只有0-9特殊处理拿表
function JingHuaShuiYueWGData:GetYuShouCfg(suit_id, pos)
    local star = self:GetSuitPosLevel(suit_id, pos)
    local level = math.max(1, star)
    return ((self.yushou_cfg[suit_id] or {})[pos] or {})[level] or {}
end

--大类型显示表，纯展示所用
function JingHuaShuiYueWGData:GetBigTyfpeToggleCfg(big_type)
    return self.big_type_show_cfg[big_type] or {}
end

--小类型显示表，纯展示所用
function JingHuaShuiYueWGData:GetSmallTypeToggleCfg(small_type)
    return self.small_type_show_cfg[small_type] or {}
end

--程景：左边列表改成只展示小类
function JingHuaShuiYueWGData:GetSmallTypeShowCfg()
    return self.small_type_show_cfg
end

--创建大小类型列表，提前解析每个大类型内的小类型列表，方便展示
function JingHuaShuiYueWGData:CreateTypeList()
    for k, v in pairs(self.big_type_show_cfg) do
        local small_type_str = Split(v.small_type_list, "|")
        local small_type_list = {}
        for index, small_type in ipairs(small_type_str) do
            local suit_id = tonumber(small_type)
            local data = {small_type = suit_id, from_big_type = k}
            table.insert(small_type_list, data)

            if not self.small_type_list[suit_id] then
                self.small_type_list[suit_id] = data
            end
        end

        self.big_type_list[k] = {
            big_type = k,
            big_type_name = v.big_type_name,
            small_type_list = small_type_list,
        }
    end
end

function JingHuaShuiYueWGData:GetBigTypeData(big_type)
    return self.big_type_list[big_type] or {}
end

function JingHuaShuiYueWGData:GetSmallTypeData(small_type)
    return self.small_type_list[small_type] or {}
end

-- 获得当前羁绊所有激活升星属性
function JingHuaShuiYueWGData:GetShowAttrListBySuitId(suit_id)
    local attr_list = {}
    local suit_active_cfg = self:GetSuitCfgBySuitId(suit_id)
    if IsEmptyTable(suit_active_cfg) then
        return attr_list
    end

    local active_progress = self:GetSuitActiveProgress(suit_id)
    local cur_active_cfg = suit_active_cfg[active_progress + 1]
    if IsEmptyTable(cur_active_cfg) then
        return attr_list
    end

    local next_active_cfg = suit_active_cfg[active_progress + 2]
    local active_attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_active_cfg, next_active_cfg, "attr", "value", 1, 8)
    local is_max_act_level = self:IsMaxActiveProgress(suit_id)
    local cur_upstar_cfg = self:GetUpStarLevelCfg(suit_id)
    if not is_max_act_level or IsEmptyTable(cur_upstar_cfg) then
        return active_attr_list
    end

    local suit_star = self:GetSuitStarLevel(suit_id)
    local cur_attr_cfg = suit_star <= 0 and cur_active_cfg or cur_upstar_cfg
    local next_upstar_cfg = self:GetUpStarLevelCfg(suit_id, true)

    attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_attr_cfg, next_upstar_cfg, "attr", "value", 1, 8)
    return attr_list
end

function JingHuaShuiYueWGData:GetSuitCapability(suit_id)
    local attribute = AttributePool.AllocAttribute()
    local attr_list = self:GetShowAttrListBySuitId(suit_id)
    for k, v in pairs(attr_list) do
        attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
    end

	local capability = AttributeMgr.GetCapability(attribute)
    return capability
end

--界面中间显示的物品列表
function JingHuaShuiYueWGData:GetShowItemList(small_type)
    local show_item_list = {}
    local cfg = self:GetActiveCfgBySuitId(small_type)
    if IsEmptyTable(cfg) then
        return show_item_list
    end

    for pos, v in pairs(cfg) do
        local data = {}
        data.pos = pos
        data.from_suit = small_type
        data.sys_id = v.sys_id
        local star_level = self:GetSuitPosLevel(small_type, pos)
        local is_act = star_level > -1
        local item_id = v.item_id
        if v.sys_id == JHSY_SYSTEM_TYPE.Yushou then      --驭兽特殊处理
            local yushou_cfg = self:GetYuShouCfg(small_type, pos)
            item_id = yushou_cfg.yushou_id or 0
        end

        data.is_act = is_act
        data.star_level = star_level
        data.item_id = item_id
        table.insert(show_item_list, data)
    end

    return show_item_list
end

--是否达到最大星级
function JingHuaShuiYueWGData:IsMaxStarLevel(suit_id)
    local star_level = self:GetSuitStarLevel(suit_id)
    local star_cfg = self:GetStarCfgById(suit_id)
    local max_star_level = star_cfg[#star_cfg] and star_cfg[#star_cfg].level or 999

    return star_level >= max_star_level
end

--获得下一个套装激活数量
function JingHuaShuiYueWGData:GetNextSuitActiveNum(suit_id)
    local suit_cfg = self:GetSuitCfgBySuitId(suit_id)
    local active_progress = self:GetSuitActiveProgress(suit_id)
    local num = 0
    local next_active_cfg = suit_cfg[active_progress + 2]
    if next_active_cfg then
        num = next_active_cfg.count
    else
        local cur_active_cfg = suit_cfg[active_progress + 1]
        num = cur_active_cfg and cur_active_cfg.count or 0
    end

    return num
end

--是否达到最大进度
function JingHuaShuiYueWGData:IsMaxActiveProgress(suit_id)
    local active_progress = self:GetSuitActiveProgress(suit_id)
    local max_active_progress = self:GetSuitMaxActiveProgress(suit_id)
    return active_progress >= max_active_progress
end

-- 获得一个套装最大可激活进度，初始有0但无用故-1
function JingHuaShuiYueWGData:GetSuitMaxActiveProgress(suit_id)
    local suit_cfg = self:GetSuitCfgBySuitId(suit_id)
    return #suit_cfg - 1
end

--获得激活数量
function JingHuaShuiYueWGData:GetSuitActiveNum(suit_id)
    local suit_data = self:GetShowItemList(suit_id)
    local act_num = 0
    if not IsEmptyTable(suit_data) then
        for k, v in pairs(suit_data) do
            if v.is_act then
                act_num = act_num + 1
            end
        end
    end

    return act_num
end

--获得星级达成数量
function JingHuaShuiYueWGData:GetSuitStarEnoughNum(suit_id)
    local suit_data = self:GetShowItemList(suit_id)
    local star_enough_num = 0
    if not IsEmptyTable(suit_data) then
        local suit_star_level = self:GetSuitStarLevel(suit_id)
        for k, v in pairs(suit_data) do
            if v.star_level >= suit_star_level + 1 then
                star_enough_num = star_enough_num + 1
            end
        end
    end

    return star_enough_num, #suit_data
end