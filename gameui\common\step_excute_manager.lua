StepExcuteManager = StepExcuteManager or BaseClass()
-- 分布key
StepExcuteKey = {
	TuJian = "TuJian",
}

function StepExcuteManager:__init()
	if nil ~= StepExcuteManager.Instance then
		print_error("[StepExcuteManager]:Attempt to create singleton twice!")
	end
	StepExcuteManager.Instance = self

	-- 待执行列表
	self.wait_excute_list = {}
	-- 注册列表
	self.register_callback_list = {}

	Runner.Instance:AddRunObj(self, 8)
end

function StepExcuteManager:__delete()
	StepExcuteManager.Instance = nil
	self.wait_excute_list = {}
	self.register_callback_list = {}
	Runner.Instance:RemoveRunObj(self)
end

function StepExcuteManager:Update(now_time, elapse_time)
	for i=1,2 do
		self:StepExecute()
	end
end

function StepExcuteManager:StepExecute()
	if #self.wait_excute_list <= 0 then
		return
	end	

	local t = table.remove(self.wait_excute_list, 1)
	t.in_excute_list = false
	t.callback()
end

-- 添加单条执行事件  不通过step_key
function StepExcuteManager:AddOneExecuteEvent(callback)
	table.insert(self.wait_excute_list, {callback = callback})
end

function StepExcuteManager:Fire(step_key)
	local register_step_list = self.register_callback_list[step_key]
	if not register_step_list or IsEmptyTable(register_step_list) then
		return
	end

	-- in_excute_list 避免多次添加执行
	for k,v in pairs(register_step_list) do
		if not v.in_excute_list then
			v.in_excute_list = true
			table.insert(self.wait_excute_list , v) 
		end
	end

end

--注册一个分步事件
function StepExcuteManager:Register(step_key, callback)
	self.register_callback_list[step_key] = self.register_callback_list[step_key] or {}
	self.register_callback_list[step_key][callback] = {callback = callback ,in_excute_list = false}
	return callback
end

function StepExcuteManager:UnRegister(step_key,callback)
	if self.register_callback_list[step_key] then
		self.register_callback_list[step_key][callback] = nil

		local remove_index
		for i,v in ipairs(self.wait_excute_list) do
			if v.callback == callback then
				remove_index = i
			end
		end

		if remove_index then
			table.remove(self.wait_excute_list, remove_index)
		end
	end
end

-- 移除当前key对应的所有回调事件
function StepExcuteManager:UnRegisterAllByStepKey(step_key)
	self.register_callback_list[step_key] = nil
end