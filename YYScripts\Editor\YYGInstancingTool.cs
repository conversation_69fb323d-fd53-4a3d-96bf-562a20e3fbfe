﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

using UnityEditor;

using UnityEditor.SceneManagement;

using UnityEngine.Profiling;

namespace YYGame.Code
{

    public class YYGInstancingTool
    {
        [MenuItem("Window/YYGInstancingToolCheck")]
        public static void YYGInstancingToolCheck()
        {

            //BuildMaterial.Build();

            GameObject rootObj = GameObject.Find("Main/Models");
            if (null == rootObj)
            {
                Debug.Log("Main/Models 不存在");
                return;
            }

            YYGInstancingPreProccess yYGInstancingToolOpt = new YYGInstancingPreProccess(rootObj);

            yYGInstancingToolOpt.Check(true);
            


        }

        [MenuItem("Window/YYGInstancingToolMoveGInstancing")]
        public static void GInstancingToolMove()
        {

            //BuildMaterial.Build();
            GameObject rootObj = GameObject.Find("Main/Models");
            if (null == rootObj)
            {
                Debug.Log("Main/Models 不存在");
                return;
            }

           
            GameObject gInstancingObj = YYGInstancingPreProccess.GetGo("GInstancing");

            YYGInstancingPreProccess yYGInstancingToolOpt = new YYGInstancingPreProccess(rootObj);

            yYGInstancingToolOpt.Check();
            yYGInstancingToolOpt.MoveTo(gInstancingObj);

            yYGInstancingToolOpt.ShowMatCount();

            EditorSceneManager.MarkSceneDirty(rootObj.scene);
        }

        [MenuItem("Window/YYGInstancingToolMoveNotBatch")]
        public static void GInstancingToolNotBatch()
        {

            //BuildMaterial.Build();
            GameObject rootObj = GameObject.Find("Main/Models");
            if (null == rootObj)
            {
                Debug.Log("Main/Models 不存在");
                return;
            }

          
            GameObject gInstancingObj = YYGInstancingPreProccess.GetGo("NotBatch");

            YYGInstancingPreProccess yYGInstancingToolOpt = new YYGInstancingPreProccess(rootObj);

            yYGInstancingToolOpt.Check();
            yYGInstancingToolOpt.MoveToNotBatch(gInstancingObj);

            EditorSceneManager.MarkSceneDirty(rootObj.scene);
        }

        

    }
   
}

