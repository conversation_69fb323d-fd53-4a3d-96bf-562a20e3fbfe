﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Game_AttachSkinObjectWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(Game.AttachSkinObject), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("ResetBone", ResetBone);
		<PERSON><PERSON>Function("__eq", op_Equality);
		L<PERSON>Function("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResetBone(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Game.AttachSkinObject obj = (Game.AttachSkinObject)ToLua.CheckObject(L, 1, typeof(Game.AttachSkinObject));
			obj.ResetBone();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

