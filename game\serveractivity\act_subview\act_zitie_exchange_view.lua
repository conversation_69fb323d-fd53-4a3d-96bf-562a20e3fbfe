----------------------------------------------------
--开服活动 -- 字帖兑换
----------------------------------------------------
local ZiTieRemindToggleFlags = 0

function ServerActivityTabView:KFJZReleaseCallBack()
	if self.kfjz_competition_list ~= nil then
		self.kfjz_competition_list:DeleteMe()
		self.kfjz_competition_list = nil
	end

	if self.jizi_item_list ~= nil then
        for i, v in ipairs(self.jizi_item_list) do
            v:DeleteMe()
        end
        self.jizi_item_list = nil
    end

	CountDownManager.Instance:RemoveCountDown("kaifujizi_countdown")
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.kfjz_datachange_callback)
end

function ServerActivityTabView:KFJZLoadCallBack()
	-- self.kfjz_competition_list = AsyncListView.New(OpenServerZiTieExchangeItemRender, self.node_list.kfjz_ph_list)

	if self.kfjz_competition_list == nil then
        local bundle = "uis/view/open_server_activity_ui_prefab"
        local asset = "ph_server_reward_render"
		self.kfjz_competition_list = AsyncBaseGrid.New()
		self.kfjz_competition_list:CreateCells({
			col = 2, 
			change_cells_num = 1, 
			list_view = self.node_list["kfjz_ph_list"],
			assetBundle = bundle, 
			assetName = asset, 
			itemRender = OpenServerZiTieExchangeItemRender})
		self.kfjz_competition_list:SetStartZeroIndex(false)
	end

	self.jizi_item_list = {}
    for i = 1, 5 do
        self.jizi_item_list[i] = JiZiItemRender.New(self.node_list["zi_cell_" .. i])
         self.jizi_item_list[i]:SetIndex(i)
    end

	self.kfjz_datachange_callback = BindTool.Bind1(self.KFJZOnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.kfjz_datachange_callback)
	self:KFJZFlushCountDownTime()

	XUI.AddClickEventListener(self.node_list.kfjz_btn_go, BindTool.Bind(self.KFJZOnClickBenGo, self))
end

function ServerActivityTabView:KFJZOnClickBenGo()
	ViewManager.Instance:Open(GuideModuleName.Boss, TabIndex.boss_vip)
end

function ServerActivityTabView:KFJZShowIndexCallBack()
	-- self:DoKFJZAnimation()
	-- self:DoKFJZCellListAnimation()
end

function ServerActivityTabView:KFJZOnItemDataChange()
	self:KFJZFlushView()
end

function ServerActivityTabView:KFJZUpdataEndTime(elapse_time, total_time)
	self.node_list.kfjz_lbl_activity_time.text.text = string.format(Language.OpenServer.ActRemainTime2, TimeUtil.FormatSecondDHM6(total_time - elapse_time))
end

function ServerActivityTabView:KFJZCompleteEndCallBack()
	if self.node_list.kfjz_lbl_activity_time then
		self.node_list.kfjz_lbl_activity_time.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
	end
end

function ServerActivityTabView:KFJZOnClickState()
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetTitle(Language.OpenServer.KaiFuJiZiTipsTitle)
		role_tip:SetContent(Language.OpenServer.KaiFuJiZiTipsShow)
	end
end

function ServerActivityTabView:KFJZOnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" or (k == "act_info" and v.protocol_id == 2718) then
			self:KFJZFlushView()
		end
	end
end

function ServerActivityTabView:KFJZFlushView()
	local opengame_cfg = ServerActivityWGData.Instance:GetOpenGameActivityConfig()
	local flag = PlayerPrefsUtil.GetInt("OpenServerZiTieRemindToggle") or 0
	ZiTieRemindToggleFlags = bit:d2b(flag)
	self.kfjz_competition_list:SetDataList(opengame_cfg.word_exchange)
	local jizi_data = opengame_cfg.word_exchange[1]
	for i = 1, 5 do
		local data = {}
		if jizi_data["item_id_" .. i] then
			data.item_id = jizi_data["item_id_" .. i]
	        self.jizi_item_list[i]:SetData(data)

	        local item_num = 0
			item_num = ItemWGData.Instance:GetItemNumInBagById(jizi_data["item_id_" .. i])
			self.node_list["zi_num_" .. i].text.text = item_num
		end
    end
end

function ServerActivityTabView:KFJZFlushCountDownTime()
	CountDownManager.Instance:RemoveCountDown("kaifujizi_countdown")
	local count_down_time = ServerActivityWGData.Instance:GetOpenServerToSevenDayTime(ACTIVITY_TYPE.RAND_COLLECT_ITEMS)
	if count_down_time > 0 then
		self.node_list.kfjz_lbl_activity_time.text.text = string.format(Language.OpenServer.ActRemainTime2, TimeUtil.FormatSecondDHM6(count_down_time))
		CountDownManager.Instance:AddCountDown(
			"kaifujizi_countdown",
			BindTool.Bind(self.KFJZUpdateCountDown, self),
			BindTool.Bind(self.KFJZFlushCountDownTime, self),
			nil,
			count_down_time,
			1
		)
	else
		self.node_list.kfjz_lbl_activity_time.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
		self.node_list.kfjz_lbl_activity_time.text.color = Str2C3b(COLOR3B.RED)
	end
end

function ServerActivityTabView:KFJZUpdateCountDown(elapse_time, total_time)
	self.node_list.kfjz_lbl_activity_time.text.text =string.format(Language.OpenServer.ActRemainTime2, TimeUtil.FormatSecondDHM6(total_time - elapse_time))
	
end

function ServerActivityTabView:DoKFJZAnimation()
	local tween_info = UITween_CONSTS.ServerActivityTab
	-- UITween.CleanAllTween(GuideModuleName.ServerActivityTabView)
	UITween.FakeHideShow(self.node_list["kfjz_root"])
	UITween.AlphaShow(GuideModuleName.ServerActivityTabView, self.node_list["kfjz_root"], 0, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
end

function ServerActivityTabView:DoKFJZCellListAnimation()
	local tween_info = UITween_CONSTS.ServerActivityTab.ListCellRender
	self.node_list["kfjz_ph_list"]:SetActive(false)
	ReDelayCall(self, function()
		self.node_list["kfjz_ph_list"]:SetActive(true)
		local list = self.kfjz_competition_list:GetAllItems()
		local sort_list = ServerActivityWGData.Instance:GetSortListView(list)
		local count = 0
		for k,v in ipairs(sort_list) do
			if 0 ~= v.index then
				count = count + 1
			end
			v.item:PalyKFJZItemAnim(count)
		end
	end, tween_info.DelayDoTime, "KFJZ_Cell_Tween")
end



-- ItemRender
OpenServerZiTieExchangeItemRender = OpenServerZiTieExchangeItemRender or BaseClass(BaseRender)

function OpenServerZiTieExchangeItemRender:__init()
	self.can_get_reward = false
	self.can_ex_num = 0
end

function OpenServerZiTieExchangeItemRender:__delete()
	if self.reward_item then
		self.reward_item:DeleteMe()
		self.reward_item = nil
	end
end

function OpenServerZiTieExchangeItemRender:LoadCallBack()
	self:CreateChild()
end

function OpenServerZiTieExchangeItemRender:CreateChild()
	self.reward_item = ItemCell.New(self.node_list.reward_root)

	XUI.AddClickEventListener(self.node_list.btn_lingqu, BindTool.Bind1(self.OnClickLingQu, self))
end


function OpenServerZiTieExchangeItemRender:OnClickLingQu()
	if not self.data then
		return
	end
	if not self.can_get_reward then
		if self.max_ex_num <= 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.NotExchange)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.NoEnough)
		end
		return
	end
	if self.can_ex_num <= 1 then
		self:SureExchangeCallBack(1)
		return
	end
	local info_list = {}
	info_list.sure_call_back = BindTool.Bind(self.SureExchangeCallBack, self)
	info_list.max_num = self.can_ex_num
	info_list.min_num = 1
	info_list.cur_num = self.can_ex_num
	info_list.item_data = self.data.reward_item
	info_list.title_str = Language.RedEnvelopes.ChangeItem
	ServerActivityWGCtrl.Instance:OpenActSliderNumView(info_list)
end

function OpenServerZiTieExchangeItemRender:SureExchangeCallBack(ex_num)
	ServerActivityWGCtrl.Instance:SendOpenGameActivityFetchReward(OPEN_SERVER_REWARD_TYPE.REWARD_TYPE_WORD_EXCHANGE_REWARD, self.data.seq, ex_num)
end

function OpenServerZiTieExchangeItemRender:OnFlush()
	local data = self:GetData()
	if not data then
		return
	end

	local exchange_flag = ServerActivityWGData.Instance:GetOpenServerData().oga_word_exchange_flag[self.index]
	local max_ex_num = data.limit - exchange_flag
	local can_ex_num = max_ex_num
	local item_id = 0
	local item_num = 0
	local str = ""
	local item_id_num = 0
	for i = 1, 5 do
		item_id = data["item_id_" .. i]
		-- self.node_list["img_word_"..i]:SetActive(false)
		local word_index = ServerActivityWGData.Instance:GetIndexByItemId(item_id);
		if word_index ~= 0 then
			item_id_num = item_id_num + 1
			self.node_list["img_word_"..word_index]:SetActive(true)
		end
	end
	
	for i = 1, item_id_num do
		item_id = data["item_id_" .. i]
		local x = 1
		if item_id > 0 then
			item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
			if max_ex_num > 0 and item_num < can_ex_num then
				can_ex_num = math.min(item_num, max_ex_num)
			end
		else
			
		end
	end
	self.reward_item:SetData(data.reward_item)
	local flag = can_ex_num > 0 and max_ex_num > 0
	self.can_ex_num = can_ex_num
	self.max_ex_num = max_ex_num
	self.can_get_reward = flag
	-- self.node_list.btn_lingqu.button.interactable = flag
	-- XUI.SetButtonEnabled(self.node_list.btn_lingqu, flag)
	XUI.SetGraphicGrey(self.node_list.btn_lingqu, not flag)
	self.node_list.img_red:SetActive(flag)
	self.node_list.lbl_exchange_time.text.text = string.format(Language.OpenServer.ExchangeTime, max_ex_num)
end

function OpenServerZiTieExchangeItemRender:PalyKFJZItemAnim(item_index)
	if not self.node_list["tween_root"] then return end
	local wait_index = item_index - 1
	wait_index = wait_index < 0 and 0 or wait_index
	local tween_info = UITween_CONSTS.ServerActivityTab.ListCellRender

	UITween.FakeHideShow(self.node_list["tween_root"])
	ReDelayCall(self, function()
		if self.node_list and self.node_list["tween_root"] then
			UITween.MoveAlphaShow(GuideModuleName.ServerActivityTabView, self.node_list["tween_root"], tween_info)
		end

	end, tween_info.NextDoDelay * wait_index, "ji_zi_item_" .. wait_index)
end

----------------------------JiZiItemRender-----------------------------
JiZiItemRender = JiZiItemRender or BaseClass(BaseRender)

function JiZiItemRender:__init()
	XUI.AddClickEventListener(self.node_list.click_btn, BindTool.Bind1(self.ClickCell, self))
end

function JiZiItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return 
	end

	local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)

end
function JiZiItemRender:ClickCell()
	if self.data.item_id then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.data.item_id})
	end
end