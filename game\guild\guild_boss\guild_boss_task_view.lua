GuildBossTaskView = GuildBossTaskView or BaseClass(SafeBaseView)

function GuildBossTaskView:__init()
	self.view_cache_time = 1
	self:AddViewResource(0, "uis/view/guild_boss_prefab", "layout_guild_boss_info")
	self.view_layer = UiLayer.MainUIHigh
    self.is_pass = -1
    self.active_close = false
	self.comin = false
	self.is_safe_area_adapter = true
end

function GuildBossTaskView:__delete()
	self.old_state = nil
end

function GuildBossTaskView:ReleaseCallBack()
	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end

	-- if self.info then
	-- 	-- self.info:SetInstanceParent(self.node_list.layout_fuben)
	-- 	self.info:DeleteMe()
	-- 	self.info = nil
	-- end

	if self.change_state_anim then
		self.change_state_anim:Kill()
		self.change_state_anim = nil
	end

	self.fb_star = -1
	self.is_pass = -1
	self.comin = false
	self.shaizi_num = nil

	if CountDownManager.Instance:HasCountDown("guild_boss_star_time_countdown") then
		CountDownManager.Instance:RemoveCountDown("guild_boss_star_time_countdown")
	end

	if CountDownManager.Instance:HasCountDown("guild_boss_flush_time") then
		CountDownManager.Instance:RemoveCountDown("guild_boss_flush_time")
	end

	if CountDownManager.Instance:HasCountDown("guild_boss_shaizi") then
		CountDownManager.Instance:RemoveCountDown("guild_boss_shaizi")
	end

	if self.mainui_open_complete_handle then
		GlobalEventSystem:UnBind(self.mainui_open_complete_handle)
		self.mainui_open_complete_handle = nil
	end

	self.star_effect = nil

	self.has_load_callback = nil
	self.need_move = nil
	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end
	
	if self.boss_reward_list then
       self.boss_reward_list:DeleteMe()
       self.boss_reward_list = nil
    end
	
	if self.num_list1 then
       self.num_list1:DeleteMe()
       self.num_list1 = nil
    end
	
	if self.num_list2 then
       self.num_list2:DeleteMe()
       self.num_list2 = nil
    end

	self.old_star_num = nil

    if nil ~= self.shrinkbuttons_change then
		GlobalEventSystem:UnBind(self.shrinkbuttons_change)
		self.shrinkbuttons_change = nil
	end

	if nil ~= self.main_menu_icon_change then
		GlobalEventSystem:UnBind(self.main_menu_icon_change)
		self.main_menu_icon_change = nil
	end

	self:RemoveShaiziCountDown()
end

function GuildBossTaskView:ShowIndexCallBack()
    if self.obj then
        self.obj:SetActive(true)
    end

	self.fb_star = -1
end

function GuildBossTaskView:LoadCallBack()
	MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))

	-- if MainuiWGCtrl.Instance:IsLoadMainUiView() then
	-- 	self:InitCallBack()
	-- else
	-- 	self.mainui_open_complete_handle = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.InitCallBack, self))
	-- end

	self.has_load_callback = true
	if self.need_move then
		self.need_move = nil
	end
 	-- for i = 1, 2 do
	-- 	self.node_list["TaskButton" .. i].toggle:AddValueChangedListener(BindTool.Bind(self.OnTaskSelectToggle, self, i))
	-- end
	-- self:SetContentActive(1, true)
	self.rank_list = AsyncListView.New(GuildBossAssistInfoItem,self.node_list.TaskList1)

	self.boss_reward_list = AsyncListView.New(ItemCell,self.node_list.reward_list)
	self.boss_reward_list:SetStartZeroIndex(true)

	XUI.AddClickEventListener(self.node_list["shaizi_btn"], BindTool.Bind(self.OnClickShaiZiBtn,self))
	
	local scence_info = GuildBossWGData.Instance:GetGuildBossSceneInfo()
	local is_pass = scence_info.is_pass == 1
	if is_pass then
		self:ShowShaiZiTips()
	end

	self.node_list.rich_txt.text.text = Language.GuildBoss.BossSceneDec
	self.old_star_num = -1

 	-- self.shrinkbuttons_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind1(self.ShrinkButtonsValueChange, self))
    -- self.main_menu_icon_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_MENU_ICON_CHANGE, BindTool.Bind1(self.MainMenuIconChangeEvent, self))
end

-- function GuildBossTaskView:ShrinkButtonsValueChange(isOn)
--     local menu_ison = MainuiWGCtrl.Instance.view:GetMenuButtonIsOn()
-- 	if isOn or menu_ison then
-- 		self.node_list.shaizi_tips.rect:DOAnchorPosX(316, 0.3)
-- 	else
-- 		self.node_list.shaizi_tips.rect:DOAnchorPosX(-128, 0.3)
-- 	end
-- end

-- function GuildBossTaskView:MainMenuIconChangeEvent(isOn)
-- 	if isOn then
-- 		self.node_list.shaizi_tips.rect:DOAnchorPosX(316, 0.3)
-- 	else
-- 		self.node_list.shaizi_tips.rect:DOAnchorPosX(-128, 0.3)
-- 	end
-- end

function GuildBossTaskView:ShowShaiZiTips()
	if not self:IsLoaded() then
		return
	end
	local mydamage_list = GuildBossWGData.Instance:GetMyGuildBossPersonRankInfo()
	local hurt_val = mydamage_list.hurt_val or 0
	if hurt_val > 0 then
		self:DiceStartTime()
		self.node_list["not_reward_tips"]:SetActive(false)
	else
		self.node_list["not_reward_tips"]:SetActive(true)
		-- self.node_list["boss_shaizi"]:SetActive(false)
		self.node_list["dice_start_time_panel"]:SetActive(false)
	end
end

function GuildBossTaskView:DiceStartTime()
	if CountDownManager.Instance:HasCountDown("dice_start_time") then
		CountDownManager.Instance:RemoveCountDown("dice_start_time")
	end
	local scene_info = GuildBossWGData.Instance:GetGuildBossSceneInfo()
	local dice_start_time = scene_info.dice_start_time
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	
	if dice_start_time > server_time then
		-- self:DOPlayBackwards()
		-- self.node_list["boss_shaizi"]:SetActive(false)
		self.node_list["dice_start_time_panel"]:SetActive(true)
		self:DiceStartRefreshTime(server_time, dice_start_time)
		CountDownManager.Instance:AddCountDown("dice_start_time", BindTool.Bind1(self.DiceStartRefreshTime, self), BindTool.Bind1(self.DiceStartTimeComplete, self), dice_start_time, dice_start_time,1)
	else
		MainuiWGCtrl.Instance:FlushView(0, "guild_boss_shaizi_state", {true})
		self:OnClickShaiZiBtn()
		-- self:PlayForward()
		-- self.node_list["boss_shaizi"]:SetActive(true)
		self.node_list["dice_start_time_panel"]:SetActive(false)
	end
end


function GuildBossTaskView:DiceStartRefreshTime(elapse_time, total_time)
	if total_time - elapse_time > 0 then
		local time = math.floor(total_time - elapse_time)
		self.node_list["dice_start_time_str"].text.text = time
	end
end

function GuildBossTaskView:DiceStartTimeComplete()
	local scene_type = Scene.Instance:GetSceneType()
	if SceneType.GuildBoss == scene_type and self:IsLoaded() then
		MainuiWGCtrl.Instance:FlushView(0, "guild_boss_shaizi_state", {true})
		self:OnClickShaiZiBtn()
		self.node_list["dice_start_time_panel"]:SetActive(false)
	end
end

function GuildBossTaskView:OnTaskSelectToggle(index,is_on)
	if is_on then
        if self.old_state == index then
            return
        end
        self:SetContentActive(index, true)
        if index == 1 then
        	self:FlushRewardCell()
        elseif index == 2 then
        	self:FlushMyMesg()
    	end
        self.old_state = index
    end
end

-- function GuildBossTaskView:SetContentActive(index, active)
--     for i = 1, 2 do
--         if i == index then
--             self.node_list["TargetContent_"..i]:SetActive(active)
--         else
--             self.node_list["TargetContent_"..i]:SetActive(not active)
--         end
--     end
-- end


function GuildBossTaskView:InitCallBack()
	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()

	if self.node_list["task_root"] then
		self.obj = self.node_list["task_root"].gameObject
		self.obj.transform:SetParent(parent.gameObject.transform)
		self.obj.transform.localPosition = Vector3(0, 0, 0)
		self.obj.transform.localScale = Vector3.one
		self.obj:SetActive(true)
	end

    if self.is_out_fb then
        self.obj:SetActive(false)
    end

    self.is_out_fb = nil




	-- self.node_list["layout_star"]:SetActive(false)
	local mainui_ctrl = MainuiWGCtrl.Instance
	-- local parent = mainui_ctrl:GetTaskOtherContent()
	-- self.info = GuildBossTaskList.New(self.node_list.root_fuben_info)
	-- self.info:SetInstanceParent(parent)
	-- self.info:AnchorPosition(2,-128)

	self.task_panel_change = true
	-- mainui_ctrl:SetTaskPanel(false,151,-185)
	mainui_ctrl:SetAutoGuaJi(true)

	-- self:Flush()
end

function GuildBossTaskView:ResetTaskPanel()
	if self.task_panel_change then
		MainuiWGCtrl.Instance:ResetTaskPanel()
		self.task_panel_change = false
	end
end

function GuildBossTaskView:CloseCallBack()
	self.is_out_fb = true
    if self.obj then
        self.obj:SetActive(false)
    end

	self.fb_star = -1
	self.is_pass = -1
	MainuiWGCtrl.Instance:SetAutoGuaJi(false)
	if not self.is_send_end and self.shaizi_num ~= nil then
		self.is_send_end = false
    	GuildBossWGCtrl.Instance:SendCSGuildBossShaiZiEnd()
    end

    if CountDownManager.Instance:HasCountDown("dice_start_time") then
		CountDownManager.Instance:RemoveCountDown("dice_start_time")
	end
end

function GuildBossTaskView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			self:OnFlushView()
		elseif k == "ShowOrHideHurtRankList" then
			self:ShowOrHideHurtRankList(v)
        end
    end
end

function GuildBossTaskView:ShowOrHideHurtRankList(state_info)
	if self.node_list.task_hurt_rank_list then
		local start_num = state_info.state == true and 0 or 1
		local end_num = state_info.state == true and 1 or 0
		self.node_list.task_hurt_rank_list.canvas_group:DoAlpha(start_num, end_num, 0.3)
	end
end

function GuildBossTaskView:OnFlushView()
	local mydamage_list = GuildBossWGData.Instance:GetMyGuildBossPersonRankInfo()
	local hurt_val = mydamage_list and mydamage_list.hurt_val or -1

	self:FlushRewardCell()

	if hurt_val > 0 then
		self:FlushMyMesg()
	end
	self.node_list.task_hurt_rank_list:CustomSetActive(hurt_val > 0)

	local scence_info = GuildBossWGData.Instance:GetGuildBossSceneInfo()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local other_cfg = GuildBossWGData.Instance:GetOtherCfg()

	if scence_info.is_pass ~= 1 then
		if scence_info.next_boss_refresh_timestamp and scence_info.next_boss_refresh_timestamp ~= 0 and server_time < scence_info.next_boss_refresh_timestamp then
			self.node_list["boss_tip"]:SetActive(true)
			self.node_list["count_time"]:SetActive(true)
			local CDM = CountDownManager.Instance
			if CDM:HasCountDown("guild_boss_flush_time") then
				CDM:RemoveCountDown("guild_boss_flush_time")
			end
			CDM:AddCountDown("guild_boss_flush_time", 
				BindTool.Bind(self.UpdateFlushTime, self),
				BindTool.Bind(self.CompleteFlushTime, self), 
				scence_info.next_boss_refresh_timestamp, 
				nil, 
			0.2)

			self:UpdateTime(TimeWGCtrl.Instance:GetServerTime(), TimeWGCtrl.Instance:GetServerTime() + other_cfg.three_star_time_s)
			return
		else
			MainuiWGCtrl.Instance:SetShowTimeTextState(true)
			self.node_list["boss_tip"]:SetActive(false)
			self.node_list["count_time"]:SetActive(false)
		end

		if self.fb_star ~= scence_info.cur_star_num then
			if scence_info.cur_star_num <= 0 then
	 			self:CompleteTime()
	 		else
			 	local time = scence_info.next_star_timestamp - TimeWGCtrl.Instance:GetServerTime()
				self:UpdateTime(TimeWGCtrl.Instance:GetServerTime(), scence_info.next_star_timestamp)
				if CountDownManager.Instance:HasCountDown("guild_boss_star_time_countdown") then
					CountDownManager.Instance:RemoveCountDown("guild_boss_star_time_countdown")
				end
				CountDownManager.Instance:AddCountDown("guild_boss_star_time_countdown", BindTool.Bind1(self.UpdateTime, self), BindTool.Bind1(self.CompleteTime, self), nil,time, 1)
			end
		end
	else
		if CountDownManager.Instance:HasCountDown("guild_boss_flush_time") then
			CountDownManager.Instance:RemoveCountDown("guild_boss_flush_time")
		end
		if CountDownManager.Instance:HasCountDown("guild_boss_star_time_countdown") then
			CountDownManager.Instance:RemoveCountDown("guild_boss_star_time_countdown")
		end
		local str = string.format(Language.GuildBoss.HasKillBoss, Language.Common.StarLevelStr[scence_info.cur_star_num])
		self.node_list["rich_star_tips"].text.text = str
		self.node_list["boss_tip"]:SetActive(false)
		self.node_list["count_time"]:SetActive(false)

		self:FlushShaiZiBtnInfo()
	end

	if self.fb_star == scence_info.cur_star_num and self.is_pass == scence_info.is_pass then return end
	self.fb_star = scence_info.cur_star_num
	self.is_pass = scence_info.is_pass


	self.node_list.lbl_boshu.text.text = Language.FuBen.KillBoss .. string.format("  <color=#9DF5A7>%s/%d</color>", scence_info.is_pass, 1)

	-- self.info:SetBoCount(Language.FuBen.KillBoss .. string.format("  <color=#9DF5A7>%s/%d</color>", scence_info.is_pass, 1))
	local time = scence_info.next_star_timestamp - TimeWGCtrl.Instance:GetServerTime()

	-- 3 SSS 2 SS 1 S 0 A

	local is_show_anim = false
	if scence_info.cur_star_num ~= self.old_star_num then
		self.old_star_num = scence_info.cur_star_num
		is_show_anim = true
	end

	local star_res = GetSpecialStarImgResByStar5(scence_info.cur_star_num)
	local bundle, asset = ResPath.GetCommonImages(star_res)
    self.node_list.level_img.image:LoadSprite(bundle, asset, function()
        self.node_list.level_img.image:SetNativeSize()
    end)

	local eff_bundle, eff_asset = ResPath.GetStarLevelUIEffect(scence_info.cur_star_num)
    self.node_list.level_effct:ChangeAsset(eff_bundle, eff_asset)

	if is_show_anim then
		self:ChangeStarAnim()
	end
end

--刷新奖励格子
function GuildBossTaskView:FlushRewardCell()
	local reward_cfg = GuildBossWGData.Instance:GetGuildBossRewards()
	local reward_list = reward_cfg and reward_cfg.reward_item or {}
	self.boss_reward_list:SetDataList(reward_list)
end

--刷新个人信息
function GuildBossTaskView:FlushMyMesg()
	--获取总排行榜数据
	local all_damage_list = GuildBossWGData.Instance:GetGuildBossRankListInfo()
	self.rank_list:SetDataList(all_damage_list,3)
	--个人信息
	local mydamage_list = GuildBossWGData.Instance:GetMyGuildBossPersonRankInfo()
	local hurt_val = mydamage_list and mydamage_list.hurt_val or 0
	local rank = mydamage_list and mydamage_list.rank or 0
	local myhurt = CommonDataManager.ConverNumber(hurt_val)

	self.node_list["myname"].text.text = Language.Camp.RoleHurt2 
	self.node_list["my_damate"].text.text = myhurt == 0 and "" or myhurt

	if rank <= 3 and rank > 0 then
        -- local bundle, asset = ResPath.GetCommonIcon("a3_tb_jp"..rank)
		local bundle, asset = ResPath.GetCommonImages("a3_hurt_list_rank_" .. rank)
        self.node_list["rank_img"].image:LoadSprite(bundle, asset)
    else
        self.node_list["my_paiming"].text.text = rank 
    end
    self.node_list["rank_img"]:SetActive(rank <= 3 and hurt_val > 0)
    self.node_list["my_paiming"]:SetActive(rank > 3 and hurt_val > 0)
    --刷新个人
    local hurt_rank_list = GuildBossWGData.Instance:GetGuildBossRankListInfo()
    --最大伤害
    if hurt_rank_list[1] ~= nil then
   	 	local max_hurt_val = hurt_rank_list[1].hurt_val
   	 	if max_hurt_val <= 0 or max_hurt_val == nil then
			max_hurt_val = 1
		end
	-- 	self.node_list.hunt_slider.slider.value = hurt_val / max_hurt_val
    -- else
    -- 	self.node_list.hunt_slider.slider.value = 0
    end
end


function GuildBossTaskView:UpdateTime(elapse_time, total_time)
	local scence_info = GuildBossWGData.Instance:GetGuildBossSceneInfo()
	if total_time - elapse_time > 0 then
		local str = ""
		if scence_info.cur_star_num > 0 then
			local star_str = NumberToChinaNumber(scence_info.cur_star_num)
			str = string.format(Language.GuildBoss.StarTaskStr, TimeUtil.FormatSecond((total_time - elapse_time), 2), Language.Common.StarLevelStr[star_str])
		else
			str = string.format(Language.GuildBoss.StarTaskStr1, TimeUtil.FormatSecond((total_time - elapse_time), 2))
		end
		self.node_list["rich_star_tips"].text.text = str
	end
end

function GuildBossTaskView:CompleteTime()
	local scence_info = GuildBossWGData.Instance:GetGuildBossSceneInfo()
	if scence_info.cur_star_num > 0 then return end


	local temp_time = FuBenPanelWGData.Instance:GetOutTimer()
	local time = temp_time - TimeWGCtrl.Instance:GetServerTime()
	MainuiWGCtrl.Instance:SetFbIconEndCountDown(temp_time)
	self:UpdateTime(TimeWGCtrl.Instance:GetServerTime(), temp_time)

	if CountDownManager.Instance:HasCountDown("guild_boss_star_time_countdown") then
		CountDownManager.Instance:RemoveCountDown("guild_boss_star_time_countdown")
    end

    if time > 0 then
        CountDownManager.Instance:AddCountDown("guild_boss_star_time_countdown", BindTool.Bind1(self.UpdateTime, self),
        nil, nil, time, 1)
    end
end

function GuildBossTaskView:UpdateFlushTime(elapse_time, total_time)
	local time = GameMath.Round(total_time - elapse_time)

	if time > 0 and (nil == self.flush_cache_time or self.flush_cache_time ~= time)  then
		self.node_list["count_time"].text.text = time
		self.flush_cache_time = time
		self.node_list["boss_tip"].rect.localScale = Vector3(3,3,1)
		self.node_list["boss_tip"].rect:DOScale(Vector3.one, 0.3)
		EffectManager.Instance:PlayAtTransform("effects/prefab/ui/ui_daojishi_1_prefab", "UI_daojishi_1", self.node_list.effect_point.transform)
	end
end

function GuildBossTaskView:CompleteFlushTime()
	self:Flush()
end

function GuildBossTaskView:StopStarCountDown()
	if CountDownManager.Instance:HasCountDown("guild_boss_star_time_countdown") then
		CountDownManager.Instance:RemoveCountDown("guild_boss_star_time_countdown")
	end
end

function GuildBossTaskView:SetShaiZiBtnActive(flag)
	if self:IsLoaded() then
		self.node_list["shaizi_btn"]:SetActive(flag)
	end
end

function GuildBossTaskView:HideShaiZiBtn(all_close)
	if self:IsLoaded() and self.node_list["shaizi_btn"] and all_close then
		self.node_list["shaizi_btn"]:SetActive(true)
		self.node_list["shaizi_btn"].button.enabled = false
		self.node_list["shaizi_btn"].transform.localScale = Vector3(1,1,1)
		UITween.ScaleShowPanel(self.node_list["shaizi_btn"], Vector3(0,0,0), 0.2,  DG.Tweening.Ease.Linear,function ()
			self.node_list["shaizi_btn"].button.enabled = true
		end) 
	end
end

function GuildBossTaskView:OnClickShaiZiBtn()
	local chat_open = ChatWGCtrl.Instance:IsOpenTransmitPopView()
		MainuiWGCtrl.Instance:HideOrShowChatView(true)
		GuildBossWGCtrl.Instance:OpenGuildBossReward()
		-- GuildBossWGCtrl.Instance:ShowGuildBossShaiZiPanel()
		self.node_list["shaizi_btn"]:SetActive(false)
	-- end
end

function GuildBossTaskView:FlushShaiZiBtnInfo()
	local mydamage_list = GuildBossWGData.Instance:GetMyGuildBossPersonRankInfo()
	local hurt_val = mydamage_list.hurt_val or 0

	local shaizi_num = GuildBossWGData.Instance:GetDiceRoundShaiZiNum()
	local scene_info = GuildBossWGData.Instance:GetGuildBossSceneInfo()
	local dice_end_time = scene_info.dice_end_time
	local dice_start_time = scene_info.dice_start_time

	local dice_round = scene_info.max_dice_rounds or 1
	local cur_dice_round = scene_info.dice_rounds or 1

	self.node_list.lunci_bg:CustomSetActive(dice_start_time <= 0)

	local time = scene_info.dice_end_time
	local server_time = TimeWGCtrl.Instance:GetServerTime()

	if dice_start_time <= 0 then
		if (time < server_time) and (cur_dice_round >= dice_round) then
			self.node_list["desc_shaizi_lunci"].text.text = ToColorStr(Language.GuildBoss.AnswerIsEnd, COLOR3B.C10)
		else
			self.node_list["desc_shaizi_lunci"].text.text = string.format(Language.GuildBoss.ShaiZTitle1, cur_dice_round, dice_round)
		end
	end

	self:RemoveShaiziCountDown()

	if time > server_time then
		CountDownManager.Instance:AddCountDown("guild_boss_task_shaizi", 
		function (elapse_time, total_time)
			if self.node_list["shaizi_slider"] then
				local scene_info = GuildBossWGData.Instance:GetGuildBossSceneInfo()
				local end_time = scene_info.dice_end_time
	
				if total_time - elapse_time > 0 then
					local server_time = TimeWGCtrl.Instance:GetServerTime()
					self.node_list["shaizi_slider"].image.fillAmount = (end_time - server_time) / 10
					-- self.node_list["time_num"].text.text = math.floor(total_time - elapse_time)
				end
			end
		end, 
		function ()
			if self.node_list["shaizi_slider"] then
				self.node_list["shaizi_slider"].image.fillAmount = 0
			end

			self:RemoveShaiziCountDown()
		end, time, time, 0.02)
	end
end

function GuildBossTaskView:RemoveShaiziCountDown()
	if CountDownManager.Instance:HasCountDown("guild_boss_task_shaizi") then
		CountDownManager.Instance:RemoveCountDown("guild_boss_task_shaizi")
	end
end

function GuildBossTaskView:ChangeStarAnim()
	if self.change_state_anim then
		self.change_state_anim:Kill()
		self.change_state_anim = nil
	end
	
	self.node_list.level_parent_node.transform.localScale = u3dpool.vec3(3, 3, 3)
	self.change_state_anim = DG.Tweening.DOTween.Sequence()
	self.change_state_anim:Append(self.node_list.level_parent_node.rect:DOScale(Vector3(1, 1, 1), 1.2):SetEase(DG.Tweening.Ease.OutBack))
	self.node_list.tanchu_effect:SetActive(false)
	self.node_list.tanchu_effect:SetActive(true)
	self.change_state_anim:OnComplete(function ()
		self.node_list.tanchu_effect:SetActive(false)
	end)
end

---------------------

-- GuildBossTaskList = GuildBossTaskList or BaseClass(BaseRender)
-- function GuildBossTaskList:__init()
-- 	-- body
-- end

-- function GuildBossTaskList:__delete()
-- 	-- body
-- end

-- function GuildBossTaskList:AnchorPosition( x,y )
-- 	self.view.rect.anchoredPosition = Vector2(x,y)
-- end

-- function GuildBossTaskList:SetBoCount( value )
-- 	self.node_list.lbl_boshu.text.text = value
-- end

-- function GuildBossTaskList:SetDesc( str )
-- 	self.node_list.rich_txt.text.text = str
-- end

-- function GuildBossTaskList:LoadCallBack()
-- 	self:SetDesc(Language.GuildBoss.BossSceneDec)
-- end



-----------------------------GuildBossAssistInfoItem  内部排行榜item--------------------------

GuildBossAssistInfoItem = GuildBossAssistInfoItem or BaseClass(BaseRender)

function GuildBossAssistInfoItem:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.node_list.num.text.text = self.index >= 4 and self.index or ""
	local bg_bundle, bg_asset = ResPath.GetCommonImages("a3_hurt_list_bg_4")
	if self.index < 4 then    
		bg_bundle, bg_asset = ResPath.GetCommonImages("a3_hurt_list_bg_" .. self.index)
		self.node_list["rank_img"]:SetActive(true)
		self.node_list["rank_img"].image:LoadSprite(ResPath.GetCommonImages("a3_hurt_list_rank_" .. self.index))
	else
		self.node_list["rank_img"]:SetActive(false)
    end

	self.node_list["Fill"].image:LoadSprite(bg_bundle, bg_asset)
	local name = self.data.user_name
	local hurt_val = self.data.hurt_val
	local role_info= RoleWGData.Instance:GetRoleInfo()
	local damage_info = CommonDataManager.ConverNumber(hurt_val)
	if name == role_info.name then 
		self.node_list.name.text.text = name
		self.node_list.damage.text.text = damage_info
	else
		self.node_list.name.text.text = name
		self.node_list.damage.text.text = damage_info
	end

	-- if self.index <= 3 then
    --     local bundle, asset = ResPath.GetCommonIcon("a3_tb_jp"..self.index)
    --     self.node_list["rank_img"].image:LoadSprite(bundle, asset)
    -- else
    --     local num = self.data.rank or self.index
    --     self.node_list["num"].text.text = ToColorStr(num, COLOR3B.GREEN)
    -- end
    -- self.node_list["rank_img"]:SetActive(self.index <= 3)
    -- self.node_list["num"]:SetActive(self.index > 3)
	-- local name = self.data.user_name
	-- local hurt_val = self.data.hurt_val
	-- local role_info= RoleWGData.Instance:GetRoleInfo()
	-- local damage_info = CommonDataManager.ConverNumber(hurt_val)
	-- if name == role_info.name then 
	-- 	self.node_list.name.text.text = name
	-- 	self.node_list.damage.text.text = damage_info
	-- else
	-- 	self.node_list.name.text.text = name
	-- 	self.node_list.damage.text.text = damage_info
	-- end
	-- self.node_list.damage.text.text = CommonDataManager.ConverNumber(hurt_val)
	-- self:CellImageFillAmount()
end

function GuildBossAssistInfoItem:CellImageFillAmount()
	local hurt_rank_list = GuildBossWGData.Instance:GetGuildBossRankListInfo()
	if hurt_rank_list then
		local hurt_val = hurt_rank_list[1].hurt_val
		if hurt_val <= 0 or hurt_val == nil then
			hurt_val = 1
		end
		self.node_list.hunt_slider.slider.value = self.data.hurt_val / hurt_val
	else
		self.node_list.hunt_slider.slider.value = 0
	end
end
