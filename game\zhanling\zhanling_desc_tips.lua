ZhanLingDescTips = ZhanLingDescTips or BaseClass(SafeBaseView)
function ZhanLingDescTips:__init()
	self:SetMaskBg(true)
    self:SetMaskBgAlpha(0)
	self.view_layer = UiLayer.PopWhite
    self:AddViewResource(0, "uis/view/zhanling_ui_prefab", "layout_zhanling_desc_tips")
end

function ZhanLingDescTips:ReleaseCallBack()
    self.show_data = nil
end

--{anchoredPosition = , show_text = }
function ZhanLingDescTips:SetData(data)
    self.show_data = data
    self:Open()
end

function ZhanLingDescTips:OnFlush()
    self.node_list.node.rect.anchoredPosition = self.show_data.anchoredPosition
    self.node_list.Text.text.text = self.show_data.show_text
end
