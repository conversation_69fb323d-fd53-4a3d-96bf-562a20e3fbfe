FairyLandStrengthenMasterView = FairyLandStrengthenMasterView or BaseClass(SafeBaseView)

local FLE_SM_TYPE = {
	Normal = 1,
	Spec = 2,
}

function FairyLandStrengthenMasterView:__init()
	self.view_name = "FairyLandStrengthenMasterView"
    self:SetMaskBg(true, true)
    local bundle = "uis/view/fairy_land_equipment_ui_prefab"
	self:AddViewResource(0, bundle, "layout_fle_strength_master")
end

function FairyLandStrengthenMasterView:ReleaseCallBack()
	if not IsEmptyTable(self.item_render_list) then
		for k, v in pairs(self.item_render_list) do
			v:DeleteMe()
		end
		self.item_render_list = nil
	end
	self.open_slot = nil
end

function FairyLandStrengthenMasterView:LoadCallBack()
	self.item_render_list = {}
	local max_cell = 1
	local res_async_loader = AllocResAsyncLoader(self, "fle_strength_master_cell")
	res_async_loader:Load("uis/view/fairy_land_equipment_ui_prefab", "fle_strength_master_cell", nil,
	function(new_obj)
		for i = 1, max_cell do
			local obj = ResMgr:Instantiate(new_obj)
			obj.transform:SetParent(self.node_list["content_root"].transform, false)
			table.insert(self.item_render_list, FLEStrengthenMasterRender.New(obj))
		end
		self:Flush()
	end)
	self.open_slot = nil
	self.node_list["fle_strength_title"].text.text = Language.FairyLandEquipment.StrengthMaster
end

function FairyLandStrengthenMasterView:SetCurShowSlot(slot)
	self.open_slot = slot
end

function FairyLandStrengthenMasterView:OnFlush()
	if not IsEmptyTable(self.item_render_list) then
		local temp_data = {}
		for i, v in ipairs(self.item_render_list) do
			temp_data.open_slot = self.open_slot
			v:SetData(temp_data)
		end
	end
end

-- 展示特效
function FairyLandStrengthenMasterView:ShowEffect(effect_type, is_success)
	if self.node_list["effect_node"] then
		TipWGCtrl.Instance:ShowEffect({effect_type = effect_type, is_success = is_success,
			pos = Vector2(0, 0), parent_node = self.node_list["effect_node"]})
	end
end

---------------------------------------------------------------------------------
----- FLEStrengthenMasterRender start----------------------------------
FLEStrengthenMasterRender = FLEStrengthenMasterRender or BaseClass(BaseRender)

function FLEStrengthenMasterRender:__init()
	self.cur_attr_list = {}
	self.next_attr_list = {}
	for i = 1, 5 do
		self.cur_attr_list[i] = CommonAttrRender.New(self.node_list.layout_cur_attr_add:FindObj("rich_cur_" .. i))
		self.next_attr_list[i] = CommonAttrRender.New(self.node_list.layout_next_attr_add:FindObj("rich_next_" .. i))
		self.cur_attr_list[i]:SetAttrNameNeedSpace(true)
		self.next_attr_list[i]:SetAttrNameNeedSpace(true)
	end

    XUI.AddClickEventListener(self.node_list["left_active_btn"], BindTool.Bind1(self.OnClickActiveBtn, self))
    self.node_list["lbl_attr_title"].text.text = Language.FairyLandEquipment.StrengthenTotalAttrTitle
end

function FLEStrengthenMasterRender:__delete()
    if self.cur_attr_list then
		for k,v in pairs(self.cur_attr_list) do
			v:DeleteMe()
		end
	end
	self.cur_attr_list = nil

	if self.next_attr_list then
		for k,v in pairs(self.next_attr_list) do
			v:DeleteMe()
		end
	end
	self.next_attr_list = nil
end

function FLEStrengthenMasterRender:OnFlush()
	local cur_select_slot = self.data.open_slot or FairyLandEquipmentWGCtrl.Instance:GetViewSelectSlotIndex()
	local cur_total_act_level = FairyLandEquipmentWGData.Instance:GetFLEStrengthenActiveTotalLv(cur_select_slot)
	local cur_total_level = FairyLandEquipmentWGData.Instance:GetFLEStrengthenAllLevel(cur_select_slot)
	local now_attr_cfg = FairyLandEquipmentWGData.Instance:GetFLEStrengthenTotalCfg(cur_select_slot)
	local next_attr_cfg = FairyLandEquipmentWGData.Instance:GetFLEStrengthenTotalCfg(cur_select_slot, true)
	local is_max_level = not next_attr_cfg
	local has_attr_add = cur_total_act_level > 0--是否有加成
	local can_active = not is_max_level and cur_total_level >= next_attr_cfg.level--是否可激活

	if now_attr_cfg then
		local attr_list = EquipWGData.GetSortAttrListByCfg(now_attr_cfg)
		for k,v in pairs(self.cur_attr_list) do
			v:SetData(attr_list[k])
		end
	end

	if next_attr_cfg then
		local attr_list = EquipWGData.GetSortAttrListByCfg(next_attr_cfg)
		for k,v in pairs(self.next_attr_list) do
			v:SetData(attr_list[k])
		end
	end

	--标题
	self.node_list["cur_level_bg"]:SetActive(has_attr_add)
	if has_attr_add then
		self.node_list["now_level"].text.text = string.format(Language.FairyLandEquipment.StrengthenTotalAttrTitle_1, cur_total_level)
	end
	self.node_list["next_level_bg"]:SetActive(not is_max_level)
	if is_max_level then	
    	self.node_list["next_level"].text.text = Language.FairyLandEquipment.StrengthenTotalAttrTitleMax
	else
    	local next_desc = string.format(Language.FairyLandEquipment.StrengthenTotalAttrTitle_1, next_attr_cfg.level)
    	local color = cur_total_level >= next_attr_cfg.level and COLOR3B.GREEN or COLOR3B.RED
    	local next_pro_desc = ToColorStr(string.format("(%s/%s)", cur_total_level, next_attr_cfg.level), color)
    	self.node_list["next_level"].text.text = next_desc .. next_pro_desc
	end

    --属性列表
    self.node_list["no_attr_tip"]:SetActive(not has_attr_add)
    self.node_list["layout_cur_attr_add"]:SetActive(has_attr_add)
    self.node_list["max_attr_tip"]:SetActive(is_max_level)
    self.node_list["layout_next_attr_add"]:SetActive(not is_max_level)

    --按钮
    self.node_list["yimanji"]:SetActive(is_max_level)
    self.node_list["left_active_btn"]:SetActive(not is_max_level)
    if not is_max_level then
    	XUI.SetGraphicGrey(self.node_list["left_active_btn"], not can_active)
    	self.node_list["left_active_btn"].button.enabled = can_active
    end
    self.node_list["remind"]:SetActive(not is_max_level and can_active)
end

function FLEStrengthenMasterRender:OnClickActiveBtn()
	local cur_select_slot = FairyLandEquipmentWGCtrl.Instance:GetViewSelectSlotIndex()
	local cur_total_level = FairyLandEquipmentWGData.Instance:GetFLEStrengthenAllLevel(cur_select_slot)
	local next_attr_cfg = FairyLandEquipmentWGData.Instance:GetFLEStrengthenTotalCfg(cur_select_slot, true)
	if next_attr_cfg and cur_total_level >= next_attr_cfg.level then
		FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.ACT_TOTAL_LEVEL, cur_select_slot, next_attr_cfg.level)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FairyLandEquipment.StrengthenMasterNotEnough)
	end
end
----- FLEStrengthenMasterRender end------------------------------------------
---------------------------------------------------------------------------------