require("game/person_mabiboss/person_mabiboss_scene_view")
require("game/person_mabiboss/person_mabiboss_wg_data")

PersonMaBiBossWGCtrl = PersonMaBiBossWGCtrl or BaseClass(BaseWGCtrl)

function PersonMaBiBossWGCtrl:__init()
	if PersonMaBiBossWGCtrl.Instance then
		print_error("[PersonMaBiBossWGCtrl]:Attempt to create singleton twice!")
	end
	PersonMaBiBossWGCtrl.Instance = self

    self.data = PersonMaBiBossWGData.New()
    self.view = PersonMaBiBossSceneView.New()

	self:RegisterProtocol(SCPalsyExperienceInfo, "OnSCPalsyExperienceInfo")
	self:RegisterProtocol(SCPalsyExperienceFbInfo, "OnSCPalsyExperienceFbInfo")

	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.OnDayChange, self))
	self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
end

function PersonMaBiBossWGCtrl:__delete()
	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end
	
	PersonMaBiBossWGCtrl.Instance = nil
end

function PersonMaBiBossWGCtrl:OpenSceneView()
	if self.view and not self.view:IsOpen() then
		self.view:Open()
	end
end

function PersonMaBiBossWGCtrl:CloseSceneView()
	if self.view and self.view:IsOpen() then
		self.view:Close()
	end
end

function PersonMaBiBossWGCtrl:OnSCPalsyExperienceInfo(protocol)
	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.BOSS_MABI_FB)
	self.data:SetPalsyExperienceInfo(protocol)
end

function PersonMaBiBossWGCtrl:OnSCPalsyExperienceFbInfo(protocol)
	self.data:SetPalsyExperienceFbInfo(protocol)

	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end

	if 1 == protocol.is_finish then
		if 1 ==  protocol.is_pass then
			local data_list = SortDataByItemColor(self.data:GetRewardDataList())
			-- FuBenWGCtrl.Instance:OpenWin(SceneType.PERSON_MABI_BOSS_DISPLAY, data_list, 0, 0, 0, 10)
			TipWGCtrl.Instance:ShowGetReward(nil, data_list, false, nil, nil, nil, nil, function ()
				MainuiWGCtrl.Instance:LevelFB()
			end)
		else
			FuBenWGCtrl.Instance:OpenLose(SceneType.PERSON_MABI_BOSS_DISPLAY)
		end
	end
end

function PersonMaBiBossWGCtrl:CheckBossMabiActState()
	local act_cfg = self.data:GetBossMaBiActInfo()

	if IsEmptyTable(act_cfg) then
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.BOSS_MABI_FB, ACTIVITY_STATUS.CLOSE)
		return
	end

	local role_level = RoleWGData.Instance:GetAttr('level')
	local level_limit = role_level >= act_cfg.level
    local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local day_limit = server_day >= act_cfg.act_serveropen and server_day <= act_cfg.act_serverover_day

	-- local stat = MainuiWGData.Instance:CheckMainUIActIsLimit(act_cfg)
	local act_state = level_limit and day_limit and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE
	ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.BOSS_MABI_FB, act_state)
end

function PersonMaBiBossWGCtrl:OnDayChange()
	self:CheckBossMabiActState()
end

function PersonMaBiBossWGCtrl:OnRoleAttrChange()
	self:CheckBossMabiActState()
end