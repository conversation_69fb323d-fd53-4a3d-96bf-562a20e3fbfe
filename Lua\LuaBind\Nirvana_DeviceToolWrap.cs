﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_DeviceToolWrap
{
	public static void Register(LuaState L)
	{
		L.BeginStaticLibs("DeviceTool");
		<PERSON><PERSON>Function("GetDeviceID", GetDeviceID);
		<PERSON><PERSON>Function("GetVersion", GetVersion);
		<PERSON><PERSON>ction("GetNetworkAccessibility", GetNetworkAccessibility);
		<PERSON><PERSON>unction("Quit", Quit);
		<PERSON><PERSON>unction("SetScreenBrightness", SetScreenBrightness);
		<PERSON><PERSON>Function("GetScreenBrightness", GetScreenBrightness);
		<PERSON><PERSON>unction("GetUserScreenBrightness", GetUserScreenBrightness);
		<PERSON><PERSON>unction("GetScreenSafeArea", GetScreenSafeArea);
		<PERSON><PERSON>RegFunction("GetScreenSafeAreaFix", GetScreenSafeAreaFix);
		<PERSON><PERSON>ction("GetHomeSafeHei<PERSON>", GetHomeSafeHeight);
		<PERSON><PERSON>("GetObbFilePath", GetObbFilePath);
		<PERSON><PERSON>("IsEmulator", IsEmulator);
		<PERSON><PERSON>unction("IsEmulator2", IsEmulator2);
		L.RegFunction("IsAndroid64", IsAndroid64);
		L.RegFunction("RestartApplication", RestartApplication);
		L.RegFunction("UpdateApk", UpdateApk);
		L.EndStaticLibs();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetDeviceID(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			string o = Nirvana.DeviceTool.GetDeviceID();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetVersion(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			string o = Nirvana.DeviceTool.GetVersion();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetNetworkAccessibility(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = Nirvana.DeviceTool.GetNetworkAccessibility();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Quit(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			Nirvana.DeviceTool.Quit();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetScreenBrightness(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 1);
			Nirvana.DeviceTool.SetScreenBrightness(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetScreenBrightness(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			float o = Nirvana.DeviceTool.GetScreenBrightness();
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetUserScreenBrightness(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			float o = Nirvana.DeviceTool.GetUserScreenBrightness();
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetScreenSafeArea(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Rect arg0;
			int arg1;
			int arg2;
			Nirvana.DeviceTool.GetScreenSafeArea(out arg0, out arg1, out arg2);
			ToLua.PushValue(L, arg0);
			LuaDLL.lua_pushinteger(L, arg1);
			LuaDLL.lua_pushinteger(L, arg2);
			return 3;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetScreenSafeAreaFix(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Rect arg0;
			int arg1;
			int arg2;
			Nirvana.DeviceTool.GetScreenSafeAreaFix(out arg0, out arg1, out arg2);
			ToLua.PushValue(L, arg0);
			LuaDLL.lua_pushinteger(L, arg1);
			LuaDLL.lua_pushinteger(L, arg2);
			return 3;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetHomeSafeHeight(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			float o = Nirvana.DeviceTool.GetHomeSafeHeight();
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetObbFilePath(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			string o = Nirvana.DeviceTool.GetObbFilePath();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsEmulator(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = Nirvana.DeviceTool.IsEmulator();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsEmulator2(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			string o = Nirvana.DeviceTool.IsEmulator2(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsAndroid64(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = Nirvana.DeviceTool.IsAndroid64();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RestartApplication(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			Nirvana.DeviceTool.RestartApplication();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UpdateApk(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			bool o = Nirvana.DeviceTool.UpdateApk(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

