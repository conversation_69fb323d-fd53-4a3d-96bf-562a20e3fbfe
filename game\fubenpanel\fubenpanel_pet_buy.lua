PetBuyView = PetBuyView or BaseClass(DayCountChangeView)

function PetBuyView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Normal
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_pet_msg")
end

function PetBuyView:__delete()

end

function PetBuyView:ReleaseCallBack()
	DayCountChangeView.ReleaseCallBack(self)
	if self.copper_vip_num1 then
		self.copper_vip_num1:DeleteMe()
		self.copper_vip_num1 = nil
	end

	if self.copper_vip_num2 then
		self.copper_vip_num2:DeleteMe()
		self.copper_vip_num2 = nil
	end
end

function PetBuyView:CloseCallBack()
	self.fb_type = nil
end

function PetBuyView:LoadCallBack()
	DayCountChangeView.LoadCallBack(self)
	self:SetSecondView(nil, self.node_list["size"])
	self.node_list["title_view_name"].text.text = Language.ViewName.CiShuZengJia
	--self.node_list["layout_commmon_second_root"].rect.sizeDelta = Vector2(589,412)
	local other_cfg = FuBenPanelWGData.Instance:GetPetOtherCfg()
	local gold = other_cfg.gold
	if FUBEN_TYPE.HIGH_TEAM_EQUIP == self.fb_type then
		other_cfg = TeamEquipFbWGData.Instance:GetHighTeamEquipFbOther()
		gold = other_cfg.buy_times_need_gold
	end
	local str = string.format(Language.FuBenPanel.CopperBuyNum, gold)
	self.node_list["rich_buy_desc"].text.text = str
	self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind(self.OnClinkCancelHandler, self))
	self.node_list["btn_buy_count"].button:AddClickListener(BindTool.Bind(self.OnClinkBuyCount, self))

end

function PetBuyView:SetIsOnce(is_once)
	self.is_once = is_once
end

function PetBuyView:SetFubenInfo(param)
	self.fb_type = param.fb_type
end

function PetBuyView:OnFlush()
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	local vip_buy_cfg = FuBenPanelWGData.Instance:GetVipBuyPetCfg()
	local next_vip =  FuBenPanelWGData.Instance:GetVipNextPetCount(role_vip)
	local fb_info = FuBenPanelWGData.Instance:GetPetAllInfo()
	local des
	local temp_str = vip_buy_cfg["param_" .. role_vip] - fb_info.buy_times
	
	if temp_str > 0 then
    	des = string.format(Language.FuBenPanel.CopperBuyTips, vip_buy_cfg["param_" .. role_vip] - fb_info.buy_times, vip_buy_cfg["param_" .. role_vip])
    else
    	des = string.format(Language.FuBenPanel.CopperBuyTipsNotnough, vip_buy_cfg["param_" .. role_vip] - fb_info.buy_times, vip_buy_cfg["param_" .. role_vip])
    end
	--local des = string.format(Language.FuBenPanel.CopperBuyTips, vip_buy_cfg["param_" .. role_vip] - fb_info.buy_times, vip_buy_cfg["param_" .. role_vip])
	if FUBEN_TYPE.HIGH_TEAM_EQUIP == self.fb_type then
		vip_buy_cfg = TeamEquipFbWGData.Instance:GetHTEVipCfg()
		next_vip =  TeamEquipFbWGData.Instance:GetVipBuyTimes(TeamEquipFbWGData.Instance:GetHTEVipCfg(), role_vip)
		local buy_times = TeamEquipFbWGData.Instance:GetHTEFbVipBuyTimes()
	local temp_str = vip_buy_cfg["param_" .. role_vip] - buy_times
	
	if temp_str > 0 then
    	des = string.format(Language.FuBenPanel.CopperBuyTips, vip_buy_cfg["param_" .. role_vip] - buy_times, vip_buy_cfg["param_" .. role_vip])
    else
    	des = string.format(Language.FuBenPanel.CopperBuyTipsNotnough, vip_buy_cfg["param_" .. role_vip] - buy_times, vip_buy_cfg["param_" .. role_vip])
    end
		--des = string.format(Language.FuBenPanel.CopperBuyTips, vip_buy_cfg["param_" .. role_vip] - buy_times, vip_buy_cfg["param_" .. role_vip])
		-- if (vip_buy_cfg["param_" .. role_vip] - buy_times) <= 0 then
		-- --self:Close()
		-- print_error("关闭界面")
		-- end
	end 
	
	self.node_list["rich_buy_des_1"].text.text = des
	local max_vip = VipWGData.Instance:GetMaxVIPLevel()
	if role_vip < max_vip and vip_buy_cfg["param_" .. role_vip + 1] then
		if vip_buy_cfg["param_" .. role_vip + 1] > vip_buy_cfg["param_" .. role_vip] then
			des = string.format(Language.FuBenPanel.CopperBuyTips2, vip_buy_cfg["param_" .. role_vip + 1])
		else
			for i = 1, max_vip do
				if vip_buy_cfg["param_" .. i] > vip_buy_cfg["param_" .. role_vip] then
					des = string.format(Language.FuBenPanel.CopperBuyTips2, vip_buy_cfg["param_" .. i])
					next_vip = i

					break
				end
			end
		end
	else
		--des = string.format(Language.FuBenPanel.CopperBuyTips2, vip_buy_cfg["param_" .. role_vip])
		des = Language.FuBenPanel.CopperBuyTips3
	end
	if role_vip >= next_vip then
		des = Language.FuBenPanel.CopperBuyTips3
	end
	
	self.node_list["rich_buy_des_2"].text.text = des


	if self.node_list["img_vip_curlevel"] and self.node_list["img_vip_nexlevel"] then
		self.node_list["img_vip_curlevel"].text.text = "V"..role_vip

		if role_vip < next_vip then
			self.node_list["img_vip_nexlevel"].text.text = "V"..next_vip

			-- self.copper_vip_num2:SetNumber(next_vip)

			self.node_list["layout_next_vip"]:SetActive(true)	
			self.node_list["img_arrow"]:SetActive(true)	

		
		else
			self.node_list["img_vip_nexlevel"].text.text = "V"..role_vip
			
			self.node_list["layout_next_vip"]:SetActive(true)	
			self.node_list["img_arrow"]:SetActive(true)	

			
		end
	end
end

function PetBuyView:OnClinkCancelHandler()
	self:Close()
end

function PetBuyView:OnClinkBuyCount()
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	local vip_buy_cfg = FuBenPanelWGData.Instance:GetVipBuyPetCfg()
	local pet_info = FuBenPanelWGData.Instance:GetPetAllInfo()
	local buy_times = vip_buy_cfg["param_" .. role_vip] - pet_info.buy_times
	local is_max_count = vip_buy_cfg["param_" .. 15] == pet_info.buy_times
	--print_error(vip_buy_cfg["param_" .. 15],pet_info.buy_times,is_max_count)

	if FUBEN_TYPE.HIGH_TEAM_EQUIP == self.fb_type then
		self:HighTeamEquip()
		return
	end

	if self.is_once then
		FuBenPanelWGCtrl.Instance:SendChongWuFbOperate(NEW_PETFB_REQ_TYPE.NEW_PETFB_REQ_TYPE_BUY_TIMES)
		--self:Close()
		return
	end
	
	if 0 == buy_times then
		if is_max_count then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.BuyMaxCount)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.VIPTooLow)
		end
		--self:Close()
	elseif 1 == buy_times then
		FuBenPanelWGCtrl.Instance:SendChongWuFbOperate(NEW_PETFB_REQ_TYPE.NEW_PETFB_REQ_TYPE_BUY_TIMES)
		--self:Close()
	else
		FuBenPanelWGCtrl.Instance:SendChongWuFbOperate(NEW_PETFB_REQ_TYPE.NEW_PETFB_REQ_TYPE_BUY_TIMES)
	end
end

function PetBuyView:HighTeamEquip()
	--print_error("远古仙殿")
	FuBenWGCtrl.Instance:SendYuanGuFBReq(FB_HIGH_REQ_TYPE.FB_HIGH_REQ_TYPE_BUY_TIMES)
end