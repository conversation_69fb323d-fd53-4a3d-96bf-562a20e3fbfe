LingHeLibraryView = LingHeLibraryView or BaseClass(SafeBaseView)

function LingHeLibraryView:__init()
	self.view_layer = UiLayer.Pop
    self:SetMaskBg(true, true)

    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(750,550)})
    self:AddViewResource(0, "uis/view/tianshen_linghe_ui_prefab", "layout_linghe_library")
end

function LingHeLibraryView:__delete()

end

function LingHeLibraryView:ReleaseCallBack()
	if self.library_grid_list then
        self.library_grid_list:DeleteMe()
        self.library_grid_list = nil
    end
end


function LingHeLibraryView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.TianShenLingHe.LibraryTitle
	local bundle = "uis/view/tianshen_linghe_ui_prefab"
	local asset = "linghe_library_cell"
	if self.library_grid_list == nil then
		self.library_grid_list = AsyncBaseGrid.New()
		self.library_grid_list:CreateCells({col = 5, change_cells_num = 1, list_view = self.node_list["posy_library_list"],
				assetBundle = bundle, assetName = asset, itemRender = LingHeLibraryItemRender})
		self.library_grid_list:SetStartZeroIndex(false)
	end
end

function LingHeLibraryView:OnFlush(param_t)
	local list_data = TianShenLingHeWGData.Instance:ShowLingHeShopItem()
	self.library_grid_list:SetDataList(list_data)
end

--LingHeLibraryItemRender-----
LingHeLibraryItemRender = LingHeLibraryItemRender or BaseClass(BaseRender)

function LingHeLibraryItemRender:LoadCallBack()
	self.linghe_show_cell = BaseLingHeCell.New(nil, self.node_list.item_pos)
end

function LingHeLibraryItemRender:__delete()
	if self.linghe_show_cell then
		self.linghe_show_cell:DeleteMe()
        self.linghe_show_cell = nil
    end
end

function LingHeLibraryItemRender:OnFlush()
	if not self.data then
		return
	end
	self.linghe_show_cell:SetData({item_id = self.data.runes_id})
	local color = ItemWGData.Instance:GetItemColor(self.data.runes_id)
    local item_name = ItemWGData.Instance:GetItemName(self.data.runes_id)
    self.node_list.name.text.text = ToColorStr(item_name, color) 
end