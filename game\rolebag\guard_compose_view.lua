GuardComposeView = GuardComposeView or BaseClass(SafeBaseView)

local STUFF_NUM = 2

local key_str = {
	normal = "normal",
    special = "special",
}

function GuardComposeView:__init()
	self.view_style = ViewStyle.Half
	self:SetMaskBg()

	self:AddViewResource(0, "uis/view/gurad_invalid_time_prefab", "layout_guard_conpose_result_panel")
	self:AddViewResource(0, "uis/view/gurad_invalid_time_prefab", "layout_guard_conpose")
end

function GuardComposeView:__delete()

end

function GuardComposeView:ShowIndexCallBack()
	local bundle, asset = ResPath.GetGuardComposeImg("a3_ui_shusj_shsj")
    XUI.SetNodeImage(self.node_list.top_title_bg, bundle, asset)
end

function GuardComposeView:LoadCallBack()
	if not self.change_callback then
		self.change_callback = BindTool.Bind(self.PlayerDataChangeCallback, self)
		RoleWGData.Instance:NotifyAttrChange(self.change_callback, {"gold"})
    end

    if nil == self.item_data_change_callback then
		self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
	end

	XUI.AddClickEventListener(self.node_list.open_compose_btn, BindTool.Bind(self.OnClickOpenComposeBtn,self))

	self.stuff_item_list = {}
	for i = 1, STUFF_NUM do
		self.stuff_item_list[i] = ItemCell.New(self.node_list["stuff_node_" .. i])
	end

	if not self.model_display_list then
		self.model_display_list = {}

		for i = 1, 2 do
			self.model_display_list[i] = OperationActRender.New(self.node_list["model_node_" .. i])
		end
	end

	if not self.cur_attr_item_list then
		local function init_attr_func(node, attr_item_list)
			for i = 1, 6 do
				local attr_list = node:FindObj(string.format("attr_%d", i))
				if attr_list then
					local cell = CommonAddAttrRender.New(attr_list)
					cell:SetIndex(i)
					cell:SetAttrNameNeedSpace(true)
					attr_item_list[i] = cell
				end
			end
		end

		self.cur_attr_item_list = {}
		self.next_attr_item_list = {}

		--当前属性:1, 下级属性:2.
		for j = 1, 2 do
			local attr_item_list = nil
			if j == 1 then
				attr_item_list = self.cur_attr_item_list
			else
				attr_item_list = self.next_attr_item_list
			end

			attr_item_list[key_str.normal] = {}
			attr_item_list[key_str.special] = {}
			init_attr_func(self.node_list["normal_attr_" .. j], attr_item_list[key_str.normal])
			init_attr_func(self.node_list["special_attr_" .. j], attr_item_list[key_str.special])
		end
    end

    self.scene_change_complete = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_ENTER, BindTool.Bind1(self.OnSceneChangeComplete, self))
end

function GuardComposeView:ReleaseCallBack()
	if self.change_callback then
		if nil ~= RoleWGData.Instance then
			RoleWGData.Instance:UnNotifyAttrChange(self.change_callback)
		end
		self.change_callback = nil
	end

    if self.item_data_change_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
		self.item_data_change_callback = nil
	end

	if self.scene_change_complete then
		GlobalEventSystem:UnBind(self.scene_change_complete)
		self.scene_change_complete = nil
	end

	if self.stuff_item_list then
		for k, v in pairs(self.stuff_item_list) do
			v:SetGraphicGreyCualityBg(false)
			v:DeleteMe()
		end
		self.stuff_item_list = nil
    end

	if self.model_display_list then
		for k, v in pairs(self.model_display_list) do
			v:DeleteMe()
			v = nil
		end
		self.model_display_list = nil
	end

    self.stuff_id1 = nil
    self.stuff_id2 = nil

	if self.cur_attr_item_list then
		for k, v in pairs(self.cur_attr_item_list) do
			for _, attr_cell in pairs(v) do
				attr_cell:DeleteMe()
				attr_cell = nil
			end
		end
		self.cur_attr_item_list = nil
	end

	if self.next_attr_item_list then
		for k, v in pairs(self.next_attr_item_list) do
			for _, attr_cell in pairs(v) do
				attr_cell:DeleteMe()
				attr_cell = nil
			end
		end
		self.next_attr_item_list = nil
	end
end

function GuardComposeView:CloseCallBack()
    self.stuff_id1 = nil
    self.stuff_id2 = nil
end

function GuardComposeView:OnSceneChangeComplete(old_scene_type,new_scene_type)
	if self:IsOpen() then
		self:Close()
	end
end

function GuardComposeView:SetData(product_id)
	self.product_id = product_id
	self.cur_guard_id = nil
	local xiaogui_compose_show_cfg = EquipmentWGData.Instance:GetGuardComposeShowCfg()
	for xiaogui_type, xiaogui_list in pairs(xiaogui_compose_show_cfg) do
		for k, xiaogui_info in pairs(xiaogui_list) do
			if xiaogui_info.product_id == self.product_id then
				self.cur_guard_id = xiaogui_info.stuff_id_1
				break
			end
		end
	end
end

-- 物品变化
function GuardComposeView:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_item_id == self.stuff_id1 or change_item_id == self.stuff_id2 then
		self:Flush()
	end
end

function GuardComposeView:PlayerDataChangeCallback(attr_name, value)
	if attr_name == 'gold' then
		self:Flush()
	end
end

function GuardComposeView:OnFlush()
	self:FlushXiaoGuiStuff()
	self:FlushXiaoGuiAttrView()
	self:FlushXiaoGuiModel()
end

function GuardComposeView:FlushXiaoGuiStuff()
	--所有的小鬼合成信息.
	local compose_cfg = ComposeWGData.Instance:GetXiaoGuiComposeCfg()
	if not compose_cfg then
		return
	end

	local scene_type = Scene.Instance:GetSceneType()
	local scene_cfg = FuBenWGData.GetFbSceneConfig(scene_type)
	local cfg_type = scene_cfg.scene_guard_type
	--根据类型获取对应的数据.
	local compose_type_info = compose_cfg[cfg_type]

	local cfg = nil

	for k, v in pairs(compose_type_info) do
		if v.product_id == self.product_id then
			cfg = v
			break
		end
	end

	if not cfg then
		return
	end

	local total_price = 0
	local show_idx = 0

	for i = 1, STUFF_NUM do
		local seq = i + 1
		local stuff_id = cfg["stuff_id_" .. seq]
		local stuff_num = cfg["stuff_count_" .. seq]
		self.stuff_id1 = i == 1 and stuff_id
        self.stuff_id2 = i == 2 and stuff_id
		if stuff_id and stuff_num and stuff_id > 0 and stuff_num > 0 then
			show_idx = show_idx + 1

			local item_cfg = ItemWGData.Instance:GetItemConfig(stuff_id)
			if item_cfg then
				self.node_list["stuff_name_" .. i].text.text = item_cfg.name
			end
			local have_num = ItemWGData.Instance:GetItemNumInBagById(stuff_id)
			local is_enough = have_num >= stuff_num

			self.stuff_item_list[show_idx]:SetData({item_id = stuff_id})
			self.stuff_item_list[show_idx]:SetGraphicGreyCualityBg(not is_enough)
			self.stuff_item_list[show_idx]:SetEffectRootEnable(is_enough)
		end

		self.node_list["stuff_" .. i]:SetActive(show_idx >= i)
	end
end

function GuardComposeView:FlushXiaoGuiModel()
	for i = 1, 2 do
		local show_item_id = i == 1 and self.cur_guard_id or self.product_id

		local display_data = {}
		display_data.should_ani = true
		display_data.item_id = show_item_id

		display_data.render_type = OARenderType.RoleModel
		display_data.model_rt_type = ModelRTSCaleType.M

		local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(show_item_id)

		if xiaogui_cfg.get_position and xiaogui_cfg.get_position ~= "" then
			local pos_list = string.split(xiaogui_cfg.get_position, "|")
			local pos_x = tonumber(pos_list[1]) or 0
			local pos_y = tonumber(pos_list[2]) or 0
			local pos_z = tonumber(pos_list[3]) or 0
	
			display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
		end
	
		if xiaogui_cfg.set_rotation and xiaogui_cfg.set_rotation ~= "" then
			local rot_list = string.split(xiaogui_cfg.set_rotation, "|")
			local rot_x = tonumber(rot_list[1]) or 0
			local rot_y = tonumber(rot_list[2]) or 0
			local rot_z = tonumber(rot_list[3]) or 0
	
			display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
		end
	
		display_data.model_adjust_root_local_scale = xiaogui_cfg.set_scale

		self.model_display_list[i]:SetData(display_data)

		-- 战力
		local capability, show_max_cap, _ = 0, false, nil
		if show_item_id then
			if ItemWGData.GetIsXiaogGui(show_item_id) then
				_, capability = ItemShowWGData.Instance:GetShouHuAttrByData(show_item_id)
			elseif HiddenWeaponWGData.Instance:GetOnlyEquipCfgById(show_item_id) then
				capability = HiddenWeaponWGData.Instance:GetItemCapability(show_item_id)
			else
				local item_cfg = ItemWGData.Instance:GetItemConfig(show_item_id)
				if item_cfg then
					local need_get_sys, sys_type = ItemShowWGData.Instance:GetIsNeedSysAttr(show_item_id, item_cfg.sys_attr_cap_location)
					if sys_type == ITEMTIPS_SYSTEM.MOUNT or sys_type == ITEMTIPS_SYSTEM.LING_CHONG
					or sys_type == ITEMTIPS_SYSTEM.HUA_KUN or sys_type == ITEMTIPS_SYSTEM.TS_HUANHUA
					or sys_type == ITEMTIPS_SYSTEM.XIAN_WA then
						show_max_cap = false
						capability = ItemShowWGData.CalculateCapability(show_item_id, show_max_cap) or 0
					else
						capability = ItemShowWGData.CalculateCapability(show_item_id) or 0
					end
				end
			end
		end

		self.node_list["cap_value_" .. i].text.text = capability
	end
end

--刷新属性.
function GuardComposeView:FlushXiaoGuiAttrView()
	for i = 1, 2 do
		local show_item_id = i == 1 and self.cur_guard_id or self.product_id
		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(show_item_id)
		if item_cfg == nil then
			return
		end

		local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(show_item_id)
		if xiaogui_cfg == nil then
			return
		end
	
		-- attr_list = {{attr_name = "", attr_value = "", add_str = ""}}
		local attr_list = {}
		local specail_list = {}
		local pingfen = 0
		for key,value in pairs(xiaogui_cfg) do
			local temp = {}
			temp.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(key)
			if temp.attr_name and temp.attr_name ~= "" and temp.attr_name ~= "：" then
				if value > 0 then
					if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(key) then
						temp.attr_value = string.format("%d%%", value / 100)
						specail_list[#specail_list + 1] = temp
					else
						temp.attr_value = value
						attr_list[#attr_list + 1] = temp
						local count = TipWGData.Instance:GetCommonPingFenCfgByIndex(key)
						if count > 0 then
							pingfen = pingfen + count * value
						end
					end
				end
			end
		end

		local attr_item_list = nil
		if i == 1 then
			attr_item_list = self.cur_attr_item_list
		else
			attr_item_list = self.next_attr_item_list
		end

		for i = 1, 6 do
			local nor_attr_item = attr_item_list[key_str.normal]
			if attr_list[i] then
				nor_attr_item[i].view:SetActive(true)
				nor_attr_item[i]:SetData(attr_list[i])
			else
				nor_attr_item[i].view:SetActive(false)
			end

			local sp_attr_item = attr_item_list[key_str.special]
			if specail_list[i] then
				sp_attr_item[i].view:SetActive(true)
				sp_attr_item[i]:SetData(specail_list[i])
			else
				sp_attr_item[i].view:SetActive(false)
			end
		end
	end
end

function GuardComposeView:OnClickOpenComposeBtn()
	--所有的小鬼合成信息.
	local compose_cfg = ComposeWGData.Instance:GetXiaoGuiComposeCfg()
	if not compose_cfg then
		return
	end

	local scene_type = Scene.Instance:GetSceneType()
	local scene_cfg = FuBenWGData.GetFbSceneConfig(scene_type)
	local cfg_type = scene_cfg.scene_guard_type
	--根据类型获取对应的数据.
	local compose_type_info = compose_cfg[cfg_type]

	local cfg = nil

	for k, v in pairs(compose_type_info) do
		if v.product_id == self.product_id then
			cfg = v
			break
		end
	end

	if not cfg then
		return
	end

	ViewManager.Instance:Open(GuideModuleName.Compose, TabIndex.other_compose_xiaogui, nil, {open_param = cfg.sub_type, sub_view_name = cfg.child_type})
	self:Close()
end