local BOSS_REBIRTH_REMIND_INTERVAL = 30  -- s

KfBossSceneLogic = KfBossSceneLogic or BaseClass(CrossServerSceneLogic)
KfBossSceneLogic.TREASURECRYTAL = {125, 126, 127}
function KfBossSceneLogic:__init()
	self.last_select_monster = nil
	self.select_monster_event = nil
end

function KfBossSceneLogic:__delete()
	self.has_get_team_target = nil
	self.select_monster_event = nil

	if self.select_monster_event then
		GlobalEventSystem:UnBind(self.select_monster_event)
		self.select_monster_event = nil
	end
end

-- 进入场景
function KfBossSceneLogic:Enter(old_scene_type, new_scene_type)
	self.has_get_team_target = -1
    CrossServerSceneLogic.Enter(self, old_scene_type, new_scene_type)
    -- MainuiWGCtrl.Instance:SetTaskButtonTrue()
    MainuiWGCtrl.Instance:SetOtherContents(false)
    MainuiWGCtrl.Instance:SetTaskContents(false)
	BossWGCtrl.Instance:Close()
	GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE, true)
    MainuiWGCtrl.Instance:ShowMainuiMenu(false)
    MainuiWGCtrl.Instance:SetFbIconEndCountDown(-1)
	self:InitGoToPos()
	-- XuiBaseView.CloseAllView()
	--MainuiWGCtrl.Instance.attack_mode_view.node_list.["SwitchServerMode"]:SetActive(true)
    BossWGCtrl.Instance:EnterSceneCallback()
    MainuiWGCtrl.Instance:ResetLightBoss()
	BossWGCtrl.Instance:SetRoleTiredIcon(0, TabIndex.worserv_boss_mh)
	local main_role = Scene.Instance:GetMainRole()
	-- self.before_act_mode = main_role:GetVo().attack_mode
	-- MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.GUILD)
	-- local select_obj_list = Scene.Instance:GetRoleList()
	-- local obj_select = nil
	-- for _,v in pairs(select_obj_list) do
	-- 	obj_select = v
	-- 	break
	-- end
	-- if obj_select then
	-- 	GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj_select, SceneTargetSelectType.SELECT)
	-- end
	--BossWGCtrl.Instance:OpenKFBossGatherInfoView()
    BaseFbLogic.SetLeaveFbTip(true)
    

    FuhuoWGCtrl.Instance:SetFuhuoMustCallback(BindTool.Bind(self.FuHuoCallBack, self))
    self.select_monster_event = GlobalEventSystem:Bind(ObjectEventType.BE_SELECT, BindTool.Bind(self.OnSelectObjCallBack, self))
	self.main_role_move_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_POS_CHANGE, BindTool.Bind(self.OnMainRoleMove, self))
	self.refresh_boss_list = GlobalEventSystem:Bind(OtherEventType.BOSS_CHANGE, BindTool.Bind(self.CheckUseBossReflushCard, self))

    --local view = BossWGCtrl.Instance:GetKFBossGatherInfoView()
	--ViewManager.Instance:AddMainUIFuPingChangeList(view)

	local xiezhu_view = BossXiezhuWGCtrl.Instance:GetKFBossXieZhuInfoView()
	ViewManager.Instance:AddMainUIFuPingChangeList(xiezhu_view)

	BossPrivilegeWGCtrl.Instance:EnterBossPrivilegeSceneCallBack()

	self.boss_rebirth_boss_cfg = {}
	self.last_remind_time = 0
end

function KfBossSceneLogic:OnSelectObjCallBack(obj)
	if obj ~= nil and not obj:IsDeleted() and obj:IsBoss() then
		self.last_select_monster = obj:GetMonsterId()
	end
end

function KfBossSceneLogic:FuHuoCallBack(use_type)
	local tarck_type, track_role_uuid = self:GetTrackRoleInfo()
	if tarck_type == OBJ_FOLLOW_TYPE.TEAM then
		return
	end
	
	use_type = use_type or FuHuoType.Common
	if use_type == FuHuoType.Common then
		self:CommonMoveCallBack()
	else
		self:HereFuHuoCallBack()
	end
end

function KfBossSceneLogic:CommonMoveCallBack()
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)

	if self.last_select_monster ~= nil and self.last_select_monster ~= 0 then
		local scene_id = Scene.Instance:GetSceneId()
		local cfg = BossWGData.Instance:GetBossTypeBySceneId(scene_id)
		if cfg ~= nil then
			if self.last_select_monster == GameEnum.TeamInvite then
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
				return
			end

			local list_data = BossWGData.Instance:GetCrossLayerBossBylayer(cfg.boss_view_layer)
			local data = {}
			if list_data then
				for k,v in pairs(list_data) do
					if v.boss_id == self.last_select_monster then
						data = v
						break
					end
				end

				if IsEmptyTable(data) then return end

				GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
					GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
				end)

				MoveCache.SetEndType(MoveEndType.FightByMonsterId)
				GuajiCache.monster_id = self.last_select_monster
				MoveCache.param1 = self.last_select_monster
				local range = BossWGData.Instance:GetMonsterRangeByid(self.last_select_monster)
				GuajiWGCtrl.Instance:MoveToPos(scene_id, data.x_pos, data.y_pos, range)
			else
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end			
		else
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end
	else
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end
end

function KfBossSceneLogic:HereFuHuoCallBack()
	local select_obj = nil
	if GuajiCache.target_obj then
		select_obj = GuajiCache.target_obj
	end

	if select_obj and not select_obj:IsDeleted() and Scene.Instance:IsEnemy(select_obj) then
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
		GuajiCache.target_obj = select_obj
		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, select_obj, SceneTargetSelectType.SELECT)
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	else
		self:CommonMoveCallBack()
	end

end

-- 获取挂机打怪的位置
function KfBossSceneLogic:GetGuiJiMonsterPos()
	CommonFbLogic.GetGuiJiMonsterPos(self)
	local target_distance = 20 * 20
	local target_x = 0
    local target_y = 0
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()

	local obj_move_info_list = Scene.Instance:GetObjMoveInfoList()

	for k, v in pairs(obj_move_info_list) do
		local vo = v:GetVo()
		if vo.obj_type == SceneObjType.Monster and BaseSceneLogic.IsAttackMonster(vo.type_special_id, vo) then
			local distance = GameMath.GetDistance(x, y, vo.pos_x, vo.pos_y, false)
			if distance < target_distance then
				target_x = vo.pos_x
                target_y = vo.pos_y
				target_distance = distance
			end
		end
	end

	return target_x, target_y
end

function KfBossSceneLogic:InitGoToPos()
	local sence_id = Scene.Instance:GetSceneId()
	local type1, layer, boss_id = BossWGData.Instance:GetCurSelectBossID()

	if boss_id == GameEnum.TeamInvite then
		return
	end

	local list_data = BossWGData.Instance:GetCrossLayerBossBylayer(layer)
	local data = {}
	if list_data then
		for k,v in pairs(list_data) do
			if v.boss_id == boss_id then
				data = v
				break
			end
		end

		if IsEmptyTable(data) then return end
		GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end)

		MoveCache.SetEndType(MoveEndType.FightByMonsterId)
		GuajiCache.monster_id = boss_id
		MoveCache.param1 = boss_id
		local range = BossWGData.Instance:GetMonsterRangeByid(boss_id)
		GuajiWGCtrl.Instance:MoveToPos(sence_id, data.x_pos, data.y_pos, range)
	end
end

-- function KfBossSceneLogic:AtkTeamLeaderTarget()
-- 	local team_leader_info = SocietyWGData.Instance:GetTeamLeader() or {}
-- 	local leader = Scene.Instance:GetRoleByRoleId(team_leader_info.role_id)
-- 	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)

-- 	if not leader then
-- 		return
-- 	end

-- 	self.has_get_team_target = leader:GetVo() and leader:GetVo().obj_id or -1

-- 	if not leader:IsAtkPlaying() then
-- 		return
-- 	end

-- 	local target_obj
-- 	if leader:IsAtkPlaying() then
-- 		target_obj = leader:GetAttackTarget()
-- 		if not target_obj then
-- 			return
-- 		end
-- 	else
-- 		self.has_get_team_target = -1
-- 		return
-- 	end

-- 	if self:IsEnemy(target_obj) then
-- 		self.has_get_team_target = -1
-- 		GuajiWGCtrl.Instance:StopGuaji()
-- 		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, target_obj, SceneTargetSelectType.SELECT)
-- 	end
-- 	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
-- end

function KfBossSceneLogic:GetTeamTargetFlag()
	return self.has_get_team_target or -1
end

function KfBossSceneLogic:OnLoadingComplete()
	-- print_error("OnLoadingComplete")
	 MainuiWGCtrl.Instance:ShowMainuiMenu(false)
end

function KfBossSceneLogic:Out(old_scene_type, new_scene_type)
	self.last_select_monster = nil
	self.has_get_team_target = -1
	CommonFbLogic.Out(self)
	BossWGCtrl.Instance:OutSceneCallback()
	--BossWGCtrl.Instance:SendWorldBossReq(BossView.ReqType.ALLINFO)
    -- UiInstanceMgr.Instance:CloseRewardAction()
    MainuiWGCtrl.Instance:SetOtherContents(false)
    MainuiWGCtrl.Instance:SetTaskContents(true)
    GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,false)
    MainuiWGCtrl.Instance:ShowMainuiMenu(true)
    
	
	if self.main_role_move_event then
		GlobalEventSystem:UnBind(self.main_role_move_event)
		self.main_role_move_event = nil
   end

   if self.refresh_boss_list then
		GlobalEventSystem:UnBind(self.refresh_boss_list)
		self.refresh_boss_list = nil
   end

	-- if self.before_act_mode ~= nil then
	-- 	MainuiWGCtrl.Instance:SendSetAttackMode(self.before_act_mode)
	-- end
--	MainuiWGCtrl.Instance.attack_mode_view.node_list.SwitchServerMode:SetActive(true)

	if self.create_obj_event then
		GlobalEventSystem:UnBind(self.create_obj_event)
		self.create_obj_event = nil
	end
	--BossWGCtrl.Instance:CloseKFBossGatherInfoView()
	BossWGCtrl.Instance:OpenBossViewByScene(old_scene_type, new_scene_type)
	FuhuoWGCtrl.Instance:ClearFuhuoMustCallback()
	-- local view = BossWGCtrl.Instance:GetKFBossGatherInfoView()
	local view_manager = ViewManager.Instance
	-- view_manager:RemoveMainUIFuPingChangeList(view)
	local xiezhu_view = BossXiezhuWGCtrl.Instance:GetKFBossXieZhuInfoView()
	view_manager:RemoveMainUIFuPingChangeList(xiezhu_view)

	BossPrivilegeWGCtrl.Instance:OutBossPrivilegeSceneCallBack()

	self.boss_rebirth_boss_cfg = nil
	self.last_remind_time = nil

	if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end
end

function KfBossSceneLogic:GuaiJiMonsterUpdate(now_time, elapse_time)
	self.guai_ji_next_move_time = Status.NowTime - 3

	if self:RolePickUpFallItem() then
		return true
	end

	local target_obj = MainuiWGData.Instance:GetTargetObj()
	if target_obj == nil then
		local main_role = Scene.Instance:GetMainRole()
		local x, y = main_role:GetLogicPos()
		local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
		target_obj = Scene.Instance:SelectObjHelper(Scene.Instance:GetRoleList(), x, y, distance_limit, SelectType.Enemy)
		if target_obj ~= nil and self:IsRoleEnemy(target_obj, main_role) then
			MainuiWGCtrl.Instance:SetTargetObj(target_obj)
		end
	end

	target_obj = MainuiWGData.Instance:GetTargetObj()
	if target_obj == nil or (target_obj ~= nil and target_obj:GetType() == SceneObjType.GatherObj) then
		for k, v in pairs(KfBossSceneLogic.TREASURECRYTAL) do
			local gather_obj = Scene.Instance:SelectMinDisGather(v)
			if nil ~= gather_obj then
				local left_treasure_crystal_gather_times, left_ordinary_crystal_gather_times = BossWGData.Instance:GetTreasureGatherTimes()
				if (125 == v and left_ordinary_crystal_gather_times > 0) or (v ~= 125 and left_treasure_crystal_gather_times > 0) then
					MainuiWGCtrl.Instance:SetTargetObj(gather_obj)
					self:MoveToObj(gather_obj)
				end
			end
		end
	end

	local mosnter_type = nil
	if target_obj ~= nil and target_obj:GetType() == SceneObjType.Monster then
		mosnter_type = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[target_obj:GetVo().monster_id].type
	end

	local left_can_kill_boss_num, max_tire_value = BossWGData.Instance:GetCrossBossTire()
	local search_boss = true
	if left_can_kill_boss_num >= max_tire_value and (target_obj ~= nil and mosnter_type == MONSTER_TYPE.BOSS) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.OutTire)
		Scene.Instance:ClearAllOperate()
		MainuiWGCtrl.Instance:SetTargetObj(nil)
		search_boss = false
	end

	target_obj = MainuiWGData.Instance:GetTargetObj()
	if (left_can_kill_boss_num < max_tire_value) or (target_obj ~= nil and target_obj:GetType() ~= SceneObjType.GatherObj) or (target_obj == nil and search_boss) then
		BaseSceneLogic.GuaiJiMonsterUpdate(self, now_time, elapse_time)
		return
	end
end

-- 是否是挂机打怪的敌人
function KfBossSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:IsRealDead() or not Scene.Instance:IsEnemy(target_obj) then
		return false
	end
	return true
end

-- 获取挂机打怪的敌人(优先级： 优先打角色，如果点击前往击杀则优先打BOSS)
function KfBossSceneLogic:GetGuajiCharacter()
	local target_obj
	local is_need_stop = false

	if target_obj == nil then
		return self:GetMonster(), nil, is_need_stop
	end

	target_obj, is_need_stop = self:GetNormalRole()
	if target_obj ~= nil then
		return target_obj, nil, is_need_stop
	end
end

function KfBossSceneLogic:GetNormalRole()
	local role_list = Scene.Instance:GetRoleList()
	local info = self:GetGuaJiInfo()
	local is_stop = info ~= nil

	for k,v in pairs(role_list) do
		if self:IsEnemy(v) then
			if info ~= nil then
				if not v:IsDeleted() then
					local pos_x, pos_y = v:GetLogicPos()
					local dis = GameMath.GetDistance(info.x, info.y, pos_x, pos_y, false)
					if dis <= info.aoi_range * info.aoi_range then
						GuajiCache.target_obj = v
						return v, is_stop
					end
				end
			else
				GuajiCache.target_obj = v
				return v, is_stop
			end
		end
	end

	return nil, is_stop
end

function KfBossSceneLogic:IsMonsterEnemy( target_obj, main_role )
	local tire_value, max_tire_value = BossWGData.Instance:GetCrossBossTire()
	if tire_value == nil then
		return
	end
	if tire_value >= max_tire_value and target_obj:IsBoss() then
		if BossXiezhuWGData.Instance:IsGotoXiezhu() and BossXiezhuWGData.Instance:IsXiezhuCurBoss(target_obj) then
			return true
		else
			return false, Language.Boss.OutTire
		end
	end
	return true
end

function KfBossSceneLogic:GetMonster()
	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local main_role = Scene.Instance:GetMainRole()
	local obj = nil
	if main_role ~= nil then
		local x, y = main_role:GetLogicPos()

		local info = self:GetGuaJiInfo()
		if info ~= nil then
			distance_limit = info.aoi_range * info.aoi_range
			x = info.x
			y = info.y
		end

		obj = Scene.Instance:SelectObjHelper(SceneObjType.Monster, x, y, distance_limit, SelectType.Enemy)
	end

	return obj
end

function KfBossSceneLogic:GetFbSceneMonsterListCfg(monsters_list_cfg)
	local monsters_list = MapWGData.Instance:GetSceneMonsterSort(monsters_list_cfg)
	monsters_list = BossWGData.Instance:GetCurSceneAllMonster(nil,BossWGData.MonsterType.Monster)
	return BossWGData.Instance:GetCurSceneAllMonster(monsters_list), true
end

function KfBossSceneLogic:GetFbSceneMonsterBossCfg()
	return BossWGData.Instance:GetCurSceneAllBoss()
end

function KfBossSceneLogic:GetFbSceneMonsterCfg()
	local list = BossWGData.Instance:GetCurSceneAllMonster(nil, BossWGData.MonsterType.Monster)
	return list,#list
end

function KfBossSceneLogic:Update(now_time, elapse_time)
	CrossServerSceneLogic.Update(self, now_time, elapse_time)
	self:CheckGuaJiPosMove()
end

function KfBossSceneLogic:GetIsCanGather()
    local empty_num = ItemWGData.Instance:GetEmptyNum()
	if empty_num < 1 then
		BossWGCtrl.Instance:FullBagGatherHandle()
		return false
	end
	return true
end

function KfBossSceneLogic:OnMainRoleMove()
	-- self:CheckUseBossReflushCard()
end

function KfBossSceneLogic:CheckUseBossReflushCard()
	-- 是否今日不再提醒
	-- if self:TodayIgnoreBossReflushRemind() then
	-- 	return
	-- end

	local main_role = Scene.Instance:GetMainRole()
	if not main_role then
		return
	end

	local tire_value, max_tire_value = BossWGData.Instance:GetCrossBossTire()
	if tire_value == nil then
		return
	end

	if tire_value >= max_tire_value then
		return
	end

	local boss_list = self:GetFbSceneMonsterBossCfg()
	if IsEmptyTable(boss_list) then
		return
	end

	local pos_x, pos_y = main_role:GetLogicPos()
	local target_distance = 1000 * 1000
	local boss_rebirth_cfg = MainuiWGData.Instance:GetBossRebirthCfg()
	local rebirth_range = boss_rebirth_cfg.rebirth_range
	local rebirth_dis = rebirth_range * rebirth_range
	local boss_cfg

	for k, v in pairs(boss_list) do
		local dis = GameMath.GetDistance(v.x, v.y, pos_x, pos_y, false)
		if dis < rebirth_dis and dis < target_distance then
			boss_cfg = v
			target_distance = dis
		end
	end

	if boss_cfg then
		local boss_state = BossWGData.Instance:GetBossStatusByBossId(boss_cfg.boss_id)
		if boss_state == 0 then
			local server_time = TimeWGCtrl.Instance:GetServerTime()

			local need_remind = false
			if IsEmptyTable(self.boss_rebirth_boss_cfg) then
				need_remind = true
			else
				local cache_boss_cfg = self.boss_rebirth_boss_cfg
				local is_same_boss_cfg = cache_boss_cfg.boss_id == boss_cfg.boss_id and cache_boss_cfg.x == boss_cfg.x and cache_boss_cfg.y == boss_cfg.y
				if not is_same_boss_cfg or (is_same_boss_cfg and (self.last_remind_time + BOSS_REBIRTH_REMIND_INTERVAL) < server_time) then
					need_remind = true
				end
			end

			if need_remind then
				self.boss_rebirth_boss_cfg = boss_cfg
				local rebirth_item, has_num = MainuiWGData.Instance:GetBossRefreshItemIdStuff()
				if rebirth_item then
					if self:TodayIgnoreBossReflushRemind() then
						BossWGCtrl.Instance:DoOperaBossRefresh(rebirth_item)
					else
						if not self:TodayNoTips() and BossWGData.Instance:IsBossQuickRebirthNotCountDown() then
							BossWGCtrl.Instance:OpenBossQuickRebirthShow()
						end
					end
				end
			end
		else
			self.boss_rebirth_boss_cfg = {}
		end
	end
end

function KfBossSceneLogic:TodayIgnoreBossReflushRemind()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local rebirth_reflush_status = PlayerPrefsUtil.GetInt("boss_quick_rebirth_reflush" .. main_role_id)
	return rebirth_reflush_status == 1
end

function KfBossSceneLogic:TodayNoTips()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local has_today_flag = PlayerPrefsUtil.GetInt("boss_quick_rebirth_tips" .. main_role_id) == open_day
	return has_today_flag
end