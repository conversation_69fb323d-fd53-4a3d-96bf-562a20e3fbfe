﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class GameLayersWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>("GameLayers");
		<PERSON><PERSON>("Default", get_Default, null);
		<PERSON><PERSON>("Walkable", get_Walkable, null);
		<PERSON><PERSON>("Terrain", get_Terrain, null);
		<PERSON><PERSON>("DisableCastShadow", get_DisableCastShadow, null);
		<PERSON><PERSON>("Clickable", get_Clickable, null);
		<PERSON><PERSON>("AreaAtmosphere", get_AreaAtmosphere, null);
		<PERSON><PERSON>("Water", get_Water, null);
		<PERSON><PERSON>("WaterSurface", get_WaterSurface, null);
		<PERSON><PERSON>("BigBuilding", get_BigBuilding, null);
		<PERSON><PERSON>("SmallBuilding", get_SmallBuilding, null);
		<PERSON><PERSON>("UI3D", get_UI3D, null);
		<PERSON><PERSON>("UI3DEffect", get_UI3DEffect, null);
		<PERSON><PERSON>("UIEffect", get_UIEffect, null);
		<PERSON><PERSON>("UI3DTerrain", get_UI3DTerrain, null);
		L.RegVar("UIScene", get_UIScene, null);
		L.RegVar("LODMask", get_LODMask, null);
		L.RegVar("NotImportantCulling", get_NotImportantCulling, null);
		L.RegVar("NotImportantCulling2", get_NotImportantCulling2, null);
		L.RegVar("ImportantCulling2", get_ImportantCulling2, null);
		L.RegVar("ImportantCulling3", get_ImportantCulling3, null);
		L.RegVar("ReceiveShadow", get_ReceiveShadow, null);
		L.RegVar("CastShadow", get_CastShadow, null);
		L.RegVar("Invisible", get_Invisible, null);
		L.RegVar("UI", get_UI, null);
		L.RegVar("UICG", get_UICG, null);
		L.EndStaticLibs();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Default(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.Default);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Walkable(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.Walkable);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Terrain(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.Terrain);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_DisableCastShadow(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.DisableCastShadow);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Clickable(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.Clickable);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AreaAtmosphere(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.AreaAtmosphere);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Water(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.Water);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_WaterSurface(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.WaterSurface);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_BigBuilding(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.BigBuilding);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_SmallBuilding(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.SmallBuilding);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UI3D(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.UI3D);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UI3DEffect(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.UI3DEffect);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UIEffect(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.UIEffect);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UI3DTerrain(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.UI3DTerrain);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UIScene(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.UIScene);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_LODMask(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.LODMask);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_NotImportantCulling(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.NotImportantCulling);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_NotImportantCulling2(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.NotImportantCulling2);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ImportantCulling2(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.ImportantCulling2);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ImportantCulling3(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.ImportantCulling3);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ReceiveShadow(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.ReceiveShadow);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CastShadow(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.CastShadow);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Invisible(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.Invisible);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UI(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.UI);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UICG(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, GameLayers.UICG);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

