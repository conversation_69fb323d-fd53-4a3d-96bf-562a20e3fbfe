require("game/crossserver/crossserver_common/crossserver_wg_data")

CrossServerWGCtrl = CrossServerWGCtrl or BaseClass(BaseWGCtrl)
function CrossServerWGCtrl:__init()
	if CrossServerWGCtrl.Instance then
		error("[CrossServerWGCtrl]:Attempt to create singleton twice!")
	end
	CrossServerWGCtrl.Instance = self

	self.data = CrossServerWGData.New()
	self:RegisterAllProtocals()
end

function CrossServerWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	CrossServerWGCtrl.Instance = nil
end

function CrossServerWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(SCCrossHonorChange, "OnCrossHonorChange")
	self:RegisterProtocol(SCReturnOriginalServer, "OnReturnOriginalServer")
	self:RegisterProtocol(SCInviteStartCross, "OnSCInviteStartCross")
	self:RegisterProtocol(SCInviteStartCrossResult, "OnSCInviteStartCrossResult")
	self:RegisterProtocol(CSCrossStartReq)
	self:RegisterProtocol(CSInviteStartCross)	--邀请参加跨服活动
	----------------------------------
	-- 跨服活动 统一拉去数据，领取奖励等操作
	self:RegisterProtocol(CSCrossRandActivityRequest)


	-- 2024年 跨服新数据
	self:RegisterProtocol(SCCrossServerInfo, "OnSCCrossServerInfo")
end

-- 跨服荣誉值改变
function CrossServerWGCtrl:OnCrossHonorChange(protocol)
	local obj = Scene.Instance.main_role
	if obj then
		RoleWGData.Instance:SetAttr("cross_honor", protocol.honor)
	end
end

-- 通知返回原服
function CrossServerWGCtrl:OnReturnOriginalServer(protocol)
	if Scene.Instance:GetSceneType() == SceneType.Kf_OneVOne_Prepare or
	Scene.Instance:GetSceneType() == SceneType.Kf_PvP_Prepare or
	BossWGData.IsBossScene(Scene.Instance:GetSceneType()) then
		FuBenWGCtrl.Instance:SendCSLeaveFB()
	end
	CrossServerWGCtrl.ConnectBack()
end

-- 请求开始跨服
function CrossServerWGCtrl.SendCrossStartReq(cross_activity_type, param)
	if cross_activity_type ~= ACTIVITY_TYPE.KF_ONEVONE 
		and cross_activity_type ~= ACTIVITY_TYPE.KF_PVP 
		and cross_activity_type ~= ACTIVITY_TYPE.KF_DUCK_RACE 
		and cross_activity_type ~= ACTIVITY_TYPE.KF_YANGLONGSI
		and cross_activity_type ~= ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_ULTIMATE_BATTLE
		and not FuBenWGCtrl.CanEnterFuben() then
		return
	end

	CrossServerWGData.LAST_CROSS_TYPE = cross_activity_type
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossStartReq)
	send_protocol.cross_activity_type = cross_activity_type
	send_protocol.param = param or 0
	send_protocol:EncodeAndSend()
end


-- W3返回原服
function CrossServerWGCtrl.ConnectBack()
	FuBenWGCtrl.Instance:SendLeaveFB()
end

------------------------------------------------
--跨服  统一跨服随机活动拉数据，领取奖励等操作
function CrossServerWGCtrl:SendCrossRandActivityOperaReq(param_t)
	-- Log("跨服随机活动请求---->>>>", param_t.activity_type, param_t.opera_type, param_t.param_1, param_t.param_2, param_t.param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossRandActivityRequest)
	protocol.activity_type = param_t.activity_type
	protocol.opera_type = param_t.opera_type
	protocol.param_1 = param_t.param_1 or 0
	protocol.param_2 = param_t.param_2 or 0
	protocol.param_3 = param_t.param_3 or 0
	protocol:EncodeAndSend()
end

--参加跨服活动请求下发
function CrossServerWGCtrl:OnSCInviteStartCross(protocol)
	-- print_error("----参加跨服活动请求下发",protocol)
	if protocol.reason == EnumInviteStartCrossReason.Zhandui3V3Match then
		--print_error("参加跨服活动请求下发 Zhandui3V3Match", protocol)
		KF3V3WGCtrl.Instance:HandleReceiveInviteMsg(protocol)
	elseif protocol.reason == EnumInviteStartCrossReason.LoverPKMatch then
		LoverPkWGCtrl.Instance:HandleReceiveInviteMsg(protocol)
	end
end

--邀请回应结果
function CrossServerWGCtrl:OnSCInviteStartCrossResult(protocol)
	if protocol.reason == EnumInviteStartCrossReason.Zhandui3V3Match then
		KF3V3WGCtrl.Instance:HandleInviteResultMsg(protocol)
	elseif protocol.reason == EnumInviteStartCrossReason.LoverPKMatch then
		LoverPkWGCtrl.Instance:HandleInviteResultMsg(protocol)
	end
end

--邀请参加跨服活动
-- 邀请请求 param1 是 邀请的玩家 uid
-- 回应邀请 param1 是邀请者的 uid, param2是否拒绝
function CrossServerWGCtrl:SendInviteStartCross(oper_type, reason, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSInviteStartCross)
	protocol.oper_type = oper_type
	protocol.reason = reason
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end


--=========================== 2024年 跨服新数据  =====================================
function CrossServerWGCtrl:OnSCCrossServerInfo(protocol)
	self.data:SetCrossServerInfo(protocol)
	GlobalEventSystem:Fire(OtherEventType.GS_COUNT_WORLD_LEVEL_CHANGE)
end
