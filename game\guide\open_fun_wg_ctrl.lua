require("game/guide/open_fun_wg_data")
OpenFunWGCtrl = OpenFunWGCtrl or BaseClass(BaseWGCtrl)

function OpenFunWGCtrl:__init()
	if OpenFunWGCtrl.Instance then
		print_error("[OpenFunWGCtrl] Attemp to create a singleton twice !")
	end
	OpenFunWGCtrl.Instance = self
	self.data = OpenFunWGData.New()
	self:RegisterAllProtocols()
end

function OpenFunWGCtrl:__delete()
	self.data:DeleteMe()
	OpenFunWGCtrl.Instance = nil
end

function OpenFunWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCAdvanceNoticeInfo, "OnAdvanceNoticeInfo")
end

function OpenFunWGCtrl:OnAdvanceNoticeInfo(protocol)
	self.data:SetFunNoticeInfo(protocol)
	MainuiWGCtrl.Instance:FlushView(0,"trailerview")
	TipWGCtrl.Instance:FlushFunTrailerTip(0, "trailerview")
end

function OpenFunWGCtrl:SendAdvanceNoitceOperate(operate_type, param_1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSAdvanceNoitceOperate)
	protocol.operate_type = operate_type
	protocol.param_1 = param_1 or 0
	protocol:EncodeAndSend()
end