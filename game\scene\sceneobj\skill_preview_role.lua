SkillPreviewRole = SkillPreviewRole or BaseClass(Role)

SkillPreviewRole.ShowerType = {
	TianShen = 1,
	Monster = 2
}

function SkillPreviewRole:__init(vo)
	self.obj_type = SceneObjType.SkillShower
	self.draw_obj:SetObjType(self.obj_type)
    self.shield_obj_type = ShieldObjType.SkillShower
    self.draw_obj:SetCurDetailLevel(SceneObjDetailLevel.High)
    
    if SceneObjLODManager.Instance then
        SceneObjLODManager.Instance:Remove(self)
    end

    self:InitInfo()
    Runner.Instance:AddRunObj(self, 8)
end

function SkillPreviewRole:__delete()
    self.wuhun_create_callback = nil
    self:DeleteAllBeast()
    self:RemoveShuangSheng()
    self:RemoveWuHun()
    self:RemoveAransformationEffect()
	self:RemoveAransformationDelayTime()
	Runner.Instance:RemoveRunObj(self)
end

function SkillPreviewRole:InitSummonInfo()
    self.is_monster_obj = false
    self.monster_res_id = 0
    self.monster_res_type = 0
    if self.vo.monster_id and self.vo.monster_id > 0 then
        self.is_monster_obj = true
        local cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.vo.monster_id]
        if nil ~= cfg then
            self.monster_res_id = cfg.resid
            self.monster_res_type = cfg.boss_type2

            local transform = self.draw_obj:GetRoot().transform
            transform.localScale = Vector3(cfg.scale, cfg.scale, cfg.scale)
        end
    end
end

function SkillPreviewRole:InitInfo()
	self.actor_trigger = ActorTrigger.New(self.obj_type)
    self.actor_trigger:SetTargetEffectTriggerCustomScale(Vector3(0.5, 0.5, 0.5))
    self.actor_trigger:DeleteEffectHandle()
    self.actor_trigger:DeleteCameraShakeHandle()
	self.actor_ctrl = ActorWGCtrl.New(self.actor_trigger)

    if self:IsTianShenAppearance() then     -- 是否天神
        self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("Tianshen", self.special_res_id))
    elseif self:IsGundam() then             -- 是否是高达
        self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("Gundam", self.gundam_weapon_id))
    elseif self:IsRidingFightMount() then   -- 是否是战斗坐骑
        self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("zuoqi", self.vo.mount_appeid))
    elseif self:IsXiuWeiBianShen()then --怒气形象
        local power_type = self.vo.appearance_param + 1
        self:SetActorConfigPrefabData(ConfigManager.Instance:GetRolePowerAutoPrefabConfig(self.vo.sex, 1, power_type))
    elseif self.is_monster_obj then         -- 是否是怪物
        if self:IsTianShenMonsterRes() then -- 是否是天神怪物
            self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("Tianshen", self.monster_res_id))
        else
            self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("Monster", self.monster_res_id))
        end
    else
        -- 角色
        self:SetActorConfigPrefabData(ConfigManager.Instance:GetRoleAutoPrefabConfig(self.vo.sex, self:GetProf()))
    end
end

-- 修正特效大小
function SkillPreviewRole:CorrectEffectTriggerCustomScale(correct_ratio)
    self.actor_trigger:SetTargetEffectTriggerCustomScale(Vector3(0.7 * correct_ratio, 0.7 * correct_ratio, 0.7 * correct_ratio))
end

function SkillPreviewRole:GetQualityOffsetLevel()
    return 1
end

function SkillPreviewRole:InitPartQualityRules()
end

function SkillPreviewRole:InitAppearance()

end

function SkillPreviewRole:IsTianShenMonsterRes()
    return self.monster_res_type == ClientBossType.TSBoss or self.monster_res_type == ClientBossType.WeaponAniTSBoss
end

function SkillPreviewRole:UpdateAppearance(is_xiuwei_bianshen)
    self:InitSummonInfo()
    if self:IsTianShenAppearance() then         -- 天神
        self.special_res_id = self.vo.appearance_param
        self:UpdateTianShenShowerModel()
    elseif self:IsGundam() then
        -- special_res_id 索引从0开始，兼容旧逻辑
        local cfg = MechaWGData.Instance:GetPartCfgBySeq(self.vo.appearance_param_extend[MECHA_PART_TYPE.BODY])
        if cfg then
            local gundam_seq = cfg.mechan_seq + 1
            self.special_res_id = gundam_seq * 100000 + cfg.res_id
        end

        cfg = MechaWGData.Instance:GetPartCfgBySeq(self.vo.appearance_param_extend[MECHA_PART_TYPE.WEAPON])
        if cfg then
            local gundam_seq = cfg.mechan_seq + 1
            self.gundam_weapon_id = gundam_seq * 100000 + cfg.res_id + MECHA_PART_TYPE.WEAPON * 1000
        end

        self:UpdateGundamModel()
    elseif self:IsXiuWeiBianShen() then --怒气形象
        local power_type = self.vo.appearance_param + 1     --(0 人, 1 仙, 2 魔)（不区分职业）
        self.special_res_id = string.format("%s0%s", RoleWGData.GetJobModelId(self.vo.sex, 1), power_type) 
        self:UpdateXiuWeiModel(is_xiuwei_bianshen)
    elseif self.is_monster_obj then
        if self:IsTianShenMonsterRes() then
            self:UpdateTianShenShowerModel()
        else
            self:UpdateMonsterModel()
        end
    else    -- 玩家角色
        self:UpdateRoleShowerModel()
    end
end

-- 天神
function SkillPreviewRole:UpdateTianShenShowerModel()
    local res_id = self.special_res_id
    if self.is_monster_obj and self:IsTianShenMonsterRes() then
        res_id = self.monster_res_id
    end

	local bundle, name = nil, nil
    bundle, name = ResPath.GetBianShenModel(res_id)
    self:ChangeMainPartModel(bundle, name, nil, SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.TIAN_SHEN)
    self:EquipDataChangeListen()
    self:ChangeShuangSheng()
end

-- 怪物
function SkillPreviewRole:UpdateMonsterModel()
	local bundle, asset = self:GetMonsterBundleAsset()
    local sync_anim_type = self:IsTianShenMonsterRes() and SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.TIAN_SHEN or nil
    self:ChangeObjWaitSyncAnimType(sync_anim_type)
	self:ChangeModel(SceneObjPart.Main, bundle, asset)
end

function SkillPreviewRole:GetMonsterBundleAsset()
	local bundle, asset
	if self:IsTianShenMonsterRes() then
		bundle, asset = ResPath.GetBianShenModel(self.monster_res_id)
	else
		bundle, asset = ResPath.GetMonsterModel(self.monster_res_id)
	end

	return bundle, asset
end

-- 角色
function SkillPreviewRole:UpdateRoleShowerModel()
    local appe_data = self.vo.appearance
    local resouce = appe_data.fashion_body
    local body_res, face_res, hair_res = RoleWGData.GetShowRoleSkinPartRes(self.vo.sex, self:GetProf(), resouce, 0, appe_data.default_face_res_id, appe_data.default_hair_res_id)
    local role_bundle, role_name = ResPath.GetRoleModel(RoleWGData.GetJobModelId(self.vo.sex, self:GetProf()))
    self:ClearTianShenSpecialEff()
    local extra_model_data = {
        role_body_res = body_res,
        role_face_res = face_res,
        role_hair_res = hair_res,
    }

    self:ChangeMainPartModel(role_bundle, role_name, nil, SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.ROLE, DRAW_MODEL_TYPE.ROLE, extra_model_data)


    self:EquipDataChangeListen()    -- 武器
    self:ChangeFaZhen()             -- 法阵
    self:UpdateSkillHaloResId()     -- 更新技能光环
    self:ChangeSkillHalo()          -- 技能光环
    self:UpdateWing()
    self:UpdateJianZhen()
    self:UpdateHalo()
    self:ChangeBeast()
    self:ChangeWuHun()

    local load_ride_back = BindTool.Bind(self.LoadRideBack, self)
    self:UpdateMount(load_ride_back)
end

-- 修为角色
function SkillPreviewRole:UpdateXiuWeiModel(is_xiuwei_bianshen)
    -- 只有主部件模型改变，其他使用角色的外观
    self:RemoveModel(SceneObjPart.Weapon)
    self:RemoveModel(SceneObjPart.Wing)
    self:RemoveModel(SceneObjPart.Halo)
    self:RemoveModel(SceneObjPart.BaoJu)
    self:RemoveModel(SceneObjPart.QiLinBi)
    self:RemoveModel(SceneObjPart.Waist)
    self:RemoveModel(SceneObjPart.Mask)
    self:RemoveModel(SceneObjPart.ShouHuan)
    self:RemoveModel(SceneObjPart.Tail)
    self:RemoveModel(SceneObjPart.Jianling)
    self:RemoveModel(SceneObjPart.FaZhen)
    self:RemoveModel(SceneObjPart.SkillHalo)
    self:RemoveModel(SceneObjPart.GodOrDemonHalo)
    local role_bundle, role_name = ResPath.GetRoleModel(self.special_res_id)
    local appe_data = self.vo.appearance
    local resouce = appe_data.fashion_body
    local body_res, face_res, hair_res = RoleWGData.GetShowRoleSkinPartRes(self.vo.sex, self:GetProf(), resouce, 0, appe_data.default_face_res_id, appe_data.default_hair_res_id)

    local extra_model_data = {
        role_body_res = body_res,
        role_face_res = face_res,
        role_hair_res = hair_res,
    }

    if is_xiuwei_bianshen or self:IsXiuWeiBianShen() then
        local _, image_lv, _ = CultivationWGData.Instance:GetProgressImage(self.vo.appearance_param)
        --刷新模型
        local real_image_lv = image_lv or 0
        local cfg = CultivationWGData.Instance:GetNuqiUpgradeCfg(self.vo.appearance_param, real_image_lv)

        if cfg ~= nil then
            local main_vo = GameVoManager.Instance:GetMainRoleVo()
            local prof = main_vo.prof
            local sex = main_vo.sex
            body_res, face_res, hair_res = RoleWGData.GetShowRoleRealmSkinPartRes(sex, prof, cfg.default_body, cfg.default_face, cfg.default_hair)

            extra_model_data = {
                role_body_res = body_res,
                role_face_res = face_res,
                role_hair_res = hair_res,
                is_realm = true,
            }
        end
    end

    self:RemoveAransformationEffect()

    if is_xiuwei_bianshen then
        self:ChangeMainPartModel(role_bundle, role_name, function()
            self:ExecuteAransformationAction()
        end, nil, true, extra_model_data)
    else
        self:ChangeMainPartModel(role_bundle, role_name, nil, nil, DRAW_MODEL_TYPE.ROLE, extra_model_data)
        -- self:TransformationActionFinish()
    end

    self:UpdateJianZhen()
end

function SkillPreviewRole:LoadRideBack()
    if self:IsRiding() then
        self:CrossAction(SceneObjPart.Main, self:SetRidingActionIdelParam())
    end
end

-- 更新武器模型
function SkillPreviewRole:EquipDataChangeListen()
    local vo = self.vo
    if self:IsRiding() then
        return
    end

    local can_use_weapon_attach = false
    self.draw_obj:SetIsCanWeaponPointAttach(can_use_weapon_attach)
    local weapon_res_id = 0
    local res_func = ResPath.GetWeaponModelRes

    -- if need_weapon then
        if self:IsTianShenAppearance() then -- 天神变身 武器
            res_func = ResPath.GetTianShenShenQiPath
            if 0 == vo.tianshenshenqi_appeid then
                local tianshen_cfg, is_huamo = TianShenWGData.Instance:GetImageModelByAppeId(self.vo.appearance_param, false)
                local shenqi_cfg = TianShenWGData.Instance:GetShenQiByTianShenIndex(tianshen_cfg and tianshen_cfg.index)
                local cur_huanhua_id = TianShenWGData.Instance:GetWaiGuanHuanHua(shenqi_cfg and shenqi_cfg.index)
                if cur_huanhua_id == -1 then
                    weapon_res_id = shenqi_cfg and shenqi_cfg.ts_res_id or weapon_res_id
                else
                    weapon_res_id = shenqi_cfg and shenqi_cfg.ts_res_id1 or weapon_res_id
                end
                weapon_res_id = tianshen_cfg and is_huamo and tianshen_cfg.ts_res_id or weapon_res_id
            elseif -1 == vo.tianshenshenqi_appeid then
                local tianshen_cfg, is_huamo = TianShenWGData.Instance:GetImageModelByAppeId(self.vo.appearance_param, false)
                local shenqi_cfg = TianShenWGData.Instance:GetShenQiByTianShenIndex(tianshen_cfg and tianshen_cfg.index)
                weapon_res_id = shenqi_cfg and shenqi_cfg.ts_res_id or weapon_res_id
                weapon_res_id = tianshen_cfg and is_huamo and tianshen_cfg.ts_res_id or weapon_res_id
            else
                local waiguan_cfg = TianShenWGData.Instance:GetWaiGuanCfg(vo.tianshenshenqi_appeid)
                weapon_res_id = waiguan_cfg and waiguan_cfg.facade_res_id or weapon_res_id
            end
        elseif self:IsTianShenMonsterRes() then
            res_func = ResPath.GetTianShenShenQiPath
            local tianshen_cfg, is_huamo = TianShenWGData.Instance:GetImageModelByAppeId(self.monster_res_id, false)
            local shenqi_cfg = TianShenWGData.Instance:GetShenQiByTianShenIndex(tianshen_cfg and tianshen_cfg.index)
            local cur_huanhua_id = TianShenWGData.Instance:GetWaiGuanHuanHua(shenqi_cfg and shenqi_cfg.index)
            if cur_huanhua_id == -1 then
                weapon_res_id = shenqi_cfg and shenqi_cfg.ts_res_id or weapon_res_id
            else
                weapon_res_id = shenqi_cfg and shenqi_cfg.ts_res_id1 or weapon_res_id
            end
            weapon_res_id = tianshen_cfg and is_huamo and tianshen_cfg.ts_res_id or weapon_res_id
        elseif 0 ~= vo.appearance.fashion_wuqi then
            weapon_res_id = AppearanceWGData.GetFashionWeaponIdByResViewId(vo.appearance.fashion_wuqi)
        elseif 0 ~= vo.shenwu_appeid then
            weapon_res_id = RoleWGData.GetFashionWeaponId(vo.sex, vo.prof, vo.shenwu_appeid)
        else
            weapon_res_id = RoleWGData.GetJobWeaponId(vo.sex, vo.prof)
        end
    -- end

    self.weapon_res_id = weapon_res_id
    if 0 ~= weapon_res_id then
        local bundle, asset = res_func(weapon_res_id)
        self:ChangeModel(SceneObjPart.Weapon, bundle, asset)
    else
        self:RemoveModel(SceneObjPart.Weapon)
    end
end

-- 法阵
function SkillPreviewRole:ChangeFaZhen()
    local res_id = self.vo.appearance and self.vo.appearance.fazhen_id
    self.fazhen_res_id = res_id
    if res_id and res_id > -1 then
        self:ChangeModel(SceneObjPart.FaZhen, ResPath.GetSkillFaZhenModel(self.fazhen_res_id))
    else
        self:RemoveModel(SceneObjPart.FaZhen)
    end
end

-- 技能光环
function SkillPreviewRole:ChangeSkillHalo()
    local is_show = not self.is_fighting
    if self.skill_halo_res_id ~= nil and self.skill_halo_res_id ~= 0 and is_show then
        self:ChangeModel(SceneObjPart.SkillHalo, ResPath.GetSkillHaloModel(self.skill_halo_res_id))
    else
        self:RemoveModel(SceneObjPart.SkillHalo)
    end
end

-- 翅膀
function SkillPreviewRole:UpdateWing()
    if not self.vo then return end
    if 0 ~= self.vo.wing_appeid then
        self:ChangeModel(SceneObjPart.Wing, ResPath.GetWingModel(self.vo.wing_appeid))
    else
        self:RemoveModel(SceneObjPart.Wing)
    end
end

-- 剑阵
function SkillPreviewRole:UpdateJianZhen()
    if not self.vo then return end
    if 0 ~= self.vo.jianzhen_appeid then
        self:ChangeModel(SceneObjPart.Jianling, ResPath.GetJianZhenModel(self.vo.jianzhen_appeid))
    else
        self:RemoveModel(SceneObjPart.Jianling)
    end
end

-- 光环
function SkillPreviewRole:UpdateHalo()
    if not self.vo then return end
    if self.vo.appearance ~= nil and self.vo.appearance.fashion_guanghuan > 0 then
        self:ChangeModel(SceneObjPart.Halo, ResPath.GetHaloModel(self.vo.appearance.fashion_guanghuan))
    else
        self:RemoveModel(SceneObjPart.Halo)
    end
end

---------------------------------------武魂---------------------------------------------------
function SkillPreviewRole:SetWuHunCreateCallBack(callback)
    self.wuhun_create_callback = callback
    self:ChangeWuHun()
end

function SkillPreviewRole:CreateWuHunObj()
    local wuhun_obj = nil
    local role_vo = self:GetVo()
    local wuhun_vo = GameVoManager.Instance:CreateVo(WuHunObjVo)
    wuhun_vo.role_vo = role_vo
    wuhun_vo.wuhun_id = role_vo.wuhun_id
    wuhun_vo.wuhun_lv = role_vo.wuhun_lv
    wuhun_vo.owner_obj = self
    wuhun_vo.hp = 100
    wuhun_vo.max_hp = 100
    wuhun_obj = WuHunObj.New(wuhun_vo, SceneObjType.SkillShower)
    wuhun_obj:CorrectEffectTriggerCustomScale(1)
    return wuhun_obj
end

function SkillPreviewRole:ChangeWuHun()
    local is_mount = self:IsRiding()
    if self.vo and self.vo.wuhun_id and self.vo.wuhun_id > 0 and (not is_mount) then
        if not self.wuhun_obj then
            self.wuhun_obj = self:CreateWuHunObj()
            if self.wuhun_create_callback then
                self.wuhun_create_callback(self)
                self.wuhun_create_callback = nil
            end
        else
            self.wuhun_obj:ChangeWuHunImageId(self.vo.wuhun_id, self.vo.wuhun_lv)
            self.wuhun_obj:AttachToRoleMainPart()
            if self.wuhun_create_callback then
                self.wuhun_create_callback(self)
                self.wuhun_create_callback = nil
            end
        end
    else
        self:RemoveWuHun()
    end
end

--武魂
function SkillPreviewRole:RemoveWuHun()
    if self.wuhun_obj then
        self.wuhun_obj:DeleteMe()
        self.wuhun_obj = nil
    end
end
--武魂 end]]

-- [[驭兽
-- 灵兽变更
function SkillPreviewRole:ChangeBeast()
    if self.vo.beast_ids and self.vo.beast_ids > 0 then
        if not self.beast_obj or self.beast_obj:IsDeleted() then
            self.beast_obj = self:CreateBeastObjByRole(self, 1, self.vo.beast_ids)
            if self.beast_obj then
                self:SetBeast(self.beast_obj, 1)
                if self.beast_create_callback then
                    self.beast_create_callback(self.beast_obj, 1)
                end
            end
        else
            self:SetBeast(self.beast_obj, 1)
            self.beast_obj:TryFlushAppearance(self.vo.beast_ids, 1)
        end
    else
        self:DeleteAllBeast()
    end
end

function SkillPreviewRole:CreateBeastObjByRole(role, battle_index, beast_id)
    local beast_obj = nil
    local role_vo = role:GetVo()
    local beast_vo = GameVoManager.Instance:CreateVo(BeastObjVo)
    local aim_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.beast_id)
    beast_vo.role_vo = role_vo
    beast_vo.obj_name = aim_cfg and aim_cfg.beast_name or ""
    beast_vo.battle_index = battle_index
    beast_vo.beast_id = beast_id
    beast_vo.beast_skin = role_vo.beast_skin
    beast_vo.pos_x, beast_vo.pos_y = role:GetLogicPos()
    beast_vo.pos_x = beast_vo.pos_x + 2
    beast_vo.owner_objid = role_vo.obj_id
    beast_vo.owner_obj_name = role_vo.name
    beast_vo.move_speed = role_vo.move_speed
    beast_vo.hp = 100
    beast_vo.max_hp = 100
    beast_vo.owner_obj = role
    beast_obj = self:CreateBeast(beast_vo)
    return beast_obj
end

function SkillPreviewRole:CreateBeast(vo)
    local obj = BeastObj.New(vo)
    return obj
end

function SkillPreviewRole:DeleteAllBeast()
    if self.beast_obj and self.beast_obj:GetVo() then
        self.beast_obj:DeleteMe()
        self.beast_obj = nil
    end
end
--驭兽 end]]


-- [[双生天神
function SkillPreviewRole:CreateShuangShengObj()
    local shaungsheng_tianshen_obj = nil
    local role_vo = self:GetVo()
    local shuangsheng_vo = GameVoManager.Instance:CreateVo(ShuangShengTianShenObjVo)
    shuangsheng_vo.role_vo = role_vo
    shuangsheng_vo.shaungsheng_tianshen_aura_id = role_vo.shaungsheng_tianshen_aura_id
    shuangsheng_vo.owner_obj = self
    shuangsheng_vo.hp = 100
    shuangsheng_vo.max_hp = 100
    shaungsheng_tianshen_obj = ShuangShengTianShenObj.New(shuangsheng_vo)

    return shaungsheng_tianshen_obj
end

function SkillPreviewRole:ChangeShuangSheng()
    if self.vo and self.vo.shaungsheng_tianshen_aura_id and self.vo.shaungsheng_tianshen_aura_id > -1 then
        if not self.shaungsheng_tianshen_obj then
            self.shaungsheng_tianshen_obj = self:CreateShuangShengObj()
            if self.shuangsheng_create_callback then
                self.shuangsheng_create_callback(self.shaungsheng_tianshen_obj)
            end
        else
            self.shaungsheng_tianshen_obj:ChangeShuangShengImageId(self.vo.shaungsheng_tianshen_aura_id)
            self.shaungsheng_tianshen_obj:AttachToRoleMainPart()
        end
    else
        self:RemoveShuangSheng()
    end
end

--双生天神
function SkillPreviewRole:RemoveShuangSheng()
    if self.shaungsheng_tianshen_obj then
        self.shaungsheng_tianshen_obj:DeleteMe()
        self.shaungsheng_tianshen_obj = nil
    end
end

--双生天神 end]]


function SkillPreviewRole:EnterStateStand()
    if self.draw_obj == nil then
        return
    end

    Role.EnterStateStand(self)

    if self.shaungsheng_tianshen_obj ~= nil then
        Role.EnterStateStand(self.shaungsheng_tianshen_obj)
    end
end

function SkillPreviewRole:EnterStateAttack(anim_name)
    self:ClearActionData()

    -- 秘笈技能只在技能展示时有动作
    local esoterica_skill_cfg = CultivationWGData.Instance:GetEsotericaCfgBySkillId(self.attack_skill_id)
    if esoterica_skill_cfg then
        anim_name = esoterica_skill_cfg.skill_show_anim
    else
        anim_name = SkillWGData.GetSkillActionStr(self.obj_type, self.attack_skill_id, self.attack_index)
    end

    if anim_name and anim_name ~= "" then
        self.action_time_record = self:GetActionTimeRecord(anim_name)
    end

    local part = self.draw_obj:GetPart(SceneObjPart.Main)
    local part_obj = part:GetObj()
    if part_obj == nil or IsNil(part_obj.gameObject) then
        return
    end

    if not anim_name or anim_name == SceneObjAnimator.Atk0 then
        anim_name = SceneObjAnimator.Idle
    end

    self.anim_name = anim_name
    if self:IsRidingFightMount() then
        self:CrossAction(SceneObjPart.Mount, anim_name, false)
    else
        self:CrossAction(SceneObjPart.Main, anim_name, false)
    end

    -- 无技能动作，需要播放音效
    local sound_bundle, sound_asset
    local is_tianshen_heji_skill = SkillWGData.Instance:GetIsTianShenHeJiSkill(self.attack_skill_id)
    if is_tianshen_heji_skill then
        local cfg = TianShenWGData.Instance:GetHejiCfgBySkillId(self.attack_skill_id)
        if cfg then
            sound_bundle = cfg.sound_bundle
            sound_asset = cfg.sound_asset
        end
    end

    if sound_bundle and sound_asset then
        local shower_draw_obj = self:GetDrawObj()
        if shower_draw_obj ~= nil and not IsNil(shower_draw_obj:GetTransfrom()) then
            local play_pos = shower_draw_obj:GetTransfrom().position
            AudioManager.PlayAndForget(sound_bundle, sound_asset, nil, play_pos.transform)
        end
    end

    self:OnAnimatorBegin()
end

function SkillPreviewRole:AttackActionEndHandle()
	if self.action_time_record ~= nil and self.action_time_record.has_back then
        local part = self.draw_obj:GetPart(SceneObjPart.Main)
        local part_obj = part:GetObj()
        if part_obj == nil or IsNil(part_obj.gameObject) then
            return
        end
        
        self.play_action_back_fun = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.OnPlayActionBackEnd, self), self.action_time_record.back_time)
        if self:IsRidingFightMount() then
            self:CrossAction(SceneObjPart.Mount, self.anim_name.."_back")
        else
            self:CrossAction(SceneObjPart.Main, self.anim_name.."_back")

            if self.shaungsheng_tianshen_obj ~= nil then
                self.shaungsheng_tianshen_obj:CrossAction(SceneObjPart.Main, self.anim_name.."_back")
            end
        end
    else
        self:ChangeToCommonState()
    end
end

-- 获取动作时间
function SkillPreviewRole:GetActionTimeRecord(anim_name)
    if self:IsTianShenAppearance() then
        return (TianShenBossActionConfig[self.special_res_id] or {})[anim_name]

    elseif self:IsGundam() then
        return GundamActionConfig[self.special_res_id][anim_name]

    elseif self:IsRidingFightMount() then
        local appid = self:GetCurRidingResId()
        return (ZuoqiActionConfig[appid] or {})[anim_name]

    elseif self.is_monster_obj then
        if self:IsTianShenMonsterRes() then
            return (TianShenBossActionConfig[self.monster_res_id] or {})[anim_name]
        else
            return (MonsterActionConfig[self.monster_res_id] or {})[anim_name]
        end
    elseif self:IsXiuWeiBianShen() then
        local power_type = self.vo.appearance_param + 1
        return ((RoleActionConfig[self.vo.sex] or {})[power_type + 1000] or {})[anim_name]
    end

    return ((RoleActionConfig[self.vo.sex] or {})[self:GetProf()] or {})[anim_name]
end

function SkillPreviewRole:EnterFightState()
    if self.draw_obj == nil then return end

    self.is_fighting = true
    self:ChangeSkillHalo()
end

function SkillPreviewRole:OnModelLoaded(part, obj, obj_class)
	Role.OnModelLoaded(self, part, obj, obj_class)
	if part == SceneObjPart.Main then
        if self.load_callback ~= nil then
            self.load_callback()
        end
	end
end

function SkillPreviewRole:SetModelLoadCallback(load_callback)
	self.load_callback = load_callback
end

function SkillPreviewRole:SetEnemyPos(enemy_target)
	self.enemy_target = enemy_target
end


function SkillPreviewRole:CharacterAnimatorEvent(param, state_info, anim_name, is_not_skill_shower)
    local actor_trigger = self:GetActorTrigger()
    if actor_trigger ~= nil then
        local source = self.draw_obj:GetPart(SceneObjPart.Main):GetObj()
        local target = self.enemy_target

        if self.attack_skill_id ~= nil then
            local is_hit = string.find(anim_name, "/hit")
            local cfg = SkillWGData.Instance:GetBuffLayerCfg(self.attack_skill_id)
            if cfg ~= nil and cfg.effect_buff_type == SKILL_BUFF_SHOW.SHAKE then
                state_info = state_info or {}
                state_info.is_ignore_shake = true
            end
            
            self:PlayAfterSkillEfffect(anim_name)
        end

        local pos = nil
        if self.attack_target_pos_x ~= nil and self.attack_target_pos_x ~= 0 and self.attack_target_pos_y ~= nil and self.attack_target_pos_y ~= 0 then
        	if self.attack_target_pos_x ~= self.logic_pos.x and self.attack_target_pos_y ~= self.logic_pos.x then
        		local t_x, t_y = GameMapHelper.LogicToWorld(self.attack_target_pos_x, self.attack_target_pos_y)
        		pos = Vector3(t_x, 0, t_y)
        	end
        end

        if pos ~= nil then
        	if state_info ~= nil then
        		if state_info.dir_pos == nil then
        			state_info.dir_pos = pos
        		end
        	else
        		state_info = {dir_pos = pos}
        	end
        end

        state_info = state_info or {}
        state_info.is_skill_shower = not is_not_skill_shower
        -- 怪物蓄力多范围技能
        local begin = string.find(anim_name, "magic[1-4]_3/begin")
        if anim_name ~= nil and begin ~= nil and self.attack_skill_id ~= nil and self.attack_skill_id ~= nil and self:IsMonster() and self.attack_sub_zone ~= nil and next(self.attack_sub_zone) ~= nil then
            local skill_cfg = SkillWGData.GetMonsterSkillConfig(self.attack_skill_id)
            if skill_cfg ~= nil and skill_cfg.RandZoneCount ~= "" and skill_cfg.RandZoneCount > 0 then
                local pos = self:GetLuaPosition()
                local pos_table = {}
                local info = {pos_table = pos_table}
                for k,v in pairs(self.attack_sub_zone) do
                    local real_x, real_y = GameMapHelper.LogicToWorld(v.pos_x, v.pos_y)
                    local start_pos = Vector3(real_x, pos.y + 0.1, real_y)
                    table.insert(pos_table, start_pos)
                end

                actor_trigger:OnAnimatorEvent(param, info, source, nil, anim_name)
            else
                actor_trigger:OnAnimatorEvent(param, state_info, source, target, anim_name)
            end
        else
            actor_trigger:OnAnimatorEvent(param, state_info, source, target, anim_name)
        end
    end
end

function SkillPreviewRole:ClearActionTimeRecord()
	self.action_time_record = nil
end

function SkillPreviewRole:IsSkillShower()
    return true
end

function SkillPreviewRole:IsMonster()
	return self.is_monster_obj
end

function SkillPreviewRole:ClearActorEffect()
    if self.actor_trigger then
        self.actor_trigger:StopAllEffectPlay()
    end
end

function SkillPreviewRole:SetSpecialEffectRoot(effect_root)
    if self.actor_trigger then
        self.actor_trigger:SetSpecialEffectRoot(effect_root)
    end
end

------------------------------------------------ 增加怒气变身动作---------------------
-- 增加怒气变身动作
function SkillPreviewRole:ExecuteAransformationAction(callback)
    local anim_name = SceneObjAnimator.transformation 
	local clip_name = SceneObjAnimator.transformation 

    local part = self.draw_obj:GetPart(SceneObjPart.Main)
    if not part or not part.obj then 
        return 
    end

    local anim = part:GetObj().animator
    if not anim then 
        return 
    end

    local clip = anim:GetAnimationClip(clip_name)
    if not clip then 
        return 
    end


    local action_end_time = clip.length or 0.1
    self:CrossAction(SceneObjPart.Main, anim_name)
    local bianshen_name_1 = "bianshen"
    local bundle_name, asset_name = ResPath.GetAransformationEffect(bianshen_name_1, self.vo.appearance_param)
    self:CreateAransformationEffect(bundle_name, asset_name, self.vo.appearance_param)
    self:RemoveAransformationDelayTime()
    self.cancel_transformation_action = GlobalTimerQuest:AddDelayTimer(function ()
        -- self:TransformationActionFinish()
        self:CrossAction(SceneObjPart.Main, SceneObjAnimator.Idle)

        if callback then
            callback()
        end
    end, action_end_time)
end

-- 
function SkillPreviewRole:TransformationActionFinish()
    self:RemoveAransformationDelayTime()
    local bianshen_name_2 = "qiliu"
    local bundle_name, asset_name = ResPath.GetAransformationEffect(bianshen_name_2, self.vo.appearance_param)
    self:CreateAransformationEffect(bundle_name, asset_name, self.vo.appearance_param)
    -- self:CreateAransformationWingEffect(self.vo.appearance_param)
end


-- 创建变身特效
function SkillPreviewRole:CreateAransformationEffect(bundle, asset, index)
    local foot_trail_point = self.draw_obj:GetAttachPoint(AttachPoint.HurtRoot)
    local uuid = self.vo and self.vo.uuid or 0
    local load_str = string.format("aransformation_effect_%s", uuid)

    if foot_trail_point then
        if self.aransformation_effect_loader == nil then
            self.aransformation_effect_loader = AllocAsyncLoader(self, load_str)
            self.aransformation_effect_loader:SetIsUseObjPool(true)
            self.aransformation_effect_loader:SetIsInQueueLoad(true)
            self.aransformation_effect_loader:SetParent(foot_trail_point)
        end

        self.aransformation_effect_loader:Load(bundle, asset)
    end
end

-- 创建变身翅膀特效
function SkillPreviewRole:CreateAransformationWingEffect(index)
    local bundle, asset = ResPath.GetAransformationEffect("beishi", index)
    local wing_point = self.draw_obj:GetAttachPoint(AttachPoint.Wing)
    local uuid = self.vo and self.vo.uuid or 0
    local load_str = string.format("aransformation_wing_effect_%s", uuid)

    if wing_point then
        if self.aransformation_wing_effect_loader == nil then
            self.aransformation_wing_effect_loader = AllocAsyncLoader(self, load_str)
            self.aransformation_wing_effect_loader:SetIsUseObjPool(true)
            self.aransformation_wing_effect_loader:SetIsInQueueLoad(true)
            self.aransformation_wing_effect_loader:SetParent(wing_point)
        end

        self.aransformation_wing_effect_loader:Load(bundle, asset)
    end
end

-- 移除变身特效
function SkillPreviewRole:RemoveAransformationEffect()
	if self.aransformation_effect_loader then
        self.aransformation_effect_loader:Destroy()
		self.aransformation_effect_loader = nil
	end

    if self.aransformation_wing_effect_loader then
        self.aransformation_wing_effect_loader:Destroy()
		self.aransformation_wing_effect_loader = nil
	end
end

-- 移除定时器
function SkillPreviewRole:RemoveAransformationDelayTime()
    if self.cancel_transformation_action then
        GlobalTimerQuest:CancelQuest(self.cancel_transformation_action)
        self.cancel_transformation_action = nil
    end
end
------------------------------------------------ 增加怒气变身动作End---------------------