-- S-随机活动-周一狂欢.xls

return {
other={
{}
},

other_meta_table_map={
},
task={
{}
},

task_meta_table_map={
},
reward_level={
{choose_count=1,},
{level=1,reward_item="27810:1:1|27852:1:1|27830:1:1|27831:1:1|27832:1:1",},
{level=2,reward_item="22575:1:1|32287:1:1|26500:2:1|26515:2:1|28805:1:1|28806:1:1",},
{level=3,choose_count=3,reward_item="22011:1:1|26121:1:1|26344:1:1|26376:1:1|26149:1:1|26148:1:1|22636:1:1|22611:1:1|26165:1:1",}
},

reward_level_meta_table_map={
},
other_default_table={draw_time="6, 24",bonus_need_draw_times=10,},

task_default_table={task_type=1,complete_param=9999,des="获得活跃点",},

reward_level_default_table={level=0,choose_count=2,reward_item="27812:1:1",}

}

