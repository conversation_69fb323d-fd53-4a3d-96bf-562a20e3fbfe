require("game/customized_suit/customized_suit_wg_data")
require("game/customized_suit/customized_suit_view")
require("game/customized_suit/customized_suit_item")
require("game/customized_suit/customized_suit_skill_view")
require("game/customized_suit/customized_suit_star_up_view")
require("game/customized_suit/customized_suit_attr_view")
require("game/customized_suit/customized_suit_part_xuancai_view")


-- 【套装定制】
CustomizedSuitWGCtrl = CustomizedSuitWGCtrl or BaseClass(BaseWGCtrl)

function CustomizedSuitWGCtrl:__init()
	if nil ~= CustomizedSuitWGCtrl.Instance then
		ErrorLog("[CustomizedSuitWGCtrl] attempt to create singleton twice!")
		return
	end
	CustomizedSuitWGCtrl.Instance = self

    self.data = CustomizedSuitWGData.New()
	self.view = CustomizedSuitView.New(GuideModuleName.CustomizedSuitView)

	self.customized_suit_skill_view = CustomizedSuitSkillView.New()
	self.customized_skill_attr_view = CustomizedAttrView.New()
	self.customized_suit_star_up_view = CustomizedSuitStarUpView.New()
	self.customized_suit_part_xuancai_view = CustomizedSuitPartXuanCaiView.New()

	self:RegisterAllProtocols()
end

function CustomizedSuitWGCtrl:__delete()
    self.data:DeleteMe()
    self.data = nil

    self.view:DeleteMe()
    self.view = nil

	self.customized_suit_skill_view:DeleteMe()
	self.customized_suit_skill_view = nil

	self.customized_skill_attr_view:DeleteMe()
	self.customized_skill_attr_view = nil

	self.customized_suit_star_up_view:DeleteMe()
	self.customized_suit_star_up_view = nil

	self.customized_suit_part_xuancai_view:DeleteMe()
	self.customized_suit_part_xuancai_view = nil

    CustomizedSuitWGCtrl.Instance = nil
end


function CustomizedSuitWGCtrl:OnPartActiveResult(result, suit, part)
	if result == 1 then
		if self.view:IsOpen() then
			self.view:Flush()
			self.view:DoActiveEffect(suit, part)
			---刷洗一下解锁状态
			self.data:RefreshAllThemeRedbySuit()		---刷新一下红点
			RemindManager.Instance:Fire(RemindName.CustomizedSuitView)
		end
	end
end

function CustomizedSuitWGCtrl:FlushView()
	if self.view:IsOpen() then
		self.view:Flush()
	end

	---刷洗一下解锁状态
	self:FlushCustomizedSkillPanel()
	self.data:RefreshAllThemeRedbySuit()		---刷新一下红点
	RemindManager.Instance:Fire(RemindName.CustomizedSuitView)
end

function CustomizedSuitWGCtrl:RegisterAllProtocols()
	-- 技能定制
	self:RegisterProtocol(CSWardrobeSkillClientOperate)
	self:RegisterProtocol(SCWardrobeSkillAllInfo, "SCWardrobeSkillAllInfo")
	self:RegisterProtocol(SCWardrobeSkillOneInfo, "SCWardrobeSkillOneInfo")
	self:RegisterProtocol(SCWardrobeXuancaiList, "SCWardrobeXuancaiList")
	self:RegisterProtocol(SCWardrobeXuancaiUpdate, "SCWardrobeXuancaiUpdate")
end

---------------------------套装定制------------------------------
function CustomizedSuitWGCtrl:OpenCustomizedSkillPanel(suit)
	self.data:RefreshAllThemeRedbySuit()
	self.customized_suit_skill_view:SetSuit(suit)

	if not self.customized_suit_skill_view:IsOpen() then
		self.customized_suit_skill_view:Open()
	else
		self.customized_suit_skill_view:Flush()
	end
end

function CustomizedSuitWGCtrl:FlushCustomizedSkillPanel()
	if self.customized_suit_skill_view:IsOpen() then
		self.customized_suit_skill_view:Flush()
	end
end

function CustomizedSuitWGCtrl:OpenCustomizedSkillAttrPanel(suit, part)
	self.customized_skill_attr_view:SetSuitPart(suit, part)

	if not self.customized_skill_attr_view:IsOpen() then
		self.customized_skill_attr_view:Open()
	else
		self.customized_skill_attr_view:Flush()
	end
end

function CustomizedSuitWGCtrl:FlushCustomizedSkillAttrPanel()
	if self.customized_skill_attr_view:IsOpen() then
		self.customized_skill_attr_view:Flush()
	end
end

---套装升星
function CustomizedSuitWGCtrl:OpenStarUpPanel(suit)
	self.customized_suit_star_up_view:SetSuit(suit)

	if not self.customized_suit_star_up_view:IsOpen() then
		self.customized_suit_star_up_view:Open()
	else
		self.customized_suit_star_up_view:Flush()
	end
end

function CustomizedSuitWGCtrl:FlushCustomizedStarUpPanel()
	if self.customized_suit_star_up_view:IsOpen() then
		self.customized_suit_star_up_view:Flush()
	end
end

---套装炫彩
function CustomizedSuitWGCtrl:OpenPartColorfulPanel(suit, part)
	self.customized_suit_part_xuancai_view:SetSuitPart(suit, part)

	if not self.customized_suit_part_xuancai_view:IsOpen() then
		self.customized_suit_part_xuancai_view:Open()
	else
		self.customized_suit_part_xuancai_view:Flush()
	end
end

-- 刷新炫彩
function CustomizedSuitWGCtrl:FlushPartColorfulPanel()
	if self.customized_suit_part_xuancai_view:IsOpen() then
		self.customized_suit_part_xuancai_view:FlushXuanCailistForServer()
	end
end

-- 刷新炫彩
function CustomizedSuitWGCtrl:ChangeSelectPartColorful(xuancai_item)
	if self.customized_suit_part_xuancai_view:IsOpen() then
		self.customized_suit_part_xuancai_view:ChangeSelectPartColorful(xuancai_item)
	end
end

-- 请求操作
function CustomizedSuitWGCtrl:SendWardrobeSkillClientOperate(opera_type, param1, param2, param3, attr_list, attr_id2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSWardrobeSkillClientOperate)
	protocol.type = opera_type
	protocol.param1 = param1
	protocol.param2 = param2
	protocol.param3 = param3

	protocol.attr1_id = {}
	for i = 1, 3 do
		protocol.attr1_id[i] = attr_list and attr_list[i] or 0
	end

	protocol.attr2_id = attr_id2 or 0

	-- print_error("SendWardrobeSkillClientOperate", protocol)
	protocol:EncodeAndSend()
end

-- 客户端请求激活套装选择技能
function CustomizedSuitWGCtrl:SendWardrobeTypeChoose(param1, param2, param3)
	self:SendWardrobeSkillClientOperate(WARDROBE_SKILL_TYPE.WARDROBE_SKILL_TYPE_CHOOSE, param1, param2, param3)
end

-- 客户端请求激活套装技能升级
function CustomizedSuitWGCtrl:SendWardrobeTypeUpgrade(param1, param2, param3)
	self:SendWardrobeSkillClientOperate(WARDROBE_SKILL_TYPE.WARDROBE_SKILL_TYPE_UPGRADE, param1, param2, param3)
end

-- 客户端请求激活套装技能升星
function CustomizedSuitWGCtrl:SendWardrobeTypeStarUp(param1, param2, param3)
	self:SendWardrobeSkillClientOperate(WARDROBE_SKILL_TYPE.WARDROBE_SKILL_TYPE_STAR, param1, param2, param3)
end

-- 客户端请求激活套装技能重置
function CustomizedSuitWGCtrl:SendWardrobeTypeReset(param1, param2, param3)
	self:SendWardrobeSkillClientOperate(WARDROBE_SKILL_TYPE.WARDROBE_SKILL_TYPE_RESET, param1, param2, param3)
end

-- 客户端请求激活定制属性
function CustomizedSuitWGCtrl:SendSkillTypeAttrChoose(param1, param2, param3, attr_list, attr_id2)
	self:SendWardrobeSkillClientOperate(WARDROBE_SKILL_TYPE.WARDROBE_SKILL_TYPE_ATTR_CHOOSE, param1, param2, param3, attr_list, attr_id2)
end

-- 客户端请求激活套装技能重置
function CustomizedSuitWGCtrl:SendSkillTypeAttrReset(param1, param2, param3)
	self:SendWardrobeSkillClientOperate(WARDROBE_SKILL_TYPE.WARDROBE_SKILL_TYPE_ATTR_RESET, param1, param2, param3)
	self.data:ResetThemeAttrBySuitPart(param1, param2)
end

---单个套装定制信息
function CustomizedSuitWGCtrl:SCWardrobeSkillOneInfo(protocol)
	-- print_error("定制技能单个套装", protocol)
	if protocol.wardrobe_info then
		self.data:RefreshOneThemeSkill(protocol.wardrobe_info)
		--刷新红点
		self.view:Flush()
		--刷新界面
		self:FlushCustomizedSkillPanel()
		self:FlushCustomizedStarUpPanel()
		RemindManager.Instance:Fire(RemindName.CustomizedSuitView)
	end
end

---全部套装定制信息
function CustomizedSuitWGCtrl:SCWardrobeSkillAllInfo(protocol)
	-- print_error("定制技能全部套装", protocol)
	if protocol.wardrobe_list then
		self.data:RefreshAllThemeSkill(protocol.wardrobe_list)
		--刷新红点
		self.view:Flush()
		--刷新界面
		self:FlushCustomizedSkillPanel()
		self:FlushCustomizedStarUpPanel()
		RemindManager.Instance:Fire(RemindName.CustomizedSuitView)
	end
end


---全部套装炫彩信息
function CustomizedSuitWGCtrl:SCWardrobeXuancaiList(protocol)
	-- print_error("全部套装炫彩信息", protocol)
	if protocol.wardrobe_xuancai_list then
		self.data:RefreshAllXuanCai(protocol.wardrobe_xuancai_list)
	end
end

---全部套装单个炫彩信息
function CustomizedSuitWGCtrl:SCWardrobeXuancaiUpdate(protocol)
	-- print_error("全部套装单个炫彩信息", protocol)
	self.data:RefreshOneXuanCai2(protocol)
	self.view:OnSelectXuanCaiUpdate()
	self:FlushPartColorfulPanel()
	self.view:Flush()
end

