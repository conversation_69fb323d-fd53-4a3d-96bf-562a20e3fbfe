﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class FancyScrollView_FancyCell_FancyScrollView_NullContextWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(FancyScrollView.FancyCell<FancyScrollView.NullContext>), typeof(UnityEngine.MonoBehaviour), "FancyCell_FancyScrollView_NullContext");
		<PERSON><PERSON>Function("SetContext", SetContext);
		<PERSON><PERSON>RegFunction("RefreshCellView", RefreshCellView);
		<PERSON><PERSON>RegFunction("Initialize", Initialize);
		<PERSON>.RegFunction("SetVisible", SetVisible);
		<PERSON><PERSON>unction("SetDataCount", SetDataCount);
		L.RegFunction("UpdatePosition", UpdatePosition);
		L.RegFunction("UpdateSibling", UpdateSibling);
		<PERSON>.RegFunction("__eq", op_Equality);
		<PERSON><PERSON>RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("Index", get_Index, set_Index);
		<PERSON><PERSON>ar("CurrentPosition", get_CurrentPosition, set_CurrentPosition);
		L.RegVar("IsVisible", get_IsVisible, null);
		L.RegVar("RefreshCellDel", get_RefreshCellDel, set_RefreshCellDel);
		L.RegVar("RefreshPosDel", get_RefreshPosDel, set_RefreshPosDel);
		L.RegFunction("RefreshPosDelegate", FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshPosDelegate);
		L.RegFunction("RefreshCellDelegate", FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshCellDelegate);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetContext(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FancyScrollView.FancyCell<FancyScrollView.NullContext> obj = (FancyScrollView.FancyCell<FancyScrollView.NullContext>)ToLua.CheckObject<FancyScrollView.FancyCell<FancyScrollView.NullContext>>(L, 1);
			FancyScrollView.NullContext arg0 = (FancyScrollView.NullContext)ToLua.CheckObject(L, 2, typeof(FancyScrollView.NullContext));
			obj.SetContext(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RefreshCellView(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			FancyScrollView.FancyCell<FancyScrollView.NullContext> obj = (FancyScrollView.FancyCell<FancyScrollView.NullContext>)ToLua.CheckObject<FancyScrollView.FancyCell<FancyScrollView.NullContext>>(L, 1);
			obj.RefreshCellView();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Initialize(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			FancyScrollView.FancyCell<FancyScrollView.NullContext> obj = (FancyScrollView.FancyCell<FancyScrollView.NullContext>)ToLua.CheckObject<FancyScrollView.FancyCell<FancyScrollView.NullContext>>(L, 1);
			obj.Initialize();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetVisible(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FancyScrollView.FancyCell<FancyScrollView.NullContext> obj = (FancyScrollView.FancyCell<FancyScrollView.NullContext>)ToLua.CheckObject<FancyScrollView.FancyCell<FancyScrollView.NullContext>>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetVisible(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetDataCount(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FancyScrollView.FancyCell<FancyScrollView.NullContext> obj = (FancyScrollView.FancyCell<FancyScrollView.NullContext>)ToLua.CheckObject<FancyScrollView.FancyCell<FancyScrollView.NullContext>>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SetDataCount(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UpdatePosition(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FancyScrollView.FancyCell<FancyScrollView.NullContext> obj = (FancyScrollView.FancyCell<FancyScrollView.NullContext>)ToLua.CheckObject<FancyScrollView.FancyCell<FancyScrollView.NullContext>>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.UpdatePosition(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UpdateSibling(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			FancyScrollView.FancyCell<FancyScrollView.NullContext> obj = (FancyScrollView.FancyCell<FancyScrollView.NullContext>)ToLua.CheckObject<FancyScrollView.FancyCell<FancyScrollView.NullContext>>(L, 1);
			obj.UpdateSibling();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Index(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.FancyCell<FancyScrollView.NullContext> obj = (FancyScrollView.FancyCell<FancyScrollView.NullContext>)o;
			int ret = obj.Index;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Index on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CurrentPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.FancyCell<FancyScrollView.NullContext> obj = (FancyScrollView.FancyCell<FancyScrollView.NullContext>)o;
			float ret = obj.CurrentPosition;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CurrentPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsVisible(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.FancyCell<FancyScrollView.NullContext> obj = (FancyScrollView.FancyCell<FancyScrollView.NullContext>)o;
			bool ret = obj.IsVisible;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsVisible on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RefreshCellDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.FancyCell<FancyScrollView.NullContext> obj = (FancyScrollView.FancyCell<FancyScrollView.NullContext>)o;
			FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshCellDelegate ret = obj.RefreshCellDel;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RefreshCellDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RefreshPosDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.FancyCell<FancyScrollView.NullContext> obj = (FancyScrollView.FancyCell<FancyScrollView.NullContext>)o;
			FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshPosDelegate ret = obj.RefreshPosDel;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RefreshPosDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Index(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.FancyCell<FancyScrollView.NullContext> obj = (FancyScrollView.FancyCell<FancyScrollView.NullContext>)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.Index = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Index on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_CurrentPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.FancyCell<FancyScrollView.NullContext> obj = (FancyScrollView.FancyCell<FancyScrollView.NullContext>)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.CurrentPosition = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CurrentPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_RefreshCellDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.FancyCell<FancyScrollView.NullContext> obj = (FancyScrollView.FancyCell<FancyScrollView.NullContext>)o;
			FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshCellDelegate arg0 = (FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshCellDelegate)ToLua.CheckDelegate<FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshCellDelegate>(L, 2);
			obj.RefreshCellDel = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RefreshCellDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_RefreshPosDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.FancyCell<FancyScrollView.NullContext> obj = (FancyScrollView.FancyCell<FancyScrollView.NullContext>)o;
			FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshPosDelegate arg0 = (FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshPosDelegate)ToLua.CheckDelegate<FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshPosDelegate>(L, 2);
			obj.RefreshPosDel = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RefreshPosDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshPosDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshPosDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshPosDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshCellDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshCellDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshCellDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

