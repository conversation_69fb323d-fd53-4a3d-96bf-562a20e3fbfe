-- 签到奖励弹窗
GuildSignRewardView = GuildSignRewardView or BaseClass(SafeBaseView)

function GuildSignRewardView:__init()
	self.view_style = ViewStyle.Window
	self:SetMaskBg(true, true)
	self.is_safe_area_adapter = true

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(694, 444)})
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_guild_sign_reward")
end

function GuildSignRewardView:__delete()

end

function GuildSignRewardView:LoadCallBack()
	if not self.sigh_reward_list then
		self.sigh_reward_list = AsyncListView.New(ItemCell, self.node_list["sigh_reward_list"])
		self.sigh_reward_list:SetStartZeroIndex(false)
	end
end

function GuildSignRewardView:ReleaseCallBack()
	if self.sigh_reward_list then
		self.sigh_reward_list:DeleteMe()
		self.sigh_reward_list = nil
	end

	self.data = nil
end

function GuildSignRewardView:SetDataAndOpen(data)
	self.data = data
	if not self:IsOpen() then
		self:Open()
	else
		self:Flush()
	end
end

function GuildSignRewardView:OnFlush(param_t, index)
	if not self.data then
		return
	end

	self.sigh_reward_list:SetDataList(self.data.reward_list)
end