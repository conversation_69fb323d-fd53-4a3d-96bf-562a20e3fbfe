require("game/hundred_equip/hundred_equip_wg_data")
require("game/hundred_equip/hundred_equip_view")
require("game/hundred_equip/hundred_equip_ratio")
require("game/hundred_equip/hundred_equip_tip")
require("game/hundred_equip/hundred_equip_award_view")
require("game/hundred_equip/hundred_equip_award_preview")
require("game/hundred_equip/hundred_equip_scene_view")
require("game/hundred_equip/hundred_equip_drop_times_reward_view")

HundredEquipWGCtrl = HundredEquipWGCtrl or BaseClass(BaseWGCtrl)

function HundredEquipWGCtrl:__init()
	if HundredEquipWGCtrl.Instance then
		error("[HundredEquipWGCtrl]:Attempt to create singleton twice!")
		return
	end
	HundredEquipWGCtrl.Instance = self

	self.data = HundredEquipWGData.New()
	self.view = HundredEquipView.New(GuideModuleName.HundredEquipView)
    self.ratio_view = HundredEquipRatio.New(GuideModuleName.HundredEquipRatio)
    self.award_tip_view = HundredEquipTip.New(GuideModuleName.HundredEquipTip)
    self.award_preview = HundredEquipAwardPreView.New()
    self.drop_times_reward_view = HundredEquipDropTimesRewardView.New()
    self.scene_view = HundredEquipSceneView.New(GuideModuleName.HundredLogicView)

	self:RegisterAllprotocols()
    self:RegisterAllEvents()
end

function HundredEquipWGCtrl:__delete()
	self.view:DeleteMe()
	self.view = nil

	self.data:DeleteMe()
	self.data = nil

    self.ratio_view:DeleteMe()
    self.ratio_view = nil

    self.award_tip_view:DeleteMe()
    self.award_tip_view = nil

    self.award_preview:DeleteMe()
    self.award_preview = nil

    if self.scene_view then
        self.scene_view:DeleteMe()
        self.scene_view = nil
    end

    if self.drop_times_reward_view then
        self.drop_times_reward_view:DeleteMe()
        self.drop_times_reward_view = nil
    end

    self:UnRegisterAllEvents()

	HundredEquipWGCtrl.Instance = nil
end

function HundredEquipWGCtrl:RegisterAllprotocols()
	self:RegisterProtocol(CSHundredfoldDropClientReq)
	self:RegisterProtocol(SCHundredfoldDropTotalInfo, "OnSCHundredfoldDropTotalInfo")
    self:RegisterProtocol(SCHundredfoldDropRealRechargeNumUpdate, "OnSCHundredfoldDropRealRechargeNumUpdate")
    self:RegisterProtocol(SCHundredfoldDropRealRechargeRewardFlagUpdate, "OnSCHundredfoldDropRealRechargeRewardFlagUpdate")
    self:RegisterProtocol(SCHundredfoldDropRmbBuyLevelUpdate, "OnSCHundredfoldDropRmbBuyLevelUpdate")
    self:RegisterProtocol(SCHundredfoldDropRealRechargeBossStatusUpdate, "OnSCHundredfoldDropRealRechargeBossStatusUpdate")
    self:RegisterProtocol(SCHundredfoldDropTargetRewardFlagUpdate, "OnSCHundredfoldDropTargetRewardFlagUpdate")
    self:RegisterProtocol(SCHundredfoldDropExpPerTimesUpdate, "OnSCHundredfoldDropExpPerTimesUpdate")
    self:RegisterProtocol(SCHundredfoldDropTaskUpdate, "OnSCHundredfoldDropTaskUpdate")
    self:RegisterProtocol(SCHundredfoldDropRankInfo, "OnSCHundredfoldDropRankInfo")
    
end

function HundredEquipWGCtrl:RegisterAllEvents()
    -- self.fun_open_event = BindTool.Bind(self.OpenFunEventChange, self)
	-- FunOpen.Instance:NotifyFunOpen(self.fun_open_event)

    self.open_fun_change = GlobalEventSystem:Bind(OpenFunEventType.OPEN_TRIGGER, BindTool.Bind(self.OpenFunEventChange, self))
	self.mainui_open_comlete = self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreateCallBack, self)) --主界面加载完成
end

function HundredEquipWGCtrl:UnRegisterAllEvents()
    -- FunOpen.Instance:UnNotifyFunOpen(self.fun_open_event)
    -- self.fun_open_event = nil

    if self.open_fun_change then
        GlobalEventSystem:UnBind(self.open_fun_change)
        self.open_fun_change = nil
    end

    if self.mainui_open_comlete then
		GlobalEventSystem:UnBind(self.mainui_open_comlete)
		self.mainui_open_comlete = nil
    end
end

function HundredEquipWGCtrl:SendHundredEquipRequest(operate_type, param_1)
 	local protocol = ProtocolPool.Instance:GetProtocol(CSHundredfoldDropClientReq)
    protocol.operate_type = operate_type
    protocol.param_1 = param_1 or 0
 	protocol:EncodeAndSend()
end

--百倍爆率 - 总信息
function HundredEquipWGCtrl:OnSCHundredfoldDropTotalInfo(protocol)
    self.data:SetAllInfo(protocol)
    if self.view:IsOpen() then
        self.view:Flush()
    end

    if self.ratio_view:IsOpen() then
        self.ratio_view:Flush()
    end

	RemindManager.Instance:Fire(RemindName.HundredEquipView)
    RemindManager.Instance:Fire(RemindName.HundredEquipAward)
	MainuiWGCtrl.Instance:FlushView(0, "flush_hundred_num")
end

--百倍爆率 - 真充额度更新
function HundredEquipWGCtrl:OnSCHundredfoldDropRealRechargeNumUpdate(protocol)
    self.data:RealRechargeNumUpdate(protocol)

    if self.view:IsOpen() then
        self.view:Flush()
    end

	RemindManager.Instance:Fire(RemindName.HundredEquipView)
end

--百倍爆率 - 真充奖励领取状态更新
function HundredEquipWGCtrl:OnSCHundredfoldDropRealRechargeRewardFlagUpdate(protocol)
    self.data:RealRechargeRewardFlagUpdate(protocol)

    if self.view:IsOpen() then
        self.view:Flush()
    end

    if self.ratio_view:IsOpen() then
        self.ratio_view:Flush()
    end

	RemindManager.Instance:Fire(RemindName.HundredEquipView)
end

--百倍爆率 - 直购等级更新
function HundredEquipWGCtrl:OnSCHundredfoldDropRmbBuyLevelUpdate(protocol)
    local old_buy_level = self.data:GetRmbBuyLevel()

    self.data:RmbBuyLevelUpdate(protocol)

    local ned_buy_level = self.data:GetRmbBuyLevel()

    if old_buy_level ~= ned_buy_level then
        self:OnGetHundredEquipReward()
    end

    if self.view:IsOpen() then
        self.view:Flush()
    end

    if self.award_tip_view:IsOpen() then
        self.award_tip_view:Flush()
    end

	RemindManager.Instance:Fire(RemindName.HundredEquipView)
    RemindManager.Instance:Fire(RemindName.HundredEquipAward)
	MainuiWGCtrl.Instance:FlushView(0, "flush_hundred_num")
end

function HundredEquipWGCtrl:HundredDropUpLevel()
    if self.view:IsOpen() then
        self.view:Flush(0, "move_tween")
    end
end

--打开恭喜获得.
function HundredEquipWGCtrl:OnGetHundredEquipReward()
    local buy_level = HundredEquipWGData.Instance:GetRmbBuyLevel()
    local cur_cfg = HundredEquipWGData.Instance:GetRmbBuyCfg(buy_level)
	local reward_list = cur_cfg.reward
	if not IsEmptyTable(reward_list) then
		local reward_list = ListIndexFromZeroToOne(reward_list)
		TipWGCtrl.Instance:ShowGetReward(nil, reward_list, nil, nil, nil, true)
	end
end

--百倍爆率 - 真充boss状态更新
function HundredEquipWGCtrl:OnSCHundredfoldDropRealRechargeBossStatusUpdate(protocol)
    self.data:RealRechargeBossStatusUpdate(protocol)

    local boss_list = self.data:GetBossListBySceneId(Scene.Instance:GetSceneId())
	GlobalEventSystem:Fire(OtherEventType.SHOW_TASK_CHANGE, boss_list, true)
end

--百倍爆率 - 爆率达标奖励
function HundredEquipWGCtrl:OnSCHundredfoldDropTargetRewardFlagUpdate(protocol)
    self.data:TargetRewardFlagUpdate(protocol)

    if self.view:IsOpen() then
        self.view:Flush()
    end

	RemindManager.Instance:Fire(RemindName.HundredEquipView)
    RemindManager.Instance:Fire(RemindName.HundredEquipAward)
end

--百倍爆率 - 活跃值更新
function HundredEquipWGCtrl:OnSCHundredfoldDropExpPerTimesUpdate(protocol)
    self.data:ExpPerUpdate(protocol)

    if self.view:IsOpen() then
        self.view:Flush()
    end

	RemindManager.Instance:Fire(RemindName.HundredEquipView)
    RemindManager.Instance:Fire(RemindName.HundredEquipAward)
    MainuiWGCtrl.Instance:FlushView(0, "flush_hundred_num")
end

--百倍爆率 - 任务更新
function HundredEquipWGCtrl:OnSCHundredfoldDropTaskUpdate(protocol)
    self.data:TaskUpdate(protocol)

    if self.view:IsOpen() then
        self.view:Flush()
    end
end

-- 百倍爆装 - 排行榜
function HundredEquipWGCtrl:OnSCHundredfoldDropRankInfo(protocal)
    self.data:UpdateRankInfos(protocal)

    if self.ratio_view:IsOpen() then
        self.ratio_view:FlushRank()
    end
end

function HundredEquipWGCtrl:OpenAwardTip()
    if not self.award_tip_view:IsOpen() then
        self.award_tip_view:Open()
    end
end

function HundredEquipWGCtrl:OpenRatioView()
    if not self.ratio_view:IsOpen() then
        self.ratio_view:Open()
    end
end

function HundredEquipWGCtrl:OpenFunEventChange(check_all, fun_name, is_open)
    if check_all or fun_name == FunName.HundredEquipView then
		self:CheckHundredEquipIsOpen()
    end
end

function HundredEquipWGCtrl:OpenAwardPreView()
    if not self.award_preview:IsOpen() then
        self.award_preview:Open()
    end
end

function HundredEquipWGCtrl:OpenDropTimesRewardView()
    if not self.drop_times_reward_view:IsOpen() then
        self.drop_times_reward_view:Open()
    end
end

function HundredEquipWGCtrl:MainuiOpenCreateCallBack()
    self:CheckHundredEquipIsOpen()
end

function HundredEquipWGCtrl:CheckHundredEquipIsOpen()
    local is_open = FunOpen.Instance:GetFunIsOpened(FunName.HundredEquipView)
    local state = is_open and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE
    ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.HUNDRED_EQUIP, state)
end

function HundredEquipWGCtrl:FlushHundredView()
    if self.view:IsOpen() then
        self.view:Flush()
    end
end

function HundredEquipWGCtrl:CloseHundredView()
    if self.view:IsOpen() then
        self.view:Close()
    end
end

function HundredEquipWGCtrl:OpenHundredLogicView()
    ViewManager.Instance:Open(GuideModuleName.HundredLogicView)
end

function HundredEquipWGCtrl:CloseHundredLogicView()
    ViewManager.Instance:Close(GuideModuleName.HundredLogicView)
end

function HundredEquipWGCtrl:FlushHundredLogicView(cur_wave_num, monster_num)
    self.data:SetFuBenCurNum(cur_wave_num)

    if self.scene_view:IsOpen() then
        self.scene_view:FlushTaskMsgInfo(cur_wave_num, monster_num)
    else
        GlobalTimerQuest:AddDelayTimer(function()
			self:FlushHundredLogicView(cur_wave_num, monster_num)
		end, 0.5)
    end
end

function HundredEquipWGCtrl:GetSceneView()
    return self.scene_view
end