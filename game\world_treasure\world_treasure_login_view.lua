local LO_REWARD_NUM = 5 --奖励数量

function WorldTreasureView:ReleaseCallBack_Login()
	if self.lo_reward_list then
		self.lo_reward_list:DeleteMe()
		self.lo_reward_list = nil
	end

    if self.lo_reward_tweener then
		self.lo_reward_tweener:Kill()
		self.lo_reward_tweener = nil
	end

    if self.lo_dragon_egg_tween then
		self.lo_dragon_egg_tween:Kill()
		self.lo_dragon_egg_tween = nil
	end
end

function WorldTreasureView:LoadIndexCallBack_Login()
    self.login_grade_change = true
    self.slider_old_value = -1

    XUI.AddClickEventListener(self.node_list.lo_btn_receive, BindTool.Bind1(self.OnClickLoBtnReceive, self))
    XUI.AddClickEventListener(self.node_list.lo_cost_item, BindTool.Bind(self.OnClickLoCostItem, self))
    XUI.AddClickEventListener(self.node_list.btn_lo_reward_yulan, BindTool.Bind(self.OnClickLoRewardYulan, self))
    XUI.AddClickEventListener(self.node_list.lo_btn_receive_egg, BindTool.Bind(self.OnClickLoReceiveEgg, self))
    XUI.AddClickEventListener(self.node_list.lo_btn_inject, BindTool.Bind(self.OnClickLoInject, self))

    -- if not self.lo_reward_tweener then
	-- 	RectTransform.SetAnchoredPositionXY(self.node_list["lo_reward_list"].rect, 348, 50)
	-- 	self.lo_reward_tweener = self.node_list["lo_reward_list"].gameObject.transform:DOAnchorPosY(25, 1)
	-- 	self.lo_reward_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	-- 	self.lo_reward_tweener:SetEase(DG.Tweening.Ease.Linear)
	-- end

    if not self.lo_reward_list then
		self.lo_reward_list = AsyncListView.New(ItemCell, self.node_list.lo_reward_list)
		self.lo_reward_list:SetStartZeroIndex(true)
	end

    local desc_cfg = WorldTreasureWGData.Instance:GetClientDesc(TabIndex.tcdb_login_gift)
	if  not IsEmptyTable(desc_cfg) then
		self.node_list.lo_desc.text.text = desc_cfg.rule_desc
	end
end

function WorldTreasureView:LoadLoImg()
	local bundle, asset = ResPath.GetWorldTreasureRawImages("jsrh_bg_2")
	self.node_list.lo_bg.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.lo_bg.raw_image:SetNativeSize()
	end)

    bundle, asset = ResPath.GetWorldTreasureRawImages("xtyg_bt_1")
	self.node_list.img_title_login.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.img_title_login.raw_image:SetNativeSize()
	end)

    bundle, asset = ResPath.GetWorldTreasureRawImages("xtyg_js_1")
	self.node_list.lo_img_desc.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.lo_img_desc.raw_image:SetNativeSize()
	end)

    bundle, asset = ResPath.GetWorldTreasureRawImages("xtyg_xx_hs")
	self.node_list.dragon_egg_img.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.dragon_egg_img.raw_image:SetNativeSize()
	end)

    bundle, asset = ResPath.GetWorldTreasureRawImages("xtyg_jdd_1")
	self.node_list.inject_bg.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.inject_bg.raw_image:SetNativeSize()
	end)

    bundle, asset = ResPath.GetWorldTreasureRawImages("xtyg_btd_1")
	self.node_list.lo_day_desc_bg.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.lo_day_desc_bg.raw_image:SetNativeSize()
	end)

    local cur_grade = WorldTreasureWGData.Instance:GetTreasureCurGrade()
    self.node_list.btn_lo_reward_yulan.text.color = StrToColor(Language.WorldTreasure.GradeColorList1[cur_grade])
    self.node_list.lo_talent_desc.text.color = StrToColor(Language.WorldTreasure.GradeColorList1[cur_grade])
    self.node_list.progress_text.text.color = StrToColor(Language.WorldTreasure.GradeColorList1[cur_grade])
    self.node_list.lo_cost_num.text.color = StrToColor(Language.WorldTreasure.GradeColorList1[cur_grade])
    self.node_list.max_text.text.color = StrToColor(Language.WorldTreasure.GradeColorList1[cur_grade])

    local name = WorldTreasureWGData.Instance:GetCurGradeName()
    self.node_list.lo_btn_receive_egg_text.text.text = string.format(Language.WorldTreasure.LoginTxt13, name)
end

function WorldTreasureView:ShowIndexCallBack_Login()

end

function WorldTreasureView:OnFlush_Login(param_t, index)
    if self.login_grade_change then
        self.login_grade_change = false
        self:LoadLoImg()
    end
    self:UpdateLoginView()
end

function WorldTreasureView:UpdateLoginView()
    local day_data = WorldTreasureWGData.Instance:GetLoginDayData()
    local day_list = day_data.day_list
    local login_day_num = day_data.activity_day_index
    local is_has_tomorrow = login_day_num < #day_list
    local data_list = {}

    if not day_list[login_day_num] then
        return
    end

    if day_list[login_day_num].state == TREASURE_OPERATE_TYPE.HAS_LINGQU and is_has_tomorrow then
        data_list = day_list[login_day_num + 1].reward_item
        self.node_list.lo_day_desc.text.text = Language.WorldTreasure.LoginTxt7
    else
        data_list = day_list[login_day_num].reward_item
        self.node_list.lo_day_desc.text.text = Language.WorldTreasure.LoginTxt6
    end

    if not IsEmptyTable(data_list) then
        self.lo_reward_list:SetRefreshCallback(function(item_cell, cell_index)
            if item_cell then
                item_cell:SetLingQuVisible(day_list[login_day_num].state == TREASURE_OPERATE_TYPE.HAS_LINGQU and not is_has_tomorrow)
                item_cell:SetRedPointEff(day_list[login_day_num].state == TREASURE_OPERATE_TYPE.CAN_LINGQU)
            end
        end)
        self.lo_reward_list:SetDataList(data_list)
    end

    self.node_list.lo_btn_receive:SetActive(day_list[login_day_num].state == TREASURE_OPERATE_TYPE.CAN_LINGQU)
    self.node_list.lo_btn_receive_remind:SetActive(day_list[login_day_num].state == TREASURE_OPERATE_TYPE.CAN_LINGQU)

    local cfg = WorldTreasureWGData.Instance:GetCurGradeCfg()
    local cost_itemid = cfg.consume_item_id
    local have_num = ItemWGData.Instance:GetItemNumInBagById(cost_itemid)
    --local item_num_color = have_num >= 1 and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
    local str = string.format("%s/%s", have_num, have_num >= 1 and have_num or 1)

    local bundle, asset = ResPath.GetItem(cost_itemid)
    self.node_list.lo_cost_item.image:LoadSpriteAsync(bundle, asset, function ()
        self.node_list.lo_cost_item.image:SetNativeSize()
    end)

    self.node_list["lo_cost_num"].text.text = str

    local is_inject_remind = WorldTreasureWGData.Instance:GetDragonEggInjectIsRemind()
    local is_receive_egg_remind = WorldTreasureWGData.Instance:GetReceiveEggIsRemind()
    self.node_list.lo_btn_inject_remind:SetActive(is_inject_remind)
    self.node_list.lo_btn_receive_egg_remind:SetActive(is_receive_egg_remind)

    local cur_cfg = WorldTreasureWGData.Instance:GetDragonEggCurLevelCfg()
    local cur_exp = WorldTreasureWGData.Instance:GetDragonEggExp()
    local is_max = WorldTreasureWGData.Instance:GetDragonEggIsMaxLevel()
    local slider_value = is_max and 1 or cur_exp / cur_cfg.exp

    self.node_list["progress_text"]:SetActive(not is_max)
    self.node_list["max_text"]:SetActive(is_max)
    self.node_list["progress_text"].text.text = string.format(Language.WorldTreasure.TotalRechargeBili, cur_exp, cur_cfg.exp)
    if self.slider_old_value >= 0 and slider_value > self.node_list.de_slider.slider.value then
        self.slider_old_value = slider_value
        self.node_list.de_slider.slider:DOValue(slider_value, 0.2)
    else
        self.node_list.de_slider.slider.value = slider_value
        self.slider_old_value = slider_value
    end

    bundle, asset = ResPath.GetCommon("a3_ty_pz" .. cur_cfg.level)
    self.node_list.lo_level_icon.image:LoadSprite(bundle, asset, function ()
        self.node_list.lo_level_icon.image:SetNativeSize()
    end)

    local reward_flag = WorldTreasureWGData.Instance:GetDragonEggRewardFlag()
    self.node_list.lo_btn_receive_egg:SetActive(reward_flag ~= 1)
    self.node_list.lo_btn_inject:SetActive(reward_flag ~= 1)
    self.node_list.lo_reward_get_flag:SetActive(reward_flag == 1)
    self.node_list.lo_cost:SetActive(reward_flag ~= 1)
    -- 图片资源
	-- local grade_cfg = WorldTreasureWGData.Instance:GetCurGradeCfg()
	-- if not IsEmptyTable(grade_cfg) then
	-- 	self:SetImageRes("img_title_login", grade_cfg.title_image)
	-- else
    --     local grade = WorldTreasureWGData.Instance:GetTreasureCurGrade()
	-- 	print_error("未找到对应档位配置,grade:" .. grade)
	-- end
    if self.lo_dragon_egg_tween then
        self.lo_dragon_egg_tween:Restart()
    else
        if self.lo_dragon_egg_tween then
            self.lo_dragon_egg_tween:Kill()
            self.lo_dragon_egg_tween = nil
        end

        self.lo_dragon_egg_tween = DG.Tweening.DOTween.Sequence()
        WorldTreasureView.ShakeAnimi(self.node_list.dragon_egg_img.transform, self.lo_dragon_egg_tween)
    end
end

function WorldTreasureView.ShakeAnimi(trans, sequence, interval_time)
	interval_time = interval_time or 2
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 0), 1, DG.Tweening.RotateMode.Fast))
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 8), 0.1, DG.Tweening.RotateMode.Fast))
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, -15), 0.1, DG.Tweening.RotateMode.Fast))
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 15), 0.1, DG.Tweening.RotateMode.Fast))
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, -14), 0.1, DG.Tweening.RotateMode.Fast))
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 14), 0.1, DG.Tweening.RotateMode.Fast))
    sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 0), 0.1, DG.Tweening.RotateMode.Fast))
    sequence:AppendInterval(interval_time)
    sequence:SetEase(DG.Tweening.Ease.Linear)
    sequence:SetLoops(-1)
end

-- 升级特效
function WorldTreasureView:PlayUpLevelEffect()
    TipWGCtrl.Instance:ShowEffect({
        effect_type = UIEffectName.s_shengji,
        is_success = true,
        pos = Vector2(0, 0),
        parent_node = self.node_list["lo_effect_pos"]
    })
end

function WorldTreasureView:OnClickLoBtnReceive()
    local day_data = WorldTreasureWGData.Instance:GetLoginDayData()
    WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_LOGIN_GIFT_COMMON_REWARD, day_data.activity_day_index)
end

function WorldTreasureView:OnClickLoCostItem()
    local cfg = WorldTreasureWGData.Instance:GetCurGradeCfg()
	TipWGCtrl.Instance:OpenItem({item_id = cfg.consume_item_id})
end

function WorldTreasureView:OnClickLoRewardYulan()
    local cfg = WorldTreasureWGData.Instance:GetDragonEggCfg()
	if cfg then
        local name = WorldTreasureWGData.Instance:GetCurGradeName()
		local title_reward_item_data = {}
		for index, value in ipairs(cfg) do
			local data = ListIndexFromZeroToOne(value.reward_item)
			title_reward_item_data[index] = {}
            local quality_text = Language.WorldTreasure.QualityList[value.level]
			title_reward_item_data[index].title_text = string.format(Language.WorldTreasure.LoginTxt10, name, quality_text)
			title_reward_item_data[index].reward_item_list = data
			title_reward_item_data[index].sort = value.level
		end
        SortTools.SortDesc(title_reward_item_data, "sort")

		local data_list =
		{
			view_type = RewardShowViewType.Title,
			title_reward_item_data = title_reward_item_data
		}
		RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
	end
end

function WorldTreasureView:OnClickLoReceiveEgg()
    local reward_flag = WorldTreasureWGData.Instance:GetDragonEggRewardFlag()
    local is_max = WorldTreasureWGData.Instance:GetDragonEggIsMaxLevel()
    if reward_flag == 1 then
        local name = WorldTreasureWGData.Instance:GetCurGradeName()
        TipWGCtrl.Instance:ShowSystemMsg(Language.WorldTreasure.LoginTxt11, name)
    elseif is_max then
        WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_FETCH_DRAGON_EGG_REWARD)
    else
        WorldTreasureWGCtrl.Instance:OpenReceiveEggView()
    end
end

function WorldTreasureView:OnClickLoInject()
    local cfg = WorldTreasureWGData.Instance:GetCurGradeCfg()
    local cost_itemid = cfg.consume_item_id
    local have_num = ItemWGData.Instance:GetItemNumInBagById(cost_itemid)
    local is_max = WorldTreasureWGData.Instance:GetDragonEggIsMaxLevel()
    if is_max then
        TipWGCtrl.Instance:ShowSystemMsg(Language.WorldTreasure.LoginTxt9, cfg.grade_name)
        return
    elseif have_num <= 0 then
        TipWGCtrl.Instance:ShowSystemMsg(Language.WorldTreasure.LoginTxt8)
        return
    end

    WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_UPLEVEL_DRAGON_EGG)
end