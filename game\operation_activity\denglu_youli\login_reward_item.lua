LoginRewardState = 
{
	BKL = 0,
	KLQ = 1,
	YLQ = 2,
}

LoginRewardItem = LoginRewardItem or BaseClass(BaseRender)
function LoginRewardItem:__init()
	self.num_str = {"一","二","三","四","五","六","七","八","九","十","十一","十二"}
end

function LoginRewardItem:__delete()
	self.btn_yilingqu = nil
	self.select_bg = nil
	self.day_num = nil

    self.vip_tip = nil
    if self.only_normal_list then
		for k,v in pairs(self.only_normal_list) do
			v:DeleteMe()
		end
	end
	self.only_normal_list = nil

	if self.normal_list then
		for k,v in pairs(self.normal_list) do
			v:DeleteMe()
		end
	end
	self.normal_list = nil

	if self.special_list then
		for k,v in pairs(self.special_list) do
			v:DeleteMe()
		end
	end
	self.special_list = nil
end

function LoginRewardItem:LoadCallBack()
    self.only_normal_list = {}
	self.normal_list = {}
    self.special_list = {}
    XUI.AddClickEventListener(self.node_list.btn_lingqu, BindTool.Bind(self.OnBtnReceiveClickHandler,self))
    self:ShowLoginRewardViewCfg()
end

function LoginRewardItem:ShowLoginRewardViewCfg()
	-- local view_cfg = LoginRewardWGData.Instance:GetViewCfg()
	-- if nil == view_cfg or IsEmptyTable(view_cfg) then
	-- 	return
	-- end

	local root_bg_name = OperationActivityWGData.Instance:GetCurTypeImgName("a3_xskh_di_2da")
	local bundle, asset = ResPath.GetOperationActivityImagePath(root_bg_name)
	self.node_list["root_bg"].image:LoadSprite(bundle, asset, function()
        self.node_list["root_bg"].image:SetNativeSize()
    end)

	-- local index = OperationActivityWGData.Instance:GetCurTypeId()
	-- local btn_lingqu_name = index == 1 and "a3_ty_hd_btn_2" or "a3_ty_hd_btn_4"
	-- local bundle1, asset1 = ResPath.GetCommonButton(btn_lingqu_name)
	-- self.node_list["btn_lingqu"].image:LoadSprite(bundle1, asset1)
	--self.node_list.root_bg.image:LoadSprite(ResPath.GetOperationActivityImagePath(view_cfg.list_item_bg))
	-- local bundle_name1,asset_name1 = ResPath.GetOperationActivityImagePath(view_cfg.day_line)
	-- self.node_list["day_line"].image:LoadSprite(bundle_name1,asset_name1,function ()
	-- 	self.node_list["day_line"].image:SetNativeSize()
	-- end)
	--[[
	OperationActivityChangeToQualityText(self.node_list["day_num"], view_cfg.day_num, false, true)

	local bundle_name2, asset_name2= ResPath.GetOperationActivityImagePath(view_cfg.normal_lh)
			self.node_list["normal_bg"].image:LoadSprite(bundle_name2,asset_name2,function ()
			self.node_list["normal_bg"].image:SetNativeSize()
		end)
	local bundle_name3, asset_name3 = ResPath.GetOperationActivityImagePath(view_cfg.ylq_lh)
			self.node_list["cur_reward_bg"].image:LoadSprite(bundle_name3,asset_name3,function ()
			self.node_list["cur_reward_bg"].image:SetNativeSize()
		end)
	local bundle_name4, asset_name4 = ResPath.GetOperationActivityImagePath(view_cfg.select_lh)
			self.node_list["select_bg"].image:LoadSprite(bundle_name4,asset_name4,function ()
			self.node_list["select_bg"].image:SetNativeSize()
		end)
	--]]
end

function LoginRewardItem:OnFlush()
    local data = self.data
    if not data then
        return
    end
	self.btn_yilingqu = self.node_list.btn_yilingqu
	self.select_bg = self.node_list.select_bg
	self.day_num = self.node_list.day_num
	--self.vip_tip = self.node_list.vip_tip
	
	local view_cfg = LoginRewardWGData.Instance:GetViewCfg()
	if nil == view_cfg or IsEmptyTable(view_cfg) then
		return
	end
	-- local day_num
	-- day_num = string.format(Language.OperationActivity.LoginStr1, self.num_str[data.index])
	-- day_num = string.format("<color=%s>%s</color>", view_cfg.day_num, day_num)
	self.day_num.text.text = string.format(Language.OperationActivity.LoginRewardTitle, data.index)
	-- local cur_vip_level = VipWGData.Instance:GetRoleVipLevel()
	-- if data.vip_level > 0 then
	-- 	self.vip_tip:SetActive(data.common_gift_state == LoginRewardState.YLQ and cur_vip_level < data.vip_level)
	-- 	self.vip_tip.text.text = string.format(Language.OperationActivity.LoginStr2, data.vip_level)
	-- else
	-- 	--self.btn_yilingqu:SetActive(data.common_gift_state == LoginRewardState.YLQ)
	-- end

    self:ShowLoginReward(self.index)

	local cur_login_day = LoginRewardWGData.Instance:GetLoginDyaIndex()
    self.node_list.redpoint:SetActive((data.special_gift_state == LoginRewardState.KLQ or data.common_gift_state == LoginRewardState.KLQ))
    self:FlushLoginRewardReceiveBtnState()
end

-- 显示登录奖励
function LoginRewardItem:ShowLoginReward(day)
	local cfg = LoginRewardWGData.Instance:GetLoginRewardCfgByDay(day)
	if not cfg then
		day = LoginRewardWGData.Instance:GetLoginDyaIndex()
		cfg = LoginRewardWGData.Instance:GetLoginRewardCfgByDay(day)
	end

	if cfg ~= nil then
		self.select_day = day
		self.login_reward_btn_state = OPERATION_LOGIN_REWARD_BTN_STATE.HAS_NORMAL_REWARD

		local login_data = LoginRewardWGData.Instance:GetLoginDayData()
		if IsEmptyTable(login_data) then
			return
		end
		local day_info = login_data.day_list[self.select_day]
		if not day_info then
			return
		end

		local normal_group = nil
		local special_group = nil
		if cfg.vip_lv ~= nil and cfg.vip_lv > 0 then
			self.node_list["reward_group"]:SetActive(false)
			self.node_list["special"]:SetActive(true)

			normal_group = self.node_list["normal_group"]
			special_group = self.node_list["special_group"]

			self:SetItemData(self.normal_list, cfg.reward_item, normal_group, day_info.common_gift_state)
			self:SetItemData(self.special_list, cfg.special_reward_item, special_group, day_info.special_gift_state)
			--self:SetRewardBgWidth(true,#cfg.reward_item+1, #cfg.special_reward_item + 1)

			self.node_list.vip_label.text.text = string.format(Language.OperationActivity.Vipstr3, day_info.vip_level)
		else
			self.node_list["reward_group"]:SetActive(true)
			self.node_list["special"]:SetActive(false)
            normal_group = self.node_list["reward_group"]
			self:SetItemData(self.only_normal_list, cfg.reward_item, normal_group, day_info.common_gift_state)

			--self:SetRewardBgWidth(false)
		end
	end
end

function LoginRewardItem:SetItemData(reward_list,data,layout_group, state)
	local login_data = LoginRewardWGData.Instance:GetLoginDayData()
	if not login_data then
		return
	end

	local data1 = login_data.day_list[self.select_day]
	local is_can_lq = data1.common_gift_state == ActivityRewardState.KLQ
	local is_ylq = data1.common_gift_state == ActivityRewardState.YLQ

	local reward_count = #data + 1 --表格数据索引从0开始，直接取长度会-1
	local offset = reward_count - #reward_list
	if offset > 0 then
		for i=1, offset do
			local cell = ItemCell.New(layout_group)
			table.insert(reward_list, cell)
		end
	end
    layout_group.rect.sizeDelta = Vector2(#reward_list * COMMON_CONSTS.ItemCellSize + (#reward_list -1) * 10, 84)
    local temp_list = OperationActivityWGData.Instance:SortDataByItemColor(data)
	for i=1, #reward_list do
		if i <= reward_count then
			--data[i-1].is_ylq = state == ActivityRewardState.YLQ --屏蔽掉勾
			reward_list[i]:SetActive(true)
			reward_list[i]:SetData(temp_list[i])
			reward_list[i]:SetRedPointEff(is_can_lq)
			reward_list[i]:SetLingQuVisible(is_ylq)
		else
			reward_list[i]:SetActive(false)
		end
	end
end


function LoginRewardItem:OnBtnItemClickHnadler()
	ViewManager.Instance:FlushView(GuideModuleName.OperationActivityView, 
		TabIndex.operation_act_login_reward, "RefreshLoginReward", {self.data.index})
end


function LoginRewardItem:IsGetReward(is_getreward, index)
	if is_getreward then 
		ViewManager.Instance:FlushView(GuideModuleName.OperationActivityView, 
			TabIndex.operation_act_login_reward, "RefreshLoginReward", {index})
	else	
		self.select_bg:SetActive(is_getreward)
	end
end


--更新领取按钮状态
function LoginRewardItem:FlushLoginRewardReceiveBtnState()
	if self.select_day == nil then
		return
	end
	local cur_vip_level = VipWGData.Instance:GetRoleVipLevel()
	local cur_login_day = LoginRewardWGData.Instance:GetLoginDyaIndex()
	self.node_list.redpoint:SetActive(false)
	self.node_list.btn_yilingqu:SetActive(false)
	self.node_list.btn_lingqu:SetActive(true)

	if cur_login_day < self.select_day then
		--self.node_list.login_reward_label.text.text = string.format(Language.OperationActivity.Vipstr1, self.select_day)
		self.login_reward_btn_state = OPERATION_LOGIN_REWARD_BTN_STATE.WEIDA_TIANSHU
		XUI.SetGraphicGrey(self.node_list.btn_lingqu, true)
	else
		XUI.SetGraphicGrey(self.node_list.btn_lingqu, false)
		local login_data = LoginRewardWGData.Instance:GetLoginDayData()
		if not login_data then
			return
		end

		local data = login_data.day_list[self.select_day]
		if not data then
			return
		end

		if data.vip_level > 0 then
			if data.common_gift_state == ActivityRewardState.YLQ then
				if cur_vip_level < data.vip_level then
					--self.node_list.login_reward_label.text.text = string.format(Language.OperationActivity.Vipstr2, data.vip_level)
					self.login_reward_btn_state = OPERATION_LOGIN_REWARD_BTN_STATE.VIP_REWARD
				elseif data.special_gift_state == ActivityRewardState.YLQ then
					-- self.node_list.login_reward_label.text.text = Language.TianShenRoad.BtnStr1
					self.node_list.btn_yilingqu:SetActive(true)
					self.node_list.btn_lingqu:SetActive(false)
					self.login_reward_btn_state = OPERATION_LOGIN_REWARD_BTN_STATE.YILINGQU
					XUI.SetGraphicGrey(self.node_list.btn_lingqu, true)
				else
					--self.node_list.login_reward_label.text.text = Language.OperationActivity.BtnStr2
					self.login_reward_btn_state = OPERATION_LOGIN_REWARD_BTN_STATE.HAS_SPECIAL_REWARD
					self.node_list.redpoint:SetActive(true)
				end
			else
				if cur_vip_level >= data.vip_level then
					self.login_reward_btn_state = OPERATION_LOGIN_REWARD_BTN_STATE.HAS_ALL_REWARD
				end

				--self.node_list.login_reward_label.text.text = Language.OperationActivity.BtnStr2
				self.node_list.redpoint:SetActive(true)
			end
		else
			if data.common_gift_state == ActivityRewardState.YLQ then
				self.node_list.btn_yilingqu:SetActive(true)
				self.node_list.btn_lingqu:SetActive(false)
			else
				--self.node_list.login_reward_label.text.text = Language.OperationActivity.BtnStr2
				self.node_list.redpoint:SetActive(true)
			end
		end
	end
end



function LoginRewardItem:OnBtnReceiveClickHandler()
	if self.login_reward_btn_state == OPERATION_LOGIN_REWARD_BTN_STATE.YILINGQU then
		TipWGCtrl.Instance:ShowSystemMsg(Language.OperationActivity.BtnTip1)
	elseif self.login_reward_btn_state == OPERATION_LOGIN_REWARD_BTN_STATE.WEIDA_TIANSHU then
		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.OperationActivity.BtnTip2, self.select_day))
	elseif self.login_reward_btn_state == OPERATION_LOGIN_REWARD_BTN_STATE.VIP_REWARD then
		FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge, "recharge_vip")
	else
		if self.login_reward_btn_state == OPERATION_LOGIN_REWARD_BTN_STATE.HAS_ALL_REWARD then
			LoginRewardWGCtrl.Instance:SendActivityRewardOp(OPERATION_LOGIN_GIFT_OP_TYPE.COMMON_REWARD,self.select_day)
			LoginRewardWGCtrl.Instance:SendActivityRewardOp(OPERATION_LOGIN_GIFT_OP_TYPE.SPECIAL_REWARD,self.select_day)
		elseif self.login_reward_btn_state == OPERATION_LOGIN_REWARD_BTN_STATE.HAS_NORMAL_REWARD then
			LoginRewardWGCtrl.Instance:SendActivityRewardOp(OPERATION_LOGIN_GIFT_OP_TYPE.COMMON_REWARD,self.select_day)
			self.is_login_day_show = true
		elseif self.login_reward_btn_state == OPERATION_LOGIN_REWARD_BTN_STATE.HAS_SPECIAL_REWARD then
			LoginRewardWGCtrl.Instance:SendActivityRewardOp(OPERATION_LOGIN_GIFT_OP_TYPE.SPECIAL_REWARD,self.select_day)
		end
	end
end

function LoginRewardItem:GetItemState()
	return self.data.special_gift_state
end

-----------------------------------------------------------------------------------
OperationLoginDayRender = OperationLoginDayRender or BaseClass(BaseRender)
function OperationLoginDayRender:__init()
	
end

function OperationLoginDayRender:LoadCallBack()
	


end

function OperationLoginDayRender:__delete()
	self.btn_yilingqu = nil
	self.select_bg = nil
	self.day_num = nil
end

function OperationLoginDayRender:OnFlush()
	if not self.data then
		return
	end
	self.day_num.text.text = string.format(Language.TianShenRoad.LoginStr1, self.num_str[self.data.index])
	self.btn_yilingqu:SetActive(self.data.state > 0)
end

function OperationLoginDayRender:OnSelectChange(is_select)
	if nil ~= self.select_bg then
		self.select_bg:SetActive(is_select)
	end
end