XianyuTrunTableWGData = XianyuTrunTableWGData or BaseClass()

XianyuTrunTableWGData.DrawMode = {
	One = 0,
	Ten = 1
}

function XianyuTrunTableWGData:__init()
	if XianyuTrunTableWGData.Instance then
		error("[XianyuTrunTableWGData] Attempt to create singleton twice!")
		return
	end
	XianyuTrunTableWGData.Instance = self
	RemindManager.Instance:Register(RemindName.XianyuTrunTable, BindTool.Bind(self.GetXianyuTrunTableRed, self))
	self.all_cfg = ConfigManager.Instance:GetAutoConfig("cross_gold_zhuanpan_auto")
	self.rewared_cfg = ListToMap(self.all_cfg.reward,"reward_id")
	self.draw_mode_cfg = self.all_cfg.draw_mode
	self.other_cfg = self.all_cfg.other[1]
	self.jump_yuncheng_ani = false

	self:SetAllDrawCfg()

	self.notify_flush = BindTool.Bind(self.CheckActiveChange,self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.notify_flush)

	self.first_login_flush_remind_flag = false

	self.person_record_list = {}
	self.world_record_list = {}
	self.result_reward_list = {}

	self.pass_day_event = GlobalEventSystem:Bind(OtherEventType.PASS_DAY2, BindTool.Bind1(self.DayChangeOrOpen, self))
end

function XianyuTrunTableWGData:DayChangeOrOpen()
	self:SetAllDrawCfg()
end


function XianyuTrunTableWGData:__delete()
	XianyuTrunTableWGData.Instance = nil

	if self.notify_flush then
   		ActivityWGData.Instance:UnNotifyActChangeCallback(self.notify_flush)
    	self.notify_flush = nil
    end

	if self.remind_time_quest then
		GlobalTimerQuest:CancelQuest(self.remind_time_quest)
		self.remind_time_quest = nil
	end

	if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end

	self.first_login_flush_remind_flag = nil

	GlobalEventSystem:UnBind(self.pass_day_event)
end

--设置根据开服天数的抽奖数据
function XianyuTrunTableWGData:SetAllDrawCfg()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	self.cur_open_day_draw_cfg = {}
	local temp_day = nil
	local jiangchi_index = self.jiangchi_index or 0
	for k,v in pairs(self.all_cfg.draw) do
		local cfg_open_day = tonumber(v.open_day)
		if (not temp_day or temp_day == cfg_open_day) and open_day <= cfg_open_day and jiangchi_index == v.jiangchi_index then
			temp_day = cfg_open_day
			table.insert(self.cur_open_day_draw_cfg,v)
		end
	end
end

function XianyuTrunTableWGData:GetRewardCfgById(reward_id)
	return self.rewared_cfg[reward_id]
end

function XianyuTrunTableWGData:GetAllDrawCfg()
	return self.cur_open_day_draw_cfg
end

function XianyuTrunTableWGData:GetDrawModeCfg(draw_type)
	for k,v in pairs(self.draw_mode_cfg) do
		if v.draw_type == draw_type then
			return v
		end
	end
end

function XianyuTrunTableWGData:GetOtherCfg()
	return self.other_cfg
end

function XianyuTrunTableWGData:CheckActiveChange(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.CROSS_CHANNEL_ACTIVITY_TYPE_GOLD_ZHUANPAN and status == ACTIVITY_STATUS.OPEN then
		-- self:CheckNeedReqInfo()
		RemindManager.Instance:Fire(RemindName.XianyuTrunTable)
	end
end

function XianyuTrunTableWGData:CheckNeedReqInfo()
	if not self.task_finish_flag or not self.draw_times_fetch_flag or not self.choose_reward_level then
		local open_type = CS_CROSS_CHANNEL_ACTIVITY_GOLD_ZHUANPAN_OP_TYPE.CS_CROSS_CHANNEL_ACTIVITY_GOLD_ZHUANPAN_OP_TYPE_INFO
		XianyuTrunTableWGCtrl.Instance:SendYunChengRollReq(open_type)
		return true
	end
	return false
end

function XianyuTrunTableWGData:GetJumpAni()
	return self.jump_yuncheng_ani or false
end

function XianyuTrunTableWGData:SetJumpAni()
	self.jump_yuncheng_ani = not self.jump_yuncheng_ani
end

--是否跳过动画
function XianyuTrunTableWGData:GetSkipAnim()
    return self.is_skip_comic
end

function XianyuTrunTableWGData:GetXianyuTrunTableRed()
	local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.CROSS_CHANNEL_ACTIVITY_TYPE_GOLD_ZHUANPAN)
	if not is_open then
		return 0
	end
	if not self.first_login_flush_remind_flag then
		return 1
	else
		return 0
	end
end

function XianyuTrunTableWGData:SetFirstLoginFlushRemind(flag)
	self.first_login_flush_remind_flag = flag
	RemindManager.Instance:Fire(RemindName.XianyuTrunTable)
end

function XianyuTrunTableWGData:SetSCCrossChannelActGoldZhuanpanPersonInfo(protocol)
	self.person_record_list = protocol.record_list
end

function XianyuTrunTableWGData:SetSCCrossChannelActGoldZhuanpanWorldInfo(protocol)
	self.world_record_list = protocol.record_list
	table.sort(self.world_record_list, function (a,b)
		local get_gold_a = a.get_gold and a.get_gold > 0 and a.get_gold or 0
		local get_gold_b = b.get_gold and b.get_gold > 0 and b.get_gold or 0
		local cfg_a = self:GetRewardCfgById(a.reward_id)
		local cfg_b = self:GetRewardCfgById(b.reward_id)
		local show_a = cfg_a.show > 0 and cfg_a.show or 9999
		local show_b = cfg_b.show > 0 and cfg_b.show or 9999
		local order_a = 100000
		local order_b = 100000
		if show_a < show_b then
			order_a = order_a + 10000
		elseif show_a > show_b then
			order_b = order_b + 10000
		end
		if get_gold_a > get_gold_b then
			order_a = order_a + 1000
		elseif get_gold_a < get_gold_b then
			order_b = order_b + 1000
		end
		if a.timestamp > b.timestamp then
			order_a = order_a + 100
		elseif a.timestamp < b.timestamp then
			order_b = order_b + 100
		end

		return order_a > order_b
	end)
end

function XianyuTrunTableWGData:SetSCCrossChannelActGoldZhuanpanJiangchi(protocol)
	self.jiangchi_gold = protocol.jiangchi_gold
	local need_change = false
	if not self.jiangchi_index or self.jiangchi_index ~= protocol.jiangchi_index then
		need_change = true
	end
	self.jiangchi_index = protocol.jiangchi_index
	if need_change then 
		self:SetAllDrawCfg()
	end
end

function XianyuTrunTableWGData:SetSCCrossChannelActGoldZhuanpanResult(protocol)
	self.result_reward_list = protocol.reward_list
	local get_reward = self.result_reward_list[1]
	if IsEmptyTable(get_reward) then 
		print_error(">>>>>>>>>> 服务端下发的奖励为空!!!")
		return
	end 
	local all_draw_cfg = self:GetAllDrawCfg()
	self.result_item = get_reward
	self.roll_index = -1
	for k,v in pairs(all_draw_cfg) do
		if tonumber(v.reward_id) == tonumber(get_reward.reward_id) then
			self.roll_index = k
			break
		end
	end
end

function XianyuTrunTableWGData:GetResultRewardList()
	return self.result_reward_list
end

function XianyuTrunTableWGData:CaleResultRewardList()
	local reward_list = {}
	for k,v in pairs(self.result_reward_list) do
		local data = {}
		data.cfg = self:GetRewardCfgById(v.reward_id)
		data.get_gold = v.get_gold
		table.insert(reward_list,data)
	end
	return reward_list
end

function XianyuTrunTableWGData:GetRollResult()
	return self.result_item,self.roll_index
end

function XianyuTrunTableWGData:GetWorldRecord()
	return self.world_record_list or {}
end

function XianyuTrunTableWGData:GetPersonalRecord()
	return self.person_record_list or {}
end

function XianyuTrunTableWGData:GetJingchiGlod()
	return self.jiangchi_gold or 0
end

--是否跳过动画对应的延时
function XianyuTrunTableWGData:GetDelayTime()
    --是否跳过动画
    if self:GetJumpAni() then
        return 0
    else
        return 5
    end
end

function XianyuTrunTableWGData:IsAvalibaleTime()
	return ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.CROSS_CHANNEL_ACTIVITY_TYPE_GOLD_ZHUANPAN)
end

function XianyuTrunTableWGData:SetCurRollDrawType(draw_type)
	self.cur_roll_draw_type = draw_type
end

function XianyuTrunTableWGData:GetCurRollDrawType()
	return self.cur_roll_draw_type or XianyuTrunTableWGData.DrawMode.One
end

function XianyuTrunTableWGData:SetCurIsTurning(flag)
	self.cur_is_turning = flag
end

function XianyuTrunTableWGData:GetCurIsTurning()
	return self.cur_is_turning or false
end


