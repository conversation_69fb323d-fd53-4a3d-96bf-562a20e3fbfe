ArenaTianTiRankView = ArenaTianTiRankView or BaseClass(SafeBaseView)

function ArenaTianTiRankView:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
    self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "arena_tianti_rank")
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_top_panel")
    self:SetMaskBg()
end

function ArenaTianTiRankView:ReleaseCallBack()
    if self.scroll_award_list then
        self.scroll_award_list:DeleteMe()
        self.scroll_award_list = nil
    end

    if self.role_display then
        for k_1, v_1 in pairs(self.role_display) do
            v_1:DeleteMe()
        end
        self.role_display = nil
    end
end

function ArenaTianTiRankView:OpenCallBack()
	ArenaTiantiWGCtrl.Instance:SendChallengeField(CROSS_CHALLENGE_RANK_LIST_TYPE.CHALLENGE_RANK_LIST_TYPE_INFO)
end

function ArenaTianTiRankView:LoadCallBack()
    self.role_display = {}
    XUI.AddClickEventListener(self.node_list.btn_rank_award, BindTool.Bind(self.ClickRankAwardBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_my_zan, BindTool.Bind(self.ClickZanBtn, self))

    self.scroll_award_list = AsyncListView.New(ArenaTianTiRankAwardRender, self.node_list.scroll_award_list)

    self.node_list.title_view_name.text.text = Language.Field1v1.TianTiTxt9
	if self.node_list.RawImage_tongyong then
		local bundle, asset = ResPath.GetRawImagesPNG("a3_jjc_ttzb_bj")
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
end

function ArenaTianTiRankView:OnFlush()
    local play_rank_list = ArenaTianTiWGData.Instance:GetChallengeRankListInfo()
    self.scroll_award_list:SetDataList(play_rank_list)

    for i = 1, 3, 1 do
        if not self.role_display[i] then
            self.role_display[i] = ArenaTianTiRankRoleRender.New(self.node_list["role_display" .. i], self)
        end

        self.role_display[i]:SetData(play_rank_list[i])
    end

    local my_rank_data = ArenaTianTiWGData.Instance:GetRoleChallengeRankListInfo()
    self.node_list.text_rank_my.text.text = my_rank_data.rank_pos + 1
    if my_rank_data.rank_pos < 3 then
        self.node_list.image_rank_my.image:LoadSprite(ResPath.GetCommonImages("a3_ty_panking_" .. my_rank_data.rank_pos + 1))
    end
    self.node_list.image_rank_my:SetActive(my_rank_data.rank_pos < 3)
	self.node_list.text_rank_my:SetActive(my_rank_data.rank_pos >= 3)

    self.node_list.text_name_my.text.text = my_rank_data.role_name
    self.node_list.text_score_my.text.text = my_rank_data.score
	self.node_list.text_my_zan_num.text.text = my_rank_data.have_like_count
end

function ArenaTianTiRankView:ClickRankAwardBtn()
    ArenaTiantiWGCtrl.Instance:OpenRankRewardTip()
end

function ArenaTianTiRankView:ClickZanBtn()
	TipsSystemManager.Instance:ShowSystemTips(Language.Field1v1.TianTiTxt19)
end

----------------------------排行榜界面 -----------------------------
ArenaTianTiRankAwardRender = ArenaTianTiRankAwardRender or BaseClass(BaseRender)

function ArenaTianTiRankAwardRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_zan, BindTool.Bind(self.ClickZanBtn, self))
end

function ArenaTianTiRankAwardRender:__delete()
end

function ArenaTianTiRankAwardRender:OnFlush()
    if not self.data then
        return
    end

	local coloe_str = self.data.uuid == RoleWGData.Instance:GetUUid() and IS_CAN_COLOR.ENOUGH or "#FFFFE9"
    self.node_list.text_name.text.text = string.format(Language.Field1v1.TianTiTxt18, coloe_str, self.data.role_name, self.data.server_id)
    self.node_list.text_rank.text.text = self.data.rank_pos > 2 and self.data.rank_pos + 1 or ""
    self.node_list.text_score.text.text = string.format("<color=%s>%s</color>", coloe_str, self.data.score)
    self.node_list.text_zan_num.text.text = self.data.have_like_count
    self.node_list.image_rank:SetActive(self.data.rank_pos <= 2)
    if self.data.rank_pos <= 2 then
        self.node_list.image_rank.image:LoadSprite(ResPath.GetCommonImages("a3_ty_panking_" .. self.data.rank_pos + 1))
    end

	XUI.SetGraphicGrey(self.node_list.btn_zan, self.data.is_like == 1)
end

function ArenaTianTiRankAwardRender:ClickZanBtn()
	if self.data.uuid == RoleWGData.Instance:GetUUid() then
		TipsSystemManager.Instance:ShowSystemTips(Language.Field1v1.TianTiTxt19)
		return
	end

    if self.data.is_like == 1 then
        TipsSystemManager.Instance:ShowSystemTips(Language.Field1v1.UpvoteRankTips2)
        return
    end

	local daily_count = ArenaTianTiWGData.Instance:GetDailyLikeCount()
    local max_daily_like_count = ArenaTianTiWGData.Instance:GetChallengeFieldOtherCfgByName("max_daily_like_count")
	if daily_count >= max_daily_like_count then
		TipsSystemManager.Instance:ShowSystemTips(Language.Field1v1.TianTiTxt10)
		return
	end

    ArenaTiantiWGCtrl.Instance:SendChallengeField(CROSS_CHALLENGE_RANK_LIST_TYPE.CHALLENGE_RANK_LIST_TYPE_LIKE, self.data.uuid)
end


--------------------------------------排行榜前三名-----------------------------------
ArenaTianTiRankRoleRender = ArenaTianTiRankRoleRender or BaseClass(BaseRender)
function ArenaTianTiRankRoleRender:__init(instance, parent_view)
	XUI.AddClickEventListener(self.node_list.rank_upvote_btn, BindTool.Bind1(self.OnClickUpvoteBtn, self))
	self.parent_view = parent_view

	self.late_id = -1
end

function ArenaTianTiRankRoleRender:__delete()
	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	self.parent_view = nil
end

function ArenaTianTiRankRoleRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local role_info = ArenaTianTiWGData.Instance:GetRoleInfoByUid(self.data.uuid)
	if IsEmptyTable(role_info) then
		return
	end

	self.node_list["name_txt"].text.text = self.data.uuid.temp_low == 0 and Language.Field1v1.TianTiTxt5 or role_info.name .. "s_" .. self.data.server_id
    self.node_list.text_role_score.text.text = self.data.score
	XUI.SetGraphicGrey(self.node_list.rank_upvote_btn, self.data.is_like == 1)

    self.node_list["rank_upvote_btn_text"].text.text =  self.data.have_like_count
	local title_id = ArenaTianTiWGData.Instance:GetChallengeFieldTitleReward(self.data.rank_pos)

	self.node_list["image_title"]:SetActive(false)
	if title_id ~= 0 then
		local b, a = ResPath.GetTitleModel(title_id)
		self.node_list["image_title"]:SetActive(true)
		self.node_list["image_title"]:ChangeAsset(b, a, false)
	end

	if self.late_id ~= self.data.uuid.temp_low then
		self.late_id = self.data.uuid.temp_low
		self:FlushModel(role_info)
	end
	if self.role_model then
		self.role_model:PlayLastAction()
	end
end

function ArenaTianTiRankRoleRender:FlushModel(role_info)
	if IsEmptyTable(role_info) then
		return
	end

    self.node_list["model_pos"]:SetActive(self.data.uuid.temp_low ~= 0)
    if self.data.uuid.temp_low == 0 then
        return
    end

	if not self.role_model then
		self.role_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["model_pos"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = true,
		}

		self.role_model:SetRenderTexUI3DModel(display_data)
		self.role_model:SetRTAdjustmentRootLocalScale(0.8)
		self.role_model:SetIsSupportClip(true)
	end

	local special_status_table = {ignore_wing = true, ignore_fazhen = true, ignore_jianzhen = true, ignore_halo = true}
	self.role_model:SetModelResInfo(role_info, special_status_table)
	self.role_model:FixToOrthographic(self.parent_view.root_node_transform)
end

function ArenaTianTiRankRoleRender:OnClickUpvoteBtn()
    local daily_count = ArenaTianTiWGData.Instance:GetDailyLikeCount()

    local max_daily_like_count = ArenaTianTiWGData.Instance:GetChallengeFieldOtherCfgByName("max_daily_like_count")
	if daily_count < max_daily_like_count then
		if self.data.uuid == RoleWGData.Instance:GetUUid() then
			TipsSystemManager.Instance:ShowSystemTips(Language.Field1v1.TianTiTxt19)
		elseif self.data.is_like == 0 then
			ArenaTiantiWGCtrl.Instance:SendChallengeField(CROSS_CHALLENGE_RANK_LIST_TYPE.CHALLENGE_RANK_LIST_TYPE_LIKE, self.data.uuid)
		else
			TipsSystemManager.Instance:ShowSystemTips(Language.Field1v1.UpvoteRankTips2)
		end
	else
		TipsSystemManager.Instance:ShowSystemTips(Language.Field1v1.TianTiTxt10)
	end
end