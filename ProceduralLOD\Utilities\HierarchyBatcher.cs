#if UNITY_EDITOR

using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using Unity.HLODSystem;
using Unity.HLODSystem.Serializer;
using Unity.HLODSystem.Simplifier;
using Unity.HLODSystem.SpaceManager;
using Unity.HLODSystem.Streaming;
using Unity.VisualScripting;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;

namespace ProceduralLOD
{
    public class HierarchyBatcher
    {
        private readonly static string SupportedShaderName = "JYShaders/StylizedScene";
        private readonly static string[] TextureInputs = new string[]{"_BaseMap", "_NormalMap", "_EmissionMap"};
        private readonly static string[] TextureOutputs = new string[]{"_BaseMap", "_BumpMap", "_EmissionMap"};
        
        public static IEnumerator BuildBatch(GameObject target)
        {
            GameObject[] rawChildren = new GameObject[target.transform.childCount];
            Color emissionColor = Color.black;
            for (int i = 0; i < rawChildren.Length; i++)
            {
                Renderer rawRenderer = target.transform.GetChild(i).gameObject.GetComponent<Renderer>();
                rawChildren[i] = rawRenderer.gameObject;

                Material[] materials = rawRenderer.sharedMaterials;
                for (int n = 0; n < materials.Length; n++)
                {
                    Material mat = materials[n];
                    Material instMat = null;
                    
                    int maxWidth = 0;
                    int maxHeight = 0;
                    for (int k = 0; k < TextureInputs.Length; k++)
                    {
                        Texture2D t = mat.GetTexture(TextureInputs[k]) as Texture2D;
                        if (t != null)
                        {
                            maxWidth = Mathf.Max(t.width, maxWidth);
                            maxHeight = Mathf.Max(t.height, maxHeight);
                        }
                    }
                    
                    for (int k = 0; k < TextureInputs.Length; k++)
                    {
                        Texture2D t = mat.GetTexture(TextureInputs[k]) as Texture2D;
                        if (t != null && (t.width != maxWidth || t.height != maxHeight))
                        {
                            LODTextureUtility.Resize(ref t, maxWidth, maxHeight);
                            if (instMat == null)
                                instMat = new Material(mat);
                            instMat.SetTexture(TextureInputs[k], t);
                        }
                    }

                    if (instMat != null)
                    {
                        materials[n] = instMat;
                    }

                    Color emission = mat.GetColor("_EmissionColor");
                    if (emission != Color.black && emission != Color.clear && emissionColor == Color.black)
                    {
                        emissionColor = emission;
                    }
                }

                rawRenderer.sharedMaterials = materials;
            }
            
            yield return Build_Enumerator(target);
            
            for (int i = 0; i < rawChildren.Length; i++)
            {
                GameObject.DestroyImmediate(rawChildren[i]);
            }
            
            string path = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(target.GetComponentInParent<LODGenerateHelper>().gameObject);
            string name = Path.GetFileNameWithoutExtension(path);
            Transform child = target.transform.GetChild(0);
            Texture[] textures = new Texture[TextureInputs.Length];
            Material material = child.GetComponent<MeshRenderer>().sharedMaterial;
            for (int i = 0; i < textures.Length; i++)
            {
                textures[i] = material.GetTexture(TextureOutputs[i]);
            }
            
            material.shader = Shader.Find(SupportedShaderName);
            material.name = name;
            
            for (int i = 0; i < textures.Length; i++)
            {
                material.SetTexture(TextureInputs[i], textures[i]);
            }
            material.SetColor("_EmissionColor", emissionColor);
            
            child.gameObject.SetActive(true);
            child.name = name;

        }

        public static bool IsSupported(Material[] materials)
        {
            bool supported = materials.Length > 1;

            string shaderName = "";
            foreach (var mat in materials)
            {
                if (string.IsNullOrEmpty(shaderName))
                {
                    shaderName = mat.shader.name;
                }
                if (mat.shader.name != shaderName)
                {
                    supported = false;
                }
            }

            if (shaderName != SupportedShaderName)
            {
                supported = false;
            }
            
            return supported;
        }

        private static IEnumerator Build_Enumerator(GameObject target)
        {
            
            HLOD hlod = target.AddComponent<HLOD>();

            hlod.ChunkSize = 10000;
            hlod.BatcherType = typeof(SimpleBatcher);
            hlod.SpaceSplitterType = typeof(QuadTreeSpaceSplitter);
            hlod.SimplifierType = typeof(None);
            hlod.StreamingType = typeof(Nonserialized);
            hlod.UserDataSerializerType = typeof(EmptyUserDataSerializerRegisterHelper);
            dynamic batcherOptions = hlod.BatcherOptions;
            if (batcherOptions.PackTextureSize == null)
                batcherOptions.PackTextureSize = 2048;
            if (batcherOptions.LimitTextureSize == null)
                batcherOptions.LimitTextureSize = 128;
            if (batcherOptions.MaterialGUID == null)
                batcherOptions.MaterialGUID = "";
            if (batcherOptions.TextureInfoList == null)
            {
                batcherOptions.TextureInfoList = new List<SimpleBatcher.TextureInfo>();

                for (int i = 0; i < TextureInputs.Length; i++)
                {
                    batcherOptions.TextureInfoList.Add(new SimpleBatcher.TextureInfo()
                    {
                        InputName = TextureInputs[i],
                        OutputName = TextureOutputs[i],
                        Type = SimpleBatcher.PackingType.White
                    });
                }
            }

            if (batcherOptions.EnableTintColor == null)
                batcherOptions.EnableTintColor = false;
            if (batcherOptions.TintColorName == null)
                batcherOptions.TintColorName = "";

            dynamic simplifierOptions = hlod.SimplifierOptions;
            if (simplifierOptions.PCCompression == null)
                simplifierOptions.PCCompression = TextureFormat.BC7;
            if (simplifierOptions.WebGLCompression == null)
                simplifierOptions.WebGLCompression = TextureFormat.DXT5;
            if (simplifierOptions.AndroidCompression == null)
                simplifierOptions.AndroidCompression = TextureFormat.ETC2_RGBA8;
            if (simplifierOptions.iOSCompression == null)
                simplifierOptions.iOSCompression = TextureFormat.PVRTC_RGBA4;
            if (simplifierOptions.tvOSCompression == null)
                simplifierOptions.tvOSCompression = TextureFormat.ASTC_4x4;

            using (new LODTextureUtility.TextureReadableScope(hlod.gameObject))
            {
                yield return HLODCreator.Create(hlod);
            }

            Object.DestroyImmediate(target.GetComponent<DefaultHLODController>());
            Object.DestroyImmediate(hlod);
        }

        private static int CalcPackTextureSize(GameObject target)
        {
            Renderer[] renderers = target.GetComponentsInChildren<Renderer>();
            foreach (var renderer in renderers)
            {
                foreach (var material in renderer.sharedMaterials)
                {
                    
                }
            }

            return 1;
        }
    }
}

#endif