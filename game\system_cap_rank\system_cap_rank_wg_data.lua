SystemCapRankWGData = SystemCapRankWGData or BaseClass()

function SystemCapRankWGData:__init()
	if SystemCapRankWGData.Instance then 
		ErrorLog("[SystemCapRankWGData] Attemp to create a singleton twice !")
	end

	SystemCapRankWGData.Instance = self

    local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_system_rank_auto")
    self.act_cfg = ListToMap(cfg.type, "activity_type")
	self.act_reward_cfg = ListToMapList(cfg.reward, "type")
	self.act_other_cfg = ListToMap(cfg.other, "type")
end


function SystemCapRankWGData:__delete()
	SystemCapRankWGData.Instance = nil
end

function SystemCapRankWGData:GetActTypeCfg(act_type)
    return self.act_cfg[act_type] or {}
end

function SystemCapRankWGData:GetActRewardCfg(type)
	return self.act_reward_cfg[type] or {}
end

function SystemCapRankWGData:SetCurActRewardType(reward_type)
	self.cur_reward_type = reward_type
end

function SystemCapRankWGData:GetCurActRewardType()
	return self.cur_reward_type or 0
end

function SystemCapRankWGData:GetMyRankValue()
	return self.my_rank_value or nil
end

function SystemCapRankWGData:GetActOtherCfg(type)
	return self.act_other_cfg[type] or {}
end

---------------------------------排行榜请求类型

--五行
function SystemCapRankWGData:GetSwornRankType()
	local cfg_type = self:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_SWORN)
	return cfg_type.rank_type or 0
end

--神技
function SystemCapRankWGData:GetShenJiRankType()
	local cfg_type = self:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_SHENJI)
	return cfg_type.rank_type or 0
end

--灵核
function SystemCapRankWGData:GetLingHeRankType()
	local cfg_type = self:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_LINGHE)
	return cfg_type.rank_type or 0
end

--圣器
function SystemCapRankWGData:GetShengQiRankType()
	local cfg_type = self:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_SQGUANG)
	return cfg_type.rank_type or 0
end

--暗器
function SystemCapRankWGData:GetAnQiRankType()
	local cfg_type = self:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_SQAN)
	return cfg_type.rank_type or 0
end

--铸魂(铸神)
function SystemCapRankWGData:GetZhuHunRankType()
	local cfg_type = self:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_CAST_SOUL)
	return cfg_type.rank_type or 0
end

--龙神
function SystemCapRankWGData:GetLongShenRankType()
	local cfg_type = self:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_DRAGON_TEMPLE)
	return cfg_type.rank_type or 0
end

--御灵
function SystemCapRankWGData:GetYuLingRankType()
	local cfg_type = self:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_YULING)
	return cfg_type.rank_type or 0
end

-- 武魂
function SystemCapRankWGData:GetWuHunRankType()
	local cfg_type = self:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_WUHUN)
	return cfg_type.rank_type or 0
end

-- 驭兽
function SystemCapRankWGData:GetYuShouRankType()
	local cfg_type = self:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_YUSHOU)
	return cfg_type.rank_type or 0
end
------------------------------------------------

function SystemCapRankWGData:CreatNoRankItemData(nSectionIndex)
	return {rank = nSectionIndex, rank_value = 0}
end

function SystemCapRankWGData:ExpandRankData(rank_list, nNextIndex, nCurRank, index)
	local next_data = rank_list[nNextIndex]
	if next_data then
		local nNextRank = next_data.rank_index
		if nCurRank + 1 ~= nNextRank then
			for nSectionIndex = nCurRank + 1, nNextRank - 1 do
				local data = {}
				data.no_true_rank = true
				data.index = index
				data.rank_data = self:CreatNoRankItemData(nSectionIndex)
				table.insert(self.systemRankdata.rank_list, data)
				index = index + 1
			end
		end
	end

	return index
end

function SystemCapRankWGData:SetSwornRankSort(protocol)
	--print_error(protocol)
	if protocol.self_value then
		self.my_rank_value = protocol.self_value
	end

	local cur_reward_type = self:GetCurActRewardType()
	local cfg = self:GetActRewardCfg(cur_reward_type)
	local rank_cfg = cfg[#cfg]
	local rank_list = {}
	local qujian_list = {}
	local is_rank = {}
	self.pro_data_list = {}

	local max_rank = rank_cfg.max_rank  --最多名次
	if #protocol.rank_list > max_rank then
		for i = 1, max_rank do
			rank_list[i] = protocol.rank_list[i]
		end
	else
		rank_list = protocol.rank_list
	end

	for i = 1, #cfg do
		qujian_list[i] = {}
	end

	for i = 1, max_rank do
		is_rank[i] = false
	end

	for k, v in pairs(rank_list) do
		local data_qujian = self:GetRankItemData(v.rank_value)
		if data_qujian ~= nil then
			local data = {
				rank_data = v,
				rank = data_qujian,
				rank_index = 0,
				sort = v.rank_value,
			}
			table.insert(qujian_list[data_qujian], data)
		end
	end

	local index = 0
	for i = 1, #cfg do
		if qujian_list[i] ~= nil then
			SortTools.SortDesc(qujian_list[i], "sort")
			for k, v in pairs(qujian_list[i]) do
				local _, rank_data = self:GetRankItemData(v.rank_data.rank_value)
				if rank_data and v.rank_index < rank_data.max_rank then
					v.rank_index = index + rank_data.min_rank
					index = index + 1
					if not is_rank[v.rank_index] then
						is_rank[v.rank_index] = true
					end
				end
				table.insert(self.pro_data_list, v)
			end
			index = 0
		end
	end

	for k, v in pairs(self.pro_data_list) do
		if is_rank[v.rank_index] and k ~= v.rank_index and v.rank_index < k then
			v.rank_index = k
		end
	end

	self.systemRankdata = {}
	self.systemRankdata.rank_list = {}
	local index2 = 1
    --local MaxValue = rank_cfg.limit_chongzhi  --最低上榜条件
	for i = 1, #self.pro_data_list do
		if i == 1 then
			if self.pro_data_list[i].rank_index ~= 1 then -- 没有第一名
				local item = {}
				item.rank_data = self:CreatNoRankItemData(1)
				item.index = index2
				item.no_true_rank = true
				index2 = index2 + 1
				table.insert(self.systemRankdata.rank_list, item)
				index2 = self:ExpandRankData(self.pro_data_list, 1, 1, index2)
			end
		end

		local item = {}
		item.index = index2
		item.rank_data = self.pro_data_list[i].rank_data
		item.rank_index = self.pro_data_list[i].rank_index
        local nCurRank = item.rank_index
		table.insert(self.systemRankdata.rank_list, item)
		index2 = index2 + 1

		index2 = self:ExpandRankData(self.pro_data_list, i + 1, nCurRank, index2)
	end

	local nMaxRank = rank_cfg.max_rank
	if index2 - 1 < nMaxRank then
		for nSectionIndex = index2, nMaxRank do
			local data = {}
			data.no_true_rank = true
			data.index = nSectionIndex
			data.rank_data = self:CreatNoRankItemData(nSectionIndex)
			table.insert(self.systemRankdata.rank_list, data)
		end
	end
end

function SystemCapRankWGData:GetRankInfo()
	if self.systemRankdata ~= nil and self.systemRankdata.rank_list ~= nil then
		return self.systemRankdata.rank_list	
	end
	return nil
end

function SystemCapRankWGData:GetRankItemData(rank_zhanli)
	local index, data
	local cur_reward_type = self:GetCurActRewardType()
	local rank_cfg = self:GetActRewardCfg(cur_reward_type)
	if rank_cfg ~= nil then
		for k ,v in ipairs(rank_cfg) do
			if rank_zhanli >= v.reach_value then
				return k, v
			end
		end
	end

	return nil
end

function SystemCapRankWGData:GetMyRankInfo()
	local rank_list = self:GetRankInfo()
	if rank_list == nil then
		return nil
	end
    
	local my_uid = RoleWGData.Instance:GetRoleVo().origin_uid
	for k, v in pairs(rank_list) do
		if v.rank_data and v.rank_data.user_id then
			if v.rank_data.user_id == my_uid then
				return v
			end
		end
	end

	return nil
end