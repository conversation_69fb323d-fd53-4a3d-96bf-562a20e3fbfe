GuildCreateView = GuildCreateView or BaseClass(SafeBaseView)
function GuildCreateView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Normal
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
						{vector2 = Vector2(0, 0), sizeDelta = Vector2(812, 522)})
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_guild_create_view")
end

function GuildCreateView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_onclick_create"], BindTool.Bind(self.OnClickCreateHandler, self))

	local guild_other_cfg = GuildWGData.Instance:GetGuildOtherCfg()
	self.create_item_id = guild_other_cfg.create_item_id
	self.create_vip_level = guild_other_cfg.creat_guild_vip
	self.create_level = guild_other_cfg.creat_guild_level

	local item_cfg = ItemWGData.Instance:GetItemConfig(self.create_item_id)
	if item_cfg then
		self.node_list.lbl_xm_item_icon.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
	end

	if nil == self.itemdata_change_callback then
		self.itemdata_change_callback = BindTool.Bind1(self.InitGuildShowInfo, self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.itemdata_change_callback)
	end

	self.node_list.title_view_name.text.text = Language.GuildCreate.GuildCreateViewName
end

function GuildCreateView:ReleaseCallBack()
	self.create_item_id = nil
	self.create_vip_level = nil
	self.create_level = nil
	self.cur_color = nil
	self.cur_index = nil
	self.cur_flag_name = nil
	self.cur_guild_level = nil
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.itemdata_change_callback)
end

function GuildCreateView:InitGuildShowInfo()
	self.cur_index = 1  --旗帜类型     （旧逻辑会向服务器发送 旗帜类型和颜色值，新版本不需要旗帜了 默认设为1发过去）
	self.cur_color = 1  --初始旗帜颜色
	local guildvo = GuildDataConst.GUILDVO
	local vo = RoleWGData.Instance:GetRoleVo()
	local vip_level = vo.vip_level
	-- local vip_str = ToColorStr(self.create_vip_level, vip_level >= self.create_vip_level and COLOR3B.DEFAULT_NUM or COLOR3B.RED)
	self.node_list.lbl_xm_vip_level.text.text = string.format(Language.GuildCreate.GuildVipDesc, self.create_vip_level)
	-- self.node_list["img_vip_level"]:SetActive(vip_level >= self.create_vip_level) --VIP等级勾子

	local item_num = ItemWGData.Instance:GetItemNumInBagById(self.create_item_id)
	self.node_list.lbl_xm_item_num.text.text = ToColorStr(1, item_num > 0 and COLOR3B.DEFAULT or COLOR3B.RED)

	self.node_list["layout_create"]:SetActive(guildvo.guild_id == 0)
end

function GuildCreateView:ShowIndexCallBack()
	self.old_color = 0
	self:InitGuildShowInfo()
end

function GuildCreateView:OnClickColorHandler(color_type, is_click)
	if color_type == self.cur_color then
		return
	end
	self.cur_color = color_type
	self:OnClickFlagHandler(self.cur_index)
end

function GuildCreateView:OnClickFlagHandler(index)
	if index == self.cur_index and  self.old_color == self.cur_color then
		return 
	end
	self.cur_index = index
	self.old_color = self.cur_color
end


function GuildCreateView:OnClickCreateHandler()
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	--等级
	if main_role_vo.level < self.create_level then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildCreate.GuildCreateLevelLimit)
		return
	end
	--vip等级
	if main_role_vo.vip_level < self.create_vip_level then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildCreate.GuildCreatetVipLimit)
		return
	end

	--建帮令
	local item_num = ItemWGData.Instance:GetItemNumInBagById(self.create_item_id)
	if item_num <= 0 then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.create_item_id})
		return
	end

	--仙盟名字规范
	local guild_name = ""
	if self.node_list.guild_name.input_field.text == "" then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildCreate.GuildNameNull)
		return
	else
		guild_name = self.node_list.guild_name.input_field.text
	end
	
	if ChatFilter.Instance:IsIllegal(guild_name, true) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildCreate.GuildNameIllegal)
		return
    end
    
	-- 重名
	local is_shield_fake = GuildWGData.Instance:GetIsShieldFakeGuild()
	if not is_shield_fake then
		local guild_list = GuildWGData.Instance:GetFakeGuildShowList()
		for k,v in pairs(guild_list) do
			if v.guild_name == guild_name then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildCreate.SameName)
				return
			end
		end
	end

    local flag_name = Language.Guild.DefaultFlagName
	--有非法字符直接不让发
	if ChatFilter.Instance:IsIllegal(flag_name, false) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.IllegalContent)
		return
	end

	local bag_index = ItemWGData.Instance:GetItemIndex(self.create_item_id)

	GuildWGCtrl.Instance:SendCreateGuildReq(guild_name,GUILD_CREATE_TYPE.GUILD_CREATE_TYPE_ITEM, bag_index, Language.Guild.EmptyNotice, self.cur_index, self.cur_color, flag_name)
	self:Close()
end
