-- 新手弹窗对话数据
InterludePopDialogWGData = InterludePopDialogWGData or BaseClass()
function InterludePopDialogWGData:__init()
	if InterludePopDialogWGData.Instance ~= nil then
		<PERSON>rrorLog("[InterludePopDialogWGData] attempt to create singleton twice!")
		return
	end
	InterludePopDialogWGData.Instance = self

	self.cfg = ConfigManager.Instance:GetAutoConfig("story_auto").game_interlude
end

function InterludePopDialogWGData:__delete()
	InterludePopDialogWGData.Instance = nil
end

function InterludePopDialogWGData:GetConfig()
	return self.cfg
end

function InterludePopDialogWGData:GetInterludeCfgById(interlude_id)
	local enemy = {}
	return (self.cfg or enemy)[interlude_id] or nil
end