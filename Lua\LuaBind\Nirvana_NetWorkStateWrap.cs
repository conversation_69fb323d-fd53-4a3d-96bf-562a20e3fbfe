﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class Nirvana_NetWorkStateWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>BeginStaticLibs("NetWorkState");
		<PERSON><PERSON>Function("SetNetWorkStateListener", SetNetWorkStateListener);
		<PERSON><PERSON>EndStaticLibs();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetNetWorkStateListener(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				Nirvana.NetWorkState.SetNetWorkStateListener();
				return 0;
			}
			else if (count == 1)
			{
				System.Action<int,string> arg0 = (System.Action<int,string>)ToLua.CheckDelegate<System.Action<int,string>>(L, 1);
				Nirvana.NetWorkState.SetNetWorkStateListener(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Nirvana.NetWorkState.SetNetWorkStateListener");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

