-- 竞技场结算面板
ArenaTianTiFinish = ArenaTianTiFinish or BaseClass(SafeBaseView)
--数据来源
DATAFROMTHERE = {
	NONE = 0, --无
	GUILD_SHOWHU = 1 --仙盟守护
}


function ArenaTianTiFinish:__init()
	self.view_style = ViewStyle.Half
	--self:SetModal(true)
	self.default_index = 0
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
    self:SetMaskBg(true, true, nil, BindTool.Bind1(self.OnOutCloseViewHandler, self))
    self.view_name = "ArenaTianTiFinish"
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_jiesuan_min_panel")
    self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_txt_end")
	self.is_guide = false
end

function ArenaTianTiFinish:__delete()

end

function ArenaTianTiFinish:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("field1v1_txt_end_countdown") then
		CountDownManager.Instance:RemoveCountDown("field1v1_txt_end_countdown")
	end
	-- if self.reward_baecell2 then
	-- 	self.reward_baecell2:DeleteMe()
	-- 	self.reward_baecell2 = nil
	-- end
	-- if self.reward_baecell1 then
	-- 	self.reward_baecell1:DeleteMe()
	-- 	self.reward_baecell1 = nil
	-- end
	for i=1,5 do
		if self["reward_baecell"..i] then
			self["reward_baecell"..i]:DeleteMe()
			self["reward_baecell"..i] = nil
		end
	end

	self.data_from = nil
	self.is_skip = nil
end

function ArenaTianTiFinish:LoadCallBack(index, loaded_times)
	self.node_list.reward_container:SetActive(false)
	self.node_list.btn_out_end:SetActive(false)
	ArenaTianTiWGData.Instance.is_fail_select = false
	for i=1,5 do
		self["reward_baecell"..i] = ItemCell.New(self.node_list["reward_"..i])
	end
	-- self.reward_baecell1 = ItemCell.New(self.node_list.reward_1)
	-- self.reward_baecell2 = ItemCell.New(self.node_list.reward_2)
	XUI.AddClickEventListener(self.node_list.btn_out_end, BindTool.Bind1(self.OnOutCloseViewHandler, self)) --关闭
	XUI.AddClickEventListener(self.node_list.btn_miaosha_again, BindTool.Bind(self.OnClickMiaoShaoAgain, self))
end

function ArenaTianTiFinish:GetGuideOutBtn(ui_name, ui_param)
end

function ArenaTianTiFinish:ClickFunIconHandler(view_name, tab_index)
	ArenaTianTiWGData.Instance.is_fail_select = true
	self:Close()
	FuBenWGCtrl.Instance:SendLeaveFB()
	FunOpen.Instance:OpenViewByName(view_name, tab_index)
end

function ArenaTianTiFinish:OnFlush()
	if nil == self.end_data then
		return
	end

	local end_data = self.end_data
	self.node_list.new_sdcore:SetActive(end_data.best_score >= end_data.new_score and end_data.new_score > end_data.old_score)
	-- if self.data_from == DATAFROMTHERE.GUILD_SHOWHU then
	-- 	self:GuildShouHuDataDealWith()
	-- 	return
	-- end

	local text_t = {}
	if nil ~= self.end_data.tip1 then
		text_t = Split(self.end_data.tip1, "@")
	end

	if #text_t > 1 then

	else
		if self.end_data.tip1 == nil then
			self.end_data.tip1 = string.format(Language.Dungeon.TipTime, HtmlTool.GetHtml(self.format_time, COLOR3B.YELLOW))
		end

		self.node_list.rich_time_des2:SetActive(false)
		self.node_list.exp_show:SetActive(false)
        --self.node_list.text_parent.transform.anchoredPosition = Vector2(232.1,123.5)

        if self.end_data.is_split then
            local str = Split(self.end_data.tip1, "|")
            self.node_list.rich_des1.text.text = str[1]
            self.node_list.rich_des2.text.text = str[2]
            self.node_list.text_parent:SetActive(false)
            self.node_list.win_container:SetActive(true)
            UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.rich_des1.rect)
            UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.win_container.rect)
        else
            self.node_list.text_parent:SetActive(true)
            self.node_list.win_container:SetActive(false)
            self.node_list.rich_time_des1.text.text = self.end_data.tip1
        end

		--self.node_list.new_sdcore:SetActive(self.end_data.is_score == true)
	end

	for i=1,5 do
		--self.node_list["reward_"..i]:SetActive(i <= 2)
		if self.end_data.item_list[i] then
			self.node_list["reward_"..i]:SetActive(true)
			self["reward_baecell"..i]:SetData(self.end_data.item_list[i])
		else
			self.node_list["reward_"..i]:SetActive(false)
		end
	end

	self.node_list.lbl_rongyu.text.text = self.end_data.tip2
	self.node_list.lbl_exp.text.text = CommonDataManager.ConverExp(self.end_data.get_exp,nil,false)
	-- self.reward_baecell1:SetData({item_id = 90071,num = self.end_data.tip2})
	-- self.reward_baecell2:SetData({item_id = 90050,num = self.end_data.get_exp })

	if CountDownManager.Instance:HasCountDown("field1v1_txt_end_countdown") then
		CountDownManager.Instance:RemoveCountDown("field1v1_txt_end_countdown")
	end

	self:UpdataNextTime(0, 7)
	CountDownManager.Instance:AddCountDown("field1v1_txt_end_countdown", BindTool.Bind1(self.UpdataNextTime, self), BindTool.Bind1(self.CompleteNextTime, self), nil, 7, 1)

	local boo = ArenaTianTiWGData.Instance:GetGuideFlag()
	if boo == true then
		self.is_guide = true
		local guide_cfg = FunctionGuide.Instance:GetGuideCfgByTrigger(GuideTriggerType.Field1v1, 1300)
		FunctionGuide.Instance:SetCurrentGuideCfg(guide_cfg)
	end

	--奖励
	local other_cfg = ArenaTianTiWGData.Instance:GetChallengeFieldOtherCfg()
	local reward_list = {}
	if self.end_data.reward_item_list ~= nil then						--扫荡奖励服务端发
		self.node_list.victory:SetActive(true)
		self.node_list.lose:SetActive(false)
		self:PanelAni(true)
		--reward_list = self.end_data.reward_item_list
	elseif 1 == self.end_data.is_passed then
		self.node_list.victory:SetActive(true)
		self.node_list.lose:SetActive(false)
		reward_list = other_cfg.win_reward_item
		self:PanelAni(true)
	else
		self.node_list.victory:SetActive(false)
		self.node_list.lose:SetActive(true)
		self:PanelAni(false)
		--reward_list = other_cfg.lose_reward_item
	end

	local can_again = ArenaTianTiWGData.Instance:GetIsCanMSAgain()
	self.node_list.btn_miaosha_again:SetActive(false)--can_again and self.is_skip)
end

function ArenaTianTiFinish:GuildShouHuDataDealWith()
	self.node_list.rich_time_des2:SetActive(true)
	self.node_list.exp_show:SetActive(true)
	--self.node_list.text_parent.transform.anchoredPosition = Vector2(232.1,138.5)
	--从0开始
	local cur_num = GuildWGData.Instance:GetNowMonsterLun()
	local max_num = GuildWGData.Instance:GetGuildMiJingWave()
	if 1 == self.end_data[1].process_flag.pass_flag then
		cur_num = cur_num + 1
		if cur_num > max_num then
			cur_num = max_num
		end
	end

	local cunhuo_num = GuildWGData.Instance:GetGuildCunHuoNum()
	self.node_list.rich_time_des1.text.text = string.format(Language.Dungeon.GuildShouHuWaveNum,cur_num)
	self.node_list.rich_time_des2.text.text = string.format(Language.Dungeon.GuildShouHuCunHuoNum,cunhuo_num)
	self.node_list.exp_num.text.text = self.end_data[1].mijing_exp
	self.node_list.lbl_rongyu.text.text = ""
	self.node_list.lbl_exp.text.text = ""
	local guild_showhu_rewrd_item = {}
	local item_num = 0
	-- if 1 == self.end_data[1].process_flag.pass_flag then
		guild_showhu_rewrd_item = GuildWGData.Instance:GetGuildItemList(GuildWGData.Instance:GetNowMonsterLun())
		item_num = #guild_showhu_rewrd_item
	-- end
	for i=1,4 do
		self.node_list["reward_"..i]:SetActive(i <= item_num)
	end

	if item_num > 0 then
		for k,v in pairs(guild_showhu_rewrd_item) do
			self["reward_baecell"..k]:SetData(v)
			if v.num > 0 then
				self["reward_baecell"..k]:SetRightBottomTextVisible(true)
				self["reward_baecell"..k]:SetRightBottomColorText(v.num)
			end
		end
	end

	if CountDownManager.Instance:HasCountDown("field1v1_txt_end_countdown") then
		CountDownManager.Instance:RemoveCountDown("field1v1_txt_end_countdown")
	end

	self:UpdataNextTime(0, 10)
	CountDownManager.Instance:AddCountDown("field1v1_txt_end_countdown", BindTool.Bind1(self.UpdataNextTime, self), BindTool.Bind1(self.CompleteNextTime, self), nil, 10, 1)
	if 1 == self.end_data[1].process_flag.pass_flag then
		self.node_list.victory:SetActive(true)
		self.node_list.lose:SetActive(false)
		self:PanelAni(true)
	else
		self.node_list.victory:SetActive(false)
		self.node_list.lose:SetActive(true)
		self:PanelAni(false)
	end

	self.node_list.btn_miaosha_again:SetActive(false)
end

function ArenaTianTiFinish:PanelAni(is_show)
	self.node_list.reward_container:SetActive(true)
	self.node_list.btn_out_end:SetActive(true)
	if is_show then
		UITween.AlpahShowPanel(self.node_list.reward_container.gameObject, true, 0.3)
		UITween.AlpahShowPanel(self.node_list.btn_out_end.gameObject, true, 0.3)
		--UITween.MoveShowPanel(self.node_list.mScroll.gameObject, Vector3(1000,0), 0.5)
	else
		UITween.AlpahShowPanel(self.node_list.reward_container.gameObject, true, 0.3)
		UITween.AlpahShowPanel(self.node_list.btn_out_end.gameObject, true, 0.3)
		--UITween.MoveShowPanel(self.node_list.mScroll.gameObject, Vector3(1000,0), 0.5)
	end
end

function ArenaTianTiFinish:UpdataNextTime(elapse_time, total_time)
	local time = math.ceil(total_time - elapse_time)
	if time >= 0 then
		self.node_list.rich_time_out.text.text = string.format(Language.Dungeon.OutTimerTxt1, time)
	else
		self.node_list.rich_time_out.text.text = string.format(Language.Dungeon.OutTimerTxt1, 0)
	end
end

function ArenaTianTiFinish:CompleteNextTime()
	self.node_list.rich_time_out.text.text = ""
	self:OnChallengeNextMission()
end

function ArenaTianTiFinish:OnChallengeNextMission()
	if self.is_guide == true then
		self.is_guide = false
		self:OnOutCloseViewHandler()
		FunctionGuide.Instance:StartNextStep()
		return
	end

	if self.data_from == DATAFROMTHERE.GUILD_SHOWHU then
		self:Close()
	else
		self:OnOutCloseViewHandler()
	end
end

function ArenaTianTiFinish:OnClickMiaoShaoAgain()
	local info = ArenaTianTiWGData.Instance:GetUserinfo()
	if nil == info or nil == info.rank_list then
		return
	end

	local sorted_list = info.rank_list
	--table.sort(sorted_list, SortTools.KeyLowerSorter("rank"))
	local rank_index = sorted_list[#sorted_list]
	if nil == rank_index or nil == rank_index.user_id then
		return
	end

	local tz_info = ArenaTianTiWGData.Instance:GetRoleTiaoZhanInfoByUid(rank_index.user_id)
	if tz_info then
		local data = {}
		data.opponent_index = tz_info.index
		data.rank_pos = tz_info.rank_pos
		data.opponent_uuid = rank_index.user_id
		data.is_skip = self.is_skip and 1 or 0
		ArenaTiantiWGCtrl.Instance:ResetFieldFightReq(data)
	end
end

function ArenaTianTiFinish:OpenCallBack()
	--AudioManager.Instance:PlayOpenCloseUiEffect()
end

function ArenaTianTiFinish:CloseCallBack()
	-- if not is_all then
		-- FunctionGuide.Instance:StopGuideByModuleName(GuideUIName.Field1v1Finish)
		-- ArenaTiantiWGCtrl.Instance:CloseTiaoZhanPanel()
	-- end
	self.end_data = nil
	ArenaTianTiWGData.Instance:SetUerGold(0)
	if CountDownManager.Instance:HasCountDown("field1v1_txt_end_countdown") then
		CountDownManager.Instance:RemoveCountDown("field1v1_txt_end_countdown")
	end

	if not self:IsLoaded() then
		return
	end
	-- AudioManager.Instance:PlayOpenCloseUiEffect()
	self.node_list.reward_container:SetActive(false)
	self.node_list.btn_out_end:SetActive(false)
end

function ArenaTianTiFinish:OpenFinish(data, from, is_skip)
	if from == nil then
		from = DATAFROMTHERE.NONE
	end

	self.data_from = from
	self.end_data = data
	self.is_skip = is_skip
end

function ArenaTianTiFinish:OnOutCloseViewHandler()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.ARENA_TIANTI then
		ViewManager.Instance:Open(GuideModuleName.ActJjc, TabIndex.arena_tianti)
		RobertManager.Instance:Stop()
	end

	self:Close()
end
