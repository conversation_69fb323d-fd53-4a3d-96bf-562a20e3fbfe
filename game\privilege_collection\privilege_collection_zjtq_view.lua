function PrivilegeCollectionView:ZJTQLoadIndecCallBack()
    if not self.suit_tzsd_grid_list then
        self.suit_tzsd_grid_list = AsyncListView.New(PRICOLZJTQItem<PERSON>ell<PERSON>ender, self.node_list.suit_tzsd_grid_list)
        self.suit_tzsd_grid_list:SetSelectCallBack(BindTool.Bind(self.OnSelectZJ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self))
    end

    if not self.suit_tzsd_reward_list then
        self.suit_tzsd_reward_list = AsyncListView.New(ItemCell, self.node_list.suit_tzsd_reward_list)
        self.suit_tzsd_reward_list:SetStartZeroIndex(true)
    end

	-- if not self.suit_tzsd_reward_list then
	-- 	self.suit_tzsd_reward_list = AsyncBaseGrid.New()
	-- 	self.suit_tzsd_reward_list:SetStartZeroIndex(true)
	-- 	self.suit_tzsd_reward_list:CreateCells(
    --         {col = 3,
    --         change_cells_num = 1,
    --         list_view = self.node_list.suit_tzsd_reward_list}
    --     )

    --     -- self.suit_tzsd_reward_list = AsyncListView.New(ItemCell, self.node_list.suit_tzsd_reward_list)
    -- end

    -- if not self.tzsd_model then
    --     self.tzsd_model = OperationActRender.New(self.node_list.tzsd_model_pos)
    --     self.tzsd_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
    -- end

    self.tzsd_select_suit_data = {}
    XUI.AddClickEventListener(self.node_list.btn_tzsd_buy, BindTool.Bind(self.OnClickTQSDBuy, self))
	XUI.AddClickEventListener(self.node_list.zjtq_skill_show_btn, BindTool.Bind(self.OnClickSkillShow, self))

	self.node_list.zztq_zztl_skill_name.text.text = Language.PrivilegeCollection.ZJTQSkillNameStr
end

function PrivilegeCollectionView:ZJTQShowIndecCallBack()
	self.tzsd_model_cache = nil
end

function PrivilegeCollectionView:ZJTQReleaseCallBack()
    if self.suit_tzsd_grid_list then
        self.suit_tzsd_grid_list:DeleteMe()
        self.suit_tzsd_grid_list = nil
    end

    if self.suit_tzsd_reward_list then
        self.suit_tzsd_reward_list:DeleteMe()
        self.suit_tzsd_reward_list = nil
    end

    if self.zjtq_role_model then
        self.zjtq_role_model:DeleteMe()
        self.zjtq_role_model = nil
    end

    if self.zjtq_lingchong_model then
        self.zjtq_lingchong_model:DeleteMe()
        self.zjtq_lingchong_model = nil
    end

    if self.zjtq_xianwa_model then
        self.zjtq_xianwa_model:DeleteMe()
        self.zjtq_xianwa_model = nil
    end

    if self.zjtq_mount_model then
        self.zjtq_mount_model:DeleteMe()
        self.zjtq_mount_model = nil
    end

	if self.zjtq_weapon_model then
		self.zjtq_weapon_model:DeleteMe()
		self.zjtq_weapon_model = nil
	end

    -- if self.tzsd_model then
    --     self.tzsd_model:DeleteMe()
    --     self.tzsd_model = nil
    -- end

	self.video_clips = nil
    self.zjtq_video_palyer = nil

    self.tzsd_select_suit_data = nil
    self.zjtq_model_cache = nil
	self.tzsd_model_cache = nil
end

function PrivilegeCollectionView:ZJTQOnFlush(param_t, index)
    local show_data_list = PrivilegeCollectionWGData.Instance:GetShowTZSDDataList()
    self.suit_tzsd_grid_list:SetDataList(show_data_list)
    local jump_to_index = self.suit_tzsd_grid_list:GetSelectIndex() or 1
    self.suit_tzsd_grid_list:JumpToIndex(jump_to_index)
end

function PrivilegeCollectionView:OnSelectZJTQCellHandler(item)
    if nil == item or IsEmptyTable(item.data) then
        return
    end

    local data = item.data
    local cfg = data.cfg

	local effect_bundle, effect_asset = ResPath.GetUIEffect(cfg.button_effect_xia)
	self.node_list.zjtq_reward_bg_effect:ChangeAsset(effect_bundle, effect_asset)

	local bundle, asset = ResPath.GetRawImagesPNG(data.cfg.zjtq_title)
	self.node_list.zjtq_title.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["zjtq_title"].raw_image:SetNativeSize()
    end)

	local title_effect_bundle, title_effect_asset = ResPath.GetUIEffect(cfg.left_txt_effect)
	self.node_list.zjtq_title_effect:ChangeAsset(title_effect_bundle, title_effect_asset)

	local btn_bg_bundle, btn_bg_asset = ResPath.GetRawImagesPNG(data.cfg.zjtq_button_bg)
	self.node_list.btn_tzsd_buy.raw_image:LoadSprite(btn_bg_bundle, btn_bg_asset, function ()
        self.node_list["btn_tzsd_buy"].raw_image:SetNativeSize()
    end)
	
	local reward_bg_bundle, reward_bg_asset = ResPath.GetRawImagesPNG(data.cfg.zjtq_reward_bg)
	self.node_list.zjtq_reward_bg.raw_image:LoadSprite(reward_bg_bundle, reward_bg_asset, function ()
        self.node_list["zjtq_reward_bg"].raw_image:SetNativeSize()
    end)

    self.tzsd_select_suit_data = data
    self.suit_tzsd_reward_list:SetDataList(cfg.reward_item)
    local limit_buy_times = cfg.buy_limit - data.buy_times
    self.node_list.btn_tzsd_buy:CustomSetActive(limit_buy_times > 0)

    if limit_buy_times > 0 then
		-- 有前置，并且前置未兑换 
		if not PrivilegeCollectionWGData.Instance:IsSuitSeqIsUnLock(cfg.seq) then
			self.node_list.desc_tzsd_buy.text.text = Language.PrivilegeCollection.TZSDUnLock
		else
			if cfg.buy_type == 1 then
				self.node_list.desc_tzsd_buy.text.text = RoleWGData.GetPayMoneyStr(cfg.price, cfg.rmb_type, cfg.rmb_seq)
			elseif cfg.buy_type == 2 then
				self.node_list.desc_tzsd_buy.text.text = string.format(Language.PrivilegeCollection.ScoreStr, cfg.price)
			end
	
			self.node_list.desc_tzsd_buy_tip.text.text = string.format(Language.PrivilegeCollection.TZSDLimitBuyTimeStr, limit_buy_times, cfg.buy_limit)
		end
    else
        self.node_list.desc_tzsd_buy_tip.text.text = ""
    end

	self.node_list.desc_tzsd_buy.text.color = Str2C3b(cfg.buy_txt)

    self.node_list.flag_sell_out:CustomSetActive(limit_buy_times <= 0)

    -- local score = PrivilegeCollectionWGData.Instance:GetCurScore()
    -- self.node_list.btn_tzsd_buy_remind:CustomSetActive(limit_buy_times > 0 and cfg.buy_type == 2 and score >= cfg.price)

	self.ui_scene_change_config_index = cfg.ui_scene_config_index
    Scene.Instance:ChangeUISceneController(UI_SCENE_TYPE.DEFAULT, UI_SCENE_CONFIG_INDEX.PRI_COL_ZJTQ, cfg.ui_scene_config_index)
    self:FlushTZSDModel(self.tzsd_select_suit_data.cfg)
	-- self.node_list.zjtq_skill_show_btn:SetActive(cfg.show_skilll_id > 0)

	self.node_list.zztq_zztl:CustomSetActive(cfg.right_show_type == 0)
	self.node_list.zztq_smls:CustomSetActive(cfg.right_show_type == 1)
	self.node_list.zztq_yyyl:CustomSetActive(cfg.right_show_type == 2)

	if cfg.right_show_type == 0 and cfg.show_skilll_id > 0 then
        local play_video = function()
			if not IsNil(self.zjtq_video_palyer) then
				self.zjtq_video_palyer.time = 0
				self.zjtq_video_palyer:Play()
			end
        end

		if not self.zjtq_video_palyer then
			self.zjtq_video_palyer = self.node_list.zjtq_video_palyer:GetComponent(typeof(UnityEngine.Video.VideoPlayer))

			if self.video_clips == nil then
				self.video_clips = {}
			end

			if self.video_clips[cfg.show_skilll_id] then
				self.zjtq_video_palyer.clip = self.video_clips[cfg.show_skilll_id]
				play_video()
			else
				local bundle, asset = ResPath.GetCommonSpiritSkillVideoPath(cfg.show_skilll_id)

				ResPoolMgr:GetVideoClip(bundle, asset, function (asset)
					if self.video_clips ~= nil then
						self.video_clips[cfg.show_skilll_id] = asset
					end

					if not IsNil(self.zjtq_video_palyer) then
						self.zjtq_video_palyer.clip = asset
						play_video()
					end
				end)
			end
		else
			play_video()
		end
	end
end

function PrivilegeCollectionView:ZJTQPlayLastAction()
	-- if self.zjtq_role_model then
	-- 	self.zjtq_role_model:PlayLastAction()
	-- end

	-- if self.zjtq_lingchong_model then
	-- 	self.zjtq_lingchong_model:PlayLastAction()
	-- end

	-- if self.zjtq_xianwa_model then
	-- 	self.zjtq_xianwa_model:PlayLastAction()
	-- end

	-- if self.zjtq_mount_model then
	-- 	self.zjtq_mount_model:PlayLastAction()
	-- end

	-- if self.zjtq_weapon_model then
	-- 	self.zjtq_weapon_model:PlayLastAction()
	-- end
	local cfg = self.tzsd_select_suit_data and self.tzsd_select_suit_data.cfg or {}
	self:FlushTZSDModel(cfg)
end

function PrivilegeCollectionView:FlushTZSDModel(model_data)
	if self.tzsd_model_cache == model_data then
		return 
	end

	self.tzsd_model_cache = model_data

	if model_data and model_data.need_show_role == 1 then
		if not self.zjtq_role_model then
			self.zjtq_role_model = RoleModel.New()
			self.zjtq_role_model:SetUISceneModel(self.node_list["zjtq_role_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
			self:AddUiRoleModel(self.zjtq_role_model, TabIndex.pri_col_zjtq)
		end
	else
		if self.zjtq_role_model then
			self.zjtq_role_model:RemoveAllModel()
		end
	end

	if self.zjtq_lingchong_model then
		self.zjtq_lingchong_model:RemoveAllModel()
	end

	if self.zjtq_xianwa_model then
		self.zjtq_xianwa_model:RemoveAllModel()
	end

	if self.zjtq_mount_model then
        self.zjtq_mount_model:RemoveAllModel()
    end

	if self.zjtq_weapon_model then
		self.zjtq_weapon_model:RemoveAllModel()
	end

    local show_list = PrivilegeCollectionWGData.Instance:GetShowModelCfgListBySeq(model_data.model_show_seq)
	if IsEmptyTable(model_data) or IsEmptyTable(show_list) then
		return
	end

    -- self.zjtq_role_model:RemoveAllModel()
	-- self.node_list["zjtq_lc_display"]:SetActive(false)
	-- self.node_list["zjtq_xw_display"]:SetActive(false)
	-- self.node_list["zjtq_mount_display"]:SetActive(false)


	if model_data.need_show_role == 1 then
		local body_res_id = AppearanceWGData.Instance:GetRoleResId()
		local mount_res_id = 0
		local have_foot_print = false
		local has_fashion_show = false
	
		local res_id, fashion_cfg
		for k, data in pairs(show_list) do
			if data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.BODY then -- 时装大类
				fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
				if fashion_cfg then                                                          -- 时装
					local prof = GameVoManager.Instance:GetMainRoleVo().prof
					body_res_id = ResPath.GetFashionModelId(prof, fashion_cfg.resouce)
					has_fashion_show = true
				end
			elseif data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.FOOT then -- 足迹
				have_foot_print = true
			end
		end
	
		local d_body_res, d_hair_res, d_face_res
		local main_role = not has_fashion_show and Scene.Instance:GetMainRole()
		local vo = main_role and main_role:GetVo()
		if not has_fashion_show and vo and vo.appearance then
			if vo.appearance.fashion_body == 0 then
				d_body_res = vo.appearance.default_body_res_id
				d_hair_res = vo.appearance.default_hair_res_id
				d_face_res = vo.appearance.default_face_res_id
			end
		end
	
		local animation_name = have_foot_print and SceneObjAnimator.Move or SceneObjAnimator.Rest
		
		local extra_role_model_data = {
			d_face_res = d_face_res,
			d_hair_res = d_hair_res,
			d_body_res = d_body_res,
			animation_name = animation_name,
		}
		self.zjtq_role_model:SetRoleResid(body_res_id, nil, extra_role_model_data)
	end

	for k, v in pairs(show_list) do
		self:ShowZJTQModelByData(model_data, v)
	end

    self:ChangeZJTQModelShowScale(model_data.theme_seq)
end

function PrivilegeCollectionView:ShowZJTQModelByData(model_data, data)
    if IsEmptyTable(data) then
		return
	end

    local res_id, fashion_cfg
	if model_data.need_show_role == 1 and data.type == WARDROBE_PART_TYPE.FASHION then -- 时装大类
		fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
		if fashion_cfg then
			res_id = fashion_cfg.resouce
			if data.param1 == SHIZHUANG_TYPE.MASK then -- 脸饰
				self.zjtq_role_model:SetMaskResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.BELT then -- 腰饰
				self.zjtq_role_model:SetWaistResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.WEIBA then -- 尾巴
				self.zjtq_role_model:SetTailResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.SHOUHUAN then -- 手环
				self.zjtq_role_model:SetShouHuanResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.HALO then -- 光环
				self.zjtq_role_model:SetHaloResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.WING then -- 羽翼
				self.zjtq_role_model:SetWingResid(res_id, true)
			elseif data.param1 == SHIZHUANG_TYPE.FABAO then -- 法宝
				self.zjtq_role_model:SetBaoJuResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.JIANZHEN then -- 剑阵
				self.zjtq_role_model:SetJianZhenResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.SHENBING then -- 武器
				res_id = AppearanceWGData.GetFashionWeaponIdByResViewId(res_id)
				self.zjtq_role_model:SetWeaponResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.FOOT then -- 足迹
				self.zjtq_role_model:SetFootTrailModel(res_id)
				self.zjtq_role_model:SetRotation(MODEL_ROTATION_TYPE.FOOT)
				self.zjtq_role_model:PlayRoleAction(SceneObjAnimator.Move)
			end
		end
	elseif data.type == WARDROBE_PART_TYPE.LING_CHONG then -- 灵宠
		fashion_cfg = NewAppearanceWGData.Instance:GetLingChongActCfgByImageId(data.param1)
		if fashion_cfg then
			self:SetZJTQLingChongModelData(WARDROBE_PART_TYPE.LING_CHONG, fashion_cfg.appe_image_id)
		end
	elseif data.type == WARDROBE_PART_TYPE.XIAN_WA then -- 仙娃
		fashion_cfg = MarryWGData.Instance:GetActiveCfgByTypeAndId(data.param1, data.param2)

		if fashion_cfg then
			self:SetZJTQXianWaModelData(fashion_cfg.appe_image_id)
		end
	elseif data.type == WARDROBE_PART_TYPE.MOUNT then -- 坐骑
		fashion_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(data.param1)
		-- if fashion_cfg then
		-- 	self:SetZJTQMountModelData(fashion_cfg.appe_image_id)
		-- end

		if fashion_cfg and model_data.need_show_role == 1 and model_data.need_mount == 1 then
			self.zjtq_role_model:SetMountResid(fashion_cfg.appe_image_id)

			local mount_ridding_type = MOUNT_RIDING_TYPE[1]
			local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(fashion_cfg.appe_image_id)
			if action_cfg and action_cfg.action and MOUNT_RIDING_TYPE[action_cfg.action] then
				mount_ridding_type = MOUNT_RIDING_TYPE[action_cfg.action]
			end
			self.zjtq_role_model:PlayStartAction(mount_ridding_type)
		else
			if fashion_cfg then
				self:SetZJTQMountModelData(fashion_cfg.appe_image_id)
			end
		end
	elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then -- 化鲲
		fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
		-- if fashion_cfg then
		-- 	self:SetZJTQMountModelData(fashion_cfg.active_id)
		-- end

		if fashion_cfg and model_data.need_show_role == 1 and model_data.need_mount == 1 then
			self.zjtq_role_model:SetMountResid(fashion_cfg.appe_image_id)
		
			local mount_ridding_type = MOUNT_RIDING_TYPE[1]
			local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(fashion_cfg.appe_image_id)
			if action_cfg and action_cfg.action and MOUNT_RIDING_TYPE[action_cfg.action] then
				mount_ridding_type = MOUNT_RIDING_TYPE[action_cfg.action]
			end
			self.zjtq_role_model:PlayStartAction(mount_ridding_type)
		else
			if fashion_cfg then
				self:SetZJTQMountModelData(fashion_cfg.appe_image_id)
			end
		end
	elseif data.type == 6 then
		-- 武器
		local weapon_res_id, _ = RoleWGData.GetFashionWeaponId(nil, nil, data.param1)
		local bundle, asset = ResPath.GetWeaponModelRes(weapon_res_id)

		if model_data.need_show_role == 1 then
			self.zjtq_role_model:SetWeaponModel(bundle, asset)
		else
			if not self.zjtq_weapon_model then
				self.zjtq_weapon_model = RoleModel.New()
				self.zjtq_weapon_model:SetUISceneModel(self.node_list["zjtq_weapon_display"].event_trigger_listener,
				MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
				self:AddUiRoleModel(self.zjtq_weapon_model, TabIndex.pri_col_zjtq)
			end

			self.zjtq_weapon_model:SetMainAsset(bundle, asset)
		end
	end
end

function PrivilegeCollectionView:SetZJTQLingChongModelData(type, res_id)
	self.node_list["zjtq_lc_display"]:SetActive(true)
	if not self.zjtq_lingchong_model then
		self.zjtq_lingchong_model = RoleModel.New()
		self.zjtq_lingchong_model:SetUISceneModel(self.node_list["zjtq_lc_display"].event_trigger_listener,
		MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.zjtq_lingchong_model, TabIndex.pri_col_zjtq)
	else
		if self.zjtq_lingchong_model then
			self.zjtq_lingchong_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetPetModel(res_id)

	self.zjtq_lingchong_model:SetMainAsset(bundle, asset, function()
		self.zjtq_lingchong_model:PlaySoulAction()
	end)

	self.zjtq_lingchong_model:FixToOrthographic(self.root_node_transform)
end

function PrivilegeCollectionView:SetZJTQXianWaModelData(res_id)
	self.node_list["zjtq_xw_display"]:SetActive(true)
	if nil == self.zjtq_xianwa_model then
		self.zjtq_xianwa_model = RoleModel.New()

		self.zjtq_xianwa_model:SetUISceneModel(self.node_list["zjtq_xw_display"].event_trigger_listener,
		MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.zjtq_xianwa_model, TabIndex.pri_col_zjtq)
	else
		if self.zjtq_xianwa_model then
			self.zjtq_xianwa_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetHaiZiModel(res_id)
	self.zjtq_xianwa_model:SetMainAsset(bundle, asset, function()
		self.zjtq_xianwa_model:PlaySoulAction()
	end)

	self.zjtq_xianwa_model:FixToOrthographic(self.root_node_transform)
end

function PrivilegeCollectionView:SetZJTQMountModelData(res_id)
    self.node_list["zjtq_mount_display"]:SetActive(true)
	if not self.zjtq_mount_model then
		self.zjtq_mount_model = RoleModel.New()

        self.zjtq_mount_model:SetUISceneModel(self.node_list["zjtq_mount_display"].event_trigger_listener,
		MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.zjtq_mount_model, TabIndex.pri_col_zjtq)
	else
		if self.zjtq_mount_model then
			self.zjtq_mount_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetMountModel(res_id)
	self.zjtq_mount_model:SetMainAsset(bundle, asset, function()
		self.zjtq_mount_model:PlayMountAction()
	end)

	self.zjtq_mount_model:FixToOrthographic(self.root_node_transform)
end

function PrivilegeCollectionView:ChangeZJTQModelShowScale(theme_seq)
	local data = PrivilegeCollectionWGData.Instance:GetShowModelThemeCfgBySeq(theme_seq)
	if IsEmptyTable(data) then
		return
	end

	-- local pos_str = data.main_whole_display_pos
	-- if pos_str and pos_str ~= "" then
	-- 	local pos = Split(pos_str, "|")
	-- 	RectTransform.SetAnchoredPosition3DXYZ(self.node_list.zjtq_role_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
	-- 	RectTransform.SetAnchoredPosition3DXYZ(self.node_list.zjtq_weapon_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
	-- end

	local prof = RoleWGData.Instance:GetRoleProf()
	local sex = RoleWGData.Instance:GetRoleSex()

	local pos_str = data["main_pos_" .. prof.."_"..sex] or data.main_pos
	if pos_str and pos_str ~= "" then
		local pos = Split(pos_str, "|")
		if self.zjtq_role_model then
			self.zjtq_role_model:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end

		if self.zjtq_weapon_model then
			self.zjtq_weapon_model:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end
	end

	local rotate_str = data["main_rot_" .. prof.."_"..sex] or data.main_rot
	if rotate_str and rotate_str ~= "" then
		local rot = Split(rotate_str, "|")
		if self.zjtq_role_model then
			self.zjtq_role_model:SetUSAdjustmentNodeLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
		end

		if self.zjtq_weapon_model then
			self.zjtq_weapon_model:SetUSAdjustmentNodeLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
		end
	end

	local scale = data["main_scale_" .. prof.."_"..sex] or data.main_scale
	if scale and scale ~= "" then
		if self.zjtq_role_model then
			self.zjtq_role_model:SetUSAdjustmentNodeLocalScale(scale)
		end

		if self.zjtq_weapon_model then
			self.zjtq_weapon_model:SetUSAdjustmentNodeLocalScale(scale)
		end
	end

	--灵宠
	if self.node_list["zjtq_lc_display"]:GetActive() then
		pos_str = data.pet_whole_display_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			RectTransform.SetAnchoredPosition3DXYZ(self.node_list.zjtq_lc_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end

		pos_str = data.pet_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			if self.zjtq_lingchong_model then
				self.zjtq_lingchong_model:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
			end
		end

		rotate_str = data.pet_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.zjtq_lingchong_model then
				self.zjtq_lingchong_model:SetRTAdjustmentRootLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
			end
		end

		scale = data.lc_scale
		if scale and scale ~= "" then
			if self.zjtq_lingchong_model then
				self.zjtq_lingchong_model:SetUSAdjustmentNodeLocalScale(scale)
			end
		end
	end

	--仙娃
	if self.node_list["zjtq_xw_display"]:GetActive() then
		pos_str = data.xw_whole_display_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			RectTransform.SetAnchoredPosition3DXYZ(self.node_list.zjtq_xw_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end

		pos_str = data.xw_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			if self.zjtq_xianwa_model then
				self.zjtq_xianwa_model:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
			end
		end

		rotate_str = data.xw_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.zjtq_xianwa_model then
				self.zjtq_xianwa_model:SetUSAdjustmentNodeLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
			end
		end

		scale = data.xw_scale
		if scale and scale ~= "" then
			if self.zjtq_xianwa_model then
				self.zjtq_xianwa_model:SetUSAdjustmentNodeLocalScale(scale)
			end
		end
	end

	--坐骑
	if self.node_list["zjtq_mount_display"]:GetActive() then
		pos_str = data.mount_whole_display_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			RectTransform.SetAnchoredPosition3DXYZ(self.node_list.zjtq_mount_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end

		pos_str = data.mount_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			if self.zjtq_mount_model then
				self.zjtq_mount_model:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
			end
		end

		rotate_str = data.mount_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.zjtq_mount_model then
				self.zjtq_mount_model:SetUSAdjustmentNodeLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
			end
		end

		scale = data.mount_scale
		if scale and scale ~= "" then
			if self.zjtq_mount_model then
				self.zjtq_mount_model:SetUSAdjustmentNodeLocalScale(scale)
			end
		end
	end
end

function PrivilegeCollectionView:OnClickTQSDBuy()
    if IsEmptyTable(self.tzsd_select_suit_data) then
        return
    end

	if not PrivilegeCollectionWGData.Instance:IsSuitSeqIsUnLock(self.tzsd_select_suit_data.cfg.seq) then
		local last_cfg = PrivilegeCollectionWGData.Instance:GetCurSuitShopDataBySeq(self.tzsd_select_suit_data.cfg.last_seq)
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.PrivilegeCollection.TZSDUnLockTipStr, last_cfg.suit_name))
		return
	end

    local cfg = self.tzsd_select_suit_data.cfg
    local cur_score = PrivilegeCollectionWGData.Instance:GetCurScore()

    if cfg.buy_type == 1 then
        RechargeWGCtrl.Instance:Recharge(cfg.price, cfg.rmb_type, cfg.rmb_seq)
        return
    elseif cfg.buy_type == 2 then
        if cur_score < cfg.price then
            TipWGCtrl.Instance:ShowSystemMsg(Language.PrivilegeCollection.NoEnoughScore)
            RechargeWGCtrl.Instance:RemindRechargeByCangJinShangPuScoreNoEnough(cfg.price - cur_score)
            return
        end
    end

	TipWGCtrl.Instance:OpenAlertTips(string.format(Language.PrivilegeCollection.TZSDPayRechargeScoreStr, cfg.price, cur_score), 
		function()
			PrivilegeCollectionWGCtrl.Instances:SendOperateRequest(YANYUGE_OPERA_TYPE.BUY_SUIT, cfg.seq, 1)
		end
	)
end

function PrivilegeCollectionView:OnClickSkillShow()
    if IsEmptyTable(self.tzsd_select_suit_data) then
        return
    end

	local cfg = self.tzsd_select_suit_data.cfg

	if cfg.show_skilll_id > 0 then
		TipWGCtrl.Instance:OpenCommonSkillPreView(cfg.show_skilll_id)
	end
end
---------------------------------------PRICOLZJTQItemCellRender--------------------------------------
PRICOLZJTQItemCellRender = PRICOLZJTQItemCellRender or BaseClass(BaseRender)

function PRICOLZJTQItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    -- self.node_list.desc_name.text.text = self.data.cfg.suit_name
	-- self.node_list.desc_name_hl.text.text = self.data.cfg.suit_name
    -- if self.data.cfg.suit_icon and "" ~= self.data.cfg.suit_icon then
    --     local bundle, asset = ResPath.GetYanYuGeImg(self.data.cfg.suit_icon)
    --     self.node_list.icon.image:LoadSprite(bundle, asset, function ()
    --         self.node_list.icon.image:SetNativeSize()
    --     end)
    -- end

    local cfg = self.data.cfg
    local limit_buy_times = cfg.buy_limit - self.data.buy_times
    -- local score = PrivilegeCollectionWGData.Instance:GetCurScore()
    -- self.node_list.remind:CustomSetActive(limit_buy_times > 0 and score >= cfg.price)

	local bundle, asset = ResPath.GetRawImagesPNG(self.data.cfg.zjtq_normal_bg)
	self.node_list.normal_bg.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["normal_bg"].raw_image:SetNativeSize()
    end)

	local select_bundle, select_asset = ResPath.GetRawImagesPNG(self.data.cfg.zjtq_select_bg)
	self.node_list.select_bg.raw_image:LoadSprite(select_bundle, select_asset, function ()
        self.node_list["select_bg"].raw_image:SetNativeSize()
    end)

	-- local name_bg_bundle, name_bg_asset = ResPath.GetRawImagesPNG(self.data.cfg.zjtq_nav_name_bg)
	-- self.node_list.select_name_bg.raw_image:LoadSprite(name_bg_bundle, name_bg_asset, function ()
    --     self.node_list["select_name_bg"].raw_image:SetNativeSize()
    -- end)

	-- local name_bg_bundle, name_bg_asset = ResPath.GetRawImagesPNG(self.data.cfg.zjtq_name_bg)
	-- self.node_list.name_bg.raw_image:LoadSprite(name_bg_bundle, name_bg_asset, function ()
    --     self.node_list["name_bg"].raw_image:SetNativeSize()
    -- end)

	-- self.node_list.name_bg_hl.raw_image:LoadSprite(name_bg_bundle, name_bg_asset, function ()
    --     self.node_list["name_bg_hl"].raw_image:SetNativeSize()
    -- end)

	local effect_bundle, effect_asset = ResPath.GetUIEffect(cfg.button_effect)
	self.node_list.select_bg_effect:ChangeAsset(effect_bundle, effect_asset)
end

function PRICOLZJTQItemCellRender:OnSelectChange(is_select)
    self.node_list.normal_bg:CustomSetActive(not is_select)
    self.node_list.select_bg:CustomSetActive(is_select)
end