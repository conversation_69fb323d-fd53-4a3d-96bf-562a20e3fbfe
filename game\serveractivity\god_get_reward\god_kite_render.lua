KiteRender = KiteRender or BaseClass(BaseRender)
function KiteRender:__init()
    if not self.func_list then
        self.func_list = {}
    end
end

function KiteRender:__delete()
    if self.cell then
        self.cell:DeleteMe()
        self.cell = nil
    end

    self.is_best = nil
    self.kite_state = nil
    self.func_list = {}

    if self.effect_disable_timequest then
        GlobalTimerQuest:CancelQuest(self.effect_disable_timequest)
        self.effect_disable_timequest = nil
    end
end

function KiteRender:LoadCallBack()
    self.kite_state = nil
    self.load_callback = true
    if self.node_list.effectobj then
        self.node_list.effectobj:SetActive(false)
    end
    self:DoDelayFunc()
end

function KiteRender:OnFlush()
    for i = 1, 6 do
        self.node_list["kite_bg_" .. i]:SetActive(false)
    end

    local reward_info = GodGetRewardWGData.Instance:GetRewardInfoByRewardID(self.data)
    self:GetCell()
    self.cell:SetData(reward_info.reward_item)
    local item_id = reward_info.reward_item.item_id
    local item_cell_cfg = ItemWGData.Instance:GetItemConfig(item_id)
    local item_color = 1
    if item_cell_cfg and item_cell_cfg.color then
        item_color = item_cell_cfg.color
    end

    if self.node_list["kite_bg_" .. item_color] then
        self.node_list["kite_bg_" .. item_color]:SetActive(true)
    end

    self:SetPosition(reward_info.x_pos, reward_info.y_pos)

    self.is_best = reward_info.is_best == 1
    if reward_info.is_l_kite == -1 then
        self:SetCenterImg(true)
    else
        self:SetKiteImg(reward_info.is_l_kite, reward_info.reward_item)
    end
end

function KiteRender:GetCell()
    if not self.cell then
        self.cell = ItemCell.New(self.node_list["cell"])
    end
end

function KiteRender:GetKiteState()
    return self.kite_state and true or false
end

function KiteRender:SetKiteState(state)
    if not self.load_callback then
        self:AddDelay(BindTool.Bind(self.SetKiteState, self, state))
        return
    end
    self.kite_state = state
    self.node_list["kite_root"]:SetActive(state)
    if state then
        self.node_list["kite_root"].canvas_group.alpha = 1
         self.node_list["kite_root"].rect.localRotation = Quaternion.Euler(0,0,0)
    end
end

function KiteRender:SetCenterImg(state)
    self.node_list["frame"]:SetActive(state)
    --self.node_list["kite_l"]:SetActive(not state)
    --self.node_list["kite_r"]:SetActive(not state)
    self.node_list["cell"].rect.localScale = Vector3(1,1,1)
end

function KiteRender:SetPosition(x, y)
    self.node_list["kite_root"].rect.anchoredPosition = Vector2(x, y)
end

function KiteRender:SetKiteImg(is_l, item)
    self.node_list["cell"].rect.localScale = Vector3(0.7,0.7,0.7)
    self.node_list["frame"]:SetActive(false)
    local cfg = item and ItemWGData.Instance:GetItemConfig(item.item_id)
    local color = cfg and cfg.color or 1
    if is_l == 0 then
        --self.node_list["kite_l"]:SetActive(true)
        --self.node_list["kite_r"]:SetActive(false)
        --self.node_list["kite_l"].image:LoadSprite(ResPath.GetTSActivityImg("kite_" .. color))
    else
        --self.node_list["kite_l"]:SetActive(false)
        --self.node_list["kite_r"]:SetActive(true)
        --self.node_list["kite_r"].image:LoadSprite(ResPath.GetTSActivityImg("kite_" .. color))
    end
end

function KiteRender:PlayDropAnim(time)
    self.node_list["kite_root"].rect:DORotate(Vector3(-180, 0, 0), 0.05):SetEase(DG.Tweening.Ease.Linear)
    self.node_list["kite_root"].rect:DOAnchorPosY(-300, time):SetEase(DG.Tweening.Ease.Linear)
    self.node_list["kite_root"].canvas_group:DoAlpha(1, 0, time):SetEase(DG.Tweening.Ease.Linear)
    self.node_list.effectobj:SetActive(true)

    if self.effect_disable_timequest then
        GlobalTimerQuest:CancelQuest(self.effect_disable_timequest)
        self.effect_disable_timequest = nil
    end
    self.effect_disable_timequest = GlobalTimerQuest:AddDelayTimer(function()
        self.node_list.effectobj:SetActive(false)
    end, 0.3)
end

function KiteRender:AddDelay(func)
    table.insert(self.func_list, func)
end

function KiteRender:DoDelayFunc()
    if not self.func_list then
        self.func_list = {}
    end
    for i, v in ipairs(self.func_list) do
        v()
    end
    self.func_list = {}
end
