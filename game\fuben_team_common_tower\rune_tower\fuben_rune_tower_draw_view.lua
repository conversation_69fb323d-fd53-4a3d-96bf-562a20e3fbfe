FuBenRuneTowerDrawView = FuBenRuneTowerDrawView or BaseClass(SafeBaseView)
function FuBenRuneTowerDrawView:__init()
    self:SetMaskBg(false, false)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_fuben_rune_tower_draw")
end

function FuBenRuneTowerDrawView:OpenCallBack()

end

function FuBenRuneTowerDrawView:CloseCallBack()
end

function FuBenRuneTowerDrawView:LoadCallBack()
    if self.nor_draw_reward_list == nil then
        self.nor_draw_reward_list = {}
        local node_num = self.node_list["nor_draw_reward_list"].transform.childCount
        for i = 0, node_num do
            self.nor_draw_reward_list[i] = FuBenRuneTowerDrawRender.New(self.node_list["nor_draw_reward_list"]:FindObj("nor_draw_reward_cell_" .. i))
        end
    end

    if self.spe_draw_reward_list == nil then
        self.spe_draw_reward_list = {}
        local node_num = self.node_list["spe_draw_reward_list"].transform.childCount
        for i = 0, node_num do
            self.spe_draw_reward_list[i] = FuBenRuneTowerDrawRender.New(self.node_list["spe_draw_reward_list"]:FindObj("spe_draw_reward_cell_" .. i))
        end
    end

    XUI.AddClickEventListener(self.node_list.reward_info_btn, BindTool.Bind(self.RewardInfoClick, self))
end

function FuBenRuneTowerDrawView:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("rune_tower_draw_end_time") then
        CountDownManager.Instance:RemoveCountDown("rune_tower_draw_end_time")
    end

    if self.nor_draw_reward_list then
        for k, v in pairs(self.nor_draw_reward_list) do
            v:DeleteMe()
        end
        self.nor_draw_reward_list = nil
    end

    if self.spe_draw_reward_list then
        for k, v in pairs(self.spe_draw_reward_list) do
            v:DeleteMe()
        end
        self.spe_draw_reward_list = nil
    end

    self:CancelDelayFlushTimer()
end

function FuBenRuneTowerDrawView:ShowIndexCallBack()

end

function FuBenRuneTowerDrawView:OnFlush(param_t, index)
    for k,v in pairs(param_t) do
        if k == "all" then
            self:SetDrawInfo()
            self:OnFlushOtherInfo()
            self:FlushEndTime()
        elseif k == "change_draw" then
            self:OnFlushChangeDraw(v.change_info)
        elseif k == "draw_state_anim" then
            self:OnFlushDrawState()
        end
    end
end

function FuBenRuneTowerDrawView:SetDrawInfo()
    local nor_draw_reward_list, spe_draw_reward_list = FuBenTeamCommonTowerWGData.Instance:GetTeamCommonTowerFBDrawInfo()
    local is_my_nor_draw, is_my_spe_draw = FuBenTeamCommonTowerWGData.Instance:GetTeamCommonTowerFBMyDrawState()
    self.node_list.spe_draw_reward_list:SetActive(is_my_nor_draw)
    for k, v in pairs(self.nor_draw_reward_list) do
        local data = {}
        data.is_nor = true
        data.index = k
        v:SetData(data)
    end

    for k, v in pairs(self.spe_draw_reward_list) do
        local data = {}
        data.is_nor = false
        data.index = k
        v:SetData(data)
    end
end

function FuBenRuneTowerDrawView:OnFlushChangeDraw(change_info)
    for k, v in pairs(change_info) do
        if v.is_nor then
            if self.nor_draw_reward_list[v.change_index] then
                self.nor_draw_reward_list[v.change_index]:PlayTurnAnim()
            end
        else
            if self.spe_draw_reward_list[v.change_index] then
                self.spe_draw_reward_list[v.change_index]:PlayTurnAnim()
            end
        end
    end
end

function FuBenRuneTowerDrawView:OnFlushOtherInfo()
    local is_my_nor_draw, is_my_spe_draw = FuBenTeamCommonTowerWGData.Instance:GetTeamCommonTowerFBMyDrawState()
    local scene_id = Scene.Instance:GetSceneId()
    local scene_fuben_cfg = FuBenTeamCommonTowerWGData.Instance:GetFuBenSceneCfg(scene_id)
    local fb_scene_info = FuBenTeamCommonTowerWGData.Instance:GetTeamCommonTowerFBInfo(scene_fuben_cfg and scene_fuben_cfg.seq or 0)
    local level_cfg = FuBenTeamCommonTowerWGData.Instance:GetLevelCfgByLevel(fb_scene_info.grade or 1, fb_scene_info.level or 1)

    self.node_list.spe_draw_tip.text.text = string.format(Language.RuneTower.AganDrawCost, level_cfg and level_cfg.added_draw_cost_gold or 0)
    self.node_list.spe_draw_tip:SetActive(is_my_nor_draw)
end

function FuBenRuneTowerDrawView:OnFlushDrawState()
    self.node_list.spe_draw_reward_list:SetActive(true)
    ReDelayCall(self, function()
        for i = 0, #self.nor_draw_reward_list do
            self.nor_draw_reward_list[i]:PlayTurnAnim2()
		end

        for i = 0, #self.nor_draw_reward_list do
            self.spe_draw_reward_list[i]:PlayTurnAnim2()
		end

	end, 0.1, "rune_tower_draw_tween_1")

    self:CancelDelayFlushTimer()
    self.flush_delay_time = GlobalTimerQuest:AddDelayTimer(function()
        self:OnFlushOtherInfo()
	end, 2)
end

function FuBenRuneTowerDrawView:CancelDelayFlushTimer()
    if self.flush_delay_time ~= nil then
        GlobalTimerQuest:CancelQuest(self.flush_delay_time)
    end
    self.flush_delay_time = nil
end

function FuBenRuneTowerDrawView:FlushEndTime()
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local end_time = FuBenTeamCommonTowerWGData.Instance:GetTeamCommonTowerFBDrawEndTime()
    local time = end_time - server_time
    if time > 0 then
        if CountDownManager.Instance:HasCountDown("rune_tower_draw_end_time") then
            CountDownManager.Instance:RemoveCountDown("rune_tower_draw_end_time")
        end

		CountDownManager.Instance:AddCountDown("rune_tower_draw_end_time", 
                        BindTool.Bind1(self.UpdateCountDownTime, self), 
                        BindTool.Bind1(self.CompleteCountDownTime, self), 
                        nil, time, 1)
    else
        self:CompleteCountDownTime()
    end
end

function FuBenRuneTowerDrawView:UpdateCountDownTime(elapse_time, total_time)
	if total_time - elapse_time > 0 then
        self.node_list.draw_endtime.text.text = string.format(Language.RuneTower.DrawEndTime, math.floor(total_time - elapse_time))
	end
end

function FuBenRuneTowerDrawView:CompleteCountDownTime()
	self:Close()
end

function FuBenRuneTowerDrawView:RewardInfoClick()
    local scene_id = Scene.Instance:GetSceneId()
    local scene_fuben_cfg = FuBenTeamCommonTowerWGData.Instance:GetFuBenSceneCfg(scene_id)
    if not scene_fuben_cfg then
        return
    end

    local fb_scene_info = FuBenTeamCommonTowerWGData.Instance:GetTeamCommonTowerFBInfo(scene_fuben_cfg and scene_fuben_cfg.seq)
    if not fb_scene_info then
        return
    end

    local level_cfg = FuBenTeamCommonTowerWGData.Instance:GetLevelCfgByLevel(fb_scene_info.grade, fb_scene_info.level)
    if not level_cfg then
        return
    end

    RewardShowViewWGCtrl.Instance:SetRewardShowData({ reward_item_list = level_cfg.pass_reward_item})
end
----------------------------- FuBenRuneTowerDrawRender-------------------
FuBenRuneTowerDrawRender = FuBenRuneTowerDrawRender or BaseClass(BaseRender)
function FuBenRuneTowerDrawRender:__init()

end

function FuBenRuneTowerDrawRender:LoadCallBack()
    if not self.reward_item then
        self.reward_item = ItemCell.New(self.node_list["item_pos"])
    end

    XUI.AddClickEventListener(self.node_list["click_btn"], BindTool.Bind(self.OnClickDraw, self))
end

function FuBenRuneTowerDrawRender:__delete()
    if self.reward_item then
        self.reward_item:DeleteMe()
        self.reward_item = nil
    end

    if self.turning_anim1 then
		GlobalTimerQuest:CancelQuest(self.turning_anim1)
		self.turning_anim1 = nil
	end

	if self.turning_anim2 then
		GlobalTimerQuest:CancelQuest(self.turning_anim2)
		self.turning_anim2 = nil
	end

	if self.turning_anim3 then
		GlobalTimerQuest:CancelQuest(self.turning_anim3)
		self.turning_anim3 = nil
	end
end

function FuBenRuneTowerDrawRender:OnFlush()
	if not self.data then
		return
	end
	
    self:FlusPanleInfo()
    self:SetInitPos()
end

function FuBenRuneTowerDrawRender:FlusPanleInfo()
    if not self.data then
		return
	end

    local draw_info = {}
    local nor_draw_reward_list, spe_draw_reward_list = FuBenTeamCommonTowerWGData.Instance:GetTeamCommonTowerFBDrawInfo()

    if self.data.is_nor then
        draw_info = nor_draw_reward_list[self.data.index] or {}
    else
        draw_info = spe_draw_reward_list[self.data.index] or {}
    end

    if IsEmptyTable(draw_info) then
        self.node_list.nor_img:SetActive(false)
        self.node_list.mask_img:SetActive(true)
        self.node_list.role_name.text.text = ""
        self.node_list.click_btn:SetActive(true)
    else
        self.node_list.nor_img:SetActive(true)
        self.node_list.mask_img:SetActive(false)
        self.node_list.click_btn:SetActive(false)
        self.node_list.role_name.text.text = draw_info.role_name
        self.reward_item:SetData({item_id = draw_info.item_id, num = draw_info.num, is_bind = draw_info.is_bind})
        local color = ItemWGData.Instance:GetItemColor(draw_info.item_id)
        local item_name = ItemWGData.Instance:GetItemName(draw_info.item_id) or ""
        self.node_list["item_name"].text.text = ToColorStr(item_name, color)
    end
end

function FuBenRuneTowerDrawRender:SetInitPos()
    if not self.data then
		return
	end

    local canvas_group = self.node_list.root:GetComponent(typeof(UnityEngine.CanvasGroup))
    local is_my_nor_draw, is_my_spe_draw = FuBenTeamCommonTowerWGData.Instance:GetTeamCommonTowerFBMyDrawState()
    if self.data.is_nor then
        local pos_y = is_my_nor_draw and 158 or 75
        local pos_x = self.node_list.root.transform.anchoredPosition.x
        RectTransform.SetAnchoredPositionXY(self.node_list.root.rect, pos_x, pos_y)
        canvas_group.alpha = 1
    else
        local pos_y = is_my_nor_draw and -110 or -185
        local pos_x = self.node_list.root.transform.anchoredPosition.x
        RectTransform.SetAnchoredPositionXY(self.node_list.root.rect, pos_x, pos_y)
        canvas_group.alpha = is_my_spe_draw and 1 or 0
    end
end

function FuBenRuneTowerDrawRender:OnClickDraw()
    if not self.data then
		return
	end

    local draw_info = {}
    local nor_draw_reward_list, spe_draw_reward_list = FuBenTeamCommonTowerWGData.Instance:GetTeamCommonTowerFBDrawInfo()
    local is_my_nor_draw, is_my_spe_draw = FuBenTeamCommonTowerWGData.Instance:GetTeamCommonTowerFBMyDrawState()
    if self.data.is_nor then
        draw_info = nor_draw_reward_list[self.data.index] or {}
        if IsEmptyTable(draw_info) and (not is_my_nor_draw) then
            RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_TEAM_COMMON_TOWER_FB_DRAW, self.data.index, 0)
        end
    else
        draw_info = spe_draw_reward_list[self.data.index] or {}
        if IsEmptyTable(draw_info) and (not is_my_spe_draw) then
            local scene_id = Scene.Instance:GetSceneId()
            local scene_fuben_cfg = FuBenTeamCommonTowerWGData.Instance:GetFuBenSceneCfg(scene_id)
            if not scene_fuben_cfg then
                return
            end
            local fb_scene_info = FuBenTeamCommonTowerWGData.Instance:GetTeamCommonTowerFBInfo(scene_fuben_cfg.seq)
            if not fb_scene_info then
                return
            end

            local level_cfg = FuBenTeamCommonTowerWGData.Instance:GetLevelCfgByLevel(fb_scene_info.grade, fb_scene_info.level)
            if not level_cfg then
                return
            end

            local have_enough = RoleWGData.Instance:GetIsEnoughUseGold(level_cfg.added_draw_cost_gold)
            local str = string.format(Language.RuneTower.AganDrawCost1, level_cfg.added_draw_cost_gold)
            local ok_func = function ()
                if have_enough then
                    RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_TEAM_COMMON_TOWER_FB_DRAW, self.data.index, 1)
                else
                    VipWGCtrl.Instance:OpenTipNoGold()
                end
            end

            TipWGCtrl.Instance:OpenAlertTips(str, ok_func)
        end
    end
end

function FuBenRuneTowerDrawRender:PlayTurnAnim()
    self.node_list.nor_img:SetActive(false)
    self.node_list.click_btn:SetActive(false)
    self.node_list.mask_img:SetActive(true)
    self.node_list.role_name.text.text = ""
    self.node_list.mask_img.transform.rotation = Quaternion.Euler(0, 0, 0)

    local draw_info = {}
    local nor_draw_reward_list, spe_draw_reward_list = FuBenTeamCommonTowerWGData.Instance:GetTeamCommonTowerFBDrawInfo()

    if self.data.is_nor then
        draw_info = nor_draw_reward_list[self.data.index] or {}
    else
        draw_info = spe_draw_reward_list[self.data.index] or {}
    end

    local base_time = 0
    local time1 = 0.1
    local time2 = 0.1

    self.turning_anim1 = GlobalTimerQuest:AddDelayTimer(function ()
        self.node_list["mask_img"].rect:DORotate(Vector3(0, 90, 0), time1, DG.Tweening.RotateMode.FastBeyond360)
	end, base_time)

    self.turning_anim2 = GlobalTimerQuest:AddDelayTimer(function ()
        self.node_list["mask_img"].rect:DORotate(Vector3(0, 0, 0), time2, DG.Tweening.RotateMode.FastBeyond360)
        self:FlusPanleInfo()
	end, base_time + time1)

    self.turning_anim3 = GlobalTimerQuest:AddDelayTimer(function ()
        if not IsEmptyTable(draw_info) and self.data.is_nor then
            if draw_info.uuid == RoleWGData.Instance:GetUUid() then
                FuBenTeamCommonTowerWGCtrl.Instance:FlushRuneTowerDrawStateAnim()
            end
        end
    end, base_time + time1 + time2)
end

function FuBenRuneTowerDrawRender:PlayTurnAnim2() 
    if not self.data then
		return
	end

    if (not self.node_list) or (not self.node_list.root) then
        return
    end

    local do_delay_time = 0.2
    if self.data.is_nor then
        self.cell_delay_key = "nor_draw_rune_tower" .. self.data.index
        ReDelayCall(self, function()
            self.node_list["root"].transform:DOLocalMoveY(158, 0.2)
        end, do_delay_time * self.data.index, self.cell_delay_key)
    else
        local canvas_group = self.node_list.root:GetComponent(typeof(UnityEngine.CanvasGroup))
        canvas_group.alpha = 0
        self.cell_delay_key = "spe_draw_rune_tower" .. self.data.index
        ReDelayCall(self, function()
            self.node_list["root"].transform:DOLocalMoveY(-110, 0.2)
            canvas_group:DoAlpha(0, 1, 0.2)
        end, (do_delay_time * self.data.index) + 1, self.cell_delay_key)
    end
end