----------------------------------------------------
-- 兑换狂欢
----------------------------------------------------
ActConvertCrazyView = ActConvertCrazyView or BaseClass(ActBaseViewTwo)

function ActConvertCrazyView:__init(act_id)
	self.ui_config = {"uis/view/act_subview_ui_prefab", "ActSubviewUi"}
	self.config_tab = {
		{"layout_convert_crazy",{0}},
	}
	self.act_id = act_id
	self.open_tween = nil
	self.close_tween = nil
	self.data_change_event = BindTool.Bind(self.ItemDataChange, self)
end

function ActConvertCrazyView:__delete()

end

function ActConvertCrazyView:ReleaseCallBack()
	if self.reward_list ~= nil then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.data_change_event)
	self.reward_cfg = nil
	self.has_load_callback = nil
	self.need_refresh = nil
end

function ActConvertCrazyView:LoadCallBack()
	local bundle = "uis/view/act_subview_ui_prefab"
	local asset = "ph_convert_crazy"
	self.reward_list = AsyncBaseGrid.New()
	self.reward_list:CreateCells({
		col = 2,
		change_cells_num = 1,
		assetName = asset,
		assetBundle = bundle,
		itemRender = ConvertCrazyItemRender,
		list_view = self.node_list["ph_convert_crazy_list"]
	})

	ItemWGData.Instance:NotifyDataChangeCallBack(self.data_change_event)
	self.has_load_callback = true
	if self.need_refresh then
		self.need_refresh = false
		self:RefreshView()
	end
end

function ActConvertCrazyView:ItemDataChange( change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if new_num and old_num and new_num > old_num then
		local item_id = self.reward_cfg and self.reward_cfg[1].consume_stuff_id or nil
		if item_id and item_id == change_item_id then
			self:RefreshView()
		end
	end
end

function ActConvertCrazyView:RefreshView(param_list)
	if not self.has_load_callback then
		self.need_refresh = true
		return
	end

	self:RefreshTopDesc()
	local reward_cfg = ServerActivityWGData.Instance:GetConvertCrazyListData()
	self.reward_cfg = reward_cfg
	self.reward_list:SetStartZeroIndex(false)
	self.reward_list:SetDataList(reward_cfg,3)

	if param_list then
		if param_list.flush_param == "change_index" then
			self.reward_list:ReloadData(0)
		end
	end
end

function ActConvertCrazyView:UpdateCountDownTime(elapse_time, total_time)
	local last_time = math.floor(total_time - elapse_time)
	local tip_str = self.act_status.status == ACTIVITY_STATUS.CLOSE and Language.Activity.ActClosing or Language.Activity.ActPreparing
	self.node_list.wb_next_flush_time.text.text = (tip_str..TimeUtil.FormatSecond2HMS(last_time))
end

function ActConvertCrazyView:CompleteCountDownTime(is_auto_fuhuo)
	self:RefreshView()
end

-------------------------------------------------------------------
------------------         itemRender           -------------------
-------------------------------------------------------------------
ConvertCrazyItemRender = ConvertCrazyItemRender or BaseClass(BaseRender)
function ConvertCrazyItemRender:__init()

end

function ConvertCrazyItemRender:__delete()

end

function ConvertCrazyItemRender:ReleaseCallBack()
	if nil ~= self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
	self.bind_show_icon = nil
end

function ConvertCrazyItemRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.ph_cell)

	XUI.AddClickEventListener(self.node_list.btn_exchange, BindTool.Bind1(self.OnClickExchange, self))
end

function ConvertCrazyItemRender:OnFlush()
	if IsEmptyTable(self.data) then return end
	self.item_cell:SetData(self.data.reward_item)
	local info = ServerActivityWGData.Instance:GetCacheActRewardData(ServerActClientId.RAND_ACTIVITY_TYPE_CONVERT_CRAZY)
	local item_data_instance = ItemWGData.Instance
	local item_num = item_data_instance:GetItemNumInBagById(self.data.consume_stuff_id)

	local can_convert_num = info.cur_value_t.can_convert_num_list[self.data.seq + 1]    --可以兑换物品数量
	local already_convert_num = info.cur_value_t.convert_info_list[self.data.seq + 1]	--已兑换的物品数量
	local boo = (item_num < self.data.consume_stuff_num or can_convert_num <= already_convert_num) 	--true == 材料不足
	local color = boo and COLOR3B.RED or COLOR3B.GREEN
	local num_desc = ToColorStr(item_num .. "/" .. self.data.consume_stuff_num,color)
	local item = item_data_instance:GetItemConfig(self.data.reward_item.item_id)
	local tmp = item_data_instance:GetItemConfig(self.data.consume_stuff_id)
	if nil == self.bind_show_icon then
		self.bind_show_icon = BindTool.Bind(self.OnClickIcon, self, self.data.consume_stuff_id)
		XUI.AddClickEventListener(self.node_list["need_icon"], BindTool.Bind(self.OnClickIcon, self, tmp.id))
	end
	if nil ~= tmp then
		local bundle, asset = ResPath.GetItem(tmp.icon_id)
		self.node_list.need_icon.image:LoadSprite(bundle, asset)
	else
		print_error("read the cfg by item_id error", self.data.consume_stuff_id)
	end
	self.node_list.lbl_name.text.text = item and item.name or ""
	self.node_list.lbl_desc.text.text = num_desc
	self.node_list.lbl_num.text.text = info.cur_value_t.can_convert_num_list[self.data.seq + 1] - info.cur_value_t.convert_info_list[self.data.seq + 1]

	XUI.SetButtonEnabled(self.node_list.btn_exchange, not boo)
end

function ConvertCrazyItemRender:OnClickIcon( item_id )
	TipWGCtrl.Instance:OpenItem({item_id = item_id}, ItemTip.FROM_NORMAL, nil)
end

function ConvertCrazyItemRender:OnClickExchange()
	local item_data_instance = ItemWGData.Instance
	local item_num = item_data_instance:GetItemNumInBagById(self.data.consume_stuff_id)
	local info = ServerActivityWGData.Instance:GetCacheActRewardData(ServerActClientId.RAND_ACTIVITY_TYPE_CONVERT_CRAZY)
	local can_convert_num = info.cur_value_t.can_convert_num_list[self.data.seq + 1] 	--可以兑换物品数量
	local already_convert_num = info.cur_value_t.convert_info_list[self.data.seq + 1]	--已兑换的物品数量

	if item_num < self.data.consume_stuff_num then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.ItemNoEnough, item_data_instance:GetItemName(self.data.consume_stuff_id)))
		return
	end

	if can_convert_num < already_convert_num then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ErrorTips.MaxLimit)
		return
	end

	local param_t ={}
	param_t = {
		rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CONVERT_CRAZY,
		opera_type = RA_TOTAL_CHONGZHI_DAY_OPERA_TYPE.RA_TOTAL_CHONGZHI_DAY_OPERA_TYPE_FETCH_REWARD,
		param_1 = self.data.seq
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
	AudioService.Instance:PlayRewardAudio()
end
