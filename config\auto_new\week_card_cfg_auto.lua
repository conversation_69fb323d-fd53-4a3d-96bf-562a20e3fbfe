-- Z-周卡.xls
local item_table={
[1]={item_id=30447,num=9,is_bind=1},
[2]={item_id=39470,num=2,is_bind=1},
[3]={item_id=22532,num=1,is_bind=1},
[4]={item_id=30447,num=6,is_bind=1},
[5]={item_id=22531,num=2,is_bind=1},
[6]={item_id=48071,num=2,is_bind=1},
[7]={item_id=26367,num=2,is_bind=1},
[8]={item_id=26368,num=2,is_bind=1},
[9]={item_id=28719,num=1,is_bind=1},
[10]={item_id=91450,num=1,is_bind=1},
}

return {
grade={
{},
{rmb_seq=1,week_card_day=999,week_card_first_day_reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3]},week_card_reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3]},week_card_price=328,buff_virtual_gold_2_daily_use_limit=6000,show_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3]},grade_addition="1,10000|2,10000|3,10000|4,10000|5,10000|6,10000",}
},

grade_meta_table_map={
},
open_day={
{},
{start_day=8,end_day=9999,},
{rmb_seq=1,},
{rmb_seq=1,}
},

open_day_meta_table_map={
[4]=2,	-- depth:1
},
grade_default_table={rmb_seq=0,week_card_day=7,week_card_first_day_reward_item={[0]=item_table[4],[1]=item_table[2],[2]=item_table[5]},week_card_reward_item={[0]=item_table[4],[1]=item_table[2],[2]=item_table[5]},max_replace_day=7,rmb_type=136,week_card_price=68,rmb_seq1=1,week_card_discount_price=25,forever_buff_need_times=7,buff_virtual_gold_2_daily_get_limit=0,buff_virtual_gold_2_daily_use_limit=4000,open=1,show_item={[0]=item_table[4],[1]=item_table[2],[2]=item_table[5]},cehua_model=15,zhekou=8,grade_addition="1,5000|2,5000|3,5000|4,5000",},

open_day_default_table={rmb_seq=0,start_day=1,end_day=7,rmb_type=137,rmb_seq=0,price=12,rmb_buy_reward_item={[0]=item_table[6],[1]=item_table[7],[2]=item_table[8],[3]=item_table[9]},reward_item={[0]=item_table[8],[1]=item_table[9]},rmb_buy_reward_item_show={[0]=item_table[10],[1]=item_table[6],[2]=item_table[7],[3]=item_table[8],[4]=item_table[9]},}

}

