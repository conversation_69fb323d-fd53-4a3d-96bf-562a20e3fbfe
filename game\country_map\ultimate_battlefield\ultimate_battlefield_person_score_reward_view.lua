UltimateBattlefieldPersonScoreRewardView = UltimateBattlefieldPersonScoreRewardView or BaseClass(SafeBaseView)
function UltimateBattlefieldPersonScoreRewardView:__init()
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(4, 13), sizeDelta = Vector2(968, 557)})
	self:AddViewResource(0, "uis/view/country_map_ui/ultimate_battlefield_prefab", "layout_reward_preview")
	self.view_layer = UiLayer.Pop
end

function UltimateBattlefieldPersonScoreRewardView:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function UltimateBattlefieldPersonScoreRewardView:LoadCallBack()
    self.reward_list = AsyncListView.New(PersonScoreRewardRender, self.node_list.reward_list)

    self.node_list.title_view_name.text.text = Language.YeZhanWangCheng.RewardPreView
	self.node_list["tabbar_1"].toggle:AddClickListener(BindTool.Bind(self.OnClickbar, self, 1))
	self.node_list["tabbar_2"].toggle:AddClickListener(BindTool.Bind(self.OnClickbar, self, 2))
    self.bar_index = 1
end

function UltimateBattlefieldPersonScoreRewardView:OnClickbar(index)
	self.bar_index = index
	self:Flush()
end

function UltimateBattlefieldPersonScoreRewardView:OnFlush()
    local title_rank_str = self.bar_index == 1 and Language.UltimateBattlefield.ScoreTitleDesc or Language.UltimateBattlefield.ScorePersonTitleDesc
    local title_reward_str = self.bar_index == 1 and Language.UltimateBattlefield.ScoreTitleReward or Language.UltimateBattlefield.ScoreTitlePersonReward
    local per_tip_str = self.bar_index == 1 and Language.UltimateBattlefield.ScoreRewardTips or Language.UltimateBattlefield.ScorePersonRewardTips
    local rank_info = UltimateBattlefieldWGData.Instance:GetMyRankInfo()
    local rank = Language.YeZhanWangCheng.Rank4

    if rank_info then
        rank = string.format(Language.YeZhanWangCheng.Rank3, rank_info.rank)
    end

	self.node_list.my_rank_txt.text.text = rank
    self.node_list.title_rank_txt.text.text = title_rank_str
    self.node_list.title_reward_txt.text.text = title_reward_str
    self.node_list.txt_per_tip.text.text = per_tip_str

    local rank_list = nil
    if self.bar_index == 1 then
        rank_list = UltimateBattlefieldWGData.Instance:GetAllScoreRankReward()
    else
        rank_list = UltimateBattlefieldWGData.Instance:GetAllScoreReward()
    end

    if rank_list then
        self.reward_list:SetDataList(rank_list)
    end
end


-------------------------ItemRender------------------------------ 查看奖励
PersonScoreRewardRender = PersonScoreRewardRender or BaseClass(BaseRender)

function PersonScoreRewardRender:__init()
end

function PersonScoreRewardRender:__delete()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end

function PersonScoreRewardRender:LoadCallBack()
    if not self.item_list then
	    self.item_list = AsyncListView.New(ItemCell, self.node_list.reward_item_list)
    end
end

function PersonScoreRewardRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local real_reward_item = {}
    for _, reward_data in pairs(self.data.reward_item) do
        if reward_data and reward_data.item_id then
            table.insert(real_reward_item, reward_data)
        end
    end

    self.item_list:SetDataList(real_reward_item)
    local rank = 0
    local rank_str = ""

    if self.data.max_rank and self.data.min_rank then
        if self.data.max_rank > self.data.min_rank then
            rank = self.data.max_rank
            rank_str = string.format(Language.UltimateBattlefield.SocreRanksStr[2], self.data.min_rank, self.data.max_rank)
        else
            rank = self.data.min_rank
            rank_str = string.format(Language.UltimateBattlefield.SocreRanksStr[1], self.data.min_rank)
        end
    else
        rank = self.data.need_score
        rank_str = string.format(Language.UltimateBattlefield.ScoreStr, self.data.need_score)

    end

    self.node_list.rank_txt.text.text = rank_str
	self.node_list.img_rank:SetActive(rank <= 3)
    self.node_list.rank_txt:SetActive(rank > 3)
	if rank <= 3 then
		self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. self.index))
		self.node_list.bg.image:LoadSprite(ResPath.GetCommonImages("a2_xm_di_dj" .. self.index))
		self.node_list.bg:SetActive(true)
	else
		self.node_list.bg.image:LoadSprite(ResPath.GetCommonImages("a2_ty_xxd_bt_2"))
		self.node_list.bg:SetActive(self.index % 2 ~= 0)
	end
end
