-- 在线
WelfareView = WelfareView or BaseClass(SafeBaseView)

function WelfareView:InitZaiXianView()
	self.zaixian_list_view = AsyncListView.New(ZaiXianItemRender,self.node_list["ph_zaixian_list"])
	self.zaixian_list_view:SetSelectCallBack(BindTool.Bind1(self.OnClickZaiXianHandler, self))
	self.zaixian_list_view:SetDataList(WelfareWGData.Instance:GetZaiXianListData())

	local online_time = WelfareWGData.Instance:GetTodayOnlineTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local zaixian_count_time = server_time + online_time + 21600

	if online_time > 0 then
		if CountDownManager.Instance:HasCountDown("online_countdown") then
			CountDownManager.Instance:RemoveCountDown("online_countdown")
		end
		self:UpdateCallBack(0, online_time + 21600)
		CountDownManager.Instance:AddCountDown("online_countdown", BindTool.Bind1(self.UpdateCallBack, self), BindTool.Bind1(self.CompleteCallBack, self), zaixian_count_time, nil, 1)
	else
		self:CompleteCallBack()
	end

	self:FlushZaiXianView()

	XUI.AddClickEventListener(self.node_list["btn_lingqu"], BindTool.Bind(self.OnClickFetchReward, self))

end

function WelfareView:DeleteZaiXianView()
	if self.zaixian_list_view then
		self.zaixian_list_view:DeleteMe()
		self.zaixian_list_view = nil
	end

	if CountDownManager.Instance:HasCountDown("online_countdown") then
		CountDownManager.Instance:RemoveCountDown("online_countdown")
	end
end

function WelfareView:OnClickZaiXianHandler(cell)
	if nil == cell or nil == cell:GetData() then return end
	
	if self.node_list["btn_lingqu"] then
		local is_enabled = self.node_list["btn_lingqu"].gameObject.activeSelf
		local data = WelfareWGData.Instance:GetZaiXianIsCanReward()
		if is_enabled then
			for k,v in pairs(data) do
				if v == true then
					WelfareWGCtrl.Instance:SendFetchOnlineReward(k)
				end
			end
		-- else
			-- SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.LingQuJiangLiTimeNotUp)
			-- local select_index = cell.index - 1
			-- for k, v in pairs(data) do
			-- 	if k == select_index and v == true then
			-- 		SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.RewardLingQu)
			-- 	elseif k == select_index and v == false then
			-- 		SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.LingQuJiangLiTimeNotUp)
			-- 	end
			-- end
		end
	end
end

function WelfareView:OnClickFetchReward()
	local data = WelfareWGData.Instance:GetZaiXianIsCanReward()
	for k,v in pairs(data) do
		if v == true then
			WelfareWGCtrl.Instance:SendFetchOnlineReward(k)
		end
	end

	-- WelfareWGCtrl.Instance:SendFetchOnlineReward(self.select_online_index)
end

function WelfareView:FlushZaiXianView()
	if self.zaixian_list_view then
		self.zaixian_list_view:SetData(WelfareWGData.Instance:GetZaiXianListData())
	end

	local is_visible = false
	local data = WelfareWGData.Instance:GetZaiXianIsCanReward()
	for k,v in pairs(data) do
		if v == true then
			is_visible = true
		end
	end
	XUI.SetButtonEnabled(self.node_list["btn_lingqu"], is_visible)
end

function WelfareView:UpdateCallBack(elapse_time, total_time)
	self.node_list["label_zaixian_time"].text.text = TimeUtil.FormatSecond2HMS(total_time + elapse_time - 21600)
end

function WelfareView:CompleteCallBack()
	if CountDownManager.Instance:HasCountDown("online_countdown") then
		CountDownManager.Instance:RemoveCountDown("online_countdown")
	end
end

----------------------------------------------------------------
ZaiXianItemRender = ZaiXianItemRender or BaseClass(BaseRender)

function ZaiXianItemRender:__init()
	self.percent = 0
	self.show_flag = false
	self.is_first_roll = true

	-- self.is_lingqu_data = {}
	-- self.chongzhi_progressbar = {}

	self.is_lingqu = false
end

function ZaiXianItemRender:OnClickZaiXianItemRender()
end


function ZaiXianItemRender:__delete()
	-- if nil ~= self.chongzhi_progressbar[self.index] then
	-- 	self.chongzhi_progressbar[self.index]:DeleteMe()
	-- 	self.chongzhi_progressbar[self.index] = nil
	-- end

	if self.online_cell then
		self.online_cell:DeleteMe()
		self.online_cell = nil
	end

	if CountDownManager.Instance:HasCountDown("online_time_countdown" .. self.index) then
		CountDownManager.Instance:RemoveCountDown("online_time_countdown" .. self.index)
	end

	if self.ani_list_view then
		self.ani_list_view:DeleteMe()
		self.ani_list_view = nil
	end

	if self.roll_cell then
		self.roll_cell:DeleteMe()
		self.roll_cell = nil
	end
end

function ZaiXianItemRender:LoadCallBack()
	local ph = self.node_list["ph_reward_item"]
	self.online_cell = ItemCell.New(ph)
	self.online_cell:SetActive(false)
	self.roll_cell = ItemCell.New(ph)
	self.roll_cell:SetIsShowTips(false)
	self.roll_cell:SetActive(false)
	XUI.AddClickEventListener(self.node_list["btn_zaixian_lingqu"], BindTool.Bind1(self.OnClickBtn, self))
end

function ZaiXianItemRender:OnClickBtn()
	-- body
	-- WelfareView.OnClickFetchReward()
	local data = WelfareWGData.Instance:GetZaiXianIsCanReward()
	if data[self.index-1]==true then
		WelfareWGCtrl.Instance:SendFetchOnlineReward(self.index-1)
	end
end

function ZaiXianItemRender:OnFlush()
	if nil == self.data then return end

	local online_time = WelfareWGData.Instance:GetTodayOnlineTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()


	if self.data.is_lingqu then									--已经领取
		self.node_list["label_reward_name1"].text.text = Language.Common.YiLingQu
	elseif self.data.min_time - online_time > 0 then			--时间未到
		if CountDownManager.Instance:HasCountDown("online_time_countdown" .. self.index) then
			CountDownManager.Instance:RemoveCountDown("online_time_countdown" .. self.index)
		end
		self:ItemRenderUpdateCallBack(0, self.data.min_time - online_time)
		CountDownManager.Instance:AddCountDown("online_time_countdown" .. self.index, BindTool.Bind1(self.ItemRenderUpdateCallBack, self), BindTool.Bind1(self.ItemRenderCompleteCallBack, self), nil, self.data.min_time - online_time, 1)
	else 														-- 可以领取
		self.node_list["label_reward_name1"].text.text = Language.Welfare.OnlineRewardKelingqu
	end

	XUI.SetButtonEnabled(self.node_list["btn_zaixian_lingqu"], not self.data.is_lingqu)
	self.node_list["item_baoxiang_bg"]:SetActive(not self.data.is_lingqu)

	if not self.data.is_lingqu then
		self.show_flag = true
	end

	self:ShowCellItem()
end

function ZaiXianItemRender:ItemRenderUpdateCallBack(elapse_time, total_time)
	local timer = total_time - elapse_time
	if timer > 0 then
		local time_stamp = TimeUtil.FormatSecond2HMS(timer)
		self.node_list["label_reward_name1"].text.text = time_stamp
		self.node_list["btn_zaixian_lingqu_text"].text.text = time_stamp
	else
		if CountDownManager.Instance:HasCountDown("online_time_countdown" .. self.index) then
			CountDownManager.Instance:RemoveCountDown("online_time_countdown" .. self.index)
		end
		WelfareWGCtrl.Instance:SendOnlineRewardInfo()
	end
end

function ZaiXianItemRender:ItemRenderCompleteCallBack()
	if CountDownManager.Instance:HasCountDown("online_time_countdown" .. self.index) then
		CountDownManager.Instance:RemoveCountDown("online_time_countdown" .. self.index)
	end
	self.node_list["label_reward_name1"].text.text = Language.Welfare.OnlineRewardKelingqu
	print_error("领取字样==========================================")
	self.node_list["btn_zaixian_lingqu_text"].text.text = Language.Common.LingQu
	WelfareWGCtrl.Instance:SendOnlineRewardInfo()
end

function ZaiXianItemRender:CreateRollEffect()
	
end

function ZaiXianItemRender:ShowCellItem()
	self.online_cell:SetData(self.data.reward_item)
	self.online_cell:SetActive(self.data.is_lingqu)

	if self.data.is_lingqu == true then
		self.roll_cell:SetActive(false)
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.reward_item.item_id)
	if self.data.is_lingqu then
		self.node_list["label_reward_name1"].text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
	end
end

-- 创建选中特效
function ZaiXianItemRender:CreateSelectEffect()
end