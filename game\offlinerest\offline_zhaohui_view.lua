
--离线经验--找回

OfflineZhaoHuiView = OfflineZhaoHuiView or BaseClass(SafeBaseView)

function OfflineZhaoHuiView:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/offlinerest_ui_prefab", "layout_offline_zhaohui")
end

function OfflineZhaoHuiView:__delete()

end

function OfflineZhaoHuiView:OpenCallBack()

end

function OfflineZhaoHuiView:ReleaseCallBack()
	if self.zh_cost_item then
		self.zh_cost_item:DeleteMe()
		self.zh_cost_item = nil
	end

	if self.close_alert then
		self.close_alert:DeleteMe()
		self.close_alert = nil
	end
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
end

function OfflineZhaoHuiView:CloseCallBack()
	--设置本次登陆不再打开
	OfflineRestWGData.Instance:SetOfflineZhaoHuiHasOpened(true)
end

function OfflineZhaoHuiView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_close_window"], BindTool.Bind(self.OnClickCloseBtn, self))
	--XUI.AddClickEventListener(self.node_list["btn_zh_cancel"], BindTool.Bind(self.OnClickCloseBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_zh_confirm"], BindTool.Bind(self.OnClickConfirmBtn, self))
	XUI.AddClickEventListener(self.node_list["zh_buy_item_btn"], BindTool.Bind(self.OpenBuyTip, self))

	self.zh_cost_item = ItemCell.New(self.node_list["zh_cost_item_pos"])
	self.zh_cost_item_id = OfflineRestWGData.Instance:GetOfflineZhaoHuiCostItem()
	if self.zh_cost_item_id then
		self.zh_cost_item:SetData({item_id = self.zh_cost_item_id, is_bind = 1})
		self.zh_cost_item:SetNeedItemGetWay(true)
	end

	self.item_data_change_callback = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
end

function OfflineZhaoHuiView:OnFlush()
	self.zhaohui_info = OfflineRestWGData.Instance:GetOfflineZhaoHuiInfo()
	if IsEmptyTable(self.zhaohui_info) then
		return
	end
	local my_num = ItemWGData.Instance:GetItemNumInBagById(self.zh_cost_item_id)
	local show_hour = self.zhaohui_info.find_hour or 0
	local exp_num = self.zhaohui_info.find_exp or 0
	local item_name = ItemWGData.Instance:GetItemName(self.zh_cost_item_id)

	local color = (my_num >= show_hour) and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
	self.zh_cost_item:SetRightBottomText(ToColorStr(my_num .. "/" .. show_hour, color))
	self.zh_cost_item:SetRightBottomTextVisible(true)

	local other_cfg = OfflineRestWGData.Instance:GetOtherCfg()
    local hang_max_offlie_time = other_cfg.hang_max_offlie_time or 0
	local time_tab = TimeUtil.Format2TableDHMS(hang_max_offlie_time)
	local hour = time_tab.hour
	self.node_list["txt_zhaohui_time"].text.text = string.format(Language.OfflineRest.ZhaoHuiTime, show_hour, hour)
	self.node_list["txt_zhaohui_exp"].text.text = CommonDataManager.ConverExpExtend(exp_num)
	self.node_list["txt_zhaohui_cost"].text.text = string.format(Language.OfflineRest.OfflineExpFindItemCost, item_name)
	local gurad_data = ItemWGData.Instance:GetItemConfig(10100)
	self.node_list["Txt"].text.text = string.format(Language.OfflineRest.GuradTitle, gurad_data.name)
	--设置默认显示购买的数量
	-- local def_num = show_hour - my_num
	-- def_num = def_num > 0 and def_num or 1
	-- self.zh_cost_item:SetDefShowBuyCount(def_num)
end

--物品变化
function OfflineZhaoHuiView:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_item_id == self.zh_cost_item_id then
		self:Flush()
	end
end

function OfflineZhaoHuiView:OnClickCloseBtn()
	if not self.close_alert then
		self.close_alert = Alert.New()
	end
	self.close_alert:SetLableString(Language.OfflineRest.OfflineExpFindCancelTip)
	self.close_alert:SetOkFunc(function()
		self:Close()
	end)
	self.close_alert:Open()
end

function OfflineZhaoHuiView:OnClickConfirmBtn()
	if not self.zh_cost_item_id or IsEmptyTable(self.zhaohui_info) then
		return
	end
	local my_num = ItemWGData.Instance:GetItemNumInBagById(self.zh_cost_item_id)
	local need_num = self.zhaohui_info.find_hour or 0
	if need_num > 0 and my_num >= need_num then
		-- print_error("FFFFF======= 请求找回")
		RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_OFFLINE_ZHAOHUI_REQ)
		OfflineRestWGCtrl.Instance:OpenOfflinerestExpEffectView()
		self:Close()
	else
		-- print_error("FFFFF======= 物品不足")
		self:OpenBuyTip()
	end
end

function OfflineZhaoHuiView:OpenBuyTip()
	if not self.zh_cost_item_id or IsEmptyTable(self.zhaohui_info) then
		return
	end
	local my_num = ItemWGData.Instance:GetItemNumInBagById(self.zh_cost_item_id)
	local need_num = self.zhaohui_info.find_hour or 0
	local def_num = need_num - my_num
	def_num = def_num > 0 and def_num or 1
	-- TipWGData.Instance:SetDefShowBuyCount(def_num)
	-- TipWGCtrl.Instance:OpenItem({item_id = self.zh_cost_item_id})
	local shop_item_cfg = ShopWGData.Instance:GetShopItemCfg(self.zh_cost_item_id, Shop_Money_Type.Type2)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.zh_cost_item_id)
	if item_cfg and shop_item_cfg then
		ShopTip.Instance:SetData(item_cfg, nil, GameEnum.SHOP, def_num, shop_item_cfg.seq, nil, def_num, nil)
	end
end