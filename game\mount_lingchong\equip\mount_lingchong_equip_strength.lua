--骑宠装备强化
MountLingChongEquipView = MountLingChongEquipView or BaseClass(SafeBaseView)

local COIN_DATA = {is_bind = 0, item_id = 65535} 		--铜币
local STRENGTH_DELT_TIME = 0.2 --0.2s强化一次


function MountLingChongEquipView:InitStrengthView()
	self.node_list["btn_equip_qh"].button:AddClickListener(BindTool.Bind2(self.OnBtnEquipStrengthHander, self)) --强化
    self.node_list["btn_equip_qh_auto"].button:AddClickListener(BindTool.Bind1(self.AutoEquipStrength, self)) --一键强化
    --self.node_list["btn_equip_rule"].button:AddClickListener(BindTool.Bind1(self.OnClickBtnStrengthTip, self)) --rule

    self.strength_stuff_cell = ItemCell.New(self.node_list["strength_stuff_pos"])
    self.strength_stuff_cell:SetNeedItemGetWay(true)

    local bundle, asset = ItemWGData.Instance:GetTipsItemIcon(COMMON_CONSTS.VIRTUAL_ITEM_BIND_COIN)
    self.node_list["strength_coin_stuff"]:SetActive(false)
    if bundle and asset then
        self.node_list["strength_coin_stuff"].image:LoadSprite(bundle, asset, function ()
            self.node_list["strength_coin_stuff"].image:SetNativeSize()
            self.node_list["strength_coin_stuff"]:SetActive(true)
        end)
    end
    self:SetAutoStrengthBtnNameStr(Language.MountPetEquip.OneKeyStrength)
	self.last_level = -1
	self.last_part = -1
	self.equip_up_struff = {} --强化石缓存
	self.is_auto_equip_qh = false
	self.need_strength_coin = 0
end


function MountLingChongEquipView:DeleteStrengthView()
	if self.equip_qh_item_cell then
		self.equip_qh_item_cell:DeleteMe()
		self.equip_qh_item_cell = nil
	end

	if self.strength_stuff_cell then
		self.strength_stuff_cell:DeleteMe()
		self.strength_stuff_cell = nil
    end
    if CountDownManager.Instance:HasCountDown('MountLingChongEquipView_Strength') then
		CountDownManager.Instance:RemoveCountDown('MountLingChongEquipView_Strength')
	end
    self.strength_time = nil
	self:CancelAutoStrengthLevelTimer()
	self.is_auto_equip_qh = false
	self.equip_qh_list_flag = nil
	self.last_level = -1
    self.equip_up_struff = nil
    self.strength_item_id = nil
end

function MountLingChongEquipView:ShowStrengthCallBack()
    self.strength_item_id = 0
end

function MountLingChongEquipView:FlushStrengthView()
	self:FlushStrengthAllAttr()
end

-- 点击穿戴的物品
function MountLingChongEquipView:OnSelectEquipStrengthItemHandler()
	local strength_item_data = self:GetCurSelectData()
	if not strength_item_data or self.strength_item_id == strength_item_data.item_id then
		return
	end

	self.last_level = -1
	self.last_part = -1
	self.strength_item_id = strength_item_data.item_id
	self:StopStrengthOperator() --停止自动强化
	self:FlushStrengthAllAttr()
end

--刷新材料
function MountLingChongEquipView:FlushStrengthStuffItem()
    if self:IsOpen() and self:IsLoadedIndex(MountLingChongEquipViewIndex[self.cur_show_type].Strength) then
        local can_strength = MountLingChongEquipWGData.Instance:EquipStrengthRemind(self.cur_show_type) == 1
        self.node_list.onekey_strength_remind:SetActive(can_strength)
        local cur_item_data = self:GetCurSelectData()
        if not cur_item_data then
            return
        end
        
        local is_max_level = MountLingChongEquipWGData.Instance:IsStrengthMax(self.cur_show_type, self.cur_select_part, cur_item_data.strengthen_level)
        if not is_max_level then
            local now_level = cur_item_data.strengthen_level + 1
            local equip_cfg = MountLingChongEquipWGData.Instance:GetStrengthCfgByLevel(self.cur_show_type, self.cur_select_part, now_level)
            -- 材料消耗
            local is_have_stuff = true
            self.equip_up_struff = {need_num = 0}
            if equip_cfg and equip_cfg.num > 0 then
                self.equip_up_struff = {item_id = equip_cfg.stuff_id, need_num = equip_cfg.num}
                local num = ItemWGData.Instance:GetItemNumInBagById(equip_cfg.stuff_id)
                self.strength_stuff_cell:SetData({item_id = equip_cfg.stuff_id})
                is_have_stuff = num >= equip_cfg.num
                self.strength_stuff_cell:SetRightBottomColorText(num .. "/" .. equip_cfg.num, is_have_stuff and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH)
                self.strength_stuff_cell:SetRightBottomTextVisible(true)
            else
                self.strength_stuff_cell:ClearData()
                self.strength_stuff_cell:SetRightBottomTextVisible(false)
                self.strength_stuff_cell:SetItemIcon(ResPath.GetCommonImages("a1_suo_bb2"))
            end
            local is_max_level = MountLingChongEquipWGData.Instance:IsStrengthMax(self.cur_show_type, self.cur_select_part, now_level)
            local need_coin = equip_cfg.consume_coin
            self.need_strength_coin = need_coin
            local role_coin = RoleWGData.Instance.role_info.coin or 0
            local coin_str_color = need_coin <= role_coin and COLOR3B.GREEN or COLOR3B.PINK
            local cur_coin_str = ToColorStr(CommonDataManager.ConverExpByThousand(role_coin), coin_str_color)
            local color_str
            if need_coin ~= 0 then
                color_str = cur_coin_str .. "/" .. CommonDataManager.ConverExpByThousand(need_coin)
                self.node_list.strength_coin_contanier:SetActive(true)
            else
                self.node_list.strength_coin_contanier:SetActive(false)
            end
            self.node_list.qh_coin_desc.text.text = color_str
            self:SetStrengthRemind(is_have_stuff and need_coin <= role_coin and not is_max_level)
        end
    end
end

function MountLingChongEquipView:FlushStrengthAllAttr()
	local strength_item_data = self:GetCurSelectData()
	if IsEmptyTable(strength_item_data) then
		return
	end

	local now_level = strength_item_data.strengthen_level
	local equip_cfg = MountLingChongEquipWGData.Instance:GetStrengthCfgByLevel(self.cur_show_type, self.cur_select_part, now_level)
	if nil == equip_cfg then
		return
	end	
    local is_max_level = MountLingChongEquipWGData.Instance:IsStrengthMax(self.cur_show_type, self.cur_select_part, now_level)
    if not is_max_level then
        self:FlushStrengthStuffItem()
    end
	self:SetStrengthMaxView(is_max_level)
	if not is_max_level then
		--self:SetEquipStrengthButtonEnabled(self:IsAutoStrength())
    end
	self:FlushEquipStrengthAttr(now_level, is_max_level)
	if self.last_part == strength_item_data.part and now_level > self.last_level then
		self:PlayEquipStrengthEffect()
    end
	self.last_level = now_level
	self.last_part = strength_item_data.part
	self.last_stuff_num = equip_cfg.num
end

function MountLingChongEquipView:PlayEquipStrengthEffect()
    if self.node_list and self.node_list["strength_effect_root1"] then
        self.node_list["strength_effect_root1"]:SetActive(true)

        TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_qianghua, is_success = true, pos = Vector2(0, 0),
            parent_node = self.node_list["strength_effect_root1"]})
        AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
    end
end

function MountLingChongEquipView:AutoStrengthOnce()
    self:CancelAutoStrengthLevelTimer()
	if self:GetShowIndex() ~= MountLingChongEquipViewIndex[self.cur_show_type].Strength then
		self:StopStrengthOperator()
		return
	end

	local strength_item_data = self:GetCurSelectData()
	if IsEmptyTable(strength_item_data) then
		return
    end
    local now_level = strength_item_data.strengthen_level
    local is_max_level = MountLingChongEquipWGData.Instance:IsStrengthMax(self.cur_show_type, self.cur_select_part, now_level)
    if not is_max_level then
        local is_limit = MountLingChongEquipWGData.Instance:GetIsStrengthLimitLv(strength_item_data.item_id, strength_item_data.strengthen_level)
        if is_limit then
            return
        end
		if self:IsAutoStrength() then
			self.qh_equip_timer_quest = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.OnBtnEquipStrengthHander, self, true), STRENGTH_DELT_TIME)
		end
	end
end

--刷新强化面板属性显示
function MountLingChongEquipView:FlushEquipStrengthAttr(level, is_max_level)
	local strength_item_data = self:GetCurSelectData()
	if IsEmptyTable(strength_item_data) then
		return
	end

	local now_level = level or strength_item_data.strengthen_level
	local equip_cfg = MountLingChongEquipWGData.Instance:GetStrengthCfgByLevel(self.cur_show_type, self.cur_select_part, now_level)
	if IsEmptyTable(equip_cfg) then return end

	local next_equip_cfg = MountLingChongEquipWGData.Instance:GetStrengthCfgByLevel(self.cur_show_type, self.cur_select_part, now_level + 1)
	self.node_list["lbl_now_text_0"].text.text = string.format(Language.MountPetEquip.StrengthLevel, equip_cfg.level)
	if is_max_level == false then
		self.node_list["lbl_next_text_0"].text.text = string.format(Language.MountPetEquip.StrengthLevel, next_equip_cfg.level)
	else
		self.node_list["lbl_next_text_0"].text.text = Language.MountPetEquip.MAX3 --已满级
    end

    local equip_info = MountLingChongEquipWGData.Instance:ConvertAttrType(equip_cfg)
    local next_equip_info = MountLingChongEquipWGData.Instance:ConvertAttrType(next_equip_cfg)
    local attr_data = EquipWGData.GetSortAttrListHaveNextByCfg(equip_info, next_equip_info)
	local index = 0
	for k, v in pairs(attr_data) do
		index = index + 1
		self.node_list["ph_strength_attrt_item" .. index]:SetActive(true)
		local attr_name = Language.Common.AttrNameList2[v.attr_str] or ""
		self.node_list["lbl_now_text_" .. index].text.text = v.attr_value
        self.node_list["lbl_name_text_".. index].text.text = attr_name

		if is_max_level then
			self.node_list["lbl_next_text_" .. index].text.text = Language.MountPetEquip.MAX3
		else
			self.node_list["lbl_next_text_" .. index].text.text = v.attr_next_value
		end
	end

	for i = index + 1, 5 do
		self.node_list["ph_strength_attrt_item" .. i]:SetActive(false)
    end

    if not is_max_level then
        local is_limit = MountLingChongEquipWGData.Instance:GetIsStrengthLimitLv(strength_item_data.item_id, now_level)
        self.node_list.qh_btn_part:SetActive(not is_limit)
        self.node_list.qh_nomax_limit_desc:SetActive(is_limit)
        if is_limit then
            local next_qua = MountLingChongEquipWGData.Instance:GetNextStrengthQuality(strength_item_data.item_id, now_level)
            local str = Language.MountPetEquip.ColorStr[next_qua]
            self.node_list.qh_nomax_limit_desc.text.text = string.format(Language.MountPetEquip.UpQualityDes, str)
            if self:IsAutoStrength() then
                self:JumpToRemindCell()
            end
        end
    end
end

--强化分割线--------------------------------------------------------------------------
-- 强化装备请求
function MountLingChongEquipView:OnBtnEquipStrengthHander(is_auto)
	if self:IsAutoStrength() and not is_auto then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Equip.StrengthBtnTip)
		return
	end

	local strength_item_data = self:GetCurSelectData()
	if nil == strength_item_data then
		return
    end

	local is_max_level = MountLingChongEquipWGData.Instance:IsStrengthMax(self.cur_show_type, strength_item_data.part, strength_item_data.strengthen_level)
    if is_max_level then
        self:StopStrengthOperator()
        self:JumpToRemindCell()
		return
	end
    local is_limit = MountLingChongEquipWGData.Instance:GetIsStrengthLimitLv(strength_item_data.item_id, strength_item_data.strengthen_level)
    if is_limit then
        self:StopStrengthOperator()
        self:JumpToRemindCell()
		return
    end

	if not self:IsHaveStuffToStrengthUpLevel() then
        self:StopStrengthOperator()
        if is_auto and self:IsCanJumpTo() then
            self:JumpToRemindCell()
        else
            TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.equip_up_struff.item_id})
        end
		return
	end

	if self:IsHaveMoneyToUplevel() then
        MountLingchongEquipWGCtrl.Instance:SendMountPetEquipReq(self.cur_show_type, 
        MOUNT_PET_EQUIP_OPERA_TYPE.MOUNT_PET_EQUIP_OPER_TYPE_STRENGTHEN, self.cur_select_part) --请求强化
		if is_auto then
			self:SetEquipStrengthButtonEnabled(true)
		end
	else
        self:StopStrengthOperator()
        self:JumpToRemindCell()
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NoCoin)
		TipWGCtrl.Instance:OpenItemTipGetWay(COIN_DATA)
	end
end

function MountLingChongEquipView:IsAutoStrength()
	return self.is_auto_equip_qh
end

-- 自动进阶请求处理
function MountLingChongEquipView:AutoEquipStrength()
    local can_strength = MountLingChongEquipWGData.Instance:EquipStrengthRemind(self.cur_show_type) == 1
    if can_strength then
        if self.strength_time then return end
        CountDownManager.Instance:AddCountDown('MountLingChongEquipView_Strength',function (time,total_time)
            time = math.modf(time)
            self:SetAutoStrengthBtnNameStr(total_time - time)
        end,
        function ()
            self:SetAutoStrengthBtnNameStr(Language.MountPetEquip.OneKeyStrength)
            self.strength_time = false
            XUI.SetButtonEnabled(self.node_list.btn_equip_qh_auto,true)
            CountDownManager.Instance:RemoveCountDown('MountLingChongEquipView_Strength')
        end, nil, 3, 1)
        self:SetAutoStrengthBtnNameStr(3)
        XUI.SetButtonEnabled(self.node_list.btn_equip_qh_auto,false)
        self.strength_time = true
        MountLingchongEquipWGCtrl.Instance:SendMountPetEquipReq(self.cur_show_type, MOUNT_PET_EQUIP_OPERA_TYPE.MOUNT_PET_EQUIP_OPER_TYPE_ONE_KEY_STRENGTH) --请求强化
    else
        if not self:IsHaveStuffToStrengthUpLevel() then
            if self:IsCanJumpTo() then
                self:JumpToRemindCell()
            else
                TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.equip_up_struff.item_id})
            end
            return
        end

        if not self:IsHaveMoneyToUplevel() then
            self:StopStrengthOperator()
            self:JumpToRemindCell()
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NoCoin)
            TipWGCtrl.Instance:OpenItemTipGetWay(COIN_DATA)     
        end
    end

	-- if self:IsAutoStrength() then --正在自动强化则取消强化
	-- 	self:StopStrengthOperator()
	-- 	return
	-- end

	-- local strength_item_data = self:GetCurSelectData()
	-- if not IsEmptyTable(strength_item_data) then
	-- 	local is_max_level = MountLingChongEquipWGData.Instance:IsStrengthMax(self.cur_show_type, self.cur_select_part, strength_item_data.strengthen_level)
	-- 	if is_max_level then
	-- 		SysMsgWGCtrl.Instance:ErrorRemind(Language.Equip.EquipStrengthMaxLevel)
	-- 		return
	-- 	end
	-- 	self:OnBtnEquipStrengthHander(true)
	-- end
end

--取消自动强化倒计时
function MountLingChongEquipView:CancelAutoStrengthLevelTimer( )
	if nil ~= self.qh_equip_timer_quest then
		GlobalTimerQuest:CancelQuest(self.qh_equip_timer_quest)
		self.qh_equip_timer_quest = nil
	end
end

--------------------------------------------------------------------------------------
function MountLingChongEquipView:StopStrengthOperator()
	--self:SetEquipStrengthButtonEnabled(false)
end

-- 设置强化按钮是否可用
function MountLingChongEquipView:SetEquipStrengthButtonEnabled(enabled)
    if self:IsLoadedIndex(MountLingChongEquipViewIndex[self.cur_show_type].Strength) then
        self.is_auto_equip_qh = enabled
        --self:SetAutoStrengthBtnNameStr(not enabled and Language.Equip.AutoEquipStrength or Language.Equip.EquipStrengthBtnStop)
        if false == enabled then
            self:CancelAutoStrengthLevelTimer()
        end
    end
end

--关闭所有强化按钮
function MountLingChongEquipView:SetAllEquipStrengthButtonEnabled( enabled )
	self:SetBtnStrengthEnable(enabled)
	self:SetAutoStrengthEnable(enabled)
end

function MountLingChongEquipView:SetAutoStrengthEnable( enabled )
	--XUI.SetButtonEnabled(self.node_list["btn_equip_qh_auto"], enabled)
	-- if false == enabled then
	-- 	self:SetAutoStrengthBtnNameStr(Language.Equip.AutoEquipStrength)
	-- end
end

function MountLingChongEquipView:SetAutoStrengthBtnNameStr( strength_btn_str )
	self.strength_btn_str = strength_btn_str
	if self.node_list["equip_qh_auto_text"] then
		self.node_list["equip_qh_auto_text"].text.text = self.strength_btn_str
	end
end

function MountLingChongEquipView:SetBtnStrengthEnable(enabled)
	XUI.SetButtonEnabled(self.node_list["btn_equip_qh"], enabled)
end

function MountLingChongEquipView:SetStrengthRemind(enable)
	self.node_list.strength_remind:SetActive(enable)
end

--判断是否有足够的金钱强化装备 --@true 金钱足够 --@false 金钱不够
function MountLingChongEquipView:IsHaveMoneyToUplevel()
	local role_coin = RoleWGData.Instance.role_info.coin or 0
	if self.need_strength_coin <= role_coin then
		return true
	end
	return false
end

--判断是否有材料强化
function MountLingChongEquipView:IsHaveStuffToStrengthUpLevel( )
	if self.equip_up_struff.need_num and self.equip_up_struff.need_num > 0 then --需要材料
		local num = ItemWGData.Instance:GetItemNumInBagById(self.equip_up_struff.item_id)
		if num >= self.equip_up_struff.need_num then
			return true
		end
	else
		return true
	end
	return false
end

function MountLingChongEquipView:SetStrengthMaxView(is_max_level)
	self:SetAllEquipStrengthButtonEnabled(not is_max_level)
	self.node_list["strengtn_maxlevel_content"]:SetActive(not is_max_level)
	self.node_list["strength_max_state"]:SetActive(is_max_level)
    self.node_list["qh_nomax_limit_desc"]:SetActive(false)
end


function MountLingChongEquipView:OnClickBtnStrengthTip()
	local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.Equip.TipsStrengthTitle)
	rule_tip:SetContent(Language.Equip.TipsStrengthTipContent, nil, nil, nil, true)
end
