EquipTargetAttrView = EquipTargetAttrView or BaseClass(SafeBaseView)

function EquipTargetAttrView:__init()
	self:SetMaskBg(true,true)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:AddViewResource(0, "uis/view/cultivation_ui_prefab", "layout_equiptarget_attr_view")
end

function EquipTargetAttrView:LoadCallBack()
    -- self.node_list.desc_privilege_tip.text.text = Language.Cultivation.PrivilegeViewTips
    self.cur_attr_item = {}
    for i = 1, 5 do
        self.cur_attr_item[i] = EquipTargetAttrRender.New(self.node_list["rich_cur_"..i])
    end

    self.next_attr_item = {}
    for i = 1, 5 do
        self.next_attr_item[i] = EquipTargetAttrRender.New(self.node_list["rich_next_"..i])
    end

    self.et_suit_attr_item_list = {}
    XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind(self.Close, self))


    
end

function EquipTargetAttrView:ShowIndexCallBack()
    -- self:PlayTween()
end

function EquipTargetAttrView:ReleaseCallBack()
    if self.et_suit_attr_item_list then
        for k,v in pairs(self.et_suit_attr_item_list) do
            v:DeleteMe()
        end
        self.et_suit_attr_item_list = nil
    end

    if self.cur_attr_item then
        for k,v in pairs(self.cur_attr_item) do
            v:DeleteMe()
        end
        self.cur_attr_item = nil
    end

    if self.next_attr_item then
        for k,v in pairs(self.next_attr_item) do
            v:DeleteMe()
        end
        self.next_attr_item = nil
    end

end

function EquipTargetAttrView:OnFlush()

    local suit_info = EquipTargetWGData.Instance:GetEquipinfo(self.et_suit_index)
    if not IsEmptyTable(suit_info) then
        self.node_list.text_title_suit.text.text = suit_info.suit_name
        self:FlushCurSuitAttrListView()
        self:FlushNextSuitAttrListView()
    end
end

function EquipTargetAttrView:SetData(suit_index)

    self.et_suit_index = suit_index
end

-- 刷新当前属性列表
function EquipTargetAttrView:FlushCurSuitAttrListView()
    local list_data = EquipTargetWGData.Instance:GetEquipmenSuitAttr(self.et_suit_index)
    if IsEmptyTable(list_data) then
        self.node_list.no_attr_tip:CustomSetActive(true)
        return
    end

    local cur_suit_attr = {}
    for i,v in ipairs(list_data) do
        if v.is_open == 1 then
            table.insert(cur_suit_attr,v)
        end
    end

    if not IsEmptyTable(cur_suit_attr) then
        for k, v in ipairs(self.cur_attr_item) do
            if cur_suit_attr[k] then
                local attr_data = cur_suit_attr[k].attr_list[1]
                local is_per = not EquipmentWGData.Instance:GetAttrIsPerByAttrStr(cur_suit_attr[k].attr_type)
                local name = Language.Common.AttrNameList2[attr_data.attr_type]
                local value = is_per and attr_data.value or attr_data.value / 100 .. "%"
    
                local data = {}
                data.name = name
                data.value = value
                v:SetData(data)
                v:SetActive(true)
            else
                v:SetActive(false)
            end
        end
        
        local last_attr = cur_suit_attr[#cur_suit_attr]
        local attri_color = COLOR3B.WHITE
        if last_attr.is_open == 1 then
            attri_color = COLOR3B.DEFAULT_NUM
        end
    
        local need_str = last_attr.equip_num .. Language.Equip.SuitNumCompany1
        self.node_list.text_cur_level.text.text = ToColorStr(need_str, attri_color)

        self.node_list.no_attr_tip:CustomSetActive(false)
        self.node_list.cur_level_bg:CustomSetActive(true)
        self.node_list.layout_cur_attr_add:CustomSetActive(true)


    else
        self.node_list.no_attr_tip:CustomSetActive(true)
        self.node_list.cur_level_bg:CustomSetActive(false)
        self.node_list.layout_cur_attr_add:CustomSetActive(false)


    end
end

-- 刷新下级属性列表
function EquipTargetAttrView:FlushNextSuitAttrListView()
    local list_data = EquipTargetWGData.Instance:GetEquipmenSuitAttr(self.et_suit_index)
    if IsEmptyTable(list_data) then
        self.node_list.max_attr_tip:CustomSetActive(true)
        return
    end

    local next_suit_attr = {}
    local is_have = false
    for i,v in ipairs(list_data) do
        if v.is_open == 1 then
            table.insert(next_suit_attr, v)
        elseif not is_have then
            table.insert(next_suit_attr, v)
            is_have = true
        end 
    end

    if is_have and next_suit_attr then
        for k, v in ipairs(self.next_attr_item) do
            if next_suit_attr[k] then
                local attr_data = next_suit_attr[k].attr_list[1]
                local is_per = not EquipmentWGData.Instance:GetAttrIsPerByAttrStr(attr_data.attr_type)
                local name = Language.Common.AttrNameList2[attr_data.attr_type]
                local value = is_per and attr_data.value or attr_data.value / 100 .. "%"
    
                local data = {}
                data.name = name
                data.value = value
                v:SetData(data)
                v:SetActive(true)
            else
                v:SetActive(false)
            end
        end
        
        local last_attr = next_suit_attr[#next_suit_attr]

        local attri_color = COLOR3B.WHITE
        if last_attr.is_open == 1 then
            attri_color = COLOR3B.DEFAULT_NUM
        end
    
        local need_str = last_attr.equip_num .. Language.Equip.SuitNumCompany1
        self.node_list.text_next_level.text.text = ToColorStr(need_str, attri_color)
        self.node_list.max_attr_tip:CustomSetActive(false)
        self.node_list.next_level_bg:CustomSetActive(true)
        self.node_list.layout_next_attr_add:CustomSetActive(true)


    else
        self.node_list.max_attr_tip:CustomSetActive(true)
        self.node_list.next_level_bg:CustomSetActive(false)
        self.node_list.layout_next_attr_add:CustomSetActive(false)

    end
end

-----------------------------套装属性-------------------------------
EquipTargetSuitAttrRender = EquipTargetSuitAttrRender or BaseClass(BaseRender)

function EquipTargetSuitAttrRender:__init()
    self.attr_list = {}
    local attr_num = self.node_list.attr_list.transform.childCount
    for i = 1, attr_num do
        self.attr_list[i] = EquipTargetAttrRender.New(self.node_list.attr_list:FindObj("attr_" .. i))  
    end
end

function EquipTargetSuitAttrRender:__delete()

    if self.attr_list then
        for i, v in ipairs(self.attr_list) do
            self.attr_list[i]:DeleteMe()
            self.attr_list[i] = nil
        end
        self.attr_list = nil
    end

end

function EquipTargetSuitAttrRender:LoadCallBack()
    
end

function EquipTargetSuitAttrRender:OnFlush()
    if nil == self.data then
        return
    end

    local attri_color = COLOR3B.WHITE
    if self.data.is_open == 1 then
        attri_color = COLOR3B.DEFAULT_NUM
    -- else
    --     attri_color = COLOR3B.D_GRAY
    end

    local need_str = self.data.equip_num .. Language.Equip.SuitNumCompany1--"【" .. self.data.equip_num .. Language.Equip.SuitNumCompany1 .. "】"
    self.node_list.need_num.text.text = ToColorStr(need_str, attri_color)
    local list = self.data.attr_list

    for k, v in ipairs(self.attr_list) do
        if list[k] then
            local is_per = not EquipmentWGData.Instance:GetAttrIsPerByAttrStr(list[k].attr_type)
            local name = Language.Common.AttrNameList2[list[k].attr_type]
            local value = is_per and list[k].value or list[k].value / 100 .. "%"

            local data = {}
            data.name = name
            data.value = value
            v:SetData(data)
            -- v.node_list.name.text.text = name
            -- v.node_list.value.text.text = string.format("+%s", value)
            -- local attr_str = string.format("%s +%s", name, value)
            -- v.text.text = ToColorStr(attr_str, attri_color)
            v:SetActive(true)
        else
            v:SetActive(false)
        end
    end
end

EquipTargetAttrRender = EquipTargetAttrRender or BaseClass(BaseRender)

function EquipTargetAttrRender:__init()

end

function EquipTargetAttrRender:__delete()
    self.attr_list = nil
end


function EquipTargetAttrRender:OnFlush()
    if nil == self.data then
        return
    end

    self.node_list.name.text.text = self.data.name
    self.node_list.value.text.text = string.format("+%s", self.data.value)
end