TotalChargeWGData = TotalChargeWGData or BaseClass()

function TotalChargeWGData:__init()
	if TotalChargeWGData.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[TotalChargeWGData] attempt to create singleton twice!")
		return
	end
	TotalChargeWGData.Instance = self
	self.recharge = {}
	self.recharge.total_charge_value = 0
	self.recharge.reward_has_fetch_flag = 0
end

function TotalChargeWGData:__delete()
	TotalChargeWGData.Instance = nil
end

function TotalChargeWGData:SetRATotalChargeDayInfo(protocol)
	self.recharge = {}
	self.recharge.total_charge_value = protocol.total_charge_value
	self.recharge.reward_has_fetch_flag = protocol.reward_has_fetch_flag
end

-- 获取按钮状态
function TotalChargeWGData:GetRewardState(index)
	local temp_flag = bit:d2b(self.recharge.reward_has_fetch_flag)
	return temp_flag[32 - index]
end

function TotalChargeWGData:GetRATotalChargeDayInfo()
	return self.recharge
end

function TotalChargeWGData:GetConfigByRoleLevel(cfg)
	if not cfg or not next(cfg) then return end
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local level_condition
	local cfg_list = {}
	local temp_level = nil
	for k,v in pairs(cfg) do
		if v.level_part or v.level_max then
			level_condition = v.level_part and v.level_part or v.level_max or 0
			if (nil == temp_level or level_condition == temp_level) and role_level <= level_condition then
				temp_level = level_condition
				table.insert(cfg_list,v)
			end
		end
	end

	return cfg_list
end

function TotalChargeWGData:GetConfigByServerOpenDay(cfg)
	if not cfg or not next(cfg) then return end
	local cur_openserver_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local a = nil
	local cfg_list = {}
	local open_day
	for i,v in ipairs(cfg) do
		if v.server_open_day or v.open_day or v.opengame_day then
			open_day = v.server_open_day and v.server_open_day or v.open_day or v.opengame_day
			if (not a or a == tonumber(open_day)) and cur_openserver_day <= tonumber(open_day) then
				a = tonumber(open_day)
				table.insert(cfg_list,v)
			end
		end
	end
	return cfg_list
end

function TotalChargeWGData:GetRechargeDataInfo()
	local cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local rand_t = cfg.rand_total_chongzhi
	local total_recharge_cfg = ServerActivityWGData.Instance:GetRandActivityConfig(rand_t, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_TOTAL_CHONGZHI)
	total_recharge_cfg = self:GetConfigByRoleLevel(total_recharge_cfg)	--根据人物等级筛选出对应等级区间的配置
	total_recharge_cfg = self:GetConfigByServerOpenDay(total_recharge_cfg)	--根据开服天数筛选出对应天数区间的配置
	local data_list = {}
	local temp_level = nil
	if not total_recharge_cfg or not next(total_recharge_cfg) then return end
	for k,v in pairs(total_recharge_cfg) do
		local data = __TableCopy(v)
		local is_geted = self.recharge.total_charge_value >= v.need_chognzhi
		local is_have_opp = 1 == self:GetRewardState(v.seq)

		local instruction = string.format(Language.Activity.RandTotalRechargItemDesc_1, self.recharge.total_charge_value, v.need_chognzhi) -- v.need_chognzhi,

		data.is_geted = is_geted
		data.is_have_opp = is_have_opp
		data.instruction = instruction
		data.reward_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_TOTAL_CHONGZHI
		table.insert(data_list, data)
	end

	local sort_func = function (a, b)
		local order_a = 100000
		local order_b = 100000
		if a.is_have_opp then
			order_a = order_a + 10000
		end
		if b.is_have_opp then
			order_b = order_b + 10000
		end

		if a.seq > b.seq then
			order_a = order_a + 100
		elseif a.seq < b.seq then
			order_b = order_b + 100
		end
		return order_a < order_b
	end

	table.sort(data_list, sort_func)
	return data_list
end

function TotalChargeWGData:RemindTotalCharge()
	local num = 0
	local data_list = self:GetRechargeDataInfo()
	for i=1,#data_list do
		if (self:GetRewardState(data_list[i].seq)) == 0 and self.recharge.total_charge_value >= data_list[i].need_chognzhi then
			return 1
		end
	end
	return num
end

function TotalChargeWGData:GetSelectIndex()
	local index = 0
	for i = 0, 32 do
		local is_lingqu = self:GetRewardState(i)
		if is_lingqu == 0 then
			index = i
			break
		end
	end
	return index
end