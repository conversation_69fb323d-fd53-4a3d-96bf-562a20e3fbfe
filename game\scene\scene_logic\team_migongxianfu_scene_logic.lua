TeamMiGongXianFuSceneLogic = TeamMiGongXianFuSceneLogic or BaseClass(CommonFbLogic)

function TeamMiGongXianFuSceneLogic:__init()
	self.door_obj_list = {}
end

function TeamMiGongXianFuSceneLogic:__delete()
	self.door_obj_list = {}
end

function TeamMiGongXianFuSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	
	DailyWGCtrl.Instance:Close()

	FuBenWGCtrl.Instance:OpenTaskFollow()
end

function TeamMiGongXianFuSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
end

function TeamMiGongXianFuSceneLogic:Out()
	CommonFbLogic.Out(self)
	FuBenWGCtrl.Instance:CloseTaskFollow()
end

function TeamMiGongXianFuSceneLogic:UpdateSceneLogic()
	local scene_logic_info = FuBenWGData.Instance:GetMgxfTeamFbSceneLogicInfo()
	if nil == scene_logic_info then
		return
	end
	local cur_layer = scene_logic_info.layer
	local cur_layer_door_list = scene_logic_info.door_status_list[cur_layer]
	if nil == cur_layer_door_list then
		return
	end

	local layer_config = FuBenWGData.Instance:GetMgxfTeamFbLayerConfig(cur_layer)
	for k,v in pairs(cur_layer_door_list) do
		local door_vo = GameVoManager.Instance:CreateVo(DoorVo)
		door_vo.obj_id = Scene.Instance:GetClientObjId()
		door_vo.door_id = k
		door_vo.name = Language.FuBen.MiGongDoorName
		door_vo.type = 10
		door_vo.pos_x = layer_config["door" .. k + 1 .."_x"]
		door_vo.pos_y = layer_config["door" .. k + 1 .."_y"]
		if door_vo.pos_x > 0 and door_vo.pos_y > 0 then
			if nil == self.door_obj_list[k]  then
				local door_obj = Scene.Instance:CreateDoor(door_vo)
				self.door_obj_list[k] = door_obj
			end
			local door_effect_id = FuBenWGData.GetMiGongDoorEffectId(v.status)
			local anim_path, anim_name = ResPath.GetEffectAnimPath(door_effect_id)
			self.door_obj_list[k]:GetModel():ChangeLayerResFrameAnim(GRQ_SCENE_OBJ - 1, InnerLayerType.Main, anim_path, anim_name, false, FrameTime.Door)
		end
	end
end