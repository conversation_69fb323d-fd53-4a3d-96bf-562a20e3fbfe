require("game/merge_activity/merge_leichong/merge_leichong_recharge_wg_data")

MergeLeiChongRechargeWGCtrl = MergeLeiChongRechargeWGCtrl or BaseClass(BaseWGCtrl)
function MergeLeiChongRechargeWGCtrl:__init()
	if MergeLeiChongRechargeWGCtrl.Instance then
		ErrorLog("[MergeLeiChongRechargeWGCtrl] Attemp to create a singleton twice !")
	end
	MergeLeiChongRechargeWGCtrl.Instance = self
	self.data = MergeLeiChongRechargeWGData.New()
	self:RegisterAllProtocols()
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
end

function MergeLeiChongRechargeWGCtrl:__delete()
	MergeLeiChongRechargeWGCtrl.Instance = nil
	self.data:DeleteMe()
	self.data = nil
	if self.LCdelay_timer then
		GlobalTimerQuest:CancelQuest(self.LCdelay_timer)
		self.LCdelay_timer = nil
	end
end

function MergeLeiChongRechargeWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCCSALimitRechargeInfo, "OnSCCSALimitRechargeInfo")
	self:RegisterProtocol(CSCSALimitRechargeOpera)
end

function MergeLeiChongRechargeWGCtrl:OnSCCSALimitRechargeInfo(protocol)
	self.data:SetLeiChongRechargeData(protocol)
	MergeActivityWGData.Instance:GetActivityIsEvent(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_LIMIT_RECHARGE)
	MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2110)
	RemindManager.Instance:Fire(RemindName.MergeTotalRecharge)
	
end

function MergeLeiChongRechargeWGCtrl:SendLeiChongRechargeReq(type, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCSALimitRechargeOpera)
	protocol.type = type
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

function MergeLeiChongRechargeWGCtrl:OnPassDay()
		--做个容错，跨天的时候，服务器的数据可能还没同步过来
	self.LCdelay_timer = GlobalTimerQuest:AddDelayTimer(function()
		MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2110)
		self.LCdelay_timer = nil
	end, 1)
end

