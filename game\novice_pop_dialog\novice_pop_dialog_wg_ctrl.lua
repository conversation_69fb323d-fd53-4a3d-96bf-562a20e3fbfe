require("game/novice_pop_dialog/novice_pop_dialog_wg_data")
require("game/novice_pop_dialog/novice_pop_dialog_view")

-- 新手弹窗对话控制
NovicePopDialogWGCtrl = NovicePopDialogWGCtrl or BaseClass(BaseWGCtrl)
function NovicePopDialogWGCtrl:__init()
	if NovicePopDialogWGCtrl.Instance ~= nil then
		ErrorLog("[NovicePopDialogWGCtrl] attempt to create singleton twice!")
		return
	end
	NovicePopDialogWGCtrl.Instance = self

	self.update_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.Update, self), 0.5)

	self.task_change = GlobalEventSystem:Bind(OtherEventType.TASK_CHANGE, BindTool.Bind(self.OnTaskChange, self))

	self.data = NovicePopDialogWGData.New()
	self.view = NovicePopDialogView.New(GuideModuleName.NovicePopDialogView)

	-- 已触发列表
	self.triggered_list = {}
end

function NovicePopDialogWGCtrl:__delete()
	NovicePopDialogWGCtrl.Instance = nil

	if self.task_change then
		GlobalEventSystem:UnBind(self.task_change)
		self.task_change = nil
	end

	GlobalTimerQuest:CancelQuest(self.update_timer)
	self.update_timer = nil

	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil
end

function NovicePopDialogWGCtrl:Update()
	self:CheckEnterAreaTrigger()
end

function NovicePopDialogWGCtrl:OnTaskChange(task_event_type, task_id)
	if task_event_type ==TASK_EVENT_TYPE.ACCEPTED then
		self:CheckAcceptTaskTrigger(task_id)
	elseif task_event_type ==TASK_EVENT_TYPE.COMPLETED then
		self:CheckCompletedTaskTrigger(task_id)
	end
end

function NovicePopDialogWGCtrl:CheckAcceptTaskTrigger(task_id)
	if self.data == nil then
		return
	end

	local cfg = self.data:GetAcceptTaskTriggerCfgByTaskId(task_id)
	if cfg then
		if GLOBAL_MAIN_TASK_POP_SWITCH then
			print_error("接取任务有弹窗数据:",task_id)
		end
		if not self.data:IsTriggered(cfg[1].id) then
			-- 弹出剧情面板
			ViewManager.Instance:Close(GuideModuleName.NovicePopDialogView)
			ViewManager.Instance:Open(GuideModuleName.NovicePopDialogView, nil, "all", {data = cfg})
		else
			if GLOBAL_MAIN_TASK_POP_SWITCH then
				print_error("已经触发过该剧情弹窗：",cfg[1].id)
			end
		end
	end
end

function NovicePopDialogWGCtrl:CheckCompletedTaskTrigger(task_id)
	if self.data == nil then
		return
	end

	local cfg = self.data:GetCompleteTaskTriggerCfgByTaskId(task_id)
	if cfg then
		if GLOBAL_MAIN_TASK_POP_SWITCH then
			print_error("完成任务有弹窗数据:",task_id)
		end
		if not self.data:IsTriggered(cfg[1].id) then
			ViewManager.Instance:Close(GuideModuleName.NovicePopDialogView)
			ViewManager.Instance:Open(GuideModuleName.NovicePopDialogView, nil, "all", {data = cfg})
		else
			if GLOBAL_MAIN_TASK_POP_SWITCH then
				print_error("已经触发过该剧情弹窗：",cfg[1].id)
			end
		end
	end
end

-- 检查移动到某个区域触发
function NovicePopDialogWGCtrl:CheckEnterAreaTrigger()
	if self.data == nil then
		return
	end

	local cfg = self.data:GetEnterAreaTriggerCfgBySceneId(Scene.Instance:GetSceneId())

	if cfg then
		for i,v in ipairs(cfg) do
			local role_x, role_y = Scene.Instance:GetMainRole():GetLogicPos()
			local first_step_cfg = v.cfg[1]
			local need_accept_task_id = first_step_cfg.trigger_param_2
			if need_accept_task_id == 0 or need_accept_task_id == "" or TaskWGData.Instance:GetTaskIsAccepted(tonumber(need_accept_task_id)) then
				if not self.data:IsTriggered(first_step_cfg.id) and GameMath.IsInPolygon(v.pos_list, {x = role_x, y = role_y}) then
					-- 弹出剧情面板
					ViewManager.Instance:Close(GuideModuleName.NovicePopDialogView)
					ViewManager.Instance:Open(GuideModuleName.NovicePopDialogView, nil, "all", {data = v.cfg})
				end
			end
		end
	end
end