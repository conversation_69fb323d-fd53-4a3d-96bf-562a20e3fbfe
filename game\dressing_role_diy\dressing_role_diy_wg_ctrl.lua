require("game/dressing_role_diy/dressing_role_diy_view")
require("game/dressing_role_diy/dressing_role_diy_wg_data")
require("game/dressing_role_diy/dressing_role_diy_rename_view")
require("game/dressing_role_diy/dressing_replace_diy_view")

DressingRoleDiyWGCtrl = DressingRoleDiyWGCtrl or BaseClass(BaseWGCtrl)

function DressingRoleDiyWGCtrl:__init()
	if DressingRoleDiyWGCtrl.Instance then
		ErrorLog("[DressingRoleDiyWGCtrl] attempt to create singleton twice!")
		return
	end

	DressingRoleDiyWGCtrl.Instance = self

	self.data = DressingRoleDiyWGData.New()
    self.view = DressingRoleDiyView.New(GuideModuleName.DressingRoleDiyView)
    self.rename_view = DressingRoleDiyRenameView.New()
	self.repalce_view = DressingReplaceDiyView.New()

    self:RegisterAllProtocols()
end

function DressingRoleDiyWGCtrl:__delete()
	DressingRoleDiyWGCtrl.Instance = nil

    if self.view then
        self.view:DeleteMe()
        self.view = nil
    end

    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

    if self.rename_view then
        self.rename_view:DeleteMe()
        self.rename_view = nil
    end

	if self.repalce_view then
        self.repalce_view:DeleteMe()
        self.repalce_view = nil
    end
end

function DressingRoleDiyWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSDiyAppearanceOperate)
	self:RegisterProtocol(CSSetDiyAppearance)
    self:RegisterProtocol(CSDiyAppearanceProjectSetName)

	self:RegisterProtocol(SCDiyAppearanceInfo, "OnSCDiyAppearanceInfo")
	self:RegisterProtocol(SCDiyAppearanceProjectUpdate, "OnSCDiyAppearanceProjectUpdate")
	self:RegisterProtocol(SCDiyAppearanceTypeActiveInfo, "OnSCDiyAppearanceTypeActiveInfo")
end

--请求（激活， 使用）
function DressingRoleDiyWGCtrl:SendCSDiyAppearanceOperate(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSDiyAppearanceOperate)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

-- 易容方案
function DressingRoleDiyWGCtrl:SendCSSetDiyAppearance(sex, prof, project_id, diy_appearance_info)
	local protocol = ProtocolPool.Instance:GetProtocol(CSSetDiyAppearance)
	protocol.project_id = project_id
	protocol.sex = sex
	protocol.prof = prof

    protocol.default_body_res_id = diy_appearance_info.body_id or 0
	protocol.default_face_res_id = diy_appearance_info.face_id or 0
	protocol.default_hair_res_id = diy_appearance_info.hair_id or 0

	protocol.hair_color = diy_appearance_info.hair_color or {}
	protocol.eye_size = diy_appearance_info.eye_size or 0
	protocol.eye_position = diy_appearance_info.eye_position or 0
	protocol.eye_shadow_color = diy_appearance_info.eye_shadow_color or {}
	protocol.left_pupil_type = diy_appearance_info.left_pupil_type or 0
	protocol.left_pupil_size = diy_appearance_info.left_pupil_size or 0
	protocol.left_pupil_color = diy_appearance_info.left_pupil_color or {}
	protocol.right_pupil_type = diy_appearance_info.right_pupil_type or 0
	protocol.right_pupil_size = diy_appearance_info.right_pupil_size or 0
	protocol.right_pupil_color = diy_appearance_info.right_pupil_color or {}
	protocol.mouth_size = diy_appearance_info.mouth_size or 0
	protocol.mouth_position = diy_appearance_info.mouth_position or 0
	protocol.mouth_color = diy_appearance_info.mouth_color or {}
	protocol.face_decal_id = diy_appearance_info.face_decal_id or 0
    protocol.preset_seq = diy_appearance_info.preset_seq or 0

	protocol:EncodeAndSend()
end

function DressingRoleDiyWGCtrl:SendCSDiyAppearanceProjectSetName(project_id, sex, prof, project_name)
	local protocol = ProtocolPool.Instance:GetProtocol(CSDiyAppearanceProjectSetName)
	protocol.project_id = project_id
	protocol.sex = sex
	protocol.prof = prof
	protocol.project_name = project_name or ""
	protocol:EncodeAndSend()
end

function DressingRoleDiyWGCtrl:OnSCDiyAppearanceInfo(protocol)
	--print_error("=======全部信息======", protocol)
    self.data:SetAllDiyAppearanceInfo(protocol)
end

function DressingRoleDiyWGCtrl:OnSCDiyAppearanceProjectUpdate(protocol)
	--print_error("=======单个信息======", protocol)
    self.data:SetUpdateDiyAppearanceInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.DressingRoleDiyView)
end

function DressingRoleDiyWGCtrl:OnSCDiyAppearanceTypeActiveInfo(protocol)
	--print_error("=======激活信息======", protocol)
	self.data:SetDiyAppearanceActiveInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.RoleDiyAppearanceView)
end

function DressingRoleDiyWGCtrl:OpeRenameView(info)
	self.rename_view:SetShowDataAndOpen(info)
end

function DressingRoleDiyWGCtrl:OpeReplaceDiyView(info)
	self.repalce_view:SetShowDataAndOpen(info)
end