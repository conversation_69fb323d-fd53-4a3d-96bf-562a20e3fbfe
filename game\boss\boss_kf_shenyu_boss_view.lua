--圣域boss
function BossView:InitShengyuBossView()
	self:CreateShengyuBossCellList()
	XUI.AddClickEventListener(self.node_list.btn_play_shengyu<PERSON>, BindTool.Bind1(self.BrowseShengyuPlayInfo, self))
	XUI.AddClickEventListener(self.node_list.btn_goto_kill_shengyu<PERSON>s, BindTool.Bind1(self.GoToKillShengyuBoss, self))
	BossWGData.Instance:SetShenYuIndex(-1)
	if not self.activity_change_callback then
		self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack,self)
		ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)
	end
end

function BossView:DeleteShengyuBossView()
	if self.rare_cell_list then
		for k,v in pairs(self.rare_cell_list) do
			v:DeleteMe()
		end
	self.rare_cell_list = nil
	end

	if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
		self.activity_change_callback = nil
	end
end

function BossView:CreateShengyuBossCellList()
	self.rare_cell_list = {}
	for i = 1, 4 do
		local cell = ItemCell.New(self.node_list["ph_rarecell_" .. i])
		table.insert(self.rare_cell_list, cell)
	end
end

function BossView:OnFlushShengyuBossView()
	self:ShengyuBossLsitSelectCallBack()
	local bossdata_instance = BossWGData.Instance
	local open_time = bossdata_instance:GetNextFlushSacredBossTurnTime()
	self.node_list.lbl_boss_update.text.text = string.format(Language.Boss.ShengYu_ChangeTurn, open_time)
	self.node_list.lbl_fatigue_val.text.text = ToColorStr(bossdata_instance:GetShengYuBossTire(), COLOR3B.GREEN)

	XUI.SetButtonEnabled(self.node_list.btn_goto_kill_shengyuboss,bossdata_instance:GetShengYuBossState())
end

function BossView:ShengyuBossLsitSelectCallBack()
	local boss_cfg = BossWGData.Instance:GetSacredBossCfgList()[1]
	self.layer = boss_cfg.layer_index

	self.kill_reward = boss_cfg.kill_reward
	if self.kill_reward then
		for k,v in pairs(self.kill_reward) do
			if self.rare_cell_list[k + 1] then
				self.rare_cell_list[k + 1]:SetData(v)
				self.rare_cell_list[k + 1]:SetItemTipFrom(ItemTip.FROM_BOSS)
			end
		end
	end
end

-- 前往击杀
function BossView:GoToKillShengyuBoss()
	if Scene.Instance:GetSceneType() == SceneType.SHENGYU_BOSS then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.ShengyuBossIntoTips)
		return
	end
	if self.layer then
		local bossdata_instance = BossWGData.Instance
		if bossdata_instance:GetShengYuBossState() then
			bossdata_instance:SetShengyuBossBossLayer(self.layer)
			CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_SHENGYU_BOSS, self.layer) --ACTIVITY_TYPE.KF_SHENGYU_BOSS == 3089
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.GUILDJIUHUINOOPEN) --活动未开启
		end
	end
end

function BossView:BrowseShengyuPlayInfo()
	RuleTip.Instance:SetContent(Language.Boss.ShengyuPlayDes2, Language.Boss.PlayTitle)
end

--活动刷新
function BossView:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.KUAFUSACREDBOSS then
		self:Flush()
	end
end
------------------------------------------------计入场景左边的东西-------------------------------------------------------------------------------------
function BossView:OnClickShengyuAscription()
	local boss_cfg_list = BossWGData.Instance:GetSacredBossCfgList()
	local boss_pos = Split(boss_cfg_list.boss_pos, ",")

	Scene.Instance:GetSceneLogic():MoveToPos(boss_pos[1], boss_pos[2], nil, true)
end

function BossView:OnClickShengyuAscriptionStart()
	if nil == BossWGCtrl.Instance.view.ascription_countdown_timer then
		BossWGCtrl.Instance.view.ascription_countdown_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind(BossWGCtrl.Instance.view.ShengyuAscriptionUpdate, BossWGCtrl.Instance.view), 1)
	end
end

function BossView:OnClickShengyuAscriptionEnd()
	if BossWGCtrl.Instance.view.ascription_countdown_timer then
		GlobalTimerQuest:CancelQuest(BossWGCtrl.Instance.view.ascription_countdown_timer)
		BossWGCtrl.Instance.view.ascription_countdown_timer = nil
	end
end

------------------------------------------------------------------------------------------------------------------------------------
----------------------------------------------------
-- boss列表ItemRender
----------------------------------------------------
ShengyuBossItemRender = ShengyuBossItemRender or BaseClass(BaseRender)

function ShengyuBossItemRender:__init()

	-- self.alert_view = BossAlert.New()
	-- self.alert_view:SetCheckBoxDefaultSelect(true)
	XUI.AddClickEventListener(self.node_list.btn_tips, BindTool.Bind1(self.ShengyuBossTips, self))
	XUI.AddClickEventListener(self.node_list.layout_kf_shenyu_check_hook, BindTool.Bind1(self.ClickHookHandler, self))

	--XUI.AddClickEventListener(self.node_list.layout_hook.btn_nohint_checkbox, BindTool.Bind1(self.ClickHookHandler, self))
	self.alert = false
end

function ShengyuBossItemRender:__delete()
	if self.alert_view ~= nil then
		self.alert_view:DeleteMe()
		self.alert_view = nil
	end
	self.alert = nil
end

function ShengyuBossItemRender:OnFlush()
	if self.data == nil then return end
	--print_error(self.index,self.data)
	if self.index <= 10 then
		self.node_list.title_name.text.text = string.format(Language.Boss.ShenYuZhangJie,Language.Common.UpNum[self.index ])
	elseif self.index <= 19 and self.index > 10 then
		local str = Language.Common.UpNum[10] ..Language.Common.UpNum[self.index - 10]
		self.node_list.title_name.text.text = string.format(Language.Boss.ShenYuZhangJie,str)
	else
		local str = Language.Common.UpNum[2] ..Language.Common.UpNum[10]
		self.node_list.title_name.text.text = string.format(Language.Boss.ShenYuZhangJie,str)
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	local boss_cfg = BossWGData.Instance:GetSacredBossCfgList()
	local level_color = role_level >= boss_cfg.level_limit and COLOR3B.D_GREEN or COLOR3B.D_RED

	local is_vis, level = RoleWGData.Instance:GetDianFengLevel(boss_cfg.level_limit)

	level = is_vis and level or boss_cfg.level_limit
	self.node_list.rich_need_level.text.text = level

	self:RefreshTime()

	local flag = BossWGData.Instance:GetShengyuBossBossKillConcernFlag(self.index)
	self.node_list.img_nohint_hook:SetActive(flag == 1 and true or false)
	local layer_bg_index = 0
	if self.index > 10 and self.index <= 20 or self.index % 10 == 0 then
		layer_bg_index = 2
	elseif self.index > 20 and self.index % 10 ~= 0 then
		layer_bg_index = 3
	else
		layer_bg_index = 1
	end
	local index1 = self.index
	local index2 = -1
	if self.index > 10 then
		index1 = math.floor(self.index / 10)
		index2 = self.index % 10
		index1 = (1 == index1) and 10 or index1
	end
end

function ShengyuBossItemRender:RefreshTime()
	if self.node_list == nil then return end
	local str = (self.data.next_flush_time == nil or self.data.next_flush_time - TimeWGCtrl.Instance:GetServerTime() <= 0) and Language.Boss.BossRefresh or Language.FuBen.UpdateCountDown .. TimeUtil.FormatSecond(self.data.next_flush_time - TimeWGCtrl.Instance:GetServerTime(), 3)
	--self.node_list.layout_boss_info.lbl_need_level.text.text = str)
	self.node_list.lbl_need_level.text.text = str
	if self.data.next_flush_time == nil or self.data.next_flush_time - TimeWGCtrl.Instance:GetServerTime() <= 0 then
		self.node_list.lbl_need_level.text.text = str
	else
		self.node_list.lbl_need_level.text.text = string.format(Language.Boss.RedColor,str)
	end
end
--如果要创建不同的选中特效可以重写这个函数
function ShengyuBossItemRender:CreateSelectEffect()

end

function ShengyuBossItemRender:ShengyuBossTips()
	local boss_cfg = BossWGData.Instance:GetSacredBossCfgList()
	BossWGCtrl.Instance:SendSacredBossReq(SACRED_BOSS_OPERA_TYPE.SACRED_BOSS_OPERA_TYPE_GET_ALL_INFO, boss_cfg.layer, self.data.boss_id)
	BossWGCtrl.Instance.view.shengyu_boss_list:SelectIndex(self.index)
end

function ShengyuBossItemRender:ClickHookHandler()
	self.alert_view = BossWGData.Instance.shenyu_alert_view
	if IS_ON_CROSSSERVER then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.OnCrossServerTip)
		return
	end
	local img_hook = self.node_list.img_nohint_hook
	local boss_cfg = BossWGData.Instance:GetSacredBossCfgList()
	if not img_hook:GetActive() then
		self.alert_view:UseOne()
		self.alert_view:NoCloseButton()
		self.alert_view:SetShowCheckBox(true)
		self.alert_view:SetLableString(Language.Boss.FouseOnBossAlter)
		self.alert_view:SetCheckBoxDefaultSelect(false)
		self.alert_view:SetOkFunc(

			function()
				BossWGData.Instance:SetShenYuIndex(self.index)
				img_hook:SetActive(true)
				-- BossWGCtrl.Instance:SendSacredBossReq(SACRED_BOSS_OPERA_TYPE.SACRED_BOSS_OPERA_TYPE_CONCERN_BOSS, boss_cfg.layer, self.data.boss_id)
				if self.alert_view:GetIsNolongerTips() then
					self.alert = true
				end
				self.alert_view:Close()
			end
		)
		self.alert_view:SetCancelFunc(
			function()
				img_hook:SetActive(false)
				self.alert_view:CloseBossAlert()
			end
			)
		self.alert_view:OpenBossAlert()
	else
		BossWGData.Instance:SetShenYuIndex(self.index)
		-- BossWGCtrl.Instance:SendSacredBossReq(SACRED_BOSS_OPERA_TYPE.SACRED_BOSS_OPERA_TYPE_UNCONCERN_BOSS, boss_cfg.layer, self.data.boss_id)
	end
end
function ShengyuBossItemRender:OnSelectChange(is_select)
	self.node_list.is_select_bg:SetActive(is_select)
end
