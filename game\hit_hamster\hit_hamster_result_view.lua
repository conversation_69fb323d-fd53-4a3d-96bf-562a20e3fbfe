HitHamsterResultView = HitHamsterResultView or BaseClass(SafeBaseView)

function HitHamsterResultView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/hit_hamster_prefab", "hit_hamster_result")
end

function HitHamsterResultView:LoadCallBack()
    if not self.time_reward_list then
        self.time_reward_list = AsyncListView.New(ItemCell, self.node_list.time_reward_list)
        self.time_reward_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list.play_again, BindTool.Bind(self.OnClickStartGame, self))
    XUI.AddClickEventListener(self.node_list.play_start, BindTool.Bind(self.OnClickStartGame, self))
end

function HitHamsterResultView:ReleaseCallBack()
    if self.time_reward_list then
        self.time_reward_list:DeleteMe()
        self.time_reward_list = nil
    end

	self.show_data = nil
end

function HitHamsterResultView:SetShowData(show_result_data)
	self.show_data = show_result_data
end


function HitHamsterResultView:OnFlush(param_t)
	if not self.show_data then
		return
	end

	self.node_list.normal_status:CustomSetActive(not self.show_data.is_experience)
	self.node_list.experience_status:CustomSetActive(self.show_data.is_experience)

	if not self.show_data.is_experience then
		self:FlushMessage()
	end
end

function HitHamsterResultView:FlushMessage()
	local now_rank_data = HitHamsterWGData.Instance:GetMyWhackMoleRank()
	local last_rank_data = HitHamsterWGData.Instance:GetOldMyWhackMoleRank()

	if (not last_rank_data) or (not now_rank_data) then
		return
	end

	self.node_list.new_best:CustomSetActive(HitHamsterWGData.Instance:GetWhackMoleCurrScore() > last_rank_data.score)
	self.node_list.rank_arrow:CustomSetActive(now_rank_data.rank < last_rank_data.rank and last_rank_data.rank ~= 999)
	self.node_list.rank_up:CustomSetActive(now_rank_data.rank < last_rank_data.rank and last_rank_data.rank ~= 999)
	self.node_list.rank_up.text.text = last_rank_data.rank - now_rank_data.rank
	self.node_list.curr_score_txt.text.text = HitHamsterWGData.Instance:GetWhackMoleCurrScore()
	self.node_list.combo_times.text.text = HitHamsterWGData.Instance:GetWhackMoleBestHit()
	self.node_list.rank_txt.text.text = string.format(Language.Activity.XuQiuJiFenRank, now_rank_data.rank)  

	-- 检测是否可以领取奖励
	local is_can_get, times_reward = HitHamsterWGData.Instance:CheckHaveTimeRewardGet()
	local reward_cfg = HitHamsterWGData.Instance:GetJoinRewardCfg()
	local reward_count = #reward_cfg
	self.node_list.time_reward_list:CustomSetActive(is_can_get or times_reward < reward_count)
	self.node_list.time_reward_over:CustomSetActive((not is_can_get) and times_reward > reward_count)

	if times_reward ~= nil then
		local times_reward_cfg = HitHamsterWGData.Instance:GetJoinRewardByJoinTimes(times_reward)
		if times_reward_cfg ~= nil then
			-- 发送领取奖励
			self.node_list.time_reward_over:CustomSetActive(false)
			self.node_list.time_reward_list:CustomSetActive(true)
			self.time_reward_list:SetDataList(times_reward_cfg.reward_item)
			HitHamsterWGCtrl.Instance:SendWhackMoleJoinReward()
		end
	end
end

-- 游戏开始
function HitHamsterResultView:OnClickStartGame()
	self:Close()
	HitHamsterWGCtrl.Instance:SendWhackMoleGameStart()
end
