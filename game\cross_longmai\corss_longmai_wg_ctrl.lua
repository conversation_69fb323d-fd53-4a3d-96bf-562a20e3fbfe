require("game/cross_longmai/corss_longmai_wg_data")
-- 龙脉
require("game/cross_longmai/corss_longmai_view")
require("game/cross_longmai/corss_longmai_scene_view")

-- 商店
require("game/cross_longmai/longmai_shop_view")
require("game/cross_longmai/longmai_library_view")
-- 玩法
require("game/cross_longmai/corss_longmai_war_rule_view")

CrossLongMaiWGCtrl = CrossLongMaiWGCtrl or BaseClass(BaseWGCtrl)
function CrossLongMaiWGCtrl:__init()
	if CrossLongMaiWGCtrl.Instance then
		error("[CrossLongMaiWGCtrl]:Attempt to create singleton twice!")
	end

	CrossLongMaiWGCtrl.Instance = self

    self.data = CrossLongMaiWGData.New()
    -- 龙脉
    self.view = CrossLongMaiView.New(GuideModuleName.CrossLongMaiView)
    self.scene_view = CrossLongMaiSceneView.New()
    -- 商店
    self.longmai_shop_view = LongMaiShopView.New(GuideModuleName.LongMaiShop)
    self.longmai_library_view = LongMaiLibraryView.New()
    -- 玩法
    self.rule_view = CrossLongMaiWarRuleView.New()

    self:RegisterAllProtocols()
    self:RegisterAllEvents()
end

function CrossLongMaiWGCtrl:__delete()
    self:UnRegisterAllEvents()

    self.data:DeleteMe()
	self.data = nil
    -- 龙脉
    self.view:DeleteMe()
    self.view = nil

    self.scene_view:DeleteMe()
    self.scene_view = nil
    -- 商店
    if self.longmai_shop_view then
        self.longmai_shop_view:DeleteMe()
        self.longmai_shop_view = nil
    end

    if self.longmai_library_view then
        self.longmai_library_view:DeleteMe()
        self.longmai_library_view = nil
    end
    -- 玩法
    self.rule_view:DeleteMe()
    self.rule_view = nil
    
    CrossLongMaiWGCtrl.Instance = nil
end

function CrossLongMaiWGCtrl:RegisterAllProtocols()
    -- 龙脉
	self:RegisterProtocol(SCCrossDragonSceneInfo, "OnSCCrossDragonSceneInfo")
    self:RegisterProtocol(SCCrossDragonBossGatherInfo, "OnSCCrossDragonBossGatherInfo")

    -- 商店
    self:RegisterProtocol(CSCrossDragonVeinOperate)
    self:RegisterProtocol(SCCrossDragonVeinShopInfo,"OnSCCrossDragonVeinShopInfo")

end

function CrossLongMaiWGCtrl:RegisterAllEvents()
	self.act_change = BindTool.Bind(self.OnActivityChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)
end

function CrossLongMaiWGCtrl:UnRegisterAllEvents()
    if self.act_change then
        ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
        self.act_change = nil
    end
end

-- 活动改变
function CrossLongMaiWGCtrl:OnActivityChange(activity_type, status, next_time, open_type)
    if activity_type == ACTIVITY_TYPE.KF_LONGMAI then
        -- print_error("---活动改变---", status)
        -- status == ACTIVITY_STATUS.OPEN
        RemindManager.Instance:Fire(RemindName.CrossLongMaiAct)
    end
end

-- 龙脉
-- 场景信息
function CrossLongMaiWGCtrl:OnSCCrossDragonSceneInfo(protocol)
    -- print_error("----场景信息-----", protocol)
    self.data:SetSceneInfo(protocol)
    self:FlushSceneView("boss_time")
end

-- boss宝箱信息
function CrossLongMaiWGCtrl:OnSCCrossDragonBossGatherInfo(protocol)
    -- print_error("----boss宝箱信息-----", protocol)
    self.data:SetBossGatherInfo(protocol)
    self:FlushSceneView("boss_list")
end

function CrossLongMaiWGCtrl:OnBossHurtInfo(state)
    if state then
        self:FlushSceneView("hurt_list")
    end
end

-- 采集次数
function CrossLongMaiWGCtrl:OnGatherTimesChange(value)
    -- print_error("----已采集次数-----", value)
    self.data:SetGatherTimes(value)

    self:FlushSceneView("gather_times")
    RemindManager.Instance:Fire(RemindName.CrossLongMaiAct)
end

-- 查找NPC
function CrossLongMaiWGCtrl:FindNpc()
    local oher_cfg = self.data:GetOtherCfg()
    local scene_id = oher_cfg.npc_scene
    local npc_id = oher_cfg.npcid
    local curr_sence_id = Scene.Instance:GetSceneId()

    local scene_npc_cfg
    local scene_cfg = ConfigManager.Instance:GetSceneConfig(scene_id)
    if scene_cfg ~= nil and scene_cfg.npcs ~= nil then
        for i, j in pairs(scene_cfg.npcs) do
            if j.id == npc_id then
                scene_npc_cfg = j
                break
            end
        end
    end

    if scene_npc_cfg == nil then
        return
    end

    local function move_to_pos()
        MoveCache.SetEndType(MoveEndType.NpcTask)
        MoveCache.param1 = npc_id
        local range = TaskWGData.Instance:GetNPCRange(npc_id)
        GuajiWGCtrl.Instance:MoveToPos(scene_id, scene_npc_cfg.x, scene_npc_cfg.y, range)
    end

    if curr_sence_id == scene_id then
        move_to_pos()
    else
        TaskWGCtrl.Instance:SendFlyByShoe(scene_id, scene_npc_cfg.x, scene_npc_cfg.y)
        TaskWGCtrl.Instance:AddFlyUpList(move_to_pos)
    end
end

function CrossLongMaiWGCtrl:GetSceneView()
    return self.scene_view
end

function CrossLongMaiWGCtrl:OpenSceneView()
    self.scene_view:Open()
end

function CrossLongMaiWGCtrl:CloseSceneView()
    self.scene_view:Close()
end

function CrossLongMaiWGCtrl:FlushSceneView(key)
    if self.scene_view:IsOpen() then
        self.scene_view:Flush(0, key)
    end
end

-- 采集完成返回
function CrossLongMaiWGCtrl:SceneGatherCompelete(gather_id)
    local gather_cfg = self.data:GetGatherCfg(gather_id)
    if gather_cfg == nil then
        return
    end

    local item_list = {}
    local reward_item = gather_cfg.reward_item
    for i = 0, #reward_item do
        table.insert(item_list, reward_item[i])
    end

    TipWGCtrl.Instance:ShowGetReward(nil, item_list)
end


-- 商店
function CrossLongMaiWGCtrl:OnSCCrossDragonVeinShopInfo(protocol)
    self.data:SetShopInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.LongMaiShop)
end

function CrossLongMaiWGCtrl:SendLongMaiShopReq(operate_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSCrossDragonVeinOperate)
    protocol.operate_type = operate_type or 0
    protocol.param_1 = param_1 or 0
    protocol.param_2 = param_2 or 0
    protocol.param_3 = param_3 or 0
    protocol:EncodeAndSend()
end

function CrossLongMaiWGCtrl:SetLongMaiFlushTimes(times)
    self.data:SetShopFlushTimes(times)
    ViewManager.Instance:FlushView(GuideModuleName.LongMaiShop)
end

function CrossLongMaiWGCtrl:LongMaiShopFLushReturn(result)
    if result == 1 then
        if self.longmai_shop_view:IsOpen() then
            self.longmai_shop_view:Flush(0, "flush_cell_anim")
        end
    end
end

function CrossLongMaiWGCtrl:OpenLongMaiLibrary(result)
    self.longmai_library_view:Open()
end

-- 玩法
function CrossLongMaiWGCtrl:OpenRuleTipsView()
    self.rule_view:Open()
end
