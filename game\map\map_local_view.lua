MapLocalView = MapLocalView or BaseClass(BaseRender)

local ITEM_MAX_HEIGHT = 63

local NPC_CELL = 1	 	--npc格子索引
local MONSTER_CELL = 2	 --怪物格子索引
local OTHER = 3	 		--其他索引 ：传送点格子，传送点图标，采集物图标
local NPC_ICON = 4	 	--npc图标索引
local MONSTER_ICON = 5	 --怪物图标

function MapLocalView:LoadCallBack()
	self.path_line = MapPathLine.New(self, self.node_list["PathLine"])
	self.icon_boss_obj_list = {}
	self.icon_gather_obj_list = {}
	self.npc_button_list = {}
	self.is_move_finished = true
	self.npc_icon_obj_list = {}
	self.monster_icon_obj_list = {}
	self.boss_icon_obj_list = {}
	self.special_icon_list = {}
	self.gather_icon_obj_list = {}
	self.door_icon_obj_list = {}
	self.region_icon_obj_list = {}
	self.is_play_tween = false
	self.run_list = {}

	self.local_to_world_timestemp = 0

	-- if nil == self.change_head_icon then
	-- 	self.change_head_icon = GlobalEventSystem:Bind(OtherEventType.CHANGE_HEAD_ICON, BindTool.Bind(self.ChangeHeadIcon, self))
	-- end

	self.all_obj_list = {}
	for i = 1, 5 do
		self.all_obj_list[i] = BaseRender.New()
	end

	-- self.monster_panl = self.node_list["MonsterTip"]		--TODO 副本中爆率信息
	-- XUI.AddClickEventListener(self.node_list["layout_open_list"],BindTool.Bind(self.OnClickChangeLine, self)) -- 换线 策划说暂时屏蔽
	XUI.AddClickEventListener(self.node_list["Icon_FlyShoe"],BindTool.Bind(self.OnClickFly, self))
	XUI.AddClickEventListener(self.node_list["btn_local_to_world"],BindTool.Bind(self.OnLocalToWorld, self))
	--XUI.AddClickEventListener(self.node_list["arrow_btn"],BindTool.Bind(self.ListAnimation, self))
	self.btn = {}
	for i = 1, 3 do
		self.btn[i] = self.node_list["SelectBtn" .. i]
		self.btn[i].toggle:AddValueChangedListener(function()
			MapWGCtrl.Instance:CloseMonsterTip()
		end)
	end

	local size_delta = self.node_list["ph_img_bg"].rect.sizeDelta
	self.map_width = size_delta.x
	self.map_height = size_delta.y
	self.node_list["ph_img_bg"].event_trigger_listener:AddPointerClickListener(BindTool.Bind(self.OnClickMiniMap, self))

	self.list_table = {}
	for i = 1, 3 do
		self.list_table[i] = self.node_list["List" .. i].transform
	end

	self.btn_list = {}
	self.line_count = 0
	self.line_list = self.node_list["line"]

	self.sort_order = 0
	self.last_move_end_time = 0

	if not self.line_render_list then
		self.line_render_list = AsyncListView.New(MainUILineButton,self.node_list["line"])
		self.line_render_list:SetSelectCallBack(BindTool.Bind(self.OnselectChangeLineBtnclick,self))
	end

	if not self.eh_load_quit then
		self.eh_load_quit = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind1(self.OnSceneLoadingQuite, self)) 	-- 进入场景加载结束回调
		self.eh_pos_change = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_POS_CHANGE, BindTool.Bind1(self.OnMainRolePosChangeFunc, self))	-- 主角位置改变(x, y)
		self.eh_move_end = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_MOVE_END, BindTool.Bind1(self.OnMainRoleMoveEnd, self))				-- 主角移动结束
		self.task_change = GlobalEventSystem:Bind(OtherEventType.TASK_CHANGE,BindTool.Bind(self.OnTaskChange, self))							-- 任务改变
		self.reset_pos = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_RESET_POS,BindTool.Bind(self.OnMainRoleMoveEnd, self))				-- 主角位置重置
		self.cannot_find_theway = GlobalEventSystem:Bind(ObjectEventType.CAN_NOT_FIND_THE_WAY, BindTool.Bind1(self.OnCanNotFindWay, self))		-- 无法找到路径
	end

	Runner.Instance:AddRunObj(self)

	--self:ChangeHeadIcon()
end

-- function MapLocalView:DoOpenTween()
-- 	UITween.ScaleShowPanel(self.node_list["layout_local_root"].transform.parent, Vector3(0.2, 1, 1), 0.15, DG.Tweening.Ease.Linear)
-- end

function MapLocalView:ShowIndexCallBack()
	self.scene_id = Scene.Instance:GetSceneId()	--当前场景id
	-- self.last_scene_id = 0
	self.is_draw_path = false
	self.is_move_finished = true
	self.is_can_draw_path = true
	self.last_move_end_time = 0
	self.is_zhu_cheng = false

	local mini_camera = Scene.Instance:GetSceneMiniMapCamera()
	if IsNil(mini_camera) then
		print_error("小地图相机为空 场景id:", self.scene_id)
	end

	self:ChangeLeftButton()
	self:SetFlyShoseState(false)
	self:Flush()
end

function MapLocalView:ReleaseCallBack()
	self:ClearCache()
	if nil ~= self.eh_load_quit then
		GlobalEventSystem:UnBind(self.eh_load_quit)
		self.eh_load_quit = nil
	end

	if nil ~= self.eh_pos_change then
		GlobalEventSystem:UnBind(self.eh_pos_change)
		self.eh_pos_change = nil
	end
	if nil ~= self.eh_move_end then
		GlobalEventSystem:UnBind(self.eh_move_end)
		self.eh_move_end = nil
	end
	if nil ~= self.reset_pos then
		GlobalEventSystem:UnBind(self.reset_pos)
		self.reset_pos = nil
	end
	if nil ~= self.task_change then
		GlobalEventSystem:UnBind(self.task_change)
		self.task_change = nil
	end

	if nil ~= self.cannot_find_theway then
		GlobalEventSystem:UnBind(self.cannot_find_theway)
		self.cannot_find_theway = nil
	end

	self:RemoveCountDown()
	if self.delay_time2 then
		GlobalTimerQuest:CancelQuest(self.delay_time2)
		self.delay_time2 = nil
	end

	if self.time_accordion then
		GlobalTimerQuest:CancelQuest(self.time_accordion)
		self.time_accordion = nil
	end

	for k, v in pairs(self.icon_gather_obj_list) do
		ResMgr:Destroy(v)
	end
	self.icon_gather_obj_list = {}

	for k, v in pairs(self.icon_boss_obj_list) do
		ResMgr:Destroy(v)
	end

	if self.line_render_list then
		self.line_render_list:DeleteMe()
		self.line_render_list = nil
	end

	if self.all_obj_list then
		for i,v in ipairs(self.all_obj_list) do
			v:DeleteMe()
		end
		self.all_obj_list = nil
	end

	self.run_list = {}
	Runner.Instance:RemoveRunObj(self)
	self.is_click_change_line = nil
	self.icon_boss_obj_list = {}
	self.is_play_tween = false
	self:ClearNpcIcon()
	self:ClearMonsterIcon()
	self:ClearGatherIcon()
	self:ClearSpecialIcon()
	self:ClearDoorIcon()
	self:ClearBossIcon()
	self:ClearRegionIcon()

	if nil ~= self.path_line then
		self.path_line:DeleteMe()
		self.path_line = nil
	end

	if self.role_head_cell then
		self.role_head_cell:SetHeadCellScale(1)
		self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
	end

	-- if self.change_head_icon then
	-- 	GlobalEventSystem:UnBind(self.change_head_icon)
	-- 	self.change_head_icon = nil
	-- end
end

function MapLocalView:RemoveCountDown()
	if self.delay_time then
		GlobalTimerQuest:CancelQuest(self.delay_time)
		self.delay_time = nil
	end
end

function MapLocalView:ChangeHeadIcon()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	self:GetRoleHeadCell()
	local appearance = role_vo and role_vo.appearance
	local data = {fashion_photoframe = appearance.fashion_photoframe}
	data.role_id = RoleWGData.Instance:InCrossGetOriginUid()
	data.sex = role_vo.sex
	data.prof = role_vo.prof
	data.is_show_main = true

	self.role_head_cell:SetImgBg(appearance and appearance.fashion_photoframe > 0)
	self.role_head_cell:SetData(data)

	self.role_head_cell:SetHeadCellScale(0.4)
end

function MapLocalView:GetRoleHeadCell()
	if not self.role_head_cell then
		self.role_head_cell = BaseHeadCell.New(self.node_list["Icon_Location"])
	end
end

function MapLocalView:IsClose( enable )
	self.is_close = enable
end

function MapLocalView:SetFlyShoseState( enable )
	self.node_list["Icon_FlyShoe"]:SetActive(enable)
end

function MapLocalView:OnClickFly()
	local main_role = Scene.Instance:GetMainRole()
	if main_role:CantPlayerDoMove(true) then
		return
	end
	if Scene.Instance:GetSceneType() ~= SceneType.Common then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CannotFindPath)
		return
	end

	local flag, str = GuajiWGCtrl.Instance:CheakCanFly()
	if not flag then
        if str then
            TipWGCtrl.Instance:ShowSystemMsg(str)
        end
        return
    end

	self:ClearOldGuaJiOperator()
	GuajiWGCtrl.Instance:SetMoveToPosCallBack(nil)
	self:FlyToPos(self.scene_id, self.target_position.x, self.target_position.y)
	-- self.monster_tip:SetActive(false)
end

function MapLocalView:OnLocalToWorld()
	if Status.NowTime - self.local_to_world_timestemp >= 1.2 then
		self.local_to_world_timestemp = Status.NowTime
		GlobalEventSystem:Fire(OtherEventType.MAP_VIEW_SWITCH, 20)
	end
end

--[[ function MapLocalView:ListAnimation()
	if self.is_play_tween then
		return
	end
	self.is_play_tween = true
	local list_panel = self.node_list["ph_list"]
	local sequence = DG.Tweening.DOTween.Sequence()
	local tween = nil
	if self.node_list["ph_list"].rect.anchoredPosition.y < 10 then
		tween = list_panel.transform:DOAnchorPosY(479, 0.5)
	else
		tween = list_panel.transform:DOAnchorPosY(7, 0.5)
	end

	--sequence:AppendInterval(0.3)
	sequence:Append(tween)
	sequence:OnComplete(function ()
		self.is_play_tween = false
	end)
	sequence:SetEase(DG.Tweening.Ease.Linear)
end
 ]]
--点击换线 TODO
function MapLocalView:OnClickChangeLine()
	self.is_click_change_line = not self.is_click_change_line
	self:SetChangeLineState()
	MapWGCtrl.Instance:CloseMonsterTip()
end

function MapLocalView:SetChangeLineState()
	self.node_list["line_content"]:SetActive(self.is_click_change_line)
	self.node_list["img_branch_line_flag_down"]:SetActive(not self.is_click_change_line)
	self.node_list["img_branch_line_flag_up"]:SetActive(self.is_click_change_line)

	if self.is_click_change_line then
		local line_count = MapWGData.Instance:GetMapMaxLineNum()
		local data = {}
		for i = 1, line_count do
			table.insert(data,{index = i})
		end
		self.line_render_list:SetDataList(data, 0)
	end
end

function MapLocalView:OnselectChangeLineBtnclick(cell)
	local main_role = Scene.Instance:GetMainRole()
	if main_role:CantPlayerDoMove(true) then
		return
	end
	self:ClearOldGuaJiOperator()
	local index = cell.data.index
	MapWGCtrl.Instance:SendSwitchSceneLineReq(index)
	self:OnClickChangeLine()
end

--场景加载完毕
function MapLocalView:OnSceneLoadingQuite()
	self.scene_id = Scene.Instance:GetSceneId()
	-- if self.is_close then
		-- return
	-- end
	self:Flush()
	self:ClearWalkPath()
end

--任务发生改变（npc处于完成任务状态/npc处于可接任务状态等）
function MapLocalView:OnTaskChange(task_event_type, task_id)
	local icon_table = MapWGData.Instance:GetNpcIcon()
	if icon_table then
		for _, v in pairs(icon_table) do
			local task_status = TaskWGData.Instance:GetNpcTaskStatus(v.npc_id)
			if task_status == GameEnum.TASK_STATUS_CAN_ACCEPT then
				v.jing_tan_hao_image:SetActive(true)
				v.wen_hao_image:SetActive(false)
				v.img_npc:SetActive(false)
			elseif task_status == GameEnum.TASK_STATUS_ACCEPT_PROCESS or task_status == GameEnum.TASK_STATUS_COMMIT then
				v.wen_hao_image:SetActive(true)
				v.jing_tan_hao_image:SetActive(false)
				v.img_npc:SetActive(false)
			else
				v.wen_hao_image:SetActive(false)
				v.jing_tan_hao_image:SetActive(false)
				v.img_npc:SetActive(true)
			end
		end
	end
end

function MapLocalView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushAllView()
		elseif k == "special_icon" then
			self:UpdateSpecialIcon()
		elseif k == "gather_icon" then
			self:UpdateGatcherlIcon()
		end
	end
end

function MapLocalView:FlushAllView()
	local mini_camera = Scene.Instance:GetSceneMiniMapCamera()
	if not IsNil(mini_camera) then
		self.node_list["ph_img_bg"].raw_image.texture = mini_camera.MapTexture
		self.node_list["ph_img_bg"].raw_image:SetNativeSize()
	end
	
	TryDelayCall(self, function ()
		self:FlushCell()
	end, 0.2, "map_delay_flush_cell")
	
	self:SetMapMainRoleImg()

	-- 换线 策划说暂时屏蔽
	-- local scene_key = MapWGData.Instance:GetMapCurLine()
	-- if scene_key and scene_key ~= 0 and Scene.Instance:GetSceneType() == SceneType.Common and CommonDataManager.GetDaXie(scene_key) then
	-- 	self.node_list["layout_open_list"]:SetActive(true)
	-- 	self.node_list["lbl_scene_line_name"].text.text = (string.format(Language.Common.Line, CommonDataManager.GetDaXie(scene_key)))
	-- else
	-- 	self.node_list["layout_open_list"]:SetActive(false)
	-- end

	--local index = MapWGData.Instance:GetMapBottomColor(self.scene_id)
	--local bundle, asset = ResPath.GetF2RawImagesPNG("di_" .. index)

	--self.node_list["mapbg"].raw_image:LoadSprite(bundle, asset)
end

function MapLocalView:FlushBtn()
	for i = 1, 3 do
		self.btn[i].accordion_element:Refresh()
	end
end

-- 清空缓存
function MapLocalView:ClearCache()
	MapWGData.Instance:ClearInfo()
	MapWGData.Instance:ClearIcon()
end

-- logic坐标转ui坐标
function MapLocalView:LogicToUI(logic_x, logic_y)
	local mini_camera = Scene.Instance:GetSceneMiniMapCamera()
	if IsNil(mini_camera) then
		return 0, 0
	end

	if logic_x == nil or logic_y == nil then
		return 0, 0
	end

	local wx, wy = GameMapHelper.LogicToWorld(logic_x, logic_y)
	local uipos = mini_camera:TransformWorldToUV(Vector3(wx, 0, wy))
	local ui_x, ui_y = self.map_width * uipos.x, self.map_height * uipos.y
	return ui_x, ui_y
end

-- ui坐标转logic坐标
function MapLocalView:UIToLogic(ui_x, ui_y)
	local mini_camera = Scene.Instance:GetSceneMiniMapCamera()
	if IsNil(mini_camera) then
		return 0, 0
	end

	local uipos_x = ui_x / self.map_width
	local uipos_y =  ui_y / self.map_height
	local world_pos = mini_camera:TransformUVToWorld(Vector2(uipos_x, uipos_y))
	local logic_x, logic_y = GameMapHelper.WorldToLogic(world_pos.x, world_pos.z)
	return logic_x, logic_y
end

function MapLocalView:MoveToPos(scene_id, x, y, call_back)
	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic ~= nil then
		scene_logic:ClearGuaJiInfo()
	end

	local scene_key = RoleWGData.Instance:GetAttr("scene_key") or 0
	GuajiWGCtrl.Instance:SetMoveToPosCallBack(call_back)
	GuajiWGCtrl.Instance:MoveToPos(scene_id, x, y, 0, false, scene_key, true)
	self:DrawWalkPath()
end

function MapLocalView:FlyToPos(scene_id, x, y)
	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic ~= nil then
		scene_logic:ClearGuaJiInfo()
	end

    local main_role = Scene.Instance:GetMainRole()
    if main_role:IsInXunYou() then
        return
    end
	TaskWGCtrl.Instance:JumpFly(scene_id, x, y)
end

--设置小地图角色人物小图标
function MapLocalView:SetMapMainRoleImg()
	local mini_camera = Scene.Instance:GetSceneMiniMapCamera()
	if IsNil(mini_camera) then
		return
	end

	local role_x, role_y = Scene.Instance:GetMainRole():GetLogicPos()
	local ui_x, ui_y = self:LogicToUI(role_x, role_y)
	self.node_list["Icon_Location"].transform:SetLocalPosition(ui_x, ui_y, 0)
end

function MapLocalView:SetMapTargetImg(flag, x, y)
	if x and y then
		local ui_x, ui_y = self:LogicToUI(x, y)
		self.node_list["Icon_FlyShoe"].transform:SetLocalPosition(ui_x, ui_y, 0)
	end
	if not flag then
		self.node_list["Icon_FlyShoe"]:SetActive(false)
		return
	end
	self.node_list["Icon_FlyShoe"]:SetActive(true)
end

--角色移动回调
function MapLocalView:OnMainRolePosChangeFunc()
	if self.is_close then
		return
	end
	if not MapWGCtrl.Instance.view:IsLoaded() then
		return
	end
	self:SetMapMainRoleImg()

	if self.last_move_end_time + 0.2 > Status.NowTime then
		self.node_list["Icon_FlyShoe"]:SetActive(false)
	else
		self.node_list["Icon_FlyShoe"]:SetActive(true)
	end

	local main_role = Scene.Instance:GetMainRole()
    if main_role:IsJump() then
		self.node_list["Icon_FlyShoe"]:SetActive(false)
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	local path_pos_list = main_role:GetPathPosList()
	if self.last_path_pos_list ~= path_pos_list then
		self.last_path_pos_list = path_pos_list
		self:ClearWalkPath()
	end

	if not self.is_draw_path and self.is_move_finished then
		self:DrawWalkPath()
	else
		self.is_move_finished = false
	end
	-- self:UpdateWalkPath()
end

--角色移动结束
function MapLocalView:OnMainRoleMoveEnd()
	if self.is_close then
		return
	end
	if not MapWGCtrl.Instance.view:IsLoaded() then
		return
	end
	local main_role = Scene.Instance:GetMainRole()
    if main_role:IsJump() then
		return
	end
	self.node_list["Icon_FlyShoe"]:SetActive(false)
	self.is_move_finished = true
	self:ClearWalkPath()
	self.last_move_end_time = Status.NowTime
end

function MapLocalView:OnClickMiniMap(event)
	-- 当前场景无法移动
	MapWGCtrl.Instance:CloseMonsterTip()
	local logic = Scene.Instance:GetSceneLogic()
	if logic and not logic:CanCancleAutoGuaji() then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Rune.CanNotCancleGuaji)
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	if main_role:CantPlayerDoMove(true) then
		return
	end

    if main_role:IsJump() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotMoveInJump)
		return
	end

	local ok, local_position = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(
		self.node_list["ph_img_bg"].rect, event.position, event.pressEventCamera, Vector2(0, 0))
	if not ok then
		return
	end

	local logic_x, logic_y = self:UIToLogic(local_position.x, local_position.y)
	if AStarFindWay:IsBlock(logic_x, logic_y) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotMoveInBlock)
		return
	end
	self:ClearOldGuaJiOperator()
	self:MoveToPos(self.map_id, logic_x, logic_y)
	GlobalEventSystem:Fire(OtherEventType.MOVE_BY_CLICK)
end

--清楚当前的挂机操作
function MapLocalView:ClearOldGuaJiOperator()
	GuajiWGCtrl.Instance:ResetMoveCache()
	GuajiWGCtrl.Instance:ClearGuajiCache()
	TaskGuide.Instance:CanAutoAllTask(false)
	GuajiWGCtrl.Instance:SetMoveToPosCallBack(nil)
	GuajiWGCtrl.Instance:StopGuaji()
end

-- 找不到去往目标的路径
function MapLocalView:OnCanNotFindWay()
	if self.is_close then
		return
	end
	GuajiWGCtrl.Instance:StopGuaji()
end

function MapLocalView:ChangeLeftButton()
	-- local scene_type = Scene.Instance:GetSceneType()
	-- if BossWGData.IsBossScene(scene_type) then
	-- 	self.btn[1]:SetActive(false)
	-- 	self.btn[3]:SetActive(false)
	-- end
end
-----------------------------------------------动态生成Cell------------------------------------------------------
function MapLocalView:FlushCell()
	local res_async_loader = AllocResAsyncLoader(self, "Button_loader")
	res_async_loader:Load("uis/view/map_ui_prefab", "Button2", nil, function (prefab)
		if nil == prefab then
			return
		end

		self:_FlushCell(prefab)
	end)
end

function MapLocalView:_FlushCell(prefab)
	self.map_id = self.scene_id
	if (self.map_id == self.last_scene_id) then
		return
	end
	self.last_scene_id = self.map_id
	local matdata_instance = MapWGData.Instance
	local config = matdata_instance:GetMapConfig(self.map_id)
	local scene_logic = Scene.Instance:GetSceneLogic()
	local scene_type = Scene.Instance:GetSceneType()
	if not config then
		print_warning("No Map Config")
		return
	end

	self.node_list["local_name"].text.text = config.name
	-- 是否是主城
	self.is_zhu_cheng = self:GetIsZhuCheng()
	local prefab_table = {}
	-- 生成NPC
	local toggle_group_1 = self.node_list["List1"].toggle_group
	local npc_index = 0
	local npc_list = {}
	local bool = false
	local npc_cfg = nil
	for k,v in pairs(config.npcs) do
		bool = TaskWGData.Instance:GetNPCHideCfg(v.id)
		if not bool then
			npc_cfg = TaskWGData.Instance:GetNPCConfig(v.id)
			bool = npc_cfg and 1 == tonumber(npc_cfg.cansee)
		end
        if not bool then
        	npc_list[#npc_list + 1] = v
        end
	end

	local npc_count = #npc_list
	for k, v in pairs(npc_list) do
		local object = ResMgr:Instantiate(prefab)
		object.transform:SetParent(self.list_table[1], false)
		local name_table = U3DNodeList(object:GetComponent(typeof(UINameTable)))

		local name = ""
		npc_cfg = TaskWGData.Instance:GetNPCConfig(v.id)
		if npc_cfg and npc_cfg.show_name and npc_cfg.show_name ~= "" then
			name = npc_cfg.show_name
		end
		name_table["normal_text"].text.text = name
		name_table["select_text"].text.text = name
		--name_table["Level_Text"].text.text = ""
		name_table["button"].toggle.group = toggle_group_1
		local info = {
			obj = object,
			x = v.x,
			y = v.y,
			id = v.id,
			obj_type = SceneObjType.Npc,
			scene_id = self.map_id,
			name = name,
			level = 0
		}
		prefab_table[info] = info
		name_table["button"].toggle:AddClickListener(BindTool.Bind2(self.ClickButton, self, info))
		name_table["cell"].button:AddClickListener(function()
					self.target_position = {}
					self.target_position.x = v.x
					self.target_position.y = v.y
					self:OnClickFly()
				end)
		npc_index = npc_index + 1
		if npc_index == npc_count then
			self.node_list["List1"].layout_element.preferredHeight = npc_count * ITEM_MAX_HEIGHT
		end
	end

	-- 生成Door
	local door_button_list = {}
	local toggle_group_3 = self.node_list["List3"].toggle_group
	local door_count = #config.doors
	local door_index = 0
	for k, v in ipairs(config.doors) do
		if v.target_door_id == 0 then
			return
		end

		local object = ResMgr:Instantiate(prefab)
		object.transform:SetParent(self.list_table[3], false)
		local name_table = U3DNodeList(object:GetComponent(typeof(UINameTable)))
		local scene_config = matdata_instance:GetMapConfig(v.target_scene_id)
		local name = ""
		local level = 0
		if scene_config then
			name = scene_config.name
			level = scene_config.levellimit
		end

		name_table["normal_text"].text.text = name
		name_table["select_text"].text.text = name

		local info = {
			obj = object,
			x = v.x,
			y = v.y,
			id = v.id,
			obj_type = SceneObjType.Door,
			scene_id = self.map_id,
			name = name,
			level = level
		}
		prefab_table[info] = info
		name_table["button"].toggle.group = toggle_group_3
		name_table["button"].toggle:AddClickListener(BindTool.Bind2(self.ClickButton, self, info))
		name_table["cell"].button:AddClickListener(function()
					self.target_position = {}
					self.target_position.x = v.x
					self.target_position.y = v.y
					self:OnClickFly()
				end)
		door_index = door_index + 1
		if door_index == door_count then
			self.node_list["List3"].layout_element.preferredHeight = door_count * ITEM_MAX_HEIGHT
		end
		if Scene.Instance:GetSceneType() == SceneType.CROSS_LIEKUN and not scene_logic:GetShowDoorFlag() then
			object:SetActive(false)
		end
	end

	matdata_instance:SetInfo(prefab_table)

	self:FlushIcon()
	if self.is_zhu_cheng then
		self.node_list.SelectBtn2:SetActive(false)
		return
	else
		self.node_list.SelectBtn2:SetActive(true)
	end

	local max_lev = 0
	-- 生成Monster
	local monsters_list, boo = scene_logic:GetFbSceneMonsterListCfg(config.monsters)
	if false == boo then
		monsters_list = matdata_instance:GetSceneMonsterSort(config.monsters)
	end
	local monster_count = 0
	if monsters_list and not IsEmptyTable(monsters_list) then
		monster_count = #monsters_list
		local monster_index = 0
		local toggle_group_2 = self.node_list["List2"].toggle_group

		for k,v in ipairs(monsters_list) do
			table.insert(self.run_list, function()
				local object = ResMgr:Instantiate(prefab)
				object.transform:SetParent(self.list_table[2], false)
				local name_table = U3DNodeList(object:GetComponent(typeof(UINameTable)))
				name_table["normal_text"].text.text = v.name
				name_table["select_text"].text.text = v.name

				local info = {
					obj = object,
					x = v.x,
					y = v.y,
					id = v.id,
					obj_type = SceneObjType.Monster,
					scene_id = self.map_id,
					name = v.name,
					level = v.level
				}
				prefab_table[info] = info
				name_table["button"].toggle.group = toggle_group_2
				name_table["button"].toggle:AddClickListener(BindTool.Bind2(self.ClickButton, self, info))
				name_table["cell"].button:AddClickListener(function()
					self.target_position = {}
					self.target_position.x = v.x
					self.target_position.y = v.y
					self:OnClickFly()
				end)
				monster_index = monster_index + 1
				if monster_index == monster_count then
					self.node_list["List2"].layout_element.preferredHeight = monster_count * ITEM_MAX_HEIGHT
				end
			end)
		end

		self.time_accordion = GlobalTimerQuest:AddDelayTimer(function()
			if self.btn[2].accordion_element then
				self.btn[2].accordion_element:Refresh()
			end
		end, 0.1)
	end

	-- if npc_count > 0 then
	-- 	self.node_list.SelectBtn1.accordion_element.isOn = true
	-- elseif monster_count > 0 then
	-- 	self.node_list.SelectBtn2.accordion_element.isOn = true
	-- elseif door_count > 0 then
	-- 	self.node_list.SelectBtn3.accordion_element.isOn = true
	-- end
	
	if npc_count < 1 and monster_count < 1 and door_count < 1 then
		RectTransform.SetAnchoredPositionXY(self.node_list["ph_list"].rect, 0, 800)
	end

	self.btn[1]:SetActive(npc_count > 0)
	self.btn[2]:SetActive(monster_count > 0)
	self.btn[3]:SetActive(door_count > 0)

	--地图等级显示
	local map_config = MapWGData.Instance:GetMapConfig(self.scene_id)
	if monster_count == 0 then
		self.node_list["level_limit"].text.text = string.format("%s级开启", map_config.levellimit)
	else
		local max_lev = 0
		for k,v in ipairs(monsters_list) do
			local boss_config = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[v.id]
			local lev_num = 0
			if boss_config then
				if scene_type == SceneType.CROSS_LIEKUN then
					local cross_lieKun_info = GuildWGData.Instance:GetCrossLieKunFBSceneInfo()
					lev_num = boss_config.level + cross_lieKun_info.boss_extra_level
				else
					lev_num = boss_config.level
				end
			end
			if max_lev < lev_num then
				max_lev = lev_num
			end
		end
		self.node_list["level_limit"].text.text = string.format("%s-%s级开启", map_config.levellimit, max_lev)
	end
end

function MapLocalView:Update()
	if not IsEmptyTable(self.run_list) then
		local count = 0
		local list = {}
		for k,v in ipairs(self.run_list) do
			if v then
				v()
				table.insert(list,k)
				count = count + 1
			end
			if count == 3 then
				break
			end
		end
		for i=1,count do
			table.remove(self.run_list,1)
		end
	end
end

-- 点击右侧的按钮
function MapLocalView:ClickButton(index)
	local info = MapWGData.Instance:GetInfoByIndex(index)
	-- 当前场景无法移动
	local logic = Scene.Instance:GetSceneLogic()
	if logic and not logic:CanCancleAutoGuaji() then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Rune.CanNotCancleGuaji)
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	if main_role:CantPlayerDoMove(true) then
		return
	end

	self:ClearOldGuaJiOperator()

	if info then
		if (info.obj_type == SceneObjType.Npc) then
			local function call_back()
				MoveCache.SetEndType(MoveEndType.NpcTask)
				MoveCache.param1 = info.id
				GuajiCache.target_obj_id = info.id
			end
			self:MoveToPos(info.scene_id, info.x, info.y, call_back)
		elseif (info.obj_type == SceneObjType.Monster) then
				local function call_back()
					MoveCache.SetEndType(MoveEndType.Auto)
					MoveCache.param1 = info.id
					GuajiCache.target_obj_id = info.id
					GuajiCache.guaji_type = GuajiType.Monster
					GuajiCache.monster_id = info.id
				end
				
				self:MoveToPos(info.scene_id, info.x, info.y, call_back)
				MapWGData.Instance:GetSetCurSelectInfo(info)
				if Scene.Instance:GetSceneType() == SceneType.Common then
					MapWGCtrl.Instance:ShowMonsterInfo()
				end

		elseif (info.obj_type == SceneObjType.Door) then
			MoveCache.SetEndType(MoveEndType.Normal)
			self:MoveToPos(info.scene_id, info.x, info.y)
		end
	else
		print_error("找不到去往目标的路径")
	end
end

function MapLocalView:SetMonterTip(index, monsters_info)
	if monsters_info then
		self.monster_tip:SetActive(true)
		local ui_x, ui_y = self:LogicToUI(index.x, index.y)
		local anchor_x = 0
		local anchor_y = 1
		if ui_x > 110 then --面板位置超出界面时改变锚点
			anchor_x = 1
		end
		if ui_y < -165 then
			anchor_y = 0
		end
		self.mix_Level:SetActive(monsters_info.mix_level)
		self.recommend_gongji:SetActive(monsters_info.recommend_gongji)
		self.equip_level:SetActive(CommonDataManager.GetDaXie(monsters_info.equip_level))
		self.blue_num:SetActive(monsters_info.blue_num)
		self.purple_num:SetActive(monsters_info.purple_num)
		self.standard_exp:SetActive(CommonDataManager.ConverMoney(monsters_info.standard_exp or 0))

		-- self.monster_panl.rect.pivot = Vector2(anchor_x, anchor_y)
		-- self.monster_panl.transform:SetLocalPosition(ui_x, ui_y, 0)
	else
		self.monster_tip:SetActive(false)
	end
end

-----------------------------------------------动态生成Icon------------------------------------------------------
function MapLocalView:FlushIcon(scene_id)
	local mini_camera = Scene.Instance:GetSceneMiniMapCamera()
	if IsNil(mini_camera) then
		return
	end
	
	local icon_table = {}
	local mapdata_instance = MapWGData.Instance

	local npc_info, npc_count = mapdata_instance:GetNpcCfgList()
	--NPC
	self:CreateNpcIcon(npc_info, npc_count)
	--加载怪物
	if not self.is_zhu_cheng then
		local monster_info, monster_count = mapdata_instance:GetMonsterCfgList()
		self:CreateMonsterIcon(monster_info, monster_count)
	end

	--加载采集物
	-- local gather_info, gather_count = mapdata_instance:GetGatherCfgList()
	-- self:CreateGatherIcon(gather_info, gather_count)

	--加载传送门
	local door_info, door_count = mapdata_instance:GetDoorCfgList()
	self:CreateDoorIcon(door_info,door_count)

	self:UpdateBossIcon()
	self:UpdateSpecialIcon()
	self:UpdateGatcherlIcon()
end

-- 特殊处理，显示BOSS图标
function MapLocalView:UpdateBossIcon()
	local monster_info = MapWGData.Instance:GetBossCfgList()
	if monster_info then
		self:CreateBossIcon(monster_info)
	end
end

-- 刷新特殊icon
function MapLocalView:UpdateSpecialIcon()
	self:CreateSpecialIcon(MapWGData.Instance:GetSpecialIconInfo())
end

function MapLocalView:UpdateGatcherlIcon()
	self:CreateGatherIcon(MapWGData.Instance:GetGatherIconInfo())
end

--是否是主城
function MapLocalView:GetIsZhuCheng()
	return false
end

function MapLocalView:SetMapImg(obj, x, y)
	obj.transform:SetParent(self.node_list["CellParent"].transform, false)
	local ui_x, ui_y = self:LogicToUI(x, y)
	obj.transform:SetLocalPosition(ui_x, ui_y, 0)
end

------------------------------------------------------------------------------------
--创建地图图标接口
function MapLocalView:ClearNpcIcon()
	if not IsEmptyTable(self.npc_icon_obj_list) then
		for k,v in pairs(self.npc_icon_obj_list) do
			ResMgr:Destroy(v.obj)
		end
		self.npc_icon_obj_list = {}
	end
end

function MapLocalView:ClearMonsterIcon()
	if not IsEmptyTable(self.monster_icon_obj_list) then
		for k,v in pairs(self.monster_icon_obj_list) do
			ResMgr:Destroy(v.obj)
		end
		self.monster_icon_obj_list = {}
	end
end

function MapLocalView:ClearBossIcon()
	if not IsEmptyTable(self.boss_icon_obj_list) then
		for k,v in pairs(self.boss_icon_obj_list) do
			ResMgr:Destroy(v.obj)
		end
		self.boss_icon_obj_list = {}
	end
end

function MapLocalView:ClearGatherIcon()
	if not IsEmptyTable(self.gather_icon_obj_list) then
		for k,v in pairs(self.gather_icon_obj_list) do
			ResMgr:Destroy(v.obj)
		end
		self.gather_icon_obj_list = {}
	end
end

function MapLocalView:ClearSpecialIcon()
	if not IsEmptyTable(self.special_icon_list) then
		for k,v in pairs(self.special_icon_list) do
			ResMgr:Destroy(v.obj)
		end
		self.special_icon_list = {}
	end
end

function MapLocalView:ClearDoorIcon()
	if not IsEmptyTable(self.door_icon_obj_list) then
		for k,v in pairs(self.door_icon_obj_list) do
			ResMgr:Destroy(v.obj)
		end
		self.door_icon_obj_list = {}
	end
end

function MapLocalView:ClearRegionIcon()
	if not IsEmptyTable(self.region_icon_obj_list) then
		for k,v in pairs(self.region_icon_obj_list) do
			ResMgr:Destroy(v.obj)
		end
		self.region_icon_obj_list = {}
	end
end

function MapLocalView:CreateNpcIcon( npc_info, count )
	if npc_info == nil then
		return
	end
	local npc_count = count or #npc_info
	self:ClearNpcIcon()
	local res_async_loader = AllocResAsyncLoader(self, "Icon_NPC_loader")
	res_async_loader:Load("uis/view/map_ui_prefab", "Icon_NPC", nil, function (prefab)
		if nil == prefab then
			return
		end

		if npc_count > 0 then
			for k,v in pairs(npc_info) do
				local object = ResMgr:Instantiate(prefab)
				local variable_table = U3DNodeList(object:GetComponent(typeof(UINameTable)))
				local wen_hao_image = variable_table["Img1"]
				local jing_tan_hao_image = variable_table["Img"]
				local img_npc = variable_table["img_npc"]
				self:SetMapImg(object, v.x, v.y)

				local task_status = TaskWGData.Instance:GetNpcTaskStatus(v.id)
				if task_status == GameEnum.TASK_STATUS_CAN_ACCEPT then
					jing_tan_hao_image:SetActive(true)
				elseif task_status == GameEnum.TASK_STATUS_ACCEPT_PROCESS or task_status == GameEnum.TASK_STATUS_COMMIT then
					wen_hao_image:SetActive(true)
				else
					img_npc:SetActive(true)
				end
				self.npc_icon_obj_list[k] = {obj = object, }
			end
		end
	end)
end

function MapLocalView:CreateMonsterIcon(  monster_info, count )
	if monster_info == nil then
		return
	end

	local monster_count = count or #monster_info
	self:ClearMonsterIcon()
	local res_async_loader = AllocResAsyncLoader(self, "Icon_Monster_loader")
	res_async_loader:Load("uis/view/map_ui_prefab", "Icon_Monster", nil, function (prefab)
		if nil == prefab then
			return
		end
		if monster_count > 0 then
			for k,v in pairs(monster_info) do
				local object = ResMgr:Instantiate(prefab)
				local variable_table = U3DNodeList(object:GetComponent(typeof(UINameTable)))
				self:SetMapImg(object, v.x, v.y)
				local level
				local scene_type = Scene.Instance:GetSceneType()
				if scene_type == SceneType.CROSS_LIEKUN then
					local cross_lieKun_info = GuildWGData.Instance:GetCrossLieKunFBSceneInfo()
					level = v.level + cross_lieKun_info.boss_extra_level
				else
					level = v.level
				end
				variable_table["Text"].text.text = level .. Language.Common.Ji

				if BossWGData.IsBossScene(scene_type) then
					if scene_type == SceneType.KFSHENYUN_FB then
						local tuteng_type = BossWGData.Instance:GetTutengType(v.index)
						if tuteng_type == 1 then
							variable_table["Text"].text.text = v.name .. Language.Boss.GoldT
						else
							variable_table["Text"].text.text = v.name
						end
					else
						variable_table["Text"].text.text = v.name
					end
				end
				self.monster_icon_obj_list[v.id] = {obj = object,}
			end
		end
	end)
end

-- function MapLocalView:CreateGatherIcon( gather_info, count )
-- 	if gather_info == nil then
-- 		return
-- 	end

-- 	local gather_count = count or #gather_info
-- 	self:ClearGatherIcon()
-- 	local res_async_loader = AllocResAsyncLoader(self, "Icon_Gather_loader")
-- 	res_async_loader:Load("uis/view/map_ui_prefab", "Icon_Gather", nil, function (prefab)
-- 		if nil == prefab then
-- 			return
-- 		end

-- 		if gather_count > 0 then
-- 			for k,v in pairs(gather_info) do
-- 				local object = ResMgr:Instantiate(prefab)
-- 				local variable_table = U3DNodeList(object:GetComponent(typeof(UINameTable)))
-- 				self:SetMapImg(object, v.x, v.y)
-- 				variable_table["Text"].text.text = v.name

-- 				self.gather_icon_obj_list[k] = {obj = object,}
-- 			end
-- 		end
-- 	end)
-- end

function MapLocalView:CreateGatherIcon(gather_info)
	if gather_info == nil then
		return
	end

	self:ClearGatherIcon()
	local res_async_loader = AllocResAsyncLoader(self, "Icon_Gather_loader")
	res_async_loader:Load("uis/view/map_ui_prefab", "Icon_Gather", nil, function (prefab)
		if nil == prefab then
			return
		end

		for k,v in pairs(gather_info) do
			local object = ResMgr:Instantiate(prefab)
			local variable_table = U3DNodeList(object:GetComponent(typeof(UINameTable)), self)
			variable_table["text"].text.text = v.str or ""
			variable_table["icon"].image:LoadSprite(v.icon_bundle, v.icon_name)

			-- TODO 需要改为新版处理方式
			-- local out_line = variable_table["text"]:GetOrAddComponent(typeof(UnityEngine.UI.Outline))
			-- if v.str_outline_color then
			-- 	out_line.effectColor = Str2C3b(v.str_outline_color)
			-- 	out_line.enabled = true
			-- else
			-- 	out_line.enabled = false
			-- end

			if v.call_back then
				XUI.AddClickEventListener(variable_table["icon"], v.call_back)
			end
	
			self:SetMapImg(object, v.x, v.y)
			self.gather_icon_obj_list[k] = {obj = object}
		end
	end)
end

function MapLocalView:CreateDoorIcon( door_info, count )
	if door_info == nil then
		return
	end
	local scene_logic = Scene.Instance:GetSceneLogic()
	self:ClearDoorIcon()

	local door_count = count or #door_info
	local res_async_loader = AllocResAsyncLoader(self, "Icon_Door_loader")
	res_async_loader:Load("uis/view/map_ui_prefab", "Icon_Door", nil, function (prefab)
		if nil == prefab then
			return
		end

		if door_count > 0 then
			for k,v in ipairs(door_info) do
				local object = ResMgr:Instantiate(prefab)
				local name_table = U3DNodeList(object:GetComponent(typeof(UINameTable)))
				if Scene.Instance:GetSceneType() == SceneType.GongChengZhan then
					local map_name = ConfigManager.Instance:GetAutoConfig("gongchengzhan_auto").other[1].door_name
					name_table["Text"].text.text = map_name
				else
					name_table["Text"].text.text = v.name
				end
				self:SetMapImg(object, v.x, v.y)
				self.door_icon_obj_list[k] = {obj = object, }
				if Scene.Instance:GetSceneType() == SceneType.CROSS_LIEKUN and not scene_logic:GetShowDoorFlag() then
					object:SetActive(false)
				end
			end
		end
	end)
end

function MapLocalView:CreateRegionIcon(region_info, count)
	if region_info == nil then
		return
	end
	self:ClearRegionIcon()

	local region_count = count or #region_info
	local res_async_loader = AllocResAsyncLoader(self, "Icon_Region_loader")
	res_async_loader:Load("uis/view/map_ui_prefab", "Icon_Region", nil, function (prefab)
		if nil == prefab then
			return
		end

		if region_count > 0 then
			for k,v in ipairs(region_info) do
				local object = ResMgr:Instantiate(prefab)
				local name_table = U3DNodeList(object:GetComponent(typeof(UINameTable)))
				local level = v.level or 0
				name_table["Text"].text.text = v.name
				self:SetMapImg(object, v.x, v.y)
				self.region_icon_obj_list[k] = {obj = object, }
			end
		end
	end)
end

function MapLocalView:ShowDoors()
	for k,v in pairs(self.door_icon_obj_list) do
		if v.obj then
			v.obj:SetActive(true)
		end
	end
	local list = MapWGData.Instance:GetInfoByType(SceneObjType.Door)
	for k,v in pairs(list) do
		if v.obj then
			v.obj:SetActive(true)
		end
	end
end

function MapLocalView:GetMonsterIconObj(id)
	return self.monster_icon_obj_list[id]
end

function MapLocalView:CreateBossIcon(monster_info, count)
	if monster_info == nil then
		return
	end
	local monster_count = count or #monster_info
	self:ClearBossIcon()

	local res_async_loader = AllocResAsyncLoader(self, "Icon_Boss_loader")
	res_async_loader:Load("uis/view/map_ui_prefab", "Icon_Boss", nil, function (prefab)
		if nil == prefab then
			return
		end

		if monster_count > 0 then
			for k,v in pairs(monster_info) do
				local object = ResMgr:Instantiate(prefab)
				local variable_table = U3DNodeList(object:GetComponent(typeof(UINameTable)))
				local x = v.x_pos or v.x
				local y = v.y_pos or v.y
				self:SetMapImg(object, x, y)
				local level
				if v.level then
					level = v.level
				else
					level = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[v.boss_id].level
				end
				local scene_type = Scene.Instance:GetSceneType()
				if scene_type == SceneType.CROSS_LIEKUN then
					local cross_lieKun_info = GuildWGData.Instance:GetCrossLieKunFBSceneInfo()
					level = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[v.boss_id].level
					level = level + cross_lieKun_info.boss_extra_level
				end
				variable_table["Text"].text.text = v.name .. "Lv." .. level
				if BossWGData.IsBossScene(scene_type) then
					variable_table["Text"].text.text = v.name
				end
				self.boss_icon_obj_list[k] = {obj = object,}
			end
		end
	end)
end

-- 创建特殊icon
function MapLocalView:CreateSpecialIcon(sepcial_icon_info)
	if sepcial_icon_info == nil then
		return
	end
	self:ClearSpecialIcon()

	local count = #sepcial_icon_info
	local res_async_loader = AllocResAsyncLoader(self, "CreateSpecialIcon_Icon_Special")
	res_async_loader:Load("uis/view/map_ui_prefab", "Icon_Special", nil, function (prefab)
		if nil == prefab then
			return
		end

		if count > 0 then
			for k,v in ipairs(sepcial_icon_info) do
				local object = ResMgr:Instantiate(prefab)
				local name_table = U3DNodeList(object:GetComponent(typeof(UINameTable)), self)
				name_table["str"].text.text = v.str or ""
				name_table["icon"].image:LoadSprite(v.icon_bundle, v.icon_name)
				name_table["bottom_str"].text.text = v.bottom_str or ""

				local gradient = name_table["bottom_str"]:GetOrAddComponent(typeof(UIGradient))
				if v.bottom_str_up_color and v.bottom_str_down_color then
					XUI.ChangeGradientColor(name_table["bottom_str"], v.bottom_str_up_color, v.bottom_str_down_color)
					gradient.enabled = true
				else
					gradient.enabled = false
				end
				-- TODO 需要改为新版处理方式
				-- local out_line = name_table["bottom_str"]:GetOrAddComponent(typeof(UnityEngine.UI.Outline))
				-- if v.bottom_str_outline_color then
				-- 	out_line.effectColor = Str2C3b(v.bottom_str_outline_color)
				-- 	out_line.enabled = true
				-- else
				-- 	out_line.enabled = false
				-- end

				if v.call_back then
					XUI.AddClickEventListener(name_table["icon"], v.call_back)
				end
				self:SetMapImg(object, v.x, v.y)
				self.special_icon_list[k] = {obj = object, data = v}
			end
		end
	end)
end
--------------------------------------------------------------------------------------------------------

--------------------------------------------------------------------画路径线-----------------------------------------------------------------

local step = 21

function MapLocalView:ClearWalkPath()
	self.path_line:SetCount(0)
	self.path_line:SetActive(false)
	self.is_draw_path = false
	self:SetMapTargetImg(false)
	self.is_can_draw_path = false
	self.is_move_finished = true
	self:RemoveCountDown()
	self.delay_time = GlobalTimerQuest:AddDelayTimer(function() self.is_can_draw_path = true end, 0.3)
end

function MapLocalView:DrawWalkPath()
	self.path_line:SetActive(true)
	local path_pos_list = self:GetPathPosList()
	if #path_pos_list <= 0 then
		self:ClearWalkPath()
		return
	end

	self.target_position = {}
	self.target_position.x = path_pos_list[#path_pos_list].x
	self.target_position.y = path_pos_list[#path_pos_list].y

	--设置结束位置图标
	self:SetMapTargetImg(true, self.target_position.x, self.target_position.y)

	--画线
	local count = #path_pos_list + 1
	local list = {}
	self.length_map = {}
	local total_length = 0
	for i = count, 1, -1 do
		local spinodal_x, spinodal_y
		if i == 1 then
			spinodal_x, spinodal_y = self:LogicToUI(Scene.Instance:GetMainRole():GetLogicPos())
		else
			spinodal_x, spinodal_y = self:LogicToUI(path_pos_list[i - 1].x, path_pos_list[i - 1].y)
		end
		list[i] = Vector3(spinodal_x, spinodal_y, 0)
		if i < count then
			local length = u3d.v3Length(u3d.v3Sub(list[i + 1], list[i]), true)
			total_length = total_length + length
			self.length_map[i + 1] = total_length
		end
	end

	local point_count = math.ceil(total_length / step)
	self.path_line:SetCount(point_count)

	total_length = 0
	local cur_index = count
	for i = point_count, 1, -1 do
		if self.length_map[cur_index] < total_length then
			for j = cur_index - 1, 2, -1 do
				if self.length_map[j] >= total_length then
					cur_index = j
					break
				end
			end
		end
		local position = list[cur_index]
		local dir = u3d.v3Normalize(u3d.v3Sub(list[cur_index - 1], position))
		local length = total_length - (self.length_map[cur_index + 1] and self.length_map[cur_index + 1] or 0)
		position = u3d.v3Add(position, u3d.v3Mul(dir, length))
		self.path_line:SetPosition(point_count - i + 1, position)
		total_length = total_length + step
	end

	self.is_draw_path = true
end

function MapLocalView:UpdateWalkPath()
	if not self.is_draw_path then
		return
	end
	if not self.is_can_draw_path then
		return
	end

	local path_index = self:GetPathPosIndex() + 1
	local rest_length = self.length_map[path_index] or 0
	local rest_count = math.ceil(rest_length / step)
	self.path_line:SetCount(rest_count)
	
end

function MapLocalView:SetOrder(sort_order)
	self.sort_order = sort_order
end

function MapLocalView:GetPathPosList()
	local main_role = Scene.Instance:GetMainRole()
	local path_pos_list = main_role:GetPathPosList()
	return path_pos_list
end

function MapLocalView:GetPathPosIndex()
	local main_role = Scene.Instance:GetMainRole()
	local path_index = main_role:GetPathPosIndex()
	return path_index
end

----------------------------------------------------------MapPathLine----------------------------------------------------

MapPathLine = MapPathLine or BaseClass()

function MapPathLine:__init(parent_view, parent)
	self.parent = parent
	self.active_path_point_list = {}
	self.recycled_path_point_list = {}
end

function MapPathLine:__delete()
	self.parent = nil
	for k,v in pairs(self.active_path_point_list) do
		ResPoolMgr:Release(v)
	end
	for k,v in pairs(self.recycled_path_point_list) do
		ResPoolMgr:Release(v)
	end
	self.active_path_point_list = {}
	self.recycled_path_point_list = {}
end

function MapPathLine:SetCount(count)
	self.count = count
	local active_count = #self.active_path_point_list
	if count > active_count then
		for i = 1, count - active_count do
			local path_line = self:GetPathLine()
			table.insert(self.active_path_point_list, path_line)
		end
	else
		for i = count + 1, active_count do
			self:RecyclePathPoint(self.active_path_point_list[#self.active_path_point_list])
			table.remove(self.active_path_point_list, #self.active_path_point_list)
		end
	end
end

function MapPathLine:SetActive(active)
	self.parent:SetActive(active)
end

function MapPathLine:SetPosition(index, position)
	local path_line = self.active_path_point_list[index]
	if path_line then
		path_line.transform:SetLocalPosition(position.x, position.y, position.z)
	end
end

function MapPathLine:GetPathLine()
	local count = #self.recycled_path_point_list
	local obj
	if count > 0 then
		obj = self.recycled_path_point_list[count]
		obj:SetActive(true)
		table.remove(self.recycled_path_point_list, count)
	else
		obj = ResPoolMgr:TryGetGameObject("uis/view/miscpre_load_prefab", "MapPoint")
		obj.transform:SetParent(self.parent.transform, false)
	end

	return obj
end

function MapPathLine:RecyclePathPoint(obj)
	table.insert(self.recycled_path_point_list, obj)
	obj:SetActive(false)
end

--换线按钮-------------------------------------------------------------
MainUILineButton = MainUILineButton or BaseClass(BaseRender)

function MainUILineButton:OnFlush()
	if self.data == nil then
		return
	end
	self.node_list["Name"].text.text = string.format(Language.Common.Line, CommonDataManager.GetDaXie(self.data.index))
	self.node_list["Select"]:SetActive(MapWGData.Instance:GetMapCurLine() == self.data.index)
end
