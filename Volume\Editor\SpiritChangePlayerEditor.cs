
using UnityEditor;
using UnityEngine;

#if UNITY_EDITOR
[CustomEditor(typeof(SpiritChangePlayer))]
public class SpiritChangePlayerEditor : Editor
{
    GameObject player;
    Transform effect_trans;

    public override void OnInspectorGUI()
    {
        //base.OnInspectorGUI();
        var comp = target as SpiritChangePlayer;

        if (!Application.isPlaying)
        {
            return;
        }
        player = (GameObject)EditorGUILayout.ObjectField("目标物体", player, typeof(GameObject),true, GUILayout.Width(400f));
        effect_trans = (Transform)EditorGUILayout.ObjectField("目标特效", effect_trans, typeof(Transform), true, GUILayout.Width(400f));

        if (comp.m_Zone != null)
        {
            if (!comp.m_PlayablePlaying)
            {
                if (GUILayout.Button("播放【领域展开】"))
                {
                    comp.SetScreenEffectZoneEffect(effect_trans);
                    comp.PlayScreenEffectZone(null, 0.5f, 0.5f);
                }
            }
            else
            {
                if (comp.m_PlayablePlaying)
                {
                    if (GUILayout.Button("关闭【领域展开】"))
                    {
                        comp.ContinueScreenEffectZone();
                    }
                }
            }
        }

        if (comp.m_WangQi != null)
        {
            if (!comp.m_PlayWangQiPlaying)
            {
                if (GUILayout.Button("打开【望气】"))
                {
                    if (player != null)
                        comp.ShowWangQi(player.transform, 1);
                }
            }
            else
            {
                if (GUILayout.Button("关闭【望气】"))
                {
                    comp.CloseWangQi(1);
                }
            }
        }
    }
}

#endif