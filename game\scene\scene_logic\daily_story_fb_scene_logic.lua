DailyStoryFbSceneLogic = DailyStoryFbSceneLogic or BaseClass(CommonFbLogic)

function DailyStoryFbSceneLogic:__init()

end

function DailyStoryFbSceneLogic:__delete()
	
end

function DailyStoryFbSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	
	DailyWGCtrl.Instance:Close()

	FuBenWGCtrl.Instance:OpenTaskFollow()
	-- XuiBaseView.CloseAllView()
end

function DailyStoryFbSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
end

function DailyStoryFbSceneLogic:Out()
	CommonFbLogic.Out(self)
	FuBenWGCtrl.Instance:CloseTaskFollow()
end