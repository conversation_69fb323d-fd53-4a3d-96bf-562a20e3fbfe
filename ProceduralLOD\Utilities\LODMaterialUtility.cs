#if UNITY_EDITOR

using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;
using Object = UnityEngine.Object;

namespace ProceduralLOD
{
    public static class LODMaterialUtility
    {
        public static readonly int BillboardFrameCount = 9;
        public static readonly int BillboardAtlasResolution = 512;

        public static Dictionary<string, Material> guid2Material;

        public static bool MakeMaterialLOD(Material material, out Material lodMaterial)
        {
            lodMaterial = null;

            string path = AssetDatabase.GetAssetPath(material);
            string guid = AssetDatabase.AssetPathToGUID(path);
            if (guid2Material.TryGetValue(guid, out Material cache))
            {
                lodMaterial = cache;
                return true;
            }

            try
            {
                if (!MaterialFactory.InstantiateMaterial(material, out lodMaterial))
                {
                    return false;
                }
                guid2Material.Add(guid, lodMaterial);
                
                return true;
            }
            catch (Exception e)
            {
                Debug.LogError(e);
                return false;
            }
        }

        #region Billboard

        private const float CulloffBias = 0.1f;
        
        //todo:封装独立Simplifier
        public static Material MakeMaterialBillboard(GameObject target, Shader billboarShader)
        {
            string path = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(target);
            string guid = AssetDatabase.AssetPathToGUID(path);

            if (guid2Material.TryGetValue(guid, out Material cache))
            {
                return cache;
            }

            TakeBillboardTextures(target, out var albedo, out var normal);
            Material lodMaterial = new Material(billboarShader);
            string file = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(target);
            lodMaterial.name = Path.GetFileNameWithoutExtension(file);
            lodMaterial.SetTexture("_BaseMap", albedo);
            lodMaterial.SetTexture("_NormalMap", normal);
            lodMaterial.SetFloat("_FrameCount", BillboardFrameCount);

            guid2Material.Add(guid, lodMaterial);
            return lodMaterial;
        }

        static void TakeBillboardTextures(GameObject prefabObject, out Texture2D albedoAtlasTexture, out Texture2D normalAtlasTexture)
        {
            GameObject sample = null;
            GameObject billboardCameraPivot = null;
            albedoAtlasTexture = null;
            normalAtlasTexture = null;
#if UNITY_EDITOR
            string albedoAtlasPath = null;
            string normalAtlasPath = null;
#endif

            int cachedMasterTextureLimit = QualitySettings.masterTextureLimit;
            QualitySettings.masterTextureLimit = 0;

            string prefabPath = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(prefabObject);
            string prefabName = Path.GetFileNameWithoutExtension(prefabPath);
            try
            {
                //Debug.Log("Generating Billboard for " + prototype.name);

                Renderer[] renderers = prefabObject.GetComponent<LODGroup>().GetLODs()[0].renderers;

                int frameCount = BillboardFrameCount;
                int atlasResolution = BillboardAtlasResolution;
                int framePerline = (int)Mathf.Sqrt(frameCount);

                // calculate frame resolution
                int frameResolution = atlasResolution / framePerline;

                // initialize the atlas textures
                albedoAtlasTexture = new Texture2D(atlasResolution, atlasResolution);
                albedoAtlasTexture.name = prefabName + "_BillboardAlbedo";
                albedoAtlasTexture.SetPixels(new Color[atlasResolution * atlasResolution]);
                normalAtlasTexture = new Texture2D(atlasResolution, atlasResolution);
                normalAtlasTexture.name = prefabName + "_BillboardNormal";
                normalAtlasTexture.SetPixels(new Color[atlasResolution * atlasResolution]);

                // create render target for atlas frames (both albedo and normal will share the same target)
                RenderTexture frameTarget = RenderTexture.GetTemporary(frameResolution, frameResolution, 32, RenderTextureFormat.ARGB32, RenderTextureReadWrite.Linear);
                frameTarget.enableRandomWrite = true;
                frameTarget.filterMode = FilterMode.Point;
                frameTarget.Create();

                // instantiate an instance of the prefab to sample
                List<Renderer> bakingRenderers = new List<Renderer>();
                List<Material> bakingMaterials = new List<Material>();

                sample = CommonUtility.CreateSampleObject(prefabObject, renderers);
                foreach (var r in sample.GetComponentsInChildren<Renderer>())
                {
                    bakingRenderers.Add(r);
                    var sharedMaterials = r.sharedMaterials;
                    for (int k = 0; k < sharedMaterials.Length; k++)
                    {
                        Material billboardBakeMaterial = new Material(sharedMaterials[k]);
                        sharedMaterials[k] = billboardBakeMaterial;
                        bakingMaterials.Add(billboardBakeMaterial);
                        float culloff = GetCutoff(billboardBakeMaterial);
                        culloff = Mathf.Max(0, culloff - CulloffBias);
                        billboardBakeMaterial.SetFloat("_Cutoff", culloff);
                    }

                    r.sharedMaterials = sharedMaterials;
                }


                // set all children of the sample to the sample layer and calculate their overall bounds
                int sampleLayer = 31;
                MeshRenderer[] sampleChildrenMRs = sample.GetComponentsInChildren<MeshRenderer>();
                for (int i = 0; i < sampleChildrenMRs.Length; i++)
                {
                    sampleChildrenMRs[i].gameObject.layer = sampleLayer;
                }

                Bounds sampleBounds = BillboardCaptureBounds(sampleChildrenMRs, out float sampleBoundsMaxSize);

                Shader billboardAlbedoBakeShader = Shader.Find("Hidden/Billboard/AlbedoBake");
                Shader billboardNormalBakeShader = Shader.Find("Hidden/Billboard/NormalBake");

#if UNITY_EDITOR
                Shader.SetGlobalFloat("_IsLinearSpace", PlayerSettings.colorSpace == ColorSpace.Linear ? 1.0f : 0.0f);
#endif

                // create the billboard snapshot camera
                billboardCameraPivot = new GameObject("BillboardCameraPivot");
                Camera billboardCamera = new GameObject().AddComponent<Camera>();
                billboardCamera.transform.SetParent(billboardCameraPivot.transform);

                billboardCamera.gameObject.hideFlags = HideFlags.DontSave;
                billboardCamera.cullingMask = 1 << sampleLayer;
                billboardCamera.clearFlags = CameraClearFlags.SolidColor;
                billboardCamera.backgroundColor = Color.clear;
                billboardCamera.orthographic = true;
                billboardCamera.nearClipPlane = 0f;
                billboardCamera.farClipPlane = sampleBoundsMaxSize;
                billboardCamera.orthographicSize = sampleBoundsMaxSize * 0.5f;
                billboardCamera.allowMSAA = false;
                billboardCamera.enabled = false;
                billboardCamera.renderingPath = RenderingPath.Forward;
                billboardCamera.targetTexture = frameTarget;
                billboardCamera.transform.localPosition = new Vector3(0, sampleBounds.center.y, -sampleBoundsMaxSize / 2);

                float rotateAngle = 360f / frameCount;

                RenderPipelineAsset renderPipelineAsset = GraphicsSettings.renderPipelineAsset;
                RenderPipelineAsset qualityPipelineAsset = QualitySettings.renderPipeline;

                {
                    GraphicsSettings.renderPipelineAsset = null;
                    QualitySettings.renderPipeline = null;
                }
                
                void SetShader(Shader shader)
                {
                    for (int i = 0; i < bakingMaterials.Count; i++)
                    {
                        Material material = bakingMaterials[i];
                        material.shader = shader;
                    }
                }

                // render the frames into the atlas textures
                for (int f = 0; f < frameCount; f++)
                {
                    sample.transform.rotation = Quaternion.AngleAxis(rotateAngle * -f, Vector3.up);

                    Rect source = new Rect(0, 0, frameResolution, frameResolution);
                    int destX = Mathf.CeilToInt(f % framePerline * frameResolution);
                    int destY = Mathf.CeilToInt(f / framePerline * frameResolution);

                    SetShader(billboardAlbedoBakeShader);
                    billboardCamera.Render();
                    RenderTexture.active = frameTarget;
                    albedoAtlasTexture.ReadPixels(source, destX, destY);
                    RenderTexture.active = null;

                    SetShader(billboardNormalBakeShader);
                    billboardCamera.Render();
                    RenderTexture.active = frameTarget;
                    normalAtlasTexture.ReadPixels(source, destX, destY);
                    RenderTexture.active = null;
                }

                {
                    GraphicsSettings.renderPipelineAsset = renderPipelineAsset;
                    QualitySettings.renderPipeline = qualityPipelineAsset;
                }

                // set the result billboard to the prototype
                albedoAtlasTexture.Apply();
                normalAtlasTexture.Apply();
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }

            QualitySettings.masterTextureLimit = cachedMasterTextureLimit;

            GameObject.DestroyImmediate(sample);
            GameObject.DestroyImmediate(billboardCameraPivot); // this will also release the frameTarget RT
        }

        public static Bounds BillboardCaptureBounds(Renderer[] renderers, out float sampleBoundsMaxSize)
        {
            Bounds sampleBounds = renderers[0].bounds;

            for (int i = 1; i < renderers.Length; i++)
            {
                sampleBounds.Encapsulate(renderers[i].bounds);


                // MeshFilter sampleChildMF = renderers[i].GetComponent<MeshFilter>();
                // if (sampleChildMF == null || sampleChildMF.sharedMesh == null || sampleChildMF.sharedMesh.vertices == null)
                //     continue;
                //
                // // encapsulate vertices instead of mesh renderer bounds; the latter are sometimes larger than necessary
                // Vector3[] verts = sampleChildMF.sharedMesh.vertices;
                // for (var v = 0; v < verts.Length; v++)
                // {
                //     if (v == 0)
                //     {
                //         sampleBounds = new Bounds(renderers[i].transform.localToWorldMatrix.MultiplyPoint3x4(verts[v]), Vector3.zero);
                //         continue;
                //     }
                //     
                //     sampleBounds.Encapsulate(renderers[i].transform.localToWorldMatrix.MultiplyPoint3x4(verts[v]));
                // }
            }

            // calculate quad size
            sampleBoundsMaxSize = Mathf.Max(sampleBounds.size.x, sampleBounds.size.z);
            sampleBoundsMaxSize = Mathf.Max(sampleBoundsMaxSize, sampleBounds.size.y); // if height is longer, adjust.
            //sampleBoundsMaxSize = Mat sampleBounds.size.magnitude;

            return sampleBounds;
        }
        
        static float GetCutoff(Material material)
        {
            if (material.shader.name == "JYShaders/StylizedPlant" || 
                material.IsKeywordEnabled("_ALPHATEST_ON") ||
                material.HasFloat("_Cutoff"))
            {
                return material.GetFloat("_Cutoff");
            }

            return 0;
        }

        #endregion

        public static void ClearCache()
        {
            guid2Material = new Dictionary<string, Material>();
        }
        
        public static string GetSourceID(Material material)
        {
            foreach (var pair in guid2Material)
            {
                if (pair.Value == material)
                {
                    return pair.Key;
                }
            }

            return null;
        }
        
        public static void SetCache(string key, Object material)
        {
            if (!string.IsNullOrEmpty(key))
            {
                guid2Material[key] = material as Material;
            }
        }
    }
}

#endif