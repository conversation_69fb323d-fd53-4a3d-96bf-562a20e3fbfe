ViewRuleWGData = ViewRuleWGData or BaseClass()

function ViewRuleWGData:__init()
	if ViewRuleWGData.Instance then
		error("[ViewRuleWGData] Attempt to create singleton twice!")
		return
	end
	ViewRuleWGData.Instance = self

	self.view_rule_auto = ConfigManager.Instance:GetAutoConfig("view_rule_cfg_auto")
	self.view_rule_list_cfg = ListToMap(self.view_rule_auto.rule_list, "guide_module_name", "sub_index")
end

function ViewRuleWGData:__delete()
	ViewRuleWGData.Instance = nil
end

-- 获取对应的配置
function ViewRuleWGData:GetViewRuleCfg(view_name, sub_index)
	return ((self.view_rule_list_cfg or {})[view_name] or {})[sub_index]
end