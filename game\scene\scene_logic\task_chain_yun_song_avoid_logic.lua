TaskChainYunSongAvoidLogic = TaskChainYunSongAvoidLogic or BaseClass(CommonFbLogic)
function TaskChainYunSongAvoidLogic:__init()
	self.obj_die_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_DEAD, BindTool.Bind(self.MainRoleDie,self))
	-- self.yun_song_role_data_change = BindTool.Bind1(self.YunSongAvoidCallback, self)
	self.is_need_guaji = true
end

function TaskChainYunSongAvoidLogic:__delete()
	if self.obj_die_event then
		GlobalEventSystem:UnBind(self.obj_die_event)
		self.obj_die_event = nil
	end

	-- self.yun_song_role_data_change = nil
end

function TaskChainYunSongAvoidLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	ViewManager.Instance:CloseAll()

	self.is_need_guaji = true
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetSkillShowState(false)
		MainuiWGCtrl.Instance:SetButtonModeClick(false)
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(true, Scene.Instance:GetSceneName())
		MainuiWGCtrl.Instance:SetTeamBtnState(false)

		ViewManager.Instance:Open(GuideModuleName.OperationTaskChainFbInfoView)
	end)

	ViewManager.Instance:Open(GuideModuleName.OperationTaskChainScheduleView)
	ViewManager.Instance:Open(GuideModuleName.OperationTaskChainSkillView)

	-- RoleWGData.Instance:NotifyAttrChange(self.yun_song_role_data_change, {"hp"})
end

function TaskChainYunSongAvoidLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)
	
	self.is_need_guaji = false
	ViewManager.Instance:CloseAll()
	MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
    MainuiWGCtrl.Instance:SetFBNameState(false)
    MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetSkillShowState(true)
	MainuiWGCtrl.Instance:SetButtonModeClick(true)
	UiInstanceMgr.Instance:ColseFBStartDown()
	OperationTaskChainWGCtrl.Instance:ResetInfoView()
	OperationTaskChainWGData.Instance:ResetYunSongAvoidInfo()
	
	FuBenPanelCountDown.Instance:CloseViewHandler()

	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil then
		main_role:SetUnderShowInfo(nil)
	end

	-- RoleWGData.Instance:UnNotifyAttrChange(self.yun_song_role_data_change)
end

function TaskChainYunSongAvoidLogic:IsEnemy(target_obj, main_role, ignore_table, test)
	return false
end

function TaskChainYunSongAvoidLogic:IsRoleEnemy(target_obj, main_role)
	return false
end

function TaskChainYunSongAvoidLogic:GetGuajiCharacter()
	local is_need_stop = true

	local info = OperationTaskChainWGData.Instance:GetYunSongAvoidInfo()
	local main_role = Scene.Instance:GetMainRole()
	if info ~= nil and info.task_status ~= OPERATION_TASK_CHAIN_ACT_STATUS.STANDY and main_role ~= nil and not main_role:GetIsGatherState() and main_role:IsStand() and not self:GetIsSendGather() then
		local wait_time = OperationTaskChainWGData.Instance:GetGatherWaitTime()
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		if (wait_time ~= nil and wait_time <= server_time) or wait_time == nil then
			local gather_id = OperationTaskChainWGData.Instance:GetYunSongAvoidGatherCfg()
			if gather_id ~= nil then
				local obj = Scene.Instance:GetGatherByGatherId(gather_id)
				if obj ~= nil and not obj:IsDeleted() then
					MoveCache.SetEndType(MoveEndType.Gather)
					MoveCache.target_obj = obj
					GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), obj.vo.pos_x, obj.vo.pos_y, COMMON_CONSTS.GATHER_TRIIGER_RANGE)
				end
			end
		end
	end
	return nil, nil, is_need_stop
end

-- 怪物是否是敌人
function TaskChainYunSongAvoidLogic:IsMonsterEnemy(target_obj, main_role)
	return false
end

function TaskChainYunSongAvoidLogic:GetGuajiPos()
end

function TaskChainYunSongAvoidLogic:CanGetMoveObj()
	return false
end

-- function TaskChainYunSongAvoidLogic:ObjCreate(obj)
-- 	local npc_id = OperationTaskChainWGData.Instance:GetCaiJiOperaInfo()
-- 	if obj ~= nil and npc_id ~= nil and not obj:IsDeleted() and obj:IsNpc() and obj:GetNpcId() == npc_id then
-- 		obj:CheckShowUnderBar()
-- 	end
-- end

function TaskChainYunSongAvoidLogic:GetMonsterName(monster)
	local color_name = ""
	if monster.vo then
		color_name = ToColorStr(monster.vo.name or "", COLOR3B.RED)
	end
	return color_name
end

function TaskChainYunSongAvoidLogic:MainRoleDie()
	ViewManager.Instance:Open(GuideModuleName.OperationTaskChainFuHuoView)
end

-- function TaskChainYunSongAvoidLogic:YunSongAvoidCallback(atr_name, new_value, old_value)
-- end

function TaskChainYunSongAvoidLogic:GetIsNeedGuaJi()
	return self.is_need_guaji
end

function TaskChainYunSongAvoidLogic:SetIsNeedGuaJi(value)
	self.is_need_guaji = value
end