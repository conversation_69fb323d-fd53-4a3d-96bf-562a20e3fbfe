-- Y-运势.xls

return {
other={
{}
},

other_meta_table_map={
},
fortune_type={
{des="帮助仙盟成员刷新时，最高刷出<color=#b759fa>紫色宝箱</color>",},
{fortune_type=2,name="锦上添花",common_color=3,des="帮助仙盟成员刷新时，最高刷出<color=#ff6000>橙色宝箱</color>",},
{fortune_type=3,name="吉星高照",common_color=4,},
{fortune_type=4,name="鸿运当头",is_best=1,common_color=5,}
},

fortune_type_meta_table_map={
},
fortune_addition={
{},
{addition_id=2,addition_type=2,addition_value=1000000,title="极品装备",des="BOSS极品装备掉落概率<color=#2b9134>+10%</color>",},
{addition_id=3,addition_type=3,title="极品外显",des="BOSS外显掉落概率<color=#2b9134>+10%</color>",},
{addition_id=4,addition_type=4,title="主城护送",des="护送刷新到<color=#ec0228>扶苏花神</color>的概率<color=#2b9134>+10%</color>",},
{addition_id=5,addition_type=8,addition_value=1000,title="经验加成",des="全天拥有<color=#2b9134>10%</color>的经验加成",},
{addition_id=6,addition_type=5,addition_value=20,title="许愿双倍",des="祈福双倍概率<color=#2b9134>+20%</color>",},
{addition_id=7,addition_type=6,title="玉魄附魂",des="玉魄附魂概率<color=#2b9134>+10%</color>",},
{addition_id=8,addition_type=7,title="装备附魔",des="装备附魔成功概率<color=#2b9134>+10%</color>",},
{addition_id=9,addition_type=9,title="装备寻宝",des="天地至宝极品获取概率<color=#2b9134>+10%</color>",},
{addition_id=10,addition_type=10,title="星光魂契",des="星光魂契极品获取概率<color=#2b9134>+10%</color>",},
{addition_id=11,addition_value=500000,des="BOSS极品装备掉落概率<color=#2b9134>+5%</color>",type=0,},
{addition_id=12,addition_type=3,title="极品外显",des="BOSS外显掉落概率<color=#2b9134>+5%</color>",},
{addition_id=13,addition_value=500,des="全天拥有<color=#2b9134>5%</color>的经验加成",type=0,},
{addition_id=14,addition_type=5,title="许愿双倍",des="祈福双倍概率<color=#2b9134>+10%</color>",type=0,},
{addition_id=15,addition_value=5,des="装备合成成功概率<color=#2b9134>+5%</color>",type=0,},
{addition_id=16,addition_type=7,title="装备附魔",des="装备附魔成功概率<color=#2b9134>+5%</color>",}
},

fortune_addition_meta_table_map={
[3]=2,	-- depth:1
[7]=5,	-- depth:1
[8]=5,	-- depth:1
[11]=2,	-- depth:1
[12]=11,	-- depth:2
[13]=5,	-- depth:1
[16]=13,	-- depth:2
},
fortune={
{addition="4,10|5,10|13,20",},
{fortune_type=2,},
{fortune_type=3,addition="4,10|5,10|13,20|2,10|11,20|3,10|12,20|9,10|10,10",addition_num=2,},
{fortune_type=4,addition="4,10|5,10|13,20|2,10|11,20|3,10|12,20|6,10|14,20|9,10|10,10",addition_num=4,},
{min_level=300,max_level=349,addition="4,10|5,10|13,20|2,10|11,20",},
{min_level=300,max_level=349,},
{fortune_type=3,addition="5,10|13,20|2,10|11,20|3,10|12,20|9,10|1,10|15,20|10,10",addition_num=2,},
{fortune_type=4,addition="5,10|13,20|2,10|11,20|3,10|12,20|9,10|1,10|15,20|10,10|6,10|14,20",addition_num=4,},
{min_level=350,max_level=399,addition="4,10|5,10|13,20|9,10|1,10|15,20",},
{fortune_type=2,addition="4,10|5,10|13,20|2,10|11,20|9,10|1,10|15,20",},
{fortune_type=3,addition="5,10|13,20|2,10|11,20|9,10|3,20|12,20|1,10|15,20|10,10|6,10|14,20",addition_num=2,},
{fortune_type=4,addition="5,10|13,20|2,10|11,20|9,10|3,20|12,20|1,10|15,20|10,10|6,10|14,20|7,10",addition_num=4,},
{min_level=400,max_level=2000,},
{min_level=400,max_level=2000,},
{min_level=400,max_level=2000,addition="5,10|13,20|2,10|11,20|9,10|3,20|12,20|1,10|15,20|8,10|16,20|10,10|6,10|14,20",},
{min_level=400,max_level=2000,addition="5,10|13,20|2,10|11,20|9,10|3,20|12,20|1,10|15,20|8,10|16,20|10,10|6,10|14,20|7,10",}
},

fortune_meta_table_map={
[6]=2,	-- depth:1
[13]=9,	-- depth:1
[10]=9,	-- depth:1
[14]=10,	-- depth:2
[7]=5,	-- depth:1
[8]=5,	-- depth:1
[11]=9,	-- depth:1
[12]=9,	-- depth:1
[15]=3,	-- depth:1
[16]=4,	-- depth:1
},
fortune_refresh={
{cost_gold=0,cost_item_num=0,},
{times=2,},
{times=3,},
{times=4,},
{times=5,},
{times=6,},
{times=7,},
{times=8,},
{times=9,},
{times=10,},
{times=11,},
{times=12,},
{times=13,},
{times=14,},
{times=15,},
{times=16,},
{times=17,},
{times=18,},
{times=19,},
{times=20,},
{times=21,},
{times=22,},
{times=23,},
{times=24,},
{times=25,},
{times=26,},
{times=27,},
{times=28,},
{times=29,},
{times=30,},
{times=31,},
{times=32,},
{times=33,},
{times=34,},
{times=35,},
{times=36,},
{times=37,},
{times=38,},
{times=39,},
{times=40,}
},

fortune_refresh_meta_table_map={
},
refresh_weight={
{},
{fortune_type=2,fortune_type_weight=35,},
{fortune_type=3,},
{fortune_type=4,fortune_type_weight=15,},
{refresh_type=2,fortune_type_weight=15,},
{fortune_type=2,fortune_type_weight=20,},
{fortune_type=3,fortune_type_weight=40,},
{refresh_type=2,fortune_type=4,}
},

refresh_weight_meta_table_map={
[6]=8,	-- depth:1
[7]=8,	-- depth:1
},
other_default_table={open_level=150,},

fortune_type_default_table={fortune_type=1,name="祥瑞之兆",is_best=0,common_color=2,title="每日宝箱",des="帮助仙盟成员刷新时，最高刷出<color=#ec0228>红色宝箱</color>",},

fortune_addition_default_table={addition_id=1,addition_type=1,addition_value=10,title="装备合成",des="装备合成成功概率<color=#2b9134>+10%</color>",type=1,},

fortune_default_table={min_level=1,max_level=299,fortune_type=1,addition="4,10|5,10|13,20|2,10|11,20|9,10",addition_num=1,},

fortune_refresh_default_table={times=1,cost_gold=50,cost_item_id=46561,cost_item_num=1,},

refresh_weight_default_table={refresh_type=1,fortune_type=1,fortune_type_weight=25,}

}

