-- K-跨服伏龙翔天.xls
local item_table={
[1]={item_id=23350,num=1,is_bind=1},
[2]={item_id=38120,num=1,is_bind=1},
[3]={item_id=48441,num=1,is_bind=1},
[4]={item_id=48118,num=1,is_bind=1},
[5]={item_id=26200,num=2,is_bind=1},
[6]={item_id=26203,num=2,is_bind=1},
[7]={item_id=56316,num=1,is_bind=1},
[8]={item_id=26415,num=2,is_bind=1},
[9]={item_id=28446,num=1,is_bind=1},
[10]={item_id=28447,num=1,is_bind=1},
[11]={item_id=28448,num=1,is_bind=1},
[12]={item_id=26355,num=1,is_bind=1},
[13]={item_id=26356,num=1,is_bind=1},
[14]={item_id=26121,num=1,is_bind=1},
[15]={item_id=28812,num=1,is_bind=1},
[16]={item_id=28826,num=1,is_bind=1},
[17]={item_id=45017,num=1,is_bind=1},
[18]={item_id=48071,num=1,is_bind=1},
[19]={item_id=38123,num=1,is_bind=1},
[20]={item_id=44180,num=1,is_bind=1},
[21]={item_id=48187,num=1,is_bind=1},
[22]={item_id=48186,num=1,is_bind=1},
[23]={item_id=26191,num=1,is_bind=1},
[24]={item_id=26148,num=2,is_bind=1},
[25]={item_id=26149,num=2,is_bind=1},
[26]={item_id=26347,num=1,is_bind=1},
[27]={item_id=26350,num=1,is_bind=1},
[28]={item_id=26371,num=1,is_bind=1},
[29]={item_id=26377,num=1,is_bind=1},
[30]={item_id=26345,num=1,is_bind=1},
[31]={item_id=38125,num=1,is_bind=1},
[32]={item_id=39105,num=1,is_bind=1},
[33]={item_id=29624,num=1,is_bind=1},
[34]={item_id=29625,num=1,is_bind=1},
[35]={item_id=30423,num=1,is_bind=1},
[36]={item_id=30424,num=1,is_bind=1},
[37]={item_id=30425,num=1,is_bind=1},
[38]={item_id=29615,num=1,is_bind=1},
[39]={item_id=48096,num=1,is_bind=1},
[40]={item_id=48099,num=1,is_bind=1},
[41]={item_id=48101,num=1,is_bind=1},
[42]={item_id=48102,num=1,is_bind=1},
[43]={item_id=29614,num=1,is_bind=1},
[44]={item_id=30003,num=1,is_bind=1},
[45]={item_id=30013,num=1,is_bind=1},
[46]={item_id=38126,num=1,is_bind=1},
[47]={item_id=26944,num=1,is_bind=1},
[48]={item_id=48120,num=1,is_bind=1},
[49]={item_id=48117,num=1,is_bind=1},
[50]={item_id=48073,num=1,is_bind=1},
[51]={item_id=44073,num=1,is_bind=1},
[52]={item_id=22070,num=1,is_bind=1},
[53]={item_id=22071,num=1,is_bind=1},
[54]={item_id=22072,num=1,is_bind=1},
[55]={item_id=26346,num=1,is_bind=1},
[56]={item_id=26038,num=1,is_bind=1},
[57]={item_id=26037,num=1,is_bind=1},
[58]={item_id=44174,num=1,is_bind=1},
[59]={item_id=50085,num=1,is_bind=1},
[60]={item_id=39151,num=1,is_bind=1},
[61]={item_id=50011,num=1,is_bind=1},
[62]={item_id=48133,num=1,is_bind=1},
[63]={item_id=44173,num=1,is_bind=1},
[64]={item_id=38118,num=1,is_bind=1},
[65]={item_id=48093,num=1,is_bind=1},
[66]={item_id=29621,num=1,is_bind=1},
[67]={item_id=32283,num=1,is_bind=1},
[68]={item_id=23352,num=1,is_bind=1},
[69]={item_id=48098,num=1,is_bind=1},
[70]={item_id=29627,num=1,is_bind=1},
[71]={item_id=31064,num=1,is_bind=1},
[72]={item_id=31057,num=1,is_bind=1},
[73]={item_id=27658,num=1,is_bind=1},
[74]={item_id=27659,num=1,is_bind=1},
[75]={item_id=22026,num=1,is_bind=1},
[76]={item_id=26569,num=1,is_bind=1},
[77]={item_id=26570,num=1,is_bind=1},
[78]={item_id=22027,num=1,is_bind=1},
[79]={item_id=44183,num=1,is_bind=1},
[80]={item_id=26502,num=1,is_bind=1},
[81]={item_id=26517,num=1,is_bind=1},
[82]={item_id=22618,num=2,is_bind=1},
[83]={item_id=22028,num=1,is_bind=1},
[84]={item_id=26450,num=1,is_bind=1},
[85]={item_id=26503,num=1,is_bind=1},
[86]={item_id=26518,num=1,is_bind=1},
[87]={item_id=22618,num=3,is_bind=1},
[88]={item_id=22029,num=1,is_bind=1},
[89]={item_id=26455,num=1,is_bind=1},
[90]={item_id=26504,num=1,is_bind=1},
[91]={item_id=26519,num=1,is_bind=1},
[92]={item_id=22618,num=4,is_bind=1},
[93]={item_id=22030,num=1,is_bind=1},
[94]={item_id=26459,num=1,is_bind=1},
[95]={item_id=26193,num=1,is_bind=1},
[96]={item_id=26505,num=1,is_bind=1},
[97]={item_id=26520,num=1,is_bind=1},
[98]={item_id=28706,num=1,is_bind=1},
[99]={item_id=38421,num=1,is_bind=1},
[100]={item_id=44182,num=1,is_bind=1},
[101]={item_id=26501,num=1,is_bind=1},
[102]={item_id=26516,num=1,is_bind=1},
[103]={item_id=22618,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
open_day={
{},
{start_day=8,end_day=14,grade=2,},
{start_day=15,end_day=28,grade=3,},
{start_day=29,end_day=35,grade=4,rate_count=3,},
{start_day=36,end_day=999,grade=5,rate_count=3,}
},

open_day_meta_table_map={
},
grade={
{},
{mode=2,times=10,need_cost=400,add_lucky=10,},
{mode=3,times=50,need_cost=2000,add_lucky=50,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=4,},
{seq=4,},
{seq=4,},
{grade=2,},
{grade=2,},
{grade=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{grade=2,},
{seq=3,},
{seq=3,},
{seq=4,},
{grade=2,},
{grade=2,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{seq=2,},
{grade=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{grade=3,},
{grade=3,},
{seq=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{seq=2,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{seq=3,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{seq=2,},
{seq=2,},
{seq=3,},
{seq=3,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,}
},

grade_meta_table_map={
[46]=10,	-- depth:1
[40]=46,	-- depth:2
[34]=46,	-- depth:2
[31]=34,	-- depth:3
[52]=40,	-- depth:3
[28]=52,	-- depth:4
[55]=52,	-- depth:4
[19]=55,	-- depth:5
[22]=19,	-- depth:6
[43]=19,	-- depth:6
[16]=22,	-- depth:7
[58]=22,	-- depth:7
[39]=3,	-- depth:1
[38]=2,	-- depth:1
[5]=2,	-- depth:1
[6]=3,	-- depth:1
[8]=5,	-- depth:2
[50]=38,	-- depth:2
[9]=6,	-- depth:2
[51]=39,	-- depth:2
[12]=9,	-- depth:3
[27]=51,	-- depth:3
[26]=50,	-- depth:3
[14]=26,	-- depth:4
[15]=27,	-- depth:4
[11]=8,	-- depth:3
[48]=12,	-- depth:4
[47]=11,	-- depth:4
[53]=50,	-- depth:3
[54]=51,	-- depth:3
[45]=48,	-- depth:5
[56]=53,	-- depth:4
[57]=45,	-- depth:6
[30]=54,	-- depth:4
[42]=30,	-- depth:5
[41]=53,	-- depth:4
[36]=30,	-- depth:5
[35]=47,	-- depth:5
[33]=36,	-- depth:6
[32]=35,	-- depth:6
[59]=35,	-- depth:6
[29]=32,	-- depth:7
[24]=36,	-- depth:6
[23]=59,	-- depth:7
[21]=24,	-- depth:7
[20]=23,	-- depth:8
[18]=21,	-- depth:8
[17]=20,	-- depth:9
[44]=20,	-- depth:9
[60]=24,	-- depth:7
},
reward_pool={
{item=item_table[1],is_rare=1,},
{item=item_table[2],},
{seq=1,gift_name="伏龙·装备挑战",},
{item=item_table[3],},
{item=item_table[4],},
{item=item_table[5],},
{item=item_table[6],},
{item=item_table[7],},
{},
{item=item_table[8],},
{item=item_table[9],},
{item=item_table[10],},
{item=item_table[11],},
{item=item_table[12],},
{item=item_table[13],},
{item=item_table[14],},
{item=item_table[15],},
{item=item_table[16],},
{item=item_table[17],},
{item=item_table[18],},
{item=item_table[1],is_rare=1,},
{item=item_table[19],},
{seq=2,gift_name="伏龙·骑宠挑战",},
{item=item_table[20],},
{item=item_table[21],},
{item=item_table[22],},
{item=item_table[23],},
{item=item_table[24],},
{item=item_table[25],},
{item=item_table[26],},
{item=item_table[27],},
{item=item_table[9],},
{item=item_table[10],},
{item=item_table[11],},
{item=item_table[12],},
{item=item_table[13],},
{item=item_table[14],},
{item=item_table[7],},
{item=item_table[28],},
{item=item_table[29],},
{item=item_table[30],},
{item=item_table[1],is_rare=1,},
{item=item_table[31],},
{},
{item=item_table[32],},
{item=item_table[33],},
{item=item_table[34],},
{item=item_table[35],},
{item=item_table[36],},
{item=item_table[37],},
{item=item_table[38],},
{item=item_table[39],},
{item=item_table[9],},
{item=item_table[10],},
{item=item_table[11],},
{item=item_table[12],},
{item=item_table[13],},
{item=item_table[14],},
{item=item_table[7],},
{item=item_table[40],},
{item=item_table[41],},
{item=item_table[42],},
{item=item_table[43],},
{item=item_table[44],},
{item=item_table[45],},
{item=item_table[1],is_rare=1,},
{item=item_table[46],},
{seq=4,gift_name="伏龙·天魂法器挑战",},
{item=item_table[47],},
{item=item_table[48],},
{item=item_table[49],},
{item=item_table[50],},
{item=item_table[51],},
{item=item_table[52],},
{item=item_table[53],},
{item=item_table[54],},
{item=item_table[55],},
{item=item_table[9],},
{item=item_table[10],},
{item=item_table[11],},
{item=item_table[12],},
{item=item_table[13],},
{item=item_table[14],},
{item=item_table[7],},
{item=item_table[56],},
{item=item_table[57],},
{grade=2,},
{grade=2,},
{grade=2,},
{item=item_table[3],},
{item=item_table[4],},
{item=item_table[58],},
{item=item_table[6],},
{item=item_table[59],},
{item=item_table[60],},
{item=item_table[8],},
{item=item_table[61],},
{item=item_table[62],},
{item=item_table[17],},
{item=item_table[63],},
{grade=2,},
{item=item_table[64],},
{grade=2,},
{item=item_table[20],},
{item=item_table[21],},
{item=item_table[65],},
{item=item_table[23],},
{item=item_table[24],},
{item=item_table[25],},
{item=item_table[26],},
{item=item_table[27],},
{item=item_table[66],},
{item=item_table[67],},
{grade=2,},
{item=item_table[30],},
{grade=2,item=item_table[68],},
{grade=2,},
{grade=2,},
{item=item_table[32],},
{item=item_table[33],},
{item=item_table[34],},
{item=item_table[35],},
{item=item_table[36],},
{item=item_table[37],},
{item=item_table[38],},
{item=item_table[39],},
{item=item_table[69],},
{item=item_table[40],},
{item=item_table[41],},
{item=item_table[42],},
{item=item_table[43],},
{item=item_table[44],},
{item=item_table[45],},
{grade=2,},
{grade=2,},
{grade=2,},
{item=item_table[47],},
{item=item_table[48],},
{item=item_table[49],},
{item=item_table[50],},
{item=item_table[51],},
{item=item_table[52],},
{item=item_table[53],},
{item=item_table[70],},
{item=item_table[71],},
{item=item_table[72],},
{item=item_table[73],},
{item=item_table[74],},
{grade=3,},
{grade=3,},
{grade=3,},
{item=item_table[3],},
{item=item_table[4],},
{item=item_table[58],},
{item=item_table[6],},
{item=item_table[59],},
{item=item_table[60],},
{grade=3,},
{item=item_table[61],},
{item=item_table[62],},
{grade=3,},
{item=item_table[63],},
{grade=3,},
{grade=3,},
{grade=3,},
{item=item_table[20],},
{grade=3,},
{item=item_table[65],},
{item=item_table[23],},
{item=item_table[24],},
{item=item_table[25],},
{item=item_table[26],},
{item=item_table[27],},
{item=item_table[66],},
{item=item_table[67],},
{item=item_table[29],},
{item=item_table[30],},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{item=item_table[33],},
{item=item_table[34],},
{item=item_table[35],},
{item=item_table[36],},
{item=item_table[37],},
{item=item_table[38],},
{item=item_table[39],},
{item=item_table[69],},
{item=item_table[40],},
{item=item_table[41],},
{item=item_table[42],},
{item=item_table[43],},
{grade=3,},
{item=item_table[45],},
{grade=3,},
{item=item_table[64],},
{grade=3,},
{item=item_table[47],},
{item=item_table[48],},
{item=item_table[49],},
{item=item_table[50],},
{grade=3,},
{item=item_table[52],},
{item=item_table[53],},
{item=item_table[70],},
{item=item_table[71],},
{item=item_table[72],},
{item=item_table[73],},
{item=item_table[74],},
{grade=4,item=item_table[68],},
{grade=4,},
{grade=4,},
{item=item_table[3],},
{item=item_table[4],},
{item=item_table[58],},
{item=item_table[6],},
{item=item_table[59],},
{item=item_table[60],},
{item=item_table[8],},
{item=item_table[61],},
{item=item_table[62],},
{item=item_table[17],},
{item=item_table[63],},
{grade=4,},
{item=item_table[64],},
{grade=4,},
{item=item_table[20],},
{grade=4,},
{item=item_table[65],},
{grade=4,},
{item=item_table[24],},
{item=item_table[25],},
{item=item_table[26],},
{item=item_table[27],},
{item=item_table[66],},
{item=item_table[67],},
{item=item_table[29],},
{item=item_table[30],},
{grade=4,},
{item=item_table[64],},
{grade=4,},
{item=item_table[32],},
{item=item_table[33],},
{item=item_table[34],},
{item=item_table[35],},
{item=item_table[36],},
{item=item_table[37],},
{item=item_table[38],},
{item=item_table[39],},
{item=item_table[69],},
{item=item_table[40],},
{item=item_table[41],},
{item=item_table[42],},
{item=item_table[43],},
{grade=4,},
{item=item_table[45],},
{grade=4,item=item_table[68],},
{item=item_table[64],},
{grade=4,},
{grade=4,},
{item=item_table[48],},
{item=item_table[49],},
{item=item_table[50],},
{item=item_table[51],},
{item=item_table[52],},
{item=item_table[53],},
{item=item_table[70],},
{item=item_table[71],},
{item=item_table[72],},
{item=item_table[73],},
{item=item_table[74],},
{grade=5,},
{item=item_table[64],},
{grade=5,},
{item=item_table[3],},
{item=item_table[4],},
{item=item_table[58],},
{item=item_table[6],},
{item=item_table[59],},
{item=item_table[60],},
{item=item_table[8],},
{item=item_table[61],},
{item=item_table[62],},
{item=item_table[17],},
{item=item_table[63],},
{grade=5,item=item_table[68],},
{item=item_table[64],},
{grade=5,},
{item=item_table[20],},
{item=item_table[21],},
{grade=5,},
{item=item_table[23],},
{item=item_table[24],},
{item=item_table[25],},
{item=item_table[26],},
{item=item_table[27],},
{item=item_table[66],},
{item=item_table[67],},
{item=item_table[29],},
{item=item_table[30],},
{grade=5,},
{item=item_table[64],},
{grade=5,},
{item=item_table[32],},
{item=item_table[33],},
{item=item_table[34],},
{item=item_table[35],},
{item=item_table[36],},
{item=item_table[37],},
{item=item_table[38],},
{grade=5,},
{grade=5,},
{grade=5,},
{item=item_table[41],},
{item=item_table[42],},
{item=item_table[43],},
{item=item_table[44],},
{item=item_table[45],},
{grade=5,},
{grade=5,},
{grade=5,},
{item=item_table[47],},
{item=item_table[48],},
{item=item_table[49],},
{item=item_table[50],},
{item=item_table[51],},
{item=item_table[52],},
{item=item_table[53],},
{item=item_table[70],},
{item=item_table[71],},
{item=item_table[72],},
{grade=5,},
{item=item_table[74],}
},

reward_pool_meta_table_map={
[122]=118,	-- depth:1
[123]=122,	-- depth:2
[124]=123,	-- depth:3
[125]=124,	-- depth:4
[126]=125,	-- depth:5
[127]=126,	-- depth:6
[128]=127,	-- depth:7
[129]=128,	-- depth:8
[130]=129,	-- depth:9
[131]=130,	-- depth:10
[132]=131,	-- depth:11
[133]=132,	-- depth:12
[241]=242,	-- depth:1
[255]=241,	-- depth:2
[121]=133,	-- depth:13
[254]=255,	-- depth:3
[120]=121,	-- depth:14
[179]=241,	-- depth:2
[312]=126,	-- depth:6
[315]=312,	-- depth:7
[316]=315,	-- depth:8
[317]=316,	-- depth:9
[318]=317,	-- depth:10
[319]=318,	-- depth:11
[311]=319,	-- depth:12
[310]=311,	-- depth:13
[309]=310,	-- depth:14
[308]=309,	-- depth:15
[307]=308,	-- depth:16
[306]=307,	-- depth:17
[305]=306,	-- depth:18
[303]=305,	-- depth:19
[117]=303,	-- depth:20
[119]=117,	-- depth:21
[181]=119,	-- depth:22
[183]=181,	-- depth:23
[182]=183,	-- depth:24
[256]=318,	-- depth:11
[243]=256,	-- depth:12
[244]=243,	-- depth:13
[245]=244,	-- depth:14
[246]=245,	-- depth:15
[248]=246,	-- depth:16
[249]=248,	-- depth:17
[250]=249,	-- depth:18
[251]=250,	-- depth:19
[252]=251,	-- depth:20
[253]=252,	-- depth:21
[247]=253,	-- depth:22
[257]=247,	-- depth:23
[313]=251,	-- depth:20
[194]=256,	-- depth:12
[184]=194,	-- depth:13
[185]=184,	-- depth:14
[186]=185,	-- depth:15
[187]=186,	-- depth:16
[188]=187,	-- depth:17
[195]=188,	-- depth:18
[190]=195,	-- depth:19
[189]=190,	-- depth:20
[191]=189,	-- depth:21
[192]=191,	-- depth:22
[193]=192,	-- depth:23
[314]=190,	-- depth:20
[227]=23,	-- depth:1
[165]=227,	-- depth:2
[213]=3,	-- depth:1
[116]=42,	-- depth:1
[302]=116,	-- depth:2
[275]=213,	-- depth:2
[289]=165,	-- depth:3
[136]=68,	-- depth:1
[151]=275,	-- depth:3
[198]=136,	-- depth:2
[89]=151,	-- depth:4
[240]=302,	-- depth:3
[35]=23,	-- depth:1
[34]=35,	-- depth:2
[33]=34,	-- depth:3
[32]=33,	-- depth:4
[31]=32,	-- depth:5
[30]=31,	-- depth:6
[29]=30,	-- depth:7
[28]=29,	-- depth:8
[27]=28,	-- depth:9
[26]=27,	-- depth:10
[25]=26,	-- depth:11
[24]=25,	-- depth:12
[22]=24,	-- depth:13
[20]=3,	-- depth:1
[19]=20,	-- depth:2
[18]=19,	-- depth:3
[17]=18,	-- depth:4
[2]=17,	-- depth:5
[4]=2,	-- depth:6
[5]=4,	-- depth:7
[6]=5,	-- depth:8
[7]=6,	-- depth:9
[8]=7,	-- depth:10
[36]=22,	-- depth:14
[9]=8,	-- depth:11
[11]=9,	-- depth:12
[12]=11,	-- depth:13
[13]=12,	-- depth:14
[14]=13,	-- depth:15
[15]=14,	-- depth:16
[16]=15,	-- depth:17
[10]=16,	-- depth:18
[103]=289,	-- depth:4
[37]=36,	-- depth:15
[39]=37,	-- depth:16
[322]=198,	-- depth:3
[260]=322,	-- depth:4
[86]=68,	-- depth:1
[85]=86,	-- depth:2
[84]=85,	-- depth:3
[83]=84,	-- depth:4
[82]=83,	-- depth:5
[81]=82,	-- depth:6
[38]=39,	-- depth:17
[79]=81,	-- depth:7
[78]=79,	-- depth:8
[77]=78,	-- depth:9
[80]=77,	-- depth:10
[75]=80,	-- depth:11
[40]=38,	-- depth:18
[41]=40,	-- depth:19
[76]=75,	-- depth:12
[67]=76,	-- depth:13
[69]=67,	-- depth:14
[70]=69,	-- depth:15
[178]=42,	-- depth:1
[72]=70,	-- depth:16
[73]=72,	-- depth:17
[74]=73,	-- depth:18
[71]=74,	-- depth:19
[231]=27,	-- depth:10
[233]=231,	-- depth:11
[230]=233,	-- depth:12
[234]=230,	-- depth:13
[235]=234,	-- depth:14
[236]=235,	-- depth:15
[237]=236,	-- depth:16
[238]=237,	-- depth:17
[239]=238,	-- depth:18
[259]=260,	-- depth:5
[232]=239,	-- depth:19
[1]=20,	-- depth:2
[262]=259,	-- depth:6
[292]=230,	-- depth:13
[293]=292,	-- depth:14
[294]=293,	-- depth:15
[295]=294,	-- depth:16
[296]=295,	-- depth:17
[297]=296,	-- depth:18
[298]=297,	-- depth:19
[299]=298,	-- depth:20
[300]=299,	-- depth:21
[301]=300,	-- depth:22
[321]=259,	-- depth:6
[323]=321,	-- depth:7
[324]=323,	-- depth:8
[325]=324,	-- depth:9
[326]=325,	-- depth:10
[327]=326,	-- depth:11
[328]=327,	-- depth:12
[329]=328,	-- depth:13
[330]=329,	-- depth:14
[331]=330,	-- depth:15
[332]=331,	-- depth:16
[291]=301,	-- depth:23
[261]=323,	-- depth:8
[290]=291,	-- depth:24
[286]=275,	-- depth:3
[263]=261,	-- depth:9
[264]=263,	-- depth:10
[265]=264,	-- depth:11
[266]=265,	-- depth:12
[267]=266,	-- depth:13
[268]=267,	-- depth:14
[269]=268,	-- depth:15
[270]=269,	-- depth:16
[271]=270,	-- depth:17
[272]=271,	-- depth:18
[274]=286,	-- depth:4
[276]=274,	-- depth:5
[277]=276,	-- depth:6
[278]=277,	-- depth:7
[279]=278,	-- depth:8
[280]=279,	-- depth:9
[281]=280,	-- depth:10
[282]=281,	-- depth:11
[283]=282,	-- depth:12
[284]=283,	-- depth:13
[285]=284,	-- depth:14
[288]=290,	-- depth:25
[229]=291,	-- depth:24
[167]=229,	-- depth:25
[226]=229,	-- depth:25
[114]=300,	-- depth:22
[115]=114,	-- depth:23
[135]=321,	-- depth:7
[137]=135,	-- depth:8
[138]=137,	-- depth:9
[139]=138,	-- depth:10
[140]=139,	-- depth:11
[141]=140,	-- depth:12
[142]=141,	-- depth:13
[143]=142,	-- depth:14
[144]=143,	-- depth:15
[145]=144,	-- depth:16
[146]=145,	-- depth:17
[147]=146,	-- depth:18
[148]=147,	-- depth:19
[150]=274,	-- depth:5
[152]=150,	-- depth:6
[153]=152,	-- depth:7
[154]=153,	-- depth:8
[228]=226,	-- depth:26
[156]=154,	-- depth:9
[113]=115,	-- depth:24
[157]=156,	-- depth:10
[112]=113,	-- depth:25
[110]=112,	-- depth:26
[21]=35,	-- depth:2
[66]=86,	-- depth:2
[88]=150,	-- depth:6
[90]=88,	-- depth:7
[91]=90,	-- depth:8
[92]=91,	-- depth:9
[93]=92,	-- depth:10
[94]=93,	-- depth:11
[95]=94,	-- depth:12
[96]=95,	-- depth:13
[97]=96,	-- depth:14
[98]=97,	-- depth:15
[99]=98,	-- depth:16
[100]=99,	-- depth:17
[102]=110,	-- depth:27
[104]=102,	-- depth:28
[105]=104,	-- depth:29
[106]=105,	-- depth:30
[107]=106,	-- depth:31
[108]=107,	-- depth:32
[109]=108,	-- depth:33
[111]=109,	-- depth:34
[158]=96,	-- depth:14
[155]=158,	-- depth:15
[160]=155,	-- depth:16
[159]=160,	-- depth:17
[203]=141,	-- depth:13
[204]=203,	-- depth:14
[205]=204,	-- depth:15
[206]=205,	-- depth:16
[207]=206,	-- depth:17
[208]=207,	-- depth:18
[209]=208,	-- depth:19
[210]=209,	-- depth:20
[212]=88,	-- depth:7
[214]=212,	-- depth:8
[215]=214,	-- depth:9
[216]=215,	-- depth:10
[217]=216,	-- depth:11
[218]=217,	-- depth:12
[219]=218,	-- depth:13
[220]=219,	-- depth:14
[221]=220,	-- depth:15
[222]=221,	-- depth:16
[223]=222,	-- depth:17
[224]=223,	-- depth:18
[201]=210,	-- depth:21
[200]=201,	-- depth:22
[202]=200,	-- depth:23
[197]=202,	-- depth:24
[161]=223,	-- depth:18
[162]=161,	-- depth:19
[164]=102,	-- depth:28
[166]=164,	-- depth:29
[333]=209,	-- depth:20
[168]=166,	-- depth:30
[199]=197,	-- depth:25
[170]=168,	-- depth:31
[171]=170,	-- depth:32
[169]=171,	-- depth:33
[177]=169,	-- depth:34
[172]=177,	-- depth:35
[176]=172,	-- depth:36
[175]=176,	-- depth:37
[334]=333,	-- depth:21
[174]=175,	-- depth:38
[173]=174,	-- depth:39
[149]=1,	-- depth:3
[287]=21,	-- depth:3
[211]=1,	-- depth:3
[196]=66,	-- depth:3
[273]=211,	-- depth:4
[163]=21,	-- depth:3
[258]=66,	-- depth:3
[87]=273,	-- depth:5
[134]=258,	-- depth:4
[225]=287,	-- depth:4
[320]=134,	-- depth:5
[101]=225,	-- depth:5
},
rate_weight={
{},
{min_lucky=200001,max_lucky=500000,},
{min_lucky=500001,max_lucky=800000,},
{min_lucky=800001,max_lucky=1600000,},
{min_lucky=1600001,max_lucky=3200000,},
{min_lucky=3200001,max_lucky=6400000,},
{min_lucky=6400001,max_lucky=12800000,weight=10000000,}
},

rate_weight_meta_table_map={
},
rate_show={
{model_bundle_name="model/zuoqi/2016_prefab",model_asset_name=2016,model_show_itemid=23350,},
{grade=2,},
{grade=3,},
{grade=4,},
{grade=5,}
},

rate_show_meta_table_map={
[3]=1,	-- depth:1
},
lucky_grade_reward={
{item=item_table[75],},
{seq=1,need_lucky=18000,},
{seq=2,need_lucky=31000,},
{seq=3,need_lucky=50000,},
{seq=4,need_lucky=75000,item=item_table[4],},
{seq=5,need_lucky=106000,item=item_table[76],},
{seq=6,need_lucky=149000,item=item_table[77],},
{seq=7,need_lucky=200000,show_item=91421,item=item_table[1],},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,item=item_table[68],},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,}
},

lucky_grade_reward_meta_table_map={
[17]=1,	-- depth:1
[33]=17,	-- depth:2
[9]=33,	-- depth:3
[25]=9,	-- depth:4
[27]=3,	-- depth:1
[19]=27,	-- depth:2
[18]=2,	-- depth:1
[34]=18,	-- depth:2
[35]=19,	-- depth:3
[26]=34,	-- depth:3
[11]=35,	-- depth:4
[10]=26,	-- depth:4
[4]=5,	-- depth:1
[38]=6,	-- depth:1
[31]=7,	-- depth:1
[30]=38,	-- depth:2
[29]=5,	-- depth:1
[28]=4,	-- depth:2
[37]=29,	-- depth:2
[36]=28,	-- depth:3
[20]=36,	-- depth:4
[23]=31,	-- depth:2
[22]=30,	-- depth:3
[21]=37,	-- depth:3
[39]=23,	-- depth:3
[15]=39,	-- depth:4
[14]=22,	-- depth:4
[13]=21,	-- depth:4
[12]=20,	-- depth:5
[32]=8,	-- depth:1
[16]=32,	-- depth:2
[24]=8,	-- depth:1
[40]=16,	-- depth:3
},
rmb_buy={
{},
{seq=1,rmb_seq=1,rmb_price=128,reward_item={[0]=item_table[78],[1]=item_table[79],[2]=item_table[23],[3]=item_table[80],[4]=item_table[81],[5]=item_table[82]},show_reward_item=item_table[78],},
{seq=2,rmb_seq=2,rmb_price=648,reward_item={[0]=item_table[83],[1]=item_table[84],[2]=item_table[23],[3]=item_table[85],[4]=item_table[86],[5]=item_table[87]},show_reward_item=item_table[83],},
{seq=3,rmb_seq=3,rmb_price=1000,reward_item={[0]=item_table[88],[1]=item_table[89],[2]=item_table[23],[3]=item_table[90],[4]=item_table[91],[5]=item_table[92]},show_reward_item=item_table[88],},
{seq=4,rmb_seq=4,rmb_price=2000,reward_item={[0]=item_table[93],[1]=item_table[64],[2]=item_table[94],[3]=item_table[95],[4]=item_table[96],[5]=item_table[97]},show_reward_item=item_table[93],},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,}
},

rmb_buy_meta_table_map={
[45]=5,	-- depth:1
[32]=2,	-- depth:1
[33]=3,	-- depth:1
[34]=4,	-- depth:1
[35]=45,	-- depth:2
[38]=33,	-- depth:2
[47]=32,	-- depth:2
[39]=34,	-- depth:2
[40]=35,	-- depth:3
[42]=47,	-- depth:3
[43]=38,	-- depth:3
[44]=39,	-- depth:3
[48]=43,	-- depth:4
[37]=42,	-- depth:4
[30]=40,	-- depth:4
[25]=30,	-- depth:5
[28]=48,	-- depth:5
[7]=37,	-- depth:5
[8]=28,	-- depth:6
[9]=44,	-- depth:4
[10]=25,	-- depth:6
[12]=7,	-- depth:6
[13]=8,	-- depth:7
[14]=9,	-- depth:5
[15]=10,	-- depth:7
[17]=12,	-- depth:7
[18]=13,	-- depth:8
[19]=14,	-- depth:6
[20]=15,	-- depth:8
[22]=17,	-- depth:8
[23]=18,	-- depth:9
[24]=19,	-- depth:7
[49]=24,	-- depth:8
[27]=22,	-- depth:9
[29]=49,	-- depth:9
[50]=20,	-- depth:9
},
open_act_day={
{},
{start_day=5,end_day=99999,}
},

open_act_day_meta_table_map={
},
lucky_double={
{type=1,},
{grade=2,need_cost=5888,lucky_per=3,rmb_seq=2,},
{grade=3,need_cost=30,lucky_per=4,rmb_seq=3,},
{grade=4,need_cost=68,lucky_per=8,rmb_seq=4,},
{grade=5,need_cost=128,lucky_per=10,rmb_seq=5,}
},

lucky_double_meta_table_map={
[2]=1,	-- depth:1
},
other_default_table={daily_reward_item={[0]=item_table[98]},daily_reward_lucky=10,chongzhi_add_lucky=1,chongzhi_add_lucky_max=30000,open_day_1_auto_open=0,is_show_lucky_double=1,},

open_day_default_table={start_day=1,end_day=7,grade=1,rate_count=2,},

grade_default_table={grade=1,seq=1,mode=1,times=1,need_cost=40,add_lucky=1,},

reward_pool_default_table={grade=1,seq=3,item=item_table[99],is_rare=0,gift_name="伏龙·云篆挑战",},

rate_weight_default_table={min_lucky=75000,max_lucky=200000,weight=0,},

rate_show_default_table={grade=1,model_show_type=1,model_bundle_name="model/zuoqi/2109_prefab",model_asset_name=2109,model_show_itemid=23352,show_icon=0,},

lucky_grade_reward_default_table={grade=1,seq=0,need_lucky=8000,add_lucky=0,show_item=91420,item=item_table[78],},

rmb_buy_default_table={grade=1,seq=0,rmb_type=121,rmb_seq=0,rmb_price=68,reward_item={[0]=item_table[75],[1]=item_table[100],[2]=item_table[23],[3]=item_table[101],[4]=item_table[102],[5]=item_table[103]},show_reward_item=item_table[75],},

open_act_day_default_table={start_day=1,end_day=1,},

lucky_double_default_table={grade=1,type=2,need_cost=2888,lucky_per=2,rmb_type=129,rmb_seq=1,}

}

