
FuBenSaoDang = FuBenSaoDang or BaseClass(SafeBaseView)

function FuBenSaoDang:__init()
	self:SetMaskBg(true)
	self.view_name = "FuBenSaoDang"
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(600, 420)})
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_combine")
	self.item_data_event = BindTool.Bind1(self.ItemDataChangeCallback, self)
end

function FuBenSaoDang:__delete()

end

function FuBenSaoDang:ReleaseCallBack()
	self.fb_type = nil
	if self.callback_func then
		self.callback_func = nil
	end

	if self.item then
		self.item:DeleteMe()
		self.item = nil
	end

	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
	if self.day_count_change_event then
		GlobalEventSystem:UnBind(self.day_count_change_event)
		self.day_count_change_event = nil
	end
end

function FuBenSaoDang:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.FuBenPanel.SaoDangTitle
	self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind(self.Close, self))
	self.node_list["btn_buy_count"].button:AddClickListener(BindTool.Bind(self.OnClinkCombine, self))
	self.node_list["Add_Button"].button:AddClickListener(BindTool.Bind(self.AddCount, self))
	self.node_list["Button_reduce"].button:AddClickListener(BindTool.Bind(self.ReduceCount, self))
	self.node_list["Add_Button_tips"].button:AddClickListener(BindTool.Bind(self.Showtips, self))
	self.node_list["free_combine_btn"].button:AddClickListener(BindTool.Bind(self.OnClickFreeCombine, self))
	self.node_list["free_combine_count"].button:AddClickListener(BindTool.Bind(self.OnClickFreeCombine, self))

	self.item = ItemCell.New(self.node_list.comsume_item)
	self.item:SetNeedItemGetWay(true)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
	self.day_count_change_event = GlobalEventSystem:Bind(OtherEventType.DAY_COUNT_CHANGE, BindTool.Bind(self.DayCounterChange, self))
end

function FuBenSaoDang:ItemDataChangeCallback()
	self:Flush()
end

function FuBenSaoDang:ShowIndexCallBack()
	self:Flush()
end

function FuBenSaoDang:DayCounterChange(day_counter_id)
	self:Flush(0, "DayCounterChange", {DayCounterChange = true})
end

function FuBenSaoDang:OnFlush(param_t, index)
	self:RefreshView(param_t)
	self:FlushComsumeInfo()
end

function FuBenSaoDang:RefreshView(param_t)
	if nil == self.fb_type then
		return
	end

	local cfg = FuBenWGData.Instance:GetSaoDangCfg(self.fb_type)
	if nil == cfg then
		return
	end
	
	self:CalcTimes()
	self.node_list.shengyu_count.text.text = string.format(Language.FuBenPanel.CommbineHaveCount, self.have_count, self.all_count)

	--购买完次数刷新处理
	for k, v in pairs(param_t) do
		if k == "DayCounterChange" then
			self.need_combine_count = self.have_count
		end
	end

	self.node_list.num_show_txt.text.text = self.need_combine_count
	local data = cfg.item
	local has_num = ItemWGData.Instance:GetItemNumInBagById(data.item_id)
	local need_num = data.num * self.need_combine_count
	local color = has_num >= need_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
	self.item:SetData(data)
    self.item:SetRightBottomColorText(ToColorStr(has_num .. "/" .. need_num, color))
    local count = need_num - has_num > 1 and need_num - has_num or 1
    self.item:SetDefShowBuyCount(count)
    self.item:SetRightBottomTextVisible(true)

    self.node_list.saodang_count.text.text = Language.FuBenPanel.ShowSaoDangCount
	self.node_list.rich_buy_desc.text.text = Language.FuBenPanel.ShowSaoDangDsc1
	if self.fb_type == FUBEN_TYPE.FBCT_TIANSHEN_FB or self.fb_type == FUBEN_TYPE.FBCT_PETBEN then

		self.node_list.rich_comsume_tip.text.text = Language.FuBenPanel.ShengLingSaoDangTip
		--self.node_list.free_combine_btn.text.text = Language.FuBenPanel.ShengLingSaoDangTip3
	else
		self.node_list.rich_comsume_tip.text.text = ""
		--self.node_list.free_combine_btn.text.text = Language.FuBenPanel.ShengLingSaoDangTip2
	end
end

function FuBenSaoDang:FlushComsumeInfo()
	self.node_list.free_left_day.text.text = Language.FuBenPanel.CombineFreeCount2

	local is_active = RechargeWGData.Instance:IsActiveTZCard(INVEST_CARD_TYPE.MonthCard)
	self.node_list.combine_root:SetActive(not is_active)
	self.node_list.free_combine_count:SetActive(is_active)
end

function FuBenSaoDang:OnClinkCombine()
	local cfg = FuBenWGData.Instance:GetSaoDangCfg(self.fb_type)
	if nil == cfg then
		return
	end

	local item= cfg.item
	local need_num = item.num * self.need_combine_count
	local item_num = ItemWGData.Instance:GetItemNumInBagById(item.item_id)
	local is_free_combine = RechargeWGData.Instance:IsActiveTZCard(INVEST_CARD_TYPE.MonthCard) -- 月卡免费扫荡

    if need_num > item_num and not is_free_combine then
        TipWGData.Instance:SetDefShowBuyCount(need_num - item_num)
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item.item_id})
	else
		if self.callback_func then
			self.callback_func()
			self.callback_func = nil
		end

		local param_1 = self.param1
		local param_2 = self.need_combine_count
		if self.fb_type == FUBEN_TYPE.FBCT_PETBEN then
			param_1 = self.param1 - 1
		elseif self.fb_type == FUBEN_TYPE.FBCT_TONGBIBEN then
			param_1 = self.need_combine_count
			param_2 = 0
		end

		FuBenWGCtrl.Instance:SendSwipeFB(self.fb_type, param_1, param_2)
		self:Close()
	end
end

function FuBenSaoDang:AddCount()
	if self.need_combine_count < self.have_count then
		self.need_combine_count = self.need_combine_count + 1
		self:Flush()
		return
	end

	FuBenPanelWGCtrl.Instance:OpenBuyView(self.fb_type)
end

function FuBenSaoDang:ReduceCount()
	if self.need_combine_count <= 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CombineMinNum)
		return
	end

	self.need_combine_count = self.need_combine_count - 1
	self:Flush()
end

function FuBenSaoDang:Showtips()
	local str = ""
	if self.fb_type == FUBEN_TYPE.FBCT_TIANSHEN_FB then
		str = Language.FuBen.TianShen_Des
	elseif self.fb_type == FUBEN_TYPE.FBCT_PETBEN then
		str = Language.FuBen.Pet_Des
	elseif self.fb_type == FUBEN_TYPE.FBCT_BAGUAMIZHEN_FB then
		str = Language.FuBenPanel.BaGuaMiZhenTipsDec
	elseif self.fb_type == FUBEN_TYPE.FBCT_TONGBIBEN then
		str = Language.FuBen.Copper_Des
	end

	RuleTip.Instance:SetContent(str, Language.FuBenPanel.SaoDangTipsTitle)
end

function FuBenSaoDang:SetBasData(fb_type, param1, param2, callback_func)
	self.fb_type = fb_type
	self.param1 = param1
	self.param2 = param2
	self.callback_func = callback_func
	if nil == self.fb_type then
		return
	end

	self:CalcTimes()
	self.need_combine_count = self.have_count
	self:Open()
end

function FuBenSaoDang:CalcTimes()
	self.have_count = 0
	self.all_count = 0

	local day_enter_times = 0
	if self.fb_type == FUBEN_TYPE.FBCT_TIANSHEN_FB then
		local day_free_times = FuBenPanelWGData.Instance:GetTianShenDayFreeTimes()
	    day_enter_times = FuBenPanelWGData.Instance:GetTianShenEnterTimes()
	    local day_buy_times = FuBenPanelWGData.Instance:GetTianShenBuyTimes()
	    local day_comsume_item_times = FuBenPanelWGData.Instance:GetTianShenComsumeItemTimes()
		self.all_count = day_free_times + day_buy_times + day_comsume_item_times

	elseif self.fb_type == FUBEN_TYPE.FBCT_PETBEN then
		local pet_info = FuBenPanelWGData.Instance:GetPetAllInfo()
		local other_cfg = FuBenPanelWGData.Instance:GetPetOtherCfg()
		day_enter_times = pet_info.day_times
		self.all_count = other_cfg.free_times + pet_info.buy_times

	elseif self.fb_type == FUBEN_TYPE.FBCT_BAGUAMIZHEN_FB then
		local day_free_times = FuBenPanelWGData.Instance:GetBaGuaMiZhenDayFreeTimes()
	    day_enter_times = FuBenPanelWGData.Instance:GetBaGuaMiZhenEnterTimes()
	    local day_buy_times = FuBenPanelWGData.Instance:GetBaGuaMiZhenBuyTimes()
	    self.all_count = day_free_times + day_buy_times

	elseif self.fb_type == FUBEN_TYPE.FBCT_TONGBIBEN then
		local copper_info = CopperFbWGData.Instance:GetTongBiInfo()
		local other_cfg = CopperFbWGData.Instance:GetOtherCfg()
		day_enter_times = copper_info.day_has_enter_times
		self.all_count = other_cfg.fb_day_free_times + copper_info.day_buy_times
	end

	self.have_count = self.all_count - day_enter_times
end

function FuBenSaoDang:OnClickFreeCombine()
	ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_month_card)
	self:Close()
end