require "game/common_skill_show/common_skill_show_data"
require "game/common_skill_show/common_skill_show_view"
require "game/common_skill_show/common_window_skill_show_view"
require "game/common_skill_show/tianshen_skill_show_view"
require "game/common_skill_show/boss_mabi_skill_show_view"
require "game/common_skill_show/fight_mount_skill_show_view"
require "game/common_skill_show/wuhun_skill_show_view"
require "game/common_skill_show/customized_skill_show_view"
require "game/common_skill_show/tianshen_heji_skill_show_view"
require "game/common_skill_show/beast_group_skill_show_view"
require "game/common_skill_show/mecha_skill_show_view"
require "game/common_skill_show/supreme_fields_skill_show_view"
require "game/common_skill_show/break_skill_show_view"
require "game/common_skill_show/spirit_skill_show_view"

CommonSkillShowCtrl = CommonSkillShowCtrl or BaseClass(BaseWGCtrl)
function CommonSkillShowCtrl:__init()
	if CommonSkillShowCtrl.Instance then
		error("[CommonSkillShowCtrl]:Attempt to create singleton twice!")
	end

	CommonSkillShowCtrl.Instance = self

    self.data = CommonSkillShowData.New()
    self.common_window_view = CommonWindowSkillShowView.New(GuideModuleName.CommonWindowSkillShowView)
	self.tianshen_show_view = TianShenSkillShowView.New(GuideModuleName.TianShenSkillShowView)
	self.boss_mabi_show_view = BossMaBiSkillShowView.New(GuideModuleName.BossMaBiSkillShowView)
	self.fight_mount_show_view = FightMountSkillShowView.New(GuideModuleName.FightMountSkillShowView)
	self.wuhun_show_view = WuHunSkillShowView.New(GuideModuleName.WuHunSkillShowView)
	self.customized_show_view = CustomizedSkillShowView.New(GuideModuleName.CustomizedSkillShowView)
	self.tianshen_heji_show_view = TianShenHeJiSkillShowView.New(GuideModuleName.TianShenHeJiSkillShowView)
	self.beast_group_skill_show_view = BeastGroupSkillShowView.New(GuideModuleName.BeastGroupSkillShowView)
	self.mecha_skill_show_view = MechaSkillShowView.New(GuideModuleName.MechaSkillShowView)
	self.supreme_fields_show_view = SupremeFieldsSkillShowView.New(GuideModuleName.SupremeFieldsSkillShowView)
	self.break_skill_show_view = BreakSkillShowView.New(GuideModuleName.SkillBreakPurchaseShowView)
	self.spirit_skill_show_view = SpiritSkillShowView.New(GuideModuleName.SpiritSkillShowView)
end

function CommonSkillShowCtrl:__delete()
    self.data:DeleteMe()
	self.data = nil

	self.common_window_view:DeleteMe()
	self.common_window_view = nil

	self.tianshen_show_view:DeleteMe()
	self.tianshen_show_view = nil

	self.boss_mabi_show_view:DeleteMe()
	self.boss_mabi_show_view = nil

	self.fight_mount_show_view:DeleteMe()
	self.fight_mount_show_view = nil

	self.wuhun_show_view:DeleteMe()
	self.wuhun_show_view = nil

	self.customized_show_view:DeleteMe()
	self.customized_show_view = nil

	self.tianshen_heji_show_view:DeleteMe()
	self.tianshen_heji_show_view = nil


	self.beast_group_skill_show_view:DeleteMe()
	self.beast_group_skill_show_view = nil

	self.mecha_skill_show_view:DeleteMe()
	self.mecha_skill_show_view = nil

	self.supreme_fields_show_view:DeleteMe()
	self.supreme_fields_show_view = nil

	self.spirit_skill_show_view:DeleteMe()
	self.spirit_skill_show_view = nil
	
    CommonSkillShowCtrl.Instance = nil
end

function CommonSkillShowCtrl:SetViewDataAndOpen(data)
    self.common_window_view:SetShowDataAndOpen(data)
end

function CommonSkillShowCtrl:SetTianShenSkillViewDataAndOpen(data)
    self.tianshen_show_view:SetShowDataAndOpen(data)
end

function CommonSkillShowCtrl:SetFightMountSkillViewDataAndOpen(data)
    self.fight_mount_show_view:SetShowDataAndOpen(data)
end

function CommonSkillShowCtrl:SetWuHunSkillViewDataAndOpen(data)
    self.wuhun_show_view:SetShowDataAndOpen(data)
end

function CommonSkillShowCtrl:SetCustomizedSkillViewDataAndOpen(data)
    self.customized_show_view:SetShowDataAndOpen(data)
end

function CommonSkillShowCtrl:SetTianShenHeJiSkillViewDataAndOpen(data)
    self.tianshen_heji_show_view:SetShowDataAndOpen(data)
end


function CommonSkillShowCtrl:SetBeastSkillViewDataAndOpen(data)
    self.beast_group_skill_show_view:SetShowDataAndOpen(data)
end

function CommonSkillShowCtrl:SetMechaSkillViewDataAndOpen(data)
    self.mecha_skill_show_view:SetShowDataAndOpen(data)
end

function CommonSkillShowCtrl:SetSupremeFieldsSkillViewDataAndOpen(data)
	self.supreme_fields_show_view:SetShowDataAndOpen(data)
end

function CommonSkillShowCtrl:SetBreakSkillViewDataAndOpen(data)
	self.break_skill_show_view:SetShowDataAndOpen(data)
end

function CommonSkillShowCtrl:SetSpiritSkillViewDataAndOpen(nuqi_type)
    self.spirit_skill_show_view:SetShowDataAndOpen(nuqi_type)
end