TransferFubenSceneLogic = TransferFubenSceneLogic or BaseClass(CommonFbLogic)

function TransferFubenSceneLogic:__init()
	BaseFbLogic.__init(self)
end

function TransferFubenSceneLogic:__delete()

end

function TransferFubenSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	ViewManager.Instance:Open(GuideModuleName.TransFerDemon)

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
        MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetTaskContents(false)

		local fb_cfg = Scene.Instance:GetCurFbSceneCfg()
		MainuiWGCtrl.Instance:SetFBNameState(true, fb_cfg.name)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)
	end)

	-- local time = FuBenWGData.Instance:GetSetFBPrepareTime()
	--副本cg 屏蔽
	-- local prof_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
	-- if prof_zhuan == 0 and time == 0 then
	-- 	CgManager.Instance:Play(BaseCg.New("cg/x1_bs_geren03_prefab", "X1_BS_GeRen03_cg_01"),function ()
	-- 			FuBenWGCtrl.Instance:CSFBPlayedCG()
	-- 		end,nil,nil)
	-- else
	-- 	FuBenWGCtrl.Instance:CSFBPlayedCG()
	-- end
	
	FuBenWGCtrl.Instance:CSFBPlayedCG()
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
end

function TransferFubenSceneLogic:Out()
	CommonFbLogic.Out(self)

    MainuiWGCtrl.Instance:SetOtherContents(false)
	MainuiWGCtrl.Instance:SetTaskContents(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)

	ViewManager.Instance:Close(GuideModuleName.TransFerDemon)
	if GameVoManager.Instance:GetMainRoleVo().hp <= 0 then
		FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.BACK_HOME, 1, -1)
	end
	TransFerWGCtrl.Instance:Open()
end