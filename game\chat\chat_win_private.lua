-- require("game/chat/chat_private_obj_item")
ChatWindow = ChatWindow or BaseClass(SafeBaseView)

function ChatWindow:InitPrivate()
	if self.is_init then
		return 
	end
	self.is_init = true
	self.curr_index = 0
	self.select_effect_index = 0
	self.content_list_view_list = {}                -- 聊天内容listview列表，每个对象一个listview
	
	--角色列表listview
	self.role_list_view = AsyncListView.New(ChatFriendItem, self.node_list["friend_cell_list"])
	self.role_list_view:SetSelectCallBack(BindTool.Bind1(self.OnClickListView, self))
	self.role_list_view:SetDataList(ChatWGData.Instance:GetPrivateObjList())

	self.chat_list_view = nil
	self:CreateContentListView()
end

function ChatWindow:DeletePrivate()
	self.is_init = false
	self.layout_chat_private = nil

	if self.role_list_view then
		self.role_list_view:DeleteMe()
		self.role_list_view = nil 
	end
	if self.chat_list_view then 
		self.chat_list_view:DeleteMe()
		self.chat_list_view = nil
	end
end

function ChatWindow:SetPrivateVisible(visible)
	if nil ~= self.layout_chat_private then
		self.layout_chat_private:setVisible(visible)
	end
end

function ChatWindow:IsPrivateOpen()
	return self:IsOpen() and self:IsPrivateIndex()
end

function ChatWindow:IsPrivateIndex()
	return self.curr_send_channel == CHANNEL_TYPE.PRIVATE
end

function ChatWindow:OpenPrivate(index)
	self.curr_index = index
	if self:IsOpen() then
		if self:IsPrivateIndex() then
			self:UpdatePrivateView(true)
			-- self:Flush(ChatWindowIndex.Private)
		else
			self.curr_send_channel = CHANNEL_TYPE.PRIVATE
			self:SelectTabCallback(CHANNEL_TYPE.PRIVATE)
		end
	else
		self.curr_send_channel = CHANNEL_TYPE.PRIVATE
		self:Open()
	end
end

function ChatWindow:OnClickListView(item, index)

	self.select_effect_index = index
	self:OnChangePrivateIndex(index)
	self:UpdateContentVisible()
end

-- 切换聊天
function ChatWindow:OnChangePrivateIndex(index)
	if self.curr_index ~= index then
		self.curr_index = index
		self:UpdatePrivateView()
	end
end

-- 添加聊天
function ChatWindow:OnAddChat()
	-- if nil == self.edit_player then
	-- 	return
	-- end
	-- 判断等级是否足够
	if GameVoManager.Instance:GetMainRoleVo().level < COMMON_CONSTS.PRIVATE_CHAT_LEVEL_LIMIT then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.LevelDeficient, COMMON_CONSTS.PRIVATE_CHAT_LEVEL_LIMIT))
		self.node_list["add_dialog_input"].input_field.text = ""
		return
	end

	local role_name = self.node_list["add_dialog_input"].input_field.text
	if string.len(role_name) <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.AddPrivate)
		return
	end
	self.node_list["add_dialog_input"].input_field.text = ""
	ChatWGCtrl.Instance:AddPrivateRequset(role_name)
end

function ChatWindow:UpdatePrivateView(is_list_change)

	if 0 == self.curr_index and ChatWGData.Instance:GetPrivateObjCount() > 0 then
		self.curr_index = 1
	end
	local obj_list = ChatWGData.Instance:GetPrivateObjList()

	local private_obj = ChatWGData.Instance:GetPrivateObjByIndex(self.curr_index)
	if nil ~= private_obj then
		self.node_list["chat_with_group"]:SetActive(true)
		self:UpdatePrivateRich(private_obj.sex, private_obj.username)
		self:SetPrivateRoleId(private_obj.role_id)
		ChatWindow.UpdateContentListView(self.chat_list_view, private_obj.msg_list, private_obj.unread_num)
		private_obj.unread_num = 0

		ChatWGData.Instance:RemPrivateUnreadMsg(private_obj.role_id)
		MainuiWGCtrl.Instance:SetChatPrivateUnreadMsgHead()
		-- MainuiWGCtrl.Instance:UpdatePrivateTip()
	else
		self.node_list["chat_private_list"]:SetActive(false)
		self.node_list["chat_with_group"]:SetActive(false)
		self:UpdatePrivateRich()
		self:SetPrivateRoleId(0)
	end

	local cur_item = self.role_list_view:GetItemAt(self.curr_index)
	if cur_item then
		cur_item:SetData(obj_list[self.curr_index])
	end
	
	if is_list_change then self:UpdateListView() end
	
	-- 更新选中特效
	if self.select_effect_index ~= self.curr_index then
		self.role_list_view:SelectIndex(self.curr_index)
		self:UpdateContentVisible()
	end
end

function ChatWindow:UpdateListView()
	local obj_list = ChatWGData.Instance:GetPrivateObjList()
	local old_count = self.role_list_view:GetCount()

	self.role_list_view:SetDataList(obj_list)
end

-- 刷新文字
function ChatWindow:UpdatePrivateRich(sex, name) 
	self.node_list["talk_with_name"].text.text = string.format(Language.Chat.PrivateDesc, name) 
end

-- 关闭当前聊天
function ChatWindow:CloseCurrentPrivate()
	ChatWGData.Instance:RemovePrivateObjByIndex(self.curr_index)
	self:CleanInput()
	self:RemoveContentListView(self.curr_index)

	if nil == ChatWGData.Instance:GetPrivateObjByIndex(self.curr_index) then
		self.curr_index = self.curr_index - 1
	end
	self.select_effect_index = 0
	self:UpdatePrivateView(true)

	SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.CloseCurrentPrivate)
end

-- 打开好友列表
function ChatWindow:OpenFriendList()

	if GameVoManager.Instance:GetMainRoleVo().level < COMMON_CONSTS.PRIVATE_CHAT_LEVEL_LIMIT then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.LevelDeficient, COMMON_CONSTS.PRIVATE_CHAT_LEVEL_LIMIT))
		return
	end

	--打开还有界面选择好友index回调
	SocietyWGCtrl.Instance:OpenFriendListView(9999, function (user_info)
		if nil == user_info then
			return
		end

		if nil == ChatWGData.Instance:GetPrivateObjByRoleId(user_info.user_id) then
			local private_obj = ChatWGData.CreatePrivateObj()
			private_obj.role_id = user_info.user_id
			private_obj.username = user_info.username or user_info.gamename or user_info.role_name
			private_obj.sex = user_info.sex
			private_obj.camp = user_info.camp
			private_obj.prof = user_info.prof
			private_obj.avatar_key_small = user_info.avatar_key_small
			private_obj.level = user_info.level
			ChatWGData.Instance:AddPrivateObj(private_obj.role_id, private_obj)
		end

		self.curr_index = ChatWGData.Instance:GetPrivateIndex(user_info.user_id)
		self:UpdatePrivateView(true)
	end)
end

-- 创建聊天内容listview
function ChatWindow:CreateContentListView()
	if not self.chat_list_view then
		self.chat_list_view =  ChatListView.New()
		self.chat_list_view:Create(ChatCell, self.node_list["chat_private_list"])
	end
end

-- 移除聊天内容listview
function ChatWindow:RemoveContentListView(index)
	local list_view = self.content_list_view_list[index]
	if nil == list_view then
		return
	end

	list_view:DeleteMe()
	table.remove(self.content_list_view_list, index)
	list_view = nil 
end

function ChatWindow:UpdateContentVisible()
	local obj_list = ChatWGData.Instance:GetPrivateObjList()
	if #obj_list <= 0 then 
		self.node_list["chat_private_list"]:SetActive(false)
	else
		self.node_list["chat_private_list"]:SetActive(true)
	end
end


--------------------------------------------------------------------------------------------
--私聊好友格子
ChatFriendItem = ChatFriendItem or BaseClass(BaseRender)

function ChatFriendItem:__init()
	
end

function ChatFriendItem:__delete()
	
end

function ChatFriendItem:OnFlush()
	if self.data == nil then return end

	self.node_list["Name"].text.text = self.data.username
	self.node_list["Level"].text.text = self.data.level


	local bundle, asset = AvatarManager.GetChatDefAvatar(self.data.prof, false)			
	self.node_list["Image"].image:LoadSprite(bundle, asset)
end

function ChatFriendItem:OnSelectChange(is_select)
	self.node_list["HL"]:SetActive(is_select)
end
