MustBuyTip = MustBuyTip or BaseClass(SafeBaseView)
function MustBuyTip:__init()
	self:AddViewResource(0, "uis/view/must_buy_ui_prefab", "must_buy_tip")
end

function MustBuyTip:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

	if self.close_anim then
        self.close_anim:Kill()
        self.close_anim = nil
    end

	if self.start_tween then
        self.start_tween:Kill()
        self.start_tween = nil
    end
end

function MustBuyTip:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("must_buy_tip") then
		CountDownManager.Instance:HasCountDown("must_buy_tip")
	end
end

function MustBuyTip:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list["cell"])
	self.node_list["btn_goto"].button:AddClickListener(BindTool.Bind(self.OnClickBtnGoto, self))
	self.node_list["tip_content"].canvas_group.alpha = 0
end

function MustBuyTip:ShowIndexCallBack()
	if CountDownManager.Instance:HasCountDown("must_buy_tip") then
		CountDownManager.Instance:HasCountDown("must_buy_tip")
	end
	local time = MustBuyWGData.Instance:GetShowTipTime()
	GlobalTimerQuest:AddDelayTimer(function()
		self:CloseAnim()
	end, time - 1)
	--CountDownManager.Instance:AddCountDown("must_buy_tip", BindTool.Bind(self.UpdateCount, self),
	--	BindTool.Bind(self.CompleteCount, self), nil, time, 0.3)
	self:Flush()
	self:OpenTween()
end

function MustBuyTip:OpenTween()
	if not self.start_tween then
		local cfg = MustBuyWGData.Instance:GetOtherCfg()
		local alpha_time = cfg.alpha_time or 0.3
		local sizeDelta_time = cfg.sizeDelta_time or 0.5
		--self.node_list["tip_content"].rect.sizeDelta = Vector2(200, 280)

		self.start_tween = DG.Tweening.DOTween.Sequence()
		self.start_tween:Append(self.node_list["tip_content"].canvas_group:DoAlpha(0,1,alpha_time))
		self.start_tween:AppendInterval(alpha_time)

		-- self.start_tween:Append(self.node_list["tip_content"].rect:DOSizeDelta(Vector2(500,280), 0.5))
		-- self.start_tween:AppendInterval(sizeDelta_time)

		self.start_tween:OnComplete(function()
			self.start_tween:Kill()
			self.start_tween = nil
		end)
	end
end

function MustBuyTip:OnClickBtnGoto()
	ViewManager.Instance:Open(GuideModuleName.MustBuy)
	self:Close()
end

function MustBuyTip:CloseAnim()
	if self.close_anim or self.node_list["tip_content"] == nil then
		return
	end
	local cfg = MustBuyWGData.Instance:GetOtherCfg()
	local alpha_time = cfg.alpha_time or 0.3
	local sizeDelta_time = cfg.sizeDelta_time or 0.5
	self.node_list["tip_content"].rect.sizeDelta = Vector2(500, 280)

	self.close_anim = DG.Tweening.DOTween.Sequence()
	-- self.close_anim:Append(self.node_list["tip_content"].rect:DOSizeDelta(Vector2(200,280), 0.5))
	-- self.close_anim:AppendInterval(sizeDelta_time)

	self.close_anim:Append(self.node_list["tip_content"].canvas_group:DoAlpha(1,0,alpha_time))
	self.close_anim:AppendInterval(alpha_time)

	self.close_anim:OnComplete(function()
		self.close_anim:Kill()
		self.close_anim = nil
		self:Close()
	end)

end

function MustBuyTip:OnFlush()
	if not self.data then
		return
	end
	self.item_cell:SetData(self.data.buy_item)
	--self.node_list["txt_tip"].text.text = self.data.description
end

function MustBuyTip:SetData(data)
	self.data = data
	self:Flush()
end
