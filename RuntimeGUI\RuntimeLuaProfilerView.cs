﻿using MikuLuaProfiler;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class RuntimeLuaProfilerView : RuntimeBaseView
{
    private bool isOpening = false;
    private bool isConnecting = false;
    public RuntimeLuaProfilerView() : base(RuntimeViewName.LUA_PROFILER)
    {
    
    }

    // 注意：必须在lua虚拟机启动前
    public void TryOpenLuaProfiler()
    {
        if (PlayerPrefs.GetInt("a3_fanli_is_local_windows_debug_exe_lua_profiler") != 1 || isOpening)
        {
            return;
        }

        //if (HookLuaSetup.TryOpenLuaProfiler())
        //{
        //    isOpening = true;
        //    isConnecting = true;
        //}
    }

    public void TryCloseLuaProfiler()
    {
        if (!isOpening)
        {
            return;
        }

        isOpening = false;
        //if (LuaProfiler.mainL != IntPtr.Zero)
        //{
        //    LuaDLL.lua_close(LuaProfiler.mainL);
        //}
        //LuaProfiler.mainL = IntPtr.Zero;
        //NetWorkClient.Close();
    }

    public void TryConnect()
    {
        //if (isOpening && !isConnecting)
        //{
        //    isConnecting = true;
        //    NetWorkClient.ConnectServer("127.0.0.1", 2333);
        //}
    }

    public void TryDisconnect()
    {
        //if (isOpening && isConnecting)
        //{
        //    isConnecting = false;
        //    NetWorkClient.Close();
        //}
    }

    override public void OnApplicationQuit()
    {
        this.TryCloseLuaProfiler();
    }

    override public void OnGameStop()
    {
        this.TryCloseLuaProfiler();
    }

    override protected void OnReapintWindow(int windowid)
    {
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("开始调试(须重启)"))
        {
            PlayerPrefs.SetInt("a3_fanli_is_local_windows_debug_exe_lua_profiler", 1);
            this.TryCloseLuaProfiler();
            GameRoot.Instance.Restart();
        }

        if (GUILayout.Button("停止调试(须重启)"))
        {
            PlayerPrefs.SetInt("a3_fanli_is_local_windows_debug_exe_lua_profiler", 1);
            this.TryCloseLuaProfiler();
            GameRoot.Instance.Restart();
        }

        if (isOpening)
        {
            if (isConnecting)
            {
                if (GUILayout.Button("暂停调试"))
                {
                    this.TryDisconnect();
                }
            }
            else
            {
                if (GUILayout.Button("继续调试"))
                {
                    this.TryConnect();
                }
            }
        }
        
        GUILayout.EndHorizontal();
    }
}
