require("game/bulit_in_gm/bulit_in_gm_data")
require("game/bulit_in_gm/bulit_in_gm_total_view")
require("game/bulit_in_gm/bulit_in_gm_gm_view")
require("game/bulit_in_gm/bulit_in_gm_print_view")
require("game/bulit_in_gm/bulit_in_gm_top_view")

BulitInGMCtrl = BulitInGMCtrl or BaseClass(BaseWGCtrl)
function BulitInGMCtrl:__init()
	if BulitInGMCtrl.Instance then
		error("[BulitInGMCtrl]:Attempt to create singleton twice!")
	end
	BulitInGMCtrl.Instance = self

    self.data = BulitInGMData.New()
    self.view = BulitInGMView.New()
	self.gm_top_view = BulitInTopGMView.New()
end

function BulitInGMCtrl:__delete()
    self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	self.gm_top_view:DeleteMe()
	self.gm_top_view = nil

    BulitInGMCtrl.Instance = nil
end

function BulitInGMCtrl:AddProtocolMsg(msg_type, op_type, time)
	self.view:AddProtocolMsg(msg_type, op_type, time)
end