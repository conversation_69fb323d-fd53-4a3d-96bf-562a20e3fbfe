local ROOT_POSITION = {
    [1] = Vector2(0, 136),
    [2] = Vector2(-30, -24),
    [3] = Vector2(10, 76)
}

local LINE_POSITION = {
    [1] = Vector2(219, 12),
    [2] = Vector2(226, 12),
    [3] = Vector2(240, 14)
}


FuBenPanelView = FuBenPanelView or BaseClass(SafeBaseView)
--仙灵圣殿【宠物本】(新)

function FuBenPanelView:InitPetView()
    self.cur_pet_fb_index = 1
    self.has_fake = TaskGuide.Instance:CheckShowFakeSlFb()
    self.is_fake = true

    self.pet_fb_list = AsyncListView.New(PetFbItemRender, self.node_list["pet_fb_list"])
    self.pet_fb_list:SetSelectCallBack(BindTool.Bind(self.OnClickPetRender, self))
    self.pet_fb_list:SetDefaultSelectIndex(1)
    self.pet_fb_reward_list = AsyncListView.New(PetFbRewardItemRender, self.node_list["pet_fb_reward_list"])

    XUI.AddClickEventListener(self.node_list["btn_pet_fb_clear"], BindTool.Bind(self.OnClickPetSaoDang, self))     --扫荡
    XUI.AddClickEventListener(self.node_list["btn_pet_fb_enter"], BindTool.Bind(self.OnClickEnterPetFb, self))       --进入
    XUI.AddClickEventListener(self.node_list["btn_pet_fb_add_count"], BindTool.Bind(self.OnClickPetAddCount, self))  --添加次数
    --XUI.AddClickEventListener(self.node_list["btn_pet_rule"], BindTool.Bind(self.OnClickPetRule, self))           --玩法
    --XUI.AddClickEventListener(self.node_list["btn_pet_right"], BindTool.Bind(self.OnClickPetRight, self))         --右边
    --XUI.AddClickEventListener(self.node_list["btn_pet_left"], BindTool.Bind(self.OnClickPetLeft, self))         --右边
    -- self.node_list["pet_fb_list"].scroll_rect.onValueChanged:AddListener(BindTool.Bind(self.OnPetSVValueChanged, self))

    self:ShowBriefIntroduction()

    self.node_list.desc_pet_fb_ads.text.text = Language.FuBenPanel.PetFBAds

    local other_cfg = FuBenPanelWGData.Instance:GetPetOtherCfg()
    self.node_list.desc_pet_fb.text.text = other_cfg and other_cfg.fb_des or ""
end

function FuBenPanelView:ShowBriefIntroduction()
    for i = 2, 9 do
        if Language.FuBenPanel.PetShuoming[i] then
            self.node_list["txt_pet_fb_info"..i]:SetActive(true)          
            self.node_list["txt_pet_fb_info"..i].text.text = Language.FuBenPanel.PetShuoming[i]
        else
            self.node_list["txt_pet_fb_info"..i]:SetActive(false)
        end
    end
end

-- function FuBenPanelView:OnPetSVValueChanged(position)
--     if position then
--         self.node_list["arrow_left"]:SetActive(position.x > 0.01)
--         self.node_list["arrow_right"]:SetActive(position.x < 0.99)
--     end
-- end

function FuBenPanelView:DeletePetFuBenPanelView()
     if self.pet_fb_list then
        self.pet_fb_list:DeleteMe()
        self.pet_fb_list = nil
    end

    if self.pet_fb_reward_list then
        self.pet_fb_reward_list:DeleteMe()
        self.pet_fb_reward_list = nil
    end

    if self.role_xiannv_model then
        self.role_xiannv_model:DeleteMe()
        self.role_xiannv_model = nil
    end

    if self.enter_pet_alert then
        self.enter_pet_alert:DeleteMe()
        self.enter_pet_alert = nil
    end
    self.pet_is_rolling = nil
    self.old_select_pet_fb_index = nil
    self.cur_pet_fb_index = 1
    self.has_fake = true
    self.is_fake = true
    self.pet_model = nil
end

function FuBenPanelView:OnFlushPetView(param_t)
    self:_InternalFlushPetList()
    --self:FlushChongWuDesc()
    -- for i=1,5 do
    --     self.node_list["pet_shuoming"..i].text.text = Language.FuBenPanel.PetShuoming[i]
    -- end
end

-- function FuBenPanelView:FlushChongWuDesc()
--     local other_cfg = FuBenPanelWGData.Instance:GetPetOtherCfg()
--     if other_cfg.fb_des then
--         self.node_list["chong_wudesc"].text.text = other_cfg.fb_des
--     end
-- end

function FuBenPanelView:_SetPetSaoDangBtn()
    --扫荡按钮
    local main_vo = GameVoManager.Instance:GetMainRoleVo()
    local limit_level = FuBenPanelWGData.Instance:GetPetSaoDangLevelLimit(FUBEN_TYPE.FBCT_PETBEN)
    local star_num = FuBenPanelWGData.Instance:SetPetStarDisplay(self.cur_pet_fb_index, self.has_fake)
    if self.is_fake then
        star_num = 0
    end
    --self.node_list["btn_tianshen_saodang"]
    if limit_level <= main_vo.level then
        -- if star_num < 3 then
            --XUI.SetButtonEnabled(self.node_list["btn_pet_saodang"], false)
            --self.node_list["btn_pet_saodang_text"].text.text = Language.FuBenPanel.TianShenSaoDangText_Can
        --  else
            -- XUI.SetButtonEnabled(self.node_list["btn_pet_saodang"], true)
        --     self.node_list["btn_pet_saodang_text"].text.text = Language.FuBenPanel.TianShenSaoDangText_Can
        --  end
    end

        -- 扫荡按钮显隐逻辑
    -- local show_level = FuBenPanelWGData.Instance:GetPetSaoDangShowLevelLimit()
    local cfg = FuBenWGData.Instance:GetSaoDangCfg(FUBEN_TYPE.FBCT_PETBEN)
    if cfg then
        self.node_list.btn_pet_fb_clear:SetActive(main_vo.level >= cfg.pre_show_level)
    else
        self.node_list.btn_pet_fb_clear:SetActive(false)
    end
end

function FuBenPanelView:_SetPetList()
    if not self:IsOpen() or not self:IsLoadedIndex(TabIndex.fubenpanel_pet) then
        return
    end
    if self.pet_fb_list then
        local data_list = FuBenPanelWGData.Instance:GetPetFbDataList()
        self.pet_fb_list:SetDataList(data_list)
    end

    self:_SetPetSaoDangBtn()
end

function FuBenPanelView:_InternalFlushPetList()
    self:_SetPetList()
    local max_layer = FuBenPanelWGData.Instance:GetPetMaxLayerCount()
    local cur_layer = FuBenPanelWGData.Instance:GetPetCurLayer()
    local data_list = FuBenPanelWGData.Instance:GetPetFbDataList()
    local show_layer = 0
    if data_list ~= nil then
        local main_vo = GameVoManager.Instance:GetMainRoleVo()
        for k,v in pairs(data_list) do
            if v.cfg.role_level <= main_vo.level and v.cfg.level <= cur_layer then
                show_layer = v.cfg.level
            end
        end
    end

    local jump_layer = MathClamp(show_layer + 1, 1, max_layer)
    --self:GoPetListToIndex(jump_layer))
    self.pet_fb_list:JumpToIndex(jump_layer)
end

function FuBenPanelView:OnClickPetRight()
    --local max_layer = FuBenPanelWGData.Instance:GetPetMaxLayerCount()
    --local cur_layer = FuBenPanelWGData.Instance:GetPetCurLayer()
    local data_list = FuBenPanelWGData.Instance:GetPetFbDataList()
    if self.cur_pet_fb_index >= #data_list then
        return
    end
    --self.pet_fb_list:JumpToIndex(self.cur_pet_fb_index + 1)
    self:GoPetListToIndex(self.cur_pet_fb_index + 1)
end

function FuBenPanelView:OnClickPetLeft()
    --local data_list = FuBenPanelWGData.Instance:GetPetFbDataList()
    if self.cur_pet_fb_index <= 1 then
        return
    end
   -- self.pet_fb_list:JumpToIndex(self.cur_pet_fb_index - 1)
    self:GoPetListToIndex(self.cur_pet_fb_index - 1)
end

function FuBenPanelView:GoPetListToIndex(index)
    if self.pet_is_rolling or self.pet_fb_list:GetDataList() == nil then
        return
    end

    self.pet_is_rolling = true
    self.pet_fb_list:SelectIndex(index)
    local data_list = FuBenPanelWGData.Instance:GetPetFbDataList()
    local max_cell = #data_list
    local cell_width = 120
    local space = 0
    local list_width = 393
    local roll_x = (index -2) * 120 
    roll_x = roll_x > 0 and roll_x or 0
    if max_cell <= 3 then
        roll_x = 0
    end

    if index + 1 >= max_cell then
        roll_x = cell_width * max_cell - list_width
    end

    roll_x = roll_x * -1

    local rect_transform = self.node_list["pet_fb_list"].scroll_rect.content:GetComponent(typeof(UnityEngine.RectTransform))
    local list_animal = rect_transform:DOAnchorPosX(roll_x, 0.3)
    list_animal:SetEase(DG.Tweening.Ease.OutCubic)
    GlobalTimerQuest:AddDelayTimer(function ()
        self.pet_is_rolling = false
    end,0.3)
end


function FuBenPanelView:OnClickPetRender(cell, index)
    local data = cell:GetData()
    if self.old_select_pet_fb_index == index or not data then
        return
    end

    self.has_fake = data.has_fake
    self.is_fake = data.is_fake
    self.cur_pet_fb_index = index
    self:_FlushPet()
    self.old_select_pet_fb_index = index
end

function FuBenPanelView:OnFlushPetBuyTimes(param_t)
    self:_InternalFlushPetOther()
end
function FuBenPanelView:OnFlushPetEnterTimes(param_t)
    self:_InternalFlushPetOther()
end

function FuBenPanelView:_FlushPet()
    self:_InternalFlushPetRewardCell()
    self:_InternalFlushPetOther()
end

--刷新宠物副本奖励
function FuBenPanelView:_InternalFlushPetRewardCell()
    local drop_data_list
    if self.is_fake then
        drop_data_list =  FuBenPanelWGData.Instance:GetFakePetDropList()
    else
        drop_data_list = FuBenPanelWGData.Instance:GetPetDropList(self.cur_pet_fb_index - 1)
    end
    self.pet_fb_reward_list:SetDataList(drop_data_list)
end

--刷新其他
function FuBenPanelView:_InternalFlushPetOther()
    --每天免费次数
    local pet_info = FuBenPanelWGData.Instance:GetPetAllInfo()
	local other_cfg = FuBenPanelWGData.Instance:GetPetOtherCfg()
    local level_cfg = FuBenPanelWGData.Instance:GetPetFBCfgByLevel(self.cur_pet_fb_index - 1)
    self.enter_cw_times = other_cfg.free_times + pet_info.buy_times - pet_info.day_times
    local str = self.enter_cw_times.."/".. other_cfg.free_times + pet_info.buy_times
    self.node_list["txt_pet_fb_count"].text.text = str
	--self.node_list["lbl_pet_count"].text.text = ToColorStr(str,self.enter_cw_times > 0 and COLOR3B.WHITE or COLOR3B.D_RED)

    local role_level = RoleWGData.Instance:GetAttr('level')
    local color = level_cfg.role_level <= role_level and COLOR3B.GREEN or COLOR3B.C10
    self.node_list["txt_pet_fb_info1"].text.text = string.format(Language.FuBenPanel.PetShuoming[1], color, level_cfg.role_level)
    self:_SetPetSaoDangBtn()
    -- local layer = self.cur_pet_fb_index -1
    -- local level_cfg = FuBenPanelWGData.Instance:GetPetItemByLayer(layer, self.is_fake)
    --self.node_list.pet_xiannv_name.text.text = level_cfg.name

    --首次通关tips
    local is_pass = FuBenPanelWGData.Instance:SetPetStarDisplay(self.cur_pet_fb_index) > 0 --当前关卡是否已经通关过一次
    self.node_list["first_pass_tips"]:CustomSetActive(not is_pass)
end

--点击扫荡
function FuBenPanelView:OnClickPetSaoDang()
    if self.is_fake then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CannotSweep)
        return
    end

    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.PET_FB then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Dungeon.ShaodangTips)
        return
    end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	local cfg = FuBenWGData.Instance:GetSaoDangCfg(FUBEN_TYPE.FBCT_PETBEN)
	local limit_level = cfg.level_limit
    local index = self.cur_pet_fb_index
	local star_num = FuBenPanelWGData.Instance:SetPetStarDisplay(index)
    local can_enter_times = FuBenPanelWGData.Instance:GetPetCanEnterTimes()
	if role_level >= limit_level then
		if star_num <= 2 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CannotSweep)
			return
		end

		if can_enter_times > 0 then
            FuBenWGCtrl.Instance:ShowSaoDangPanel(FUBEN_TYPE.FBCT_PETBEN, index, nil, nil)
			self.is_saodang_mark = true
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.HaveNotEnterTimes)
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FuBenPanel.FunNotOpen, limit_level))
	end
end

--点击进入宠物本
function FuBenPanelView:OnClickEnterPetFb(is_fake)
    if not FuBenWGCtrl.Instance:GetCanEnterFuBen() then
		return
	end
    if not is_fake then
        local layer = self.cur_pet_fb_index - 1
        local main_vo = GameVoManager.Instance:GetMainRoleVo()
        local need_level = FuBenPanelWGData.Instance:GetPetFbNeedLevel(layer)
        if need_level > main_vo.level then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.TianShenEnterTip1)
            return
        end

        local pass_layer = FuBenPanelWGData.Instance:GetPetPassLayer()
        if pass_layer then
            if layer - 1 > pass_layer then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.TianShenPassTip)
                return
            end
        end

        local can_enter_times = FuBenPanelWGData.Instance:GetPetCanEnterTimes()
        if can_enter_times <= 0 then
            self:OnClickPetAddCount()
        else
            FuBenWGCtrl.Instance:SendPETFb(layer)
        end
    else
        FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.FBCT_FAKE_PETBEN, 0)
    end
end

--点击增加次数
function FuBenPanelView:OnClickPetAddCount()
    --FuBenPanelWGCtrl.Instance:OpenPetBuy()
    FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.FBCT_PETBEN)
end

function FuBenPanelView:OnClickPetRule()
    local role_tip = RuleTip.Instance
    role_tip:SetTitle(Language.FuBenPanel.RuleTitle[13])
    role_tip:SetContent(Language.FuBen.Pet_Des)
end

----------------------PetFbItemRender------------------------
PetFbItemRender = PetFbItemRender or BaseClass(BaseRender)
function PetFbItemRender:__init()
    self.locked = false
end
function PetFbItemRender:__delete()
    self.locked = false
end

function PetFbItemRender:OnFlush()
    --名字
    self.node_list["fb_name"].text.text = self.data.cfg.name
    self.node_list["hl_fb_name"].text.text = self.data.cfg.name

    --星级
    local main_vo = GameVoManager.Instance:GetMainRoleVo()

    self.show_pass = false
    if self.data.is_fake then
        --伪副本显示0星
        --self.node_list["desc"]:CustomSetActive(false)
        self.node_list["star_container"]:CustomSetActive(true)
        self.node_list.desc_unlock:CustomSetActive(false)

        for i = 1, 3 do
            self.node_list["star"..i]:CustomSetActive(false)
        end

    --等级条件不到,并且没有通关直接显示多少级开启Tips
    elseif self.data.cfg.role_level > main_vo.level then
        local pass_layer = FuBenPanelWGData.Instance:GetPetPassLayer() 
        local cur_layer = pass_layer + 1 --服务器下发从0开始，这里 + 1 和 self.index 判断
        -- if self.index > cur_layer then 
            self.node_list["star_container"]:CustomSetActive(false)
            --self.node_list["desc"]:CustomSetActive(true)
            --self.node_list["lock"]:CustomSetActive(true)
            self.locked = true
            self.node_list.desc_unlock:CustomSetActive(true)
           -- XUI.SetGraphicGrey(self.node_list.chahua, true)
            -- local str = RoleWGData.GetLevelString(self.data.cfg.role_level)
            --self.node_list["desc"].text.text = string.format(Language.FuBenPanel.MountLevelAstrict, str)
        -- else
             --通过了直接显示所有星星
        --     self.node_list["desc"]:CustomSetActive(false)
        --     self.node_list["lock"]:CustomSetActive(false)
        --     --XUI.SetGraphicGrey(self.node_list.chahua, false)
        --     self.node_list["star_container"]:CustomSetActive(true)
        --     for i = 1, 3 do
        --         self.node_list["star"..i]:CustomSetActive(true)
        --     end
        -- end
    else
        --local pass_layer = FuBenPanelWGData.Instance:GetTianShenPassLayer()
        local pass_layer = FuBenPanelWGData.Instance:GetPetPassLayer()
        local cur_layer = pass_layer + 1
        if self.index > cur_layer then
            if self.index == pass_layer + 2 and FuBenPanelWGData.Instance:SetPetStarDisplay(cur_layer) == 3 then --下一关要+2, 并且当前关是3星
                --self.node_list["desc"]:CustomSetActive(false)
                --self.node_list["lock"]:CustomSetActive(false)
                self.locked = false
             --   XUI.SetGraphicGrey(self.node_list.chahua, false)
                self.node_list["star_container"]:CustomSetActive(true)
                self.node_list.desc_unlock:CustomSetActive(false)
                --显示对应星数
                local star_count = FuBenPanelWGData.Instance:SetPetStarDisplay(self.index)
                for i = 1, 3 do
                    self.node_list["star_bg"..i]:CustomSetActive(i > star_count)
                    self.node_list["star"..i]:CustomSetActive(i <= star_count)
                end
            else
                --self.node_list["desc"].text.text = Language.FuBenPanel.MountTianShenPassTip
                --self.node_list["desc"]:CustomSetActive(true)
                --self.node_list["lock"]:CustomSetActive(true)
                self.locked = true
                --XUI.SetGraphicGrey(self.node_list.chahua, true)
                self.node_list["star_container"]:CustomSetActive(false)
                self.node_list.desc_unlock:CustomSetActive(true)
            end
        elseif self.index == cur_layer then
             --self.node_list["desc"]:CustomSetActive(false)
            --self.node_list["lock"]:CustomSetActive(false)
            self.locked = false
            self.show_pass = FuBenPanelWGData.Instance:SetPetStarDisplay(cur_layer) == 3
           -- XUI.SetGraphicGrey(self.node_list.chahua, false)
            self.node_list["star_container"]:CustomSetActive(true)
            self.node_list.desc_unlock:CustomSetActive(false)
            --显示对应星数
            local star_count = FuBenPanelWGData.Instance:SetPetStarDisplay(self.index)
            for i = 1, 3 do
                self.node_list["star_bg"..i]:CustomSetActive(i > star_count)
                self.node_list["star"..i]:CustomSetActive(i <= star_count)
            end
        else
             --通过了直接显示所有星星
            --self.node_list["desc"]:CustomSetActive(false)
            --self.node_list["lock"]:CustomSetActive(false)
            self.locked = false
            self.show_pass = true
           -- XUI.SetGraphicGrey(self.node_list.chahua, false)
            self.node_list["star_container"]:CustomSetActive(true)
            self.node_list.desc_unlock:CustomSetActive(false)
            for i = 1, 3 do
                self.node_list["star_bg"..i]:CustomSetActive(false)
                self.node_list["star"..i]:CustomSetActive(true)
            end
        end
    end

    --local real_index = index % 5
    --real_index = real_index == 0 and 5 or real_index
   -- local xiannv_config = FuBenPanelWGData.Instance:GetPetFbXianNvCfg(self.index)
    --local b,a = ResPath.GetFuBenPanel("pet_item_bg" .. self.index)
    
    --头像
    --if not IsEmptyTable(self.data.cfg) and self.data.cfg.big_icon then
    --    local b,a = ResPath.GetBossIcon("wrod_boss_" .. self.data.cfg.big_icon)
    --    self.node_list["chahua"].image:LoadSprite(b,a, function()
    --        XUI.ImageSetNativeSize(self.node_list["chahua"])
    --    end)
    --end
    self:SetItemShow()
    --XUI.SetGraphicGrey(self.node_list.img_bg, self.locked)
    XUI.SetGraphicGrey(self.node_list.img_fb_icon, self.locked)
    --XUI.SetGraphicGrey(self.node_list.fb_name, self.locked)
    --self.node_list["suotou"]:SetActive(self.locked)
    self.node_list.line_hl:SetActive(self.show_pass)
end

function PetFbItemRender:OnSelectChange(isSelect)
    self.node_list.img_bg:SetActive(not isSelect)
    self.node_list.img_bg_hl:SetActive(isSelect)
    -- self.node_list.img_name_bg:SetActive(not isSelect)
    -- self.node_list.img_name_bg_hl:SetActive(isSelect)

    -- if self.data then
    --     local color = isSelect and COLOR3B.GLOD_TITLE or COLOR3B.DEFAULT
    --     self.node_list.fb_name.text.text = ToColorStr(self.data.cfg.name, color)
    -- end
end

function PetFbItemRender:SetItemShow()
    local count = (self.index - 1) % 3 + 1

    -- local bundle, asset = ResPath.GetRawImagesPNG("a3_fb_jz_" .. count)
    -- self.node_list["img_fb_icon"].raw_image:LoadSprite(bundle, asset, function()
    --     self.node_list.img_fb_icon.raw_image:SetNativeSize()
    -- end)

    local bundle, asset = ResPath.GetRawImagesPNG(string.format("a3_fb_tg_%s_1", count))
    self.node_list["line_normal"].raw_image:LoadSprite(bundle, asset, function()
        self.node_list["line_normal"].raw_image:SetNativeSize()
    end)

    local bundle, asset = ResPath.GetRawImagesPNG(string.format("a3_fb_tg_%s_2", count))
    self.node_list["line_hl"].raw_image:LoadSprite(bundle, asset, function()
        self.node_list["line_hl"].raw_image:SetNativeSize()
    end)

    -- self.node_list.root.rect.anchoredPosition = ROOT_POSITION[count]
    -- self.node_list.line_root.rect.anchoredPosition = LINE_POSITION[count]
end
---------------------PetFbRewardItemRender------------------------
PetFbRewardItemRender = PetFbRewardItemRender or BaseClass(BaseRender)
function PetFbRewardItemRender:LoadCallBack()
    self.base_cell = ItemCell.New(self.node_list["pos"])
    self.base_cell:SetIsShowTips(true)
end
function PetFbRewardItemRender:__delete()
    if self.base_cell then
        self.base_cell:DeleteMe()
        self.base_cell = nil
    end
end
function PetFbRewardItemRender:OnFlush()
    self.base_cell:SetData(self.data)
    --local new_flag = FuBenPanelWGData.Instance:GetIsNew(self.data.item_id)
    self.node_list.three_flag:SetActive(self.data.is_three_must_drop == true)
    --self.node_list.new_flag:SetActive(false)
end