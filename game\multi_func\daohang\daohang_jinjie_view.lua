local ONE_KEY_UP_DELAY = 0.5

function MultiFunctionView:ShowDaoHangJinJieCallBack()
    self:RightPanleShowTween(self.node_list.jinjie_right_tween_root, self.node_list.jinjie_mid)
    self.node_list.btn_jinjie_onekey_text.text.text = Language.Charm.DaoHangJinJieOneKeyUpDesc
end

function MultiFunctionView:LoadDaoHangJinJieCallBack()
    if not self.jinjie_target_item then
        self.jinjie_target_item = ItemCell.New(self.node_list.jinjie_target_item)
        self.jinjie_target_item:SetItemTipFrom(ItemTip.FROM_DAOHANG_EQUIP)
    end

    if not self.jinjie_equip_list then
        self.jinjie_equip_list = AsyncListView.New(DaoHangJinJieEquipItemRender, self.node_list.jinjie_equip_list)
        self.jinjie_equip_list:SetSelectCallBack(BindTool.Bind(self.OnSelectJinJieEquip<PERSON>tem<PERSON><PERSON><PERSON>, self))
    end

    if not self.jinjie_attr_list then
        self.jinjie_attr_list = AsyncListView.New(DaoHangJinJieAttrItemRender, self.node_list.jinjie_attr_list)
    end

    if not self.jinjie_cost_item then
        self.jinjie_cost_item = ItemCell.New(self.node_list.jinjie_cost_item)
    end

    self.daohang_jinjie_select = -1
    self.daohang_jinjie_select_slot = -1
	self.daohang_jinjie_equip_data = {}

    XUI.AddClickEventListener(self.node_list.btn_jinjie, BindTool.Bind(self.OnClickJinJieBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_jinjie_onekey, BindTool.Bind(self.OnClickJinJieOneKay, self))
end

function MultiFunctionView:ReleaseJinJieCallBack()
    if self.jinjie_target_item then
        self.jinjie_target_item:DeleteMe()
        self.jinjie_target_item = nil
    end

    if self.jinjie_equip_list then
        self.jinjie_equip_list:DeleteMe()
        self.jinjie_equip_list = nil
    end

    if self.jinjie_attr_list then
        self.jinjie_attr_list:DeleteMe()
        self.jinjie_attr_list = nil
    end

    if self.jinjie_cost_item then
        self.jinjie_cost_item:DeleteMe()
        self.jinjie_cost_item = nil
    end
end

function MultiFunctionView:DaoHangJinJieShowIndexChange()
    self:DaoHangJinJieOneKeyStop()
end

function MultiFunctionView:OnFlushDaoHangJinJie()
    local data_list = MultiFunctionWGData.Instance:GetDaoHangEquipedEquip()
    self.jinjie_equip_list:SetDataList(data_list)

    local select = self:GetDaoHangJinJieSelect(data_list)
    if self.daohang_jinjie_select ~= select then
        self:DaoHangJinJieOneKeyStop()
    end

    self.jinjie_equip_list:JumpToIndex(select)

    local cap = MultiFunctionWGData.Instance:GetDaoHangJinJieCap()
    self.node_list.jinjie_cap_value.text.text = cap or 0
end

function MultiFunctionView:DaoHangJinJieOneKeyStop()
    self.node_list.btn_jinjie_onekey_text.text.text = Language.Charm.DaoHangJinJieOneKeyUpDesc
    MultiFunctionWGData.Instance:SetDaoHangJinJieAutoUpState(false)
end

function MultiFunctionView:GetDaoHangJinJieSelect(data_list)
    if self.daohang_jinjie_select > 0 and self.daohang_jinjie_select_slot >= 0 then
        if MultiFunctionWGData.Instance:GetDaoHangJinJieEquipRemindByslot(self.daohang_jinjie_select_slot) then
            return self.daohang_jinjie_select
        end
    end

    for k, v in pairs(data_list) do
        if MultiFunctionWGData.Instance:GetDaoHangJinJieEquipRemindByslot(v.slot) then
            return k
        end
    end
 
    self.daohang_jinjie_select = self.daohang_jinjie_select > 0 and self.daohang_jinjie_select or 1
    return self.daohang_jinjie_select
end

function MultiFunctionView:OnSelectJinJieEquipItemHandler(item)
	if nil == item or nil == item.data then
		return
	end

    local data = item.data
    self.jinjie_target_item:SetData(data)
    self.daohang_jinjie_select_slot = data.slot
    self.daohang_jinjie_select = item.index
    self.daohang_jinjie_equip_data = data

    self:FlushJinJieMid()
end

function MultiFunctionView:FlushJinJieMid()
    if IsEmptyTable(self.daohang_jinjie_equip_data) then
        return
    end

    local slot = self.daohang_jinjie_equip_data.slot
    local cur_grade = self.daohang_jinjie_equip_data.grade
    local next_grade = cur_grade + 1
    local cur_grade_cfg = MultiFunctionWGData.Instance:GetDaoHangSlotAdvanceCfgBySlotAndLevel(slot, cur_grade)
    local next_grade_cfg = MultiFunctionWGData.Instance:GetDaoHangSlotAdvanceCfgBySlotAndLevel(slot, next_grade)
    local not_max_grade = not IsEmptyTable(next_grade_cfg)

    self.node_list.jinjie_arrow:CustomSetActive(not_max_grade)
    self.node_list.jinjie_next_level_bg:CustomSetActive(not_max_grade)
    self.node_list.jinjie_current_level.text.text = string.format(Language.Common.Order, cur_grade)
    self.node_list.jinjie_next_level.text.text = string.format(Language.Common.Order, next_grade)

    local attr_data = MultiFunctionWGData.Instance:MergeTwoAttrTable(cur_grade_cfg, next_grade_cfg, 5, true)
    self.jinjie_attr_list:SetDataList(attr_data)  -- 369    737    

    self.node_list.jinjie_max_grade_flag:CustomSetActive(not not_max_grade)
    self.node_list.jinjie_cost_panel:CustomSetActive(not_max_grade)

    local cost_enough = false
    if not_max_grade then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_grade_cfg.cost_item_id)
        local enough = item_num >= cur_grade_cfg.cost_item_num
        self.jinjie_cost_item:SetFlushCallBack(function ()
            local right_text = ToColorStr(item_num .. "/" .. cur_grade_cfg.cost_item_num, enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
            self.jinjie_cost_item:SetRightBottomColorText(right_text)
            self.jinjie_cost_item:SetRightBottomTextVisible(true)
        end)
        self.jinjie_cost_item:SetData({item_id = cur_grade_cfg.cost_item_id})

		local role_gold = GameVoManager.Instance:GetMainRoleVo().gold or 0
        local bind_gold_enough = cur_grade_cfg.cost_gold <= role_gold
		local coin_str_color = bind_gold_enough and COLOR3B.D_GREEN or COLOR3B.D_RED
		local cur_coin_str = ToColorStr(CommonDataManager.ConverExpByThousand(role_gold), coin_str_color)
		local color_str = cur_coin_str .. "/" .. CommonDataManager.ConverExpByThousand(cur_grade_cfg.cost_gold)
		self.node_list.jinjie_xianyu_desc.text.text = color_str

        cost_enough = enough and bind_gold_enough
    end

    self.node_list.btn_jinjie_remind:CustomSetActive(cost_enough)
    self.node_list.btn_jinjie_onekey_remind:CustomSetActive(cost_enough)
end

function MultiFunctionView:IsDaoHangJinJieSlotCanUp(need_show_tip)
    if IsEmptyTable(self.daohang_jinjie_equip_data) then
        return false
    end

    local cur_grade = self.daohang_jinjie_equip_data.grade
    local cur_grade_cfg = MultiFunctionWGData.Instance:GetDaoHangSlotAdvanceCfgBySlotAndLevel(self.daohang_jinjie_select_slot, cur_grade)
    local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_grade_cfg.cost_item_id)

    if item_num < cur_grade_cfg.cost_item_num then
        if need_show_tip then
            TipWGCtrl.Instance:OpenItem({item_id = cur_grade_cfg.cost_item_id})
        end

        return false
    end

    local role_bind_gold = GameVoManager.Instance:GetMainRoleVo().gold or 0
    if cur_grade_cfg.cost_gold > role_bind_gold then
        if need_show_tip then
            TipWGCtrl.Instance:OpenItem({item_id = COMMON_CONSTS.VIRTUAL_ITEM_GOLD})
        end

        return false
    end

    return true
end

function MultiFunctionView:OnClickJinJieBtn()
    if self:IsDaoHangJinJieSlotCanUp(true) then
        MultiFunctionWGCtrl.Instance:OnCSTaoistOperate(TAOIST_OPERATE_TYPE.SLOT_ADVANCE, self.daohang_jinjie_select_slot)
    end
end

function MultiFunctionView:OnClickJinJieOneKay()
    if self:IsDaoHangJinJieSlotCanUp(true) then
        if MultiFunctionWGData.Instance:GetDaoHangJinJieAutoUpState() then
            self:DaoHangJinJieOneKeyStop()
        else
            MultiFunctionWGData.Instance:SetDaoHangJinJieAutoUpState(true)
            MultiFunctionWGCtrl.Instance:OnCSTaoistOperate(TAOIST_OPERATE_TYPE.SLOT_ADVANCE, self.daohang_jinjie_select_slot)
            self.node_list.btn_jinjie_onekey_text.text.text = Language.Charm.DaoHangJinJieStopDesc
        end
    end
end

function MultiFunctionView:StartUpGradeAni(slot)
    TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengjie, is_success = true, pos = Vector2(0, 0)})

    if self:IsDaoHangJinJieSlotCanUp(false) then
        if MultiFunctionWGData.Instance:GetDaoHangJinJieAutoUpState() then
            ReDelayCall(self, function()
                MultiFunctionWGCtrl.Instance:OnCSTaoistOperate(TAOIST_OPERATE_TYPE.SLOT_ADVANCE, slot)
             end, ONE_KEY_UP_DELAY, "daohang_jinjie_one_key")
        end
    else
        self:DaoHangJinJieOneKeyStop()
    end
end

-------------------------------------------DaoHangJinJieEquipItemRender---------------------------------------
DaoHangJinJieEquipItemRender = DaoHangJinJieEquipItemRender or BaseClass(BaseRender)

function DaoHangJinJieEquipItemRender:LoadCallBack()
    if not self.item then
        self.item = ItemCell.New(self.node_list.item)
        self.item:SetItemTipFrom(ItemTip.FROM_DAOHANG_EQUIP)
    end
end

function DaoHangJinJieEquipItemRender:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function DaoHangJinJieEquipItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    local name = item_cfg and item_cfg.name or ""
    local remind = MultiFunctionWGData.Instance:GetDaoHangJinJieEquipRemindByslot(self.data.slot)

    self.item:SetData(self.data)
    self.node_list.text.text.text = name
    self.node_list.text_hl.text.text = name
    self.node_list.remind:CustomSetActive(remind)
    self.node_list.value.text.text = string.format(Language.Charm.DaoHangJinJieItemValue, self.data.grade)
end

function DaoHangJinJieEquipItemRender:OnSelectChange(is_select)
    self.node_list["bg"]:CustomSetActive(not is_select)
    self.node_list["bg_hl"]:CustomSetActive(is_select)
end

------------------------------------------DaoHangJinJieAttrItemRender-------------------------------------------
DaoHangJinJieAttrItemRender = DaoHangJinJieAttrItemRender or BaseClass(BaseRender)

function DaoHangJinJieAttrItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.attr_name.text.text = self.data.attr_name or ""
    self.node_list.attr_value.text.text = self.data.attr_value or ""
    local can_up = self.data.add_attr_value > 0
    self.node_list.attr_arrow:CustomSetActive(can_up)
    local add_str = can_up and self.data.add_value_str or ""
    self.node_list.attr_add_value.text.text = add_str
end