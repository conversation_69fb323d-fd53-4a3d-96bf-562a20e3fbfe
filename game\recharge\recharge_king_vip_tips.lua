RechargeKingVipTips = RechargeKingVipTips or BaseClass(SafeBaseView)

function RechargeKingVipTips:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/recharge_ui_prefab", "layout_recharge_king_vip_tips")
end

function RechargeKingVipTips:LoadCallBack()
	self.page_max = 3
	self.cur_page_idx = 1

	if not self.add_item_list then
		self.add_item_list = AsyncListView.New(RechargeKingVipTipsItem, self.node_list.content)
	end

	XUI.AddClickEventListener(self.node_list.left_btn, BindTool.Bind(self.ChangePage, self, -1))
	XUI.AddClickEventListener(self.node_list.right_btn, BindTool.Bind(self.ChangePage, self, 1))
end

function RechargeKingVipTips:ReleaseCallBack()
	if self.add_item_list then
		self.add_item_list:DeleteMe()
		self.add_item_list = nil
	end
end

function RechargeKingVipTips:OnFlush()
	local data_list = RechargeWGData.Instance:GetKingVipClientCfg()
	if not IsEmptyTable(data_list) then
		self.add_item_list:SetDataList(data_list)
	end

	self.node_list.page_text.text.text = string.format(Language.KingVip.page_text, self.cur_page_idx, self.page_max)
end

function RechargeKingVipTips:ChangePage(value)
	self.cur_page_idx = self.cur_page_idx + value

	if self.cur_page_idx > 3 then
		self.cur_page_idx = 3
		return
	elseif self.cur_page_idx < 1 then
		self.cur_page_idx = 1
		return
	end

	self.node_list.left_btn_image1:SetActive(self.cur_page_idx <= 1)
	self.node_list.left_btn_image2:SetActive(self.cur_page_idx > 1)
	self.node_list.right_btn_image1:SetActive(self.cur_page_idx >= 3)
	self.node_list.right_btn_image2:SetActive(self.cur_page_idx < 3)

	self.node_list.page_text.text.text = string.format(Language.KingVip.page_text, self.cur_page_idx, self.page_max)

	if self.add_item_list then
		local data_list = RechargeWGData.Instance:GetKingVipClientCfg()
		if not IsEmptyTable(data_list) then
			self.add_item_list:SetDataList(data_list)
		end
	end
end

function RechargeKingVipTips:GetPageIdx()
	return self.cur_page_idx
end

---------------------------------RechargeKingVipTipsItem-----------------------------
RechargeKingVipTipsItem = RechargeKingVipTipsItem or BaseClass(BaseRender)

function RechargeKingVipTipsItem:ReleaseCallBack()

end

function RechargeKingVipTipsItem:LoadCallBack()
	self.cur_page_idx = 1
end

function RechargeKingVipTipsItem:OnFlush()
	if not self.data then
		return
	end

	self.cur_page_idx = RechargeWGCtrl.Instance:GetKingVipTipsPageIdx()
	local page_info = RechargeWGData.Instance:GetKingVipPageInfo(self.cur_page_idx)
	if IsEmptyTable(page_info) then
		return
	end

	local page_start = page_info.start_idx
	local page_end = page_info.end_idx

	self.node_list.bg:SetActive(self.data.vip_add_name_index % 2 == 0)

	self.node_list.add_name_text.text.text = self.data.vip_add_name

	if self.data.is_title == 1 then
		--i为预制体后缀.
		for i = 0, 10 do
			local text = ""
			local vip_level = page_start + i
			if vip_level <= page_end then
				text = ToColorStr(self.data["vip_" .. vip_level], "#272727")
			end

			--有数据则显示实际数据，无数据的位置显示为 空.
			self.node_list["value_text" .. i].text.text = text
		end
	else
		for i = 0, 10 do
			local text = ""
			local vip_level = page_start + i
			if vip_level <= page_end then
				local data_list = RechargeWGData.Instance:GetKingVipAddition(vip_level)
				if IsEmptyTable(data_list) then
					return
				end

				--特权加成从0开始，名称索引从2开始.
				local add_value = data_list["add_" .. self.data.vip_add_name_index - 2]
				add_value = math.ceil(add_value / 100)
				text = add_value .. "%"
			end
			self.node_list["value_text" .. i].text.text = text
		end
	end
end
