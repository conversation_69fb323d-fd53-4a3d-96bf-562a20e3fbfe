GuDaoFBSceneLogic = GuDaoFBSceneLogic or BaseClass(CommonFbLogic)

function GuDaoFBSceneLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
end

function GuDaoFBSceneLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
end

function GuDaoFBSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)


	-- local main_load_call_back = function ()
	
	-- end


    MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
       	MainuiWGCtrl.Instance:PlayTopButtonTween(false)
        MainuiWGCtrl.Instance:SetMianUITopButtonStateTwo(true)
		MainuiWGCtrl.Instance.view:SetBtnLevel(true)
		MainuiWGCtrl.Instance:SetFBNameState(true, Language.GuDaoJiZhan.FbName)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)

		-- MainuiWGCtrl.Instance:SetTaskActive(true)
		MainuiWGCtrl.Instance:SetTaskContents(false)
	    MainuiWGCtrl.Instance:SetOtherContents(true)
    end)

	-- BootyBayWGCtrl.In                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               stance:CloseBootybayFollowView()
	GuDaoFuBenWGCtrl.Instance:OpenGuDaoFBFollowView()
 --    ViewManager.Instance:Close(GuideModuleName.BootyBayFBReadyView)
 --    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.BOOTYBAY_READY_VIEW, 0)

end


function GuDaoFBSceneLogic:Out()
	CommonFbLogic.Out(self)
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
	-- BootyBayWGCtrl.Instance:CloseBootyBayFBView()
	ViewManager.Instance:Close(GuideModuleName.GuDaoSceneFollowView)
	MainuiWGCtrl.Instance:PlayTopButtonTween(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetTaskContents(true)
	MainuiWGCtrl.Instance:SetOtherContents(false)
 --    BootyBayWGData.Instance:SetWaBaoType(0)
 --    MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
    GlobalTimerQuest:AddDelayTimer(function ()
       GuDaoFuBenWGCtrl.Instance:TryOpenGuDaoResultView()
    end, 3)
 --    end)
 --    --切换成无目标
 --    NewTeamWGCtrl.Instance:ChangNotGoalWhenOutFromOtherFb()
end

function GuDaoFBSceneLogic:OnSceneDetailLoadComplete()
	local next_boss_fresh_time,now_boss_index = GuDaoFuBenWGData.Instance:GetBattleHrutRankInfo()
	if now_boss_index == 0 or now_boss_index == -3  then
		local call_back = function ()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end
		local boss_born_x,boss_born_y = GuDaoFuBenWGData.Instance:GetGuaJiPos()
		if boss_born_y and boss_born_x then
			GlobalTimerQuest:AddDelayTimer(function ()
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
				GuajiWGCtrl.Instance:SetMoveToPosCallBack(call_back)
				GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), boss_born_x, boss_born_y, 0)
			end,0)	
		else
			call_back()
		end
	end
end