﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class ChangeSkinWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(ChangeSkin), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>RegFunction("ChangeBody", ChangeBody);
		<PERSON><PERSON>RegFunction("ChangeFace", ChangeFace);
		<PERSON><PERSON>RegFunction("ChangeEyeball", ChangeEyeball);
		<PERSON><PERSON>Function("ChangeHair", ChangeHair);
		<PERSON><PERSON>RegFunction("ChangeFaceAndEyeball", ChangeFaceAndEyeball);
		<PERSON><PERSON>Function("ChangeSkinPart", ChangeSkinPart);
		<PERSON><PERSON>RegFunction("ChangeBodyMat", ChangeBodyMat);
		<PERSON><PERSON>RegFunction("ChangeBodyMats", ChangeBodyMats);
		<PERSON><PERSON>RegFunction("ChangeHairMat", ChangeHairMat);
		<PERSON>.RegFunction("ChangeHairMats", ChangeHairMats);
		<PERSON><PERSON>RegFunction("ChangeFaceMat", ChangeFaceMat);
		<PERSON><PERSON>RegFunction("ChangeFaceMats", ChangeFaceMats);
		<PERSON><PERSON>RegFunction("BatchSetPartDyeColor", BatchSetPartDyeColor);
		<PERSON><PERSON>RegFunction("BatchReSetPartDyeColor", BatchReSetPartDyeColor);
		L.RegFunction("ChangePartDyeColor", ChangePartDyeColor);
		L.RegFunction("ResetPartDyeColor", ResetPartDyeColor);
		L.RegFunction("ResetPartHairDyeColor", ResetPartHairDyeColor);
		L.RegFunction("IsArtMode", IsArtMode);
		L.RegFunction("ClearBoneArray", ClearBoneArray);
		L.RegFunction("SetHeadCustomization", SetHeadCustomization);
		L.RegFunction("BatchSetHeadCustomization", BatchSetHeadCustomization);
		L.RegFunction("GetBlendShapeValue", GetBlendShapeValue);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ChangeBody(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ChangeSkin obj = (ChangeSkin)ToLua.CheckObject<ChangeSkin>(L, 1);
			ActorRender arg0 = (ActorRender)ToLua.CheckObject<ActorRender>(L, 2);
			obj.ChangeBody(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ChangeFace(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ChangeSkin obj = (ChangeSkin)ToLua.CheckObject<ChangeSkin>(L, 1);
			ActorRender arg0 = (ActorRender)ToLua.CheckObject<ActorRender>(L, 2);
			obj.ChangeFace(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ChangeEyeball(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ChangeSkin obj = (ChangeSkin)ToLua.CheckObject<ChangeSkin>(L, 1);
			ActorRender arg0 = (ActorRender)ToLua.CheckObject<ActorRender>(L, 2);
			obj.ChangeEyeball(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ChangeHair(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ChangeSkin obj = (ChangeSkin)ToLua.CheckObject<ChangeSkin>(L, 1);
			ActorRender arg0 = (ActorRender)ToLua.CheckObject<ActorRender>(L, 2);
			obj.ChangeHair(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ChangeFaceAndEyeball(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ChangeSkin obj = (ChangeSkin)ToLua.CheckObject<ChangeSkin>(L, 1);
			ActorRender arg0 = (ActorRender)ToLua.CheckObject<ActorRender>(L, 2);
			obj.ChangeFaceAndEyeball(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ChangeSkinPart(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			ChangeSkin obj = (ChangeSkin)ToLua.CheckObject<ChangeSkin>(L, 1);
			ChangeSkin.SkinType arg0 = (ChangeSkin.SkinType)ToLua.CheckObject(L, 2, typeof(ChangeSkin.SkinType));
			ActorRender arg1 = (ActorRender)ToLua.CheckObject<ActorRender>(L, 3);
			obj.ChangeSkinPart(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ChangeBodyMat(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ChangeSkin obj = (ChangeSkin)ToLua.CheckObject<ChangeSkin>(L, 1);
			UnityEngine.Material arg0 = (UnityEngine.Material)ToLua.CheckObject<UnityEngine.Material>(L, 2);
			obj.ChangeBodyMat(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ChangeBodyMats(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ChangeSkin obj = (ChangeSkin)ToLua.CheckObject<ChangeSkin>(L, 1);
			UnityEngine.Material[] arg0 = ToLua.CheckObjectArray<UnityEngine.Material>(L, 2);
			obj.ChangeBodyMats(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ChangeHairMat(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ChangeSkin obj = (ChangeSkin)ToLua.CheckObject<ChangeSkin>(L, 1);
			UnityEngine.Material arg0 = (UnityEngine.Material)ToLua.CheckObject<UnityEngine.Material>(L, 2);
			obj.ChangeHairMat(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ChangeHairMats(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ChangeSkin obj = (ChangeSkin)ToLua.CheckObject<ChangeSkin>(L, 1);
			UnityEngine.Material[] arg0 = ToLua.CheckObjectArray<UnityEngine.Material>(L, 2);
			obj.ChangeHairMats(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ChangeFaceMat(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ChangeSkin obj = (ChangeSkin)ToLua.CheckObject<ChangeSkin>(L, 1);
			UnityEngine.Material arg0 = (UnityEngine.Material)ToLua.CheckObject<UnityEngine.Material>(L, 2);
			obj.ChangeFaceMat(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ChangeFaceMats(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ChangeSkin obj = (ChangeSkin)ToLua.CheckObject<ChangeSkin>(L, 1);
			UnityEngine.Material[] arg0 = ToLua.CheckObjectArray<UnityEngine.Material>(L, 2);
			obj.ChangeFaceMats(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int BatchSetPartDyeColor(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ChangeSkin obj = (ChangeSkin)ToLua.CheckObject<ChangeSkin>(L, 1);
			string arg0 = ToLua.CheckString(L, 2);
			obj.BatchSetPartDyeColor(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int BatchReSetPartDyeColor(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ChangeSkin obj = (ChangeSkin)ToLua.CheckObject<ChangeSkin>(L, 1);
			string arg0 = ToLua.CheckString(L, 2);
			obj.BatchReSetPartDyeColor(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ChangePartDyeColor(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			ChangeSkin obj = (ChangeSkin)ToLua.CheckObject<ChangeSkin>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Color arg1 = ToLua.ToColor(L, 3);
			obj.ChangePartDyeColor(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResetPartDyeColor(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ChangeSkin obj = (ChangeSkin)ToLua.CheckObject<ChangeSkin>(L, 1);
			obj.ResetPartDyeColor();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResetPartHairDyeColor(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ChangeSkin obj = (ChangeSkin)ToLua.CheckObject<ChangeSkin>(L, 1);
			obj.ResetPartHairDyeColor();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsArtMode(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ChangeSkin obj = (ChangeSkin)ToLua.CheckObject<ChangeSkin>(L, 1);
			bool o = obj.IsArtMode();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearBoneArray(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			ChangeSkin.ClearBoneArray();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetHeadCustomization(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3 && TypeChecker.CheckTypes<int, object>(L, 2))
			{
				ChangeSkin obj = (ChangeSkin)ToLua.CheckObject<ChangeSkin>(L, 1);
				int arg0 = (int)LuaDLL.lua_tonumber(L, 2);
				object arg1 = ToLua.ToVarObject(L, 3);
				obj.SetHeadCustomization(arg0, arg1);
				return 0;
			}
			else if (count == 3 && TypeChecker.CheckTypes<ChangeSkin.HeadCustomizationType, object>(L, 2))
			{
				ChangeSkin obj = (ChangeSkin)ToLua.CheckObject<ChangeSkin>(L, 1);
				ChangeSkin.HeadCustomizationType arg0 = (ChangeSkin.HeadCustomizationType)ToLua.ToObject(L, 2);
				object arg1 = ToLua.ToVarObject(L, 3);
				obj.SetHeadCustomization(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: ChangeSkin.SetHeadCustomization");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int BatchSetHeadCustomization(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ChangeSkin obj = (ChangeSkin)ToLua.CheckObject<ChangeSkin>(L, 1);
			LuaTable arg0 = ToLua.CheckLuaTable(L, 2);
			obj.BatchSetHeadCustomization(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetBlendShapeValue(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			ChangeSkin obj = (ChangeSkin)ToLua.CheckObject<ChangeSkin>(L, 1);
			ChangeSkin.SkinType arg0 = (ChangeSkin.SkinType)ToLua.CheckObject(L, 2, typeof(ChangeSkin.SkinType));
			ChangeSkin.HeadCustomizationType arg1 = (ChangeSkin.HeadCustomizationType)ToLua.CheckObject(L, 3, typeof(ChangeSkin.HeadCustomizationType));
			float o = obj.GetBlendShapeValue(arg0, arg1);
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

