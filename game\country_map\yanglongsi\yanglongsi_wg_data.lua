YangLongSiaWGData = YangLongSiaWGData or BaseClass()

function YangLongSiaWGData:__init()
	if YangLongSiaWGData.Instance ~= nil then
		ErrorLog("[YangLongSiaWGData] attempt to create singleton twice!")
		return
	end
	YangLongSiaWGData.Instance = self

	self.cfg = ConfigManager.Instance:GetAutoConfig("cross_yanglong_auto")
	self.boss_cfg = ListToMap(self.cfg.boss, "seq")
	self.reward_cfg = ListToMapList(self.cfg.reward, "seq")
	self.other_cfg = self.cfg.other[1]
	self.convert_grade_cfg = self.cfg.convert_grade
	self.rank_reward_cfg = self.cfg.rank_reward
	self.convert_cfg = ListToMap(self.cfg.convert, "grade", "seq")
	self.conver_cost_item_id = ((self.convert_cfg[1] or {})[1] or {}).stuff_id_1
	self.yanglongsi_remind = false
	self.yanglongsi_lsgw_remind = false
	self.first_calculate_lsgw_remind = true

	self.current_hurt_boss_seq = -1
	self.current_tram_count = 0
	self.team_info_list = {}
	self.reward_info_list = {}
	self.reward_ori_list = {}
	self.myteam_hurt_data = {}
	self.team_data_list = {}
	self.boss_info_list = {}
	self.rank_item_list = {}
	self.convert_times_list = {}

	self.get_reward_index = -1
	self.refresh_remind_flag = 0
	self.main_button_last_time = 0
	self.button_time_before_flag = 0
	RemindManager.Instance:Register(RemindName.CrossYangLongSi, BindTool.Bind(self.GetYangLongSiRemind, self)) 			-- 国家星图秘境红点
end

function YangLongSiaWGData:__delete()
	YangLongSiaWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.CrossYangLongSi)
end

function YangLongSiaWGData:GetYangLongSiRemind()
	local is_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.CountryMapActView)
	if not is_open then
		return 0
	end
	
	if self.first_calculate_lsgw_remind then
		self.first_calculate_lsgw_remind = false
		
		if self:CalculateLSGWRemind() then
			return 1
		end
	end

	return (self.yanglongsi_lsgw_remind or self.yanglongsi_remind) and 1 or 0
end

function YangLongSiaWGData:GetBossCfgBySeq(seq)
	return self.boss_cfg[seq - 1] or {}
end

function YangLongSiaWGData:GetRewardCfgBySeq(seq)
	return self.reward_cfg[seq - 1] or {}
end

function YangLongSiaWGData:GetBossNameBySeq(seq)
	local monster_id = self.boss_cfg[seq - 1].monster_id
	local monster_name = ""

	if monster_id then
		local cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[monster_id]
		if not IsEmptyTable(cfg) then
			monster_name = cfg.name
		end
	end

	return monster_name
end

function YangLongSiaWGData:GetOtherCfg()
	return self.other_cfg
end

function YangLongSiaWGData:GetOtherCfgData(key)
	return self.other_cfg[key]
end

-- 固定大小30list
function YangLongSiaWGData:SetSCCrossYangLongRewardInfo(protocol)
	local reward_info_list = {}
	local reward_ori_list = {}
	local get_reward_flag = false
	local get_reward_list = {}
	self.yanglongsi_remind = false

	for k, v in pairs(protocol.reward_item_list) do
		local box_id, reward_list = self:GetRewardBoxIcon(v.boss_seq, v.rank)
		if v.boss_seq >= 0 and v.rank > 0 and box_id > 0 then
			local data = {}
			data.seq = v.boss_seq
			data.rank = v.rank
			data.box_id = box_id
			data.index = v.index
			data.reward_list = reward_list

			if nil == reward_info_list[data.seq] then
				reward_info_list[data.seq] = {}
			end

			self.yanglongsi_remind = true
			reward_ori_list[v.index] = data
			table.insert(reward_info_list[data.seq], data)
		else
			if self.get_reward_index == v.index and not IsEmptyTable(self.reward_ori_list[v.index]) then
				local old_data = self.reward_ori_list[v.index]
				if old_data.seq >= 0 and old_data.rank > 0 then
					get_reward_flag = true
					get_reward_list = self.reward_ori_list[v.index].reward_list
				end
			end

			reward_ori_list[v.index] = {seq = -1, box_id = box_id, rank = -1, index = v.index, reward_list = reward_list}
		end
	end

	self.reward_ori_list = reward_ori_list
	self.reward_info_list = reward_info_list
	self.get_reward_index = -1

	return get_reward_flag, get_reward_list
end

function YangLongSiaWGData:GetRewardBoxIcon(seq, rank_id)
	local data_list = self.reward_cfg[seq] or {}
	local box_id = -1
	local reward_list = {}
	local had_jieyi = SwornWGData.Instance:HadSworn()

	if not IsEmptyTable(data_list) then
		for k, v in pairs(data_list) do
			if v.min_rank == v.max_rank and rank_id == v.min_rank then
				box_id = v.icon
				reward_list = had_jieyi and v.reward_item or v.not_jieyi_reward_item
				break
			elseif v.max_rank > v.min_rank and rank_id >= v.min_rank and rank_id <= v.max_rank then
				box_id = v.icon
				reward_list = had_jieyi and v.reward_item or v.not_jieyi_reward_item
				break
			end
		end
	end

	return box_id, reward_list
end

function YangLongSiaWGData:GetRewardDataList()
	return self.reward_info_list
end

function YangLongSiaWGData:SetGetRewardItemIndex(index)
	self.get_reward_index = index
end

function YangLongSiaWGData:SetTeamInfo(protocol)
	local my_data = {}
	local team_data_list = {}
	local my_jieyi_id = SwornWGData.Instance:GetMyJieYiId()
	local role_uuid = RoleWGData.Instance:GetUUid()

	local hurt_add = false
	local hurt_del = false
	local old_hurt_boss_seq  = self.current_hurt_boss_seq

	self.current_tram_count = protocol.count
	self.team_info_list = protocol.team_info_list

	if not IsEmptyTable(self.team_info_list) then
		for k, v in pairs(self.team_info_list) do
			if my_jieyi_id > 0 and my_jieyi_id == v.jieyi_id then
				my_data = v
			elseif role_uuid == v.max_hurt_uuid then
				my_data = v
			end
	
			team_data_list[v.rank] = v
		end
	end

	hurt_add = old_hurt_boss_seq < 0 or old_hurt_boss_seq >= 0 and protocol.boss_seq >= 0 and old_hurt_boss_seq ~= protocol.boss_seq
	hurt_del = old_hurt_boss_seq >= 0 and protocol.boss_seq < 0
	self.myteam_hurt_data = my_data
	self.team_data_list = team_data_list
	self.current_hurt_boss_seq = protocol.boss_seq

	return {hurt_add = hurt_add, hurt_del = hurt_del}
end

function YangLongSiaWGData:GetMyTeamData()
	return self.myteam_hurt_data
end

function YangLongSiaWGData:GetBossHurtTeamDataList()
	return self.team_data_list
end

function YangLongSiaWGData:GetBossDataList()
	return self.boss_cfg
end

function YangLongSiaWGData:SetBossStateInfo(protocol)
	self.boss_info_list = protocol.boss_info_list
end

function YangLongSiaWGData:GetBossStateInfoBySeq(seq)
	return self.boss_info_list[seq] or {}
end

function YangLongSiaWGData:GetBeKillReduce()
	return self.other_cfg.be_kill_reduce_per / 100
end

function YangLongSiaWGData:GetCurrentBossRankTopJieYiId()
	return self.team_data_list[1] or {}
end

function YangLongSiaWGData:GetCurrentHurtBossSeq()
	return self.current_hurt_boss_seq
end

function YangLongSiaWGData:GetShowBossData()
	local data = {}
	data.monster_id = self.other_cfg.show_model
	data.display_scale = self.other_cfg.display_scale
	data.display_pos = self.other_cfg.display_pos
	data.display_rotation = self.other_cfg.display_rotation

	return data
end

---------------------------------------龙神归位-------------------------------------------
function YangLongSiaWGData:SetLSGWRankInfo(protocol)
	self.rank_item_list = protocol.rank_item_list
	table.sort(self.rank_item_list, SortTools.KeyLowerSorter("rank"))
end

function YangLongSiaWGData:SetLSGWBaseInfo(protocol)
	self.kill_role_daily_rank_score = protocol.kill_role_daily_rank_score
	self.convert_times_list = protocol.convert_times_list
	self.refresh_remind_flag = protocol.refresh_remind_flag
	self:CalculateLSGWRemind()
end

function YangLongSiaWGData:GetLSGWRankInfo()
	return self.rank_item_list
end

function YangLongSiaWGData:GetMyRankData()
	local my_rank_data = {}
	local rank_item_list = self:GetLSGWRankInfo()

	if IsEmptyTable(rank_item_list) then
		return my_rank_data
	end

	local role_uuid = RoleWGData.Instance:GetUUid()
	for k, v in pairs(rank_item_list) do
		if v.uuid == role_uuid then
			my_rank_data = v
			break
		end
	end  

	return my_rank_data
end

function YangLongSiaWGData:GetConvertGradeCfg()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local convert_cfg = {}

	for k, v in pairs(self.convert_grade_cfg) do
		if server_day >= v.min_day and server_day <= v.max_day then
			convert_cfg = v
			break
		end
	end

	return convert_cfg
end

function YangLongSiaWGData:GetRankRewardCfgByRankId(rank_id)
	local rank_reward_cfg = {}

	for k, v in pairs(	self.rank_reward_cfg) do
		if rank_id >= v.min_rank and rank_id <= v.max_rank then
			rank_reward_cfg = v
			break
		end
	end

	return rank_reward_cfg
end

function YangLongSiaWGData:GetConvertCfg()
	local grade_cfg = self:GetConvertGradeCfg()
	local convert_cfg = {}

	if not IsEmptyTable(grade_cfg) and grade_cfg.grade then
		convert_cfg = self.convert_cfg[grade_cfg.grade]
	end

	return convert_cfg
end

function YangLongSiaWGData:GetConvertCostItemId()
	return self.conver_cost_item_id
end

function YangLongSiaWGData:GetConvertTimeBySeq(seq)
	return self.convert_times_list[seq] or 0
end

function YangLongSiaWGData:CheckIsYangLongSiLSGWItem(change_item_id)
	local cost_item = self:GetConvertCostItemId()
	return (nil ~= cost_item) and (change_item_id == cost_item)
end 

function YangLongSiaWGData:CalculateLSGWRemind()
	self.yanglongsi_lsgw_remind = false
	local cfg = self:GetConvertCfg()

	if not IsEmptyTable(cfg) then
		for k, v in pairs(cfg) do
			local time = self:GetConvertTimeBySeq(v.seq)
			local is_times_limit = v.times_limit ~= 0
			local time_enough = not is_times_limit or (is_times_limit and (time < v.times_limit))

			if time_enough then
				local item_count = ItemWGData.Instance:GetItemNumInBagById(v.stuff_id_1)

				if v.stuff_num_1 <= item_count then
					self.yanglongsi_lsgw_remind = true
					break
				end
			end
		end
	end

	return self.yanglongsi_lsgw_remind
end

function YangLongSiaWGData:GetLSGWRemind()
	return self.yanglongsi_lsgw_remind
end

function YangLongSiaWGData:GetBossRefreshRemindFlag()
	return 	self.refresh_remind_flag == 1
end

function YangLongSiaWGData:GetCurMianUIBtnStatus()
	local seq = -1
	local start_time = 0
	local end_time = 0
	-- local rest_time = 0
	local open_level = YangLongSiaWGData.Instance:GetOtherCfgData("open_level") or 0
	local role_level = RoleWGData.Instance:GetRoleLevel()

	if role_level < open_level then
		return false, seq, start_time, end_time
	end

	-- 策划LJ要求，倒计时20分钟
	local act_keep_time = 20 * 60
	local cur_time = TimeWGCtrl.Instance:GetServerTime()
	for k,v in ipairs(self.cfg.refresh_time) do
		local cfg_time = TimeUtil.FormatCfgTimestamp(v.refresh_time)
		-- 3s 容错
		if (cur_time >= cfg_time - 3) and (cur_time <= cfg_time + act_keep_time) then
			start_time = cfg_time
			seq = v.seq
		end
	end

	if start_time > 0 then
		end_time = start_time + act_keep_time
		return true, seq, start_time, end_time
	end

	return false, seq, start_time, end_time
end

function YangLongSiaWGData:CanEnterFightYangLongSiBoss()
    local enter_time = self:GetOtherCfgData("enter_time")
    local time_count_tab = string.split(enter_time, "|")
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local day_start_time = TimeWGCtrl.Instance:NowDayTimeStart(server_time)
    local reason = ""
    local time_str = ""

    for k, v in pairs(time_count_tab) do
        local time_tab = string.split(v, ",")

        local time_num_s = tonumber(time_tab[1])
        local hour_s = math.floor(time_num_s / 100)
        local min_s = time_num_s % 100

        local time_num_e = tonumber(time_tab[2])
        local hour_e = math.floor(time_num_e / 100)
        local min_e = time_num_e % 100

        if server_time >= (day_start_time + hour_s * 60 * 60 + min_s * 60) then
            if server_time < (day_start_time + hour_e * 60 * 60 + min_e * 60) then
                return true
            end
        end

        time_str = time_str == "" and time_str or time_str .. ","
        local hour_s_str = hour_s < 10 and "0" .. hour_s or hour_s
        local min_s_str = min_s == 0 and "00" or min_s < 10 and "0" .. min_s or min_s
        local hour_e_str = hour_e < 10 and "0" .. hour_e or hour_e
        local min_e_str = min_e == 0 and "00" or min_e < 10 and "0" .. min_e or min_e
        time_str = time_str .. (hour_s_str .. ":" ..min_s_str .. "~".. hour_e_str .. ":" ..min_e_str)
    end

    reason = string.format(Language.YangLongSi.DescEnterFBTimeLimit, time_str)

    return false, reason
end