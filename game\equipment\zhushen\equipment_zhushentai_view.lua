function EquipmentView:InitZhuShenTaiView()
    self.zhushentai_select_equip_part = -1
	self.zhushentai_select_equip_data = nil

    XUI.AddClickEventListener(self.node_list["btn_zhushentai_up"], BindTool.Bind(self.OnClickZhuShenTaiUpLevel, self))
    XUI.AddClickEventListener(self.node_list["zhushentai_rule_btn_tips"], BindTool.Bind(self.OnClickZhuShenTaiTipsBtn, self))

    if self.zhushentai_hole_list == nil then
        self.zhushentai_hole_list = {}
        local node_num = self.node_list["zhushentai_hole_root"].transform.childCount
        for i = 0, node_num - 1 do
            self.zhushentai_hole_list[i] = ZSTEquipRender.New(self.node_list["zhushentai_hole_root"]:FindObj("zhushentai_hole_" .. i))
            self.zhushentai_hole_list[i]:SetIndex(i)
            self.zhushentai_hole_list[i]:SetCellClickCallBack(BindTool.Bind(self.OnSelectZSTCallBack, self))
        end
    end

    if self.zhushentai_hole_attr_list == nil then
        self.zhushentai_hole_attr_list = {}
        local node_num = self.node_list["zhushentai_attr_list"].transform.childCount
        for i = 1, node_num do
            self.zhushentai_hole_attr_list[i] = CommonAddAttrRender.New(self.node_list["zhushentai_attr_list"]:FindObj("attr_" .. i))
            local cell = CommonAddAttrRender.New(self.node_list["zhushentai_attr_list"]:FindObj("attr_" .. i))
		    cell:SetIndex(i)
			cell:SetAttrNameNeedSpace(true)
            self.zhushentai_hole_attr_list[i] = cell
        end
    end

    if self.zhushentai_stuff_item == nil then
        self.zhushentai_stuff_item = ItemCell.New(self.node_list["zhushentai_stuff_pos"])
    end

  --[[   if self.zhushentai_tween_root then
        self.zhushentai_tween_root:Restart()
    else
        self.zhushentai_tween_root = self.node_list["zhushentai_bg2"].transform:DORotate(Vector3(0, 0, -360), 20, DG.Tweening.RotateMode.FastBeyond360)
        self.zhushentai_tween_root:SetEase(DG.Tweening.Ease.Linear)
        self.zhushentai_tween_root:SetLoops(-1)
    end ]]
end

function EquipmentView:DeleteZhuShenTaiView()
    self.zhushentai_select_equip_part = -1
	self.zhushentai_select_equip_data = nil

    if self.zhushentai_hole_list then
        for k, v in pairs(self.zhushentai_hole_list) do
            v:DeleteMe()
        end
        self.zhushentai_hole_list = nil
    end

    if self.zhushentai_hole_attr_list then
        for k, v in pairs(self.zhushentai_hole_attr_list) do
            v:DeleteMe()
        end
        self.zhushentai_hole_attr_list = nil
    end

    if self.zhushentai_stuff_item then
        self.zhushentai_stuff_item:DeleteMe()
        self.zhushentai_stuff_item = nil
    end

    if self.zhushentai_tween_root then
        self.zhushentai_tween_root:Kill()
        self.zhushentai_tween_root = nil
    end
end

function EquipmentView:FlushEquipZhuShenTaiView(to_ui_param)
    self:FlushZhuShenTaiLeftView()

    if to_ui_param == nil then
        local jump_hole = EquipmentWGData.Instance:GetZhuShenTaiJumpHole()
        local is_red = false
        if self.zhushentai_select_equip_data then
            is_red = EquipmentWGData.Instance:GetZhuShenTaiHoleRedmind(self.zhushentai_select_equip_data.seq)
        end

        if jump_hole ~= self.zhushentai_select_equip_part and not is_red then
            local cell = self.zhushentai_hole_list[jump_hole]
            if cell then
                self:OnSelectZSTCallBack(cell)
                return
            end
        end
    else
        local cell = self.zhushentai_hole_list[tonumber(to_ui_param)]
        if cell then
            self:OnSelectZSTCallBack(cell)
            return
        end
    end

    

    self:FlushZhuShenTaiRightView()
end

function EquipmentView:OnSelectZSTCallBack(cell)
    if cell == nil then
        return
    end

    local data = cell:GetData()
    if data == nil then
        return
    end

    local hole = data.seq
    if hole == self.zhushentai_select_equip_part and self.zhushentai_select_equip_data == data then
        return
    end

    self.zhushentai_select_equip_part = hole
    for k,v in pairs(self.zhushentai_hole_list) do
        v:FlushSelectHL(k == hole)
    end

    self:FlushZhuShenTaiRightView()
end

function EquipmentView:OnClickZhuShenTaiUpLevel()
    if self.zhushentai_select_equip_data == nil then
        return
    end

    local select_data = self.zhushentai_select_equip_data
    local hole_cfg = EquipmentWGData.Instance:GetZhuShenTaiHoleLevelCfg(select_data.seq, select_data.level)
    if hole_cfg == nil then
        return
    end

    local has_num = ItemWGData.Instance:GetItemNumInBagById(hole_cfg.cost_item_id)
    if has_num < hole_cfg.cost_item_num then
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = hole_cfg.cost_item_id})
        return
    end

    local is_special_hole = EquipmentWGData.Instance:GetZhuShenTaiIsSpecialHole(select_data.seq)
    if is_special_hole then
        local is_up = EquipmentWGData.Instance:GetZhuShenTaiAllHoleLevel(select_data.level, select_data.min_level_limit)
        if not is_up then
            TipWGCtrl.Instance:ShowSystemMsg(Language.EquipmentZhuShen.ZhuShenTaiUpError)
            return
        end
    end

    local effect_type = select_data.level < 1 and UIEffectName.s_jihuo or UIEffectName.s_shengji
    TipWGCtrl.Instance:ShowEffect({effect_type = effect_type, is_success = true, 
                                pos = Vector2(0, 0), parent_node = self.node_list["zhushentai_effect_level_root"]})

    EquipmentWGCtrl.Instance:SendEquipZhuShenOperate(CAST_SOUL_OPERATE_TYPE.GOD_LEVEL_UP, select_data.seq)
end

function EquipmentView:OnClickZhuShenTaiTipsBtn()
	local role_tip = RuleTip.Instance
	role_tip:SetTitle(Language.EquipmentZhuShen.ZhuShenTaiRuleTitle)
	role_tip:SetContent(Language.EquipmentZhuShen.ZhuShenTaiRuleDesc)
end

function EquipmentView:FlushZhuShenTaiLeftView()
    local hole_list = EquipmentWGData.Instance:GetZhuShenTaiHoleList()
    if hole_list then
        for k, v in pairs(self.zhushentai_hole_list) do
            v:SetData(hole_list[k + 1])
        end
    end
end

function EquipmentView:FlushZhuShenTaiRightView()
    local cell = self.zhushentai_hole_list[self.zhushentai_select_equip_part]
    if cell and cell.data ~= nil then
        self.zhushentai_select_equip_data = cell.data
    else
        self.zhushentai_select_equip_data = nil
        return
    end

    local is_can_uplevel = false
    local select_data = self.zhushentai_select_equip_data
    self.node_list["zhushentai_hole_title"].text.text = select_data.level < 1 
    and select_data.name 
    or string.format(Language.EquipmentZhuShen.ZhuShenTaiHoleName, select_data.name, select_data.level)

    local hole_cfg = EquipmentWGData.Instance:GetZhuShenTaiHoleLevelCfg(select_data.seq, select_data.level)
    if hole_cfg == nil then
        return
    end
    
    local attr_list = EquipmentWGData.Instance:GetZhuShenTaiHoleAttrList(select_data.seq, select_data.level)

    if attr_list then
        local need_show_attr_up_effect = false

        if nil ~= self.zhushentai_seq_cache and nil ~= self.zhushentai_level_cache then
            if (self.zhushentai_seq_cache == select_data.seq) and (select_data.level - self.zhushentai_level_cache == 1) then
                need_show_attr_up_effect = true
            end
        end

        for k, v in pairs(self.zhushentai_hole_attr_list) do
            v:SetData(attr_list[k])

            if need_show_attr_up_effect then
                v:PlayAttrValueUpEffect()
            end
        end
    end
    self.zhushentai_seq_cache = select_data.seq
    self.zhushentai_level_cache = select_data.level
    
    self.zhushentai_stuff_item:SetData({item_id = hole_cfg.cost_item_id})
    local has_num = ItemWGData.Instance:GetItemNumInBagById(hole_cfg.cost_item_id)
    is_can_uplevel = has_num >= hole_cfg.cost_item_num
    local color = (not is_can_uplevel) and ITEM_NUM_COLOR.NOT_ENOUGH or ITEM_NUM_COLOR.ENOUGH
    local str = has_num .. "/" .. hole_cfg.cost_item_num
    self.zhushentai_stuff_item:SetRightBottomColorText(str, color)
    self.zhushentai_stuff_item:SetRightBottomTextVisible(true)

    local is_act = EquipmentWGData.Instance:GetZhuShenTaiHoleIsAct(select_data.seq)
    self.node_list["zhushentai_up_title"].text.text = is_act and Language.EquipmentZhuShen.ZhuShenTaiUpBtnTxt2 or Language.EquipmentZhuShen.ZhuShenTaiUpBtnTxt1
    self.node_list["zhushentai_btn_text"].text.text = is_act and Language.EquipmentZhuShen.ZhuHunUpBtnTxt2 or Language.EquipmentZhuShen.ZhuHunUpBtnTxt1

    local max_level = EquipmentWGData.Instance:GetZhuShenTaiHoleMaxLevel(select_data.seq)
    self.node_list["zhuanshentai_is_max"]:SetActive(select_data.level >= max_level)
    self.node_list["btn_zhushentai_up"]:SetActive(not (select_data.level >= max_level))

    local is_special_hole = EquipmentWGData.Instance:GetZhuShenTaiIsSpecialHole(select_data.seq)
    if is_special_hole then
        local is_act = EquipmentWGData.Instance:GetZhuShenTaiHoleIsAct(select_data.seq)
        local is_all_act = EquipmentWGData.Instance:GetZhuShenTaiNorHoleIsAllAct()
        self.node_list["zhushentai_act_root"]:SetActive(not is_act and (not is_all_act))
        self.node_list["zhushentai_up_root"]:SetActive(is_act or is_all_act)

        for i = 0, 4 do
            local nor_act = EquipmentWGData.Instance:GetZhuShenTaiHoleIsAct(i)
            XUI.SetGraphicGrey(self.node_list["zhushentai_act_hole_" .. i], not nor_act)
        end

        local is_special_up = EquipmentWGData.Instance:GetZhuShenTaiAllHoleLevel(select_data.level, select_data.min_level_limit)
        self.node_list["zhushentai_stuff_pos"]:SetActive(is_special_up)
        self.node_list["zhushentai_sphole_desc"]:SetActive(not is_special_up)
        self.node_list["btn_zhushentai_up"]:SetActive(is_special_up)
        self.node_list["zhushentai_up_red"]:SetActive(is_all_act and is_can_uplevel and is_special_up)
    else
        self.node_list["zhushentai_stuff_pos"]:SetActive(true)
        self.node_list["zhushentai_sphole_desc"]:SetActive(false)
        self.node_list["zhushentai_act_root"]:SetActive(false)
        self.node_list["zhushentai_up_root"]:SetActive(true)
        self.node_list["zhushentai_up_red"]:SetActive(is_can_uplevel)
    end

end

ZSTEquipRender = ZSTEquipRender or BaseClass(BaseRender)
function ZSTEquipRender:__init()
	XUI.AddClickEventListener(self.view, BindTool.Bind(BindTool.Bind(self.OnClickCellBtn, self)))
end

function ZSTEquipRender:__delete()
    self.item_click_callback = nil
end

function ZSTEquipRender:SetCellClickCallBack(call_back)
    self.item_click_callback = call_back
end

function ZSTEquipRender:OnClickCellBtn()
    if self.item_click_callback then
        self.item_click_callback(self)
    end
end

function ZSTEquipRender:OnFlush()
    if self.data == nil then
        return
    end

    self.node_list["level"].text.text = string.format(Language.EquipmentZhuShen.ZhuShenTaiHoleLevel, self.data.level)

    local is_act = EquipmentWGData.Instance:GetZhuShenTaiHoleIsAct(self.data.seq)
    XUI.SetGraphicGrey(self.node_list["icon"], not is_act)

    local is_red = EquipmentWGData.Instance:GetZhuShenTaiHoleRedmind(self.data.seq)
    self.node_list["redmind"]:SetActive(is_red)
end

function ZSTEquipRender:FlushSelectHL(is_select)
    self.node_list["hl_img"]:SetActive(is_select)
end