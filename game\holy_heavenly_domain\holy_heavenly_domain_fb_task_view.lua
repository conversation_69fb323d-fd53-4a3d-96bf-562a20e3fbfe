HolyHeavenlyDomainFBTaskView = HolyHeavenlyDomainFBTaskView or BaseClass(SafeBaseView)

function HolyHeavenlyDomainFBTaskView:__init()
	self:AddViewResource(0, "uis/view/holy_heavenly_domain_ui_prefab", "layout_holy_heavenly_domain_map_task")
	self.view_cache_time = 0
    self.active_close = false
    self.is_safe_area_adapter = true
	self.need_change_to_hurt_list = true
	self.select_boss_data = {}
end

function HolyHeavenlyDomainFBTaskView:LoadCallBack()
	MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))

	if not self.boss_list then
		self.boss_list = AsyncListView.New(HolyHeavenlyDomainTaskBossItemRender, self.node_list.boss_list)
		local select_boss_index = HolyHeavenlyDomainWGData.Instance:GetEnterFbCache()
		self.boss_list:SetDefaultSelectIndex(select_boss_index)
        self.boss_list:SetSelect<PERSON>allBack(BindTool.Bind(self.OnClickBossItem, self))
	end

	if not self.task_rank_list then
		self.task_rank_list = AsyncListView.New(HolyHeavenlyDomainBossHurtItemRender, self.node_list.task_rank_list)
	end

	XUI.AddClickEventListener(self.node_list.btn_switch_damage, BindTool.Bind(self.OnClickSwitchDamage, self))
	XUI.AddClickEventListener(self.node_list.btn_swithc_list, BindTool.Bind(self.OnClickSwitchList, self))
	XUI.AddClickEventListener(self.node_list.boss_power_tip_btn, BindTool.Bind(self.OnClickBossPowerTip, self))
	self.node_list.desc_power.text.text = Language.HolyHeavenlyDomain.DescPower
end

function HolyHeavenlyDomainFBTaskView:InitCallBack()
	local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
	if self.node_list["fb_task_root"] then
		self.obj = self.node_list["fb_task_root"].gameObject
		self.obj.transform:SetParent(parent.gameObject.transform)
		self.obj.transform.localPosition = Vector3.zero
		self.obj.transform.localScale = Vector3.one
	end
end

function HolyHeavenlyDomainFBTaskView:CloseCallBack()
	self.is_out_fb = true
    if self.obj then
        self.obj:SetActive(false)
    end
end

function HolyHeavenlyDomainFBTaskView:ShowIndexCallBack()
	if self.obj then
        self.obj:SetActive(true)
    end
end

function HolyHeavenlyDomainFBTaskView:ReleaseCallBack()
	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end

	if self.boss_list then
		self.boss_list:DeleteMe()
		self.boss_list = nil
	end

	if self.task_rank_list then
		self.task_rank_list:DeleteMe()
		self.task_rank_list = nil
	end

	self.select_boss_data = nil
	self.need_select_boss = nil
end

function HolyHeavenlyDomainFBTaskView:OnFlush()
	local boss_data = HolyHeavenlyDomainWGData.Instance:GetFbBossList()
    self.boss_list:SetDataList(boss_data)

	local power = HolyHeavenlyDomainWGData.Instance:GetMapXianLiValue()
	self.node_list.boss_power_num.text.text = power

	local today_score = HolyHeavenlyDomainWGData.Instance:GetTodayScore()
	self.node_list.desc_add_score.text.text = string.format(Language.HolyHeavenlyDomain.CurFbAddScore, today_score)
end

function HolyHeavenlyDomainFBTaskView:UpdateRankList(state)
	if self.node_list then
		if state then
			if self.node_list.btn_switch_damage then
				self.node_list.btn_switch_damage:CustomSetActive(true)
			end

			if self.need_change_to_hurt_list then
				self.need_change_to_hurt_list = false
				self:SetPanelState(false)
			end
	
			self:UpdateDamageRankList()
		else
			if self.node_list.btn_switch_damage then
				self.node_list.btn_switch_damage:CustomSetActive(false)
			end

			self.need_change_to_hurt_list = true
			self:SetPanelState(true)
		end
	end
end

function HolyHeavenlyDomainFBTaskView:UpdateDamageRankList()
	local all_hurt_info = BossAssistWGData.Instance:GetHurtInfo()
	local damage_list = all_hurt_info.hurt_info or {}
	local boss_id = all_hurt_info.target_boss_id

	local main_role_hurt = all_hurt_info.main_role_hurt or 0
	if self.node_list.my_damate then
		self.node_list.my_damate.text.text = CommonDataManager.ConverNumber(main_role_hurt)
	end

	if self.node_list.des_text_info then
		local boss_cfg = BossWGData.Instance:GetMonsterCfgByid(boss_id)
		self.node_list.des_text_info.text.text = string.format(Language.CountryMap.DemageGetReward, boss_cfg.drop_param)
	end

	if self.task_rank_list then
		self.task_rank_list:SetDataList(damage_list)
	end
end

function HolyHeavenlyDomainFBTaskView:OnClickSwitchDamage()
	self:SetPanelState(false)
end

function HolyHeavenlyDomainFBTaskView:OnClickSwitchList()
	self:SetPanelState(true)
end

function HolyHeavenlyDomainFBTaskView:SetPanelState(show_boss)
	if self.node_list.bosslist_root then
		self.node_list.bosslist_root:CustomSetActive(show_boss)
	end

	if self.node_list.rank_list_root then
		self.node_list.rank_list_root:CustomSetActive(not show_boss)
	end
end

function HolyHeavenlyDomainFBTaskView:OnClickBossItem(item)
	if nil == item or IsEmptyTable(item.data) then
		return
	end

	local data = item.data
	self.select_boss_data = data
	
	self:GoToBossPoint(data)
	self:UpdateSelectBoss()
end

function HolyHeavenlyDomainFBTaskView:GoToBossPoint(data)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
	local boss_data = data.boss_cfg
	local pos_list = string.split(boss_data.monster_pos, ",") 
	local boss_x, boss_y = pos_list[1], pos_list[2]

    local sence_id = Scene.Instance:GetSceneId()
    local role = Scene.Instance:GetMainRole()
    local role_x, role_y = role:GetLogicPos()

    if role_x == boss_x and role_y == boss_y then
        local scene_logic = Scene.Instance:GetSceneLogic()

    	if scene_logic ~= nil then
    		scene_logic:SetGuaJiInfoPos(role_x, role_y)
    	end

		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
    else
		local scene_logic = Scene.Instance:GetSceneLogic()
		if scene_logic ~= nil then
			scene_logic:ClearGuaJiInfo()
		end

		MoveCache.SetEndType(MoveEndType.FightByMonsterId)
		GuajiCache.monster_id = boss_data.monster_id
		MoveCache.param1 = boss_data.monster_id
		local range = BossWGData.Instance:GetMonsterRangeByid(boss_data.boss_id)

		GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
			-- 切换挂机模式，以优先选择玩家 --
			if scene_logic ~= nil then
				local mian_role = Scene.Instance:GetMainRole()
				if mian_role ~= nil and not mian_role:IsDeleted() then
					local pos_x, pos_y = mian_role:GetLogicPos()
					if GameMath.GetDistance(boss_x, boss_y, pos_x, pos_y, false) <= range * range then
						scene_logic:SetGuaJiInfoPos(pos_x, pos_y, range, boss_data.monster_id)
					end
				end
			end

			GuajiWGCtrl.Instance:StopGuaji()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end)

		GuajiWGCtrl.Instance:MoveToPos(sence_id, boss_x, boss_y, range)
	end
end

function HolyHeavenlyDomainFBTaskView:UpdateSelectBoss()
	local cell_list = self.boss_list:GetSortCellList()

	for k,v in ipairs(cell_list) do
		v:FlushHl(self.select_boss_data)
	end
end

function HolyHeavenlyDomainFBTaskView:OnClickBossPowerTip()
	RuleTip.Instance:SetContent(Language.HolyHeavenlyDomain.XianLiContent, Language.HolyHeavenlyDomain.XianLiTitle)
end

-------------------------------------HolyHeavenlyDomainTaskBossItemRender---------------------------------------
HolyHeavenlyDomainTaskBossItemRender = HolyHeavenlyDomainTaskBossItemRender or BaseClass(BaseRender)

function HolyHeavenlyDomainTaskBossItemRender:__init()
	self.select_boss_data = {}
end

function HolyHeavenlyDomainTaskBossItemRender:__delete()
    if self.refresh_event then
        GlobalTimerQuest:CancelQuest(self.refresh_event)
        self.refresh_event = nil
    end

	self.select_boss_data = nil
end

function HolyHeavenlyDomainTaskBossItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local boss_id = self.data.boss_cfg.monster_id
    local boss_cfg = BossWGData.Instance:GetMonsterCfgByid(boss_id)

	self:FlushSelectState()

    if IsEmptyTable(boss_cfg) then
        return
    end

	self.node_list.desc_name.text.text = boss_cfg.name or ""
    local jieshu = boss_cfg.boss_jieshu or 0
    self.node_list.desc_jieshu.text.text = string.format(Language.Common.Order, jieshu)

    self:RefreshRemainTime()

	if self.refresh_event then
		GlobalTimerQuest:CancelQuest(self.refresh_event)
		self.refresh_event = nil
	end

	self.refresh_event = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.RefreshRemainTime, self), 0.5)
end

function HolyHeavenlyDomainTaskBossItemRender:RefreshRemainTime()
    if not self.data then
		return
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local next_refresh_time = self.data.info.next_refresh_time
	local time_str = ""

	if next_refresh_time > 0 and (next_refresh_time - server_time > 1) then
		time_str =  ToColorStr(TimeUtil.FormatSecond(next_refresh_time - server_time, 3), COLOR3B.RED)
	else
		if self.refresh_event then
			GlobalTimerQuest:CancelQuest(self.refresh_event)
			self.refresh_event = nil
		end

		time_str = Language.CountryMap.CountryMapBossActiveState
	end

	self.node_list.desc_time_flag.text.text = time_str
end

function HolyHeavenlyDomainTaskBossItemRender:FlushHl(select_data)
	self.select_boss_data = select_data
	self:Flush()
end

function HolyHeavenlyDomainTaskBossItemRender:FlushSelectState()
	local state = false

	if not IsEmptyTable(self.select_boss_data) then
		local data_info = self.data.boss_cfg or {}
		local select_data_info = self.select_boss_data.boss_cfg or {}

		if not IsEmptyTable(data_info) and not IsEmptyTable(select_data_info) then
			state = data_info.monster_id == select_data_info.monster_id and data_info.seq == select_data_info.seq
		end
	end

	if self.node_list["flag_light"] then
		self.node_list["flag_light"]:SetActive(state)
	end
end

---------------------------------HolyHeavenlyDomainBossHurtItemRender------------------------------
HolyHeavenlyDomainBossHurtItemRender = HolyHeavenlyDomainBossHurtItemRender or BaseClass(BaseRender)

function HolyHeavenlyDomainBossHurtItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.node_list.num.text.text = self.index
    self.node_list.name.text.text = self.data.name
	local damage = CommonDataManager.ConverNumber(self.data.hurt)
	self.node_list.damage.text.text = damage
end