ManHuangBuyView = ManHuangBuyView or BaseClass(DayCountChangeView)

function ManHuangBuyView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(812, 574)})
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_tianshen_msg")
	self.role_data_change_callback =  BindTool.Bind1(self.OnRoleDataChange, self)
end

function ManHuangBuyView:__delete()

end

function ManHuangBuyView:ReleaseCallBack()
	DayCountChangeView.ReleaseCallBack(self)
	RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)
end

function ManHuangBuyView:CloseCallBack()
end

function ManHuangBuyView:OnRoleDataChange(attr_name, value, old_value)
	if attr_name == "vip_level" then
		self:Flush()
	end
end

function ManHuangBuyView:LoadCallBack()
	DayCountChangeView.LoadCallBack(self)
	self.node_list["title_view_name"].tmp.text = Language.ViewName.CiShuZengJia

	self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind(self.OnClinkCancelHandler, self))
	self.node_list["btn_buy_count"].button:AddClickListener(BindTool.Bind(self.OnClinkBuyCount, self))
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback, {"vip_level"})
end

function ManHuangBuyView:ShowIndexCallBack()
end

function ManHuangBuyView:OnFlush()
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	local vip_buy_cfg = FuBenPanelWGData.Instance:GetVipBuyManHuangCfg()
	local next_vip =  FuBenPanelWGData.Instance:GetVipNextManHuangCount(role_vip)
	local buy_times = ManHuangGuDianWGData.Instance:GetManHuangBuyTimes()

	local remain_count = vip_buy_cfg["param_" .. role_vip] - buy_times
	local color = remain_count > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.RED
   	local des = string.format(Language.FuBenPanel.FuBenBuyTips, color, vip_buy_cfg["param_" .. role_vip] - buy_times, vip_buy_cfg["param_" .. role_vip])


	local comsume_gold = ManHuangGuDianWGData.Instance:GetManHuangBuyComsumeGold()
	if comsume_gold then
		local comsume_str = string.format(Language.FuBenPanel.CopperBuyNum, comsume_gold)
		self.node_list["rich_buy_desc"].tmp.text = comsume_str
	end

	self.node_list["rich_buy_des_1"].tmp.text = des
	local max_vip = VipWGData.Instance:GetMaxVIPLevel()
	if role_vip < max_vip and vip_buy_cfg["param_" .. role_vip + 1] then
		if vip_buy_cfg["param_" .. role_vip + 1] > vip_buy_cfg["param_" .. role_vip] then
			des = string.format(Language.FuBenPanel.CopperBuyTips2, vip_buy_cfg["param_" .. role_vip + 1])
		else
			for i = 1, max_vip do
				if vip_buy_cfg["param_" .. i] > vip_buy_cfg["param_" .. role_vip] then
					des = string.format(Language.FuBenPanel.CopperBuyTips2, vip_buy_cfg["param_" .. i])
					next_vip = i

					break
				end
			end
		end
	else
		--des = string.format(Language.FuBenPanel.CopperBuyTips2, vip_buy_cfg["param_" .. role_vip])
		des = Language.FuBenPanel.CopperBuyTips3
	end
	if role_vip >= next_vip then
		des = Language.FuBenPanel.CopperBuyTips3
	end

	self.node_list["rich_buy_des_2"].tmp.text = des


	if self.node_list["img_vip_curlevel"] and self.node_list["img_vip_nexlevel"] then
		self.node_list["img_vip_curlevel"].tmp.text = "V"..role_vip

		if role_vip < next_vip then
			self.node_list["img_vip_nexlevel"].tmp.text = "V"..next_vip

			-- self.copper_vip_num2:SetNumber(next_vip)

			self.node_list["layout_next_vip"]:SetActive(true)


		else
			self.node_list["img_vip_nexlevel"].tmp.text = "V"..role_vip

			self.node_list["layout_next_vip"]:SetActive(false)
		end
	end
end

function ManHuangBuyView:OnClinkCancelHandler()
	self:Close()
end

function ManHuangBuyView:OnClinkBuyCount()
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	local vip_buy_cfg = FuBenPanelWGData.Instance:GetVipBuyManHuangCfg()
	local buy_times = ManHuangGuDianWGData.Instance:GetManHuangBuyTimes()
	local times = vip_buy_cfg["param_" .. role_vip] - buy_times
	local is_max_count = vip_buy_cfg["param_" .. 15] == buy_times

	if 0 == times then
		if is_max_count then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.BuyMaxCount)
			self:Close()
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.VIPTooLow)
		end
	else
		FuBenWGCtrl.Instance:SendManHuangGuDianOperate(MANHUANGGUDIAN_OPERA_TYPE.BUY_TIMES)
	end
end
