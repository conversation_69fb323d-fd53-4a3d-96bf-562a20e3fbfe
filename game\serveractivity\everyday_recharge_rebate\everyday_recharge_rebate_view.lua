
local ZHIGOU_MAX_DAY = 7
local zhigou_max_cell_num = 5

function EveryDayRechargeView:InitEDRechargeView()
	self.zhigou_list_cell_index = nil

	if not self.recharge_rebate_list then
		self.recharge_rebate_list = AsyncListView.New(EveryDayRechargeZhiGouCell,self.node_list["recharge_rebate_list"])
		self.recharge_rebate_list:SetSelectCallBack(BindTool.Bind(self.ZhiGouListCellSelectCallBack, self))
		self.recharge_rebate_list:SetDefaultSelectIndex(1)
	end
	self.node_list["recharge_zhigou_btn"].button:AddClickListener(BindTool.Bind(self.OnClickOneKeyGetRebate,self))
	self.node_list["recharge_zhigou_note"].button:AddClickListener(BindTool.Bind(self.OnClickRechargeRebateNote,self))

	self.cur_show_cfg = {}
end

function EveryDayRechargeView:ReleseEDRechargeView()
	self.zhigou_list_cell_index = nil
	self.cur_show_cfg = nil

	if self.recharge_rebate_list then
		self.recharge_rebate_list:DeleteMe()
		self.recharge_rebate_list = nil
	end
	self.had_load_list = false

	if self.delay_rebate_anim then
		GlobalTimerQuest:CancelQuest(self.delay_rebate_anim)
		self.delay_rebate_anim = nil
	end

	if self.rebate_recharge_model then
		self.rebate_recharge_model:DeleteMe()
		self.rebate_recharge_model = nil
	end

	if self.zhigou_pic_yoyo_tween then
		self.zhigou_pic_yoyo_tween:Kill()
		self.zhigou_pic_yoyo_tween = nil
	end
end

function EveryDayRechargeView:ShowIndexEDRechargeView()
	 -- self.node_list["recharge_rebate_list"]:SetActive(false)--设置隐藏会导致list中后几个cell跳转显示有问题
	 self.node_list["recharge_zhigou_list_canva"].canvas_group.alpha = 0
	 self.node_list["zhigou_hight_order"]:SetActive(false)

	 --每日首次登陆显示一次红点刷新
	 if EveryDayRechargeRebateWGData.Instance:GetTodayFirstLoginRed() then
	 	EveryDayRechargeRebateWGData.Instance:SetTodayFirstLoginRed(0)
	 	RemindManager.Instance:Fire(RemindName.DailyRecharge_ZhiGou)
	 end

	 self:PlayRechargeRebateOpen()
end

function EveryDayRechargeView:FlushEDRechargeView()
	if not self.recharge_rebate_list then
		return
	end

	local list_data = EveryDayRechargeRebateWGData.Instance:GetRebateListData()
	if not IsEmptyTable(list_data) then
		local temp_data = {}
		for k, v in pairs(list_data) do
			temp_data[#temp_data + 1] = v
		end--索引要从1开始
		table.sort(temp_data, SortTools.KeyLowerSorter("day_id"))
		self.cur_show_cfg = temp_data --刷新模型用的
		self.recharge_rebate_list:SetDataList(temp_data)
		local jump_index = EveryDayRechargeRebateWGData.Instance:GetFirstSelectCell(temp_data)
		self.recharge_rebate_list:JumpToIndex(jump_index)
	end

	---[[
	local money = EveryDayRechargeRebateWGData.Instance:GetNeedRechargeCount()
	local already_recharge = EveryDayRechargeRebateWGData.Instance:GetAlreadyRecharge()
	money = money / RECHARGE_BILI
	already_recharge = already_recharge / RECHARGE_BILI
	--local color = already_recharge >= money and COLOR3B.D_GREEN or COLOR3B.D_RED
	local is_can_get = EveryDayRechargeRebateWGData.Instance:IsShowRebateZhiGouRedPoint() == 1
	
	-- self.node_list["recharge_rebate_text"].text.text = string.format(Language.RebateRecharge.AlreadyRecharge,color,already_recharge,money)
	self.node_list["recharge_rebate_repoint"]:SetActive(is_can_get)
	-- self.node_list["recharge_rebate_cost_text"].text.text = money
	-- local a,b
	if already_recharge < money then
		--XUI.SetGraphicGrey(self.node_list.recharge_zhigou_btn, true)
		-- a,b = ResPath.GetF2RechargeIcon("lianchong_weidacheng") --18元购买
		-- self.node_list.recharge_rebate_btn_text.image:LoadSprite(a,b,function ()
		-- 	self.node_list.recharge_rebate_btn_text.image:SetNativeSize()
		-- end)
		local cur_open_group = EveryDayRechargeRebateWGData.Instance:GetSCActZhiChongOpenGroup()
		local price = RoleWGData.GetPayMoneyStr(money, GET_GOLD_REASON.GET_GOLD_REASON_ZHI_GOU, cur_open_group)
		self.node_list.recharge_rebate_btn_text.text.text = price
	else
		if is_can_get then
			-- a,b = ResPath.GetF2RechargeIcon("lianchong_yijianlingqu") --一键领取
			-- self.node_list.recharge_rebate_btn_text.image:LoadSprite(a,b,function ()
			-- 	self.node_list.recharge_rebate_btn_text.image:SetNativeSize()
			-- end)
			self.node_list.recharge_rebate_btn_text.text.text = Language.RebateRecharge.RebateBtnDesc2
		end
	end

	self.node_list["recharge_zhigou_btn"]:SetActive(is_can_get or already_recharge < money)
	--]]
	self:ZhiGouFlushModelShow()
	--顶部标题天数
	--local cur_open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()--开服天数
	--local open_cfg = EveryDayRechargeRebateWGData.Instance:GetRebateOpenCfg()
	--local act_start_day = open_cfg and open_cfg.open_day or 1--活动开始
	--local show_day = cur_open_day - act_start_day + 1
	--show_day = show_day >= ZHIGOU_MAX_DAY and ZHIGOU_MAX_DAY or show_day
	--self.node_list["zhigou_day_text"].text.text = show_day
end

function EveryDayRechargeView:ZhiGouListCellSelectCallBack(cell)
	if cell ~= nil and self.node_list.recharge_rebate_list ~= nil then
		cell:SetParentScrollRect(self.node_list.recharge_rebate_list.scroll_rect)
	end
	-- local data = cell:GetData()
	-- if not data then return end

	-- if self.zhigou_list_cell_index ~= data.day_id then
	-- 	self.zhigou_list_cell_index = data.day_id
	-- 	self:ZhiGouFlushModelShow(data.day_id)
	-- end
end

function EveryDayRechargeView:ZhiGouFlushModelShow()
	if not self.rebate_recharge_model then
		self.rebate_recharge_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["zhigou_model_pos"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}
		
		self.rebate_recharge_model:SetRenderTexUI3DModel(display_data)
		-- self.rebate_recharge_model:SetUI3DModel(self.node_list["zhigou_model_pos"].transform, self.node_list["zhigou_model_pos"].event_trigger_listener,
		-- 1, false, MODEL_CAMERA_TYPE.BASE)
	end

	local cfg_name = ""
	if self.cur_show_cfg then
		self:ZhiGouSetFashionRoleModel(self.cur_show_cfg[1].res_show)
		--zhanli_num = self:ZhiGouGetPowerNum(cur_show_cfg.res_show)
		cfg_name = ItemWGData.Instance:GetItemName(self.cur_show_cfg[1].res_show)
	end

	self.node_list["model_name"].text.text = cfg_name
	--设置战力显示
	-- self.node_list["zhigou_zhanli_root"]:SetActive(zhanli_num > 0)
	-- self.node_list["recharge_zhigou_cap_value"].text.text = zhanli_num
end

function EveryDayRechargeView:ZhiGouSetFashionRoleModel(item_id)
	if not item_id then
		return
	end

	self.rebate_recharge_model:ClearModel()

	local role_res_id = AppearanceWGData.Instance:GetRoleResId()
	local model_rotate = Vector3(0, 180, 0)
	local main_role = Scene.Instance:GetMainRole()
    local vo = main_role and main_role:GetVo()
    local d_body_res, d_hair_res, d_face_res
    if vo and vo.appearance then
        if vo.appearance.fashion_body == 0 then
            d_body_res = vo.appearance.default_body_res_id
            d_hair_res = vo.appearance.default_hair_res_id
            d_face_res = vo.appearance.default_face_res_id
        end
    end

	local extra_role_model_data = {
        d_face_res = d_face_res,
        d_hair_res = d_hair_res,
		d_body_res = d_body_res,
    }
	self.rebate_recharge_model:SetRoleResid(role_res_id, nil, extra_role_model_data)

	local res_id, part_type = NewAppearanceWGData.Instance:GetFashionResByItemId(item_id)
	if part_type == SHIZHUANG_TYPE.HALO then--光环:3
		self.rebate_recharge_model:SetHaloResid(res_id)
	elseif part_type == SHIZHUANG_TYPE.MASK then--面具:10
		self.rebate_recharge_model:SetMaskResid(res_id)
	elseif part_type == SHIZHUANG_TYPE.BELT then--腰:11
		self.rebate_recharge_model:SetWaistResid(res_id)
	elseif part_type == SHIZHUANG_TYPE.WEIBA then--尾巴:12
		self.rebate_recharge_model:SetTailResid(res_id)
		model_rotate = MODEL_ROTATION_TYPE.WEIBA
	elseif part_type == SHIZHUANG_TYPE.SHOUHUAN then--手:13
		self.rebate_recharge_model:SetShouHuanResid(res_id)
	end
	
	self.rebate_recharge_model:SetRotation(model_rotate)
end

function EveryDayRechargeView:ZhiGouGetPowerNum(item_list_str)
	if not item_list_str then
		return 0
	end
	local temp_item_list = Split(item_list_str, ",")
    local power_num = 0
    if not IsEmptyTable(temp_item_list) then
        for k, item_id in pairs(temp_item_list) do
            power_num = power_num + ItemShowWGData.CalculateCapability(tonumber(item_id))
        end
    end
    return power_num
end

function EveryDayRechargeView:OnClickOneKeyGetRebate()
	local cur_open_group = EveryDayRechargeRebateWGData.Instance:GetSCActZhiChongOpenGroup()
	if not EveryDayRechargeRebateWGData.Instance:GetIsEnoughRecharge() then
		local money = EveryDayRechargeRebateWGData.Instance:GetNeedRechargeCount()
		local ok_func = function ()
			if money > 0 then
				RechargeWGCtrl.Instance:Recharge(money / RECHARGE_BILI, GET_GOLD_REASON.GET_GOLD_REASON_ZHI_GOU, cur_open_group)
			end
		end

		local price = RoleWGData.GetPayMoneyStr(money / RECHARGE_BILI, GET_GOLD_REASON.GET_GOLD_REASON_ZHI_GOU, cur_open_group)
		local text_dec = string.format(Language.RebateRecharge.CheckAndRecharge, price)
		TipWGCtrl.Instance:OpenAlertTips(text_dec, ok_func)

		TipWGCtrl.Instance:ShowSystemMsg(Language.RebateRecharge.GetRebateDay[7])
		return
	end
	
	if EveryDayRechargeRebateWGData.Instance:IsShowRebateZhiGouRedPoint() == 1 then
		ServerActivityWGCtrl.Instance:SendSCZhiGouOpera(ACT_ZHICHONG_OPER_TYPE.ACT_ZHICHONG_OPER_TYPE_ONE_KEY_FETCH_ALL_REWARD, cur_open_group)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.RebateRecharge.GetRebateDay[6])
	end
end

function EveryDayRechargeView:OnClickRechargeRebateNote()
	RuleTip.Instance:SetContent(EveryDayRechargeRebateWGData.Instance:GetRebateRechargeRule(), Language.Recharge.ZhiGouFanLiTitle)
end


function EveryDayRechargeView:PlayRechargeRebateOpen()
	if self.delay_rebate_anim then
		GlobalTimerQuest:CancelQuest(self.delay_rebate_anim)
		self.delay_rebate_anim = nil
	end
	self.delay_rebate_anim = GlobalTimerQuest:AddDelayTimer(function ()
		-- self.node_list["recharge_rebate_list"]:SetActive(true)
		self.node_list["recharge_zhigou_list_canva"].canvas_group.alpha = 1
		local cell_list = self.recharge_rebate_list:GetAllItems()
		local sort_list = GetSortListView(cell_list)
		zhigou_max_cell_num = #sort_list
    	local count = 0
    	for k,v in ipairs(sort_list) do
    	    if 0 ~= v.index then
    	        count = count + 1
    	    end
    	    v.item:OnPlayOpenAnim(count)
    	end
	end,0.4)
	
	--UITween.DoUpDownCrashTween(self.node_list["zhigou_title_scale_root"])
end

function EveryDayRechargeView:EDZhiGouCellAnimCallBack()
	self.node_list["zhigou_hight_order"]:SetActive(true)
end
---------------------------------------------------------------------------------------------------------------------------
EveryDayRechargeZhiGouCell = EveryDayRechargeZhiGouCell or BaseClass(BaseRender)

function EveryDayRechargeZhiGouCell:__init()
	if not self.reward_cell_list then
		self.reward_cell_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
		self.reward_cell_list:SetIsDelayFlush(false)
	end
	self.is_load_complete = true
	self.nested_scroll_rect = SNestedScrollRect.New(self.node_list.reward_list)
    if self.parent_scroll_rect then
        self.nested_scroll_rect:SetParentScrollRect(self.parent_scroll_rect)
    end

	self.node_list["rebate_get_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGetRechargeRebate,self))
end

function EveryDayRechargeZhiGouCell:__delete()
	if self.reward_cell_list then
		self.reward_cell_list:DeleteMe()
		self.reward_cell_list = nil
	end

	self.parent_scroll_rect = nil
    if self.nested_scroll_rect then
        self.nested_scroll_rect:DeleteMe()
        self.nested_scroll_rect = nil
    end
    self.is_load_complete = false
end

function EveryDayRechargeZhiGouCell:SetParentScrollRect(scroll_rect)
    self.parent_scroll_rect = scroll_rect
    if self.is_load_complete then
        self.nested_scroll_rect:SetParentScrollRect(self.parent_scroll_rect)
    end
end

function EveryDayRechargeZhiGouCell:OnFlush()
	if self.data and not IsEmptyTable(self.data) then
		if self.data.reward_item then
			local srot_data = SortTableKey(self.data.reward_item)
			self.reward_cell_list:SetDataList(srot_data)
		end
		--self.node_list["rebate_num"].text.text = self.data.show or 1
		local day_index = self.data.day_id or 0
		local reward_index = self.data.reward_index or 0
		self.node_list["day_text"].text.text = string.format(Language.RebateRecharge.DayNumIndex, day_index)
		self.node_list["hlday_text"].text.text = string.format(Language.RebateRecharge.DayNumIndex, day_index)
		self.day_status = EveryDayRechargeRebateWGData.Instance:GetThisDayCanGet(self.data.day_id)
		self.node_list["have_get"]:SetActive(self.day_status == 2)
		self.node_list["rebate_get_btn"]:SetActive(self.day_status ~= 2)
		self.node_list["red_point"]:SetActive(false)
		if self.day_status == 1 then
			self.node_list["get_text"].text.text = Language.RebateRecharge.GetRebateDay[1]
			self.node_list["red_point"]:SetActive(true)
		elseif self.day_status == 0 then
			local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()		
			local need_day =  day_index - cur_day
			if need_day == 1 then
				self.node_list["get_text"].text.text = Language.RebateRecharge.GetRebateDay[2]
			elseif need_day == 2 then
				self.node_list["get_text"].text.text = Language.RebateRecharge.GetRebateDay[3]
			elseif need_day > 2 then
				self.node_list["get_text"].text.text = string.format(Language.RebateRecharge.GetRebateDay[4], math.min(need_day, ZHIGOU_MAX_DAY))
			end
		elseif self.day_status == -1 then
			self.node_list["get_text"].text.text = Language.RebateRecharge.GetRebateDay[5]
		end

		--刷新背景底: 1:红 2:橙 
		--local cur_group = EveryDayRechargeRebateWGData.Instance:GetSCActZhiChongOpenGroup()
		--local img_res_name = cur_group % 2 == 0 and "lianchong_gaiban_ban1" or "lianchong_gaiban_ban"
		--self.node_list["normal_bg"].image:LoadSprite(ResPath.GetF2RechargeIcon(img_res_name))
	end
end

function EveryDayRechargeZhiGouCell:OnClickGetRechargeRebate()
	if self.day_status == -1 then
		self:OnClickRecharge()
	elseif self.day_status == 1 then
		local cur_open_group = EveryDayRechargeRebateWGData.Instance:GetSCActZhiChongOpenGroup()
		ServerActivityWGCtrl.Instance:SendSCZhiGouOpera(ACT_ZHICHONG_OPER_TYPE.ACT_ZHICHONG_OPER_TYPE_FETCH_REWARD, cur_open_group, self.data.reward_index)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.RebateRecharge.NeedWayToGet)
	end
end

function EveryDayRechargeZhiGouCell:OnClickRecharge()
	local money = EveryDayRechargeRebateWGData.Instance:GetNeedRechargeCount()
	local ok_func = function ()
		if money > 0 then
			local cur_open_group = EveryDayRechargeRebateWGData.Instance:GetSCActZhiChongOpenGroup()
			RechargeWGCtrl.Instance:Recharge(money / RECHARGE_BILI, GET_GOLD_REASON.GET_GOLD_REASON_ZHI_GOU, cur_open_group)
		end
	end
	local text_dec = string.format(Language.RebateRecharge.CheckAndRecharge,money/ RECHARGE_BILI)
	TipWGCtrl.Instance:OpenAlertTips(text_dec, ok_func)
	TipWGCtrl.Instance:ShowSystemMsg(Language.RebateRecharge.GetRebateDay[7])
end

function EveryDayRechargeZhiGouCell:OnPlayOpenAnim(index)
	local time = 0.5
	local start_x = 1120
	local speed = start_x / time
	start_x = start_x + 150 * index
	time = start_x / speed
	self.node_list["animal_root"].transform.localPosition = Vector3(start_x, 0, 0)
	local tween = self.node_list["animal_root"].rect:DOAnchorPos(Vector2(0, 0), time)
	if index == zhigou_max_cell_num then
		tween:OnComplete(function()
			ServerActivityWGCtrl.Instance:EDZhiGouCellAnimCallBack()
		end)
	end
end

function EveryDayRechargeZhiGouCell:OnSelectChange(is_select)
    -- 高亮
    if nil == is_select then
        return
    end
    if self.node_list["select_effect"] then
		self.node_list["select_effect"]:SetActive(is_select)
	end
end