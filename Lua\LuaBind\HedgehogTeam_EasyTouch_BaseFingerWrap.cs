﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class HedgehogTeam_EasyTouch_BaseFingerWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(HedgehogTeam.EasyTouch.BaseFinger), typeof(System.Object));
		<PERSON><PERSON>unction("GetGesture", GetGesture);
		<PERSON><PERSON>unction("New", _CreateHedgehogTeam_EasyTouch_BaseFinger);
		<PERSON><PERSON>unction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("fingerIndex", get_fingerIndex, set_fingerIndex);
		<PERSON><PERSON>("touchCount", get_touchCount, set_touchCount);
		<PERSON><PERSON>("startPosition", get_startPosition, set_startPosition);
		<PERSON><PERSON>("position", get_position, set_position);
		<PERSON><PERSON>("deltaPosition", get_deltaPosition, set_deltaPosition);
		<PERSON><PERSON>("actionTime", get_actionTime, set_actionTime);
		<PERSON><PERSON>("deltaTime", get_deltaTime, set_deltaTime);
		<PERSON><PERSON>("pickedCamera", get_pickedCamera, set_pickedCamera);
		L.RegVar("pickedObject", get_pickedObject, set_pickedObject);
		L.RegVar("isGuiCamera", get_isGuiCamera, set_isGuiCamera);
		L.RegVar("isOverGui", get_isOverGui, set_isOverGui);
		L.RegVar("pickedUIElement", get_pickedUIElement, set_pickedUIElement);
		L.RegVar("altitudeAngle", get_altitudeAngle, set_altitudeAngle);
		L.RegVar("azimuthAngle", get_azimuthAngle, set_azimuthAngle);
		L.RegVar("maximumPossiblePressure", get_maximumPossiblePressure, set_maximumPossiblePressure);
		L.RegVar("pressure", get_pressure, set_pressure);
		L.RegVar("radius", get_radius, set_radius);
		L.RegVar("radiusVariance", get_radiusVariance, set_radiusVariance);
		L.RegVar("touchType", get_touchType, set_touchType);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateHedgehogTeam_EasyTouch_BaseFinger(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				HedgehogTeam.EasyTouch.BaseFinger obj = new HedgehogTeam.EasyTouch.BaseFinger();
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: HedgehogTeam.EasyTouch.BaseFinger.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetGesture(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)ToLua.CheckObject<HedgehogTeam.EasyTouch.BaseFinger>(L, 1);
			HedgehogTeam.EasyTouch.Gesture o = obj.GetGesture();
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_fingerIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			int ret = obj.fingerIndex;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fingerIndex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_touchCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			int ret = obj.touchCount;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index touchCount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_startPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			UnityEngine.Vector2 ret = obj.startPosition;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index startPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_position(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			UnityEngine.Vector2 ret = obj.position;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index position on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_deltaPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			UnityEngine.Vector2 ret = obj.deltaPosition;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index deltaPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_actionTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			float ret = obj.actionTime;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index actionTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_deltaTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			float ret = obj.deltaTime;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index deltaTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_pickedCamera(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			UnityEngine.Camera ret = obj.pickedCamera;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pickedCamera on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_pickedObject(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			UnityEngine.GameObject ret = obj.pickedObject;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pickedObject on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isGuiCamera(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			bool ret = obj.isGuiCamera;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isGuiCamera on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isOverGui(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			bool ret = obj.isOverGui;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isOverGui on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_pickedUIElement(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			UnityEngine.GameObject ret = obj.pickedUIElement;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pickedUIElement on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_altitudeAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			float ret = obj.altitudeAngle;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index altitudeAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_azimuthAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			float ret = obj.azimuthAngle;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index azimuthAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_maximumPossiblePressure(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			float ret = obj.maximumPossiblePressure;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index maximumPossiblePressure on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_pressure(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			float ret = obj.pressure;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pressure on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_radius(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			float ret = obj.radius;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index radius on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_radiusVariance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			float ret = obj.radiusVariance;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index radiusVariance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_touchType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			UnityEngine.TouchType ret = obj.touchType;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index touchType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_fingerIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.fingerIndex = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fingerIndex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_touchCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.touchCount = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index touchCount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_startPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.startPosition = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index startPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_position(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.position = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index position on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_deltaPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.deltaPosition = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index deltaPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_actionTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.actionTime = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index actionTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_deltaTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.deltaTime = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index deltaTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_pickedCamera(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			UnityEngine.Camera arg0 = (UnityEngine.Camera)ToLua.CheckObject(L, 2, typeof(UnityEngine.Camera));
			obj.pickedCamera = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pickedCamera on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_pickedObject(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
			obj.pickedObject = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pickedObject on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isGuiCamera(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isGuiCamera = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isGuiCamera on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isOverGui(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isOverGui = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isOverGui on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_pickedUIElement(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
			obj.pickedUIElement = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pickedUIElement on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_altitudeAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.altitudeAngle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index altitudeAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_azimuthAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.azimuthAngle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index azimuthAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_maximumPossiblePressure(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.maximumPossiblePressure = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index maximumPossiblePressure on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_pressure(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.pressure = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pressure on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_radius(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.radius = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index radius on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_radiusVariance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.radiusVariance = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index radiusVariance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_touchType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.BaseFinger obj = (HedgehogTeam.EasyTouch.BaseFinger)o;
			UnityEngine.TouchType arg0 = (UnityEngine.TouchType)ToLua.CheckObject(L, 2, typeof(UnityEngine.TouchType));
			obj.touchType = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index touchType on a nil value");
		}
	}
}

