﻿using UnityEngine;
using System.Collections;

public class GuideMask : MonoBehaviour
{
    public static GuideMask Instance;
    private Material ma;
    public float ChangeSpeed = 1.0f;
    public float Acceleration = 0.0f;
    public float waitTime = 0.8f;
    public RectTransform uiRect;
    public Camera cam;
    public RectTransform rootRect;
    private float count;
    private bool isPlay = false;
    static private float radius = 2.0f;
    private void Awake()
    {
        Instance = this;
    }
    // Use this for initialization
    void Start()
    {
        if (ma == null)
        {
            var img = this.GetComponent<UnityEngine.UI.Image>();
            if (img)
            {
                ma = img.material;
            }
            if (ma == null)
            {
                Debug.LogError("Can not find GuideMaskShader!");
                return;
            }
            if (isPlay)
            {
                isPlay = false;
                StartCoroutine(ChangeEffect());
            }
        }
    }

    private void Update()
    {
        //if (Input.GetKeyDown(KeyCode.Space))
        //{
        //    StartCoroutine(ChangeEffect());
        //}
    }

    public void DoChangeEffect()
    {
        if (ma == null)
        {
            isPlay = true;
            return;
        }
        StartCoroutine(ChangeEffect());
    }

    void OnRenderImage(RenderTexture source, RenderTexture destination)
    {
        Graphics.Blit(source, destination, ma);
    }

    IEnumerator ChangeEffect()
    {
        if (rootRect && uiRect && cam)
        {
            ma.SetFloat("_Radius", radius);
            ma.SetFloat("_Center_X", 0.5f);
            ma.SetFloat("_Center_Y", 0.5f);
            ma.SetFloat("_Center_XR", 0.5f);
            ma.SetFloat("_Center_YR", 0.5f);
            Vector3 pos;
            Vector2 localPos;
            float pro = rootRect.rect.width / rootRect.rect.height;
            yield return new WaitForSecondsRealtime(waitTime);
            float addDis = ChangeSpeed;
            float ui_w = uiRect.rect.width / rootRect.rect.width;
            float ui_h = uiRect.rect.height / rootRect.rect.height;
            float min_radius = 0.1f;
            if (uiRect.rect.width > uiRect.rect.height)
                min_radius = Mathf.Sqrt(0.5f * ui_h * ui_h) + Mathf.Sqrt(ui_w * ui_w - ui_w * ui_h + 0.5f * ui_h * ui_h);
            else
                min_radius = Mathf.Sqrt(0.5f * ui_w * ui_w) + Mathf.Sqrt(ui_h * ui_h - ui_h * ui_w + 0.5f * ui_w * ui_w);

            float center_x = 0.0f;
            float center_y = 0.0f;
            count = radius;
            while (ma.GetFloat("_Radius") > min_radius)
            {
                pos = cam.WorldToScreenPoint(uiRect.position);
                UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(rootRect, pos, cam, out localPos);
                center_x = (localPos.x + (0.5f - uiRect.pivot.x) * uiRect.rect.width + rootRect.rect.width / 2) / rootRect.rect.width;
                center_y = (localPos.y + (0.5f - uiRect.pivot.y) * uiRect.rect.height + rootRect.rect.height / 2) / rootRect.rect.height;
                if (uiRect.rect.width > uiRect.rect.height)
                {
                    ma.SetFloat("_Center_X", center_x - 0.5f * ui_w + 0.5f * ui_h / pro);
                    ma.SetFloat("_Center_Y", center_y);
                    ma.SetFloat("_Center_XR", center_x + 0.5f * ui_w - 0.5f * ui_h / pro);
                    ma.SetFloat("_Center_YR", center_y);
                }
                else
                {
                    ma.SetFloat("_Center_X", center_x);
                    ma.SetFloat("_Center_Y", center_y - 0.5f * ui_h + 0.5f * ui_w * pro);
                    ma.SetFloat("_Center_XR", center_x);
                    ma.SetFloat("_Center_YR", center_y + 0.5f * ui_h - 0.5f * ui_w * pro);
                }
                if (count - min_radius <= 0.25f)
                    addDis = Mathf.Max(addDis * Acceleration, 0.05f);
                count = ma.GetFloat("_Radius") - Mathf.Min(Time.fixedDeltaTime, 0.03f) * addDis;
                if (addDis == ChangeSpeed)
                    Mathf.Max(count, min_radius + 0.25f);
                else
                    Mathf.Max(count, min_radius);
                ma.SetFloat("_Radius", Mathf.Max(count, min_radius));
                yield return 0;
            }
        }
        else
        {
            ma.SetFloat("_Radius", radius);
            yield return 0;
        }
        yield return 0;
    }
}