MergeFirstRechargeWGData = MergeFirstRechargeWGData or BaseClass()

function MergeFirstRechargeWGData:__init()
	if MergeFirstRechargeWGData.Instance then
		ErrorLog("[MergeFirstRechargeWGData] Attemp to create a singleton twice !")
	end
	MergeFirstRechargeWGData.Instance = self
	self:IntFirstRechargeData()
	MergeActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_DAY_FIRST_RECHARGE, {[1] = MERGE_EVENT_TYPE.LEVEL}, 
    	BindTool.Bind(self.GetActCanOpen, self), BindTool.Bind(self.IsShowFirstRechargeRedPoint, self))
	RemindManager.Instance:Register(RemindName.MergeFirstRecharge, BindTool.Bind(self.IsShowFirstRechargeRedPoint, self))

end

function MergeFirstRechargeWGData:__delete()
	MergeFirstRechargeWGData.Instance = nil 
	RemindManager.Instance:UnRegister(RemindName.MergeFirstRecharge)
end

function MergeFirstRechargeWGData:IntFirstRechargeData()
	self.merge_day_first_recharge_cfg = ConfigManager.Instance:GetAutoConfig("combineserve_activity_day_first_recharge_auto")
	self.day_first_reward_cfg = self.merge_day_first_recharge_cfg.reward
	self.day_first_param = self.merge_day_first_recharge_cfg.config_param
	self.day_first_param_cfg = ListToMap(self.day_first_param, "grade")
	self.day_first_recharge_cfg = ListToMap(self.merge_day_first_recharge_cfg.first_recharge, "grade", "day_index", "seq")
end

function MergeFirstRechargeWGData:SetFirstRechargeData(protocol)
	--self.first_gift_state = protocol.gift_state                    --当前可领取状态(已废弃)
	self.activity_start_combineserver_day = protocol.activity_start_combineserver_day   --当前活动开启时合服的天数
    self.cur_activity_grade = protocol.grade
	self.chongzhi_rmb_num = protocol.chongzhi_rmb_num
	self.first_recharge_reward_flag = bit:d2b_two(protocol.first_recharge_reward_flag)		--次数奖励标记 bit.
	self:SetRewardState()
end

function MergeFirstRechargeWGData:SetRewardState()
	local val1, val2 = self:GetRewardRechargeValue()

	if not val1 or not val2 then
		return
	end

	if self.first_recharge_reward_flag[0] == 1 then
		self.reward_state1 = 2
	elseif (self.first_recharge_reward_flag[0] == 0 and self.chongzhi_rmb_num >= val1) then
		self.reward_state1 = 1
	else
		self.reward_state1 = 0
	end

	if self.first_recharge_reward_flag[1] == 1 then
		self.reward_state2 = 2
	elseif (self.first_recharge_reward_flag[1] == 0 and self.chongzhi_rmb_num >= val2) then
		self.reward_state2 = 1
	else
		self.reward_state2 = 0
	end

	if self.reward_state1 == self.reward_state2 then
		self.all_reward_state = self.reward_state1
	elseif self.reward_state1 == 1 or self.reward_state2 == 1 then
		self.all_reward_state = 1
	else
		self.all_reward_state = 0
	end
end

function MergeFirstRechargeWGData:GetAllRewardState()
	return self.all_reward_state or 0
end

function MergeFirstRechargeWGData:GetChongZhiRmbNum()
	return self.chongzhi_rmb_num or 0
end

function MergeFirstRechargeWGData:GetRewardState()
	return self.reward_state1, self.reward_state2
end

function MergeFirstRechargeWGData:GetRewardRechargeValue()
	local day = self:GetFirstDayRechargeOpenDay()
	local grade = self:GetGradeByServerDay()
	local val1 = self:GetFirstRechargeCfgBySeq(grade, day, 0).recharge
	local val2 = self:GetFirstRechargeCfgBySeq(grade, day, 1).recharge

	return val1, val2
end

function MergeFirstRechargeWGData:GetFirstRechargeRewardCfg()
	return self.day_first_reward_cfg
end

function MergeFirstRechargeWGData:GetFirstRechargeCfgBySeq(grade, day_index, seq)
	return ((self.day_first_recharge_cfg[grade] or {})[day_index] or {})[seq] or {}
end

function MergeFirstRechargeWGData:GetFirstRechargeCfgByDay(grade, day_index)
	return ((self.day_first_recharge_cfg[grade] or {})[day_index]) or {}
end

-- function MergeFirstRechargeWGData:GetFirstRechargeState()
-- 	return self.first_gift_state
-- end

function MergeFirstRechargeWGData:GetFirstRechargeopenServerDay()
	local open_day = MergeActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_DAY_FIRST_RECHARGE)
	return open_day
end


--根据服务器开服天数获得活动配置grade
function MergeFirstRechargeWGData:GetGradeByServerDay()
	return self.cur_activity_grade or 0
end

-- 获取每日首充奖励配置 根据活动配置grade和开始天数 去获取奖励
function MergeFirstRechargeWGData:GetShouChongRewardCfgByDay(day)
	local grade = self:GetGradeByServerDay()
	local cfg = self:GetFirstRechargeCfgByDay(grade, day)
	local data_list = {}
	for k, v in pairs(cfg) do
		if day == v.day_index then
			table.insert(data_list, v)
		end
	end

	return data_list
end

--获取活动已开启的天数
function MergeFirstRechargeWGData:GetFirstDayRechargeOpenDay()
	local open_time = MergeActivityWGData.Instance:GetActOpenDay(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_DAY_FIRST_RECHARGE)
	return open_time
end

function MergeFirstRechargeWGData:IsShowFirstRechargeRedPoint()
	local state = MergeActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_DAY_FIRST_RECHARGE)
	if not state then
		return 0
	end

	if self.all_reward_state == MERGE_ACTIVITY_FIRST_RECHARGE.KLQ then
		return 1
	end
	return 0
end


function MergeFirstRechargeWGData:GetActCanOpen()
	if nil == self.cur_activity_grade then
        return false
    end
	local grade = self.cur_activity_grade
	local cfg = self.day_first_param_cfg[grade]

	if nil == cfg then
		return false
	end

	local vo = GameVoManager.Instance:GetMainRoleVo()
	if vo.level >= cfg.open_role_level then
		return true
	end

	return false
end