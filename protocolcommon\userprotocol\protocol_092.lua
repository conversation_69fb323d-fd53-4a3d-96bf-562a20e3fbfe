-- 跨服星图秘境
CSCrossXingTuMiJingOpera =  CSCrossXingTuMiJingOpera or BaseClass(BaseProtocolStruct) 
function  CSCrossXingTuMiJingOpera:__init()
	self.msg_type = 9200
end
function CSCrossXingTuMiJingOpera:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
end

SCCrossXingTuMiJingInfo = SCCrossXingTuMiJingInfo or BaseClass(BaseProtocolStruct)
function SCCrossXingTuMiJingInfo:__init()
	self.msg_type = 9201
end

function SCCrossXingTuMiJingInfo:Decode()
	self.enter_xtmj_times = MsgAdapter.ReadInt() -- 已进入副本次数
	self.enter_xtmj_last_time = MsgAdapter.ReadUInt()--抢夺开始时间
end


SCCrossXingTuMiJingSyncWarInfo = SCCrossXingTuMiJingSyncWarInfo or BaseClass(BaseProtocolStruct)
function SCCrossXingTuMiJingSyncWarInfo:__init()
	self.msg_type = 9202
end

function SCCrossXingTuMiJingSyncWarInfo:Decode()
	self.fetch_exp = MsgAdapter.ReadLL()	-- 个人获得经验
	self.kill_count = MsgAdapter.ReadShort()    -- 击杀怪物数    
	self.scene_lv = MsgAdapter.ReadShort()      -- 秘境等级   
	self.wave = MsgAdapter.ReadChar()			--进度
	self.role_count = MsgAdapter.ReadChar()     --玩家数
	self.boss_step = MsgAdapter.ReadChar()     --玩家数
	MsgAdapter.ReadChar()                      
end

SCXinTuMiJingFinishInfo = SCXinTuMiJingFinishInfo or BaseClass(BaseProtocolStruct)
function SCXinTuMiJingFinishInfo:__init()
	self.msg_type = 9203
end

function SCXinTuMiJingFinishInfo:Decode()
	self.exp = MsgAdapter.ReadLL()	-- 个人获得经验    
	self.count = MsgAdapter.ReadInt()	    
	self.is_win = MsgAdapter.ReadInt() 
	self.reward_item_list = {}
	for i=1, self.count do
		local item = {}
		item.item_id = MsgAdapter.ReadUShort()
		item.is_bind = MsgAdapter.ReadShort()
		item.num = MsgAdapter.ReadShort()
		item.star_count = MsgAdapter.ReadShort()
		self.reward_item_list[i] = item
	end    
end

--累计登录
CSLeiJiLoginOperate = CSLeiJiLoginOperate or BaseClass(BaseProtocolStruct)
function CSLeiJiLoginOperate:__init()
	self.msg_type = 9204
end

function CSLeiJiLoginOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param_1)
	MsgAdapter.WriteInt(self.param_2)
	MsgAdapter.WriteInt(self.param_3)
end


SCLeiJiLoginInfo = SCLeiJiLoginInfo or BaseClass(BaseProtocolStruct)
function SCLeiJiLoginInfo:__init()
	self.msg_type = 9205
end

function SCLeiJiLoginInfo:Decode()
	self.open_time = MsgAdapter.ReadUInt()
	local reward_can_fetch_flag = MsgAdapter.ReadLL()  --可领取奖励
	self.reward_can_fetch_flag = bit:d2b_l2h(reward_can_fetch_flag, nil, true)

	local reward_flag = MsgAdapter.ReadLL()  --所有奖励领取标记
	self.reward_flag = bit:d2b_l2h(reward_flag, nil, true)

	local day_reward_flag = MsgAdapter.ReadLL()  --天数奖励标记
	self.day_reward_flag = bit:d2b_l2h(day_reward_flag, nil, true)
end


-- 天神灵核请求
CSTianShenLingHeOpera = CSTianShenLingHeOpera or BaseClass(BaseProtocolStruct)
function CSTianShenLingHeOpera:__init()
	self.msg_type = 9220
end

function CSTianShenLingHeOpera:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param_1)
	MsgAdapter.WriteInt(self.param_2)
	MsgAdapter.WriteInt(self.param_3)
end

function TianShenLingHeItemRead()
	local data = {}
	data.index = 0
	data.item_id = MsgAdapter.ReadUInt()
	data.num = MsgAdapter.ReadUShort()
	data.is_bind = MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
	data.color = 0
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	if item_cfg then
		data.color = item_cfg.color
	end

	data.used_slot = -1
	data.can_reslove = false
	local linghe_cfg = TianShenLingHeWGData.Instance:GetLingGHeCfgByItemId(data.item_id)
	if linghe_cfg then
		data.used_slot = linghe_cfg.slot
		data.can_reslove = linghe_cfg.get_id > 0 and linghe_cfg.get_num > 0
	end

	return data
end

-- 天神灵核全部背包信息
SCTianShenLingHeAllBagInfo = SCTianShenLingHeAllBagInfo or BaseClass(BaseProtocolStruct)
function SCTianShenLingHeAllBagInfo:__init()
	self.msg_type = 9221
end

function SCTianShenLingHeAllBagInfo:Decode()
	self.bag_list = {}
	self.change_reason = MsgAdapter.ReadInt()
	self.bag_count = MsgAdapter.ReadInt()
	for i = 1, self.bag_count do
		local index = MsgAdapter.ReadInt()
		local data = TianShenLingHeItemRead()
		data.index = index
		self.bag_list[index] = data
	end
end

-- 天神灵核全部槽位信息
SCTianShenLingHeAllSlotInfo = SCTianShenLingHeAllSlotInfo or BaseClass(BaseProtocolStruct)
function SCTianShenLingHeAllSlotInfo:__init()
	self.msg_type = 9222
end

function SCTianShenLingHeAllSlotInfo:Decode()
	self.slot_list = {}
	local max_slot = TianShenLingHeWGData.MAX_SLOT - 1
	for ts_index = 0, 24 do
		self.slot_list[ts_index] = {}
		for slot = 0, max_slot do
			self.slot_list[ts_index][slot] = TianShenLingHeItemRead()
		end

		for slot = 0, max_slot do
			self.slot_list[ts_index][slot].level = MsgAdapter.ReadChar()
		end
	end
end

-- 天神灵核单个物品信息
SCTianShenLingHeSingleItemInfo = SCTianShenLingHeSingleItemInfo or BaseClass(BaseProtocolStruct)
function SCTianShenLingHeSingleItemInfo:__init()
	self.msg_type = 9223
end

function SCTianShenLingHeSingleItemInfo:Decode()
	local index = MsgAdapter.ReadInt()
	local data = TianShenLingHeItemRead()
	data.index = index
	self.change_data = data
end

-- 天神灵核单个槽位信息
SCTianShenLingHeSingleSlotInfo = SCTianShenLingHeSingleSlotInfo or BaseClass(BaseProtocolStruct)
function SCTianShenLingHeSingleSlotInfo:__init()
	self.msg_type = 9224
end

function SCTianShenLingHeSingleSlotInfo:Decode()
	self.ts_index = MsgAdapter.ReadInt()
	self.slot = MsgAdapter.ReadInt()
	local data = TianShenLingHeItemRead()
	data.level = MsgAdapter.ReadChar()
	self.change_data = data
	-- MsgAdapter.ReadChar()
	-- MsgAdapter.ReadChar()
	-- MsgAdapter.ReadChar()
	-- MsgAdapter.ReadChar()
end

-- 分解请求
CSTianShenLingHeResloveOpera = CSTianShenLingHeResloveOpera or BaseClass(BaseProtocolStruct)
function CSTianShenLingHeResloveOpera:__init()
	self.msg_type = 9225
end

function CSTianShenLingHeResloveOpera:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(#self.reslove_list)
	for i = 1, #self.reslove_list do
		MsgAdapter.WriteInt(self.reslove_list[i].index or -1)
	end
end

------------------------------------------阵地战------------------------------------
CSCrossLandWarOperate = CSCrossLandWarOperate or BaseClass(BaseProtocolStruct)
function CSCrossLandWarOperate:__init()
	self.msg_type = 9206
end

function CSCrossLandWarOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
	MsgAdapter.WriteInt(self.param4)
end

SCCrossLandWarBaseInfo = SCCrossLandWarBaseInfo or BaseClass(BaseProtocolStruct)
function SCCrossLandWarBaseInfo:__init()
	self.msg_type = 9207
end

function SCCrossLandWarBaseInfo:Decode()
	self.cycle_reset_time = MsgAdapter.ReadUInt()
	self.cycle_group_index = MsgAdapter.ReadInt()
	self.shop_score = MsgAdapter.ReadLL()
	self.devote = MsgAdapter.ReadLL()
	self.devote_reward_flag = MsgAdapter.ReadInt()

	local order_reward_flag = {}
	for i = 1, 16 do
		order_reward_flag[i] = MsgAdapter.ReadUChar()
	end
	self.order_reward_flag = order_reward_flag

	local order_added_reward_flag = {}
	for i = 1, 16 do
		order_added_reward_flag[i] = MsgAdapter.ReadUChar()
	end
	self.order_added_reward_flag = order_added_reward_flag

	self.special_flag = bit:d2b_l2h(MsgAdapter.ReadInt(), nil, true)
	self.capture_reward_flag = bit:d2b_l2h(MsgAdapter.ReadLL(), nil, true)   
	self.tired = MsgAdapter.ReadInt()                

	local shop_buy_list = {}  
	for i = 0, 99 do
		shop_buy_list[i] = MsgAdapter.ReadUShort()
	end
	self.shop_buy_list = shop_buy_list
end

local function GetMsgCrossLandWarLandInfo()
	local data = {}
	data.owner_camp = MsgAdapter.ReadInt()
	data.tem_owner_camp = MsgAdapter.ReadInt()  -- 临时占领
	data.monster_die_flag = bit:d2b_l2h(MsgAdapter.ReadLL(), nil, true)

	local camp_score_list = {}  -- 占领积分
	for i = 0, 4 do
		camp_score_list[i] = MsgAdapter.ReadLL()
	end
	data.camp_score_list = camp_score_list

	return data
end

SCCrossLandWarRoomInfo = SCCrossLandWarRoomInfo or BaseClass(BaseProtocolStruct)	-- 房间信息 场景外请求
function SCCrossLandWarRoomInfo:__init()
	self.msg_type = 9208   
end

function SCCrossLandWarRoomInfo:Decode()
	self.room_id = MsgAdapter.ReadInt()
	self.group_index = MsgAdapter.ReadInt()  
	self.open_time = MsgAdapter.ReadUInt()

	local land_info_list = {}
	for i = 0, 14 do
		land_info_list[i] = GetMsgCrossLandWarLandInfo()
	end
	self.land_info_list = land_info_list
end

local function GetMsgLandMonsterInfo()
	local data = {}
	data.owner_camp = MsgAdapter.ReadInt()
	data.hp_scale = MsgAdapter.ReadInt()

	local camp_player_num = 0
	local camp_player_num_list = {}
	for i = 0, 4 do
		camp_player_num_list[i] = MsgAdapter.ReadInt()
		camp_player_num = camp_player_num + camp_player_num_list[i]
	end

	data.camp_player_num_list = camp_player_num_list
	data.camp_player_num = camp_player_num
	return data
end

SCCrossLandWarLandInfo = SCCrossLandWarLandInfo or BaseClass(BaseProtocolStruct)
function SCCrossLandWarLandInfo:__init()
	self.msg_type = 9209
end

function SCCrossLandWarLandInfo:Decode()
	self.land_seq = MsgAdapter.ReadInt()
	self.owner_camp = MsgAdapter.ReadInt()  -- 没用到 走9208 owner_camp
	self.tem_owner_camp = MsgAdapter.ReadInt()  -- 零时占领
	self.monster_die_flag = bit:d2b_l2h(MsgAdapter.ReadLL(), nil, true)

	local total_num = 0
	local camp_total_num_list = {}

	local monster_info_list = {}
	for i = 0, 14 do
		monster_info_list[i] = GetMsgLandMonsterInfo()
		total_num = total_num + monster_info_list[i].camp_player_num

		local camp_player_num_list = monster_info_list[i].camp_player_num_list
		for k, v in pairs(camp_player_num_list) do
			camp_total_num_list[k] = camp_total_num_list[k] or 0
			camp_total_num_list[k] = camp_total_num_list[k] + v
		end
	end

	self.total_num = total_num
	self.monster_info_list = monster_info_list
	self.camp_total_num_list = camp_total_num_list
end 

local function GetMsgRecord()
	local data = {}
	data.kill_camp = MsgAdapter.ReadInt()
	data.time = MsgAdapter.ReadUInt()
	return data
end

SCCrossLandWarMonsterRecordInfo = SCCrossLandWarMonsterRecordInfo or BaseClass(BaseProtocolStruct)
function SCCrossLandWarMonsterRecordInfo:__init()
	self.msg_type = 9210
end

function SCCrossLandWarMonsterRecordInfo:Decode()
	self.land_seq = MsgAdapter.ReadInt()
	self.monster_seq = MsgAdapter.ReadInt()
	self.count = MsgAdapter.ReadInt()

	local record_list = {}
	for i = 1, self.count do
		record_list[i] = GetMsgRecord()
		record_list[i].land_seq = self.land_seq
		record_list[i].monster_seq = self.monster_seq
	end
	self.record_list = record_list
end

local function GetMsgCrossLandWarMonsterInfo(land_seq)
	local data = {}
	data.land_seq = land_seq
	data.seq = MsgAdapter.ReadInt()
	data.monster_id = MsgAdapter.ReadInt()
	data.is_die = MsgAdapter.ReadInt()
	data.owner_camp = MsgAdapter.ReadInt()
	local camp_hurt_list = {}   --伤害列表  
	for i = 0, 4 do
		camp_hurt_list[i] = MsgAdapter.ReadLL()
	end
	data.camp_hurt_list = camp_hurt_list

	return data
end

SCCrossLandWarMonsterInfo = SCCrossLandWarMonsterInfo or BaseClass(BaseProtocolStruct)
function SCCrossLandWarMonsterInfo:__init()
	self.msg_type = 9211
end

function SCCrossLandWarMonsterInfo:Decode()
	self.land_seq = MsgAdapter.ReadInt()
	self.count = MsgAdapter.ReadInt()

	local monster_info_list = {}
	for i = 1, self.count do
		local data = GetMsgCrossLandWarMonsterInfo(self.land_seq)
		monster_info_list[data.seq] = data
	end
	
	self.monster_info_list = monster_info_list
end

SCCrossLandWarMonsterUpdate = SCCrossLandWarMonsterUpdate or BaseClass(BaseProtocolStruct)
function SCCrossLandWarMonsterUpdate:__init()
	self.msg_type = 9212
end

function SCCrossLandWarMonsterUpdate:Decode()
	self.land_seq = MsgAdapter.ReadInt()
	self.monster_info = GetMsgCrossLandWarMonsterInfo(self.land_seq)
end

local function GetMsgCrossLandWarRankItem()
	local data = {}
	data.uuid = MsgAdapter.ReadUUID()
	data.name = MsgAdapter.ReadStrN(32)
	data.sex = MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
	data.is_hide_vip = MsgAdapter.ReadChar()
	data.rank_id = MsgAdapter.ReadChar()
	data.camp = MsgAdapter.ReadInt()
	data.cap = MsgAdapter.ReadLL()
	data.avatar = MsgAdapter.ReadLL()
	data.vip_level = MsgAdapter.ReadInt()
	data.usid = MsgAdapter.ReadUniqueServerID()
	data.rank_value = MsgAdapter.ReadLL()
	data.prof = MsgAdapter.ReadInt()
	return data
end

SCCrossLandWarRankInfo = SCCrossLandWarRankInfo or BaseClass(BaseProtocolStruct)

function SCCrossLandWarRankInfo:__init()
	self.msg_type = 9213
end

function SCCrossLandWarRankInfo:Decode()
	self.rank_type = MsgAdapter.ReadInt()
	self.rank_param = MsgAdapter.ReadInt()
	self.count = MsgAdapter.ReadInt()

	local rank_item_list = {}
	for i = 1, self.count do
		rank_item_list[i] = GetMsgCrossLandWarRankItem()
	end

	self.rank_item_list = rank_item_list
end

local function GetWarSituationMsgItem()
	local data = {}
	data.land_seq = MsgAdapter.ReadChar()
	data.monster_seq = MsgAdapter.ReadChar()
	data.player_num = MsgAdapter.ReadUShort()

	return data
end

SCCrossLandWarSituationInfo = SCCrossLandWarSituationInfo or BaseClass(BaseProtocolStruct)

function SCCrossLandWarSituationInfo:__init()
	self.msg_type = 9214
end

function SCCrossLandWarSituationInfo:Decode()
	self.camp = MsgAdapter.ReadInt()
	self.count = MsgAdapter.ReadInt()

	local item_list = {}
	for i = 1, self.count do
		item_list[i] = GetWarSituationMsgItem()
	end
	self.item_list = item_list
end

local function GetMsgCrossLandWarCampInfo()
	local data = {}
	data.camp = MsgAdapter.ReadInt()
	data.param = MsgAdapter.ReadUniqueServerID()   -- usid
	data.guild_id = MsgAdapter.ReadInt()           -- 仙盟Id
	return data
end

SCCrossLandWarCampInfo = SCCrossLandWarCampInfo or BaseClass(BaseProtocolStruct)
function SCCrossLandWarCampInfo:__init()
	self.msg_type = 9215
end

function SCCrossLandWarCampInfo:Decode()
	self.count = MsgAdapter.ReadInt()

	local camp_info_list = {}
	for i = 1, self.count do
		camp_info_list[i] = GetMsgCrossLandWarCampInfo()
	end
	self.camp_info_list = camp_info_list
end

---------------------------------------天山修炼（历练副本） start---------------------
-- 天山修炼（历练副本）操作请求
CSExpWestOperate = CSExpWestOperate or BaseClass(BaseProtocolStruct)
function CSExpWestOperate:__init()
    self.msg_type = 9216
end

function CSExpWestOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

-- 副本信息
SCExpWestBaseInfo = SCExpWestBaseInfo or BaseClass(BaseProtocolStruct)
function SCExpWestBaseInfo:__init()
	self.msg_type = 9217
end

function SCExpWestBaseInfo:Decode()
	self.level = MsgAdapter.ReadInt()							--副本等级
	self.wave_pass_reward_flag = bit:ll2b_two(MsgAdapter.ReadUInt(), MsgAdapter.ReadUInt())			--副本通关波数信息
end

-- 场景信息
SCExpWestSceneInfo = SCExpWestSceneInfo or BaseClass(BaseProtocolStruct)
function SCExpWestSceneInfo:__init()
	self.msg_type = 9218
end

function SCExpWestSceneInfo:Decode()
	self.level = MsgAdapter.ReadInt()							--副本等级
	self.wave = MsgAdapter.ReadInt()							--副本波数
	self.reserve_sh = MsgAdapter.ReadShort()						
	self.is_end = MsgAdapter.ReadChar()							--当前波数是否结束
	self.is_pass = MsgAdapter.ReadChar()						--当前波数是否通过
	self.wave_end_time = MsgAdapter.ReadUInt()					--波数结束时间
	self.kick_out_time = MsgAdapter.ReadUInt()					--踢出时间
	self.fb_end_time = MsgAdapter.ReadUInt()					--副本结束时间
	self.monster_count = MsgAdapter.ReadInt()					--副本当前波次剩余怪数量
	self.pass_time = MsgAdapter.ReadUInt()						--副本通关
end

-- 排行信息
SCExpWestRankInfo = SCExpWestRankInfo or BaseClass(BaseProtocolStruct)
function SCExpWestRankInfo:__init()
	self.msg_type = 9219
end

function SCExpWestRankInfo:Decode()
	self.level = MsgAdapter.ReadInt()							--副本等级
	self.rank_item_list = {}

	for i = 1, 3 do
		self.rank_item_list[i] = ProtocolStruct.ReadExpWestRankItem()
	end
end

-- 场景信息
SCExpWestCardInfo = SCExpWestCardInfo or BaseClass(BaseProtocolStruct)
function SCExpWestCardInfo:__init()
	self.msg_type = 9226
end

function SCExpWestCardInfo:Decode()
	self.card_count = MsgAdapter.ReadInt()						--当前卡牌数量
	self.card_item_list = {}

	for i = 1, self.card_count do
		self.card_item_list[i] = {}
		self.card_item_list[i].choose_seq = MsgAdapter.ReadShort()	
		self.card_item_list[i].choose_level = MsgAdapter.ReadShort()	
		
		self.card_item_list[i].random_seq_list = {}
		for j = 1, 3 do
			self.card_item_list[i].random_seq_list[j] = {}
			self.card_item_list[i].random_seq_list[j].seq = MsgAdapter.ReadShort()
			self.card_item_list[i].random_seq_list[j].level = MsgAdapter.ReadShort()
		end
	end
end

-- 场景信息
SCExpWestSkillInfo = SCExpWestSkillInfo or BaseClass(BaseProtocolStruct)
function SCExpWestSkillInfo:__init()
	self.msg_type = 9227
end

function SCExpWestSkillInfo:Decode()
	self.skill_power = MsgAdapter.ReadInt()						--技能能量
end

---------------------------------------天山修炼（历练副本） end---------------------

---------------------------------------通用记录保存 start---------------------
-- 请求保存操作
CSCommonGuideOperate = CSCommonGuideOperate or BaseClass(BaseProtocolStruct)
function CSCommonGuideOperate:__init()
    self.msg_type = 9228
end

function CSCommonGuideOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

-- 保存信息
SCCommonGuideInfo = SCCommonGuideInfo or BaseClass(BaseProtocolStruct)
function SCCommonGuideInfo:__init()
	self.msg_type = 9229
end

function SCCommonGuideInfo:Decode()
	self.guide_flag = {}
	
	local bit_index = 0
	for i = 1, 32 do
		local list = bit:d2b8_two(MsgAdapter.ReadChar())
		for j = 0, 7 do
			self.guide_flag[bit_index] = list[j]
			bit_index = bit_index + 1
		end
	end
end

-- 保存信息更新
SCCommonGuideUpdate = SCCommonGuideUpdate or BaseClass(BaseProtocolStruct)
function SCCommonGuideUpdate:__init()
	self.msg_type = 9230
end

function SCCommonGuideUpdate:Decode()
	self.bit = MsgAdapter.ReadInt()						--bit 位
	self.is_set = MsgAdapter.ReadInt()					--是否设置
end
---------------------------------------通用记录保存 start---------------------

SCCrossLandWarVieStart = SCCrossLandWarVieStart or BaseClass(BaseProtocolStruct)
function SCCrossLandWarVieStart:__init()
	self.msg_type = 9231
end

function SCCrossLandWarVieStart:Decode()
	
end

SCCrossLandWarCallInfo = SCCrossLandWarCallInfo or BaseClass(BaseProtocolStruct)
function SCCrossLandWarCallInfo:__init()
	self.msg_type = 9232
end

function SCCrossLandWarCallInfo:Decode()
	self.call_land_seq = MsgAdapter.ReadInt()
	self.call_cd_time = MsgAdapter.ReadUInt()
end


-------------------阵地战单人副本--------------
CSLandWarFBOperate = CSLandWarFBOperate or BaseClass(BaseProtocolStruct)
function CSLandWarFBOperate:__init()
    self.msg_type = 9233
end

function CSLandWarFBOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
	MsgAdapter.WriteInt(self.param4)
end

SCLandWarFBBaseInfo = SCLandWarFBBaseInfo or BaseClass(BaseProtocolStruct)
function SCLandWarFBBaseInfo:__init()
	self.msg_type = 9234
end

function SCLandWarFBBaseInfo:Decode()
	self.stage = MsgAdapter.ReadInt()
	self.monster_die_flag = bit:d2b_l2h(MsgAdapter.ReadLL(), nil, true)
	self.pass_reward_flag = bit:d2b_l2h(MsgAdapter.ReadInt(), nil, true)
end

---------------------------- 跨服空战协议 start ----------------------------
-- 空战请求操作 CROSS_AIR_WAR_OPERATE_TYPE
CSCrossAirWarOperate = CSCrossAirWarOperate or BaseClass(BaseProtocolStruct)
function CSCrossAirWarOperate:__init()
    self.msg_type = 9235
end

function CSCrossAirWarOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

-- 空战基础信息
SCCrossAirWarBaseInfo = SCCrossAirWarBaseInfo or BaseClass(BaseProtocolStruct)
function SCCrossAirWarBaseInfo:__init()
	self.msg_type = 9236
end

function SCCrossAirWarBaseInfo:Decode()
	self.monster_seq 				= MsgAdapter.ReadInt()		-- 怪物seq
	self.monster_num				= MsgAdapter.ReadUInt() 	-- 当前怪物剩余个数
	self.seq_start_time				= MsgAdapter.ReadUInt()		-- 当前seq开始的事件戳
end

-- 空战拍卖信息
SCCrossAirWarAuctionInfo = SCCrossAirWarAuctionInfo or BaseClass(BaseProtocolStruct)
function SCCrossAirWarAuctionInfo:__init()
	self.msg_type = 9237
end

function SCCrossAirWarAuctionInfo:Decode()
	self.auction_grade 					= MsgAdapter.ReadInt()		-- 拍卖档位 grade
	self.auction_round 					= MsgAdapter.ReadInt()		-- 拍卖轮次
end

-- 空战角色基础信息
SCCrossAirWarRoleBaseInfo = SCCrossAirWarRoleBaseInfo or BaseClass(BaseProtocolStruct)
function SCCrossAirWarRoleBaseInfo:__init()
	self.msg_type = 9238
end

function SCCrossAirWarRoleBaseInfo:Decode()
	self.score 					= MsgAdapter.ReadLL() 					-- 玩家分数
	self.score_reward_flag_list = bit:d2b(MsgAdapter.ReadInt())			-- 玩家分数奖励标识
	self.gather_flag_list 		= bit:d2b(MsgAdapter.ReadInt())			-- 采集标识
	self.auction_reward_flag 	= MsgAdapter.ReadChar()
	self.can_auction_reward_flag = MsgAdapter.ReadChar()
	self.re_sh 					= MsgAdapter.ReadShort()
end

-- 空战场景信息
SCCrossAirWarSceneInfo = SCCrossAirWarSceneInfo or BaseClass(BaseProtocolStruct)
function SCCrossAirWarSceneInfo:__init()
	self.msg_type = 9244
end

function SCCrossAirWarSceneInfo:Decode()
	self.stage 					= MsgAdapter.ReadInt() 			-- 当前阶段
	self.m_status 				= MsgAdapter.ReadInt() 			-- 当前状态 
	self.m_next_status_time		= MsgAdapter.ReadUInt()			-- 下一个状态的时间戳	
	self.pre_status_time		= MsgAdapter.ReadUInt()			-- 准备状态时间
end

-- 空战活动状态
SCCrossAirWarActivityStatus = SCCrossAirWarActivityStatus or BaseClass(BaseProtocolStruct)
function SCCrossAirWarActivityStatus:__init()
	self.msg_type = 9245
end

function SCCrossAirWarActivityStatus:Decode()
	self.m_status 				= MsgAdapter.ReadInt() 			-- 活动状态 
end

-- 空战操作结果
SCCrossAirWarOperateResult = SCCrossAirWarOperateResult or BaseClass(BaseProtocolStruct)
function SCCrossAirWarOperateResult:__init()
	self.msg_type = 9246
end

function SCCrossAirWarOperateResult:Decode()
	self.operate_type 				= MsgAdapter.ReadInt() 		-- 活动状态 
	self.param1 				= MsgAdapter.ReadInt() 			-- 活动状态 
	self.param2 				= MsgAdapter.ReadInt() 			-- 活动状态 
	self.param3 				= MsgAdapter.ReadInt() 			-- 活动状态 
end

-- 空战拍卖物品信息
SCCrossAirWarAuctionItemInfo = SCCrossAirWarAuctionItemInfo or BaseClass(BaseProtocolStruct)
function SCCrossAirWarAuctionItemInfo:__init()
	self.msg_type = 9262
end

function SCCrossAirWarAuctionItemInfo:Decode()
	self.count = MsgAdapter.ReadInt()
	self.auction_info_list = {}
	
	if self.count > 0 then
		for i = 1, self.count do
			self.auction_info_list[i] = ProtocolStruct.ReadAirAuctionItem()
		end
	end
end

-- 空战拍卖物品信息
SCCrossAirWarAuctionItemUpdate = SCCrossAirWarAuctionItemUpdate or BaseClass(BaseProtocolStruct)
function SCCrossAirWarAuctionItemUpdate:__init()
	self.msg_type = 9263
end

function SCCrossAirWarAuctionItemUpdate:Decode()
	local server_index = MsgAdapter.ReadInt()
	self.index = server_index + 1
	self.auction_info = ProtocolStruct.ReadAirAuctionItem()
end
---------------------------- 跨服空战协议  end  ----------------------------

-- 渡劫邀请
SCOrdealViewInvite = SCOrdealViewInvite or BaseClass(BaseProtocolStruct)
function SCOrdealViewInvite:__init()
	self.msg_type = 9264
end

function SCOrdealViewInvite:Decode()
	self.invite_uid = MsgAdapter.ReadInt()
	self.invite_name = MsgAdapter.ReadName()
end

-- 正在渡劫人数
SCOrdealingInfo = SCOrdealingInfo or BaseClass(BaseProtocolStruct)
function SCOrdealingInfo:__init()
	self.msg_type = 9265
end

function SCOrdealingInfo:Decode()
	self.ordeal_num = MsgAdapter.ReadInt()
	self.ordeal_range_num = MsgAdapter.ReadInt()
end


---------------------------- 跨服boss入侵协议 start ----------------------------
CSCrossBossStrikeOperate = CSCrossBossStrikeOperate or BaseClass(BaseProtocolStruct)
function CSCrossBossStrikeOperate:__init()
    self.msg_type = 9239
end

function CSCrossBossStrikeOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

SCCrossBossStrikeBaseInfo = SCCrossBossStrikeBaseInfo or BaseClass(BaseProtocolStruct)
function SCCrossBossStrikeBaseInfo:__init()
	self.msg_type = 9240
end

function SCCrossBossStrikeBaseInfo:Decode()
	self.group_index = MsgAdapter.ReadInt()
	self.boss_color = MsgAdapter.ReadInt()
	self.boss_num = MsgAdapter.ReadInt()
	self.status = MsgAdapter.ReadInt()
end

SCCrossBossStrikeSceneInfo = SCCrossBossStrikeSceneInfo or BaseClass(BaseProtocolStruct)
function SCCrossBossStrikeSceneInfo:__init()
	self.msg_type = 9241
end

function SCCrossBossStrikeSceneInfo:Decode()
	self.status = MsgAdapter.ReadInt()
	self.next_status_time = MsgAdapter.ReadUInt()
	self.group_index = MsgAdapter.ReadInt()
	self.boss_color = MsgAdapter.ReadInt() 
	self.question_num = MsgAdapter.ReadInt() 
	self.question_seq = MsgAdapter.ReadInt()  
	self.hurt_top_uuid = MsgAdapter.ReadUUID()
	self.hurt_top_usid = MsgAdapter.ReadUniqueServerID()
	self.hurt_top_guild_id = MsgAdapter.ReadInt()
	self.hurt_top_name = MsgAdapter.ReadStrN(32)
end

SCCrossBossStrikeRoleInfo = SCCrossBossStrikeRoleInfo or BaseClass(BaseProtocolStruct)
function SCCrossBossStrikeRoleInfo:__init()
	self.msg_type = 9242
end

function SCCrossBossStrikeRoleInfo:Decode()
	self.get_exp = MsgAdapter.ReadLL()
	self.get_xiuwei = MsgAdapter.ReadLL()
	self.get_beast_exp = MsgAdapter.ReadLL()
	self.get_coin = MsgAdapter.ReadLL()
	self.right_answer_times = MsgAdapter.ReadShort()
	self.gather_flag = MsgAdapter.ReadChar()
	self.can_gather = MsgAdapter.ReadChar()
	self.coin_guwu_times = MsgAdapter.ReadShort()
	self.gold_guwu_times = MsgAdapter.ReadShort()
end

SCCrossBossStrikeRoleBaseInfo = SCCrossBossStrikeRoleBaseInfo or BaseClass(BaseProtocolStruct)
function SCCrossBossStrikeRoleBaseInfo:__init()
	self.msg_type = 9243
end

function SCCrossBossStrikeRoleBaseInfo:Decode()
	self.rmb_buy_flag = MsgAdapter.ReadChar()
	self.auto_guwu_flag = MsgAdapter.ReadChar()
	self.reserve_sh = MsgAdapter.ReadShort()
end

SCCrossBossStrikeHurtRankInfo = SCCrossBossStrikeHurtRankInfo or BaseClass(BaseProtocolStruct)
function SCCrossBossStrikeHurtRankInfo:__init()
	self.msg_type = 9247
end

function SCCrossBossStrikeHurtRankInfo:Decode()
	self.info_count = MsgAdapter.ReadInt()

	local hurt_info = {}
	for i = 1, self.info_count do
		local data = {}
		data.uuid = MsgAdapter.ReadUUID()
		data.usid = MsgAdapter.ReadUniqueServerID()
		data.name = MsgAdapter.ReadStrN(32) 
		data.level = MsgAdapter.ReadInt()
		data.vip_level = MsgAdapter.ReadShort()
		data.sex = MsgAdapter.ReadChar()
		data.is_hide_vip = MsgAdapter.ReadChar()
		data.hurt = MsgAdapter.ReadLL()
		data.rank = MsgAdapter.ReadInt()
		table.insert(hurt_info, data)
	end

	self.hurt_info = hurt_info
end

---------------------------- 跨服boss入侵协议 end ------------------------------

---------------------------- 渡劫协议 start ----------------------------
CSOrdealOperate = CSOrdealOperate or BaseClass(BaseProtocolStruct)
function CSOrdealOperate:__init()
    self.msg_type = 9248
end

function CSOrdealOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

SCOrdealBaseInfo = SCOrdealBaseInfo or BaseClass(BaseProtocolStruct)
function SCOrdealBaseInfo:__init()
	self.msg_type = 9249
end

function SCOrdealBaseInfo:Decode()
	self.level = MsgAdapter.ReadInt()
	self.fail_times = MsgAdapter.ReadInt()
	self.ordeal_damage_time = MsgAdapter.ReadUInt()
	self.last_ordeal_end_time = MsgAdapter.ReadUInt()

	self.fail_reduce_time = MsgAdapter.ReadUInt()
	self.fail_reduce_per = MsgAdapter.ReadInt()
	self.watch_reward_times = MsgAdapter.ReadInt()
	self.box_reward_times = MsgAdapter.ReadInt()
end

local function GetMsgThunderItem()

	local info = {}
	info.thunder_seq = MsgAdapter.ReadInt()
	info.thunder_offset_ms = MsgAdapter.ReadInt() -- 偏移的毫秒,需要和开始时间一起计算
	return info
end

SCOrdealInfo = SCOrdealInfo or BaseClass(BaseProtocolStruct)
function SCOrdealInfo:__init()
	self.msg_type = 9250
end

function SCOrdealInfo:Decode()
	self.vitality = MsgAdapter.ReadInt()
	self.strength = MsgAdapter.ReadInt()
	self.ordeal_start_time = MsgAdapter.ReadUInt()  -- 开始时间
	self.next_thunder_index = MsgAdapter.ReadInt()  -- 下一道雷的index
	self.thunder_item_list = {}
	-- 雷电数量修改要同步DujieWGData:SetOrdealInfo(protocol)中的数量
	for i = 1, 30 do
		self.thunder_item_list[i] = GetMsgThunderItem()
	end
	self.skill_seq = MsgAdapter.ReadInt()
	self.skill_active_time = MsgAdapter.ReadUInt()      --技能使用时间
	self.now_time = MsgAdapter.ReadUInt()      --当前时间 用来计算雷电时间
	self.skill_cd_time_list = {}
	for i = 1, 5 do
		self.skill_cd_time_list[i] = MsgAdapter.ReadUInt()
	end
	self.ordeal_show_role_num = MsgAdapter.ReadInt()

end

---------------------------- 渡劫协议 end ------------------------------

---------------------------- 灵根协议 start ----------------------------
local MAX_MONEY_TYPE_COUNT = 5
-- 灵根基础信息 
SCOrdealBodyBaseInfo = SCOrdealBodyBaseInfo or BaseClass(BaseProtocolStruct)
function SCOrdealBodyBaseInfo:__init()
	self.msg_type = 9251
end

function SCOrdealBodyBaseInfo:Decode()
	self.ordeal_body_money_list = {}
	-- 货币数量
	for i = 0, MAX_MONEY_TYPE_COUNT - 1 do
		self.ordeal_body_money_list[i] = MsgAdapter.ReadLL()
	end
end

-- 灵根 天赋子节点
local function ReadOrdealBodyTalentCellInfo()
	local info = {}
	info.element = MsgAdapter.ReadChar()			-- 元素索引
	info.element_count = MsgAdapter.ReadChar()		-- 元素数量
	info.color = MsgAdapter.ReadChar()				-- 元素品质
	info.is_active = MsgAdapter.ReadChar()			-- 是否激活
	info.skill_seq = MsgAdapter.ReadInt()			-- 元素技能索引
	return info
end

-- 灵根 天赋父节点
local MAX_ORDEAL_TALENT_COUNT = 50
local function ReadOrdealBodyInfo()
	local info = {}
	info.is_active 		= MsgAdapter.ReadChar()		-- 是否激活灵根
	local re_ch 		= MsgAdapter.ReadChar()		-- 预留1
	local re_sh 		= MsgAdapter.ReadShort()	-- 预留2
	info.score 			= MsgAdapter.ReadInt()		-- 灵根评分
	-- local active_flag 	= MsgAdapter.ReadLL()		-- 激活状态
	
	info.active_flag = bit:ll2b_two(MsgAdapter.ReadUInt(), MsgAdapter.ReadUInt())
	-- info.active_flag = bit:d2b_l2h(active_flag, nil, true)
	local element_count_list = {}

	local talent_item_list = {}
	local active_count = 0
	for i = 0, MAX_ORDEAL_TALENT_COUNT - 1 do
		local talent_info = ReadOrdealBodyTalentCellInfo()
		if talent_info.is_active > 0 then
			active_count = active_count + 1
		end
		if talent_info.element ~= -1 then
			if element_count_list[talent_info.element] then
				element_count_list[talent_info.element] = element_count_list[talent_info.element] + talent_info.element_count
			else
				element_count_list[talent_info.element] = talent_info.element_count
			end
		end
		talent_item_list[i] = talent_info
	end
	info.talent_item_list = talent_item_list
	info.active_count = active_count
	info.element_count_list = element_count_list

	return info
end

-- 灵根全部信息更新 
local MAX_BODY_COUNT = 10
SCOrdealBodyInfo = SCOrdealBodyInfo or BaseClass(BaseProtocolStruct)
function SCOrdealBodyInfo:__init()
	self.msg_type = 9252
end

function SCOrdealBodyInfo:Decode()
	self.ordeal_body_item_list = {}
	for i = 0, MAX_BODY_COUNT - 1 do
		self.ordeal_body_item_list[i] = ReadOrdealBodyInfo()
	end
end

-- 灵根指定信息更新 
SCOrdealBodyUpdate = SCOrdealBodyUpdate or BaseClass(BaseProtocolStruct)
function SCOrdealBodyUpdate:__init()
	self.msg_type = 9253
end

function SCOrdealBodyUpdate:Decode()
	self.body_seq = MsgAdapter.ReadInt()
	self.talent_seq = MsgAdapter.ReadInt()
	self.single_body_item_info = ReadOrdealBodyInfo()
end

-- 灵根属性
SCOrdealBodyAttrUpdate = SCOrdealBodyAttrUpdate or BaseClass(BaseProtocolStruct)
function SCOrdealBodyAttrUpdate:__init()
	self.msg_type = 9254
end

function SCOrdealBodyAttrUpdate:Decode()
	self.body_seq = MsgAdapter.ReadInt()
	self.count = MsgAdapter.ReadInt()
	self.attr_list = {}
	for i = 1, self.count do
		self.attr_list["attr_id"..i] = MsgAdapter.ReadInt()			-- 属性id
		self.attr_list["attr_value"..i] = MsgAdapter.ReadLL()	-- 属性值
	end
end

---------------------------- 灵根协议  end  ----------------------------

----------------------------雷法协议 start ----------------------------
CSThunderOperate = CSThunderOperate or BaseClass(BaseProtocolStruct)
function CSThunderOperate:__init()
    self.msg_type = 9255
end

function CSThunderOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end


local function ReadThunderItem(type)
	local info = {}

	local suit_active_flag = MsgAdapter.ReadInt()
	info.suit_active_flag_list = bit:d2b_l2h(suit_active_flag, nil, true)
	info.part_item_list = {}
	for i = 0, 4 do
		local data = {}
		data.item_id = MsgAdapter.ReadUShort()
		data.grade = MsgAdapter.ReadShort()
		data.level = MsgAdapter.ReadInt()
		data.exp = MsgAdapter.ReadLL()
		data.star = 0
		data.seq = type
		data.part = i
		if data.item_id > 0 then
			local equip_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(data.item_id)
			data.star = equip_cfg and equip_cfg.star or 0
		end

		info.part_item_list[i] = data
	end

	return info
end

SCThunderItemInfo = SCThunderItemInfo or BaseClass(BaseProtocolStruct)
function SCThunderItemInfo:__init()
	self.msg_type = 9256
end

function SCThunderItemInfo:Decode()
	self.thunder_item_list = {}

	for i = 0, 1 do
		self.thunder_item_list[i] = ReadThunderItem(i)
	end
end


SCThunderItemUpdate = SCThunderItemUpdate or BaseClass(BaseProtocolStruct)
function SCThunderItemUpdate:__init()
	self.msg_type = 9257
end

function SCThunderItemUpdate:Decode()
	self.change_data = {}

	local data = {}
	data.seq = MsgAdapter.ReadInt()
	data.thunder_item = ReadThunderItem(data.seq)
	self.change_data = data
end

local function GetScThunderBagGridItem()
    local grid_item = {}
    grid_item.index = MsgAdapter.ReadInt()
    grid_item.item_id = MsgAdapter.ReadUShort()
    grid_item.reserve_sh = MsgAdapter.ReadShort()
    grid_item.num = MsgAdapter.ReadInt()
    grid_item.is_bind = MsgAdapter.ReadChar()
    grid_item.param1 = MsgAdapter.ReadChar()
    grid_item.param2 = MsgAdapter.ReadChar()
    grid_item.param3 = MsgAdapter.ReadChar()

	grid_item.seq = 0
	grid_item.part = 0
	grid_item.star = 0
	local equip_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(grid_item.item_id)
	if equip_cfg then
		grid_item.seq = equip_cfg.seq
		grid_item.part = equip_cfg.part
		grid_item.star = equip_cfg.star
	end
	grid_item.sort_star = -grid_item.star --排序特殊处理
    return grid_item
end

SCThunderBagInfo = SCThunderBagInfo or BaseClass(BaseProtocolStruct) -- 背包信息
function SCThunderBagInfo:__init()
    self.msg_type = 9258
end

function SCThunderBagInfo:Decode()
    local count = MsgAdapter.ReadInt()
    self.thunder_grid_list = {}
    for i = 1, count do
        local bag_data = GetScThunderBagGridItem()
        self.thunder_grid_list[bag_data.index] = bag_data
    end
end

SCThunderBagChangeInfo = SCThunderBagChangeInfo or BaseClass(BaseProtocolStruct) -- 背包信息变化
function SCThunderBagChangeInfo:__init()
    self.msg_type = 9259
end

function SCThunderBagChangeInfo:Decode()
    self.change_info = GetScThunderBagGridItem()
end


CSThunderEquipUpStar = CSThunderEquipUpStar or BaseClass(BaseProtocolStruct)
function CSThunderEquipUpStar:__init()
    self.msg_type = 9260
	self.compose_count = 0 --合成总数量
	self.compose_list = {}
	-- self.seq = 0     --类型(如果是背包传-1，是槽位传入类型)
    -- self.stuff_count = 0   -- 材料数量（使用的格子数量）
	-- self.param = 0   -- （如果是是背包传入背包index， 是槽位传槽位索引） 
    -- self.stuff_item_list = {}
end

function CSThunderEquipUpStar:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.compose_count)

	for i = 1, self.compose_count do
		local data = self.compose_list[i]
		if data then
			MsgAdapter.WriteChar(data.seq)
			MsgAdapter.WriteChar(data.stuff_count)
			MsgAdapter.WriteShort(data.param)
			local stuff_item_list = data.stuff_item_list

			for i = 1, 10 do --最多10个背包索引
				local bag_index = stuff_item_list[i] and stuff_item_list[i].bag_index or -1
				local item_num = stuff_item_list[i] and stuff_item_list[i].item_num or 0
	
				MsgAdapter.WriteShort(bag_index)
				MsgAdapter.WriteShort(item_num)
			end
		end
	end
end

CSThunderEquipDecompos = CSThunderEquipDecompos or BaseClass(BaseProtocolStruct)
function CSThunderEquipDecompos:__init()
    self.msg_type = 9261
end

function CSThunderEquipDecompos:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.seq)
	MsgAdapter.WriteInt(self.part)
	MsgAdapter.WriteInt(self.compose_count)

	for i = 1, self.compose_count do
		local data = self.compose_list[i]
		if data then
			MsgAdapter.WriteShort(data.item_id)
			MsgAdapter.WriteShort(data.bag_index)
			MsgAdapter.WriteInt(data.item_num)
		end
	end
end
----------------------------雷法协议 end ----------------------------
