
--------------------------------------------------
--引导时禁用点击层
--------------------------------------------------
GuideDisableClickView = GuideDisableClickView or BaseClass(SafeBaseView)

function GuideDisableClickView:__init()
	if GuideDisableClickView.Instance then
		ErrorLog("[GuideDisableClickView] Attemp to create a singleton twice !")
	end

	self.view_layer = UiLayer.PopTop
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
    self:SetMaskBg(false,true)
end

function GuideDisableClickView:__delete()

end

function GuideDisableClickView:ReleaseCallBack()

end

function GuideDisableClickView:LoadCallBack()

end

function GuideDisableClickView:OnFlush()

end


