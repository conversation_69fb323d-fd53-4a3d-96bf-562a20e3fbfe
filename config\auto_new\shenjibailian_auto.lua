-- S-圣武降世.xls
local item_table={
[1]={item_id=16507,num=1,is_bind=1},
[2]={item_id=16512,num=1,is_bind=1},
[3]={item_id=16552,num=1,is_bind=1},
[4]={item_id=16557,num=1,is_bind=1},
[5]={item_id=16562,num=1,is_bind=1},
[6]={item_id=26357,num=2,is_bind=1},
[7]={item_id=26357,num=3,is_bind=1},
[8]={item_id=26357,num=4,is_bind=1},
[9]={item_id=26357,num=5,is_bind=1},
[10]={item_id=26500,num=1,is_bind=1},
[11]={item_id=26500,num=2,is_bind=1},
[12]={item_id=26500,num=3,is_bind=1},
[13]={item_id=26500,num=4,is_bind=1},
[14]={item_id=26500,num=5,is_bind=1},
[15]={item_id=16508,num=1,is_bind=1},
[16]={item_id=16513,num=1,is_bind=1},
[17]={item_id=16504,num=1,is_bind=1},
[18]={item_id=16509,num=1,is_bind=1},
[19]={item_id=16514,num=1,is_bind=1},
[20]={item_id=16502,num=1,is_bind=1},
[21]={item_id=16501,num=1,is_bind=1},
[22]={item_id=16506,num=1,is_bind=1},
[23]={item_id=16511,num=1,is_bind=1},
[24]={item_id=16551,num=1,is_bind=1},
[25]={item_id=16556,num=1,is_bind=1},
[26]={item_id=16561,num=1,is_bind=1},
[27]={item_id=16500,num=1,is_bind=1},
[28]={item_id=16505,num=1,is_bind=1},
[29]={item_id=16510,num=1,is_bind=1},
[30]={item_id=16550,num=1,is_bind=1},
[31]={item_id=16555,num=1,is_bind=1},
[32]={item_id=16560,num=1,is_bind=1},
[33]={item_id=16554,num=1,is_bind=1},
[34]={item_id=16559,num=1,is_bind=1},
[35]={item_id=16564,num=1,is_bind=1},
[36]={item_id=16503,num=1,is_bind=1},
[37]={item_id=16553,num=1,is_bind=1},
[38]={item_id=16558,num=1,is_bind=1},
[39]={item_id=16563,num=1,is_bind=1},
[40]={item_id=27946,num=1,is_bind=1},
[41]={item_id=27946,num=9,is_bind=1},
[42]={item_id=27948,num=1,is_bind=1},
[43]={item_id=26357,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
first_single_lotto={
{},
{first_single_lotto_reward=2,reward_item=item_table[1],},
{first_single_lotto_reward=3,reward_item=item_table[2],},
{first_single_lotto_reward=4,reward_item=item_table[3],},
{first_single_lotto_reward=5,reward_item=item_table[4],},
{first_single_lotto_reward=6,reward_item=item_table[5],}
},

first_single_lotto_meta_table_map={
},
first_multi_lotto_group={
{},
{first_multi_lotto_group=2,group_count=9,}
},

first_multi_lotto_group_meta_table_map={
},
first_multi_lotto={
{},
{first_single_lotto_id=2,reward_item=item_table[6],},
{first_single_lotto_id=3,reward_item=item_table[7],},
{first_single_lotto_id=4,reward_item=item_table[8],},
{first_single_lotto_id=5,reward_item=item_table[9],},
{first_multi_lotto_group=2,reward_item=item_table[10],reward_type=2,broadcast=2,},
{first_single_lotto_id=2,reward_item=item_table[11],},
{first_single_lotto_id=3,reward_item=item_table[12],},
{first_single_lotto_id=4,reward_item=item_table[13],},
{first_single_lotto_id=5,reward_item=item_table[14],}
},

first_multi_lotto_meta_table_map={
[7]=6,	-- depth:1
[8]=6,	-- depth:1
[9]=6,	-- depth:1
[10]=6,	-- depth:1
},
special_multi_lotto_group={
{},
{special_multi_lotto_group=2,special_group_count=10,}
},

special_multi_lotto_group_meta_table_map={
},
special_multi_lotto={
{special_multi_lotto_group=1,reward_type=1,broadcast=1,},
{special_single_lotto_id=2,reward_item=item_table[15],},
{special_single_lotto_id=3,reward_item=item_table[16],},
{special_single_lotto_id=4,reward_item=item_table[17],},
{special_single_lotto_id=5,reward_item=item_table[18],},
{special_single_lotto_id=6,reward_item=item_table[19],},
{reward_item=item_table[20],},
{special_single_lotto_id=2,reward_item=item_table[1],},
{special_single_lotto_id=3,reward_item=item_table[2],},
{special_single_lotto_id=4,reward_item=item_table[3],},
{special_single_lotto_id=5,reward_item=item_table[4],},
{special_single_lotto_id=6,reward_item=item_table[5],},
{special_single_lotto_id=7,reward_item=item_table[21],},
{special_single_lotto_id=8,reward_item=item_table[22],},
{special_single_lotto_id=9,reward_item=item_table[23],},
{special_single_lotto_id=10,reward_item=item_table[24],},
{special_single_lotto_id=11,reward_item=item_table[25],},
{special_single_lotto_id=12,reward_item=item_table[26],},
{special_single_lotto_id=13,reward_item=item_table[27],},
{special_single_lotto_id=14,reward_item=item_table[28],},
{special_single_lotto_id=15,reward_item=item_table[29],},
{special_single_lotto_id=16,reward_item=item_table[30],},
{special_single_lotto_id=17,reward_item=item_table[31],},
{special_single_lotto_id=18,reward_item=item_table[32],}
},

special_multi_lotto_meta_table_map={
[6]=1,	-- depth:1
[5]=6,	-- depth:2
[4]=6,	-- depth:2
[3]=6,	-- depth:2
[2]=6,	-- depth:2
},
reward={
{reward_type=1,broadcast=1,},
{reward_id=2,reward_item=item_table[18],},
{reward_id=3,reward_item=item_table[19],},
{reward_id=4,reward_item=item_table[33],},
{reward_id=5,reward_item=item_table[34],},
{reward_id=6,reward_item=item_table[35],},
{reward_id=7,reward_item=item_table[36],},
{reward_id=8,reward_item=item_table[15],},
{reward_id=9,reward_item=item_table[16],},
{reward_id=10,reward_item=item_table[37],},
{reward_id=11,reward_item=item_table[38],},
{reward_id=12,reward_item=item_table[39],},
{reward_id=13,reward_item=item_table[20],},
{reward_id=14,reward_item=item_table[1],},
{reward_id=15,reward_item=item_table[2],},
{reward_id=16,reward_item=item_table[3],},
{reward_id=17,reward_item=item_table[4],},
{reward_id=18,reward_item=item_table[5],},
{reward_id=19,reward_item=item_table[21],},
{reward_id=20,reward_item=item_table[22],},
{reward_id=21,reward_item=item_table[23],},
{reward_id=22,reward_item=item_table[24],},
{reward_id=23,reward_item=item_table[25],},
{reward_id=24,reward_item=item_table[26],},
{reward_id=25,reward_item=item_table[27],},
{reward_id=26,reward_item=item_table[28],},
{reward_id=27,reward_item=item_table[29],},
{reward_id=28,reward_item=item_table[30],},
{reward_id=29,reward_item=item_table[31],},
{reward_id=30,reward_item=item_table[32],}
},

reward_meta_table_map={
[12]=1,	-- depth:1
[11]=12,	-- depth:2
[10]=12,	-- depth:2
[9]=12,	-- depth:2
[8]=12,	-- depth:2
[7]=12,	-- depth:2
[6]=12,	-- depth:2
[5]=12,	-- depth:2
[4]=12,	-- depth:2
[3]=12,	-- depth:2
[2]=12,	-- depth:2
},
group_multi_limit={
{},
{cycle=1,},
{cycle=2,},
{cycle=3,},
{cycle=4,},
{cycle=5,}
},

group_multi_limit_meta_table_map={
},
group_cycle_limit={
{},
{cycle=1,},
{cycle=2,},
{cycle=3,},
{cycle=4,},
{cycle=5,}
},

group_cycle_limit_meta_table_map={
},
pool_cycle={
{},
{cycle=1,},
{cycle=2,},
{cycle=3,},
{cycle=4,},
{cycle=5,}
},

pool_cycle_meta_table_map={
},
reward_cycle={
{},
{reward_id=14,},
{reward_id=15,},
{reward_id=16,},
{reward_id=17,},
{reward_id=18,},
{cycle=1,},
{reward_id=14,},
{reward_id=15,},
{reward_id=16,},
{reward_id=17,},
{cycle=1,},
{cycle=2,},
{reward_id=14,},
{reward_id=15,},
{reward_id=16,},
{cycle=2,},
{cycle=2,},
{cycle=3,},
{reward_id=14,},
{reward_id=15,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=4,},
{reward_id=14,},
{reward_id=15,},
{reward_id=16,},
{reward_id=17,},
{cycle=4,},
{cycle=5,},
{reward_id=14,},
{reward_id=15,},
{cycle=5,},
{cycle=5,},
{cycle=5,}
},

reward_cycle_meta_table_map={
[34]=4,	-- depth:1
[33]=34,	-- depth:2
[32]=33,	-- depth:3
[24]=6,	-- depth:1
[30]=24,	-- depth:2
[29]=30,	-- depth:3
[28]=29,	-- depth:4
[27]=28,	-- depth:5
[26]=27,	-- depth:6
[23]=29,	-- depth:4
[18]=30,	-- depth:3
[21]=23,	-- depth:5
[20]=21,	-- depth:6
[35]=23,	-- depth:5
[17]=35,	-- depth:6
[16]=17,	-- depth:7
[15]=16,	-- depth:8
[14]=15,	-- depth:9
[12]=18,	-- depth:4
[11]=12,	-- depth:5
[10]=11,	-- depth:6
[9]=10,	-- depth:7
[8]=9,	-- depth:8
[22]=10,	-- depth:7
[36]=12,	-- depth:5
},
exchange={
{},
{seq=2,goods=item_table[18],},
{seq=3,goods=item_table[19],},
{seq=4,goods=item_table[33],},
{seq=5,goods=item_table[34],},
{seq=6,goods=item_table[35],},
{seq=7,goods=item_table[36],exchange_num=2000,},
{seq=8,goods=item_table[15],},
{seq=9,goods=item_table[16],},
{seq=10,goods=item_table[37],},
{seq=11,goods=item_table[38],},
{seq=12,goods=item_table[39],}
},

exchange_meta_table_map={
[8]=7,	-- depth:1
[9]=7,	-- depth:1
[10]=7,	-- depth:1
[11]=7,	-- depth:1
[12]=7,	-- depth:1
},
other_default_table={single_lotto_consume=item_table[40],complement_num=98,multi_lotto=10,multi_lotto_consume_special=27947,multi_lotto_consume=item_table[41],discount_text="9折",lotto_exchange=item_table[42],danmu_row=4,danmu_time=15,show="16504,16509,16514,16554,16559,16564",label="冰冻,附毒,禁锢,护盾,吸收,恢复",labels="终结,持续,弱化,增伤,续命,反伤",shop_seq=10012,is_jumpanim=0,},

first_single_lotto_default_table={first_single_lotto_reward=1,reward_item=item_table[20],reward_type=2,broadcast=0,},

first_multi_lotto_group_default_table={first_multi_lotto_group=1,group_count=1,},

first_multi_lotto_default_table={first_multi_lotto_group=1,first_single_lotto_id=1,reward_item=item_table[43],reward_type=1,broadcast=1,},

special_multi_lotto_group_default_table={special_multi_lotto_group=1,special_group_count=0,},

special_multi_lotto_default_table={special_multi_lotto_group=2,special_single_lotto_id=1,reward_item=item_table[36],reward_type=2,broadcast=2,},

reward_default_table={reward_id=1,reward_item=item_table[17],reward_type=2,broadcast=0,},

group_multi_limit_default_table={cycle=0,reward_multi_group=1,reward_id="1|2|3|4|5|6|7|8|9|10|11|12",},

group_cycle_limit_default_table={cycle=0,reward_group=1,reward_id="1|2|3|4|5|6|7|8|9|10|11|12",},

pool_cycle_default_table={cycle=0,reward_pool=2,},

reward_cycle_default_table={cycle=0,reward_pool=2,reward_id=13,},

exchange_default_table={seq=1,goods=item_table[17],exchange_num=4000,}

}

