WorldServerView.SG_OPERA = {
	ALLINFO = 0,
	CONCERN = 1, --p1 scene_id, p2 boss_id
	UNCONCERN = 2, --p1 scene_id, p2 boss_id
}


function WorldServerView:DeleteSGBossView()
	self.boss_layout_rect = nil
	self.boss_list_refresh_callback = nil

	if self.hide_boss_list then
		for i, v in pairs(self.hide_boss_list) do
			v:DeleteMe()
		end
		self.hide_boss_list = nil
	end
end

function WorldServerView:InitSGBossView()
	if not self.cur_sg_layer then
		self.cur_sg_layer = BossWGData.Instance:GetSGLayerLevel()
	end

	self.node_list["txt_sg_up_tip"].button:AddClickListener(
			BindTool.Bind(self.OpenTimesVipView, self, TabIndex.worserv_boss_sgyj + 1))

	if not self.hide_boss_list then
		self.hide_boss_list = {}
		for i = 1, 4 do
			self.hide_boss_list[i] = HideBossRender.New(self.node_list["hide_boss_render_" .. i])
			self.hide_boss_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickHideCell, self))
			self.hide_boss_list[i]:SetIndex(i)
		end
	end
end

function WorldServerView:SelectSGBoss()
    self.cur_sg_boss_id = self.select_item_data.boss_id
    BossWGData.Instance:SetCurSelectBossID(self:GetShowIndex(), self.cur_sg_layer, self.cur_sg_boss_id)
    self:FreshSmallElementSGBoss()
end

function WorldServerView:FreshSmallElementSGBoss()
    local boss_data = self.select_item_data
	local is_gather = boss_data.type == BossWGData.MonsterType.Gather

	self.node_list["Txt_goto_kill"].text.text = is_gather and Language.Common.GotoCaiJi or Language.Common.GotoKillBoss
	self:FlushEnterAndBossNum()
	self:FlushSGVipTip()
	self:FlushCenterBox(is_gather, boss_data.layer)
end

function WorldServerView:FlushSGVipTip()
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	self.node_list["sg_up_tip"]:SetActive(role_vip ~= COMMON_CONSTS.MAX_VIP_LEVEL)
	self.node_list["sg_up_tip"]:SetActive(false)
end

function WorldServerView:FlushCenterBox(is_gather, layer)
	self.node_list['center_box']:SetActive(is_gather)
	if is_gather then
		local max_box_num, min_box_num = BossWGData.Instance:GetSGBoxNum(layer + 1)
		self.node_list["box_num_1"].text.text = "x" .. (max_box_num or 0)
		self.node_list["box_num_2"].text.text = "x" .. (min_box_num or 0)

		local time = BossWGData.Instance:GetSGBoxFlushTime()
		local time_str = time and TimeUtil.FormatHM(time) or ""
		self.node_list["box_time_1"].text.text = time_str .. Language.Boss.Flush
		self.node_list["box_time_2"].text.text = time_str .. Language.Boss.Flush
	end
end

function WorldServerView:FlushEnterAndBossNum()
	local tire_value, max_tire_value = BossWGData.Instance:GetSgBossTire()
	local num_str1 = self:SetStrColor(tire_value, max_tire_value)
	self.node_list["txt_sg_boss_num"].text.text = string.format(Language.Boss.SGTireValue, num_str1)

	local enter_times, max_enter_times = BossWGData.Instance:GetSgBossEnterTimes()
	local num_str2 = self:SetStrColor(enter_times, max_enter_times)
	self.node_list["rich_sgenter_num"].text.text = string.format(Language.Boss.SGLeftEnterTime1, num_str2)
end

function WorldServerView:OnFlushSGBossView()
    self:ClearBossTuJianIndex()
    self:RefreshSGInfoShow()
	self:OnFlushHideList()
	self:OnFlushBossList()
end

function WorldServerView:RefreshSGInfoShow()
    local role_num = BossWGData.Instance:GetSgEnterRoleNum(self.cur_sg_layer + 1)
    self.node_list["enter_role"].text.text = string.format(Language.Boss.EnterRoleNum, role_num)
end


function WorldServerView:SelectLayerBtnSGYJ(btn_index)
	local old_index_sg = self.cur_sg_layer
	self.cur_sg_layer = btn_index - 1
	local falg, need_level = BossWGData.Instance:GetSgLayerIsEnter(self.cur_sg_layer)
	need_level = RoleWGData.GetLevelString(need_level)
	if not falg and self.cur_sg_layer == 0 then
		self:FlushSGLayerInfo()
		return
	else
		if not falg then
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Boss.EnterWorldBossLevel,need_level))
			if old_index_sg ~= self.cur_sg_layer then
				local flag2 = BossWGData.Instance:GetSgLayerIsEnter(old_index_sg)
				if flag2 then
					old_index_sg = old_index_sg + 1
				else
					old_index_sg = BossWGData.Instance:GetSGLayerLevel()
				end
				self.layer_btn_list:SelectIndex(old_index_sg)
			end
			return
		end
	end
	self:FlushSGLayerInfo()
end

function WorldServerView:FlushSGLayerInfo()
	self:OnFlushHideList()
	self:OnFlushBossList()
	self:ChangeHideBoss()
	self:RefreshSGInfoShow()
end

function WorldServerView:BrowseSGPlayInfo()
	local des, title = Language.Boss.SgPlayDes, Language.Boss.PlayTitle
	local role_tip = RuleTip.Instance
	role_tip:SetTitle(title)
	role_tip:SetContent(des)
end

function WorldServerView:GetGatherModel2()
	if not self.boss_display_model_1 then
        self.boss_display_model_1 = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["model_up"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}

		self.boss_display_model_1:SetRenderTexUI3DModel(display_data)
        -- self.boss_display_model_1:SetUI3DModel(self.node_list["model_up"].transform, self.node_list["world_boss_model"].event_trigger_listener, 3)
	end
end

function WorldServerView:ShowSpecialGatherModel()
	self:GetGatherModel2()
	local cfg = BossWGData.Instance:GetSGGatherCfg()
	local gather_cfg
    local data
    local bundle, asset

	for i = 0, 1 do	--两个模型
		data = cfg[i + 1]
		if not data then
			return
		end
		gather_cfg = ConfigManager.Instance:GetAutoConfig("gather_auto").gather_list[data.gather_id]
		if not gather_cfg then
			return
		end
        bundle, asset = ResPath.GetGatherModel(gather_cfg.resid)
        if i == 0 then
            self.boss_display_model:SetMainAsset(bundle, asset)
            self.boss_display_model:SetVisible(true)
        else
            self.boss_display_model_1:SetMainAsset(bundle, asset)
        end
	end

    if self.boss_display_model_1 then
		self.boss_display_model_1:SetVisible(self.show_index == TabIndex.worserv_boss_sgyj)
	end
end

function WorldServerView:GoToKillSGYJ()
	if Scene.Instance:GetSceneType() == SceneType.SG_BOSS then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.OutFubenTip)
		return
	end

	local enter_comsun = BossWGData.Instance:GetSGBossEnterComsun()
	local tiky_id_seq = BossWGData.Instance:GetSGOtherCfg().ticket_shop_seq
	BossWGCtrl.Instance:SetEnterBossComsunData(tiky_id_seq, enter_comsun, Language.Boss.EnterSGBoss, Language.Boss.EnterBossConsum, function()
		BossWGCtrl.Instance:SendSGEnterReq(self.cur_sg_layer)
		BossWGData.Instance:SetBossEnterFlag(true)
       	BossWGData.Instance:SetOldBossID(TabIndex.worserv_boss_sgyj + 100, self.cur_sg_boss_id)
		BossWGData.Instance:GetSetLastSelectInfo(self.cur_sg_layer, self.list_index)
	end)
end

function WorldServerView:OpenTimesVipView(tab_index)
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	if role_vip == COMMON_CONSTS.MAX_VIP_LEVEL then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.SGVipTip6)
	else
		BossWGCtrl.Instance:OpenQuickVipBuyView(tab_index)
	end
end

function WorldServerView:ShowHideBossList(cell)
	if not self.boss_list_refresh_callback then
		self.boss_list:SetRefreshCallback(function(cell, index)
			if cell.data.type == BossWGData.MonsterType.HideBoss then
				self.node_list["hide_list"].transform:SetParent(cell.node_list["hide_pos"].transform, false)
				cell:ShowTweenState()
			end
		end)
	end
	self.boss_list_refresh_callback = true
end

function WorldServerView:ChangeHideBoss(cell)
	local old_hide_state = self.is_show_hide
	if cell == nil or cell.data == nil then
		return
	end
	if cell and cell.data.type == BossWGData.MonsterType.HideBoss and not self.is_show_hide then
		self.is_show_hide = not self.is_show_hide
		if self.is_show_hide then
			self:ShowHideBossList(cell)			
			self.hide_boss_list[1]:OnClick()
		end 
    else
		self.is_show_hide = false
	end
end

function WorldServerView:OnFlushHideList()
	local hide_list = BossWGData.Instance:GetHideMossByLayer(self.cur_sg_layer)
	hide_list = hide_list or {}
	BossView.HIDELISTHIGH = #hide_list * BossView.HIDECELLHIGH + BossView.CELLNORHIGH

	for i, v in ipairs(self.hide_boss_list) do
		local list = hide_list[i]
		if list then
			v:SetData(hide_list[i])
			v:SetActive(true)
		else
			v:SetActive(false)
		end
	end
end

function WorldServerView:OnClickHideCell(cell)
	self.select_item_data = cell.data
	for i, v in ipairs(self.hide_boss_list) do
		v:OnSelectChange(cell.index == v.index)
	end
	self:OnFlushBossInfo()
end

------------------------------------------------------------------------------
HideBossRender = HideBossRender or BaseClass(BaseRender)
function HideBossRender:OnFlush()
	local mon_cfg = BossWGData:GetMonsterInfo( self.data.boss_id)
	if mon_cfg and mon_cfg.name then
		self.node_list["Text"].text.text = mon_cfg.name..string.format(Language.Boss.Ji, self.data.boss_lv_seq)
	end
	if self.data.num == nil or self.data.num == 0 then
		self.node_list["text_flu"].text.text = ToColorStr(Language.Boss.WeiChuxian, COLOR3B.D_RED)
	else
		self.node_list["text_flu"].text.text = ToColorStr(Language.Boss.YiShuaxin, COLOR3B.D_GREEN)
	end
	self.node_list["jieshu_text"].text.text = string.format(Language.Boss.JieShu, NumberToChinaNumber(mon_cfg.boss_jieshu))
end

function HideBossRender:OnSelectChange(is_select)
	self.node_list["high"]:SetActive(is_select)
end