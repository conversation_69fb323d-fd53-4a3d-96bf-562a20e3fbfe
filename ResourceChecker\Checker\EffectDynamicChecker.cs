﻿#if UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System;
using Nirvana;
using System.IO;

public class EffectDynamicChecker : DynamicChecker
{
    private static readonly string EffectDir = "effects/";
    private static readonly string EffectDir2 = "effects2/";
    private static readonly string ActorDir = "actors/";
    private static readonly string ModelDir = "model/";
    private static HashSet<string> logSet = new HashSet<string>();
    private static List<string> logList = new List<string>();

    private static HashSet<string> filterList = new HashSet<string>()
    {

    };

    private static Dictionary<UnityEngine.Object, string> refPaths = new Dictionary<UnityEngine.Object, string>();

    protected override bool NeedCheck(string bundleName)
    {
        if (bundleName.StartsWith(EffectDir)
            || bundleName.StartsWith(EffectDir2)
            || bundleName.StartsWith(ActorDir)
            || bundleName.StartsWith(ModelDir))
        {
            return true;
        }

        return false;
    }

    protected override void Check(string assetPath, Action<CheckObject[]> callback)
    {
        List<CheckObject> list = new List<CheckObject>();
        GameObject go = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
        if (null != go)
        {
            CheckParitlceSystem(go, list);
        }

        callback(list.ToArray());
    }

    protected override HashSet<string> GetFilterList()
    {
        return filterList;
    }

    protected override BaseWarningWindow GetWindow()
    {
        return UnityEditor.EditorWindow.GetWindowWithRect<EffectWarningWindow>(new Rect(1200, 600, 800, 600));
    }

    public void CheckParitlceSystem(GameObject go, List<CheckObject> list)
    {
        if (go.GetComponent<SrpUIEffect>() || go.GetComponentInChildren<UiParticles.UiParticles>())
        {
            return;
        }

        ParticleSystem[] particles = go.GetComponentsInChildren<ParticleSystem>();
        if (null == particles)
        {
            return;
        }

        CheckEffectIsBad(go, particles, list);
    }

    // 是否是永驻特效
    public static bool IsLoopEffect(GameObject go, ParticleSystem[] particles)
    {
        if (null == particles)
        {
            particles = go.GetComponentsInChildren<ParticleSystem>();
        }

        if (particles.Length <= 0) return false;

        int loopCount = 0;
        for (int i = 0; i < particles.Length; i++)
        {
            if (particles[i].main.loop)
            {
                loopCount++;
            }
        }


        bool isLoopEffect = (loopCount / particles.Length) >= 0.8f;
        var ctrl = go.GetComponent<EffectControl>();
        if (null != ctrl && ctrl.Duration <= 10)
        {
            isLoopEffect = false;
        }
        return isLoopEffect;
    }

    // 检查该特效是否不合格特效
    public static bool CheckEffectIsBad(GameObject go, ParticleSystem[] particles = null, List<CheckObject> list = null)
    {
        bool isDcOver = EffectDynamicChecker.CheckIsDcOver(go, particles, null);
        bool isOVerDraw = EffectDynamicChecker.CheckIsOverDraw(go, particles, null);
        bool isError = EffectDynamicChecker.CheckSubEmittersIsError(go, particles, null);
        if (isDcOver || isOVerDraw || isError)
        {
            return true;
        }

        return false;
    }

    // 检查DC是否超出
    public static bool CheckIsDcOver(GameObject go, ParticleSystem[] particles = null, List<CheckObject> list = null)
    {
        if (null == particles)
        {
            particles = go.GetComponentsInChildren<ParticleSystem>();
        }

        bool isLoopEffect = IsLoopEffect(go, particles);
        int[] dcLimitList = isLoopEffect ? new int[] { 20, 10, 7, 3 } : new int[] { 30, 20, 10, 5 };  // 不同情况下的DC个数限制
        string[] qualityStr = new string[] { "完美品质", "高品质", "中品质", "低品质" };
        string loopStr = isLoopEffect ? "循环特效" : "非循环特效";

        string name = AssetDatabase.GetAssetPath(go);
        if (string.IsNullOrEmpty(name)) name = go.name;

        var dc = CalcParticleSystemDC(go, particles);
        if (dc <= dcLimitList[dcLimitList.Length - 1])
        {
            return false;
        }

        // Animator来控制粒子显示隐藏的不处理的情况会影响DC计算，不处理先
        if (null != go.GetComponentInChildren<Animator>())
        {
            return false;
        }

        var ctrls = go.GetComponentsInChildren<QualityControlActive>();
        if (ctrls.Length <= 0)
        {
            string log = string.Format("特效渲染效率低，DC超标，通知美术增加QualityControlActive组件进行控制！{0}, {1} , 当前{2}个DC,  应不高于{3}个", loopStr, go.name, dc, dcLimitList[dcLimitList.Length - 1]);
            Report(go, log, list);
            return true;
        }

        bool isDcOVer = false;
        // 计算各个品质下DC个数
        ControlItem[] ctrlItem;
        // 考虑多个QualityControlActive
        List<ControlItem> ctrlItemList = new List<ControlItem>();
        for (int i = 0; i < ctrls.Length; i++)
        {
            ControlItem[] tempCtrlItem = ctrls[i].GetControls();
            ctrlItemList.AddRange(tempCtrlItem);
        }
        ctrlItem = ctrlItemList.ToArray();
 
        HashSet<GameObject> filterGameObjSet = new HashSet<GameObject>();
        for (int i = 0; i < 4; i++)
        {
            // 构建过滤列表
            filterGameObjSet.Clear();
            for (int m = 0; m < ctrlItem.Length; m++)
            {
                if (!ctrlItem[m].EnabledLevels[i])
                {
                    filterGameObjSet.Add(ctrlItem[m].Target as GameObject);
                }
            }

            dc = CalcParticleSystemDC(go, particles, filterGameObjSet);
            if (dc > dcLimitList[i])
            {
                string log = string.Format("特效渲染效率低，{0}下DC超标, 通知美术处理，！{1}, {2} , 当前{3}个DC, 应不高于{4}个", qualityStr[i], loopStr, go.name, dc, dcLimitList[i]);
                Report(go, log, list);
                isDcOVer = true;
            }
        }
        return isDcOVer;
    }

    public static int CalcParticleSystemDC(GameObject go, ParticleSystem[] particles = null, HashSet<GameObject> filterGameObjSet = null)
    {
        if (null == particles)
        {
            particles = go.GetComponentsInChildren<ParticleSystem>();
        }

        // 先一次性搜集particle的信息
        float playTime = 0;
        List<float[]> particleInfoList = new List<float[]>();
        for (int i = 0; i < particles.Length; i++)
        {
            var particle = particles[i];
            if (null != filterGameObjSet && filterGameObjSet.Contains(particle.gameObject))
            {
                continue;
            }

            Material material = particle.GetComponentInChildren<Renderer>(true).sharedMaterial;
            if (null == material)
            {
                Debug.LogError("不允许粒子特效没有指定材质球，找美术！！");
                continue;
            }

            float materialId = material ? (float)material.GetInstanceID() : (float)i;
            float startTime = particle.main.startDelay.constantMax;
            float endTime = 0;
            if (particle.main.loop)
            {
                endTime = 10;
            }
            else
            {
                endTime = startTime + particle.main.duration + particle.main.startLifetime.constantMax;
            }

            particleInfoList.Add(new float[] { materialId, startTime, endTime });

            playTime = endTime > playTime ? endTime : playTime;
        }

        // 计算粒子特效的DC
        float time = 0;
        var maxDc = 0;
        HashSet<float> materialIdSet = new HashSet<float>();
        float maxSampleTime = Mathf.Min(playTime, 5);
        while (time < maxSampleTime)
        {
            time += 0.2f;
            materialIdSet.Clear();
            for (int i = 0; i < particleInfoList.Count; i++)
            {
                var materialId = particleInfoList[i][0];
                var startTime = particleInfoList[i][1];
                var endtime = particleInfoList[i][2];
                if (time >= startTime && time <= endtime)
                {
                    materialIdSet.Add(materialId);
                }
            }
            maxDc = materialIdSet.Count > maxDc ? materialIdSet.Count : maxDc;
        }

        // 补上SkinnedMeshRenderer , meshRender
        maxDc += go.GetComponentsInChildren<SkinnedMeshRenderer>().Length;
        maxDc += go.GetComponentsInChildren<MeshRenderer>().Length;

        return maxDc;
    }

    // 检查绘制象素复的杂度
    public static bool CheckIsOverDraw(GameObject go, ParticleSystem[] particles = null, List<CheckObject> list = null)
    {
        if (null == particles)
        {
            particles = go.GetComponentsInChildren<ParticleSystem>();
        }

        bool isTextureFillRate = CheckIsOverDrawInTextureFillRate(go, particles, null);
        bool isTooBigSize = CheckIsOverDrawInEmitTooBigSize(go, particles, list);
        bool isCountBigSize = CheckIsOverDrawInOverCountBigSize(go, particles, list);
        if (isTextureFillRate || isTooBigSize  || isCountBigSize)
        {
            return true;
        }

        return false;
    }

    // 规则：使用的纹理周围空白象素过多，导致面片无意义需增大
    public static bool CheckIsOverDrawInTextureFillRate(GameObject go, ParticleSystem[] particles = null, List<CheckObject> list = null)
    {
        if (null == go) return true;
        if (null == particles)
        {
            particles = go.GetComponentsInChildren<ParticleSystem>(true);
        }

        List<ParticleSystem> overList = new List<ParticleSystem>();

        for (int i = 0; i < particles.Length; i++)
        {
            if (particles[i].main.startSize.constantMax <= 10)
            {
                continue;
            }

            var render = particles[i].GetComponent<Renderer>();
            var material = render.sharedMaterial;
            if (null != material && material.mainTexture && material.mainTexture.width >= 16 && material.mainTexture.height >= 16)
            {
                var texture = material.mainTexture as Texture2D;
                Rect fillRect = TextureFillRateUtil.GetTextureFillRect(texture, 10);
                if (1.0f * fillRect.width / texture.width <= 0.85 || 1.0f * fillRect.height / texture.height <= 0.85)
                {
                    bool isFixed = false;
                    if (material.IsKeywordEnabled("ENABLE_UV_TRANSFORM"))
                    {
                        var vector = material.GetVector("_UVTransform");
                        if (vector.x != 1 || vector.y != 1) // 允许美术视效果手填，这里只是做基本检查
                        {
                            isFixed = true;
                        }
                    }

                    if (!isFixed)
                    {
                        overList.Add(particles[i]);
                    }
                }
            }
        }

        if (overList.Count > 0)
        {
            var nameDesc = "超标粒子：";
            for (int i = 0; i < overList.Count; i++)
            {
                nameDesc += overList[i].name + "  ";
            }
            string log = string.Format("特效渲染效率低，找特效处理。！！!纹理问题导致象素填充率太高,特效:{0}, {1}", go.name, nameDesc);
            Report(go, log, list);
        }

        return overList.Count > 0;
    }

    // 规则：总面积超过500。 无视品质控制，说明该粒子系统实在是太差。像去那种
    // 一个发射器发射了大量面片
    private static bool CheckIsOverDrawInEmitTooBigSize(GameObject go, ParticleSystem[] particles = null, List<CheckObject> list = null)
    {
        int limitEmitCount = 5;
        int limitEmitSize = 100;
        float totalSize = 0;
        List<string> overSizeList = new List<string>();
        for (int i = 0; i < particles.Length; i++)
        {
            var particle = particles[i];
            if (particle.main.loop || ParticleSystemUtil.CalcParitcleAvgLifeTime(particle) > 1)  // 超过1秒的才算
            {
                var emitCount = ParticleSystemUtil.CalcParticleCountInPerSecond(particles[i]);
                var emitSize = ParticleSystemUtil.CalcParticlePlanMaxSize(particles[i]);
                if (emitCount > limitEmitCount && emitSize > limitEmitSize)
                {
                    totalSize = totalSize + emitSize * emitCount;
                    overSizeList.Add(string.Format("{0},  粒子数：{1}, 粒子大小:{2}", particles[i].name, emitCount, emitSize));
                }
            }
        }

        if (overSizeList.Count > 0)
        {
            string overListStr = "";
            for (int i = 0; i < overSizeList.Count; i++)
            {
                overListStr = overListStr + overSizeList[i] + "\n";
            }

            string name = AssetDatabase.GetAssetPath(go);
            if (string.IsNullOrEmpty(name)) name = go.name;
            if (null != list)
            {
                string log = string.Format("特效太废！找美术! Overdraw太高： 发射器发射了超标大粒子 {0}", name);
                Report(go, log, list);
            }
            else
            {
                string log = string.Format("Overdraw太高：{0}, {1}个发射器发射了超标大粒子（面片大小>{2}, 个数>{3})，超标如下：\n{4}", name, overSizeList.Count, limitEmitCount, limitEmitSize, overListStr);
                Report(go, log, list);
            }
            return true;
        }

        return false;
    }

    // 规则: 只统计面片大于25的，只有当不合格system大于3个时则认为该粒子系统不合格。高品质控制内的不再统计
    private static bool CheckIsOverDrawInOverCountBigSize(GameObject go, ParticleSystem[] particles = null, List<CheckObject> list = null)
    {
        ControlItem[] qualityItems = null;
        QualityControlActive qualityAct = go.GetComponent<QualityControlActive>();
        if (null != qualityAct)
        {
            if (qualityAct.isBestFlagByArt)
            {
                return false;
            }

            qualityItems = qualityAct.GetControls();
        }

        int limitParticlePlanSize = 25;
        int lmitOverCount = 4;
        // 统计大面片列表
        List<string> overSizeDescList = new List<string>();
        List<ParticleSystem> overSizeParticleList = new List<ParticleSystem>();
        for (int i = 0; i < particles.Length; i++)
        {
            var particle = particles[i];

            // 在高品质控制中的则不参与计算
            if (null != qualityItems)
            {
                bool isHadQuality = false;
                for (int m = 0; m < qualityItems.Length; m++)
                {
                    if (particle.gameObject == qualityItems[m].Target && !qualityItems[m].EnabledLevels[1])
                    {
                        isHadQuality = true;
                        break;
                    }
                }

                if (isHadQuality)
                {
                    continue;
                }
            }

            float particlePlanSize = ParticleSystemUtil.CalcParticlePlanMaxSize(particle);
            if (particlePlanSize > limitParticlePlanSize)
            {
                overSizeParticleList.Add(particle);
                
                overSizeDescList.Add(string.Format("名字{0} , 面片大小{1}，同时存在面片个数{2}", particle.name, particlePlanSize, ParticleSystemUtil.CalcParticleCountInPerSecond(particle)));
            }
        }

        if (overSizeParticleList.Count <= lmitOverCount)
        {
            return false;
        }

        float maxTotalSize = ParticleSystemUtil.CaleParticleMaxTotalSize(overSizeParticleList);
        if (maxTotalSize <= lmitOverCount * limitParticlePlanSize)
        {
            return false;
        }

        string overListStr = "";
        for (int i = 0; i < overSizeDescList.Count; i++)
        {
            overListStr = overListStr + overSizeDescList[i] + "\n";
        }

        string name = AssetDatabase.GetAssetPath(go);
        if (string.IsNullOrEmpty(name)) name = go.name;
        if (null != list)
        {
            string log = string.Format("特效太废！找美术! Overdraw太高： 同时存在大面片个数超标 {0}", name);
            Report(go, log, list);
        }
        else
        {
            string log = string.Format("特效Overdraw太高：{0}, 超标大粒子（面片大小>{1}）{2}个(应<={3}), 总渲染大小{4}，超标如下：\n{5}", name, limitParticlePlanSize, overSizeParticleList.Count, lmitOverCount , maxTotalSize, overListStr);
            Report(go, log, list);
        }

        return true;
    }

    public static bool CheckSubEmittersIsError(GameObject go, ParticleSystem[] particles = null, List<CheckObject> list = null)
    {
        if (null == go) return true;
        if (null == particles)
        {
            particles = go.GetComponentsInChildren<ParticleSystem>();
        }

        bool isError = false;
        for (int i = 0; i < particles.Length; i++)
        {
            var emitterModule = particles[i].subEmitters;
            if (emitterModule.enabled)
            {
                for (int j = 0; j < emitterModule.subEmittersCount; j++)
                {
                    ParticleSystem system = emitterModule.GetSubEmitterSystem(j);
                    if (system
                        && system.gameObject.transform.parent != particles[i].gameObject.transform
                        && system.gameObject.transform.parent != particles[i].gameObject.transform.parent)
                    {
                        string log = string.Format("该特效存在严重问题！！！SubEmitters is Error, {0} {1}", go.name, system.gameObject.name);
                        Report(go, log, list);
                        isError = true;
                    }
                }
            }
        }

        return isError;
    }

    private static void Report(GameObject gameobj, string log, List<CheckObject> list = null)
    {
        Debug.LogError(log);
        if (null != list)
        {
            // 写本地缓存
            if (!logSet.Contains(log))
            {
                logSet.Add(log);
                CheckObject checkObj = new CheckObject(gameobj, log, WarningLevel.High);
                list.Add(checkObj);

                logList.Add(string.Format("【{0}】{1}", gameobj.name, log));
                string path =  string.Format("{0}/../temp/effect_check.txt", Application.dataPath);
                File.WriteAllLines(path, logList.ToArray());
            }
        }
    }
}
#endif