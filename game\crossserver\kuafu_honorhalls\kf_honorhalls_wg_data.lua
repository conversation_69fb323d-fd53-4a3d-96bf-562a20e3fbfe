KuafuHonorhallWGData = KuafuHonorhallWGData or BaseClass()

KuafuHonorhallWGData.RANK_LINE_COUNT = 6
function KuafuHonorhallWGData:__init()
	if KuafuHonorhallWGData.Instance then
		Error<PERSON><PERSON>("[KuafuHonorhallWGData] attempt to create singleton twice!")
		return
	end
	KuafuHonorhallWGData.Instance =self
	self.role_info = {
		cur_layer = 0,
		immediate_realive_count = 0,
		total_kill_count = 0,
		kill_role_count = 0,
		cur_layer_kill_count = 0,
		reward_cross_honor = 0,
		cur_layer_role_num = 0,
	}
	self.rank = {}
	self.attr_info = {
		buy_realive_count = 0,
		add_gongji_per = 0,
		add_hp_per = 0,
		is_pass_all_layer = 0,
	}
	self.honorhall_cfg = ConfigManager.Instance:GetAutoConfig("kuafu_rongyudiantang_auto")
	self.other_cfg = self.honorhall_cfg.other[1]
end

function KuafuHonorhallWGData:__delete()
	KuafuHonorhallWGData.Instance = nil
end

function KuafuHonorhallWGData:GetDelayNextLayerTime()
	local time = self.other_cfg.delay_next_layer
	return time + TimeWGCtrl.Instance:GetServerTime()
end

function KuafuHonorhallWGData:GetKuaFuActivityCfg(act_type)
	local cfg = ConfigManager.Instance:GetAutoConfig("dailywork_auto").activity_hall
	for k,v in pairs(cfg) do
		if v.act_type == act_type then
			return v
		end
	end
end

-- 设置个人信息
function KuafuHonorhallWGData:SetRoleInfo(protocol)
	self.role_info.cur_layer = protocol.cur_layer
	self.role_info.immediate_realive_count = protocol.immediate_realive_count
	self.role_info.total_kill_count = protocol.total_kill_count
	self.role_info.kill_role_count = protocol.kill_role_count
	self.role_info.cur_layer_kill_count = protocol.cur_layer_kill_count
	self.role_info.reward_cross_honor = protocol.reward_cross_honor
	self.role_info.exp = protocol.reward_exp
	self.role_info.delay_kick_out_timestamp = protocol.delay_kick_out_timestamp
	if self:IsFinishKaufuHonor() then
		-- MainuiWGCtrl.Instance:ActivitySetButtonVisible(ACTIVITY_TYPE.KF_HONORHALLS, false)
		ActivityWGCtrl.Instance:RemoveActNotice(ACTIVITY_TYPE.KF_HONORHALLS)
	end
end

function KuafuHonorhallWGData:SetActFinishInfo(info)
	if info then
		self.act_pass_flag = info.is_pass
	else
		self.act_pass_flag = 0
	end
	if self:IsFinishKaufuHonor() then
		-- MainuiWGCtrl.Instance:ActivitySetButtonVisible(ACTIVITY_TYPE.KF_HONORHALLS, false)
		ActivityWGCtrl.Instance:RemoveActNotice(ACTIVITY_TYPE.KF_HONORHALLS)
	end
end

-- 设置快速通关信息
function KuafuHonorhallWGData:SetFastPassInfo(info)
	self.fast_pass_info = info
end

-- 获取快速通关信息
function KuafuHonorhallWGData:GetFastPassInfo()
	return self.fast_pass_info
end

-- 设置快速通关信息
function KuafuHonorhallWGData:SetCurLayerRoleInfo(protocol)
	self.role_info.cur_layer_role_num = protocol.cur_layer_role_num
end

function KuafuHonorhallWGData:IsFinishKaufuHonor()
	if self.act_pass_flag and self.act_pass_flag > 0 then
		return true
	end

	if self.role_info and self.role_info.cur_layer >= self:GetMaxLayer() then
		return true
	end

	return false
end

function KuafuHonorhallWGData:GetOutTimestamp()
	return self.role_info.delay_kick_out_timestamp or 0
end

-- 获取个人信息
function KuafuHonorhallWGData:GetRoleInfo()
	return self.role_info
end

function KuafuHonorhallWGData:GetTitleRewardInfo()
	local cfg = self.honorhall_cfg.title_reward
	local data = {}
	for k,v in pairs(cfg) do
		local cell_data = {}
		cell_data.rank = v.rank
		cell_data.item_id = v.item_id
		cell_data.title_id = v.title_id
		cell_data.vacancy = (self.rank[k] and self.rank[k].finish_time > 0 )and true or false
		if cell_data.vacancy then
			cell_data.uuid = self.rank[k].uuid
			cell_data.sex = self.rank[k].sex
			cell_data.prof = self.rank[k].prof
			cell_data.user_name = self.rank[k].user_name
			cell_data.photoframe = self.rank[k].photoframe
			cell_data.finish_time = TimeUtil.FormatSecond2MS(self.rank[k].finish_time) or ""
		else
			cell_data.user_name = ""
			cell_data.finish_time = ""
			cell_data.photoframe = nil
		end
		table.insert(data, cell_data)
	end
	return data
end

-- 设置属性加成信息
function KuafuHonorhallWGData:SetAttrInfo(info)
	self.attr_info.buy_realive_count = info.buy_realive_count
	self.attr_info.add_gongji_per = info.add_gongji_per
	self.attr_info.add_hp_per = info.add_hp_per
	self.attr_info.is_pass_all_layer = info.is_pass_all_layer
end

-- 获取属性加成信息
function KuafuHonorhallWGData:GetAttrInfo()
	return self.attr_info
end

-- 设置排行信息
function KuafuHonorhallWGData:SetRankInfo(info)
	self.rank = info.rank
end

-- 获取排行信息
function KuafuHonorhallWGData:GetRankInfo()
	return self.rank
end

--获取前3名的排名信息
function KuafuHonorhallWGData:GetThereRankInfo()
	local data = {}
	local rank_list = self:GetRankInfo()
	for i=1,3 do
		if rank_list[i] then
			table.insert(data,rank_list[i])
		end
	end
	return data
end


function KuafuHonorhallWGData:SetPassInfo(protocol)
	local pass_info = {}
	pass_info.max_layer = protocol.max_layer
	pass_info.rank_pos = protocol.rank_pos
	pass_info.kill_role_count = protocol.kill_role_count
	pass_info.finish_time = protocol.finish_time
	pass_info.total_exp = protocol.total_exp
	self.pass_info = pass_info
end

function KuafuHonorhallWGData:GetPassInfo()
	return self.pass_info
end

--获取结算所有层数奖励包括排行的称号奖励
function KuafuHonorhallWGData:GetAllRewardItem()
	local role_info = self:GetRoleInfo()
	local reward_data = {}

	if role_info then
		local cfg_list = self.honorhall_cfg.layer_list or {}
		for i = 1, #cfg_list do
			if cfg_list[i].layer <= role_info.cur_layer then
				for k,v in pairs(cfg_list[i].reward) do
					if reward_data[v.item_id] then
						reward_data[v.item_id].num = reward_data[v.item_id].num + v.num
					else
						reward_data[v.item_id] = DeepCopy(v)
					end
				end
			end
		end

		if role_info.exp > 0 then
			local item_id = COMMON_CONSTS.VIRTUAL_ITEM_EXP
			reward_data[item_id] = {item_id = item_id, num = role_info.exp, is_bind = 0}
		end

		reward_data = SortTableKey(reward_data)
	end

	local title_info = nil
	local pass_info = self:GetPassInfo()
	if pass_info then
		local title_cfg = self.honorhall_cfg.title_reward or {}
		for k, v in pairs(title_cfg) do
			if v.rank == pass_info.rank_pos then
				title_info = v
				break
			end
		end
	end

	return reward_data, title_info
end

function KuafuHonorhallWGData:GetRankNum()
	local rank_info = self:GetRankInfo()
	local my_uuid = RoleWGData.Instance:GetUUid()
	if rank_info then
		for k,v in pairs(rank_info) do
			if v.uuid == my_uuid then
				return k
			end
		end
	end
	return 0
end

--排行奖励
function KuafuHonorhallWGData:GetRankRewardAuto(index)
	local cfg = self.honorhall_cfg.rank_reward_list
	 for k,v in pairs(cfg) do
		if v.min_pos <= index and index <= v.max_pos then
			return v.item_id or {}
		end
	end
	return {}
end

function KuafuHonorhallWGData:GetUpLayerKills(lay)
	local cfg_list = self.honorhall_cfg.layer_list
	return cfg_list[lay] and cfg_list[lay].up_layer_need_kills
end

function KuafuHonorhallWGData:GetMaxLayer()
	local cfg_list = self.honorhall_cfg.layer_list
	return cfg_list[#cfg_list].layer
end

function KuafuHonorhallWGData:GetRewardItem(lay)
	local cfg_list = self.honorhall_cfg.layer_list
	for i=1,#cfg_list do
		if cfg_list[i].layer == lay then
			return cfg_list[i].reward
		end
	end
end

function KuafuHonorhallWGData:GetLayerCfgBySceneId(scene_id)
	local cfg_list = self.honorhall_cfg.layer_list
	for i=1,#cfg_list do
		if cfg_list[i].scene_id == scene_id then
			return cfg_list[i]
		end
	end
end

function KuafuHonorhallWGData:IsKuaFu()
	local join_limit_cfg = self.honorhall_cfg.join_limit[1]
	local limit_day = join_limit_cfg and join_limit_cfg.openday_limit or 0
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	return cur_day >= limit_day
end

function KuafuHonorhallWGData:GetOtherCfg()
	return self.other_cfg
end

function KuafuHonorhallWGData:GetFGBWinRewardList()
    return (self.other_cfg or {}).show_reward or {}
end


