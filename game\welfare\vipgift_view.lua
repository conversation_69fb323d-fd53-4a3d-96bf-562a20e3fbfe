-- F2vip礼包

function WelfareView:InitVipGiftView()
	if not self.vipgift_list then
		self.vipgift_list = AsyncListView.New(VipGiftRender, self.node_list.ph_vip_list)
	end
--[[ 	self.node_list.ph_vip_tip_btn.button:AddClickListener(function ()
		RuleTip.Instance:SetTitle(Language.Welfare.VIPGiftTipsTitle)
		RuleTip.Instance:SetContent(Language.Welfare.VIPGiftTipsDesc)
	end) ]]
end

function WelfareView:DescoryVipGift()
	if self.vipgift_list then
		self.vipgift_list:DeleteMe()
		self.vipgift_list = nil
	end
end

function WelfareView:OnFlushVipGift()
	local cfg_list = WelfareWGData.Instance:GetUpLevelGiftCfgList()
	local ser_data_list = WelfareWGData.Instance:GetUpLevelAndVIPGiftInfo()
	if cfg_list and ser_data_list then
		local can_fetch_flag = ser_data_list.vip_level_gift_can_fetch_flag
		local fetch_flag = ser_data_list.vip_level_gift_fetch_flag
		local server_count = ser_data_list.server_count
		local data_list = {}
		for i=1,#cfg_list do
			if fetch_flag[i] == 1 or server_count[i] <= 0 then
				data_list[100 + i] = cfg_list[i]
			else
				data_list[i] = cfg_list[i]
			end
		end
		data_list = SortTableKey(data_list)
		self.vipgift_list:SetDataList(data_list)
	end
end

----------------------------------------------------------------------------------------------------------------

VipGiftRender = VipGiftRender or BaseClass(WelfareUpLevelGift)

function VipGiftRender:OnFlush()
	local data = self:GetData()
	local gift_info = WelfareWGData.Instance:GetGiftInfoByIndex(GITT_LINGQU_TYPE.VIPGIFT, data.index)
	if not data or not gift_info then
		return
	end
	--self.node_list.get_desc.text.text = string.format(Language.Welfare.LevelGiftTarget, data.level_limit)
	self.node_list.get_desc.text.text = string.format(Language.Welfare.LevelGift, data.level_limit)
	self.node_list.vip_level_lbl.text.text = data.vip_level_limit
	self.node_list.btn_text.text.text = string.format(Language.Welfare.VIPLingQu, data.vip_level_limit)
	--self.node_list.reward_desc.text.text = not gift_info.is_infinite and string.format(Language.Welfare.VIPGiftRemain, gift_info.ser_count) or ""
	--self.node_list.btn_lingqu.rect.anchoredPosition = gift_info.is_infinite and Vector2(self.save_btn_pos.x, 0) or self.save_btn_pos

	self:FlushGiftItem(data, gift_info)
	self:FlushRewardList(data.vip_gift_item, gift_info)
	self:FlushCanExchangeCount(data, gift_info)
end

function VipGiftRender:OnClickGetBtn()
	local data = self:GetData()
	if not IsEmptyTable(data) then
		WelfareWGCtrl.Instance:RequestLevelAndVIPGift(data.index, GITT_LINGQU_TYPE.VIPGIFT)
	end
end