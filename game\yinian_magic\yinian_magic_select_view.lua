YinianMagicSelectView = YinianMagicSelectView or BaseClass(SafeBaseView)
function YinianMagicSelectView:__init()
	self:SetMaskBg()
	self.view_layer = UiLayer.Normal

	self:AddViewResource(0, "uis/view/yinianmagic_prefab", "layout_magic_select")
end

function YinianMagicSelectView:LoadCallBack()
	for i = 1, 2 do
		XUI.AddClickEventListener(self.node_list["magic_select_btn_" .. i], BindTool.Bind(self.OnClickSelectType ,self, i))
	end

	self.select_type = nil
end

function YinianMagicSelectView:ReleaseCallBack()
	self.select_type = nil
end

function YinianMagicSelectView:ShowIndexCallBack()
	self:PlayAnim()
end

function YinianMagicSelectView:OnFlush()
	if self.select_type == nil then
		self:OnClickSelectType(1)
	end
end

function YinianMagicSelectView:PlayAnim()
	RectTransform.SetAnchoredPositionXY(self.node_list["panel_".. 1].rect, -1000, 0)
	RectTransform.SetAnchoredPositionXY(self.node_list["panel_".. 2].rect, 1000, 0)
	local right_tween_1 = self.node_list["panel_".. 1].rect:DOAnchorPos(Vector2(0, 0), 0.5)
	local right_tween_2 = self.node_list["panel_".. 2].rect:DOAnchorPos(Vector2(0, 0), 0.5)
	right_tween_2:OnComplete(function ()
	end)
end

function YinianMagicSelectView:OnClickSelectType(select_type)
	if select_type ~= self.select_type then
		self.select_type = select_type
	else
		local vip_limit = YinianMagicWGData.Instance:GetOtherCfg().vip_limit
		local role_vip = VipWGData.Instance:GetRoleVipLevel()
		if role_vip >= vip_limit then
			YinianMagicWGCtrl.Instance:OpenTipsView(select_type)
		else
			TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.YinianMagicView.VipLimit, vip_limit))
		end
	end

	self:FlushPanelInfo()
end

function YinianMagicSelectView:FlushPanelInfo()
	if not self.select_type then
		return
	end

	for i = 1, 2 do
		XUI.SetGraphicGrey(self.node_list["magic_select_btn_" .. i], self.select_type ~= i)
		XUI.SetGraphicGrey(self.node_list["des_bg" .. i], self.select_type ~= i)
		XUI.SetGraphicGrey(self.node_list["keel_ani_"..i], self.select_type ~= i)
		self.node_list["bg_eff_" .. i]:SetActive(self.select_type == i)
		self.node_list["graphic_eff_" .. i]:SetActive(self.select_type == i)
	end
	self.node_list["pan_eff"]:SetActive(self.select_type == 1)
end