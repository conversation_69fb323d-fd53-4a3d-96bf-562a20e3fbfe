local DEFAULT_MECHA_POSITION = {
	[1] = {x = -519, y = -12},
	[2] = {x = -221, y = -65},
	[3] = {x = 61, y = 0},
	[4] = {x = 346, y = -68},
}

function MechaView:LoadToFightCallBack()
    if not self.mecha_to_fight_item_list then
        self.mecha_to_fight_item_list = {}

        local item_cell = nil
        for i = 1, 4 do
            item_cell = MechaToFighterItemRender.New(self.node_list["mecha_to_fight_item" .. i], self)
            item_cell:SetIndex(i)
            self.mecha_to_fight_item_list[i] = item_cell
        end
    end

    self.battle_data_list = {}
end

function MechaView:ShowToFightCallBack()
    
end

function MechaView:ChangeToFightCallBack()
    
end

function MechaView:ReleaseToFightCallBack()
    if self.mecha_to_fight_item_list then
		for k, v in pairs(self.mecha_to_fight_item_list) do
			v:DeleteMe()
		end
	end

    self.mecha_to_fight_item_list = nil
    self.battle_data_list = nil
end

function MechaView:OnFlushToFightCallBack()
    for i = 1, 4 do
		self.node_list["mecha_to_fight_item"..i].transform.anchoredPosition = DEFAULT_MECHA_POSITION[i]
	end

    self.battle_data_list = MechaWGData.Instance:GetFightBattleDataList()
    for i=1, #self.mecha_to_fight_item_list do
		self.mecha_to_fight_item_list[i]:SetData(self.battle_data_list[i])
	end
end

------------------------------------MechaToFighterItemRender-------------------------------------
MechaToFighterItemRender = MechaToFighterItemRender or BaseClass(BaseRender)

function MechaToFighterItemRender:LoadCallBack()
    if not self.gundam_model then
        self.gundam_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["display_root"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.S,
            can_drag = true,
        }
        
        self.gundam_model:SetRenderTexUI3DModel(display_data)
        -- self.gundam_model:SetUI3DModel(self.node_list["display_root"].transform, self.node_list["display_root"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
    end

    XUI.AddClickEventListener(self.node_list.btn_add, BindTool.Bind(self.OnClickAdd, self))
    XUI.AddClickEventListener(self.node_list.btn_lock, BindTool.Bind(self.OnClickLock, self))
    XUI.AddClickEventListener(self.node_list.btn_change_mecha, BindTool.Bind(self.OnClickChangeMecha, self))
end

function MechaToFighterItemRender:__delete()
    if self.gundam_model then
        self.gundam_model:DeleteMe()
        self.gundam_model = nil
    end

    self.mtf_model_cache = nil
end

function MechaToFighterItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local is_main_battle = self.data.is_main_battle
    self.node_list.flag_main:CustomSetActive(is_main_battle)
    self.node_list.flag_assist:CustomSetActive(not is_main_battle)

    local remind = false
    if is_main_battle then
        remind = MechaWGData.Instance:GetMTFMainFightRemind()
    else
        remind = MechaWGData.Instance:GetMTFHelpFightRemind(self.data.open_cfg.seq)
    end

    self.node_list.operate_red:CustomSetActive(remind)

    local seq = self.data.seq
    local is_open = self.data.is_open
    self.node_list.btn_add:CustomSetActive(is_open and seq < 0)
    self.node_list.btn_lock:CustomSetActive(not is_open)
    self.node_list.btn_change_mecha:CustomSetActive(is_open and seq >= 0)
    self.node_list.opa_bg:CustomSetActive(not is_open or seq < 0)
    self.node_list.bg_active:CustomSetActive(is_open)
    self.node_list.bg_not_active:CustomSetActive(not is_open)
    self.node_list.desc_attr_bg:CustomSetActive(not is_main_battle and is_open and seq >= 0)

    if not is_open then
        local open_cfg = self.data.open_cfg
        local open_level = open_cfg.role_level or 0
        local role_level = RoleWGData.Instance:GetAttr('level')

        if open_level <= role_level then
            local cost_item_id = open_cfg.consume_id
            if cost_item_id and cost_item_id > 0 then
                local cost_item_num = open_cfg.consume_num
                local item_cfg = ItemWGData.Instance:GetItemConfig(cost_item_id)
        
                if not IsEmptyTable(item_cfg) then
                    local name_str = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
                    local has_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
                    local cost_color = has_num >= cost_item_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
                    local num_str = ToColorStr(has_num .. "/" .. cost_item_num, cost_color)
                    self.node_list.desc_btn_lock.text.text = string.format(Language.Mecha.BattlePosActiveDesc, name_str, num_str)
                end
            else
                self.node_list.desc_btn_lock.text.text = Language.Mecha.BattlePosActiveClick
            end
        else
            self.node_list.desc_btn_lock.text.text = string.format(Language.Mecha.BattlePosActiveLevelLimit, open_level)
        end
    else
        -- 显示机甲
        if seq >= 0 then
            self:FlushMechaModel()
            self:FlushHelpBattleInfo()
        else
            self:ClearMechaModel()
        end
    end
end

function MechaToFighterItemRender:FlushMechaModel()
    if IsEmptyTable(self.data) then
        return
    end

    local seq = self.data.seq
    local wear_part_list = MechaWGData.Instance:GetMechaPartWearPartList(seq)
    local res_info = {}

    if not IsEmptyTable(wear_part_list) then
        for k, v in pairs(wear_part_list) do
            if v >= 0 then
                local part_cfg = MechaWGData.Instance:GetPartCfgBySeq(v)
                res_info[part_cfg.part] = part_cfg.res_id
            end
        end
    end

    if not IsEmptyTable(res_info) then 
        local part_info = {
            gundam_seq = self.data.seq,
            gundam_body_res = res_info[MECHA_PART_TYPE.BODY] or 0,
            gundam_weapon_res = res_info[MECHA_PART_TYPE.WEAPON] or 0,
            gundam_left_arm_res = res_info[MECHA_PART_TYPE.LEFT_HAND] or 0,
            gundam_right_arm_res = res_info[MECHA_PART_TYPE.RIGHT_HAND] or 0,
            gundam_left_leg_res = res_info[MECHA_PART_TYPE.LEFT_FOOT] or 0,
            gundam_right_leg_res = res_info[MECHA_PART_TYPE.RIGHT_FOOT] or 0,
            gundam_left_wing_res = res_info[MECHA_PART_TYPE.LEFT_WING] or 0,
            gundam_right_wing_res = res_info[MECHA_PART_TYPE.RIGHT_WING] or 0,
        }

        if self:IsMTFModelChange(part_info) then
            self.mtf_model_cache = part_info
            self.gundam_model:SetGundamModel(part_info)
        end
    end
end

function MechaToFighterItemRender:ClearMechaModel()
    self.mtf_model_cache = nil
    self.gundam_model:RemoveGundamAll()
end

function MechaToFighterItemRender:IsMTFModelChange(part_info)
    if IsEmptyTable(self.mtf_model_cache) then
        return true
    end

    for k, v in pairs(part_info) do
        if self.mtf_model_cache [k] ~= v then
            return true
        end
    end
    
    return false
end

function MechaToFighterItemRender:FlushHelpBattleInfo()
    if IsEmptyTable(self.data) then
        return
    end

    local seq = self.data.seq
    local is_open = self.data.is_open
    local is_main_battle = self.data.is_main_battle

    if is_open and seq >= 0 and not is_main_battle then
        local attr_desc_info = MechaWGData.Instance:GetHelpFightAttrDesc(seq)

        if not IsEmptyTable(attr_desc_info) then
            local desc_attr_content = ""
            local space = "\n"
            local attr_name_info = Language.Mecha.HelpBattleAttrName

            if attr_desc_info.bianshen_second and attr_desc_info.bianshen_second > 0 then
                desc_attr_content = desc_attr_content == "" and desc_attr_content or desc_attr_content .. space
                desc_attr_content = string.format(attr_name_info.bianshen_second, attr_desc_info.bianshen_second)
            end

            if attr_desc_info.bianshen_cd and attr_desc_info.bianshen_cd > 0 then
                desc_attr_content = desc_attr_content == "" and desc_attr_content or desc_attr_content .. space
                desc_attr_content = desc_attr_content .. string.format(attr_name_info.bianshen_cd, attr_desc_info.bianshen_cd)
            end

            if attr_desc_info.bianshen_hp and attr_desc_info.bianshen_hp > 0 then
                desc_attr_content = desc_attr_content == "" and desc_attr_content or desc_attr_content .. space
                desc_attr_content = desc_attr_content .. string.format(attr_name_info.bianshen_hp, math.floor(attr_desc_info.bianshen_hp / 100))
            end

            if attr_desc_info.bianshen_speed and attr_desc_info.bianshen_speed > 0 then
                desc_attr_content = desc_attr_content == "" and desc_attr_content or desc_attr_content .. space
                desc_attr_content = desc_attr_content .. string.format(attr_name_info.bianshen_speed, attr_desc_info.bianshen_speed)
            end
            
            self.node_list.desc_attr_bg:CustomSetActive(true)
            self.node_list.desc_attr_info.text.text = desc_attr_content
        else
            self.node_list.desc_attr_bg:CustomSetActive(false)
        end
    else
        self.node_list.desc_attr_bg:CustomSetActive(false)
    end
end

function MechaToFighterItemRender:OnClickLock()
    if IsEmptyTable(self.data) then
        return
    end

    local open_cfg = self.data.open_cfg
    local open_level = open_cfg.role_level or 0
    local role_level = RoleWGData.Instance:GetAttr('level')

    if open_level <= role_level then
        local cost_item_id = open_cfg.consume_id
        if cost_item_id and cost_item_id > 0 then
            local cost_item_num = open_cfg.consume_num
            local item_cfg = ItemWGData.Instance:GetItemConfig(cost_item_id)
    
            if not IsEmptyTable(item_cfg) then
                local name_str = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
                local has_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
                local cost_color = has_num >= cost_item_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
        
                if has_num >= cost_item_num then

                    local ok_func = function()
                        MechaWGCtrl.Instance:SendRequest(MECHA_OPERA_TYPE.HELP_FIGHT_OPEN, self.data.open_cfg.seq)
                        TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo, is_success = true, pos = Vector2(0, 0)})
                    end

                    local desc = string.format(Language.Mecha.BattlePosCostActiveDesc, name_str, cost_item_num)
                    TipWGCtrl.Instance:OpenConfirmAlertTips(desc, ok_func)
                else
                    TipWGCtrl.Instance:OpenItem({item_id = cost_item_id})
                end
            end
        else
            MechaWGCtrl.Instance:SendRequest(MECHA_OPERA_TYPE.HELP_FIGHT_OPEN, self.data.open_cfg.seq)
            TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo, is_success = true, pos = Vector2(0, 0)})
        end
    else
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Mecha.BattlePosActiveLevelLimit, open_level))
    end
end

function MechaToFighterItemRender:OnClickAdd()
    if IsEmptyTable(self.data) then
        return
    end

    MechaWGCtrl.Instance:OpenMechaSelectView(self.data)
end

function MechaToFighterItemRender:OnClickChangeMecha()
    if IsEmptyTable(self.data) then
        return
    end

    MechaWGCtrl.Instance:OpenMechaSelectView(self.data)
end