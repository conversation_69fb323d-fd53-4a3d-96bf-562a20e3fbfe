ThunderManaWGData = ThunderManaWGData or  BaseClass()

ThunderManaWGData.ThunderType = {
	ShadyType = 0,					--阴类型
	SunType = 1,					--阳类型
}

ThunderManaWGData.EquipCount = 5     --装备数量

function ThunderManaWGData:__init()
	if ThunderManaWGData.Instance ~= nil then
		ErrorLog("[ThunderManaWGData] attempt to create singleton twice!")
		return
	end

	ThunderManaWGData.Instance = self
    self:InitCfg()
	self:InitChacheData()

	self.thunder_item_list = {}
	self.thunder_equip_bag_list = {}

	RemindManager.Instance:Register(RemindName.ThunderManaShady, BindTool.Bind(self.GetThunderManaRemind, self, 0))
	RemindManager.Instance:Register(RemindName.ThunderManaSun, BindTool.Bind(self.GetThunderManaRemind, self, 1))
end

function ThunderManaWGData:__delete()
	ThunderManaWGData.Instance = nil

	RemindManager.Instance:UnRegister(RemindName.ThunderManaShady)
	RemindManager.Instance:UnRegister(RemindName.ThunderManaShady)
end

function ThunderManaWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("thunder_cfg_auto")
	self.thunder_type_cfg = cfg.thunder
	self.part_cfg = ListToMap(cfg.part, "seq", "part")
	self.part_level_cfg = ListToMap(cfg.part_level, "seq", "part", "level")
	self.part_level_cost_cfg = ListToMap(cfg.part_level, "cost_item_id")
	self.part_grade_cfg = ListToMap(cfg.part_grade, "seq", "part", "grade")
	self.part_grade_cost_cfg = ListToMap(cfg.part_grade, "cost_item_id")
	self.suit_cfg = ListToMap(cfg.suit, "seq", "index")
	self.equip_star_cfg = ListToMap(cfg.equip_star, "item_id")
	self.equip_next_star_cfg = ListToMap(cfg.equip_star, "next_star_id")
	self.virtual_star_item_cfg = ListToMap(cfg.virtual_star_item_cfg, "seq", "star_num")
	self.other_cfg = cfg.other[1]
end

-- 初始化缓存数据
function ThunderManaWGData:InitChacheData()
	self.equip_star_up_cache = {}	-- 升星条件列表
	self.equip_star_item_list = {}	-- 星级对应的物品
	self.equip_type_item_list = {}  -- 按照类型缓存

	if self.equip_star_cfg then
		for k, cfg in pairs(self.equip_star_cfg) do
			if cfg and cfg.item_id and cfg.next_star_id then

				-- 缓存升星条件
				local equip_item_id = cfg.item_id
				local starup_beast_group_str_list = Split(cfg.up_star_cost, "|")
				local starup_beast_list_data = {}
	
				for i, starup_beast_str_list in ipairs(starup_beast_group_str_list) do
					local starup_beast_list = Split(starup_beast_str_list, ",")
					local param_1 = tonumber(starup_beast_list[1]) or 0 
					local param_2 = tonumber(starup_beast_list[2]) or 0 
			
					local starup_beast_data = {}
					starup_beast_data.num = param_2
		
					if param_1 > 10000 then			-- 特殊物品
						starup_beast_data.special_item = param_1
					else
						starup_beast_data.star = param_1
					end
	
					starup_beast_list_data[i] = starup_beast_data
				end

				self.equip_star_up_cache[equip_item_id] = starup_beast_list_data

				-- 缓存星级对应物品
				if not self.equip_star_item_list[cfg.seq] then
					self.equip_star_item_list[cfg.seq] = {}
				end

				-- 缓存星级对应物品
				if not self.equip_star_item_list[cfg.seq][cfg.star] then
					self.equip_star_item_list[cfg.seq][cfg.star] = {}
				end

				table.insert(self.equip_star_item_list[cfg.seq][cfg.star], equip_item_id)
			end

			local type_equip_data = {}
			type_equip_data.item_id = cfg.item_id
			type_equip_data.seq = cfg.seq
			type_equip_data.part = cfg.part
			type_equip_data.star = cfg.star
			type_equip_data.next_star_id = cfg.next_star_id
			if not self.equip_type_item_list[cfg.seq] then
				self.equip_type_item_list[cfg.seq] = {}
			end

			if not self.equip_type_item_list[cfg.seq][cfg.part] then
				self.equip_type_item_list[cfg.seq][cfg.part] = {}
			end

			table.insert(self.equip_type_item_list[cfg.seq][cfg.part], type_equip_data)
		end
	end
end

function ThunderManaWGData:SetAllThunderItemInfo(protocol)
	self.thunder_item_list = protocol.thunder_item_list
end

function ThunderManaWGData:SetSingleThunderItemInfo(protocol)
	local data = protocol.change_data
	local seq = data.seq
	if self.thunder_item_list[seq] then
		self.thunder_item_list[seq] = data.thunder_item
	end
end

function ThunderManaWGData:SetAllThunderBagInfo(protocol)
	self.thunder_equip_bag_list = protocol.thunder_grid_list
	self.item_id_num_t = {}
	for k, info in pairs(self.thunder_equip_bag_list) do
		if info.item_id ~= 0 and info.num ~= 0 then
			self:SetOneThunderBagNumInfo(info)
		end
	end
end

function ThunderManaWGData:SetThunderBagChangeInfo(protocol)
	local new_data = protocol.change_info
	local index = new_data.index

	local old_info = self:GetThunderBagInfoByIndex(index)

	local old_num = old_info and old_info.num or 0
	local old_item_id = old_info and old_info.item_id or 0
	local now_num = new_data.num or 0
	local now_item_id = new_data.item_id or 0

	local change_reason = GameEnum.DATALIST_CHANGE_REASON_UPDATE
	local change_num = 0
	local change_item_id = 0
	if now_item_id ~= 0 and old_item_id ~= 0 then	-- 数量发生变化
		change_num = now_num - old_num
		change_item_id = now_item_id
	elseif now_item_id ~= 0 and old_item_id == 0 then	-- 物品新增
		change_num = now_num
		change_item_id = now_item_id
	elseif now_item_id == 0 and old_item_id ~= 0 then	-- 物品减少
		change_num = old_num * -1
		change_item_id = old_item_id
	end

	self.thunder_equip_bag_list[index] = new_data
	self:SetOneThunderBagNumInfo(new_data, change_num, change_item_id)

	local change_reason = GameEnum.DATALIST_CHANGE_REASON_UPDATE
	if old_item_id == 0 and now_item_id ~= 0 then
		change_reason = GameEnum.DATALIST_CHANGE_REASON_ADD
	elseif old_item_id > 0 and now_num < old_num then
		change_reason = GameEnum.DATALIST_CHANGE_REASON_REMOVE
	end

	ThunderManaWGCtrl.Instance:OnThunderEquipItemDataChange(new_data, index, change_reason, old_num, now_num)
end

--背包数据
function ThunderManaWGData:GetAllThunderBagInfo()
	return self.thunder_equip_bag_list
end

--背包索引数据
function ThunderManaWGData:GetThunderBagInfoByIndex(index)
	return self.thunder_equip_bag_list[index] or {}
end

-- 设置背包数量缓存
function ThunderManaWGData:SetOneThunderBagNumInfo(info, change_num, change_item_id)
	if not self.item_id_num_t then
		self.item_id_num_t = {}
	end

	local change_item_id = change_item_id or info.item_id 
	if change_item_id <= 0 then
		return
	end

	if self.item_id_num_t[change_item_id] then
		change_num = change_num or info.num
		self.item_id_num_t[change_item_id] = self.item_id_num_t[change_item_id] + change_num
	else
		self.item_id_num_t[change_item_id] = info.num
	end
end

--获得背包里的物品数量
function ThunderManaWGData:GetItemNumInBagById(item_id)
	local empty = {}
	return (self.item_id_num_t or empty)[item_id] or 0
end

--获得背包里的物品数量
function ThunderManaWGData:GetItemNumInBagByIndex(index, item_id)
	local info = self:GetThunderBagInfoByIndex(index)
	if info then
		if item_id then
			if info.item_id == item_id then
				return info.num
			end
		else
			return info.num
		end
	end
	return 0
end

--根据物品id获得在【背包、材料背包】中的index
function ThunderManaWGData:GetItemIndex(item_id)
	for k,v in pairs(self.thunder_equip_bag_list) do
		if v.item_id == item_id then
			return v.index
		end
	end

	return -1
end

--根据物品id获得在【背包】中所有的index
function ThunderManaWGData:GetItemListIndex(item_id)
	local index_list = {}
	for k,v in pairs(self.thunder_equip_bag_list) do
		if v.item_id == item_id then
			table.insert(index_list, v.index)
		end
	end
	return index_list
end

function ThunderManaWGData:GetThunderInfoByType(thunder_type)
	return self.thunder_item_list[thunder_type]
end

function ThunderManaWGData:GetEquipPartInfo(thunder_type, part)
	local thunder_info = self:GetThunderInfoByType(thunder_type)
	local part_item_list = thunder_info and thunder_info.part_item_list or {}
	return part_item_list[part]
end

function ThunderManaWGData:GetOtherCfg()
	return self.other_cfg
end

function ThunderManaWGData:GetAllEquipPartCfgByType(thunder_type)
	return self.part_cfg[thunder_type]
end

function ThunderManaWGData:GetEquipPartCfgByType(thunder_type, part)
	return (self.part_cfg[thunder_type] or {})[part]
end

function ThunderManaWGData:GetEquipItemStarCfg(item_id)
	return self.equip_star_cfg[item_id]
end

function ThunderManaWGData:IsThunderManaEquip(item_id)
    return nil ~= self.equip_star_cfg[item_id]
end

function ThunderManaWGData:GetEquipNextItemStarCfg(next_item_id)
	return self.equip_next_star_cfg[next_item_id]
end

function ThunderManaWGData:GetEquipPartLevelCfg(thunder_type, part, level)
	return ((self.part_level_cfg[thunder_type] or {})[part] or {})[level]
end

--升级消耗物
function ThunderManaWGData:GetIsEquipPartLevelCostCfg(stuff_id)
    return self.part_level_cost_cfg[stuff_id] ~= nil
end

function ThunderManaWGData:GetEquipPartGradeCfg(thunder_type, part, level)
	return ((self.part_grade_cfg[thunder_type] or {})[part] or {})[level]
end

--升阶消耗物
function ThunderManaWGData:GetIsEquipPartGradeCostCfg(stuff_id)
    return self.part_grade_cost_cfg[stuff_id] ~= nil
end

function ThunderManaWGData:GetThunderSuitCfg(thunder_type, index)
	if index then
		return (self.suit_cfg[thunder_type] or {})[index]
	else
		return self.suit_cfg[thunder_type]
	end
end

-- 获取当前装备物品的升星数据
function ThunderManaWGData:GetEquipComposeCost(equip_item_id)
	local empty = {}
	return (self.equip_star_up_cache or empty)[equip_item_id]
end

-- 获取当前的星级的所有符合的物品(要匹配自身的类型)
function ThunderManaWGData:GetEquipItemListByStar(equip_star, self_item_id)
	local cfg = self:GetEquipItemStarCfg(self_item_id)
	if not cfg then
		return nil
	end

	local empty = {}
	return ((self.equip_star_item_list or empty)[cfg.seq] or empty)[equip_star]
end

-- 获取当前对应虚拟物
function ThunderManaWGData:GetVirualItemByStar(aim_item_id, star)
	local cfg = self:GetEquipItemStarCfg(aim_item_id)
	if not cfg then
		return nil
	end

	local empty = {}
	return ((self.virtual_star_item_cfg or empty)[cfg.seq] or empty)[star]
end

-------------------------------------------
--------------------------------------------
--等级属性
function ThunderManaWGData:GetEquipPartLevelAttrList(thunder_type, part, level)
	local cur_level_cfg = self:GetEquipPartLevelCfg(thunder_type, part, level)
	local next_level_cfg = self:GetEquipPartLevelCfg(thunder_type, part, level + 1)
	local attr_level_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_level_cfg, next_level_cfg, "attr_id", "attr_value", 1, 5)

	return attr_level_list
end

--星级属性
function ThunderManaWGData:GetEquipPartStarAttrList(item_id)
	local cur_item_cfg = self:GetEquipItemStarCfg(item_id)
	local next_star_id = cur_item_cfg and cur_item_cfg.next_star_id or 0
	local next_item_cfg = self:GetEquipItemStarCfg(next_star_id)

	local attr_star_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_item_cfg, next_item_cfg, "attr_id", "attr_value", 1, 5)

	return attr_star_list
end

function ThunderManaWGData:GetThunderSuitFlagStateByIndex(thunder_type, index)
	local thunder_info = self:GetThunderInfoByType(thunder_type)
	if not thunder_info then
		return REWARD_STATE_TYPE.UNDONE
	end

	local suit_active_flag_list = thunder_info.suit_active_flag_list or {}
	local cur_flag = suit_active_flag_list[index] or 0
	if cur_flag == 1 then
		return REWARD_STATE_TYPE.FINISH
	end

	local cur_suit_cfg = self:GetThunderSuitCfg(thunder_type, index)
	if not cur_suit_cfg then
		return REWARD_STATE_TYPE.UNDONE
	end

	--需要依次领取
	local pre_flag_state = true
	if cur_suit_cfg.index ~= 0 and suit_active_flag_list[cur_suit_cfg.index - 1] then
		pre_flag_state = suit_active_flag_list[cur_suit_cfg.index - 1] == 1 
	end

	local need_star = cur_suit_cfg.need_star
	local need_num = cur_suit_cfg.need_num
	local equip_count = self:GetThunderGetEquipNum(thunder_type, need_star)
	
	if pre_flag_state and equip_count >= need_num then
		return REWARD_STATE_TYPE.CAN_FETCH
	end 

	return REWARD_STATE_TYPE.UNDONE
end

--下一个领取奖励索引
function ThunderManaWGData:GetThunderGetSuitRewardIndex(thunder_type)
	local thunder_info = self:GetThunderInfoByType(thunder_type)
	if not thunder_info then
		return 0
	end

	local cur_all_suit_cfg = self:GetThunderSuitCfg(thunder_type)
	local suit_active_flag_list = thunder_info.suit_active_flag_list or {}
	for k, v in pairs(cur_all_suit_cfg) do
		if suit_active_flag_list[v.index] and suit_active_flag_list[v.index] ~= 1 then
			return k
		end
	end

	return -1
end

--符合条件装备数量
function ThunderManaWGData:GetThunderGetEquipNum(thunder_type, need_star)
	local thunder_info = self:GetThunderInfoByType(thunder_type)
	if not thunder_info then
		return 0 
	end

	local equip_count = 0
	for i = 0, 4 do
		local item_id =  thunder_info.part_item_list[i].item_id or 0
		local equip_cfg = self:GetEquipItemStarCfg(item_id)
		if equip_cfg and equip_cfg.star >= need_star then 
			equip_count = equip_count + 1
		end
	end

	return equip_count
end

function ThunderManaWGData:GetThunderCapValue(thunder_type)
	local attribute = AttributePool.AllocAttribute()
	local cap = 0

	local thunder_info = self:GetThunderInfoByType(thunder_type)
	if not thunder_info then
		return cap
	end

	for k, v in pairs(thunder_info.part_item_list) do
		if v.item_id > 0 then
			local level_attr_list = self:GetEquipPartLevelAttrList(v.seq, v.part, v.level)
			local star_attr_list = self:GetEquipPartStarAttrList(v.item_id) 
			local cur_grade_cfg = self:GetEquipPartGradeCfg(v.seq, v.part, v.grade)
			local level_attr_per = (cur_grade_cfg.attr_per or 0) * 0.0001
			local star_attr_per = (cur_grade_cfg.equip_attr_per or 0) * 0.0001
			--等级属性
			for k, v in pairs(level_attr_list) do
				local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v.attr_str) --百分比属性不叠加
				if is_per then
					attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
				else
					attribute[v.attr_str] = attribute[v.attr_str] + (v.attr_value * (1 + level_attr_per))
				end
			end

			for k, v in pairs(star_attr_list) do
				local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v.attr_str) --百分比属性不叠加
				if is_per then
					attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
				else
					attribute[v.attr_str] = attribute[v.attr_str] + (v.attr_value * (1 + star_attr_per))
				end
			end
		end
	end

	cap = AttributeMgr.GetCapability(attribute)

	return cap
end

-- 获取升星预览列表
function ThunderManaWGData:GetThundePreviewTypeList(thunder_type, part)
	local show_list = {}
	local item_list = (self.equip_type_item_list[thunder_type] or {})[part] or {}
	for k, v in pairs(item_list) do
		if v.next_star_id > 0 then
			local data = {}
			data.item_id = v.item_id
			data.next_star_id = v.next_star_id
			data.star = v.star
			table.insert(show_list, data)
		end
	end

	if not IsEmptyTable(show_list) then
		table.sort(show_list,SortTools.KeyUpperSorters("star"))
	end

	return show_list
end

-- 获取当前的物品合成的数据(筛选出是否能合成)
function ThunderManaWGData:GetOneEquipComposeListData(equip_item_id, now_cache_table, equip_seq, equip_part)
	local equip_star_up_list = self:GetEquipComposeCost(equip_item_id)
	local compose_data = {}
	local is_can_compose = true
	compose_data.special_list = nil
	compose_data.star_list = {}
	compose_data.self_item_id = equip_item_id
	compose_data.equip_seq = equip_seq or -1
	compose_data.select = true
	compose_data.param = equip_part or self:GetItemIndex(compose_data.self_item_id)
	
	local cfg = self:GetEquipItemStarCfg(equip_item_id)
	if (not cfg) or (cfg.next_star_id == 0) then
		return false, compose_data
	end

	for i, v in ipairs(equip_star_up_list) do
		if v.special_item ~= nil then
			local is_satisfy, special_list = self:GetSpecialItemNumIsSatisfy(v.special_item, v.num, now_cache_table)
			compose_data.special_list = special_list
			is_can_compose = is_can_compose and is_satisfy
		elseif v.star ~= nil then
			local is_star_satisfy, star_list = self:GetStarItemNumIsSatisfy(equip_item_id, v.star, v.num, now_cache_table)
			compose_data.star_list[v.star] = star_list
			is_can_compose = is_can_compose and is_star_satisfy
		end
	end

	local get_now_cache_fun = function(aim_item_id)
		if not now_cache_table then
			return nil
		end

		for _, data in ipairs(now_cache_table) do
			if data.item_id == aim_item_id then
				return data
			end
		end
	end

	if now_cache_table ~= nil then
		if not is_can_compose then	-- 不能合成要还原个数
			if compose_data.special_list then
				for i, v in ipairs(compose_data.special_list) do
					local data = get_now_cache_fun(v.cur_item_id)
					if data and v.is_deduct then
						data.num = data.num + v.cur_item_num
					end
				end
			end

			if compose_data.star_list then
				for _, list in pairs(compose_data.star_list) do
					for _, star_data in ipairs(list) do
						local data = get_now_cache_fun(star_data.cur_item_id)
						if data and star_data.is_deduct then
							data.num = data.num + star_data.cur_item_num
						end
					end
				end
			end
		end
	end

	return is_can_compose, compose_data
end

-- 检测星级道具是否满足个数
function ThunderManaWGData:GetSpecialItemNumIsSatisfy(var_special_item_id, need_num, now_cache_table)
	local need_num = need_num
	local special_list = {}

	local is_satisfy, cur_num, is_deduct = self:CheckItemIsSatisfy(var_special_item_id, need_num, now_cache_table)
	if cur_num ~= 0 then
		local data = {}
		data.cur_item_id = var_special_item_id
		data.cur_item_num = cur_num
		data.is_deduct = is_deduct

		table.insert(special_list, data)
	end

	return is_satisfy, special_list
end

-- 检测星级道具是否满足个数
function ThunderManaWGData:GetStarItemNumIsSatisfy(self_item_id, star, need_num, now_cache_table)
	local need_num = need_num
	local star_list = {}

	local list = self:GetEquipItemListByStar(star, self_item_id)
	for i, item_id in ipairs(list) do
		local is_satisfy, cur_num, is_deduct = self:CheckItemIsSatisfy(item_id, need_num, now_cache_table)
		if cur_num ~= 0 then
			local data = {}
			data.cur_item_id = item_id
			data.cur_item_num = cur_num
			data.is_deduct = is_deduct

			need_num = need_num - cur_num
			table.insert(star_list, data)
		end

		if is_satisfy then
			return true, star_list
		end
	end

	return false, star_list
end

-- 获取背包中的物品是否满足条件个数
function ThunderManaWGData:CheckItemIsSatisfy(equip_item_id, need_num, now_cache_table)
	if not equip_item_id then
		return
	end

	local get_now_cache_fun = function(aim_item_id)
		if not now_cache_table then
			return nil
		end

		for _, data in ipairs(now_cache_table) do
			if data.item_id == aim_item_id then
				return data
			end
		end
	end

	local item_num = self:GetItemNumInBagById(equip_item_id)
	if now_cache_table ~= nil then
		item_num = (get_now_cache_fun(equip_item_id) or {}).num or 0
	end

	if item_num > 0 then
		local data = get_now_cache_fun(equip_item_id)
		if item_num >= need_num then
			if data then
				data.num = data.num - need_num
			end

			return true, need_num, true
		else
			if data then
				data.num = data.num - item_num
			end
		end
	end

	return false, item_num, false
end

-- 获取批量升星列表(最多选取20个)
function ThunderManaWGData:GetQuickBagComposeData()
	-- 先整合所有的物品个数
	local bag_list = self:GetAllThunderBagInfo()
	if not bag_list then
		return nil
	end

	local now_cache_table = {}
	for k, info in pairs(self.thunder_equip_bag_list) do
		if info.item_id ~= 0 and info.num ~= 0 then
			local data = {}
			data.item_id = info.item_id
			data.num = info.num
			data.is_equip = false

			local star_cfg = self:GetEquipItemStarCfg(data.item_id)
			if star_cfg then
				data.self_star = star_cfg.star

				local part_info = self:GetEquipPartInfo(star_cfg.seq, star_cfg.part)
				if part_info and part_info.item_id > 0 then
					local part_star_cfg = self:GetEquipItemStarCfg(part_info.item_id)
					data.equip_satr = part_star_cfg.star or 1
				end
			end

			table.insert(now_cache_table, data)
		end
	end

	table.sort(now_cache_table, function(a, b)
		local compose_a = a.equip_satr or 1
		local compose_b = b.equip_satr or 1

		if compose_a ~= compose_b then
			return compose_a < compose_b
		end

		compose_a = a.self_star or 1
		compose_b = b.self_star or 1
		return compose_a > compose_b
	end)

	local final_table = {}
	local cache_same_table = {}
	if self.thunder_item_list then
		-- 遍历装备
		local equip_item_data = {}
		for seq, thunder_info in pairs(self.thunder_item_list) do
			if thunder_info and thunder_info.part_item_list then
				for part, part_item in pairs(thunder_info.part_item_list) do
					if part_item and part_item.item_id ~= 0 then
						local star_cfg = self:GetEquipItemStarCfg(part_item.item_id)
						if star_cfg and star_cfg.up_star_type == 0 then
							local equip_data = {}
							equip_data.item_id = part_item.item_id
							equip_data.seq = seq
							equip_data.part = part
							equip_data.satr = star_cfg.star or 1
							table.insert(equip_item_data, equip_data)
						end
					end
				end
			end
		end

		table.sort(equip_item_data, SortTools.KeyLowerSorter("satr"))
		for i, part_item in ipairs(equip_item_data) do
			local is_can_compose, compose_data = self:GetOneEquipComposeListData(part_item.item_id, now_cache_table, part_item.seq, part_item.part)
			if is_can_compose then
				if not cache_same_table[part_item.seq] then
					cache_same_table[part_item.seq] = {}
				end

				cache_same_table[part_item.seq][part_item.part] = true
				table.insert(final_table, compose_data)
			end
		end
	end

	return self:GetFinalBatchList(final_table, now_cache_table, cache_same_table)
end

-- 获取批量升星列表(最多选取20个)
-- 背包中同类型星级只做一次处理
function ThunderManaWGData:GetFinalBatchList(final_table, now_cache_table, cache_same_table)
	if #final_table >= 20 then
		return final_table
	end

	local is_find_compose = false
	for _, data in ipairs(now_cache_table) do
		if data.item_id ~= 0 and data.num ~= 0 then
			local star_cfg = self:GetEquipItemStarCfg(data.item_id)
			if star_cfg and star_cfg.up_star_type == 0 then
				local seq = star_cfg.seq or 0
				local part = star_cfg.part or 0
				local is_cache_ed = cache_same_table[seq] and cache_same_table[seq][part] or false

				if not is_cache_ed then
					data.num = data.num - 1
					local is_can_compose, compose_data = self:GetOneEquipComposeListData(data.item_id, now_cache_table)
		
					if is_can_compose then
						table.insert(final_table, compose_data)
						is_find_compose = true

						if not cache_same_table[seq] then
							cache_same_table[seq] = {}
						end
		
						cache_same_table[seq][part] = true
						break
					else
						-- 还原个数
						data.num = data.num + 1
					end
				end
			end
		end
	end

	if is_find_compose then	-- 找到了尝试继续找
		return self:GetFinalBatchList(final_table, now_cache_table, cache_same_table)
	end

	return final_table
end

-----------------------remind---------------------
function ThunderManaWGData:GetThunderManaRemind(thunder_type)
	--4件装备
	for i = 0, 4 do
		if self:GetEuqipSlotRemindByPart(thunder_type, i) then
			self:SetThunderManaStrengthen(thunder_type, 1)
			return 1
		end
	end

	if self:GetEuqipSlotSuitRemind(thunder_type) then
		self:SetThunderManaStrengthen(thunder_type, 1)
		return 1
	end

	local batch_list = self:GetQuickBagComposeData()
	if not IsEmptyTable(batch_list) then
		self:SetThunderManaStrengthen(thunder_type, 1)
		return 1
	end

	self:SetThunderManaStrengthen(thunder_type, 0)
	return 0
end

function ThunderManaWGData:GetEuqipSlotRemindByPart(thunder_type, part)
	local part_info = self:GetEquipPartInfo(thunder_type, part)
	if not part_info then
		return false
	end

	--有更好得装备(没穿装备)
	for k, v in pairs(self.thunder_equip_bag_list) do
		if v.item_id > 0 and part_info.item_id <= 0 and v.seq == thunder_type and v.part == part then
			return true
		end
	end

	if self:GetEuqipSlotLevelRemind(thunder_type, part) then
		return true
	elseif self:GetEuqipSlotGradeRemind(thunder_type, part) then
		return true
	elseif self:GetEuqipSlotStarRemind(thunder_type, part) then
		return true
	end

	return false
end

-- 等级红点
function ThunderManaWGData:GetEuqipSlotLevelRemind(thunder_type, part)
	local part_info = self:GetEquipPartInfo(thunder_type, part)
	if not part_info then
		return false
	end

	if self:GetEuqipSlotIsBetterWear(thunder_type, part) then
		return true
	end

	if part_info.item_id > 0 then
		local cur_level_cfg = self:GetEquipPartLevelCfg(part_info.seq, part_info.part, part_info.level)
		local next_level_cfg = self:GetEquipPartLevelCfg(part_info.seq, part_info.part, part_info.level + 1)
		local equip_cfg = self:GetEquipItemStarCfg(part_info.item_id)
		if cur_level_cfg and next_level_cfg and equip_cfg then
			local part_level_limit = equip_cfg and equip_cfg.part_level_limit or 0
			local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
			if part_info.level < part_level_limit and item_num >= cur_level_cfg.cost_item_num then
				return true
			end
		end 
	end

	return false
end

-- 是否是更好的穿戴（已穿戴情况下）
function ThunderManaWGData:GetEuqipSlotIsBetterWear(thunder_type, part)
	local part_info = self:GetEquipPartInfo(thunder_type, part)
	if not part_info then
		return false
	end

	for k, v in pairs(self.thunder_equip_bag_list) do
		if v.item_id > 0 and part_info.item_id > 0 and v.seq == thunder_type and v.part == part and
			v.star > part_info.star then
				return true
		end
	end

	return false
end

--阶级红点
function ThunderManaWGData:GetEuqipSlotGradeRemind(thunder_type, part)
	local part_info = self:GetEquipPartInfo(thunder_type, part)
	if not part_info then
		return false
	end

	if part_info.item_id > 0 then
		local cur_grade_cfg = self:GetEquipPartGradeCfg(part_info.seq, part_info.part, part_info.grade)
		local next_grade_cfg = self:GetEquipPartGradeCfg(part_info.seq, part_info.part, part_info.grade + 1)
		local equip_cfg = self:GetEquipItemStarCfg(part_info.item_id)
		if cur_grade_cfg and next_grade_cfg and equip_cfg then
			local part_grade_limit = equip_cfg and equip_cfg.part_grade_limit or 0
			local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_grade_cfg.cost_item_id)
			if part_info.grade < part_grade_limit and item_num >= cur_grade_cfg.cost_item_num then
				return true
			end
		end 
	end

	return false
end

-- 装备升星红点
function ThunderManaWGData:GetEuqipSlotStarRemindById(item_id)
	-- 先整合所有的物品个数
	local bag_list = self:GetAllThunderBagInfo()
	if not bag_list then
		return nil
	end

	local now_cache_table = {}
	for k, info in pairs(bag_list) do
		if info.item_id ~= 0 and info.num ~= 0 then
			local data = {}
			data.item_id = info.item_id
			data.num = info.num

		table.insert(now_cache_table, data)
		end
	end

	return ThunderManaWGData.Instance:GetOneEquipComposeListData(item_id, now_cache_table)
end

-- 装备升星红点（吞噬）
local ONCE_SWALLOW_NUM = 20
function ThunderManaWGData:GetEuqipSlotSwallowStarRemindById(part_info)
	local equip_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(part_info.item_id)
	if not equip_cfg then
		return
	end

	local bag_list = self:GetAllThunderBagInfo()
	local swallow_list = {}
	local is_compose = false
	local now_exp = part_info.exp or 0
	local full_exp = equip_cfg.up_star_cost1 or 0
	local need_exp = full_exp - now_exp
	local now_add_exp = 0

	for i, v in pairs(bag_list) do
		if part_info.seq == v.seq and v.item_id > 0 and v.star <= part_info.star then
			local cfg = self:GetEquipItemStarCfg(v.item_id)
			local item_exp_value = cfg and cfg.item_exp_value or 0
			local last_exp = need_exp - now_add_exp
			local need_now_num = math.ceil(last_exp / item_exp_value)
			local data = {}
			data.cur_item_id = v.item_id
			-- 大于这个数量，全拿走
			if need_now_num > v.num then
				data.cur_item_num = v.num
			else
				data.cur_item_num = need_now_num
			end

			if data.cur_item_num > 0 and #swallow_list <= 20 then
				table.insert(swallow_list, data)
			end

			if data.cur_item_num > 0 then
				now_add_exp = now_add_exp + item_exp_value * data.cur_item_num
			end

			if now_add_exp >= need_exp then
				is_compose = true
				break
			end
		end
	end

	return is_compose, swallow_list
end

--升星红点
function ThunderManaWGData:GetEuqipSlotStarRemind(thunder_type, part)
	local is_compose = false
	local part_info = self:GetEquipPartInfo(thunder_type, part)
	if not part_info or part_info.item_id <= 0 then
		return is_compose
	end

	local equip_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(part_info.item_id)
	if not equip_cfg then
		return is_compose
	end

	if equip_cfg.up_star_type == 0 then
		is_compose = self:GetEuqipSlotStarRemindById(part_info.item_id)
	else
		is_compose = self:GetEuqipSlotSwallowStarRemindById(part_info)
	end

	return is_compose
end

--套装目标红点
function ThunderManaWGData:GetEuqipSlotSuitRemind(thunder_type)
	local cur_all_suit_cfg = self:GetThunderSuitCfg(thunder_type)
	if not cur_all_suit_cfg then
		return false
	end

	for k, v in pairs(cur_all_suit_cfg) do
		local flag_state = self:GetThunderSuitFlagStateByIndex(v.seq, v.index)
		if REWARD_STATE_TYPE.CAN_FETCH == flag_state then
			return true
		end
	end

	return false
end

--变强更新
function ThunderManaWGData:SetThunderManaStrengthen(select_type, value)
	local tip_type = select_type == 0 and MAINUI_TIP_TYPE.THUNDER_MANA_SHADY or MAINUI_TIP_TYPE.THUNDER_MANA_SUN 
	local open_fun_name = select_type == 0 and GuideModuleName.ShadyThunderView or GuideModuleName.SunThunderView

	if value == 1 then
		MainuiWGCtrl.Instance:InvateTip(tip_type, 1, function ()
			ViewManager.Instance:Open(open_fun_name)
            return true
        end)
	else
		MainuiWGCtrl.Instance:InvateTip(tip_type, 0)
	end
end