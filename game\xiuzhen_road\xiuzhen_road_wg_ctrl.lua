require("game/xiuzhen_road/xiuzhen_road_view")
require("game/xiuzhen_road/xiuzhen_road_view_cell")
require("game/xiuzhen_road/xiuzhen_road_wg_data")
require("game/xiuzhen_road/xiuzhen_reward_tips")
require("game/xiuzhen_road/xiuzhen_road_btn")
require("game/xiuzhen_road/xiuzhen_road_dalian_view")


XiuZhenRoadWGCtrl = XiuZhenRoadWGCtrl or BaseClass(BaseWGCtrl)
function XiuZhenRoadWGCtrl:__init()
	if XiuZhenRoadWGCtrl.Instance then
		ErrorLog("[XiuZhenRoadWGCtrl] Attemp to create a singleton twice !")
	end
	XiuZhenRoadWGCtrl.Instance = self
	self:RegisterAllProtocols()
	self.xiuzhen_road_data = XiuZhenRoadWGData.New()
	self.xiuzhen_road_view = XiuZhenRoadView.New(GuideModuleName.XiuZhenRoadView)
	self.xiuzhen_skill_view = XieZhenRoadSkillTips.New()
	self.reward_tips = XiuZhenRewardTips.New()
	self.da_lian_view = XiuZhenDaLianView.New()

	self.fun_open_event = BindTool.Bind(self.FunOpenEvent, self)
	FunOpen.Instance:NotifyFunOpen(self.fun_open_event)

	self.init_call_back = BindTool.Bind1(self.MainuiInitCallBack, self)

	self.act_change = BindTool.Bind(self.OnActivityChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)

	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.OnDayChange, self))
end

function XiuZhenRoadWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSBestNeckFetchReward)
	self:RegisterProtocol(SCXiuZhenZhiLuAllInfo, "OnSCXiuZhenZhiLuAllInfo")
	self:RegisterProtocol(SCXiuZhenZhiLuInfo, "OnSCXiuZhenZhiLuInfo")
	self:RegisterProtocol(SCXiuZhenZhiLuGiftPackInfo, "OnSCXiuZhenZhiLuGiftPackInfo")
	self:RegisterProtocol(SCXiuZhenZhiLuGiftInfo, "OnSCXiuZhenZhiLuGiftInfo")
end

function XiuZhenRoadWGCtrl:__delete()
	XiuZhenRoadWGCtrl.Instance = nil
	if self.xiuzhen_road_data ~= nil then
		self.xiuzhen_road_data:DeleteMe()
		self.xiuzhen_road_data = nil
	end
	if self.xiuzhen_road_view ~= nil then
		self.xiuzhen_road_view:DeleteMe()
		self.xiuzhen_road_view = nil
	end
	if self.xiuzhen_skill_view ~= nil then
		self.xiuzhen_skill_view:DeleteMe()
		self.xiuzhen_skill_view = nil
	end
	if self.reward_tips ~= nil then
		self.reward_tips:DeleteMe()
		self.reward_tips = nil
	end

	if self.da_lian_view ~= nil then
		self.da_lian_view:DeleteMe()
		self.da_lian_view = nil
	end

	if self.act_change then
        ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
        self.act_change = nil
    end

	FunOpen.Instance:UnNotifyFunOpen(self.fun_open_event)
end

function XiuZhenRoadWGCtrl:Open(tab_index, param_t)
	self.xiuzhen_road_view:Open()
end

function XiuZhenRoadWGCtrl:OpenSkillTip(data)
	self.xiuzhen_skill_view:SetSkillData(data)
end


function XiuZhenRoadWGCtrl:Flush()
	if self.xiuzhen_road_view:IsOpen() then
		self.xiuzhen_road_view:Flush()
	end
end

function XiuZhenRoadWGCtrl:CloseView()
	if self.xiuzhen_road_view:IsOpen() then
		self.xiuzhen_road_view:Close()
	end
end

-- 修真之路相关请求
function XiuZhenRoadWGCtrl:SendXiuZhenZhiLuReq(type,param_1,param_2,param_3)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSBestNeckFetchReward)
	send_protocol.type = type or 0
	send_protocol.param_1 = param_1 or 0
	send_protocol.param_2 = param_2 or 0
	send_protocol.param_3 = param_3 or 0
	send_protocol:EncodeAndSend()
end

--全部信息
function XiuZhenRoadWGCtrl:OnSCXiuZhenZhiLuAllInfo(protocol)
	self.xiuzhen_road_data:SetXiuZhenZhiLuAllInfo(protocol)
	self:Flush()
	self:CheckActIsOpen()
	MainuiWGCtrl.Instance:FlushTaskTopPanel()
    MainuiWGCtrl.Instance:FlushView(0, "mainui_xxym_tip")
	RemindManager.Instance:Fire(RemindName.XiuZhenRoad)
end

--单个信息
function XiuZhenRoadWGCtrl:OnSCXiuZhenZhiLuInfo(protocol)
	self.xiuzhen_road_data:SetXiuZhenZhiLuInfo(protocol)
	self:Flush()
	self:CheckActIsOpen()
	MainuiWGCtrl.Instance:FlushTaskTopPanel()
	MainuiWGCtrl.Instance:FlushView(0, "mainui_xxym_tip")
	RemindManager.Instance:Fire(RemindName.XiuZhenRoad)
end

--礼包信息
function XiuZhenRoadWGCtrl:OnSCXiuZhenZhiLuGiftPackInfo(protocol)
	self.xiuzhen_road_data:SetXiuZhenZhiLuGiftInfo(protocol)
	self:Flush()
	self:FlushXiuZhenRewardTips()
	MainuiWGCtrl.Instance:FlushTaskTopPanel()
	RemindManager.Instance:Fire(RemindName.XiuZhenRoad)
end

--礼包信息
function XiuZhenRoadWGCtrl:OnSCXiuZhenZhiLuGiftInfo(protocol)
	self.xiuzhen_road_data:SetSCXiuZhenZhiLuGiftInfo(protocol)
	self:Flush()
	self:FlushXiuZhenRewardTips()
	MainuiWGCtrl.Instance:FlushTaskTopPanel()
	RemindManager.Instance:Fire(RemindName.XiuZhenRoad)
end

function XiuZhenRoadWGCtrl:MainuiInitCallBack()
	if self.xiuzhen_road_data:XiuZhenRoadIsOpen() then
		local end_time = self.xiuzhen_road_data:GetActivityEndTime()
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.XIUZHEN_ROAD, ACTIVITY_STATUS.OPEN, end_time, nil, nil, RAND_ACTIVITY_OPEN_TYPE.RAND_ACTIVITY_OPEN_TYPE_NORMAL)
	else
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.XIUZHEN_ROAD, ACTIVITY_STATUS.CLOSE)
	end
end

function XiuZhenRoadWGCtrl:OnActivityChange(act_type)
	if act_type == ACTIVITY_TYPE.XIUZHEN_ROAD then
		MainuiWGCtrl.Instance:FlushTaskTopPanel()
	end
end

function XiuZhenRoadWGCtrl:OnDayChange()
	MainuiWGCtrl.Instance:FlushTaskTopPanel()
end

function XiuZhenRoadWGCtrl:CheckActIsOpen()
	MainuiWGCtrl.Instance:AddInitCallBack(ACTIVITY_TYPE.XIUZHEN_ROAD, self.init_call_back)
end

function XiuZhenRoadWGCtrl:OpenXiuZhenRewardTips()
	self.reward_tips:Open()
end

function XiuZhenRoadWGCtrl:CloseXiuZhenRewardTips()
	self.reward_tips:Close()
end

function XiuZhenRoadWGCtrl:FlushXiuZhenRewardTips()
	if self.reward_tips:IsOpen() then
		self.reward_tips:Flush()
	end
end

function XiuZhenRoadWGCtrl:FunOpenEvent(fun_name)
	if fun_name == "XiuZhenRoadView" then
		self:CheckActIsOpen()
		RemindManager.Instance:Fire(RemindName.XiuZhenRoad)
	end
end

function XiuZhenRoadWGCtrl:CheckNeedOpenDaLian()
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "xiuzhen_dalian_flag")
	local flag = PlayerPrefsUtil.GetInt(key)
	if flag == 1 then
		return false
	end

	local xiuzhen_road_is_open = XiuZhenRoadWGData.Instance:XiuZhenRoadIsOpen()
	if not xiuzhen_road_is_open then
		return false
	end

	-- ViewManager.Instance:OpenByQueue(self.da_lian_view)
	return true
end
