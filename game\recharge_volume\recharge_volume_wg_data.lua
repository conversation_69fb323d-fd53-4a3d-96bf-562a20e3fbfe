RechargeVolumeWGData = RechargeVolumeWGData or BaseClass()

function RechargeVolumeWGData:__init()
	if RechargeVolumeWGData.Instance ~= nil then
		Error<PERSON><PERSON>("[RechargeVolumeWGData] attempt to create singleton twice!")
		return
	end

	RechargeVolumeWGData.Instance = self

	self:InitParam()
	self:InitCfg()

    RemindManager.Instance:Register(RemindName.CumulateRechargeGift, BindTool.Bind(self.GetRemind, self))
	RemindManager.Instance:Register(RemindName.RechargeVolumeCanUse, BindTool.Bind(self.GetRechargeVolumeCanUseRemind, self))
end

function RechargeVolumeWGData:InitParam()
	self.round = 0
	self.recharge_num = 0
	self.fetch_flag_list = {}
	self.daily_open_flag = false

	self.rmb_buy_seq = 0
	self.task_finish_flag = {}
	self.task_condition_list = {}
end

function RechargeVolumeWGData:InitCfg()
	self.recharge_volume_other_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto").other[1]
	
	local cumulate_all_cfg = ConfigManager.Instance:GetAutoConfig("recharge_volume_show_auto")
	self.base_cfg = cumulate_all_cfg.other[1]
	self.cumulate_recharge_cfg = ListToMap(cumulate_all_cfg.cumulate_recharge, "round", "recharge_grade", "day")
	self.grade_decorate_cfg = ListToMap(cumulate_all_cfg.grade_client, "recharge_grade")
	self.get_way_cfg = cumulate_all_cfg.get_way
	self.improve_way_cfg = cumulate_all_cfg.improve_way

	self.rmb_cfg = ListToMap(cumulate_all_cfg.rmb, "seq")
	self.task_cfg = ListToMap(cumulate_all_cfg.task, "task_seq")
	self.exchange_cfg = ListToMap(cumulate_all_cfg.exchange, "exchange_seq")
end

function RechargeVolumeWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.CumulateRechargeGift)
    RemindManager.Instance:UnRegister(RemindName.RechargeVolumeCanUse)
	RechargeVolumeWGData.Instance = nil
end

function RechargeVolumeWGData:GetRechargeVolumeCanUseRemind()
	local can_use_remind = self:GetCanUseRemind()
	--财神送福屏蔽
	--local task_is_can_receive = self:GetIsHaveTaskCanReceive()

	-- local daily_open_flag = self:GetDailyOpenFlag()
    -- if can_use_remind == 1 or not daily_open_flag or task_is_can_receive then
	-- if can_use_remind == 1 or not daily_open_flag or task_is_can_receive then
    --     return 1
    -- end

	local red_flag = PrivilegeCollectionWGData.Instance:GetZZTQRedPoint() > 0

	if can_use_remind == 1 or red_flag then
		return 1
    end

	return 0
end

function RechargeVolumeWGData:GetCanUseRemind()
	local recharge_volume_num = RoleWGData.Instance:GetAttr("recharge_volume")
    local max_num = RechargeWGData.Instance:GetAllVirtualGoldMaxUseNum(VIRTUAL_GOLD_TYPE.RECHARGE_VOLUME)
	--local time_limit_num = RechargeWGData.Instance:GetVirtualGoldLimitTimeLimited(VIRTUAL_GOLD_TYPE.RECHARGE_VOLUME)
	local use_num = RechargeWGData.Instance:GetVirtualGoldUseNum(VIRTUAL_GOLD_TYPE.RECHARGE_VOLUME)
	local last_use_num = max_num - use_num		-- 剩下可使用的额度
	local min_exchange = 10

    if recharge_volume_num >= min_exchange and last_use_num >= min_exchange then
        return 1
    end

	return 0
end

function RechargeVolumeWGData:GetCanUseRemindBySeq(seq)
	local recharge_volume_num = RoleWGData.Instance:GetAttr("recharge_volume")
    local max_num = RechargeWGData.Instance:GetAllVirtualGoldMaxUseNum(VIRTUAL_GOLD_TYPE.RECHARGE_VOLUME)
	--local time_limit_num = RechargeWGData.Instance:GetVirtualGoldLimitTimeLimited(VIRTUAL_GOLD_TYPE.RECHARGE_VOLUME)
	local use_num = RechargeWGData.Instance:GetVirtualGoldUseNum(VIRTUAL_GOLD_TYPE.RECHARGE_VOLUME)
	local last_use_num = max_num - use_num		-- 剩下可使用的额度
	local cfg = self:GetExchangeCfgBySeq(seq)
	local cur_exchange = cfg.consume_num

    if recharge_volume_num >= cur_exchange and last_use_num >= cur_exchange then
        return true
    end

	return false
end

--是否有任务完成可以领取
function RechargeVolumeWGData:GetIsHaveTaskCanReceive()
	local cfg = self:GetAllTaskCfg()
	for k, v in pairs(cfg) do
		local progress = self:GetTaskProgressBySeq(v.task_seq)
		local is_finish = self:GetTaskFinishFlag(v.task_seq)
		if progress >= 1 and is_finish ~= 1 then
			return true
		end
	end
	--local progress = self:GetCurTaskProgress()
	return false
end

--true表示打开过不需要显示红点
function RechargeVolumeWGData:GetDailyOpenFlag()
	-- 没有可购买的不再提示红点
	-- local rmb_cfg = RechargeVolumeWGData.Instance:GetRmbCfg()
	-- local no_can_buy = true
	-- for i, v in ipairs(rmb_cfg) do
	-- 	local buy_state = RechargeVolumeWGData.Instance:GetRmbBuyStateBySeq(i)
	-- 	if buy_state == GODDESS_BLESSING_BUY_TYPE.CAN_BUY then
	-- 		no_can_buy = false
	-- 		break
	-- 	end
	-- end

	-- return no_can_buy -- or self.daily_open_flag
	return true
end

-- function RechargeVolumeWGData:SetDailyOpenFlag(bool)
-- 	self.daily_open_flag = bool
-- 	RemindManager.Instance:Fire(RemindName.RechargeVolumeCanUse)
-- 	RemindManager.Instance:Fire(RemindName.PrivilegeCollection_NSTQ)
-- end

function RechargeVolumeWGData:GetJumpPath()
	return self.recharge_volume_other_cfg.virtual_recharge_colume_open_panel
end

--获取特权配置
function RechargeVolumeWGData:GetRmbCfg()
	return self.rmb_cfg
end

--获取特权配置数量
function RechargeVolumeWGData:GetRmbCfgNum()
	return #self.rmb_cfg
end

--获取指定特权配置
function RechargeVolumeWGData:GetRmbCfgBySeq(seq)
	return self.rmb_cfg[seq]
end

--获取任务配置通过Seq
function RechargeVolumeWGData:GetTaskCfgBySeq(seq)
	return self.task_cfg[seq]
end

--获取所有任务配置
function RechargeVolumeWGData:GetAllTaskCfg()
	return self.task_cfg
end

function RechargeVolumeWGData:GetTaskCfgNum()
	return #self.task_cfg + 1
end

--获取灵玉兑换配置
function RechargeVolumeWGData:GetExchangeCfg()
	return self.exchange_cfg
end

--获取灵玉兑换配置
function RechargeVolumeWGData:GetExchangeCfgBySeq(seq)
	return self.exchange_cfg[seq]
end

function RechargeVolumeWGData:GetEveryDayLimit()
	return self.base_cfg.limit
end

function RechargeVolumeWGData:GetOneBossQuotaLimit()
	return self.base_cfg.quota_each
end

function RechargeVolumeWGData:GetOneBossQuotaLimitOpenDay()
	return self.base_cfg.add_quota_open_day
end

function RechargeVolumeWGData:GetGiftRewardCfgByRoundDay(round)
	return self.cumulate_recharge_cfg[round] or {}
end

function RechargeVolumeWGData:GetDecorateCfg(grade)
	grade = grade or 0
	return self.grade_decorate_cfg[grade] or {}
end

function RechargeVolumeWGData:SetAllCumulateGiftInfo(protocol)
	self.info_round			= protocol.info_round
	self.info_day 			= protocol.info_day
	self.recharge_num 		= protocol.recharge_num
	self.fetch_flag_list 	= protocol.fetch_flag_list
end

function RechargeVolumeWGData:SetCumulateData(protocol)
	local old_rmb_buy_seq = self.rmb_buy_seq
	self.rmb_buy_seq = protocol.rmb_buy_seq
	self.task_finish_flag = protocol.task_finish_flag
	self.task_condition_list = protocol.task_condition_list
	if old_rmb_buy_seq ~= self.rmb_buy_seq then
		RechargeVolumeWGCtrl.Instance:SetGoddessBlessingViewJumpFlag()
		PrivilegeCollectionWGCtrl.Instance:SetViewJumpFlag()
	end
end

function RechargeVolumeWGData:GetRmbBuySeq()
	return self.rmb_buy_seq
end

function RechargeVolumeWGData:GetDefaultSelectIndex()
	local num = self:GetRmbCfgNum()
	local default_index = 1
	if self.rmb_buy_seq < num then
		default_index = self.rmb_buy_seq + 1
	else
		default_index = num
	end

	return default_index
end

function RechargeVolumeWGData:GetRmbBuyStateBySeq(seq)
	local state = 0
	if seq <= self.rmb_buy_seq then
		state = GODDESS_BLESSING_BUY_TYPE.HAVE_BUY
	elseif seq == self.rmb_buy_seq + 1 then
		state = GODDESS_BLESSING_BUY_TYPE.CAN_BUY
	elseif seq > self.rmb_buy_seq + 1 then
		state = GODDESS_BLESSING_BUY_TYPE.CAN_NOT_BUY
	end

	return state
end

--是否购买了所有女神特权
function RechargeVolumeWGData:GetIsBuyAllGoddessBlessing()
	local num = self:GetRmbCfgNum()
	return self.rmb_buy_seq >= num
end

function RechargeVolumeWGData:GetTaskFinishFlag(seq)
	return self.task_finish_flag[seq] or 0
end

function RechargeVolumeWGData:GetCurTaskSeq()
	local num = self:GetTaskCfgNum()
	local seq = -1
	for i = 0, num - 1 do
		if self.task_finish_flag[i] == 0 then
			seq = i
			break
		end
	end

	return seq
end

--获取当前任务的完成进度
function RechargeVolumeWGData:GetCurTaskProgress()
	local cur_seq = self:GetCurTaskSeq()
	local progress = 0
	if cur_seq >= 0 then
		local cur_cfg = self:GetTaskCfgBySeq(cur_seq)
		local cur_value = self:GetTaskConditionList(cur_seq)
		progress = cur_value / cur_cfg.task_condition
	end

	return progress
end

--获取任务的完成进度通过seq
function RechargeVolumeWGData:GetTaskProgressBySeq(seq)
	local progress = 0
	if seq >= 0 then
		local cur_cfg = self:GetTaskCfgBySeq(seq)
		local cur_value = self:GetTaskConditionList(seq)
		progress = cur_value / cur_cfg.task_condition
	end

	return progress
end

--获取当前任务配置
function RechargeVolumeWGData:GetCurTaskCfg()
	local num = self:GetTaskCfgNum()
	local cur_cfg = {}
	for i = 0, num - 1 do
		if self.task_finish_flag[i] == 0 then
			cur_cfg = self:GetTaskCfgBySeq(i)
			return cur_cfg
		end
	end

	return cur_cfg
end

--获取任务额度
function RechargeVolumeWGData:GetTaskAddNumDataList()
	local num = self:GetTaskCfgNum()
	local data_list = {}
	local base_num = RechargeWGData.Instance:GetVirtualGoldMaxUseNum(VIRTUAL_GOLD_TYPE.RECHARGE_VOLUME)
	local cur_add_num = base_num
	for i = 1, num do
		local cfg = self:GetTaskCfgBySeq(i - 1)
		cur_add_num = cur_add_num + cfg.add_num
		data_list[i] = cur_add_num
	end

	return data_list
end

--获取已完成领取任务的提升额度值
function RechargeVolumeWGData:GetFinishTaskLimitValue()
	local num = self:GetTaskCfgNum()
	local base_num = RechargeWGData.Instance:GetVirtualGoldMaxUseNum(VIRTUAL_GOLD_TYPE.RECHARGE_VOLUME)
	local value = base_num
	for i = 0, num - 1 do
		if self.task_finish_flag[i] == 1 then
			local cfg = self:GetTaskCfgBySeq(i)
			value = value + cfg.add_num
		end
	end

	return value
end

function RechargeVolumeWGData:GetIsFinishAllTask()
	local num = self:GetTaskCfgNum()
	for i = 0, num - 1 do
		if self.task_finish_flag[i] == 0 then
			return false
		end
	end

	return true
end

function RechargeVolumeWGData:GetFinishTaskProgress()
	local num = self:GetTaskCfgNum()
	local finish_num = 0
	for i = 0, num - 1 do
		if self.task_finish_flag[i] == 1 then
			finish_num = finish_num + 1
		end
	end

	local progress = math.floor(finish_num / (num / 4))

	return progress
end

function RechargeVolumeWGData:GetTaskConditionList(seq)
	return self.task_condition_list[seq] or 0
end

function RechargeVolumeWGData:GetCurGiftRewardDay()
	return self.info_day or 1
end

function RechargeVolumeWGData:GetCurGiftRechargeNum()
	return self.recharge_num or 0
end

function RechargeVolumeWGData:GetGradeRewardStatusByDay(grade, day)
	if not day then
		day = self:GetCurGiftRewardDay()
	end

	local is_get_reawrd = false
	if self.fetch_flag_list[day] and self.fetch_flag_list[day][grade] then
		is_get_reawrd = self.fetch_flag_list[day][grade] == 1
	end

	return is_get_reawrd
end

function RechargeVolumeWGData:GetTodayRewardRemind()
	local is_red = false
    local data_list = self:GetGiftRewardListByRound()
	if not IsEmptyTable(data_list) then
		local recharge_num, reharget_grade
		local is_get_reward = false
		for key, value in pairs(data_list) do
			recharge_num, reharget_grade = value[1].recharge_num, value[1].recharge_grade
			if recharge_num and reharget_grade then
				if self.recharge_num >= recharge_num then
					is_get_reward = self:GetGradeRewardStatusByDay(reharget_grade)
					if not is_get_reward then
						is_red = true
						break
					end
				end
			end
		end
	end
	return is_red
end

function RechargeVolumeWGData:GetRemind()
	if not FunOpen.Instance:GetFunIsOpened(GuideModuleName.RechargeVolumeRewardView) then
		return 0
	end

    local ret_num = 0
    if ret_num <= 0 then
        ret_num = self:GetTodayRewardRemind() and 1 or 0
    end

	return ret_num
end

function RechargeVolumeWGData:GetGiftRewardListByRound()
	return self:GetGiftRewardCfgByRoundDay(self.info_round)
end

-- 获取路径
function RechargeVolumeWGData:GetRechargeGetWayList()
	return self.get_way_cfg
end

-- 提额路径
function RechargeVolumeWGData:GetRechargeProduceList()
	return self.improve_way_cfg
end
