WaWillGetNew = WaWillGetNew or BaseClass(SafeBaseView)

function WaWillGetNew:__init()
    self.view_layer = UiLayer.Pop
    self.view_style = ViewStyle.Half
    self.can_do_fade = false
    self:SetMaskBg(true, true, nil, BindTool.Bind1(self.DoCloseTween, self))
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    local bundle_name = "uis/view/marry_ui_prefab"
    self:AddViewResource(0, bundle_name, "layout_will_get_new_view")
    self.info = {}
end

function WaWillGetNew:ReleaseCallBack()
    self.delay_to_show_model = nil
    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end

    if CountDownManager.Instance:HasCountDown("willgetbaby_end_countdown") then
        CountDownManager.Instance:RemoveCountDown("willgetbaby_end_countdown")
    end

    self:CancelTween()
    self.info = nil
end

function WaWillGetNew:ShowIndexCallBack()
    self.delay_to_show_model = true
end

function WaWillGetNew:LoadCallBack()
    self.model_display = RoleModel.New()
    local display_data = {
        parent_node = self.node_list["ph_display"],
        camera_type = MODEL_CAMERA_TYPE.BASE,
        -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
        rt_scale_type = ModelRTSCaleType.M,
        can_drag = true,
    }
    
    self.model_display:SetRenderTexUI3DModel(display_data)
    -- self.model_display:SetUI3DModel(self.node_list["ph_display"].transform,
    --     self.node_list["ph_display"].event_trigger_listener,
    --     1, false, MODEL_CAMERA_TYPE.BASE)

    if self.node_list["btn_save"] then
        XUI.AddClickEventListener(self.node_list["btn_save"], BindTool.Bind(self.DoCloseTween, self))
    end

    if self.node_list["btn_close_window"] then
		XUI.AddClickEventListener(self.node_list["btn_close_window"], BindTool.Bind(self.DoCloseTween, self))
	end

    self.info = MarryWGData.Instance:GetFirstBabyInfo()
end

function WaWillGetNew:OnFlush()
    if not self.info then return end
    if self.delay_to_show_model then
        self:DoOpenTween()
        return
    end

    self.model_display:RemoveAllModel()
    self.node_list["ph_display"]:SetActive(true)
    local get_baby_timestamp, get_baby_flag = MarryWGData.Instance:GetBabyLingQuInfo()
    local bundle, asset = ResPath.GetHaiZiModel(self.info.appe_image_id)
    self.model_display:SetMainAsset(bundle, asset, function()
        self.model_display:PlaySoulAction()
    end)
    self.node_list.obj_name.text.text = self.info.baby_name

    if get_baby_timestamp - TimeWGCtrl.Instance:GetServerTime() > 0 and get_baby_flag == 0 then
        self:UpdataEndTime(TimeWGCtrl.Instance:GetServerTime(), get_baby_timestamp)
        if CountDownManager.Instance:HasCountDown("willgetbaby_end_countdown") then
            CountDownManager.Instance:RemoveCountDown("willgetbaby_end_countdown")
        end
        CountDownManager.Instance:AddCountDown("willgetbaby_end_countdown", BindTool.Bind1(self.UpdataEndTime, self),
            BindTool.Bind1(self.EndTimeCallBack, self), get_baby_timestamp, nil, 1)
    end
end

function WaWillGetNew:UpdataEndTime(elapse_time, total_time)
    local time = total_time - elapse_time
    local format_time = TimeUtil.FormatSecondDHM4(time)
    self.node_list["get_baby_hint"].text.text = string.format(Language.Marry.GetBabyTimeHintDes1, format_time)
end

function WaWillGetNew:EndTimeCallBack()
    local get_baby_timestamp, get_baby_flag = MarryWGData.Instance:GetBabyLingQuInfo()
    if get_baby_timestamp > 0 and get_baby_flag == 0 then --时间到未领取
        self.node_list["get_baby_hint"].text.text = ""
    else
        self.node_list["get_baby_hint"].text.text = ""
    end
end

function WaWillGetNew:DoOpenTween()
	if self.model_display then
		self.model_display:RemoveAllModel()
	end

	self.delay_to_show_model = false
	self.node_list["ph_display"]:SetActive(false)
    self:CancelTween()
	local show_tweener = DG.Tweening.DOTween.Sequence()
	local panel = self.node_list.view_panel
	panel.transform.localScale = Vector3(0.6, 0.8, 0.8)

	show_tweener:Append(panel.canvas_group:DoAlpha(0.6, 1, 0.3))
	show_tweener:Join(panel.rect:DOScale(Vector3(1.2, 1.2, 1.2), 0.25))
	show_tweener:Append(panel.rect:DOScale(Vector3(1, 1, 1), 0.05))
	show_tweener:SetEase(DG.Tweening.Ease.InOutQuad)
	show_tweener:OnComplete(
		function()
			self:Flush()
		end
	)

    self.enter_play_tween = show_tweener
end

function WaWillGetNew:CancelTween()
    if self.enter_play_tween then
        self.enter_play_tween:Kill()
        self.enter_play_tween = nil
    end
end

function WaWillGetNew:DoCloseTween()
    self.info = nil
    -- local show_tweener = DG.Tweening.DOTween.Sequence()
    -- local panel = self.node_list.view_panel
    -- panel.transform.localScale = Vector3(1, 1, 1)

    -- show_tweener:Append(panel.rect:DOScale(Vector3(1.2, 1.2, 1.2), 0.25))
    -- show_tweener:Join(panel.canvas_group:DoAlpha(1, 0.1, 0.2))
    -- show_tweener:Append(panel.rect:DOScale(Vector3(1.1, 1.1, 1.1), 0.05))
    -- show_tweener:SetEase(DG.Tweening.Ease.InOutQuad)
    -- show_tweener:OnComplete(
    --     function()
    --         self:Close()
    --     end
    -- )
    self:Close()
end
