BaseHeadCell = BaseHeadCell or BaseClass(BaseRender)

function BaseHeadCell:__init(instance)
    if nil == self.root_node then
		local bundle, asset = ResPath.GetWidgets("BaseHeadCell")

        local u3dobj = U3DObject(ResPoolMgr:TryGetGameObject(bundle, asset))
        if self.instance_parent then
            u3dobj.transform:SetParent(self.instance_parent)
        end

		self:SetInstance(u3dobj)
		self.is_use_objpool = true
	end

	if instance then
		self:SetInstanceParent(instance)
	end

    self.root_node = self.node_list["root_cell"]
    self.frame = self.node_list["img_frame"]
    self.spine_frame = self.node_list["spine_frame"]
    self.default_icon = self.node_list["default_head_icon"]
    self.custom_icon = self.node_list["custom_head_icon"]
    self.frame_adorn = self.node_list["head_adorn_pos"]
    self.menu_btn = self.node_list["menu_btn"]
    self.head_btn = self.node_list.head_btn

    self.is_bg_click = false
    self.head_btn.button:AddClickListener(BindTool.Bind(self.OpenBigView, self))
    self.head_btn.image.raycastTarget = self.is_bg_click
    
    self:SetNeedMenuBtn(false)
    self.menu_btn.button:AddClickListener(BindTool.Bind(self.OpenMenu, self))
    self:FlushMenuBtn()

    self.default_bg_bundle, self.default_bg_asset = ResPath.GetCommonImages("a3_ty_tx")
    self.cur_bg_bundle, self.cur_bg_asset = self.default_bg_bundle, self.default_bg_asset

    if self.is_use_objpool then
		self:Reset()
	end
    self.old_head_adorn_resid = nil

    self.save_gray = false
    self.last_role_id = nil
    self.is_need_check_same = false
end

function BaseHeadCell:__delete()
    self:Reset(true)
    self:SetGray(false)
    self:SetFrameScale(0.8)
    self:SetSpineFrameScale(1)

    if self.role_head_cell then
        self.role_head_cell:DeleteMe()
        self.role_head_cell = nil
    end

    self.root_node = nil
    self.frame = nil
    self.spine_frame = nil
    self.default_icon = nil
    self.custom_icon = nil
    self.frame_adorn = nil
    self.frame_adorn = nil
    self.menu_btn = nil
    self.show_head_adorn = nil
    self.old_head_adorn_resid = nil
    self.save_gray = nil
    self.head_btn = nil
    self.is_bg_click = false
    self.last_role_id = nil
    self.async_loader = nil
    self.is_need_check_same = false
    self:SetNeedMenuBtn(false)

    if self.is_use_objpool and not self:IsNil() then
		ResPoolMgr:Release(self.view.gameObject, ResPoolReleasePolicy.NotDestroy)
	end
end

function BaseHeadCell:Reset(is_delete)
    if not is_delete then
        self:SetImgBg(true)
        self:SetGray(false)
        self:SetBgActive(true)
        self.is_bg_click = false
        self:SetNeedMenuBtn(false)
        if self.frame then
            self.frame:SetActive(false)
            self:SetFrameScale(0.8)
        end

        if self.spine_frame then
            self.spine_frame:SetActive(false)
            self:SetSpineFrameScale(1)
        end
    else
        if self.default_bg_bundle ~= self.cur_bg_bundle or self.default_bg_asset ~= self.cur_bg_asset then
            self:ChangeBg(self.default_bg_bundle, self.default_bg_asset, true)
        end
    end
end


function BaseHeadCell:SetIsBgClick(is_click)
    self.is_bg_click = is_click
    if self.head_btn then
        self.head_btn.image.raycastTarget = self.is_bg_click
    end
    if is_click then
        self:SetNeedMenuBtn(not is_click)
    end
end

function BaseHeadCell:SetNeedMenuBtn(need)
    self.need_menu_btn = need
    self:FlushMenuBtn()
end

function BaseHeadCell:SetIsNeedCheckSame(value)
    self.is_need_check_same = value
end

function BaseHeadCell:FlushMenuBtn()
    if self.menu_btn and not IsNil(self.menu_btn.gameObject) then
        self.menu_btn:SetActive(self.need_menu_btn)
    end
end

function BaseHeadCell:OpenBigView()
    if self.data and self.data.role_id then
        local data = {}
        data.role_id = self.data.role_id
        data.sex = self.data.sex
        data.prof = self.data.prof
        data.plat_type = 0
        RoleWGCtrl.Instance:OpenHeadBigView(data)
    end
end

-- 目前需要role_id, sex, fashion_photoframe
function BaseHeadCell:OnFlush()
    if self.is_need_check_same then
        if self.last_role_id == nil and self.data ~= nil then
            self.last_role_id = self.data.role_id
        else
            if self.last_role_id ~= nil and self.data ~= nil then
                if self.last_role_id == self.data.role_id then
                    return
                else
                    self.last_role_id = self.data.role_id
                end
            end
        end
    end

    if self.data.role_id then
        if self.data.is_show_main then  -- 是否需要显示主角自定义头像
            local cutom_head_info = RoleWGData.Instance:GetCustomHeadInfo()
            if cutom_head_info then
                XUI.UpdateMainRoleHead(cutom_head_info.avatar_key_big,
                                        cutom_head_info.avatar_key_small,
                                        self.default_icon,
                                        self.custom_icon,
                                        self.data.role_id,
                                        self.data.sex, self.data.prof,
                                        self.save_gray, false, true)
            end
        else
            XUI.UpdateRoleHead(self.default_icon, self.custom_icon, self.data.role_id, self.data.sex, self.data.prof, self.save_gray, nil , true)
        end
    else
        self.default_icon:SetActive(false)
        self.custom_icon:SetActive(false)
    end

    RectTransform.SetSizeDeltaXY(self.default_icon.rect, 92, 92)

    local fashion_photoframe = self.data.fashion_photoframe or 0

    -- 预防id未转换处理
    if fashion_photoframe > GameEnum.FASHION_CRISIS_NUM then
        local image_cfg = NewAppearanceWGData.Instance:GetFashionCfgByProtocolIndex(SHIZHUANG_TYPE.PHOTOFRAME, fashion_photoframe)
        fashion_photoframe = image_cfg and image_cfg.resouce or 0
    end

    if self.img_frame_state then
        self.frame_adorn:SetActive(fashion_photoframe > 0)
        if fashion_photoframe > 0 then
            local spine_frame_cfg = NewAppearanceWGData.Instance:GetSpineTXFrameCfgByIndex(fashion_photoframe)
            if not spine_frame_cfg then
                self.frame:SetActive(true)
                self.spine_frame:SetActive(false)
                self.frame_adorn:SetActive(true)
                local b, a = ResPath.GetPhotoFrame(fashion_photoframe)
                self.frame.image:LoadSprite(b, a, function()
                    self.frame.image:SetNativeSize()
                end)
            else
                self.frame:SetActive(false)
                self.spine_frame:SetActive(true)
                self.frame_adorn:SetActive(true)
                if not self.async_loader then
                    local async_loader = AllocAsyncLoader(self, "spine_tx_frame" .. fashion_photoframe)
                    async_loader:SetIsUseObjPool(true)
                    async_loader:SetParent(self.spine_frame.transform)

                    self.async_loader = async_loader
                end

                local spine_frame_bundle = spine_frame_cfg.touxiang_farme_bundle
                local spine_frame_asset = spine_frame_cfg.touxiang_farme_asset
                self.async_loader:Load(spine_frame_bundle, spine_frame_asset)

                local pos_x, pos_y = 0, 0
                if spine_frame_cfg.touxiang_farme_pos and spine_frame_cfg.touxiang_farme_pos ~= "" then
                    local pos_list = string.split(spine_frame_cfg.touxiang_farme_pos, "|")
                    pos_x = tonumber(pos_list[1]) or pos_x
                    pos_y = tonumber(pos_list[2]) or pos_y
                end

	            RectTransform.SetAnchoredPositionXY(self.spine_frame.rect, pos_x, pos_y)
            end
        else
            self.frame:SetActive(false)
            self.spine_frame:SetActive(false)
            self.frame_adorn:SetActive(false)
        end
    else
        self.frame:SetActive(false)
        self.spine_frame:SetActive(false)
        self.frame_adorn:SetActive(false)
    end

    local index = AvatarManager.Instance:GetAvatarKey(self.data.role_id, true)

    if index <= GameEnum.CUSTOM_HEAD_ICON then --系统头像
        self:SetIsBgClick(false)
    end

    if self.data.set_bg_status ~= nil then
        self:SetBgActive(self.data.set_bg_status)
    end
end

function BaseHeadCell:SetImgBg(state)
    self.img_frame_state = state
    -- if self.data and self.data.fashion_photoframe then
    --     self.frame_adorn:SetActive(state and self.data.fashion_photoframe > 0)
    -- end
end

function BaseHeadCell:SetGray(is_gray)
    if self.frame and not IsNil(self.frame.gameObject) then
        XUI.SetGraphicGrey(self.frame, is_gray)
    end

    if self.spine_frame and not IsNil(self.spine_frame.gameObject) then
        XUI.SetGraphicGrey(self.spine_frame, is_gray)
    end

    if self.default_icon and not IsNil(self.default_icon.gameObject) then
        XUI.SetGraphicGrey(self.default_icon, is_gray)
    end

    if self.custom_icon and not IsNil(self.custom_icon.gameObject) then
        XUI.SetGraphicGrey(self.custom_icon, is_gray)
    end

    if self.frame_adorn and not IsNil(self.frame_adorn.gameObject) then
        XUI.SetGraphicGrey(self.frame_adorn, is_gray)
    end

    self.save_gray = is_gray
end

function BaseHeadCell:SetFrameScale(scale)
    if self.frame and not IsNil(self.frame.gameObject) then
        self.frame.rect.localScale = Vector3(scale, scale, scale)
    end
end

function BaseHeadCell:SetSpineFrameScale(scale)
    if self.spine_frame and not IsNil(self.spine_frame.gameObject) then
        self.spine_frame.rect.localScale = Vector3(scale, scale, scale)
    end
end

function BaseHeadCell:SetHeadCellScale(scale)
    if self.root_node and not IsNil(self.root_node.gameObject) then
        self.root_node.gameObject.transform:SetLocalScale(scale, scale, scale)
    end
end

function BaseHeadCell:SetBgActive(flag)
    self.node_list["bg"]:SetActive(flag)
end

function BaseHeadCell:ChangeBg(bundle, asset, need_set_nativesize)
    self.cur_bg_bundle = bundle
    self.cur_bg_asset = asset
    self.node_list["bg"].image:LoadSprite(bundle, asset, function ()
        if need_set_nativesize then
            self.node_list["bg"].image:SetNativeSize()
        end
    end)
end

function BaseHeadCell:SetData(...)
    BaseRender.SetData(self, ...)
    self:FlushRoleHeadInfo()
end

function BaseHeadCell:CreateRoleHeadCell()
    self.role_head_cell = RoleHeadCell.New(false)
    self:FlushRoleHeadInfo()
end

function BaseHeadCell:FlushRoleHeadInfo()
    if self.role_head_cell and self.data then
        local role_info = {
            role_id = self.data.role_id,
            role_name = self.data.role_name,
            prof = self.data.prof,
            sex = self.data.sex,
            is_online = self.data.is_online,
            team_index = self.data.team_index,
            team_type = self.data.team_type,
            plat_type = self.data.plat_type,
            plat_name = self.data.plat_name,
            server_id = self.data.server_id,
        }
        self.role_head_cell:SetRoleInfo(role_info)
    end
end

function BaseHeadCell:OpenMenu(node, vec2, pos_offset, alpha_type)
    if not self.data then
        return
    end
    if self.role_head_cell == nil then
        self:CreateRoleHeadCell()
    end
    if vec2 == nil and node == nil then
        local worldPos = self.menu_btn.transform.position
        local screenPos = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, worldPos)
        vec2 = Vector2(screenPos.x - UnityEngine.Screen.width / 2, screenPos.y - UnityEngine.Screen.height / 2)
    end

    self.role_head_cell:OpenMenu(node, vec2, pos_offset, alpha_type)
end

