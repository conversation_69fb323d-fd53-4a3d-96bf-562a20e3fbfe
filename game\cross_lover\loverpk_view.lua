LoverPkView = LoverPkView or BaseClass(SafeBaseView)
function LoverPkView:__init()
    self:SetMaskBg()
    self.view_style = ViewStyle.Full
    self.default_index = TabIndex.lover_pk_qcdj
    self.is_safe_area_adapter = true

    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_common_panel")
    self:AddViewResource(TabIndex.lover_pk_qcdj, "uis/view/cross_lover_prefab", "layout_loverpk_qcdj")
    self:AddViewResource(TabIndex.lover_pk_fszz, "uis/view/cross_lover_prefab", "layout_loverpk_fszz")
    self:AddViewResource(0, "uis/view/cross_lover_prefab", "layout_loverpk_top_panel")
    self:AddViewResource(0, "uis/view/cross_lover_prefab", "VerticalTabbar")
    self.remind_tab = {
        { RemindName.LoverPkQCDJ },
        { RemindName.LoverPkFSJL },
    }
end

function LoverPkView:ReleaseCallBack()
    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

    if self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end

    self:ReleaseFSZZCallBack()
    self:ReleaseQCDJCallBack()
end

function LoverPkView:LoadCallBack()
    self.tabbar = Tabbar.New(self.node_list)
    local view_bundle = "uis/view/cross_lover_prefab"
    self.tabbar:Init(Language.LoverPK.TabGrop, nil, view_bundle, nil, self.remind_tab)
    self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))

    local bundle, asset = ResPath.GetRawImagesPNG("a2_xlpk_jm_dbjtu")
    if self.node_list.RawImage_tongyong then
        self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
            self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
        end)
    end
end

function LoverPkView:CreatMoneyBar()
    if not self.money_bar then
        self.money_bar = MoneyBar.New()
        local bundle, asset = ResPath.GetWidgets("MoneyBar")
        local show_params = {
            show_gold = true,
            show_bind_gold = true,
            show_coin = true,
            show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
        self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
    end
end

function LoverPkView:LoadIndexCallBack(index)
    if index == TabIndex.lover_pk_qcdj then
        self:LoadQCDJCallBack()
    elseif index == TabIndex.lover_pk_fszz then
        self:LoadFSZZCallBack()
    end
end

function LoverPkView:ShowIndexCallBack(index)
    if index == TabIndex.lover_pk_qcdj then
        LoverPkWGCtrl.Instance:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.QUERY_RANK)
        LoverPkWGCtrl.Instance:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.COUPLE_INFO)
    elseif index == TabIndex.lover_pk_fszz then
        LoverPkWGCtrl.Instance:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.REQ_LAST_WINNER)
    end

    local title = Language.LoverPK.TabGrop[index / 10]
    if self.node_list["title_view_name"] then
        self.node_list["title_view_name"].text.text = title
    end
end

function LoverPkView:OnFlush(param_t, index)
    for k, v in pairs(param_t) do
        if "all" == k then
            if index == TabIndex.lover_pk_qcdj then
                self:FlushQCDJCallBack()
            elseif index == TabIndex.lover_pk_fszz then
                self:FlushFSZZCallBack()
            end
        end
    end
end
