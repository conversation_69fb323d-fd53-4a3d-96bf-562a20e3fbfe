require("game/diy_draw/diy_draw_wg_data")
require("game/diy_draw/diy_draw_one_view")
require("game/diy_draw/diy_draw_one_select")
require("game/diy_draw/diy_draw_one_gailv")
require("game/diy_draw/diy_draw_one_reward")
require("game/diy_draw/diy_draw_two_view")
require("game/diy_draw/diy_draw_two_gailv")
require("game/diy_draw/diy_draw_two_reward")
require("game/diy_draw/diy_draw_two_select")

DIYDrawWGCtrl = DIYDrawWGCtrl or BaseClass(BaseWGCtrl)

function DIYDrawWGCtrl:__init()
    if DIYDrawWGCtrl.Instance ~= nil then
		print_error("[DIYDrawWGCtrl] attempt to create singleton twice!")
		return
	end

    DIYDrawWGCtrl.Instance = self
    self.data = DIYDrawWGData.New()
    self.diy1_view = DIYDrawOneView.New(GuideModuleName.DIYDrawOneView)
    self.diy1_select_view = DIYDrawOneSelectView.New()
    self.diy1_gailv_view = DIYOneProbabilityView.New()
    self.diy_draw_reward = DIYDrawReward.New()
    self.diy2_view = DIYDrawTwoView.New(GuideModuleName.DIYDrawTwoView)
    self.diy2_gailv_view = DIYTwoProbabilityView.New()
    self.diy2_draw_reward = DIYDrawTwoReward.New()
    self.diy2_select_view = DIYDrawTwoSelectView.New()

    self:RegisterAllProtocals()
    self:RegisterAllEvents()
end

function DIYDrawWGCtrl:__delete()
    self:UnRegisterAllEvents()
    DIYDrawWGCtrl.Instance = nil

    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

    if self.diy1_view then
        self.diy1_view:DeleteMe()
        self.diy1_view = nil
    end

    if self.diy1_select_view then
        self.diy1_select_view:DeleteMe()
        self.diy1_select_view = nil
    end

    if self.diy1_gailv_view then
        self.diy1_gailv_view:DeleteMe()
        self.diy1_gailv_view = nil
    end
    
    if self.diy_draw_reward then
        self.diy_draw_reward:DeleteMe()
        self.diy_draw_reward = nil
    end

    if self.diy2_view then
        self.diy2_view:DeleteMe()
        self.diy2_view = nil
    end

    if self.diy2_gailv_view then
        self.diy2_gailv_view:DeleteMe()
        self.diy2_gailv_view = nil
    end

    if self.diy2_draw_reward then
        self.diy2_draw_reward:DeleteMe()
        self.diy2_draw_reward = nil
    end

    if self.diy2_select_view then
        self.diy2_select_view:DeleteMe()
        self.diy2_select_view = nil
    end

    if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end
end

function DIYDrawWGCtrl:RegisterAllProtocals()
    self:RegisterProtocol(SCOADiyDraw1Info, "OnSCOADiyDraw1Info") --DIY1抽奖信息
    self:RegisterProtocol(SCOADiyDraw2Info, "OnSCOADiyDraw2Info") --DIY2抽奖信息
    self:RegisterProtocol(SCOADiyDraw1Result, "OnSCOADiyDraw1Result") --DIY1抽奖结果
    self:RegisterProtocol(SCOADiyDraw2Result, "OnSCOADiyDraw2Result") --DIY2抽奖结果
    self:RegisterProtocol(SCOADiyDraw1Record, "OnSCOADiyDraw1Record") --DIY1抽奖日志
    self:RegisterProtocol(SCOADiyDraw1RecordAdd, "OnSCOADiyDraw1RecordAdd") --DIY1抽奖日志更新
end

function DIYDrawWGCtrl:SendDIYDrawReq(act_type, opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = act_type
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

--DIY1抽奖信息
function DIYDrawWGCtrl:OnSCOADiyDraw1Info(protocol)
    --print_error("DIY1抽奖信息", protocol)
    self.data:SetDIY1DrawAllInfo(protocol)
    if self.diy1_view:IsOpen() then
        self.diy1_view:Flush()
    end
end

--DIY2抽奖信息
function DIYDrawWGCtrl:OnSCOADiyDraw2Info(protocol)
    --print_error("DIY2抽奖信息", protocol)
    self.data:SetDIY2DrawAllInfo(protocol)
    if self.diy2_view:IsOpen() then
        self.diy2_view:Flush()
    end
end

--DIY1抽奖结果
function DIYDrawWGCtrl:OnSCOADiyDraw1Result(protocol)
    --print_error("DIY1抽奖结果", protocol)
    self.data:SetDIY1DrawRewardList(protocol)
    local data_list = self.data:GetDIY1DrawRewardList()
	self:OpenRewardView(data_list.result_item_list)
end

--DIY1恭喜获得界面
function DIYDrawWGCtrl:OpenRewardView(data)
    -- 抽奖
    local index = DIYDrawWGData.Instance:CacheOrGetDIY1DrawIndex()
    local cfg = DIYDrawWGData.Instance:GetDIY1DrawConsumeCfg()
    local other_cfg = DIYDrawWGData.Instance:GetDrawOneItem()

    local other_info = {}
    other_info.stuff_id = other_cfg.cost_item_id
    other_info.times = cfg[index].times
    other_info.spend = other_cfg.cost_gold
    other_info.again_text = string.format(Language.DIYDraw.BtnDrawDesc2, cfg[index].times)
    other_info.total_price_desc1 = Language.DIYDraw.ValueDesc

    local reward_pool_list = DIYDrawWGData.Instance:GetSpecialRewardPoolListCfg()

    --普通奖励
    local data_list = data
    --大奖展示.
    local item_ids = {}
    for k, v in pairs(data_list) do
        for k1, v1 in pairs(reward_pool_list) do
            if v.item_id == v1[1].item.item_id then
                v.draw_corner_type = cfg.reward_big_show
                table.remove(data_list, k)
                table.insert(item_ids, v1[1].item.item_id)
            end
        end
    end

    local best_data = {}
    if IsEmptyTable(item_ids) then
        best_data = nil
    else
        best_data.item_ids = item_ids
    end
    other_info.best_data = best_data

    local ok_func = function()
        DIYDrawWGCtrl.Instance:ClickUseDrawItem(DIYDRAW_TYPE.DIY1, index, function()
            DIYDrawWGCtrl.Instance:SendDIYDrawReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY1_DRAW, OA_DIY_DRAW1_OPERATE_TYPE.DRAW, cfg[index].mode)
        end)
    end
    
    TipWGCtrl.Instance:ShowGetValueReward(data_list, ok_func, other_info, false)

    -- 神品大奖弹窗
    local data_cfg_list = {}
    for index, value in ipairs(item_ids) do
        local cfg = TipWGData.Instance:GetBigRewardCfgByItemId(value)
        if not IsEmptyTable(cfg) then
            table.insert(data_cfg_list, cfg)
        end
    end
    if not IsEmptyTable(data_cfg_list) then
        TipWGCtrl.Instance:OpenBigRewardView(data_cfg_list)
    end
end

--DIY2抽奖结果
function DIYDrawWGCtrl:OnSCOADiyDraw2Result(protocol)
    --print_error("DIY2抽奖结果", protocol)
    self.data:SetDIY2DrawRewardList(protocol)
    local data_list = self.data:GetDIY2DrawRewardList()
    self:OpenDIY2RewardView(data_list.result_item_list)
end

--DIY2恭喜获得界面
function DIYDrawWGCtrl:OpenDIY2RewardView(data)
    self.diy2_draw_reward:SetData(data)
    self.diy2_draw_reward:Open()
end

--DIY1抽奖日志
function DIYDrawWGCtrl:OnSCOADiyDraw1Record(protocol)
    --print_error("DIY1抽奖日志", protocol)
    self.data:SetDIY1Serverlog(protocol)
    if self.diy1_view:IsOpen() then
        self.diy1_view:Flush(0, "flush_record_list")
    end
end

--DIY1抽奖日志更新
function DIYDrawWGCtrl:OnSCOADiyDraw1RecordAdd(protocol)
    --print_error("DIY1抽奖日志更新", protocol)
    self.data:SetDIY1NewServerlog(protocol)
    if self.diy1_view:IsOpen() then
        self.diy1_view:Flush(0, "flush_record_list")
    end
end

-- 注册事件监听
function DIYDrawWGCtrl:RegisterAllEvents()
    if not self.item_data_change then
        self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
        ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)
    end
end

-- 注销事件监听
function DIYDrawWGCtrl:UnRegisterAllEvents()
    -- 注销物品改变监听
    if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end
end

function DIYDrawWGCtrl:OpenDrawOneGaiLvView()
    self.diy1_gailv_view:Open()
end

function DIYDrawWGCtrl:OpenDrawChooseView()
    self.diy1_select_view:Open()
end

function DIYDrawWGCtrl:OpenDrawTwoGaiLvView()
    self.diy2_gailv_view:Open()
end

function DIYDrawWGCtrl:OpenDIY2DrawChooseView()
    self.diy2_select_view:Open()
end

function DIYDrawWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
        if DIYDrawWGData.Instance:GetDIY1StuffCfg(change_item_id) and (self.diy1_view and self.diy1_view:IsOpen()) then
            self.diy1_view:FlushDrawBtnShow(true)
        end
    end
end

function DIYDrawWGCtrl:ClickUseDrawItem(draw_type, index, func)
    local cfg
    local mode_cfg
    if draw_type == DIYDRAW_TYPE.DIY1 then
        cfg = DIYDrawWGData.Instance:GetDIY1DrawConsumeCfg()
        mode_cfg = DIYDrawWGData.Instance:GetDrawOneItem()
    elseif draw_type == DIYDRAW_TYPE.DIY2 then
        cfg = DIYDrawWGData.Instance:GetDIY2DrawConsumeCfg()
        mode_cfg = DIYDrawWGData.Instance:GetDrawTwoItem()
    end

	local cur_cfg = cfg[index]
	if cur_cfg == nil or mode_cfg == nil then
		return
	end

    local tips_data = {}
    tips_data.item_id = mode_cfg.cost_item_id
    tips_data.price = mode_cfg.cost_gold
    tips_data.draw_count = cur_cfg.cost_item_num
    tips_data.has_checkbox = true
    tips_data.checkbox_str = string.format("treasure_hunt%s%s", draw_type, index) 
    TipWGCtrl.Instance:OpenTipsDrawRewardView(tips_data, func, nil)
end