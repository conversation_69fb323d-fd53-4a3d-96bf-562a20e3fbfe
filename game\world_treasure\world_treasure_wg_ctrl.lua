require("game/world_treasure/world_treasure_wg_data")
require("game/world_treasure/world_treasure_view")
require("game/world_treasure/world_treasure_login_view")
require("game/world_treasure/world_treasure_first_recharge_view")
require("game/world_treasure/world_treasure_total_recharge_view")
require("game/world_treasure/world_treasure_pintu_view")
require("game/world_treasure/world_treasure_shop_view")
require("game/world_treasure/world_treasure_limit_buy_view")
require("game/world_treasure/world_treasure_limit_buy_tip_view")
require("game/world_treasure/world_treasure_receive_egg_view")
require("game/world_treasure/world_treasure_jianglin_view")
require("game/world_treasure/world_treasure_mowang_view")
require("game/world_treasure/world_treasure_tsjl_fb_view")
require("game/world_treasure/world_treasure_raise_star_gift_view")
require("game/world_treasure/world_treasure_together_view")
require("game/world_treasure/together_invite_friend_view")
require("game/world_treasure/together_invite_alert")
require("game/world_treasure/world_treasure_premium_store_view")
require("game/world_treasure/world_treasure_scheme_view")
require("game/world_treasure/world_treasure_first_page_view")
require("game/world_treasure/world_treasure_flowing_view")
require("game/world_treasure/world_treasure_pursuit_view")
require("game/world_treasure/world_treasure_notice_view")
require("game/world_treasure/world_treasure_pursuit_task_view")
require("game/world_treasure/pursuit_game_view")

WorldTreasureWGCtrl = WorldTreasureWGCtrl or BaseClass(BaseWGCtrl)

function WorldTreasureWGCtrl:__init()
	if WorldTreasureWGCtrl.Instance then
		error("[WorldTreasureWGCtrl]:Attempt to create singleton twice!")
	end
	WorldTreasureWGCtrl.Instance = self

	self.data = WorldTreasureWGData.New()
	self.view = WorldTreasureView.New(GuideModuleName.WorldTreasureView)
	self.receive_egg_view = WorldTreasureReceiveEggView.New()
	self.limit_buy_tip_view = WorldTreasureLimitBuyTipView.New()
	self.jianglin_fb_view = WorldTreasureTSJLFBView.New()
	self.together_invite_view = TogetherInviteFriendView.New()
	self.together_beinvite_alert = TogetherInviteAlert.New()
	self.world_treasure_notice_view = WorldTreasureNoticeView.New()
	self.pursuit_task_view = WorldTreasurePursuitTaskView.New(GuideModuleName.WorldTreasurePursuitTaskView)
	self.pursuit_game_view = PursuitGameView.New(GuideModuleName.PursuitGameView)

	self:RegisterAllProtocals()
	self:RegisterAllEvents()
end



function WorldTreasureWGCtrl:__delete()
	
	if self.shilian_timer then
		GlobalTimerQuest:CancelQuest(self.shilian_timer)
		self.shilian_timer = nil
	end

	self.view:DeleteMe()
	self.view = nil

	self.receive_egg_view:DeleteMe()
	self.receive_egg_view = nil

	self.limit_buy_tip_view:DeleteMe()
	self.limit_buy_tip_view = nil

	self.jianglin_fb_view:DeleteMe()
	self.jianglin_fb_view = nil

	if self.together_invite_view then
		self.together_invite_view:DeleteMe()
		self.together_invite_view = nil
	end

	if self.together_beinvite_alert then
		self.together_beinvite_alert:DeleteMe()
		self.together_beinvite_alert = nil
	end

	if self.world_treasure_notice_view then
		self.world_treasure_notice_view:DeleteMe()
		self.world_treasure_notice_view = nil
	end

	if self.pursuit_task_view then
		self.pursuit_task_view:DeleteMe()
		self.pursuit_task_view = nil
	end

	if self.pursuit_game_view then
		self.pursuit_game_view:DeleteMe()
		self.pursuit_game_view = nil
	end

	self.data:DeleteMe()
	self.data = nil

	self:UnRegisterAllEvents()

	WorldTreasureWGCtrl.Instance = nil
end

function WorldTreasureWGCtrl:OpenReceiveEggView()
	self.receive_egg_view:Open()
end

function WorldTreasureWGCtrl:OpenLimitBuyTipAndSetData(data)
	self.limit_buy_tip_view:SetData(data)
	self.limit_buy_tip_view:Open()
end

function WorldTreasureWGCtrl:Close()
	if self.view then
		self.view:Close()
	end
end

function WorldTreasureWGCtrl:Open(index, param_t)
	self.view:Open(index)
end


function WorldTreasureWGCtrl:RegisterAllEvents()
	self.open_fun_change = GlobalEventSystem:Bind(OpenFunEventType.OPEN_TRIGGER, BindTool.Bind(self.OpenFunEventChange, self))
    -- 天数改变
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.OnDayChange, self))
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreateCallBack, self)) --主界面加载完成
end

-- 注销事件监听
function WorldTreasureWGCtrl:UnRegisterAllEvents()
	if self.open_fun_change then
		GlobalEventSystem:UnBind(self.open_fun_change)
	end

	if self.view_open_event then
		GlobalEventSystem:UnBind(self.view_open_event)
		self.view_open_event = nil
	end
end


function WorldTreasureWGCtrl:OnDayChange()
    self.data:SetShiLianRefreshTime()
	self:UpdateActivityTime()
	self.view:SetTabVisible()
end

function WorldTreasureWGCtrl:RegisterAllProtocals()
    self:RegisterProtocol(CSTreasureOperate)

    self:RegisterProtocol(SCTreasureNewLoginGiftInfo, 'OnSCTreasureNewLoginGiftInfo')
    self:RegisterProtocol(SCTreasureShouChongInfo, 'OnSCTreasureShouChongInfo')
    self:RegisterProtocol(SCTreasureLeiChongInfo, 'OnSCTreasureLeiChongInfo')
    self:RegisterProtocol(SCWoYaoShenQiInfo, 'OnSCWoYaoShenQiInfo')
    self:RegisterProtocol(SCHunzhenShopAllInfo, 'OnSCHunzhenShopAllInfo')
    self:RegisterProtocol(SCHunzhenShopUpdateInfo, 'OnSCHunzhenShopUpdateInfo')
    self:RegisterProtocol(SCTreasureBaseInfo, 'OnSCTreasureBaseInfo')
	self:RegisterProtocol(SCTreasureDragonEgg, 'OnSCTreasureDragonEgg')
    self:RegisterProtocol(SCTrialFbFinishInfo, 'OnSCTrialFbFinishInfo')
    self:RegisterProtocol(SCTreasureSpecialDropInfo, 'OnSCTreasureSpecialDropInfo')
    self:RegisterProtocol(SCTrialFBBossDeadInfo, 'OnSCTrialFBBossDeadInfo')
	self:RegisterProtocol(SCTreasureUpstarGiftInfo, 'OnSCTreasureUpstarGiftInfo')

	-- 通天降临新
	self:RegisterProtocol(SCTreasureWalkTogetherInfo, 'OnSCTreasureWalkTogetherInfo')
	self:RegisterProtocol(SCTreasureWalkTogetherInvite, 'OnSCTreasureWalkTogetherInvite')
	self:RegisterProtocol(SCTreasureWalkTogetherFriendInfo, 'OnSCTreasureWalkTogetherFriendInfo')
	self:RegisterProtocol(SCTreasureConvertShopInfo, 'OnSCTreasureConvertShopInfo') --专享商店
	self:RegisterProtocol(SCTreasureSchemeInfo, 'OnSCTreasureSchemeInfo') --奕者谋定
	self:RegisterProtocol(SCTreasureFlowingInfo, 'OnSCTreasureFlowingInfo') --川流不息
	self:RegisterProtocol(SCTreasurePursuitTaskInfo, 'OnSCTreasurePursuitTaskInfo') --迷影寻踪任务信息
	self:RegisterProtocol(SCTreasurePursuitTaskUpdateInfo, 'OnSCTreasurePursuitTaskUpdateInfo') --迷影寻踪单个任务更新信息
	self:RegisterProtocol(SCTreasurePursuitGridInfo, 'OnSCTreasurePursuitGridInfo') --迷影寻踪格子信息
	self:RegisterProtocol(SCTreasurePursuitInfo, 'OnSCTreasurePursuitInfo') --迷影寻踪轮次信息
end

function WorldTreasureWGCtrl:SendWorldTreasureOp(opera_type, param1, param2, param3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSTreasureOperate)
    protocol.opera_type = opera_type or 0
    protocol.param_1 = param1 or 0
    protocol.param_2 = param2 or 0
    protocol.param_3 = param3 or 0
    protocol:EncodeAndSend()
end

--天财地宝 登录有礼信息
function WorldTreasureWGCtrl:OnSCTreasureNewLoginGiftInfo(protocol)
    self.data:SetLoginRewardInfo(protocol)
	RemindManager.Instance:Fire(RemindName.WorldTreasure_Login)
	if self.view:IsOpen() then
		self.view:Flush(self.view.show_index)
	end
end

--天财地宝 首充奖励信息
function WorldTreasureWGCtrl:OnSCTreasureShouChongInfo(protocol)
	self.data:SetTreasureShouChongInfo(protocol)
	RemindManager.Instance:Fire(RemindName.WorldTreasure_FirstRecharge)
	if self.view:IsOpen() then
		self.view:Flush(self.view.show_index)
	end
end

--天财地宝 累计充值信息
function WorldTreasureWGCtrl:OnSCTreasureLeiChongInfo(protocol)
	self.data:SetTreasureLeiChongInfo(protocol)
	RemindManager.Instance:Fire(RemindName.WorldTreasure_TotalReCharge)
	if self.view:IsOpen() then
		self.view:Flush(self.view.show_index)
	end
end

--天财地宝 拼图领奖信息
function WorldTreasureWGCtrl:OnSCWoYaoShenQiInfo(protocol)
	self.data:SetPinTuProtocolInfo(protocol)
	RemindManager.Instance:Fire(RemindName.WorldTreasure_ReceiveAward)
	if self.view:IsOpen() then
		self.view:Flush(self.view.show_index)
	end
end

--天财地宝 限时抢购全部信息
function WorldTreasureWGCtrl:OnSCHunzhenShopAllInfo(protocol)
	self.data:SetTreasureLimitBuyAllInfo(protocol)
	RemindManager.Instance:Fire(RemindName.WorldTreasure_LimitBuy)
	if self.view:IsOpen() then
		self.view:Flush(self.view.show_index)
	end
end

--限时抢购单个更新
function WorldTreasureWGCtrl:OnSCHunzhenShopUpdateInfo(protocol)
	self.data:SetTreasureLimitBuyInfo(protocol)
	RemindManager.Instance:Fire(RemindName.WorldTreasure_LimitBuy)
	if self.view:IsOpen() then
		self.view:Flush(self.view.show_index)
	end
end

--升星赠礼信息
function WorldTreasureWGCtrl:OnSCTreasureUpstarGiftInfo(protocol)
	self.data:SetTreasureUpstarGiftInfo(protocol)

	RemindManager.Instance:Fire(RemindName.WorldTreasure_RaiseStarGift)
	if self.view:IsOpen() then
		self.view:Flush(self.view.show_index)
	end
end

--天财地宝 基础信息
function WorldTreasureWGCtrl:OnSCTreasureBaseInfo(protocol)
	local old_grade = WorldTreasureWGData.Instance:GetTreasureCurGrade()
	self.data:SetTreasureBaseInfo(protocol)
	local new_grade = WorldTreasureWGData.Instance:GetTreasureCurGrade()

	if old_grade ~= new_grade then
		self.view.world_treasure_grade_change = true
		self.view.login_grade_change = true
		self.view.total_recharge_grade_change = true
		self.view.first_recharge_grade_change = true
		self.view.jigsaw_grade_change = true
		self.view.flash_sale1_grade_change = true
		self.view.flash_sale2_grade_change = true
		self.view.jianglin_grade_change = true
		self.view.mowang_grade_change = true
	end

	if self.view:IsOpen() then
		self.view:Flush(self.view.show_index)
	end

	self:UpdateActivityTime()

	local act_btn = MainuiWGCtrl.Instance:GetActivityButtonByActivityType(ACTIVITY_TYPE.WORLD_TREASURE)
	if act_btn then
		act_btn:Flush("SetSprite")
	end
end

--龙蛋信息
function WorldTreasureWGCtrl:OnSCTreasureDragonEgg(protocol)
	local old_level = WorldTreasureWGData.Instance:GetDragonEggLevel()
	self.data:SetTreasureDragonEggInfo(protocol)
	local new_level = WorldTreasureWGData.Instance:GetDragonEggLevel()
	if new_level > old_level and ViewManager.Instance:IsOpenByIndex(GuideModuleName.WorldTreasureView, TabIndex.tcdb_login_gift) then
		self.view:PlayUpLevelEffect()
	end

	RemindManager.Instance:Fire(RemindName.WorldTreasure_Login)
	if self.view:IsOpen() then
		self.view:Flush(self.view.show_index)
	end
end

function WorldTreasureWGCtrl:FlushLoginView()
	self.view:Flush(TabIndex.tcdb_login_gift)
end

function WorldTreasureWGCtrl:OpenFunEventChange(check_all, fun_name, is_open)
	local is_update = false
    if check_all then
		is_update = true
	elseif fun_name == FunName.tcdb_login_gift then
		is_update = true
	elseif fun_name == FunName.tcdb_first_recharge then
		is_update = true
	elseif fun_name == FunName.tcdb_total_recharge then
		is_update = true
	elseif fun_name == FunName.tcdb_jigsaw then
		is_update = true
	elseif fun_name == FunName.tcdb_flash_sale then
		is_update = true
	elseif fun_name == FunName.tcdb_jianglin then
		is_update = true
    end
	
	if is_update then
		if self.view:IsOpen() then
			self.view:Flush(self.view.show_index, "func_update")
		end
	end

    if check_all or fun_name == FunName.WorldTreasureView then
		is_open = WorldTreasureWGData.Instance:GetActivityIsOpen()
        local state = is_open and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE
		local end_time = WorldTreasureWGData.Instance:GetCurGradeEndTime()
        ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.WORLD_TREASURE, state, end_time)--, nil, end_time)

		if is_open then
			RemindManager.Instance:Fire(RemindName.WorldTreasure_Login)
			RemindManager.Instance:Fire(RemindName.WorldTreasure_FirstRecharge)
			RemindManager.Instance:Fire(RemindName.WorldTreasure_TotalReCharge)
			RemindManager.Instance:Fire(RemindName.WorldTreasure_ReceiveAward)
			RemindManager.Instance:Fire(RemindName.WorldTreasure_LimitBuy)
			RemindManager.Instance:Fire(RemindName.WorldTreasure_JiangLin)
		end

		if state == ACTIVITY_STATUS.CLOSE then
			self.view:Close()
		end

		self:RegisterViewOpenHandler(is_open)
		self:CheckJiangLinIsOpen()
    end
end

-- 跨天或持续时间到后更新主界面活动时间
function WorldTreasureWGCtrl:UpdateActivityTime()
	local is_open = WorldTreasureWGData.Instance:GetActivityIsOpen()
	local state = is_open and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE
	local end_time = WorldTreasureWGData.Instance:GetCurGradeEndTime()
	ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.WORLD_TREASURE, state, end_time)
	if state == ACTIVITY_STATUS.CLOSE then
		self.view:Close()
	end
	self:RegisterViewOpenHandler(is_open)
	self:CheckJiangLinIsOpen()
end

function WorldTreasureWGCtrl:MainuiOpenCreateCallBack()
	local is_open = WorldTreasureWGData.Instance:GetActivityIsOpen()
	local state = is_open and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE
	local end_time = WorldTreasureWGData.Instance:GetCurGradeEndTime()
	ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.WORLD_TREASURE, state, end_time)--, nil, end_time)
	if is_open then
		RemindManager.Instance:Fire(RemindName.WorldTreasure_Login)
		RemindManager.Instance:Fire(RemindName.WorldTreasure_FirstRecharge)
		RemindManager.Instance:Fire(RemindName.WorldTreasure_TotalReCharge)
		RemindManager.Instance:Fire(RemindName.WorldTreasure_ReceiveAward)
		RemindManager.Instance:Fire(RemindName.WorldTreasure_LimitBuy)
		RemindManager.Instance:Fire(RemindName.WorldTreasure_JiangLin)

		local grade = WorldTreasureWGData.Instance:GetTreasureCurGrade()  --临时处理，档位1才有拍脸图
		if grade == 1 and not self:TodayNoTips() then
			if self.world_treasure_notice_view and not self.world_treasure_notice_view:IsOpen() then
				self.world_treasure_notice_view:Open()
			end
		end
	end
	self:RegisterViewOpenHandler(is_open)
	self:CheckJiangLinIsOpen()
	
end

function WorldTreasureWGCtrl:TodayNoTips()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local has_today_flag = PlayerPrefsUtil.GetInt("tcdb_notice_view_open" .. main_role_id) == open_day
	return has_today_flag
end

function WorldTreasureWGCtrl:RegisterViewOpenHandler(is_open)
	if is_open then
		if not self.view_open_event then
			self.view_open_event = GlobalEventSystem:Bind(OtherEventType.VIEW_OPEN, BindTool.Bind(self.ViewOpenHandler, self))
		end
	else
		if self.view_open_event then
			GlobalEventSystem:UnBind(self.view_open_event)
			self.view_open_event = nil
		end
	end

end




function WorldTreasureWGCtrl:ViewOpenHandler(view)
	local is_open = WorldTreasureWGData.Instance:GetActivityIsOpen()
	if is_open then
		local is_has_open_task, open_task_list = WorldTreasureWGData.Instance:GetPinTuOpenTaskData()
		if is_has_open_task then
			for k, v in pairs(open_task_list) do
				if view and view.view_name and view.view_name == tostring(v.panel) then
					self:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_WOYAOSHENQI_TASK_CLIENT, v.ID)
					break
				end
			end
		end
	end
end


function WorldTreasureWGCtrl:OnSCTreasureSpecialDropInfo(protocol)
	self.data:SetExtraDropInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.tcdb_mowang)
	end
end

function WorldTreasureWGCtrl:OnSCTrialFBBossDeadInfo(protocol)
	self.data:SetShiLianBossState(protocol.is_boss_dead)
	if self.jianglin_fb_view:IsOpen() then
		self.jianglin_fb_view:Flush("boss_state",protocol.is_boss_dead)
	end
	self.view:Flush(TabIndex.tcdb_jianglin)
	self:CheckJiangLinIsOpen()
	RemindManager.Instance:Fire(RemindName.WorldTreasure_JiangLin)
end


------------------------------试炼副本-----------------------

---[[ 试炼副本
function WorldTreasureWGCtrl:RankActTSJLReq()
	self:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_CENTER_TRIAL_FB)
end

-- 结算
function WorldTreasureWGCtrl:OnSCTrialFbFinishInfo(protocol)
	WorldTreasureWGData.Instance:SetTSJLFinishInfo(protocol)
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.WORLD_TREASURE_JIANLIN then
		ActivityWGCtrl.Instance:OpenActJiseSuanView(ACTIVITY_TYPE.WORLD_TREASURE)
	end
end

-- boss出生震屏效果
function WorldTreasureWGCtrl:BossBornTween()
   local boss_id, cfg_data = WorldTreasureWGData.Instance:GetBossId()
   local scene_id = Scene.Instance:GetSceneId()
   if cfg_data and scene_id == cfg_data.scene_id then
	   Scene.Instance:ShakeMainCamera(1, 0.5, 30, 40)
   end
end

-- 活动icon控制
function WorldTreasureWGCtrl:CheckTianShenJianLinActIcon()
   local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.WORLD_TREASURE_JIANGLIN)
   if not act_cfg then
	   return
   end

   local role_level = RoleWGData.Instance:GetRoleLevel()
   if role_level < act_cfg.level or role_level > act_cfg.level_max then
	   return
   end

   local act_info = self.tianshen_road_data:GetTianShenJianLinInfo()
   if act_info and (act_info.activity_state == ACTIVITY_STATUS.STANDY or act_info.activity_state == ACTIVITY_STATUS.OPEN) then
	   local time_cfg = TianshenRoadWGData.Instance:GetJiangLinFlushTimeCfg()
	   local ser_time = TimeWGCtrl.Instance:GetServerTime()
	   local time_tab = os.date("*t", ser_time)
	   local hour = 0
	   local min = 0
	   for i=1,#time_cfg do
		   hour = math.floor(time_cfg[i].refresh_time / 100)
		   min = time_cfg[i].refresh_time - hour * 100
		   if time_tab.hour < hour and time_tab.min < min then
			   break
		   end
	   end
	   local open_time_str = string.format("%d:%02d%s", hour, min, Language.Activity.KaiQi)
	   MainuiWGCtrl.Instance:AddTempActIcon(ACTIVITY_TYPE.TIANSHENJIANLIN, act_info.next_time, act_cfg, COLOR3B.GREEN, nil, open_time_str)
   else
	   MainuiWGCtrl.Instance:ActivitySetButtonVisible(ACTIVITY_TYPE.TIANSHENJIANLIN, false)
   end
end

-- 设置活动开启状态
function WorldTreasureWGCtrl:CheckJiangLinIsOpen()
	local is_open = WorldTreasureWGData.Instance:GetActivityIsOpen()
	local is_in_shilian_open, refresh_time= WorldTreasureWGData.Instance:IsInShiLianActivity()
	local act_type = ACTIVITY_TYPE.WORLD_TREASURE_JIANGLIN
	local server_time = TimeWGCtrl.Instance:GetServerTime()

	local first_ready_times = WorldTreasureWGData.Instance:GetOtherCfg("first_ready_times") or 0
	local is_boss_dead = WorldTreasureWGData.Instance:GetShiLianBossState()
	if is_open and refresh_time then
		MainuiWGCtrl.Instance:ActivitySetButtonVisible(act_type, true)
		if is_in_shilian_open and not is_boss_dead then
			-- print_error("试炼活动开1")
			ActivityWGData.Instance:SetActivityStatus(act_type, ACTIVITY_STATUS.OPEN, refresh_time.end_timestemp,0,refresh_time.end_timestemp)
			return
		elseif not is_in_shilian_open and server_time >= refresh_time.start_timestemp - first_ready_times then
			-- print_error("试炼活动开2")
			ActivityWGData.Instance:SetActivityStatus(act_type, ACTIVITY_STATUS.STANDY, refresh_time.start_timestemp, 0, refresh_time.end_timestemp)
			return
		end
	end
	-- print_error("试炼活动关")
	MainuiWGCtrl.Instance:ActivitySetButtonVisible(act_type, false)
	ActivityWGData.Instance:SetActivityStatus(act_type, ACTIVITY_STATUS.CLOSE)
end

function WorldTreasureWGCtrl:SetShilLianTimer()
	if self.shilian_timer then
		GlobalTimerQuest:CancelQuest(self.shilian_timer)
		self.shilian_timer = nil
	end
	local first_ready_times = WorldTreasureWGData.Instance:GetOtherCfg("first_ready_times")
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local refresh_time_list = WorldTreasureWGData.Instance:GetShiLianRefreshTime()
	for index, value in ipairs(refresh_time_list) do
		-- 预告
		if server_time < value.start_timestemp and server_time > value.start_timestemp - first_ready_times then
			self:CheckJiangLinIsOpen()
			-- print_error("预告")
			-- print_error("时间：",value.start_timestemp - server_time)
			self.shilian_timer = GlobalTimerQuest:AddDelayTimer(function ()
				RemindManager.Instance:Fire(RemindName.WorldTreasure_JiangLin)
				self:SetShilLianTimer()
			end, value.start_timestemp - server_time)
			return
		-- 未开启
		elseif server_time < value.start_timestemp then
			-- print_error("未开启")
			-- print_error("时间2：",value.start_timestemp - server_time - first_ready_times)

			self:CheckJiangLinIsOpen()
			self.shilian_timer = GlobalTimerQuest:AddDelayTimer(function ()
				self.is_boss_dead = false -- 重置boss死亡状态
				RemindManager.Instance:Fire(RemindName.WorldTreasure_JiangLin)
				self:SetShilLianTimer()
			end, value.start_timestemp - server_time - first_ready_times)
			return
		-- 开启中
		elseif server_time < value.end_timestemp then
			-- print_error("开启中")
			self:CheckJiangLinIsOpen()
			self.shilian_timer = GlobalTimerQuest:AddDelayTimer(function ()
				RemindManager.Instance:Fire(RemindName.WorldTreasure_JiangLin)
				self:SetShilLianTimer()
			end, value.end_timestemp - server_time)
			return
		end
	end
	-- print_error("全部结束")
end

-- npc对话修改
function WorldTreasureWGCtrl:CheckShiLianNpc(npc_id)
	local npcid = WorldTreasureWGData.Instance:GetOtherCfg("npcid")
   	if npcid ~= npc_id then
		return false
   	end

   	local is_in = WorldTreasureWGData.Instance:IsInShiLianActivity()
   	if is_in then
		return true, Language.WorldTreasure.NPCTalkStr_2, BindTool.Bind(self.RankActTSJLReq, self)
   	end
	return false
	--		return true, Language.TianShenRoad.NPCTalkStr_1
end

-- 前往NPC
function WorldTreasureWGCtrl:GotoShiLian()
   	local scene_type = Scene.Instance:GetSceneType()
   	if scene_type == SceneType.WORLD_TREASURE_JIANLIN then
		return
   	end

   	local npc_scene = WorldTreasureWGData.Instance:GetOtherCfg("npc_scene")
   	local npc_id = WorldTreasureWGData.Instance:GetOtherCfg("npcid")
   	if not npc_scene or not npc_id then
		return
	end
   	RoleWGCtrl.Instance:SetJumpAlertCheck(npc_scene, function()
	   GuajiWGCtrl.Instance:MoveToNpc(npc_id, nil, npc_scene)
   	end, true)
end

function WorldTreasureWGCtrl:OpenJLFbView()
	self.jianglin_fb_view:Open()
end
--]]

------------------------------携手同行-----------------------

function WorldTreasureWGCtrl:OpenTogetherInviteView()
	if not self.together_invite_view:IsOpen() then
		self.together_invite_view:Open()
	else
		self.together_invite_view:Flush()
	end
end

function WorldTreasureWGCtrl:CloseTogetherInviteView()
	if self.together_invite_view:IsOpen() then
		self.together_invite_view:Close()
	end
end

--携手同行信息
function WorldTreasureWGCtrl:OnSCTreasureWalkTogetherInfo(protocol)
	self.data:SetTreasureWalkTogetherInfo(protocol)
	RemindManager.Instance:Fire(RemindName.WorldTreasure_Together)
	if self.view:IsOpen() then
		self.view:Flush(self.view.show_index)
	end
    ViewManager.Instance:FlushView(GuideModuleName.ControlBeastsPrizeDrawWGView, 0, "team_info")
end

--携手同行邀请
function WorldTreasureWGCtrl:OnSCTreasureWalkTogetherInvite(protocol)
	self.data:SetTogetherInviteInfo(protocol)
	self:OpenTogetherInviteAlert()
end

--携手可邀请好友信息
function WorldTreasureWGCtrl:OnSCTreasureWalkTogetherFriendInfo(protocol)
	self.data:SetTogetherInviteFriendList(protocol)
	-- for i, v in ipairs(protocol.friend_list) do
	-- 	AvatarManager.Instance:SetAvatarKey(v.uid, v.avatar_key_big, v.avatar_key_small)
	-- end
	if self.together_invite_view:IsOpen() then
		self.together_invite_view:Flush()
	end
end

-- 打开受到邀请弹窗
function WorldTreasureWGCtrl:OpenTogetherInviteAlert()
	local invite_info = WorldTreasureWGData.Instance:GetTogetherInviteTopInfo()
	if invite_info == nil or IsEmptyTable(invite_info) then return end

	local data = {}
	local inviter_str = string.format(Language.WorldTreasure.TogetherInvite, invite_info.inviter_name)

	data.ok_str = Language.NewTeam.AlertTipApply
	data.cancel_str = Language.WorldTreasure.CancelStr
	data.lable_str = inviter_str
	data.ok_func = BindTool.Bind(self.OnAlertClickOK, self, invite_info)
	data.cancel_func = BindTool.Bind(self.OnAlertClickCancel, self, invite_info)
	data.countdown_func = BindTool.Bind(self.OnAlertCountDown, self, invite_info)

	self.together_beinvite_alert:SetDataAndOpen(data)
end

function WorldTreasureWGCtrl:OnAlertClickOK(info)
	self:SendTogetherInviteReq(info)
end

function WorldTreasureWGCtrl:OnAlertClickCancel(info)
	--self:SendTogetherInviteReq(info, 1)
end

function WorldTreasureWGCtrl:OnAlertCountDown(info)
	--self:SendTogetherInviteReq(info, 1)
	self.together_beinvite_alert:Close()
end

function WorldTreasureWGCtrl:SendTogetherInviteReq(invite_info)
	if not invite_info or not invite_info.inviter_uid or invite_info.inviter_uid == 0 then
		return
	end
	WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_WALK_TOGETHER_ACCEPT, invite_info.inviter_uid)
end

function WorldTreasureWGCtrl:FlushTextInvite()
	if self.together_invite_view:IsOpen() and self.together_invite_view:IsLoaded() then
	    self.together_invite_view:FlushTextInvite()
    end
end

------------------------------专享商店-----------------------

function WorldTreasureWGCtrl:OnSCTreasureConvertShopInfo(protocol)
	self.data:SetTreasureConvertShopInfo(protocol)
	RemindManager.Instance:Fire(RemindName.WorldTreasure_PremiumShop)
	if self.view:IsOpen() then
		self.view:Flush(self.view.show_index)
	end
end

------------------------------奕者谋定-----------------------
function WorldTreasureWGCtrl:OnSCTreasureSchemeInfo(protocol)
	self.data:SetTreasureSchemeInfo(protocol)
	RemindManager.Instance:Fire(RemindName.WorldTreasure_Scheme)
	if self.view:IsOpen() then
		self.view:Flush(self.view.show_index)
	end
end

------------------------------川流不息-----------------------
function WorldTreasureWGCtrl:OnSCTreasureFlowingInfo(protocol)
	self.data:SetTreasureFlowingInfo(protocol)
	RemindManager.Instance:Fire(RemindName.WorldTreasure_Flowing)
	if self.view:IsOpen() then
		self.view:Flush(self.view.show_index)
	end
end

------------------------------迷影寻踪-----------------------
-- 任务信息
function WorldTreasureWGCtrl:OnSCTreasurePursuitTaskInfo(protocol)
	self.data:SetTreasurePursuitTaskInfo(protocol)
	RemindManager.Instance:Fire(RemindName.WorldTreasure_Pursuit)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.tcdb_pursuit)
	end
	if self.pursuit_task_view:IsOpen() then
		self.pursuit_task_view:Flush()
	end
end

-- 单个任务更新
function WorldTreasureWGCtrl:OnSCTreasurePursuitTaskUpdateInfo(protocol)
	self.data:SetTreasurePursuitTaskUpdateInfo(protocol)
	RemindManager.Instance:Fire(RemindName.WorldTreasure_Pursuit)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.tcdb_pursuit)
	end
	if self.pursuit_task_view:IsOpen() then
		self.pursuit_task_view:Flush()
	end
end

-- 格子信息
function WorldTreasureWGCtrl:OnSCTreasurePursuitGridInfo(protocol)
	-- print_error("=======OnSCTreasurePursuitGridInfo======", protocol)
	self.data:SetTreasurePursuitGridInfo(protocol)
	RemindManager.Instance:Fire(RemindName.WorldTreasure_Pursuit)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.tcdb_pursuit)
	end
	if self.pursuit_game_view:IsOpen() then
		self.pursuit_game_view:Flush(nil, "flush_grid")
	end
end

-- 轮次信息
function WorldTreasureWGCtrl:OnSCTreasurePursuitInfo(protocol)
	-- print_error("=======OnSCTreasurePursuitInfo======", protocol)
	self.data:SetTreasurePursuitInfo(protocol)
	RemindManager.Instance:Fire(RemindName.WorldTreasure_Pursuit)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.tcdb_pursuit)
	end
	if self.pursuit_game_view:IsOpen() then
		self.pursuit_game_view:Flush(nil, "flush_turn")
	end
end

-- 打开迷影寻踪任务界面
function WorldTreasureWGCtrl:OpenPursuitTaskView()
	if not self.pursuit_task_view:IsOpen() then
	    self.pursuit_task_view:Open()
	else
		self.pursuit_task_view:Flush()
    end
end

-- 打开迷影寻踪游戏界面
function WorldTreasureWGCtrl:OpenPursuitGameView()
	if not self.pursuit_game_view:IsOpen() then
	    self.pursuit_game_view:Open()
	else
		self.pursuit_game_view:Flush()
    end
end

-- 请求飞镖协议
function WorldTreasureWGCtrl:PursuitClickReq(index)
	if self.is_block_dart_req then
		return
	end
	self:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_PURSUIT_CHOOSE, index - 1)
	WorldTreasureWGData.Instance:SetBreakGridIndex(index)
	self.is_block_dart_req = true
	ReDelayCall(self, function()
		self.is_block_dart_req = false
	end, 1.5, "pursuit_dart_req_block")
end