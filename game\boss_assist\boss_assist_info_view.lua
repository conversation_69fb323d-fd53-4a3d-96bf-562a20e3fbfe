
BossAssistInfoView = BossAssistInfoView or BaseClass(SafeBaseView)
function BossAssistInfoView:__init()
	self.default_index = 1
	self.active_close = false
	self.view_layer = UiLayer.MainUILow
    self.view_name = "BossAssistInfoView"
	self:AddViewResource(0, "uis/view/boss_assist_ui_prefab", "layout_assist_info")
	self.view_cache_time = 0
	self.open_tween = nil
	self.close_tween = nil
	self.next_call_time = 0
end

function BossAssistInfoView:__delete()
end

function BossAssistInfoView:ReleaseCallBack()
	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end
    self.next_call_time = 0
    if self.next_call_timer then
		GlobalTimerQuest:CancelQuest(self.next_call_timer)
		self.next_call_timer = nil
	end
end

function BossAssistInfoView:LoadCallBack(index, loaded_times)
	XUI.AddClickEventListener(self.node_list.btn_help, BindTool.Bind1(self.OnclickHelp, self))
	XUI.AddClickEventListener(self.node_list.btn_assist_goto, BindTool.Bind1(self.OnClickGotoKill, self))
	XUI.AddClickEventListener(self.node_list.btn_swithc_list, BindTool.Bind1(self.OnClicSwithList, self))


	self.rank_list = AsyncListView.New(BossAssistInfoItem, self.node_list.TaskList1)
    self.rank_list:SetSelectCallBack(BindTool.Bind(self.OnclickInfoItem, self))
    
	self:Flush()
end

function BossAssistInfoView:Init()
	local mainui_ctrl = MainuiWGCtrl.Instance

	local parent = mainui_ctrl:GetTaskOtherContent()
	self.node_list.task_root_view.transform:SetParent(parent.transform)
	self.node_list.task_root_view.transform.anchoredPosition = Vector3(0, 0, 0)
	self.node_list.task_root_view.transform.localScale = Vector3(1, 1, 1)

    GlobalEventSystem:Fire(OtherEventType.SHOW_BOSS_CHANGE, true)
    self:FlushDesText()
end

function BossAssistInfoView:OnclickHelp()
	if BossAssistWGData.Instance:IsAssist() then
		ViewManager.Instance:Open(GuideModuleName.BossAssistHelp)
	else
		if RoleWGData.Instance.role_vo.guild_id > 0 then
			BossAssistWGCtrl.SendGuildAssistCallHelp()
			self.next_call_time = TimeWGCtrl.Instance:GetServerTime() + 60
			self:Flush()
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotEnterNoGuild)
		end
	end
end

function BossAssistInfoView:OnClickGotoKill()
	local assist_info = BossAssistWGData.Instance:GetAssistInfo()
	GuajiWGCtrl.Instance:StopGuaji()
	GuajiWGCtrl.Instance:ClearTemporary()
	MoveCache.SetEndType(MoveEndType.FightByMonsterId)
	GuajiCache.monster_id = assist_info.target_boss_id
	MoveCache.param1 = assist_info.target_boss_id
	local range = BossWGData.Instance:GetMonsterRangeByid(assist_info.target_boss_id)

	GuajiWGCtrl.Instance:MoveToPos(assist_info.target_boss_scene_id, assist_info.target_boss_pos_x, assist_info.target_boss_pos_y, range)
end

function BossAssistInfoView:OnClicSwithList()
	GlobalEventSystem:Fire(OtherEventType.SHOW_BOSS_CHANGE, false)
end

function BossAssistInfoView:ShowIndexCallBack(index)
    local init_callback = function ()
		self:Init()
       
	end
	local mainuictrl = MainuiWGCtrl.Instance
	mainuictrl:AddInitCallBack(nil,init_callback)
	self:Flush(index)
end

function BossAssistInfoView:OpenCallBack()

end

function BossAssistInfoView:CloseCallBack()
    GlobalEventSystem:Fire(OtherEventType.SHOW_BOSS_CHANGE, false)
    if self.node_list.task_root_view then
        self.node_list.task_root_view.transform:SetParent(self.root_node_transform, false)
    end
end

function BossAssistInfoView:FlushDesText()
    local damage_list, boss_id = BossAssistWGData.Instance:GetGuildAssistBossDamageListInfo()
    if Scene.Instance:GetSceneType() == SceneType.VIP_BOSS or Scene.Instance:GetSceneType() == SceneType.WorldBoss then
        if boss_id ~= 0 then
            local boss_cfg = BossWGData.Instance:GetMonsterCfgByid(boss_id)
            --魔王巢穴和世界boss
            if boss_cfg and boss_cfg.drop_type and boss_cfg.drop_type == BOSS_DROP_OWN_TYPE.BOSS_DROP_OWN_TYPE_HURT_RANK then
                self.node_list.des_text_info.text.text = string.format(Language.Boss.DemageGetReward, boss_cfg.drop_param) 
            else
                print_error("取不到boss配置或者boss配置有误， boss_id ==",boss_id)
            end
        end
    else
        self.node_list.des_text_info.text.text = Language.Boss.DemageFirstGetAward
    end
end

function BossAssistInfoView:OnFlush(param_t, index)
    local damage_list, boss_id = BossAssistWGData.Instance:GetGuildAssistBossDamageListInfo()
    self:FlushDesText()

	self.rank_list:SetDataList(damage_list)
	local assist_info = BossAssistWGData.Instance:GetAssistInfo()
	self.node_list.my_damate.text.text = CommonDataManager.ConverNumber(assist_info.damage_to_boss)
	if assist_info.assist_role_id > 0 then
		self.node_list.btn_help_text.text.text = Language.BossAssist.AssistReward
	else
		if self.next_call_time > TimeWGCtrl.Instance:GetServerTime() then
			if nil == self.next_call_timer then
				self.next_call_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.UpdateNextCallTime, self), 1)
			end
		end
		self:UpdateNextCallTime()
	end
	self.node_list.per_bg.slider.value = assist_info.damage_to_boss / BossAssistWGData.Instance:GetGuildAssistBossDamageMax()


	self.node_list["btn_swithc_list"]:SetActive(not BossWGData.IsSingleBoss())
end

function BossAssistInfoView:UpdateNextCallTime()
	local time = self.next_call_time - TimeWGCtrl.Instance:GetServerTime()
	if time > 0 then
		self.node_list.btn_help_text.text.text = Language.BossAssist.GuildAssist .. math.floor(time)--, COLOR3B.GRAY)
	else
		if self.next_call_timer then
			GlobalTimerQuest:CancelQuest(self.next_call_timer)
			self.next_call_timer = nil
		end
		self.node_list.btn_help_text.text.text =  Language.BossAssist.GuildAssist
	end
	XUI.SetButtonEnabled(self.node_list.btn_help, time <= 0)
end

function BossAssistInfoView:OnclickInfoItem(cell)
	--cell:OpenCustomMenu()
end
-------------------------------------------------------------------------------------------------------------------------
BossAssistInfoItem = BossAssistInfoItem or BaseClass(BaseRender)
function BossAssistInfoItem:__init()

end
function BossAssistInfoItem:__delete()
	if self.alert_tips then
		self.alert_tips:DeleteMe()
		self.alert_tips = nil
	end
end

function BossAssistInfoItem:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.attk, BindTool.Bind(self.OnClickAttk, self))
	--XUI.AddClickEventListener(self.node_list.xiezhu_btn, BindTool.Bind(self.OnClickXieZhu, self))
	--XUI.AddClickEventListener(self.node_list.button, BindTool.Bind(self.OpenCustomMenu, self))
end

--点击协助
function BossAssistInfoItem:OnClickXieZhu()
	if nil == self.data then return end
	local obj_id = self.data.role_id
	local assist_info = BossAssistWGData.Instance:GetAssistInfo()
	local need_level = BossWGData.Instance:GetBossLevelLimitBySceneId(assist_info.target_boss_scene_id)
    local role_level = RoleWGData.Instance:GetRoleLevel()
	local str = ""

	if role_level < need_level then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BossAssist.AssistTips6)
		return
	end

	if SocietyWGData.Instance:GetIsInTeam() == 1 then
		str = Language.BossAssist.AssistTips3
	elseif assist_info.target_boss_id > 0 then
		if assist_info.assist_role_id > 0 then
			if assist_info.assist_role_id == self.data.role_id then
				TipWGCtrl.Instance:ShowSystemMsg(Language.BossAssist.AssistTips4)
				return
			else
				str = Language.BossAssist.AssistTips2
			end
		else
			str = Language.BossAssist.AssistTips7
		end
	else
		self:SendAssist()
		return
	end
	if not self.alert_tips then
		self.alert_tips = Alert.New()
		self.alert_tips:SetOkFunc(BindTool.Bind(self.SendAssist, self))
	end
	self.alert_tips:SetLableString(string.format(str, self.data.role_name))
	self.alert_tips:Open()
	
end

function BossAssistInfoItem:SendAssist()
	if SocietyWGData.Instance:GetIsInTeam() == 1 then
		SocietyWGCtrl.Instance:SendExitTeam()
	end
	local assist_info = BossAssistWGData.Instance:GetAssistInfo()
	BossAssistWGCtrl.SendGuildAssistGoToHelp(self.data.role_id, assist_info.target_boss_scene_id, assist_info.target_boss_id)
end


function BossAssistInfoItem:OnClickAttk()
	if nil == self.data then return end
	local obj

	if IS_ON_CROSSSERVER then
		obj = Scene.Instance:GetObjByOriId(self.data.role_id)
	else
		obj = Scene.Instance:GetObjByUId(self.data.role_id)
	end

	if nil == obj then
		for k,v in pairs(self.data.team_member_t) do
			obj = Scene.Instance:GetObjByUId(v)
			if obj then
				break
			end
		end
	end
	if obj then
		local is_enemy, reason = Scene.Instance:IsEnemy(obj)
		if is_enemy then
			BossAssistWGData.Instance:AddForceAttackRole(self.data.role_id)
			local assist_info = BossAssistWGData.Instance:GetAssistInfo()
			BossAssistWGCtrl.SendGuildAssistForceAttackReq(self.data.role_id, assist_info.target_boss_scene_id, assist_info.target_boss_id)
			GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj, SceneTargetSelectType.SCENE)
		else
			if reason and reason ~= "" then
				TipWGCtrl.Instance:ShowSystemMsg(reason)
			else
				TipWGCtrl.Instance:ShowSystemMsg(Language.Fight.ModeCantAttack)
			end
		end
	end
end

function BossAssistInfoItem:OnFlush()
	local name = self.data.role_name
	if #self.data.team_member_t > 0 then
		name = name .. Language.BossAssist.TeamFlag
	end
	self.node_list.name.text.text = name
	local damage = CommonDataManager.ConverNumber(self.data.damage_value)
	self.node_list.damage.text.text = damage
	self.node_list.per_bg.slider.value = self.data.damage_value / BossAssistWGData.Instance:GetGuildAssistBossDamageMax()
	local is_meng = not self.data.is_mine and self.data.guild_id > 0 and self.data.guild_id == RoleWGData.Instance.role_vo.guild_id

	self.node_list.meng:SetActive(is_meng)
	self.node_list.xiezhu_btn:SetActive(false)

	self.node_list.attk:SetActive(not self.data.is_mine)
	if not self.data.is_mine then
		local attack_info = BossAssistWGData.Instance:GetIsAttackerInfo(self.data.role_id)	
		if attack_info then
			local cur_time = TimeWGCtrl.Instance:GetServerTime()
			local time_1 = attack_info.last_attack_me_time + 5 - cur_time
			if time_1 > 0 then			
				self.node_list.qipao:SetActive(true)				
			else		
				self.node_list.qipao:SetActive(false)
			end
		else
			self.node_list.qipao:SetActive(false)
		end
		if self.index == 1 then
			--self.node_list.attk.image:LoadSprite() --显示抢夺
		else
			--self.node_list.attk.image:LoadSprite() --显示攻击
		end
	else
		self.node_list.qipao:SetActive(false)
	end
	local owner_id
	if IS_ON_CROSSSERVER then
		owner_id = RoleWGData.Instance:GetAttr("origin_uid")
	else
		owner_id = GameVoManager.Instance:GetMainRoleVo().role_id
	end

	self.node_list.xiezhuzhong:SetActive(false)--self.data.is_mine and owner_id ~= self.data.role_id)

	if self.index < 4 then
		self.node_list["icon"]:SetActive(true)
		self.node_list["icon"].image:LoadSprite(ResPath.GetF2CommonIcon("icon_paiming_big_" .. self.index))
		self.node_list.num.text.text = ""
	else
		self.node_list["icon"]:SetActive(false)
		self.node_list.num.text.text = self.index
	end
end

function BossAssistInfoItem:OpenCustomMenu()
	local main_role_id = RoleWGData.Instance:InCrossGetOriginUid()
	if self.data.role_id == main_role_id then
		return
	end

    local member_count = SocietyWGData.Instance:GetTeamMemberCount()
	local items, str_tip, oprate
	local this_team_num = #self.data.team_member_t

	if member_count > 0 then
		if this_team_num > 0 then
			items = {Language.Menu.MergeTeam}--队伍合并
			oprate = BossAssistWGData.TeamOperate.Merge

			if this_team_num + member_count > 3 then
				str_tip = Language.BossAssist.BossMergeMaxMember
			end
		else
			items = {Language.Menu.InviteInTeam}--邀请入队
			oprate = BossAssistWGData.TeamOperate.Invite

			if member_count == GameEnum.TEAM_MAX_COUNT then
				str_tip = Language.BossAssist.BossInviteMaxMember
			end
		end
	else
		if this_team_num > 0 then
			items = {Language.Menu.ApplyTeam}--申请入队
			oprate = BossAssistWGData.TeamOperate.Apply

			if this_team_num == GameEnum.TEAM_MAX_COUNT then
				str_tip = Language.BossAssist.BossApplyMaxMember
			end
		else
			items = {Language.Menu.InviteTeam2}--邀请组队
			oprate = BossAssistWGData.TeamOperate.Invite
		end
	end

	if UserVo.GetServerId(self.data.role_id) ~= UserVo.GetServerId(main_role_id) then
		str_tip = Language.BossAssist.BossNoSameServer
	end

	if items == nil then
		return
	end

	UiInstanceMgr.Instance:OpenCustomMenu(items, self:GetPos(self.node_list["button"]), BindTool.Bind(self.OnclickInvite, self, oprate, str_tip))
end

function BossAssistInfoItem:OnclickInvite(oprate, str_tip)
	if str_tip then
		SysMsgWGCtrl.Instance:ErrorRemind(str_tip)
		return
	end

	if oprate == BossAssistWGData.TeamOperate.Invite then
		if 0 == SocietyWGData.Instance:GetIsInTeam() then
			local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
			NewTeamWGCtrl.Instance:SendCreateTeam(0, 1, min_level, max_level)
		end
		NewTeamWGCtrl.Instance:SendInviteUser(self.data.role_id, 0, 1)
	elseif oprate == BossAssistWGData.TeamOperate.Apply then
		SocietyWGCtrl.Instance:SendReqJoinTeam(self.data.team_index)
	else
		NewTeamWGCtrl.Instance:SendTeamMergeReq(self.data.role_id)
	end

end

function BossAssistInfoItem:GetPos(node)
    if nil == node then return nil end
    local main_view = ViewManager.Instance:GetView(GuideModuleName.MainUIView)
	if nil == main_view or not main_view:IsOpen() then
		return
	end
    local parent_rect= main_view.root_node.transform:GetComponent(typeof(UnityEngine.RectTransform))
	local x, y = parent_rect.sizeDelta.x / 2, parent_rect.sizeDelta.y / 2
    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, node.transform.position)
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, Vector2(0, 0))

    x = local_position_tbl.x
    y = local_position_tbl.y
    return Vector2(x, y)
end

function BossAssistInfoItem:OnSelectChange(is_select)
	self.node_list["high"]:SetActive(is_select)
end