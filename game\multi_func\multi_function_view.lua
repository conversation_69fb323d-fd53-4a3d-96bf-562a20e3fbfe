local CHARM_BG = {
	[11] = "a2_hzx_bj_1",
	[12] = "a2_hzx_bj_2",
	[21] = "a2_bgyx_bg1",
	[22] = "a2_bgyx_bg1",
	[23] = "a2_bgyx_bg1",
	[24] = "a2_bgyx_bg1",
	[25] = "a2_bgyx_bg1",
	[26] = "a2_bgyx_bg1",
}

local NOTICE_EQUIP_STATE_INDEX = {
	TabIndex.daohang_qianghua,
	TabIndex.daohang_jinjie,
	TabIndex.daohang_keyin,
	TabIndex.daohang_kaiguang,
}

-- local CHARM_INDEX = {
-- 	TabIndex.charm_holy_seal,
-- 	TabIndex.charm_lingzhu,
-- }

local VER_INDEX = {
	--CHARM = 1,
	DAOHANG = 1,
}

-- 画中仙已移至境界-天道石
MultiFunctionView = MultiFunctionView or BaseClass(SafeBaseView)

function MultiFunctionView:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
	self.default_index = TabIndex.daohang_suit
	self.view_name = GuideModuleName.MultiFunctionView

	local common_bundle_name = "uis/view/multi_function_ui_prefab"
	local charm_bundle_name = "uis/view/multi_function_ui/charm_prefab"
	local daohang_bundle_name = "uis/view/multi_function_ui/daohang_prefab"
	self:AddViewResource(0, common_bundle_name, "layout_muti_func_bg")
	--self:AddViewResource(TabIndex.charm_holy_seal, charm_bundle_name, "layout_charm_holy_seal")
	--self:AddViewResource(TabIndex.charm_lingzhu, charm_bundle_name, "layout_charm_lingzhu")

	self:AddViewResource(TabIndex.daohang_suit, daohang_bundle_name, "layout_daohang_suit")
	self:AddViewResource(TabIndex.daohang_keling, daohang_bundle_name, "layout_daohang_keling")
	self:AddViewResource(TabIndex.daohang_qianghua, daohang_bundle_name, "layout_daohang_qianghua")
	self:AddViewResource(TabIndex.daohang_jinjie, daohang_bundle_name, "layout_daohang_jinjie")
	self:AddViewResource(TabIndex.daohang_keyin, daohang_bundle_name, "layout_daohang_keyin")
	self:AddViewResource(TabIndex.daohang_kaiguang, daohang_bundle_name, "layout_daohang_kaiguang")

	self:AddViewResource(0, "uis/view/common_panel_prefab", "VerticalTabbar")
	self:AddViewResource(0, common_bundle_name, "HorizontalTabbar")
	self:AddViewResource(0, common_bundle_name, "layout_multi_func_top_panel")

    self.tab_sub = {
		Language.Charm.TabGrop2, --Language.Charm.TabGrop1,
	}
	self.remind_tab = {
		--{RemindName.Charm_Holy_Seal, RemindName.Charm_LingZhu},
		{RemindName.DaoHang_Suit, RemindName.DaoHang_KeLing, RemindName.DaoHang_QiangHua, RemindName.DaoHang_JinJie, RemindName.DaoHang_KeYin, 
		RemindName.DaoHang_KaiGuang}
	}
end

function MultiFunctionView:LoadIndexCallBack(index)
	-- if index == TabIndex.charm_holy_seal then
    --     self:LoadHolySealCallBack()
	-- elseif index == TabIndex.charm_lingzhu then
    --     self:LoadLingZhuCallBack()
	if index == TabIndex.daohang_suit then
		self:LoadDaoHangSuitCallBack()
	elseif index == TabIndex.daohang_keling then
		self:LoadDaoHangKeLingCallBack()
	elseif index == TabIndex.daohang_qianghua then
		self:LoadDaoHangQiangHuaCallBack()
	elseif index == TabIndex.daohang_jinjie then
		self:LoadDaoHangJinJieCallBack()
	elseif index == TabIndex.daohang_keyin then
		self:LoadDaoHangKeYinCallBack()
	elseif index == TabIndex.daohang_kaiguang then
		self:LoadDaoHangKaiGuangCallBack()
	end
end

function MultiFunctionView:ChangeCharmBg(index)
	local bg_str = CHARM_BG[index] or CHARM_BG[11]

	if nil == self.bg_res_cache or self.bg_res_cache ~= bg_str then
		self.bg_res_cache = bg_str
		local bundle, asset = ResPath.GetRawImagesPNG(bg_str)
		self.node_list.charm_big_bg.raw_image:LoadSprite(bundle, asset, function()
			self.node_list.charm_big_bg.raw_image:SetNativeSize()
		end)
	end
end

function MultiFunctionView:LoadCallBack()
	self.tween_info = UITween_CONSTS.MultiFunction
	self.last_show_index = -1
	self.last_show_ver = -1

	self.tabbar = Tabbar.New(self.node_list)
	local view_bundle = "uis/view/multi_function_ui_prefab"
	--self.tabbar:SetHorTabSmallIconStr("a1_charm_icon")
	--self.tabbar:SetHorTabbarIconPath(ResPath.GetMultiFunctionImg)
	self.tabbar:Init(Language.Charm.TabGrop, self.tab_sub, nil, view_bundle, self.remind_tab)
	self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))

	FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.MultiFunctionView, self.tabbar)
    XUI.AddClickEventListener(self.node_list.charm_tips, BindTool.Bind(self.OnClickCharmTips, self))
end

function MultiFunctionView:ReleaseCallBack()
    if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	self.last_show_index = nil
	self.bg_res_cache = nil
	self.tween_info = nil
	self.last_show_ver = nil
    -- self:ReleaseLingZhuCallBack()
    -- self:ReleaseHolySealCallBack()
	self:ReleaseDaoHangSuitCallBack()
	self:ReleaseKeLingCallBack()
	self:ReleaseQiangHuaCallBack()
	self:ReleaseJinJieCallBack()
	self:ReleaseKeYinCallBack()
	self:ReleaseKaiGuangCallBack()
end

function MultiFunctionView:ShowIndexCallBack(index)
	local ver_index = math.floor(index / 10)
	local title_str = Language.Charm.TabGrop[ver_index] or ""
	self.node_list.title_view_name.text.text = title_str

	self:ChangeCharmBg(index)

	self:ShowIndexChange(self.last_show_index)
	self.last_show_index = index
    -- if index == TabIndex.charm_holy_seal then
    --     self:ShowHolySealCallBack()
	-- elseif index == TabIndex.charm_lingzhu then
    --     self:ShowLingZhuCallBack()
	if index == TabIndex.daohang_suit then
		self:ShowDaoHangSuitCallBack()
	elseif index == TabIndex.daohang_keling then
		self:ShowDaoHangKeLingCallBack()
	elseif index == TabIndex.daohang_qianghua then
		self:ShowDaoHangQiangHuaCallBack()
	elseif index == TabIndex.daohang_jinjie then
		self:ShowDaoHangJinJieCallBack()
	elseif index == TabIndex.daohang_keyin then
		self:ShowDaoHangKeYinCallBack()
	elseif index == TabIndex.daohang_kaiguang then
		self:ShowDaoHangKaiGuangCallBack()
	end

	if self.last_show_ver ~= ver_index then
		self:UpdateMutiFuncVerClickLimit(ver_index)
		self.last_show_ver = ver_index
	end
end

function MultiFunctionView:ShowIndexChange(old_index)
	-- if old_index == TabIndex.charm_holy_seal then
	-- 	self:DeleteHolySealAnim()
	-- 	return
	-- end

	-- if old_index == TabIndex.charm_lingzhu then
	-- 	self:DeleteLingZhuAnim()
	-- 	return
	-- end

	if old_index == TabIndex.daohang_qianghua then
		self:DaoHangQiangShowIndexChange()
		return
	end

	if old_index == TabIndex.daohang_jinjie then
    	self:DaoHangJinJieShowIndexChange()
		return
	end

	if old_index == TabIndex.daohang_kaiguang then
		self:DeleteDaoHangKaiGuangAnim()
		return
	end
end

function MultiFunctionView:OnFlush(param_t, index)
	for k,v in pairs(param_t) do
		if k == "all" then
			-- if index == TabIndex.charm_holy_seal then
            --     self:OnFlushHolySeal()
            -- elseif index == TabIndex.charm_lingzhu then
            --     self:OnFlushLingZhu()
			if index == TabIndex.daohang_suit then
				self:OnFlushDaoHangSuit()
			elseif index == TabIndex.daohang_keling then
				self:OnFlushDaoHangKeLing()
			elseif index == TabIndex.daohang_qianghua then
				self:OnFlushDaoHangQiangHua()
			elseif index == TabIndex.daohang_jinjie then
				self:OnFlushDaoHangJinJie()
			elseif index == TabIndex.daohang_keyin then
				self:OnFlushDaoHangKeYin()
			elseif index == TabIndex.daohang_kaiguang then
				self:OnFlushDaoHangKaiGuang()
			end
		end
	end
end

function MultiFunctionView:OnClickCharmTips()
	local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.Charm.TipsTitle[self.show_index])
	rule_tip:SetContent(Language.Charm.TipsContent[self.show_index], nil, nil, nil, true)
end

function MultiFunctionView:RightPanleShowTween(right_node, canvas_group_node)
	RectTransform.SetAnchoredPositionXY(right_node.rect, 500, 0)
	right_node.rect:DOAnchorPos(Vector2(0, 0), self.tween_info.movetime)
	canvas_group_node.canvas_group.alpha = 0
	canvas_group_node.canvas_group:DoAlpha(0, 1, self.tween_info.canvas_group_show)
end

function MultiFunctionView:UpdateMutiFuncTabbarClickLimit()
	local ver_index = math.floor(self.show_index / 10)

	if ver_index == VER_INDEX.DAOHANG then
		self:UpdateMutiFuncVerClickLimit(VER_INDEX.DAOHANG)
	end
end

function MultiFunctionView:UpdateMutiFuncVerClickLimit(ver_index)
	-- if ver_index == VER_INDEX.CHARM then
	-- 	for k, v in pairs(CHARM_INDEX) do
	-- 		self.tabbar:SetHorOtherBtn(v, false, nil)
	-- 	end
	-- else
	if ver_index == VER_INDEX.DAOHANG then
		local no_wear_equip_func = function ()
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Charm.DaoHangNoWearTips)
		end

		local no_wear_equip = MultiFunctionWGData.Instance:IsDaoHangNoWearEquip()

		for k, v in pairs(NOTICE_EQUIP_STATE_INDEX) do
			self.tabbar:SetHorOtherBtn(v, no_wear_equip, no_wear_equip_func)
		end

		local keling_equip_func = function ()
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Charm.DaoHangKeLingOpenTips)
		end

		local cannot_keling_open = MultiFunctionWGData.Instance:CanOpenDaoHangKeLing()
		self.tabbar:SetHorOtherBtn(TabIndex.daohang_keling, cannot_keling_open, keling_equip_func)
	end
end