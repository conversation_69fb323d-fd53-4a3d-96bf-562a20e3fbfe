EquipmentBSXQOverview = EquipmentBSXQOverview or BaseClass(SafeBaseView)

function EquipmentBSXQOverview:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 14),sizeDelta = Vector2(1080, 614)})
	self:AddViewResource(0, "uis/view/equipment_ui_prefab", "layout_equipment_bsxq_overview")
end

function EquipmentBSXQOverview:LoadCallBack()
    if not self.equipment_bsxq_equip_body_list then
        self.equipment_bsxq_equip_body_list = AsyncListView.New(BSXQEquipBodyOverviewItemCellRender, self.node_list.equipment_bsxq_equip_body_list)
    end

    self.node_list.title_view_name.text.text = Language.Equipment.BSXQOverviewTitle
end

function EquipmentBSXQOverview:ReleaseCallBack()
    if self.equipment_bsxq_equip_body_list then
        self.equipment_bsxq_equip_body_list:DeleteMe()
        self.equipment_bsxq_equip_body_list = nil
    end
end

function EquipmentBSXQOverview:OnFlush()
    local data_list = EquipBodyWGData.Instance:GetTotalEquipBodyDataList()
    self.equipment_bsxq_equip_body_list:SetDataList(data_list)
end

----------------------------------BSXQEquipBodyOverviewItemCellRender-------------------------------------------
BSXQEquipBodyOverviewItemCellRender = BSXQEquipBodyOverviewItemCellRender or BaseClass(BaseRender)

function BSXQEquipBodyOverviewItemCellRender:LoadCallBack()
    self.default_select_part = -1
    self.equip_body_seq_cache = -1
    self.default_select_part_data = {}

    if not self.bsxq_equip_list then
        self.bsxq_equip_list = AsyncListView.New(BSXQOverviewEquipItemCellRender, self.node_list.bsxq_equip_list)
        self.bsxq_equip_list:SetSelectCallBack(BindTool.Bind(self.OnSelectEquipHandler, self))
    end

    XUI.AddClickEventListener(self.node_list["btn_to_equip_suit"], BindTool.Bind(self.OnClickToEquipSuitBtn, self))
    XUI.AddClickEventListener(self.node_list["btn_bsxq"], BindTool.Bind(self.OnClickBtnBSXQBtn, self))
end

function BSXQEquipBodyOverviewItemCellRender:__delete()
    if self.bsxq_equip_list then
        self.bsxq_equip_list:DeleteMe()
        self.bsxq_equip_list = nil
    end
end

function BSXQEquipBodyOverviewItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    if self.equip_body_seq_cache ~= self.data.seq then
        self.default_select_part = -1
        self.default_select_part_data = {}
    end

    self.equip_body_seq_cache = self.data.seq

    local unlock = EquipBodyWGData.Instance:IsEquipBodyUnLock(self.data.seq)
    local is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyWearEquip(self.data.seq)
    self.node_list.unlock:CustomSetActive(not unlock or not is_wear_equip)
    self.node_list.bsxq_equip_list:CustomSetActive(unlock and is_wear_equip)
    self.node_list.name.text.text = self.data.name
    self.node_list.desc_unlock.text.text = not unlock and Language.RoleEquipBody.EquipBodyLock or Language.RoleEquipBody.EquipBodyNotWearEquip
    self.node_list.btn_bsxq:CustomSetActive(unlock and is_wear_equip)

    local baoshi_total_level = EquipmentWGData.Instance:GetTotalStoneLevel(self.data.seq)
    self.node_list.desc_btn_bsxq.text.text = string.format(Language.Equipment.BSXQOverviewCellName, baoshi_total_level)

    if unlock then
        local show_equip_data_list = EquipmentWGData.Instance:GetBSXQShowEquipList(self.data.seq)
        self.bsxq_equip_list:SetDataList(show_equip_data_list)
    end
end

function BSXQEquipBodyOverviewItemCellRender:OnSelectEquipHandler(cell)
    if nil == cell or IsEmptyTable(cell.data) then
        return
    end

    self.default_select_part_data = cell.data
end

function BSXQEquipBodyOverviewItemCellRender:OnClickToEquipSuitBtn()
    local unlock = EquipBodyWGData.Instance:IsEquipBodyUnLock(self.data.seq)
    local is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyWearEquip(self.data.seq)

    if not unlock or not is_wear_equip then
        local str = not unlock and Language.RoleEquipBody.EquipBodyLock or Language.RoleEquipBody.EquipBodyNotWearEquip
        SysMsgWGCtrl.Instance:ErrorRemind(str)
        return
    end

    if unlock and is_wear_equip then
        EquipmentWGCtrl.Instance:Flush(TabIndex.equipment_baoshi, "equip_baoshi_change_to_equip_body", {equip_body_seq = self.data.seq, selct_part_data = self.default_select_part_data})
    end

    EquipmentWGCtrl.Instance:CloseBSXQOverviewView()
end

function BSXQEquipBodyOverviewItemCellRender:OnClickBtnBSXQBtn()
    RoleWGCtrl.Instance:SetEquipTextData(ROLE_EQUIP_ATTR_TIPS.STONE_TIP, self.data.seq)
	RoleWGCtrl.Instance:OpenEquipAttr()
end

-------------------------------------BSXQOverviewEquipItemCellRender--------------------------------------
BSXQOverviewEquipItemCellRender = BSXQOverviewEquipItemCellRender or BaseClass(BaseRender)

function BSXQOverviewEquipItemCellRender:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["ph_item"])
        self.item_cell:SetIsShowTips(false)
    end

	self.old_item_id = 0
end

function BSXQOverviewEquipItemCellRender:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end

    self.old_item_id = nil
end

function BSXQOverviewEquipItemCellRender:OnFlush()
	if nil == self.data or nil == self.data.param then
		return
	end

    if self.item_cell and self.data.item_id ~= self.old_item_id then
		self.item_cell:SetData(self.data)
        self.item_cell:SetRightTopImageTextActive(false)
	end

	self.old_item_id = self.data.item_id

    local stone_info = EquipmentWGData.Instance:GetStoneInfoListByIndex(self.data.index)
	if nil == stone_info then
		return
	end

	local show_stone_list = {}
	for i= 0, GameEnum.MAX_STONE_COUNT - 1 do
		local is_open = EquipmentWGData.Instance:BaoShiSlotIsOpen(self.data.index, i)
		if is_open then
			table.insert(show_stone_list, stone_info[i])
		end
	end

	if not IsEmptyTable(show_stone_list) then
		table.sort(show_stone_list, SortTools.KeyUpperSorter("item_id"))
	end

    local total_level = 0
	for i = 0, GameEnum.MAX_STONE_COUNT - 1 do
		if show_stone_list[i + 1] then
			local data = show_stone_list[i + 1]
			local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
			local bundle, asset = "", ""
			if item_cfg then
				local stone_cfg = EquipmentWGData.Instance:GetBaoShiCfgByItemId(data.item_id)
				local stone_type = stone_cfg.stone_type
				bundle, asset = ResPath.GetEquipmentIcon("a2_bsi_" .. (stone_type or 1))
				self.node_list["icon_" .. i].image:LoadSprite(bundle, asset, function()
					self.node_list["icon_" .. i].image:SetNativeSize()
				end)

                total_level = total_level + stone_cfg.level
			end

			self.node_list["icon_" .. i]:SetActive(item_cfg ~= nil)
			self.node_list["img_suo_" .. i]:SetActive(false)
		else
			self.node_list["icon_" .. i]:SetActive(false)
			self.node_list["img_suo_" .. i]:SetActive(true)
		end
	end

    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if item_cfg then
		local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		-- if self.is_jinlian_item then
		-- 	local refine_level = EquipmentWGData.Instance:GetStoneRefineLevelByPart(self.data.index)
		-- 	local level_str = refine_level > 0 and " +" .. refine_level or ""
		-- 	item_name = item_name .. level_str
		-- end

        local show_name = item_name .. (total_level > 0 and string.format(Language.Equipment.BSXQOverviewItemCellName, total_level) or "")
		self.node_list["lbl_name"].text.text = show_name
		self.node_list["lbl_name_hl"].text.text = show_name
	end
end

function BSXQOverviewEquipItemCellRender:OnSelectChange(is_select)
	self.node_list["normal_bg"]:SetActive(not is_select)
	self.node_list["select_bg"]:SetActive(is_select)
end