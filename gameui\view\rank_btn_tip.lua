----------------------------------------------------
-- 描述显示
--<AUTHOR>
----------------------------------------------------
RankBtnTip = RankBtnTip or BaseClass(SafeBaseView)

function RankBtnTip:__init()
	if RankBtnTip.Instance then
		ErrorLog("[RankBtnTip] Attemp to create a singleton twice !")
	end
	self:AddViewResource(0, "uis/view/ranktips_prefab", "rank")
	self.btn_list = {}
	self.btn_event_list = {}

	self.bundle_name = 'uis/view/tips/ranktips_prefab'
	self.prefab_name = 'rank_btn_item'
end

function RankBtnTip:__delete()
	self.btn_list = nil
	self.btn_event_list = nil
end

function RankBtnTip:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.bg,function ()
		self:Close()
	end)
	for i,v in ipairs(self.btn_event_list) do
		local async_loader = AllocAsyncLoader(self, "button" .. i)
		async_loader:SetParent(self.node_list['list_view'].transform)
		async_loader:Load(self.bundle_name, self.prefab_name, function(obj)
		 	obj = U3DObject(obj)
			obj.button.onClick:AddListener(v)
			table.insert(self.btn_list,obj)
		end)
	end
end

function RankBtnTip:ReleaseCallBack()

end

function RankBtnTip:SetBtnName()
	for i,v in ipairs(self.btn_event_list) do

	end
end

function RankBtnTip:SetData(btn_event_list)
	self.btn_event_list = btn_event_list
end

