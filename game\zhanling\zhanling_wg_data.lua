ZhanLingWGData = ZhanLingWGData or BaseClass()

function ZhanLingWGData:__init()
	if ZhanLingWGData.Instance ~= nil then
		ErrorL<PERSON>("[ZhanLingWGData] attempt to create singleton twice!")
		return
	end

	ZhanLingWGData.Instance = self

	self.zhanling_info = {
		level = 0,
		act_value = 0,
		cur_round = 1,
		cur_day = 1,
		open_zl_sys = false,
		act_high = false,
		overflow_nor_reward_num = 0,
		overflow_high_reward_num = 0,
	}
	self.level_reward_flag = {}
	self.round_limit_list = {}
	self.daily_limit_list = {}
	self.level_buy_count_list = {}
	self.week_task_list = {}
	self.daily_task_list = {}
	self.updata_reward_list_flag = false
	self.zhanling_old_level = 0
	self.is_first = true
	self.is_new_round = false
	self.exchange_open_flag = false

	self.zhanling_cfg = ConfigManager.Instance:GetAutoConfig("battlelingpai_cfg_auto")
	self.up_level_cfg = ListToMap(self.zhanling_cfg.up_level, "cycle_id", "level")
	self.shop_exchange_cfg = ListToMapList(self.zhanling_cfg.rechange_shop, "cycle_id")
	self.task_cfg_map = ListToMap(self.zhanling_cfg.task_list, "task_id")
	self.special_item_sort_cfg = ListToMap(self.zhanling_cfg.item_sort, "cycle", "item_id")
    RemindManager.Instance:Register(RemindName.ZhanLing_Reward, BindTool.Bind(self.GetZhanLingRewardRemind, self))
	RemindManager.Instance:Register(RemindName.ZhanLing_Task, BindTool.Bind(self.GetZhanLingTaskAllRemind, self))
	RemindManager.Instance:Register(RemindName.ZhanLing_ExChange, BindTool.Bind(self.GetZhanLingExChangeRemind, self))
end

function ZhanLingWGData:__delete()
	ZhanLingWGData.Instance = nil
    RemindManager.Instance:UnRegister(RemindName.ZhanLing_Reward)
	RemindManager.Instance:UnRegister(RemindName.ZhanLing_Task)
	RemindManager.Instance:UnRegister(RemindName.ZhanLing_ExChange)
end

function ZhanLingWGData:SetZhanLingInfo(protocol)
	self.zhanling_info.level = protocol.zhanling_level
	self.zhanling_info.act_value = protocol.act_value
	self.zhanling_info.cur_round = protocol.zhanling_round
	self.zhanling_info.cur_day = protocol.day_in_round
	self.zhanling_info.open_zl_sys = protocol.isopen_zhanling_sys == 1
	self.zhanling_info.act_high = protocol.buy_high_flag == 1
	self.zhanling_info.overflow_nor_reward_num = protocol.overflow_nor_reward_num
	self.zhanling_info.overflow_high_reward_num = protocol.overflow_high_reward_num
	self.is_new_round = protocol.is_new_round == 1
	self.level_reward_flag = protocol.level_reward_flag
	self.updata_reward_list_flag = true
	if self.is_first then
		self:SetZhanLingOldLevel()
		self.is_first = false
	end
end

function ZhanLingWGData:SetZhanLingOldLevel()
	self.zhanling_old_level = self.zhanling_info.level
end

function ZhanLingWGData:GetZhanLingOldLevel()
	return self.zhanling_old_level
end

function ZhanLingWGData:GetZhanLingInfo()
	return self.zhanling_info
end

function ZhanLingWGData:SetZhanLingItemPos(pos)
	self.item_pos = pos
end

function ZhanLingWGData:GetZhanLingItemPos()
	return self.item_pos
end

function ZhanLingWGData:GetIsNewRound()
	return self.is_new_round
end

-- 当前的周期
function ZhanLingWGData:GetZhanLingCurRound()
	return self.zhanling_info.cur_round
end

-- 当前的周期的第几天
function ZhanLingWGData:GetZhanLingCurRoundDay()
	return self.zhanling_info.cur_day
end

-- 高级战令激活状态
function ZhanLingWGData:GetIsActHighZhanLing()
	return self.zhanling_info.act_high
end

-- 等级溢出后战令奖励
function ZhanLingWGData:GetAfterMaxLevelRewardNum()
	return self.zhanling_info.overflow_nor_reward_num, self.zhanling_info.overflow_high_reward_num
end

function ZhanLingWGData:GetZhanLingOtherCfg()
	return self.zhanling_cfg.other and self.zhanling_cfg.other[1]
end

-- 获取活跃度
function ZhanLingWGData:GetUpNextLevelNeedActValue()
	local cur_level = self.zhanling_info.level
	local cur_max_level = self:GetCurRewardMaxLevel()
	local need_act_value = 1
	if cur_level >= cur_max_level then
		local other_cfg = self:GetZhanLingOtherCfg()
		need_act_value = other_cfg and other_cfg.fix_up_level_exp or 1
	else
		local level_cfg = ZhanLingWGData.Instance:GetCurLevelCfgByLevel(cur_level + 1)
		need_act_value = level_cfg and level_cfg.up_level_exp or 1
	end

	return need_act_value
end

-- 奖励状态   0:不可领取  1：可领取  2：已领白银奖励  3：已领取所有奖励
function ZhanLingWGData:GetLevelReWardStateByLevel(level)
	level = level or 0
	local nor_state = REWARD_STATE_TYPE.UNDONE
	local high_state = REWARD_STATE_TYPE.UNDONE
	local is_act = self:GetIsActHighZhanLing()
	local cur_max_level = self:GetCurRewardMaxLevel()
	if level > cur_max_level then
		local overflow_nor_num, overflow_high_num = self:GetAfterMaxLevelRewardNum()
		if overflow_nor_num > 0 then
			nor_state = REWARD_STATE_TYPE.CAN_FETCH
		elseif overflow_nor_num <= 0 and overflow_high_num > 0 and is_act then
			nor_state = REWARD_STATE_TYPE.FINISH
		end

		if overflow_high_num > 0 and is_act then
			high_state = REWARD_STATE_TYPE.CAN_FETCH
		end
	else
		local cur_flag = self.level_reward_flag[level] or ZhanLingRewardFetchState.None

		if cur_flag == ZhanLingRewardFetchState.CanFetch then
			nor_state = REWARD_STATE_TYPE.CAN_FETCH
			high_state = is_act and REWARD_STATE_TYPE.CAN_FETCH or high_state
		elseif cur_flag == ZhanLingRewardFetchState.FetchNormal then
			nor_state = REWARD_STATE_TYPE.FINISH
			high_state = is_act and REWARD_STATE_TYPE.CAN_FETCH or high_state
		elseif cur_flag == ZhanLingRewardFetchState.FetchAll then
			nor_state = REWARD_STATE_TYPE.FINISH
			high_state = REWARD_STATE_TYPE.FINISH
		end
	end

	return nor_state, high_state
end

-- 当前升级奖励
function ZhanLingWGData:GetCurLevelCfg()
	local round = self:GetZhanLingCurRound()
	return self.up_level_cfg[round]
end

function ZhanLingWGData:GetCurLevelCfgByLevel(level)
	local cfg = self:GetCurLevelCfg()
	return cfg and cfg[level]
end

-- 展示奖励的等级列表
function ZhanLingWGData:GetRewardShowLevelCfg()
	local cur_round = self:GetZhanLingCurRound()
	if not self.reward_show_level_list or self.old_reward_round ~= cur_round then
		self.reward_show_level_list = {}
		self.max_show_level = 0
		self.old_reward_round = cur_round
		local cfg = self:GetCurLevelCfg()
		if IsEmptyTable(cfg) then
			return self.reward_show_level_list, self.max_show_level
		end

		for k, v in ipairs(cfg) do
			if v.guding_mark > 0 then
				self.reward_show_level_list[k] = true
				self.max_show_level = k
			end
		end
	end

	return self.reward_show_level_list, self.max_show_level
end

function ZhanLingWGData:GetIsShowLevel(level)
	local list = self:GetRewardShowLevelCfg()
	return list[level]
end

-- 获得对应等级的下一显示等级
function ZhanLingWGData:GetNextShowLevel(level)
	level = level or 0
	local show_level = -1
	local list = self:GetRewardShowLevelCfg()
	local max_level = self:GetCurRewardMaxLevel()
	local last_can_show_level = 0
	for i = 1, max_level do
		if list[i] then
			last_can_show_level = i
			if i > level then
				show_level = i
				break
			end
		end
	end

	if show_level < 0 then
		show_level = max_level + 1
	end

	return show_level, last_can_show_level
end

-- 奖励显示的最大等级
function ZhanLingWGData:GetCurRewardMaxLevel()
	local cfg = self:GetCurLevelCfg()
	return cfg and #cfg or 0
end

-- 升级奖励 带状态 列表
function ZhanLingWGData:GetRewardShowRemindInfo()
	if self.updata_reward_list_flag or self.level_reward_list == nil then
		self.level_reward_list = {}
		local cfg = self:GetCurLevelCfg()
		local cur_max_level = self:GetCurRewardMaxLevel()

		if IsEmptyTable(cfg) then
			return self.level_reward_list
		end

		for k, v in ipairs(cfg) do
			local data = {}
			local nor_state, high_state = self:GetLevelReWardStateByLevel(v.level)
			data.level = v.level
			data.guding_mark = v.guding_mark
			data.model_item_id = v.model_item_id
			data.nor_reward_list = v.reward_item_1
			data.high_reward_list = v.reward_item_2
			data.nor_reward_state = nor_state
			data.high_reward_state = high_state
			table.insert(self.level_reward_list, data)
		end

		self.updata_reward_list_flag = false
	end
	--print_error("黄金奖励", self.level_reward_list)
	return self.level_reward_list
end

--获取普通战令和典藏战令 无状态的奖励
function ZhanLingWGData:GetBuyPriceCfg(reward_type)
	if self.silver_reward_list == nil or self.gold_reward_list == nil then
		self.silver_reward_list = {}
		self.gold_reward_list = {}

		local cfg = self:GetCurLevelCfg()
		if IsEmptyTable(cfg) then
			return self.level_reward_list
		end

		for k, v in ipairs(cfg) do

			--白银奖励
			local silver_data = v.reward_item_1[0]
			table.insert(self.silver_reward_list, silver_data)
			
			--黄金奖励
			if #v.reward_item_2 > 0 then
				local gold1 = v.reward_item_2[0]
				local gold2 = v.reward_item_2[1]
				table.insert(self.gold_reward_list, gold1)
				table.insert(self.gold_reward_list, gold2)
			else
				table.insert(self.gold_reward_list, v.reward_item_2[0])
			end
		end
	end

	if reward_type == 1 then --白银奖励
		return self.silver_reward_list
	elseif reward_type == 2 then --黄金奖励
		return self.gold_reward_list
	end
end

function ZhanLingWGData:GetBuyShowItemLst()
	if not self.silver_buy_show_list or not self.gold_buy_show_list then
		self.silver_buy_show_list = {}
		self.gold_buy_show_list = {}
		local buy_show_config = self:GetActViewShowModelCfg()

		if not buy_show_config then
			print_error("展示奖励配置有问题！")
			return {}, {}
		end

		local reward_item_1 = buy_show_config.reward_item_1
		local reward_item_2 = buy_show_config.reward_item_2
		local idx = 0
		for _, _ in pairs(reward_item_1) do
			table.insert(self.silver_buy_show_list, reward_item_1[idx])
			idx = idx + 1
		end
		local idx = 0
		for _, _ in pairs(reward_item_2) do
			table.insert(self.gold_buy_show_list, reward_item_2[idx]) 
			idx = idx + 1
		end
	end
	return self.silver_buy_show_list, self.gold_buy_show_list
end

function ZhanLingWGData:GetRewardAutoJumpIndex()
	local reward_list = self:GetRewardShowRemindInfo()
	local def_level = 1
	local cur_level = self.zhanling_info.level
	local cur_max_level = self:GetCurRewardMaxLevel()

	if IsEmptyTable(reward_list) then
		return def_level
	end

	for k, v in ipairs(reward_list) do
		if v.nor_reward_state == REWARD_STATE_TYPE.CAN_FETCH
		or v.high_reward_state == REWARD_STATE_TYPE.CAN_FETCH then
			return v.level
		end
	end

	if cur_level >= cur_max_level then
		def_level = cur_max_level
	else
		for k, v in ipairs(reward_list) do
			if v.nor_reward_state == REWARD_STATE_TYPE.UNDONE then
				def_level = v.level - 1
				def_level = def_level >= 1 and def_level or 1
				return def_level
			end
		end
	end

	return def_level
end

function ZhanLingWGData:GetZhanLingRemindKey(key)
	local role_uuid = RoleWGData.Instance:GetUUid()
	local role_id = role_uuid.temp_low .. role_uuid.temp_high--RoleWGData.Instance:GetOriginUid()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local real_key = "zhanling" .. role_id .. key .. server_day
	return real_key
end

function ZhanLingWGData:SetZhanLingTodayRemind(key)
	local real_key = self:GetZhanLingRemindKey(key)
	PlayerPrefsUtil.SetInt(real_key, 1)
end

function ZhanLingWGData:GetZhanLingTodayRemind(key)
	local real_key = self:GetZhanLingRemindKey(key)
	local cache_value = PlayerPrefsUtil.GetInt(real_key)
	return cache_value ~= 1
end

function ZhanLingWGData:GetZhanLingRewardRemind()
	-- local today_remind = self:GetZhanLingTodayRemind("reward")
	-- if today_remind then
	-- 	return 1
	-- end

	local reward_list = self:GetRewardShowRemindInfo()
	if IsEmptyTable(reward_list) then
		return 0
	end

	for k, v in pairs(reward_list) do
		if v.nor_reward_state == REWARD_STATE_TYPE.CAN_FETCH
			or v.high_reward_state == REWARD_STATE_TYPE.CAN_FETCH then
			return 1
		end
	end

	local is_act = self:GetIsActHighZhanLing()
	local overflow_nor_num, overflow_high_num = self:GetAfterMaxLevelRewardNum()
	if overflow_nor_num > 0 or (is_act and overflow_high_num > 0) then
		return 1
	end

	return 0
end

function ZhanLingWGData:GetRewardShowInfoByLevel(level)
	local info = self:GetRewardShowRemindInfo()
	return info[level]
end

-- 无限升级的奖励
function ZhanLingWGData:GetSpecialRewardShowRemindInfo()
	local other_cfg = self:GetZhanLingOtherCfg()
	if IsEmptyTable(other_cfg) then
		return {}
	end

	local data = {}
	local cur_max_level = self:GetCurRewardMaxLevel()
	local show_level = cur_max_level + 1
	local nor_state, high_state = self:GetLevelReWardStateByLevel(show_level)
	data.level = show_level
	data.nor_reward_list = other_cfg.reward_item_1
	data.high_reward_list = other_cfg.reward_item_2
	data.nor_reward_state = nor_state
	data.high_reward_state = high_state

	return data
end

-- 战令奖励可领取
function ZhanLingWGData:GetHaveRewardCanGet()
	local reward_info = self:GetRewardShowRemindInfo()
	if not IsEmptyTable(reward_info) then
		for k, v in ipairs(reward_info) do
			if v.nor_reward_state == REWARD_STATE_TYPE.CAN_FETCH
			or v.high_reward_state == REWARD_STATE_TYPE.CAN_FETCH then
				return true
			end
		end
	end

	local special_info = self:GetSpecialRewardShowRemindInfo()
	if not IsEmptyTable(special_info) then
		if special_info.nor_reward_state == REWARD_STATE_TYPE.CAN_FETCH
		or special_info.high_reward_state == REWARD_STATE_TYPE.CAN_FETCH then
			return true
		end
	end

	return false
end

-- 获取当前周期 活动剩余时间
function ZhanLingWGData:GetCurRoundRestTime()
	local other_cfg = self:GetZhanLingOtherCfg()
	local cur_day = self.zhanling_info.cur_day
	local round_day = other_cfg and other_cfg.cycle_day or 0
	-- 垃圾后端逻辑写的是>=round_day就会结束轮次
	-- 现只能 配置加多一天 前端热更新处理
	local rest_day = round_day - cur_day - 1
	rest_day = rest_day > 0 and rest_day or 0
	local today_rest = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
	local total_rest = today_rest + rest_day * 86400

	return total_rest
end

--获取描述
function ZhanLingWGData:GetActViewShowModelCfg()
	local cfg = self.zhanling_cfg.buy_model_show
	local cur_round = self:GetZhanLingCurRound()
	if IsEmptyTable(cfg) then
		return nil
	end

	for k, v in ipairs(cfg) do
		if v.cycle_id == cur_round then
			return v
		end
	end

	return nil
end

--获取id
function ZhanLingWGData:GetActViewShowModelCfgId()
	local cfg = self.zhanling_cfg.buy_model_show
	local cur_round = self:GetZhanLingCurRound()
	if IsEmptyTable(cfg) then
		return nil
	end

	for k, v in ipairs(cfg) do
		if v.cycle_id == cur_round then
			return v.cycle_id
		end
	end

	return nil
end

------------------------------  购买战令   --------------------------------------
-- 强行提前排序显示
function ZhanLingWGData:GetSpeicalItemSortWeight(round, item_id)
	local cfg = self.special_item_sort_cfg and self.special_item_sort_cfg[round]
				and self.special_item_sort_cfg[round][item_id]
	return cfg and cfg.weight or 0
end

-- 统计并排序所有奖励
function ZhanLingWGData:GetAllRewardList()
	local cur_round = self:GetZhanLingCurRound()
	if not self.zhanling_all_reward_list or self.old_all_reward_round ~= cur_round then
		self.zhanling_all_reward_list = {}
		self.old_all_reward_round = cur_round
		local cfg = self:GetCurLevelCfg()
		if IsEmptyTable(cfg) then
			return self.zhanling_all_reward_list
		end
		
		local temp_list = {}
		for k, v in ipairs(cfg) do
			for i_1, d_1 in pairs(v.reward_item_1) do --白银奖励
				temp_list[d_1.item_id] = temp_list[d_1.item_id] or {}
				temp_list[d_1.item_id][d_1.is_bind] = temp_list[d_1.item_id][d_1.is_bind] or 0
				temp_list[d_1.item_id][d_1.is_bind] = temp_list[d_1.item_id][d_1.is_bind] + d_1.num
			end

			for i_2, d_2 in pairs(v.reward_item_2) do --黄金奖励
				temp_list[d_2.item_id] = temp_list[d_2.item_id] or {}
				temp_list[d_2.item_id][d_2.is_bind] = temp_list[d_2.item_id][d_2.is_bind] or 0
				temp_list[d_2.item_id][d_2.is_bind] = temp_list[d_2.item_id][d_2.is_bind] + d_2.num
			end
		end

		for k, v in pairs(temp_list) do
			for i, j in pairs(v) do
				local data = {}
				local item_cfg = ItemWGData.Instance:GetItemConfig(k)
				if item_cfg then
					data.item_id = k
					data.is_bind = i
					data.num = j
					-- 配置有 不需要考虑品质，按权重排
					local weight = self:GetSpeicalItemSortWeight(cur_round, k)
					data.sort = weight > 0 and weight or item_cfg.color
					table.insert(self.zhanling_all_reward_list, data)
				end
			end
		end

		if not IsEmptyTable(self.zhanling_all_reward_list) then
			table.sort(self.zhanling_all_reward_list, SortTools.KeyUpperSorter("sort"))
		end
	end

	return self.zhanling_all_reward_list
end

-- 统计并排序满级后的奖励
function ZhanLingWGData:GetAfterMaxLevelRewardList()
	local cur_round = self:GetZhanLingCurRound()
	if not self.after_maxlevel_reward_list or self.old_maxlevel_reward_round ~= cur_round then
		self.after_maxlevel_reward_list = {}
		self.old_maxlevel_reward_round = cur_round
		local cfg = self:GetZhanLingOtherCfg()
		if IsEmptyTable(cfg) or IsEmptyTable(cfg.reward_item_1) or IsEmptyTable(cfg.reward_item_2) then
			return self.after_maxlevel_reward_list
		end

		local temp_list = {}
		for i_1, d_1 in pairs(cfg.reward_item_1) do
			temp_list[d_1.item_id] = temp_list[d_1.item_id] or {}
			temp_list[d_1.item_id][d_1.is_bind] = temp_list[d_1.item_id][d_1.is_bind] or 0
			temp_list[d_1.item_id][d_1.is_bind] = temp_list[d_1.item_id][d_1.is_bind] + d_1.num
		end

		for i_2, d_2 in pairs(cfg.reward_item_2) do
			temp_list[d_2.item_id] = temp_list[d_2.item_id] or {}
			temp_list[d_2.item_id][d_2.is_bind] = temp_list[d_2.item_id][d_2.is_bind] or 0
			temp_list[d_2.item_id][d_2.is_bind] = temp_list[d_2.item_id][d_2.is_bind] + d_2.num
		end

		for k, v in pairs(temp_list) do
			for i, j in pairs(v) do
				local data = {}
				local item_cfg = ItemWGData.Instance:GetItemConfig(k)
				if item_cfg then
					data.item_id = k
					data.is_bind = i
					data.num = j
					data.sort = item_cfg.color
					table.insert(self.after_maxlevel_reward_list, data)
				end
			end
		end

		if not IsEmptyTable(self.after_maxlevel_reward_list) then
			table.sort(self.after_maxlevel_reward_list, SortTools.KeyUpperSorter("sort"))
		end
	end

	return self.after_maxlevel_reward_list
end
------------------------------  购买战令end -------------------------------------

------------------------------  购买等级   --------------------------------------
function ZhanLingWGData:SetZhanLingLevelBuyInfo(protocol)
	self.level_buy_count_list = protocol.level_buy_count_list
end

-- 计算每档不同购买次数的折扣
function ZhanLingWGData:GetLevelBuyTypeDisCount()
	if not self.level_buy_discount_list then
		self.level_buy_discount_list = {}
		local cfg = self.zhanling_cfg.buy_exp
		if IsEmptyTable(cfg) then
			return self.level_buy_discount_list
		end

		local function list_tonumber(list)
			if IsEmptyTable(list) then
				return {}
			end

			for k, v in pairs(list) do
				list[k] = tonumber(v)
			end

			return list
		end

		for k, v in ipairs(cfg) do
			local discount_count_list = Split(v.discount_count, "|")
			local discount_value_list = Split(v.discount_value, "|")
			discount_count_list = list_tonumber(discount_count_list)
			discount_value_list = list_tonumber(discount_value_list)
			local max_count = discount_count_list[#discount_count_list] or 0
			local temp_list = {}
			for i = 1, #discount_count_list do
				local start_count = discount_count_list[i - 1] or -1
				start_count = start_count + 1
				for start = start_count, discount_count_list[i] do
					local discount = discount_value_list[i] or 10
					temp_list[start] = discount / 10
				end
			end

			self.level_buy_discount_list[v.id] = temp_list
		end
	end

	return self.level_buy_discount_list
end

function ZhanLingWGData:GetLevelBuyTypeList()
	local buy_level_list = {}
	local buy_exp_cfg = self.zhanling_cfg.buy_exp
	if IsEmptyTable(buy_exp_cfg) then
		return buy_level_list
	end

	local function calc_reward_num(item_list)
		local num = 0
		if IsEmptyTable(item_list) then
			return num
		end

		for k, v in pairs(item_list) do
			num = num + 1
		end
		return num
	end

	local function calc_level(add_exp)
		add_exp = add_exp or 0
		local cur_level = self.zhanling_info.level
		local act_high = self:GetIsActHighZhanLing()
		local level_cfg = self:GetCurLevelCfg()
		local max_level = self:GetCurRewardMaxLevel()
		local other_cfg = self:GetZhanLingOtherCfg()

		local add_level = 0
		local can_get_reward_num = 0
		local add_to_level = cur_level
		if IsEmptyTable(level_cfg) then
			return add_level, add_to_level, can_get_reward_num
		end

		local temp_list = {}
		for i = cur_level, max_level - 1 do
			local cfg = level_cfg[i + 1]
			add_exp = add_exp - cfg.up_level_exp
			if add_exp >= 0 then
				add_level = add_level + 1
				local nor_num = calc_reward_num(cfg.reward_item_1)
				local high_num = calc_reward_num(cfg.reward_item_2)
				local cur_num = act_high and (nor_num + high_num) or nor_num
				can_get_reward_num = can_get_reward_num + cur_num
			else
				break
			end
		end

		-- 溢出后
		if add_exp > 0 and other_cfg then
			local after_max_exp = other_cfg.fix_up_level_exp
			local after_add_level = math.floor(add_exp / after_max_exp)
			local nor_num = calc_reward_num(other_cfg.reward_item_1)
			local high_num = calc_reward_num(other_cfg.reward_item_2)
			local cur_num = act_high and (nor_num + high_num) or nor_num
			add_level = add_level + after_add_level
			can_get_reward_num = can_get_reward_num + cur_num * after_add_level
		end

		add_to_level = cur_level + add_level
		return add_level, add_to_level, can_get_reward_num
	end

	local min_discount = 1
	local tuijian_flag = #buy_exp_cfg
	for k, v in ipairs(buy_exp_cfg) do
		local cur_has_buy_count = self.level_buy_count_list[v.id] or 0
		local discount_list = self:GetLevelBuyTypeDisCount()[v.id]
		local cur_discount = discount_list and discount_list[cur_has_buy_count] or 1
		local add_level, add_to_level, can_get_reward_num = calc_level(v.exp)
		local data = {level_id = v.id, add_level = add_level, add_to_level = add_to_level, can_get_reward_num = can_get_reward_num}
		data.old_price = v.cost
		data.cur_discount = cur_discount
		data.now_price = v.cost * cur_discount
		data.has_buy_count = cur_has_buy_count
		data.limit_count = v.limit_count
		data.is_tuijian = false
		if cur_discount <= min_discount
		 	and ((v.limit_count > 0 and cur_has_buy_count < v.limit_count) or v.limit_count <= 0) then
			min_discount = cur_discount
			tuijian_flag = v.id
		end

		table.insert(buy_level_list, data)
	end

	buy_level_list[tuijian_flag].is_tuijian = true

	return buy_level_list
end


------------------------------  购买等级end -------------------------------------


---------------------------------  战令商城  ------------------------------------
function ZhanLingWGData:SetLimitExchangeItemInfo(protocol)
	self.zhanling_coin_num = protocol.zhanling_coin_num
	self.round_limit_list = protocol.round_limit_list
	self.daily_limit_list = protocol.daily_limit_list
end

function ZhanLingWGData:GetLimitExchangeItemInfo(type, shop_seq)
	if type == ZhanLingShowType.DailyLimit then
		return self.daily_limit_list[shop_seq] or 0
	elseif type == ZhanLingShowType.RoundLimit then
		return self.round_limit_list[shop_seq] or 0
	end

	return 0
end

function ZhanLingWGData:GetShopExchangeList()
	local cur_round = self:GetZhanLingCurRound()
	return self.shop_exchange_cfg[cur_round] or {}
end

--获取商店物品配置
function ZhanLingWGData:GetShopShowList()
	local big_data
	local exchange_list = {}

	local model_num = 0
	for k,v in ipairs(self:GetShopExchangeList()) do
		if v.model_id > 0 then
			model_num = model_num + 1
			big_data = v
		else
			table.insert(exchange_list, v)
		end
	end

	if model_num > 1 then
		print_error("战令商店模型奖励有问题")
	end

	return big_data, exchange_list
end

function ZhanLingWGData:GetExchangeItemCfg()
	local other_cfg = self:GetZhanLingOtherCfg()
	local exchange_item_id = other_cfg and other_cfg.reward_item_id or 0
	local item_cfg = ItemWGData.Instance:GetItemConfig(exchange_item_id)
	return item_cfg
end

function ZhanLingWGData:GetExchangeItemNum()
	return self.zhanling_coin_num or 0
end

function ZhanLingWGData:GetZhanLingExChangeRemind()
	local shop_exchange_list = self:GetShopExchangeList()
	local had_num = self:GetExchangeItemNum()

	for k,v in pairs(shop_exchange_list) do
		if(had_num >= v.item_price)then
			if self:GetExChangeOpenFlag() then
				return 0
			else
				return 1
			end
		end
	end
	return 0
end

function ZhanLingWGData:GetExChangeOpenFlag()
	return self.exchange_open_flag
end

function ZhanLingWGData:SetExChangeOpenFlag()
	if not self.exchange_open_flag then
		self.exchange_open_flag = true
		RemindManager.Instance:Fire(RemindName.ZhanLing_ExChange)
	end
end

------------------------------  战令商城end  ------------------------------------

---------------------------------  战令任务  ------------------------------------
function ZhanLingWGData:SetZhanLingTaskInfo(protocol)
	self.week_task_list = protocol.week_task_list
	self.daily_task_list = protocol.daily_task_list
end

-- 根据类型获取任务列表
function ZhanLingWGData:GetZhanLingTaskInfo(type)
	local function get_task_list(task_list, task_class)
		local temp_list = {}
		local weight_list = {[0] = 1000, 100, 10000}
		if IsEmptyTable(task_list) then
			return temp_list
		end

		for k, v in ipairs(task_list) do
			local cfg = self.task_cfg_map[v.task_id]
			if cfg then
				local data = {}
				local sort_weight = weight_list[v.task_state] or 1
				data.task_id = cfg.task_id
				data.task_type = cfg.task_type
				data.task_class = task_class
				data.task_exp = cfg.task_exp
				data.difficult = cfg.difficult
				data.jump_view = cfg.jump_view
				data.task_state = v.task_state
				data.sort = sort_weight + v.index

				local desc = ""
				if cfg.task_type == ZhanLingTaskType.BATTLE_LINGPAI_TASK_TYPE_USE_TREASURE_MAP then
					local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.param2)
					local color = item_cfg and item_cfg.color or 0
					local str_color = ITEM_COLOR[color]
					-- local str1 = Language.Common.ColorName[color]
					local str1 = Language.ZhanLing.CBTQuality[color] or ""
					local str2 = ToColorStr(v.process .. "/" .. cfg.param1, COLOR3B.C8)
					desc = string.format(cfg.task_des, ToColorStr(str1, str_color), str2)
				else
					local str = ToColorStr(v.process .. "/" .. cfg.param1, COLOR3B.C8)
					desc = string.format(cfg.task_des, str)
				end

				data.task_des = desc
				table.insert(temp_list, data)
			end
		end

		if not IsEmptyTable(temp_list) then
			table.sort(temp_list, SortTools.KeyLowerSorter("sort"))
		end

		return temp_list
	end

	local task_list = {}
	if type == ZhanLingTaskClass.Daily then
		task_list = get_task_list(self.daily_task_list, type)
	elseif type == ZhanLingTaskClass.Week then
		task_list = get_task_list(self.week_task_list, type)
	end

	return task_list
end

function ZhanLingWGData:GetZhanLingTaskRemind(type)
	local list = self:GetZhanLingTaskInfo(type)
	if IsEmptyTable(list) then
		return false
	end

	for k,v in pairs(list) do
		if v.task_state == REWARD_STATE_TYPE.CAN_FETCH then
			return true
		end
	end

	return false
end

function ZhanLingWGData:GetZhanLingTaskAllRemind()
	for type = 1, ZhanLingTaskClass.Week do
		if self:GetZhanLingTaskRemind(type) then
			return 1
		end
	end

	return 0
end

function ZhanLingWGData:SetRefreshTaskID(task_id)
	self.old_task_id = task_id
end

function ZhanLingWGData:GetRefreshTaskID()
	return self.old_task_id
end