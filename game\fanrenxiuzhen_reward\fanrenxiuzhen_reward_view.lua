-- 登神豪礼
-- 此功能已屏蔽，和另一个奖励合并在FanRenXiuZhenRewardPreview显示
FanrenxiuzhenRewardView = FanrenxiuzhenRewardView or BaseClass(SafeBaseView)

function FanrenxiuzhenRewardView:__init()
	self.view_style = ViewStyle.Half
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(true)

	self:AddViewResource(0, "uis/view/fanrenxiuzhen_reward_ui_prefab", "fanrenxiuzhen_reward_view")
end

function FanrenxiuzhenRewardView:__delete()

end

function FanrenxiuzhenRewardView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_close"], BindTool.Bind(self.Close, self))

	-- 奖励预览列表
	self.reward_list = AsyncListView.New(FanrenxiuzhenRewardItem, self.node_list["reward_list"])
    self.reward_list:SetStartZeroIndex(true)
end

function FanrenxiuzhenRewardView:ReleaseCallBack()

	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

end



function FanrenxiuzhenRewardView:OnFlush(param_t, index)
	for k,v in pairs(param_t) do
		if "all" == k then
            self:FlushReward()
		end
	end
end

function FanrenxiuzhenRewardView:FlushReward()
    local data_list = FuBenPanelWGData.Instance:GetFanrenxiuzhenLevelRwawrdData()
    self.reward_list:SetDataList(data_list)
end


-----------------------------------------------

FanrenxiuzhenRewardItem = FanrenxiuzhenRewardItem or BaseClass(BaseRender)

function FanrenxiuzhenRewardItem:LoadCallBack()
    if self.model_display == nil then
        self.model_display = OperationActRender.New(self.node_list["model_pos"])
    end

	XUI.AddClickEventListener(self.node_list["btn_receive"], BindTool.Bind(self.OnCliekReceive, self))
	XUI.AddClickEventListener(self.node_list["btn_open_item"], BindTool.Bind(self.OnCliekOpenItem, self))

    
end

function FanrenxiuzhenRewardItem:__delete()
    self:CleanTimer()
    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end
end

function FanrenxiuzhenRewardItem:OnFlush()


    local pass_level = FuBenPanelWGData.Instance:GetPassLevel()
    local is_receive = FuBenPanelWGData.Instance:GetLevelRewardFlag(self.data.seq)
    
    local is_pass = pass_level >= self.data.level

    local text_level = string.format(Language.FanRenXiuZhen.RewardLevelStr, self.data.level)
    self.node_list.text_level.text.text = (is_pass and not is_receive) and ToColorStr(text_level, "#fff799") or text_level
    
    self.node_list.btn_receive:CustomSetActive(is_pass and not is_receive)
    self.node_list.img_select:CustomSetActive(is_pass and not is_receive)
    self.node_list.img_state:CustomSetActive(is_pass and is_receive)
    self.node_list.img_mask:CustomSetActive(is_pass and is_receive)

    self.node_list.img_bg:CustomSetActive(not (is_pass and not is_receive))

    self:FlushModel()
end

function FanrenxiuzhenRewardItem:OnCliekReceive()
    if self.data then
        FuBenPanelWGCtrl.Instance:SendPataFbRewrad(self.data.seq)
    end

end

function FanrenxiuzhenRewardItem:OnCliekOpenItem()
    if self.data then
        TipWGCtrl.Instance:OpenItem({item_id = self.data.model_show_itemid})
    end

end

function FanrenxiuzhenRewardItem:CleanTimer()
    if self.delay_timer then
        GlobalTimerQuest:CancelQuest(self.delay_timer)
        self.delay_timer = nil
    end
end


function FanrenxiuzhenRewardItem:PlayWeaponAction()
    if self.model_display then
        self.model_display:PlayWeaponAction()
    end
end

function FanrenxiuzhenRewardItem:FlushModel()
    local show_model_data = self.data
    if show_model_data then
        local old_data = self.model_display:GetData()
        if old_data and old_data.item_id == show_model_data.model_show_itemid then
            return
        end

        local data = {}
        data.model_rt_type = ModelRTSCaleType.S
        data.item_id = show_model_data.model_show_itemid
        data.render_type = show_model_data.model_show_type - 1
        data.hide_model_block = true
        data.skip_rest_action = true
        data.can_drag = false

        if show_model_data.display_pos ~= nil and show_model_data.display_pos ~= "" then
            local pos = Split(show_model_data.display_pos, "|")
            RectTransform.SetAnchoredPositionXY(self.node_list.model_pos.rect, tonumber(pos[1]) or 0, tonumber(pos[2]) or 0)
            -- data.model_adjust_root_local_position = Vector3(tonumber(pos[1]), tonumber(pos[2]), tonumber(pos[3]))
        end

        if show_model_data.display_rotation and show_model_data.display_rotation ~= "" then
            local rot_list = string.split(show_model_data.display_rotation, "|")
            local rot_x = tonumber(rot_list[1]) or 0
            local rot_y = tonumber(rot_list[2]) or 0
            local rot_z = tonumber(rot_list[3]) or 0
            --display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
            data.role_rotation = Vector3(rot_x, rot_y, rot_z)
        end

        if show_model_data.display_scale ~= nil and show_model_data.display_scale ~= "" then
            local scale = show_model_data.display_scale
            data.model_adjust_root_local_scale = scale
        end

        self.model_display:ActModelPlayLastAction()
        self.model_display:SetData(data)

        self:CleanTimer()

        local item_cfg = ItemWGData.Instance:GetItemConfig(show_model_data.model_show_itemid)
        local display_type = item_cfg and item_cfg.is_display_role or 0
        if display_type == DisplayItemTip.Display_type.SHENBING or display_type == DisplayItemTip.Display_type.WUQI then
            self.delay_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.PlayWeaponAction, self), 0.1)
        end
    end
end
