GuildShowHuRankView = GuildShowHuRankView or BaseClass(SafeBaseView)

function GuildShowHuRankView:__init()
    self.view_layer = UiLayer.MainUILow
    self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
    self:AddViewResource(0, "uis/view/fubenpanel_prefab", "guild_shouhu_rank")
    self.view_cache_time = 1
    self.active_close = false
    self.is_safe_area_adapter = true
end

function GuildShowHuRankView:ReleaseCallBack()
    if nil ~= self.rank_list then
        self.rank_list:DeleteMe()
        self.rank_list = nil
    end

    if nil ~= self.shrinkbuttons_change then
        GlobalEventSystem:UnBind(self.shrinkbuttons_change)
        self.shrinkbuttons_change = nil
    end

    if nil ~= self.main_menu_icon_change then
        GlobalEventSystem:UnBind(self.main_menu_icon_change)
        self.main_menu_icon_change = nil
    end
end

function GuildShowHuRankView:LoadCallBack()
    self.rank_list = AsyncListView.New(GuildShowHuRankItemRender, self.node_list["rank_list"])
    self.shrinkbuttons_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind1(self.ShrinkButtonsValueChange, self))
    self.main_menu_icon_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_MENU_ICON_CHANGE, BindTool.Bind1(self.MainMenuIconChangeEvent, self))

end

function GuildShowHuRankView:ShrinkButtonsValueChange(isOn)
    if not self.node_list or not self.node_list.guild_shouhu_rank_root then
        return
    end
    local menu_ison = MainuiWGCtrl.Instance.view:GetMenuButtonIsOn()
    self.node_list.guild_shouhu_rank_root:SetActive(not isOn and not menu_ison)
end

function GuildShowHuRankView:MainMenuIconChangeEvent(isOn)
    if not self.node_list or not self.node_list.guild_shouhu_rank_root then
        return
    end
    self.node_list.guild_shouhu_rank_root:SetActive(not isOn)
end

function GuildShowHuRankView:ShowIndexCallBack()
    self:Flush()
end

function GuildShowHuRankView:OnFlush()
    local data_list = GuildWGData.Instance:GetGuildShouHuInFBRankInfo()
    self.rank_list:SetDataList(data_list, 3)

    local vo = GameVoManager.Instance:GetMainRoleVo()
    if not vo or vo.guild_id == 0 then
        return
    end

    --自己排名
    local my_guild_cur_rank = 0
    local my_guild_data = nil
    for i, v in ipairs(data_list) do
        if vo.guild_id == v.guild_id then
            my_guild_cur_rank = i
            my_guild_data = v
        end
    end
    self.node_list.my_guild_rank.text.text = my_guild_cur_rank == 0 and Language.Guild.SHNotRank or string.format(Language.Guild.SHJieSuanRankStr, my_guild_cur_rank)

    --当前关
    local fb_data = GuildWGData.Instance:GetGuildFBData()
    if not fb_data then
        return
    end
    local is_pass = fb_data.pass_time > 0
    if is_pass then
        self.node_list.my_guild_pass_str.text.text = TimeUtil.MSTime(fb_data.pass_time or 0)      -- 通关时间
    else
        self.node_list.my_guild_pass_str.text.text = string.format(Language.Guild.SHWaveStr, fb_data.curr_wave and fb_data.curr_wave + 1 or 1) --波数从0开始，没数据默认第1关
    end
end

GuildShowHuRankItemRender = GuildShowHuRankItemRender or BaseClass(BaseRender)

function GuildShowHuRankItemRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list.rank_text.text.text = self.index >= 4 and self.index or ""
    local bg_bundle, bg_asset = ResPath.GetCommonImages("a3_hurt_list_bg_4")

    if self.index < 4 then    
        bg_bundle, bg_asset = ResPath.GetCommonImages("a3_hurt_list_bg_" .. self.index)
		self.node_list["rank_img"]:SetActive(true)
		self.node_list["rank_img"].image:LoadSprite(ResPath.GetCommonImages("a3_hurt_list_rank_" .. self.index))
	else
		self.node_list["rank_img"]:SetActive(false)
    end

    self.node_list["bg"].image:LoadSprite(bg_bundle, bg_asset)
    self.node_list.guild_name.text.text = self.data.guild_name
    local is_pass = self.data.pass_time > 0
    if is_pass then
        self.node_list.pass_str.text.text = TimeUtil.MSTime(self.data.pass_time or 0)
    else
        self.node_list.pass_str.text.text = string.format(Language.Guild.SHWaveStr, self.data.wave and self.data.wave + 1 or 1)
    end

    -- local flag = self.index <= 3
    -- self.node_list.rank_img:SetActive(flag)
    -- self.node_list.rank_text:SetActive(not flag)
    -- if flag then
    --     --local b,a = ResPath.GetCommonImages("icon_paiming" .. self.index)
    --     local b,a = ResPath.GetCommonIcon("a3_tb_jp" .. self.index)
	-- 	self.node_list.rank_img.image:LoadSprite(b, a, function()
    --         XUI.ImageSetNativeSize(self.node_list.rank_img)
	-- 	end)
    -- else
    --     self.node_list.rank_text.text.text = self.index
    -- end

    -- self.node_list.guild_name.text.text = self.data.guild_name
    -- local is_pass = self.data.pass_time > 0
    -- if is_pass then
    --     self.node_list.pass_str.text.text = TimeUtil.MSTime(self.data.pass_time or 0)
    -- else
    --     self.node_list.pass_str.text.text = string.format(Language.Guild.SHWaveStr, self.data.wave and self.data.wave + 1 or 1)
    -- end
end