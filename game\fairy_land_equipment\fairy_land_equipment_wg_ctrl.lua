require("game/fairy_land_equipment/fairy_land_equipment_enum")
require("game/fairy_land_equipment/fairy_land_equipment_item")
require("game/fairy_land_equipment/fairy_land_equipment_wg_data")
require("game/fairy_land_equipment/fairy_land_equipment_tabbar")
require("game/fairy_land_equipment/fairy_land_god_body")
require("game/fairy_land_equipment/fairy_land_equipment_view")
require("game/fairy_land_equipment/fairy_land_plane_view")
require("game/fairy_land_equipment/fairy_land_plane_detail_view")
require("game/fairy_land_equipment/fairy_land_rule_view")
-- god_body
require("game/fairy_land_equipment/god_body/fairy_land_god_body_wg_data")
require("game/fairy_land_equipment/god_body/fairy_land_god_book_view")
require("game/fairy_land_equipment/god_body/fairy_land_god_body_view")
require("game/fairy_land_equipment/god_body/fairy_land_god_book_attr_tips")
require("game/fairy_land_equipment/god_body/fairy_land_god_body_right_view")
require("game/fairy_land_equipment/god_body/fairy_land_god_book_act_view")
require("game/fairy_land_equipment/god_body/fairy_land_god_body_act_view")
-- holy_equip
require("game/fairy_land_equipment/holy_equip/fairy_land_holy_equip_wg_data")
require("game/fairy_land_equipment/holy_equip/fairy_land_holy_equip_view")
-- forge
require("game/fairy_land_equipment/forge/fairy_land_evolve_wg_data")
require("game/fairy_land_equipment/forge/fairy_land_strengthen_wg_data")
require("game/fairy_land_equipment/forge/fairy_land_upquality_wg_data")
require("game/fairy_land_equipment/forge/evolve_pop_equip_bag")
require("game/fairy_land_equipment/forge/fairy_land_strengthen_view")
require("game/fairy_land_equipment/forge/fairy_land_strength_master_view")
require("game/fairy_land_equipment/forge/fairy_land_evolve_view")
require("game/fairy_land_equipment/forge/fairy_land_upquality_view")
require("game/fairy_land_equipment/forge/evolve_star_master_view")
-- require("game/fairy_land_equipment/god_body/god_body_dujie_view")

FairyLandEquipmentWGCtrl = FairyLandEquipmentWGCtrl or BaseClass(BaseWGCtrl)
function FairyLandEquipmentWGCtrl:__init()
	if FairyLandEquipmentWGCtrl.Instance then
		error("[FairyLandEquipmentWGCtrl]:Attempt to create singleton twice!")
	end
	FairyLandEquipmentWGCtrl.Instance = self

    self.data = FairyLandEquipmentWGData.New()
	self.view = FairyLandPlaneView.New(GuideModuleName.FairyLandPlaneView)
	self.plane_detail_view = FairyLandPlaneDetailView.New()
	self.rule_view = FairyLandRuleView.New(GuideModuleName.FairyLandRuleView)
    self.equipment_view = FairyLandEquipmentView.New(GuideModuleName.FairyLandEquipmentView)
	self.gb_attr_tips = FairyLandGodBookAttrTips.New()
	self.gb_right_view = FairyLandGodBodyRightView.New()
	-- self.god_body_dujie_view = GodBodyDuJieView.New(GuideModuleName.GodBodyDuJieView)
	self.fle_strength_master_view = FairyLandStrengthenMasterView.New()
	self.evolve_pop_equip_bag = EvolvePopEquipBag.New(GuideModuleName.EvolvePopEquipBag)
	self.evolve_star_master_view = EvolveStarMasterView.New(GuideModuleName.EvolveStarMasterView)
	self.god_book_act_view = FairyLandGodBookActView.New()
	self.god_body_act_view = FairyLandGodBodyActView.New()


	self.old_remind_uplevel = false
	self.old_remind_uplevel_cd = 0

    self:RegisterAllProtocols()
    self:RegisterAllEvents()
end

function FairyLandEquipmentWGCtrl:__delete()
    self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	self.plane_detail_view:DeleteMe()
	self.plane_detail_view = nil

	self.rule_view:DeleteMe()
	self.rule_view = nil

	self.equipment_view:DeleteMe()
	self.equipment_view = nil

	self.gb_attr_tips:DeleteMe()
	self.gb_attr_tips = nil

	self.gb_right_view:DeleteMe()
	self.gb_right_view = nil

	self.evolve_pop_equip_bag:DeleteMe()
	self.evolve_pop_equip_bag = nil

	self.evolve_star_master_view:DeleteMe()
	self.evolve_star_master_view = nil

	self.fle_strength_master_view:DeleteMe()
	self.fle_strength_master_view = nil

	-- self.god_body_dujie_view:DeleteMe()
	-- self.god_body_dujie_view = nil

	self.god_book_act_view:DeleteMe()
	self.god_book_act_view = nil

	self.god_body_act_view:DeleteMe()
	self.god_body_act_view = nil

	if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end

	self:CancelRemindGodBodyUplevelTime()
	self.old_remind_uplevel = nil
	self.old_remind_uplevel_cd = nil
    FairyLandEquipmentWGCtrl.Instance = nil
end

function FairyLandEquipmentWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSXianJieEquipReq)
	self:RegisterProtocol(SCXianJieEquipOperateResult, "OnSCXianJieEquipOperateResult")
	self:RegisterProtocol(SCXianJieRightBuyInfo, "OnSCXianJieRightBuyInfo")					-- 8722 渡劫特权购买信息
	self:RegisterProtocol(SCXianJieEquipSlotInfo, "OnSCXianJieEquipSlotInfo")				-- 8723 神体总信息
	self:RegisterProtocol(SCXianJieEquipSlotInfoUpdate, "OnSCXianJieEquipSlotInfoUpdate")	-- 8724 神体单个信息
	self:RegisterProtocol(SCXianjieEquipBagInfo, "OnSCXianjieEquipBagInfo")					-- 8725 圣装背包信息
	self:RegisterProtocol(SCXianjieEquipBagChangeInfo, "OnSCXianjieEquipBagChangeInfo")		-- 8726 圣装背包单个变化
	self:RegisterProtocol(SCXianJieEquipShenZhuangInfo, "OnSCXianJieEquipShenZhuangInfo")	-- 8727 圣装穿戴信息
	self:RegisterProtocol(SCXianJieEquipShenZhuangPartInfo, "OnSCXianJieEquipShenZhuangPartInfo")	-- 8721 单个穿戴信息变化
	self:RegisterProtocol(SCXianJieEquipShenZhuangInfoChange, "OnSCXianJieEquipShenZhuangInfoChange")
	self:RegisterProtocol(CSXianjieEquipShenZhuangUpGrade)
end

function FairyLandEquipmentWGCtrl:RegisterAllEvents()
	self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)
end

-- 物品变化
function FairyLandEquipmentWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		if self.data:GetIsEvolveRemindItem(change_item_id) then
			self:FlushEquipMentView(TabIndex.fl_eq_forge_evolve)
		elseif self.data:GetIsUpqualityRemindItem(change_item_id) then
			self:FlushEquipMentView(TabIndex.fl_eq_forge_upquality)
		elseif self.data:GetIsStrengthenRemindItem(change_item_id) then
			self:FlushEquipMentView(TabIndex.fl_eq_forge_strengthen)
		elseif self.data:IsGodBodyUpgradeStuff(change_item_id) then
			self:FlushEquipMentView(TabIndex.fairy_land_eq_god_body)
		end
	end
end

-- 圣装物品变化
function FairyLandEquipmentWGCtrl:OnHolyEquipItemDataChange(change_data, change_item_index, change_reason, old_num, new_num)
	local change_item_id = change_data.item_id
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then

		local item_cfg = ItemWGData.Instance:GetItemConfig(change_item_id)
		if item_cfg then
			-- 新获得提示
			local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.SysRemind.AddItem, item_name, new_num - old_num))

			-- 快速使用
			self:AddKeyUse(change_data)
		end
	end
end

-- 快速使用
function FairyLandEquipmentWGCtrl:AddKeyUse(data)
	if data == nil then
		return
	end

	if not self.data:HolyEquipIsBetterWear(data) then
		return
	end

	FunctionGuide.Instance:OpenHolyEquipKeyUseView(data)
end

function FairyLandEquipmentWGCtrl:GetViewShowIndex()
	if self.equipment_view:IsOpen() then
		return self.equipment_view.show_index
	end

	return 0
end

function FairyLandEquipmentWGCtrl:GetViewSelectSlotIndex()
	if self.equipment_view:IsOpen() then
		return self.equipment_view:GetCurSelectSlot()
	end

	return 0
end

function FairyLandEquipmentWGCtrl:FlushEquipMentView(index, key, param_t)
	if self.equipment_view:IsOpen() then
		self.equipment_view:Flush(index, key, param_t)
	end
end

function FairyLandEquipmentWGCtrl:OpenView(index, key, param_t)
	ViewManager.Instance:Open(GuideModuleName.FairyLandEquipmentView, index, key, param_t)
end

function FairyLandEquipmentWGCtrl:OpenEquipmentView()
    self.equipment_view:Open(0)
end

function FairyLandEquipmentWGCtrl:OpenPlaneDetailViewAndSetData(index)
	if self.plane_detail_view:IsOpen() then
        self.plane_detail_view:Flush()
    else
		self.plane_detail_view:SetPlaneIndex(index)
        self.plane_detail_view:Open()
    end
end

function FairyLandEquipmentWGCtrl:OpenRuleViewAndSetData(index)
	self.rule_view:SetPlaneIndex(index)
	self.rule_view:Open()
end

function FairyLandEquipmentWGCtrl:OpenRuleViewByParam(index, key, param_t)
	ViewManager.Instance:Open(GuideModuleName.FairyLandRuleView, index, key, param_t)
end

--刷新法则界面
function FairyLandEquipmentWGCtrl:FlushRuleView()
	if self.rule_view:IsOpen() then
        self.rule_view:Flush()
    end
end

--恢复法则界面
function FairyLandEquipmentWGCtrl:RestoreRuleView()
	if self.rule_view:IsOpen() then
        self.rule_view:RestoreView()
    end
end

--刷新位面界面
function FairyLandEquipmentWGCtrl:FlushView()
	if self.view:IsOpen() then
        self.view:Flush()
    end
end

--打开位面界面
function FairyLandEquipmentWGCtrl:OpenPlaneView()
	self.view:Open()
end

-- 操作请求
function FairyLandEquipmentWGCtrl:SendOperateReq(operate_type, param_0, param_1, param_2)
	-- print_error("【-----操作请求----】：", operate_type, param_0, param_1, param_2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSXianJieEquipReq)
	protocol.operate_type = operate_type or 0
	protocol.param_0 = param_0 or 0
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol:EncodeAndSend()
end

-- 操作结果返回
function FairyLandEquipmentWGCtrl:OnSCXianJieEquipOperateResult(protocol)
	-- print_error("【-----操作结果返回----】：", protocol.operate_type, protocol.result,
									-- protocol.param1, protocol.param2, protocol.param3)
	local operate_type = protocol.operate_type
	local is_success = protocol.result == 1
	local effect_type = UIEffectName.s_shengji
	local show_effect = true

	if is_success then
		if operate_type == GB_OPERATE_RESULT_TYPE.PAGE_ACT then					-- 书页激活
			effect_type = UIEffectName.s_ningju
			-- self:OpenGodBookActView(protocol.param1, protocol.param2)
		elseif operate_type == GB_OPERATE_RESULT_TYPE.GOD_BODY_ACT then			-- 神体激活
			show_effect = false
			-- self:OpenGodBookActView(protocol.param1, self.data:GetActBodyNeedNum() - 1)
			self:OpenGodBodyActView(protocol.param1)
			self:RestoreRuleView()
		elseif operate_type == GB_OPERATE_RESULT_TYPE.GOD_BODY_UPLEVEL then		-- 神体升级
			effect_type = UIEffectName.s_shengji
		elseif operate_type == GB_OPERATE_RESULT_TYPE.GOD_BODY_UPGRADE then		-- 神体渡劫
			effect_type = UIEffectName.s_dujie
			if ViewManager.Instance:IsOpenByIndex(GuideModuleName.FairyLandEquipmentView, TabIndex.fairy_land_eq_god_body) then
				self.equipment_view:PlayDuJieEffect()
			end

			-- local data = {}
			-- data.slot = protocol.param1
			-- data.result = protocol.result
			-- self:OpenGodBodyDuJie(data)
		elseif operate_type == GB_OPERATE_RESULT_TYPE.EQUIP_UPLEVEL
			or operate_type == GB_OPERATE_RESULT_TYPE.ONE_KEY_STRENGTHEN then--圣装强化/一键强化
			effect_type = UIEffectName.s_qianghua

		elseif operate_type == GB_OPERATE_RESULT_TYPE.EQUIP_UPGRADE then		--圣装进化
			effect_type = UIEffectName.s_jinhua
		elseif operate_type == GB_OPERATE_RESULT_TYPE.EQUIP_UPCOLOR then		--圣装升品
			effect_type = UIEffectName.s_shengpin
		elseif operate_type == GB_OPERATE_RESULT_TYPE.EQUIP_TOTAL_LEVEL then	--圣装总等级
			show_effect = false
			effect_type = UIEffectName.s_jihuo
			if self.fle_strength_master_view:IsOpen() then
				self.fle_strength_master_view:ShowEffect(effect_type, is_success)
			end
		elseif operate_type == GB_OPERATE_RESULT_TYPE.EQUIP_TOTAL_STAR then		--圣装总星级
			show_effect = false
			effect_type = UIEffectName.s_jihuo
			if self.evolve_star_master_view:IsOpen() then
				self.evolve_star_master_view:ShowEffect(effect_type, is_success)
			end
		elseif operate_type == GB_OPERATE_RESULT_TYPE.BREAK_JING_MAI then
			show_effect = false
		end
	-- 失败
	else
		if operate_type == GB_OPERATE_RESULT_TYPE.GOD_BODY_UPGRADE then		-- 神体渡劫
			effect_type = UIEffectName.f_dujie
			-- local data = {}
			-- data.slot = protocol.param1
			-- data.result = protocol.result
			-- self:OpenGodBodyDuJie(data)
		elseif operate_type == GB_OPERATE_RESULT_TYPE.EQUIP_UPGRADE then		--圣装进化
			effect_type = UIEffectName.f_jinhua
			FairyLandEquipmentWGData.Instance:ClearEvolveSelectBagParam()
			self:FlushEquipMentView(TabIndex.fl_eq_forge_evolve)
			RemindManager.Instance:Fire(RemindName.FLEF_Evolve)
		else
			show_effect = false
		end
	end

	if show_effect and self.equipment_view:IsOpen() then
		self.equipment_view:ShowEffect(effect_type, is_success, operate_type)
	end
end

--============================================================================--
--================================【神体】=====================================--
-- 渡劫特权购买信息
function FairyLandEquipmentWGCtrl:OnSCXianJieRightBuyInfo(protocol)
	-- print_error("【-----渡劫特权购买信息----】：", protocol.uplevel_right_flag, protocol.upgrade_right_flag)
	self.data:SetGodBodyRightBuyInfo(protocol)
	if self.gb_right_view and self.gb_right_view:IsOpen() then
		self.gb_right_view:Flush()
	end

	self:FlushEquipMentView(TabIndex.fairy_land_eq_god_body, "flush_gb")
	self:RemindGodBodyUplevel()
end

-- 神体总信息
function FairyLandEquipmentWGCtrl:OnSCXianJieEquipSlotInfo(protocol)
	-- print_error("【-----神体总信息----】：")
	self.data:SetGodBodyAllInfo(protocol)
	self:FlushEquipMentView(nil, "flush_gb")
	self:FlushView()
	self:FlushRuleView()

    RemindManager.Instance:Fire(RemindName.FLE_GodBook)
	RemindManager.Instance:Fire(RemindName.FLE_GodBody)
    BossWGCtrl.Instance:OnXianJieEquipInfoReceive()
	self:RemindGodBodyUplevel()
	ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.xianjie_boss, "flushsolt")
end

-- 神体单个信息
function FairyLandEquipmentWGCtrl:OnSCXianJieEquipSlotInfoUpdate(protocol)
	-- print_error("【----神体单个信息-----】：", protocol.slot_index, protocol.slot_info)
	self.data:SetGodBodySingleInfo(protocol)
	self:FlushEquipMentView(nil, "flush_gb")
	self:FlushView()
	self:FlushRuleView()

    RemindManager.Instance:Fire(RemindName.FLE_GodBook)
	RemindManager.Instance:Fire(RemindName.FLE_GodBody)
    BossWGCtrl.Instance:FlushXianjieBossInfo()
	self:RemindGodBodyUplevel()
	ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.xianjie_boss, "flushsolt")
end

function FairyLandEquipmentWGCtrl:OpenGodBookAttrTips(slot)
	self.gb_attr_tips:SetDataAndOpen(slot)
end

function FairyLandEquipmentWGCtrl:OpenGodBodyRightView(right_type, slot)
	self.gb_right_view:SetDataAndOpen(right_type, slot)
end

-- function FairyLandEquipmentWGCtrl:OpenGodBodyDuJie(data)
-- 	self.god_body_dujie_view:SetData(data)
-- 	if self.god_body_dujie_view:IsOpen() and self.god_body_dujie_view:IsLoaded() then
-- 		self.god_body_dujie_view:ReadyDuJie()
-- 	else
-- 		self.god_body_dujie_view:Open()
-- 	end
-- end

function FairyLandEquipmentWGCtrl:OpenGodBodySkillTips(skill_data)
	if IsEmptyTable(skill_data) then
		return
	end

	local slot = skill_data.slot
	local grade = skill_data.grade
	local is_max = skill_data.is_max
	local is_act = skill_data.is_act
	local skill_index = skill_data.skill_index
	local skill_level = skill_data.skill_level
	local capability = self.data:GetGodBodySkillCapability(slot, skill_index, skill_level)
	local cur_skill_attr_list = self.data:GetGodBodySkillAttrTipsShow(slot, skill_index, skill_level)
	local cur_skill_desc = cur_skill_attr_list and table.concat(cur_skill_attr_list, "\n") or ""

	local limit_text, next_skill_desc, real_limit_text
	if not is_act or not is_max then
		local next_grade = skill_data.next_grade - 1
        next_grade = next_grade >= 0 and next_grade or 0
		local next_grade_cfg = self.data:GetGBUpGradeCfg(slot, next_grade)
		local need_level = next_grade_cfg and next_grade_cfg.up_max_level or 0
		local need_level_cfg = self.data:GetGBUpLevelCfg(slot, need_level + 1)
		if need_level_cfg then
			if not is_act then
				limit_text = string.format(Language.FairyLandEquipment.SkillActNeed, need_level_cfg.name)
			else
				limit_text = ""
				real_limit_text = string.format(Language.FairyLandEquipment.SkillUpLevelNeed, need_level_cfg.name)
			end
		end

		if is_act then
			local next_skill_attr_list = self.data:GetGodBodySkillAttrTipsShow(slot, skill_index, skill_level + 1)
			next_skill_desc = next_skill_attr_list and table.concat(next_skill_attr_list, "\n") or ""
		end
	end

	local show_data = {
		icon = skill_data.skill_icon,
		top_text = skill_data.skill_name,					-- 技能名
		top_text_color = COLOR3B.D_GREEN,
		skill_level = skill_level,
		body_text = cur_skill_desc,							-- 当前等级技能描述
		limit_text = limit_text ~= "" and limit_text or real_limit_text,
		hide_next = skill_data.hide_next,
		capability = capability,
		x = 0,
		y = 0,
		set_pos2 = true,
	}

	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

function FairyLandEquipmentWGCtrl:CancelRemindGodBodyUplevelTime()
	if self.remind_godbody_uplevel_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.remind_godbody_uplevel_timer)
	end

	self.remind_godbody_uplevel_timer = nil
end

-- 未购买特权计时刷新神体升级红点
function FairyLandEquipmentWGCtrl:RemindGodBodyUplevel()
	local is_need_remind, remind_end_time = self.data:GetGodBodyUplevelRemindCD()
	local remind_cd = remind_end_time - TimeWGCtrl.Instance:GetServerTime()

	if not is_need_remind or remind_cd <= 0 then
		self:CancelRemindGodBodyUplevelTime()
		self.old_remind_uplevel = false
		self.old_remind_uplevel_cd = 0
		return
	end

	if self.old_remind_uplevel ~= is_need_remind or self.old_remind_uplevel_cd ~= remind_cd then
		self:CancelRemindGodBodyUplevelTime()
		self.old_remind_uplevel = is_need_remind
		self.old_remind_uplevel_cd = remind_cd
		local error_value = 10	-- 误差
		self.remind_godbody_uplevel_timer = GlobalTimerQuest:AddDelayTimer(function()
			RemindManager.Instance:Fire(RemindName.FLE_GodBody)
		end, remind_cd + error_value)
	end
end

function FairyLandEquipmentWGCtrl:OpenGodBookActView(slot, page, only_look)
	self.god_book_act_view:SetDataAndOpen(slot, page, only_look)
end

function FairyLandEquipmentWGCtrl:OpenGodBodyActView(slot)
	self.god_body_act_view:SetDataAndOpen(slot)
end


--============================================================================--
--================================【圣装】=====================================--
-- 8725 圣装背包信息
function FairyLandEquipmentWGCtrl:OnSCXianjieEquipBagInfo(protocol)
	-- print_error("【-----圣装背包总信息----】：", protocol.bag_grid_list)
	self.data:SetHolyEquipBagInfo(protocol)
	self:FlushEquipMentView(TabIndex.fairy_land_eq_holy_equip, "flush_holy_equip")
	RemindManager.Instance:Fire(RemindName.FLEF_Evolve)

	RoleBagWGCtrl.Instance:FlushSpecialBagResloveView(KNAPSACK_TYPE.XIANJIE_EQUIP_BAG)
end

-- 8726 圣装背包单个变化
function FairyLandEquipmentWGCtrl:OnSCXianjieEquipBagChangeInfo(protocol)
	-- print_error("【-----圣装背包单个变化----】：", protocol.change_info)
	self.data:SetHolyEquipBagChangeInfo(protocol)
	self:FlushEquipMentView(TabIndex.fairy_land_eq_holy_equip, "flush_holy_equip")
	RemindManager.Instance:Fire(RemindName.FLE_HolyEquip)
	RemindManager.Instance:Fire(RemindName.FLEF_Evolve)

	RoleBagWGCtrl.Instance:FlushSpecialBagResloveView(KNAPSACK_TYPE.XIANJIE_EQUIP_BAG)
end

-- 8727 圣装穿戴信息
function FairyLandEquipmentWGCtrl:OnSCXianJieEquipShenZhuangInfo(protocol)
	-- print_error("【-----圣装穿戴总信息----】：")
	self.data:SetHolyEquipWearInfo(protocol)

	RemindManager.Instance:Fire(RemindName.FLE_HolyEquip)
	RemindManager.Instance:Fire(RemindName.FLEF_Strengthen)
	RemindManager.Instance:Fire(RemindName.FLEF_Evolve)
	RemindManager.Instance:Fire(RemindName.FLEF_UpQuality)
end

-- 8721 单个穿戴信息变化
function FairyLandEquipmentWGCtrl:OnSCXianJieEquipShenZhuangPartInfo(protocol)
	-- print_error("【-----单个穿戴信息变化----】：")
	self.data:SetHolyEquipWearChangeInfo(protocol)
	self:FlushEquipMentView()

	RemindManager.Instance:Fire(RemindName.FLE_HolyEquip)
	RemindManager.Instance:Fire(RemindName.FLEF_Strengthen)
	RemindManager.Instance:Fire(RemindName.FLEF_Evolve)
	RemindManager.Instance:Fire(RemindName.FLEF_UpQuality)
end

-- 8728 圣装成长线变化
function FairyLandEquipmentWGCtrl:OnSCXianJieEquipShenZhuangInfoChange(protocol)
	self.data:SetHolyEquipChengZhangInfoChange(protocol)

	local reason_type = protocol.reason_type
	if reason_type == XIANJIE_CHANGE_REASON.UPLEVEL or reason_type == XIANJIE_CHANGE_REASON.ONE_KEY_LEVEL then--装备升级
		self:FlushEquipMentView(TabIndex.fl_eq_forge_strengthen)
		RemindManager.Instance:Fire(RemindName.FLEF_Strengthen)
	elseif reason_type == XIANJIE_CHANGE_REASON.UPGRADE then					--装备进阶
		FairyLandEquipmentWGData.Instance:ClearEvolveSelectBagParam()
		self:FlushEquipMentView(TabIndex.fl_eq_forge_evolve)
		RemindManager.Instance:Fire(RemindName.FLEF_Evolve)
	elseif reason_type == XIANJIE_CHANGE_REASON.TAOTAL_LEVEL then				--装备总等级激活
		self:FlushEquipMentView(TabIndex.fl_eq_forge_strengthen)
		self:FlushFLEStrengthenMasterView()
		RemindManager.Instance:Fire(RemindName.FLEF_Strengthen)
	elseif reason_type == XIANJIE_CHANGE_REASON.TAOTAL_STAR then				--装备总星级激活
		self:FlushEquipMentView(TabIndex.fl_eq_forge_evolve)
		self:FlushEvolveStarMasterView()
		RemindManager.Instance:Fire(RemindName.FLEF_Evolve)
	end
end

--============================================================================--
--==============================【锻造 强化】==================================--
--打开强化大师
function FairyLandEquipmentWGCtrl:OpenFLEStrengthenMasterView()
	if self.fle_strength_master_view and not self.fle_strength_master_view:IsOpen() then
		self.fle_strength_master_view:Open()
	end
end

function FairyLandEquipmentWGCtrl:OpenFLEStrengthenMasterViewBySlot(slot)
	if self.fle_strength_master_view then
		self.fle_strength_master_view:SetCurShowSlot(slot)
		if not self.fle_strength_master_view:IsOpen() then
			self.fle_strength_master_view:Open()
		else
			self.fle_strength_master_view:Flush()
		end
	end
end

function FairyLandEquipmentWGCtrl:FlushFLEStrengthenMasterView()
	if self.fle_strength_master_view then
		self.fle_strength_master_view:Flush()
	end
end

--============================================================================--
--==============================【锻造 进化】==================================--
--圣装进化请求
function FairyLandEquipmentWGCtrl:SendCSXianjieEquipShenZhuangUpGrade(slot_index,part_index,count,bag_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSXianjieEquipShenZhuangUpGrade)
	protocol.slot_index = slot_index
	protocol.part_index = part_index or 0
	protocol.count = count or 0
	protocol.bag_list = bag_list or {}
	protocol:EncodeAndSend()
end

function FairyLandEquipmentWGCtrl:OpenEvolvePopEquipBag(slot,part)
	local data = {}
	data.slot = slot
	data.part = part
	self.evolve_pop_equip_bag:SetData(data)
	self.evolve_pop_equip_bag:Open()
end

function FairyLandEquipmentWGCtrl:OpenEvolveStarMaster(slot)
	local data = {}
	data.slot = slot
	self.evolve_star_master_view:SetData(data)
	self.evolve_star_master_view:Open()
end

function FairyLandEquipmentWGCtrl:FlushEvolveStarMasterView()
	if self.evolve_star_master_view then
		self.evolve_star_master_view:Flush()
	end
end
