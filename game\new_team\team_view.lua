require("game/new_team/team_pingtai")
require("game/new_team/team_myteam")
require("game/new_team/team_near")
--组队
TeamView = TeamView or BaseClass(SafeBaseView)

local TEAM_INDEX = {
	PING_TAI = TabIndex.team_pingtai, --组队平台
	MY_TEAM = TabIndex.team_my_team, --我的队伍
	NEAR = TabIndex.team_near,     --附近
	CLONE = TabIndex.team_clone,
}

function TeamView:__init()
	self.view_style = ViewStyle.Full
	self:SetMaskBg()
	self:LoadConfig()
	self.default_index = TEAM_INDEX.PING_TAI
	self.is_safe_area_adapter = true

	self.remind_tab = {
		{ nil },
		{ RemindName.NewTeam_MyTeam },
		{ nil },
	}
	--设置界面缓存时间低一点，这个界面的按钮需要根据功能开启来显示，防止开启了功能却看不到按钮
	self.view_cache_time = 1
	--逻辑相关，Update中判断所有大按钮加载完没有，加载完后排序
	self.is_sort_pingtai_left_btn = nil
	self.is_sort_my_team_left_btn = nil
	self.team_cell_flush = GlobalEventSystem:Bind(TeamInfoQuery.TEAM_INFO_BACK, BindTool.Bind(self.TeamFlush, self))
end

function TeamView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
	-- self:AddViewResource(0, "uis/view/common_panel_prefab", "VerticalTabbar")
	self:AddViewResource(TEAM_INDEX.PING_TAI, "uis/view/new_team_ui_prefab", "layout_pingtai")
	self:AddViewResource(TEAM_INDEX.MY_TEAM, "uis/view/new_team_ui_prefab", "layout_team")
	self:AddViewResource(TEAM_INDEX.CLONE, "uis/view/new_team_ui_prefab", "layout_team_clone")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
	-- self:AddViewResource(TEAM_INDEX.NEAR, "uis/view/new_team_ui_prefab", "layout_near")
	--self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a1_commmon_panel_adorn")
end

function TeamView:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
	-- if self.money_bar then
	-- 	self.money_bar:DeleteMe()
	-- 	self.money_bar = nil
	-- end
	self:PingTaiReleaseCallBack()
	self:MyTeamReleaseCallBack()
	self:NearReleaseCallBack()
	self:CloneReleaseCallBack()

	if Runner and Runner.Instance and Runner.Instance:IsExistRunObj(self) then
		Runner.Instance:RemoveRunObj(self)
	end

	self.is_sort_pingtai_left_btn = nil --是否排序组队平台按钮
	self.is_sort_my_team_left_btn = nil --是否排序我的队伍按钮
	self.has_change_btn_gray = nil
	self.pt_has_change_btn_gray = nil
	self.near_has_change_btn_gray = nil
	if self.team_info_event then
		GlobalEventSystem:UnBind(self.team_info_event)
		self.team_info_event = nil
	end

	if self.world_talk_timecount then
		GlobalEventSystem:UnBind(self.world_talk_timecount)
		self.world_talk_timecount = nil
	end

	if self.word_talk_cd_over then
		GlobalEventSystem:UnBind(self.word_talk_cd_over)
		self.word_talk_cd_over = nil
	end
end

function TeamView:LoadCallBack()
	-- if not self.tabbar then
	-- 	self.tabbar = Tabbar.New(self.node_list)
	--  	-- self.tabbar:SetVerTabbarIconStr("team_view")
	--  	self.tabbar:Init(Language.NewTeam.TabGrop1, nil,nil,nil,self.remind_tab)
	--  	self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	-- end

	-- if not self.money_bar then
	-- 	self.money_bar = MoneyBar.New()
	-- 	local bundle, asset = ResPath.GetWidgets("MoneyBar")
	-- 	local show_params = {
	-- 		show_gold = false,
	-- 		show_bind_gold = false,
	-- 		show_coin = false,
	-- 		show_silver_ticket = false,
	-- 	}
	-- 	self.money_bar:SetMoneyShowInfo(0, 0, show_params)
	-- 	self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	-- end
	self.team_info_event = GlobalEventSystem:Bind(OtherEventType.TEAM_INFO_CHANGE,
		BindTool.Bind1(self.OnTeamChange, self))                                                                         -- 队伍
	-- self.world_talk_timecount = GlobalEventSystem:Bind(TeamWorldTalk.NEW_TEAM_WORLD_TALK,
	-- 	BindTool.Bind(self.UpdateTeamWordTalkBtn, self))
	-- self.word_talk_cd_over = GlobalEventSystem:Bind(TeamWorldTalk.COMPLETE_CALL_BACK,
	-- 	BindTool.Bind(self.ComleteTeamWoldTalkCD, self))
	Runner.Instance:AddRunObj(self)
end

function TeamView:LoadIndexCallBack(index)
	if index == TEAM_INDEX.PING_TAI then --组队平台
		self:PingTaiLoadCallBack()
	elseif index == TEAM_INDEX.MY_TEAM then --我的队伍
		self:MyTeamLoadCallBack()
	elseif index == TEAM_INDEX.NEAR then --附近
		--self:NearLoadCallBack()
	elseif index == TEAM_INDEX.CLONE then
		self:CloneLoadCallBack()
	end
end

function TeamView:ShowIndexCallBack(index)
	local bundle, asset = ResPath.GetRawImagesJPG("a3_zd_bg")
	self:SetTeamViewName(index)
	if index == TEAM_INDEX.PING_TAI then --组队平台
		self:PingTaiShowIndexCallBack()
	elseif index == TEAM_INDEX.MY_TEAM then --我的队伍
		self:MyTeamShowIndexCallBack()
	elseif index == TEAM_INDEX.NEAR then --附近
		--self:NearShowIndexCallBack()
	elseif index == TEAM_INDEX.CLONE then
		self:CloneShowIndexCallBack()
	end

	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)

	local callback_list = NewTeamWGData.Instance:GetOpenTeamCallBack()
	if not IsEmptyTable(callback_list) then
		for i, v in ipairs(callback_list) do
			v()
		end
	end

end

function TeamView:SetTeamViewName(index)
	if index == TEAM_INDEX.PING_TAI then --组队平台
		self.node_list.title_view_name.text.text = Language.NewTeam.ViewNamePT
	elseif index == TEAM_INDEX.MY_TEAM then --我的队伍
		self.node_list.title_view_name.text.text = Language.NewTeam.ViewNameMyTeam
	elseif index == TEAM_INDEX.NEAR then --附近
		--self.node_list.title_view_name.text.text = Language.NewTeam.ViewNameFuJin
	end
end

function TeamView:CloseCallBack()
	self:PingTaiCloseCallBack()
end

function TeamView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if index == TEAM_INDEX.PING_TAI then --组队平台
			self:OnFlushPingTai(k, v)
		elseif index == TEAM_INDEX.MY_TEAM then --我的队伍
			self:OnFlushMyTeam(k, v)
		elseif index == TEAM_INDEX.NEAR then --附近
			--self:OnFlushNear(k,v)
		elseif index == TEAM_INDEX.CLONE then
			self:CloneOnFlush()
		end
	end
end

function TeamView:OnTeamChange()
	if self:IsLoadedIndex(TEAM_INDEX.PING_TAI) then
		self:Flush(TEAM_INDEX.PING_TAI, "HasZuDuiInfoChange", { HasZuDuiInfoChange = true })
	end
	--    if self:IsLoadedIndex(TEAM_INDEX.NEAR) then
	-- 	self:Flush(TEAM_INDEX.NEAR, "HasZuDuiInfoChange", { HasZuDuiInfoChange = true })
	-- end
end

function TeamView:UpdateTeamWordTalkBtn(time)
	if self:IsLoadedIndex(TabIndex.team_pingtai) then
		if self.node_list.pt_btn_speak_text then
			self.node_list.pt_btn_speak_text.text.text = string.format(Language.NewTeam.WorldTalk6, time)
		end
		if not self.pt_has_change_btn_gray then
			self.pt_has_change_btn_gray = true
			XUI.SetGraphicGrey(self.node_list["pt_btn_speak"], true)
		end
	end
	if self:IsLoadedIndex(TabIndex.team_my_team) then
		if self.node_list.btn_word_talk_txt then
			 self.node_list.btn_word_talk_txt.text.text = string.format(Language.NewTeam.WorldTalk6, time)
		end
		if not self.has_change_btn_gray then
			self.has_change_btn_gray = true
			XUI.SetGraphicGrey(self.node_list["btn_mt_word_talk"], true)
		end
	end
	if self:IsLoadedIndex(TabIndex.team_near) then
		if self.node_list.near_btn_speak_text then
			self.node_list.near_btn_speak_text.text.text = string.format(Language.NewTeam.WorldTalk6, time)
		end
		if not self.near_has_change_btn_gray then
			self.near_has_change_btn_gray = true
			XUI.SetGraphicGrey(self.node_list["near_btn_speak"], true)
		end
	end
end

function TeamView:ComleteTeamWoldTalkCD()
	if self:IsLoadedIndex(TabIndex.team_pingtai) then
		if CountDownManager.Instance:HasCountDown("team_world_talk") then
			CountDownManager.Instance:RemoveCountDown("team_world_talk")
		end
		if self.node_list.pt_btn_speak_text then
			self.node_list.pt_btn_speak_text.text.text = ""
		end
		self.pt_has_change_btn_gray = nil
		XUI.SetGraphicGrey(self.node_list["pt_btn_speak"], false)
	end
	if self:IsLoadedIndex(TabIndex.team_my_team) then
		if CountDownManager.Instance:HasCountDown("team_world_talk") then
			CountDownManager.Instance:RemoveCountDown("team_world_talk")
		end
		if self.node_list.btn_word_talk_txt then
			self.node_list.btn_word_talk_txt.text.text = ""
		end
		self.has_change_btn_gray = nil
		XUI.SetGraphicGrey(self.node_list.btn_mt_word_talk, false)
	end
	if self:IsLoadedIndex(TabIndex.team_near) then
		if CountDownManager.Instance:HasCountDown("team_world_talk") then
			CountDownManager.Instance:RemoveCountDown("team_world_talk")
		end
		if self.node_list.near_btn_speak_text then
			self.node_list.near_btn_speak_text.text.text = ""
		end
		self.near_has_change_btn_gray = nil
		XUI.SetGraphicGrey(self.node_list["near_btn_speak"], false)
	end
end

function TeamView:Update(now_time, elapse_time)
	if self:IsLoadedIndex(TabIndex.team_pingtai) then
		if self.pingtai_total_complete_count ~= 0 and self.pingtai_cur_complete_count ~= 0 then
			if self.pingtai_cur_complete_count >= self.pingtai_total_complete_count then
				--平台左侧大按钮加载完
				if not self.is_sort_pingtai_left_btn then
					self.is_sort_pingtai_left_btn = true
					self:PingTaiSortBtnSiblingIndex()
				end
			end
		end
	end

	if self:IsLoadedIndex(TabIndex.team_my_team) then
		if self.myteam_cur_complete_count and self.myteam_total_complete_count ~= 0 and self.myteam_total_complete_count and self.myteam_cur_complete_count ~= 0 then
			if self.myteam_cur_complete_count >= self.myteam_total_complete_count then
				--平台左侧大按钮加载完
				if not self.is_sort_my_team_left_btn then
					self.is_sort_my_team_left_btn = true
					self:SortBtnSiblingIndex()
				end
			end
		end
	end

	--两个列表排序完就可以移除了
	if self.is_sort_pingtai_left_btn and self.is_sort_my_team_left_btn then
		Runner.Instance:RemoveRunObj(self)
	end
end
