require("game/operation_activity/operation_quanfu_juanxian/operation_juanxian_wg_data")
require("game/operation_activity/operation_quanfu_juanxian/operation_juanxian_tips")

OperationJuanXianWGCtrl = OperationJuanXianWGCtrl or BaseClass(BaseWGCtrl)

function OperationJuanXianWGCtrl:__init()
	if OperationJuanXianWGCtrl.Instance ~= nil then
		print("[OperationJuanXianWGCtrl]error:create a singleton twice")
	end

	OperationJuanXianWGCtrl.Instance = self
	-- self:RegisterAllProtocols()
	self.data = OperationJuanXianWGData.New()
	self.xj_reward_tip = SerOperationJuanXianTipView.New()
	self.effect_change = GlobalEventSystem:Bind(ObjectEventType.FIGHT_EFFECT_CHANGE, BindTool.Bind(self.OnEffectChange, self))

	OperationActivityWGCtrl.Instance:ListenHotUpdate("config/auto_new/operation_activity_quanfu_juanxian_auto", BindTool.Bind(self.UpdataConfig, self))
end

function OperationJuanXianWGCtrl:__delete()
	OperationJuanXianWGCtrl.Instance = nil

	self.data:DeleteMe()
	self.data = nil

	self.xj_reward_tip:DeleteMe()
	self.xj_reward_tip = nil

	if self.effect_change then
		GlobalEventSystem:UnBind(self.effect_change)
		self.effect_change = nil
	end

end

function OperationJuanXianWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSOAQuanFuJuanXianOper)
	self:RegisterProtocol(SCOAQuanFuJuanXian, "OnSCOAQuanFuJuanXian")
end

function OperationJuanXianWGCtrl:OnSCOAQuanFuJuanXian(protocol)
	self.data:SetQuanFuJuanXianInfo(protocol)
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_quanfu_juanxian)
	RemindManager.Instance:Fire(RemindName.OperationQuanFuJuanXian)
end

function OperationJuanXianWGCtrl:SendActivityRewardOp(op_type, num, stuff_type)
	-- 暂时屏蔽等后端
 	-- local protocol = ProtocolPool.Instance:GetProtocol(CSOAQuanFuJuanXianOper)
 	-- protocol.op_type = op_type or 0
 	-- protocol.num = num or 0
 	-- protocol.stuff_type = stuff_type or 0
 	-- protocol:EncodeAndSend()
end

function OperationJuanXianWGCtrl:OpenJXRewardTip(data)
	self.xj_reward_tip:SetData(data)
	self.xj_reward_tip:Open()
end

function OperationJuanXianWGCtrl:UpdataConfig()
	self.data:UpdataConfig()
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_quanfu_juanxian)
end

function OperationJuanXianWGCtrl:OnEffectChange(is_main_role)
	if is_main_role then
		self.data:CreatMainExpBtn()
	end
end