TeamHandleClickPoint = {}

local CROSS_SCENCE_TYPE = {
    [SceneType.KF_BOSS] = true,
    [SceneType.Shenyuan_boss] = true,
}

function TeamHandleClickPoint.CheckSceneIdOperate(scene_id, params)
    local cfg = ConfigManager.Instance:GetSceneConfig(scene_id)
    if cfg then
        local cur_scene_type = Scene.Instance:GetSceneType()
        local cur_scene_cfg = ConfigManager.Instance:GetSceneConfig(Scene.Instance:GetSceneId())
        local cur_scene_id = Scene.Instance:GetSceneId()
        local target_scene_type = cfg.scene_type
        if not CROSS_SCENCE_TYPE[cur_scene_type] then
            if target_scene_type == SceneType.Common then  														--普通场景
                TaskWGCtrl.Instance:FlySceneToMove(tonumber(params[2]) or 0, tonumber(params[3]) or 0, tonumber(params[4]) or 0, nil, params[5])
            elseif target_scene_type == SceneType.KF_BOSS then  													--跨服BOSS
                BossWGCtrl.Instance:GoToHelpGuildAssist(params)
            elseif target_scene_type == SceneType.WorldBoss then  													--世界BOSS
                BossWGCtrl.Instance:GoToHelpGuildAssist(params)
            elseif target_scene_type == SceneType.VIP_BOSS then  													--VIP_BOSS(首领之家)
                BossWGCtrl.Instance:GoToHelpGuildAssist(params)
            elseif target_scene_type == SceneType.PERSON_BOSS then  												--个人BOSS
                ViewManager.Instance:Open(GuideModuleName.Boss, FunName.PersonalBoss)
            elseif target_scene_type == SceneType.Shenyuan_boss then  											    --深渊BOSS
                BossWGCtrl.Instance:GoToHelpGuildAssist(params)
            else
                SysMsgWGCtrl.Instance:ErrorRemind(Language.FollowState.NoFollowLeader)
            end
        else
            --跨服场景退出
            NewTeamWGData.Instance:GetSetFollowLeaderFlag(true)
            FuBenWGCtrl.Instance:SendLeaveFB()
        end

    end
end

return TeamHandleClickPoint