DefenseTowerObj = DefenseTowerObj or BaseClass(SceneObj)

function DefenseTowerObj:__init(vo)
	self.obj_type = SceneObjType.DefenseTowerObj
	self.draw_obj:SetObjType(self.obj_type)
	self.res_id = "6001001"
end

function DefenseTowerObj:__delete()
	if self.select_effect then
		self.select_effect = nil
	end
end

function DefenseTowerObj:InitInfo()
	SceneObj.InitInfo(self)
end

function DefenseTowerObj:IsDefenseTower()
	return true
end

function DefenseTowerObj:InitAppearance()
	self:ChangeModel(SceneObjPart.Main, ResPath.GetGatherModel(self.res_id))
	if Scene.Instance:GetSceneType() == SceneType.ClashTerritory then
		--self:ForceSetVisible(self.vo.affiliation == ClashTerritoryData.Instance:GetMainRoleTerritoryWarSide())
	end
end

function DefenseTowerObj:ChangeModelByResid(res_id)
	if self.res_id ~= res_id then
		self.res_id = res_id
		self:ChangeModel(SceneObjPart.Main, ResPath.GetGatherModel(self.res_id))
	end
	if Scene.Instance:GetSceneType() == SceneType.ClashTerritory then
		--self:ForceSetVisible(self.vo.affiliation == ClashTerritoryData.Instance:GetMainRoleTerritoryWarSide())
	end
end

function DefenseTowerObj:OnClick()
	if SceneObj.select_obj then
		SceneObj.select_obj:CancelSelect()
		SceneObj.select_obj = nil
	end
	
	if self:IsDeleted() then
		print_error("该对象已经被Delete掉了，什么代码还在调用，立查？？")
	end

	self.is_select = true
	SceneObj.select_obj = self

	if nil == self.select_effect then
	    self.select_effect = AllocAsyncLoader(self, "select_effect")
	    self.select_effect:SetParent(self.draw_obj:GetRoot().transform)
	    self.select_effect:Load(ResPath.GetEffectUi("UI_xuanzhong_tower"))
    end
    self.select_effect:SetActive(true)
end

function DefenseTowerObj:CancelSelect()
	SceneObj.CancelSelect(self)
    if nil ~= self.select_effect then
    	self.select_effect:SetActive(false)
    end
end