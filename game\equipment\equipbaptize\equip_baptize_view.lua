local auto_key = "auto_baptize_equi"

function EquipmentView:InitBaptizeView()
	if not self.equip_bt_list then
	    self.equip_bt_list = {}
	    for part = 0, GameEnum.EQUIP_INDEX_XIANZHUO do
	        self.equip_bt_list[part] = EquipBaptizeItemRender.New(self.node_list.baptize_equip_list:FindObj("item_" .. part))
	        self.equip_bt_list[part]:SetIndex(part)
	        self.equip_bt_list[part]:SetClickCallBack(BindTool.Bind(self.OnClickBaptizeListCallBack, self))
	    end
    end

	if not self.bpt_equip_body_list then
		self.bpt_equip_body_list = AsyncListView.New(BPTEquipBodyListCellRender, self.node_list.bpt_equip_body_list)
		self.bpt_equip_body_list:SetStartZeroIndex(false)
		self.bpt_equip_body_list:SetSelectCallBack(BindTool.Bind(self.OnSelectBPTEquip<PERSON><PERSON><PERSON><PERSON><PERSON>, self))
		self.bpt_equip_body_list:SetEndScrolledCallBack(BindTool.Bind(self.BPTEquipBodyListSetEndScrollCallBack, self))
	end

	if not self.baptize_show_item then
		self.baptize_show_item = ItemCell.New(self.node_list.baptize_show_item)
		self.baptize_show_item:SetItemTipFrom(ItemTip.FROM_EQUIPMENT)
	end

	if not self.baptize_attr_list then
		self.baptize_attr_list = {}
		for i = 0, GameEnum.EQUIP_BAPTIZE_ONE_PART_MAX_BAPTIZE_NUM - 1 do
			self.baptize_attr_list[i] = BaptizeAttrRender.New(self.node_list.baptize_attr_list:FindObj("baptize_attr_" .. i))
			self.baptize_attr_list[i]:SetIndex(i)
		end
	end

    if not self.common_cost_item then
        self.common_cost_item = ItemCell.New(self.node_list.consume_item_node)
		self.common_cost_item:SetNeedItemGetWay(true)
    end

	if not self.guarantee_cost_item then
        self.guarantee_cost_item = ItemCell.New(self.node_list.guarantee_item_node)
		self.guarantee_cost_item:SetIsShowTips(false)
    end

	self.bt_select_equip_index = -1
	self.bpt_jump_equip_body_seq = -1
	self.bpt_jump_equip_body_equip_data = {}
	self.bpt_need_equip_body_tween = true

	XUI.AddClickEventListener(self.node_list["btn_equip_baptize"], BindTool.Bind(self.OnClickBaptizeEquip, self, false))
	XUI.AddClickEventListener(self.node_list["btn_baptize_add"], BindTool.Bind1(self.OpenBaptizeAdditionView, self))
	XUI.AddClickEventListener(self.node_list["btn_guarantee_add"],BindTool.Bind(self.OnClickUseGuaranteeBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_bpt_equip_body"],BindTool.Bind(self.OnClickBaptizeEquipBodyBtn, self))
	XUI.AddClickEventListener(self.node_list["baptize_once_lock_btn"],BindTool.Bind(self.OnClickBaptizeOnceLockBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_equip_baptize_once"], BindTool.Bind(self.OnClickBaptizeEquip, self, true))

	self.bap_btn_delay_ing = false
	self.is_auto_baptize_equip = false
	self.auto_record_count = 0
	self.baptize_color_lock_record = {}
	self.baptize_select_target_quality = 1

	for i = 1, 2 do
		local desc = EquipmentWGData.Instance:GetEquipBaptizeOtherAttrInfo("target_quality_desc" .. i)
		self.node_list["label_no_longer" .. i].text.text = desc
		self.node_list["img_nohint_hook" .. i]:CustomSetActive(i == self.baptize_select_target_quality)
		XUI.AddClickEventListener(self.node_list["btn_bap_target_color" .. i], BindTool.Bind(self.OnClickBaptizeTargetColorBtn, self, i))
	end
end

function EquipmentView:BaptizeViewDelete()
	if self.bpt_equip_body_list then
		self.bpt_equip_body_list:DeleteMe()
		self.bpt_equip_body_list = nil
	end

	if self.equip_bt_list then
		for k,v in pairs(self.equip_bt_list) do
			v:DeleteMe()
		end
		self.equip_bt_list = nil
	end

	if self.baptize_show_item then
		self.baptize_show_item:DeleteMe()
		self.baptize_show_item = nil
	end

	if self.baptize_attr_list then
		for k, v in pairs(self.baptize_attr_list) do
			v:DeleteMe()
		end
		self.baptize_attr_list = nil
	end

    if self.common_cost_item then
        self.common_cost_item:DeleteMe()
        self.common_cost_item = nil
    end

	if self.guarantee_cost_item then
        self.guarantee_cost_item:DeleteMe()
        self.guarantee_cost_item = nil
    end

	if self.bt_alert_window ~= nil then
		self.bt_alert_window:DeleteMe()
		self.bt_alert_window = nil
	end

	if self.bt_cost_toggle_window ~= nil then
		self.bt_cost_toggle_window:DeleteMe()
		self.bt_cost_toggle_window = nil
	end

	if self.delay_effect_close then
		GlobalTimerQuest:CancelQuest(self.delay_effect_close)
		self.delay_effect_close = nil
	end

	self.bt_select_equip_data = nil
	self.bt_select_equip_part = nil
	self.do_xl_slider_tween = nil
	self.baptize_mian_flush = nil
	self.xl_jump_remind_item = nil
	self.bap_btn_delay_ing = nil
	self.baptize_show_index_change = nil
	self.baptize_color_lock_record = {}
	self.can_baptize_upgrade = nil
	self.bpt_jump_equip_body_seq = nil
	self.bpt_jump_equip_body_equip_data = nil
	self.bpt_need_equip_body_tween = nil
end

-- 刷列表数据源
function EquipmentView:FlushEquipBaptizeListDataSource(mian_flush)
	local total_equip_body_data_list = EquipBodyWGData.Instance:GetDZEquipBodyDataList()
	self.bpt_equip_body_list:SetDataList(total_equip_body_data_list)
	self.bpt_equip_body_list:JumpToIndex(self:GetBptSelectEquipBodySeq(total_equip_body_data_list))
	-- self.baptize_mian_flush = mian_flush
	-- if nil ~= self.equip_bt_list then
	-- 	local cur_data = self.bt_select_equip_data
	-- 	local cur_index = cur_data and cur_data.index
	-- 	local list_data, default_index = EquipmentWGData.Instance:GetEquipBaptizeShowList(self.baptize_show_index_change, cur_index)
	-- 	self.baptize_show_index_change = false
	-- 	for k,v in pairs(self.equip_bt_list) do
	-- 		v:SetData(list_data[k])
	-- 	end

	-- 	if self.equip_bt_list[default_index] then
	-- 		self.equip_bt_list[default_index]:OnClick()
	-- 	end
	-- end

	-- local free_times, total_times = EquipmentWGData.Instance:GetEquipBaptizeFreeTimes()
	-- self.node_list.txt_baptize_free_times.text.text = string.format(Language.Equipment.BaptizeFreeTimesTip, free_times, total_times)
end

function EquipmentView:BaptizeChangeToTargetEquipBody(data)
	if IsEmptyTable(data) then
		return
	end

	local total_equip_body_data_list = EquipBodyWGData.Instance:GetDZEquipBodyDataList()
	if IsEmptyTable(total_equip_body_data_list) then
		return
	end

	self.bpt_jump_equip_body_seq = data.equip_body_seq
	self.bpt_jump_equip_body_equip_data = data.selct_part_data

	for k, v in pairs(total_equip_body_data_list) do
		if v.seq == self.bpt_jump_equip_body_seq then
			if self.bpt_equip_body_list then
				self.bpt_equip_body_list:JumpToIndex(k)
				self.bpt_jump_equip_body_seq = -1
			end

			break
		end
	end
end

function EquipmentView:GetBptSelectEquipBodySeq(total_equip_body_data_list)
	if self.bpt_select_equip_body_seq then
		return self.bpt_select_equip_body_index
	end

	local default_seq = -1
	local default_index = -1
	if not IsEmptyTable(total_equip_body_data_list) then
		for i = #total_equip_body_data_list, 1, -1 do
			local data = total_equip_body_data_list[i]

			if EquipmentWGData.Instance:IsEquipBaptizeUnlockLevelLimit(data.seq) then 
				if EquipmentWGData.Instance:GetEquipBodyBaptizeRemind(data.seq) > 0 then
					return i
				end
	
				if default_seq < 0 or data.seq > default_seq  then
					local can_duanzao, is_unlock, is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyCanDuanZao(data.seq)
					
					if is_unlock and is_wear_equip then
						default_seq = data.seq
						default_index = i
					end
				end
			end
		end
	end

	return default_index
end

-- function EquipmentView:GetBptSelectEquipBodySeq(total_equip_body_data_list)
-- 	if self.bpt_select_equip_body_seq then
-- 		if EquipmentWGData.Instance:GetEquipBodyBaptizeRemind(self.bpt_select_equip_body_seq) then
-- 			return self.bpt_select_equip_body_index
-- 		end
-- 	end

-- 	if not IsEmptyTable(total_equip_body_data_list) then
-- 		for k, v in pairs(total_equip_body_data_list) do
-- 			if EquipmentWGData.Instance:GetEquipBodyBaptizeRemind(v.seq) > 0 then
-- 				return k
-- 			end
-- 		end
-- 	end
	
-- 	return self.bpt_select_equip_body_index or 1
-- end

function EquipmentView:OnSelectBPTEquipBodyHandler(item, cell_index, is_default, is_click)
	if nil == item or IsEmptyTable(item.data) then
		return
	end

	local data = item.data
	local bpt_seq_change = self.bpt_select_equip_body_seq ~= data.seq
	self.bpt_select_equip_body_seq = data.seq
	self.bpt_select_equip_body_index = item.index
	self.bpt_select_equip_body_data = data

	self:FlushEquipBaptizeDataList(bpt_seq_change)
end

function EquipmentView:FlushEquipBaptizeDataList(bpt_seq_change)
	local bpt_equip_data_list = EquipmentWGData.Instance:GetEquipBaptizeShowList(self.bpt_select_equip_body_seq)
	
	for k,v in pairs(self.equip_bt_list) do
		v:SetData(bpt_equip_data_list[k])
	end

	if not IsEmptyTable(self.bpt_jump_equip_body_equip_data) then
		local equip_body_index = self.bpt_jump_equip_body_equip_data.index
		local part_is_open, need_level = EquipmentWGData.Instance:IsEquipBaptizePartOpen(equip_body_index)
		local equip_part = EquipmentWGData.GetEquipPartByEquipBodyIndex(equip_body_index)

		if part_is_open and self.equip_bt_list[equip_part] then
			self.equip_bt_list[equip_part]:OnClick()
			return
		end
	end

	local default_index = self:GetBptEquipSelect(bpt_equip_data_list, bpt_seq_change)

	if default_index < 0 or not self.equip_bt_list[default_index] then
		self:FlushBaptizeOperateInfo(false)
	else
		self.equip_bt_list[default_index]:OnClick()
	end

	-- if default_index >= 0 and self.equip_bt_list[default_index] then
	-- 	self.equip_bt_list[default_index]:OnClick()
	-- end
end

-- 选中需要判断是否达到开启等级
function EquipmentView:GetBptEquipSelect(data_list, bpt_seq_change)
	if not bpt_seq_change and self.bt_select_equip_index >= 0 then
		return self.bt_select_equip_index
		-- local can_baptize = EquipmentWGData.Instance:GetEquipBodyBaptizeEquipRemind(self.bt_select_equip_data.index)

		-- if can_baptize then
		-- 	return self.bt_select_equip_index
		-- end
	end

	local default_index = -1
	for k, v in pairs(data_list) do
		local can_baptize = EquipmentWGData.Instance:GetEquipBodyBaptizeEquipRemind(v.index)
		local equip_part = EquipmentWGData.GetEquipPartByEquipBodyIndex(v.index)

		if default_index < 0 and EquipmentWGData.Instance:IsEquipBaptizePartOpen(v.index) then
			default_index = equip_part
		end

		if can_baptize then
			return equip_part
		end
	end


	if self.bt_select_equip_index >= 0 and not IsEmptyTable(data_list[self.bt_select_equip_index]) and EquipmentWGData.Instance:IsEquipBaptizePartOpen(self.bt_select_equip_index) then
		return self.bt_select_equip_index
	else
		return default_index
	end
end

-- 选择装备列表项回调
function EquipmentView:OnClickBaptizeListCallBack(item)
	if nil == item or nil == item.data then
		return
	end

	local part_is_open, need_level = EquipmentWGData.Instance:IsEquipBaptizePartOpen(item.data.index)
	if not part_is_open then
		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.Equipment.LevelNotEnoughStr, RoleWGData.Instance:TransToDianFengLevelStr(need_level)))
		self:FlushBaptizeOperateInfo(false, item.data)
		return
	end

	self:FlushBaptizeOperateInfo(true, item.data)
	local is_change = self.bt_select_equip_part ~= item.data.index
	self.bt_select_equip_data = item.data

	self.do_xl_slider_tween = nil
	self.baptize_mian_flush = true
	self.bt_select_equip_part = item.data.index

	local bundle, asset = ResPath.GetEquipmentIcon("a3_lq_js")
	local cost_cfg = EquipmentWGData.Instance:GetEquipBaptizeCostCfg(self.bt_select_equip_part)
	if not IsEmptyTable(cost_cfg) then
		bundle, asset = ResPath.GetItem(ItemWGData.Instance:GetItemIconByItemId(cost_cfg.guarantee_item_id))
	end
	self.node_list.image_stone.image:LoadSprite(bundle, asset, function ()
		self.node_list.image_stone.image:SetNativeSize()
	end)

	self.bt_select_equip_index = item.index

	for k, v in pairs(self.equip_bt_list) do
		v:SetSelectIndex(self.bt_select_equip_part % GameEnum.MAX_EQUIP_BODY_EQUIP_NUM)
	end

	self:FlushBaptizeView()
end

function EquipmentView:FlushBaptizeOperateInfo(open_baptize, data)
	local is_max_grade = false

	if data and data.index then
		local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(data.index)
		local baptize_level_limit = EquipBodyWGData.Instance:GetBaptizeLevelLimit(equip_body_seq)
	
		local baptize_part_info = EquipmentWGData.Instance:GetBaptizeInfoByEquipBodyIndex(data.index)
		is_max_grade = baptize_level_limit < baptize_part_info.grade
	end

	self.node_list.equip_baptize_left_group:CustomSetActive(open_baptize)
	self.node_list.equip_baptize_right_group:CustomSetActive(open_baptize and not is_max_grade)
	self.node_list.baptize_show_item:CustomSetActive(open_baptize)
	self.node_list.btn_bap_target_color1:CustomSetActive(open_baptize and not is_max_grade)
	self.node_list.btn_bap_target_color2:CustomSetActive(open_baptize and not is_max_grade)
	self.node_list.desc_bap_target_color:CustomSetActive(open_baptize and not is_max_grade)
	self.node_list.bap_max_grade:CustomSetActive(open_baptize and is_max_grade)

	if not open_baptize then
		self.node_list["txt_baptize_grade"].text.text = ""
		self.node_list.baptize_equip_name.text.text = ""
		self.node_list.txt_identify_score.text.text = 0

		if 	self.node_list.baptize_once_lock_btn.gameObject.activeSelf then
			self.node_list.baptize_once_lock_btn:SetActive(false)
		end

		if self.baptize_attr_list then
			for k, v in pairs(self.baptize_attr_list) do
				v:ClearData()
			end
		end

		if self.equip_bt_list then
			for k, v in pairs(self.equip_bt_list) do
				v:SetSelectIndex(-1)
			end
		end

		self.bt_select_equip_data = nil
		self.bt_select_equip_part = nil
		self.bt_select_equip_index = -1
	end
end

function EquipmentView:FlushBaptizeView()
	if not self.bt_select_equip_data then
		return
	end

	self.can_baptize_upgrade = false

	-- 中心装备
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.bt_select_equip_data.item_id)
	-- 中心属性
	local do_slider_tween = self.do_xl_slider_tween
	local baptize_part_info = EquipmentWGData.Instance:GetBaptizeInfoByEquipBodyIndex(self.bt_select_equip_part)

	if item_cfg then
		self.baptize_show_item:SetData(self.bt_select_equip_data)
		self.node_list.baptize_equip_name.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
	end

	if IsEmptyTable(baptize_part_info) then
		return
	end

	local target_grade = EquipmentWGData.Instance:GetBaptizeTargetGrade(self.bt_select_equip_part)
	-- local str = Language.Equipment.BaptizeGradeColorName[baptize_part_info.grade] or ""
	local str = Language.Equipment.BaptizeGradeColorName[target_grade] or ""
	self.baptize_show_item:SetRightTopImageText(str)

	local not_buy_num = 0
	for k, v in pairs(self.baptize_attr_list) do
		v:SetCurEquipPart(self.bt_select_equip_part)
		v:SetSliderTweenSate(do_slider_tween)
		v:SetData(baptize_part_info.slot_info[k])
		if baptize_part_info.slot_info[k] and baptize_part_info.slot_info[k].is_buy == 0 then
			not_buy_num = not_buy_num + 1
		end
	end

	self.node_list.baptize_once_lock_btn:SetActive(not_buy_num > 0)
	self.node_list["txt_baptize_grade"].text.text = Language.Equipment.BaptizeGradeName[target_grade] or ""
	-- self.node_list["txt_baptize_grade"].text.text = string.format(Language.Equipment.BaptizeGrade, baptize_part_info.grade)
	self.node_list["txt_identify_score"].text.text = baptize_part_info.total_score

	local max_baptize_grade = EquipBodyWGData.Instance:GetBaptizeLevelLimit(self.bpt_select_equip_body_seq)
	local is_max_grade = max_baptize_grade < baptize_part_info.grade

	-- 进阶条件
	local target_score, target_quality = EquipmentWGData.Instance:GetEquipBaptizeUpgradeCondition(self.bt_select_equip_part)
	if is_max_grade or not target_score or not target_quality then
		-- self.node_list["txt_upgrade_condition"].text.text = Language.Equipment.BaptizeGradeMax
		self.node_list.txt_baptize_free_times.text.text = Language.Equipment.BaptizeGradeMax
	else
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.bt_select_equip_data.item_id)
		local str1 = string.format(Language.Equipment.UpgradeConditionOne, target_score)
		-- local str2 = string.format(Language.Equipment.UpgradeConditionTwo, NumberToChinaNumber(target_order))
		local str2 = string.format(Language.Equipment.UpgradeConditionTwo, Language.Common.ColorName[target_quality])

		local is_complete_1 = baptize_part_info.total_score >= target_score
		local is_complete_2 = item_cfg.color >= target_quality
		local condition1_str = ToColorStr(str1, is_complete_1 and COLOR3B.C8 or COLOR3B.C10)
		local condition2_str = ToColorStr(str2, is_complete_2 and COLOR3B.C8 or COLOR3B.C10)
		-- self.node_list["txt_upgrade_condition"].text.text = string.format(Language.Equipment.BaptizeUpgradeConditionTip, condition1_str .. "," .. condition2_str)-- , condition2_str)

		self.node_list.txt_baptize_free_times.text.text = string.format(Language.Equipment.BaptizeUpgradeConditionTip, condition1_str .. "," .. condition2_str)
		self.can_baptize_upgrade = is_complete_1  and is_complete_2
	end

	self.node_list["txt_baptize_btn"].text.text = self.can_baptize_upgrade and Language.Equipment.BtnBaptizeUpgrade or Language.Equipment.AutoBaptize

	local free_times, total_times = EquipmentWGData.Instance:GetEquipBaptizeFreeTimes()
	self.node_list.desc_bpt_free_time.text.text = (not self.can_baptize_upgrade and free_times > 0) and string.format(Language.Equipment.BapViewtizeFreeTimesTip, free_times) or ""

	self.node_list.bpt_consume_item:CustomSetActive(not self.can_baptize_upgrade and free_times <= 0)
	self.node_list.bpt_guarantee_item:CustomSetActive(not self.can_baptize_upgrade and free_times <= 0)
	self.node_list.btn_equip_baptize_once:CustomSetActive(not self.can_baptize_upgrade)

	-- 按钮状态 濯灵条件  
	local have_buy = false
	for k, v in pairs(baptize_part_info.slot_info) do
		if v.is_buy == 1 then
			have_buy = true
			break
		end
	end

	XUI.SetButtonEnabled(self.node_list.btn_equip_baptize, have_buy)
	XUI.SetButtonEnabled(self.node_list.btn_equip_baptize_once, have_buy)

	local btn_color = have_buy and COLOR3B.C21 or COLOR3B.C5
	self.node_list.desc_btn_equip_baptize_once.text.color = StrToColor(btn_color)

	-- 消耗部分
	self:FlushBaptizeCostPart()

	-- 洗炼加成
	self:FlushBaptizeSlider()

	-- 按钮红点
	-- local show_remind = EquipmentWGData.Instance:GetEquipBaptizeCanUpgrade(self.bt_select_equip_part)
	self.node_list.equip_baptize_remind:SetActive( self.can_baptize_upgrade)
	-- self.node_list.btn_equip_baptize_once_remind:SetActive(show_remind)

	self.do_xl_slider_tween = true
end

-- 消耗部分显示
function EquipmentView:FlushBaptizeCostPart()
	local cost_cfg = EquipmentWGData.Instance:GetEquipBaptizeCostCfg(self.bt_select_equip_part)
	
	if IsEmptyTable(cost_cfg) then
		return
	end

	local have_num = ItemWGData.Instance:GetItemNumInBagById(cost_cfg.consume_item_id)
	local show_color = have_num >= cost_cfg.consume_item_num and COLOR3B.GREEN or COLOR3B.PINK
    self.common_cost_item:SetData({item_id = cost_cfg.consume_item_id})
    self.common_cost_item:SetRightBottomColorText(ToColorStr(have_num, show_color) .. "/" .. cost_cfg.consume_item_num)
    self.common_cost_item:SetRightBottomTextVisible(true)

	local is_use = EquipmentWGData.Instance:GetIsUseGuaranteeItem()
	local guarantee_have_num = ItemWGData.Instance:GetItemNumInBagById(cost_cfg.guarantee_item_id)
	local guarantee_show_color = guarantee_have_num >= cost_cfg.guarantee_item_num and COLOR3B.GREEN or COLOR3B.PINK
	if is_use then
		self.guarantee_cost_item:SetData({item_id = cost_cfg.guarantee_item_id})
		self.guarantee_cost_item:SetRightBottomTextVisible(true)
		self.guarantee_cost_item:SetRightBottomColorText(ToColorStr(guarantee_have_num, guarantee_show_color) .. "/" .. cost_cfg.guarantee_item_num)
	else
		self.guarantee_cost_item:ClearData()
		self.guarantee_cost_item:SetRightBottomTextVisible(true)
		self.guarantee_cost_item:SetRightBottomColorText(ToColorStr(guarantee_have_num, guarantee_show_color))
	end
end

-- 洗炼进度
function EquipmentView:FlushBaptizeSlider()
	local progress, desc, need_desc = EquipmentWGData.Instance:GetEquipBaptizeAttrAddProgress(self.bt_select_equip_part)
	if self.do_xl_slider_tween then
		self.node_list.slider_baptize_add.slider:DOValue(progress, 0.5)
	else
		self.node_list.slider_baptize_add.slider.value = progress
	end

	self.node_list["special_xilian_attr"].text.text = desc
	self.node_list["special_xilian_jindu"].text.text = need_desc

	-- local add_remind = EquipmentWGData.Instance:GetEquipBaptizeAddRemind(self.bt_select_equip_part)
	-- self.node_list["baptize_add_remind"]:SetActive(add_remind)
end

-- 使用保底道具按钮点击
function EquipmentView:OnClickUseGuaranteeBtn()
	local cost_cfg = EquipmentWGData.Instance:GetEquipBaptizeCostCfg(self.bt_select_equip_part)
	local has_num = ItemWGData.Instance:GetItemNumInBagById(cost_cfg.guarantee_item_id)
	local is_use = EquipmentWGData.Instance:GetIsUseGuaranteeItem()

	local btn_callback_event
	if not IsEmptyTable(cost_cfg) then
		btn_callback_event = {}
		btn_callback_event[1] = {btn_text = Language.Equipment.UnUseGuarantee, callback = function()
			EquipmentWGData.Instance:SetIsUseGuaranteeItem(false)
			self:FlushBaptizeCostPart()
		end}
	end

	if not is_use then
		if has_num < cost_cfg.guarantee_item_num then
			TipWGCtrl.Instance:OpenItem({item_id = cost_cfg.guarantee_item_id})
			return
		end

		EquipmentWGData.Instance:SetIsUseGuaranteeItem(true)
		self:FlushBaptizeCostPart()
	else
		TipWGCtrl.Instance:OpenItem({item_id = cost_cfg.guarantee_item_id}, nil, nil, nil, btn_callback_event)
	end
end

-- 洗炼操作
function EquipmentView:OnClickBaptizeEquip(flag_once)
	if not self.bt_select_equip_part then
		return
	end

	-- 按钮延迟 解决手快点击 协议下发慢，导致玩家错过好属性提醒
	if self.bap_btn_delay_ing then
		return
	end
	

	if not self.is_auto_baptize_equip then
		self.bap_btn_delay_ing = true
		GlobalTimerQuest:AddDelayTimer(BindTool.Bind(function ()
			self.bap_btn_delay_ing = false
		end, self), 0.4)
	end

	local part_is_open, need_level = EquipmentWGData.Instance:IsEquipBaptizePartOpen(self.bt_select_equip_part)
	if not part_is_open then
		local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(need_level)
		local need_str = is_vis and Language.Equip.BaptizeNotOpen3 or Language.Equip.BaptizeNotOpen2
		TipWGCtrl.Instance:ShowSystemMsg(string.format(need_str, role_level))
		return
	end

	-- -- 装备阶级是否足够
	-- local need_order = EquipmentWGData.Instance:GetEquipBaptizeNeedOrder()
	-- local item_cfg = ItemWGData.Instance:GetItemConfig(self.bt_select_equip_data.item_id)
	-- if IsEmptyTable(item_cfg) then return end
	-- if item_cfg.order < need_order then
	-- 	TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.Equipment.OrderNotEnough, NumberToChinaNumber(need_order)))
	-- 	return
	-- end
	-- local cur_order = self.bt_select_equip_data.item_id

	-- 是否可晋升
	if self.can_baptize_upgrade then
		EquipmentWGCtrl.Instance:SendEquipBaptizeOperaReq(EQUIP_BAPTIZE_OPERA_TYPE.EQUIP_BAPTIZE_OPERA_TYPE_UP_GRADE, self.bt_select_equip_part)
		return
	end

	local cost_cfg = EquipmentWGData.Instance:GetEquipBaptizeCostCfg(self.bt_select_equip_part)
	-- 是否有免费次数
	local has_free = EquipmentWGData.Instance:GetEquipBaptizeFreeTimes() > 0 
	
	if not has_free then
		-- 基础洗炼道具是否足够
		local cost_id = cost_cfg.consume_item_id
		local need_num = cost_cfg.consume_item_num
		local have_num = ItemWGData.Instance:GetItemNumInBagById(cost_id)
		-- 道具不足
		if have_num < need_num then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cost_id})
			return
		end
	end

	-- 保底洗炼道具是否足够
	local is_use = EquipmentWGData.Instance:GetIsUseGuaranteeItem()
	if is_use then
		local guarantee_item_num = ItemWGData.Instance:GetItemNumInBagById(cost_cfg.guarantee_item_id)
		if guarantee_item_num < cost_cfg.guarantee_item_num then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cost_cfg.guarantee_item_id})
			return
		end
	end

	-- 最高品质提示
	local baptize_part_info = EquipmentWGData.Instance:GetBaptizeInfoByEquipBodyIndex(self.bt_select_equip_part)
	local max_baptize_grade = EquipBodyWGData.Instance:GetBaptizeLevelLimit(self.bpt_select_equip_body_seq)
	local is_max_grade = max_baptize_grade < baptize_part_info.grade
	
	local do_baptize = function ()
		-- 红橙品质属性未上锁提醒
		local orange_no_lock, red_no_lock = EquipmentWGData.Instance:GetEquipBaptizeBatterNoLock(self.bt_select_equip_part)
		if orange_no_lock or red_no_lock then
			self:RemindBatterAttrToLock(orange_no_lock, red_no_lock)
			return
		end

		local select_target_quality = EquipmentWGData.Instance:GetEquipBaptizeOtherAttrInfo("target_quality" .. self.baptize_select_target_quality) or 0

		if flag_once then
			select_target_quality = 0
		end
	
		EquipmentWGCtrl.Instance:SendEquipBaptizeOperaReq(EQUIP_BAPTIZE_OPERA_TYPE.EQUIP_BAPTIZE_OPERA_TYPE_BAPTIZE, self.bt_select_equip_part, is_use and 1 or 0, select_target_quality)
	end

	if is_max_grade then
		TipWGCtrl.Instance:OpenCheckTodayAlertTips(Language.Equipment.BaptizeMaxQualityTip, function ()
			do_baptize()
		end, "BaptizeMaxQualityTip", Language.Equip.BaptizeOneKeyResultNoRemind)
		return
	end

	do_baptize()
end

function EquipmentView:BaptizeViewClose()
	EquipmentWGData.Instance:SaveBaptizeAddRemind() --保存红点
end

function EquipmentView:BaptizeShowIndexCallBack()
	self.bpt_select_equip_body_seq = nil
	self.baptize_show_index_change = true
	self:CancelAutoBaptizeEquip()
end

function EquipmentView:ClearBTSliderTween()
	self.do_xl_slider_tween = nil
	self.xl_jump_remind_item = true
end

-- 自动洗炼按钮点击[废弃]
function EquipmentView:OnClickAutoBaptizeEquip()
	self.is_auto_baptize_equip = not self.is_auto_baptize_equip
	if self.is_auto_baptize_equip then
		self:OnClickBaptizeEquip()
		self:FlushAutoBtnText()
	else
		self:CancelAutoBaptizeEquip(true)
	end
end

-- 取消自动洗炼[废弃]
function EquipmentView:CancelAutoBaptizeEquip(is_click_btn)
	if self.is_auto_baptize_equip or is_click_btn then
		self.auto_record_count = 0
		self.is_auto_baptize_equip = false
		self:FlushAutoBtnText()
		if CountDownManager.Instance:HasCountDown(auto_key) then
			CountDownManager.Instance:RemoveCountDown(auto_key)
		end
	end
end

function EquipmentView:FlushAutoBtnText()
	if self.node_list.btn_auto_equip_baptize_text then
		self.node_list.btn_auto_equip_baptize_text.text.text = self.is_auto_baptize_equip and Language.Equipment.CancelBaptize or Language.Equipment.AutoBaptize
	end
end

-- 提示未锁定弹窗
function EquipmentView:RemindBatterAttrToLock(orange_no_lock, red_no_lock)
	if nil == self.bt_alert_window then
		self.bt_alert_window = Alert.New(nil, nil, nil, nil, true, nil, false)
	end

	local is_auto_baptize_equip = self.is_auto_baptize_equip
	local auto_record_count = self.auto_record_count
	local is_auto_remind = is_auto_baptize_equip and auto_record_count > 0

	local orange = self.baptize_color_lock_record[GameEnum.ITEM_COLOR_ORANGE]
	local red = self.baptize_color_lock_record[GameEnum.ITEM_COLOR_RED]

	local cahce_color = 0
	local tips_desc = ""
	local is_remind = false
	local is_clear_tip_state = false
	if red_no_lock and (not red or is_auto_baptize_equip) then
		is_remind = true
		is_clear_tip_state = red_no_lock and not red
		cahce_color = GameEnum.ITEM_COLOR_RED
		tips_desc = is_auto_remind and Language.Equipment.AutoBaptizeTips[2] or Language.Equipment.NoLockTips[2]
	elseif orange_no_lock and (not orange or is_auto_baptize_equip) then
		is_remind = true
		is_clear_tip_state = orange_no_lock and not orange
		cahce_color = GameEnum.ITEM_COLOR_ORANGE
		tips_desc = is_auto_remind and Language.Equipment.AutoBaptizeTips[1] or Language.Equipment.NoLockTips[1]
	end

	if is_remind and is_clear_tip_state then
		self.bt_alert_window:ClearCheckHook()
		self.bt_alert_window:SetCheckBoxDefaultSelect(false)
	else
		self.bt_alert_window.record_not_tip = true
	end

	local is_use = EquipmentWGData.Instance:GetIsUseGuaranteeItem()
	self.bt_alert_window:SetLableString(tips_desc)
	self.bt_alert_window:SetUseOneSign(is_auto_remind)
	self.bt_alert_window:SetShowCheckBox(not is_auto_remind)
	self.bt_alert_window:SetOkFunc(function()
		if not is_auto_baptize_equip or auto_record_count == 0 then
			local select_target_quality = EquipmentWGData.Instance:GetEquipBaptizeOtherAttrInfo("target_quality" .. self.baptize_select_target_quality) or 0
			EquipmentWGCtrl.Instance:SendEquipBaptizeOperaReq(EQUIP_BAPTIZE_OPERA_TYPE.EQUIP_BAPTIZE_OPERA_TYPE_BAPTIZE, self.bt_select_equip_part, is_use and 1 or 0, select_target_quality)

			if is_remind and self.bt_alert_window:GetIsNolongerTips() then
				self.baptize_color_lock_record[cahce_color] = true
			end

			self.is_auto_baptize_equip = is_auto_baptize_equip and auto_record_count == 0
			self:FlushAutoBtnText()
		end
	end)

	self.bt_alert_window:Open()

	if self.bt_alert_window:IsOpen() then
		self:CancelAutoBaptizeEquip()
	end
end

function EquipmentView:OpenBaptizeAdditionView()
	if nil == self.bt_select_equip_part or IsEmptyTable(self.bt_select_equip_data) then
		
		return
	end

	EquipmentWGCtrl.Instance:SetDataAndIOpenBaptizeAddView(self.bt_select_equip_data)
	EquipmentWGData.Instance:SetEquipBaptizeAddRemind(self.bt_select_equip_part)
	self:FlushEquipBaptizeListDataSource() --刷新一下界面的红点
end

function EquipmentView:ShowBaptizeSuceeEffect(is_upgrade)
	if self.delay_effect_close then return end
	--self.node_list["baptize_effect_root"]:SetActive(true)
	if is_upgrade then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jinsheng, is_success = true, pos = Vector2(0, 0)})
	else
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.ty_zhuoling, is_success = true, pos = Vector2(0, 0)})
	end

	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.DecomPosition))
	self.delay_effect_close = GlobalTimerQuest:AddDelayTimer(function()
		--self.node_list["baptize_effect_root"]:SetActive(false)
		GlobalTimerQuest:CancelQuest(self.delay_effect_close)
		self.delay_effect_close = nil
	end, 1.9)
end

function EquipmentView:OnClickBaptizeEquipBodyBtn()
	EquipmentWGCtrl.Instance:OpenBaptizeOverviewView()
end

function EquipmentView:OnClickBaptizeOnceLockBtn()
	local baptize_part_info = EquipmentWGData.Instance:GetBaptizeInfoByEquipBodyIndex(self.bt_select_equip_part)
	if IsEmptyTable(baptize_part_info) then
		return
	end

	local need_money_num = 0
	local not_buy_num = 0
	for k, v in pairs(baptize_part_info.slot_info) do
		if v.is_buy ~= 1 then
			local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(self.bt_select_equip_part)
			local cost_num = EquipmentWGData.Instance:GetEquipBaptizeSlotActiveCost(equip_body_seq, k)
			need_money_num = need_money_num + cost_num
			not_buy_num = not_buy_num + 1
		end
	end

	if not_buy_num > 0 then
		local ok_func = function()
			if not RoleWGData.Instance:GetIsEnoughAllGold(need_money_num) then
				TipWGCtrl.Instance:ShowSystemMsg(Language.Common.NoEnoughGold)
				return
			end

			EquipmentWGCtrl.Instance:SendEquipBaptizeOperaReq(EQUIP_BAPTIZE_OPERA_TYPE.EQUIP_BAPTIZE_OPERA_TYPE_UNLOCK_BAPTIZE, self.bt_select_equip_part, -1)
		end

		TipWGCtrl.Instance:OpenAlertTips(string.format(Language.Equipment.BySlotCostDesc, need_money_num), ok_func)
	end
end

function EquipmentView:BPTEquipBodyListSetEndScrollCallBack()
	if self.bpt_need_equip_body_tween then
		self.bpt_need_equip_body_tween = false

		local tween_info = UITween_CONSTS.EquipBody

		local cell_list = self.bpt_equip_body_list:GetAllItems()
		for i = 1, #cell_list do
			cell_list[i]:PlayItemTween()
		end
	end
end

function EquipmentView:OnClickBaptizeTargetColorBtn(index)
	self.baptize_select_target_quality = index

	for i = 1, 2 do
		self.node_list["img_nohint_hook" .. i]:CustomSetActive(i == self.baptize_select_target_quality)
	end
end

---------------------------------------------
-- 洗练装备列表的itemrender
---------------------------------------------
EquipBaptizeItemRender = EquipBaptizeItemRender or BaseClass(BaseRender)
function EquipBaptizeItemRender:__init()
	self.item_cell = ItemCell.New(self.node_list.item_node)
	self.item_cell:SetIsShowTips(false)

	self.slot_list = {}
	for i = 1, GameEnum.EQUIP_BAPTIZE_ONE_PART_MAX_BAPTIZE_NUM do
		self.slot_list[i] = self.node_list["Img_" .. i]
	end

	self.node_list.select_img:SetActive(true)
	self:OnSelectChange(false)
	XUI.AddClickEventListener(self.node_list.block_click, BindTool.Bind(self.OnClick, self))
end


function EquipBaptizeItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function EquipBaptizeItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.item_cell:ClearData()
		self.item_cell:SetItemIcon(ResPath.GetEquipIcon(self.index))
        -- local scale = self.index == 5 and Vector3.one * 0.7 or Vector3.one
		-- self.item_cell:SetItemIconLocalScale(scale)

		self.node_list.limit_text:SetActive(false)
		self.node_list.quality_list:SetActive(false)
		self.node_list.remind:SetActive(false)
		return
	end

	self.item_cell:SetData(self.data)
	self:OtherFlush()
end

function EquipBaptizeItemRender:OtherFlush()
	-- 洗炼部位开启状态
	local part_is_open, need_level = EquipmentWGData.Instance:IsEquipBaptizePartOpen(self.data.index)
	self.node_list.limit_text:SetActive(not part_is_open)
	self.node_list.quality_list:SetActive(part_is_open)
	if not part_is_open then
		local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(need_level)
		local need_str = is_vis and Language.Common.DianFengLevelOpen or Language.Common.LevelOpen1
		local limit_desc = string.format(need_str, role_level)
		self.node_list.limit_text.text.text = limit_desc
		self.node_list.remind:SetActive(false)
		return
	end

	local slot_infos = EquipmentWGData.Instance:GetBaptizeSlotInfoByEquipBodyIndex(self.data.index)

	for i = 0, GameEnum.EQUIP_BAPTIZE_ONE_PART_MAX_BAPTIZE_NUM - 1 do
		local data = slot_infos and slot_infos[i] or nil
		if data then
			local bundle, asset = ResPath.GetEquipmentIcon("a3_lq_xq" .. (data.quality))
			self.slot_list[i + 1].image:LoadSprite(bundle, asset)
			self.slot_list[i + 1]:SetActive(data.is_buy == 1)
		else
			self.slot_list[i + 1]:SetActive(false)
		end
	end

	local baptize_part_info = EquipmentWGData.Instance:GetBaptizeInfoByEquipBodyIndex(self.data.index)
	local target_grade = EquipmentWGData.Instance:GetBaptizeTargetGrade(self.data.index)
	-- local str = Language.Equipment.BaptizeGradeColorName[baptize_part_info.grade] or ""
	local str = Language.Equipment.BaptizeGradeColorName[target_grade] or ""
	self.item_cell:SetRightTopImageText(str)

    -- 红点
	local show_remind = EquipmentWGData.Instance:GetEquipBodyBaptizeEquipRemind(self.data.index)
	self.node_list.remind:SetActive(show_remind)
end

function EquipBaptizeItemRender:OnSelectChange(is_select)
	self.node_list.select_img.image.enabled = is_select
end


---------------------------------------------
-- 洗炼属性Render
---------------------------------------------
BaptizeAttrRender = BaptizeAttrRender or BaseClass(BaseRender)
function BaptizeAttrRender:__init()
	XUI.AddClickEventListener(self.node_list["btn_unlock"], BindTool.Bind(self.OnClickBuyBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_slot_lock"], BindTool.Bind(self.OnClickSlotLockBtn, self))
end

function BaptizeAttrRender:__delete()
	self.equip_part = nil

	if self.bt_buy_slot_window then
		self.bt_buy_slot_window:DeleteMe()
		self.bt_buy_slot_window = nil
	end
end

function BaptizeAttrRender:SetCurEquipPart(equip_part)
	self.equip_part = equip_part
end

function BaptizeAttrRender:SetSliderTweenSate(bool)
	self.slider_tween = bool
end

function BaptizeAttrRender:OnFlush()
	if IsEmptyTable(self.data) then
		  self.node_list.attr_slider_text.text.text = ""
		  self.node_list.attr_slider.slider.value = 0
		  self.node_list.btn_unlock:SetActive(false)
		  self.node_list.btn_slot_lock:SetActive(false)
		return
	end
	self.is_buy = self.data.is_buy == 1
	self.is_lock = self.data.is_lock == 1

	if self.data.quality > 0 then
		local bundle, asset = ResPath.GetEquipmentIcon("a3_lq_js" .. self.data.quality)
		self.node_list.attr_slider_fill.image:LoadSprite(bundle, asset)
	end

	self.node_list.attr_slider_fill:SetActive(self.data.progress > 0)
	if self.slider_tween then
		self.node_list.attr_slider.slider:DOValue(self.data.progress, 0.5)
	else
		self.node_list.attr_slider.slider.value = self.data.progress
	end

	local slide_desc = ""
	if self.is_buy then
		local attr_name = EquipmentWGData.Instance:GetAttrName(self.data.word_type)
		local is_per = EquipmentWGData.Instance:GetAttrIsPer(self.data.word_type)
		local per_desc = is_per and "%" or ""
		local attr_value = is_per and self.data.attr_value * 0.01 .. "%" or self.data.attr_value
		local min_value = is_per and self.data.min_value * 0.01 .. "%" or self.data.min_value
		local max_value = is_per and self.data.max_value * 0.01 .. "%" or self.data.max_value
		slide_desc = string.format(Language.Equipment.BTSliderDescNew, attr_name, attr_value, min_value, max_value)
	else
		local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(self.equip_part)
		local cost_num = EquipmentWGData.Instance:GetEquipBaptizeSlotActiveCost(equip_body_seq, self.index)
		slide_desc = string.format(Language.Equipment.BTSliderDescNoAct, cost_num)
	end
	self.node_list.attr_slider_text.text.text = slide_desc
	self.node_list.btn_unlock:SetActive(self.data.is_first_buy)
	self.node_list.btn_slot_lock:SetActive(self.is_buy and not self.data.is_max_grade)
	self.node_list.img_slot_locked:SetActive(self.is_lock)
	self.node_list.img_slot_unlocked:SetActive(not self.is_lock)

	self.node_list.remind:SetActive(not self.is_buy and self.data.slot_id == 0)
end

function BaptizeAttrRender:OnClickBuyBtn()
	local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(self.equip_part)
	local cost_num = EquipmentWGData.Instance:GetEquipBaptizeSlotActiveCost(equip_body_seq, self.index)
	if cost_num > 0 then
		if not RoleWGData.Instance:GetIsEnoughAllGold(cost_num) then
			TipWGCtrl.Instance:ShowSystemMsg(Language.Common.NoEnoughGold)
			return
		end

		if nil == self.bt_buy_slot_window then
			self.bt_buy_slot_window = Alert.New(nil, nil, nil, nil, true)
			self.bt_buy_slot_window:SetShowCheckBox(false)
		end
		self.bt_buy_slot_window:SetLableString(string.format(Language.Equipment.BySlotCostDesc, cost_num))
		self.bt_buy_slot_window:SetOkFunc(function()
			EquipmentWGCtrl.Instance:SendEquipBaptizeOperaReq(EQUIP_BAPTIZE_OPERA_TYPE.EQUIP_BAPTIZE_OPERA_TYPE_UNLOCK_BAPTIZE, self.equip_part, self.data.slot_id)
		end)
		self.bt_buy_slot_window:Open()
	else
		EquipmentWGCtrl.Instance:SendEquipBaptizeOperaReq(EQUIP_BAPTIZE_OPERA_TYPE.EQUIP_BAPTIZE_OPERA_TYPE_UNLOCK_BAPTIZE, self.equip_part, self.data.slot_id)
	end
end

function BaptizeAttrRender:OnClickSlotLockBtn()
	if IsEmptyTable(self.data) then return end

	local have_lock_num = EquipmentWGData.Instance:GetEquipBaptizeAttrLockNum(self.equip_part)
	local have_buy_num = EquipmentWGData.Instance:GetEquipBaptizeSlotBuyNum(self.equip_part)

	if not self.is_lock and (have_lock_num + 2 >= GameEnum.EQUIP_BAPTIZE_ONE_PART_MAX_BAPTIZE_NUM or have_lock_num + 1 >= have_buy_num) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Equipment.CanLockTips)
		return
	end
	EquipmentWGCtrl.Instance:SendEquipBaptizeOperaReq(EQUIP_BAPTIZE_OPERA_TYPE.EQUIP_BAPTIZE_OPERA_TYPE_LOCK_OR_UNLOCK, self.equip_part, self.data.slot_id)
end

--------------------------------BPTEquipBodyListCellRender-----------------------------------
BPTEquipBodyListCellRender = BPTEquipBodyListCellRender or BaseClass(BaseRender)

function BPTEquipBodyListCellRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_tip"], BindTool.Bind(self.OnClickTipBtn, self))
end

function BPTEquipBodyListCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local icon_bundle, icon_asset = ResPath.GetEquipBodyIcon(self.data.show_icon)
	self.node_list.icon.image:LoadSprite(icon_bundle, icon_asset, function ()
		self.node_list.icon.image:SetNativeSize()
	end)

	local hl_icon_bundle, hl_icon_asset = ResPath.GetEquipBodyIcon(self.data.show_hl_icon)
	self.node_list.icon_hl.image:LoadSprite(hl_icon_bundle, hl_icon_asset, function ()
		self.node_list.icon_hl.image:SetNativeSize()
	end)

	self.node_list.name.text.text = self.data.name
	self.node_list.name_hl.text.text = self.data.name
	self.node_list.specall_name.text.text = self.data.name

	local unlock_level_limit = EquipmentWGData.Instance:IsEquipBaptizeUnlockLevelLimit(self.data.seq)
	local can_duanzao, is_unlocak, is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyCanDuanZao(self.data.seq)
	self.node_list.flag_lock:CustomSetActive(not unlock_level_limit or (not is_unlocak and not is_wear_equip))
	self.node_list.no_equip_tip:CustomSetActive(unlock_level_limit and is_unlocak and not is_wear_equip)

	self.node_list.btn_tip:CustomSetActive(not unlock_level_limit or not can_duanzao)

	local remind = EquipmentWGData.Instance:GetEquipBodyBaptizeRemind(self.data.seq) > 0
	self.node_list.remind:CustomSetActive(remind)
end

function BPTEquipBodyListCellRender:OnSelectChange(is_select)
	local is_special = self.data.type == 1
	self.node_list.icon:CustomSetActive(is_special or (not is_special and not is_select))
	self.node_list.icon_hl:CustomSetActive(not is_special and is_select)
	self.node_list.special_select:CustomSetActive(is_special and is_select)
	self.node_list.name:CustomSetActive(not is_special and not is_select)
	self.node_list.name_hl:CustomSetActive(not is_special and is_select)
	self.node_list.specall_name:CustomSetActive(is_special)
end

function BPTEquipBodyListCellRender:OnClickTipBtn()
	local can_duanzao, is_unlocak, is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyCanDuanZao(self.data.seq)

	if not can_duanzao then
		if not is_unlocak then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleEquipBody.EquipBodyLockCanDuanZao)
		elseif not is_wear_equip then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleEquipBody.EquipBodyNotWearEquipCanDuanZao)
		end
	end

	local unlock_level_limit, unlock_level = EquipmentWGData.Instance:IsEquipBaptizeUnlockLevelLimit(self.data.seq)

	if not unlock_level_limit then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Equipment.BaptizeLevelLimitStr, unlock_level))
	end
end

function BPTEquipBodyListCellRender:PlayItemTween()
	UITween.FakeHideShow(self.node_list.root)
	local index = self:GetIndex()
	local tween_info = UITween_CONSTS.EquipBody.ListCellTween
	self.cell_delay_key = "BPTEquipBodyItemCellRender" .. index
	ReDelayCall(self, function()
		if self.node_list and self.node_list.root then
			UITween.FakeToShow(self.node_list.root)
		end
	end, tween_info.NextDoDelay * (index - 1), self.cell_delay_key)
end







--[[
---------------------------------------------
-- 洗炼装备列表 （没用到）
---------------------------------------------
BaptizeAsyncListView = BaptizeAsyncListView or BaseClass(AsyncListView)

function BaptizeAsyncListView:IsLimitSelectByIndex(cell_index)
	if self.data_list and self.data_list[cell_index] then
		local data = self.data_list[cell_index]

		local part_is_open, need_level = EquipmentWGData.Instance:IsEquipBaptizePartOpen(data.index)
		if not part_is_open then
			local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(need_level)
			local need_str = is_vis and Language.Equip.BaptizeNotOpen3 or Language.Equip.BaptizeNotOpen2
			TipWGCtrl.Instance:ShowSystemMsg(string.format(need_str, role_level))
			return true
		end
	end

	return false
end

--list事件回调
function BaptizeAsyncListView:ListEventCallback(item_cell)
	local cell_index = item_cell:GetIndex()
	if self:IsLimitSelectByIndex(cell_index) then
		return
	end

	self.cur_select_index = cell_index
	self:__OnSelectIndex(self.cur_select_index, false, true)
end

function BaptizeAsyncListView:__OnSelectIndex(cell_index, is_default, is_click)
	if self:IsLimitSelectByIndex(cell_index) then
		return
	end

	for k, v in pairs(self.cell_list) do
		v:SetSelectIndex(cell_index)
	end

	if nil ~= self.select_callback and nil ~= cell_index then
		local item = self:GetItemAt(cell_index)
		if nil ~= item then
			self.select_callback(item, cell_index, is_default, is_click)
		end
	end
end
]]