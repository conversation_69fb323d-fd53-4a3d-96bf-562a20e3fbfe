--Vip续费界面
VipDescInfoView = VipDescInfoView or BaseClass(SafeBaseView)

function VipDescInfoView:__init()
	self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Pop
	self.view_name = "VipDescInfoView"
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {sizeDelta = Vector2(612, 364)})
	self:AddViewResource(0, "uis/view/recharge_ui_prefab", "layout_recharge_info")
	self.select_vip_level = 0
end

function VipDescInfoView:ReleaseCallBack()

end

function VipDescInfoView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Recharge.ViewNameInfo
end

function VipDescInfoView:OnFlush(param_t, index)
    local vip_level = self.select_vip_level
	local desc_list = RechargeWGData.Instance:GetRechargeVipLevelDesc(vip_level)
	if not IsEmptyTable(desc_list) then
		local format = string.format
		local temp_list = {}
		local index = 1
		for i=1,#desc_list do
			if desc_list[i] ~= "" then
				temp_list[index] = format(Language.Recharge.VipTeQuan, index, desc_list[i])
				index = index + 1
			end
		end
		local desc = table.concat(temp_list, "\n")
		self.node_list.info_text.text.text = desc
	else
		self.node_list.info_text.text.text = ""
	end
end

function VipDescInfoView:SetVipLevel(level)
	self.select_vip_level = level
end