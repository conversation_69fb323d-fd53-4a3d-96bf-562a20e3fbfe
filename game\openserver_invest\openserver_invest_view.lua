OpenServerInvestView = OpenServerInvestView or BaseClass(SafeBaseView)

function OpenServerInvestView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
    self:AddViewResource(0, "uis/view/openserver_invest_ui_prefab", "layout_openserver_invest_view")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
    self.view_style = ViewStyle.Half
    self.is_safe_area_adapter = true
    self.view_layer = UiLayer.Normal
end

function OpenServerInvestView:LoadCallBack()
    self.cur_type = -1
    self.jump_to_red_point = true
    self.jump_reward_flag = false
    self.scorll_index = -1
    self.max_show_level = 0
    self.old_gd_show_level = -1
    self.old_gd_show_type = -1

    local bundle, asset = ResPath.GetRawImagesPNG("a3_kftz_bg")
    if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

    self.node_list.title_view_name.text.text = Language.OpenServerInvest.Title

    if not self.gd_show_reward_item then
        self.gd_show_reward_item = OpenServerInvestRewardRender.New(self.node_list["gd_reward_cell"])
        self.gd_show_reward_item:SetIsGuDingReward(true)
    end

    if not self.reward_list then
        self.reward_list = AsyncListView.New(OpenServerInvestRewardRender, self.node_list.reward_list)
        self.node_list.reward_list.scroller.scrollerEndScrolled = BindTool.Bind(self.OnScrollValueChanged, self)
    end

    if self.openserver_invest_toggle_list == nil then
        self.openserver_invest_toggle_list = OpenServerInvestToggleList.New(OpenServerInvestToggleRender,
            self.node_list.openserver_invest_toggle_list)
        self.openserver_invest_toggle_list:SetSelectCallBack(BindTool.Bind(self.OnClickToggle, self))
    end

    if not self.display_model then
        self.display_model = OperationActRender.New(self.node_list["display_model"])
        self.display_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

    if not self.soul_ring_model then
		self.soul_ring_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["soul_ring_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = false,
		}

		self.soul_ring_model:SetRenderTexUI3DModel(display_data)

		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local special_status_table = {ignore_wing = true, ignore_halo = true, ignore_fazhen = true, ignore_mantle = true, ignore_tail = true, ignore_jianzhen = true}
		self.soul_ring_model:SetModelResInfo(role_vo, special_status_table)
    end

    XUI.AddClickEventListener(self.node_list.btn_active_invest, BindTool.Bind(self.OnClickBuyInvestBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_get_all_reward, BindTool.Bind(self.OnClickGetAllReward, self))
    XUI.AddClickEventListener(self.node_list.btn_goto_func, BindTool.Bind(self.OnClickGoToFunc, self))
    XUI.AddClickEventListener(self.node_list.btn_open_reward_show, BindTool.Bind(self.OpenRewardShowView, self))
end

function OpenServerInvestView:ReleaseCallBack()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end

    if self.gd_show_reward_item then
        self.gd_show_reward_item:DeleteMe()
        self.gd_show_reward_item = nil
    end

    if self.openserver_invest_toggle_list then
        self.openserver_invest_toggle_list:DeleteMe()
        self.openserver_invest_toggle_list = nil
    end

    if self.display_model then
        self.display_model:DeleteMe()
        self.display_model = nil
    end

    if self.soul_ring_model then
        self.soul_ring_model:DeleteMe()
        self.soul_ring_model = nil
    end

    if CountDownManager.Instance:HasCountDown("openserver_invest_count_down") then
        CountDownManager.Instance:RemoveCountDown("openserver_invest_count_down")
    end
end

function OpenServerInvestView:ShowIndexCallBack()
    self.jump_to_red_point = true
end

function OpenServerInvestView:SetJumpRewardflag(bool)
    self.jump_reward_flag = bool
end

function OpenServerInvestView:OnFlush()
    self:FlushToggleList()
    self:FlushPanel()
    self:FlushModel()
    self:FlushTimeCountDown()
end

function OpenServerInvestView:FlushToggleList()
    local toggle_data_list = OpenServerInvestWGData.Instance:GetToggleShowList()
    self.openserver_invest_toggle_list:SetDataList(toggle_data_list)
    self.openserver_invest_toggle_list:FlushRemind()

    if self.jump_to_red_point then
        self.jump_to_red_point = false
        local jump_index = OpenServerInvestWGData.Instance:GetJumpRedTab()
        self:JumpToShowView(jump_index)
    else
        local index = self:GetToggleIndex(toggle_data_list)
        self:JumpToShowView(index)
    end
end

function OpenServerInvestView:GetToggleIndex(toggle_data_list)
    for k, v in pairs(toggle_data_list) do
        if v.type == self.cur_type then
            return k
        end
    end

    return 1
end

function OpenServerInvestView:FlushPanel()
    local type_cfg = OpenServerInvestWGData.Instance:GetInvestTypeCfgByType(self.cur_type)
    if IsEmptyTable(type_cfg) then
        return
    end

    local is_can_receive = OpenServerInvestWGData.Instance:GetActRemindByType(self.cur_type)
    local is_meet_max_level = OpenServerInvestWGData.Instance:IsMeetMaxLevel(self.cur_type)
    self.node_list.btn_get_all_reward:SetActive(is_can_receive)
    self.node_list.btn_goto_func:SetActive(not (is_can_receive or is_meet_max_level))

    local invest_buy_flag = OpenServerInvestWGData.Instance:GetInvestBuyFlagByType(self.cur_type)
    self.node_list.high_lock:CustomSetActive(not invest_buy_flag)
    self.node_list.high_mask:CustomSetActive(not invest_buy_flag)
    self.node_list.btn_active_invest:SetActive(not invest_buy_flag)
    self.node_list.btn_open_reward_show:SetActive(not invest_buy_flag)
    --self.node_list.have_bug_flag:SetActive(invest_buy_flag)

    local reward_list = OpenServerInvestWGData.Instance:GetInvestRewardListByType(self.cur_type)
    self.reward_list:SetDataList(reward_list)

    --if self.jump_reward_flag then
    local index = OpenServerInvestWGData.Instance:GetJumpRewardIndex(self.cur_type)
    self.reward_list:JumpToIndex(index)
        --self.jump_reward_flag = false
    --end

    self:FlushGuDingShowReward(self.scorll_index)
    local remind_get = OpenServerInvestWGData.Instance:GetActRemindByType(self.cur_type)
    self.node_list.get_all_red_point:SetActive(remind_get)

    --self.node_list.text_profit.text.text = type_cfg.profit_label
    self.node_list.desc.text.text = type_cfg.txt
    self.node_list.condition_title.text.text = Language.OpenServerInvest.ConditionTitle[self.cur_type]
    self.node_list.btn_active_invest_text.text.text = string.format(Language.OpenServerInvest.btn_text1, type_cfg.price)
    local bundle, asset = ResPath.GetRawImagesPNG("a3_kftz_txt" .. type_cfg.art_title)
    self.node_list.title_img.raw_image:LoadSprite(bundle, asset, function()
        self.node_list.title_img.raw_image:SetNativeSize()
    end)

    self.node_list.text_profit.text.text = type_cfg.profit_num.."%"

    self.node_list.text_reward_desc.text.text = type_cfg.gold_num
end

function OpenServerInvestView:FlushModel()
    local type_cfg = OpenServerInvestWGData.Instance:GetInvestTypeCfgByType(self.cur_type)
    if IsEmptyTable(type_cfg) then
		return
	end

    if type_cfg.show_type == 2 then
        self.node_list.soul_ring_model:SetActive(true)
        self.node_list.display_model:SetActive(false)
		if type_cfg.rotation and "" ~= type_cfg.rotation then
			local pos_list = string.split(type_cfg.rotation, "|")
			local x = tonumber(pos_list[1]) or 0
			local y = tonumber(pos_list[2]) or 0
			local z = tonumber(pos_list[3]) or 0

			self.soul_ring_model:SetRTAdjustmentRootLocalRotation(x, y, z)
		end

		if type_cfg.display_scale and "" ~= type_cfg.display_scale then
			self.soul_ring_model:SetRTAdjustmentRootLocalScale(type_cfg.display_scale, type_cfg.display_scale, type_cfg.display_scale)
		end

        local target_data = {}
        if type_cfg.soul_ring_id and "" ~= type_cfg.soul_ring_id then
			local soul_ring_id_list = string.split(type_cfg.soul_ring_id, "|")
            for k, v in pairs(soul_ring_id_list) do
                local cfg = ShenShouWGData.Instance:GetShenShouCfg(tonumber(v))
                target_data[k - 1] = {soul_ring_effect = cfg.soul_ring_effect}
            end

            self.soul_ring_model:SetTotalSoulRingResid(target_data, false, #soul_ring_id_list)
		end
    else
        self.node_list.soul_ring_model:SetActive(false)
        self.node_list.display_model:SetActive(true)
        local display_data = {}
        display_data.should_ani = true
        if type_cfg.model_show_itemid and type_cfg.model_show_itemid ~= 0 and type_cfg.model_show_itemid ~= "" then
            local split_list = string.split(type_cfg.model_show_itemid, "|")
            if #split_list > 1 then
                local list = {}
                for k, v in pairs(split_list) do
                    list[tonumber(v)] = true
                end
                display_data.model_item_id_list = list
            else
                display_data.item_id = type_cfg.model_show_itemid
            end

            display_data.model_click_func = function ()
                TipWGCtrl.Instance:OpenItem({item_id = type_cfg["model_show_itemid"]})
            end
        end

        display_data.bundle_name = type_cfg["model_bundle_name"]
        display_data.asset_name = type_cfg["model_asset_name"]
        local model_show_type = tonumber(type_cfg["model_show_type"]) or 1
        display_data.render_type = model_show_type - 1

        self.display_model:SetData(display_data)
        local scale = type_cfg["display_scale"]
        if scale and scale ~= "" then
            Transform.SetLocalScaleXYZ(self.node_list["display_model"].transform, scale, scale, scale)
        end

        if type_cfg.rotation and type_cfg.rotation ~= "" then
            local rotation_tab = string.split(type_cfg.rotation,"|")
            self.node_list["display_model"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
        end
    end

    local pos_x, pos_y = 0, 0
    if type_cfg.display_pos and type_cfg.display_pos ~= "" then
        local pos_list = string.split(type_cfg.display_pos, "|")
        pos_x = tonumber(pos_list[1]) or pos_x
        pos_y = tonumber(pos_list[2]) or pos_y
    end

    RectTransform.SetAnchoredPositionXY(self.node_list.display_node.rect, pos_x, pos_y)
end

function OpenServerInvestView:OnClickToggle(cell)
    if not cell or not cell.data or self.cur_type == cell.data.type then
        return
    end

    self.cur_type = cell.data.type
    self.openserver_invest_toggle_list:FlushRemind()
    local max_cfg = OpenServerInvestWGData.Instance:GetMaxLevelCfg(self.cur_type)
    self.max_show_level = max_cfg.seq
    self:FlushPanel()
    self:FlushModel()
    self:FlushTimeCountDown()
end

--有效时间倒计时
function OpenServerInvestView:FlushTimeCountDown()
	CountDownManager.Instance:RemoveCountDown("openserver_invest_count_down")
	local count_down_time = OpenServerInvestWGData.Instance:GetInvestTimeByType(self.cur_type)
	if count_down_time > 0 then
        self.node_list.activity_time:SetActive(true)

		self.node_list["time_down"].text.text = TimeUtil.FormatSecondDHM9(count_down_time)
		CountDownManager.Instance:AddCountDown("openserver_invest_count_down",
        BindTool.Bind1(self.UpdateCountDown, self),
        BindTool.Bind1(self.CountDownOnComplete, self),
        nil, count_down_time, 1)
    else
        self.node_list.activity_time:SetActive(false)
	end
end

function OpenServerInvestView:UpdateCountDown(elapse_time, total_time)
	self.node_list["time_down"].text.text = TimeUtil.FormatSecondDHM9(total_time - elapse_time)
end

function OpenServerInvestView:CountDownOnComplete()
    CountDownManager.Instance:RemoveCountDown("openserver_invest_count_down")
end

function OpenServerInvestView:OnScrollValueChanged(scroll_obj, start_idx, end_idx)
    local now_index  = end_idx
    if self.scorll_index == now_index and self.old_gd_show_type == self.cur_type then
        return
    end

    self.scorll_index = now_index
    now_index = now_index > self.max_show_level and self.max_show_level or now_index
    self:FlushGuDingShowReward(now_index)
end

function OpenServerInvestView:JumpToShowView(jump_index)
    self.openserver_invest_toggle_list:JumpToIndex(jump_index)
end

function OpenServerInvestView:FlushGuDingShowReward(level)
    if not level or level < 0 then
        return
    end

    local max_level_cfg = OpenServerInvestWGData.Instance:GetMaxLevelCfg(self.cur_type)
    if IsEmptyTable(max_level_cfg) then
        return
    end

    local show_level = -1
    local reward_list = OpenServerInvestWGData.Instance:GetInvestTaskCfgByType(self.cur_type)

    if not IsEmptyTable(reward_list) then
		for k, v in ipairs(reward_list) do
            if ((v.is_fix_show > 0) and (v.seq > level)) then
                show_level = v.seq
                break
            end
        end
	end

	if show_level < 0 then
		show_level = max_level_cfg.seq
	end

    local reward = OpenServerInvestWGData.Instance:GetInvestRewardDataByTypeAndSeq(self.cur_type, show_level)

    if self.old_gd_show_level ~= show_level or self.old_gd_show_type ~= self.cur_type then
        self.gd_show_reward_item:SetData(reward)
        self.old_gd_show_level = show_level
        self.old_gd_show_type = self.cur_type
    end
end

function OpenServerInvestView:OnClickBuyInvestBtn()
    self:OpenRewardShowView()
end

function OpenServerInvestView:OpenRewardShowView()
    local type_cfg = OpenServerInvestWGData.Instance:GetInvestTypeCfgByType(self.cur_type)

    if IsEmptyTable(type_cfg) then
        return
    end

    OpenServerInvestWGCtrl.Instance:OpenRewardShowView(self.cur_type)
end

function OpenServerInvestView:OnClickGetAllReward()
    if not OpenServerInvestWGData.Instance:GetActRemindByType(self.cur_type) then
		TipsSystemManager.Instance:ShowSystemTips(Language.OpenServerInvest.NoGetReward)
		return
	end

    OpenServerInvestWGCtrl.Instance:SendOpenServerInvestReq(OPENSERVER_INVEST_OPERATE_TYPE.OGA_INVEST_OPERATE_TYPE_FETCH_TASK_REWARD, self.cur_type)
end

function OpenServerInvestView:OnClickGoToFunc()
    local type_cfg = OpenServerInvestWGData.Instance:GetInvestTypeCfgByType(self.cur_type)
    FunOpen.Instance:OpenViewNameByCfg(type_cfg.open_panel)
end

------------------------------------------OpenServerInvestRewardRender-------------------------------------------
OpenServerInvestRewardRender = OpenServerInvestRewardRender or BaseClass(BaseRender)
function OpenServerInvestRewardRender:__init()
    self.view:SetActive(true)
end

function OpenServerInvestRewardRender:LoadCallBack()
    self.is_guding_reward = false

    if self.node_list["btn_get_nor_reward"] then
        self.node_list["btn_get_nor_reward"].button:AddClickListener(BindTool.Bind(self.OnClickGetReward,self))
    end

    if self.node_list["btn_get_high_reward"] then
        self.node_list["btn_get_high_reward"].button:AddClickListener(BindTool.Bind(self.OnClickGetReward,self))
    end

    self.normal_item = OpenServerInvestRewardItem.New(self.node_list.normal_item)

    self.high_item_list = {}
    for i = 1, 2 do
        self.high_item_list[i] = OpenServerInvestRewardItem.New(self.node_list["high_item_" .. i])
        self.high_item_list[i]:SetIsHighItem(true)
        self.high_item_list[i]:SetIndex(i)
    end
end

function OpenServerInvestRewardRender:__delete()
    if self.normal_item then
        self.normal_item:DeleteMe()
        self.normal_item = nil
    end

    if self.high_item_list then
        for k, v in pairs(self.high_item_list) do
            v:DeleteMe()
        end
        self.high_item_list = nil
    end
end

function OpenServerInvestRewardRender:SetIsGuDingReward(bool)
    self.is_guding_reward = bool
end

function OpenServerInvestRewardRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    if self.node_list.btn_get_nor_reward then
        self.node_list.btn_get_nor_reward:SetActive(self.data.nor_can_get)
    end

    if self.node_list.btn_get_high_reward then
        self.node_list.btn_get_high_reward:SetActive(self.data.high_can_get)
    end

    if self.node_list.normal_item_red_point then
        self.node_list.normal_item_red_point:SetActive(self.data.nor_can_get)
    end

    if self.node_list.high_red_point1 then
        self.node_list.high_red_point1:SetActive(self.data.high_can_get)
    end

    if self.node_list.high_red_point2 then
        self.node_list.high_red_point2:SetActive(self.data.high_can_get)
    end

    if self.node_list.condition_desc then
        local condition_desc
        local is_meet_conditon = OpenServerInvestWGData.Instance:IsMeetCondition(self.data.value1, self.data.param1)
        local color = is_meet_conditon and COLOR3B.C2 or COLOR3B.C3

        if self.is_guding_reward then
            color = is_meet_conditon and COLOR3B.C8 or COLOR3B.C10
        end

        if self.data.is_special_show then
            local value1, value2 = self:GetGradeAndStarNum(self.data.value1)
            local param1, param2 = self:GetGradeAndStarNum(self.data.param1)
            condition_desc = string.format(Language.OpenServerInvest.TaskDesc[self.data.describe_type], color, value1, value2, param1, param2)
        else
            local value_str = self.data.value1
            local param1_str = self.data.param1
            if self.data.describe_type == 3 then --战力特殊显示
                value_str = CommonDataManager.ConverExpExtend(self.data.value1)
                param1_str = CommonDataManager.ConverExpExtend(self.data.param1)
            end
            condition_desc = string.format(Language.OpenServerInvest.TaskDesc[self.data.describe_type], color, value_str, param1_str)
        end
        self.node_list.condition_desc.text.text = condition_desc
    end

    self.normal_item:SetData({reward_data = self.data.item_list[0], is_get = self.data.nor_is_get})
    for k, v in ipairs(self.high_item_list) do
        v:SetData({reward_data = self.data.rmb_item_list[k - 1], is_get = self.data.higer_is_get})
    end

    -- if self.node_list.normal_lock then
    --     self.node_list.normal_lock:SetActive(not (self.data.nor_is_get or self.data.nor_can_get))
    -- end

    -- if self.node_list.high_lock then
    --     local invest_buy_flag = OpenServerInvestWGData.Instance:GetInvestBuyFlagByType()
    --     self.node_list.high_lock:SetActive(not invest_buy_flag or (not (self.data.higer_is_get or self.data.high_can_get)))
    -- end
end

function OpenServerInvestRewardRender:GetGradeAndStarNum(value)
    local value1 = value % 10 == 0 and value / 10 or math.floor(value / 10) + 1
    local value2 = value % 10 == 0 and 10 or value % 10
    return value1, value2
end

function OpenServerInvestRewardRender:OnClickGetReward()
    if IsEmptyTable(self.data) then
        return
    end

    OpenServerInvestWGCtrl.Instance:SendOpenServerInvestReq(OPENSERVER_INVEST_OPERATE_TYPE.OGA_INVEST_OPERATE_TYPE_FETCH_TASK_REWARD)
end

---------------------------------OpenServerInvestRewardItem--------------------------------------
OpenServerInvestRewardItem = OpenServerInvestRewardItem or BaseClass(BaseRender)

function OpenServerInvestRewardItem:LoadCallBack()
    self.reward_item = ItemCell.New(self.node_list.item_node)
end

function OpenServerInvestRewardItem:__delete()
    if self.reward_item then
        self.reward_item:DeleteMe()
        self.reward_item = nil
    end

    self.is_high_item = nil
end

function OpenServerInvestRewardItem:SetIsHighItem(bool)
    self.is_high_item = bool
end

function OpenServerInvestRewardItem:OnFlush()
    if IsEmptyTable(self.data.reward_data) then
        self.view:SetActive(false)
    else
        self.view:SetActive(true)

        --local invest_buy_flag = OpenServerInvestWGData.Instance:GetInvestBuyFlagByType()
        self.reward_item:SetData(self.data.reward_data)
        --self.node_list.item_lock:SetActive(self.is_high_item and not invest_buy_flag)
        self.node_list.item_isget:SetActive(self.data.is_get)
    end
end

----------------------------
OpenServerInvestToggleRender = OpenServerInvestToggleRender or BaseClass(BaseRender)

function OpenServerInvestToggleRender:LoadCallBack()
end

function OpenServerInvestToggleRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list.normal_txt.text.text = self.data.label_name
    self.node_list.TextHL.text.text = self.data.label_name
    self:ShowRemind()
end

function OpenServerInvestToggleRender:ShowRemind()
    if not self.data then
        return
    end

    local is_remind = OpenServerInvestWGData.Instance:GetActRemindByType(self.data.type)
    self.node_list.RedPoint:SetActive(is_remind)
end

function OpenServerInvestToggleRender:OnSelectChange(is_select)
    self.node_list.normal:SetActive(not is_select)
    self.node_list.HLImage:SetActive(is_select)
end

---------------
OpenServerInvestToggleList = OpenServerInvestToggleList or BaseClass(AsyncListView)

function OpenServerInvestToggleList:FlushRemind()
    for k, v in pairs(self.cell_list) do
        v:ShowRemind()
    end
end