local LAYER_LIST = {
    [0] = 0,
    [TabIndex.equipxunbao_xunbao + 1] = 0,
    [TabIndex.equipxunbao_xunbao + 2] = 1,
    [TabIndex.xuanshi_xunbao + 1] = 2,
    [TabIndex.xuanshi_xunbao + 2] = 3}

function NewXunbaoView:ReleaseTaoBaoLong()
    if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end

    if self.reset_alert then
        self.reset_alert:DeleteMe()
        self.reset_alert = nil
    end

    if self.big_reward_list then
        self.big_reward_list:DeleteMe()
        self.big_reward_list = nil
    end

    for i = 0, 4 do
        --local list = self["cell_list_" .. i] or {}
        --for i, v in pairs(list) do
        --    v:DeleteMe()
        --end
        self["cell_list_" .. i] = nil
    end

    if self.all_cell_list then
        for i, v in pairs(self.all_cell_list) do
            v:DeleteMe()
        end
        self.all_cell_list = nil
    end

    if self.cell_effect_event then
        GlobalEventSystem:UnBind(self.cell_effect_event)
    end

    self.show_index_layer_tween = nil
    self.show_index_layer_tween2 = nil
    self.long_start_click = nil
    self.cur_layer_index = nil
    self.play_shuffle_anim = nil
    self.xipai_sequence = nil
    self.show_card_anim = nil
end

function NewXunbaoView:InitLongXunBao()
    if not self.all_cell_list then
        self.node_list["btn_open_xunbao_bag"].button:AddClickListener(BindTool.Bind(self.OnClickOpenLongBag, self))
        self.node_list["btn_open_record"].button:AddClickListener(BindTool.Bind(self.OnClickOpenLongRecord, self))
        self.node_list["btn_start"].button:AddClickListener(BindTool.Bind(self.OnClickLongStart, self))
        self.node_list["btn_open_tip"].button:AddClickListener(BindTool.Bind(self.OnClickOpenLongTip, self))
        self.node_list["btn_long_reset"].button:AddClickListener(BindTool.Bind(self.OnClickLongReset, self))
        self.node_list["btn_long_shuffle"].button:AddClickListener(BindTool.Bind(self.OnClickLongShuffle, self))
        self:AddToggleListener()

        self:LoadAllCell()

        self.big_reward_list = AsyncListView.New(XunBaoBigRewardRender, self.node_list["big_cell_list"])
        self.cell_effect_event = GlobalEventSystem:Bind(OtherEventType.TAOBAO_EFFECT, BindTool.Bind(self.PlayCellEffect, self))
    end
end

function NewXunbaoView:LoadAllCell()
    local bundle, asset = ResPath.GetXunBaoCellRender()
    self.all_cell_list = {}

    for i = 1, 44 do
        self.all_cell_list[i] = XunBaoCellRender.New()
        self.all_cell_list[i]:LoadAsset(bundle, asset, self.node_list["cell" .. i].transform)
        self.all_cell_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickCell, self))
    end
end

function NewXunbaoView:AddToggleListener()
    for i = 1, 2 do
        self.node_list["btn_layer_" .. i].toggle:AddClickListener(BindTool.Bind(self.OnClickLongLayer, self, i))
    end
    --self.node_list["btn_layer_2"].toggle:AddClickListener(BindTool.Bind(self.OnClickLongLayer, self, 1))
end

--改变层数toggle的interactable
function NewXunbaoView:ChangeToggleInteractable(status)
    if self.node_list["btn_layer_1"] and self.node_list["btn_layer_2"] then
        --print_error("set", status)
        self.node_list["btn_layer_1"].toggle.interactable = status
        self.node_list["btn_layer_2"].toggle.interactable = status
    end
end

function NewXunbaoView:CheckShowIndexAnim()
    if self.xipai_sequence then
        self.xipai_sequence:Kill()
        self.xipai_sequence = nil
    end

    if self.show_card_anim then
        self.show_card_anim:Kill()
        self.show_card_anim = nil
    end

    self:ChangeToggleInteractable(true)

    for i, v in pairs(self.all_cell_list) do
        v:ResetCell(self.node_list["cell" .. i])
    end

    self.onclick_cell = nil
    self.play_shuffle_anim = false

    GlobalTimerQuest:AddDelayTimer(function()
        --print_error("get", self.node_list["btn_layer_1"].toggle.interactable)
        if self.node_list["btn_layer_1"].toggle.interactable then
            self.node_list["btn_layer_1"].toggle.isOn = false
            self.node_list["btn_layer_1"].toggle.isOn = true
        end
        self:CheckNeedReset()
    end, 0)
end

function NewXunbaoView:ShowIndexLongXunBao(index)
    self:CheckShowIndexAnim()
    for i = 1, 2 do
        self.node_list["hl_text_layer_" .. i].text.text = Language.Xunbao.LayerName[i + self.show_index]
        self.node_list["nor_text_layer_" .. i].text.text = Language.Xunbao.LayerName[i + self.show_index]
    end
    self:PlayLayerAnim()
end

--播放层数动画
function NewXunbaoView:PlayLayerAnim()
    if self.show_index_layer_tween then
        self.show_index_layer_tween:Kill()
    end
    if self.show_index_layer_tween2 then
        self.show_index_layer_tween2:Kill()
    end
    local cfg = NewXunbaoWGData.Instance:GetTBLongPerformanceCfg()
    self.node_list["layer_1"].rect.anchoredPosition = Vector2(0, 80)
    self.node_list["layer_2"].rect.anchoredPosition = Vector2(0, 145)

    local layer_tween_time = cfg.layer_tween_time or 0.5
    local layer_interval_time = cfg.layer_interval_time or 0.3
    self.show_index_layer_tween = self.node_list["layer_1"].rect:DOAnchorPosY(0, layer_tween_time)
    self.show_index_layer_tween:SetEase(DG.Tweening.Ease.InQuart)
    self.show_index_layer_tween:OnComplete(function()
        self.show_index_layer_tween = nil
    end)

    GlobalTimerQuest:AddDelayTimer(function()
        self.show_index_layer_tween2 = self.node_list["layer_2"].rect:DOAnchorPosY(0, 0.5)
        self.show_index_layer_tween2:SetEase(DG.Tweening.Ease.InQuart)
        self.show_index_layer_tween2:OnComplete(function()
            self.show_index_layer_tween2 = nil
        end)
    end, layer_interval_time)

end

--检测是否需要reset
function NewXunbaoView:CheckNeedReset()
    if not self.cur_layer_index then
        return
    end
    if NewXunbaoWGData.Instance:GetisDrawOutByLayer(self.cur_layer_index) then
       NewXunbaoWGCtrl.Instance:SendTaoBaoOp(TAOBAO_OP_TYPE.RESET, self.cur_layer_index)
    end
end

--刷新
function NewXunbaoView:OnFlushLongXunBao()
    if not self.cur_layer_index then
        return
    end
    self:FlushLongCommon()
end

--刷新指定方法
function NewXunbaoView:OnFlushLongShow(key, value)
    if "draw" == key then
        self:OnFlushDrawCell()
    elseif "draw_all" == key then
        self:OnFlushLongReset()
    elseif "shuffle" == key then
        self:OnFlushLongShuffle()
    end
end

--播放重置动画
function NewXunbaoView:OnFlushLongReset()
    local list = self["cell_list_" .. self.cur_layer_index]
    self:ResetAllCell()
    --self:GetSetIsPrepareStatus(0)
    self:PlayStartAnim()
    --for i, v in pairs(list) do
    --    --v:SetResetAnim(BindTool.Bind(self.FlushLongCommon, self))
    --end
end

--获取准备状态
function NewXunbaoView:GetSetIsPrepareStatus(val)
    if val then
        NewXunbaoWGCtrl.Instance:SendTaoBaoOp(TAOBAO_OP_TYPE.OPNE, self.cur_layer_index)
        if self.show_index == TabIndex.equipxunbao_xunbao then
            RemindManager.Instance:Fire(RemindName.XunBao_XunBao)
        else
            RemindManager.Instance:Fire(RemindName.XunBao_XuanShi)
        end
    else
        return NewXunbaoWGData.Instance:GetTBOpenFlagByLayer(self.cur_layer_index) == 0
    end
end

--刷新中间显示和下面的大奖信息
function NewXunbaoView:FlushLongCommon()
    local is_prepare_status = self:GetSetIsPrepareStatus()
    local is_draw_out = NewXunbaoWGData.Instance:GetisDrawOutByLayer(self.cur_layer_index)

    self.node_list["btn_start"]:SetActive(is_prepare_status or is_draw_out)
    self.node_list["txt_btn_start"].text.text = is_draw_out and Language.Xunbao.BtnTxt2 or Language.Xunbao.BtnTxt1
    self.node_list["txt_center"]:SetActive(is_prepare_status)
    if is_prepare_status then
        self.node_list["txt_center"].text.text = Language.Xunbao["LongCenterDesc" .. self.cur_layer_index]
    end
    --self.node_list["center_desc"]:SetActive(not is_prepare_status)
    if not is_prepare_status then
        local cur_draw_info = NewXunbaoWGData.Instance:GetCurDrawInfo(self.cur_layer_index)
        self.node_list["center_desc"]:SetActive(cur_draw_info ~= nil)
        if cur_draw_info ~= nil then
            local num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_info.consume_item_id)
            local color = num < cur_draw_info.consume_item_num and COLOR3B.RED or COLOR3B.GREEN
            self.node_list["txt_item_num"].text.text = ToColorStr(num .. "/" .. cur_draw_info.consume_item_num, color)
        end
    else
        self.node_list["center_desc"]:SetActive(false)
    end

    self:FlushBigReward()
    self:FlushLongRemind()
    self:FlushBtnShuffle()
end

function NewXunbaoView:FlushBtnShuffle()
    self.node_list["btn_long_shuffle"]:SetActive(NewXunbaoWGData.Instance:CheckHasRewardId(self.cur_layer_index))
end

function NewXunbaoView:FlushBigReward()
    local data = NewXunbaoWGData.Instance:GetLongBigRewardByLayer(self.cur_layer_index)
    self.big_reward_list:SetDataList(data, 3)
end

--刷新层数红点
function NewXunbaoView:FlushLongRemind()
    if self.node_list["btn_red_bag"] then
        self.node_list["btn_red_bag"]:SetActive(not NewXunbaoWGData.Instance:GetXunBaoBagEmpty())
        for i = 1, 2 do
            self.node_list["toggle_red_" .. i]:SetActive(NewXunbaoWGData.Instance:CheckLongLayerRemind(LAYER_LIST[self.show_index + i]))
        end
    end
end

--刷新抽奖的格子
function NewXunbaoView:OnFlushDrawCell()
    if self.play_shuffle_anim then
        return
    end
    local draw_result = NewXunbaoWGData.Instance:GetDrawResult()
    local cell = self["cell_list_" .. self.cur_layer_index][draw_result.draw_index]
    cell:SetNewParent(self.node_list["cell_max"])
    local data = cell:GetData()
    data.id = draw_result.draw_id
    data.is_prepare = false
    cell:PlaySelectAnim()
    cell:PlayCardAudio()
    cell:SetCellSelectEffect(false)
    local time = NewXunbaoWGData.Instance:GetFlushBigRewardTime()
    self:DelayFlushCommon(time)
end

--打开背包
function NewXunbaoView:OnClickOpenLongBag()
    ViewManager.Instance:Open(GuideModuleName.XunBaoBag)
end

--打开日志
function NewXunbaoView:OnClickOpenLongRecord()
    NewXunbaoWGCtrl.Instance:SetRecordViewLayer(self.cur_layer_index)
    ViewManager.Instance:Open(GuideModuleName.XunBaoRecord)
end

--检查是否动画ing
function NewXunbaoView:CheckIsAnim()
    local list = self["cell_list_" .. self.cur_layer_index]
    for i, v in pairs(list) do
        if v:GetIsTween() then
            return true
        end
    end
end

--点击重置
function NewXunbaoView:OnClickLongReset()
    if not NewXunbaoWGData.Instance:CheckHasRewardId(self.cur_layer_index) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Xunbao.ResetBtnTip)
        return
    end
    if self:CheckIsAnim() then
        return
    end

    if not self.reset_alert then
        self.reset_alert = Alert.New()
        self.reset_alert:SetShowCheckBox(false)
    end
    self.reset_alert:SetLableString(Language.Xunbao.ResetTip)
    self.reset_alert:SetOkFunc(function()
        NewXunbaoWGCtrl.Instance:SendTaoBaoOp(TAOBAO_OP_TYPE.RESET, self.cur_layer_index)
    end)
    self.reset_alert:Open()
end

--洗牌
function NewXunbaoView:OnClickLongShuffle()
    if not NewXunbaoWGData.Instance:CheckHasRewardId(self.cur_layer_index) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Xunbao.ResetBtnTip)
        return
    end
    if self:CheckIsAnim() then
        return
    end
    if not self.reset_alert then
        self.reset_alert = Alert.New()
        self.reset_alert:SetShowCheckBox(false)
    end
    self.reset_alert:SetLableString(Language.Xunbao.ShuffleTip)
    self.reset_alert:SetOkFunc(function()
        NewXunbaoWGCtrl.Instance:SendTaoBaoOp(TAOBAO_OP_TYPE.SHUFFLE, self.cur_layer_index)
    end)
    self.reset_alert:Open()
end

--关闭检测是否需要reset
function NewXunbaoView:CloseTaobaoCallBack()
    self:CheckNeedReset()
end

--点击开始，播放洗牌动画
function NewXunbaoView:OnClickLongStart()
    if self:CheckIsAnim() then
        return
    end
    local is_draw_out = NewXunbaoWGData.Instance:GetisDrawOutByLayer(self.cur_layer_index)
    if is_draw_out then
        NewXunbaoWGCtrl.Instance:SendTaoBaoOp(TAOBAO_OP_TYPE.RESET, self.cur_layer_index)
    else
        self:GetSetIsPrepareStatus(1)
        self:PlayStartAnim()
    end
    self:ChangeToggleInteractable(false)
    self.node_list["btn_start"]:SetActive(false)
end

--打开功能描述
function NewXunbaoView:OnClickOpenLongTip()
    --NewXunbaoWGCtrl.Instance:OpenXunBaoTip()
    RuleTip.Instance:SetContent(Language.Xunbao.TipContent1, Language.Xunbao.TipTitle1)
end

--切换层数
function NewXunbaoView:OnClickLongLayer(layer_index)
    --print_error("layer", layer_index)
    layer_index = LAYER_LIST[self.show_index + layer_index]
    self.cur_layer_index = layer_index
    local cell_list = NewXunbaoWGData.Instance:GetDefaultLongCellListByLayer(layer_index)
    if not self["cell_list_" .. layer_index] then
        local list = {}
        for i, v in ipairs(cell_list) do
            table.insert(list, self.all_cell_list[v.pos])
        end
        self["cell_list_" .. layer_index] = list
    end

    self:FlushLongCommon()
    self:FlushLongRawBg()

    self:ResetAllCell()
end

function NewXunbaoView:ResetCellData()
    local layer_index = self.cur_layer_index
    local cell_list = NewXunbaoWGData.Instance:GetDefaultLongCellListByLayer(layer_index)
    local is_prepare_status = self:GetSetIsPrepareStatus()
    local reward_list = NewXunbaoWGData.Instance:GetTBDrawLayerInfoByLayer(layer_index)
    reward_list = reward_list.draw_id_list or {}

    local t_data
    for i, v in pairs(self.all_cell_list) do
        local status = false
        for m, n in pairs(cell_list) do
            if i == n.pos then
                status = true
                t_data = {}
                t_data.id = reward_list[m] or 0
                t_data.layer = n.layer
                t_data.is_prepare = is_prepare_status
                t_data.reward_item = n.reward_item
                t_data.pos = m-1
                t_data.ori_pos = i
                v:SetData(t_data)
                break
            end
        end
        v:SetAnimActive(status)
        v:ResetParent()
    end
end

--切换背景和消耗图标
function NewXunbaoView:FlushLongRawBg()
    local raw_bg = NewXunbaoWGData.Instance:GetTBLongLayerImageByLayer(self.cur_layer_index)
    if raw_bg then
        local bundle, asset = ResPath.GetF2RawImagesPNG(raw_bg)
        self.node_list["raw_bg"].raw_image:LoadSprite(bundle, asset)
    end
    local cur_draw_info = NewXunbaoWGData.Instance:GetCurDrawInfo(self.cur_layer_index)
    if not cur_draw_info then
        return
    end
    local bundle, asset = ResPath.GetItem(cur_draw_info.consume_item_id)
    self.node_list["long_cost_icon"].image:LoadSprite(bundle, asset, function()
        self.node_list["long_cost_icon"].image:SetNativeSize()
    end)

    self.node_list["txt_long_tip"].text.text = Language.Xunbao.LongLayerTip[self.cur_layer_index] or ""
end

function NewXunbaoView:PlayCardAudio()
    AudioManager.PlayAndForget(ResPath.UiseRes("effect_zhenlong_xipai"))
end

--播放洗牌动画，限制点击
function NewXunbaoView:PlayStartAnim(need_flush)
    self:PlayCardAudio()
    self.onclick_cell = true
    self.play_shuffle_anim = true
    self:ChangeToggleInteractable(false)
    self.node_list["txt_center"]:SetActive(false)
    local cfg = NewXunbaoWGData.Instance:GetTBLongPerformanceCfg()
    local fire_start_time = (cfg.card_rot_time or 0.5) + (cfg.to_center_time or 1.5)
    local fire_dur_time = cfg.fire_dur_time or 2
    local all_time = cfg.card_rot_time + cfg.to_center_time*2 + fire_dur_time
    --print_error("changtoggle")

    self.xipai_sequence = DG.Tweening.DOTween.Sequence()
    --聚到中间
    self.xipai_sequence:AppendCallback(function()
        local list = self["cell_list_" .. self.cur_layer_index]
        for i, v in pairs(list) do
            v:PlayCardEffect(false)
            v:PlayCellStartAnim(self.node_list["cell17"])
        end
    end)
    --喷火
    self.xipai_sequence:AppendInterval(fire_start_time)
    self.xipai_sequence:AppendCallback(function()
        self.node_list["effect_huo"]:SetActive(true)
    end)

    --关闭idle特效
    self.xipai_sequence:InsertCallback(fire_start_time + 0.1, function()
        self.node_list["effect_idle"]:SetActive(false)
    end)

    --播放卡牌特效
    self.xipai_sequence:AppendInterval(fire_dur_time/2)
    self.xipai_sequence:AppendCallback(function()
        self:PlayCardEffect(true)
    end)

    --切换idle特效,卡牌归位
    self.xipai_sequence:AppendInterval(fire_dur_time/2)
    self.xipai_sequence:AppendCallback(function()
        self.node_list["effect_idle"]:SetActive(true)
        self:PlayMoveBackAnim()
    end)

    --关闭火特效
    self.xipai_sequence:InsertCallback(fire_start_time + fire_dur_time + 0.2, function()
        self.node_list["effect_huo"]:SetActive(false)
    end)

    --恢复正常
    self.xipai_sequence:AppendInterval(cfg.to_center_time)
    self.xipai_sequence:AppendCallback(function()
        self.onclick_cell = nil
        self:ResetAllCell()
        self.play_shuffle_anim = false
        self:ChangeToggleInteractable(true)
    end)

    if need_flush then
        --刷新界面
        self.xipai_sequence:AppendInterval(0.1)
        self.xipai_sequence:AppendCallback(function()
            self:OnClickLongLayer(self.cur_layer_index%2+1)
        end)
    end
end

--播放cell回到初始位置的动画
function NewXunbaoView:PlayMoveBackAnim()
    local list = self["cell_list_" .. self.cur_layer_index]
    for i, v in pairs(list) do
        v:PlayMoveBackAnim()
    end
    local to_center_time = NewXunbaoWGData.Instance:GetTBLongPerformanceCfg().to_center_time
    self:DelayFlushCommon(to_center_time)
end

--播放卡牌特效
function NewXunbaoView:PlayCardEffect(status)
    local list = self["cell_list_" .. self.cur_layer_index]
    for i, v in pairs(list) do
        v:PlayCardEffect(status)
    end
end

--延时刷新公共信息
function NewXunbaoView:DelayFlushCommon(time)
    self:ChangeToggleInteractable(false)
    GlobalTimerQuest:AddDelayTimer(function()

        self:FlushLongCommon()
    end, time)

    if self.delay_flush_toggle then
        GlobalTimerQuest:CancelQuest(self.delay_flush_toggle)
    end
    self.delay_flush_toggle = GlobalTimerQuest:AddDelayTimer(function()
        self:ChangeToggleInteractable(true)
        self.delay_flush_toggle = nil
    end, time)
end

--获取点击cell弹窗
function NewXunbaoView:GetLongAlert()
    if not self.alert then
        self.alert = Alert.New()
        self.alert:SetShowCheckBox(true, "xunbao_click")
    end
end

--点击cell抽奖
function NewXunbaoView:OnClickCell(cell)
    if self.onclick_cell then
       return
    end
    self.onclick_cell = true
    self:ChangeToggleInteractable(false)

    local callback = function()
        self:SendLongDraw(cell.data.pos)
    end

    local close_func = function ()
        self:ChangeToggleInteractable(true)
    end

    local dif_num, shop_seq = NewXunbaoWGData.Instance:CheckEnoughDraw(self.cur_layer_index)
    if dif_num and dif_num > 0 then
        self:GetLongAlert()
        local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(shop_seq)
        local item_cfg = ItemWGData.Instance:GetItemConfig(shop_cfg.itemid)
        local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
        local str = string.format(Language.Xunbao.NotEnough, dif_num, item_name, shop_cfg.price*dif_num)
        self.alert:SetLableString(str)
        self.alert:SetOkFunc(callback)
        self.alert:SetCloseFunc(close_func)
        self.alert:SetCancelFunc(close_func)
        self.alert:Open()
    else
        callback()
    end

    GlobalTimerQuest:AddDelayTimer(function()
        self.onclick_cell = nil
    end,0.3)
end

--发送抽奖协议
function NewXunbaoView:SendLongDraw(pos)
    NewXunbaoWGCtrl.Instance:SendTaoBaoOp(TAOBAO_OP_TYPE.DRAW, self.cur_layer_index, pos)
end

--播放落地特效，因为层级原因提取override到公共父物体上
function NewXunbaoView:PlayCellEffect(effect_level, pos, callback)
    local other_cfg = NewXunbaoWGData.Instance:GetTBLongPerformanceCfg()
    local break_time = other_cfg.break_time or 0.4
    local bundle = string.format("effects2/prefab/ui/kapaiza/ui_kapai_za%d_prefab", effect_level)
    local asset = "UI_kapai_za" .. effect_level
    local ori_pos = self.node_list["cell" .. pos].rect.anchoredPosition
    if not self.effect_pos_change then
        self.effect_pos_change = self.node_list["kapai_effect"].transform.sizeDelta/2
    end
    ori_pos = Vector2(ori_pos.x - self.effect_pos_change.x, ori_pos.y + self.effect_pos_change.y)
    EffectManager.Instance:PlayAtTransform(bundle, asset, self.node_list["kapai_effect"].transform, break_time,
            ori_pos, nil, Vector3(20,20,20))
    GlobalTimerQuest:AddDelayTimer(callback, break_time)
end

--明牌动画
function NewXunbaoView:OnFlushLongShuffle()
    self:ResetAllCell()
    self:ChangeToggleInteractable(false)
    local list = NewXunbaoWGData.Instance:GetShuffleLastRewardId(self.cur_layer_index)
    local near_cell = self:CheckNearestCell()
    local cfg = NewXunbaoWGData.Instance:GetTBLongPerformanceCfg()
    local shuffle_delay = cfg.shuffle_delay or 2
    --明牌
    if near_cell then
        near_cell.data.id = list[1].reward_id
        near_cell.data.reward_item = list[1].reward_item
        table.remove(list, 1)
    end

    self.show_card_anim = DG.Tweening.DOTween.Sequence()
    self.show_card_anim:AppendCallback(function()
        for i, v in pairs(self["cell_list_" .. self.cur_layer_index]) do
            if v.open_type then
                if v ~= near_cell then
                    local rand_num = GameMath.Rand(1, #list)
                    v.data.id = list[rand_num].reward_id
                    v.data.reward_item = list[rand_num].reward_item
                    table.remove(list, rand_num)
                end
                v:FlushCell()
                v:PlayRotAnim(true)
            --else
            --    print_error(v.data)
            end
        end
    end)

     ----洗牌
    self.show_card_anim:AppendInterval(shuffle_delay)
    self.show_card_anim:AppendCallback(function()
        NewXunbaoWGData.Instance:FlushShuffleCahce()
        self:PlayStartAnim(true)
    end)

    self.show_card_anim:OnComplete(function()
        self.show_card_anim:Kill()
        self.show_card_anim = nil
    end)
end

function NewXunbaoView:CheckNearestCell()
    local last_pos = NewXunbaoWGData.Instance:GetShffleLastPos()
    local reward_list = NewXunbaoWGData.Instance:GetTBDrawLayerInfoByLayer(self.cur_layer_index)
	reward_list = reward_list.draw_id_list or {}

    local last_index
    local layer_cell_list = self["cell_list_" .. self.cur_layer_index]
    for i, v in pairs(layer_cell_list) do
        if v.data.pos == last_pos then
            last_index = v.data.ori_pos
            break
        end
    end
--print_error("last", last_index)
    local pos
    local len = 11--一排11个格子
    local near_cell_list = {}
    for dis = 1, len do
        for i = -dis,dis,1 do
            local cen_pos = last_index + i * len
            local pos =  math.floor(cen_pos / len - 0.01) * len
            if pos >= 0 and pos <= 33 then--限制index在1-44之间
                local min = pos + 1
                local max = pos + len
                --print_error(cen_pos - dis, cen_pos + dis, "||", pos,"||", min ,max)
                for index = cen_pos - dis, cen_pos + dis do
                    if index >= min and index <= max then
                        local cell = self.all_cell_list[index]
                        if cell then
                            local data = cell:GetData()
                            if data and cell.open_type and data.layer == self.cur_layer_index then
                                --print_error(data.ori_pos)
                                table.insert(near_cell_list, self.all_cell_list[index])
                            end
                        end
                    end
                end

            end
        end
        --print_error("???", #near_cell_list)
        if not IsEmptyTable(near_cell_list) then
            return near_cell_list[GameMath.Rand(1, #near_cell_list)]
        end
    end
end

function NewXunbaoView:ResetAllCell()
    if not self.all_cell_list then
        return
    end

    if not self.cur_layer_index then
        return
    end

    if self.xipai_sequence then
        self.xipai_sequence:Kill()
        self.xipai_sequence = nil
    end

    if self.show_card_anim then
        self.show_card_anim:Kill()
        self.show_card_anim = nil
    end

    self:ResetCellData()

    for i, v in pairs(self.all_cell_list) do
        v:ResetCell(self.node_list["cell" .. i])
    end
end
----------------------------------------------------------------------------------
XunBaoBigRewardRender = XunBaoBigRewardRender or BaseClass(BaseRender)
function XunBaoBigRewardRender:LoadCallBack()
    self.cell = ItemCell.New(self.node_list["big_reward"])
    self.cell:UseNewSelectEffect(true)
end

function XunBaoBigRewardRender:__delete()
    if self.cell then
        self.cell:DeleteMe()
        self.cell = nil
    end
end

function XunBaoBigRewardRender:OnFlush()
    self.cell:SetData(self.data.reward_item)
    self.cell:SetSelectEffect(self.data.has_get)
end