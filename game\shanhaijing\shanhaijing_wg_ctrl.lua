require("game/shanhaijing/shanhaijing_wg_data")
require("game/shanhaijing/shanhaijing_view")
require("game/shanhaijing/shanhaijing_tj_view")
require("game/shanhaijing/shanhaijing_sl_view")
require("game/shanhaijing/shanhaijing_zuhe_view")
require("game/shanhaijing/shanhaijing_resolve_view")
require("game/shanhaijing/shanhaijing_attrinfo_view")
require("game/shanhaijing/shanhaijing_skill_view")
-- require("game/shanhaijing/shenshou_shangzhen_view")

ShanHaiJingWGCtrl = ShanHaiJingWGCtrl or BaseClass(BaseWGCtrl)
function ShanHaiJingWGCtrl:__init()
	if ShanHaiJingWGCtrl.Instance ~= nil then
		ErrorLog("[ShanHaiJingWGCtrl] Attemp to create a singleton twice !")
	end
	ShanHaiJingWGCtrl.Instance = self

	self.view = ShanHaiJingView.New(GuideModuleName.ShanHaiJingView)
	self.shj_zuhe_view = ShanHaiJingZuHeView.New(GuideModuleName.ShanHaiJingZuHeView)
	self.shj_fenjie_view = SHJFenjieView.New(GuideModuleName.SHJFenjieView)
	self.shj_attrinfo_view = SHJAttrView.New(GuideModuleName.SHJAttrView)
	self.shj_skill_view = SHJSkillTipView.New(GuideModuleName.SHJSkillTipView)
	-- self.shenshou_shangzhen_view = ShenShouShangZhenView.New()

	self.data = ShanHaiJingWGData.New()
	self:RegisterAllProtocols()

end

function ShanHaiJingWGCtrl:__delete()
	ShanHaiJingWGCtrl.Instance = nil

	-- self.shenshou_shangzhen_view:DeleteMe()
	-- self.shenshou_shangzhen_view = nil

	self.view:DeleteMe()
	self.view = nil

	self.data:DeleteMe()
	self.data = nil

	self.shj_zuhe_view:DeleteMe()
	self.shj_zuhe_view = nil

	self.shj_fenjie_view:DeleteMe()
	self.shj_fenjie_view = nil

	self.shj_attrinfo_view:DeleteMe()
	self.shj_attrinfo_view = nil

	self.shj_skill_view:DeleteMe()
	self.shj_skill_view = nil
end

function ShanHaiJingWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCTujianSingleInfo, "OnSCTujianSingleInfo")
	self:RegisterProtocol(SCTujianAllInfo, "OnSCTujianAllInfo")
	self:RegisterProtocol(SCTujianGroupInfo, "OnSCTujianGroupInfo")
	self:RegisterProtocol(SCTujianBagInfo, "OnSCTujianBagInfo")
	self:RegisterProtocol(SCTujianBagChangeInfo, "OnSCTujianBagChangeInfo")
	self:RegisterProtocol(CSTujianOperaReq)
	self:RegisterProtocol(CSTuJianBreakList)
end

function ShanHaiJingWGCtrl:ClickHookHandler()
	self.view:ClickHookHandler()
end

function ShanHaiJingWGCtrl:OpenSkillView(data)
	self.shj_skill_view:SetData(data)
end

-- function ShanHaiJingWGCtrl:OpenShenShouSZ()
-- 	if self.shenshou_shangzhen_view then
-- 		self.shenshou_shangzhen_view:Open()
-- 	end
-- end

-- function ShanHaiJingWGCtrl:CloseShenShouSZ()
-- 	if self.shenshou_shangzhen_view:IsOpen() then
-- 		self.shenshou_shangzhen_view:CloseView()
-- 	end
-- end

-- function ShanHaiJingWGCtrl:ShenShouShangZhenFlush()
-- 	if self.shenshou_shangzhen_view:IsOpen() then
-- 		self.shenshou_shangzhen_view:Flush()
-- 	end
-- end

-- 宠图鉴操作请求 7580
function ShanHaiJingWGCtrl:CSTujianOperaReq(req_type,param1,param2)
	-- print_error('CSTujianOperaReq----',req_type,param1,param2)
    local protocol = ProtocolPool.Instance:GetProtocol(CSTujianOperaReq)
	protocol.req_type = req_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
    protocol:EncodeAndSend()
end

function ShanHaiJingWGCtrl:OnSCTujianGroupInfo(protocol)
	--print_error('OnSCTujianGroupInfo----',protocol)
	self.data:SetTJGroupActiveServerInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.ShanHaiJingZuHeView)
	ViewManager.Instance:FlushView(GuideModuleName.ShanHaiJingView)
	RemindManager.Instance:Fire(RemindName.ShanHaiJing_TJ)
	RemindManager.Instance:Fire(RemindName.ShanHaiJing_LuoShenCe)
end

function ShanHaiJingWGCtrl:OnSCTujianSingleInfo(protocol)
	--print_error('OnSCTujianSingleInfo----',protocol)
	self.data:SetTJSingleServerInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.ShanHaiJingView, nil, "all", {flush_single_item = protocol})
	RemindManager.Instance:Fire(RemindName.ShanHaiJing_TJ)
	RemindManager.Instance:Fire(RemindName.ShanHaiJing_LuoShenCe)
	StepExcuteManager.Instance:Fire(StepExcuteKey.TuJian)
end

function ShanHaiJingWGCtrl:OnSCTujianAllInfo(protocol)
	--print_error('OnSCTujianAllInfo----',protocol)
	self.data:SetTJAllServerInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.ShanHaiJingView)
	RemindManager.Instance:Fire(RemindName.ShanHaiJing_TJ)
	RemindManager.Instance:Fire(RemindName.ShanHaiJing_LuoShenCe)
end

-- 背包信息
function ShanHaiJingWGCtrl:OnSCTujianBagInfo(protocol)
	--print_error("OnSCTujianBagInfo------",protocol)
	self.data:SetBagInfo(protocol)
	RemindManager.Instance:Fire(RemindName.ShanHaiJing_TJ)
	RemindManager.Instance:Fire(RemindName.ShanHaiJing_LuoShenCe)
	ViewManager.Instance:FlushView(GuideModuleName.SHJFenjieView)
	ViewManager.Instance:FlushView(GuideModuleName.ShanHaiJingView, nil, "all", {tujian_bag_change = true})
end

--背包信息改变
function ShanHaiJingWGCtrl:OnSCTujianBagChangeInfo(protocol)
	--print_error("OnSCTujianBagChangeInfo------",protocol)
	self.data:SetChangeBagInfo(protocol)
	-- if self.view:IsOpen() then
	-- 	self.view:Flush()
	-- end
	ViewManager.Instance:FlushView(GuideModuleName.ShanHaiJingView, nil, "all",  {tujian_bag_change = true})
	ViewManager.Instance:FlushView(GuideModuleName.SHJFenjieView)
	RemindManager.Instance:Fire(RemindName.ShanHaiJing_TJ)
	RemindManager.Instance:Fire(RemindName.ShanHaiJing_LuoShenCe)
end

function ShanHaiJingWGCtrl:ShakeRoot()
	local perform_cfg = NewXunbaoWGData.Instance:GetTBLongPerformanceCfg()
	local shake_frame_num = perform_cfg.shake_frame_num or 4
	local shake_pixel = perform_cfg.shake_pixel or 30
	local count = 0
	GlobalTimerQuest:AddTimesTimer(function()
		count = count + 1
		local rand_num_x = 0
		local rand_num_y = 0
		local rand_num_z = 0
		if count ~= shake_frame_num then
			rand_num_x = GameMath.Rand(-shake_pixel,shake_pixel)
			rand_num_y = GameMath.Rand(-shake_pixel,shake_pixel)
			rand_num_z = GameMath.Rand(-shake_pixel,shake_pixel)
		end
		self.view.root_node_transform.anchoredPosition = Vector3(rand_num_x, rand_num_y, rand_num_z)
	end, 0, shake_frame_num)
end

function ShanHaiJingWGCtrl:FlushView(index)
	if self.view:IsOpen() then
		self.view:Flush(index)
	end
end

-- 图鉴一键分解 7586
function ShanHaiJingWGCtrl:SendBreakListOpera(list)
    local protocol = ProtocolPool.Instance:GetProtocol(CSTuJianBreakList)
    protocol.consume_list = list or {}
	protocol.count = #protocol.consume_list or 0
    protocol:EncodeAndSend()
end

local TypeUIGrey = typeof(UIGrey)
local greyMat = nil
-- 图片置灰 --tue:置灰 false：非置灰
function ShanHaiJingWGCtrl:SetGraphicGrey(node, is_grey)
	local uiGrey = node:GetComponent(TypeUIGrey)
	if nil == uiGrey and is_grey then
		uiGrey = node:GetOrAddComponent(TypeUIGrey)

		if nil == greyMat then
			greyMat = ResPoolMgr:TryGetMaterial("misc/material", "UI_SHJGrey")
		end
	end

	if nil ~= uiGrey then
		uiGrey:SetIsGrey(is_grey, greyMat)
	end
end
