function WorldServerView:DeleteKfDropRecordView()
	if self.kf_drop_list ~= nil then
		self.kf_drop_list:DeleteMe()
		self.kf_drop_list = nil
	end
end

function WorldServerView:InitKfDropRecordView(index, loaded_times)
	self.node_list.layout_blank_tip2:SetActive(false)
	self.kf_drop_list = AsyncListView.New(KfDropRender, self.node_list["ph_kf_drop_show"])
end

function WorldServerView:OnFlushKfDropRecordView()
	local kf_drop_list = BossWGData.Instance:GetCrossBossDropRecord()
	if kf_drop_list == nil or #kf_drop_list == 0 then
		self.node_list.layout_blank_tip2:SetActive(true)
	else
		self.node_list.layout_blank_tip2:SetActive(false)
	end
	if self.kf_drop_list then
		self.kf_drop_list:SetDataList(kf_drop_list)
	end
end

-------------------------------------------------------------------------------------------
KfDropRender = KfDropRender or BaseClass(BaseRender)
function KfDropRender:__delete()
end

function KfDropRender:LoadCallBack()
	self.node_list["item_name"].button:AddClickListener(BindTool.Bind(self.OnClickItem, self))
end

function KfDropRender:OnFlush()
	--local star_list = self.data.item_data.param.xianpin_type_list
	--local star_num = self.data.item_data.param.star_level
	local item_config = ItemWGData.Instance:GetItemConfig(self.data.item_data.item_id)
	local str = "【%s】"
	local item_msg = string.format(str, item_config.name)
	local map_name = Config_scenelist[self.data.scene_id] and Config_scenelist[self.data.scene_id].name or ""
	local boss_info = BossWGData.Instance:GetMonsterInfo(self.data.monster_id)
	local boss_name = string.format(str, boss_info.name)
	local time = os.date("%m-%d  %X", self.data.pickup_timestamp)
	self.node_list["time"].text.text = time
	self.node_list["rich_content"].text.text = string.format(Language.Boss.DropRecord1, self.data.role_name, map_name, boss_name)
	self.node_list["img9_drop_render_bg"]:SetActive(self.index % 2 == 1)
	self.node_list["item_name"].text.text = item_msg
	ChangeToQualityText(self.node_list["item_name"], item_config.color)
end

function KfDropRender:OnClickItem()
	if not self.data or not self.data.item_data then
		return
	end

	if ShenShouWGData.Instance:GetIsShenShouEquip(self.data.item_data.item_id) then
		ShenShouWGCtrl.Instance:OpenShenShouEquipTip(self.data.item_data)
	else
		TipWGCtrl.Instance:OpenItem(self.data.item_data)
	end
end

