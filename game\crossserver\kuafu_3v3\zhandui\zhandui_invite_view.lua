ZhanDuiInviteView = ZhanDuiInviteView or BaseClass(SafeBaseView)

function ZhanDuiInviteView:__init()
    self:SetMaskBg()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", 
	{vector2 = Vector2(0, -4), sizeDelta = Vector2(814, 580)})
	self:AddViewResource(0, "uis/view/zhandui_ui_prefab", "layout_zhandui_invite")
	self.select_index = 0
	self.data_list = {}
end
function ZhanDuiInviteView:ReleaseCallBack()
    if self.invite_list then
        self.invite_list:DeleteMe()
        self.invite_list = nil
    end

	if self.near_role_list_ret_event then
		GlobalEventSystem:UnBind(self.near_role_list_ret_event)
		self.near_role_list_ret_event = nil
	end
end

--打开界面请求一大波数据
function ZhanDuiInviteView:OpenCallBack()
	SocietyWGCtrl.Instance:SendFriendInfoReq()
	NewTeamWGCtrl.Instance:SendGetNearRoleList()
	if 0 ~= RoleWGData.Instance.role_vo.guild_id then
		GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, RoleWGData.Instance.role_vo.guild_id)
	end
end

function ZhanDuiInviteView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.NewTeam.TitleInvite
    --XUI.AddClickEventListener(self.node_list.btn_create, BindTool.Bind(self.OnClickCreate, self))
    self.invite_list = AsyncListView.New(ZhanDuiInviteRender, self.node_list.ph_invite_list)
    for i=1,3 do
		self.node_list["ph_btn_" .. i].toggle:AddClickListener(BindTool.Bind(self.OnClickInviteSelect, self, i))
	end
	self.node_list.btn_invite_all.button:AddClickListener(BindTool.Bind(self.OnClickInviteAll,self))
	self:OnClickInviteSelect(1)

	self.near_role_list_ret_event = GlobalEventSystem:Bind(OtherEventType.NearRoleListRet, BindTool.Bind(self.OnFlushNearRoleList, self))
end

function ZhanDuiInviteView:ShowIndexCallBack()
	self:Flush()
end

function ZhanDuiInviteView:OnFlushNearRoleList()
	self:Flush()
end

function ZhanDuiInviteView:OnFlush()
	local data_list = {}
	if 1 == self.select_index then
		local temp_list = TableCopy(NewTeamWGData.Instance:GetFriendList())
		local init_uuid = MsgAdapter.InitUUID()
		for i, v in pairs(temp_list) do
			if v.is_online == 1 and KF3V3WGCtrl.Instance:CheckOpenFun(v.level) and (v.zhandui_id and v.zhandui_id == init_uuid) then
				local data = v
				data.team_type = TEAM_INVITE_TYPE.FRIEND
				table.insert(data_list, data)
			end
		end

	elseif 2 == self.select_index then
		data_list = NewTeamWGData.Instance:GetNearRoleList()
		local init_uuid = MsgAdapter.InitUUID()

		for i = #data_list, 1, -1 do
			if not KF3V3WGCtrl.Instance:CheckOpenFun(data_list[i].level) or data_list[i].zhandui_id ~= init_uuid then
				table.remove(data_list, i)
			end
		end
	elseif 3 == self.select_index then
		local m_list = GuildDataConst.GUILD_MEMBER_LIST
		local init_uuid = MsgAdapter.InitUUID()
		for i = 1, m_list.count do
			local item = m_list.list[i]
			if item.uid ~= RoleWGData.Instance:InCrossGetOriginUid() and 1 == item.is_online then
				local datasource = {user_id = item.uid, gamename = item.role_name, level = item.level, sex = item.sex,
						prof = item.prof, team_index = item.team_index, post = item.post,  is_online = item.is_online, shield_vip_flag = item.shield_vip_flag,
						join_time = item.join_time,capability = item.capability, zhandui_id = item.zhandui_id, vip_level = item.vip_level}
				if KF3V3WGCtrl.Instance:CheckOpenFun(datasource.level) and datasource.zhandui_id == init_uuid then
					table.insert(data_list, datasource)
				end
			end
		end
	end
	self.data_list = data_list

	if nil ~= self.data_list and nil ~= self.invite_list then
		self.invite_list:SetDataList(self.data_list)
	end

	if #self.data_list == 0 then
		self.node_list["layout_blank_tip"]:SetActive(true)
		self.node_list["btn_invite_all"]:SetActive(false)
		if self.select_index == 1 then
			self.node_list["lbl_tips"].text.text = (Language.NewTeam.Friends)
		elseif self.select_index == 2 then
			self.node_list["lbl_tips"].text.text = (Language.NewTeam.Nearby)
		elseif self.select_index == 3 then
			if 0 ~= RoleWGData.Instance.role_vo.guild_id then
				self.node_list["lbl_tips"].text.text = (Language.NewTeam.FairyUnion)
			else
				self.node_list["lbl_tips"].text.text = (Language.Common.PleaseJoinGuild)
			end
		end
	else
		self.node_list["btn_invite_all"]:SetActive(true)
		self.node_list["layout_blank_tip"]:SetActive(false)
	end

end
function ZhanDuiInviteView:OnClickInviteSelect(index , force_flush)
	if index == self.select_index and not force_flush then return end
	if 1 == index then
		SocietyWGCtrl.Instance:SendFriendInfoReq()
	elseif 2 == index then
		NewTeamWGCtrl.Instance:SendGetNearRoleList()
	elseif 3 == index then
		if 0 ~= RoleWGData.Instance.role_vo.guild_id then
			GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, RoleWGData.Instance.role_vo.guild_id)
		--else
		--	SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.PleaseJoinGuild)
		end
	end
	self.select_index = index
	self:Flush()
end
function ZhanDuiInviteView:OnClickInviteAll()
	-- local data_list = NewTeamWGData.Instance:GetNearRoleList()
	local init_uuid = MsgAdapter.InitUUID()
	for k,v in pairs(self.data_list) do
		if v.zhandui_id == init_uuid then
			ZhanDuiWGData.Instance:AddCacheCDList(v.user_id)
			ZhanDuiWGCtrl.Instance:SendInviteZhanDui(v.user_id)
		end
	end
	SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.AutoInviteSucessTip)
end

function ZhanDuiInviteView:ForceFlushInviteList()
	if not self:IsOpen() then return end
	if ZhanDuiWGData.Instance:GetZhanDuiMemberCount() == 3 then
		self:Close()
	end
	local index = self.select_index or 2
	if 1 == index then
		SocietyWGCtrl.Instance:SendFriendInfoReq()
	elseif 2 == index then
		NewTeamWGCtrl.Instance:SendGetNearRoleList()
	elseif 3 == index then
		if 0 ~= RoleWGData.Instance.role_vo.guild_id then
			GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, RoleWGData.Instance.role_vo.guild_id)
		end
	end
end

function ZhanDuiInviteView:FlushTextInvite()
    if not IsEmptyTable(self.invite_list.cell_list) then
        for k, v in pairs(self.invite_list.cell_list) do
            v:FlushTextInvite()
        end
    end
end

--------------------------------------------------------------

ZhanDuiInviteRender = ZhanDuiInviteRender or BaseClass(BaseRender)

function ZhanDuiInviteRender:__init()
	self.is_myself = false
	XUI.AddClickEventListener(self.node_list["btn_invite"], BindTool.Bind1(self.OnClickInvite, self))
	XUI.AddClickEventListener(self.node_list["head_click"], BindTool.Bind1(self.OnClickHead, self))
	self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
end

function ZhanDuiInviteRender:__delete()
	if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end
end

function ZhanDuiInviteRender:OnFlush()
	if nil == self.data then
		return
	end

	self:FlushTextInvite()

	-- self.node_list["text_invite"].text.text = (Language.NewTeam.Invite)

	--local str = string.format(Language.NewTeam.ApplyViewRoleName, self.data.gamename, self.data.level)
	--EmojiTextUtil.ParseRichText(self.node_list["lbl_role_name"].emoji_text, str, 21, TEXT_COLOR.GRAY_RED)
	--NewTeamWGCtrl.CheckDianFengLevel(self.data.level, self.node_list.dianfeng_icon, self.node_list.lbl_role_level)
	local is_vis, level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
	self.node_list.lbl_role_name.text.text = self.data.gamename

	local is_vis, level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
	self.node_list.dianfen_img:SetActive(is_vis)
    self.node_list.lbl_level.text.text = level

	if self.data.vip_level then   -- 设置VIP等级
		local is_hide_vip = SettingWGData.Instance:IsHideRoleVipLv(self.data.shield_vip_flag)
        self.node_list.vip_level:SetActive(self.data.vip_level >= 1)
        self.node_list.vip_level.text.text = is_hide_vip and "V" or "V" .. self.data.vip_level
    end
	
	--self.node_list.power_icon.image:LoadSprite(ResPath.GetCommonImages(RoleWGData.GetProfIcon(self.data.prof, self.data.sex)))
	--self.node_list.power_right.text.text = self.data.capability
	local role_id = (self.data.orig_uid and self.data.orig_uid > 0) and self.data.orig_uid or self.data.user_id
	local data = {role_id = role_id, prof = self.data.prof, sex = self.data.sex, fashion_photoframe = 0}
	self.head_cell:SetData(data)
end

function ZhanDuiInviteRender:OnClickInvite()
	ZhanDuiWGData.Instance:AddCacheCDList(self.data.user_id)
	ZhanDuiWGCtrl.Instance:SendInviteZhanDui(self.data.user_id)
	--if 0 == SocietyWGData.Instance:GetIsInTeam() then
	--	local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	--	local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
	--	NewTeamWGCtrl.Instance:SendCreateTeam(team_type, fb_mode, min_level, max_level)
	--end
	--
	--NewTeamWGCtrl.Instance:SendInviteUser(self.data.user_id, self.data.team_type, 1)
	--NewTeamWGCtrl.Instance.invite_view:RemoveItemByUid(self.data.user_id)
end

function ZhanDuiInviteRender:OnClickHead()
	local role_id = (self.data.orig_uid and self.data.orig_uid > 0) and self.data.orig_uid or self.data.user_id
	BrowseWGCtrl.Instance:BrowRoelInfo(role_id, function(param_protocol)
		NewTeamWGCtrl.Instance:CreateInviteRoleHeadCell(param_protocol.role_id, self.data.gamename,param_protocol.prof,param_protocol.is_online, self.node_list.head_click, param_protocol.plat_type, param_protocol.server_id, param_protocol.plat_name)
	end)
end

function ZhanDuiInviteRender:FlushTextInvite()
    local role_id = (self.data.orig_uid and self.data.orig_uid > 0) and self.data.orig_uid or self.data.user_id

    if ZhanDuiWGData.Instance:GetCacheCDByRoleid(role_id) > 0 then
        self.node_list["text_invite"].text.text = ZhanDuiWGData.Instance:GetCacheCDByRoleid(role_id)
        XUI.SetButtonEnabled(self.node_list["btn_invite"], false)
        return
    end
    XUI.SetButtonEnabled(self.node_list["btn_invite"], true)
    self.node_list["text_invite"].text.text = Language.NewTeam.Invite
end