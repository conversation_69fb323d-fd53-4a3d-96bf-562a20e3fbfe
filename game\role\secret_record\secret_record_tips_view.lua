local ATTR_COUNT = 13
local MAX_DISPLAY_NUM = 9
SecretRecordTipsView = SecretRecordTipsView or BaseClass(SafeBaseView)

function SecretRecordTipsView:__init()
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Pop
    self.view_name = "SecretRecordTipsView"
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_attr_tip")
end

function SecretRecordTipsView:LoadCallBack()
	self.attr_list = {}
	self.attr_name_list = {}
	self.attr_value_list = {}
	for i=1,ATTR_COUNT do
		self.attr_list[i] = self.node_list.attr_list:FindObj("attr_" .. i)
		if self.attr_list[i] then
			self.attr_name_list[i] = self.attr_list[i]:FindObj("attr_name")
			self.attr_value_list[i] = self.attr_list[i]:FindObj("attr_value")
		end
	end
	self:Flush()

	self.node_list.attr_scroll:SetActive(true)
end

function SecretRecordTipsView:ReleaseCallBack()
	self.attr_list = {}
	self.attr_name_list = {}
	self.attr_value_list = {}
end

function SecretRecordTipsView:OnFlush()
    local secret_record_data = SecretRecordWGData.Instance
    local bottle_info = secret_record_data:GetBottleInfo()
	local level_info = secret_record_data:GetLevelCfgByLevel(bottle_info.level or 0)
    local next_level_info = secret_record_data:GetLevelCfgByLevel((bottle_info.level or 0) + 1)

	self.node_list.attr_title_name.text.text = Language.SecretRecord.TitleAttrName

	local sort_list = AttributeMgr.SortAttribute()
    local data = AttributeMgr.GetAttributteByClass(level_info)
    local next_level_data = AttributeMgr.GetAttributteByClass(next_level_info)

	local attr_name = Language.Common.AttrName
	local index = 1
	for i,v in ipairs(sort_list) do
		if self.attr_list[index] then
			if (data[v] ~= nil and data[v] > 0) or (next_level_data[v] ~= nil and next_level_data[v] > 0) then
				self.attr_name_list[index].text.text = attr_name[v]
				if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v) then
					self.attr_value_list[index].text.text = "+" .. (data[v]/100).."%"
				else
					self.attr_value_list[index].text.text = "+" .. data[v]
				end
				index = index + 1
			end
		end
	end

    if index < MAX_DISPLAY_NUM then
        self.node_list.attr_scroll.scroll_rect.enabled = false
    else
        self.node_list.attr_scroll.scroll_rect.enabled = true
    end
	for i = 1, ATTR_COUNT do
		self.attr_list[i]:SetActive(i < index)
    end
end
