require("game/cross_lover/loverpk_wg_data")
require("game/cross_lover/loverpk_view")
require("game/cross_lover/loverpk_fszz_view")
require("game/cross_lover/loverpk_qcdj_view")
require("game/cross_lover/loverpk_msg_view")
require("game/cross_lover/loverpk_blessing_view")
require("game/cross_lover/loverpk_bounty_view")
require("game/cross_lover/loverpk_matching_view")
require("game/cross_lover/loverpk_settlement_view")
require("game/cross_lover/loverpk_prepare_scene_view")
require("game/cross_lover/loverpk_fight_scene_view")
require("game/cross_lover/loverpk_msg_dzb_view")
require("game/cross_lover/loverpk_msg_jbjl_view")
require("game/cross_lover/loverpk_msg_fsjl_view")
require("game/cross_lover/loverpk_head_view")
require("game/cross_lover/loverpk_preliminary_end_view")

LoverPkWGCtrl = LoverPkWGCtrl or BaseClass(BaseWGCtrl)

function LoverPkWGCtrl:__init()
    if LoverPkWGCtrl.Instance then
        error("[LoverPkWGCtrl]:Attempt to create singleton twice!")
    end
    LoverPkWGCtrl.Instance = self

    self.data = LoverPkWGData.New()
    self.view = LoverPkView.New(GuideModuleName.LoverPkView)                        --入口界面
    self.loverpk_msg_view = LoverPkMsgView.New(GuideModuleName.LoverPkMsgView)      --金榜
    self.loverpk_prepare_scene_view = LoverPkPrepareSceneView.New()                 -- 仙侣PK 匹配场景
    self.loverpk_settlement_view = LoverPkSettlementView.New()                      --决赛  结算界面
    self.loverpk_matching_view = LoverPkMatchingView.New()                          --匹配信息展示等待界面
    self.loverpk_blessing_view = LoverPkBlessingView.New(GuideModuleName.LoverPKBlessingView)                          --送花界面
    self.loverpk_bounty_view = LoverPkBountyView.New()                              --花球奖励界面
    self.loverpk_fight_scene_view = LoverPkFightSceneView.New()                     --战斗血量展示界面     
    self.loverpk_head_view = LoverPkHeadView.New()                                  -- 头像展示界面
    self.loverpk_preliminary_end_view = LoverPKPreliminaryEndView.New()             --匹配赛积分结算界面 

    self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback)

    self:RegisterAllProtocals()
end

function LoverPkWGCtrl:__delete()
    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

    if self.view then
        self.view:DeleteMe()
        self.view = nil
    end

    if self.loverpk_bounty_view then
        self.loverpk_bounty_view:DeleteMe()
        self.loverpk_bounty_view = nil
    end

    if self.loverpk_fight_scene_view then
        self.loverpk_fight_scene_view:DeleteMe()
        self.loverpk_fight_scene_view = nil
    end

    if self.loverpk_msg_view then
        self.loverpk_msg_view:DeleteMe()
        self.loverpk_msg_view = nil
    end

    if self.loverpk_prepare_scene_view then
        self.loverpk_prepare_scene_view:DeleteMe()
        self.loverpk_prepare_scene_view = nil
    end

    if self.loverpk_settlement_view then
        self.loverpk_settlement_view:DeleteMe()
        self.loverpk_settlement_view = nil
    end

    if self.loverpk_matching_view then
        self.loverpk_matching_view:DeleteMe()
        self.loverpk_matching_view = nil
    end

    if self.loverpk_blessing_view then
        self.loverpk_blessing_view:DeleteMe()
        self.loverpk_blessing_view = nil
    end

    if self.loverpk_head_view then
        self.loverpk_head_view:DeleteMe()
        self.loverpk_head_view = nil
    end

    if self.loverpk_preliminary_end_view then
        self.loverpk_preliminary_end_view:DeleteMe()
        self.loverpk_preliminary_end_view = nil
    end

    if self.item_data_change_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
		self.item_data_change_callback = nil
	end

    LoverPkWGCtrl.Instance = nil
    self:DeleteMatchTimer()
end

function LoverPkWGCtrl:RegisterAllProtocals()
    self:RegisterProtocol(CSCrossCouple2V2Operate)
	self:RegisterProtocol(SCCrossCouple2V2PKKillInfo, "OnSCCrossCouple2V2PKKillInfo")
    self:RegisterProtocol(SCCrossCouple2V2GuessInfo, "OnSCCrossCouple2V2GuessInfo")
    self:RegisterProtocol(SCCrossCouple2V2BaseInfo, "OnSCCrossCouple2V2BaseInfo")
    self:RegisterProtocol(SCCross2V2MatchingInfo, "OnSCCross2V2MatchingInfo")
    self:RegisterProtocol(SCCross2V2MatchingCancel, "OnSCCross2V2MatchingCancel")
    self:RegisterProtocol(SCCrossCouple2V2PKResult, "OnSCCrossCouple2V2PKResult")
    self:RegisterProtocol(SCCrossCouple2v2Rank, "OnSCCrossCouple2v2Rank")
    self:RegisterProtocol(SCCross2V2WinnerInfo, "OnSCCross2V2WinnerInfo")
    self:RegisterProtocol(SCCross2V2KnockoutMatchInfo, "OnSCCross2V2KnockoutMatchInfo")
    self:RegisterProtocol(SCCrossCouple2V2GuessListInfo, "OnSCCrossCouple2V2GuessListInfo")
    self:RegisterProtocol(SCCross2V2CoupleInfo, "OnSCCross2V2CoupleInfo")
    self:RegisterProtocol(SCCrossCouple2V2PKSceneInfo, "OnSCCrossCouple2V2PKSceneInfo")
    self:RegisterProtocol(SCCrossCouple2V2PKMatchResult, "OnSCCrossCouple2V2PKMatchResult")
    self:RegisterProtocol(SCCrossCouple2v2KnockOutRank, "OnSCCrossCouple2v2KnockOutRank")
    self:RegisterProtocol(SCCrossCouple2vTotalFlowersCount, "OnSCCrossCouple2vTotalFlowersCount")
    self:RegisterProtocol(SCCrossCouple2v2StandBySceneInfo, "OnSCCrossCouple2v2StandBySceneInfo")
    self:RegisterProtocol(SCCross2v2KnockOutInfo, "OnSCCross2v2KnockOutInfo")
    self:RegisterProtocol(SCCross2v2KnockOutPlayerRankInfo, "OnSCCross2v2KnockOutPlayerRankInfo")
    self:RegisterProtocol(SCCross2v2GatherNotify, "OnSCCross2v2GatherNotify")
end

-----------------------------------protocol---------------------------------
function LoverPkWGCtrl:SendCSCrossCouple2V2Operate(operate_type, param1, param2, param3)
    -- print_error("-----------SendCSCrossCouple2V2Operate----------", operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossCouple2V2Operate)
	protocol.operate_type = operate_type or 0
 	protocol.param1 = param1 or 0
 	protocol.param2 = param2 or 0
 	protocol.param3 = param3 or 0
 	protocol:EncodeAndSend()
end

function LoverPkWGCtrl:OnSCCrossCouple2V2PKKillInfo(protocol)   --击杀信息
    -- print_error("---------------OnSCCrossCouple2V2PKKillInfo------------------", protocol)
end

function LoverPkWGCtrl:OnSCCrossCouple2V2GuessInfo(protocol) -- 更新单个竞猜信息
    -- print_error("---------------OnSCCrossCouple2V2GuessInfo------------------", protocol)
    self.data:SetSCCrossCouple2V2GuessInfo(protocol)
    RemindManager.Instance:Fire(RemindName.LoverPkFSJL)
    RemindManager.Instance:Fire(RemindName.LoverPKDZB)
    ViewManager.Instance:FlushView(GuideModuleName.LoverPkMsgView)
    ViewManager.Instance:FlushView(GuideModuleName.LoverPkView)
end

function LoverPkWGCtrl:OnSCCrossCouple2V2BaseInfo(protocol)  -- 基础信息个人总积分/伴侣积分/当日/总匹配次数/比赛场次奖励领取标记
    -- print_error("---------------OnSCCrossCouple2V2BaseInfo------------------", protocol)
    self.data:SetSCCrossCouple2V2BaseInfo(protocol)

    RemindManager.Instance:Fire(RemindName.LoverPkQCDJ)
    ViewManager.Instance:FlushView(GuideModuleName.LoverPkView)
    self:FlushLoverPKPrepareSceneView()
    self:FlushLoverPKBlessingView()
end

function LoverPkWGCtrl:OnSCCross2V2MatchingInfo(protocol)     -- 跨服2V2匹配信息 单双人/类型/原因 Cross2V2MatchingInfoNotifyReason 匹配开始时间 双方信息 成功走这里  收到后主动请求进入场景
    -- print_error("---------------OnSCCross2V2MatchingInfo------------------", protocol)
    self.data:SetSCCross2V2MatchingInfo(protocol)
    local notify_reason = protocol.notify_reason

    if notify_reason == Cross2V2MatchingInfoNotifyReason.StartMatching then
        self:OpenLoverPKMatchingView()
		GuajiWGCtrl.Instance:StopGuaji()
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
		
        -- 刷新匹配时间
		if not self.match_time_run_quest then
			self.match_time_run_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.UpdateMatchTime, self), 1)
		end
    elseif notify_reason == Cross2V2MatchingInfoNotifyReason.MatchingSucc then

    end
end

function LoverPkWGCtrl:OnSCCross2V2MatchingCancel(protocol)     -- 匹配取消原因返回
    -- print_error("---------------OnSCCross2V2MatchingCancel------------------", protocol)
    self:CloseLoverPKMatchView(true)
    self.data:SetSCCross2V2MatchingCancel(protocol)

    local notify_reason = protocol.reason_id
    local reason_str = Language.LoverPK.MatchingCancelReason[notify_reason] or Language.LoverPK.MatchingCancelReason[0]
    SysMsgWGCtrl.Instance:ErrorRemind(reason_str)
end

function LoverPkWGCtrl:OnSCCrossCouple2V2PKResult(protocol)             -- 战斗结果  淘汰赛结算界面 
    -- print_error("---------------OnSCCrossCouple2V2PKResult------------------", protocol)
    if self.loverpk_settlement_view then
        self.loverpk_settlement_view:SetDataInfoAndOpen({is_win = protocol.is_win,fight_second = protocol.fight_second, round = protocol.round, side_list = protocol.side_list})
    end
end

function LoverPkWGCtrl:OnSCCrossCouple2v2Rank(protocol)         --跨服仙侣2v2积分排行榜    倾城对决界面排行榜  金榜
    -- print_error("---------------OnSCCrossCouple2v2Rank------------------", protocol)
    self.data:SetSCCrossCouple2v2Rank(protocol)

    ViewManager.Instance:FlushView(GuideModuleName.LoverPkView)
    ViewManager.Instance:FlushView(GuideModuleName.LoverPkMsgView)
end

function LoverPkWGCtrl:OnSCCross2V2WinnerInfo(protocol)           --上一赛季比赛的冠军信息   两人物UUID
    -- print_error("---------------OnSCCross2V2WinnerInfo------------------", protocol)
    self.data:SetSCCross2V2WinnerInfo(protocol)

    ViewManager.Instance:FlushView(GuideModuleName.LoverPkView)
end

function LoverPkWGCtrl:OnSCCross2V2KnockoutMatchInfo(protocol)     --淘汰赛对阵表信息    增量全体
    -- print_error("---------------OnSCCross2V2KnockoutMatchInfo------------------", protocol)
    self.data:SetSCCross2V2KnockoutMatchInfo(protocol)
    RemindManager.Instance:Fire(RemindName.LoverPkFSJL)
    RemindManager.Instance:Fire(RemindName.LoverPKDZB)
    ViewManager.Instance:FlushView(GuideModuleName.LoverPkMsgView)
    ViewManager.Instance:FlushView(GuideModuleName.LoverPkView)

    self:FlushLoverPKBlessingView()
end

function LoverPkWGCtrl:OnSCCrossCouple2V2GuessListInfo(protocol)        --淘汰赛 - 所有的个人已竞猜数据 8756
    -- print_error("---------------OnSCCrossCouple2V2GuessListInfo------------------", protocol)
    self.data:SetSCCrossCouple2V2GuessListInfo(protocol)
    RemindManager.Instance:Fire(RemindName.LoverPkFSJL)
    RemindManager.Instance:Fire(RemindName.LoverPKDZB)
    ViewManager.Instance:FlushView(GuideModuleName.LoverPkMsgView)
    ViewManager.Instance:FlushView(GuideModuleName.LoverPkView)
end

function LoverPkWGCtrl:OnSCCross2V2CoupleInfo(protocol)                 --双人信息   自己的双人信息  两UUId + score积分
    -- print_error("---------------OnSCCross2V2CoupleInfo------------------", protocol)
    self.data:SetSCCross2V2CoupleInfo(protocol)

    ViewManager.Instance:FlushView(GuideModuleName.LoverPkView)
end

function LoverPkWGCtrl:OnSCCrossCouple2V2PKSceneInfo(protocol)          -- 场景信息 玩家血量等信息  对战状态  下一状态时间戳
    -- print_error("---------------OnSCCrossCouple2V2PKSceneInfo------------------")
    self.data:SetSCCrossCouple2V2PKSceneInfo(protocol)
    self:FlushLoverPkFightSceneView()
end

function LoverPkWGCtrl:OnSCCrossCouple2V2PKMatchResult(protocol)        -- 匹配赛战斗结果   
    -- print_error("---------------OnSCCrossCouple2V2PKMatchResult------------------", protocol)

    if self.loverpk_preliminary_end_view then
        self.loverpk_preliminary_end_view:SetDataInfoAndOpen({is_win = protocol.is_win, is_couple = protocol.is_couple})
    end
end

function LoverPkWGCtrl:OnSCCrossCouple2v2KnockOutRank(protocol)         -- 跨服仙侣2v2积分排行榜 淘汰赛排行榜
    -- print_error("---------------OnSCCrossCouple2v2KnockOutRank------------------", protocol)
    self.data:SetSCCrossCouple2v2KnockOutRank(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.LoverPkMsgView)
end

function LoverPkWGCtrl:OnSCCrossCouple2v2StandBySceneInfo(protocol)     --当前比赛类型 匹配赛 淘汰赛
    -- print_error("---------------OnSCCrossCouple2v2StandBySceneInfo------------------", protocol)
    self.data:SetSCCrossCouple2v2StandBySceneInfo(protocol)
end

function LoverPkWGCtrl:OnSCCross2v2KnockOutInfo(protocol)              -- 当前淘汰赛轮次  下一状态时间 本状态时间
    -- print_error("---------------OnSCCross2v2KnockOutInfo------------------", protocol)
    self.data:SetSCCross2v2KnockOutInfo(protocol)
    
    --  轮次改变对阵表数据改变 需要主动请求，跟后端不下发
    LoverPkWGCtrl.Instance:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.QUERY_KNOCKOUT_MATCH_INFO)
    self:FlushLoverPKPrepareSceneView()
    ViewManager.Instance:FlushView(GuideModuleName.LoverPkMsgView)
end

function LoverPkWGCtrl:OnSCCrossCouple2vTotalFlowersCount(protocol)      -- 全服总送花数
    -- print_error("---------------OnSCCrossCouple2vTotalFlowersCount------------------", protocol)
    self.data:SetTotalFlowersCount(protocol)
    self:FlushLoverPKBlessingView()
end

function LoverPkWGCtrl:OnSCCross2v2KnockOutPlayerRankInfo(protocol)      -- 淘汰赛个人排名  轮次/输赢 
    -- print_error("---------------OnSCCross2v2KnockOutPlayerRankInfo------------------", protocol)
    self.data:SetSCCross2v2KnockOutPlayerRankInfo(protocol)
    self:FlushLoverPKPrepareSceneView()
end

-- 收到邀请仙侣PK活动
function LoverPkWGCtrl:HandleReceiveInviteMsg(protocol)
    local alert_func = function ()
        local content = string.format(Language.CrossInvite.InviteLoverPK, protocol.inviter_name)
        TipWGCtrl.Instance:OpenAlertTips(content, function ()
			CrossServerWGCtrl.Instance:SendInviteStartCross(EnumInviteStartCrossOperType.InviteAck, EnumInviteStartCrossReason.LoverPKMatch, protocol.inviter_uid , 0)
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.INVITE_START_LOVER_PK, 0)
            CrossServerWGCtrl.Instance.SendCrossStartReq(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_LOVERPK)
        end, 
        function ()
            MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.INVITE_START_LOVER_PK, 0)
			CrossServerWGCtrl.Instance:SendInviteStartCross(EnumInviteStartCrossOperType.InviteAck, EnumInviteStartCrossReason.LoverPKMatch, protocol.inviter_uid , 1)
        end, nil, nil, 30)
    end
    
    alert_func()

    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.INVITE_START_LOVER_PK, 1, function ()
        alert_func()
		return true
	end)
end

function LoverPkWGCtrl:HandleInviteResultMsg(protocol)
    if protocol.is_reject == 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.CrossInvite.ResultLoverPK, protocol.inviter_name))
	end
end

function LoverPkWGCtrl:OnSCCross2v2GatherNotify(protocol)
    -- print_error("---------------OnSCCross2v2GatherNotify------------", protocol)
    if self.loverpk_prepare_scene_view and self.loverpk_prepare_scene_view:IsOpen() then
        self.loverpk_prepare_scene_view:Flush(0, "reward_box_create")
    end
end

function LoverPkWGCtrl:OnGetMatchCountReward(result, param1)
    local cfg = self.data:GetMatchCountRewardCfgBySeq(param1)
    
    if not IsEmptyTable(cfg) then
        local data_list = SortDataByItemColor(cfg.reward_list)
        TipWGCtrl.Instance:ShowGetReward(nil, data_list)
    end
end

function LoverPkWGCtrl:OnGetLoverPKGatherReward(result, param1)
    local cfg = self.data:GetGatherRewardsCfgByGrade(param1)

    if not IsEmptyTable(cfg) then
        local data_list = SortDataByItemColor(cfg.reward_item)
        TipWGCtrl.Instance:ShowGetReward(nil, data_list)
    end
end

-------------------------------------view-----------------------------------
function LoverPkWGCtrl:OpenLoverPKBountyView()
    if self.loverpk_bounty_view then
        self.loverpk_bounty_view:Open()
    end
end

function LoverPkWGCtrl:OpenLoverPKMsgView(view_index)
    if self.loverpk_msg_view then
        self.loverpk_msg_view:Open(view_index)
    end
end

function LoverPkWGCtrl:OpenLoverPKBlessingView()
    if self.loverpk_blessing_view then
        self.loverpk_blessing_view:Open()
    end
end

function LoverPkWGCtrl:FlushLoverPKBlessingView()
    if self.loverpk_blessing_view then
        self.loverpk_blessing_view:Flush()
    end
end

function LoverPkWGCtrl:OpenLoverPKHeadView()
    if self.loverpk_head_view then
        self.loverpk_head_view:Open()
    end
end

function LoverPkWGCtrl:GetLoverPKPrepareSceneView()
    return self.loverpk_prepare_scene_view
end

function LoverPkWGCtrl:OpenLoverPKPrepareSceneView()
    if self.loverpk_prepare_scene_view then
        self.loverpk_prepare_scene_view:Open()
    end
end

function LoverPkWGCtrl:CloseLoverPKPrepareSceneView()
    if self.loverpk_prepare_scene_view and self.loverpk_prepare_scene_view:IsOpen() then
        self.loverpk_prepare_scene_view:Close()
    end
end

function LoverPkWGCtrl:FlushLoverPKPrepareSceneView()
    if self.loverpk_prepare_scene_view and self.loverpk_prepare_scene_view:IsOpen() then
        self.loverpk_prepare_scene_view:Flush()
    end
end

--打开匹配界面
function LoverPkWGCtrl:OpenLoverPKMatchingView()
    if self.loverpk_matching_view then
        self.loverpk_matching_view:Open()
    end
end

--关闭匹配界面
function LoverPkWGCtrl:CloseLoverPKMatchView(is_clear)
	if is_clear then
		self:DeleteMatchTimer()
        self.loverpk_prepare_scene_view:RefreshBtnTime(true)
	end
	if self.loverpk_matching_view:IsOpen() then
		self.loverpk_matching_view:Close()
	end
end

--删除,重置匹配时间
function LoverPkWGCtrl:DeleteMatchTimer()
	if self.match_time_run_quest then
        GlobalTimerQuest:CancelQuest(self.match_time_run_quest)   
		self.match_time_run_quest = nil
	end		
end

function LoverPkWGCtrl:ReqStartMatch(match_type)
	self:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.MATCH, match_type)
end

--请求取消匹配
function LoverPkWGCtrl:ReqCancelMatch()
	self:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.CANCEL_MATCH)
	self:DeleteMatchTimer()
    if self.loverpk_prepare_scene_view and self.loverpk_prepare_scene_view:IsOpen() and self.loverpk_prepare_scene_view:IsLoaded() then
        --匹配按钮
	    self.loverpk_prepare_scene_view:RefreshBtnTime(true)
    end
end
--刷新匹配时间
function LoverPkWGCtrl:UpdateMatchTime()
    if self.loverpk_prepare_scene_view and self.loverpk_prepare_scene_view:IsOpen() and self.loverpk_prepare_scene_view:IsLoaded() then
        --匹配按钮
	    self.loverpk_prepare_scene_view:RefreshBtnTime()
    end

	--匹配界面
	if self.loverpk_matching_view and self.loverpk_matching_view:IsOpen() and self.loverpk_matching_view:IsLoaded() then
		self.loverpk_matching_view:RefreshState()
	end
end

function LoverPkWGCtrl:GetLoverPkFightSceneView()
    return self.loverpk_fight_scene_view
end

function LoverPkWGCtrl:CloseLoverPkFightSceneView()
    if self.loverpk_fight_scene_view and self.loverpk_fight_scene_view:IsOpen() then
        self.loverpk_fight_scene_view:Close()
    end
end

function LoverPkWGCtrl:OpenLoverPkFightSceneView()
    if self.loverpk_fight_scene_view then
        self.loverpk_fight_scene_view:Open()
    end
end

function LoverPkWGCtrl:FlushLoverPkFightSceneView()
    if self.loverpk_fight_scene_view and self.loverpk_fight_scene_view:IsOpen() then
        self.loverpk_fight_scene_view:Flush()
    end
end

function LoverPkWGCtrl:FightSceneViewShowCountDown()
    if self.loverpk_fight_scene_view and self.loverpk_fight_scene_view:IsOpen() then
        self.loverpk_fight_scene_view:Flush(0, "show_count_down")
    end
end

function LoverPkWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
        local cost_item_id = LoverPkWGData.Instance:GetOtherCfgDataByAttrName("flower_item_id")

        if change_item_id == cost_item_id then
            self:FlushLoverPKBlessingView()
        end
	end
end