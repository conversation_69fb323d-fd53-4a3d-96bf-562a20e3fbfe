require("core/draw_part")

DrawObj = DrawObj or BaseClass()
PRINT_NO_SHOW_SWITCH = false
DrawObj.ObjList = {}
DrawObj.ObjCount = 0
DrawObj.v_root = ResMgr:CreateEmptyGameObj("DrawObjPool", true)
DrawObj.v_root_transform = DrawObj.v_root.transform
DrawObj.v_root:SetActive(false)
local TypeOfActorAnimatorSyncter = typeof(ActorAnimatorSyncter)

function DrawObj:__init(parent_obj, parent_transform)
	self.parent_obj = parent_obj

	-- 控制的根节点.
	self.root = DrawObj.Pop()
	self.root_transform = self.root.transform
	if parent_transform ~= nil then
		self.root_transform:SetParent(parent_transform)
	end
	self.curPos = self.root_transform.position
	self.part_list = {}
	self.shield_part_list = nil
	self.disable_effect_part_list = nil
	self.auto_fly = false
	self.load_complete = nil
	self.obj_type = 0
	self.scene_obj = nil
	self.is_visible = true
	self.is_use_objpool = true
	self.is_enter_objpool = true
	self.is_in_queue_load = true
	self.is_disable_effect = false
	self.quality_override_level = -1
	self.part_material_quality_offset_list = nil
	self.material_quality = 0
	self.mount_loaded_and_up_call_back = nil

	self.part_need_detail_level_list = nil
	self.cur_detail_level = SceneObjDetailLevel.High

	self.is_rolemodel = false
	self.is_hd_texture = false
	self.is_lerp_probe = false
	self.multi_color = nil
	self.is_gray = false
	self.is_can_weapon_point_attach = false
	self.obj_scale = nil
	self.is_force_max_lod = false
	self.is_need_layer_mask_reflection = false

	self.wait_part_loaded_status_list = {}

	self.actor_animator_syncter = nil
    self:ResetWaitSyncAnimPartCache()
end

function DrawObj:__delete()
	if not IsNil(self.actor_animator_syncter) then
		self.actor_animator_syncter:ClearSyncTargetAnimatorData()
		self.actor_animator_syncter.enabled = false
		self.actor_animator_syncter = nil
	end

	for k,v in pairs(self.part_list) do
		DrawPart.Release(v)
	end
	self.part_list = nil

	if self.look_at_point then
		ResMgr:Destroy(self.look_at_point)
		self.look_at_point = nil
	end

	DrawObj.Release(self.root)

	GlobalTimerQuest:CancelQuest(self.delay_set_attached)
	self:CleanPlayPartSyncAnimFixedDelay()
	self.root = nil
	self.parent_obj = nil
	self.scene_obj = nil
	self.can_get_main_point = nil
	self.root_transform = nil
	self.multi_color = nil
	self.load_complete = nil
	self.is_can_weapon_point_attach = false
	self.obj_scale = nil
	self.mount_loaded_and_up_call_back = nil
	self.is_rolemodel = false
	self.is_force_max_lod = false
	self.is_need_layer_mask_reflection = false
	self.wait_part_loaded_status_list = {}
	self:ResetWaitSyncAnimPartCache()
end

function DrawObj:GetIsRoleModel()
	return self.is_rolemodel
end

function DrawObj:GetObjVisible()
	return self.is_visible
end

function DrawObj:IsDeleted()
	return self.root == nil
end

function DrawObj:GetRoot()
	return self.root
end

function DrawObj:SetIsUseObjPool(is_use_objpool)
	self.is_use_objpool = is_use_objpool
end

function DrawObj:SetIsEnterObjPool(is_enter_objpool)
	self.is_enter_objpool = is_enter_objpool
end

function DrawObj:SetRootActive(bool)
	if self.root then
		self.root.gameObject:SetActive(bool)
	end
end

function DrawObj:SetIsInQueueLoad(is_in_queue_load)
	self.is_in_queue_load = is_in_queue_load
end

function DrawObj:SetMaterialQuality(material_quality)
	self.material_quality = material_quality
	for k,v in pairs(self.part_list) do
		v:SetMaterialQuality(self:GetPartMaterialQuality(k))
	end
end

function DrawObj:SetPartMaterialQualityOffset(part, material_quality_offset)
	if nil == self.part_material_quality_offset_list then
		self.part_material_quality_offset_list = {}
	end

	self.part_material_quality_offset_list[part] = material_quality_offset
	local part_obj = self.part_list[part]
	if part_obj then
		part_obj:SetMaterialQuality(self:GetPartMaterialQuality(part))
	end
end

function DrawObj:SetIsHdTexture(is_hd_texture)
	self.is_hd_texture = is_hd_texture
	for k,v in pairs(self.part_list) do
		v:SetIsHdTexture(self.is_hd_texture)
	end
end

function DrawObj:SetIsLerpProbe(is_lerp, is_force)
	self.is_lerp_probe = is_lerp
	for k,v in pairs(self.part_list) do
		v:SetIsLerpProbe(self.is_lerp_probe, is_force)
	end
end

-- 设置颜色
function DrawObj:SetIsMultiColor(multi_color)
	self.multi_color = multi_color
	for _, part in pairs(SceneObjPart) do
		if self.part_list[part] then
			self.part_list[part]:SetIsMultiColor(multi_color)
		end
	end
end

-- 设置颜色
function DrawObj:SetIsGray(is_gray)
	self.is_gray = is_gray
	for _, part in pairs(SceneObjPart) do
		if self.part_list[part] then
			self.part_list[part]:SetIsGray(is_gray)
		end
	end
end

function DrawObj:SetIsForceMaxLod(is_max)
	self.is_force_max_lod = is_max
	for _, part in pairs(SceneObjPart) do
		if self.part_list[part] then
			self.part_list[part]:SetForceLodMaxLevel(is_max)
		end
	end
end

function DrawObj:SetIsDisableAllAttachEffects(is_disable_effect)
	self.is_disable_effect = is_disable_effect
	for k, v in pairs(self.part_list) do
		v:SetIsDisableAllAttachEffects(self:GetPartIsDisableEffect(k))
	end
end

function DrawObj:SetQualityControlOverrideLevel(quality_override_level)
	self.quality_override_level = quality_override_level
	for k, v in pairs(self.part_list) do
		v:SetQualityControlOverrideLevel(self.quality_override_level)
	end
end

function DrawObj:SetPartIsDisableAttachEffect(part, is_disable_effect)
	if nil == self.disable_effect_part_list then
		self.disable_effect_part_list = {}
	end

	self.disable_effect_part_list[part] = is_disable_effect
	local part_obj = self.part_list[part]
	if part_obj then
		part_obj:SetIsDisableAllAttachEffects(self:GetPartIsDisableEffect(part))
	end
end

function DrawObj:GetPartIsDisableEffect(part)
	return self.is_disable_effect or
		(nil ~= self.disable_effect_part_list and self.disable_effect_part_list[part]) or false
end

function DrawObj:GetPartMaterialQuality(part)
	-- ui材质球直接返回
	if self.material_quality == GameEnum.MATERIAL_QUALITY_UI then
		return self.material_quality
	else
		return GameEnum.MATERIAL_QUALITY_PBR
	end
end

function DrawObj:_CheckMoveObj()
	if self.root == nil or IsNil(self.root.move_obj) then
		return nil
	end
	return self.root.move_obj
end

function DrawObj:SetName(name)
	self.root.gameObject.name = name
end

function DrawObj:GetName()
	return self.root.gameObject.name
end

function DrawObj:SetSceneObj(scene_obj)
	self.scene_obj = scene_obj
end

function DrawObj:GetSceneObj()
	return self.scene_obj
end

function DrawObj:SetOffset(offset)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj:SetOffset(offset)
end

function DrawObj:GetOffsetY()
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	return move_obj:GetOffsetY()
end

function DrawObj:GetOffset()
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	return move_obj:GetOffset()
end

local infValue = 1/0
function DrawObj:SetPosition(x, y, z)
	z = z or 0
	if x ~= x or y ~= y or x == infValue or y == infValue then
		print_error("Try set a NaN or Inf Pos!!!!", x, y, debug.traceback())
		return
	end

	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	-- 重置坐标时，开启一下轻功，避免人物出现在建筑物底下
	if self.parent_obj.IsRole and self.parent_obj:IsRole() then
		local logic_pos_x, logic_pos_y = self.parent_obj:GetLogicPos()
		if AStarFindWay:IsHighArea(logic_pos_x, logic_pos_y) then
			local old_state = move_obj.enableQingGong
			self:QingGongEnable(true)
			move_obj:SetPosition(x, z, y)
			Transform.GetPosition(self.root_transform, self.curPos)
			self:QingGongEnable(old_state)
			return
		end
	end

	move_obj:SetPosition(x, z, y)
	Transform.GetPosition(self.root_transform, self.curPos)
end

function DrawObj:SetPosition2(x, y, z)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj:SetPosition(x, z, y)
end

function DrawObj:SetScale(x_scale, y_scale, z_scale)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	if self.obj_scale == nil then
		local scale = move_obj.transform.localScale
		self.obj_scale = {x = scale.x, y = scale.y, z = scale.z}
	end

	Transform.SetLocalScaleXYZ(move_obj.transform, x_scale, y_scale, z_scale)
end

function DrawObj:TryResetScale()
	local move_obj = self:_CheckMoveObj()
	if self.obj_scale ~= nil and move_obj then
		Transform.SetLocalScaleXYZ(move_obj.transform, self.obj_scale.x, self.obj_scale.y, self.obj_scale.z)
	end

	self.obj_scale = nil
end

function DrawObj:Rotate(x_angle, y_angle, z_angle)
	self.root_transform:Rotate(x_angle, y_angle, z_angle)
end

function DrawObj:GetRotation()
	return self.root_transform.rotation
end

function DrawObj:SetRotation(rotation)
	self.root_transform.rotation = rotation
end

function DrawObj:GetRootPosition()
	return self:GetPosition()
end

function DrawObj:GetPosition()
	if self.root_transform.hasChanged then
		self.root_transform.hasChanged = false
		Transform.GetPosition(self.root_transform, self.curPos)
	end
	return self.curPos
end

function DrawObj:SetDirectionByXY(x, y, speed)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	speed = speed or COMMON_CONSTS.OBJ_ROTOATE_SPEED
	move_obj:RotateTo(x, 0, y, speed)
end

function DrawObj:MoveTo(x, y, speed, h)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj:MoveTo(x, h or 0, y, speed)
end

function DrawObj:SetMoveCallback(callback)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	-- if nil ~= callback then
		move_obj:SetMoveCallback(callback)
	-- end
end

function DrawObj:SetRotatingCallback(callback)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	-- if nil ~= callback then
		move_obj:SetRotateCallback(callback)
	-- end
end

function DrawObj:StopMove()
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj:StopMove()
end

function DrawObj:SetVisible(visible)
	self.is_visible = visible

	for k, part in pairs(SceneObjPart) do
		if self.part_list[part] then
			self.part_list[part]:SetVisible(self:GetPartVisible(part))
		end
	end
end

function DrawObj:GetPart(part)
	if self.part_list[part] == nil then
		self.part_list[part] = self:_CreatePart(part)
	end

	return self.part_list[part]
end

function DrawObj:TryGetPart(part)
	return self.part_list[part]
end

function DrawObj:ChangeModel(part, bundle, name, callback, draw_model_type, extra_model_data, load_skin_callback)
	if (not IsNil(self.actor_animator_syncter)) and part == SceneObjPart.Main then
		self.actor_animator_syncter:ClearSyncTargetAnimatorData()
		self.actor_animator_syncter.enabled = false
	end

	local part_obj = self:GetPart(part)
	part_obj.load_priority = self.load_priority

	if part == SceneObjPart.Main and self.parent_obj and self.parent_obj.GetIsNeedPartAnimSync then
		self:SetIsNeedSyncAnim(self.parent_obj:GetIsNeedPartAnimSync())
	end

	self:SetLoadedEntiretyStatus(part, false, bundle, name, extra_model_data)
	self:ChangeWaitSyncAnimPartCache(part, SENCE_OBJ_LOADED_STATUS.WAIT, name)

	local new_callback = callback
	if part == SceneObjPart.Main then
		new_callback = function()
			local obj = self:GetPart(SceneObjPart.Main):GetObj()
			if nil ~= obj and not IsNil(obj.gameObject) then
				self.actor_animator_syncter = obj.gameObject:GetComponent(TypeOfActorAnimatorSyncter)

				if not IsNil(self.actor_animator_syncter) then
					self.actor_animator_syncter.enabled = true
				end
			end

			if callback then
				callback()
			end
		end
	end

	part_obj:ChangeModel(bundle, name, new_callback, draw_model_type, extra_model_data, load_skin_callback)
end

function DrawObj:RemoveModel(part, real_remove)
	self:SetLoadedEntiretyStatus(part, nil)
	self:SetWaitSyncAnimPartIsRemove(part, real_remove)
	local part_obj = self.part_list[part]
	if part_obj then
		part_obj:RemoveModel()
	end
end

function DrawObj:GetAttachPoint(point)
	local part = self:GetPart(SceneObjPart.Main)
	local point_node = part:GetAttachPoint(point)
	if point_node ~= nil then
		return point_node
	else
		return self.root_transform
	end
end

function DrawObj:GetRealAttachPoint(point)
	local part = self:GetPart(SceneObjPart.Main)
	local point_node = part:GetAttachPoint(point)
	return point_node
end

function DrawObj:GetTransfrom()
	return self.root_transform
end

function DrawObj:GetObjType()
	return self.obj_type
end

function DrawObj:SetObjType(obj_type)
	self.obj_type = obj_type
end

function DrawObj:SetLoadComplete(complete)
	self.load_complete = complete
end

function DrawObj:DoLoadComplete(part, obj, part_obj, bundle_name, asset_name)
	if self.load_complete then
		self.load_complete(part, obj, part_obj, bundle_name, asset_name)
	end

	self:SetLoadedEntiretyStatus(part, true)
	self:CheckEntiretyAction(part)
	if self.wait_sync_anim_part_cache and self.wait_sync_anim_part_cache[part] ~= nil then
        self:SetWaitSyncAnimPartIsLoaded(part)
    end
end

function DrawObj:SetRemoveCallback(callback)
	self.remove_callback = callback
end

function DrawObj:MainPartCompleteCallback(obj, part, part_obj, bundle_name, asset_name)
	local attachment = obj.actor_attachment
	local attach_skin = obj.attach_skin

	if attachment == nil then
		self:DoLoadComplete(part, obj, part_obj, bundle_name, asset_name)
		return
	end

	local do_attach_point = PartAttachPoint
	local attach, point = nil, nil
	GlobalTimerQuest:CancelQuest(self.delay_set_attached)

	-- local func = function()
		for k, v in pairs(do_attach_point) do
			local attach_skin_obj = self:_TryGetPartAttachSkinObj(k)
			-- local active_part = self.part_list[k]

			if nil ~= attach_skin_obj and nil ~= attach_skin then
				attach_skin_obj.gameObject:SetActive(true)
				attach_skin:AttachMesh(attach_skin_obj.gameObject)
			else
				attach = self:_TryGetPartAttachObj(k)
				if attach ~= nil then
					point = attachment:GetAttachPoint(v)
					if nil ~= point and not IsNil(point.gameObject) then
						attach:SetAttached(point)
						attach:SetTransform(attachment.Prof)
					end

					attach.gameObject:SetActive(true)
				end

				-- if active_part then
				-- 	active_part:CheckAniParam()
				-- end
			end

			-- if active_part ~= nil then
			-- 	active_part:CheckAniPlay()
			-- end
		end

		self.can_get_main_point = true
	-- end

	-- if self.is_rolemodel then
		-- func()
	-- else
	-- 	self.delay_set_attached = GlobalTimerQuest:AddDelayTimer(function ()
	-- 		func()
	-- 	end, 0.05)
	-- end

	local wing = self.part_list[SceneObjPart.Wing]
	if nil ~= wing then
		local attachment = self:_TryGetPartAttachment(SceneObjPart.Main)
		if attachment then
			local object = wing.obj
			if object and not IsNil(object.gameObject) then
				object:SetActive(true)
		        local point = attachment:GetAttachPoint(AttachPoint.Wing)
		        if object.attach_obj ~= nil and point then
		        	object.attach_obj:SetAttached(point)
		        	local attach = self:_TryGetPartAttachObj(SceneObjPart.Wing)
		        	if attach then
		        		attach:SetTransform(attachment.Prof)
		        	end
		    	end
		    end
   		end

   		self:UpdateFabaoPosition()
	end

	local baoju_obj = self:_TryGetPartAttachObj(SceneObjPart.BaoJu)
	if baoju_obj ~= nil then
		baoju_obj.gameObject:SetActive(true)
		point = attachment:GetAttachPoint(AttachPoint.FaBao)
		if nil ~= point and not IsNil(point.gameObject) then
			baoju_obj:SetAttached(point)
			baoju_obj:SetTransform(attachment.Prof)
		end
	end

	local mantle_obj = self:_TryGetPartAttachObj(SceneObjPart.Mantle)
	if mantle_obj ~= nil then
			mantle_obj.gameObject:SetActive(true)
		point = attachment:GetAttachPoint(AttachPoint.Wing)
		if nil ~= point and not IsNil(point.gameObject) then
			mantle_obj:SetAttached(point)
			mantle_obj:SetTransform(attachment.Prof)
		end
	end

	--[[
	local fight_mount_obj = self:_TryGetPartObj(SceneObjPart.FightMount)
	if fight_mount_obj ~= nil then
		fight_mount_obj.gameObject:SetActive(true)
		attachment:AddMount(fight_mount_obj.gameObject)
		if fight_mount_obj.attach_obj ~= nil then
			fight_mount_obj.attach_obj:SetTransform(attachment.Prof)
		end

		local fight_mount_part = self:GetPart(SceneObjPart.FightMount)
		if fight_mount_part ~= nil then
			fight_mount_part:CheckAniPlay()
		end

		self:UpdateFabaoPosition()
	end
	]]

	local fazhen_obj = self:_TryGetPartObj(SceneObjPart.FaZhen)
	if fazhen_obj ~= nil then
		fazhen_obj.gameObject:SetActive(true)
		point = attachment:GetAttachPoint(AttachPoint.Root)
		if nil ~= point and not IsNil(point.gameObject) and fazhen_obj.attach_obj ~= nil then
			fazhen_obj.attach_obj:SetAttached(point)
			fazhen_obj.attach_obj:SetTransform(0)
		end

		self:ChangeFaZhenAttachObjectAttached()
	end


	local god_or_demon_halo_obj = self:_TryGetPartObj(SceneObjPart.GodOrDemonHalo)
	if god_or_demon_halo_obj ~= nil then
		god_or_demon_halo_obj.gameObject:SetActive(true)
		point = attachment:GetAttachPoint(AttachPoint.HurtRoot)
		if nil ~= point and not IsNil(point.gameObject) and god_or_demon_halo_obj.attach_obj ~= nil then
			god_or_demon_halo_obj.attach_obj:SetAttached(point)
			god_or_demon_halo_obj.attach_obj:SetTransform(attachment.Prof)
		end
	end

	self:DoLoadComplete(part, obj, part_obj, bundle_name, asset_name)

	self.actor_animator_syncter = obj:GetComponent(TypeOfActorAnimatorSyncter)
	if not IsNil(self.actor_animator_syncter) then
		self.actor_animator_syncter.enabled = true
	end
	self:CheckActorAnimatorSyncter(SceneObjPart.Main)

	self:ChangeAttachmentByAddMount()
end

function DrawObj:MainPartRemoveCallback(obj)
	self.can_get_main_point = false

	local attachment = self:_TryGetPartAttachment(SceneObjPart.Main)
	if attachment ~= nil then
		attachment:RemoveMount()
	end

	-- 移除动作同步
	self:RemoveActorAnimatorSyncter(SceneObjPart.Main)

	local weapon = self.part_list[SceneObjPart.Weapon]
	if nil ~= weapon then
		if self.weapon_effect then
			ResMgr:Destroy(self.weapon_effect)
			self.weapon_effect = nil
		end
		weapon:Reset()
	end

	local wing = self.part_list[SceneObjPart.Wing]
	if nil ~= wing then
		wing:Reset()
	end

	local fazhen = self.part_list[SceneObjPart.FaZhen]
	if nil ~= fazhen then
		fazhen:Reset()
	end

	local mantle = self.part_list[SceneObjPart.Mantle]
	if nil ~= mantle then
		mantle:Reset()
	end

	local halo = self.part_list[SceneObjPart.Halo]
	if nil ~= halo then
		halo:Reset()
	end

	local beauty = self.part_list[SceneObjPart.HoldBeauty]
	if nil ~= beauty then
		beauty:Reset()
	end

	local waist = self.part_list[SceneObjPart.Waist]
	if nil ~= waist then
		waist:Reset()
	end

	local mask = self.part_list[SceneObjPart.Mask]
	if nil ~= mask then
		mask:Reset()
	end

	local tail = self.part_list[SceneObjPart.Tail]
	if nil ~= tail then
		tail:Reset()
	end

	local shouhuan = self.part_list[SceneObjPart.ShouHuan]
	if nil ~= shouhuan then
		shouhuan:Reset()
	end

	local jianzhen = self.part_list[SceneObjPart.Jianling]
	if nil ~= jianzhen then
		jianzhen:Reset()
	end

	local skill_halo = self.part_list[SceneObjPart.SkillHalo]
	if nil ~= skill_halo then
		skill_halo:Reset()
	end
	
	local god_or_demon_halo_obj = self.part_list[SceneObjPart.GodOrDemonHalo]
	if god_or_demon_halo_obj ~= nil then
		god_or_demon_halo_obj:Reset()
	end

	local gundam_l_arm = self.part_list[SceneObjPart.GundamLArm]
	if nil ~= gundam_l_arm then
		gundam_l_arm:Reset()
	end

	local gundam_r_arm = self.part_list[SceneObjPart.GundamRArm]
	if nil ~= gundam_r_arm then
		gundam_r_arm:Reset()
	end

	local gundam_l_leg = self.part_list[SceneObjPart.GundamLLeg]
	if nil ~= gundam_l_leg then
		gundam_l_leg:Reset()
	end

	local gundam_r_leg = self.part_list[SceneObjPart.GundamRLeg]
	if nil ~= gundam_r_leg then
		gundam_r_leg:Reset()
	end

	local gundam_l_wing = self.part_list[SceneObjPart.GundamLWing]
	if nil ~= gundam_l_wing then
		gundam_l_wing:Reset()
	end

	local gundam_r_wing = self.part_list[SceneObjPart.GundamRWing]
	if nil ~= gundam_r_wing then
		gundam_r_wing:Reset()
	end

	if self.remove_callback ~= nil then
		self.remove_callback(SceneObjPart.Main, obj)
	end
end

function DrawObj:AttachPartCompleteCallback(obj, part, part_obj, bundle_name, asset_name)
	local attachment = self:_TryGetPartAttachment(SceneObjPart.Main)
	local attach_skin = self:_TryGetPartAttachSkin(SceneObjPart.Main)

	if nil ~= attach_skin and nil ~= obj.attach_skin_obj then
		obj.gameObject:SetActive(true)
		attach_skin:AttachMesh(obj.gameObject)
	elseif nil ~= attachment and self.can_get_main_point
		and (part ~= SceneObjPart.Weapon or (part == SceneObjPart.Weapon and self.is_can_weapon_point_attach)) then
		obj.gameObject:SetActive(true)
		local point = attachment:GetAttachPoint(PartAttachPoint[part])
		if not IsNil(point) and obj.attach_obj then
			obj.attach_obj:SetAttached(point)
			obj.attach_obj:SetTransform(attachment.Prof)
		end
	elseif part == SceneObjPart.Weapon and not self.is_can_weapon_point_attach then
		obj.gameObject:SetActive(true)
	else
		obj.gameObject:SetActive(false)
	end

	-- 动作同步
	self:CheckActorAnimatorSyncter(part)
	self:DoLoadComplete(part, obj, part_obj, bundle_name, asset_name)
end

function DrawObj:AttachPartRemoveCallback(obj, part, part_obj)
	self:RemoveActorAnimatorSyncter(part)

	if self.remove_callback ~= nil then
		self.remove_callback(part, obj)
	end
end

function DrawObj:WeaponPartCompleteCallback(obj, part, part_obj, bundle_name, asset_name)
	obj.gameObject:SetActive(true)
	-- 动作同步
	self:CheckActorAnimatorSyncter(part)
	self:DoLoadComplete(part, obj, part_obj, bundle_name, asset_name)
	if self:GetIsRoleModel() then
		self:ChangeAttachmentByAddMount()
	end

end

function DrawObj:WeaponPartRemoveCallback(obj, part, part_obj)
	local attachment = self:_TryGetPartAttachment(SceneObjPart.Weapon)
	if attachment ~= nil then
		attachment:RemoveMount()
	end

	self:RemoveActorAnimatorSyncter(part)

	if self.remove_callback ~= nil then
		self.remove_callback(part, obj)
	end
end

function DrawObj:GundamPartCompleteCallback(obj, part, part_obj, bundle_name, asset_name)
	obj.gameObject:SetActive(true)
	self:DoLoadComplete(part, obj, part_obj, bundle_name, asset_name)
end

function DrawObj:GundamPartRemoveCallback(obj, part, part_obj)
	self:RemoveActorAnimatorSyncter(part)

	if self.remove_callback ~= nil then
		self.remove_callback(part, obj)
	end
end

function DrawObj:WingPartCompleteCallback(obj, part, part_obj, bundle_name, asset_name)
	local attachment = self:_TryGetPartAttachment(SceneObjPart.Main)
	if attachment ~= nil then
		obj.gameObject:SetActive(true)
		local point = attachment:GetAttachPoint(AttachPoint.Wing)
		if not IsNil(point) and obj.attach_obj then
			obj.attach_obj:SetAttached(point)
			obj.attach_obj:SetTransform(attachment.Prof)
		end
	else
		obj.gameObject:SetActive(false)
	end

	self:DoLoadComplete(part, obj, part_obj, bundle_name, asset_name)
	self:UpdateFabaoPosition()
end

function DrawObj:WingPartRemoveCallback(obj, part, part_obj)
	self:RemoveActorAnimatorSyncter(part)

	if self.remove_callback ~= nil then
		self.remove_callback(part, obj)
	end

	self:UpdateFabaoPosition(false)
end

function DrawObj:MantlePartCompleteCallback(obj, part, part_obj, bundle_name, asset_name)
	local attachment = self:_TryGetPartAttachment(SceneObjPart.Main)
	if attachment ~= nil then
		local point = attachment:GetAttachPoint(AttachPoint.Wing)
		if not IsNil(point) then
			obj.attach_obj:SetAttached(point)
			obj.attach_obj:SetTransform(attachment.Prof)
		end
	end

	self:DoLoadComplete(part, obj, part_obj, bundle_name, asset_name)
end

function DrawObj:MantlePartRemoveCallback(obj, part, part_obj)
	if self.remove_callback ~= nil then
		self.remove_callback(part, obj)
	end
end

function DrawObj:MountPartCompleteCallback(obj, part, part_obj, bundle_name, asset_name)
	obj.gameObject:SetActive(true)
	self:DoLoadComplete(part, obj, part_obj, bundle_name, asset_name)
	self:ChangeAttachmentByAddMount()
end

function DrawObj:MountPartRemoveCallback(obj, part, part_obj)
	local attachment = self:_TryGetPartAttachment(SceneObjPart.Main)
	if attachment ~= nil then
		attachment:RemoveMount()
		self:UpdateFabaoPosition(false)
		self:ChangeFaZhenAttachObjectAttached(true)
	end

	if self.remove_callback ~= nil then
		self.remove_callback(part, obj)
	end
end

function DrawObj:HaloPartCompleteCallback(obj, part, part_obj, bundle_name, asset_name)
	local attachment = self:_TryGetPartAttachment(SceneObjPart.Main)
	if attachment ~= nil then
		obj.gameObject:SetActive(true)
		local point = attachment:GetAttachPoint(AttachPoint.Hurt)
		if not IsNil(point) and obj.attach_obj then
			obj.attach_obj:SetAttached(point)
			obj.attach_obj:SetTransform(attachment.Prof)
		end
	else
		obj.gameObject:SetActive(false)
	end

	self:DoLoadComplete(part, obj, part_obj, bundle_name, asset_name)
end

function DrawObj:HaloPartRemoveCallback(obj, part, part_obj)
	if self.remove_callback ~= nil then
		self.remove_callback(part, obj)
	end
end

function DrawObj:BaoJuPartCompleteCallback(obj, part, part_obj, bundle_name, asset_name)
	self:UpdateFabaoPosition()
	local attachment = self:_TryGetPartAttachment(SceneObjPart.Main)
	if attachment ~= nil then
		obj.gameObject:SetActive(true)
		local point = attachment:GetAttachPoint(AttachPoint.FaBao)
		if not IsNil(point) then
			obj.attach_obj:SetAttached(point)
			obj.attach_obj:SetTransform(attachment.Prof)
		end
	else
		obj.gameObject:SetActive(false)
	end

	self:DoLoadComplete(part, obj, part_obj, bundle_name, asset_name)
	part_obj:SetInteger("status", ActionStatus.Run)
end

function DrawObj:BaoJuPartRemoveCallback(obj, part, part_obj)
	-- 移除的时候恢复动画场景标志

	if self.remove_callback ~= nil then
		self.remove_callback(part, obj)
	end
end

function DrawObj:ParticlePartCompleteCallback(obj, part, part_obj, bundle_name, asset_name)
	local attachment = self:_TryGetPartAttachment(SceneObjPart.Main)
	if attachment ~= nil then
		obj.gameObject:SetActive(true)
		local point = attachment:GetAttachPoint(AttachPoint.Hurt)
		if not IsNil(point) and obj.attach_obj then
			obj.attach_obj:SetAttached(point)
			obj.attach_obj:SetTransform(attachment.Prof)
		end
	else
		obj.gameObject:SetActive(false)
	end

	self:DoLoadComplete(part, obj, part_obj, bundle_name, asset_name)
end

function DrawObj:ParticlePartRemoveCallback(obj, part, part_obj)
	if self.remove_callback ~= nil then
		self.remove_callback(part, obj)
	end
end

function DrawObj:FaZhenPartCompleteCallback(obj, part, part_obj, bundle_name, asset_name)
	local attachment = self:_TryGetPartAttachment(SceneObjPart.Main)
	if attachment ~= nil then
		obj.gameObject:SetActive(true)
		-- local point = attachment:GetAttachPoint(AttachPoint.HurtRoot)
		-- if not IsNil(point) and obj.attach_obj then
		-- 	obj.attach_obj:SetAttached(point)
		-- end

		self:ChangeFaZhenAttachObjectAttached()
		if obj.attach_obj then
			obj.attach_obj:SetTransform(0)
		end
	else
		obj.gameObject:SetActive(false)
	end

	self:DoLoadComplete(part, obj, part_obj, bundle_name, asset_name)
end

function DrawObj:FaZhenPartRemoveCallback(obj, part, part_obj)
	if self.remove_callback ~= nil then
		self.remove_callback(part, obj)
	end
end

function DrawObj:HoldBeautyPartCompleteCallback(obj, part, part_obj, bundle_name, asset_name)
	local attachment = self:_TryGetPartAttachment(SceneObjPart.Main)
	if attachment ~= nil then
		obj.gameObject:SetActive(true)
		local point = attachment:GetAttachPoint(AttachPoint.Hurt)
		if not IsNil(point) and obj.attach_obj then
			obj.attach_obj:SetAttached(point)
			obj.attach_obj:SetTransform(attachment.Prof)
		end
	else
		obj.gameObject:SetActive(false)
	end

	self:DoLoadComplete(part, obj, part_obj, bundle_name, asset_name)
end

function DrawObj:HoldBeautyPartRemoveCallback(obj, part, part_obj)
	if self.remove_callback ~= nil then
		self.remove_callback(part, obj)
	end
end

function DrawObj:_CreatePart(part)
	local part_obj = DrawPart.Pop()
	part_obj:SetParent(self.root)
	part_obj:SetIsUseObjPool(self.is_use_objpool)
	part_obj:SetIsInQueueLoad(self.is_in_queue_load)
	part_obj:SetIsHdTexture(self.is_hd_texture)
	part_obj:SetMaterialQuality(self:GetPartMaterialQuality(part))
	part_obj:SetIsLerpProbe(self.is_lerp_probe)
	part_obj:SetIsMultiColor(self.multi_color)
	part_obj:SetIsGray(self.is_gray)
	part_obj:SetIsDisableAllAttachEffects(self:GetPartIsDisableEffect(part))
	part_obj:SetQualityControlOverrideLevel(self.quality_override_level)

	part_obj:SetVisible(self:GetPartVisible(part))
	part_obj:SetNeedDetailLevel(self:GetPartNeedDetailLevel(part))
	part_obj:SetCurDetailLevel(self.cur_detail_level)
	part_obj:SetForceLodMaxLevel(self.is_force_max_lod)
	part_obj:SetDrawObj(self)
	part_obj:SetChangeWaitSyncAnimPartCacheCallBack(BindTool.Bind(self.ChangeWaitSyncAnimPartCache, self))
	part_obj:SetLoadedEntiretyCallBack(BindTool.Bind(self.SetLoadedEntiretyStatus, self))

	-- 水面反射
	if self.is_need_layer_mask_reflection and WaterReflectionPartList[part] then
		part_obj:SetLayerMaskReflectionSwitch(true)
	end

	local is_main_role = self.parent_obj.IsMainRole and self.parent_obj:IsMainRole() or false
	if part == SceneObjPart.Main then
		part_obj:SetMainRole(is_main_role)
		part_obj:SetLoadComplete(DrawObj.MainPartCompleteCallback, part)
		part_obj:SetRemoveCallback(DrawObj.MainPartRemoveCallback)

	elseif part == SceneObjPart.Weapon then
		part_obj:SetMainRole(is_main_role)
		part_obj:SetLoadComplete(DrawObj.WeaponPartCompleteCallback, part)
		part_obj:SetRemoveCallback(DrawObj.WeaponPartRemoveCallback)

	elseif PartAttachPoint[part] ~= nil then
		part_obj:SetMainRole(is_main_role)
		part_obj:SetLoadComplete(DrawObj.AttachPartCompleteCallback, part)
		part_obj:SetRemoveCallback(DrawObj.AttachPartRemoveCallback)
		
	elseif part == SceneObjPart.Wing then
		part_obj:SetMainRole(is_main_role)
		part_obj:SetLoadComplete(DrawObj.WingPartCompleteCallback, part)
		part_obj:SetRemoveCallback(DrawObj.WingPartRemoveCallback)
	elseif part == SceneObjPart.Mantle then
		part_obj:SetMainRole(is_main_role)
		part_obj:SetLoadComplete(DrawObj.MantlePartCompleteCallback, part)
		part_obj:SetRemoveCallback(DrawObj.MantlePartRemoveCallback)
	elseif part == SceneObjPart.Mount or part == SceneObjPart.FightMount then
		part_obj:SetMainRole(is_main_role)
		part_obj:SetIsCastShadow(is_main_role)
		part_obj:SetLoadComplete(DrawObj.MountPartCompleteCallback, part)
		part_obj:SetRemoveCallback(DrawObj.MountPartRemoveCallback)
	-- elseif part == SceneObjPart.Halo then
	-- 	part_obj:SetLoadComplete(DrawObj.HaloPartCompleteCallback, part)
	-- 	part_obj:SetRemoveCallback(DrawObj.HaloPartRemoveCallback)
	elseif part == SceneObjPart.BaoJu then
		part_obj:SetMainRole(is_main_role)
		part_obj:SetLoadComplete(DrawObj.BaoJuPartCompleteCallback, part)
		part_obj:SetRemoveCallback(DrawObj.BaoJuPartRemoveCallback)
	-- elseif part == SceneObjPart.Particle then
	-- 	part_obj:SetLoadComplete(DrawObj.ParticlePartCompleteCallback, part)
	-- 	part_obj:SetRemoveCallback(DrawObj.ParticlePartRemoveCallback)
	elseif part == SceneObjPart.FaZhen then
		part_obj:SetLoadComplete(DrawObj.FaZhenPartCompleteCallback, part)
		part_obj:SetRemoveCallback(DrawObj.FaZhenPartRemoveCallback)
	elseif part == SceneObjPart.HoldBeauty then
		part_obj:SetLoadComplete(DrawObj.HoldBeautyPartCompleteCallback, part)
		part_obj:SetRemoveCallback(DrawObj.HoldBeautyPartRemoveCallback)
	elseif DrawObj.IsGundamPart(part) then
		part_obj:SetMainRole(is_main_role)
		part_obj:SetLoadComplete(DrawObj.GundamPartCompleteCallback, part)
		part_obj:SetRemoveCallback(DrawObj.GundamPartRemoveCallback)
	else
		-- print_error("_CreatePart failed: ", part)
	end

	return part_obj
end

function DrawObj.IsGundamPart(part)
	return part >= SceneObjPart.GundamLArm and part <= SceneObjPart.GundamRWing
end

function DrawObj:UpdateFabaoPosition(flag)
	-- 现跟随法宝挂点
	if true then return end
	
	local baoju_part = self:GetPart(SceneObjPart.BaoJu)
	local t = {x = -1.15, y = 3, z = 0}
	if baoju_part and baoju_part:GetObj() then
		if nil == flag then
			local mount_obj
			local part_type = SceneObjPart.Mount
			mount_obj = self:_TryGetPartObj(part_type)
			--[[
			if not mount_obj then
				part_type = SceneObjPart.FightMount
				mount_obj = self:_TryGetPartObj(part_type)
			end
			]]

			flag = nil ~= mount_obj
			if flag then
				local part = self:GetPart(part_type)
				local p = part:GetAdjustPoint()
				if p ~= nil then
					t.x = p.x
					t.y = p.y
					t.z = p.z
				end
			end
		end

		local is_wing_fly = (self.parent_obj.IsRole and self.parent_obj:IsRole())
							and self.parent_obj:IsWingFly()
							or false
		local obj = baoju_part:GetObj()
		local transform = obj.transform
		if not IsNil(transform) then
			if flag or is_wing_fly then
				Transform.SetLocalPositionXYZ(transform, t.x, t.y, t.z)
			else
				Transform.SetLocalPositionXYZ(transform, -0.67, 1.25, 0.3)
			end

			-- Transform.SetLocalScaleXYZ(transform, 1, 1, 1)
		end
	end
end

-- 改变法阵的附属点
-- no_mount_flag 没坐骑标记（因为部位移除在回调之后）
function DrawObj:ChangeFaZhenAttachObjectAttached(no_mount_flag)
    local fazhen_obj = self:_TryGetPartAttachObj(SceneObjPart.FaZhen)
    if fazhen_obj == nil then
        return
    end

    local point
	local mount_obj
	local part_type = SceneObjPart.Mount
	mount_obj = self:_TryGetPartObj(part_type)
	--[[
	if not mount_obj then
		part_type = SceneObjPart.FightMount
		mount_obj = self:_TryGetPartObj(part_type)
	end
	]]

    if (not no_mount_flag and mount_obj) then
        local mount_part = self:GetPart(part_type)
        point = mount_part and mount_part:GetMountFaZhenPoint()
    else
        local attachment = self:_TryGetPartAttachment(SceneObjPart.Main)
        if attachment then
            point = attachment:GetAttachPoint(AttachPoint.Root)
        end
    end

    if point then
        fazhen_obj:SetAttached(point)
    end
end




function DrawObj:_TryGetPartObj(part)
	local part_obj = self.part_list[part]
	if part_obj == nil then
		return nil
	end

	local obj = part_obj:GetObj()
	if obj == nil or IsNil(obj.gameObject) then
		return nil
	end

	return obj
end

function DrawObj:_TryGetPartAttachObj(part)
	local part_obj = self.part_list[part]
	if part_obj == nil then
		return nil
	end

	local obj = part_obj:GetObj()
	if obj == nil or IsNil(obj.gameObject) then
		return nil
	end

	return obj.attach_obj
end

function DrawObj:_TryGetPartAttachment(part)
	local part_obj = self.part_list[part]
	if part_obj == nil then
		return nil
	end

	if not part_obj:GetIsLoaded() then
		return nil
	end

	local obj = part_obj:GetObj()
	if obj == nil or IsNil(obj.gameObject) then
		return nil
	end

	return obj.actor_attachment
end

function DrawObj:_TryGetPartAttachSkinObj(part)
	local part_obj = self.part_list[part]
	if part_obj == nil then
		return nil
	end

	local obj = part_obj:GetObj()
	if obj == nil or IsNil(obj.gameObject) then
		return nil
	end

	return obj.attach_skin_obj
end

function DrawObj:_TryGetPartAttachSkin(part)
	local part_obj = self.part_list[part]
	if part_obj == nil then
		return nil
	end

	local obj = part_obj:GetObj()
	if obj == nil or IsNil(obj.gameObject) then
		return nil
	end

	return obj.attach_skin
end

function DrawObj:PlayDead(dietype, callback, time,obj_id)
	time = time or 2.0
	local main_part = self:GetPart(SceneObjPart.Main)
	local main_obj = main_part:GetObj()
	if main_obj == nil then
		if callback then
			callback()
		end
		return
	end

	local tween = main_obj.transform:DOLocalMoveY(-1.0, 1.0)
	tween:SetEase(DG.Tweening.Ease.Linear)
	tween:OnComplete(callback)
end

function DrawObj:AddOcclusion()
	for k, part in pairs(SceneObjPart) do
		if self.part_list[part] then
			self.part_list[part]:AddOcclusion()
		end
	end
end

function DrawObj:RemoveOcclusion()
	for k, part in pairs(SceneObjPart) do
		if self.part_list[part] then
			self.part_list[part]:RemoveOcclusion()
		end
	end
end

function DrawObj:GetLookAtPoint(x, y, z, immediate)
	x = x or 0
	y = y or 0
	z = z or 0

	if nil == self.look_at_point then
		self.look_at_point = GameObject.New("CamerafocalPoint")
		self.look_at_point.transform:SetParent(self.root_transform)
		self.look_at_point.transform.localEulerAngles = Vector3(0, 0, 0)
		self.look_at_point.transform.localPosition = Vector3(x, y, z)
	else
		if immediate then
			self.look_at_point.transform.localPosition = Vector3(x, y, z)
		else
			local tween = self.look_at_point.transform:DOLocalMove(Vector3(x, y, z), 0.5)
			tween:SetEase(DG.Tweening.Ease.OutQuad)
		end
	end
	return self.look_at_point.transform
end

function DrawObj:GetLookAtPointTrans()
	return self.look_at_point and self.look_at_point.transform
end

function DrawObj:StopQinggong()
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj:StopQinggong()
end

function DrawObj:QingGongEnable(state)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj.enableQingGong = state
end

function DrawObj:SetIsWalkFree(is_walk_free)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj.isWalkFree = is_walk_free
end

function DrawObj:Jump(qinggongObject)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj:Jump(qinggongObject)
end

--轻功御剑
function DrawObj:Mitsurugi(qinggongObject)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj:Mitsurugi(qinggongObject)
end

function DrawObj:SetMitsurugi(hight)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj.mitsurugi_hight = hight
end

function DrawObj:SimpleJump(qinggongObject, target, autoJump)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj:SimpleJump(qinggongObject, target, autoJump or false)
end

function DrawObj:AdjustMoveMent(fx, fy)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end
	
	move_obj:AdjustMoveMent(fx, fy)
end

function DrawObj:SetStateChangeCallBack(callback)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj:SetStateChangeCallBack(callback)
end

function DrawObj:SetGravityMultiplier(multiplier)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj:SetGravityMultiplier(multiplier)
end

function DrawObj:SetJumpHorizonSpeed(speed)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj.JumpHorizonSpeed = speed or 0
end

function DrawObj:ForceLanding(speed)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj:ForceLanding()
end

function DrawObj:StopMitsurugi()
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj:StopMitsurugi()
end

function DrawObj:SetDrag(drag)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj:SetDrag(drag)
end

function DrawObj:SetQingGongTarget(target)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj:SetQingGongTarget(target)
end

function DrawObj:JumpFormAir(height, target, qinggongObject, percent)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj:JumpFormAir(height, target, qinggongObject, percent)
end

function DrawObj:GetHeight(layer, position)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	return move_obj:Height(layer, position)
end

function DrawObj:CheckBuilding(state)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj.checkBuilding = state
end

function DrawObj:ShieldPart(part, is_shield)
	if nil == self.shield_part_list then
		self.shield_part_list = {}
	end

	self.shield_part_list[part] = is_shield
	local part_obj = self.part_list[part]
	if part_obj then
		part_obj:SetVisible(self:GetPartVisible(part))
	end
end

function DrawObj:GetPartVisible(part)
	return (self.is_visible and (nil == self.shield_part_list or not self.shield_part_list[part])) or false
end

local localPosition = Vector3(0, 0, 0)
local localRotation = Quaternion.Euler(0, 0, 0)
local localScale = Vector3(1, 1, 1)
local DefaultLayer = UnityEngine.LayerMask.NameToLayer("Default")

function DrawObj.Pop()
	local drawobj = next(DrawObj.ObjList)
	if nil == drawobj then
		drawobj = U3DObject(ResMgr:CreateEmptyGameObj(nil, true))
		drawobj.gameObject:AddComponent(typeof(MoveableObject))
	else
		drawobj.transform.localPosition = localPosition
		drawobj.transform.localRotation = localRotation
		drawobj.transform.localScale = localScale
		drawobj.gameObject.layer = DefaultLayer
		drawobj.gameObject:SetActive(true)
		drawobj.transform:DOKill()

		DrawObj.ObjList[drawobj] = nil
		DrawObj.ObjCount = DrawObj.ObjCount - 1
	end
	return drawobj
end

function DrawObj.Release(drawobj)
	if DrawObj.ObjCount <= 50 and drawobj.is_enter_objpool then
		if not IsNil(drawobj.gameObject) then
			drawobj.move_obj:Reset()
			drawobj.transform:SetParent(DrawObj.v_root_transform, false)
			DrawObj.ObjList[drawobj] = drawobj
			DrawObj.ObjCount = DrawObj.ObjCount + 1
		end
	else
		ResMgr:Destroy(drawobj.gameObject)
	end
end

function DrawObj.OnGameStop()
	for k,v in pairs(DrawObj.ObjList) do
		ResMgr:Destroy(v.gameObject)
	end
	DrawObj.ObjList = {}
	DrawObj.ObjCount = 0

	if nil ~= DrawObj.v_root then
        ResMgr:Destroy(DrawObj.v_root)
        DrawObj.v_root = nil
    end
end

function DrawObj:SetPartNeedDetailLevel(part, need_level)
	if nil == self.part_need_detail_level_list then
		self.part_need_detail_level_list = {}
	end

	self.part_need_detail_level_list[part] = need_level
	local part_obj = self.part_list[part]
	if part_obj then
		part_obj:SetNeedDetailLevel(need_level)
	end
end

function DrawObj:GetPartNeedDetailLevel(part)
	return nil ~= self.part_need_detail_level_list and self.part_need_detail_level_list[part] or SceneObjDetailLevel.Low
end

function DrawObj:SetCurDetailLevel(cur_level)
	if self.cur_detail_level ~= cur_level then
		self.cur_detail_level = cur_level
		for k, v in pairs(self.part_list) do
			v:SetCurDetailLevel(cur_level)
		end

		return true
	end

	return false
end

function DrawObj:SetCheckWater(state)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj.CheckWater = state
end

function DrawObj:SetWaterHeight(height)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj.WaterHeight = height
end

function DrawObj:SetEnterWaterCallBack(callback)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj:SetEnterWaterCallBack(callback)
end

--武器挂点是否可用
function DrawObj:SetIsCanWeaponPointAttach(is_can_weapon_point_attach)
	self.is_can_weapon_point_attach = is_can_weapon_point_attach
end

function DrawObj:SetIsKeepGroundH(is_keep)
	local move_obj = self:_CheckMoveObj()
	if not move_obj then
		return
	end

	move_obj:SetIsKeepGroundH(is_keep)
end

--是否需要水面反射
function DrawObj:SetIsNeedLayerMaskReflection(bool)
	self.is_need_layer_mask_reflection = bool
end

-- 动作同步 检测 by.Ljh
function DrawObj:CheckActorAnimatorSyncter(part)
	-- print_error("------ <color=#9DF5A7FF>动作同步 check</color>---", SceneObjPartStr[part], self.root.transform.name)
	if IsNil(self.actor_animator_syncter) then
		return
	end

	if not self.wait_sync_anim_part_cache or not self.wait_sync_anim_part_cache[part] then
		return
	end

	local sync_anim_part_fun = function()
		-- local cahce_part_data = self.wait_sync_anim_part_cache[part]
		-- print_error("------ <color=#9DF5A7FF>动作同步 check xxx</color>---", SceneObjPartStr[part], cahce_part_data, self.root.transform.name)
		-- if cahce_part_data.status == SENCE_OBJ_LOADED_STATUS.LOADED then
		local a_part = self:GetPart(part)
		local part_obj = a_part:GetObj()
		if part_obj and part_obj.animator then
			-- print_error("------ <color=#9DF5A7FF>动作同步 check 333</color>---", SceneObjPartStr[part], self.root.transform.name)
			self.actor_animator_syncter:SetDrawPartAnimator(part_obj.animator)
		end
		-- end
	end

	if self.wait_sync_anim_part_cache[SceneObjPart.Main].status ~= SENCE_OBJ_LOADED_STATUS.LOADED then
		-- sync_anim_part_fun()
		return
	end

	if part == SceneObjPart.Main then
		for k,v in pairs(self.wait_sync_anim_part_cache) do
			if k ~= SceneObjPart.Main and v.status == SENCE_OBJ_LOADED_STATUS.LOADED then
				local a_part = self:GetPart(k)
				local part_obj = a_part:GetObj()
				if part_obj and part_obj.animator then
					-- print_error("------ <color=#9DF5A7FF>动作同步 check 222</color>---", SceneObjPartStr[k], self.root.transform.name)
					self.actor_animator_syncter:SetDrawPartAnimator(part_obj.animator)
				end
			end
		end
	else
		sync_anim_part_fun()
	end
end

-- 动作同步 移除 by.Ljh
function DrawObj:RemoveActorAnimatorSyncter(part)
	-- print_error("------ <color=#ff0000>动作同步 remove</color> ---", SceneObjPartStr[part], self.root.transform.name)
	if IsNil(self.actor_animator_syncter) then
		return
	end

	-- if part == SceneObjPart.Weapon then
	-- 	print_error("------ <color=#ff0000>动作同步 remove</color> ---", SceneObjPartStr[part], self.wait_sync_anim_part_cache[part])
	-- end

	if not self.wait_sync_anim_part_cache or not self.wait_sync_anim_part_cache[part] then
		return
	end

	if self.wait_sync_anim_part_cache[SceneObjPart.Main].status ~= SENCE_OBJ_LOADED_STATUS.LOADED then
		return
	end

	if part == SceneObjPart.Main then
		-- for k,v in pairs(self.wait_sync_anim_part_cache) do
		-- 	if k ~= SceneObjPart.Main then
		-- 		local a_part = self:GetPart(k)
		-- 		local part_obj = a_part:GetObj()
		-- 		if part_obj and part_obj.animator then
		-- 			-- print_error("------ <color=#ff0000>动作同步 remove 222</color> ---", SceneObjPartStr[k], self.root.transform.name)
		-- 			self.actor_animator_syncter:RemoveDrawPartAnimator(part_obj.animator)
		-- 		end
		-- 	end
		-- end

		self.actor_animator_syncter:ClearSyncTargetAnimatorData()
		self.actor_animator_syncter = nil
	else
		local cahce_part_data = self.wait_sync_anim_part_cache[part]
		if cahce_part_data.status == SENCE_OBJ_LOADED_STATUS.WAIT or cahce_part_data.status == SENCE_OBJ_LOADED_STATUS.LOADED then
			local a_part = self:GetPart(part)
			local part_obj = a_part:GetObj()
			if part_obj and part_obj.animator then
				-- print_error("------ <color=#ff0000>动作同步 remove 333</color> ---", SceneObjPartStr[part], self.root.transform.name)
				self.actor_animator_syncter:RemoveDrawPartAnimator(part_obj.animator)
			end
		end
	end
end

-- ======================================== 等待部位加载完成执行动画 =================================================
function DrawObj:SetLoadedEntiretyStatus(part_type, is_loaded, bundle, asset, extra_model_data)
	if is_loaded == nil then
		self.wait_part_loaded_status_list[part_type] = nil
		return
	end

	local tab = self.wait_part_loaded_status_list[part_type]
	if not tab then
		self.wait_part_loaded_status_list[part_type] = {
			is_loaded = is_loaded,
			bundle = bundle,
			asset = asset,
			extra_model_data = extra_model_data,
		}
	else
		local is_need_change = true
		if is_loaded == false and (tab.bundle == bundle and tab.asset == asset) then
			if IsSameTable(tab.extra_model_data, extra_model_data) then
				is_need_change = false
			end
		end

		if is_need_change then
			tab.is_loaded = is_loaded
		end
	end
end

function DrawObj:CheckEntiretyAction(part_type)
	local tab = self.wait_part_loaded_status_list[part_type]
	if not tab or not tab.is_loaded or not tab.anim_data then
		return
	end

	local anim_data = tab.anim_data
	self:DoEntiretyAction(part_type, anim_data.anim_name,
	anim_data.check_same_action,
	anim_data.time,
	anim_data.is_force)
end

function DrawObj:DoEntiretyAction(part_type, action, check_same_action, time, is_force)
	local tab = self.wait_part_loaded_status_list[part_type]
	if not tab then
		return
	end

	if not tab.is_loaded then
		tab.anim_data = {
			anim_name = action,
			check_same_action = check_same_action,
			time = time,
			is_force = true,
		}
		return
	end

	tab.anim_data = nil
	local play_part = self:GetPart(part_type)
	if play_part ~= nil then
		play_part:CrossFade(action, check_same_action, time, is_force)
	end
	
	--这里过滤一下双生天神，双生天生要一起跟着做动作，会影响原来的动作回调
	if not self:GetIsNeedSyncAnim() then
		if self.start_do_action_call_back then
			self.start_do_action_call_back()
		end
	end
end

function DrawObj:SetMountLoadedAndUpCallBack(call_back)
	self.mount_loaded_and_up_call_back = call_back
end

-- 上坐骑 等待加载后挂点偏移
function DrawObj:ChangeAttachmentByAddMount()
	local part_mount = SceneObjPart.Mount
	local part_main = SceneObjPart.Main
	local is_ui_show_model = self:GetIsRoleModel()
	if not self.wait_part_loaded_status_list[part_main] or not self.wait_part_loaded_status_list[part_mount] then
		return
	end

	if not self.wait_part_loaded_status_list[part_main].is_loaded or not self.wait_part_loaded_status_list[part_mount].is_loaded then
		return
	end

	local part_weapon = SceneObjPart.Weapon
	-- 目前只有UI模型乘骑挂武器
	if is_ui_show_model then
		local waepon_tab = self.wait_part_loaded_status_list[part_weapon]
		if is_ui_show_model and not (not waepon_tab or (waepon_tab and waepon_tab.is_loaded)) then
			return
		end
	end

	local attachment = self:_TryGetPartAttachment(part_main)
	if not attachment then
		return
	end

	local mount_obj = self:_TryGetPartObj(part_mount)
	if mount_obj ~= nil then
		attachment:AddMount(mount_obj.gameObject)
		if self.mount_loaded_and_up_call_back then
			self.mount_loaded_and_up_call_back()
		end
	end

	self:UpdateFabaoPosition()
	self:ChangeFaZhenAttachObjectAttached()

	-- 目前只有UI模型乘骑挂武器
	if is_ui_show_model then
		local weapon_attachment = self:_TryGetPartAttachment(part_weapon)
		if weapon_attachment then
			if mount_obj ~= nil then
				weapon_attachment:AddMount(mount_obj.gameObject)
			end
		end
	end
end





-- ======================================== 部位同步动画 ============================================================
-- 重置等待同步动画是否加载缓存
function DrawObj:ResetWaitSyncAnimPartCache()
	self.cur_wait_sync_anim_type = nil			-- 当前部位同步动画类型
    self.wait_sync_anim_part_cache = nil		-- 记录已加载的部位
    self.wait_sync_anim_data = nil				-- 需要同步动画的数据
	self.wait_sync_trigger_action_name = nil	-- 需要同步动画的trigger
	self.wait_sync_bool_action_list = nil
	self.is_need_sync_anim = nil
end

function DrawObj.WaitSyncAnimData()
	return
	{
		status = SENCE_OBJ_LOADED_STATUS.WAIT,
		res_name = "",
	}
end

function DrawObj:SetIsNeedSyncAnim(bool)
	self.is_need_sync_anim = bool
end

function DrawObj:GetIsNeedSyncAnim()
	return self.is_need_sync_anim and self.wait_sync_anim_part_cache ~= nil
end

function DrawObj:SetStartDoActionCallBack(call_back)
	self.start_do_action_call_back = call_back
end

function DrawObj:CrossAction(part_type, action, check_same_action, time, is_force)
	-- if self.parent_obj.IsMainRole and self.parent_obj:IsMainRole() then
	-- 	print_error("---DrawObj:CrossAction--", SceneObjPartStr[part_type], action, self:GetIsNeedSyncAnim())
	-- end

	if part_type == SceneObjPart.Main and self:GetIsNeedSyncAnim() then
		self:TryPlayPartSyncAnim(part_type, action, check_same_action, time, is_force)
	else
		self:DoEntiretyAction(part_type, action, check_same_action, time, is_force)
		if self:GetIsNeedSyncAnim() and self:GetIsWaitSyncAnimPart(part_type) then
			print_error("【动作执行逻辑有误】", part_type, action)
		end
	end
end

function DrawObj:SetActionTrigger(part_type, name)
	-- print_error("---DrawObj:SetActionTrigger--", SceneObjPartStr[part_type], name, self:GetIsNeedSyncAnim())
	if part_type == SceneObjPart.Main and self:GetIsNeedSyncAnim() then
		self:TryPlayPartSyncActionTrigger(name)
	else
		local play_part = self:GetPart(part_type)
		if play_part ~= nil then
			play_part:SetTrigger(name)
		end

		if self:GetIsNeedSyncAnim() and self:GetIsWaitSyncAnimPart(part_type) then
			print_error("【动作trigger执行逻辑有误】", part_type, name)
		end
	end
end

function DrawObj:SetActionBool(part_type, name, bool)
	-- print_error("---DrawObj:SetActionBool--", SceneObjPartStr[part_type], name, bool, self:GetIsNeedSyncAnim())
	if part_type == SceneObjPart.Main and self:GetIsNeedSyncAnim() then
		self:TryPlayPartSyncActionBool(name, bool)
	else
		local play_part = self:GetPart(part_type)
		if play_part ~= nil then
			play_part:SetBool(name, bool)
		end

		if self:GetIsNeedSyncAnim() and self:GetIsWaitSyncAnimPart(part_type) then
			print_error("【动作bool执行逻辑有误】", part_type, name)
		end
	end
end

function DrawObj:SetActionFloat(part_type, name, float_value)
	-- print_error("---DrawObj:SetActionFloat--", SceneObjPartStr[part_type], name, bool, self:GetIsNeedSyncAnim())
	local play_part = self:GetPart(part_type)
	if play_part ~= nil then
		play_part:SetFloat(name, float_value)
	end
end

-- local anim_type = {
-- 	[0] = "人物",
-- 	"天神",
-- 	"高达",
-- }
function DrawObj:ChangeWaitSyncAnimType(type, force_change)
	-- print_error("-----改变类型为：", anim_type[type], "  旧的为：", anim_type[self.cur_wait_sync_anim_type])
	if type == self.cur_wait_sync_anim_type and not force_change then
		return
	end

	self:CleanPlayPartSyncAnimFixedDelay()
	self.cur_wait_sync_anim_type = type
	self.wait_sync_trigger_action_name = nil
	self.wait_sync_bool_action_list = nil
	self.wait_sync_anim_data = {
		anim_name = SceneObjAnimator.Idle,
		check_same_action = true,
		time = 0,
		is_force = false,
	}

	local need_part_list
	if not type then
		self.wait_sync_anim_part_cache = nil

	elseif type == SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.ROLE or type == SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.TIAN_SHEN then
		need_part_list = {SceneObjPart.Main, SceneObjPart.Weapon}

	elseif type == SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.GUNDAM then
		need_part_list = {
			SceneObjPart.Main, SceneObjPart.Weapon,
			SceneObjPart.GundamLArm, SceneObjPart.GundamRArm,
			SceneObjPart.GundamLLeg, SceneObjPart.GundamRLeg,
			SceneObjPart.GundamLWing, SceneObjPart.GundamRWing,
		}
	end

	if need_part_list then
		self.wait_sync_anim_part_cache = {}
		for k,v in pairs(need_part_list) do
			self.wait_sync_anim_part_cache[v] = DrawObj.WaitSyncAnimData()
		end
	end
end

local empty_tab = {}
function DrawObj:GetIsWaitSyncAnimPart(part)
	return (self.wait_sync_anim_part_cache or empty_tab)[part] ~= nil
end

-- local wait_type = {
-- 	[0] = "等待",
-- 	"已加载",
-- 	"假隐藏",
-- }
function DrawObj:ChangeWaitSyncAnimPartCache(part, status, res_name)
	if not self:GetIsWaitSyncAnimPart(part) then
        return
    end

	-- if self.parent_obj.IsMainRole and self.parent_obj:IsMainRole() then
	-- 	print_error("-----改变部位cache状态------", SceneObjPartStr[part], part, wait_type[status], res_name)
	-- end

	if status == SENCE_OBJ_LOADED_STATUS.WAIT then
		if self.wait_sync_anim_part_cache[part].res_name == res_name then
			return
		end

		self.wait_sync_anim_part_cache[part].res_name = res_name
	end

	self.wait_sync_anim_part_cache[part].status = status
end

-- 设置等待同步动画是否加载缓存
function DrawObj:SetWaitSyncAnimPartIsLoaded(part)
	if not self:GetIsWaitSyncAnimPart(part) then
        return
    end

	self:ChangeWaitSyncAnimPartCache(part, SENCE_OBJ_LOADED_STATUS.LOADED)
	-- if self.parent_obj.IsMainRole and self.parent_obj:IsMainRole() then
	-- 	print_error("-----加载 缓存------", SceneObjPartStr[part], wait_type[SENCE_OBJ_LOADED_STATUS.LOADED])
	-- end

	if self.wait_sync_bool_action_list ~= nil then
		for k,v in pairs(self.wait_sync_bool_action_list) do
			self:TryPlayPartSyncActionBool(k, v)
		end
	end

    if self.wait_sync_anim_data ~= nil then
        self:TryPlayPartSyncAnim(part, self.wait_sync_anim_data.anim_name,
								self.wait_sync_anim_data.check_same_action,
								self.wait_sync_anim_data.time,
								self.wait_sync_anim_data.is_force)
	elseif part ~= SceneObjPart.Main then
		self:TryPlayPartSyncAnim(part)
    end

	if self.wait_sync_trigger_action_name ~= nil then
		self:TryPlayPartSyncActionTrigger(self.wait_sync_trigger_action_name)
	end
end

-- 设置等待同步动画是否加载缓存
function DrawObj:SetWaitSyncAnimPartIsRemove(part, real_remove)
	if not self:GetIsWaitSyncAnimPart(part) then
        return
    end

	local status = real_remove and SENCE_OBJ_LOADED_STATUS.WAIT or SENCE_OBJ_LOADED_STATUS.FAKE_REMOVE
	-- if self.parent_obj.IsMainRole and self.parent_obj:IsMainRole() then
	-- 	print_error("-----移除 缓存------", SceneObjPartStr[part], wait_type[status])
	-- end

	self:ChangeWaitSyncAnimPartCache(part, status)

	if self.wait_sync_bool_action_list ~= nil then
		for k,v in pairs(self.wait_sync_bool_action_list) do
			self:TryPlayPartSyncActionBool(k, v)
		end
	end

    if self.wait_sync_anim_data ~= nil then
        self:TryPlayPartSyncAnim(part, self.wait_sync_anim_data.anim_name,
								self.wait_sync_anim_data.check_same_action,
								self.wait_sync_anim_data.time,
								self.wait_sync_anim_data.is_force)
    end

	if self.wait_sync_trigger_action_name ~= nil then
		self:TryPlayPartSyncActionTrigger(self.wait_sync_trigger_action_name)
	end
end

-- 部位动画同步
function DrawObj:TryPlayPartSyncAnim(part, anim_name, check_same_action, time, is_force)
    if self.wait_sync_anim_part_cache == nil then
        return
    end

	local main_is_loaded = self.wait_sync_anim_part_cache[SceneObjPart.Main].status == SENCE_OBJ_LOADED_STATUS.LOADED
	local part_is_loaded = self.wait_sync_anim_part_cache[part].status == SENCE_OBJ_LOADED_STATUS.LOADED

	-- if self.parent_obj.IsMainRole and self.parent_obj:IsMainRole() then
		-- print_error("-----部位动画同步 111------", part, anim_name, main_is_loaded, part_is_loaded)
	-- end

	-- 主部位未加载，缓存动画信息
	if not main_is_loaded then
		if not self.wait_sync_anim_data then
			self.wait_sync_anim_data = {
				anim_name = anim_name,
				check_same_action = check_same_action,
				time = time,
				is_force = true,
			}
		else
			self.wait_sync_anim_data.anim_name = anim_name
			self.wait_sync_anim_data.check_same_action = check_same_action
			self.wait_sync_anim_data.time = time
			self.wait_sync_anim_data.is_force = is_force
		end
	else
		if self.wait_sync_anim_data then
			self.wait_sync_anim_data = nil
		end

		-- 主部位加载，与已加载部位一同执行动画
		if part == SceneObjPart.Main then
			for k,v in pairs(self.wait_sync_anim_part_cache) do
				if v.status == SENCE_OBJ_LOADED_STATUS.LOADED then
					local play_part = self:GetPart(k)
					play_part:CrossFade(anim_name, check_same_action, time, is_force)
				end
			end

			if self.start_do_action_call_back then
				self.start_do_action_call_back()
			end
		-- 后加载的非主部位，强制与主部位动画同步
		else
			if self.actor_animator_syncter and part_is_loaded then
				local a_part = self:GetPart(part)
				local part_obj = a_part:GetObj()
				if part_obj and part_obj.animator then
					self.actor_animator_syncter:SyncAnimationImmediate(part_obj.animator)
				end
			end
		end
	end
end

function DrawObj:CleanPlayPartSyncAnimFixedDelay()
	if self.delay_play_part_sync_anim_fixed_timer then
		GlobalTimerQuest:CancelQuest(self.delay_play_part_sync_anim_fixed_timer)
		self.delay_play_part_sync_anim_timer = nil
	end
end


-- 修正
function DrawObj:FixedPlayPartSyncAnim()
	if self.wait_sync_anim_part_cache == nil then
        return
    end

	for k,v in pairs(self.wait_sync_anim_part_cache) do
		if v.status == SENCE_OBJ_LOADED_STATUS.LOADED then
			local play_part = self:GetPart(k)
			play_part:CheckAniPlay()
		end
    end
end

-- 部位 trigger同步
function DrawObj:TryPlayPartSyncActionTrigger(action_name)
    if self.wait_sync_anim_part_cache == nil then
        return
    end

    for k,v in pairs(self.wait_sync_anim_part_cache) do
        if v.status == SENCE_OBJ_LOADED_STATUS.WAIT then
			-- print_error("-----部位trigger同步 等待中的------", SceneObjPartStr[k], v)
            self.wait_sync_trigger_action_name = action_name
            return
        end
    end

    self.wait_sync_trigger_action_name = nil
	for k,v in pairs(self.wait_sync_anim_part_cache) do
		if v.status == SENCE_OBJ_LOADED_STATUS.LOADED then
			local play_part = self:GetPart(k)
			-- print_error("-----部位trigger同步111------", SceneObjPartStr[k], action_name)
			play_part:SetTrigger(action_name)
		end
    end
end

-- 部位 bool同步
function DrawObj:TryPlayPartSyncActionBool(name, bool)
    if self.wait_sync_anim_part_cache == nil then
        return
    end

	for k,v in pairs(self.wait_sync_anim_part_cache) do
        if v.status == SENCE_OBJ_LOADED_STATUS.WAIT then
			if not self.wait_sync_bool_action_list then
				self.wait_sync_bool_action_list = {}
			end

			-- print_error("-----部位bool同步 等待中的------", SceneObjPartStr[k], v)
			self.wait_sync_bool_action_list[name] = bool
            return
        end
    end

	if not self.wait_sync_bool_action_list then
		self.wait_sync_bool_action_list = {}
	end

	self.wait_sync_bool_action_list[name] = bool
	-- print_error("-----部位bool同步000------", self.wait_sync_anim_part_cache, self.wait_sync_bool_action_list, name, bool)
	local bool_list = self.wait_sync_bool_action_list or {}
	self.wait_sync_bool_action_list = nil
	for k,v in pairs(self.wait_sync_anim_part_cache) do
		if v.status == SENCE_OBJ_LOADED_STATUS.LOADED then
			for i,j in pairs(bool_list) do
				local play_part = self:GetPart(k)
				-- print_error("-----部位bool同步111------", SceneObjPartStr[k], name, bool)
				play_part:SetBool(i, j)
			end
		end
    end

	self.wait_sync_bool_action_list = nil
end
-- ======================================== 部位同步动画 end ==========================================================









