﻿using UnityEngine;

[ExecuteInEditMode]
public class LimitSceneEffects : MonoBehaviour
{
    [SerializeField]
    private GameObject[] effects;

    private int curLayer = 0;

    private void OnTransformParentChanged()
    {
        if (Application.isPlaying)
        {
            this.CheckEffects();
        }
    }

    private void Update()
    {
        if (this.curLayer != this.gameObject.layer)
        {
            this.curLayer = this.gameObject.layer;
            this.CheckEffects();
        }
    }

    private void CheckEffects()
    {
        var display = this.GetComponentInParent<Nirvana.UI3DDisplay>();
        if(display != null || this.gameObject.layer == GameLayers.UIScene)
        {
            this.ShowEffects();
            return;
        }
        if(this.effects == null || this.effects.Length <= 0)
        {
            return;
        }
        foreach(var effects in this.effects)
        {
            if(effects != null)
            {
                effects.SetActive(false);
            }
        }
    }

    private void ShowEffects()
    {
        if(this.effects == null || this.effects.Length <= 0)
        {
            return;
        }
        foreach(var effects in this.effects)
        {
            if(effects != null)
            {
                effects.SetActive(true);
            }
        }
    }
}
