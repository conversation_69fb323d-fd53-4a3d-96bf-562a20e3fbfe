
DayCounterWGData = DayCounterWGData or BaseClass(BaseWGCtrl)

function DayCounterWGData:__init()
	if DayCounterWGData.Instance ~= nil then
		ErrorLog("[DayCounterWGData] attempt to create singleton twice!")
		return
	end
	DayCounterWGData.Instance = self

	self.day_count_list = {}
end

function DayCounterWGData:__delete()
	DayCounterWGData.Instance = nil
end

function DayCounterWGData:SetDayCount(day_counter_id, count)
	self.day_count_list[day_counter_id] = count

	if DAY_COUNT.DAYCOUNT_ID_GUILD_CHUANGONG_REWARD_EXP_TIMES == day_counter_id then
		if count > 0 then
			MainuiWGCtrl.Instance:ActivitySetButtonVisible(ACTIVITY_TYPE.GUILD_CHUAN_GONG, false)
			ActivityWGCtrl.Instance:RemoveActNotice(ACTIVITY_TYPE.GUILD_CHUAN_GONG)
		end
	elseif DAY_COUNT.DAYCOUNT_ID_FINISH_HUSONG_TASK_COUNT == day_counter_id then
		if YunbiaoWGData.Instance:GetHusongRemainTimes() == 0 then
			MainuiWGCtrl.Instance:ActivitySetButtonVisible(ACTIVITY_TYPE.HUSONG, false)
			ActivityWGCtrl.Instance:RemoveActNotice(ACTIVITY_TYPE.HUSONG)
		end
	end
end

function DayCounterWGData:GetDayCount(day_counter_id)
	if nil ~= self.day_count_list[day_counter_id] then
		return self.day_count_list[day_counter_id]
	end
	
	return 0
end
