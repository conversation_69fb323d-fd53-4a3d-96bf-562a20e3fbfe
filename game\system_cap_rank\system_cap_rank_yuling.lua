SystemCapRankYuLingView = SystemCapRankYuLingView or BaseClass(SafeBaseView)
function SystemCapRankYuLingView:__init()
    self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/system_cap_rank_ui_prefab", "layout_system_cap_rank")
end

function SystemCapRankYuLingView:LoadCallBack()
    self.node_list["btn_rank_tips"].button:AddClickListener(BindTool.Bind(self.OnClickOpenRankView, self))
    self.node_list["btn_close"].button:AddClickListener(BindTool.Bind(self.Close, self))
    self.node_list["goto_tz_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGoToTiZhanBtn, self))

    self.rank_list_view = AsyncListView.New(SystemCapRankItem, self.node_list["rank_reward_list"])

    self.node_list["rule_desc"].text.text = Language.SystemCapRank.YuLingRankDesc

    self:LoadModel()
end

function SystemCapRankYuLingView:ReleaseCallBack()
    if self.rank_list_view then
        self.rank_list_view:DeleteMe()
        self.rank_list_view = nil
    end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end

    if CountDownManager.Instance:HasCountDown("system_yuling_rank_down") then
		CountDownManager.Instance:RemoveCountDown("system_yuling_rank_down")
	end
end

function SystemCapRankYuLingView:OpenCallBack()
    local cfg_type = SystemCapRankWGData.Instance:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_YULING)
    if cfg_type then
        SystemCapRankWGData.Instance:SetCurActRewardType(cfg_type.type)
        RankWGCtrl.Instance:SendGetPersonRankListReq(cfg_type.rank_type, nil)
    end
end

function SystemCapRankYuLingView:OnFlush(param_t)
    for k, v in pairs(param_t) do
		if "all" == k then
			self:FlushView()
		end
	end

end

function SystemCapRankYuLingView:FlushView()
    self:LoginTimeCountDown()
    local cfg_type = SystemCapRankWGData.Instance:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_YULING)
    if cfg_type then
        local reward_list = SystemCapRankWGData.Instance:GetActRewardCfg(cfg_type.type)
        if reward_list then
            self.rank_list_view:SetDataList(reward_list)
        end

        local bundle, asset = ResPath.GetRawImagesPNG("a1_tzb_title_" .. cfg_type.type)
        self.node_list["title_bg"].raw_image:LoadSprite(bundle, asset, function()
            self.node_list["title_bg"].raw_image:SetNativeSize()
        end)
    end

    local bg_bundle, bg_asset = ResPath.GetRawImagesPNG("a1_tzb_yl")
    self.node_list["raw_image"].raw_image:LoadSprite(bg_bundle, bg_asset, function()
        self.node_list["raw_image"].raw_image:SetNativeSize()
    end)

    local total_zhanli = SystemCapRankWGData.Instance:GetMyRankValue()
	self.node_list["my_cap"].text.text = total_zhanli or 0

    local rank_data = SystemCapRankWGData.Instance:GetMyRankInfo()
	if rank_data ~= nil then
		self.node_list["my_rank"].text.text = string.format(Language.SystemCapRank.RechargeRankTitle2, rank_data.rank_index)
	else
		self.node_list["my_rank"].text.text = Language.SystemCapRank.RechargeNoRank
	end

    self.node_list.goto_tz_btn:SetActive(cfg_type.open_panel ~= "")
end

function SystemCapRankYuLingView:OnClickGoToTiZhanBtn()
    local view_name = SystemCapRankWGData.Instance:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_YULING).open_panel
    FunOpen.Instance:OpenViewByName(view_name)
end

function SystemCapRankYuLingView:LoadModel()
    if self.model_display == nil then
		self.model_display = OperationActRender.New(self.node_list["model_root"])
	end

    local cfg_type = SystemCapRankWGData.Instance:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_YULING)
    if cfg_type and cfg_type.model_show > 0 then
        local data = {}
        data.item_id = cfg_type.model_show
        data.render_type = 0
        data.position = Vector3(0, 0, 0)
	    data.rotation = Vector3(0, 0, 0)
        data.scale = Vector3(1, 1, 1)
        self.model_display:SetData(data)
    end
end

function SystemCapRankYuLingView:OnClickOpenRankView()
    local cfg_type = SystemCapRankWGData.Instance:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_YULING)
    if cfg_type and cfg_type.rank_type then
        SystemCapRankWGCtrl.Instance:OpenRankTipsView(cfg_type.rank_type)
    end
end


------------------------------------活动时间倒计时
function SystemCapRankYuLingView:LoginTimeCountDown()
    local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_YULING)
	if activity_data ~= nil then
		local invalid_time = activity_data.end_time
        if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
            self.node_list["act_time"].text.text = TimeUtil.FormatSecondDHM8(invalid_time - TimeWGCtrl.Instance:GetServerTime())
            CountDownManager.Instance:AddCountDown("system_yuling_rank_down", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
        end
	end
end

function SystemCapRankYuLingView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.login_act_date = TimeUtil.FormatUnixTime2Date()
		self.node_list["act_time"].text.text = TimeUtil.FormatSecondDHM8(valid_time)
	end
end

function SystemCapRankYuLingView:OnComplete()
    self.node_list["act_time"].text.text = ""
	self:Close()
end