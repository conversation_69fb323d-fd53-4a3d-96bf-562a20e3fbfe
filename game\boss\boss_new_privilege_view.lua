BossNewPrivilegeView = BossNewPrivilegeView or BaseClass(SafeBaseView)

function BossNewPrivilegeView:__init()
	self.view_style = ViewStyle.Half
	self.default_index = MarketViewIndex.Shop.shop_limit
	self.is_safe_area_adapter = true
	self:SetMaskBg()

	local bundle = "uis/view/boss_ui_prefab"
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
	self:AddViewResource(TabIndex.boss_mabi_skill, bundle, "layout_bossmabi_skill_show_view")
	self:AddViewResource(TabIndex.boss_godwar, bundle, "layout_boss_godwar_view")
	self:AddViewResource(TabIndex.boss_zaibaoyici, bundle, "layout_boss_privilege_activate")
	self:AddViewResource(TabIndex.boss_kill_every, bundle, "layout_boss_kill_every")
	self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar_Activity")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function BossNewPrivilegeView:LoadCallBack()
	self:InitTabbar()

	self.node_list.title_view_name.text.text = Language.BossPrivilege.TitleName
end

function BossNewPrivilegeView:ReleaseCallBack()
	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	self:BossMaBiReleaseCallBack()
	self:BossGodwarReleaseCallBack()
end

function BossNewPrivilegeView:OpenCallBack()
	self:ZBYCOpenCallBack()
end

function BossNewPrivilegeView:LoadIndexCallBack(index)
	if index == TabIndex.boss_mabi_skill then
		self:BossMaBiLoadCallBack()
	elseif index == TabIndex.boss_godwar then
		self:BossGodwarLoadCallBack()
	elseif index == TabIndex.boss_zaibaoyici then
		self:ZBYCLoadCallBack()
	elseif index == TabIndex.boss_kill_every then
		self:BossKillEveryLoadCallBack()
	end
end

function BossNewPrivilegeView:ShowIndexCallBack(index)
	local bundle, asset = ResPath.GetRawImagesPNG("a3_boss_bg_zbyc")
	if index == TabIndex.boss_mabi_skill then
		bundle, asset = ResPath.GetRawImagesPNG("a3_boss_bg_zbyc")
		self:BossMaBiShowIndexCallBack()
		self.node_list.bossmabi_skill_show_bg.animation_player:Play("Play")
	elseif index == TabIndex.boss_godwar then
		bundle, asset = ResPath.GetRawImagesPNG("a3_boss_bg_xmsn")
		self.node_list.boss_godwar_bg.animation_player:Play("Play")
	elseif index == TabIndex.boss_zaibaoyici then
		bundle, asset = ResPath.GetRawImagesPNG("a3_boss_bg_lmbd")
	elseif index == TabIndex.boss_kill_every then
		bundle, asset = ResPath.GetRawImagesJPG("a3_boss_bg_mmsl")
	end

	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)
end

function BossNewPrivilegeView:OnFlush(param_t, index)
	if index == TabIndex.boss_mabi_skill then
		self:BossMaBiOnFlush()
	elseif index == TabIndex.boss_godwar then
		self:BossGodwarOnFlush()
	elseif index == TabIndex.boss_zaibaoyici then
		self:ZBYCOnFlush()
	elseif index == TabIndex.boss_kill_every then
		self:BossKillEveryOnFlush()
	end

	local god_war_data = BossGodWarWGData.Instance:GetPrivilegeInfo()
	--已经购买也开启
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.boss_godwar) or (god_war_data and god_war_data.level > -1)
	self.tabbar:SetToggleVisible(TabIndex.boss_godwar, is_open)
end

function BossNewPrivilegeView:InitTabbar()
	if not self.tabbar then
		local remind_tab = {
			{},
			{ RemindName.BossGodwar },
			{}
		}
		self.tabbar = Tabbar.New(self.node_list, "VerticalTabbar_Activity")
		self.tabbar:SetVerTabbarCellName("VerticalTabbarCell_Activity")
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		self.tabbar:Init(Language.BossPrivilege.VerTabGroup, nil, ResPath.CommonBundleName, nil, remind_tab)
		FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.BossNewPrivilegeView, self.tabbar)
	end
end
