BaGuaMiZhenTaskView = BaGuaMiZhenTaskView or BaseClass(SafeBaseView)

function BaGuaMiZhenTaskView:__init()
	self.view_cache_time = 1 --界面缓存时间
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_baguamizhen_info")
	self.is_pass = -1
	self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
	self.active_close = false
end

function BaGuaMiZhenTaskView:__delete()
end

function BaGuaMiZhenTaskView:ReleaseCallBack()
	self.is_pass = -1

	if self.info then
		self.info:DeleteMe()
		self.info = nil
	end

	if self.mainui_open_complete_handle then
		GlobalEventSystem:UnBind(self.mainui_open_complete_handle)
		self.mainui_open_complete_handle = nil
	end
 
	self.star_effect = nil
	self.is_init = false

	self.need_move = nil
	self.boss_special_toggle_param = nil
	if self.boss_reward_list then
       self.boss_reward_list:DeleteMe()
       self.boss_reward_list = nil
    end
end

function BaGuaMiZhenTaskView:ShowIndexCallBack()
    if MainuiWGCtrl.Instance:IsLoadMainUiView() then
		self:InitCallBack()
	else
		self.mainui_open_complete_handle = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.InitCallBack, self))
	end
	self:Flush()
end

function BaGuaMiZhenTaskView:LoadCallBack()
	self.boss_reward_list = AsyncBaseGrid.New()
	self.boss_reward_list:CreateCells({col = 1, change_cells_num = 1, list_view = self.node_list.reward_list,
                                                assetBundle = "uis/view/guild_shouhu_ui_prefab",
                                                assetName = "shouhu_reward_render",
                                                itemRender = ConditionPaiPinItemRender, })
	self.boss_reward_list:SetStartZeroIndex(false)
	local layer = FuBenWGData.Instance:GetBaGuaMiZhenFBStatus()
    local reward_cfg = BaGuaMiZhenWGData.Instance:GetBaGuaZhenRewardList(layer)	
	self.boss_reward_list:SetDataList(reward_cfg, 3)
end

function BaGuaMiZhenTaskView:InitCallBack()
	local mainui_ctrl = MainuiWGCtrl.Instance
    local parent = mainui_ctrl:GetTaskOtherContent()
    if not self.info then
        self.info = BaGuaMiZhenTaskList.New(self.node_list.root_fuben_info)
    end

	self.info:SetInstanceParent(parent)
	self.info:AnchorPosition(1, 0)
	MainuiWGCtrl.Instance:SetBtnLevel(true)
	
	self.task_panel_change = true
	mainui_ctrl:SetTaskPanel(false,151,-185)
	mainui_ctrl:SetAutoGuaJi(true)
	self.is_init = true
	self:Flush()
end


function BaGuaMiZhenTaskView:ResetTaskPanel()
	if self.task_panel_change then
		MainuiWGCtrl.Instance:ResetTaskPanel()
		self.task_panel_change = false
	end

	if self.info then
		self.info:SetInstanceParent(self.node_list.layout_bagua_fuben)
	end
end

function BaGuaMiZhenTaskView:CloseCallBack()
	self.is_pass = -1
	MainuiWGCtrl.Instance:SetAutoGuaJi(false)
end

function BaGuaMiZhenTaskView:OnFlush()
	if not self.is_init then
		return
	end
	
	local layer = FuBenWGData.Instance:GetBaGuaMiZhenFBStatus()
    if layer <= 0 then
		return
	end

	local reward_cfg = BaGuaMiZhenWGData.Instance:GetBaGuaZhenRewardList(layer)	
    self.boss_reward_list:SetDataList(reward_cfg, 3)
	local zhanli_tuijian = BaGuaMiZhenWGData.Instance:GetBaGuaMiZhenZhanLiBuyLevelAndLayer(layer)
	if self.info then
		self.info:SetTuijianCount(zhanli_tuijian)
		self.info:SetBossCount(layer)
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local scence_info = FuBenWGData.Instance:GetFbSceneLogicInfo()
	if scence_info.flush_timestamp and scence_info.flush_timestamp ~= 0 and server_time < scence_info.flush_timestamp then

	else
		MainuiWGCtrl.Instance:SetShowTimeTextState(true)
	end
end

function BaGuaMiZhenTaskView:CompleteFlushTime()
	self:Flush()
end

BaGuaMiZhenTaskList = BaGuaMiZhenTaskList or BaseClass(BaseRender)
function BaGuaMiZhenTaskList:__init()
	-- body
end

function BaGuaMiZhenTaskList:__delete()
	-- body
end

function BaGuaMiZhenTaskList:AnchorPosition(x, y)
	self.view.rect.anchoredPosition = Vector2(x,y)
end

function BaGuaMiZhenTaskList:SetBossCount(layer)
	-- local bundle, asset = ResPath.GetCommonIcon("a3_zjm_fb_diff_" .. layer)
	-- self.node_list.diff_icon.image:LoadSprite(bundle, asset, function()
	-- 	self.node_list.diff_icon.image:SetNativeSize()
	-- end)

	local bgmz_cur_data_list = BaGuaMiZhenWGData.Instance:GetBossInfoCfg()
	local cur_data = bgmz_cur_data_list[layer]
	if cur_data and cur_data.name_fb then
		self.node_list.text_diff.text.text = cur_data.name_fb
	end

end

function BaGuaMiZhenTaskList:SetTuijianCount(tuijianzhenli)
	local role_zhanli = RoleWGData.Instance:GetRoleVo().capability
	local is_green = role_zhanli >= tuijianzhenli
	local tuijian_zhanli = ToColorStr(tuijianzhenli, is_green and COLOR3B.D_GREEN or COLOR3B.D_RED)
	self.node_list.tuijian_zhanli.text.text = string.format(Language.FuBen.TuiJianZhanLi, tuijian_zhanli)
end
