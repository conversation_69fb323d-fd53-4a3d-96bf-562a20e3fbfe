﻿    Shader "Hidden/Billboard/AlbedoBake"
{
    Properties
    {
        _BaseMap ("Texture", 2D) = "white" {}
        _BaseColor ("_Color", Color) = (1,1,1,1)
        _Cutoff("Cutoff" , Range(0,1)) = 0.3
    }
    
    CGINCLUDE
    #include "UnityCG.cginc"

    sampler2D _BaseMap;
    float4 _BaseColor;
    float _Cutoff;

    struct Varyings
    {
        float2 uv : TEXCOORD0;
        float4 vertex : SV_POSITION;
    };

    Varyings vert(float4 uv : TEXCOORD0, float4 vertex : POSITION, float3 normal : NORMAL, float4 tangent : TANGENT)
    {
        Varyings o;
        o.vertex = UnityObjectToClipPos(vertex);
        o.uv = uv;
        return o;
    }
    
    ENDCG

    SubShader
    {
        Cull Off

        Pass
        {
            Name "Bake Albedo"
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            float4 frag(Varyings input) : SV_Target
            {
                float4 col = tex2D(_BaseMap, input.uv);
                col.rgb *= LinearToGammaSpace(_BaseColor.rgb);
                col.rgb = LinearToGammaSpace(col.rgb);
                clip(col.a - max(_Cutoff - 0.05, 0));
                return float4(col.rgba);
            }
            ENDCG
        }
    }

}