local MeridiansType =
{
	Meridians = 1,		--打通.
	UpMeridians = 2,	--突破.
}

RoleView.LongMaiJiaChengAttr = 100000

function RoleView:JingMaiInit()
    self.jing_mai_item_list = nil
    self.jm_attr_item_list = nil

    self.meridians_cambered_list = nil
    self.jm_attr_list = nil
    self.curr_select_whole_body = 1
    self.meridians_drag_select_index = -1
	self.meridians_btn_select_index = -1
    self.is_auto_meridians = false
    self.is_auto_condensate = false
    self.jingmai_timer = nil
    self.meridians_list = nil

    self.jing_mai_list_loader = nil
    self.jing_mai_old_select_index = nil

    self.jing_mai_cur_tab_index = 1
end

function RoleView:JingMaiCloseCallBack()
    self.is_auto_meridians = false
    self.is_auto_condensate = false
end

function RoleView:JingMaiLoadCallBack()
    if self.jing_mai_item_list == nil then
        self.jing_mai_item_list = {}
        for i = 1, 12 do
            local jing_mai_cell = JingMaiPartItem.New()
            jing_mai_cell:SetIndex(i)
            self.jing_mai_item_list[i] = jing_mai_cell
        end
    end

    if self.jm_attr_list == nil then
        self.jm_attr_list = {}
        self.jm_attr_item_list = {}
        for i = 1, 6 do
            self.jm_attr_list[i] = self.node_list["layout_attr"]:FindObj(string.format("attr_%d", i))
            if self.jm_attr_list[i] then
                local cell = CommonAddAttrRender.New(self.jm_attr_list[i])
                cell:SetIndex(i)
                cell:SetAttrNameNeedSpace(true)
                self.jm_attr_item_list[i] = cell
            end
        end
    end

    if not self.meridians_item then
        self.meridians_item = ItemCell.New(self.node_list.meridians_icon)
    end

	if not self.up_meridians_item then
        self.up_meridians_item = ItemCell.New(self.node_list.up_meridians_icon)
    end

    -- 按钮事件绑定
    XUI.AddClickEventListener(self.node_list.meridians_down, BindTool.Bind2(self.ChangeMeridians, self, 1))
    XUI.AddClickEventListener(self.node_list.meridians_up, BindTool.Bind2(self.ChangeMeridians, self, -1))
    XUI.AddClickEventListener(self.node_list.btn_all_attr, BindTool.Bind2(self.JingMaiOpenAllAttrPanel, self))
    XUI.AddClickEventListener(self.node_list.btn_skill, BindTool.Bind2(self.JingMaiGetSkill, self))
    XUI.AddClickEventListener(self.node_list.meridians_btn, BindTool.Bind2(self.JingMaiMeridians, self))
    XUI.AddClickEventListener(self.node_list.meridians_auto, BindTool.Bind2(self.JingMaiOperateAuto, self))
    XUI.AddClickEventListener(self.node_list.condensate_btn, BindTool.Bind2(self.JingMaiOperate, self, JINGMAI_OPERA_TYPE.JINGMAI_OPERA_TYPE_BREAK_UP))
    XUI.AddClickEventListener(self.node_list.btn_rule_tips, BindTool.Bind(self.OnClickAutoTips, self))
    XUI.AddClickEventListener(self.node_list.jm_tab_toggle_1, BindTool.Bind(self.OnClickTabMeridians, self, MeridiansType.Meridians))
    XUI.AddClickEventListener(self.node_list.jm_tab_toggle_2, BindTool.Bind(self.OnClickTabMeridians, self, MeridiansType.UpMeridians))
    XUI.AddClickEventListener(self.node_list.go_up_btn, BindTool.Bind(self.OnClickGoMeridians, self, MeridiansType.Meridians))
    XUI.AddClickEventListener(self.node_list.go_meridians_btn, BindTool.Bind(self.OnClickGoMeridians, self, MeridiansType.UpMeridians))
    self.node_list["auto_toggle"].toggle:AddValueChangedListener(BindTool.Bind(self.JingMaiCondensateAuto, self))

    ---这里选出最优解
    local curr_meridians_data = JingJieWGData.Instance:GetMeridiansInfo()
    self.curr_select_whole_body = curr_meridians_data and curr_meridians_data.whole_body_id or 1
    self.meridians_drag_select_index = curr_meridians_data and curr_meridians_data.meridians_id or 1
    self.meridians_btn_select_index = curr_meridians_data and curr_meridians_data.meridians_id or 1
    self:InitCreateMeridiansCamberedList()

    RoleWGCtrl.Instance:ReqJingMaiInfo(1)

    self.node_list.jm_tab_toggle_1.toggle.isOn = true

    self.node_list["meridians_auto"]:SetActive(false)
    self.node_list["auto_toggle"]:SetActive(false)
end

function RoleView:JingMaiReleaseCallBack()
	if self.jing_mai_item_list and #self.jing_mai_item_list > 0 then
		for _, jing_mai_cell in ipairs(self.jing_mai_item_list) do
			jing_mai_cell:DeleteMe()
			jing_mai_cell = nil
		end
	end

	if self.jm_attr_item_list and #self.jm_attr_item_list > 0 then
		for _, attr_cell in ipairs(self.jm_attr_item_list) do
			attr_cell:DeleteMe()
			attr_cell = nil
		end
	end

    if self.meridians_cambered_list then
		self.meridians_cambered_list:DeleteMe()
		self.meridians_cambered_list = nil
	end

    if self.meridians_item then
		self.meridians_item:DeleteMe()
		self.meridians_item = nil
	end

	if self.up_meridians_item then
		self.up_meridians_item:DeleteMe()
		self.up_meridians_item = nil
	end

    self.jing_mai_item_list = nil
    self.jm_attr_item_list = nil
    self.jm_attr_list = nil
    self.curr_select_whole_body = 1
    self.meridians_drag_select_index = -1
	self.meridians_btn_select_index = -1
    self.is_auto_meridians = false
    self.is_auto_condensate = false
    self.meridians_list = nil

    self.jing_mai_list_loader = nil
    self.jing_mai_old_select_index = nil
    self:CleanJingMaiTimer()
end

function RoleView:JingMaiShowIndexCallBack()
    ---这里选出最优解
    self.node_list.title_view_name.text.text = Language.JingJie.Text_view_name2
    local curr_meridians_data = JingJieWGData.Instance:GetMeridiansInfo()
    self.curr_select_whole_body = curr_meridians_data and curr_meridians_data.whole_body_id or 1
    self.meridians_drag_select_index = curr_meridians_data and curr_meridians_data.meridians_id or 1
    self.meridians_btn_select_index = curr_meridians_data and curr_meridians_data.meridians_id or 1
    self:OnMeridiansSelectedBtnChange(nil, true, self.meridians_drag_select_index)
end

function RoleView:InitCreateMeridiansCamberedList()
	local cambered_list_data = {
		item_render = MeridiansCamberedRander,
		asset_bundle = "uis/view/role_ui/jingjie_ui_prefab",
		asset_name = "meridians_item",

		scroll_list = self.node_list.meridians_list,
		center_x = 390, center_y = 0,
		radius_x = 440, radius_y = 440,
		angle_delta = Mathf.PI / 12,
		origin_rotation = Mathf.PI * 0.42,
		is_drag_horizontal = false,
        is_clockwise_list = false,
		scale_min = 0.6,
		speed = 1,
		arg_speed = 0.2,
		limit_drag_over_angle = (Mathf.PI * 2) - ((Mathf.PI / 12) * 7),
        limit_drag_over_angle_min = (Mathf.PI * 2) - ((Mathf.PI / 12) * 7),

		click_item_cb = BindTool.Bind(self.OnClickMeridiansBtn, self),
		drag_to_next_cb = BindTool.Bind(self.OnDragMeridiansToNextCallBack, self),
		drag_to_last_cb = BindTool.Bind(self.OnDragMeridiansToLastCallBack, self),
		on_drag_end_cb = BindTool.Bind(self.OnDragMeridiansLEndCallBack, self),
	}

    self.meridians_list = JingJieWGData.Instance:GetCurrMeridiansList()
	self.meridians_cambered_list = CamberedList.New(cambered_list_data)
	self.meridians_cambered_list:CreateCellList(#self.meridians_list)
	local btn_item_list = self.meridians_cambered_list:GetRenderList()

	for k, item_cell in ipairs(btn_item_list) do
		local item_data = self.meridians_list[k]
		item_cell:SetData(item_data)
	end
end

function RoleView:OnClickMeridiansBtn(item_cell, force_jump)
	if item_cell == nil then
		return
	end

	local select_index = item_cell:GetIndex()
	local select_data = item_cell:GetData()
	if select_data == nil then
		return
	end

	if (not force_jump and self.meridians_btn_select_index == select_index) then
		return
	end

	self.meridians_btn_select_index = select_index
    self.meridians_drag_select_index = select_index

    self:OnMeridiansSelectedBtnChange(function ()
        self:JingMaiOnFlush()   ---直接刷新
    end, true)
end

function RoleView:OnMeridiansSelectedBtnChange(callback, is_click, drag_index)
	if self.meridians_cambered_list == nil then
		return
	end

	local to_index = drag_index ~= nil and drag_index or self.meridians_btn_select_index or 1
	self.meridians_cambered_list:ScrollToIndex(to_index, callback, is_click)

    if is_click then
		local item_list = self.meridians_cambered_list:GetRenderList()
		for k, item_cell in ipairs(item_list) do
			item_cell:SetSelectedHL(to_index)
		end
	end
end

-- 拖拽到下一个
function RoleView:OnDragMeridiansToNextCallBack()
	local max_index = self.meridians_list and #self.meridians_list or 8
	self.meridians_drag_select_index = self.meridians_drag_select_index + 1
	self.meridians_drag_select_index = self.meridians_drag_select_index > max_index and max_index or self.meridians_drag_select_index
end

function RoleView:OnDragMeridiansToLastCallBack()
	self.meridians_drag_select_index = self.meridians_drag_select_index - 1
	self.meridians_drag_select_index = self.meridians_drag_select_index < 1 and 1 or self.meridians_drag_select_index
end

function RoleView:OnDragMeridiansLEndCallBack()
    if self.meridians_drag_select_index == self.meridians_btn_select_index then
        return
    end
	self:OnMeridiansSelectedBtnChange(nil, false, self.meridians_drag_select_index)
end

function RoleView:JingMaiOnFlush(param_t)
    if param_t and param_t.all and param_t.all.opera_type then
        if param_t.all.opera_type == JINGMAI_OPERA_TYPE.JINGMAI_OPERA_TYPE_UPGRADE then
            self:PlayUpgradeEffect(param_t.all.upgrade_succeed)
        elseif param_t.all.opera_type == JINGMAI_OPERA_TYPE.JINGMAI_OPERA_TYPE_BREAK_UP then
            self:PlayCondensateEffect(param_t.all.upgrade_succeed)
			if param_t.all.upgrade_succeed == 1 then
				---技能刷新
				local curr_meridians_data = JingJieWGData.Instance:GetMeridiansInfo()
				if curr_meridians_data then
					local index = curr_meridians_data.meridians_id
					self.meridians_drag_select_index = index
					self.meridians_btn_select_index = index
					local btn_item_list = self.meridians_cambered_list:GetRenderList()

					local item_cell = btn_item_list[index]
					if item_cell then
						self:OnClickMeridiansBtn(item_cell, true)
					end
				end
				self.node_list.jm_tab_toggle_1.toggle.isOn = true
			end
		end
	end

	self:JingMaiFlushPartView()
	self:JingMaiFlushAttrView()
	self:JingMaiFlushSpendView()
	self:FulshMeridiansLv()
end

-- 刷新穴位面板
function RoleView:JingMaiFlushPartView()
    local curr_meridians_data = JingJieWGData.Instance:GetMeridiansInfo()

    if not curr_meridians_data then
        return
    end

    self.curr_select_whole_body = curr_meridians_data.whole_body_id or 1

    self.node_list.whole_body_label.text.text = Language.JingMai.WholeGrop[self.curr_select_whole_body]     ---这里永远显示当前的那个

    self.node_list.acupoint_name.text.text = Language.JingMai.MeridiansName[self.meridians_btn_select_index] or ""
    self.node_list.jing_mai_select_eff_pos:CustomSetActive(false)
    self:JingMaiFlushPartViewRoot(function ()
        if self.jing_mai_item_list and #self.jing_mai_item_list > 0 then
            local acupoint_list = JingJieWGData.Instance:GetAcupointList(self.curr_select_whole_body, self.meridians_btn_select_index)
            if acupoint_list and #acupoint_list > 0 then
                for index, jing_mai_cell in ipairs(self.jing_mai_item_list) do
                    if jing_mai_cell and acupoint_list[index] then
                        jing_mai_cell:SetData(acupoint_list[index])

                        if acupoint_list[index].is_select and jing_mai_cell.view then
                            self.node_list.jing_mai_select_eff_pos:CustomSetActive(true)
                            self.node_list.jing_mai_select_eff_pos.transform.position = jing_mai_cell.view.transform.position
                        end
                    end
                end
            end
        end

        local is_curr_meridians = self.curr_select_whole_body == curr_meridians_data.whole_body_id and self.meridians_btn_select_index == curr_meridians_data.meridians_id
        local meridians_attr_cfg = JingJieWGData.Instance:GetMeridiansAttrById(self.curr_select_whole_body, self.meridians_btn_select_index)

        if meridians_attr_cfg then
            local bundel, asset = ResPath.GetSkillIconById(meridians_attr_cfg.icon)
            self.node_list.skill_icon.image:LoadSprite(bundel, asset)
            self.node_list.skill_dec.text.text = meridians_attr_cfg.name

            ---设置领取状态
            local can_get = is_curr_meridians and curr_meridians_data.skill_state == 1
            local is_lock_meridians = self.meridians_btn_select_index > curr_meridians_data.meridians_id
            local is_unlock_meridians = self.meridians_btn_select_index < curr_meridians_data.meridians_id or curr_meridians_data.is_max
            self.node_list.skill_can_get:SetActive(can_get)
            self.node_list.skill_not_can_get:SetActive(is_lock_meridians or not is_unlock_meridians)
            self.node_list.btn_skill_red:SetActive(can_get)
        end

        self.node_list.meridians_up:SetActive(self.meridians_btn_select_index ~= 1)
        local max_index = self.meridians_list and #self.meridians_list or 8
        self.node_list.meridians_down:SetActive(self.meridians_btn_select_index ~= max_index)
    end)
end

function RoleView:JingMaiFlushPartViewRoot(callback)
    if self.jing_mai_old_select_index and self.jing_mai_old_select_index == self.meridians_btn_select_index then
        ---同一个目标不用刷新
        if callback then
            callback()
        end
        return
    end

    self.jing_mai_old_select_index = self.meridians_btn_select_index
    local asset_bundle = "uis/view/role_ui/jingjie_ui_prefab"
    local asset_name = "jing_mai_list_%d"
    asset_name = string.format(asset_name, self.meridians_btn_select_index)

    if not self.jing_mai_list_loader then
        local jing_mai_list_loader = AllocAsyncLoader(self, "jing_mai_list_loader")
        if jing_mai_list_loader then
            jing_mai_list_loader:SetIsUseObjPool(true)
            jing_mai_list_loader:SetParent(self.node_list["jing_mai_list_pos"].transform)
            self.jing_mai_list_loader = jing_mai_list_loader
        end
    end

    if self.jing_mai_list_loader then
        self.jing_mai_list_loader:Load(asset_bundle, asset_name, function ()
            self:JingMaiFlushPartViewRender()
            if callback then
                callback()
            end
        end)
    end
end

function RoleView:JingMaiFlushPartViewRender()
    if not self.jing_mai_list_loader then
        ---同一个目标不用刷新
        return
    end

    if (not self.jing_mai_item_list) or (#self.jing_mai_item_list <= 0) then
        return
    end

    local list_obj = self.jing_mai_list_loader:GetGameObj()
    local list_u3d_obj = U3DObject(list_obj, list_obj.transform, self)

    for i, jing_mai_cell in ipairs(self.jing_mai_item_list) do
        if jing_mai_cell and list_u3d_obj then
            local path = string.format("root/part%d", i)
            local root = list_u3d_obj:FindObj(path).gameObject
            jing_mai_cell:SetInstance(root)
        end
    end
end

-- 刷新属性面板
function RoleView:JingMaiFlushAttrView()
	local meridians_attr_list = nil
	if self.jing_mai_cur_tab_index == MeridiansType.Meridians then
		meridians_attr_list = JingJieWGData.Instance:GetMeridiansAttr(self.curr_select_whole_body, self.meridians_btn_select_index)
	elseif self.jing_mai_cur_tab_index == MeridiansType.UpMeridians then
		meridians_attr_list = JingJieWGData.Instance:GetMeridiansAttr(self.curr_select_whole_body, self.meridians_btn_select_index, true)
	end

	for index, attr_cell in ipairs(self.jm_attr_item_list) do
		if meridians_attr_list and attr_cell then
			if meridians_attr_list[index] and meridians_attr_list[index].attr_str == RoleView.LongMaiJiaChengAttr then
				attr_cell:SetNeedPer(true)
				attr_cell:SetData(meridians_attr_list[index])
				attr_cell:ResetName(Language.JingMai.LongMaiAttrPerName)
			else
				attr_cell:SetData(meridians_attr_list[index])
			end
		end
	end

    self.node_list.jingmai_cap_value.text.text = JingJieWGData.Instance:GetJingMaiAttrCap(meridians_attr_list)
end

-- 刷新花费面板
function RoleView:JingMaiFlushSpendView()
    self.is_auto_condensate = self.node_list.auto_toggle.toggle.isOn
    local curr_meridians_data = JingJieWGData.Instance:GetMeridiansInfo()

    if not curr_meridians_data then
        return
    end

    local acupoint_id = 1
    local is_full = false
    local is_curr_meridians = self.curr_select_whole_body == curr_meridians_data.whole_body_id and self.meridians_btn_select_index == curr_meridians_data.meridians_id
    local is_lessthan_meridians = self.meridians_btn_select_index < curr_meridians_data.meridians_id

    if is_curr_meridians then
        acupoint_id = curr_meridians_data.acupoint_id == 0 and 1 or curr_meridians_data.acupoint_id + 1
        local length = JingJieWGData.Instance:GetAcupointCfgCountById(curr_meridians_data.whole_body_id, curr_meridians_data.meridians_id)
        if acupoint_id > length and curr_meridians_data.break_state == 1 then
            acupoint_id = length
            is_full = true
        end
    elseif is_lessthan_meridians then
        is_full = true
    end

    local cfg = JingJieWGData.Instance:GetAcupointCfgById(self.curr_select_whole_body, self.meridians_btn_select_index , acupoint_id)
	if cfg then
        -- 检测凝气状态
        local coin = RoleWGData.Instance.role_info.coin or 0
        local color = coin >= cfg.condensate_coin_num and COLOR3B.D_GREEN or COLOR3B.D_RED
        self.node_list["qh_coin_desc"].text.text = string.format(Language.Common.ColorStr, color, cfg.condensate_coin_num)

        -- 境界状态
        local jingjie_cfg = JingJieWGData.Instance:GetJingJieCfgBylevel(cfg.jingjie_level)
        local curr_jingjie_level = JingJieWGData.Instance:GetJingJieLevel()
        local jingjie_color = curr_jingjie_level >= cfg.jingjie_level and COLOR3B.D_GREEN or COLOR3B.D_RED
        local is_jingjie_enougth = curr_jingjie_level >= cfg.jingjie_level
        if jingjie_cfg and jingjie_cfg.name then
            self.node_list["jingjie_level"].text.text = string.format(Language.Common.ColorStr, jingjie_color, jingjie_cfg.name)
        end

        local role_info = RoleWGData.Instance:GetRoleVo()
        local cur_yuanbao_num = role_info.bind_gold
        local custom_num = cfg.yuanbao_num
        local is_spend_enougth = cur_yuanbao_num >= custom_num
        local spend_color = is_spend_enougth and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
		self.node_list["meridians_pro_text"].text.text = string.format(Language.Common.ColorStr .. "/%s", spend_color,
        cur_yuanbao_num, custom_num)

		self.node_list.meridians_progress.slider.value = cur_yuanbao_num / custom_num

		local other_cfg = JingJieWGData.Instance:GetOtherCfg()
		if other_cfg then
			self.meridians_item:SetData({ item_id = COMMON_CONSTS.VIRTUAL_ITEM_BIND_GOLD })
		end

		local is_max = curr_meridians_data.is_max
		self.node_list.meridians_can_panel:SetActive(not is_max and not is_full)
		self.node_list.meridians_not_panel:SetActive(not is_max and is_full)
		self.node_list.meridians_max_flag:SetActive(is_max)

		--突破.
		local break_up_cfg = JingJieWGData.Instance:GetBreakUpCfgById(self.curr_select_whole_body, self.meridians_btn_select_index)

		if not IsEmptyTable(break_up_cfg) then
			-- 成功率状态
			local vip_level = VipWGData.Instance:GetVipLevel()
			local vip_suc_per = JingJieWGData.Instance:GetSucPerByVipForCfg(vip_level)
			self.node_list["up_success_rate"].text.text = string.format(Language.JingMai.MeridianColorPercent, break_up_cfg.suc_per, vip_suc_per)

			local item_name = ItemWGData.Instance:GetItemName(break_up_cfg.consume_item)
			self.node_list["up_meridians_desc"].text.text = string.format(Language.JingMai.MeridianFailureCost, item_name, break_up_cfg.fail_consume_num)

			local item_num = ItemWGData.Instance:GetItemNumInBagById(break_up_cfg.consume_item)
			local color = item_num >= break_up_cfg.suc_consume_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
			self.up_meridians_item:SetData({ item_id = break_up_cfg.consume_item })
			self.up_meridians_item:SetRightBottomText(ToColorStr(item_num .. '/' .. break_up_cfg.suc_consume_num, color))
			self.up_meridians_item:SetRightBottomTextVisible(true)
		end

		self.node_list.up_meridians_can_panel:SetActive(is_full)
		self.node_list.up_meridians_not_panel:SetActive(not is_full)

		self.node_list["meridians_btn_remind"]:SetActive(is_jingjie_enougth and is_spend_enougth and (not is_full) and is_curr_meridians)
		self.node_list["mer_red"]:SetActive(is_jingjie_enougth and is_spend_enougth and (not is_full) and is_curr_meridians)

        local is_up_full = JingJieWGData.Instance:GetMeridiansUpRemind()
		self.node_list["up_meridians_btn_remind"]:SetActive(is_jingjie_enougth and is_spend_enougth and is_full and is_up_full and is_curr_meridians)
		self.node_list["mer_up_red"]:SetActive(is_jingjie_enougth and is_spend_enougth and is_full and is_up_full and is_curr_meridians)

        self:JingMaiSetMeridiansAutoText()

        if self.is_auto_meridians then ---开启了自动通脉进入倒计时自动通脉
            self:OperateAutoTimer()
        end
	else
		self.node_list.meridians_can_panel:SetActive(false)
		self.node_list.meridians_not_panel:SetActive(false)
		self.node_list.up_meridians_can_panel:SetActive(false)
		self.node_list.up_meridians_not_panel:SetActive(false)
		self.node_list.meridians_btn_remind:SetActive(false)
		self.node_list.mer_red:SetActive(false)
		self.node_list.up_meridians_btn_remind:SetActive(false)
		self.node_list.mer_up_red:SetActive(false)
		self.node_list.meridians_max_flag:SetActive(true)
    end
end

function RoleView:CleanJingMaiTimer()
    if self.jingmai_timer then
        GlobalTimerQuest:CancelQuest(self.jingmai_timer)
        self.jingmai_timer = nil
    end
end

-- 自动凝气通脉时间倒计时
function RoleView:OperateAutoTimer(is_now)
    self:CleanJingMaiTimer()
    local delay_time = is_now and 0.1 or 0.4       -- 延迟时间
    local do_count = 1         -- 执行次数
    local callback = function()
        if not self.is_auto_meridians then
            self:CleanJingMaiTimer()
            return
        end

        if not self.is_auto_condensate then
            -- 直接通脉
            self:JingMaiOperate(JINGMAI_OPERA_TYPE.JINGMAI_OPERA_TYPE_UPGRADE)
        else
            -- 自动凝气（凝气满了直接调用通脉）
            self:JingMaiOperate(JINGMAI_OPERA_TYPE.JINGMAI_OPERA_TYPE_BREAK_UP, true)
        end
    end
    -- 相当于一秒后执行一次
    self.jingmai_timer = GlobalTimerQuest:AddTimesTimer(callback, delay_time, do_count)
end

-- 自动凝气
function RoleView:JingMaiCondensateAuto(is_on)
    if self.is_auto_condensate ~= is_on then
		self.is_auto_condensate = is_on
	end
end

function RoleView:JingMaiOperate(operate_type, is_auto)
    local curr_meridians_data = JingJieWGData.Instance:GetMeridiansInfo()

    if not curr_meridians_data then
        return
    end

    local is_curr_meridians = self.curr_select_whole_body == curr_meridians_data.whole_body_id and self.meridians_btn_select_index == curr_meridians_data.meridians_id

    if not is_curr_meridians then
        if operate_type == JINGMAI_OPERA_TYPE.JINGMAI_OPERA_TYPE_BREAK_UP then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.JingMai.CondensateError2)
            return
        elseif operate_type == JINGMAI_OPERA_TYPE.JINGMAI_OPERA_TYPE_UPGRADE then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.JingMai.ConditionError)
            self.is_auto_meridians = false
            self:JingMaiSetMeridiansAutoText()
            return
        end
    end

    local acupoint_id = 1

    if is_curr_meridians then
        acupoint_id = curr_meridians_data.acupoint_id == 0 and 1 or curr_meridians_data.acupoint_id
    end

	local cfg = JingJieWGData.Instance:GetAcupointCfgById(self.curr_select_whole_body, self.meridians_btn_select_index, acupoint_id)
	if cfg then
		local curr_jingjie_level = JingJieWGData.Instance:GetJingJieLevel()
		local is_aim_jingjie_level = cfg and curr_jingjie_level >= cfg.jingjie_level
		local is_not_aim_jingjie_level = not is_aim_jingjie_level
		
		local break_up_cfg = JingJieWGData.Instance:GetBreakUpCfgById(self.curr_select_whole_body, self.meridians_btn_select_index)
		if operate_type == JINGMAI_OPERA_TYPE.JINGMAI_OPERA_TYPE_BREAK_UP and not IsEmptyTable(break_up_cfg) then
			-- 检测突破状态
			local cost_item_num =  ItemWGData.Instance:GetItemNumInBagById(break_up_cfg.consume_item)
			local is_cost_item_enough = cost_item_num >= break_up_cfg.suc_consume_num

			if not is_cost_item_enough then
				self.node_list.auto_toggle.toggle.isOn = false
				self.is_auto_condensate = false
				local item_name = ItemWGData.Instance:GetItemName(break_up_cfg.consume_item)
				SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.JingMai.CondensateError, item_name))
		        local suc_consume_num = break_up_cfg.suc_consume_num
                TipWGData.Instance:SetDefShowBuyCount(suc_consume_num)
				TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = break_up_cfg.consume_item })

				if self.is_auto_meridians then
					self.is_auto_meridians = false
					self:JingMaiSetMeridiansAutoText()
				end
				return
			end

			-- 检测境界
			if is_not_aim_jingjie_level then
				self.node_list.auto_toggle.toggle.isOn = false
				self.is_auto_condensate = false
				local jingjie_cfg = JingJieWGData.Instance:GetJingJieCfgBylevel(cfg.jingjie_level)

				if jingjie_cfg and jingjie_cfg.name then
					SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.JingMai.CondensateError4, jingjie_cfg.name))
				end

				if self.is_auto_meridians then
					self.is_auto_meridians = false
					self:JingMaiSetMeridiansAutoText()
				end
				return
			end

			RoleWGCtrl.Instance:ReqJingMaiBreakUp(1)
		elseif operate_type == JINGMAI_OPERA_TYPE.JINGMAI_OPERA_TYPE_UPGRADE then
            -- 检测境界
            if is_not_aim_jingjie_level then
                local jingjie_cfg = JingJieWGData.Instance:GetJingJieCfgBylevel(cfg.jingjie_level)

                if jingjie_cfg and jingjie_cfg.name then
                    SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.JingMai.jingjieError, jingjie_cfg.name))
                end

                self.is_auto_meridians = false
                self:JingMaiSetMeridiansAutoText()
                return
            end

            -- 检测材料是否充足
			local cost_item_num = curr_meridians_data.longhun_exp
			local is_cost_item_enough = cost_item_num >= cfg.longhun_exp
            if not is_cost_item_enough then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.JingMai.MeridiansError)

                self.is_auto_meridians = false
                self:JingMaiSetMeridiansAutoText()
                return
            end

            -- 通脉
            RoleWGCtrl.Instance:ReqJingMaiUpgrade(1)
		else    --没有下一条穴位信息了将自动关掉
			self.node_list.auto_toggle.toggle.isOn = false
			self.is_auto_meridians = false
			self.is_auto_condensate = false
			self:JingMaiSetMeridiansAutoText()
		end
	end
end

function RoleView:JingMaiSetMeridiansAutoText()
    local str = self.is_auto_meridians and Language.JingMai.MeridiansAuto2 or Language.JingMai.MeridiansAuto1
    self.node_list["meridians_auto_text"].text.text = str
end

-- 自动通脉
function RoleView:JingMaiOperateAuto()
    self.is_auto_meridians = not self.is_auto_meridians
    if self.is_auto_meridians then
        self:OperateAutoTimer(true)
    end
    self:JingMaiSetMeridiansAutoText()
end

-- 通脉
function RoleView:JingMaiMeridians()
    local curr_meridians_data = JingJieWGData.Instance:GetMeridiansInfo()
    if not curr_meridians_data then
        return
    end

    local acupoint_id = 1
    local is_curr_meridians = self.curr_select_whole_body == curr_meridians_data.whole_body_id and self.meridians_btn_select_index == curr_meridians_data.meridians_id

    if is_curr_meridians then
        acupoint_id = curr_meridians_data.acupoint_id + 1
        local length = JingJieWGData.Instance:GetAcupointCfgCountById(curr_meridians_data.whole_body_id, curr_meridians_data.meridians_id)
        if acupoint_id > length and curr_meridians_data.break_state == 1 then
            acupoint_id = length
        end
    end

    local cfg = JingJieWGData.Instance:GetAcupointCfgById(self.curr_select_whole_body, self.meridians_btn_select_index , acupoint_id)
    local cur_yuanbao_num = RoleWGData.Instance:GetRoleVo().bind_gold
    local custom_num = cfg.yuanbao_num
    local is_spend_enougth = cur_yuanbao_num >= custom_num

    if not self.is_auto_meridians then
        if is_spend_enougth then
            self:JingMaiOperate(JINGMAI_OPERA_TYPE.JINGMAI_OPERA_TYPE_UPGRADE)
        else
            TipWGCtrl.Instance:OpenItemTipGetWay({item_id = COMMON_CONSTS.VIRTUAL_ITEM_BIND_GOLD})
        end
    end
end

-- 总属性
function RoleView:JingMaiOpenAllAttrPanel()
	local data_list = JingJieWGData.Instance:GetAllMeridiansAttr()
	local tips_data = {
		title_text = Language.JingMai.MeridiansTitleAttr,
		attr_data = data_list,
		add_attr_data =
		{
			{
				attr_str_id = RoleView.LongMaiJiaChengAttr,
				attr_str = Language.JingMai.LongMaiAttrPerName,
				need_per = true
			}
		}
	}

    if IsEmptyTable(tips_data.attr_data) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.JingMai.AttrTip)
        return
    end
	TipWGCtrl.Instance:OpenTipsAttrView(tips_data)
end

-- 领取技能
function RoleView:JingMaiGetSkill()
    local curr_meridians_data = JingJieWGData.Instance:GetMeridiansInfo()

    if not curr_meridians_data then
        return
    end

    local is_curr_meridians = self.curr_select_whole_body == curr_meridians_data.whole_body_id and self.meridians_btn_select_index == curr_meridians_data.meridians_id
    local meridians_attr_cfg = JingJieWGData.Instance:GetMeridiansAttrById(self.curr_select_whole_body, self.meridians_btn_select_index)
    local is_lock_meridians = self.meridians_btn_select_index > curr_meridians_data.meridians_id
    local is_unlock_meridians = self.meridians_btn_select_index < curr_meridians_data.meridians_id or curr_meridians_data.is_max

    if meridians_attr_cfg then
        ---设置领取状态
        local is_can_get_skill = is_curr_meridians and curr_meridians_data.skill_state == 1
        if is_can_get_skill then
            RoleWGCtrl.Instance:ReqJingMaiGetSkill(1)
        else
            local _,capability = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(meridians_attr_cfg, "attr_id", "attr_value")
            local show_data = {
                icon = meridians_attr_cfg.icon,
                top_text = meridians_attr_cfg.name,
                body_text = meridians_attr_cfg.skill_describe,
                limit_text = Language.JingMai.MeridiansSkillTips,
                show_bg_kunag = true,
                is_lock = is_lock_meridians or (not is_unlock_meridians),
                hide_level = true,
                x = -49,
                y = -90,
                set_pos = true,
                capability = capability,
            }
            NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
        end
    end
end

-- 切换经脉(-1 表示减， 1表示加)
function RoleView:ChangeMeridians(offset)
    if offset > 0 then
        self:OnDragMeridiansToNextCallBack()
    else
        self:OnDragMeridiansToLastCallBack()
    end

    if self.meridians_cambered_list then
        local btn_item_list = self.meridians_cambered_list:GetRenderList()

        local item_cell = btn_item_list[self.meridians_drag_select_index]
        if item_cell then
            self:OnClickMeridiansBtn(item_cell, true)
        end
    end
end

-- 通脉成功或失败特效
function RoleView:PlayUpgradeEffect(is_success)
	local effect_type = is_success == 1 and UIEffectName.s_tongmai or UIEffectName.f_tongmai
	TipWGCtrl.Instance:ShowEffect({effect_type = effect_type,
						is_success = is_success == 1, pos = Vector2(0, 0), parent_node = self.node_list["effect_pos"]})

	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
end

-- 突破特效.
function RoleView:PlayCondensateEffect(is_success)
    local effect_type = is_success == 1 and UIEffectName.s_tupo or UIEffectName.f_tupo
    TipWGCtrl.Instance:ShowEffect({
        effect_type = effect_type,
        is_success = is_success == 1,
        pos = Vector2(0, 0),
        parent_node = self.node_list["effect_pos"]
    })
    AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
end

function RoleView:OnClickAutoTips()
	local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.JingMai.AutoTipsTitle)
	rule_tip:SetContent(Language.JingMai.AutoTipsContent, nil, nil, nil, true)
end

function RoleView:FulshMeridiansLv()
    local curr_meridians_data = JingJieWGData.Instance:GetMeridiansInfo()
    if not curr_meridians_data then
        return
    end

    local temp_acupoint_id = curr_meridians_data.acupoint_id + 1
    if temp_acupoint_id > #Language.JingMai.AcupointName then
        temp_acupoint_id = #Language.JingMai.AcupointName
    end

    local cur_str = ""
    local next_str = ""

    if self.jing_mai_cur_tab_index == MeridiansType.Meridians then
        cur_str = string.format(Language.JingMai.MerLvText,
            Language.JingMai.MeridiansName[curr_meridians_data.meridians_id],
            Language.JingMai.AcupointName[temp_acupoint_id]
        )

        local acupoint_is_max = JingJieWGData.Instance:CheckCurAcupointIsMax()

        if not acupoint_is_max then
            next_str = string.format(Language.JingMai.MerLvText,
                Language.JingMai.MeridiansName[curr_meridians_data.meridians_id],
                Language.JingMai.AcupointName[temp_acupoint_id + 1]
            )
        end

        self.node_list.next_mer_lv:SetActive(not acupoint_is_max)
    elseif self.jing_mai_cur_tab_index == MeridiansType.UpMeridians then
		local whole_count = JingJieWGData.Instance:GetWholeCfgCount()
		local is_whole_max = curr_meridians_data.whole_body_id == whole_count
		local meridians_count = JingJieWGData.Instance:GetMeridiansCfgCountById(curr_meridians_data.whole_body_id)
		local is_meridians_max = curr_meridians_data.meridians_id == meridians_count
		local is_acupoint_max = JingJieWGData.Instance:CheckCurAcupointIsMax()
		local is_show = not curr_meridians_data.is_max and not (is_whole_max and is_meridians_max and is_acupoint_max)

        self.node_list.next_mer_lv:SetActive(is_show)

        cur_str = Language.JingMai.MeridiansName[curr_meridians_data.meridians_id]

        if is_show then
            next_str = Language.JingMai.MeridiansName[curr_meridians_data.meridians_id + 1] or Language.JingMai.MeridiansName[1]
        end
    end

    self.node_list.cur_mer_lv_text.text.text = cur_str
    self.node_list.next_mer_lv_text.text.text = next_str

    local show_total_btn = not (curr_meridians_data.meridians_id == 1 and temp_acupoint_id == 1)
    self.node_list.btn_all_attr:SetActive(show_total_btn)
end

function RoleView:OnClickTabMeridians(index, is_on)
	if is_on then
		self.jing_mai_cur_tab_index = index
		self:FulshMeridiansLv()
		self:JingMaiFlushAttrView()
	end
end

function RoleView:OnClickGoMeridians(index)
	if index == 1 then
		self.node_list.jm_tab_toggle_2.toggle.isOn = true
	elseif index == 2 then
		self.node_list.jm_tab_toggle_1.toggle.isOn = true
	end
end

---------------------经脉item------------------
MeridiansCamberedRander = MeridiansCamberedRander or BaseClass(BaseRender)
function MeridiansCamberedRander:__init()
	self.click_callback = nil
end

function MeridiansCamberedRander:__delete()
	self.click_callback = nil
end

function MeridiansCamberedRander:LoadCallBack()
	XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClick, self))
end

-- 设置点击回调
function MeridiansCamberedRander:SetClickCallback(click_callback)
	self.click_callback = click_callback
end

function MeridiansCamberedRander:OnClick()
	if self.click_callback then
		self.click_callback()
	end
end

function MeridiansCamberedRander:OnFlush()
	if not self.data then
		return
	end
    self.node_list.normal_label.text.text = self.data.normal_name
    self.node_list.select_label.text.text = self.data.select_name
end

function MeridiansCamberedRander:SetSelectedHL(index)
	local is_select = self.index == index

	self.node_list.normal:SetActive(not is_select)
	self.node_list.select:SetActive(is_select)
end

---------------------经脉穴位属性item------------------
JingMaiPartItem = JingMaiPartItem or BaseClass(BaseRender)
function JingMaiPartItem:__init()
end

function JingMaiPartItem:__delete()
	self.data = nil
end

function JingMaiPartItem:OnFlush()
	if not self.data then
		return
	end
	self:SetLockState(self.data.is_lock)
	self:SetCurrSelect(self.data.is_select)

	local curr_meridians_data = JingJieWGData.Instance:GetMeridiansInfo()

	if not curr_meridians_data then
		return
	end

	local whole_body_id = curr_meridians_data.whole_body_id or 1
	local num = whole_body_id % 3	--只有三张图循环使用.
	local res = "a3_lm_zhu" .. num
	local bundle, asset = ResPath.GetRoleUIImage(res)
	self.node_list.img.image:LoadSprite(bundle, asset, function()
		self.node_list.img.image:SetNativeSize()
	end)
end

function JingMaiPartItem:SetCurrSelect(is_curr_select)
	self.node_list["select"]:SetActive(is_curr_select)
end

function JingMaiPartItem:SetLockState(is_lock)
	-- self.node_list["normal"]:SetActive(is_lock)
	self.node_list["hl"]:SetActive(not is_lock)
end