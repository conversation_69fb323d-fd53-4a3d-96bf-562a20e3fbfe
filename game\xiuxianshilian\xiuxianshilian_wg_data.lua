XiuXianShiLianWGData = XiuXianShiLianWGData or BaseClass()
XiuXianShiLianWGData.XIUXIAN_OPER_TYPE = {
    XIUXIAN_OPER_TYPE_FETCH_REWARD = 0, --领取任务奖励 param1: task_type
    XIUXIAN_OPER_TYPE_ACTIVE_ATTR = 1, --领取章节技能
    XIUXIAN_OPER_TYPE_SEND_INFO = 2, --发送信息
    XIUXIAN_OPER_TYPE_CHAPTER_REWARD = 3, --领取章节奖励 param1: chapter_id
}

local XIUXIANZHILV_SKILL_TYPE_1 = 1

XiuXianShiLianWGData.CONDITION_TYPE = {
    CAP = 10,                               -- 战力达标条件
}

function XiuXianShiLianWGData:__init()
    if XiuXianShiLianWGData.Instance then
		ErrorLog("[XiuXianShiLianWGData] attempt to create singleton twice!")
		return
	end
    XiuXianShiLianWGData.Instance = self
    local origin_cfg = ConfigManager.Instance:GetAutoConfig("xiuxian_cfg_auto")
    self.xiuxian_chapter_cfg = origin_cfg.chapter_cfg
    self.model_show_cfg = ListToMap(origin_cfg.model_show, "chapter_id")
    local cfg = origin_cfg.task_cfg
    self.ori_xiuxian_task_cfg = cfg
    self.xiuxian_task_cfg = ListToMapList(cfg, "chapter_id")
    self.world_chat_cfg = origin_cfg.world_chat_cfg
    self.chapter_fetch_flag = {}
    self.chapter_reward_flag = {}
    self.select_xiuxian_item_index = 0
    self.chapter_model_show_info = {}

    RemindManager.Instance:Register(RemindName.XiuXianShiLian, BindTool.Bind(self.GetXiuxianRemind, self))
end

function XiuXianShiLianWGData:__delete()
    XiuXianShiLianWGData.Instance = nil
    RemindManager.Instance:UnRegister(RemindName.XiuXianShiLian)
end

function XiuXianShiLianWGData:GetTaskListInfoById(index) --优先显示可领取的，其次显示未完成的，最后显示已完成的；同等优先级按照id大小排列即可
    local list = {}
    for k, v in pairs(self.xiuxian_task_cfg[index]) do
        local info = {}
        info.task_type = v.task_type
        local sub_task = self:GetTaskProgressByTaskType(info.task_type)
        if not IsEmptyTable(sub_task) then
            info.progress = sub_task.progress
            info.has_fetched_reward = sub_task.has_fetched_reward --领取奖励标志
            info.is_complete = sub_task.is_complete --是否完成 0 未完成 1完成
        else
            info.progress = 0
            info.has_fetched_reward = 0
            info.is_complete = 0
        end
        if v.condition_type == XiuXianShiLianWGData.CONDITION_TYPE.CAP then --战斗力特殊处理
            local role_vo = RoleWGData.Instance.role_vo
            info.progress = role_vo.capability
        end
        if self:GetTaskTypeIsOpen(info.task_type) and info.has_fetched_reward == 0 and info.is_complete == 1 then  --可领取
            info.weight = 1300 * (#self.xiuxian_task_cfg[index] + 1 - k)
        elseif info.has_fetched_reward == 0 and info.is_complete == 0 then --未完成
            info.weight = 100 * (#self.xiuxian_task_cfg[index] + 1 - k)
        elseif info.has_fetched_reward == 1 and info.is_complete == 1 then --已领取
            info.weight = #self.xiuxian_task_cfg[index] + 1 - k
        else
            info.weight = 10 * (#self.xiuxian_task_cfg[index] + 1 - k)
        end
        list[#list+1] = info
    end
    table.sort(list, SortTools.KeyUpperSorter("weight"))
    return list
end


function XiuXianShiLianWGData:GetShowViewTaskListInfoById(index)
    local list = {}
    for k, v in pairs(self.xiuxian_task_cfg[index]) do
        local info = {}
        info.task_type = v.task_type
        local sub_task = self:GetTaskProgressByTaskType(info.task_type)
        if not IsEmptyTable(sub_task) then
            info.progress = sub_task.progress
            info.has_fetched_reward = sub_task.has_fetched_reward --领取奖励标志
            info.is_complete = sub_task.is_complete --是否完成 0 未完成 1完成
        else
            info.progress = 0
            info.has_fetched_reward = 0
            info.is_complete = 0
        end

        if v.condition_type == XiuXianShiLianWGData.CONDITION_TYPE.CAP then --战斗力特殊处理
            local role_vo = RoleWGData.Instance.role_vo
            info.progress = role_vo.capability
        end

        list[#list+1] = info
    end

    table.sort(list, SortTools.KeyLowerSorter("task_type"))
    return list
end

function XiuXianShiLianWGData:GetTaskCfgByTaskType(task_type)
    for k, v in pairs(self.ori_xiuxian_task_cfg) do
        if v.task_type == task_type then
            return v
        end
    end
end

function XiuXianShiLianWGData:GetModelShowCfg(chapter_id)
    return self.model_show_cfg[chapter_id]
end

function XiuXianShiLianWGData:GetChapterListInfo()
    if not IsEmptyTable(self.chapter_list) then
        return self.chapter_list
    end

    self.chapter_list = {}
    self.outfit_list = {}
    for k, v in pairs(self.xiuxian_chapter_cfg) do
        self.chapter_list[k] = {}
        self.chapter_list[k].chapter_id = v.chapter_id
        self.chapter_list[k].open_level = v.open_level
        self.chapter_list[k].chapter_name = v.chapter_name
        self.chapter_list[k].param1 = v.param1
        self.chapter_list[k].param2 = v.param2
        self.chapter_list[k].param3 = v.param3
        self.chapter_list[k].param4 = v.param4
        self.chapter_list[k].capability_inc = v.capability_inc
        self.chapter_list[k].skill_id = v.skill_id
        self.chapter_list[k].attr_type1 = v.attr_type1
        self.chapter_list[k].attr_type2 = v.attr_type2
        self.chapter_list[k].skill_index = v.skill_index
        self.chapter_list[k].chapter_png = v.chapter_png
        self.chapter_list[k].item_list = v.item_list
        self.chapter_list[k].chapter_des = v.chapter_des
        local reward_item = v.item_list
        self.chapter_list[k].reward_item = reward_item[0].item_id
    end

    local role_lv = RoleWGData.Instance:GetRoleLevel()
    local is_complete, is_fetched, reward_is_fetched = 0, 0, 0
    for k, v in pairs(self.chapter_list) do
        is_complete = self:GetChapterCompleteByChapterId(k) 
        is_fetched = self:GetChapterFetchedByChapterId(k)
        reward_is_fetched = self:GetChapterRewardFetchedByChapterId(k)

        v.is_complete = is_complete
        v.is_fetched = is_fetched -- 章节技能是否已领取
        v.reward_is_fetched = reward_is_fetched -- 物品是否已领取
        
        v.is_can_fetch = false
        if is_complete == 1 and is_fetched == 0 then
            v.is_can_fetch = true
        end

        local list = self.xiuxian_task_cfg[k]
        local all_task_count = #list
        v.all_task_count = all_task_count

        local cur_task_index = 0
        local cur_task_reward_num = 0
        for k1, v1 in pairs(list) do
            local task_progress = self:GetTaskProgressByTaskType(v1.task_type)
            if not IsEmptyTable(task_progress) then
                if task_progress.is_complete == 1 then
                    cur_task_index = cur_task_index + 1
                end
                if task_progress.has_fetched_reward == 1 then
                    cur_task_reward_num = cur_task_reward_num + 1
                end
                if task_progress.is_complete == 1 and task_progress.has_fetched_reward == 0 then
                    v.is_can_fetch = true
                end
            end
        end
        v.cur_task_index = cur_task_index

        local can_get_reward = cur_task_reward_num == all_task_count
        v.can_get_reward = can_get_reward

        v.is_unlock = true
        if k > 1 then
            local last_is_complete = self:GetChapterCompleteByChapterId(k - 1)
            local last_is_fetched = self:GetChapterFetchedByChapterId(k - 1)
            local last_reward_is_fetched = self:GetChapterRewardFetchedByChapterId(k - 1)
            v.is_unlock = last_is_complete == 1 and last_is_fetched == 1 and last_reward_is_fetched == 1
        end

        v.item_data_1 = nil
        if v.item_list[0] then
            local item_data = {}
            item_data.item_id =  v.item_list[0].item_id
            item_data.is_received = reward_is_fetched == 1
            item_data.is_locked = not can_get_reward or reward_is_fetched == 0
            item_data.is_remind = reward_is_fetched == 0 and can_get_reward
            v.item_data_1 = item_data
        end

        v.item_data_2 = nil
        if v.item_list[1] then
            local item_data = {}
            item_data.item_id = v.item_list[1].item_id
            item_data.is_received = reward_is_fetched == 1
            item_data.is_locked = not can_get_reward or reward_is_fetched == 0
            item_data.is_remind = reward_is_fetched == 0 and can_get_reward
            v.item_data_2 = item_data
            table.insert(self.outfit_list, item_data)
        end
    end
    return self.chapter_list
end

function XiuXianShiLianWGData:GetOutfitInfoList()
    return self.outfit_list
end

function XiuXianShiLianWGData:GetChapterListLenght()
    if not self.chapter_list then
        return 0
    end
    return #self.chapter_list
end

function XiuXianShiLianWGData:GetXiuXianShiLianInfoByCaptherId(chapter_id)
    local info_list = self:GetChapterListInfo()
    return info_list[chapter_id]
end

-- 任务奖励是否可以领取
function XiuXianShiLianWGData:GetTaskRewardFetable(task_type)
    local task_cfg = self:GetTaskCfgByTaskType(task_type)
    local chapter_id = task_cfg.chapter_id
    if not self:GetChapterIsOpen(chapter_id) then
        return false
    end
    local task_info = self:GetTaskProgressByTaskType(task_type)
    if task_info then
        return task_info.has_fetched_reward == 0 and task_info.is_complete == 1  --可领取
    end
    return false
end

-- 获得一个技能可以激活的章节
function XiuXianShiLianWGData:GetSingleCanActiveSkillChapterId()
    local chapter_info_list = self:GetChapterListInfo()
    for i, v in ipairs(chapter_info_list) do
        if v.is_unlock and self:GetXiuXianSkillCanActive(v.chapter_id) then
            return v.chapter_id
        end
    end

    return 0
end

-- 判断章节对应的技能是否可以激活
function XiuXianShiLianWGData:GetXiuXianSkillCanActive(chapter_id)
    if not self:GetChapterIsOpen(chapter_id) then
        return false
    end

    local chapter_info = self:GetXiuXianShiLianInfoByCaptherId(chapter_id)
    if chapter_info then
        return chapter_info.can_get_reward and chapter_info.is_fetched == 0
    end

    return false
end

-- 章节总数
function XiuXianShiLianWGData:GetChapterMaxAmount()
    return #self.xiuxian_chapter_cfg
end

function XiuXianShiLianWGData:SetXiuxianData(protocol)
    self.task_progress_list = protocol.task_progress_info
    self.chapter_complete_flag = protocol.chapter_complete_flag
    self.chapter_fetch_flag = protocol.chapter_fetch_flag
    self.chapter_reward_flag = protocol.chapter_reward_flag
    -- 清空缓存
    self.chapter_list = nil
    self:GetChapterListInfo()
    FunctionGuide.Instance:OnXiuXianShiLianInfoChange()
end

function XiuXianShiLianWGData:GetTaskProgressByTaskType(task_type)
    if not IsEmptyTable(self.task_progress_list) then
        return self.task_progress_list[task_type]
    end
    return {}
end

function XiuXianShiLianWGData:GetOldChapterFetchedList()
    if not IsEmptyTable(self.chapter_fetch_flag) then
        local list = {}
        for k, v in pairs(self.chapter_fetch_flag) do
            list[k] = v
        end
        return list
    end
    return {}
end

function XiuXianShiLianWGData:GetChapterFetchedByChapterId(id)
    if not IsEmptyTable(self.chapter_fetch_flag) then
        return self.chapter_fetch_flag[id]
    end
    return 0
end

function XiuXianShiLianWGData:GetChapterRewardFetchedByChapterId(id)
    if not IsEmptyTable(self.chapter_reward_flag) then
        return self.chapter_reward_flag[id]
    end
    return 0
end

function XiuXianShiLianWGData:GetChapterCompleteByChapterId(id)
    if not IsEmptyTable(self.chapter_complete_flag) then
        return self.chapter_complete_flag[id]
    end
    return 0
end

function XiuXianShiLianWGData:GetXiuxianRemind()
    if not FunOpen.Instance:GetFunIsOpened(GuideModuleName.XiuXianShiLian) then
        return 0
    end

    local chapter_list = self:GetChapterListInfo()

    for k, v in pairs(chapter_list) do
        if self:GetChapterIsOpen(v.chapter_id) and v.is_unlock then
            if v.is_complete == 1 and (v.is_fetched == 0 or v.reward_is_fetched == 0) then
                return 1
            end
        end
    end

    for k, v in pairs(chapter_list) do
        if self:GetChapterIsOpen(v.chapter_id) and v.is_unlock then
            for k1, v1 in pairs(self.xiuxian_task_cfg[k]) do
                local task_pro = self:GetTaskProgressByTaskType(v1.task_type)
                if not IsEmptyTable(task_pro) then
                    if task_pro.is_complete == 1 and task_pro.has_fetched_reward == 0 then
                        return 1
                    end
                end
            end
        end
    end
    return 0
end

function XiuXianShiLianWGData:GetTianShenAwakenFetchFlag(skill_index)
    local data_info = self:GetChapterListInfo()
    for k, v in pairs(data_info) do
        if v.skill_index == skill_index then
            return self:GetChapterFetchedByChapterId(k) == 1
        end
    end

   return false
end

--进来的时候 默认选中最近的&未完成的章节
function XiuXianShiLianWGData:GetCurChapter()
    local data_info = self:GetChapterListInfo()
    local role_lv = RoleWGData.Instance:GetRoleLevel()
    local cur_index = 1

    for k, v in pairs(data_info) do
        if v.is_unlock and v.open_level <= role_lv and (v.is_complete == 0 or v.is_fetched == 0 or v.reward_is_fetched == 0) then
            cur_index = k
            break
        end
    end

    for k, v in pairs(data_info) do
        if v.is_unlock and v.is_can_fetch and v.open_level <= role_lv then --可领取
            cur_index = k
            break
        end
    end

    local open_index = #data_info + 1
    for k, v in pairs(data_info) do
        if v.is_unlock and v.open_level > role_lv then
            open_index = k - 1
            break
        end
    end

    if open_index == 0 then
        open_index = 1
    end

    if cur_index > open_index then
        cur_index = open_index
    end

    return cur_index
end

-- 判断任务是否开启
function XiuXianShiLianWGData:GetTaskTypeIsOpen(task_type)
    local task_cfg = self:GetTaskCfgByTaskType(task_type)
    return self:GetChapterIsOpen(task_cfg.chapter_id)
end

-- 判断章节是否开启
function XiuXianShiLianWGData:GetChapterIsOpen(chapter_id)
    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
    local role_level = main_role_vo.level
    local chapter_cfg = self.xiuxian_chapter_cfg[chapter_id]
    if not IsEmptyTable(chapter_cfg) and chapter_cfg.open_level then
        return chapter_cfg.open_level <= role_level
    end

    return false
end

--主界面任务栏显示已开启等级最低的并且未完成或者未领取完奖励的
function XiuXianShiLianWGData:GetLastChapterIndex()
    local role_lv = RoleWGData.Instance:GetRoleLevel()
    local data_info = self:GetChapterListInfo()
    local open_index = 0
    local max_complete_index = 0
    for k, v in ipairs(data_info) do
        if v.is_unlock and role_lv >= v.open_level and (v.is_complete == 0 or v.is_fetched == 0 or v.reward_is_fetched == 0) then
            open_index = k
            break
        elseif v.is_unlock and role_lv >= v.open_level and k > max_complete_index then
            max_complete_index = k
        end
    end

    if open_index > #data_info then
        open_index = #data_info
    elseif open_index == 0 and max_complete_index > 0 then
        open_index = max_complete_index
    elseif open_index == 0 then
        open_index = 1
    end

    return open_index
end

function XiuXianShiLianWGData:GetMaxUnLockChapterId()
    local data_info = self:GetChapterListInfo()
    if not IsEmptyTable(data_info) then
        local role_lv = RoleWGData.Instance:GetRoleLevel()
        for i = #data_info, 1, -1 do
            local data = data_info[i]
            if data and role_lv >= data.open_level then
                return data.chapter_id
            end
        end
    end

    return 0
end


-- 得到一个有奖励领取的chapter_id
function XiuXianShiLianWGData:GetHasRewardChapterId()
    local data_info = self:GetChapterListInfo()
    for k,v in ipairs(data_info) do
        if self:GetChapterIsOpen(v.chapter_id) and v.is_unlock then
            if self:GetXiuXianSkillCanActive(k) then
                return k
            end

            local task_info_list = self:GetTaskListInfoById(v.chapter_id)
            for i, task_info in ipairs(task_info_list) do
                if self:GetTaskRewardFetable(task_info.task_type) then  --可领取
                    return k
                end
            end
        end
    end

    return nil
end

-- 主界面任务栏显示的chapter_id
function XiuXianShiLianWGData:GetMainViewDisplayChapterId()
    local has_reward_chapter_id = self:GetHasRewardChapterId()
    if has_reward_chapter_id then
        return has_reward_chapter_id
    else
        return self:GetLastChapterIndex()
    end
end

function XiuXianShiLianWGData:IsAllRewardGet()
    local data_info = self:GetChapterListInfo()
    for k, v in pairs(data_info) do
        if v.is_complete == 0 or v.is_fetched == 0 or v.reward_is_fetched == 0 then
            return false
        end
    end

    return true
end

function XiuXianShiLianWGData:GetRandomContent()
    local content_list = self.world_chat_cfg
    if not IsEmptyTable(content_list) then
        local idx = math.random(1, #content_list)
        return content_list[idx].chat_content
    end
    return ""
end

function XiuXianShiLianWGData:GetSkillAttrCap(data)
    local cap = 0
	if IsEmptyTable(data) then
		return cap
	end

	local attr_list = {}
	local function add_tab(attr_str, value)
		if attr_str == nil or value == nil or attr_str == "" or value <= 0 then
			return
		end

		if attr_list[attr_str] then
			attr_list[attr_str] = attr_list[attr_str] + value
		else
			attr_list[attr_str] = value
		end
	end

    -- 特殊处理
    if data.skill_id == XIUXIANZHILV_SKILL_TYPE_1 then
        local attr_type = GameEnum.BASE_CHARINTATTR_TYPE_GONGJI
        local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_type)
        local role_level = RoleWGData.Instance:GetRoleLevel()
        local attr_value = role_level * tonumber(data.param1)
        add_tab(attr_str, attr_value)
    else
    	for i = 1, 2 do
            local index = (i - 1) * 2 + 1
            local attr_type = tonumber(data["param" .. index])
            local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_type)
            local attr_value = data["param" .. index + 1] or 0
    		if attr_value > 0 and attr_str ~= "" then
                attr_str = AttributeMgr.GetAttributteKey(attr_str)
    			add_tab(attr_str, attr_value)
    		end
    	end
    end

	local attribute = AttributeMgr.GetAttributteByClass(attr_list)
	cap = AttributeMgr.GetCapability(attribute) + data.capability_inc
	return cap
end

function XiuXianShiLianWGData:SetXiuXianSelectItemIndex(index)
    self.select_xiuxian_item_index = index or 0
end

function  XiuXianShiLianWGData:GetXuXianSelectItemIndex()
    return self.select_xiuxian_item_index or 0
end

function XiuXianShiLianWGData:IsGetAllTaskReward(chapter_id)
    if not self.xiuxian_task_cfg[chapter_id] then
        return false
    end

    local is_get_all_reward = true
    for k, v in pairs(self.xiuxian_task_cfg[chapter_id]) do
        local sub_task = self:GetTaskProgressByTaskType(v.task_type)
        if sub_task and sub_task.has_fetched_reward == 0 then
            is_get_all_reward = false
            break
        end
    end

    return is_get_all_reward
end

-- 获得最新解锁的后面一个章节id
function XiuXianShiLianWGData:GetNextUnLockChapterId()
    local data_info = self:GetChapterListInfo()
    if not IsEmptyTable(data_info) then
        local role_lv = RoleWGData.Instance:GetRoleLevel()
        for i = #data_info, 1, -1 do
            local data = data_info[i]
            if data and role_lv >= data.open_level then
                return data.chapter_id
            end
        end
    end

    return 1
end

-- 是否完成全部章节(包括完成任务,领取奖励)
function XiuXianShiLianWGData:GetAllCapterIsComplete()
    local max_index = self:GetMaxUnLockChapterId()
    if max_index > 0 then
        local is_complete = self:GetChapterCompleteByChapterId(max_index)
        local is_fetched = self:GetChapterFetchedByChapterId(max_index)
        local reward_is_fetched = self:GetChapterRewardFetchedByChapterId(max_index)
        return is_complete == 1 and is_fetched == 1 and reward_is_fetched == 1
    end

    return false
end

-- 获取最新章节中第一个未完成的任务配置
function XiuXianShiLianWGData:GetNoCompleteTaskCfgByCapterID(chapter_id)
    local cfg = self.xiuxian_task_cfg[chapter_id]
    if cfg then
        for k, v in ipairs(cfg) do
            local task_pro = self:GetTaskProgressByTaskType(v.task_type)
            if not IsEmptyTable(task_pro) and (task_pro.is_complete == 0 or task_pro.has_fetched_reward == 0) then
                return v
            end
        end
    end
end

function XiuXianShiLianWGData:GetModelShowInfo(chapter_id)
    if self.chapter_model_show_info[chapter_id] then
        return self.chapter_model_show_info[chapter_id]
    end

    local model_cfg = self:GetModelShowCfg(chapter_id)
    local display_data = {}
    if not model_cfg then
        return display_data
    end

	local model_show_type = tonumber(model_cfg.model_show_type) or 1
	if model_cfg.model_show_itemid ~= 0 and model_cfg.model_show_itemid ~= "" then
		local split_list = Split(model_cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
                if not display_data.show_item_id  then
                    display_data.show_item_id = tonumber(v)
                end
			end

			display_data.model_item_id_list = list
		else
			display_data.item_id = model_cfg.model_show_itemid
			display_data.show_item_id = model_cfg.model_show_itemid
		end
	end

    display_data.bundle_name = model_cfg.model_bundle_name
    display_data.asset_name = model_cfg.model_asset_name
    display_data.render_type = model_show_type - 1
    display_data.can_drag = false
    display_data.should_ani = display_data.render_type ~= OARenderType.Image

    local view_use_pos
    if model_cfg.display_pos and model_cfg.display_pos ~= "" then
        local pos_x, pos_y, pos_z = 0, 0, 0
        local pos_list = Split(model_cfg.display_pos, "|")
        pos_x = tonumber(pos_list[1]) or pos_x
        pos_y = tonumber(pos_list[2]) or pos_y
        pos_z = tonumber(pos_list[3]) or pos_z
        display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
        if display_data.render_type ~= OARenderType.RoleModel then
            display_data.position = display_data.model_adjust_root_local_position
        end
    end
    display_data.position = view_use_pos or Vector3(0, 0, 0)

    local view_use_rot
    if model_cfg.rotation and model_cfg.rotation ~= "" then
        local rot_list = string.split(model_cfg.rotation, "|")
        local rot_x = tonumber(rot_list[1]) or 0
        local rot_y = tonumber(rot_list[2]) or 0
        local rot_z = tonumber(rot_list[3]) or 0
        display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
        if display_data.render_type ~= OARenderType.RoleModel then
            display_data.rotation = display_data.model_adjust_root_local_rotation
        end
    end
    display_data.rotation = view_use_rot or Vector3(0, 0, 0)

    local view_use_scale
    if model_cfg.display_scale and model_cfg.display_scale ~= "" then
        display_data.model_adjust_root_local_scale = model_cfg.display_scale
        if display_data.render_type ~= OARenderType.RoleModel then
            display_data.scale = Vector3(model_cfg.display_scale, model_cfg.display_scale, model_cfg.display_scale)
        end
    end
    display_data.scale = view_use_scale or Vector3(1, 1, 1)

    if model_cfg.mainui_display_pos and model_cfg.mainui_display_pos ~= "" then
        local pos_x, pos_y, pos_z = 0, 0, 0
        local pos_list = Split(model_cfg.mainui_display_pos, "|")
        pos_x = tonumber(pos_list[1]) or pos_x
        pos_y = tonumber(pos_list[2]) or pos_y
        pos_z = tonumber(pos_list[3]) or pos_z
        display_data.mainui_display_pos = Vector3(pos_x, pos_y, pos_z)
    end

    if model_cfg.mainui_rotation and model_cfg.mainui_rotation ~= "" then
        local rot_list = string.split(model_cfg.mainui_rotation, "|")
        local rot_x = tonumber(rot_list[1]) or 0
        local rot_y = tonumber(rot_list[2]) or 0
        local rot_z = tonumber(rot_list[3]) or 0
        display_data.mainui_rotation = Vector3(rot_x, rot_y, rot_z)
    end

    if model_cfg.mainui_display_scale and model_cfg.mainui_display_scale ~= "" then
        display_data.mainui_display_scale = model_cfg.mainui_display_scale
    end

    if display_data.render_type > -1 then
        self.chapter_model_show_info[chapter_id] = display_data
    end

    return display_data
end