--中间放大 传闻

TipSystemNoticeView = TipSystemNoticeView or BaseClass(SafeBaseView)

local SPEED = 100						-- 字幕滚动的速度(像素/秒)

function TipSystemNoticeView:__init()
	self.view_name = "TipSystemNoticeView"
	self:AddViewResource(0, "uis/view/miscpre_load_prefab", "SystemNoticeView")
	self.view_layer = UiLayer.PopTop

	self.is_open = false
	self.str_list = {}
	self.current_index = 1
	self.can_do_fade = false
	self.total_count = 0
	self.calculate_time_quest = nil
end

function TipSystemNoticeView:__delete()
	if nil ~= self.calculate_time_quest then
		GlobalTimerQuest:CancelQuest(self.calculate_time_quest)
		self.calculate_time_quest = nil
	end

	self.is_open = false
end

function TipSystemNoticeView:LoadCallBack()
	self.text_obj = self.node_list["TextContent"]
	self.text_trans = self.node_list["NotifyBanner"].rect

	self.mask_width = self.node_list["mask"].rect.sizeDelta.x
	self.mask_width_cost_time = self.mask_width / SPEED
	self.is_open = true
	self.shield_hearsay = GlobalEventSystem:Bind(
		SettingEventType.CLOSE_HEARSAY,
		BindTool.Bind1(self.OnShieldHearsay, self))
end

function TipSystemNoticeView:ReleaseCallBack()
	if self.shield_hearsay then
		GlobalEventSystem:UnBind(self.shield_hearsay)
		self.shield_hearsay = nil
	end

	self:CancelTween()
	-- 清理变量和对象
	self.text_obj = nil
	self.text_trans = nil
	self.tweener = nil
end

function TipSystemNoticeView:OnShieldHearsay(value)
	if value then
		self:Close()
	end
end

function TipSystemNoticeView:OpenCallBack()
	--不允许随机公告出现
	-- RandSystemWGCtrl.Instance:SetCanOpenSystem(false)
	self.tweener = nil
end

function TipSystemNoticeView:CloseCallBack()
	--允许随机公告出现
	-- RandSystemWGCtrl.Instance:SetCanOpenSystem(true)
	if self.tweener then
		self.tweener:Pause()
	end

end

function TipSystemNoticeView:SetNotice(str)
	local shield_hearsay = SettingWGData.Instance:GetSettingData(SETTING_TYPE.CLOSE_HEARSAY)
	if shield_hearsay then
		return
	end
	if not self.is_open then
		self:Open()
		self.str_list = {}
		self.current_index = 1
		self.total_count = 0
		self:AddNotice(str)
		self:Flush()
	else
		self:AddNotice(str)
	end
end

function TipSystemNoticeView:AddNotice(str)
	self.total_count = self.total_count + 1
	self.str_list[self.total_count] = str
end

function TipSystemNoticeView:OnFlush()
	if(self.current_index <= self.total_count) then
		local str = self.str_list[self.current_index]
		EmojiTextUtil.ParseRichText(self.text_obj.emoji_text, str, 20, nil, nil, false, RICH_CONTENT_TYPE.MAIN_UI, nil, true)
		GlobalTimerQuest:AddDelayTimer(function()
			if self.node_list["bg"] then
				local v2 = self.node_list["bg"].rect.sizeDelta
				self.node_list["bg"].rect.sizeDelta = Vector2(self.text_obj.rect.sizeDelta.x + 150, v2.y)
			end
		end, 0)
		--self.node_list.Text.text.text = str
		-- self.node_list.Text.text.text = str

		-- self.text_trans.anchoredPosition = Vector2(0, 0)
		self.text_trans.gameObject:SetActive(false)
		self.calculate_time_quest = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.Calculate, self), 0.1)
	end
end

-- 计算滚动的时间的位置
function TipSystemNoticeView:Calculate()
	if self.calculate_time_quest then
		GlobalTimerQuest:CancelQuest(self.calculate_time_quest)
	end
	self.calculate_time_quest = nil
	if nil == self.text_trans then
		return
	end

	local str = self.str_list[self.current_index]
	EmojiTextUtil.ParseRichText(self.text_obj.emoji_text, str, 20, nil, nil, false, RICH_CONTENT_TYPE.MAIN_UI, nil, true)

	local width = self.text_trans.sizeDelta.x
	local duration = width / SPEED + self.mask_width_cost_time
	width = width + self.mask_width
	-- print_log(width,self.mask_width,duration,self.mask_width_cost_time)


	-- local tweener = self.text_trans:DOAnchorPosX(-width, duration, false)
	-- self.tweener = tweener
	-- tweener:SetEase(DG.Tweening.Ease.Linear)
	-- tweener:OnComplete(BindTool.Bind(self.OnMoveEnd, self))


	-- self.text_trans.transform.localScale = Vector3(1.3,1.3,1.3)
	-- local tweener = self.text_trans.gameObject.transform:DOScale(1, 0.7)
	-- -- self.tweener = tweener
	-- -- tweener:SetEase(DG.Tweening.Ease.Linear)

	-- tweener:OnComplete(BindTool.Bind(self.OnMoveEnd, self))

	local speed = #self.str_list == 1 and 2 or #self.str_list
	self:CancelTween()
	local sequence = DG.Tweening.DOTween.Sequence();
	sequence:AppendCallback(function()
	self.text_trans.gameObject:SetActive(true)
	self.text_trans.transform.localScale = Vector3(2,2,2)
	self.text_trans.gameObject.transform:DOScale(1, 0.2):SetEase(DG.Tweening.Ease.Linear)
	end)
	sequence:AppendInterval(2.8)
	sequence:AppendCallback(BindTool.Bind(self.OnMoveEnd, self))
	self.sequence_tween = sequence
end

function TipSystemNoticeView:CancelTween()
    if self.sequence_tween then
        self.sequence_tween:Kill()
        self.sequence_tween = nil
    end
end

function TipSystemNoticeView:OnMoveEnd()
	self.current_index = self.current_index + 1
	self:Delay()
end

function TipSystemNoticeView:Delay()
	if(self.current_index > self.total_count) then
		self:Close()
	else
		self:Flush()
	end
end

function TipSystemNoticeView:ClearNoticeList()
	self.str_list = {}
	self.current_index = 1
	self.total_count = 0
end
