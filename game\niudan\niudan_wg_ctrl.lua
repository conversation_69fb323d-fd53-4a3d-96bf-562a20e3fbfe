require("game/niudan/niudan_wg_data")
require("game/niudan/niudan_view")
require("game/niudan/niudan_record_view")
require("game/niudan/niudan_reward")

NiuDanWGCtrl = NiuDanWGCtrl or BaseClass(BaseWGCtrl)

function NiuDanWGCtrl:__init()
	if NiuDanWGCtrl.Instance then
		ErrorLog("[NiuDanWGCtrl] Attemp to create a singleton twice !")
	end
	NiuDanWGCtrl.Instance = self
	self:RegisterAllProtocols()
    self.view = NiuDanView.New(GuideModuleName.NiuDanView)
	self.data = NiuDanWGData.New()
	self.record_view = NiuDanRecord.New()
    self.reward_view = NiuDanReward.New()

	self.act_change = BindTool.Bind(self.ActChange, self)
    ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.MainuiOpenCreate, self))
end

function NiuDanWGCtrl:__delete()
    self.view:DeleteMe()
    self.view = nil

	self.data:DeleteMe()
	self.data = nil

    self.record_view:DeleteMe()
	self.record_view = nil

    self.reward_view:DeleteMe()
	self.reward_view = nil

	ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
	self.act_change = nil

    if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end

    self.main_ui_load = nil
	NiuDanWGCtrl.Instance = nil
end

function NiuDanWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCFAYanHuaShengDianInfo,'OnSCFAYanHuaShengDianInfo')
    self:RegisterProtocol(SCFAYanHuaShengDianRecordListInfo,'OnSCFAYanHuaShengDianRecordListInfo')
    self:RegisterProtocol(SCFAYanHuaShengDianDrawRewardInfo,'OnSCFAYanHuaShengDianDrawRewardInfo')
    self:RegisterProtocol(SCRAYanHuaShengDian2BaoDiRewardDrawInfo,'OnSCRAYanHuaShengDian2BaoDiRewardDrawInfo')
end

function NiuDanWGCtrl:OnSCRAYanHuaShengDian2BaoDiRewardDrawInfo(protocol)
    self.data:SaveDrawInfo(protocol)
    if self.view:IsOpen() then
        self.view:Flush()
    end

    RemindManager.Instance:Fire(RemindName.NiuDan)
end

function NiuDanWGCtrl:MainuiOpenCreate()
    self.main_ui_load = true
    if self.data:GetActIsOpen() then
        NiuDanWGCtrl.Instance:SendReq(CSA_YANHUA_OP.INFO)
    end
end

--活动信息
function NiuDanWGCtrl:OnSCFAYanHuaShengDianInfo(protocol)
	-- print_error("info",protocol)
	self.data:SetInfo(protocol)
	if self.view:IsOpen() then
        self.view:Flush()
    end
    self.reward_view:Flush("ignore")
    RemindManager.Instance:Fire(RemindName.NiuDan)
end

--日志
function NiuDanWGCtrl:OnSCFAYanHuaShengDianRecordListInfo(protocol)
	-- print_error("record", protocol.record_count, protocol)
	self.data:SetRecord(protocol.record_list)
    if self.view:IsOpen() then
        self.view:Flush(0, "all", {[2] = "record"})
    end
	self.record_view:Flush()
end

--奖励
function NiuDanWGCtrl:OnSCFAYanHuaShengDianDrawRewardInfo(protocol)
    -- print_error("reward", protocol.reward_count, protocol)
    if self.view:IsOpen() then
        self.view:Flush(0, "all", {[2] = "play_ani"})
    end
    local delay_time = self.data:GetDelayTime()
    GlobalTimerQuest:AddDelayTimer(function ()
        local data_list = self.data:CalDrawRewardList(protocol)
        self:OpenRewardView(data_list)
    end, delay_time)
end

--请求
function NiuDanWGCtrl:SendReq(opera_type, param1, param2)
	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.FESTIVAL_ACT_YANHUA_SHENGDIAN_2,
		opera_type = opera_type,
        param_1 = param1,
        param_2 = param2,
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

--奖励弹窗
function NiuDanWGCtrl:OpenRewardView(data)
    self.reward_view:SetData(data)
    self.reward_view:Open()
end

function NiuDanWGCtrl:CloseReawrdCallBack()
    if self.view:IsOpen() then
        self.view:Flush(0, "all", {[2] = "reset_ani"})
    end
end

--监听活动状态改变，清除缓存标记，添加道具监听，
function NiuDanWGCtrl:ActChange(act_type, status)
    if act_type ~= ACTIVITY_TYPE.FESTIVAL_ACT_YANHUA_SHENGDIAN_2 then
        return
    end

	self.data:ClearCache()
    if status == ACTIVITY_STATUS.OPEN then
        if self.main_ui_load then
            NiuDanWGCtrl.Instance:SendReq(CSA_YANHUA_OP.INFO)
        end
    elseif status == ACTIVITY_STATUS.CLOSE then
        self.is_fire_map = false
    end
end

--打开记录
function NiuDanWGCtrl:OpenRecord()
    self.record_view:Open()
end

--使用道具并弹窗
function NiuDanWGCtrl:ClickUse(index, func)
     --数量检测
    local cfg = NiuDanWGData.Instance:GetConsumeCfg()
    local num = ItemWGData.Instance:GetItemNumInBagById(cfg[index].yanhua_item.item_id)

    --不足弹窗
    if num < cfg[index].yanhua_item.num then
        if not self.alert then
            self.alert = Alert.New()
        end
        self.alert:ClearCheckHook()
        self.alert:SetShowCheckBox(true, "festival_fireworks")
        self.alert:SetCheckBoxDefaultSelect(false)
        local item_cfg = ItemWGData.Instance:GetItemConfig(cfg[index].yanhua_item.item_id)
        local name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
        local cost = cfg[1].consume_count * (cfg[index].yanhua_item.num - num)
        local str = string.format(Language.MergeFireworks.CostStr, name, cost)

        self.alert:SetLableString(str)
        self.alert:SetOkFunc(func)
        self.alert:Open()
    else
        --使用
        func()
    end
end


function NiuDanWGCtrl:GetIsFireMap()
    return self.is_fire_map
end