require("game/life_indulgence/life_indulgence_view")
require("game/life_indulgence/life_indulgence_wg_data")

LifeIndulgenceWGCtrl = LifeIndulgenceWGCtrl or BaseClass(BaseWGCtrl)

function LifeIndulgenceWGCtrl:__init()
    if LifeIndulgenceWGCtrl.Instance then
		error("[LifeIndulgenceWGCtrl]:Attempt to create singleton twice!")
	end

	LifeIndulgenceWGCtrl.Instance = self

    self.view = LifeIndulgenceView.New(GuideModuleName.LifeIndulgenceView)
    self.data = LifeIndulgenceWGData.New()

    self:RegisterAllProtocols()
    self:RegisterAllEvents()
end

function LifeIndulgenceWGCtrl:__delete()
    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

    if self.view then
        self.view:DeleteMe()
        self.view = nil
    end

    LifeIndulgenceWGCtrl.Instance = nil

    self:UnRegisterAllEvents()
end

function LifeIndulgenceWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCLifeIndulgenceInfo, "OnSCLifeIndulgenceInfo")
end

function LifeIndulgenceWGCtrl:RegisterAllEvents()
    self.open_fun_change = GlobalEventSystem:Bind(OpenFunEventType.OPEN_TRIGGER, BindTool.Bind(self.OpenFunEventChange, self))
	self.mainui_open_comlete = self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreateCallBack, self)) --主界面加载完成

    -- 天数改变
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.OnDayChange, self))
end

function LifeIndulgenceWGCtrl:UnRegisterAllEvents()
    if self.open_fun_change then
        GlobalEventSystem:UnBind(self.open_fun_change)
        self.open_fun_change = nil
    end

    if self.mainui_open_comlete then
		GlobalEventSystem:UnBind(self.mainui_open_comlete)
		self.mainui_open_comlete = nil
    end
end

function LifeIndulgenceWGCtrl:OnSCLifeIndulgenceInfo(protocol)
    self.data:SetInfo(protocol)

    if self.view:IsOpen() then
        if self.data:IsShowTips() then
            self.view:Flush()
        else
            self.view:Close()
            self:SetLifeIndulgenceActivity()
        end
    end
end

function LifeIndulgenceWGCtrl:OpenFunEventChange(check_all, fun_name, is_open)
    if check_all or fun_name == FunName.LifeIndulgenceView then
		self:SetLifeIndulgenceActivity()
    end
end

function LifeIndulgenceWGCtrl:MainuiOpenCreateCallBack()
    self:SetLifeIndulgenceActivity()
end

-- 终身特惠
function LifeIndulgenceWGCtrl:SetLifeIndulgenceActivity()
	local is_show_tip = LifeIndulgenceWGData.Instance:IsShowTips()
	if IS_AUDIT_VERSION or (not is_show_tip) then
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.LIFE_INDULGENCE_ACTIVITY, ACTIVITY_STATUS.CLOSE)
        GameAssistantWGCtrl.Instance:FlushView()
        return
	end

	ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.LIFE_INDULGENCE_ACTIVITY, ACTIVITY_STATUS.OPEN)
end

-- 天数改变
function LifeIndulgenceWGCtrl:OnDayChange()
    if self.view:IsOpen() then
        if self.data:IsShowTips() then
            self.view:Flush()
        else
            self.view:Close()
            self:SetLifeIndulgenceActivity()
        end
    end

    RemindManager.Instance:Fire(RemindName.LifeIndulgence)

    local act_btn = MainuiWGCtrl.Instance:GetActivityButtonByActivityType(ACTIVITY_TYPE.LIFE_INDULGENCE_ACTIVITY)
	if act_btn then
		act_btn:Flush("SetSprite")
	end
end

function LifeIndulgenceWGCtrl:SetDayChangeRed()
    LifeIndulgenceWGData.Instance:CheckDayChangeRed(true)
    RemindManager.Instance:Fire(RemindName.LifeIndulgence)
end