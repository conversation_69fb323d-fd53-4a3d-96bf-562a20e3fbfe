--寻宝 符文、装备、巅峰、至尊
TreasureHuntWGData = TreasureHuntWGData or BaseClass()

TreasureHuntWGData.CHESTSHOP_REQ = 
{
    CHESTSHOP_REQ_STORGE_INFO = 0,	--请求寻宝仓库信息
    CHESTSHOP_BUY = 1,				--寻宝抽取
    CHESTSHOP_FETCH_ITEM = 2,		--寻宝仓库取出单个物品
    CHESTSHOP_FETCH_ALL = 3,		--寻宝仓库取出所有物品
    CHESTSHOP_RECORD_REQ = 4,		--寻宝日志信息请求
    CHESTSHOP_FETCH_WEEK_REWARD = 5, --周奖励领取
    CHESTSHOP_REQ_BASE_INFO = 6,	--请求寻宝基本信息
    CHESTSHOP_REQ_SORT_BAG = 7,     --请求整理仓库
};

TreasureHuntWGData.MingwenMode =
{
    One = 101,		--单抽
    Ten = 102,      --10抽
}

TreasureHuntWGData.IsOpenType = {
    [1] = "treasurehunt_equip",            -- 装备 
    [2] = "treasurehunt_dianfeng" ,        -- 巅峰
    [3] = "treasurehunt_zhizun",           -- 至尊
    [4] = "treasurehunt_fuwen" ,           -- 符文
}

TreasureHuntWGData.CHESTSHOP_CONVERT_REQ =
{
	CHESTSHOP_CONVERT_BUY = 0,		--兑换 index num
}

TreasureHuntWGData.TreasureType = {
    Equipment = 1,      -- 装备
    DianFeng = 2,       -- 巅峰
    ZhiZun = 3,         -- 至尊
}

--铭纹寻宝
TreasureHuntWGData.POSY_OPERATOR_TYPE =
{
    POSY_SHOP_OPERATOR_TYPE_BUY = 0,			-- 商店购买 p1(购买模式) p2(是否免费)
    POSY_SHOP_OPERATOR_TYPE_WEEK_REWARD = 1,	-- 周奖励 p1(index)
    POSY_SHOP_RECORD_INFO = 3,	                --请求记录
};

function TreasureHuntWGData:__init()
	if TreasureHuntWGData.Instance then
		error("[TreasureHuntWGData] Attempt to create singleton twice!")
		return
	end
	TreasureHuntWGData.Instance = self
    self.treasurehunt_cfg = ConfigManager.Instance:GetAutoConfig("chestshop_auto")
    self.treasurehunt_mode_cfg = ListToMapList(self.treasurehunt_cfg.mode, "xunbao_type")
    self.treasurehunt_equip_cfg = self.treasurehunt_cfg.rare_item_list
    self.treasurehunt_dianfeng_cfg = self.treasurehunt_cfg.xishi_item_list
    self.treasurehunt_zhizun_cfg = self.treasurehunt_cfg.fumo_item_list
    self.treasurehunt_probability_cfg = ListToMapList(self.treasurehunt_cfg.item_random_desc, "xunbao_type")
    self.treasurehunt_week_reward_cfg = ListToMapList(self.treasurehunt_cfg.week_reward, "shop_mode")
    self.treasurehunt_convertshop_cfg = ListToMapList(self.treasurehunt_cfg.convert_shop, "price_type")
    self.treasurehunt_baodi_cfg = ListToMap(self.treasurehunt_cfg.baodi, "xunbao_type", "pool_level", "seq")
    self.treasurehunt_rare_cfg = ListToMap(self.treasurehunt_cfg.item_random_desc, "item_id")
    self.treasurehunt_other_cfg = self.treasurehunt_cfg.other[1]
    self.treasurehunt_vip_mode_cfg = ListToMapList(self.treasurehunt_cfg.vip_mode, "mode")
    self.treasurehunt_pool_level_cfg = ListToMap(self.treasurehunt_cfg.pool_level, "xunbao_type", "pool_level")
    self.best_show_cfg_2_cfg = ListToMap(self.treasurehunt_cfg.best_show_cfg_2, "treasure_type")

    self.treasurehunt_show_model_cfg = ListToMapList(self.treasurehunt_cfg.best_show_cfg,"treasure_type")
    self.treasurehunt_boss_cfg = ConfigManager.Instance:GetAutoConfig("chestshop_boss_auto")
    -- self.treasurehunt_type_cfg = ListToMapList(self.treasurehunt_boss_cfg.boss_cfg,"chestshop_type") -- 没用到注释
    self.treasurehunt_seq_cfg = ListToMap(self.treasurehunt_boss_cfg.boss_cfg,"seq")
    --铭纹寻宝相关
    self.mingwen_cfg = ConfigManager.Instance:GetAutoConfig("posy_auto")
    self.mingwen_weekreward_cfg = self.mingwen_cfg.posy_shop_week_reward
    self.mingwen_mode_cfg = self.mingwen_cfg.posy_shop_mode
    self.mingwen_items_cfg = self.mingwen_cfg.posy_shop_item
    self.mingwen_other_cfg = self.mingwen_cfg.posy_shop_other
    self.mingwen_vip_mode_cfg = ListToMapList(self.mingwen_cfg.vip_mode, "mode")

    ------------
    self.treasure_hunt_tab = {}
    self.is_first_login = 0
    self.person_record_info = {}
    self.world_record_info = {}
    self.world_big_record_info = {}
    self.base_info = {
        chestshop_score_list = {},
        week_count_list = {}, --周抽奖次数
        week_count_reward_flag_list = {},
        times_list = {}, --抽奖总次数
        pool_level = {}, --奖池等级  seq / level
    }
    self.anim_toggle_enable_list = {}
    self.result_data_list = {}
    self.model_data_list_cfg = {}
    self.flush_effect_show = false

    self.mark_first_flag_list =
    {
        [1] = false,
        [2] = false,
        [3] = false,
        [4] = false,
    }
    self.mingwen_base_info =
    {
        day_count = 0,  --每天抽奖次数
        week_count = 0, --周抽奖次数
        week_count_reward_flag = {},
        free_timestamp = 0,  --免费时间
    }
    self.old_week_index = 0
    self.cache_sub_name = {}
    
    self.skip_spine_cache = {}

    self:SetTreasureHuntIsFirst()
    RemindManager.Instance:Register(RemindName.TreasureHunt, BindTool.Bind(self.GetAllRemind, self))
    RemindManager.Instance:Register(RemindName.TreasureHunt_FuWen, BindTool.Bind(self.GetMingwenRemindNum, self))			-- 符文寻宝红点
	RemindManager.Instance:Register(RemindName.TreasureHunt_Equip, BindTool.Bind(self.GetCommonRemind, self, 1))			-- 装备寻宝红点
	RemindManager.Instance:Register(RemindName.TreasureHunt_DianFeng, BindTool.Bind(self.GetCommonRemind, self, 2))		-- 巅峰寻宝红点
	RemindManager.Instance:Register(RemindName.TreasureHunt_Zhizun, BindTool.Bind(self.GetCommonRemind, self, 3))			-- 至尊寻宝红点
    self:RegisterRemindInBag()
end

function TreasureHuntWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.TreasureHunt)
    RemindManager.Instance:UnRegister(RemindName.TreasureHunt_FuWen)
    RemindManager.Instance:UnRegister(RemindName.TreasureHunt_Equip)
    RemindManager.Instance:UnRegister(RemindName.TreasureHunt_DianFeng)
    RemindManager.Instance:UnRegister(RemindName.TreasureHunt_Zhizun)

    if self.refresh_event then
        GlobalTimerQuest:CancelQuest(self.refresh_event)
        self.refresh_event = nil
    end
    TreasureHuntWGData.Instance = nil
end

function TreasureHuntWGData:RegisterRemindInBag()
    self:RegisterTreasureHuntRemindInBag(RemindName.TreasureHunt_FuWen, 4)
    self:RegisterTreasureHuntRemindInBag(RemindName.TreasureHunt_Equip, 1)
    self:RegisterTreasureHuntRemindInBag(RemindName.TreasureHunt_DianFeng, 2)
    self:RegisterTreasureHuntRemindInBag(RemindName.TreasureHunt_Zhizun, 3)
end

function TreasureHuntWGData:GetMingwenRemindNum()
    local mw_open = FunOpen.Instance:GetFunIsOpenedByTabName(TreasureHuntWGData.IsOpenType[4])
    if not mw_open then
        return 0
    end
    for i = 1, 2 do
        if self:GetMingWenBtnRemind(i) == 1 then
            return 1
        end
    end

    if self:ConvertRemind() then
        return 1
    end
    local mingwen_week = self:GetMingwenWeek()
    for k ,v in pairs(mingwen_week) do
        if v.is_get == 0 and v.count <= v.total_week_count then
            return 1
        end
    end

    if OneSwordFrostbiteWGData.Instance:GetOneSwordFrostbiteRemind() == 1 then
        return 1
    end

    return 0
end

function TreasureHuntWGData:RegisterTreasureHuntRemindInBag(remind_name, treasure_type)
    local map = {}

    if treasure_type <= 3 then
        local treasure_cfg = self:GetTreasureCfgByType(treasure_type)
        local key_id = treasure_cfg[1].stuff_id
        map[key_id] = true
    else
        local cfg = self:GetMingwenModeCfg()
        local key_id = cfg[1].stuff_id
        map[key_id] = true
    end

    local item_id_list = {}
    for k,v in pairs(map) do
        table.insert(item_id_list, k)
    end
    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function TreasureHuntWGData:GetCommonRemind(treasure_type)
    --周奖励
    -- local WeekReward = self:GetWeekRewardInfo(treasure_type)
    -- for k,v in pairs(WeekReward) do
    --     if v.is_get == 0 and v.total_week_count >= v.count then
    --         return 1
    --     end
    -- end
    local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(TreasureHuntWGData.IsOpenType[treasure_type])
    if not is_open then
        return 0
    end
    --仓库
    if self:GetStorageBtnRemind() == 1 then
        return 1
    end
    
    if self:ConvertRemind(treasure_type) then
        return 1
    end

    if self.mark_first_flag_list[treasure_type] == false then  --单抽，10抽上线只提醒一次
        for i = 1, 2 do
            if self:GetBtnRemind(treasure_type, i) == 1 then
                return 1
            end
        end
    else
        if self:GetBtnRemind(treasure_type, 3) == 1 then --50抽上线一直提醒
            return 1
        end
    end
    return 0
end

function TreasureHuntWGData:GetAllRemind()
    --符文
    if self:GetMingwenRemindNum() == 1 then
        return 1
    end

    for i = 1, 3 do 
        if self:GetCommonRemind(i) == 1 then
            return 1
        end
    end
    return 0
end

function TreasureHuntWGData:SetOpenFlag(treasure_type)
    --按钮上面
    self.mark_first_flag_list[treasure_type] = true
    RemindManager.Instance:Fire(RemindName.TreasureHunt_DianFeng)
    RemindManager.Instance:Fire(RemindName.TreasureHunt_Equip)
    RemindManager.Instance:Fire(RemindName.TreasureHunt_Zhizun)
    RemindManager.Instance:Fire(RemindName.TreasureHunt_FuWen)
    RemindManager.Instance:Fire(RemindName.TreasureHunt)
end

function TreasureHuntWGData:GetBtnRemind(treasure_type, btn_index)
    --按钮上面
    local treasure_cfg = self:GetTreasureCfgByType(treasure_type)
    local key_id = treasure_cfg[1].stuff_id
    local num = ItemWGData.Instance:GetItemNumInBagById(key_id)

    local cur_cfg = treasure_cfg[btn_index]
    local vip_mode_cfg = self:GetTreasureVipModeCfgByType(cur_cfg.mode)
    local need_num = not IsEmptyTable(vip_mode_cfg) and vip_mode_cfg.vip_stuff_num or cur_cfg.stuff_num
    if num >= need_num then
        return 1
    end

    return 0
end

function TreasureHuntWGData:GetIsGoOverDayLimit()
    return self:GetMingWenInfo().day_count >= self:GetMingwenOtherCfg().day_count_limit
end

function TreasureHuntWGData:GetViewMingWenBtnRemind(btn_index)
    if self:GetIsGoOverDayLimit()then --超过每日限制次数
        return 0
    end
    --免费的那一次
    if self:GetMingWenInfo().free_timestamp <= TimeWGCtrl.Instance:GetServerTime() and btn_index == 1  then
        return 1
    end
    --数量
    local cfg = self:GetMingwenModeCfg()
    local key_id = cfg[1].stuff_id
    local num = ItemWGData.Instance:GetItemNumInBagById(key_id)
    local cfg_need_num = cfg[btn_index].stuff_num
    local mingwen_vip_cfg = self:GetTreasureVipModeCfgByType(cfg[btn_index].mode, true)
    local need_num = not IsEmptyTable(mingwen_vip_cfg) and mingwen_vip_cfg.vip_stuff_num or cfg_need_num

    if num >= need_num then
        return 1
    end
    return 0
end

function TreasureHuntWGData:GetMingWenBtnRemind(btn_index)
    if self:GetIsGoOverDayLimit()then --超过每日限制次数
        return 0
    end
    --免费的那一次
    if self:GetMingWenInfo().free_timestamp <= TimeWGCtrl.Instance:GetServerTime() and btn_index == 1  then
        return 1
    end
    --数量
    local cfg = self:GetMingwenModeCfg()
    local key_id = cfg[1].stuff_id
    local num = ItemWGData.Instance:GetItemNumInBagById(key_id)
    if btn_index == 2 then --10抽上线一直提醒
        local cfg_need_num = cfg[btn_index].stuff_num
        local mingwen_vip_cfg = self:GetTreasureVipModeCfgByType(cfg[btn_index].mode ,true)
        local need_num = not IsEmptyTable(mingwen_vip_cfg) and mingwen_vip_cfg.vip_stuff_num or cfg_need_num

        if num >= need_num then
            return 1
        end
    elseif self.mark_first_flag_list[4] == false then --单抽上线只提醒一次
        if num >= cfg[btn_index].stuff_num then
            return 1
        end
    end
    return 0
end

function TreasureHuntWGData:GetWeeekRewardCfg(shop_mode)
    for i,v in ipairs(self.treasurehunt_week_reward_cfg) do
        if shop_mode == i then
            return v
        end
    end
end

-- function TreasureHuntWGData:GetBaodiCfgByType(xunbao_type)
--     for i,v in ipairs(self.treasurehunt_baodi_cfg) do
--         if xunbao_type == i then
--             return v
--         end
--     end
--     return {}
-- end

function TreasureHuntWGData:GetBaodiCfgByTypeAndLevel(xunbao_type, pool_level)
    return (self.treasurehunt_baodi_cfg[xunbao_type] or {})[pool_level] or {}
end

function TreasureHuntWGData:GetTreasureCfgByType(xunbao_type)
    for i,v in ipairs(self.treasurehunt_mode_cfg) do
        if xunbao_type == i then
            return v
        end
    end
end

--获取展示的13个物品list
function TreasureHuntWGData:GetZhanshiCfgByType(index)
    local cfg = {}
    if index == TabIndex.treasurehunt_equip then
        cfg = self.treasurehunt_equip_cfg
    elseif index == TabIndex.treasurehunt_dianfeng then
        cfg = self.treasurehunt_dianfeng_cfg
    elseif index == TabIndex.treasurehunt_zhizun then
        cfg = self.treasurehunt_zhizun_cfg
    end
    local item_data_list = {}
	local temp_level = 0
	local role_level = RoleWGData.Instance.role_vo.level or 0
	local role_sex = RoleWGData.Instance.role_vo.sex or 0

	if cfg then
        for k,v in ipairs(cfg) do
			if role_level > temp_level and v.role_level and role_level <= v.role_level then
				local data = {}
				data.display_index = v.display_index
				data.rare_item_id = role_sex == GameEnum.MALE and v.rare_item_id or v.rare_item_id_2
                data.show_jipin = v.show_jipin and v.show_jipin or 0
				table.insert(item_data_list, data)
			else
				temp_level = v.role_level or 0
			end
		end
    end
    return item_data_list
end

function TreasureHuntWGData:SetPersonRecord(protocol)
    self.is_first_login = protocol.is_first_login
    self.person_record_info[protocol.shop_mode] = protocol.record_list
    for k, v in pairs(self.person_record_info[protocol.shop_mode]) do
        v.shop_mode = protocol.shop_mode
    end
end

function TreasureHuntWGData:SetWorldRecord(protocol)
    self.world_record_info[protocol.shop_mode] = protocol.record_list
    for k, v in pairs(self.world_record_info[protocol.shop_mode]) do
        v.shop_mode = protocol.shop_mode
    end
end

-- 1 全服 2个人
function TreasureHuntWGData:GetRecordByMode(index, shop_mode)
    if index == 2 then
        return self.person_record_info[shop_mode] or {}
    else
        return self.world_record_info[shop_mode] or {}
    end
    return {}
end

-- 1 全服 2个人
function TreasureHuntWGData:GetRecordViewByMode(index, shop_mode)
    local list = {}
    if index == 2 then
        list = self.person_record_info[shop_mode] or {}
    else
        list = self.world_record_info[shop_mode] or {}
    end
    for k, v in pairs(list) do
        local item_id = v.item_data and v.item_data.item_id or 0
        v.is_rare = self:IsZhenXiItem(item_id) and 1 or 0
    end

    table.sort(list, SortTools.KeyUpperSorter("is_rare"))
    return list
end

function TreasureHuntWGData:IsZhenXiItem(item_id)
    return self.treasurehunt_rare_cfg[item_id] and self.treasurehunt_rare_cfg[item_id].is_rare == 1
end

function TreasureHuntWGData:GetWorldBigRecord(shop_mode)
    return self.world_big_record_info[shop_mode] or {}
end

function TreasureHuntWGData:SetWorldBigRecord(protocol)
    self.world_big_record_info[protocol.shop_mode] = protocol.record_list
    for k, v in pairs(self.world_big_record_info[protocol.shop_mode]) do
        v.shop_mode = protocol.shop_mode
    end
end

function TreasureHuntWGData:SetBaseData(protocol)
    self.base_info.chestshop_score_list = protocol.chestshop_score_list --积分
    self.base_info.week_count_list = protocol.week_count_list --周次数
    self.base_info.week_count_reward_flag_list = protocol.week_count_reward_flag_list  --领取详情
    self.base_info.times_list = protocol.times_list  --总次数
    self.base_info.total_draw_count = protocol.total_draw_count --历史所有抽奖次数
    self.base_info.pool_level = protocol.pool_level --奖池等级
end

function TreasureHuntWGData:GetBaodiTimes(treasure_type)
    if not IsEmptyTable(self.base_info.times_list) then
        local total_draw_time = self.base_info.times_list[treasure_type]  -- 抽奖总次数
        local cur_draw_level = self.base_info.pool_level[treasure_type].level or 1

        local cfg_list = self:GetBaodiCfgByTypeAndLevel(treasure_type, cur_draw_level)
        local seq = 0
        local count = 1

        for k, v in pairs(cfg_list) do
            if v.count > total_draw_time then
                seq = v.seq
                count = v.count
                break
            end
        end

        if seq > 0 then
            for k, v in pairs(cfg_list) do
                if seq - 1 == v.seq  then
                    total_draw_time = total_draw_time - v.count
                    count = count - v.count      
                    break
                end
            end
        end

        return total_draw_time, count
    end

    return 0, 1

    -- if not IsEmptyTable(self.base_info.times_list) then
    --     local cur = self.base_info.times_list[treasure_type]  -- 抽奖总次数
    --     local cfg = TreasureHuntWGData.Instance:GetBaodiCfgByType(treasure_type)
    --     local seq = 0
    --     local count = 1
    --     for k, v in pairs(cfg) do
    --         if v.count > cur then
    --             seq = v.seq
    --             count = v.count
    --             break
    --         end
    --     end
    --     if seq > 0 then
    --         for k, v in pairs(cfg) do
    --             if seq - 1 == v.seq  then
    --                 cur = cur - v.count
    --                 count = count - v.count      
    --                 break
    --             end
    --         end
    --     end
    --     return cur, count
    -- end
    -- return 0, 1
end

-- 获取奖池当前等级的的config
-- function TreasureHuntWGData:GetBaodiConfig(treasure_type, need_seq)
--     if not IsEmptyTable(self.base_info.pool_level) then
--         local pool_level = need_seq and need_seq or self.base_info.pool_level[treasure_type]
--         local cfg = TreasureHuntWGData.Instance:GetBaodiCfgByType(treasure_type)
--         local seq = 0
--         local count = 1
--         for k, v in pairs(cfg) do
--             if v.seq == pool_level then
--                 return v
--             end
--         end
--     end
--     return nil
-- end


function TreasureHuntWGData:GetBaodiTimesAndCfg(treasure_type)
    if not IsEmptyTable(self.base_info.times_list) then
        local cur = self.base_info.times_list[treasure_type]
        local cur_draw_level = self.base_info.pool_level[treasure_type].level or 1

        -- local cfg = TreasureHuntWGData.Instance:GetBaodiCfgByType(treasure_type)
        local cfg_list = self:GetBaodiCfgByTypeAndLevel(treasure_type, cur_draw_level)
        local seq = 0
        local count = 1
        for k, v in pairs(cfg_list) do
            if v.count > cur then
                seq = v.seq
                return v.count - cur, v
            end
        end
    end

    return 0, {}
end

function TreasureHuntWGData:GetTreasureMaxCfg(treasure_type)
    -- if not IsEmptyTable(self.base_info.times_list) then
    --     local cur = self.base_info.times_list[treasure_type]
    --     local cfg = TreasureHuntWGData.Instance:GetBaodiCfgByType(treasure_type)
    --     for k, v in pairs(cfg) do
    --         if v.seq +1  == #cfg then
    --             return v
    --         end
    --     end
    -- end

    local cfg_list = self:GetXunBaoPoolLevelCfgBuType(treasure_type)

    if not IsEmptyTable(cfg_list) then
        return cfg_list[#cfg_list]
    end

    return {}
end

function TreasureHuntWGData:GetTreasureTotalTimes(treasure_type)
    if not IsEmptyTable(self.base_info.total_draw_count) then
        local cur = self.base_info.total_draw_count[treasure_type]
        if nil ~= cur then
            return cur
        end
        return 0
    end
    return 0
end

function TreasureHuntWGData:GetBaseData()
    return self.base_info
end

function TreasureHuntWGData:SetTreasureHuntIsFirst()
    for i = 1, 4 do
        if not self.treasure_hunt_tab[i] then 
            self.treasure_hunt_tab[i] = {}
        end

        self.treasure_hunt_tab[i].is_first = 0
    end
end

function TreasureHuntWGData:SetTreasureHuntTab(treasure_type)
    if treasure_type == 4 then
        if self.treasure_hunt_tab[treasure_type] then 
            self.treasure_hunt_tab[treasure_type].is_first = 1
        end
    else
        for i = 1, 3 do --兑换成通用的
            if self.treasure_hunt_tab[i] then 
                self.treasure_hunt_tab[i].is_first = 1
            end
        end
    end
end

--兑换的红点，上线时检测
function TreasureHuntWGData:ConvertRemind(treasure_type)
    local treasure_type = treasure_type 
    local data = self:GetBaseData()
    local sui_pian = MingWenWGData.Instance:GetMingWenSuiPian()
    local num = 0
    local info = {}
    local sign = true

    if treasure_type then --通用的三种
        local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(TreasureHuntWGData.IsOpenType[treasure_type])
        num = data.chestshop_score_list[treasure_type] or 0
        if self.treasurehunt_other_cfg.common_exchange_score_red <= num then
            if self.treasure_hunt_tab[treasure_type].is_first == 1 or not is_open then
               sign = false
            end
            return sign
        end
    else
        num = MingWenWGData.Instance:GetMingWenSuiPian()
        if self.treasurehunt_other_cfg.mingwen_exchange_score_red <= num then
            local mw_open = FunOpen.Instance:GetFunIsOpenedByTabName(TreasureHuntWGData.IsOpenType[4])
            if self.treasure_hunt_tab[4].is_first == 1 or not mw_open then
                return false
            end

            return true
        end
    end

    return false
end

function TreasureHuntWGData:GetWeekRewardInfo(shop_type)
    local info = self:GetBaseData()
    local cfg = self:GetWeeekRewardCfg(shop_type)
    local reward_info = {}
    for i, v in pairs(cfg) do
        local data = {}
        local item_info = v.reward_item[0]
        data.reward_item_id = item_info.item_id
        data.num =  item_info.num
        data.count = v.count
        data.is_bind = item_info.is_bind
        if info.week_count_reward_flag_list[shop_type] then
            data.is_get = info.week_count_reward_flag_list[shop_type][33 - i] or 0
        else
            data.is_get = 0
        end
        data.shop_type = shop_type
        data.total_week_count = info.week_count_list[shop_type] or 0 
        table.insert(reward_info, data)
    end
    return reward_info
end

-- enable_anim为false表示跳过动画
function TreasureHuntWGData:SetAnimToggleData(xunbao_type, enable_anim)
	self.anim_toggle_enable_list[xunbao_type] = enable_anim
end

-- 返回false表示跳过动画
function TreasureHuntWGData:GetAnimToggleData(xunbao_type)
	if self.anim_toggle_enable_list[xunbao_type] == nil then
		return false 												-- 默认激活动画
	end
	return self.anim_toggle_enable_list[xunbao_type]
end

function TreasureHuntWGData:SetResultData(protocol)
    self.result_data_list = protocol.hit_item_list
    self.baodi_list = protocol.baodi_list
end

function TreasureHuntWGData:GetResultInfoList()
    local baodi_list = self:GetBaodiList()
    if not IsEmptyTable(baodi_list) and not IsEmptyTable(self.result_data_list) then
        for k, v in pairs(self.result_data_list) do
            for k1, v1 in pairs(baodi_list) do
                if v.item_id == v1.item_id then
                    table.remove(self.result_data_list, k)
                end
            end
        end
    end

    return self.result_data_list or {}
end

function TreasureHuntWGData:GetBaodiList()
    local data_list = {}
    if not IsEmptyTable(self.baodi_list) then
        for k,v in pairs(self.baodi_list) do
            if v.item_id ~= 0 then
                data_list[#data_list + 1] = v
            end
        end
    end
    return data_list
end

-- 0 装备 1 巅峰 2 至尊 4 铭纹
function TreasureHuntWGData:GetConvertShopCfgByType(shop_type)
    for k ,v in pairs(self.treasurehunt_convertshop_cfg) do
        if shop_type == k then
            return v
        end
    end
    return {}
end

function TreasureHuntWGData:SetStorageInfo(protocol)
    self.storage_info = protocol
end

function TreasureHuntWGData:GetStorageInfo()
    return self.storage_info or {}
end

function TreasureHuntWGData:SetCurBtnIndex(index, is_mingwen)
    self.cur_btn_index = index
    self.is_mingwen = is_mingwen
end

function TreasureHuntWGData:GetProbabilityInfo(type_1)
    if type_1 == TreasureHuntView.GetTypeByShowIndex(TabIndex.treasurehunt_thunder) then
        local list = TreasureHuntThunderWGData.Instance:GetGaiLvInfoList(1)
        return list
    else
        for k, v in pairs(self.treasurehunt_probability_cfg) do
            if type_1 == k then
                return v
            end
        end  
    end
end

function TreasureHuntWGData:GetStorageBtnRemind()
    local storage_info = TreasureHuntWGData.Instance:GetStorageInfo()
    local storage_grid_list = storage_info.item_list or {}
    if #storage_grid_list > 0 then
        return 1
    else
        return 0
    end
end

function TreasureHuntWGData:SetCurShowIndex(index)
    self.cur_show_index = index
end

function TreasureHuntWGData:GetCurShowType()
    local index = self.cur_show_index
    return TreasureHuntView.GetTypeByShowIndex(index)
end

function TreasureHuntWGData:GetMingwenItems()
    local pass_lv =  FuBenPanelWGData.Instance:GetPassLevel() + 1
    local item_list = {}
    for k ,v in pairs(self.mingwen_items_cfg) do
        if v.is_show == 1 then
            local data = {}
            local cfg = MingWenWGData.Instance:GetMingWenBaseCfgByID(v.posy_id)
            data.item_id  = v.posy_id
            data.pass_level  = v.tianxiange_fb_pass_level
            if cfg then
                data.type  = cfg.type
                data.quality  = cfg.quality
            end
            table.insert(item_list, data)
        end
    end

    table.sort(item_list, SortTools.KeyLowerSorter("type", "quality"))
    local data_list = ListToMapList(item_list, "type")
    local data_list2 = {}
    for k, v in pairs(data_list) do
        local data = {}
        data.item_list = v
        data.pass_level = v[1].pass_level
        table.insert(data_list2, data)
    end

    table.sort(data_list2, SortTools.KeyLowerSorter("pass_level"))
    return data_list2
end

function TreasureHuntWGData:GetMingwenWeek()
    local info_list = {}
    local flag_list = self.mingwen_base_info.week_count_reward_flag
    local cur_count = self.mingwen_base_info.week_count
    for k,v in pairs(self.mingwen_weekreward_cfg) do
        local info = {}
        info.count = v.count
        info.item = v.reward_item[0]
        info.total_week_count = cur_count
        if not IsEmptyTable(flag_list) then
            info.is_get = flag_list[32 - k] or 0
        else
            info.is_get = 0
        end
        info.index = v.index
        info_list[k+1] = info
        --table.insert(info_list,info)
    end
    return info_list
end

function TreasureHuntWGData:GetMingwenModeCfg()
    return self.mingwen_mode_cfg
end

function TreasureHuntWGData:GetMingwenOtherCfg()
    return self.mingwen_other_cfg[1]
end

function TreasureHuntWGData:SetMingWenInfo(protocol)
    self.mingwen_base_info.day_count = protocol.day_count
    self.mingwen_base_info.week_count = protocol.week_count
    self.mingwen_base_info.week_count_reward_flag = protocol.week_count_reward_flag
    self.mingwen_base_info.free_timestamp = protocol.free_timestamp
    if self.mingwen_base_info.free_timestamp > TimeWGCtrl.Instance:GetServerTime() then
        if self.refresh_event == nil then
            self.refresh_event = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.RefreshRemainTime,self),1)
        end
    end
end

--当前抽到第几阶段
function TreasureHuntWGData:GetMingWenWeekCurIndex()
    local total = #self.mingwen_weekreward_cfg
    if not IsEmptyTable(self.mingwen_weekreward_cfg) then
        for i = total, 0, - 1 do
            if self.mingwen_weekreward_cfg[i].count <= self.mingwen_base_info.week_count then
                return i + 1
            end
        end
    end
    return 0
end

function TreasureHuntWGData:SetOldWeekIndex(value)
    self.old_week_index = value
end

function TreasureHuntWGData:GetOldWeekIndex()
    return  self.old_week_index
end

function TreasureHuntWGData:GetCurProgress(count_value)  --进度条
    local cur_progress = 0
    local cfg = self:GetMingwenWeek()
    local cur_count_value = tonumber(count_value)
    if next(cfg) == nil or cur_count_value == nil then
        return cur_progress
    end
    local progress_list = {0.1, 0.33, 0.55, 0.78, 1}           --对应的进度条值

    for k, v in pairs(progress_list) do
        local seq = k - 1
        local length = #progress_list
        local cur_need = cfg[seq] and cfg[seq].count or 0
        local next_need = cfg[seq + 1] and cfg[seq + 1].count or cfg[#cfg].count
        local cur_value = progress_list[seq] and progress_list[seq] or 0
        local next_value = progress_list[seq + 1] and progress_list[seq + 1] or progress_list[length]
        if cur_count_value > cur_need and cur_count_value <= next_need then
            cur_progress = (cur_count_value - cur_need) * (next_value - cur_value) / (next_need - cur_need) + cur_value
            break
        elseif cur_count_value > cfg[#cfg].count then
            cur_progress = progress_list[length]
            break
        end
    end
    return cur_progress
end

function TreasureHuntWGData:RefreshRemainTime()
    self.next_free_time_interal = self.mingwen_base_info.free_timestamp - TimeWGCtrl.Instance:GetServerTime()
    if self.next_free_time_interal <= 0 then
        RemindManager.Instance:Fire(RemindName.TreasureHunt_FuWen)
        RemindManager.Instance:Fire(RemindName.TreasureHunt)
        GlobalTimerQuest:CancelQuest(self.refresh_event)
        self.refresh_event = nil
    end
end

function TreasureHuntWGData:GetNextFreeTime()
    return self.next_free_time_interal or 0
end

function TreasureHuntWGData:GetMingWenInfo()
    return self.mingwen_base_info
end

function TreasureHuntWGData:SetMingWenResultInfo(protocol)
    self.get_mingwen_score = protocol.score
    self.mingwen_result = protocol.item_list
end

function TreasureHuntWGData:GetMingWenResultInfo()
    return self.mingwen_result
end

function TreasureHuntWGData:GetMingWenResultScore()
    return self.get_mingwen_score or 0
end

function TreasureHuntWGData:SetMingWenPersonRecord(protocol)
   self.mingwen_person_record = protocol.record_list
end

function TreasureHuntWGData:GetMingWenRecordByType(idx)
    if idx == 1 then
        return self.mingwen_world_record or {}
    elseif idx == 2 then
        return self.mingwen_person_record or {}
    end
end

 -- 1 全服 2个人
function TreasureHuntWGData:GetMingWenRecordViewByType(index)
    local list = {}
    if index == 1 then
        list = self.mingwen_world_record or {}
        for k, v in pairs(list) do
            local item_id =  v.item_id or 0
            v.is_rare = self:IsZhenXiItem(item_id) and 1 or 0
        end
        table.sort(list, SortTools.KeyUpperSorter("is_rare"))
    else
        list = self.mingwen_person_record or {}
        for k, v in pairs(list) do
            local item_id = v.item_id or 0
            v.is_rare = self:IsZhenXiItem(item_id) and 1 or 0
        end
        table.sort(list, SortTools.KeyUpperSorters("is_rare", "timestamp"))
    end

    return list
end

function TreasureHuntWGData:SetMingWenWorldRecord(protocol)
    self.mingwen_world_record = protocol.world_record_list
end

function TreasureHuntWGData:SetIsMingwen(is_mingwen)
    self.cur_is_mingwen_mode = is_mingwen
end

function TreasureHuntWGData:GetIsMingwen()
    return self.cur_is_mingwen_mode
end

function TreasureHuntWGData:GetHuntShowModelCfg(treasure_type)
    local pool_level = self.base_info.pool_level[treasure_type].level or 1
    if self.treasurehunt_show_model_cfg and self.treasurehunt_show_model_cfg[treasure_type] then
        local show_cfg_list = self.treasurehunt_show_model_cfg[treasure_type]

        for key, show_cfg in pairs(show_cfg_list) do
            if show_cfg.seq == pool_level then
                if (not self.model_data_list_cfg[treasure_type]) or (not self.model_data_list_cfg[treasure_type][pool_level]) then
                    if not self.model_data_list_cfg[treasure_type] then
                        self.model_data_list_cfg[treasure_type] = {}
                    end
                    
                    local model_data_list = {}
                    for i =1,3 do
                        if show_cfg["show_model"..i] and show_cfg["show_model"..i] ~= "" then
                            model_data_list[i] = Split(show_cfg["show_model"..i],"|")
                        end
                    end
                    self.model_data_list_cfg[treasure_type][pool_level] = model_data_list
                    return self.model_data_list_cfg[treasure_type][pool_level]
                else
                    return self.model_data_list_cfg[treasure_type][pool_level]
                end
            end
        end
    end
    return {}

    -- local pool_level = need_seq and need_seq or self.base_info.pool_level[treasure_type]
    -- if self.treasurehunt_show_model_cfg and self.treasurehunt_show_model_cfg[treasure_type] then
    --     local show_cfg_list = self.treasurehunt_show_model_cfg[treasure_type]
    --     for key, show_cfg in pairs(show_cfg_list) do
    --         if show_cfg.seq == pool_level then
    --             if (not self.model_data_list_cfg[treasure_type]) or (not self.model_data_list_cfg[treasure_type][pool_level]) then
    --                 if not self.model_data_list_cfg[treasure_type] then
    --                     self.model_data_list_cfg[treasure_type] = {}
    --                 end
                    
    --                 local model_data_list = {}
    --                 for i =1,3 do
    --                     if show_cfg["show_model"..i] and show_cfg["show_model"..i] ~= "" then
    --                         model_data_list[i] = Split(show_cfg["show_model"..i],"|")
    --                     end
    --                 end
    --                 self.model_data_list_cfg[treasure_type][pool_level] = model_data_list
    --                 return self.model_data_list_cfg[treasure_type][pool_level]
    --             else
    --                 return self.model_data_list_cfg[treasure_type][pool_level]
    --             end
    --         end
    --     end
    -- end
    -- return {}
end

function TreasureHuntWGData:GetHuntCfg(treasure_type)
    if not treasure_type then
        return nil
    end

    local pool_level = (((self.base_info or {}).pool_level or {})[treasure_type] or {}).level or 1
    local list = (self.treasurehunt_show_model_cfg or {})[treasure_type] or {}

    for k, v in pairs(list) do
        if v.seq == pool_level then
            return v
        end
    end
    
    return (self.treasurehunt_show_model_cfg or {})[1]

    -- local pool_level = need_seq and need_seq or self.base_info.pool_level[treasure_type]
    -- for k, v in pairs(self.treasurehunt_show_model_cfg[treasure_type]) do
    --     if v.seq == pool_level then
    --         return v
    --     end
    -- end

    -- return self.treasurehunt_show_model_cfg[treasure_type][1]
end

function TreasureHuntWGData:GetShowModelDataByType(treasure_type)
    return self.best_show_cfg_2_cfg[treasure_type]
end

function TreasureHuntWGData:GetHuntBossDataListByType(treasure_type)
    return TreasureBossWGData.Instance:GetHuntBossDataListByType(treasure_type)
end

function TreasureHuntWGData:GetHuntTotalCountByType(treasure_type)
    if self.base_info and self.base_info.total_draw_count then
        return self.base_info.total_draw_count[treasure_type] or 0
    end
    return 0
end

function TreasureHuntWGData:GetPoolLevelByType(treasure_type)
    return (((self.base_info or {}).pool_level or {})[treasure_type] or {}).level or 1

    -- if self.base_info and self.base_info.pool_level then
    --     return self.base_info.pool_level[treasure_type] or 0
    -- end
    -- return 0
end

function TreasureHuntWGData:GetBestOpenTreasureBoss() --开启的最好的寻宝boss
    local role_level = GameVoManager.Instance:GetMainRoleVo().level or 0
    local temp_boss = 0
    local temp_seq = -1
    if self.treasurehunt_seq_cfg then
        for k,v in pairs(self.treasurehunt_seq_cfg) do
            if role_level >= v.enter_level then
                if self:GetHuntTotalCountByType(v.chestshop_type) >= v.chestshop_num then
                    if v.seq > temp_seq then
                        temp_seq = v.seq
                        temp_boss = v.boss_id
                    end
                end
            end
        end
    end
    return temp_boss,temp_seq
end

function TreasureHuntWGData:SetOpenSelectEffect(value)
    self.flush_effect_show = value
end


function TreasureHuntWGData:GetOpenSelectEffect()
    return self.flush_effect_show
end

function TreasureHuntWGData:CacheSubName(name_string,value)
    if not self.cache_sub_name then
        self.cache_sub_name = {}
    end
    self.cache_sub_name[name_string] = value
end

function TreasureHuntWGData:GetCacheSubName(name_string)
    if self.cache_sub_name and self.cache_sub_name[name_string] then
        return true,self.cache_sub_name[name_string]
    end
    return false,nil
end

function TreasureHuntWGData:ClearCacheSubName()
    self.cache_sub_name = {}
end

function TreasureHuntWGData:HaveTreasureBossCanKill() --还有可打的寻宝boss活着
    local role_level = GameVoManager.Instance:GetMainRoleVo().level or 0
    local temp_boss = 0
    local temp_seq = -1
    if self.treasurehunt_seq_cfg then
        for k,v in pairs(self.treasurehunt_seq_cfg) do
            if role_level >= v.enter_level then
                if self:GetHuntTotalCountByType(v.chestshop_type) >= v.chestshop_num then
                    if v.seq > temp_seq then
                        local boss_info = TreasureBossWGData.Instance:GetTreasureBossInfoByBossID(v.boss_id)
                        if (boss_info and boss_info.status == 1) or IsEmptyTable(boss_info) then
                            return true
                        end
                    end
                end
            end
        end
    end
    return false
end

function TreasureHuntWGData:HaveTreasureBossCountEnough() --有次数够的
    local role_level = GameVoManager.Instance:GetMainRoleVo().level or 0
    local temp_boss = 0
    local temp_seq = -1
    if self.treasurehunt_seq_cfg then
        for k,v in pairs(self.treasurehunt_seq_cfg) do
            if role_level >= v.enter_level then
                if self:GetHuntTotalCountByType(v.chestshop_type) >= v.chestshop_num then
                   return true
                end
            end
        end
    end
    return false
end

-- 获取当前vip特权配置
function TreasureHuntWGData:GetTreasureVipModeCfgByType(mode, is_mingwen)
    local vip_mode_data = {}
    local data_list
    local is_mingwen = is_mingwen or false
    if is_mingwen then
        data_list = self.mingwen_vip_mode_cfg[mode]
    else
        data_list = self.treasurehunt_vip_mode_cfg[mode]
    end

    if IsEmptyTable(data_list) then
        return vip_mode_data
    end

    table.sort(data_list, function (a, b)
		return a.need_vip_level < b.need_vip_level
	end)

    local vip_level = VipWGData.Instance:GetVipLevel()

    for k, v in pairs(data_list) do
        if vip_level >= v.need_vip_level then
            vip_mode_data = v
        elseif vip_level < v.need_vip_level then
            break
        end
    end

    if IsEmptyTable(vip_mode_data) then
        vip_mode_data = data_list[#data_list]
    end

    return vip_mode_data
end

function TreasureHuntWGData:GetXunBaoPoolLevelCfgBuType(xunbao_type)
    return self.treasurehunt_pool_level_cfg[xunbao_type] or {}
end

function TreasureHuntWGData:GetXunBaoPoolLevelCfg(xunbao_type, pool_level)
    return (self.treasurehunt_pool_level_cfg[xunbao_type] or {})[pool_level] or {}
end

function TreasureHuntWGData:GetXunBaoPoolLevelCountCfg(xunbao_type, pool_level)
    local start_count, end_count = 0, 0
    local cfg = self:GetXunBaoPoolLevelCfg(xunbao_type, pool_level)
    end_count = cfg and cfg.uplevel_count or 0
    local start_cfg = self:GetXunBaoPoolLevelCfg(xunbao_type, pool_level - 1)
    start_count = start_cfg and start_cfg.uplevel_count or 0

    return start_count, end_count
end

function TreasureHuntWGData:SetSkipSpineStatusByType(draw_type, is_skip)
    is_skip = is_skip or false
    self.skip_spine_cache[draw_type] = is_skip
end

function TreasureHuntWGData:GetSkipSpineStatusByType(draw_type)
    return self.skip_spine_cache[draw_type] or false
end