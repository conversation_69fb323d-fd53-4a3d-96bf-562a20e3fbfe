SwornRechargeView = SwornRechargeView or BaseClass(SafeBaseView)
function SwornRechargeView:__init()
	self:SetMaskBg()
	self.view_style = ViewStyle.Half
	self.is_safe_area_adapter = true

	self:AddViewResource(0, "uis/view/sworn_recharge_prefab", "layout_sworn_recharge_view")
end

function SwornRechargeView:OpenCallBack()
	SwornRechargeWGCtrl.Instance:SendCrossCapRankReq(CROSS_CAPABILITY_RANK_OPERATE_TYPE.INFO)
end

function SwornRechargeView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("sworn_recharge_down") then
		CountDownManager.Instance:RemoveCountDown("sworn_recharge_down")
	end

	if self.rank_reward_list then
		self.rank_reward_list:DeleteMe()
		self.rank_reward_list = nil
	end

	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end

	if self.player_item_list then
		for key, value in pairs(self.player_item_list) do
			value:DeleteMe()
			value = nil
		end
		self.player_item_list = nil
	end
end

function SwornRechargeView:LoadCallBack()
	self.panel_idx = 0

	if not self.rank_reward_list then
		self.rank_reward_list = AsyncListView.New(SwornRechargeRewardItem, self.node_list.rank_reward_list)
	end

	if not self.rank_list then
		self.rank_list = AsyncListView.New(SwornRechargeRankItem, self.node_list.rank_list)
	end

	if not self.player_item_list then
		self.player_item_list = {}
		for idx = 1, 5 do
			self.player_item_list[idx] = SwornRechargePlayerItem.New(self.node_list["Node_PalyerItem" .. idx])
		end
	end

	XUI.AddClickEventListener(self.node_list.button_gotoSworn, BindTool.Bind(self.OpenSworn, self))
	XUI.AddClickEventListener(self.node_list.button_team, BindTool.Bind(self.OpenTeamTips, self))
	XUI.AddClickEventListener(self.node_list.rule_tips_btn, BindTool.Bind(self.OpenRuleTip, self))
	XUI.AddClickEventListener(self.node_list.button_Look, BindTool.Bind(self.ChangePanel, self))

	self:LoginTimeCountDown()
	self:ChangePanel()
end

function SwornRechargeView:OnFlush()
	self:SetTeam()
end

function SwornRechargeView:OpenTeamTips()
	SwornRechargeWGCtrl.Instance:OpenTeamTipsView()
end

function SwornRechargeView:OpenSworn()
	SwornRechargeWGCtrl.Instance:OpenSwornView()
end

function SwornRechargeView:OpenRuleTip()
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetContent(Language.SwornRecharge.RechargeRankRuleInfo, Language.SwornRecharge.RechargeRankRuleTitle)
	end
end

function SwornRechargeView:ChangePanel()
	self.panel_idx = self.panel_idx + 1
	if self.panel_idx > 2 then
		self.panel_idx = 1
	end

	--展示奖励.
	self.node_list.Node_Panel1:SetActive(self.panel_idx == 1)
	self.node_list.image_LookRank:SetActive(self.panel_idx == 1)
	self.node_list.rank_reward_list:SetActive(self.panel_idx == 1)
	--展示排名.
	self.node_list.Node_Panel2:SetActive(self.panel_idx == 2)
	self.node_list.image_LookAward:SetActive(self.panel_idx == 2)
	self.node_list.rank_list:SetActive(self.panel_idx == 2)

	if self.panel_idx == 1 then
		--展示奖励.
		self:FlushRankRewardPanel()
	elseif self.panel_idx == 2 then
		--展示排名.
		self:FlushRankPanel()
		self:SetPanelText()
	end
end

function SwornRechargeView:FlushRankRewardPanel()
	local reward_list = SwornRechargeWGData.Instance:GetActRewardCfg()
	if not IsEmptyTable(reward_list) then
		self.rank_reward_list:SetDataList(reward_list)
	end
end

function SwornRechargeView:FlushRankPanel()
	local data_list = SwornRechargeWGData.Instance:GetAllRank()
	if not IsEmptyTable(data_list) then
		self.rank_list:SetDataList(data_list)
	end
end

function SwornRechargeView:SetPanelText()
	local reward_cfg = SwornRechargeWGData.Instance:GetActRewardCfg()
	if IsEmptyTable(reward_cfg) then
		return
	end

	local my_rank_value = SwornRechargeWGData.Instance:GetMyRankValue()

	local text_rankValue = nil
	local rank = SwornRechargeWGData.Instance:GetMyRank()
	if rank == 1 then
		text_rankValue = Language.SwornRecharge.TopRankText
	else
		local next_grade, need_money, next_rank = SwornRechargeWGData.Instance:GetNeedMoneyAndNextRank()

		--已达到了第一档.
		if next_grade <= 0 then
			text_rankValue = string.format(Language.SwornRecharge.AllUpRankText)
		else
			local need_price = RoleWGData.GetPayMoneyStr(need_money)
			text_rankValue = string.format(Language.SwornRecharge.UpRankText, next_rank, need_price)
		end
	end

	self.node_list.text_rankValue.text.text = text_rankValue

	local price = RoleWGData.GetPayMoneyStr(my_rank_value)
	self.node_list.text_hasRechargeValue.text.text = string.format(Language.SwornRecharge.LeiChongNumText, price)
end

function SwornRechargeView:SetTeam()
	local data = SwornRechargeWGData.Instance:GetAllRank()
	if IsEmptyTable(data) then
		return
	end
	if not data[1] then
		return
	end
	local team_data = data[1].team_data
	if IsEmptyTable(team_data) then
		return
	end

	for key, value in pairs(self.player_item_list) do
		if team_data[key] then
			team_data[key].is_show_top_team = true
			value:SetData(team_data[key])
		end
	end
end

------------------------------------活动时间倒计时
function SwornRechargeView:LoginTimeCountDown()
	local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE
		.CROSS_CHANNEL_ACTIVITY_TYPE_SWORN_RECHARGE_RANK)
	if activity_data ~= nil then
		local invalid_time = activity_data.end_time
		if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
			self.node_list.text_remainTime.text.text = TimeUtil.FormatSecondDHM6(invalid_time -
				TimeWGCtrl.Instance:GetServerTime())
			CountDownManager.Instance:AddCountDown("sworn_recharge_down", BindTool.Bind1(self.UpdateCountDown, self),
				BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
		end
	end
end

function SwornRechargeView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.login_act_date = TimeUtil.FormatUnixTime2Date()
		self.node_list.text_remainTime.text.text = TimeUtil.FormatSecondDHM6(valid_time)
	end
end

function SwornRechargeView:OnComplete()
	self.node_list.text_remainTime.text.text = ""
	self:Close()
end

---------------------------------------SwornRechargePlayerItem----------------------------------
SwornRechargePlayerItem = SwornRechargePlayerItem or BaseClass(BaseRender)

function SwornRechargePlayerItem:LoadCallBack()
	if not self.role_head then
		self.role_head = BaseHeadCell.New(self.node_list.Node_Player)
	end

	if not self.role_avatar then
		self.role_avatar = RoleHeadCell.New(false)
		self.role_avatar:AddCustomMenu(Language.Menu.ShowInfo, Language.Menu.AddEnemy)
	end

	XUI.AddClickEventListener(self.node_list.head_btn, BindTool.Bind(self.OnClickMainRoleHead, self))
end

function SwornRechargePlayerItem:ReleaseCallBack()
	if self.role_head then
		self.role_head:DeleteMe()
		self.role_head = nil
	end

	if self.role_avatar then
		self.role_avatar:DeleteMe()
		self.role_avatar = nil
	end
end

function SwornRechargePlayerItem:OnFlush()
	if not self.data then
		return
	end

	self:SetName()
	self:SetRechargeValue()
	self:SetVip()
	self:SetHeadIcon()
end

function SwornRechargePlayerItem:SetName()
	local text = nil
	if self.data.uuid.temp_low == 0 then
		--无成员时.
		text = Language.SwornRecharge.NoMemberText
	else
		text = self.data.name
	end

	self.node_list.text_playerName.text.text = text
end

function SwornRechargePlayerItem:SetRechargeValue()
	local text = nil
	local is_show_leichong = false

	if self.data.uuid.temp_low == 0 then
		--无成员时.
		text = string.format(Language.SwornRecharge.RechargeNumText, Language.SwornRecharge.NoText)
	else
		if self.data.is_show_top_team then
			local rank = SwornRechargeWGData.Instance:GetMyRank()
			if rank == 1 then
				is_show_leichong = true
			end

			if is_show_leichong then
				text = string.format(Language.SwornRecharge.RechargeNumText, self.data.chongzhi_num)
			else
				text = string.format(Language.SwornRecharge.RechargeNumText, Language.SwornRecharge.BaoMi)
			end
		else
			text = string.format(Language.SwornRecharge.RechargeNumText, self.data.chongzhi_num)
		end
	end

	self.node_list.text_RechargeValue.text.text = text
end

function SwornRechargePlayerItem:SetVip()
	--是否显示VIP
	local is_show_vip = not SettingWGData.Instance:IsHideMainRoleVipLv()

	if self.data.uuid.temp_low == 0 then
		--无成员时.
		is_show_vip = false
	else
		is_show_vip = not self.data.is_hide_vip == 0
		self.node_list.vip_num.text.text = self.data.vip_level
	end

	self.node_list.img_vip:SetActive(is_show_vip)
end

function SwornRechargePlayerItem:SetHeadIcon()
	self.role_head:SetActive(self.data.uuid.temp_low ~= 0)
	self.node_list.head_btn:SetActive(self.data.uuid.temp_low ~= 0)

	if self.data.uuid.temp_low ~= 0 then
		local data = {}
		data.role_id = self.data.uuid.temp_low
		data.prof = self.data.prof
		data.sex = self.data.sex
		self.role_head:SetImgBg(true)
		self.role_head:SetData(data)
	end

	if not self.data.is_show_top_team then
		self.node_list.head_btn:SetActive(false)
	end
end

function SwornRechargePlayerItem:OnClickMainRoleHead()
	--无成员时.
	if self.data.uuid.temp_low == 0 then
		return
	end

	--判断是否是自己.
	if self.data.uuid == RoleWGData.Instance:GetUUid() then
		ViewManager.Instance:Open(GuideModuleName.RoleView, TabIndex.role_intro)
	else
		local role_info = {
			role_id =self.data.uuid.temp_low ,
			role_name = self.data.name,
			prof = self.data.prof,
			sex = self.data.sex,
			is_online = true,
			server_id = self.data.usid.temp_low,
		}
		self.role_avatar:SetRoleInfo(role_info)
		self.role_avatar:OpenMenu(nil, nil, nil, MASK_BG_ALPHA_TYPE.Normal)
	end
end

---------------------------------------SwornRechargeRewardItem----------------------------------
SwornRechargeRewardItem = SwornRechargeRewardItem or BaseClass(BaseRender)

function SwornRechargeRewardItem:LoadCallBack()
	if not self.rank_list then
		self.rank_list = AsyncListView.New(ItemCell, self.node_list.item_list)
		self.rank_list:SetStartZeroIndex(true)
	end
end

function SwornRechargeRewardItem:ReleaseCallBack()
	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end
end

function SwornRechargeRewardItem:OnFlush()
	if not self.data then
		return
	end

	local desc = ""
	if self.data.min_rank == self.data.max_rank then
		desc = string.format(Language.SwornRecharge.RechargeRankTitle2, self.data.min_rank)
	else
		desc = string.format(Language.SwornRecharge.RechargeRankTitle3, self.data.min_rank, self.data.max_rank)
	end

	local price = RoleWGData.GetPayMoneyStr(self.data.reach_value)
	self.node_list.text_rechargeTitle.text.text = string.format(Language.SwornRecharge.RewardTitleText, desc, price)

	self.rank_list:SetDataList(self.data.reward_item)
end

---------------------------------SwornRechargeRankItem-----------------------------
SwornRechargeRankItem = SwornRechargeRankItem or BaseClass(BaseRender)

function SwornRechargeRankItem:ReleaseCallBack()
	if self.role_head then
		self.role_head:DeleteMe()
		self.role_head = nil
	end
end

function SwornRechargeRankItem:OnFlush()
	if not self.data then
		return
	end

	--前三名展示排名旗帜.
	if self.data.rank <= 3 then
		self.node_list.text_rank:SetActive(false)
		self.node_list.image_rankValue:SetActive(true)

		local bundle, asset = ResPath.GetSwornRechargeImg("a2_kfjy_rank_" .. self.data.rank)
		self.node_list.image_rankValue.image:LoadSprite(bundle, asset)
	else
		self.node_list.image_rankValue:SetActive(false)
		self.node_list.text_rank:SetActive(true)

		self.node_list.text_rank.text.text = string.format(Language.SwornRecharge.RechargeRankTitle1,
			NumberToChinaNumber(self.data.rank))
	end

	--是否显示VIP
	local is_show_vip = not SettingWGData.Instance:IsHideMainRoleVipLv()

	if not self.role_head then
		self.role_head = BaseHeadCell.New(self.node_list["Node_Player"])
	end

	--空榜,累充0.
	if self.data.rank_value == 0 then
		self.node_list.text_PlayerName.text.text = Language.SwornRecharge.NoTeamText
		self.node_list.text_RechargeValue.text.text = Language.SwornRecharge.NoText

		self.role_head:SetActive(false)

		is_show_vip = false
	else
		self.node_list.text_PlayerName.text.text = string.format(Language.SwornRecharge.RechargeTeamNameText,
			self.data.team_data[1].name)

		--判断是否是自己的队伍.
		local my_rank = SwornRechargeWGData.Instance:GetMyRank()

		if self.data.rank == my_rank then
			self.node_list.text_RechargeValue.text.text = self.data.rank_value
		else
			self.node_list.text_RechargeValue.text.text = Language.SwornRecharge.BaoMi
		end

		self.role_head:SetActive(true)

		local data = {}
		data.role_id = self.data.team_data[1].uuid.temp_low
		data.prof = self.data.team_data[1].prof
		data.sex = self.data.team_data[1].sex
		self.role_head:SetImgBg(true)
		self.role_head:SetData(data)

		is_show_vip = not self.data.team_data[1].is_hide_vip == 0
	end

	self.node_list["img_vip"]:SetActive(is_show_vip)
end
