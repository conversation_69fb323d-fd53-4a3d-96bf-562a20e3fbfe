
BossAssistWGData = BossAssistWGData or BaseClass()

BossAssistWGData.TeamOperate = {
	Invite = 1,
	Apply = 2,
	Merge = 3,
}

function BossAssistWGData:__init()
	if BossAssistWGData.Instance then
		error("[BossAssistWGData] Attempt to create singleton twice!")
		return
	end
	BossAssistWGData.Instance = self

	local other_config_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto")
	self.boss_dead_efficiency_cfg = ListToMap(other_config_cfg.boss_dead_efficiency, "boss_id")

	self.assist_info = {
		target_boss_scene_id = 0,
		target_boss_id = 0,
		target_boss_pos_x = 0,
		target_boss_pos_y = 0,
		assist_role_id = 0,
		assist_role_name = "",
		assist_role_prof = 0,
		assist_role_sex = 0,
		avatar_key_big = 0,
		avatar_key_small = 0,
		damage_to_boss = 0,
		owner_damage_to_boss = 0,
		day_thank_reward_times = 0,
		day_assist_reward_times = 0,
		force_attack_role_id_list = {},
	}
	self.call_help_info_list = {}
	self.role_damage_info = {
		target_boss_id = 0,
		role_damage_info_list = {},
	}
	self.role_assist_damage_info = {
		target_boss_id = 0,
		role_damage_info_list = {},
	}

	self.role_assist_reward_info = {
		target_boss_id = 0,
		has_first_reward = 0,
		reward_item_list = {},
		first_reward_item_list = {},
	}

	self.role_assist_thank_list_info = {
		target_boss_id = 0,
		target_boss_scene_id = 0,
		thank_role_info_list = {},
	}

	self.role_assist_thank_latter_info = {
		role_id = 0,
		role_name = "",
		prof = 0,
		sex = 0,
		select_statment = 0,
		role_level = 0,
		target_boss_id = 0,
		avatar_key_big = 0,
		avatar_key_small = 0,
		target_boss_scene_id = 0,
	}

	self.drop_info = {
		target_boss_id = 0,
		has_first_reward = 0,
		reward_item_list = {},
		first_reward_item_list = {},
	}

	self.normal_hurt = {
		main_role_hurt = 0,
		target_boss_id = 0,
		hurt_info = {}
    }

	self.boss_hp_record_info = {}

    self.be_attack_info = {}
	self.main_role_kanjia_list = {}
	self.last_convene_time = 0
	self.is_xianli_reduce = false
    RemindManager.Instance:Register(RemindName.KanJiaXieZhu, BindTool.Bind(self.IsShowKanJiaRedPoint, self))
end

function BossAssistWGData:__delete()
    BossAssistWGData.Instance = nil
    self.old_energy_num = nil
	RemindManager.Instance:UnRegister(RemindName.KanJiaXieZhu)
end

-- 个人信息
function BossAssistWGData:SetGuildAssistRoleInfo(info)
	self.assist_info.target_boss_scene_id = info.target_boss_scene_id
	self.assist_info.target_boss_id = info.target_boss_id
	self.assist_info.target_boss_pos_x = info.target_boss_pos_x
	self.assist_info.target_boss_pos_y = info.target_boss_pos_y
	self.assist_info.assist_role_id = info.assist_role_id
	self.assist_info.assist_role_name = info.assist_role_name
	self.assist_info.assist_role_prof = info.assist_role_prof
	self.assist_info.assist_role_sex = info.assist_role_sex
	self.assist_info.avatar_key_big = info.avatar_key_big
	self.assist_info.avatar_key_small = info.avatar_key_small
	self.assist_info.damage_to_boss = info.damage_to_boss
	self.assist_info.owner_damage_to_boss = info.owner_damage_to_boss
	self.assist_info.day_thank_reward_times = info.day_thank_reward_times
	self.assist_info.day_assist_reward_times = info.day_assist_reward_times
	self.assist_info.force_attack_role_id_list = info.force_attack_role_id_list
end

function BossAssistWGData:AddForceAttackRole(role_id)
	self.assist_info.force_attack_role_id_list[role_id] = role_id
end

function BossAssistWGData:IsForceAttack(role_id)
	return self.assist_info.force_attack_role_id_list[role_id] ~= nil
end

--是否协助
function BossAssistWGData:GetAssistInfo()
	return self.assist_info
end
--是否协助
function BossAssistWGData:IsAssist()
	return self.assist_info.assist_role_id > 0
end
--是否协助
function BossAssistWGData:IsAssistRole(role_id)
	return self.assist_info.assist_role_id == role_id
end

--是否显示协助panel
function BossAssistWGData:ShowAssistPanel()
	return self.assist_info.assist_role_id > 0 or
			(self.assist_info.owner_damage_to_boss > 0 and not IsEmptyTable(self:GetGuildAssistRoleAssistDamageList()))
end


--是否协助
function BossAssistWGData:HasBossDamage()
	return self.assist_info.owner_damage_to_boss > 0
end

--out场景的时候清除协助伤害
function BossAssistWGData:ClearBossAssistInfo()
	self.assist_info.owner_damage_to_boss = 0
end

-- 求助列表
function BossAssistWGData:SetGuildAssistCallHelpListInfo(info)
	local list = {}
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	for i, v in ipairs(info.call_help_info_list) do
		local cfg = BossWGData.Instance:GetVipBossLayerCfgBySceneId(v.target_boss_scene_id)
		if not cfg or cfg.vip_level <= role_vip then
			table.insert(list, v)
		end
	end
	self.call_help_info_list = list
	MainuiWGCtrl.Instance:SetXieZhuRemind()
end

-- 求助列表
function BossAssistWGData:HasCallHelp()
	if #self.call_help_info_list == 1 and self.call_help_info_list[1].role_id == self.assist_info.assist_role_id and self:IsShowKanJiaRedPoint() == 0  then
		return false
	end
	return #self.call_help_info_list > 0 or self:IsShowKanJiaRedPoint() > 0
end


-- 求助列表
function BossAssistWGData:GetGuildAssistCallHelpListInfo()
	return self.call_help_info_list
end

-- 伤害列表
function BossAssistWGData:SetGuildAssistBossDamageListInfo(info)
	self.role_damage_info.target_boss_id = info.target_boss_id
	self.role_damage_info.role_damage_info_list = info.role_damage_info_list
end

-- 伤害列表
function BossAssistWGData:GetGuildAssistBossDamageListInfo()
	return self.role_damage_info.role_damage_info_list, self.role_damage_info.target_boss_id
end


-- 协助者伤害列表
function BossAssistWGData:GetGuildAssistBossDamageMax()
	local damage = 1
	if not IsEmptyTable(self.role_damage_info) and not IsEmptyTable(self.role_damage_info.role_damage_info_list) then 
		for _,v in ipairs(self.role_damage_info.role_damage_info_list) do
			if v.damage_value > damage then
				damage = v.damage_value
			end
		end
	end
	return damage
end

-- 协助者伤害列表
function BossAssistWGData:SetGuildAssistRoleAssistDamageInfo(info)
	self.role_assist_damage_info.target_boss_id = info.target_boss_id
	self.role_assist_damage_info.role_damage_info_list = info.role_damage_info_list
end

-- 协助者伤害列表
function BossAssistWGData:GetGuildAssistRoleAssistDamageList()
	return self.role_assist_damage_info.role_damage_info_list
end

-- 协助者伤害列表
function BossAssistWGData:GetGuildAssistRoleAssistDamageMax()
	local max_info = self.role_assist_damage_info.role_damage_info_list[1]
	if max_info then
		return max_info.damage_value
	end
	return 0
end

-- 击杀Boss奖励展示
function BossAssistWGData:SetGuildAssisBossRewardInfo(info)
	self.role_assist_reward_info.target_boss_id = info.target_boss_id
	self.role_assist_reward_info.has_first_reward = info.has_first_reward
	local list = info.reward_item_list
	self:SortRewardItemList(list)
	self.role_assist_reward_info.reward_item_list = list
	list = info.first_reward_item_list
	self:SortRewardItemList(list)
	self.role_assist_reward_info.first_reward_item_list = list
end

function BossAssistWGData:SortRewardItemList(list)
	for i, v in pairs(list) do
		local cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		v.color = cfg.color
	end
	table.sort(list, SortTools.KeyUpperSorter("color"))
end

-- 击杀Boss奖励展示
function BossAssistWGData:GetGuildAssisBossRewardInfo()
	return self.role_assist_reward_info
end

-- 世界boss掉落
function BossAssistWGData:SetWorldBossDropInfo(protocol)
	self.drop_info.target_boss_id = protocol.monster_id
	self.drop_info.has_first_reward = protocol.has_first_reward
	self:SortRewardItemList(protocol.reward_item_list)
	self.drop_info.reward_item_list = protocol.reward_item_list
	self:SortRewardItemList(protocol.fisrt_reward_item_list)
	self.drop_info.first_reward_item_list = protocol.fisrt_reward_item_list
end

-- 被攻击信息
function BossAssistWGData:SetBeAttackInfo(protocol)
    self.be_attack_info[protocol.scene_type] = protocol
end

function BossAssistWGData:GetIsAttackerInfo(role_id)
    local scene_type = Scene.Instance:GetSceneType()
    local be_attack_info
    if not IsEmptyTable(self.be_attack_info) and not IsEmptyTable(self.be_attack_info[scene_type]) then
        be_attack_info = self.be_attack_info[scene_type]
    end
	if not IsEmptyTable(be_attack_info) and not IsEmptyTable(be_attack_info.attacker_list) then
		for _, v in pairs(be_attack_info.attacker_list) do
			if v.attacker_obj_id then
				local role = Scene.Instance:GetRoleByObjId(v.attacker_obj_id)
				if role and role.vo then
					if role.vo.role_id == role_id then
						return v
					end
				end
			end
		end
	end
	return nil
end

function BossAssistWGData:GetWorldBossDropInfo()
	return self.drop_info or {}
end

-- 感谢协助者列表
function BossAssistWGData:SetGuildAssistThankRoleListInfo(info)
	self.role_assist_thank_list_info.target_boss_id = info.target_boss_id
	self.role_assist_thank_list_info.target_boss_scene_id = info.target_boss_scene_id
	self.role_assist_thank_list_info.thank_role_info_list = info.thank_role_info_list
end

-- 感谢协助者列表
function BossAssistWGData:GetGuildAssistThankRoleListInfo()
	return self.role_assist_thank_list_info
end

-- 感谢协助者列表
function BossAssistWGData:GetGuildAssistThankRoleList()
	return self.role_assist_thank_list_info.thank_role_info_list
end

-- 收到的感谢信
function BossAssistWGData:SetGuildAssisThankLattertInfo(info)
	self.role_assist_thank_latter_info.role_id = info.role_id
	self.role_assist_thank_latter_info.role_name = info.role_name
	self.role_assist_thank_latter_info.prof = info.prof
	self.role_assist_thank_latter_info.sex = info.sex
	self.role_assist_thank_latter_info.select_statment = info.select_statment
	self.role_assist_thank_latter_info.role_level = info.role_level
	self.role_assist_thank_latter_info.target_boss_id = info.target_boss_id
	self.role_assist_thank_latter_info.avatar_key_big = info.avatar_key_big
	self.role_assist_thank_latter_info.avatar_key_small = info.avatar_key_small
	self.role_assist_thank_latter_info.target_boss_scene_id = info.target_boss_scene_id
end

-- 收到的感谢信
function BossAssistWGData:GetGuildAssisThankLattertInfo()
	return self.role_assist_thank_latter_info
end
-------------------------------------------------------------------------------------
-- 门票boss的排行榜
function BossAssistWGData:SetHurtInfo(protocol)
    self.normal_hurt.main_role_rank = protocol.role_rank
	self.normal_hurt.main_role_hurt = protocol.role_hurt
	self.normal_hurt.target_boss_id = protocol.monster_id
	self.normal_hurt.monster_key = protocol.monster_key
	self.normal_hurt.hurt_info = protocol.hurt_info
	self.normal_hurt.target_boss_obj_id = protocol.monster_obj_id
end

--获取伤害排行
function BossAssistWGData:GetHurtInfo()
	return self.normal_hurt
end

--获取最大伤害值
function BossAssistWGData:GetNormalHurtInfoMaxValue()
	local list = self.normal_hurt.hurt_info or {}
	local hurt = 1

	if list[1] and list[1].hurt > hurt then
		hurt = list[1].hurt
	end
	return hurt
end

function BossAssistWGData:HasDemageHurt()
	return not IsEmptyTable(self.normal_hurt.hurt_info)
end
--获取
function BossAssistWGData:HasMainRoleHurt()
	return self.normal_hurt.main_role_hurt > 0
end

--获取
function BossAssistWGData:ClearNormalHurtList()
	self.normal_hurt = {
        main_role_rank = 0,
		main_role_hurt = 0,
		target_boss_id = 0,
		hurt_info = {}
	}
end
--------------------------------------------------------------------------------------
--砍价协助信息返回
function BossAssistWGData:SetKanJiaInfo(protocol)
	if self.kanjia_data == nil then
		self.kanjia_data = {}
		self.kanjia_data.info_list = {}
	end

	self.kanjia_data.today_kanjia_cnt = protocol.KanjianItemInfo.today_kanjia_cnt
	self.kanjia_data.info_list[protocol.KanjianItemInfo.info.kanjia_index] = protocol.KanjianItemInfo.info

	local role_vo = RoleWGData.Instance:GetRoleVo()
	if role_vo.role_id == protocol.KanjianItemInfo.info.owner_id  then
		self.main_role_kanjia_list[protocol.KanjianItemInfo.info.kanjia_item_id] = protocol.KanjianItemInfo.info
	end
	RemindManager.Instance:Fire(RemindName.KanJiaXieZhu)
	MainuiWGCtrl.Instance:SetXieZhuRemind()
end

--所有砍价协助信息返回
function BossAssistWGData:SetKanJiaListInfo(protocol)
	if self.kanjia_data == nil then
		self.kanjia_data = {}
	end

	self.kanjia_data.today_kanjia_cnt = protocol.today_kanjia_cnt
	self.kanjia_data.info_list = protocol.KanjianItemInfoList

	local role_vo = RoleWGData.Instance:GetRoleVo()
	for k,v in pairs(self.kanjia_data.info_list) do
		if role_vo.role_id == v.owner_id  then
			self.main_role_kanjia_list[v.kanjia_item_id] = v
		end
	end
	RemindManager.Instance:Fire(RemindName.KanJiaXieZhu)
	MainuiWGCtrl.Instance:SetXieZhuRemind()
end

function BossAssistWGData:RemoveKanJiaInfo(protocol)
	if self.kanjia_data ~= nil and self.kanjia_data.info_list ~= nil then
		self.kanjia_data.info_list[protocol.kanjia_index] = nil
	end
end

function BossAssistWGData:GetKanJiaInfoList()
	return self.kanjia_data
end

function BossAssistWGData:GetKanJiaItemInfo(item_id)
	local cfg = ConfigManager.Instance:GetAutoConfig("kanjiaconfig_auto").kanjia_item
	for k,v in pairs(cfg) do
		if v.kanjia_item_id == item_id then
			return v
		end
	end
	return nil
end

--获取砍价排序列表
function BossAssistWGData:GetKanJiaInfoSortList()
	local list = {}
	if not self.kanjia_data or not self.kanjia_data.info_list then
		return list
	end
	local other_cfg = ConfigManager.Instance:GetAutoConfig("kanjiaconfig_auto").other
	if not other_cfg then
		return list
	end
	local role_vo = RoleWGData.Instance:GetRoleVo()
	local state = 0
	for k,v in pairs(self.kanjia_data.info_list) do
		state = QuanMinBeiZhanWGData.Instance:GetKanjiaRewardStateByItemID(v.kanjia_item_id)
		if state ~= ActivityRewardState.YLQ then
			v.has_start_time = TimeWGCtrl.Instance:GetServerTime() - v.start_time
			v.left_time = other_cfg[1].kanjia_valid_time - v.has_start_time
			if v.left_time > 0 then
				if role_vo.role_id == v.owner_id  then
					v.sort = 0
					v.is_mainrole = true
					table.insert(list,v)
				else
					v.is_mainrole = false
					v.sort = 1
					table.insert(list,v)
				end
			end
		end
		table.sort(list, SortTools.KeyLowerSorters("sort", "has_helped", "has_start_time"))
	end
	return list
end

--获取自己的砍价礼包砍价有效期时间
function BossAssistWGData:GetKanJiaGiftInvalidTime(gift_id)
	if self.main_role_kanjia_list[gift_id] ~= nil then
		local cfg = ConfigManager.Instance:GetAutoConfig("kanjiaconfig_auto").kanjia_item
		if cfg ~= nil then
			for i,v in ipairs(cfg) do
				if v.kanjia_item_id == gift_id then
					return self.main_role_kanjia_list[gift_id].start_time + v.cd
				end
			end
		end
	end
	return 0
end

--获取主角的礼包砍价信息
function BossAssistWGData:GetMainRoleGiftZheKou(gift_id)
	local cfg = self:GetKanJiaItemInfo(gift_id)
	local kanjia_cnt = RoleWGData.Instance:GetKanJiaGiftItem(gift_id) or 0
	if cfg ~= nil then
		return 1 - cfg.once_sub_percent * kanjia_cnt / 100
	end
	return 1 --100% 原价
end

--获取当前剩余次数
function BossAssistWGData:GetKanJiaLeftTime()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("kanjiaconfig_auto").other
	local today_kanjia_cnt = RoleWGData.Instance:GetTodayKanJiaCount()
	if other_cfg ~= nil then
		if self.kanjia_data ~= nil then
			return other_cfg[1].role_max_kanjia_limit - today_kanjia_cnt
		end
		
	end
	return 0
end

function BossAssistWGData:IsShowKanJiaRedPoint()
	local role_vo = RoleWGData.Instance:GetRoleVo()
	if self.kanjia_data ~= nil and role_vo then
		local today_kanjia_cnt = RoleWGData.Instance:GetTodayKanJiaCount()
		local other_cfg = ConfigManager.Instance:GetAutoConfig("kanjiaconfig_auto").other
		if today_kanjia_cnt < other_cfg[1].role_max_kanjia_limit then
			for k,v in pairs(self.kanjia_data.info_list) do
				if v.has_helped == 0 and role_vo.role_id ~= v.owner_id then
					if other_cfg[1].kanjia_valid_time - (TimeWGCtrl.Instance:GetServerTime() - v.start_time) > 0 then
						return self:GetKanJiaLeftTime() > 0 and 1 or 0
					end
				end
			end
		end
	end
	return 0
end

-- boss体力
function BossAssistWGData:SaveBossXianli(protocol)
	self.is_xianli_reduce = false
    self.cur_energy_num = protocol.xianli_value
    if not self.old_energy_num then
        self.old_energy_num = self.cur_energy_num
    end
    if self.old_energy_num < self.cur_energy_num then
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Boss.GetXianli, self.cur_energy_num - self.old_energy_num))
	elseif self.old_energy_num > self.cur_energy_num then
		self.is_xianli_reduce = true
    end
	self.old_energy_num = self.cur_energy_num
    local flag = protocol.xianli_receive_flag
    self.xianli_receive_flag_tb = bit:d2b_two(flag)
end

function BossAssistWGData:GetIsXianliReduce()
    return self.is_xianli_reduce
end

function BossAssistWGData:SetIsXianliReduce(bool)
    self.is_xianli_reduce = bool
end

function BossAssistWGData:GetXianliFlagTbIndex(idx)
    return self.xianli_receive_flag_tb[idx] or 0
end

function BossAssistWGData:GetBossXianli()
    return self.cur_energy_num or 0
end

--返回是否仙力足够，是否需要消耗仙力/是vip_boss
function BossAssistWGData:JudgeIsEnoughBoss(boss_id)
    local boss_info = BossWGData.Instance:GetBossInfoByBossId(boss_id)
    local kill_reduce_xianli = boss_info and boss_info.kill_reduce_xianli
    if kill_reduce_xianli then
        local total_xianli = self:GetBossXianli()
        return kill_reduce_xianli <= total_xianli, true
    end
    return false, false
end

--返回是否仙力足够，是否需要消耗仙力/是vip_boss
function BossAssistWGData:JudgeIsEnoughBossBySceneId(scene_id)
	local cfg = BossWGData.Instance:GetVipBossListByScene(scene_id)
	if IsEmptyTable(cfg) then
		return false
	end

    local kill_reduce_xianli = cfg[1].kill_reduce_xianli
    if kill_reduce_xianli then
        local total_xianli = self:GetBossXianli()
        return kill_reduce_xianli <= total_xianli
    end
    return false
end

--true 可攻击 false 不可攻击
function BossAssistWGData:JudgeIsLevelLimitBoss(boss_id)
    local boss_data = BossWGData.Instance:GetBossInfoByBossId(boss_id)
    local role_level = RoleWGData.Instance:GetRoleLevel()
    if not IsEmptyTable(boss_data) and boss_data.boss_level and boss_data.max_delta_level then
        return role_level - boss_data.boss_level < boss_data.max_delta_level
    else
        return false
    end
end


function BossAssistWGData:GetCanOpenViewScene(scene_type)
    if scene_type ~= SceneType.Common and
        scene_type ~= SceneType.KF_BOSS and
        scene_type ~= SceneType.WorldBoss and
        scene_type ~= SceneType.DABAO_BOSS and
        scene_type ~= SceneType.VIP_BOSS and
        scene_type ~= SceneType.PERSON_BOSS and
        scene_type ~= SceneType.Shenyuan_boss then
        return false
    end
    return true
end

function BossAssistWGData:SaveConveneTime(time)
	self.last_convene_time = time
end

function BossAssistWGData:GetConveneTime()
	return self.last_convene_time or 0
end

function BossAssistWGData:SetVipBossKillTimes(times)
	self.vip_boss_kill_times = times
end

function BossAssistWGData:GetVipBossKillTimes()
	return self.vip_boss_kill_times or 0
end

--记录Boss 3秒的血量值.
function BossAssistWGData:SetBossHpRecordInfo(protocol)
	if not self.boss_hp_record_info[protocol.obj_id] then
		self.boss_hp_record_info[protocol.obj_id] =
		{
			obj_id = 0,
			monster_id = 0,
			max_hp = 0,
			record_index = 0,
			record_pro = 0,			--评分范围内的百分百进度.
			monster_hp_record_list = {},
			boss_dead_efficiency_list = {}
		}
	end

	local old_monster_id = self.boss_hp_record_info[protocol.obj_id].monster_id
	self.boss_hp_record_info[protocol.obj_id].obj_id = protocol.obj_id
	self.boss_hp_record_info[protocol.obj_id].monster_id = protocol.monster_id
	self.boss_hp_record_info[protocol.obj_id].monster_hp_record_list = protocol.monster_hp_record_list

	if old_monster_id ~= protocol.monster_id then
		self:SetBossDeadEfficiencyInfo(protocol.obj_id, protocol.monster_id)

		local boss_data = BossWGData.Instance:GetMonsterInfo(protocol.monster_id)
		if boss_data then
			self.boss_hp_record_info[protocol.obj_id].max_hp = boss_data.shengming_max
		end
	end

	self:SetBossDeadEfficiencyRecord(protocol.obj_id, protocol.monster_hp_record_list)
end

function BossAssistWGData:GetBossHpRecordInfo(obj_id)
	return self.boss_hp_record_info[obj_id]
end

function BossAssistWGData:RemoveBossHpRecordInfo(obj_id)
	self.boss_hp_record_info[obj_id] = nil
end

function BossAssistWGData:SetBossDeadEfficiencyInfo(obj_id, boss_id)
	local boss_dead_efficiency = self.boss_dead_efficiency_cfg[boss_id] and self.boss_dead_efficiency_cfg[boss_id].boss_dead_efficiency or {}
	local boss_dead_efficiency_list = {}

	local str_list = Split(boss_dead_efficiency, ",")

	for index, value in ipairs(str_list) do
		table.insert(boss_dead_efficiency_list, tonumber(value))
	end

	self.boss_hp_record_info[obj_id].boss_dead_efficiency_list = boss_dead_efficiency_list
end

function BossAssistWGData:SetBossDeadEfficiencyRecord(obj_id, monster_hp_record_list)
	local first_hp = monster_hp_record_list[1] or 0
	local end_hp = monster_hp_record_list[3] or 0
	if end_hp == 0 then
		self.boss_hp_record_info[obj_id].record_index = 1
		self.boss_hp_record_info[obj_id].record_pro = 0
		return
	end

	local record = (end_hp - first_hp) / self.boss_hp_record_info[obj_id].max_hp * 10000
	local record_index = 1
	local record_pro = 0
	local late_record = 0

	for index, value in ipairs(self.boss_hp_record_info[obj_id].boss_dead_efficiency_list) do
		if record >= value then
			record_index = index

			if index == #self.boss_hp_record_info[obj_id].boss_dead_efficiency_list then
				record_pro = 1
			end
		else
			record_pro = (record - late_record) / (value - late_record)
			break
		end
		late_record = value
	end

	self.boss_hp_record_info[obj_id].record_index = record_index
	self.boss_hp_record_info[obj_id].record_pro = record_pro
end