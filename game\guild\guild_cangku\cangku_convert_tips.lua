CangKuConvertTips = CangKuConvertTips or BaseClass(SafeBaseView)

function CangKuConvertTips:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(700, 450)})
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_ck_convert")
end

function CangKuConvertTips:__delete()
end

function CangKuConvertTips:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end	
end

function CangKuConvertTips:OpenCallBack()
	self.amount = 1
	self.max_num = 0
end 

function CangKuConvertTips:LoadCallBack()
	-- self:SetSecondView(nil,self.node_list["size"])
	self.item_cell = ItemCell.New(self.node_list["item_cell"])
	self.item_cell:SetIsShowTips(true)

	XUI.AddClickEventListener(self.node_list["btn_OK"], BindTool.Bind(self.OnClickOKBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_minus"], BindTool.Bind(self.OnClickMinus, self))
	XUI.AddClickEventListener(self.node_list["btn_plus"], BindTool.Bind(self.OnClickPlus, self))
	self.node_list.num_slider.slider.onValueChanged:AddListener(BindTool.Bind(self.OnSliderValueChange, self))
end

function CangKuConvertTips:ShowIndexCallBack()
	local min_value = 1
	self.node_list.num_slider.slider.value = 1
end

function CangKuConvertTips:SetData(convery_id)
	self.convery_id = convery_id
end

function CangKuConvertTips:OnClickOKBtn()
	local num = math.floor(self.num_value)
	GuildWGCtrl.Instance:SendGuildExchangeReq(self.convery_id, num)
	self:Close()
end

function CangKuConvertTips:OnFlush()
	if not self.convery_id then return end
	local cfg = GuildCangKuWGData.Instance:GetGuildConvertItemById(self.convery_id)
	if cfg then
		local has_num = GuildCangKuWGData.Instance:GetStorgeScore()
		local one_need = cfg.need_storge_score or 1
		local max_value = math.floor(has_num/one_need) > 1 and math.floor(has_num/one_need) or 1
		self.max_num = max_value
		self.node_list.num_slider.slider.maxValue = max_value

		self.item_cell:SetData(cfg.reward_item)
		local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.reward_item.item_id)
		self.node_list.name.text.text = item_cfg and item_cfg.name or ""
	end 
end

function CangKuConvertTips:FlushConsume(value)
	local cfg = GuildCangKuWGData.Instance:GetGuildConvertItemById(self.convery_id)
	local has_num = GuildCangKuWGData.Instance:GetStorgeScore()
	local one_need = cfg and cfg.need_storge_score or 1
	self.node_list.need_num.text.text = value * one_need
	self.node_list.has_num.text.text = has_num
	self.node_list.num.text.text = value
	self.num_value = value
end

function CangKuConvertTips:OnSliderValueChange(value)
	self.amount = value
	self:FlushConsume(value)
end

function CangKuConvertTips:OnClickMinus()
	self.amount = math.max(self.amount - 1, 1)
	self:FlushConsume(self.amount)
	self.node_list.num_slider.slider.value = self.amount
end

function CangKuConvertTips:OnClickPlus()
	self.amount = math.min(self.amount + 1, self.max_num)
	self:FlushConsume(self.amount)
	self.node_list.num_slider.slider.value = self.amount
end