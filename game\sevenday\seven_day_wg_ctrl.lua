SevenDayWGCtrl = SevenDayWGCtrl or BaseClass(BaseWGCtrl)
function SevenDayWGCtrl:__init()
	if SevenDayWGCtrl.Instance then
		error("[SevenDayWGCtrl]:Attempt to create singleton twice!")
	end
	SevenDayWGCtrl.Instance = self
	self.view = SevenDayView.New(GuideModuleName.SevenDay)
end

function SevenDayWGCtrl:__delete()


	if nil ~= self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	SevenDayWGCtrl.Instance = nil
end

function SevenDayWGCtrl:Open(tab_index, param_t)
	-- self.view:Open(tab_index, param_t)
end

function SevenDayWGCtrl:FlushSevenDayView()
	self.view:Flush()
end