SkillView.SkillType = {
    None = 0,
    Fire = 1,
    ICE = 2,
    THUNDER = 3
}

SkillView.FlushFormView = {
    Click = 0,
    Protocol = 1,
}

function SkillView:InitSkillUpGradeView()
    self.node_list["btn_reset"].button:AddClickListener(BindTool.Bind(self.OnClickReset, self))
    self.node_list["btn_upgrade"].button:AddClickListener(BindTool.Bind(self.OnClickUpGrade, self))
    self.node_list["btn_upgrade_tips"].button:AddClickListener(BindTool.Bind(self.OnClickBtnUpgradeTip, self))
    if not self.series_list then
        self.series_list = {}
    end
    for i = 1, 3 do
        local item_type = self.node_list["grade_" .. i]
        self.series_list[i] = SkillTypeRender.New(item_type)
        self.series_list[i]:SetType(i)
        --item_type.button:AddClickListener(BindTool.Bind(self.OnSelectSeries, self, i))
        self.node_list["txt_ele_title_" .. i].text.text = Language.Skill.EleTitle[i]
    end

    self.jinjie_btn_select_list = {}
    self.jinjie_second_btn_select_list = {}
    for i = 1, 3 do
        self.jinjie_btn_select_list[i] = self.node_list["jinjie_btn_select_" .. i]
        self.jinjie_second_btn_select_list[i] = self.node_list["jinjie_second_btn_select_" .. i]
        self.node_list["jinjie_type_btn_" .. i].button:AddClickListener(BindTool.Bind(self.OnClickJinJieSkill, self, i))
        self.node_list["jinjie_second_type_btn_" .. i].button:AddClickListener(BindTool.Bind(self.OnClickJinJieSecondSkill, self, i))
    end

    self.select_jinjie_skill_type = 1
    local _, select_type = SkillWGData.Instance:GetCurUpGradeInfo()
    if select_type ~= 0 then
        self.select_jinjie_skill_type = select_type
    end
    self:OnClickJinJieSkill(self.select_jinjie_skill_type)

    if not self.scene_model_t then
        self.scene_model_t = {}
    end

    if nil == self.scene_model_t[self.show_index] then
        self.scene_model_t[self.show_index] = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["SiutDisplay1"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}

		self.scene_model_t[self.show_index]:SetRenderTexUI3DModel(display_data)

        local temp_res_id = NewAppearanceWGData.Instance:GetAdvancedImageCfgByType(ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_SHENBING).appe_image_id
        local bundle, asset = ResPath.GetWeaponModelRes(temp_res_id)
        self.scene_model_t[self.show_index]:SetMainAsset(bundle, asset, function ()
            self.node_list["role_weapon2"].transform.localRotation = Quaternion.Euler(0, 0, -25)
        end)
    end
end

function SkillView:DeleteSkillUpGradeView()
    if self.series_list then
        for i, v in pairs(self.series_list) do
            v:DeleteMe()
        end
        self.series_list = nil
    end

    if self.scene_model_t then
        for k,v in pairs(self.scene_model_t) do
            v:DeleteMe()
        end

        self.scene_model_t = {}
    end

    if self.skill_info_list then
        self.skill_info_list:DeleteMe()
        self.skill_info_list = nil
    end

    self.now_self_attach = nil
    self.effct_attach = nil
    self.select_type = nil
    self.select_grade = nil

    self.jinjie_btn_select_list = nil
    self.jinjie_second_btn_select_list = nil
end

function SkillView:OnFlushSecondSkillRedpoint()
    local grade, series = SkillWGData.Instance:GetCurUpGradeInfo()
    local prof_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
    for i = 1, 3 do
        local limit = SkillWGData.Instance:GetSkillUpGradeProfLimit(i)
        if series == 0 or series == self.select_jinjie_skill_type then
            if grade >= i then
                self.node_list["icon_remind_0" .. i]:SetActive(false)
            else
                self.node_list["icon_remind_0" .. i]:SetActive(prof_zhuan >= limit)
            end
        else
            self.node_list["icon_remind_0" .. i]:SetActive(false)
        end
    end
end

function SkillView:OnClickJinJieSkill(index)
    self.select_jinjie_skill_type = index
    for i = 1, 3 do
        self.jinjie_btn_select_list[i]:SetActive(i == index)
        self.node_list["second_skill_img_" .. i].image:LoadSprite(ResPath.GetRoleUIImage(string.format("icon_%s_%s", self.select_jinjie_skill_type, i)))
    end
    self:OnClickJinJieSecondSkill(1)
    self:OnFlushSecondSkillRedpoint()
end

function SkillView:OnClickJinJieSecondSkill(index)
    for i = 1, 3 do
        self.jinjie_second_btn_select_list[i]:SetActive(i == index)
    end
    local t = {}
    t.select_type = self.select_jinjie_skill_type
    t.select_grade = index
    t.from_view = SkillView.FlushFormView.Click
    ViewManager.Instance:FlushView(GuideModuleName.SkillView, TabIndex.skill_upgrade, nil, t)
end

function SkillView:OnClickReset()
    local _, select_type = SkillWGData.Instance:GetCurUpGradeInfo()
    if select_type == 0 then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Skill.NoJinJieTip1)
        return
    end
    ViewManager.Instance:Open(GuideModuleName.SkillReset)
end

function SkillView:ShowIndexUpGradeView()
    SkillWGCtrl.Instance:SendCareerUpgrade(CAREER_UPGRADE_OPERA.INFO)
end

function SkillView:OnClickUpGrade()
    local skill_grade, skill_series = SkillWGData.Instance:GetCurUpGradeInfo()

    if self.select_type ~= skill_series and skill_series ~= 0 then
        self:OnClickReset()
        return
    end

    local prof_limit = SkillWGData.Instance:GetSkillUpGradeProfLimit(skill_grade + 1)

    if prof_limit == nil then
        return
    end

    local prof_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
    if prof_zhuan < prof_limit then
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Skill.ProfLimit, prof_limit))
        return
    end

    local callback = function()
        SkillWGCtrl.Instance:SendCareerUpgrade(CAREER_UPGRADE_OPERA.UPGRADE)
    end

    if skill_series == 0 then
        if not self.select_type then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Skill.SelectSeries)
            return
        end
        SkillWGCtrl.Instance:SendCareerUpgrade(CAREER_UPGRADE_OPERA.SELECT_TYPE, self.select_type)
        GlobalTimerQuest:AddDelayTimer(function()
            callback()
        end,0.1)
    else
        callback()
    end
end

function SkillView:OnClickBtnUpgradeTip()
    RuleTip.Instance:SetContent(Language.Skill.UpGradeTipContent, Language.Skill.UpGradeTipTitle, nil, nil, true)
end

function SkillView:OnFlushSkillUpgrade(param)
    for i, v in pairs(param) do
        if i == "all" then
            if v.from_view == SkillView.FlushFormView.Click then
                self:OnFlushUpgradeRightSelectInfo(v)
            else
                self:OnFlushAllInfo()
            end
        end
    end
end

function SkillView:OnFlushUpgradeRightSelectInfo(select_table)
    if select_table == nil then
        local grade, series = SkillWGData.Instance:GetCurUpGradeInfo()
        self.select_type = series
        self.select_grade = grade
    else
        self.select_type = select_table.select_type
        self.select_grade = select_table.select_grade
    end

    local cfg = SkillWGData.Instance:GetSkillUpGradeInfo(self.select_type, self.select_grade)

    if cfg then
        self.node_list["grade_name_img"].image:LoadSprite(ResPath.GetRoleUIImage(string.format("skill_type_icon_%s", self.select_jinjie_skill_type)))
        -- self.node_list["txt_now_grade_name"].text.text = cfg.name
        self.node_list["txt_now_grade_desc"].text.text = cfg.desc
    end

    if self.select_type ~= 0 and self.select_grade ~= 0 then
        local str = string.format("icon_%d_%d", self.select_type, self.select_grade)
        self.node_list["now_grade_icon"].image:LoadSprite(ResPath.GetRoleUIImage(str))
    end
    self:SetSelectEffect()

    self:FlushProfLimit()
    self:OnFlushRightSkillList()
    self:FlushAllUpgradeSkillHigh(select_table)
    -- self:OnFlushUpgradeRightInfo(self.select_type)
    self:OnFlushButtonState()
end

function SkillView:SetSelectEffect()
    if not self.effct_attach then
        self.effct_attach = self.node_list["series_effect"].gameObject:GetComponent(typeof(Game.GameObjectAttach))
    end
    local status = self.select_type ~= 0
    if status then
        local bundle, asset = RoleWGData.Instance:GetSKillGradeEffectByType(self.select_type)
        self.effct_attach.BundleName = bundle
        self.effct_attach.AssetName = asset
        self.effct_attach.enabled = false
        self.effct_attach.enabled = true
    end
    self.node_list["series_effect"]:SetActive(status)
end

function SkillView:OnFlushButtonState()
    local grade, series = SkillWGData.Instance:GetCurUpGradeInfo()
    if self.select_type ~= series and series ~= 0 then
        XUI.SetButtonEnabled(self.node_list['btn_upgrade'], true)
        self.node_list["txt_btn_upgrade"].text.text = Language.Skill.BtnReset
        self:FlushBtnUpGradeRed(false)
    elseif grade < self.select_grade then
        XUI.SetButtonEnabled(self.node_list['btn_upgrade'], true)
        self.node_list["txt_btn_upgrade"].text.text = Language.Skill.BtnUpGrade

        local select_prof_limit = SkillWGData.Instance:GetSkillUpGradeProfLimit(self.select_grade)
        local prof_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
        self:FlushBtnUpGradeRed(prof_zhuan >= select_prof_limit)
    else
        XUI.SetButtonEnabled(self.node_list['btn_upgrade'], false)
        self:FlushBtnUpGradeRed(false)
        self.node_list["txt_btn_upgrade"].text.text = Language.Skill.HasUpGrade
    end
    --local active = grade ~= 3 or self.select_type ~= series
    --XUI.SetButtonEnabled(self.node_list["btn_upgrade"], active)
end

function SkillView:FlushProfLimit()
    local grade, series = SkillWGData.Instance:GetCurUpGradeInfo()
    local select_prof_limit = SkillWGData.Instance:GetSkillUpGradeProfLimit(self.select_grade)
    if select_prof_limit == nil then
        return
    end
    local prof_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
    local color = select_prof_limit > prof_zhuan and COLOR3B.RED or COLOR3B.GREEN
    if series == 0 or series == self.select_type then
        local str = string.format(Language.Skill.ProfLimit, select_prof_limit)
        self.node_list["txt_upgrade_condition"].text.text = ToColorStr(str, color)
        self.node_list["txt_upgrade_condition"].text.alignment = UnityEngine.TextAnchor.MiddleLeft
    else
        self.node_list["txt_upgrade_condition"].text.alignment = UnityEngine.TextAnchor.MiddleCenter
        self.node_list["txt_upgrade_condition"].text.text = Language.Skill.ResetDesc
    end
end

function SkillView:OnFlushRightSkillList()
    local skill_list = SkillWGData.Instance:GetUpGradeSkillInfoByTypeAndGrade(self.select_type, self.select_grade)
    if not self.skill_info_list then
        self.skill_info_list = AsyncListView.New(SkillTypeInfoRender, self.node_list["upgrade_skill_list"])
    end
    self.skill_info_list:SetDataList(skill_list)
end

function SkillView:FlushAllUpgradeSkillHigh(select_table)
    if select_table == nil then
        local grade, series = SkillWGData.Instance:GetCurUpGradeInfo()
        select_table = {}
        select_table.select_type = self.select_type or series
        select_table.select_grade = self.select_grade or grade
    end
    for i, v in pairs(self.series_list) do
        v:SetSkillHigh(select_table)
    end
end

function SkillView:OnFlushAllInfo()
    self:OnFlushUpgradeRightSelectInfo()
    self:OnFlushUpgradeRightInfo()
    self:OnFlushNowSeriesIcon()
    self:OnFlushNowSeriesEffect()
    self:OnFlushNowLine()
    self:OnFlushSecondSkillRedpoint()
    --self:FlushBtnUpGradeRed()
    --self:FlushAllUpgradeSkillHigh()
    --self:FlushProfLimit()
    for i, v in pairs(self.series_list) do
        v:Flush()
    end
end

function SkillView:FlushBtnUpGradeRed(status)
    self.node_list['btn_skill_upgrade_red']:SetActive(status)
end

function SkillView:OnFlushUpgradeRightInfo(select_type)
    if select_type == nil then
        _, select_type = SkillWGData.Instance:GetCurUpGradeInfo()
    end
    if select_type == 0 then
        self.select_jinjie_skill_type = 1
        self:OnClickJinJieSkill(1)
    --     self.node_list["no_choose_info"]:SetActive(true)
    --     self.node_list["upgrade_right_panel"]:SetActive(false)
    -- else
    --     self.node_list["no_choose_info"]:SetActive(false)
    --     self.node_list["upgrade_right_panel"]:SetActive(true)
    end
end

function SkillView:OnFlushNowSeriesIcon()
    local grade, series = SkillWGData.Instance:GetCurUpGradeInfo()

    if series == SkillView.SkillType.None or grade == 0 then
        self.node_list["skill_upgrade_bg2"]:SetActive(false)
        local bundle, asset = ResPath.GetRoleUIImage("icon_0")
        self.node_list["img_skill_upgrade_icon"].image:LoadSprite(bundle, asset)
    else
        self.node_list["skill_upgrade_bg2"]:SetActive(true)
        local str = string.format("icon_%d_%d", series, grade)
        local bundle, asset = ResPath.GetRoleUIImage(str)
        self.node_list["img_skill_upgrade_icon"].image:LoadSprite(bundle, asset)
    end
end

function SkillView:OnFlushNowSeriesEffect()
    local grade, series = SkillWGData.Instance:GetCurUpGradeInfo()
    if series == SkillView.SkillType.None or grade == 0 then
        self.node_list["self_series_effect"]:SetActive(false)
    else
        if not self.now_self_attach then
            self.now_self_attach = self.node_list["self_series_effect"].gameObject:GetComponent(typeof(Game.GameObjectAttach))
        end
        local bundle, asset = RoleWGData.Instance:GetSKillGradeEffectByType(series)
        self.now_self_attach.BundleName = bundle
        self.now_self_attach.AssetName = asset
        self.now_self_attach.enabled = false
        self.now_self_attach.enabled = true
        self.node_list["self_series_effect"]:SetActive(true)
    end
end

function SkillView:OnFlushNowLine()
    local _, series = SkillWGData.Instance:GetCurUpGradeInfo()
    for i = 1, 3 do
        self.node_list["grade_line_" .. i]:SetActive(i == series)
    end
end


--------------------------------------------------------------------------------------------
SkillTypeRender = SkillTypeRender or BaseClass(BaseRender)

function SkillTypeRender:SetType(index)
    self.skill_type = index
end

function SkillTypeRender:LoadCallBack()
    for i = 1, 3 do
        self.node_list["skill_bg_" .. i].button:AddClickListener(BindTool.Bind(self.OnClickSkillIcon, self, i))
    end
end

function SkillTypeRender:OnClickSkillIcon(index)
    self.select_grade = index
    local t = {}
    t.select_type = self.skill_type
    t.select_grade = index
    t.from_view = SkillView.FlushFormView.Click
    ViewManager.Instance:FlushView(GuideModuleName.SkillView, TabIndex.skill_upgrade, nil, t)
end

function SkillTypeRender:OnFlush()
    local grade, series = SkillWGData.Instance:GetCurUpGradeInfo()
    self:OnFlushSelfSeries(series)
    self:FlushSkillInfo(grade)
    self:FlushRed()
end

function SkillTypeRender:FlushRed()
    local grade, series = SkillWGData.Instance:GetCurUpGradeInfo()
    local prof_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
    for i = 1, 3 do
        local limit = SkillWGData.Instance:GetSkillUpGradeProfLimit(i)
        if series == 0 or series == self.skill_type then
            if grade >= i then
                self.node_list["icon_remind_0" .. i]:SetActive(false)
            else
                self.node_list["icon_remind_0" .. i]:SetActive(prof_zhuan >= limit)
            end
        else
            self.node_list["icon_remind_0" .. i]:SetActive(false)
        end
    end
end

function SkillTypeRender:OnFlushSelfSeries(series)
    local status = series == self.skill_type
    if series ~= 0 then
        self.node_list["high"]:SetActive(status)
    else
        self.node_list["high"]:SetActive(false)
    end
    self.node_list["cur_select"]:SetActive(status)

    local bundle, asset = ResPath.GetRoleUIImage(status and "skill_grade_name_bg_high" or "skill_grade_name_bg")
    self.node_list["img_grade_title"].image:LoadSprite(bundle, asset, function()
        self.node_list["img_grade_title"].image:SetNativeSize()
    end)
end

function SkillTypeRender:FlushSkillInfo(grade)
    local prof_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
    for i = 1, 3 do
        local prof_limit = SkillWGData.Instance:GetSkillUpGradeProfLimit(i)
        self.node_list["need_level_bg_" .. i]:SetActive(prof_zhuan < prof_limit)

        if self.node_list["arrow_" .. i-1] then
            XUI.SetGraphicGrey(self.node_list["arrow_" .. i-1], prof_zhuan < prof_limit)
        end

        local status = i > grade
        XUI.SetGraphicGrey(self.node_list["skill_icon_" .. i], status)
        if status then
            self.node_list["txt_need_level_" .. i].text.text = NumberToChinaNumber(prof_limit) .. Language.Skill.Zhuan
        end
    end
end

function SkillTypeRender:SetSkillHigh(select_table)
    local status = self.skill_type == select_table.select_type
    for i = 1, 3 do
        self.node_list["skill_high_" .. i]:SetActive(status and i == select_table.select_grade)
    end
    local _, series = SkillWGData.Instance:GetCurUpGradeInfo()
    if series == 0 then
        self.node_list["high"]:SetActive(status)
    end
end


--------------------------------------------------------------------------------------------------
SkillTypeInfoRender = SkillTypeInfoRender or BaseClass(BaseRender)

function SkillTypeInfoRender:OnFlush()
    if not self.data then
        return
    end

    local bundle, asset = ResPath.GetSkillIconById(SkillWGData.Instance:GetSkillIconId(self.data.skill_id))
    self.node_list["img_skill_icon"].image:LoadSprite(bundle, asset)
    self.node_list["Text"].text.text = ToColorStr(self.data.skill_name, COLOR3B.RED) .. "：" .. (self.data.desc or "")
end