DiscountPurchaseWGData = DiscountPurchaseWGData or BaseClass()

function DiscountPurchaseWGData:__init()
	if DiscountPurchaseWGData.Instance then
		ErrorLog("[DiscountPurchaseWGData]:Attempt to create singleton twice!")
		return
	end
	DiscountPurchaseWGData.Instance = self

	local cfg_auto = ConfigManager.Instance:GetAutoConfig("popup_gift2_cfg_auto")
	if cfg_auto then
		self.gift_info_cfg_list = ListToMap(cfg_auto.gift,"gift_grade","gift_id")
		self.condition_cgf_list = ListToMap(cfg_auto.condition, "gift_grade")
	end
	self.show_tip = true

	self:InitGradeData()
end

function DiscountPurchaseWGData:__delete()
	DiscountPurchaseWGData.Instance = nil
end

-- 初始化当前数据
function DiscountPurchaseWGData:InitGradeData()
	self.show_data = self:CreateShowData()
end

-- 创建展示数据
function DiscountPurchaseWGData:CreateShowData()
	local info = {}
	info.grade = 0
	info.end_time = 0
	info.gift = nil
	info.gift_data = nil
	info.title_name = ""
	return info
end

-- 刷新弹出数据(服务器)
function DiscountPurchaseWGData:RefreshPopupGiftData(protocol)
	self.show_data.grade = protocol.grade
	self.show_data.end_time = protocol.end_time
	self.show_data.gift = protocol.gift
	self:RefreshCurrGradeData()
end

-- 获取当前的礼包数据
function DiscountPurchaseWGData:RefreshCurrGradeData()
	if (not self.show_data) or (self.show_data.grade == 0) or (not self.gift_info_cfg_list) then
		return
	end

	local condition_cfg = self.condition_cgf_list[self.show_data.grade]
	local gift_cfg = self.gift_info_cfg_list[self.show_data.grade]

	if (not condition_cfg) or (not gift_cfg) then
		return
	end

	self.show_data.title_name = condition_cfg.gift_type

	local gift_data = {}
	for _, cfg_data in pairs(gift_cfg) do
		local temp_data = {}
		temp_data.title_name = cfg_data.gift_name
		temp_data.discount = cfg_data.gift_dis

		local price = RoleWGData.GetPayMoneyStr(cfg_data.RMB, cfg_data.rmb_type, cfg_data.rmb_seq)
		temp_data.price = price

		local orgPriceStr = RoleWGData.GetPayMoneyStr(cfg_data.org_rmb, cfg_data.rmb_type, cfg_data.rmb_seq)
		temp_data.org_price = orgPriceStr

		temp_data.RMB = cfg_data.RMB
		temp_data.rmb_type = cfg_data.rmb_type
		temp_data.rmb_seq = cfg_data.rmb_seq
		temp_data.gift_id = cfg_data.gift_id
		temp_data.num = cfg_data.num

		local buy_num = self.show_data.gift and self.show_data.gift[cfg_data.gift_id + 1] or 0
		local is_limit = buy_num >= cfg_data.num
		temp_data.is_buy = is_limit

		local can_buy_num = cfg_data.num - buy_num
		temp_data.can_buy_num = can_buy_num

		local item_list = {}
		if cfg_data.reward_item then
			for key, item_data in pairs(cfg_data.reward_item) do
				table.insert(item_list, item_data)
			end
		end

		temp_data.item_list = item_list
		table.insert(gift_data, temp_data)
	end

	self.show_data.gift_data = gift_data
end

-- 获取对应的礼包配置
function DiscountPurchaseWGData:GetCurrPopupConditionCfg()
	if (not self.show_data) or (self.show_data.grade == 0) or (not self.show_data.gift_data) then
		return nil
	end
	-- print_error("GetCurrPopupConditionCfg",(self.condition_cgf_list or {})[self.show_data.grade])
	return (self.condition_cgf_list or {})[self.show_data.grade]
end

-- 获取对应的礼包配置
function DiscountPurchaseWGData:GetCurrPopupGiftCfg()
	if (not self.show_data) or (self.show_data.grade == 0) or (not self.show_data.gift_data) then
		return nil
	end
	-- print_error("GetCurrPopupGiftCfg",(self.gift_info_cfg_list or {})[self.show_data.grade])
	return (self.gift_info_cfg_list or {})[self.show_data.grade]
end

-- 获取当前的礼包数据
function DiscountPurchaseWGData:GetCurrShowData()
	return self.show_data
end

-- 获取一折弹窗礼包时间
function DiscountPurchaseWGData:GetPopupGiftEndTime()
	return (self.show_data or {}).end_time or 0
end


-- 获取当前的是否还存在可以购买的东西
function DiscountPurchaseWGData:CkeckCurrGradeCanBuy()
	if (not self.show_data) or (self.show_data.grade == 0) or (not self.show_data.gift_data) then
		return false
	end

	for _, data in ipairs(self.show_data.gift_data) do
		if not data.is_buy then
			return true
		end
	end

	return false
end


--- 检测一折弹窗礼包是否满足灵玉不足的条件
function DiscountPurchaseWGData:CheckNeedNoGoldPopupGift()
	if not self.condition_cgf_list then
		return false, 0
	end

	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or 0   -- 开服天数
	local role_lv = RoleWGData.Instance:GetRoleLevel() or 0
	for _, condition_data in pairs(self.condition_cgf_list) do
		if condition_data and condition_data.popup_type == 1 and open_day >= condition_data.open_day and role_lv >= condition_data.role_level then
			return true, condition_data.gift_grade
		end
	end

	return false, 0
end

--- 检测一折弹窗礼包是否满足灵玉不足的条件
function DiscountPurchaseWGData:CheckNeedOpenViewPopupGift(act_id)
	if not self.condition_cgf_list then
		return false, 0
	end

	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or 0   -- 开服天数
	local role_lv = RoleWGData.Instance:GetRoleLevel() or 0

	for _, condition_data in pairs(self.condition_cgf_list) do
		
		if condition_data and condition_data.popup_type == 3 and open_day >= condition_data.open_day and 
		role_lv >= condition_data.role_level and condition_data.popup_condition == act_id then
			local item_num = ItemWGData.Instance:GetItemNumInBagById(condition_data.popup_condition2)
			if item_num <= 0 then
				return true, condition_data.gift_grade
			end
		end
	end
	
	return false, 0
end

-- 获取是否可以打开一折弹窗界面
function DiscountPurchaseWGData:checkCanOpenDiscountPurchaseView()
	local end_time = self:GetPopupGiftEndTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local is_time_out = server_time > end_time
	local is_can_buy = self:CkeckCurrGradeCanBuy()

	return end_time > 0 and is_can_buy and (not is_time_out)
end

--获取是否已经打开过主界面左下角小窗口
function DiscountPurchaseWGData:GetIsShowTip()
	local is_show = false
    if self.show_tip and self:checkCanOpenDiscountPurchaseView() then
		is_show = true
	end

	return is_show
end

function DiscountPurchaseWGData:SetIsShowTip(state)
	self.show_tip = state
end

-- 有没有能买的限时礼包
function DiscountPurchaseWGData:HasCanBuyGift()
	local gift_data = self.show_data.gift_data and self.show_data.gift_data[1]
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if gift_data then
		if gift_data.can_buy_num > 0 and self.show_data.end_time > server_time then
			return true
		end
	end
	return false
end
