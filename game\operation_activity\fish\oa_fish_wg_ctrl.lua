require("game/operation_activity/fish/oa_fish_wg_data")
require("game/operation_activity/fish/oa_fish_record")
require("game/operation_activity/fish/oa_fish_fanli")
require("game/operation_activity/fish/oa_getreward")

OAFishWGCtrl = OAFishWGCtrl or BaseClass(BaseWGCtrl)

function OAFishWGCtrl:__init()
	if OAFishWGCtrl.Instance then
		ErrorLog("[OAFishWGCtrl] Attemp to create a singleton twice !")
	end
	OAFishWGCtrl.Instance = self
	self:RegisterAllProtocols()

	self.data = OAFishWGData.New()
	self.record_view = OAFishRecord.New(GuideModuleName.OAFishRecord)
	self.fanli_view = OAFishFanLiView.New(GuideModuleName.OAFishFanLiView)
	self.getreward_view = OATipsGetRewardView.New(GuideModuleName.OATipsGetRewardView)
	
	OperationActivityWGCtrl.Instance:ListenHotUpdate("config/auto_new/operation_activity_luck_charm_auto", BindTool.Bind(self.UpdateConfig, self))
end

function OAFishWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.record_view:DeleteMe()
	self.record_view = nil

	self.fanli_view:DeleteMe()
	self.fanli_view = nil
    if self.fish_alert then
        self.fish_alert:DeleteMe()
        self.fish_alert = nil
    end
	self.getreward_view:DeleteMe()
	self.getreward_view = nil

	OAFishWGCtrl.Instance = nil
end

function OAFishWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCOALuckCharmInfo,'OnSCOALuckCharmInfo')
    self:RegisterProtocol(SCOALuckCharmRecordListInfo,'OnSCOALuckCharmRecordListInfo')
    self:RegisterProtocol(SCOALuckCharmDrawRewardInfo,'OnSCOALuckCharmDrawRewardInfo')
    self:RegisterProtocol(SCPersonBaoDiRewardDrawInfo,'OnSCPersonBaoDiRewardDrawInfo')
end

function OAFishWGCtrl:OnSCPersonBaoDiRewardDrawInfo(protocol)
    self.data:SaveDrawInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.OperationActivityView, TabIndex.operation_act_fish, "fish", {normal = true})
end

function OAFishWGCtrl:OnSCOALuckCharmDrawRewardInfo(protocol)
	-- print_error("OnSCOALuckCharmDrawRewardInfo", protocol)

	local data_list = self.data:CalDrawRewardList(protocol)
	local time = OAFishWGData.Instance:GetNoticeDelayTime()
	GlobalTimerQuest:AddDelayTimer(function()
		if #data_list > 0 then
			self:OpenRewardView(data_list)
		end
	end, time - 1.5)
	ViewManager.Instance:FlushView(GuideModuleName.OperationActivityView, TabIndex.operation_act_fish, "fish", {draw = true,["count"] = #data_list})
end

function OAFishWGCtrl:OpenRewardView(item_list)
	ViewManager.Instance:FlushView(GuideModuleName.OperationActivityView, TabIndex.operation_act_fish, "fish", {param = "set_draw_flag"})
	self.getreward_view:SetData(item_list)
	ViewManager.Instance:Open(GuideModuleName.OATipsGetRewardView)
end

function OAFishWGCtrl:OnSCOALuckCharmInfo(protocol)
	local old_draw_times = self.data:GetCurDrawNum()
	self.data:SetDrawInfo(protocol)
	if old_draw_times < 0 then
		ViewManager.Instance:FlushView(GuideModuleName.OperationActivityView, TabIndex.operation_act_fish, "fish", {flushview = true})
	elseif protocol.person_draw_count > old_draw_times then
	else
		ViewManager.Instance:FlushView(GuideModuleName.OperationActivityView, TabIndex.operation_act_fish, "fish", {normal = true})
	end
	ViewManager.Instance:FlushView(GuideModuleName.OAFishFanLiView)
	RemindManager.Instance:Fire(RemindName.OAFish)
end

function OAFishWGCtrl:OnSCOALuckCharmRecordListInfo(protocol)
	self.data:SetRecordInfo(protocol)
	self.data:CalNewRecordNum()
	ViewManager.Instance:FlushView(GuideModuleName.OAFishRecord)
	ViewManager.Instance:FlushView(GuideModuleName.OperationActivityView, TabIndex.operation_act_fish, "fish", {record = true})
end

--2257
function OAFishWGCtrl:SendOpera(opera_type, param_1, param_2, param_3)
    local t = {}
    t.rand_activity_type = ACTIVITY_TYPE.OPERA_ACT_FISH
    t.opera_type = opera_type
    t.param_1 = param_1
    t.param_2 = param_2
    t.param_3 = param_3
    ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(t)
end

function OAFishWGCtrl:UpdateConfig()
	self.data:LoadCfg()
	ViewManager.Instance:FlushView(GuideModuleName.OperationActivityView, TabIndex.operation_act_fish, "fish", {flushview = true})
end

function OAFishWGCtrl:GetFishAlert()
	if not self.fish_alert then
        self.fish_alert = Alert.New()
        self.fish_alert:SetShowCheckBox(true, "fish_click")
    end
end

function OAFishWGCtrl:OnClickBtn(btn_index)
    local grade_cfg, has_next, end_time = OAFishWGData.Instance:GetCurActGradeCfg()
    local btn_list = OAFishWGData.Instance:GetCurConsumeCfg(grade_cfg)
    if not btn_list then
        return
    end
	local cfg = btn_list[btn_index]
    local has_num = ItemWGData.Instance:GetItemNumInBagById(grade_cfg.consume_item)
    local send_num = cfg.onekey_lotto_num
    local show_num = cfg.consume_count
    
    local sp_guarantee_x,sp_guarantee_n,sp_enter_num = OAFishWGData.Instance:GetSpGuarantee()
    local reward_pool_id = grade_cfg.reward
    local sp_max_num = OAFishWGData.Instance:GetGuaranteeListCount(reward_pool_id)
    
    if grade_cfg.sp_guarantee_finish == 1 and sp_guarantee_n >= (grade_cfg.sp_guarantee_n - 1) and
        sp_enter_num == (sp_max_num - 1) and grade_cfg.sp_guarantee_x - sp_guarantee_x < send_num then
        
        send_num = grade_cfg.sp_guarantee_x - sp_guarantee_x
        show_num = send_num 
    end

    if has_num < show_num then
        if grade_cfg.complement_num > 0 then
            self:GetFishAlert()
            local cfg = ItemWGData.Instance:GetItemConfig(grade_cfg.consume_item)
            local dif_num = show_num - has_num
            local str = string.format(Language.OAFish.AlertStr, ToColorStr(cfg.name,ITEM_COLOR[cfg.color]) , dif_num * grade_cfg.complement_num)
            self.fish_alert:SetLableString(str)
            self.fish_alert:SetCheckBoxText(Language.OAFish.AlertCheckStr)
            self.fish_alert:SetCheckBoxDefaultSelect(false)
            self.fish_alert:SetOkFunc(function ()
                self:SendFishDraw(send_num, 1, grade_cfg, btn_index)
            end)
            self.fish_alert:Open()
        else
            TipWGCtrl.Instance:OpenItemTipGetWay({item_id = grade_cfg.consume_item})
        end
    else
        self:SendFishDraw(send_num, 0, grade_cfg, btn_index)
    end
end

function OAFishWGCtrl:SendFishDraw(num, need_gold, grade_cfg, btn_index)
    if OperationActivityWGCtrl.Instance:GetFishDrawFlag() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.OAFish.RepeatTurnTableTips)
        return
    end
    OperationActivityWGCtrl.Instance:OnClickFishDrawFlag()
    UITween.CleanAllTween(OAFishWGData.Mark_Tween_Str)
    OAFishWGData.Instance:CacheOrGetFishDrawCfg(grade_cfg)
    OAFishWGData.Instance:CacheOrGetFishDrawIndex(btn_index)
    OAFishWGCtrl.Instance:SendOpera(OA_LUCK_CHARM_OP_TYPE.DRAW, num, need_gold)
end