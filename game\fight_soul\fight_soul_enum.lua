-- 战魂数量枚举
FIGHT_SOUL_NUM = {
	MAX_ACT_SUIT = 3,				-- 最大激活套装
	MAX_SUIT_NUM = 3,				-- 最大套装数
	MAX_BATCH_NUM = 20,				-- 最大批量数
}

-- 战魂操作类型
FIGHT_SOUL_OP_TYPE = {
	FIGHT_SOUL_UPLEVEL = 0,		-- 战魂升级 param1 = 位索引
	FIGHT_SOUL_BREAK = 1,		-- 战魂突破 param1 = 位索引
	FIGHT_SOUL_WEAR = 2,		-- 战魂穿戴 param1 = 位索引， param1 = 背包索引
	EXP_POOL_UPLEVEL = 3,		-- 经验池升级
	YUGAO_FETCH = 4,			-- 预告领取 param1 = 索引
	YUGAO_UNLOCK = 5,			-- 预告解锁
	SIXIANG_CALL = 6,			-- 出战 param1 = 位索引
	BONE_UPLEVEL = 7,			-- 魂骨升级 param1 = 四象槽, param2 = 魂骨部位
	BONE_WEAR = 8,				-- 魂骨穿戴 param1 = 四象槽, param2 = 魂骨部位, param3 = 背包索引
	BONE_PUT_DOWN = 9,			-- 魂骨卸下 param1 = 四象槽, param2 = 魂骨部位, param3 = 背包索引
	SLOT_UNLOCK = 10,			-- 槽位解锁 param1 = 位索引
	FIGHT_SOUL_RESTORE = 11,	-- 还原 param1 = 背包索引
	FAKE_LOCK_PROTOCOL = 12,	-- 假协议-功能解锁
	BONE_ONE_KEY_UPLEVEL = 13,	-- 魂骨一键升级 param1 = 四象槽,
}

-- 战魂升星、升品操作类型
FIGHT_SOUL_COMPOSE_TYPE = {
	STUFF_NUM = 10,
	FIGHT_SOUL = 1,				-- 元神 param1 = 背包索引, param_list = 子材料背包索引列表
	BONE = 2,					-- 魂骨 param1 = 背包索引, param_list = 子材料背包索引列表
}

-- 战魂材料
FIGHT_SOUL_STUFF_TYPE = {
	SAME_TYPE = 1,
	NOSAME_TYPE_1 = 2,
	NOSAME_TYPE_2 = 3,
 }

FIGHT_SOUL_TYPE = {
	QL = 1,			-- 青龙
	BH = 2,			-- 白虎
	ZQ = 3,			-- 朱雀
	XW = 4,			-- 玄武
	MAX = 4,
}

FIGHT_SOUL_SKILL = {
	QL = 5001,			-- 青龙
	BH = 5002,			-- 白虎
	ZQ = 5003,			-- 朱雀
	XW = 5004,			-- 玄武
}

-- 战魂魂骨类型
FIGHT_SOUL_BONE_TYPE = {
	BODY = -1,								-- 战魂主体
	HEAD = 0,								-- 战魂头部
	BACK = 1,								-- 战魂后背
	SHOULDER = 2,							-- 战魂肩部
	HAND = 3,								-- 战魂爪部
	BREAST = 4,								-- 战魂胸部
	TAIL = 5,								-- 战魂尾部
	MAX = 6,
}

-- 战魂需要一起加载完再播动画的部位
FIGHT_SOUL_BONE_ANI_PART = {
	[FIGHT_SOUL_BONE_TYPE.BODY] = SceneObjPart.Main,
	[FIGHT_SOUL_BONE_TYPE.BACK] = SceneObjPart.FightSoulBack,
	[FIGHT_SOUL_BONE_TYPE.SHOULDER] = SceneObjPart.FightSoulShoulder,
	[FIGHT_SOUL_BONE_TYPE.HAND] = SceneObjPart.FightSoulHand,
	[FIGHT_SOUL_BONE_TYPE.BREAST] = SceneObjPart.FightSoulBreast,
	[FIGHT_SOUL_BONE_TYPE.TAIL] = SceneObjPart.FightSoulTail,
}

-- 战魂魂骨部位位置
FIGHT_SOUL_BONE_DIR = {
	BEFORE_LEFT = 0,						-- 前左
	BEFORE_RIGHT = 1,						-- 前右
	AFTER_LEFT = 2,							-- 后左
	AFTER_RIGHT = 3,						-- 后右
}

-- 战魂元神位更新原因
FIGHT_SOUL_SLOT_SEND_REASON = {
	LOGIN = 0,					-- 下发原因-登录
	OPERA = 1,					-- 下发原因-主动请求
	USE_EXP = 2,				-- 下发原因-使用经验
	ADD_EXP = 3,				-- 下发原因-添加经验
	PUT_ON = 4,					-- 下发原因-穿戴
	EXP_POOL_UPLEVEL = 5,		-- 下发原因-经验池升级
	OUT_FIGHT = 6,				-- 下发原因-出战

	ADD_EXP_DECOMPOSE = 7,		-- 下发原因-分解
	REPLACE = 8,				-- 下发原因-替换
	UPLEVEL = 9,				-- 下发原因-升级成功
	UPGRADE = 10,				-- 下发原因-突破成功
	SLOT_UNLOCK = 11,			-- 下发原因-槽位解锁
}

-- 战魂元神背包更新原因
FIGHT_SOUL_BAG_SEND_REASON = {
	ALL = 0,					-- 下发原因-全部
	ADD_ITEM = 1,				-- 下发原因-获得
	REMOVE_ITEM = 2,			-- 下发原因-移除
	UPLEVEL_OR_UPGRADE = 3,		-- 下发原因-升级or突破
	UP_COLOR_STAR = 4,			-- 下发原因-升品升星成功
	SIXIANG_CALL_GET = 5,		-- 下发原因-四象召唤获得
}

-- 战魂魂骨位更新原因
FIGHT_SOUL_BONE_SLOT_SEND_REASON = {
	LOGIN = 0,					-- 下发原因-登录
	OPERA = 1,					-- 下发原因-主动请求
	PUT_ON = 2,					-- 下发原因-穿戴、替换
	UPLEVEL = 3,				-- 下发原因-升级成功
	PUT_DOWN = 4,				-- 下发原因-卸下
}

-- 战魂魂骨背包更新原因
FIGHT_SOUL_BONE_BAG_SEND_REASON = {
	ALL = 0,					-- 下发原因-全部
	ADD_ITEM = 1,				-- 下发原因-获得
	REMOVE_ITEM = 2,			-- 下发原因-移除
	UP_COLOR_STAR = 3,			-- 下发原因-升品升星成功
	SIXIANG_CALL_GET = 4,		-- 下发原因-四象召唤获得
}

FIGHT_SOUL_BONE_BATCH_DATA_STATE = {
	IS_WEAR = 0,
	CAN_SELECT = 1,
	CAN_NOT_SELECT = 2,
}
