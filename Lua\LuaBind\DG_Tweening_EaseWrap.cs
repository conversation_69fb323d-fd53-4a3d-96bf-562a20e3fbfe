﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class DG_Tweening_EaseWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>(typeof(DG.Tweening.Ease));
		<PERSON><PERSON>("Unset", get_Unset, null);
		<PERSON><PERSON>("Linear", get_Linear, null);
		<PERSON><PERSON>("InSine", get_InSine, null);
		<PERSON><PERSON>("OutSine", get_OutSine, null);
		<PERSON><PERSON>("InOutSine", get_InOutSine, null);
		<PERSON><PERSON>("InQuad", get_InQuad, null);
		<PERSON><PERSON>("OutQuad", get_OutQuad, null);
		<PERSON><PERSON>("InOutQuad", get_InOutQuad, null);
		<PERSON><PERSON>("InCubic", get_InCubic, null);
		<PERSON><PERSON>ar("OutCubic", get_OutCubic, null);
		<PERSON><PERSON>("InOutCubic", get_InOutCubic, null);
		<PERSON><PERSON>("InQuart", get_InQuart, null);
		<PERSON><PERSON>("OutQuart", get_OutQuart, null);
		<PERSON><PERSON>("InOutQuart", get_InOutQuart, null);
		L.RegVar("InQuint", get_InQuint, null);
		L.RegVar("OutQuint", get_OutQuint, null);
		L.RegVar("InOutQuint", get_InOutQuint, null);
		L.RegVar("InExpo", get_InExpo, null);
		L.RegVar("OutExpo", get_OutExpo, null);
		L.RegVar("InOutExpo", get_InOutExpo, null);
		L.RegVar("InCirc", get_InCirc, null);
		L.RegVar("OutCirc", get_OutCirc, null);
		L.RegVar("InOutCirc", get_InOutCirc, null);
		L.RegVar("InElastic", get_InElastic, null);
		L.RegVar("OutElastic", get_OutElastic, null);
		L.RegVar("InOutElastic", get_InOutElastic, null);
		L.RegVar("InBack", get_InBack, null);
		L.RegVar("OutBack", get_OutBack, null);
		L.RegVar("InOutBack", get_InOutBack, null);
		L.RegVar("InBounce", get_InBounce, null);
		L.RegVar("OutBounce", get_OutBounce, null);
		L.RegVar("InOutBounce", get_InOutBounce, null);
		L.RegVar("Flash", get_Flash, null);
		L.RegVar("InFlash", get_InFlash, null);
		L.RegVar("OutFlash", get_OutFlash, null);
		L.RegVar("InOutFlash", get_InOutFlash, null);
		L.RegVar("INTERNAL_Zero", get_INTERNAL_Zero, null);
		L.RegVar("INTERNAL_Custom", get_INTERNAL_Custom, null);
		L.RegFunction("IntToEnum", IntToEnum);
		L.EndEnum();
		TypeTraits<DG.Tweening.Ease>.Check = CheckType;
		StackTraits<DG.Tweening.Ease>.Push = Push;
	}

	static void Push(IntPtr L, DG.Tweening.Ease arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(DG.Tweening.Ease), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Unset(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.Unset);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Linear(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.Linear);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InSine(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.InSine);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OutSine(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.OutSine);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InOutSine(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.InOutSine);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InQuad(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.InQuad);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OutQuad(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.OutQuad);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InOutQuad(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.InOutQuad);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InCubic(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.InCubic);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OutCubic(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.OutCubic);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InOutCubic(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.InOutCubic);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InQuart(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.InQuart);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OutQuart(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.OutQuart);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InOutQuart(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.InOutQuart);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InQuint(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.InQuint);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OutQuint(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.OutQuint);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InOutQuint(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.InOutQuint);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InExpo(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.InExpo);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OutExpo(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.OutExpo);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InOutExpo(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.InOutExpo);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InCirc(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.InCirc);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OutCirc(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.OutCirc);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InOutCirc(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.InOutCirc);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InElastic(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.InElastic);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OutElastic(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.OutElastic);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InOutElastic(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.InOutElastic);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InBack(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.InBack);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OutBack(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.OutBack);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InOutBack(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.InOutBack);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InBounce(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.InBounce);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OutBounce(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.OutBounce);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InOutBounce(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.InOutBounce);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Flash(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.Flash);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InFlash(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.InFlash);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OutFlash(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.OutFlash);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InOutFlash(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.InOutFlash);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_INTERNAL_Zero(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.INTERNAL_Zero);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_INTERNAL_Custom(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.Ease.INTERNAL_Custom);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		DG.Tweening.Ease o = (DG.Tweening.Ease)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

