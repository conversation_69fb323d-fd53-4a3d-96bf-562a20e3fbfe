AudioWGData = AudioWGData or BaseClass()
AudioUrl = 
{
	Advanced1 = "Advanced1",
	Levelup = "Levelup",
	Advanced = "Advanced",
	DefaultOpen = "DefaultOpen",
	DefaultClose = "DefaultClose",
	SystemLevelUp = "SystemLevelUp",
	Rewards = "Rewards",
	FemaleDeath = "FemaleDeath",
	MaleDeath = "MaleDeath",
	Flight = "Flight",
	GoldCoinsFall = "GoldCoinsFall",
	DecomPosition = "DecomPosition",
	HorseshoeSound = "HorseshoeSound",
	Run = "Run",
	WetlandRunning = "WetlandRunning",
	PowerUp = "PowerUp",
	ShengLi = "ShengLi",
	MissionFailed = "MissionFailed",
	ReceiveMission = "ReceiveMission",
	PawWalking = "PawWalking",
	StrenthenFail = "StrenthenFail",
	DropItem1001 = "DropItem1001",
	ChuanSong = "ChuanSong",
	MoveMale1 = "MoveMale1",
	MoveMale2 = "MoveMale2",
	MoveMale3 = "MoveMale3",
	MoveFemale1 = "MoveFemale1",
	MoveFemale2 = "MoveFemale2",
	MoveFemale3 = "MoveFemale3",
	HouseShengXing = "HouseShengXing",
	ShengJi = "ShengJi",

	EffectJingYanQiFu = "EffectJingYanQiFu",
	EffectXianYuHuoDe = "EffectXianYuHuoDe",
	EffectZhanChangKaiShi = "EffectZhanChangKaiShi",

	Sound20 = "Sound20",
	Sound21 = "Sound21",
	Sound22 = "Sound22",
	Sound23 = "Sound23",
	Sound24 = "Sound24",

	XinJiNengKaiQi = "XinJiNengKaiQi",
	XinShouFuBen = "XinShouFuBen",
	UiXunBao = "UiXunBao",
	UiGuoZhan = "UiGuoZhan",
	Kun = "Kun",
	HuanYinYe = "HuanYinYe",
	XianZhuoZi = "XianZhuoZi",
	FirstGetTS = "FirstGetTS",
	ChuiShao = "ChuiShao",

	Thunder = "Thunder",
	Jump = "Jump",
	Fly = "Fly",
	Hawk = "Hawk",
	Down = "Down",

	Dujie_Jin = "Dujie_Jin",
	Dujie_Hong = "Dujie_Hong",

	ChongZhi = "ChongZhi",
}
--AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.ChuiShao))

function AudioWGData:__init()
	if AudioWGData.Instance then
		print_error("[AudioWGData] Attemp to create a singleton twice !")
	end
	AudioWGData.Instance = self
end

function AudioWGData:__delete()
	AudioWGData.Instance = nil
end

function AudioWGData:GetAudioConfig()
	if not self.audio_config then
		self.audio_config = ConfigManager.Instance:GetAutoConfig("audio_auto")
	end
	return self.audio_config
end

function AudioWGData:GetOtherCfg()
	local audio_cfg = self:GetAudioConfig()
	return audio_cfg and audio_cfg.other[1]
end

function AudioWGData:GetOtherCfgKeyData(key)
	local auto_cfg = self:GetOtherCfg()
	if auto_cfg and auto_cfg[key] ~= "" then
		return auto_cfg[key]
	end

	return nil
end

--获取音效
function AudioWGData:GetOtherAutioEffect(key, is_from_mount, is_from_view)
	local auto_cfg = self:GetOtherCfg()
	if nil == auto_cfg then
		return nil, nil
	end

	local name
	if not is_from_mount then
		name = auto_cfg[key]
		if nil == name or name == "" then
			return nil, nil
		end
	elseif type(key) == "number" and key == 0 then
		return nil, nil
	else 
		name = key
	end

	local bundle, asset = ResPath.GetOtherVoiceRes(name)
	if is_from_view then
		bundle, asset = ResPath.GetUisVoiceRes(name)
	end
	return bundle, asset
end