FestivalActBtn = FestivalActBtn or BaseClass(MainActivityBtn)

function FestivalActBtn:__init(instance)
	self:BindGlobalEvent(ACTIVITY_BTN_EVENT.MERGE_ACT_BTN_CHANGE, BindTool.Bind(self.Button<PERSON>hange, self))
end

function FestivalActBtn:LoadCallBack()
	self:ButtonChange()
	self:SetBgActive(false)
end

function FestivalActBtn:CreateBtnAsset(parent)
	self:LoadAsset("uis/view/festival_activity_ui_prefab", "merge_act_btn", parent.transform, function(obj)
		self.node_list.SpecialEff:SetActive(true)
	end)
end

function FestivalActBtn:ButtonChange()
	if self:IsNil() then
		return
	end

	local show_new_flag = FestivalActivityWGData.Instance:GetIsShowMainBtnNewTxt()
	self:SetTxtNewStatus(show_new_flag == 1)
end

function FestivalActBtn:SetTxtNewStatus(status)
	if not self.node_list then return end
	self.node_list["txt_new"]:SetActive(status)
end