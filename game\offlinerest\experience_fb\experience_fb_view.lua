ExperienceFbView = ExperienceFbView or BaseClass(SafeBaseView)

local SHOW_QUICK_POS = {
	QUICK = Vector3(190, -8, 0),
	NOT_QUICK = Vector3(280, -8, 0),
}

function ExperienceFbView:__init()
    self.view_style = ViewStyle.Full
    self:SetMaskBg(false)

    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
    self:AddViewResource(0, "uis/view/offlinerest_ui_prefab", "layout_experience_fb_view")
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_light_common_top_panel")
end

function ExperienceFbView:OpenCallBack(index)
	-- 打开界面获取一下排行榜
	local level = ExperienceFbWGData.Instance:GetCurrExpWestLevel()
	ExperienceFbWgCtrl.Instance:RequestExpWestRankInfo(level)
end

function ExperienceFbView:ReleaseCallBack()
    if self.mid_fb_list and #self.mid_fb_list > 0 then
		for _, render_cell in ipairs(self.mid_fb_list) do
			render_cell:DeleteMe()
			render_cell = nil
		end

		self.mid_fb_list = nil
	end

	if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end

	if self.show_reward_item_list then
        self.show_reward_item_list:DeleteMe()
        self.show_reward_item_list = nil
    end

	self.curr_level = nil
	self.curr_wave = nil
	
	if self.act_change then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
		self.act_change = nil
	end
	self:RemoveCountDown()
end

function ExperienceFbView:CloseCallBack()

end

function ExperienceFbView:LoadCallBack()
	local bundle, asset = ResPath.GetRawImagesJPG("a3_ll_bj")
	self.node_list.RawImage_tongyong.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.RawImage_tongyong.raw_image:SetNativeSize()
	end)

    self.node_list.title_view_name.text.text = Language.OfflineRest.ExperienceFbTitleName

	-- 副本
	if not self.mid_fb_list then
		self.mid_fb_list = {}

		for i = 1, 8 do
			local fb_item_obj = self.node_list.mid_fb_list:FindObj(string.format("fb_item_%d", i))
			if fb_item_obj then
				local cell = ExperienceFbWaveRender.New(fb_item_obj)
				cell:SetIndex(i)
				cell:SetClickCallBack(BindTool.Bind(self.ShowWaveReward, self))
				self.mid_fb_list[i] = cell
			end
		end
	end

    if not self.item_list then
	    self.item_list = AsyncListView.New(ItemCell, self.node_list.reward_item_list)
		self.item_list:SetStartZeroIndex(true)
    end

	if not self.show_reward_item_list then
	    self.show_reward_item_list = AsyncListView.New(ItemCell, self.node_list.show_reward_item_list)
		self.show_reward_item_list:SetStartZeroIndex(true)
    end

	XUI.AddClickEventListener(self.node_list.quick_pass_btn, BindTool.Bind(self.OnClickQuickPass, self))
    XUI.AddClickEventListener(self.node_list.start_challenge_btn, BindTool.Bind(self.OnClickChallenge, self))
	XUI.AddClickEventListener(self.node_list.stage_change_left_btn, BindTool.Bind(self.OnClickQuickStageLeft, self))
    XUI.AddClickEventListener(self.node_list.stage_change_right_btn, BindTool.Bind(self.OnClickQuickStageRight, self))
	XUI.AddClickEventListener(self.node_list.pass_rank_btn, BindTool.Bind(self.OnClickPassRank, self))
	XUI.AddClickEventListener(self.node_list.stage_buff_show_btn, BindTool.Bind(self.OnClickStageBuffShow, self))
	XUI.AddClickEventListener(self.node_list.close_show_item_btn, BindTool.Bind(self.OnClickShowWaveList, self))
	XUI.AddClickEventListener(self.node_list.btn_dreamland, BindTool.Bind(self.OnClickDreamlandBtn, self))
	
	self.act_change = BindTool.Bind(self.OnActivityChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)
	self:ActivityTimeCountDown()
end

function ExperienceFbView:OnFlush(param_t)
	for k, v in pairs(param_t) do
        if k == "all" then
			self:FlushLevelMessage()
            self:FlushWaveList()
			self:FlushLevelChangeStatus()
        elseif k == "flush_stage" then
			self:FlushLevelChangeStatus()
            self:FlushLevelMessage()
        elseif k == "flush_wave_list" then
            self:FlushWaveList()
		elseif k == "flush_pass_time" then
			self.node_list.curr_stage_pass_root:CustomSetActive(v.fb_pass_time ~= 0)
			self.node_list.curr_stage_tips.text.text = string.format(Language.OfflineRest.ExperienceFbStageTips, TimeUtil.FormatSecond2MS(v.fb_pass_time)) 
        end
    end
end

-- 刷新波次列表
function ExperienceFbView:FlushWaveList()
	if not self.curr_level then
		return
	end

	local max_lv = ExperienceFbWGData.Instance:GetCurrExpWestLevel()
	local wave_list = ExperienceFbWGData.Instance:GetWaveListByLevel(self.curr_level)
	if self.curr_level == max_lv then
		self.curr_wave = ExperienceFbWGData.Instance:GetCurrExpWestWave()
	else
		self.curr_wave = #wave_list
	end

	local list = ExperienceFbWGData.Instance:LiLianShowWaveList(self.curr_level, self.curr_wave)
    for i, render_cell in ipairs(self.mid_fb_list) do
        render_cell:SetVisible(list[i] ~= nil)

        if list[i] ~= nil then
            render_cell:SetData(list[i])
        end
    end
end

-- 刷新关卡信息
function ExperienceFbView:FlushLevelMessage()
	if self.curr_level == nil then
		self.curr_level = ExperienceFbWGData.Instance:GetCurrExpWestLevel()
	end

	local cfg = ExperienceFbWGData.Instance:GetLevelCfgByLevel(self.curr_level)
	if not cfg then
		return
	end

	--等级压制
    local role_lv = RoleWGData.Instance:GetRoleLevel()
    local color = role_lv >= cfg.need_level and COLOR3B.BLACK or COLOR3B.L_RED
    self.node_list.level_limit_text.text.text = ToColorStr(string.format(Language.OfflineRest.ExperienceFbLvLimit, cfg.need_level), color) 
	
	--战斗力限制
    local role_cap = GameVoManager.Instance:GetMainRoleVo().capability
    color = role_cap >= cfg.need_cap and COLOR3B.BLACK or COLOR3B.L_RED
    self.node_list.cap_limit_text.text.text = ToColorStr(cfg.need_cap, color)
	self.item_list:SetDataList(cfg.pass_reward_item)
	self.node_list.curr_stage_name.text.text = cfg.stage_name
	self.node_list.start_challenge_tips:CustomSetActive(self.curr_level == ExperienceFbWGData.Instance:GetCurrExpWestLevel())

	local is_can_auto = role_cap >= cfg.auto_need_cap
	self.node_list.quick_pass_btn:CustomSetActive(self.curr_level == ExperienceFbWGData.Instance:GetCurrExpWestLevel() and is_can_auto)
	local pos = (self.curr_level == ExperienceFbWGData.Instance:GetCurrExpWestLevel() and is_can_auto) and SHOW_QUICK_POS.QUICK or SHOW_QUICK_POS.NOT_QUICK
	self.node_list.show_other_root.transform.anchoredPosition = pos

	if self.curr_level == ExperienceFbWGData.Instance:GetCurrExpWestLevel() then
		local wave_list = ExperienceFbWGData.Instance:GetWaveListByLevel(self.curr_level)
		local curr_wave = ExperienceFbWGData.Instance:GetCurrExpWestWave()
		local all_wave = #wave_list
		self.node_list.start_challenge_tips.text.text = string.format(Language.OfflineRest.ExperienceFbTips, curr_wave, all_wave)
	end
	-- XUI.SetGraphicGrey(self.node_list.quick_pass_btn, not is_can_auto)
end

-- 切换副本
function ExperienceFbView:FlushLevelChangeStatus()
	if not self.curr_level then
		return
	end

	local max_lv = ExperienceFbWGData.Instance:GetCurrExpWestLevel()
	self.node_list.stage_change_left_btn:CustomSetActive(self.curr_level > 1)
    self.node_list.stage_change_right_btn:CustomSetActive(self.curr_level < max_lv)
	self.node_list.curr_stage_pass_root:CustomSetActive(false)
	ExperienceFbWgCtrl.Instance:RequestExpWestLevelTime(self.curr_level)
end

-- 展示波次奖励信息
function ExperienceFbView:ShowWaveReward(wave_cell)
	if (not wave_cell) or (not wave_cell.data) then
		return
	end

	self.node_list.show_item_over_root:CustomSetActive(true)

	local pos = Vector3.zero
	if wave_cell.view then
		pos = wave_cell.view.transform.position
	end

	self.node_list.show_item_root.transform.position = pos
	local item_data = wave_cell.data.pass_reward_item
	self.show_reward_item_list:SetDataList(item_data)
	local local_pos = self.node_list.show_item_root.transform.localPosition
	local pos_x = -100
	local offset_vec = Vector3(pos_x, 100, 1)
	self.node_list.show_item_root.transform.localPosition = self.node_list.show_item_root.transform.localPosition + offset_vec

	local now_wave = 1
	local is_curr_level = false
	local max_lv = ExperienceFbWGData.Instance:GetCurrExpWestLevel()

	if wave_cell.data.level == max_lv then
		now_wave = ExperienceFbWGData.Instance:GetCurrExpWestWave()
		is_curr_level = true
	elseif wave_cell.data.level < max_lv then
		local wave_list = ExperienceFbWGData.Instance:GetWaveListByLevel(wave_cell.data.level)
		now_wave = #wave_list
		is_curr_level = false
	end

	self.show_reward_item_list:SetRefreshCallback(function(item_cell, cell_index)
		if is_curr_level then
			item_cell:SetLingQuVisible(wave_cell.data.wave < now_wave)
		else
			item_cell:SetLingQuVisible(true)
		end
	end)
end

---------------------------------------------------------------------------
-- 一键通关
function ExperienceFbView:OnClickQuickPass()
	if not self.curr_level then
		return
	end

	local cfg = ExperienceFbWGData.Instance:GetLevelCfgByLevel(self.curr_level)
	if not cfg then
		return
	end

	--战斗力限制
	local role_cap = GameVoManager.Instance:GetMainRoleVo().capability
	local is_can_auto = role_cap >= cfg.auto_need_cap
	if not is_can_auto then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.SmallGoal.NoEnoughPower)
		return
	end

	ExperienceFbWgCtrl.Instance:RequestExpWestAutoDare()
end

-- 开始挑战
function ExperienceFbView:OnClickChallenge()
	if not self.curr_level then
		return
	end

	local cfg = ExperienceFbWGData.Instance:GetLevelCfgByLevel(self.curr_level)
	if not cfg then
		return
	end

	local role_lv = RoleWGData.Instance:GetRoleLevel()
    local is_can_challenge = role_lv >= cfg.need_level 

	if not is_can_challenge then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.HaveNotEnterLevel)
		return
	end

	ExperienceFbWgCtrl.Instance:RequestExpWestDare(self.curr_level)
end

-- 切换关卡←
function ExperienceFbView:OnClickQuickStageLeft()
	if not self.curr_level then
		return
	end

	self.curr_level = self.curr_level - 1
	if self.curr_level < 1 then
		self.curr_level = 1
	end

	ExperienceFbWgCtrl.Instance:RequestExpWestRankInfo(self.curr_level)
	self:Flush()
end

-- 切换关卡→
function ExperienceFbView:OnClickQuickStageRight()
	if not self.curr_level then
		return
	end

	local max_lv = ExperienceFbWGData.Instance:GetCurrExpWestLevel()
	self.curr_level = self.curr_level + 1
	if self.curr_level > max_lv then
		self.curr_level = max_lv
	end

	ExperienceFbWgCtrl.Instance:RequestExpWestRankInfo(self.curr_level)
	self:Flush()
end

-- 查看排行
function ExperienceFbView:OnClickPassRank()
	if not self.curr_level then
		return
	end

	ExperienceFbWgCtrl.Instance:OpenExperienceFbRankView(self.curr_level)
end

-- 查看Buff
function ExperienceFbView:OnClickStageBuffShow()
	if not self.curr_level then
		return
	end

	ExperienceFbWgCtrl.Instance:OpenExperienceFbBuffView(self.curr_level)
end

-- 关闭波次列表信息
function ExperienceFbView:OnClickShowWaveList()
	self.node_list.show_item_over_root:CustomSetActive(false)
end

----------------------------- 幻梦秘境-----------------------

-- 打开幻梦秘境
function ExperienceFbView:OnClickDreamlandBtn()
	PhantomDreamlandWGCtrl.Instance:OpenPhantomDreamlandFbView()
end

--活动时间倒计时
function ExperienceFbView:ActivityTimeCountDown()
	self:RemoveCountDown()
	
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DREAM_SECRET)
	if IsEmptyTable(activity_info) or activity_info.status ~= ACTIVITY_STATUS.OPEN then
		self.node_list["btn_dreamland"]:CustomSetActive(false)
		return
	end
	self.node_list["btn_dreamland"]:CustomSetActive(true)
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	if activity_info.end_time <= now_time then
		self:OnComplete()
		return
    else		
		self:UpdateTimeCallBack(0, activity_info.end_time - now_time)
		CountDownManager.Instance:AddCountDown("exp_dreamland_fb_countdown",
			BindTool.Bind(self.UpdateTimeCallBack, self),
			BindTool.Bind(self.OnComplete, self),
			nil, activity_info.end_time - now_time, 1)
	end
end

function ExperienceFbView:OnComplete()
	self.node_list["txt_dreamland_act_time"].text.text = ""
end

function ExperienceFbView:RemoveCountDown()
	if CountDownManager.Instance:HasCountDown("exp_dreamland_fb_countdown") then
		CountDownManager.Instance:RemoveCountDown("exp_dreamland_fb_countdown")
	end
end

function ExperienceFbView:UpdateTimeCallBack(now_time, total_time)
	local time = math.ceil(total_time - now_time)
	local time_str = TimeUtil.FormatSecondDHM9(time)
	self.node_list["txt_dreamland_act_time"].text.text = string.format(Language.OfflineRest.ActivityTime, time_str)
end

function ExperienceFbView:OnActivityChange(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DREAM_SECRET then
		self.node_list["btn_dreamland"]:CustomSetActive(status == ACTIVITY_STATUS.OPEN)
	end
end

----------------------------副本-----------------------------------------
-- 副本信息
ExperienceFbWaveRender = ExperienceFbWaveRender or BaseClass(BaseRender)
function ExperienceFbWaveRender:LoadCallBack()
end

function ExperienceFbWaveRender:__delete()
end

function ExperienceFbWaveRender:OnFlush()
    if not self.data then
        return
    end


    self.node_list.fb_level.text.text = self.data.name
    self.node_list.fb_name_txt.text.text = string.format(Language.OfflineRest.ExperienceFbGoToTips2, self.data.name)
	local max_lv = ExperienceFbWGData.Instance:GetCurrExpWestLevel()
	local now_wave = 1

	if self.data.level == max_lv then
		now_wave = ExperienceFbWGData.Instance:GetCurrExpWestWave()
	elseif self.data.level < max_lv then
		local wave_list = ExperienceFbWGData.Instance:GetWaveListByLevel(self.data.level)
		now_wave = #wave_list
	end

	local is_pass = self.data.wave <= now_wave
	local is_pass_ed = self.data.wave < now_wave
    XUI.SetGraphicGrey(self.node_list.line_root, not is_pass)
    self.node_list.fb_name_root:CustomSetActive(self.data.wave == now_wave)
	self.node_list.pass_ed:CustomSetActive(is_pass_ed)
	self:OnSelectChange(self.data.wave == now_wave)

	local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.data.monster_id]
    if monster_cfg then
        local asset_name,bundle_name = ResPath.GetBossIcon("wrod_boss_"..monster_cfg.small_icon)
        self.node_list.fb_icon.image:LoadSprite(asset_name,bundle_name,function ()
            self.node_list.fb_icon.image:SetNativeSize()
        end)
    end
end

function ExperienceFbWaveRender:OnSelectChange(is_select)
    self.node_list.normal:CustomSetActive(not is_select)
    self.node_list.select:CustomSetActive(is_select)
end