----------------------------------------------------
-- 仙盟争霸
----------------------------------------------------
XianMengZhengBaView = XianMengZhengBaView or BaseClass(SafeBaseView)

function XianMengZhengBaView:__init()
	self.view_style = ViewStyle.Half
    self.view_layer = UiLayer.Normal
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "layout_open_server_guild_contend")
end

function XianMengZhengBaView:__delete()
end
function XianMengZhengBaView:ReleaseCallBack()
	if self.item_list_1 then
		for k,v in pairs(self.item_list_1) do
			v:DeleteMe()
		end
		self.item_list_1 = {}
	end
	
	if self.item_list_2 then
		for k,v in pairs(self.item_list_2) do
			v:DeleteMe()
		end
		self.item_list_2 = {}
	end
end

function XianMengZhengBaView:LoadCallBack()
	local parent = self.node_list.item_cell_root_1
	local item_list_1 = {}
	for i=1,4 do
		item_list_1[i] = ItemCell.New(parent)
	end
	self.item_list_1 = item_list_1

	local parent = self.node_list.item_cell_root_2
	local item_list_2 = {}
	for i=1,4 do
		item_list_2[i] = ItemCell.New(parent)
	end
	self.item_list_2 = item_list_2
	for i = 1, 3 do
		XUI.AddClickEventListener(self.node_list["lingqu_"..i], BindTool.Bind2(self.OnClickLingQu, self, i))
	end
end

function XianMengZhengBaView:ShowIndexCallBack()
	
end

function XianMengZhengBaView:OnFlush()
	self:XMZBRefreshView()
end

function XianMengZhengBaView:XMZBRefreshView()
	local opengame_cfg = ServerActivityWGData.Instance:GetOpenGameActivityConfig()
	self.data_list = opengame_cfg.guild_battle
	if IsEmptyTable(self.data_list) then
		return
	end
	local reward_item_1 = self.data_list[1].reward_item
	for i,cell in ipairs(self.item_list_1) do
		if reward_item_1[i - 1] then
			cell:SetData(reward_item_1[i - 1])
			cell:SetActive(true)
		else
			cell:SetActive(false)
		end
	end

	local reward_item_2 = self.data_list[2].reward_item
	for i,cell in ipairs(self.item_list_2) do
		if reward_item_2[i - 1] then
			cell:SetData(reward_item_2[i - 1])
			cell:SetActive(true)
		else
			cell:SetActive(false)
		end
	end

	local title_str_1 = Language.OpenServer.GuildContendLingquTitle[self.data_list[1].type]
	self.node_list.name_1.text.text = title_str_1

	local title_str_2 = Language.OpenServer.GuildContendLingquTitle[self.data_list[2].type]
	self.node_list.name_2.text.text = title_str_2

	self.node_list.xmzb_lbl_open_desc.text.text = (Language.OpenServer.GuildContendDesc)

	local opengame_info = ServerActivityWGData.Instance:GetOpenServerData()
	local reward_type = opengame_info.oga_guild_battle_reward_type

	local can_get_reward_1 = false
	local is_get_reward_1 = false
	if self.data_list[1].type == reward_type then
		can_get_reward_1 = opengame_info.oga_guild_battle_reward_flag == 0
		is_get_reward_1 = opengame_info.oga_guild_battle_reward_flag == 1
	end
	self.node_list.lingqu_1:SetActive(not is_get_reward_1)
	self.node_list.yiling_1:SetActive(is_get_reward_1)
	XUI.SetButtonEnabled(self.node_list.lingqu_1, can_get_reward_1)
	self.node_list["red_1"]:SetActive(can_get_reward_1)

	local can_get_reward_2 = false
	local is_get_reward_2 = false
	if self.data_list[2].type == reward_type then
		can_get_reward_2 = opengame_info.oga_guild_battle_reward_flag == 0
		is_get_reward_2 = opengame_info.oga_guild_battle_reward_flag == 1
	end
	self.node_list.lingqu_2:SetActive(not is_get_reward_2)
	self.node_list.yiling_2:SetActive(is_get_reward_2)
	XUI.SetButtonEnabled(self.node_list.lingqu_2, can_get_reward_2)
	self.node_list["red_2"]:SetActive(can_get_reward_2)

	local can_get_reward_3 = false
	local is_get_reward_3 = false
	if self.data_list[3].type == reward_type then
		can_get_reward_3 = opengame_info.oga_guild_battle_reward_flag == 0
		is_get_reward_3 = opengame_info.oga_guild_battle_reward_flag == 1
	end
	self.node_list.yiling_3:SetActive(is_get_reward_3)
	XUI.SetButtonEnabled(self.node_list.lingqu_3, can_get_reward_3)
	self.node_list["red_3"]:SetActive(can_get_reward_3)
end

function XianMengZhengBaView:OnClickLingQu(index)
	ServerActivityWGCtrl.Instance:SendOpenGameActivityFetchReward(OPEN_SERVER_REWARD_TYPE.REWARD_TYPE_GUILD_BATTLE_REWARD, self.data_list[index].type)
end
