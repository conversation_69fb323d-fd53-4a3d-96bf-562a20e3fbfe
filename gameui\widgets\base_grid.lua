----------------------------------------------------
-- 基础网格,index从0开始
----------------------------------------------------
GRID_TYPE_BAG = "bag"
GRID_TYPE_STORAGE = "storge"
GRID_TYPE_EQUIP = "equip"
GRID_TYPE_SHENSHOU_BAG = "shenshou_bag"

local develop_mode = require("editor/develop_mode")
local is_develop = develop_mode:IsDeveloper()

BaseGrid = BaseGrid or BaseClass()
function BaseGrid:__init()
	-- self.cells = {}									-- 格子列表
	self.select_callback = nil						-- 选中某个格子回调
	self.item_render = nil							-- 创建的item类型
	self.is_show_tips = nil							-- 是否显示tips

	-- self.is_set_open_count = false					-- 是否设置了开启格子数
	-- self.max_open_index = 0							-- 已开启的最大格子索引（物品格子专用）

	self.grid_name = ""								-- 格子名称
	self.cell_data_list = {}						-- 格子数据列表
	self.create_callback = nil						-- 创建完成回调
	self.max_cell_index = 0							-- 最大索引
	self.is_multi_select = false 					-- 是否多选
	------------
	self.cell_list = {}
	self.first_time_load = true						-- 是否第一次加载
	self.start_zero = true 							-- 是否从0开始
	self.select_tab = {[1] = {}} 					-- 选择项保存表
	self.cur_index = nil 							-- 当前选择的格子
	self.change_cells_num = 0						-- 是否固定格子数
	self.min_row = nil 								-- 最少行数
	self.max_cell = nil 							-- 最多格子
	self.asset_bundle = nil
	self.asset_name = nil
	self.has_data_max_index = 0						--最大有数据的格子索引
	self.is_no_data_hide = false  					--没有数据时是否隐藏空格子
end

function BaseGrid:__delete()
	for i, v in pairs(self.cell_list) do
		v:DeleteMe()
	end
	self.cell_list = {}
end

function BaseGrid:GetView()
	return self.list_view
end

function BaseGrid:GetGrid()
	return self.list_view
end

function BaseGrid:GetGridName()
	return self.grid_name
end

function BaseGrid:SetGridName(grid_name)
	self.grid_name = grid_name
end

--取消当前所有选择的格子
function BaseGrid:CancleAllSelectCell()
	self.select_tab[1] = {}
	self:__DoRefresh(2)
end

-- 当面板显示全部item才用
function BaseGrid:GetAllCell()
	local all_cell = {}
	for k, v in pairs(self.cell_list) do
		local cell_tab = v:GetAllCell()
		for k, v in pairs(cell_tab) do
			all_cell[v:GetIndex()] = v
		end
	end
	return all_cell
end


function BaseGrid:GetCellList()
	return self.cell_list
end

--取消当前选择的格子
function BaseGrid:CancleSelectCurCell()
	if nil ~= self.cur_index then
		local cell = self:GetCell(self.cur_index)
		if cell then
			cell:SetSelect(false)
		end
		if self.is_multi_select then
			self.select_tab[1][self.cur_index] = nil
		else
			self.select_tab[1] = {}
		end
		self.cur_index = nil
	end
end

-- 创建网格 {t.cell_count, t.col, t.row, t.itemRender, t.change_cells_num, t.asset_bundle, t.asset_name, t.list_view}
-- t.itemRender可为nil，默认为ItemCell
-- asset_bundle和asset_name 创建自己的预制物（不传默认创建ItemCell）
-- change_cells_num 可为nil，默认为0   0(固定格子，格子数为cell_count)  1(格子数为data长度，可不传cell_count)  2(有最少格子数，数据超过最少格子数会扩展格子)
function BaseGrid:CreateCells(t)
	if t.change_cells_num then
		self.change_cells_num = t.change_cells_num
	elseif t.cell_count then
		self.change_cells_num = 0
		self.max_cell = t.cell_count
	end

	self.is_no_data_hide = t.is_no_data_hide


	if t.cell_count then
		self.row = math.ceil(t.cell_count / t.col)
	elseif t.row then
		self.row = t.row
	else
		self.row = 1
	end

	if self.change_cells_num == 2 then
		self.min_row = self.row
	end

	if t.assetBundle and t.assetName then
		self.asset_bundle = t.assetBundle
		self.asset_name = t.assetName
	end

	self.columns = t.col

	local function get_length_tab(length)
		local length_tab = {}

		for i = 0, (length - 1) do
			local tab = {}
			table.insert(length_tab, i, tab)
		end
		return length_tab
	end
	self.data_list = get_length_tab(self.row)

	self.item_render = t.itemRender or ItemCell
	self.list_view = t.list_view

	if self.first_time_load then
		local list_delegate = self.list_view.list_simple_delegate
		list_delegate.NumberOfCellsDel = BindTool.Bind(self.GetListViewNumbers, self)
		list_delegate.CellRefreshDel = BindTool.Bind(self.RefreshListViewCells, self)
		self.first_time_load = false
	end

	if nil ~= self.create_callback then
		self.create_callback()
	end
end

--刷新格子
function BaseGrid:RefreshListViewCells(cell, cell_index)
	local item_cell = self.cell_list[cell]
	if not item_cell then
		item_cell = BaseGridItemRender.New(cell.gameObject)
		item_cell:SetSelectTab(self.select_tab)
		item_cell:SetAssetBundle(self.asset_bundle, self.asset_name)
		item_cell:SetItemRender(self.item_render)
		item_cell:SetColumns(self.columns)
		item_cell:SetHideEmptyCell(self.is_no_data_hide)
		item_cell:SetClickCallBack(BindTool.Bind1(self.SelectCellHandler, self))
		if nil ~= self.is_show_tips then
			item_cell:SetIsShowTips(self.is_show_tips)
		end
		self.cell_list[cell] = item_cell
	end

	item_cell:SetRows(cell_index)
	item_cell:SetData(self.data_list[cell_index])
end


--获得格子数
function BaseGrid:GetListViewNumbers()
	return self.row
end

function BaseGrid:SetSelectCallBack(callback)
	self.select_callback = callback
end

function BaseGrid:GetDataList()
	return self.cell_data_list
end

function BaseGrid:GetCellDataList()
	return self.data_list
end


-- 0 刷新跳到顶部  1 刷新调到底部  2 刷新可视格子  3 刷新和重新加载listview
function BaseGrid:SetDataList(data, refresh_type, percent)
	self.cell_data_list = data
	self:ManageDataList(data)
	self:__DoRefresh(refresh_type, percent)
end

function BaseGrid:ReloadData(percent)
	self:__DoRefresh(0, percent)
end

-- 0 刷新跳到顶部  1 刷新调到底部  2 刷新可视格子  3 刷新和重新加载listview
function BaseGrid:__DoRefresh(refresh_type, percent)
	if self.list_view.scroller.isActiveAndEnabled then
		if refresh_type == 0 then
			percent = percent or 0
			self.list_view.scroller:ReloadData(percent)
		elseif refresh_type == 1 then
			percent = percent or 1
			self.list_view.scroller:ReloadData(percent)
		elseif refresh_type == 2 then
			self.list_view.scroller:RefreshActiveCellViews()
		elseif refresh_type == 3 then
			self.list_view.scroller:RefreshAndReloadActiveCellViews(true)
		end
	end

	-- print_error("[BaseGrid]###############__DoRefresh>>>>>>>>>>>>", refresh_type, percent)
	if is_develop then
		self:__CheckRefreshCall()
	end
end


function BaseGrid:__CheckRefreshCall()
	local now_frame = UnityEngine.Time.frameCount
	self.last_refresh_frame = self.last_refresh_frame or now_frame
	self.refresh_times_in_frame = self.refresh_times_in_frame or 0
	if self.last_refresh_frame ~= UnityEngine.Time.frameCount then
		self.last_refresh_frame = now_frame
		self.refresh_times_in_frame = 1
	else
		self.refresh_times_in_frame = self.refresh_times_in_frame + 1
		if self.refresh_times_in_frame >= 2 then
			print_error(string.format("[BaseGrid] 在1帧里执行了%s次__DoRefresh，请整理代码或改用AsyncBaseGrid来代替", self.refresh_times_in_frame))
		end
	end
end


--解决刷新延迟问题
function BaseGrid:FulshCanSeeitem()
	self:__DoRefresh(2)
end

-- 分配数据
function BaseGrid:ManageDataList(data, refresh)
	self.has_data_max_index = 0
	if self.change_cells_num == 2 then
    	self.data_list = {}
 	else
 		for k, v in pairs(self.data_list) do
			self.data_list[k] = {}
		end
 	end

	for i, v in pairs(data) do
		i = self.start_zero and i or i - 1
		local row = math.floor(i / self.columns)
		local col = i % self.columns
		col = (col == 0) and 1 or (col + 1)
		if not self.data_list[row] then
			self.data_list[row] = {}
		end
		if self.max_cell and self.max_cell < (row * self.columns) + col then
			break
		end
		self.data_list[row][col] = v
		self.has_data_max_index = self.has_data_max_index + 1
	end

	if self.change_cells_num == 1 then
		 self.row = math.ceil((self.start_zero and (#data + 1) or #data) / self.columns)
	elseif self.change_cells_num == 2 or self.change_cells_num == 0 then
		if self.min_row and #self.data_list + 1 > self.min_row then
			self.row =#self.data_list + 1
		else
			self.row = self.min_row and self.min_row or self.row
			for row = 0, (self.row - 1) do
				for col = 1, self.columns do
					if not self.data_list[row] then
						self.data_list[row] = {}
					end
					if self.max_cell and self.max_cell < (row * self.columns) + col then
						break
					else
						if not self.data_list[row][col] then
							self.data_list[row][col] = {}
						end
					end
				end
			end
		end
	end
end

function BaseGrid:SetIsMultiSelect(is_multi_select)
	self.is_multi_select = is_multi_select
end

-- 数据开始下标是否0开始
function BaseGrid:SetStartZeroIndex(start_zero)
	self.start_zero = start_zero
end

function BaseGrid:GetDataByIndex(index)
	-- return self.origin_data[index]
end

-- 默认返回item(可能部分) 传参返回index(全)
function BaseGrid:GetMultiSelectCell(is_get_index)
	if is_get_index then
		return self.select_tab[1]
	else
		local cells = {}
		for k, v in pairs(self.select_tab[1]) do
			if v then
				local cell = self:GetCell(k)
				table.insert(cells, cell)
			end
		end
		return cells
	end
end

-- 设置创建完成回调函数(在执行CreateCells之前设置)
function BaseGrid:SetCreateCallback(create_callback)
	self.create_callback = create_callback
end

function BaseGrid:GetMaxCellCount()
	return self.max_cell_index + 1
end

-- -- 刷新某个格子里的数据
function BaseGrid:UpdateOneCell(index, data)
	local cell = self:GetCell(index)
	if cell then
		cell:SetData(data)
	end

	index = self.start_zero and index or (index - 1)
	local row = math.floor(index / self.columns)    --行
	local col = index % self.columns                -- 列
	col = col + 1
	self.data_list[row][col] = data
end

-- -- 获得指定的格子
function BaseGrid:GetCell(index)
	for k, v in pairs(self.cell_list) do
		local row = math.floor(self.start_zero and index or (index - 1) / self.columns)
		if v:GetActive() and row == v:GetRows() then
			local cell = v:GetCell(index)
			if cell then
				return cell
			end
		end
	end
	return nil
end

-- -- 根据格子索引选择格子
function BaseGrid:SelectCellByIndex(index)
	local select_cell = self:GetCell(index)
	if nil ~= select_cell then
		self:SelectCellHandler(select_cell)
	end
end

-- 设置可选择空格子（选择特效）  没做完
function BaseGrid:CanSelectNilData(value)
	for k, v in pairs(self.cell_list) do
		v:SetIgnoreDataToSelect(value)
	end
end

-- 选择某个格子回调
function BaseGrid:SelectCellHandler(cell)
	if nil ~= self.select_callback then
		self.select_callback(cell)
	end

	self.cur_index = cell:GetIndex()

	local cell_index = self.cur_index
	if self.is_multi_select then
		if not self.select_tab[1][cell_index] then
			self.select_tab[1][cell_index] = true
		elseif self.select_tab[1][cell_index] then
			self.select_tab[1][cell_index] = nil
		else
			self.select_tab[1][cell_index] = true
		end
	else
		for k, v in pairs(self.select_tab[1]) do
			if cell_index == k then return end
		end
		self.select_tab[1] = {}
		self.select_tab[1][cell_index] = true
	end

	self:__DoRefresh(2)
end

--获取所有高亮格子数据
function BaseGrid:GetAllSelectCell()
	local all_cell = {}
	for k,v in pairs(self.select_tab[1]) do
		table.insert(all_cell,self.cell_data_list[k])
	end
	return all_cell
end

function BaseGrid:SetDefultSelectCell(cell_index)
	self.select_tab[1] = {}
	self.select_tab[1][cell_index] = true
end

--跳转到某个cell 新增
function BaseGrid:JumpToIndex(index , min_show_len)
	local percent = 0
	local min = min_show_len or 4

	if index > min then
		local len = self:GetCellLength()
		len = len == 0 and 1 or len
		percent = index / len
		self:__DoRefresh(0, percent)
	else
		self:__DoRefresh(0)
	end
end

function BaseGrid:GetCellLength()
	if not self.cell_data_list then
		return 0
	end
	local length = #self.cell_data_list
	length = self.cell_data_list[0] and (length + 1) or length
	return length
end

----------------------------------------------------
-- 以下为物品格子专用
----------------------------------------------------

--根据物品id获得第一个符合的格子
function BaseGrid:GetFirstCellByItemId(item_id)
	for i = 1, self.row do
		for k, v in pairs(self.cell_list) do
			if v:GetRows() == i then
				local cell = v:GetFirstCellByItemId(item_id)
				if cell then
					return cell
				end
			end
		end
	end
	return nil
end

function BaseGrid:SetIsShowTips(is_show_tips)
	self.is_show_tips = is_show_tips
	for k, v in pairs(self.cell_list) do
		v:SetIsShowTips(is_show_tips)
	end
end

function BaseGrid:SetSelectTab(bo)
	for k, v in pairs(self.cell_list) do
		v:SetIgnoreDataToSelect(bo)
	end
end
