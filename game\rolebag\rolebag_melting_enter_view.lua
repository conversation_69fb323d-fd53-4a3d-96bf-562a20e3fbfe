RoleBagMeltingEnterView = RoleBagMeltingEnterView or BaseClass(SafeBaseView)
function RoleBagMeltingEnterView:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true

    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
    self:AddViewResource(0, "uis/view/rolebag_ui_prefab", "layout_bag_melting_enter_view")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
    self.role_data_change_callback = BindTool.Bind(self.OnRoleDataChange, self)
end

function RoleBagMeltingEnterView:ReleaseCallBack()
    RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)

    if self.attr_list then
        for k, v in pairs(self.attr_list) do
            v:DeleteMe()
        end
        self.attr_list = nil
    end

    if self.show_model then
        self.show_model:DeleteMe()
        self.show_model = nil
    end
end

function RoleBagMeltingEnterView:CloseCallBack()
    self.is_do_ani = nil
    self.old_model_is_do_ani = nil
    self.success_melting_num = nil
end

function RoleBagMeltingEnterView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.Bag.EquipName
    if nil == self.attr_list then
        self.attr_list = {}
        local attr_num = self.node_list["attr_list"].transform.childCount
        for i = 1, attr_num do
            local cell = RoleBagMeltingAttrCellRender.New(self.node_list["attr_list"]:FindObj("attr" .. i))
            cell:SetIndex(i)
            cell:SetAttrNameNeedSpace(true)
            self.attr_list[i] = cell
        end
    end

    if nil == self.show_model then
        self.show_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["ph_display"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.L,
            can_drag = false,
        }
        
        self.show_model:SetRenderTexUI3DModel(display_data)
        -- self.show_model:SetUI3DModel(self.node_list["ph_display"].transform,
        --     nil, 1, true, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.show_model)
    end

    local bundle, asset = ResPath.GetOtherUIModel(4)
    self.show_model:SetMainAsset(bundle, asset)
    -- self.show_model:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("UIother", 4))

    local bundle, asset = ResPath.GetRawImagesPNG("a3_ty_bg1")
    self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
        self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
    end)
    XUI.AddClickEventListener(self.node_list["btn_active_vip"], BindTool.Bind(self.OnClickOpenVip, self))
    XUI.AddClickEventListener(self.node_list["btn_open_melting"], BindTool.Bind(self.OnClickOpenMelting, self))
    RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback, { "vip_level" })
end

function RoleBagMeltingEnterView:OpenCallBack()
    self:OnClickOpenMelting()
end

function RoleBagMeltingEnterView:OnRoleDataChange(attr_name, value, old_value)
    if attr_name == "vip_level" then
        self:FlushVipPart()
    end
end

function RoleBagMeltingEnterView:OnFlush(param_t)
    for k, v in pairs(param_t) do
        if k == "all" then
            if not self.is_do_ani then
                self:FlushView()
                self:FlushVipPart()
            end
        elseif k == "start_do_ani" then
            self.is_do_ani = true
            self:DoModelAni()
        elseif k == "preview" then
            self:FlushView(v.info)
        end
    end
end

function RoleBagMeltingEnterView:DoModelAni()
    if self.old_model_is_do_ani == self.is_do_ani then
        return
    end

    self.old_model_is_do_ani = self.is_do_ani
    self.show_model:PlayMountAction()
    ReDelayCall(self, function()
        self.is_do_ani = nil
        self.old_model_is_do_ani = nil
        self:FlushView()
        self:ShowEffect()
    end, 3.1, "Melting_ani_time")
end

function RoleBagMeltingEnterView:FlushVipPart()
    local vo = GameVoManager.Instance:GetMainRoleVo()
    local role_vip = vo.vip_level or 0
    local other_cfg = RoleBagWGData.Instance:EquipMeltCfg().other[1]
    local need_vip_level = other_cfg and other_cfg.vip_level or 0
    self.node_list["btn_active_vip"]:SetActive(role_vip < need_vip_level)
    self.node_list["rich_ronglian_tip"].text.text = role_vip >= need_vip_level and Language.Bag.VipMeltingTips or
    Language.Bag.MeltingTips
end

function RoleBagMeltingEnterView:FlushView(info)
    local old_info = RoleBagWGData.Instance:GetEquipMeltInfo()
    info = info or old_info
    local cur_level = info.melt_level
    local max_level = RoleBagWGData.Instance:GetEquipMeltMaxLevel()
    self.node_list["cur_melt_level"].text.text = "Lv." .. cur_level


    local attr_list = RoleBagWGData.Instance:GetEquipMelAttrListByLevel(cur_level)
    for k, v in ipairs(self.attr_list) do
        v:SetData(attr_list[k])
    end

    local is_max_level = cur_level >= max_level
    local cur_level_cfg = RoleBagWGData.Instance:GetEquipMeltCfgByLevel(cur_level)

    local cur_pro_value, need_pro_value, pro_percent = 0, 0, 1
    if not is_max_level and cur_level_cfg then
        cur_pro_value = info.melt_exp
        need_pro_value = cur_level_cfg.need_exp
        pro_percent = info.melt_exp / cur_level_cfg.need_exp
    end

    self.node_list["level_pro_text"].text.text = string.format("%s/%s", cur_pro_value, need_pro_value)
    self.node_list["level_progress"].slider.value = pro_percent
    self.node_list["up_level_arrow"]:SetActive(cur_level > old_info.melt_level)
end

function RoleBagMeltingEnterView:OnClickOpenVip()
    ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_vip, "card_seq", { card_seq = 3 })
end

function RoleBagMeltingEnterView:OnClickOpenMelting()
    ViewManager.Instance:Open(GuideModuleName.RoleBagViewMeltingView)
end

function RoleBagMeltingEnterView:CacheSuccessMeltingNum(num)
    self.success_melting_num = num
end

function RoleBagMeltingEnterView:ShowEffect()
    if self.success_melting_num then
        TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.Bag.MeltingError1, self.success_melting_num))
        -- TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_ronglian, is_success = true,
        -- pos = Vector2(0, 0), parent_node = self:GetRootNode()})
        self.success_melting_num = nil
    end
end

RoleBagMeltingAttrCellRender = RoleBagMeltingAttrCellRender or BaseClass(CommonAttrRender)

function RoleBagMeltingAttrCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end

    if self.data.is_special_attr then
        self.node_list.attr_name.text.text = self.data.attr_str
        self.node_list.attr_value.text.text = self.data.attr_value
    else
        local is_per = EquipmentWGData.Instance:GetAttrIsPer(self.data.attr_str)
        local per_desc = is_per and "%" or ""
        local value_str = is_per and self.data.attr_value / 100 or self.data.attr_value
        self.node_list.attr_name.text.text = EquipmentWGData.Instance:GetAttrName(self.data.attr_str, self.name_need_space, self.need_mao_hao)
        self.node_list.attr_value.text.text = string.format("%s%s%s", self.value_prefix, value_str, per_desc)
    end
    
	local special_attr_color = SPECIAL_ATTR_COLOR[self.data.attr_str]
	
	if nil ~= special_attr_color then
		self.node_list.attr_name.text.supportRichText = true
		self.node_list.attr_value.text.supportRichText = true
		self.node_list.attr_name.text.text = ToColorStr(self.node_list.attr_name.text.text, special_attr_color)
		self.node_list.attr_value.text.text = ToColorStr(self.node_list.attr_value.text.text, special_attr_color)
	end

    self.view:SetActive(true)
end