require("config/config_location")

Location = Location or BaseClass(BaseWGCtrl)

function Location:__init()
	if Location.Instance then
		ErrorLog("[Location] Attemp to create a singleton twice !")
	end

	Location.Instance = self

	self.callback_list = {}
	self:RegisterProtocol(CSCityName)

	self.is_start = false
	self.next_check_cityname_invalid_time = 0
	self.check_citynmae_invalid_times = 0
	Runner.Instance:AddRunObj(self, 8)
	self.role_province_name = ""
end

function Location:__delete()
	Runner.Instance:RemoveRunObj(self)
	Location.Instance = nil
	self.callback_list = {}
end

function Location:NotifyCityNameChange(callback)
	for _, v in ipairs(self.callback_list) do
		if v == callback then
			return
		end
	end

	table.insert(self.callback_list, callback)
end

function Location:UnNotifyCityNameChange(callback)
	for k, v in ipairs(self.callback_list) do
		if v == callback then
			table.remove(self.callback_list, k)
			break
		end 
	end
end

function Location:NoticeCityChange()
	for _, v in ipairs(self.callback_list) do
		v()
	end
end

function Location:Update(now_time, elapse_time)
	if self.is_start and "" == GameVoManager.Instance:GetMainRoleVo().city_name
		and self.check_citynmae_invalid_times <= 30 then

		if 0 == self.next_check_cityname_invalid_time then
			self.next_check_cityname_invalid_time = now_time + 15
		end

		if now_time >= self.next_check_cityname_invalid_time then
			self.next_check_cityname_invalid_time = now_time + 15
			self.check_citynmae_invalid_times = self.check_citynmae_invalid_times + 1
			self:UseRealLoaction()
		end
	end
end

function Location:OnStart()
	self.is_start = true
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	if 1 == main_role_vo.is_real_location then
		if "" == main_role_vo.city_name or TimeWGCtrl.Instance:GetServerTime() - main_role_vo.last_get_city_name_time > 3 * 60 * 60  then
			self:UseRealLoaction()
		end
	end
end

-- 使用真实地址
function Location:UseRealLoaction()
	-- 屏蔽，后续再开启
	-- GameVoManager.Instance:GetMainRoleVo().is_real_location = 1

	-- local ip = self:GetIpAddress()
	-- if "0.0.0.0" == ip then
	-- 	local get_ip_url = "http://www.3322.org/dyndns/getip"
	-- 	Log("[Location] StartReqIp", get_ip_url)
	-- 	HttpClient:Request(get_ip_url, BindTool.Bind1(self.ReqIpCallback, self))
	-- else
	-- 	self:ReqLocation(ip)
	-- end
end

function Location:GetIpAddress()
    if nil ~= GLOBAL_CONFIG.param_list and nil ~= GLOBAL_CONFIG.param_list.client_ip then
        return GLOBAL_CONFIG.param_list.client_ip
    end

    return "0.0.0.0"
end

-- 使用玩家自己选择的地址
function Location:UseSelectLocation(city_name, role_province_name)
	if "" == city_name then
		return
	end

	Log("[Location] UseSelectLocation", city_name)

	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	role_vo.is_real_location = 0
	self.role_province_name = role_province_name or ""
	role_vo.city_name = city_name

	self:SyncToServer()

	self:NoticeCityChange()
end

function Location:ReqIpCallback(url, arg, data, size)
	if nil == data then
		return
	end

	Log("[Location] StartReqLocation", data or "")
	self:ReqLocation(data or "")
end

function Location:ReqLocation(ip)
	if IS_AUDIT_VERSION then
		return
	end
	
	local ip_check_url = "http://cls.*************/api/ip_check.php"
	if GLOBAL_CONFIG.param_list.ip_check_url and GLOBAL_CONFIG.param_list.ip_check_url ~= "" then
		ip_check_url = GLOBAL_CONFIG.param_list.ip_check_url
	end
	local get_location_url = ip_check_url.."?ip=" .. ip
	HttpClient:Request(get_location_url, BindTool.Bind1(self.ReqLocationCallback, self))
end

function Location:ReqLocationCallback(url, arg, data, size)
	if nil == data then
		return
	end

	local ret_t = cjson.decode(data)
	if nil == ret_t or type(ret_t) == "userdata" or nil == ret_t.data or nil == ret_t.data.city or
		"" == ret_t.data.city then
		return
	end

	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	if 1 == role_vo.is_real_location then
		self.role_province_name = ChatWGCtrl.Instance and ChatWGCtrl.Instance:GetProvinceNameByCityName(ret_t.data.city) or ""
		role_vo.city_name = ret_t.data.city
		role_vo.last_get_city_name_time = TimeWGCtrl.Instance:GetServerTime()
		self:SyncToServer()

		self:NoticeCityChange()
	end
end

-- 追踪令
function Location:SyncToServer()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local protocol = ProtocolPool.Instance:GetProtocol(CSCityName)
	protocol.province_name = self.role_province_name or ""
	protocol.city_name = role_vo.city_name or ""
	protocol.is_real_location = role_vo.is_real_location
	protocol:EncodeAndSend()

	Log("[Location] StartSendToServer", protocol.is_real_location, protocol.city_name, self.role_province_name)
end
