--------------------------------------------------------
--技能数据管理
--------------------------------------------------------
SkillWGData = SkillWGData or BaseClass()
SkillWGData.XingMo_Skill_Id = 5 -- 心魔技能id
SkillWGData.TianFu_Skill_Id_202 = 202 -- 天赋技能 不屈意志
SkillWGData.BianShen_Common_Skill_Id = 281 -- 变身普攻
SkillWGData.Guild_BianShen_Common_Skill_Id = 922 -- 仙盟变身普攻
SkillWGData.Skill_Id_285 = 285 -- 跃击+恐惧
SkillWGData.Skill_Id_291 = 291 -- 变身特殊主动技能
SkillWGData.Skill_Id_11303 = 11303 -- 闪电链技能
SkillWGData.Skill_Id_11304 = 11304 -- 扔雪球
SkillWGData.Skill_Id_270 = 270     --攻城战战车技能
SkillWGData.Skill_Id_2152 = 2152

-- 【职业改动修改】
use_prof_normal_skill_list =
{
    [0] = { [1] = { 11, 12, 13, 14 },
            [2] = { 81, 82, 83, 84 },
            [3] = { 21, 22, 23, 24 },
            [4] = { 71, 72, 73, 74 },},
    [1] = { [1] = { 31, 32, 33, 34 },
            [2] = { 51, 52, 53, 54 },
            [3] = { 41, 42, 43, 44 },
            [4] = { 61, 62, 63, 64 }},
}

-- 【职业改动修改】
local use_prof_skill_list =
{
    [0] = { [1] = { 1001, 1002, 1003, 1004 },
            [2] = { 1701, 1702, 1703, 1704 },
            [3] = { 1101, 1102, 1103, 1104 },
            [4] = { 1601, 1602, 1603, 1604 },},
    [1] = { [1] = { 1201, 1202, 1203, 1204 },
            [2] = { 1401, 1402, 1403, 1404 },
            [3] = { 1301, 1302, 1303, 1304 },
            [4] = { 1501, 1502, 1503, 1504 },},
}

-- 【职业改动修改】
local zhudong_skill_list =
{
    [0] = { [1] = { [1001] = true, [1002] = true, [1003] = true, [1004] = true },
            [2] = { [1701] = true, [1702] = true, [1703] = true, [1704] = true },
            [3] = { [1101] = true, [1102] = true, [1103] = true, [1104] = true },
            [4] = { [1601] = true, [1602] = true, [1603] = true, [1604] = true },},
    [1] = { [1] = { [1201] = true, [1202] = true, [1203] = true, [1204] = true },
            [2] = { [1401] = true, [1402] = true, [1403] = true, [1404] = true },
            [3] = { [1301] = true, [1302] = true, [1303] = true, [1304] = true },
            [4] = { [1501] = true, [1502] = true, [1503] = true, [1504] = true },},
}

---净化技能列表
purify_skill_list = 
{
    7002,
}

function SkillWGData:__init()
    if SkillWGData.Instance then
        ErrorLog("[SkillWGData] Attemp to create a singleton twice !")
    end
    SkillWGData.Instance = self

    self.notify_data_change_callback_list = {}-- 技能有更新变化时进行回调

    self.default_skill_index = -1-- 默认技能
    self.normal_skill_index = 0

    self.skill_index_1 = -1 -- 技能1
    self.skill_index_2 = -1 -- 技能2
    self.skill_index_3 = -1 -- 技能3
    self.skill_index_4 = -1 -- 技能4
    self.skill_index_5 = -1 -- 技能5
    self.skill_index_6 = -1 -- 技能6

    self.jingling_skill_index_list = {}

    -- 是否自动释放标记
    self.skill_auto_flag_1 = false
    self.skill_auto_flag_2 = false
    self.skill_auto_flag_3 = false
    self.skill_auto_flag_4 = false
    self.skill_auto_flag_5 = false
    self.skill_auto_flag_6 = false
    self.skill_auto_flag_7 = false

    self.skill_list = {}-- 里面存储的是通过CreateSkillInfo创建的table
    self.curr_skill_list = {}-- 当前状态下的技能列表（变身/普通）
    self.cur_pet_skill_list = {}--出战宠物技能列表
    self.UP_SKILL_ITEM_ID = 26500
    self.other_skill_info = {}
    self.global_cd_end = 0-- 全局CD结束时间

    self.next_duan_atk = 0 -- 第几段攻击
    self.next_duan_reset_time = nil
    self.next_normal_skill_index_reset_time = nil

    self.can_next_skill_time = 0

    self.cur_combo_index = 1
    self.default_beidong_index = -1

    local career_cfg = ConfigManager.Instance:GetAutoConfig("career_config_auto")
    self.skill_upgrade_change_cfg = ListToMap(career_cfg.skill_change, "sex", "prof", "serious_type", "grade")
    self.skill_up_level_cfg = ListToMap(career_cfg.skill_uplevel, "skill_id", "skill_level")
    self:InitNormalSkillToAwakeSkillMap()

    local roleskill_auto = ConfigManager.Instance:GetAutoConfig("roleskill_auto")
    self.passive_skill_open = roleskill_auto.passive_skill_open
    self.pet_skill_cfg = ListToMap(roleskill_auto.pet_skill, "skill_id", "skill_level")
    self.tianshen_skill_cfg = ListToMap(roleskill_auto.tianshen_skill, "skill_id", "skill_level")
    self.xmz_car_skill_cfg = ListToMap(roleskill_auto.xmz_car_skill, "skill_id")
    self.skill_ext_cfg = ConfigManager.Instance:GetAutoConfig("skill_ext_cfg_auto")
    self.skill_ext_base_cfg = self.skill_ext_cfg.other[1]
    self.skill_shout_move_cfg = self.skill_ext_cfg.skill_shout_move
    local skill_fenduan = ListToMap(self.skill_ext_cfg.fenduan, "skill_id")

    self.buff_layer_cfg = ListToMap(roleskill_auto.bufflayer_show, "skill_id")
    self.buff_layer_type_cfg = ListToMap(roleskill_auto.bufflayer_show, "buff_type")
    self.before_skill_cfg = ListToMap(roleskill_auto.before_skill, "skill_id")
    self.before_skill_type_cfg = ListToMap(roleskill_auto.before_skill, "skill_type")

    local buffconfig = ConfigManager.Instance:GetAutoConfig("buffconfig_auto")
    self.buff_cfg = buffconfig.buff
    self.scene_effect_cfg = buffconfig.scene_effect
    self.scene_buff_effect_cfg = buffconfig.scene_buff_effect

    self.after_skill_type_cfg = roleskill_auto.after_skill
    self.sixiang_skill_cfg = ListToMap(roleskill_auto.sixiang_skill, "skill_id", "skill_level")
    self.wuxing_skill_cfg = ListToMap(roleskill_auto.wuxing_skill, "skill_id", "skill_level")
    self.halo_skill_cfg = ListToMap(roleskill_auto.waist_skill, "skill_id", "skill_level")
    self.wuhun_skill_cfg = ListToMap(roleskill_auto.wuhun_skill, "skill_id", "skill_level")
    self.other_skill_cfg = ListToMap(roleskill_auto.other_skill, "skill_id", "skill_level")
    self.roleskill_client_cfg = ListToMapList(roleskill_auto.client, "skill_id")
    self.skill_use_limit_cfg = ListToMap(roleskill_auto.skill_limit, "skill_id")
    self.skill_special_show_cfg = ListToMap(roleskill_auto.skill_special_show, "skill_id")
    self.fight_mount_skill_cfg = ListToMap(roleskill_auto.fightmount2_skill, "skill_id", "skill_level")
    self.skill_screen_effect_cfg = ConfigManager.Instance:GetAutoConfig("skill_screen_effect_cfg_auto").effect
    self.customized_skill_cfg = ListToMap(roleskill_auto.wardrobe_skill, "skill_id", "skill_level")
    self.tianshen_heji_skill_cfg = ListToMap(roleskill_auto.tianshenheji_skill, "skill_id", "skill_level")
    self.esoterica_skill_cfg = ListToMap(roleskill_auto.esoterica_skill, "skill_id", "skill_level")
    self.non_resident_skill_cfg = ListToMap(roleskill_auto.non_resident_skill, "skill_id", "skill_level")
    self.beasts_skill_cfg = ListToMap(roleskill_auto.beast_skill, "skill_id")
    self.jijia_skill_cfg = ListToMap(roleskill_auto.mechan_skill, "skill_id", "skill_level")
    self.xiuxian_skill_cfg = ListToMap(roleskill_auto.xiuwei_skill, "skill_id", "skill_level")

    self.cache_skill_attach_info = {}
    self.skill_attach_info_cfg = roleskill_auto.attach_info

    self:ExplainFenDuan(skill_fenduan)
    self.back_skill_list = Split(roleskill_auto.other[1].back_skill_list, "|")
    for i=1,#self.back_skill_list do
        self.back_skill_list[i] = tonumber(self.back_skill_list[i])
    end
    Runner.Instance:AddRunObj(self, 8)

    -- GlobalEventSystem:Bind(SettingEventType.GUAJI_SETTING_CHANGE, BindTool.Bind1(self.OnGuaJiSettingChange, self))

    self.role_min_atk_range = 3
end

function SkillWGData:__delete()
    SkillWGData.Instance = nil
    self.notify_data_change_callback_list = nil
    self.skill_list = nil
    self.break_skill_cfg = nil
    Runner.Instance:RemoveRunObj(self)
end

function SkillWGData:Update(now_time, elapse_time)
    if nil ~= self.next_duan_reset_time and Status.NowTime * 1000 > self.next_duan_reset_time then
        self:ResetNextDuanAtk()
    end

    if nil ~= self.next_normal_skill_index_reset_time and Status.NowTime * 1000 > self.next_normal_skill_index_reset_time then
        self:ResetNormalSkillIndex()
    end
end

----[[ 初始化普通技能 to 觉醒技能映射表
function SkillWGData:InitNormalSkillToAwakeSkillMap()
    self.normal_skill_to_awake_skill_map = {}
    self.awake_skill_to_normal_skill_map = {}
    local skill_cfg = ConfigManager.Instance:GetAutoConfig("career_config_auto").awaken_skill_replace or {}
    for k,v in pairs(skill_cfg) do
        if type(v.replace_skill_id) == "number" then
            self.normal_skill_to_awake_skill_map[v.skill_id] = v.replace_skill_id
            self.awake_skill_to_normal_skill_map[v.replace_skill_id] = v.skill_id
        else
            self.normal_skill_to_awake_skill_map[v.skill_id] = v.skill_id
            self.awake_skill_to_normal_skill_map[v.skill_id] = v.skill_id
        end
    end
end

-- 当玩家觉醒普通技能时，需要转换成另一特效播放。
-- 后端则将玩家的普通技能转换成觉醒技能id做广播，前端则需要将觉醒技能id转换成普通技能id，设置觉醒标记后做相关逻辑处理。
-- 获取普通技能对应的觉醒技能
function SkillWGData:GetNormalSkillToAwakeSkill(skill_id)
    return self.normal_skill_to_awake_skill_map[skill_id]
end

-- 获取觉醒技能对应的普通技能
function SkillWGData:GetAwakeSkillToNormalSkill(skill_id)
    return self.awake_skill_to_normal_skill_map[skill_id]
end
--]]

function SkillWGData:IsBackSkill(skill_id)
    for k,v in pairs(self.back_skill_list) do
        if v == skill_id then
            return true
        end
    end

    return false
end

function SkillWGData:ExplainFenDuan(skill_fenduan_cfg)
    if IsEmptyTable(self.skill_fenduan) then
        self.skill_fenduan = {}
        for k,v in pairs(skill_fenduan_cfg) do
            local fenduan = {}

            local duan_xs_s = Split(v.duan_xs, "|")
            local time_s = Split(v.time, "|")

            local duan_xs_number = {}
            for i = 1, #duan_xs_s do
                duan_xs_number[i] = tonumber(duan_xs_s[i])
            end
            fenduan.duan_xs = duan_xs_number

            local time_number = {}
            for i = 1, #time_s do
                time_number[i] = tonumber(time_s[i])
            end
            fenduan.time = time_number
            fenduan.total_time = tonumber(time_s[#time_s])

            self.skill_fenduan[k] = fenduan
        end
    end

    if self.skill_shout_move_cfg then
        self.skill_shout_move_list = {}

        for k, v in pairs(self.skill_shout_move_cfg) do
            if v.skill_id and v.sex then
                if not self.skill_shout_move_list[v.sex] then
                    self.skill_shout_move_list[v.sex] = {}
                end

                local data = {}
                local asset_list = Split(v.shout_move_asset, "|")
                data.bundle_folder = v.shout_move_bundle_folder
                data.asset_list = asset_list
                self.skill_shout_move_list[v.sex][v.skill_id] = data
            end
        end
    end
end

function SkillWGData:GetFenDuanSkill(skill_id)
    return self.skill_fenduan[skill_id]
end

function SkillWGData:SeSkillResetTime(time)
    self.next_normal_skill_index_reset_time = Status.NowTime * 1000 + time * 1000
end

function SkillWGData:ResetNormalSkillIndex()
    self.normal_skill_index = 0
    self.next_normal_skill_index_reset_time = nil
    self.cur_combo_index = 1
end

function SkillWGData:GetNormalSkillIndex(skill_id)
    self.normal_skill_index = self.normal_skill_index + 1
    local sex, prof = RoleWGData.Instance:GetRoleSexProf()
    local max_count = 0

    local special_appearance = GameVoManager.Instance:GetMainRoleVo().special_appearance
    if special_appearance == SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM then
        max_count = 1
    elseif special_appearance == SPECIAL_APPEARANCE_TYPE.GUNDAM then
        max_count = 3
    elseif self:IsFightMountSkill(skill_id) then
        max_count = 1
    else
        max_count = #use_prof_normal_skill_list[sex][prof]
    end

    if self.normal_skill_index > max_count then
        self.normal_skill_index = 1
    end

    self.next_normal_skill_index_reset_time = Status.NowTime * 1000 + COMMON_CONSTS.DUAN_SKILL_RESET_TIME
    return self.normal_skill_index, self.cur_combo_index
end

function SkillWGData:GetNormalSkillIdList(sex, prof)
    prof = prof % 10
    return use_prof_normal_skill_list[sex][prof]
end

function SkillWGData:GetSkillIdList(sex, prof)
    prof = prof % 10
    return use_prof_skill_list[sex][prof]
end

function SkillWGData:GetAttackIndex(skill_id)
    if SkillWGData.GetSkillBigType(skill_id) == SKILL_BIG_TYPE.NORMAL then
        return self:GetNormalSkillIndex(skill_id)
    end

    self:ResetNormalSkillIndex()
    return 1
end

function SkillWGData:GetSkillAwakeLevel(skill_id)
    for k, v in pairs(self.skill_list) do
        if v.skill_id == skill_id then
            return v.awake_level
        end
    end
    return 0
end

function SkillWGData:GetTianShenSkillCfg(skill_id, level)
    skill_id = tonumber(skill_id)
    level = level or 1
    return self.tianshen_skill_cfg[skill_id] and self.tianshen_skill_cfg[skill_id][level]
end

function SkillWGData:GetTianShenSkillMaxLevel(skill_id)
    if IsEmptyTable(self.tianshen_max_lev_skill_cfg) then
        self.tianshen_max_lev_skill_cfg = {}
        local tianshen_skill_cfg
        if self.tianshen_skill_cfg == nil or IsEmptyTable(self.tianshen_skill_cfg) then
            tianshen_skill_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("roleskill_auto").tianshen_skill, "skill_id", "skill_level")
        else
            tianshen_skill_cfg = self.tianshen_skill_cfg
        end

        for k1, v1 in pairs(tianshen_skill_cfg) do
            local skill_id = k1
            local max_skill_level = 1
            for k2, v2 in pairs(v1) do
                if v2.skill_level > max_skill_level then
                    max_skill_level = v2.skill_level
                end
            end
            self.tianshen_max_lev_skill_cfg[skill_id] = max_skill_level
        end
    end

    return self.tianshen_max_lev_skill_cfg[skill_id]
end

function SkillWGData:OnGuaJiSettingChange(setting_type, flag)
    if setting_type == SETTING_TYPE.GUAJI_SKILL_1 then
        self.skill_auto_flag_1 = flag -- 技能1
    elseif setting_type == SETTING_TYPE.GUAJI_SKILL_2 then
        self.skill_auto_flag_2 = flag -- 技能2
    elseif setting_type == SETTING_TYPE.GUAJI_SKILL_3 then
        self.skill_auto_flag_3 = flag -- 技能3
    elseif setting_type == SETTING_TYPE.GUAJI_SKILL_4 then
        self.skill_auto_flag_4 = flag -- 技能4
    elseif setting_type == SETTING_TYPE.GUAJI_SKILL_5 then
        self.skill_auto_flag_5 = flag -- 技能5
    elseif setting_type == SETTING_TYPE.GUAJI_SKILL_6 then
        self.skill_auto_flag_6 = flag -- 技能6
    elseif setting_type == SETTING_TYPE.GUAJI_SKILL_7 then
        self.skill_auto_flag_7 = flag -- 技能7
    end
end

--根据技能index获得info
function SkillWGData:GetSkillInfoByIndex(skill_index)
    return self.skill_list[skill_index]
end

--根据技能id获得info
function SkillWGData:GetSkillInfoById(skill_id)
    skill_id = tonumber(skill_id)
    for k, v in pairs(self.skill_list) do
        if v.skill_id == skill_id then
            return v
        end
    end


    return nil
end

--获取第一个普通技能
function SkillWGData:GetFirstCommonSkillInfo()
    if 0 == #self.skill_list then return nil end
    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
    local skill_info = nil

    local is_bianshen_system = SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM == main_role_vo.special_appearance
    local ts_first_skill_id = TianShenWGData.Instance:GetSkillByAttackSeq(1, true)

    for k,v in pairs(self.skill_list) do
        -- 变身
        if is_bianshen_system then
            if ts_first_skill_id == v.skill_id then
                skill_info = v
                break
            end
        else
            if not skill_info or skill_info.skill_id > v.skill_id then
                skill_info = v
            end
        end
    end

    return skill_info
end

function SkillWGData:CaclAndCacheMinAtkRange(skill_list)
    if not skill_list then
        return
    end

    local distance
    for k,v in pairs(skill_list) do
        local skill_cfg = self:GetSkillConfigByIdLevel(v.skill_id, v.skill_level)
        if skill_cfg then
            if not distance or skill_cfg.distance < distance then
                distance = skill_cfg.distance
            end
        end
    end
    
    if distance then
        self.role_min_atk_range = distance
    end
end

function SkillWGData:GetRoleMinAtkRange()
    return self.role_min_atk_range or COMMON_CONSTS.MIN_SKILL_DIS
end

--根据index获得技能Config
function SkillWGData:GetSkillConfigByIndex(skill_index)
    local skill_info = self:GetSkillInfoByIndex(skill_index)
    if skill_info ~= nil then
        return self:GetSkillConfigByIdLevel(skill_info.skill_id, skill_info.level)
    end
end

--根据id和等级获得技能Config
function SkillWGData:GetSkillConfigByIdLevel(skill_id, level)
    if skill_id == nil then
        return nil
    end

    local roleskill_auto = ConfigManager.Instance:GetAutoConfig("roleskill_auto")
    local vo = roleskill_auto.normal_skill[skill_id]
    if vo ~= nil then
        return vo
    end

    -- 天神技能
    local skill_cfg = self:GetTianShenSkillCfg(skill_id, level)
    if skill_cfg then
        return skill_cfg
    end

    -- 仙盟战战车技能
    skill_cfg = self:GetXMZCarSkill(skill_id)
    if skill_cfg then
        return skill_cfg
    end

    -- 四象技能
    skill_cfg = self:GetSiXiangSkillById(skill_id, level)
    if skill_cfg then
        return skill_cfg
    end

    -- 五行技能
    skill_cfg = self:GetWuXingSkillById(skill_id, level)
    if skill_cfg then
        return skill_cfg
    end

    -- 光环技能
    skill_cfg = self:GetHaloSkillById(skill_id, level)
    if skill_cfg then
        return skill_cfg
    end

    -- 战斗坐骑技能
    skill_cfg = self:GetFightMountSkillCfg(skill_id, level)
    if skill_cfg then
        return skill_cfg
    end

    -- 定制技能
    skill_cfg = self:GetCustomizedById(skill_id, level)
    if skill_cfg then
        return skill_cfg
    end

    --天神合击技能
    skill_cfg = self:GetTianShenHeJiSkillById(skill_id, level)
    if skill_cfg then
        return skill_cfg
    end

    -- 秘笈技能
    skill_cfg = self:GetEsotericaSkillById(skill_id, level)
    if skill_cfg then
        return skill_cfg
    end

    -- 驭兽技能
    skill_cfg = self:GetBeastsSkillById(skill_id, level)
    if skill_cfg then
        return skill_cfg
    end

    -- 机甲技能
    skill_cfg = self:GetJiJiaSkillConfig(skill_id, level)
    if skill_cfg then
        return skill_cfg
    end

    -- 怒气变身技能
    skill_cfg = self:GetXiuXianSkillConfig(skill_id, level)
    if skill_cfg then
        return skill_cfg
    end

    -- 其它技能
    skill_cfg = self:GetOtherSkillCfg(skill_id, level)
    if skill_cfg then
    	return skill_cfg
    end

    skill_cfg = self:GetUpgradeSkillCfg(skill_id)
    if skill_cfg then
        return skill_cfg
    end

    if SkillWGData.IsPetSkill(skill_id) then
        return self.pet_skill_cfg[skill_id] and self.pet_skill_cfg[skill_id][level]
    end

    local vo_list = roleskill_auto["s" .. skill_id]
    if vo_list ~= nil then
        return vo_list[1]
    end

    return nil
end

function SkillWGData:GetRealSkillIndex(skill_id, is_send)
    for k, v in pairs(self.skill_list) do
        if v.skill_id == skill_id then
            return v.index
        end
    end
    return nil
end

function SkillWGData:GetSkillIconId(skill_id, duan_atk)
    local skill_info = self:GetSkillInfoById(skill_id)
    if skill_info then
        local skill_break_cfg = self:GetSkillBreakCfgByLevel(skill_id, skill_info.awake_level)
        if skill_break_cfg then
            return skill_break_cfg.skill_icon_id
        end
    end
    local normal_skill_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id)
    if normal_skill_cfg then
        return normal_skill_cfg.icon_resource
    end
    return 1
end

--当前技能是否觉醒了
function SkillWGData:GetCurSkillIsAwake(skill_id)
    local skill_info = self:GetSkillInfoById(skill_id)
    if skill_info and skill_info.awake_level and skill_info.awake_level >= 1 then
        return true
    end

    return false
end

-- 普通技能都觉醒
function SkillWGData:GetAllNormalSkillIsAwake()
    local sex, prof = RoleWGData.Instance:GetRoleSexProf()
    local skill_list = self:GetSkillIdList(sex, prof)
    for k, v in pairs(skill_list) do
        if not self:GetCurSkillIsAwake(v) then
            return false
        end
    end

    return true
end

-- 普通技能转换成觉醒技能
function SkillWGData:GetNormalSkillTransformAwakeSkill(skill_id)
    -- 角色觉醒技能id只做特效变化逻辑处理，实际逻辑要转换成正常角色技能处理
    local skill_cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_id, 1)
    if skill_cfg then
        local skill_type = skill_cfg.skill_type
        -- 角色普通技能都觉醒，普攻也要觉醒
        if skill_type == SKILL_TYPE.SKILL_1 then
            local is_all_normal_skill_awake = SkillWGData.Instance:GetAllNormalSkillIsAwake()
            if is_all_normal_skill_awake then
                local awake_skill_id = SkillWGData.Instance:GetNormalSkillToAwakeSkill(skill_id)
                skill_id = awake_skill_id or skill_id
            end
        -- 角色普通技能觉醒替换id
        elseif skill_type == SKILL_TYPE.SKILL_2 then
            local is_cur_skill_awake = SkillWGData.Instance:GetCurSkillIsAwake(skill_id)
            if is_cur_skill_awake then
                local awake_skill_id = SkillWGData.Instance:GetNormalSkillToAwakeSkill(skill_id)
                skill_id = awake_skill_id or skill_id
            end
        end
    end

    return skill_id
end

function SkillWGData:GetRealSkillId(skill_id, duan_atk)
    if not self:IsDuanSkill(skill_id) then
        return skill_id
    end

    if nil == duan_atk or duan_atk < 1 or duan_atk > COMMON_CONSTS.MAX_DUAN_ATK then
        duan_atk = 1
    end

    return skill_id + (duan_atk - 1) * 100
end

function SkillWGData.GetSkillBigType(skill_id)
    if 0 == skill_id then
        return SKILL_BIG_TYPE.PASSIVITY
    end

    if SkillWGData.Guild_BianShen_Common_Skill_Id == skill_id then
        return SKILL_BIG_TYPE.PASSIVITY
    end

    local skill_cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_id, 1)
    if skill_cfg then
        local skill_type = skill_cfg.skill_type
        if skill_type == SKILL_TYPE.SKILL_1 or skill_type == SKILL_TYPE.SKILL_3
        or skill_type == SKILL_TYPE.SKILL_13 or skill_type == SKILL_TYPE.SKILL_18 or skill_type == SKILL_TYPE.SKILL_28 then
            return SKILL_BIG_TYPE.NORMAL
        end

        return SKILL_BIG_TYPE.INITIATIVE
    end

    return SKILL_BIG_TYPE.PASSIVITY
end

function SkillWGData.GetSkillBtnIndex(skill_id)
    if not skill_id or 0 == skill_id then
        return -1
    end

    if SkillWGData.Guild_BianShen_Common_Skill_Id == skill_id then
        return 1
    end

    local skill_cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_id, 1)
    if skill_cfg then
        local skill_type = skill_cfg.skill_type
        if skill_type == SKILL_TYPE.SKILL_1 or skill_type == SKILL_TYPE.SKILL_3
        or skill_type == SKILL_TYPE.SKILL_13 or skill_type == SKILL_TYPE.SKILL_18 or skill_type == SKILL_TYPE.SKILL_28  then
            return 1
        end

        if skill_type ~= SKILL_TYPE.SKILL_2 then
            return -1
        end
    end

    local base_prof = math.floor(skill_id / 10) % 10
    local skill_index = (skill_id % 10) + 1
    if GameEnum.ROLE_PROF_1 <= base_prof and base_prof <= GameEnum.ROLE_PROF_4 and
        2 <= skill_index and skill_index <= 8 then
        return skill_index
    end

    return -1
end








-- 创建技能信息table
function SkillWGData:CreateSkillInfo(source)
    source = source or {}
    return {
        index = source.index or 0,
        skill_id = source.skill_id or 0,
        level = source.level or 0,
        last_perform = source.last_perform or 0,
        cd_end_time = source.cd_end_time or 0,
        cost_mp = source.cost_mp or 0,
        awake_level = source.awake_level or 0,
        cd_past_time = source.cd_past_time or 0,
    }
end

function SkillWGData:DeleteSkill(skill_index)
    self.skill_list[skill_index] = nil
end

function SkillWGData:CelarJinglingSkillIndexList()
    self.jingling_skill_index_list = {}
end

function SkillWGData:ClearJinglingSkillIndex()
    self.jingling_skill_index = -1
end

function SkillWGData:GetActJinglingSkillIndex()
    return self.jingling_skill_index
end

function SkillWGData:GetJinglingSkillNum()
    return #self.jingling_skill_index_list
end

-- 精灵技能切换到下一个
function SkillWGData:JinglingSkillTransferToNext()
    if #self.jingling_skill_index_list <= 1 then
        return
    end

    local skill_end_time = SkillWGData.Instance:GetSkillCDEndTime(self.jingling_skill_index)
    if skill_end_time > Status.NowTime * 1000 then -- 还在cd中
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.SkillCD)
        return
    end

    local index = 0
    for k, v in ipairs(self.jingling_skill_index_list) do
        if self.jingling_skill_index == v then
            index = k - 1
        end
    end

    index = (index + 1) % (#self.jingling_skill_index_list)

    self.jingling_skill_index = self.jingling_skill_index_list[index + 1]

    local skill_info = self.skill_list[self.jingling_skill_index]
    if nil ~= skill_info then
        GlobalEventSystem:Fire(MainUIEventType.ROLE_SKILL_LIST)
    end
end

function SkillWGData:ChangeJiBanLockInfo(info)
    self.lock_skill_info = info
end

function SkillWGData:GetJiBanLockInfo(id)
    if self.lock_skill_info and not IsEmptyTable(self.lock_skill_info) then
        if self.lock_skill_info.skill_id == tonumber(id) then
            return self.lock_skill_info
        end
    end
    return {}
end

function SkillWGData:GetJiBanLockInfoByIndex(index)
    if self.lock_skill_info and not IsEmptyTable(self.lock_skill_info) then
        if self.lock_skill_info.index == tonumber(index) then
            return self.lock_skill_info
        end
    end
    return {}
end

function SkillWGData:ChangeSkillInfo(info)
    if not self.skill_list[info.index] then
        self.skill_list[info.index] = self:CreateSkillInfo(info)
    else
        self.skill_list[info.index].index = info.index
        self.skill_list[info.index].skill_id = info.skill_id
        self.skill_list[info.index].level = info.level
        self.skill_list[info.index].last_perform = info.last_perform
        self.skill_list[info.index].skill_exp = info.skill_exp
        self.skill_list[info.index].awake_level = info.awake_level
        self.skill_list[info.index].cd_past_time = info.cd_past_time
    end

    local skill_info = self.skill_list[info.index]
    local cfg = self:GetSkillConfigByIdLevel(info.skill_id, info.level)
    -- 处理CD
    local cd_s = cfg and cfg.cd_s or COMMON_CONSTS.SKILL_GLOBAL_CD
    -- 天赋技能减少CD
    local reduce_cd = 0
    local skill_level = RoleWGData.Instance:GetRoleTalentSkillLevel(SkillWGData.TianFu_Skill_Id_202) or 0
    local skill_cfg_1 = RoleWGData.Instance:GetRoleTalentSkillCfg(SkillWGData.TianFu_Skill_Id_202, skill_level)
    if skill_cfg_1 then
        if skill_info.skill_id == skill_cfg_1.param_c or skill_info.skill_id == skill_cfg_1.param_d then
            reduce_cd = skill_cfg_1.param_b * 1000
        end
    end

    -- local cd = (info.last_perform + cd_s - reduce_cd - cur_pi_time)
    local cd = cd_s - info.cd_past_time - reduce_cd
    cd = cd <= 0 and 0 or cd
    -- 特殊需求，多段伤害技能前期，只制灰，不跑CD
    local fenduan_skill_cfg = self:GetFenDuanSkill(info.skill_id)
    if fenduan_skill_cfg and cd_s - cd < fenduan_skill_cfg.total_time then
        cd = cd + fenduan_skill_cfg.total_time
    end

    -- 解决服务端和客户端交互引起的误差
    if SkillWGData.GetSkillBigType(info.skill_id) ~= SKILL_BIG_TYPE.NORMAL then
        cd = cd + 300
    end
    -- 特殊需求，多段伤害技能前期，只制灰，不跑CD
    self.skill_list[info.index].cd_end_time = Status.NowTime * 1000 + cd
    self:SetGlobalCDEndTime(Status.NowTime * 1000 + COMMON_CONSTS.SKILL_GLOBAL_CD)

    -- 耗蓝
    if nil ~= cfg then
        skill_info.cost_mp = cfg.cost_mp or 0
    end

    --职业技能indexGetRoleBaseProf
    local base_prof = RoleWGData.Instance:GetRoleProf()

    if skill_info.skill_id == 100 + base_prof * 10 + 1 then
        self.skill_index_1 = info.index
    elseif skill_info.skill_id == 100 + base_prof * 10 + 2 then
        self.skill_index_2 = info.index
    elseif skill_info.skill_id == 100 + base_prof * 10 + 3 then
        self.skill_index_3 = info.index
    elseif skill_info.skill_id == 100 + base_prof * 10 + 4 then
        self.skill_index_4 = info.index
    elseif skill_info.skill_id == 100 + base_prof * 10 + 5 then
        self.skill_index_5 = info.index
    elseif skill_info.skill_id == 100 + base_prof * 10 + 6 then
        self.skill_index_6 = info.index
    end

    for k, v in pairs(self.notify_data_change_callback_list) do--技能有变化，通知观察者，带消息体
        v(skill_info)
    end
end

function SkillWGData:IsOnceSkill(skill_id)
    if nil == skill_id then return false end

    return skill_id >= 200 and skill_id <= 203
end

function SkillWGData:GetOnceSkillIndex()
    local prof = RoleWGData.Instance:GetRoleProf()
    local skill_id = -1

    if GameEnum.ROLE_PROF_1 == prof then skill_id = 200 end
    if GameEnum.ROLE_PROF_2 == prof then skill_id = 201 end
    if GameEnum.ROLE_PROF_3 == prof then skill_id = 202 end
    if GameEnum.ROLE_PROF_4 == prof then skill_id = 203 end

    local skill_info = self:GetSkillInfoById(skill_id)
    if nil == skill_info then
        return - 1
    end

    return skill_info.index
end

function SkillWGData:GetDefaultSkillIndex()
    return self.default_skill_index
end

function SkillWGData:SetDefaultSkillIndex(default_skill_index)
    self.default_skill_index = default_skill_index
end

function SkillWGData:GetIsTransferProfSkill(skill_id, prof)
    local prof = RoleWGData.Instance:GetRoleProf()
    for i = 1, 2 do
        if skill_id == 100 + prof * 10 + 3 + (3 - i) then
            return true
        end
    end
    return false
end

function SkillWGData:GetSkillList()
    return self.skill_list
end

function SkillWGData:GetCurrSkillList()
    self.curr_skill_list = {}
    if 0 == #self.skill_list then
        return self.curr_skill_list
    end

    local has_skill_1 = false
    for k,v in pairs(self.skill_list) do
        if SkillWGData.GetSkillBigType(v.skill_id) == SKILL_BIG_TYPE.NORMAL then
            if not has_skill_1 then
                has_skill_1 = true
                self.curr_skill_list[v.index] = v
            end
        else
            self.curr_skill_list[v.index] = v
        end
    end

    return self.curr_skill_list
end

function SkillWGData:GetCurrPetSkillList()
    self.cur_pet_skill_list = {}
    if 0 == #self.skill_list then return self.cur_pet_skill_list end
    for k,v in pairs(self.skill_list) do
        if (v.skill_id >= 400 and v.skill_id < 500) then
            self.cur_pet_skill_list[v.index] = v
        end
    end
    return self.cur_pet_skill_list
end

--绑定数据改变时的回调方法.用于任意技能有更新时进行回调
function SkillWGData:NotifyDataChangeCallBack(callback)
    for k, v in pairs(self.notify_data_change_callback_list) do
        if v == callback then
            return
        end
    end
    self.notify_data_change_callback_list[#self.notify_data_change_callback_list + 1] = callback
end

--移除绑定回调
function SkillWGData:UnNotifyDataChangeCallBack(callback)
    for k, v in pairs(self.notify_data_change_callback_list) do
        if v == callback then
            self.notify_data_change_callback_list[k] = nil
            return
        end
    end
end

function SkillWGData:GetNextLevelSkillVo(skill_id)
    local skill_info = self:GetSkillInfoById(skill_id)
    local now_level = skill_info ~= nil and skill_info.level or 0
    return self:GetSkillConfigByIdLevel(skill_id, now_level + 1)
end

function SkillWGData:GetLearnSkillIsEnoughLevel(skill_id)
    local next_skill_vo = self:GetNextLevelSkillVo(skill_id)
    if next_skill_vo ~= nil and next_skill_vo.learn_level_limit > RoleWGData.Instance.role_vo.level then
        return false
    end
    return true
end

function SkillWGData:GetLeanSkillIEnoughNWS(skill_id)
    local next_skill_vo = self:GetNextLevelSkillVo(skill_id)
    if next_skill_vo ~= nil and next_skill_vo.zhenqi_cost > RoleWGData.Instance.role_vo.nv_wa_shi then
        return false
    end
    return true
end

function SkillWGData:GetLeanSkillIEnoughCoin(skill_id)
    local next_skill_vo = self:GetNextLevelSkillVo(skill_id)
    if next_skill_vo ~= nil and not RoleWGData.GetIsEnoughAllCoin(next_skill_vo.coin_cost) then
        return false
    end
    return true
end

function SkillWGData:GetLeanSkillIEnoughItem(skill_id)
    if self:GetIsTransferProfSkill(skill_id) then
        local next_skill_vo = self:GetNextLevelSkillVo(skill_id)
        local item_num = ItemWGData.Instance:GetItemNumInBagById(self.UP_SKILL_ITEM_ID)
        if next_skill_vo ~= nil and item_num < next_skill_vo.item_cost then
            return false
        else
            return true
        end
    end
    return true
end

--获得某个技能的可学习状态
--应付多种状态，使用时用取模方法
function SkillWGData:GetLearnSkillStatus(skill_id)
    local flag = 0
    local skill_info = self:GetSkillInfoById(skill_id)
    local now_level = skill_info ~= nil and skill_info.level or 0

    local next_skill_vo = self:GetSkillConfigByIdLevel(skill_id, now_level + 1)
    if next_skill_vo == nil then
        flag = 10000 --满级
        return flag
    end

    if next_skill_vo.learn_level_limit > RoleWGData.Instance.role_vo.level then
        flag = 10000 + 1--人物等级不足
    elseif "" ~= next_skill_vo.item_cost and 0 ~= next_skill_vo.item_cost
        and ItemWGData.Instance:GetItemNumInBagById(next_skill_vo.stuff_id) < next_skill_vo.item_cost then
        flag = 10000 + 1 * 1000 --物品不足
    end
    if flag ~= 0 then--若不能升级学习直接返回
        return flag
    end
    flag = 20000
    if now_level == 0 then
        flag = 20000--可学
    else
        flag = 20000 + 1 * 10--可升级
    end
    return flag
end

-- 技能是否可学
function SkillWGData:CanLearnSkill(skill_id)
    return self:GetLearnSkillStatus(skill_id) == 20000
end

function SkillWGData:SetSkillCD(skill_index, cd_s)
    if nil == self.skill_list[skill_index] then
        return
    end

    self:SetGlobalCDEndTime(Status.NowTime * 1000 + COMMON_CONSTS.SKILL_GLOBAL_CD)
    self.skill_list[skill_index].cd_end_time = Status.NowTime * 1000 + cd_s
end

function SkillWGData:IsInSkillCD(skill_index, skill_id)
    if self:GetIsSiXiangSkill(skill_id) then
        local cur_nuqi, need_nuqi, enough = FightSoulWGData.Instance:GetSkillNuQiData()
        return not enough
    end

    if self:GetIsHaloSkill(skill_id) then
        return not FiveElementsWGData.Instance:GetIsCanDoHaloSkill()
    end

    if skill_id then
        local skill_data = self:GetSkillInfoById(skill_id)
        skill_index = skill_data and skill_data.index or skill_index
    end
    return self:GetSkillCDEndTime(skill_index, skill_id) > Status.NowTime * 1000
end

function SkillWGData:CanUseSkill(skill_id)
    if self:CheckIsLimitUseSkill(skill_id) then
        return false, 0
    end

    local skill_index = self:GetRealSkillIndex(skill_id)
    if skill_index == nil then
        return false, 0
    end

    if self:IsInSkillCDEx(skill_index, skill_id) then
        return false, 0
    end

    local skill_cfg = self:GetSkillConfigByIndex(skill_index)

    if not skill_cfg then
        return false, 0
    end

    return true, skill_cfg.distance
end

function SkillWGData:CanSkillDistance(skill_id)
    local skill_index = self:GetRealSkillIndex(skill_id)
    if skill_index == nil then
        return 0
    end

    local skill_cfg = self:GetSkillConfigByIndex(skill_index)
    if not skill_cfg then
        return 0
    end
    return skill_cfg.distance
end

-- 不算全局CD
function SkillWGData:IsInSkillCDEx(skill_index, skill_id)
    if self:GetIsSiXiangSkill(skill_id) then
        local cur_nuqi, need_nuqi, enough = FightSoulWGData.Instance:GetSkillNuQiData()
        return not enough
    end

    if self:GetIsHaloSkill(skill_id) then
        return not FiveElementsWGData.Instance:GetIsCanDoHaloSkill()
    end

    if self:GetIsBeastsSkill(skill_id) then     --驭兽已单独处理了，有存在技能显隐，有延迟播放技能会被过滤
        return false
    end

    if skill_id then
        local skill_data = self:GetSkillInfoById(skill_id)
        skill_index = skill_data and skill_data.index or skill_index
    end

    if nil == self.skill_list[skill_index] then
        return false
    end

    return self.skill_list[skill_index].cd_end_time > Status.NowTime * 1000
end

function SkillWGData:GetSkillCDEndTime(skill_index, skill_id)
    if nil == self.skill_list[skill_index] then
        return 0
    end

    -- 普攻，龙珠技能 宠物 驭兽技能不计算全局CD
    if skill_id and (SkillWGData.GetSkillBigType(skill_id) == SKILL_TYPE.SKILL_1
    or SkillWGData.IsPetSkill(skill_id)
    or SkillWGData.Instance:GetIsLongZhuSkill(skill_id)
    or SkillWGData.Instance:GetIsBeastsSkill(skill_id)) then
        return math.max(0, self.skill_list[skill_index].cd_end_time)
    end
    
    return math.max(self.global_cd_end, self.skill_list[skill_index].cd_end_time)
end

function SkillWGData:SetGlobalCDEndTime(value)
    self.global_cd_end = value
end

function SkillWGData:GetGlobalCDEndTime()
    return self.global_cd_end
end

function SkillWGData:RandSkill()
    local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()

    local flag = true
    local hit_index = -1
    if self.can_next_skill_time < Status.NowTime * 1000 then
        for _, v in pairs(self.skill_list) do
            local skill_index = v.index
            flag = true
            if skill_index == self.default_skill_index then
                flag = false
            end

            if self:IsOnceSkill(v.skill_id) then
                flag = false
            end

            if skill_index == self.skill_index_1 then
                flag = self.skill_auto_flag_1
            elseif skill_index == self.skill_index_2 then
                flag = self.skill_auto_flag_2
            elseif skill_index == self.skill_index_3 then
                flag = self.skill_auto_flag_3
            elseif skill_index == self.skill_index_4 then
                flag = self.skill_auto_flag_4
            elseif skill_index == self.skill_index_5 then
                flag = self.skill_auto_flag_5
            elseif skill_index == self.skill_index_6 then
                flag = self.skill_auto_flag_6
            elseif skill_index == self.skill_index_7 then
                flag = self.skill_auto_flag_7
            end

            if flag and v.cd_end_time >= 0
                and v.cd_end_time <= Status.NowTime * 1000
                and mainrole_vo.mp >= v.cost_mp
                and v.skill_id ~= 264
            then-- 判断技能ID是否等于264(灵石技能中的冰冻技能)
                hit_index = skill_index
                break
            end
        end
        if - 1 == hit_index then
            local common_skill = self.skill_list[self.default_skill_index]
            if nil ~= common_skill and common_skill.cd_end_time <= Status.NowTime * 1000 then
                hit_index = self.default_skill_index
            end
        end
    end

    return hit_index
end

function SkillWGData:RepleCfgContent(source, skill_vo)
    if not source or not skill_vo then
        return ""
    end
    local len = string.len(source)
    local rule = '%[([^%]]-)%]%%'
    local key = ""
    local rep = ""

    local var = 1
    while var <= len do
        var = var + 1
        local i, j = string.find(source, rule)
        if i and i < var then
            var = j
            key = string.sub(source, i + 1, j - 2)
            rep = skill_vo[key] / 100
            source = string.gsub(source, '%[' .. key .. '%]%%', rep .. "%%")
        end
    end
    len = string.len(source)
    rule = '%[(.-)%]'
    var = 1
    while var <= len do
        var = var + 1
        local i, j = string.find(source, rule)
        if i and i < var then
            var = j
            key = string.sub(source, i + 1, j - 1)
            rep = skill_vo[key]
            source = string.gsub(source, '%[' .. key .. '%]', rep)
        end
    end
    return source
end

function SkillWGData:GetRoleSkillRemindList()
    local data_list = self:GetSkillListByType(1) -- 1 主动技能
    local item_list = {}
    local item_key = {}
    for k, v in pairs(data_list) do
        local skill_info = SkillWGData.Instance:GetSkillInfoById(v.skill_id)
        local now_level = skill_info ~= nil and skill_info.level or 0
        for level = 1, 1000 do
            local next_skillvo = SkillWGData.Instance:GetSkillConfigByIdLevel(v.skill_id, now_level + level)
            if nil ~= next_skillvo then
                if item_key[next_skillvo.stuff_id] == nil then
                    item_key[next_skillvo.stuff_id] = next_skillvo.stuff_id
                    item_list[#item_list + 1] = next_skillvo.stuff_id
                end
            else
                break
            end
        end
    end
    return item_list
end

-- 是否有技能可升级
function SkillWGData:CheckCanUpgrade()
    local data_list = self:GetSkillListByType(1) -- 1 主动技能
    for k, v in pairs(data_list) do
        local skill_info = SkillWGData.Instance:GetSkillInfoById(v.skill_id)
        local now_level = skill_info ~= nil and skill_info.level or 0
        local next_skillvo = SkillWGData.Instance:GetSkillConfigByIdLevel(v.skill_id, now_level + 1)
        if nil ~= next_skillvo then
            local item_num = ItemWGData.Instance:GetItemNumInBagById(next_skillvo.stuff_id)
            if item_num ~= 0 and item_num >= next_skillvo.item_cost then
                return 1
            end
        end
    end
    return 0
end

function SkillWGData:SetSkillOtherSkillInfo(protocol)
    if protocol.skill124_effect_baoji == 1 and nil ~= self.other_skill_info.skill124_effect_star then
        self.other_skill_info.skill124_effect_star = self.other_skill_info.skill124_effect_star + 1
    else
        self.other_skill_info.skill124_effect_star = protocol.skill124_effect_star
    end
    self.other_skill_info.skill124_effect_baoji = protocol.skill124_effect_baoji
end

function SkillWGData:GetSkillOtherSkillInfo()
    return self.other_skill_info
end

function SkillWGData:IsDuanSkill(skill_id)
    return false
end

function SkillWGData:GetProfDuanSkillId()
    local sex, prof = RoleWGData.Instance:GetRoleSexProf()
    return use_prof_skill_list[sex][prof][1]
end

function SkillWGData:GetNextDuanAtk()
    return self.next_duan_atk
end

function SkillWGData:SetNextDuanAtk(next_duan_atk)
    if next_duan_atk > 3 or next_duan_atk < 1 then
        next_duan_atk = 1
    end

    self.next_duan_atk = next_duan_atk
    self.next_duan_reset_time = Status.NowTime * 1000 + COMMON_CONSTS.DUAN_SKILL_RESET_TIME
    GlobalEventSystem:Fire(MainUIEventType.ROLE_SKILL_DUAN_ATK_CHANGE)
end

function SkillWGData:ResetNextDuanAtk()
    self.next_duan_atk = 1
    self.next_duan_reset_time = nil
    GlobalEventSystem:Fire(MainUIEventType.ROLE_SKILL_DUAN_ATK_CHANGE)
end

function SkillWGData:IsBuffSkill()
    return false
end

-- 根据技能类型获取技能数据列表  1主动  2被动
-- force 强制刷新
function SkillWGData:GetSkillListByType(skill_type, force)
    local skill_cfg_list = {}
    if skill_type == 1 then -- 主动技能
        skill_cfg_list = self:GetNowSkillInfo(nil, nil, force)
    elseif skill_type == 2 then
        local prof_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
        local sex, prof = RoleWGData.Instance:GetRoleSexProf()

        local function beiDongSkillIsOpen(complete_type, v)
            if complete_type == 1 then
                return prof_zhuan >= v.prof_level
            elseif complete_type == 2 then
                return FuBenPanelWGData.Instance:GetXinMoFBRoleIsOpen(v.value)
            elseif complete_type == 3 then
                return TianShuWGData.Instance:GetTianShuIsFetch(v.value)
            elseif complete_type == 5 then
                return TianShenJuexingWGData.Instance:GetTianShenAwakenIsCompleted()
            elseif complete_type == 6 then
                return BossOfferWGData.Instance:GetBossOfferAwakenIsCompleted()
            elseif complete_type == 7 then
                return XiuXianShiLianWGData.Instance:GetTianShenAwakenFetchFlag(v.index)
            elseif complete_type == 8 then
                return XiuZhenRoadWGData.Instance:GetXiuZhenSkillIsActive(v.value)
            elseif complete_type == 9 then
                return EquipTargetWGData.Instance:GetEquipSkillAct(v.value)
            end
    
            return false
        end

        local passive_skill_auto = ConfigManager.Instance:GetAutoConfig("roleskill_auto").passive_skill
        for i, v in ipairs(passive_skill_auto) do
            if prof == v.prof_limit and sex == v.sex_limit
                or v.prof_limit == GameEnum.ROLE_PROF_NOLIMIT and v.sex_limit == GameEnum.SEX_NOLIMIT
            then
                local cfg = __TableCopy(v)
                cfg.is_open = beiDongSkillIsOpen(cfg.complete_type, cfg)
                table.insert(skill_cfg_list, cfg)
            end
        end

        table.sort(skill_cfg_list, function(a, b)
            if a.is_open ~= b.is_open then
                return a.is_open
            else
                return a.index < b.index
            end
        end)
    end

    return skill_cfg_list
end

function SkillWGData:GetBianShenSkill(tianshen_index, ignore_sore)
    local skill_list = {}
    local bianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(tianshen_index)
    if not bianshen_cfg then return skill_list end
    local skill_cfg = TianShenWGData.Instance:GetTianshenSkillList(bianshen_cfg, true)
    for i,v in ipairs(skill_cfg) do
        local skill_info = self.tianshen_skill_cfg[tonumber(v)][1]
        table.insert(skill_list, skill_info)
    end

    if not ignore_sore then
        table.sort(skill_list,SortTools.AscFunc('skill_id'))
    end

    -- 插入一個普攻技能
    local normal_skill = TianShenWGData.Instance:GetSkillByAttackSeq(1, true)
    normal_skill = self.tianshen_skill_cfg[normal_skill][1]
    table.insert(skill_list,1,normal_skill)
    return skill_list
end

-- 仙盟战 获得战车技能
function SkillWGData:GetXMZCarSkill(skill_id)
    return self.xmz_car_skill_cfg[skill_id]
end

function SkillWGData:GetIsXMZCarSkill(skill_id)
    return self.xmz_car_skill_cfg[skill_id] ~= nil
end

-- 仙盟战 根据战车id获得战车技能配置列表
function SkillWGData:GetXMZCarSkillCfgList()
    local other_cfg = GuildBattleRankedWGData.Instance:GetCfgOther()
    local skill_str_list = Split(other_cfg.skill_list, "|", true)
    -- local normal_skill = SkillWGData.GetSkillinfoConfig(SkillWGData.Skill_Id_270)
    local result = {}
    --插入一個普攻技能
    -- table.insert(result, normal_skill)
    for i, skill_id in ipairs(skill_str_list) do
        local skill_cfg = self:GetXMZCarSkill(tonumber(skill_id))
        table.insert(result, skill_cfg)
    end
    return result
end

--根据技能类型获取对应数据
function SkillWGData:GetPassiveSkillByType(skilltype,passivecfg)
    skilltype = skilltype or 1
    local skill_data_type = skilltype ~= GameEnum.PASSIVE_SKILL.AFTER_TYPE and skilltype or GameEnum.PASSIVE_SKILL.BEFORE_TYPE
    passivecfg = passivecfg or ConfigManager.Instance:GetAutoConfig('roleskill_auto').passive_skill
    local cfg = {}
    for i,v in ipairs(passivecfg) do
        if v.skill_type == skill_data_type and v.shield ~= 1 then
            table.insert(cfg,v)
        end
    end

    if(skilltype == GameEnum.PASSIVE_SKILL.BEFORE_TYPE or skilltype == GameEnum.PASSIVE_SKILL.AFTER_TYPE) then
        local start, endPos
        local result = {}
        if(skilltype == GameEnum.PASSIVE_SKILL.BEFORE_TYPE) then
            start = 1
            endPos = GameEnum.PASSIVE_SKILL.SIGN_NUM
        else
            start = GameEnum.PASSIVE_SKILL.SIGN_NUM + 1
            endPos = #cfg
        end

        for i = start, endPos do
            table.insert(result, cfg[i])
        end

        return result
    end

    return cfg
end

function SkillWGData:GetPassiveSkillOpen(index)
    return self.passive_skill_open[index]
end

-- 是否是被动技能
function SkillWGData.IsBeiDongSkill(skill_id)
    local roleskill_auto = ConfigManager.Instance:GetAutoConfig("roleskill_auto")
    for k, v in pairs(roleskill_auto.skill_show) do
        if skill_id == v.skill_id then
            return 2 == v.skill_type
        end
    end
    return false
end

function SkillWGData:GetPassiveSkillByIndex(skill_index)
    local passive_skill_auto = ConfigManager.Instance:GetAutoConfig("roleskill_auto").passive_skill
    for i, v in pairs(passive_skill_auto) do
        if v.index == skill_index then
            return v
        end
    end
end

function SkillWGData.GetSkillinfoConfig(skill_id)
    return ConfigManager.Instance:GetAutoConfig("roleskill_auto").normal_skill[skill_id]
end

function SkillWGData.GetSkillFunOpenCfg(skill_id)
    local skill_funopen_cfg = ConfigManager.Instance:GetAutoConfig("roleskill_auto").funopen_list_skill
    for k,v in pairs(skill_funopen_cfg) do
        if v.skill_id == skill_id then
            return v
        end
    end
end

function SkillWGData.GetSkillPassiveCfg(skill_id)
    local passive_skill_cfg = ConfigManager.Instance:GetAutoConfig("roleskill_auto").passive_skill
    for k,v in pairs(passive_skill_cfg) do
        if v.icon == skill_id then
            return v
        end
    end
end

local skill_new_cacha = 0
function SkillWGData:CheckIsNew(skill_list, is_init)
    if is_init == 1 then
        return
    end

    for k, v in pairs(skill_list) do
        if self.skill_list[k] == nil then
            if ViewManager.Instance:IsOpen(GuideModuleName.PetHatchSuccessView) then
                skill_new_cacha = v.skill_id
            else
                TipWGCtrl.Instance:ShowGetNewSkillView(v.skill_id)
            end
            break
        end
    end
end

--第一个宠物自动出战的特殊处理
function SkillWGData.CheckPetSkillNew()
    if skill_new_cacha > 0 then
        TipWGCtrl.Instance:ShowGetNewSkillView(skill_new_cacha)
        skill_new_cacha = 0
    end
end

function SkillWGData.GetSkillBloodDelay(skill_id, deliverer)
    if deliverer and not deliverer:IsDeleted() then --获取受击时间
        local anim_name = nil

        if deliverer:IsMonster() then
            local skill_cfg = SkillWGData.GetMonsterSkillConfig(skill_id)

            if skill_cfg then
                anim_name = skill_cfg.skill_action
            end
        else
            local info_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id)
            if nil ~= info_cfg then
                anim_name = SkillWGData.GetActionString(skill_id, info_cfg.action, deliverer.attack_index)
            end

        end

        if anim_name then
            local action_time_record = deliverer:GetActionTimeRecord(anim_name) or {}
            return action_time_record.hit_time or 1
        end
    end
    return 1
end

function SkillWGData.GetMonsterSkillConfig(skill_id)
    local data = ConfigManager.Instance:GetAutoConfig("monsterskill_auto").skill_list[skill_id]
    if data == nil then
        data = ConfigManager.Instance:GetAutoConfig("roleskill_auto").other_monster_skill[skill_id]
    end

    return data
end

-- 是否群技能
function SkillWGData.IsAoeSkill(skill_id)
    local is_aoe = false
    local skill_config = SkillWGData.Instance:GetSkillClientConfig(skill_id)
    if nil ~= skill_config then
        is_aoe = skill_config.is_aoe == 1
    else
        skill_config = SkillWGData.GetMonsterSkillConfig(skill_id)
        -- 怪物aoe技能判断：同步后端定义的aoe的判断
        if nil ~= skill_config then
            if (skill_id >= 11001 and skill_id <= 12000) or (skill_id >= 13001 and skill_id <= 14000) then
                is_aoe = true
            end
        end
    end

    return is_aoe
end

-- 获取天神技能描述
function SkillWGData:GetTianShenSkillDes(skill_id, level,user_dark_green, other_hurt_percent, other_description)
    local client_skill_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, level)
    local tianshen_skill = SkillWGData.Instance:GetTianShenSkillCfg(skill_id, level)
    if not client_skill_cfg then
        return nil
    end

    local description = client_skill_cfg.description
    if other_description then
        description = other_description
    end

    if not tianshen_skill then
        return description
    end

    return self:GetiSkillViewShowDesc(description, tianshen_skill,nil,nil,user_dark_green, other_hurt_percent)
end

-- 获取四象技能描述
function SkillWGData:GetSiXiangSkillDes(skill_id, skill_level, user_dark_green)
    local skill_cfg = self:GetSiXiangSkillById(skill_id, skill_level)
	local skill_client_cfg = self:GetSkillClientConfig(skill_id, skill_level)
    if IsEmptyTable(skill_cfg) or IsEmptyTable(skill_client_cfg) then
        return ""
    end

    local desc = skill_client_cfg.description
    return self:GetiSkillViewShowDesc(desc, skill_cfg, nil, nil, user_dark_green)
end

function SkillWGData:GetSkillClientConfig(skill_id, skill_level)
    local cfg_list = self.roleskill_client_cfg[skill_id]
    if not cfg_list then
        return nil
    end

    if #cfg_list > 1 then
        if skill_level == nil then
            return cfg_list[1]
        end

        for k,v in pairs(cfg_list) do
            if v.skill_level == skill_level then
                return v
            end
        end
    else
        return cfg_list[1]
    end
end

function SkillWGData:GetSkillClientMaxLv(skill_id)
    local cfg_list = self.roleskill_client_cfg[skill_id]
    if IsEmptyTable(cfg_list) then
        return 0
    else
        return #cfg_list
    end
end

function SkillWGData.GetSkillActionStr(obj_type, skill_id, attack_index)
    attack_index = attack_index or 1
    local anim_name = ""
    if obj_type == SceneObjType.Monster then
        local skill_cfg = SkillWGData.GetMonsterSkillConfig(skill_id)
        if skill_cfg then
            anim_name = skill_cfg.skill_action
        end
    else
        local info_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id)
        if nil ~= info_cfg then
            anim_name = SkillWGData.GetActionString(skill_id, info_cfg.action, attack_index)
        end
    end

    return anim_name
end

local combo_str1 = "combo%s"
local combo_str2 = "combo%s_%s"
local attack_str = "attack%s"
function SkillWGData.GetActionString(skill_id, action_enum, attack_index)
    
    -- 机甲冲刺技能
    if MechaWGData.Instance:GetSkillActiveNeedPartList(skill_id) ~= nil then
        return "rush_" .. action_enum
    end

    local result = ""
    -- 天神
    if TianShenWGData.Instance:GetIsTianShenNormalSkill(skill_id) then
        local use_index = TianShenWGData.Instance:GetIsTianShenNormalSkillWuXingIndex(skill_id) or 1
        result = string.format(combo_str2, use_index, attack_index)

    -- 仙盟变神
    elseif SkillWGData.Guild_BianShen_Common_Skill_Id == skill_id then
        result = string.format(combo_str2, 1, attack_index)

    -- 普攻
    elseif SkillWGData.GetSkillBigType(skill_id) == SKILL_BIG_TYPE.NORMAL then
        result = string.format(combo_str1, action_enum)

    -- 出场
    elseif action_enum == SceneObjAnimator.ChuChang then
        result = SceneObjAnimator.ChuChang
    else
        result = string.format(attack_str, action_enum)
    end

    -- print_error("----GetActionString---", skill_id, action_enum, attack_index, result)
    return result
end

function SkillWGData.GetExpEfficiency(level)
    local data = ConfigManager.Instance:GetAutoConfig("rest_auto").exp_efficiency
    for k,v in pairs(data) do
        if level == v.level then
            return v.std_exp * 6
        end
    end
end

function SkillWGData:SetSkillisFunctionOpen(flag)
    self.skill_is_function_open = flag
end

function SkillWGData:GetSkillisFunctionOpen()
    return self.skill_is_function_open
end

-- 是否作用到自己的技能
function SkillWGData:IsSkillToSelf(skill_id)
    local skill_info = self:GetSkillInfoById(skill_id)
    if not skill_info then
        return false
    end

    local skill_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id)
    return skill_cfg ~= nil and 1 == skill_cfg.larenmubiao
end

function SkillWGData.ChangeSkillID(skill_id)
    local attack_index, cur_combo_index = SkillWGData.Instance:GetAttackIndex(skill_id)
    -- 战斗坐骑普攻
    local is_fight_mount_skill = SkillWGData.Instance:IsFightMountSkill(skill_id)
    if is_fight_mount_skill then
        return skill_id, attack_index
    end

    -- 普攻
    if SkillWGData.GetSkillBigType(skill_id) == SKILL_BIG_TYPE.NORMAL then
        local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
        local special_appearance = main_role_vo.special_appearance
        -- 天神普攻
        if special_appearance == SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM
        and TianShenWGData.Instance:GetIsTianShenNormalSkill(skill_id) then
            skill_id = TianShenWGData.Instance:GetSkillByAttackSeq(attack_index, true)
            return skill_id, attack_index
        end

        -- 高达普攻
        if special_appearance == SPECIAL_APPEARANCE_TYPE.GUNDAM
        and SkillWGData.Instance:IsJiJiaSkill(skill_id) then
            skill_id = MechaWGData.Instance:GetSkillByAttackSeq(attack_index) or skill_id
            return skill_id, attack_index
        end

        -- 境界怒气普攻
        if special_appearance == SPECIAL_APPEARANCE_TYPE.XIUWEIBIANSHEN
        and SkillWGData.Instance:IsXiuXianSkill(skill_id) then
            skill_id = CultivationWGData.Instance:GetSkillByAttackSeq(attack_index, skill_id) or skill_id
            return skill_id, attack_index
        end

        -- 正常角色
        local sex, prof = RoleWGData.Instance:GetRoleSexProf()
        skill_id = use_prof_normal_skill_list[sex][prof][attack_index]
    end

    return skill_id, attack_index
end

function SkillWGData:SetExpEfficiencyInfo(protocol)
    self.exp_efficiency_info = protocol
end

function SkillWGData:GetExpEfficiencyInfo()
    return self.exp_efficiency_info
end

function SkillWGData:SetOfflineExpEfficiencyInfo(protocol)
    self.exp_offlineefficiency_info = protocol
end

function SkillWGData:GetOfflineExpEfficiencyInfo()
    return self.exp_efficiency_info
end

function SkillWGData:GetTaskSkillOpen(skill_id)
    local cfg = ConfigManager.Instance:GetAutoConfig("newer_cfg_auto").task_skill_open
    for i, v in pairs(cfg) do
        if v.skill_id == skill_id then
            return v
        end
    end
end

function SkillWGData.IsPetSkill(skill_id)
    local self = SkillWGData.Instance
    return self.pet_skill_cfg[skill_id] ~= nil
end

function SkillWGData.CanUseSkillWithoutTarget(skill_id)
    local self = SkillWGData.Instance
    if self.pet_skill_cfg[skill_id] ~= nil then
        if self.pet_skill_cfg[skill_id][1].param_e ~= nil and self.pet_skill_cfg[skill_id][1].param_e == 0 then
            return false
        end
    end

    return true
end

function SkillWGData:GetSkillBreakDesc(skill_id, desc, is_next)
    if is_next then
        desc = string.gsub(desc, Language.Skill.TxtAwake1, Language.Skill.TxtAwake2)
    end
    return self:GetSKillDescBySkillId(skill_id, nil, nil, desc)
end

function SkillWGData:GetSkillLevelCfgByLevel(skill_id, skill_level)
    return CheckList(self.skill_up_level_cfg, skill_id, skill_level)
end

function SkillWGData:GetSkillBreakCfgByLevel(skill_id, awake_level)
    local career_cfg = ConfigManager.Instance:GetAutoConfig("career_config_auto").skill_awken
    for i, v in pairs(career_cfg) do
        if v.skill_id == skill_id and v.awaken_level == awake_level then
            return v
        end
    end
end

function SkillWGData.GetZhuDongSkillCfgBySkillId(skill_id)
    local role_skill_auto = ConfigManager.Instance:GetAutoConfig("roleskill_auto")
    local cfg = role_skill_auto["s" .. skill_id]
    return cfg and cfg[1] or {}
end

function SkillWGData:GetUpgradeSkillCfg(skill_id)
    local jingjie_skill = ConfigManager.Instance:GetAutoConfig("roleskill_auto").jinjieskill
    return jingjie_skill[skill_id]
end

function SkillWGData:GetUpgradeSkillNumCfg(skill_id, skill_series, skill_grade)
    local career_cfg = ConfigManager.Instance:GetAutoConfig("career_config_auto").skill_upgrade
    for i, v in pairs(career_cfg) do
        if v.skill_id == skill_id and v.series_type == skill_series and v.grade == skill_grade then
            return v
        end
    end
end

function SkillWGData:GetAllUpGradeSkillCfg(skill_id, skill_info, skill_series, skill_grade)
    local list1 = self:GetSkillUpGradeInfo(skill_series, skill_grade)
    local list2 = self:GetUpgradeSkillNumCfg(skill_id, skill_series, skill_grade)
    local list3 = self:GetSkillBreakCfgByLevel(skill_id, skill_info and skill_info.awake_level or 1)
    return {list1, list2, list3}
end

function SkillWGData:GetSKillDescBySkillId(skill_id, skill_series, skill_grade, desc)
    local sex, prof = RoleWGData.Instance:GetRoleSexProf()
    skill_series = skill_series or self.skill_series
    skill_grade = skill_grade or self.skill_grade
    local cfg = self:GetSkillClientConfig(skill_id)
    desc = desc or cfg and cfg.description
    local skill_info = self:GetSkillInfoById(skill_id)
    local skill_level_cfg = self:GetSkillLevelCfgByLevel(skill_id, skill_info and skill_info.level or 1)
    local normal_info = zhudong_skill_list[sex][prof][skill_id] and self.GetZhuDongSkillCfgBySkillId(skill_id) or self:GetUpgradeSkillCfg(skill_id)
    local skill_upgrade_list = self:GetAllUpGradeSkillCfg(skill_id, skill_info, skill_series, skill_grade)
    return self:GetiSkillViewShowDesc(desc, normal_info, skill_level_cfg, skill_upgrade_list)
end

-- other_hurt_percent增加一个额外伤害比例参加计算
function SkillWGData:GetiSkillViewShowDesc(desc, normal_skill_cfg, skill_level_cfg, skill_upgrade_list, user_dark_green, other_hurt_percent)
    desc = desc or ""
    local starti, endj = 0,0
    local replate_t = {}
    for index = 1, 20 do
        local key
        --寻找参数
        starti,endj,key = string.find(desc,"{(.-)}",endj+1)

        if starti == nil or endj == nil or key == nil then
            break
        end

        local attr_list
        local is_add, is_role_level_param
        local param
        --是否+XX参数
        if string.find(key, "+") then
            attr_list = string.gsub(key, "+", "")
            is_add = true
        else
            attr_list = key
        end

        -- 是否为玩家等级变化 属性
        if string.find(key, "<level_change>") then
            is_role_level_param = true
            attr_list = string.gsub(attr_list, "<level_change>", "")
        end

        local is_millisecond = string.find(attr_list, "<millisecond>") ~= nil
        if is_millisecond then
            attr_list = string.gsub(attr_list, "<millisecond>", "")
        end

        local is_per = string.find(attr_list, "#") ~= nil
        if is_per then
            attr_list = string.gsub(attr_list, "#", "")
        end

        --"+"号通配符，所以要转义
        if is_add then
            param = "{%" .. key .. "}"
        else
            param = string.format("{%s}", key)
        end

        local color = COLOR3B.DEFAULT_NUM
        if user_dark_green then
            color = COLOR3B.GREEN
        end

        attr_list = Split(attr_list, "|")
        local sums = #attr_list > 1

        local skill_id = normal_skill_cfg and normal_skill_cfg.skill_id or 0
        local client_skill_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id)
        local is_tianshen_skill = false
        if client_skill_cfg then
            is_tianshen_skill = client_skill_cfg.skill_type == SKILL_TYPE.SKILL_4
        end

        local role_level = RoleWGData.Instance:GetRoleLevel()
        local value, normal_value, skill_level_value = 0, 0, 0
        local is_special_value = false
        for _, attr in pairs(attr_list) do
            is_special_value = string.find(attr, "attach_") ~= nil
            if normal_skill_cfg and normal_skill_cfg[attr] and normal_skill_cfg[attr] ~= "" then
                if is_special_value then
                    local value_str = normal_skill_cfg[attr]
                    local value_table = Split(value_str, "|")
                    local attach_id = tonumber(value_table[3]) or 0
                    local attach_info = self:GetSkillAttachInfoById(attach_id)
                    if attach_info and attach_info[0] then
                        normal_value = attach_info[0][3] or 0
                    end
                else
                    normal_value = normal_skill_cfg[attr]
                end

                if is_tianshen_skill then
                    --策划说 天神技能的固定伤害万分比要乘以2
                    if attr == "hurt_percent" then
                        normal_value = normal_value * 2
                    end
                end

                if attr == "hurt_percent" and other_hurt_percent and other_hurt_percent ~= 0 then
                    normal_value = normal_value + other_hurt_percent
                end

                value = sums and (value + normal_value) or normal_value
            elseif skill_level_cfg and skill_level_cfg[attr] and skill_level_cfg[attr] ~= "" then
                if is_special_value then
                   local value_str = skill_level_cfg[attr]
                   local value_table = Split(value_str, "|")
                   skill_level_value = tonumber(value_table[4]) or 0
                else
                   skill_level_value = skill_level_cfg[attr]
                end
                value = sums and (value + skill_level_value) or skill_level_value
            else
                if skill_upgrade_list then
                    for i, v in pairs(skill_upgrade_list) do
                        if v[attr] and v[attr] ~= "" then
                            value = sums and (value + v[attr]) or v[attr]
                        end
                    end
                end
            end
        end

        if type(value) == "string" then
            replate_t[param] = ToColorStr(value, color)
        elseif type(value) == "number" and value > 0 then
            if is_millisecond then
                value = value / 1000
            end

            if is_per then
                value = value / 100 .. "%%"
            end

            if is_add then
                value = "+" .. value
            end

            if is_role_level_param then
                value = value * role_level
            end

            replate_t[param] = ToColorStr(value, color)
        else
            replate_t[param] = ""
        end
    end

    for i, v in pairs(replate_t) do
        desc = string.gsub(desc, i, v)
    end
    return desc
end

function SkillWGData:SetCurUpGradeInfo(protocol)
    self.skill_grade = protocol.grade
    self.skill_series = protocol.cur_select_type
end

function SkillWGData:GetCurUpGradeInfo()
    return self.skill_grade, self.skill_series
end

function SkillWGData:GetUpGradeSkillInfoByTypeAndGrade(select_type, select_grade)
    local desc_list = {}
    local skill_list = self:GetNowSkillInfo(select_type, select_grade)
    local cfg = self:GetSkillUpGradeInfo(select_type, select_grade)
    if cfg == nil then
        return desc_list
    end

    for i, v in pairs(skill_list) do
        local t = {}
        if cfg.add_skill_id_1 == v.skill_id then
            t.desc = cfg.skill_desc_add_1
        elseif cfg.add_skill_id_2 == v.skill_id then
            t.desc = cfg.skill_desc_add_2
        else
            t.desc = cfg["skill_desc_" .. tonumber(v.skill_id)%10]
        end
        t.desc = self:GetSKillDescBySkillId(v.skill_id, select_type, select_grade, t.desc)
        t.skill_id = v.skill_id
        t.skill_name = v.skill_name
        table.insert(desc_list, t)
    end
    return desc_list
end

function SkillWGData:GetSkillUpGradeProfLimit(grade)
   local career_cfg = ConfigManager.Instance:GetAutoConfig("career_config_auto").skill_upgrade_limit
    for i, v in pairs(career_cfg) do
        if v.grade == grade then
            return v.zhuan_level_limit
        end
    end
end

function SkillWGData:GetSkillUpGradeInfo(series, grade)
    local prof = RoleWGData.Instance:GetRoleProf()
    local career_cfg = ConfigManager.Instance:GetAutoConfig("career_config_auto").skill_change
    for i, v in pairs(career_cfg) do
        if prof == v.prof and v.serious_type == series and v.grade == grade then
            return v
        end
    end
end

function SkillWGData:GetSkillResetCfg()
   local career_cfg = ConfigManager.Instance:GetAutoConfig("career_config_auto").other[1]
   return career_cfg
end

function SkillWGData:GetNowSkillInfo(skill_series, skill_grade, force)
    local skill_list = {}
    local sex, prof = RoleWGData.Instance:GetRoleSexProf()
    local role_skill_auto = ConfigManager.Instance:GetAutoConfig("roleskill_auto")

    skill_series = skill_series or self.skill_series
    skill_grade = skill_grade or self.skill_grade

    --拿到普通的4个主动技能
    if not self.normal_skill_cfg or force then
        self.normal_skill_cfg = {}
        local prof_skill_list = use_prof_skill_list[sex][prof]
        if prof_skill_list then
            for i = 1, 4 do
                local skill_id = prof_skill_list[i]
                local skill_cfg = role_skill_auto[string.format("s%d", skill_id)]
                table.insert(self.normal_skill_cfg, skill_cfg[1])
            end
        else
            print_error("----哪一个没有----", sex, prof)
        end
    end
    skill_list = DeepCopy(self.normal_skill_cfg)

    --判断是否进阶
    if skill_grade and skill_series and skill_grade ~= 0 and skill_series ~= 0 then
        --取进阶技能
        local cfg = self.skill_upgrade_change_cfg[sex][prof][skill_series]
        local upgrade_skill_cfg = role_skill_auto.jinjieskill
        for k, v in ipairs(cfg) do
            if skill_grade >= v.grade then
                for i = 1, 2 do
                    local skill_id = v["add_skill_id_" .. i]
                    if skill_id > 0 then
                        table.insert(skill_list, DeepCopy(upgrade_skill_cfg[skill_id]))
                    end
                end
            end
        end
    end

    --判断是否觉醒，觉醒了换名称，图标
    for i, v in pairs(skill_list) do
        local skill_info = self:GetSkillInfoById(v.skill_id)
        if skill_info then
            local skill_break_cfg = SkillWGData.Instance:GetSkillBreakCfgByLevel(v.skill_id, skill_info.awake_level)
            if skill_break_cfg then
                v.skill_name = skill_break_cfg.skill_name .. "·" .. v.skill_name
                --v.skill_icon_res = skill_break_cfg.skill_icon_id
            end
        end
    end

    return skill_list
end

function SkillWGData:GetAwakeForItem()
    local sex, prof = RoleWGData.Instance:GetRoleSexProf()
    local tab = {}

    local skill_list = use_prof_skill_list[sex][prof]
    for i = 1, 4 do
        local skill_id = skill_list[i]
        table.insert(skill_list, skill_id)
    end

    --判断是否进阶
    if self.skill_grade
        and self.skill_series
        and self.skill_grade ~= 0
        and self.skill_series ~= 0
        and self.skill_upgrade_change_cfg[sex]
        and self.skill_upgrade_change_cfg[sex][prof]
    then
        --取进阶技能
        local cfg = self.skill_upgrade_change_cfg[sex][prof] [self.skill_series]
        if cfg ~= nil then
            for k, v in ipairs(cfg) do
                if self.skill_grade >= v.grade then
                    for i = 1, 2 do
                        local skill_id = v["add_skill_id_" .. i]
                        if skill_id and skill_id > 0 then
                            table.insert(skill_list, skill_id)
                        end
                    end
                end
            end
        end
    end

    for k, v in pairs(skill_list) do
        local skill_id = v
        local skill_info = self:GetSkillInfoById(skill_id)
        if skill_info ~= nil and skill_info.skill_id ~= 0 then
            local skill_break_cfg = self:GetSkillBreakCfgByLevel(skill_id, 1)
            if skill_break_cfg then
                for i = 1, 2 do
                    local cost_item = skill_break_cfg["consume_stuff_id" .. i]
                    if cost_item ~= nil and tab[cost_item] == nil then
                        tab[cost_item] = k
                    end
                end
            end
        end
    end

    return tab
end

function SkillWGData:GetIsVaildItem(value)
    local is_vaild = true
    if value == nil or value == "" or value == 0 then
        is_vaild = false
    end

    return is_vaild
end

function SkillWGData:SetDefaultBeiDongIndex(index)
    self.default_beidong_index = index
end

function SkillWGData:IsChongCiSkill(skill_id)
    if not skill_id then
        return false
    end

    local normal_skill_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id)
    if normal_skill_cfg == nil or normal_skill_cfg.is_chongci == nil then
        return false
    end
    return normal_skill_cfg.is_chongci == 1
end

function SkillWGData:GetRobertSkillList(sex, prof)
    prof = prof % 10
    local skill_list = tostring(use_prof_normal_skill_list[sex][prof][1])
    for i=1,4 do
        skill_list = skill_list .. "##" .. use_prof_skill_list[sex][prof][i]
    end
    return skill_list
end

--获取转职技能
function SkillWGData:GetZhuanZhiSkillCfg(prof_level)
    local passive_skill_auto = ConfigManager.Instance:GetAutoConfig("roleskill_auto").passive_skill
    for i, v in pairs(passive_skill_auto) do
        if v.skill_type == 1 and v.prof_level == prof_level then
            return v
        end
    end
end

function SkillWGData:GetBuffLayerCfg(skill_id)
    return self.buff_layer_cfg[skill_id]
end

function SkillWGData:GetBuffLayerCfgByType(buff_type)
    return self.buff_layer_type_cfg[buff_type]
end

function SkillWGData:GetBeforeSkillCfg(skill_id)
    return self.before_skill_cfg[skill_id]
end

function SkillWGData:GetBeforeSkillCfgByType(skill_type)
    return self.before_skill_type_cfg[skill_type]
end

function SkillWGData:GetAfterSkillCfgById(skill_id)
    return self.after_skill_type_cfg[skill_id]
end

function SkillWGData:GetSiXiangSkillById(skill_id, skill_level)
    return (self.sixiang_skill_cfg[skill_id] or {})[skill_level]
end

function SkillWGData:GetIsSiXiangSkill(skill_id)
    return self.sixiang_skill_cfg[skill_id] ~= nil
end

function SkillWGData:GetWuXingSkillById(skill_id, skill_level)
    return (self.wuxing_skill_cfg[skill_id] or {})[skill_level]
end

function SkillWGData:GetIsWuXingSkill(skill_id)
    return self.wuxing_skill_cfg[skill_id] ~= nil
end

function SkillWGData:GetHaloSkillById(skill_id, skill_level)
    return (self.halo_skill_cfg[skill_id] or {})[skill_level]
end

function SkillWGData:GetIsHaloSkill(skill_id)
    return self.halo_skill_cfg[skill_id] ~= nil
end

function SkillWGData:GetWuHunSkillById(skill_id, skill_level)
    return (self.wuhun_skill_cfg[skill_id] or {})[skill_level]
end

function SkillWGData:GetIsWuHunSkill(skill_id)
    return self.wuhun_skill_cfg[skill_id] ~= nil
end

function SkillWGData:GetIsLongZhuSkill(skill_id)
    return LongZhuWGData.Instance:GetIsLongZhuSkill(skill_id)
end

function SkillWGData:GetCustomizedById(skill_id, skill_level)
    return (self.customized_skill_cfg[skill_id] or {})[skill_level]
end

function SkillWGData:GetIsCustomizedSkill(skill_id)
    return self.customized_skill_cfg[skill_id] ~= nil
end

function SkillWGData:GetTianShenHeJiSkillById(skill_id, skill_level)
    return (self.tianshen_heji_skill_cfg[skill_id] or {})[skill_level]
end

function SkillWGData:GetIsTianShenHeJiSkill(skill_id)
    return self.tianshen_heji_skill_cfg[skill_id] ~= nil
end

function SkillWGData:GetEsotericaSkillById(skill_id, skill_level)
    return (self.esoterica_skill_cfg[skill_id] or {})[skill_level]
end

function SkillWGData:GetIsEsotericaSkill(skill_id)
    return self.esoterica_skill_cfg[skill_id] ~= nil
end

function SkillWGData:GetResidentSkillById(skill_id, skill_level)
    return (self.non_resident_skill_cfg[skill_id] or {})[skill_level]
end

function SkillWGData:GetIsResidentSkill(skill_id)
    return self.non_resident_skill_cfg[skill_id] ~= nil
end

function SkillWGData:GetBeastsSkillById(skill_id)
    return self.beasts_skill_cfg[skill_id]
end

function SkillWGData:GetIsBeastsSkill(skill_id)
    return self.beasts_skill_cfg[skill_id] ~= nil
end

function SkillWGData:GetBuffCfgById(id)
    return self.buff_cfg[id]
end

function SkillWGData:GetSceneEffectCfgById(id)
    return self.scene_effect_cfg[id]
end

function SkillWGData:GetSceneBuffEffectCfg(product_method, skill_id)
    local cfg = self.scene_buff_effect_cfg[product_method]
	if not cfg then
		cfg = self.scene_buff_effect_cfg[skill_id]
	end

    return cfg
end

function SkillWGData:GetSkillAttackType(skill_id)
    local client_cfg = self:GetSkillClientConfig(skill_id)
    if client_cfg and client_cfg.damage_icon then
        local skill_type_tab = Split(client_cfg.damage_icon,"|")
        return skill_type_tab
    end
    return {}
end

function SkillWGData:CheckIsLimitUseSkill(skill_id)
    local is_limit = false
    local str = nil
    local limit_type = nil
    if skill_id == nil then
        return is_limit, str, limit_type
    end

    local cfg = self.skill_use_limit_cfg[skill_id]
    if cfg == nil then
        return is_limit, str, limit_type
    end

    limit_type = cfg.limit_type
    if cfg.limit_type == USE_SKILL_LIMIT.BUFF_LAYER then
        is_limit = true
        str = string.format(Language.Common.SkillUseLimit, cfg.limit_param2)
        local buff_list = FightWGData.Instance:GetMainRoleEffectList()
        if buff_list ~= nil then
            for k = 1, #buff_list do
                if buff_list[k].buff_type == cfg.limit_param then
                    is_limit = buff_list[k].merge_layer < cfg.limit_param2
                    break
                end
            end
        end
    elseif cfg.limit_type == USE_SKILL_LIMIT.ACTIVE_USE_SHOW_CHANGE then
        -- 天神切换技能的技能，不给用,只能通过专门的界面使用
        is_limit = true
    end

    return is_limit, str, limit_type
end

function SkillWGData:GetSkillAttachInfoById(attach_id)
    if self.cache_skill_attach_info[attach_id] then
        return self.cache_skill_attach_info[attach_id]
    end

    local attach_info = self.skill_attach_info_cfg[attach_id]
    if not attach_info then
        return nil
    end

    self.cache_skill_attach_info[attach_id] = {}
    for info_index = 0, 4 do
        self.cache_skill_attach_info[attach_id][info_index] = {}
        local str = attach_info["attach_info_" .. info_index]
        if str and str ~= "" then
            local str_list = Split(str, "|")
            if tonumber(str_list[1]) ~= 0 then
                for k,v in ipairs(str_list) do
                    self.cache_skill_attach_info[attach_id][info_index][k] = tonumber(v)
                end
            end
        end
    end

    return self.cache_skill_attach_info[attach_id]
end

function SkillWGData:GetOtherSkillCfg(skill_id, skill_level)
	if not skill_id or not skill_level then
		return
	end
    
    local cfg_list = self.other_skill_cfg
    if cfg_list and cfg_list[skill_id] and cfg_list[skill_id][skill_level] then
        return cfg_list[skill_id][skill_level]
    end

    ----[[ 龙珠的等级超过配置的技能最大等级取最大配置
    local longzhu_skill_id = LongZhuWGData.Instance:GetOtherCfg("skill_id")
    if skill_id == longzhu_skill_id then
        local max_level = LongZhuWGData.Instance:GetMaxLongZhuSkillLevel()
        return cfg_list and cfg_list[skill_id] and cfg_list[skill_id][max_level]
    end
    --]]
end

function SkillWGData:GetSkillSpecialShowCfg(skill_id)
    return self.skill_special_show_cfg[skill_id]
end

function SkillWGData:GetFightMountSkillCfg(skill_id, skill_level)
    skill_level = skill_level or 1
    return (self.fight_mount_skill_cfg[skill_id] or {})[skill_level]
end

function SkillWGData:IsFightMountSkill(skill_id)
    return self.fight_mount_skill_cfg[skill_id] ~= nil
end

function SkillWGData:GetSkillScreenEffect(type)
    return self.skill_screen_effect_cfg[type]
end

---净化技能不受异常buff影响
function SkillWGData:IsPurifySkill(skill_id)
    for _, purify_skill_id in ipairs(purify_skill_list) do
        if skill_id == purify_skill_id then
            return true
        end
    end

    return false
end

-- 机甲技能
function SkillWGData:GetJiJiaSkillConfig(skill_id, skill_level)
    skill_level = skill_level or 1
    return (self.jijia_skill_cfg[skill_id] or {})[skill_level]
end

function SkillWGData:IsJiJiaSkill(skill_id)
    return self.jijia_skill_cfg[skill_id] ~= nil
end

-- 修仙境界技能
function SkillWGData:GetXiuXianSkillConfig(skill_id, skill_level)
    skill_level = skill_level or 1
    return (self.xiuxian_skill_cfg[skill_id] or {})[skill_level]
end

function SkillWGData:IsXiuXianSkill(skill_id)
    return self.xiuxian_skill_cfg[skill_id] ~= nil
end

-- 获取当前职业觉醒技能信息
function SkillWGData:GetSkillBreakCfgList()
    local sex, prof = RoleWGData.Instance:GetRoleSexProf()
    local role_skill_auto = ConfigManager.Instance:GetAutoConfig("roleskill_auto")
    
    if not self.break_skill_cfg then
        self.break_skill_cfg = {}
        local prof_skill_list = use_prof_skill_list[sex][prof]
        if prof_skill_list then
            for i = 1, 4 do
                local skill_id = prof_skill_list[i]
                local skill_cfg = role_skill_auto[string.format("s%d", skill_id)]
                local skill_break_cfg = DeepCopy(self:GetSkillBreakCfgByLevel(skill_id, 1))
                skill_break_cfg.skill_name = skill_break_cfg.skill_name .. "·" .. skill_cfg[1].skill_name
                table.insert(self.break_skill_cfg, skill_break_cfg)
            end
        else
            print_error("----哪一个没有----", sex, prof)
        end
    end
    return self.break_skill_cfg
end

-- 重置缓存
function SkillWGData:ResetSkillBreakCfgList()
    self.break_skill_cfg = nil
end

-- 获取技能拓展配置基础表
function SkillWGData:GetSkillExtBaseCfg()
    return self.skill_ext_base_cfg
end

-- 获取技能喊招配置
function SkillWGData:GetSkillExtShoutMoveCfgBySkillId(sex, skill_id)
    local empty = {}
    return ((self.skill_shout_move_list or empty)[sex] or empty)[skill_id]
end

-- 获取当前技能的随机喊招音效
function SkillWGData:GetSkillShoutMoveDataBySkillId(skill_id)
    local self_sex = RoleWGData.Instance:GetRoleSex()
    local bundle, asset
    local data = self:GetSkillExtShoutMoveCfgBySkillId(self_sex, skill_id)

    if data and data.asset_list then
        local random_num = math.random(1, #data.asset_list)
        bundle = data.bundle_folder
        asset = data.asset_list[random_num]
    end

    return bundle, asset
end