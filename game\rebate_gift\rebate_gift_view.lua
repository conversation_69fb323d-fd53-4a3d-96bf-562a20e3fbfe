RebateExtinctGiftView = RebateExtinctGiftView or BaseClass(SafeBaseView)
function RebateExtinctGiftView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
    self:AddViewResource(0, "uis/view/rebate_gift_ui_prefab", "rebate_gift_view")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
    self.view_style = ViewStyle.Half
    self.is_safe_area_adapter = true
    self.cur_select_sub_index = -1
    self.view_layer = UiLayer.Normal
end

function RebateExtinctGiftView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.PremiumGift.Title
    self:InitMoneyBar()
    self.list_cell_count = -1
    self.index = -1
    self.big_act_list = {}
    XUI.AddClickEventListener(self.node_list["btn_main_lq"], BindTool.Bind1(self.OnClickMainGet, self))
    XUI.AddClickEventListener(self.node_list["btn_sub_lq"], BindTool.Bind1(self.OnClickSubGet, self))      --子任务奖励领取
    XUI.AddClickEventListener(self.node_list["skill_item"], BindTool.Bind1(self.OnClickSkillTips, self))   --技能点击
    --XUI.AddClickEventListener(self.node_list["btn_rule_tips"], BindTool.Bind(self.OnClickTipsBtn, self))
    --XUI.AddClickEventListener(self.node_list["fz_skill_root"], BindTool.Bind(self.OnFZBtnSkillIcon, self)) --技能预览

    self.small_act_list = {}
    for i = 1, 10 do
        self.small_act_list[i] = RebateGiftSubTaskItem.New(self.node_list.small_content:FindObj("rebate_gift_small_item" .. i))
    end

    for i=1, 4 do
        self.big_act_list[i] = RebateGiftMainTaskItem.New(self.node_list["rebate_gift_big_item" .. i])
    end

    if self.mount_display == nil then
        self.mount_display = OperationActRender.New(self.node_list["mount_display"])
        self.mount_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
    end

    if self.sub_display == nil then
        self.sub_display = OperationActRender.New(self.node_list["sub_display_root"])
    end

    if self.rebate_gift_toggle_list == nil then
        self.rebate_gift_toggle_list = RebateGiftToggleList.New(RebateGiftToggleRender,
            self.node_list.rebate_gift_toggle_list)
        self.rebate_gift_toggle_list:SetStartZeroIndex(true)
        self.rebate_gift_toggle_list:SetSelectCallBack(BindTool.Bind(self.OnClickToggle, self))
    end

    if not self.model_display then
        self.model_display = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["display_root"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.S,
            can_drag = false,
        }
        
        self.model_display:SetRenderTexUI3DModel(display_data)
        -- self.model_display:SetUI3DModel(self.node_list["display_root"].transform,
        --     self.node_list.display_root.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.model_display)
    end

    if not self.fz_display then
        self.fz_display = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["fzdisplay"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = false,
        }
        self.fz_display:SetRenderTexUI3DModel(display_data)
        -- self.fz_display:SetUI3DModel(self.node_list["fzdisplay"].transform,
        --     self.node_list.fzdisplay.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.fz_display)
    end

    --self.gift_reward_list = AsyncListView.New(ItemCell, self.node_list["gift_reward_list"])
    self:LoginTimeCountDown()
    self:InitTitleAttr()
end

function RebateExtinctGiftView:InitMoneyBar()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
            show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end

function RebateExtinctGiftView:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("rebate_gift_down") then
        CountDownManager.Instance:RemoveCountDown("rebate_gift_down")
    end

    if self.rebate_gift_toggle_list then
        self.rebate_gift_toggle_list:DeleteMe()
        self.rebate_gift_toggle_list = nil
    end

    if self.big_act_list then
		for k, v in pairs(self.big_act_list) do
			v:DeleteMe()
		end
	 end
	self.big_act_list = {}

    if self.small_act_list then
		for k, v in pairs(self.small_act_list) do
            v:DeleteMe()
		end

		self.small_act_list = nil
	end

    if self.mount_display then
        self.mount_display:DeleteMe()
        self.mount_display = nil
    end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end

    if self.sub_display then
        self.sub_display:DeleteMe()
        self.sub_display = nil
    end

    if self.fz_display then
        self.fz_display:DeleteMe()
        self.fz_display = nil
    end

    -- if self.gift_reward_list then
    --     self.gift_reward_list:DeleteMe()
    --     self.gift_reward_list = nil
    -- end

    if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

    if CountDownManager.Instance:HasCountDown("rebate_today_time") then
        CountDownManager.Instance:RemoveCountDown("rebate_today_time")
    end

    self.cur_select_sub_index = -1

    self.attr_title_list = {}
    self.attr_name_list = {}
    self.attr_value_list = {}
end

function RebateExtinctGiftView:OpenCallBack()
    RebateGiftActivityWGCtrl.Instance:SendRechargeInfo(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_EXTINCT_GIFT,
        EXTINCT_GIFT_OPERATE_TYPE.INFO)
end

function RebateExtinctGiftView:CloseCallBack()
    self.index = -1
end

function RebateExtinctGiftView:OnFlush(param_t)
    for k, v in pairs(param_t) do
        if k == "all" then
            self:FlushShowView()
            self:FlushActList(self.cur_select_sub_index)
            self:FlushModelShow()
        end
    end

    self:TodayTimeCountDown()
end

function RebateExtinctGiftView:OnClickTipsBtn()
    local role_tip = RuleTip.Instance
    role_tip:SetTitle(Language.RebateGiftAct.GiftRuleTitle)
    role_tip:SetContent(Language.RebateGiftAct.GiftRuleDesc)
end

function RebateExtinctGiftView:OnFZBtnSkillIcon()
    local open_day_cfg = RebateGiftActivityWGData.Instance:GetopenDayGradeCfg()
    local data = SupremeFieldsWGData.Instance:SkillShowCfgList(open_day_cfg.footlight_type, 1) -- 默认获取第一级技能
    CommonSkillShowCtrl.Instance:SetViewDataAndOpen(data)
end

function RebateExtinctGiftView:InitTitleAttr()
    self.attr_title_list = {}
    self.attr_name_list = {}
    self.attr_value_list = {}

    local attr_num = self.node_list.attr_list.transform.childCount
    for i = 1, attr_num do
        self.attr_title_list[i] = self.node_list.attr_list:FindObj("attr_" .. i)
        self.attr_name_list[i] = self.attr_title_list[i]:FindObj("attr_name")
        self.attr_value_list[i] = self.attr_title_list[i]:FindObj("attr_value")
    end
end

function RebateExtinctGiftView:FlushAttr(index)
    local grade = RebateGiftActivityWGData.Instance:GetExtinctGrade()
    --只有第一个配置才显示称号属性.
    self.node_list.attr:SetActive(grade == 1)
    if grade ~= 1 then
        return
    end

    local cur_day_data = RebateGiftActivityWGData.Instance:GetSubTaskActTabDay(index)
    if cur_day_data > 0 then
        local sub_model_item = RebateGiftActivityWGData.Instance:GetSubTaskRewardCfg(cur_day_data)
        if sub_model_item ~= nil and sub_model_item[1] and sub_model_item[1].subtask_item_list[0] then
            if sub_model_item[1].subtask_item_list[0].item_id > 0 then
                local item_id = sub_model_item[1].subtask_item_list[0].item_id

                local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
                if not item_cfg then
                    return
                end

                local info = TitleWGData.Instance:GetConfig(item_cfg.param1)
                if not info then
                    return
                end
                local cur_attr_list = TitleWGData.Instance:GetTitleAttrInfo(info)

                -- 属性显示
                local index = 1
                local is_per = true
                local sort_list = AttributeMgr.SortAttribute()

                for k, v in ipairs(sort_list) do
                    local is_show = RebateGiftActivityWGData.Instance:CheckIsSelectTitleAttr(v)

                    if is_show and cur_attr_list[v] ~= 0 then
                        is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v)
                        self.attr_name_list[index].text.text = EquipmentWGData.Instance:GetAttrNameByAttrStr(v, true,
                            false)
                        self.attr_value_list[index].text.text = is_per and cur_attr_list[v] / 100 .. "%" or
                            cur_attr_list[v]

                        self.attr_title_list[index]:SetActive(true)
                        index = index + 1
                    end
                end

                local attr_num = self.node_list.attr_list.transform.childCount
                for i = index, attr_num do
                    self.attr_title_list[i]:SetActive(false)
                end
            end
        end
    end
end

function RebateExtinctGiftView:FlushModelShow()
    local model_item_id, img_type, footlight_type = RebateGiftActivityWGData.Instance:GetBigRewardCfg()
    if model_item_id > 0 then
        local cfg = RebateGiftActivityWGData.Instance:GetopenDayGradeCfg()
        local data = {}
        local rotate_str = cfg.mount_rot
        if rotate_str and rotate_str ~= "" then
            local rot = Split(rotate_str, "|")
            --local quaternion_euler = Quaternion.Euler(rot[1], rot[2], rot[3])
            data.model_adjust_root_local_rotation = Vector3(rot[1], rot[2], rot[3])
        end

        local pos_str = cfg.mount_pos
        if pos_str and pos_str ~= "" then
            local pos = Split(pos_str, "|")
            data.model_adjust_root_local_position = Vector3(pos[1] or 0, pos[2] or 0, pos[3] or 0)
        end
        local scale = cfg.mount_scale or 1
        data.model_adjust_root_local_scale = scale
        data.item_id = model_item_id
        data.render_type = 0
        data.model_click_func = function()
            TipWGCtrl.Instance:OpenItem({ item_id = model_item_id })
        end

        data.model_rt_type = ModelRTSCaleType.L
        self.mount_display:SetData(data)
        local mode_data = { item_id = data.item_id }
        local skill_data = RebateGiftActivityWGData.Instance:GetBigRewardSkillCfg(mode_data)

        local bundle, asset
        if skill_data.skill_icon then
            bundle, asset = ResPath.GetSkillIconById(skill_data.skill_icon)
        end

        self.node_list["img_skill_icon"].image:LoadSpriteAsync(bundle, asset, function()
            self.node_list["img_skill_icon"].image:SetNativeSize()
        end)

        self.node_list.skill_desc.text.text = cfg.skill_desc
    end

    if footlight_type > 0 then
        self.model_display:RemoveAllModel()
        local role_vo = GameVoManager.Instance:GetMainRoleVo()
        self.model_display:SetModelResInfo(role_vo, nil, function()
            self.model_display:PlayRoleShowAction()
        end)
        self.model_display:FixToOrthographic(self.root_node_transform)
    end

    local capability = 0
    local open_day_cfg = RebateGiftActivityWGData.Instance:GetopenDayGradeCfg()
    if open_day_cfg then
        for k, v in pairs(open_day_cfg.maintask_item_list) do
            local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
            if item_cfg then
                capability = capability + ItemShowWGData.CalculateCapability(v.item_id)
            end
        end
    end

    self.node_list["model_cap_value"].text.text = capability
end

function RebateExtinctGiftView:FlushShowView()
    local toggle_data_list = RebateGiftActivityWGData.Instance:GetToggleShowList()
    if self.list_cell_count ~= #toggle_data_list then
        self.list_cell_count = #toggle_data_list
        self.rebate_gift_toggle_list:SetDataList(toggle_data_list)
    end

    self.rebate_gift_toggle_list:FlushRemind()
    local jump_index = RebateGiftActivityWGData.Instance:GetJumpRedTab()
    self:JumpToShowView(jump_index)

    local extinct_gift_list = RebateGiftActivityWGData.Instance:GetExtinctGiftInfo()
    if not IsEmptyTable(extinct_gift_list) then
        local main_task_red = RebateGiftActivityWGData.Instance:GetMainTaskRed()
        --self.node_list["btn_main_yl"]:SetActive(extinct_gift_list.main_long_get > 0) --大龙已领取
        self.node_list["btn_main_lq"]:SetActive(main_task_red > 0)                   --大龙可领取
        --self.node_list["desc"]:SetActive(main_task_red <= 0 and extinct_gift_list.main_long_get <= 0)
    end

    local big_data_list = RebateGiftActivityWGData.Instance:GetMainTaskList()

    if big_data_list then
        for i = 1, 4 do
            self.big_act_list[i]:SetData(big_data_list[i])
        end
    end

    local gift_other_list = RebateGiftActivityWGData.Instance:GetGiftOtherCfg()
    if gift_other_list then
        self.node_list.xingchen_text.text.text = gift_other_list.xingchen_text
        self.node_list.today_text.text.text = gift_other_list.jinri_text
    end

    self:FlushFZView()
end

function RebateExtinctGiftView:JumpToShowView(jump_index)
    self.rebate_gift_toggle_list:JumpToIndex(jump_index)
end

function RebateExtinctGiftView:FlushFZView()
    local open_day_cfg = RebateGiftActivityWGData.Instance:GetopenDayGradeCfg()
    self.node_list.fzdisplay:SetActive(open_day_cfg and open_day_cfg.footlight_type >= 0)
    --self.node_list.gift_reward_list:SetActive(open_day_cfg and open_day_cfg.footlight_type >= 0)
    --self.node_list.fz_skill_root:SetActive(open_day_cfg and open_day_cfg.footlight_type >= 0)

    --self.node_list.desc.text.text = open_day_cfg and open_day_cfg.type_desc or ""
    if open_day_cfg and open_day_cfg.footlight_type >= 0 then
        self.fz_display:RemoveAllModel()
        local bundle, asset = ResPath.GetSkillFaZhenModel(open_day_cfg.footlight_type)
        self.fz_display:SetMainAsset(bundle, asset)

        local display_reward = {}
        for i = 0, #open_day_cfg.maintask_item_list do
            table.insert(display_reward, open_day_cfg.maintask_item_list[i])
        end
        --self.gift_reward_list:SetDataList(display_reward)

        local fz_skill_id = SupremeFieldsWGData.Instance:GetSkillIDList(open_day_cfg.footlight_type, 1)[1] or 0 -- 获取一级技能
        local fz_skill_cfg = SupremeFieldsWGData.Instance:GetFootLightSkillCfg(fz_skill_id)
        --local icon_bundle, icon_asset = ResPath.GetSkillIconById(fz_skill_cfg.icon)
        -- self.node_list.fz_skill_icon.image:LoadSpriteAsync(icon_bundle, icon_asset, function()
        --     self.node_list.fz_skill_icon.image:SetNativeSize()
        -- end)

        --local cfg = SupremeFieldsWGData.Instance:GetFootLightCfg(open_day_cfg.footlight_type)
        --self.node_list.fz_skill_text.text.text = cfg.skill_txt
    end
end

function RebateExtinctGiftView:FlushActList(index)
    local cur_day_data = RebateGiftActivityWGData.Instance:GetSubTaskActTabDay(index)
    if cur_day_data > 0 then

        local bundle, asset = ResPath.GetRawImagesPNG("a3_hsjd_lhfh_bt" .. cur_day_data)
        if self.node_list.sub_title_img then
            self.node_list["sub_title_img"].raw_image:LoadSprite(bundle, asset, function()
                self.node_list["sub_title_img"].raw_image:SetNativeSize()
            end)
        end

        local small_data_list = RebateGiftActivityWGData.Instance:GetSubTaskList(cur_day_data)
        if small_data_list then
            for i = 1, 10 do
                if i <= #small_data_list then
                    self.node_list.small_content:FindObj("rebate_gift_small_item" .. i):SetActive(true)
                    self.small_act_list[i]:SetData(small_data_list[i])
                    self.small_act_list[i]:SetIndex(i)
                    self.small_act_list[i]:SetChainActive(i < #small_data_list)
                else
                    self.node_list.small_content:FindObj("rebate_gift_small_item" .. i):SetActive(false)
                end
            end
            --self.small_act_list:SetDataList(small_data_list)
        end

        local sub_model_item = RebateGiftActivityWGData.Instance:GetSubTaskRewardCfg(cur_day_data)
        if sub_model_item ~= nil and sub_model_item[1] and sub_model_item[1].subtask_item_list[0] then
            if sub_model_item[1].subtask_item_list[0].item_id > 0 then
                local data = {}
                data.item_id = sub_model_item[1].subtask_item_list[0].item_id
                data.render_type = sub_model_item[1].render_type
                local position = Split(sub_model_item[1].mount_pos, "|")
                data.position = Vector3.New(position[1], position[2], position[3])
                local rotation = Split(sub_model_item[1].mount_rot, "|")
                data.rotation = Quaternion.Euler(rotation[1], rotation[2], rotation[3])
                local scale_t = Split(sub_model_item[1].mount_scale, "|")
                data.scale = Vector3.New(scale_t[1], scale_t[2], scale_t[3])
                data.model_click_func = function()
                    TipWGCtrl.Instance:OpenItem({ item_id = data.item_id })
                end

                self.sub_display:SetData(data)

                local capability = ItemShowWGData.CalculateCapability(data.item_id, true)
                self.node_list["sub_cap_value"].text.text = capability
            end
        end

        local sub_task_red = RebateGiftActivityWGData.Instance:GetSubTaskRed(cur_day_data)
        local sub_get_state = RebateGiftActivityWGData.Instance:GetSubTaskRewardState(cur_day_data)

        self.node_list["btn_sub_lq"]:SetActive(sub_task_red > 0)  --子活动可领
        self.node_list["btn_sub_yl"]:SetActive(sub_get_state > 0) --子活动已领取
    end
end

function RebateExtinctGiftView:ChangeUIStyle()
    local style_cfg = RebateGiftActivityWGData.Instance:GetopenDayGradeCfg()
    if not style_cfg then
        return
    end
end

function RebateExtinctGiftView:DoCellsAnim()
    self.node_list["small_act_list"]:SetActive(false)
    local cur_day_data = RebateGiftActivityWGData.Instance:GetSubTaskActTabDay(self.cur_select_sub_index)
    local small_data_list = RebateGiftActivityWGData.Instance:GetSubTaskList(cur_day_data)
    local tween_info = UITween_CONSTS.RebateExtinctGiftView
    ReDelayCall(self, function()
        UITween.CleanAlphaShow(GuideModuleName.RebateExtinctGiftView)
        self.node_list["small_act_list"]:SetActive(true)
        --local list =  self.small_act_list:GetAllItems()
        local count = 0
        -- for k,v in ipairs(list) do
        --     if 0 ~= v.index then
        --         count = count + 1
        --     end
        --     v:PlayItemAnim(count)
        -- end
        for i = 1, 10 do
            if 0 ~= self.small_act_list[i].index then
                count = count + 1
            end
            self.small_act_list[i]:PlayItemAnim(count)
        end
    end, tween_info.DelayDoTime, "rg_small_act_list")
end

function RebateExtinctGiftView:PlayBigItemAnim(tween_info)
    for i = 1, 4 do
        self.node_list["rebate_gift_big_item" .. i]:SetActive(false)
    end

    ReDelayCall(self, function()
        UITween.CleanScaleAlaphaShow(GuideModuleName.RebateExtinctGiftView)
        for i = 1, 4 do
            self.node_list["rebate_gift_big_item" .. i]:SetActive(true)
            UITween.DoScaleAlaphaShow(GuideModuleName.RebateExtinctGiftView, self.node_list["rebate_gift_big_item" .. i], tween_info)
        end
    end, 0.2, "rebate_gift_big_item")
end

function RebateExtinctGiftView:PlayModelAnim(tween_info)
    self.node_list["sub_display"]:SetActive(false)

    ReDelayCall(self, function()
        UITween.CleanScaleAlaphaShow(GuideModuleName.RebateExtinctGiftView)
        self.node_list["sub_display"]:SetActive(true)
        UITween.DoScaleAlaphaShow(GuideModuleName.RebateExtinctGiftView, self.node_list["sub_display"], tween_info)
    end, 0.2, "sub_display")
end

function RebateExtinctGiftView:OnClickSwitch(index)
    if self.index == index then
        return
    end

    self.index = index

    local tween_info = UITween_CONSTS.RebateExtinctGiftView.ListBigRender
    local bundle, asset = ResPath.GetRawImagesPNG("a3_hsjd_hwxj_bg")
    if index == 0 then
        bundle, asset = ResPath.GetRawImagesPNG("a3_hsjd_lhfh_bj2")
        self:PlayBigItemAnim(tween_info)
    end

    if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

    self.node_list["main_task_show"]:SetActive(index == 0)
    self.node_list["sub_task_show"]:SetActive(index ~= 0)
    self.node_list["effect_bgeff"]:SetActive(index ~= 0)
    self.node_list.main_display:SetActive(index == 0)
    self.node_list.sub_display:SetActive(index ~= 0)
    self.node_list.activity_time:SetActive(index == 0)
    self.node_list.totay_time:SetActive(index ~= 0)
    self.cur_select_sub_index = index
    self:FlushActList(index)
    if index > 0 then
        self:DoCellsAnim()
        self:PlayModelAnim(tween_info)
    end
    self:FlushAttr(index)
end

--主活动奖励领取
function RebateExtinctGiftView:OnClickMainGet()
    local extinct_gift_list = RebateGiftActivityWGData.Instance:GetExtinctGiftInfo()
    if extinct_gift_list == nil then
        return
    end

    if extinct_gift_list.main_long_get < 1 then --未领取
        RebateGiftActivityWGCtrl.Instance:SendRechargeInfo(
            ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_EXTINCT_GIFT,
            EXTINCT_GIFT_OPERATE_TYPE.GET_MAIN_TASK_REWARD)
    end
end

--子活动奖励领取
function RebateExtinctGiftView:OnClickSubGet()
    local extinct_gift_list = RebateGiftActivityWGData.Instance:GetExtinctGiftInfo()
    if extinct_gift_list == nil then
        return
    end

    if self.cur_select_sub_index == -1 then
        print_error("index为-1!")
        return
    end

    local cur_day_data = RebateGiftActivityWGData.Instance:GetSubTaskActTabDay(self.cur_select_sub_index)
    RebateGiftActivityWGCtrl.Instance:SendRechargeInfo(
        ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_EXTINCT_GIFT,
        EXTINCT_GIFT_OPERATE_TYPE.GET_SUB_TASK_REWARD,
        cur_day_data)
end

--技能点击
function RebateExtinctGiftView:OnClickSkillTips()
    local skill_type, skill_param = RebateGiftActivityWGData.Instance:GetSkillShowParam()
    if skill_type == -1 then
        return
    end

    if skill_type == RebateGiftActivityWGData.SKILL_SHOW_TYPE.MOUNT then
        self:ShowMountSkillTip()
    elseif skill_type == RebateGiftActivityWGData.SKILL_SHOW_TYPE.DIYSKILL then
        self:ShowDIYSkillTip(skill_param)
    end
end

function RebateExtinctGiftView:OnClickToggle(cell)
    if not cell then
        return
    end

    self:OnClickSwitch(cell:GetIndex())
end

function RebateExtinctGiftView:ShowMountSkillTip()
    local model_item_id = RebateGiftActivityWGData.Instance:GetBigRewardCfg()
    local data = RebateGiftActivityWGData.Instance:GetBigRewardSkillCfg({ item_id = model_item_id })
    if IsEmptyTable(data) then
        return
    end

    local skill_describe = data.skill_describe or data.skill_des
    local limit_text = ""
    if not data.is_open_skill then
        limit_text = string.format(Language.NewAppearance.SkillGradeActTips, data.active_grade)
    end


    local capability = NewAppearanceWGData.Instance:GetSingleSkillCap(data)

    local show_data = {
        icon = data.skill_icon,
        top_text = data.skill_name,
        body_text = skill_describe,
        limit_text = limit_text,
        x = 0,
        y = 0,
        set_pos2 = true,
        capability = capability,
    }

    NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

function RebateExtinctGiftView:ShowDIYSkillTip(param)
    if not param then
        return
    end

    CustomizedSuitWGCtrl.Instance:OpenCustomizedSkillPanel(param)
end

------------------------------------活动时间倒计时
function RebateExtinctGiftView:LoginTimeCountDown()
    local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE
    .RAND_ACTIVITY_TYPE_OA_EXTINCT_GIFT)
    if activity_data ~= nil then
        local invalid_time = activity_data.end_time
        if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
            self.node_list["time_down"].text.text = TimeUtil.FormatSecondDHM8(invalid_time -
            TimeWGCtrl.Instance:GetServerTime())
            CountDownManager.Instance:AddCountDown("rebate_gift_down", BindTool.Bind1(self.UpdateCountDown, self),
                BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
        end
    end
end

function RebateExtinctGiftView:UpdateCountDown(elapse_time, total_time)
    local valid_time = total_time - elapse_time
    if valid_time > 0 then
        self.login_act_date = TimeUtil.FormatUnixTime2Date()
        self.node_list["time_down"].text.text = TimeUtil.FormatSecondDHM8(valid_time)
    end
end

function RebateExtinctGiftView:OnComplete()
    self.node_list["time_down"].text.text = ""
    self:Close()
end

function RebateExtinctGiftView:TodayTimeCountDown()
    local time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
    if time > 0 then
        if CountDownManager.Instance:HasCountDown("rebate_today_time") then
            CountDownManager.Instance:RemoveCountDown("rebate_today_time")
        end

        CountDownManager.Instance:AddCountDown("rebate_today_time",
            BindTool.Bind(self.UpdateTodayCountDown, self),
            BindTool.Bind(self.OnTodayComplete, self),
            nil, time, 1)
    else
        self:OnTodayComplete()
    end
end

function RebateExtinctGiftView:UpdateTodayCountDown(elapse_time, total_time)
    local valid_time = total_time - elapse_time
    if valid_time > 0 then
        self.login_act_date = TimeUtil.FormatUnixTime2Date()
        self.node_list["today_time_down"].text.text = TimeUtil.FormatSecondDHM8(valid_time)
    end
end

function RebateExtinctGiftView:OnTodayComplete()
    self.node_list["today_time_down"].text.text = ""
end

---------------------------------------主活动---------------------------------

local Btn_Pos = {
    [1] = { x = -1, y = -44 },
    [2] = { x = -1, y = -70 },
}

local Btn_Text_Color = {
    [1] = "#4A361B",
    [2] = COLOR3B.WHITE
}

RebateGiftMainTaskItem = RebateGiftMainTaskItem or BaseClass(BaseRender)
function RebateGiftMainTaskItem:__init()
    self.main_item_reward = ItemCell.New(self.node_list["item_pos"])
end

function RebateGiftMainTaskItem:__delete()
    if self.main_item_reward then
        self.main_item_reward:DeleteMe()
        self.main_item_reward = nil
    end
end

function RebateGiftMainTaskItem:LoadCallBack()
    self.first_flush_img_flag = false
    XUI.AddClickEventListener(self.node_list["btn_get"], BindTool.Bind1(self.OnClickJump, self))
    XUI.AddClickEventListener(self.node_list["is_get"], BindTool.Bind1(self.OnClickJump, self))
    XUI.AddClickEventListener(self.node_list["title_cell_root"], BindTool.Bind1(self.OnClickTips, self))
end

function RebateGiftMainTaskItem:OnFlush()
    if self.data == nil then
        return
    end

    if not self.first_flush_img_flag then
        self.first_flush_img_flag = true

        local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_EXTINCT_GIFT)
        --local cur_day = CommonDataManager.GetAncientNumber(self.data.seq + 1)
        local name_str = (self.data.seq + 1) == act_day and Language.RebateGiftAct.GiftToDayTitle or
        Language.RebateGiftAct.DayTitleList[self.data.seq + 1]
        self.node_list["btn_name"].text.text = name_str
        self.node_list["is_get_text"].text.text = name_str
    end

    local data_item_id = 0
    self.node_list.item_cell:SetActive(self.data.type ~= 6)
    self.node_list.title_cell_root:SetActive(self.data.type == 6)
    if self.data.type == 6 then --称号
        self:SetTitleCell()
    else                        --时装以及其他
        data_item_id = WardrobeWGData.Instance:GetActItemId(self.data)
        self.main_item_reward:SetData({ item_id = data_item_id })
    end

    local is_get = self.data.is_get > 0
    -- self.node_list["btn_get"]:SetActive(not is_get)
    self.node_list["is_get"]:SetActive(is_get)

    self.node_list["lock_content"]:SetActive(not is_get)
    --self.node_list["lock_bg"]:SetActive(not is_get)
    self.node_list["active_bg"]:SetActive(is_get)
end

function RebateGiftMainTaskItem:SetTitleCell()
    local title_cfg = TitleWGData.GetTitleConfig(self.data.param1)
    local title_id = title_cfg.title_id
    local asset, bundle = ResPath.GetTitleModel(title_id)

    local scale_t = Split(self.data.mount_scale, "|")
    local pos_t = Split(self.data.mount_pos, "|")
    Transform.SetLocalScaleXYZ(self.node_list["title_display"].transform, scale_t[1], scale_t[2], scale_t[3])
    Transform.SetLocalPositionXYZ(self.node_list["title_display"].transform, pos_t[1], pos_t[2], pos_t[3])

    self.node_list["title_display"]:ChangeAsset(asset, bundle, false, function(obj)
        if IsNil(obj) then
            return
        end

        local diy_cfg = TitleWGData.Instance:GetDiyTitleCfg(title_id)
        if not diy_cfg then
            return
        end

        local text_obj = obj.gameObject.transform:Find("Text")
        local title_cfg = TitleWGData.Instance:GetConfig(title_id)
        if text_obj == nil or not title_cfg then
            return
        end

        local title_text = text_obj.gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))
        title_text.text = title_cfg.name
    end)

    -- if effect_asset and effect_bundle then
    --     self.node_list["title_effect"]:ChangeAsset(effect_asset, effect_bundle)
    --     self.node_list["title_effect"]:SetActive(true)
    -- else
    --     self.node_list["title_effect"]:SetActive(false)
    -- end
end

function RebateGiftMainTaskItem:OnClickTips()
    local title_cfg = TitleWGData.GetTitleConfig(self.data.param1)
    local item_id = title_cfg.item_id
    TipWGCtrl.Instance:OpenItem({ item_id = item_id })
end

function RebateGiftMainTaskItem:OnClickJump()
    if self.data == nil then
        return
    end

    local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_EXTINCT_GIFT)
    if self.data.seq + 1 > act_day then
        TipWGCtrl.Instance:ShowSystemMsg(Language.RebateGiftAct.GiftTabError)
        return
    end

    -- --跳到子活动标签
    local jump_index = RebateGiftActivityWGData.Instance:GetSubTaskDayByIndex(self.data.seq + 1)
    RebateGiftActivityWGCtrl.Instance:OnClickExtinctGiftTab(jump_index)
end

------------------------------------------子活动--------------------------------------------------
RebateGiftSubTaskItem = RebateGiftSubTaskItem or BaseClass(BaseRender)
function RebateGiftSubTaskItem:__init()
    --self.sub_item_reward = ItemCell.New(self.node_list["sub_item_pos"])
end

function RebateGiftSubTaskItem:__delete()
    -- if self.sub_item_reward then
    --     self.sub_item_reward:DeleteMe()
    --     self.sub_item_reward = nil
    -- end
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function RebateGiftSubTaskItem:LoadCallBack()
    self.first_flush_img_flag = false
    XUI.AddClickEventListener(self.node_list["btn_jump_bg"], BindTool.Bind1(self.OnClickSubJump, self))

    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["item_cell"])
    end
end

function RebateGiftSubTaskItem:OnFlush()
    if self.data == nil then
        return
    end

    local count = 1
    -- if self.index % 2 == 0 then
    --     count = self.index / 2
    --     self.node_list.root.rect.anchoredPosition = ROOT_POSITION2[count]
    -- else
    --     count = math.ceil(self.index / 2)
    --     self.node_list.root.rect.anchoredPosition = ROOT_POSITION[count]
    -- end

    if not self.first_flush_img_flag then
        self.first_flush_img_flag = true
    end

    local data_item_id = WardrobeWGData.Instance:GetActItemId(self.data)
    local item_cfg = ItemWGData.Instance:GetItemConfig(data_item_id)
    local item_name = ""
    if item_cfg then
        item_name = item_cfg.name
    end

    -- local bundle, asset = ResPath.GetItem(data_item_id)
    -- self.node_list.item_cell.image:LoadSpriteAsync(bundle, asset, function ()
    --     self.node_list.item_cell.image:SetNativeSize()
    -- end)

    self.item_cell:SetIsUseRoundQualityBg(true)
    self.item_cell:SetData({item_id = data_item_id})
    self.item_cell:Nodes("item_icon").transform.localScale = Vector3(0.7, 0.7, 0.7)
    self.item_cell:SetEffectRootEnable(false)
    self.item_cell:SetCellBgEnabled(false)
    --self.item_cell:SetBindIconVisible(false)

    --self.sub_item_reward:SetData({ item_id = data_item_id })
    self.node_list["sub_lock_item_name"].text.text = item_name
    self.node_list["sub_active_item_name"].text.text = item_name
    self.node_list["sub_btn_name"].text.text = self.data.show_open_decs

    --self.node_list["btn_jump_bg"]:SetActive(not self.data.is_get) --未激活
    --self.node_list["sub_is_get"]:SetActive(self.data.is_get)       --已激活

    self.node_list["lock_content"]:SetActive(not self.data.is_get) --未激活
    self.node_list["active_content"]:SetActive(self.data.is_get)       --已激活
    -- self.node_list["sub_lock_bg"]:SetActive(not self.data.is_get) --未激活
    -- self.node_list["sub_active_bg"]:SetActive(self.data.is_get)       --已激活
end

function RebateGiftSubTaskItem:SetChainActive(bool)
    self.node_list["chain_img"]:SetActive(bool)
end

function RebateGiftSubTaskItem:PlayItemAnim(item_index)
    if not self.node_list["root"] then return end

    local tween_info = UITween_CONSTS.RebateExtinctGiftView.ListCellRender
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index

    self.node_list.root.canvas_group.alpha = 0
    ReDelayCall(self, function()
        UITween.AlphaShow(GuideModuleName.RebateExtinctGiftView, self.node_list["root"], tween_info.FromAlpha, tween_info.ToAlpha, tween_info.AlphaTweenTime)
    end, tween_info.NextDoDelay * wait_index, "rebate_gift_small_item" .. wait_index)
end

--跳转活动
function RebateGiftSubTaskItem:OnClickSubJump()
    if self.data == nil then
        return
    end

    if self.data.open_panel ~= "" then
        local is_act_open = ActivityWGData.Instance:GetActivityIsOpen(self.data.act_type) --活动是否开启
        if self.data.act_type == "" then                                                  --非活动
            FunOpen.Instance:OpenViewNameByCfg(self.data.open_panel)
        else
            if is_act_open then
                FunOpen.Instance:OpenViewNameByCfg(self.data.open_panel)
            else
                TipWGCtrl.Instance:ShowSystemMsg(Language.Common.ActivateNoOpen)
            end
        end
    end
end

----------------------------
RebateGiftFlowerItem = RebateGiftFlowerItem or BaseClass(BaseRender)
function RebateGiftFlowerItem:__init()

end

function RebateGiftFlowerItem:__delete()

end

function RebateGiftFlowerItem:LoadCallBack()

end

function RebateGiftFlowerItem:OnFlush()
    local is_get = self.data.is_get > 0
    self.node_list.normal_img:SetActive(not is_get)
    self.node_list.active_img:SetActive(is_get)
end

----------------------------
RebateGiftToggleRender = RebateGiftToggleRender or BaseClass(BaseRender)

function RebateGiftToggleRender:LoadCallBack()
    self.first_flush_img_flag = false
end

function RebateGiftToggleRender:OnFlush()
    if not self.data then
        return
    end

    local cfg = RebateGiftActivityWGData.Instance:GetopenDayGradeCfg()
    if not cfg then
        return
    end

    if not self.first_flush_img_flag then
        self.first_flush_img_flag = true
    end

    local name_str = ""
    if self.data.is_main then
        name_str = cfg.title_name or ""
    elseif self.data.is_today then
        name_str = cfg.today_title_name or ""
    else
        --local day = CommonDataManager.GetAncientNumber(self.data.day)
        --name_str = string.format(Language.RebateGiftAct.GiftDayTxt, day)
        name_str = Language.RebateGiftAct.DayTitleList[self.data.day]
    end

    self.node_list.normal_txt.text.text = name_str
    self.node_list.TextHL.text.text = name_str
    self:ShowRemind()
end

function RebateGiftToggleRender:ShowRemind()
    if not self.data then
        return
    end

    local is_remind = false
    if self.data.is_main then
        local main_task_red = RebateGiftActivityWGData.Instance:GetMainTaskRed()
        is_remind = main_task_red > 0
    else
        local sub_red = RebateGiftActivityWGData.Instance:GetSubTaskRed(self.data.day)
        is_remind = sub_red > 0
    end

    self.node_list.RedPoint:SetActive(is_remind)
end

function RebateGiftToggleRender:OnSelectChange(is_select)
    self.node_list.normal:SetActive(not is_select)
    self.node_list.HLImage:SetActive(is_select)
end

---------------
RebateGiftToggleList = RebateGiftToggleList or BaseClass(AsyncListView)

function RebateGiftToggleList:FlushRemind()
    for k, v in pairs(self.cell_list) do
        v:ShowRemind()
    end
end
