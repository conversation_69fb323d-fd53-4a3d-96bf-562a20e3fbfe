local ONE_KEY_UP_DELAY = 0.5

function MultiFunctionView:ShowDaoHangQiangHuaCallBack()
    self:RightPanleShowTween(self.node_list.qianghua_right_tween_root, self.node_list.qianghua_mid)
    self.node_list.btn_qianghua_onekey_text.text.text = Language.Charm.DaoHangQiangHuaOneKeyUpDesc
end

function MultiFunctionView:LoadDaoHangQiangHuaCallBack()
    if not self.qianghua_target_item then
        self.qianghua_target_item = ItemCell.New(self.node_list.qianghua_target_item)
        self.qianghua_target_item:SetItemTipFrom(ItemTip.FROM_DAOHANG_EQUIP)
    end

    if not self.qianghua_equip_list then
        self.qianghua_equip_list = AsyncListView.New(DaoHangQiangHuaEquipItemRender, self.node_list.qianghua_equip_list)
        self.qianghua_equip_list:SetSelectCallBack(BindTool.Bind(self.OnSelectQiangHuaEquip<PERSON>tem<PERSON><PERSON><PERSON>, self))
    end

    if not self.qianghua_attr_list then
        self.qianghua_attr_list = AsyncListView.New(DaqoHangQiangHuaAttrItemRender, self.node_list.qianghua_attr_list)
    end

    if not self.qianghua_cost_item then
        self.qianghua_cost_item = ItemCell.New(self.node_list.qianghua_cost_item)
    end

    self.daohang_qianghua_select = -1
    self.daohang_qianghua_select_slot = -1
	self.daohang_qianghua_equip_data = {}

    XUI.AddClickEventListener(self.node_list.btn_qianghua, BindTool.Bind(self.OnClickQiangHuaBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_onekey, BindTool.Bind(self.OnClickQiangHuaOneKay, self))
    XUI.AddClickEventListener(self.node_list.btn_qianghua_resonance, BindTool.Bind(self.OnClickQiangHuaResonance, self))
end

function MultiFunctionView:ReleaseQiangHuaCallBack()
    if self.qianghua_target_item then
        self.qianghua_target_item:DeleteMe()
        self.qianghua_target_item = nil
    end

    if self.qianghua_equip_list then
        self.qianghua_equip_list:DeleteMe()
        self.qianghua_equip_list = nil
    end

    if self.qianghua_attr_list then
        self.qianghua_attr_list:DeleteMe()
        self.qianghua_attr_list = nil
    end

    if self.qianghua_cost_item then
        self.qianghua_cost_item:DeleteMe()
        self.qianghua_cost_item = nil
    end
end

function MultiFunctionView:DaoHangQiangShowIndexChange()
    self:DaoHangQiangHuaOneKeyStop()
end

function MultiFunctionView:OnFlushDaoHangQiangHua()
    local remind = MultiFunctionWGData.Instance:GetDaoHangQiangHuaResonanceRemind()
    local data_list = MultiFunctionWGData.Instance:GetDaoHangEquipedEquip()
    local select = self:GetDaoHangQianghuaSelect(data_list)

    self.node_list.btn_qianghua_resonance_remind:CustomSetActive(remind)
    self.qianghua_equip_list:SetDataList(data_list)

    if self.daohang_qianghua_select ~= select then
        self:DaoHangQiangHuaOneKeyStop()
    end

    self.qianghua_equip_list:JumpToIndex(select)

    local cap = MultiFunctionWGData.Instance:GetDaoHangQiangHuaCap()
    self.node_list.qianghua_cap_value.text.text = cap or 0
end

function MultiFunctionView:GetDaoHangQianghuaSelect(data_list)
    if self.daohang_qianghua_select > 0 and self.daohang_qianghua_select_slot >= 0 then
        if MultiFunctionWGData.Instance:GetDaoHangQiangHuaEquipRemindByslot(self.daohang_qianghua_select_slot) then
            return self.daohang_qianghua_select
        end
    end

    for k, v in pairs(data_list) do
        if MultiFunctionWGData.Instance:GetDaoHangQiangHuaEquipRemindByslot(v.slot) then
            return k
        end
    end

    self.daohang_qianghua_select = self.daohang_qianghua_select > 0 and self.daohang_qianghua_select or 1
    return self.daohang_qianghua_select
end

function MultiFunctionView:OnClickQiangHuaResonance()
    local resonance_data = MultiFunctionWGData.Instance:GetDaoHangQiangHuaResonanceData()
    MultiFunctionWGCtrl.Instance:ShowResonanceView(resonance_data)
end

function MultiFunctionView:OnSelectQiangHuaEquipItemHandler(item)
	if nil == item or nil == item.data then
		return
	end

    local data = item.data
    self.qianghua_target_item:SetData(data)
    self.daohang_qianghua_select_slot = data.slot
    self.daohang_qianghua_select = item.index
    self.daohang_qianghua_equip_data = data

    self:FlushQiangHuaMid()
end

function MultiFunctionView:FlushQiangHuaMid()
    if IsEmptyTable(self.daohang_qianghua_equip_data) then
        return
    end

    local cur_level = self.daohang_qianghua_equip_data.level
    local next_level = cur_level + 1
    local cur_level_cfg = MultiFunctionWGData.Instance:GetDaoHangSlotEnhanceCfgBySlotAndLevel(self.daohang_qianghua_select_slot, cur_level)
    local next_level_cfg = MultiFunctionWGData.Instance:GetDaoHangSlotEnhanceCfgBySlotAndLevel(self.daohang_qianghua_select_slot, next_level)
    local not_max_level = not IsEmptyTable(next_level_cfg)

    self.node_list.qianghua_arrow:CustomSetActive(not_max_level)
    self.node_list.qianghua_next_level_bg:CustomSetActive(not_max_level)
    self.node_list.qianghua_max_level_flag:CustomSetActive(not not_max_level)
    self.node_list.qinghua_cost_panel:CustomSetActive(not_max_level)
    self.node_list.qianghua_current_level.text.text = string.format(Language.Common.LevelNormal, cur_level)
    self.node_list.qianghua_next_level.text.text = string.format(Language.Common.LevelNormal, next_level)

    -- local attr_data = MultiFunctionWGData.Instance:MergeTwoAttrTable(cur_level_cfg, next_level_cfg, 5)
    local attr_data = self:GetDaoHangQiangHuaAttrData(cur_level_cfg, next_level_cfg, 5)
    self.qianghua_attr_list:SetDataList(attr_data)

    local cost_enough = false
    if not_max_level then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
        local enough = item_num >= cur_level_cfg.cost_item_num
        self.qianghua_cost_item:SetFlushCallBack(function ()
            local right_text = ToColorStr(item_num .. "/" .. cur_level_cfg.cost_item_num, enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
            self.qianghua_cost_item:SetRightBottomColorText(right_text)
            self.qianghua_cost_item:SetRightBottomTextVisible(true)
        end)
        self.qianghua_cost_item:SetData({item_id = cur_level_cfg.cost_item_id})

		local role_bind_gold = RoleWGData.Instance.role_info.coin or 0
        local bind_gold_enough = cur_level_cfg.cost_coin_num <= role_bind_gold
		local coin_str_color = bind_gold_enough and COLOR3B.D_GREEN or COLOR3B.D_RED
		local cur_coin_str = ToColorStr(CommonDataManager.ConverExpByThousand(role_bind_gold), coin_str_color)
		self.node_list.strength_coin_desc.text.text = cur_coin_str .. "/" .. CommonDataManager.ConverExpByThousand(cur_level_cfg.cost_coin_num)
        cost_enough = enough and bind_gold_enough
    end

    self.node_list.btn_qianghua_remind:CustomSetActive(cost_enough)
    self.node_list.btn_qianghua_onekey_remind:CustomSetActive(cost_enough)
end

function MultiFunctionView:GetDaoHangQiangHuaAttrData(cur_attr, add_attr, attr_len)
    local attr_list = {}
    local attr_id_list = {}

    local function cal_attr_data(attr_id, attr_value, is_add)
        if attr_id > 0 then
            local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_id))

            if not attr_id_list[attr_id] then
                local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str, true)
                local value_str = AttributeMgr.PerAttrValue(attr_str, attr_value)
                local attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)

                local temp = {}
                temp.attr_id = attr_id     			               -- 属性ID
                temp.attr_name = attr_name                         -- 标准属性名
                temp.attr_value = not is_add and attr_value or 0   -- 属性值原值
                temp.value_str =  not is_add and value_str or 0    -- 转换后的属性值 string
                temp.add_attr_value = is_add and attr_value or 0   -- 属性值原值
                temp.add_value_str = is_add and value_str or ""    -- 转换后的属性值 string
                temp.attr_sort = attr_sort                         -- 属性排序值
                table.insert(attr_list, temp)
                attr_id_list[attr_id] = #attr_list
            else
                local index = attr_id_list[attr_id]

                if is_add then
                    attr_list[index].add_attr_value = attr_list[index].add_attr_value + attr_value
                    local value_str = AttributeMgr.PerAttrValue(attr_str, attr_list[index].add_attr_value)
                    attr_list[index].add_value_str = value_str
                
                else
                    attr_list[index].attr_value = attr_list[index].attr_value + attr_value
                    local value_str = AttributeMgr.PerAttrValue(attr_str, attr_list[index].attr_value)
                    attr_list[index].value_str = value_str
                end
            end

        end
    end

    if not IsEmptyTable(cur_attr) or not IsEmptyTable(add_attr) then
        for i = 1, attr_len do
            local attr_id = cur_attr["attr_id" .. i] or 0
            local attr_value = cur_attr["attr_value" .. i] or 0
            cal_attr_data(attr_id, attr_value, false)

            local next_attr_id = add_attr["attr_id" .. i] or 0
            local next_attr_value = add_attr["attr_value" .. i] or 0
            local add_next_value = next_attr_value - attr_value
            add_next_value = add_next_value > 0 and add_next_value or 0
            cal_attr_data(next_attr_id, add_next_value, true)
        end
    end

    if not IsEmptyTable(attr_list) then
        table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
    end

    return attr_list
end

function MultiFunctionView:OnClickQiangHuaBtn()
    if self:IsDaoHangQiangHuaSlotCanUp(true) then
        MultiFunctionWGCtrl.Instance:OnCSTaoistOperate(TAOIST_OPERATE_TYPE.SLOT_ENHANCE, self.daohang_qianghua_select_slot)
    end
end

function MultiFunctionView:OnClickQiangHuaOneKay()
    if self:IsDaoHangQiangHuaSlotCanUp(true) then
        if MultiFunctionWGData.Instance:GetDaoHangQiangHuaAutoUpState() then
            self:DaoHangQiangHuaOneKeyStop()
        else
            MultiFunctionWGData.Instance:SetDaoHangQiangHuaAutoUpState(true)
            MultiFunctionWGCtrl.Instance:OnCSTaoistOperate(TAOIST_OPERATE_TYPE.SLOT_ENHANCE, self.daohang_qianghua_select_slot)
            self.node_list.btn_qianghua_onekey_text.text.text = Language.Charm.DaoHangQiangHuaStopDesc
        end
    end
end

function MultiFunctionView:DaoHangQiangHuaOneKeyStop()
    self.node_list.btn_qianghua_onekey_text.text.text = Language.Charm.DaoHangQiangHuaOneKeyUpDesc
    MultiFunctionWGData.Instance:SetDaoHangQiangHuaAutoUpState(false)
end

function MultiFunctionView:StartUpLevelAni(slot)
    TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji, is_success = true, pos = Vector2(0, 0)})

    if self:IsDaoHangQiangHuaSlotCanUp(false) then
        if MultiFunctionWGData.Instance:GetDaoHangQiangHuaAutoUpState() then
            ReDelayCall(self, function()
                MultiFunctionWGCtrl.Instance:OnCSTaoistOperate(TAOIST_OPERATE_TYPE.SLOT_ENHANCE, slot)
             end, ONE_KEY_UP_DELAY, "daohang_qianghua_one_key")
        end
    else
        self:DaoHangQiangHuaOneKeyStop()
    end
end

function MultiFunctionView:IsDaoHangQiangHuaSlotCanUp(need_show_tip)
    if IsEmptyTable(self.daohang_qianghua_equip_data) then
        return false
    end

    local cur_level = self.daohang_qianghua_equip_data.level
    local cur_level_cfg = MultiFunctionWGData.Instance:GetDaoHangSlotEnhanceCfgBySlotAndLevel(self.daohang_qianghua_select_slot, cur_level)
    local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)

    if item_num < cur_level_cfg.cost_item_num then
        if need_show_tip then
            TipWGCtrl.Instance:OpenItem({item_id = cur_level_cfg.cost_item_id})
        end

        return false
    end

    local role_bind_gold = RoleWGData.Instance.role_info.coin or 0
    if cur_level_cfg.cost_coin_num > role_bind_gold then
        if need_show_tip then
            TipWGCtrl.Instance:OpenItem({item_id = COMMON_CONSTS.VIRTUAL_ITEM_COIN})
        end

        return false
    end

    return true
end

------------------------------------DaoHangQiangHuaEquipItemRender---------------------------
DaoHangQiangHuaEquipItemRender = DaoHangQiangHuaEquipItemRender or BaseClass(BaseRender)

function DaoHangQiangHuaEquipItemRender:LoadCallBack()
    if not self.item then
        self.item = ItemCell.New(self.node_list.item)
        self.item:SetItemTipFrom(ItemTip.FROM_DAOHANG_EQUIP)
    end
end

function DaoHangQiangHuaEquipItemRender:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function DaoHangQiangHuaEquipItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.item:SetData(self.data)
    self.node_list.value.text.text = string.format(Language.Charm.DaoHangQiangHuaItemValue, self.data.level)
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    local name = item_cfg and item_cfg.name or ""
    self.node_list.text.text.text = name
    self.node_list.text_hl.text.text = name

    local remind = MultiFunctionWGData.Instance:GetDaoHangQiangHuaEquipRemindByslot(self.data.slot)
    self.node_list.remind:CustomSetActive(remind)
end

function DaoHangQiangHuaEquipItemRender:OnSelectChange(is_select)
    self.node_list["bg"]:CustomSetActive(not is_select)
    self.node_list["bg_hl"]:CustomSetActive(is_select)
end

-----------------------------------------DaqoHangQiangHuaAttrItemRender--------------------------
DaqoHangQiangHuaAttrItemRender = DaqoHangQiangHuaAttrItemRender or BaseClass(BaseRender)

function DaqoHangQiangHuaAttrItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.attr_name.text.text = self.data.attr_name or ""
    self.node_list.attr_value.text.text = self.data.attr_value or ""

    local can_up = self.data.add_attr_value > 0
    self.node_list.attr_arrow:CustomSetActive(can_up)
    local add_str = can_up and self.data.add_value_str or ""
    self.node_list.attr_add_value.text.text = add_str
end