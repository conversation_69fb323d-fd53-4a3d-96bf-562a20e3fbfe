﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using HUDProgramme;


public class HUDAnimShowType
{
    public static string HUD_SHOW_EXP_ADD = "exp_anim";            //e+ 主角获得经验
    public static string HUD_SHOW_LIFE_EXP = "lift_exp_anim";      //- l+ 主角获得历练
    public static string HUD_SHOW_MONEY_ADD = "money_anim";        //- m+ 主角获得Money
    public static string HUD_SHOW_HP_HURT = "hurt_anim";           //- - 主角掉血
    public static string HUD_SHOW_COMMON_ATTACK = "common_anim";   //- - 其他角色掉血
    public static string HUD_SHOW_CT_ATTACKED = "cted_hurt_anim";  //- 主角被爆击
    public static string HUD_SHOW_CT_ATTACK = "ct_hurt_anim";      //- 主角爆击其他角色
    public static string HUD_SHOW_ABSORB = "absorb_anim";          //- 吸收
    public static string HUB_SHOW_DODGE = "dodge_anim";            //- 闪躲
    public static string HUD_SHOW_RECOVER_HP = "recover_anim";     //- 回血
    public static string HUD_SHOW_PET_ATTACK = "pet_hurt_anim";    //- 宠物造成的伤害数值
}


public class Player : MonoBehaviour
{
    public bool m_bMain;
    public int m_nID;
    private int m_nTitleIns = 0;
    public HUDBloodType m_nBloodType = HUDBloodType.Blood_Red;
    public float m_fBloodPos = 1.0f;
    public string m_szName;
    // Use this for initialization
    void Start ()
    {
        m_szName = name;
        //RefreshTitle();
    }

    void RefreshTitle()
    {
        if( 0 == m_nTitleIns )
            m_nTitleIns = HUDTitleInfo.HUDTitleRender.Instance.RegisterTitle(transform, 1.8f, m_bMain);

        float fOffsetY = GetHeadNameOffsetY();

        HUDTitleInfo  title = HUDTitleInfo.HUDTitleRender.Instance.GetTitle(m_nTitleIns);
        title.Clear();

        title.SetOffsetY(fOffsetY);
        title.ShowTitle(true);
        // 血条
        HUDBloodType nBloodType = GetBloodType();
        if (nBloodType != HUDBloodType.Blood_None)
        {
            title.BeginTitle();
            title.PushBlood(nBloodType, curHpBarValue());
            title.EndTitle();
        }

        title.BeginTitle();
        title.PushTitle(m_szName, HUDTilteType.PlayerName, 0);
        // 威望
        {
            title.PushTitle("天下无双", HUDTilteType.PlayerPrestige, 1);
        }
        // 可反击标识(主角和平模式，并且可以反击）
        if (!m_bMain)
        {
            title.PushIcon(HUDTilteType.PKFlag, HudSetting.Instance.m_nPKFlagPic);
        }
        title.EndTitle();

        // 帮会名字
        string szFamily = "天下第一帮";
        if (!string.IsNullOrEmpty(szFamily))
        {
            title.BeginTitle();
            title.PushTitle(szFamily, HUDTilteType.PlayerCorp, 0);
            title.EndTitle();
        }

        // 称号
        {
            {
                string szDesign = "武林蒙主";
                int nFontType = 1;
                title.BeginTitle();
                title.PushTitle(szDesign, HUDTilteType.PlayerDesignation, nFontType);
                title.EndTitle();
            }
        }

        // 队长标记
        //if (isTeamLeader)
        {
            title.BeginTitle();
            title.PushIcon(HUDTilteType.HeadIcon, HudSetting.Instance.m_nTeamFlagPic);
            title.EndTitle();
        }
    }

    public float curHpBarValue()
    {
        return m_fBloodPos;
    }

    HUDBloodType GetBloodType()
    {
        return m_nBloodType;
    }

    float GetHeadNameOffsetY()
    {
        return 0.5f;
    }

    void OnDestory()
    {
        if(m_nTitleIns != 0)
            HUDTitleInfo.HUDTitleRender.Instance.ReleaseTitle(m_nTitleIns);
        m_nTitleIns = 0;
    }

    // 功能：显示伤害数字
    public void ShowHurt(int nHurtHp)
    {
        // 有一定概率显示爆击
        bool bShowCT = Random.Range(1, 100) < 10;
        if(bShowCT)
        {
            if(m_bMain)
                HUDNumberRender.Instance.AddHudNumber(transform, HUDAnimShowType.HUD_SHOW_CT_ATTACKED, nHurtHp, "爆击", "", false, false, true);
            else
                HUDNumberRender.Instance.AddHudNumber(transform, HUDAnimShowType.HUD_SHOW_CT_ATTACK, nHurtHp, "被爆击", "", false, false, true);
        }
        else
        {
            if (m_bMain)
                HUDNumberRender.Instance.AddHudNumber(transform, HUDAnimShowType.HUD_SHOW_HP_HURT, nHurtHp, "", "", false, true, true);
            else
                HUDNumberRender.Instance.AddHudNumber(transform, HUDAnimShowType.HUD_SHOW_COMMON_ATTACK, nHurtHp, "", "", false, true, true);
        }
    }

    // 功能：显示经验数字
    public void  ShowExp(int nExp)
    {
        HUDNumberRender.Instance.AddHudNumber(transform, HUDAnimShowType.HUD_SHOW_EXP_ADD, nExp, "经验", "", true, false, true);
    }
	
	// Update is called once per frame
	void Update ()
    {		
	}
}
