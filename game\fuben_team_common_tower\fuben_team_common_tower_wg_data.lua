FuBenTeamCommonTowerWGData = FuBenTeamCommonTowerWGData or BaseClass()

function FuBenTeamCommonTowerWGData:__init()
	if FuBenTeamCommonTowerWGData.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[FuBenTeamCommonTowerWGData] attempt to create singleton twice!")
		return
	end

	FuBenTeamCommonTowerWGData.Instance = self

	self:InitCfg()
    self.fb_info_list = {}
    self.fuben_enter_times_list = {}
    self.fuben_hurt_list = {}
    self.nor_draw_reward_list = {}
	self.spe_draw_reward_list = {}
end


function FuBenTeamCommonTowerWGData:__delete()
	FuBenTeamCommonTowerWGData.Instance = nil
end

function FuBenTeamCommonTowerWGData:InitCfg()
    local cfg = ConfigManager.Instance:GetAutoConfig("fb_team_commom_tower_auto")
    self.fb_seq_cfg = ListToMap(cfg.fb, "seq")
    self.scene_cfg = ListToMap(cfg.fb, "scene_id")
    self.fb_type_model_cfg = ListToMap(cfg.fb, "team_type","team_mode")
    self.team_type_cfg = ListToMap(cfg.team_type, "team_type")
    self.suggest_cap_cfg = ListToMapList(cfg.suggest_cap, "team_type")
    self.monster_cfg =  ListToMapList(cfg.monster, "grade","level","wave")
    self.level_cfg = ListToMap(cfg.level, "grade", "level")
    self.other_cfg = cfg.other[1]
end

function FuBenTeamCommonTowerWGData:SetTeamCommonTowerFBInfo(protocol)
    local data = {}
    data.fb_seq = protocol.fb_seq
    data.grade = protocol.grade
    data.level = protocol.level
    data.wava = protocol.wava
    data.last_wave_monster_num = protocol.last_wave_monster_num
    data.is_end = protocol.is_end
    data.is_pass = protocol.is_pass
    data.kick_out_time = protocol.kick_out_time
    data.fb_end_time = protocol.fb_end_time
    data.next_refresh_monster_time = protocol.next_refresh_monster_time
    data.next_level_time = protocol.next_level_time
    self.fb_info_list[protocol.fb_seq] = data
end

function FuBenTeamCommonTowerWGData:SetTeamCommonTowerFBHurtInfo(protocol)
    self.fuben_hurt_list = protocol.team_hurt_list
    if not IsEmptyTable(self.fuben_hurt_list) then
        table.sort(self.fuben_hurt_list, SortTools.KeyUpperSorters("hurt_count", "count"))
    end
end

function FuBenTeamCommonTowerWGData:SetTeamCommonTowerFBDrawInfo(protocol)
    self.nor_draw_reward_list = protocol.nor_draw_reward_list or {}
	self.spe_draw_reward_list = protocol.spe_draw_reward_list or {}
    self.draw_end_time = protocol.draw_end_time
    self.is_my_draw_nor = false
    self.is_my_draw_spe = false
    for i, v in pairs(self.nor_draw_reward_list) do
        if v.uuid == RoleWGData.Instance:GetUUid() then
            self.is_my_draw_nor = true
            break
        end
    end

    for i, v in pairs(self.spe_draw_reward_list) do
        if v.uuid == RoleWGData.Instance:GetUUid() then
            self.is_my_draw_spe = true
            break
        end
    end
end

function FuBenTeamCommonTowerWGData:GetTeamCommonTowerFBInfo(fb_seq)
    return self.fb_info_list[fb_seq]
end

function FuBenTeamCommonTowerWGData:GetAllTeamCommonTowerHurtInfo()
    return self.fuben_hurt_list
end

function FuBenTeamCommonTowerWGData:GetTeamCommonTowerFBDrawInfo()
    return self.nor_draw_reward_list, self.spe_draw_reward_list
end

function FuBenTeamCommonTowerWGData:GetTeamCommonTowerFBMyDrawState()
    return self.is_my_draw_nor or false, self.is_my_draw_spe or false
end

function FuBenTeamCommonTowerWGData:GetTeamCommonTowerFBDrawEndTime()
    return self.draw_end_time or 0
end

function FuBenTeamCommonTowerWGData:RestSetTeamCommonTowerFBInfo()
    self.fb_info_list = {}
end

function FuBenTeamCommonTowerWGData:GetFuBenCfgBySeq(fb_seq)
    return self.fb_seq_cfg[fb_seq]
end

function FuBenTeamCommonTowerWGData:GetFuBenTypeModelCfg(team_type, team_mode)
    if team_mode then
        return (self.fb_type_model_cfg[team_type] or {})[team_mode]
    else
        return self.fb_type_model_cfg[team_type]
    end
end

function FuBenTeamCommonTowerWGData:GetFuBenTeamTypeCfg(team_type)
    return self.team_type_cfg[team_type]
end

function FuBenTeamCommonTowerWGData:GetFuBenSceneCfg(scene_id)
    return self.scene_cfg[scene_id]
end

function FuBenTeamCommonTowerWGData:GetSuggestCfgByType(team_type)
    return self.suggest_cap_cfg[team_type]
end

function FuBenTeamCommonTowerWGData:GetMonsterCfgByWave(grade, level, wave)
    return ((self.monster_cfg[grade] or {})[level] or {})[wave]
end

function FuBenTeamCommonTowerWGData:GetLevelCfgByLevel(grade, level)
    if level then
        return (self.level_cfg[grade] or {})[level]
    else
        return self.level_cfg[grade]
    end
end

function FuBenTeamCommonTowerWGData:GetFuBenOtherCfg()
    return self.other_cfg
end

--设置副本每天已进入次数
function FuBenTeamCommonTowerWGData:SetFuBenEnterTimes(team_type, times)
	self.fuben_enter_times_list[team_type] = times
end

--获取副本每天已进入次数
function FuBenTeamCommonTowerWGData:GetFuBenEnterTimes(team_type)
	return self.fuben_enter_times_list[team_type] or 0
end

--获取最大伤害值
function FuBenTeamCommonTowerWGData:GetNormalHurtInfoMaxValue()
	local hurt = 1
	if self.fuben_hurt_list[1] and self.fuben_hurt_list[1].hurt_count > hurt then
		hurt = self.fuben_hurt_list[1].hurt_count
	end

	return hurt
end

function FuBenTeamCommonTowerWGData:SetGetIsHelpState(is_help)
    if is_help then
        self.win_show_is_help = is_help
    else
       return self.win_show_is_help or 0
    end
end

function FuBenTeamCommonTowerWGData:SetGetMvpInfo(mvp_player_info)
    if mvp_player_info then
        self.mvp_info = mvp_player_info
    else
       return self.mvp_info
    end
end