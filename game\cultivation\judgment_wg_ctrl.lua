--[[
	/渡劫
]]

CultivationWGCtrl = CultivationWGCtrl or BaseClass(BaseWGCtrl)

function CultivationWGCtrl:OpenJudgmentReadyView()
	if self.judgment_ready_view then
		self.judgment_ready_view:Open()
	end
end

-- 渡劫 开始渡劫
function CultivationWGCtrl:JudgmentStart(hp)
	self.judgment_ready_view:Close()
	if self.judgment_view:IsOpen() then
		self.judgment_view:JudgmentStart(hp)
	end
end

function CultivationWGCtrl:FlushJudgmentView()
	if self.judgment_view:IsOpen() then
		self.judgment_view:Flush()
	end
end


function CultivationWGCtrl:FlushJudgmentReadyView()
	if self.judgment_ready_view:IsOpen() then
		self.judgment_ready_view:Flush()
	end
end

function CultivationWGCtrl:FlushJudgment()
	self:FlushJudgmentView()
	self:FlushJudgmentReadyView()
end

-- 渡劫 兑换血量
function CultivationWGCtrl:ExchangeHp(data)
	self:SendRoleXiuWeiOperate(XIUWEI_OPERA_TYPE.UPGRADE_STAGE_CLIENT,0,data.xiuwei)
	
	self:JudgmentStart(data.hp)
end

-- 渡劫 渡劫结果
function CultivationWGCtrl:JudgmentResult(is_success)
	if is_success then
		self:SendRoleXiuWeiOperate(XIUWEI_OPERA_TYPE.UPGRADE_STAGE_CLIENT,1,0)
	end
end

-- 渡劫 渡劫结果显示
function CultivationWGCtrl:JudgmentResultShow(is_success)
	if is_success then
		CultivationWGCtrl.Instance:OpenCultivationBreakSuccessView()
	else
		CultivationWGCtrl.Instance:OpenCultivationBreakFailView()
	end
end