-- 暗器软甲详情
local left_cell_start_pos = Vector2(77, -343)
local left_cell_end_pos = Vector2(-580, -343)
local detail_bag_start_pos = Vector2(-580, -36)
local detail_bag_end_pos = Vector2(0, -36)

function HiddenWeaponView:LoadBuildIndexCallBack()
    self.current_data = {}

    if self.is_init_loaded == true then
        return
    end

    self.is_init_loaded = true
    if not self.bag_grid then
        self.bag_grid = AsyncBaseGrid.New()
        self.bag_grid:SetIsShowTips(false)
        self.bag_grid:SetStartZeroIndex(false)
        local bag_limit = HiddenWeaponWGData.Instance:GetBagLimit()
        self.bag_grid:CreateCells(
            {
                col = 2,
                cell_count = bag_limit,
                list_view = self.node_list["hw_build_bag_list"],
                itemRender = HWBuildItemRender,
                assetBundle = "uis/view/shenji_anqiruanjia_ui_prefab",
                assetName = "hw_build_cell"
            }
        )
        self.bag_grid:SetSelectCallBack(BindTool.Bind1(self.OnSelectEquip, self))
    end
    self.bag_grid:CancleAllSelectCell()

    self.current_cell = ItemCell.New(self.node_list["item_current"])
    self.target_cell = ItemCell.New(self.node_list["item_target"])

    -- 筛选
    for index = 0, 6 do
        XUI.AddClickEventListener(
            self.node_list["build_filter_toggle_" .. index],
            BindTool.Bind(self.OnFilterChanged, self, index)
        )
    end
    -- 材料框
    self.render_materials_same = {}
    self.render_materials_diff = {}
    self.render_materials_node_type = {}
    for index = 1, 3 do
        local render = HWMaterialRender.New(self.node_list["hw_build_material" .. index], self)
        table.insert(self.render_materials_same, render)
    end
    for index = 4, 6 do
        local render = HWMaterialRender.New(self.node_list["hw_build_material" .. index], self)
        table.insert(self.render_materials_node_type, render)
    end
    for index = 7, 9 do
        local render = HWMaterialRender.New(self.node_list["hw_build_material" .. index], self)
        table.insert(self.render_materials_diff, render)
    end
    XUI.AddClickEventListener(self.node_list["btn_build"], BindTool.Bind1(self.ReqBuildCompose, self))
    XUI.AddClickEventListener(self.node_list["btn_quick_build"], BindTool.Bind1(self.ReqQuickBuildCompose, self))
    
    -- 7个技能
    self.hw_wudao_skill_node = {}
    for index = 1, 7 do
        self.hw_wudao_skill_node[index] = HWBuildSkillRender.New(self.node_list["hw_wudao_skill" .. index])
    end

    if not self.change_skill_list then
        self.change_skill_list = AsyncListView.New(HWChangeSkillRender, self.node_list.wudao_change_skill_list)
    end

    XUI.AddClickEventListener(self.node_list["btn_wudao_select"], BindTool.Bind1(self.OnClickWuDaoSelectBtn, self))
    XUI.AddClickEventListener(self.node_list["filter_select"], BindTool.Bind1(self.OnClickWuDaoBackSelect, self))
    -- XUI.AddClickEventListener(self.node_list["btn_build_bag_close"], BindTool.Bind1(self.OnClickBuildBag, self))
    -- XUI.AddClickEventListener(self.node_list["btn_build_bag"], BindTool.Bind1(self.OnClickBuildBag, self))
end

function HiddenWeaponView:ReleaseBuildCallBack()
    CancleAllDelayCall(self)
    self.is_init_loaded = nil

    if self.change_skill_list then
        self.change_skill_list:DeleteMe()
        self.change_skill_list = nil
    end

    if nil ~= self.bag_grid then
        self.bag_grid:DeleteMe()
        self.bag_grid = nil
    end
    if nil ~= self.current_cell then
        self.current_cell:DeleteMe()
        self.current_cell = nil
    end
    if nil ~= self.target_cell then
        self.target_cell:DeleteMe()
        self.target_cell = nil
    end

    if self.render_materials_same then
        for index, render in ipairs(self.render_materials_same) do
            render:DeleteMe()
        end
        self.render_materials_same = nil
    end
    if self.render_materials_diff then
        for index, render in ipairs(self.render_materials_diff) do
            render:DeleteMe()
        end
        self.render_materials_diff = nil
    end
    self.render_materials_diff = nil

    if self.render_materials_node_type then
        for index, render in ipairs(self.render_materials_node_type) do
            render:DeleteMe()
        end
        self.render_materials_node_type = nil
    end
    self.render_materials_node_type = nil

    self.select_material_indexs = nil
    self.current_data = nil

    if self.hw_wudao_skill_node then
        for k, v in pairs(self.hw_wudao_skill_node) do
            v:DeleteMe()
        end

        self.hw_wudao_skill_node = nil
    end
end

function HiddenWeaponView:ShowBuildIndexCallBack()
    if self.bag_grid == nil then
        return
    end

    self:RefreshBuildView()
    self:InitBuildBagPosition()
end

function HiddenWeaponView:RefreshBuildView()
    ReDelayCall(
        self,
        function()
            self:SetBuildupData()
        end,
        0.01,
        "HiddenWeaponBuildView_FILTER"
    )
    self:SetupFilterName()
end

function HiddenWeaponView:ReqQuickBuildCompose()
    HiddenWeaponWGCtrl.Instance:OpenHiddenWeaponRL()
end

-- 打造
function HiddenWeaponView:ReqBuildCompose()
    local data = self.current_data
    if data == nil or data.equip == nil then
        return
    end

    local selected_indexs = self:GetSelectedMaterial()
    local send_fun = function ()
        local weapon_type = self:GetWeaponType()
        if #selected_indexs < (self.require_num or 0) then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenJiEquip.COMPOSE_TIP[weapon_type])
        end

        if data.index == HW_ANQI_EQUIP_INDEX or data.index == HW_RUANJIA_EQUIP_INDEX then
            -- 当前选择升级的是装备中的
            HiddenWeaponRequest:ReqCurrEquipCompose(data.equip.big_type, selected_indexs)
        else
            HiddenWeaponRequest:ReqCompose1(data.index, selected_indexs)
        end
    end

    for k,v in pairs(selected_indexs) do
        local select_data = HiddenWeaponWGData.Instance:GetDataByIndex(v)
        if not IsEmptyTable(select_data) then
            local select_color = select_data.equip.origin_data.base_color
            local data_color = data.equip.origin_data.base_color
            local select_special_flag = select_data.color_attr.special_flag
            local data_special_flag = data.color_attr.special_flag
            if select_color > data_color or (select_special_flag == 1 and data_special_flag == 0) then
                local select_name = ToColorStr(select_data.equip.name,ITEM_COLOR[select_data.equip.base_color])
                local weapon_type_name = Language.ShenJiEquip.COMMON_WEAPONTYPE_NAME[select_data.equip.big_type]
                local str = string.format(Language.ShenJiEquip.BUILD_EQUIP_CHANGE,select_name,weapon_type_name,select_name)
                TipWGCtrl.Instance:OpenAlertTips(str,send_fun)
                return
            end
        end        
    end

    send_fun()
end

function HiddenWeaponView:SetBuildupData()
    if self.bag_grid == nil then
        return
    end

    local filter_tag = self:GetFilterTag()
    local select_str = Language.ShenJiEquip.SUBTYPE_NAME_all
    if filter_tag > 0 then
        local weapon_type = filter_tag > 3 and filter_tag - 3 or filter_tag
        local name_str = filter_tag > 3 and "ARMOR_SUBTYPE_NAME" or "WEAPON_SUBTYPE_NAME"
        local attr_str = name_str .. weapon_type
        select_str = Language.ShenJiEquip[attr_str]
    end
    self.node_list.text_wudao_select.text.text = select_str
    self.curr_datas = HiddenWeaponWGData.Instance:GetDataList(nil, filter_tag)
    self.bag_grid:SetDataList(self.curr_datas)
    self.bag_grid:CancleAllSelectCell()
    if #self.curr_datas > 0 then
        local select_index = 1
        for index, data in ipairs(self.curr_datas) do
            if data.can_compose then
                select_index = index
                break
            end
        end
        self.bag_grid:SetSelectCellIndex(select_index)
        self:SetupContent(self.curr_datas[select_index])
    else
        self:ClearContent()
    end
end

function HiddenWeaponView:ClearContent()
    self.node_list["build_group_empty"]:SetActive(true)
    self.node_list["build_group_right"]:SetActive(false)
    self.node_list["shenji_build_name_bg"]:SetActive(false)
    self.node_list["mid_build_view"]:SetActive(false)
    self.node_list["group_material"]:SetActive(false)
    --self.node_list["bg_kongzhi_build"]:SetActive(true)

    local weapon_type = self:GetWeaponType()
    self.node_list["empty_desc"].text.text = Language.ShenJiEquip.NO_HAS_EQUIP_TIPS[weapon_type]

    for k, v in pairs(self.hw_wudao_skill_node) do
        self.node_list["hw_wudao_skill" .. k]:CustomSetActive(false)
    end
end

function HiddenWeaponView:GetSelectedMaterial()
    local selected_indexs = {}
    for index, render in ipairs(self.render_materials_same) do
        local item_index = render:GetSelect()
        if item_index then
            table.insert(selected_indexs, item_index)
        end
    end

    for index, render in ipairs(self.render_materials_node_type) do
        local item_index = render:GetSelect()
        if item_index then
            table.insert(selected_indexs, item_index)
        end
    end

    for index, render in ipairs(self.render_materials_diff) do
        local item_index = render:GetSelect()
        if item_index then
            table.insert(selected_indexs, item_index)
        end
    end

    return selected_indexs
end

function HiddenWeaponView:CheckWuDaoSkillUp(skill_type, skill_id, color, star, target_color, target_star)
    local cfg, is_active, max_cfg = HiddenWeaponWGData.Instance:GetSkillCfg(skill_type, skill_id, color, star)
    local target_cfg, target_active, target_max = HiddenWeaponWGData.Instance:GetSkillCfg(skill_type, skill_id, target_color, target_star)

    if IsEmptyTable(cfg) then
        return false, {}
    end

    local can_up = false
    local next_skill_data = {cfg = target_cfg, is_active = target_active, skill_type = skill_type, max_cfg = target_max}
    local skill_data = {cfg = cfg, is_active = is_active, skill_type = skill_type, max_cfg = max_cfg, next_skill_data = next_skill_data, can_up = false, can_acitve = false}

    if (is_active == false and target_active == true) or (is_active and target_active and cfg.skill_level < target_cfg.skill_level) then
        skill_data.can_up = true
        skill_data.can_active = (is_active == false and target_active == true)
        can_up = true
    end

    return can_up, skill_data
end

function HiddenWeaponView:GetMaxWuDaoSkillData(skill_type, skill_id, color, star)
    local cfg, is_active, max_cfg = HiddenWeaponWGData.Instance:GetSkillCfg(skill_type, skill_id, color, star)

    if IsEmptyTable(cfg) then
        return false, {}
    end

    local can_up = false
    local skill_data = {cfg = cfg, is_active = is_active, skill_type = skill_type, max_cfg = max_cfg, next_skill_data = cfg, can_up = false, can_acitve = false}

    return can_up, skill_data
end

function HiddenWeaponView:SetupSkillUpgrade(data, target_vo)
    for k, v in pairs(self.hw_wudao_skill_node) do
        v:SetData(nil)
        self.node_list["hw_wudao_skill" .. k]:CustomSetActive(false)
    end

    self.node_list.list_wudao_skill:CustomSetActive(false)
    self.node_list.wudao_change_skill_bg:CustomSetActive(false)

    local color = data.equip.base_color
    local star = data.equip.base_star
    local target_color = target_vo and target_vo.equip.base_color
    local target_star = target_vo and target_vo.equip.base_star
    local index = 1
    local change_skill_list = {}

    local function change_skill_state(skill_index, skill_data)
        if self.hw_wudao_skill_node[skill_index] then
            if IsEmptyTable(skill_data) then
                return
            end

            self.node_list["hw_wudao_skill" .. skill_index]:CustomSetActive(true)
            self.hw_wudao_skill_node[skill_index]:SetData(skill_data)
            index = index + 1

            if skill_data.can_up or skill_data.can_active then
                table.insert(change_skill_list, skill_data)
            end
        end
    end

    local function get_skill_data(skill_type, skill_id, color, star, target_color, target_star)
        local can_up, skill_data = false, nil
        if target_vo then
            can_up, skill_data = self:CheckWuDaoSkillUp(skill_type,
            skill_id,
            color,
            star,
            target_color,
            target_star
            )
        else
            can_up, skill_data = self:GetMaxWuDaoSkillData(skill_type,
            skill_id,
            color,
            star)
        end

        return can_up, skill_data
    end

    -- 主动技能
    local active_skill_list = string.split(data.equip.active_skill_list, "|")
    for k, v in pairs(active_skill_list) do
        local skill_id = tonumber(active_skill_list[k])
        local can_up, skill_data = get_skill_data(HW_CONST_PARAM.ACTIVE_SKILL_TYPE,
            skill_id,
            color,
            star,
            target_color,
            target_star)

        change_skill_state(index, skill_data)
    end

    --特殊
    local special_skill_list = string.split(data.equip.special_skill_list, "|")
    for k, v in pairs(special_skill_list) do
        local skill_id = tonumber(special_skill_list[k])
        local can_up, skill_data = get_skill_data(HW_CONST_PARAM.SPECIAL_SKILL_TYPE,
            skill_id,
            color,
            star,
            target_color,
            target_star)

        change_skill_state(index, skill_data)
    end

    index = 5
    local passive_skill_list = string.split(data.equip.passive_skill_list, "|")
    for k, v in pairs(passive_skill_list) do
        local skill_id = tonumber(passive_skill_list[k])
        local can_up, skill_data = get_skill_data(HW_CONST_PARAM.PASSIVE_SKILL_TYPE,
            skill_id,
            color,
            star,
            target_color,
            target_star)

        change_skill_state(index, skill_data)
    end

    self.node_list.list_wudao_skill:CustomSetActive(#change_skill_list == 0)
    self.node_list.wudao_change_skill_bg:CustomSetActive(#change_skill_list > 0)

    if #change_skill_list > 0 then
        self.change_skill_list:SetDataList(change_skill_list)
    end
end

function HiddenWeaponView:SetupContent(data)
    self.current_data = data
    self.select_material_indexs = {}
    self.node_list["shenji_build_name_bg"]:SetActive(true)
    local a, b = ResPath.GetShenJiModel(data.equip.up_model)
    local big_type = data.equip.big_type or 1
    local node_type = data.equip.node_type or 1
    local node_type_name = Language.ShenJiEquip.NodeTypeName[big_type][node_type]
    -- local name = string.format(Language.ShenJiEquip.BuildName,data.equip.name,node_type_name)
    local name = data.equip.name
    self.node_list["shenji_build_name"].text.text = name

    -- 检查目标装备
    for index, render in ipairs(self.render_materials_same) do
        render:ClearSelect()
    end
    for index, render in ipairs(self.render_materials_node_type) do
        render:ClearSelect()
    end
    for index, render in ipairs(self.render_materials_diff) do
        render:ClearSelect()
    end
    local target_vo, breakthrough = HiddenWeaponWGData.Instance:GetComposeTargetEquip(data)
    if target_vo == nil then
        self.node_list["build_group_empty"]:SetActive(true)
        self.node_list["build_group_right"]:SetActive(false)
        self.node_list["group_material"]:SetActive(false)
        self.node_list["mid_build_view"]:SetActive(false)
        --self.node_list["bg_kongzhi_build"]:SetActive(false)
        self:RefreshBuildBtnState()
        self:SetupSkillUpgrade(data)
        return
    end

    self.node_list["build_group_empty"]:SetActive(false)
    self.node_list["build_group_right"]:SetActive(true)
    self.node_list["group_material"]:SetActive(true)
    self.node_list["mid_build_view"]:SetActive(true)
    --self.node_list["bg_kongzhi_build"]:SetActive(false)

    -- 检查技能升级
    self:SetupSkillUpgrade(data, target_vo)
    -- 等级限制
    local limit = breakthrough.role_level_limit or 0
    local level = RoleWGData.Instance:GetRoleLevel()
    if level >= limit then
        XUI.SetButtonEnabled(self.node_list["btn_build"], true)
        self.node_list["txt_level_limit"]:SetActive(false)
    else
        XUI.SetButtonEnabled(self.node_list["btn_build"], false)
        self.node_list["txt_level_limit"]:SetActive(true)

        local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(limit)
        if is_vis then
            self.node_list["txt_level_limit"].text.text = string.format(Language.ShenJiEquip.LEVEL_LIMIT2, role_level)
        else
            self.node_list["txt_level_limit"].text.text = string.format(Language.ShenJiEquip.LEVEL_LIMIT, role_level)
        end
    end
    -- 设置材料框
    local same_node_type_num = breakthrough.same_node_type_num or 0
    local same_big_type_num = breakthrough.same_big_type_num or 0
    local same_id_num = breakthrough.same_id_num or 0
    self.require_num = same_node_type_num + same_big_type_num + same_id_num
    local item_index = data.index --当前选择装备的 index

    local show_materials_same = false
    local show_materials_node_type = false
    local show_materials_diff = false

    -- 伪造一个同名卡装备数据
    local same_id_preview_data =
        HiddenWeaponWGData.Instance:GetCommonEquipVo(
        {
            item_id = data.item_id,
            color = breakthrough.same_id_color,
            star = breakthrough.same_id_star
        }
    )
    for index, render in ipairs(self.render_materials_same) do
        if index < same_id_num + 1 then
            render:SetVisible(true)
            render:SetPreview(same_id_preview_data, false)

            render:SetTarget(
                item_index,
                target_vo.equip.big_type,
                breakthrough.same_id_color,
                breakthrough.same_id_star,
                target_vo.item_id,
                data.can_compose,
                -- target_vo.equip.node_type
                nil
            )
            show_materials_same = true
        else
            render:SetVisible(false)
            render:ClearSelect()
        end
    end

    -- 伪造一个同子类卡装备数据
    local same_node_type_preview_data =
        HiddenWeaponWGData.Instance:GetCommonEquipVo(
        {
            item_id = data.item_id,
            color = breakthrough.same_node_type_color,
            star = breakthrough.same_node_type_star
        }
    )
    for index, render in ipairs(self.render_materials_node_type) do
        if index < same_node_type_num + 1 then
            render:SetVisible(true)
            render:SetPreview(same_node_type_preview_data, false)

            render:SetTarget(
                item_index,
                target_vo.equip.big_type,
                breakthrough.same_node_type_color,
                breakthrough.same_node_type_star,
                -- target_vo.item_id,
                nil,
                data.can_compose,
                target_vo.equip.node_type
            )
            show_materials_node_type = true
        else
            render:SetVisible(false)
            render:ClearSelect()
        end
    end

    -- 伪造一个非同名卡装备数据
    local diff_id_preview_data =
        HiddenWeaponWGData.Instance:GetCommonEquipVo(
        {
            item_id = data.item_id,
            color = breakthrough.same_big_type_color,
            star = breakthrough.same_big_type_star
        }
    )

    for index, render in ipairs(self.render_materials_diff) do
        if index < same_big_type_num + 1 then
            render:SetVisible(true)
            render:SetPreview(diff_id_preview_data, false)

            render:SetTarget(
                item_index,
                target_vo.equip.big_type,
                breakthrough.same_big_type_color,
                breakthrough.same_big_type_star,
                nil,
                data.can_compose,
                -- target_vo.equip.node_type
                nil
            )
            -- show_materials_same = false
            -- show_materials_node_type = false
            show_materials_diff = true
        else
            render:SetVisible(false)
            render:ClearSelect()
        end
    end

    local has_show_index = {}

    if show_materials_same then
         -- 自动填充同名卡
        local data_list =
            HiddenWeaponWGData.Instance:GetDataListByCondition(
            target_vo.equip.big_type,
            item_index,
            target_vo.item_id,
            -- nil,
            breakthrough.same_id_color,
            breakthrough.same_id_star,
            target_vo.equip.node_type
        )

        local counter = 1
        for index, render in ipairs(self.render_materials_same) do
            if data_list[counter] and render:GetActive() and render:GetSelect() == nil then
                render:OnMaterialSelect(data_list[counter].index)
                has_show_index[data_list[counter].index] = true
                counter = counter + 1
            end
        end
    end

    if show_materials_node_type then
        -- 自动填充同子类卡
        local data_list =
            HiddenWeaponWGData.Instance:GetDataListByCondition(
            target_vo.equip.big_type,
            item_index,
            -- target_vo.item_id,
            nil,
            breakthrough.same_node_type_color,
            breakthrough.same_node_type_star,
            target_vo.equip.node_type
        )

        local counter = 1
        for index, render in ipairs(self.render_materials_node_type) do
            if render:GetActive() and render:GetSelect() == nil then
                local data_info = {}
                for i=1,#data_list do
                    if not has_show_index[data_list[i].index] then
                        data_info = data_list[i]
                        break
                    end
                end
                if not IsEmptyTable(data_info) and has_show_index[data_info.index] == nil then
                    render:OnMaterialSelect(data_info.index)
                    has_show_index[data_info.index] = true
                end
                counter = counter + 1
            end
        end
    end

    if show_materials_diff then
        local diff_data_list =
            HiddenWeaponWGData.Instance:GetDataListByCondition(
            target_vo.equip.big_type,
            item_index,
            nil,
            breakthrough.same_big_type_color,
            breakthrough.same_big_type_star
        )

        local diff_counter = 1
        for index, render in ipairs(self.render_materials_diff) do
            if render:GetActive() and render:GetSelect() == nil then
                local data_info = {}
                for i=1,#diff_data_list do
                    if not has_show_index[diff_data_list[i].index] then
                        data_info = diff_data_list[i]
                        break
                    end
                end
                
                if not IsEmptyTable(data_info) and has_show_index[data_info.index] == nil then
                    render:OnMaterialSelect(data_info.index)
                    has_show_index[data_info.index] = true
                end
                diff_counter = diff_counter + 1
            end
        end
    end

    self:RefreshBuildBtnState()
    -- 判断设置材料装备框数量
    self.current_cell:SetData(data)
    self.target_cell:SetData(target_vo)

    -- 设置 itemcell

    -- 技能变化
    -- 设置基础信息，按 HW_BASE_ATTRS 顺序来检索
    local current_base_attrs = data.base_attr
    local target_base_attrs = target_vo.base_attr

    local counter = 1
    for index, attr_name in ipairs(HW_BASE_ATTRS) do
        local key = "txt_build_base_attr_" .. counter
        if current_base_attrs[attr_name] and current_base_attrs[attr_name] > 0 and self.node_list[key .. 1] then
            self.node_list["build_base_attr_node" .. counter]:SetActive(true)
            self.node_list[key .. 1].text.text = (Language.Common.AttrNameList2[attr_name] or "") .. "：" .. tostring(current_base_attrs[attr_name])
            local lerp_attr = target_base_attrs[attr_name] - current_base_attrs[attr_name]
            self.node_list[key .. 2].text.text = tostring(lerp_attr)
            self.node_list[key .. 2]:SetActive(lerp_attr > 0)

            counter = counter + 1
        end
    end

    if counter <= 3 then
        for i = counter, 3 do
            self.node_list["build_base_attr_node" .. i]:SetActive(false)
        end
    end

    -- 设置星级属性
    local attrs_index = HiddenWeaponWGData.Instance:GetColorAttrIndex(data.equip.big_type)
    local current_star_attrs = data.color_attr
    local target_star_attrs = target_vo.color_attr
    counter = 1
    for index, attr_name in ipairs(attrs_index) do
        local key = "txt_build_star_attr_" .. counter
        if current_star_attrs[attr_name] and current_star_attrs[attr_name] > 0 and self.node_list[key .. 1] then
            self.node_list["build_star_attr_node" .. counter]:SetActive(true)
            if "base_attr_per" == attr_name then
                self.node_list[key .. 1].text.text =
                    (Language.Common.AttrNameList2[attr_name] or "") .. "：" .. tostring(current_star_attrs[attr_name] / 100) .. "%" 
                local lerp_attr = target_star_attrs[attr_name] - current_star_attrs[attr_name]
                self.node_list[key .. 2].text.text = tostring(lerp_attr / 100) .. "%"
                self.node_list[key .. 2]:SetActive(lerp_attr > 0)
            else
                self.node_list[key .. 1].text.text =
                    (Language.Common.AttrNameList2[attr_name] or "") .. "：" .. tostring(current_star_attrs[attr_name]) 
                local lerp_attr = target_star_attrs[attr_name] - current_star_attrs[attr_name]
                self.node_list[key .. 2].text.text = tostring(lerp_attr)
                self.node_list[key .. 2]:SetActive(lerp_attr > 0)
            end
            counter = counter + 1
        end
    end
    if counter <= 6 then
        for i = counter,6 do
            self.node_list["build_star_attr_node" .. i]:SetActive(false)
        end
    end
end
-- 选择装备
function HiddenWeaponView:OnSelectEquip(item)
    if not item.data or not item.data.equip then
        return
    end
    self:SetupContent(item.data)
end

-- 材料选择变化
function HiddenWeaponView:SelectItem()
end

-- 获取筛选类型
function HiddenWeaponView:GetFilterTag()
    for index = 1, 6 do
        if self.node_list["build_filter_toggle_" .. index].toggle.isOn == true then
            return index
        end
    end

    return -1
end

function HiddenWeaponView:RefreshRemindQuick()
    if self.node_list and self.node_list.remind_quick_build then
        local data_list = HiddenWeaponWGData.Instance:GetRLBagList()
        self.node_list["remind_quick_build"]:SetActive(#data_list > 0)
    end
end

-- 筛选变化
function HiddenWeaponView:OnFilterChanged(index, is_on)
    -- local filter_tag = self:GetFilterTag()
    ReDelayCall(
        self,
        function()
            self:SetBuildupData()
        end,
        0.01,
        "HiddenWeaponBuildView_FILTER"
    )

    self.node_list.part_wudao:CustomSetActive(false)
    self.node_list.filter_select:CustomSetActive(false)
end

function HiddenWeaponView:SetupFilterName()
    if not self.is_init_loaded then return end
    -- local weapon_type = self:GetWeaponType()
    -- if weapon_type == 2 then
    --     --SysMsgWGCtrl.Instance:ErrorRemind("软甲")
    --     self.node_list["an_txt_11"].text.text = Language.ShenJiEquip.ARMOR_SUBTYPE_NAME1
    --     self.node_list["hl_txt_11"].text.text = Language.ShenJiEquip.ARMOR_SUBTYPE_NAME1
    --     self.node_list["an_txt_22"].text.text = Language.ShenJiEquip.ARMOR_SUBTYPE_NAME2
    --     self.node_list["hl_txt_22"].text.text = Language.ShenJiEquip.ARMOR_SUBTYPE_NAME2
    --     self.node_list["an_txt_33"].text.text = Language.ShenJiEquip.ARMOR_SUBTYPE_NAME3
    --     self.node_list["hl_txt_33"].text.text = Language.ShenJiEquip.ARMOR_SUBTYPE_NAME3
    -- else
    --     --SysMsgWGCtrl.Instance:ErrorRemind("暗器")
    --     self.node_list["an_txt_11"].text.text = Language.ShenJiEquip.WEAPON_SUBTYPE_NAME1
    --     self.node_list["hl_txt_11"].text.text = Language.ShenJiEquip.WEAPON_SUBTYPE_NAME1
    --     self.node_list["an_txt_22"].text.text = Language.ShenJiEquip.WEAPON_SUBTYPE_NAME2
    --     self.node_list["hl_txt_22"].text.text = Language.ShenJiEquip.WEAPON_SUBTYPE_NAME2
    --     self.node_list["an_txt_33"].text.text = Language.ShenJiEquip.WEAPON_SUBTYPE_NAME3
    --     self.node_list["hl_txt_33"].text.text = Language.ShenJiEquip.WEAPON_SUBTYPE_NAME3
    -- end

    for i = 1, 6 do
        local text_str
        if i > 3 then
            local weapon_type2_index = i - 3
            text_str = Language.ShenJiEquip["ARMOR_SUBTYPE_NAME" .. weapon_type2_index]
        else
            text_str = Language.ShenJiEquip["WEAPON_SUBTYPE_NAME" .. i]
        end
        self.node_list["an_txt_" .. i .. i].text.text = text_str
    end
end

-- 200714:新增优化被当做材料的，需要在背包有个勾选
function HiddenWeaponView:UpdateGridSelect()
    if self.curr_datas == nil then
        return
    end
    -- 获取已选择的索引
    local selected_indexs = self:GetSelectedMaterial()
    local map_selected = {}
    if selected_indexs then
        for index, value in ipairs(selected_indexs) do
            map_selected[value] = true
        end
    end
    -- 设置选择状态
    local is_status_change = false
    for index, data in ipairs(self.curr_datas) do
        local is_material_pre = data.is_material or false
        if data.index and map_selected[data.index] == true then
            data.is_material = true
        else
            data.is_material = false
        end
        if is_material_pre ~= data.is_material then
            is_status_change = true
        --self.bag_grid:UpdateOneCell(index)
        end
    end
    if is_status_change and self.bag_grid then
        local cells = self.bag_grid:GetAllCell()
        -- 直接 SetDataList 或 RefreshActiveCell 会有轻度掉帧。尝试获取cell调用单一方法
        for index, value in pairs(cells) do
            if value and value.FlushMaterialCheck then
                value:FlushMaterialCheck()
            end
        end
    end
end

function HiddenWeaponView:RefreshBuildBtnState()
    self:UpdateGridSelect()
    local data = self.current_data
    if not data.can_compose then
        self.node_list["remind_build_sub"]:SetActive(false)
        return
    end

    if self.node_list["btn_build"].button.interactable == false then
        self.node_list["remind_build_sub"]:SetActive(false)
        return
    end
    local is_material_full = false
    for index, render in ipairs(self.render_materials_same) do
        if render:GetActive() and render:GetSelect() == nil then
            self.node_list["remind_build_sub"]:SetActive(false)
            return
        end
    end
    for index, render in ipairs(self.render_materials_node_type) do
        if render:GetActive() and render:GetSelect() == nil then
            self.node_list["remind_build_sub"]:SetActive(false)
            return
        end
    end
    for index, render in ipairs(self.render_materials_diff) do
        if render:GetActive() and render:GetSelect() == nil then
            self.node_list["remind_build_sub"]:SetActive(false)
            return
        end
    end
    self.node_list["remind_build_sub"]:SetActive(true)
end

function HiddenWeaponView:InitBuildBagPosition()
    self.node_list.tabbar_begin.rect.anchoredPosition = left_cell_start_pos
    self.node_list.shenji_wudao_bag_root.rect.anchoredPosition = detail_bag_start_pos
    self.node_list.btn_build_bag_close:CustomSetActive(false)
end

function HiddenWeaponView:OnClickBuildBag()
    self:DoBuildBagTween()
end

function HiddenWeaponView:DoBuildBagTween(is_show_bag)
    -- 显示背包 切换成  cell
    if is_show_bag then
        self.node_list.tabbar_begin.rect:DOAnchorPos(left_cell_start_pos, 1)
        if self.node_list.shenji_wudao_bag_root then
            self.node_list.shenji_wudao_bag_root.rect:DOAnchorPos(detail_bag_start_pos, 1)
        end
    else
        self.node_list.tabbar_begin.rect:DOAnchorPos(left_cell_end_pos, 1)
        if self.node_list.shenji_wudao_bag_root then
            self.node_list.shenji_wudao_bag_root.rect:DOAnchorPos(detail_bag_end_pos, 1)
        end
    end

    -- self.node_list.btn_build_bag_close:CustomSetActive(self.detail_show_bag)
    -- self.node_list.btn_bag_text.text.text = self.detail_show_bag and "返回" or "背包"
end

function HiddenWeaponView:OnClickWuDaoSelectBtn()
    local active = self.node_list.part_wudao:GetActive()
    self.node_list.part_wudao:CustomSetActive(not active)
    self.node_list["filter_select"]:CustomSetActive(not active)
end

function HiddenWeaponView:OnClickWuDaoBackSelect()
    self.node_list.part_wudao:CustomSetActive(false)
    self.node_list.filter_select:CustomSetActive(false)
end


----------------------------展示技能
HWBuildSkillRender = HWBuildSkillRender or BaseClass(BaseRender)

function HWBuildSkillRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.show_tips_btn, BindTool.Bind(self.ShowTip, self))
end

function HWBuildSkillRender:ShowTip()
    HiddenWeaponWGCtrl.Instance:ShowEquipSkillView(self.data)
end

function HWBuildSkillRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local cfg = self.data.cfg
    local is_active = self.data.is_active
    local bundle, asset = ResPath.GetSkillIconById(cfg.skill_icon)
    self.node_list["btn_icon"].image:LoadSprite(bundle, asset, function()
        self.node_list["btn_icon"].image:SetNativeSize()
    end)
    self.node_list["txt_level"].text.text = string.format(Language.Common.LevelNormal, cfg.skill_level)
    self.node_list["level_bg"]:SetActive(is_active)
    XUI.SetGraphicGrey(self.node_list["btn_icon"], not is_active and not self.data.can_active)
end

---------------------------展示发生变化技能

HWChangeSkillRender = HWChangeSkillRender or BaseClass(BaseRender)

function HWChangeSkillRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local cfg = self.data.cfg
    local is_active = self.data.is_active

    local bundle, asset = ResPath.GetSkillIconById(cfg.skill_icon)
    self.node_list["cur_skill_icon"].image:LoadSprite(bundle, asset, function()
        self.node_list["cur_skill_icon"].image:SetNativeSize()
        XUI.SetGraphicGrey(self.node_list["cur_skill_icon"], not is_active)
    end)

    self.node_list["next_skill_icon"].image:LoadSprite(bundle, asset, function()
        self.node_list["next_skill_icon"].image:SetNativeSize()
    end)

    local skill_type_str = Language.ShenJiEquip.SkillType1
    if self.data.skill_type == HW_CONST_PARAM.PASSIVE_SKILL_TYPE then
        skill_type_str = Language.ShenJiEquip.SkillType2
    end

    self.node_list.skill_type_txt.text.text = skill_type_str
    self.node_list.level_bg:SetActive(not self.data.can_active)
    self.node_list.cur_skill_text.text.text = string.format(Language.Common.LevelNormal, cfg.skill_level)

    local next_level = self.data.can_active and 1 or cfg.skill_level + 1
    self.node_list.next_skill_text.text.text = string.format(Language.Common.LevelNormal, next_level)
end
