require("game/dragon_temple/dragon_temple_view")
require("game/dragon_temple/dragon_temple_wg_data")
require("game/dragon_temple/dragon_temple_longshen_view")
require("game/dragon_temple/dragon_temple_equip_view")
require("game/dragon_temple/dragon_temple_equip_bag_view")
require("game/dragon_temple/dragon_temple_item_tip")
require("game/dragon_temple/dragon_temple_hatch_view")
require("game/dragon_temple/dragon_temple_rank_view")
require("game/dragon_temple/dragon_temple_mibao_view")
require("game/dragon_temple/dragon_temple_mibao_showreward_view")
require("game/dragon_temple/dragon_temple_hatch_privilege_view")
require("game/dragon_temple/dragon_temple_up_grade_view")
require("game/dragon_temple/dragon_temple_rank_info_view")
require("game/dragon_temple/dragon_temple_longshen_level_buy_view")
require("game/dragon_temple/dragon_temple_wild_buy_view")

DragonTempleWGCtrl = DragonTempleWGCtrl or BaseClass(BaseWGCtrl)

function DragonTempleWGCtrl:__init()
	if DragonTempleWGCtrl.Instance ~= nil then
		ErrorLog("[DragonTempleWGCtrl] attempt to create singleton twice!")
		return
	end

	DragonTempleWGCtrl.Instance = self
	self.view = DragonTempleView.New(GuideModuleName.DragonTempleView)
	self.data = DragonTempleWGData.New()
	self.equip_bag_view = DragonTempleEquipBag.New()
	self.equip_show_tip = DragonTempleItemTip.New()
	self.mibao_showreward_view = DragonTempleMiBaoShowRewardView.New()
	self.hatch_privilege_view = DragonTempleHatchPrivilegeView.New(GuideModuleName.DragonTemplePrivilegeView)
	self.upgrade_view = DragonTempleUpGradeView.New(GuideModuleName.DragonTempleUpgradeView)
	self.rank_info_view = DragonTempleRankInfoView.New()
	self.longshen_level_buy_view = DragonTempleLongShenLevelBuyView.New(GuideModuleName.DragonTempleLevelBuyView)
	self.dragon_wild_buy_view = DragonTempleWildBuyView.New(GuideModuleName.DragonTempleWildBuyView)

	self:RegisterAllProtocols()
	
	self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)

	self.role_data_change_callback = BindTool.Bind1(self.OnRoleDataChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback, {"gold", "level"})

	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
	self.uplevel_timer = {}
end

function DragonTempleWGCtrl:__delete()
	DragonTempleWGCtrl.Instance = nil

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.equip_bag_view then
		self.equip_bag_view:DeleteMe()
		self.equip_bag_view = nil
	end

	if self.equip_show_tip then
		self.equip_show_tip:DeleteMe()
		self.equip_show_tip = nil
	end

	if self.rank_info_view then
		self.rank_info_view:DeleteMe()
		self.rank_info_view = nil
	end

	if self.longshen_level_buy_view then
		self.longshen_level_buy_view:DeleteMe()
		self.longshen_level_buy_view = nil
	end

	if self.mibao_showreward_view then
		self.mibao_showreward_view:DeleteMe()
		self.mibao_showreward_view = nil
	end

	if self.hatch_privilege_view then
		self.hatch_privilege_view:DeleteMe()
		self.hatch_privilege_view = nil
	end

	if self.upgrade_view then
		self.upgrade_view:DeleteMe()
		self.upgrade_view = nil
	end

	if self.role_data_change_callback then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)
	end

	if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
	end

	self.old_longhun = nil
	self:CancelAllUplevelTime()
end

function DragonTempleWGCtrl:RegisterAllProtocols()	
	self:RegisterProtocol(CSDragonTempleOperate)
	self:RegisterProtocol(SCDragonTempleBagInfo,"OnSCDragonTempleBagInfo")
	self:RegisterProtocol(SCDragonTempleBagChangeInfo,"OnSCDragonTempleBagChangeInfo")
	self:RegisterProtocol(SCDragonTempleInfo,"OnSCDragonTempleInfo")
	self:RegisterProtocol(SCDragonTempleSoltInfo, "OnSCDragonTempleSoltInfo")
	self:RegisterProtocol(SCDragonTempleSoltUpdate, "OnSCDragonTempleSoltUpdate")
	self:RegisterProtocol(SCDragonTempleHatchInfo, "OnSCDragonTempleHatchInfo")
	self:RegisterProtocol(SCDragonTempleDrawRecordInfo, "OnSCDragonTempleDrawRecordInfo")
	self:RegisterProtocol(SCDragonTempleDrawRecordAdd, "OnSCDragonTempleDrawRecordAdd")
	self:RegisterProtocol(SCDragonTempleRankInfo, "OnSCDragonTempleRankInfo")
	self:RegisterProtocol(SCDragonTempleDonateInfo, "OnSCDragonTempleDonateInfo")
	self:RegisterProtocol(SCDragonTempleHatchUpdate, "OnSCDragonTempleHatchUpdate")
	self:RegisterProtocol(SCDragonTempleDanUpdate, "OnSCDragonTempleDanUpdate")
end

-- 通用请求请求操作
function DragonTempleWGCtrl:SendCSDragonTempleRequest(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSDragonTempleOperate)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function DragonTempleWGCtrl:OnSCDragonTempleBagInfo(protocol)
	--print_error("=======背包信息======", protocol)
	self.data:SetDragonTempleBagInfo(protocol)
	RemindManager.Instance:Fire(RemindName.DragonTemple_Equip)
end

function DragonTempleWGCtrl:OnSCDragonTempleBagChangeInfo(protocol)
	--print_error("=======背包改变信息======", protocol)
	local new_data = protocol.change_info
	local index = new_data.index
	local old_data = self.data:GetDragonTempleBagInfoByIndex(index)
	local old_num = 0
	if not IsEmptyTable(old_data) then
		old_num = old_data.num
	end

	if not IsEmptyTable(new_data) and new_data.item_id > 0 and new_data.num > old_num then
		local name = ItemWGData.Instance:GetItemNameDarkColor(new_data.item_id)
		local num = new_data.num
		num = new_data.num - old_num
		local str = string.format(Language.Bag.GetItemTxt, ToColorStr(name, ITEM_TIP_D_COLOR[new_data.color]), num)
        SysMsgWGCtrl.Instance:ErrorRemind(str)
	end

	self.data:SetDragonTempleBagChangeInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_equip)

	RemindManager.Instance:Fire(RemindName.DragonTemple_Equip)
end

function DragonTempleWGCtrl:OnSCDragonTempleInfo(protocol)
	-- print_error("=======所有信息======", protocol)
	if self.old_longhun then
		local str = ""
		if self.old_longhun > protocol.longhun then
			str = string.format(Language.DragonTemple.LoseLongHunTxt, self.old_longhun - protocol.longhun)
		elseif self.old_longhun < protocol.longhun then
			str = string.format(Language.DragonTemple.GetLongHunTxt, protocol.longhun - self.old_longhun)
		end

		if str ~= "" then
			SysMsgWGCtrl.Instance:ErrorRemind(str)
		end
	end

	local old_privilege_state = self.data:IsHatchHasPrivilege()
	local old_longhun_level = self.data:GetLongShenLevel()
	self.data:SetDragonTempleAllInfo(protocol)
	local new_privilege_state = self.data:IsHatchHasPrivilege()

	if old_privilege_state ~= new_privilege_state or old_longhun_level ~= protocol.level then
		self.data:CalculationHatchAddExpList()
		self:AddAllUplevelDelayTimer()
		ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_hatch)
	end

	ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_longhun)
	ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_equip)
	ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_draw)

	if self.equip_show_tip:IsOpen() then
		self.equip_show_tip:Flush()
	end

	if self.hatch_privilege_view:IsOpen() then
		self.hatch_privilege_view:Flush()
	end

	if self.dragon_wild_buy_view:IsOpen() then
		self.dragon_wild_buy_view:Flush()
	end

	if self.longshen_level_buy_view:IsOpen() then
        self.longshen_level_buy_view:Flush()
    end

    PrivilegedGuidanceWGCtrl.Instance:FlushGuidanceView()
	ViewManager.Instance:FlushView(GuideModuleName.CangJinExchangeView, TabIndex.cangjin_exchange_privilege)
    RemindManager.Instance:Fire(RemindName.PrivilegedGuidance)
	RemindManager.Instance:Fire(RemindName.DragonTemple_LongShen)
	RemindManager.Instance:Fire(RemindName.DragonTemple_Equip)
	RemindManager.Instance:Fire(RemindName.DragonTemple_Mibao)
	-- MainuiWGCtrl.Instance:FlushView(0, "wild_buy_tip")

	self.old_longhun = protocol.longhun
end

function DragonTempleWGCtrl:OnSCDragonTempleSoltInfo(protocol)
	-- print_error("=======槽位信息======", protocol)
	self.data:SetAllSoltInfo(protocol)
end

function DragonTempleWGCtrl:OnSCDragonTempleSoltUpdate(protocol)
	-- print_error("=======单个槽位信息======", protocol)
	self.data:SetSingleSoltInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_equip)
	if self.equip_show_tip:IsOpen() then
		self.equip_show_tip:Flush()
	end

	RemindManager.Instance:Fire(RemindName.DragonTemple_Equip)
end

function DragonTempleWGCtrl:OnSCDragonTempleHatchInfo(protocol)
	-- print_error("=======孵化信息======", protocol)
	self.data:SetHatchItemListInfo(protocol)
	self:AddAllUplevelDelayTimer()
	RemindManager.Instance:Fire(RemindName.DragonTemple_Hatch)
	ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_hatch)

	ViewManager.Instance:FlushView(GuideModuleName.NewFightMountView)
	RemindManager.Instance:Fire(RemindName.NewFightMount)
end

function DragonTempleWGCtrl:OnSCDragonTempleHatchUpdate(protocol)
	-- print_error("=======孵化信息更新======", protocol)
	local is_hatch, level_change, level_breath = self.data:UpdateHatchItemListInfo(protocol)
	self:AddUplevelDelayTimer(protocol.seq)
	RemindManager.Instance:Fire(RemindName.DragonTemple_Hatch)

	local is_load = (self.view.show_index == TabIndex.dragon_temp_hatch) and self.view:IsLoadedIndex(TabIndex.dragon_temp_hatch)
	if is_load then
		if is_hatch then
			self.view:PlayUseEffect(UIEffectName.s_tupo)
			self.view:PlayHatchEffect()
		elseif level_change and not is_hatch and not level_breath then
			local auto_uplevel = self.view:IsHatchAutoUpLevelNow()

			if not auto_uplevel then
				self.view:PlayUseEffect(UIEffectName.s_shengji)
			end
		elseif level_breath then
			self.view:PlayUseEffect(UIEffectName.s_tupo)
		end
	
		ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_hatch)
	end

	ViewManager.Instance:FlushView(GuideModuleName.NewFightMountView)
	RemindManager.Instance:Fire(RemindName.NewFightMount)
end


function DragonTempleWGCtrl:OnSCDragonTempleDanUpdate(protocol)
	--print_error("=======丹药信息变更======", protocol)
	RemindManager.Instance:Fire(RemindName.DragonTemple_Hatch)
	self.data:SetSingleDanUpdateInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_hatch)
end

function DragonTempleWGCtrl:OnSCDragonTempleDrawRecordInfo(protocol)
	-- print_error("=======抽奖信息======", protocol)
	self.data:SetDragonTempleDrawRecordInfo(protocol)
	if protocol.reocrd_type == DRAGON_TEMPLE_OPERATE_DRAW_TYPE.PERSON then
		ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_draw, "person_draw")
	else
		ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_draw, "server_draw")
	end
end

function DragonTempleWGCtrl:OnSCDragonTempleDrawRecordAdd(protocol)
	-- print_error("=======单个抽奖信息======", protocol)
	self.data:DragonTempleDrawRecordAdd(protocol)
	if protocol.reocrd_type == DRAGON_TEMPLE_OPERATE_DRAW_TYPE.PERSON then
		ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_draw, "person_draw")
	else
		ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_draw, "server_draw")
	end
end

function DragonTempleWGCtrl:OnSCDragonTempleRankInfo(protocol)
	-- print_error("=======排行信息======", protocol)
	self.data:SetDragonTempleRankInfo(protocol)
	if protocol.rank_type == DRAGON_TEMPLE_OPERATE_RANK_TYPE.LONGHUN then
		ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_rank, "flush_rank_longhun")
	else
		ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_rank, "flush_rank_contribute")
	end
end

function DragonTempleWGCtrl:OnSCDragonTempleDonateInfo(protocol)
	-- print_error("=======排行奖励信息======", protocol)
	local old_donate_time = self.data:GetDailyDonateTime()
	local new_donate_time = protocol.daily_donate_times
	if old_donate_time >= 0 and new_donate_time > 0 and (new_donate_time - old_donate_time == 1) and self.view:IsLoadedIndex(TabIndex.dragon_temp_rank) then
		self.view:PlayRankDonateSucEffect()
	end

	self.data:SetDragonTempleDonateInfo(protocol)
	RemindManager.Instance:Fire(RemindName.DragonTemple_Rank)
	ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_rank, "donate_reward")
end

function DragonTempleWGCtrl:UseDanReq(item_id)
	local dan_cfg = self.data:GetHatchPelletCostCfg(item_id)

	if not IsEmptyTable(dan_cfg) then
		local role_level = RoleWGData.Instance:GetAttr('level')
		if role_level < dan_cfg.role_level then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.DragonTemple.KeyUseLevelLimit)
			return
		end

		local used_num =  self.data:GetHatchPelletUseNum(dan_cfg.type, dan_cfg.index)
		local had_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
		local remain_num = dan_cfg.use_limit_num - used_num
		if remain_num > 0 then
			local show_num = had_num > remain_num and remain_num or had_num
			DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.HANTCH_DAN_UP, dan_cfg.type, dan_cfg.index, show_num)
		else
			ViewManager.Instance:Open(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_hatch)
		end
	end
end

--背包界面
function DragonTempleWGCtrl:OpenEquipBagView()
	if self.equip_bag_view:IsOpen() then
        self.equip_bag_view:Flush()
    else
    	self.equip_bag_view:Open()
    end
end

--打开升级界面
function DragonTempleWGCtrl:OpenDragonTempleUpGradeView()
    self.upgrade_view:Open()
end

--打开等级直购界面
function DragonTempleWGCtrl:OpenDragonTempleLevelBuyView()
    self.longshen_level_buy_view:Open()
end

--打开tips
function DragonTempleWGCtrl:OpenDragonTempleEquipTip(item_id, from_view)
	if not item_id then
		return
	end

	local equip_cfg = self.data:GetEquipCfgById(item_id)
	if IsEmptyTable(equip_cfg) then
		return
	end

	local data_list = {
		item_id = item_id,
		from_view = from_view,
		solt = equip_cfg.solt,
		type = equip_cfg.type
	}

	self.equip_show_tip:SetDataAndOpen(data_list)
end

function DragonTempleWGCtrl:OnDragonTempleOperateResult(result, operate_type, info)
	if result == 1 then
		if operate_type == DRAGON_TEMPLE_OPERATE_TYPE.LEVEL_UP and self.view:IsOpen() then
			self.view:PlayUseEffect(UIEffectName.s_shengji)
			self.view:PlayLongShenSucEffcet()
		elseif operate_type == DRAGON_TEMPLE_OPERATE_TYPE.SOLT_ACTIVE and self.view:IsOpen() then
			self.view:PlayUseEffect(UIEffectName.s_jihuo)
		elseif operate_type == DRAGON_TEMPLE_OPERATE_TYPE.SOLT_LEVEL_UP and self.view:IsOpen() then
			self.view:PlayUseEffect(UIEffectName.s_shengji)
		elseif operate_type == DRAGON_TEMPLE_OPERATE_TYPE.SOLT_GRADE_UP and self.view:IsOpen() then
			self.view:PlayUseEffect(UIEffectName.s_shengjie)
		elseif operate_type == DRAGON_TEMPLE_OPERATE_TYPE.SUIT_LEVEL_UP and self.view:IsOpen() then
			self.view:PlayUseEffect(UIEffectName.s_jihuo)
		end
	end
end

function DragonTempleWGCtrl:OnDragonTemplePurchaseResult(result, level)
	if result == 1 and self.view:IsOpen() then
		self.view:PlayUseEffect(UIEffectName.s_shengji)
		self.view:PlayLongShenSucEffcet()
	end
end

function DragonTempleWGCtrl:OnMountLevelResult(result, seq, level)
	if result == 1 then
		if self.view:IsOpen() then
			self.view:PlayUseEffect(UIEffectName.s_tupo)
			if level == 1 then
				NewFightMountWGCtrl.Instance:OpenGetNewView(seq)
			end
		end
	end
end

--------------------------------------
function DragonTempleWGCtrl:OpenLongHunRankInfoView()
	if self.rank_info_view and not self.rank_info_view:IsOpen() then
		self.rank_info_view:Open()
	end
end

function DragonTempleWGCtrl:OpenMiBaoShowRewardView()
	if self.mibao_showreward_view and not self.mibao_showreward_view:IsOpen() then
		self.mibao_showreward_view:Open()
	end
end

----------------------------------------------------------------
function DragonTempleWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	  (change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		if self.data:CheckIsHatchItem(change_item_id) or NewFightMountWGData.Instance:GetIsUpgradeCostCfg(change_item_id) 
			or self.data:GetIsHatchPelletCostCfg(change_item_id) then
			RemindManager.Instance:Fire(RemindName.DragonTemple_Hatch)
			ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_hatch, "hatch_item_change")
		end
	end
end

function DragonTempleWGCtrl:OnRoleDataChange(attr_name, value, old_value)
    if attr_name == "gold" then
		if self.data:IsHatchBreath() then
			RemindManager.Instance:Fire(RemindName.DragonTemple_Hatch)
			ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_hatch, "hatch_gold_change")
		end
	elseif attr_name == "level" then
		local show_info = self.data:GetShowWildBuyList()
		for k, v in pairs(show_info) do
            if old_value < v.open_level and value >= v.open_level then
				if self.dragon_wild_buy_view:IsOpen() then
					self.dragon_wild_buy_view:Flush()
				end

				ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_longhun)
                return
            end
        end
	end
end

function DragonTempleWGCtrl:OnPassDay()
	if self.dragon_wild_buy_view:IsOpen() then
		self.dragon_wild_buy_view:Flush()
	end

	ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_longhun)
end

--------------------------------孵化时间升级--------------------------------
function DragonTempleWGCtrl:CancelAllUplevelTime()
    for i = 0, 4 do
		self:CancelUplevelTimeBySeq(i)
	end	
end

function DragonTempleWGCtrl:CancelUplevelTimeBySeq(seq)
	if nil ~= self.uplevel_timer[seq] then
		GlobalTimerQuest:CancelQuest(self.uplevel_timer[seq])
		self.uplevel_timer[seq] = nil
	end
end

function DragonTempleWGCtrl:AddAllUplevelDelayTimer()
    for i = 0, 4 do
		self:AddUplevelDelayTimer(i)
	end	
end

function DragonTempleWGCtrl:AddUplevelDelayTimer(seq)
	local time = TimeWGCtrl.Instance:GetServerTime()
	local data = self.data:GetHatchAutoUplevelInfo(seq)
	if IsEmptyTable(data) then
		return
	end

	local cd_time = data.up_level_cd_time - time
	
	if self.data:CanAddExpTimer(seq) and cd_time > 0 then
		self:CancelUplevelTimeBySeq(seq)

		self.uplevel_timer[seq] = GlobalTimerQuest:AddDelayTimer(function()
			RemindManager.Instance:Fire(RemindName.DragonTemple_Hatch)
			ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_hatch)
		end, cd_time)
	else
		self:CancelUplevelTimeBySeq(seq)
	end
end

function DragonTempleWGCtrl:OpenHatchPrivilegeView()
	if self.hatch_privilege_view and not self.hatch_privilege_view:IsOpen() then
		self.hatch_privilege_view:Open()
	end
end

function DragonTempleWGCtrl:OpenDragonWildBuyView()
	if self.dragon_wild_buy_view and not self.dragon_wild_buy_view:IsOpen() then
		self.dragon_wild_buy_view:Open()
	end
end