-- 弹幕
BarrageView = BarrageView or BaseClass(SafeBaseView)

local anim_time = 30
local track_amount = 10

function BarrageView:__init()
	self.open_tween = nil
	self.close_tween = nil
	self.active_close = false
	self.blocks_raycasts = false
	self.view_layer = UiLayer.MainUIHigh
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self:SetMaskBg(false, false)
	self.wait_show_barrage = {}
	self:AddViewResource(0, "uis/view/duck_race_ui_prefab", "duck_race_barrage_layout")
	self.first_index = 0 	-- 队列第一个元素的index
	self.last_index = 0 	-- 队列最后一个元素的下一个元素index
	self.cur_anim_count = 0
end

function BarrageView:__delete()

end

function BarrageView:OpenCallBack()
end

function BarrageView:CloseCallBack()
	self.wait_show_barrage = {}
end

-- select_type:选择类型（对应枚举BarrageView.SELECT_TYPE）, select_callback:选中后回调
function BarrageView:AddBarrageText(barrage_data)
	self.wait_show_barrage[self.last_index] = barrage_data
	self.last_index = self.last_index + 1
	if not self:IsOpen() then
		self:Open()
	end
	if self.show_timer == nil then
		self:StartTimer()
	end
end

function BarrageView:LoadCallBack()
	self.cur_anim_count = 0
	self:StartTimer()

	self.barrage_item_pool = {}

	self.track_last_show_time_list = {}
	for i = 1, track_amount do
		self.track_last_show_time_list[i] = 0
	end
	self.node_list["barrage_text_prefab"]:SetActive(false)
	self.scene_change_complete = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind(self.OnSceneChangeComplete, self))
end

function BarrageView:ReleaseCallBack()
	self:CancelTimer()
	if self.duck_head_list then
		for k,v in pairs(self.duck_head_list) do
			v:DeleteMe()
		end
	end
	self.duck_head_list = nil

	if self.scene_change_complete then
		GlobalEventSystem:UnBind(self.scene_change_complete)
		self.scene_change_complete = nil
	end

	for k,v in ipairs(self.barrage_item_pool) do
		v:DeleteMe()
	end
	self.barrage_item_pool = {}

	self.track_last_show_time_list = nil
end

function BarrageView:StartTimer()
	self:CancelTimer()
	self.show_timer = GlobalTimerQuest:AddRunQuest(function()
		self:ShowSingleBarrage()
	end, 0)
end

function BarrageView:CancelTimer()
	if self.show_timer then
		GlobalTimerQuest:CancelQuest(self.show_timer)
		self.show_timer = nil
	end
end

function BarrageView:ShowSingleBarrage()
	if not self.node_list["barrage_text_prefab"] then
		return
	end

	local barrage_data = self.wait_show_barrage[self.first_index]
	if not barrage_data then
		self:CancelTimer()
		return
	end

	local free_track = self:GetFreeTrack(barrage_data)
	if not free_track then
		return
	end
	
	self.wait_show_barrage[self.first_index] = nil
	self.first_index = self.first_index + 1
	local barrage_item = self.barrage_item_pool[#self.barrage_item_pool]
	if not barrage_item then
		local obj = ResMgr:Instantiate(self.node_list["barrage_text_prefab"].gameObject)
		obj:SetActive(true)
		barrage_item = BarrageItem.New(obj)
	else
		self.barrage_item_pool[#self.barrage_item_pool] = nil
	end

	barrage_item.view.transform:SetParent(free_track.transform, false)
	barrage_item.view.rect.anchoredPosition = Vector2(750, 0)
	barrage_item:SetData(barrage_data)
	self.cur_anim_count = self.cur_anim_count + 1
	barrage_item.view.rect:DOAnchorPos(Vector2(-2000, 0), anim_time):SetEase(DG.Tweening.Ease.Linear):OnComplete(function()
		barrage_item:Reset()
		self.barrage_item_pool[#self.barrage_item_pool + 1] = barrage_item
		self.cur_anim_count = self.cur_anim_count - 1
		self:CheckNeedClose()
	end)
end

-- 随机得到一个空闲的轨道
function BarrageView:GetFreeTrack(barrage_data)
	local free_track_index_list = {}
	for i = 1, track_amount do
		if TimeWGCtrl.Instance:GetServerTime() - self.track_last_show_time_list[i] > 0 then
			table.insert(free_track_index_list, i)
		end
	end

	if IsEmptyTable(free_track_index_list) then
		return nil
	end

	local random_key = math.random(1, #free_track_index_list)
	local free_track_index = free_track_index_list[random_key]
	self.track_last_show_time_list[free_track_index] = TimeWGCtrl.Instance:GetServerTime() + anim_time / 140 / 3 * (#barrage_data.text + 15)
	return self.node_list["barrage_track_" .. free_track_index], free_track_index
end

function BarrageView:OnFlush()
end

-- 检查是否需要关闭面板
function BarrageView:CheckNeedClose()
	local barrage_data = self.wait_show_barrage[self.first_index]
	if not barrage_data and self.cur_anim_count <= 0 then
		self.first_index = 0
		self.last_index = 0
		self:Close()
	end
end

function BarrageView:OnSceneChangeComplete()
	self.wait_show_barrage = {}
	self.cur_anim_count = 0
	self:CheckNeedClose()
end
------------------------------------------------------------------
-- 单个弹幕
BarrageItem = BarrageItem or BaseClass(BaseGridRender)
function BarrageItem:__init()
	self:Reset()
end

function BarrageItem:__delete()

end

function BarrageItem:OnFlush()
	self.node_list["normal_text"]:SetActive(not self.data.is_emoji_text)
	self.node_list["text"]:SetActive(self.data.is_emoji_text)
	local color = COLOR3B.WHITE
	local format = "%s"
	-- 是否是自己发的弹幕
	if self.data.is_self_barrage then
		color = COLOR3B.D_GREEN
		format = Language.Common.Me .. format
	end
	local color_text = ToColorStr(string.format(format, self.data.text), color)
	if self.data.is_emoji_text then
		EmojiTextUtil.ParseRichText(self.node_list["text"].tmp, color_text, 24, color)--, nil, nil, nil, RICH_CONTENT_TYPE.CHAT_WINDOW)
	else
		self.node_list["normal_text"].tmp.text = color_text
	end
end

function BarrageItem:Reset()
	self.node_list["text"].tmp.text = ""
end