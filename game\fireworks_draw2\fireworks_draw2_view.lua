FireWorksDrawSecondView = FireWorksDrawSecondView or BaseClass(SafeBaseView)

function FireWorksDrawSecondView:__init()
    self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/fireworks_draw2_ui_prefab", "layout_fireworks_draw2_view")
end

function FireWorksDrawSecondView:LoadCallBack()
    for i = 1, 2 do
        self.node_list["fireworks2_btn_draw_" .. i].button:AddClickListener(BindTool.Bind2(self.OnClickRecord, self, i))
        self.node_list["fireworks2_btn_icon_" .. i].button:AddClickListener(BindTool.Bind(self.ShowDrawItemTips, self))
    end
    XUI.AddClickEventListener(self.node_list["fireworks2_btn_range"], BindTool.Bind1(self.OnClickGaiLvShow, self))

    self.fireworks_item_data_change = BindTool.Bind(self.OnDrawItemChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.fireworks_item_data_change)

    if nil == self.fireworks_show_cell then
        self.fireworks_show_cell = {}
        for i = 1, 9 do
            self.fireworks_show_cell[i] = ItemCell.New(self.node_list["show_item_" .. i])
        end
    end

end

function FireWorksDrawSecondView:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("fireworks_draw2_down") then
		CountDownManager.Instance:RemoveCountDown("fireworks_draw2_down")
	end

    if self.fireworks_item_data_change then
        ItemWGData.Instance:UnNotifyDataChangeCallBack(self.fireworks_item_data_change)
        self.fireworks_item_data_change = nil
    end

    if self.fireworks_show_cell then
        for k, v in pairs(self.fireworks_show_cell) do
            v:DeleteMe()
        end
        self.fireworks_show_cell = nil
    end

end

function FireWorksDrawSecondView:OpenCallBack()
    FireWorksDrawSecondWGCtrl.Instance:SendFireWokrsInfo(OA_FIREWORKS_DRAW2_OPERATE_TYPE.INFO)
end

function FireWorksDrawSecondView:OnFlush(param_t)
    for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushshowView()
		end
	end

    self:LoginTimeCountDown()
end

function FireWorksDrawSecondView:FlushshowView()
    self:FlushDrawBtnShow()

    local show_list = FireWorksDrawSecondWGData.Instance:GetFireWorksRewardShowCfg()
    --珍稀奖励展示
    if show_list then
        for i = 1, #show_list do
            self.fireworks_show_cell[i]:SetData(show_list[i].item)
        end
    end

    local baodi_times = FireWorksDrawSecondWGData.Instance:GetFireWorksBaoDiDrawTimes()
    local is_baodi = baodi_times == -1
    if baodi_times > 0 then
        self.node_list["baodi_times"].text.text = baodi_times
    end
    
    self.node_list["text_1"]:SetActive(not is_baodi)
    self.node_list["text_2"]:SetActive(not is_baodi)
    self.node_list["baodi_times"]:SetActive(not is_baodi)
    self.node_list["no_times_txt"]:SetActive(is_baodi)
end

--抽奖
function FireWorksDrawSecondView:OnClickRecord(draw_type) --抽奖
    local cfg = FireWorksDrawSecondWGData.Instance:GetFireWorksDrawConsumeCfg()
    cfg = cfg[draw_type]
    local num = ItemWGData.Instance:GetItemNumInBagById(cfg.cost_item_id)
    --检查道具数量
    if num >= cfg.cost_item_num then
        FireWorksDrawSecondWGData.Instance:CacheOrGetDrawIndex(draw_type)
        --发送协议
        FireWorksDrawSecondWGCtrl.Instance:SendFireWokrsInfo(
            OA_FIREWORKS_DRAW2_OPERATE_TYPE.DRAW,
            cfg.mode
        )
    else
        FireWorksDrawSecondWGCtrl.Instance:ClickUseDrawItem(draw_type, function ()
            self:OnClickDrawBuy(draw_type)
        end)
    end
end

function FireWorksDrawSecondView:OnClickGaiLvShow() --抽奖概率
    FireWorksDrawSecondWGCtrl.Instance:OpenGaiLvView()
end

function FireWorksDrawSecondView:FlushDrawBtnShow(is_flush_num) --刷新抽奖次数
    local cfg = FireWorksDrawSecondWGData.Instance:GetFireWorksDrawConsumeCfg()
    if cfg == nil then
        return
    end

    local item_cfg
    local mode_cfg = FireWorksDrawSecondWGData.Instance:GetFireWorksDrawItem()
    local item_id = mode_cfg.cost_item_id
    local count
    for i = 1, 2 do
        if cfg[i] then
            if not is_flush_num then
                item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
                if not IsEmptyTable(item_cfg) then
                    --道具图标
                    self.node_list["fireworks2_btn_icon_" .. i].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
                end
                --抽几次
                self.node_list["fireworks2_txt_buy_" .. i].text.text = string.format(Language.FireWorksDrawSecond.DrawBtnDesc, cfg[i].times)
                -- --折扣
                --local is_zhekou = cfg[i].count ~= cfg[i].cost_item_num
                --self.node_list["btn_discount_" .. i]:SetActive(is_zhekou)
                -- if is_zhekou then
                --     self.node_list["txt_lh_discount_" .. i].text.text = cfg[i].cost_item_num .. "折"
                -- end
            end
            --道具红点数量
            count = ItemWGData.Instance:GetItemNumInBagById(item_id)
            self.node_list["fireworks2_btn_red_" .. i]:SetActive(count >= cfg[i].cost_item_num)
            local color = count >= cfg[i].cost_item_num and COLOR3B.D_GREEN or COLOR3B.D_RED
            local left_str = ToColorStr(count, color) 
            self.node_list["fireworks2_btn_num_" .. i].text.text = left_str .."/".. cfg[i].cost_item_num
            self.node_list["fireworks2_btn_num_"..i].text.color = Str2C3b(color)

        end
    end
end

function FireWorksDrawSecondView:ShowDrawItemTips()
    local cfg = FireWorksDrawSecondWGData.Instance:GetFireWorksDrawItem()
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cfg.cost_item_id})
end

--点击购买
function FireWorksDrawSecondView:OnClickDrawBuy(draw_type)
    local cfg = FireWorksDrawSecondWGData.Instance:GetFireWorksDrawConsumeCfg()
    local item_cfg = FireWorksDrawSecondWGData.Instance:GetFireWorksDrawItem()
    local cur_cfg = cfg[draw_type]
    if cur_cfg == nil then
        return
    end

    local num = ItemWGData.Instance:GetItemNumInBagById(item_cfg.cost_item_id)
    local consume = item_cfg.cost_gold * (cur_cfg.cost_item_num - num)
	--检查仙玉
	local enough = RoleWGData.Instance:GetIsEnoughUseGold(consume)
	--足够购买，不足弹窗
    if enough then
        FireWorksDrawSecondWGData.Instance:CacheOrGetDrawIndex(draw_type)
		--发送协议
        FireWorksDrawSecondWGCtrl.Instance:SendFireWokrsInfo(
            OA_FIREWORKS_DRAW2_OPERATE_TYPE.DRAW,
            cur_cfg.mode
        )
	else
		VipWGCtrl.Instance:OpenTipNoGold()
	end
end

--物品监听刷新按钮显示
function FireWorksDrawSecondView:OnDrawItemChange(item_id)
    local check_list = FireWorksDrawSecondWGData.Instance:GetFireWorksItemDataChangeList()
    if check_list ~= nil then
        for i, v in pairs(check_list) do
            if v == item_id then
                self:FlushDrawBtnShow(true)
                return
            end
        end
    end
end

------------------------------------活动时间倒计时
function FireWorksDrawSecondView:LoginTimeCountDown()
 --    local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FIREWORKS_DRAW)
	-- if activity_data ~= nil then
	-- 	local invalid_time = activity_data.end_time
 --        if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
 --            self.node_list["fireworks_time_down"].text.text = TimeUtil.FormatSecondDHM(invalid_time - TimeWGCtrl.Instance:GetServerTime())
 --            CountDownManager.Instance:AddCountDown("fireworks_draw2_down", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
 --        end
	-- end
    local time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
    if time > 0 then
        if CountDownManager.Instance:HasCountDown("fireworks_draw2_down") then
            CountDownManager.Instance:RemoveCountDown("fireworks_draw2_down")
        end

        CountDownManager.Instance:AddCountDown("fireworks_draw2_down", 
            BindTool.Bind(self.UpdateCountDown, self), 
            BindTool.Bind(self.OnComplete, self), 
            nil, time, 1)
    else
        self:OnComplete()
    end
end

function FireWorksDrawSecondView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.login_act_date = TimeUtil.FormatUnixTime2Date()
		self.node_list["fireworks2_time_down"].text.text = TimeUtil.FormatSecondDHM8(valid_time)
	end
end

function FireWorksDrawSecondView:OnComplete()
    self.node_list["fireworks2_time_down"].text.text = ""
end
--------------------------------------------------------