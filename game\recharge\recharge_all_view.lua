RechargeView = RechargeView or BaseClass(SafeBaseView)

RechargeView.TABINDEX = {
	CZ = TabIndex.recharge_cz,                     -- 充值
	ZEROBUY = TabIndex.recharge_zerobuy,           -- 0元购买
	VIP = TabIndex.recharge_vip,                   -- VIP
	VTZ = TabIndex.recharge_storehouse,            -- VIP投资
	KTQ = TabIndex.recharge_king_vip,              -- 王者特权
	YCTZ = TabIndex.recharge_month_card,           -- 月卡投资
	ZFTQ = TabIndex.recharge_reserve_card,         -- 攒福特权
	TQTZ = TabIndex.recharge_tqtz,                 -- 投资特权
	WEEKBUY = TabIndex.recharge_week_buy,          --每周必买
	WEEKPRIVILEGE = TabIndex.recharge_week_privilege, -- 特权周卡
	LEICHONG = TabIndex.recharge_leichong, -- 累充
	XIAOFEI = TabIndex.recharge_xiaofei, -- 消费
	-- 下面的删除了
	SDR = TabIndex.recharge_sevenday,              -- 七天累计充值(开服累充)
	TZJH = TabIndex.recharge_tzjh,                 -- 投资计划
	ZZTZ = TabIndex.recharge_zztz,                 -- 至尊投资
	JBP = TabIndex.recharge_jbp,                   -- 聚宝盆
	BOSSTZ = TabIndex.recharge_bosstz,             -- Boss投资
}

function RechargeView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	local suit_tab_list = { TabIndex.recharge_month_card, TabIndex.recharge_week_privilege }
	
	self.default_index = TabIndex.recharge_cz
	self.hide_index = nil

	self:SetMaskBg(false, false)

	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(TabIndex.recharge_cz, "uis/view/recharge_ui_prefab", "layout_recharge")             -- 充值
	self:AddViewResource(TabIndex.recharge_zerobuy, "uis/view/recharge_ui_prefab", "layout_zero_buy_panel")  -- 0元购
	self:AddViewResource(TabIndex.recharge_vip, "uis/view/recharge_ui_prefab", "layout_vip_panel")           --VIP特权
	self:AddViewResource(TabIndex.recharge_king_vip, "uis/view/recharge_ui_prefab", "layout_recharge_king_vip") --王者特权
	self:AddViewResource(TabIndex.recharge_month_card, "uis/view/recharge_ui_prefab", "layout_vip_month_card") -- 扫荡月卡
	self:AddViewResource(TabIndex.recharge_month_card, "uis/view/recharge_ui_prefab", "layout_vip_storehouse") --特权月卡
	self:AddViewResource(TabIndex.recharge_week_card, "uis/view/recharge_ui_prefab", "layout_vip_week_card") -- 周卡特权
	self:AddViewResource(TabIndex.recharge_reserve_card, "uis/view/recharge_ui_prefab", "layout_reserve_card") -- 攒福特权
	self:AddViewResource(TabIndex.recharge_tqtz, "uis/view/recharge_ui_prefab", "layout_zhizun_plan")        --成长基金
	self:AddViewResource(TabIndex.recharge_leichong, "uis/view/recharge_ui_prefab", "everyday_recharge")        --累充
	self:AddViewResource(TabIndex.recharge_xiaofei, "uis/view/recharge_ui_prefab", "everyday_xiaofei_recharge_view") --消费

	self.open_source_view = "ButtonVIP"
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
	self:AddViewResource(0, "uis/view/recharge_ui_prefab", "VerticalTabbar")
	self:AddViewResource(0, "uis/view/recharge_ui_prefab", "HorizontalTabbar")
	self:AddViewResource(suit_tab_list, "uis/view/recharge_ui_prefab", "HorizontalTabbar")
end

function RechargeView:ReleaseCallBack()
	self.hide_index = nil
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end
	if self.item_data_event then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
		self.item_data_event = nil
	end
	self:DeleteRechargeView()
	self:DeleteVipPanel()
	self:DeleteZeroBuyPanel()
	self:DeleteMonthCardPanel()
	self:DeleteStorehousePanel()
	self:DeleteTouZiPlan()
	self:DeleteRechargeWeekBuyView()
	self:DeleteWeekCardPanel()
	self:DeleteReserveCardPanel()
	self:DeleteRechargeKingVipView()
	self:DeleteRechargeLeichongView()
	self:ReleseXFRechargeView()
end

function RechargeView:LoadCallBack()
	if not self.tabbar then
		local remind_tab = {
			{},
			{ RemindName.Vip_ZeroBuy },
			{ RemindName.Vip_Col },
			{ RemindName.Vip_King },
			{ RemindName.MonthCardProvolege, RemindName.Vip_Week, RemindName.Reserve_Zftq },
			{ RemindName.Vip_TQTZ },
			{ RemindName.DailyRecharge_LeiChong },
			{ RemindName.DailyRecharge_XiaoFei },
			-- {RemindName.Week_Buy},
		}
		self.tab_sub = { nil, nil, nil, nil, Language.Recharge.HorTableName, nil }
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:SetVerTabbarIconStr("a3_vip_yq_")
		self.tabbar:SetVerTabbarIconPath(ResPath.GetVipImage)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		self.tabbar:SetCreateVerCallBack(BindTool.Bind1(self.SetTabVisible, self))
		self.tabbar:Init(Language.Recharge.VerTableName, self.tab_sub, "uis/view/recharge_ui_prefab",
			"uis/view/recharge_ui_prefab", remind_tab)
		self.tabbar:JumpToVerPrecent(1)
		FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.Recharge, self.tabbar)
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
			show_cangjin_score = true,
			show_cash_point = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	if not self.item_data_event then
		self.item_data_event = BindTool.Bind1(self.OnItemDataChange, self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
	end

	local bundle, asset = ResPath.GetCommonButton("a3_ty_hretutn")
	self.node_list["btn_close_window_img"].image:LoadSprite(bundle, asset, function()
		self.node_list["btn_close_window_img"].image:SetNativeSize()
	end)
	self.node_list.title_view_name.text.color = Str2C3b('#FCF8DD')
end

function RechargeView:OpenCallBack()
	RemindManager.Instance:Fire(RemindName.Vip_VTZ)
	RemindManager.Instance:Fire(RemindName.Vip_Month)
	ServerActivityWGCtrl.Instance:SendEveryDayExpenseInfo()
end

function RechargeView:SetTabVisible()
	if nil == self.tabbar then
		return
	end
	-- 零元购
	self.tabbar:SetVerToggleVisble(TabIndex.recharge_zerobuy, RechargeWGData.Instance:HasVipZeroBuy())

	-- 每周必买开服天数判断
	local week_buy = false
	local open_day = RechargeWGData.Instance:GetWeekBuyOpenDay()
	local cur_open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local is_open = FunOpen.Instance:GetFunIsOpened(FunName.recharge_week_buy)
	if is_open and cur_open_server_day >= open_day then
		week_buy = true
	end
	self.tabbar:SetVerToggleVisble(TabIndex.recharge_week_buy, week_buy)

	-- 特权投资
	local is_finish_all = RechargeWGData.Instance:TouZiPlanIsAllFinish()
	local is_open = FunOpen.Instance:GetFunIsOpened('recharge_tqtz')
	self.tabbar:SetVerToggleVisble(TabIndex.recharge_tqtz, not is_finish_all and is_open)

	-- 月卡
	-- local recharge_store_card_open = RechargeWGData.Instance:GetTZCardOpenState(INVEST_CARD_TYPE.StorehouseCard)
	-- local recharge_month_card_open = RechargeWGData.Instance:GetTZCardOpenState(INVEST_CARD_TYPE.MonthCard)
	-- local week_card_open = recharge_store_card_open and recharge_month_card_open
	local recharge_week_privilege_open = RechargeWGData.Instance:GetWeekCardOpenState(1)

	--self.tabbar:SetVerToggleVisble(TabIndex.recharge_month_card, week_card_open)
	self.tabbar:SetVerToggleVisble(TabIndex.recharge_week_privilege, recharge_week_privilege_open)
end

function RechargeView:LoadIndexCallBack(index)
	if index == TabIndex.recharge_cz then
		self:InitRechargeView()
	elseif index == TabIndex.recharge_vip then
		self:InitVipPanel()
	elseif index == TabIndex.recharge_zerobuy then
		self:InitZeroBuyPanel()
	elseif index == TabIndex.recharge_month_card then
		self:InitMonthCardPanel()
		self:InitStorehousePanel()
	elseif index == TabIndex.recharge_tqtz then
		self:InitTouZiPlanPanel()
	elseif index == TabIndex.recharge_week_card then
		self:InitWeekCardPanel()
	elseif index == TabIndex.recharge_reserve_card then
		self:InitReserveCard()
	elseif index == TabIndex.recharge_king_vip then
		self:InitRechargeKingVipView()
	elseif index == TabIndex.recharge_leichong then
		self:InitRechargeLeiChongView()
	elseif index == TabIndex.recharge_xiaofei then
		self:InitXFRechargeView()
	end
end

function RechargeView:ShowIndexCallBack(index)
	self:SetTabVisible()

	local bg_res = "a3_vip_bg1"

	if index == TabIndex.recharge_cz then
		bg_res = "a3_vip_bg1"
		self:ShowRechargeView()
	elseif index == TabIndex.recharge_tqtz then
		bg_res = "a3_vip_czjj_bg"
		self:ShowTouZiPlanPanel()
	elseif index == TabIndex.recharge_week_card then
		bg_res = "a3_vip_bg1"
		self:ShowWeekCardIndexCallBack()
	elseif index == TabIndex.recharge_vip then
		bg_res = "a3_vip_bg1"
		self:PlayTequanModelAction()
	elseif index == TabIndex.recharge_month_card then
		bg_res = "a3_vip_tqk_bg1"
		self:ShowMonthCardIndexCallBack()
	elseif index == TabIndex.recharge_reserve_card then
		bg_res = "a3_vip_zftq_bg1"
	elseif index == TabIndex.recharge_king_vip then
		self:ShowRechargeKingVipView()
	elseif index == TabIndex.recharge_leichong then
		bg_res = "a3_mrlc_ty_bg"
		self:ViewAnimation()
	elseif index == TabIndex.recharge_xiaofei then
		bg_res = "a3_mrlc_ty_bg"
		self:ExpenseDoAnimation()
	end

	local bundle, asset = ResPath.GetRawImagesJPG(bg_res)
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

	if self.hide_index and self.hide_index ~= index then
		self:HideIndexCallBack(self.hide_index)
	end
	self.hide_index = index
end

function RechargeView:HideIndexCallBack(index)
	if index == TabIndex.recharge_cz then
		self:HideRechargeView()
	end
end

function RechargeView:CloseCallBack()
	-- AudioManager.Instance:PlayOpenCloseUiEffect()
	-- 如果打开查看面板 那么关闭本面板的时候顺便关闭查看面板
	-- if self.money_change_event then
	-- 	GlobalEventSystem:UnBind(self.money_change_event)
	-- 	self.money_change_event = nil
	-- end
end

function RechargeView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			if index == TabIndex.recharge_leichong then
				if v.link_cfg_key then
					self.def_rec_btn_index = tonumber(v.link_cfg_key)
				end
				self:FlushRechargeLeiChongView()
			elseif index == TabIndex.recharge_xiaofei then
				self:FlushXFRechargeView()
			end
		elseif k == "ser_info" then
			self:FlushRechargeLeiChongView()
			self:RefreshLingQuBtnState()
		end
	end

	if index == TabIndex.recharge_cz then
		self:OnFlushRechargeView(param_t)
	elseif index == TabIndex.recharge_vip then
		self:FlushVipPanel(param_t)
	elseif index == TabIndex.recharge_zerobuy then
		self:FlushZeroBuyPanel()
	elseif index == TabIndex.recharge_month_card then
		self:FlushMonthCardPanel()
		self:FlushStorehousePanel()
	elseif index == TabIndex.recharge_tqtz then
		self:FlushTouZiPlanPanel(param_t)
	elseif index == TabIndex.new_recharge_weekbuy then
		self:OnFlushRechargeWeekBuyView()
	elseif index == TabIndex.recharge_week_card then
		self:FlushWeekCardPanel()
	elseif index == TabIndex.recharge_reserve_card then
		self:FlushReserveCardPanel()
	elseif index == TabIndex.recharge_king_vip then
		self:FlushRechargeKingVipView()
	end

	self.node_list.title_view_name.text.text = Language.Recharge.RechargeTitle
end

function RechargeView:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	local index = self:GetShowIndex()
	if index == TabIndex.recharge_zerobuy then
		self:ZeroBuyUseCard(change_item_id, change_item_index, change_reason)
	end
end

-----------------------------------------------------------------------

VipVerTab = VipVerTab or BaseClass(VerItemRender)

function VipVerTab:OnFlush()
	VerItemRender.OnFlush(self)
	local index = self:GetIndex()
	local title = Language.Recharge.ToggleTitleList[index]
	if title and title ~= "" then
		self.node_list.title_label.text.text = title
		self.node_list.title_img:SetActive(true)
	else
		self.node_list.title_img:SetActive(false)
	end
end
