TianShenZhenFaView = TianShenZhenFaView or BaseClass(SafeBaseView)

function TianShenZhenFaView:__init()
	self.view_name = "TianShenZhenFaView"
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(1026, 636)})
	local bundle_name = "uis/view/tianshen_prefab"
	self:AddViewResource(0, bundle_name, "layout_ts_zhenfa")
end

function TianShenZhenFaView:__delete()

end

function TianShenZhenFaView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.TianShen.TianShenShenLingJiBan
	if not self.item_list then
		self.item_list = AsyncListView.New(TianShenZhenFaItem, self.node_list.item_list_root)
		self.item_list:SetDataList(TianShenWGData.Instance:GetUnionSkillCfg())
	end
end

function TianShenZhenFaView:ReleaseCallBack()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end

function TianShenZhenFaView:OnFlush()
	self.item_list:RefreshActiveCellViews()
end


------------------------- TianShenZhenFaItem -------------------------
TianShenZhenFaItem = TianShenZhenFaItem or BaseClass(BaseRender)

function TianShenZhenFaItem:__init()
	XUI.AddClickEventListener(self.node_list["one_key_btn"], BindTool.Bind(self.OnClickOneKeyBtn, self))
	if not self.ts_head_list then
		self.ts_head_list = AsyncListView.New(TianShenUnionHeadRender, self.node_list.ts_icon_list)
	end
end

function TianShenZhenFaItem:__delete()
	if self.ts_head_list then
		self.ts_head_list:DeleteMe()
		self.ts_head_list = nil
	end
end

function TianShenZhenFaItem:OnFlush()
	if not self.data then
		return
	end

	self.node_list.skill_name.text.text = string.format("【%s】", self.data.skill_name)

	local desc_list = Split(self.data.skill_describe or "", "\n")
	for i = 1, 2 do
		if desc_list[i] then
			self.node_list["skill_desc_" .. i].text.text = desc_list[i]
		end
		self.node_list["skill_desc_" .. i]:SetActive(desc_list[i] ~= nil)
	end

	local active_num = 0
	local ts_idx_list = {}
	local temp_data = Split(self.data.tianshen_list or "", "|")
	if not IsEmptyTable(temp_data) then
		for i, v in ipairs(temp_data) do
			ts_idx_list[i] = {}
			ts_idx_list[i].ts_index = tonumber(v)

			if TianShenWGData.Instance:IsActivation(tonumber(v)) then
				active_num = active_num + 1
			end
		end
		self.ts_head_list:SetDataList(ts_idx_list)
	end

	self.is_union_all_active = #temp_data > 0 and active_num >= #temp_data
	self.node_list.level_img.image:LoadSprite(ResPath.GetCommon("a2_quality_text_" .. self.data.skill_color))
	self.node_list.level_bg.image:LoadSprite(ResPath.GetCommon("a2_sl_di_" .. self.data.skill_color))
	local is_skill_active = TianShenWGData.Instance:CheckTSUnionSkillActiveById(self.data.skill_id)
	self.node_list.active_flag:SetActive(is_skill_active)
	self.node_list.one_key_btn:SetActive(not is_skill_active)
	if not is_skill_active then
		XUI.SetGraphicGrey(self.node_list.one_key_btn, not self.is_union_all_active)
	end

end

function TianShenZhenFaItem:OnClickOneKeyBtn()
	local data = self:GetData()
	if IsEmptyTable(data) then
		TianShenWGCtrl.Instance:CloseTianShenUnionSkillView()--关闭
		return
	end

	--已激活技能
	if TianShenWGData.Instance:CheckTSUnionSkillActiveById(data.skill_id) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.TSUnionSkillHasActive)
		return
	end

	--组合是否全部激活
	if not self.is_union_all_active then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.TSUnionNotAllActive)
		return
	end

	local ts_idx_list = Split(data.tianshen_list or "", "|")
	if IsEmptyTable(ts_idx_list) then
		print_error("天神组合技能(tianshen_list字段)配置有误,技能Id:", data.skill_id)
		TianShenWGCtrl.Instance:CloseTianShenUnionSkillView()--关闭
		return
	end

	--槽位解锁判断
	if TianShenWGData.Instance:GetActiveZhanSitNum() < #ts_idx_list then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.TSUnionNotLockFightPos)
		return
	end

	--一键上阵判断
	local all_fight_pos_data = TianShenWGData.Instance:GetAllFightPosData()--全部位置信息
	local can_fight_list = {}
	local temp_pos_data, is_same_ts, t_fight_data

	for pos = 0, #all_fight_pos_data do
		temp_pos_data = all_fight_pos_data[pos]
		if temp_pos_data.is_pos_act then--位置是否解锁
			t_fight_data = {}
			if temp_pos_data.image_index < 0 then--位置为空槽,位置可替换上阵
				t_fight_data.pos = pos
				t_fight_data.sort_key = temp_pos_data.image_index
				table.insert(can_fight_list, t_fight_data)
			else
				--比对需要一键上阵的天神
				is_same_ts = false
				for i, ts_idx in ipairs(ts_idx_list) do
					if temp_pos_data.image_index == tonumber(ts_idx) then
						is_same_ts = true
						table.remove(ts_idx_list, i)
						break
					end
				end

				--1.该位置已上阵的和待上阵中一致,位置不用替换上阵
				--2.该位置处于cd状态,位置不可替换上阵
				if not is_same_ts and not temp_pos_data.is_in_cd then
					t_fight_data.pos = pos
					t_fight_data.sort_key = temp_pos_data.image_index
					table.insert(can_fight_list, t_fight_data)
				end
			end
		end
	end

	--可上阵槽位判断
	if #can_fight_list < #ts_idx_list then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.TSUnionNotCDFightPos)
		return
	end

	--排序一下,优先空槽
	SortTools.SortAsc(can_fight_list, "sort_key")

	-- 请求一键上阵
	local t_fight_data
	for i, ts_idx in ipairs(ts_idx_list) do
		t_fight_data = can_fight_list[i]
		if t_fight_data then
			TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type3, tonumber(ts_idx), t_fight_data.pos)
		end
	end

	TianShenWGCtrl.Instance:CloseTianShenZhenFaView()
end


--天神头像---------------------------TianShenUnionHeadRender start
TianShenUnionHeadRender = TianShenUnionHeadRender or BaseClass(BaseRender)
function TianShenUnionHeadRender:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
		return
	end
	local base_cfg = TianShenWGData.Instance:GetTianShenCfg(data.ts_index)
	local ts_item_cfg = TianShenWGData.Instance:GetTianshenItemCfgByIndex(data.ts_index)
	if IsEmptyTable(base_cfg) or IsEmptyTable(ts_item_cfg) then
		return
	end
	local item_cfg = ItemWGData.Instance:GetItemConfig(ts_item_cfg.act_item_id)

	local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
	self.node_list["head_icon"].image:LoadSprite(bundle, asset, function ()
		self.node_list["head_icon"]:SetActive(true)
	end)
	local is_active = TianShenWGData.Instance:IsActivation(data.ts_index)
	local color = is_active and ItemWGData.Instance:GetItemColor(ts_item_cfg.act_item_id) or "#ffffff"

	self.node_list["name"].text.text = ToColorStr(base_cfg.bianshen_name or "", color)
	XUI.SetGraphicGrey(self.node_list["head_icon"], not is_active)
	self.node_list["head_bg"].image:LoadSprite(ResPath.GetCommonImages(TianShenWGData.TianShenBGcolor[base_cfg.series]))
	local item_cfg, big_type =ItemWGData.Instance:GetItemConfig(ts_item_cfg.act_item_id)
	XUI.SetGraphicGrey(self.node_list["head_icon"], not is_active)
	local eff_bundle, eff_asset = ResPath.GetWuPinKuangEffectUi(BaseCell_Ui_Effect[item_cfg.color])
	if is_active and eff_bundle and eff_asset then
		self.node_list["effect"]:SetActive(true)
		self.node_list.effect:ChangeAsset(eff_bundle, eff_asset)
	else
		self.node_list["effect"]:SetActive(false)
	end
end

-----------------------------TianShenUnionHeadRender end