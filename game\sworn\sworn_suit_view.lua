function SwornView:InitSwornSuitView()
    XUI.AddClickEventListener(self.node_list["btn_suit_active"], BindTool.Bind(self.OnClickSuitAttrView, self))
    self:FlushSuitEquipList()
    self.change_model = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_APPERANCE_CHANGE, BindTool.Bind(self.FlushRoleModel, self))
end

function SwornView:FlushSuitEquipList()
    if self.suit_equip_list == nil then
        self.suit_equip_list = {}
        local node_num = self.node_list["suit_equip_list"].transform.childCount
        for i = 0, node_num - 1 do
            self.suit_equip_list[i] = JlpSuitEquipRender.New(self.node_list["suit_equip_list"]:FindObj("equip_" .. i + 1))
            self.suit_equip_list[i]:SetIndex(i)
            self.suit_equip_list[i]:SetCellClickCallBack(BindTool.Bind(self.OnSelectSuitCallBack, self))
        end
    end
end

function SwornView:ShowSwornSuitIndexCallBack()
    self:FlushRoleModel()
end

function SwornView:SwornSuitViewReleaseCallBack()
    if self.suit_equip_list then
        for k, v in pairs(self.suit_equip_list) do
            v:DeleteMe()
        end
        self.suit_equip_list = nil
    end

    if self.change_model then
		GlobalEventSystem:UnBind(self.change_model)
		self.change_model = nil
	end

	if self.role_intro_model then
		self.role_intro_model:DeleteMe()
		self.role_intro_model = nil
	end
end

function SwornView:OnSelectSuitCallBack(cell)
    if cell == nil then
        return
    end

    local data = cell:GetData()
    if data == nil then
        return
    end

    local is_act = data.star_level > 0
    
    if not is_act then
        local upstar_cfg = SwornWGData.Instance:GetEquipStarCfg(data.suit, data.hole, data.star_level)
        if upstar_cfg then
            local has_num = ItemWGData.Instance:GetItemNumInBagById(data.item_id)
            if has_num >= upstar_cfg.cost_item_num then
                SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_EQUIP_STAR_UP, data.suit, data.hole)
                self:UpSucessEffect(self.node_list["suit_effect_node"], UIEffectName.s_jihuo)
                return
            end
        end
    end

    TipWGCtrl.Instance:OpenItem({item_id = data.item_id}, is_act and ItemTip.FROM_SWORN_EQUIP or ItemTip.FROM_NORMAL)
end

function SwornView:FlushRoleModel(appe_type)
	if appe_type ~= nil and not RoleWGData.IsNeedFlushModelAppeType(appe_type) then
		return
	end

	if nil == self.role_intro_model then
		local node = self.node_list.model_drag_event
		self.role_intro_model = RoleModel.New()
        local display_data = {
            parent_node = node,
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = true,
        }
        
        self.role_intro_model:SetRenderTexUI3DModel(display_data)
		-- self.role_intro_model:SetUI3DModel(node.transform, node.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.role_intro_model)
	end

	if self.role_intro_model and self.show_index == TabIndex.sworn_suit then
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
        local ignore_list = {ignore_wing = true, ignore_halo = true, ignore_fazhen = true, 
        ignore_shouhuan = true, ignore_waist = true, ignore_jianzhen = true}
		self.role_intro_model:SetModelResInfo(role_vo, ignore_list)
		self.role_intro_model:PlayRoleShowAction()
		self.role_intro_model:SetModelScale(Vector3(0.8, 0.8, 0.8))
		self.role_intro_model:FixToOrthographic(self.root_node_transform)
	end
end

function SwornView:FlushSuitView()
    if not self.suit_equip_list then
        return
    end

    local suit_index = self:GetSelectSuitIndex()
    local hole_list = SwornWGData.Instance:GetSuitHoleList(suit_index)

    for k,v in pairs(self.suit_equip_list) do
        v:SetData(hole_list[k])
    end

    if self.node_list["suit_act_red"] then
        local is_active = SwornWGData.Instance:GetStarSuitRemind(suit_index)
        self.node_list["suit_act_red"]:SetActive(is_active)
    end
    
    if self.node_list["suit_cap_value"] then
        local can_total_cap = SwornWGData.Instance:GetSuitCapability(suit_index)
        self.node_list["suit_cap_value"].text.text = can_total_cap
    end
end

function SwornView:OnClickSuitAttrView()
    local suit_index = self:GetSelectSuitIndex()
    SwornWGCtrl.Instance:OpenSuitAttrView(suit_index)
end

JlpSuitEquipRender = JlpSuitEquipRender or BaseClass(BaseRender)
function JlpSuitEquipRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_cell_click"], BindTool.Bind(self.OnClickSuitCellBtn, self))
end

function JlpSuitEquipRender:__delete()
    self.suit_click_callback = nil
end

function JlpSuitEquipRender:SetCellClickCallBack(call_back)
    self.suit_click_callback = call_back
end

function JlpSuitEquipRender:OnClickSuitCellBtn()
    if self.suit_click_callback then
        self.suit_click_callback(self)
    end
end

function JlpSuitEquipRender:OnFlush()
    if self.data == nil then
        return
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    if item_cfg ~= nil then
        self.node_list["suit_cell_pos"].image:LoadSpriteAsync(ResPath.GetCommon("a2_sc_btn_bg_" .. item_cfg.color))
        self.node_list["icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
    end

    local not_act = self.data.star_level <= 0
    self.node_list["equip_level"].text.text = string.format(Language.Sworn.EquipLevel, self.data.level)
    self.node_list["equip_star"].text.text = string.format(Language.Sworn.EquipStar, CommonDataManager.GetAncientNumber(self.data.star_level))
    XUI.SetGraphicGrey(self.node_list["icon"], not_act)
    XUI.SetGraphicGrey(self.node_list["suit_cell_pos"], not_act)

    local remind = false
    if not_act then
        local has_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
        remind = has_num > 0
    end
    
    self.node_list["remind"]:SetActive(remind)
end