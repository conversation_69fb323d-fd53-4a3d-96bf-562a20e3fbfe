HundredEquipTip = HundredEquipTip or BaseClass(SafeBaseView)
local RATIO_RARE = 100
function HundredEquipTip:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/hundred_equip_ui_prefab", "hundredequip_tip")
end

function HundredEquipTip:LoadIndexCallBack()
    XUI.AddClickEventListener(self.node_list.button_level, BindTool.Bind1(self.OnClickLevelUp, self))
    XUI.AddClickEventListener(self.node_list.rule_tips_btn, BindTool.Bind(self.OpenRuleTip, self))

    self.scroll_prop_attr_list = AsyncListView.New(HundredEquipAttrRender, self.node_list.scroll_prop_attr_list)
    self.scroll_award_list = AsyncListView.New(ItemCell, self.node_list.scroll_award_list)
    self.scroll_award_list:SetStartZeroIndex(true)
    if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list.node_model_parent)
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end
end

function HundredEquipTip:ReleaseCallBack()
    if self.scroll_prop_attr_list then
        self.scroll_prop_attr_list:DeleteMe()
        self.scroll_prop_attr_list = nil
    end

    if self.scroll_award_list then
        self.scroll_award_list:DeleteMe()
        self.scroll_award_list = nil
    end

    if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
    end

    self.show_model_item_id = nil
end

function HundredEquipTip:OnFlush()
    self:SetShowInfo()
    self:SetModelShow()
end

function HundredEquipTip:SetShowInfo()
    local buy_level = HundredEquipWGData.Instance:GetRmbBuyLevel()
    local level_cfg = HundredEquipWGData.Instance:GetRmbBuyCfg(buy_level)
    local next_cfg = HundredEquipWGData.Instance:GetRmbBuyCfg(buy_level + 1)
    local attr_data = EquipWGData.GetSortAttrListHaveNextByTypeCfg(level_cfg, next_cfg, "attr_id", "attr_value", 1, 8)
    self.scroll_prop_attr_list:SetDataList(attr_data)
    --XUI.SetButtonEnabled(self.node_list.button_level, next_cfg ~= nil)

    local btn_name = Language.HundredEquip.RechargeTxt6
    local buy_str2
    if not IsEmptyTable(next_cfg) then
        local price_2 = RoleWGData.GetPayMoneyStr(next_cfg.price, next_cfg.rmb_type, next_cfg.rmb_seq)
        btn_name = string.format(Language.HundredEquip.RechargeTxt7, price_2)
    end

    self.node_list.text_price.tmp.text = btn_name

    if level_cfg == 0 then
        level_cfg = HundredEquipWGData.Instance:GetRmbBuyCfg(1)
    end

    local name_txt = next_cfg and Language.HundredEquip.MainTxt9 or Language.HundredEquip.MainTxt10
    self.node_list.text_next_txt.text = name_txt
    self.node_list.text_next_ratio:SetActive(next_cfg ~= nil)
    self.node_list.node_next_add:SetActive(next_cfg ~= nil)
    self.node_list.text_max_ratio_show:SetActive(not next_cfg)

	--守护特权.
    local shtq_add_value = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeSHTQHundredfoldDropPro()
    if not next_cfg then
        next_cfg = level_cfg
        self.node_list.text_max_ratio_show.tmp.text = string.format(Language.HundredEquip.MainTxt7, level_cfg.drop_time_add_show + shtq_add_value)
    end

    local now_value = buy_level == 0 and 0 or (level_cfg.drop_time_add_show)
    self.node_list.text_now_ratio.tmp.text = string.format(Language.HundredEquip.NowRadio, now_value + shtq_add_value) .. "%"
    self.node_list.text_next_ratio.tmp.text = (next_cfg.drop_time_add_show + shtq_add_value) .. "%"

    self.scroll_award_list:SetDataList(next_cfg.reward)

    local _, fight = ItemShowWGData.Instance:OutStrAttrListAndCapalityByAttrId(next_cfg, "attr_id", "attr_value", 1, 8)
    if buy_level == 0 then
        fight = 0
    end

    self.node_list.text_fight_value.text.text = fight

    self.node_list.tips_text.text.text = Language.HundredEquip.EquipTip
end

function HundredEquipTip:SetModelShow()
    local buy_level = HundredEquipWGData.Instance:GetRmbBuyLevel()
    local level_cfg = HundredEquipWGData.Instance:GetRmbBuyCfg(buy_level)
    if buy_level == 0 then
        level_cfg = HundredEquipWGData.Instance:GetRmbBuyCfg(1)
    end

    if self.show_model_item_id ~= level_cfg.model_show_itemid then
        local display_data = {}
        display_data.item_id = level_cfg.model_show_itemid
        display_data.should_ani = true
        display_data.render_type = 0
        display_data.skip_rest_action = true
        display_data.model_rt_type = ModelRTSCaleType.M
        display_data.can_drag = false

        local pos = Vector2(0, 0)
        if level_cfg.display_pos ~= "" then
            local pos_list = string.split(level_cfg.display_pos, "|")
            pos = Vector2(tonumber(pos_list[1]), tonumber(pos_list[2]))
        end
        local rota = Quaternion.Euler(0, 0, 0)
        if level_cfg.display_rotation ~= "" then
            local pos_list = string.split(level_cfg.display_rotation, "|")
            rota = Vector3(tonumber(pos_list[1]), tonumber(pos_list[2]), tonumber(pos_list[3]))
        end
        display_data.position = pos
        display_data.rotation = rota
        local scale = level_cfg.display_scale or 1
        display_data.scale = Vector3(scale, scale, scale)
        self.model_display:SetData(display_data)
    end

    self.show_model_item_id = level_cfg.model_show_itemid
end

function HundredEquipTip:OnClickLevelUp()
    local buy_level = HundredEquipWGData.Instance:GetRmbBuyLevel()
    local next_cfg = HundredEquipWGData.Instance:GetRmbBuyCfg(buy_level + 1)
    if not next_cfg then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.HundredEquip.RechargeTxt6)
        return
    end

    RechargeWGCtrl.Instance:Recharge(next_cfg.price, next_cfg.rmb_type, next_cfg.rmb_seq)
end

function HundredEquipTip:OpenRuleTip()
	RuleTip.Instance:SetContent(Language.HundredEquip.RuleInfo, Language.HundredEquip.RuleTitle)
end



HundredEquipAttrRender = HundredEquipAttrRender or BaseClass(CommonAddAttrRender)

function HundredEquipAttrRender:LoadCallBack()
    CommonAddAttrRender.LoadCallBack(self)
    self:SetAttrNameNeedSpace(true)
end

function HundredEquipAttrRender:OnFlush()
    CommonAddAttrRender.OnFlush(self)
    local pos_x = self.data.add_value == 0 and 50 or 0
    self.node_list.node_now_parent.transform.localPosition = Vector3(pos_x, 0, 0)
end