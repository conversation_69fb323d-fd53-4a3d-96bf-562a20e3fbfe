local GIFG_ID_TO_RES = {
	[26125] = "a3_qy_bajpxb",
	[26126] = "a3_qy_bacjhj",
	[26127] = "a3_qy_bahhyl",
}

ChooseProfessView = ChooseProfessView or BaseClass(SafeBaseView)

function ChooseProfessView:__init()
    self:SetMaskBg(true)
	self.view_name = "ChooseProfessView"
    self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_choose_profess_view")
end

function ChooseProfessView:__delete()

end

function ChooseProfessView:LoadCallBack()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_bar"].transform)

	end
	self.item_num_enough = true
	self.other_name_var = self.node_list["name"]

	self.gift_list_data = {}
	self.selcet_item_list = {}

	self.select_friend_callback = BindTool.Bind(self.SelectFrendCallBack, self)
	XUI.AddClickEventListener(self.node_list["btn_mask"], BindTool.Bind(self.OpenFriendList, self))
    XUI.AddClickEventListener(self.node_list["ph_head"], BindTool.Bind(self.OpenFriendList, self))
    XUI.AddClickEventListener(self.node_list["img_plus"], BindTool.Bind(self.OpenFriendList, self))
	XUI.AddClickEventListener(self.node_list["btn_send"], BindTool.Bind(self.OnClickProfess, self))
    XUI.AddClickEventListener(self.node_list["btn_exchange"], BindTool.Bind(self.OnClickExchange, self))

	XUI.AddClickEventListener(self.node_list["btn_left"], BindTool.Bind(self.OnSelectChange, self, -1))
	XUI.AddClickEventListener(self.node_list["btn_right"], BindTool.Bind(self.OnSelectChange, self, 1))

	for i = 1, 3 do
		self.selcet_item_list[i] = ProfessGiftItemCell.New(self.node_list["cell_" .. i])
		XUI.AddClickEventListener(self.node_list["cell_" .. i], BindTool.Bind(self.SelectGift, self, i))
		self.selcet_item_list[i]:SetSelcetActive(false)
	end

    self:OnClickExchange()
end

function ChooseProfessView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
		elseif k == "info_change" then
			self:OnClickExchange()--策划需求:发送成功刷新文本
		end
	end
end

function ChooseProfessView:OnSelectChange(change_index)
	local cur_index = self.select_index or 1
	local target_index = cur_index + change_index
	target_index = (target_index > 3) and (target_index - 3) or (target_index < 1 and target_index + 3) or target_index
	self:SelectGift(target_index)
end

function ChooseProfessView:OnClickExchange()
    local profess_fandom_cfg = MarryWGData.Instance:GetQingyuanCfg()
    if not IsEmptyTable(profess_fandom_cfg) and not IsEmptyTable(profess_fandom_cfg.profess_random) then
        local default_text_index = math.random(1, #profess_fandom_cfg.profess_random)
        self.node_list["content"].text.text = Language.ProfessWall.SpaceBlank .. profess_fandom_cfg.profess_random[default_text_index].conect
    end
end

function ChooseProfessView:SelectGift(index)
	if index == self.select_index then
		return
	end

	self.select_index = index
	for i = 1, 3 do
		self.selcet_item_list[i]:SetSelcetActive(i == index)
	end

	local data_list = ProfessWallWGData.Instance:GetProfessGiftCfg()
	local target_data = data_list[self.select_index]
	local bundle, asset = ResPath.GetJieHunImg(GIFG_ID_TO_RES[target_data.gift_id])
	self.node_list.item_icon.image:LoadSprite(bundle, asset, function()
		self.node_list.item_icon.image:SetNativeSize()
	end)

	self.node_list.btn_right:CustomSetActive(self.select_index < 3)
	self.node_list.btn_left:CustomSetActive(self.select_index > 1)
end

function ChooseProfessView:ReleaseCallBack()
	if self.selcet_item_list then
		for k,v in pairs(self.selcet_item_list) do
			v:DeleteMe()
		end
		self.selcet_item_list = nil
	end
	self.select_index = nil
	self.select_friend_callback = nil
	self.list_view = nil
	self.other_name_var = nil
	self.other_rawimage = nil
    self.role_id = nil

    if self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
    end

	if self.head_frame_cell then
		self.head_frame_cell:DeleteMe()
		self.head_frame_cell = nil
	end

	self:RemoveNotifyDataChangeCallBack()
	if nil ~= self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end
end
function ChooseProfessView:CloseCallBack()
	self.select_index = nil
	if self.selcet_item_list then
		for i = 1, 3 do
			self.selcet_item_list[i]:SetSelcetActive(false)
		end
	end
end

function ChooseProfessView:ShowIndexCallBack()
	-- self.select_index = nil
	self.gift_list_data = ProfessWallWGData.Instance:GetProfessGiftCfg()
	self:SetNotifyDataChangeCallBack()
	--self:SelectGift(1)
	for k,v in pairs(self.selcet_item_list) do
		v:SetData(self.gift_list_data[k])
	end

	local select_gift_index = 1
	for i = 1,3 do
		local num = ItemWGData.Instance:GetItemNumInBagById(self.gift_list_data[i].gift_id)
		if num > 0 then
			select_gift_index = i
			-- self:SelectGift(i)
			break
		end
    end

	self:SelectGift(select_gift_index)

    self.node_list["img_plus"]:SetActive(true)
--	local data = self.gift_list_data[self.select_index]
	local is_delfault,is_delfault_load = ProfessWallWGData.Instance:GetDefaultInfo()  -- 其他入口表白   仙侣表白页签打开
	if is_delfault_load then
		local friend_list = ProfessWallWGCtrl.Instance:GetMyFriendList()
		if friend_list[1] and friend_list[1].is_love_id == 1 then
			self:SelectFrendCallBack(friend_list[1])	
		end
		ProfessWallWGData.Instance:SetDefaultInfo(nil,nil)
		return
	end
	if is_delfault then
		self:SelectFrendCallBack(is_delfault)
		ProfessWallWGData.Instance:SetDefaultInfo(nil,nil)
	end
end


--移除物品回调
function ChooseProfessView:RemoveNotifyDataChangeCallBack()
	if self.item_data_event ~= nil then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
		self.item_data_event = nil
	end
end

-- 设置物品回调
function ChooseProfessView:SetNotifyDataChangeCallBack()
	-- 监听系统事件
	if self.item_data_event == nil then
		self.item_data_event = BindTool.Bind1(self.ItemDataChangeCallback, self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
	end
end

function ChooseProfessView:ItemDataChangeCallback()
	self.gift_list_data = ProfessWallWGData.Instance:GetProfessGiftCfg()
	for k,v in pairs(self.selcet_item_list) do
		v:SetData(self.gift_list_data[k])
	end
end

function ChooseProfessView:OpenFriendList()
	local scroller_data =  ProfessWallWGCtrl.Instance:GetMyFriendList()
	ProfessWallWGCtrl.Instance:SetSelectFriendCallBack(self.select_friend_callback)
	if #scroller_data == 0 then
		ViewManager.Instance:Open(GuideModuleName.OpenKeyAddFriend)
	else
		ViewManager.Instance:Open(GuideModuleName.ProfessSelectFriendView, TabIndex.profess_wall_select_friend)
	end
	
end

function ChooseProfessView:SelectFrendCallBack(role_info)
	self.other_name = role_info.gamename or ""
	self.node_list["name"].text.text = self.other_name
	self:SetOtherHead(role_info)
	self.role_id = role_info.user_id
end

function ChooseProfessView:SelectTargetCallBack(role_info)
	self.other_name = role_info.gamename or ""
	self.other_name_var:SetValue(self.other_name)
	self:SetOtherHead(role_info)
	self.role_id = role_info.user_id
end

--设置他人头像
function ChooseProfessView:SetOtherHead(info)
    if not self.head_frame_cell then
        self.head_frame_cell = BaseHeadCell.New(self.node_list["ph_head"])
		self.head_frame_cell:SetBgActive(false)
    end
    self.node_list["img_plus"]:SetActive(false)
    local data = {role_id = info.user_id, prof = info.prof,  sex = info.sex}
    self.head_frame_cell:SetImgBg(false)
    self.head_frame_cell:SetData(data)
end

--发送表白
function ChooseProfessView:OnClickProfess()
	if nil == self.select_index then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ProfessWall.ProfessMaterial)
		return
	end
	local data = self.gift_list_data[self.select_index]
	local target_role_id = self.role_id
	local gift_type = data.gift_type
	local profess_text = self.node_list["content"].text.text
	profess_text = string.gsub(profess_text, Language.ProfessWall.SpaceBlank, "")

	--有非法字符直接不让发
	if ChatFilter.Instance:IsIllegal(profess_text, false) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.IllegalContent)
		return
	end

	if target_role_id == nil or target_role_id == 0 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.ProfessWall.SelectPrompt)
		return
	end

	if profess_text == "" then
		TipWGCtrl.Instance:ShowSystemMsg(Language.ProfessWall.ProfessContent)
	else
		-- local num = ItemWGData.Instance:GetItemNumInBagById(data.gift_id)
		-- if num == 0 then
		-- 	local cfg, _ = ItemWGData.Instance:GetItemConfig(data.gift_id)
		-- 	local seq = ShopWGData.Instance:GetShopCfgItemId(cfg.id)
		-- 	if nil == seq then return end
		-- 	ShopTip.Instance:SetData(cfg, 2, GameEnum.SHOP, 1, seq, false, 999)
		-- 	return
		-- end
        -- local is_auto_buy = 0

        local num = ItemWGData.Instance:GetItemNumInBagById(data.gift_id)

        if self.select_index ~= 1 and num <= 0 then
            if nil == self.alert_window then
                self.alert_window = Alert.New(nil, nil, nil, nil, true)
            end
            local info_cfg = ShopWGData.Instance:GetShopCfgItemId(data.gift_id)
            local cfg = ItemWGData.Instance:GetItemConfig(data.gift_id)
            local name = ToColorStr(cfg.name, ITEM_COLOR[cfg.color])
            self.alert_window:SetShowCheckBox(true, "ChooseProfessView")
            self.alert_window:SetLableString(string.format(Language.Flower.SendBuyFlower, info_cfg.price, name))
            self.alert_window:SetOkFunc(function()
                ProfessWallWGCtrl.Instance:SendProfessToReq(target_role_id, gift_type, nil, profess_text)
                MarryWGCtrl.Instance:SendProfessWallReq(PROFESS_WALL_REQ_TYPE.PROFESS_WALL_REQ_INFO, 2, 0)
                if ProfessWallWGCtrl.Instance:GetViewIsOpen() then --如果界面打开，强制请求一次数据
                    ProfessWallWGCtrl.Instance:SendProfessWallReqInfo()
                end
            end)
            self.alert_window:Open()
        else
            ProfessWallWGCtrl.Instance:SendProfessToReq(target_role_id, gift_type, nil, profess_text)
            MarryWGCtrl.Instance:SendProfessWallReq(PROFESS_WALL_REQ_TYPE.PROFESS_WALL_REQ_INFO, 2, 0)
            if ProfessWallWGCtrl.Instance:GetViewIsOpen() then --如果界面打开，强制请求一次数据
                ProfessWallWGCtrl.Instance:SendProfessWallReqInfo()
            end
        end
	end
end

--------------------ProfessGiftItemCell--------------------------------------
ProfessGiftItemCell = ProfessGiftItemCell or BaseClass(BaseRender)

function ProfessGiftItemCell:__init(instance)
	self.gift_name = self.node_list["title"]
	self.content = self.node_list["content"]
	self.item = ItemCell.New(self.node_list["reward_item"])
	--self.item:SetHideRightDownBgLessNum(-1)
	self.is_choose = false
end

function ProfessGiftItemCell:__delete()
	self.gift_name = nil
	self.content = nil
	if self.item then
		self.item:DeleteMe()
		self.item = nil
	end
end

function ProfessGiftItemCell:OnFlush()
	local data = self.data
	if data == nil then return end
	--local color_ping_dark = "#462953"
	local num = ItemWGData.Instance:GetItemNumInBagById(data.gift_id)
	self.item:SetData({item_id = data.gift_id})
	self.node_list.mater_count:SetActive(num > 0)
	--self.node_list.money_num:SetActive(num <= 0)
	if num > 0 then
		self.node_list.mater_count.text.text = string.format(Language.Market.HaveMaterialNum1,num)
	end

	local info_cfg = ShopWGData.Instance:GetShopCfgItemId(data.gift_id)
	if info_cfg then
		self.node_list.money_num.text.text = info_cfg.price
	end

	--self.gift_name.text.text = self.is_choose and ToColorStr(data.gift_name, color_ping_dark) or data.gift_name
	self.gift_name.text.text =data.gift_name
	local str = string.format(Language.ProfessWall.AddExp, data.self_charm, data.exp)
	--str = self.is_choose and ToColorStr(str, color_ping_dark) or str
	self.content.text.text = str
end

function ProfessGiftItemCell:SetSelcetActive(enable)
	--self.is_choose = enable
	if nil == self.data then 
		self.node_list["HightImage"]:SetActive(false)
		return
	end
	--local num = ItemWGData.Instance:GetItemNumInBagById( self.data.gift_id)
	self.node_list["HightImage"]:SetActive(enable)
	self:Flush()
end