----------------------------------------------------
-- 角色头像(小头像)
----------------------------------------------------
RoleHeadCell = RoleHeadCell or BaseClass(BaseRender)

-- 默认菜单项
RoleHeadDefItems = {
	Language.Menu.PrivateChat,
	-- Language.Menu.Trade,
	Language.Menu.ShowInfo,
	Language.Menu.AddFriend,
	Language.Menu.InviteTeam,
	Language.Menu.QiuHun,
	Language.Menu.GiveFlower,
	--Language.Menu.Profess,
	Language.Menu.Blacklist,
	--Language.Menu.SendMail,
	Language.Menu.ApplyTeam,
	Language.Menu.InviteGuild,
	Language.Menu.ApplyToBeTeamLeader,
	-- Language.Menu.ShowSpace,
	-- Language.Menu.InviteToSitMount,
	-- Language.Menu.Trace,
	Language.Menu.AddEnemy,
	Language.Menu.DeleteEnemy,


}

function RoleHeadCell:__init(has_bg, instance)
	self.role_id = 0
	self.role_name = ""
	self.prof = 0
	self.is_online = true
	self.is_can_open_menu = true
	self.is_inteam_index = -1
    self.is_from_private = false
    self.area_index = -1

	self.has_bg = has_bg
	if nil == self.has_bg then self.has_bg = true end

	self.add_items = {}								-- 要添加的菜单项
	self.remove_items = {}							-- 要移除的菜单项

	if instance then
		local bundle = "uis/view/miscpre_load_prefab"
		local asset = "RoleHeadCell"
		local async_loader = AllocAsyncLoader(self, "RoleHeadCell")
		async_loader:SetParent(instance.transform)
		async_loader:Load(bundle, asset, function(obj)
			self:SetInstance(obj.gameObject)
		end)
	end
end

function RoleHeadCell:__delete()
	if nil ~= self.img_role_head then
		AvatarManager.Instance:CancelUpdateAvatar(self.img_role_head)
	end

	self.img_head_frame = nil

	if nil ~= self.number_level then
		self.number_level:DeleteMe()
		self.number_level = nil
	end
	self.img_role_head = nil
	if self.alert_window1 then
		self.alert_window1:DeleteMe()
		self.alert_window1 = nil
    end
    self.area_index = -1
	self.custom_menu = {}
	self.plat_type = nil
end

function RoleHeadCell:LoadCallBack()
	self.node_list.Bg.button:AddClickListener(BindTool.Bind(self.OnClick, self))
	self.img_role_head = self.node_list.img_role_head
end

-- 设置角色信息 area_index 战区/阵营/大跨服组 索引
function RoleHeadCell:SetRoleInfo(info)
	local temp_plat_type = RoleWGData.Instance.role_vo.plat_type
	self.role_id = info.role_id
	self.role_name = info.role_name
    self.prof = info.prof
    self.sex = info.sex
	self.is_online = info.is_online
	self.is_inteam_index = info.team_index or -1
	self.team_type = info.team_type or 0
	self.plat_type = info.plat_type or temp_plat_type
	self.plat_name = info.plat_name
    self.server_id = info.server_id
    self.area_index = info.area_index or -1
	self.role_level = info.role_level
	self:Flush()
end

function RoleHeadCell:SetRobotInfo(role_name, prof, sex)
	local role_vo = RoleWGData.Instance.role_vo
	self.role_id = COMMON_CONSTS.GUILD_MENGZHU_ROBOT_ID
	self.role_name = role_name
    self.prof = prof
    self.sex = sex
	self.is_online = 0
	self.is_inteam_index = -1
	self.team_type = 0
	self.plat_type = role_vo.plat_type
	self.plat_name = role_vo.plat_name
    self.server_id = role_vo.server_id
    self:AddCustomMenu(
    Language.Menu.ShowInfo,
    Language.Menu.AddFriend,
    Language.Menu.InviteTeam)
    --Language.Menu.Profess)
	self:Flush()
end

function RoleHeadCell:SetIsFormPrivate(flag)
	self.is_from_private = flag
end

function RoleHeadCell:SetIsCanOpenMenu(is_can_open_menu)
	self.is_can_open_menu = is_can_open_menu
end

-- 添加菜单项
function RoleHeadCell:AddItems(...)
	self.add_items = {...}
end

-- 移除菜单项
function RoleHeadCell:RemoveItems(...)
	for i, v in ipairs({...}) do
		self.remove_items[v] = true
	end
end

-- 移除菜单项列表
function RoleHeadCell:RemoveItemsList(...)
	for i, v in ipairs({...}) do
		self.remove_items[v] = nil
	end
end

function RoleHeadCell:CreateChild()
	BaseRender.CreateChild(self)
end

function RoleHeadCell:OnFlush()
	AvatarManager.Instance:CancelUpdateAvatar(self.img_role_head)
	if 0 ~= self.role_id then
		--AvatarManager.Instance:UpdateAvatarImg(self.img_role_head, self.role_id, self.prof, false, not self.is_online)
	else
		if "system" == self.role_name then
		end
	end
end

function RoleHeadCell:OnClick()
	BaseRender.OnClick(self)

	-- 机器人不能点击查看
	if COMMON_CONSTS.ROBERT_ROLE_ID == self.role_id then
		return
	end

	self:OpenMenu()
end

function RoleHeadCell:OpenMenu(node, Vec2, pos_offset, alpha_type)
	if not self.is_can_open_menu then
		return
    end

	if 0 == self.role_id or "" == self.role_name then
		return
	end

	local vo = GameVoManager.Instance:GetMainRoleVo()
	local flag = vo.origin_uid == self.role_id or vo.role_id == self.role_id
	if flag and ((vo.merge_server_id == self.server_id and vo.plat_name == self.plat_name) or
		(nil == self.server_id and nil == self.plat_name)) then
		return
	end

	local items = {}
	if self.custom_menu then
        for k, v in pairs(self.custom_menu) do
			if self:CanAddMenu(v, vo) then
				table.insert(items, v)
			end
		end
	else
		for k, v in pairs(RoleHeadDefItems) do
			if self:CanAddMenu(v, vo) then
				table.insert(items, v)
			end
		end

		for k, v in pairs(self.add_items) do
			-- print_error(v,self:CanAddMenu(v, vo))
			if self:CanAddMenu(v, vo) then
				table.insert(items, v)
			end
		end
	end

	if not self.is_from_private then
		local is_has_enemy = false
		local is_has_black = false
		for k,v in pairs(items) do
			if v == Language.Menu.DeleteEnemy then
				is_has_enemy = true
			end
			if v == Language.Menu.BlackRemove then
				is_has_black = true
			end
        end

		if not self:IsSameServer() then --后端说不同服不能加好友
			if is_has_enemy then
				items = {Language.Menu.ShowInfo, Language.Menu.DeleteEnemy}
			else
				items = {Language.Menu.ShowInfo, Language.Menu.AddEnemy}
			end

			if is_has_black then
				table.insert(items,Language.Menu.BlackRemove)
			end
		end
	end

	if MultiMountWGData.Instance:IsHuanHuaMultiMount() then
		table.insert(items, Language.Menu.InviteToSitMount)
	end

	local pos = Vector2(0, 0)
	if nil == node and nil ~= Vec2 then
		pos = Vec2
	else
		pos = self:GetPos(node, items)
	end

	if pos_offset then
		pos = pos + pos_offset
	end

	local player_info = {}
	player_info.user_id = self.role_id
	player_info.role_name = self.role_name
	player_info.prof = self.prof
	player_info.is_online = self.is_online
	player_info.is_inteam_index = self.is_inteam_index
	player_info.team_type = self.team_type
	player_info.plat_type = self.plat_type
	player_info.plat_name = self.plat_name
    player_info.server_id = self.server_id
    player_info.sex = self.sex
	UiInstanceMgr.Instance:OpenCustomMenu(items, pos, BindTool.Bind1(self.OnClickMenuButton, self), items,nil,nil,nil,player_info, alpha_type)
end

--自定义菜单
function RoleHeadCell:AddCustomMenu( ... )
	self.custom_menu = { ... }
end

function RoleHeadCell:GetPos(node, items)
	if nil == node then return nil end
	local main_view = ViewManager.Instance:GetView(GuideModuleName.MainUIView)
	if nil == main_view or not main_view:IsOpen() then
		return
	end

	local parent_rect= main_view.root_node.transform:GetComponent(typeof(UnityEngine.RectTransform))
	local x, y = parent_rect.sizeDelta.x / 2, parent_rect.sizeDelta.y / 2
	local screen_pos_tbl
	if node.gameObject then
		screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, node.transform.position)
	else
		screen_pos_tbl = node
	end

	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, Vector2(0, 0))
	if local_position_tbl then
		x = local_position_tbl.x + 66 + (node.rect and node.rect.sizeDelta.x or 0)
		x = math.max(x, -parent_rect.sizeDelta.x / 2 + 162)
		x = math.min(x, parent_rect.sizeDelta.x / 2 - 162)
		y = local_position_tbl.y + 127
		y = math.max(y, -parent_rect.sizeDelta.y / 2 + math.max(#items * 60 / 2 + 156, 206))
		y = math.min(y, parent_rect.sizeDelta.y / 2 - math.max(#items * 60 / 2 + 156, 206))
		return Vector2(x, y)
	end
end

-- 判断是否可以添加菜单项
function RoleHeadCell:CanAddMenu(item_name, mainrole_vo)
	if nil ~= self.remove_items[item_name] then
		return false
	end

	if not self.is_online then
		-- 离线时不显示的菜单项
		if item_name == Language.Menu.Trade
			or item_name == Language.Menu.AddFriend
			or item_name == Language.Menu.InviteTeam
			or item_name == Language.Menu.ApplyTeam
			or item_name == Language.Menu.GiveFlower
			or item_name == Language.Menu.InviteGuild
			or item_name == Language.Menu.ChangeLeader
			--or item_name == Language.Menu.Profess
			or item_name == Language.Menu.QiuHun
			or item_name == Language.Menu.ApplyToBeTeamLeader
			or item_name == Language.Menu.AddEnemy
			-- or item_name == Language.Menu.DeleteEnemy
		then
			return false
		end
	end

	if item_name == Language.Menu.InviteGuild then
		if mainrole_vo.guild_post ~= GUILD_POST.TUANGZHANG and mainrole_vo.guild_post ~= GUILD_POST.FU_TUANGZHANG then
			return false
        end

        if not self:IsSameServer() then
            return false
        end

    elseif item_name == Language.Menu.ChangeLeader then
        local is_leader = SocietyWGData.Instance:GetIsTeamLeader() == 1
        if Scene.Instance:GetIsOpenCrossViewByScene() then
            is_leader = CrossTeamWGData.Instance:GetIsTeamLeader() == 1
        end

		if not is_leader then
			return false
		end

    elseif item_name == Language.Menu.AddFriend then
        if not self:IsSameServer() then
            return false
        end

		if SocietyWGData.Instance:CheckIsFriend(self.role_id) then
			return false
        end

    elseif item_name == Language.Menu.Blacklist then
        if not self:IsSameServer() then
            return false
        end
   --  elseif item_name == Language.Menu.Profess then
   --      if not self:IsSameServer() then
   --          return false
   --      end
   --      if not SocietyWGData.Instance:CheckIsFriend(self.role_id) then
			-- return false
   --      end
   --      if RoleWGData.Instance:GetRoleSex() == self.sex then
   --          return false
   --      end
    elseif item_name == Language.Menu.GiveFlower or item_name == Language.Menu.GiveFlowerNew then
        if not self:IsSameServer() then
            return false
        end

		local is_friend = SocietyWGData.Instance:CheckIsFriend(self.role_id)
        return is_friend

    elseif item_name == Language.Menu.QiuHun then
        if not self:IsSameServer() then
            return false
        end

		local is_show = SocietyWGData.Instance:GetQiuHunBtnShow(self.role_id)
		return is_show

    elseif item_name == Language.Menu.InviteTeam then
		local is_open = FunOpen.Instance:GetFunIsOpened(FunName.NewTeamView, true)
		if not is_open then
			return false
		end

		local fun_cfg = FunOpen.Instance:GetFunByName(FunName.NewTeamView)
	    if fun_cfg and self.role_level and fun_cfg.trigger_type == GuideTriggerType.LevelUp then
			local limit_lv = tonumber(fun_cfg.trigger_param) or 0
			if limit_lv > self.role_level then
				return false
			end
		end

		return self.is_inteam_index < 0

    elseif item_name == Language.Menu.ApplyTeam then
		local is_open = FunOpen.Instance:GetFunIsOpened(FunName.NewTeamView, true)
		if not is_open then
			return false
		end

		local fun_cfg = FunOpen.Instance:GetFunByName(FunName.NewTeamView)
	    if fun_cfg and self.role_level and fun_cfg.trigger_type == GuideTriggerType.LevelUp then
			local limit_lv = tonumber(fun_cfg.trigger_param) or 0
			if limit_lv > self.role_level then
				return false
			end
		end

        local team_index = SocietyWGData.Instance:GetTeamIndex()
        if Scene.Instance:GetIsOpenCrossViewByScene() then
            team_index = CrossTeamWGData.Instance:GetTeamIndex()
        end

		return self.is_inteam_index >= 0 and team_index ~= self.is_inteam_index

    elseif item_name == Language.Menu.ApplyToBeTeamLeader then
        local is_leader = SocietyWGData.Instance:GetIsTeamLeader() == 1
        if Scene.Instance:GetIsOpenCrossViewByScene() then
            is_leader = CrossTeamWGData.Instance:GetIsTeamLeader() == 1
        end

		return SocietyWGData.Instance:GetIsInTeam() == 1 and not is_leader

    elseif item_name == Language.Menu.PrivateChat then
        if not self:IsSameServer() then
            return false
        end

		local is_friend = SocietyWGData.Instance:CheckIsFriend(self.role_id)
		return is_friend

	elseif item_name == Language.Menu.AddEnemy then
		local is_enemy = SocietyWGData.Instance:CheckisRoleEnemy(self.plat_type, self.role_id)
		return not is_enemy

	elseif item_name == Language.Menu.DeleteEnemy then
		local is_enemy = SocietyWGData.Instance:CheckisRoleEnemy(self.plat_type, self.role_id)
		return is_enemy
	end

	return true
end

function RoleHeadCell:IsSameServer()
	local main_role_server_id = RoleWGData.Instance:GetMergeServerId()
	local main_role_plat_type = RoleWGData.Instance:GetPlatType()
	local server_id = self.server_id or main_role_server_id
	local plat_type = self.plat_type or main_role_plat_type
	return server_id == main_role_server_id and plat_type == main_role_plat_type
end

function RoleHeadCell:OnClickMenuButton(index, sender, param)
	local is_union = true
	--跨服组不同不能操作
	if self.server_id and self.plat_type and not CrossServerWGData.Instance:GetIsSameCrossServerGroupAsRole(self.server_id, self.plat_type) then
		is_union = false
	end

	local menu_text = param[index]
	if menu_text == Language.Menu.PrivateChat then
		SocietyWGCtrl.Instance:Flush("find_role_id",{self.role_id})

	elseif menu_text == Language.Menu.Trade then
		-- TradeWGCtrl.Instance:SendReqTrade(self.role_id)

    elseif menu_text == Language.Menu.ShowInfo then
        if self.role_id == COMMON_CONSTS.GUILD_MENGZHU_ROBOT_ID then
            --机器人操作
            local data = GuildWGData.Instance:GetRobotBrowseCfgByRoleName(self.role_name)
            BrowseWGCtrl.Instance:BrowseRobotInfo(data)
            return
        end
        local is_cross = not self:IsSameServer()
		BrowseWGCtrl.Instance:OpenWithUid(self.role_id,nil,is_cross,self.plat_type,1)

	elseif menu_text == Language.Menu.AddFriend then
		if not is_union then 
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Rank.NoOperate)
			return
        end
        if self.role_id == COMMON_CONSTS.GUILD_MENGZHU_ROBOT_ID then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOnline)
			return
        end

		SocietyWGCtrl.Instance:IAddFriend(self.role_id)
	elseif menu_text == Language.Menu.SendMail then
		SocietyWGCtrl.Instance:IOpenSendMail(self.role_name)

    elseif menu_text == Language.Menu.InviteTeam then
		if Scene.Instance:GetSceneType() == SceneType.Kf_PvP_Prepare then
			local is_my_zhandui = ZhanDuiWGData.Instance:GetTargetIsMyZhanDui(self.role_id)
			if not is_my_zhandui then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.DifferentZhandui2)
				return
			end
		end
        if Scene.Instance:GetIsOpenCrossViewByScene() then --跨服组队
            BrowseWGCtrl.Instance:AllReqRoleInfo(self.role_id,self.plat_type, function (protocol)
                local uuid = MsgAdapter.ReadUUIDByValue(protocol.role_id, self.plat_type)
                if protocol.is_online == 1 or protocol.is_online == 3 then
                    CrossTeamWGCtrl.Instance:ITeamInvite(uuid)
                else
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOnline)
                end
            end)
            return
        end

        if not is_union then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Rank.NoOperate)
			return
        end
        if self.role_id == COMMON_CONSTS.GUILD_MENGZHU_ROBOT_ID then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOnline)
			return
        end

		BrowseWGCtrl.Instance:AllReqRoleInfo(self.role_id,self.plat_type, function (protocol)
			if protocol.is_online == 1 or protocol.is_online == 3 then
				if 0 == SocietyWGData.Instance:GetIsInTeam() then
					--local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
					--local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
					local team_type = 0
    				local fb_mode = 1
    				local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()  --获取服务器最高世界等级
    				local min_level, max_level = COMMON_CONSTS.NoalGoalLimitMinLevel, top_user_level
                    --NewTeamWGCtrl.Instance:SendCreateTeam(team_type, fb_mode, min_level, max_level)
                    SocietyWGCtrl.Instance:ITeamInvite(protocol.role_id, self.team_type)
                    return
				end
				--NewTeamWGCtrl.Instance:SendCreateTeam()
				if self.is_inteam_index > 0 then
					SocietyWGCtrl.Instance:SendReqJoinTeam(self.is_inteam_index)
				else
					SocietyWGCtrl.Instance:ITeamInvite(protocol.role_id, self.team_type)
				end
			else
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOnline)
			end
		end)

	elseif menu_text == Language.Menu.ApplyTeam then
		if Scene.Instance:GetSceneType() == SceneType.Kf_PvP_Prepare then
			
			local is_my_zhandui = ZhanDuiWGData.Instance:GetTargetIsMyZhanDui(self.role_id)
			if not is_my_zhandui then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.DifferentZhandui)
				return
			end
		end
        BrowseWGCtrl.Instance:AllReqRoleInfo(self.role_id,self.plat_type, function (protocol)
            if Scene.Instance:GetIsOpenCrossViewByScene() then
                if SocietyWGData.Instance:GetIsInTeam() == 1 then
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.AlreadyInTeam)
                    return
                end
                CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_JOIN, self.is_inteam_index)
            else
                if protocol.is_online == 1 or protocol.is_online == 3 then
                    if SocietyWGData.Instance:GetIsInTeam() == 1 then
                        SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.AlreadyInTeam)
                        return
                    end
                    SocietyWGCtrl.Instance:SendReqJoinTeam(self.is_inteam_index)
                else
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOnline)
                end
            end
		end)

    elseif menu_text == Language.Menu.ApplyToBeTeamLeader then
        if Scene.Instance:GetIsOpenCrossViewByScene() then
            CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_REQ_LEADER)
        else
            SocietyWGCtrl.Instance:SendReqMemChangeTeamLeader()
        end

	elseif menu_text == Language.Menu.GiveFlower then
		-- SocietyWGData.Instance:SetSelectFriendSendId(self.role_id)
		FlowerWGCtrl.Instance:OpenSendFlowerView(self.role_id, self.role_name,self.sex,self.prof)
		-- SocietyWGCtrl.Instance:SendGoodsOpen()

	elseif menu_text == Language.Menu.InviteGuild then
		GuildWGCtrl.Instance:SendInviteGuild(self.role_id)

	elseif menu_text == Language.Menu.ChangeLeader then
		SocietyWGCtrl.Instance:SendChangeTeamLeader(self.role_id)

	elseif menu_text == Language.Menu.DeleteFriend then
		local lover_id = RoleWGData.Instance.role_vo.lover_uid
		if self.role_id ~= lover_id then
			if nil == self.alert_window then
				self.alert_window = Alert.New()
			end
			self.alert_window:SetLableString(string.format(Language.Society.DeleteFriend, self.role_name))
			self.alert_window:SetOkFunc(function ()
				SocietyWGCtrl.Instance:DeleteFriend(self.role_id)
			end)
			self.alert_window:Open()
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.RemoveLoverTips)
		end
	elseif menu_text == Language.Menu.DeleteEnemy then
		SocietyWGCtrl.Instance:DeleteEnemy(self.role_id, self.plat_type)
	elseif menu_text == Language.Menu.AddEnemy then
		SocietyWGCtrl.Instance:SendAddEnemy(self.role_id, self.plat_type)

	elseif menu_text == Language.Menu.CopyName then


	-- elseif menu_text == Language.Menu.ShowSpace then
	-- 	SocietyWGCtrl.Instance:OpenSpaceView(self.role_id)
	-- elseif menu_text == Language.Menu.Trace then
	-- 	local alert = nil
	-- 	local function close_callback()
	-- 		if nil ~= alert then
	-- 			alert:DeleteMe()
	-- 			alert = nil
	-- 		end
	-- 	end
	-- 	local function ok_callback()
	-- 		RoleWGCtrl.Instance:SendSeekRoleWhere(self.role_name)
	-- 	end
	-- 	local str = string.format(Language.Role.TraceConfirm, self.role_name)
	-- 	alert = Alert.New(str, ok_callback, nil, close_callback)
	-- 	alert:Open()
	elseif menu_text == Language.Menu.Blacklist then
		if not is_union then 
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Rank.NoOperate)
			return
		end 
		SocietyWGCtrl.Instance:DeleteFriend(self.role_id)
		ChatWGCtrl.Instance:SendAddBlackReq(self.role_id)

	elseif menu_text == Language.Menu.InviteToSitMount then
		if MultiMountWGData.Instance:IsHuanHuaMultiMount() then
			local obj_id = RoleWGData.Instance:GetAttr("obj_id")
			local multi_mount_info, multi_mount_state = MultiMountWGData.Instance:GetMultiMountDataInfo(obj_id)
			
			if multi_mount_state ~= MULTI_MOUNT_STATE.NONE then
				if multi_mount_state == MULTI_MOUNT_STATE.DRIZVE then
					-- 是否满人
					if MultiMountWGData.Instance:IsMyMultiMountFullRole(obj_id) then
						SysMsgWGCtrl.Instance:ErrorRemind(Language.MultiMount.MaxMultiMountTakeRoleNum)
					else
						SysMsgWGCtrl.Instance:ErrorRemind(Language.MultiMount.IsInviteOtherRoleTake)
						MultiMountWGCtrl:SendDoubleMountOperate(DOUBLE_MOUNT_OPERATE_TYPE.INVITE, self.plat_type, self.role_id)
					end
				elseif multi_mount_state == MULTI_MOUNT_STATE.TAKE then
					SysMsgWGCtrl.Instance:ErrorRemind(Language.MultiMount.NotMultiMountOwnerCanNotInvite)
				end
			else
				SysMsgWGCtrl.Instance:ErrorRemind(Language.MultiMount.IsInviteOtherRoleTake)
				MultiMountWGCtrl:SendDoubleMountOperate(DOUBLE_MOUNT_OPERATE_TYPE.INVITE, self.plat_type, self.role_id)
			end
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.MultiMount.NotHuanHuaMultiMount)
		end

	-- 	local role = Scene.Instance:GetObjectByRoleId(self.role_id)
	-- 	local mainrole = GameVoManager.Instance:GetMainRoleVo()
	-- 	if role and role.vo.multi_mount_res_id > 0 then
	-- 		SysMsgWGCtrl.Instance:ErrorRemind(Language.MultiMount.HasOnMultiMount)
	-- 	elseif role and role.vo.special_appearance ~= SPECIAL_APPEARANCE_TYPE.NORMAL then
	-- 		SysMsgWGCtrl.Instance:ErrorRemind(Language.MultiMount.PleaseCancelSpecialAppearence)
	-- 	else
	-- 		MultiMountWGCtrl.Instance:SendMultiModuleReq(MULTI_MOUNT_REQ_TYPE.MULTI_MOUNT_REQ_TYPE_INVITE_RIDE, self.role_id)
	-- 	end
	elseif menu_text == Language.Menu.BlackRemove or menu_text == Language.Menu.BlackRemove_2 then

		ChatWGCtrl.Instance:SendDeleteBlackReq(self.role_id)

	elseif menu_text == Language.Menu.GiveFlowerNew then
		FlowerWGCtrl.Instance:OpenSendFlowerView(self.role_id, self.role_name,self.sex,self.prof)
	-- elseif menu_text == Language.Menu.Profess then
	-- 	if not is_union then 
	-- 		SysMsgWGCtrl.Instance:ErrorRemind(Language.Rank.NoOperate)
	-- 		return
 --        end 
 --        if self.role_id == COMMON_CONSTS.GUILD_MENGZHU_ROBOT_ID then
 --            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOnline)
	-- 		return
 --        end
	-- 	local is_friend = SocietyWGData.Instance:CheckIsFriend(self.role_id)
	-- 	if is_friend then
	-- 		ProfessWallWGData.Instance:SetDefaultInfo(self.role_id,nil)
	-- 		--ViewManager.Instance:Open(GuideModuleName.ChooseProfessView)
	-- 	else
	-- 		local data = {} --role_id, role_name, prof
	-- 		data.role_id = self.role_id
	-- 		data.role_name = self.role_name
 --            data.prof = self.prof
 --            data.sex = self.sex
	-- 		SocietyWGCtrl.Instance:OpenAddTipsPanel(data)
	-- 	end
	-- 	--FlowerWGCtrl.Instance:OpenSendFlowerView(self.role_id, self.role_name)
	elseif menu_text == Language.Menu.QiuHun then
		MarryWGCtrl.Instance:OpenSelectLoverView(1) --1结婚2离婚
	end
end
