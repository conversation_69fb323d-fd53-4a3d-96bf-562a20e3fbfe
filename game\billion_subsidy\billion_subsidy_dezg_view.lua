-----------------------------------
-- 百亿补贴-大额直购
-----------------------------------

function BillionSubsidyView:DEZGReleaseCallBack()
	if self.dezg_tab_list then
		self.dezg_tab_list:DeleteMe()
		self.dezg_tab_list = nil
	end

	if self.dezg_count_reward_list then
		self.dezg_count_reward_list:DeleteMe()
		self.dezg_count_reward_list = nil
	end

	if self.dezg_reward_grid_list then
		self.dezg_reward_grid_list:DeleteMe()
		self.dezg_reward_grid_list = nil
	end

	if self.dezg_helper_item_list then
		for k, v in pairs(self.dezg_helper_item_list) do
			v:DeleteMe()
			v = nil
		end
		self.dezg_helper_item_list = nil
	end

	if self.model_act_render then
		self.model_act_render:DeleteMe()
		self.model_act_render = nil
	end

	self.dezg_jump_tab_seq = nil
	self.dezg_select_tab_seq = nil
	self.dezg_select_list_index = nil

	self:DEZGCleanTimer()
end

function BillionSubsidyView:DEZGCloseCallBack()
	self.dezg_jump_tab_seq = nil
	self.dezg_select_tab_seq = -1
	self.dezg_select_list_index = -1
end

function BillionSubsidyView:DEZGLoadIndexCallBack()
	if not self.dezg_tab_list then
		self.dezg_tab_list = AsyncListView.New(BillionSubsidyDEZGTabCell, self.node_list.dezg_tab_list)
		self.dezg_tab_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectDEZGTabItemCallBack, self))
	end

	if not self.dezg_count_reward_list then
		self.dezg_count_reward_list = AsyncListView.New(BillionSubsidyDEZGCountRewardCell, self.node_list.dezg_count_reward_list)
		self.dezg_count_reward_list:SetStartZeroIndex(true)
	end

	if not self.dezg_reward_grid_list then
		self.dezg_reward_grid_list = AsyncBaseGrid.New()
		self.dezg_reward_grid_list:CreateCells({
			col = 3,
			change_cells_num = 1,
			list_view = self.node_list.dezg_item_list,
		})
		self.dezg_reward_grid_list:SetStartZeroIndex(true)
	end

	if not self.dezg_helper_item_list then
		self.dezg_helper_item_list = {}
		for i = 1, 3 do
			self.dezg_helper_item_list[i] = BillionSubsidyDEZGRewardCell.New(self.node_list["dezg_helper_item_" .. i])
		end
	end

	self.model_act_render = OperationActRender.New(self.node_list.dezg_model_display)

	XUI.AddClickEventListener(self.node_list["dezg_buy_btn"], BindTool.Bind(self.OnClickDEZGBuyBtn, self))
	XUI.AddClickEventListener(self.node_list["dezg_helper_reward_btn"], BindTool.Bind(self.OnClickDEZGHelperRewardBtn, self))
	XUI.AddClickEventListener(self.node_list["dezg_buy_desc_btn"], BindTool.Bind(self.OnClickDEZGBuyDescBtn, self))

	self.dezg_jump_tab_seq = nil
	self.dezg_select_tab_seq = -1
	self.dezg_select_list_index = -1
end

function BillionSubsidyView:DEZGShowIndexCallBack()

end

--ViewManager.Instance:Open(GuideModuleName.BillionSubsidy, TabIndex.billion_subsidy_dezg, "jump_tab", {jump_tab = index})外部跳转到对应页签.
function BillionSubsidyView:DEZGOnFlush(param_t, index)
	local to_tab = nil
	for k_1, v_1 in pairs(param_t or {}) do
		if k_1 == "jump_tab" then
			to_tab = v_1.jump_tab
			break
		end
	end

	self:FlushDEZGTabList(to_tab)
	self:FlushDEZGPanel()

	if self.model_act_render then
		self.model_act_render:ActModelPlayLastAction()
	end
end

function BillionSubsidyView:FlushDEZGTabList(to_tab)
	local show_list = BillionSubsidyWGData.Instance:GetDEZGCfg()

	local no_data = IsEmptyTable(show_list)
	self.node_list["no_data_root"]:SetActive(no_data)
	self.node_list["had_data_root"]:SetActive(not no_data)
	if no_data then
		return
	end

	if self.dezg_jump_tab_seq == nil then
		self.dezg_jump_tab_seq = 0

		for index, temp_data in ipairs(show_list) do
			if to_tab then
				if to_tab == temp_data.item_seq then
					self.dezg_jump_tab_seq = index
					break
				end
			else
				--跳红点.
				local is_red = 0
				if is_red then
					self.dezg_jump_tab_seq = index
					break
				end
			end
		end
	end

	self.node_list.dezg_tab_list:SetActive(show_list and #show_list > 0)

	if self.dezg_tab_list ~= nil then
		self.dezg_tab_list:SetDataList(show_list)
		if self.dezg_jump_tab_seq then
			if self.dezg_jump_tab_seq == 0 then
				self.dezg_tab_list:JumpToIndex(1, 5)
				self.dezg_jump_tab_seq = 100
			elseif self.dezg_jump_tab_seq ~= 100 then
				self.dezg_tab_list:JumpToIndex(self.dezg_jump_tab_seq, 5)
				self.dezg_jump_tab_seq = 100
			end
		end
	end
end

function BillionSubsidyView:OnSelectDEZGTabItemCallBack(item)
	if nil == item or nil == item.data then
		return
	end

	--拿到当前选中的格子下标
	local cell_index = item:GetIndex()
	if self.dezg_select_list_index == cell_index then
		return
	end

	self.dezg_select_list_index = cell_index
	self.dezg_select_tab_seq = item.data.item_seq

	--刷新界面.
	self:FlushDEZGModel()
	self:FlushDEZGRewardList()
	self:FlushDEZGPanel()
	self:DEZGLoginTimeCountDown()
end

function BillionSubsidyView:FlushDEZGRewardList()
	local dezg_cfg = BillionSubsidyWGData.Instance:GetDEZGCfgBySeq(self.dezg_select_tab_seq)
	if not dezg_cfg then
		return
	end

	local data_list = dezg_cfg.reward
	if not IsEmptyTable(data_list) then
		self.dezg_reward_grid_list:SetDataList(data_list)
	end

	self.node_list.dezg_desc.text.text = dezg_cfg.desc
	self.node_list.dezg_model_name.text.text = dezg_cfg.tab_name
end

function BillionSubsidyView:FlushDEZGModel()
	local dezg_cfg = BillionSubsidyWGData.Instance:GetDEZGCfgBySeq(self.dezg_select_tab_seq)
	if not dezg_cfg then
		return
	end

	local display_data = {}
	display_data.should_ani = true
	if dezg_cfg.show_item_id ~= 0 and dezg_cfg.show_item_id ~= "" then
		local split_list = string.split(dezg_cfg.show_item_id, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = dezg_cfg.show_item_id
		end
	end

	display_data.render_type = OARenderType.RoleModel
	display_data.model_rt_type = ModelRTSCaleType.L

	if dezg_cfg.position and dezg_cfg.position ~= "" then
		local pos_list = string.split(dezg_cfg.position, "|")
		local pos_x = tonumber(pos_list[1]) or 0
		local pos_y = tonumber(pos_list[2]) or 0
		local pos_z = tonumber(pos_list[3]) or 0

		display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
	end

	if dezg_cfg.rotation and dezg_cfg.rotation ~= "" then
		local rot_list = string.split(dezg_cfg.rotation, "|")
		local rot_x = tonumber(rot_list[1]) or 0
		local rot_y = tonumber(rot_list[2]) or 0
		local rot_z = tonumber(rot_list[3]) or 0

		display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
	end

	display_data.model_adjust_root_local_scale = dezg_cfg.scale

	self.model_act_render:SetData(display_data)
end

function BillionSubsidyView:FlushDEZGPanel()
	local dezg_cfg = BillionSubsidyWGData.Instance:GetDEZGCfgBySeq(self.dezg_select_tab_seq)
	if not dezg_cfg then
		return
	end

	local help_num = BillionSubsidyWGData.Instance:GetDEZGShareHelpTimesInfoBySeq(self.dezg_select_tab_seq)
	local need_num_max = dezg_cfg["helper_num_" .. 3]
	self.node_list.dezg_helper_reward_text.text.text = string.format(Language.BillionSubsidy.DEZGHelpNumText, COLOR3B.C2, help_num, need_num_max)

	for i = 1, 3 do
		local need_num = dezg_cfg["helper_num_" .. i]

		local helper_item_data = {}
		helper_item_data.is_act = help_num >= need_num
		helper_item_data.need_num = need_num
		helper_item_data.reward = dezg_cfg["helper_num_reward_" .. i]
		self.dezg_helper_item_list[i]:SetData(helper_item_data)
	end

	local slider_value = BillionSubsidyWGData.Instance:GetDEZGHelpTimesProgress(self.dezg_select_tab_seq, help_num)
	self.node_list.dezg_helper_slider.slider.value = slider_value

	local is_have_buy = BillionSubsidyWGData.Instance:CheckHaveBuyDEZGShopItemBySeq(self.dezg_select_tab_seq)
	self.node_list.dezg_buy_group:SetActive(not is_have_buy)
	self.node_list.dezg_helper_reward:SetActive(is_have_buy)

	-- 可用券
	local reduce_value = BillionSubsidyWGData.Instance:GetDEZGShopCurReduceValue()
	self.dezg_cur_use_dc = nil
	local dezg_ticket_text = ""
	local cur_price = dezg_cfg.member_price - reduce_value

	local free_dc, full_dc, direct_dc = BillionSubsidyWGData.Instance:GetCanUseDiscountTicketBySeq(BillionSubsidyWGData.ShopType.DEZG, self.dezg_select_tab_seq)
	if full_dc then --满减券
		cur_price = cur_price - full_dc.reduce_quota
		cur_price = cur_price < 0 and 0 or cur_price

		dezg_ticket_text = string.format(Language.BillionSubsidy.FullDCSelectDesc, full_dc.quota_limit, full_dc.reduce_quota)
		self.dezg_cur_use_dc = full_dc
	elseif direct_dc then --立减券
		cur_price = cur_price - direct_dc.reduce_quota
		cur_price = cur_price < 0 and 0 or cur_price

		dezg_ticket_text = string.format(Language.BillionSubsidy.DirectDCSelectDesc, direct_dc.reduce_quota)
		self.dezg_cur_use_dc = direct_dc
	end

	self.node_list.dezg_ticket_text.text.text = dezg_ticket_text
	self.node_list.dezg_ticket:SetActive(nil ~= self.dezg_cur_use_dc)
	self.node_list["dezg_buy_price"].text.text = string.format(Language.BillionSubsidy.MonthCardBuy, cur_price)
	self.node_list["dezg_buy_cost"].text.text = string.format(Language.BillionSubsidy.MonthCardBuy, dezg_cfg.price)

	local member_level = BillionSubsidyWGData.Instance:GetMemberLevel()
	local member_level_str = Language.BillionSubsidy.VipNameList[member_level]
	local member_level_cfg = BillionSubsidyWGData.Instance:GetMemberLevelCfg()
	self.node_list.dezg_tips_desc.text.text = string.format(Language.BillionSubsidy.DEZGTipsDesc, member_level_str, member_level_cfg.high_price_shop_discount)

    local bundle, asset = ResPath.GetBillionSubsidyImg("a3_bybt_hz" .. member_level)
    self.node_list.dezg_vip_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.dezg_vip_icon.image:SetNativeSize()
    end)

	local is_max = BillionSubsidyWGData.Instance:GetIsMaxMemberLevel()
	self.node_list.dezg_buy_desc_content:SetActive(not is_max)

	self.node_list.dezg_buy_desc.text.text = Language.BillionSubsidy.DEZGBuyDesc

	local reduce_cfg = BillionSubsidyWGData.Instance:GetDEZGShopReduceCfg()
	self.dezg_count_reward_list:SetDataList(reduce_cfg)
end

--活动时间倒计时
function BillionSubsidyView:DEZGLoginTimeCountDown()
	local dezg_cfg = BillionSubsidyWGData.Instance:GetDEZGCfgBySeq(self.dezg_select_tab_seq)
	if not dezg_cfg then
		return
	end

	local end_time = BillionSubsidyWGData.Instance:GetItemEndTimeByGrade(BillionSubsidyWGData.ShopType.DEZG, dezg_cfg.grade)
	self.node_list.dezg_ticket_count_down.text.text = TimeUtil.FormatSecondDHM6(end_time)

	self:DEZGCleanTimer()
	if not self.bs_dezg_down and end_time > 0 then
		self.bs_dezg_down = CountDown.Instance:AddCountDown(end_time, 1,
			-- 回调方法
			function(elapse_time, total_time)
				local valid_time = total_time - elapse_time
				self.node_list.dezg_ticket_count_down.text.text = TimeUtil.FormatSecondDHM6(valid_time)
			end,
			-- 倒计时完成回调方法
			function()
				self.node_list.dezg_ticket_count_down.text.text = Language.BillionSubsidy.CountDownEndText
			end
		)
	end
end

function BillionSubsidyView:DEZGCleanTimer()
	if self.bs_dezg_down and CountDown.Instance:HasCountDown(self.bs_dezg_down) then
		CountDown.Instance:RemoveCountDown(self.bs_dezg_down)
		self.bs_dezg_down = nil
	end
end

function BillionSubsidyView:OnClickDEZGBuyBtn()
	local dezg_cfg = BillionSubsidyWGData.Instance:GetDEZGCfgBySeq(self.dezg_select_tab_seq)
	if not dezg_cfg then
		return
	end

	local is_show = BillionSubsidyWGData.Instance:CheckIsShowItemByGrade(BillionSubsidyWGData.ShopType.DEZG, dezg_cfg.grade)
	if not is_show then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BillionSubsidy.NotBuyTime)
	end

	local high_price_shop_buy_times = BillionSubsidyWGData.Instance:GetDEZGShopReduceCount()
	BillionSubsidyWGCtrl.Instance:OpenBuyTipView(BillionSubsidyWGData.ShopType.DEZG, self.dezg_select_tab_seq, high_price_shop_buy_times)
end

function BillionSubsidyView:OnClickDEZGHelperRewardBtn()
	local dezg_cfg = BillionSubsidyWGData.Instance:GetDEZGCfgBySeq(self.dezg_select_tab_seq)
	if not dezg_cfg then
		return
	end

	local is_show = BillionSubsidyWGData.Instance:CheckIsShowItemByGrade(BillionSubsidyWGData.ShopType.DEZG, dezg_cfg.grade)
	if not is_show then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BillionSubsidy.NotBuyTime)
	end

	BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.SHARE_HIGH_PRICE_RMB_BUY_HELP, self.dezg_select_tab_seq)
	SysMsgWGCtrl.Instance:ErrorRemind(Language.BillionSubsidy.DRPTInviteTips)
end

function BillionSubsidyView:OnClickDEZGBuyDescBtn()
	BillionSubsidyWGCtrl.Instance:OpenActiceVipView()
end

-------------------------------------BillionSubsidyDEZGTabCell-------------------------------------
BillionSubsidyDEZGTabCell = BillionSubsidyDEZGTabCell or BaseClass(BaseRender)

function BillionSubsidyDEZGTabCell:OnFlush()
    if self.data == nil then
        return
    end

    self.node_list.name.text.text = self.data.tab_name
    self.node_list.name_hl.text.text = self.data.tab_name
    self.node_list.desc.text.text = self.data.tab_desc
    self.node_list.desc_hl.text.text = self.data.tab_desc
end

function BillionSubsidyDEZGTabCell:OnSelectChange(is_select)
    self.node_list.HLImage:SetActive(is_select)
end

-------------------------------------BillionSubsidyDEZGRewardCell-------------------------------------
BillionSubsidyDEZGRewardCell = BillionSubsidyDEZGRewardCell or BaseClass(BaseRender)

function BillionSubsidyDEZGRewardCell:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.item_cell)
	end
end

function BillionSubsidyDEZGRewardCell:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function BillionSubsidyDEZGRewardCell:OnFlush()
    if self.data == nil then
        return
    end

	self.node_list.pro_img:SetActive(self.data.is_act)
	self.node_list.num.text.text = string.format(Language.BillionSubsidy.NnmText, self.data.need_num)
	self.item_cell:SetData(self.data.reward)
	self.item_cell:SetLingQuVisible(self.data.is_act)
end

-------------------------------------BillionSubsidyDEZGCountRewardCell-------------------------------------
BillionSubsidyDEZGCountRewardCell = BillionSubsidyDEZGCountRewardCell or BaseClass(BaseRender)

function BillionSubsidyDEZGCountRewardCell:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.item_cell)
		self.item_cell:UseNewSelectEffect(true)
	end

	XUI.AddClickEventListener(self.node_list["btn_receive"], BindTool.Bind(self.OnClickReceiveBtn, self))
end

function BillionSubsidyDEZGCountRewardCell:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function BillionSubsidyDEZGCountRewardCell:OnFlush()
    if self.data == nil then
        return
    end

	local is_can_receive = BillionSubsidyWGData.Instance:GetDEZGShopReduceRewardIsCanReceive(self.data.seq)
	local is_receive = BillionSubsidyWGData.Instance:GetDEZGShopReduceRewardIsReceive(self.data.seq)
	self.node_list.btn_receive:SetActive(is_can_receive)

	self.item_cell:SetFlushCallBack(function ()
		self.item_cell:ResetSelectEffect()
		self.item_cell:SetSelectEffect(is_receive)
		self.item_cell:SetRedPointEff(is_can_receive)
	end)
	self.item_cell:SetData(self.data.reward[0])

	self.node_list.reward_desc.text.text = string.format(Language.BillionSubsidy.DRPTCountRewardDesc1, self.data.reduce_value)
	self.node_list.count_desc.text.text = string.format(Language.BillionSubsidy.DRPTCountRewardDesc2, self.data.buy_times)
end

function BillionSubsidyDEZGCountRewardCell:OnClickReceiveBtn()
	BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.HIGH_PRICE_FETCH_REDUCE_REWARD, self.data.seq)
end