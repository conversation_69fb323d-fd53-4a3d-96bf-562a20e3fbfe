
TwinDirectPurchaseView = TwinDirectPurchaseView or BaseClass(SafeBaseView)

function TwinDirectPurchaseView:__init()
    self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:SetMaskBg(false, true)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

    -- self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/twin_direct_purchase_ui_prefab", "layout_twin_direct_purchase")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")

	self:SetTabShowUIScene(0,{type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.TWIN_DIRECT_PURCHASE})
end

function TwinDirectPurchaseView:ReleaseCallBack()
    self:CleanActTimer()

    if self.grade_list then
        self.grade_list:DeleteMe()
        self.grade_list = nil
    end

    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end

    if self.display_model then
		self.display_model:DeleteMe()
		self.display_model = nil
	end

    if self.role_model_display then
		self.role_model_display:DeleteMe()
		self.role_model_display = nil
	end
end

function TwinDirectPurchaseView:OpenCallBack()
    self.tianshen_info_audio_play_t = {}
end

function TwinDirectPurchaseView:CloseCallBack()
    self.tianshen_info_audio_play_t = {}
end

function TwinDirectPurchaseView:ShowIndexCallBack()
    if not self.act_timer then
        local total_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_Twin_Direct_Purchase)
        if total_time > 0 then
            self.act_timer = CountDown.Instance:AddCountDown(total_time, 0.5,
            function (elapse_time, act_time)
                local time = math.floor(act_time - elapse_time)
                self:UpdateTimeStr(time)
            end,
            function ()
                self:Close()
            end
            )
        end
    end

    ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.TwinDirectPurchase, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_Twin_Direct_Purchase)
end

function TwinDirectPurchaseView:LoadCallBack()
    local bundle, asset = ResPath.GetRawImagesJPG("a3_jpzg_bj")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
	self.node_list.title_view_name.text.text = Language.TwinDirectPurchase.ViewName

    self.cur_select_grade = -1
    self.old_show_id = -1
    if not self.grade_list then
        self.grade_list = AsyncListView.New(TwinDirectPurchaseToggleRender, self.node_list.grade_list)
        self.grade_list:SetStartZeroIndex(false)
        self.grade_list:SetSelectCallBack(BindTool.Bind(self.OnSelectGardeToggle, self))
    end

    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(true)
    end

    if self.display_model == nil then
		self.display_model = OperationActRender.New(self.node_list.model_pos)
		self.display_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
        self:AddUiRoleModel(self.display_model, 0)
	end

    if self.role_model_display == nil then
		self.role_model_display = OperationActRender.New(self.node_list.role_model_display)
		self.role_model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
        self:AddUiRoleModel(self.role_model_display, 0)
	end

    XUI.AddClickEventListener(self.node_list.buy_btn, BindTool.Bind(self.OnClickBuyBtn, self))
    XUI.AddClickEventListener(self.node_list.skill_btn, BindTool.Bind2(self.OnSkillShowClick, self))
    XUI.AddClickEventListener(self.node_list.btn_skill_pre_btn, BindTool.Bind(self.OnClickSpiritSkillShow, self))
end

function TwinDirectPurchaseView:OnFlush()
    local dafult_index = 1
    local twin_cfg = TwinDirectPurchaseData.Instance:GetGiftShowList()
    self.grade_list:SetDataList(twin_cfg)
    self.grade_list:JumpToIndex(dafult_index)

    self.node_list.left:SetActive(false)--#twin_cfg > 1)    --策划金雪威要屏蔽.

    self:FlushAllPanel()
end

function TwinDirectPurchaseView:FlushAllPanel()
    local twin_cfg = TwinDirectPurchaseData.Instance:GetCurGradeCfgBySeq(self.cur_select_grade)
    if IsEmptyTable(twin_cfg) then
        return
    end

    local buy_times = TwinDirectPurchaseData.Instance:GetCurGradeBuyFlag(twin_cfg.rmb_buy_flag)
    local is_can_buy = twin_cfg.buy_times > buy_times
    local color = is_can_buy and COLOR3B.C2 or COLOR3B.C3
    self.node_list.limit_count.text.text = string.format(Language.TwinDirectPurchase.BuyLimit, color, buy_times, twin_cfg.buy_times)
    self.node_list.buy_done:SetActive(not is_can_buy)
    self.node_list.buy_btn:SetActive(is_can_buy)
    self.node_list.btn_skill_pre_btn:SetActive(type(twin_cfg.nuqi_type) == "number")

    self.node_list.skill_name.text.text = twin_cfg.skill_name
    local bundle, asset = ResPath.GetSkillIconById(twin_cfg.skill_icon)
	XUI.SetNodeImage(self.node_list.skill_icon, bundle, asset)

    local desc_bundle, desc_asset = ResPath.GetTwinDirectPurchaseImage(twin_cfg.skill_desc_img)
	XUI.SetNodeImage(self.node_list.skill_desc_img, desc_bundle, desc_asset)

    if self.old_show_id == twin_cfg.rmb_buy_flag then
        return
    end

    self.reward_list:SetDataList(twin_cfg.reward_item)

    -- 战力
	local capability, show_max_cap, _ = 0, false, nil
	local show_item_id = twin_cfg.model_show_itemid
    local item_list_str = Split(show_item_id, "|")
    show_item_id = tonumber(item_list_str[1])
	if show_item_id then
		if ItemWGData.GetIsXiaogGui(show_item_id) then
			_, capability = ItemShowWGData.Instance:GetShouHuAttrByData(show_item_id)
		elseif HiddenWeaponWGData.Instance:GetOnlyEquipCfgById(show_item_id) then
			capability = HiddenWeaponWGData.Instance:GetItemCapability(show_item_id)
		else
			local item_cfg = ItemWGData.Instance:GetItemConfig(show_item_id)
			if item_cfg then
				local need_get_sys, sys_type = ItemShowWGData.Instance:GetIsNeedSysAttr(show_item_id, item_cfg.sys_attr_cap_location)
				if sys_type == ITEMTIPS_SYSTEM.MOUNT or sys_type == ITEMTIPS_SYSTEM.LING_CHONG
				or sys_type == ITEMTIPS_SYSTEM.HUA_KUN or sys_type == ITEMTIPS_SYSTEM.TS_HUANHUA
				or sys_type == ITEMTIPS_SYSTEM.XIAN_WA then
					show_max_cap = false
					capability = ItemShowWGData.CalculateCapability(show_item_id, show_max_cap) or 0
				else
					capability = ItemShowWGData.CalculateCapability(show_item_id) or 0
				end
			end
		end
	end
	self.node_list["cap_value"].text.text = capability

    self:ChangeStyleImage()
    self:FlushModel()
end

function TwinDirectPurchaseView:OnSelectGardeToggle(cell)
    local data = cell:GetData()
    if not data or self.cur_select_grade == data.rmb_buy_flag then
        return
    end

    self.cur_select_grade = data.rmb_buy_flag

    self:FlushAllPanel()
end

function TwinDirectPurchaseView:ChangeStyleImage()
    local twin_cfg = TwinDirectPurchaseData.Instance:GetCurGradeCfgBySeq(self.cur_select_grade)
    if IsEmptyTable(twin_cfg) then
        return
    end

    local color_cfg = TwinDirectPurchaseData.Instance:GetColorCfgByColorType(twin_cfg.color_type)
	if not color_cfg then
		return
	end

    local bundle, asset = ResPath.GetRawImagesPNG(color_cfg.title_img)
	XUI.SetNodeImage(self.node_list.title_img, bundle, asset)

    bundle, asset = ResPath.GetRawImagesPNG(color_cfg.down_bg)
	XUI.SetNodeImage(self.node_list.down_bg, bundle, asset)

    bundle, asset = ResPath.GetTwinDirectPurchaseImage(color_cfg.zsp_img)
	XUI.SetNodeImage(self.node_list.zsp_img_1, bundle, asset)
	XUI.SetNodeImage(self.node_list.zsp_img_2, bundle, asset)

    bundle, asset = ResPath.GetTwinDirectPurchaseImage(color_cfg.buy_bg)
	XUI.SetNodeImage(self.node_list.buy_bg, bundle, asset)

    local price_str = RoleWGData.GetPayMoneyStr(twin_cfg.price, twin_cfg.rmb_type, twin_cfg.rmb_seq)
    self.node_list.buy_price_text.text.text = ToColorStr(price_str, color_cfg.buy_price_text)

    self.ui_scene_change_config_index = color_cfg.ui_scene_config_index
    Scene.Instance:ChangeUISceneController(UI_SCENE_TYPE.DEFAULT, UI_SCENE_CONFIG_INDEX.TWIN_DIRECT_PURCHASE, color_cfg.ui_scene_config_index)
end

function TwinDirectPurchaseView:OnClickBuyBtn()
    local twin_cfg = TwinDirectPurchaseData.Instance:GetCurGradeCfgBySeq(self.cur_select_grade)
    if IsEmptyTable(twin_cfg) then
        return
    end

    RechargeWGCtrl.Instance:Recharge(twin_cfg.price, twin_cfg.rmb_type, twin_cfg.rmb_seq)
end

function TwinDirectPurchaseView:UpdateTimeStr(time)
    if self.node_list["act_txt"] then
        self.node_list["act_txt"].text.text = string.format(Language.TwinDirectPurchase.Act_Time_Segment, TimeUtil.FormatSecondDHM9(time))
    end
end

function TwinDirectPurchaseView:CleanActTimer()
    if self.act_timer and CountDown.Instance:HasCountDown(self.act_timer) then
        CountDown.Instance:RemoveCountDown(self.act_timer)
        self.act_timer = nil
    end
end

function TwinDirectPurchaseView:FlushModel()
    local data = TwinDirectPurchaseData.Instance:GetCurGradeCfgBySeq(self.cur_select_grade)
    if IsEmptyTable(data) then
        return
    end

    self.old_show_id = data.rmb_buy_flag

    local show_id = data.model_show_itemid
	local display_data = {}
	display_data.should_ani = true
	if show_id ~= 0 and show_id ~= "" then
		local split_list = string.split(show_id, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
            show_id = split_list[1]
		else
			display_data.item_id = show_id
		end
	end

    display_data.is_ui_scene_model = true
	display_data.bundle_name = data.model_bundle_name
	display_data.asset_name = data.model_asset_name
	display_data.render_type = data.model_show_type - 1
    display_data.model_rt_type = ModelRTSCaleType.L
	display_data.image_effect_bundle = data.image_effect_bundle
	display_data.image_effect_asset = data.image_effect_asset
    display_data.hide_model_block = false
    display_data.model_click_func = function ()
        TipWGCtrl.Instance:OpenItem({item_id = show_id})
    end

	if data.display_pos and data.display_pos ~= "" then
		local pos_list = string.split(data.display_pos, "|")
		local pos_x = tonumber(pos_list[1]) or 0
		local pos_y = tonumber(pos_list[2]) or 0
		local pos_z = tonumber(pos_list[3]) or 0

		display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
	end

	if data.display_rotation and data.display_rotation ~= "" then
		local rot_list = string.split(data.display_rotation, "|")
		local rot_x = tonumber(rot_list[1]) or 0
		local rot_y = tonumber(rot_list[2]) or 0
		local rot_z = tonumber(rot_list[3]) or 0

		display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
	end

	if data.display_scale and data.display_scale ~= "" then
		display_data.model_adjust_root_local_scale = data.display_scale
	end

	self.display_model:SetData(display_data)

	local item_cfg = ItemWGData.Instance:GetItemConfig(show_id)
    local display_type = item_cfg and item_cfg.is_display_role or 0

	self:FlushModePanel(display_type)

    if display_type == DisplayItemTip.Display_type.LNGYU or     --领域.
        display_type == DisplayItemTip.Display_type.WING then	--羽翼.
	    self.role_model_display:SetData(display_data)
		self.role_model_display:ShowCurRoleModel(nil, nil, true, true)
    end
end

function TwinDirectPurchaseView:FlushModePanel(res_type)
    if res_type == DisplayItemTip.Display_type.LNGYU or	    --领域.
        res_type == DisplayItemTip.Display_type.WING then	--羽翼.
        self.role_model_display:DisplayRemoveAllModel()
    end
end

function TwinDirectPurchaseView:OnSkillShowClick()
    local data = TwinDirectPurchaseData.Instance:GetCurGradeCfgBySeq(self.cur_select_grade)
    if IsEmptyTable(data) then
        return
    end

	local show_data = {
		icon = data.skill_icon,
		top_text = data.skill_name,
		body_text = data.skill_des,
		skill_level = 1,
		x = 0,
		y = 0,
		set_pos = true,
	}
	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

-- 点击专精预览
function TwinDirectPurchaseView:OnClickSpiritSkillShow()
    local data = TwinDirectPurchaseData.Instance:GetCurGradeCfgBySeq(self.cur_select_grade)
    if IsEmptyTable(data) then
        return
    end

    DujieWGCtrl.Instance:OpenDujieSpiritSkillPreView(data.nuqi_type)
end

------------ 档位toggle ------------
TwinDirectPurchaseToggleRender = TwinDirectPurchaseToggleRender or BaseClass(BaseRender)
function TwinDirectPurchaseToggleRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list.name.text.text = self.data.grade_name
end

function TwinDirectPurchaseToggleRender:OnSelectChange(is_select)
    self.node_list.hl_panel:SetActive(is_select)
end