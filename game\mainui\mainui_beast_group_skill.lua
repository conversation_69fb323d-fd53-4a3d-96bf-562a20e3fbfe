MainUiBeastGroupSkill = MainUiBeastGroupSkill or BaseClass(BaseRender)

function MainUiBeastGroupSkill:__init()
	self.m_skill_level = 1
	self.m_skill_cd = 0
end

function MainUiBeastGroupSkill:__delete()
	if self.cd_mask_tween then
		self.cd_mask_tween:Kill()
		self.cd_mask_tween = nil
	end

	CountDownManager.Instance:RemoveCountDown("mainui_beast_group_count_down")
end

function MainUiBeastGroupSkill:LoadCallBack()
	local beast_group_cell = self.node_list.beast_group_skill
	if beast_group_cell.event_trigger_listener then
		beast_group_cell.event_trigger_listener:AddPointerUpListener(BindTool.Bind(self.OnSkillUp, self, self.data))
		beast_group_cell.event_trigger_listener:AddDragListener(BindTool.Bind(self.OnDragSkill, self, self.data))
		beast_group_cell.event_trigger_listener:AddPointerDownListener(BindTool.Bind(self.OnClickRangSkill, self, self.data))
		beast_group_cell.event_trigger_listener:AddPointerExitListener(BindTool.Bind(self.CheckRangState, self, self.data))
	end
end

function MainUiBeastGroupSkill:SetParent(parent)
	self.parent = parent
end

function MainUiBeastGroupSkill:OnSkillUp(data)
	local view = MainuiWGCtrl.Instance:GetView()
	if view then
		local is_suc = view:OnSkillUp(self.data)
		if is_suc then
			self:FlushActiveStatus(false)
			local cfg = SkillWGData.Instance:GetBeastsSkillById(self.data.skill_id, self.m_skill_level)
			SkillWGData.Instance:SetSkillCD(SkillWGData.Instance:GetRealSkillIndex(self.data.skill_id), cfg and cfg.cd_s or 0)
		end
	end
end

function MainUiBeastGroupSkill:OnDragSkill(data, eventData)
	local view = MainuiWGCtrl.Instance:GetView()
	if view then
		view:OnDragSkill(self.data, eventData)
	end
end

function MainUiBeastGroupSkill:OnClickRangSkill(data)
	local view = MainuiWGCtrl.Instance:GetView()
	if view then
		view:OnClickRangSkill(self.data)
	end
end

function MainUiBeastGroupSkill:CheckRangState(data)
	local view = MainuiWGCtrl.Instance:GetView()
	if view then
		view:CheckRangState(self.data)
	end
end

function MainUiBeastGroupSkill:SetData(data)
	if IsEmptyTable(data) then
		return
	end

	self.data = data
	self.data.skill_index = data.index
	self:Flush()
end

function MainUiBeastGroupSkill:OnFlush()
	if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end

	-- 技能类型
	local skill_type_tab = SkillWGData.Instance:GetSkillAttackType(self.data.skill_id)
	if self.node_list.skill_type ~= nil then	-- 加个容错
		if skill_type_tab and skill_type_tab[1] and tonumber(skill_type_tab[1]) ~= 0 then
			self.node_list.skill_type:SetActive(true)
			self.node_list.skill_type.text.text = Language.Skill.CommonSkillType[skill_type_tab[1]] or ""
		else
			self.node_list.skill_type:SetActive(false)
		end
	end

	local clien_skill_cfg = SkillWGData.Instance:GetSkillClientConfig(self.data.skill_id, self.m_skill_level)
	if clien_skill_cfg then
		self.node_list.icon.image:LoadSprite(ResPath.GetSkillIconById(clien_skill_cfg.icon_resource))
	end

	-- self:FlushActiveStatus(false)
end

function MainUiBeastGroupSkill:FlushActiveStatus(active_status)
	if not self.data then
		return
	end

	--当需要展示的时候需要检测其冷却时间
	if active_status then
		local skill_end_time = SkillWGData.Instance:GetSkillCDEndTime(self.data.skill_index,self.data.skill_id)
		if skill_end_time <= Status.NowTime * 1000 then -- 还在cd中
			self.view:SetActive(active_status)
		end
	else
		self.view:SetActive(active_status)
	end
end
