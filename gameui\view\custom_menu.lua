CustomMenu = CustomMenu or BaseClass(SafeBaseView)
CustomMenu.WIDTH = 280
CustomMenu.MIN_HEIGHT = 100
CustomMenu.ITEM_HEIGHT = 78
CustomMenu.BTN_BTN_INTERVAL = 0
CustomMenu.BG_INTERVAL = 15
CustomMenu.FLOERT_INTERVAL_X = 5.5
CustomMenu.FLOERT_INTERVAL_Y = 1.5
CustomMenu.BTN_INTERVAL_X = 21
CustomMenu.BTN_INTERVAL_Y = 18

local HeightMax = 400						--最大高度

function CustomMenu:__init()
	self:AddViewResource(0, "uis/view/miscpre_load_prefab", "ListDetail")
	self:SetMaskBg(true, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self.view_layer = UiLayer.Pop
	self.view_name = "CustomMenu"
	self.is_any_click_close = true
	self.callback_func = nil						-- 点击回调
	self.callback_param = nil						-- 点击回调参数
	self.close_callback_func = nil					-- 关闭回调
	self.cell_list = {}
	self.list_data = {}
end

function CustomMenu:__delete()
	
end

function CustomMenu:ReleaseCallBack()
	for k, v in pairs(self.cell_list) do
		v:DeleteMe()
	end
	self.cell_list = {}

	self.panel = nil
	self.left = nil
	self.btn_list = nil
	self.Frame = nil
	self.list_data = {}
	self.red_point_list = nil
	self.info = nil
	self.player_info = nil

	self.callback_param = nil
	self.callback_func = nil
	self.close_callback_func = nil

	if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end

end

function CustomMenu:LoadCallBack()
	self.panel = self.node_list.Panel
	self.left = self.node_list.Left
	self.Frame = self.node_list.Frame
	--self:ListenEvent("CloseWindow", BindTool.Bind(self.CloseWindow, self))
	self.left.button:AddClickListener(BindTool.Bind(self.CloseWindow, self))
	if self.pos then
		self.panel.rect.anchoredPosition = self.pos
	else
		self.panel.rect.anchoredPosition = Vector2(0,0)
	end
	-- 生成滚动条
	self.panel:SetActive(true)
	self.btn_list = self.node_list["ButtonList"]
	local list_delegate = self.btn_list.list_simple_delegate

	self.cell_height = list_delegate:GetCellViewSize(self.btn_list.scroller, 0)			--单个cell的大小（根据排列顺序对应高度或宽度）
	self.list_spacing = self.btn_list.scroller.spacing
	self.list_padding_top = self.btn_list.scroller.padding.top
	self.list_padding_bottom = self.btn_list.scroller.padding.bottom

		--生成数量
	list_delegate.NumberOfCellsDel = function()
		return math.ceil((#self.list_data or 0)/2) 
	end
	--刷新函数
	list_delegate.CellRefreshDel = function(cell, data_index, cell_index)
		data_index = data_index + 1

		local btn_cell = self.cell_list[cell]
		if btn_cell == nil then
			btn_cell = ListButtonCell.New(cell.gameObject, self)
			btn_cell.list_detail_view = self
			btn_cell:SetClickCallBack(BindTool.Bind(self.ClickBtnCell, self))
			self.cell_list[cell] = btn_cell
		end

		btn_cell:SetIndex(data_index)
		local cell_index1 = (data_index -1) *2 + 1
		local cell_index2 = (data_index -1) *2 + 2
		if self.red_point_list then
			btn_cell:SetRemind(self.red_point_list[cell_index1],self.red_point_list[cell_index2])
		end
		
		local data = {}
		data[1] = self.list_data[cell_index1] or ""
		data[2] = self.list_data[cell_index2] or ""
		btn_cell:SetData(data)
	end

	
	-- self.btn_list.scroller:ReloadData(0)
end

function CustomMenu:CloseWindow()
    self:Close()
end

function CustomMenu:ClickBtnCell(item)
	if not item then
		return
	end
	if self.callback_func then
		local real_index = (item:GetIndex()-1) *2 + item:GetClickIndex()
		local data = item:GetData()
		self.callback_func(real_index,nil, self.callback_param, data)
		self:Close()
	end
end

function CustomMenu:OpenCallBack()

end

function CustomMenu:CloseCallBack()
	self.pos = nil
	if nil ~= self.close_callback_func then
		self.close_callback_func()
	end
	self.red_point_list = nil
end

function CustomMenu:GetView()
	return self.view
end

-- player_info = { 
--	user_id
-- 	is_online 0-1
--  server_id
--  plat_type
-- }
function CustomMenu:SetMenuData(menu_data, player_info)
	self.list_data = menu_data
	self.player_info = player_info or {}
end

function CustomMenu:SetMaskAlpha(alpha_type)
	local type = alpha_type and alpha_type or MASK_BG_ALPHA_TYPE.Zero
	self:SetMaskBgAlpha(type)
end

function CustomMenu:SetRedPointList(red_point_list)
	self.red_point_list = red_point_list
end

function CustomMenu:OnFlush()
	self:ChangePanelHeight()
	self.btn_list.scroller:ReloadData(0)
	self:FlushPlayerMessage()
end

function CustomMenu:FlushPlayerMessage()
	self.node_list.name:SetActive(false)
    self.node_list.power:SetActive(false)
    self.node_list.guild:SetActive(false)
    -- self.node_list.default_head_icon:SetActive(false)
    self.node_list.custom_head_icon:SetActive(false)

	if self.player_info and not IsEmptyTable(self.player_info) then
		local user_id = 0
		if self.player_info.user_id then
			user_id = self.player_info.user_id
		elseif self.player_info.role_id then
			user_id = self.player_info.role_id
		elseif self.player_info.orgin_role_id then
			user_id = self.player_info.orgin_role_id
		elseif self.player_info.role_original_id then
			user_id = self.player_info.role_original_id
        end

        if user_id == COMMON_CONSTS.GUILD_MENGZHU_ROBOT_ID then
            local cfg = GuildWGData.Instance:GetGuildRobotCfgByRoleName(self.player_info.role_name)
            self.player_info.sex = cfg.sex
            self.player_info.prof = cfg.prof 
            self.player_info.capability = cfg.mengzhu_cap or 0
            self.player_info.guild_name = cfg.guild_name
            self:FlushDetailMessage(self.player_info)
        else
        	local is_cross = not self:IsSameServer()
            BrowseWGCtrl.Instance:BrowRoelInfo(user_id, BindTool.Bind(self.FlushDetailMessage,self), self.player_info.plat_type,is_cross)
        end
	end
end

function CustomMenu:IsSameServer()
	if IsEmptyTable(self.player_info) then
		return true
    end

    --没传server_id 默认本服玩家
    if self.player_info.server_id == nil then
        return true
    end
	local main_role_server_id = RoleWGData.Instance:GetMergeServerId()
	local main_role_plat_type = RoleWGData.Instance:GetPlatType()
	return self.player_info.server_id == main_role_server_id and self.player_info.plat_type == main_role_plat_type
end

function CustomMenu:FlushDetailMessage(info)
    if self.node_list and self.node_list.default_head_icon then
        if info and not IsEmptyTable(info) then
            self.info = info
            local user_id = self.info.role_id and self.info.role_id or self.player_info.user_id	
            local sex = self.info.sex and self.info.sex or self.player_info.sex	
            local prof = self.info.prof and self.info.prof or self.player_info.prof	
            local name = self.info.role_name or ""

            if IS_ON_CROSSSERVER then
                name = name.."_s"..info.server_id
            end

            				 --self.info.is_online == 0:不在线 1:在线 2:离线挂机 3:跨服在线
            local is_online = (self.info.is_online == 0 or self.info.is_online == 2) and Language.Common.GreenOutLine or Language.Common.GreenOnLine
            local power = self.info.capability and self.info.capability or "0"
            local guild_name = (self.info.guild_name and self.info.guild_name ~= "" ) and self.info.guild_name or Language.Society.CheckNoGuild
            
            -- XUI.UpdateRoleHead(self.node_list["default_head_icon"], self.node_list["custom_head_icon"], user_id, sex ,prof, self.info.is_online ~= 1, nil, true)
            local fashion_photoframe = 0
            local cur_fashion_photoframe = AvatarManager.Instance:GetAvatarFrameKey(user_id)
            if nil ~= cur_fashion_photoframe and cur_fashion_photoframe >= 0 then
                fashion_photoframe = cur_fashion_photoframe
            else
                fashion_photoframe = info.appearance and info.appearance.fashion_photoframe or 0
            end
            
            if not self.head_cell then
                self.head_cell = BaseHeadCell.New(self.node_list["default_head_icon"])
                local index = AvatarManager.Instance:GetAvatarKey(user_id, true)
                if index <= GameEnum.CUSTOM_HEAD_ICON then --系统头像
                    self.head_cell:SetIsBgClick(false)
                else
                    self.head_cell:SetIsBgClick(true)
                end
            end
            self.head_cell:SetData({role_id = user_id, sex = sex, prof = prof, fashion_photoframe = fashion_photoframe})
            self.node_list.name.text.text = name..is_online
            -- self.node_list.power.text.text = Language.Society.CheckPower..power
            self.node_list.guild.text.text = Language.Society.CheckGuild..guild_name
            self.node_list.name:SetActive(true)
            -- self.node_list.power:SetActive(true)
            self.node_list.guild:SetActive(true)
        else
            self.node_list.name.text.text = Language.Society.CheckNo..Language.Common.GreenOutLine
            -- self.node_list.power.text.text = Language.Society.CheckPower.."0"
            self.node_list.guild.text.text = Language.Society.CheckGuild..Language.Society.CheckNoGuild
            self.node_list.name:SetActive(true)
            -- self.node_list.power:SetActive(true)
            self.node_list.guild:SetActive(true)
        end
    end
end

function CustomMenu:BindCallBack(callback_func, callback_param)
	self.callback_func = callback_func
	self.callback_param = callback_param
end

function CustomMenu:BindCloseCallBack(callback_func)
	self.close_callback_func = callback_func
end

--改变列表长度
function CustomMenu:ChangePanelHeight()
	local item_count = math.ceil(#self.list_data / 2)
	local panel_Width = self.panel.rect.rect.width
	local panel_height = self.cell_height * item_count + self.list_spacing * (item_count - 1) + self.list_padding_top + self.list_padding_bottom + 118			--30是listview和底框的间距和
	
	if panel_height > HeightMax then
	 	panel_height = HeightMax
	end
	self.Frame.rect.sizeDelta = Vector2(panel_Width, panel_height)
end

function CustomMenu:CreateMenu(items)
	
end

--设置上升标记
function CustomMenu:SetCanOperateIconVisible(cell, is_visible)
	
end

function CustomMenu:CreateImage(path, cell)
	
end


function CustomMenu:OnCellClickHandler(index, sender)
	
end

function CustomMenu:SetPosition(pos)
	self.pos = pos or Vector2(0, 0)
	if self.pos and self.panel then
		self.panel.rect.anchoredPosition = self.pos
	end
end


----------------------------------------
-------------------ListButtonCell
----------------------------------------
ListButtonCell = ListButtonCell or BaseClass(BaseRender)
function ListButtonCell:__init()
	self.node_list.Button.button:AddClickListener(BindTool.Bind(self.ClickBtnCell, self,1))
	self.node_list.Button2.button:AddClickListener(BindTool.Bind(self.ClickBtnCell, self,2))
	-- self.on_point_down = BindTool.Bind1(self.OnPointDown, self)
	-- self.on_point_up = BindTool.Bind1(self.OnPointUp, self)
	-- self.on_point_exit = BindTool.Bind1(self.OnPointExit, self)
	-- self.event_listener = self.node_list["Button"].event_trigger_listener
	-- if self.event_listener ~= nil then
	-- 	self.event_listener:AddPointerDownListener(self.on_point_down)		--按下
	-- 	self.event_listener:AddPointerExitListener(self.on_point_exit)
	-- 	self.event_listener:AddPointerUpListener(self.on_point_up)
	-- end
end

-- function ListButtonCell:OnPointDown(event_data)
-- 	self.node_list["HLText"]:SetActive(true)
-- 	self.node_list["Text"]:SetActive(false)
-- end
-- function ListButtonCell:OnPointUp(event_data)
-- 	self.node_list["HLText"]:SetActive(false)
-- 	self.node_list["Text"]:SetActive(true)
-- end
-- function ListButtonCell:OnPointExit(event_data)
-- 	self.node_list["HLText"]:SetActive(false)
-- 	self.node_list["Text"]:SetActive(true)
-- end


function ListButtonCell:__delete()
	self.on_point_down = nil
	self.on_point_up = nil
	self.on_point_exit = nil
	self.list_detail_view = nil
	self.event_listener = nil
	self.remind_flag = nil
end

function ListButtonCell:OnFlush()
	if self.data[1] and self.data[1] ~= "" then
		self.node_list.Text.text.text = self.data[1] or ""
		self.node_list.Button:SetActive(true)
	else
		self.node_list.Button:SetActive(false)
	end

	if self.data[2] and self.data[2] ~= "" then
		self.node_list.Text2.text.text = self.data[2] or ""
		self.node_list.Button2:SetActive(true)
	else
		self.node_list.Button2:SetActive(false)
	end

	-- if self.node_list["HLText"] then
	-- 	self.node_list["HLText"].text.text = self.data[1]
	-- end

	if self.remind_flag1 ~= nil then
		self.node_list.Remind:SetActive(self.remind_flag1 > 0)
	end

	if self.remind_flag2 ~= nil then
		self.node_list.Remind2:SetActive(self.remind_flag2 > 0)
	end
end

function ListButtonCell:ClickBtnCell(index)
	self.click_index = index
	BaseRender.OnClick(self,index)
end

function ListButtonCell:GetClickIndex()
	return self.click_index or 1
end

function ListButtonCell:SetRemind(remind_flag1,remind_flag2)
	self.remind_flag1 = remind_flag1
	self.remind_flag2 = remind_flag2
end