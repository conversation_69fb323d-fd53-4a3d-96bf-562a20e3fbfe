-----------------------------------
-- 百亿补贴
-----------------------------------
BillionSubsidyActiveVIPView = BillionSubsidyActiveVIPView or BaseClass(SafeBaseView)

local EFFECT_LEVEL = {
	[1] = Ui_Effect.UI_bybt_huangjin,
	[2] = Ui_Effect.UI_bybt_heijin,
	[3] = Ui_Effect.UI_bybt_liujin,
}

function BillionSubsidyActiveVIPView:__init()
	self.view_layer = UiLayer.Pop
	self.view_style = ViewStyle.Window
	self.is_safe_area_adapter = true
	self:SetMaskBg(true, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)

	self:AddViewResource(0, "uis/view/billion_subsidy_ui_prefab", "layout_billion_subsidy_active_vip")
end

function BillionSubsidyActiveVIPView:LoadCallBack()
	if not self.vip_item_list then
		self.vip_item_list = {}
		for i = 1, 3 do
			self.vip_item_list[i] = ActiveVIPSelectionItem.New(self.node_list["vip_item_" .. i])
		end
	end

	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.BillionSubsidyActiveVip, self.get_guide_ui_event)
end

function BillionSubsidyActiveVIPView:ReleaseCallBack()
	if self.vip_item_list then
		for k, v in pairs(self.vip_item_list) do
			v:DeleteMe()
		end
		self.vip_item_list = nil
	end

	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.BillionSubsidyActiveVip, self.get_guide_ui_event)
	self.get_guide_ui_event = nil
end

function BillionSubsidyActiveVIPView:CloseCallBack()
	
end

function BillionSubsidyActiveVIPView:OnFlush(param_t, index)
	local benefits_cfg_list = BillionSubsidyWGData.Instance:GetMenberBenefitsList()
	for i, v in ipairs(self.vip_item_list) do
		v:SetData(benefits_cfg_list[i])
	end
end

function BillionSubsidyActiveVIPView:PlayAnim()
	for i = 2, 3 do
		if self.vip_item_list[i] then
			self.vip_item_list[i]:PlayAnim()
		end
	end
end

function BillionSubsidyActiveVIPView:StopAnim()
	for i = 2, 3 do
		if self.vip_item_list[i] then
			self.vip_item_list[i]:StopAnim()
		end
	end
end

function BillionSubsidyActiveVIPView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.BillionSubsidyActiveVipHigh then
		if nil == self.node_list.high_vip_show_node then
			return
		end

		self:PlayAnim()
		return self.node_list.high_vip_show_node, BindTool.Bind(self.StopAnim, self)
	end

	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

-------------------------------------------
-- 会员激活Item
-------------------------------------------
ActiveVIPSelectionItem = ActiveVIPSelectionItem or BaseClass(BaseRender)
function ActiveVIPSelectionItem:LoadCallBack()
	if not self.benefits_item_grid then
		self.benefits_item_grid = AsyncBaseGrid.New()
		self.benefits_item_grid:SetStartZeroIndex(false)
		self.benefits_item_grid:CreateCells({
			col = 3,
			assetName = "benifits_item",
			assetBundle = "uis/view/billion_subsidy_ui_prefab",
			list_view = self.node_list["benefits_item_list"],
			itemRender = BenifitsItem,
			change_cells_num = 1,
		})
	end

	if not self.active_reward_list then
		self.active_reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
		self.active_reward_list:SetStartZeroIndex(true)
	end

	XUI.AddClickEventListener(self.node_list["btn_active"], BindTool.Bind(self.OnClickActiveMenberBtn, self))
end

function ActiveVIPSelectionItem:ReleaseCallBack()
	if self.benefits_item_grid then
		self.benefits_item_grid:DeleteMe()
		self.benefits_item_grid = nil
	end

	if self.active_reward_list then
		self.active_reward_list:DeleteMe()
		self.active_reward_list = nil
	end
end

function ActiveVIPSelectionItem:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end
	self.benefits_item_grid:SetDataList(self.data.cfg)

	local member_level = BillionSubsidyWGData.Instance:GetMemberLevel()
	local member_rmb_buy_cfg = BillionSubsidyWGData.Instance:GetMenberRmbBuy(self.data.member_level)
	local had_rmb_buy_cfg = not IsEmptyTable(member_rmb_buy_cfg)
	self.node_list["title"]:SetActive(had_rmb_buy_cfg)
	self.node_list["reward_list"]:SetActive(had_rmb_buy_cfg)
	self.node_list["btn_active"]:SetActive(had_rmb_buy_cfg)
	self.node_list["actived_flag"]:SetActive(not had_rmb_buy_cfg)
	self.node_list["light_bg"]:SetActive(member_level == self.data.member_level)
	if had_rmb_buy_cfg then
		self.active_reward_list:SetDataList(member_rmb_buy_cfg.reward_show)
		self.node_list["btn_active"]:SetActive(member_level < self.data.member_level)
		self.node_list["actived_flag"]:SetActive(member_level >= self.data.member_level)

		local price_str = RoleWGData.GetPayMoneyStr(member_rmb_buy_cfg.price, member_rmb_buy_cfg.rmb_type, member_rmb_buy_cfg.rmb_seq)
		local str = string.format(Language.BillionSubsidy.ActiveRMB, price_str)
		self.node_list["txt_active_btn_text"].text.text = str
	end
end

function ActiveVIPSelectionItem:OnClickActiveMenberBtn()
	local member_level = BillionSubsidyWGData.Instance:GetMemberLevel()
	local member_rmb_buy_cfg = BillionSubsidyWGData.Instance:GetMenberRmbBuy(self.data.member_level)
	if IsEmptyTable(member_rmb_buy_cfg) then
		return
	end
	
	RechargeWGCtrl.Instance:Recharge(member_rmb_buy_cfg.price, member_rmb_buy_cfg.rmb_type, member_rmb_buy_cfg.rmb_seq)
end

function ActiveVIPSelectionItem:PlayAnim()
	local list = self.benefits_item_grid:GetAllCell()
	local sort_list = GetSortListView(list)
	for k,v in ipairs(sort_list) do
		v.item:PlayItemAnim()
	end
end

function ActiveVIPSelectionItem:StopAnim()
	local list = self.benefits_item_grid:GetAllCell()
	local sort_list = GetSortListView(list)
	for k,v in ipairs(sort_list) do
		v.item:StopItemAnim()
	end
end

-------------------------------------------
-- 特权Item
-------------------------------------------
BenifitsItem = BenifitsItem or BaseClass(BaseRender)
function BenifitsItem:ReleaseCallBack()
	self:StopItemAnim()
end

function BenifitsItem:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end
	local bundle, asset = ResPath.GetBillionSubsidyImg("a3_bybt_hy_" .. self.data.required_level)
	self.node_list["img_bg"].image:LoadSprite(bundle, asset, function()
		self.node_list["img_bg"].image:SetNativeSize()
	end)
	bundle, asset = ResPath.GetBillionSubsidyImg(self.data.benefits_icon)
	self.node_list["img_icon"].image:LoadSprite(bundle, asset, function()
		self.node_list["img_icon"].image:SetNativeSize()
	end)
	self.node_list["txt_benefits_name"].text.text = self.data.benefits_name
	self.node_list["txt_benefits_desc"].text.text = self.data.benefits_desc
	self.node_list["img_up_arrow"]:SetActive(self.data.is_show_arrow ~= 0)
end

function BenifitsItem:PlayItemAnim()
	self:StopItemAnim()

	self.benifits_item_tween = DG.Tweening.DOTween.Sequence()
	BenifitsItem.ShakeAnimi(self.node_list.img_bg.transform, self.benifits_item_tween)
end

function BenifitsItem:StopItemAnim()
	if self.benifits_item_tween then
		self.benifits_item_tween:Kill()
		self.benifits_item_tween = nil
	end
	self.node_list.img_bg.rect.localRotation = Quaternion.Euler(0, 0, 0)
end

function BenifitsItem.ShakeAnimi(trans, sequence, interval_time)
	interval_time = interval_time or 2
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 0), 1, DG.Tweening.RotateMode.Fast))
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 8), 0.1, DG.Tweening.RotateMode.Fast))
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, -15), 0.1, DG.Tweening.RotateMode.Fast))
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 15), 0.1, DG.Tweening.RotateMode.Fast))
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, -14), 0.1, DG.Tweening.RotateMode.Fast))
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 14), 0.1, DG.Tweening.RotateMode.Fast))
    sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 0), 0.1, DG.Tweening.RotateMode.Fast))
    sequence:AppendInterval(interval_time)
    sequence:SetEase(DG.Tweening.Ease.Linear)
    sequence:SetLoops(-1)
end