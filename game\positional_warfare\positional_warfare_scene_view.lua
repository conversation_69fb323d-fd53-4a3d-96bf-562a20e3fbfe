--场景内view
PositionalWarfareSceneView = PositionalWarfareSceneView or BaseClass(SafeBaseView)

function PositionalWarfareSceneView:__init()
	self.is_safe_area_adapter = true
    self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_positional_warfare_scene_view")
end

function PositionalWarfareSceneView:OpenCallBack()
	if self.obj and self:IsLoadedIndex(0) then
        self.obj:SetActive(true)
    end
end

function PositionalWarfareSceneView:LoadCallBack()
    MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))

    if not self.boss_list then
        self.boss_list = AsyncListView.New(PWSceneBossItemCellRender, self.node_list.boss_list)
		self.boss_list:SetStartZeroIndex(false)
		self.boss_list:SetSelectCallBack(BindTool.Bind(self.OnClickBossItem, self))

		local _, boss_seq_cache = PositionalWarfareWGData.Instance:GetEnterSceneSelectDataCache()
		self.jump_to_boss_index = -1

		if boss_seq_cache < 0 then
			self.boss_list:SetDefaultSelectIndex(nil)
		else
			local boss_data = PositionalWarfareWGData.Instance:GetCurSceneBossDataList(true)

			for k, v in pairs(boss_data) do
				if v.seq == boss_seq_cache then
					self.boss_list:SetDefaultSelectIndex(k)
					self.jump_to_boss_index = k
					break
				end
			end
		end
    end

    if not self.hurt_list then
        self.hurt_list = AsyncListView.New(PWSceneHurtItemCellRender, self.node_list.hurt_list)
		self.hurt_list:SetStartZeroIndex(false)
    end

	self.select_boss_info = {}
	self.sync_nav_time = 0
	self.is_sync_nav = false
	self.is_in_fight_state = false

	self:UpdateView()
    self.refresh_event = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.UpdateView,self), 1)
	XUI.AddClickEventListener(self.node_list.btn_nuqi, BindTool.Bind(self.OnClickNuQiBtn, self))
end

function PositionalWarfareSceneView:UpdateView()
    if self.node_list.desc_act_time then
		local time_str, is_in_fight_time = PositionalWarfareWGData.Instance:GetActivityTimeInfo()
        self.node_list.desc_act_time.text.text = time_str

		if self.is_in_fight_state and not is_in_fight_time then
			RemindManager.Instance:Fire(RemindName.PositionalWarfare)
			self:Flush()
		end
		
		self.is_in_fight_state = is_in_fight_time
    end
end

function PositionalWarfareSceneView:InitCallBack()
	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()
	if self.node_list["task_root"] then
		self.obj = self.node_list["task_root"].gameObject
		self.obj.transform:SetParent(parent.gameObject.transform)
		self.obj.transform.localPosition = Vector3(0, 0, 0)
		self.obj.transform.localScale = Vector3.one
	end

    if self.is_out_fb then
        self.obj:SetActive(false)
    end

    self.is_out_fb = nil
end

function PositionalWarfareSceneView:CloseCallBack()
	self.is_out_fb = true
    if self.obj then
        self.obj:SetActive(false)
    end
end

function PositionalWarfareSceneView:ReleaseCallBack()
	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end

    if self.boss_list then
        self.boss_list:DeleteMe()
        self.boss_list = nil
    end

    if self.hurt_list then
        self.hurt_list:DeleteMe()
        self.hurt_list = nil
    end

	self.select_boss_info = nil
	self.is_sync_nav = nil

	if self.refresh_event then
		GlobalTimerQuest:CancelQuest(self.refresh_event)
		self.refresh_event = nil
	end

	self.jump_to_boss_index = nil
end

function PositionalWarfareSceneView:ShowIndexCallBack()
	if self.obj then
        self.obj:SetActive(true)
    end
end

function PositionalWarfareSceneView:OnFlush(param_t, index)
    for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushLeftBossInfo()
		elseif k == "rank_info"then
			self:FlushRankInfo(v)
        end
    end
end

function PositionalWarfareSceneView:FlushLeftBossInfo()
	local bass_data_list = PositionalWarfareWGData.Instance:GetCurSceneBossDataList(true)
	self.boss_list:SetDataList(bass_data_list)

	if self.jump_to_boss_index >= 0 then
		self.jump_to_boss_index = -1
		ReDelayCall(self, function ()
			self.boss_list:JumpToIndex(self.jump_to_boss_index)
		end, 3, "PositionalWarfareSceneView")
	end

	local max_tired = PositionalWarfareWGData.Instance:GetOtherAttrValue("max_tired")
    local tired = PositionalWarfareWGData.Instance:GetTired()
	local value = tired / max_tired
    self.node_list.desc_tired.text.text = tired .. "/".. max_tired -- string.format("%s%%", math.floor(value * 100))
	self.node_list.tired_pro.image.fillAmount = value

	local land_seq_cache = PositionalWarfareWGData.Instance:GetEnterSceneSelectDataCache()
	local city_info = PositionalWarfareWGData.Instance:GetLandInfoBySeq(land_seq_cache)
	local camp_score_list = (city_info or {}).camp_score_list or {}
	local my_camp = PositionalWarfareWGData.Instance:GetMyCamp()
	local camp_score = camp_score_list[my_camp] or 0
	self.node_list.desc_camp_score.text.text = string.format(Language.PositionalWarfare.SceneCampScore, camp_score)

	local owner_camp = (city_info or {}).owner_camp or -1
	local tem_owner_camp = (city_info or {}).tem_owner_camp or -1
	local target_camp = tem_owner_camp >= 0 and tem_owner_camp or (owner_camp >= 0 and owner_camp or -1)

	local owner_name = Language.PositionalWarfare.Neutral

	if target_camp >= 0 then
		local cur_camp_cfg = PositionalWarfareWGData.Instance:GetCurCampCfg(target_camp)
		owner_name = cur_camp_cfg.camp_name
	end

	self.node_list.desc_owner_camp.text.text = string.format(Language.PositionalWarfare.SceneLandOwner, owner_name)
end

function PositionalWarfareSceneView:FlushRankInfo(info)
	if info.info_count > 0 then
		self.hurt_list:SetDataList(info.hurt_info)
		self.node_list.my_damate.text.text = CommonDataManager.ConverExpByThousand(info.role_hurt)
		self.node_list.my_hurt_info.slider.value = info.role_hurt / BossAssistWGData.Instance:GetNormalHurtInfoMaxValue()
		self.node_list.my_rank.text.text = info.role_rank
		self.node_list.both_move_right_node:CustomSetActive(true)
	else
		self.node_list.both_move_right_node:CustomSetActive(false)
	end

	local boss_cfg = BossWGData.Instance:GetMonsterCfgByid(info.monster_id)
	self.node_list.desc_drop.text.text = boss_cfg and string.format(Language.PositionalWarfare.DemageGetReward, boss_cfg.drop_param) or ""

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if self.sync_nav_time + 5 > server_time then
		return
	end

	local boss_object = Scene.Instance:GetObjectByObjId(info.monster_obj_id)

	if boss_object then
		local boss_vo = boss_object:GetVo()
		if not IsEmptyTable(boss_vo) and boss_vo.special_param > 0 then
			local boss_seq = boss_vo.special_param % 1000
			local land_seq = math.floor(boss_vo.special_param / 1000) - 1
			local monster_group = math.floor(boss_vo.special_param / 100000) - 1

			local boss_data = PositionalWarfareWGData.Instance:GetCurSceneBossDataList(trues)
			local jump_index = -1

			for k, v in pairs(boss_data) do
				if v.seq == boss_seq then
					jump_index = k
					break
				end
			end

			if jump_index >= 0 then
				local select_index = self.boss_list:GetSelectIndex()

				if jump_index ~= select_index then
					self.is_sync_nav = true
					self.boss_list:JumpToIndex(jump_index)
				end
			end
		end
	end
end

function PositionalWarfareSceneView:OnClickBossItem(item, cell_index, is_default, is_click)
	if nil == item or IsEmptyTable(item.data) then
		return
	end

	if not is_click then
		if self.is_sync_nav then
			self.is_sync_nav = false
			return
		end
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if is_click then
		self.sync_nav_time = server_time
	end

	local data = item.data
	self.select_boss_info = data

	local sence_id = Scene.Instance:GetSceneId()
    local role = Scene.Instance:GetMainRole()
	if role == nil then
		return
	end

	local pw_boss_cfg = PositionalWarfareWGData.Instance:GetCurMonsterCfg(data.land_seq, data.seq)
	if IsEmptyTable(pw_boss_cfg) then
		return
	end

    local role_x, role_y = role:GetLogicPos()
	local pos_data = Split(pw_boss_cfg.monster_pos, ",")
	local pos_x, pos_y = pos_data[1],  pos_data[2]

	if role_x == pos_x and role_y == pos_y then
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	else
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
		GuajiWGCtrl:StopGuaji()
		AtkCache.target_obj = nil

		local boss_info = PositionalWarfareWGData.Instance:GetCurSceneBossInfo(data.seq)
		-- local boss_active = false

		-- if boss_info and boss_info.is_die == 0 then
		-- 	boss_active = true
		-- end

		-- if boss_active then
		-- 	MoveCache.SetEndType(MoveEndType.FightByMonsterId)
		-- else
		-- 	MoveCache.SetEndType(MoveEndType.Normal)
		-- end
		MoveCache.SetEndType(MoveEndType.Auto)
		
		MoveCache.param1 = data.monster_id
		GuajiCache.monster_id = data.monster_id
		-- GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		local range = BossWGData.Instance:GetMonsterRangeByid(data.monster_id)
		GuajiWGCtrl.Instance:MoveToPos(sence_id, pos_x, pos_y, range, nil, nil, nil, function()
		end)
	end
end

function PositionalWarfareSceneView:OnClickNuQiBtn()
	PositionalWarfareWGCtrl.Instance:OpenNuQiTip()	
end

-------------------------------------PWSceneBossItemCellRender---------------------------------
PWSceneBossItemCellRender = PWSceneBossItemCellRender or BaseClass(BaseRender)
function PWSceneBossItemCellRender:__delete()
    if self.refresh_event then
		GlobalTimerQuest:CancelQuest(self.refresh_event)
		self.refresh_event = nil
	end
end

function PWSceneBossItemCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		return 
	end

    local boss_cfg = BossWGData.Instance:GetMonsterCfgByid(self.data.monster_id)
	self.node_list.TextDesc.text.text = boss_cfg.name

	-- local boss_info = PositionalWarfareWGData.Instance:GetMonsterCityBossInfo(self.data.land_seq, self.data.seq)
	-- local camp = boss_info and boss_info.owner_camp or -1

	local boss_info = PositionalWarfareWGData.Instance:GetCurSceneBossInfo(self.data.seq)
	local camp = boss_info and boss_info.owner_camp or -1

	if camp >= 0 then
		local cur_camp_cfg = PositionalWarfareWGData.Instance:GetCurCampCfg(camp)
		-- self.node_list.OwnerDesc.text.text = cur_camp_cfg.camp_name
		self.node_list.OwnerDesc.text.text = ""
		self.node_list.OwnerIcon.image:LoadSprite(ResPath.GetPositionalWarfareImg("a3_zdz_tt_" .. cur_camp_cfg.camp_sign))
	else
		self.node_list.OwnerDesc.text.text = Language.PositionalWarfare.Neutral
	end

	self.node_list.OwnerIcon:CustomSetActive(camp >= 0)

    self:RefreshRemainTime()
	if self.refresh_event then
		GlobalTimerQuest:CancelQuest(self.refresh_event)
		self.refresh_event = nil
	end
	self.refresh_event = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.RefreshRemainTime, self), 1)
end

function PWSceneBossItemCellRender:RefreshRemainTime()
	if self.data then
		local boss_info = PositionalWarfareWGData.Instance:GetCurSceneBossInfo(self.data.seq)

		if boss_info and boss_info.is_die == 0 then
			self.node_list.TimeDesc.text.text = Language.PositionalWarfare.BossRefresh
			
			if self.refresh_event then
				GlobalTimerQuest:CancelQuest(self.refresh_event)
				self.refresh_event = nil
			end
		else
			local has_next_refresh_time, time = PositionalWarfareWGData.Instance:GetBossNextRefreshTime()
			
			local time_str = ""
			local server_time = TimeWGCtrl.Instance:GetServerTime()
			if has_next_refresh_time and time > server_time then
				time_str = ToColorStr(TimeUtil.FormatSecond(time - server_time, 3), COLOR3B.RED)
			else
				time_str = ToColorStr(Language.PositionalWarfare.BossReBirthState, COLOR3B.RED)
			end

			self.node_list.TimeDesc.text.text = time_str
		end
	end
end

function PWSceneBossItemCellRender:OnSelectChange(is_select)
	if self.node_list["SelectLigth"] then
		self.node_list["SelectLigth"]:SetActive(is_select)
	end
end

-------------------------------------PWSceneHurtItemCellRender---------------------------------
PWSceneHurtItemCellRender = PWSceneHurtItemCellRender or BaseClass(BaseRender)

function PWSceneHurtItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
		return
	end

	self.node_list.num.text.text = self.index > 3 and self.index or ""
	self.node_list.name.text.text = self.data.name
	self.node_list.damage.text.text = CommonDataManager.ConverExpByThousand(self.data.hurt)
	local asset = self.index < 4 and "a3_hurt_list_bg_" .. self.index or "a3_hurt_list_bg_4"
	local bg_bundle, bg_asset = ResPath.GetCommonImages(asset)
	self.node_list.fill.image:LoadSprite(bg_bundle, bg_asset)

	self.node_list.rank_icon:CustomSetActive(self.index < 4)

	if self.index < 4 then
		self.node_list.rank_icon.image:LoadSprite(ResPath.GetCommonImages("a3_hurt_list_rank_" .. self.index))
	end

	-- self.node_list.num.text.text = self.index
	-- self.node_list.name.text.text = self.data.name
	-- self.node_list.damage.text.text = CommonDataManager.ConverExpByThousand(self.data.hurt)
	-- self.node_list.per_bg.slider.value = self.data.hurt / BossAssistWGData.Instance:GetNormalHurtInfoMaxValue()
end