YiNianMagicPurchaseView = YiNianMagicPurchaseView or BaseClass(SafeBaseView)
function YiNianMagicPurchaseView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/yinianmagic_prefab", "laout_magic_xiulian_view")
end

function YiNianMagicPurchaseView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["buy_btn"], BindTool.Bind(self.OnClickBuy, self))

    if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(true)
	end
end

function YiNianMagicPurchaseView:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function YiNianMagicPurchaseView:OnFlush()
    local level = YinianMagicWGData.Instance:GetPurchaseLevel()
    local cur_cfg = YinianMagicWGData.Instance:GetPurchaseCfgbyLevel(level)
    local next_cfg = YinianMagicWGData.Instance:GetPurchaseCfgbyLevel(level + 1)
    self:FlushCurDesc(cur_cfg)
    self:FlushNextDesc(next_cfg)
    self:FlushSingleDesc(cur_cfg)
    self.node_list.buy_btn:SetActive(next_cfg ~= nil)
    local y_value = next_cfg ~= nil and (not IsEmptyTable(next_cfg.rmb_reward_item) and 232 or 232) or 120
	self.node_list["desc_bg"].rect.sizeDelta = Vector2(568, y_value)
    self.node_list.cur_desc_group:SetActive(next_cfg ~= nil)
    self.node_list.next_desc_group:SetActive(next_cfg ~= nil)
    self.node_list.single_desc_group:SetActive(next_cfg == nil)
    self.node_list.reward_group:SetActive(next_cfg ~= nil and not IsEmptyTable(next_cfg.rmb_reward_item))
    if next_cfg ~= nil and not IsEmptyTable(next_cfg.rmb_reward_item) and self.reward_list ~= nil then
        self.reward_list:SetDataList(next_cfg.rmb_reward_item)
    end
end

function YiNianMagicPurchaseView:FlushCurDesc(cfg)
    if cfg then
        self.node_list.cur_name.text.text = cfg.name
        self.node_list.cur_desc_1.text.text = string.format(Language.YinianMagicView.TeQuan1, cfg.exp_add_per / 100)
        self.node_list.cur_desc_2.text.text = string.format(Language.YinianMagicView.TeQuan2, cfg.bless_add_per / 100)
    else
        self.node_list.cur_name.text.text = Language.YinianMagicView.NoActPurchase
        self.node_list.cur_desc_1.text.text = string.format(Language.YinianMagicView.TeQuan1, 0)
        self.node_list.cur_desc_2.text.text = string.format(Language.YinianMagicView.TeQuan2, 0)
    end
end

function YiNianMagicPurchaseView:FlushNextDesc(cfg)
    if cfg then 
        self.node_list.next_name.text.text = cfg.name
        self.node_list.next_desc_1.text.text = string.format(Language.YinianMagicView.TeQuan1, cfg.exp_add_per / 100)
        self.node_list.next_desc_2.text.text = string.format(Language.YinianMagicView.TeQuan2, cfg.bless_add_per / 100)
        local price = RoleWGData.GetPayMoneyStr(cfg.rmb_price, cfg.rmb_type, cfg.rmb_seq)
        self.node_list.price_text.text.text = price --购买价格
    end
end

function YiNianMagicPurchaseView:FlushSingleDesc(cfg)
    if cfg then
        self.node_list.single_name.text.text = cfg.name
        self.node_list.single_desc_1.text.text = string.format(Language.YinianMagicView.TeQuan1, cfg.exp_add_per / 100)
        self.node_list.single_desc_2.text.text = string.format(Language.YinianMagicView.TeQuan2, cfg.bless_add_per / 100)
    else
        self.node_list.single_name.text.text = Language.YinianMagicView.NoActPurchase
        self.node_list.single_desc_1.text.text = string.format(Language.YinianMagicView.TeQuan1, 0)
        self.node_list.single_desc_2.text.text = string.format(Language.YinianMagicView.TeQuan2, 0)
    end
end

function YiNianMagicPurchaseView:OnClickBuy()
    local level = YinianMagicWGData.Instance:GetPurchaseLevel()
    local next_cfg = YinianMagicWGData.Instance:GetPurchaseCfgbyLevel(level + 1)
    if next_cfg == nil then
        return
    end

    RechargeWGCtrl.Instance:Recharge(next_cfg.rmb_price, next_cfg.rmb_type, next_cfg.rmb_seq)
end