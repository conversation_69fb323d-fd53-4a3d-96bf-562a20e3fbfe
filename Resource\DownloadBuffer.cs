﻿using System.Collections.Generic;
using System;
using System.IO;
using UnityEngine;

public class DownloadBuffer
{
    private byte[] buffer;
    private FileStream streamWriter;

    private int beginPtr = 0;
    private int endPtr = 0;
    private bool isFaild = false;
    private bool isFinished = false;
    private bool isEmpty = true;
    private bool isDestoryed = false;
    private float timeStamp = 0;

    private string path;
    private int totalSize = 0;
    private int receivedSize = 0;
    private Action<bool, int> cb;
    private int cbData;
    private int priority = 1;

    private const int BUFFER_SIZE = 2 * 1024 * 1024;

    /// <summary>
    /// Debug
    /// </summary>
    private bool isDebug = false;
    private int totalIOCount = 0;
    private int totalWrited2MemSize = 0;
    private int totalWrited2DiskSize = 0;
    private int downloadSpeed = 0;
    private int totalFinishedCount = 0;
    private int totalFaildCount = 0;

    public DownloadBuffer(int size)
    {
        buffer = new byte[size];
    }

    public void StarReceiveData(string path, int size, Action<bool, int> cb, int cbData)
    {
        this.path = path;
        this.cb = cb;
        this.cbData = cbData;
        totalSize = size;
        isFinished = false;

        RuntimeAssetHelper.InsureDirectory(path);
        if (File.Exists(path))
        {
            Debug.LogErrorFormat("[DownloadBuffer] BigBug, exists {0}", this.path);
        }

        size = Math.Min(BUFFER_SIZE, size);

        try
        {
            streamWriter = File.Create(path, size);
        }
        catch(Exception e)
        {
            Debug.LogErrorFormat("[DownloadBuffer] CreateFile Faild {0} {1}", path, e.Message);
            isFaild = true;
        }
    }

    public void StopReceiveData()
    {
        isFinished = true;

        if (totalSize != receivedSize)
        {
            //Debug.LogErrorFormat("下载失败:path:{0} totalSize:{1} receivedSize:{2}", path, totalSize, receivedSize);
            OnFaild();
        }

        InvokeCallBack();
    }

    public void SetPriority(int priority)
    {
        priority = Math.Max(priority, 0);
        this.priority = priority;
    }

    public void Clear()
    {
        CloseWriter();

        isEmpty = true;
        isFaild = false;
        beginPtr = 0;
        endPtr = 0;
        totalSize = 0;
        receivedSize = 0;
        cb = null;
        cbData = 0;
        priority = 1;
    }

    public void OnDestory(bool isGameStop)
    {
        TryDeleteFile();
        if (!isGameStop)
        {
            if (null != cb)
            {
                cb(false, cbData);
            }
        }
        Clear();
        isDestoryed = true;
    }

    public bool IsStoped
    {
        get
        {
            return isFaild || isDestoryed;
        }
    }

    public void ReceiveData(byte[] data, int length)
    {
        if (isDestoryed)
            return;

        receivedSize += length;

        // 同步写入
        if (this.priority <= 0)
        {
            // 先把现有的缓存写入硬盘
            if (UsedCache > 0)
            {
                WriteData2Disk(UsedCache);
            }

            byte[] oldBuffer = buffer;
            buffer = data;
            beginPtr = 0;
            endPtr = 0;
            isEmpty = false;
            WriteData2Disk(length);

            buffer = oldBuffer;
            beginPtr = 0;
            endPtr = 0;
            isEmpty = true;
        }
        // 写入的数据大小超出缓冲区容量最大值
        // 如果缓冲区容量直接设置为0，就相当于下载多少写多少
        else if (length > BufferLength)
        {
            // 先把现有的缓存写入硬盘
            if (UsedCache > 0)
            {
                WriteData2Disk(UsedCache);
            }

            // 然后把超出容量的数据写入硬盘
            byte[] oldBuffer = buffer;
            buffer = data;
            beginPtr = 0;
            endPtr = 0;
            isEmpty = false;
            WriteData2Disk(length - oldBuffer.Length);

            // 最后把剩余的数据写入缓冲区(刚好把缓冲区写满)
            buffer = oldBuffer;
            beginPtr = 0;
            endPtr = 0;
            isEmpty = true;
            WriteData2Buffer(data, BufferLength, length - BufferLength);
        }
        // 写入的数据大小超出缓冲区剩余值
        else if (length > RestCache)
        {
            WriteData2Disk(length - RestCache);
            WriteData2Buffer(data, length);
        }
        else
        {
            WriteData2Buffer(data, length);
        }

        if (isDebug)
        {
            downloadSpeed += length;
        }
    }

    // 每次update检查是否需要从缓冲区写入到硬盘
    // 这里尽量要减少io调用次数，所以要控制好最低写入的数据量大小
    public void Update(float nowTime)
    {
        if (isFaild || isEmpty || isDestoryed)
            return;

        if (priority <= 0)
        {
            WriteData2Disk(UsedCache);
            return;
        }

        if (timeStamp > nowTime)
        {
            return;
        }

        float writeCD = DownloadBufferMgr.WriteCD;
        writeCD /= priority;
        timeStamp = nowTime + writeCD;

        int minSize = DownloadBufferMgr.MinWriteSize;
        int usedCache = UsedCache;
        float value = (float)usedCache / BufferLength;
        if (usedCache >= minSize || isFinished || value >= 0.8f)
        {
            int writeSize = minSize + Convert.ToInt32(usedCache * value * priority / 10);
            writeSize = Math.Min(writeSize, usedCache);
            WriteData2Disk(writeSize);
        }
    }

    public int BufferLength
    {
        get { return buffer.Length; }
    }

    // 已经使用的缓冲
    public int UsedCache
    {
        get
        {
            if (endPtr > beginPtr)
            {
                return endPtr - beginPtr;
            }
            else if (endPtr < beginPtr)
            {
                return BufferLength - (beginPtr - endPtr);
            }
            else
            {
                return isEmpty ? 0 : BufferLength;
            }
        }
    }

    // 剩余的缓冲
    public int RestCache
    {
        get { return BufferLength - UsedCache; }
    }

    public int FileSize
    {
        get { return totalSize; }
    }

    public int ReceivedSize
    {
        get { return receivedSize; }
    }

    private void WriteData2Buffer(byte[] data, int length, int offset = 0)
    {
        if (length <= 0 || isFaild)
        {
            return;
        }

        int diff = BufferLength - endPtr;
        if (diff >= length)
        {
            Buffer.BlockCopy(data, offset, buffer, endPtr, length);
            endPtr += length;
            endPtr = endPtr == BufferLength ? 0 : endPtr;
        }
        else
        {
            Buffer.BlockCopy(data, offset, buffer, endPtr, diff);
            endPtr = length - diff;
            Buffer.BlockCopy(data, offset + diff, buffer, 0, endPtr);
        }

        isEmpty = false;

        if (isDebug)
        {
            totalWrited2MemSize += length;
        }
    }

    private void WriteData2Disk(int length)
    {
        if (length <= 0 || isFaild || isDestoryed)
        {
            return;
        }

        bool succ = true;
        int diff = BufferLength - beginPtr;
        if (diff >= length)
        {
            succ = WriteData2Disk(buffer, beginPtr, length);
            beginPtr += length;
            beginPtr = beginPtr == BufferLength ? 0 : beginPtr;
        }
        else
        {
            succ = WriteData2Disk(buffer, beginPtr, diff);
            beginPtr = length - diff;
            if (succ)
            {
                succ = WriteData2Disk(buffer, 0, beginPtr);
            }
        }

        if (beginPtr == endPtr)
        {
            isEmpty = true;
            beginPtr = 0;
            endPtr = 0;
        }

        if (isDebug)
        {
            totalWrited2DiskSize += length;
            ++totalIOCount;
        }

        if (!succ)
        {
            OnFaild();
        }

        InvokeCallBack();
    }

    private bool WriteData2Disk(byte[] data, int offset, int dataLength)
    {
        bool succ = true;
        try
        {
            streamWriter.Write(data, offset, dataLength);
        }
        catch (Exception e)
        {
            Debug.LogErrorFormat("[DownloadBuffer] WriteData2Disk Faild {0} {1}", path, e.Message);
            succ = false;
        }

        return succ;
    }

    private void OnFaild()
    {
        if (isFaild)
        {
            return;
        }
        isFaild = true;
        
        if (isDebug)
        {
            Debug.LogErrorFormat("[DownloadBuffer]下载失败:path:{0} totalSize:{1} receivedSize:{2}", path, totalSize, receivedSize);
        }

        TryDeleteFile();
    }

    private void TryDeleteFile()
    {
        CloseWriter();

        if (File.Exists(path))
        {
            try
            {
                Debug.LogFormat("[DownloadBuffer] Try Delete {0} {1} {2}", path, totalSize, receivedSize);
                File.Delete(path);
            }
            catch (Exception e)
            {
                Debug.LogErrorFormat("[DownloadBuffer] Delete Faild {0} {1}", path, e.Message);
                throw;
            }
        }
    }

    private void CloseWriter()
    {
        if (null != streamWriter)
        {
            try
            {
                streamWriter.Flush();
                streamWriter.Dispose();
                streamWriter.Close();
            }
            catch(Exception e)
            {
                Debug.LogErrorFormat("[DownloadBuffer] CloseWriter Error {0} {1}", path, e.Message);
                isFaild = true;
            }
            finally
            {
                streamWriter = null;
            }
        }
    }

    private void InvokeCallBack()
    {
        if (isFinished && !isDestoryed)
        {
            if (isFaild || isEmpty)
            {
                CloseWriter();

                if (null != cb)
                {
                    cb(!isFaild, cbData);
                }

                if (isDebug)
                {
                    ++totalFinishedCount;
                    if (isFaild)
                        ++totalFaildCount;
                }

                DownloadBufferMgr.ReleaseBuffer(this);
            }
        }
    }

    public bool IsDebug
    {
        get { return isDebug; }
        set { isDebug = value; }
    }

    public void ClearDebugLogs()
    {
        totalIOCount = 0;
        totalWrited2MemSize = 0;
        totalWrited2DiskSize = 0;
        downloadSpeed = 0;
}

    public void GetDebugLogs(out int totalIOCount, out int totalWrited2MemSize, out int totalWrited2DiskSize, out int totalDownloadSize)
    {
        totalIOCount = this.totalIOCount;
        totalWrited2MemSize = this.totalWrited2MemSize;
        totalWrited2DiskSize = this.totalWrited2DiskSize;
        totalDownloadSize = this.downloadSpeed;
    }

    public int TotalFinishedCount
    {
        get { return totalFinishedCount; }
    }

    public int TotalFaildCount
    {
        get { return totalFaildCount; }
    }
}
