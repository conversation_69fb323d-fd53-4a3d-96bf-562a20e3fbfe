ServerWarningTips = ServerWarningTips or BaseClass(SafeBaseView)

function ServerWarningTips:__init()
    self:SetMaskBg()
    self.active_close = false
	self.view_layer = UiLayer.PopTop
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)

	self:AddViewResource(0, "uis/view/login_ui_prefab", "layout_server_warning_tips")
end

function ServerWarningTips:SetContent(data)
   self.data = data
end

function ServerWarningTips:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_agree_warning"], BindTool.Bind(self.Close, self))
    XUI.AddClickEventListener(self.node_list["btn_close_window"], BindTool.Bind(self.Close, self))
end

function ServerWarningTips:ReleaseCallBack()
    self.data = nil
end

function ServerWarningTips:OnFlush()
    self.node_list.warning_title.text.text = self.data.title
    self.node_list.warning_text.text.text = self.data.desc
    self.node_list.warning_btn_text.text.text = self.data.warning_btn_text
end