TianShenBaGuaComposeTips = TianShenBaGuaComposeTips or BaseClass(SafeBaseView)

function TianShenBaGuaComposeTips:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",{vector2 = Vector2(0, 0), sizeDelta = Vector2(706, 520)})
	self:AddViewResource(0, "uis/view/tianshen_prefab", "layout_bagua_compose_tip")
end

function TianShenBaGuaComposeTips:__delete()
end
function TianShenBaGuaComposeTips:ReleaseCallBack()
	if self.target_cell then
		self.target_cell:DeleteMe()
		self.target_cell = nil
	end

	if self.small_cost_list then
		for k,v in pairs(self.small_cost_list) do
			v:DeleteMe()
		end
		self.small_cost_list = nil
	end
end

function TianShenBaGuaComposeTips:LoadCallBack()

	self.target_cell = ItemCell.New(self.node_list.target_cell)
	self:SetViewName(Language.TianShen.BaGuaHeCheng)
	XUI.AddClickEventListener(self.node_list.btn_compose_one, BindTool.Bind(self.OnClickComposeOne, self))

	XUI.AddClickEventListener(self.node_list.btn_compose_all, BindTool.Bind(self.OnClickComposeAll, self))

	XUI.AddClickEventListener(self.node_list.attr_panel, BindTool.Bind(self.OnClickCloseAttrPanel, self))
	XUI.AddClickEventListener(self.node_list.attr_all_btn, BindTool.Bind(self.OnClickAttrPanel, self))
	self.small_cost_list = {}
	for i =1, 4 do
		self.small_cost_list[i] = BaGuaComposeCell.New(self.node_list["tianshen_compose_cell"..i])
	end

	self.compose_list = AsyncListView.New(BaGuaComposeCell, self.node_list.list_roll)

end

function TianShenBaGuaComposeTips:ShowIndexCallBack(index)

end

function TianShenBaGuaComposeTips:OnFlush()
	if self.compose_data then
		self.target_item_id,self.compose_type,self.compose_way,self.count_2 = TianShenBaGuaWGData.Instance:GetTargetComposeItem(self.compose_data.cost_item)
		self.target_cell:SetData({item_id = self.target_item_id,num = 1})
		local base_cfg = TianShenBaGuaWGData.Instance:GetBaGuaBaseInfo(self.target_item_id)
		if base_cfg then
			self.target_cell:SetLeftTopImg(base_cfg.star or 0)
		else
			self.target_cell:SetLeftTopImg(0)
		end
		for i=1,4 do
			self.node_list["tianshen_compose_cell"..i]:SetActive(false)
		end
		if #self.compose_way <= 4 then
			self.node_list["mask2"]:SetActive(true)
			self.node_list["mask1"]:SetActive(false)
			local new_data = {}
			for k,v in pairs(self.compose_way) do
				new_data[k] = {}
				new_data[k].item_id = v.item_id
				new_data[k].cost_num = v.need_num
				new_data[k].count_1 = v.have_num
				new_data[k].count_2 = 0
				self.small_cost_list[k]:SetData(new_data[k])
				self.node_list["tianshen_compose_cell"..k]:SetActive(true)
			end
		else
			self.node_list["mask2"]:SetActive(false)
			self.node_list["mask1"]:SetActive(true)
			local new_data = {}
			for k,v in pairs(self.compose_way) do
				new_data[k] = {}
				new_data[k].item_id = v.item_id
				new_data[k].cost_num = v.need_num
				new_data[k].count_1 = v.have_num
				new_data[k].count_2 = 0
			end
			self.compose_list:SetDataList(new_data)
		end
		local attr_list,is_pre = TianShenBaGuaWGData.Instance:GetBaGuaEquipAttrAndZhanLi(self.target_item_id,false)
		self.attr_str = {}
		local str2 = ""
		local num = 0
		for k,v in pairs(attr_list) do
			num = num + 1
			if num > 1 then
				str2 = str2 .."\n"
			end

			if is_pre[k].is_pre then
				self.attr_str[num] = Language.Common.TipsAttrNameList[k]..ToColorStr(" + "..tonumber(v)/100 .."%", COLOR3B.DEFAULT_NUM)
				if num <= 4 then
					self.node_list["attr_name"..num].text.text = Language.Common.TipsAttrNameList[k]
					self.node_list["attr_value"..num].text.text = "+"..tonumber(v)/100 .."%"
				end
			else
				self.attr_str[num] = Language.Common.TipsAttrNameList[k]..ToColorStr(" + "..v, COLOR3B.DEFAULT_NUM)
				if num <= 4 then
					self.node_list["attr_name"..num].text.text = Language.Common.TipsAttrNameList[k]
					self.node_list["attr_value"..num].text.text = "+"..v
				end
			end
		end

		if num > 4 then
			self.node_list["attr_all_btn"]:SetActive(true)
			self.node_list["attr_group"]:SetActive(false)
		else
			self.node_list["attr1"]:SetActive(num >= 1)
			self.node_list["attr2"]:SetActive(num >= 2)
			self.node_list["attrcell1"]:SetActive(num >= 1)

			self.node_list["attr3"]:SetActive(num >= 3)
			self.node_list["attr4"]:SetActive(num >= 4)
			self.node_list["attrcell2"]:SetActive(num >= 3)

			self.node_list["attr_group"]:SetActive(true)
			self.node_list["attr_all_btn"]:SetActive(false)
		end
	end
end

function TianShenBaGuaComposeTips:OnClickCloseAttrPanel()
	self.node_list.attr_panel:SetActive(false)
end

function TianShenBaGuaComposeTips:OnClickAttrPanel()
	self.node_list.attr_panel:SetActive(true)
	for k,v in pairs(self.attr_str) do
		if self.node_list["attr_text"..k] then
			self.node_list["attr_text"..k].text.text = v
		end
	end
	for i=1,8 do
		if self.attr_str[i] then
			self.node_list["attr_text"..i]:SetActive(true)
		else
			self.node_list["attr_text"..i].text.text = ""
		end
	end
end


function TianShenBaGuaComposeTips:OnClickCompose()

end

function TianShenBaGuaComposeTips:SetComposeData(data)
	self.compose_data = data
end



function TianShenBaGuaComposeTips:OnClickComposeOne()
	if self.compose_type == 1 or self.compose_type == 0 then
		local use_equip = 0
		for k,v in pairs(self.compose_way) do
			if v.use_equip then
				use_equip = 1
				break
			end
		end
		local need_delay = use_equip > 0 and 0.5 or 0
		GlobalTimerQuest:AddDelayTimer(function ()
			TianShenWGCtrl.Instance:SendBaGuaOpera(TIANSHEN_BAGUA_REQ.TIANSHEN_BAGUA_REQ_UP_STAR,self.target_item_id,use_equip)
		end,need_delay)
		local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_tianshen_jiesuo)
		EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.target_cell.transform, 2)
		if use_equip > 0 then
			GlobalTimerQuest:AddDelayTimer(function ()
				self:Close()
			end,need_delay)
		end

	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.TianShen.BaGuaComposeNotEnough)
	end
end

function TianShenBaGuaComposeTips:OnClickComposeAll() --有问题
	local count_1 = self.count_1
	local count_2 = self.count_2
	if self.count_1 + self.count_2 >= self.cost_num then

		local need_delay = count_2 > 0 and 0.5 or 0
		local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_tianshen_jiesuo)

		if count_2 > 0 then
			GlobalTimerQuest:AddDelayTimer(function ()
				TianShenWGCtrl.Instance:SendBaGuaOpera(TIANSHEN_BAGUA_REQ.TIANSHEN_BAGUA_REQ_UP_STAR,self.target_item_id,1)
			end,need_delay)
			count_1 = count_1 - self.cost_num + count_2
		end

		local normal_times = math.floor(count_1/self.cost_num)
		GlobalTimerQuest:AddDelayTimer(function ()
			if normal_times >= 1 then
				for i = 1,normal_times do
					TianShenWGCtrl.Instance:SendBaGuaOpera(TIANSHEN_BAGUA_REQ.TIANSHEN_BAGUA_REQ_UP_STAR,self.target_item_id,0)
				end
			end
		end,need_delay)
		EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.target_cell.transform, 2)
		if count_2 > 0 then
			GlobalTimerQuest:AddDelayTimer(function ()
				self:Close()
			end,need_delay)
		end
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.TianShen.BaGuaComposeNotEnough)
		return
	end
end


BaGuaComposeCell = BaGuaComposeCell or BaseClass(BaseRender)

function BaGuaComposeCell:__init()
	self.item_cell = ItemCell.New(self.node_list.cell_pos)
end

function BaGuaComposeCell:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function BaGuaComposeCell:OnFlush()
	if self.data then
		self.item_cell:SetData(self.data)
		local have_num = self.data.count_1 + self.data.count_2
		local color = have_num >= self.data.cost_num and  COLOR3B.DEFAULT_NUM or COLOR3B.D_RED
		local str = ToColorStr(have_num, color).."/"..self.data.cost_num
		self.node_list.cost_text.text.text = str
		local base_cfg = TianShenBaGuaWGData.Instance:GetBaGuaBaseInfo(self.data.item_id)
		if base_cfg then
			self.item_cell:SetLeftTopImg(base_cfg.star or 0)
		else
			self.item_cell:SetLeftTopImg(0)
		end

	end
end