require("game/achievement/achievement_view")

ACHIEVEMENT_TAB_TYPE =
{
	CJZL = TabIndex.achievement_achieve_totle, --成就总览
	CJ = TabIndex.achievement_achieve,      --成就
}

AchievementView = AchievementView or BaseClass(SafeBaseView)

function AchievementView:__init()
	self.view_style = ViewStyle.Full
	self.view_name = GuideModuleName.Achievement
	self:SetMaskBg(false, true)
	self:LoadConfig()
	self.is_safe_area_adapter = true
	self.TabIndex = ACHIEVEMENT_TAB_TYPE
	self.tab_sub = { nil, nil }
	self.remind_tab = {
		{ RemindName.AchievementTotal },
		{ RemindName.Achievement },
	}
end

function AchievementView:__delete()
end

-- 加载配置
function AchievementView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
	local bundle_name = "uis/view/achievement_ui_prefab"
	self:AddViewResource(ACHIEVEMENT_TAB_TYPE.CJZL, bundle_name, "layout_achievement_total")
	self:AddViewResource(ACHIEVEMENT_TAB_TYPE.CJ, bundle_name, "layout_achievement")
	self:AddViewResource(0, bundle_name, "layout_achievement_progress")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "VerticalTabbar")

	self.default_index = ACHIEVEMENT_TAB_TYPE.CJZL
end

function AchievementView:LoadCallBack()
	local bundle, asset = ResPath.GetRawImagesPNG("a3_ty_bg4")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

	self:InitTabbar()
	self.node_list.title_view_name.text.text = Language.Achievement.title_view_name

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end

function AchievementView:LoadIndexCallBack(index)
	if 0 == index then
		return
	end

	if index == ACHIEVEMENT_TAB_TYPE.CJ then
		self:InitAchievementPanel()
	elseif index == ACHIEVEMENT_TAB_TYPE.CJZL then
		self:InitMainPanelText()
	end
end

function AchievementView:CloseCallBack()
	self:DelAchieventBtnData()
end

function AchievementView:ReleaseCallBack()
	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	self:DeleteAchievementPanel()
end

function AchievementView:InitTabbar()
	if nil == self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:Init(Language.Achievement.TabGrop, self.tab_sub, nil, nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	end

	FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.Achievement, self.tabbar)
end

function AchievementView:ShowIndexCallBack(index)
	self:SetCanDoAchievementAnim()
	if index == ACHIEVEMENT_TAB_TYPE.CJ then
		self:FlushShieldCJ()
	end

	self:DelAchieventBtnData()
end

function AchievementView:OnFlush(param_t, index)
	if index == ACHIEVEMENT_TAB_TYPE.CJ then
		self:FlushAchievementPanel()
	elseif index == ACHIEVEMENT_TAB_TYPE.CJZL then
		self:FlushAchievementTotal()
	end
end
