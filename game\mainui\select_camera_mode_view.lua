SelectCameraModeView = SelectCameraModeView or BaseClass(SafeBaseView)
function SelectCameraModeView:__init()
	self:LoadConfig()
end

-- 加载配置
function SelectCameraModeView:LoadConfig()
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_third_panel")
	self:AddViewResource(0, "uis/view/select_camera_mode_ui_prefab", "layout_select_camera_mode")
	self:SetMaskBg()
end

function SelectCameraModeView:ReleaseCallBack()
end

function SelectCameraModeView:LoadCallBack(index, loaded_times)
	self.node_list["layout_commmon_third_root"].rect.sizeDelta = Vector2(730, 450)
	XUI.AddClickEventListener(self.node_list["btn_confirm"], BindTool.Bind1(self.ClickConfirmHandler, self))
end

function SelectCameraModeView:OpenCallBack()

end

function SelectCameraModeView:CloseCallBack()
	TaskGuide.Instance:CanAutoAllTask(true)
end

function SelectCameraModeView:OnFlush()

end

function SelectCameraModeView:ClickConfirmHandler()
	local flag = self.node_list.toggle1.toggle.isOn and 0 or 1
	Scene.Instance:SetCameraMode(flag)
	SettingWGCtrl.Instance:SendChangeHotkeyReq({[1] = {HOT_KEY.CAMERA_KEY_FLAG, flag}})
	SettingWGData.Instance:SetSettingDataListByKey(HOT_KEY.CAMERA_KEY_FLAG, flag)
	GlobalEventSystem:Fire(SettingEventType.MAIN_CAMERA_MODE_CHANGE, flag)
	self:Close()
end