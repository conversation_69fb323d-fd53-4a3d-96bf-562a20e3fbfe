--------------------------------------------------
-- 1v1  3v3开启信息提示
--------------------------------------------------
XunYouOpenHintView = XunYouOpenHintView or BaseClass(SafeBaseView)

function XunYouOpenHintView:__init()
	self.is_modal = true
	self.is_any_click_close = true
    self.view_layer = UiLayer.Pop
    self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_jiehun_third2_panel")
	self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_xunyou_forenotice")
end

function XunYouOpenHintView:LoadCallBack()
	self.node_list["btn_go_kill"].button:AddClickListener(BindTool.Bind1(self.<PERSON><PERSON><PERSON><PERSON><PERSON>, self))
end

function XunYouOpenHintView:ShowIndexCallBack()
	self.node_list["lbl_title"].text.text = Language.Marry.XunYouTitleName
end

function XunYouOpenHintView:OnFlush()
    local marry_info = MarryWGData.Instance:GetCurWeddingInfo()
    if marry_info and not IsEmptyTable(marry_info) then
        self.node_list["role_name"].text.text = marry_info.role_name    
        self.node_list["lover_role_name"].text.text = marry_info.lover_role_name
    end
end

function XunYouOpenHintView:ClickHandler()
    local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.HUNYAN)
    if nil == activity_info then
        self:Close()
        return
    end
    
	if activity_info.status == HUNYAN_STATE_TYPE.HUNYAN_STATE_TYPE_XUNYOU then
		MarryWGCtrl.Instance:SetMoveXuyou(true)
		--这个魔性得数字为先人遗留财产(服务器说只是代表不同的obj)
		local flag =  2
        MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_XUNYOU_OBJ_POS, flag)
        ViewManager.Instance:CloseAll()
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.Marry.TipsXunYouEnd)
    end
    self:Close()
end
