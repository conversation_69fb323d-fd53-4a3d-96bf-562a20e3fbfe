require("game/chat/chat_transmit")
-- require("game/chat/chat_msg_item_record")
-- require("game/chat/chat_com")
require("game/chat/chat_face")
require("game/chat/chat_setting")
-- require("game/chat/chat_item")
require("game/chat/chat_list_view")
-- require("game/chat/chat_big_face")
-- require("game/chat/chat_word_face")
require("game/chat/chat_transmit")
require("game/chat/chat_location")
-- require("game/chat/add_blacklist_view")
-- require("game/chat/blacklist_view")


ChatView = ChatView or BaseClass(SafeBaseView)

CHAT_FONT_SIZE = 20

ChatViewIndex = {
	World = 1,
	Team = 2,
	Private = 3,
	Camp = 4,
	System = 5,
	Setting = 100,
}

-- CHANNEL_TYPE = {
-- 	WORLD = 0,										-- 世界
-- 	CAMP = 1,										-- 阵营
-- 	SCENE = 2,										-- 场景
-- 	TEAM = 3,										-- 队伍
-- 	GUILD = 4,										-- 公会
-- 	PRIVATE = 5,									-- 私聊
-- 	SYSTEM = 6,										-- 系统
-- 	SPEAKER = 8,									-- 喇叭
-- 	CROSS = 9,										-- 跨服
-- 	CHUAN_WEN = 10, 								-- 传闻
-- 	ALL = 100,										-- 全部
-- }

function ChatView:__init(view_name)
	ChatView.Instance = self
	-- self:SetModal(true)
	self.default_index = 1

	--self.texture_path_list[1] = "res/xui/chat.png"
	--self.texture_path_list[2] = "res/xui/face.png"
	-- self.ui_config = {"uis/view/chat_ui_prefab", "ChatUi"}
	-- self.config_tab = {
	-- 	{"layout_chat_item", {0}},
	-- }

	-- self.transmit_pop_view = ChatTransmitPopView.New()
	self.is_align_right = true						-- 是否向右对齐
	self.tabbar = nil
	self.tab_index = 1
	self.curr_channel = CHANNEL_TYPE.ALL			-- 当前频道

end

function ChatView:__delete()
	-- self.transmit_pop_view:DeleteMe()
	-- self.transmit_pop_view = nil

	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
end

function ChatView:LoadCallBack(index, loaded_times)

end

function ChatView:LoadIndexCallBack(index)
	if ChatViewIndex.Private == index then
		self:InitPrivate()
	end
end

function ChatView:OpenCallBack()
	-- AudioManager.Instance:PlayOpenCloseUiEffect()
	-- if not self:IsLoading() and not self:IsPrivateIndex() then
	-- 	self:RefreshChannel()
	-- end
	-- local camp = GameVoManager.Instance:GetMainRoleVo().camp
	-- self.tabbar:SetToggleVisible(ChatViewIndex.Camp, camp > 0)
	-- if self:GetShowIndex() == ChatViewIndex.Camp then
	-- 	self:ChangeToIndex(ChatViewIndex.World)
	-- end
end

function ChatView:ShowIndexCallBack(index)
	-- self.transmit_pop_view:Close()
	print_error("",index)
	self.tab_index = index
	-- self.tabbar:ChangeToIndex(index)

	if ChatViewIndex.World == index then
		self.curr_channel = CHANNEL_TYPE.ALL
		self:RefreshChannel()
	elseif ChatViewIndex.Team == index then
		self.curr_channel = CHANNEL_TYPE.TEAM
		self:RefreshChannel()
	elseif ChatViewIndex.Private == index then
		self.curr_channel = CHANNEL_TYPE.PRIVATE
		self:UpdatePrivateView(true)
	elseif ChatViewIndex.Camp == index then
		self.curr_channel = CHANNEL_TYPE.CAMP
		self:RefreshChannel()
	elseif ChatViewIndex.System == index then
		self.curr_channel = CHANNEL_TYPE.SYSTEM
		self:RefreshChannel()

	end

	if nil ~= self.list_view_list then
		for k, v in pairs(self.list_view_list) do
			v:GetView():setVisible(index == k)
		end
	end

end

function ChatView:CloseCallBack()
	-- AudioManager.Instance:PlayOpenCloseUiEffect()
end

function ChatView:ReleaseCallBack()
	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
end

function ChatView:OnFlush(param_list, index)
	if index == ChatViewIndex.Private then
		self:UpdatePrivateView(true)
	else
		self:RefreshChannel()
	end
end

function ChatView:SendMsgCallback()

end

-- 当前频道
function ChatView:GetChannel()
	return self.curr_channel
end

function ChatView:GetInputEdit()
	return nil
end

function ChatView:IsTransmitOpen()
	-- return self.transmit_pop_view:IsTransmitOpen()
end

function ChatView:GetTransmitInputEdit()
	-- return self.transmit_pop_view:GetEditText()
end

--获取大喇叭按钮
function ChatView:GetLayoutBigHornBtn()
	if nil == self.icon_right_list[2] then
		return nil
	end
	return self.icon_right_list[2]:GetView()
end

-- 刷新频道
function ChatView:RefreshChannel()
	if not self:IsOpen() or nil == self.list_view_list then
		return
	end
	local channel = ChatWGData.Instance:GetChannel(self.curr_channel)
	if nil == channel then
		return
	end

	ChatView.UpdateContentListView(self.list_view_list[self.tab_index], channel.msg_list, channel.unread_num)
	channel.unread_num = 0
end

-- --获取人物当前坐标
-- function ChatView:GetMainRolePos()
-- 	local main_role = Scene.Instance.main_role
-- 	if nil ~= main_role then
-- 		local x, y = main_role:GetLogicPos()
-- 		local pos_msg = string.format(Language.Chat.PosFormat, Scene.Instance:GetSceneName(), x, y)
-- 		if ChatWGData.ExamineEditText(edit_text:getText(), 1) then
-- 			edit_text:setText(edit_text:getText() .. pos_msg)
-- 			ChatWGData.Instance:InsertPointTab()
-- 		end
-- 	end
-- end

-- 刷新聊天列表
function ChatView.UpdateContentListView(list_view, msg_list, unread_num)
	if nil == list_view or nil == msg_list then
		return
	end

	local msg_count = #msg_list
	if unread_num > 0 and unread_num < msg_count then
		list_view:MoveFrontToLast(unread_num - (msg_count - list_view:GetCount()))
	end

	list_view:SetDataList(msg_list)
end
