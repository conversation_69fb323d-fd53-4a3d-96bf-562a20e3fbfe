OpenningActNoticeView = OpenningActNoticeView or BaseClass(SafeBaseView)
--local COUNT = 5 --每次打开最多显示5个

function OpenningActNoticeView:__init()
	self.view_layer = UiLayer.MainUI
	self:SetMaskBg(false, false)
	self.mask_alpha = 0
	self:AddViewResource(0, "uis/view/calendar_prefab", "layout_openning_act_notice")
end

function OpenningActNoticeView:__delete()
end

function OpenningActNoticeView:CloseCallBack()
	CalendarWGData.Instance:SetClickCloseAct(self.data.cfg.act_seq)
	CalendarWGCtrl.Instance:CheckOpenOpenningActNotice()
end

function OpenningActNoticeView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["go_btn"], BindTool.Bind(self.OnClick, self))
	self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack,self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)
end

function OpenningActNoticeView:ShowIndexCallBack()
end

function OpenningActNoticeView:ReleaseCallBack()
	if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
		self.activity_change_callback = nil
	end
	self.data = nil
end


function OpenningActNoticeView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "data" then
			self.data = v.cfg
		end
	end
	if self.data then
		local bundle, asset = ResPath.GetF2RawImagesPNG(self.data.cfg.openning_act_bg)
		self.node_list["act_img"].raw_image:LoadSpriteAsync(bundle, asset)

		-- 活动名称
		if self.data.cfg.openning_act_name_asset and self.data.cfg.openning_act_name_asset ~= "" then
			local bundle, asset = ResPath.GetF2RawImagesPNG(self.data.cfg.openning_act_name_asset)
			self.node_list["act_name"].raw_image:LoadSpriteAsync(bundle, asset, function ()
				self.node_list["act_name"].raw_image:SetNativeSize()
			end)

			self.node_list["act_name"]:SetActive(true)
		else
			self.node_list["act_name"]:SetActive(false)
		end
	else
		self:Close()
	end
end

function OpenningActNoticeView:OnClick()
	CalendarWGCtrl.Instance:OnClickAct(self.data)
end

function OpenningActNoticeView:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	if open_type == ACTIVITY_STATUS.CLOSE and activity_type == self.data.cfg.act_type then
		self:Close()
	end
end
