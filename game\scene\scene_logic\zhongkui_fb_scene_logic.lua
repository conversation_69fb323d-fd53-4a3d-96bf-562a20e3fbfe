ZhongKuiFBSceneLogic = ZhongKuiFBSceneLogic or BaseClass(CommonFbLogic)

function ZhongKuiFBSceneLogic:__init()
	self.is_tips_fuligui = true
end

function ZhongKuiFBSceneLogic:__delete()

end

function ZhongKuiFBSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	
end

function ZhongKuiFBSceneLogic:Update(now_time, elapse_time)
	
end

function ZhongKuiFBSceneLogic:Out()
	CommonFbLogic.Out(self)
end

function ZhongKuiFBSceneLogic:CreateHuoLiTips()
	
end

function ZhongKuiFBSceneLogic:OnZhongKuiInfoHandle()
	
end

function ZhongKuiFBSceneLogic:DelayCallBack()
	
end

function ZhongKuiFBSceneLogic:RefreshHuoLi(huili)
	
end

