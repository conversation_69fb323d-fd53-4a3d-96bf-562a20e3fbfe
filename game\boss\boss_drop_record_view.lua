BossDropRecordView = BossDropRecordView or BaseClass(SafeBaseView)
function BossDropRecordView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(978, 590)})
	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_kfboss_drop_record")
end

-- 默认菜单项
local RoleHeadItems = {
	Language.Menu.ShowInfo,
	Language.Menu.AddFriend,
	Language.Menu.InviteTeam,
	Language.Menu.QiuHun,
	Language.Menu.Blacklist,
	Language.Menu.InviteGuild,
}

function BossDropRecordView:OpenCallBack()
end

function BossDropRecordView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Boss.RecordTitle
	self.drop_list = AsyncListView.New(DropR<PERSON>, self.node_list["ph_kf_drop_show"])
end

function BossDropRecordView:SetState(state)
	self.state = state
	if state == BOSS_RECORD_TYPE.WORLD_BOSS then
        BossWGCtrl.Instance:SendWorldBossReq(BossView.ReqType.DROP_HISTORY)
    elseif state == BOSS_RECORD_TYPE.XIANJIE_BOSS then

	elseif state == BOSS_RECORD_TYPE.EVERYDAY_RECHARGE_BOSS then
		BossWGCtrl.Instance:SendCrossRealChargeBossOperate(CROSS_REAL_CHARGE_BOSS_OPERATE_TYPE.RECORD)
	else
		BossWGCtrl.Instance:SendHMSYDropRecord()
	end
end

function BossDropRecordView:ReleaseCallBack()
	if nil ~= self.drop_list then
		self.drop_list:DeleteMe()
		self.drop_list = nil
	end

	self.state = nil
end

function BossDropRecordView:OnFlush()
	local drop_list = BossWGData.Instance:GetNewsList()
	if self.state == BOSS_RECORD_TYPE.WORLD_BOSS then
        drop_list = BossWGData.Instance:GetNewsList()
    elseif self.state == BOSS_RECORD_TYPE.XIANJIE_BOSS then
        drop_list = BossWGData.Instance:GetXianjieDropList()
	elseif self.state == BOSS_RECORD_TYPE.EVERYDAY_RECHARGE_BOSS then
		drop_list = BossWGData.Instance:GetERBDropList()
	else
		drop_list = BossWGData.Instance:GetCrossBossDropRecord()
	end
	if #drop_list == 0 then
		self.node_list["img_no_record"]:SetActive(true)
	else
		self.node_list["img_no_record"]:SetActive(false)
	end
	if self.drop_list then
		self.drop_list:SetDataList(drop_list, 3)
	end
end

-------------------------------------------------------------------------------------------
DropRender = DropRender or BaseClass(BaseRender)
function DropRender:__delete()
	if self.enter_timer then
		GlobalTimerQuest:CancelQuest(self.enter_timer)
		self.enter_timer = nil
	end
end

function DropRender:LoadCallBack()
	self.node_list["item_name"].button:AddClickListener(BindTool.Bind(self.OnClickItem, self))
	self.node_list["role_name"].button:AddClickListener(BindTool.Bind(self.OnClickName, self))
end

function DropRender:OnFlush()
	local item_config = ItemWGData.Instance:GetItemConfig(self.data.item_data.item_id)
    local str = "【%s】"
    if IsEmptyTable(item_config) then
        print_error("取不到物品配置 item_id =",self.data.item_data.item_id)
        return 
    end

	local item_msg = string.format(str, item_config.name)
	local color = ITEM_COLOR[item_config.color]
	self.node_list["item_name"].text.text = ToColorStr(item_msg, color) 

	local map_name = Config_scenelist[self.data.scene_id] and Config_scenelist[self.data.scene_id].name or ""
	local boss_info = BossWGData.Instance:GetMonsterInfo(self.data.monster_id)

	local boss_name = boss_info and boss_info.name or Language.Boss.Unkonwn	-- string.format(str, boss_info.name)
	local time = self.data.drop_timestamp or self.data.pickup_timestamp
	time = os.date("%m-%d  %X", time)
	self.node_list["time"].text.text = time
	local role_name = self.data.name or self.data.role_name


	self.node_list["role_name"].text.text = role_name
	self.node_list["info"].text.text = string.format(Language.Boss.DropRecord1, map_name, boss_name)

	-- local width = self.data.is_top == 1 and 82 or 0
	-- self.node_list["top"].rect.sizeDelta = Vector2(width, 30)

	-- local scro_width = self.data.is_top == 1 and 566 or 640
	-- self.node_list.scroll.rect.sizeDelta = Vector2(scro_width, 30)

	-- 列表滑动
	-- if self.enter_gs_timer then
	-- 	GlobalTimerQuest:CancelQuest(self.enter_timer)
	-- 	self.enter_timer = nil
	-- end

	-- self.enter_timer = GlobalTimerQuest:AddDelayTimer(
	-- 	function() 
	-- 		if self.node_list.content.rect.sizeDelta.x > self.node_list.scroll.rect.sizeDelta.x then
	-- 			self.node_list.scroll.scroll_rect.enabled = true
	-- 			self.node_list.scroll.scroll_rect.horizontalNormalizedPosition = 0
	-- 		else
	-- 			self.node_list.scroll.scroll_rect.enabled = false
	-- 		end
	-- 		self.enter_timer = nil
	-- 	end, 0.1)
end

function DropRender:OnClickItem()
	if not self.data or not self.data.item_data then
		return
	end

    TipWGCtrl.Instance:OpenItem(self.data.item_data)
end

function DropRender:OnClickName()
	if self.data == nil then
		return
	end
    local main_role_id = RoleWGData.Instance:InCrossGetOriginUid()
   
	if self.data.killer_role_id and self.data.killer_role_id == main_role_id then
		return
    end

    if self.data.uuid then --仙界boss
        if self.data.uuid == RoleWGData.Instance:GetUUid() then
            return
        end
        self:DealXianjieItem()
        return
	end

	if self.data.is_same_server and self.data.is_same_server ~= 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.OnlyOneServer)
		return 
	end

	local vo = GameVoManager.Instance:GetMainRoleVo()
	local items = {}
	for k, v in pairs(RoleHeadItems) do
		if self:CanAddMenu(v, vo) then
			table.insert(items, v)
		end
	end

	local pos = self:GetPos(self.node_list["role_name"])
	local player_info = {}
	player_info.user_id = self.data.killer_role_id
	player_info.role_name = self.data.name or self.data.role_name
	UiInstanceMgr.Instance:OpenCustomMenu(items, pos, BindTool.Bind(self.OnClickMenuButton, self), items,nil,nil,nil,player_info)
end

function DropRender:DealXianjieItem()
    self.data.killer_role_id = self.data.uuid.temp_low
    self.data.is_same_server = self.data.usid_str == RoleWGData.Instance:GetOriginalUSIDStr()

    local vo = GameVoManager.Instance:GetMainRoleVo()
	local items = {}
	if self.data.is_same_server then
        for k, v in pairs(RoleHeadItems) do
            if self:CanAddMenu(v, vo) then
                table.insert(items, v)
            end
        end
    else
        table.insert(items, Language.Menu.ShowInfo)
    end

	local pos = self:GetPos(self.node_list["role_name"])
    local plat, server = LLStrToInt(self.data.usid_str)
	local player_info = {}
	player_info.user_id = self.data.killer_role_id
    player_info.role_name = self.data.name or self.data.role_name
    player_info.server_id = server
    player_info.plat_type = plat
	UiInstanceMgr.Instance:OpenCustomMenu(items, pos, BindTool.Bind(self.OnClickMenuButton, self), items,nil,nil,nil,player_info)
end

function DropRender:IsSameServer()
	local main_role_server_id = RoleWGData.Instance:GetMergeServerId()
    local main_role_plat_type = RoleWGData.Instance:GetPlatType()
    local plat, server
    if self.data.usid_str then
        plat, server = LLStrToInt(self.data.usid_str)
    end

	local server_id = server or main_role_server_id
    local plat_type = plat or main_role_plat_type
    self.plat_type = plat_type
	return server_id == main_role_server_id and plat_type == main_role_plat_type
end

function DropRender:OnClickMenuButton(index, sender, param)
	--print_error(index, sender, param)
	self.role_id = self.data.killer_role_id
	local menu_text = param[index]
	if menu_text == Language.Menu.PrivateChat then
		SocietyWGCtrl.Instance:Close()
		ChatWGCtrl.Instance:AddPrivateRequset(self.role_name, nil, self.killer_role_id)

	elseif menu_text == Language.Menu.Trade then
		-- TradeWGCtrl.Instance:SendReqTrade(self.killer_role_id)

	elseif menu_text == Language.Menu.ShowInfo then
        local is_cross = not self:IsSameServer()

		BrowseWGCtrl.Instance:OpenWithUid(self.role_id,nil,is_cross,self.plat_type,1)

	elseif menu_text == Language.Menu.AddFriend then
		SocietyWGCtrl.Instance:IAddFriend(self.role_id)

	elseif menu_text == Language.Menu.SendMail then
		SocietyWGCtrl.Instance:IOpenSendMail(self.role_name)

	elseif menu_text == Language.Menu.InviteTeam then
		BrowseWGCtrl.Instance:AllReqRoleInfo(self.role_id,self.plat_type, function (protocol)
			if protocol.is_online == 1 or protocol.is_online == 3 then
				if 0 == SocietyWGData.Instance:GetIsInTeam() then
					local team_type = 0
    				local fb_mode = 1
    				local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()	--获取服务器最高世界等级
    				local min_level, max_level = COMMON_CONSTS.NoalGoalLimitMinLevel, top_user_level
					NewTeamWGCtrl.Instance:SendCreateTeam(team_type, fb_mode, min_level, max_level)
				end
				if self.is_inteam_index > 0 then
					SocietyWGCtrl.Instance:SendReqJoinTeam(self.is_inteam_index)
				else
					SocietyWGCtrl.Instance:ITeamInvite(protocol.role_id, self.team_type)
				end
			else
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOnline)
			end
		end)

	elseif menu_text == Language.Menu.ApplyTeam then

	elseif menu_text == Language.Menu.InviteGuild then
		GuildWGCtrl.Instance:SendInviteGuild(self.role_id)

	elseif menu_text == Language.Menu.ChangeLeader then
		SocietyWGCtrl.Instance:SendChangeTeamLeader(self.role_id)

	elseif menu_text == Language.Menu.DeleteFriend then
		local lover_id = RoleWGData.Instance.role_vo.lover_uid
		if self.role_id ~= lover_id then
			if nil == self.alert_window then
				self.alert_window = Alert.New()
			end
			self.alert_window:SetLableString(string.format(Language.Society.DeleteFriend, self.role_name))
			self.alert_window:SetOkFunc(function ()
				SocietyWGCtrl.Instance:DeleteFriend(self.role_id)
			end)
			self.alert_window:Open()
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.RemoveLoverTips)
		end

	elseif menu_text == Language.Menu.DeleteEnemy then
		SocietyWGCtrl.Instance:DeleteEnemy(self.role_id, self.plat_type)

	elseif menu_text == Language.Menu.CopyName then

	elseif menu_text == Language.Menu.Blacklist then
		SocietyWGCtrl.Instance:DeleteFriend(self.role_id)
		ChatWGCtrl.Instance:SendAddBlackReq(self.role_id)

	elseif menu_text == Language.Menu.BlackRemove or menu_text == Language.Menu.BlackRemove_2 then

		ChatWGCtrl.Instance:SendDeleteBlackReq(self.role_id)

	elseif menu_text == Language.Menu.QiuHun then
		MarryWGCtrl.Instance:OpenSelectLoverView(1) --1结婚2离婚
	end
end

function DropRender:GetPos(node)
    if nil == node then return nil end
    local main_view = ViewManager.Instance:GetView(GuideModuleName.MainUIView)
	if nil == main_view or not main_view:IsOpen() then
		return
	end

    local parent_rect= main_view.root_node.transform:GetComponent(typeof(UnityEngine.RectTransform))
	local x, y = parent_rect.sizeDelta.x / 2, parent_rect.sizeDelta.y / 2
    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, node.transform.position)
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, Vector2(0, 0))

    x = local_position_tbl.x
    y = local_position_tbl.y
    return Vector2(x + 245, y)
end

function DropRender:GetItems()
    local items = {Language.NewTeam.MenuBrouse}
    local callback_param = {}
    local index = 1
    if nil == SocietyWGData.Instance:FindFriend(self.data.killer_role_id) and not UserVo.IsCrossServer(self.data.killer_role_id) then
        table.insert(items, Language.NewTeam.MenuAddFriend)
        callback_param.has_add_friend = true
        index = index + 1
        callback_param.add_friend_index = index
    end

    return items, callback_param
end

-- 判断是否可以添加菜单项
function DropRender:CanAddMenu(item_name, mainrole_vo)
	if not self.is_online then
		-- 离线时不显示的菜单项
		if item_name == Language.Menu.Trade
			or item_name == Language.Menu.AddFriend
			or item_name == Language.Menu.InviteTeam
			or item_name == Language.Menu.ApplyTeam
			or item_name == Language.Menu.InviteGuild
			or item_name == Language.Menu.ChangeLeader
			--or item_name == Language.Menu.Profess
			or item_name == Language.Menu.QiuHun
			then
			return false
		end
	end

	if item_name == Language.Menu.InviteGuild then
		if mainrole_vo.guild_post ~= GUILD_POST.TUANGZHANG and mainrole_vo.guild_post ~= GUILD_POST.FU_TUANGZHANG then
			return false
		end
	elseif item_name == Language.Menu.ChangeLeader then
		if 0 == SocietyWGData.Instance:GetIsTeamLeader() then
			return false
		end
	elseif item_name == Language.Menu.AddFriend then
		if SocietyWGData.Instance:CheckIsFriend(self.role_id) then
			return false
		end
	elseif item_name == Language.Menu.QiuHun then
		local is_show = SocietyWGData.Instance:GetQiuHunBtnShow(self.role_id)
		return is_show
	end

	return true
end