CultivationWGCtrl = CultivationWGCtrl or BaseClass(BaseWGCtrl)

function CultivationWGCtrl:InitCharmCtrl()
    self.suit_overview_view = CharmSuitOverViewView.New()
	self.charm_compose_view = CharmComposeView.New(GuideModuleName.CharmComposeView)
	self.charm_attr_view = CharmAttrView.New()
	self.charm_tianzui_view = CharmTianZuiView.New()
	self.charm_lingzhu_tunshi_view = CharmLingZhuTunShiView.New()

    self:RegisterProtocol(CSCharmOperate)
	self:RegisterProtocol(CSCharmEquipDecompos)
	self:RegisterProtocol(CSCharmEquipWear)
	self:RegisterProtocol(SCCharmSoltInfo, "OnSCCharmSoltInfo")
	self:RegisterProtocol(SCCharmSoltUpdate, "OnSCCharmSoltUpdate")
	self:RegisterProtocol(SCCharmYinYangInfo, "OnSCCharmYinYangInfo")
	self:RegisterProtocol(SCCharmMsgBag, "OnSCCharmMsgBag")
	self:RegisterProtocol(SCCharmMsgBagUpdate, "OnSCCharmMsgBagUpdate")
end

function CultivationWGCtrl:DeleteCharmCtrl()
    if self.charm_compose_view then
		self.charm_compose_view:DeleteMe()
		self.charm_compose_view = nil
	end

	if self.charm_attr_view then
		self.charm_attr_view:DeleteMe()
		self.charm_attr_view = nil
	end

	if self.suit_overview_view then
		self.suit_overview_view:DeleteMe()
		self.suit_overview_view = nil
	end

	if self.charm_lingzhu_tunshi_view then
		self.charm_lingzhu_tunshi_view:DeleteMe()
		self.charm_lingzhu_tunshi_view = nil
	end

	if self.charm_tianzui_view then
		self.charm_tianzui_view:DeleteMe()
		self.charm_tianzui_view = nil
	end
end

function CultivationWGCtrl:OnCSCharmOperate(operate_type, param_1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCharmOperate)
	protocol.operate_type = operate_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function CultivationWGCtrl:OnCSCharmEquipDecompos(count, msg_item)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCharmEquipDecompos)
	protocol.count = count or 0
 	protocol.msg_item = msg_item or {}
 	protocol:EncodeAndSend()
end

function CultivationWGCtrl:OnCSCharmEquipWear(count, msg_item)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCharmEquipWear)
	protocol.count = count or 0
 	protocol.msg_item = msg_item or {}
 	protocol:EncodeAndSend()
end

----------------------------------------open_view_start--------------------------------------------
function CultivationWGCtrl:OpenCharmComposeView()
	if self.charm_compose_view and not self.charm_compose_view:IsOpen() then
		self.charm_compose_view:Open()
	end	
end

function CultivationWGCtrl:OpenCharmTianZuiView()
	if self.charm_tianzui_view and not self.charm_tianzui_view:IsOpen() then
		self.charm_tianzui_view:Open()
	end	
end

function CultivationWGCtrl:OpenCharmAttrView()
	if self.charm_attr_view and not self.charm_attr_view:IsOpen() then
		self.charm_attr_view:Open()
	end	
end

function CultivationWGCtrl:OpenCharmSuitOverView()
	if self.suit_overview_view and not self.suit_overview_view:IsOpen() then
		self.suit_overview_view:Open()
	end	
end

function CultivationWGCtrl:OpenCharmLingZhuTunShiView()
	if self.charm_lingzhu_tunshi_view and not self.charm_lingzhu_tunshi_view:IsOpen() then
		self.charm_lingzhu_tunshi_view:Open()
	end	
end

function CultivationWGCtrl:PlayCharmLingZhuUpFlag(index)
	if self.charm_tianzui_view and self.charm_tianzui_view:IsOpen() then
		self.charm_tianzui_view:PlayCharmLingZhuUpEffect(index)
	end
end
-----------------------------------------open_view_end---------------------------------------------

-----------------------------------------protocol_start---------------------------------------------
function CultivationWGCtrl:OnSCCharmSoltInfo(protocol)
	--print_error("槽位数据", protocol)
	self.data:SetCharmSoltInfo(protocol)
    RemindManager.Instance:Fire(RemindName.Charm_Holy_Seal)
    ViewManager.Instance:FlushView(GuideModuleName.CultivationView, TabIndex.tiandao_stone)
end

function CultivationWGCtrl:OnSCCharmSoltUpdate(protocol)
	--print_error("槽位数据更新", protocol)
	self.data:CharmSoltUpdate(protocol)
	RemindManager.Instance:Fire(RemindName.Charm_Holy_Seal)
	ViewManager.Instance:FlushView(GuideModuleName.CultivationView, TabIndex.tiandao_stone)
end

function CultivationWGCtrl:OnSCCharmYinYangInfo(protocol)
	-- print_error("阴阳info", protocol)
	local old_level = self.data:GetCharmYingYangLevel()
	local change_index = self.data:GetCharmYingYangBeadChangeIndex(protocol.yinyang_bead_level_list)

	if change_index > 0 then
		self:PlayCharmLingZhuUpFlag(change_index)
	end

	self.data:SetCharmYingYangInfo(protocol)
	local new_level = self.data:GetCharmYingYangLevel()

	RemindManager.Instance:Fire(RemindName.Charm_LingZhu)
	RemindManager.Instance:Fire(RemindName.Charm_Holy_Seal)
	if self.charm_tianzui_view and self.charm_tianzui_view:IsOpen() then
		self.charm_tianzui_view:Flush()

		if new_level > old_level and (new_level - old_level == 1) then
			TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji,
	    		is_success = true, pos = Vector2(0, 0)})
		end
	end

	if self.charm_lingzhu_tunshi_view and self.charm_lingzhu_tunshi_view:IsOpen() then
		self.charm_lingzhu_tunshi_view:Flush()
	end
end

function CultivationWGCtrl:OnSCCharmMsgBag(protocol)
	-- print_error("背包数据", protocol)
	self.data:SetCharmBagInfo(protocol)

	if self.charm_compose_view and self.charm_compose_view:IsOpen() then
		self.charm_compose_view:Flush()
	end	
end

function CultivationWGCtrl:OnSCCharmMsgBagUpdate(protocol)
	--print_error("背包数据更新", protocol)
	local new_data = protocol.grid_info
	local is_add, add_num = self.data:UpdateCharmBagInfo(protocol)

	if not IsEmptyTable(new_data) and new_data.item_id > 0 and new_data.num > 0 then
		if is_add and add_num > 0 then
			local name = ItemWGData.Instance:GetItemNameDarkColor(new_data.item_id)
			local str = string.format(Language.Bag.GetItemTxt, ToColorStr(name, ITEM_TIP_D_COLOR[new_data.color]), add_num)
			GlobalTimerQuest:AddDelayTimer(function ()
				SysMsgWGCtrl.Instance:ErrorRemind(str)
			end, 0.2)
		end
	end

	RemindManager.Instance:Fire(RemindName.Charm_Holy_Seal)
	ViewManager.Instance:FlushView(GuideModuleName.CultivationView, TabIndex.tiandao_stone)

	if self.charm_compose_view and self.charm_compose_view:IsOpen() then
		self.charm_compose_view:Flush()
	end

	ViewManager.Instance:FlushView(GuideModuleName.Boss, TabIndex.boss_dabao)
	BossWGCtrl.Instance:FlushDabaoBossEquipShowView()
end