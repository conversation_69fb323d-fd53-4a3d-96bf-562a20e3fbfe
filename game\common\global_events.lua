--全局事件定义：主要是通用模块中的事件，比如游戏进程、本玩家动作及状态改变、场景选中对象、商城购买物品道具等
--基础模块是指在两个其它模块以上中会调用到的功能，例如购买物品道具，很多地方都会用到

SystemEventType =
{
	GAME_PAUSE = "game_pause",
	GAME_FOCUS = "game_focus",
}

--登入游戏相关事件
LoginEventType =
{
	LOGIN_SERVER_CONNECTED = "login_server_connected",					-- 登录服连接结果反馈(is_succ)
	LOGIN_SERVER_DISCONNECTED = "login_server_disconnected",			-- 登录服断开连接

	GAME_SERVER_CONNECTED = "game_server_connected",					-- 游戏服连接结果反馈(is_succ)
	GAME_SERVER_DISCONNECTED = "game_server_disconnected",				-- 游戏服断开连接

	ENTER_GAME_SERVER_SUCC = "enter_game_server_succ",					-- 登录游戏服成功

	LOADING_COMPLETED = "loading_completed",							-- 加载完成

	RECV_MAIN_ROLE_INFO = "recv_main_role_info",						-- 收到主角信息

	START_OPENING_ANIMATION = "start_opening_animation",				-- 开始开场剧情
	END_OPENING_ANIMATION = "end_opening_animation",					-- 结束开场剧情

	CREATE_ROLE = "CREATE_ROLE",										-- 创建角色
	LOGOUT = "LOGOUT",													-- 角色登录
}

-- 场景对象相关事件
ObjectEventType =
{
	MAIN_ROLE_CREATE = "main_role_create",								-- 玩家自己创建完毕
	OBJ_CREATE = "obj_create",											-- 对象创建(obj)
	OBJ_DEAD = "obj_dead",												-- 对象死亡(obj)
	OBJ_DELETE = "obj_delete",											-- 对象被删除(obj)
    BE_SELECT = "be_select",											-- 对象被选中(obj, select_type)
    CANCEL_SELECT = "cancel_select",									-- 取消选中(obj)
    ON_CLICKED = "on_clicked",											-- 对象被点击(obj)
	TARGET_HP_CHANGE = "target_hp_change",								-- 对象血量变化(obj)
	OBJ_MAIN_PART_LOADED = "obj_main_part_loaded", 						-- 对象main_part加载完
	TEAM_HP_CHANGE = "team_hp_change",									-- 附近队友血量变化(obj)
	OBJ_MONSTER_CHANGE = "obj_monster_change",							-- 指定怪物伤害变化(obj)

	MAIN_ROLE_MOVE_START = "main_role_move_start",						-- 主角开始移动（包括移动中重新发起移动）
	MAIN_ROLE_MOVE_END = "main_role_move_end",							-- 主角移动结束
	MAIN_ROLE_POS_CHANGE = "main_role_pos_change",						-- 主角位置改变(x, y)
	MAIN_ROLE_RESET_POS = "main_role_reset_pos",						-- 主角位置重置
	MAIN_ROLE_USE_SKILL = "main_role_use_skill",						-- 主角使用技能(skill_id)
	MAIN_ROLE_DEAD = "main_role_dead",									-- 主角死亡(main_role)
	MAIN_ROLE_REALIVE = "main_role_realive",							-- 主角复活(main_role)
	MAIN_ROLE_APPERANCE_CHANGE = "main_role_apperance_change",			-- 主角形象改变
	MAIN_ROLE_BE_HIT = "MAIN_ROLE_BE_HIT",								-- 主角被打
	MAIN_ROLE_DO_HIT = "MAIN_ROLE_DO_HIT",								-- 主角攻击
	MAIN_ROLE_EXP_CHANGE = "MAIN_ROLE_EXP_CHANGE",						-- 主角经验改变
	MAIN_ROLE_ENTER_JUMP_STATE = "main_role_enter_jump_state",			-- 主角进入Jump状态
	MAIN_ROLE_EXIT_JUMP_STATE = "main_role_exit_jump_state",			-- 主角离开Jump状态
	MAIN_ROLE_ADD_BUFF = "main_role_add_buff",							-- 主角加buff
	MAIN_ROLE_REMOVE_BUFF = "main_role_remove_buff",					-- 主角移除buff
	MAIN_ROLE_FLY_DOWN_END = "mian_role_fly_down_end", 					-- 主角下降完毕
	REMOVE_BUFF = "remove_buff",										-- 移除buff
	START_GATHER = "start_gather",										-- 采集开始(role)
	STOP_GATHER = "stop_gather",										-- 采集结束(role)
	COMPLETE_GATHER = "complete_gather",								-- 完成采集
	GATHER_TIMER = "gather_timer",										-- 主角采集时间
	ENTER_FIGHT = "enter_fight",										-- 主角进入战斗状态
	EXIT_FIGHT = "exit_fight",											-- 主角离开战斗状态
	SPECIAL_SHIELD_CHANGE = "special_shield_change",					-- 护盾变化
	MAIN_ROLE_AUTO_XUNLU = "main_role_auto_xunlu",						-- 自动寻路（状态，是否手动操作）
	FOLLOW_NPC_CHANGE = "follow_npc_change", 							-- 跟随NPC改变

	MAIN_ROLE_CHANGE_AREA_TYPE = "main_role_change_area_type",			-- 玩家改变场景区域的类型，如离开安全区

	FIGHT_EFFECT_CHANGE = "fight_effect_change",						-- 战斗Effect变更(is_main_role)
	CAN_NOT_FIND_THE_WAY = "can't find the way",						-- 无法找到路径
	HEAD_CHANGE = "head_change",										-- 主角头像改变
	LEVEL_CHANGE = "level_change",										-- 主角等级改变
	CLICK_SHUANGXIU = "click_shuangxiu",								-- 对象点击双修事件
	GUILD_HEAD_CHANGE = "guild_head_change",							-- 公会头像改变

	LEAVE_SCENE = "leave_scene",										-- 强制脱离卡死进度条
	VISIBLE_OBJ_ENTER_MONSTER = "visible_obj_enter_monster",			-- 怪物进入视野
	VISIBLE_OBJ_ENTER_GATHER = "visible_obj_enter_gather",				-- 采集物进入视野

	SELECT_DEFENSE_OBJ = "select_defense_obj",							-- 选择防御塔

	FLY_TO_HUSONG_NPC = "fly_to_husong_npc", 							--飞到护送NPC
	FLY_TO_SCENE = "fly_to_scene", 							    		--使用小飞鞋飞到对应Scene
	CLICK_GROUND_STOP_KILL = "click_ground_stop_kill",  				--
	CG_EVENT_END = "cg_event_end",                                      --CG播放完成
	HP_RECOVER = "hp_recover",											-- hp恢复
	TOWER_ATTACK_RANGE = "tower_attack_range",                          --塔防本显示攻击范围特效
	QUALITY_CHANGE = "quality_change",
	HuDun_Change = "HuDun_Change",										-- 护盾变化
	NPC_TALK = "npc_talk",												--NPC对话
	ROLE_HURT_CHANGE = "role_hurt_change",								-- 战斗单位伤害改变
	ROLE_FOLLOW_STATUS_CHANGE = "role_follow_status_change",			-- 主角跟随状态变化

	SHIELD_OTHER_SCENEOBJ_FOLLOWUI_IN_DEPTH_OF_FIELD = "shield_other_sceneobj_followui_in_depth_Of_field",	--使用景深后屏蔽场景除主角外其他角色follow_ui
}

-- 场景相关事件
SceneEventType =
{
	SCENE_LOADING_STATE_ENTER = "scene_loading_state_enter",			-- 进入场景加载事件
	SCENE_LOADING_STATE_QUIT = "scene_loading_state_quit",				-- 场景加载结束
	SCENE_CHANGE_COMPLETE = "scene_change_complete",					-- 场景改变事件
	SHOW_MAINUI_RIGHT_UP_VIEW = "show_mainui_right_up_view"	,			-- 主界面右上界面显示
	ROLE_ENTER_ROLE = "role_enter_role",								-- 【玩家】进入角色视野
	OBJ_LEVEL_ROLE = "obj_level_role",									-- 【物体】离开角色视野
	OBJ_ENTER_SHADOW = "obj_enter_shadow",								-- 【机器人】进入角色视野
	SCENE_ALL_LOAD_COMPLETE = "scene_all_load_complete",				-- 场景所有加载完成(主场景和细节场景)
	SCENE_WATER_EFFECT_END = "scene_water_effect_end",					-- 水波纹效果结束
	FLUSH_GUILD_SCENE_VIEW = "flush_guild_scene_view",                  -- 刷新帮派试炼面板
	CLOSE_LOADING_VIEW = "close_loading_view",                   		-- 关闭加载界面
	SCENE_VOLUME_CREATE_COMPLETE = "SCENE_VOLUME_CREATE_COMPLETE",		-- Scene Volume创建完成
	ENTER_OTHER_SERVER_COMPLETE = "enter_other_server_complete",  		-- 进入其他服完成
	CHANGE_ROLE_WANGQI_MODE_STATUS = "change_role_wangqi_mode_status",	-- 望气模式状态改变

	UI_SCENE_LOADING_STATE_QUIT = "ui_scene_loading_state_quit",		-- UI场景加载结束
	UI_SCENE_CHANGE_COMPLETE = "ui_scene_change_complete",				-- UI场景切换完成	
}

--Touch相关事件
LayerEventType =
{
	KEYBOARD_RELEASED = "keyboard_released",							-- 按键事件
	TOUCH_BEGAN = "touch_began",										-- 触摸事件 按下
	TOUCH_MOVED = "touch_moved",										-- 触摸事件 移动
	TOUCH_ENDED = "touch_ended",										-- 触摸事件 抬起
	TOUCH_CANCELLED = "touch_cancelled",								-- 触摸事件 取消
	LINE_GESTURE = "line_gesture",										-- 线性手势(方向 0123==上右下左)
	ACCELEROMETER = "accelerometer",									-- 加速计事件(x, y, z)

	SCREEN_TOUCH_BEGAN = "screen_touch_began",							-- 屏幕触摸事件 按下
	SCREEN_TOUCH_MOVED = "screen_touch_moved",							-- 屏幕触摸事件 按下
	SCREEN_TOUCH_ENDED = "screen_touch_ended",							-- 屏幕触摸事件 抬起
	SCREEN_TOUCH_CANCELLED = "screen_touch_cancelled",					-- 屏幕触摸事件 取消

	MOUSE_BUTTON_DOWN = "mouse_button_down", 							-- 按下鼠标
	MOUSE_BUTTON_UP = "mouse_button_up", 								-- 抬起鼠标

	SCENE_CLICK_FLOOR = "scene_click_floor",							-- 点击地板
}

-- 主界面相关事件
MainUIEventType =
{
	MAIN_HEAD_CLICK = "main_head_click",
	MAIN_BTN_STATE = "main_btn_state",
	ROLE_SKILL_CHANGE = "role_skill_change",
	ROLE_SKILL_DUAN_ATK_CHANGE = "role_skill_duan_atk_change",
	ROLE_SKILL_BIANSHEN_CLICK = "role_skill_bianshen_click",
	ROLE_SKILL_BIANSHEN = "role_skill_bianshen", 						-- 变身技能
	ROLE_SKILL_LIST = "role_skill_list",
	ROLE_BEASTS_SKILL_LIST = "role_beasts_skill_list",					-- 驭兽技能
	EXP_EFFICIENCY_INFO = "ExpEfficiencyInfo",
	CHAT_CHANGE = "chat_change",
	MAINUI_OPEN_COMLETE = "mainui_open_complete",

	MAIN_FUNC_OPEN = "main_func_open",	--主界面功能开放
	CHANGE_RED_POINT = "change_red_point",	--改变主界面红点
	SHOW_OR_HIDE_OTHER_BUTTON = "show_or_hide_other_button",			-- 显示或隐藏其他界面的按钮
	SHOW_OR_HIDE_REBATE_BUTTON = "show_or_hide_rebate_button",			-- 显示或隐藏百倍返利按钮
	SHOW_OR_HIDE_SHRINK_BUTTON = "show_or_hide_shrink_button",			-- 显示或隐藏收缩按钮
	SHOW_OR_HIDE_TASK_BUTTON = "show_or_hide_task_button",				-- 显示或隐藏左边任务组队面板
	PORTRAIT_TOGGLE_CHANGE = "portrait_toggle_change",					-- 显示或隐藏左上角图标
	MAINUI_CLEAR_TASK_TOGGLE = "mainui_clear_task_toggle",				-- 取消任务栏选择
	SHOW_OR_HIDE_MODE_LIST = "show_or_hide_mode_list",					-- 显示或隐藏模式列表
	SHOW_OR_HIDE_DAFUHAO_INFO = "show_or_hide_dafuhao_info",			-- 显示或隐藏大富豪面板
	FIGHT_STATE_BUTTON = "fight_state_button",							-- 战斗收缩按钮(右下角)
	CHNAGE_FIGHT_STATE_BTN = "chnage_fight_state_btn",					-- 改变战斗收缩按钮操作

	CHAT_VIEW_HIGHT_CHANGE = "chat_view_hight_change",					-- 主界面聊天框高度改变
	CHANGE_MAINUI_BUTTON = "change_mainui_button",						-- 主界面活动按钮改变：按钮生成或销毁
	CLICK_SKILL_BUTTON = "click_skill_button",							-- 按下技能键
	CLICK_PET_BUTTON = "click_pet_button", 								-- 按下宠物技能

	JINJIE_EQUIP_SKILL_CHANGE = "jinjie_equip_skill_change",			-- 进阶装备技能改变
	OPEN_NEAR_VIEW = "mainui_open_near_view",							-- 打开附件玩家界面
	JUNXIAN_LEVEL_CHANGE = "junxian_level_change",						-- 军衔等级改变
	SHOW_TASK_TYPE = "show_task_type" ,                                 -- 更换任务栏显示
	SHOW_ASCRIPTION = "show_ascripion",                                 -- 显示归属

	SHOW_MAIN_CHAT = "show_main_chat",									-- 显示主界面
	MAIN_TOP_ARROW_CLICK = "main_top_arrow_click",						-- 右上角按钮点击事件
	MAIN_CHAT_ZUOQI_CLICK = "main_chat_zuoqi_click",					-- 坐骑按钮点击事件
	INIT_ICON_LIST = "init_icon_list",									-- 初始化主界面图标
	SETMAINTASK = "set_main_task", 										-- 主线任务相关
	BIANSHEN_SKILL_CHANGE = "bianshen_skill_change",					-- 主角变身技能信息改变
	TianShen_Item_Change = "TianShen_Item_Change",						-- 天神背包物品改变
	CAMERA_HANDLE_CHANGE = "camera_handle_change",						-- 相机主动改变
	MAIN_MENU_ICON_CHANGE = "main_menu_icon_change",					-- 主界面副屏MenuIcon改变
	MAIN_TOP_ARROW_ANIM_COMPLETE = "main_top_arrow_anim_complete",		-- 主界面按钮动画全部播放完回调
	SIXIANG_ICON_TIYE_CHANGE = "sixiang_icon_type_change",				-- 主界面四象召唤类型改变
}

KnapsackEventType = {
	KNAPSACK_EXTEND_BAG = "knapsack_extend_bag",						-- 背包拓展
	KNAPSACK_EXTEND_STORAGE = "knapsack_extend_storage",				-- 仓库拓展
	KNAPSACK_LECK_ITEM = "knapsack_leck_item",							-- 物品不足
}

SettingEventType = {
	SHIELD_OTHERS = "shield_others",									--屏蔽其他玩家
	SELF_SKILL_EFFECT = "self_skill_effect",							--屏蔽自己技能特效
	SHIELD_SAME_CAMP = "shield_same_camp",								--屏蔽友方玩家
	SKILL_EFFECT = "skill_effect",										--屏蔽他人技能特效
	CLOSE_BG_MUSIC = "close_bg_music",									--关闭背景音乐
	CHANGE_FPS = "change_fps",											--切换帧率
	CLOSE_SOUND_EFFECT = "close_sound_effect",							--关闭音效
	FLOWER_EFFECT = "flower_effect",									--屏蔽送花特效
	FRIEND_REQUEST = "friend_request", 									--拒绝好友邀请
	STRANGER_CHAT = "stranger_chat", 									--拒绝陌生私聊
	CLOSE_TITLE = "close_title",	 									--屏蔽称号显示
	CLOSE_HEARSAY = "close_hearsay",									--屏蔽传闻
	CLOSE_GODDESS = "close_goddess",									--屏蔽女神
	AUTO_RELEASE_SKILL = "auto_release_skill",							--自动释放技能
	AUTO_RELEASE_ANGER = "auto_release_anger",							--自动释放怒气技能
	CLOSE_SHOCK_SCREEN = "close_shock_screen",							--关闭震屏效果
	CLOSE_SKILL_RADIAL_BLUR = "CLOSE_SKILL_RADIAL_BLUR",				--关闭技能模糊效果
	AUTO_PICK_PROPERTY = "auto_pick_property",							--自动拾取道具
	AUTO_RECYCLE_EQUIP = "auto_recycle_equip",							--自动回收装备
	-- USE_NOTBLIND_EQUIP = "use_notblind_equip",						--只消耗非绑定装备
	SHIELD_ENEMY = "shield_enemy",										--屏蔽怪物
	SHIELD_SPIRIT = "shield_spirit",									--屏蔽精灵
	CLOSE_WEATHWE = "close_weather",									--关闭天气
	AUTO_RECYCLE_BLUE = "recycle_blue",									--自动回收蓝色
	AUTO_RECYCLE_PURPLE = "recycle_purple",								--自动回收紫色
	AUTO_RECYCLE_ORANGE = "recycle_orange",								--自动回收橙色
	AUTO_RECYCLE_RED = "recycle_red",									--自动回收红色
	AUTO_LUCK_SCREEN = "luck_screen",									--锁屏
	AUTO_BIANSHEN = "auto_bianshen",									--自动变身
	AUTO_REVIVE = "auto_revive",										--自动复活
	AUTO_RELEASE_GODDESS_SKILL = "auto_release_goddess_skill",			--自动释放女神技能
	AUTO_USE_FLY_SHOE = "auto_use_fly_shoe",							--自动使用小飞鞋
	AUTO_SIXIANG_SKILL = "auto_sixiang_skill",							--自动释放四象技能
	AUTO_LONGZHU_SKILL = "auto_longzhu_skill",							--自动释放龙珠技能
	AUTO_WUXING_SKILL = "auto_wuxing_skill",							--自动释放五行技能
	AUTO_HALO_SKILL = "auto_halo_skill",                                --自动释放光环技能
	FIGHT_MOUNT_SKILL_CG_PLAY = "fight_mount_skill_cg_play",            -- 新战斗坐骑大招节能播放cg
	AUTO_TIANSHEN_HEJI_SKILL  = "auto_tianshen_heji_skill",             -- 挂机时自动释放天神合击技能
	AUTO_YU_LONG = "auto_yu_long",                                      -- 自动御龙
	AUTO_CUSTOMIZED_SKILL = "auto_customized_skill",					-- 自动定制技能
	AUTO_PICK_COLOR = "auto_pick_color",
	AUTO_RECYCLE_COLOR = "auto_recycle_color",
	AUTO_GUNDAM = "auto_gundam",										-- 自动变身高达
	AUTO_ESOTERICA_SKILL = "auto_esoterica_skill",						-- 挂机时自动释放秘笈技能
	CLOSE_OTHERS_RUMOR = "close_other_rumor",							-- 屏蔽他人自定义传闻
	CLOSE_OTHERS_BEAST = "close_other_beast",							-- 屏蔽他人驭兽
	AUTO_BEASTS_SKILL = "auto_beasts_skill",							-- 挂机时自动释放御兽技能
	CLOSE_SELECT_EFFECT = "close_select_effect",						-- 关闭攻击选中效果
	GUAJI_SETTING_CHANGE = "guaji_setting_change",						-- 挂机设置改变
	MAIN_CAMERA_MODE_CHANGE = "main_camera_change",						-- 摄像机镜头改变
	MAIN_CAMERA_SETTING_CHANGE = "main_camera_setting_change",			-- 摄像机镜头参数改变

	Change_Attack_Mode = "Change_Attack_Mode",							-- 切换攻击模式
	-- 下面几个是cocos项目的
	SYSTEM_SETTING_CHANGE = "system_setting_change",					-- 系统设置改变
	-- GUAJI_SETTING_CHANGE = "guaji_setting_change",						-- 挂机设置改变
	SOUND_SETTING_CHANGE = "sound_setting_change",						-- 音效设置改变
	BG_SOUND_SETTING_CHANGE = "bg_sound_setting_change",				-- 背景音乐设置改变
	REFUSE_STRANGER_TEAM = "refuse_stranger_team",						-- 拒绝陌生人组队邀请
	AUTO_JINGJIE_BIANSHEN = "auto_jingjie_bianshen",					-- 自动境界变身

}

OtherEventType = {
	ROLE_ONLINE_CHANGE = "role_online_change",							-- 其它玩家在线信息改变
	DAY_COUNT_CHANGE = "day_count_change",								-- 每日计数改变(day_counter_id, -1表示全部)
	PASS_DAY = "pass_day",												-- 换天时发送
	PASS_DAY2 = "pass_day2",											-- 换天时发送(第一次改变也调用)
	TEAM_INFO_CHANGE = "team_info_change",								-- 队伍信息改变
	WORLD_LEVEL_CHANGE = "world_level_change",							-- 世界等级改变
	GUAJI_TYPE_CHANGE = "guaji_type_change",							-- 挂机类型改变
	HAND_CLICK_GUAJI_BTN = "hand_click_guaji_btn", 						-- 手动点挂机按钮
	TASK_CHANGE = "task_change",										-- 任务改变
	GUIDE_STEP_CHANGE = "guide_step_change",							-- 引导步骤改变
	SHOW_TASK_CHANGE = "show_task_change",								-- 显示中的任务改变(3d为了改变任务)
	CAMP_BOSS_CHANGE = "camp_boss_change",								-- 阵营普通夺宝
	CAMP_STATUE_CHANGE = "camp_statue_change",							-- 阵营雕像信息
	ACTIVITY_CHANGE = "activity_change",								-- 活动改变
	FUBEN_COUNTDOWN = "fuben_countdown",								-- 副本倒计时
	FUBEN_QUIT = "fuben_quit",											-- 副本离开
	BOSS_CHANGE = "boss_change",										-- boss改变
	MOUNT_INFO_CHANGE = "mount_info_change",
	OPERATE_RESULT = "operate_result",									-- 操作结果
	WAREHOUSE_FLUSH_VIEW = "warehouse_flush_view",						-- 刷新仓库
	RECYCLE_FLUSH_CONTENT = "recycle_flush_content",					-- 刷新回收面板
	OPEN_RECYCLE_VIEW = "open_recycle_view",							-- 打开回收面板
	FLUSH_BAG_GRID = "flush_bag_grid",									-- 刷新背包面板
	FLUSH_MAGIC_BAG = "flush_magic_bag",								-- 刷新魔器背包
	FLUSH_RESOLVE_EVENT = "flush_resolve_event",						-- 刷新天神武器分解面板
	ROLE_LEVEL_UP = "role_level_up",									-- 角色等级改变
	TASK_WINDOW = "task_window",										-- 任务对话框
	TURN_COMPLETE = "turn_complete",									-- 转盘结束
	USE_PROP_SUCCE = "use_prop_succe",									-- 使用物品成功
	CLOSE_FUBEN_FAIL_VIEW = "close_fuben_fail_view",					-- 关闭副本失败面板
	RoleInfo = "role_info",												-- 角色信息返回（通过id查询的）
	GUILD_MEMBER_INFO_CHANGE = "guild_member_info_change",				-- 公会成员信息改变
	RANDOM_INFO_CHANGE = "random_info_change",							-- 随机在线玩家列表改变
	FRIEND_INFO_CHANGE = "friend_info_change",							-- 好友信息改变
	REPAIR_STATE_CHANGE = "repair_state_change",						-- 双修状态改变
	DAFUHAO_INFO_CHANGE = "dafuhao_info_change",						-- 大富豪信息改变
	EQUIP_DATA_CHANGE = "equip_data_change",							-- 装备数据改变
	ROBERT_ATTACK_ROBERT = "robert_attack_robert",						-- 机器人攻击机器人时
	ROBERT_DIE = "robert_die",											-- 机器人死亡
	ROLE_ISONLINE_CHANGE = "role_isonline_change",						-- 玩家上下线通知
	RANK_CHANGE = "rank_change",										-- 排行榜信息改变
	CLOSE_VIEW_EVENT = "CLOSE_VIEW_EVENT",								-- 关闭系统事件

	VIRTUAL_TASK_CHANGE = "virtual_task_change",						-- 虚拟任务改变
	SHOW_TASK_GUIDE_DIALOG = "show_task_guide_dialog",					-- 显示任务引导对话框
	ROLE_NAME_INFO = "role_name_info",									-- 角色信息查询返回（通过名字查询的）
	VIEW_CLOSE = "view_close",											-- 界面关闭
	VIEW_OPEN = "view_open",											-- 界面打开
	POWER_CHANGE_VIEW_OPEN = "power_change_view_open",					-- 战力变化面板打开关闭
	RANDOW_ACTIVITY = "random_activity",								-- 随机活动返回
	MOVE_BY_CLICK = "move_by_click",									-- 玩家手动控制主角移动
	JUMP_STATE_CHANGE = "jump_state_change",							-- 主角跳跃状态改变
	CHEST_SHOP_ITEM_LIST = "ChestShopItemList",							-- 获得新的抽奖物品
	ON_GATHER_ENTER_SCENE = "on_gather_enter_scene",
	BLACK_LIST_CHANGE = "black_list_change",							-- 黑名单列表发生改变
	CAMP_ROLE_INFO = "camp_role_info",									-- 角色国家信息变更
	FPS_SAMPLE_RESULT = "fps_sample_result",							-- 帧频采样结果
	DAILY_ROOL_VIEW = "DAILY_ROOL_VIEW",								-- 日常任务轮盘
	TASK_INFO_CHANGE = "TASK_INFO_CHANGE",								-- 任务进度改变
	TASK_GUILD_INFO_CHANGE = "TASK_GUILD_INFO_CHANGE",					-- 帮派任务进度改变
	BEFORE_OUT_SCENE = "BEFORE_OUT_SCENE",                              -- 竞技场退出
	FIELD_FIGHT = "FIELD_FIGHT",                                        -- 竞技场战斗开始
	ONEVONE_STATUS_CHANGE = "ONEVONE_STATUS_CHANGE",                    -- 跨服1v1准备显示
	ONEVONE_PREPARE_ANI = "ONEVONE_PREPARE_ANI",                    	-- 跨服1v1动画Ani
	GODDESS_HURT_FLOAT_TEXT = "GODDESS_HURT_FLOAT_TEXT",   				-- 仙女伤害飘字
	CHANGE_HEAD_ICON = "CHANGE_HEAD_ICON",   							-- 更换头像
	CHAT_HANDLE = "CHAT_HANDLE", 										-- 聊天处理
	ENABLE_QING_GONG_CHANGE = "enable_qing_gong_change", 				-- 轻功
	ENABLE_ROLE_SHOW_CHANGE = "role_show_totation", 					-- 角色模型显示转向
	GUILD_FB_LONGHUI_HURT = "guild_fb_longhui_hurt", 					-- 仙盟守护龙徽被攻击
	GUILD_FB_SHOUWEI_HURT = "guild_fb_showwei_hurt", 					-- 仙盟守护守护被攻击
	TIANSHEN_BUFF_CHANGE = "tianshen_buff_change", 						-- 天神BUFF改变
	GET_BAG_YUANBAO = "GET_BAG_YUANBAO",								-- 背包获取元宝特效
	SetTeamFiltrateLevel = "SetTeamFiltrateLevel", 						-- 设置组队平台筛选等级
	MatchChangeEvent = "MatchChangeEvent", 								-- 匹配信息改变
	GUILD_ANSWER_TRUE = "guild_answer_true",							-- 答题正确
	Shop_Buy = "Shop_Buy",												-- 商城购买
	Shop_Buy_Info = "Shop_Buy_Info",									-- 商城购买数据
	Guild_Change = "Guild_Change",										-- 仙盟属性变化
	WenGuaAnim = "WenGuaAnim",											-- 问卦点击动画
	NearRoleListRet = "NearRoleListRet",								-- 附近玩家列表接收
	ZhanDui_Info_Change = "ZhanDui_Info_Change", 						-- 战队信息改变
	ZhanDui_List_Receive = "ZhanDui_List_Receive", 						-- 战队列表接收
	ZhanDui_Applied_List_Receive = "ZhanDui_Applied_List_Receive", 		-- 战队已申请列表接收
	ZhanDui_Add_Log = "ZhanDui_Add_Log", 								-- 战队日志新消息
	ZhanDui_Add_BeInvite = "ZhanDui_Add_BeInvite", 						-- 战队被邀请新消息
	ZhanDui_New_Apply = "ZhanDui_New_Apply", 							-- 战队新申请消息
	ZhanDui_All_Apply = "ZhanDui_All_Apply", 							-- 战队所有申请消息接收
	KF3V3MatchInfoChange = "KF3V3MatchInfoChange", 						-- 跨服3V3匹配信息改变
	KF3V3SceneInfoChange = "KF3V3SceneInfoChange", 						-- 跨服3V3场景信息改变
	KF1V1SceneInfoMatch = "KF1V1SceneInfoMatch", 						-- 跨服1V1场景匹配
	KF3V3PrepareSceneInfoChange = "KF3V3PrepareSceneInfoChange", 		-- 跨服3V3准备场景信息改变
	KF3V3ZhanDuiRoleInfoChnage = "KF3V3ZhanDuiRoleInfoChnage",			-- 跨服3V3战队中个人信息
	TeamMemberSceneInfoChange = "TeamMemberSceneInfoChange", 			-- 组队队员所在场景改变
	Gold_Change_Event = "Gold_Change_Event", 							-- 金钱发生变化之前
	Gold_Change_Event_End = "Gold_Change_Event_End", 					-- 金钱发生变化之后
	QiFu_Gold_Change_Event = "QiFu_Gold_Change_Event",                  -- 祈福界面元宝变化
	Money_Change_Event = "Money_Change_Event", 							-- 金钱发生变化
	TAOBAO_EFFECT = "taobao_effect",									-- 寻宝淘宝特效
	GUILD_ASSIST = "guild_assist",										-- 队伍邀请前往
	FB_GUWU_CHANGE = "FB_GUWU_CHANGE",									-- 副本鼓舞改变
	VIP_INFO_CHANGE = "VIP_INFO_CHANGE",								-- VIP信息改变
	BeforeTaskCommit= "BeforeTaskCommit",
	PICK_ITEM_EVENT = "PICK_ITEM_EVENT",								-- 物品拾取回调
	TitleInfoChange = "TitleInfoChange",								-- 称号信息改变
	MatchSuccess = "MatchSuccess",										-- 匹配成功
	StrangerStarLevel = "StrangerStarLevel",							-- 强化，升星等级改变
    ShengPingAvtiveLevel = "ShengPingAvtiveLevel",						-- 升品改变
    RoleStrangerStarLevel = "RoleStrangerStarLevel",                    -- 角色面板的强化升星等
	TeamHeBingInfo = "TeamHeBingInfo", 									-- 队伍合并消息
	SHOW_BOSS_CHANGE = "show_boss_change", 								-- 切换boss列表
	CHAT_PB_CHANGE = "chat_pb_change",									-- 聊天屏蔽改变
	Ming_Wen_Change = "ming_wen_change",
	MAP_VIEW_SWITCH = "map_view_switch", 								-- 世界/本地地图切換
	LONG_HUN_BAG_CHANGE = "long_hun_bag_change",						-- 龙魂改变
	LONG_HUN_EQUIP_CHANGE = "long_hun_equip_change",					-- 龙魂装备改变
	TASK_COMPLETED_ID_LIST_INIT = "task_completed_id_list_init", 		-- 已完成任务列表初始化
    SendAllObjMoveInfoEvent = "SendAllObjMoveInfoEvent",				-- 请求所以物体移动信息协议 事件监听
    Ba_Gua_Change = "ba_gua_Change",									-- 八卦币改变
	Beast_EXP_Change = "Beast_EXP_Change",								-- 幻兽经验
    Cross_GS_Change = "cross_gs_change",								-- 跨服进度改变
	MAIN_ROLE_3V3_SIDE_CHANGE = "main_role_3v3_side_change", 			-- 主角3V3阵型更变
	CANG_JIN_SCORE_CHANGE = "cangjin_score_change",						-- 藏金商铺积分改变

    BossInfoChange = "BossInfoChange",									-- boss信息改变
    GS_COUNT_WORLD_LEVEL_CHANGE = "gs_count_world_level_change",		-- 跨服进度跨服世界等级
    COUNTRY_SERVER_CHANGE = "country_server_change", 					-- 国家信息改变
	ROLE_WUHUNZHENSHEN_CHANGE = "role_wuhunzhenshen_change", 			-- 武魂真身发生改变
	ROLE_WUHUNZHENSHEN_HIT_NUMBER = "role_wuhunzhenshen_hit_number", 	-- 武魂真身伤害数字
	Sworn_State_Change = "sworn_state_change",                          -- 结义状态变化
	ROLE_SKILL_POWER_CHANGE = "Role_Skill_Power_Change",				-- 角色技能怒气变化事件
	Task_Status_Change = "task_status_change",                          -- 任务状态变化
	Common_Reward_Close3  = "common_reward_close3",                     -- 通用奖励弹窗关闭事件
	
}

FuBenEvent =
{
	WuJinJiTanFirstTimeEvent = "WuJinJiTanFirstTimeEvent",				-- 是否首次
}

Field1v1Type = {
	FIELD_STATUS_CHANGE = "field_status_change",						-- 1v1场景信息改变
}

KFONEVONE1v1Type = {
	KF_STATUS_CHANGE = "kfonevsone_status_change",						-- 跨服1v1场景信息改变
	KF_INFO_CHANGE = "kf_info_change",									-- 跨服1v1信息下发
	START_PVP = "start_1v1",											-- 开始1v1
}

OpenFunEventType = {
	OPEN_TRIGGER = "open_trigger",							--功能开启有变化触发  参数：(是否全部的功能, 单个功能名, 单个功能是否开启))
}

FinishedOpenFun = "finished_open_fun" 						--完成功能开启

TeamInfoQuery = {
	TEAM_INFO_BACK = "team_info_back",						--查询队伍详细信息
}

SkillEventType = {
	FLUSH_TALENT_INFO = 'flush_talent_info',                 -- 刷新天赋信息
	FLUSH_SKILL_LIST = 'flush_skill_list'                 -- 刷新技能信息

}

ShenShouEventType = {
	BAG_FLUSH = "bag_flsuh",								--刷新背包信息
	SHEN_SHOU_INFO_CHANGED = "shen_shou_info_changed",		--刷新界面
	CLOSE_TIPS = "close_tips",								--关闭两个弹窗
	SHEN_SHOU_EXTRA_COUNT = "shen_shou_extra_count",		--额外助战栏
	CONSTRAST_TIP = "constast_tip",							--神兽对比tip
}

TeamWorldTalk = {
	MAIN_WORLD_TALK = "main_world_talk",					--主界面组队世界喊话
	NEW_TEAM_WORLD_TALK = "new_team_world_talk",			--组队界面世界喊话
	COMPLETE_CALL_BACK = "complete_call_back",				--结束回调
}

ActEventType = {
	FLUSH_ADISCOUNTGIFT = "flush_adiscountgift",             -- 折扣礼包刷新
	OpenServerRedPoint = "Remind_OpenServer",				-- 开服活动红点
}

MarryDianChunType =
{
	SELF = "marry_dianchun_self", 					--自己的点唇
	COMPANION = "marry_dianchun_lover", 			--伴侣的点唇
}

KF_BOSS_TYPE = {
	SHENGYU_CLICK = "kf_shengyu_boss_select", 		--圣域task列表boss点击
}

HotSpring = {
	SHUANGXIU_STATE = "Shuangxiu_state"				--双修状态改变
}

FlyShoesState = {
	IS_FLY = "fly_shoes_isfly", 			--小飞鞋使用，播放从天空中落下的特效
}

YanhuaEventType = {
	AGAIN = "again",						-- 再次购买
}

BOSS_SERVER_INFO = {
	ALL_INFO = "all_info"					-- boss所有信息
}

OPENSERVER_ACTIVITY_COMPETITION = {
	UPDATE_BTN_LIST = "OPENSERVER_ACTIVITY_COMPETITION.UpdateBtnList",
	REFRESH_BTN_LIST = "OPENSERVER_ACTIVITY_COMPETITION.RefershBtnList",
	REFRESH_VIEW = "OPENSERVER_ACTIVITY_COMPETITION.RefreshView",
	MONEY_EFFECT = "money_effect",
}

GLOBELEVENT_OPENSERVER_ACTIVITY = {
	UPDATE_BTN_LIST = "GLOBELEVENT_OPENSERVER_ACTIVITY.UpdateBtnList",
}

MUSTBUY_EVENT = {
	BUTTON_CHANGE = "must_buy_btn_change",	-- 超值必买button状态改变
}

--活动按钮状态
ACTIVITY_BTN_EVENT = {
	ZEROBUY_BTN_CHANGE = "zerobuy_btn_change",	-- 零元购状态改变
}

TeamEventType = {
	TEAM_TYPE_CHANGE = "team_type_change",						-- 组队类型改变
}

ZhanDuiWorldTalk = {
	UPDATE_WORLD_TALK = "UPDATE_WORLD_TALK",				--战队世界喊话
	COMPLETE_WORLD_TALK = "COMPLETE_WORLD_TALK",			--结束回调
}

NetWorkStateEvent = {
	NET_WORK_STATE_CHANGE = "net_work_state_change", 			-- 网络变化
}

HotUpdateEvent = {
	HOT_UPDATE_FINISH = "hot_update_finish",			-- 在线热更新完成
}

RankEventType = {
	PersonRankInfoChange = "PersonRankInfoChange", 				--个人排行榜数据
	GuildRankInfoChange = "GuildRankInfoChange", 				--帮派排行榜数据
}

OPERATION_ACTIVITY = {
	ACT_STATE_CHANGE = "act_state_change",				--活动状态改变
	CHUAN_WEN = "chuan_wen",							--活动传闻
}

CHONGBANG_TIP_EVENT = {
	ACT_CHECK = "ACT_CHECK",							-- 冲榜通用弹窗的检查事件
}

ShenJiEventType = {
	ShenJiNotice_TaskInfoChange = "ShenJiNotice_TaskInfoChange", 			--神机预告任务信息变化
	ShenJiNotice_SaleInfoChange = "ShenJiNotice_SaleInfoChange", 			--神机预告特卖信息变化
}

AuditEvent = {
	ROLE_CHANGE = "role_change",										-- 人物数据改变
	RECHARGE_CHANGE = "recharge_change",								-- 充值改变(1443协议触发)
	IS_BIND_PHONE = "is_bind_phone",									-- 是否绑定手机
	IS_BIND_PHONE_SUCCESS = "is_bind_phone_success",					-- 是否绑定手机成功
	PHP_BIND_PHONE = "php_bind_phone",									-- PHP后台绑定手机
}

Guill_XMZ_FIGHT_STATE = {
	STATE_CHANGE = "STATE_CHANGE",					--仙盟战状态发生改变
	LINGSHI_CHANGE = "LINGSHI_CHANGE",				--灵石位置信息变化
}

-- 角色充值信息改变
ROLE_REAL_RECHARGE_NUM_CHANGE = {
	REAL_RECHARGE = "REAL_RECHARGE",				-- 角色真实充值(总)
	TODAY_REAL_RECHARGE_CHANGE = "TODAY_REAL_RECHARGE_CHANGE",	-- 角色当日充值信息改变
}

CULTIVATION_CHANGE = {
	ADD_EXP_CHANGE = "ADD_EXP_CHANGE",					--修为值增加变化
}

-- 全局表值变动监听
GlobalTableValChange = {
	GUAJI_CACHE = "GUAJI_CACHE",					-- GuajiCache表
	ATK_CACHE = "ATK_CACHE",						-- AtkCache表
}