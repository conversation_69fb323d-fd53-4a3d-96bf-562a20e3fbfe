OperationActModel = OperationActModel or BaseClass(OperationActRenderBase)

local ZeroRotation = Quaternion.Euler(0, 0, 0)

function OperationActModel:__delete()
    if nil ~= self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end

    self:CancelWeaponTween()
    self:CancelXianQiTween()
end

function OperationActModel:ReleaseCallBack()
 	-- self.use_update = false
 	-- if Runner.Instance:IsExistRunObj(self) then
	-- 	Runner.Instance:RemoveRunObj(self)
	-- end

	-- self.footprint_eff_t = nil
	self.model_type = nil
	self.offset_type = nil
end

function OperationActModel:Reset()
	-- self.is_foot_view = false
    if self.node_list["display_root"] then
        self.node_list["display_root"]:SetActive(false)
    end
    
    if self.model_display then
        self.model_display:RemoveAllModel()
    end
end

--[[配置数据：
    ----[[模型
        --有物品id的模型
        item_id = 0,
        model_item_id_list = {[物品id] = true, }

        --没有物品id的模型
        bundle_name = "",
        asset_name = "",

        is_ui_scene_model --是否使用UI场景模型展示

        render_type = OARenderType.RoleModel,
        model_rt_type = ModelRTSCaleType.S,
        need_wp_tween
        hide_model_block
        fix_model_node_transform
        not_show_active
        role_rotation --角色旋转角度
        model_click_func (function)
        
        -- 【额外调整模型调整节点的transform】
        -- 一般不建议修改model_adjust_root_local_position，因为容易将显示内容超出RT区域，导致边缘切割
        model_adjust_root_local_position (table、Vector3)
        model_adjust_root_local_rotation (table、Vector3)
        model_adjust_root_local_scale (number)
    ----]]
--]]

function OperationActModel:SetModelType(model_type, offset_type)
	self.model_type = model_type
	self.offset_type = offset_type
end

function OperationActModel:Show()
    if not self.data then
        return
    end

    self.node_list["display_root"]:SetActive(true)
    if nil == self.model_display then
		self.model_display = RoleModel.New()
        if self.data.is_ui_scene_model then
            self.model_display:SetUISceneModel(self.node_list["display_root"].event_trigger_listener, self.model_type or MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
        else
            local model_type = self.model_type or MODEL_CAMERA_TYPE.BASE
            local display_data = {
                parent_node = self.node_list["display_root"],
                camera_type = model_type,
                offset_type = self.offset_type,
                rt_scale_type = self.data.model_rt_type or ModelRTSCaleType.M,
                can_drag = self.data.can_drag ~= false,
                camera_depth = self.data.camera_depth,
            }
            
            self.model_display:SetRenderTexUI3DModel(display_data)
        end
        -- local event_trigger_listener_node = self.data.event_trigger_listener_node or self.node_list["display_root"]
        -- self.model_display:SetUI3DModel(self.node_list["display_root"].transform, event_trigger_listener_node.event_trigger_listener, 1, true, model_type, self.offset_type)
        self.default_display_y = self.node_list["display_root"].rect.anchoredPosition.y
        self.model_block = self.node_list["display_root"]:GetComponent("UIBlock")
    end

    self.fix_model_node_transform = self.data.fix_model_node_transform

    if self.data.model_click_func then
        self.node_list["display_root"].event_trigger_listener:AddPointerClickListener(BindTool.Bind(self.OnClickModel, self))
    end

    if self.model_block then
        self.model_block.enabled = not self.data.hide_model_block
    end

    if self.data.role_rotation then
        self.model_display:SetPositionAndRotation(nil, self.data.role_rotation, nil)
    end

    --【单个】普通模型
    if self.data.bundle_name and self.data.bundle_name ~= "" and
            self.data.asset_name and self.data.asset_name ~= "" then
        self.model_display:SetCameraOffsetType(self.offset_type)
        self:SetModelRootTransformByCfg()
        self:ShowNormalModel()
		return
	end

    --【组合】仅限人物角色的形象组合
    if not IsEmptyTable(self.data.model_item_id_list) then
        self:SetModelRootTransformByCfg()
        self:ShowRoleCombinationModel()
        return
    end

    --【单个】带物品id的模型
    if not self.data.item_id or self.data.item_id == 0 then
		return
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if item_cfg == nil then
		print_error("OperationActModel", "ShowModel() item_cfg is a nil value: item_id = ", self.data.item_id)
		return
	end

    self:SetModelRootTransformByCfg()
    self.model_display:SetCameraOffsetType(self.offset_type)
    self:ShowHasItemConfigModel(item_cfg)
end


function OperationActModel:SetModelRootTransformByCfg()
    if not self.data then
        return
    end

    if type(self.data.model_adjust_root_local_position) == "table" then
		self:SetModelAdjustmentRootLocalPosition(self.data.model_adjust_root_local_position)
	end

	if type(self.data.model_adjust_root_local_rotation) == "table" then
		self:SetModelAdjustmentRootLocalRotation(self.data.model_adjust_root_local_rotation)
	end

	if type(self.data.model_adjust_root_local_scale) == "number" then
		self:SetModelAdjustmentRootLocalScale(self.data.model_adjust_root_local_scale)
	end
end

-- 修改模型 调整节点 local Position
function OperationActModel:SetModelAdjustmentRootLocalPosition(local_position)
    if self.model_display then
        self.model_display:SetRTAdjustmentRootLocalPosition(local_position.x, local_position.y, local_position.z)
    end
end

-- 修改模型 调整节点 local Rotation
function OperationActModel:SetModelAdjustmentRootLocalRotation(model_adjust_root_local_rotation)
    if self.model_display then
        self.model_display:SetRTAdjustmentRootLocalRotation(model_adjust_root_local_rotation.x, model_adjust_root_local_rotation.y, model_adjust_root_local_rotation.z)
    end
end

-- 修改模型 调整节点 local Scale
function OperationActModel:SetModelAdjustmentRootLocalScale(val)
    if self.model_display then
        self.model_display:SetRTAdjustmentRootLocalScale(val)
    end
end

function OperationActModel:FlushModel(path_fun, res_id, animation_type)
	if path_fun == nil or res_id == nil then
		return
	end

	if self.model_display then
		local bundle, asset = path_fun(res_id)
		self.model_display:SetMainAsset(bundle, asset)
	end

	if animation_type and animation_type ~= "" and not self.data.not_show_active then
		if animation_type == "soul" then 		--灵童
			self.model_display:PlaySoulAction()
        elseif animation_type == "lingjian" then
            self.model_display:PlayLingJianAction()
        else
            self.model_display:PlayMountAction()
		end
	end
end

--显示当前玩家模型
--res_id默认当前角色模型，weapon_id默认当前武器，is_need_weapon是否展示武器，is_need_do_anim是否需要UIIdle动画.
function OperationActModel:ShowCurRoleModel(res_id, weapon_id, is_need_weapon, is_need_do_anim)
    local role_res_id, weapon_res_id = AppearanceWGData.Instance:GetRoleResId()
    if res_id then
        role_res_id = res_id
    end

    if weapon_id then
        weapon_res_id = weapon_id
    end

	if role_res_id then
        local extra_role_model_data = {
            no_need_do_anim = not is_need_do_anim,
            weapon_res_id = is_need_weapon and weapon_res_id,
        }
        self.model_display:SetRoleResid(role_res_id, nil, extra_role_model_data)
	end
end

function OperationActModel:ShowWeapon(weapon_res_index)
    local weapon_res_id, weapon_res_id_2 = AppearanceWGData.GetFashionWeaponIdByResViewId(weapon_res_index)
    local bundle, asset = ResPath.GetWeaponModelRes(weapon_res_id)
    self.model_display:SetMainAsset(bundle, asset)
end

function OperationActModel:DisplayRemoveAllModel()
	if self.model_display then
		self.model_display:RemoveAllModel()
	end
end

function OperationActModel:PlayWeaponTween()
	if not self.tween_weapon then
		self.tween_weapon = self.node_list["display_root"].rect:DOAnchorPosY(self.default_display_y + 50, 1)
		self.tween_weapon:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	else
		self.tween_weapon:Restart()
	end
end

function OperationActModel:CancelWeaponTween()
	if self.tween_weapon then
        self.tween_weapon:Kill()
        self.tween_weapon = nil
	end

    local tween_root = self.node_list["display_root"].rect
    if tween_root then
        RectTransform.SetAnchoredPositionXY(tween_root, 0, self.default_display_y)
    end
end

function OperationActModel:PlayXianQiTween(big_type)
	if not self.tween_weapon then
		local tween_root = self.node_list["display_root"].rect
        if big_type == 1 then
    		self.tween_weapon = tween_root:DOLocalRotate(Vector3(0, 360, 0), 4, DG.Tweening.RotateMode.FastBeyond360)
            self.tween_weapon:SetEase(DG.Tweening.Ease.Linear)
    		self.tween_weapon:SetLoops(-1)
        else
            self.tween_weapon = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 20, 1)
            self.tween_weapon:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
        end
	else
		self.tween_weapon:Restart()
	end
end

function OperationActModel:CancelXianQiTween()
	if self.tween_weapon then
        self.tween_weapon:Kill()
        self.tween_weapon = nil
	end

    local tween_root = self.node_list["display_root"].rect
    if tween_root then
        RectTransform.SetAnchoredPositionXY(tween_root, 0, self.default_display_y)
        tween_root.localRotation = ZeroRotation
    end
end

local ROLE_MODEL_TYPE = {
    [DisplayItemTip.Display_type.MOUNT_LINGCHONG] = true,
    [DisplayItemTip.Display_type.FASHION] = true,
    [DisplayItemTip.Display_type.FABAO] = true,
    [DisplayItemTip.Display_type.WING] = true,
    [DisplayItemTip.Display_type.LIANSHI] = true,
    [DisplayItemTip.Display_type.YAOSHI] = true,
    [DisplayItemTip.Display_type.WEIBA] = true,
    [DisplayItemTip.Display_type.SHOUHUAN] = true,
    [DisplayItemTip.Display_type.JIANZHEN] = true,
    [DisplayItemTip.Display_type.SHENBING] = true,
    [DisplayItemTip.Display_type.WUQI] = true,
    [DisplayItemTip.Display_type.FOOT] = true,
    [DisplayItemTip.Display_type.KUN] = true,
    [DisplayItemTip.Display_type.HALO] = true,
}

-- 人物形象组合
function OperationActModel:ShowRoleCombinationModel()
    if not self.data or IsEmptyTable(self.data.model_item_id_list) then
        return
    end

    self.model_display:SetCameraOffsetType(nil)
    self:DisplayRemoveAllModel()
    self:CancelWeaponTween()
    self:CancelXianQiTween()
    -- self:ClearFootEff()
    self.model_display:RemoveFootTrail()

    local show_list = {}
    local list = self.data.model_item_id_list
    local item_cfg, display_type
    for k,v in pairs(list) do
        item_cfg = ItemWGData.Instance:GetItemConfig(k)
        if item_cfg then
            display_type = item_cfg.is_display_role
            if display_type ~= nil and display_type ~= 0 and display_type ~= "" and ROLE_MODEL_TYPE[display_type] then
                show_list[display_type] = item_cfg
            end
        end
    end

    local res_id, part_type, attr_cfg
    local body_res_id = AppearanceWGData.Instance:GetRoleResId()
    local mount_res_id = 0
    local mount_action = ""
    local have_foot_print = false
    local have_weapon = false

    for k, v in pairs(show_list) do
        if k == DisplayItemTip.Display_type.FASHION then
            res_id, part_type = NewAppearanceWGData.Instance:GetFashionResByItemId(v.id)
            if part_type == SHIZHUANG_TYPE.BODY then                            -- 时装
                local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByItemId(v.id)
                if fashion_cfg then
                    local prof = GameVoManager.Instance:GetMainRoleVo().prof
                    body_res_id = ResPath.GetFashionModelId(prof, fashion_cfg.resouce)
                end
            end
        elseif k == DisplayItemTip.Display_type.FOOT then            -- 足迹
            have_foot_print = true
        elseif k == DisplayItemTip.Display_type.MOUNT_LINGCHONG then	-- 坐骑
            res_id = NewAppearanceWGData.Instance:GetQiChongResIdAndActAttrCfg(v.id)
            local param = {}
            if v.param2 then
                param = string.split(v.param2, "|")
            end

            mount_res_id = res_id or param[1]
            local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(mount_res_id)
            mount_action = MOUNT_RIDING_TYPE[1]
            if not IsEmptyTable(action_cfg) then
                mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
            end
        elseif k == DisplayItemTip.Display_type.KUN then	            -- 化鲲
            local kun_base_cfg = NewAppearanceWGData.Instance:GetKunActCfgByItemId(v.id)
            if kun_base_cfg then
                mount_res_id = kun_base_cfg.active_id
                local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(mount_res_id)
                mount_action = MOUNT_RIDING_TYPE[1]
                if not IsEmptyTable(action_cfg) then
                    mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
                end
            end
        elseif k == DisplayItemTip.Display_type.SHENBING                        -- 神兵
            or k == DisplayItemTip.Display_type.WUQI then                       -- 武器
            have_weapon = true
        end
    end

    local extra_role_model_data = {
        no_need_do_anim = true,
    }
    if tonumber(mount_res_id) > 0 then
        self.model_display:SetRoleResid(body_res_id, nil, extra_role_model_data)
        if not have_weapon then
            self.model_display:SetWeaponModelFakeRemove()
        end

        self.model_display:SetMountResid(mount_res_id)
        self.model_display:PlayStartAction(mount_action)
    else
        self.model_display:SetRoleResid(body_res_id, nil, extra_role_model_data)
        if not have_weapon then
            self.model_display:SetWeaponModelFakeRemove()
        end

        if have_foot_print then
            self.model_display:PlayRoleAction(SceneObjAnimator.Move)
        else
            self.model_display:PlayRoleShowAction()
        end
    end

    for k, v in pairs(show_list) do
        if k == DisplayItemTip.Display_type.FASHION then
            res_id, part_type = NewAppearanceWGData.Instance:GetFashionResByItemId(v.id)
            res_id = res_id or v.param2
        elseif k == DisplayItemTip.Display_type.FABAO                           -- 法宝
            or k == DisplayItemTip.Display_type.JIANZHEN                        -- 剑阵
            or k == DisplayItemTip.Display_type.WING then                       -- 羽翼
            res_id, part_type = NewAppearanceWGData.Instance:GetFashionResByItemId(v.id)
            local param = {}
            if v.param2 then
                param = string.split(v.param2, "|")
            end
            res_id = res_id or param[1]

            if k == DisplayItemTip.Display_type.FABAO then
                self.model_display:SetBaoJuResid(res_id)
            elseif k == DisplayItemTip.Display_type.JIANZHEN then
                self.model_display:SetJianZhenResid(res_id)
            elseif k == DisplayItemTip.Display_type.WING then
                self.model_display:SetWingResid(res_id, true)
            end

        elseif k == DisplayItemTip.Display_type.LIANSHI                         -- 脸饰
            or k == DisplayItemTip.Display_type.YAOSHI                          -- 腰饰
            or k == DisplayItemTip.Display_type.WEIBA		                    -- 尾巴
            or k == DisplayItemTip.Display_type.SHOUHUAN                        -- 手环
            or k == DisplayItemTip.Display_type.FOOT then                       -- 足迹

            res_id, part_type = NewAppearanceWGData.Instance:GetFashionResByItemId(v.id)
            res_id = res_id or v.param2

            if k == DisplayItemTip.Display_type.LIANSHI then
                self.model_display:SetMaskResid(res_id)
            elseif k == DisplayItemTip.Display_type.YAOSHI then
                self.model_display:SetWaistResid(res_id)
            elseif k == DisplayItemTip.Display_type.WEIBA then
                self.model_display:SetTailResid(res_id)
            elseif k == DisplayItemTip.Display_type.SHOUHUAN then
                self.model_display:SetShouHuanResid(res_id)
            elseif k == DisplayItemTip.Display_type.FOOT then
                -- self.is_foot_view = true
                -- self.foot_effect_id = res_id
                -- if not self.use_update then
                --     Runner.Instance:AddRunObj(self, 8)
                --     self.use_update = true
                -- end
                self.model_display:SetFootTrailModel(res_id)
                self.model_display:PlayRoleAction(SceneObjAnimator.Move)
                self.model_display:SetRotation(MODEL_ROTATION_TYPE.FOOT)
            end
        elseif k == DisplayItemTip.Display_type.HALO then            -- 光环
            res_id = NewAppearanceWGData.Instance:GetFashionResByItemId(v.id)
            self.model_display:SetHaloResid(res_id)
        elseif k == DisplayItemTip.Display_type.SHENBING                        -- 神兵
            or k == DisplayItemTip.Display_type.WUQI then                       -- 武器
            res_id, part_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(v.id)
            if res_id and attr_cfg then
                res_id = attr_cfg.resouce
            end

            local weapon_res_id = AppearanceWGData.GetFashionWeaponIdByResViewId(res_id)
            self.model_display:SetWeaponResid(weapon_res_id)
        end
    end
end

function OperationActModel:ShowHasItemConfigModel(item_cfg)
    if not self.data then
        return
    end

    local display_type = item_cfg.is_display_role

    local is_xianqi = HiddenWeaponWGData.Instance:GetOnlyEquipCfgById(item_cfg.id)
    if is_xianqi then
        display_type = DisplayItemTip.Display_type.XIANQI
    end

    if nil == display_type or display_type == 0 or display_type == "" then
        return
    end

    local path, bundle, asset, res_id, image_type, attr_cfg, animation_type, name, is_weapon_anim = nil, nil, nil, nil, nil, nil, nil, nil, nil
    local part_type

	self:DisplayRemoveAllModel()
    self:CancelWeaponTween()
    self:CancelXianQiTween()
    -- self:ClearFootEff()
    self.model_display:RemoveFootTrail()

    local anim_name
	if display_type == DisplayItemTip.Display_type.MOUNT_LINGCHONG then 			--坐骑
		res_id, attr_cfg = NewAppearanceWGData.Instance:GetQiChongResIdAndActAttrCfg(item_cfg.id)
		local item_id
		if item_cfg.param2 then
			item_id = string.split(item_cfg.param2, "|")
		end
		res_id = res_id or item_id[1]
		path = ResPath.GetMountModel
		animation_type = "mount"
    elseif display_type == DisplayItemTip.Display_type.LINGCHONG then 			--灵宠
        res_id, attr_cfg = NewAppearanceWGData.Instance:GetQiChongResIdAndActAttrCfg(item_cfg.id)
        local item_id
        if item_cfg.param2 then
            item_id = string.split(item_cfg.param2, "|")
        end
        res_id = res_id or item_id[1]
        path = ResPath.GetPetModel
        animation_type = "lingjian"
	elseif display_type == DisplayItemTip.Display_type.KUN then 				--鲲
		path = ResPath.GetMountModel
		local kun_base_cfg = NewAppearanceWGData.Instance:GetKunActCfgByItemId(item_cfg.id)
		if kun_base_cfg then
			res_id = kun_base_cfg.active_id
		else
			print_error('配置错误~~~~~~~~~~~~')
		end

	elseif display_type == DisplayItemTip.Display_type.FASHION then             --时装
        res_id, part_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
        if res_id and part_type and attr_cfg then
            path, res_id, animation_type = self:GetFashionModlePathFun(part_type, item_cfg)
            res_id = res_id or item_cfg.param2
            --self.attr_cfg = NewAppearanceWGData.Instance:GetFashionUpLevelAttrCfg(part_type, attr_cfg.index, 1)
        end

        if part_type ~= SHIZHUANG_TYPE.HALO then
            path = nil
            self:ShowCurRoleModel(res_id)
            local _, weapon_res_id, __ = AppearanceWGData.Instance:GetRoleResId()
            self.model_display:SetWeaponResid(weapon_res_id)
        end

	elseif display_type == DisplayItemTip.Display_type.LIANSHI or
		display_type == DisplayItemTip.Display_type.YAOSHI or
		display_type == DisplayItemTip.Display_type.WEIBA or
	    display_type == DisplayItemTip.Display_type.SHOUHUAN or
        display_type == DisplayItemTip.Display_type.FOOT or 
        display_type == DisplayItemTip.Display_type.HALO then

		res_id, part_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		if res_id and part_type and attr_cfg then
			path, res_id, animation_type = self:GetFashionModlePathFun(part_type, item_cfg)
			res_id = res_id or item_cfg.param2
			--self.attr_cfg = NewAppearanceWGData.Instance:GetFashionUpLevelAttrCfg(part_type, attr_cfg.index, 1)
		end

	elseif display_type == DisplayItemTip.Display_type.WING then 						--羽翼
		--1.获取翅膀模型
		res_id, part_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		local item_id
		if item_cfg.param2 then
			item_id = string.split(item_cfg.param2, "|")
		end
		res_id = res_id or item_id[1]
		path = ResPath.GetWingModel
		animation_type = "wing"

	elseif display_type == DisplayItemTip.Display_type.SHENBING or display_type == DisplayItemTip.Display_type.WUQI then 						--神兵
		res_id, part_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		if res_id and attr_cfg then
			-- self:ShowCurRoleModel()
			self:ShowWeapon(attr_cfg.resouce)
		end
        -- self:PlayWeaponTween()
        anim_name = SceneObjAnimator.WeaponIdel
	elseif display_type == DisplayItemTip.Display_type.FABAO then 						--法宝
		res_id, image_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		res_id = res_id or item_cfg.param2
		path = ResPath.GetFaBaoModel

	elseif display_type == DisplayItemTip.Display_type.JIANZHEN then 					--剑阵
		res_id, image_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		res_id = res_id or item_cfg.param2
		path = ResPath.GetJianZhenModel
        animation_type = "mount"
	elseif display_type == DisplayItemTip.Display_type.BABY then 					--宝宝
		res_id, attr_cfg = MarryWGData.Instance:GetBabyResByItemId(item_cfg.id)
		if attr_cfg then
			--self.attr_cfg = attr_cfg
			path = ResPath.GetHaiZiModel
			animation_type = "soul"
		end

	elseif display_type == DisplayItemTip.Display_type.XIAOGUI then 					--小鬼
		path = ResPath.GetGuardModel
		--self.attr_cfg = EquipmentWGData.GetXiaoGuiCfg(item_cfg.id)
		local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(item_cfg.id)
		if xiaogui_cfg then
			res_id = xiaogui_cfg.appe_image_id
		end

	elseif display_type == DisplayItemTip.Display_type.TIANSHEN then 				--天神
        local show_id = ItemWGData.Instance:GetComposeProductid(item_cfg.id)
        self.model_display:SetCameraOffsetType(MODEL_OFFSET_TYPE.NORMALIZE)
        local magic_image = TianShenWGData.Instance:GetAppeImage(show_id)
		--local magic_image = TianShenWGData.Instance:GetAppeImage(item_cfg.id)
        if not magic_image then
			magic_image = TianShenHuamoWGData.Instance:GetHuaMoAttributeCfgByItem(show_id)
		end
		if not magic_image then
			return
		end

        self.model_display:SetTianShenModel(magic_image.appe_image_id, magic_image.index, false, nil, nil)--SceneObjAnimator.Rest)
    elseif display_type == DisplayItemTip.Display_type.WUHUNZHENSHEN then
        local wuhun_cfg = WuHunWGData.Instance:GetWuhunResByItemId(item_cfg.id)
        if not wuhun_cfg then
            return
        end

        local b, a = ResPath.GetWuHunModel(wuhun_cfg.appe_image_id)
		self.model_display:SetMainAsset(b, a)
        -- anim_name = SceneObjAnimator.Rest
	elseif display_type == DisplayItemTip.Display_type.TIANSHEN_SHENSHIWAIGUAN then 				--天神神饰外观
		local shenqi_waiguan_cfg = TianShenWGData.Instance:GetWaiGuanCfgByStuff(item_cfg.id)
		if not shenqi_waiguan_cfg then
            return
        end
        
		local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(shenqi_waiguan_cfg.index)
		if not tianshen_cfg then
            return
        end

		local b, a = ResPath.GetBianShenModel(tianshen_cfg.appe_image_id)
		self.model_display:SetMainAsset(b, a)

		bundle, asset = TianShenWGData.Instance:GetTianShenWeapon(tianshen_cfg.index, shenqi_waiguan_cfg.waiguan_id)
		self.model_display:SetWeaponModel(bundle, asset)
    elseif display_type == DisplayItemTip.Display_type.SHENQI then --天神神器
        local shenqi_cfg = TianShenWGData.Instance:GetShenQiCfgByActItemId(item_cfg.id)
        if not shenqi_cfg then
            local compose_cfg = ComposeWGData.Instance:GetComposeCfgByStuffId1(item_cfg.id)
            if compose_cfg then
                shenqi_cfg = TianShenWGData.Instance:GetShenQiCfgByActItemId(compose_cfg.product_id)
            end
            if not shenqi_cfg then
                return
            end
        end

        local b, a = ResPath.GetTianShenShenQiPath(shenqi_cfg.ts_res_idui)
        self.model_display:SetMainAsset(b, a)
        -- self:PlayWeaponTween()

    elseif display_type == DisplayItemTip.Display_type.FIGHTSOUL then                               -- 战魂（四象）
		self.model_display:SetCameraOffsetType(MODEL_OFFSET_TYPE.NORMALIZE)
		local fs_cfg = FightSoulWGData.Instance:GetFightSoulItemCfg(item_cfg.id)
		if fs_cfg then
			res_id = fs_cfg.sixiang_type
			path = ResPath.GetFightSoulModel
			animation_type = "soul_no_do_ideal"
		end
    elseif display_type == DisplayItemTip.Display_type.XIANQI then                                  -- 仙器
        local xq_cfg = HiddenWeaponWGData.Instance:GetOnlyEquipCfgById(item_cfg.id)
        if xq_cfg then
            res_id = xq_cfg.base_model
            path = ResPath.GetShenJiModel

            self:PlayXianQiTween(xq_cfg.big_type)
        end
    elseif display_type == DisplayItemTip.Display_type.NEWFIGHTMOUNT then
        local cfg = NewFightMountWGData.Instance:GetUpgradeCostCfg(item_cfg.id)
        local mount_seq = cfg and cfg.mount_seq or 0
		local mount_type_cfg = NewFightMountWGData.Instance:GetMountTypeCfgBySeq(mount_seq)
		res_id = mount_type_cfg.appe_image_id
		path = ResPath.GetMountModel
		animation_type = "mount"
    elseif display_type == DisplayItemTip.Display_type.MINGQI then	                                --双修.
		local res_id = ArtifactWGData.Instance:GetArtifactCfgByItemId(item_cfg.id).model_id
		local bundle_name, asset_name = ResPath.GetShuangXiuTipUI(res_id)
        self.model_display:SetMainAsset(bundle_name, asset_name)
    elseif display_type == DisplayItemTip.Display_type.LNGYU then	                                --领域.
        local lingyu_type = SupremeFieldsWGData.Instance:GetFootLightTypeByItemId(item_cfg.id)
		self.model_display:SetMainAsset(ResPath.GetSkillFaZhenModel(lingyu_type))
    elseif display_type == DisplayItemTip.Display_type.BEASTS then                                  -- 驭兽
		path = ResPath.GetBeastsModel
		res_id = ControlBeastsWGData.Instance:GetBeastModelResId(item_cfg.id)
        -- anim_name = SceneObjAnimator.Rest
    elseif display_type == DisplayItemTip.Display_type.BEASTS_SKIN then                                  -- 幻兽皮肤
		path = ResPath.GetBeastsModel
		res_id = ControlBeastsWGData.Instance:GetBeastModelSkinResIdByItemId(item_cfg.id)
        anim_name = SceneObjAnimator.Rest
    elseif display_type == DisplayItemTip.Display_type.ANGER_APPE_IMAGE then                                  -- 怒气形象
        local show_nuqi_type = item_cfg.param1
		local show_nuqi_lv = CultivationWGData.Instance:GetAngerLevel(show_nuqi_type)
	
		local cfg = CultivationWGData.Instance:GetNuqiUpgradeCfg(show_nuqi_type, show_nuqi_lv)
		if cfg ~= nil then
			local role_vo = GameVoManager.Instance:GetMainRoleVo()
			local special_status_table = {ignore_halo = true, ignore_wing = true}
			self.model_display:SetModelResInfo(role_vo, special_status_table)
			self.model_display:SetAngerImage(show_nuqi_type, true, cfg.default_body, cfg.default_face, cfg.default_hair)
		end
	end

    if self.data.skip_rest_action then
        animation_type = nil
    end

	self:FlushModel(path, res_id, animation_type)
    if not animation_type then
        self.model_display:PlayRoleAction(anim_name or SceneObjAnimator.UiIdle)
    end

    if self.fix_model_node_transform then
        self.model_display:FixToOrthographic(self.fix_model_node_transform)
    end
end


function OperationActModel:GetFashionModlePathFun(part_type, item_cfg )
	if part_type == nil then
		print_error("DisplayItemTip:GetFashionModlePathFun() can not found the type!! id:::", item_cfg and item_cfg.id)
		return nil
	end

	local path = nil
	local res_id = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
	local animation_type = nil
	-- self.is_foot_view = false
	if part_type == SHIZHUANG_TYPE.BODY then							    --时装
		path = ResPath.GetRoleModel
		local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByItemId(item_cfg.id)
		if fashion_cfg then
			local prof = GameVoManager.Instance:GetMainRoleVo().prof
			res_id = ResPath.GetFashionModelId(prof, fashion_cfg.resouce)
		end
	elseif part_type == SHIZHUANG_TYPE.FOOT then							--足迹
		-- self.is_foot_view = true
		path = nil
		-- self.foot_effect_id = res_id
		-- if not self.use_update then
		-- 	Runner.Instance:AddRunObj(self, 8)
		-- 	self.use_update = true
		-- end
		self:ShowCurRoleModel()
        -- self.model_display:SetFootTrailModel(res_id)
		-- self.model_display:PlayRoleAction(SceneObjAnimator.Move)
		-- self.model_display:SetRotation(MODEL_ROTATION_TYPE.FOOT)
	elseif part_type == SHIZHUANG_TYPE.HALO then						--光环
		path = ResPath.GetHaloModel
	elseif part_type == SHIZHUANG_TYPE.LINGGONG then						--灵弓
		path = ResPath.GetSoulBoyWeaponModel2
	elseif part_type == SHIZHUANG_TYPE.MASK then 							--脸饰
		self:ShowCurRoleModel()
		self.model_display:SetMaskResid(res_id)
	elseif part_type == SHIZHUANG_TYPE.BELT then 							--腰饰
		self:ShowCurRoleModel()
		self.model_display:SetWaistResid(res_id)
	elseif part_type == SHIZHUANG_TYPE.WEIBA then 							--尾巴
		self:ShowCurRoleModel()
		self.model_display:SetTailResid(res_id)
        self.model_display:SetRotation(MODEL_ROTATION_TYPE.WEIBA)
	elseif part_type == SHIZHUANG_TYPE.SHOUHUAN then 						--手环
		self:ShowCurRoleModel()
		self.model_display:SetShouHuanResid(res_id)
	else

	end
	return path, res_id, animation_type
end

function OperationActModel:ShowNormalModel()
    self:CancelWeaponTween()
    self:CancelXianQiTween()
    if self.data.need_wp_tween then
        self:PlayWeaponTween()
    end
    self.model_display:SetMainAsset(self.data.bundle_name, self.data.asset_name)
end

-- function OperationActModel:ClearFootEff()
-- 	if self.footprint_eff_t ~= nil then
-- 		for k,v in pairs(self.footprint_eff_t) do
-- 			if v.obj ~= nil and not IsNil(v.obj) and v.role_model ~= nil then
-- 				-- ResPoolMgr:Release(v.obj)
-- 				v.role_model:OnRemoveGameObject(v.obj)
-- 				v.obj:SetActive(false)
-- 			end
-- 		end
-- 	end

-- 	self.footprint_eff_t = {}
-- end

-- function OperationActModel:CreateFootPrint()
-- 	if nil == self.foot_effect_id or nil == self.model_display then
-- 		return
-- 	end

-- 	if nil == self.footprint_eff_t then
-- 		self.footprint_eff_t = {}
-- 	end
--     local pos = self.model_display.draw_obj:GetRoot().transform
--     local bundle, asset = ResPath.GetUIFootEffect(self.foot_effect_id)
-- 	EffectManager.Instance:PlayControlEffect(self, bundle, asset, Vector3(pos.position.x , pos.position.y, pos.position.z), nil, pos, nil, function(obj)
-- 		if obj then
-- 			if nil ~= obj then
-- 				if self.model_display then
-- 					obj.transform.localPosition = Vector3.zero
-- 					obj:SetActive(false)
-- 					obj:SetActive(true)
-- 					table.insert(self.footprint_eff_t, {obj = obj, role_model = self.model_display})
-- 					self.model_display:OnAddGameobject(obj)
-- 				else
-- 					ResPoolMgr:Release(obj)
-- 				end
-- 			end
-- 		end
--    	end)

-- 	if #self.footprint_eff_t > 2 then
-- 		local obj = table.remove(self.footprint_eff_t, 1)
-- 		obj.role_model:OnRemoveGameObject(obj.obj)
--         if not IsNil(obj.obj) then
--             obj.obj:SetActive(false)
--         end
-- 	end
-- end


-- function OperationActModel:Update(now_time, elapse_time)
-- 	if not self.is_foot_view then
-- 		return
-- 	end
-- 	 if self.next_create_footprint_time == 0 then
--         self:CreateFootPrint()
--         self.next_create_footprint_time = Status.NowTime + COMMON_CONSTS.FOOTPRINT_CREATE_GAP_TIME
--     end

--     if self.next_create_footprint_time == nil then --初生时也是位置改变，不播
--         self.next_create_footprint_time = 0
--     end
--     if self.next_create_footprint_time > 0 and now_time >= self.next_create_footprint_time then
--         self.next_create_footprint_time = 0
--     end
--     self:UpdateFootprintPos()
-- end


-- function OperationActModel:UpdateFootprintPos()
-- 	if nil == self.footprint_eff_t then	return end
-- 	for k,v in pairs(self.footprint_eff_t) do
-- 		if not IsNil(v.obj) then
-- 			local pos = v.obj.transform.localPosition
-- 			v.obj.transform.localPosition = Vector3(pos.x, pos.y, pos.z - 0.16)
-- 		end
-- 	end
-- end

function OperationActModel:OnClickModel()
    if self.data.model_click_func then
        self.data.model_click_func()
    end
end

function OperationActModel:PlayLastAction()
	if self.model_display then
		self.model_display:PlayLastAction()
	end
end

function OperationActModel:PlayWeaponAction()
    if self.model_display then
		self.model_display:PlayRoleAction(SceneObjAnimator.WeaponIdel)
	end
end

function OperationActModel:PlayRoleAction()
    if self.model_display then
		self.model_display:PlayRoleAction(SceneObjAnimator.WeaponIdel)
	end
end

function OperationActModel:SetModleStageIsActive(value)
    if self.model_display then
		self.model_display:SetModleStageIsActive(value)
	end
end