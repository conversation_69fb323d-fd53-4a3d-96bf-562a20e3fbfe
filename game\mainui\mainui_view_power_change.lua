TipsPowerChangeView = TipsPowerChangeView or BaseClass(SafeBaseView)

function TipsPowerChangeView:__init()
	self.view_layer = UiLayer.Guide
	self:AddViewResource(0, "uis/view/powerchangetips_prefab", "PowerChangeNew")
	self.add_value = 0
	self.new_value = 0
	self.is_playing = false
	self.is_tween = false
end

function TipsPowerChangeView:CloseCallBack()
	self.add_value = 0
	self.new_value = 0
	self.is_playing = false
	self:RemoveCutDown()
	self.is_tween = false
end

function TipsPowerChangeView:ReleaseCallBack()
	self:RemoveCutDown()

	if self.anim_tween then
		self.anim_tween:Kill()
		self.anim_tween = nil
	end
end

function TipsPowerChangeView:ShowIndexCallBack()
	self:RemoveCutDown()
	self:FlushView()
end

function TipsPowerChangeView:ShowView(new_value, old_value)
	if new_value > old_value and new_value > self.new_value then
		self.last_add_value = self.add_value
		self.add_value = self.add_value + (new_value - old_value)
		self.new_value = new_value
		if not self:IsOpen() then
			self:Open()
		else
			self:FlushView()
		end
	end
end

function TipsPowerChangeView:DoShow()
	if not self:IsLoaded() then
		return
	end

	self.is_playing = true
	local last_add_value = self.last_add_value
	local add_value = self.add_value
	self.last_add_value = add_value
	self.node_list["TxtGreen"].text:DoNumberTo(last_add_value, add_value, 0.5, function ()
		self.is_playing = false
		if self.last_add_value ~= self.add_value and self.add_value > self.last_add_value then
			self:FlushView()
		end
	end)

	if not self.is_tween then
		self:DoJTTween()
	end

	self.is_tween = true
end

function TipsPowerChangeView:DoJTTween()
	UITween.FakeHideShow(self.node_list.jt)
	self.node_list.jt.gameObject.transform.anchoredPosition = u3dpool.vec3(145, -55, 0)
	if self.anim_tween then
		self.anim_tween:Kill()
		self.anim_tween = nil
	end

	local tween1 = self.node_list.jt.canvas_group:DoAlpha(0, 1, 0.2)
	local tween2 = self.node_list.jt.rect:DOAnchorPos(Vector2(145, -25), 0.3)
	self.anim_tween = DG.Tweening.DOTween.Sequence()
	self.anim_tween:Append(tween1)
	self.anim_tween:Join(tween2)
	self.anim_tween:SetEase(DG.Tweening.Ease.Linear)
	ReDelayCall(self, function()
		self.node_list.jt.canvas_group:DoAlpha(1, 0, 0.6)
		self.node_list.jt.rect:DOAnchorPos(Vector2(145, 10), 0.2)
		ReDelayCall(self, function()
			self.is_tween = false
		end, 0.3, "power_tween1")
	end, 1, "power_tween")

	
end

function TipsPowerChangeView:FlushView()
	if not self.is_playing then
		self:DoShow()
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.PowerUp))
	end
	
	self:RemoveCutDown()
	self.close_time_event = GlobalTimerQuest:AddDelayTimer(function ()
		self:Close()
	end, 2)
end

function TipsPowerChangeView:RemoveCutDown()
	if self.close_time_event then
		GlobalTimerQuest:CancelQuest(self.close_time_event)
		self.close_time_event = nil
	end
end
