RoleCharmNoticeView = RoleCharmNoticeView or BaseClass(SafeBaseView)

function RoleCharmNoticeView:__init()
	self.view_layer = UiLayer.Normal
	self.rank_type = nil
	self:SetMaskBg(false, true)
	self:AddViewResource(0, "uis/view/rolecharmnotice_ui_prefab", "layout_rolecharm_notice")
end

function RoleCharmNoticeView:LoadCallBack()
	self.boys_charm_list = AsyncListView.New(RoleCharmItemRender, self.node_list["boys_charm_list"])
	self.girls_charm_list = AsyncListView.New(RoleCharmItemRender, self.node_list["girls_charm_list"])
	self.champion_reward_list = AsyncListView.New(ItemCell, self.node_list["champion_reward_list"])
	self.charm_head_cell = BaseHeadCell.New(self.node_list["champion_head_img"])
	XUI.AddClickEventListener(self.node_list["champion_head_img"], BindTool.Bind1(self.CharmRoleInfo, self))
	self.charm_head_cell:SetBgActive(false)
	self.champion_reward_list:SetStartZeroIndex(true)
	XUI.AddClickEventListener(self.node_list["get_charm_btn"], BindTool.Bind(self.OnBtnOpenFlowerView, self))
	XUI.AddClickEventListener(self.node_list["reward_preview_btn"], BindTool.Bind(self.OnBtnOpenRewardView, self))
	XUI.AddClickEventListener(self.node_list["btn_tips"], BindTool.Bind(self.OnClickBtnCharmTip, self))
	XUI.AddClickEventListener(self.node_list["everyday_gift"], BindTool.Bind(self.OnBtnfreeBtn, self))

	self.node_list["mianui_show_rank"].toggle.isOn = RoleCharmNoticeWGData.Instance:GetMainUiIsShowCharmRank()
	self.node_list["mianui_show_rank"].toggle:AddValueChangedListener(BindTool.Bind(self.CharmOnClickMainUiShowRankToggle, self))

	for i = 1, 2 do
		XUI.AddClickEventListener(self.node_list["suit_toogle_" .. i], BindTool.Bind(self.SelectBtnType, self, i))
	end

	self.tab_index = -1
	local role_sex = RoleWGData.Instance:GetRoleSex()
	local tab_index = role_sex == GameEnum.MALE and 1 or 2
	self:SelectBtnType(tab_index)

	self.node_list["txt_charm_rank_tips_1"].text.text = Language.RoleCharmRank.CharmRankTips1
	self.node_list["txt_charm_rank_tips_2"].text.text = Language.RoleCharmRank.CharmRankTips2
end

function RoleCharmNoticeView:ReleaseCallBack()
	self.tab_index = nil
	self.rank_type = nil
	if self.boys_charm_list then
	self.boys_charm_list:DeleteMe()
	self.boys_charm_list = nil
	end
	
	if self.girls_charm_list then
		self.girls_charm_list:DeleteMe()
		self.girls_charm_list = nil
	end

	if self.champion_reward_list then
		self.champion_reward_list:DeleteMe()
		self.champion_reward_list = nil
	end

	if self.charm_head_cell then
		self.charm_head_cell:DeleteMe()
		self.charm_head_cell = nil
	end

	if CountDownManager.Instance:HasCountDown("charm_rank_count_down") then
		CountDownManager.Instance:RemoveCountDown("charm_rank_count_down")
	end
end

function RoleCharmNoticeView:SelectBtnType(index)
	if self.tab_index == index then
		return
	end

	self.node_list.select_img1:SetActive(index == 1)
	self.node_list.select_img2:SetActive(index == 2)
	self.node_list.boys_charm_list:SetActive(index == 1)
	self.node_list.girls_charm_list:SetActive(index == 2)
	self.tab_index = index
	self:SendRankInfo()
end



function RoleCharmNoticeView:SendRankInfo()
	local rank_type = self.tab_index == 1 and PROFESS_RANK_TYPE.SEND_FLOWER_MALE or PROFESS_RANK_TYPE.SEND_FLOWER_FEMALE
	RoleCharmNoticWGCtrl.Instance:SendActivityCharmRankReq(SEND_FLOWER_OPER_TYPE.SEND_FLOWER_OPER_TYPE_RANK_INFO, rank_type)
end

function RoleCharmNoticeView:CharmRoleInfo()
	self.charm_head_cell:OpenMenu(nil,RoleCharmNoticWGCtrl.Instance:GetCacularPos(self.node_list["champion_head_img"]),nil,MASK_BG_ALPHA_TYPE.Normal)
end

function RoleCharmNoticeView:OnBtnOpenFlowerView()
	ViewManager.Instance:Open(GuideModuleName.Flower, TabIndex.flower_send, "all", { flower_type = 2 })
end

function RoleCharmNoticeView:OnBtnOpenRewardView()
	ViewManager.Instance:Open(GuideModuleName.RoleCharmRewardPreviewView)
end

function RoleCharmNoticeView:OnClickBtnCharmTip()
	local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.RoleCharmRank.tips)
	rule_tip:SetContent(Language.RoleCharmRank.tipsContent, nil, nil, nil, true)
end

function RoleCharmNoticeView:OpenCallBack()

end

function RoleCharmNoticeView:OnFlush()
	if self.tab_index == nil  then
		return
	end

	local rank_type = self.tab_index == 1 and PROFESS_RANK_TYPE.SEND_FLOWER_MALE or PROFESS_RANK_TYPE.SEND_FLOWER_FEMALE
	local data_list = RoleCharmNoticeWGData.Instance:GetHadDataRankList(rank_type)
	if IsEmptyTable(data_list) then
		return
	end

	local cur_sex
	if self.tab_index == 1 then
		cur_sex = GameEnum.MALE
		self.boys_charm_list:SetDataList(data_list)
	elseif self.tab_index == 2 then
		cur_sex = GameEnum.FEMALE
		self.girls_charm_list:SetDataList(data_list)
	end

	local my_rank_num = RoleCharmNoticeWGData.Instance:GetSelfRankNum()
	local champion_reward_list = RoleCharmNoticeWGData.Instance:GetCharmRankRewardCfg(rank_type)
	self.champion_reward_list:SetDataList(champion_reward_list[1] or {})
	if my_rank_num == 0 then
		self.node_list.my_rank_state.text.text = Language.RoleCharmRank .nocharm
	else
		self.node_list.my_rank_state.text.text = my_rank_num
	end
	
	local first_rank = data_list[1] or {}
	local role_id = data_list[1].user_id
	if role_id and role_id > 0 then
		self.node_list.no_noticeinfo_text:SetActive(false)
		self.node_list.have_noticeinfo_panel:SetActive(true)
		self.node_list.no_champion_head_img:SetActive(false)
		self.node_list.champion_name.text.text = first_rank.user_name
		self.charm_head_cell:SetData({role_id = first_rank.user_id, sex = first_rank.sex, prof = first_rank.prof, fashion_photoframe = first_rank.fashion_photoframe})
	else
		-- 无上榜
		self.node_list.no_noticeinfo_text:SetActive(true)
		self.node_list.have_noticeinfo_panel:SetActive(false)
		self.node_list.no_champion_head_img:SetActive(true)
	end

	local red_point_show = RoleCharmNoticeWGData.Instance:IsShowNeedCharmRedPoint()
	local role_sex = RoleWGData.Instance:GetRoleSex()
	local my_charm = RoleCharmNoticeWGData.Instance:GetSelfRankCharmData().self_value
	self.node_list.my_charm_root:SetActive(role_sex == cur_sex)
    self.node_list.my_charm_num.text.text = my_charm
	self.node_list.champion_insever.text.text = first_rank.rank_value
	self.node_list.btn_red_point:SetActive(red_point_show == 1)
	
	self:StartTimeCountDown()
	self:FlushDailyGift()
end

function RoleCharmNoticeView:FlushDailyGift()
	local reward_flag = RoleCharmNoticeWGData.Instance:GetDailyGiftRewardFlag()
	self.node_list.gift_is_get:SetActive(reward_flag == 1)
	self.node_list.gift_remind:SetActive(reward_flag ~= 1)
end


function RoleCharmNoticeView:OnBtnfreeBtn()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHARMRANK)
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		local is_buy_free = RoleCharmNoticeWGData.Instance:GetDailyGiftRewardFlag()
		if is_buy_free == 1 then
			TipWGCtrl.Instance:ShowSystemMsg(Language.GoldStoneBuy.AllFreeShopBuy)
			return
		end

		ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHARMRANK, SEND_FLOWER_OPER_TYPE.SEND_FLOWER_OPER_TYPE_DAILY_REWARD)
	end
end

function RoleCharmNoticeView:CharmOnClickMainUiShowRankToggle(is_on)
	RoleCharmNoticeWGData.Instance:SetMainUiIsShowCharmRank(is_on and 1 or 0)
end

------------------------------------活动时间倒计时
function RoleCharmNoticeView:StartTimeCountDown()
	local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHARMRANK)
	if activity_data ~= nil then
		local invalid_time = activity_data.end_time
		if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
			local time_str = string.format(Language.RoleCharmRank.ActivityTime, TimeUtil.FormatSecondDHM8(invalid_time - TimeWGCtrl.Instance:GetServerTime()))
			self.node_list["act_time"].text.text = time_str
			CountDownManager.Instance:AddCountDown("charm_rank_count_down", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
		end
	end
end

function RoleCharmNoticeView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.login_act_date = TimeUtil.FormatUnixTime2Date()
		local time_str = string.format(Language.RoleCharmRank.ActivityTime, TimeUtil.FormatSecondDHM8(valid_time))
		self.node_list["act_time"].text.text = time_str
	end
end

function RoleCharmNoticeView:OnComplete()
	self.node_list["act_time"].text.text = ""
	self:Close()
end

-------------------------------------------
-- 排行Render RoleCharmItemRender
-------------------------------------------
RoleCharmItemRender = RoleCharmItemRender or BaseClass(BaseRender)
function RoleCharmItemRender:__init()
end

function RoleCharmItemRender:LoadCallBack()
	self.reward_rank_list = AsyncListView.New(ItemCell, self.node_list["item_list"])
	self.head_cell = BaseHeadCell.New(self.node_list["role_head_sculpture"])
	XUI.AddClickEventListener(self.node_list["role_head_sculpture"], BindTool.Bind1(self.RoleInfoList, self))
	self.head_cell:SetBgActive(false)
	self.reward_rank_list:SetStartZeroIndex(true)
end

function RoleCharmItemRender:__delete()
    if self.reward_rank_list then
		self.reward_rank_list:DeleteMe()
		self.reward_rank_list = nil
	end

	if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end
end

function RoleCharmItemRender:RoleInfoList()
	self.head_cell:OpenMenu(nil,RoleCharmNoticWGCtrl.Instance:GetCacularPos(self.node_list["role_head_sculpture"]), nil, MASK_BG_ALPHA_TYPE.Normal)
end

function RoleCharmItemRender:OnFlush()
	if not self.data then
		return
	end
	
	local type = self.data.sex == GameEnum.MALE and PROFESS_RANK_TYPE.SEND_FLOWER_MALE or PROFESS_RANK_TYPE.SEND_FLOWER_FEMALE
	local reward_list = RoleCharmNoticeWGData.Instance:GetCharmRankRewardCfg(type)

	if not self.data.user_name or self.data.user_name == "" then
		local rank_target = RoleCharmNoticeWGData.Instance:GetCharmRankTarget(self.index)
		self.node_list["charm_role_name"].text.text = string.format(Language.RoleCharmRank.RankCondition, rank_target)
		self.node_list["charm_value"]:SetActive(false)
	else
		self.node_list["charm_role_name"].text.text = self.data.user_name
		self.node_list["charm_value"]:SetActive(true)
		self.node_list["charm_value"].text.text = string.format(Language.RoleCharmRank.RankCharmValue, self.data.rank_value)
	end

	local asset_id = self.index <= 4 and self.index or 4
	local bundle, asset = ResPath.GetActCharmRankImg("a3_cb_mlb_jb" .. asset_id)
	self.node_list["role_rank_bg"].image:LoadSprite(bundle, asset)

	if self.data.user_id and self.data.user_id > 0 then
		self.node_list.role_head_sculpture:SetActive(true)
		self.head_cell:SetData({role_id = self.data.user_id, sex = self.data.sex, prof = self.data.prof, fashion_photoframe = self.data.fashion_photoframe})
		self.node_list.nodata_head_mask:SetActive(false)
	else
		self.node_list.role_head_sculpture:SetActive(false)
		self.node_list.nodata_head_mask:SetActive(true)
	end

	self.node_list.role_rank_place.text.text = string.format(Language.RoleCharmRank.rank_text, self.index)
	self.reward_rank_list:SetDataList(reward_list[self.index])
end
