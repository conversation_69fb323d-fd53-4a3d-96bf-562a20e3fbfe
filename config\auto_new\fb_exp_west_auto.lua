-- F-副本-历练西行.xls
local item_table={
[1]={item_id=26502,num=1,is_bind=1},
[2]={item_id=26503,num=1,is_bind=1},
[3]={item_id=26504,num=1,is_bind=1},
[4]={item_id=26501,num=1,is_bind=1},
[5]={item_id=57853,num=5,is_bind=1},
[6]={item_id=57852,num=5,is_bind=1},
[7]={item_id=26500,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
level={
[1]={level=1,auto_need_cap=15000000,},
[2]={level=2,need_level=400,need_cap=10000000,auto_need_cap=20000000,record_index=1,building_img=2,stage_name="二转大陆",},
[3]={level=3,need_level=500,need_cap=15000000,auto_need_cap=25000000,record_index=2,building_img=3,stage_name="三转大陆",},
[4]={level=4,need_level=600,need_cap=20000000,auto_need_cap=30000000,record_index=3,stage_name="四转大陆",},
[5]={level=5,need_level=700,need_cap=25000000,auto_need_cap=35000000,record_index=4,building_img=2,stage_name="五转大陆",},
[6]={level=6,need_level=800,need_cap=30000000,auto_need_cap=40000000,record_index=5,building_img=3,stage_name="六转大陆",},
[7]={level=7,need_level=900,need_cap=35000000,auto_need_cap=45000000,record_index=6,stage_name="七转大陆",},
[8]={level=8,need_level=1000,need_cap=40000000,auto_need_cap=50000000,record_index=7,building_img=2,stage_name="八转大陆",},
[9]={level=9,need_level=1100,need_cap=45000000,auto_need_cap=55000000,record_index=8,building_img=3,stage_name="九转大陆",},
[10]={level=10,need_level=1200,need_cap=50000000,auto_need_cap=60000000,record_index=9,stage_name="长生大陆",},
[11]={level=11,need_level=1300,need_cap=55000000,auto_need_cap=65000000,record_index=10,building_img=2,stage_name="魂涅大陆",},
[12]={level=12,need_level=1400,need_cap=60000000,auto_need_cap=70000000,record_index=11,building_img=3,stage_name="婴涅大陆",},
[13]={level=13,need_level=1500,need_cap=65000000,auto_need_cap=75000000,record_index=12,stage_name="涅槃大陆",},
[14]={level=14,need_level=1600,need_cap=70000000,record_index=13,building_img=2,stage_name="星极大陆",},
[15]={level=15,need_level=1700,need_cap=75000000,record_index=14,building_img=3,stage_name="昊天大陆",}
},

level_meta_table_map={
},
wave={
{wave_time=200,},
{wave=2,std_exp=200,building_img=2,name="聚魂九天",},
{wave=3,pass_reward_item={[0]=item_table[1]},std_exp=300,name="迷雾之森",},
{wave=4,pass_reward_item={[0]=item_table[2]},std_exp=400,name="冰封之涯",},
{wave=5,pass_reward_item={[0]=item_table[3]},std_exp=5000,name="鬼雾迷林",},
{wave=6,std_exp=600,building_img=3,name="血渊魔域",},
{wave=7,pass_reward_item={[0]=item_table[4]},std_exp=700,name="凌龙之谷",},
{wave=8,std_exp=800,building_img=2,name="炽炎炼狱",},
{level=2,std_exp=1600,},
{level=2,std_exp=1700,},
{level=2,std_exp=1800,},
{level=2,std_exp=1900,},
{level=2,std_exp=2000,},
{level=2,std_exp=2100,},
{level=2,std_exp=2200,},
{level=2,std_exp=2300,},
{level=3,std_exp=3100,},
{level=3,std_exp=3200,},
{level=3,std_exp=3300,},
{level=3,std_exp=3400,},
{level=3,std_exp=35000,},
{level=3,std_exp=3600,},
{level=3,std_exp=3700,},
{level=3,std_exp=3800,},
{level=4,std_exp=4600,},
{level=4,std_exp=4700,},
{level=4,std_exp=4800,},
{level=4,std_exp=4900,},
{level=4,std_exp=50000,},
{level=4,std_exp=5100,},
{level=4,std_exp=5200,},
{level=4,std_exp=5300,},
{level=5,std_exp=6100,},
{level=5,std_exp=6200,},
{level=5,std_exp=6300,},
{level=5,std_exp=6400,},
{level=5,std_exp=65000,},
{level=5,std_exp=6600,},
{level=5,std_exp=6700,},
{level=5,std_exp=6800,},
{level=6,std_exp=7600,},
{level=6,std_exp=7700,},
{level=6,std_exp=7800,},
{level=6,std_exp=7900,},
{level=6,std_exp=8000,},
{level=6,wave=6,std_exp=8100,building_img=3,name="血渊魔域",},
{level=6,std_exp=8200,},
{level=6,std_exp=8300,},
{level=7,std_exp=9100,},
{level=7,std_exp=9200,},
{level=7,std_exp=9300,},
{level=7,std_exp=9400,},
{level=7,wave_time=20,std_exp=95000,},
{level=7,std_exp=9600,},
{level=7,std_exp=9700,},
{level=7,std_exp=9800,},
{level=8,std_exp=10600,},
{level=8,std_exp=10700,},
{level=8,std_exp=10800,},
{level=8,std_exp=10900,},
{level=8,std_exp=11000,},
{level=8,std_exp=11100,},
{level=8,std_exp=11200,},
{level=8,wave=8,pass_reward_item={[0]=item_table[1]},std_exp=11300,building_img=2,name="炽炎炼狱",},
{level=9,wave_time=30,std_exp=12100,},
{level=9,wave_time=30,std_exp=12200,},
{level=9,wave_time=20,std_exp=12300,},
{level=9,wave_time=20,std_exp=12400,},
{level=9,std_exp=125000,},
{level=9,std_exp=12600,},
{level=9,std_exp=12700,},
{level=9,std_exp=12800,},
{level=10,std_exp=13600,},
{level=10,std_exp=13700,},
{level=10,std_exp=13800,},
{level=10,std_exp=13900,},
{level=10,std_exp=14000,},
{level=10,std_exp=14100,},
{level=10,std_exp=14200,},
{level=10,std_exp=14300,},
{level=11,std_exp=15100,},
{level=11,std_exp=15200,},
{level=11,std_exp=15300,},
{level=11,std_exp=15400,},
{level=11,std_exp=155000,},
{level=11,std_exp=15600,},
{level=11,std_exp=15700,},
{level=11,std_exp=15800,},
{level=12,std_exp=16600,},
{level=12,std_exp=16700,},
{level=12,std_exp=16800,},
{level=12,std_exp=16900,},
{level=12,std_exp=17000,},
{level=12,std_exp=17100,},
{level=12,std_exp=17200,},
{level=12,std_exp=17300,},
{level=13,std_exp=18100,},
{level=13,std_exp=18200,},
{level=13,std_exp=18300,},
{level=13,std_exp=18400,},
{level=13,std_exp=185000,},
{level=13,std_exp=18600,},
{level=13,std_exp=18700,},
{level=13,std_exp=18800,},
{level=14,std_exp=19600,},
{level=14,wave_time=20,std_exp=19700,},
{level=14,std_exp=19800,},
{level=14,std_exp=19900,},
{level=14,std_exp=20000,},
{level=14,std_exp=20100,},
{level=14,std_exp=20200,},
{level=14,std_exp=20300,},
{level=15,std_exp=21100,},
{level=15,std_exp=21200,},
{level=15,std_exp=21300,},
{level=15,std_exp=21400,},
{level=15,wave=5,pass_reward_item={[0]=item_table[3]},std_exp=215000,building_img=2,name="鬼雾迷林",},
{level=15,std_exp=21600,},
{level=15,wave=7,pass_reward_item={[0]=item_table[4]},std_exp=21700,name="凌龙之谷",},
{level=15,std_exp=21800,}
},

wave_meta_table_map={
[49]=65,	-- depth:1
[73]=65,	-- depth:1
[41]=65,	-- depth:1
[81]=65,	-- depth:1
[33]=65,	-- depth:1
[89]=65,	-- depth:1
[25]=1,	-- depth:1
[97]=65,	-- depth:1
[17]=25,	-- depth:2
[105]=65,	-- depth:1
[57]=65,	-- depth:1
[9]=25,	-- depth:2
[113]=65,	-- depth:1
[4]=1,	-- depth:1
[47]=119,	-- depth:1
[39]=119,	-- depth:1
[38]=46,	-- depth:1
[78]=46,	-- depth:1
[79]=119,	-- depth:1
[6]=1,	-- depth:1
[7]=4,	-- depth:2
[86]=46,	-- depth:1
[87]=119,	-- depth:1
[70]=46,	-- depth:1
[71]=119,	-- depth:1
[94]=46,	-- depth:1
[95]=119,	-- depth:1
[63]=119,	-- depth:1
[62]=46,	-- depth:1
[54]=46,	-- depth:1
[102]=46,	-- depth:1
[103]=119,	-- depth:1
[111]=119,	-- depth:1
[55]=119,	-- depth:1
[110]=46,	-- depth:1
[118]=46,	-- depth:1
[68]=4,	-- depth:2
[108]=68,	-- depth:3
[80]=64,	-- depth:1
[116]=68,	-- depth:3
[76]=68,	-- depth:3
[109]=117,	-- depth:1
[84]=68,	-- depth:3
[92]=68,	-- depth:3
[96]=64,	-- depth:1
[112]=64,	-- depth:1
[100]=68,	-- depth:3
[101]=117,	-- depth:1
[104]=64,	-- depth:1
[72]=64,	-- depth:1
[88]=64,	-- depth:1
[60]=68,	-- depth:3
[28]=4,	-- depth:2
[2]=7,	-- depth:3
[3]=6,	-- depth:2
[5]=2,	-- depth:4
[8]=3,	-- depth:3
[12]=4,	-- depth:2
[14]=6,	-- depth:2
[15]=7,	-- depth:3
[20]=4,	-- depth:2
[22]=6,	-- depth:2
[23]=7,	-- depth:3
[30]=6,	-- depth:2
[31]=7,	-- depth:3
[36]=68,	-- depth:3
[40]=64,	-- depth:1
[120]=64,	-- depth:1
[48]=64,	-- depth:1
[52]=68,	-- depth:3
[44]=68,	-- depth:3
[56]=64,	-- depth:1
[53]=117,	-- depth:1
[21]=5,	-- depth:5
[67]=3,	-- depth:3
[19]=3,	-- depth:3
[18]=2,	-- depth:4
[16]=8,	-- depth:4
[66]=2,	-- depth:4
[106]=2,	-- depth:4
[107]=67,	-- depth:4
[50]=66,	-- depth:5
[99]=67,	-- depth:4
[13]=5,	-- depth:5
[51]=67,	-- depth:4
[11]=3,	-- depth:3
[10]=2,	-- depth:4
[114]=106,	-- depth:5
[115]=67,	-- depth:4
[61]=53,	-- depth:2
[98]=106,	-- depth:5
[75]=67,	-- depth:4
[69]=53,	-- depth:2
[42]=66,	-- depth:5
[77]=53,	-- depth:2
[37]=53,	-- depth:2
[43]=67,	-- depth:4
[35]=67,	-- depth:4
[34]=66,	-- depth:5
[82]=66,	-- depth:5
[83]=67,	-- depth:4
[32]=8,	-- depth:4
[85]=53,	-- depth:2
[45]=53,	-- depth:2
[29]=5,	-- depth:5
[58]=66,	-- depth:5
[90]=66,	-- depth:5
[91]=67,	-- depth:4
[27]=3,	-- depth:3
[93]=53,	-- depth:2
[26]=2,	-- depth:4
[24]=8,	-- depth:4
[74]=66,	-- depth:5
[59]=67,	-- depth:4
},
monster={
{},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{wave=2,monster_id=6706,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{wave=3,monster_id=6707,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{wave=4,monster_id=6708,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{wave=5,monster_id=6709,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{wave=6,monster_id=6710,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{wave=7,monster_id=6711,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{wave=8,monster_id=6712,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{wave=9,monster_id=6713,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{wave=10,monster_id=6714,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{wave=11,monster_id=6715,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{wave=12,monster_id=6716,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{wave=13,monster_id=6717,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{wave=14,monster_id=6718,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{wave=15,monster_id=6719,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=2,},
{seq=1,},
{seq=2,},
{level=2,},
{level=2,},
{seq=5,},
{seq=6,},
{seq=7,},
{level=2,},
{seq=9,},
{level=2,},
{level=2,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=2,},
{level=2,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=2,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{level=2,},
{level=2,},
{seq=1,},
{seq=2,},
{seq=3,},
{level=2,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=2,},
{level=2,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=2,},
{level=2,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=2,},
{level=2,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=2,},
{level=2,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=2,},
{level=2,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=2,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{level=2,},
{level=2,},
{level=2,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=2,},
{level=2,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=2,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{level=2,},
{level=2,},
{seq=1,},
{seq=2,},
{seq=3,},
{level=2,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=3,},
{seq=1,},
{seq=2,},
{level=3,},
{level=3,},
{seq=5,},
{level=3,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=3,},
{level=3,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=3,},
{level=3,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=3,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{level=3,},
{seq=8,},
{seq=9,},
{level=3,},
{level=3,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=3,},
{level=3,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=3,},
{level=3,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=3,},
{level=3,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=3,},
{level=3,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=3,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{level=3,},
{seq=9,},
{level=3,},
{seq=1,},
{seq=2,},
{level=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=3,},
{level=3,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=3,},
{level=3,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=3,},
{level=3,},
{seq=2,},
{level=3,},
{seq=4,},
{seq=5,},
{level=3,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=3,},
{level=3,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=4,},
{level=4,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{level=4,},
{seq=8,},
{seq=9,},
{level=4,},
{seq=1,},
{level=4,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=4,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{level=4,},
{level=4,},
{level=4,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=4,},
{level=4,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=4,},
{level=4,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=4,},
{seq=1,},
{level=4,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=4,},
{level=4,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=4,},
{level=4,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=4,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{level=4,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=4,},
{level=4,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=4,},
{seq=1,},
{level=4,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=4,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{level=4,},
{level=4,},
{level=4,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=4,},
{level=4,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{level=4,},
{level=5,},
{level=5,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{level=5,},
{level=5,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{level=5,},
{seq=9,},
{level=5,},
{seq=1,},
{seq=2,},
{level=5,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=5,},
{level=5,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=5,},
{level=5,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=5,},
{level=5,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=5,},
{level=5,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=5,},
{seq=1,},
{level=5,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=5,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{level=5,},
{level=5,},
{level=5,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=5,},
{level=5,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=5,},
{level=5,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{level=5,},
{seq=9,},
{level=5,},
{level=5,},
{level=5,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=5,},
{level=5,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=5,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{level=5,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=6,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{level=6,},
{seq=7,},
{level=6,},
{seq=9,},
{level=6,},
{level=6,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=6,},
{level=6,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=6,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{level=6,},
{level=6,},
{seq=1,},
{level=6,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=6,},
{level=6,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=6,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{level=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=6,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{level=6,},
{level=6,},
{level=6,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=6,},
{level=6,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{level=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=6,},
{level=6,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=6,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{level=6,},
{level=6,},
{seq=1,},
{level=6,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=6,},
{level=6,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=6,},
{level=6,},
{level=6,},
{seq=3,},
{level=6,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=7,},
{level=7,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{level=7,},
{level=7,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{level=7,},
{level=7,},
{level=7,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=7,},
{level=7,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=7,},
{level=7,},
{seq=2,},
{seq=3,},
{level=7,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=7,},
{level=7,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{level=7,},
{level=7,},
{level=7,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=7,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{level=7,},
{seq=9,},
{level=7,},
{seq=1,},
{level=7,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=7,},
{seq=1,},
{level=7,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=7,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{level=7,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=7,},
{level=7,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=7,},
{level=7,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=7,},
{level=7,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=7,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{level=7,},
{level=8,},
{level=8,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{level=8,},
{level=8,},
{level=8,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=8,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{level=8,},
{level=8,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{level=8,},
{seq=8,},
{seq=9,},
{level=8,},
{level=8,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{level=8,},
{seq=9,},
{level=8,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{level=8,},
{seq=8,},
{level=8,},
{level=8,},
{level=8,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{seq=9,},
{level=8,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{level=8,},
{seq=9,},
{level=8,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{level=8,},
{seq=6,},
{level=8,},
{seq=8,},
{level=8,},
{level=8,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{level=8,},
{level=8,},
{level=8,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{level=8,},
{level=8,},
{seq=1,},
{seq=2,},
{seq=3,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{seq=8,},
{level=8,},
{level=8,},
{seq=1,},
{seq=2,},
{level=8,},
{seq=4,},
{level=8,},
{seq=6,},
{level=8,},
{seq=8,},
{seq=9,},
{level=8,},
{seq=1,},
{seq=2,},
{seq=3,},
{level=8,},
{seq=5,},
{seq=6,},
{seq=7,},
{level=8,},
{seq=9,},
{level=8,},
{level=8,},
{seq=2,},
{level=8,},
{seq=4,},
{seq=5,},
{seq=6,},
{seq=7,},
{level=8,},
{level=8,}
},

monster_meta_table_map={
[305]=5,	-- depth:1
[910]=10,	-- depth:1
[909]=910,	-- depth:2
[908]=909,	-- depth:3
[907]=908,	-- depth:4
[906]=907,	-- depth:5
[905]=906,	-- depth:6
[904]=905,	-- depth:7
[903]=904,	-- depth:8
[307]=907,	-- depth:5
[154]=904,	-- depth:8
[306]=307,	-- depth:6
[153]=154,	-- depth:9
[308]=306,	-- depth:7
[152]=153,	-- depth:10
[902]=152,	-- depth:11
[309]=308,	-- depth:8
[458]=308,	-- depth:8
[1052]=902,	-- depth:12
[1053]=1052,	-- depth:13
[1054]=1053,	-- depth:14
[1055]=1054,	-- depth:15
[1056]=1055,	-- depth:16
[1057]=1056,	-- depth:17
[1058]=1057,	-- depth:18
[1059]=1058,	-- depth:19
[310]=309,	-- depth:9
[452]=1052,	-- depth:13
[454]=452,	-- depth:14
[455]=454,	-- depth:15
[456]=455,	-- depth:16
[457]=456,	-- depth:17
[155]=455,	-- depth:16
[459]=457,	-- depth:18
[460]=459,	-- depth:19
[1060]=460,	-- depth:20
[453]=460,	-- depth:20
[156]=155,	-- depth:17
[158]=156,	-- depth:18
[759]=459,	-- depth:19
[760]=759,	-- depth:20
[756]=760,	-- depth:21
[755]=756,	-- depth:22
[754]=755,	-- depth:23
[753]=754,	-- depth:24
[157]=158,	-- depth:19
[752]=753,	-- depth:25
[758]=752,	-- depth:26
[610]=760,	-- depth:21
[609]=610,	-- depth:22
[607]=609,	-- depth:23
[159]=609,	-- depth:23
[160]=159,	-- depth:24
[304]=754,	-- depth:24
[303]=304,	-- depth:25
[302]=303,	-- depth:26
[602]=302,	-- depth:27
[603]=602,	-- depth:28
[604]=603,	-- depth:29
[757]=607,	-- depth:24
[605]=604,	-- depth:30
[606]=605,	-- depth:31
[608]=606,	-- depth:32
[331]=31,	-- depth:1
[1151]=101,	-- depth:1
[211]=61,	-- depth:1
[1161]=111,	-- depth:1
[201]=51,	-- depth:1
[341]=41,	-- depth:1
[1041]=141,	-- depth:1
[351]=201,	-- depth:2
[221]=71,	-- depth:1
[1061]=11,	-- depth:1
[231]=81,	-- depth:1
[1081]=331,	-- depth:2
[291]=1041,	-- depth:2
[281]=131,	-- depth:1
[1091]=341,	-- depth:2
[271]=121,	-- depth:1
[1101]=351,	-- depth:3
[311]=1061,	-- depth:2
[1071]=21,	-- depth:1
[321]=1071,	-- depth:2
[261]=1161,	-- depth:2
[1111]=211,	-- depth:2
[251]=1151,	-- depth:2
[1121]=221,	-- depth:2
[241]=91,	-- depth:1
[1131]=231,	-- depth:2
[1141]=241,	-- depth:2
[1031]=281,	-- depth:2
[941]=1091,	-- depth:3
[371]=1121,	-- depth:3
[561]=261,	-- depth:3
[851]=251,	-- depth:3
[571]=271,	-- depth:2
[841]=1141,	-- depth:3
[581]=1031,	-- depth:3
[831]=1131,	-- depth:3
[591]=291,	-- depth:3
[821]=371,	-- depth:4
[611]=311,	-- depth:3
[621]=321,	-- depth:3
[811]=1111,	-- depth:3
[631]=1081,	-- depth:3
[801]=1101,	-- depth:4
[641]=941,	-- depth:4
[791]=641,	-- depth:5
[651]=801,	-- depth:5
[781]=631,	-- depth:4
[661]=811,	-- depth:4
[771]=621,	-- depth:4
[671]=821,	-- depth:5
[761]=611,	-- depth:4
[681]=831,	-- depth:4
[691]=841,	-- depth:4
[741]=591,	-- depth:4
[701]=851,	-- depth:4
[731]=581,	-- depth:4
[711]=561,	-- depth:4
[861]=711,	-- depth:5
[551]=701,	-- depth:5
[871]=571,	-- depth:3
[541]=691,	-- depth:5
[1011]=861,	-- depth:6
[381]=681,	-- depth:5
[1001]=551,	-- depth:6
[391]=541,	-- depth:6
[401]=1001,	-- depth:7
[991]=391,	-- depth:7
[411]=1011,	-- depth:7
[981]=381,	-- depth:6
[421]=871,	-- depth:4
[971]=671,	-- depth:6
[431]=731,	-- depth:5
[961]=661,	-- depth:5
[441]=741,	-- depth:5
[1021]=421,	-- depth:5
[951]=651,	-- depth:6
[1171]=1021,	-- depth:6
[471]=771,	-- depth:5
[931]=781,	-- depth:5
[481]=931,	-- depth:6
[921]=471,	-- depth:6
[491]=791,	-- depth:6
[911]=761,	-- depth:5
[501]=951,	-- depth:7
[511]=961,	-- depth:6
[521]=971,	-- depth:7
[891]=441,	-- depth:6
[531]=981,	-- depth:7
[881]=431,	-- depth:6
[461]=911,	-- depth:6
[191]=491,	-- depth:7
[361]=511,	-- depth:7
[102]=101,	-- depth:1
[109]=102,	-- depth:2
[108]=109,	-- depth:3
[107]=108,	-- depth:4
[106]=107,	-- depth:5
[105]=106,	-- depth:6
[104]=105,	-- depth:7
[103]=104,	-- depth:8
[100]=91,	-- depth:1
[99]=100,	-- depth:2
[98]=99,	-- depth:3
[97]=98,	-- depth:4
[96]=97,	-- depth:5
[110]=103,	-- depth:9
[95]=96,	-- depth:6
[93]=95,	-- depth:7
[92]=93,	-- depth:8
[90]=81,	-- depth:1
[89]=90,	-- depth:2
[88]=89,	-- depth:3
[87]=88,	-- depth:4
[86]=87,	-- depth:5
[85]=86,	-- depth:6
[84]=85,	-- depth:7
[83]=84,	-- depth:8
[82]=83,	-- depth:9
[80]=71,	-- depth:1
[94]=92,	-- depth:9
[112]=111,	-- depth:1
[113]=112,	-- depth:2
[114]=113,	-- depth:3
[144]=141,	-- depth:1
[143]=144,	-- depth:2
[142]=143,	-- depth:3
[140]=131,	-- depth:1
[139]=140,	-- depth:2
[138]=139,	-- depth:3
[137]=138,	-- depth:4
[136]=137,	-- depth:5
[135]=136,	-- depth:6
[134]=135,	-- depth:7
[133]=134,	-- depth:8
[132]=133,	-- depth:9
[130]=121,	-- depth:1
[129]=130,	-- depth:2
[128]=129,	-- depth:3
[127]=128,	-- depth:4
[126]=127,	-- depth:5
[125]=126,	-- depth:6
[124]=125,	-- depth:7
[123]=124,	-- depth:8
[122]=123,	-- depth:9
[120]=114,	-- depth:4
[119]=120,	-- depth:5
[118]=119,	-- depth:6
[117]=118,	-- depth:7
[116]=117,	-- depth:8
[115]=116,	-- depth:9
[79]=80,	-- depth:2
[78]=79,	-- depth:3
[77]=78,	-- depth:4
[76]=77,	-- depth:5
[40]=31,	-- depth:1
[39]=40,	-- depth:2
[38]=39,	-- depth:3
[37]=38,	-- depth:4
[36]=37,	-- depth:5
[35]=36,	-- depth:6
[34]=35,	-- depth:7
[33]=34,	-- depth:8
[32]=33,	-- depth:9
[30]=21,	-- depth:1
[29]=30,	-- depth:2
[28]=29,	-- depth:3
[27]=28,	-- depth:4
[26]=27,	-- depth:5
[25]=26,	-- depth:6
[24]=25,	-- depth:7
[23]=24,	-- depth:8
[22]=23,	-- depth:9
[20]=11,	-- depth:1
[19]=20,	-- depth:2
[18]=19,	-- depth:3
[17]=18,	-- depth:4
[16]=17,	-- depth:5
[15]=16,	-- depth:6
[14]=15,	-- depth:7
[13]=14,	-- depth:8
[12]=13,	-- depth:9
[42]=41,	-- depth:1
[145]=142,	-- depth:4
[43]=42,	-- depth:2
[45]=43,	-- depth:3
[75]=76,	-- depth:6
[74]=75,	-- depth:7
[73]=74,	-- depth:8
[72]=73,	-- depth:9
[70]=61,	-- depth:1
[69]=70,	-- depth:2
[68]=69,	-- depth:3
[67]=68,	-- depth:4
[66]=67,	-- depth:5
[65]=66,	-- depth:6
[64]=65,	-- depth:7
[63]=64,	-- depth:8
[62]=63,	-- depth:9
[60]=51,	-- depth:1
[59]=60,	-- depth:2
[58]=59,	-- depth:3
[57]=58,	-- depth:4
[56]=57,	-- depth:5
[55]=56,	-- depth:6
[54]=55,	-- depth:7
[53]=54,	-- depth:8
[52]=53,	-- depth:9
[50]=45,	-- depth:4
[49]=50,	-- depth:5
[48]=49,	-- depth:6
[47]=48,	-- depth:7
[46]=47,	-- depth:8
[44]=46,	-- depth:9
[146]=145,	-- depth:5
[721]=1171,	-- depth:7
[148]=146,	-- depth:6
[1181]=881,	-- depth:7
[181]=481,	-- depth:7
[1191]=891,	-- depth:7
[161]=461,	-- depth:7
[171]=921,	-- depth:7
[150]=148,	-- depth:7
[147]=150,	-- depth:8
[149]=147,	-- depth:9
[1138]=88,	-- depth:4
[830]=80,	-- depth:2
[1137]=1138,	-- depth:5
[832]=82,	-- depth:10
[833]=832,	-- depth:11
[834]=833,	-- depth:12
[835]=834,	-- depth:13
[836]=835,	-- depth:14
[837]=836,	-- depth:15
[1186]=1181,	-- depth:8
[838]=837,	-- depth:16
[1136]=836,	-- depth:15
[839]=838,	-- depth:17
[840]=839,	-- depth:18
[1184]=1186,	-- depth:9
[842]=92,	-- depth:9
[843]=842,	-- depth:10
[844]=843,	-- depth:11
[845]=844,	-- depth:12
[846]=845,	-- depth:13
[829]=830,	-- depth:3
[828]=829,	-- depth:4
[827]=828,	-- depth:5
[826]=827,	-- depth:6
[817]=67,	-- depth:5
[818]=817,	-- depth:6
[819]=818,	-- depth:7
[820]=819,	-- depth:8
[1149]=99,	-- depth:3
[1148]=1149,	-- depth:4
[1147]=1148,	-- depth:5
[1146]=1147,	-- depth:6
[1145]=1146,	-- depth:7
[847]=1147,	-- depth:6
[1182]=1184,	-- depth:10
[1143]=1145,	-- depth:8
[1142]=1143,	-- depth:9
[1183]=1182,	-- depth:11
[1140]=840,	-- depth:19
[1139]=1140,	-- depth:20
[822]=826,	-- depth:7
[823]=822,	-- depth:8
[824]=823,	-- depth:9
[825]=824,	-- depth:10
[1144]=1142,	-- depth:10
[1135]=1139,	-- depth:21
[848]=847,	-- depth:7
[849]=848,	-- depth:8
[873]=123,	-- depth:9
[874]=873,	-- depth:10
[1129]=829,	-- depth:4
[875]=874,	-- depth:11
[876]=875,	-- depth:12
[877]=876,	-- depth:13
[878]=877,	-- depth:14
[879]=878,	-- depth:15
[880]=879,	-- depth:16
[1128]=1129,	-- depth:5
[882]=1182,	-- depth:11
[883]=882,	-- depth:12
[1127]=1128,	-- depth:6
[884]=883,	-- depth:13
[885]=884,	-- depth:14
[886]=885,	-- depth:15
[1126]=1127,	-- depth:7
[887]=886,	-- depth:16
[888]=887,	-- depth:17
[889]=888,	-- depth:18
[890]=889,	-- depth:19
[872]=880,	-- depth:17
[1130]=1126,	-- depth:8
[870]=120,	-- depth:5
[869]=870,	-- depth:6
[850]=849,	-- depth:9
[816]=820,	-- depth:9
[1134]=1135,	-- depth:22
[852]=102,	-- depth:2
[853]=852,	-- depth:3
[854]=853,	-- depth:4
[855]=854,	-- depth:5
[856]=855,	-- depth:6
[1133]=1134,	-- depth:23
[857]=856,	-- depth:7
[892]=142,	-- depth:4
[858]=857,	-- depth:8
[860]=858,	-- depth:9
[1132]=1133,	-- depth:24
[862]=869,	-- depth:7
[863]=862,	-- depth:8
[864]=863,	-- depth:9
[865]=864,	-- depth:10
[1185]=885,	-- depth:15
[866]=865,	-- depth:11
[867]=866,	-- depth:12
[868]=867,	-- depth:13
[859]=860,	-- depth:10
[815]=816,	-- depth:10
[812]=815,	-- depth:11
[813]=812,	-- depth:12
[747]=147,	-- depth:9
[748]=747,	-- depth:10
[1178]=878,	-- depth:15
[749]=748,	-- depth:11
[750]=749,	-- depth:12
[1170]=870,	-- depth:6
[1169]=1170,	-- depth:7
[1168]=1169,	-- depth:8
[1167]=1168,	-- depth:9
[1166]=1167,	-- depth:10
[1165]=1166,	-- depth:11
[1164]=1165,	-- depth:12
[1163]=1164,	-- depth:13
[1179]=1178,	-- depth:16
[1162]=1163,	-- depth:14
[1180]=1179,	-- depth:17
[1160]=860,	-- depth:10
[762]=12,	-- depth:10
[763]=762,	-- depth:11
[764]=763,	-- depth:12
[765]=764,	-- depth:13
[746]=750,	-- depth:13
[745]=746,	-- depth:14
[744]=745,	-- depth:15
[743]=744,	-- depth:16
[723]=873,	-- depth:10
[724]=723,	-- depth:11
[725]=724,	-- depth:12
[726]=725,	-- depth:13
[727]=726,	-- depth:14
[728]=727,	-- depth:15
[729]=728,	-- depth:16
[730]=729,	-- depth:17
[1174]=724,	-- depth:12
[1173]=1174,	-- depth:13
[766]=765,	-- depth:14
[732]=882,	-- depth:12
[734]=732,	-- depth:13
[735]=734,	-- depth:14
[736]=735,	-- depth:15
[737]=736,	-- depth:16
[738]=737,	-- depth:17
[739]=738,	-- depth:18
[1172]=1173,	-- depth:14
[740]=739,	-- depth:19
[1177]=1172,	-- depth:15
[742]=743,	-- depth:17
[733]=740,	-- depth:20
[814]=813,	-- depth:13
[1159]=1160,	-- depth:11
[768]=766,	-- depth:15
[793]=43,	-- depth:3
[794]=793,	-- depth:4
[795]=794,	-- depth:5
[796]=795,	-- depth:6
[797]=796,	-- depth:7
[798]=797,	-- depth:8
[799]=798,	-- depth:9
[800]=799,	-- depth:10
[1153]=1159,	-- depth:12
[802]=52,	-- depth:10
[1152]=1153,	-- depth:13
[803]=802,	-- depth:11
[804]=803,	-- depth:12
[805]=804,	-- depth:13
[806]=805,	-- depth:14
[807]=806,	-- depth:15
[808]=807,	-- depth:16
[809]=808,	-- depth:17
[810]=809,	-- depth:18
[1150]=850,	-- depth:10
[893]=743,	-- depth:17
[792]=800,	-- depth:11
[1154]=1152,	-- depth:14
[790]=40,	-- depth:2
[789]=790,	-- depth:3
[769]=768,	-- depth:16
[770]=769,	-- depth:17
[1158]=1154,	-- depth:15
[772]=22,	-- depth:10
[773]=772,	-- depth:11
[774]=773,	-- depth:12
[775]=774,	-- depth:13
[1157]=1158,	-- depth:16
[776]=775,	-- depth:14
[777]=776,	-- depth:15
[767]=770,	-- depth:18
[778]=777,	-- depth:16
[780]=778,	-- depth:17
[1156]=1157,	-- depth:17
[782]=789,	-- depth:4
[783]=782,	-- depth:5
[784]=783,	-- depth:6
[1155]=1156,	-- depth:18
[785]=784,	-- depth:7
[786]=785,	-- depth:8
[787]=786,	-- depth:9
[788]=787,	-- depth:10
[779]=780,	-- depth:18
[894]=893,	-- depth:18
[898]=894,	-- depth:19
[896]=898,	-- depth:20
[1006]=1156,	-- depth:18
[1007]=1006,	-- depth:19
[1008]=1007,	-- depth:20
[1009]=1008,	-- depth:21
[1088]=788,	-- depth:11
[1010]=1009,	-- depth:22
[1087]=1088,	-- depth:12
[1012]=1162,	-- depth:15
[1013]=1012,	-- depth:16
[1014]=1013,	-- depth:17
[1015]=1014,	-- depth:18
[1016]=1015,	-- depth:19
[1017]=1016,	-- depth:20
[1018]=1017,	-- depth:21
[1086]=1087,	-- depth:13
[1019]=1018,	-- depth:22
[1020]=1019,	-- depth:23
[1085]=1086,	-- depth:14
[1022]=1172,	-- depth:15
[1023]=1022,	-- depth:16
[1024]=1023,	-- depth:17
[1005]=1010,	-- depth:23
[1004]=1005,	-- depth:24
[1003]=1004,	-- depth:25
[1002]=1003,	-- depth:26
[1092]=792,	-- depth:12
[983]=1133,	-- depth:24
[984]=983,	-- depth:25
[985]=984,	-- depth:26
[986]=985,	-- depth:27
[987]=986,	-- depth:28
[988]=987,	-- depth:29
[989]=988,	-- depth:30
[990]=989,	-- depth:31
[1192]=742,	-- depth:18
[1025]=1024,	-- depth:18
[1193]=1192,	-- depth:19
[993]=1143,	-- depth:9
[994]=993,	-- depth:10
[995]=994,	-- depth:11
[996]=995,	-- depth:12
[997]=996,	-- depth:13
[998]=997,	-- depth:14
[999]=998,	-- depth:15
[1000]=999,	-- depth:16
[1090]=1085,	-- depth:15
[1089]=1090,	-- depth:16
[992]=1000,	-- depth:17
[982]=990,	-- depth:32
[1026]=1025,	-- depth:19
[722]=1022,	-- depth:16
[1080]=780,	-- depth:18
[1079]=1080,	-- depth:19
[1078]=1079,	-- depth:20
[1077]=1078,	-- depth:21
[1076]=1077,	-- depth:22
[1075]=1076,	-- depth:23
[1074]=1075,	-- depth:24
[1073]=1074,	-- depth:25
[1196]=1193,	-- depth:20
[1197]=1196,	-- depth:21
[1198]=1197,	-- depth:22
[1062]=762,	-- depth:11
[1063]=1062,	-- depth:12
[1064]=1063,	-- depth:13
[1065]=1064,	-- depth:14
[1066]=1065,	-- depth:15
[1067]=1066,	-- depth:16
[1068]=1067,	-- depth:17
[1069]=1068,	-- depth:18
[1070]=1069,	-- depth:19
[1072]=1073,	-- depth:26
[1195]=1198,	-- depth:23
[1050]=750,	-- depth:13
[1049]=1050,	-- depth:14
[1048]=1049,	-- depth:15
[1028]=1026,	-- depth:20
[1029]=1028,	-- depth:21
[1030]=1029,	-- depth:22
[1084]=1089,	-- depth:17
[1032]=732,	-- depth:13
[1033]=1032,	-- depth:14
[1034]=1033,	-- depth:15
[1035]=1034,	-- depth:16
[1036]=1035,	-- depth:17
[1083]=1084,	-- depth:18
[1027]=1030,	-- depth:23
[1037]=1036,	-- depth:18
[1039]=1037,	-- depth:19
[1040]=1039,	-- depth:20
[1082]=1083,	-- depth:19
[1042]=1048,	-- depth:16
[1043]=1042,	-- depth:17
[1044]=1043,	-- depth:18
[1045]=1044,	-- depth:19
[1194]=1044,	-- depth:19
[1046]=1045,	-- depth:20
[1047]=1046,	-- depth:21
[1038]=1040,	-- depth:21
[895]=1045,	-- depth:20
[1093]=1092,	-- depth:13
[979]=1129,	-- depth:5
[1112]=812,	-- depth:12
[922]=1072,	-- depth:27
[923]=922,	-- depth:28
[924]=923,	-- depth:29
[925]=924,	-- depth:30
[926]=925,	-- depth:31
[927]=926,	-- depth:32
[928]=927,	-- depth:33
[1189]=1039,	-- depth:20
[929]=928,	-- depth:34
[930]=929,	-- depth:35
[1110]=810,	-- depth:19
[932]=1082,	-- depth:20
[933]=932,	-- depth:21
[934]=933,	-- depth:22
[935]=934,	-- depth:23
[936]=935,	-- depth:24
[937]=936,	-- depth:25
[1109]=1110,	-- depth:20
[938]=937,	-- depth:26
[939]=938,	-- depth:27
[920]=1070,	-- depth:20
[1113]=1112,	-- depth:13
[919]=920,	-- depth:21
[918]=919,	-- depth:22
[897]=895,	-- depth:21
[899]=897,	-- depth:22
[900]=899,	-- depth:23
[1125]=1130,	-- depth:9
[1124]=1125,	-- depth:10
[1123]=1124,	-- depth:11
[1122]=1123,	-- depth:12
[1187]=1189,	-- depth:21
[1120]=1113,	-- depth:14
[1119]=1120,	-- depth:15
[940]=939,	-- depth:28
[1118]=1119,	-- depth:16
[1117]=1118,	-- depth:17
[1116]=1117,	-- depth:18
[1115]=1116,	-- depth:19
[1114]=1115,	-- depth:20
[912]=918,	-- depth:23
[913]=912,	-- depth:24
[914]=913,	-- depth:25
[915]=914,	-- depth:26
[916]=915,	-- depth:27
[917]=916,	-- depth:28
[1188]=1187,	-- depth:22
[980]=979,	-- depth:6
[942]=1092,	-- depth:13
[944]=942,	-- depth:14
[960]=1110,	-- depth:20
[1097]=1093,	-- depth:14
[962]=1112,	-- depth:13
[963]=962,	-- depth:14
[964]=963,	-- depth:15
[1096]=1097,	-- depth:15
[965]=964,	-- depth:16
[966]=965,	-- depth:17
[967]=966,	-- depth:18
[968]=967,	-- depth:19
[969]=968,	-- depth:20
[970]=969,	-- depth:21
[1095]=1096,	-- depth:16
[972]=980,	-- depth:7
[973]=972,	-- depth:8
[1094]=1095,	-- depth:17
[974]=973,	-- depth:9
[975]=974,	-- depth:10
[976]=975,	-- depth:11
[977]=976,	-- depth:12
[978]=977,	-- depth:13
[959]=960,	-- depth:21
[958]=959,	-- depth:22
[957]=958,	-- depth:23
[956]=957,	-- depth:24
[945]=1095,	-- depth:17
[946]=945,	-- depth:18
[1108]=958,	-- depth:23
[1107]=1108,	-- depth:24
[1106]=1107,	-- depth:25
[1105]=1106,	-- depth:26
[1104]=1105,	-- depth:27
[1103]=1104,	-- depth:28
[1102]=1103,	-- depth:29
[1176]=1026,	-- depth:20
[943]=946,	-- depth:19
[1190]=1188,	-- depth:23
[947]=943,	-- depth:20
[948]=947,	-- depth:21
[949]=948,	-- depth:22
[950]=949,	-- depth:23
[1099]=949,	-- depth:23
[952]=1102,	-- depth:30
[953]=952,	-- depth:31
[954]=953,	-- depth:32
[955]=954,	-- depth:33
[1098]=1099,	-- depth:24
[1100]=1098,	-- depth:25
[1175]=1176,	-- depth:21
[600]=900,	-- depth:24
[719]=1019,	-- depth:23
[338]=938,	-- depth:27
[339]=338,	-- depth:28
[340]=339,	-- depth:29
[342]=942,	-- depth:14
[343]=342,	-- depth:15
[344]=343,	-- depth:16
[345]=344,	-- depth:17
[346]=345,	-- depth:18
[347]=346,	-- depth:19
[348]=347,	-- depth:20
[349]=348,	-- depth:21
[350]=349,	-- depth:22
[352]=952,	-- depth:31
[353]=352,	-- depth:32
[354]=353,	-- depth:33
[355]=354,	-- depth:34
[356]=355,	-- depth:35
[357]=356,	-- depth:36
[358]=357,	-- depth:37
[359]=358,	-- depth:38
[360]=359,	-- depth:39
[362]=962,	-- depth:14
[363]=362,	-- depth:15
[364]=363,	-- depth:16
[365]=364,	-- depth:17
[337]=340,	-- depth:30
[336]=337,	-- depth:31
[335]=336,	-- depth:32
[334]=335,	-- depth:33
[295]=895,	-- depth:21
[296]=295,	-- depth:22
[297]=296,	-- depth:23
[298]=297,	-- depth:24
[299]=298,	-- depth:25
[300]=299,	-- depth:26
[312]=912,	-- depth:24
[313]=312,	-- depth:25
[314]=313,	-- depth:26
[315]=314,	-- depth:27
[316]=315,	-- depth:28
[317]=316,	-- depth:29
[366]=365,	-- depth:18
[318]=317,	-- depth:30
[320]=318,	-- depth:31
[322]=922,	-- depth:28
[323]=322,	-- depth:29
[324]=323,	-- depth:30
[325]=324,	-- depth:31
[326]=325,	-- depth:32
[327]=326,	-- depth:33
[328]=327,	-- depth:34
[329]=328,	-- depth:35
[330]=329,	-- depth:36
[332]=334,	-- depth:34
[333]=332,	-- depth:35
[319]=320,	-- depth:32
[367]=366,	-- depth:19
[368]=367,	-- depth:20
[369]=368,	-- depth:21
[404]=1004,	-- depth:25
[405]=404,	-- depth:26
[406]=405,	-- depth:27
[407]=406,	-- depth:28
[408]=407,	-- depth:29
[409]=408,	-- depth:30
[410]=409,	-- depth:31
[412]=1012,	-- depth:16
[413]=412,	-- depth:17
[414]=413,	-- depth:18
[415]=414,	-- depth:19
[416]=415,	-- depth:20
[403]=410,	-- depth:32
[417]=416,	-- depth:21
[419]=417,	-- depth:22
[420]=419,	-- depth:23
[422]=722,	-- depth:17
[423]=422,	-- depth:18
[424]=423,	-- depth:19
[425]=424,	-- depth:20
[426]=425,	-- depth:21
[427]=426,	-- depth:22
[428]=427,	-- depth:23
[429]=428,	-- depth:24
[430]=429,	-- depth:25
[432]=1032,	-- depth:14
[418]=420,	-- depth:24
[294]=300,	-- depth:27
[402]=403,	-- depth:33
[399]=999,	-- depth:16
[370]=369,	-- depth:22
[372]=972,	-- depth:8
[373]=372,	-- depth:9
[374]=373,	-- depth:10
[375]=374,	-- depth:11
[376]=375,	-- depth:12
[377]=376,	-- depth:13
[378]=377,	-- depth:14
[379]=378,	-- depth:15
[380]=379,	-- depth:16
[382]=982,	-- depth:33
[383]=382,	-- depth:34
[400]=399,	-- depth:17
[384]=383,	-- depth:35
[386]=384,	-- depth:36
[387]=386,	-- depth:37
[388]=387,	-- depth:38
[389]=388,	-- depth:39
[390]=389,	-- depth:40
[392]=400,	-- depth:18
[393]=392,	-- depth:19
[394]=393,	-- depth:20
[395]=394,	-- depth:21
[396]=395,	-- depth:22
[397]=396,	-- depth:23
[398]=397,	-- depth:24
[385]=390,	-- depth:41
[293]=294,	-- depth:28
[292]=293,	-- depth:29
[290]=1190,	-- depth:24
[195]=345,	-- depth:18
[196]=195,	-- depth:19
[197]=196,	-- depth:20
[198]=197,	-- depth:21
[199]=198,	-- depth:22
[200]=199,	-- depth:23
[202]=352,	-- depth:32
[203]=202,	-- depth:33
[204]=203,	-- depth:34
[205]=204,	-- depth:35
[206]=205,	-- depth:36
[207]=206,	-- depth:37
[194]=200,	-- depth:24
[208]=207,	-- depth:38
[210]=208,	-- depth:39
[212]=362,	-- depth:15
[213]=212,	-- depth:16
[214]=213,	-- depth:17
[215]=214,	-- depth:18
[216]=215,	-- depth:19
[217]=216,	-- depth:20
[218]=217,	-- depth:21
[219]=218,	-- depth:22
[220]=219,	-- depth:23
[222]=372,	-- depth:9
[223]=222,	-- depth:10
[209]=210,	-- depth:40
[224]=223,	-- depth:11
[193]=194,	-- depth:25
[190]=340,	-- depth:30
[162]=312,	-- depth:25
[163]=162,	-- depth:26
[164]=163,	-- depth:27
[165]=164,	-- depth:28
[166]=165,	-- depth:29
[167]=166,	-- depth:30
[168]=167,	-- depth:31
[169]=168,	-- depth:32
[170]=169,	-- depth:33
[172]=322,	-- depth:29
[173]=172,	-- depth:30
[174]=173,	-- depth:31
[192]=193,	-- depth:26
[175]=174,	-- depth:32
[177]=175,	-- depth:33
[178]=177,	-- depth:34
[179]=178,	-- depth:35
[180]=179,	-- depth:36
[182]=190,	-- depth:31
[183]=182,	-- depth:32
[184]=183,	-- depth:33
[185]=184,	-- depth:34
[186]=185,	-- depth:35
[187]=186,	-- depth:36
[188]=187,	-- depth:37
[189]=188,	-- depth:38
[176]=180,	-- depth:37
[433]=432,	-- depth:15
[225]=224,	-- depth:12
[227]=225,	-- depth:13
[262]=412,	-- depth:17
[263]=262,	-- depth:18
[264]=263,	-- depth:19
[265]=264,	-- depth:20
[266]=265,	-- depth:21
[267]=266,	-- depth:22
[268]=267,	-- depth:23
[269]=268,	-- depth:24
[270]=269,	-- depth:25
[272]=422,	-- depth:18
[273]=272,	-- depth:19
[274]=273,	-- depth:20
[260]=410,	-- depth:32
[275]=274,	-- depth:21
[277]=275,	-- depth:22
[278]=277,	-- depth:23
[279]=278,	-- depth:24
[280]=279,	-- depth:25
[282]=290,	-- depth:25
[283]=282,	-- depth:26
[284]=283,	-- depth:27
[285]=284,	-- depth:28
[286]=285,	-- depth:29
[287]=286,	-- depth:30
[288]=287,	-- depth:31
[289]=288,	-- depth:32
[276]=280,	-- depth:26
[226]=227,	-- depth:14
[259]=260,	-- depth:33
[257]=259,	-- depth:34
[228]=226,	-- depth:15
[229]=228,	-- depth:16
[230]=229,	-- depth:17
[232]=382,	-- depth:34
[233]=232,	-- depth:35
[234]=233,	-- depth:36
[235]=234,	-- depth:37
[236]=235,	-- depth:38
[237]=236,	-- depth:39
[238]=237,	-- depth:40
[239]=238,	-- depth:41
[240]=239,	-- depth:42
[258]=257,	-- depth:35
[242]=392,	-- depth:19
[244]=242,	-- depth:20
[245]=244,	-- depth:21
[246]=245,	-- depth:22
[247]=246,	-- depth:23
[248]=247,	-- depth:24
[249]=248,	-- depth:25
[250]=249,	-- depth:26
[252]=258,	-- depth:36
[253]=252,	-- depth:37
[254]=253,	-- depth:38
[255]=254,	-- depth:39
[256]=255,	-- depth:40
[243]=250,	-- depth:27
[434]=284,	-- depth:28
[435]=434,	-- depth:29
[436]=435,	-- depth:30
[624]=174,	-- depth:32
[625]=624,	-- depth:33
[626]=625,	-- depth:34
[627]=626,	-- depth:35
[628]=627,	-- depth:36
[629]=628,	-- depth:37
[630]=629,	-- depth:38
[632]=182,	-- depth:32
[633]=632,	-- depth:33
[634]=633,	-- depth:34
[635]=634,	-- depth:35
[636]=635,	-- depth:36
[623]=630,	-- depth:39
[637]=636,	-- depth:37
[639]=637,	-- depth:38
[640]=639,	-- depth:39
[642]=192,	-- depth:27
[643]=642,	-- depth:28
[644]=643,	-- depth:29
[645]=644,	-- depth:30
[646]=645,	-- depth:31
[647]=646,	-- depth:32
[648]=647,	-- depth:33
[649]=648,	-- depth:34
[650]=649,	-- depth:35
[652]=202,	-- depth:33
[638]=640,	-- depth:40
[653]=652,	-- depth:34
[622]=623,	-- depth:40
[619]=169,	-- depth:33
[580]=280,	-- depth:26
[582]=282,	-- depth:26
[583]=582,	-- depth:27
[584]=583,	-- depth:28
[585]=584,	-- depth:29
[586]=585,	-- depth:30
[587]=586,	-- depth:31
[588]=587,	-- depth:32
[589]=588,	-- depth:33
[590]=589,	-- depth:34
[592]=292,	-- depth:30
[593]=592,	-- depth:31
[620]=619,	-- depth:34
[594]=593,	-- depth:32
[596]=594,	-- depth:33
[597]=596,	-- depth:34
[598]=597,	-- depth:35
[599]=598,	-- depth:36
[1199]=599,	-- depth:37
[612]=620,	-- depth:35
[613]=612,	-- depth:36
[614]=613,	-- depth:37
[615]=614,	-- depth:38
[616]=615,	-- depth:39
[617]=616,	-- depth:40
[618]=617,	-- depth:41
[595]=599,	-- depth:37
[579]=580,	-- depth:27
[654]=653,	-- depth:35
[656]=654,	-- depth:36
[690]=240,	-- depth:43
[692]=242,	-- depth:20
[693]=692,	-- depth:21
[694]=693,	-- depth:22
[695]=694,	-- depth:23
[696]=695,	-- depth:24
[697]=696,	-- depth:25
[698]=697,	-- depth:26
[699]=698,	-- depth:27
[700]=699,	-- depth:28
[702]=252,	-- depth:37
[703]=702,	-- depth:38
[689]=690,	-- depth:44
[704]=703,	-- depth:39
[706]=704,	-- depth:40
[707]=706,	-- depth:41
[708]=707,	-- depth:42
[709]=708,	-- depth:43
[710]=709,	-- depth:44
[712]=262,	-- depth:18
[713]=712,	-- depth:19
[714]=713,	-- depth:20
[715]=714,	-- depth:21
[716]=715,	-- depth:22
[717]=716,	-- depth:23
[718]=717,	-- depth:24
[705]=710,	-- depth:45
[655]=656,	-- depth:37
[688]=689,	-- depth:45
[686]=688,	-- depth:46
[657]=655,	-- depth:38
[658]=657,	-- depth:39
[659]=658,	-- depth:40
[660]=659,	-- depth:41
[662]=212,	-- depth:16
[663]=662,	-- depth:17
[664]=663,	-- depth:18
[665]=664,	-- depth:19
[666]=665,	-- depth:20
[667]=666,	-- depth:21
[668]=667,	-- depth:22
[669]=668,	-- depth:23
[687]=686,	-- depth:47
[670]=669,	-- depth:24
[673]=223,	-- depth:11
[674]=673,	-- depth:12
[675]=674,	-- depth:13
[676]=675,	-- depth:14
[677]=676,	-- depth:15
[678]=677,	-- depth:16
[679]=678,	-- depth:17
[680]=679,	-- depth:18
[682]=687,	-- depth:48
[683]=682,	-- depth:49
[684]=683,	-- depth:50
[685]=684,	-- depth:51
[672]=680,	-- depth:19
[720]=718,	-- depth:25
[578]=579,	-- depth:28
[576]=578,	-- depth:29
[480]=630,	-- depth:39
[482]=632,	-- depth:33
[483]=482,	-- depth:34
[484]=483,	-- depth:35
[485]=484,	-- depth:36
[486]=485,	-- depth:37
[487]=486,	-- depth:38
[488]=487,	-- depth:39
[489]=488,	-- depth:40
[490]=489,	-- depth:41
[492]=642,	-- depth:28
[493]=492,	-- depth:29
[479]=480,	-- depth:40
[494]=493,	-- depth:30
[496]=494,	-- depth:31
[497]=496,	-- depth:32
[498]=497,	-- depth:33
[499]=498,	-- depth:34
[500]=499,	-- depth:35
[502]=652,	-- depth:34
[503]=502,	-- depth:35
[504]=503,	-- depth:36
[505]=504,	-- depth:37
[506]=505,	-- depth:38
[507]=506,	-- depth:39
[508]=507,	-- depth:40
[495]=500,	-- depth:36
[509]=508,	-- depth:41
[478]=479,	-- depth:41
[476]=478,	-- depth:42
[437]=587,	-- depth:32
[438]=437,	-- depth:33
[439]=438,	-- depth:34
[440]=439,	-- depth:35
[442]=592,	-- depth:31
[443]=442,	-- depth:32
[444]=443,	-- depth:33
[445]=444,	-- depth:34
[446]=445,	-- depth:35
[447]=446,	-- depth:36
[448]=447,	-- depth:37
[449]=448,	-- depth:38
[477]=476,	-- depth:43
[450]=449,	-- depth:39
[463]=613,	-- depth:37
[464]=463,	-- depth:38
[465]=464,	-- depth:39
[466]=465,	-- depth:40
[467]=466,	-- depth:41
[468]=467,	-- depth:42
[469]=468,	-- depth:43
[470]=469,	-- depth:44
[472]=477,	-- depth:44
[473]=472,	-- depth:45
[474]=473,	-- depth:46
[475]=474,	-- depth:47
[462]=470,	-- depth:45
[577]=576,	-- depth:30
[510]=509,	-- depth:42
[513]=663,	-- depth:18
[547]=697,	-- depth:26
[548]=547,	-- depth:27
[549]=548,	-- depth:28
[550]=549,	-- depth:29
[552]=702,	-- depth:38
[553]=552,	-- depth:39
[554]=553,	-- depth:40
[555]=554,	-- depth:41
[556]=555,	-- depth:42
[557]=556,	-- depth:43
[558]=557,	-- depth:44
[559]=558,	-- depth:45
[546]=550,	-- depth:30
[560]=559,	-- depth:46
[563]=713,	-- depth:20
[564]=563,	-- depth:21
[565]=564,	-- depth:22
[566]=565,	-- depth:23
[567]=566,	-- depth:24
[568]=567,	-- depth:25
[569]=568,	-- depth:26
[570]=569,	-- depth:27
[572]=577,	-- depth:31
[573]=572,	-- depth:32
[574]=573,	-- depth:33
[575]=574,	-- depth:34
[562]=570,	-- depth:28
[512]=513,	-- depth:19
[545]=546,	-- depth:31
[543]=545,	-- depth:32
[514]=512,	-- depth:20
[515]=514,	-- depth:21
[516]=515,	-- depth:22
[517]=516,	-- depth:23
[518]=517,	-- depth:24
[519]=518,	-- depth:25
[520]=519,	-- depth:26
[522]=672,	-- depth:20
[523]=522,	-- depth:21
[524]=523,	-- depth:22
[525]=524,	-- depth:23
[526]=525,	-- depth:24
[544]=543,	-- depth:33
[527]=526,	-- depth:25
[529]=527,	-- depth:26
[530]=529,	-- depth:27
[532]=682,	-- depth:49
[533]=532,	-- depth:50
[534]=533,	-- depth:51
[535]=534,	-- depth:52
[536]=535,	-- depth:53
[537]=536,	-- depth:54
[538]=537,	-- depth:55
[539]=538,	-- depth:56
[540]=539,	-- depth:57
[542]=544,	-- depth:34
[528]=530,	-- depth:28
[1200]=450,	-- depth:40
},
card={
{param2=2,icon=1,},
{level=2,param1=20,name="时间 2级",desc="增加20秒",},
{level=3,param1=30,name="时间 3级",desc="增加30秒",},
{level=4,param1=40,name="时间 4级",desc="增加40秒",},
{level=5,param1=50,name="时间 5级",desc="增加50秒",},
{seq=1,type=7,param1=3,name="变身 1级",desc="变身减少3秒",},
{level=2,param1=5,name="变身 2级",desc="变身减少5秒",},
{level=3,param1=8,name="变身 3级",desc="变身减少8秒",},
{seq=1,type=7,level=4,name="变身 4级",desc="变身减少10秒",},
{level=5,param1=20,name="变身 5级",desc="变身减少20秒",},
{seq=2,type=1,param1=300,name="buff 1级",desc="获得1级buff",},
{level=2,param1=301,name="buff 2级",desc="获得2级buff",},
{level=3,param1=302,name="buff 3级",desc="获得3级buff",},
{level=4,param1=303,name="buff 4级",desc="获得4级buff",},
{level=5,param1=304,name="buff 5级",desc="获得5级buff",}
},

card_meta_table_map={
[2]=1,	-- depth:1
[3]=2,	-- depth:2
[4]=2,	-- depth:2
[5]=2,	-- depth:2
[7]=9,	-- depth:1
[8]=9,	-- depth:1
[10]=9,	-- depth:1
[12]=11,	-- depth:1
[13]=11,	-- depth:1
[14]=11,	-- depth:1
[15]=11,	-- depth:1
},
other_default_table={open_level=165,scene_id=9700,},

level_default_table={level=1,need_level=300,need_cap=5000000,auto_need_cap=200000000,hurt_decay=1000,record_index=0,pass_reward_item={[0]=item_table[5],[1]=item_table[6]},card_pool=0,skill_id=504,need_skill_power=100,building_img=1,stage_name="一转大陆",},

wave_default_table={level=1,wave=1,wave_time=15,pass_reward_item={[0]=item_table[7]},std_exp=100,building_img=1,name="黑暗丘凌",},

monster_default_table={level=1,wave=1,seq=0,monster_id=6705,monster_num=1,kill_skill_power=100,},

card_default_table={index=0,seq=0,type=0,level=1,param1=10,param2=0,param3=0,param4=0,icon=3,name="时间 1级",desc="增加10秒",}

}

