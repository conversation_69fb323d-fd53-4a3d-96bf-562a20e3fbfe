LimitedTimeOfferWGData = LimitedTimeOfferWGData or BaseClass()

function LimitedTimeOfferWGData:__init()
    if LimitedTimeOfferWGData.Instance then
		ErrorLog("[LimitedTimeOfferWGData] attempt to create singleton twice!")
		return
	end
    LimitedTimeOfferWGData.Instance = self
    local cfg = ConfigManager.Instance:GetAutoConfig("timelimitdiscountcfg_auto")
    self.reward_item_list = cfg.reward_cfg
    self.drop_reward_cfg = cfg.drop_reward_cfg
    self.other_cfg = cfg.other_cfg[1]
    self.extra_reward_cfg = ListToMap(cfg.extra_reward, "grade")

    self.cur_buy_times_list = {}
    self.is_buy_one = 0

    RemindManager.Instance:Register(RemindName.RemindLimitedTimeOffer, BindTool.Bind(self.GetRemind, self))
end

function LimitedTimeOfferWGData:__delete()
    LimitedTimeOfferWGData.Instance = nil
    self.item_cfg_list = nil
    RemindManager.Instance:UnRegister(RemindName.RemindLimitedTimeOffer)
end

function LimitedTimeOfferWGData:GetRemind()
    if self:GetCanGetFreeItem() then
        return 1
    end

    if self:GetCanGetExtraItem() then
        return 1
    end

    return 0
end

function LimitedTimeOfferWGData:GetOtherCfg()
    return self.other_cfg
end

function LimitedTimeOfferWGData:GetItemShowList(is_reset)
    self.item_cfg_list = {}
    local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    for k, v in pairs(self.reward_item_list) do
        if cur_day >= v.open_begine_day and cur_day <= v.close_end_day then
            local temp = {}
            temp.rmb_seq = v.rmb_seq
            temp.rmb_type = v.rmb_type
            temp.rmb_price = v.rmb_price
            temp.open_level = v.open_level
            temp.gift_name = v.gift_name
            temp.reward_list = v.reward_list
            temp.buy_limit = v.buy_limit
            temp.return_gold = v.return_gold
            temp.price_show = v.price_show
            temp.gold_show = v.gold_show
            temp.grade = v.grade
            temp.index = k
            local drop_cfg = self:GetDropCfgBySeq(v.rmb_seq)
            temp.reward_item = drop_cfg.reward_item
            temp.drop_per = drop_cfg.drop_per
            self.item_cfg_list[#self.item_cfg_list+1] = temp
        end
    end

    local list = {}
    local level = RoleWGData.Instance:GetRoleLevel()
    for k, v in pairs(self.item_cfg_list) do
        v.cur_buy_times = self.cur_buy_times_list[v.rmb_seq] or 0
        v.is_buy_all = (v.buy_limit - v.cur_buy_times > 0) and 0 or 1
        if level >= v.open_level then
            list[#list + 1] = v
        end
    end

    table.sort(list, SortTools.KeyLowerSorters("index"))
    return list
end

function LimitedTimeOfferWGData:GetExtraItemList()
    local grade = 1
    local list = self:GetItemShowList()
    if not IsEmptyTable(list) then
        if list[1] and list[1].grade then
            grade = list[1].grade
        end
    end

    local extra_list = self.extra_reward_cfg[grade].reward_item
    local list = {}
    if extra_list ~= nil then
        for k, v in pairs(extra_list) do
            table.insert(list, v)
        end
    end

    return list
end

function LimitedTimeOfferWGData:SetActData(protocol)
    self.day_reward_flag = protocol.day_reward_flag
    self.cur_buy_times_list = protocol.gift_list
    self.is_get_extra_reward = protocol.is_get_extra_reward
    self.is_buy_one = protocol.is_buy_one
end

function LimitedTimeOfferWGData:GetCanGetFreeItem()
    return self.day_reward_flag ~= 1
end

function LimitedTimeOfferWGData:IsGetExtraItem()
    return self.is_get_extra_reward == 1
end

function LimitedTimeOfferWGData:GetCanGetExtraItem()
    return self:GetRemainCanBuyPrice() <= 0 and self.is_get_extra_reward ~= 1
end

function LimitedTimeOfferWGData:GetRemainCanBuyPrice()
    local list = self:GetItemShowList()
    local money = 0
    for k, v in pairs(list) do
        local remain_count = v.buy_limit - v.cur_buy_times
        if remain_count > 0 then
            money = money + remain_count * v.rmb_price
        end
    end

    return money
end

function LimitedTimeOfferWGData:GetDropCfgBySeq(rmb_seq)
    for k, v in pairs(self.drop_reward_cfg) do 
        if rmb_seq == v.rmb_seq then
            return v
        end
    end
    return {}
end

function LimitedTimeOfferWGData:GetCurAllBuyCfg()
    local list = self:GetItemShowList()
    local all_buy_cfg = ConfigManager.Instance:GetAutoConfig("timelimitdiscountcfg_auto").buy_all
    local grade = 1

    if list[1] and list[1].grade then
        grade = list[1].grade
    end

    local is_empty_table = IsEmptyTable(all_buy_cfg)

    return is_empty_table and {} or all_buy_cfg[grade]
end

function LimitedTimeOfferWGData:CanAllBuy()
    local list = self:GetItemShowList()

    for k, v in pairs(list) do
        if v.cur_buy_times >= v.buy_limit then
            return false
        end
    end

    return true
end

function LimitedTimeOfferWGData:IsCanGetExtraItem()
    local list = self:GetItemShowList()
    local buy_num = 0

    for k, v in pairs(list) do
        if v.cur_buy_times >= 1 then
            buy_num = buy_num + 1
        end
    end

    return buy_num == #list
end

function LimitedTimeOfferWGData:GetAllBuyTimes()
    local list = self:GetItemShowList()
    local buy_num = 5

    for k, v in pairs(list) do
        buy_num = math.min(v.buy_limit - v.cur_buy_times, buy_num)
    end

    return buy_num
end