--------------------------------------------------
--充值数据管理
--<AUTHOR>
--------------------------------------------------
RechargeWGData = RechargeWGData or BaseClass()

-- 投资卡类型
INVEST_CARD_TYPE = {
	StorehouseCard = 0,		-- VIP投资
	MonthCard = 1,			-- 月卡
	SpreadMonthCard = 2,			-- 差价月卡
}

INVEST_CARD_TIPS = {
	None = 0,
	HasReward = 1,		-- 有奖励可领取
	GetReward = 2,		-- 已领取奖励
	PassDay = 3,		-- 奖励已过期
	NoInvest = 4,		-- 未投资
	NoDay = 5,			-- 天数未到
	Reissuance = 6,		-- 补领奖励
}

INVESTMENT_TOGGLE_ITEM_POS = {
	[1] = {item_position = Vector3(100, -40, 0),item_rotation = Vector3(0, 0, 0),},
	[2] = {item_position = Vector3(100, -116, 0),item_rotation = Vector3(0, 0, 0),},
	[3] = {item_position = Vector3(100, -192, 0),item_rotation = Vector3(0, 0, 0),}
}

RechargeWGData.MAX_RESERVE_REWARD_NUM = 5
RechargeWGData.MAX_RESERVE_ONLINE_NUM = 6

RechargeWGData.IsOpenRecharge = GLOBAL_CONFIG.param_list.switch_list.open_chongzhi


function RechargeWGData:__init()
	if RechargeWGData.Instance then
		ErrorLog("[RechargeWGData] Attemp to create a singleton twice !")
	end
	RechargeWGData.Instance = self
	self:InitParam()
	self:InitCfg()
	self:RegisterRemind()
  	self.bind_day_pass = GlobalEventSystem:Bind(OtherEventType.PASS_DAY, BindTool.Bind(self.DayChange, self))
end

function RechargeWGData:__delete()
	RechargeWGData.Instance = nil
	self:UnRegisterRemind()
	if self.bind_day_pass then
		GlobalEventSystem:UnBind(self.bind_day_pass)
		self.bind_day_pass = nil
	end

	self.is_cross_challenge_skip = nil
end

function RechargeWGData:InitParam()
	self.first_login = true
	self.history_recharge = 0
	self.history_recharge_count = 0
	self.today_recharge = 0
	self.recharge_change_callback_list = {}
	self.first_reward_list = {}				--首冲标记列表
	self.mouth_card_list = {}
	self.week_buy_all_info = {}
	self.invest_card_info_list = {}
	self.vip_zerobuy_info = nil
	self.use_relive_count = 0
	self.virtual_gold_daily_get_info = {}
	self.virtual_gold_daily_use_info = {}
	self.rmb_discount_used_cd_list = {}
	self.virtual_gold_limit_item_add_info = {}
	self.virtual_gold_2_limit_time_limited = {}
	self.kill_boss_virtual_gold_2_limit_time_limited = {}
	self.change_gold_daily_use_val = 0
end

function RechargeWGData:InitCfg()
	self.touzijihua_cfg = ConfigManager.Instance:GetAutoConfig("touzijihua_auto")
	self.week_buy_other = ConfigManager.Instance:GetAutoConfig("period_cfg_auto").other[1]
	self.invest_plan_cfg = ListToMap(self.touzijihua_cfg.plan_type, "type", "grade")
	self.tz_card_cfg = self.touzijihua_cfg.invest_card 										-- 专享月卡
	self.month_card_cfg = self.touzijihua_cfg.invest_card[1]								-- 尊享月卡
	self.tz_card_reward_cfg = ListToMapList(self.touzijihua_cfg.invest_card_reward, "card_type")
	local is_enforce_cfg = GLOBAL_CONFIG.param_list.is_enforce_cfg -- 强制用1安卓的配置 2IOS配置
	if is_enforce_cfg == 2 then
		self.recharge_cfg_list = ConfigManager.Instance:GetAutoConfig("rechargeappstore_auto").recharge_list
		self.chongzhireward = ConfigManager.Instance:GetAutoConfig("chongzhirewardappstore_auto").reward
		self.reward_chongzhi_key_cfg = self:CacheCfgByKey(self.chongzhireward, "chongzhi")
	else
		self.recharge_cfg_list = ConfigManager.Instance:GetAutoConfig("recharge_auto").recharge_list
		self.chongzhireward = ConfigManager.Instance:GetAutoConfig("chongzhireward_auto").reward
		self.reward_chongzhi_key_cfg = self:CacheCfgByKey(self.chongzhireward, "chongzhi")
	end

	local tongpiao_cfg = ConfigManager.Instance:GetAutoConfig("tongpiao_auto")
	self.tongpiao_shop_cfg = tongpiao_cfg.reward

	-- 直购对照表
	local zhigou_list_cfg = ConfigManager.Instance:GetAutoConfig("zhigou_list_cfg_auto")
	self.recharge_map_map_cfg = ListToMap(zhigou_list_cfg.rechage_map, "rmb_type", "rmb_seq", "RMB")

	self.rmb_discount_cfg = ConfigManager.Instance:GetAutoConfig("rmb_discount_cfg_auto").discount
	
	-- 审核服下读取SDK传过来的档位配置
	local is_audit_android = GLOBAL_CONFIG.param_list.is_audit_android == 1
	if IS_AUDIT_VERSION or is_audit_android then
		if GLOBAL_CONFIG.param_list.chongzhi_data and type(GLOBAL_CONFIG.param_list.chongzhi_data) == "string" then
			local chongzhi_sdk_cfg = cjson.decode(GLOBAL_CONFIG.param_list.chongzhi_data)
			if chongzhi_sdk_cfg and chongzhi_sdk_cfg.chongzhi_data then
				self.recharge_cfg_list = {}
			  	for i,v in pairs(chongzhi_sdk_cfg.chongzhi_data) do
			  		local index = tonumber(i)
			  		local cfg = {}
			  		cfg.id = index + 1
			  		cfg.money = tonumber(v.money) or 0
			  		cfg.gold_icon = v.gold_icon or "a2_vip_box1"
			  		cfg.gold = tonumber(v.gold) or 0
			  		cfg.first_r_reward_icon = v.first_r_reward_icon or ""
			  		cfg.show_overbalance = tonumber(v.show_overbalance) or 0
			  		cfg.show_word = v.show_word or ""
			  		cfg.show_ui = tonumber(v.show_ui) or 1
			  		self.recharge_cfg_list[index + 1] = cfg
			  	end
			end
		end
	end

	self:InitInvestPlanRewardCfg()
	self:InitWeekCardCfg()
	self:InitReserveCardCfg()
	self:InitKingVipCfg()
end

function RechargeWGData:CacheCfgByKey(cfg, key)
	if not cfg then
		return nil
	end

	local list = {}
	for k, v in pairs(cfg) do
		list[v[key]] = v
	end

	return list
end

function RechargeWGData:InitInvestPlanRewardCfg()
	local data_list = {}
	local cfg_list = self.touzijihua_cfg.plan_reward
	if cfg_list then
		local function split_gold(data)
			local temp = {}
			for k,v in pairs(data) do
				temp[k] = v
			end
			temp.reward_gold_bind = Split(data.reward_gold_bind, "|")
			return temp
		end
		local temp_list = nil
		for i,v in ipairs(cfg_list) do
			temp_list = data_list[v.type] or {}
			temp_list[#temp_list + 1] = split_gold(v)
			data_list[v.type] = temp_list
		end
	end
	self.invest_plan_reward_cfg = data_list
end

function RechargeWGData:RegisterRemind()
	RemindManager.Instance:Register(RemindName.Vip, BindTool.Bind(self.IsShowVipRedPoint, self))--VIP主界面按钮
	RemindManager.Instance:Register(RemindName.Vip_ZeroBuy, BindTool.Bind(self.IsShowVipZeroBuyRemind, self))--VIP零元购
	RemindManager.Instance:Register(RemindName.Vip_Col, BindTool.Bind(self.IsShowVipColRedPoint, self))--VIP贵族
	RemindManager.Instance:Register(RemindName.Vip_VTZ, BindTool.Bind(self.IsShowInvestCardRemind, self, INVEST_CARD_TYPE.StorehouseCard))--VIP投资
	RemindManager.Instance:Register(RemindName.Vip_Month, BindTool.Bind(self.IsShowInvestCardRemind, self, INVEST_CARD_TYPE.MonthCard))--VIP月卡投资
	RemindManager.Instance:Register(RemindName.Vip_TQTZ, BindTool.Bind(self.IsShowTouZiPlanRedPoint, self))--VIP特权投资
	-- RemindManager.Instance:Register(RemindName.Week_Buy, BindTool.Bind(self.IsShowWeekBuyRedPoint, self))--每周必买
	RemindManager.Instance:Register(RemindName.MonthCardProvolege, BindTool.Bind(self.IsShowMonthCardProvolege, self))
	RemindManager.Instance:Register(RemindName.Vip_Week, BindTool.Bind(self.IsShowWeekCardRemind, self))
	RemindManager.Instance:Register(RemindName.Reserve_Zftq, BindTool.Bind(self.IsZFShowCardRemind, self))
	RemindManager.Instance:Register(RemindName.Vip_King, BindTool.Bind(self.IsShowKingVipRemind, self))
end

function RechargeWGData:UnRegisterRemind()
	RemindManager.Instance:UnRegister(RemindName.Vip)
	RemindManager.Instance:UnRegister(RemindName.Vip_ZeroBuy)
	RemindManager.Instance:UnRegister(RemindName.Vip_Col)
	RemindManager.Instance:UnRegister(RemindName.Vip_VTZ)
	RemindManager.Instance:UnRegister(RemindName.Vip_Month)
	RemindManager.Instance:UnRegister(RemindName.Vip_TQTZ)
	-- RemindManager.Instance:UnRegister(RemindName.Week_Buy)
	RemindManager.Instance:UnRegister(RemindName.MonthCardProvolege)
	RemindManager.Instance:UnRegister(RemindName.Vip_Week)
	RemindManager.Instance:UnRegister(RemindName.Reserve_Zftq)
	RemindManager.Instance:UnRegister(RemindName.Vip_King)
end

function RechargeWGData:DayChange()
	self:FlushInvestCardInfo()
	RemindManager.Instance:Fire(RemindName.Vip)--VIP主界面按钮
end

--保存充值数据
function RechargeWGData:SetRechargeData(protocol)
	self.history_recharge = protocol.history_recharge
	self.history_recharge_count = protocol.history_recharge_count
	self.today_recharge = protocol.today_recharge
	self:SetFirstRewardList(protocol.reward_flag)

	for k,v in pairs(self.recharge_change_callback_list) do
		v()
	end
end

-- 获取当天充值
function RechargeWGData:GetTodayRecharge()
	return self.today_recharge or 0
end

-- 玩家累充
function RechargeWGData:GetHistoryRecharge()
	return self.history_recharge or 0
end

function RechargeWGData:GetHistoryRechargeCount()
	return self.history_recharge_count or 0
end

function RechargeWGData:SetRealChongZhiRmbInfo(protocol)
	self.real_chongzhi_rmb = protocol.real_chongzhi_rmb
end

function RechargeWGData:SetTodayRealChongZhiRmbInfo(protocol)
	self.today_real_chongzhi_rmb = protocol.today_real_chongzhi_rmb
end

function RechargeWGData:GetRealChongZhiRmb()
	return self.real_chongzhi_rmb or 0
end

function RechargeWGData:GetToDayRealChongZhiRmb()
	return self.today_real_chongzhi_rmb or 0
end

--观察充值数据的改变
function RechargeWGData:NotifyRechargeChange(callback)
	for k,v in pairs(self.recharge_change_callback_list) do
		if v == callback then
			return
		end
	end

	self.recharge_change_callback_list[#self.recharge_change_callback_list + 1] = callback
end

-- 设置首冲标记
function RechargeWGData:SetFirstRewardList(value)
	if nil == value then
		return
	end

	local reward_list = bit:d2b(value)
	self.first_reward_list = {}
	for i = 0, #self.chongzhireward do
		local flag = (0 == reward_list[32 - i])
		self.first_reward_list[i] = flag
	end
end

function RechargeWGData:GetExtraReward(gold)
	return self.reward_chongzhi_key_cfg[gold]
end

-- 转换成后端用的索引
function RechargeWGData:GetServerRechargeIndexByGold(gold)
	local reward_cfg = self:GetExtraReward(gold)
	return reward_cfg and reward_cfg.seq or -1
end

--获取首冲标记
function RechargeWGData:GetIsFirstReward(gold)
	if IS_AUDIT_VERSION then
		return false
	end

	local seq = self:GetServerRechargeIndexByGold(gold)
	return self.first_reward_list[seq]
end

function RechargeWGData:GetShowList()
	local list = {}
	for k,v in pairs(self.recharge_cfg_list) do
		if v.show_ui == 1 then
			table.insert(list, v)
		end
	end

	local function Sorters(sort_key_name1)
		return function(a, b)
			local order_a = 100
			local order_b = 100
			if a[sort_key_name1] < b[sort_key_name1] then
				order_a = order_a + 1
			elseif a[sort_key_name1] > b[sort_key_name1] then
				order_b = order_b + 1
			end

			return order_a > order_b
		end
	end

	table.sort(list, Sorters("money"))
	return list
end

function RechargeWGData:GetRechargeList()
	return self.recharge_cfg_list
end

-- 获取remder数据
function RechargeWGData:GetData(child)
	if nil == child then
		return
	end

	return self.recharge_cfg_list[child.id]
end

--获取通票配置.
function RechargeWGData:GetTongPiaoCfg()
	return self.tongpiao_shop_cfg
end

--获取vip 等级描述
function RechargeWGData:GetRechargeVipLevelDesc(vip_level)
	local level_config = ConfigManager.Instance:GetAutoConfig("vip_auto").uplevel
	local describe = level_config[vip_level + 1].desc
	local desc = Split(describe, "\n")
	return desc
end

--获取vip 等级特权描述
function RechargeWGData:GetRechargeVipDesc(vip_level)
	local level_config = ConfigManager.Instance:GetAutoConfig("vip_auto").uplevel
	local describe1 = level_config[vip_level + 1].desc1
	return describe1
end

function RechargeWGData:GetLoginCardRemindNum()
	return (-1 == self.mouth_card_list.card_0_day_index or -1 == self.mouth_card_list.card_1_day_index) and TimeWGCtrl.Instance:GetCurOpenServerDay() <= 7 and RoleWGData.Instance:GetDayFirstLoginFlag()
end

function RechargeWGData:SetLoginCardRemindNum(flag)
	self.first_login = flag
end

--红点判断
--VIP主界面按钮
function RechargeWGData:IsShowVipRedPoint()
	-- VIP零元购
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowVipZeroBuyRemind() then
		return 1
	end

	-- 贵族红点
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowVipColRedPoint() then
		return 1
	end

	-- VIP投资
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowInvestCardRemind(INVEST_CARD_TYPE.StorehouseCard) then
		return 1
	end

	-- VIP月卡投资
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowInvestCardRemind(INVEST_CARD_TYPE.MonthCard) then
		return 1
	end

	-- 特权投资
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowTouZiPlanRedPoint() then
		return 1
	end

	-- -- 每周必买
	-- if ShowRedPoint.SHOW_RED_POINT == self:IsShowWeekBuyRedPoint() then
	-- 	return 1
	-- end

	return 0
end

--月卡特权红点
function RechargeWGData:IsShowMonthCardProvolege()
	-- VIP投资
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowInvestCardRemind(INVEST_CARD_TYPE.StorehouseCard) then
		return 1
	end

	-- VIP月卡投资
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowInvestCardRemind(INVEST_CARD_TYPE.MonthCard) then
		return 1
	end

	return 0
end

-- VIP零元购红点
function RechargeWGData:IsShowVipZeroBuyRemind()
	local info_list = self:GetVipZeroBuyInfo()
	local up_level = VipWGData.Instance:GetVIPZeroBuyCfg("arrive_level") or 0
	if not info_list then
		return 0
	end

	if up_level < 1 then
		return 0
	end

	if info_list.buy_time > 0 and info_list.reward_flag == 0 then
		return 1
	end
	if info_list.buy_time > 0 and info_list.return_flag == 0 then
		local need_day = VipWGData.Instance:GetVIPZeroBuyCfg("return_time") or 0
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		if open_day >= need_day then
			return 1
		end
	end
	return 0
end

-- VIP贵族
function RechargeWGData:IsShowVipColRedPoint()
	if VipWGData.Instance:CanGetDailyGiftReward() then
		return 1
	end

	if VipWGData.Instance:CanGetFreeCard() then
		return 1
	end

	return 0
end

-- 打印VIP贵族红点信息查bug用
function RechargeWGData:ShowVipColRedInfo()
	if VipWGData.Instance:CanGetDailyGiftReward() == 1 then
		print_error("每日礼包信息", VipWGData.Instance.vip_daily_gift_info)
		if VipWGData.Instance.vip_daily_gift_info then
			print_error(VipWGData.Instance.vip_daily_gift_info)
		end
	else
		print_error("每日礼包没有可领取,逻辑没问题")
	end
	if VipWGData.Instance:CanGetFreeCard() == 1 then
		print_error("有没有免费的卡可以领取", VipWGData:CanGetFreeCard())
		if VipWGData.Instance.f2_vip_crad_info then
			print_error(VipWGData.Instance.f2_vip_crad_info)
		end
	else
		print_error("免费的卡没有可领取,逻辑没问题")
	end
end

--VIP七天累充
function RechargeWGData:IsShowVipLeiChongRedPoint()
	if not FunOpen.Instance:GetFunIsOpened(FunName.ActOpenServerRecharge) or
	  not ServerActivityWGData.Instance:CheckOpenSevenChongzhi() then
		return 0
	end
	local consume_cfg = ServerActivityWGData.Instance:GetOpenGameActivityConfig().total_chongzhi

	local info = ServerActivityWGData.Instance:GetOpenServerData()
	if not IsEmptyTable(info) and info.oga_total_chongzhi_reward_fetch_flag then
		local flag_t = bit:d2b(info.oga_total_chongzhi_reward_fetch_flag)
		for k,v in pairs(consume_cfg) do
			if info.oga_total_chongzhi_num >= v.chongzhi_gold and flag_t[32 - v.seq] == 0 then
				return 1
			end
		end
	end

	return 0
end

function RechargeWGData:GetFirstRechargeValue()
	local cfg = ConfigManager.Instance:GetAutoConfig("firstchongzhirewardcfg_auto").first_chongzhi_reward
	return cfg[1].need_total_chongzhi
end

function RechargeWGData:GetIsFirstRecharge()
	local his = self:GetHistoryRecharge()
	local need = self:GetFirstRechargeValue()
	return his >= need
end

function RechargeWGData:IsShowWeekBuyRedPoint(no_need_today)
	local is_open = FunOpen.Instance:GetFunIsOpened(FunName.recharge_week_buy)
	if not is_open then
		return 0
	end

	local open_day = self:GetWeekBuyOpenDay()
	local cur_open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if cur_open_server_day < open_day then
		return 0
	end

	local flag_info = self:GetWeekBuyInfo()
	if flag_info.cur_day and flag_info.cur_day > 0 then
		local flag = bit:d2b(flag_info.fetch_flag)
		for i=1,flag_info.cur_day + 1 do
			if flag[33 - i] == 0 then
				return 1
			end
		end
	end
	if not no_need_today and not self:RemindToday() then
		return 1
	end
	return 0
end

--今日是否提醒过
function RechargeWGData:RemindToday()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local uid = RoleWGData.Instance:InCrossGetOriginUid()
	local name = uid.."weekbuy"
	local role_level = RoleWGData.Instance.role_vo.level
	local fun_list = ConfigManager.Instance:GetAutoConfig("funopen_auto").funopen_list
	local week_buy = false
	local remind_day = PlayerPrefsUtil.GetString(name) or 0
	if fun_list and fun_list.recharge_week_buy.task_level <= role_level then
		week_buy =  true
	end
	return tonumber(remind_day) == cur_day and week_buy
end

--本地保存今天提醒状态（用于remind局部单日提醒）
function RechargeWGData:SetWeekBuyRedPointMark()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local uid = RoleWGData.Instance:InCrossGetOriginUid()
	local name = uid.."weekbuy"
	PlayerPrefsUtil.SetString(name, cur_day)
end

--每周必买信息数据
function RechargeWGData:SetWeekBuyInfo(protocol)
	self.week_buy_all_info = protocol
end

function RechargeWGData:GetWeekBuyInfo()
	return self.week_buy_all_info or {}
end

function RechargeWGData:IsBuyWeekBuy()
	if IsEmptyTable(self.week_buy_all_info) then
		return false
	end
	if self.week_buy_all_info.cur_day <= 0 then
		return true
	end
	return false
end

-- 获取每周必买开服天数
function RechargeWGData:GetWeekBuyOpenDay()
	return self.week_buy_other and self.week_buy_other.openday_limit or 0
end

function RechargeWGData:GetBaseInfoWeekBuy()
	if IsEmptyTable(self.week_buy_all_info) then return {} end
	local chongzhireward = ConfigManager.Instance:GetAutoConfig("period_cfg_auto").period_reward
	local period_recharge = ConfigManager.Instance:GetAutoConfig("period_cfg_auto").period
	local index = self.week_buy_all_info.cur_period > 0 and self.week_buy_all_info.cur_period or 1
	local return_cfg = {}
	for k,v in pairs(period_recharge) do
		if index == v.period_seq then
			return_cfg[1] = v
			break
		end
	end

	for k,v in pairs(chongzhireward) do
		if v.period_seq == index then
			table.insert(return_cfg,v)
		end
	end
	return return_cfg
end

-- 获取VIP打开的默认页签
function RechargeWGData:GetVIPDefultIndex()
	-- VIP零元购 返利领取
	if self:IsShowVipZeroBuyRemind() == 1 then
		return TabIndex.recharge_zerobuy
	end

	-- 贵族 红点
	if self:IsShowVipColRedPoint() == 1 then
		return TabIndex.recharge_vip
	end

	if self:IsShowInvestCardRemind(INVEST_CARD_TYPE.StorehouseCard) == 1 then
		return TabIndex.recharge_month_card
	end

	if self:IsShowInvestCardRemind(INVEST_CARD_TYPE.MonthCard) == 1 then
		return TabIndex.recharge_month_card
	end

	-- 特权投资 可领取
	if self:IsShowTouZiPlanRedPoint() == 1 then
		return TabIndex.recharge_tqtz
	end

	-- -- 每周必买 可领取
	-- if self:IsShowWeekBuyRedPoint(true) == 1 then
	-- 	return TabIndex.recharge_week_buy
	-- end

	-- -- 每周必买 每日红点
	-- if FunOpen.Instance:GetFunIsOpened('recharge_week_buy') then
	-- 	local open_day = self:GetWeekBuyOpenDay()
	-- 	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	-- 	if cur_day >= open_day then
	-- 		if not self:RemindToday() then
	-- 			return TabIndex.recharge_week_buy
	-- 		end
	-- 	end
	-- end

	if not self:IsBuyVipZeroBuy() and self:HasVipZeroBuy() then
		local tab_index = VipWGData.Instance:GetVIPZeorBuyIsOpenJump()
		return tab_index
	end

	if ServerActivityWGData.Instance:IsShowDailyRed() == 1 then
		return TabIndex.recharge_leichong
	end

	if ServerActivityWGData.Instance:IsShowExpenseRed() == 1 then
		return TabIndex.recharge_xiaofei
	end

	return TabIndex.recharge_vip
end

--尊享月卡返利奖励
function RechargeWGData:GetZunXiangMonthCardCfg()
	return self.month_card_cfg
end

--专享月卡返利奖励
function RechargeWGData:GetZhuanXiangMonthCardCfg()
	return self.tz_card_cfg
end

---[[ F2 VIP投资 and 月卡投资
function RechargeWGData:GetTZCardCfg(card_type)
	if card_type then
		if card_type > 1 then
			return self.tz_card_cfg[card_type + 1]
		end

		if self.tz_card_cfg then
			for k,v in pairs(self.tz_card_cfg) do
				if v.card_type == card_type then
					return v
				end
			end
		end
	else
		return self.tz_card_cfg
	end
end

function RechargeWGData:GetTZCardRewardCfg(card_type, day)
	local cfg_list = self.tz_card_reward_cfg[card_type]
	if day and cfg_list then
		return cfg_list[day + 1]
	else
		return cfg_list
	end
end

function RechargeWGData:GetTZJHOtherCfg(key)
	local cfg_list = self.touzijihua_cfg.other[1]
	if key then
		return cfg_list and cfg_list[key]
	else
		return cfg_list
	end
end

function RechargeWGData:GetTZCardMaxDay(card_type)
	local card_cfg = self:GetTZCardRewardCfg(card_type)
	if card_cfg then
		return card_cfg[#card_cfg].day + 1
	end
	return 0
end

function RechargeWGData:GetTZCardSurplusDay(card_type)
	local card_info = self:GetInvestCardInfo(card_type)
	local max_day = self:GetTZCardMaxDay(card_type)
	if card_info then
		return max_day - card_info.cur_day
	end
	return 0
end

function RechargeWGData:CanActiveTZCard(card_type)
	local can_active = true
	local card_info = self:GetInvestCardInfo(card_type)
	local max_day = self:GetTZCardMaxDay(card_type)
	if card_info then
		can_active = card_info.buy_timestamp <= 0 or card_info.cur_day + 1 > max_day
	end
	return can_active
end

function RechargeWGData:IsActiveTZCard(card_type)
	return not self:CanActiveTZCard(card_type)
end

function RechargeWGData:IsShowInvestCardRemind(card_type)
	if not self:GetTZCardOpenState(card_type) then
		return 0
	end

	local card_info = self:GetInvestCardInfo(card_type)

	if IsEmptyTable(card_info) then
		return 0
	end

	if self:GetInvestCardEnterNeedRemind() then
		return 1
	end

	if card_type == INVEST_CARD_TYPE.StorehouseCard then
		local mc_info = self:GetInvestCardInfo(INVEST_CARD_TYPE.MonthCard)
		local __, mc_state = self:CanGetInvestCardReward(INVEST_CARD_TYPE.MonthCard, mc_info.cur_day)
		if mc_state ~= INVEST_CARD_TIPS.NoInvest then
			return 0
		end
	end

	local can_get_reward = self:CanGetInvestCardReward(card_type, card_info.cur_day)
	return can_get_reward and 1 or 0
end

-- 月卡没购买每日提醒
local invest_card_enter_remind = "invest_card_enter_remind"
function RechargeWGData:GetInvestCardEnterNeedRemind()
	local mc_card_info = self:GetInvestCardInfo(INVEST_CARD_TYPE.MonthCard)
	local sc_card_info = self:GetInvestCardInfo(INVEST_CARD_TYPE.StorehouseCard)

	if IsEmptyTable(mc_card_info) or IsEmptyTable(sc_card_info) then
		return false
	end

	local mc_reward_get, mc_state = self:CanGetInvestCardReward(INVEST_CARD_TYPE.MonthCard, mc_card_info.cur_day)
	local sc_reward_get, sc_state = self:CanGetInvestCardReward(INVEST_CARD_TYPE.StorehouseCard, mc_card_info.cur_day)
	if mc_state == INVEST_CARD_TIPS.NoInvest and sc_state == INVEST_CARD_TIPS.NoInvest then
		local uuid_str = RoleWGData.Instance:GetUUIDStr()
		local save_key = invest_card_enter_remind .. uuid_str
		if PlayerPrefsUtil.HasKey(save_key) then
			local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
			local save_day = PlayerPrefsUtil.GetInt(save_key)
			return open_day ~= save_day
		else
			return true
		end
	end

	return false
end

function RechargeWGData:SetInvestCardEnterNeedRemind()
	local uuid_str = RoleWGData.Instance:GetUUIDStr()
	local save_key = invest_card_enter_remind .. uuid_str
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	PlayerPrefsUtil.SetInt(save_key, open_day)
	RemindManager.Instance:Fire(RemindName.Vip_Month)
	RemindManager.Instance:Fire(RemindName.Vip_VTZ)
end

function RechargeWGData:SetInvestCardInfo(protocol)
	local data_list = {}
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	for i,card in ipairs(protocol.invest_card_list) do
		local cur_day = 0
		if card.buy_timestamp > 0 then
			local time_t = os.date("*t", card.buy_timestamp)
			local zero_time = os.time({year = time_t.year, month = time_t.month, day = time_t.day, hour = 0, min = 0, sec = 0})
			local time = server_time - zero_time
			local day = math.floor(time / (24 * 3600))
			cur_day = day % 30	--由原本30天到永久需要做一个取余操作，不然配置报空

			local card_cfg = self:GetTZCardCfg(i)
			if card_cfg and card_cfg.is_cross_challenge_skip == 1 then
				self.is_cross_challenge_skip = 1
			end
		end
		local card_info = {}
		card_info.cur_day = cur_day
		card_info.buy_timestamp = card.buy_timestamp
		card_info.fetch_reward_flag = bit:d2b_two(card.fetch_reward_flag)
		data_list[i] = card_info
	end
	self.invest_card_info_list = data_list
	self.use_relive_count = protocol.use_relive_count
end

--判断天梯战斗是否跳过
function RechargeWGData:GetIsCrossChallengeSkip()
	return self.is_cross_challenge_skip == 1
end

-- 0点跳天后刷新
function RechargeWGData:FlushInvestCardInfo()
	local invest_card_info_list = self.invest_card_info_list
	if not IsEmptyTable(invest_card_info_list) then
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		for i=1,#invest_card_info_list do
			local cur_day = invest_card_info_list[i].cur_day
			if invest_card_info_list[i].buy_timestamp > 0 then
				local time_t = os.date("*t", invest_card_info_list[i].buy_timestamp)
				local zero_time = os.time({year = time_t.year, month = time_t.month, day = time_t.day, hour = 0, min = 0, sec = 0})
				local time = server_time - zero_time
				local day = math.floor(time / (24 * 3600))
				cur_day = day % 30
			end
			invest_card_info_list[i].cur_day = cur_day
		end
		self.invest_card_info_list = invest_card_info_list
	end
	RechargeWGCtrl.Instance:Flush(TabIndex.recharge_month_card)
	RemindManager.Instance:Fire(RemindName.Vip_Month)
	RemindManager.Instance:Fire(RemindName.Vip_VTZ)
end

function RechargeWGData:GetInvestCardInfo(card_type)
	return self.invest_card_info_list[card_type + 1]
end

-- 能否领取投资卡奖励
function RechargeWGData:CanGetInvestCardReward(card_type, reward_day)
	local card_info_list = self:GetInvestCardInfo(card_type)
	if not card_info_list then
		return false, INVEST_CARD_TIPS.None
	end
	local max_day = self:GetTZCardMaxDay(card_type)

	if card_info_list.buy_timestamp <= 0 or card_info_list.cur_day + 1 > max_day then
		return false, INVEST_CARD_TIPS.NoInvest
	end

	if card_type and reward_day then
		local card_cfg = self:GetTZCardRewardCfg(card_type, reward_day)
		if card_cfg then
			if card_info_list.fetch_reward_flag[card_cfg.day] == 1 then
				return false, INVEST_CARD_TIPS.GetReward
			elseif card_cfg.day == card_info_list.cur_day then
				return true, INVEST_CARD_TIPS.HasReward
			elseif card_cfg.day < card_info_list.cur_day then
				if card_cfg.can_fetch_miss_reward == 1 then
					return true, INVEST_CARD_TIPS.Reissuance
				else
					return false, INVEST_CARD_TIPS.PassDay
				end
			else
				return false, INVEST_CARD_TIPS.NoDay
			end
		end
	elseif card_type then
		local card_cfg_list = self:GetTZCardRewardCfg(card_type)
		if card_cfg_list then
			local cur_day = card_info_list.cur_day
			local reward_flag = card_info_list.fetch_reward_flag
			for _,card in pairs(card_cfg_list) do
				if reward_flag[card.day] == 0 then
					if card.day == cur_day or (card.can_fetch_miss_reward == 1 and card.day < cur_day) then
						return true
					end
				end
			end
		end
	end
	return false, INVEST_CARD_TIPS.None
end

-- 是否购买了返利特权
function RechargeWGData:IsBuyInvestCard()
	local card_info_list1 = self:GetInvestCardInfo(INVEST_CARD_TYPE.MonthCard)
	local card_info_list2 = self:GetInvestCardInfo(INVEST_CARD_TYPE.StorehouseCard)
	if not card_info_list1 and not card_info_list2 then
		return false
	end

	local max_day1 = self:GetTZCardMaxDay(INVEST_CARD_TYPE.MonthCard)
	local max_day2 = self:GetTZCardMaxDay(INVEST_CARD_TYPE.StorehouseCard)

	if (card_info_list1.buy_timestamp <= 0 or card_info_list1.cur_day + 1 > max_day1) and
		(card_info_list2.buy_timestamp <= 0 or card_info_list2.cur_day + 1 > max_day2) then
		return false
	else
		return true
	end
end

-- 免费复活次数
function RechargeWGData:GetFreeFuHuoCount()
	local can_active = self:CanActiveTZCard(INVEST_CARD_TYPE.StorehouseCard)
	if can_active then
		return 0
	end
	local free_fuhuo_count = self:GetTZJHOtherCfg("free_relive_count") or 0
	return free_fuhuo_count - self.use_relive_count, free_fuhuo_count
end
--]]

---[[ F2特权投资
function RechargeWGData:GetTouZiPlanCfgList(plan_type, plan_grade)
	local cfg_list = self.invest_plan_cfg
	if not cfg_list then
		return
	end
	if plan_type and plan_grade then
		return cfg_list[plan_type] and cfg_list[plan_type][plan_grade]
	elseif plan_type then
		return cfg_list[plan_type]
	end
	return cfg_list
end

function RechargeWGData:GetTouZiPlanRewardCfgList(plan_type, seq)
	local reward_cfg_list = self.invest_plan_reward_cfg
	if not reward_cfg_list then
		return
	end
	if plan_type and seq then
		return reward_cfg_list[plan_type] and reward_cfg_list[plan_type][seq + 1]
	elseif plan_type then
		return reward_cfg_list[plan_type]
	end
end

function RechargeWGData:SetTouZiPlanSerData(protocol)
	local touzijihua_list = protocol.touzijihua_list
	local temp_list = {}
	for i,v in ipairs(touzijihua_list) do
		local temp = {}
		temp.cur_grade = v.cur_grade
		temp.cur_fetch_reward_flag = v.cur_fetch_reward_flag
		temp.old_grade_fetch_flag = v.old_grade_fetch_flag
		temp_list[i] = temp
	end
	self.touziplan_serdata_list = temp_list
end

function RechargeWGData:GetTouZiPlanSerData(plan_type)
	return self.touziplan_serdata_list and self.touziplan_serdata_list[plan_type + 1]
end

function RechargeWGData:GetTouZiPlanRewardInfo(plan_type, seq)
	local info_list = {cur_grade = 0, old_grade = 0, is_get = false}
	local ser_data = self:GetTouZiPlanSerData(plan_type)
	if not ser_data then
		return info_list
	end
	info_list.cur_grade = ser_data.cur_grade
	info_list.old_grade = ser_data.old_grade_fetch_flag[seq + 1] or 0
	info_list.is_get = ser_data.cur_fetch_reward_flag[seq] == 1
	return info_list
end

function RechargeWGData:IsShowTouZiPlanRedPoint()
	if not self.touziplan_serdata_list then
		return 0
	end

	local is_open = FunOpen.Instance:GetFunIsOpened('recharge_tqtz')
	if not is_open then
		return 0
	end

	for i=1,MAX_PLAN_COUNT do
		if self:GetTouZiPlanRedPoint(i - 1) then
			return 1
		end
	end

	if self:IsBuyAllMaxGradeTouZiPlan(true) then
		return 0
	end

	if self:IsShowTouZiPlanFakeRedPoint() then
		return 1
	end

	return 0
end

-- 投资特权每日登陆红点(骗玩家看看，策划需求)
function RechargeWGData:IsShowTouZiPlanFakeRedPoint()
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "TouZiPlan")
	local remind_day = PlayerPrefsUtil.GetInt(key)
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if cur_day ~= remind_day then
		return true
	end
end

function RechargeWGData:SetTouZiPlanFakeRedPoint()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "TouZiPlan")
	PlayerPrefsUtil.SetInt(key, cur_day)
end

function RechargeWGData:GetTouZiPlanRedPoint(plan_type, plan_grade)
	local ser_data = self:GetTouZiPlanSerData(plan_type)
	if not ser_data then
		return false
	end
	if ser_data.cur_grade > 0 and (not plan_grade or ser_data.cur_grade == plan_grade) then
		local role_level = RoleWGData.Instance:GetRoleLevel()
		local reward_cfg_list = self:GetTouZiPlanRewardCfgList(plan_type) or {}
		for i=1,#reward_cfg_list do
			if ser_data.cur_fetch_reward_flag[reward_cfg_list[i].seq] == 0 and role_level >= reward_cfg_list[i].need_level then
				return true
			end
		end
	end
	return false
end

function RechargeWGData:TouZiPlanIsAllFinish()
	for i=1,MAX_PLAN_COUNT do
		if not self:TouZiPlanIsFinish(i - 1, true) then
			return false
		end
	end
	return true
end

-- 某类型投资某档位的所有奖励领取完
function RechargeWGData:TouZiPlanIsFinish(plan_type, is_all)
	local ser_data = self:GetTouZiPlanSerData(plan_type)
	if not ser_data then
		return false
	end
	local cfg_list = self:GetTouZiPlanCfgList(plan_type)
	if not cfg_list or (is_all and ser_data.cur_grade < #cfg_list) then
		return false
	end
	local reward_cfg_list = self:GetTouZiPlanRewardCfgList(plan_type) or {}
	local cur_fetch_reward_flag = ser_data.cur_fetch_reward_flag
	for i=1,#reward_cfg_list do
		if cur_fetch_reward_flag[reward_cfg_list[i].seq] == 0 then
			return false
		end
	end
	return true
end

-- 是否购买过特权投资
function RechargeWGData:IsBuyTouZiPlan()
	if self.touziplan_serdata_list then
		for k,v in pairs(self.touziplan_serdata_list) do
			if v.cur_grade > 0 then
				return true
			end
		end
	end
	return false
end

-- 是否所有档位都购买过
function RechargeWGData:IsBuyAllTouZiPlan()
	if self.touziplan_serdata_list then
		for k,v in pairs(self.touziplan_serdata_list) do
			if v.cur_grade <= 0 then
				return false
			end
		end
	end
	return true
end

-- 是否所有档位的最高档都买了(加上等级开放判断)
function RechargeWGData:IsBuyAllMaxGradeTouZiPlan(need_check_level)
	local is_all_buy = true
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local cfg_list = nil
	local cfg_data = nil
	local ser_data = nil
	for i=1,MAX_PLAN_COUNT do
		ser_data = self:GetTouZiPlanSerData(i - 1)
		cfg_list = self:GetTouZiPlanCfgList(i - 1)
		cfg_data = cfg_list and cfg_list[1]
		if cfg_data and ser_data then
			if ser_data.cur_grade < #cfg_list then
				if need_check_level then
					if role_level >= cfg_data.min_level and role_level <= cfg_data.max_level then
						is_all_buy = false
						break
					end
				else
					is_all_buy = false
					break
				end
			end
		end
	end
	return is_all_buy
end

-- 是否所有档位的最高档都买了(加上等级开放判断,等级不足开放的阶段也当作买了)
function RechargeWGData:IsBuyAllMaxGradeTouZiPlanCheckLevel()
	local is_all_buy = true
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local cfg_list = nil
	local cfg_data = nil
	local ser_data = nil
	for i=1,MAX_PLAN_COUNT do
		ser_data = self:GetTouZiPlanSerData(i - 1)
		cfg_list = self:GetTouZiPlanCfgList(i - 1)
		cfg_data = cfg_list and cfg_list[1]
		if cfg_data and ser_data then
			if role_level >= cfg_data.min_level and role_level <= cfg_data.max_level then
				if ser_data.cur_grade < #cfg_list then
					is_all_buy = false
					break
				end
			end
		end
	end
	return is_all_buy
end
--]]

---[[ F2 VIP零元购
function RechargeWGData:SetVipZeroBuyInfo(protocol)
	local info_list = {}
	info_list.buy_time = protocol.buy_time
	info_list.return_flag = protocol.return_flag
	info_list.buy_day_count = protocol.buy_day_count
	info_list.is_price_diff = protocol.is_price_diff == 1
	info_list.reward_flag = protocol.reward_flag
	self.vip_zerobuy_info = info_list
end

function RechargeWGData:GetVipZeroBuyInfo()
	return self.vip_zerobuy_info
end

function RechargeWGData:IsBuyVipZeroBuy()
	local info_list = self:GetVipZeroBuyInfo()
	if info_list and info_list.buy_time > 0 then
		return true
	end
end

function RechargeWGData:CheckNeedOpenDaLian()
	local info_list = self:GetVipZeroBuyInfo()
	if info_list and info_list.buy_time > 0 then
		return false
	end
	local vip_level = VipWGData.Instance:GetVipLevel()
	local up_level = VipWGData.Instance:GetVIPZeroBuyCfg("arrive_level") or 0
	local is_forever = VipWGData.Instance:IsForeverVip()
	if vip_level >= up_level and is_forever then
		return false
	end
	return true
end

function RechargeWGData:HasVipZeroBuy()
	if IS_AUDIT_VERSION then
		return false
	end
	local info_list = self:GetVipZeroBuyInfo()
	if info_list and info_list.buy_time > 0 then
		return info_list.return_flag == 0 or info_list.reward_flag == 0
	end
	local vip_level = VipWGData.Instance:GetVipLevel()
	local up_level = VipWGData.Instance:GetVIPZeroBuyCfg("arrive_level") or 0
	local is_forever = VipWGData.Instance:IsForeverVip()
	if up_level > 0 and vip_level < up_level then
		return true
	end

	if vip_level >= up_level then -- and is_forever then
		return false
	end
	
	return true
end

function RechargeWGData:GetVipZeroBuyPrice()
	local price = VipWGData.Instance:GetVIPZeroBuyCfg("consume_count") or 0
	local info_list = self:GetVipZeroBuyInfo()
	if not info_list then
		return price
	end
	if info_list.is_price_diff then
		local sub_price = VipWGData.Instance:GetVIPZeroBuyCfg("vip_price") or 0
		price = price - sub_price
	elseif info_list.buy_time == 0 then
		local vip_level = VipWGData.Instance:GetVipLevel()
		local price_diff_level = VipWGData.Instance:GetVIPZeroBuyCfg("price_diff_level") or 0
		if vip_level >= price_diff_level then
			local sub_price = VipWGData.Instance:GetVIPZeroBuyCfg("vip_price") or 0
			price = price - sub_price
		end
	end
	return price
end
--]]

function RechargeWGData:GetRechargeMapCfg(rmb, rmb_type, rmb_seq)
	return ((self.recharge_map_map_cfg[rmb_type] or {})[rmb_seq] or {})[rmb]
end

-- 获取充值内购表里，与当前匹配的商品（向上取）
-- 要求配置表对应的商品，价格从低到高
function RechargeWGData:GetRechargeMapBestCfg(rmb, rmb_type, rmb_seq)
	rmb = rmb or 0
	rmb_type = rmb_type or 0
	local cfg = self:GetRechargeMapCfg(rmb, rmb_type, rmb_seq)
	if cfg then
		return cfg
	end

	local zhigou_list_cfg = ConfigManager.Instance:GetAutoConfig("zhigou_list_cfg_auto").rechage_map
	for k,v in ipairs(zhigou_list_cfg) do
		if v.rmb_type == rmb_type then
			if v.RMB >= rmb then
				return v
			end
		end
	end

	return nil
end


-- 代币数量
function RechargeWGData:GetReplaceCoinNum()
	-- return RoleWGData.Instance:GetAttr("replace_coin")
	local other_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto").other[1]
	local num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.virtual_gold_3_item_id)
	return num
end

-- 虚拟现金每日信息
function RechargeWGData:SetVirtualGoldDailyInfo(protocol)
	local type = VIRTUAL_GOLD_TYPE.RECHARGE_VOLUME
	local old_virtual_gold_daily_use_info = self.virtual_gold_daily_use_info[type]
	self.virtual_gold_daily_get_info = protocol.virtual_gold_daily_get_info
	self.virtual_gold_daily_use_info = protocol.virtual_gold_daily_use_info
	self.virtual_gold_limit_item_add_info = protocol.virtual_gold_limit_item_add_info
	self.virtual_gold_2_limit_time_limited = protocol.virtual_gold_2_limit_time_limited
	self.kill_boss_virtual_gold_2_limit_time_limited = protocol.kill_boss_virtual_gold_2_limit_time_limited

	if old_virtual_gold_daily_use_info and old_virtual_gold_daily_use_info ~= self.virtual_gold_daily_use_info[type] then
		self.change_gold_daily_use_val = self.virtual_gold_daily_use_info[type] - old_virtual_gold_daily_use_info
		RechargeVolumeWGCtrl.Instance:OpenAccountTipContent()
	end
end

-- 充值券改变的使用额度对应配置
function RechargeWGData:GetVirtualGoldUseChangeValCfg()
	local cfg = RechargeVolumeWGData.Instance:GetExchangeCfg()
	for k, v in pairs(cfg) do
		if v.consume_num == self.change_gold_daily_use_val then
			return v
		end
	end

	return nil
end

-- 充值券临时额度2(打boss的额度)
function RechargeWGData:GetVirtualGoldLimitTimeLimitedByBoss(type)
	return self.kill_boss_virtual_gold_2_limit_time_limited[type] or 0
end

-- 虚拟现金每日信息 增加上限额度
function RechargeWGData:GetVirtualGoldMaxAddNum(type)
	return self.virtual_gold_limit_item_add_info[type] or 0
end

-- 充值券临时额度
function RechargeWGData:GetVirtualGoldLimitTimeLimited(type)
	return self.virtual_gold_2_limit_time_limited[type] or 0
end

-- 虚拟现金每日信息 已获得额度
function RechargeWGData:GetVirtualGoldGetNum(type)
	return self.virtual_gold_daily_get_info[type] or 0
end

-- 虚拟现金每日信息 已使用额度
function RechargeWGData:GetVirtualGoldUseNum(type)
	return self.virtual_gold_daily_use_info[type] or 0
end

-- 虚拟现金 每日最大获得额度
function RechargeWGData:GetVirtualGoldMaxGetNum(type)
	if not type then
		return 0
	end

	local key = string.format("virtual_gold_%s_daily_get_limit", type)
	local other_cfg = ConfigManager.Instance:GetAutoConfig("recharge_volume_show_auto").other[1]
	return other_cfg[key] or 0
end

-- 虚拟现金 每日最大使用额度
function RechargeWGData:GetVirtualGoldMaxUseNum(type)
	if not type then
		return 0
	end

	-- 充值卷特权处理
	if type == VIRTUAL_GOLD_TYPE.RECHARGE_VOLUME then
		if self:IsHasWeekCard() then
			local grade = self.week_card_grade < 0 and 1 or self.week_card_grade + 1
			local data = self:GetWeekCardOtherCfg(grade)
			return data and data.buff_virtual_gold_2_daily_use_limit or 0
		end
	end

	local key = string.format("virtual_gold_%s_daily_use_limit", type)
	local other_cfg = ConfigManager.Instance:GetAutoConfig("recharge_volume_show_auto").other[1]
	return other_cfg[key] or 0
end

-- 虚拟现金 每日所有最大使用额度
function RechargeWGData:GetAllVirtualGoldMaxUseNum(type)
	local max_use_num = self:GetVirtualGoldMaxUseNum(type)
    local time_limit_num = self:GetVirtualGoldLimitTimeLimited(type)
    local max_add_num = self:GetVirtualGoldMaxAddNum(type)
	local all_num = max_use_num + time_limit_num + max_add_num
	return all_num
end

-------------------------------------周卡----------------------------------------
function RechargeWGData:InitWeekCardCfg()
	self.week_start_time = 0
    self.week_invalid_time = 0
	self.week_card_grade = -1
    self.daily_reward_flag = {}
	self.week_card_cfg = ConfigManager.Instance:GetAutoConfig("week_card_cfg_auto")
	self.week_card_grade_cfg = self.week_card_cfg.grade
	
end

function RechargeWGData:GetWeekCardCfg()
	return self.week_card_cfg
end

function RechargeWGData:SetWeekCardInfo(protocol)
	self.week_start_time = protocol.start_time
    self.week_invalid_time = protocol.invalid_time   -- 结束时间戳
    self.daily_reward_flag = bit:d2b_l2h(protocol.daily_reward_flag, nil, true)
	self.week_card_grade = protocol.grade
end

function RechargeWGData:GetWeekCardGrade()
	return self.week_card_grade
end

function RechargeWGData:GetWeekCardItemShow(rmb_seq)
	return (self.week_card_grade_cfg[rmb_seq] or {}).show_item or {}
end

function RechargeWGData:GetWeekCardAddition(rmb_seq)
	local data = (self.week_card_grade_cfg[rmb_seq] or {}).grade_addition or {}
	return Split(data, "|")
end

function RechargeWGData:GetWeekCardAdditionByIndex(idx)
	local addition_list = self:GetWeekCardAddition(self.week_card_grade + 1)
	for index, value in ipairs(addition_list) do
		local addition_data = Split(value, ",")
		local addition_idx = tonumber(addition_data[1])
		if addition_idx == idx then
			return addition_data[2] / 10000 --万分比.
		end
	end
	return 0
end

function RechargeWGData:GetWeekCardOpenOtherCfg()
	return self.week_card_grade_cfg
end

function RechargeWGData:IsShowWeekCardGrade(rmb_type)
	if self.week_card_grade < 0 then
		if rmb_type == 0 then
			return true
		end
	else
		local next_grade = self.week_card_grade + 1	--逐级开启配置
		local week_card_cfg = self.week_card_grade_cfg[rmb_type + 1]
	
		if IsEmptyTable(week_card_cfg) then
			return false
		end
	
		if week_card_cfg.rmb_seq >= self.week_card_grade and week_card_cfg.rmb_seq <= next_grade then
			return true
		end
	end

	return false
end

function RechargeWGData:GetWeekCardOtherCfg(grade)
	return self.week_card_grade_cfg[grade]
end

function RechargeWGData:GetWeekCardRewardFlag(day)
	return self.daily_reward_flag[day] == 1
end

function RechargeWGData:GetIsInfinite(grade)
	if self.week_card_grade_cfg[grade] then
		local day = self.week_card_grade_cfg[grade].week_card_day or 0
		return day > 7
	end

	return false
end

function RechargeWGData:GetWeekCardCurrentDay()
	local time = TimeWGCtrl.Instance:GetServerTime()
	local week_time = 7 * 24 * 3600
	local day_time = 24 * 3600
	local total_time = (time - self.week_start_time) % week_time
	local day = math.ceil(total_time / day_time)
	return day
end

function RechargeWGData:IsHasWeekCard()
	local time = TimeWGCtrl.Instance:GetServerTime()
	return self.week_invalid_time >= time
end

function RechargeWGData:GetWeekCardTaskDataList(rmb_seq)
	local data_list = {}
	local other_cfg = self:GetWeekCardOtherCfg(rmb_seq)
	local day_num = other_cfg.week_card_day

	for i = 1, day_num do
		local data = {}
		data.day = i
		data.item_list = i == 1 and other_cfg.week_card_first_day_reward_item or other_cfg.week_card_reward_item
		data_list[i] = data
	end

	return data_list
end

function RechargeWGData:GetWeekCardRemainTime()
	local remain_time = 0
	local time = TimeWGCtrl.Instance:GetServerTime()
	if self.week_invalid_time <= time then
		return remain_time
	end

	remain_time = self.week_invalid_time - time
	return remain_time > 0 and remain_time or 0
end

function RechargeWGData:IsShowWeekCardRemind()
	for k, v in pairs(self.week_card_grade_cfg) do
		if not self:GetWeekCardOpenState(v.rmb_seq + 1) then
			return 0
		end
	end

	if self:GetWeekCardToDayEnterNeedRemind() then
		return 1
	end

	local has_week_card = self:IsHasWeekCard()
	if not has_week_card then
		return 0
	end

	local day = self:GetWeekCardCurrentDay()
	local can_get_reward = self:GetWeekCardRewardFlag(day)

	if not can_get_reward then
		return 1
	end

	return 0
end

-- 周卡没购买每日提醒
local week_card_today_enter = "week_card_today_enter"
function RechargeWGData:GetWeekCardToDayEnterNeedRemind()
	local has_week_card = self:IsHasWeekCard()
	if not has_week_card then
		local uuid_str = RoleWGData.Instance:GetUUIDStr()
		local save_key = week_card_today_enter .. uuid_str
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		if PlayerPrefsUtil.HasKey(save_key) then
			local save_day = PlayerPrefsUtil.GetInt(save_key)
			return open_day ~= save_day
		else
			return true
		end
	end

	return false
end

function RechargeWGData:SetWeekCardToDayEnterNeedRemind()
	local uuid_str = RoleWGData.Instance:GetUUIDStr()
	local save_key = week_card_today_enter .. uuid_str
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	PlayerPrefsUtil.SetInt(save_key, open_day)
	RemindManager.Instance:Fire(RemindName.Vip_Week)
end

function RechargeWGData:GetWeekCardOpenState(rmb_seq)
	local fun_open = FunOpen.Instance:GetFunIsOpened(FunName.RechargeWeekcard)
	if not fun_open then
		return false
	end

	local week_card_open = false
	local cfg = self:GetWeekCardOtherCfg(rmb_seq)
	if cfg then
		week_card_open = cfg.open == 1
	end

	return week_card_open
end

function RechargeWGData:GetTZCardOpenState(card_type)
	local fun_name = card_type == INVEST_CARD_TYPE.StorehouseCard and FunName.VIPTouZi or FunName.RechargeMonthcard
	local fun_open = FunOpen.Instance:GetFunIsOpened(fun_name)

	if not fun_open then
		return false
	end

	local open = false
	local cfg = self:GetTZCardCfg(card_type)
	if not IsEmptyTable(cfg) then
		open = cfg.open == 1
	end

	return open
end

function RechargeWGData:GetRechargeVolumeShowOther(key)
	local cfg = ConfigManager.Instance:GetAutoConfig("recharge_volume_show_auto").other[1]
	return cfg and cfg[key]
end


function RechargeWGData:GetRechargeVolumeShowGetWay(seq)
	local cfg = ConfigManager.Instance:GetAutoConfig("recharge_volume_show_auto").get_way
	return cfg and cfg[seq]
end





function RechargeWGData:GetRmbDiscountCfg(item_id)
	return self.rmb_discount_cfg[item_id]
end

function RechargeWGData:GetRmbDiscountUsedCDList()
	return self.rmb_discount_used_cd_list
end

function RechargeWGData:GetRmbDiscountUsedCDNum(item_id)
	local data = self.rmb_discount_used_cd_list[item_id]
	return data and #data or 0
end

function RechargeWGData:GetRmbDiscountUsedCD(item_id, idx)
	local data = self.rmb_discount_used_cd_list[item_id]
	return (data or {})[idx] or 0
end

-- 设计原因：支付成功存在延迟(SDK + 后台 + 后端每5秒检查一次数据库），防止玩家重复使用券
function RechargeWGData:AddRmbDiscountUsedCD(item_id, time)
	local tab = self.rmb_discount_used_cd_list[item_id]
	if not tab then
		self.rmb_discount_used_cd_list[item_id] = {}
		tab = self.rmb_discount_used_cd_list[item_id]
	end

	table.insert(tab, time)
end

-- 存在缺陷：15s内任然未成功支付，会将另外的券限制CD移除，导致玩家重复使用券
function RechargeWGData:RemoveRmbDiscountUsedCD(item_id)
	local tab = self.rmb_discount_used_cd_list[item_id]
	if not tab or #tab == 0 then
		return
	end

	table.remove(tab, 1)
end

function RechargeWGData:UpdateRmbDiscountCD()
	local time = TimeWGCtrl.Instance:GetServerTime()
	for k,v in pairs(self.rmb_discount_used_cd_list) do
		for i = #v, 1, -1 do
			if time >= v[i] then
				table.remove(self.rmb_discount_used_cd_list[k], i)
			end
		end
	end
end

function RechargeWGData:GetRmbDiscountList(money)
	local list = {}
	local can_use_num = 0
	if money <= 0 then
		return list, can_use_num
	end

	local limit_cd = 0
	for k,v in pairs(self.rmb_discount_cfg) do
		local num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
		if num > 0 then
			if money >= v.discount_limit then
				can_use_num = can_use_num + num
			end

			local cd_num = self:GetRmbDiscountUsedCDNum(v.item_id)
			local discount_num = 0
			local discount_sub_num = 0
			if v.discount_type == 1 then
				discount_num = money - v.discount_value
				discount_sub_num = v.discount_value
			else
				discount_num = math.floor(money * v.discount_value / 100)
				discount_sub_num = math.floor((1 - v.discount_value / 100) * money)
			end

			for i = 1, num do
				local limit_sort = 0
				if i <= cd_num then
					limit_sort = 1000000 + (10 - i) * 100000
				elseif money >= v.discount_limit then
					limit_sort = 10000000
				end
				
				limit_cd = self:GetRmbDiscountUsedCD(v.item_id, i)
				local data = {
					cfg = v,
					limit_cd = limit_cd,
					limit_use = money < v.discount_limit,
					discount_num = discount_num,
					sort = limit_sort + discount_sub_num,
				}
				table.insert(list, data)
			end
		end
	end

	SortTools.SortDesc(list, "sort")
	return list, can_use_num
end

--------------------------------攒福特权----------------------------
function RechargeWGData:InitReserveCardCfg()
	self.zftq_reserve_level = -1
    self.zftq_final_reward_flag = {}
    self.zftq_online_reward_flag = {}
	self.zftq_mail_back_num = 0
	self.zftq_today_back_num = 0
	self.zftq_all_back_num = 0
	self.zftq_today_online_time = 0

	local reserve_card_cfg = ConfigManager.Instance:GetAutoConfig("zanfu_tequan_cfg_auto")
	self.zftq_cur_rmb_cfg = ListToMap(reserve_card_cfg.rmb, "level")
	self.zftq_all_back_cfg = ListToMap(reserve_card_cfg.back, "level")
	self.zftq_online_reward_cfg = ListToMapList(reserve_card_cfg.online_reward, "level")
	self.zftq_final_reward_cfg = ListToMapList(reserve_card_cfg.final_reward, "level")
end

function RechargeWGData:SetReserveCardInfo(protocol)
	self.zftq_reserve_level = protocol.level																		-- 特权等级
    self.zftq_final_reward_flag = bit:d2b_l2h(protocol.final_reward_flag, nil, true) 								-- 终生奖励领取标识
    self.zftq_online_reward_flag = bit:d2b_l2h(protocol.online_reward_flag, nil, true) 								-- 每日在线奖励领取标识
	self.zftq_mail_back_num = protocol.mail_back_num																-- 可领取或者跨天邮件返利领取灵玉
	self.zftq_today_back_num = protocol.today_back_num																-- 当日累计返利可领取灵玉数量
	self.zftq_all_back_num = protocol.all_life_get_back_num															-- 玩家这辈子领取过的特权返利灵玉数量
	self.zftq_today_online_time = protocol.today_online_time														-- 今日在线时长
	self.zftq_start_online_time = TimeWGCtrl.Instance:GetServerTime() - self.zftq_today_online_time					-- 功能开始时间
end

function RechargeWGData:GetZFOnlineRewardNextTime()
	local is_preview = self.zftq_reserve_level == 0
	local map_list = self:GetZFOnlineRewardCfg(is_preview)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local zftq_start_online_time = self:GetZFStartOnlineTime()
	local online_time = server_time - zftq_start_online_time

	for k,v in ipairs(map_list) do
		if online_time < v.online_second then
			return v.online_second - online_time
		end
	end
	return 0
end

function RechargeWGData:GetZFOnlineRewardShowList()
	local is_preview = self.zftq_reserve_level == 0
	local map_list = self:GetZFOnlineRewardCfg(is_preview)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local zftq_start_online_time = self:GetZFStartOnlineTime()
	local online_time = server_time - zftq_start_online_time
	local show_list = {}
	local ready_next_time = 0
	for k,v in ipairs(map_list) do
		local status = REWARD_STATE_TYPE.UNDONE
		local is_get = self:GetZFOnlineRewardFlag(v.seq)
		local is_can_get = online_time >= v.online_second
		local is_remind = false
		if is_can_get then
			if is_get then
				status = REWARD_STATE_TYPE.FINISH
			else
				is_remind = true
				status = REWARD_STATE_TYPE.CAN_FETCH
			end
		else
			if ready_next_time == 0 then
				ready_next_time = v.online_second - online_time
				status = REWARD_STATE_TYPE.READY
			else
				status = REWARD_STATE_TYPE.UNDONE
			end
		end

		local data = {
			status = status,								-- 状态
			is_remind = is_remind,							-- 红点
			ready_next_time = ready_next_time,				-- 下次的时间
			cfg = v,										-- 表数据
		}

		table.insert(show_list, data)
	end

	return show_list
end

function RechargeWGData:GetZFMaxLevel()
	local cfg = self.zftq_cur_rmb_cfg[#self.zftq_cur_rmb_cfg]
	return cfg and cfg.level or 0
end

function RechargeWGData:GetZFCurBuyCfg()
	return self.zftq_cur_rmb_cfg[self.zftq_reserve_level + 1] or {}
end

function RechargeWGData:GetZFBackCfg(is_next)
	local level = is_next and self.zftq_reserve_level + 1 or self.zftq_reserve_level
	local max_level = self:GetZFMaxLevel()
	local need_level = math.min(level, max_level)
	return self.zftq_all_back_cfg[need_level] or {}
end

function RechargeWGData:GetZFAllBackCfg()
	return self.zftq_all_back_cfg
end

function RechargeWGData:GetZFOnlineRewardCfg(is_preview)
	local level = is_preview and 1 or self.zftq_reserve_level
	return self.zftq_online_reward_cfg[level] or {}
end

function RechargeWGData:GetZFFinalRewardCfg(is_preview)
	local level = is_preview and 1 or self.zftq_reserve_level
	return self.zftq_final_reward_cfg[level] or {}
end

function RechargeWGData:GetZFAllFinalRewardCfg(level)
	return self.zftq_final_reward_cfg[level] or {}
end

function RechargeWGData:GetZFCurLevel()
	return self.zftq_reserve_level or 0
end

-- 获取返还奖励状态
function RechargeWGData:GetZFFinalRewardFlag(seq)
	return self.zftq_final_reward_flag[seq] or 0
end

-- 获取在线奖励状态
function RechargeWGData:GetZFOnlineRewardFlag(seq)
	return self.zftq_online_reward_flag[seq] == 1
end

function RechargeWGData:GetZFTodayBackNum()
	return self.zftq_today_back_num or 0
end

function RechargeWGData:GetZFMailBackNum()
	return self.zftq_mail_back_num or 0
end

function RechargeWGData:GetZFAllBackNum()
	return self.zftq_all_back_num or 0
end

function RechargeWGData:GetZFStartOnlineTime()
	return self.zftq_start_online_time or 0
end

function RechargeWGData:GetZFFinalRewardShowList()
	local is_preview = self.zftq_reserve_level == 0
	local cfg = self:GetZFFinalRewardCfg(is_preview)
	local show_list = {}
	if IsEmptyTable(cfg) then
		return show_list
	end

	local length = #cfg
	local last_data = cfg[length]
	for k, v in ipairs(cfg) do
		if #show_list > 3 then
			break
		end

		local is_last_4 = k >= length - 4
		if self:GetZFFinalRewardFlag(v.seq) == 0 or is_last_4 then
			table.insert(show_list, v)
		end
	end

	table.insert(show_list, last_data)

	return show_list
end

function RechargeWGData:GetZFOneGradeRewardList(reward_type)
	local temp_list = {}
	local cfg = self:GetZFAllFinalRewardCfg(reward_type)
	if IsEmptyTable(cfg) then
		return temp_list
	end

	for k, v in pairs(cfg) do
		for k1, v1 in pairs(v.reward_item) do
			table.insert(temp_list, v1)
		end
	end

	table.sort(temp_list,function (a, b)
		if a and b and a.item_id and b.item_id and a.item_id ~= b.item_id then
			local a_cfg = ItemWGData.Instance:GetItemConfig(a.item_id)
			local b_cfg = ItemWGData.Instance:GetItemConfig(b.item_id)
			if a_cfg and a_cfg.color and b_cfg and b_cfg.color then
				return a_cfg.color > b_cfg.color
			else
				return false
			end
		end
		return false
	end )

	return temp_list
end

function RechargeWGData:GetZFCurBXProgress()
	local cur_progress = 0
	local show_list = self:GetZFFinalRewardShowList()
	if not IsEmptyTable(show_list) and #show_list == RechargeWGData.MAX_RESERVE_REWARD_NUM then
		for k, v in ipairs(show_list) do
			if self.zftq_all_back_num >= v.back_num then
				cur_progress = cur_progress + 1
			end
		end
	end

	return cur_progress
end

--王者特权每日礼包.
function RechargeWGData:IsShowKingVipRemind()
	local is_open = FunOpen.Instance:GetFunIsOpened(FunName.RechargeKingVip)
	local is_get = self:GetKingVipIsCanaGetFreeBox()
	if is_open and is_get == 0 then
		return 1
	end

	return 0
end

function RechargeWGData:IsZFShowCardRemind()
	local is_buy = self:IsBuyReserveCard()
	if self:IsZFTQOnlineRemind() and is_buy then
		return 1
	end

	if self:IsZFTQFinalRemind() then
		return 1
	end

	if self:IsShowPreviewRemind() then
		return 1
	end

	local can_get_num = self:GetZFMailBackNum()
	if can_get_num > 0 then
		return 1
	end

	return 0
end

function RechargeWGData:IsZFTQFinalRemind()
	local cfg = self:GetZFFinalRewardCfg()
	if IsEmptyTable(cfg) then
		return false
	end

	local all_lift_num = self:GetZFAllBackNum()
	for k, v in pairs(cfg) do
		local is_get = self:GetZFFinalRewardFlag(v.seq)
		local need_num = v.back_num
		if all_lift_num >= need_num and is_get == 0 then
			return true
		end
	end
end

function RechargeWGData:IsZFTQOnlineRemind()
	local cfg = self:GetZFOnlineRewardShowList()
	if IsEmptyTable(cfg) then
		return false
	end

	for k, v in pairs(cfg) do
		if v.status == REWARD_STATE_TYPE.CAN_FETCH then
			return true
		end
	end
end

-- 判断是否购买攒福特权
function RechargeWGData:IsBuyReserveCard()
	return self.zftq_reserve_level > 0
end

-- 判断是否购买所有攒福特权
function RechargeWGData:IsBuyAllReserveCard()
	local max_level = self:GetZFMaxLevel()

	return self.zftq_reserve_level >= max_level
end

-- 红点每日上线显示一次
function RechargeWGData:IsShowPreviewRemind()
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "ShowPreview")
	local remind_day = PlayerPrefsUtil.GetInt(key)
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local is_zf_open = FunOpen.Instance:GetFunIsOpened(FunName.RechargeReservecard)
	if cur_day ~= remind_day and is_zf_open then
		return true
	end

	return false
end

function RechargeWGData:SetPreviewRemind()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "ShowPreview")
	PlayerPrefsUtil.SetInt(key, cur_day)
end

-- ====================================== 王者特权 ==================================================================
function RechargeWGData:InitKingVipCfg()
	self.king_vip_cfg = ConfigManager.Instance:GetAutoConfig("king_privilege_auto")
	self.king_vip_other = self.king_vip_cfg.other[1]

	self.king_vip_other_free = {}
	for i = 0, #self.king_vip_other.item do
		self.king_vip_other_free[i + 1] = self.king_vip_other.item[i]
	end

	self.king_vip_rmb_buy = self.king_vip_cfg.rmb_buy[0]

	--加成属性为万分比.
	self.king_vip_privilege_addition = ListToMap(self.king_vip_cfg.privilege_addition, "vip_level")

	self.king_vip_client_show = ListToMap(self.king_vip_cfg.client_show, "vip_add_name_index")

	self.king_vip_end_time = 0
	self.king_vip_is_get = 1

	self.king_vip_page_info = {}
	self.king_vip_page_info[1] = { start_idx = 0, end_idx = 10 }
	self.king_vip_page_info[2] = { start_idx = 11, end_idx = 21 }
	self.king_vip_page_info[3] = { start_idx = 22, end_idx = 30 }
end

function RechargeWGData:GetKingVipModel()
	return (self.king_vip_other.wings_model) or 0
end

function RechargeWGData:GetKingVipPrice()
	return (self.king_vip_rmb_buy.rmb_price) or 0
end

function RechargeWGData:GetKingVipRmbBuyCfg()
	return self.king_vip_rmb_buy
end

function RechargeWGData:GetKingVipFreeItem()
	return (self.king_vip_other_free) or {}
end

function RechargeWGData:GetKingVipAddition(level)
	for i = 0, #self.king_vip_privilege_addition do
		local vip_lv = self.king_vip_privilege_addition[i].vip_level
		if vip_lv == level then
			return self.king_vip_privilege_addition[i]
		end
	end

	return {}
end

--获取当前的王者特权百分比加成
function RechargeWGData:GetCurKingVipAddition()
    --判断功能开启.
	local is_fun_open = FunOpen.Instance:GetFunIsOpened(FunName.RechargeKingVip)
	if not is_fun_open then
		return {}
	end

	local cur_vip_lv = VipWGData.Instance:GetRoleVipLevel()
	local data = self:GetKingVipAddition(cur_vip_lv)
	if IsEmptyTable(data) then
		return {}
	end

	return data
end

function RechargeWGData:GetKingVipClientCfg()
	return self.king_vip_client_show
end

function RechargeWGData:SetKingVipInfo(protocol)
	self.king_vip_end_time = protocol.end_time              --特权失效时间.
	self.king_vip_is_get = protocol.is_fetch_daily_rewards --是否领取每日奖励 0:false, 1:true
end

function RechargeWGData:GetKingVipEndTime()
	return self.king_vip_end_time
end

function RechargeWGData:GetKingVipIsCanaGetFreeBox()
	return self.king_vip_is_get
end

--获取页数.
function RechargeWGData:GetKingVipPageInfo(idx)
	return self.king_vip_page_info[idx]
end
-- ====================================== 王者特权 end ==============================================================