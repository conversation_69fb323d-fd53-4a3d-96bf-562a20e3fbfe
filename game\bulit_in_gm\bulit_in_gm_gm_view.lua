function BulitInGMView:ReleaseBulitInGMView()
    self.cur_select_big_type = nil
    if self.gm_big_list then
		self.gm_big_list:DeleteMe()
		self.gm_big_list = nil
	end

    if self.gm_sub_list then
		self.gm_sub_list:DeleteMe()
		self.gm_sub_list = nil
	end
end

function BulitInGMView:InitBulitInGMView()
    self.cur_select_big_type = 0
    self.gm_big_list = AsyncListView.New(GMBigListRender, self.node_list.gm_big_list)
    self.gm_big_list:SetSelectCallBack(BindTool.Bind(self.OnSelectGMBigHandler, self))

    self.gm_sub_list = AsyncListView.New(GMSubListRender, self.node_list.gm_sub_list)
    self.gm_sub_list:SetSelectCallBack(BindTool.Bind(self.OnSelectGMSubHandler, self))

    XUI.AddClickEventListener(self.node_list.btn_gm_send, BindTool.Bind(self.OnClickGMSend, self))
    XUI.AddClickEventListener(self.node_list.btn_gm_send_close, BindTool.Bind(self.OnClickGMSendClose, self))
end

function BulitInGMView:FlushBulitInGMView()
    local cfg = BulitInGMData.Instance:GetBigTypeCfg()
    self.gm_big_list:SetDataList(cfg)
end

function BulitInGMView:OnSelectGMBigHandler(item)
    if not item or not item.data then
        return
    end
    
    local type = item.data.big_type
    if type == self.cur_select_big_type then
        return
    end

    self.cur_select_big_type = type
    local data = BulitInGMData.Instance:GetSubTypeCfgList(type)
    self.gm_sub_list:SetDataList(data)
    self.gm_sub_list:JumpToIndex(1)
end

function BulitInGMView:OnSelectGMSubHandler(item)
    if not item or not item.data then
        return
    end

    local str = string.format("/jy_%s %s", item.data.gm_type, item.data.gm_str)
    self.node_list.gm_input_field.input_field.text = str
end

function BulitInGMView:OnClickGMSend()
    local text = self.node_list.gm_input_field.input_field.text
    if text == "" then
        return
    end

    local len = string.len(text)
    if len >= 6 and string.sub(text, 1 , 6) == "/jy_gm" then
		local blank_begin, blank_end = string.find(text, " ")
		local colon_begin, colon_end = string.find(text, ":")
		if blank_begin and blank_end and colon_begin and colon_end then
			local type = string.sub(text, blank_end + 1, colon_begin - 1)
			local command = string.sub(text, colon_end + 1, -1)
			SysMsgWGCtrl.SendGmCommand(type, command)
		end

	elseif len >= 11 and string.sub(text, 1 , 11) == "/jy_crossgm" then
		local blank_begin, blank_end = string.find(text, " ")
		local colon_begin, colon_end = string.find(text, ":")
		if blank_begin and blank_end and colon_begin and colon_end then
			local type = string.sub(text, blank_end + 1, colon_begin - 1)
			local command = string.sub(text, colon_end + 1, -1)
			SysMsgWGCtrl.SendCrossGmCommand(type, command)
		end

	elseif len >= 7 and string.sub(text, 1 , 7) == "/jy_cmd" then
		local blank_begin, blank_end = string.find(text, " ")
		if blank_begin and blank_end then
			ClientCmdWGCtrl.Instance:Cmd(string.sub(text, blank_end + 1, len))
		end
    else
        TipWGCtrl.Instance:ShowSystemMsg("输入有误！！！")
	end
end

function BulitInGMView:OnClickGMSendClose()
    self:OnClickGMSend()
    self:Close()
end

















GMBigListRender = GMBigListRender or BaseClass(BaseRender)
function GMBigListRender:OnFlush()
    if self.data == nil then
        return
    end

    self.node_list["text_n"].text.text = self.data.name
    self.node_list["text_hl"].text.text = self.data.name
end

function GMBigListRender:OnSelectChange(is_select)
    self.node_list["img_n"]:SetActive(not is_select)
    self.node_list["img_hl"]:SetActive(is_select)
    self.node_list["text_n"]:SetActive(not is_select)
    self.node_list["text_hl"]:SetActive(is_select)
end









GMSubListRender = GMSubListRender or BaseClass(BaseRender)
function GMSubListRender:OnFlush()
    if self.data == nil then
        return
    end

    self.node_list["text_n"].text.text = self.data.name
    self.node_list["text_hl"].text.text = self.data.name
end

function GMSubListRender:OnSelectChange(is_select)
    self.node_list["img_n"]:SetActive(not is_select)
    self.node_list["img_hl"]:SetActive(is_select)
    self.node_list["text_n"]:SetActive(not is_select)
    self.node_list["text_hl"]:SetActive(is_select)
end