ZeroBuyBtn = ZeroBuyBtn or BaseClass(MainActivityBtn)

ZEROBUY_ACT_BTN = {
	SHOW_NEW = 1,
	CHANGE_STATUS = 2,
}

function ZeroBuyBtn:__init(instance)
	self.zero_btn_change = self:BindGlobalEvent(ACTIVITY_BTN_EVENT.ZEROBUY_BTN_CHANGE, BindTool.Bind(self.ButtonChange, self))
end

function ZeroBuyBtn:__delete()
	if self.zero_btn_change then
		GlobalEventSystem:UnBind(self.zero_btn_change)
		self.zero_btn_change = nil
	end

	self.act_id = nil
end

function ZeroBuyBtn:CreateBtnAsset(parent, activity_type)
	self:LoadAsset("uis/view/zero_buy_ui_prefab", "zero_buy_btn", parent.transform, function(obj)
		self.node_list.SpecialEff:SetActive(true)
		obj.name = "id:" .. activity_type
		self.act_id = activity_type
	end)
end

function ZeroBuyBtn:CheckSpecialEffectBtn(activity_type)
	-- self.node_list.Icon:SetActive(false)
	self.node_list.SpecialEff:SetActive(true)
end

function ZeroBuyBtn:ButtonChange(reason, arg1)
	if self:IsNil() then
		return
	end

	if reason == ZEROBUY_ACT_BTN.CHANGE_STATUS then
		self:SetActive(arg1)
		self:SetCanvasGroupVal(1)
		self:SetBtnEnable(MainuiWGCtrl.Instance:GetShrinkButtonToggleIsOn())
	elseif reason == ZEROBUY_ACT_BTN.SHOW_NEW then
		self:SetTxtNewStatus(arg1)
	end
	-- if self.node_list and self.node_list.SpecialEff and self.node_list.Icon then
	-- 	self.node_list.SpecialEff:SetActive(true)
	-- 	-- self.node_list.Icon:SetActive(false)
	-- end
end

function ZeroBuyBtn:SetActive(active)
	if self.view and not IsNil(self.view.gameObject) then
		self.view.gameObject:SetActive(active)
		self:SetCanvasGroupVal(1)
		self:SetBtnEnable(MainuiWGCtrl.Instance:GetShrinkButtonToggleIsOn())
	end
	self.active = active
end


function ZeroBuyBtn:SetTxtNewStatus(status)
	if not self.node_list then return end
	self.node_list["txt_new"]:SetActive(status)
	-- self.node_list.SpecialEff:SetActive(true)
	-- self.node_list.Icon:SetActive(false)
end