FairyLandGodBodyActView = FairyLandGodBodyActView or BaseClass(SafeBaseView)
function FairyLandGodBodyActView:__init()
    self.view_name = "FairyLandGodBodyActView"
	self:SetMaskBg(true)
    self.view_style = ViewStyle.Half
    self:AddViewResource(0, "uis/view/fairy_land_equipment_ui_prefab", "layout_god_body_act_view")
end

function FairyLandGodBodyActView:__delete()

end

function FairyLandGodBodyActView:ReleaseCallBack()
    self:CancelTween()

	-- if self.model_display then
	-- 	self.model_display:DeleteMe()
	-- 	self.model_display = nil
	-- end

    if self.display_model then
		self.display_model:DeleteMe()
		self.display_model = nil
	end

    self.slot = nil
end

function FairyLandGodBodyActView:LoadCallBack()
    if not self.raw_tween then
        RectTransform.SetAnchoredPositionXY(self.node_list["tween_node"].rect, -15, 0)
		self.raw_tween = self.node_list["tween_node"].rect:DOAnchorPosY(20, 1.2)
		self.raw_tween:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	end

    XUI.AddClickEventListener(self.node_list.btn_sure, BindTool.Bind(self.Close, self))

    -- if nil == self.model_display then
	-- 	self.model_display = RoleModel.New()
	-- 	local display_data = {
	-- 		parent_node = self.node_list["display_root"],
	-- 		camera_type = MODEL_CAMERA_TYPE.BASE,
	-- 		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
	-- 		rt_scale_type = ModelRTSCaleType.L,
	-- 		can_drag = true,
	-- 	}

	-- 	self.model_display:SetRenderTexUI3DModel(display_data)

	-- 	-- self.model_display:SetUI3DModel(self.node_list["display_root"].transform,
	-- 	-- 	self.node_list["display_root"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
	-- 	self:AddUiRoleModel(self.model_display)
	-- end

    if not self.display_model then
        self.display_model = OperationActRender.New(self.node_list["display_root"])
        self.display_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end
end

function FairyLandGodBodyActView:SetDataAndOpen(slot)
    self.slot = slot
	self:Open()
end

function FairyLandGodBodyActView:CloseCallBack()
    local gb_data = FairyLandEquipmentWGData.Instance:GetGodBodyData(self.slot)
    if gb_data then
        local level = gb_data:GetLevel()
        local end_time = gb_data:GetUplevelEndTime()
        if level == 0 and end_time == 0 then
            FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.BREAK_JING_MAI, self.slot)
        end
    end

    FairyLandEquipmentWGCtrl.Instance:OpenView(TabIndex.fairy_land_eq_god_body, "all", {to_ui_name = self.slot})
end

function FairyLandGodBodyActView:CancelTween()
	if self.raw_tween then
        self.raw_tween:Kill()
        self.raw_tween = nil
	end
end

function FairyLandGodBodyActView:OnFlush()
    if self.slot == nil then
        return
    end

    -- local bundle, asset = ResPath.GetRawImagesPNG("a2_gb_act_" .. self.slot)
    -- self.node_list.slot_img.raw_image:LoadSprite(bundle, asset, function()
    --     self.node_list.slot_img.raw_image:SetNativeSize()
    -- end)

    local slot_cfg = FairyLandEquipmentWGData.Instance:GetGodBodyCfg(self.slot)
    if slot_cfg then
        -- local tianshen_index = TianShenWGData.Instance:GetWeaponIndexByAppeImageId(slot_cfg.show_model)
        -- --local name = TianShenWGData.Instance:GetTianshennameByAppeId(slot_cfg.show_model)
        self.node_list.obj_name.text.text = slot_cfg.jieling_name
        -- self.model_display:SetTianShenModel(slot_cfg.show_model, tianshen_index, true, nil, SceneObjAnimator.Rest)

        local display_data = {}
        display_data.should_ani = true
        if slot_cfg.model_show_itemid and slot_cfg.model_show_itemid ~= 0 and slot_cfg.model_show_itemid ~= "" then
            local split_list = string.split(slot_cfg.model_show_itemid, "|")
            if #split_list > 1 then
                local list = {}
                for k, v in pairs(split_list) do
                    list[tonumber(v)] = true
                end
                display_data.model_item_id_list = list
            else
                display_data.item_id = slot_cfg.model_show_itemid
            end

            -- display_data.model_click_func = function ()
            --     TipWGCtrl.Instance:OpenItem({item_id = slot_cfg["model_show_itemid"]})
            -- end
        end

        display_data.bundle_name = slot_cfg["model_bundle_name"]
        display_data.asset_name = slot_cfg["model_asset_name"]
        local model_show_type = tonumber(slot_cfg["model_show_type"]) or 1
        display_data.render_type = model_show_type - 1
        display_data.model_rt_type = ModelRTSCaleType.L

        local scale = slot_cfg["display_scale"]
        if scale and scale ~= "" then
            display_data.model_adjust_root_local_scale = scale
        end

        if slot_cfg.rotation and slot_cfg.rotation ~= "" then
            local rotation_tab = string.split(slot_cfg.rotation,"|")
            display_data.model_adjust_root_local_rotation = Vector3(rotation_tab[1], rotation_tab[2], rotation_tab[3])
        end

        self.display_model:SetData(display_data)

        -- local pos_x, pos_y = 0, 0
        -- if slot_cfg.display_pos and slot_cfg.display_pos ~= "" then
        --     local pos_list = string.split(slot_cfg.display_pos, "|")
        --     pos_x = tonumber(pos_list[1]) or pos_x
        --     pos_y = tonumber(pos_list[2]) or pos_y
        -- end

        -- RectTransform.SetAnchoredPositionXY(self.node_list.display_root.rect, pos_x, pos_y)
    end
end
