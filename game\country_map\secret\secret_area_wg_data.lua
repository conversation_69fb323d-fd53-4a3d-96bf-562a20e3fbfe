SecretAreaWGData = SecretAreaWGData or BaseClass()

function SecretAreaWGData:__init()
	if SecretAreaWGData.Instance ~= nil then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[SecretAreaWGData] attempt to create singleton twice!")
		return
	end
	SecretAreaWGData.Instance = self

	self:InitCfg()
	self:InitMedicineItemConfig()
	RemindManager.Instance:Register(RemindName.CountryMapActSecret, BindTool.Bind(self.ShowCountrySecretRemind, self)) 			-- 国家星图秘境红点
end

function SecretAreaWGData:__delete()
	SecretAreaWGData.Instance = nil

	RemindManager.Instance:UnRegister(RemindName.CountryMapActSecret)
end

function SecretAreaWGData:InitCfg()
	self.secret_cfg_auto = ConfigManager.Instance:GetAutoConfig("cross_xingtumijing_auto")
	self.secret_other_cfg = self.secret_cfg_auto.other[1]
	self.secret_reward_cfg =  self.secret_cfg_auto.reward_cfg
end


--设置基本信息
function SecretAreaWGData:SetSecretAreaInfo(protocol)
	self.secret_area_info = {}
	self.secret_area_info.enter_xtmj_times = protocol.enter_xtmj_times
	self.secret_area_info.enter_xtmj_last_time = protocol.enter_xtmj_last_time
end

function SecretAreaWGData:SetSecretAreaTaskInfo(protocol)
	self.secret_area_task_info = {}
	self.secret_area_task_info.fetch_exp = protocol.fetch_exp
	self.secret_area_task_info.kill_count = protocol.kill_count
	self.secret_area_task_info.scene_lv = protocol.scene_lv
	self.secret_area_task_info.wave = protocol.wave
	self.secret_area_task_info.role_count = protocol.role_count
	self.secret_area_task_info.boss_step = protocol.boss_step
end

function SecretAreaWGData:SetSecretAreaFinishInfo(protocol)
	self.secret_area_finish_info = {}
	self.secret_area_finish_info.reward_item_list = protocol.reward_item_list
	self.secret_area_finish_info.exp = protocol.exp
	self.secret_area_finish_info.count = protocol.count
	self.secret_area_finish_info.is_win = protocol.is_win

end

-- 获取其他配置
function SecretAreaWGData:GetOtherCfg()
	return self.secret_other_cfg
end

function SecretAreaWGData:GetSecretAreaInfo()
	return self.secret_area_info or {}
end

function SecretAreaWGData:GetSecretAreaTaskInfo()
	return self.secret_area_task_info or {}
end

function SecretAreaWGData:GetSecretAreaFinishInfo()
	return self.secret_area_finish_info or {}
end

function SecretAreaWGData:GetSecretRewardCfgByWorldLv()
	if IsEmptyTable(self.secret_reward_cfg) then
		return
	end

	local kf_world_level = CrossServerWGData.Instance:GetServerMeanWorldLevel()
	local other_cfg = self:GetOtherCfg()
	local mijing_cut_level = other_cfg.mijing_cut_level
	local cfg_level =  kf_world_level - mijing_cut_level
	local cur_reward_cfg = {}
	for k,v in pairs(self.secret_reward_cfg) do
		if cfg_level >= v.mijing_level_min and cfg_level <= v.mijing_level_max then
			cur_reward_cfg = v
			break
		end
	end
	if IsEmptyTable(cur_reward_cfg) then
		print("<<<<<<<拿不到相匹配得奖励配置<<世界等级 = ", kf_world_level)
	end
	return cur_reward_cfg
end

function SecretAreaWGData:ShowCountrySecretRemind()
	local is_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.CountryMapActView)
	if not is_open then
		return 0
	end
	
	local remind = self:GetCountrySecretRemind()
	return remind
end

function SecretAreaWGData:GetCountrySecretRemind()
	local secret_area_info = self:GetSecretAreaInfo()
	local secret_other_cfg = self:GetOtherCfg()
	if IsEmptyTable(secret_area_info) or IsEmptyTable(secret_other_cfg) then
		return 0
	end
	local remain_times = secret_other_cfg.limit_times - secret_area_info.enter_xtmj_times
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_COUNTRY_SECRET_AREA)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if activity_info then
		if activity_info.status == ACTIVITY_STATUS.STANDY then
			if remain_times > 0 or secret_area_info.enter_xtmj_last_time >= server_time then
				return 1
			end
		elseif activity_info.status == ACTIVITY_STATUS.OPEN then
			if secret_area_info.enter_xtmj_last_time >= server_time then
				return 1
			end
		end
	end
	return 0
end

function SecretAreaWGData:InitMedicineItemConfig()
	if not self.medicine_item_id_list then
		self.medicine_item_id_list = {}
	end
	local other_cfg = self:GetOtherCfg()
	local str = other_cfg.exp_medicine
	local list = Split(str, "|")
	for i, v in ipairs(list) do
		table.insert(self.medicine_item_id_list, tonumber(v))
	end

	if not self.medicine_item_seq_list then
		self.medicine_item_seq_list = {}
	end
	local str =  other_cfg.exp_medicicn_seq
	local list = Split(str, "|")
	for i, v in ipairs(list) do
		table.insert(self.medicine_item_seq_list, tonumber(v))
	end
end

function SecretAreaWGData:GetMedicineItemList()
	return self.medicine_item_id_list
end
function SecretAreaWGData:GetMedicineItemSeqList()
	return self.medicine_item_seq_list
end