--require("game/crossserver/kuafu_onevone/kf_onevone_cfg")
require("game/crossserver/kuafu_onevone/kf_onevone_wg_data")
-- require("game/crossserver/kuafu_onevone/kf_onevone_view")
require("game/crossserver/kuafu_onevone/kffield_head_panel")
require("game/crossserver/kuafu_onevone/kf_onevone_finish")
require("game/crossserver/kuafu_onevone/kf_onevone_ring_view")
require("game/crossserver/kuafu_onevone/kf_onevone_match_view")
require("game/crossserver/kuafu_onevone/kf_rank_score_view")

KuafuOnevoneWGCtrl = KuafuOnevoneWGCtrl or BaseClass(BaseWGCtrl)
function KuafuOnevoneWGCtrl:__init()
	if KuafuOnevoneWGCtrl.Instance then
		error("[KuafuOnevoneWGCtrl]:Attempt to create singleton twice!")
	end
	KuafuOnevoneWGCtrl.Instance = self

	self.data = KuafuOnevoneWGData.New()
--	self.view = KuafuOnevoneView.New()
	self.head_view = KfFieldHeadPanel.New()
	self.finish_view = KfOneVoneFisish.New()
	self.ring_view = KfOneVOneRing.New()
	self.match_view = KFOneVOneMatchView.New(GuideModuleName.KfOneVOneMatch)
	-- self.rank_view = KuafuOnevoneRankView.New()
	self:RegisterAllProtocals()
end

function KuafuOnevoneWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	if self.head_view then
		self.head_view:DeleteMe()
		self.head_view = nil
	end

	if self.finish_view then
		self.finish_view:DeleteMe()
		self.finish_view = nil
	end

	if self.alert_window then
  		self.alert_window:DeleteMe()
   		self.alert_window = nil
    end

    if self.ring_view then
  		self.ring_view:DeleteMe()
   		self.ring_view = nil
    end

    if self.match_view then
    	self.match_view:DeleteMe()
    	self.match_view = nil
    end

	KuafuOnevoneWGCtrl.Instance = nil
end

function KuafuOnevoneWGCtrl:RegisterAllProtocals()
	-- self:RegisterProtocol(SCCross1v1FightStart, "OnCross1v1FightStart")
	self:RegisterProtocol(SCCross1v1MatchAck, "OnCross1v1MatchAck")
	self:RegisterProtocol(SCCross1v1MatchResult, "OnCross1v1MatchResult")
	-- self:RegisterProtocol(SCCross1v1FightResult, "OnCross1v1FightResult")
	self:RegisterProtocol(SCCross1v1WeekRecord, "OnCross1v1WeekRecord")
	self:RegisterProtocol(SCCrossActivity1V1SelfInfo, "OnCrossActivity1V1SelfInfo")

	self:RegisterProtocol(CSCrossMatch1V1Req)
	self:RegisterProtocol(CSCross1v1FightReady)
	self:RegisterProtocol(CSCross1v1MatchQuery)
	self:RegisterProtocol(CSCross1v1WeekRecordQuery)
	self:RegisterProtocol(CSCross1v1FetchRewardReq)
	self:RegisterProtocol(CSCross1v1BuyTimeReq)
	self:RegisterProtocol(CSCross1v1WearRingReq)
	self:RegisterProtocol(CSCross1v1MatchResultReq)
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.KFOneVOneInfoReq, self))
end

function KuafuOnevoneWGCtrl:KFOneVOneInfoReq()
	local is_open =  FunOpen.Instance:GetFunIsOpened(FunName.KF1V1)
	if is_open then
		RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_CROSS_1V1_SELF_INFO)
	end
end

function KuafuOnevoneWGCtrl:Open()
	if Field1v1WGCtrl.Instance.view then
		Field1v1WGCtrl.Instance.view:Open(KUAFU_TAB_TYPE.ONEVSONE)
	end
end

function KuafuOnevoneWGCtrl:RemindGongXun()
	return self.data:RemindKFOnevOne()
end

function KuafuOnevoneWGCtrl:OpenHeadView()
	self.head_view:Open()
--	self.head_view:SetOutTime(0)
	local match_info = self.data:GetOppoInfo()

	self.head_view:SetData(self.data:GetSceneUser())
end

function KuafuOnevoneWGCtrl:CloseHeadView()
	self.head_view:Close()
end

function KuafuOnevoneWGCtrl:GetOpenHeadView()
	return self.head_view
end

function KuafuOnevoneWGCtrl:Close()
	if Field1v1WGCtrl.Instance.view then
		 Field1v1WGCtrl.Instance.view:Close()
	end
end

-- 跨服1V1战斗开始
function KuafuOnevoneWGCtrl:OnCross1v1FightStart(protocol)
	--print_error("跨服1V1战斗开始",protocol.timestamp_type,protocol.fight_start_timestmap,protocol.fight_start_timestmap - TimeWGCtrl.Instance:GetServerTime() )
	 self.head_view:SetIsShowStartImg(true)
	local match_info = self.data:GetOppoInfo()
	local out_time = FuBenWGData.Instance:GetOutFbTime()
	self.data.scene_status = protocol.timestamp_type
	self.data.scene_next_time = protocol.fight_start_timestmap

	if out_time and protocol.timestamp_type == KUAFUONEVONE_STATUS.AWAIT  then
		UiInstanceMgr.Instance:ShowFBStartDown2(protocol.fight_start_timestmap)
	elseif protocol.timestamp_type == KUAFUONEVONE_STATUS.PREPARE then
		local cfg = KuafuOnevoneWGData.Instance:GetKFOneVOneOtherCfg()
		local scene_id = Scene.Instance:GetSceneId()
		GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end)
		GuajiWGCtrl.Instance:MoveToPos(scene_id, cfg.pos_1_x_1, cfg.pos_1_y_1, 3)
		self.head_view:SetOutTime(protocol.fight_start_timestmap)
	end
	self.data.fight_start_timestmap = protocol.fight_start_timestmap
	GlobalEventSystem:Fire(KFONEVONE1v1Type.KF_STATUS_CHANGE)
end

-- 跨服1v1匹配确认 --开始匹配返回预计时间
function KuafuOnevoneWGCtrl:OnCross1v1MatchAck(protocol)
--	 print_error("OnCross1v1MatchAck", protocol)
	self.data:Set1V1MacthInfo(protocol)
	Field1v1WGCtrl.Instance.view:Flush(KUAFU_TAB_TYPE.ONEVSONE)
	if protocol.result == 1 then
		ViewManager.Instance:Open(GuideModuleName.KfOneVOneMatch)
	end
	Field1v1WGCtrl.Instance:FlushPrepareScenePanel(ACTIVITY_TYPE.KF_ONEVONE)
	-- Field1v1WGCtrl.Instance.view:OpenPepeiEffect()
	ViewManager.Instance:FlushView(GuideModuleName.KfOneVOneMatch)
end

-- 跨服1v1匹配结果
function KuafuOnevoneWGCtrl:OnCross1v1MatchResult(protocol)
	local macth_info = self.data:Get1V1MacthInfo()
	macth_info.result = 0
	macth_info.match_end_left_time = 0

	local user_vo = GameVoManager.Instance:GetUserVo()
	local info = protocol.info
	self.data:SetMatchingEnemySex(info)
	AvatarManager.Instance:SetAvatarKey(info.role_id, info.head_icon_big, info.head_icon_small)
	-- Field1v1WGCtrl:StopMatchingEffects()
	GlobalEventSystem:Fire(KFONEVONE1v1Type.KF_INFO_CHANGE)
	if nil == self.alert_window then
		self.alert_window = Alert.New(nil, nil, nil, nil, nil, nil, false)
	end
	self.alert_window:SetCloseBeforeFunc()
	if info.result == 1 then
		local act_statu = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_ONEVONE) or {}
		if act_statu.status == ACTIVITY_STATUS.CLOSE then
			self.alert_window:SetLableString(Language.Kuafu1V1.MatchFailTxt2)
			self.alert_window:SetOkString(Language.Common.Confirm)
			self.alert_window:SetOkFunc()
		else
			self.alert_window:SetLableString(Language.Kuafu1V1.MatchFailTxt1)
			self.alert_window:SetOkString(Language.Kuafu1V1.GoOnMatch)
			self.alert_window:SetOkFunc(BindTool.Bind1(self.SendCrossMatch1V1Req, self))

			self:SendCross1v1WeekRecordQuery()
		end
	else
		info.name = info.oppo_name
		info.origin_uid = info.oppo_sever_id

		-- 无意义了
		-- if info.oppo_sever_id <= 0 then
		-- 	info.oppo_sever_id = 1
		-- 	info.is_robert = true
		-- end
		-- info.name = info.oppo_name .. "_s" .. info.oppo_sever_id

		self.data:SetOppoInfo(info)
		GlobalEventSystem:Fire(KFONEVONE1v1Type.START_PVP)
		Field1v1WGCtrl.Instance:SetMatchBtnFalse()
	end

end

-- 跨服1v1挑战结果
function KuafuOnevoneWGCtrl:OnCross1v1FightResult(protocol)
	GlobalEventSystem:Fire(OtherEventType.BEFORE_OUT_SCENE)
	self.finish_view:SetEndData(protocol)
	self.finish_view:Open()
	self.data:ClearEnemyInfo()
end

function KuafuOnevoneWGCtrl:ShowRankChange(protocol)
	self.data:Set1V1ShowRank(protocol.kf_1v1_show_rank)
	-- self.view:Flush(0, "show_rank")
end

-- 跨服1v1活动信息
function KuafuOnevoneWGCtrl:OnCrossActivity1V1SelfInfo(protocol)

	self.data:Set1V1Info(protocol)
	if KuafuOnevoneWGData.Instance:GetComeFromScene() then
		 Field1v1WGCtrl.Instance.view:Open()
		KuafuOnevoneWGData.Instance:SetComeFromScene(false)
	end

	Field1v1WGCtrl.Instance.view:Flush(KUAFU_TAB_TYPE.ONEVSONE)
	Field1v1WGCtrl.Instance:FlushPrepareScenePanel(ACTIVITY_TYPE.KF_ONEVONE)
	-- Field1v1WGCtrl.Instance.view:Flush(KUAFU_TAB_TYPE.ONEREWARD)
	ViewManager.Instance:FlushView(GuideModuleName.PvPGongXunReward)

	if self.ring_view:IsOpen() then
		self.ring_view:Flush(RING_TAB_TYPE.KingRing)
	end
	MainuiWGCtrl.Instance:FlushKfPKRenPoint()
	Field1v1WGCtrl.Instance:FlushGongXunView(KFPVP_TYPE.ONE)
	RemindManager.Instance:Fire(RemindName.ActOneVSOnebArena)
	RemindManager.Instance:Fire(RemindName.ActOneVSOneJiFenbArena)
end

-- 跨服1v1挑战记录
function KuafuOnevoneWGCtrl:OnCross1v1WeekRecord(protocol)
	self.data:ClearKf1V1News()
	for i,v in ipairs(protocol.kf_1v1_news) do
		local plat = ""
		if RoleWGData.Instance.role_vo.plat_type ~= v.oppo_plat_type then
			plat = Language.Common.WaiYu .. "_"
		end
		if v.oppo_server_id <= 0 then
			v.oppo_server_id = 1
		end
		local name = plat .. v.oppo_name .. "_s" .. v.oppo_server_id
		self.data:AddKf1V1News(name, v.result, v.add_score)
	end
end

-- 跨服1v1匹配请求
function KuafuOnevoneWGCtrl:SendCrossMatch1V1Req()
	if Scene.Instance:GetMainRole():GetIsFlying() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Fight.FlyTransmitLimit)
		return
	end
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossMatch1V1Req)
	send_protocol:EncodeAndSend()
--	print_error("跨服1v1匹配请求")
end

-- 跨服1v1战斗准备
function KuafuOnevoneWGCtrl.SendCross1v1FightReady()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCross1v1FightReady)
	send_protocol:EncodeAndSend()
end

-- 跨服1v1匹配查询
function KuafuOnevoneWGCtrl:SendCross1v1MatchQuery(req_type)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCross1v1MatchResultReq)
	send_protocol.req_type = req_type or 0
	send_protocol:EncodeAndSend()
end

-- 跨服1v1奖励领取
function KuafuOnevoneWGCtrl:SendCSCross1v1FetchRewardReq(fetch_type, seq)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCross1v1FetchRewardReq)
	send_protocol.fetch_type = fetch_type
	send_protocol.seq = seq or 0
	send_protocol:EncodeAndSend()
end
-- 跨服1v1奖励领取
function KuafuOnevoneWGCtrl:SendCSCross1v1BuyTimeReq()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCross1v1BuyTimeReq)
	send_protocol:EncodeAndSend()
end

-- 获取跨服1v1挑战记录
function KuafuOnevoneWGCtrl:SendCross1v1WeekRecordQuery()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCross1v1WeekRecordQuery)
	send_protocol:EncodeAndSend()
end

--穿戴戒指
function KuafuOnevoneWGCtrl:SendCross1v1Ring(opr_type,ring_seq)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCross1v1WearRingReq)
	send_protocol.opr_type = opr_type or 0
	send_protocol.ring_seq = ring_seq or 0
	send_protocol:EncodeAndSend()
end
--王者之戒
function KuafuOnevoneWGCtrl:OpenWZRingView(index)
	if index == TabIndex.kfpvp_lingpai then
		self:OpenWZCardView()
	else
		self.ring_view:Open(RING_TAB_TYPE.KingRing)
	end
end

-- 王者令牌
function KuafuOnevoneWGCtrl:OpenWZCardView()
	self.ring_view:Open(RING_TAB_TYPE.KingLingPai)
end

-- function KuafuOnevoneWGCtrl:Flush1v1RankView(index)
-- 	self.rank_view:Flush(index)
-- end
