FuBenPanelView = FuBenPanelView or BaseClass(SafeBaseView)
--诛神塔
function FuBenPanelView:InitZhuShenTaView()
	RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_ZHUSHENTA_FB_INFO_REQ)
	self.reaward_item_cell = {}
	self.zhushenta_data = FuBenPanelWGData.Instance
	for i = 0,3 do 
		local ph = self.node_list["ph_award_cell_"..i]
		local item_cell = ItemCell.New(ph)
		self.reaward_item_cell[i] = item_cell
	end
	self.zhushenta_fb_list = AsyncListView.New(ZhuShenTaTowerItem, self.node_list["ph_monster_list_view"])
	self.zhushenta_fb_list:SetSelectCallBack(BindTool.Bind1(self.OnZhuShenTaFbCallBack, self))
	
	self.alert_window2 = Alert.New()
	XUI.AddClickEventListener(self.node_list.btn_single_into,BindTool.Bind1(self.OnClickSingleInto, self))
	XUI.AddClickEventListener(self.node_list.btn_team_into,BindTool.Bind1(self.OnClickTeamInto, self))
	XUI.AddClickEventListener(self.node_list.btn_tip,BindTool.Bind1(self.OnClickOpenTip))
	self.node_list["layout_combine_zst_mark"].button:AddClickListener(BindTool.Bind1(self.OnClickZSTComnine,self))

	local honor,_ = self.zhushenta_data:GetHelpHonorAndMaxLevel()
	local str2 = string.format(Language.FuBenPanel.ZhuShenTaFuBenTip,honor)
	self.node_list.rich_tip_2.text.text = str2
end

function FuBenPanelView:OnClickZSTComnine()
    local combine_cfg = FuBenWGData.Instance:GetCombineCfg()
    local role_level = GameVoManager.Instance:GetMainRoleVo().level
    local need_level = combine_cfg[FB_COMBINE_TYPE.KILL_GOD_TOWER + 1].level_limit
    if need_level > role_level then
        local level_des = RoleWGData.GetLevelString(need_level)
        local str = string.format(Language.FuBenPanel.CombineLimitTips,level_des)
        SysMsgWGCtrl.Instance:ErrorRemind(str)
        return
    end
	local cur_count ,total_count = FuBenPanelWGData.Instance:GetZhuShenTaFBCount()
    if cur_count < 2 then 
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CountTooLess)
        return
    end
    local vas = self.node_list.layout_combine_zst_mark_hook:GetActive()
    
    local is_combine = vas == true and 0 or 1
    if vas then
        FuBenWGCtrl.Instance:SendFBUseCombine(is_combine,FB_COMBINE_TYPE.KILL_GOD_TOWER)
        self.node_list.layout_combine_zst_mark_hook:SetActive(not vas)
    else
        local callback_func = function()
            self.node_list.layout_combine_zst_mark_hook:SetActive(true)
        end
        FuBenWGCtrl.Instance:ShowCombinePanel(FB_COMBINE_TYPE.KILL_GOD_TOWER,callback_func)
    end
end

function FuBenPanelView:DeleteZhuShenTaView()
	if self.reaward_item_cell ~= nil then 
		for i,v in pairs(self.reaward_item_cell) do 
			v:DeleteMe()
		end
	end
	self.reaward_item_cell = nil
	if self.close_timer_quest12 ~= nil then
		GlobalTimerQuest:CancelQuest(self.close_timer_quest12)
		self.close_timer_quest12 = nil
	end
	if nil ~= self.zhushenta_display1 then
		self.zhushenta_display1:DeleteMe()
		
	end
	if nil ~= self.zhushenta_display2 then
		self.zhushenta_display2:DeleteMe()
		
	end
	if nil ~= self.zhushenta_display3 then
		self.zhushenta_display3:DeleteMe()
		
	end
	self.display_zhushenta1 = nil
	self.display_zhushenta2 = nil
	self.display_zhushenta3 = nil

	if self.monster_list ~= nil then 
		self.monster_list:DeleteMe()
	end
	self.monster_list = nil
	if self.zhushenta_fb_list then
		self.zhushenta_fb_list:DeleteMe()
	end
	self.zhushenta_fb_list = nil
	if self.alert_window2 ~= nil then 
		self.alert_window2:DeleteMe()
	end
	self.alert_window2 = nil
	self.zhushenta_data = nil
end

--点击单人进入
function FuBenPanelView:OnClickSingleInto()
	-- local cur_count ,total_count = self.zhushenta_data:GetZhuShenTaFBCount()
	-- cur_count = cur_count < 0 and 0 or cur_count 
 --    if nil == cur_count or cur_count < 1 then
 --        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.HaveNotEnterTimes)
 --        return
 --    end 
	-- self.alert_window2:SetLableString(Language.FuBenPanel.AloneEnter)
	-- self.alert_window2:SetOkFunc(function ()
	-- 	FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.ZHUSHENTA_FB, FUBEN_ENTER_TYPE.ALONE) 
	-- 	--FuBenWGData.Instance:SetEnterFb(FUBEN_TYPE.ZHUSHENTA_FB)
	-- 	FuBenPanelWGData.Instance:SetEnterType(FUBEN_ENTER_TYPE.ALONE)
	-- 	--self:Close()
	-- end)
	-- self.alert_window2:Open()
	self:SendFBUseCombineZhuShenTa()
    local is_in_team = SocietyWGData.Instance:GetIsInTeam()
    local  is_leader = SocietyWGData.Instance:GetIsTeamLeader()
    local member_count = #SocietyWGData.Instance:GetTeamMemberList() 
    if (1 == is_in_team and is_leader ~= 1) or (1 == is_in_team and is_leader == 1 and member_count >= 3) then
    	NewTeamWGCtrl.Instance:Open({team_type = TeamDataConst.GoalType.ZhuShenTa, fb_mode = 0,is_match = false})
    else
        NewTeamWGCtrl.Instance:Open({team_type = TeamDataConst.GoalType.ZhuShenTa, fb_mode = 0,is_match = true})
    end
end

function FuBenPanelView:SendFBUseCombineZhuShenTa()
    local vas = self.node_list.layout_combine_zst_mark_hook:GetActive()
    if not vas then return end
    --local is_linghunguangchang_fb = WuJinJiTanWGData.Instance:IsLingHunGuangChang() and FB_COMBINE_TYPE.LINGHUN_GUANGCHANG or FB_COMBINE_TYPE.WUJINJITAN
    FuBenWGCtrl.Instance:SendFBUseCombine(1,FB_COMBINE_TYPE.KILL_GOD_TOWER)
end

--点击组队进入
function FuBenPanelView:OnClickTeamInto()
	self:SendFBUseCombineZhuShenTa()
	local is_in_team =SocietyWGData.Instance:GetIsInTeam()
    if is_in_team == 1 then
        local  is_leader = SocietyWGData.Instance:GetIsTeamLeader()
        if is_leader ~= 1 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.HaveNotTeamLeader)
            return
        end
    end

	local cur_count,total_count = self.zhushenta_data:GetZhuShenTaFBCount()
	cur_count = cur_count < 0 and 0 or cur_count
    if nil == cur_count or cur_count < 1 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.HaveNotEnterTimes)
        return
    end

    local is_can_entr = FuBenPanelWGCtrl.Instance:IsCheckCanEnterFb()
    if not is_can_entr then
    	SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CanNotEnterFb)
    	return 
    end

    if 1 == SocietyWGData.Instance:GetIsInTeam() then
            if 1 == #SocietyWGData.Instance:GetTeamMemberList() then
                self.alert_window2:SetLableString(Language.FuBenPanel.AloneEnter1)
                self.alert_window2:SetOkFunc(function ()
                    FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.ZHUSHENTA_FB, FUBEN_ENTER_TYPE.TEAM)   -- 副本类型, 是否组队, 无意义
                    FuBenPanelWGData.Instance:SetEnterType(FUBEN_ENTER_TYPE.TEAM)
                   -- print_error("进入副本")
                end)
                self.alert_window2:Open()
            else
            	self:EnterFubenLiXian(FUBEN_TYPE.ZHUSHENTA_FB, FUBEN_ENTER_TYPE.TEAM,0)
                -- FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.ZHUSHENTA_FB, FUBEN_ENTER_TYPE.TEAM)   -- 副本类型, 是否组队, 无意义
                -- FuBenPanelWGData.Instance:SetEnterType(FUBEN_ENTER_TYPE.TEAM)
            end
    else
		NewTeamWGCtrl.Instance:Open({team_type = TeamDataConst.GoalType.ZhuShenTa, fb_mode = 0})--, is_match = false
	end
end

function FuBenPanelView:OnZhuShenTaFbCallBack(item)
	if nil == item or nil == item.data then return end 
	local reaward_item_list,reward_item_att,equip_part_item_id = self.zhushenta_data:GetZhuShenTaLayoutReaward(50- item.index)
	--print_error(reaward_item_list,reward_item_att,equip_part_item_id,item.index)
	for i=0,2 do
		self.reaward_item_cell[i]:SetData(reaward_item_list[i])
    	self.node_list["ph_award_cell_"..i]:SetActive(nil ~= reaward_item_list[i])
    	
	end
    if equip_part_item_id > 0 then
    	local data = {}
    	data.item_id = equip_part_item_id
    	self.reaward_item_cell[3]:SetData(data)
    	self.node_list["ph_award_cell_3"]:SetActive(true)
    else
    	self.node_list["ph_award_cell_3"]:SetActive(false)
    end
 --    local str3 = string.format(Language.FuBenPanel.ZhuShenTaFuCurLevel,51- item.index)
	-- self.node_list.rich_layout_num.text.text = str3
	local part, order = self.zhushenta_data:GetJieSuoPartByLayout(51- item.index)
	if order > 1 then
	--local str4 = string.format(Language.FuBenPanel.ZhuShenTaFuBenReawardOrder,Language.Equip.EquipName[part],order)
		self.node_list.rich_reaward_order.text.text = string.format(Language.FuBenPanel.ZhuShenTaFuBenReawardOrder,Language.Equip.EquipName[part],order)
	else
		self.node_list.rich_reaward_order.text.text = string.format(Language.FuBenPanel.ZhuShenTaFuBenReawardPart,Language.Equip.EquipName[part])
	end
	--self.node_list.rich_reaward_order.text.text = str4

end

--刷新诛神塔界面
function FuBenPanelView:OnFlushZhuShenTaView()
	local monsterdata_list = self.zhushenta_data:GetZhuShenTaTotalMonsterData()
	local index_level = self.zhushenta_data:GetZhuShenTaPassMaxLevel()
	--local reaward_item_list,reward_item_att,equip_part_item_id = self.zhushenta_data:GetZhuShenTaLayoutReaward(index_level + 1 )
	local index_level1 = index_level + 1 --服务端 从-1开始 客户端 加 1 从0开始
	local cur_count ,total_count = self.zhushenta_data:GetZhuShenTaFBCount()
	if cur_count > 0 then
		cur_count = "<color=#7cffb7>"..cur_count.."</color>"
		total_count = "<color=#7cffb7>".."/"..total_count.."</color>"
		
	else
		cur_count = "<color=#ff0000>"..cur_count.."</color>"--
		total_count = "<color=#ff0000>".."/"..total_count.."</color>"
	end
	local str1 = string.format(Language.FuBenPanel.ZhuShenTaFuBenNum,cur_count ,total_count)
	self.node_list.rich_tip_1.text.text = str1
	local flag_data = FuBenWGData.Instance:SetCombineMark()
	local is_combine_flag = bit:d2b(flag_data)
	self.node_list.layout_combine_zst_mark_hook:SetActive(is_combine_flag[32 - FB_COMBINE_TYPE.KILL_GOD_TOWER] == 1)
	-- for i=0,2 do
	-- 	self.reaward_item_cell[i]:SetData(reaward_item_list[i])
 --    	self.node_list["ph_award_cell_"..i]:SetActive(nil ~= reaward_item_list[i])
    	
	-- end
 --    if equip_part_item_id > 0 then
 --    	local data = {}
 --    	data.item_id = equip_part_item_id
 --    	self.reaward_item_cell[3]:SetData(data)
 --    	self.node_list["ph_award_cell_3"]:SetActive(true)
 --    else
 --    	self.node_list["ph_award_cell_3"]:SetActive(false)
 --    end
    
 --  	local temp_index = index_level+1 >= 50 and 50 or index_level+1
	-- local str3 = string.format(Language.FuBenPanel.ZhuShenTaFuCurLevel,temp_index)
	-- self.node_list.rich_layout_num.text.text = str3
	-- local part, order = self.zhushenta_data:GetJieSuoPartByLayout(index_level)
	-- local str4 = string.format(Language.FuBenPanel.ZhuShenTaFuBenReawardOrder,Language.Equip.EquipName[part],order)
	-- self.node_list.rich_reaward_order.text.text = str4

	local data_list = FuBenPanelWGData.Instance:GetZhuShenTaTotalMonsterData()
	local  tempdata = __TableCopy(data_list)
	local data_set_list = {}
	for i= #tempdata,1,-1 do
		table.insert(data_set_list,tempdata[i])
	end
	if index_level1 <= #tempdata/2 then
		self.zhushenta_fb_list:SetDataList(tempdata,1)
	else
		self.zhushenta_fb_list:SetDataList(tempdata,0)
	end
	if index_level1 < 2 then
		self.node_list.ph_monster_list_view.scroller:ReloadData(1)
	else
		self.node_list.ph_monster_list_view.scroller:ReloadData(1-(index_level1/#tempdata))
	end
	local index = 1 
	if index_level1 == 0 then
		index = 50 
	else
		index = 50-index_level1 > 0 and 50-index_level1 or 1 
	end
	
	self:SelectZhuShenTaItem(index) 
end

function FuBenPanelView:SelectZhuShenTaItem(index)
  if self.zhushenta_fb_list:GetItemAt(index) == nil then
    self.close_timer_quest12 = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.SelectZhuShenTaItem, self,index), 0.05)
    return
  end  
  self.zhushenta_fb_list:SelectIndex(index)
end

function FuBenPanelView:CheckZhuShenTaCount()
	local cur_count ,total_count = FuBenPanelWGData.Instance:GetZhuShenTaFBCount()
	return cur_count > 0
end

--点击提示
function FuBenPanelView:OnClickOpenTip()
	local role_tip = RuleTip.Instance
	role_tip:SetTitle(Language.FuBenPanel.ZhuShenTaFuBenTipTitle)
	role_tip:SetContent(Language.FuBenPanel.ZhuShenTaFuBenTipText)
end


ZhuShenTaTowerItem = ZhuShenTaTowerItem or BaseClass(BaseRender)
function ZhuShenTaTowerItem:__init()

end

function ZhuShenTaTowerItem:OnFlush()
	if nil == self.data then return end
	local tower_level = 51 - self.index
	local index_level = FuBenPanelWGData.Instance:GetZhuShenTaPassMaxLevel()
	--if index_level == -1 then index_level = 0 end
	index_level = index_level + 1 --服务端 从-1开始 客户端 加 1 从0开始
	self.node_list.level.text.text = "第"..tower_level.."层"
	local star_num = FuBenPanelWGData.Instance:GetStartCountByLayout(tower_level)
	for i=1,3 do
		if i > star_num then
			self.node_list["img_start_"..i]:SetActive(false)
		else
			self.node_list["img_start_"..i]:SetActive(true)
		end
	end
	if index_level+1 > tower_level then
		self.node_list.tong_guan:SetActive(true)
		self.node_list.door:SetActive(true)
		self.node_list.wei_tong_guan:SetActive(false)
	elseif index_level+1 == tower_level then
		self.node_list.tong_guan:SetActive(false)
		self.node_list.door:SetActive(false)
		self.node_list.wei_tong_guan:SetActive(false)
	else
		self.node_list.tong_guan:SetActive(false)
		self.node_list.door:SetActive(true)
		self.node_list.wei_tong_guan:SetActive(true)

	end
	--self.node_list.UI_zhushenta:SetActive(index_level+1 == tower_level)
	self.node_list["zhushenta_itemrender"].button.interactable = index_level+1 >= tower_level and true or false
end

function ZhuShenTaTowerItem:OnSelectChange(is_select)
	self.node_list.select_high_bg:SetActive(is_select)
	self.node_list.UI_zhushenta:SetActive(is_select)
	
end