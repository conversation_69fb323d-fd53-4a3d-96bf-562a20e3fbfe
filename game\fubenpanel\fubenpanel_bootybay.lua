FuBenPanelView = FuBenPanelView or BaseClass(SafeBaseView)
--诛神塔
function FuBenPanelView:InitBootybayView()
	if not self.bootybay_reward_list then
		self.bootybay_reward_list = AsyncListView.New(PetFbRewardItemRender, self.node_list["booty_rewared_list"])
	end

	self.node_list.go_btn.button:AddClickListener(BindTool.Bind(self.OnClickGoBtn,self))

	self:ShowBootyBayBriefIntroduction()

	self.node_list.desc_bootybay_ads.text.text = Language.BootyBay.DescFBAds

	-- self.is_des_expand = false
	--self.node_list.booty_tips_btn.button:AddClickListener(BindTool.Bind(self.OnClickTipsBtn,self)) --2020/1/2 lsq说去掉
	--self.node_list.btn_bootybay_rule.button:AddClickListener(BindTool.Bind(self.OnClickTipsBtn,self))
	--self.node_list["booty_close"].button:AddClickListener(BindTool.Bind(self.OnClickBOOTYClose, self))
	-- XUI.AddClickEventListener(self.node_list["btn_booty_show_desc"], BindTool.Bind(self.OnClickBootybayShowDescBtn, self))  --描述文本展开

	-- local reward_list = BootyBayWGData.Instance:GetRewardList()

	-- if not self.reward_list then
	-- 	self.reward_list = {}
	-- 	for k,v in pairs(reward_list) do
	-- 		v.show_duobei = true
	-- 		v.task_type = RATSDUOBEI_TASK.WABAO
	-- 		self.reward_list[k] = ItemCell.New(self.node_list.reward_list)
	-- 		self.reward_list[k]:SetData(v)
	-- 	end
	-- end
end

function FuBenPanelView:ShowBootyBayBriefIntroduction()
	local other_cfg = BootyBayWGData.Instance:GetOtherConfig()
	if other_cfg.fb_des then
		-- self.node_list["booty_desc"].text.text = other_cfg.fb_des
		self.node_list["desc_booty"].text.text = other_cfg.fb_des
	end

	-- for i = 1, 9 do
	-- 	if Language.FuBenPanel.BootyDayShuoming[i] then
	-- 		self.node_list["booty_fb_info"..i]:SetActive(true)
	-- 		self.node_list["booty_fb_info"..i].text.text = Language.FuBenPanel.BootyDayShuoming[i]
	-- 	else
	-- 		self.node_list["booty_fb_info"..i]:SetActive(false)
	-- 	end
	-- end

	-- self.node_list.booty_desc_bg.rect.sizeDelta = u3dpool.vec2(570, 28)
	--self.node_list.booty_name.text.text = Language.FuBenPanel.RuleTitle[TabIndex.fubenpanel_bootybay]
end

function FuBenPanelView:DeleteBootybayView()
	if self.bootybay_reward_list then
		self.bootybay_reward_list:DeleteMe()
		self.bootybay_reward_list = nil
	end
end

-- function FuBenPanelView:OnClickBootybayShowDescBtn()
-- 	local is_expand = not self.is_des_expand
-- 	local height = self.node_list.booty_desc.rect.sizeDelta.y + 8
-- 	self.node_list.booty_desc_bg.rect.sizeDelta = is_expand and u3dpool.vec2(570, height) or u3dpool.vec2(570, 28)
-- 	self.node_list.img_booty_desc_arrow.rect.rotation = is_expand and Quaternion.Euler(0, 0, 180) or Quaternion.identity
-- 	self.is_des_expand = is_expand
-- end

-- function FuBenPanelView:DoBootybayTweenStart()
-- 	-- if self.node_list.bootyday_tween_root then
-- 	-- 	UITween.CanvasGroup(self.node_list.bootyday_tween_root.gameObject).alpha = 0
-- 	-- end
-- end

-- function FuBenPanelView:DoBootybayTween()
-- 	-- local tween_info = UITween_CONSTS.FuBen
-- 	-- if self.node_list.bootyday_text_img then
-- 	-- 	UITween.ImgFillDoValue(self.node_list.bootyday_text_img, 0, 1, tween_info.TipsTweenTime)
-- 	-- end

-- 	-- if self.node_list.bootyday_tween_root then
-- 	-- 	UITween.CanvasGroup(self.node_list.bootyday_tween_root.gameObject).alpha = 0
-- 	-- 	UITween.AlphaShow(GuideModuleName.FuBenPanel,self.node_list.bootyday_tween_root.gameObject, 0, 1, tween_info.TipsTweenTime * 0.5)
-- 	-- end

-- 	-- if self.node_list.bootyday_shuoming then
-- 	-- 	local start_pos = Vector3(250, self.node_list.bootyday_shuoming.transform.anchoredPosition.y, 0)
-- 	-- 	UITween.MoveShowPanel(self.node_list.bootyday_shuoming, start_pos, tween_info.TipsTweenTime)
-- 	-- end
-- end

function FuBenPanelView:FlushBootybayView()
	local wabao_times = BootyBayWGData.Instance:GettDailyWaBaoRemainCount()
	local other_cfg = BootyBayWGData.Instance:GetOtherConfig()
	local is_accepted = BootyBayWGData.Instance:GetIsAcceptWaBaoTask()
	local times = is_accepted and other_cfg.task_wabao_times - wabao_times or other_cfg.task_wabao_times

	local enter_teamfb_times = BootyBayWGData.Instance:GetEnterTeamFBTimes() or 0
    local remain_fb_times = other_cfg.team_fb_enter_times_limit - enter_teamfb_times
    local color = times > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED
    local str = times.."/"..other_cfg.task_wabao_times
	self.node_list.task_desc.text.text = ToColorStr(str,color) --string.format(Language.FuBenPanel.RemainWaBaoTimes, color, ,)
	

    local color1 = remain_fb_times > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED
    local str1 = remain_fb_times.."/"..other_cfg.team_fb_enter_times_limit
	self.node_list.task_desc2.text.text = ToColorStr(str1,color1)--string.format(Language.BootyBay.EnterTeamFBTimes, color1, , )

	local remind = FuBenPanelWGData.Instance:IsShowBootybayRedPoint()
	self.node_list.xjyj_remind:SetActive(remind > 0)
	-- for i=1,5 do
	-- 	self.node_list["booty_shuoming"..i].text.text = Language.FuBenPanel.BootyDayShuoming[i]
	-- end
	local reward_list = BootyBayWGData.Instance:GetRewaredListData()
	self.bootybay_reward_list:SetDataList(reward_list)
	self.bootybay_reward_list:JumpToIndex(#reward_list)
end

function FuBenPanelView:OnClickGoBtn()
	if not FuBenWGCtrl.Instance:GetCanEnterFuBen() then
		return
	end
	local bootybay_scene_id = BootyBayWGData.Instance:GetBootyBaySceneId()
	BootyBayWGData.Instance:SetWaBaoType(WABAO_TYPE.WABAO_TYPE_TASK)
	local scene_id = Scene.Instance:GetSceneId()
	local bootybay_other_cfg = BootyBayWGData.Instance:GetOtherConfig()
	local role_level = RoleWGData.Instance.role_vo.level
	local scene_type = Scene.Instance:GetSceneType()

	if bootybay_other_cfg.open_level > role_level then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Boss.CaveBossTips)
		return
	end
	-- if scene_type == SceneType.BOOTYBAY_FB or scene_type == SceneType.TEAM_BOOTYBAY_FB then
	-- 	TipWGCtrl.Instance:ShowSystemMsg(Language.BootyBay.BaoTuDesc1)
	-- 	return
	-- end

	-- if scene_type ~= SceneType.Common then
	-- 	TipWGCtrl.Instance:ShowSystemMsg(Language.BootyBay.BaoTuDesc2)
	-- 	return
	-- end
	if scene_id ~= bootybay_scene_id then
		local scene_logic = Scene.Instance:GetSceneLogic()
   		local x, y = scene_logic:GetTargetScenePos(bootybay_scene_id)

   		TaskWGCtrl.Instance:SendFlyByShoe(bootybay_scene_id, x or 0, y or 0, -1, false)
		--GuajiWGCtrl.Instance:FlyToScene(bootybay_scene_id, true)
	else
		BootyBayWGCtrl.Instance:SendTaskWaBao()
	end
    self:Close()
    ViewManager.Instance:CloseAll()
end

--[[
function FuBenPanelView:OnClickTipsBtn()
   	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetContent(Language.BootyBay.BootyBayFBTipsDesc,Language.BootyBay.BootyBayFBTitle)
	end
end

function FuBenPanelView:OnClickBOOTYClose()
	self.fb_index = -1
	self.node_list.bootybay_left_msg.rect:DOAnchorPos(Vector2(500, 0), 0.3)
	ReDelayCall(self, function()
		self:OnTabChangeHandler(0)
	end, 0.3, "bootybay_tween")
end
]]