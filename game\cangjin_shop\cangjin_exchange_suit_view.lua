function CangJinExchangeView:LoadIndexCallBackSuit()
	if nil == self.suit_grid_list then
		self.suit_grid_list = AsyncBaseGrid.New()
		self.suit_grid_list:CreateCells({col = 2,change_cells_num = 1, list_view = self.node_list["suit_grid_list"],
		assetBundle = "uis/view/cangjin_shop_prefab", assetName = "suit_item",  itemRender = CangJinExchangeSuitCell})
		self.suit_grid_list:SetStartZeroIndex(false)
	end
end

function CangJinExchangeView:ShowIndexCallBackSuit()

end

function CangJinExchangeView:ReleaseCallBackSuit()
    if self.suit_grid_list then
        self.suit_grid_list:DeleteMe()
        self.suit_grid_list = nil
    end
end

function CangJinExchangeView:OnFlushSuit()
	local suit_info = CangJinShopWGData.Instance:GetShowSuitInfo()
	if self.suit_grid_list then
		self.suit_grid_list:SetDataList(suit_info)
	end
end

----------------CangJinExchangeSuitCell--------------
CangJinExchangeSuitCell = CangJinExchangeSuitCell or BaseClass(BaseRender)

function CangJinExchangeSuitCell:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["buy_btn"], BindTool.Bind(self.OnClickBuySuit, self))
	
	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
		self.reward_list:SetStartZeroIndex(true)
	end
end

function CangJinExchangeSuitCell:__delete()
	if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function CangJinExchangeSuitCell:OnFlush()
	if not self.data then
		return
	end

	local cfg = self.data.cfg
	local limit_buy_times = cfg.buy_limit - self.data.buy_times
	self.node_list.can_buy_time.text.text = string.format(Language.CangJinShopView.LimitBuyTimes, limit_buy_times)
	self.node_list.need_score.text.text = string.format(Language.CangJinShopView.JiFen, cfg.consume_score)

	XUI.SetGraphicGrey(self.node_list.buy_btn, limit_buy_times <= 0)
	self.reward_list:SetDataList(cfg.reward_item)

	local bundle, asset = ResPath.GetNoPackPNG("a2_cjsp_suit_" .. cfg.seq)
    self.node_list.suit_img.image:LoadSprite(bundle, asset, function()
        self.node_list.suit_img.image:SetNativeSize()
    end)
end

function CangJinExchangeSuitCell:OnClickBuySuit()
	if not self.data then
		return
	end

	local cfg = self.data.cfg 
	local limit_buy_times = cfg.buy_limit - self.data.buy_times
	if limit_buy_times <= 0 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.CangJinShopView.MaxExchangeNum)
        return
	end

	local cur_score = CangJinShopWGData.Instance:GetCurScore()
	if cur_score < cfg.consume_score then
		TipWGCtrl.Instance:ShowSystemMsg(Language.CangJinShopView.NoEnoughScore)
		RechargeWGCtrl.Instance:RemindRechargeByCangJinShangPuScoreNoEnough(cfg.consume_score - cur_score)
        return
	end

	TipWGCtrl.Instance:OpenAlertTips(string.format(Language.Recharge.PayRechargeScoreStr, cfg.consume_score, cur_score), 
		function()
			CangJinShopWGCtrl.Instance:SendCangJinShopRequest(CANGJINSHANGPU_OPERA_TYPE.BUY_SUIT, cfg.seq, 1)
		end
	)
end