--------------------------------丹道----------------------------
local Refining_List_Cfg = {
    [1] = 2,
    [2] = 2,
    [3] = 4,
    [4] = 5,
    [5] = 7,
    [6] = 8,
    [7] = 8,
}

local RefiningNameColor = {
    [1] = COLOR3B.C4,
    [2] = COLOR3B.A3,
    [3] = COLOR3B.A4,
    [4] = COLOR3B.A5,
}

function RoleView:InitRefiningView()
    -- 大类型Item
    if not self.refining_big_type_list then
        self.refining_big_type_list = AsyncListView.New(RefiningBigTypeItem, self.node_list["big_type_list"])
        self.refining_big_type_list:SetSelectCallBack(BindTool.Bind(self.OnRefiningBigTypeSel, self))
    end

    -- 初始化队列
    if not self.refining_item_list then
        self.refining_item_list = AsyncBaseGrid.New()
        self.refining_item_list:SetStartZeroIndex(false)
        self.refining_item_list:SetIsMultiSelect(false)
        self.refining_item_list:CreateCells({
            col = 3,
            assetName = "refining_item_render",
            assetBundle = "uis/view/role_ui_prefab",
            list_view = self.node_list["refining_item_list"],
            itemRender = BaseRefiningCell,
            change_cells_num = 1,
        })
        self.refining_item_list:SetSelectCallBack(BindTool.Bind(self.OnSelectRefiningCellCB, self))
    end
    --[[
    if not self.refining_item_list then
        self.refining_item_list = {}
    end

    for i, v in ipairs(Refining_List_Cfg) do
        self.refining_item_list[i] = {}
        local parent_node = self.node_list[string.format("refining_type_list_%d", i)]

        if parent_node then
            for j = 1, v do
                local cell_obj = parent_node:FindObj(string.format("refining_render_%d", j))

                if cell_obj then
                    local cell = BaseRefiningCell.New(cell_obj)
                    cell:SetClickCallBack(BindTool.Bind1(self.OnSelectRefiningCellCB, self))
                    cell:SetIndex(j)
                    self.refining_item_list[i][j] = cell
                end
            end
        end
    end
    ]]
    self.cur_sel_big_type = 1
    self.refining_select_item = nil
    self.refining_select_data = nil
    XUI.AddClickEventListener(self.node_list["goto_refining"], BindTool.Bind1(self.OnGotoRefining,self))
    XUI.AddClickEventListener(self.node_list["btn_refining_uplevel"], BindTool.Bind1(self.OnBtnRefiningUpLevel,self))
    --XUI.AddClickEventListener(self.node_list["toggle_1"], BindTool.Bind(self.ToggleClick, self, 1))
    --XUI.AddClickEventListener(self.node_list["toggle_2"], BindTool.Bind(self.ToggleClick, self, 2))

    -- 属性列表
    if self.re_attr_list == nil then
        self.re_attr_list = {}
        local node_num = self.node_list["refining_attr_list"].transform.childCount
        for i = 1, node_num do
            self.re_attr_list[i] = CommonAddAttrRender.New(self.node_list["refining_attr_list"]:FindObj("attr_" .. i))
        end
    end

    if not self.refining_item_data_event then
        self.refining_item_data_event = BindTool.Bind1(self.OnRefiningItemDataChange, self)
        ItemWGData.Instance:NotifyDataChangeCallBack(self.refining_item_data_event)
    end

    -- if not self.refining_anim_rotation_list then
    --     self.refining_anim_rotation_list = {}

    --     for i = 1, 4 do
    --         local parent_node = self.node_list[string.format("refining_anim_group_%d", i)]
    --         if parent_node then
    --             self.refining_anim_rotation_list[i] = parent_node:GetComponent(typeof(UGUITweenRotation))
    --         end
    --     end

    --     self.refining_anim_rotation_list[5] = self.node_list.refining_item_show:GetComponent(typeof(UGUITweenScale))
    -- end

    -- 获取玩家性别、职业
    -- local role_sex = RoleWGData.Instance:GetRoleSex()
    -- local role_prof = RoleWGData.Instance:GetRoleProf()
    -- local lh_node = self.node_list[string.format("a3_tm_lh_%d_%d", role_sex, role_prof)]
    -- if lh_node then
    --     lh_node:CustomSetActive(true)
    -- end
    
    self.re_uplevel_item = ItemCell.New(self.node_list["re_upgrade_pos"])
    --self.cur_refining_cell = ItemCell.New(self.node_list["cur_refining_cell"])
end

function RoleView:DeleteRefiningView()
    if self.refining_big_type_list then
        self.refining_big_type_list:DeleteMe()
        self.refining_big_type_list = nil
    end
    --[[
    if self.refining_item_list then
        for _, v in ipairs(self.refining_item_list) do
            for _, n in ipairs(v) do
                n:DeleteMe()
            end
        end

		self.refining_item_list = nil
	end
    ]]

    if self.refining_item_list then
        self.refining_item_list:DeleteMe()
        self.refining_item_list = nil
    end

    if self.re_uplevel_item then
        self.re_uplevel_item:DeleteMe()
        self.re_uplevel_item = nil
    end

    -- if self.cur_refining_cell then
    --     self.cur_refining_cell:DeleteMe()
    --     self.cur_refining_cell = nil
    -- end

    if self.re_attr_list then
        for k, v in pairs(self.re_attr_list) do
            v:DeleteMe()
        end
        self.re_attr_list = nil
    end

    if self.refining_item_data_event then
        ItemWGData.Instance:UnNotifyDataChangeCallBack(self.refining_item_data_event)
        self.refining_item_data_event = nil
    end

    self.cur_sel_big_type = nil
    self.refining_select_item = nil
    self.refining_select_data = nil
    self.group_index = nil
    self.refining_select_item_index = nil
    --self.refining_anim_rotation_list = nil
end

function RoleView:FulshRefiningView(param_t)
    if param_t and param_t.item_id then
        --self.cur_sel_big_type = AlchemyWGData.Instance:GetPelletInfo(param_t.item_id).big_type
        local big_type, index = AlchemyWGData.Instance:GetPelletTypeIndexByItemId(param_t.item_id)
        self.need_jump_index = index
        self.is_flush = true
        self:FlushRefiningBigTypeList(big_type)
        self:FlushRefiningLeftShow()
    else
        local is_need_auto_jump = true
        if self.refining_select_data ~= nil and AlchemyWGData.Instance:GetPelletCellRemind(self.refining_select_data) then
            is_need_auto_jump = false
        end
        local big_type, index = AlchemyWGData.Instance:GetJumpPelletRemindIndex()
        if is_need_auto_jump and big_type > 0 and index > 0 then
            self.need_jump_index = index
            self:FlushRefiningBigTypeList(big_type)
        else
            if self.cur_sel_big_type and self.refining_select_item_index then
                self.is_flush = true
                self:FlushRefiningBigTypeList()
                self:FlushRefiningLeftShow()
                self:FulshRefiningRightShow(self.refining_select_data)
            else
                self.need_jump_index = 1
                self.is_flush = true
                self:FlushRefiningBigTypeList(1)
                self:FlushRefiningLeftShow()
            end
        end

        -- local is_jump = false
        -- self.cur_sel_big_type = 1

        -- if self.refining_select_item == nil then
        --     local remind_1, remind_2 = AlchemyWGData.Instance:GetPelletUpRemindByBigType()
        --     if remind_2 == 1 and (not remind_1 ~= 1) then
        --         self.cur_sel_big_type = 2
        --     end
    
        --     is_jump = true
        -- end

        -- if is_jump then
        --     self:FlushRefiningJumpPos(nil, self.cur_sel_big_type)
        --     --self:FlushRefiningRotationAnim()
        -- else
        --     local remind_1, remind_2 = AlchemyWGData.Instance:GetPelletUpRemindByBigType()
        --     if remind_2 == 1 and remind_1 ~= 1 then
        --         self.cur_sel_big_type = 2
        --         self:FlushRefiningJumpPos(nil, self.cur_sel_big_type)
        --     else
        --         self:FlushRefiningJumpPos(nil, self.cur_sel_big_type)
        --     end
        -- end

        -- self:FlushRefiningLeftShow()
        -- self:FulshRefiningToggleStatus()
    end

    --self:FulshRefiningRightShow(self.refining_select_item.data)
end

function RoleView:OnSelectRefiningCellCB(refining_cell)
    if nil == refining_cell or nil == refining_cell.data then
		return
	end

    self.refining_select_data = refining_cell.data
    local data = refining_cell.data
    -- if self.refining_select_item_index == refining_cell.index and self.group_index == data.type then
    --     return
    -- end

    -- if self.refining_select_item then
    --     self.refining_select_item:ChangeSelectCell(false, false)
    -- end

    --self.refining_select_item = refining_cell
    --self.refining_select_item:ChangeSelectCell(true, true)
    self.group_index = data.type
    self.refining_select_item_index = refining_cell.index
    --self:FlushRefiningLeftShow()
    self:FulshRefiningRightShow(data)
end

--[[
-- 刷新开关状态
function RoleView:FulshRefiningToggleStatus()
    if self.cur_sel_big_type == 1 then
        self.node_list["toggle_1"].toggle.isOn = true
    else
        self.node_list["toggle_2"].toggle.isOn = true
    end
end

--跳转到格子位置
function RoleView:FlushRefiningJumpPos(item_id, index)
    local group_index, item_index = AlchemyWGData.Instance:GetJumpPelletRemindIndex(item_id, index)
    self:FlushRefiningChangeSelect(group_index, item_index)
end

-- 刷新选中
function RoleView:FlushRefiningChangeSelect(group_index, item_index)
    for big_index, v in ipairs(self.refining_item_list) do
        for small_index, cell in ipairs(v) do
            cell:ChangeSelectCell(big_index == group_index, small_index == item_index)

            if big_index == group_index and small_index == item_index then
                self.refining_select_item = cell
            end
        end
    end
end
]]

-- function RoleView:FlushRefiningRotationAnim()
--     if self.refining_anim_rotation_list then
--         for _, refining_anim in ipairs(self.refining_anim_rotation_list) do
--             refining_anim:ResetToBeginning()
--         end
--     end
-- end

function RoleView:FlushRefiningBigTypeList(need_sel_big_type)
    local refining_cfg = AlchemyWGData.Instance:GetPelletCfgGroup()
    self.refining_big_type_list:SetDataList(refining_cfg)
    if need_sel_big_type then
        self.refining_big_type_list:SelectIndex(need_sel_big_type)
    end
end

--刷新丹药列表
function RoleView:FlushRefiningLeftShow()
    local refining_cfg = AlchemyWGData.Instance:GetPelletCfgGroup()
    if not refining_cfg or not refining_cfg[self.cur_sel_big_type] then
        return
    end
    local cur_cfg = refining_cfg[self.cur_sel_big_type]
    self.refining_item_list:SetDataList(cur_cfg)

    if self.need_jump_index then
        self.refining_item_list:JumpToIndexAndSelect(self.need_jump_index)
        self.need_jump_index = nil
    end

    -- local refining_left_cfg = AlchemyWGData.Instance:GetPelletCfg(self.cur_sel_big_type)
    -- if refining_left_cfg == nil then
    --     return
    -- end

    -- for big_index, v in ipairs(self.refining_item_list) do
    --     for small_index, cell in ipairs(v) do
    --         if refining_left_cfg[big_index] and refining_left_cfg[big_index][small_index] then
    --             cell:SetVisible(true)
    --             cell:SetData(refining_left_cfg[big_index][small_index])
    --         else
    --             cell:SetVisible(false)
    --         end
    --     end
    -- end
end

--刷新右侧显示
function RoleView:FulshRefiningRightShow(item)
    if item == nil then
        return
    end
    local item_cfg = ItemWGData.Instance:GetItemConfig(item.cost_item_id)
    local has_num = ItemWGData.Instance:GetItemNumInBagById(item.cost_item_id)

	self.re_uplevel_item:SetFlushCallBack(function ()
        local color = has_num < 1 and ITEM_NUM_COLOR.NOT_ENOUGH or ITEM_NUM_COLOR.ENOUGH
        local str = has_num .. "/" .. 1
		self.re_uplevel_item:SetRightBottomColorText(str, color)
		self.re_uplevel_item:SetRightBottomTextVisible(true)
	end)
    
    -- local color = has_num < 1 and ITEM_NUM_COLOR.NOT_ENOUGH or ITEM_NUM_COLOR.ENOUGH
    -- local str = has_num .. "/" .. 1
    -- self.node_list.upgrade_item_count.text.text = ToColorStr(str, color)
    self.re_uplevel_item:SetData({item_id = item.cost_item_id})
    --self.cur_refining_cell:SetData({item_id = item.cost_item_id})

    local level = AlchemyWGData.Instance:GetPelletItemLevel(item.seq)
    local btn_str = level > 0 and Language.Role.RefiningBtnDesc2 or Language.Role.RefiningBtnDesc1
    self.node_list["btn_refining_text"].tmp.text = btn_str

    local cur_name = ""
    local color = 3
    local level_txt = Language.Role.RefiningNoActive
    if level > 0 then
        level_txt = string.format(Language.Role.RefiningLevel, math.floor(level / 10), level % 10)
    end
    
    if item_cfg then
        cur_name =  item_cfg.name
        color = item_cfg.color
    end
    self.node_list["cur_refining_name"].tmp.text = string.format("<color=%s>%s</color>", RefiningNameColor[item.big_type] or "#ffffff", cur_name)
    self.node_list["cur_refining_lv"].tmp.text = level_txt

    local bundle, asset = ResPath.GetRawImagesPNG("a3_gf_mc_" .. item.big_type)
    self.node_list["img_name_bg"].raw_image:LoadSprite(bundle, asset)

    local btn_remind = AlchemyWGData.Instance:GetPelletCellRemind(item)
    self.node_list["btn_refining_remind"]:SetActive(btn_remind)

    local attr_list = AlchemyWGData.Instance:GetPelletUpLevelCfg(item)
    for k,v in ipairs(self.re_attr_list) do
        v:SetData(attr_list[k])
    end

    self.node_list["refining_max_show"]:SetActive(level >= item.level_limit)
    self.node_list["btn_refining_uplevel"]:SetActive(level < item.level_limit)
    -- local remind_1, remind_2 = AlchemyWGData.Instance:GetPelletUpRemindByBigType()
    -- self.node_list["toggle_red_1"]:SetActive(remind_1 == 1)
    -- self.node_list["toggle_red_2"]:SetActive(remind_2 == 1)

    local capability = AlchemyWGData.Instance:GetPelletTotalCapability()
    self.node_list["dan_cap_value"].tmp.text = capability
end

----------------------------------------------------------------------------------------------
function RoleView:OnRefiningBigTypeSel(item, cell_index)
    self.cur_sel_big_type = cell_index
    if not self.need_jump_index then
        self.need_jump_index = 1
        self:FlushRefiningLeftShow()
    else
        self:FlushRefiningLeftShow()
    end
end

-- function RoleView:ToggleClick(index, is_on)
--     if is_on then
--         self.cur_sel_big_type = index
--         self:FulshRefiningView("toggle")
--     end
-- end

--前往炼丹炉
function RoleView:OnGotoRefining()
    ViewManager.Instance:Open(GuideModuleName.AlchemyView)
end

--激活/升级
function RoleView:OnBtnRefiningUpLevel()
    if self.refining_select_data == nil or self.refining_select_data.cost_item_id <= 0 then
        return
    end
    local data = self.refining_select_data
    local has_num = ItemWGData.Instance:GetItemNumInBagById(data.cost_item_id)
    if has_num < 1 then
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = data.cost_item_id})
        return
    end

    
    --道具数量大于999
    if has_num > data.level_limit then
        has_num = data.level_limit
    end
    
    local level = AlchemyWGData.Instance:GetPelletItemLevel(data.seq)
    --道具数量加当前等级大于999
    if has_num + level > data.level_limit then
        has_num = data.level_limit - level
    end

    if level >= 1 then
        self:RefiningUpLevelSucessEffect() --升级成功
    else
        has_num = 1 --未激活就先激活
    end
    AlchemyWGCtrl.Instance:SenSAlchemyReq(ALCHEMY_OPERATE_TYPE.PELLET_UP, data.seq, has_num)
end

function RoleView:RefiningUpLevelSucessEffect()
	if self.node_list["re_uplevel_success_roo"] then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji, is_success = true,
                                        pos = Vector2(0, 0), parent_node = self.node_list["re_uplevel_success_roo"]})

		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
	end
end

function RoleView:OnRefiningItemDataChange(change_item_id)
    if change_item_id == nil then
        return
    end
    self:FulshRefiningView()
end