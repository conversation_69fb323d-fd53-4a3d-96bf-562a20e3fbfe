using UnityEditor;
using UnityEngine;

namespace ProceduralLOD
{
    [CustomEditor(typeof(LODModifyHelper))]
    public class LODModifyHelperEditor : Editor
    {
        private LODModifyHelper m_Component;

        private void OnEnable()
        {
            m_Component = this.target as LODModifyHelper;
        }
        private void OnDestroy()
        {
            if (m_Component != null)
                m_Component.Clear();
        }

        public override void OnInspectorGUI()
        {
            if (GUILayout.Button("取消"))
            {
                m_Component.Clear(true);
            }

            if (GUILayout.Button("保存"))
            {
                m_Component.Complete();
            }
        }
    }
}