require("game/arena_tianti/arena_tianti_wg_data")
require("game/arena_tianti/arena_tianti_view")
require("game/arena_tianti/tianti_rank_reward_tip")
require("game/arena_tianti/arena_tianti_rank_view")
require("game/arena_tianti/arena_tianti_record_tip")
require("game/arena_tianti/arena_tianti_finish")
require("game/arena_tianti/arena_tianti_challenge_tip")

ArenaTiantiWGCtrl = ArenaTiantiWGCtrl or BaseClass(BaseWGCtrl)

function ArenaTiantiWGCtrl:__init()
	if nil ~= ArenaTiantiWGCtrl.Instance then
		ErrorLog("[ArenaTiantiWGCtrl]:Attempt to create singleton twice!")
	end
	ArenaTiantiWGCtrl.Instance = self
	self.data = ArenaTianTiWGData.New()
	self.view = ArenaTianTiView.New(GuideModuleName.ArenaTianTi)
    self.rank_reward_tip = TianTiRankRewardTip.New()
    self.rank_view = ArenaTianTiRankView.New()
    self.record_tip = ArenaTianTiRecordTip.New()
	self.view_finish = ArenaTianTiFinish.New()				-- 挑战结果
	self.challenge_tip = ArenaTianTiChallengeTip.New()      --反击弹窗

	self:RegisterAllProtocol()

	self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)
end

function ArenaTiantiWGCtrl:__delete()
	if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

    if self.view then
        self.view:DeleteMe()
        self.view = nil
    end

    if self.rank_reward_tip then
        self.rank_reward_tip:DeleteMe()
        self.rank_reward_tip = nil
    end

    if self.rank_view then
        self.rank_view:DeleteMe()
        self.rank_view = nil
    end

    if self.record_tip then
        self.record_tip:DeleteMe()
        self.record_tip = nil
    end

	if self.view_finish then
		self.view_finish:DeleteMe()
		self.view_finish = nil
	end

	if self.challenge_tip then
		self.challenge_tip:DeleteMe()
		self.challenge_tip = nil
	end

	ArenaTiantiWGCtrl.Instance = nil
end

-- 注册协议
function ArenaTiantiWGCtrl:RegisterAllProtocol()
	self:RegisterProtocol(CSCrossChallengeFieldOperate)
	self:RegisterProtocol(SCCrossChallengeRankListInfo, 'OnSCCrossChallengeRankListInfo')
	self:RegisterProtocol(SCCrossChallengeFieldRewardInfo, 'OnSCCrossChallengeFieldRewardInfo')
	self:RegisterProtocol(SCCrossChallengeFieldStatus, "OnSCCrossChallengeFieldStatus")
	self:RegisterProtocol(CSCrossChallengeFieldGetOpponentInfo)
	self:RegisterProtocol(SCCrossChallengeFieldOpponentInfo, 'OnSCCrossChallengeFieldOpponentInfo')
	self:RegisterProtocol(CSCrossChallengeFieldGetUserInfo)
	self:RegisterProtocol(CSCrossChallengeFieldResetOpponentList)
	self:RegisterProtocol(CSCrossChallengeFieldFightReq)
	self:RegisterProtocol(CSCrossChallengeFieldSkipFighting)
    self:RegisterProtocol(SCCrossChallengeFieldUserInfo, "OnSCCrossChallengeFieldUserInfo")
	self:RegisterProtocol(SCCrossChallengeFieldFightBackInfo, "OnSCCrossChallengeFieldFightBackInfo")
	self:RegisterProtocol(SCCrossChallengeFieldReportInfo, "OnSCCrossChallengeFieldReportInfo")
	self:RegisterProtocol(SCCrossChallengeFieldSkipResult, "OnSCCrossChallengeFieldSkipResult") --跳过战斗返回
	self:RegisterProtocol(SCCrossChallengeFieldSkipInfo, "OnSCCrossChallengeFieldSkipInfo") --跳过战斗返回
end


function ArenaTiantiWGCtrl:SendChallengeField(operate_type, param1, param2, param3, param4, param5)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossChallengeFieldOperate)
	protocol.operate_type = operate_type or 0
	protocol.param1 = param1 or 0
    protocol.param2 = param2 or 0
    protocol.param3 = param3 or 0
    protocol.param4 = param4 or 0
    protocol.param5 = param5 or 0
	protocol:EncodeAndSend()
end

function ArenaTiantiWGCtrl:OnSCCrossChallengeRankListInfo(protocol)
	self.data:SetChallengeRankListInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush()
	end

	if self.rank_view:IsOpen() then
        self.rank_view:Flush()
    end
end

function ArenaTiantiWGCtrl:OnSCCrossChallengeFieldRewardInfo(protocol)
	self.data:SetChallengeFieldRewardInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush()
	end

	if self.rank_reward_tip:IsOpen() then
		self.rank_reward_tip:Flush()
	end

	RemindManager.Instance:Fire(RemindName.ActJjc)
	RemindManager.Instance:Fire(RemindName.ActTianTi)
end

-- function ArenaTiantiWGCtrl:OnSCChallengeFieldInfo(protocol)
-- 	self.data:SetChallengeFieldInfo(protocol)
-- 	if self.view:IsOpen() then
-- 		self.view:Flush()
-- 	end

-- 	if self.rank_reward_tip:IsOpen() then
-- 		self.rank_reward_tip:Flush()
-- 	end
-- end

function ArenaTiantiWGCtrl:GetField1v1RewardTabType()
	if self.rank_reward_tip:IsOpen() then
		return self.rank_reward_tip:GetField1v1RewardTabType()
	end

	return 1
end

-- 场景用户信息
function ArenaTiantiWGCtrl:OnSCCrossChallengeFieldStatus(protocol)
	self.data.scene_user_list = protocol.scene_user_list
	self.data.scene_status = protocol.status
	self.data.scene_next_time = protocol.next_time
	if self.data.scene_status == FIELD1V1_STATUS.OVER then
		self:OpenFinish(self.data:GetResultData())
	end
	GlobalEventSystem:Fire(Field1v1Type.FIELD_STATUS_CHANGE, protocol.status)
end

-- 请求其它玩家详细信息
function ArenaTiantiWGCtrl:ReqOtherRoleInfo(type)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossChallengeFieldGetOpponentInfo)
	send_protocol.type = type
	send_protocol:EncodeAndSend()
end

-- 返回玩家详细信息
function ArenaTiantiWGCtrl:OnSCCrossChallengeFieldOpponentInfo(protocol)
	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end

	if self.rank_view:IsOpen() then
        self.rank_view:Flush()
    end
end

-- 刷新挑战对手
function ArenaTiantiWGCtrl:ResetOpponentList()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossChallengeFieldResetOpponentList)
	send_protocol:EncodeAndSend()
end

--获取玩家挑战详细信息
function ArenaTiantiWGCtrl:ReqFieldGetDetailRankInfo()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossChallengeFieldGetUserInfo)
	send_protocol:EncodeAndSend()
end

-- 请求进入挑战
function ArenaTiantiWGCtrl:ResetFieldFightReq(data, is_fanji)
	if IS_ON_MATCHING then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.NoEnterFB)
		return
	end

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.ARENA_TIANTI then
		--self.view:Close()
	end
	-- 请求进入副本
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossChallengeFieldFightReq)
	send_protocol.opponent_index = data.opponent_index or 0
	send_protocol.ignore_rank_pos = 1
	send_protocol.rank_pos = data.rank_pos or 0
	send_protocol.is_skip = is_fanji or 0  --这个字段说没用到  改成反击字段  1是反击
	send_protocol.opponent_uuid = data.opponent_uuid
	send_protocol:EncodeAndSend()
end

function ArenaTiantiWGCtrl:SendFieldTiantiInfo()
	OperateFrequency.Operate(function()
		ArenaTiantiWGCtrl.Instance:ReqFieldGetDetailRankInfo()
		ArenaTiantiWGCtrl.Instance:ResetOpponentList()
		ArenaTiantiWGCtrl.Instance:ReqOtherRoleInfo(0)
	end, "ArenaTiantiWGCtrl:SendInfo", 2, "")
	if self.pick_delay_timer then
		GlobalTimerQuest:CancelQuest(self.pick_delay_timer)
		self.pick_delay_timer = nil
	end
	self.pick_delay_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.PickEnemyFieldFight, self), 0.5)
end

-- 选一个排名低的对手进入挑战
function ArenaTiantiWGCtrl:PickEnemyFieldFight()
	local info = ArenaTianTiWGData.Instance:GetUserinfo()
	if nil == info then
		self:SendFieldTiantiInfo()
		return
	end

	local sorted_list = info.rank_list
	table.sort(sorted_list, SortTools.KeyLowerSorter("rank"))

	local role_rank = info.rank
	local role_cap = RoleWGData.Instance:GetAttr("capability") or 0
	local rank_index = sorted_list[#sorted_list]
	if not IsEmptyTable(sorted_list) then
		for i = 1, #sorted_list do
			local data = sorted_list[i]
			local target_info = ArenaTianTiWGData.Instance:GetRoleInfoByUid(data.user_id)
			if target_info and role_cap >= target_info.capability and role_rank > data.rank_pos then
				rank_index = data
				break
			end
		end
	end

	if nil == rank_index or nil == rank_index.user_id or rank_index.user_id == 0 then
		self:SendFieldTiantiInfo()
		return
	end

	local tz_info = ArenaTianTiWGData.Instance:GetRoleTiaoZhanInfoByUid(rank_index.user_id)
	local data = {}
	data.opponent_index = tz_info.index
	data.rank_pos = tz_info.rank_pos
	data.opponent_uuid = rank_index.user_id
	data.is_skip = 0
	self:ResetFieldFightReq(data)
end

--结束战斗请求
function ArenaTiantiWGCtrl:EndFightArna()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossChallengeFieldSkipFighting)
	send_protocol:EncodeAndSend()
end

-- 挑战列表信息和个人信息
function ArenaTiantiWGCtrl:OnSCCrossChallengeFieldUserInfo(protocol)
	self.data:SetUserinfo(protocol.user_info)

	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.ActJjc)
	RemindManager.Instance:Fire(RemindName.ActTianTi)
end

function ArenaTiantiWGCtrl:FlushView()
	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end
end

--反击信息的返回
function ArenaTiantiWGCtrl:OnSCCrossChallengeFieldFightBackInfo(protocol)
	self.challenge_tip:SetDataAndOpen(protocol)
end

-- 战报
function ArenaTiantiWGCtrl:OnSCCrossChallengeFieldReportInfo(protocol)
	self.data:SetReportinfo(protocol.report_info)
	--self.field_record:Flush()
end

function ArenaTiantiWGCtrl:OnSCCrossChallengeFieldSkipResult(protocol)
	self.data:SetRoleLevel(RoleWGData.Instance.role_vo.level)
	self:OpenFinish(self.data:GetResultData(protocol.is_win), nil, true)
end

function ArenaTiantiWGCtrl:OnSCCrossChallengeFieldSkipInfo(protocol)
	self.data:SetSkipInfo(protocol)

	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end
end

function ArenaTiantiWGCtrl:OpenFinish(data, from, is_skip)
	GlobalEventSystem:Fire(OtherEventType.BEFORE_OUT_SCENE)
	self.view_finish:OpenFinish(data, from, is_skip)
	if self.view_finish:IsOpen() then
		self.view_finish:Flush()
	else
		self.view_finish:Open()
	end

	--RobertManager.Instance:StopFight()
end

function ArenaTiantiWGCtrl:OpenRankRewardTip()
    if self.rank_reward_tip:IsOpen() then
        self.rank_reward_tip:Flush()
    else
        self.rank_reward_tip:Open()
    end
end

function ArenaTiantiWGCtrl:OpenRankView()
    if self.rank_view:IsOpen() then
        self.rank_view:Flush()
    else
        self.rank_view:Open()
		self:ReqOtherRoleInfo(1)
    end
end

function ArenaTiantiWGCtrl:OpenRecordTip()
    if self.record_tip:IsOpen() then
        self.record_tip:Flush()
    else
        self.record_tip:Open()
    end
end

--关闭记录界面
function ArenaTiantiWGCtrl:CloseRecordTip()
    if self.record_tip:IsOpen() then
        self.record_tip:Close()
    end
end

function ArenaTiantiWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	local cost_item_id = self.data:GetChallengeFieldOtherCfgByName("join_item_id")
	if change_item_id == cost_item_id then
		if self.view:IsOpen() then
			self.view:Flush()
		end
	end
end
