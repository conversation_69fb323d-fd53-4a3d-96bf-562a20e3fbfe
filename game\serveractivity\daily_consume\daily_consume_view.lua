--------------------------------------------------
-- 每日累消
--------------------------------------------------
DailyConsumeView = DailyConsumeView or BaseClass(SafeBaseView)

function DailyConsumeView:__init()
	--self:SetModal(true)
	-- self.texture_path_list[1] = 'res/xui/rechargereward.png'
	-- self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/rechargereward_ui_prefab", "layout_daily_recharge")
end

function DailyConsumeView:__delete()
end

function DailyConsumeView:ReleaseCallBack()
	if self.item_cell_list then
		for k,v in pairs(self.item_cell_list) do
			v:DeleteMe()
		end
		self.item_cell_list = nil
	end

	-- if self.radio_btn then
	-- 	self.radio_btn:DeleteMe()
	-- 	self.radio_btn = nil
	-- end
	if self.ph_cell_top then
		self.ph_cell_top:DeleteMe()
		self.ph_cell_top = nil
	end
	if self.list_reward then
		self.list_reward:DeleteMe()
		self.list_reward = nil
	end
end

function DailyConsumeView:OpenCallBack()
	self.tab_index = 1
	local act_cornucopia_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_DAILYCONSUME) or {}
	if act_cornucopia_info.status == ACTIVITY_STATUS.OPEN then
		ServerActivityWGCtrl.Instance:SendChongzhiFetchReward(RECHARGEREWARD_REQ_TYPE.DAILY_TOTAL_CONSUME_INFO)
	end
end

function DailyConsumeView:LoadCallBack()
	-- self.layout_recharge_some = self.node_list.layout_recharge_some
	-- self.layout_recharge_some:setTouchEnabled(true)
	self:CreateRadioBtn()
	self:CreateExtraRewardList()
	self.item_cell_list = {}
	self:CreateRewardCells()
	self:RefreshItemSlot()
	XUI.AddClickEventListener(self.node_list.layout_recharge_some, BindTool.Bind(self.OnClickRecharge, self))
end

function DailyConsumeView:ShowIndexCallBack(index)
	self:Flush()
end

function DailyConsumeView:OnFlush()
	local day_consume_gold = DailyConsumeWGData.Instance:GetDayConsumeGold()
	if nil == day_consume_gold then return end
	local auto_index = nil
	for i = 1, 3 do
		local fetch_flag = DailyConsumeWGData.Instance:GetTotalRechargeFetchRewardFlag(i - 1)--每日领取标记
		local can_fetch_flag = DailyConsumeWGData.Instance:GetTotalRechargeCanFetchRewardFlag(i - 1)--累计消费奖励可领取标记
		local need_gold = DailyConsumeWGData.Instance:GetFirstRechargeNeedGold(i)
		local remind_num = (can_fetch_flag == 1 and fetch_flag ~= 1) and 1 or 0
		if auto_index == nil and remind_num == 1 then
			auto_index = i
		end
		-- if can_fetch_flag == 1 and fetch_flag ~= 1 then
		-- 	auto_index = i
		-- 	break
		-- elseif can_fetch_flag ~= 1 then
		-- 	auto_index = i
		-- 	break
		-- end
	end
	self:OnClickToggleRechargeNum(auto_index or self.tab_index)
	-- self.node_list.all_monry.text.text = cfg.need_consume_gold
	--self.radio_btn:SelectIndex(auto_index or self.tab_index)
	self:RefreshRadioBtnTipsVisible()
	self:RefreshItemSlot()
	self:RefreshBtnFetch()
	self:RefreshListDayReward()
end

function DailyConsumeView:SetBtnTitleImg(num)
	self.node_list.img_xiaofei:SetActive(num==3)
	self.node_list.img_lingqu:SetActive(num==1)
	self.node_list.img_yilingqu:SetActive(num==2)
	XUI.SetButtonEnabled(self.node_list.layout_recharge_some, num ~= 2)
end

function DailyConsumeView:SetBtnEnabled(is_enabled)
	XUI.SetButtonEnabled(self.node_list["layout_recharge_some"], is_enabled)
	-- self.node_list["img_btn_recharge"].node:setGrey(not is_enabled)
	-- self.node_list["img_btn_name"].node:setGrey(not is_enabled)
end

function DailyConsumeView:CreateRadioBtn()
	for i = 1, 3 do
		XUI.AddClickEventListener(self.node_list["btn_consume_"..i], BindTool.Bind(self.OnClickToggleRechargeNum, self,i))
	end
end

--物品格子
function DailyConsumeView:CreateRewardCells()
	for i = 1, 6 do
		local item_cell = ItemCell.New(self.node_list["ph_item_" .. i])
		self.item_cell_list[i] = item_cell
		if i == 1 then
			self.ph_cell_top = ItemCell.New(self.node_list["ph_cell_top"])
		end
	end
end


-- 右边累计奖励列表
function DailyConsumeView:CreateExtraRewardList()
	self.list_reward = AsyncListView.New(DailyConsumeRender, self.node_list.ph_list_reward)
end

-- 刷新可领取提示
function DailyConsumeView:RefreshRadioBtnTipsVisible()
	local day_consume_gold = DailyConsumeWGData.Instance:GetDayConsumeGold()
	if nil == day_consume_gold then
		return
	end

	for i = 1, 3 do
		local cfg = DailyConsumeWGData.Instance:GetDailyTotalRechargeReward(i)
		local fetch_flag = DailyConsumeWGData.Instance:GetTotalRechargeFetchRewardFlag(i - 1)
		local can_fetch_flag = DailyConsumeWGData.Instance:GetTotalRechargeCanFetchRewardFlag(i - 1)
		local is_can_get = false
		if cfg then
			is_can_get = can_fetch_flag == 1 and fetch_flag ~= 1
		end
		local remind_num = is_can_get and 1 or 0
		self.node_list["remind_point_"..i]:SetActive(remind_num > 0)
		-- Remind.Instance:DoRemind(RemindId.daily_consume)
	end
end

--刷新物品格子
function DailyConsumeView:RefreshItemSlot()
	local item_list = DailyConsumeWGData.Instance:GetDailyTotalRechargeReward(self.tab_index)
	if item_list == nil then return end
 
	for i = 1, #self.item_cell_list do
		self.item_cell_list[i]:SetData(item_list.reward_item[i - 1],0)
		if i == 1 then
			self.ph_cell_top:SetData(item_list.reward_item[i - 1],0)
		end
	end
end

-- 刷新领取奖励按钮
function DailyConsumeView:RefreshBtnFetch()
	local day_consume_gold = DailyConsumeWGData.Instance:GetDayConsumeGold()
	if nil == day_consume_gold then
		return
	end

	local cfg = DailyConsumeWGData.Instance:GetDailyTotalRechargeReward(self.tab_index)
	local fetch_flag = DailyConsumeWGData.Instance:GetTotalRechargeFetchRewardFlag(self.tab_index - 1)
	local can_fetch_flag = DailyConsumeWGData.Instance:GetTotalRechargeCanFetchRewardFlag(self.tab_index - 1)
	local need_recharge = cfg and cfg.need_consume_gold or 0
	if can_fetch_flag == 1 then
		self:SetBtnTitleImg(1) -- 领取奖励
		if fetch_flag ~= 1 then
			self:SetBtnEnabled(true)
		else
			self:SetBtnTitleImg(2)
			self:SetBtnEnabled(false)
			return
		end
	else
		self:SetBtnEnabled(true)
		self:SetBtnTitleImg(3) -- 前往消费
	end
end

-- 刷新按天奖励列表
function DailyConsumeView:RefreshListDayReward()
	local data_list = DailyConsumeWGData.Instance:GetDailyTotalRechargeRightReward()
	self.list_reward:SetDataList(data_list,0)

	-- 自动跳到未领取项
	local fetch_flag = DailyConsumeWGData.Instance:GetTotalRechargeTotalDayFetchRewardFlag()
	local cfg = ConfigManager.Instance:GetAutoConfig("daily_total_consume_auto").total_consume_day_reward
	for i = 1, #cfg do
		if fetch_flag[32 - i] ~= 1 then
			break
		end
	end
end

function DailyConsumeView:OnClickRecharge()
	local day_consume_gold = DailyConsumeWGData.Instance:GetDayConsumeGold()
	if nil == day_consume_gold then
		return
	end
	local cfg = DailyConsumeWGData.Instance:GetDailyTotalRechargeReward(self.tab_index)
	local need_recharge = cfg and cfg.need_consume_gold or 0
	local can_fetch_flag = DailyConsumeWGData.Instance:GetTotalRechargeCanFetchRewardFlag(self.tab_index - 1)
	if can_fetch_flag == 1 then
		ServerActivityWGCtrl.Instance:SendChongzhiFetchReward(RECHARGEREWARD_REQ_TYPE.DAILY_TOTAL_CONSUME, self.tab_index - 1, 0)
		AudioService.Instance:PlayRewardAudio()
	else
		ShopWGCtrl.Instance:Open()  -- 策划说直接跳到商城每周限购
	end
end

function DailyConsumeView:OnClickToggleRechargeNum(index)
	for i=1, 3 do
		if i == index then
			self.node_list["Image_light_"..i]:SetActive(true)
			self.node_list["img_money_"..i]:SetActive(true)
		else
			self.node_list["Image_light_"..i]:SetActive(false)
			self.node_list["img_money_"..i]:SetActive(false)
		end
	end
	local cfg = DailyConsumeWGData.Instance:GetDailyTotalRechargeReward(index)
	local costmoney = DailyConsumeWGData.Instance:GetDayConsumeGold()
	if costmoney > cfg.need_consume_gold then
		costmoney = cfg.need_consume_gold
	end
	self.node_list.recharge_gold_text.text.text = costmoney.."/"..cfg.need_consume_gold
	self.tab_index = index
	self:RefreshItemSlot()
	self:RefreshBtnFetch()
end


-- ItemRender --------------------------------
DailyConsumeRender = DailyConsumeRender or BaseClass(BaseRender)
function DailyConsumeRender:__init()
	--self.has_inited = false
end

function DailyConsumeRender:__delete()
	if self.cell_reward then
		self.cell_reward:DeleteMe()
		self.cell_reward = nil
	end

	if self.recharge_days then 
		self.recharge_days:DeleteMe()
		self.recharge_days = nil
	end
	--self.has_inited = false
end

function DailyConsumeRender:LoadCallBack()
	self.cell_reward = ItemCell.New(self.node_list.ph_cell_reward)
	XUI.AddClickEventListener(self.node_list.btn_fetch_daily, BindTool.Bind(self.OnClickBtnFetch, self))

end
function DailyConsumeRender:CreateChild()
	self.cell_reward = ItemCell.New(self.node_list.ph_cell_reward)
	XUI.AddClickEventListener(self.node_list.btn_fetch_daily, BindTool.Bind(self.OnClickBtnFetch, self))
end

function DailyConsumeRender:OnFlush()
	if not self.data then return end
	local recharge_days = DailyConsumeWGData.Instance:GetTotalRechargeTotalDay()
	local days = recharge_days > 3 and 3 or recharge_days 

	if  self.data.reward_item then
		self.cell_reward:SetData(self.data.reward_item)
		self.node_list.image_daily__reward_title.text.text = string.format(Language.Welfare.ConsumeYuanBao,self.data.total_consume_day,days,self.data.total_consume_day) 
	end
	local fetch_flag = self.data.fetch_flag
	if fetch_flag ~= nil then
		XUI.SetButtonEnabled(self.node_list["btn_fetch_daily"], fetch_flag == 0)
		if fetch_flag < 1 then
			self.node_list.btn_text.text.text = Language.Welfare.LingQu
		else
			self.node_list.btn_text.text.text = Language.Welfare.YiLingQu
		end
		
	end
end

function DailyConsumeRender:OnClickBtnFetch()
	ServerActivityWGCtrl.Instance:SendChongzhiFetchReward(RECHARGEREWARD_REQ_TYPE.TOTAL_CONSUME_DAY, self.index - 1)
	AudioService.Instance:PlayRewardAudio()
end

-- override
function DailyConsumeRender:CreateSelectEffect()
	
end
-- ItemRender --------------------------------