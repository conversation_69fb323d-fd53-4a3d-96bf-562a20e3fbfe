OperationActivityView = OperationActivityView or BaseClass(SafeBaseView)


local POT_TYPE = {
	["common_pot"] = 0,	-- 普通花盆
	["lover_pot"] = 1, 	-- 情侣花盆
}


-- 1:隐藏位置 2：显示位置
local FRIEND_LIST_POS_LIST = {[1] = Vector3(500, -6, 0), [2] = Vector3(-3, -6, 0)}

local WATERING_REWARD_COUNT = 5			--固定展示5个奖励.

function OperationActivityView:InitWateringFlowers()

end

function OperationActivityView:LoadIndexCallBackWateringFlowers()
	self.cur_panel_index = 1
	self.cur_select_toggle = 1
	self.is_show_friend_list = false
	self.is_all_invited = false
	self.item_cell_1 = {}
	--self.item_cell_2 = {}

	for i=1,2 do
		self.node_list["toggle_" .. i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickToggle, self, i))
	end

	self.node_list.watering_record_btn.button:AddClickListener(BindTool.Bind(self.OnClickWateringRecordBtn, self))
	self.node_list.help_btn.button:AddClickListener(BindTool.Bind(self.OnClickHelpBtn, self))
	self.node_list.all_invite_btn.button:AddClickListener(BindTool.Bind(self.OnClickAllInviteBtn, self))
	self.node_list.open_view_btn.button:AddClickListener(BindTool.Bind(self.OnClickOpenViewBtn, self))
	self.node_list.back_selfpot_btn.button:AddClickListener(BindTool.Bind(self.OnClickBackSelfPotBtn, self))
	self.node_list.back_btn_2.button:AddClickListener(BindTool.Bind(self.OnClickBackBtn, self))
	for i = 1, 2 do
		self.node_list["qiehuan_btn_" .. i].button:AddClickListener(BindTool.Bind(self.OnClickQieHuanBtn, self, i))
	end


	if not self.friend_list_view then
		self.friend_list_view = AsyncListView.New(WateringFriendRender, self.node_list.water_flower_list_view)
	end

	self.node_list["friend_obj_root"].transform.localPosition = FRIEND_LIST_POS_LIST[1]
	self.node_list.back_btn_2:SetActive(false)
	
	WateringFlowersWGCtrl.Instance:SendAllFirendAndMemberFlowerInfo()
	-- WateringFlowersWGCtrl:CSOAWaterFlowerInfo(OA_WATER_FLOWER_OPERA_TYPE.OA_WATER_FLOWER_OPERA_TYPE_OTHER_HELP_ME_INFO_REQ)
	
	WateringFlowersWGData.Instance:SetGardenFlag(0)

	self:FlushWateringFlowersPicture()

	self:CreateFlowersPot()
	self:OnClickQieHuanBtn(1)
end

-- 创建花盆
function OperationActivityView:CreateFlowersPot()
	if not self.flowers_pot_list then
		self.flowers_pot_list = {}
		local obj_name = "flowers_pot_obj"
		local res_async_loader = AllocResAsyncLoader(self, obj_name)
		res_async_loader:Load("uis/view/operation_watering_flowers_prefab", obj_name, nil, function(new_obj)
			for i=1,2 do
				local obj = ResMgr:Instantiate(new_obj)
				local obj_transform = obj.transform
				obj_transform:SetParent(self.node_list["flowers_pot_" .. i].transform, false)
				local flowers_pot = FlowersPotRender.New(obj)
				flowers_pot.parent_view = self
				flowers_pot:SetIndex(i)
				self.flowers_pot_list[i] = flowers_pot
			end
			self:FlushMidPortion()
		end)
	end
end

function OperationActivityView:FlushWaterTextInvite()
    if self.friend_list_view then
        if not IsEmptyTable(self.friend_list_view.cell_list) then
            for k, v in pairs(self.friend_list_view.cell_list) do
                v:FlushTextInvite()
            end
        end
    end
end

function OperationActivityView:DeleteWateringFlowers()
	self.cur_select_toggle = nil

	if self.flowers_pot_list then
		for k,v in pairs(self.flowers_pot_list) do
			v:DeleteMe()
		end

		self.flowers_pot_list = nil
	end

	if self.friend_list_view then
		self.friend_list_view:DeleteMe()
		self.friend_list_view = nil
	end

	if self.item_cell_1 then
		for k,v in pairs(self.item_cell_1) do
			v:DeleteMe()
		end
		self.item_cell_1 = nil
	end

	-- if self.item_cell_2 then
	-- 	for k,v in pairs(self.item_cell_2) do
	-- 		v:DeleteMe()
	-- 	end
	-- 	self.item_cell_2 = nil
	-- end
	if self.node_list.all_invite_btn then
		XUI.SetButtonEnabled(self.node_list.all_invite_btn, true)
	end

	if CountDownManager.Instance:HasCountDown("all_invite_delaytime") then
		CountDownManager.Instance:RemoveCountDown("all_invite_delaytime")
	end
end

function OperationActivityView:ShowIndexCallWateringFlowers()
	WateringFlowersWGData.Instance:SetGardenFlag(0)

	if not self.water_flower_interface_cfg then
		return 
	end

	self:SetRuleInfo(WateringFlowersWGData.Instance:GetInterfaceDesc("desc_text_25", self.cur_panel_index == 2), WateringFlowersWGData.Instance:GetInterfaceDesc("desc_text_26"))
	self:SetOutsideRuleTips(WateringFlowersWGData.Instance:GetInterfaceDesc("desc_text_34", self.cur_panel_index == 2))

	for i=1,2 do
		if self.flowers_pot_list[i] then
			self.flowers_pot_list[i]:SetKettleActive(false)
		end
	end
end

function OperationActivityView:FlushWateringFlowersPicture()
	self.water_flower_interface_cfg = WateringFlowersWGData.Instance:GetInterfaceCfg()
	self:InitWaterFlowerPictureAndDescText()
end

function OperationActivityView:InitWaterFlowerPictureAndDescText()
	if not self.water_flower_interface_cfg then
		return 
	end

	-- self.node_list.title_name.text.text = self.water_flower_interface_cfg.desc_text_1
	-- self.node_list.title_tips.text.text = self.water_flower_interface_cfg.desc_text_2
	-- self.node_list.record_btn_text.text.text = self.water_flower_interface_cfg.desc_text_15
	-- self.node_list.toggle_text_1.text.text = self.water_flower_interface_cfg.desc_text_20
	-- self.node_list.toggle_text_2.text.text = self.water_flower_interface_cfg.desc_text_21
	
	-- self.node_list.watering_record_btn.image:LoadSprite(ResPath.GetOperationWateringFlower(self.water_flower_interface_cfg.pic_3))
	-- self.node_list.watering_record_btn.image:SetNativeSize()
	-- self.node_list.help_btn.image:LoadSprite(ResPath.GetOperationWateringFlower(self.water_flower_interface_cfg.pic_4))
	-- self.node_list.help_btn.image:SetNativeSize()
	-- self.node_list.toggle_bg_1.image:LoadSprite(ResPath.GetOperationWateringFlower(self.water_flower_interface_cfg.pic_6))
	-- self.node_list.toggle_bg_1.image:SetNativeSize()
	-- self.node_list.toggle_bg_2.image:LoadSprite(ResPath.GetOperationWateringFlower(self.water_flower_interface_cfg.pic_6))
	-- self.node_list.toggle_bg_2.image:SetNativeSize()
	-- self.node_list.toggle_bg_hl_1.image:LoadSprite(ResPath.GetOperationWateringFlower(self.water_flower_interface_cfg.pic_7))
	-- self.node_list.toggle_bg_hl_1.image:SetNativeSize()
	-- self.node_list.toggle_bg_hl_2.image:LoadSprite(ResPath.GetOperationWateringFlower(self.water_flower_interface_cfg.pic_7))
	-- self.node_list.toggle_bg_hl_2.image:SetNativeSize()
	-- self.node_list.all_invite_btn.image:LoadSprite(ResPath.GetCommonButtonToggle_atlas(self.water_flower_interface_cfg.pic_9))
	-- self.node_list.all_invite_btn.image:SetNativeSize()

	-- self.node_list.back_selfpot_btn.image:LoadSprite(ResPath.GetOperationWateringFlower(self.water_flower_interface_cfg.pic_35))
	-- self.node_list.back_selfpot_btn.image:SetNativeSize()

	-- self.node_list.flower_yezi.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(self.water_flower_interface_cfg.pic_31))
	-- self.node_list.flower_yezi.raw_image:SetNativeSize()
	--self.node_list.friend_obj_root.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(self.water_flower_interface_cfg.pic_5))
	-- self.node_list.friend_obj_root.raw_image:SetNativeSize()
end

function OperationActivityView:FlushWateringFlowers()
	local garden_flag = WateringFlowersWGData.Instance:GetGardenFlag()
	if garden_flag == 1 then
		--别人花园的信息
		self.water_flower_info = WateringFlowersWGData.Instance:GetOtherWaterFlowerInfo()
	else
		--自己花园的信息
		self.water_flower_info = WateringFlowersWGData.Instance:GetWaterFlowerInfo()
	end

	self:FlushTopPortion()
	self:FlushMidPortion()
	self:FlushBottomPortion()
	self:FlushRightPortion()
	self:FlushFriendListView()
	self:FlushPaneRemind()
end

function OperationActivityView:WateringFlowersShowIndexAndCloseCallBack()
	WateringFlowersWGData.Instance:SetGardenFlag(0)
	WateringFlowersWGCtrl.Instance:CSOAWaterFlowerInfo(OA_WATER_FLOWER_OPERA_TYPE.OA_WATER_FLOWER_OPERA_TYPE_INFO_REQ)
end

-------------------------------------------------------

--刷新顶部名称
function OperationActivityView:FlushTopPortion()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()

	self.node_list.watering_record_btn:SetActive(self.water_flower_info.uid == role_id)
	self.node_list.help_btn:SetActive(self.water_flower_info.uid == role_id)
	
	self.node_list.friend_name_bg:SetActive(self.water_flower_info.uid ~= role_id)
	self.node_list.friend_name:SetActive(self.water_flower_info.uid ~= role_id)

	self.node_list.back_selfpot_btn:SetActive(self.water_flower_info.uid ~= role_id)

	if self.water_flower_interface_cfg then
		self.node_list.friend_name.text.text = string.format(WateringFlowersWGData.Instance:GetInterfaceDesc("desc_text_3", self.cur_panel_index == 2), self.water_flower_info.name)
	end
end

--刷新中间两个盆栽
function OperationActivityView:FlushMidPortion()
	if IsEmptyTable(self.water_flower_info) or IsEmptyTable(self.water_flower_info.flower_list) then
		return
	end

	for i=1,2 do
		if self.flowers_pot_list[i] and self.water_flower_info.flower_list[i] then
			self.flowers_pot_list[i]:SetData(self.water_flower_info.flower_list[i])
		end
	end
end

function OperationActivityView:FlushPaneRemind()
	if IsEmptyTable(self.water_flower_info) or IsEmptyTable(self.water_flower_info.flower_list) then
		return
	end

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local graden_uid = WateringFlowersWGData.Instance:GetCurGardenUid()
	local is_remind = 0

	for i = 1,2 do
		if role_id == graden_uid then
			is_remind = self:GetPaneRemind(i)
		end
		self.node_list["pane_red_" .. i]:SetActive(is_remind == 1)
	end
end

function OperationActivityView:GetPaneRemind(index)
	if IsEmptyTable(self.water_flower_info) or IsEmptyTable(self.water_flower_info.flower_list) then
		return
	end

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local is_remind = 0

	if role_id > 0 then -- 无种植红点
		if self.water_flower_info.flower_list[index].status == FLOWER_STATUS.FLOWER_STATUS_NOTHING then
			if index == 2 then -- 情侣花盆
				local lover_id = RoleWGData.Instance:GetAttr("lover_uid") or 0
				is_remind = lover_id > 0 and 1 or 0
			else
				is_remind = 1
			end
			return is_remind
		end
	else
		if index == 1 and self.water_flower_info.flower_list[index].status == FLOWER_STATUS.FLOWER_STATUS_NOTHING then
			is_remind = 1
			return is_remind
		end
	end

	if self.water_flower_info.flower_list[index].status == FLOWER_STATUS.FLOWER_STATUS_RIPE then -- 可领取红点
		is_remind = 1
		return is_remind
	else
		local help_me_list = WateringFlowersWGData.Instance:GetHlepMeDataList(index) -- 帮助列表
		if not IsEmptyTable(help_me_list) then
			is_remind = 1
			return is_remind
		end
	end
	return is_remind
end


-- 刷新文本按钮部分
function OperationActivityView:FlushText()
	self.node_list.text_help.text.text = Language.OpertionAcitvity.WateringFlowers.InviteWateringBtnTextList[self.cur_panel_index]
end

--刷新底部部分
function OperationActivityView:FlushBottomPortion()
	-- self:ClearWateringFlowerActTime()

	local other_cfg = WateringFlowersWGData.Instance:GetOtherCfg()

	if not other_cfg then
		return
	end

	local watering_reward_list = self:WFSortItemList(other_cfg.watering_reward_show)
	local content_parent = self.node_list.watering_reward_content
	for i=1, WATERING_REWARD_COUNT do
		if not self.item_cell_1[i] then
			self.item_cell_1[i] = ItemCell.New(content_parent:FindObj("cell_" .. i))
			self.item_cell_1[i]:SetIsUseRoundQualityBg(true)
			self.item_cell_1[i]:SetCellBgEnabled(false)
		end
		self.item_cell_1[i]:SetData(watering_reward_list[i]) 
	end

	-- local ripe_reward_list = self:WFSortItemList(other_cfg.ripe_reward_show)
	-- for i=1,#ripe_reward_list do
	-- 	if not self.item_cell_2[i] then
	-- 		self.item_cell_2[i] = ItemCell.New(self.node_list.harvest_reward_content)
	-- 	end
	-- 	self.item_cell_2[i]:SetData(ripe_reward_list[i]) 
	-- end

	local remain_water_times = WateringFlowersWGData.Instance:GetTodayRemainWaterTimes2(self.cur_panel_index)
	local help_other_times = WateringFlowersWGData.Instance:GetTodayRemainHelpOtherTimes2(self.cur_panel_index)

	if self.water_flower_interface_cfg then
		local color = help_other_times > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.RED
		self.node_list.help_times.text.text = string.format(WateringFlowersWGData.Instance:GetInterfaceDesc("desc_text_13", self.cur_panel_index == 2), ToColorStr(help_other_times, color))
		color = remain_water_times > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.RED
		self.node_list.watering_times.text.text = string.format(WateringFlowersWGData.Instance:GetInterfaceDesc("desc_text_12", self.cur_panel_index == 2), ToColorStr(remain_water_times, color))
	end

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPERA_ACT_WATERING_FLOWERS)
	if not activity_info or activity_info.end_time == -1 then
		return
	end

	self:SetActRemainTime(TabIndex.operation_act_watering_flowers, activity_info.end_time)
end

function OperationActivityView:WFSortItemList(sort_tab)
	local temp_data_list = {}
	for i = 0, #sort_tab do
		temp_data_list[i + 1] = {}
		temp_data_list[i + 1] = sort_tab[i]
		if sort_tab[i] and sort_tab[i].item_id then
			local _, color = ItemWGData.Instance:GetItemColor(sort_tab[i].item_id)
			temp_data_list[i + 1].color = color
		end
	end
	SortTools.SortDesc(temp_data_list, "color")
	return temp_data_list
end

-- 刷新右边部分
function OperationActivityView:FlushRightPortion()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()

	self.node_list.watering_record_btn:SetActive(self.water_flower_info.uid == role_id)

	local is_be_invited_lsit = {}

	for i = 1, 2 do
		is_be_invited_lsit[i] = WateringFlowersWGData.Instance:GetIsBeInveiteByType(i)
		self.node_list["toggle_remind_" .. i]:SetActive(is_be_invited_lsit[i] == true)
	end
	self.node_list.help_btn_remind:SetActive(is_be_invited_lsit[1] == true or is_be_invited_lsit[2] == true)
end

--刷新好友列表
function OperationActivityView:FlushFriendListView()
	local self_flower_info = WateringFlowersWGData.Instance:GetWaterFlowerInfo()
	local count = 0
	for i=1,2 do
		if self_flower_info.flower_list and self_flower_info.flower_list[i] then
			if self_flower_info.flower_list[i].status ~= FLOWER_STATUS.FLOWER_STATUS_GROWING then
				count = count + 1
			end
		end
	end
	if not self.is_all_invited then
		XUI.SetButtonEnabled(self.node_list.all_invite_btn, count < 2)
	end

	local data_list

	if self.cur_select_toggle == 1 then
		data_list = WateringFlowersWGData.Instance:GetWateringFlowersFriendList()
	else
		data_list = WateringFlowersWGData.Instance:GetWateringFlowersMemberList()
	end
	self.node_list.common_no_data_panel:SetActive(IsEmptyTable(data_list))
	self.node_list.open_view_btn:SetActive(IsEmptyTable(data_list))
	self.node_list.all_invite_btn:SetActive(not IsEmptyTable(data_list))
	self.node_list.open_view_btn_text.text.text = Language.OpertionAcitvity.WateringFlowers.OpenViewBtn[self.cur_select_toggle]
	self.friend_list_view:SetDataList(data_list)
end

-- 浇水记录按钮
function OperationActivityView:OnClickWateringRecordBtn()
	WateringFlowersWGCtrl.Instance:OpenWateringRecordView()
end

-- 右边好友协助按钮
function OperationActivityView:OnClickHelpBtn()
	--帮派id不为0得时候请求一下成员列表信息
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	if 0 ~= role_vo.guild_id then
		GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, role_vo.guild_id)
	end	
	WateringFlowersWGCtrl.Instance:SendAllFirendAndMemberFlowerInfo()

	local do_pos = self.node_list["friend_obj_root"].transform:DOLocalMoveX(FRIEND_LIST_POS_LIST[2].x, 0.2)
	UITween.AlpahShowPanel(self.node_list["friend_obj_root"].gameObject , true , 0.2, nil,function ()
		self.node_list["friend_obj_root"].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup)).alpha = 1
		self.node_list["friend_obj_root"].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup)).blocksRaycasts = true
	end)
	do_pos:SetEase(DG.Tweening.Ease.OutBack)
	self.node_list["right_obj"]:SetActive(false)
	self.node_list.back_btn_2:SetActive(true)

	for i=1,2 do
		if self.flowers_pot_list[i] then
			self.flowers_pot_list[i]:SetKettleActive(false)
		end
	end
end

function OperationActivityView:OnClickToggle(index, is_on)
	if self.cur_select_toggle ~= index and is_on then
		self.cur_select_toggle = index
		self:FlushFriendListView()
	end
end

function OperationActivityView:OnClickOpenViewBtn()
	local data_list
	if self.cur_select_toggle == 1 then
		data_list = WateringFlowersWGData.Instance:GetWateringFlowersFriendList()
	else
		data_list = WateringFlowersWGData.Instance:GetWateringFlowersMemberList()
	end

	if IsEmptyTable(data_list) then
		if self.cur_select_toggle == 1 then
			ViewManager.Instance:Open(GuideModuleName.OpenKeyAddFriend)
		else
			FunOpen.Instance:OpenViewByName(GuideModuleName.Guild)
		end
	end
end

function OperationActivityView:OnClickAllInviteBtn()
	if CountDownManager.Instance:HasCountDown("all_invite_delaytime") then
		CountDownManager.Instance:RemoveCountDown("all_invite_delaytime")
	end

	local data_list1 = WateringFlowersWGData.Instance:GetWateringFlowersFriendList()
	local data_list2 = WateringFlowersWGData.Instance:GetWateringFlowersMemberList()

	--好友列表和仙盟列表都为空
	if IsEmptyTable(data_list1) and IsEmptyTable(data_list2) then
		TipWGCtrl.Instance:ShowSystemMsg(Language.OpertionAcitvity.WateringFlowers.TipsDesc1)
		return
	end
    local other_cfg = WateringFlowersWGData.Instance:GetOtherCfg()

	if not other_cfg then
		return
    end
    
	local uid_list = {}
	local count = 0
	local friend_list = SocietyWGData.Instance:GetSCFriendList()
    local min_cd = other_cfg.cd
	if friend_list and not IsEmptyTable(friend_list) then
        for k,v in pairs(friend_list) do
            local cur_cd = WateringFlowersWGData.Instance:GetCacheCDByRoleid(v.user_id)
            if v.user_id > 0 then
                if cur_cd <= 0 then
                    count = count + 1
                    WateringFlowersWGData.Instance:AddCacheCDList(v.user_id, other_cfg.cd)
                    table.insert(uid_list, v.user_id)
                else
                    min_cd = min_cd >= cur_cd and cur_cd or min_cd
                end
			end
		end
	end

	local member_list = GuildDataConst.GUILD_MEMBER_LIST
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()

	if member_list and member_list.list and not IsEmptyTable(member_list.list) then
		for k,v in pairs(member_list.list) do
            if not IsEmptyTable(v) and v.uid > 0 then
                local cur_cd = WateringFlowersWGData.Instance:GetCacheCDByRoleid(v.uid)
                if v.uid ~= role_id then
                    if cur_cd <= 0 then
                        count = count + 1
                        WateringFlowersWGData.Instance:AddCacheCDList(v.uid, other_cfg.cd)
                        table.insert(uid_list, v.uid)
                    else
                        min_cd = min_cd >= cur_cd and cur_cd or min_cd
                    end
                end
			end
		end
	end
    if #uid_list > 0 then
        WateringFlowersWGCtrl.Instance:CSOAWaterFlowerHelpFriendReqInfo(QUERY_TYPE.QUERY_TYPE_INVITE, count, uid_list)
        TipWGCtrl.Instance:ShowSystemMsg(Language.OpertionAcitvity.WateringFlowers.AllInviteSuccess)
    end
    self.is_all_invited = true
    if min_cd > 0 then
	    CountDownManager.Instance:AddCountDown("all_invite_delaytime", BindTool.Bind1(self.AllInviteDelayTime, self), 
                                            BindTool.Bind1(self.CompleteAllInviteDelayTime, self), nil, min_cd, 1)
        self:AllInviteDelayTime(0, min_cd)
    else
        self:CompleteAllInviteDelayTime()
    end

end

function OperationActivityView:AllInviteDelayTime(elapse_time, total_time)
	local time = math.ceil(total_time - elapse_time)

	if time >= 0 then
		XUI.SetButtonEnabled(self.node_list.all_invite_btn, false)
        self.node_list.all_invite_btn_text.text.text = WateringFlowersWGData.Instance:GetInterfaceDesc("desc_text_28") .. string.format("(%s)", time)
	end
end

function OperationActivityView:CompleteAllInviteDelayTime()
	XUI.SetButtonEnabled(self.node_list.all_invite_btn, true)
	self.node_list.all_invite_btn_text.text.text = WateringFlowersWGData.Instance:GetInterfaceDesc("desc_text_28")
	self.is_all_invited = false
end

function OperationActivityView:OnClickBackBtn()
	local do_pos = self.node_list["friend_obj_root"].transform:DOLocalMoveX(FRIEND_LIST_POS_LIST[1].x, 0.2)
	UITween.AlpahShowPanel(self.node_list["friend_obj_root"].gameObject , false , 0.2, nil,function ()
		self.node_list["friend_obj_root"].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup)).alpha = 0
		self.node_list["friend_obj_root"].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup)).blocksRaycasts = false
	end)
	do_pos:SetEase(DG.Tweening.Ease.OutBack)
	self.node_list["right_obj"]:SetActive(true)
	self.node_list.back_btn_2:SetActive(false)
end

function OperationActivityView:OnClickBackSelfPotBtn()
	WateringFlowersWGData.Instance:SetGardenFlag(0)
	WateringFlowersWGCtrl.Instance:CSOAWaterFlowerInfo(OA_WATER_FLOWER_OPERA_TYPE.OA_WATER_FLOWER_OPERA_TYPE_INFO_REQ)

	for i=1,2 do
		if self.flowers_pot_list[i] then
			self.flowers_pot_list[i]:SetKettleActive(false)
		end
	end
end
function OperationActivityView:OnClickQieHuanBtn(index)
	for i = 1, 2 do
		self.node_list["pane_" .. i]:SetActive(index == i)
		self.node_list["watering_top_img_" .. i]:SetActive(index == i)
	end
	self.cur_panel_index = index
	self:FlushBottomPortion()
	self:FlushText()
end

--====================================================FlowersPotRender(花盆)====================================================--
FlowersPotRender = FlowersPotRender or BaseClass(BaseRender)

function FlowersPotRender:__init()
	self.node_list.plant_btn.button:AddClickListener(BindTool.Bind(self.OnClickPlantBtn, self))
	self.node_list.watering_btn.button:AddClickListener(BindTool.Bind(self.OnClickWateringBtn, self))
	self.node_list.invite_watering_btn.button:AddClickListener(BindTool.Bind(self.OnClickInviteWateringBtn, self))
	self.node_list.reward_box.button:AddClickListener(BindTool.Bind(self.OnClickRewardBox, self))

	self.help_me_list = {}
	for i=1,4 do
		self.help_me_list[i] = {}
		self.help_me_list[i].button = self.node_list["help_player_" .. i]
		self.help_me_list[i].name = self.node_list["player_name_" .. i]
		self.help_me_list[i].button.button:AddClickListener(BindTool.Bind(self.OnClickHelpPlayer, self, i))
	end

	self.alert_tips = Alert.New()

	self.node_list.kettle_model:SetActive(false)

	self.interface_cfg = WateringFlowersWGData.Instance:GetInterfaceCfg()
	self:InitPictureAndDescText()

	self.flower_model_cfg = nil

	self.begin_time = 0
	self.is_complete = false
end

function FlowersPotRender:__delete()
	self:ClearCountDown()
	self:ClearNextWaterTime()

	if self.alert_tips then
		self.alert_tips:DeleteMe()
		self.alert_tips = nil
	end

	-- if self.node_list.watering_btn then
	-- 	XUI.SetButtonEnabled(self.node_list.watering_btn, true)
	-- end

	self.flower_model_cfg = nil

	-- if self.water_delay_time then
	-- 	GlobalTimerQuest:CancelQuest(self.water_delay_time)
	-- 	self.water_delay_time = nil
	-- end
	self:KillKettleWaterEffTime()

	self.is_complete = false

	self.parent_view = nil
	-- XUI.SetGraphicGrey(self.node_list.watering_btn, false)

	if self.gift_box_tween then
		self.gift_box_tween:Kill()
		self.gift_box_tween = nil
	end

	self:CancelTween()
end

function FlowersPotRender:InitPictureAndDescText()
	if not self.interface_cfg then
		return
	end
end

function FlowersPotRender:OnFlush()
	self:ClearCountDown()
	self:ClearNextWaterTime()
	self:PlayBoxAni()

	if not self.data then
		return
	end

	self.flower_model_cfg = nil

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	local graden_uid = WateringFlowersWGData.Instance:GetCurGardenUid()

	-- XUI.SetGraphicGrey(self.node_list.watering_btn, false)
	self.node_list.watering_btn:SetActive(true)
	self.node_list.watering_times_bg:SetActive(true)
	self.node_list.invite_watering_btn:SetActive(true)

	if self.interface_cfg then
		local penzai_name_str = self.index == 2 and Language.OpertionAcitvity.WateringFlowers.PenZai_Name_2 or Language.OpertionAcitvity.WateringFlowers.PenZai_Name_1
		self.node_list.text_penzai_name.text.text = penzai_name_str
		self.node_list.plant_btn_text.text.text =  self.index == 2 and Language.OpertionAcitvity.WateringFlowers.HeChaName2 or Language.OpertionAcitvity.WateringFlowers.HeChaName1
		if self.data.status == FLOWER_STATUS.FLOWER_STATUS_NOTHING then 
			self.node_list.pot_bg.image:LoadSprite(ResPath.GetOperationWateringFlower("a3_xskh_cahu_" .. self.index))
			self.node_list.pot_bg.image:SetNativeSize()
		else
			self.node_list.pot_bg.image:LoadSprite(ResPath.GetOperationWateringFlower("a3_xskh_cahu_" .. self.index))
			self.node_list.pot_bg.image:SetNativeSize()
		end
	end

	local flower_cfg = WateringFlowersWGData.Instance:GetFlowerCfgByType(self.index - 1)

	if flower_cfg then
		self.begin_time = self.data.can_fetch_reward_timestamp - (flower_cfg.ripe_time * 60)
	end

	self.node_list.watering_btn_text.text.text = role_id == graden_uid and
		Language.OpertionAcitvity.WateringFlowers.WateringBtnText1 or Language.OpertionAcitvity.WateringFlowers.WateringBtnTextList[self.index]

	self.node_list.invite_watering_btn_text.text.text = Language.OpertionAcitvity.WateringFlowers.InviteWateringBtnTextList[self.index]

	--不在自己的花园
	if role_id ~= graden_uid then
		for k,v in pairs(self.help_me_list) do
			v.button:SetActive(false)
		end

		local can_watering = self.data.status == FLOWER_STATUS.FLOWER_STATUS_GROWING

		self.node_list.plant_btn:SetActive(false)
		self.node_list.invite_watering_btn:SetActive(false)
		self.node_list.maturity_remain_time:SetActive(false)
		self.node_list.bg_maturity_remain_time:SetActive(false)
		self.node_list.reward_box:SetActive(false)

		self.node_list.flower_bubble_box:SetActive(self.data.status == FLOWER_STATUS.FLOWER_STATUS_FETCH)

		--贼他妈绕，WDNMD
		--今日被浇剩余次数，被浇总次数
		local remain_times, max_times = WateringFlowersWGData.Instance:GetOtherTodayRemindWaterTimesByType(self.index)
		--自己剩余协助他人浇水次数，剩余协助他人浇水总次数（盆栽表-help_other_times）
		local self_remain_times, slef_max_times = WateringFlowersWGData.Instance:GetOtherTodayRemindWaterTimesByTypeTwo(self.index)
		--自己剩余协助次数（根据类型）,自己最大协助次数（根据类型）（其他表-help_other_times）
		local self_remain_help_times, self_max_help_times = WateringFlowersWGData.Instance:GetTodayRemainHelpOtherTimesByType(self.index)

		if self.interface_cfg then
			-- A代指本人，B指好友1，C指好友2
			--浇水按钮显示-- A 剩余协助他人浇水次数/剩余协助他人浇水总次数
			self.node_list.watering_times.text.text = string.format("%s/%s", self_remain_times, slef_max_times)
		end

		if self.index == 2 then
			-- 1)B仙侣盆栽other_times≤0，浇水按钮置灰----点击提示--***花今日被协助浇水次数已达上限
			-- 2)A对B仙侣盆栽今天浇水次数≥help_lover_times，浇水按钮置灰----点击提示--今日对仙侣***花的协助浇水次数已达上限
			-- 3)A剩余协助仙侣次数为0，浇水按钮置灰----点击提示--今日协助仙侣浇水次数已达上限
			local temp_boo = (can_watering and remain_times > 0 and self_remain_times > 0 and self_remain_help_times > 0 and lover_id == graden_uid)
			-- XUI.SetGraphicGrey(self.node_list.watering_btn, not temp_boo)
			self.node_list.watering_times_bg:SetActive(can_watering and lover_id == graden_uid)
		else
			--1)B普通盆栽other_times≤0，浇水按钮置灰----点击提示--***花今日被协助浇水次数已达上限	
			--2)A对B普通盆栽今天浇水次数≥help_other_times，浇水按钮置灰----点击提示--今日对好友**花的协助浇水次数已达上限	
			--3)A剩余协助他人次数为0，浇水按钮置灰----点击提示--今日协助好友浇水次数已达上限
			local temp_boo = (can_watering and remain_times > 0 and self_remain_times > 0 and self_remain_help_times > 0)
			-- XUI.SetGraphicGrey(self.node_list.watering_btn, not temp_boo)
			self.node_list.watering_times_bg:SetActive(can_watering)
		end

		local time = self.data.can_fetch_reward_timestamp - TimeWGCtrl.Instance:GetServerTime()

		if time > 0 then
			self.node_list.flower_model:SetActive(true)
			self:FlowerCanFetchTime(0, time)

			CountDownManager.Instance:AddCountDown("flower_can_fetch_time_" .. self.index, BindTool.Bind1(self.FlowerCanFetchTime, self), BindTool.Bind1(self.CompleteFlowerCanFetchTime, self), nil, time, 1)
		else
			self.node_list.flower_model:SetActive(self.data.status == FLOWER_STATUS.FLOWER_STATUS_RIPE)

			if self.data.status == FLOWER_STATUS.FLOWER_STATUS_RIPE then

				if flower_cfg then
					self:FlushFlowerModel(flower_cfg, self.is_complete)
				end
			end
		end

		return
	end

	--在自己的花园
	self:FlushHelpMeList()
	-- 自己浇水次数
	local remain_times, max_times = WateringFlowersWGData.Instance:GetTodayRemainWaterTimesByType(self.index)
	-- 被浇水次数
	local be_water_remain_times, be_water_max_times = WateringFlowersWGData.Instance:GetTodayRemindBeWaterdTimesByType(self.index)

	if self.interface_cfg then
		self.node_list.watering_times.text.text = string.format("%s/%s", remain_times, max_times)
		self.node_list.pot_name.text.text = self.index == 1 and self.interface_cfg.desc_text_4 or self.interface_cfg.desc_text_5
		self.node_list.invite_times.text.text = string.format("%s/%s", be_water_remain_times, be_water_max_times)
	end

	if self.data.status == FLOWER_STATUS.FLOWER_STATUS_NOTHING then --无
		self.node_list.plant_btn:SetActive(true)
		self.node_list.watering_btn:SetActive(false)
		self.node_list.invite_watering_btn:SetActive(false)
		self.node_list.maturity_remain_time:SetActive(false)
		self.node_list.bg_maturity_remain_time:SetActive(false)
		self.node_list.reward_box:SetActive(false)
	elseif self.data.status == FLOWER_STATUS.FLOWER_STATUS_GROWING then --成长期
		self.node_list.plant_btn:SetActive(false)
		-- self.node_list.watering_btn:SetActive(remain_times > 0)
		-- self.node_list.invite_watering_btn:SetActive(be_water_remain_times > 0)
		self.node_list.maturity_remain_time:SetActive(true)
		self.node_list.bg_maturity_remain_time:SetActive(true)
		self.node_list.reward_box:SetActive(true)
	elseif self.data.status == FLOWER_STATUS.FLOWER_STATUS_RIPE then --未领取
		self.node_list.plant_btn:SetActive(false)
		-- self.node_list.watering_btn:SetActive(false)
		-- self.node_list.invite_watering_btn:SetActive(false)
		self.node_list.maturity_remain_time:SetActive(false)
		self.node_list.bg_maturity_remain_time:SetActive(false)
		self.node_list.reward_box:SetActive(true)
	elseif self.data.status == FLOWER_STATUS.FLOWER_STATUS_FETCH then --已领取
		self.node_list.plant_btn:SetActive(false)
		-- self.node_list.watering_btn:SetActive(false)
		-- self.node_list.invite_watering_btn:SetActive(false)
		self.node_list.maturity_remain_time:SetActive(false)
		self.node_list.bg_maturity_remain_time:SetActive(false)
		self.node_list.reward_box:SetActive(false)
	end

	self.node_list.flower_bubble_box:SetActive(self.data.status == FLOWER_STATUS.FLOWER_STATUS_FETCH)
	self.node_list.box_red:SetActive(self.data.status == FLOWER_STATUS.FLOWER_STATUS_RIPE)
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	self.node_list["plant_remind"]:SetActive(self.data.status == FLOWER_STATUS.FLOWER_STATUS_NOTHING and (self.index ~= 2) or lover_id > 0)

	local time = self.data.can_fetch_reward_timestamp - TimeWGCtrl.Instance:GetServerTime()
	if time > 0 then
		self.node_list.flower_model:SetActive(true)
		self:FlowerCanFetchTime(0, time)
		CountDownManager.Instance:AddCountDown("flower_can_fetch_time_" .. self.index, BindTool.Bind1(self.FlowerCanFetchTime, self), BindTool.Bind1(self.CompleteFlowerCanFetchTime, self), nil, time, 1)
	else
		self.node_list.flower_model:SetActive(self.data.status == FLOWER_STATUS.FLOWER_STATUS_RIPE)
		if self.data.status == FLOWER_STATUS.FLOWER_STATUS_RIPE then
			if flower_cfg then
				self:FlushFlowerModel(flower_cfg, self.is_complete)
			end
		end
	end

	local next_water_time = self.data.next_water_timestamp - TimeWGCtrl.Instance:GetServerTime()
	-- print_error("FFF========== 时间??", self.data.next_water_timestamp - TimeWGCtrl.Instance:GetServerTime(), self.data.next_water_timestamp, TimeWGCtrl.Instance:GetServerTime())
	if next_water_time > 0 then
		CountDownManager.Instance:AddCountDown("next_water_time_" .. self.index, BindTool.Bind1(self.NextWaterTime, self), BindTool.Bind1(self.CompleteNextWaterTime, self), nil, next_water_time, 1)
		self:NextWaterTime(0, next_water_time)
	end
end

-- 领取成熟奖励时间
function FlowersPotRender:FlowerCanFetchTime(elapse_time, total_time)
	local time = math.floor(total_time - elapse_time)

	if time > 0 and self.node_list.maturity_remain_time and self.node_list.maturity_remain_slider and self.interface_cfg then
		self.node_list.maturity_remain_time.text.text = string.format(Language.OpertionAcitvity.WateringFlowers.RewardTime, TimeUtil.FormatSecond(time, 3))
		
		local min_time = math.ceil(time / 60)

		if not self.flower_model_cfg then
			self.flower_model_cfg = WateringFlowersWGData.Instance:GetFlowerModelCfgByTypeAndRemainTime(self.index, min_time)
			self:FlushFlowerModel(self.flower_model_cfg)
		else
			if self.flower_model_cfg.remain_time_1 >= min_time then
				self.flower_model_cfg = WateringFlowersWGData.Instance:GetFlowerModelCfgByTypeAndRemainTime(self.index, min_time)
				self:FlushFlowerModel(self.flower_model_cfg, true)
			end
		end

		self.node_list.maturity_remain_slider.slider.value = (TimeWGCtrl.Instance:GetServerTime() - self.begin_time) / (self.data.can_fetch_reward_timestamp - self.begin_time)
	end
end

function FlowersPotRender:FlushFlowerModel(flower_model_cfg, is_play_anim)
	if flower_model_cfg then
		if self.is_complete == true then
			self.is_complete = false
		end
	end
end

function FlowersPotRender:CompleteFlowerCanFetchTime()
	self.is_complete = true
	self:ClearCountDown()
	self:Flush()
end

function FlowersPotRender:ClearCountDown()
	if CountDownManager.Instance:HasCountDown("flower_can_fetch_time_" .. self.index) then
		CountDownManager.Instance:RemoveCountDown("flower_can_fetch_time_" .. self.index)
	end
end

-- 浇水冷却时间
function FlowersPotRender:NextWaterTime(elapse_time, total_time)
	local time = math.floor(total_time - elapse_time)
	-- print_error("FFFF====== time", time)
	if time > 0 and self.node_list.next_water_time and self.node_list.watering_btn and self.interface_cfg then
		self.node_list.next_water_time.text.text = string.format(WateringFlowersWGData.Instance:GetInterfaceDesc("desc_text_33", self.cur_panel_index == 2), TimeUtil.FormatSecond(time))
		-- XUI.SetButtonEnabled(self.node_list.watering_btn, false)
	end
end

function FlowersPotRender:CompleteNextWaterTime()
	self:ClearNextWaterTime()
end

function FlowersPotRender:ClearNextWaterTime()
	if CountDownManager.Instance:HasCountDown("next_water_time_" .. self.index) then
		CountDownManager.Instance:RemoveCountDown("next_water_time_" .. self.index)
	end
	if self.node_list.next_water_time then
		self.node_list.next_water_time.text.text = ""
	end

	-- if self.node_list.watering_btn then
	-- 	XUI.SetButtonEnabled(self.node_list.watering_btn, true)
	-- end
end

function FlowersPotRender:FlushHelpMeList()
	local data_list = WateringFlowersWGData.Instance:GetHlepMeDataList(self.index)
	
	for i,v in ipairs(self.help_me_list) do
		if data_list[i] then
			v.button:SetActive(true)
			v.name.text.text = data_list[i].name
		else
			v.button:SetActive(false)
		end
	end
end

--种植按钮
function FlowersPotRender:OnClickPlantBtn()
	if not self.data then
		return
	end

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local graden_uid = WateringFlowersWGData.Instance:GetCurGardenUid()

	if role_id == graden_uid then

		if self.index - 1 == POT_TYPE.common_pot then
			--普通盆栽
			WateringFlowersWGCtrl.Instance:CSOAWaterFlowerInfo(OA_WATER_FLOWER_OPERA_TYPE.OA_WATER_FLOWER_OPERA_TYPE_PLANT, self.index - 1)
			SysMsgWGCtrl.Instance:ErrorRemind(Language.OpertionAcitvity.WateringFlowers.PlantDesc1)
		else
			--情侣盆栽
			local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	
			if lover_id > 0 then
				--直接种植
				WateringFlowersWGCtrl.Instance:CSOAWaterFlowerInfo(OA_WATER_FLOWER_OPERA_TYPE.OA_WATER_FLOWER_OPERA_TYPE_PLANT, self.index - 1)
				SysMsgWGCtrl.Instance:ErrorRemind(Language.OpertionAcitvity.WateringFlowers.PlantDesc2)
			else
				--滚去结婚
				if self.alert_tips and self.interface_cfg then
					local ok_func = function ()
						self:GotoMarryFunc()
					end
	
					local content = self.interface_cfg.desc_text_27
	
					self.alert_tips:SetLableString(content)
					self.alert_tips:SetOkFunc(ok_func)
					self.alert_tips:Open()
				end
			end
		end
	end
end

--前往结婚
function FlowersPotRender:GotoMarryFunc()
	--巡游中直接拦截掉
	if MarryWGData.Instance:GetOwnIsXunyou() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotMoveInXunYou)
		return
	end

	-- 护送中不可操作
	if YunbiaoWGData.Instance:GetIsHuShong() then
		TipWGCtrl.Instance:ShowSystemMsg(Language.YunBiao.CanNotDO)
		return
	end

	if TaskWGCtrl.Instance:IsFly() then
		return
	end	

	GuajiWGCtrl.Instance:StopGuaji()
	TaskGuide.Instance:CanAutoAllTask(false)
	ViewManager.Instance:CloseAll()

	local npc_cfg = MarryWGData.Instance:GetYueLaoCfg()

	if npc_cfg then
		local npc_id = MarryWGData.Instance:GetYueLaoNpcId()
		local range = TaskWGData.Instance:GetNPCRange(npc_id)
		GuajiWGCtrl.Instance:MoveToPos(npc_cfg.scene_id, npc_cfg.x, npc_cfg.y, range)
		MoveCache.SetEndType(MoveEndType.ClickNpc)
		MoveCache.param1 = npc_id
	end
end

--浇水按钮
function FlowersPotRender:OnClickWateringBtn()
	-- if self.water_delay_time then
	-- 	GlobalTimerQuest:CancelQuest(self.water_delay_time)
	-- 	self.water_delay_time = nil
	-- end

	--播放浇水动画

	if not self.data then
		return
	end

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local graden_uid = WateringFlowersWGData.Instance:GetCurGardenUid()

	if role_id ~= graden_uid then
		--贼他妈绕，WDNMD
		--今日被浇剩余次数，被浇总次数
		local remain_times, max_times = WateringFlowersWGData.Instance:GetOtherTodayRemindWaterTimesByType(self.index)
		--自己剩余协助他人浇水次数，剩余协助他人浇水总次数（盆栽表-help_other_times）
		local self_remain_times, slef_max_times = WateringFlowersWGData.Instance:GetOtherTodayRemindWaterTimesByTypeTwo(self.index)
		--自己剩余协助次数（根据类型）,自己最大协助次数（根据类型）（其他表-help_other_times）
		local self_remain_help_times, self_max_help_times = WateringFlowersWGData.Instance:GetTodayRemainHelpOtherTimesByType(self.index)

		if self.data.status == FLOWER_STATUS.FLOWER_STATUS_NOTHING then
			TipWGCtrl.Instance:ShowSystemMsg(Language.OpertionAcitvity.WateringFlowers.TipsDesc2)
			return
		end
	
		if self.data.status == FLOWER_STATUS.FLOWER_STATUS_FETCH then
			TipWGCtrl.Instance:ShowSystemMsg(Language.OpertionAcitvity.WateringFlowers.TipsDesc3)
			return
		end

		if remain_times <= 0 then
			TipWGCtrl.Instance:ShowSystemMsg(Language.OpertionAcitvity.WateringFlowers.FlowerNames[self.index] .. Language.OpertionAcitvity.WateringFlowers.TipsDesc5)
			return
		end

		if self_remain_times <= 0 then
			TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.OpertionAcitvity.WateringFlowers.TipsDesc6, Language.OpertionAcitvity.WateringFlowers.FriendPotNames[self.index], Language.OpertionAcitvity.WateringFlowers.FlowerNames[self.index]))
			return
		end

		if self_remain_help_times <= 0 then
			TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.OpertionAcitvity.WateringFlowers.TipsDesc7, Language.OpertionAcitvity.WateringFlowers.FriendPotNames[self.index]))
			return
		end
	else
		-- 自己浇水次数
		local remain_times, max_times = WateringFlowersWGData.Instance:GetTodayRemainWaterTimesByType(self.index)

		if remain_times <= 0 then
			TipWGCtrl.Instance:ShowSystemMsg(Language.OpertionAcitvity.WateringFlowers.TipsDesc5)
			return
		end
	end

	-- if self.data.status == FLOWER_STATUS.FLOWER_STATUS_NOTHING or
	-- 	self.data.status == FLOWER_STATUS.FLOWER_STATUS_RIPE or 
	-- 	self.data.status == FLOWER_STATUS.FLOWER_STATUS_FETCH then
	-- 	return
	-- end

	--self:DoKettleAnim()
	--local clip_time = 1.2
	-- XUI.SetButtonEnabled(self.node_list.watering_btn, false)

	if role_id == graden_uid then
		-- self.water_delay_time = GlobalTimerQuest:AddDelayTimer(function ()
		-- 	WateringFlowersWGCtrl.Instance:CSOAWaterFlowerInfo(OA_WATER_FLOWER_OPERA_TYPE.OA_WATER_FLOWER_OPERA_TYPE_WATER, self.index - 1)
		-- end, clip_time)
		WateringFlowersWGCtrl.Instance:CSOAWaterFlowerInfo(OA_WATER_FLOWER_OPERA_TYPE.OA_WATER_FLOWER_OPERA_TYPE_WATER, self.index - 1)
	else
		-- self.water_delay_time = GlobalTimerQuest:AddDelayTimer(function ()
		-- 	WateringFlowersWGCtrl.Instance:CSOAWaterFlowerInfo(OA_WATER_FLOWER_OPERA_TYPE.OA_WATER_FLOWER_OPERA_TYPE_WATER_OTHER, self.index - 1, graden_uid)
		-- 	XUI.SetButtonEnabled(self.node_list.watering_btn, true)
		-- end, clip_time)
		WateringFlowersWGCtrl.Instance:CSOAWaterFlowerInfo(OA_WATER_FLOWER_OPERA_TYPE.OA_WATER_FLOWER_OPERA_TYPE_WATER_OTHER, self.index - 1, graden_uid)
		-- XUI.SetButtonEnabled(self.node_list.watering_btn, true)
	end
end

function FlowersPotRender:DoKettleAnim()
	local tween_pos_to = self.node_list.pot_bg.transform:DOAnchorPos(Vector2(-80, -50), 0.5)--, DG.Tweening.Ease.Linear) 
	self:CancelTween()
	local tween = DG.Tweening.DOTween.Sequence()
	tween:Append(tween_pos_to)
	self.node_list.pot_bg.transform:DOLocalRotate(u3dpool.vec3(0, 0, 45), 0.5, DG.Tweening.RotateMode.Fast)
	tween:AppendInterval(0.2)		
 	tween:OnComplete(function ()
		self.node_list.pot_bg.transform:DOAnchorPos(Vector2(0, -111), 0.5)
		self.node_list.pot_bg.transform:DOLocalRotate(u3dpool.vec3(0, 0, 0), 0.5, DG.Tweening.RotateMode.Fast)
	end)

	self.sequence_tween = tween
end

function FlowersPotRender:CancelTween()
    if self.sequence_tween then
        self.sequence_tween:Kill()
        self.sequence_tween = nil
    end
end

function FlowersPotRender:KillKettleWaterEffTime()
	if self.kettle_water_delay_time then
		GlobalTimerQuest:CancelQuest(self.kettle_water_delay_time)
		self.kettle_water_delay_time = nil
	end
end

--邀请浇水按钮
function FlowersPotRender:OnClickInviteWateringBtn()
	if not self.data then
		return
	end

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local graden_uid = WateringFlowersWGData.Instance:GetCurGardenUid()

	if graden_uid ~= role_id then
		return
	end

	local be_water_remain_times, be_water_max_times = WateringFlowersWGData.Instance:GetTodayRemindBeWaterdTimesByType(self.index)

	if be_water_remain_times <= 0 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.OpertionAcitvity.WateringFlowers.TipsDesc8)
		return
	end

	if self.index - 1 == POT_TYPE.common_pot then
		--普通盆栽
		if self.parent_view then
			self.parent_view:OnClickHelpBtn()
		end
	else
		--情侣盆栽
		local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0

		if lover_id > 0 then
			--直接邀请伴侣
			if self.interface_cfg then
				TipWGCtrl.Instance:ShowSystemMsg(self.interface_cfg.desc_text_31)
			end

			WateringFlowersWGCtrl.Instance:CSOAWaterFlowerInfo(OA_WATER_FLOWER_OPERA_TYPE.OA_WATER_FLOWER_OPERA_TYPE_INVITE_HELP_ME, lover_id)

		else
			--滚去结婚
			if self.alert_tips and self.interface_cfg then
				local ok_func = function ()
					self:GotoMarryFunc()
				end

				local content = self.interface_cfg.desc_text_27

				self.alert_tips:SetLableString(content)
				self.alert_tips:SetOkFunc(ok_func)
				self.alert_tips:Open()
			end
		end
	end
end

--收获按钮
function FlowersPotRender:OnClickHarvestBtn()
	if not self.data then
		return
	end
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local graden_uid = WateringFlowersWGData.Instance:GetCurGardenUid()

	if graden_uid ~= role_id then
		return
	end

	if self.data.status ~= FLOWER_STATUS.FLOWER_STATUS_RIPE then
		return
	end
	-- print_error("FFFFF======== --收获按钮请求", OA_WATER_FLOWER_OPERA_TYPE.OA_WATER_FLOWER_OPERA_TYPE_FETCH, self.index - 1)
	WateringFlowersWGCtrl.Instance:CSOAWaterFlowerInfo(OA_WATER_FLOWER_OPERA_TYPE.OA_WATER_FLOWER_OPERA_TYPE_FETCH, self.index - 1)

end

--点击帮助过我的人的奖励
function FlowersPotRender:OnClickHelpPlayer(index)
	if not self.data then
		return
	end

	local data_list = WateringFlowersWGData.Instance:GetHlepMeDataList(self.index)

	if not data_list[index] then
		return
	end

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local graden_uid = WateringFlowersWGData.Instance:GetCurGardenUid()

	if graden_uid ~= role_id then
		return
	end

	WateringFlowersWGCtrl.Instance:CSOAWaterFlowerInfo(OA_WATER_FLOWER_OPERA_TYPE.OA_WATER_FLOWER_OPERA_TYPE_FETCH_HELP_ME_REWARD, self.index - 1, data_list[index].uid)
end

function FlowersPotRender:SetKettleActive(is_active)
	if self.node_list.kettle_model then
		self.node_list.kettle_model:SetActive(is_active)
	end
end

function FlowersPotRender:PlayBoxAni()
	if not self.data then
		return
	end
	if self.data.status == FLOWER_STATUS.FLOWER_STATUS_RIPE then
		if self.gift_box_tween then
				self.gift_box_tween:Restart()
		else
			if self.gift_box_tween then
				self.gift_box_tween:Kill()
				self.gift_box_tween = nil
			end
			self.gift_box_tween = DG.Tweening.DOTween.Sequence()
			UITween.ShakeAnimi(self.node_list.reward_box.transform, self.gift_box_tween)
		end
	else
		if self.gift_box_tween then
			self.gift_box_tween:Pause()
			self.node_list.reward_box.transform.localRotation = Quaternion.identity
		end
	end
end

-- 点击宝箱
function FlowersPotRender:OnClickRewardBox()
	if not self.data then
		return
	end
	local other_cfg = WateringFlowersWGData.Instance:GetOtherCfg()
	local ripe_reward_list = self:SortItemList(other_cfg.ripe_reward_show)
	OperationActivityWGCtrl.Instance:ShowWateringReward(ripe_reward_list, self.data.status, BindTool.Bind(self.OnClickHarvestBtn, self))
end

function FlowersPotRender:SortItemList(sort_tab)
	local temp_data_list = {}
	for i = 0, #sort_tab do
		temp_data_list[i + 1] = {}
		temp_data_list[i + 1] = sort_tab[i]
		if sort_tab[i] and sort_tab[i].item_id then
			local _, color = ItemWGData.Instance:GetItemColor(sort_tab[i].item_id)
			temp_data_list[i + 1].color = color
		end
	end
	SortTools.SortDesc(temp_data_list, "color")
	return temp_data_list
end

--===========================================================WateringFriendRender(好友列表)=============================================================--
WateringFriendRender = WateringFriendRender or BaseClass(BaseRender)

function WateringFriendRender:LoadCallBack()
	self.node_list["help_watering_btn"].button:AddClickListener(BindTool.Bind(self.OnClickHelpWateringBtn, self))
	self.node_list["invite_btn"].button:AddClickListener(BindTool.Bind(self.OnClickInviteBtn, self))

	self.interface_cfg = WateringFlowersWGData.Instance:GetInterfaceCfg()
	self:InitPictureAndDescText()
end

function WateringFriendRender:__delete()
	if self.invite_delaytime then
		GlobalTimerQuest:CancelQuest(self.invite_delaytime)
		self.invite_delaytime = nil
	end

	if self.node_list.invite_btn then
		XUI.SetButtonEnabled(self.node_list.invite_btn, true)
	end
end

function WateringFriendRender:InitPictureAndDescText()
	if not self.interface_cfg then
		return
	end
end

function WateringFriendRender:OnFlush()
	if not self.data then
		return
	end

	local cur_time = TimeWGCtrl.Instance:GetServerTime()
	
	local is_can_watering, type = WateringFlowersWGData.Instance:GetIsCanHelpWatering(self.data)
	XUI.SetGraphicGrey(self.node_list.help_watering_btn, not is_can_watering)

	self.node_list.help_flag:SetActive(self.data.is_be_invited == 1 and is_can_watering)

	local status = ""
	--3是跨服在线的状态
	if 1 == self.data.is_online or 3 == self.data.is_online then
		status = Language.Common.OnLine
	else
		local time_format = TimeWGCtrl.Instance:GetServerTime() - self.data.offline_time
		status = GuildWGData.FormatTime(time_format)
	end
	
	local color1 = (1 == self.data.is_online or 3 == self.data.is_online) and COLOR3B.C5 or COLOR3B.C5
	self.node_list.role_name.text.text = ToColorStr(self.data.name, color1)
    self:FlushTextInvite()
end

--前往花园浇水按钮
function WateringFriendRender:OnClickHelpWateringBtn()
	if not self.data then
		return
	end

	local is_can_watering, log_type = WateringFlowersWGData.Instance:GetIsCanHelpWatering(self.data)

	if not is_can_watering and self.interface_cfg then
		if log_type == 1 then
			TipWGCtrl.Instance:ShowSystemMsg(WateringFlowersWGData.Instance:GetInterfaceDesc("desc_text_37", self.cur_panel_index == 2))
		elseif log_type == 2 then
			TipWGCtrl.Instance:ShowSystemMsg(WateringFlowersWGData.Instance:GetInterfaceDesc("desc_text_35", self.cur_panel_index == 2))
		elseif log_type == 3 then
			TipWGCtrl.Instance:ShowSystemMsg(WateringFlowersWGData.Instance:GetInterfaceDesc("desc_text_36", self.cur_panel_index == 2))
		end
		return
	end
	
	WateringFlowersWGCtrl.Instance:HideFriendListView()
	WateringFlowersWGData.Instance:SetGardenFlag(1)
	WateringFlowersWGCtrl.Instance:CSOAWaterFlowerInfo(OA_WATER_FLOWER_OPERA_TYPE.OA_WATER_FLOWER_OPERA_TYPE_CONFIRM_INVITE, self.data.uid)
	WateringFlowersWGCtrl.Instance:CSOAWaterFlowerInfo(OA_WATER_FLOWER_OPERA_TYPE.OA_WATER_FLOWER_OPERA_TYPE_OTER_INFO_REQ, self.data.uid)
end

--邀请按钮
function WateringFriendRender:OnClickInviteBtn()
	local other_cfg = WateringFlowersWGData.Instance:GetOtherCfg()

	if not other_cfg then
		return
	end
    WateringFlowersWGData.Instance:AddCacheCDList(self.data.uid, other_cfg.cd)
	WateringFlowersWGCtrl.Instance:CSOAWaterFlowerInfo(OA_WATER_FLOWER_OPERA_TYPE.OA_WATER_FLOWER_OPERA_TYPE_INVITE_HELP_ME, self.data.uid)
	
	if self.interface_cfg then
		TipWGCtrl.Instance:ShowSystemMsg(self.interface_cfg.desc_text_30)
	end

	
	-- local cur_time = TimeWGCtrl.Instance:GetServerTime()
	-- WateringFlowersWGData.Instance:SetFriendListNextInviteFlag(self.data.list_type, self.index, cur_time + 5)
	-- XUI.SetButtonEnabled(self.node_list.invite_btn, false)

	-- self.invite_delaytime = GlobalTimerQuest:AddDelayTimer(function ()

	-- 	if self.invite_delaytime then
	-- 		GlobalTimerQuest:CancelQuest(self.invite_delaytime)
	-- 		self.invite_delaytime = nil
	-- 	end
	-- 	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_watering_flowers)
	-- end, other_cfg.cd)
end
 
function WateringFriendRender:FlushTextInvite()
    local role_id = self.data.uid
    if WateringFlowersWGData.Instance:GetCacheCDByRoleid(role_id) > 0 then
        XUI.SetButtonEnabled(self.node_list["invite_btn"], false)
        return
    end
    XUI.SetButtonEnabled(self.node_list["invite_btn"], true)
end