MarryFingerPrintView = MarryFingerPrintView or BaseClass(SafeBaseView)

local AutoCloseTime = 15
local LoopMaxAni = 8
local OneAniTime = 0.5

function MarryFingerPrintView:__init()
	self.view_layer = UiLayer.PopTop
    self:SetMaskBg(false)
    self.view_style = ViewStyle.Half

	self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_fingerprint_view")
end

function MarryFingerPrintView:ReleaseCallBack()
	self.show_effect = nil
	self.cur_ani_num = nil

	if self.auto_close then
		GlobalTimerQuest:CancelQuest(self.auto_close)
		self.auto_close = nil
	end
    if self.tween_up then
		self.tween_up:Kill()
		self.tween_up = nil
	end
	if self.tween then
		self.tween:Kill()
		self.tween = nil
    end

    if self.tween_jiezhi then
		self.tween_jiezhi:Kill()
		self.tween_jiezhi = nil
    end
end

function MarryFingerPrintView:LoadCallBack()
	self.marry_way = MARRY_REQ_TYPE.MARRY_PRESS_FINGER_REQ
	self.agree = false
	self.otehr_agree = false
    self.node_list.jiezhi2:SetActive(false)
    self.node_list.layout_fingerprint.button.enabled = false

    self.node_list.layout_fingerprint.button:AddClickListener(BindTool.Bind(self.Close, self))
    self.node_list.jiezhi.event_trigger_listener:AddDragListener(BindTool.Bind1(self.OnDrag, self))
    
    self.base_pos = self.node_list.jiezhi.rect.anchoredPosition
    self.jiantou_default_pos = self.node_list.jiantou.rect.anchoredPosition
    self.end_pos = self.node_list.jiezhi_end_pos.rect.anchoredPosition
    self.node_list.jiezhi_des_text.text.text = string.format(Language.Marry.JieZhiDes, MarryWGData.Instance:GetCurLoverName()) 

    self:MoveLoop()
    self.node_list["Time"]:SetActive(true)
    if self.auto_close then
        GlobalTimerQuest:CancelQuest(self.auto_close)
        self.auto_close = nil
    end
    local time = AutoCloseTime
    if self.auto_close == nil then
        self.auto_close = GlobalTimerQuest:AddRunQuest(function ()
            self.node_list["Time"].text.text = string.format(Language.Marry.TimeCount, time)
            time = time - 1
            if time < 4 and not self.agree then
                self.agree = true
                self:DoJiezhiTween()
            end
            if time < 0 then
                self:Close()
                GlobalTimerQuest:CancelQuest(self.auto_close)
                self.auto_close = nil
            end
        end, 1)
    end
end

function MarryFingerPrintView:CloseCallBack()
	ViewManager.Instance:CloseAll()
	local marry_yuyue_list = MarryWGData.Instance:GetYuYueRoleInfo()
	if nil == marry_yuyue_list or (marry_yuyue_list and IsEmptyTable(marry_yuyue_list)) then
		return
	end
	if (marry_yuyue_list.marry_type > 0 and marry_yuyue_list.marry_count > 0) or
	   (marry_yuyue_list.marry_type2 > 0 and marry_yuyue_list.marry_count2 > 0) then
		ViewManager.Instance:Open(GuideModuleName.MarryYuYue)
    end
    
    self.agree = false
	self.otehr_agree = false
end

--自己同意
function MarryFingerPrintView:OnClickStart()
	self.agree = true 
	MarryWGCtrl.Instance:SendMarryReq(self.marry_way)
end

function MarryFingerPrintView:OnClickClose()
	if self.agree and self.otehr_agree then
		self:Close()
		return
	end

	MarryWGCtrl.Instance:SendMarryReq(self.marry_way)
	self:Close()
end

function MarryFingerPrintView:OnFlush(param_list)
	for k,v in pairs(param_list) do
		if k == "finish" then
			if not self.otehr_agree then
				--self.node_list["FingerOther"]:SetActive(true)
				self.otehr_agree = true
			else
				--self.node_list["FingerMe"]:SetActive(true)
			end
		elseif k == "bothfinish" then
			
			
		end
	end
end

function MarryFingerPrintView:OnDrag(event_data)
    local cur_di_posx, cur_di_posy = self.node_list.jiezhi.rect.anchoredPosition.x, self.node_list.jiezhi.rect.anchoredPosition.y
    local x_dis = self.end_pos.x - self.base_pos.x
    local y_dis = self.end_pos.y - self.base_pos.y
    local Xa = self.base_pos.x
    local Ya = self.base_pos.y
    
    local Xc = cur_di_posx + event_data.delta.x
    local Yc = cur_di_posy + event_data.delta.y
    local k = y_dis / x_dis
    local posx = (k * k * Xa + Xc + k * Yc - k * Ya)/(1 + k * k)
    local posy = posx * k - k * Xa + Ya

    if posx < Xa then
        self.node_list.jiezhi.rect.anchoredPosition = self.base_pos
    elseif posx > self.end_pos.x then  --戴上戒指
       self:JiezhiEnd()
    else
        self.node_list.jiezhi.rect.anchoredPosition = Vector2(posx, posy)
    end
end

function MarryFingerPrintView:JiezhiEnd()
    self.agree = true
    
    self.node_list.jiezhi:SetActive(false)
    self.node_list.jiezhi2:SetActive(true)
    if self.tween_up then
        self.tween_up:Kill()
        self.tween_up = nil
    end
    self.node_list.layout_fingerprint.button.enabled = true
    self.node_list.jiantou:SetActive(false)
end

function MarryFingerPrintView:MoveLoop()
    local tween_time = 2
	self.node_list.jiantou.transform.anchoredPosition = self.jiantou_default_pos
	self.tween_up = self.node_list.jiantou.transform:DOAnchorPos(self.end_pos, tween_time)		-- 到终点
	self.tween_up:SetEase(DG.Tweening.Ease.OutCubic)
	self.tween_up:OnComplete(function ()
		self.tween_up:Restart()
	end)
end

function MarryFingerPrintView:DoJiezhiTween()
    self.node_list.jiantou:SetActive(false)
    self.node_list.jiezhi.event_trigger_listener.enabled = false
    local tween_time = 2
	self.node_list.jiezhi.transform.anchoredPosition = self.jiantou_default_pos
	self.tween_jiezhi = self.node_list.jiezhi.transform:DOAnchorPos(self.end_pos, tween_time)		-- 到终点
	self.tween_jiezhi:SetEase(DG.Tweening.Ease.OutCubic)
	self.tween_jiezhi:OnComplete(function ()
        self:JiezhiEnd()
	end)
end