﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class System_IO_PathWrap
{
	public static void Register(LuaState L)
	{
		L.BeginStaticLibs("Path");
		<PERSON><PERSON>RegFunction("ChangeExtension", ChangeExtension);
		<PERSON><PERSON>RegFunction("Combine", Combine);
		<PERSON><PERSON>Function("GetDirectoryName", GetDirectoryName);
		<PERSON><PERSON>Function("GetExtension", GetExtension);
		<PERSON><PERSON>RegFunction("GetFileName", GetFileName);
		<PERSON><PERSON>RegFunction("GetFullPath", GetFullPath);
		<PERSON><PERSON>RegFunction("GetPathRoot", GetPathRoot);
		<PERSON>.RegFunction("GetTempFileName", GetTempFileName);
		<PERSON>.RegFunction("GetTempPath", GetTempPath);
		<PERSON>.RegFunction("HasExtension", HasExtension);
		<PERSON><PERSON>RegFunction("IsPathRooted", IsPathRooted);
		<PERSON><PERSON>unction("GetInvalidFileNameChars", GetInvalidFileNameChars);
		<PERSON><PERSON>unction("GetInvalidPathChars", GetInvalidPathChars);
		L.RegFunction("GetRandomFileName", GetRandomFileName);
		L.RegFunction("GetRelativePath", GetRelativePath);
		L.RegFunction("IsPathFullyQualified", IsPathFullyQualified);
		L.RegVar("AltDirectorySeparatorChar", get_AltDirectorySeparatorChar, null);
		L.RegVar("DirectorySeparatorChar", get_DirectorySeparatorChar, null);
		L.RegVar("PathSeparator", get_PathSeparator, null);
		L.RegVar("VolumeSeparatorChar", get_VolumeSeparatorChar, null);
		L.EndStaticLibs();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ChangeExtension(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			string arg0 = ToLua.CheckString(L, 1);
			string arg1 = ToLua.CheckString(L, 2);
			string o = System.IO.Path.ChangeExtension(arg0, arg1);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Combine(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2 && TypeChecker.CheckTypes<string, string>(L, 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				string arg1 = ToLua.ToString(L, 2);
				string o = System.IO.Path.Combine(arg0, arg1);
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else if (count == 3 && TypeChecker.CheckTypes<string, string, string>(L, 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				string arg1 = ToLua.ToString(L, 2);
				string arg2 = ToLua.ToString(L, 3);
				string o = System.IO.Path.Combine(arg0, arg1, arg2);
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<string, string, string, string>(L, 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				string arg1 = ToLua.ToString(L, 2);
				string arg2 = ToLua.ToString(L, 3);
				string arg3 = ToLua.ToString(L, 4);
				string o = System.IO.Path.Combine(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else if (TypeChecker.CheckParamsType<string>(L, 1, count))
			{
				string[] arg0 = ToLua.ToParamsString(L, 1, count);
				string o = System.IO.Path.Combine(arg0);
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: System.IO.Path.Combine");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetDirectoryName(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			string o = System.IO.Path.GetDirectoryName(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetExtension(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			string o = System.IO.Path.GetExtension(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetFileName(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			string o = System.IO.Path.GetFileName(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetFullPath(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				string arg0 = ToLua.CheckString(L, 1);
				string o = System.IO.Path.GetFullPath(arg0);
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else if (count == 2)
			{
				string arg0 = ToLua.CheckString(L, 1);
				string arg1 = ToLua.CheckString(L, 2);
				string o = System.IO.Path.GetFullPath(arg0, arg1);
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: System.IO.Path.GetFullPath");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetPathRoot(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			string o = System.IO.Path.GetPathRoot(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetTempFileName(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			string o = System.IO.Path.GetTempFileName();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetTempPath(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			string o = System.IO.Path.GetTempPath();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HasExtension(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			bool o = System.IO.Path.HasExtension(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsPathRooted(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			bool o = System.IO.Path.IsPathRooted(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetInvalidFileNameChars(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			char[] o = System.IO.Path.GetInvalidFileNameChars();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetInvalidPathChars(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			char[] o = System.IO.Path.GetInvalidPathChars();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetRandomFileName(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			string o = System.IO.Path.GetRandomFileName();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetRelativePath(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			string arg0 = ToLua.CheckString(L, 1);
			string arg1 = ToLua.CheckString(L, 2);
			string o = System.IO.Path.GetRelativePath(arg0, arg1);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsPathFullyQualified(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			bool o = System.IO.Path.IsPathFullyQualified(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AltDirectorySeparatorChar(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, System.IO.Path.AltDirectorySeparatorChar);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_DirectorySeparatorChar(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, System.IO.Path.DirectorySeparatorChar);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PathSeparator(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, System.IO.Path.PathSeparator);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_VolumeSeparatorChar(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, System.IO.Path.VolumeSeparatorChar);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

