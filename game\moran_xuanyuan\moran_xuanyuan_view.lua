MoRanXuanYuanView = MoRanXuanYuanView or BaseClass(SafeBaseView)
function MoRanXuanYuanView:__init()
    self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/moran_xuanyuan_ui_prefab", "layout_moran_xuanyuan")
end

function MoRanXuanYuanView:LoadCallBack()
    if not self.all_reward_list then
        self.all_reward_list = AsyncListView.New(ItemCell,self.node_list["all_reward_list"])
    end

    if not self.leiji_reward_list then
        self.leiji_reward_list = AsyncListView.New(MoRanLeiJiRender, self.node_list["leiji_reward_list"])
    end

    if not self.reward_record_list then
        self.reward_record_list = AsyncListView.New(MoRanRecordCell,self.node_list["reward_record_list"])
    end

    for i = 1, 3 do
		self.node_list["draw_buy_btn_" .. i].button:AddClickListener(BindTool.Bind2(self.OnClickDraw, self, i))
        self.node_list["draw_buy_icon_" .. i].button:AddClickListener(BindTool.Bind(self.ShowItemTips, self, i))
	end

    XUI.AddClickEventListener(self.node_list["gailv_btn"], BindTool.Bind(self.OnClickGaiLv, self))
end

function MoRanXuanYuanView:ReleaseCallBack()
    if self.all_reward_list then
        self.all_reward_list:DeleteMe()
        self.all_reward_list = nil
    end

    if self.leiji_reward_list then
        self.leiji_reward_list:DeleteMe()
        self.leiji_reward_list = nil
    end

    if self.reward_record_list then
        self.reward_record_list:DeleteMe()
        self.reward_record_list = nil
    end
end

function MoRanXuanYuanView:OpenCallBack()
    MoRanXuanYuanWGCtrl.Instance:SendReq(RA_MORANXUANYUAN_OP_TYPE.INFO)
end

function MoRanXuanYuanView:OnFlush(param)
    self:FlushShowReward()
    self:FlushBtnShow()
end

function MoRanXuanYuanView:FlushShowReward()
    local show_list = MoRanXuanYuanWGData.Instance:GetShowAllrewardList()
    if not IsEmptyTable(show_list)  then
        self.all_reward_list:SetDataList(show_list)
    end

    local leiji_list = MoRanXuanYuanWGData.Instance:GetFanliRewardList()
    if not IsEmptyTable(leiji_list) then
        self.leiji_reward_list:SetDataList(leiji_list)
    end

    local record_list = MoRanXuanYuanWGData.Instance:GetRecordInfo()
    if record_list then
        self.reward_record_list:SetDataList(record_list)
    end
end

function MoRanXuanYuanView:FlushBtnShow()
    local cfg = MoRanXuanYuanWGData.Instance:GetConsumeCfg()
    local item_cfg
    local count = 0
    for i = 1, 3 do
        if cfg[i] then
            local item_id = cfg[i].yanhua_item.item_id
            item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
            if not IsEmptyTable(item_cfg) then               
                self.node_list["draw_buy_icon_" .. i].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
            end

            self.node_list["draw_buy_text_" .. i].text.text = string.format(Language.MoRanXuanYuanView.TxtBuy, cfg[i].onekey_lotto_num)
            count = ItemWGData.Instance:GetItemNumInBagById(item_id)
            self.node_list["draw_buy_red_" .. i]:SetActive(count >= cfg[i].yanhua_item.num)
            local color = count >= cfg[i].yanhua_item.num and COLOR3B.L_GREEN or COLOR3B.L_RED

            self.node_list["draw_buy_item_num_" .. i].text.text = ToColorStr(count .."/".. cfg[i].yanhua_item.num, color)
        end
    end
end

function MoRanXuanYuanView:OnClickDraw(draw_type)
    local cfg = MoRanXuanYuanWGData.Instance:GetConsumeCfg()
    cfg = cfg[draw_type]
    local num = ItemWGData.Instance:GetItemNumInBagById(cfg.yanhua_item.item_id)
    if num >= cfg.yanhua_item.num then
        MoRanXuanYuanWGData.Instance:CacheOrGetDrawIndex(draw_type)
        MoRanXuanYuanWGCtrl.Instance:SendReq(RA_MORANXUANYUAN_OP_TYPE.BUY, cfg.onekey_lotto_num, 1)
    else
        --不足够买
        MoRanXuanYuanWGCtrl.Instance:ClickUse(draw_type, function()
            self:OnClickBuy(draw_type)
        end)
    end
end

function MoRanXuanYuanView:OnClickBuy(draw_type)
    local cfg = MoRanXuanYuanWGData.Instance:GetConsumeCfg()
    local cur_cfg = cfg[draw_type]
    local num = ItemWGData.Instance:GetItemNumInBagById(cur_cfg.yanhua_item.item_id)
    local consume = cfg[1].consume_count * (cur_cfg.yanhua_item.num - num)
    --检查仙玉
    local enough = RoleWGData.Instance:GetIsEnoughUseGold(consume)
    --足够购买，不足弹窗
    if enough then
        MoRanXuanYuanWGData.Instance:CacheOrGetDrawIndex(draw_type)
        MoRanXuanYuanWGCtrl.Instance:SendReq(RA_MORANXUANYUAN_OP_TYPE.BUY, cfg[draw_type].onekey_lotto_num)
    else
        VipWGCtrl.Instance:OpenTipNoGold()
    end
end

function MoRanXuanYuanView:ShowItemTips(draw_type)
    local cfg = MoRanXuanYuanWGData.Instance:GetConsumeCfg()
    local item_id = cfg[draw_type].yanhua_item.item_id
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id}) 
end

function MoRanXuanYuanView:OnClickGaiLv()
	local info = MoRanXuanYuanWGData.Instance:GetRandomGaiLvinfo()
    if info then
        TipWGCtrl.Instance:OpenGaiLvShowView(info)
    end
end
-----------------------MoRanLeiJiRender-----------------------
MoRanLeiJiRender = MoRanLeiJiRender or BaseClass(BaseRender)
function MoRanLeiJiRender:LoadCallBack()
    self.node_list["btn_lingqu"].button:AddClickListener(BindTool.Bind1(self.OnClickGet, self))
    if not self.show_item then
        self.show_item = ItemCell.New(self.node_list.item_pos)
    end
end

function MoRanLeiJiRender:ReleaseCallBack()
    if self.show_item then
        self.show_item:DeleteMe()
    end
    self.show_item = nil
end

function MoRanLeiJiRender:OnFlush()
    if not self.data then
        return
    end

    local data = self.data.data
    local draw_times = MoRanXuanYuanWGData.Instance:GetCurDrawTimes()
    local color = draw_times >= data.lotto_num and COLOR3B.D_GREEN or COLOR3B.D_RED
    local str = ToColorStr(draw_times .. "/" .. data.lotto_num, color)
    self.node_list["time_str"].text.text = string.format(Language.MoRanXuanYuanView.TxtTimes, str)
    if not IsEmptyTable(data.reward_item) then
        self.show_item:SetData(data.reward_item[0])
    end
    
    local is_show = data.lotto_num <= draw_times and self.data.has_get ~= 1
    self.node_list["is_get"]:SetActive(self.data.has_get == 1)
    self.node_list["btn_lingqu"]:SetActive(is_show)
end

function MoRanLeiJiRender:OnClickGet()
    if not self.data then
        return
    end

    local draw_times = MoRanXuanYuanWGData.Instance:GetCurDrawTimes()
    local data = self.data.data
    if draw_times >= data.lotto_num and self.data.has_get == 0 then
        MoRanXuanYuanWGCtrl.Instance:SendReq(RA_MORANXUANYUAN_OP_TYPE.FETCH, data.index)
    end
end


MoRanRecordCell = MoRanRecordCell or BaseClass(BaseRender)
function MoRanRecordCell:OnFlush()
    if not self.data then
        return
    end

    local item_data = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    local item_name = ToColorStr(item_data.name, ITEM_COLOR[item_data.color])
    self.node_list["name_str"].text.text = string.format(Language.MoRanXuanYuanView.RecordName, self.data.role_name, item_name)
end