    --收缩状态
local ShrinkState = {
    Open = 0,
    Close = 1,
    Close_None = 2,
}
local MinClosePos = 180
local MaxClosePos = 1000

KF3V3MatchView = KF3V3MatchView or BaseClass(SafeBaseView)
function KF3V3MatchView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(706, 488)})
    self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_3v3_match")
    self.open_tween = nil
    self.close_tween = nil
end
function KF3V3MatchView:ReleaseCallBack()
	if  self.member_info_list ~= nil then
        for i, v in pairs(self.member_info_list) do
            v:DeleteMe()
        end
        self.member_info_list = nil
    end
end
function KF3V3MatchView:LoadCallBack()
    self.member_info_list = {}
	for i = 1, 3 do
        if not self.member_info_list[i] then
            self.member_info_list[i] = KF3V3MatchMemberInfoRender.New(self.node_list["info"..i])
        end
    end
    XUI.AddClickEventListener(self.node_list.btn_canel, BindTool.Bind(self.CancelMatch, self))
    XUI.AddClickEventListener(self.node_list.btn_xiaohua, BindTool.Bind(self.Close, self)) --最小化

    self:SetSecondView(nil, self.node_list["size"])
    self.node_list.title_view_name.text.text = Language.Activity.DailyActTips12
end

function KF3V3MatchView:OpenCallBack()
    --播放打开动画
    self:PlayPanelTween(ShrinkState.Open)
end

function KF3V3MatchView:ShowIndexCallBack()
    self.cur_time_value = KF3V3WGData.Instance:GetMatchTime()
    self:RefreshState()
end

function KF3V3MatchView:Open()
    --初始化缓存的状态,防止频繁的去访问C#
    SafeBaseView.Open(self)
    self.max_tip_state = true
    self.pre_container_state = nil
end

function KF3V3MatchView:Close()
    --播放关闭动画--最小化
    self:PlayPanelTween(ShrinkState.Close)
end

function KF3V3MatchView:CloseCallBack()
    self.pre_container_state = nil
end

function KF3V3MatchView:OnFlush()
    if SocietyWGData.Instance:GetIsInTeam() == 1 then
        for i = 1, 3 do
            self.node_list["info"..i]:SetActive(true)
        end
        --组队匹配
        local member_info_list = SocietyWGData.Instance:GetTeamMemberList()
        for i = 1, 3 do
            if member_info_list[i] then
                self.member_info_list[i]:SetData(member_info_list[i])
            else
                self.member_info_list[i]:SetData({})
            end
        end
    else
        local vo = GameVoManager.Instance:GetMainRoleVo()
        local orgin_role_id = RoleWGData.Instance:InCrossGetOriginUid()
        local member_info = {}
        member_info.role_id = orgin_role_id
        member_info.name = vo.name
        member_info.scene_id = Scene.Instance:GetSceneId()
        member_info.is_online = 1
        member_info.is_hang = 0
        member_info.prof = vo.prof
        member_info.camp = vo.camp
        member_info.sex = vo.sex
        member_info.level = vo.level
        member_info.vip_level = vo.vip_level
        member_info.capability = vo.capability
        member_info.avatar_key_big = vo.avatar_key_big
        member_info.avatar_key_small = vo.avatar_key_small
        member_info.shizhuang_photoframe = vo.shizhuang_photoframe
        member_info.jingjie_level = vo.jingjie_level
        member_info.plat_type = vo.plat_type
        member_info.orgin_role_id = orgin_role_id
        member_info.is_danren_pipei = true
        member_info.server_id = RoleWGData.Instance:GetMergeServerId()
        self.member_info_list[2]:SetData(member_info)
        for i = 1, 3 do
            self.node_list["info"..i]:SetActive(i == 2)
        end
        --单人匹配
    end
end

function KF3V3MatchView:CancelMatch()
    KF3V3WGCtrl.Instance:ReqCancelMatch()
    self:PlayPanelTween(ShrinkState.Close_None)
end

function KF3V3MatchView:UpdateMatchTime(elapse_time, total_time)
    self.cur_time_value = KF3V3WGData.Instance:GetMatchTime()
    self:RefreshRefreshState()
end

function KF3V3MatchView:RefreshState()
    self.cur_time_value = KF3V3WGData.Instance:GetMatchTime()
    self:RefreshPanelState()
end

function KF3V3MatchView:RefreshPanelState()
    local pre_time, is_max = NewTeamWGData.Instance:GetTeamMatchPreTime(self.cur_time_value)
    if is_max then
        --self.node_list.pre_time.text.text = Language.NewTeam.MaxMatchTimeTips
        if self.pre_container_state then
            -- self.node_list.pre_container:SetActive(false)
            self.pre_container_state = false
        end
        if not self.max_tip_state then
            self.node_list.max_time_tip:SetActive(true)
            self.max_tip_state = true
        end
    else
        self.node_list.pre_time.text.text = TimeUtil.MSTime(pre_time)
        if not self.pre_container_state then
            -- self.node_list.pre_container:SetActive(true)
            self.pre_container_state = true
        end
        if self.max_tip_state then
            self.node_list.max_time_tip:SetActive(false)
            self.max_tip_state = false
        end
    end

    self.node_list.cur_time.text.text = TimeUtil.MSTime(self.cur_time_value)
end

function KF3V3MatchView:PlayPanelTween(play_type)
    local start_pos = SocietyWGData.Instance:GetIsInTeam() == 1 and Vector3(200, -149, 0) or Vector3(-100, -149, 0)
    local end_pos = Vector3.zero
    local tween_time = 0.5
    if ShrinkState.Open == play_type then
        self.root_node_transform.localScale = Vector3.zero
        self.root_node_transform.anchoredPosition = start_pos
        self.root_node_transform:DOScale(1, tween_time)
        self.root_node_transform:DOAnchorPos(end_pos, tween_time)
    elseif ShrinkState.Close == play_type then
        self.root_node_transform.localScale = Vector3.one
        self.root_node_transform.anchoredPosition = end_pos
        self.root_node_transform:DOScale(0, tween_time)
        self.root_node_transform:DOAnchorPos(start_pos, tween_time)
        GlobalTimerQuest:AddDelayTimer(function()
                SafeBaseView.Close(self)
        end, tween_time)
    elseif ShrinkState.Close_None == play_type then 
        SafeBaseView.Close(self)
    end
end

KF3V3MatchMemberInfoRender = KF3V3MatchMemberInfoRender or BaseClass(BaoMingEnterMemberInfoRender)
function KF3V3MatchMemberInfoRender:SetName()
    --内网无法请求PHP，先设置默认名字
    local temp_name = string.format(Language.WorldServer.ServerDefName, self.data.server_id)
    --self.node_list.role_name.text.text = string.format(Language.NewTeam.ServerName, COLOR3B.DEFAULT, temp_name, self.data.name)  --"<color=%s>%s</color>-%s"
    self.node_list.role_name.text.text = self.data.name
    self.node_list.role_server.text.text = temp_name
end

function KF3V3MatchMemberInfoRender:OnFlush()
    BaoMingEnterMemberInfoRender.OnFlush(self)
    --self.node_list.vip_level:SetActive(false)
    self.node_list["head_cell"]:SetActive(not IsEmptyTable(self.data))
    self.node_list.text_vip:SetActive(not IsEmptyTable(self.data))
    if self.data.vip_level then
        self.node_list.text_vip:SetActive(self.data.vip_level >= 0)
        self.node_list.text_vip.text.text = self.data.vip_level
        --self.node_list.vip_img.image:LoadSprite(ResPath.GetVipIcon("vip"..self.data.vip_level))
        --self.node_list.vip_img.image:SetNativeSize()
    end

    -- 容错
    self.head_cell:SetGray(false)

    if not IsEmptyTable(self.data) and self.data.role_id > 0 then
		--人物等级
        local is_dianfeng, show_level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
        self.node_list.dianfen_img:SetActive(is_dianfeng)
        self.node_list.role_level.text.text = show_level
    end
end