﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public static class WMS
{
    private static int ID(string s) { return Shader.PropertyToID(s); }

    public static readonly int _SrcBlendMode = ID("_SrcBlendMode");
    public static readonly int _DstBlendMode = ID("_DstBlendMode");

    public static readonly int _WeatherDownsampleScale = ID("_WeatherMakerDownsampleScale");

    public static readonly int _WeatherMakerSunDirectionDown = ID("_WeatherMakerSunDirectionDown");
    public static readonly int _WeatherMakerSunDirectionDown2D = ID("_WeatherMakerSunDirectionDown2D");
    public static readonly int _WeatherMakerSunDirectionUp = ID("_WeatherMakerSunDirectionUp");
    public static readonly int _WeatherMakerSunDirectionUp2D = ID("_WeatherMakerSunDirectionUp2D");

    public static readonly int _WeatherMakerSunPositionNormalized = ID("_WeatherMakerSunPositionNormalized");
    public static readonly int _WeatherMakerSunPositionWorldSpace = ID("_WeatherMakerSunPositionWorldSpace");

    public static readonly int _WeatherMakerSunColor = ID("_WeatherMakerSunColor");
    public static readonly int _WeatherMakerSunTintColor = ID("_WeatherMakerSunTintColor");
    public static readonly int _WeatherMakerSunVar1 = ID("_WeatherMakerSunVar1");

    public static readonly int _WeatherMakerSunLightPower = ID("_WeatherMakerSunLightPower");

    public static readonly int _WeatherMakerSkyMie = ID("_WeatherMakerSkyMie");


    public static readonly int _MainTex2 = ID("_MainTex2");
    public static readonly int _MainTex3 = ID("_MainTex3");
    public static readonly int _MainTex4 = ID("_MainTex4");

    public static readonly int _WeatherMakerTemporaryDepthTexture = ID("_WeatherMakerTemporaryDepthTexture");

    public static readonly int _CameraDepthTextureHalf = ID("_CameraDepthTextureHalf");


    public static readonly int _WeatherMakerInverseProj = ID("_WeatherMakerInverseProj");
    public static readonly int _WeatherMakerInverseView = ID("_WeatherMakerInverseView");

    public static readonly int _DownsampleDepthScale = ID("_DownsampleDepthScale");
    public static readonly int _WeatherMakerDownsampleScale = ID("_WeatherMakerDownsampleScale");


    public static readonly int _WeatherMakerFogSunShaftsParam1 = ID("_WeatherMakerFogSunShaftsParam1");
    public static readonly int _WeatherMakerFogSunShaftsParam2 = ID("_WeatherMakerFogSunShaftsParam2");


    public static readonly int _WeatherMakerDirLightViewportPosition = ID("_WeatherMakerDirLightViewportPosition");

   

    public static readonly int _WeatherMakerDirLightColor = ID("_WeatherMakerDirLightColor");

    public static readonly int _WeatherMakerDirLightVar1 = ID("_WeatherMakerDirLightVar1");

    public static readonly int _WeatherMakerDirLightPosition = ID("_WeatherMakerDirLightPosition");

    // 雾
    public static readonly int _WeatherMakerFogColor = ID("_WeatherMakerFogColor");
    public static readonly int _WeatherMakerFogStartDepth = ID("_WeatherMakerFogStartDepth");
    public static readonly int _WeatherMakerFogEndDepth = ID("_WeatherMakerFogEndDepth");

    public static readonly int _WeatherMakerFogCloudShadowStrength = ID("_WeatherMakerFogCloudShadowStrength");

    //由deth  影响 fogfactor
    public static readonly int _WeatherMakerFogLinearFogFactor = ID("_WeatherMakerFogLinearFogFactor");
    public static readonly int _WeatherMakerFogFactorMax = ID("_WeatherMakerFogFactorMax");
    public static readonly int _WeatherMakerFogFactorMultiplier = ID("_WeatherMakerFogFactorMultiplier");

    public static readonly int _WeatherMakerTime = ID("_WeatherMakerTime");


   // 灯
    public static readonly int _WeatherMakerDirLightPower = ID("_WeatherMakerDirLightPower");

    public static readonly int _WeatherMakerFogLightAbsorption = ID("_WeatherMakerFogLightAbsorption");
    public static readonly int _WeatherMakerFogDirectionalLightScatterIntensity = ID("_WeatherMakerFogDirectionalLightScatterIntensity");
    public static readonly int _WeatherMakerFogDensity = ID("_WeatherMakerFogDensity");

    public static readonly int _WeatherMakerFogDensityScatter = ID("_WeatherMakerFogDensityScatter");

    //全局
    public static readonly int _WeatherMakerCloudGlobalShadow = ID("_WeatherMakerCloudGlobalShadow");
    public static readonly int _WeatherMakerCloudGlobalShadow2 = ID("_WeatherMakerCloudGlobalShadow2");


    //
    public static readonly int _WeatherMakerFogSunShaftsTintColor = ID("_WeatherMakerFogSunShaftsTintColor");
}
