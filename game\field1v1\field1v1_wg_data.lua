FIELD1V1_STATUS =
{
	AWAIT = 0,					-- 等待
	PREPARE = 1,				-- 准备
	PROCEED = 2,				-- 进行中
	OVER = 3,					-- 结束
}

Field1v1WGData = Field1v1WGData or BaseClass()
function Field1v1WGData:__init()
	if Field1v1WGData.Instance then
		ErrorLog("[Field1v1WGData] Attemp to create a singleton twice !")
	end
	Field1v1WGData.Instance = self

	self.scene_user_list = {} 								-- 场景对象信息
	self.scene_status = -1									-- 当前状态 0 发起方等待中 1准备 2战斗开始 3战斗完成
	self.scene_next_time = -1								-- 当前状态倒计时

	self.user_info = nil									-- 用户信息
	self.role_info_list = {}								-- 挑战列表用户信息
	self.report_info = {}									-- 战报
	self.rank_info = {}										-- 英雄榜
	self.lucky_info = {}									-- 幸运榜
	self.last_lucky_info = {}								-- 幸运榜
	self.challenger_rank_info = {}							--竞技场1v1排行.
	self.challenger_rank_info.rank_info = {}
	self.challenger_rank_info.daily_like_flag_list = {}

	self.challenger_rank_reward = {}						--竞技场1v1排行榜奖励标记.

	self.rank_end_reward_grade = 0							--竞技场1v1排行榜结算奖励档次.

	self.challenge_flag = 0

	self.user_info_gold = 0									-- 通关奖励的元宝

	self.guide_flag = false									--结算面板引导标记
	self.is_fail_select = false                             --竞技场失败是否选择强化界面标记

	self.role_level = 0                                     --挑战时的等级, 防止结算后升级导致获取经验数据错误

	self.challengefield_cfg = ConfigManager.Instance:GetAutoConfig("challengefield_auto")
	self.count_reward_cfg = self.challengefield_cfg.count_reward
	self.rank_end_reward_cfg = ListToMapList(self.challengefield_cfg.rank_end_reward, "grade")
	self.rank_reward_cfg = self.challengefield_cfg.rank_reward
	self.break_rank_reward_cfg = self.challengefield_cfg.break_rank_reward

	RemindManager.Instance:Register(RemindName.ActJjc, BindTool.Bind(self.IsShowActJjcRedPoint, self))  -- 主界面图标红点
	--RemindManager.Instance:Register(RemindName.ActJjcArena, BindTool.Bind(self.IsShowActJjcArenaRedPoint, self))  -- 竞技场
	-- RemindManager.Instance:Register(RemindName.ActOneVSOnebArena, BindTool.Bind(self.IsShowActOneVSOnebArenaRedPoint, self))  -- 巅峰战
	RemindManager.Instance:Register(RemindName.ActOneVSOneJiFenbArena, BindTool.Bind(self.IsShowActOneVSOneJiFenbArenaRedPoint, self))  -- 巅峰战积分奖励

	-- self:RegisterOneVSOnebArenaRemindInBag(RemindName.ActOneVSOnebArena)
end

function Field1v1WGData:GetGuideFlag()
	if self.guide_flag ~= nil then
		return self.guide_flag
	end
end

function Field1v1WGData:SetGuideFlag(boo)
	if self.guide_flag  ~= nil then
		self.guide_flag = boo
	end
end

function Field1v1WGData:__delete()
	Field1v1WGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.ActJjc)
	--RemindManager.Instance:UnRegister(RemindName.ActJjcArena)
	-- RemindManager.Instance:UnRegister(RemindName.ActOneVSOnebArena)
	RemindManager.Instance:UnRegister(RemindName.ActOneVSOneJiFenbArena)
	RemindManager.Instance:UnRegister(RemindName.ActPVPbArena)
	RemindManager.Instance:UnRegister(RemindName.ActPVPJiFenbArena)
	if CountDownManager.Instance:HasCountDown("field_arena_red_point") then
		CountDownManager.Instance:RemoveCountDown("field_arena_red_point")
	end
end

function Field1v1WGData:RegisterOneVSOnebArenaRemindInBag(remind_name)
	local map = {}

	local upgrade_cfg = ConfigManager.Instance:GetAutoConfig("battleflag2_cfg_auto").upgrade
	for _, v in pairs(upgrade_cfg) do
		for _, item in pairs(v.consume_item) do
			map[item.item_id] = true
		end
	end

	local item_id_list = {}
	for k,v in pairs(map) do
		table.insert(item_id_list, k)
	end

	BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function Field1v1WGData:SetRoleLevel(role_level)
	self.role_level = role_level
end

function Field1v1WGData:GetChallengeFieldOtherCfg()
	return ConfigManager.Instance:GetAutoConfig("challengefield_auto").other[1]
end

function Field1v1WGData:GetChallengeFieldBuyConsume()
	return ConfigManager.Instance:GetAutoConfig("challengefield_auto").other[1].buy_join_times_cost
end

function Field1v1WGData:SetUserinfo(user_info)
	self.user_info = user_info
end

function Field1v1WGData:GetUserinfo()
	return self.user_info
end

function Field1v1WGData:MainUiField1v1BtnTipsIsShow()
	local flag = 0
	if self.user_info then
		local timer = self.user_info.challenge_cd_timer - TimeWGCtrl.Instance:GetServerTime()
		if self.user_info.is_cd_timer_limit == 1 and timer > 0 then
			flag = 0
		else
			local num = self:GetResidueTiaoZhanNum()
			if num > 0 then
				flag = 1
			end
		end
	end
	if flag == 0 then
		if self.user_info and self.user_info.reward_get_flag > 0 then
			flag = 1
		end
	end
	return flag
end

-- 连胜
function Field1v1WGData:GetField1v1Liansheng()
	if self.user_info then
		return self.user_info.liansheng or 0
	end
	return 0
end

function Field1v1WGData:GetUerGold()
	if self.user_info_gold ~= nil then
		return self.user_info_gold
	end
end

function Field1v1WGData:SetUerGold(boo)
	if self.user_info_gold ~= nil then
		self.user_info_gold = boo
	end
end

function Field1v1WGData:SetReportinfo(report_info)
	self.report_info = report_info
end

function Field1v1WGData:GetReportinfo()
	return self.report_info or {}
end

function Field1v1WGData:SetRankinfo(rank_info)
	self.rank_info = rank_info
end

function Field1v1WGData:GetRankinfo()
	return self.rank_info
end

function Field1v1WGData:SetChallengeFlag(flag)
	self.challenge_flag = flag
end

function Field1v1WGData:GetChallengeFlag()
	return self.challenge_flag
end

--获取购买次数
function Field1v1WGData:GetBuyJoinTimes()
	if self.user_info then
		return self.user_info.buy_join_times
	else
		return 0
	end
end

-- 获取当前积分
function Field1v1WGData:GetCurJifen()
	if self.user_info then
		return self.user_info.jifen
	else
		return nil
	end
end

-- 获取排名
function Field1v1WGData:GetRankByUid(uid)
	local rank = 0
	if self.user_info then
		if uid == GameVoManager.Instance:GetMainRoleVo().role_id then
			rank = self.user_info.rank
		else
			for k,v in pairs(self.user_info.rank_list) do
				if v.user_id == uid then
					rank = v.rank
					break
				end
			end
			if rank == 0 then
				for k,v in pairs(self.rank_info) do
					if v.user_id == uid then
						rank = v.rank
						break
					end
				end
			end
		end
	end
	return rank
end


function Field1v1WGData:AddRoleInfo(role_info)
	if role_info then
		self.role_info_list[role_info.role_id] = role_info
	end
end

-- 获取玩家信息
function Field1v1WGData:GetRoleInfoByUid(uid)
	return self.role_info_list[uid]
end

-- 获取玩家挑战信息
function Field1v1WGData:GetRoleTiaoZhanInfoByUid(uid)
	local info = nil
	if self.user_info then
		for k,v in pairs(self.user_info.rank_list) do
			if v.user_id == uid then
				info = v
				break
			end
		end
	end

	return info
end

-- 奖励是否可领取
function Field1v1WGData:GetCurSeqJiFenRewardIsGet(seq)
	if self.user_info then
		return self.user_info.jifen_reward_flag[seq]
	end
end

-- 获取积分奖励配置
function Field1v1WGData:GetJIfenConfig()
	local config = ConfigManager.Instance:GetAutoConfig("challengefield_auto")

	return config.jifen_reward
end

-- 获取是否达到跳转等级
function Field1v1WGData:GetIsCanSkipBattle()
	local vo = GameVoManager.Instance:GetMainRoleVo()
	local config = ConfigManager.Instance:GetAutoConfig("challengefield_auto")

	return config.other[1].skip_battle_level <= vo.level ,config.other[1].skip_battle_level
end

function Field1v1WGData:GetJiFenRewardByIndex(seq)
	local config = ConfigManager.Instance:GetAutoConfig("challengefield_auto")
	local role_lev = GameVoManager.Instance:GetMainRoleVo().level
	local cfg
	for i,v in ipairs(config.jifen_reward_detail) do
		if v.seq == seq and role_lev >= v.role_level then
			cfg = v
		end
	end
	return cfg
end


-- 获取可领取积分奖励数量
function Field1v1WGData:GetJiFenMayGetReardNum()
	local num = 0
	local config = ConfigManager.Instance:GetAutoConfig("challengefield_auto")

	if config.jifen_reward and self.user_info then
		for k,v in pairs(config.jifen_reward) do
			if self.user_info.jifen >= v.need_jifen and self:GetCurSeqJiFenRewardIsGet(v.seq) then
				num = num + 1
			end
		end
	end
	return num
end

function Field1v1WGData:GetFreeJoinTimes()
	local config = ConfigManager.Instance:GetAutoConfig("challengefield_auto")
	return config.other[1].free_join_times
end

-- 总挑战次数
function Field1v1WGData:GetSumTiaoZhanNum()
	local sum_num = 0
	local config = ConfigManager.Instance:GetAutoConfig("challengefield_auto")

	if config.other[1] then
		sum_num = config.other[1].free_join_times
	end

	if self.user_info then
		sum_num = sum_num + self.user_info.buy_join_times
	end
	return sum_num
end

-- 剩余次数
function Field1v1WGData:GetResidueTiaoZhanNum()
	local num = 0
	if self.user_info then
		num = self:GetSumTiaoZhanNum() - self.user_info.join_times
	end
	return num
end

function Field1v1WGData:GetChnageTotalCount()
	local num = 0
	if self.user_info then
		num = self:GetSumTiaoZhanNum() + self.user_info.join_times
	end
	return num
end

-- 获取可购买次数
function Field1v1WGData:GetCanBuyTimes()
	return tonumber(VipWGData.Instance:GetVipSpecPermissionsValue(VIP_LEVEL_AUTH_TYPE.JJC))
end

-- 主Ui上的提醒次数
function Field1v1WGData:GetRemindNum()
	return self:GetResidueTiaoZhanNum()
end

-- 每日奖励提示
function Field1v1WGData:Get1v1DayRewardRemind()
	--print_error("++++++++++++++++++++",self.user_info,self.user_info.reward_get_flag)
	if self.user_info and self.user_info.reward_get_flag > 0 then
		return 1
	end
	return 0
end

-- 是否获胜
function Field1v1WGData:IsWin()
	if self.scene_user_list and self.scene_user_list[2] then
		--print_error( self.scene_user_list[2].capability ,self.scene_user_list[1].capability)
		return self.scene_user_list[2].capability <= self.scene_user_list[1].capability
	end
	return false
end

function Field1v1WGData:GetRewardListField(add_shengwang)
	local reward_list_field = {}
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local config = ConfigManager.Instance:GetAutoConfig("challengefield_auto").challenge_reward
	local exp_config = ConfigManager.Instance:GetAutoConfig("role_level_reward_auto").level_reward_list
	local value = SkillWGData.Instance:GetExpEfficiencyInfo()
	value = value and value.world_extra_percent or 0
	local us_info = self:GetUserinfo()
	if us_info.is_up_level == 1 then
		role_level = role_level - 1
	end
	for k,v in pairs(config) do
		if role_level >= v.level_down_limit and role_level <= v.level_up_limit then
			for m,n in pairs(v.reward_item) do
				table.insert(reward_list_field,n) --奖品
			end

			if add_shengwang > 0 then
				table.insert(reward_list_field,{item_id = COMMON_CONSTS.VIRTUAL_ITEM_HORNOR, num = add_shengwang})  --声望
			end

			table.insert(reward_list_field,{item_id = COMMON_CONSTS.VIRTUAL_ITEM_BIND_COIN, num = v.reward_coin})--金钱
			for m,n in pairs(exp_config) do
				if  role_level == n.level then
					local num = n.kill_monster_exp * v.exp_copies* (1 + value)
			 		table.insert(reward_list_field,{item_id = 90708,num = num  })  --经验
			 		return reward_list_field
		 		end
			end
		end
	end
end

-- 结算信息
function Field1v1WGData:GetResultData(is_win)
	local add_shengwang = 0
	local config = ConfigManager.Instance:GetAutoConfig("challengefield_auto")
	local is_win_client = self:IsWin()
	if config.other[1] then
		if not is_win then
			add_shengwang = self:IsWin() and config.other[1].win_add_shengwang or config.other[1].lose_add_shengwang
		else
			add_shengwang = is_win == 1 and config.other[1].win_add_shengwang or config.other[1].lose_add_shengwang
		end
	end

	local data = FuBenWGData.CreatePassVo()
	data.fb_type = Scene.Instance:GetSceneType()
	if not is_win then
		data.is_passed =  self:IsWin() and 1 or 0
	else
		data.is_passed = is_win
	end

	data.tip1 = ""
	data.get_exp = 0
	data.item_list = self:GetRewardListField(add_shengwang)

	if self.user_info then
		local best_rank = self.user_info.best_rank_pos
		if nil == self.user_info.jifen then return end
		local shengwangValue = 0
		shengwangValue = self.user_info.shengwang
		data.tip2 = add_shengwang
		local get_exp_cfg = self:GetChallengeExp()
		if not is_win then
			if self.user_info.old_rankpos > best_rank and best_rank == self.user_info.rank and is_win_client then
				data.is_score = true
			else
				data.is_score = false
			end

			if self:IsWin() then
				data.tip3 = string.format(Language.Field1v1.ResultTip4, self.user_info.best_rank_pos)
				data.get_exp = get_exp_cfg and get_exp_cfg.win_exp or 0
			else
				data.get_exp = get_exp_cfg and get_exp_cfg.failure_exp or 0
			end
		else
			if self.user_info.old_rankpos > best_rank and best_rank == self.user_info.rank and is_win == 1 then
				data.is_score = true
			else
				data.is_score = false
			end

			if is_win == 1 then
				data.tip3 = string.format(Language.Field1v1.ResultTip4, self.user_info.best_rank_pos)
				data.get_exp = get_exp_cfg and get_exp_cfg.win_exp or 0
			else
				data.get_exp = get_exp_cfg and get_exp_cfg.failure_exp or 0
			end
		end

		if not data.is_score then
            if self.user_info.rank >= best_rank and self.user_info.rank == self.user_info.old_rankpos then
                local color = data.is_passed == 1 and "#FFF5D6" or "#e7e9ea"
                data.tip1 = string.format(Language.Field1v1.ResultTip1_1, color, self.user_info.rank)

            elseif self.user_info.rank >= best_rank and self.user_info.rank < self.user_info.old_rankpos then
                data.is_split = true
				data.tip1 = string.format(Language.Field1v1.ResultTip1, self.user_info.old_rankpos, self.user_info.rank, best_rank)
			end
        else
            data.is_split = true
			data.tip1 = string.format(Language.Field1v1.ResultTip1, self.user_info.old_rankpos, self.user_info.rank, best_rank)
		end

		local value = SkillWGData.Instance:GetExpEfficiencyInfo()
		value = value and value.world_extra_percent or 0
		data.get_exp = data.get_exp * (1 + value)
	end

	return data
end

-- 获取配置幸运排名声望奖励
function Field1v1WGData:GetXingYunRankRewardBySeq(seq)
	local config = ConfigManager.Instance:GetAutoConfig("challengefield_auto")

	local reward_config = config.luck_reward
	local reward = 0
	for k,v in pairs(reward_config) do
		if seq == v.seq then
			reward = v.reward_shengwang
			break
		end
	end

	return reward
end

function Field1v1WGData:GetRankLastReward()
	local config = ConfigManager.Instance:GetAutoConfig("challengefield_auto")
	local reward_config = config.rank_reward
	return reward_config[#reward_config]
end

function Field1v1WGData:GetDailyRwardPos()
	if nil == self.user_info or IsEmptyTable(self.user_info) then
		return 1
	end

	for k,v in pairs(self.rank_reward_cfg) do
		if self.user_info.ten_oclock_level <= v.max_rank_pos and self.user_info.ten_oclock_level >= v.min_rank_pos and self.user_info.reward_get_flag > 0 then
			return k
		end
	end
	return 1
end

function Field1v1WGData:GetRankRwardPos()
	if nil == self.user_info or IsEmptyTable(self.user_info) then
		return 0
	end

	local config = ConfigManager.Instance:GetAutoConfig("challengefield_auto")
	local reward_config = config.break_rank_reward
	local flag = bit:d2b(self.user_info.break_fetch_reward_flag)

	local should_pos = 0
	for k,v in pairs(reward_config) do
		if (self.user_info.best_rank_pos - 1) <= v.rank_pos then
			should_pos = k
			break
		end
	end

	local temp_flag_table = {}
	for k,v in pairs(flag) do
		local num = #flag + 1 -k
		temp_flag_table[num] = v
	end

	for k,v in pairs(temp_flag_table) do
		if k >= should_pos and should_pos > 0 and v <= 0 and k <= #reward_config then
			return k,temp_flag_table
		end
	end
	return 0,temp_flag_table
end

-- 获取配置排名声望奖励
function Field1v1WGData:GetRankRewardByRank(rank)
	local config = ConfigManager.Instance:GetAutoConfig("challengefield_auto")

	local reward_config = config.rank_reward
	local reward1, reward2 = 0, 0
	for i,v in ipairs(reward_config) do
		if rank >= v.min_rank_pos and rank <= v.max_rank_pos then
			reward1, reward2 = v.reward_shengwang, v.reward_bind_gold
			break
		end
	end
	return reward1, reward2
end

-- 获取配置冲榜排名奖励
function Field1v1WGData:GetChongBangRankRewardCfb()
	return self.break_rank_reward_cfg
end

-- 获取下次结算声望
function Field1v1WGData:GetNextJieShuanShengWangByRank(rank)
	local config = ConfigManager.Instance:GetAutoConfig("challengefield_auto")
	local reward_config = config.rank_reward
	for i,v in ipairs(reward_config) do
		if rank >= v.min_rank_pos and rank <= v.max_rank_pos then
			return v
		end
	end
end

function Field1v1WGData:SetIsFightStarMark(is_start)
	if nil ~= is_start then
		self.is_start = is_start
	end

	return self.is_start
end

-- 下次是否大结算
function Field1v1WGData:GetIsMaxJieShuan()
	local config = ConfigManager.Instance:GetAutoConfig("challengefield_auto")

	local is_max = false
	local time_tab = TimeWGCtrl.Instance:GetServerTimeFormat()
	local config = config.rank_reward_time_cfg
	if config then
		if time_tab.hour + 1 == config[#config].honor then
			is_max = true
		end
	end
	return is_max
end

-- 获取排名结算声望  返回 普通结算 最终结算
function Field1v1WGData:GetCurRanJieShuanShengWangByRank(rank)
	local config = ConfigManager.Instance:GetAutoConfig("challengefield_auto")

	local min_sw, max_sw, min_g, max_g = 0, 0, 0, 0
	local config = config.rank_reward_time_cfg
	if config then
		local rnak_sw, rnak_g = self:GetRankRewardByRank(rank)
		min_sw = rnak_sw * (config[1].percent / 100)
		max_sw = rnak_sw * (config[#config].percent / 100)
		min_g = rnak_g * (config[1].percent / 100)
		max_g = rnak_g * (config[#config].percent / 100)
	end
	return min_sw, max_sw, min_g, max_g
end

-- 是否1v1进行状态
function Field1v1WGData:Is1v1Proceed()
	return self.scene_status == FIELD1V1_STATUS.PROCEED
end

-- 获取本角色最好的排名
function Field1v1WGData:GetBestRank()
	if self.user_info then
		return self.user_info.best_rank_pos
	end
end

function Field1v1WGData:GetNextRewardByRank(rank)
	local config = ConfigManager.Instance:GetAutoConfig("challengefield_auto")

	local reward_config = config.rank_reward
	local rankdif = 0
	for k,v in pairs(reward_config) do
		if rank >= v.min_rank_pos and rank <= v.max_rank_pos then
			if reward_config[k - 1] ~= nil then
				rankdif = rank - reward_config[k - 1].max_rank_pos
			else
				rankdif = 0
			end
		end
	end
	return rankdif
end

-------------------------------------------------------------------
function Field1v1WGData:GetNextJieShuanShengWangByRank2(rank)
	local min_sw, max_sw, min_g, max_g = self:GetCurRanJieShuanShengWangByRank2(rank)
	local sw = self:GetIsMaxJieShuan() and max_sw or min_sw
	local gold = self:GetIsMaxJieShuan() and max_g or min_g
	return sw, gold
end

function Field1v1WGData:GetCurRanJieShuanShengWangByRank2(rank)
	local config = ConfigManager.Instance:GetAutoConfig("challengefield_auto")

	local min_sw, max_sw, min_g, max_g = 0, 0, 0, 0
	local config = config.rank_reward_time_cfg
	if config then
		local rnak_sw, rnak_g = self:GetRankRewardByRank2(rank)
		min_sw = rnak_sw * (config[1].percent / 100)
		max_sw = rnak_sw * (config[#config].percent / 100)
		min_g = rnak_g * (config[1].percent / 100)
		max_g = rnak_g * (config[#config].percent / 100)
	end
	return min_sw, max_sw, min_g, max_g
end

function Field1v1WGData:GetRankRewardByRank2(rank)
	local config = ConfigManager.Instance:GetAutoConfig("challengefield_auto")

	local reward_config = config.rank_reward
	local reward1, reward2 = 0, 0
	for i,v in ipairs(reward_config) do
		if rank >= v.min_rank_pos and rank <= v.max_rank_pos then
			if reward_config[i - 1] ~= nil then
				reward1, reward2 = reward_config[i - 1].reward_shengwang, reward_config[i - 1].reward_bind_gold
			else
				reward1, reward2 = v.reward_shengwang, v.reward_bind_gold
			end
			break
		end
	end
	return reward1, reward2
end

function Field1v1WGData:GetChallengeExp()
	local config = ConfigManager.Instance:GetAutoConfig("challengefield_auto").challenge_exp
	return config[self.role_level]
end

--战场红点
function Field1v1WGData:IsShowActJjcRedPoint()
	if not FunOpen.Instance:GetFunIsOpened(FunName.ArenaTianTi) then
		return 0
	end

	-- if not FunOpen.Instance:GetFunIsOpened(FunName.ActJjc) then
	-- 	return 0
	-- end

	-- if ShowRedPoint.SHOW_RED_POINT == self:IsShowActJjcArenaRedPoint() then
	-- 	return 1
	-- end

	if ShowRedPoint.SHOW_RED_POINT == KF3V3WGData.Instance:IsShowActPVPbArenaRedPoint() then
		return 1
	end

	--天梯红点
	if ArenaTianTiWGData.Instance:IsShowActJjcTianTiRedPoint() == 1 then
		return 1
	end

	return 0
end

--竞技场红点
function Field1v1WGData:IsShowActJjcArenaRedPoint()
	if IS_ON_CROSSSERVER then
		return 0
	end

	local enter_count = self:GetResidueTiaoZhanNum()
	local remind_1 = Field1v1WGData.Instance:GetChallengeFieldRankRewardRed()
	local remind_2 = Field1v1WGData.Instance:GetChallengeFieldCountRewardRemind()

	if enter_count > 0 or remind_1 or remind_2 then
		return 1
	end
	return 0
end

--巅峰战红点
function Field1v1WGData:IsShowActOneVSOnebArenaRedPoint()
	-- local kf1v1_info = KuafuOnevoneWGData.Instance:Get1V1Info()
	-- local reward_cfg = KuafuOnevoneWGData.Instance:GetRewardCfg()
	-- for i=1,#reward_cfg do
	-- 	if KuafuOnevoneWGData.Instance:GetJionTimesRewardIsGet(i) ~= 1 then
	-- 		local count = 5 * (i - 1) > 0 and 5 * (i - 1) or 1
	-- 		if kf1v1_info.cross_day_join_1v1_count and kf1v1_info.cross_day_join_1v1_count >= count then
	-- 			return 1
	-- 		end
	-- 	end
	-- end
	return 0
end

--巅峰战积分奖励红点
function Field1v1WGData:IsShowActOneVSOneJiFenbArenaRedPoint()
	-- local data = KuafuOnevoneWGData.Instance:GetJiFenReward()
	-- for k,v in pairs(data) do
	-- 	local flag = KuafuOnevoneWGData.Instance:GetGongXunRewardIsGet(v.seq)
	--  	local curr_score = v.curr_score
	--  	if curr_score >= v.need_score and flag == 0 then
	--  		--print_error("1v1",v.need_score,v.curr_score)
	--  		return 1
	--  	end
	-- end
	return 0
end

--3v3红点
function Field1v1WGData:IsShowActPVPbArenaRedPoint()
	-- local scene_type = Scene.Instance:GetSceneType()
	-- if scene_type == SceneType.Kf_OneVOne_Prepare then
	-- 	return 0
	-- end
	local act_info = KuafuPVPWGData.Instance:GetActivityInfo()
	local count_now = act_info.today_match_count
	for i=1,3 do
		if KuafuPVPWGData.Instance:GetPvPJionTimesRewardIsGet(i) ~= 1 then
			local count = 5 * (i - 1) > 0 and 5 * (i - 1) or 1
			if count_now >= count then
				return 1
			end
		end
	end
	return 0
end

--3v3积分奖励红点
function Field1v1WGData:IsShowActPVPJiFenbArenaRedPoint()
	local data = KuafuPVPWGData.Instance:GetJiFenRewardCfg()
	for k,v in pairs(data) do
		local flag = KuafuPVPWGData.Instance:GetPvPGongXunRewardIsGet(v.seq)
	 	local curr_score = KuafuPVPWGData.Instance:GetRewardGongxun()
	 	if curr_score and curr_score >= v.need_score and flag == 0 then
	 		return 1
	 	end
	end
	return 0
end

function Field1v1WGData:SetCurMatchFlag(flag)
	self.cur_match_flag = flag
end

function Field1v1WGData:GetCurMatchFlag()
	return self.cur_match_flag
end

function Field1v1WGData:SetOneVOnePrepareInfo(data)
	self.kf_onevone_prepare_panel_info = data
end

function Field1v1WGData:GetOneVOnePrepareInfo()
	return self.kf_onevone_prepare_panel_info or {}
end

function Field1v1WGData:SetPvpPrepareInfo(data)
	self.kf_pvp_prepare_panel_info = data
end

function Field1v1WGData:GetPvpPrepareInfo()
	return self.kf_pvp_prepare_panel_info or {}
end

function Field1v1WGData:GetNeedLeave1V1Scene()
	return self.need_leave_1v1_scene
end

function Field1v1WGData:SetNeedLeave1V1Scene(bool)
	self.need_leave_1v1_scene = bool
end

function Field1v1WGData:GetCapabilityGap(challenge_cap)
	challenge_cap = challenge_cap or 1
	local role_cap = RoleWGData.Instance:GetAttr("capability") or 0
	local cap_times = role_cap / challenge_cap

	local gap_show_cfg = self.challengefield_cfg.gap_show
	for k,v in ipairs(gap_show_cfg) do
		if cap_times >= v.min_times and cap_times <= v.max_times then
			return v
		end
	end

	return gap_show_cfg[1]
end

function Field1v1WGData:GetIsCanMSAgain()
	local num = self:GetResidueTiaoZhanNum()
	local config = ConfigManager.Instance:GetAutoConfig("challengefield_auto")
	local player_level = RoleWGData.Instance:GetAttr("level") or 0
	local seckill_level = config.other[1] and config.other[1].seckill_level or 0
	return player_level >= seckill_level and num > 0
end

-----------------------------------------竞技场1v1排行榜 start-----------------------------------------
--排行榜信息.
function Field1v1WGData:SetChallengeRankListInfo(protocol)
	self.challenger_rank_info.rank_info = protocol.challenger_info							--排行榜信息，第11个是玩家自己.
	self.challenger_rank_info.daily_like_flag_list = bit:d2b_two(protocol.daily_like_flag)		--每天点赞标记 bit.
	for key, value in pairs(self.challenger_rank_info.rank_info) do
		if self.challenger_rank_info.daily_like_flag_list[key - 1] then
			value.daily_like_flag = self.challenger_rank_info.daily_like_flag_list[key - 1]
		end
	end
end

--奖励信息.
function Field1v1WGData:SetChallengeFieldRewardInfo(protocol)
	self.challenger_rank_reward.count_reward_flag = bit:d2b_two(protocol.count_reward_flag)		--次数奖励标记 bit.
	self.challenger_rank_reward.rank_reward_flag = bit:d2b_two(protocol.rank_reward_flag)		--排名奖励标记 bit.
end

--结算奖励档次.
function Field1v1WGData:SetChallengeFieldInfo(protocol)
	self.rank_end_reward_grade = protocol.grade
end

--获取排行榜信息.
function Field1v1WGData:GetChallengeRankListInfo()
	local list = {}
	for key, value in ipairs(self.challenger_rank_info.rank_info) do
		if key < 11 then
			table.insert(list, value)
		end
	end

	return list
end

--获取排行榜玩家自己的信息.
function Field1v1WGData:GetRoleChallengeRankListInfo()
	return (self.challenger_rank_info.rank_info or nil)[11]
end

--获取排行榜点赞信息.
function Field1v1WGData:GetChallengeRankDailyLikeInfo()
	return self.challenger_rank_info.daily_like_flag_list
end

--获取次数奖励标记.
function Field1v1WGData:GetChallengeFieldCountReward()
	return self.challenger_rank_reward.count_reward_flag
end

--获取排名奖励标记.
function Field1v1WGData:GetChallengeFieldRankReward()
	return self.challenger_rank_reward.rank_reward_flag
end

--获取结算奖励档次.
function Field1v1WGData:GetChallengeFieldInfo()
	return self.rank_end_reward_grade
end

--获取战斗次数.
function Field1v1WGData:GetChallengeFieldBattleCount()
	return self.user_info and self.user_info.battle_count or 0
end

--获取当前战斗次数的index.
function Field1v1WGData:GetChallengeFieldBattleCountIndex()
	local cur_index = 0
	if not self.user_info then
		return cur_index
	end

	local count_reward_cfg = self:GetFieldCountRewardCfgBySpSort()
	for key, value in ipairs(count_reward_cfg) do
		if self.user_info.battle_count>= value.battle_count then
			cur_index = value.index
		else
			break
		end
	end

	return cur_index
end

-- 获取排名奖励数据.
function Field1v1WGData:GetRankRewardCfb()
	return self.rank_reward_cfg
end

--获取次数奖励数据.
function Field1v1WGData:GetChallengeFieldCountRewardCfg()
	return self.count_reward_cfg
end

--获取次数奖励数据，固定只显示15条.
function Field1v1WGData:GetChallengeFieldCountRewardCfgBySpSort()
	local list = {}
	local max = 15
	local count = 0
	for key, value in pairs(self.count_reward_cfg) do
		if count >= max then
			break
		end

		local count_reward_flag = self:GetChallengeFieldCountRewardByIndex(value.index)
		if count_reward_flag ~= 1 then
			table.insert(list, value)
			count = count + 1
		end
	end

	table.sort(list, function (a, b)
		return a.battle_count < b.battle_count
	end)

	return list
end

--获取次数奖励数据.
function Field1v1WGData:GetFieldCountRewardCfgBySpSort()
	local list = {}
	for key, value in pairs(self.count_reward_cfg) do
		table.insert(list, value)
	end

	table.sort(list, function (a, b)
		return a.battle_count < b.battle_count
	end)

	return list
end

--获取当前的排名结算奖励数据.
function Field1v1WGData:GetChallengeFieldRankEndRewardCfg()
	return self.rank_end_reward_cfg[self.rank_end_reward_grade] or {}
end

--获取当前档次第一名的排名结算奖励.
function Field1v1WGData:GetChallengeFieldFirstRankEndReward()
	local data = self.rank_end_reward_cfg[self.rank_end_reward_grade] or nil
	if data and data[1] and data[1].rank_end_reward then
		return data[1].rank_end_reward
	end

	return nil
end

function Field1v1WGData:GetChallengeFieldRewardInfo()
	local other_cfg = self:GetChallengeFieldOtherCfg()
	local list = {}
	for i = 1, 2 do
		list[i] = {}
		list[i].challenge_type = i
		list[i].challenge_reward = other_cfg["reward_item" .. i]
	end
	return list
end

--通过索引获取排名奖励标记.
function Field1v1WGData:GetChallengeFieldRankRewardByIndex(index)
	return (self.challenger_rank_reward.rank_reward_flag or {})[index] or -1
end

--通过索引获取对战奖励标记.
function Field1v1WGData:GetChallengeFieldCountRewardByIndex(index)
	return (self.challenger_rank_reward.count_reward_flag or {})[index] or -1
end

--排名奖励红点.
function Field1v1WGData:GetChallengeFieldRankRewardRed()
	local cur_rank = 0
	if self.user_info and self.user_info.rank then
		cur_rank = self.user_info.rank
	end

	local reward_data = self:GetRankRewardCfb()
	if reward_data then
		for key, value in pairs(reward_data) do
			local rank_reward_flag = self:GetChallengeFieldRankRewardByIndex(value.index)
			if cur_rank <= value.max_rank_pos and rank_reward_flag == 0 then
				return true, key
			end
		end
	end

	return false, 0
end

--对战奖励红点.
function Field1v1WGData:GetChallengeFieldCountRewardRemind()
	local battle_count = self:GetChallengeFieldBattleCount()

	local reward_data = self:GetChallengeFieldCountRewardCfg()
	if reward_data then
		for key, value in pairs(reward_data) do
			local count_reward_flag = self:GetChallengeFieldCountRewardByIndex(value.index)
			if battle_count >= value.battle_count and count_reward_flag == 0 then
				return true, key
			end
		end
	end

	return false, 0
end
-----------------------------------------竞技场1v1排行榜 end-----------------------------------------