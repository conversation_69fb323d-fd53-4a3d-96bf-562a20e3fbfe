
--聊天Vip等级限制提示
----------------------------------
ChatVipLimitTipView = ChatVipLimitTipView or BaseClass(SafeBaseView)

function ChatVipLimitTipView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self.view_name = "ChatVipLimitTipView"
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(740, 384)})
	self:AddViewResource(0, "uis/view/chat_ui_prefab", "layout_vip_limit_tips")
end

function ChatVipLimitTipView:ReleaseCallBack()

end

function ChatVipLimitTipView:LoadCallBack()
	self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind(self.Close, self))--取消
	self.node_list["btn_OK"].button:AddClickListener(BindTool.Bind(self.OnOkBtnClick, self))--确定

	self.node_list["rich_dialog"].tmp.text = Language.Chat.VipLimitGuideTips
	self.node_list["text_cancel"].tmp.text = Language.Chat.VipLimitGuideTipsBtn1
	self.node_list["text_ok"].tmp.text = Language.Chat.VipLimitGuideTipsBtn2
end

function ChatVipLimitTipView:OnOkBtnClick()
	local tab_index = VipWGData.Instance:GetVIPZeorBuyIsOpenJump()
	ViewManager.Instance:Open(GuideModuleName.Vip, tab_index)
	self:Close()
end