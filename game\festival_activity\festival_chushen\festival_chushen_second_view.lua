FestivalChuShenSecondRankPanel = FestivalChuShenSecondRankPanel or BaseClass(SafeBaseView)

function FestivalChuShenSecondRankPanel:__init()
	self:SetMaskBg(true)
	--self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",{vector2 = Vector2(54, 17), sizeDelta = Vector2(756, 480)})
	self:AddViewResource(0, "uis/view/festival_activity_ui/chushen_ui_prefab", "layout_chushen_rank_view")
	
end

function FestivalChuShenSecondRankPanel:LoadCallBack()
	local pos_x, pos_y = FestivalActivityWGData.Instance:GetHuntPosition()
	RectTransform.SetAnchoredPositionXY(self.node_list.chushen_rank_list.rect, pos_x, pos_y)

	local pos_x1, pos_y1 = FestivalActivityWGData.Instance:GetHuntTextPos()
	RectTransform.SetAnchoredPositionXY(self.node_list.hunt_text.rect, pos_x1, pos_y1)

	local title_img_bundel, title_img_asset = ResPath.GetFestivalRawImages("btzs")
	self.node_list["title_img"].raw_image:LoadSprite(title_img_bundel, title_img_asset, function ()
		self.node_list["title_img"].raw_image:SetNativeSize()
	end)

	local chushen_rank_bg_bundle, chushen_rank_bg_asset = ResPath.GetFestivalRawImages("xzdi")
	self.node_list["chushen_rank_bg"].raw_image:LoadSprite(chushen_rank_bg_bundle, chushen_rank_bg_asset, function ()
		self.node_list["chushen_rank_bg"].raw_image:SetNativeSize()
	end)

	local middle_color = FestivalActivityWGData.Instance:GetChuMiddleTextColor()
	self.node_list["middle_text"].text.text = string.format(Language.FestivalChuShen.ChuShenRankDesc, middle_color)

	local gradient1, gradient2 = FestivalActivityWGData.Instance:GetHuntGradient()
	local out_color = FestivalActivityWGData.Instance:GetHuntOutColor()
	ChangeTextGradientColor(self.node_list["hunt_text"], gradient1, gradient2, out_color)

	local text_size = FestivalActivityWGData.Instance:GetHuntTextSize()
	self.node_list["hunt_text"].text.fontSize = text_size
end

function FestivalChuShenSecondRankPanel:LoadIndexCallBack()
	--self.node_list.title_view_name.text.text = Language.FestivalChuShen.ChuShenRankRecoder
 	 if not self.chushen_rank_list then
		self.chushen_rank_list = AsyncListView.New(FestivalChuShenSecondPanelRankRender, self.node_list.chushen_rank_list)
	end
 end 

function FestivalChuShenSecondRankPanel:ReleaseCallBack()
	if self.chushen_rank_list then
		self.chushen_rank_list:DeleteMe()
		self.chushen_rank_list = nil
	end
 end

 function FestivalChuShenSecondRankPanel:OnFlush()
	local data_list = FestivalChuShenWGData.Instance:GetChuShenRankData()
	if self.chushen_rank_list then
		self.chushen_rank_list:SetDataList(data_list, 0)
	end
 	self.node_list["middle_text"]:SetActive(#data_list <= 0)
 end



FestivalChuShenSecondPanelRankRender = FestivalChuShenSecondPanelRankRender or BaseClass(BaseRender)

function FestivalChuShenSecondPanelRankRender:LoadCallBack()
	self.role_avatar = RoleHeadCell.New(false)
 	XUI.AddClickEventListener(self.node_list["head_btn"], BindTool.Bind(self.HeadClick, self))
 	--XUI.AddClickEventListener(self.node_list["item_image"], BindTool.Bind(self.FoodTips, self))
 	self.head_cell = BaseHeadCell.New(self.node_list.head_pos)
 	
end

function FestivalChuShenSecondPanelRankRender:__delete()
	if nil ~= self.role_avatar then
		self.role_avatar:DeleteMe()
		self.role_avatar = nil
	end

	if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end

end

function FestivalChuShenSecondPanelRankRender:OnFlush()
	if nil == self.data then
		return
	end

	local role_info = {
		role_id = self.data.uid,
		role_name = self.data.role_name,
		prof = self.data.prof,
		sex = self.data.sex,
		is_online = self.data.is_online,
		team_index = self.data.team_index,
	}

	self.role_avatar:SetRoleInfo(role_info)
	local data = {role_id = self.data.uid, prof = self.data.prof, sex = self.data.sex, fashion_photoframe = 0}
	self.head_cell:SetData(data)

	local info = FestivalChuShenWGData.Instance:GetOneMenuCfg(self.data.menu_id)
	--local b, a = ResPath.GetF2CommonImages("yuan_menu_quality_"..info.menu_quality)
	local b, a = ResPath.GetFestivalActImages("a2_jrkh_xltb_yd_"..info.menu_quality)
	self.node_list["item_bg"].image:LoadSprite(b, a, function ()
		XUI.ImageSetNativeSize(self.node_list["item_bg"])
	end)
	
	local tsk_image_bundel, tsk_image_asset = ResPath.GetFestivalActImages("a2_jrkh_xltb_tsk")
	self.node_list.tsk_image.image:LoadSprite(tsk_image_bundel, tsk_image_asset, function()
	end)

	local parti_b, parti_a = FestivalChuShenWGData.Instance:GetPartiAsesst(info.menu_quality)
	self.node_list["parti_pos"]:ChangeAsset(parti_b, parti_a)
	self.node_list["parti_pos"]:SetActive(info.menu_quality > SHOW_EFFECT_LEVEL)

	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(info.id)
	local b1, a1 = ResPath.GetItem(item_cfg.icon_id)
	self.node_list["item_image"].image:LoadSprite(b1, a1, function ()
		XUI.ImageSetNativeSize(self.node_list["item_image"])
	end)
	local color = ITEM_COLOR[info.menu_quality]
	local menu_name = ToColorStr(info.menu_name, color)
	local hunt_name_color = FestivalActivityWGData.Instance:GetHuntNameColor()
	local role_name = ToColorStr(self.data.role_name, hunt_name_color)
	local hunt_initiate_color = FestivalActivityWGData.Instance:GetHuntInitiateColor()
	self.node_list.name.text.text = string.format(Language.FestivalChuShen.ChuShenRankDes, role_name, hunt_initiate_color, menu_name)
	local time_table = os.date("*t", self.data.unlock_timestamp)
	local hunt_time_color = FestivalActivityWGData.Instance:GetHuntTimeColor()
	self.node_list.food_name.text.text = string.format(Language.FestivalChuShen.ChuShenYYMMDD, hunt_time_color, time_table.year, time_table.month, time_table.day, time_table.hour, time_table.min, time_table.sec) 
	--self.node_list.creat_time.text.text = string.format(Language.FestivalChuShen.ChuShenHHMMSS, time_table.hour, time_table.min, time_table.sec) 
end

function FestivalChuShenSecondPanelRankRender:HeadClick()
	self.role_avatar:OpenMenu()
end

function FestivalChuShenSecondPanelRankRender:FoodTips()
	if self.data and self.data.menu_id and self.data.menu_id > 0 then
		local data = {}
		local menu_cfg = FestivalChuShenWGData.Instance:GetOneMenuCfg(self.data.menu_id)
		data.item_id = menu_cfg.dishes_id
		TipWGCtrl.Instance:OpenItem(data, ItemTip.FROM_NORMAL, nil, nil, nil)
	end
end