WelfareView = WelfareView or BaseClass(SafeBaseView)
function WelfareView:InitWekQianDaoView()
    self:CreateDayItemList()
    
    if self.show_day1_item == nil then
        self.show_day1_item = ItemCell.New(self.node_list["show_day1_item"])
    end

    if self.show_day2_item == nil then
        self.show_day2_item = ItemCell.New(self.node_list["show_day2_item"])
    end

    self.node_list["show_day1_btn"].button:AddClickListener(BindTool.Bind(self.OnClickDay1Get,self))
    self.node_list["show_day2_btn"].button:AddClickListener(BindTool.Bind(self.OnClickDay2Get,self))
end

function WelfareView:DeleteWekQianDaoView()
    if self.day_item_list then
		for k, v in pairs(self.day_item_list) do
			v:DeleteMe()
		end
		self.day_item_list = nil
	end

    if self.show_day1_item then
        self.show_day1_item:DeleteMe()
        self.show_day1_item = nil
    end

    if self.show_day2_item then
        self.show_day2_item:DeleteMe()
        self.show_day2_item = nil
    end

end

function WelfareView:CountJumpPos(min_day,max_day)

    if min_day < 5 then
        return 0
    end
    local pos_x = min_day/ (max_day-1)
    return pos_x
end

function WelfareView:OnFlushWekQianDaoView()
    local day_item_cfg = WelfareWGData.Instance:GetRewardCfg()
    local min_day = 100
    if day_item_cfg then
        for k, v in pairs(self.day_item_list) do
            v:SetData(day_item_cfg[k + 1])

            local can_get = WelfareWGData.Instance:GetRewardIsCanGet(day_item_cfg[k + 1].day)  --可领取
            local yi_lq = WelfareWGData.Instance:GetRewardIsGet(day_item_cfg[k + 1].day) --已领取
            if can_get and not yi_lq then
                min_day = math.min(min_day,day_item_cfg[k + 1].day)
            end
        end
    end
    if min_day~=100 then
        self.node_list.accumulative_login_scroll.scroll_rect.horizontalNormalizedPosition = self:CountJumpPos(min_day, #self.day_item_list)
    end


    local day_reward_cfg = WelfareWGData.Instance:GetDayRewardCfg()
    if day_reward_cfg[1] and day_reward_cfg[2] then
        self.show_day1_item:SetData(day_reward_cfg[1].reward_item[0])
        self.show_day2_item:SetData(day_reward_cfg[2].reward_item[0])

        self.node_list["show_day1_title"].text.text = string.format(Language.AccumulativeLogin.DayRewardTitle, NumberToChinaNumber(day_reward_cfg[1].need_day))
        self.node_list["show_day2_title"].text.text = string.format(Language.AccumulativeLogin.DayRewardTitle, NumberToChinaNumber(day_reward_cfg[2].need_day))
    end

    local leiji_day1 = WelfareWGData.Instance:GetSpecialRewardIsCanGet(0)
    local leiji_day2 = WelfareWGData.Instance:GetSpecialRewardIsCanGet(1)
    local leiji_is_get1 = WelfareWGData.Instance:GetSpecialRewardIsGet(0)
    local leiji_is_get2 = WelfareWGData.Instance:GetSpecialRewardIsGet(1)
    self.node_list["show_day1_red"]:SetActive(leiji_day1 and (not leiji_is_get1))
    self.node_list["show_day2_red"]:SetActive(leiji_day2 and (not leiji_is_get2))

    self.show_day1_item:SetLingQuVisible(leiji_is_get1)
    self.show_day2_item:SetLingQuVisible(leiji_is_get2)
end


function WelfareView:OpenCallBackWekQianDao()
    WelfareWGCtrl.Instance:OnCSLeiJiLoginOperate(LEIJI_LOGIN_OPERATE_TYPE.INFO)
end

function WelfareView:OnClickDay1Get()
    local day_reward_cfg = WelfareWGData.Instance:GetDayRewardCfg()
    if day_reward_cfg[1] then
        local can_get = WelfareWGData.Instance:GetSpecialRewardIsCanGet(day_reward_cfg[1].seq)
        if can_get then
            WelfareWGCtrl.Instance:OnCSLeiJiLoginOperate(LEIJI_LOGIN_OPERATE_TYPE.FETCH_DAY_REWARD, day_reward_cfg[1].seq)
        else
            TipWGCtrl.Instance:OpenItemTipGetWay({item_id = day_reward_cfg[1].reward_item[0].item_id})
        end
    end
end

function WelfareView:OnClickDay2Get()
    local day_reward_cfg = WelfareWGData.Instance:GetDayRewardCfg()
    if day_reward_cfg[2] then
        local can_get = WelfareWGData.Instance:GetSpecialRewardIsCanGet(day_reward_cfg[2].seq)
        if can_get then
            WelfareWGCtrl.Instance:OnCSLeiJiLoginOperate(LEIJI_LOGIN_OPERATE_TYPE.FETCH_DAY_REWARD, day_reward_cfg[2].seq)
        else
            TipWGCtrl.Instance:OpenItemTipGetWay({item_id = day_reward_cfg[2].reward_item[0].item_id})
        end
    end
end

function WelfareView:CreateDayItemList()
    if self.day_item_list == nil then
        self.day_item_list = {}
        for i = 0, 6 do
        self.day_item_list[i] = AccmulativeLoginItemRender.New(self.node_list.day_item_list:FindObj("day_item_list" .. i))
        self.day_item_list[i]:SetIndex(i)
        end
    end
end

AccmulativeLoginItemRender = AccmulativeLoginItemRender or BaseClass(BaseRender)
function AccmulativeLoginItemRender:LoadCallBack()
	self.show_item = ItemCell.New(self.node_list["item_pos"])
    self.node_list["btn_click"].button:AddClickListener(BindTool.Bind(self.OnClickGetReward,self))
end

function AccmulativeLoginItemRender:__delete()
	if self.show_item then
        self.show_item:DeleteMe()
        self.show_item = nil
    end
end

function AccmulativeLoginItemRender:OnFlush()
    if not self.data then
		return
	end

    --local day_txt = CommonDataManager.GetAncientNumber(self.data.day)
    --self.node_list["cur_day"].text.text =  day_txt
    
    self.show_item:SetData(self.data.reward_item[0])

    local can_get = WelfareWGData.Instance:GetRewardIsCanGet(self.data.day)  --可领取
    local yi_lq = WelfareWGData.Instance:GetRewardIsGet(self.data.day) --已领取
    local no_lq = (not can_get) and (not yi_lq) --不可领

    -- --可领取
    --self.node_list["no_get_bg"]:SetActive(can_get and (not yi_lq))
    self.node_list["red"]:SetActive(can_get and (not yi_lq))
    self.node_list["btn_click"]:SetActive(can_get and (not yi_lq))

    -- --已领取 置灰
    self.node_list["img_ylq"]:SetActive(yi_lq)
    self.node_list["img_ylq2"]:SetActive(yi_lq)
    self.show_item:SetLingQuVisible(yi_lq)
    --self.node_list["after_get_bg"]:SetActive(yi_lq)

    -- --可领取 or 未达到领取条件
    --self.node_list["no_get_txt_bg"]:SetActive((can_get or no_lq) and (not yi_lq))
    self.node_list["no_get_txt_bg"]:SetActive(can_get and (not yi_lq))
    -- --已领取 or 未达到领取条件
    --self.node_list["get_bg"]:SetActive((can_get or no_lq) and (not yi_lq))
    self.node_list["get_bg"]:SetActive(true)

    local week_day = WelfareWGData.Instance:GetGuiOpenWeek()
    self.node_list["img_ycg"]:SetActive(self.data.day < week_day and no_lq)

    self.node_list["desc"].text.text = Language.AccumulativeLogin.DescList[self.index]
    --XUI.SetGraphicGrey(self.node_list["no_get_txt"], no_lq)
    --XUI.SetGraphicGrey(self.node_list["get_bg"], no_lq)

    -- if no_lq then
    --    -- self.show_item:SetGraphicGreyCualityBg(true)
    --     self.show_item:SetDefaultEff(false)
    -- else
    --     self.show_item:SetGraphicGreyCualityBg(false)
    -- end
    
end

function AccmulativeLoginItemRender:OnClickGetReward()
    if not self.data then
		return
	end

    local is_red = WelfareWGData.Instance:GetRewardIsRed(self.data.day)
    if is_red then
        WelfareWGCtrl.Instance:OnCSLeiJiLoginOperate(LEIJI_LOGIN_OPERATE_TYPE.FETCH_REWARD, self.data.day)
    end
end