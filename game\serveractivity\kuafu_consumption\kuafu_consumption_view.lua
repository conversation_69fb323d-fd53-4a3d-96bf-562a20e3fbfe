
KuafuConsumptionRankView = KuafuConsumptionRankView or BaseClass(SafeBaseView)

function KuafuConsumptionRankView:__init()
	self:LoadConfig()
	self:SetMaskBg()
end

function KuafuConsumptionRankView:__delete()
end

function KuafuConsumptionRankView:ReleaseCallBack()
	if nil ~= self.consumerank_list then
		self.consumerank_list:DeleteMe()
		self.consumerank_list = nil
	end

	if self.reward_list ~= nil then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function KuafuConsumptionRankView:LoadConfig()
	self:AddViewResource(0, "uis/view/consume_rank_prefab", "layout_consume_rank")
end

function KuafuConsumptionRankView:LoadCallBack()
	self.consumerank_list = AsyncListView.New(KuafuConsumptionRankItem,self.node_list.rank_list)
	self:CreateRewardList()
	self.node_list["btn_recharge"].button:AddClickListener(BindTool.Bind(self.OnclickBtnRecharge,self))
	local least_cost = ServerActivityWGData.Instance:GetCrossRandActivityConfig().other[1].consume_rank_need_min_num
	if least_cost >= 10000 then
		least_cost = least_cost/10000 .. Language.Common.Wan
	end
	self.node_list["txt_tips"].text.text = string.format(Language.KuafuConsume.Tips, least_cost)
end

function KuafuConsumptionRankView:OnclickBtnRecharge()
	ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz)
end

function KuafuConsumptionRankView:OpenCallBack()
	local param_t = {
		activity_type = ACTIVITY_TYPE.CROSS_ACTIVITY_CONSUME,
		opera_type = CROSS_RA_CHONGZHI_RANK_TYPE.CS_CROSS_RA_CHONGZHI_RANK_REQ_TYPE_INFO,
	}
	CrossServerWGCtrl.Instance:SendCrossRandActivityOperaReq(param_t) 		--玩家跨服充值信息请求
	KuafuConsumptionRankWGCtrl.Instance:SendCrossRAXiaofeiRankReq()				--排行榜请求


	if CountDownManager.Instance:HasCountDown("kuafu_consume") then
		CountDownManager.Instance:RemoveCountDown("kuafu_consume")
	end
	local act_cornucopia_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.CROSS_ACTIVITY_CONSUME) or {}
	if act_cornucopia_info.status == ACTIVITY_STATUS.OPEN then
		local end_time = act_cornucopia_info.end_time or 0
		self:UpdataRollerTime(0,end_time - TimeWGCtrl.Instance:GetServerTime())
		CountDownManager.Instance:AddCountDown("kuafu_consume", BindTool.Bind1(self.UpdataRollerTime, self), BindTool.Bind1(self.CompleteRollerTime, self), end_time, nil, 1)
	else
		self:CompleteRollerTime()
	end
end

function KuafuConsumptionRankView:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("kuafu_recharge") then
		CountDownManager.Instance:RemoveCountDown("kuafu_recharge")
	end
end

-- 奖励列表
function KuafuConsumptionRankView:CreateRewardList()
	self.reward_list = AsyncListView.New(RankRewardItemRender, self.node_list.reward_list)
	local cross_randact_cfg = ServerActivityWGData.Instance:GetCrossRandActivityConfig()
	local data_list = {}

	if nil ~= cross_randact_cfg and nil ~= cross_randact_cfg.consume_rank_show then
		local role_level = RoleWGData.Instance:GetRoleLevel()
		for k,v in ipairs(cross_randact_cfg.consume_rank_show) do
			if role_level >= v.role_level and role_level <= v.level_max then
				table.insert(data_list, v)
			end
		end
		-- table.sort(data_list,function(a,b)
		-- 	print_error(a,b)
		-- 	return a.rank < b.rank
		-- end)
		self.reward_list:SetDataList(data_list,3)
	end

	self.item_list = {}
	for i=1,3 do
		if data_list and data_list[i] then
			self.item_list[i] = ItemCell.New(self.node_list['reward_pos_' .. i])
			self.item_list[i]:SetData(data_list[i].join_reward_item);
		end
	end
end

--刷新排行
function KuafuConsumptionRankView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "kuafu_recharge_rank" then
			self:FlushKuafuConsumptionRankView()
		elseif k == "recharge_info" then
			self:FlushRechargeInfo()
		end
	end
end

function KuafuConsumptionRankView:FlushKuafuConsumptionRankView()
	if self.consumerank_list then
		local rank_count, rank_list = KuafuConsumptionRankWGData.Instance:GetCrossRAConsumeRankInfo()
		self.consumerank_list:SetDataList(rank_list,3)
	end
	local rank_num = KuafuConsumptionRankWGData.Instance:GetSelfRankNum()
	self.node_list["txt_my_rank"].text.text = rank_num and rank_num or Language.Rank.NoRank
end

function KuafuConsumptionRankView:FlushRechargeInfo()
	local total_consume = KuafuConsumptionRankWGData.Instance:GetCrossRAXiaofeiRankXiaofeiInfo()
	if nil ~= total_consume then
		self.node_list["act_my_cost"].text.text = total_consume
	end
end

function KuafuConsumptionRankView:UpdataRollerTime(elapse_time, next_time)
	local time = next_time - elapse_time
	if self.node_list.act_times ~= nil then
		if time > 0 then
			local format_time = TimeUtil.Format2TableDHM(time)
			local str_list = Language.Common.TimeList
			local time_str = ""
			if format_time.day > 0 then
				time_str = format_time.day .. str_list.d
			end
			if format_time.hour > 0 then
				time_str = time_str .. format_time.hour .. str_list.h
			end
			time_str = time_str .. format_time.min .. str_list.min

			self.node_list.act_times.text.text = (time_str)
		end
	end
end

function KuafuConsumptionRankView:CompleteRollerTime()
	if self.act_times ~= nil then
		self.act_times.text.text = ("0")
	end
end
--------------------- RankRewardItemRender ----------------------
RankRewardItemRender = RankRewardItemRender or BaseClass(BaseRender)
function RankRewardItemRender:__init()
	-- self.rank_item_list = {}
end

function RankRewardItemRender:__delete()
	if self.rank_item_list then
		self.rank_item_list:DeleteMe()
		self.rank_item_list = nil
	end
end

function RankRewardItemRender:OnFlush()
	-- 礼包解包
	local data_list = ServerActivityWGData:GetShowRewardListByCfg({self.data.person_reward_item},true) or {}
	-- if nil == next(self.rank_item_list) then
	-- 	for k,v in pairs(data_list) do
	-- 		self.rank_item_list[k] = ItemCell.New(self.node_list["cell" .. k])
	-- 		-- self.rank_item_list[k]:GetCell():setPosition(param.x, param.y)
	-- 		self.rank_item_list[k]:SetData(v)
	-- 	end
	if nil == self.rank_item_list then
		self.rank_item_list = AsyncListView.New(KuafuConsumeBaseCell,self.node_list.reward_list)
	end
	self.rank_item_list:SetDataList(data_list,0)
	local content = Language.KuafuConsume.RankConsumeTitle
   	self.node_list.Text.text.text = string.format(content, self.data.show_ranking, self.data.need_total_consume)
	-- end
end
-------------- KuafuConsumptionRankItem -----------------
KuafuConsumptionRankItem = KuafuConsumptionRankItem or BaseClass(BaseRender)
function KuafuConsumptionRankItem:__init()

end

function KuafuConsumptionRankItem:__delete()

end

function KuafuConsumptionRankItem:LoadCallBack()

end

function KuafuConsumptionRankItem:OnFlush()
	if nil == next(self.data) then return end
	self.node_list.txt_rank.text.text = (self:GetIndex()) 		-- 排名
	self.node_list.txt_name.text.text = self.data.mvp_name

	local server_list = GLOBAL_CONFIG.server_info.server_list	-- 区服
	local cur_plat_type = RoleWGData.Instance.role_vo.plat_type
	if cur_plat_type == self.data.mvp_plat_type then
		for k,v in pairs(server_list) do
			if v.id == self.data.merge_server_id then
				local name = v.name
				name = string.gsub(name, "%(.-%)", "")
				name = string.gsub(name, "（.-）", "")
				self.node_list.txt_server.text.text = (name)
			end
		end
	else
		self.node_list.txt_server.text.text = (Language.Common.WaiYu.."_s"..self.data.merge_server_id)
	end
	self.node_list.txt_cost.text.text = (self.data.total_consume) 		-- 全服总充值
	local rank = self:GetIndex()
	--前三名显示奖牌
	if rank >= 1 and rank <= 3 then
		self.node_list.img_rank:SetActive(true)
		self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonOthers("rank_num_" .. rank))
		if  rank ~= 3 and nil ~= self.data then
			self.node_list.txt_cost.text.text = (Language.Activity.GoldConsumeRankSecrecy)
		end
	else
		self.node_list.img_rank:SetActive(false)
	end
end
function KuafuConsumptionRankItem:CreateSelectEffect()
end

KuafuConsumeBaseCell = KuafuConsumeBaseCell or BaseClass(ItemCell)
function KuafuConsumeBaseCell:__init()
	self.view.transform.localScale = Vector3(0.8, 0.8, 0.8)
end

function KuafuConsumeBaseCell:__delete()
	self.view.transform.localScale = Vector3(1, 1, 1)
end
