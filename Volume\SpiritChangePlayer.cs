using System;
using DG.Tweening;
using ScreenEffects;
using UnityEngine;
using UnityEngine.Playables;

public class SpiritChangePlayer : MonoBehaviour
{
    [SerializeField]
    private VolumeController volumeController;
    [SerializeField]
    private ScreenEffectFeature screenEffectFeature;
    [SerializeField]
    private float screenEffectTime = 1f;
    public ScreenEffect m_Zone;
    public ScreenEffect m_WangQi;

    public Transform CenterTrans
    {
        get => center_trans;
        set => center_trans = value;
    }

    public bool m_PlayWangQiPlaying = false;
    private bool m_Paused = false;
    public bool m_PlayablePlaying = false;
    private Transform center_trans = null;
    private Transform m_particle_trans = null;
    private ParticleSystem particle;
    private float m_PlayableDuration;
    private float m_PlayableTime;
    private float m_ParticalClipIn;
    private float fadeIn = 0.5f;
    private float fadeOut = 0.5f;
    //private 

    private void OnEnable()
    {
        if (volumeController == null)
            volumeController = this.GetComponentInChildren<VolumeController>();
        if (screenEffectFeature == null)
            screenEffectFeature = this.GetComponentInChildren<ScreenEffectFeature>();

        if (screenEffectFeature)
        {
            m_Zone = screenEffectFeature["Zone"];
            m_WangQi = screenEffectFeature["WangQi"];
        }
    }

    private void Update()
    {
        UpdatePlayable();

        if (screenEffectFeature == null)
            return;

        if (m_Zone != null && m_Zone.strength > 0 && center_trans != null)
        {
            m_Zone.block.SetVector("_CenterPosition", this.center_trans.position);
        }

        if (m_PlayWangQiPlaying && this.center_trans != null)
        {
            m_WangQi.block.SetVector("_CenterPosition", this.center_trans.position);
        }
    }

    private void UpdatePlayable()
    {
        if (m_PlayablePlaying && !m_Paused)
        {
            float time = m_PlayableTime;
            time += Time.deltaTime;
            if (m_PlayableTime < fadeIn && time >= fadeIn)
            {
                time = fadeIn;
                PauseScreenEffectZone();
            }
            else if (time >= m_PlayableDuration)
            {
                StopScreenEffectZone();
            }
            UpdateScreenEffectZone(time);
            m_PlayableTime = time;
        }
    }

    public void PlayScreenEffectZone(Transform center_trans, float fade_in, float fade_out)
    {
        if (screenEffectFeature == null || volumeController == null || m_Zone == null)
            return;

        this.center_trans = center_trans;
        fadeIn = fade_in;
        fadeOut = fade_out;
        m_PlayableDuration = fade_in + fade_out;
        m_PlayablePlaying = true;
        m_Paused = false;
        m_PlayableTime = 0;
        //开始时播放Zone屏幕特效、镜头拉远、径向模糊
        screenEffectFeature.DoEffect(m_Zone, 1, screenEffectTime);
        volumeController.DoRadialBlur(new Vector2(0.5f, 0.5f), 0.2f, 0.0f, 0.2f, 0.05f);
        Invoke("ShakeCamera", 0.2f);

        if (this.m_particle_trans != null)
        {
            this.m_particle_trans.gameObject.SetActive(true);
        }
        m_ParticalClipIn = 0;
    }

    public void SetScreenEffectZoneEffect(Transform particle_trans)
    {
        particle = particle_trans.GetComponentInChildren<ParticleSystem>(true);
        this.m_particle_trans = particle_trans;
    }

    public void UpdateScreenEffectZone(float f)
    {
        if (screenEffectFeature == null || m_Zone == null)
            return;

        if (particle != null)
        {
            //驱动粒子特效
            particle.Simulate(f + m_ParticalClipIn);
        }
    }

    public void PauseScreenEffectZone()
    {
        m_Paused = true;
    }

    public bool CanPlayScreenEffectZone()
    {
        return true;
    }

    public void ContinueScreenEffectZone()
    {
        if (screenEffectFeature == null || m_Zone == null)
            return;

        m_Paused = false;
        screenEffectFeature.DoEffect(m_Zone, 0, screenEffectTime);
        //假如特效时长为5秒，淡入淡出时间各为0.5秒，那么淡入区间为[0 ~ 0.5], 淡出区间为[4.5 ~ 5]

        if (particle != null)
        {
            m_ParticalClipIn = particle.main.duration - fadeOut - m_PlayableTime;
        }
    }

    public void StopScreenEffectZone()
    {
        if (screenEffectFeature == null)
            return;
        m_PlayablePlaying = false;

        if (m_particle_trans != null)
        {
            m_particle_trans.gameObject.SetActive(false);
        }
    }

    public void ShowWangQi(Transform center_transform, float fade_in)
    {
        if (screenEffectFeature == null || m_Zone == null)
            return;

        this.center_trans = center_transform;
        m_PlayWangQiPlaying = true;
        screenEffectFeature.DoEffect(m_WangQi, 1, fade_in);
        m_WangQi.block.SetVector("_CenterPosition", this.center_trans.position);
    }

    public void CloseWangQi(float fade_out)
    {
        if (screenEffectFeature == null || m_Zone == null)
            return;

        m_PlayWangQiPlaying = false;
        screenEffectFeature.DoEffect(m_WangQi, 0, fade_out);
    }

    private void ShakeCamera()
    {
        CameraShake.Shake();
    }
}