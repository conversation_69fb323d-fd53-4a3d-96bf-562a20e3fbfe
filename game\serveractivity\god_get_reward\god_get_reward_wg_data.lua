GodGetRewardWGData = GodGetRewardWGData or BaseClass()

function GodGetRewardWGData:__init()
    if GodGetRewardWGData.Instance then
		error("[GodGetRewardWGData] Attempt to create singleton twice!")
		return
	end
	GodGetRewardWGData.Instance = self
    self.info = {}
    self.record = {}
    self.result_flag = {}
    self.result = {}
    self.pool_id = {}
    self.exchange_info_list = {}
    self.exchange_score_list = {}
    self.reward_list = {}

    self.reward_id_cfg_list = nil

    RemindManager.Instance:Register(RemindName.GodGetReward, BindTool.Bind(self.ShowRemind, self))
    self:RegisterRewardRemindInBag()
end

function GodGetRewardWGData:__delete()
    self.reward_id_cfg_list = nil
    RemindManager.Instance:UnRegister(RemindName.GodGetReward)
	GodGetRewardWGData.Instance = nil
end

function GodGetRewardWGData:RegisterRewardRemindInBag()
    local map = {}

    local pool_cfg = self:GetRewardPoolCfg()
    for _, cfg in pairs(pool_cfg) do
        map[cfg.draw_consume_item_id] = true
    end

    local item_id_list = {}
    for k,v in pairs(map) do
        table.insert(item_id_list, k)
    end

    BagWGCtrl.Instance:RegisterRemindByItemChange(RemindName.GodGetReward, item_id_list, nil)
end

function GodGetRewardWGData:ShowRemind()
    if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GOD_XUNBAO) then
        return 0
    end

    local layer_cfg = self:GetLayerCfg()
	for i = 1, #layer_cfg do
        if self:IsShowBtnDrawRedByLayer(i - 1) then
            return 1
        end
	end

    return 0
end

function GodGetRewardWGData:ClearLayerInfo()
    local layer_length = #self:GetLayerCfg()
    for i = 1, layer_length do
        self["cur_round_" .. i] = nil
        self["reward_id_list_" .. i] = nil
    end
end

function GodGetRewardWGData:ClearLayerCache(layer)
    self.reward_list[layer] = nil
end

function GodGetRewardWGData:SetLayerInfo(protocol)
    self.info[protocol.layer] = protocol.draw_reward_id_list
    self.result_flag[protocol.layer] = protocol.draw_result_flag
    self.pool_id[protocol.layer] = protocol.reward_pool_id
end

function GodGetRewardWGData:SetRecordInfo(protocol)
    self.record = protocol.draw_record
end

function GodGetRewardWGData:SetResultInfo(protocol)
    local result = {}
    result.reward_id = protocol.reward_id
    result.slot = protocol.hit_slot
    result.is_one_key = protocol.is_one_key == 1
    result.get_exchange_score = protocol.get_exchange_score
    self.result = result
end

function GodGetRewardWGData:GetResultInfo()
    return self.result
end

function GodGetRewardWGData:GetPoolIdByLayer(layer)
    return self.pool_id[layer]
end

function GodGetRewardWGData:UpdateRecordCount()
    self.near_record_time = TimeWGCtrl.Instance:GetServerTime()
    RoleWGData.SetRolePlayerPrefsInt("god_get_record_tiem", self.near_record_time)
    self:SetNewRecordCount(0)
end

function GodGetRewardWGData:CalNewRecordNum()
    self.near_record_time = RoleWGData.GetRolePlayerPrefsInt("god_get_record_tiem")
    local count = 0
    for i, v in pairs(self.record) do
        if v.timestamp > self.near_record_time then
            count = count + 1
        end
    end
    self:SetNewRecordCount(count)
end

function GodGetRewardWGData:SetNewRecordCount(count)
    self.new_record_count = count
end

function GodGetRewardWGData:GetNewRecordCount()
    return self.new_record_count or 0
end

function GodGetRewardWGData:GetResultFlagByLayer(layer)
    return self.result_flag[layer]
end

function GodGetRewardWGData:HasNextPool(layer)
    local pool_id = self:GetPoolIdByLayer(layer)
    if pool_id == -1 then
        return false
    end
    local _, round_count, __ = self:GetActRoundTimeByLayer(layer)
    local reward_pool_id_list = self:GetRewardPoolIdByLayerAndCount(layer, round_count)
    if nil == reward_pool_id_list then
        return false
    end

    local str_id = reward_pool_id_list[#reward_pool_id_list]
    return tonumber(str_id) ~= pool_id
end

function GodGetRewardWGData:GetCurRewardListByLayer(layer)
    if layer == nil then
        return
    end
    if not self.reward_list[layer] then
        local list = self.info[layer] or {}
        local temp_list = {}
        for i, v in ipairs(list) do
            if v > 0 then
                temp_list[#temp_list + 1] = v
            end
        end
        self.reward_list[layer] = temp_list
    end
    return self.reward_list[layer]
end

function GodGetRewardWGData:GetRewardListByLayer(layer)
    if layer == nil then
        return
    end

    local reward_id_list = self:GetCurRewardListByLayer(layer)
    reward_id_list = SortTableKey(reward_id_list, true)
    local big_reward_list = {}
    local cambered_list_data = {}
    local other_list_data = {}
    for i = 1, #reward_id_list do
        if i <= 1 then
            table.insert(big_reward_list, reward_id_list[i])
        elseif i <= 8 then
            table.insert(cambered_list_data, reward_id_list[i])
        else
            table.insert(other_list_data, reward_id_list[i])
        end
    end

    return big_reward_list, cambered_list_data, other_list_data
end

function GodGetRewardWGData:GetRecordInfo()
    local list = {}
    for i, v in ipairs(self.record) do
        if v.reward_id > 0 then
            table.insert(list, v)
        else
            break
        end
    end
    return list
end

function GodGetRewardWGData:GetLayerRewardNumByLayer(layer)
    return bit:d2b1n(self.result_flag[layer] or 0)
end

function GodGetRewardWGData:GetActRoundTimeByLayer(layer)
    local pool_id = self:GetPoolIdByLayer(layer)
    local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GOD_XUNBAO)
    if not act_info or not pool_id then
        return 0, 0, 0
    end

    local cfg_list = self:GetLayerCfg()
    local cfg_data = cfg_list and cfg_list[layer + 1]
    if not cfg_data then
        return 0, 0, 0
    end

    local cur_round_count = 1 -- 当前轮数
    local round_time_list = Split(cfg_data.round_continue_time_h, "|")

    if pool_id < 0 then -- 都抽空了
        cur_round_count = #round_time_list
    else
        local pool_list = Split(cfg_data.reward_pool_id, "|")
        pool_id = tostring(pool_id)
        for i=1,#pool_list do
            if pool_id == pool_list[i] then
                cur_round_count = i
                break
            end
        end
    end
    
    local now_time = TimeWGCtrl.Instance:GetServerTime()
    local pass_time = now_time - act_info.start_time
    local next_round_time = 0
    for i = 1, #round_time_list do
        next_round_time = next_round_time + round_time_list[i] * 3600
        if pass_time < next_round_time and cur_round_count <= i then
            break
        end
    end
    next_round_time = next_round_time - pass_time

    if next_round_time > 0 then
        return next_round_time, cur_round_count, cfg_data.round_count
    else
        return -999, cur_round_count, cfg_data.round_count -- -999仅拿来做过期标志
    end
end

function GodGetRewardWGData:GetActCfg()
    local cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
    return cfg
end

function GodGetRewardWGData:GetRewardPoolCfg()
    return self:GetActCfg().tianshenxunbao_reward_pool
end

function GodGetRewardWGData:GetLayerCfg()
    return self:GetActCfg().tianshenxunbao_layer
end

function GodGetRewardWGData:GetRewardCfg()
    if nil == self.reward_id_cfg_list then
        self.reward_id_cfg_list = ListToMapList(self:GetActCfg().tianshenxunbao_reward, "reward_id")
    end
    return self.reward_id_cfg_list
end

function GodGetRewardWGData:GetRewardPoolIdByLayerAndCount(layer, round_count)
    local cfg = self:GetLayerCfg()
    local pool_list
    for i, v in pairs(cfg) do
        if v.layer == layer then
            --添加容错判空，虽然不知道怎么错的
            if v.reward_pool_id == nil then
                return nil
            end

            pool_list = Split(v.reward_pool_id, "|")
            break
        end
    end
    if pool_list and pool_list[round_count] then
        return Split(pool_list[round_count], ",")
    end
end

function GodGetRewardWGData:GetCurDrawInfoByLayer(layer)
    local cfg = self:GetRewardPoolCfg()
    local cur_pool_id = self:GetPoolIdByLayer(layer)
    -- 当前抽取次数
    local cur_draw_times = self:GetLayerRewardNumByLayer(layer) + 1
    local info
    for _, reward_pool in pairs(cfg) do
        if cur_pool_id == reward_pool.reward_pool_id then
            info = {}
            info.reward_pool_id = reward_pool.reward_pool_id
            info.draw_consume_item_id = reward_pool.draw_consume_item_id
            info.reward_id_list = reward_pool.reward_id_list
            info.shop_seq = reward_pool.shop_seq
            info.one_key = reward_pool.one_key
            info.draw_consume_item_count = 0
            info.draw_all_consume_item_count = 0
            local draw_times = Split(reward_pool.need_draw_times, "|")
            local draw_count = Split(reward_pool.draw_consume_item_count, "|")
            for i, v in ipairs(draw_times) do
                local times = Split(v, ",")
                if cur_draw_times >= tonumber(times[1]) and cur_draw_times <= tonumber(times[2]) then
                    info.draw_consume_item_count = tonumber(draw_count[i])
                end
                if cur_draw_times <= tonumber(times[2]) then
                    info.draw_all_consume_item_count = info.draw_all_consume_item_count + tonumber(draw_count[i])
                end
            end
            break
        end
    end
    return info
end

function GodGetRewardWGData:GetRewardInfoByRewardID(id)
    local cfg = self:GetRewardCfg()
    local reward_id_cfg = cfg[id]
    return not IsEmptyTable(reward_id_cfg) and reward_id_cfg[1] or {}
end

-- 获取当前抽奖结果
function GodGetRewardWGData:GetCurGetList(layer, draw_all_flag)
    local result = GodGetRewardWGData.Instance:GetResultInfo()
    local data_list = {}
    if result.is_one_key or draw_all_flag then
        local reward_id_list = self:GetCurRewardListByLayer(layer or result.layer)
        for i, v in ipairs(reward_id_list) do
            local state = self:GetRewardIsShowByLayerAndId(layer or result.layer, i)
            if state then
                local reward_info = self:GetRewardInfoByRewardID(v)
                if not IsEmptyTable(reward_info) then
                    table.insert(data_list,reward_info.reward_item)
                end
            end
        end
        
    else
        local reward_info = self:GetRewardByLayerAndSlot(result.layer, result.slot)
        table.insert(data_list, reward_info.reward_item)
    end

    return data_list
end

-- 获取大奖信息
function GodGetRewardWGData:GetBestRewardInfo(layer)
    local reward_id_list = self:GetCurRewardListByLayer(layer)
    local reward_info
    for i, v in ipairs(reward_id_list) do
        reward_info = self:GetRewardInfoByRewardID(v)
        if not IsEmptyTable(reward_info) then
            if reward_info.is_best == 1 then
                return reward_info
            end
        end
    end

    return {}
end

-- 获取大奖是否被抽取
function GodGetRewardWGData:GetBestRewardHasGet(layer, reward_id_list)
    local state
    local reward_info
    for i, v in ipairs(reward_id_list) do
        state = self:GetRewardIsShowByLayerAndId(layer, i)
        reward_info = self:GetRewardInfoByRewardID(v)
        if state and not IsEmptyTable(reward_info) and reward_info.is_best == 1 then
            return false
        end
    end

    return true
end

-- 当前奖励是否没抽取
function GodGetRewardWGData:GetRewardIsShowByLayerAndId(layer, index)
    local flag = self.result_flag[layer] or 0
    return bit:_and(flag, bit:_lshift(1, index - 1)) == 0
end

function GodGetRewardWGData:GetIsDrawEmptyByLayer(layer)
    local list = self:GetCurRewardListByLayer(layer) or {}
    local num = self:GetLayerRewardNumByLayer(layer)
    return #list <= num
end

function GodGetRewardWGData:GetRandomTalkByLayer(layer)
    local cfg = self:GetActCfg().tianshenxunbao_bubble
    local bubble_list = {}
    for i, v in pairs(cfg) do
        if v.layer == layer then
            table.insert(bubble_list, v)
        end
    end
    local rand_num = GameMath.Rand(1, #bubble_list)
    local info = bubble_list[rand_num]
    return info and info.bubble_content or ""
end

function GodGetRewardWGData:GetLayerCfgByLayer(layer)
    local cfg = self:GetLayerCfg()
    for i, v in pairs(cfg) do
        if v.layer == layer then
            return v
        end
    end
end

function GodGetRewardWGData:GetRewardByLayerAndSlot(layer, slot)
    local reward_list = self:GetCurRewardListByLayer(layer)
    local id = reward_list[slot+1]
    return self:GetRewardInfoByRewardID(id)
end

function GodGetRewardWGData:GetAnimDelayTime()
    local other = self:GetActCfg().tianshen_theme_other[1]
    return other.tips_delay or 3
end

function GodGetRewardWGData:GetAnimRoleTime()
    local other = self:GetActCfg().tianshen_theme_other[1]
    return other.role_anim_time or 0
end

function GodGetRewardWGData:GetAnimAngleSpeed()
    local other = self:GetActCfg().tianshen_theme_other[1]
    return other.angle_speed or 50
end

function GodGetRewardWGData:GetAnimArrowSpeed()
    local other = self:GetActCfg().tianshen_theme_other[1]
    return other.arrow_speed or 500
end

function GodGetRewardWGData:GetAnimKiteTime()
    local other = self:GetActCfg().tianshen_theme_other[1]
    return other.kite_time or 1
end

function GodGetRewardWGData:IsShowBtnDrawRedByLayer(layer)
    if not self:CheckLayerIsOpenByPageIndex(layer + 1) then
        return false
    end

    local is_empty = self:GetIsDrawEmptyByLayer(layer)
    if not is_empty then
        local cur_draw_info = self:GetCurDrawInfoByLayer(layer)
        if cur_draw_info then
            local num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_info.draw_consume_item_id)
            if cur_draw_info.draw_consume_item_count - num <= 0 then
                return true
            end
        end
    end

    if self:GetExchangeTodayRemind(layer) then
        return true
    end
end

function GodGetRewardWGData:GetBigRewardExistByLayer(layer)
    local list = self:GetCurRewardListByLayer(layer)
    local cfg
    for i, v in pairs(list) do
        cfg = self:GetRewardInfoByRewardID(v)
        local is_show = self:GetRewardIsShowByLayerAndId(layer, i)
        if not IsEmptyTable(cfg) and cfg.is_best == 1 and is_show then
            return true
        end
    end
    return false
end

function GodGetRewardWGData:SetSelectLayer(cur_layer)
    self.select_layer_index = cur_layer
end

function GodGetRewardWGData:GetSelectLayer()
    return self.select_layer_index or 0
end

-- 检测某个档位的夺宝是否开启了
function GodGetRewardWGData:CheckLayerIsOpenByPageIndex(page_index, need_tip)
    local cfg_list = self:GetLayerCfg()
    local cfg_data = cfg_list and cfg_list[page_index]

    if cfg_data then
        local act_open_day = OperationActivityWGData.Instance:GetActOpenDay(ACTIVITY_TYPE.GOD_XUNBAO)
        if act_open_day < cfg_data.openday then
            return false, need_tip and string.format(Language.TSXunBao.NotOpenTips, cfg_data.name)
        end
        local last_time = self:GetActRoundTimeByLayer(page_index - 1)
        if last_time <= -999 then
            return false, Language.TSXunBao.NotOpenTips2
        end
    end
    return true
end

---[[ 兑换物品
function GodGetRewardWGData:SetExchangeInfo(protocol)
    local act_cfg = self:GetActCfg()
    local cfg_list = act_cfg and act_cfg.tianshenxunbao_exchange
    if not cfg_list then
        return
    end

    local cur_layer = protocol.layer
    local count_list = {}
    for _,v in ipairs(protocol.item_list) do
        count_list[v.item_id] = v.exchange_num
    end

    local info_list = {}
    for _,v in ipairs(cfg_list) do
        if v.layer == cur_layer then
            local exchange_count = 0
            if v.exchange_item and v.exchange_item.item_id then
                exchange_count = count_list[v.exchange_item.item_id] or 0
            end
            local data = {cfg = v, exchange_count = exchange_count}
            info_list[#info_list + 1] = data
        end
    end

    self.exchange_info_list[cur_layer] = info_list
    self.exchange_score_list[cur_layer] = protocol.exchange_score
end

function GodGetRewardWGData:GetExchangeDataList(layer)
    return self.exchange_info_list[layer] or {}
end

function GodGetRewardWGData:GetExchangeScore(layer)
    return self.exchange_score_list[layer] or 0
end

function GodGetRewardWGData:HasExchangeByLayer(layer)
    local act_cfg = self:GetActCfg()
    local cfg_list = act_cfg and act_cfg.tianshenxunbao_exchange
    if cfg_list then
        for i=1,#cfg_list do
            if cfg_list[i].layer == layer then
                return true
            end
        end
    end
    return false
end

function GodGetRewardWGData:GetExchangeTodayRemind(layer)
    if not layer then
        return false
    end

    local uuid = RoleWGData.Instance:GetUUid()
    local key = string.format("%s%s%s%s", uuid.temp_low, uuid.temp_high, "GodGetRewardExchange", layer)
    local remind_day = PlayerPrefsUtil.GetInt(key)
    local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

    if cur_day == remind_day then
        return false
    end

    local score = self:GetExchangeScore(layer)
    local info_list = self:GetExchangeDataList(layer)
    if info_list then
        for _,v in pairs(info_list) do
            if v.exchange_count < v.cfg.exchange_limit and score >= v.cfg.exchange_consume then
                return true
            end
        end
    end

    return false
end

function GodGetRewardWGData:SetExchangeTodayRemind(layer)
    local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local uuid = RoleWGData.Instance:GetUUid()
    local key = string.format("%s%s%s%s", uuid.temp_low, uuid.temp_high, "GodGetRewardExchange", layer)
    PlayerPrefsUtil.SetInt(key, cur_day)
end
--]]

function GodGetRewardWGData:CheckNeedOpenDaLian(params)
    local match_id = tonumber(params)
    for i = 1, 3 do
        local index = i
        local layer = index - 1
        local is_layer_open = self:CheckLayerIsOpenByPageIndex(index, false)
        if is_layer_open then
            local reward_list = self:GetCurRewardListByLayer(layer)
            for k,v in pairs(reward_list) do
                local reward_cfg = self:GetRewardInfoByRewardID(v)
                if not IsEmptyTable(reward_cfg) then
                    if tonumber(reward_cfg.reward_item.item_id) == match_id then
                        return true
                    end
                end
            end
        end
    end
    return false
end