SiXiangSaleState = {
	Close = 0,
	Open = 1,
}

SiXiangRecordType = {
	ALL = 1,
	SELF = 2
}

SiXiangBuyType = {
	RMB = 0,
	XY = 1,
}

DAOHUN_YUGAO_REWARD_TYPE = {
	INVALID = 0,					-- 不可领取
	CAN_FETCH = 1,					-- 可领取
	HAD_FETCH = 2,					-- 已领取
}

--元神召唤
OP_YUAN_SHEN_ZHAO_HUAN_TYPE = {
	INFO = 0,             -- 请求元神召唤信息
	DRAW = 1,             -- 进行抽奖
	SHOP_CONVERT = 2,     -- 商店兑换
	TE_DIAN = 3,          -- 请求特典操作
}

--元神召唤抽奖操作
OP_YSZH_DRAW_TYPE = {
	SINGLE = 0,				--单抽
	MULTI = 1,				--十连
	USE_MULTI_ITEM = 2,		--十连特殊道具
	NOR_SINGLE = 3,			--通用单抽
	NOR_MULTI = 4,			--通用十连
	NOR_USE_MULTI_ITEM = 5,	--通用十连特殊道具
}

--元神召唤奖励类型
OP_YSZH_REWARD_TYPE = {
	SINGLE = 0,				--单抽
	FIRST_N = 1,			--十连
	NO_FIRST = 2,			--普通抽奖
	USE_MULTI_ITEM = 3,		--特殊道具
}

OP_YUAN_SHEN_ZHAO_HUAN_TYPE_TE_DIAN_TYPE = {
	OP_YUAN_SHEN_ZHAO_HUAN_TYPE_TE_DIAN_TYPE_INFO = 0,                -- 请求特典信息
	OP_YUAN_SHEN_ZHAO_HUAN_TYPE_TE_DIAN_TYPE_GET_RMB_REWARD = 1,      -- 领取特典子活动rmb购买的商品
	OP_YUAN_SHEN_ZHAO_HUAN_TYPE_TE_DIAN_TYPE_BUY_PRODUCT = 2,         -- 请求购买特典子活动售卖商品（非rmb类型商品）
}

YuanShenZhaoHuanSubActId = {
    ProbabilityUp = 1,          --概率提升
    ShiLian1 = 2,               --1折十连
    ShiLian2 = 3,               --2折十连
    ZhenXuan1 = 4,              --元神珍选
    ZhenXuan2 = 5,              --元神珍选
    TeHuiZhaoHuan = 6,          --特惠召唤
}

FIGHT_SOUL_ITEM_TYPE = {
	SOUL = 1,					--魂晶
	BONE = 2,					--魂骨
}

--子活动售卖状态
YuanShenSaleSubActSaleStatus = {
	NotBuy = 0, 			--未购买
	HasBuyNotFetch = 1, 	--已购买未领取（rmb商品会用到）
	HasBuyAndFetched = 2, 	--已购买已领取
}