HongHuangClassicWGData = HongHuangClassicWGData or BaseClass()
function HongHuangClassicWGData:__init()
    if nil ~= HongHuangClassicWGData.Instance then
        ErrorLog("[HongHuangClassicWGData]:Attempt to create singleton twice!")
    end
    HongHuangClassicWGData.Instance = self

    self.special_reward_flag = {}
    self.special_task_flag = {}

	self.chaotic_gift_config = ConfigManager.Instance:GetAutoConfig("chaotic_gift_auto")
    self.special_cfg = self.chaotic_gift_config.special
    self.special_day_cfg = ListToMapList(self.chaotic_gift_config.special, "day")
    self.special_task_cfg = ListToMapList(self.chaotic_gift_config.special_task, "chapter")

    RemindManager.Instance:Register(RemindName.HongHuangClassic, BindTool.Bind(self.GetHongHuangClassicRemind, self))
end

function HongHuangClassicWGData:__delete()
    HongHuangClassicWGData.Instance = nil
	self.chaotic_gift_config = nil
    self.special_reward_flag = nil
    self.special_task_flag = nil
    self.special_cfg = nil
    self.special_task_cfg = nil
    RemindManager.Instance:UnRegister(RemindName.HongHuangClassic)
end

function HongHuangClassicWGData:SetChaoticGiftSpecialInfo(protocol)
    local reward_bit_list = {}
    local bit_index = 0
    for i, v in ipairs(protocol.special_reward_flag) do
        reward_bit_list = bit:d2b_l2h(v, reward_bit_list)
        for j = 1, 8 do
            self.special_reward_flag[bit_index] = reward_bit_list[j]
            bit_index = bit_index + 1
        end
    end

	local bit_list = {}
	local index = 0
 	for i, v in ipairs(protocol.special_task_flag) do
 		bit_list = bit:d2b_l2h(v, bit_list)
 		for j = 1, 8 do
 			self.special_task_flag[index] = bit_list[j]
 			index = index + 1
 		end
 	end
end

function HongHuangClassicWGData:GetHongHuangClassicRemind()
    local current_chapter = self:GetCurrentChapter()
    local state = self:GetCurrentChapterState(current_chapter)
	return state and 1 or 0
end

function HongHuangClassicWGData:GetTaskState(task_id)
    return self.special_task_flag[task_id] == 1
end

function HongHuangClassicWGData:GetTaskNum(chapter_id)
    local data_list = self.special_task_cfg[chapter_id]
   local all_num = #data_list
   local complete_num = 0
   for k, v in pairs(data_list) do
        if self:GetTaskState(v.task_id) then
            complete_num = complete_num + 1
        end
   end
    
   return complete_num, all_num
end
   
function HongHuangClassicWGData:GetCurrentChapterState(chapter_id)
    local chapter_flag = self.special_reward_flag[chapter_id] == 1
    if chapter_flag then
        return false
    else
        if not IsEmptyTable(self.special_task_cfg[chapter_id]) then
            local task_state = true
            for k, v in pairs(self.special_task_cfg[chapter_id]) do
                if not self:GetTaskState(v.task_id) then
                    task_state = false
                    break
                end
            end
            return task_state
        else
            return false
        end
    end
end

function HongHuangClassicWGData:GetCurrentChapter()
    local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay() -- 新增需求（增加天数判断）
    local day_list_cfg
    local cur_chapter = 1
    for i = server_day, 1, -1 do
        if self.special_day_cfg[i] then
            day_list_cfg = self.special_day_cfg[i]
            break
        end
    end

    if day_list_cfg then
        cur_chapter = day_list_cfg[#day_list_cfg].chapter
        for i, v in ipairs(self.special_cfg) do
            if self.special_reward_flag[i] == 0 and v.day <= server_day then
                cur_chapter = i
                break
            end
        end
    end

    return cur_chapter
end

function HongHuangClassicWGData:GetChapterTaskData()
    local current_chapter = self:GetCurrentChapter()
    return current_chapter, self.special_cfg[current_chapter], self.special_cfg[current_chapter + 1] or {}, self.special_task_cfg[current_chapter] or {}
end

function HongHuangClassicWGData:GetIsAllBuy()
    local all_buy = true
    local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay() -- 新增需求（增加天数判断）
    for i, v in ipairs(self.special_cfg) do
        if self.special_reward_flag[i] == 0 and v.day <= server_day then
            all_buy = false
            break
        end
    end

    return all_buy
end