TeamView = TeamView or BaseClass(SafeBaseView)
local YuanGuanCanSeeCount = 7 --目前展开远古仙殿可见数量
local TeamWorldInvite = "my_team_world_invite" --喊话

local CustomPos = {
	Vector2(-520, 90),
	Vector2(-260, 90),
	Vector2(0, 90),
	Vector2(260, 90),
	Vector2(520, 90),
}

function TeamView:MyTeamLoadCallBack()
    self.pingtai_left_btn_gameObject_list = {}
    self.mt_team_list = {}
    self.mt_team_info_list = {}
	for i=1,GameEnum.MAX_TEAM_MEMBER_NUMS do
		local async_loader = AllocAsyncLoader(self, "TeamMemberItem" .. i)
		async_loader:SetParent(self.node_list["ph_team_list"].transform)
		async_loader:Load("uis/view/new_team_ui_prefab", "ph_team_render",
		function (obj)
			obj = U3DObject(obj)
			self.mt_team_list[i] = TeamListItem.New(obj)
			self.mt_team_list[i]:SetIndex(i)
			if i == GameEnum.MAX_TEAM_MEMBER_NUMS then
				self:MyTeamTeamCellFlush()
				
			end
		end)
	end

	for i = 1, GameEnum.MAX_TEAM_MEMBER_NUMS do
		local async_loader2 = AllocAsyncLoader(self, "TeamMemberInfoItem" .. i)
		async_loader2:SetParent(self.node_list["ph_team_info_list"].transform)
		async_loader2:Load("uis/view/new_team_ui_prefab", "ph_team_info_render",
			function (obj)
				obj = U3DObject(obj)
				self.mt_team_info_list[i] = TeamMemberInfoListItem.New(obj)
				self.mt_team_info_list[i]:SetIndex(i)
				if i == GameEnum.MAX_TEAM_MEMBER_NUMS then
					self:TeamInfoCellFlush()
				end
			end)
	end

	self.alert_window1 = Alert.New()
	self.alert_window1:SetLableString(Language.NewTeam.EnterTip)
	self.alert_window1:SetOkFunc(BindTool.Bind1(self.OnClickAlertWindowOK, self))

	self.alert_window3 = Alert.New()
	-- self.alert_window3:SetTitle(Language.Common.Hint)

	-- self.alert_member_full = Alert.New()
	-- self.alert_member_full:SetOkFunc(BindTool.Bind1(self.EnterFuBen, self))

	self.alert_member_notfull = Alert.New()
	self.alert_member_notfull:SetOkFunc(BindTool.Bind1(self.EnterFuBen, self))

	XUI.AddClickEventListener(self.node_list.btn_mt_create_team, BindTool.Bind1(self.OnClickCrateTeam, self))
	-- XUI.AddClickEventListener(self.node_list.btn_mt_change_goal, BindTool.Bind1(self.OnClickChangeGoal, self))
	-- XUI.AddClickEventListener(self.node_list.level_limit_bg, BindTool.Bind1(self.OnClickChangeGoal, self))
	XUI.AddClickEventListener(self.node_list.btn_mt_invite2, BindTool.Bind1(self.OnClickInvite, self))
	XUI.AddClickEventListener(self.node_list.btn_mt_invite3, BindTool.Bind1(self.OnClickInvite, self))
	XUI.AddClickEventListener(self.node_list.btn_mt_invite4, BindTool.Bind1(self.OnClickInvite, self))
	XUI.AddClickEventListener(self.node_list.btn_mt_invite5, BindTool.Bind1(self.OnClickInvite, self))
	XUI.AddClickEventListener(self.node_list.btn_mt_rule, BindTool.Bind1(self.OnClickRule, self))
	XUI.AddClickEventListener(self.node_list.btn_mt_pingtai, BindTool.Bind1(self.OnClickPingTai, self))
	XUI.AddClickEventListener(self.node_list.btn_mt_match, BindTool.Bind1(self.OnClickAutoMatch, self))
	XUI.AddClickEventListener(self.node_list.btn_mt_quick_team, BindTool.Bind1(self.OnClickQuick, self))
	XUI.AddClickEventListener(self.node_list.btn_mt_apply, BindTool.Bind1(self.OnClickApplyList, self))
	XUI.AddClickEventListener(self.node_list.mt_btn_add_times, BindTool.Bind1(self.OnClickAddTimes, self)) 			--增加剩余奖励次数
	--XUI.AddClickEventListener(self.node_list.mt_auto_pass_btn, BindTool.Bind1(self.OnClickAutoPassBtn, self))
	XUI.AddClickEventListener(self.node_list.mt_level_limit_btn, BindTool.Bind1(self.OnClickChangeGoal, self))
	XUI.AddClickEventListener(self.node_list.btn_mt_follow, BindTool.Bind1(self.OnClickFollowBtn, self))
	XUI.AddClickEventListener(self.node_list.my_team_to_clone, BindTool.Bind1(self.OnClickToClone, self))
	XUI.AddClickEventListener(self.node_list.share_card_btn, BindTool.Bind1(self.OnClickShareCard, self))


	-- XUI.AddClickEventListener(self.node_list.btn_change_leader, BindTool.Bind1(self.OnClickChangeLeader, self))
	for i = 1, 5 do
		-- XUI.AddClickEventListener(self.node_list["btn_mt_choose_" .. i], BindTool.Bind(self.OnClickChoose, self),i)
		self.node_list["btn_mt_choose_" .. i].button:AddClickListener(BindTool.Bind(self.OnClickChoose,self,i))
	end
	XUI.AddClickEventListener(self.node_list.btn_mt_block, BindTool.Bind1(self.OnClickBlock, self))

	XUI.AddClickEventListener(self.node_list.btn_mt_word_talk, BindTool.Bind1(self.OnClickWorldTalk, self))

	--self.world_talk_timecount = GlobalEventSystem:Bind(TeamWorldTalk.NEW_TEAM_WORLD_TALK,BindTool.Bind(self.UpdateTeamWordTalkBtn,self))
    --self.word_talk_cd_over = GlobalEventSystem:Bind(TeamWorldTalk.COMPLETE_CALL_BACK,BindTool.Bind(self.ComleteTeamWoldTalkCD,self))
    if not self.team_cell_flush then
        self.team_cell_flush = GlobalEventSystem:Bind(TeamInfoQuery.TEAM_INFO_BACK, BindTool.Bind(self.TeamFlush,self))
    end

	-- self.role_info_back = GlobalEventSystem:Bind(TeamInfoQuery.ROLE_INFO_BACK,BindTool.Bind(self.RoleInfoBack,self))
	self.member_list = {}

	self.has_load_callback = true

	if self.first_open then
		--NewTeamWGCtrl.Instance:SendSCTeamMatchStateReq()
		self.first_open = false
	end

	if self.delay_flush then
		self.delay_flush = false
		self:TeamFlush()
	end

	self.my_team_day_count_change = GlobalEventSystem:Bind(OtherEventType.DAY_COUNT_CHANGE, BindTool.Bind(self.OnMyTeamDayCountChange, self))
	self.old_member_count = -1
end

function TeamView:MyTeamReleaseCallBack()
    self.pingtai_left_btn_gameObject_list = nil

    if self.team_type_change_event then
		GlobalEventSystem:UnBind(self.team_type_change_event)
		self.team_type_change_event = nil
	end

	if self.my_team_day_count_change then
		GlobalEventSystem:UnBind(self.my_team_day_count_change)
		self.my_team_day_count_change = nil
	end

	if self.mt_team_list then
		for k,v in pairs(self.mt_team_list) do
			if v then
				v:DeleteMe()
			end
		end
		self.mt_team_list = {}
	end

	if self.mt_team_info_list then
		for k,v in pairs(self.mt_team_info_list) do
			if v then
				v:DeleteMe()
			end
		end
		self.mt_team_info_list = {}
	end

	if self.team_goal_list then
		for k,v in pairs(self.team_goal_list) do
			if v then
				v:DeleteMe()
			end
		end
		self.team_goal_list = nil
	end

	if self.alert_window1 then
		self.alert_window1:DeleteMe()
		self.alert_window1 = nil
	end

	if self.alert_window3 then
		self.alert_window3:DeleteMe()
		self.alert_window3 = nil
	end

	if self.mt_cancel_match_alert then
		self.mt_cancel_match_alert:DeleteMe()
		self.mt_cancel_match_alert = nil
	end

	-- if self.alert_member_full then
	-- 	self.alert_member_full:DeleteMe()
	-- 	self.alert_member_full = nil
	-- end

	if self.alert_member_notfull then
		self.alert_member_notfull:DeleteMe()
		self.alert_member_notfull = nil
	end

	if self.team_cell_flush then
		GlobalEventSystem:UnBind(self.team_cell_flush)
		self.team_cell_flush = nil
	end

	if self.world_talk_timecount then
		GlobalEventSystem:UnBind(self.world_talk_timecount)
		self.world_talk_timecount = nil
	end

	if self.word_talk_cd_over then
		GlobalEventSystem:UnBind(self.word_talk_cd_over)
		self.word_talk_cd_over = nil
	end

	if self.btn_leader_list then
		self.btn_leader_list:DeleteMe()
		self.btn_leader_list = nil
	end

	self.has_specail_load = nil
	self.need_speail_load = nil
	self.select_toggle_1 = nil
	self.first_open = nil
	self.delay_flush = false
	self.has_load_callback = false
	self.img_change_leader =false
	self.team_info_id = nil
	self.has_change_btn_gray = nil
    self.myteam_left_btn_gameObject_list = nil
	self.mt_zhedie_index_list = nil
	self.myteam_btn_index = nil

	--self:CleanAlertFullTimer()
	--self:CleanAlertNotFullTimer()
end

function TeamView:MyTeamShowIndexCallBack()
    NewTeamWGData.Instance:ClearTeamInfo()
	self:Flush(TabIndex.team_my_team)
	--self:ResetRoleAction()
	for i, v in ipairs(self.mt_team_list) do
		v:ResetRoleAction()
	end
	for i, v in ipairs(self.mt_team_list) do
		v:PlayLastAni()
	end
end

function TeamView:OnFlushMyTeam(key, value)
	self:FlushRewardTimes()

	local is_in_team = SocietyWGData.Instance:GetIsInTeam() == 1
	local is_team_leader = is_in_team and 1 == SocietyWGData.Instance:GetIsTeamLeader() or false
	self.node_list["my_team_layout_blank_tip2"]:CustomSetActive(not is_in_team)
    local team_type, teamfb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	local goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, teamfb_mode)
	if not goal_info then
		return
	end

	self.node_list["target_txt"].text.text = string.format(Language.NewTeam.FBTarget, goal_info.team_type_name) --目标

	local member_list = SocietyWGData.Instance:GetTeamMemberList()
	local cur_menber_count = SocietyWGData.Instance:GetTeamMemberCount()
	self.node_list["btn_mt_apply"]:SetActive(is_in_team and is_team_leader)
	--self.node_list["auto_pass"]:SetActive(is_in_team and is_team_leader)
	self.node_list["btn_mt_word_talk"]:SetActive(is_in_team and (cur_menber_count < (GoalQingYuanFbMaxCount[team_type] or GameEnum.MAX_TEAM_MEMBER_NUMS)) or false)
	self.node_list["btn_mt_quick_team"]:SetActive(is_in_team)
	self:OnFlushBtn()
	self.node_list.add_xiayi_per.text.text = string.format(Language.NewTeam.AddXiaYiPer, Language.NewTeam.XiaYiPerCount[#member_list] or 0)

	local is_qingyuan = team_type == GoalTeamType.QingYuanFb
	self.node_list["mt_suo_invite3"]:SetActive(is_qingyuan)
	XUI.SetGraphicGrey(self.node_list["suo_img"], true)

	local main_role_id = RoleWGData.Instance:GetMainRoleId()
	local share_card_flag = 0
	if 1 == SocietyWGData.Instance:GetIsInTeam() and 0 == SocietyWGData.Instance:GetIsTeamLeader() then
		self.node_list["share_card"]:SetActive(true)
		for k, v in pairs(member_list) do
			if main_role_id == v.role_id then
				share_card_flag = v.share_card_flag
				break
			end
		end
		self.node_list["share_card_select"]:SetActive(share_card_flag == 1)
	else
		self.node_list["share_card"]:SetActive(false)
	end

	self.node_list["auto_pass_select"]:SetActive(SocietyWGData.Instance:GetTeamMustCheck() == 0)

	---------------------------------------------------------------------------
	-- 队伍已满自动弹出提示，无目标不弹出
	if team_type ~= GoalTeamType.YeWaiGuaJi then 
		local full_num = team_type == GoalTeamType.QingYuanFb and 2 or GameEnum.MAX_TEAM_MEMBER_NUMS
		if is_team_leader and cur_menber_count >= full_num and cur_menber_count ~= self.old_member_count then
			-- 策划说改为自动进入副本
			-- self.alert_member_full:SetLableString(string.format(Language.NewTeam.EnterTip_Full, ToColorStr(goal_info.team_type_name, COLOR3B.DEFAULT_NUM)) )
			-- self.alert_member_full:SetOkString(Language.NewTeam.AlertTipOK)
			-- self.alert_member_full:Open()
			-- self:StartAlertFullCancelBtnCountTimer()
			self:EnterFuBen()
		end
	end
	self.old_member_count = cur_menber_count
	---------------------------------------------------------------------------

	self:FlushLevelLimit()
	self:RemindState()
end

-- -- 开始取消倒计时(AlertFull)
-- function TeamView:StartAlertFullCancelBtnCountTimer()
-- 	self:CleanAlertFullTimer()
-- 	self.alert_member_full:SetCancelFunc(function ()
-- 		self:CleanAlertFullTimer()
-- 	end)
-- 	local total = 20
-- 	self.alert_member_full:SetCancelString(string.format(Language.NewTeam.AlertTipCancelCount, total))
-- 	self.timer_alert_full = CountDown.Instance:AddCountDown(total, 1,
-- 		function(elapse_time, total_time)
-- 			if self.alert_member_full then
-- 				self.alert_member_full:SetCancelString(string.format(Language.NewTeam.AlertTipCancelCount, total_time - math.floor(elapse_time)))
-- 			end
-- 		end,
-- 		function()
-- 			if self.alert_member_full then
-- 				self.alert_member_full:Close()
-- 			end
-- 		end)
-- end

-- -- 销毁取消按钮倒计时器(AlertFull)
-- function TeamView:CleanAlertFullTimer()
--     if self.timer_alert_full and CountDown.Instance:HasCountDown(self.timer_alert_full) then
--         CountDown.Instance:RemoveCountDown(self.timer_alert_full)
--         self.timer_alert_full = nil
--     end
-- end

-- 开始取消倒计时(AlertNotFull)
function TeamView:StartAlertNotFullCancelBtnCountTimer()
	self:CleanAlertNotFullTimer()
	self.alert_member_notfull:SetCancelFunc(function ()
		self:CleanAlertNotFullTimer()
	end)
	local total = 20
	self.alert_member_notfull:SetCancelString(string.format(Language.NewTeam.AlertTipCancelCount, total))
	self.timer_alert_not_full = CountDown.Instance:AddCountDown(total, 1,
		function(elapse_time, total_time)
			if self.alert_member_notfull then
				self.alert_member_notfull:SetCancelString(string.format(Language.NewTeam.AlertTipCancelCount, total_time - math.floor(elapse_time)))
			end
		end,
		function()
			if self.alert_member_notfull then
				self.alert_member_notfull:Close()
			end
		end)
end

-- 销毁取消按钮倒计时器(AlertNotFull)
function TeamView:CleanAlertNotFullTimer()
    if self.timer_alert_not_full and CountDown.Instance:HasCountDown(self.timer_alert_not_full) then
        CountDown.Instance:RemoveCountDown(self.timer_alert_not_full)
        self.timer_alert_not_full = nil
    end
end

--用来控制Toggle高亮
function TeamView:ClickMenuSubButton(data, toggle)
	if Scene.Instance:GetSceneType() == SceneType.Kf_PvP_Prepare then
		if data.team_type ~= 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.CanNotChangeGoal)
			return
		end
	end
	if SocietyWGData.Instance:GetIsInTeam() == 1 and SocietyWGData.Instance:GetIsTeamLeader() ~= 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.NotLeaderTip)
		return
	end
	if NewTeamWGData.Instance:GetIsMatching() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.MatchingCanNotDo2)
		return
	end
	--if toggle.isOn == false then
		toggle.isOn = not toggle.isOn
	--end
end

--左侧按钮排序(按照列表的顺序排序，具体的排序规则，交给Data排序)
function TeamView:SortBtnSiblingIndex()
	if IsEmptyTable(self.myteam_left_btn_gameObject_list) then
		return
	end
	for i, v in ipairs(self.myteam_left_btn_gameObject_list) do
		v.transform:SetSiblingIndex(i - 1)
	end
	self.myteam_left_btn_gameObject_list = nil
end

function TeamView:InitBtnSelectShow(data ,is_on)
    if is_on then
        if self.has_specail_load then
            local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
			local specail_list = NewTeamWGData.Instance:GetListByTeamType(data.team_type)
			local state = false
			for i,v in ipairs(specail_list) do
				if v.team_type == now_team_type and v.fb_mode == now_fb_mode then
					self:OnClickTeamGoal(self.team_goal_list[self.mt_zhedie_index_list[v.team_type][i]])
					state = true
				end
			end
			if not state then
				local default_goal_index = NewTeamWGData.Instance:GetDefaultGoalByTeamType(data.team_type)
				self:OnClickTeamGoal(self.team_goal_list[self.mt_zhedie_index_list[data.team_type][default_goal_index]])
			end

		else
			self.need_speail_load = true
		end
	end
end

function TeamView:OnClickAutoPassBtn()
	local must_check = SocietyWGData.Instance:GetTeamMustCheck()
	must_check = must_check == 0 and 1 or 0
	self.node_list["auto_pass_select"]:SetActive(must_check == 0)

	local send_protocol = ProtocolPool.Instance:GetProtocol(CSChangeMustCheck)
	send_protocol.must_check = must_check
	send_protocol:EncodeAndSend()
end

function TeamView:OnClickRule()
	local team_type, team_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
    local now_goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, team_fb_mode)
    local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.NewTeam.EXP_rule_title)
	rule_tip:SetContent(now_goal_info.team_rule or Language.NewTeam.EXP_desc_rule)
end

function TeamView:OnClickInvite()
	NewTeamWGCtrl.Instance:OpenInviteView()
end

function TeamView:OnClickWorldTalk()
	NewTeamWGCtrl.Instance:OpenRecruitView()
	--NewTeamWGCtrl.Instance:ShowTalkView()
end

function TeamView:RemindState()

	local apply_list = SocietyWGData.Instance:GetReqTeamList()
	if # apply_list > 0 then
		self.node_list["Remind"]:SetActive(true)
		else
		self.node_list["Remind"]:SetActive(false)
	end

end

function TeamView:OnClickApplyList()
	NewTeamWGCtrl.Instance:OpenApplyView()
end

function TeamView:OnClickPingTai()
	self:ChangeToIndex(TabIndex.team_pingtai)
end

--点击增加奖励次数
function TeamView:OnClickAddTimes()
	local team_type, team_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	if team_type == GoalTeamType.Exp_DuJieXianZhou then 			-- 渡劫仙舟
        FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.FBCT_WUJINJITAN)
	elseif team_type == GoalTeamType.QingYuanFb then				-- 情缘副本
        NewTeamWGCtrl.Instance:OnClickQingyuanAddTimes()
	elseif team_type == GoalTeamType.YuanGuXianDian then			-- 远古仙殿
		--FuBenPanelWGCtrl.Instance:OpenPetBuy(false, FUBEN_TYPE.HIGH_TEAM_EQUIP)
       -- FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.HIGH_TEAM_EQUIP)
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.CanNoBuyTimes)
	--elseif team_type == GoalTeamType.ZhuShenTa then 				-- 诛神塔 --诸神塔不能购买次数
	elseif team_type == GoalTeamType.Exp_FuMoZhanChuan then 		-- 伏魔战船
        FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.LINGHUNGUANGCHANG)
	elseif team_type == GoalTeamType.ManHuangGuDian then 			-- 蛮荒古殿
		--FuBenPanelWGCtrl.Instance:OpenManHuangGuDianBuy()
        FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.FB_MANHUANG_GUDIAN_FB)
	elseif team_type == GoalTeamType.FbControlBeastsType then			-- 幻兽副本
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.CanNoBuyTimes)
	elseif team_type == GoalTeamType.FbBeautyType then       --女神副本
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.CanNoBuyTimes)
	elseif team_type == GoalTeamType.FbWuHunType then       --武魂副本
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.CanNoBuyTimes)
	elseif team_type == GoalTeamType.FbRuneTowerType then       --符文塔副本
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.CanNoBuyTimes)
	end
end

function TeamView:OnClickQuick()
	self:ChangeToIndex(TabIndex.team_pingtai)
	local main_role = Scene.Instance:GetMainRole()
	local content = string.format(Language.NewTeam.QuitTeamStr, main_role.vo.name, main_role.vo.level)
	ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.TEAM, content, CHAT_CONTENT_TYPE.TEXT, 1, nil, true, false)
	SocietyWGCtrl.Instance:SendExitTeam()
	NewTeamWGData.Instance:ClearTeamInfo()
end

-- 进入副本
function TeamView:EnterFuBen()
	local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	local not_day_limit, open_day_tab = NewTeamWGData.Instance:CheckIsOpenDayLimit(team_type)
	if not not_day_limit then
		local num_str = ""
		for k, v in pairs(open_day_tab) do
			num_str = num_str .. NumberToChinaNumber(k)
		end
		
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.NewTeam.Tips3, num_str))
		return
	end
	
	if not self:CheckLevelEnterFB() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.Tips1)
		return
	end

	local cur_enter_count, total_count = NewTeamWGData.Instance:GetTimesByTeamType(team_type)
	local remain_count = total_count - cur_enter_count

    if 0 == SocietyWGData.Instance:GetIsInTeam() then
        local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
		NewTeamWGCtrl.Instance:SendCreateTeam(team_type, fb_mode, min_level, max_level)
		if remain_count == 0 then 
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.HaceNoTimes)
			self:OnClickAddTimes()
			return
		end
    end

	local is_match = NewTeamWGData.Instance:GetIsMatching()
	local operate = is_match and 1 or 0
	if not is_match then
		if remain_count == 0 and 1 == SocietyWGData.Instance:GetIsTeamLeader()  then 
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.HaceNoTimes)
			self:OnClickAddTimes()
			return
		end
		NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, team_type, fb_mode)
	else
		if not self.mt_cancel_match_alert then
			self.mt_cancel_match_alert = Alert.New()
		end
		self.mt_cancel_match_alert:SetLableString(Language.FuBenPanel.AlertCancelMatch)
		self.mt_cancel_match_alert:SetOkFunc(function()
			NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, team_type, fb_mode)
		end)
		self.mt_cancel_match_alert:Open()
	end
end

function TeamView:OnClickAutoMatch()
	-- 队伍未满
	local cur_menber_count = SocietyWGData.Instance:GetTeamMemberCount()
	local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	local full_num = team_type == GoalTeamType.QingYuanFb and 2 or GameEnum.MAX_TEAM_MEMBER_NUMS
	if cur_menber_count < full_num then
		-- 策划说不要这个提示弹窗，暂时注释
		local goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, fb_mode)
		self.alert_member_notfull:SetLableString(string.format(Language.NewTeam.EnterTip_NotFull, ToColorStr(goal_info.team_type_name,COLOR3B.DEFAULT_NUM)) )
		self.alert_member_notfull:SetOkString(Language.NewTeam.AlertTipOK)
		self.alert_member_notfull:Open()
		self:StartAlertNotFullCancelBtnCountTimer()
		-- 策划说改回去
		-- SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.EnterTip_NotFull2)
		return
	end
	self:EnterFuBen()
end

function TeamView:CheckLevelEnterFB()
	local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	--检测最低等级限制
	local team_target_cfg = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, fb_mode)
	if team_target_cfg and team_target_cfg.role_min_level then
		local role_level = GameVoManager.Instance:GetMainRoleVo().level
		if role_level < team_target_cfg.role_min_level then
			return false
		end
	end
	return true
end

function TeamView:OnClickCrateTeam()
	local team_type, team_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	if 1 == SocietyWGData.Instance:GetIsInTeam() then
		--跳转目标玩法入口界面
		local view_name, tab_index = NewTeamWGData.Instance:GetJumpViewNameAndIndex(team_type, team_fb_mode)
		FunOpen.Instance:OpenViewByName(view_name, tab_index)
	else
		local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
		NewTeamWGCtrl.Instance:SendCreateTeam(team_type, team_fb_mode, min_level, max_level)
	end
end

function TeamView:OnClickAlertWindowOK()
	local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	local now_goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, fb_mode)
	local flag_data = FuBenWGData.Instance:SetCombineMark()
	local is_combine_flag = bit:d2b(flag_data)
	if now_goal_info.fb_type == FUBEN_TYPE.FBCT_WUJINJITAN or now_goal_info.fb_type == FUBEN_TYPE.LINGHUNGUANGCHANG then
		local other_cfg = FuBenPanelWGCtrl.Instance.view:GetOtherCfg()
		local pass_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.pass_item_id)
		local is_linghunguangchang_fb = WuJinJiTanWGData.Instance:IsLingHunGuangChang() and FB_COMBINE_TYPE.LINGHUN_GUANGCHANG or FB_COMBINE_TYPE.WUJINJITAN
		local yet_enter_time = FuBenPanelWGCtrl.Instance.view:GetFBEnterTimes()
		local yet_buy_time = FuBenPanelWGCtrl.Instance.view:GetFBBuyTimes()
		local user_ticket_time = FuBenPanelWGCtrl.Instance.view:GetFBAddTimes()
		local enter_dj_times = other_cfg.everyday_times + yet_buy_time + user_ticket_time - yet_enter_time
		local bind_gold_num = RoleWGData.Instance.role_info.bind_gold
		local cost_text = Language.Common.GoldText
		if  is_combine_flag[32 - is_linghunguangchang_fb] == 1 then
			FuBenWGCtrl.Instance:SendFBUseCombine(1,now_goal_info.fb_type)
			if pass_num < enter_dj_times  then
				local need_buy_count = enter_dj_times - pass_num
				if need_buy_count <= 1 then
					if bind_gold_num >= other_cfg.buy_pass_item_gold then
						cost_text = Language.Common.BindGoldText
					end 
					self.alert_window3:SetLableString(string.format(Language.FuBenPanel.TicketsInsufficient, cost_text, other_cfg.buy_pass_item_gold))
				else
					if bind_gold_num >= other_cfg.buy_pass_item_gold * need_buy_count then
						cost_text = Language.Common.BindGoldText
					end 
					self.alert_window3:SetLableString(string.format(Language.FuBenPanel.TicketsInsufficient1, cost_text, other_cfg.buy_pass_item_gold,need_buy_count))
				end
				self.alert_window3:SetOkFunc(function ()
					if RoleWGData.Instance:GetIsEnoughBindGold(other_cfg.buy_pass_item_gold * need_buy_count) then
						ShopWGCtrl.Instance:SendShopBuy(other_cfg.pass_item_id, need_buy_count, 0, 1)
					else
						ShopWGCtrl.Instance:SendShopBuy(other_cfg.pass_item_id, need_buy_count, 0, 0)
					end
					--FuBenWGCtrl.Instance:SendEnterFB(now_goal_info.fb_type, 1, fb_mode)
					self:EnterFubenLiXian(now_goal_info.fb_type, 1, fb_mode)
				end)
				self.alert_window3:Open()
				return
			else
				--FuBenWGCtrl.Instance:SendEnterFB(now_goal_info.fb_type, 1, fb_mode)
				self:EnterFubenLiXian(now_goal_info.fb_type, 1, fb_mode)
			end
		else
			if 0 == pass_num then
				local flag_data = FuBenWGData.Instance:SetCombineMark()
				local is_combine_flag = bit:d2b(flag_data)
				if bind_gold_num >= other_cfg.buy_pass_item_gold then
					cost_text = Language.Common.BindGoldText
				end 
				self.alert_window3:SetLableString(string.format(Language.FuBenPanel.TicketsInsufficient, cost_text, other_cfg.buy_pass_item_gold))
				self.alert_window3:SetOkFunc(function ()
					if RoleWGData.Instance:GetIsEnoughBindGold(other_cfg.buy_pass_item_gold) then
						ShopWGCtrl.Instance:SendShopBuy(other_cfg.pass_item_id, 1, 0, 1)
					else
						ShopWGCtrl.Instance:SendShopBuy(other_cfg.pass_item_id, 1, 0, 0)
					end
				end)
				self.alert_window3:Open()
				return
			else
				self:EnterFubenLiXian(now_goal_info.fb_type, 1, fb_mode)
			end
		end
		return
	elseif now_goal_info.fb_type == FUBEN_TYPE.HIGH_TEAM_EQUIP then
		if is_combine_flag[32 - FB_COMBINE_TYPE.YUANGU] == 1 then
			FuBenWGCtrl.Instance:SendFBUseCombine(1,now_goal_info.fb_type)
		end
		self:EnterFubenLiXian(now_goal_info.fb_type, 1, fb_mode)
	elseif now_goal_info.fb_type == FUBEN_TYPE.ZHUSHENTA_FB then
		if is_combine_flag[32 - FB_COMBINE_TYPE.KILL_GOD_TOWER] == 1 then
			FuBenWGCtrl.Instance:SendFBUseCombine(1,now_goal_info.fb_type)
		end
		self:EnterFubenLiXian(now_goal_info.fb_type, 1, fb_mode)
	else
		local is_can_entr = FuBenPanelWGCtrl.Instance:IsCheckCanEnterFb()
	    if not is_can_entr then
	    	SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CanNotEnterFb)
	    	return
	    end
	    self:EnterFubenLiXian(now_goal_info.fb_type, 1, fb_mode)
	    --FuBenWGCtrl.Instance:SendEnterFB(now_goal_info.fb_type, 1, fb_mode)
	end
	--FuBenWGCtrl.Instance:SendEnterFB(now_goal_info.fb_type, 1, fb_mode)    -- 副本类型, 是否组队, 层数
end

-- Copy自MainUIView
function TeamView:OnClickFollowBtn()
    local member_list = SocietyWGData.Instance:GetTeamMemberList()
    local role_id
    local plat_type
    for k, v in pairs(member_list) do
        if v.is_leader == 1 then
            role_id = v.role_id
            plat_type = v.plat_type
            break
        end
    end

    if not role_id then
        return
    end

    if role_id == RoleWGData.Instance:InCrossGetOriginUid() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.NoFollowMe)
    else
        local uuid = MsgAdapter.ReadUUIDByValue(role_id, plat_type)
        local scene_logic = Scene.Instance:GetSceneLogic()
        if scene_logic ~= nil then
            scene_logic:SetTrackRoleData(OBJ_FOLLOW_TYPE.TEAM, uuid)
        end

        Scene.SendGetRoleMoveInfo(uuid, OBJ_FOLLOW_TYPE.TEAM, CLIENT_MOVE_REQ_PARAM.GUAJI_RECOVERY)
    end
end

function TeamView:EnterFubenLiXian(fuben_type,is_team,layer)
	local menmber_info = SocietyWGData.Instance:GetTeamMemberList()
	local is_have_lixian = false
	for k,v in pairs(menmber_info) do
		if v.is_online ~= 1 then
			is_have_lixian = true
			break
		end
	end
	if is_have_lixian then
		self.alert_window3:SetLableString(Language.FuBenPanel.LiXianMember)
		self.alert_window3:SetOkFunc(function ()
			for k,v in pairs(menmber_info) do
				if v.is_online ~= 1 then
					SocietyWGCtrl.Instance:SendKickOutOfTeam(v.orgin_role_id)
				end
			end
			self:SetIsEnterFuBen()
			FuBenWGCtrl.Instance:SendEnterFB(fuben_type,is_team,layer)   -- 副本类型, 是否组队, 无意义
			FuBenPanelWGData.Instance:SetEnterType(is_team)
		end)
		self.alert_window3:Open()
	else
		self:SetIsEnterFuBen()
		FuBenWGCtrl.Instance:SendEnterFB(fuben_type,is_team,layer)   -- 副本类型, 是否组队, 无意义
		FuBenPanelWGData.Instance:SetEnterType(is_team)
	end
end

function TeamView:SetIsEnterFuBen()
	if FuBenPanelWGCtrl.Instance.view:IsOpen() then
		FuBenPanelWGCtrl.Instance.view.is_enter_fuben = true
	end
end

function TeamView:OnClickChangeGoal()
	if NewTeamWGData.Instance:GetIsMatching() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.AlertTipOK)
		return
	end
	if 1 == SocietyWGData.Instance:GetIsInTeam() and 0 == SocietyWGData.Instance:GetIsTeamLeader() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.NotLeaderTip)
		return
	end
	NewTeamWGCtrl.Instance:OpenChangeGoalView(false)
end

function TeamView:SetIsMatching(flag)
	self.is_matching = flag
end

function TeamView:TeamFlush()
	self:MyTeamTeamCellFlush()
	self:TeamInfoCellFlush()
end

function TeamView:MyTeamTeamCellFlush()
    local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
    local is_zhedie, zhedie_btn_count = NewTeamWGData.Instance:GetIsZheDieBtn(team_type)
	if self.mt_team_list == nil or self.mt_team_list[3] == nil then
        self.delay_flush = true
        if is_zhedie then
            self.need_speail_load = true
        end
		return
	end

	if self.has_load_callback == false then
        self.delay_flush = true
        if is_zhedie then
            self.need_speail_load = true
        end
		return
	end

	local change_index , member_list = NewTeamWGData.Instance:GetTeamMemberList()
	self.member_list = member_list
	local member_state,btn_invite_state,state
	local is_leader = (1 == SocietyWGData.Instance:GetIsTeamLeader())
	for k,v in ipairs(change_index) do
		if v and self.mt_team_list[k] then
			self.mt_team_list[k]:SetData(member_list[v])
			member_state = member_list[v] and member_list[v].name or member_list[v]  --人名,"",nil
			btn_invite_state = member_state == "" or not member_state
			state = not btn_invite_state and k ~= 1 and member_list[v].role_id ~= RoleWGData.Instance:InCrossGetOriginUid() and is_leader
			self.node_list["btn_mt_invite" .. k]:SetActive(btn_invite_state)
			self.mt_team_list[k]:SetCommonActive(member_list[v] ~= nil)
		end
	end

	local team_type, teamfb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	if team_type == GoalTeamType.QingYuanFb then
		self.node_list["btn_mt_invite3"]:SetActive(false)
		self.node_list["btn_mt_invite4"]:SetActive(false)
		self.node_list["btn_mt_invite5"]:SetActive(false)
	end
end

function TeamView:TeamInfoCellFlush()
	if IsEmptyTable(self.mt_team_info_list) then
		return
	end

	local change_index, member_list = NewTeamWGData.Instance:GetTeamMemberList()
	for k,v in ipairs(change_index) do
		if v then
			if self.mt_team_info_list[k] then
				self.mt_team_info_list[k]:SetData(member_list[v])
				self.mt_team_info_list[k]:SetCommonActive(member_list[v] ~= nil)
			end
		end
	end
end

function TeamView:OnFlushBtn()
	local team_type, teamfb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	local goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, teamfb_mode)
	local team_state = 0 == SocietyWGData.Instance:GetIsInTeam()

	if NewTeamWGData.Instance:GetIsMatching() then
		self.node_list["text_match"].text.text = (Language.NewTeam.IsMatching)
	else
		self.node_list["text_match"].text.text = (Language.NewTeam.AutoMatch)
	end

	if GoalCanButTimesType[team_type] then
		self.node_list["mt_btn_add_times"]:SetActive(true)
	else
		self.node_list["mt_btn_add_times"]:SetActive(false)
	end

	if team_state then --没队伍
		self.node_list["btn_mt_match"]:SetActive(goal_info.fb_type ~= 0)
		self.node_list["btn_mt_follow"]:SetActive(false)
	else --有队伍
		self.node_list["btn_mt_match"]:SetActive(SocietyWGData.Instance:GetIsTeamLeader() == 1 and goal_info.fb_type ~= 0)
		self.node_list.mt_level_limit_btn:SetActive(SocietyWGData.Instance:GetIsTeamLeader() == 1)
		self.node_list["btn_mt_follow"]:SetActive(SocietyWGData.Instance:GetIsTeamLeader() ~= 1)
	end

	if NotShowBtnEnter[team_type] then
		self.node_list["btn_mt_match"]:SetActive(false)
	end


	
	self.node_list["text_quick_team"].text.text = team_state and Language.NewTeam.QuickTeam or Language.NewTeam.LeaveTeam

	self.node_list["btn_mt_create_team"]:SetActive(team_state)
	self.node_list["text_create_team"].text.text = (Language.NewTeam.CreateTeamBtnText)
	if CountDownManager.Instance:HasCountDown("team_word_talk") then
		XUI.SetGraphicGrey(self.node_list.btn_word_talk,true)
	end
end

function TeamView:FlushRewardTimes(goal_info)
	local team_type, teamfb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	if goal_info then
		team_type = goal_info.team_type
		teamfb_mode = goal_info.fb_mode
	end
	--全部队伍 和 野外挂机 不需要显示
	if team_type == 0 or team_type == -1 then
		self.node_list["mt_right_top"]:SetActive(false)
	else
		self.node_list["mt_right_top"]:SetActive(true)
	end
	local cur_enter_count, total_count = NewTeamWGData.Instance:GetTimesByTeamType(team_type)
	if total_count == 0 then
		self.node_list["mt_right_top"]:SetActive(false)
	end

	local remain_count = total_count - cur_enter_count
	local color = remain_count > 0 and COLOR3B.GREEN or COLOR3B.PINK
	self.node_list.mt_reward_times_value.text.text = string.format(Language.NewTeam.RemainRewardTimes, color, remain_count, total_count)
	self.node_list.mt_xiezhu:SetActive(false)--(GoalHasXieZhuType[team_type] and remain_count <= 0) or false)

	local xiezhu_times, total_xiezhu_times = NewTeamWGData.Instance:GetXieZhuTimesByTeamType(team_type)
	local color = total_xiezhu_times - xiezhu_times > 0 and COLOR3B.GREEN or COLOR3B.PINK
	self.node_list.mt_xiezhu_times_value.text.text = string.format(Language.NewTeam.RemainRewardTimes, color, total_xiezhu_times - xiezhu_times, total_xiezhu_times)
end

function TeamView:OpenCustomMenu(buff, id ,pos)
	self.custom_menu_state = not self.custom_menu_state
	self.team_info_id = self.custom_menu_state and id or nil
	self.node_list.mt_choose_team_view:SetActive(self.custom_menu_state)
	for k,v in pairs(buff) do
		self.node_list["btn_mt_choose_" .. k]:SetActive(v)
	end
	if self.custom_menu_state then
		self.node_list.layout_choose_team.rect.anchoredPosition = pos
	end
end

function TeamView:OnClickChoose(i)
	if self.team_info_id == nil or i == nil then return end
	if i == 1 then
		BrowseWGCtrl.Instance:OpenWithUid(self.team_info_id)
	elseif i == 2 then
		SocietyWGCtrl.Instance:IAddFriend(self.team_info_id)
	elseif i == 3 then
		SocietyWGCtrl.Instance:SendChangeTeamLeader(self.team_info_id)
	elseif i == 4 then
		SocietyWGCtrl.Instance:SendKickOutOfTeam(self.team_info_id)
	elseif i == 5 then
		SocietyWGCtrl.Instance:SendReqMemChangeTeamLeader()
	end
	self:OnClickBlock()
end

function TeamView:OnClickBlock()
	self.custom_menu_state = false
	self.team_info_id = nil
	self.node_list.mt_choose_team_view:SetActive(false)
end

--is_click：是否是手动点击, 匹配中手动点击需要弹错误码, 匹配中自动调用， 只需要高亮显示按钮
function TeamView:OnClickTeamGoal(cell, is_click)
	if cell == nil or cell.data == nil then
		return
	end

	local btn_index = cell:GetBtnIndex()
    if is_click and self.myteam_btn_index ~= nil and self.myteam_btn_index == btn_index then
        return
    end

    self.myteam_btn_index = btn_index

	if NewTeamWGData.Instance:GetIsMatching() then
		if is_click then --只有是手动点击才弹错误码
			SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.MatchingCanNotDo2)
		else -- 匹配中，刚进来的时候也要高亮
			self:FlushRewardTimes(cell.data)
		end
		return
	else
		--没在匹配中， 并且不是队长, 并且不是手动点击， （用来组队的情况下， 进来我的队伍界面，只高亮，不弹错误码）
		if SocietyWGData.Instance:GetIsInTeam() == 1 and SocietyWGData.Instance:GetIsTeamLeader() ~= 1 and not is_click then
			self:FlushRewardTimes(cell.data)
			return
		end
	end

	if Scene.Instance:GetSceneType() == SceneType.Kf_PvP_Prepare then
		if is_click and cell.data.team_type ~= 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.CanNotChangeGoal)
			return
		end
	end

	if SocietyWGData and SocietyWGData.Instance and 1 == SocietyWGData.Instance:GetIsInTeam() and 0 == SocietyWGData.Instance:GetIsTeamLeader() then
		if is_click then --只有是手动点击才弹错误码
			SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.NotLeaderTip)
		end
		return
	end
	local goal_cfg = cell.data
	if goal_cfg.team_type == GoalTeamType.QingYuanFb then
		local team_member_count = SocietyWGData.Instance:GetTeamMemberCount()
		local team_member_list = SocietyWGData.Instance:GetTeamMemberList()
		if team_member_count > QingYuanLimitCount then
			local tips_str = string.format(Language.NewTeam.OverflowMaxTeamMemberCount, goal_cfg.team_type_name or "")
			SysMsgWGCtrl.Instance:ErrorRemind(tips_str)
			return
		end
		for i,v in ipairs(team_member_list) do
			if v.level < goal_cfg.role_min_level then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.TeamMemberLevelLower)
				return
			end
		end
	end
	--组队的最大等级改为等级排行的第一名
    local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()	--获取服务器最高世界等级
    local top_lv = goal_cfg.role_max_level or top_user_level
	NewTeamWGData.Instance:SetTeamTypeAndMode(goal_cfg.team_type, goal_cfg.fb_mode)
	NewTeamWGData.Instance:SetTeamLimitLevel(goal_cfg.role_min_level, top_lv)
	NewTeamWGData.Instance:GetSetCurSelectTarget(goal_cfg)
	if 1 == SocietyWGData.Instance:GetIsInTeam() then
		NewTeamWGCtrl.Instance:SendChangeTeamLimit(goal_cfg.team_type, goal_cfg.fb_mode, goal_cfg.role_min_level, top_lv)
	end

	self:FlushRewardTimes()
	self:FlushLevelLimit()
	self:OnFlushBtn()
end

-- 刷新等级限制文本
function TeamView:FlushLevelLimit()
    local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
	local str = string.format(Language.NewTeam.PTLevelLimitTop, min_level, max_level)
	EmojiTextUtil.ParseRichText(self.node_list["my_team_level_limit_text"].emoji_text, str, 20, COLOR3B.DEFAULT_NUM)
end

function TeamView:OnMyTeamDayCountChange(day_counter_id)
	if self:IsLoadedIndex(TabIndex.team_my_team) then
		self:FlushRewardTimes()
	end
end

function TeamView:OnClickToClone()
	self:ChangeToIndex(TabIndex.team_clone)
end

function TeamView:OnClickShareCard()
	local member_list = SocietyWGData.Instance:GetTeamMemberList()
	local main_role_id = RoleWGData.Instance:GetMainRoleId()
	local share_card_flag = 0
	if 1 == SocietyWGData.Instance:GetIsInTeam() and 0 == SocietyWGData.Instance:GetIsTeamLeader() then
		for k, v in pairs(member_list) do
			if main_role_id == v.role_id then
				share_card_flag = v.share_card_flag
				break
			end
		end

		local agree = (share_card_flag == 1) and 0 or 1
		SocietyWGCtrl.Instance:SendReqCSTeamMemberShareItem(agree)
	end
end
------------------itemRender-----------------
TeamListItem = TeamListItem or BaseClass(BaseRender)

function TeamListItem:__init()
	XUI.AddClickEventListener(self.node_list.RoleDisplay, BindTool.Bind(self.OnClickChoose, self))
	self.role_model = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["RoleDisplay"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.PS_TS,
		can_drag = false,
	}
	
	self.role_model:SetRenderTexUI3DModel(display_data)
	-- self.role_model:SetUI3DModel(self.node_list["RoleDisplay"].transform, self.node_list.ModelEvent.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
	self.cache = nil
	self.state = nil
	self.is_self = false
	self.cur_model = nil
	self.count = 3
	self.old_role_id = -1
end

function TeamListItem:__delete()
	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	self.cache = nil
	self.state = nil
	self.is_self = nil
	self.old_role_id = nil
end

function TeamListItem:ReleaseTeam()
	self.cache = nil
	self.state = nil
	self.is_self = nil
end

function TeamListItem:OnClickChoose()
	if not self.data then
		return
	end

	if (self.data.orgin_role_id and self.data.orgin_role_id == RoleWGData.Instance:InCrossGetOriginUid()) or self.data.is_match then
		return
	end

	local buff = self:OnClickView()
	NewTeamWGCtrl.Instance:OpenCustomMenu(buff, self.data.orgin_role_id, CustomPos[self.index])
end

function TeamListItem:OnClickView()
	if self.data == nil then return end
	local items = {true,true,true,true,true}
	if 0 == SocietyWGData.Instance:GetIsTeamLeader() then--去掉 移交队长
		items[3] = false
		items[4] = false
	else
		items[5] = false--去掉 申请队长
	end
	if nil ~= SocietyWGData.Instance:FindFriend(self.data.role_id) then--去掉 添加好友
		items[2] = false
	end
	if 1 ~= self.data.is_leader then
		items[5] = false
	end
	return items
end

function TeamListItem:OnFlush()
	if not self.data then 
		self.role_model:ClearModel()
		self:ReleaseTeam()
		self.old_role_id = -1  
		return 
	end

	if self.state ~= self.data.is_match or not self.data.is_match then
		self.state  = self.data.is_match
		self.node_list["lbl_match_desc"]:SetActive(false)
		 self.node_list["lbl_role_name"]:SetActive(not self.state)
		self.node_list["level_limit_text"]:SetActive(not self.state)
		self.node_list["lbl_role_name_bg"]:SetActive(not self.state)
	end
	--隐藏VIP等级信息
	-- self:SetVipNum(self.data.vip_level, SettingWGData.Instance:IsHideRoleVipLv(self.data.shield_vip_flag))
	self.node_list["img_leader"]:SetActive(1 == self.data.is_leader)

	-- local is_me = RoleWGData.Instance:InCrossGetOriginUid() == self.data.role_id
 --    if self.data.is_in_different_country and (0 ~= self.data.is_online and 2 ~= self.data.is_online) then --当前在不同国
 --        self.node_list["lbl_role_name"].text.text = is_me and self.data.name or self.data.name .. " " .. Language.NewTeam.MemberState_Farway
	-- elseif self.data.scene_id ~= Scene.Instance:GetSceneId() and self.data.role_id ~= RoleWGData.Instance:InCrossGetOriginUid() and (1 == self.data.is_online or 1 == self.data.is_hang) and self.data.scene_id ~= 0 then
	-- 	self.node_list["lbl_role_name"].text.text = is_me and self.data.name or self.data.name .. " " .. Language.NewTeam.MemberState_Farway
	-- elseif (0 == self.data.is_online and 0 == self.data.is_hang) or 2 == self.data.is_online then
	-- 	self.node_list["lbl_role_name"].text.text = is_me and self.data.name or self.data.name .. " " .. Language.NewTeam.MemberState_Offline
	-- elseif 3 == self.data.is_online then
	-- 	self.node_list["lbl_role_name"].text.text = is_me and self.data.name or self.data.name .. " " .. Language.NewTeam.MemberState_Cross
	-- else
	-- 	self.node_list["lbl_role_name"].text.text = is_me and self.data.name or self.data.name .. " " .. Language.NewTeam.MemberState_Near
	-- end

	-- self.node_list["role_prof"].image:LoadSprite(ResPath.GetCommonImages(RoleWGData.GetProfIcon(self.data.prof, self.data.sex)))

	--人物等级
	-- local is_vis,level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
	-- self.node_list["dianfeng_img"]:SetActive(is_vis)
	-- self.node_list["level_limit_text"].text.text = string.format(Language.Common.LevelNormal,level)

	-- if self.data.role_id == RoleWGData.Instance:InCrossGetOriginUid() then
	-- 	self.node_list.bg_fightpower:SetActive(false)
	-- 	self.node_list.fightpower_num.text.text = RoleWGData.Instance.role_vo.capability
	-- else
	-- 	self.node_list.bg_fightpower:SetActive(false)--self.data.capability ~= nil)
	-- 	self.node_list.fightpower_num.text.text = self.data.capability or 0
	-- end
	self:SetModel()
	self:QiPaoState()
end

function TeamListItem:PlayLastAni()
	self.role_model:PlayLastAction()
end

function TeamListItem:QiPaoState()
	if not self.data or not next(self.data) then return end
    local is_me = self.data.role_id == RoleWGData.Instance:InCrossGetOriginUid()
    if self.data.is_in_different_country and (0 ~= self.data.is_online and 2 ~= self.data.is_online) then --当前在不同国
        local local_server_str = ToLLStr(self.data.goto_plat_type, self.data.goto_server_id) --当前所在国
        local cur_server_str = RoleWGData.Instance:GetOriginalUSIDStr() --本国
        if not is_me and local_server_str ~= cur_server_str then
            self.node_list.role_scene_bg:SetActive(true)
            self.node_list.role_scene_des.text.text = Language.NewTeam.Cross
        else
            self.node_list.role_scene_bg:SetActive(false)
        end
    --如果队友所在场景和自己不一样 and 队友ID不等于自己的ID and（队友在线或者队友在离线挂机）
    elseif self.data.scene_id ~= Scene.Instance:GetSceneId() and self.data.role_id ~= RoleWGData.Instance:InCrossGetOriginUid() and (1 == self.data.is_online or 1 == self.data.is_hang) and self.data.scene_id ~= 0 then
        local scene_cfg = ConfigManager.Instance:GetSceneConfig(self.data.scene_id)
        if scene_cfg.scene_type ~= SceneType.Common then
            self.node_list.role_scene_bg:SetActive(true)
            local map_name = Config_scenelist[self.data.scene_id] and Config_scenelist[self.data.scene_id].name or ""
            self.node_list.role_scene_des.text.text = string.format(Language.NewTeam.InMap,map_name)
        else
            self.node_list.role_scene_bg:SetActive(false)
        end
    elseif 0 == self.data.is_online and 0 == self.data.is_hang or (2 == self.data.is_online) then
        self.node_list.role_scene_bg:SetActive(false)
    elseif 3 == self.data.is_online then
        local scene_cfg = ConfigManager.Instance:GetSceneConfig(self.data.scene_id)
        if scene_cfg.scene_type ~= SceneType.Common then
            self.node_list.role_scene_bg:SetActive(true)
            local map_name = Config_scenelist[self.data.scene_id] and Config_scenelist[self.data.scene_id].name or ""
            self.node_list.role_scene_des.text.text = string.format(Language.NewTeam.InMap,map_name)
        else
            self.node_list.role_scene_bg:SetActive(false)
        end
    else
        self.node_list.role_scene_bg:SetActive(false)
    end
end

function TeamListItem:SetMatchPoint()
	self.node_list["lbl_match_desc"].text.text = Language.NewTeam["Matching" .. self.count]
	self.count = self.count + 1 < 4 and self.count + 1 or 1
end

--全模型加载，记得剔除不需要的( SetModelResInfo() )
function TeamListItem:SetModel()
	if self.data.role_id == 0 or not self.data.orgin_role_id or self.data.orgin_role_id == 0 then
		self.role_model:ClearModel()
		self:ReleaseTeam()
		self.old_role_id = -1  
		return
	end

	local vo = GameVoManager.Instance:GetMainRoleVo()
	local server_id = self.data.server_id
	local self_server_id = vo.merge_server_id
	local special_status_table = {ignore_wing = true, ignore_fazhen = true, ignore_jianzhen = true, ignore_halo = true}

	if self.data.role_id == RoleWGData.Instance:InCrossGetOriginUid() then
		if vo.role_id ~= self.old_role_id then
			self.role_model:SetModelResInfo(vo, special_status_table)
		end

		self.old_role_id = vo.role_id
	else
		local function callback( protocol )
			if self.data and self.data.role_id ~= RoleWGData.Instance:InCrossGetOriginUid() then
				NewTeamWGData.Instance:SetRoleInfo(protocol)
				local role_appearance = NewTeamWGData.Instance:GetRoleInfo()
				if self.role_model then
					-- if role_appearance.role_id ~= self.old_role_id then
					self.role_model:SetModelResInfo(role_appearance, special_status_table)
					-- end

					self.old_role_id = role_appearance.role_id
				end
			end
		end
		--跨服玩家组队的
		if server_id ~= self_server_id then
			BrowseWGCtrl.Instance:SendCrossQueryRoleInfo(server_id, self.data.role_id, callback)
		else
			BrowseWGCtrl.Instance:BrowRoelInfo(self.data.orgin_role_id, callback)
		end
	end
	self:ResetRoleAction()
	self.cache = self.data.role_id
end

function TeamListItem:ResetRoleAction()
	if self.role_model then
		self.role_model:PlayLastAction()
	end
end

function TeamListItem:OnClickOperate()
	SocietyWGCtrl.Instance:SendKickOutOfTeam(self.data.role_id)
end

function TeamListItem:SetVipNum(num, is_hide_vip)
	if num == 0 or num == nil then
		self.node_list.vip_image:SetActive(false)
	else
		--local vip_res_name = is_hide_vip and "vip_hide" or "vip"..num
		local bundle, asset = ResPath.GetCommonIcon("a2_vip"..num)
		self.node_list.vip_image.image:LoadSpriteAsync(bundle, asset, function ()  		
			self.node_list.vip_image.image:SetNativeSize()
		end)
	end
end

function TeamListItem:OnClickMenuButton(index, sender, param)
	if 0 == self.data.is_online then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NotOnline)
		return
	end
	local menu_text = param[index]
	if menu_text == Language.NewTeam.OpenMenu[1] then
		BrowseWGCtrl.Instance:OpenWithUid(self.data.role_id)
	elseif menu_text == Language.NewTeam.OpenMenu[2] then
		SocietyWGCtrl.Instance:IAddFriend(self.data.role_id)
	elseif menu_text == Language.NewTeam.OpenMenu[3] then
		SocietyWGCtrl.Instance:SendChangeTeamLeader(self.data.role_id)
	end
end

function TeamListItem:SetCommonActive(flag)
	self.node_list["common"]:SetActive(flag)
end



------------------itemRender-----------------
TeamMemberInfoListItem = TeamMemberInfoListItem or BaseClass(BaseRender)

function TeamMemberInfoListItem:__init()
end

function TeamMemberInfoListItem:__delete()
end

function TeamMemberInfoListItem:OnFlush()
	if not self.data then return end

	self.node_list["img_leader"]:SetActive(1 == self.data.is_leader)
	self.node_list["img_fenshen"]:SetActive(1 == self.data.is_fenshen)
	if self.state ~= self.data.is_match or not self.data.is_match then
		self.state  = self.data.is_match
		-- self.node_list["lbl_match_desc"]:SetActive(false)
		 self.node_list["lbl_role_name"]:SetActive(not self.state)
		self.node_list["level_limit_text"]:SetActive(not self.state)
		-- self.node_list["lbl_role_name_bg"]:SetActive(not self.state)
	end

	self.node_list["lbl_role_name"].text.text = self.data.name
	local role_id = self.data.orgin_role_id or self.data.role_id
	local is_me = RoleWGData.Instance:InCrossGetOriginUid() == role_id
	if not is_me then
		self.node_list["lbl_role_state"]:SetActive(true)
		if self.data.is_in_different_country and (0 ~= self.data.is_online and 2 ~= self.data.is_online) then --当前在不同国
	        self.node_list["lbl_role_state"].text.text = Language.NewTeam.MemberState_Farway
		elseif self.data.scene_id ~= Scene.Instance:GetSceneId() and self.data.role_id ~= RoleWGData.Instance:InCrossGetOriginUid() and (1 == self.data.is_online or 1 == self.data.is_hang) and self.data.scene_id ~= 0 then
			self.node_list["lbl_role_state"].text.text = Language.NewTeam.MemberState_Farway
		elseif (0 == self.data.is_online and 0 == self.data.is_hang) or 2 == self.data.is_online then
			self.node_list["lbl_role_state"].text.text = Language.NewTeam.MemberState_Offline
		elseif 3 == self.data.is_online then
			self.node_list["lbl_role_state"].text.text = Language.NewTeam.MemberState_Cross
		else
			self.node_list["lbl_role_state"].text.text = Language.NewTeam.MemberState_Near
		end
	else
		self.node_list["lbl_role_state"]:SetActive(false)
	end

	--不够第三个人也会调用
	if self.data.prof and self.data.sex then
		local bundle, asset = ResPath.GetCommonImages(RoleWGData.GetProfIcon(self.data.prof, self.data.sex))
		self.node_list["role_prof"].image:LoadSprite(bundle, asset, function()
			self.node_list["role_prof"].image:SetNativeSize()
		end)
	end
	--人物等级
	local is_vis,level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
	self.node_list["level_limit_text"].text.text = tostring(level)-- string.format(Language.Common.LevelNormal,level)
	self.node_list["dianfeng_img"]:SetActive(is_vis)
end

function TeamMemberInfoListItem:SetCommonActive(flag)
	self.node_list["common"]:SetActive(flag)
end