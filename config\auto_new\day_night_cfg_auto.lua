-- C-场景昼夜.xls

return {
scene_task={
{moment_index=0,},
{task_start=60,task_end=110,},
{task_start=120,task_end=150,moment_index=1,},
{task_start=160,task_end=190,},
{task_start=200,task_end=200,},
{scene_id=1003,task_start=561,task_end=810,moment_index=1,},
{scene_id=1003,task_start=810,task_end=900,},
{task_start=900,task_end=950,},
{task_start=950,task_end=990,}
},

scene_task_meta_table_map={
[5]=3,	-- depth:1
[9]=7,	-- depth:1
[8]=6,	-- depth:1
},
scen_regular={
[101]={scene_id=101,moment_index=3,},
[102]={scene_id=102,},
[103]={scene_id=103,}
},

scen_regular_meta_table_map={
},
time_day_night={
{},
{start_time=5,end_time=10,},
{start_time=10,end_time=15,},
{start_time=15,end_time=20,},
{start_time=20,end_time=25,},
{start_time=25,end_time=30,},
{start_time=30,end_time=35,},
{start_time=35,end_time=40,},
{start_time=40,end_time=45,},
{start_time=45,end_time=50,},
{start_time=50,end_time=55,},
{start_time=55,end_time=100,},
{start_time=100,end_time=105,},
{start_time=105,end_time=110,},
{start_time=110,end_time=115,},
{start_time=115,end_time=120,},
{start_time=120,end_time=125,},
{start_time=125,end_time=130,},
{start_time=130,end_time=135,},
{start_time=135,end_time=140,},
{start_time=140,end_time=145,},
{start_time=145,end_time=150,},
{start_time=150,end_time=155,},
{start_time=155,end_time=200,},
{start_time=200,end_time=205,},
{start_time=205,end_time=210,},
{start_time=210,end_time=215,},
{start_time=215,end_time=220,},
{start_time=220,end_time=225,},
{start_time=225,end_time=230,},
{start_time=230,end_time=235,},
{start_time=235,end_time=240,},
{start_time=240,end_time=245,},
{start_time=245,end_time=250,},
{start_time=250,end_time=255,},
{start_time=255,end_time=300,},
{start_time=300,end_time=305,},
{start_time=305,end_time=310,},
{start_time=310,end_time=315,},
{start_time=315,end_time=320,},
{start_time=320,end_time=325,},
{start_time=325,end_time=330,},
{start_time=330,end_time=335,},
{start_time=335,end_time=340,},
{start_time=340,end_time=345,},
{start_time=345,end_time=350,},
{start_time=350,end_time=355,},
{start_time=355,end_time=400,},
{start_time=400,end_time=405,},
{start_time=405,end_time=410,},
{start_time=410,end_time=415,},
{start_time=415,end_time=420,},
{start_time=420,end_time=425,},
{start_time=425,end_time=430,},
{start_time=430,end_time=435,},
{start_time=435,end_time=440,},
{start_time=440,end_time=445,},
{start_time=445,end_time=450,},
{start_time=450,end_time=455,},
{start_time=455,end_time=500,},
{start_time=500,end_time=505,},
{start_time=505,end_time=510,},
{start_time=510,end_time=515,},
{start_time=515,end_time=520,},
{start_time=520,end_time=525,},
{start_time=525,end_time=530,},
{start_time=530,end_time=535,},
{start_time=535,end_time=540,},
{start_time=540,end_time=545,},
{start_time=545,end_time=550,},
{start_time=550,end_time=555,},
{start_time=555,end_time=600,},
{start_time=600,end_time=605,},
{start_time=605,end_time=610,},
{start_time=610,end_time=615,},
{start_time=615,end_time=620,},
{start_time=620,end_time=625,},
{start_time=625,end_time=630,},
{start_time=630,end_time=635,},
{start_time=635,end_time=640,},
{start_time=640,end_time=645,},
{start_time=645,end_time=650,},
{start_time=650,end_time=655,},
{start_time=655,end_time=700,},
{start_time=700,end_time=705,},
{start_time=705,end_time=710,},
{start_time=710,end_time=715,},
{start_time=715,end_time=720,},
{start_time=720,end_time=725,},
{start_time=725,end_time=730,},
{start_time=730,end_time=735,},
{start_time=735,end_time=740,},
{start_time=740,end_time=745,},
{start_time=745,end_time=750,},
{start_time=750,end_time=755,},
{start_time=755,end_time=800,},
{start_time=800,end_time=805,},
{start_time=805,end_time=810,},
{start_time=810,end_time=815,},
{start_time=815,end_time=820,},
{start_time=820,end_time=825,},
{start_time=825,end_time=830,},
{start_time=830,end_time=835,},
{start_time=835,end_time=840,},
{start_time=840,end_time=845,},
{start_time=845,end_time=850,},
{start_time=850,end_time=855,},
{start_time=855,end_time=900,},
{start_time=900,end_time=905,},
{start_time=905,end_time=910,},
{start_time=910,end_time=915,},
{start_time=915,end_time=920,},
{start_time=920,end_time=925,},
{start_time=925,end_time=930,},
{start_time=930,end_time=935,},
{start_time=935,end_time=940,},
{start_time=940,end_time=945,},
{start_time=945,end_time=950,},
{start_time=950,end_time=955,},
{start_time=955,end_time=1000,},
{start_time=1000,end_time=1005,},
{start_time=1005,end_time=1010,},
{start_time=1010,end_time=1015,},
{start_time=1015,end_time=1020,},
{start_time=1020,end_time=1025,},
{start_time=1025,end_time=1030,},
{start_time=1030,end_time=1035,},
{start_time=1035,end_time=1040,},
{start_time=1040,end_time=1045,},
{start_time=1045,end_time=1050,},
{start_time=1050,end_time=1055,},
{start_time=1055,end_time=1100,},
{start_time=1100,end_time=1105,},
{start_time=1105,end_time=1110,},
{start_time=1110,end_time=1115,},
{start_time=1115,end_time=1120,},
{start_time=1120,end_time=1125,},
{start_time=1125,end_time=1130,},
{start_time=1130,end_time=1135,},
{start_time=1135,end_time=1140,},
{start_time=1140,end_time=1145,},
{start_time=1145,end_time=1150,},
{start_time=1150,end_time=1155,},
{start_time=1155,end_time=1200,},
{start_time=1200,end_time=1205,},
{start_time=1205,end_time=1210,},
{start_time=1210,end_time=1215,},
{start_time=1215,end_time=1220,},
{start_time=1220,end_time=1225,},
{start_time=1225,end_time=1230,},
{start_time=1230,end_time=1235,},
{start_time=1235,end_time=1240,},
{start_time=1240,end_time=1245,},
{start_time=1245,end_time=1250,},
{start_time=1250,end_time=1255,},
{start_time=1255,end_time=1300,},
{start_time=1300,end_time=1305,},
{start_time=1305,end_time=1310,},
{start_time=1310,end_time=1315,},
{start_time=1315,end_time=1320,},
{start_time=1320,end_time=1325,},
{start_time=1325,end_time=1330,},
{start_time=1330,end_time=1335,},
{start_time=1335,end_time=1340,},
{start_time=1340,end_time=1345,},
{start_time=1345,end_time=1350,},
{start_time=1350,end_time=1355,},
{start_time=1355,end_time=1400,},
{start_time=1400,end_time=1405,},
{start_time=1405,end_time=1410,},
{start_time=1410,end_time=1415,},
{start_time=1415,end_time=1420,},
{start_time=1420,end_time=1425,},
{start_time=1425,end_time=1430,},
{start_time=1430,end_time=1435,},
{start_time=1435,end_time=1440,},
{start_time=1440,end_time=1445,},
{start_time=1445,end_time=1450,},
{start_time=1450,end_time=1455,},
{start_time=1455,end_time=1500,},
{start_time=1500,end_time=1505,},
{start_time=1505,end_time=1510,},
{start_time=1510,end_time=1515,},
{start_time=1515,end_time=1520,},
{start_time=1520,end_time=1525,},
{start_time=1525,end_time=1530,},
{start_time=1530,end_time=1535,},
{start_time=1535,end_time=1540,},
{start_time=1540,end_time=1545,},
{start_time=1545,end_time=1550,},
{start_time=1550,end_time=1555,},
{start_time=1555,end_time=1600,},
{start_time=1600,end_time=1605,},
{start_time=1605,end_time=1610,},
{start_time=1610,end_time=1615,},
{start_time=1615,end_time=1620,},
{start_time=1620,end_time=1625,},
{start_time=1625,end_time=1630,},
{start_time=1630,end_time=1635,},
{start_time=1635,end_time=1640,},
{start_time=1640,end_time=1645,},
{start_time=1645,end_time=1650,},
{start_time=1650,end_time=1655,},
{start_time=1655,end_time=1700,moment_index=3,},
{start_time=1700,end_time=1705,},
{start_time=1705,end_time=1710,},
{start_time=1710,end_time=1715,},
{start_time=1715,end_time=1720,},
{start_time=1720,end_time=1725,},
{start_time=1725,end_time=1730,},
{start_time=1730,end_time=1735,},
{start_time=1735,end_time=1740,},
{start_time=1740,end_time=1745,},
{start_time=1745,end_time=1750,},
{start_time=1750,end_time=1755,},
{start_time=1755,end_time=1800,},
{start_time=1800,end_time=1805,},
{start_time=1805,end_time=1810,},
{start_time=1810,end_time=1815,},
{start_time=1815,end_time=1820,},
{start_time=1820,end_time=1825,},
{start_time=1825,end_time=1830,},
{start_time=1830,end_time=1835,},
{start_time=1835,end_time=1840,},
{start_time=1840,end_time=1845,},
{start_time=1845,end_time=1850,},
{start_time=1850,end_time=1855,},
{start_time=1855,end_time=1900,},
{start_time=1900,end_time=1905,},
{start_time=1905,end_time=1910,},
{start_time=1910,end_time=1915,},
{start_time=1915,end_time=1920,},
{start_time=1920,end_time=1925,},
{start_time=1925,end_time=1930,},
{start_time=1930,end_time=1935,},
{start_time=1935,end_time=1940,},
{start_time=1940,end_time=1945,},
{start_time=1945,end_time=1950,},
{start_time=1950,end_time=1955,},
{start_time=1955,end_time=2000,},
{start_time=2000,end_time=2005,},
{start_time=2005,end_time=2010,},
{start_time=2010,end_time=2015,},
{start_time=2015,end_time=2020,},
{start_time=2020,end_time=2025,},
{start_time=2025,end_time=2030,},
{start_time=2030,end_time=2035,},
{start_time=2035,end_time=2040,},
{start_time=2040,end_time=2045,},
{start_time=2045,end_time=2050,},
{start_time=2050,end_time=2055,},
{start_time=2055,end_time=2100,},
{start_time=2100,end_time=2105,},
{start_time=2105,end_time=2110,},
{start_time=2110,end_time=2115,},
{start_time=2115,end_time=2120,},
{start_time=2120,end_time=2125,},
{start_time=2125,end_time=2130,},
{start_time=2130,end_time=2135,},
{start_time=2135,end_time=2140,},
{start_time=2140,end_time=2145,},
{start_time=2145,end_time=2150,},
{start_time=2150,end_time=2155,},
{start_time=2155,end_time=2200,},
{start_time=2200,end_time=2205,},
{start_time=2205,end_time=2210,},
{start_time=2210,end_time=2215,},
{start_time=2215,end_time=2220,},
{start_time=2220,end_time=2225,},
{start_time=2225,end_time=2230,},
{start_time=2230,end_time=2235,},
{start_time=2235,end_time=2240,},
{start_time=2240,end_time=2245,},
{start_time=2245,end_time=2250,},
{start_time=2250,end_time=2255,},
{start_time=2255,end_time=2300,},
{start_time=2300,end_time=2305,},
{start_time=2305,end_time=2310,},
{start_time=2310,end_time=2315,},
{start_time=2315,end_time=2320,},
{start_time=2320,end_time=2325,},
{start_time=2325,end_time=2330,},
{start_time=2330,end_time=2335,},
{start_time=2335,end_time=2340,},
{start_time=2340,end_time=2345,},
{start_time=2345,end_time=2350,},
{start_time=2350,end_time=2355,},
{start_time=2355,end_time=0,}
},

time_day_night_meta_table_map={
[222]=204,	-- depth:1
[240]=204,	-- depth:1
[202]=204,	-- depth:1
[280]=204,	-- depth:1
[250]=204,	-- depth:1
[228]=204,	-- depth:1
[200]=204,	-- depth:1
[282]=204,	-- depth:1
[242]=204,	-- depth:1
[198]=204,	-- depth:1
[224]=204,	-- depth:1
[248]=204,	-- depth:1
[284]=204,	-- depth:1
[196]=204,	-- depth:1
[262]=204,	-- depth:1
[244]=204,	-- depth:1
[260]=204,	-- depth:1
[194]=204,	-- depth:1
[286]=204,	-- depth:1
[278]=204,	-- depth:1
[268]=204,	-- depth:1
[264]=204,	-- depth:1
[206]=204,	-- depth:1
[216]=204,	-- depth:1
[218]=204,	-- depth:1
[232]=204,	-- depth:1
[256]=204,	-- depth:1
[270]=204,	-- depth:1
[246]=204,	-- depth:1
[214]=204,	-- depth:1
[266]=204,	-- depth:1
[234]=204,	-- depth:1
[272]=204,	-- depth:1
[212]=204,	-- depth:1
[230]=204,	-- depth:1
[254]=204,	-- depth:1
[220]=204,	-- depth:1
[210]=204,	-- depth:1
[226]=204,	-- depth:1
[236]=204,	-- depth:1
[258]=204,	-- depth:1
[208]=204,	-- depth:1
[276]=204,	-- depth:1
[252]=204,	-- depth:1
[238]=204,	-- depth:1
[274]=204,	-- depth:1
[144]=204,	-- depth:1
[190]=204,	-- depth:1
[50]=204,	-- depth:1
[52]=204,	-- depth:1
[54]=204,	-- depth:1
[56]=204,	-- depth:1
[58]=204,	-- depth:1
[60]=204,	-- depth:1
[62]=204,	-- depth:1
[64]=204,	-- depth:1
[66]=204,	-- depth:1
[48]=204,	-- depth:1
[68]=204,	-- depth:1
[72]=204,	-- depth:1
[74]=204,	-- depth:1
[76]=204,	-- depth:1
[78]=204,	-- depth:1
[80]=204,	-- depth:1
[82]=204,	-- depth:1
[84]=204,	-- depth:1
[86]=204,	-- depth:1
[88]=204,	-- depth:1
[70]=204,	-- depth:1
[90]=204,	-- depth:1
[46]=204,	-- depth:1
[42]=204,	-- depth:1
[2]=204,	-- depth:1
[4]=204,	-- depth:1
[6]=204,	-- depth:1
[8]=204,	-- depth:1
[10]=204,	-- depth:1
[12]=204,	-- depth:1
[14]=204,	-- depth:1
[16]=204,	-- depth:1
[18]=204,	-- depth:1
[44]=204,	-- depth:1
[20]=204,	-- depth:1
[24]=204,	-- depth:1
[26]=204,	-- depth:1
[28]=204,	-- depth:1
[30]=204,	-- depth:1
[32]=204,	-- depth:1
[34]=204,	-- depth:1
[36]=204,	-- depth:1
[38]=204,	-- depth:1
[40]=204,	-- depth:1
[22]=204,	-- depth:1
[92]=204,	-- depth:1
[94]=204,	-- depth:1
[96]=204,	-- depth:1
[150]=204,	-- depth:1
[152]=204,	-- depth:1
[154]=204,	-- depth:1
[156]=204,	-- depth:1
[158]=204,	-- depth:1
[160]=204,	-- depth:1
[162]=204,	-- depth:1
[164]=204,	-- depth:1
[166]=204,	-- depth:1
[148]=204,	-- depth:1
[168]=204,	-- depth:1
[172]=204,	-- depth:1
[174]=204,	-- depth:1
[176]=204,	-- depth:1
[178]=204,	-- depth:1
[180]=204,	-- depth:1
[182]=204,	-- depth:1
[184]=204,	-- depth:1
[186]=204,	-- depth:1
[188]=204,	-- depth:1
[170]=204,	-- depth:1
[146]=204,	-- depth:1
[142]=204,	-- depth:1
[140]=204,	-- depth:1
[98]=204,	-- depth:1
[100]=204,	-- depth:1
[102]=204,	-- depth:1
[104]=204,	-- depth:1
[106]=204,	-- depth:1
[108]=204,	-- depth:1
[110]=204,	-- depth:1
[112]=204,	-- depth:1
[114]=204,	-- depth:1
[116]=204,	-- depth:1
[118]=204,	-- depth:1
[120]=204,	-- depth:1
[122]=204,	-- depth:1
[124]=204,	-- depth:1
[126]=204,	-- depth:1
[128]=204,	-- depth:1
[130]=204,	-- depth:1
[132]=204,	-- depth:1
[134]=204,	-- depth:1
[136]=204,	-- depth:1
[138]=204,	-- depth:1
[192]=204,	-- depth:1
[288]=204,	-- depth:1
},
other={
{}
},

other_meta_table_map={
},
scene_task_default_table={scene_id=1001,task_start=10,task_end=60,moment_index=3,moment_duration=2,},

scen_regular_default_table={scene_id=101,regular=1,moment_index=2,},

time_day_night_default_table={start_time=0,end_time=5,moment_index=0,},

other_default_table={ignore_scene_id=0,}

}

