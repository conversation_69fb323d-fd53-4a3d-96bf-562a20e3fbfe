function MechaView:LoadWingCallBack()
    if not self.mw_mecha_list then
        self.mw_mecha_list = AsyncListView.New(MWMechaListRender, self.node_list.mw_mecha_list)
        self.mw_mecha_list:SetDefaultSelectIndex(nil)
        self.mw_mecha_list:SetStartZeroIndex(true)
        self.mw_mecha_list:SetSelectCallBack(BindTool.Bind(self.OnSelectMWMechaHandler, self))
    end

    if not self.mw_mecha_part_list then
        self.mw_mecha_part_list = AsyncListView.New(MWMechaPartListRender, self.node_list.mw_mecha_part_list)
        self.mw_mecha_part_list:SetDefaultSelectIndex(nil)
        self.mw_mecha_part_list:SetStartZeroIndex(false)
        self.mw_mecha_part_list:SetSelectCallBack(BindTool.Bind(self.OnSelectMWMechaP<PERSON><PERSON><PERSON><PERSON>, self))
    end

    if not self.mw_mecha_part_cell_list then
        self.mw_mecha_part_cell_list = AsyncListView.New(MWMechaPartCellListRender, self.node_list.mw_mecha_part_cell_list)
        self.mw_mecha_part_cell_list:SetDefaultSelectIndex(nil)
        self.mw_mecha_part_cell_list:SetSelectCallBack(BindTool.Bind(self.OnSelectMWMechaPartCellHandler, self))
    end

    if not self.mw_attr_list1 then
        self.mw_attr_list1 = AsyncListView.New(MWAttrInfoListRender, self.node_list.mw_attr_list1)
    end

    if not self.mw_attr_list2 then
        self.mw_attr_list2 = AsyncListView.New(MWAttrInfoListRender, self.node_list.mw_attr_list2)
    end

    if not self.mw_uplevel_attr_list then
        self.mw_uplevel_attr_list = AsyncListView.New(MWAttrInfoListRender, self.node_list.mw_uplevel_attr_list)
    end

    if not self.mw_active_item then
        self.mw_active_item = ItemCell.New(self.node_list.mw_active_item)
    end

    if not self.mw_up_level_item then
        self.mw_up_level_item = ItemCell.New(self.node_list.mw_up_level_item)
    end

    if not self.mw_gundam_model then
        self.mw_gundam_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["mw_modle_root"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = true,
        }
        
        self.mw_gundam_model:SetRenderTexUI3DModel(display_data)
        -- self.mw_gundam_model:SetUI3DModel(self.node_list["mw_modle_root"].transform, self.node_list["mw_modle_root"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
    end

    self.node_list.mw_attr_tog.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickMWTog,self, 1))
    self.node_list.mw_uplevel_tog.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickMWTog,self, 2))

    XUI.AddClickEventListener(self.node_list.btn_mw_jsyy, BindTool.Bind(self.OnClickMWJsyy, self))
    XUI.AddClickEventListener(self.node_list.btn_mw_zbk, BindTool.Bind(self.OnClickMWZbk, self))
    XUI.AddClickEventListener(self.node_list.btn_mw_active, BindTool.Bind(self.OnClickMWActive, self))
    XUI.AddClickEventListener(self.node_list.btn_mw_up_level, BindTool.Bind(self.OnClickMWUpLevel, self))
    XUI.AddClickEventListener(self.node_list.mw_tips, BindTool.Bind(self.OnClickMWTip, self))

    self.mw_mecha_seq = -1
    self.mw_mecha_data = {}
    self.mw_mecha_part = -1
    self.mw_mecha_part_index = -1
    self.mw_mecha_part_data = {}
    self.mw_mecha_part_cell_sort = -1
    self.mw_mecha_part_cell_data = {}
    self.mw_right_tog_select_id = 1
    self.mw_can_show_sort_tip = true
    self.mw_mecha_part_cell_left_seq = -1
    self.mw_mecha_part_cell_right_seq = -1
    
    self.mw_show_action = false

    RectTransform.SetAnchoredPositionXY(self.node_list.mw_modle_root.rect, -70, -70)
    RectTransform.SetLocalScale(self.node_list.mw_modle_root.rect, 0.8)
end

function MechaView:ShowWingCallBack()
    self:RightPanleShowTween(self.node_list.mw_right_tween_root, self.node_list.mw_right_tween_root)
    self:PlayMWPShowAction()
end

function MechaView:ChangeWingCallBack()
end

function MechaView:PlayMWPShowAction()
    self.node_list.mw_modle_root.rect.localRotation = Quaternion.Euler(0, 0, 0)

    if self.mw_gundam_model then
        self.mw_gundam_model:PlayRoleShowAction()
    
        GlobalTimerQuest:AddDelayTimer(function ()
            if self.node_list.mw_modle_root then
                self.node_list.mw_modle_root.rect:DOLocalRotate(Vector3(0, 150, 0), 1)
            end
        end, 3.8)
    end
end

function MechaView:ReleaseWingCallBack()
    if self.mw_mecha_list then
        self.mw_mecha_list:DeleteMe()
        self.mw_mecha_list = nil
    end

    if self.mw_mecha_part_list then
        self.mw_mecha_part_list:DeleteMe()
        self.mw_mecha_part_list = nil
    end

    if self.mw_mecha_part_cell_list then
        self.mw_mecha_part_cell_list:DeleteMe()
        self.mw_mecha_part_cell_list = nil
    end

    if self.mw_attr_list1 then
        self.mw_attr_list1:DeleteMe()
        self.mw_attr_list1 = nil
    end

    if self.mw_attr_list2 then
        self.mw_attr_list2:DeleteMe()
        self.mw_attr_list2 = nil
    end

    if self.mw_active_item then
        self.mw_active_item:DeleteMe()
        self.mw_active_item = nil
    end

    if self.mw_uplevel_attr_list then
        self.mw_uplevel_attr_list:DeleteMe()
        self.mw_uplevel_attr_list = nil
    end

    if self.mw_up_level_item then
        self.mw_up_level_item:DeleteMe()
        self.mw_up_level_item = nil
    end

    if self.mw_gundam_model then
        self.mw_gundam_model:DeleteMe()
        self.mw_gundam_model = nil
    end

    self.mw_model_cache = nil
end

function MechaView:OnFlushWingCallBack()
    local mecha_show_data = MechaWGData.Instance:GetMechaShowDataList()
    self.mw_mecha_list:SetDataList(mecha_show_data)
    self.mw_mecha_list:JumpToIndex(self:GetMWSelectMecha(mecha_show_data))
end

------------------------------------选择回调------------------------------
function MechaView:OnSelectMWMechaHandler(item)
    if nil == item or IsEmptyTable(item.data) then
        return
    end

    self.mw_can_show_sort_tip = false
    local data = item.data
    self.mw_mecha_seq = data.seq
    self.mw_mecha_data = data
    local part_data_list_cfg = MechaWGData.Instance:GetMechaPartShowDataList(self.mw_mecha_seq)

    local part_data_list = {}
    for k, v in pairs(part_data_list_cfg) do
        if k > MECHA_PART_TYPE.RIGHT_FOOT and k <= MECHA_PART_TYPE.RIGHT_WING then
            table.insert(part_data_list, v)
        end
    end

    self.mw_mecha_part_list:SetDataList(part_data_list)
    self.mw_mecha_part_list:JumpToIndex(self:GetMWSelectMechaPart(part_data_list))

    local is_wear_complete = MechaWGData.Instance:IsMechaWearComplete(self.mw_mecha_seq)
    self.node_list.flag_mw_need_zbk:CustomSetActive(not is_wear_complete)

    local mw_cap = MechaWGData.Instance:GetMWShowCap(self.mw_mecha_seq)
    self.node_list.mw_cap_value.text.text = mw_cap
end

function MechaView:OnSelectMWMechaPartHandler(item)
    if nil == item or IsEmptyTable(item.data) then
        return
    end

    self.mw_can_show_sort_tip = false
    local data = item.data
    self.mw_mecha_part = data[1].part
    self.mw_mecha_part_index = item.index
    self.mw_mecha_part_data = data

    local part_cell_data_list = MechaWGData.Instance:GetMechaPartCellShowDataList(self.mw_mecha_seq, self.mw_mecha_part)
    self.mw_mecha_part_cell_list:SetDataList(part_cell_data_list)
    self.mw_mecha_part_cell_list:JumpToIndex(self:GetMWSelectMechaPartCell(part_cell_data_list))
end

function MechaView:OnSelectMWMechaPartCellHandler(item)
    if nil == item or IsEmptyTable(item.data) then
        return
    end

    local data = item.data
    if self.mw_can_show_sort_tip and self.mw_mecha_part_cell_sort == data.sort then
        TipWGCtrl.Instance:OpenItem({item_id = data.active_item_id})
    end

    self.mw_mecha_part_cell_sort = data.sort 
    self.mw_mecha_part_cell_data = data
    self.node_list.mw_desc_name.text.text = data.part_name

    if self.mw_mecha_part == MECHA_PART_TYPE.LEFT_WING then
        self.mw_mecha_part_cell_left_seq = data.seq

        if self.mw_mecha_part_cell_right_seq < 0 then
            local default_data = MechaWGData.Instance:GetMechaPartCellCfg(self.mw_mecha_seq,  MECHA_PART_TYPE.RIGHT_WING, 1)
            self.mw_mecha_part_cell_right_seq = default_data.seq
        end
    else
        self.mw_mecha_part_cell_right_seq = data.seq

        if self.mw_mecha_part_cell_left_seq < 0 then
            local default_data = MechaWGData.Instance:GetMechaPartCellCfg(self.mw_mecha_seq, MECHA_PART_TYPE.LEFT_WING, 1)
            self.mw_mecha_part_cell_left_seq = default_data.seq
        end
    end

    self:FlushMWMidModel()

    -- local part_cell_remind_tab = MechaWGData.Instance:GetMWMechaPartCellRemindTab(self.mw_mecha_seq, self.mw_mecha_part, self.mw_mecha_part_cell_sort)
    -- self.node_list.mw_attr_tog_remind:CustomSetActive(part_cell_remind_tab and part_cell_remind_tab.active_remind)
    -- self.node_list.mw_uplevel_tog_remind:CustomSetActive(part_cell_remind_tab and part_cell_remind_tab.up_level_remind)

    self.mw_can_show_sort_tip = true
    self:FlushWingRight()

    self:FlushMWSkillInfo()
end

function MechaView:FlushWingRight()
    local tog_select_id = self:GetMWRightTogSelect()

    if self.mw_right_tog_select_id == tog_select_id then
        if tog_select_id == 1 then
            self:FlushMWAttrInfoPanel()
        else
            self:FlushMWUplevelPanel()
        end
    else
        if tog_select_id == 1 then
            self.node_list.mw_attr_tog.toggle.isOn = true
        else
            self.node_list.mw_uplevel_tog.toggle.isOn = true
        end
    end

    self.mw_right_tog_select_id = tog_select_id
end

function MechaView:FlushMWAttrInfoPanel()
    local left_attr_info = {}
    local right_attr_info = {}

    _, left_attr_info = MechaWGData.Instance:GetPartUpLevelAttrDataList(self.mw_mecha_part_cell_left_seq)
    self.mw_attr_list1:SetDataList(left_attr_info)
    self.node_list.img_mw_top_title1.text.text = string.format(Language.Mecha.MechaAttrNameTitle, Language.Mecha.MechaPartName[MECHA_PART_TYPE.LEFT_WING])

    _, right_attr_info = MechaWGData.Instance:GetPartUpLevelAttrDataList(self.mw_mecha_part_cell_right_seq)
    self.mw_attr_list2:SetDataList(right_attr_info)
    self.node_list.img_mw_top_title2.text.text = string.format(Language.Mecha.MechaAttrNameTitle, Language.Mecha.MechaPartName[MECHA_PART_TYPE.RIGHT_WING])

    local seq = self.mw_mecha_part_cell_data.seq
    local cur_star = MechaWGData.Instance:GetPartStarBySeq(seq)
    local is_active = cur_star >= 0
    self.node_list.mw_active_cost:CustomSetActive(not is_active)
    self.node_list.flag_mw_active:CustomSetActive(is_active)

    if not is_active then
        cur_star = cur_star >= 0 and cur_star or 0
        local up_star_cfg = MechaWGData.Instance:GetPartUpStarCfg(seq, cur_star)
        if not IsEmptyTable(up_star_cfg) then
            local active_item_id = up_star_cfg.cost_item_id
            local cost_item_num = up_star_cfg.cost_item_num

            local has_num = ItemWGData.Instance:GetItemNumInBagById(active_item_id)
            local color = has_num >= cost_item_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
            local str = has_num .. "/" .. cost_item_num
            self.mw_active_item:SetFlushCallBack(function ()
                self.mw_active_item:SetRightBottomColorText(str, color)
                self.mw_active_item:SetRightBottomTextVisible(true)
            end)
            self.mw_active_item:SetData({item_id = active_item_id})
            self.node_list.btn_mw_active_remind:CustomSetActive(has_num >= cost_item_num)
        end
    end
end

function MechaView:FlushMWUplevelPanel()
    local seq = self.mw_mecha_part_cell_data.seq
    local cur_star = MechaWGData.Instance:GetPartStarBySeq(seq)
    local is_avtive = cur_star >= 0
    local _, attr_info = MechaWGData.Instance:GetPartUpLevelAttrDataList(seq)
    self.mw_uplevel_attr_list:SetDataList(attr_info)
    local is_max_star = MechaWGData.Instance:IsPartIsMaxStar(seq)
    self.node_list.mw_up_level_info:CustomSetActive(not is_max_star)
    self.node_list.mw_up_level_max:CustomSetActive(is_max_star)
    self.node_list.img_mw_up_level_title.text.text = string.format(Language.Mecha.MechaAttrNameTitle, Language.Mecha.MechaPartName[self.mw_mecha_part])

    if not is_max_star then
        cur_star = cur_star >= 0 and cur_star or 0
        local up_star_cfg = MechaWGData.Instance:GetPartUpStarCfg(seq, cur_star)
        if not IsEmptyTable(up_star_cfg) then
            local active_item_id = up_star_cfg.cost_item_id
            local cost_item_num = up_star_cfg.cost_item_num

            local has_num = ItemWGData.Instance:GetItemNumInBagById(active_item_id)
            local color = has_num >= cost_item_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
            local str = has_num .. "/" .. cost_item_num
            self.mw_up_level_item:SetFlushCallBack(function ()
                self.mw_up_level_item:SetRightBottomColorText(str, color)
                self.mw_up_level_item:SetRightBottomTextVisible(true)
            end)
            self.mw_up_level_item:SetData({item_id = active_item_id})

            self.node_list.btn_mw_up_level_remind:CustomSetActive(has_num >= cost_item_num)
        end

        for i = 1, 5 do
            local asset_index = 2
            if cur_star > 0 then
                if cur_star > 5 then
                    asset_index = (cur_star - 5) >= i and 3 or 1
                else
                    asset_index = cur_star >= i and 1 or 2
                end
            end

            local bundle, asset = ResPath.GetMechaImg("a2_jj_di_xing" .. asset_index)
            self.node_list["mw_star" .. i].image:LoadSprite(bundle, asset, function ()
                self.node_list["mw_star" .. i].image:SetNativeSize()
            end)
        end
        
        XUI.SetButtonEnabled(self.node_list.btn_mw_up_level, is_avtive)
    else
        self.node_list.btn_mw_up_level_remind:CustomSetActive(false)
    end
end

function MechaView:OnClickMWTog(index)
    if index == 1 then
        self:FlushMWAttrInfoPanel()
    else
        self:FlushMWUplevelPanel()
    end

    self.mw_right_tog_select_id = index
end

function MechaView:FlushMWMidModel()
    local part_list = {}
    local base_part = MechaWGData.Instance:GetMechaBasePartListByMechaSeq(self.mw_mecha_seq)
    for k, v in pairs(base_part) do
        local part_cfg = MechaWGData.Instance:GetPartCfgBySeq(v)
        part_list[part_cfg.part] = part_cfg.res_id
    end

    local putton_list = MechaWGData.Instance:GetMechaPartWearPartList(self.mw_mecha_seq)
    if not IsEmptyTable(putton_list) then
        for k, v in pairs(putton_list) do
            if v >= 0 then
                local part_cfg = MechaWGData.Instance:GetPartCfgBySeq(v)
                part_list[part_cfg.part] = part_cfg.res_id
            end
        end
    end

    if self.mw_mecha_part == MECHA_PART_TYPE.LEFT_WING then
        local left_wing_cfg = MechaWGData.Instance:GetPartCfgBySeq(self.mw_mecha_part_cell_left_seq)
        part_list[left_wing_cfg.part] = left_wing_cfg.res_id
    else
        local right_wing_cfg = MechaWGData.Instance:GetPartCfgBySeq(self.mw_mecha_part_cell_right_seq)
        part_list[right_wing_cfg.part] = right_wing_cfg.res_id
    end

    local part_info = {
		gundam_seq = self.mw_mecha_seq,
		gundam_body_res = part_list[MECHA_PART_TYPE.BODY] or 0,
        gundam_weapon_res = part_list[MECHA_PART_TYPE.WEAPON] or 0,
		gundam_left_arm_res = part_list[MECHA_PART_TYPE.LEFT_HAND] or 0,
        gundam_right_arm_res = part_list[MECHA_PART_TYPE.RIGHT_HAND] or 0,
		gundam_left_leg_res = part_list[MECHA_PART_TYPE.LEFT_FOOT] or 0,
        gundam_right_leg_res = part_list[MECHA_PART_TYPE.RIGHT_FOOT] or 0,
		gundam_left_wing_res = part_list[MECHA_PART_TYPE.LEFT_WING] or 0,
        gundam_right_wing_res = part_list[MECHA_PART_TYPE.RIGHT_WING] or 0,
	}

    if self:IsMWModelChange(part_info) then
        self.mw_model_cache = part_info
        self.mw_gundam_model:SetGundamModel(part_info)

        if not self.mw_show_action then
            self.mw_show_action = true
            self:PlayMWPShowAction()
        end
    end
end

function MechaView:IsMWModelChange(part_info)
    if IsEmptyTable(self.mw_model_cache) then
        return true
    end

    for k, v in pairs(part_info) do
        if self.mw_model_cache[k] ~= v then
            return true
        end
    end

    return false
end

function MechaView:FlushMWSkillInfo()
    local target_seq = self.mw_mecha_part_cell_data.seq
    local skill_id = MechaWGData.Instance:GetSKillIdBySeq(target_seq)
    self.node_list.btn_mw_jsyy:CustomSetActive(skill_id and skill_id > 0)

    if skill_id and skill_id > 0 then
        local skill_info = SkillWGData.Instance:GetJiJiaSkillConfig(skill_id)

        local active = MechaWGData.Instance:IsWingSkillIsActive(self.mw_mecha_seq, skill_id)
        self.node_list.flag_mw_skill_active:CustomSetActive(active)

        if IsEmptyTable(skill_info) then
            return
        end

        self.node_list.desc_mw_jsyy.text.text = skill_info.skill_name

        local common_skill_info = SkillWGData.Instance:GetSkillClientConfig(skill_id)
        if IsEmptyTable(common_skill_info) then
            return
        end

        local bundle, asset = ResPath.GetSkillIconById(common_skill_info.icon_resource)
        self.node_list.mw_jsyy_icon.image:LoadSprite(bundle, asset, function ()
            self.node_list.mw_jsyy_icon.image:SetNativeSize()
        end)
    end
end

------------------------------------计算选择------------------------------
function MechaView:GetMWSelectMecha(mecha_show_data)
    if self.mw_mecha_seq >= 0 then
        local cur_remind = MechaWGData.Instance:GetMWMechaRemind(self.mw_mecha_seq)

        if cur_remind then
            return self.mw_mecha_seq
        end
    end

    if not IsEmptyTable(mecha_show_data) then
        for k, v in pairs(mecha_show_data) do
            if self.mw_mecha_seq ~= v.seq then
                local remind = MechaWGData.Instance:GetMWMechaRemind(v.seq)
                if remind then
                    return v.seq
                end
            end
        end 
    end
    
    return self.mw_mecha_seq > 0 and self.mw_mecha_seq or 0
end

function MechaView:GetMWSelectMechaPart(part_data_list)
    if not IsEmptyTable(part_data_list) then
        if self.mw_mecha_part_index > 0 then
            local cur_data = part_data_list[self.mw_mecha_part_index]

            if not IsEmptyTable(cur_data) then
               local mecha_seq = cur_data[1].mechan_seq
               local part = cur_data[1].part
                local remind = MechaWGData.Instance:GetMWMechaPartRemind(mecha_seq, part)
                if remind then
                    return self.mw_mecha_part_index
                end
            end
        end

        for k, v in pairs(part_data_list) do
            if k ~= self.mw_mecha_part_index then
                local mecha_seq = v[1].mechan_seq
                local part = v[1].part
                 local remind = MechaWGData.Instance:GetMWMechaPartRemind(mecha_seq, part)
                 if remind then
                    return k
                end
            end
        end
    end

    return self.mw_mecha_part_index > 0 and self.mw_mecha_part_index or 1
end

function MechaView:GetMWSelectMechaPartCell(part_cell_data_list)
    if self.mw_mecha_part_cell_sort > 0 then
        local remind = MechaWGData.Instance:GetMWMechaPartCellRemind(self.mw_mecha_seq, self.mw_mecha_part, self.mw_mecha_part_cell_sort)
        if remind then
            return self.mw_mecha_part_cell_sort
        end
    end

    if not IsEmptyTable(part_cell_data_list) then
        for k, v in pairs(part_cell_data_list) do
            if v.sort ~= self.mw_mecha_part_cell_sort then
                local remind = MechaWGData.Instance:GetMWMechaPartCellRemind(v.mechan_seq, v.part, v.sort)

                if remind then
                    return v.sort
                end
            end
        end
    end

    return self.mw_mecha_part_cell_sort > 0 and self.mw_mecha_part_cell_sort or 1
end

function MechaView:GetMWRightTogSelect()
    local part_cell_remind_tab = MechaWGData.Instance:GetMWMechaPartCellRemindTab(self.mw_mecha_seq, self.mw_mecha_part, self.mw_mecha_part_cell_sort)

    if not IsEmptyTable(part_cell_remind_tab) then
        if self.mw_right_tog_select_id == 1 then
            if part_cell_remind_tab.active_remind then
                return 1
            elseif part_cell_remind_tab.up_level_remind then
                return 2
            end
        else
            if part_cell_remind_tab.up_level_remind then
                return 2
            elseif part_cell_remind_tab.active_remind then
                return 1
            end
        end
    end

    return self.mw_right_tog_select_id >= 1 and self.mw_right_tog_select_id or 1
end

------------------------------------btns------------------------------
function MechaView:OnClickMWJsyy()
    local target_seq = self.mw_mecha_part_cell_data.seq
    local skill_id = MechaWGData.Instance:GetSKillIdBySeq(target_seq)

    if skill_id > 0 then
        local skill_info = SkillWGData.Instance:GetJiJiaSkillConfig(skill_id)
        if IsEmptyTable(skill_info) then
            return
        end

        local common_skill_info = SkillWGData.Instance:GetSkillClientConfig(skill_id)
        if IsEmptyTable(common_skill_info) then
            return     
        end

        local skill_type_tab = SkillWGData.Instance:GetSkillAttackType(skill_id)
        local real_limit_text = ""
        local active_skill_need_part_list = MechaWGData.Instance:GetSkillActiveNeedPartList(skill_id)

        if not IsEmptyTable(active_skill_need_part_list) then
            local putton_list = MechaWGData.Instance:GetMechaPartWearPartList(self.mw_mecha_seq)
            if not IsEmptyTable(putton_list) then
                local active = true
                for k, v in pairs(active_skill_need_part_list) do
                    if putton_list[k] < 0 or putton_list[k] ~= v then
                        active = false
                    end

                    local part_cfg = MechaWGData.Instance:GetPartCfgBySeq(v)
                    real_limit_text = real_limit_text == "" and (part_cfg.part_name) or (real_limit_text .. "," .. part_cfg.part_name)
                end

                local color = active and COLOR3B.GREEN or COLOR3B.RED
                real_limit_text = ToColorStr(string.format(Language.Mecha.MechaSkillBomLimitDesc, real_limit_text), color)
            end
        end

        local show_data = {
            icon = common_skill_info.icon_resource,
            top_text = skill_info.skill_name,
            skill_level = skill_info.skill_level,
            body_text = common_skill_info.description,	
            limit_text = real_limit_text,
            is_up_operate = true,
            capability = 0,
            x = 0,
            y = 0,
            set_pos2 = true,
        }

        NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
    end
end

function MechaView:OnClickMWZbk()
    if not MechaWGData.Instance:IsMechaActive(self.mw_mecha_seq) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Mecha.ArnanentNotActiveTips)
        return
    end

    MechaWGCtrl.Instance:OpenMechaArmamentView(self.mw_mecha_seq)
end

function MechaView:OnClickMWActive()
    local seq = self.mw_mecha_part_cell_data.seq
    local cur_star = MechaWGData.Instance:GetPartStarBySeq(seq)
    if cur_star < 0 then
        cur_star = cur_star >= 0 and cur_star or 0
        local up_star_cfg = MechaWGData.Instance:GetPartUpStarCfg(seq, cur_star)

        if not IsEmptyTable(up_star_cfg) then
            local cust_item_id = up_star_cfg.cost_item_id
            local cost_item_num = up_star_cfg.cost_item_num
            local has_num = ItemWGData.Instance:GetItemNumInBagById(cust_item_id)
    
            if has_num >= cost_item_num then
                MechaWGCtrl.Instance:SendRequest(MECHA_OPERA_TYPE.PART_STAR, seq)
                TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo, is_success = true, pos = Vector2(0, 0)})
            else
                TipWGCtrl.Instance:OpenItem({item_id = cust_item_id})
            end
        end
    end
end

function MechaView:OnClickMWUpLevel()
    local seq = self.mw_mecha_part_cell_data.seq
    local cur_star = MechaWGData.Instance:GetPartStarBySeq(seq)

    if cur_star >= 0 then
        local up_star_cfg = MechaWGData.Instance:GetPartUpStarCfg(seq, cur_star)

        if not IsEmptyTable(up_star_cfg) then
            local cust_item_id = up_star_cfg.cost_item_id
            local cost_item_num = up_star_cfg.cost_item_num
            local has_num = ItemWGData.Instance:GetItemNumInBagById(cust_item_id)
    
            if has_num >= cost_item_num then
                MechaWGCtrl.Instance:SendRequest(MECHA_OPERA_TYPE.PART_STAR, seq)
                TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengxing, is_success = true, pos = Vector2(0, 0)})
            else
                TipWGCtrl.Instance:OpenItem({item_id = cust_item_id})
            end
        end
    end
end

function MechaView:OnClickMWTip()
    RuleTip.Instance:SetContent(Language.Mecha.MWTIPContent, Language.Mecha.MWTipTitle)
end

------------------------------------MWMechaListRender------------------------------
MWMechaListRender = MWMechaListRender or BaseClass(BaseRender)

function MWMechaListRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local icon_bundle, icon_asset = ResPath.GetMechaImg(self.data.icon)
    self.node_list.mecha_icon.image:LoadSprite(icon_bundle, icon_asset, function ()
        self.node_list.mecha_icon.image:SetNativeSize()
    end)

    self.node_list.desc_name.text.text = self.data.name

    local remind = MechaWGData.Instance:GetMWMechaRemind(self.data.seq)
    self.node_list.remind:CustomSetActive(remind)

    local is_wear_complete = MechaWGData.Instance:IsMechaWearComplete(self.data.seq)
    self.node_list.flag_wear_complete:CustomSetActive(is_wear_complete)
end

function MWMechaListRender:OnSelectChange(is_select)
    self.node_list.bg_nor:CustomSetActive(not is_select)
    self.node_list.select_nor:CustomSetActive(not is_select)
    self.node_list.bg_select:CustomSetActive(is_select)
    self.node_list.select_hl:CustomSetActive(is_select)
end

------------------------------------MWMechaPartListRender------------------------------
MWMechaPartListRender = MWMechaPartListRender or BaseClass(BaseRender)

function MWMechaPartListRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local part = self.data[1].part
    local mecha_seq = self.data[1].mechan_seq
    
    local part_cfg = MechaWGData.MECHA_PART_SHOW_CFG[part] or MechaWGData.MECHA_PART_SHOW_CFG[1]

    local nor_bundle, nor_asset = ResPath.GetMechaImg(part_cfg.icon)
    self.node_list.icon_nor.image:LoadSprite(nor_bundle, nor_asset, function ()
        self.node_list.icon_nor.image:SetNativeSize()
    end)

    local hl_bundle, hl_asset = ResPath.GetMechaImg(part_cfg.icon_hl)
    self.node_list.icon_hl.image:LoadSprite(hl_bundle, hl_asset, function ()
        self.node_list.icon_hl.image:SetNativeSize()
    end)

    local mirror = part == MECHA_PART_TYPE.RIGHT_WING
    local scale = mirror and -1 or 1
    RectTransform.SetLocalScaleXYZ(self.node_list.icon_nor.rect, scale, 1, 1)
    RectTransform.SetLocalScaleXYZ(self.node_list.icon_hl.rect, scale, 1, 1)

    local name = Language.Mecha.MechaPartName[part]
    self.node_list.desc_name.text.text = name
    self.node_list.desc_name_hl.text.text = name

    local remind = MechaWGData.Instance:GetMWMechaPartRemind(mecha_seq, part)
    self.node_list.remind:CustomSetActive(remind)
end

function MWMechaPartListRender:OnSelectChange(is_select)
    self.node_list.bg_nor:CustomSetActive(not is_select)
    self.node_list.select_hl:CustomSetActive(is_select)
end

------------------------------------MWMechaPartCellListRender------------------------------
MWMechaPartCellListRender = MWMechaPartCellListRender or BaseClass(BaseRender)

function MWMechaPartCellListRender:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function MWMechaPartCellListRender:LoadCallBack()
    if not self.item then
        self.item = ItemCell.New(self.node_list.item)
        self.item:SetIsShowTips(false)
        self.item:SetCellBgEnabled(false)
        self.item:SetUseButton(false)
    end
end

function MWMechaPartCellListRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.item:SetData({item_id = self.data.active_item_id})
    local seq = self.data.seq
    local cur_star = MechaWGData.Instance:GetPartStarBySeq(seq)
    self.node_list.flag_lock:CustomSetActive(cur_star < 0)

    self.item:MakeGray(cur_star < 0)

    local remind = MechaWGData.Instance:GetMWMechaPartCellRemind(self.data.mechan_seq, self.data.part, self.data.sort)
    self.node_list.remind:CustomSetActive(remind)
end

function MWMechaPartCellListRender:OnSelectChange(is_select)
    self.node_list.select:CustomSetActive(is_select)
end

----------------------------------MFPAttrInfoListRender--------------------------------------
MWAttrInfoListRender = MWAttrInfoListRender or BaseClass(BaseRender)

function MWAttrInfoListRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.attr_name.text.text = self.data.attr_name or ""
    self.node_list.attr_value.text.text =  self.data.attr_value or 0
    local has_next_value = self.data.add_attr_value and self.data.add_attr_value > 0
    self.node_list.add_value.text.text = has_next_value and self.data.add_value or ""
    self.node_list.arrow:CustomSetActive(has_next_value)
end