-- Y-运营活动-万象玄生.xls
local item_table={
[1]={item_id=48120,num=1,is_bind=1},
[2]={item_id=27410,num=1,is_bind=1},
[3]={item_id=48595,num=1,is_bind=1},
[4]={item_id=48117,num=1,is_bind=1},
[5]={item_id=44496,num=1,is_bind=1},
[6]={item_id=44495,num=1,is_bind=1},
[7]={item_id=26357,num=1,is_bind=1},
[8]={item_id=26360,num=1,is_bind=1},
[9]={item_id=26363,num=1,is_bind=1},
[10]={item_id=26369,num=1,is_bind=1},
[11]={item_id=26380,num=1,is_bind=1},
[12]={item_id=26355,num=1,is_bind=1},
[13]={item_id=26356,num=1,is_bind=1},
[14]={item_id=26358,num=1,is_bind=1},
[15]={item_id=26359,num=1,is_bind=1},
[16]={item_id=26361,num=1,is_bind=1},
[17]={item_id=26362,num=1,is_bind=1},
[18]={item_id=26367,num=1,is_bind=1},
[19]={item_id=26368,num=1,is_bind=1},
[20]={item_id=26378,num=1,is_bind=1},
[21]={item_id=26379,num=1,is_bind=1},
[22]={item_id=26502,num=1,is_bind=1},
[23]={item_id=26517,num=1,is_bind=1},
[24]={item_id=26127,num=1,is_bind=1},
[25]={item_id=26126,num=1,is_bind=1},
[26]={item_id=22618,num=1,is_bind=1},
[27]={item_id=26345,num=1,is_bind=1},
[28]={item_id=26347,num=1,is_bind=1},
[29]={item_id=26350,num=1,is_bind=1},
[30]={item_id=26353,num=1,is_bind=1},
[31]={item_id=26377,num=1,is_bind=1},
[32]={item_id=44182,num=1,is_bind=1},
[33]={item_id=44183,num=1,is_bind=1},
[34]={item_id=44184,num=1,is_bind=1},
[35]={item_id=27659,num=1,is_bind=1},
[36]={item_id=27658,num=1,is_bind=1},
[37]={item_id=27657,num=1,is_bind=1},
[38]={item_id=48598,num=1,is_bind=1},
[39]={item_id=48599,num=1,is_bind=1},
[40]={item_id=26199,num=10,is_bind=1},
[41]={item_id=26199,num=45,is_bind=1},
[42]={item_id=26199,num=20,is_bind=1},
[43]={item_id=26199,num=30,is_bind=1},
[44]={item_id=26199,num=50,is_bind=1},
[45]={item_id=28851,num=1,is_bind=1},
[46]={item_id=26563,num=1,is_bind=1},
[47]={item_id=29800,num=1,is_bind=1},
[48]={item_id=28849,num=1,is_bind=1},
[49]={item_id=26566,num=1,is_bind=1},
[50]={item_id=26080,num=6,is_bind=1},
[51]={item_id=48442,num=1,is_bind=1},
[52]={item_id=26944,num=1,is_bind=1},
[53]={item_id=26194,num=1,is_bind=1},
[54]={item_id=26193,num=1,is_bind=1},
[55]={item_id=26191,num=1,is_bind=1},
[56]={item_id=48596,num=1,is_bind=1},
[57]={item_id=48600,num=1,is_bind=1},
[58]={item_id=48601,num=1,is_bind=1},
[59]={item_id=26161,num=1,is_bind=1},
[60]={item_id=26199,num=1,is_bind=1},
[61]={item_id=18793,num=1,is_bind=1},
}

return {
open_day={
{}
},

open_day_meta_table_map={
},
config={
{},
{grade=1,consume=2,reward_pool_id=2,sp_draw_num=2,rebate=2,},
{grade=2,consume=3,reward_pool_id=3,sp_draw_num=3,rebate=3,},
{grade=3,consume=4,reward_pool_id=4,sp_draw_num=4,rebate=4,}
},

config_meta_table_map={
},
reward_pool={
{reward_type=1,big_reward_count=2501,reward_num_limit=3,reward_weight=50,broadcast=1,reward_big_show=3,},
{reward_id=2,reward_item=item_table[1],reward_type=1,big_reward_count=499,reward_num_limit=30,reward_weight=100,broadcast=1,reward_big_show=3,},
{reward_id=3,reward_item=item_table[2],reward_num_limit=5,reward_big_show=2,},
{reward_id=4,reward_item=item_table[3],reward_type=1,big_reward_count=401,reward_num_limit=20,reward_weight=200,broadcast=1,reward_big_show=1,},
{reward_id=5,reward_item=item_table[4],big_reward_count=301,reward_weight=400,},
{reward_id=6,reward_item=item_table[5],big_reward_count=101,reward_num_limit=100,reward_weight=500,broadcast=1,},
{reward_id=7,reward_item=item_table[6],big_reward_count=31,reward_weight=600,},
{reward_id=8,reward_item=item_table[7],},
{reward_id=9,reward_item=item_table[8],},
{reward_id=10,reward_item=item_table[9],},
{reward_id=11,reward_item=item_table[10],},
{reward_id=12,reward_item=item_table[11],big_reward_count=51,reward_num_limit=100,reward_weight=700,},
{reward_id=13,reward_item=item_table[12],},
{reward_id=14,reward_item=item_table[13],},
{reward_id=15,reward_item=item_table[14],},
{reward_id=16,reward_item=item_table[15],},
{reward_id=17,reward_item=item_table[16],},
{reward_id=18,reward_item=item_table[17],},
{reward_id=19,reward_item=item_table[18],},
{reward_id=20,reward_item=item_table[19],},
{reward_id=21,reward_item=item_table[20],},
{reward_id=22,reward_item=item_table[21],},
{reward_id=23,reward_item=item_table[22],},
{reward_id=24,reward_item=item_table[23],},
{reward_id=25,reward_item=item_table[24],reward_weight=1000,reward_show=0,},
{reward_id=26,reward_item=item_table[25],},
{reward_id=27,reward_item=item_table[26],},
{reward_id=28,reward_item=item_table[27],},
{reward_id=29,reward_item=item_table[28],},
{reward_id=30,reward_item=item_table[29],},
{reward_id=31,reward_item=item_table[30],reward_show=0,},
{reward_id=32,reward_item=item_table[31],},
{reward_id=33,reward_item=item_table[32],},
{reward_id=34,reward_item=item_table[33],},
{reward_id=35,reward_item=item_table[34],reward_weight=3000,reward_show=0,},
{reward_id=36,reward_item=item_table[35],reward_weight=2000,reward_show=0,},
{reward_id=37,reward_item=item_table[36],},
{reward_id=38,reward_item=item_table[37],reward_weight=4000,reward_show=0,},
{reward_id=39,reward_item=item_table[38],},
{reward_id=40,reward_item=item_table[39],}
},

reward_pool_meta_table_map={
[23]=31,	-- depth:1
[22]=31,	-- depth:1
[21]=31,	-- depth:1
[27]=31,	-- depth:1
[32]=31,	-- depth:1
[26]=31,	-- depth:1
[33]=31,	-- depth:1
[24]=31,	-- depth:1
[28]=31,	-- depth:1
[29]=31,	-- depth:1
[30]=31,	-- depth:1
[34]=31,	-- depth:1
[37]=35,	-- depth:1
[40]=38,	-- depth:1
[39]=38,	-- depth:1
[11]=12,	-- depth:1
[10]=12,	-- depth:1
[9]=12,	-- depth:1
[8]=12,	-- depth:1
[7]=12,	-- depth:1
[5]=6,	-- depth:1
[3]=4,	-- depth:1
},
consume={
{},
{onekey_draw_num=10,consume_price=2000,draw_item=item_table[40],},
{onekey_draw_num=50,consume_price=9000,draw_item=item_table[41],discount_text=0.9,},
{consume=2,},
{consume=2,},
{consume=2,},
{consume=3,},
{consume=3,},
{consume=3,},
{consume=4,},
{consume=4,},
{consume=4,}
},

consume_meta_table_map={
[5]=2,	-- depth:1
[8]=5,	-- depth:2
[11]=8,	-- depth:3
[6]=3,	-- depth:1
[9]=6,	-- depth:2
[12]=9,	-- depth:3
},
rebate={
{draw_num=30,start_num=0,},
{index=2,start_num=20,},
{index=3,draw_num=100,reward_item={[0]=item_table[42]},start_num=50,},
{index=4,draw_num=300,reward_item={[0]=item_table[43]},},
{index=5,draw_num=500,},
{index=6,draw_num=1000,reward_item={[0]=item_table[44]},start_num=500,},
{index=7,draw_num=2000,reward_item={[0]=item_table[44]},start_num=1000,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,reward_item={[0]=item_table[45]},},
{rebate=2,index=5,draw_num=250,reward_item={[0]=item_table[46]},},
{rebate=2,},
{rebate=2,},
{rebate=3,start_num=0,},
{rebate=3,},
{rebate=3,},
{rebate=3,reward_item={[0]=item_table[47]},},
{rebate=3,},
{rebate=3,draw_num=300,reward_item={[0]=item_table[48]},start_num=250,},
{rebate=3,draw_num=350,reward_item={[0]=item_table[49]},start_num=300,},
{rebate=4,},
{rebate=4,index=2,},
{rebate=4,draw_num=150,reward_item={[0]=item_table[6]},start_num=100,},
{rebate=4,draw_num=200,reward_item={[0]=item_table[50]},start_num=150,},
{rebate=4,},
{rebate=4,},
{rebate=4,}
},

rebate_meta_table_map={
[22]=15,	-- depth:1
[8]=22,	-- depth:2
[5]=4,	-- depth:1
[26]=12,	-- depth:1
[19]=26,	-- depth:2
[25]=4,	-- depth:1
[24]=3,	-- depth:1
[23]=3,	-- depth:1
[21]=7,	-- depth:1
[20]=6,	-- depth:1
[14]=21,	-- depth:2
[17]=24,	-- depth:2
[16]=23,	-- depth:2
[27]=20,	-- depth:2
[13]=27,	-- depth:3
[11]=25,	-- depth:2
[10]=17,	-- depth:3
[9]=16,	-- depth:3
[18]=25,	-- depth:2
[28]=14,	-- depth:3
},
redemption_points={
{points_num=3000,num_limit=1,},
{reward_id=2,reward_item=item_table[51],points_num=1800,},
{reward_id=3,reward_item=item_table[52],},
{reward_id=4,reward_item=item_table[1],num_limit=10,},
{reward_id=5,reward_item=item_table[2],points_num=500,num_limit=10,},
{reward_id=6,reward_item=item_table[53],points_num=300,num_limit=1,},
{reward_id=7,reward_item=item_table[54],points_num=150,num_limit=3,},
{reward_id=8,reward_item=item_table[55],points_num=80,},
{reward_id=9,reward_item=item_table[56],points_num=300,num_limit=10,},
{reward_id=10,reward_item=item_table[57],},
{reward_id=11,reward_item=item_table[58],points_num=100,num_limit=3,},
{reward_id=12,reward_item=item_table[35],points_num=50,num_limit=100,}
},

redemption_points_meta_table_map={
[10]=8,	-- depth:1
},
item_random_desc={
{item_id=26161,random_count=0.1,},
{number=2,item_id=48120,},
{number=3,item_id=27410,},
{number=4,item_id=48597,},
{number=5,item_id=48117,},
{number=6,item_id=44496,},
{number=7,item_id=44495,},
{number=8,item_id=26357,},
{number=9,random_count=0.3,},
{number=10,item_id=26363,},
{number=11,item_id=26369,},
{number=12,item_id=26380,},
{number=13,item_id=26355,},
{number=14,item_id=26356,},
{number=15,item_id=26358,},
{number=16,item_id=26359,},
{number=17,item_id=26361,},
{number=18,item_id=26362,},
{number=19,item_id=26367,},
{number=20,item_id=26368,},
{number=21,item_id=26378,},
{number=22,item_id=26379,random_count=4,},
{number=23,item_id=26502,},
{number=24,item_id=26517,},
{number=25,item_id=26127,random_count=0.5,},
{number=26,item_id=26126,},
{number=27,item_id=22618,},
{number=28,item_id=26345,},
{number=29,item_id=26347,},
{number=30,item_id=26350,},
{number=31,item_id=26353,},
{number=32,item_id=26377,},
{number=33,item_id=44182,},
{number=34,item_id=44183,},
{number=35,item_id=44184,random_count=1.65,},
{number=36,item_id=27659,random_count=1,},
{number=37,item_id=27658,},
{number=38,item_id=27657,},
{number=39,item_id=48598,},
{number=40,item_id=48599,random_count=3,},
{grade=1,item_id=37440,},
{grade=1,number=2,item_id=37408,random_count=0.5,},
{number=3,item_id=37917,},
{grade=1,item_id=37816,},
{grade=1,number=5,item_id=44073,random_count=5,},
{number=6,item_id=28851,},
{grade=1,number=7,item_id=26127,random_count=15,},
{number=8,item_id=26126,},
{grade=1,number=9,},
{grade=1,number=10,},
{number=11,item_id=27659,},
{grade=1,number=12,},
{grade=1,number=13,},
{grade=1,number=14,},
{grade=1,number=15,item_id=27658,random_count=8,},
{number=16,item_id=26502,},
{grade=1,item_id=26517,},
{grade=1,number=18,item_id=26349,random_count=40,},
{grade=1,number=19,item_id=26500,random_count=30,},
{number=20,item_id=26515,},
{grade=2,item_id=18790,},
{grade=2,item_id=18791,},
{number=3,item_id=37920,},
{grade=2,item_id=37819,},
{grade=2,item_id=28852,},
{grade=2,number=6,random_count=1,},
{number=7,item_id=26359,},
{number=8,item_id=26358,},
{grade=2,number=9,},
{number=10,item_id=26350,},
{number=11,item_id=26363,},
{number=12,item_id=26362,},
{number=13,item_id=26361,},
{grade=2,number=14,},
{number=15,item_id=26353,},
{number=16,item_id=26357,},
{number=17,item_id=26356,},
{number=18,item_id=26355,},
{grade=2,number=19,},
{grade=2,number=20,item_id=26347,},
{number=21,item_id=26380,},
{number=22,item_id=26379,},
{number=23,item_id=26378,},
{grade=2,number=24,},
{number=25,item_id=26376,},
{number=26,item_id=26369,},
{number=27,item_id=26368,},
{number=28,item_id=26367,},
{number=29,item_id=26345,},
{grade=2,number=30,item_id=26344,random_count=40,},
{grade=3,item_id=26072,random_count=0.01,},
{grade=3,item_id=26070,},
{number=3,item_id=37923,},
{grade=3,number=4,item_id=37822,random_count=2,},
{grade=3,number=5,item_id=26393,random_count=3,},
{number=6,item_id=26392,},
{grade=3,number=7,item_id=26391,random_count=40,},
{grade=3,number=8,},
{number=9,item_id=26359,},
{number=10,item_id=26358,},
{number=11,item_id=26351,},
{number=12,item_id=26350,},
{number=13,item_id=26363,},
{number=14,item_id=26362,},
{number=15,item_id=26361,},
{number=16,item_id=26354,},
{number=17,item_id=26353,},
{number=18,item_id=26357,},
{number=19,item_id=26356,},
{grade=3,item_id=26355,},
{number=21,item_id=26348,},
{grade=3,number=22,},
{number=23,item_id=26380,},
{grade=3,number=24,},
{grade=3,number=25,},
{grade=3,number=26,item_id=26377,random_count=6,},
{number=27,item_id=26376,},
{number=28,item_id=26369,},
{grade=3,number=29,},
{grade=3,number=30,},
{number=31,item_id=26345,},
{grade=3,number=32,}
},

item_random_desc_meta_table_map={
[78]=80,	-- depth:1
[77]=80,	-- depth:1
[75]=80,	-- depth:1
[73]=80,	-- depth:1
[72]=80,	-- depth:1
[70]=80,	-- depth:1
[68]=80,	-- depth:1
[67]=80,	-- depth:1
[57]=77,	-- depth:2
[56]=57,	-- depth:3
[54]=68,	-- depth:2
[53]=70,	-- depth:2
[52]=67,	-- depth:2
[49]=66,	-- depth:1
[82]=80,	-- depth:1
[83]=80,	-- depth:1
[87]=80,	-- depth:1
[88]=80,	-- depth:1
[120]=88,	-- depth:2
[119]=87,	-- depth:2
[115]=83,	-- depth:2
[114]=82,	-- depth:2
[112]=80,	-- depth:1
[110]=80,	-- depth:1
[109]=110,	-- depth:2
[46]=52,	-- depth:3
[107]=110,	-- depth:2
[104]=110,	-- depth:2
[102]=110,	-- depth:2
[100]=110,	-- depth:2
[99]=110,	-- depth:2
[98]=66,	-- depth:1
[96]=110,	-- depth:2
[105]=110,	-- depth:2
[41]=91,	-- depth:1
[61]=91,	-- depth:1
[18]=22,	-- depth:1
[30]=22,	-- depth:1
[12]=9,	-- depth:1
[29]=22,	-- depth:1
[28]=22,	-- depth:1
[27]=22,	-- depth:1
[13]=22,	-- depth:1
[26]=22,	-- depth:1
[24]=22,	-- depth:1
[14]=22,	-- depth:1
[23]=22,	-- depth:1
[15]=22,	-- depth:1
[21]=22,	-- depth:1
[16]=22,	-- depth:1
[17]=22,	-- depth:1
[20]=22,	-- depth:1
[19]=22,	-- depth:1
[32]=22,	-- depth:1
[11]=9,	-- depth:1
[31]=22,	-- depth:1
[10]=9,	-- depth:1
[2]=1,	-- depth:1
[3]=1,	-- depth:1
[39]=40,	-- depth:1
[38]=40,	-- depth:1
[4]=1,	-- depth:1
[5]=1,	-- depth:1
[37]=35,	-- depth:1
[34]=22,	-- depth:1
[33]=22,	-- depth:1
[6]=1,	-- depth:1
[8]=9,	-- depth:1
[7]=1,	-- depth:1
[108]=98,	-- depth:2
[118]=98,	-- depth:2
[117]=97,	-- depth:1
[111]=95,	-- depth:1
[103]=98,	-- depth:2
[106]=95,	-- depth:1
[101]=95,	-- depth:1
[113]=98,	-- depth:2
[93]=98,	-- depth:2
[43]=49,	-- depth:2
[44]=94,	-- depth:1
[48]=47,	-- depth:1
[50]=101,	-- depth:2
[51]=45,	-- depth:1
[60]=59,	-- depth:1
[121]=116,	-- depth:1
[92]=42,	-- depth:1
[62]=42,	-- depth:1
[64]=94,	-- depth:1
[65]=45,	-- depth:1
[69]=101,	-- depth:2
[71]=66,	-- depth:1
[74]=106,	-- depth:2
[76]=66,	-- depth:1
[79]=111,	-- depth:2
[81]=66,	-- depth:1
[84]=116,	-- depth:1
[85]=90,	-- depth:1
[86]=66,	-- depth:1
[89]=84,	-- depth:2
[63]=66,	-- depth:1
[122]=90,	-- depth:1
},
open_day_default_table={start_server_day=1,end_server_day=999,grade=0,},

config_default_table={grade=0,consume=1,reward_pool_id=1,sp_draw_num=1,rebate=1,},

reward_pool_default_table={reward_pool_id=1,reward_id=1,reward_item=item_table[59],reward_type=2,big_reward_count=0,reward_num_limit=0,reward_weight=5000,broadcast=0,reward_show=1,rewrad_rare_show=0,need_reward_display=1,reward_big_show=0,},

consume_default_table={consume=1,onekey_draw_num=1,consume_type=1,consume_price=200,draw_item=item_table[60],discount_text="",},

rebate_default_table={rebate=1,index=1,draw_num=50,reward_item={[0]=item_table[40]},start_num=200,},

redemption_points_default_table={grade=0,reward_id=1,reward_item=item_table[61],points_num=800,num_limit=5,},

item_random_desc_default_table={grade=0,number=1,item_id=26360,random_count=10,}

}

