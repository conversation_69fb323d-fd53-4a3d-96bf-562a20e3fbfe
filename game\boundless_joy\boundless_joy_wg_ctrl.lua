require("game/boundless_joy/boundless_joy_view")
require("game/boundless_joy/boundless_joy_reward_show_view")
require("game/boundless_joy/boundless_joy_wg_data")

BoundlessJoyWGCtrl = BoundlessJoyWGCtrl or BaseClass(BaseWGCtrl)

function BoundlessJoyWGCtrl:__init()
	if BoundlessJoyWGCtrl.Instance then
        error("[BoundlessJoyWGCtrl]:Attempt to create singleton twice!")
	end

	BoundlessJoyWGCtrl.Instance = self
	self.data = BoundlessJoyWGData.New()
	self.boundless_joy_view = BoundlessJoyView.New(GuideModuleName.BoundlessJoyView)
    self.selectreward_view = BoundlessJoyRewardView.New()
    self:RegisterAllProtocals()
end

function BoundlessJoyWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.boundless_joy_view:DeleteMe()
	self.boundless_joy_view = nil

	self.selectreward_view:DeleteMe()
	self.selectreward_view = nil

	if self.choose_reward_alert then
        self.choose_reward_alert:DeleteMe()
        self.choose_reward_alert = nil
    end

	BoundlessJoyWGCtrl.Instance = nil
end

function BoundlessJoyWGCtrl:FlushView()
	if self.boundless_joy_view:IsOpen() then
		self.boundless_joy_view:Flush()
	end
end

function BoundlessJoyWGCtrl:RegisterAllProtocals()
    self:RegisterProtocol(SCHappyForeverInfo, "OnSCHappyForeverInfo")
	self:RegisterProtocol(SCHappyForeverResult, "OnSCHappyForeverResult")
	self:RegisterProtocol(CSHappyForeverOperate)
end


--打开奖励选择界面
function BoundlessJoyWGCtrl:OpenSelectRewardView()
	if not self.selectreward_view:IsOpen() then
		self.selectreward_view:Open()
	end
end

--奖池信息
function BoundlessJoyWGCtrl:OnSCHappyForeverInfo(protocol)
    self.data:SetHappyForeverAllInfo(protocol)
    if self.boundless_joy_view:IsOpen() then
        self.boundless_joy_view:Flush()
    end
	RemindManager.Instance:Fire(RemindName.BoundlessJoy)
end

--一键购买奖励结果
function BoundlessJoyWGCtrl:OnSCHappyForeverResult(protocol)
	TipWGCtrl.Instance:ShowGetReward(nil, protocol.result_reward_list, false, nil, nil, false)
end

function BoundlessJoyWGCtrl:SendHappyForeverReq(operate, param1, param2)
    local protocol = ProtocolPool.Instance:GetProtocol(CSHappyForeverOperate)
 	protocol.operate = operate or 0
 	protocol.param1 = param1 or 0
 	protocol.param2 = param2 or 0
 	protocol:EncodeAndSend()
end

function BoundlessJoyWGCtrl:OpenChooseRewardAlertView(ok_func)
    if not self.choose_reward_alert then
        self.choose_reward_alert = Alert.New()
    end

    self.choose_reward_alert:SetOkFunc(ok_func)
    self.choose_reward_alert:SetLableString(Language.BoundlessJoy.choose_reward_tips)
    self.choose_reward_alert:SetCheckBoxDefaultSelect(false)
    self.choose_reward_alert:Open()
end