HomesView = HomesView or BaseClass(SafeBaseView)

local HomesTypeMax = 5

local HomesTypeList = {
    {TypeName = Language.Homes.TypeGrop[1], TypeRemind = nil, TypeModuleName = nil},
    {TypeName = Language.Homes.TypeGrop[2], TypeRemind = {RemindName.Sworn}, TypeModuleName = GuideModuleName.SwornView},
    {TypeName = Language.Homes.TypeGrop[3], TypeRemind = {RemindName.Marry_Flowers,RemindName.Marry_Send_Flowers}, TypeModuleName = GuideModuleName.Flower},
    {TypeName = Language.Homes.TypeGrop[4], TypeRemind = {RemindName.SocietyFriendsInfo}, TypeModuleName = GuideModuleName.Society},
    {TypeName = Language.Homes.TypeGrop[5], TypeRemind = {RemindName.Marry}, TypeModuleName = GuideModuleName.Marry},
}

function HomesView:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self:SetMaskBg()
    local bundle_name = "uis/view/homes_ui_prefab"
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
    self:AddViewResource(0, bundle_name, "layout_homes")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function HomesView:ReleaseCallBack()
    if nil ~= self.type_obj_list then
        for k,v in pairs(self.type_obj_list) do
            v:DeleteMe()
        end
        self.type_obj_list = nil
    end

    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

    -- 取消红点监听
    RemindManager.Instance:UnBind(self.remind_callback)
end

function HomesView:ShowIndexCallBack()
    self.node_list.sworn_bg:CustomSetActive(SwornWGData.Instance:IsShowHomeSwornBg())
end

function HomesView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.Homes.ViewName
    if self.node_list.RawImage_tongyong then
        local bundle, asset = ResPath.GetRawImagesPNG("a3_jysj_bg")
        self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
            self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
        end)
    end

    if nil == self.type_obj_list then
        self.type_obj_list = {}
        for i = 1, HomesTypeMax do
            local obj = self.node_list.content:FindObj("type_" .. i)
            local cell = HomesTypeRender.New(obj)
            cell:SetIndex(i)
            if #HomesTypeList >= i then
                local data = HomesTypeList[i]
                cell:SetData(data)
            end

            self.type_obj_list[i] = cell
        end
    end

    if not self.money_bar then
        self.money_bar = MoneyBar.New()
        local bundle, asset = ResPath.GetWidgets("MoneyBar")
        local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
        self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
    end

    XUI.AddClickEventListener(self.node_list["btn_marry"], BindTool.Bind(self.OnBtnMarry, self))--仙侣跳转
    XUI.AddClickEventListener(self.node_list["btn_sworn"], BindTool.Bind(self.OnBtnSworn, self))--结义跳转
    XUI.AddClickEventListener(self.node_list["btn_dan"], BindTool.Bind(self.OnBtnDan, self))--炼丹跳转
    XUI.AddClickEventListener(self.node_list["btn_duan"], BindTool.Bind(self.OnBtnDuan, self))--锻造跳转
    XUI.AddClickEventListener(self.node_list["btn_ts"], BindTool.Bind(self.OnBtnTS, self))--探索
    XUI.AddClickEventListener(self.node_list["btn_flower"], BindTool.Bind(self.OnBtnFlower, self))--赠花跳转

    -- 界面监听其他功能的红点变化
    local other_remind_list = {
        RemindName.SocietyFriendsInfo,
        RemindName.Marry_Flowers,  
        RemindName.Marry_Send_Flowers, 
        RemindName.Marry,
        RemindName.Sworn
        }
    self.remind_callback = BindTool.Bind(self.OtherRemindCallBack, self)
    for k,v in pairs(other_remind_list) do
        RemindManager.Instance:Bind(self.remind_callback, v)
    end
end

function HomesView:OnFlush(param_t)
    self:FlushRemind()
end

function HomesView:FlushRemind()
    if not IsEmptyTable(self.type_obj_list) then
        for k, v in pairs(self.type_obj_list) do
            v:OnFlush()
        end
    end

    self.node_list.marry_remind:SetActive(RemindManager.Instance:GetRemind(RemindName.Marry) > 0)
    self.node_list.sworn_remind:SetActive(RemindManager.Instance:GetRemind(RemindName.Sworn) > 0)
    self.node_list.flower_remind:SetActive(RemindManager.Instance:GetRemind(RemindName.Marry_Flowers) > 0 or RemindManager.Instance:GetRemind(RemindName.Marry_Send_Flowers) > 0)
    --self.node_list.duan_remind:SetActive(RemindManager.Instance:GetRemind(RemindName.Equipment) > 0)
    --self.node_list.dan_remind:SetActive(RemindManager.Instance:GetRemind(RemindName.CountryMapActAlchemy) > 0)
end

function HomesView:OtherRemindCallBack()
    self:FlushRemind()
end

function HomesView:OnBtnMarry()
    ViewManager.Instance:Open(GuideModuleName.Marry, TabIndex.marry_jiehun)
end

function HomesView:OnBtnSworn()
    -- ViewManager.Instance:Open(GuideModuleName.SwornView, TabIndex.sworn_start)
    if SwornWGData.Instance:IsShowHomeSwornBg() and not SwornWGData.Instance:HadSworn() then
        SwornWGData.Instance:SetShowHomeSwornBg()
        self.node_list.sworn_bg:CustomSetActive(SwornWGData.Instance:IsShowHomeSwornBg())
    end
    
    ViewManager.Instance:Open(GuideModuleName.SwornView)
end

function HomesView:OnBtnDan()
    ViewManager.Instance:Open(GuideModuleName.AlchemyView)
end

function HomesView:OnBtnDuan()
    ViewManager.Instance:Open(GuideModuleName.Equipment, TabIndex.equipment_strength)
end

function HomesView:OnBtnTS()
    ViewManager.Instance:Open(GuideModuleName.CountryMapActView)
end

function HomesView:OnBtnFlower()
    ViewManager.Instance:Open(GuideModuleName.Flower)
end



----------------------------HomesTypeRender-------------
HomesTypeRender = HomesTypeRender or BaseClass(BaseRender)

function HomesTypeRender:__init()

end

function HomesTypeRender:__delete()
    self.remind_fun_name = nil
end

function HomesTypeRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["type"], BindTool.Bind(self.OnBtnJump, self))--跳转
end

function HomesTypeRender:OnFlush()
    if self.data == nil or IsEmptyTable(self.data) then
        return
    end

    local num = 0
    if not IsEmptyTable(self.data.TypeRemind) then
        for k, v in pairs(self.data.TypeRemind) do
            num = RemindManager.Instance:GetRemind(v)
            self.remind_fun_name = v
            if num > 0 then
                break
            end
        end
    end

    local name = RemindFunName[self.remind_fun_name]
    if name == nil then
        name = self.data.TypeModuleName
    end

    self.node_list.btn_text.text.text = self.data.TypeName
    self.node_list.remind:SetActive(num > 0)

    local fun_name = FunOpen.Instance:GetFunNameByViewName(self.data.TypeModuleName) or self.data.TypeModuleName
    local is_open = FunOpen.Instance:GetFunIsOpened(fun_name)
    self.node_list.bg:SetActive(is_open)
    self.node_list.bg_1:SetActive(not is_open)

    self.node_list.tips_text.text.text = Language.Homes.TipsList[self:GetIndex()]
end

function HomesTypeRender:OnBtnJump()
    if self.data == nil or IsEmptyTable(self.data) then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Homes.TipsDesc)
        return
    end

    if self.remind_fun_name ~= nil and self.data.TypeModuleName then
        ViewManager.Instance:Open(self.data.TypeModuleName, RemindFunName[self.remind_fun_name])
    -- else
    --     MarryWGCtrl.Instance:OpenSelectLoverView(1)
    end
end