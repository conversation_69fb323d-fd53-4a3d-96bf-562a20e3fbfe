require("game/serveractivity/consume_rank_three/consume_rank_three_wg_data")

--消费排行
ConsumeRankThreeWGCtrl = ConsumeRankThreeWGCtrl or BaseClass(BaseWGCtrl)

function ConsumeRankThreeWGCtrl:__init()
	if ConsumeRankThreeWGCtrl.Instance ~= nil then
		print("[ConsumeRankThreeWGCtrl]error:create a singleton twice")
	end
	ConsumeRankThreeWGCtrl.Instance = self

	self.data = ConsumeRankThreeWGData.New()

	self:RegisterAllProtocols()
end

function ConsumeRankThreeWGCtrl:__delete()
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	ConsumeRankThreeWGCtrl.Instance = nil
end

function ConsumeRankThreeWGCtrl:RegisterAllProtocols()

end

