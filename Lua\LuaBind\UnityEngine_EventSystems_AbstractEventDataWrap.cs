﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_EventSystems_AbstractEventDataWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.EventSystems.AbstractEventData), typeof(System.Object));
		L<PERSON>RegFunction("Reset", Reset);
		L.RegFunction("Use", Use);
		L<PERSON>RegFunction("__tostring", ToLua.op_ToString);
		L<PERSON>ar("used", get_used, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Reset(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.EventSystems.AbstractEventData obj = (UnityEngine.EventSystems.AbstractEventData)ToLua.CheckObject<UnityEngine.EventSystems.AbstractEventData>(L, 1);
			obj.Reset();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Use(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.EventSystems.AbstractEventData obj = (UnityEngine.EventSystems.AbstractEventData)ToLua.CheckObject<UnityEngine.EventSystems.AbstractEventData>(L, 1);
			obj.Use();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_used(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.EventSystems.AbstractEventData obj = (UnityEngine.EventSystems.AbstractEventData)o;
			bool ret = obj.used;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index used on a nil value");
		}
	}
}

