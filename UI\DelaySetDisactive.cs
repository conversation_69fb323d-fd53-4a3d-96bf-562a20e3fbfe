﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 当一个游戏物体激活之后，延迟多少秒隐藏
/// </summary>
public class DelaySetDisactive : MonoBehaviour {

    [Tooltip("延迟时间")]
    public float delayTime = 5;

    void OnEnable()
    {
        StopCoroutine("DelayDisEnable");
        StartCoroutine("DelayDisEnable");
    }

    IEnumerator DelayDisEnable()
    {
        yield return new WaitForSeconds(delayTime);
        gameObject.SetActive(false);
    }
}
