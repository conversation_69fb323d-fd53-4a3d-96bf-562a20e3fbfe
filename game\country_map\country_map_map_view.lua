CountryMapMapView = CountryMapMapView or BaseClass(SafeBaseView)

function CountryMapMapView:__init()
	self.view_style = ViewStyle.Full

	self:SetMaskBg()
	self.default_index = TabIndex.country_map_actshow

	self.remind_tab = {
		{ RemindName.CountryMapActShow },
		--{ RemindName.CrossFGB },
		{ RemindName.CrossUltimate },
	}

	local bundle_name = "uis/view/country_map_ui_prefab"
	local common_bundle = "uis/view/common_panel_prefab"
	self:AddViewResource(0, common_bundle, "layout_a2_common_panel")
	self:AddViewResource(TabIndex.country_map_actshow, bundle_name, "layout_country_map_actshow_view")
	-- self:AddViewResource(TabIndex.country_map_flag_grabbing_battlefield,
	-- 	"uis/view/country_map_ui/flag_grabing_battlefield_ui_prefab", "layout_flag_grabbing_battlefield_view")
	self:AddViewResource(TabIndex.country_map_ultimate_battlefield, "uis/view/country_map_ui/ultimate_battlefield_prefab",
		"layout_ultimate_battlefield_view")
	self:AddViewResource(0, common_bundle, "VerticalTabbar")
	self:AddViewResource(0, bundle_name, "layout_countrymap_top_panel")
end

function CountryMapMapView:OpenCallBack()
	-- 请求一下终极战场膜拜信息和排行榜
	UltimateBattlefieldWGCtrl.Instance:RequestWorShipInfo()
	UltimateBattlefieldWGCtrl.Instance:RequestRankInfo()
end

function CountryMapMapView:CloseCallBack()
end

function CountryMapMapView:LoadCallBack()
	if nil == self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:Init(Language.CountryMap.NewTableGroup, nil, nil, nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
	end

	if self.node_list.btn_close_window then
		XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind(self.Close, self))
	end

	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.CountryMapMapView, self.get_guide_ui_event)
end

function CountryMapMapView:LoadIndexCallBack(index)
	if index == TabIndex.country_map_actshow then
		self:ActShowViewLoadIndexCallBack()
	-- elseif index == TabIndex.country_map_flag_grabbing_battlefield then
	-- 	self:LoadIndexCallBackFGBView()
	elseif index == TabIndex.country_map_ultimate_battlefield then
		self:LoadIndexCallBackUltimateBattlefield()
	end
end

function CountryMapMapView:ReleaseCallBack()
	self:ActShowViewReleaseCallBack()
	--self:ReleaseFGBView()
	self:ReleaseUltimateBattlefield()

	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.CountryMapMapView, self.get_guide_ui_event)
	self.get_guide_ui_event = nil
end

function CountryMapMapView:ShowIndexCallBack(index)
	local bundle, asset = ResPath.GetRawImagesPNG("a2_ty_bg")
	if index == TabIndex.country_map_actshow then
		self:ActShowViewShowIndexCallBack()
	-- elseif index == TabIndex.country_map_flag_grabbing_battlefield then
	-- 	bundle, asset = ResPath.GetRawImagesPNG("a2_dqzc_bj")
	-- 	self:FGBShowIndexCallBack()
	elseif index == TabIndex.country_map_ultimate_battlefield then
		bundle, asset = ResPath.GetRawImagesPNG("a2_zjzc_bj_1")
		self:ShowIndexCallBackUltimateBattlefield()
	end

	local title = Language.CountryMap.NewTableGroup[index / 10]
	if self.node_list["title_view_name"] then
		self.node_list["title_view_name"].text.text = title
	end

	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
end

function CountryMapMapView:OnFlush(param_t, index)
	if index == TabIndex.country_map_actshow then
		self:ActShowViewOnFlush(param_t)
	-- elseif index == TabIndex.country_map_flag_grabbing_battlefield then
	-- 	self:OnFlushFGBView(param_t, index)
	elseif index == TabIndex.country_map_ultimate_battlefield then
		self:OnFlushUltimateBattlefield(param_t, index)
	end
end
