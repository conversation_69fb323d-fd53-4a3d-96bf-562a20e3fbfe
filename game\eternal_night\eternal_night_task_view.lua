EternalNightTaskView = EternalNightTaskView or BaseClass(SafeBaseView)
function EternalNightTaskView:__init()
	self:AddViewResource(0, "uis/view/eternal_night_ui_prefab", "layout_eternal_night_task")
    self.active_close = false
    self.is_safe_area_adapter = true
end

function EternalNightTaskView:__delete()
end

function EternalNightTaskView:LoadCallBack()
	self.reward_list = AsyncListView.New(ItemCell,self.node_list["reward_list"])

	XUI.AddClickEventListener(self.node_list["rank_btn"], BindTool.Bind(self.ClickRankBtn,self))
	XUI.AddClickEventListener(self.node_list["reward_btn"], BindTool.Bind(self.ClickRewardBtn,self))
	XUI.AddClickEventListener(self.node_list["boss_laixi_btn"], BindTool.Bind(self.ClickBossBtn,self))
	XUI.AddClickEventListener(self.node_list["boss_laixi_btn_2"], BindTool.Bind(self.ClickBossBtn,self))
	XUI.AddClickEventListener(self.node_list["equip_btn"], BindTool.Bind(self.ClickEquipBtn,self))
	XUI.AddClickEventListener(self.node_list["flush_map_btn"], BindTool.Bind(self.ClickFlushMapBtn,self))

 	local map_obj = self.node_list["map"].transform
 	self.map_btn_list = {}
 	 for i=1,4 do
    	local btn_str = "map_btn"..i
		local role_num_str = "map_btn"..i.."/role_num" ..i
    	--local btn_hl_str = "map_btn"..i.."/map_btn_hl"..i
    	--local btn_hl_anim_str = "map_btn"..i.."/map_btn_anim_hl"..i
    	local btn = U3DObject(map_obj:Find(btn_str).gameObject, map_obj:Find(btn_str), self)
		local role_num = U3DObject(map_obj:Find(role_num_str).gameObject, map_obj:Find(role_num_str), self)
    	--local btn_hl = U3DObject(map_obj:Find(btn_hl_str).gameObject, map_obj:Find(btn_hl_str), self)
    	--local btn_anim_hl = U3DObject(map_obj:Find(btn_hl_anim_str).gameObject, map_obj:Find(btn_hl_anim_str), self)
    	local item = {}
    	item.btn = btn
		item.role_num = role_num
    	--item.btn_hl = btn_hl
    	--item.btn_anim_hl = btn_anim_hl
    	self.map_btn_list[i] = item
    	XUI.AddClickEventListener(btn, BindTool.Bind(self.ClickMapBtn,self,i))
    end

    local info = RoleWGData.Instance:GetRoleInfo()
    self.head_cell = BaseHeadCell.New(self.node_list["head_cell"])
	self.head_cell:SetData({role_id = info.role_id, sex = info.sex, prof = info.prof, fashion_photoframe = info.fashion_photoframe})
	--local bundle, asset = ResPath.GetEternalNightUI("a3_dt_tx")
	--self.head_cell:ChangeBg(bundle, asset, true)
	self.head_cell:SetBgActive(false)
end

function EternalNightTaskView:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

	if CountDownManager.Instance:HasCountDown("start_finals_time") then
		CountDownManager.Instance:RemoveCountDown("start_finals_time")
	end

	if CountDownManager.Instance:HasCountDown("eternal_night_boss") then
		CountDownManager.Instance:RemoveCountDown("eternal_night_boss")
	end

	if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end
end

function EternalNightTaskView:ShowIndexCallBack()
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function ( )
		self:InitInviteSceneCallBack()
	end)
end

function EternalNightTaskView:CloseCallBack()
	--MainuiWGCtrl.Instance:SetFBNameState(false)
	self:ReleaseTaskPanel()
end

function EternalNightTaskView:InitInviteSceneCallBack()
	local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
	-- local fb_icon = MainuiWGCtrl.Instance:GetFbIconContent()

	local cur_grade_type = EternalNightWGData.Instance:GetCurGradeType() or 1
	self.node_list["countdown_img"].text.text = Language.EternalNight.CountDownText[cur_grade_type]
	self.node_list["eternal_night_task"].transform:SetParent(parent.transform, false)
	self.node_list["eternal_night_task"].rect.anchoredPosition = Vector2(0, 0)
	MainuiWGCtrl.Instance:SetTaskContents(false)
	MainuiWGCtrl.Instance:SetOtherContents(true)

	-- self.node_list["flush_map_btn"].transform:SetParent(fb_icon.transform, false)
	-- self.node_list["flush_map_btn"].transform:SetSiblingIndex(0)
	-- self.node_list["equip_btn"].transform:SetParent(fb_icon.transform, false)
	-- self.node_list["equip_btn"].transform:SetSiblingIndex(0)
	MainuiWGCtrl.Instance:AddBtnToFbIconGroup2Line(self.node_list["reward_btn"])
	MainuiWGCtrl.Instance:AddBtnToFbIconGroup2Line(self.node_list["rank_btn"])
	MainuiWGCtrl.Instance:AddBtnToFbIconGroup2Line(self.node_list["equip_btn"])
	self:FlushTimeCount()
end

function EternalNightTaskView:FlushTimeCount()
	local other_cfg = EternalNightWGData.Instance:GetOtherCfg()
	local scene_info = EternalNightWGData.Instance:GetSceneInfo()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local start_finals_show_time = other_cfg.start_finals_show_time or 0
	local start_finals_time = scene_info and scene_info.start_finals_time or 0
	local info_time = 0
	local cur_grade_type = EternalNightWGData.Instance:GetCurGradeType() or 1

	if cur_grade_type == EternalNightWGData.GradeType.Collect then
		info_time = scene_info and scene_info.start_snatch_time or 0
	elseif cur_grade_type == EternalNightWGData.GradeType.Snatch then
		info_time = scene_info and scene_info.start_finals_time or 0
	elseif cur_grade_type == EternalNightWGData.GradeType.Eliminate then
		info_time = scene_info and scene_info.kick_out_role_time or 0
		local act_type = EternalNightWGData.Instance:GetCurEnterActType() or ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT_KF
		if act_type then
			local act_info = ActivityWGData.Instance:GetActivityStatuByType(act_type)
			info_time = act_info and act_info.next_time or 0
		end
	end

	local time = info_time - server_time 

	self.node_list["time_countdown"]:SetActive(false)
	self:ShowMainEndTime(true)
	if time > 0 then
		if CountDownManager.Instance:HasCountDown("start_finals_time") then
			CountDownManager.Instance:RemoveCountDown("start_finals_time")
		end
		CountDownManager.Instance:AddCountDown("start_finals_time", BindTool.Bind(self.FinalUpdateTimeCallBack,self), BindTool.Bind(self.FinalCompleteTimeCallBack,self), nil, time,1)
	elseif info_time - server_time > start_finals_show_time then
		self:FinalCompleteTimeCallBack()
	end
end

function EternalNightTaskView:FinalUpdateTimeCallBack(now_time, total_time)
	local other_cfg = EternalNightWGData.Instance:GetOtherCfg()
	local start_finals_show_time = other_cfg.start_finals_show_time or 0

	local time = math.ceil(total_time - now_time)
	local time_str = TimeUtil.MSTime(time)
	self.node_list["time_text"].text.text = time_str
	if time < start_finals_show_time then
		self:ShowMainEndTime(false)
		self.node_list["time_countdown"]:SetActive(true)
		self.node_list["countdown_text"].text.text = time .. "s"
	end
end

function EternalNightTaskView:ShowMainEndTime(flag)
	if flag then
		local act_type = EternalNightWGData.Instance:GetCurEnterActType()
		if act_type then
			local act_info = ActivityWGData.Instance:GetActivityStatuByType(act_type)
			if act_info then
				MainuiWGCtrl.Instance:SetFbIconEndCountDown(act_info.next_time,true)
			end
		end
	else
		-- MainuiWGCtrl.Instance:SetFbIconEndCountDown(0)
	end
end

function EternalNightTaskView:FinalCompleteTimeCallBack()
	self.node_list["time_countdown"]:SetActive(false)
	self:ShowMainEndTime(true)
	local cur_grade_type = EternalNightWGData.Instance:GetCurGradeType() or 1
	if cur_grade_type == EternalNightWGData.GradeType.Snatch then 
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
	end
end

--还原
function EternalNightTaskView:ReleaseTaskPanel()
	MainuiWGCtrl.Instance:SetTaskContents(true)
	MainuiWGCtrl.Instance:SetOtherContents(false)

	if self.node_list["eternal_night_task"] then
		self.node_list["eternal_night_task"].gameObject.transform:SetParent(self.root_node_transform, false)
	end

	-- if self.node_list["flush_map_btn"] then
	-- 	self.node_list["flush_map_btn"].gameObject.transform:SetParent(self.root_node_transform, false)
	-- end

	if self.node_list["equip_btn"] then
		self.node_list["equip_btn"].gameObject.transform:SetParent(self.root_node_transform, false)
	end

	if self.node_list["rank_btn"] then
		self.node_list["rank_btn"].gameObject.transform:SetParent(self.root_node_transform, false)
	end

	if self.node_list["reward_btn"] then
		self.node_list["reward_btn"].gameObject.transform:SetParent(self.root_node_transform, false)
	end

	self:CancelTween()
end

function EternalNightTaskView:GetEquipBtnNode()
	if self.node_list.equip_btn then
		return self.node_list.equip_btn
	end
end

function EternalNightTaskView:OnFlush()
	local live_num = EternalNightWGData.Instance:GetLiveNum()
	local player_info = EternalNightWGData.Instance:GetPlayerInfoBySelfId()
	local self_info = EternalNightWGData.Instance:GetSelfInfo()
	local rank = player_info and player_info.rank or 0
	local add_exp = self_info and self_info.add_exp or 0
	add_exp = CommonDataManager.ConverNumber(add_exp)
	local score = player_info and player_info.score or 0
	local relive_times = player_info and player_info.relive_times or 0
	local total_reward_cfg = EternalNightWGData.Instance:GetTotalScoreReward(score)
	local total_score = total_reward_cfg.score or 0
	self.node_list["shengyu_text"].text.text = string.format(Language.EternalNight.ShengYuStr,live_num)
	self.node_list["rank_text"].text.text = string.format(Language.EternalNight.MyRankStr,rank)
	self.node_list["exp_text"].text.text = string.format(Language.EternalNight.GetExpStr,add_exp)
	self.node_list["score_text"].text.text = string.format(Language.EternalNight.ScoreStr,score,total_score)

	local reward_item_cfg = {}
	if total_reward_cfg.reward_item then
		for i=0,#total_reward_cfg.reward_item do
			table.insert(reward_item_cfg,total_reward_cfg.reward_item[i])
		end
	end
	self.reward_list:SetDataList(reward_item_cfg)
	local other_cfg = EternalNightWGData.Instance:GetOtherCfg()
	local cur_grade_type = EternalNightWGData.Instance:GetCurGradeType() or 1
	if cur_grade_type == EternalNightWGData.GradeType.Eliminate then
		local finals_max_relive_times = other_cfg.finals_max_relive_times
		relive_times = finals_max_relive_times - relive_times
		local relive_times_color = relive_times > 0 and COLOR3B.D_GREEN or COLOR3B.D_RED
		self.node_list["life_num"].text.text = string.format(Language.EternalNight.ShengYuLife,relive_times_color,relive_times)
	else
		self.node_list["life_num"].text.text = Language.EternalNight.ShengYuLifeNot
	end

	local grade_name = Language.EternalNight.GradeTypeName[cur_grade_type]
	--MainuiWGCtrl.Instance:SetFBNameState(true, grade_name)
	self.node_list["time_grade_text"].text.text = grade_name

	local monster_list = EternalNightWGData.Instance:GetMonsterList()

	self.node_list["boss_time"]:SetActive(false)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if CountDownManager.Instance:HasCountDown("eternal_night_boss") then
		CountDownManager.Instance:RemoveCountDown("eternal_night_boss")
	end

	local show_to_boss_btn = false
	for i,v in ipairs(monster_list) do
		local lerp_time = v.refresh_time - server_time

		if ((v.refresh_time <= server_time) and v.dead_flag == 0) or ((v.refresh_time > server_time) and (lerp_time <= other_cfg.boss_show_time)) then
			show_to_boss_btn = true
			break
		end
	end

	for i,v in ipairs(monster_list) do
		if v.refresh_time > server_time then
			local time = v.refresh_time
			local lerp_time = v.refresh_time - server_time
			self.node_list["boss_laixi_btn"]:SetActive(lerp_time <= other_cfg.boss_show_time)
			-- self.node_list["boss_laixi_btn_2"]:SetActive(lerp_time <= other_cfg.boss_show_time)
			self.can_click_boss_btn = lerp_time <= other_cfg.boss_show_time
			self:UpdateTimeCallBack(server_time,time)
			CountDownManager.Instance:AddCountDown("eternal_night_boss", BindTool.Bind(self.UpdateTimeCallBack,self), BindTool.Bind(self.CompleteTimeCallBack,self), time, nil,1)
			break
		else
			self.can_click_boss_btn = true
		end

		self:FlushMonsterMap()
	end

	local is_show_boss_laixi_btn = not IsEmptyTable(monster_list) and cur_grade_type ~= EternalNightWGData.GradeType.Eliminate and self.can_click_boss_btn
	self.node_list["boss_laixi_btn"]:SetActive(is_show_boss_laixi_btn)
	self.node_list["boss_laixi_btn_2"]:SetActive(is_show_boss_laixi_btn and show_to_boss_btn)

	self:FlushMapBtn()
end

function EternalNightTaskView:UpdateTimeCallBack(now_time, total_time)
	local lerp_time = total_time - now_time
	lerp_time = math.ceil(lerp_time)
	local time_str = TimeUtil.FormatSecond(lerp_time, 2)
	local other_cfg = EternalNightWGData.Instance:GetOtherCfg()
	if lerp_time <= other_cfg.boss_show_time then
		-- self.node_list["boss_btn"]:SetActive(true)
		self:FlushMonsterMap()
		if not self.play_boss_anim then
			self:PlayBossAnim()
			self.play_boss_anim =true
		end
		self.can_click_boss_btn = true
	else
		self.can_click_boss_btn = false
	end
	self.node_list["boss_time"].text.text = string.format(Language.EternalNight.BossCountDown, lerp_time)
	self.node_list["boss_time"]:SetActive(true)
end

function EternalNightTaskView:CompleteTimeCallBack()
	-- self.node_list["boss_time"].text.text = Language.Boss.HasRefresh
	self.node_list["boss_time"]:SetActive(false)
	self.node_list["boss_time"].text.text = ""
	self.play_boss_anim = false
end

function EternalNightTaskView:ClickRankBtn()
	local cur_grade_type = EternalNightWGData.Instance:GetCurGradeType() or 1
	if cur_grade_type == EternalNightWGData.GradeType.Eliminate then
		ViewManager.Instance:Open(GuideModuleName.EternalNightEliminateRankView)
	else
		ViewManager.Instance:Open(GuideModuleName.EternalNightRankView)
	end
end

function EternalNightTaskView:ClickRewardBtn()
	ViewManager.Instance:Open(GuideModuleName.EternalNightRewardView)
end

function EternalNightTaskView:ClickBossBtn()

	if not self.can_click_boss_btn then
		local str = string.format(Language.EternalNight.FlushBossTimeTip,self.node_list["boss_time"].text.text)
		SysMsgWGCtrl.Instance:ErrorRemind(str)
		return
	end

	local cur_scene_id = Scene.Instance:GetSceneId()
	local scene_id = EternalNightWGData.Instance:GetMonsterSceneId()

	local goto_fun = function ()
		local flag, str = Scene.Instance:CheckCanChangeScene()
		if not flag then
			SysMsgWGCtrl.Instance:ErrorRemind(str)
			return
		end
		local monster_list = EternalNightWGData.Instance:GetMonsterList()
		if not IsEmptyTable(monster_list) then
			local scene_id = Scene.Instance:GetSceneId()
			for k,v in ipairs(monster_list) do
				GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
				GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
			        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			    end)
				GuajiWGCtrl.Instance:MoveToPos(scene_id, v.x, v.y,0,nil,nil, nil, nil, nil, true)
			    break
			end
		end
	end

	if cur_scene_id == scene_id then
		goto_fun()
	else
		local cfg = EternalNightWGData.Instance:GetMapCfgBySceneId(scene_id)
		local scene_name = cfg.name or ""
		local str = string.format(Language.EternalNight.FlushBossTip,scene_name)
		TipWGCtrl.Instance:OpenAlertTips(str, function ()
			local room_info = EternalNightWGData.Instance:GetSceneRoomInfo(scene_id)
			if room_info then
				local room_key = room_info.sub_room_key
				EternalNightWGCtrl.Instance:SendCSEternalNightEnterSubRoom(room_key)
				EternalNightWGData.Instance:SetGoToBossFun(goto_fun)
			end
		end)
	end
end

function EternalNightTaskView:ClickEquipBtn()
	ViewManager.Instance:Open(GuideModuleName.EternalNightEquipView)
end

function EternalNightTaskView:FlushMapBtn()
	-- local cur_grade_type = EternalNightWGData.Instance:GetCurGradeType() or 1
	-- if self.node_list["flush_map_btn"] then
	-- 	self.node_list["flush_map_btn"]:SetActive(cur_grade_type ~= EternalNightWGData.GradeType.Eliminate)
	-- end

	local cur_grade_type = EternalNightWGData.Instance:GetCurGradeType() or 1
	if self.node_list["map"] then
		self.node_list["map"]:SetActive(cur_grade_type ~= EternalNightWGData.GradeType.Eliminate)
	end

	if cur_grade_type ~= EternalNightWGData.GradeType.Eliminate then
		-- 场景人数
		for i = 1, 4 do
			local map_cfg = EternalNightWGData.Instance:GetMapCfgByIndex(i)
	
			if map_cfg then
				local role_num = EternalNightWGData.Instance:GetEternalNightSceneRoleNum(map_cfg.scene_id)
				local item = (self.map_btn_list or {})[i]
	
				if item then
					-- item.role_num.text.text = string.format(Language.EternalNight.RoleNumStr, role_num)
					item.role_num.text.text = role_num
				end
			end
		end

		local scene_id = Scene.Instance:GetSceneId()
		local map_cfg = EternalNightWGData.Instance:GetMapCfgBySceneId(scene_id)
		if map_cfg then
			local index = map_cfg.index
			-- for i=1,4 do
			-- 	local item = self.map_btn_list[i]
			-- 	if item then
			-- 		item.btn_hl:SetActive(index == i)
			-- 	end
			-- end
			local map_item = (self.map_btn_list or {})[index]
			if map_item then
				local map_item_pos = map_item.btn.transform.anchoredPosition
				self.node_list["head_root"].transform.anchoredPosition = Vector3(map_item_pos.x + 51, map_item_pos.y, map_item_pos.z)
			end
		end
	end
end

function EternalNightTaskView:FlushMonsterMap()
	self.node_list["boss_laixi_btn"]:SetActive(true)
	local monster_scene_id = EternalNightWGData.Instance:GetMonsterSceneId()
	local monster_scene_map_cfg = EternalNightWGData.Instance:GetMapCfgBySceneId(monster_scene_id)
	if monster_scene_map_cfg then
		local index = monster_scene_map_cfg.index
		local map_item = self.map_btn_list[index]
		if map_item then
			local map_item_pos = map_item.btn.transform.anchoredPosition
			--self.node_list["boss_laixi_btn"].transform.anchoredPosition = map_item.btn.transform.anchoredPosition
			self.node_list["boss_laixi_btn"].transform.anchoredPosition = Vector3(map_item_pos.x - 91, map_item_pos.y + 15, map_item_pos.z)
			--Vector3(self.model_start_pos.x, self.model_start_pos.y, self.model_start_pos.z)
		end
	else
		self.node_list["boss_laixi_btn"]:SetActive(false)

	end
end

function EternalNightTaskView:PlayBossAnim()
	local monster_scene_id = EternalNightWGData.Instance:GetMonsterSceneId()
	local monster_scene_map_cfg = EternalNightWGData.Instance:GetMapCfgBySceneId(monster_scene_id)
	if monster_scene_map_cfg then
		local index = monster_scene_map_cfg.index
		local map_item = self.map_btn_list[index]
		if map_item then
			self.node_list["boss_laixi_anim"]:SetActive(true)
			local boss_laixi_anim = self.node_list["boss_laixi_anim"].transform
			RectTransform.SetAnchoredPositionXY(boss_laixi_anim, -1334, 0)
			local boss_laixi_anim_canvas = self.node_list["boss_laixi_anim"].canvas_group
			boss_laixi_anim_canvas.alpha = 1

			local tween_move1 = boss_laixi_anim:DOAnchorPos(Vector2(0, 0), 0.5)
			local tween_move2 = boss_laixi_anim:DOAnchorPos(Vector2(1334, 0), 0.5)

			self:CancelTween()
			local show_tweener = DG.Tweening.DOTween.Sequence()
			show_tweener:Append(tween_move1)
			show_tweener:AppendInterval(2.5)
			show_tweener:Append(tween_move2)
			show_tweener:Join(boss_laixi_anim_canvas:DoAlpha(1, 0, 0.5))
			show_tweener:SetEase(DG.Tweening.Ease.Linear)
			show_tweener:OnComplete(function()
				self.node_list["boss_laixi_anim"]:SetActive(false)
			end)

			self.enter_play_tween = show_tweener
		end
	end
end

function EternalNightTaskView:CancelTween()
    if self.enter_play_tween then
        self.enter_play_tween:Kill()
        self.enter_play_tween = nil
    end
end

function EternalNightTaskView:ClickFlushMapBtn()
	ViewManager.Instance:Open(GuideModuleName.EternalNightMapView)
end

function EternalNightTaskView:ClickMapBtn(index)
	local flag = Scene.Instance:GetMainRole():IsFightState()
	if flag then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Fight.FightDesc01)
		return
	end
	local cfg = EternalNightWGData.Instance:GetMapCfgByIndex(index)
	if IsEmptyTable(cfg) then return end
	local room_info = EternalNightWGData.Instance:GetSceneRoomInfo(cfg.scene_id)
	if room_info then
		local room_key = room_info.sub_room_key
		EternalNightWGCtrl.Instance:SendCSEternalNightEnterSubRoom(room_key)
	end
end