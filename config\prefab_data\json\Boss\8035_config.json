{"actorController": {"projectiles": [], "hurts": [], "beHurtEffecct": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "hurtEffectName": "", "beHurtNodeName": "", "beHurtAttach": false, "hurtEffectFreeDelay": 0.0, "QualityCtrlList": []}, "actorTriggers": {"effects": [{"triggerEventName": "attack1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "boss8035_attack", "effectAsset": {"BundleName": "effects/prefab/model/boss/8035/boss8035_attack_prefab", "AssetName": "boss8035_attack", "AssetGUID": "31ea4940b1f83ab41bd6e6f94f5a964c", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "root", "isAttach": true, "isRotation": false, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": -1.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "boss8035_skill", "effectAsset": {"BundleName": "effects/prefab/model/boss/8035/boss8035_skill_prefab", "AssetName": "boss8035_skill", "AssetGUID": "bd3bf319553924444a5d0663443e454a", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "root", "isAttach": true, "isRotation": false, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": -1.5, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack1/begin", "triggerDelay": 0.3, "triggerFreeDelay": 0.0, "effectGoName": "eff_hit_lan", "effectAsset": {"BundleName": "effects/prefab/environment/common/eff_hit_lan_prefab", "AssetName": "eff_hit_lan", "AssetGUID": "1ff2b1322cc912a46afc928e440854ca", "IsEmpty": false}, "playerAtTarget": true, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": false, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1_hit", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack1/begin", "triggerDelay": 0.5, "triggerFreeDelay": 0.0, "effectGoName": "eff_hit_lan", "effectAsset": {"BundleName": "effects/prefab/environment/common/eff_hit_lan_prefab", "AssetName": "eff_hit_lan", "AssetGUID": "1ff2b1322cc912a46afc928e440854ca", "IsEmpty": false}, "playerAtTarget": true, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": false, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1_hit2", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack2/begin", "triggerDelay": 1.3, "triggerFreeDelay": 0.0, "effectGoName": "eff_hit_lan", "effectAsset": {"BundleName": "effects/prefab/environment/common/eff_hit_lan_prefab", "AssetName": "eff_hit_lan", "AssetGUID": "1ff2b1322cc912a46afc928e440854ca", "IsEmpty": false}, "playerAtTarget": true, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": false, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2_hit", "playerAtPos": false, "ignoreParentScale": false}], "halts": [], "sounds": [], "cameraShakes": [], "cameraFOVs": [], "sceneFades": [], "footsteps": []}, "actorBlinker": {"blinkFadeIn": 0.0, "blinkFadeHold": 0.0, "blinkFadeOut": 0.0}, "TimeLineList": []}