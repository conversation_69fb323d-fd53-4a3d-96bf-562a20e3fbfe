
TeamClimbTowerSceneLogic = TeamClimbTowerSceneLogic or BaseClass(CommonFbLogic)

function TeamClimbTowerSceneLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
end

function TeamClimbTowerSceneLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
end

function TeamClimbTowerSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	self.old_scene = old_scene_type
	self.new_scene = new_scene_type
	-- XuiBaseView.CloseAllView()
	-- MapWGCtrl.Instance:MapClose()
	-- TeamClimbTowerWGCtrl.Instance:CloseEnterView()
	-- if MainuiWGCtrl.Instance:GetMenuIsShow() then
	-- 	TeamClimbTowerWGCtrl.Instance:CloseInfoView()
	-- else
	-- 	TeamClimbTowerWGCtrl.Instance:OpenInfoView()
	-- end
end

-- 是否是挂机打怪的敌人
function TeamClimbTowerSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:GetType() == SceneObjType.Role then
		return false
	end

	return true
end

function TeamClimbTowerSceneLogic:Out()
	CommonFbLogic.Out(self)
	-- TeamClimbTowerWGCtrl.Instance:CloseInfoView()
end

-- function TeamClimbTowerSceneLogic:OnClickHeadHandler(is_show)
-- 	TeamClimbTowerWGCtrl.Instance:ShowAction(is_show)
-- end

-- function TeamClimbTowerSceneLogic:IsRoleEnemy(target_obj, main_role)
-- 	return true
-- end

function TeamClimbTowerSceneLogic:OpenFbSceneCd()

end
