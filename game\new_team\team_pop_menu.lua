TeamPopMenu = TeamPopMenu or BaseClass(SafeBaseView)
local CELL_HEIGHT = 44

function TeamPopMenu:__init()
	self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_pop_menu")
	self:SetMaskBg(true, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self.view_name = "TeamPopMenu"
	self.callback_func = nil						-- 点击回调
	self.callback_param = nil						-- 点击回调参数
	self.list_data = {}
    self.btn_list = {}
end

function TeamPopMenu:__delete()

end

function TeamPopMenu:ReleaseCallBack()
	self.panel = nil
	self.list_data = {}
	self.callback_param = nil
	self.callback_func = nil

    if nil ~= self.btn_list then
        self.btn_list:DeleteMe()
        self.btn_list = nil
    end
end

function TeamPopMenu:LoadCallBack()
	self.panel = self.node_list.ButtonList

	if self.pos then
		self.panel.rect.anchoredPosition = self.pos
	else
		self.panel.rect.anchoredPosition = Vector2(0,0)
	end

    self.btn_list = AsyncListView.New(TeamButtonCell, self.node_list.ButtonList)
    self.btn_list:SetSelectCallBack(BindTool.Bind1(self.ClickBtnCell, self))
end

function TeamPopMenu:ClickBtnCell(item, cell_index, is_default, is_click)
	if not item or not is_click then
		return
	end

	if self.callback_func then
		self.callback_func(cell_index, nil, self.callback_param)
		self:Close()
	end
end

function TeamPopMenu:OnFlush()
    self.btn_list:SetDataList(self.list_data)
    self:ChangePanelHeight()
end

--改变列表长度
function TeamPopMenu:ChangePanelHeight()
	local item_count = #self.list_data
	local panel_Width = self.panel.rect.rect.width
	local panel_height = CELL_HEIGHT * item_count + (item_count - 1) * 1 + 10
	self.panel.rect.sizeDelta = Vector2(panel_Width, panel_height)
end

function TeamPopMenu:OpenCallBack()

end

function TeamPopMenu:ShowIndexCallBack()

end

function TeamPopMenu:CloseCallBack()
	self.pos = nil
end

function TeamPopMenu:SetMenuData(menu_data)
	self.list_data = menu_data
end

function TeamPopMenu:BindCallBack(callback_func, callback_param)
	self.callback_func = callback_func
	self.callback_param = callback_param
end

function TeamPopMenu:SetPosition(pos)
	self.pos = pos or Vector2(0, 0)
	if self.pos and self.panel then
		self.panel.rect.anchoredPosition = self.pos
	end
end

-------------------TeamButtonCell----------------
TeamButtonCell = TeamButtonCell or BaseClass(BaseRender)
function TeamButtonCell:__init()

end

function TeamButtonCell:__delete()

end

function TeamButtonCell:OnFlush()
	if self.data and self.data ~= "" then
		self.node_list.Text.text.text = self.data or ""
		self.node_list.Button:SetActive(true)
	else
		self.node_list.Button:SetActive(false)
	end
end