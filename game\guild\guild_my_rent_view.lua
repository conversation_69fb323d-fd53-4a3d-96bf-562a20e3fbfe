function GuildView:InitGuildMyRentView()
    if not self.my_rent_list then
        self.my_rent_list = AsyncListView.New(GuildMyRentItemRender, self.node_list.my_rent_list) 
        self.my_rent_list:SetStartZeroIndex(false)
    end

    XUI.AddClickEventListener(self.node_list.my_rent_log_btn, BindTool.Bind(self.OnMyRentLogBtnClick, self))
end

function GuildView:DeleteGuildMyRentView()
    if self.my_rent_list then
		self.my_rent_list:DeleteMe()
		self.my_rent_list = nil
	end
end

function GuildView:ShowGuildMyRentCallBack()
	AssignmentWGCtrl.Instance:SendRentReq(ASSIGN_RENT_OPERATE_TASK.SELF_RENT)
	AssignmentWGCtrl.Instance:SendRentReq(ASSIGN_RENT_OPERATE_TASK.MY_RECORD)
end

function GuildView:FlushGuidMyRentView()
    local data_list = AssignmentWGData.Instance:GetMyRentList()
    self.my_rent_list:SetDataList(data_list)
	self.node_list.no_data:CustomSetActive(data_list and #data_list == 0)
end

function GuildView:OnMyRentLogBtnClick()
    AssignmentWGCtrl.Instance:OpenMyRentLogView()
end
-------------------------------------------GuildRentGridItemRender  start----------------------------------
GuildMyRentItemRender = GuildMyRentItemRender or BaseClass(BaseGridRender)

function GuildMyRentItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
        self.item_cell = nil
	end
	
	self:CleanMyRentTimer()
end

function GuildMyRentItemRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.item_root)
    self.item_cell:SetNeedItemGetWay(false)

	XUI.AddClickEventListener(self.node_list.unrent_btn, BindTool.Bind(self.OnUnrentBtn, self))
end

function GuildMyRentItemRender:OnUnrentBtn()
	AssignmentWGCtrl.Instance:SendRentReq(ASSIGN_RENT_OPERATE_TASK.UNRENT_FASHION, self.data.self_index)
end

function GuildMyRentItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.item_cell:SetData({item_id = self.data.cfg.item_id})
	
    self.node_list.fashion_name.text.text = self.data.cfg.name
	local time_limit = AssignmentWGData.Instance:GetBorrowTimeLimit()
	local total_time = self.data.begin_time_stamp + time_limit - TimeWGCtrl.Instance:GetServerTime()
	total_time = total_time > 0 and total_time or 0
	local format_str = TimeUtil.FormatSecond(total_time)
	self.node_list.tips_text.text.text = string.format(Language.Assignment.MyRentStr, format_str)
	if total_time > 0 then
		self:CleanMyRentTimer()
		self.my_rent_timer = CountDown.Instance:AddCountDown(total_time, 1,
			-- 回调方法
			function(elapse_time, total_time)
				local total_time = self.data.begin_time_stamp + time_limit - TimeWGCtrl.Instance:GetServerTime()
				total_time = total_time > 0 and total_time or 0
				local format_str = TimeUtil.FormatSecond(total_time)
				self.node_list.tips_text.text.text = string.format(Language.Assignment.MyRentStr, format_str)
			end,
			-- 倒计时完成回调方法
			function()
				AssignmentWGCtrl.Instance:SendTaskReq(ASSIGN_RENT_OPERATE_TASK.SELF_RENT)
			end
		)
	end    
end

function GuildMyRentItemRender:CleanMyRentTimer()
    if self.my_rent_timer and CountDown.Instance:HasCountDown(self.my_rent_timer) then
        CountDown.Instance:RemoveCountDown(self.my_rent_timer)
        self.my_rent_timer = nil
    end
end
-------------------------------------------GuildRentGridItemRender  end----------------------------------