ExpPoolView = ExpPoolView or BaseClass(SafeBaseView)

function ExpPoolView:__init()
	self.view_style = ViewStyle.Window
	self.view_layer = UiLayer.Pop
	self.is_tanchu = 1

	self:SetMaskBg(true, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
	self:AddViewResource(0, "uis/view/exp_pool_ui_prefab", "layout_exp_pool")
end

function ExpPoolView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_enter, BindTool.Bind1(self.OnClickGOBtn, self))

	self.node_list.desc_tip.text.text = Language.ExpPool.ExpPoolViewTip
	self.node_list.desc_get_tip.text.text = Language.ExpPool.ExpPoolViewGetTip
end

function ExpPoolView:OnFlush()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local cur_exp = role_vo.vip_extra_role_exp
	local can_up_level, can_up_baifenbi = self:PoolExpCanUpLevel(cur_exp, role_vo.exp, role_vo.level)
	local total_can_up_rate = (can_up_level + can_up_baifenbi) * 100

	local cur_pool_exp = CommonDataManager.ConverExpByThousand(cur_exp + role_vo.exp)--展示当前经验 = 经验池经验 + 角色自身拥有经验
	local end_level = math.min(role_vo.level + can_up_level, 1000)
	local exp_pool_up = RoleWGData.Instance.GetRoleExpCfgByLv(end_level).exp--展示所需经验 = 当前经验升至最后一级所需的经验
	local pool_max_exp= CommonDataManager.ConverExpByThousand(exp_pool_up)

	self.node_list.desc_exp.text.text = cur_pool_exp.."/"..pool_max_exp
	local desc_up = ""

	if can_up_level >= 1 then
		desc_up = string.format(Language.ExpPool.ExpPoolViewUplevel, can_up_level)
	else
		local can_up_1 = string.format("%.2f",can_up_baifenbi)
		local can_up_2 = can_up_1 * 100
		if can_up_2 == 100 then 
			can_up_2 = 99
		end

		desc_up = string.format(Language.ExpPool.ExpPoolViewUplevelPro, can_up_2)
	end

	self.node_list.desc_up_level.text.text = desc_up

	local is_get_extra_exp = ExpPoolWGData.Instance:IsGetVipExtraRoleExp()
	if is_get_extra_exp then
		self.node_list["btn_text"].text.text = Language.ExpPool.ExpPoolViewIsGetReward
		self.node_list["btn_enter_red"]:SetActive(false)
		self.node_list["desc_get_tip"]:SetActive(false)
	else
		local role_vip = VipWGData.Instance:GetRoleVipLevel()
		local is_vip = VipWGData.Instance:IsVip()

		if is_vip and role_vip >= 6 then
			self.node_list["btn_text"].text.text = Language.ExpPool.Reward
			self.node_list["btn_enter_red"]:SetActive(cur_exp > 0)
			self.node_list["desc_get_tip"]:SetActive(false)
		else
			self.node_list["btn_text"].text.text = Language.ExpPool.GoGoGo
			self.node_list["btn_enter_red"]:SetActive(false)
			self.node_list["desc_get_tip"]:SetActive(true)
		end
	end

	-- local vo = GameVoManager.Instance:GetMainRoleVo()
	-- local max_exp = RoleWGData.Instance.GetRoleExpCfgByLv(vo.level).exp
	-- local val = vo.exp / max_exp
	-- self.node_list.ExpInfo.slider.value = val

	-- --经验池上限     改成了角色当前等级的经验上限
	-- local vo = GameVoManager.Instance:GetMainRoleVo()
	-- --当前存储经验值
	-- local cur_exp = ExpPoolWGData.Instance:PoolExp()
	-- --进度条
	-- -- self.node_list["glass_slider"].slider.value = bili
	-- --直升x
	-- local can_up_level, can_up_baifenbi = ExpPoolWGCtrl.Instance:PoolExpCanUpLevel(cur_exp)
	-- local total_can_up_rate = (can_up_level + can_up_baifenbi) * 100
	-- --转化为亿或者万
	-- local cur_pool_exp = CommonDataManager.ConverExpByThousand(cur_exp + vo.exp)--展示当前经验 = 经验池经验 + 角色自身拥有经验
	-- local end_level = math.min(vo.level + can_up_level, 1000)
	-- local exp_pool_up = RoleWGData.Instance.GetRoleExpCfgByLv(end_level).exp--展示所需经验 = 当前经验升至最后一级所需的经验
	-- local pool_max_exp= CommonDataManager.ConverExpByThousand(exp_pool_up)
	-- --百分比
	-- self.node_list["baifenbi_text"].text.text = math.floor(total_can_up_rate) .. "%"
	-- self.node_list["bili_text"].text.text = cur_pool_exp.."/"..pool_max_exp
	
	-- if can_up_level >= 1 then 
	-- 	self.node_list["up_exp_txt1"]:SetActive(true)
	-- 	self.node_list["up_exp_txt2"]:SetActive(false)
	-- 	self.node_list["up_txt1"].text.text = can_up_level
	-- else
	-- 	self.node_list["up_exp_txt1"]:SetActive(false)
	-- 	self.node_list["up_exp_txt2"]:SetActive(true)
	-- 	local can_up_1 = string.format("%.2f",can_up_baifenbi)
	-- 	local can_up_2 = can_up_1 * 100
	-- 	if can_up_2 == 100 then 
	-- 		can_up_2 = 99
	-- 	end
	-- 	self.node_list["up_txt2"].text.text = can_up_2.."%"
	-- end
	-- --按钮状态
	-- --不可领
	-- local role_vip = VipWGData.Instance:GetRoleVipLevel()
	-- --vip是否生效
	-- local is_vip = VipWGData.Instance:IsVip()
	-- -- if cur_exp >= 0 then
	-- 	--是真的vip,没失效,还大于4
	-- 	if is_vip and role_vip >= 6 then
	-- 		self.node_list["btn_text"].text.text = Language.ExpPool.Reward
	-- 		self.node_list["btn_enter_red"]:SetActive(cur_exp > 0)
	-- 		self.node_list["vip_text2"]:SetActive(false)
	-- 	else
	-- 		self.node_list["btn_text"].text.text = Language.ExpPool.GoGoGo
	-- 		self.node_list["btn_enter_red"]:SetActive(false)
	-- 		self.node_list["vip_text2"]:SetActive(true)
	-- 	end
end

function ExpPoolView:OnClickGOBtn()
	local is_get_extra_exp = ExpPoolWGData.Instance:IsGetVipExtraRoleExp()
	if is_get_extra_exp then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ExpPool.ExpPoolViewIsGetReward)
		return
	end

	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	local is_vip = VipWGData.Instance:IsVip()
	if is_vip and role_vip >= 6 then
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local cur_exp = role_vo.vip_extra_role_exp

		if cur_exp > 0 then
			--发送领取协议
			ExpPoolWGCtrl.Instance:CSGetVipRoleExpPool()
			--延时关闭
			GlobalTimerQuest:AddDelayTimer(function ()
				self:Close()
			end,1.5)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.ExpPool.ExpNil)
		end
	else
		ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_vip, "vip_level", {vip_level = 6})
		self:Close()
	end

	-- local text = self.node_list["btn_text"].text.text
	-- local cur_exp = ExpPoolWGData.Instance:PoolExp()
	-- if text == Language.ExpPool.Reward then
	-- 	if cur_exp > 0 then
	-- 		RoleWGData.SetRolePlayerPrefsInt("exp_pool_tanchuang", self.is_tanchu)
	-- 		local bundle_name, asset_name = ResPath.GetEffectUi("UI_jinyanqiu_baodian")
	-- 		--播放炸开特效
	-- 		EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list["glass_slider"].transform,
	-- 													2,nil,nil,nil,nil )
	-- 		--发送领取协议
	-- 		ExpPoolWGCtrl.Instance:CSGetVipRoleExpPool()
	-- 		--延时关闭
	-- 		GlobalTimerQuest:AddDelayTimer(function ()
	-- 			self:Close()
	-- 		end,1.5)
	-- 	else
	-- 		SysMsgWGCtrl.Instance:ErrorRemind(Language.ExpPool.ExpNil)
	-- 		return
	-- 	end
	-- else
	-- 	ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_vip, "vip_level", {vip_level = 6})
	-- end
end

function ExpPoolView:PoolExpCanUpLevel(pool_exp, role_exp, role_level)
	--角色可用来升级的经验
	local sum_pool = pool_exp + role_exp
	--当前等级角色升级所需经验
	local max_exp

	--可升级别
	local can_up_exp
	for i = 0, RoleWGData.GetRoleMaxLevel() do
		local level = role_level + i

		if level > RoleWGData.GetRoleMaxLevel() then
			level = RoleWGData.GetRoleMaxLevel()
		end

		local cfg = RoleWGData.Instance.GetRoleExpCfgByLv(level)

		if cfg then
			max_exp = cfg.exp
			can_up_exp = sum_pool/max_exp
			if sum_pool - max_exp >= 0 then
				sum_pool = sum_pool - max_exp 
			else
				return i, can_up_exp
			end
		else
			break
		end
	end
	
	return 0, 0 
end



-- 功能开启显示

-- 能领取  vip 》 6   exp 》 0   flag 没领取

-- 消失  领取后消失
