require("game/need_gold_gift/need_gold_gift_view")

-- 特推宝箱
NeedGoldGiftWGCtrl = NeedGoldGiftWGCtrl or BaseClass(BaseWGCtrl)

function NeedGoldGiftWGCtrl:__init()
	if NeedGoldGiftWGCtrl.Instance ~= nil then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[NeedGoldGiftWGCtrl] attempt to create singleton twice!")
		return
	end

	NeedGoldGiftWGCtrl.Instance = self
	self.view = NeedGoldGiftView.New(GuideModuleName.NeedGoldGiftView) 								-- 种子背包面板

	-- self.bind_func = BindTool.Bind1(self.ItemChangeCallBack, self)
	self:RegisterAllProtocols()
	-- ItemWGData.Instance:NotifyDataChangeCallBack(self.bind_func)
end

function NeedGoldGiftWGCtrl:__delete()
	self.view:DeleteMe()
	self.view = nil

	-- ItemWGData.Instance:UnNotifyDataChangeCallBack(self.bind_func)
	NeedGoldGiftWGCtrl.Instance = nil
end

function NeedGoldGiftWGCtrl:RegisterAllProtocols()
end


function NeedGoldGiftWGCtrl:ItemChangeCallBack(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	-- 暂时屏蔽特推宝箱
	-- if ItemWGData.Instance:IsNeedGoldGift(change_item_id) and new_num and old_num then
	-- 	if new_num > old_num then
	-- 		ViewManager.Instance:Open(GuideModuleName.NeedGoldGiftView, nil, "all", {select_item_id = change_item_id})
	-- 	elseif new_num < old_num then
	-- 		ViewManager.Instance:FlushView(GuideModuleName.NeedGoldGiftView)
	-- 	end
	-- end
end