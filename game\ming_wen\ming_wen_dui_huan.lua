MingWenView = MingWenView or BaseClass(SafeBaseView)

function MingWenView:InitDuiHuan()
	if not self.posy_duihuan_list then
        self.posy_duihuan_list = AsyncBaseGrid.New()
		local bundle = "uis/view/ming_wen_ui_prefab"
		local asset = "duihuan_cell"
        self.posy_duihuan_list:CreateCells({col = 5, change_cells_num = 1, list_view = self.node_list["duihuan_list"],
		assetBundle = bundle, assetName = asset, itemRender = MingWenDuiHuanCell})
		self.posy_duihuan_list:SetStartZeroIndex(false)
    end
	self.node_list["coin_btn"].button:AddClickListener(BindTool.Bind(self.ClickSuiPian,self))
end

function MingWenView:DeleteDuiHuan()

end

function MingWenView:ReleaseDuiHuan()
	if self.posy_duihuan_list then
		self.posy_duihuan_list:DeleteMe()
		self.posy_duihuan_list = nil
	end
end

function MingWenView:FlushDuiHuanList()
	local data = MingWenWGData.Instance:GetDuiHuanList()
	self.posy_duihuan_list:SetDataList(data)
end

function MingWenView:ReFlushDuiHuanList()
	self:FlushDuiHuanList()
end

function MingWenView:FlushDuiHuanBottomPanel()
	local sui_pian = MingWenWGData.Instance:GetMingWenSuiPian()
	local pass_level = MingWenWGData.Instance:GetPassLevel()
	self.node_list["duihuan_xinjing_num"].text.text = sui_pian
	self.node_list["pass_level_desc"].text.text = string.format(Language.MingWenView.PassDesc1, pass_level)
end

function MingWenView:FlushDuiHuan()
	self:ReFlushDuiHuanList()
	self:FlushDuiHuanBottomPanel()
end

function MingWenView:ClickSuiPian()
	local data = {}
	data.item_id = COMMON_CONSTS.VIRTUAL_ITEM_90624
	TipWGCtrl.Instance:OpenItem(data)
end

MingWenDuiHuanCell = MingWenDuiHuanCell or BaseClass(BaseRender)

function MingWenDuiHuanCell:LoadCallBack()
	self.item_list = {}
	self.node_list["duihuan_btn"].button:AddClickListener(BindTool.Bind(self.OnClickDuiHuan, self))
	self.node_list["item_btn"].button:AddClickListener(BindTool.Bind(self.OnClickItem, self))
	--self.node_list["duihuan_icon"].button:AddClickListener(BindTool.Bind(self.OnClickIcon,self))
	--self.item_list[i] = ItemCell.New(self.node_list["duihuan_item"..i])
end

function MingWenDuiHuanCell:__delete()

end

function MingWenDuiHuanCell:OnClickIcon()
	local data = {}
	data.item_id = COMMON_CONSTS.VIRTUAL_ITEM_90624
	TipWGCtrl.Instance:OpenItem(data)
end

function MingWenDuiHuanCell:OnFlush()
	if IsEmptyTable(self.data) then return end
		local sui_pian = MingWenWGData.Instance:GetMingWenSuiPian()
		local pass_level = MingWenWGData.Instance:GetPassLevel()
	if self.data then
		self.node_list["duihuan_cell"]:SetActive(true)
		local item_data = {}
		item_data.item_id = self.data.itemid
		item_data.num = 1
		--self.item_list[i]:SetData(item_data)
		local cfg = ItemWGData.Instance:GetItemConfig(self.data.itemid)
		if nil == cfg then
			self.node_list["duihuan_cell"]:SetActive(false)
		else
			self.node_list["duihuan_name"].text.text = ToColorStr(cfg.name,ITEM_COLOR[cfg.color])
			local attr_type,attr_value,add_value = MingWenWGData.Instance:GetEquipPosyAttrByItemId(item_data.item_id)
			local name_list,is_pre = MingWenWGData.Instance:GetAttributeNameListByType(attr_type)
			--self.node_list["icon_bg"]:SetActive(not IsEmptyTable(name_list))
			--local scale, pos_x, pos_y = 0
			if not IsEmptyTable(name_list) then
				-- pos_x = -210
				-- pos_y = 1
				-- scale = 1.25
				if is_pre[1] then
					self.node_list["duihuan_attr"].text.text = name_list[1] .. ToColorStr("  "..(tonumber(attr_value[1])/100).."%",COLOR3B.DEFAULT_NUM)
				else
					self.node_list["duihuan_attr"].text.text = name_list[1] .. ToColorStr("  "..attr_value[1],COLOR3B.DEFAULT_NUM)
				end
			else
				-- pos_x = -210
				-- pos_y = 0
				-- scale = 1
				--获得经验值也要改成绿色  去掉所有加号
				self.node_list["duihuan_attr"].text.text = cfg.description
				--self.node_list["duihuan_attr"..i].text.text = cfg.name .. "  " .. ToColorStr(cfg.value,COLOR3B.GREEN)
			end

			--self.node_list["duihuan_item"..i].transform.anchoredPosition = Vector2(pos_x, pos_y)
			--self.node_list["duihuan_item"..i].transform.localScale = Vector3(scale, scale, scale)
			local color = sui_pian > self.data.price and COLOR3B.C2 or COLOR3B.C3
			self.node_list["duihuan_price"].text.text = ToColorStr(self.data.price,color)

			--XUI.SetGraphicGrey(self.node_list["duihuan_btn"..i], self.data[i].price > sui_pian)
			local is_limite,need_level = MingWenWGData.Instance:GetPosyIsLimite(self.data.itemid)
			if not is_limite then
				self.node_list["duihuan_condition"]:SetActive(false)
				self.node_list["duihuan_btn"]:SetActive(true)
				self.node_list["price"]:SetActive(true)
			else
				self.node_list["duihuan_condition"]:SetActive(true)
				self.node_list["duihuan_btn"]:SetActive(false)
				self.node_list["price"]:SetActive(false)
				self.node_list["duihuan_condition"].text.text = string.format(Language.MingWenView.HeChengCondition,need_level)
			end

			local bundle, asset = ResPath.GetItem(cfg.icon_id)
			self.node_list["duihuan_item"].image:LoadSpriteAsync(bundle, asset,function ()
				self.node_list["duihuan_item"].image:SetNativeSize()
			end)

			--local need_show_bg = cfg.is_stuff ~= 16 --16是铭文背包
			--self.node_list["duihuan_icon_bg"]:SetActive(need_show_bg)
			--if need_show_bg then
				local bundle1,asset1 = ResPath.GetCommonImages("a3_ty_wpk_"..cfg.color)
				self.node_list["duihuan_icon_bg"].image:LoadSpriteAsync(bundle1, asset1,function ()
					self.node_list["duihuan_icon_bg"].image:SetNativeSize()
				end)
			--end

		end
	else
		self.node_list["duihuan_cell"]:SetActive(false)
		self.node_list["duihuan_attr"].text.text = ""
	end
end

function MingWenDuiHuanCell:FlushFenJieCellHL()

end

function MingWenDuiHuanCell:OnClickDuiHuan()
	local data = {}
	data.index = self.data.index
	data.item_id = self.data.itemid
	data.price = self.data.price
	MingWenWGCtrl.Instance:OpenMingWenConvertTipsView(data)
end

function MingWenDuiHuanCell:OnClickItem()
	if self.data and self.data and self.data.itemid then
		local data = {}
		data.item_id = self.data.itemid
		data.num = 1
		TipWGCtrl.Instance:OpenItem(data)
	end
end