require("game/festival_activity/festival_exchange_shop/festival_exchange_shop_wg_data")
require("game/festival_activity/festival_exchange_shop/festival_exchange_shop_item")

FestivalExchangeShopWGCtrl = FestivalExchangeShopWGCtrl or BaseClass(BaseWGCtrl)

function FestivalExchangeShopWGCtrl:__init()
	if FestivalExchangeShopWGCtrl.Instance ~= nil then
		ErrorLog("[FestivalExchangeShopWGCtrl] Attemp to create a singleton twice !")
	end
	FestivalExchangeShopWGCtrl.Instance = self

	self.data = FestivalExchangeShopWGData.New()

	self:RegisterAllProtocols()
end

function FestivalExchangeShopWGCtrl:__delete()
	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	FestivalExchangeShopWGCtrl.Instance = nil
end

function FestivalExchangeShopWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCFestivalConvertInfo, "OnSCFestivalConvertInfo")

	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))

end

function FestivalExchangeShopWGCtrl:MainuiOpenCreate()
	-- RemindManager.Instance:Fire(RemindName.ExchangeShop)n
	self:SendOperExchange(0)
end

function FestivalExchangeShopWGCtrl:ItemDataCallBack()

end

function FestivalExchangeShopWGCtrl:OnDayChange()
	self.data:CheckPeriod()
end

function FestivalExchangeShopWGCtrl:OnSCFestivalConvertInfo(protocol)
    -- print_error("protocol",protocol)
	if protocol.cur_act_period == 0 then
		return
	end
	self.data:SaveData(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.FestivalActivityView, TabIndex.festival_activity_2265)
	RemindManager.Instance:Fire(RemindName.Festival_ExchangeShop)
end

function FestivalExchangeShopWGCtrl:SendOperExchange(opera_type, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
	protocol.rand_activity_type = ACTIVITY_TYPE.FESTIVAL_ACT_CONVERT_SHOP
	protocol.opera_type = opera_type
	protocol.param_1 = param1 or 0
	protocol.param_2 = 0
	protocol.param_3 = 0
	protocol:EncodeAndSend()
end
