---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON>Lua)
--- Created by jf.
--- DateTime: 2019/9/18 11:51
---
--报名进入界面
TeamBaoMingEnterView = TeamBaoMingEnterView or BaseClass(SafeBaseView)
function TeamBaoMingEnterView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
    self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_baoming_enter")
end

function TeamBaoMingEnterView:ReleaseCallBack()
    if  self.member_info_list ~= nil then
        for i, v in pairs(self.member_info_list) do
            v:DeleteMe()
        end
        self.member_info_list = nil
    end
    if self.day_count_change_event then
		GlobalEventSystem:UnBind(self.day_count_change_event)
		self.day_count_change_event = nil
	end
    if self.alert_baoming then
        self.alert_baoming:DeleteMe()
        self.alert_baoming = nil
    end
    if self.bind_pipei_event then
        GlobalEventSystem:UnBind(self.bind_pipei_event)
        self.bind_pipei_event = nil
    end
end

function TeamBaoMingEnterView:LoadCallBack()
    self.node_list["title_view_name"].text.text = Language.NewTeam.BaoMingEnterViewName
	self:SetSecondView(nil, self.node_list["size"])
    XUI.AddClickEventListener(self.node_list["btn_pingtai"], BindTool.Bind(self.OnClickPingTai, self))
    XUI.AddClickEventListener(self.node_list["btn_enter"], BindTool.Bind(self.OnClickEnter, self))
    XUI.AddClickEventListener(self.node_list["btn_exp_rule"], BindTool.Bind(self.OnClickExpRule, self))
    XUI.AddClickEventListener(self.node_list["btn_add_times"], BindTool.Bind(self.OnClickAddTimes, self))
    XUI.AddClickEventListener(self.node_list["btn_state"], BindTool.Bind(self.OnClickMarryState, self))
    XUI.AddClickEventListener(self.node_list["btn_marryadd_times"], BindTool.Bind(self.OnClickMarryAddTimes, self))
    XUI.AddClickEventListener(self.node_list["btn_loveradd_times"], BindTool.Bind(self.OnClickMarryLoverAddTimes, self))
    self.day_count_change_event = GlobalEventSystem:Bind(OtherEventType.DAY_COUNT_CHANGE, BindTool.Bind(self.OnDayCountChange, self))
    if self.alert_baoming == nil then
        self.alert_baoming = Alert.New()
    end
end

function TeamBaoMingEnterView:OnDayCountChange(day_count_id)
	self:Flush()
end

function TeamBaoMingEnterView:CloseCallBack()
    self:CloseAlert()
end

function TeamBaoMingEnterView:CloseAlert()
    if self.alert_baoming and self.alert_baoming:IsOpen() then
        self.alert_baoming:Close()
    end
end

function TeamBaoMingEnterView:ShowIndexCallBack()
   self:Flush()
end

function TeamBaoMingEnterView:OnClickPingTai()
    ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_pingtai)
end

function TeamBaoMingEnterView:OnClickEnter()
    if SocietyWGData.Instance:GetIsInTeam() == 1 then
		if SocietyWGData.Instance:GetIsTeamLeader() == 1 then
            local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
            local member_count = SocietyWGData.Instance:GetTeamMemberCount()
            if member_count == 1 then
                local cur_enter_count, total_count =  NewTeamWGData.Instance:GetTimesByTeamType(team_type)
                if (total_count - cur_enter_count) <= 0 then
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.NotTimesEnter)
                    return
                end
            end
            
            local func = function ()
                NewTeamWGData.Instance:ClearRoomInfo()
                NewTeamWGCtrl.Instance:StartOrCancelTeamFB(0)
                --end
                if team_type == GoalTeamType.Exp_DuJieXianZhou or team_type == GoalTeamType.Exp_FuMoZhanChuan then
                    Scene.Instance:ClearJumpTime()
                end

                if FuBenPanelWGData.Instance:GetFuBenJiangHuGuideFlag() then
                    local time = TimeWGCtrl.Instance:GetServerTime()
                    local guide_cfg = FunctionGuide.Instance:GetGuideCfg()[33]
                    if guide_cfg then
                        FuBenPanelWGData.Instance:SetFuBenJiangHuGuideFlag(guide_cfg.trigger_param .. "|" .. time .. "|33")
                    end
                end
            end
            if member_count < 3 and team_type ~= GoalTeamType.QingYuanFb then
                local exp_add = NewTeamWGData.Instance:GetExpAdd() * 10
                local now_goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, fb_mode)
                self.alert_baoming:SetLableString(Language.TeamFb.DirectEnter)
                self.alert_baoming:SetOkFunc(func)
                self.alert_baoming:Open()
            else
                func()
            end
            --local cur_enter_count, total_count = NewTeamWGData.Instance:GetTimesByTeamType(team_type)
            --local remain_count = total_count - cur_enter_count
            --if remain_count > 0 then
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.NotLeaderTip)
		end
	end
end

function TeamBaoMingEnterView:OnClickExpRule()
    local team_type, team_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
    local now_goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, team_fb_mode)
    local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.NewTeam.EXP_rule_title)
	rule_tip:SetContent(now_goal_info.team_rule or Language.NewTeam.EXP_desc_rule)
end

function TeamBaoMingEnterView:OnClickAddTimes()
    local team_type, team_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	if team_type == GoalTeamType.Exp_DuJieXianZhou then 			-- 渡劫仙舟
        FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.FBCT_WUJINJITAN)
	elseif team_type == GoalTeamType.QingYuanFb then				-- 情缘副本
        NewTeamWGCtrl.Instance:OnClickQingyuanAddTimes()
	elseif team_type == GoalTeamType.YuanGuXianDian then			-- 远古仙殿
		--FuBenPanelWGCtrl.Instance:OpenPetBuy(false, FUBEN_TYPE.HIGH_TEAM_EQUIP)
        --FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.HIGH_TEAM_EQUIP)
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.CanNoBuyTimes)
	--elseif team_type == GoalTeamType.ZhuShenTa then 				-- 诛神塔 --诸神塔不能购买次数
	elseif team_type == GoalTeamType.Exp_FuMoZhanChuan then 		-- 伏魔战船
        FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.LINGHUNGUANGCHANG)
	elseif team_type == GoalTeamType.ManHuangGuDian then 			-- 蛮荒古殿
		--FuBenPanelWGCtrl.Instance:OpenManHuangGuDianBuy()
        FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.FB_MANHUANG_GUDIAN_FB)
	end
end

function TeamBaoMingEnterView:OnFlush(param_t)
    if not self.member_info_list then
        self.member_info_list = {}
    end
    local member_info_list = SocietyWGData.Instance:GetTeamMemberList()
    --print_error("成员个数????? ", #member_info_list, member_info_list)
    for i = 1, 3 do
        if not self.member_info_list[i] then
            self.member_info_list[i] = BaoMingEnterMemberInfoRender.New(self.node_list["info"..i])
        end
        if member_info_list[i] then
            self.member_info_list[i]:SetData(member_info_list[i])
        else
            self.member_info_list[i]:SetData({})
        end
    end
    --界面名字为当前目标名
    local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
    local now_goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, fb_mode)
    self.node_list["title_view_name"].text.text = now_goal_info.team_type_name
    local cur_enter_count, total_count = NewTeamWGData.Instance:GetTimesByTeamType(team_type)
    local remain_count = total_count - cur_enter_count
    local xiezhu_times, total_xiezhu_times = NewTeamWGData.Instance:GetXieZhuTimesByTeamType(team_type)
    local color = remain_count > 0 and COLOR3B.GREEN or COLOR3B.RED
    self.node_list.remain_times.text.text = string.format(Language.NewTeam.RemainRewardTimes, color, remain_count, total_count)
    if remain_count <= 0 then
        self.node_list.exp_add:SetActive(false)
        local is_can_xiezhu = GoalHasXieZhuType[team_type] or false
        self.node_list.xiezhu_times_container:SetActive(is_can_xiezhu)
        if is_can_xiezhu then
            local xiezhu_count = total_xiezhu_times - xiezhu_times --TODO , 获取协助次数
            local color1 = xiezhu_count > 0 and COLOR3B.GREEN or COLOR3B.RED
            self.node_list.xiezhu_times.text.text = string.format(Language.NewTeam.RemainRewardTimes, color1, xiezhu_count, total_xiezhu_times)
            local reward_value = NewTeamWGData.Instance:GetXieZhuRewardValue(team_type)
            local item_id = XieZhuRewardTeamType[team_type]
            local item_config = ItemWGData.Instance:GetItemConfig(item_id)
            local str = ""
            if xiezhu_count > 0 then
                str = string.format(Language.NewTeam.HasXieZhuWuJiangLi,item_id, ITEM_COLOR[item_config.color], reward_value) --声望
            else
                str = Language.NewTeam.WuXieZhuWuJiangLi
            end
            EmojiTextUtil.ParseRichText(self.node_list.tips.emoji_text, str, 20, COLOR3B.DEFAULT)
        end
    else
        self.node_list.xiezhu_times_container:SetActive(false)
        self.node_list.exp_add:SetActive(true)
        local exp_add = NewTeamWGData.Instance:GetExpAdd() * 10
        self.node_list.exp_add_value.text.text = string.format(Language.NewTeam.EXP_Add, exp_add)
        --self.node_list.tips.text.text = Language.NewTeam.EXP_max_desc
        EmojiTextUtil.ParseRichText(self.node_list.tips.emoji_text, Language.NewTeam.EXP_max_desc, 20, COLOR3B.DEFAULT)
    end
    if team_type == GoalTeamType.QingYuanFb then
       self:SetQingYuanTeamInfo()
       self.node_list["info3"]:SetActive(false)
       self.node_list["marry_container"]:SetActive(true)
       self.node_list["middle_container"]:SetActive(false)
    else
       self.node_list["info3"]:SetActive(true)
       self.node_list["marry_container"]:SetActive(false)
       self.node_list["middle_container"]:SetActive(true)
    end
end

function TeamBaoMingEnterView:GetRemainEnterTimes()
    local remain_enter_times = NewTeamWGData.Instance:GetRemainEnterTimes()
end

function TeamBaoMingEnterView:SetQingYuanTeamInfo()
    local fb_info = MarryWGData.Instance:GetQyFbInfo()
    local other_cfg = MarryWGData.Instance:GetMarryOtherCfg()
    local lover_info = MarryWGData.Instance:GetLoverInfo2()
    local str_1 = 0
    local fb_shenyu_num = 0
    if fb_info then
        local fb_num = other_cfg.fb_free_times_limit + fb_info.self_buy_jion_fb_times + fb_info.total_lover_buy_fb_times
        fb_shenyu_num = fb_num - fb_info.join_fb_times
        local color = fb_shenyu_num > 0 and "#009621" or "#ff0000"
        str_1 = ToColorStr(fb_shenyu_num,color).."/"..fb_num
        local lover_buy_fb_times = fb_info.lover_buy_fb_times or 0
        local lover_shengyu_num = other_cfg.fb_buy_times_limit - lover_buy_fb_times
        local lover_id = RoleWGData.Instance.role_vo.lover_uid
        self.node_list["btn_loveradd_times"]:SetActive(lover_shengyu_num > 0 and lover_id > 0)
    end
    self.node_list["marry_times"].text.text = str_1
    local str_2 = 0
    -- local marry_fb_info = MarryWGData.Instance:GetQyFbSceneInfo()
    --local zhuzhan_num = MarryWGData.Instance:GetXieZhuTimes() -- other_cfg.help_times
    local zhuzhan_num, total_xiezhu_times = NewTeamWGData.Instance:GetXieZhuTimesByTeamType(GoalTeamType.QingYuanFb)
    local zhuzhan_shengyu_num = total_xiezhu_times - zhuzhan_num
    zhuzhan_shengyu_num = zhuzhan_shengyu_num > 0 and zhuzhan_shengyu_num or 0
    local color = zhuzhan_shengyu_num > 0 and "#009621" or "#ff0000"
    str_2 = ToColorStr(zhuzhan_shengyu_num, color).."/"..total_xiezhu_times

    self.node_list["marry_zhuzhan_num"].text.text = str_2
    local str_3 = ""
    if fb_shenyu_num > 0 then
        str_3 = Language.Marry.TeamViewHint1
    elseif zhuzhan_shengyu_num > 0 then
        str_3 = Language.Marry.TeamViewHint2
    else
        str_3 = Language.Marry.TeamViewHint3
    end
    self.node_list["btn_state"]:SetActive(fb_shenyu_num > 0)
    self.node_list["hang_2"]:SetActive(fb_shenyu_num <= 0)
    self.node_list["marry_team_hint"].text.text = str_3

end

function TeamBaoMingEnterView:OnClickMarryState()
    -- local role_tip = RuleTip.Instance
    --if role_tip then
    --    role_tip:SetTitle(Language.Marry.TeamViewMarryTips)
    --    --role_tip:SetContent(Language.Marry.TeamViewMarryState)
    --end
    local team_type, team_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
    local now_goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, team_fb_mode)
    local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.NewTeam.EXP_rule_title)
	rule_tip:SetContent(now_goal_info.team_rule or Language.NewTeam.EXP_desc_rule)
end

function TeamBaoMingEnterView:OnClickMarryAddTimes()
    NewTeamWGCtrl.Instance:OnClickQingyuanAddTimes()
end

function TeamBaoMingEnterView:OnClickMarryLoverAddTimes()
    local lover_id = RoleWGData.Instance.role_vo.lover_uid
    if lover_id > 0 then
        MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_BUY_QINGYUAN_FB_REQ)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.NoLover)
    end
end

-------------------------------------------------------------------------------------------
--- BaoMingEnterMemberInfoRender
-------------------------------------------------------------------------------------------

BaoMingEnterMemberInfoRender = BaoMingEnterMemberInfoRender or BaseClass(BaseRender)
function BaoMingEnterMemberInfoRender:__init()
    self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
end
function BaoMingEnterMemberInfoRender:__delete()
    if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end
function BaoMingEnterMemberInfoRender:OnFlush()
    if not self.data then return end
    local is_empty_data = IsEmptyTable(self.data)

	--没有队员信息，隐藏相关信息
	self.node_list.no_data_hide:SetActive(not is_empty_data and self.data.role_id > 0)
    self.node_list.no_data_active:SetActive(is_empty_data)

    if is_empty_data then return end
    --队长标记
	self.node_list.leader_img:SetActive(self.data.is_leader == 1)
    if self.data.role_id > 0 then
        self:SetName()
        if self.node_list.vip_level then
        --Vip等级
        self.node_list.vip_level:SetActive(self.data.vip_level > 0)
		self.node_list.vip_level.text.text = string.format(Language.NewTeam.VipLevel,  self.data.vip_level)
        end

		--人物等级
		local str = string.format(Language.NewTeam.PTLevel, self.data.level)
		EmojiTextUtil.ParseRichText(self.node_list["role_level"].emoji_text, str, 21, COLOR3B.DEFAULT)

        -- --巅峰符号
        -- if self.node_list.dianfen_img then
        --     local is_vis, level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
        --     self.node_list.dianfen_img:SetActive(is_vis)
        -- end
        
        self:SetHeadCell()
	end
end

function BaoMingEnterMemberInfoRender:SetName()
    local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
    --人物名字
    if GoalCrossType[team_type] then
        --内网无法请求PHP，先设置默认名字
        local temp_name = string.format(Language.WorldServer.ServerDefName, self.data.server_id)
        self.node_list["role_name"].text.text = string.format(Language.NewTeam.NewServerName, temp_name, self.data.name)
    else
        self.node_list.role_name.text.text = self.data.name
    end
end

function BaoMingEnterMemberInfoRender:SetHeadCell()
    local data = {}
    data.role_id = self.data.orgin_role_id
    data.prof = self.data.prof
    data.sex = self.data.sex
    data.fashion_photoframe = self.data.shizhuang_photoframe
    self.head_cell:SetData(data)
end