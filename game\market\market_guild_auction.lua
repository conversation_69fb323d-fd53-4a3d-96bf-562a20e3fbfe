
-- 市场-拍卖-仙盟拍卖
function MarketView:GuildAuctionLoadCallBack()
	self.guild_cur_big_type = nil
	self.guild_cur_sub_type = nil
	self.cur_guild_data_list = {}--当前展示信息
	self.old_guild_show_info = nil
	self.guild_search_name = ""
	self.has_guild = false --是否已加入仙盟

	self.node_list.xx_all_select_btn.button:AddClickListener(BindTool.Bind(self.GuildOnClickAllTypeBtn, self))
	self.node_list.xx_search_btn.button:AddClickListener(BindTool.Bind(self.GuildOnClickSearchBtn, self))
	self.guild_aiction_item_list = AsyncListView.New(MarketAuctionCommonRender, self.node_list.xx_item_list)
	self.node_list.xx_search_input.input_field.onSelect:AddListener(BindTool.Bind(self.OnInputFieldClickOrEndEdit, self, true, MarketViewIndex.XXPM))
	self.node_list.xx_search_input.input_field.onEndEdit:AddListener(BindTool.Bind(self.OnInputFieldClickOrEndEdit, self, false, MarketViewIndex.XXPM))

	--tab页签的箭头显示.
	self.node_list.market_guild_auction_list.scroller.scrollerEndScrolled = BindTool.Bind(self.GuildAuctionScrollerEndScrolled, self)

	self.guild_accor_tab_list = AsyncListView.New(MarketAuctionTabRender, self.node_list["market_guild_auction_list"])
	self.guild_accor_tab_list:SetSelectCallBack(BindTool.Bind1(self.OnClickGuildAccorTabCallBack, self))
	self.guild_accor_tab_list:SetDefaultSelectIndex(nil)
	self.guild_accor_tab_list:SetDataList(MarketWGData.Instance:GetAuctionTabList(AUCTION_INFO_BIG_TYPE.Guild))

	self:GuildOnClickAllTypeBtn()
end

function MarketView:GuildAuctionReleaseCallBack()
	if self.guild_aiction_item_list then
		self.guild_aiction_item_list:DeleteMe()
		self.guild_aiction_item_list = nil
	end

	self.cur_guild_data_list = nil
	self.guild_search_name = nil
	self.old_guild_show_info = nil

	if self.guild_accor_tab_list ~= nil then
		self.guild_accor_tab_list:DeleteMe()
		self.guild_accor_tab_list = nil
	end
end

function MarketView:GuildAuctionShowIndexCallBack()
	self.has_guild = RoleWGData.Instance.role_vo.guild_id > 0
	if not self.has_guild then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotEnterNoGuild)
	end
end

function MarketView:GuildAuctionOnFlush(param_t)
	self.has_guild = RoleWGData.Instance.role_vo.guild_id > 0
	if self.guild_cur_big_type and self.guild_cur_sub_type then
		self:GuildOnClickLeftBtnCallBack(self.guild_cur_big_type, self.guild_cur_sub_type, true)
	else
		self:GuildOnClickAllTypeBtn(true)
	end
end

--tab页签的箭头显示.
function MarketView:GuildAuctionScrollerEndScrolled()
	local val = self.node_list.market_guild_auction_list.scroll_rect.horizontalNormalizedPosition
	self.node_list.market_guild_auction_l_img:SetActive(val ~= 0 and val > 0.1)
	self.node_list.market_guild_auction_r_img:SetActive(val ~= 0 and val < 0.9)
end

function MarketView:FlushGuildAuctionAllPart()

end

function MarketView:OnClickGuildAccorTabCallBack(item)
	if IsEmptyTable(item.data) then
		return
	end

	self.node_list.xx_all_hl:SetActive(false)
	self:GuildOnClickLeftBtnCallBack(item.data.type, item.data.sub_type)
end

function MarketView:GuildOnClickLeftBtnCallBack(big_type, sub_type, force_flush)
	if self.guild_search_name == "" and not force_flush and self.guild_cur_big_type and self.guild_cur_sub_type then
		if self.guild_cur_big_type == big_type and self.guild_cur_sub_type == sub_type then
			return
		end
	else
		self.guild_cur_big_type = big_type
		self.guild_cur_sub_type = sub_type
	end
	self.guild_cur_big_type = big_type
	self.guild_cur_sub_type = sub_type

	--根据类型取得竞拍物品展示配置
	self.cur_guild_data_list = MarketWGData.Instance:GetNewAuctionItemInfoByType(AUCTION_TYPE.Guild, sub_type)

	if not force_flush then-- 普通点击页签,清空搜索状态以及搜索栏
		self.node_list["xx_search_input"].input_field.text = ""
		self.guild_search_name = ""
	end

	if self.guild_search_name ~= "" then-- 还在搜索列表中
		self:GuildOnClickSearchBtn(true)
	else
		self:GuildOnFlushGoodsList(force_flush)
	end
end

--点击全部按钮
function MarketView:GuildOnClickAllTypeBtn(force_flush)
	if self.guild_search_name == "" and not force_flush and self.node_list.xx_all_hl.gameObject.activeSelf then return end
	self.guild_cur_big_type = nil
	self.guild_cur_sub_type = nil

	self.node_list.xx_all_hl:SetActive(true)

	self.cur_guild_data_list = MarketWGData.Instance:GetAllAuctionByType(AUCTION_TYPE.Guild)
	if not force_flush then-- 普通点击页签,清空搜索状态以及搜索栏
		self.node_list["xx_search_input"].input_field.text = ""
		self.guild_search_name = ""
	end

	if self.guild_search_name ~= "" then-- 还在搜索列表中
		self:GuildOnClickSearchBtn(true)
	else
		self:GuildOnFlushGoodsList(force_flush)
	end

	self.guild_accor_tab_list:CancelSelect()
end

--刷新仙盟拍卖商品列表
--specil_flag 特殊需求:不是点击页签刷新时,列表数据有变化,按照原来的列表顺序刷新
function MarketView:GuildOnFlushGoodsList(specil_flag)
	self.node_list.xx_empty_tips:SetActive(false)
	self.node_list.xx_item_list:SetActive(true)
	if self.has_guild and self.cur_guild_data_list and not IsEmptyTable(self.cur_guild_data_list) then
		if specil_flag and self.old_guild_show_info and not IsEmptyTable(self.old_guild_show_info) then
			self.cur_guild_data_list = MarketWGData.Instance:SortAuctionInfoListByOld(self.cur_guild_data_list, self.old_guild_show_info)
		else--点击页签切换时,才按时间重新排序
			-- table.sort(self.cur_guild_data_list, SortTools.KeyLowerSorters("end_time", "index"))
			table.sort(self.cur_guild_data_list, MarketWGData.Instance:AuctionDataListSort())

		end
		self.old_guild_show_info = self.cur_guild_data_list

		self.guild_aiction_item_list:SetDataList(self.cur_guild_data_list)
		if not specil_flag then
			self.guild_aiction_item_list:JumpToTop()
		end
	else
		self.node_list["text_not_cell_tips"].text.text = self.has_guild and Language.Market.Auction_Not_Cell or Language.Common.CanNotEnterNoGuild
		self.node_list.xx_empty_tips:SetActive(true)
		self.node_list.xx_item_list:SetActive(false)
	end
end

--仙盟拍卖--点击搜索按钮
--flush_flag:刷新标志,用于搜索后列表信息变更刷新
function MarketView:GuildOnClickSearchBtn(flush_flag)
	if not self.cur_guild_data_list and IsEmptyTable(self.cur_guild_data_list) then
		return
	end

	if not flush_flag then
		local new_search_name = self.node_list["xx_search_input"].input_field.text
		if self.guild_search_name == new_search_name then
			return
		else--二次搜索:根据当前所在的 左页签类型 进行搜索
			if self.guild_cur_big_type and self.guild_cur_sub_type then
				--根据类型取得竞拍物品展示配置
				self.cur_guild_data_list = MarketWGData.Instance:GetNewAuctionItemInfoByType(AUCTION_TYPE.Guild, self.guild_cur_sub_type)
			else
				self.cur_guild_data_list = MarketWGData.Instance:GetAllAuctionByType(AUCTION_TYPE.Guild)
			end
		end
		self.guild_search_name = new_search_name
		if self.guild_search_name == "" then
			self:GuildAuctionOnFlush()
			return
		end
	end

	local temp_list = {}
	for k, v in pairs(self.cur_guild_data_list) do
		if self:GuildSearchGoodsInfo(v.item_id) then
			table.insert(temp_list, v)
		end
	end
	self.cur_guild_data_list = temp_list
	self:GuildOnFlushGoodsList(flush_flag)
end

--搜索商品信息
function MarketView:GuildSearchGoodsInfo(item_id)
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if not item_cfg then return false end

	if self.guild_search_name and self.guild_search_name ~= "" then
		if not string.find(item_cfg.name, self.guild_search_name) then
			return false
		end
	end
	return true
end
