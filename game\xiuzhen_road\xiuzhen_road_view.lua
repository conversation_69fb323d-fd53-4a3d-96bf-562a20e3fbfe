XiuZhenRoadView = XiuZhenRoadView or BaseClass(SafeBaseView)

local SHOW_MODEL_TYPE = {
	ShenLing = 1,
	Wing = 2,
	Esoterica = 3,
}

function XiuZhenRoadView:__init()
	self.view_style = ViewStyle.Full
	self:SetMaskBg()
	self.view_layer = UiLayer.Normal
	self.is_safe_area_adapter = true
	local bundle_name = "uis/view/xiuzhen_road_ui_prefab"
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
	self:AddViewResource(0, bundle_name, "layout_xiuzhen_road_view")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_top_panel")
end

function XiuZhenRoadView:ReleaseCallBack()
	if self.leiji_list_0 then
		self.leiji_list_0:DeleteMe()
		self.leiji_list_0 = nil
	end
	if self.leiji_list then
		self.leiji_list:DeleteMe()
		self.leiji_list = nil
	end
	if self.btn_list then
		self.btn_list:DeleteMe()
		self.btn_list = nil
	end
	if self.task_list then
		self.task_list:DeleteMe()
		self.task_list = nil
	end
	if self.gift_cell then
		self.gift_cell:DeleteMe()
		self.gift_cell = nil
	end
	if self.pet_cell_item then
		self.pet_cell_item:DeleteMe()
		self.pet_cell_item = nil
	end

	if self.display_model then
		self.display_model:DeleteMe()
		self.display_model = nil
	end

	if self.model1_display then
		self.model1_display:DeleteMe()
		self.model1_display = nil
	end

	if self.model2_display then
		self.model2_display:DeleteMe()
		self.model2_display = nil
	end

	if self.model3_display then
		self.model3_display:DeleteMe()
		self.model3_display = nil
	end

	if CountDownManager.Instance:HasCountDown("xiuzhe_end_time") then
		CountDownManager.Instance:RemoveCountDown("xiuzhe_end_time")
	end

	self:ModelCancelQuest()

	self.cur_select_data = nil
	self.flush_model_time = nil
	self.cur_show_index = nil

	if not IsEmptyTable(self.gift_page_cell_list) then
		for k, v in pairs(self.gift_page_cell_list) do
			v:DeleteMe()
		end
		self.gift_page_cell_list = {}
	end

	if self.gift_panel_loop_quest then
		GlobalTimerQuest:CancelQuest(self.gift_panel_loop_quest)
		self.gift_panel_loop_quest = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	--self.scroll_rect = nil
end

function XiuZhenRoadView:LoadCallBack()
	self.cur_show_index = 1
	self.node_list["btn_buy"].button:AddClickListener(BindTool.Bind(self.OnClickBuyGift, self))
	-- self.node_list["ph_leiji_cell_0"].button:AddClickListener(BindTool.Bind(self.OnClickOpenRewardView, self))
	self.node_list["btn_active_skill"].button:AddClickListener(BindTool.Bind(self.OnClickBtnActiveSkill, self))
	-- self.node_list["btn_lingqu"].button:AddClickListener(BindTool.Bind(self.OnClickLingQuPet, self))

	XUI.AddClickEventListener(self.node_list["model_btn_1"], BindTool.Bind(self.OnClickModelSwitch, self, 1))
	XUI.AddClickEventListener(self.node_list["model_btn_2"], BindTool.Bind(self.OnClickModelSwitch, self, 0))
	for i = 1, 4 do
		XUI.AddClickEventListener(self.node_list["select_btn" .. i], BindTool.Bind(self.ClickSelectBtn, self, i))
	end

	self.node_list["ph_buy_list"]:SetActive(false)
	self.select_btn_type = 1

	self.gift_page_cell_list = {}
	self.gift_page_toggle_list = {}
	self.max_page_num = 3
	self.cur_page_num = 0
	self.move_tween_flage = false
	self.force_jump_page = false
	self.drag_begin_pos_y = 0
	self.drag_end_pos_y = 0
	self.is_draging = false
	self.flush_model_time = 5

	for i = 1, 3 do
		local group_obj = self.node_list["buy_list_content"]
		local obj = ResMgr:Instantiate(self.node_list["gift_page_prefab"].gameObject)
		local obj_transform = obj.transform
		obj_transform:SetParent(group_obj.transform, false)
		self.gift_page_cell_list[i] = XiuZhenGiftPageCell.New(obj)
		self.gift_page_cell_list[i]:SetIndex(i)
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	if self.node_list.btn_close_window_img then
		local bundle, asset = ResPath.GetCommonButton("a3_ty_retutn2")
		self.node_list.btn_close_window_img.image:LoadSprite(bundle, asset)
	end
	self.node_list["title_view_name"].text.text = Language.ViewName.XiuXianYuanMeng
	self.node_list["title_view_name"].text.color = Str2C3b("#CBEFEF")

	local bundle, asset = ResPath.GetRawImagesPNG("a3_wdjx_bg53")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

	self:StartQuest()
	self:AddGiftPagePanelLoop()
end

function XiuZhenRoadView:AddGiftPagePanelLoop()
	self.gift_panel_loop_index = 1
	--self.node_list["gift_page_toggle1"].toggle.isOn = true
	XiuZhenRoadWGData.Instance:SetCanGiftPanelLoop(true)
	if self.gift_panel_loop_quest then
		GlobalTimerQuest:CancelQuest(self.gift_panel_loop_quest)
		self.gift_panel_loop_quest = nil
	end
	self.gift_panel_loop_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.UpdateGifrPagePanelLoop, self), 5)
end

function XiuZhenRoadView:UpdateGifrPagePanelLoop()
	local loop_flag = XiuZhenRoadWGData.Instance:GetCanGiftPanelLoop()
	if not loop_flag then
		if self.gift_panel_loop_quest then
			GlobalTimerQuest:CancelQuest(self.gift_panel_loop_quest)
			self.gift_panel_loop_quest = nil
		end
		return
	end
	self.gift_panel_loop_index = self.gift_panel_loop_index + 1
	if self.gift_panel_loop_index > 3 then
		self.gift_panel_loop_index = 1
	end
end

function XiuZhenRoadView:ShowIndexCallBack()
	self.cur_total_progress = 0
	self.progress = 0
	if self.node_list.ph_leiji_cell_0 then --最高的奖励宝箱
		self.leiji_list_0 = LiJiCellItemRender.New(self.node_list.ph_leiji_cell_0)
	end
	if nil == self.leiji_list then
		self.leiji_list = AsyncListView.New(LiJiCellItemRender, self.node_list["ph_leiji_list"])
		-- self.leiji_list:SetSelectCallBack(BindTool.Bind(self.OnClickOpenRewardView, self))
		self.leiji_list:SetDefaultSelectIndex(-1)
	end

	if nil == self.btn_list then
		self.btn_list = AsyncListView.New(XiuZhenBtnItemRender, self.node_list["ph_btn_list"])
		self.btn_list:SetSelectCallBack(BindTool.Bind1(self.SelecBtnCallBack, self))
	end

	if nil == self.task_list then
		self.task_list = AsyncListView.New(XiuZhenTaskItemRender, self.node_list["ph_task_list"])
	end
	--按钮
	local btn_data = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuBtnInfo()
	self.btn_list:SetDataList(btn_data, 0)
	local index = XiuZhenRoadWGData.Instance:GetCurOpenCanSelect()
	--index = #btn_data - index + 1
	self.btn_list:JumpToIndex(index)
	self.btn_list:SelectIndex(index)

	--当前小页签的任务全部已领取则默认跳转到下一个小页签
	local jump_index = XiuZhenRoadWGData.Instance:GetJumpIndex(btn_data[index].cfg.open_day)
	self.node_list["select_btn" .. jump_index].toggle.isOn = true

	--按钮完成标志
	self:FlushBtnIsFinish()
	self:SetBaseInfo()
	self:SetCurSelectShowInfo()
	self:InitCJTweenState()
end

function XiuZhenRoadView:CloseCallBack()

end

function XiuZhenRoadView:OnFlush(param_t)
	for k, v in pairs(param_t) do
		if k == "all" then
			if v.open_param and tonumber(v.open_param) then
				self.btn_list:SelectIndex(tonumber(v.open_param))
				self.btn_list:JumpToIndex(tonumber(v.open_param))
			end
			self:SetCurSelectShowInfo()
			self:XiuZhenViewAnimation()
		end
	end
end

function XiuZhenRoadView:InitCJTweenState()
	self.do_xiu_zhen_tween = true
	--RectTransform.SetAnchoredPositionXY(self.node_list.ph_btn_list_bg.rect, 321, -520)
	--RectTransform.SetAnchoredPositionXY(self.node_list.all_progress.rect, -357, 6)
end

function XiuZhenRoadView:OnClickModelSwitch(is_left)
	self.cur_show_index = is_left > 0 and math.max(self.cur_show_index - 1, 1) or math.min(self.cur_show_index + 1, 3)
	self:FlushShowModel()
	--self.node_list["model_toggle_" .. index].toggle.isOn = true
end

function XiuZhenRoadView:FlushShowModel()
	self.node_list["ph_display"]:SetActive(SHOW_MODEL_TYPE.ShenLing == self.cur_show_index)
	self.node_list["ph_display_2"]:SetActive(SHOW_MODEL_TYPE.Wing == self.cur_show_index)
	self.node_list["ph_display_3"]:SetActive(SHOW_MODEL_TYPE.Esoterica == self.cur_show_index)

	self.node_list["model_btn_1"]:SetActive(SHOW_MODEL_TYPE.ShenLing ~= self.cur_show_index)
	self.node_list["model_btn_2"]:SetActive(SHOW_MODEL_TYPE.Esoterica ~= self.cur_show_index)
	self:FlushZhanLiInfor(self.cur_show_index)
end

function XiuZhenRoadView:FlushZhanLiInfor(cur_show_index) -- 战力信息
	local other_cfg = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuOtherCfg()
	if not other_cfg then return end
	local show_zhanli_id = 0
	if SHOW_MODEL_TYPE.ShenLing == cur_show_index then
		-- local tianshen_index = TianShenWGData.Instance:GetWeaponIndexByAppeImageId(other_cfg.pet_id) or -1
		-- local tianshen_cfg = TianShenWGData.Instance:GetTianshenItemCfgByIndex(tianshen_index)
		-- show_zhanli_id = tianshen_cfg and tianshen_cfg.act_item_id or 0
		show_zhanli_id = other_cfg.first_id
	elseif SHOW_MODEL_TYPE.Wing == cur_show_index then
		show_zhanli_id = other_cfg.second_id
	elseif SHOW_MODEL_TYPE.Esoterica == cur_show_index then
		-- local miji_fameng_id_list = string.split(other_cfg.miji_fameng_id, "|")
		-- local sumzhanli = 0
		-- for i = 1 ,#miji_fameng_id_list do
		-- 	sumzhanli = sumzhanli + ItemShowWGData.Instance.CalculateCapability(tonumber(miji_fameng_id_list[i]), true)
		-- end

		show_zhanli_id = other_cfg.third_id
		local capability = ItemShowWGData.Instance.CalculateCapability(show_zhanli_id, true)
		self.node_list["pet_zhanli"].text.text = capability
		return
	end

	local _, capability = ItemShowWGData.Instance:GetShouHuAttrByData(show_zhanli_id)
	self.node_list["pet_zhanli"].text.text = capability
end

function XiuZhenRoadView:StartQuest()
	if not self.notice_timer_quest then
		self.notice_timer_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.OnFlushModelSwitch, self),
			self.flush_model_time)
	end
end

function XiuZhenRoadView:OnFlushModelSwitch()
	-- if IsEmptyTable(self.display_model) then
	-- 	return
	-- end

	local max_num = SHOW_MODEL_TYPE.Esoterica
	if self.cur_show_index then
		self.cur_show_index = self.cur_show_index < max_num and self.cur_show_index + 1 or 1
	else
		self.cur_show_index = 1
	end

	self:FlushShowModel()
end

function XiuZhenRoadView:XiuZhenViewAnimation()
	if not self.do_xiu_zhen_tween then
		return
	end
	local tween_info = UITween_CONSTS.XiuZhen_Road
	local tween_info1 = UITween_CONSTS.XiuZhen_Road
	UITween.CleanAllTween(GuideModuleName.XiuZhenRoadView)

	UITween.FakeHideShow(self.node_list.ph_task_list)
	UITween.FakeHideShow(self.node_list.ph_leiji_cell_0)
	UITween.FakeHideShow(self.node_list.ph_leiji_list)
	UITween.FakeHideShow(self.node_list.ph_btn_list)
	--self.node_list.ph_btn_list_bg.rect:DOAnchorPos(Vector2(321, -366), tween_info1.MoveTime)
	--self.node_list.all_progress.rect:DOAnchorPos(Vector2(-235, 6), tween_info1.MoveTime)

	ReDelayCall(self, function()
		if not self.node_list then
			return
		end
		UITween.AlphaShow(GuideModuleName.XiuZhenRoadView, self.node_list.ph_btn_list, 0, 1, tween_info1.AlphaTime)
		UITween.AlphaShow(GuideModuleName.XiuZhenRoadView, self.node_list.ph_leiji_cell_0, 0, 1, tween_info1.AlphaTime)
		UITween.AlphaShow(GuideModuleName.XiuZhenRoadView, self.node_list.ph_leiji_list, 0, 1, tween_info1.AlphaTime)
	end, tween_info1.AlphaDelay, "xiu_zhen_1")

	ReDelayCall(self, function()
		if not self.node_list then
			return
		end
		local list = self.task_list:GetAllItems()
		local sort_list = {}

		for i, v in pairs(list) do
			local data = {}
			data.index = v:GetIndex()
			data.item = v
			sort_list[#sort_list + 1] = data
		end
		table.sort(sort_list, SortTools.KeyLowerSorter("index"))

		local count = 0
		local cur_index = 0
		for k, v in ipairs(sort_list) do
			if 0 ~= v.index then
				count = count + 1
			end
			v.item:PalyItemAnimator(count)
		end

		UITween.FakeToShow(self.node_list.ph_task_list)
	end, tween_info.DelayDoTime, "xiu_zhen")

	self.do_xiu_zhen_tween = false
end

--设置当前选中需要展示的信息
function XiuZhenRoadView:SetCurSelectShowInfo(is_select_btn)
	if nil == self.cur_select_data then
		return
	end

	local task_data = XiuZhenRoadWGData.Instance:GetCurViewNeedInfo(self.cur_select_data.cfg.open_day,
		self.select_btn_type)
	if self.task_list and not IsEmptyTable(task_data) then
		local t_idx = 1
		for key, value in ipairs(task_data) do
			if value.task_states == XIUZHENZHILU_TASK_STATES.WDC then
				t_idx = key
				break
			end
		end

		XiuZhenRoadWGData.Instance:SetTaskTimerIdx(t_idx)
		self.task_list:SetDataList(task_data, 0)
	end

	self:FlushSelectToggle(is_select_btn)
	self:FlushSkillDescPart()
	self:FlushProgressPart()
	-- self:FlushGiftPackPart()
	-- self:FlushPetCellPart()
	--self:FlushZhanLiInfor()
end

--累积奖励展示
function XiuZhenRoadView:FlushProgressPart()
	for i = 1, 3 do
		self.gift_page_cell_list[i]:SetData(self.cur_select_data)
	end
	--累积奖励展示
	local pro_reward = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuLeiJiInfo()
	self.leiji_list_0:SetData(pro_reward[#pro_reward])
	local leiji_data, red_flag = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuFourLeiJiInfo()
	if self.leiji_list and not IsEmptyTable(leiji_data) then
		self.leiji_list:SetDataList(leiji_data)
	end
	
	--累积的总进度
	local cur_total_progress = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuLeiJiRewardProgress()
	self.node_list.leiji_num.text.text = cur_total_progress
	self.cur_total_progress = cur_total_progress

	local cur_total_progress = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuLeiJiRewardProgress()
	--local max_total_progress = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuMaxCondition()
	local value_progress = XiuZhenRoadWGData.Instance:GetCurProgress(leiji_data, cur_total_progress)
	self.node_list["all_progress_img"].slider.value = value_progress

	--按钮完成标志
	self:FlushBtnIsFinish()
end

--技能部分展示
function XiuZhenRoadView:FlushSkillDescPart()
	--local show_str = string.format(Language.XiuZhenRoad.SkillName, self.cur_select_data.cfg.skill_name)
	self.node_list["ph_skill_name"].text.text = self.cur_select_data.cfg.skill_name
	local day_info = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuCurDayInfo(self.cur_select_data.cfg.open_day)
	local complse_num, max_num = XiuZhenRoadWGData.Instance:GetCurNeedInfoProgress(self.cur_select_data.cfg.open_day)
	local progress = complse_num / max_num
	progress = string.format("%.2f", progress)
	progress = tonumber(progress)
	local desc_skill_icon = self.cur_select_data.cfg.skill_desc_icon or " "
	self.node_list.type_icon.image:LoadSprite(ResPath.GetXiuZhenRoadImg(desc_skill_icon))
	self.node_list["get_skill_desc"].text.text = self.cur_select_data.cfg.skill_desc
	--local complse_str = (complse_num < max_num) and string.format(Language.XiuZhenRoad.NoReachRed, complse_num) or complse_num
	local slider_str = (complse_num < max_num) and string.format(Language.XiuZhenRoad.TaskSliderRed, complse_num, max_num) 
	or string.format(Language.XiuZhenRoad.TaskSliderGreen, complse_num, max_num)
	self.node_list["take_slider"].text.text = slider_str

	--XUI.SetButtonEnabled(self.node_list["btn_active_skill"], progress >= 1)
	self.node_list["btn_active_skill"].button.enabled = progress >= 1 and day_info.is_active_attr == 0

	self.node_list["active_skill_red"]:SetActive(progress >= 1 and day_info.is_active_attr == 0)
	local ylq_reward_icon = self.cur_select_data.cfg.ylq_reward_icon
	self.node_list.img_active_skill.image:LoadSprite(ResPath.GetXiuZhenRoadImg(ylq_reward_icon))
	self.node_list["img_active_skill"]:SetActive(day_info.is_active_attr == 1)
	self.node_list["btn_active_skill"]:SetActive(not (day_info.is_active_attr == 1))
	self.progress = progress * 100
end

function XiuZhenRoadView:FlushSelectToggle(is_select_btn)
	if not is_select_btn then
		local top_names_list = self.cur_select_data.top_names_list
		for i = 1, 3 do
			self.node_list["select_btn_name" .. i].text.text = top_names_list[i]
			self.node_list["select_btn_name_hl" .. i].text.text = top_names_list[i]
			local top_red_show = XiuZhenRoadWGData.Instance:GetSelectTabTypeBtnRedShow(self.cur_select_data.cfg.open_day,
				i)
			self.node_list["select_btn_red" .. i]:SetActive(top_red_show)
		end
	end
end

--礼包部分的展示
function XiuZhenRoadView:FlushGiftPackPart()
	local cur_day = XiuZhenRoadWGData.Instance:GetActivityOpenDay()
	if nil == self.gift_cell then
		self.gift_cell = ItemCell.New(self.node_list["ph_gift"])
	end

	local tab_type = self.select_btn_type
	if tab_type > 3 then
		tab_type = tab_type - 1
	end
	self.gift_cell:SetData(self.cur_select_data.cfg.show_item[tab_type - 1])
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.cur_select_data.cfg.show_item[tab_type - 1].item_id)
	if item_cfg then
		self.node_list["gift_text"].text.text = item_cfg.name
	end

	local init_price = tonumber(self.cur_select_data.init_price_list[tab_type])
	local price = tonumber(self.cur_select_data.price_list[tab_type])
	local limit_time = tonumber(self.cur_select_data.limit_time_list[tab_type])
	local glod_type = tonumber(self.cur_select_data.glod_type_list[tab_type])

	self.node_list["ph_gift_picre"].text.text = init_price
	self.node_list["btn_text"].text.text = price

	local gift_star = tonumber(self.cur_select_data.gift_star_list[tab_type])
	local zhekou = math.ceil((price / init_price) * 10)
	self.node_list["zhekou_num"].text.text = string.format(Language.XiuZhenRoad.ZheKouNum,zhekou)
	local buy_count_info = XiuZhenRoadWGData.Instance:GetDayGiftInfo(self.cur_select_data.cfg.open_day, tab_type)
	local buy_count = buy_count_info and buy_count_info.buy_count or 0
	self.node_list["ph_gift_limit"].text.text = string.format(Language.XiuZhenRoad.BuyGiftLimit, buy_count, limit_time)
	self.node_list["get_star_text"].text.text = string.format(Language.XiuZhenRoad.GetStarText2, gift_star)
	-- 1 = 仙玉，2=绑玉
	self.node_list["yuanjia_picre_icon"].image:LoadSprite(ResPath.GetMoneyIcon(glod_type))
	self.node_list["buy_picre_icon"].image:LoadSprite(ResPath.GetMoneyIcon(glod_type))

	self.node_list["btn_buy"]:SetActive(buy_count < limit_time)
	self.node_list["img_ygm"]:SetActive(buy_count >= limit_time)
	self.node_list["btn_text2"]:SetActive(self.cur_select_data.cfg.open_day <= cur_day)
	self.node_list["no_buy_text"]:SetActive(self.cur_select_data.cfg.open_day > cur_day)
	XUI.SetButtonEnabled(self.node_list["btn_buy"], self.cur_select_data.cfg.open_day <= cur_day)
	self.node_list["btn_buy_effect"]:SetActive(self.cur_select_data.cfg.open_day <= cur_day)
end

--碎片信息
function XiuZhenRoadView:FlushPetCellPart()
	-- 灵宠碎片
	local other_cfg = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuOtherCfg()
	local capability = ItemShowWGData.CalculateCapability(other_cfg.consume_id, true)
	if nil == self.pet_cell_item then
		self.pet_cell_item = ItemCell.New(self.node_list["pet_cell"])
	end
	local consume_id, consume_num = XiuZhenRoadWGData.Instance:GetRewardItemInfo()
	self.pet_cell_item:SetData({ item_id = consume_id })
	local item_cfg = ItemWGData.Instance:GetItemConfig(consume_id)
	self.node_list["pet_cell_name"].text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
	self.node_list["pet_zhanli"].text.text = capability

	local item_id, consume_num, product_id, item_seq = XiuZhenRoadWGData.Instance:GetRewardItemInfo()
	local composenum = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuLeiJiComposeNum()
	local composenum_progress = composenum .. "/" .. consume_num
	self.node_list["pet_cell_process"].text.text = string.format(Language.XiuZhenRoad.SuiPianNum, composenum, consume_num)

	local finish_flag = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuPetFlag()
	local btn_lingu_name = ""
	if 1 == finish_flag then
		btn_lingu_name = Language.XiuZhenRoad.Btn_YiLingQu
	else
		btn_lingu_name = Language.XiuZhenRoad.Btn_LingQu
	end
	self.node_list["btn_lingqu_text"].text.text = btn_lingu_name
	local pet_lingqu_flag = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuPetFlag()
	self.node_list["btn_lingqu_red"]:SetActive(composenum >= consume_num and 0 == pet_lingqu_flag)
	self.node_list["btn_lingqu"]:SetActive(composenum >= consume_num and 0 == pet_lingqu_flag)
	XUI.SetButtonEnabled(self.node_list["btn_lingqu"], 0 == pet_lingqu_flag)
	
	local compose_cfg = ComposeWGData.Instance:GetComposeCfgByItemId(product_id)
	self.node_list["combine_consume"].text.text = string.format(Language.XiuZhenRoad.CombineConsume,
		CommonDataManager.NotConverExtend(compose_cfg.coin))
end

--不需要频繁改变的游戏基础信息展示
function XiuZhenRoadView:SetBaseInfo()
	local other_cfg = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuOtherCfg()
	self.cur_show_index = XiuZhenRoadWGData.Instance:GetModelShowTabIndex()
	self:FlushEndTimePart()
	self:FlushShowModel()
	-- if nil == self.display_model then
	-- 	self.display_model = RoleModel.New()
	-- 	local display_data = {
	-- 		parent_node = self.node_list["ph_display_pet"],
	-- 		camera_type = MODEL_CAMERA_TYPE.BASE,
	-- 		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
	-- 		rt_scale_type = ModelRTSCaleType.M,
	-- 		can_drag = true,
	-- 	}
		
	-- 	self.display_model:SetRenderTexUI3DModel(display_data)
	-- 	-- self.display_model:SetUI3DModel(self.node_list["ph_display_pet"].transform,
	-- 	-- 	self.node_list["ph_display_pet"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
	-- 	self.node_list.ph_display_pet.event_trigger_listener:AddPointerClickListener(BindTool.Bind(self.OnDisplayClick,
	-- 		self))
	-- end

	if self.model1_display == nil then
		self.model1_display = OperationActRender.New(self.node_list["model1_root"])
			local data = {}
			if other_cfg.first_id ~= 0 and other_cfg.first_id ~= "" then
				local split_list = string.split(other_cfg.first_id, "|")
				if #split_list > 1 then
					local list = {}
					for k, v in pairs(split_list) do
						list[tonumber(v)] = true
					end
					data.model_item_id_list = list
				else
					data.item_id = tonumber(other_cfg.first_id)
				end
			end

			data.render_type = (other_cfg.first_render_type or 1) - 1
			-- data.position = Vector3(0, 0, 0)
			-- data.rotation = Vector3(0, 0, 0)
			-- data.scale = Vector3(1, 1, 1)

			data.rt_scale_type = ModelRTSCaleType.M
			data.model_click_func = function()
				TipWGCtrl.Instance:OpenItem({ item_id = data.item_id })
			end

			local pos_x, pos_y, pos_z = 0, 0, 0
			if other_cfg.ui_pos1 and other_cfg.ui_pos1 ~= "" then
				local pos_list = string.split(other_cfg.ui_pos1, "|")
				pos_x = tonumber(pos_list[1]) or pos_x
				pos_y = tonumber(pos_list[2]) or pos_y
				pos_z = tonumber(pos_list[3]) or pos_z
			end
		
			RectTransform.SetAnchoredPosition3DXYZ(self.node_list["model1_root"].rect, pos_x, pos_y, pos_z)
		
			if other_cfg["ui_scale1"] then
				local scale = other_cfg["ui_scale1"]
				Transform.SetLocalScaleXYZ(self.node_list["model1_root"].transform, scale, scale, scale)
			end
		
			if other_cfg.ui_rotation1 and other_cfg.ui_rotation1 ~= "" then
				local rotation_tab = string.split(other_cfg.ui_rotation1,"|")
				self.node_list["model1_root"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
			end
	

			self.model1_display:SetData(data)
	end


	if self.model2_display == nil then
		self.model2_display = OperationActRender.New(self.node_list["model2_root"])
		local data = {}
		if other_cfg.second_id ~= 0 and other_cfg.second_id ~= "" then
			local split_list = string.split(other_cfg.second_id, "|")
			if #split_list > 1 then
				local list = {}
				for k, v in pairs(split_list) do
					list[tonumber(v)] = true
				end
				data.model_item_id_list = list
			else
				data.item_id = tonumber(other_cfg.second_id)
			end
		end

		data.render_type = (other_cfg.second_render_type or 1) - 1
		-- data.position = Vector3(0, 0, 0)
		-- data.rotation = Vector3(0, 0, 0)
		-- data.scale = Vector3(1, 1, 1)
		data.rt_scale_type = ModelRTSCaleType.M
		data.model_click_func = function()
			TipWGCtrl.Instance:OpenItem({ item_id = data.item_id })
		end

		local pos_x, pos_y, pos_z = 0, 0, 0
		if other_cfg.ui_pos2 and other_cfg.ui_pos2 ~= "" then
			local pos_list = string.split(other_cfg.ui_pos2, "|")
			pos_x = tonumber(pos_list[1]) or pos_x
			pos_y = tonumber(pos_list[2]) or pos_y
			pos_z = tonumber(pos_list[3]) or pos_z
		end
	
		RectTransform.SetAnchoredPosition3DXYZ(self.node_list["model2_root"].rect, pos_x, pos_y, pos_z)
	
		if other_cfg["ui_scale2"] then
			local scale = other_cfg["ui_scale2"]
			Transform.SetLocalScaleXYZ(self.node_list["model2_root"].transform, scale, scale, scale)
		end
	
		if other_cfg.ui_rotation2 and other_cfg.ui_rotation2 ~= "" then
			local rotation_tab = string.split(other_cfg.ui_rotation2,"|")
			self.node_list["model2_root"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
		end

		self.model2_display:SetData(data)
	end

	if self.model3_display == nil then
		self.model3_display = OperationActRender.New(self.node_list["model3_root"])
		local data = {}
		if other_cfg.third_id ~= 0 and other_cfg.third_id ~= "" then
			local split_list = string.split(other_cfg.third_id, "|")
			if #split_list > 1 then
				local list = {}
				for k, v in pairs(split_list) do
					list[tonumber(v)] = true
				end
				data.model_item_id_list = list
			else
				data.item_id = tonumber(other_cfg.third_id)
			end
		end

		data.item_id = other_cfg.third_id
		data.render_type = (other_cfg.third_render_type or 1) - 1
		data.rt_scale_type = ModelRTSCaleType.M
		data.model_click_func = function()
			TipWGCtrl.Instance:OpenItem({ item_id = data.item_id })
		end

		local pos_x, pos_y, pos_z = 0, 0, 0
		if other_cfg.ui_pos3 and other_cfg.ui_pos3 ~= "" then
			local pos_list = string.split(other_cfg.ui_pos3, "|")
			pos_x = tonumber(pos_list[1]) or pos_x
			pos_y = tonumber(pos_list[2]) or pos_y
			pos_z = tonumber(pos_list[3]) or pos_z
		end
	
		RectTransform.SetAnchoredPosition3DXYZ(self.node_list["model3_root"].rect, pos_x, pos_y, pos_z)
	
		if other_cfg["ui_scale3"] then
			local scale = other_cfg["ui_scale3"]
			Transform.SetLocalScaleXYZ(self.node_list["model3_root"].transform, scale, scale, scale)
		end
	
		if other_cfg.ui_rotation3 and other_cfg.ui_rotation3 ~= "" then
			local rotation_tab = string.split(other_cfg.ui_rotation3,"|")
			self.node_list["model3_root"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
		end
		self.model3_display:SetData(data)
	end

	-- local miji_cfg = XiuZhenRoadWGData.Instance:GetMiJiShowItemInfo()
	-- if not IsEmptyTable(miji_cfg) then
	-- 	local eff_bundle, eff_asset = ResPath.GetUIEffect(miji_cfg.ui_effect_asset)
	-- 	self.node_list["es_effect"]:ChangeAsset(eff_bundle, eff_asset)
	-- 	--self.node_list.es_des.text.text = miji_cfg.esoterica_des
	-- end

	-- if self.display_model then
	-- 	local is_weapon_anim = 0
	-- 	local bundle, asset = ResPath.GetBianShenModel(other_cfg.pet_id) --ResPath.GetPetModel(pet_res_id)
	-- 	if not other_cfg.index then return end
	-- 	self.display_model:SetTianShenModel(other_cfg.pet_id, other_cfg.index, false, nil, SceneObjAnimator.Rest)
	-- end
end

function XiuZhenRoadView:FlushEndTimePart()
	--活动时间倒计时显示
	-- local end_time = XiuZhenRoadWGData.Instance:GetActivityEndTime()
	-- if end_time - TimeWGCtrl.Instance:GetServerTime() > 0 then
	-- 	self:UpdataEndTime(TimeWGCtrl.Instance:GetServerTime(), end_time)
	-- 	if CountDownManager.Instance:HasCountDown("xiuzhe_end_time") then
	-- 		CountDownManager.Instance:RemoveCountDown("xiuzhe_end_time")
	-- 	end
	-- 	CountDownManager.Instance:AddCountDown("xiuzhe_end_time", BindTool.Bind1(self.UpdataEndTime, self),
	-- 		BindTool.Bind1(self.EndTimeCallBack, self), end_time, nil, 1)
	-- else
	-- 	self:EndTimeCallBack()
	-- end

	local first_login_red = XiuZhenRoadWGData.Instance:GetFirstLoginRed()
	local buy_remind = XiuZhenRoadWGData.Instance:GetRMBBuyRed()
	self.node_list["zhengdi_red"]:SetActive(first_login_red and buy_remind == 1)
	if first_login_red then
		XiuZhenRoadWGData.Instance:SetFirstLoginRed(false)
	end
end

function XiuZhenRoadView:UpdataEndTime(elapse_time, total_time)
	local time = total_time - elapse_time
	local format_time = TimeUtil.Format2TableDHMS(time)
	if format_time.day > 0 then
		self.node_list["end_time"].text.text = string.format(Language.XiuZhenRoad.ActivityEndDay[1], format_time.day)
	elseif format_time.hour > 0 then
		self.node_list["end_time"].text.text = string.format(Language.XiuZhenRoad.ActivityEndDay[2], format_time.hour,
			format_time.min)
	elseif format_time.min > 0 then
		self.node_list["end_time"].text.text = string.format(Language.XiuZhenRoad.ActivityEndDay[3], format_time.min)
	else
		self.node_list["end_time"].text.text = string.format(Language.XiuZhenRoad.ActivityEndDay[4], format_time.s)
	end
end

function XiuZhenRoadView:OnDisplayClick()
	local other_cfg = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuOtherCfg()
	TipWGCtrl.Instance:OpenItem({ item_id = other_cfg.item_id })
end

function XiuZhenRoadView:EndTimeCallBack()
	self.node_list["end_time"].text.text = Language.XiuZhenRoad.JieShuDesc
	--XiuZhenRoadWGCtrl.Instance:CloseView()
end

function XiuZhenRoadView:SelecBtnCallBack(item)
	if nil == item:GetData() then
		return
	end
	self.cur_select_data       = item:GetData()
	local day_info             = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuCurDayInfo(self.cur_select_data.cfg.open_day)
	local active_attr_value    = day_info.active_attr_value
	local all_nature_value     = XiuZhenRoadWGData.Instance:GetCurAllNatureSum(self.cur_select_data.cfg.open_day)
	local progress             = active_attr_value[1] / all_nature_value
	self.progress              = progress * 100
	local btn_name             = string.gsub(self.cur_select_data.cfg.button_name, " ", "")
	-- self.node_list["gift_text"].text.text = string.format(Language.XiuZhenRoad.BiMai,btn_name)
	self:SetCurSelectShowInfo()
end

--活动说明
function XiuZhenRoadView:OnClickActivityStates()
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetTitle(Language.XiuZhenRoad.XiuZhenRoadTips)
		role_tip:SetContent(Language.XiuZhenRoad.XiuZhenRoadStatesTips)
	end
end

--礼包购买(灵宠)
function XiuZhenRoadView:OnClickLingQuPet()
	local composenum = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuLeiJiComposeNum()
	local consume_id, consume_num, product_id, item_seq = XiuZhenRoadWGData.Instance:GetRewardItemInfo()
	if composenum >= consume_num then
		ComposeWGCtrl.Instance:SendComposeReq(item_seq, 1, 0, product_id)
		return
	end

	local config = ComposeWGData.Instance:GetConfigByStuffId(consume_id)
	if config then
		local index = config.big_type * 10 + config.type
		local list = ComposeWGData.Instance:SortChildData(config.big_type, config.type, config.sub_type)
		local jump_index = 0
		for k, v in ipairs(list) do
			if v.producd_seq == config.producd_seq then
				jump_index = k
				break
			end
		end
		local open_param = config.sub_type
		FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, index,
			{ open_param = open_param, sub_view_name = jump_index })
	end

	--FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, "other_compose_pet")
end

--礼包购买
function XiuZhenRoadView:OnClickBuyGift()
	if IsEmptyTable(self.cur_select_data) then return end
	local tab_type = self.select_btn_type
	local opert_type = XIUZHENZHILU_OPEN_TYPE.XIUZHENZHILU_OPEN_TYPE_BUY_ITEM
	tab_type = tab_type - 1
	local item_cfg = self.cur_select_data.cfg.show_item[tab_type]
	local open_day = self.cur_select_data.cfg.open_day or 1
	if item_cfg then
		open_day = open_day - 1
		XiuZhenRoadWGCtrl.Instance:SendXiuZhenZhiLuReq(opert_type, open_day, tab_type, item_cfg.item_id)
	end
end

function XiuZhenRoadView:FlushBtnIsFinish()
	if nil == self.btn_list then
		return
	end

	self.btn_list:RefreshActiveCellViews()
end

function XiuZhenRoadView:OnClickSkill()
	XiuZhenRoadWGCtrl.Instance:OpenSkillTip(self.cur_select_data.cfg)
end

-- function XiuZhenRoadView:OnClickOpenRewardView()
-- 	XiuZhenRoadWGCtrl.Instance:OpenXiuZhenRewardTips()
-- end

function XiuZhenRoadView:OnClickBtnActiveSkill()
	if self.cur_select_data then
		local opert_type = XIUZHENZHILU_OPEN_TYPE.XIUZHENZHILU_OPEN_TYPE_ACTIVE_CHAPTER_SKILL
		XiuZhenRoadWGCtrl.Instance:SendXiuZhenZhiLuReq(opert_type, self.cur_select_data.cfg.open_day)
	end
end

function XiuZhenRoadView:ClickSelectBtn(tab_type)
	-- if self.select_btn_type == tab_type and self.node_list["xiuxian"].gameObject.activeSelf == true then
	-- 	return
	-- end

	if self.select_btn_type == tab_type and self.node_list["ph_task_list"].gameObject.activeSelf == true then
		return
	end

	if tab_type == 4 then
		self.node_list["shop_icon"]:SetActive(not self.node_list["shop_icon"].gameObject.activeSelf)
		self.node_list["task_icon"]:SetActive(not self.node_list["task_icon"].gameObject.activeSelf)
		self.node_list["ph_buy_list"]:SetActive(not self.node_list["ph_buy_list"].gameObject.activeSelf)
		self.node_list["select_tabs"]:SetActive(not self.node_list["select_tabs"].gameObject.activeSelf)
		self.node_list["ph_task_list"]:SetActive(not self.node_list["ph_task_list"].gameObject.activeSelf)
		-- self.node_list["xiuxian"]:SetActive(not self.node_list["xiuxian"].gameObject.activeSelf)
		-- self.node_list["yuanmeng"]:SetActive(not self.node_list["yuanmeng"].gameObject.activeSelf)
		self.node_list["zhengdi_red"]:SetActive(false)
		return
	else
		self.select_btn_type = tab_type
	end

	self:SetCurSelectShowInfo(true)
	self.node_list["ph_task_list"]:SetActive(true)
	self.node_list["ph_buy_list"]:SetActive(false)
	-- self.node_list["xiuxian"]:SetActive(true)
	-- self.node_list["yuanmeng"]:SetActive(false)
end

function XiuZhenRoadView:ModelCancelQuest()
	if self.notice_timer_quest then
		GlobalTimerQuest:CancelQuest(self.notice_timer_quest)
		self.notice_timer_quest = nil
	end
end