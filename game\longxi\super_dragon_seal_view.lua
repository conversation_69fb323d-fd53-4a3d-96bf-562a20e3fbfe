-- 至尊玉玺
LongXiView = LongXiView or BaseClass(SafeBaseView)

local SEAL_INIT_POS = Vector3(-270, 35, 0)
local sd_tween_up_height = 30
local SD_ONE_KEY_UP_DELAY = 0.2

function LongXiView:InitSuperDragonSealView()
    self:InitSdModel()

    if not self.sd_reward_list then
        self.sd_reward_list = AsyncListView.New(ItemCell, self.node_list.sd_reward_list)
        self.sd_reward_list:SetStartZeroIndex(true)
    end

    if not self.sd_cost_item then
        self.sd_cost_item = ItemCell.New(self.node_list.sd_cost_item_pos)
    end

    if not self.sd_special_item then
        self.sd_special_item = ItemCell.New(self.node_list.sd_special_item)
        self.sd_special_item:SetClickCallBack(BindTool.Bind(self.OnSdClickSpecialItem, self))
        self.sd_special_item:SetIsShowTips(false)
    end

    if not self.sd_attr_list then
        self.sd_attr_list = AsyncListView.New(LongXiAttrItemRender, self.node_list.sd_attr_list)
    end

    if not self.sd_desc_item_list then
        self.sd_desc_item_list = {}
        for i = 1, 3 do
            self.sd_desc_item_list[i] = LongXiDescItemRender.New(self.node_list["sd_desc_info_" .. i])
        end
        self:FlushDescItemList()
    end

    XUI.AddClickEventListener(self.node_list.sd_btn_bug, BindTool.Bind(self.OnClickSdBugBtn, self))
    XUI.AddClickEventListener(self.node_list.sd_skill_icon, BindTool.Bind(self.OnSdSKillClick, self))
    XUI.AddClickEventListener(self.node_list.sd_up_skill_icon, BindTool.Bind(self.OnSdSKillClick, self))
    XUI.AddClickEventListener(self.node_list.sd_btn_up, BindTool.Bind(self.OnSdUpBtnClick, self))
    XUI.AddClickEventListener(self.node_list.sd_special_btn, BindTool.Bind(self.OnSdClickSpecialItem, self))

    --self.node_list.recharge_desc:SetActive(LongXiWGData.Instance:GetRMBByAddRechargeFlag())
end

function LongXiView:InitSdModel()
    if self.sd_active_model_display == nil then
        self.sd_active_model_display = OperationActRender.New(self.node_list["sd_active_model"])
    end

    local base_data = LongXiWGData.Instance:GetLongXiBaseInfoByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)
    if base_data and base_data["model_bundle_name"] and base_data["model_asset_name"] then
        local data = {}
        --data.item_id = base_data.show_id
        data.bundle_name = base_data["model_bundle_name"]
        data.asset_name = base_data["model_asset_name"]
        data.render_type = OARenderType.RoleModel
        data.model_rt_type = ModelRTSCaleType.L

        if data.display_pos and data.display_pos ~= "" then
            local pos_list = string.split(data.display_pos, "|")
            local pos_x = tonumber(pos_list[1]) or 0
            local pos_y = tonumber(pos_list[2]) or 0
            local pos_z = tonumber(pos_list[3]) or 0

            data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
        end

        if data.rotation and data.rotation ~= "" then
            local rot_list = string.split(data.rotation, "|")
            local rot_x = tonumber(rot_list[1]) or 0
            local rot_y = tonumber(rot_list[2]) or 0
            local rot_z = tonumber(rot_list[3]) or 0

            data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
        end

        data.model_adjust_root_local_scale = data.display_scale

        self.sd_active_model_display:SetData(data)
    end
end

function LongXiView:ReleaseSuperDragonSealView()
    if self.sd_reward_list then
        self.sd_reward_list:DeleteMe()
        self.sd_reward_list = nil
    end

    if self.sd_cost_item then
        self.sd_cost_item:DeleteMe()
        self.sd_cost_item = nil
    end

    if self.sd_special_item then
        self.sd_special_item:DeleteMe()
        self.sd_special_item = nil
    end

    if self.sd_attr_list then
        self.sd_attr_list:DeleteMe()
        self.sd_attr_list = nil
    end

    if self.sd_active_model_display then
        self.sd_active_model_display:DeleteMe()
        self.sd_active_model_display = nil
	end

    if self.sd_desc_item_list then
        for i = 1, 3 do
            self.sd_desc_item_list[i]:DeleteMe()
            self.sd_desc_item_list[i] = nil
        end
        self.sd_desc_item_list = nil
    end

    self:ClearSdLongXiCountDown()
    self:SdCancelTween()
end

function LongXiView:FlushDescItemList()
    local base_data = LongXiWGData.Instance:GetLongXiBaseInfoByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)

    if IsEmptyTable(base_data) then
        return
    end

    local flag_split = Split(base_data.sp_img_show, "|")

    for i = 1, 3 do
        local sd_desc_item_data = {}
        sd_desc_item_data.desc = Language.LongXi.SDDescInfo[i]
        local flag = flag_split[i] and tonumber(flag_split[i]) or 0
        sd_desc_item_data.flag_show = flag
        self.sd_desc_item_list[i]:SetData(sd_desc_item_data)
    end
end

function LongXiView:ShowIndexSuperDragonSealView()
    self:SdPlayTween()
    self.node_list.sd_btn_text.text.text = Language.LongXi.OneKeyUp
end

function LongXiView:SdPlayTween()
	if not self.sd_tween then
		local tween_root = self.node_list["sd_seal"].rect
        tween_root.anchoredPosition = SEAL_INIT_POS
		self.sd_tween = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + sd_tween_up_height, 1)
		self.sd_tween:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	else
		self.sd_tween:Restart()
	end
end

function LongXiView:SdCancelTween()
	if self.sd_tween then
        self.sd_tween:Kill()
        self.sd_tween = nil
        local tween_root = self.node_list["sd_seal"]

        if tween_root then
            tween_root.rect.anchoredPosition = SEAL_INIT_POS
        end
	end
end

function LongXiView:FlushSuperDragonSealView()
    local data = LongXiWGData.Instance:GetLongXiDataByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)

    if IsEmptyTable(data) then
        return
    end

    local active = data.is_active ~= 0
    self.node_list.sd_active_panel:SetActive(not active)
    self.node_list.sd_up_level_panel:SetActive(active)
    local base_data = LongXiWGData.Instance:GetLongXiBaseInfoByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)
    local base_attr_list, base_capability = ItemShowWGData.Instance:OutStrAttrListAndCapalityByAttrId(base_data, "attr_id", "attr_value")

    if active then
        self:ClearSdLongXiCountDown()

        local bundle, asset = ResPath.GetSkillIconById(base_data.skill_ico)
        self.node_list.sd_up_skill_icon.image:LoadSprite(bundle, asset, function()
            self.node_list.sd_up_skill_icon.image:SetNativeSize()
        end)

        local is_max_level, attr_list, cap, skill_info = LongXiWGData.Instance:GetUpLevelAttrInfo(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)
        self.node_list.sd_skill_name.text.text = base_data.skill_name

        -- if not IsEmptyTable(skill_info) then
        --     self.node_list.skill_desc.text.text = skill_info.attr_name .. " " .. ToColorStr(skill_info.value_str, COLOR3B.D_GREEN)
        -- end
        --self.node_list.skill_desc.text.text = base_data.skill_des

        self.node_list.sd_up_max_flag:SetActive(is_max_level)
        self.node_list.sd_btn_up:SetActive(not is_max_level)
        self.node_list.sd_up_cap_value.text.text = cap + base_capability
        self.node_list.sd_level.text.text = string.format(Language.LongXi.Level, data.level)
        self.sd_attr_list:SetDataList(attr_list)

        self:FlushSdCostItem()
    else
        -- self.node_list.active_add_cap.text.text = base_data.capability_inc
        self.node_list.sd_active_cap_value.text.text = base_capability
        --self.node_list.active_add_cap.text.text = base_capability

        -- for i = 1, 4 do
        --     self.node_list["sd_active_attr" .. i].text.text = base_attr_list[i].attr_name .. " " .. ToColorStr(base_attr_list[i].value_str, COLOR3B.D_GREEN) 
        -- end

        local price = RoleWGData.GetPayMoneyStr(base_data.price, base_data.rmb_type, base_data.rmb_seq)
        self.node_list.sd_btn_gold_text.text.text = price

        local bundle, asset = ResPath.GetSkillIconById(base_data.skill_ico)
        self.node_list.sd_skill_icon.image:LoadSprite(bundle, asset, function()
            self.node_list.sd_skill_icon.image:SetNativeSize()
        end)

        self.sd_special_item:SetData({item_id = base_data.show_item_id})
        self.sd_reward_list:SetDataList(base_data.reward_item)

        self:FlushSdCountDown()
    end
end

function LongXiView:FlushSdCostItem()
    local cost_cfg = LongXiWGData.Instance:GetLongXiLevelInfoByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)

    if not IsEmptyTable(cost_cfg) then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(cost_cfg.cost_item_id)
        local enough = item_num >= cost_cfg.cost_item_num

        self.sd_cost_item:SetFlushCallBack(function ()
            local right_text = ToColorStr(item_num .. "/" .. cost_cfg.cost_item_num, enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
            self.sd_cost_item:SetRightBottomColorText(right_text)
            self.sd_cost_item:SetRightBottomTextVisible(true)
        end)

        self.sd_cost_item:SetData({item_id = cost_cfg.cost_item_id, num = cost_cfg.cost_item_num, is_bind = 0})
    end
end

function LongXiView:OnClickSdBugBtn()
    local base_data = LongXiWGData.Instance:GetLongXiBaseInfoByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)
    RechargeWGCtrl.Instance:Recharge(base_data.price, base_data.rmb_type, base_data.rmb_seq)
end

function LongXiView:FlushSdCountDown()
	self:ClearSdLongXiCountDown()
	local count_down_time = LongXiWGData.Instance:GetLongXiCountDownTime()

	if count_down_time > 0 then
		self:UpdateSdCountDown(0, count_down_time)
        local interval = 1
        self.sd_timer = CountDown.Instance:AddCountDown(count_down_time, interval,
        function(elapse_time, total_time)
            self:UpdateSdCountDown(elapse_time, total_time)
        end,
        function()
            if self.node_list.sd_active_time then
                self.node_list.sd_active_time.text.text = ""
                self.node_list["sd_active_time"]:SetActive(false)
            end
        end)
    end
end

function LongXiView:UpdateSdCountDown(elapse_time, total_time)
    if self.node_list.sd_active_time then
        local time = TimeUtil.FormatSecondDHM8(total_time - elapse_time)
        self.node_list.sd_active_time.text.text = string.format(Language.LongXi.CountDownTime, time)
        self.node_list["sd_active_time"]:SetActive(true)
    end
end

function LongXiView:ClearSdLongXiCountDown()
    if self.sd_timer and CountDown.Instance:HasCountDown(self.sd_timer) then
        CountDown.Instance:RemoveCountDown(self.sd_timer)
        self.sd_timer = nil
    end
end

function LongXiView:OnSdSKillClick()
    local show_data = LongXiWGData.Instance:GetSkillInfo(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)
    NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

function LongXiView:OnSdClickSpecialItem()
    LongXiWGCtrl.Instance:OpenLongXiItemTips(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)
end

function LongXiView:OnSdUpBtnClick()
    if self:IsSdCanUp() then
        if LongXiWGData.Instance:GetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL) then
            self.node_list.sd_btn_text.text.text = Language.LongXi.OneKeyUp
            LongXiWGData.Instance:SetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL, false)
        else
            LongXiWGData.Instance:SetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL, true)
            self:SdUpLevel()
            self.node_list.sd_btn_text.text.text = Language.LongXi.Stop
        end

    else
        TipWGCtrl.Instance:ShowSystemMsg(Language.LongXi.UpGoodsNotEnough)
    end
end

function LongXiView:IsSdCanUp()
    local cost_cfg = LongXiWGData.Instance:GetLongXiLevelInfoByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)
    if not IsEmptyTable(cost_cfg) then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(cost_cfg.cost_item_id)
        return item_num >= cost_cfg.cost_item_num
    end

    return false
end

function LongXiView:SdUpLevel()
    local base_data = LongXiWGData.Instance:GetLongXiBaseInfoByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)
    LongXiWGCtrl.Instance:SendLongXiRequest(LONGXI_OPERATE_TYPE.LONGXI_OPERATE_TYPE_LEVEL_UP, base_data.seq)
end

function LongXiView:StartSuperDragonUpLevelAni()
    TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji,
    is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["sd_effect_root"]})

    if self:IsSdCanUp() then
        if LongXiWGData.Instance:GetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL) then
            ReDelayCall(self, function()
                self:SdUpLevel()
             end, SD_ONE_KEY_UP_DELAY, "super_dragon_token")
        end
    else
        LongXiWGData.Instance:SetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL, false)
        self.node_list.sd_btn_text.text.text = Language.LongXi.OneKeyUp
    end
end

--------------------------------------LongXiAttrItemRender------------------------------------------------
LongXiAttrItemRender = LongXiAttrItemRender or BaseClass(BaseRender)
function LongXiAttrItemRender:LoadCallBack()
	self:KillArrowTween()
	local tween_time = 0.8
    local node = self.node_list.attr_arrow
	if node then
        RectTransform.SetAnchoredPositionXY(node.rect, node.rect.anchoredPosition.x, -1)
        self.arrow_tweener = node.rect:DOAnchorPosY(5, tween_time)
        self.arrow_tweener:SetEase(DG.Tweening.Ease.InOutSine)
        self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	end
end

function LongXiAttrItemRender:__delete()
    self:KillArrowTween()
end

function LongXiAttrItemRender:KillArrowTween()
    if self.arrow_tweener then
        self.arrow_tweener:Kill()
        self.arrow_tweener = nil
    end
end

function LongXiAttrItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.attr_name.text.text = self.data.attr_name .. "："
    self.node_list.attr_value.text.text = self.data.value_str
    if nil == self.data.add_value then
        self.node_list.attr_add.text.text = ""
        self.node_list.attr_arrow:SetActive(false)
    else
        self.node_list.attr_add.text.text = self.data.add_value
        self.node_list.attr_arrow:SetActive(true)
    end
end

--------------------------------------LongXiDescItemRender------------------------------------------------
LongXiDescItemRender = LongXiDescItemRender or BaseClass(BaseRender)

function LongXiDescItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.desc.text.text = self.data.desc

    local flag = self.data.flag_show
    self.node_list.flag:SetActive(flag > 0)
    if flag > 0 then
        local bundle, asset = ResPath.GetLongXiImg("a3_mssh_bq" .. flag)
        self.node_list.flag.image:LoadSprite(bundle, asset, function()
            self.node_list.flag.image:SetNativeSize()
        end)
    end
end