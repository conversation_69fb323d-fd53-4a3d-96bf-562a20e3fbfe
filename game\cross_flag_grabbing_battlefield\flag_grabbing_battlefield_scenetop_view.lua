FlagGrabbingBattleFieldSceneTopView = FlagGrabbingBattleFieldSceneTopView or BaseClass(SafeBaseView)

local CROSS_FLAG_CAMP_TYPE = {
	BLUE = 0,
	RED = 1,
	NEUTRAL = 2,
}

function FlagGrabbingBattleFieldSceneTopView:__init()
	self.view_layer = UiLayer.MainUIHigh
	self:AddViewResource(0, "uis/view/country_map_ui/flag_grabing_battlefield_ui_prefab", "layout_fgb_scene_top_view")
    self.is_safe_area_adapter = true
	self.mid_point_seq_cache = -1
end

function FlagGrabbingBattleFieldSceneTopView:LoadCallBack()
	for i = 0, 1 do
		local team_cfg = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBCampCfgByCampId(i)
		self.node_list["team_name" .. i].text.text = team_cfg and team_cfg.camp_name or ""
		self.node_list["msg_name" .. i].text.text = team_cfg and team_cfg.camp_name or ""
	end

	self.shrinkbuttons_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind1(self.ShrinkButtonsValueChange, self))

	self:FlushEndTime()
end

function FlagGrabbingBattleFieldSceneTopView:FlushEndTime()
	local room_end_time = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBRoomEndTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()

	if room_end_time > server_time then
		if CountDownManager.Instance:HasCountDown("fgb_room_time") then
			CountDownManager.Instance:RemoveCountDown("fgb_room_time")
		end

		CountDownManager.Instance:AddCountDown("fgb_room_time", 
                function (elapse_time, total_time)
					local valid_time = total_time - elapse_time

					if self.node_list.level_time then
						self.node_list.level_time.text.text = TimeUtil.FormatSecond(math.floor(valid_time), 2)
					end
				end, 
                function ()
					if self.node_list.act_time then
						self.node_list.act_time:CustomSetActive(false)
					end
				end, 
                room_end_time, 
                nil, 1)
	end
end

function FlagGrabbingBattleFieldSceneTopView:ShrinkButtonsValueChange(isOn)
	local start_alpha = isOn and 1 or 0
	local end_alpha = isOn and 0 or 1
	self.node_list.fp_move_up_node.canvas_group:DoAlpha(start_alpha, end_alpha, 0.3)
	self.node_list.fp_move_up_node.canvas_group.blocksRaycasts = not isOn
end

function FlagGrabbingBattleFieldSceneTopView:ReleaseCallBack()
	if self.shrinkbuttons_change then
		GlobalEventSystem:UnBind(self.shrinkbuttons_change)
		self.shrinkbuttons_change = nil
	end

	self.mid_point_seq_cache = nil
	if CountDownManager.Instance:HasCountDown("fgb_next_cale_contend_time") then
		CountDownManager.Instance:RemoveCountDown("fgb_next_cale_contend_time")
	end
	
	if CountDownManager.Instance:HasCountDown("fgb_room_time") then
		CountDownManager.Instance:RemoveCountDown("fgb_room_time")
	end
end

function FlagGrabbingBattleFieldSceneTopView:OnFlush(param_t, index)
	for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushTopScoreInfo()
		elseif k == "flush_contend" then
			self:FlushMidInfo()
		elseif k == "flush_time" then
			self:FlushMidProcess()
        end
    end
end

function FlagGrabbingBattleFieldSceneTopView:FlushTopScoreInfo()
	local camp_info_list = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBSceneTeamScore()

	for i = 0, 1 do
		self.node_list["team_score_num" .. i].text.text = (camp_info_list[i] or {}).camp_score or ""
	end

	--[[
	local team_id = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBMyCamp()
	if team_id >= 0 then
		local bundle, asset = ResPath.GetCrossFGBPathImg("a3_gjzz_zjm_" .. team_id)
		self.node_list.slider_icon.image:LoadSprite(bundle, asset, function ()
			self.node_list.slider_icon.image:SetNativeSize()
		end)
	end
	]]
end

function FlagGrabbingBattleFieldSceneTopView:FlushMidInfo()
	if not self.mid_point_seq_cache then
		return
	end

	local show_mid = self.mid_point_seq_cache >= 0
	self.node_list.fgb_center_progress:CustomSetActive(show_mid)
	
	if show_mid then
		local data_info = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBContendInfoBySeq(self.mid_point_seq_cache)
	
		if not IsEmptyTable(data_info) then
			local camp_owner = CROSS_FLAG_CAMP_TYPE.NEUTRAL
			local left_progress = (data_info.camp_value_list or {})[0] or 0
			local right_progress = (data_info.camp_value_list or {})[1] or 0

			for i = 1, 10, 1 do
				self.node_list["left_point" .. i]:CustomSetActive(i <= left_progress)
				self.node_list["right_point" .. i]:CustomSetActive(i <= right_progress)
			end

			if left_progress ~= right_progress then
				camp_owner = left_progress > right_progress and CROSS_FLAG_CAMP_TYPE.BLUE or CROSS_FLAG_CAMP_TYPE.RED
			end
			local bundle, asset = ResPath.GetCrossFGBPathImg("a3_gjzz_zjm_" .. camp_owner)
			self.node_list.slider_icon.image:LoadSprite(bundle, asset, function ()
				self.node_list.slider_icon.image:SetNativeSize()
			end)
		end
	end
end

-- 策划要求需要看到 0进度状态，及满状态，然后消失
function FlagGrabbingBattleFieldSceneTopView:FlushMidProcess()
	self.node_list["mid_points_slider"].image.fillAmount = 0
	local next_cal_time, calc_contend_time = CrossFlagGrabbingBattleFieldWGData.Instance:GetNextCaleContendTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local cur_total_time = next_cal_time - server_time     -- 时间间隔   这次计算 到下次计算的总时间
	local time_prop = calc_contend_time + 1                -- 分化时间段
	local time_per = cur_total_time / time_prop    		   -- 每格所占时间
	local time_interval = time_per / 2  				   -- 临界有数值误差 折半刷

	if cur_total_time > 0 then
		CountDownManager.Instance:RemoveCountDown("fgb_next_cale_contend_time")

		CountDownManager.Instance:AddCountDown("fgb_next_cale_contend_time",
			function (elapse_time, total_time)
				if self.node_list["mid_points_slider"] then
					local index = math.floor(elapse_time / time_per) + 1
					self.node_list["mid_points_slider"].image.fillAmount = index / 5
				end
			end,
			function ()
			end,
		next_cal_time, nil, time_interval)
	end
end

function FlagGrabbingBattleFieldSceneTopView:OnEnterFGBPoint(seq)
	self.mid_point_seq_cache = seq
	self:FlushMidInfo()
end

function FlagGrabbingBattleFieldSceneTopView:OnOutFGBPoint(seq)
	self.mid_point_seq_cache = -1
	self:FlushMidInfo()
end

function FlagGrabbingBattleFieldSceneTopView:ShowFGBMsg(info)
	if self.node_list.fgb_fight_point_tip then
		self.node_list.fgb_fight_point_tip.canvas_group.alpha = 0
		self.node_list.fgb_fight_point_tip.transform.localScale = Vector3(0, 0, 0)
		self.node_list.fgb_fight_point_tip:CustomSetActive(true)
		
		local team_cfg = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBCampCfgByCampId(info.team_id)
		local team_name = team_cfg and team_cfg.camp_name or ""
		local point_cfg = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBContendBySeq(info.seq)
		local seq = team_cfg and team_cfg.seq or 0
		local bundle, asset = ResPath.GetRawImagesPNG("a3_gjzz_zjm_" .. seq)
		self.node_list.fgb_fight_icon.raw_image:LoadSprite(bundle, asset, function ()
			self.node_list.fgb_fight_icon.raw_image:SetNativeSize()
		end)

		local color = seq == 0 and "#66C7FF" or "#FFE58C"
		local desc_str = string.format(Language.FlagGrabbingBattlefield.FGBFightPointDesc, point_cfg.contend_name)
		self.node_list.fight_point_desc.text.text = ToColorStr(desc_str, color)
		self.node_list.fgb_fight_point_tip.canvas_group:DoAlpha(0, 1, 0.5)
		self.node_list.fgb_fight_point_tip.rect:DOScale(Vector3(1, 1, 1), 0.5):SetEase(DG.Tweening.Ease.OutBack)
		
		GlobalTimerQuest:AddDelayTimer(function()
			if self.node_list.fgb_fight_point_tip then
				self.node_list.fgb_fight_point_tip:CustomSetActive(false)
			end
		end, 1.4)
	end
end