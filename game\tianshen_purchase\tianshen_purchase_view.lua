TianShenPurchaseView = TianShenPurchaseView or BaseClass(SafeBaseView)

function TianShenPurchaseView:__init()
    self.view_layer = UiLayer.Normal
	self.is_need_depth = true
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/tianshen_purchase_ui_prefab", "layout_tianshen_purchase")
end

function TianShenPurchaseView:__delete()
end

function TianShenPurchaseView:ReleaseCallBack()
	if self.reward_list then
        for k,v in pairs(self.reward_list) do
            v:DeleteMe()
        end

        self.reward_list = nil
    end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
	end

	if CountDownManager.Instance:HasCountDown("tianshen_purchase_time") then
        CountDownManager.Instance:RemoveCountDown("tianshen_purchase_time")
    end

	if CountDownManager.Instance:HasCountDown("tianshen_purchase_zhe_time") then
        CountDownManager.Instance:RemoveCountDown("tianshen_purchase_zhe_time")
    end
end

function TianShenPurchaseView:OpenCallBack()
	TianShenPurchaseWGCtrl.Instance:ReqTianShenPurchaseInfo(OA_TIANSHEN_RMB_BUY_OPERATE_TYPE.INFO)
end

function TianShenPurchaseView:LoadCallBack()
    if self.reward_list == nil then
        self.reward_list = {}
        for i = 1, 5 do
            self.reward_list[i] = TianShenPurchaseCell.New(self.node_list["item_" .. i])
        end
    end

    if not self.model_display then
        self.model_display = OperationActRender.New(self.node_list["display_model"])
        self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	XUI.AddClickEventListener(self.node_list["buy_btn"], BindTool.Bind(self.OnClickBuy, self))
	XUI.AddClickEventListener(self.node_list["zhe_buy_btn"], BindTool.Bind(self.OnClickZheBuy, self))
end

function TianShenPurchaseView:ShowIndexCallBack()
    self:FlushTimeCount()
	ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.TianShenPurchaseView, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TIANSHEN_RMB_BUY)
end

function TianShenPurchaseView:OnFlush(param_t, index)
	local shop_info = TianShenPurchaseWGData.Instance:GetCurShopCfg()
	local other_cfg = TianShenPurchaseWGData.Instance:GetOtherCfg()
	local info_1 = shop_info and shop_info[1]
	local info_2 = shop_info and shop_info[2]

	if not IsEmptyTable(shop_info) then
		self:FlushModel(info_1)
		local reward_data = {}
		if info_1 and info_1.reward_item then
			for i, v in pairs(info_1.reward_item) do
				table.insert(reward_data, v)
			end
		end

		for k, v in ipairs(self.reward_list) do
			if reward_data[k] then
				v:SetActive(true)
				v:SetData(reward_data[k])
			else
				v:SetActive(false)
			end
    	end

		local is_open_gift = other_cfg.switch == 1
	    local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TIANSHEN_RMB_BUY)
		self.node_list.zhe_buy_btn:SetActive(not IsEmptyTable(info_2) and act_day == 1 and is_open_gift)
		self.node_list.buy_btn:SetActive(not IsEmptyTable(info_1))

	    self.node_list.zhe_time_str:SetActive(not IsEmptyTable(info_2) and act_day == 1 and is_open_gift)
	    self.node_list.xz_img:SetActive(not IsEmptyTable(info_2) and act_day == 1 and is_open_gift)
	    self.node_list.desc_img:SetActive(not IsEmptyTable(info_2) and act_day == 1 and is_open_gift)

		if info_1 then
			local bug_flag = TianShenPurchaseWGData.Instance:GetShopIsBuyFlag(info_1.seq)
			local buy_count_limit = info_1.buy_count_limit
			XUI.SetButtonEnabled(self.node_list["buy_btn"], bug_flag < buy_count_limit)
			local price = RoleWGData.GetPayMoneyStr(info_1.price, info_1.rmb_type, info_1.rmb_seq)
			self.node_list.buy_price.text.text = price --购买价格
			local original_price = RoleWGData.GetPayMoneyStr(info_1.original_price, info_1.rmb_type, info_1.rmb_seq)
			self.node_list.buy_original_price.text.text = string.format(Language.Common.YuanJia, original_price)

			local bg_bundle, bg_asset = ResPath.GetTianShenPurchaseBgImg(info_1.bj_img)
			self.node_list.bg.raw_image:LoadSprite(bg_bundle, bg_asset, function()
				self.node_list.bg.raw_image:SetNativeSize()
			end)

			local attr_bundle, attr_asset = ResPath.GetTianShenPurchaseImg(info_1.attr_img)
			for i = 1, 3 do
				self.node_list["attr_bg"..i].image:LoadSprite(attr_bundle, attr_asset, function()
					self.node_list["attr_bg"..i].image:SetNativeSize()
				end)
			end

			local gb_bundle, gb_asset = ResPath.GetTianShenPurchaseImg(info_1.gb_img)
			self.node_list.btn_close_window.image:LoadSprite(gb_bundle, gb_asset, function()
			self.node_list.btn_close_window.image:SetNativeSize()
			end)

			self.node_list.all_count.text.text = info_1.all_discount or ""

			self.node_list.quota_text.text.text = string.format(Language.OpertionAcitvity.XianShiMiaoSha.BuyLimitDesc, bug_flag, buy_count_limit)
			self:FlushCapStr(info_1)
		end

		if info_2 and act_day == 1 and is_open_gift then
			local bug_flag = TianShenPurchaseWGData.Instance:GetShopIsBuyFlag(info_2.seq)
			local buy_count_limit = info_2.buy_count_limit
			XUI.SetButtonEnabled(self.node_list["zhe_buy_btn"], bug_flag < buy_count_limit)
			local price = RoleWGData.GetPayMoneyStr(info_2.price, info_2.rmb_type, info_2.rmb_seq)
			self.node_list.zhe_buy_price.text.text = price --打折购买价格
			self.node_list.zhe_count.text.text = info_2.all_discount
			self:FlushZheTimeCount()
		end

	end
end

function TianShenPurchaseView:FlushModel(shop_info)
	if IsEmptyTable(shop_info) then
		return
	end

	local display_data = {}
	display_data.should_ani = true
	if shop_info.model_show_itemid ~= 0 and shop_info.model_show_itemid ~= "" then
		local split_list = string.split(shop_info.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = shop_info.model_show_itemid
		end
	end
	display_data.bundle_name = shop_info["model_bundle_name"]
    display_data.asset_name = shop_info["model_asset_name"]
    local model_show_type = tonumber(shop_info["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1
    display_data.model_click_func = function ()
        TipWGCtrl.Instance:OpenItem({item_id = shop_info["model_show_itemid"]})
    end

	if shop_info.model_pos and shop_info.model_pos ~= "" then
		local pos_list = string.split(shop_info.model_pos, "|")
		local pos_x = tonumber(pos_list[1]) or 0
		local pos_y = tonumber(pos_list[2]) or 0
		local pos_z = tonumber(pos_list[3]) or 0

		display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
	end

	if shop_info.model_rot and shop_info.model_rot ~= "" then
		local rot_list = string.split(shop_info.model_rot, "|")
		local rot_x = tonumber(rot_list[1]) or 0
		local rot_y = tonumber(rot_list[2]) or 0
		local rot_z = tonumber(rot_list[3]) or 0

		display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
	end

	if shop_info.model_scale and shop_info.model_scale ~= "" then
		display_data.model_adjust_root_local_scale = shop_info.model_scale
	end

	display_data.model_rt_type = ModelRTSCaleType.L

    self.model_display:SetData(display_data)
end

function TianShenPurchaseView:FlushCapStr(shop_info)
	local capability = 0
	local item_data = SortDataByItemColor(shop_info.reward_item)
	for k, v in pairs(item_data) do
		local show_id = ItemWGData.Instance:GetComposeProductid(v.item_id)
		local item_cfg = ItemWGData.Instance:GetItemConfig(show_id)
		if item_cfg then
			capability = capability + ItemShowWGData.CalculateCapability(show_id)
		end
	end

	self.node_list.uplevel_cap_desc.text.text = capability
end

function TianShenPurchaseView:OnClickBuy()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TIANSHEN_RMB_BUY)
	if activity_info.status ~= ACTIVITY_STATUS.OPEN then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenPurchase.CurActHadEnd)
		return
	end

	local is_all_buy = TianShenPurchaseWGData.Instance:GetCurShopIsAllBuy()
	if is_all_buy then
		TipWGCtrl.Instance:ShowSystemMsg(Language.TianShenPurchase.AllShopBuy)
		return
	end

	local shop_info = TianShenPurchaseWGData.Instance:GetCurShopCfg()
	local info_1 = shop_info and shop_info[1]
	if info_1 then
		RechargeWGCtrl.Instance:Recharge(info_1.price, info_1.rmb_type, info_1.rmb_seq)
	end
end

function TianShenPurchaseView:OnClickZheBuy()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TIANSHEN_RMB_BUY)
	if activity_info.status ~= ACTIVITY_STATUS.OPEN then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenPurchase.CurActHadEnd)
		return
	end

	local is_all_buy = TianShenPurchaseWGData.Instance:GetCurShopIsAllBuy()
	if is_all_buy then
		TipWGCtrl.Instance:ShowSystemMsg(Language.TianShenPurchase.AllShopBuy)
		return
	end

	local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TIANSHEN_RMB_BUY)
	local other_cfg = TianShenPurchaseWGData.Instance:GetOtherCfg()
	if act_day == 1 and other_cfg.switch == 1 then
		local shop_info = TianShenPurchaseWGData.Instance:GetCurShopCfg()
		local info_2 = shop_info and shop_info[2]
		if info_2 then
			RechargeWGCtrl.Instance:Recharge(info_2.price, info_2.rmb_type, info_2.rmb_seq)
		end
	end
end

function TianShenPurchaseView:FlushTimeCount()
    local time, next_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TIANSHEN_RMB_BUY)
    if time > 0 then
        if CountDownManager.Instance:HasCountDown("tianshen_purchase_time") then
            CountDownManager.Instance:RemoveCountDown("tianshen_purchase_time")
        end

        CountDownManager.Instance:AddCountDown("tianshen_purchase_time", 
            BindTool.Bind(self.FinalUpdateTimeCallBack, self), 
            BindTool.Bind(self.OnComplete, self), 
            nil, time, 1)
    else
        self:OnComplete()
    end
end

function TianShenPurchaseView:FinalUpdateTimeCallBack(now_time, total_time)
    local time = math.ceil(total_time - now_time)
    local time_str = TimeUtil.FormatSecondDHM8(time)
    self.node_list["time_str"].text.text = string.format(Language.TianShenPurchase.ActivityTime, time_str) 
end

function TianShenPurchaseView:OnComplete()
    self.node_list.time_str.text.text = ""
end

function TianShenPurchaseView:FlushZheTimeCount()
    local time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
    if time > 0 then
        if CountDownManager.Instance:HasCountDown("tianshen_purchase_zhe_time") then
            CountDownManager.Instance:RemoveCountDown("tianshen_purchase_zhe_time")
        end

        CountDownManager.Instance:AddCountDown("tianshen_purchase_zhe_time", 
            BindTool.Bind(self.FinalUpdateZheTimeCallBack, self), 
            BindTool.Bind(self.OnZheComplete, self), 
            nil, time, 1)
    else
        self:OnZheComplete()
    end
end

function TianShenPurchaseView:FinalUpdateZheTimeCallBack(now_time, total_time)
    local time = math.ceil(total_time - now_time)
    local zhe_time_str = TimeUtil.FormatSecondDHM8(time)
    self.node_list["zhe_time_str"].text.text = string.format(Language.TianShenPurchase.ZheTime, zhe_time_str) 
end

function TianShenPurchaseView:OnZheComplete()
    self.node_list.zhe_time_str.text.text = ""
end

-------------------------TianShenPurchaseCell---------------
TianShenPurchaseCell = TianShenPurchaseCell or BaseClass(BaseRender)

function TianShenPurchaseCell:__init()
	self.item = ItemCell.New(self.node_list["cell_pos"])
end

function TianShenPurchaseCell:__delete()
	if self.item then
		self.item:DeleteMe()
		self.item = nil
	end
end

function TianShenPurchaseCell:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.item:SetData(self.data)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	self.node_list.name.text.text = item_cfg.name
end