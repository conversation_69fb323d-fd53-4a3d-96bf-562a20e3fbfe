ShenYuanBossFuhuoView = ShenYuanBossFuhuoView or BaseClass(SafeBaseView)
local FuHuoType = {
    None = 0,
	Common = 1,
	Here = 2,
}
local stone_id = COMMON_CONSTS.RESURGENCE_STONE --复活石Id
function ShenYuanBossFuhuoView:__init()
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(false,true)
    self.view_name = "ShenYuanBossFuhuoView"
	self.is_modal = true
	self.is_any_click_close = false
	self.killer_name = ""
	self.killer_level = ""
	self.active_close = false
	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_shenyuan_bekilled")
    self.fuhuo_type = FuHuoType.None

end

function ShenYuanBossFuhuoView:__delete()
	self.fuhuo_common_callback = nil
	self.fuhuo_here_callback = nil
	self.killer_name = nil
	self.is_role = nil
	self.killer_level = nil
end

function ShenYuanBossFuhuoView:ReleaseCallBack()
	self.fuhuo_type = FuHuoType.None
	if CountDownManager.Instance:HasCountDown("shenyuan_fuhuo") then
		CountDownManager.Instance:RemoveCountDown("shenyuan_fuhuo")
    end
end

function ShenYuanBossFuhuoView:OpenCallBack()
	self.fuhuo_type = FuHuoType.None
end

function ShenYuanBossFuhuoView:CloseCallBack()
	self.fuhuo_type = FuHuoType.None
	self.killer_name = ""
    self.killer_level = ""
	CountDownManager.Instance:RemoveCountDown("shenyuan_fuhuo")
	if self.fuhuo_delay then
		GlobalTimerQuest:CancelQuest(self.fuhuo_delay)
		self.fuhuo_delay = nil
	end
end

function ShenYuanBossFuhuoView:LoadCallBack()
	self.node_list["btn_free_here"].button:AddClickListener(BindTool.Bind(self.OnClickFuhuoHere, self))
    self.node_list["btn_fuhuo_here"].button:AddClickListener(BindTool.Bind(self.OnClickFuhuoHere, self))
    self.node_list["btn_stone_here"].button:AddClickListener(BindTool.Bind(self.OnClickFuhuoHere, self))
    local item_config = ItemWGData.Instance:GetItemConfig(COMMON_CONSTS.RESURGENCE_STONE)
    if item_config then
        self.node_list["stone_icon"].image:LoadSprite(ResPath.GetItem(item_config.icon_id))
    end

	local btn_show = FuBenWGData.Instance:GetFuhuoBtn()
    for i = 1, 3 do
		self.node_list["btn_get_" .. i].button:AddClickListener(BindTool.Bind(self.OnclickHandler, self, btn_show[i]))
    end

	self.node_list["btn_open_chat"].button:AddClickListener(function()
		ChatWGCtrl.Instance:OpenChatWindow()
	end)
end

function ShenYuanBossFuhuoView:ShowIndexCallBack()
	self:Flush()
end

-- 复活按钮上面的字内容
-- 复活价格说明
function ShenYuanBossFuhuoView:SetTip7Dec()
	local coin_cost = FuhuoWGCtrl.Instance:GetFuhuoGold()
	self.node_list.text_money.tmp.text = coin_cost
	self.node_list.des_text_2.tmp.text = Language.Fuhuo.FuHuoXiaoHaoPrompt
end

function ShenYuanBossFuhuoView:OnFlush(param_t)
	self:RefreshView()
	for k, v in pairs(param_t) do
		if k == "all" then
		elseif k == "daojishi" then
			self:DaoJiShi(v.time or 1)
		end
	end
end

function ShenYuanBossFuhuoView:RefreshView()
	local num = ItemWGData.Instance:GetItemNumInBagById(stone_id)
	self.node_list.rich_tips:SetActive(true)

	-- local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.killer_level)
	self.node_list.tips_1.tmp.text = string.format(Language.Fuhuo.FuHuoTips_1, ToColorStr(self.killer_name or "", COLOR3B.RED))
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.tips_1.rect)
	self.node_list.tips_2.tmp.text =Language.Fuhuo.FuHuoTips_4
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.tips_2.rect)
    self:SetTip7Dec()

    local is_active = RechargeWGData.Instance:IsActiveTZCard(INVEST_CARD_TYPE.StorehouseCard)
	local free_count,all_count = RechargeWGData.Instance:GetFreeFuHuoCount()
	local is_free_fuhuo = is_active and free_count > 0
	if is_free_fuhuo then
		self.node_list.free_lbl.tmp.text = string.format(Language.Fuhuo.FreeBtnStr, free_count, all_count)
	end
    self.is_free_fuhuo = is_free_fuhuo
    self.node_list.text_stone.tmp.text = string.format(Language.Fuhuo.Stone, num <= 999 and num or 999)
    self.node_list.btn_fuhuo_here:SetActive(num <= 0 and not is_free_fuhuo)
    self.node_list.btn_stone_here:SetActive(num > 0 and not is_free_fuhuo)
    self.node_list.btn_free_here:SetActive(is_free_fuhuo)
end

function ShenYuanBossFuhuoView:DaoJiShi(time)
    local relive_time = TimeWGCtrl.Instance:GetServerTime() + time
    self.total_time = time
    
	self.node_list["daojishi_text"].tmp.text = time
	self.node_list["fuhuo_time_slider"].image.fillAmount = 1

	CountDownManager.Instance:RemoveCountDown("shenyuan_fuhuo")
	CountDownManager.Instance:AddCountDown("shenyuan_fuhuo",
		BindTool.Bind1(self.UpdateCountDownTime, self),
		BindTool.Bind(self.CompleteCountDownTime, self, true),
		relive_time, nil, 0.5)
end

-- 倒计时每次循环执行的函数
function ShenYuanBossFuhuoView:UpdateCountDownTime(elapse_time, total_time)
	if not self.node_list["daojishi_text"] then
		return
    end
    local last_time = math.floor(total_time - elapse_time)  
	self.node_list["daojishi_text"].tmp.text = last_time

	if self.node_list["fuhuo_time_slider"] then
		self.node_list["fuhuo_time_slider"].image.fillAmount = last_time / total_time
	end
end

function ShenYuanBossFuhuoView:CompleteCountDownTime(is_auto_fuhuo)
	self.node_list["daojishi_text"].tmp.text = ""
    FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.BACK_HOME, 0, -1)

	if self.node_list["fuhuo_time_slider"] then
		self.node_list["fuhuo_time_slider"].image.fillAmount = 0
	end
end

function ShenYuanBossFuhuoView:SetKillerName(killer_name,killer_level,is_role)
	self.killer_name = killer_name or ''
	self.is_role = is_role or false
	self.killer_level = killer_level or 0
	self:Flush()
end

function ShenYuanBossFuhuoView:FuhuoCallback()
	if self.fuhuo_common_callback and self.fuhuo_type == FuHuoType.Common then
		self.fuhuo_common_callback()
	elseif self.fuhuo_here_callback and self.fuhuo_type == FuHuoType.Here then
		self.fuhuo_here_callback()
	end

	self:Close()
end

function ShenYuanBossFuhuoView:SetFuhuoCallback(common_callback,here_callback)
	self.fuhuo_common_callback = common_callback
	self.fuhuo_here_callback = here_callback
end

function ShenYuanBossFuhuoView:OnClickFuhuoHere()
	if self.is_free_fuhuo then
		FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.TOZI_FREE)
	else
		local gold = FuhuoWGCtrl.Instance:GetFuhuoGold()
		local num = ItemWGData.Instance:GetItemNumInBagById(stone_id)
		if num > 0 then
			FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.HERE_STUFF, 0, -1)
		else
			if not RoleWGData.Instance:GetIsEnoughAllGold(gold) then
				VipWGCtrl.Instance:OpenTipNoGold()
			else
				FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.HERE_ICON, 0, -1)
			end
		end
	end
	self.fuhuo_type = FuHuoType.Here
	-- GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)

	-- -- 原地复活后需要默认挂机状态。增加攻击机制，优先级顺序如下
	-- -- 当前我攻击的玩家
	-- -- 当前攻击我的玩家（最近）
	-- -- 最近可攻击的玩家
	-- -- 攻击boss（怪物）

	-- local select_obj = nil
	-- if GuajiCache.target_obj and GuajiCache.target_obj:IsRole() then
	-- 	select_obj = GuajiCache.target_obj
	-- end
	-- if Scene.Instance:GetSceneType() ~= SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS and Scene.Instance:GetSceneType() ~= SceneType.Shenyuan_boss then
 --        GuajiCache.target_obj = nil
	-- 	MoveCache.target_obj = nil
	-- end
	
	-- if not select_obj then
	-- 	select_obj = ReviveWGData.Instance:GetBeHitData()
	-- end

	-- if select_obj and select_obj:IsRole() and Scene.Instance:IsEnemy(select_obj) then
	-- 	GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, select_obj, SceneTargetSelectType.SELECT)
	-- end
end

function ShenYuanBossFuhuoView:GetUseFuHuoType()
	return self.fuhuo_type
end

function ShenYuanBossFuhuoView:OnclickHandler(param_t)
	ViewManager.Instance:Open(param_t.view_name, param_t.tab_index)
end