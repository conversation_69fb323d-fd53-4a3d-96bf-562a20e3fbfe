HolyDarkWeaponTabbar = HolyDarkWeaponTabbar or BaseClass(BaseRender)
function HolyDarkWeaponTabbar:__init(instance)
    self.ver_tab = {}
    self.ver_obj_list = {}
    self.ver_cell_list = {}

    self.hor_tab = {}
    self.hor_length = {}
    self.hor_obj_list = {}
    self.hor_cell_list = {}

    self.ver_cell_load_complete = false
    self.hor_cell_load_complete = false

    local vertical_tabbar = instance.VerticalTabbar
	if vertical_tabbar then
		self.ver_list = vertical_tabbar.VerticalTabbar
		self.ver_list_content = vertical_tabbar.VerticalTabbarContent
	end

    local horizontal_tabbar = instance.HorizontalTabbar
	if horizontal_tabbar then
		self.hor_list = horizontal_tabbar.HorizontalTabbar
		self.hor_list_content = horizontal_tabbar.HorizontalTabbarContent
	end
end

function HolyDarkWeaponTabbar:__delete()
    self:CleanVerAsyncLoader()
    self:CleanVerObjList()
    self:CleanVerCellList()

    self:CleanHorAsyncLoader()
    self:CleanHorObjList()
    self:CleanHorCellList()
end

function HolyDarkWeaponTabbar:InitList(ver_tab, hor_tab)
    self.ver_tab = ver_tab or {}

    if self.hor_list then
        self.hor_tab = hor_tab or {}
        self.hor_length = {}
        for i , v in pairs(self.hor_tab) do
            self.hor_length[i] = (type(v) == "table") and #v or 0
        end
    end

    self:LoadVerCell()
    self:LoadHorCell()
end

function HolyDarkWeaponTabbar:GetVerListViewNumbers()
    return #self.ver_tab
end

function HolyDarkWeaponTabbar:GetHorListViewNumbers(ver_index)
    return self.hor_length[ver_index] or 0
end

-- 设置标签选择回调
function HolyDarkWeaponTabbar:SetSelectCallback(callback)
    self.tabbar_call_back = callback
end

-- 设置标签创建完成回调
function HolyDarkWeaponTabbar:SetTabbarCompleteCallback(callback)
    self.tabbar_complete_call_back = callback
end

-- 执行标签创建完成回调
function HolyDarkWeaponTabbar:OprateTabbarCompleteCallback()
    if not self.ver_cell_load_complete or not self.hor_cell_load_complete then
        return
    end

    if self.tabbar_complete_call_back then
        self.tabbar_complete_call_back()
    end
end

function HolyDarkWeaponTabbar:CleanVerAsyncLoader()
    if self.ver_res_async_loader then
        self.ver_res_async_loader:Destroy()
        self.ver_res_async_loader = nil
    end
end

function HolyDarkWeaponTabbar:CleanVerObjList()
    for _, v in ipairs(self.ver_obj_list) do
        ResMgr:Destroy(v)
    end

    self.ver_obj_list = {}
end

function HolyDarkWeaponTabbar:CleanVerCellList()
    for _, v in pairs(self.ver_cell_list) do
        v:DeleteMe()
    end

    self.ver_cell_list = {}
end

function HolyDarkWeaponTabbar:LoadVerCell() 
    -- local flem_data = FairyLandEquipmentWGData.Instance
    -- local ver_data = flem_data:GetGodBodyList()
    -- local max_show_num = flem_data:GetGodBodyMaxShowNum()
    local ver_cell_res_str = ""
    local weapon_type = HolyDarkWeaponWGData.Instance:GetCurWeaponType()
    if weapon_type == HolyDarkWeaponWGData.Weapon_type.HolyType then
        ver_cell_res_str = "VerticalTabbarCell_Holy"
    else
        ver_cell_res_str = "VerticalTabbarCell_Dark"
    end
    local ver_num = self:GetVerListViewNumbers()
    self.ver_res_async_loader = AllocResAsyncLoader(self, "holydark_tabbar_ver_load_key")
    self.ver_res_async_loader:Load("uis/view/holy_dark_ui_prefab", ver_cell_res_str, nil, function(new_obj)
        for i = 1, ver_num do
            local obj = ResMgr:Instantiate(new_obj)
            if nil == obj then
                return
            end

            obj.transform:SetParent(self.ver_list_content.transform, false)
            self.ver_obj_list[i] = obj
            local ver_item_cell = HolyDarkWeaponVerRender.New(obj)

            ver_item_cell:SetIndex(i)
            ver_item_cell:SetActive(false)
            ver_item_cell:SetData(self.ver_tab[i])
            ver_item_cell:SetClickCallBack(BindTool.Bind(self.OnClickVerCallBack, self))

            self.ver_cell_list[i] = ver_item_cell
        end

        self.ver_cell_load_complete = true
        if self.cahce_refresh_ver_tabbar then
            self:RefreshVerCellData()
        end

        self:OprateTabbarCompleteCallback()
        self:JumpToTabbar()
    end)
end

-- 刷新一级标签
function HolyDarkWeaponTabbar:TryRefreshVerCellData()
    self.cahce_refresh_ver_tabbar = true
    if self.ver_cell_load_complete then
        self:RefreshVerCellData()
    end
end

-- 刷新一级标签
function HolyDarkWeaponTabbar:RefreshVerCellData()
   -- print_error("---刷新一级标签------")
    local relic_seq = HolyDarkWeaponWGCtrl.Instance:GetHolyDarkViewSelectWeaponSeq()
    local is_wear = HolyDarkWeaponWGData.Instance:GetRelicEquipIsWear(relic_seq)
    self.cahce_refresh_ver_tabbar = nil
    for k, v in pairs(self.ver_cell_list) do
        v:SetData(self.ver_tab[k])
        if k > 2 then
            if k == 5 then -- 屏蔽进阶
                v:SetActive(false)
            else
                v:SetActive(is_wear)
            end
        else
            v:SetActive(true)
        end
    end
end

-- 点击一级标签回调
function HolyDarkWeaponTabbar:OnClickVerCallBack(ver_cell, tab_index)
    local data = ver_cell and ver_cell:GetData()
    if data == nil then
        return
    end

    --print_error("--点击一级标签回调---", tab_index)
    self.cur_select_ver_index = tab_index
    local num = self:GetHorListViewNumbers(math.floor(self.cur_select_ver_index / 10))

    -- 更新显示功能标签
    if self.hor_list_content then
        self.hor_list_content:CustomSetActive(num > 0)
        if num > 0 then
            self:TryRefreshHorCellData()
        else
            for k,v in pairs(self.hor_cell_list) do
                v.view.toggle.isOn = false
            end
        end
    end

    for k,v in pairs(self.ver_cell_list) do
        v:RefreshHL(tab_index)
    end

    -- 选中功能标签
    local hor_index
    local hor_length = self:GetHorListViewNumbers(tab_index / 10)

    if hor_length > 0 then
        if self.cache_hor_bar_index then
            hor_index = self.cache_hor_bar_index % 10
            self.cache_hor_bar_index = nil
        else
            local remind_jump = 11
            hor_index = remind_jump % 10
        end
    else
        self.cache_hor_bar_index = nil
    end

    if hor_index then
        local hor_cell = self.hor_cell_list[hor_index]
        if hor_cell then
            if not hor_cell.view.toggle.isOn then
                hor_cell.view.toggle.isOn = true
            else
                self:OnClickHorCallBack(hor_cell, true)
            end
        end
    else
        if self.tabbar_call_back then
            self.tabbar_call_back(tab_index)
        end
    end
end

function HolyDarkWeaponTabbar:CleanHorAsyncLoader()
    if self.hor_res_async_loader then
        self.hor_res_async_loader:Destroy()
        self.hor_res_async_loader = nil
    end
end

function HolyDarkWeaponTabbar:CleanHorObjList()
    for _, v in ipairs(self.hor_obj_list) do
        ResMgr:Destroy(v)
    end

    self.hor_obj_list = {}
end

function HolyDarkWeaponTabbar:CleanHorCellList()
    for _, v in pairs(self.hor_cell_list) do
        v:DeleteMe()
    end

    self.hor_cell_list = {}
end

function HolyDarkWeaponTabbar:LoadHorCell()
    local hor_num = 0
    for i = 1, #self.ver_tab do
        local num = self:GetHorListViewNumbers(i)
        if num > hor_num then
            hor_num = num
        end
    end

    local hor_cell_res_str = ""
    local weapon_type = HolyDarkWeaponWGData.Instance:GetCurWeaponType()
    if weapon_type == HolyDarkWeaponWGData.Weapon_type.HolyType then
        hor_cell_res_str = "HorizontalTabbarCell_Holy"
    else
        hor_cell_res_str = "HorizontalTabbarCell_Dark"
    end

    self.hor_res_async_loader = AllocResAsyncLoader(self, "holydark_tabbar_hor_load_key")
    self.hor_res_async_loader:Load("uis/view/holy_dark_ui_prefab", hor_cell_res_str, nil, function(new_obj)
        for i = 1, hor_num do
            local obj = ResMgr:Instantiate(new_obj)
            if nil == obj then
                return
            end

            obj.transform:SetParent(self.hor_list_content.transform, false)
            self.hor_obj_list[i] = obj
            local hor_item_cell = HolyDarkWeaponHorRender.New(obj)
            hor_item_cell:SetIndex(i)
            hor_item_cell:SetToggleGroup(self.hor_list.toggle_group)
            hor_item_cell:AddClickEventListener(BindTool.Bind(self.OnClickHorCallBack, self), true)
            self.hor_cell_list[i] = hor_item_cell
        end

        self.hor_cell_load_complete = true
        if self.cahce_refresh_hor_tabbar then
            self:RefreshHorCellData()
        end
        self:OprateTabbarCompleteCallback()
        self:JumpToTabbar()
    end)
end

-- 点击二级标签回调
function HolyDarkWeaponTabbar:OnClickHorCallBack(cell, isOn)
    -- print_error("--点击功能标签回调---", cell and cell:GetIndex(), isOn, self.cur_select_ver_index)
    if cell and isOn then
        local hor_index = cell:GetIndex()
        local ver_index = self.cur_select_ver_index or 10
        if self.tabbar_call_back then
            self.tabbar_call_back(ver_index + hor_index)
        end
    end
end

-- 刷新二级标签
function HolyDarkWeaponTabbar:TryRefreshHorCellData()
    self.cahce_refresh_hor_tabbar = true
    if self.hor_cell_load_complete then
        self:RefreshHorCellData()
    end
end

-- 刷新二级标签
function HolyDarkWeaponTabbar:RefreshHorCellData()
    -- print_error("---刷新二级标签------")
    local ver_index = math.floor(self.cur_select_ver_index / 10)
    local hor_data = self.hor_tab[ver_index] or {}
    local hor_index = 0
    for k,v in pairs(self.hor_cell_list) do
        hor_index = self.cur_select_ver_index + k
        v:SetData({name = hor_data[k], tab_index = hor_index})
    end
end


-- 刷新全部标签
function HolyDarkWeaponTabbar:RefreshAllCellData(refresh_ver, refresh_hor)
    if refresh_ver then
        self:TryRefreshVerCellData()
    end

    if refresh_hor then
        self:TryRefreshHorCellData()
    end
end

-- 标签选择
function HolyDarkWeaponTabbar:TryJumpToTabbar(hor_bar_index)
    self.cache_hor_bar_index = hor_bar_index
    self:JumpToTabbar()
end

function HolyDarkWeaponTabbar:JumpToTabbar()
    if not self.ver_cell_load_complete or not self.hor_cell_load_complete then
        return
    end

    if not self.cache_hor_bar_index then
        return
    end

    local ver_index = self.cache_hor_bar_index
    ver_index = math.floor(ver_index / 10) * 10
    local cell = self.ver_cell_list[math.floor(ver_index / 10)]
    if cell then
        cell:OnClick()
    end
end


-- 一级标签
HolyDarkWeaponVerRender = HolyDarkWeaponVerRender or BaseClass(BaseRender)
function HolyDarkWeaponVerRender:LoadCallBack()
     XUI.AddClickEventListener(self.node_list["click"], BindTool.Bind(self.OnClick, self))
end

function HolyDarkWeaponVerRender:SetClickCallBack(callback)
    self.click_callback = callback
end

function HolyDarkWeaponVerRender:OnClick()
    if not self.data then
        return
    end

    if nil ~= self.click_callback then
        self.click_callback(self, self.index * 10)
    end
end

function HolyDarkWeaponVerRender:RefreshHL(tab_index)
    if self.data == nil then
        return
    end

    local is_select_book =  tab_index == 10 * self.index
    self.node_list["Image"]:CustomSetActive(not is_select_book)
    self.node_list["HLImage"]:CustomSetActive(is_select_book)
end

function HolyDarkWeaponVerRender:OnFlush()
    if self.data == nil then
        return
    end

    self.node_list.nor_text.text.text = self.data
    self.node_list.hl_text.text.text = self.data

    local relic_seq = HolyDarkWeaponWGCtrl.Instance:GetHolyDarkViewSelectWeaponSeq()
    local remind = false
    if self.index == math.floor(TabIndex.holy_dark_seal / 10) then
        remind = HolyDarkWeaponWGData.Instance:ShowRelicSealRemind(relic_seq)
    elseif self.index == math.floor(TabIndex.holy_dark_equip_bag / 10) or self.index == math.floor(TabIndex.holy_dark_equip_skill / 10) then
        if HolyDarkWeaponWGData.Instance:ShowRelicEquipBagRemind(relic_seq) then
            remind = true
        elseif HolyDarkWeaponWGData.Instance:GetRelicSkillRemind(relic_seq) then
            remind = true
        end
    elseif self.index == math.floor(TabIndex.holy_dark_qianghua / 10) then
        remind = HolyDarkWeaponWGData.Instance:GetRelicQiangHuaRemind(relic_seq)
    elseif self.index == math.floor(TabIndex.holy_dark_upstar / 10) then
        remind = HolyDarkWeaponWGData.Instance:GetRelicStarRemind(relic_seq)
    elseif self.index == math.floor(TabIndex.holy_dark_jinjie / 10) then
        remind = HolyDarkWeaponWGData.Instance:GetRelicGradeRemind(relic_seq)
    end
    
    self.node_list.RedPoint:SetActive(remind)
end

-- 二级标签
HolyDarkWeaponHorRender = HolyDarkWeaponHorRender or BaseClass(BaseRender)
function HolyDarkWeaponHorRender:OnFlush()
    if self.data == nil then
        return
    end

    self.node_list.nor_text.text.text = self.data.name
    self.node_list.hl_text.text.text = self.data.name

    local remind = false
    local tab_index = self.data.tab_index
    local relic_seq = HolyDarkWeaponWGCtrl.Instance:GetHolyDarkViewSelectWeaponSeq()
    if tab_index == TabIndex.holy_dark_equip_bag then
        remind = HolyDarkWeaponWGData.Instance:ShowRelicEquipBagRemind(relic_seq)
    elseif tab_index == TabIndex.holy_dark_equip_skill then
        remind = HolyDarkWeaponWGData.Instance:GetRelicSkillRemind(relic_seq)
    end
     self.node_list.remind:SetActive(remind)
end