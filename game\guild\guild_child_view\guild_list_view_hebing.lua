GuildListHeBing = GuildListHeBing or BaseClass(SafeBaseView)

--帮派合并界面
-- 初始化战盟列表界面
function GuildListHeBing:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_combinelist")
end

function GuildListHeBing:ReleaseCallBack()
	if self.guild_list then
		self.guild_list:DeleteMe()
		self.guild_list = nil
	end
	if self.btn_list_find then
		self.btn_list_find:DeleteMe()
		self.btn_list_find = nil
	end
end

function GuildListHeBing:LoadCallBack(index, loaded_times)
	self.guild_name = self.node_list["guild_name"]
	self.guild_role_name = self.node_list["guild_role_name"]
	self.guildlist_view = self.node_list.layout_combinelist
	self.btn_list_find = self.guildlist_view.btn_find_guild
	self.btn_combine = self.guildlist_view.btn_combine

	self.select_guildlist_item = nil
	self.can_enlist_guild = true
	self:CreateGuildList()

	self:RegisterGuildListEvent()
end

function GuildListHeBing:OnClickBtnHeBingTip()
end

-- 创建列表控件
function GuildListHeBing:CreateGuildList()
	if nil == self.guild_list then
		self.guild_list = AsyncListView.New(CombineListItem, self.node_list["ph_list"])
		self.guild_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectGuildListItemHandler, self))
		self:FlushGuildListDatasource()
	end
end


function GuildListHeBing:OnSelectGuildListItemHandler(item)
	if nil == item then
		return
	end
	if nil == item.data then
		return
	end

	self.select_guildlist_item = item
	self.can_enlist_guild = item.data.can_enter
	GuildWGData.Instance:SetGuildMemberSelectIndex(item:GetIndex())
	local role_vo = GameVoManager.Instance:GetMainRoleVo()

	GuildDataConst.GUILD_IOPEN.FlushGuildListShow = true
	GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_INFO, self.select_guildlist_item.data.guild_id)
end


function GuildListHeBing:FlushGuildListShow(guildvo)
	if nil == guildvo then
		return
	end
	self.node_list.lbl_xianmeng_name.text.text = guildvo.guild_name
	self.node_list.lbl_mengzhu_name.text.text = guildvo.mengzhu_name

	local text = string.format(Language.Guild.Autopass, guildvo.applyfor_need_level, guildvo.applyfor_need_capability)
	EmojiTextUtil.ParseRichText(self.node_list.rich_guild_requirement, text, 20, COLOR3B.L_ORANGE)

	local exp_str = ""
	if guildvo.guild_exp == nil or guildvo.guild_max_exp == nil then
		exp_str = "-/-"
	else
		exp_str = string.format(Language.Guild.XX_XX, guildvo.guild_exp, guildvo.guild_max_exp)
	end
	EmojiTextUtil.ParseRichText(self.node_list.rich_build_val, exp_str, 20)

	local member_str = string.format(Language.Guild.XX_XX, guildvo.cur_member_count, guildvo.max_member_count)
	EmojiTextUtil.ParseRichText(self.node_list.rich_member_val, member_str, 20)

	EmojiTextUtil.ParseRichText(self.node_list.rich_guild_bulletin, guildvo.guild_notice, 20)

	self.node_list.lbl_list_guild_level.text.text = guildvo.guild_level
end

-- 注册战盟列表界面事件
function GuildListHeBing:RegisterGuildListEvent()
	self.btn_combine.button:AddClickListener(BindTool.Bind1(self.OnHeBingGuildHandler, self))
	self.btn_list_find.button:AddClickListener(BindTool.Bind1(self.OnFindGuildHandler, self))
end

-- 查找战盟事件
function GuildListHeBing:OnFindGuildHandler()
	self:FlushGuildListDatasource()
end

-- 合并战盟事件
function GuildListHeBing:OnHeBingGuildHandler()
	if self.select_guildlist_item ~= nil then
		GuildWGCtrl.Instance:SendGuildMergeReq(self.select_guildlist_item.data.guild_id)
	end
end

-- 申请加入战盟事件
function GuildListHeBing:OnRequestEnterGuildHandler()
	if nil == self.select_guildlist_item then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.SelectItem)
		return
	end

	if nil == self.select_guildlist_item.data then
		return
	end
	if true == self.can_enlist_guild then
		GuildWGCtrl.Instance:SendJoinGuildReq(self.select_guildlist_item.data.guild_id)
	end
end

-- 刷新仙盟列表数据源
function GuildListHeBing:FlushGuildListDatasource()
	local info_list = GuildWGData.Instance:GetGuildListData()
	self.guild_datasource = {}

	for i=1, info_list.count do
		local info = info_list.list[i]
		if "" ~= self.guild_name.input_field.text and "" ~= self.guild_role_name.input_field.text then
			if nil ~= string.find(info.guild_name, self.guild_name.input_field.text) and nil ~= string.find(info.mengzhu_name, self.guild_role_name.input_field.text) then
				table.insert(self.guild_datasource, info)
			end
		elseif "" ~= self.guild_name.input_field.text or "" ~= self.guild_role_name.input_field.text then
			if "" ~= self.guild_name.input_field.text then
				if nil ~= string.find(info.guild_name, self.guild_name.input_field.text) then
					table.insert(self.guild_datasource, info)
				end
			end
			if "" ~= self.guild_role_name.input_field.text then
				if nil ~= string.find(info.mengzhu_name, self.guild_role_name.input_field.text) then
					table.insert(self.guild_datasource, info)
				end
			end
		else
			table.insert(self.guild_datasource, info)
		end
	end

	if #self.guild_datasource > 1 then
		table.sort(self.guild_datasource, BindTool.Bind1(self.SortGuildList, self))
	end

	if nil ~= self.guild_list then
		self.guild_list:SetDataList(self.guild_datasource,1)
		self:ClickItemByGuide(1)
	end
end


-- 排序数据源
function GuildListHeBing:SortGuildList(a, b)
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	if 0 == role_vo.camp then
		if a.guild_level == b.guild_level then
			return a.cur_member > b.cur_member
		else
			return a.guild_level > b.guild_level
		end
	else
		if a.camp == b.camp then
			if a.guild_level == b.guild_level then
				return a.cur_member > b.cur_member
			else
				return a.guild_level > b.guild_level
			end
		else
			if role_vo.camp == a.camp then
				return true
			elseif role_vo.camp == b.camp then
				return false
			else
				return a.camp < b.camp
			end
		end
	end
end

-- 刷新战盟列表界面数据
function GuildListHeBing:OnFlush(param_t, index)
	self:FlushGuildListDatasource()
end

function GuildListHeBing:ClickItemByGuide(index)
	self.guild_list:SelectIndex(index)
end


CombineListItem = CombineListItem or BaseClass(BaseRender)
function CombineListItem:__init()
end

function CombineListItem:CreateChild()
	BaseRender.CreateChild(self)
	self.rich_text = nil
end

function CombineListItem:OnFlush()
	if nil == self.data then
		return
	end
	self.node_list.hight:SetActive(self.index == GuildWGData.Instance:GetGuildMemberSelectIndex())
	self.node_list.lbl_guild_name.text.text = self.data.guild_name
	print_error(self.data)
	self.node_list.lbl_mengzhu_name.text.text = self.data.mengzhu_name
	self.node_list.lbl_level.text.text = self.data.guild_level
	self.node_list.lbl_member.text.text = self.data.cur_member_num .. "/" .. self.data.max_member_num

	local setting_model = GuildDataConst.GUILD_SETTING_MODEL
	if nil ~= setting_model then
		local text = ""
		if self.data.applyfor_setup == setting_model.APPROVAL then
			text = Language.Guild.Approval
		elseif self.data.applyfor_setup == setting_model.FORBID then
			text = Language.Guild.ForbidEnlist
		elseif self.data.applyfor_setup == setting_model.AUTOPASS then
			text = string.format(Language.Guild.Autopasss, self.data.applyfor_need_level, self.data.applyfor_need_capability)
		end
		self.node_list.lbl_codition.text.text = text
	end
end
