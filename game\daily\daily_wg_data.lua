DailyWGData = DailyWGData or BaseClass()

ACTIVITY_SHOW_PAGENUM = 9

TUMO_COMMIT_TIMES = 20 --屠魔提交最大次数
PAO_HUAN_TIMES = 100 -- 跑环最大次数

COMPLETE_STATUS =
{
	DEFAULT = 1, 		--（默认）条件未达到
	YILINGQU = 0, 		-- 已领取
	WEILINGQU = 2, 		-- 未领取
}

ChallengeFb = {
	MAX_LEVEL = 6,
	MAX_LAYER = 10,
	FREE_JOIN_MAX_TIMES = 2,
	}

Welfarefb_Type = {
	Pet = 0,
	Coin = 1,
	Wing = 2,
	Meridian = 3,
}
function DailyWGData:__init()
	if DailyWGData.Instance then
		error("[DailyWGData] Attempt to create singleton twice!")
		return
	end
	DailyWGData.Instance = self

	--BOSS信息
	self.boss_datas = {}
	self.boss_config = {}

	--日常奖励信息(活跃度奖励)
	self.reward_datas = {}
	self:GetRewardConfigList() -- 先读配置，面板未打开也会用到啊。亲们

	--日常任务信息
	self.task_tumo_data = DailyWGData.CreateTaskVo()

	--跑环任务信息
	self.task_paohuan_data = DailyWGData.CreateTaskVo()

	self.daily_cfg = ConfigManager.Instance:GetAutoConfig("daily_activity_auto")
	self.daily_other_cfg = self.daily_cfg.other[1]
	self.button_sort_cfg = ListToMapList(self.daily_cfg.button_sort, "act_pos_id")
	self.button_name_cfg = ListToMap(self.daily_cfg.button_sort, "btn_name")

	self.is_auto_task = false
	self.is_double_task = false
	self.jq_auto_to_nvwashi = false
	self.welkin_pass_level = 0
	self.welkin_pass_fetch_level = 0

	self.remind_num_list = {}
	self.daily_fb_role_info = {}
	self.teamfb_room_list = {}
	self.teamfb_enter_times = {}
	self.equip_fb_info = {
		open_level = 0,
		level_list = {}
}
	self.roll_item_list = {} 		--跑环奖池
	self.lingyufb_info = { 			--灵玉副本
		join_times = 0,
		buy_join_times = 0,
		item_buy_join_times = 0,
		free_autofb_times = 0,
		level_list = {},
	}
	self.lingyufb_scene_info = {
		level = 0,
		layer = 0,
		time_out_stamp = 0,
		is_finish = 0,
		is_pass = 0,
		pass_time_s = 0,
	}

	self.peteqfb_info = {
		remain_day_times = 0,
		buy_times = 0,
		open_chapter = 0,
		open_level = 0,
		next_auto_add_times_timestamp = 0,
		level_list = {},
		is_fetched_list = {},
	}
	self.cur_peteqfb_level = 0
	self.cur_peteqfb_chapter_level = {}

	self.high_level = 0
	self.high_cur_level = 0
	self.can_auto_max_level = 0

	self.wushuang_level_info = {
		pass_level = 0,
		has_fetch_day_reward = 1
	}

	-- 用于判断是不是收到了服务端协议，因为之前有默认值，会导致逻辑有问题
	self.is_has_data = false
end

function DailyWGData:__delete()
	self.boss_datas = nil
	self.boss_config = nil
	self.task_tumo_data = nil
	self.task_paohuan_data = nil
	self.max_tower_wave = nil
	self.is_has_data = false
	DailyWGData.Instance = nil
end

function DailyWGData:GetFbRemind()
	return self:GetEquipFbRemind() + self:GetPeteqRemind() + self:LingyuFbRemind() + self:GetDailyFbRemind() + self:GetTerraceRewardRemind() + self:TeamFbRemind()

end

function DailyWGData:GetRemindNum(remind_id)
	return self.remind_num_list[remind_id]
end

---------------------------------------------------------------------------------日常奖励(先不删，有参考价值)
-- 获取符合条件显示的列表
function DailyWGData:GetRewardConditionList()
	local source = self.reward_datas
	local list = {}
	for k, v in pairs(source) do
		if self:GetRewardIsShow(v) then
			table.insert(list, v)
		end
	end
	return list
end

--获得奖励是否显示
function DailyWGData:GetRewardIsShow(reward_cfg)
	if reward_cfg == nil then return false end

	local flag = false
	if FunOpen.Instance:GetFunIsOpened(reward_cfg.fun_name, true) then  --根据功能开启名字获得活动类型
		local i, j = string.find(reward_cfg.fun_name, "activity_")		--活动类型的只显示当天会开启的活动
		if i ~= nil and j ~= nil then
			local activity_type = tonumber(string.sub(reward_cfg.fun_name, j + 1, #reward_cfg.fun_name))
			activity_type = activity_type or 0
			flag = ActivityWGData.Instance:GetActivityIsInToday(activity_type)

			local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or -1
			if open_day > 0 and open_day <= 3 then							--开服前三天
				if activity_type == ACTIVITY_TYPE.XIANMENGZHAN then
					flag = (2 == open_day)
				end
			end
		else
			flag = true
		end
	end
	return flag
end

function DailyWGData.CreateRewardVo(info)
 	local vo = {}
 	info = info or {}
    vo.type = info.type or 0
	vo.status = COMPLETE_STATUS.DEFAULT
	vo.need_count = info.need_count or 0
	vo.degree_num = info.degree_num or 0
	vo.reward_flag = info.reward_flag or 0
	vo.coin = info.coin or 0
	vo.bind_gold = info.bind_gold or 0
	vo.icon_name = info.icon_name or ""
	vo.name = info.name or ""
	vo.explain = info.explain or ""
	vo.reward_item = info.reward_item or {}
	vo.show_con = info.show_con or {}
	vo.show_param = info.show_param or {}
	vo.show_priority = info.show_priority or 0
	vo.fun_name = info.fun_name or ""
	vo.open_panel_name = info.open_panel_name or ""
	vo.item_vo = info["reward_item_" .. RoleWGData.Instance.role_vo.vip_level]
	return vo
end

function DailyWGData:GetRewardConfig(act_type)
	local reward = ConfigManager.Instance:GetAutoConfig("activedegree_auto").reward
	for k,v in pairs(reward) do
		if  v.type == act_type then
			return DailyWGData.CreateRewardVo(v)
		end
	end
	return nil
end

function DailyWGData:GetRewardConfigList()
	if self.reward_datas ~= nil and #self.reward_datas ~= 0 then
		return self.reward_datas
	end

	for k,v in pairs(ConfigManager.Instance:GetAutoConfig("activedegree_auto")) do
		table.insert(self.reward_datas, DailyWGData.CreateRewardVo(v))
	end
	return self.reward_datas
end

function DailyWGData:GetRewardItems(act_type)
	local vo = self:GetRewardConfig(act_type)
	if not vo then
		return nil
	end
	local bind_goldvo = CommonStruct.BindGoldDataWrapper(vo.bind_gold)
	local item_vo = vo.item_vo
	local items = {}
	items[0] = bind_goldvo
	items[1] = item_vo
	return items
end

function DailyWGData:GetTotalDegree()
	local total_degree = 0

	for k, v in pairs(self.reward_datas) do
		local degree_num = self:GetRewardTime(v.type)
		if nil ~= degree_num and nil ~= v.need_count and degree_num >= v.need_count then
			total_degree = total_degree + 1
		end
	end

	return total_degree
end


----------------------------------------------------------------------日常活动

function DailyWGData:GetActivityConfig(act_type)
	return ActivityWGData.Instance:GetActivityCfgByType(act_type)
end


-------------------------------------------------------------------
----------日常任务
-------------------------------------------------------------------

function DailyWGData.CreateTaskVo(info)
	info = info or {}
	local vo = {
		notify_reason = info.notify_reason or 0,	--通知原因 TUMO_NOTIFY_REASON_TYPE
		commit_times = info.commit_times or 1, 		--提交任务次数 最大次数是20次
		is_accept = info.is_accept or 0, 			--任务是否已经被接受(领取)
		task_id = info.task_id or 0, 				--任务iD
		has_fetch_complete_all_reward = info.has_fetch_complete_all_reward or 0, --1：领取10环 2：领取 20 环 3:表示都领取了
		star_level = info.star_level or 0, 			--星星等级
		frist_task_flag = info.frist_task_flag or 0 -- 首个任务接取标记
	}
	return vo
end

-- 是否领取屠魔任务
function DailyWGData:GetIsAcceptTask()
	return self.task_tumo_data.is_accept == 1
end

function DailyWGData:GetTaskCommintTimes()
	return self.task_tumo_data.commit_times
end

-- 是否完成屠魔任务
function DailyWGData:GetIsAllCommitTask()
	return self.task_tumo_data.commit_times > TUMO_COMMIT_TIMES
end

function DailyWGData:GetIsHasData()
	return self.is_has_data
end

function DailyWGData:SetTaskTuMoData(protocol)
	self.is_has_data = true
	
	self.task_tumo_data = DailyWGData.CreateTaskVo(protocol)

	if self:GetIsAllCommitTask() then
		TaskWGData.Instance:QuickAllTaskCompleted(GameEnum.TASK_TYPE_RI)
	else
		TaskWGCtrl.Instance:UpdateTaskPanelShow()
	end
end

function DailyWGData:GetTaskTuMoData()
	return self.task_tumo_data
end

-- 完成一环赏金任务之后是否需要打开赏金任务面板
-- 需求 赏金任务达成领取条件后，只有第10环，第20环才弹出赏金任务界面，其他环数不弹
function DailyWGData:NeedOpenShangjinView()
    local is_vippower = VipPower.Instance:GetHasPower(VipPowerId.daily_task_double)
    local task_data = self:GetTaskTuMoData()
    local commit_times = task_data.commit_times
    local is_show_shangjin = commit_times % 10 == 0 --or not is_vippower
    return is_show_shangjin
end

-- 是否有屠魔任务
function DailyWGData:HasTaskTuMo()
	return self.task_tumo_data.task_id ~= 0
end

function DailyWGData:GetMapName()
	local scene_id = TaskWGData.Instance:GetSceneId(self.task_tumo_data.task_id)
	if scene_id and Config_scenelist[scene_id] then
		return Config_scenelist[scene_id].name or ""
	end
	return ""
end

function DailyWGData:GetTuMoReward(role_level)
	local lev = RoleWGData.Instance.role_vo.level
	role_level = role_level or lev
	local daily_task_reward = ConfigManager.Instance:GetAutoConfig("tasklist_auto").daily_task_reward
	for k,v in pairs(daily_task_reward) do
		if role_level >= v.level and role_level <= v.level_max then
			return v
		end
	end
end

function DailyWGData:GetShangJinExpReward()
	local cfg = TaskWGData.Instance:GetRoleLevelReward(RoleWGData.Instance:GetRoleLevel())
	if cfg then
		local shangjin_task_reward_cfg = DailyWGData.Instance:GetTuMoReward(RoleWGData.Instance:GetRoleLevel())
		if shangjin_task_reward_cfg then
			return cfg.kill_monster_exp * shangjin_task_reward_cfg.role_exp
		end
	end
	return 0
end

------------------跑环--------------------------

function DailyWGData:SetTaskPaohuanData(protocol)
	self.task_paohuan_data = DailyWGData.CreateTaskVo(protocol)
end

function DailyWGData:GetTaskPaohuanData()
	return self.task_paohuan_data
end

function DailyWGData:GetHuanRewardRemind()
	return 0
end

-- 是否有跑环任务
function DailyWGData:IsTaskPaohuan(task_id)
	if task_id == 0 then
		return false
	end
	return self.task_paohuan_data.task_id == task_id
end


-- 是否有跑环任务id
function DailyWGData:GetPaohuanTaskid()
	return self.task_paohuan_data.task_id
end

-- 是否领取跑环任务
function DailyWGData:GetIsAcceptPaohuanTask()
	return self.task_paohuan_data.is_accept == 1
end

function DailyWGData:GetPaohuanTaskCommintTimes()
	return self.task_paohuan_data.commit_times
end

-- 是否完成跑环任务
function DailyWGData:GetIsAllCommitPaohuanTask()
	return self.task_paohuan_data.commit_times == TUMO_COMMIT_TIMES
end

function DailyWGData:GetPaohuanMapName()
	local scene_id = TaskWGData.Instance:GetSceneId(self.task_paohuan_data.task_id)
	if scene_id and Config_scenelist[scene_id] then
		return Config_scenelist[scene_id].name or ""
	end
	return ""
end

function DailyWGData:GetPaohuanReward(huan)
	local paohuan_reward_cfg = ConfigManager.Instance:GetAutoConfig("tasklist_auto").paohuan_task_reward
	for k,v in pairs(paohuan_reward_cfg) do
		if v.huan == huan then
			local per = self:GetPaohuanRewardFix()
			return math.floor(v.reward_nvwashi * per / 100)
		end
	end
	return 0
end

function DailyWGData:GetPaohuanRewardByHuan(huan)
	local paohuan_reward_cfg = ConfigManager.Instance:GetAutoConfig("tasklist_auto").paohuan_task_reward
	for k,v in pairs(paohuan_reward_cfg) do
		if v.huan == huan then
			return v
		end
	end
	return nil
end

function DailyWGData:GetPaohuanRewardFix(level)
	level = level or GameVoManager.Instance:GetMainRoleVo().level
	local paohuan_reward_fix = ConfigManager.Instance:GetAutoConfig("tasklist_auto").paohuan_reward_fix
	for i,v in ipairs(paohuan_reward_fix) do
		if level >= v.min_level and level <= v.max_level then
			return v.nvwashi_addpercent
		end
	end
	return 100
end

function DailyWGData:SetPaohuanPoolList(list)
	self.roll_item_list = list
end

function DailyWGData:GetPaohuanPoolReward(index)
	return self.roll_item_list[index]
end

function DailyWGData:GetPaohuanRemind()
	local paohuan_remind = math.max(PAO_HUAN_TIMES - self.task_paohuan_data.commit_times, 0)
	paohuan_remind = math.ceil(paohuan_remind/10)
	return paohuan_remind
end

function DailyWGData:GetPaohuanRewardShowCfg(reward_count)
	local paohuan_show_cfg = ConfigManager.Instance:GetAutoConfig("tasklist_auto").pahuan_reward_show
	return paohuan_show_cfg[reward_count + 1]
end

function DailyWGData:UpdateTask(change_task_id, reason)
	local task_cfg = TaskWGData.Instance:GetTaskConfig(change_task_id)
	if task_cfg then
		if task_cfg.task_type == GameEnum.TASK_TYPE_RI then
			local task_status = TaskWGData.Instance:GetTaskStatus(change_task_id)
			if task_status == GameEnum.TASK_STATUS_COMMIT then
				--self.remind[DAILY_TYPE_TASK] = self.remind[DAILY_TYPE_TASK] + 1
			end
		end
	end
end

--是否有足够的元宝进行自动任务
function DailyWGData:IsGoldEnoughToAutoAll(commit_times)
	-- if commit_times > TUMO_COMMIT_TIMES then
	-- 	return false
	-- end
	-- local gold = self:GetTuMoTaskOtherConfig().daily_double_gold
	-- local remain_times = TUMO_COMMIT_TIMES - commit_times
	-- local cost_gold = gold * remain_times

	-- if false == RoleWGData.Instance:GetIsEnoughUseGold(cost_gold) then
	-- 	return false, need_gold
	-- end
	-- return true, need_gold
end

--是否有足够的元宝自动进行双倍任务
function DailyWGData:IsGoldEnoughToDoubleTask()
	local cost_gold = self:GetTuMoTaskOtherConfig().daily_double_gold
	return RoleWGData.Instance:GetIsEnoughUseGold(cost_gold)
end

function DailyWGData:GetTuMoTaskOtherConfig()
	return ConfigManager.Instance:GetAutoConfig("tasklist_auto").other[1]
end

function DailyWGData:SetIsDoubleTask(is_double_task)
	self.is_double_task = is_double_task
end

function DailyWGData:GetIsDoubleTask()
	return self.is_double_task
end

function DailyWGData:SetIsAutoTask(is_auto_task)
	self.is_auto_task = is_auto_task
	-- TaskGuide.Instance:SetIsOpenAutoDailyTask(self.is_auto_task)
end

function DailyWGData:GetIsAutoTask()
	return self.is_auto_task
end

function DailyWGData:GetTaskTuMoRewardRate(star_level)
	local task_star_cfg = ConfigManager.Instance:GetAutoConfig("tasklist_auto").task_star
	if task_star_cfg[star_level] and task_star_cfg[star_level].reward_rate then
		return task_star_cfg[star_level].reward_rate / 100
	else
		return 0
	end
end

--------------------------------------------------------------
--装备副本
--------------------------------------------------------------
function DailyWGData:OnEquipFbInfo(info)
	self.equip_fb_info.open_level = info.open_level
	self.equip_fb_info.level_list = info.level_list
	self.equip_fb_info.buy_times_list = info.buy_times_list
end

function DailyWGData:GetEqFbOpenLevel()
	return self.equip_fb_info.open_level
end

function DailyWGData:GetEqFbOpenChapterList()
	return self.equip_fb_info.level_list
end

-- 获取购买次数表
function DailyWGData:GetEqFbChapterBuyList()
	return self.equip_fb_info.buy_times_list
end

-- 获取章节已购买次数
function DailyWGData:GetEqFbChapterBuyTimes(chapter)
	return self:GetEqFbChapterBuyList()[chapter].times
end

-- 获取购买价格
function DailyWGData:GetEqFbChapterBuyCost(chapter)
	return ConfigManager.Instance:GetAutoConfig("equipfbconfig_auto").level[chapter + 1].buy_count_cost
end


function DailyWGData.GetChapterNameList()
	local list = {}
	for i,v in ipairs(ConfigManager.Instance:GetAutoConfig("equipfbconfig_auto").level) do
		table.insert(list, v.name)
	end
	return list
end

function DailyWGData.GetEquipFbChapterCfg(chapter)
	for i,v in ipairs(ConfigManager.Instance:GetAutoConfig("equipfbconfig_auto").level) do
		if v.level == chapter then
			return v
		end
	end
	return nil
end

function DailyWGData:GetEquipFbRemind()
	local num = 0
	for k,v in pairs(self.equip_fb_info.level_list) do
		local chapter_cfg = DailyWGData.GetEquipFbChapterCfg(v.level)
		local role_lv = RoleWGData.Instance.role_vo.level
		if chapter_cfg and chapter_cfg.role_level <= role_lv and v.day_times < chapter_cfg.day_times and v.level <= self.equip_fb_info.open_level then
			local has_stuff = ItemWGData.Instance:GetItemNumInBagById(chapter_cfg.stuff_id)
			if has_stuff >= chapter_cfg.stuff_num then
				num = num + chapter_cfg.day_times - v.day_times
			end
		end
	end
	return num
end

---------------------------------------------------
--福利副本
---------------------------------------------------
function DailyWGData:GetRiChangGridListData()
	local fb_list = ConfigManager.Instance:GetAutoConfig("welfarefb_auto").fb_list
	return fb_list
end
function DailyWGData:GetRiChangGridListDataByIndex(index)
	local fb_list = ConfigManager.Instance:GetAutoConfig("welfarefb_auto").fb_list
	for k,v in pairs(fb_list) do
		if v.fb_index == index then
			return v
		end
	end
	return nil
end

function DailyWGData:OnDailyFbFBRoleInfo(protocol)
	self.daily_fb_role_info = protocol.fb_list
end

function DailyWGData:GetDailyFbFBRoleInfo(dailyfb_type)
	return dailyfb_type and self.daily_fb_role_info[dailyfb_type] or self.daily_fb_role_info
end

function DailyWGData:GetDailyFbRemind()
	local num = 0
	local role_level = RoleWGData.Instance.role_vo.level
	local role_level_enough = false

	for k,v in pairs(self.daily_fb_role_info) do

		local fb_cfg = self:GetRiChangGridListDataByIndex(v.fb_index)
		if fb_cfg then
			role_level_enough = role_level >= fb_cfg.role_level
			if fb_cfg and fb_cfg.day_times - v.day_times > 0 and role_level_enough then
				num = num + 1
			end
		end
	end
	return num
end

---------------------------------------------------
--多人副本
---------------------------------------------------
function DailyWGData:OnTeamFbRoomInfo(protocol)
	self.teamfb_room_list[protocol.team_type] = protocol.room_list
	--Log("房间列表", protocol.team_type)
end

function DailyWGData:GetTeamFbRoomList(team_type)
	return self.teamfb_room_list[team_type] or {}
end

function DailyWGData:GetMyTeamFbRoomInfo()
	local team_info = SocietyWGData.Instance:GetTeamList()
	if team_info.team_type < 1 then
		return nil
	end
	local room_list = self:GetTeamFbRoomList(team_info.team_type)
	for k,v in pairs(room_list) do
		if v.team_index == team_info.team_index then
			return v
		end
	end
end

function DailyWGData:SetTeamFbEnterTimes(team_type, enter_times)
	self.teamfb_enter_times[team_type] = enter_times
	Log("DailyWGData:SetTeamFbEnterTimes---",  team_type, enter_times)
end

function DailyWGData:GetTeamFbLeftTimes(team_type)
	local join_times_limit = 0
	local times2 = 0
	if team_type == TEAM_TYPE.WUJINJITAN then
		local other = ConfigManager.Instance:GetAutoConfig("teamwujinjitanfbconfig_auto").other
		join_times_limit = other[1].open_times
		times2 = other[1].assist_reward_times
	elseif team_type == TEAM_TYPE.DUORENTAFANG then
		local other = ConfigManager.Instance:GetAutoConfig("teamtowerdefendconfig_auto").other
		join_times_limit = other[1].day_times
	end
	if nil == self.teamfb_enter_times[team_type] then
		return 0, 0
	end
	if self.teamfb_enter_times[team_type * 100] then
		times2 = times2 - self.teamfb_enter_times[team_type * 100]
	end
	return join_times_limit - self.teamfb_enter_times[team_type], times2 or 0
end

function DailyWGData:GetReamTowerMaxWave()
	local team_wave_list = ConfigManager.Instance:GetAutoConfig("teamtowerdefendconfig_auto").wave
	return #team_wave_list
end

function DailyWGData:GetTeamFbTypeList()
	local team_fuben = ConfigManager.Instance:GetAutoConfig("daily_fuben_auto").team_fuben
	local team_fb_type_list = __TableCopy(team_fuben)
	return team_fb_type_list
end

function DailyWGData:GetTeamFbTypeListIndex(the_type)
	local team_fuben = ConfigManager.Instance:GetAutoConfig("daily_fuben_auto").team_fuben
	for k,v in pairs(team_fuben) do
		if v.team_type == the_type then
			return v.fb_index
		end
	end
	return 1
end

function DailyWGData.UpdataTeamFbTipIcon()
	-- local icon = MainuiWGCtrl.Instance:GetTipIcon(MAINUI_TIP_TYPE.TEAM_FB)
	-- local team_type = SocietyWGData.Instance:GetTeamList().team_type or 0
	-- local scene_type = Scene.Instance:GetSceneType() or 0
	-- local num = 0
	-- if team_type > 0 and scene_type == SceneType.Common then
	-- 	num = 1
	-- end

	-- if nil == icon or not icon:IsVisible() or num == 0 then
	-- 	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_FB, num, function ()
	-- 		if team_type == TEAM_TYPE.WUJINJITAN then
	-- 			FunOpen.Instance:OpenViewByName(GuideModuleName.Wujinjitan)
	-- 		-- elseif team_type == TEAM_TYPE.DUORENTAFANG then
	-- 		-- 	FunOpen.Instance:OpenViewByName(GuideModuleName.ManyTowerFB)
	-- 		-- elseif team_type == TEAM_TYPE.ZHUANZHI then
	-- 			-- FunOpen.Instance:OpenViewByName(GuideModuleName.TransferProfTeam)
	-- 		end
	-- 	end)
	-- end
end

function DailyWGData:TeamFbRemind()
	local num = 0
	local team_fuben = ConfigManager.Instance:GetAutoConfig("daily_fuben_auto").team_fuben
	for k,v in pairs(team_fuben) do
		local times = DailyWGData.Instance:GetTeamFbLeftTimes(v.team_type)
		times = RoleWGData.Instance.role_vo.level >= v.level_limit and times or 0
		num = num + times
	end
	return num
end

-------宠物品质副本--------
function DailyWGData:SetLingyuFbInfo(info)
	self.lingyufb_info.join_times = info.join_times
	self.lingyufb_info.buy_join_times = info.buy_join_times
	self.lingyufb_info.item_buy_join_times = info.item_buy_join_times
	self.lingyufb_info.free_autofb_times = info.free_autofb_times
	self.lingyufb_info.level_list = info.level_t
end

function DailyWGData:GetLingyuFbInfo()
	return self.lingyufb_info
end

function DailyWGData:GetLingyuFbLevelList()
	return self.lingyufb_info.level_list
end

function DailyWGData:GetChaptercfg(level)
	local chaptercfg = ConfigManager.Instance:GetAutoConfig("challengefbcfg_auto").chaptercfg
	for k,v in pairs(chaptercfg) do
		if v.level == level then
			return v
		end
	end
	return nil
end

function DailyWGData:GetLingyuFbBuyCost(times)
	local buy_cost_cfg = ConfigManager.Instance:GetAutoConfig("challengefbcfg_auto").buy_cost
	for k,v in pairs(buy_cost_cfg) do
		if v.buy_times == times then
			return v.gold_cost
		end
	end
	return 0
end

function DailyWGData:SetLingyuSceneInfo(info)
	self.lingyufb_scene_info.level = info.level
	self.lingyufb_scene_info.layer = info.layer
	self.lingyufb_scene_info.time_out_stamp = info.time_out_stamp
	self.lingyufb_scene_info.is_finish = info.is_finish
	self.lingyufb_scene_info.is_pass = info.is_pass
	self.lingyufb_scene_info.pass_time_s = info.pass_time_s
end

function DailyWGData:GetLingyuSceneTaskInfo()
	local fb_task_data = {}
	fb_task_data[1] = {
		tab_img = "word_fuben_info",
		text_t = {
			{str = string.format(Language.FuBen.FubenJindu, self.lingyufb_scene_info.layer + 1, ChallengeFb.MAX_LAYER)},
		},
	}
	return fb_task_data
end

function DailyWGData:LingyuFbRemind()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("challengefbcfg_auto").other[1]
	return math.max(self.lingyufb_info.buy_join_times + other_cfg.canchallenge_num - self.lingyufb_info.join_times, 0)
end




------天仙阁---------

-- 设置当前通过层数
function DailyWGData:SetPassLevel(level)
	self.welkin_pass_level = level
end

-- 获取当前通过层数
function DailyWGData:GetPassLevel()
	if nil == self.welkin_pass_level or self.welkin_pass_level < 0
		or self.welkin_pass_level > #ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").level_cfg then
		return -1
	end
	return self.welkin_pass_level
end

-- 设置下一层通过层数
function DailyWGData:SetPassFetchLevel(fetch_level)
	self.welkin_pass_fetch_level = fetch_level
end

-- 获取下一层通过层数
function DailyWGData:GetPassFetchLevel()
	return self.welkin_pass_fetch_level
end

-- 获取配置上最大等级
function DailyWGData:GetCfgMaxLevel()
	return #ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").level_cfg
end

-- 是否达到顶层
function DailyWGData:IsReachMaxLevel()
	return self.welkin_pass_level == #ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").level_cfg
end

-- 根据等级获取试炼称号
function DailyWGData:GetCurTitleName(level)
	local cur_level = level or self.welkin_pass_level
	if nil == ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").level_cfg[cur_level] then return "" end
	return ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").level_cfg[cur_level].title_name
end

-- 根据等级获取当前站台信息
function DailyWGData:GetTerraceListCfgByLevel(level)
	local terrace_list = {}
	if self:IsReachMaxLevel() then		-- 满级特殊处理
		local first_terrace_level = math.floor(level / 3) * 3 + 1 - 3
		for i = 0, 2 do
			table.insert(terrace_list, ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").level_cfg[first_terrace_level + i])
		end
		return terrace_list
	end

	local first_terrace_level = math.floor(level / 3) * 3 + 1
	for i = 0, 2 do
		table.insert(terrace_list, ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").level_cfg[first_terrace_level + i])
	end
	return terrace_list
end

-- 根据等级获取当前站台奖励
function DailyWGData:GetTerraceRewardByLevel(level)
	for k,v in pairs(ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").day_reward) do
		if v.level == level then
			return v
		end
	end
	return nil
end

function DailyWGData:GetTerraceRewardRemind()
	local reward_num = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_TIANXIANGEFB_DAY_REWARD)
	return self.welkin_pass_level > 0 and reward_num == 0 and 1 or 0
end

-- 根据等级获取配置信息
function DailyWGData:GetCfgByLevel(level)
	return ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").level_cfg[level]
end

-- 根据当前等级获取天宫试炼增加的总战力
function DailyWGData:GetTotalZhanliByLevel(level)
	local cfg = self:GetCfgByLevel(level)
	if nil == cfg then return 0 end
	local attribute = AttributeMgr.GetAttributteByClass(cfg)
	local total_zhanli = AttributeMgr.GetCapability(attribute)
	return total_zhanli
end

-- 根据当前等级获取下一级增加的战力
function DailyWGData:GetNextZhanliByLevel(level)
	local next_level = level + 1
	if next_level > self:GetCfgMaxLevel() then return 0 end		--达到最大值

	local cur_cfg = self:GetCfgByLevel(level)
	local cur_attribute = AttributeMgr.GetAttributteByClass(cur_cfg)
	local next_cfg = self:GetCfgByLevel(next_level)
	local next_attribute = AttributeMgr.GetAttributteByClass(next_cfg)

	local m_attribute = AttributeMgr.LerpAttributeAttr(cur_attribute, next_attribute)
	local add_zhanli = AttributeMgr.GetCapability(m_attribute)
	return add_zhanli
end


---------------宠物装备副本------------------

function DailyWGData:SetPeteqFbInfo(info)
	self.peteqfb_info.remain_day_times = info.remain_day_times
	self.peteqfb_info.buy_times = info.buy_times
	self.peteqfb_info.open_chapter = info.open_chapter
	self.peteqfb_info.open_level = info.open_level
	self.peteqfb_info.next_auto_add_times_timestamp = info.next_auto_add_times_timestamp
	self.peteqfb_info.level_list = info.level_list
	self.peteqfb_info.is_fetched_list = info.is_fetched_list
end

function DailyWGData:GetPeteqFbInfo()
	return self.peteqfb_info
end

function DailyWGData:GetPeteqIsReward(chapter, index)
	return self.peteqfb_info.is_fetched_list[chapter] and self.peteqfb_info.is_fetched_list[chapter][index].is_fetched == 1
end

function DailyWGData:GetPeteqChapterStars(chapter)
	local star = 0
	if self.peteqfb_info.level_list[chapter] == nil then return star end
	for k,v in pairs(self.peteqfb_info.level_list[chapter]) do
		star = star + v.star
	end
	return star
end

function DailyWGData:GetPeteqRewardStatus(chapter, index)
	local status = COMPLETE_STATUS.DEFAULT
	local data_list = self:GetPeteqStarRewardCfg(chapter)
	local cfg = data_list[index]
	if nil == cfg then return status end
	local star = self:GetPeteqChapterStars(chapter)
	local is_get = self:GetPeteqIsReward(chapter, index)
	if is_get then
		status = COMPLETE_STATUS.YILINGQU
	elseif star >= cfg.star then
		status = COMPLETE_STATUS.WEILINGQU
	end
	return status
end

function DailyWGData:GetPeteqOpenLevelCfg()
	local list = {}
	local role_level = RoleWGData.Instance.role_vo.level or 1
	for k,v in pairs(ConfigManager.Instance:GetAutoConfig("weaponfbconfig_auto").level) do
		if v.chapter <= self.peteqfb_info.open_chapter and v.role_level <= role_level then
			list[v.chapter] = list[v.chapter] or {}
			list[v.chapter][v.level + 1] = v
		end
	end
	return list
end

function DailyWGData:GetPeteqLevelCfg(chapter, level)
	for k,v in pairs(ConfigManager.Instance:GetAutoConfig("weaponfbconfig_auto").level) do
		if v.chapter == chapter and v.level == level then
			return v
		end
	end
	return nil
end

function DailyWGData:GetPeteqLevelCfgBySceneId(scene_id)
	for k,v in pairs(ConfigManager.Instance:GetAutoConfig("weaponfbconfig_auto").level) do
		if v.scene_id == scene_id then
			return v
		end
	end
	return nil
end
function DailyWGData:GetPeteqStarBySceneId(scene_id, time)
	for k,v in pairs(ConfigManager.Instance:GetAutoConfig("weaponfbconfig_auto").level) do
		if v.scene_id == scene_id then
			for i = 3, 1, -1 do
				if v["sec_" .. i .. "_star"] >= time then
					return i
				end
			end
		end
	end
	return 0
end

function DailyWGData:GetPeteqStarRewardCfg(chapter)
	local list = {}
	for k,v in ipairs(ConfigManager.Instance:GetAutoConfig("weaponfbconfig_auto").star_reward) do
		if v.chapter == chapter then
			table.insert(list, v)
		end
	end
	return list
end

function DailyWGData:SetCurPeteqfbLevel(level)
	self.cur_peteqfb_level = level
end

function DailyWGData:GetCurPeteqfbLevel()
	return self.cur_peteqfb_level
end

function DailyWGData:SetCurPeteqfbChapterLevel(chapter, level)
	self.cur_peteqfb_chapter_level[chapter] = level
end

function DailyWGData:GetCurPeteqfbChapterLevel(chapter)
	return self.cur_peteqfb_chapter_level[chapter] or 0
end


function DailyWGData:GetPeteqRemind()
	return self.peteqfb_info.remain_day_times
end


---------------------------------------------------
--高战副本
---------------------------------------------------
function DailyWGData:GetHighGridListData()
	local fb_list = ConfigManager.Instance:GetAutoConfig("gaozhanfbcfg_auto").level_cfg
	return fb_list
end

function DailyWGData:GetHighGridListDataByLevel(level)
	local fb_list = ConfigManager.Instance:GetAutoConfig("gaozhanfbcfg_auto").level_cfg
	for k,v in pairs(fb_list) do
		if v.level == level then
			return v
		end
	end
	return nil
end

function DailyWGData:GetMaxLevel()
	local fb_list = ConfigManager.Instance:GetAutoConfig("gaozhanfbcfg_auto").level_cfg
	local max_level = 0
	for k,v in pairs(fb_list) do
		if max_level < v.level then
			max_level = v.level
		end
	end
	return max_level
end

function DailyWGData.GetHighChapterNameList()
	local list = {}
	local fb_list = ConfigManager.Instance:GetAutoConfig("gaozhanfbcfg_auto").level_cfg
	local chapter_length = #fb_list / 3
	for i = 1, math.ceil(chapter_length) do
		table.insert(list, string.format(Language.GaoZhan.Chapter, i))
	end

	return list
end

function DailyWGData:SetGaoZhanInfo(pass_level, auto_cur_level, can_auto_max_level)
	self.high_level = pass_level
	self.high_cur_level = auto_cur_level
	self.can_auto_max_level = can_auto_max_level
end

function DailyWGData:GetGaoZhanLevel()
	return self.high_level
end

function DailyWGData:GetGaoZhanCurLevel()
	return self.high_cur_level
end

function DailyWGData:GetGaoZhanCanMaxLevel()
	return self.can_auto_max_level
end


-- 设置等级信息
function DailyWGData:SetWushuangLevelInfo(pass_level)
	self.wushuang_level_info.pass_level = pass_level or 0
end

-- 设置奖励信息
function DailyWGData:SetWushuangRewardInfo(reward)
	self.wushuang_level_info.has_fetch_day_reward = reward or 0
end

-- 获取等级信息
function DailyWGData:GetWushuangLevelInfo()
	return self.wushuang_level_info
end

function DailyWGData:GetWushuangFbConfig()
	local wushuang_fb_config = ConfigManager.Instance:GetAutoConfig("wushuangfbconfig_auto").fb_level_cfg

	return wushuang_fb_config
end

-- 获取无双副本配置信息
function DailyWGData:GetWushuangLevelCfg(level)
	local wushuang_fb_config = ConfigManager.Instance:GetAutoConfig("wushuangfbconfig_auto").fb_level_cfg

	for i,v in pairs(wushuang_fb_config) do
		if v.fb_level == level then
			return v
		end
	end
	return nil
end

-- 通关奖励物品信息
function DailyWGData:GetWushuangPassRewardItemCfg(level)
	local day_reward = self:GetWushuangLevelCfg(level)
	if day_reward == nil then return end
	local reward = day_reward.reward_kill
	if reward ~= nil then
		local cfg =  Split(reward, ":")
		local item = {item_id = tonumber(cfg[1]), num = tonumber(cfg[2]), is_bind = tonumber(cfg[3])}
		return item
	end
	return nil
end

-- 每日奖励物品信息
function DailyWGData:GetWushuangDayRewardItemCfg(level)
	local day_reward = self:GetWushuangLevelCfg(level)
	if day_reward == nil then return end
	local reward = day_reward.reward_item_list
	if reward ~= nil then
		return reward
	end
	return nil
end

function DailyWGData:GetWushuangRemind()
	local num = 0
	if 0 == self.wushuang_level_info.has_fetch_day_reward and 0 < self.wushuang_level_info.pass_level then
		num = 1
	end
	return num
end

function DailyWGData:GetDailyOtherConfig()
	return self.daily_other_cfg
end

function DailyWGData:GetBtnSortDataListCfg(btn_sort_index)
	return self.button_sort_cfg[btn_sort_index]
end

function DailyWGData:GetBtnSortCfgByBtnName(btn_name)
	return (self.button_name_cfg or {})[btn_name]
end