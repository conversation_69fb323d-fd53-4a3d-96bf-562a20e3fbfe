VersionsAdvanceNoticeView = VersionsAdvanceNoticeView or BaseClass(SafeBaseView)

function VersionsAdvanceNoticeView:__init()
	self:SetMaskBg(false)
    self.view_style = ViewStyle.Full

	self:AddViewResource(0, "uis/view/face_book_prefab", "layout_advance_notice_view")
end

function VersionsAdvanceNoticeView:__delete()

end

function VersionsAdvanceNoticeView:OpenCallBack()

end

function VersionsAdvanceNoticeView:CloseCallBack()

end

function VersionsAdvanceNoticeView:LoadCallBack()
    self.notice_panel_list = {}

    self.toggle_list = {}
    self.max_page_num = 0
    self.cur_page_num = 0
	self.cur_select_type = 1
    self.move_tween_flage = false
	self.force_jump_page = false
	self.drag_begin_pos_x = 0
	self.drag_end_pos_x = 0
	self.is_draging = false

    local toggle_num = self.node_list.page_point_root.transform.childCount
    for i = 1, toggle_num do
        self.toggle_list[i] = self.node_list.page_point_root:FindObj("page_point_" .. i)
        self.toggle_list[i].toggle.isOn = false
    end

    XUI.AddClickEventListener(self.node_list.left_btn, BindTool.Bind(self.OnClickMoveLeft, self))
    XUI.AddClickEventListener(self.node_list.right_btn, BindTool.Bind(self.OnClickMoveRight, self))

	self.scroll_rect = self.node_list.scroll_view.scroll_rect
	self.node_list.viewport.event_trigger_listener:AddBeginDragListener(BindTool.Bind(self.OnScrollBeginDrag, self))
	self.node_list.viewport.event_trigger_listener:AddDragListener(BindTool.Bind(self.OnScrollDrag, self))
	self.node_list.viewport.event_trigger_listener:AddEndDragListener(BindTool.Bind(self.OnScrollEndDrag, self))

    self.node_list.left_btn:SetActive(false)
    self.node_list.right_btn:SetActive(false)
end

function VersionsAdvanceNoticeView:OnScrollBeginDrag(event_data)
	if self.scroll_rect == nil or self.move_tween_flage or self.open_jump_panel_type ~= nil then
		return
	end

	self.is_draging = true
	self.scroll_rect.horizontal = true
	self.drag_begin_pos_x = event_data.position.x
	self.scroll_rect:OnBeginDrag(event_data)
end

function VersionsAdvanceNoticeView:OnScrollDrag(event_data)
	if self.scroll_rect == nil or self.move_tween_flage or self.open_jump_panel_type ~= nil then
		return
	end

	self.scroll_rect:OnDrag(event_data)
end

function VersionsAdvanceNoticeView:OnScrollEndDrag(event_data)
	if self.scroll_rect == nil or self.move_tween_flage or self.open_jump_panel_type ~= nil then
		return
	end

	self.scroll_rect:OnEndDrag(event_data)
	self.scroll_rect.horizontal = false
	self.drag_end_pos_x = event_data.position.x
	self.is_draging = false

	self:DoScrollEndDragTween()
end

function VersionsAdvanceNoticeView:DoScrollEndDragTween()
	local move_right = self.drag_end_pos_x - self.drag_begin_pos_x < 0 and 1 or -1
	local can_move = math.abs(self.drag_end_pos_x - self.drag_begin_pos_x) > 350
	move_right = can_move and move_right or 0
	self.force_jump_page = true
	local move_page = self.cur_page_num + move_right

	self:MovePanelList(move_page)
end

function VersionsAdvanceNoticeView:ReleaseCallBack()
    if self.notice_panel_list then
		for k, v in pairs(self.notice_panel_list) do
			if v.loaded_flag then
				v.cell:DeleteMe()
			end
		end
		self.notice_panel_list = nil
	end

    self:CleanCheckCDTime()

    self.toggle_list = nil
    self.max_page_num = nil
    self.cur_page_num = nil
    self.move_tween_flage = nil
    self.open_jump_panel_type = nil
	self.force_jump_page = nil
	self.scroll_rect = nil
	self.drag_begin_pos_x = nil
	self.drag_end_pos_x = nil
	self.is_draging = nil
end

function VersionsAdvanceNoticeView:ShowIndexCallBack()
    self.open_jump_panel_type = VersionsAdvanceNoticeWGData.Instance:GetCurNotice()
end

function VersionsAdvanceNoticeView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "day_change" then
			self.open_jump_panel_type = self.cur_select_type or 1
			self.force_jump_page = true
		end
	end

    self.panel_list_data = VersionsAdvanceNoticeWGData.Instance:GetShowList()
    local show_list = {}
    local panel_path = "uis/view/face_book_prefab"
	for i, v in ipairs(self.panel_list_data) do
        show_list[v.type] = true
		if self.notice_panel_list[v.type] and self.notice_panel_list[v.type].loaded_flag then
			self.notice_panel_list[v.type].cell:SetData(v)
		else
            self.notice_panel_list[v.type] = {}
            self.notice_panel_list[v.type].loaded_flag = false
			local async_loader = AllocAsyncLoader(self, "ad_notice_panel" .. v.type)
			async_loader:SetParent(self.node_list["view_list"].transform)
			async_loader:Load(panel_path, v.view_res, function (obj)
				local cell = VersionsAdvanceNoticeRender.New(obj)
				cell:SetIndex(i)
				cell:SetData(v)
				self.notice_panel_list[v.type].cell = cell
                self.notice_panel_list[v.type].loaded_flag = true
			end)
		end
	end

    for k, v in pairs(self.notice_panel_list) do
        if v.loaded_flag then
            v.cell:SetActive(show_list[k])
        end
    end

    self.max_page_num = #self.panel_list_data
    for k, v in ipairs(self.toggle_list) do
        v:SetActive(k <= self.max_page_num)
    end

    self:AutoJumpPanel()
end

function VersionsAdvanceNoticeView:CheckPanelLoadCompelete()
    for k,v in pairs(self.notice_panel_list) do
        if not v.loaded_flag then
            return false
        end
    end

    return true
end

function VersionsAdvanceNoticeView:CleanCheckCDTime()
    if self.check_load_quest ~= nil then
        GlobalTimerQuest:CancelQuest(self.check_load_quest)
        self.check_load_quest = nil
    end
end

function VersionsAdvanceNoticeView:AutoJumpPanel()
    if self.open_jump_panel_type == nil or self.move_tween_flage then
        self.open_jump_panel_type = nil
        self:CleanCheckCDTime()
        return
    end

    local list_load_over = self:CheckPanelLoadCompelete()
    if not list_load_over then
        if self.check_load_quest == nil then
            self.check_load_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.AutoJumpPanel, self), 0.1)
        end
        return
    else
        self:CleanCheckCDTime()
    end

	local page = 1
	if self.panel_list_data then
		for i, v in ipairs(self.panel_list_data) do
			if v.type == self.open_jump_panel_type then
				page = i
				break
			end
		end
	end

    self.open_jump_panel_type = nil
    self:MovePanelList(page, 0)
end

function VersionsAdvanceNoticeView:OnClickMoveLeft()
    self:MovePanelList(self.cur_page_num - 1)
end

function VersionsAdvanceNoticeView:OnClickMoveRight()
    self:MovePanelList(self.cur_page_num + 1)
end

function VersionsAdvanceNoticeView:MovePanelList(page, tween_time)
    if not self:CheckPanelLoadCompelete() or self.move_tween_flage or self.is_draging then
        return
    end

    if not self.force_jump_page and (page == self.cur_page_num or page <= 0 or page > self.max_page_num) then
        return
    end

    tween_time = tween_time or 0.5
	page = page <= 0 and 1 or page
	page = page > self.max_page_num and self.max_page_num or page

    self.cur_page_num = page
    self.move_tween_flage = true
	self.force_jump_page = false
	local cur_data = self:GetCurSelectData()
	self.cur_select_type = cur_data and cur_data.type

    -- if self.cur_select_type then
    --     local role_id = RoleWGData.Instance:InCrossGetOriginUid()
    --     PlayerPrefsUtil.SetString("VERSIONS_ADVANCE_NOTICE"..role_id..self.cur_select_type, "1")
    --     VersionsAdvanceNoticeWGCtrl.Instance:UpDateEntranceShow(true)
    -- end

    self:FlushArrowRemind()

    local page_length = 1334
    local new_pos_x = (page - 1) * page_length
    local max_pos_x = self.max_page_num * page_length
    new_pos_x = new_pos_x <= 0 and 0 or new_pos_x
    new_pos_x = new_pos_x >= max_pos_x and max_pos_x or new_pos_x
    self.node_list.view_list.rect:DOAnchorPosX(-new_pos_x, tween_time):SetEase(DG.Tweening.Ease.OutBack):OnComplete(function()
        self.move_tween_flage = false
        self.toggle_list[page].toggle.isOn = true
        self.node_list.left_btn:SetActive(page > 1)
        self.node_list.right_btn:SetActive(page < self.max_page_num)
        self:UpdataViewTitle()
    end)
end

function VersionsAdvanceNoticeView:FlushArrowRemind()
    self.node_list.arrow_right_remind:SetActive(false)
    self.node_list.arrow_left_remind:SetActive(false)
    if not IsEmptyTable(self.panel_list_data) then
        for k, v in pairs(self.panel_list_data) do
            if k > self.cur_page_num and v.type then
                local is_showred = self:GetRedNumByType(v.type) == 1
                if is_showred then
                    self.node_list.arrow_right_remind:SetActive(true)
                end
            elseif k < self.cur_page_num and v.type then
                local is_showred = self:GetRedNumByType(v.type) == 1
                if is_showred then
                    self.node_list.arrow_left_remind:SetActive(true)
                end
            end
        end
    end
end

function VersionsAdvanceNoticeView:GetRedNumByType(type_)
    local red_num = 0
    if type_ then
        local role_id = RoleWGData.Instance:InCrossGetOriginUid()
        local values = PlayerPrefsUtil.GetString("VERSIONS_ADVANCE_NOTICE"..role_id..type_)
        if values == nil or values ~= "1" then
            red_num = red_num + 1
        end
    end
    return red_num
end

function VersionsAdvanceNoticeView:GetCurSelectData()
	return self.panel_list_data and self.panel_list_data[self.cur_page_num]
end

function VersionsAdvanceNoticeView:UpdataViewTitle()
    local cur_data = self:GetCurSelectData()
    if cur_data == nil then
        return
    end


    if self.node_list.view_title then
        self.node_list.view_title.text.text = cur_data.name
    end
end

------------------------------VersionsAdvanceNoticeRender-----------------------
--------------------------------------------------------------------------------
VersionsAdvanceNoticeRender = VersionsAdvanceNoticeRender or BaseClass(BaseRender)
function VersionsAdvanceNoticeRender:__init()
	self.time_str = ""
	self.desc_color = COLOR3B.D_GREEN
    -- 动态列表
    if self.node_list.dynamic_item_list and self.dynamic_item_list == nil then
        self.dynamic_item_list = {}
    end

    -- 固定列表
    if self.node_list.fixed_item_list and self.fixed_item_list == nil then
        self.fixed_item_list = {}
        local node_num = self.node_list.fixed_item_list.transform.childCount
        for i = 1, node_num do
            self.fixed_item_list[i] = ItemCell.New(self.node_list.fixed_item_list:FindObj("item_" .. i))
        end
    end

    -- 展示形象
    if self.node_list.model_display and self.model_display == nil then
        self.model_display = OperationActRender.New(self.node_list.model_display)
        self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
    end

    -- 属性列表
    if self.node_list.attr_list and self.attr_list == nil then
        self.attr_list = {}
        local node_num = self.node_list.attr_list.transform.childCount
        for i = 1, node_num do
            self.attr_list[i] = self.node_list.attr_list:FindObj("attr_" .. i)
        end
    end

    -- 前往按钮
    if self.node_list.goto_btn then
        XUI.AddClickEventListener(self.node_list.goto_btn, BindTool.Bind(self.OnClickGoTo, self))
    end

	-- 查看跨服进度按钮
	if self.node_list.see_cs_progress_btn then
		XUI.AddClickEventListener(self.node_list.see_cs_progress_btn, BindTool.Bind(self.OnClickSeeCSProgress, self))
	end
end

function VersionsAdvanceNoticeRender:__delete()
    if self.dynamic_item_list ~= nil then
        for k, v in pairs(self.dynamic_item_list) do
            v:DeleteMe()
        end
        self.dynamic_item_list = nil
    end

    if self.fixed_item_list ~= nil then
        for k, v in pairs(self.fixed_item_list) do
            v:DeleteMe()
        end
        self.fixed_item_list = nil
    end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end

    self.attr_list = nil
	self.time_str = nil
	self.desc_color = nil
	self:CleanCDTime()
end

function VersionsAdvanceNoticeRender:OnFlush()
    if IsEmptyTable(self.data) then
    	return
    end

    -- 道具展示
    if not IsEmptyTable(self.data.item_list) then
        if self.data.item_show_type == VAN_ITEM_LIST_TYPE.DYNAMIC then
            self:SetDynamicItemListData(self.data.item_list)
        elseif self.data.item_show_type == VAN_ITEM_LIST_TYPE.FIXED then
            self:SetFixedItemListData(self.data.item_list)
        end
    end

    -- 形象展示
    self:ShowModel()

    -- 属性战力
    self:ShowAttrAndCap()

	-- 时间描述
	self:ShowTimeDesc()
end

-- 动态列表
function VersionsAdvanceNoticeRender:SetDynamicItemListData(item_list)
    if IsEmptyTable(item_list) or self.dynamic_item_list == nil then
    	return
    end

    local item_list_length = #item_list
	for i = 1, item_list_length do
        local data = item_list[i]
        if self.dynamic_item_list[i] then
            self.dynamic_item_list[i]:SetData(data)
        else
            self.dynamic_item_list[i] = ItemCell.New(self.node_list.dynamic_item_list)
            self.dynamic_item_list[i]:SetData(data)
        end
    end

    local cell_list_length = #self.dynamic_item_list
    for i = 1, cell_list_length do
        self.dynamic_item_list[i]:SetActive(i <= item_list_length)
    end
end

-- 固定列表
function VersionsAdvanceNoticeRender:SetFixedItemListData(item_list)
    if IsEmptyTable(item_list) or self.fixed_item_list == nil then
    	return
    end

    for k, v in pairs(self.fixed_item_list) do
        local data = item_list[k]
        if data then
            v:SetActive(true)
            v:SetData(data)
        else
            v:SetActive(false)
        end
    end
end

-- 展示形象
function VersionsAdvanceNoticeRender:ShowModel()
    if self.model_display == nil or IsEmptyTable(self.data)
    or self.data.model_show_type == 0 then
        return
    end

    local data = {}
    data.should_ani = true

    if self.data.model_show_itemid ~= 0 and self.data.model_show_itemid ~= "" then
        local split_list = string.split(self.data.model_show_itemid, "|")
        if #split_list > 1 then
            local list = {}
            for k, v in pairs(split_list) do
                list[tonumber(v)] = true
            end
            data.model_item_id_list = list
        else
            data.item_id = self.data.model_show_itemid
        end
    end

    data.bundle_name = self.data.model_bundle_name
    data.asset_name = self.data.model_asset_name
    data.render_type = self.data.model_show_type - 1
    self.model_display:SetData(data)
end

-- 属性战力
function VersionsAdvanceNoticeRender:ShowAttrAndCap()
    if IsEmptyTable(self.data) then
        return
    end

    -- 减少计算
    local cap, attr_desc, attr_list
    if self.data.show_item_attr > 0 and self.data.show_item_cap > 0 and self.data.show_item_attr == self.data.show_item_cap then
        cap, attr_desc, attr_list = ItemShowWGData.CalculateCapability(self.data.show_item_attr)
    end

    if self.data.show_item_attr > 0 and self.attr_list then
        if not attr_list then
            cap, attr_desc, attr_list = ItemShowWGData.CalculateCapability(self.data.show_item_attr)
        end

        local value_color = COLOR3B.D_GREEN
        for k, v in ipairs(self.attr_list) do
            local data = attr_list[k]
            if data then
                v.text.text = string.format("%s: %s", data.attr_name, ToColorStr(data.value_str, value_color))
                v:SetActive(true)
            else
                v:SetActive(false)
            end
        end
    end

    if self.data.show_item_cap > 0 and self.node_list.cap_value then
        if not cap then
            cap, attr_desc, attr_list = ItemShowWGData.CalculateCapability(self.data.show_item_cap)
        end

        self.node_list.cap_value.text.text = cap or 0
    end
end

function VersionsAdvanceNoticeRender:CleanCDTime()
	if CountDownManager.Instance:HasCountDown("van_render" .. self.index) then
		CountDownManager.Instance:RemoveCountDown("van_render" .. self.index)
	end
end

-- 时间描述
function VersionsAdvanceNoticeRender:ShowTimeDesc()
	self:CleanCDTime()
	if not self.node_list.activity_time_desc then
		return
	end

	if IsEmptyTable(self.data) or IsEmptyTable(self.data.state_table) then
		self.node_list.activity_time_desc.text.text = ""
    	return
    end

	-- 倒计时
	local state_table = self.data.state_table
	if state_table.state == VAN_NOTICE_STATE.ADVANCE_CD and state_table.cd_timestamp > 0 then
		local res_time = state_table.cd_timestamp
		self.time_str = state_table.desc
		self.desc_color = state_table.desc_color
		local time_str = TimeUtil.FormatSecondDHM6(res_time)
		self.node_list.activity_time_desc.text.text = string.format("%s: %s", self.time_str, ToColorStr(time_str, self.desc_color))

		if res_time > 0 then
			CountDownManager.Instance:AddCountDown("van_render" .. self.index, BindTool.Bind(self.UpdateRestTime, self),
													nil, nil, res_time, 1)
		end
	-- 固定描述
	else
		self.node_list.activity_time_desc.text.text = state_table.desc
	end
end

function VersionsAdvanceNoticeRender:UpdateRestTime(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		if self.node_list.activity_time_desc then
			local time_str = TimeUtil.FormatSecondDHM6(valid_time)
			self.node_list.activity_time_desc.text.text = string.format("%s: %s", self.time_str, ToColorStr(time_str, self.desc_color))
		end
	else
		self.node_list.activity_time_desc.text.text = string.format("%s: %s", self.time_str, ToColorStr("00:00", self.desc_color))
		self:CleanCDTime()
	end
end

function VersionsAdvanceNoticeRender:OnClickGoTo()
	if IsEmptyTable(self.data) or IsEmptyTable(self.data.state_table) then
		return
	end

	if self.data.state_table.btn_limit_desc ~= "" then
		SysMsgWGCtrl.Instance:ErrorRemind(self.data.state_table.btn_limit_desc)
		if self.data.state_table.btn_limit_callback then
			self.data.state_table.btn_limit_callback()
		end
		return
	end

	FunOpen.Instance:OpenViewNameByCfg(self.data.jump_view)
end

function VersionsAdvanceNoticeRender:OnClickSeeCSProgress()
end
