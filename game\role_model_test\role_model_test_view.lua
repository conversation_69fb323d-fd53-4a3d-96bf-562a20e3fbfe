-- 模型调节面板
RoleModelTestView = RoleModelTestView or BaseClass(SafeBaseView)

-- UI上测试模型显示用
function RoleModelTestView:__init()
	self.view_cache_time = 0
	self:AddViewResource(0, "uis/view/role_model_test_ui_prefab", "layout_role_model_test")
	self.path = string.format("%s/%s", UnityEngine.Application.dataPath, "Game/Lua/gameui/widgets/role_model_camera_set.lua")
end

function RoleModelTestView:__delete()
end

function RoleModelTestView:OpenCallBack()
	ClientCmdWGCtrl.Instance:Cmd("UpdateLua " .. self.path)
end

function RoleModelTestView:CloseCallBack()
end

function RoleModelTestView:ReleaseCallBack()
	self:ReleaseRoleModel()

	self.data = nil
	if self.title_list then
        self.title_list:DeleteMe()
        self.title_list = nil
    end

	if self.test_model_btn_list then
		self.test_model_btn_list:DeleteMe()
		self.test_model_btn_list = nil
	end

	GlobalTimerQuest:CancelQuest(self.hole_delay_timer)
end

function RoleModelTestView:LoadCallBack()
	self.title_select_index = 0
	self.show_data_list = {}
	self:InitRoleModel()

	self.test_model_btn_list = AsyncListView.New(TestRoleModelBtn, self.node_list["btn_list"])
	self.test_model_btn_list:SetSelectCallBack(BindTool.Bind1(self.OnClickBtn, self))


	self.node_list["pos_x"].input_field.onValueChanged:AddListener(BindTool.Bind(self.InputChange, self, "pos_x"))
	self.node_list["pos_y"].input_field.onValueChanged:AddListener(BindTool.Bind(self.InputChange, self, "pos_y"))
	self.node_list["rota_x"].input_field.onValueChanged:AddListener(BindTool.Bind(self.InputChange, self, "rota_x"))
	self.node_list["rota_y"].input_field.onValueChanged:AddListener(BindTool.Bind(self.InputChange, self, "rota_y"))
	self.node_list["rota_z"].input_field.onValueChanged:AddListener(BindTool.Bind(self.InputChange, self, "rota_z"))
	self.node_list["scale"].input_field.onValueChanged:AddListener(BindTool.Bind(self.InputChange, self, "scale"))
	self.node_list["rota_x_slider"].slider.onValueChanged:AddListener(BindTool.Bind(self.OnSliderValueChange, self, "rota_x"))
	self.node_list["rota_y_slider"].slider.onValueChanged:AddListener(BindTool.Bind(self.OnSliderValueChange, self, "rota_y"))
	self.node_list["rota_z_slider"].slider.onValueChanged:AddListener(BindTool.Bind(self.OnSliderValueChange, self, "rota_z"))
	-- XUI.AddClickEventListener(self.node_list["save_btn"], BindTool.Bind1(self.SaveModelSettingToFile, self))

	self.node_list["search_input"].input_field.onEndEdit:AddListener(BindTool.Bind(self.SearchInputChange, self))
	self.node_list["default_btn"].button:AddClickListener(BindTool.Bind(self.OnClickResetDefault, self))
	self.node_list["npc_dialog_btn"].button:AddClickListener(BindTool.Bind(self.OnClickNpcDialogBtn, self))
	self.node_list["svn_commit_btn"].button:AddClickListener(BindTool.Bind(self.OnClickSVNCommit, self))

	self.title_list = AsyncListView.New(ScreenShotModeTitleRender, self.node_list.title_list)
	self.title_list:SetSelectCallBack(BindTool.Bind(self.OnClickTitleBtn, self))
	self.title_list:SetDataList(ScreenShotModelView.MODEL_TYPE_NAME)
	self.title_list:SelectIndex(1)
end

function RoleModelTestView:InitRoleModel()
	if not self.display then
		self.display = RoleModel.New()
			-- self.display:SetUI3DModel(self.node_list["display"].transform, self.node_list["tigger_listen"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)

		local display_data = {
			parent_node = self.node_list["display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.L,
			-- drag_width = 600,
			-- drag_height = 800,
			can_drag = false,
		}
	
		self.display:SetRenderTexUI3DModel(display_data)
	end
end

function RoleModelTestView:OnClickTitleBtn(cell)
    if self.title_select_index == cell.index then
        return
    end

    self.path_select_index = 0
    self.title_select_index = cell.index
    local show_list = ScreenShotModelData.Instance:GetModelTestShowList(self.title_select_index)
	self.show_data_list = show_list
	self.test_model_btn_list:SetDataList(show_list)
	self.test_model_btn_list:SelectIndex(1)
end

function RoleModelTestView:OnSliderValueChange(key, value)
	if self.node_list[key].input_field.text ~= value then
		self.node_list[key].input_field.text = value
	end
end

-- 检查是否是整数或者小数
function RoleModelTestView:CheckIsNumber(str)
	if string.find(str, "%.") then
		if string.find(str, "%w+(.)%w+") then
			return tonumber(str) ~= nil
		end
		return false
	else
		return tonumber(str) ~= nil
	end
end

function RoleModelTestView:InputChange(key, str)
	if self:CheckIsNumber(str) and self[key] ~= tonumber(str) then
		self[key] = tonumber(str)
		self:SaveModelSettingToFile()
		self:Flush()
	end
end

function RoleModelTestView:SearchInputChange(str)
	self.search_str = str
	self:FlushModelBtnList()
end

function RoleModelTestView:TransModelStr(str)
	local str_list = Split(str, "#")
	return {bundle = str_list[1], asset = str_list[2], str = str}
end

function RoleModelTestView:ReleaseRoleModel()
	if self.display then
		self.display:DeleteMe()
		self.display = nil
	end
end

function RoleModelTestView:ShowIndexCallBack(index)
end

function RoleModelTestView:Update()
end


function RoleModelTestView:OnFlush(param_list, index)
	self.display:SetMainAsset(self.bundle, self.asset)
	local _, setting = self.display:GetModelCameraSetting(self.display.camera_type, self.display.camera_setting_key, self.bundle, self.asset)
	self.pos_x = setting.position[1]
	self.pos_y = setting.position[2]
	self.rota_x = setting.rotation[1]
	self.rota_y = setting.rotation[2]
	self.rota_z = setting.rotation[3]
	self.scale = setting.scale
	if setting.TerrainOffset then
		self.terrain_offset_x = setting.TerrainOffset[1]
		self.terrain_offset_y = setting.TerrainOffset[2]
		self.terrain_offset_z = setting.TerrainOffset[3]
	else
		self.terrain_offset_x = nil
		self.terrain_offset_y = nil
		self.terrain_offset_z = nil
	end
	self:FlushInputField()
end

function RoleModelTestView:FlushInputField()
	self.node_list["pos_x"].input_field.text = self.pos_x
	self.node_list["pos_y"].input_field.text = self.pos_y
	self.node_list["rota_x"].input_field.text = self.rota_x
	self.node_list["rota_y"].input_field.text = self.rota_y
	self.node_list["rota_z"].input_field.text = self.rota_z
	self.node_list["rota_x_slider"].slider.value = self.rota_x
	self.node_list["rota_y_slider"].slider.value = self.rota_y
	self.node_list["rota_z_slider"].slider.value = self.rota_z
	self.node_list["scale"].input_field.text = self.scale
end

function RoleModelTestView:OnClickBtn(item)
	self.bundle = item.data.bundle
	self.asset = item.data.asset
	self.select_model_str = string.format("%s#%s", self.bundle, self.asset)
	self:Flush()
end

-- 提交到SVN
function RoleModelTestView:OnClickSVNCommit()
	local file_path = UnityEngine.Application.dataPath .. "\\Game\\Lua\\gameui\\widgets\\role_model_camera_set.lua"
	os.execute("start TortoiseProc.exe /command:commit /path:" .. file_path)
end


function RoleModelTestView:FlushModelBtnList()
	local models = nil
	if self.search_str and self.search_str ~= "" then
		models = {}
		for i,v in ipairs(self.show_data_list) do
			if string.find(v.asset, self.search_str) then
				table.insert(models, v)
			end
		end
	else
		models = self.show_data_list
	end

	self.test_model_btn_list:SetDataList(models)
	self.test_model_btn_list:SelectIndex(1)
end

-- 重置模型参数到默认值
function RoleModelTestView:OnClickResetDefault()
	local setting = self.display:GetModelCameraDefSetting(self.display.camera_type, self.display.camera_setting_key)
	self.pos_x = setting.position[1]
	self.pos_y = setting.position[2]
	self.rota_x = setting.rotation[1]
	self.rota_y = setting.rotation[2]
	self.rota_z = setting.rotation[3]
	self.scale = setting.scale
	if setting.TerrainOffset then
		self.terrain_offset_x = setting.TerrainOffset[1]
		self.terrain_offset_y = setting.TerrainOffset[2]
		self.terrain_offset_z = setting.TerrainOffset[3]
	else
		self.terrain_offset_x = nil
		self.terrain_offset_y = nil
		self.terrain_offset_z = nil
	end
	self:SaveModelSettingToFile()
	self:Flush()
end

-- 保存设置到文件
function RoleModelTestView:SaveModelSettingToFile()
	local cur_select_data = {}
	cur_select_data.position = {self.pos_x, self.pos_y}
	cur_select_data.rotation = {self.rota_x, self.rota_y, self.rota_z}
	cur_select_data.scale = self.scale
	if self.terrain_offset_x then
		cur_select_data.TerrainOffset = {self.terrain_offset_x, self.terrain_offset_y, self.terrain_offset_z}
	end

	-- 打开文件
	local file = io.open(self.path, "r")

	local cur_step = 0
	local count = 0
	local str = ""
	local vaild_data_list = {}
	local str_list_1 = {}
	local str_list_2 = {}
	while(str ~= nil) do
		str = file:read("*l")
		if not str then
			break
		end

		if cur_step == 0 then
			if string.find(str, "SPECIAL_MODEL_ASSET_SETTING") then
				cur_step = 1
			end
		end

		if cur_step == 1 then
			if string.find(str, "MODEL_CAMERA_TYPE.BASE") then
				cur_step = 2
			end
		end


		if cur_step <= 2 then
			table.insert(str_list_1, str)
		end

		if cur_step == 2 or cur_step == 3 then
			for i = 1, string.len(str) do
				if string.sub(str, i, i) == '{' then
					count = count + 1
				elseif string.sub(str, i, i) == '}' then
					count = count - 1
				end
			end
		end

		if cur_step == 3 then
			local flag, _, model_path_str = string.find(str, "\"(.+)\"")
			local _, _, x, y = string.find(str, "position%s*=%s*{%s*(%-?%d+%.*%d*)%s*,%s*(%-?%d+%.*%d*)%s*}")
			local _, _, rota_x, rota_y, rota_z = string.find(str, "rotation%s*=%s*{%s*(%-?%d+%.*%d*)%s*,%s*(%-?%d+%.*%d*)%s*,%s*(%-?%d+%.*%d*)%s*}")
			local _, _, scale = string.find(str, "scale%s*=%s*(%-?%d+%.*%d*)")
			local _, _, ter_x, ter_y, ter_z = string.find(str, "TerrainOffset%s*=%s*{%s*(%-?%d+%.*%d*)%s*,%s*(%-?%d+%.*%d*)%s*,%s*(%-?%d+%.*%d*)%s*}")

			if flag then
				local data = {}
				data.path_str = model_path_str
				data.pos = {x = x, y = y}
				data.rota = {x = rota_x, y = rota_y, z = rota_z}
				data.scale = scale
				if ter_x then
					data.terrain_offset = {x = ter_x, y = ter_y, z = ter_z}
				end

				-- print_log("#str#", str, x, y, model_path_str, rota_x, rota_y, rota_z)
				table.insert(vaild_data_list, data)
			end
		end

		if cur_step == 2 then
			cur_step = 3
		end

		if cur_step == 3 then
			if count <= 0 then
				cur_step = 4
			end
		end

		if cur_step == 4 then
			table.insert(str_list_2, str)
		end
	end

	io.close(file)


	local need_write_str_list = {}   -- 需写入到文件的字符串

	for i, v in ipairs(str_list_1) do
		table.insert(need_write_str_list, v .. "\n")
	end

	local has_special = false
	for i,v in ipairs(vaild_data_list) do
		local is_select_data = false
		if v.path_str == self.select_model_str then
			v.pos.x = self.pos_x
			v.pos.y = self.pos_y
			v.rota.x = self.rota_x
			v.rota.y = self.rota_y
			v.rota.z = self.rota_z
			v.scale = self.scale
			is_select_data = true
			has_special = true
		end

		local str = string.format("\t\t[\"%s\"] = {position = {%s, %s}, rotation = {%s, %s, %s}, scale = %s},"
			, v.path_str, v.pos.x, v.pos.y, v.rota.x, v.rota.y, v.rota.z, v.scale)
		if v.terrain_offset then
			str = string.format("\t\t[\"%s\"] = {position = {%s, %s}, rotation = {%s, %s, %s}, scale = %s, TerrainOffset = {%s, %s, %s}},"
				, v.path_str, v.pos.x, v.pos.y, v.rota.x, v.rota.y, v.rota.z, v.scale, v.terrain_offset.x, v.terrain_offset.y, v.terrain_offset.z)
		end

		if not is_select_data or not self:IsDefaultSetting(v.path_str, cur_select_data) then
			table.insert(need_write_str_list, str .. "\n")
		end
	end

	if not has_special and self.select_model_str and not self:IsDefaultSetting(self.select_model_str, cur_select_data)  then
		local str = string.format("\t\t[\"%s\"] = {position = {%s, %s}, rotation = {%s, %s, %s}, scale = %s},"
				, self.select_model_str, self.pos_x, self.pos_y, self.rota_x, self.rota_y, self.rota_z, self.scale)
		if self.terrain_offset_x then
			str = string.format("\t\t[\"%s\"] = {position = {%s, %s}, rotation = {%s, %s, %s}, scale = %s, TerrainOffset = {%s, %s, %s}},"
				, self.select_model_str, self.pos_x, self.pos_y, self.rota_x, self.rota_y, self.rota_z, self.scale, self.terrain_offset_x, self.terrain_offset_y, self.terrain_offset_z)
		end
		table.insert(need_write_str_list, str .. "\n")
	end

	for i, v in ipairs(str_list_2) do
		table.insert(need_write_str_list, v .. "\n")
	end


	-- 写入文件
	file = io.open(self.path, "w")
	for i,v in ipairs(need_write_str_list) do
		file:write(v)
	end
	io.close(file)

	-- 热更lua
	ClientCmdWGCtrl.Instance:Cmd("UpdateLua " .. self.path)

	-- 刷新面板
	self:Flush()
end

-- 判断是否与默认参数一致
function RoleModelTestView:IsDefaultSetting(path_str, data)
	local bundle = self:TransModelStr(path_str).bundle
	local key = self.display:GetBundleType(bundle)
	local def_config = UI3D_MODEL_SETTING[self.display.camera_type][key]
	if def_config then
		if IsSameArray(data.position, def_config.position)
			and IsSameArray(data.rotation, def_config.rotation)
			and IsSameArray(data.TerrainOffset, def_config.TerrainOffset)
			and data.scale == def_config.scale then
			return true
		end
		return false
	else
		print_error("没有找到默认参数:" .. bundle)
		return false
	end
end

function RoleModelTestView:OnClickNpcDialogBtn()
	local npc_cfg = ConfigManager.Instance:GetAutoConfig("npc_auto").npc_list
	local npc_id = 0
	for _, v in pairs(npc_cfg) do
		local bundle, asset = ResPath.GetNpcModel(v.resid)
		if bundle and asset then
			if bundle == self.bundle and asset == self.asset then
				npc_id = v.id
			end
		end
	end

	ViewManager.Instance:Open(GuideModuleName.TaskDialog, 0, "model_camera_set", {bundle = self.bundle, asset = self.asset})
	if not npc_id then
		TipWGCtrl.Instance:ShowSystemMsg("没找到资源对应的NPC id，请检查")
	end
end
----------------------------------------------------------------------------------
TestRoleModelBtn = TestRoleModelBtn or BaseClass(BaseRender)
function TestRoleModelBtn:OnFlush()
	self.node_list["btn_name"].text.text = self.data.bundle .. "#" .. self.data.asset
end

function TestRoleModelBtn:OnSelectChange(is_select)
	self.node_list["select"]:SetActive(is_select)
end
