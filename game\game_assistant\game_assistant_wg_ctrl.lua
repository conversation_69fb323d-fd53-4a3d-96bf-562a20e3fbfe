require("game/game_assistant/game_assistant_view")
require("game/game_assistant/game_assistant_fashion_view")
require("game/game_assistant/game_assistant_today_act_view")
require("game/game_assistant/game_assistant_want_view")
require("game/game_assistant/game_assistant_xianyu_view")
require("game/game_assistant/game_assistant_wg_data")

GameAssistantWGCtrl = GameAssistantWGCtrl or BaseClass(BaseWGCtrl)

function GameAssistantWGCtrl:__init()
	if GameAssistantWGCtrl.Instance then
        error("[GameAssistantWGCtrl]:Attempt to create singleton twice!")
	end

	GameAssistantWGCtrl.Instance = self
	self.data = GameAssistantWGData.New()
	self.view = GameAssistantView.New(GuideModuleName.GameAssistant)
end

function GameAssistantWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil
	GameAssistantWGCtrl.Instance = nil
end

function GameAssistantWGCtrl:FlushView()
	if self.view and self.view:IsO<PERSON>() then
		self.view:Flush()
	end
end