XuYuanFreshPoolDrawRecordView = XuYuanFreshPoolDrawRecordView or BaseClass(SafeBaseView)

function XuYuanFreshPoolDrawRecordView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)

	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel",
		{ vector2 = Vector2(0, 0), sizeDelta = Vector2(866, 566) })
	self:AddViewResource(0, "uis/view/xuyuan_freshpool_ui_prefab", "xuyuan_freshpool_draw_record")
end

function XuYuanFreshPoolDrawRecordView:ReleaseCallBack()
	if self.record_list then
		self.record_list:DeleteMe()
		self.record_list = nil
	end

	self.title_str = nil
	self.world_list = nil
end

function XuYuanFreshPoolDrawRecordView:LoadCallBack()
	self.node_list.title_view_name.text.text = self.title_str or Language.Common.TipsTitleStr
	self.record_list = AsyncListView.New(XuYuanFreshPoolDrawRecordItem, self.node_list["role_list"])
end

function XuYuanFreshPoolDrawRecordView:SetData(world_list, title_str)
	self.world_list = world_list
	self.title_str = title_str
end

function XuYuanFreshPoolDrawRecordView:OnFlush()
	self:FlushRecordList()
end

function XuYuanFreshPoolDrawRecordView:FlushRecordList()
	local data_list = self.world_list
	local is_show_list = not IsEmptyTable(data_list)
	if is_show_list then
		self.record_list:SetDataList(data_list)
	end
	self.node_list["role_list"]:SetActive(is_show_list)
	self.node_list["no_invite"]:SetActive(not is_show_list)
end

XuYuanFreshPoolDrawRecordItem = XuYuanFreshPoolDrawRecordItem or BaseClass(BaseRender)

function XuYuanFreshPoolDrawRecordItem:OnFlush()
	local index = self:GetIndex()
	local mark = (index % 2) == 1

	if not self.data then
		return
	end
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)

	if item_cfg == nil then
		print_error("找不到item_id:" .. tostring(self.data.item_id))
		return
	end
	local color = ITEM_COLOR[item_cfg.color]
	self.node_list["time"].text.text = os.date("%m-%d  %X", self.data.consume_time)
	self.node_list["root_bg"].image.enabled = mark

	local role_name = self.data.role_name or RoleWGData.Instance:GetAttr("name")
	local str1 = string.format(Language.SiXiangCall.TxtRecord3, role_name)
	local name = string.format(Language.SiXiangCall.TxtRecord1_2, color, item_cfg.name)
	local num = string.format(Language.SiXiangCall.TxtRecord1_3, color, self.data.num or 1)
	self.node_list["desc"].text.text = str1
	self.node_list["txt_btn"].text.text = name
	self.node_list["num"].text.text = num
end
