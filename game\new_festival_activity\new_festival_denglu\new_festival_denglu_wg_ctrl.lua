require("game/new_festival_activity/new_festival_denglu/new_festival_denglu_view")
require("game/new_festival_activity/new_festival_denglu/new_festival_denglu_wg_data")

NewFestivalDengLuWGCtrl = NewFestivalDengLuWGCtrl or BaseClass(BaseWGCtrl)
function NewFestivalDengLuWGCtrl:__init()
	if NewFestivalDengLuWGCtrl.Instance then
		ErrorLog("[NewFestivalDengLuWGCtrl] Attemp to create a singleton twice !")
	end
	NewFestivalDengLuWGCtrl.Instance = self

	self.data = NewFestivalDengLuWGData.New()

	self:RegisterAllProtocols()
end

function NewFestivalDengLuWGCtrl:__delete()
	NewFestivalDengLuWGCtrl.Instance = nil

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end
end

function NewFestivalDengLuWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCJieRiDengLuDrawInfo, "OnSCJieRiDengLuDrawInfo")
end

function NewFestivalDengLuWGCtrl:OnSCJieRiDengLuDrawInfo(protocol)
	-- print_error("----SCJieRiDengLuDrawInfo----", protocol)
	local old_num_list = self.data:GetDrawNumList()
	self.data:SetDengLuInfo(protocol)

	local delay_time = self:IsFlushDLView(old_num_list, protocol.draw_num_list)
	local fun = function ()
		ViewManager.Instance:FlushView(GuideModuleName.NewFestivalActivityView, TabIndex.new_festival_activity_2345)
		RemindManager.Instance:Fire(RemindName.NewFestivalDengLu)
	end

	GlobalTimerQuest:AddDelayTimer(function ()
		fun()
	end, delay_time)
end

function NewFestivalDengLuWGCtrl:IsFlushDLView(old_num_list, new_num_list)
	if not old_num_list then
		return 0
	end

	for k, v in ipairs(old_num_list) do
		if v ~= new_num_list[k] then
			ViewManager.Instance:FlushView(GuideModuleName.NewFestivalActivityView, TabIndex.new_festival_activity_2345, "play_dlani")
			return 3.5
		end
	end

	return 0
end