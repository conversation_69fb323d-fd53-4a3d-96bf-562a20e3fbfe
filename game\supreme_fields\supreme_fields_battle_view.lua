SupremeFieldsBattleView = SupremeFieldsBattleView or BaseClass(SafeBaseView)

function SupremeFieldsBattleView:__init()
	self.view_name = "SupremeFieldsBattleView"
	self.is_modal = true
	self:SetMaskBg(false, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(948, 594)})
	self:AddViewResource(0, "uis/view/supreme_fields_ui_prefab", "layout_supreme_fields_battle")
	self.grid_list = nil
	self.skill_index = -1
	self.select_skill_data = nil
	self.select_kowei = -1
	self.select_kowei_data = nil
end

function SupremeFieldsBattleView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.SupremeFields.ShangZhenTitleName

	if nil == self.skill_slot_list then
		self.skill_slot_list = {}
		local slot_num = self.node_list.content.transform.childCount
		for i = 1, slot_num do
            local cell = SkillSlotRender.New(self.node_list.content:FindObj("skill_" .. i))
            cell:SetSelectCallBack(BindTool.Bind1(self.SkillSlotSelectCallBack, self))
            cell:SetIndex(i)
            self.skill_slot_list[i] = cell
        end
	end

	if not self.skill_list then
		self.skill_list = AsyncListView.New(SkillItemRender, self.node_list.ph_skill_grid)
    	self.skill_list:SetSelectCallBack(BindTool.Bind1(self.SkillSelectCallBack, self))
	end

	self:FlushSkillList()
	XUI.AddClickEventListener(self.node_list["btn_auto"], BindTool.Bind(self.OnBtnAuto, self))
end

function SupremeFieldsBattleView:ShowIndexCallBack()
	self.skill_index = self:FirstSelectSkill()
	self.skill_list:SelectIndex(self.skill_index)
	local first_kowei_select = self:FirstSelectKoWei()
	self:SkillSlotSelectCallBack(first_kowei_select, self.skill_slot_list[first_kowei_select]:GetData())
end

function SupremeFieldsBattleView:OnFlush(param_t, index)
	for k,v in pairs(param_t) do
		if k == "OnPutOnSkill" then
    		self:FlushSkillList()
    		self.skill_list:JumpToIndex(self.skill_index)

			self.select_kowei_data = self.skill_slot_list[self.select_kowei]:GetData()
		end
	end

	local btn_txt = Language.SupremeFields.Btn_Text1
	if self.select_kowei > 0 then
		if not self.select_kowei_data.is_open then
			btn_txt = Language.SupremeFields.AlertTitile
		else
			btn_txt = self.select_kowei_data.skill_ids[1] ~= 0 and Language.SupremeFields.Btn_Text2 or Language.SupremeFields.ShangZhenTitleName
		end
	end

	self.node_list.btn_text.text.text = btn_txt
end

function SupremeFieldsBattleView:ReleaseCallBack()
	if nil ~= self.skill_slot_list then
        for k,v in pairs(self.skill_slot_list) do
            v:DeleteMe()
			v = nil
        end
        self.skill_slot_list = nil
    end

    if nil ~= self.skill_list then
        self.skill_list:DeleteMe()
        self.skill_list = nil
    end
end

function SupremeFieldsBattleView:CloseCallBack()
	self.skill_index = -1
	self.select_skill_data = nil
	self.select_kowei = -1
	self.select_kowei_data = nil
	self:ChangeSelectKoWei()
end

function SupremeFieldsBattleView:FlushSkillList()
	local data_list = SupremeFieldsWGData.Instance:GetFootLightSkillSlotList()
    for i,v in ipairs(self.skill_slot_list) do
    	if data_list[i] then
        	v:SetData(data_list[i])
        end
    end

    local data_list2 = SupremeFieldsWGData.Instance:GetFootLightSkillList()
    self.skill_list:SetDataList(data_list2)
end

function SupremeFieldsBattleView:FirstSelectKoWei()
	local data_list = SupremeFieldsWGData.Instance:GetFootLightSkillSlotList()
	for index, v in ipairs(data_list) do
		if v.is_open and v.skill_ids[1] == 0 then
			return index
		end
	end

	return 1
end

function SupremeFieldsBattleView:FirstSelectSkill()
	local skill_data = SupremeFieldsWGData.Instance:GetFootLightSkillList()
	for k,v in pairs(skill_data) do
		if v.is_open and not v.is_put_on then
			return k
		end
	end

	return 1
end

function SupremeFieldsBattleView:SkillSelectCallBack(cell)
	if cell == nil then
        return
    end

    self.select_skill_data = cell:GetData()
    self.skill_index = cell:GetIndex()
    self:FlushRightSkillView()
end

function SupremeFieldsBattleView:SkillSlotSelectCallBack(index, data)
	if IsEmptyTable(data) then
		return
	end

	if self.select_kowei == index then
		local skill_slot_index = self.select_kowei - 1

		if not self.select_kowei_data.is_open then
			local cfg = SupremeFieldsWGData.Instance:GetSkillHoleCfg(skill_slot_index)
			local is_money = cfg.condition_type == 2
			local need_num = 0
			local item_id = nil

			if is_money then
				need_num = cfg.condition_value
				local price = RoleWGData.GetPayMoneyStr(need_num, cfg.rmb_type, cfg.rmb_seq)
				RechargeWGCtrl.Instance:Recharge(need_num, cfg.rmb_type, cfg.rmb_seq)
			else
				local item_list = Split(cfg.condition_value, "|")
				local title_str = self.select_kowei_data.is_open and Language.SupremeFields.Btn_Text2 or Language.SupremeFields.AlertTitile
				item_id = tonumber(item_list[1])
				need_num = tonumber(item_list[2])

				local ok_func = function ()
					SupremeFieldsWGCtrl.Instance:SendOperation(Supreme_Operation_Index.SKILL_SLOT_UNLOCK, skill_slot_index)
				end

				SupremeFieldsWGCtrl.Instance:OpenItemAlertTips(item_id, need_num, is_money, ok_func, title_str)
			end
		end

		return
	end

	self.select_kowei = index
	self.select_kowei_data = data

	self:ChangeSelectKoWei()
	self:Flush()
end

function SupremeFieldsBattleView:OnBtnAuto()
	if self.select_kowei == 0 then
		if not SupremeFieldsWGData.Instance:TestSkillSlotHasSurplus() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.SupremeFields.Tips6)
			return
		end

		if not SupremeFieldsWGData.Instance:TestSkillCanBattle() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.SupremeFields.Tips7)
			return
		end

		local count, data_list = SupremeFieldsWGData.Instance:GetAutoBattleSkillList()
		if count <= 0 then return end
		SupremeFieldsWGCtrl.Instance:SendAutoSkillBattle(count, data_list)
	else
		local skill_slot_index = self.select_kowei - 1

		if not self.select_kowei_data.is_open then --技能解锁
			local cfg = SupremeFieldsWGData.Instance:GetSkillHoleCfg(skill_slot_index)
			local is_money = cfg.condition_type == 2
			local need_num = 0
			local item_id = nil

			if is_money then
				need_num = cfg.condition_value
				local price = RoleWGData.GetPayMoneyStr(need_num, cfg.rmb_type, cfg.rmb_seq)
				RechargeWGCtrl.Instance:Recharge(need_num, cfg.rmb_type, cfg.rmb_seq)
			else
				local item_list = Split(cfg.condition_value, "|")
				local title_str = self.select_kowei_data.is_open and Language.SupremeFields.Btn_Text2 or Language.SupremeFields.AlertTitile
				item_id = tonumber(item_list[1])
				need_num = tonumber(item_list[2])

				local ok_func = function ()
					SupremeFieldsWGCtrl.Instance:SendOperation(Supreme_Operation_Index.SKILL_SLOT_UNLOCK, skill_slot_index)
				end

				SupremeFieldsWGCtrl.Instance:OpenItemAlertTips(item_id, need_num, is_money, ok_func, title_str)
			end

			return
		end

		if self.select_skill_data == nil then return end

		if self.select_skill_data.is_put_on then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.SupremeFields.Tips4)
			return
		elseif not self.select_skill_data.is_open then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.SupremeFields.Tips5)
			return
		end

		if self.select_kowei_data.skill_ids[1] <= 0 then
			SupremeFieldsWGCtrl.Instance:SendOperation(Supreme_Operation_Index.EQUIP_SKILL, skill_slot_index, self.select_skill_data.type, self.select_skill_data.lv)
		else
			local ok_func = function ()
				SupremeFieldsWGCtrl.Instance:SendOperation(Supreme_Operation_Index.EQUIP_SKILL, skill_slot_index, self.select_skill_data.type, self.select_skill_data.lv)
			end

			SupremeFieldsWGCtrl.Instance:OpenReplaceAlertTips(self.select_skill_data, self.select_kowei_data, ok_func)
		end
	end
end

function SupremeFieldsBattleView:FlushRightSkillView()
	if self.select_skill_data == nil then return end

 	local skill_cfg = SupremeFieldsWGData.Instance:GetFootLightSkillCfg(self.select_skill_data.skill_id)

	self.node_list.title.text.text = self.select_skill_data.name
	self.node_list.desc.text.text = skill_cfg.skill_txt
	local bundle, asset = ResPath.GetSkillIconById(skill_cfg.icon)
    	self.node_list.icon.image:LoadSpriteAsync(bundle, asset, function ()
    	self.node_list.icon.image:SetNativeSize()
    end)

	self.node_list.supreme_fields_scroll.scroll_rect.verticalNormalizedPosition = 1
end

function SupremeFieldsBattleView:ChangeSelectKoWei()
	if self.skill_slot_list then
		for i = 1, #self.skill_slot_list do
			self.skill_slot_list[i]:SetIsSelect(self.select_kowei)
		end
	end
end

--------------------------------------SkillItemRender-----------------------------------------------

SkillItemRender = SkillItemRender or BaseClass(BaseRender)

function SkillItemRender:__init()

end

function SkillItemRender:__delete()

end

function SkillItemRender:LoadCallBack()

end

function SkillItemRender:OnFlush()
	if nil == self.data then
		return
	end

	local skill_cfg = SupremeFieldsWGData.Instance:GetFootLightSkillCfg(self.data.skill_id)
	local bundle, asset = ResPath.GetSkillIconById(skill_cfg.icon)
	self.node_list.icon.image:LoadSpriteAsync(bundle, asset, function ()
	    self.node_list.icon.image:SetNativeSize()
    end)

    self.node_list.name.text.text = self.data.skill_name
    self.node_list.put_on:SetActive(self.data.is_put_on)
	XUI.SetGraphicGrey(self.node_list.tween_root, not self.data.is_open)
end

function SkillItemRender:OnSelectChange(is_select)
	self.node_list.bg_hl:SetActive(is_select)
end


--------------------------------------SkillSlotRender-----------------------------------------------

SkillSlotRender = SkillSlotRender or BaseClass(BaseRender)

function SkillSlotRender:__init()

end

function SkillSlotRender:__delete()
	self.rember_skill = nil
	self.rember_open = nil
end

function SkillSlotRender:LoadCallBack()
	self.node_list["skill"].button:AddClickListener(BindTool.Bind(self.OnClickCell,self))
end

function SkillSlotRender:OnFlush()
	if nil == self.data then
		return
	end

	if self.data.skill_ids[1] ~= 0 then
		local skill_cfg = SupremeFieldsWGData.Instance:GetFootLightSkillCfg(self.data.skill_ids[1])
		if skill_cfg then
			local bundle, asset = ResPath.GetSkillIconById(skill_cfg.icon)
			self.node_list.ph_icon.image:LoadSpriteAsync(bundle, asset, function ()
		    	self.node_list.ph_icon.image:SetNativeSize()
			end)
		end
	end

	self.node_list.ph_icon:SetActive(self.data.skill_ids[1] ~= 0)
	self.node_list.add:SetActive(self.data.skill_ids[1] == 0 and self.data.is_open)
	self.node_list.suo:SetActive(not self.data.is_open)

	if nil == self.rember_skill or nil == self.rember_open then
		self.rember_skill = self.data.skill_ids[1]
		self.rember_open = self.data.is_open
	elseif self.rember_skill ~= self.data.skill_ids[1] or self.rember_open ~= self.data.is_open then
		local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_jiNeng_up)
		EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["effect"].transform)
		self.rember_skill = self.data.skill_ids[1]
		self.rember_open = self.data.is_open
	end
end

function SkillSlotRender:OnClickCell()
	if nil == self.data then
		return
	end

	self.select_call_back(self.index, self.data)
end

function SkillSlotRender:SetIsSelect(select_index)
	self.node_list["select"]:SetActive(self.index == select_index)
end