ShengPinSortAttrList = {
	[1] = 'maxhp',
	[2] = 'gongji',
	[3] = 'fangyu',
	[4] = 'pojia',
	[5] = 'yuansu_sh',
	[6] = 'yuansu_hj',
}

function EquipmentView:InitShengPinView()
	-- self.is_select_special_stuff = false
	self.sp_btn_delay_ing = false
	self.sp_do_fill_tween = false
	self.old_fill_star_num = 0
	self.sp_old_slider_val = nil
	self.show_cur_tab = nil
	self.is_doing_shengpin_tween = false
	self.sp_effect_2_attach = self.node_list["shenpin_effect_root_2"].gameObject:GetComponent(typeof(Game.GameObjectAttach))

	self.select_part = -1

	self:CreateEquipShengPinList()
	if not self.shengpin_show_item then
		self.shengpin_show_item = ItemCell.New(self.node_list.shengpin_show_item)
		self.shengpin_show_item:SetItemTipFrom(ItemTip.FROM_EQUIPMENT)
	end

	if not self.upgrade_sp_item then
		self.upgrade_sp_item = {}
		for i=1,2 do
			self.upgrade_sp_item[i] = ItemCell.New(self.node_list["upgrade_item_pos_"..i])
			self.upgrade_sp_item[i]:SetNeedItemGetWay(true)
		end
	end

	if not self.special_stuff_item then
		self.special_stuff_item = ItemCell.New(self.node_list.special_item_pos)
		self.special_stuff_item:SetNeedItemGetWay(true)
	end

	if not self.star_list then
		self.star_list = {}
		for i = 1, 9 do
			self.star_list[i] = SPStarRender.New(self.node_list["sp_star_list"]:FindObj("star_" .. i))
			self.star_list[i]:SetIndex(i)
		end
	end

	if nil == self.shengpin_attr_list then
		self.shengpin_attr_list = {}
		for i = 1, 6 do
		    local cell = CommonAddAttrRender.New(self.node_list["shengpin_attr_" ..i])
		    cell:SetIndex(i)
			-- cell:SetAttrNameNeedSpace(true)
		    self.shengpin_attr_list[i] = cell
		end
	end


	XUI.AddClickEventListener(self.node_list["btn_tips_sp"],BindTool.Bind(self.OpenShengPinTip, self))
	XUI.AddClickEventListener(self.node_list["btn_attrtips_sp"],BindTool.Bind(self.OpenSuitAttrShengPinTip, self))
	XUI.AddClickEventListener(self.node_list["btn_upgrade"],BindTool.Bind(self.ShengPinUpGrade, self))
	self.node_list.toggle.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickSpecialStuffItem,self))
end

function EquipmentView:ShengPinViewDeleteMe()
	self:ClearShengPinTweenCD()
	self.select_part = nil
	self.show_cur_tab = nil

	if self.equip_left_sp_list then
		for k,v in pairs(self.equip_left_sp_list) do
			v:DeleteMe()
		end
		self.equip_left_sp_list = nil
	end

	if self.upgrade_sp_item then
		for k,v in pairs(self.upgrade_sp_item) do
			v:DeleteMe()
		end
		self.upgrade_sp_item = nil
	end

	if self.special_stuff_item then
		self.special_stuff_item:DeleteMe()
		self.special_stuff_item = nil
	end

	if self.star_list then
		for k,v in pairs(self.star_list) do
			v:DeleteMe()
		end
	end
	self.star_list = nil

	if self.shengpin_attr_list then
        for k,v in pairs(self.shengpin_attr_list) do
            v:DeleteMe()
        end
        self.shengpin_attr_list = nil
    end

	self.select_sp_part = nil
	self.is_select_special_stuff = false
	self.sp_old_slider_val = nil
	self.sp_do_fill_tween = nil
	self.old_fill_star_num = nil
	self.is_doing_shengpin_tween = nil
	self.sp_effect_2_attach = nil
	self.sp_btn_delay_ing = nil

	self.shengpin_show_index_change = false
	if self.shengpin_show_item then
		self.shengpin_show_item:DeleteMe()
		self.shengpin_show_item = nil
	end
end

function EquipmentView:ShengpinShowIndexCallBack()
	self.shengpin_show_index_change = true
end

--使用特殊材料
function EquipmentView:OnClickSpecialStuffItem(is_on)
	self.is_select_special_stuff = is_on
	EquipmentWGData.Instance:SetIsSelectSpecialItem(is_on)
	RemindManager.Instance:Fire(RemindName.Equipment_ShengPin)
	HomesWGCtrl.Instance:FlushHomesView()
	self:FlushShengPinCostStuff()
end

-- 升级
function EquipmentView:ShengPinUpGrade()
	-- 按钮延迟 解决手快点击 引起动画的显示问题
	if self.sp_btn_delay_ing then
		return
	end

	self.sp_btn_delay_ing = true
	GlobalTimerQuest:AddDelayTimer(BindTool.Bind(function ()
		self.sp_btn_delay_ing = false
	end, self), 0.4)

	if self.is_doing_shengpin_tween then
		return
	end

	-- 		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.show_cur_tab.stuff_id})

	if not self.cur_select_item_data then return end
	local star_count = EquipmentWGData.Instance:GetNewEquipShengPinStarInfoByPart(self.cur_select_item_data.index)
	local now_cfg = EquipmentWGData.Instance:GetNewEquipShengPinCfg(star_count,self.cur_select_item_data.index)
	--普通材料
	local stuff_list = Split(now_cfg.stuff_id,"|")
	local stuff_count_list = Split(now_cfg.stuff_count,"|")
	local meet_count = 0
	for i=1,2 do
		if stuff_list[i] then
			local item_num =  ItemWGData.Instance:GetItemNumInBagById(tonumber(stuff_list[i]))
			if item_num < tonumber(stuff_count_list[i]) then
				TipWGCtrl.Instance:OpenItemTipGetWay({item_id = tonumber(stuff_list[i])})
				return
			end
		end
	end
	if self.is_select_special_stuff then
		local special_stuff_num = ItemWGData.Instance:GetItemNumInBagById(now_cfg.succ_rate_stuff_id)
		if special_stuff_num < now_cfg.succ_rate_stuff_count then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = now_cfg.succ_rate_stuff_id})
			return
		end
	end
	local is_use_special_stuff = self.is_select_special_stuff and 1 or 0
	EquipmentWGCtrl.Instance:SendEquipComposeOperaReq(COMPOSE_EQUIP_OPERA_TYPE.EQUIP_OPERA_TYPE_EQUIP_UPQUALITY, 0,0,
							{self.cur_select_item_data.index,is_use_special_stuff})
end

-- 打开属性tip
function EquipmentView:OpenSuitAttrShengPinTip()
	if not self.select_sp_part then
		return
	end

	local view_type = self.select_sp_part >= 6 and ShengPinAttrTip.ViewType.SpeEquipView or ShengPinAttrTip.ViewType.BaseEquipView
	EquipmentWGCtrl.Instance:OpenShengPinSuitAttr(view_type)
end

function EquipmentView:OpenShengPinTip()
	RuleTip.Instance:SetContent(Language.Equip.ShengPinCont,Language.Equip.ShengPinTipTitle, nil, nil, true)
end

-- 创建装备列表
function EquipmentView:CreateEquipShengPinList()
	if not self.equip_left_sp_list then
	    self.equip_left_sp_list = {}
	    for part = 0, GameEnum.EQUIP_INDEX_XIANZHUO do
	        self.equip_left_sp_list[part] = EquipSPItemRender.New(self.node_list.shengpin_equip_list:FindObj("item_" .. part))
	        self.equip_left_sp_list[part]:SetIndex(part)
	        self.equip_left_sp_list[part]:SetClickCallBack(BindTool.Bind(self.OnSelectEquipSPItemHandler, self))
	    end
    end
end

-- 选择列表项回调
function EquipmentView:OnSelectEquipSPItemHandler(item)
	local item_data = item and item.data
	if not item_data or self.select_sp_part == item_data.index then
		return
	end

	self.sp_old_slider_val = nil
	self.sp_do_fill_tween = false

	self.node_list.toggle.toggle.isOn = false
	self.cur_select_item_data = item_data
	self.select_sp_part = item_data.index
	for k, v in pairs(self.equip_left_sp_list) do
		v:SetSelectIndex(self.select_sp_part)
	end

	if self.is_doing_shengpin_tween then
		self:OnShenPinTweenComplete()
	else
		self:OnFlushShengPinView()
	end
end

function EquipmentView:OnFlushShengPinView()
	if IsEmptyTable(self.cur_select_item_data) then
		return
	end

	self:ClearShengPinTweenCD()
	local item_data = self.cur_select_item_data
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_data.item_id)
	local star_count = EquipmentWGData.Instance:GetNewEquipShengPinStarInfoByPart(item_data.index)
	local now_cfg = EquipmentWGData.Instance:GetNewEquipShengPinCfg(star_count, item_data.index)
	local next_cfg = EquipmentWGData.Instance:GetNewEquipShengPinCfg(star_count + 1, item_data.index)

	self:FlushSPSlider()
	self:ShowSPEquipShowIcon(item_data)

	self:FlushShengPinAttrInfo(item_data, star_count, now_cfg, next_cfg)
	if item_cfg and item_data.is_meet_condition then
		self:SetSpMaxView(false)
		self:SetSpShowNextPart(true)
		self.node_list.sp_nolimit_part:SetActive(true)
		self.node_list.shenping_limit_img:SetActive(false)

		XUI.SetButtonEnabled(self.node_list.btn_upgrade, true)

		if next_cfg then
			self:FlushShengPinCostStuff()
			self:FlushBtnText()

			self.node_list.upgrade_remind:SetActive(item_data.is_remind == 1)

			--星星
			self:FlushSPStarList(now_cfg, star_count % 10)

			--品质属性
			local quality = now_cfg.quality_grade
			local now_quality_attr, now_attr_per, now_attr_type = EquipmentWGData.Instance:GetNewShengPinAttrCfgByQuality(item_data.index, quality)
			local next_quality_attr, next_attr_per, next_attr_type = EquipmentWGData.Instance:GetNewShengPinAttrCfgByQuality(item_data.index, quality + 1)
			if next_quality_attr then
				self.node_list.next_oder_part:SetActive(true)
				self.node_list.oder_level.text.text = string.format(Language.Equip.ShengPinLimitText4, Language.Equip.ColorShengPin_D[quality + 1],
																	Language.Equip.ColorShengPin_D[quality + 2])
				if quality == 0 then -- 0级特殊处理 拿下一级的属性描述名字
					self.node_list.oder_value.text.text = string.format(Language.Equip.ShengPinLimitText11
																	, Language.Stone[item_data.index]
																	, next_attr_type == "base_attr_per" and Language.Tip.CommonAttr or Language.Common.AttrNameList2[next_attr_type]
																	, now_attr_per / 100)
				else
					self.node_list.oder_value.text.text = string.format(Language.Equip.ShengPinLimitText11
																	, Language.Stone[item_data.index]
																	, now_attr_type == "base_attr_per" and Language.Tip.CommonAttr or Language.Common.AttrNameList2[now_attr_type]
																	, now_attr_per / 100)
				end
				self.node_list.next_oder_value.text.text = string.format(Language.Equip.ShengPinLimitText12, next_attr_per / 100)
			else
				self.node_list.next_oder_part:SetActive(false)
				self.node_list.oder_level.text.text = string.format(Language.Equip.shenping_desc_level, Language.Equip.ColorShengPin_D[quality + 1])  
				self.node_list.oder_value.text.text = string.format(Language.Equip.ShengPinLimitText11
																	, Language.Stone[item_data.index]
																	, now_attr_type == "base_attr_per" and Language.Tip.CommonAttr or Language.Common.AttrNameList2[now_attr_type]
																	, now_attr_per / 100)
			end

		else--满级
			self:SetSpMaxView(true)
			self:SetSpShowNextPart(false)
			self.node_list.upgrade_remind:SetActive(false)
			XUI.SetButtonEnabled(self.node_list.btn_upgrade,false)

			local quality = now_cfg.quality_grade
			local now_quality_attr, now_attr_per, now_attr_type = EquipmentWGData.Instance:GetNewShengPinAttrCfgByQuality(item_data.index, quality)
			self:FlushBtnText()
			self.node_list.oder_level.text.text = string.format(Language.Equip.shenping_desc_level, Language.Equip.ColorShengPin_D[quality + 1])  
			self.node_list.oder_value.text.text = string.format(Language.Equip.ShengPinLimitText11
																, Language.Stone[item_data.index]
																, now_attr_type == "base_attr_per" and Language.Tip.CommonAttr or Language.Common.AttrNameList2[now_attr_type]
																, now_attr_per / 100)

			--星星
			self:FlushSPStarList(now_cfg, star_count % 10)
		end

	else --不满足条件
		self:SetSpMaxView(false)
		self:SetSpShowNextPart(false)
		self.node_list.upgrade_remind:SetActive(false)
		self.node_list.sp_nomax_state:SetActive(star_count ~= 0)
		self.node_list.shenping_limit_img:SetActive(star_count == 0)
		local quality = now_cfg.quality_grade
		self.node_list.oder_level.text.text = string.format(Language.Equip.shenping_desc_level, Language.Equip.ColorShengPin_D[quality + 1])   

		local now_quality_attr, now_attr_per, now_attr_type = EquipmentWGData.Instance:GetNewShengPinAttrCfgByQuality(item_data.index, quality)
		local next_quality_attr, next_attr_per, next_attr_type = EquipmentWGData.Instance:GetNewShengPinAttrCfgByQuality(item_data.index, quality + 1)
		if quality == 0 then
			self.node_list.oder_value.text.text = string.format(Language.Equip.ShengPinLimitText11
																	, Language.Stone[item_data.index]
																	, next_attr_type == "base_attr_per" and Language.Tip.CommonAttr or Language.Common.AttrNameList2[next_attr_type]
																	, now_attr_per / 100)
		else
			self.node_list.oder_value.text.text = string.format(Language.Equip.ShengPinLimitText11
																, Language.Stone[item_data.index]
																, now_attr_type == "base_attr_per" and Language.Tip.CommonAttr or Language.Common.AttrNameList2[now_attr_type]
																, now_attr_per / 100)
		end

		self:FlushSPStarList(now_cfg, star_count % 10)
		if star_count == 0 then
			self.node_list.sp_limit_desc2.text.text = item_data.ShengPin_limit_desc
			return
		end
		-- local quality = math.floor(star_count + 1 / 10) + 1

		self.node_list.sp_limit_desc.text.text = item_data.limit_desc
		self:FlushBtnText()
		self:FlushShengPinCostStuff()
	end
end

function EquipmentView:FlushSPStarList(cfg, star_count)
	star_count = star_count or 0
	local cur_progress_num = (star_count - 1 > 0) and (star_count - 1) or 0
	local progress_list = {[0] = 0, [1] = 0.12, [2] = 0.22, [3] = 0.35, [4] = 0.5, [5] = 0.65, [6] = 0.78, [7] = 0.88, [8] = 1}		--对应的进度刻度
	local progress = progress_list[cur_progress_num] or 0

	local do_tween = self.sp_do_fill_tween and cur_progress_num > self.old_fill_star_num
	local tween_time = do_tween and 0.5 or 0
	self.node_list.sp_quality_img_slider.image:LoadSprite(ResPath.GetNoPackPNG("a2_zb_mandi"))
	self.node_list["sp_quality_img_slider"].image:DOFillAmount(progress, tween_time):OnComplete(function()
		if self.star_list and not self.is_doing_shengpin_tween then
			for k, v in pairs(self.star_list) do
				v:ClearDoTweenState()
				v:SetData({cfg = cfg, star_count = star_count, do_tween = do_tween})
			end
			self.old_fill_star_num = cur_progress_num
			self.sp_do_fill_tween = true
		end
	end)
end

function EquipmentView:ShowSPEquipShowIcon(data)
	if IsEmptyTable(data) then
		return
	end
	self.shengpin_show_item:SetData(data)
	self.node_list["shengpin_item_name"].text.text = ItemWGData.Instance:GetItemNameDarkColor(data.item_id)
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
end

function EquipmentView:FlushSPSlider()
	if not self.cur_select_item_data then
		return
	end
	local show_str = ""
	local progress = 0
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.cur_select_item_data.item_id)

	if item_cfg then
		local equip_index = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
		local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(equip_index)
		show_str, progress = EquipmentWGData.Instance:GetShengPinAddSliderStrAndValue(equip_type)

		if self.sp_old_slider_val == nil then
			self.node_list.slider_shengpin_add.slider.value = progress
		else
			self.node_list.slider_shengpin_add.slider:DOValue(progress, 0.5)
		end
		self.sp_old_slider_val = progress
	else
		self.node_list.slider_shengpin_add.slider.value = 0
	end

	self.node_list.special_shengpin_attr.text.text = show_str
end

function EquipmentView:FlushBtnText(star_count)
	local item_data = self.cur_select_item_data
	local btn_text = Language.Equip.ShengPing
	if item_data then
		local star_count = EquipmentWGData.Instance:GetNewEquipShengPinStarInfoByPart(item_data.index)
		if star_count and star_count % 10 == 9 then
			btn_text = Language.Equip.Tupo
		else
			btn_text = Language.Equip.ShengPing
		end
	end
	self.node_list.btn_text.text.text = btn_text
end

function EquipmentView:FlushShengPinCostStuff()
	local item_data = self.cur_select_item_data
	if IsEmptyTable(item_data) then
		return
	end

	local star_count = EquipmentWGData.Instance:GetNewEquipShengPinStarInfoByPart(item_data.index)
	local cfg = EquipmentWGData.Instance:GetNewEquipShengPinCfg(star_count, item_data.index)

	if IsEmptyTable(cfg) then
		return
	end

	local stuff_list = Split(cfg.stuff_id, "|")
	local stuff_count_list = Split(cfg.stuff_count, "|")

	--普通材料
	for i = 1, 2 do
		local stuff_count = stuff_count_list[i] and tonumber(stuff_count_list[i]) or 0
		if stuff_count > 0 and stuff_list[i] then
			self.node_list["upgrade_item_pos_" .. i]:SetActive(true)
			local stuff_cfg = ItemWGData.Instance:GetItemConfig(tonumber(stuff_list[i]))
			local item_num =  ItemWGData.Instance:GetItemNumInBagById(tonumber(stuff_list[i]))
			local color = item_num >= stuff_count and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
			self.upgrade_sp_item[i]:SetData({item_id = stuff_cfg.id})
			self.upgrade_sp_item[i]:SetRightBottomText(ToColorStr(item_num .. '/' .. stuff_count, color))
			self.upgrade_sp_item[i]:SetRightBottomTextVisible(true)
		else
			self.node_list["upgrade_item_pos_" .. i]:SetActive(false)
		end
	end

	if cfg.succ_rate_stuff_count > 0 then
		--特殊材料
		local special_stuff = ItemWGData.Instance:GetItemConfig(cfg.succ_rate_stuff_id)
		local special_stuff_num = ItemWGData.Instance:GetItemNumInBagById(cfg.succ_rate_stuff_id)
		local special_color = special_stuff_num >= cfg.succ_rate_stuff_count and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
		self.special_stuff_item:SetData({item_id = special_stuff.id})
		self.special_stuff_item:SetRightBottomText(ToColorStr(special_stuff_num .. '/' .. cfg.succ_rate_stuff_count, special_color))
		self.special_stuff_item:SetRightBottomTextVisible(true)
		self.node_list["upgrade_item_pos_3"]:SetActive(true)
	else
		self.node_list["upgrade_item_pos_3"]:SetActive(false)
	end

	local rate = self.is_select_special_stuff and 100 or cfg.origin_succ_rate * 0.01
	local success_rate_str = string.format(Language.Equip.ShengPinLimitText8, rate)
	local yunshi_addition_cfg = QifuYunShiWGData.Instance:GetHasYunShiAdditionIdCfg(QifuYunShiWGData.ADDITION_TYPE.SHENGPING_TYPE)
	local yunshi_addition_value = 0
	if yunshi_addition_cfg then
		yunshi_addition_value = yunshi_addition_cfg.addition_value * 0.01
	end
	-- local value = string.format(Language.YunShi.SizeRateStr,yunshi_addition_value)
	local str = string.format(Language.YunShi.AdditionRateStr,yunshi_addition_value)
	success_rate_str = success_rate_str .. str
	self.node_list.success_rate.text.text = success_rate_str
end

function EquipmentView:FlushShengPinAttrInfo(item_data, star_count, now_cfg, next_cfg)
	local attr_data = EquipWGData.GetSortAttrListHaveNextByCfg(now_cfg, next_cfg)

	local need_show_up_effect = false
	local index = item_data.index

	if nil ~= self.shengpin_index_cache and nil ~= self.shengpin_level_cache then
		if (self.shengpin_index_cache == index) and (star_count > self.shengpin_level_cache) then
			need_show_up_effect = true
		end
	end

	for k, v in ipairs(self.shengpin_attr_list) do
		v:SetData(attr_data[k])

		if need_show_up_effect then
            v:PlayAttrValueUpEffect()
        end
	end

	self.shengpin_index_cache = index
	self.shengpin_level_cache = star_count
end

function EquipmentView:SetSpMaxView(is_max)
	self.node_list["sp_max_state"]:SetActive(is_max)
	self.node_list["sp_nomax_state"]:SetActive(not is_max)
end

function EquipmentView:SetSpShowNextPart(show_next)
	self.node_list["next_oder_part"]:SetActive(show_next)
	self.node_list.success_rate:SetActive(show_next)
	self.node_list.btn_upgrade:SetActive(show_next)
	self.node_list.sp_limit_desc:SetActive(not show_next)
end


local shenpin_result_effect = {
	[EQUIP_SHENGPING_RESULT.UP_STAR_SUCCESS] = UIEffectName.s_fumo,
	[EQUIP_SHENGPING_RESULT.UP_STAR_FAILURE] = UIEffectName.f_fumo,
	[EQUIP_SHENGPING_RESULT.UP_QUALITY_SUCCESS] = UIEffectName.s_tupo,
	[EQUIP_SHENGPING_RESULT.UP_QUALITY_FAILURE] = UIEffectName.f_tupo,
}

function EquipmentView:FlushSpViewAndShowEffect(result_list)
	if IsEmptyTable(result_list) then
		return
	end
	-- 升品成功花里胡哨特效表现
	if result_list.result_type == EQUIP_SHENGPING_RESULT.UP_QUALITY_SUCCESS and result_list.quality_id > 0 then
		self.is_doing_shengpin_tween = true
		self.node_list.upgrade_remind:SetActive(false)
		XUI.SetButtonEnabled(self.node_list.btn_upgrade, false)
		self:DoShengPinTween(result_list.quality_id)
	else
		self:ShowSPResultEffect(result_list.result_type)
		self:FlushShengPin()
	end
end

local EXPLOSION_EFFECT = {
	[1] = "UI_shenpin_baodian_lv",
	[2] = "UI_shenpin_baodian_lan",
	[3] = "UI_shenpin_baodian_zi",
	[4] = "UI_shenpin_baodian_cheng",
	[5] = "UI_shenpin_baodian_hong",
	[6] = "UI_shenpin_baodian_fen",
}

function EquipmentView:DoShengPinTween(quality_id)
	self:ClearShengPinTweenCD()
	local interval = 0.02
	local rally_end_time = 1.4	-- 光聚集特效结束时间
	local fly_time = 0.5		-- 光飞行时间
	local explosion_time = 0.5	-- 爆炸播放时间
	local do_fly = false
	local do_expl = false
	local old_text_cd = 0
	local total_time = rally_end_time + fly_time + explosion_time
	for k, v in pairs(self.star_list) do
		v:DoUpQualityTween1()
	end

	CountDownManager.Instance:AddCountDown("do_shengpin_tween",
		function(elapse_time, total_time)
			local text_cd = math.floor(total_time - elapse_time)
			if old_text_cd ~= text_cd then
				self.node_list.btn_text.text.text = string.format(Language.Equip.ShengPingCD, text_cd)
				old_text_cd = text_cd
			end

			if elapse_time >= rally_end_time and not do_fly then
				for k, v in pairs(self.star_list) do
					v:DoUpQualityTween2(self.node_list["shenpin_effect_root_2"].transform.position, fly_time)
				end
				do_fly = true
			elseif elapse_time >= (rally_end_time + fly_time - interval) and not do_expl then
				if self.node_list["shenpin_effect_root_2"] then
					self.node_list["shenpin_effect_root_2"]:SetActive(false)
					local bundle_name, asset_name = ResPath.GetEffectUi(EXPLOSION_EFFECT[quality_id])
					self.sp_effect_2_attach.BundleName = bundle_name
					self.sp_effect_2_attach.AssetName = asset_name
					self.node_list["shenpin_effect_root_2"]:SetActive(true)
				end
				self:ShowSPResultEffect(EQUIP_SHENGPING_RESULT.UP_QUALITY_SUCCESS)
				do_expl = true
			end
		end,

		function()
			self:OnShenPinTweenComplete()
		end,
	nil, total_time, interval)
end

function EquipmentView:OnShenPinTweenComplete()
	if self.node_list["shenpin_effect_root_2"] then
		self.node_list["shenpin_effect_root_2"]:SetActive(false)
	end

	self.is_doing_shengpin_tween = false
	self:FlushShengPin()
end

function EquipmentView:ShowSPResultEffect(result_type)
	local is_success = result_type % 2 == 1 and true or false
	TipWGCtrl.Instance:ShowEffect({effect_type = shenpin_result_effect[result_type], is_success = is_success, pos = Vector2(0, 0)})
		
	-- local result_b_n, result_a_n = ResPath.GetEffectUi(shenpin_result_effect[result_type])
	-- self.node_list["shenpin_effect_root"]:SetActive(true)
	--EffectManager.Instance:PlayAtTransform(result_b_n, result_a_n, self.node_list["shenpin_effect_root"].transform)
	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
end

function EquipmentView:ClearShengPinTweenCD()
	if CountDownManager.Instance:HasCountDown("do_shengpin_tween") then
		CountDownManager.Instance:RemoveCountDown("do_shengpin_tween")
	end
end

-- 刷新
function EquipmentView:FlushShengPin()
	local cur_data = self.cur_select_item_data
	local cur_index = cur_data and cur_data.index
	local data_list, default_index = EquipmentWGData.Instance:GetNewEquipShengPinDataList(self.shengpin_show_index_change, cur_index)
	self.shengpin_show_index_change = false
	for k,v in pairs(self.equip_left_sp_list) do
		v:SetData(data_list[k])
	end
	if self.equip_left_sp_list[default_index] then
		self.equip_left_sp_list[default_index]:OnClick()
	end


	self:OnFlushShengPinView()
	-- --当前没有装备
	if IsEmptyTable(data_list) then
		self:FlushBtnText()
		XUI.SetButtonEnabled(self.node_list.btn_upgrade, false)
		return
    end
    local active_remind = EquipmentWGData.Instance:GetShenPinLevelRemind() or 0
	self.node_list["shengping_remind"]:SetActive( active_remind > 0)
end

----------------------------------------------------------------------------------------------------

EquipSPItemRender = EquipSPItemRender or BaseClass(BaseRender)

function EquipSPItemRender:LoadCallBack()
	self.item = ItemCell.New(self.node_list.ph_item)
	self.item:SetIsShowTips(false)
	self.toggle = self.view.toggle
	XUI.AddClickEventListener(self.node_list.block_click, BindTool.Bind(self.OnClick, self))
end

function EquipSPItemRender:__delete()
	if self.item then
		self.item:DeleteMe()
		self.item = nil
	end
	self.toggle = nil
end

function EquipSPItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.item:ClearData()
		self.item:SetItemIcon(ResPath.GetEquipIcon(self.index))
		if self.node_list.text_bg then
			self.node_list.text_bg:SetActive(false)
		end
		return
	end
	self.item:SetData(self.data)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if self.node_list.text_bg then
		self.node_list.text_bg:SetActive(true)
	end
	if item_cfg then
		local star_count = EquipmentWGData.Instance:GetNewEquipShengPinStarInfoByPart(self.data.index)
		local str = ""
		if star_count > 0 then
			local shengpin_cfg = EquipmentWGData.Instance:GetNewEquipShengPinCfg(star_count, self.data.index)

			if shengpin_cfg and shengpin_cfg.quality_grade and shengpin_cfg.quality_grade > 0 then
				str = Language.Equip.shengPin_itemTip[shengpin_cfg.quality_grade + 1]
			end
		end
	end

	self.node_list.lbl_text.text.text = ToColorStr(self.data.limit_desc, COLOR3B.PINK )

	self.node_list.img_part_is_uplevel.image.enabled = self.data.is_remind == 1
end

function EquipSPItemRender:OnSelectChange(is_select)
	-- 高亮
	self.node_list.img9_item_bg_hl.image.enabled = is_select
end

---------------------------------------tip------------------------------------------
ShengPinAttrTip = ShengPinAttrTip or BaseClass(SafeBaseView)
ShengPinAttrTip.ViewType = {
	BaseEquipView = 0,
	SpeEquipView = 1,
}

function ShengPinAttrTip:__init()
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/equipfumo_ui_prefab", "layout_shengpin_attr_tip")
end

function ShengPinAttrTip:__delete()

end

function ShengPinAttrTip:ReleaseCallBack()
	self.lbl_cur_qh_level = nil
	self.lbl_next_target = nil
	self.lbl_next_lj_level = nil

	self.layout_cur_attr_add = nil
	self.layout_next_attr_add = nil

	if self.level_change_event then
		GlobalEventSystem:UnBind(self.level_change_event)
		self.level_change_event = nil
	end

	if self.normal_cur_attr_list ~= nil then
		for k,v in pairs(self.normal_cur_attr_list) do
			v:DeleteMe()
		end

		self.normal_cur_attr_list = nil
	end

	if self.normal_next_attr_list ~= nil then
		for k,v in pairs(self.normal_next_attr_list) do
			v:DeleteMe()
		end

		self.normal_next_attr_list = nil
	end

	if self.xianqi_cur_attr_list ~= nil then
		for k,v in pairs(self.xianqi_cur_attr_list) do
			v:DeleteMe()
		end

		self.xianqi_cur_attr_list = nil
	end

	if self.xianqi_next_attr_list ~= nil then
		for k,v in pairs(self.xianqi_next_attr_list) do
			v:DeleteMe()
		end

		self.xianqi_next_attr_list = nil
	end
end

function ShengPinAttrTip:LoadCallBack(index, loaded_times)
	self.lbl_cur_qh_level = self.node_list.lbl_cur_qh_level
	self.lbl_next_lj_level = self.node_list.lbl_next_lj_level

	self.layout_cur_attr_add = self.node_list.layout_cur_attr_add
	self.layout_next_attr_add = self.node_list.layout_next_attr_add

	self.normal_cur_attr_list = {}
	self.normal_next_attr_list = {}
	self.xianqi_cur_attr_list = {}
	self.xianqi_next_attr_list = {}
	for i = 1, 8 do
		self.normal_cur_attr_list[i] = EquipAddAttrRender.New(self.node_list.layout_cur_attr_add:FindObj("rich_cur_" .. i))
		self.normal_next_attr_list[i] = EquipAddAttrRender.New(self.node_list.layout_next_attr_add:FindObj("rich_next_" .. i))
	end

	for i = 1, 8 do
		self.xianqi_cur_attr_list[i] = EquipAddAttrRender.New(self.node_list.xianqi_attr_list:FindObj("rich_cur_" .. i))
		self.xianqi_next_attr_list[i] = EquipAddAttrRender.New(self.node_list.xianqi_next_attr_list:FindObj("rich_next_" .. i))
	end

	-- local list_view_delegate1 = self.node_list.layout_cur_attr_add.list_simple_delegate
	-- list_view_delegate1.NumberOfCellsDel = BindTool.Bind(self.GetNormalCurNumberOfCells, self)
 --    list_view_delegate1.CellRefreshDel = BindTool.Bind(self.CreateNormalCurArrListView, self)

	self.level_change_event = GlobalEventSystem:Bind(OtherEventType.ShengPingAvtiveLevel, BindTool.Bind(self.LevelChangeCallBack, self))
	self.node_list["equip_xianpin_active1"].button:AddClickListener(BindTool.Bind(self.OnClickActiveXianPinLevel, self,1))
	self.node_list["equip_xianpin_active2"].button:AddClickListener(BindTool.Bind(self.OnClickActiveXianPinLevel, self,2))
end

function ShengPinAttrTip:GetNormalCurNumberOfCells()

end

function ShengPinAttrTip:SetViewType(view_type)
	self.show_view_type = view_type
end

function ShengPinAttrTip:ShowIndexCallBack()
	self.show_view_type = self.show_view_type or ShengPinAttrTip.ViewType.BaseEquipView
	self:Flush()

	self.node_list.tween_root.transform.anchoredPosition = Vector2(1000,0)
	self.node_list.tween_root.rect:DOAnchorPosX(0, 0.5)
end

function ShengPinAttrTip:OpenCallBack()
	EquipmentWGData.Instance:GetUpQualityActiveLevelChange()
end

function ShengPinAttrTip:CheckAndPlayActiveEffect()
	if EquipmentWGData.Instance:GetUpQualityActiveLevelChange() then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo, is_success = true,pos = Vector2(0, 0), parent_node = self.node_list["tj_effect"]})
	end
end

function ShengPinAttrTip:LevelChangeCallBack()
	self:Flush()
	self:CheckAndPlayActiveEffect()

end

--刷新数据
function ShengPinAttrTip:OnFlush()
	self:FlushNomalEquip()
	self:FlushXianQiEquip()
end

function ShengPinAttrTip:OnClickActiveXianPinLevel(index)
	if index == 1 then
		local equip_active_level = EquipmentWGData.Instance:GetEquipUpQualityLV()
		EquipmentWGCtrl.Instance:SendEquipXianPinActiveReq(COMPOSE_EQUIP_OPERA_TYPE.EQUIP_OPERA_TYPE_EQUIP_ACTIVE,
														equip_active_level + 1)
	elseif index == 2 then
		local equip_active_level = EquipmentWGData.Instance:GetXianQiUpQualityLV()
		EquipmentWGCtrl.Instance:SendEquipXianPinActiveReq(COMPOSE_EQUIP_OPERA_TYPE.EQUIP_OPERA_TYPE_XIANQI_ACTIVE,
														equip_active_level + 1)
	end
end

function ShengPinAttrTip:SetAttrListData(attr_cell_list, cfg)
	if IsEmptyTable(attr_cell_list) then
		return
	end

	local tab = EquipWGData.GetSortAttrListByCfg(cfg)
	for k,v in pairs(attr_cell_list) do
		local attr_data = tab[k]
		if not IsEmptyTable(attr_data) then
			local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(attr_data.attr_str)
			local per_value = is_per and attr_data.attr_value / 100 or attr_data.attr_value
			local per_str = is_per and "%" or ""
			local data = {name = Language.Common.AttrNameList2[attr_data.attr_str] or "", value = "+" .. per_value .. per_str}
			v:SetData(data)
		else
			v:SetData(nil)
		end
	end
end

function ShengPinAttrTip:FlushNomalEquip()
	self:ClearNomalEquip()
	local quality_list = EquipmentWGData.Instance:GetNewEquipShengPinDataByTy(GameEnum.EQUIP_BIG_TYPE_NORMAL)
	if not quality_list then return end
	local equip_active_level = EquipmentWGData.Instance:GetEquipUpQualityLV()
	local cur_cfg = EquipmentWGData.Instance:GetNewNormalEquipShengPinSuitCfg(equip_active_level)
	local next_cfg = EquipmentWGData.Instance:GetNewNormalEquipShengPinSuitCfg(equip_active_level+1)
	if next_cfg then
		self.node_list.lbl_cur_qh_level:SetActive(cur_cfg.quality ~= 0)
		self.node_list.layout_cur_attr_add:SetActive(cur_cfg.quality ~= 0)
		self.node_list.cur_level_bg:SetActive(cur_cfg.quality ~= 0)
		self.node_list.no_attr_tip:SetActive(cur_cfg.quality == 0)
		self.node_list.lbl_next_lj_level:SetActive(true)
		self.node_list.layout_next_attr_add:SetActive(true)
		-- self.node_list.no_attr_tip.text.text = Language.Equipment.NoGetValue
		self.node_list.desc_no_attr_tip.text.text = Language.Equipment.NoGetValue
		if cur_cfg.quality ~= 0 then
			self.node_list.lbl_cur_qh_level.text.text = string.format(Language.Equip.CurSPLevelText_3,
									Language.Equip.ColorShengPin_D[cur_cfg.quality + 1])

			self:SetAttrListData(self.normal_cur_attr_list, cur_cfg)
		end

		local next_quality_count = EquipmentWGData.Instance:GetNewShengPinLowestCount(GameEnum.EQUIP_BIG_TYPE_NORMAL, next_cfg.quality)
		local is_enough = next_quality_count == GameEnum.EQUIP_BIG_TYPE_NORMAL_COUNT
		local color = is_enough and "#99ffbb" or "#ff9292"
		local ratio_str = string.format(" <color=%s>(%s/%s)</color>", color, next_quality_count, GameEnum.EQUIP_BIG_TYPE_NORMAL_COUNT)
		-- ratio_str = is_enough and "" or ratio_str
		local next_level_str = string.format(Language.Equip.CurSPLevelText_3, Language.Equip.ColorShengPin_D[next_cfg.quality + 1]) .. ratio_str
		self.node_list.lbl_next_lj_level.text.text = next_level_str
		self:SetAttrListData(self.normal_next_attr_list, next_cfg)

		self.node_list.equip_xianpin_active1:SetActive(true)
		if next_quality_count >= GameEnum.EQUIP_BIG_TYPE_NORMAL_COUNT then
			XUI.SetButtonEnabled(self.node_list["equip_xianpin_active1"],true)
			self.node_list["equip_xianpin_remind1"]:SetActive(true)
		else
			XUI.SetButtonEnabled(self.node_list["equip_xianpin_active1"],false)
			self.node_list["equip_xianpin_remind1"]:SetActive(false)
		end
	else--满阶
		self.node_list.lbl_cur_qh_level:SetActive(true)
		self.node_list.lbl_cur_qh_level.text.text = string.format(Language.Equip.CurSPLevelText_3,
																Language.Equip.ColorShengPin_D[cur_cfg.quality + 1])

		self:SetAttrListData(self.normal_cur_attr_list, cur_cfg)
        self.node_list["equip_xianpin_remind1"]:SetActive(false)
		self.node_list.equip_xianpin_active1:SetActive(false)
		self.node_list.max_level_tip:SetActive(true)
		self.node_list.normal_max_attr_tip:SetActive(true)
		self.node_list.layout_cur_attr_add:SetActive(true)
	end
end

function ShengPinAttrTip:ClearNomalEquip()
	for i = 1, 8 do
		self.normal_cur_attr_list[i]:SetData(nil)
		self.normal_next_attr_list[i]:SetData(nil)
	end

	-- self.node_list.no_attr_tip.text.text = ""
	self.node_list.desc_no_attr_tip.text.text = ""
	self.node_list.max_level_tip:SetActive(false)
	self.node_list.lbl_next_lj_level:SetActive(false)
	self.node_list.layout_next_attr_add:SetActive(false)
	self.node_list.lbl_cur_qh_level:SetActive(false)
	self.node_list.layout_cur_attr_add:SetActive(false)
	self.node_list.no_attr_tip:SetActive(false)
	self.node_list.normal_max_attr_tip:SetActive(false)
end

function ShengPinAttrTip:FlushXianQiEquip()
	self:ClearXianQiEquip()
	local quality_list = EquipmentWGData.Instance:GetNewEquipShengPinDataByTy(GameEnum.EQUIP_BIG_TYPE_XIANQI)
	if not quality_list then return end
	local equip_active_level = EquipmentWGData.Instance:GetXianQiUpQualityLV()
	local cur_cfg = EquipmentWGData.Instance:GetNewXianQiEquipShengPinSuitCfg(equip_active_level)
	local next_cfg = EquipmentWGData.Instance:GetNewXianQiEquipShengPinSuitCfg(equip_active_level+1)
	if next_cfg then
		self.node_list.xianqi_desc1:SetActive(cur_cfg.quality ~= 0)
		self.node_list.xianqi_attr_list:SetActive(cur_cfg.quality ~= 0)
		self.node_list.cur_level_bg1:SetActive(cur_cfg.quality ~= 0)
		self.node_list.no_xianqi_attr_tip:SetActive(cur_cfg.quality == 0)
		self.node_list.xianqi_desc2:SetActive(true)
		self.node_list.xianqi_next_attr_list:SetActive(true)
		--self.node_list.no_xianqi_attr_tip.text.text = Language.Common.No
		if cur_cfg.quality ~= 0 then
			self.node_list.xianqi_desc1.text.text = string.format(Language.Equip.CurSPLevelText_4,
																Language.Equip.ColorShengPin_D[cur_cfg.quality + 1])

			self:SetAttrListData(self.xianqi_cur_attr_list, cur_cfg)
		end
		local next_quality_count = EquipmentWGData.Instance:GetNewShengPinLowestCount(GameEnum.EQUIP_BIG_TYPE_XIANQI, next_cfg.quality)
		local is_enough = next_quality_count >= GameEnum.XIANQI_SHENPIN_ADD_NEED_COUNT
		local color = is_enough and "#95f5a7" or "#ff9292"
		local ratio_str = string.format(" <color=%s>(%s/%s)</color>", color, next_quality_count, GameEnum.XIANQI_SHENPIN_ADD_NEED_COUNT)
		-- ratio_str = is_enough and "" or ratio_str
		local next_level_str = string.format(Language.Equip.CurSPLevelText_4,
											Language.Equip.ColorShengPin_D[next_cfg.quality + 1]) .. ratio_str
		self.node_list.xianqi_desc2.text.text = next_level_str

		self:SetAttrListData(self.xianqi_next_attr_list, next_cfg)

		self.node_list.equip_xianpin_active2:SetActive(true)
		if next_quality_count >= GameEnum.XIANQI_SHENPIN_ADD_NEED_COUNT then
			XUI.SetButtonEnabled(self.node_list["equip_xianpin_active2"],true)
			self.node_list["equip_xianpin_remind2"]:SetActive(true)
		else
			XUI.SetButtonEnabled(self.node_list["equip_xianpin_active2"],false)
			self.node_list["equip_xianpin_remind2"]:SetActive(false)
		end
	else--满阶
		self.node_list.xianqi_desc1:SetActive(true)
		self.node_list.xianqi_desc1.text.text = string.format(Language.Equip.CurSPLevelText_4,
																Language.Equip.ColorShengPin_D[cur_cfg.quality + 1])

		self:SetAttrListData(self.xianqi_cur_attr_list, cur_cfg)
        self.node_list["equip_xianpin_remind2"]:SetActive(false)
		self.node_list.equip_xianpin_active2:SetActive(false)
		self.node_list.xianqi_max_level_tip:SetActive(true)
		self.node_list.xianqi_max_attr_tip:SetActive(true)
		self.node_list.xianqi_attr_list:SetActive(true)
	end
end

function ShengPinAttrTip:ClearXianQiEquip()
	for i = 1, 8 do
		self.xianqi_cur_attr_list[i]:SetData(nil)
		self.xianqi_next_attr_list[i]:SetData(nil)
	end
	self.node_list.xianqi_desc1:SetActive(false)
	self.node_list.xianqi_desc2:SetActive(false)
	self.node_list.no_xianqi_attr_tip:SetActive(false)
	self.node_list.xianqi_max_level_tip:SetActive(false)
	self.node_list.xianqi_next_attr_list:SetActive(false)
	self.node_list.xianqi_attr_list:SetActive(false)
	self.node_list.xianqi_max_attr_tip:SetActive(false)
end