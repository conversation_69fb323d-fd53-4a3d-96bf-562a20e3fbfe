-- 小鸭疾走场景面板
DuckRaceFightView = DuckRaceFightView or BaseClass(SafeBaseView)

function DuckRaceFightView:__init()
	self.active_close = false
	self.view_layer = UiLayer.MainUIHigh
    self.view_cache_time = 0
    self.is_safe_area_adapter = true
	self:AddViewResource(0, "uis/view/duck_race_ui_prefab", "duck_race_fight_layout")
end

function DuckRaceFightView:__delete()

end

function DuckRaceFightView:OpenCallBack()
end

function DuckRaceFightView:CloseCallBack()
end

function DuckRaceFightView:LoadCallBack()
	--XUI.AddClickEventListener(self.node_list["follow_btn"], BindTool.Bind(self.OnClickFollow, self)) 		-- 跟随
	--XUI.AddClickEventListener(self.node_list["bet_btn"], BindTool.Bind(self.OnClickBet, self)) 				-- 下注
	XUI.AddClickEventListener(self.node_list["results_btn"], BindTool.Bind(self.OnClickResults, self)) 		-- 结算
	--XUI.AddClickEventListener(self.node_list["leave_btn"], BindTool.Bind(self.OnClickLeave, self)) 			-- 离开
	XUI.AddClickEventListener(self.node_list["inspire_btn"], BindTool.Bind(self.OnClickInspire, self)) 		-- 鼓舞
	XUI.AddClickEventListener(self.node_list["disturb_btn"], BindTool.Bind(self.OnClickDisturb, self)) 		-- 干扰
	XUI.AddClickEventListener(self.node_list["to_bet_point_btn"], BindTool.Bind(self.OnClickBet, self)) 		-- 下注
	XUI.AddClickEventListener(self.node_list["fast_follow_btn"], BindTool.Bind(self.OnClickFastFollowBtn, self)) 		-- 前往起点按钮

	-- 挂到主界面上
	local left_parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
	self.node_list["duck_race_fight_left_view"].transform:SetParent(left_parent.transform, false)
    self.node_list["race_start"]:SetActive(false)
    self.node_list["race_end"]:SetActive(false)
    self.node_list["remind_arrow"].transform:DOLocalMoveX(50, 0.5):SetLoops(-1, DG.Tweening.LoopType.Yoyo)

    self.menu_icon_click_event = GlobalEventSystem:Bind(MainUIEventType.MAIN_MENU_ICON_CHANGE, BindTool.Bind1(self.OnMenuIconChange, self))
	local size_delta = self.node_list["map_raw"].rect.sizeDelta
	self.map_width = size_delta.x
	self.map_height = size_delta.y
	self.node_list["map_icon_prefab"]:SetActive(false)
end

function DuckRaceFightView:ReleaseCallBack()
	if self.map_role_head_icon_list then
		for i,v in ipairs(self.map_role_head_icon_list) do
			ResMgr:Destroy(v.obj.gameObject)
		end
	end
	self.map_role_head_icon_list = nil

	self.last_pos_dic = nil
	-- SetParent会设不回去，直接销毁掉
	ResMgr:Destroy(self.node_list["duck_race_fight_left_view"].gameObject)
	
	if self.arrow_click_event then
		GlobalEventSystem:UnBind(self.arrow_click_event)
		self.arrow_click_event = nil
	end
    if nil ~= self.menu_icon_click_event then
        GlobalEventSystem:UnBind(self.menu_icon_click_event)
        self.menu_icon_click_event = nil
    end

	self:CancelBetTimer()
	self:CancelNextRoundTimer()
	self:CancelInspireCDTimer()
	self:CancelDisturbCDTimer()
end


function DuckRaceFightView:OnFlush(param_t, index)
	for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushAllView()
			self:FlushMapPanel()
		elseif k == "show_race_start" then
			self:ShowRaceStart()
		elseif k == "show_race_end" then
			self:ShowRaceEnd()
		end
	end
end

function DuckRaceFightView:FlushAllView()
	self.node_list["end_game_panel"]:SetActive(DuckRaceWGData.Instance:GetIsGameOver())
	self.node_list["normal_content"]:SetActive(not DuckRaceWGData.Instance:GetIsGameOver())
	self.node_list["bet_red"]:SetActive(DuckRaceWGData.Instance:GetCanFetchPlayCoin())	
	for duck_index = 0, DuckRaceWGData.CROSS_RACING_DUCK_INFO_COUNT - 1 do
		local bet_count = DuckRaceWGData.Instance:GetMyBetCountByDuckIndex(duck_index)
		self.node_list["zw_num_" .. duck_index].text.text = bet_count
	end

	if not DuckRaceWGData.Instance:GetIsGameOver() then
		self.node_list["round_desc"].text.text = DuckRaceWGData.Instance:GetRoundDesc(true) 			-- 轮次描述
		self.node_list["round_state_desc"].text.text = DuckRaceWGData.Instance:GetRoundStateDesc() 	-- 当前轮次状态
		--[[-- 每只鸭子的比赛进度
		local duck_progress_list = DuckRaceWGData.Instance:GetDuckProgressList()
		for duck_index = 0, #duck_progress_list do
			self.node_list["duck_slider_" .. duck_index].slider.value = duck_progress_list[duck_index] / 100
		end--]]
	else
		self.node_list.rule_desc.text.text = Language.DuckRace.RuleTipDesc
	end

	self.node_list["leader_duck_name"].text.text = DuckRaceWGData.Instance:GetLeaderDuckName() 	-- 第一名鸭子的名称
	-- 按钮的显示和隐藏
	local round_state = DuckRaceWGData.Instance:GetCurRoundState()
	--self.node_list["follow_btn"]:SetActive(round_state == DUCK_RACE_ROUND_STATE.RACING)
	self.node_list["results_btn"]:SetActive(round_state == DUCK_RACE_ROUND_STATE.RESULT or round_state == 0)
	--self.node_list["bet_btn"]:SetActive(round_state == DUCK_RACE_ROUND_STATE.BET)
	--self.node_list["to_start_point_btn"]:SetActive(round_state ~= DUCK_RACE_ROUND_STATE.RACING)

	-- 剩余鼓舞次数
	local inspire_count = DuckRaceWGData.Instance:GetSurplusInspireCount()
	self.node_list["inspire_amount"].text.text = inspire_count
	self.node_list["inspire_amount_bg"]:SetActive(true)

	-- 剩余干扰次数
	local disturb_count = DuckRaceWGData.Instance:GetSurplusDisturbCount()
	self.node_list["disturb_amount"].text.text = disturb_count
	self.node_list["disturb_amount_bg"]:SetActive(true)
	-- 操作按钮面板
	self.node_list["duck_op_panel"]:SetActive(round_state == DUCK_RACE_ROUND_STATE.RACING and DuckRaceWGData.Instance:GetFollowDuckIndex() ~= nil)

	--self:FlushDuckSliderSibling()
	self:FlushBetCountDown()
	self:FlushNextRoundCountDown()
	self:AddFlushInspireCD()
	self:AddFlushDisturbCD()
end

function DuckRaceFightView:FlushDuckSliderSibling()
	-- 当前跟随的鸭子显示到最前面
	local index = DuckRaceWGData.Instance:GetFollowDuckIndex()
	if index then
		self.node_list["duck_slider_" .. index].transform:SetAsLastSibling()
	end
end

-----------------------刷新下注倒计时-------------------------- 
function DuckRaceFightView:FlushBetTime()
	if not self.node_list["bet_count_down"] or not self.node_list["bet_time"] then
		return
	end
	local seconds = DuckRaceWGData.Instance:GetNextStateTimstamp() - TimeWGCtrl.Instance:GetServerTime()
	if DuckRaceWGData.Instance:GetCurRoundState() == DUCK_RACE_ROUND_STATE.BET and seconds > 0 then
		self.node_list["bet_count_down"]:SetActive(true)
		self.node_list["bet_time"].text.text = TimeUtil.FormatSecondDHM4(seconds)
	else
		self.node_list["bet_count_down"]:SetActive(false)
		self:CancelBetTimer()
	end
end

-- 刷新下注倒计时
function DuckRaceFightView:FlushBetCountDown()
	self:CancelBetTimer()
	self:FlushBetTime()
	self.bet_timer = GlobalTimerQuest:AddRunQuest(function()
		self:FlushBetTime()
 	end, 1)
end

function DuckRaceFightView:CancelBetTimer()
	if self.bet_timer then
		GlobalTimerQuest:CancelQuest(self.bet_timer)
		self.bet_timer = nil
	end
end
-----------------------刷新下注倒计时End-------------------------- 

-----------------------刷新下一轮倒计时-------------------------- 
function DuckRaceFightView:FlushNextRoundTime()
	if not self.node_list["next_round_count_down"] or not self.node_list["next_round_time"] then
		return
	end
	local seconds = DuckRaceWGData.Instance:GetNextRoundTimstamp() - TimeWGCtrl.Instance:GetServerTime()
	if DuckRaceWGData.Instance:GetCurRoundState() == DUCK_RACE_ROUND_STATE.RESULT and seconds > 0 then
		self.node_list["next_round_count_down"]:SetActive(true)
		self.node_list["next_round_time"].text.text = TimeUtil.FormatSecondDHM4(seconds)
	else
		self.node_list["next_round_count_down"]:SetActive(false)
		self:CancelNextRoundTimer()
	end
end

function DuckRaceFightView:FlushNextRoundCountDown()
	self:CancelNextRoundTimer()
	self:FlushNextRoundTime()
	self.next_round_timer = GlobalTimerQuest:AddRunQuest(function()
		self:FlushNextRoundTime()
 	end, 1)
end

function DuckRaceFightView:CancelNextRoundTimer()
	if self.next_round_timer then
		GlobalTimerQuest:CancelQuest(self.next_round_timer)
		self.next_round_timer = nil
	end
end
-----------------------刷新下一轮倒计时End-------------------------- 


-- 刷新鼓舞倒计时
function DuckRaceFightView:AddFlushInspireCD()
	self:CancelInspireCDTimer()
	self:FlushInspireCD()
	self.inspire_cd_timer = GlobalTimerQuest:AddRunQuest(function()
		self:FlushInspireCD()
 	end, 1)
end

function DuckRaceFightView:FlushInspireCD()
	if not self.node_list["inspire_cd"] or not self.node_list["inspire_btn_mask"] then
		return
	end
	if DuckRaceWGData.Instance:GetInspireCD() >= 0 then
		local cd = DuckRaceWGData.Instance:GetInspireCD()
		self.node_list["inspire_cd"].text.text = cd - cd % 1
		self.node_list["inspire_btn_mask"]:SetActive(true)
	else
		self:CancelInspireCDTimer()
		self.node_list["inspire_cd"].text.text = ""
		self.node_list["inspire_btn_mask"]:SetActive(false)
	end
end

function DuckRaceFightView:CancelInspireCDTimer()
	if self.inspire_cd_timer then
	 	GlobalTimerQuest:CancelQuest(self.inspire_cd_timer)
	 	self.inspire_cd_timer = nil
	end
end

-- 刷新干扰倒计时
function DuckRaceFightView:AddFlushDisturbCD()
	self:CancelDisturbCDTimer()
	self:FlushDisturbCD()
	self.disturb_cd_timer = GlobalTimerQuest:AddRunQuest(function()
		self:FlushDisturbCD()
 	end, 1)
end

function DuckRaceFightView:FlushDisturbCD()
	if not self.node_list["disturb_btn_mask"] or not self.node_list["disturb_cd"] then
		return
	end
	if DuckRaceWGData.Instance:GetDisturbCD() >= 0 then
		local cd = DuckRaceWGData.Instance:GetDisturbCD()
		self.node_list["disturb_cd"].text.text = cd - cd % 1
		self.node_list["disturb_btn_mask"]:SetActive(true)
	else
		self:CancelDisturbCDTimer()
		self.node_list["disturb_cd"].text.text = ""
		self.node_list["disturb_btn_mask"]:SetActive(false)
	end
end

function DuckRaceFightView:CancelDisturbCDTimer()
	if self.disturb_cd_timer then
	 	GlobalTimerQuest:CancelQuest(self.disturb_cd_timer)
	 	self.disturb_cd_timer = nil
	end
end

-- 比赛开始提示
function DuckRaceFightView:ShowRaceStart()
	self.node_list["race_start"]:SetActive(true)
end

-- 比赛结束提示
function DuckRaceFightView:ShowRaceEnd()
	self.node_list["race_end"]:SetActive(true)
end

-- 点击跟随,打开鸭子选择面板
function DuckRaceFightView:OnClickFollow()
	-- if IsEmptyTable(DuckRaceWGData.Instance:GetSelfBetDuckIndexList()) then
	-- 	TipWGCtrl.Instance:ShowSystemMsg(Language.DuckRace.NoBetFollowTips)
	-- 	return
	-- end
	DuckRaceWGCtrl.Instance:OpenDuckSelectTips(DuckSelectTips.SELECT_TYPE.FOLLOW, function(index)
		DuckRaceWGData.Instance:SetFollowDuckIndex(index)
 	end, self.node_list["follow_btn"].transform.position + Vector3(5, 0, 0), nil, 1)
end

-- 点击下注按钮，打开下注面板
function DuckRaceFightView:OnClickBet()
	ViewManager.Instance:Open(GuideModuleName.DuckRace, TabIndex.duck_race_bet)
end

-- 点击结算按钮，打开结算面板
function DuckRaceFightView:OnClickResults()
	ViewManager.Instance:Open(GuideModuleName.DuckRace, TabIndex.duck_race_results)
end

-- 点击离开
function DuckRaceFightView:OnClickLeave()
	TipWGCtrl.Instance:OpenAlertTips(Language.DuckRace.LeaveTips, function()
		FuBenWGCtrl.Instance:SendLeaveFB()
	end)
end

-- 点击鼓舞
function DuckRaceFightView:OnClickInspire()
	if DuckRaceWGData.Instance:GetSurplusInspireCount() > 0 then
		DuckRaceWGCtrl.Instance:OpenDuckSelectTips(DuckSelectTips.SELECT_TYPE.INSPIRE, function(index)
			DuckRaceWGCtrl.Instance:SendInspireDuck(index)
	 	end, self.node_list["inspire_btn"].transform.position + Vector3(-11.5, 8, 0), UnityEngine.TextAnchor.MiddleRight)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.DuckRace.Reason2)
	end
end

-- 点击干扰
function DuckRaceFightView:OnClickDisturb()
	if DuckRaceWGData.Instance:GetSurplusDisturbCount() > 0 then
		DuckRaceWGCtrl.Instance:OpenDuckSelectTips(DuckSelectTips.SELECT_TYPE.DISTURB, function(index)
			DuckRaceWGCtrl.Instance:SendDisturbDuck(index)
	 	end, self.node_list["disturb_btn"].transform.position + Vector3(-11.5, 8, 0), UnityEngine.TextAnchor.MiddleRight)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.DuckRace.Reason2)
	end
end

-- 点击前往起点
function DuckRaceFightView:OnClickToStartPointBtn()
	local x, y = DuckRaceWGData.Instance:GetRaceStartPoint()
	GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), x, y)
end

-- 点击右下角跟随按钮
function DuckRaceFightView:OnClickFastFollowBtn()
	local round_state = DuckRaceWGData.Instance:GetCurRoundState()
	if round_state == DUCK_RACE_ROUND_STATE.RACING then
		local bet_fastest_duck_index = DuckRaceWGData.Instance:GetSelfBetFastestDuckIndex()
		if bet_fastest_duck_index == -1 then
			bet_fastest_duck_index = 0
		end
		DuckRaceWGData.Instance:SetFollowDuckIndex(bet_fastest_duck_index)
	else
		local x, y = DuckRaceWGData.Instance:GetRaceStartPoint()
		GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), x, y)
	end
end

-- 主界面MenuIcon改变
function DuckRaceFightView:OnMenuIconChange(is_on)
	self.node_list["duck_op_parent"]:SetActive(not is_on)
end

-- logic坐标转ui坐标
function DuckRaceFightView:LogicToUI(logic_x, logic_y)
	local mini_camera = Scene.Instance:GetSceneMiniMapCamera()
	if IsNil(mini_camera) then
		return 0, 0
	end

	if logic_x == nil or logic_y == nil then
		return 0, 0
	end

	if not self.map_width or not self.map_height then
		return 0, 0
	end

	local wx, wy = GameMapHelper.LogicToWorld(logic_x, logic_y)
	local uipos = mini_camera:TransformWorldToUV(Vector3(wx, 0, wy))
	local ui_x, ui_y = self.map_width * uipos.x, self.map_height * uipos.y
	return ui_x, ui_y
end

-- 刷新地图
function DuckRaceFightView:FlushMapPanel()
	if DuckRaceWGData.Instance:GetIsGameOver() then
		return
	end

	self:TryCreateRoleHead() 												-- 创建小地图头像
	self:FlushMapHeadList() 												-- 刷新地图角色头像
end

-- 创建地图头像icon
function DuckRaceFightView:TryCreateRoleHead()
	local duck_progress_list = DuckRaceWGData.Instance:GetDuckProgressList()
	local prefab = self.node_list["map_icon_prefab"].gameObject
	if self.map_role_head_icon_list == nil then
		self.map_role_head_icon_list = {}
	end

	for duck_index = 0, #duck_progress_list do
		if not self.map_role_head_icon_list[duck_index] then
			local object = ResMgr:Instantiate(prefab)
			object.transform:SetParent(self.node_list["map_raw"].transform, false)
			object:SetActive(true)
			local name_table = U3DNodeList(object:GetComponent(typeof(UINameTable)), self)
			local monster_id = DuckRaceWGData.Instance:GetDuckMonsterId(duck_index)
            local bundle, asset = ResPath.GetDuckRaceImg("a2_ya_num_" .. duck_index)
			name_table["icon"].image:LoadSpriteAsync(bundle, asset, function ()
				name_table["icon"]:SetActive(true)
				name_table["icon"].image:SetNativeSize()
			end)

			self.map_role_head_icon_list[duck_index] = {obj = object, monster_id = monster_id}
		end
	end
end

function DuckRaceFightView:FlushMapHeadList()
	local index = DuckRaceWGData.Instance:GetLeaderDuckIndex()
	if self.map_role_head_icon_list then
		for i,v in pairs(self.map_role_head_icon_list) do
			local duck_progress_list = DuckRaceWGData.Instance:GetDuckProgressList()
			local duck = Scene.Instance:GetMonsterByMonsterId(v.monster_id)
			if index == i then
				v.obj.transform:SetAsLastSibling()
			end
			v.obj:SetActive(duck_progress_list[i] ~= nil)
			if duck and duck_progress_list[i] then
				local pos_x, pos_y = duck:GetLogicPos()
				self:FlushMapHeadPos(v.obj, pos_x, pos_y)
			end
		end
	end
end

-- 设置地图头像位置
function DuckRaceFightView:FlushMapHeadPos(obj, x, y)
	if not self.last_pos_dic then
		self.last_pos_dic = {}
	end

	local last_pos = self.last_pos_dic[obj]
	if not last_pos or last_pos.x ~= x or last_pos.y ~= y then
		obj.transform.localScale = Vector3(0.45, 0.45, 0.45)
		local ui_x, ui_y = self:LogicToUI(x, y)
		obj.transform:SetLocalPosition(ui_x, ui_y, 0)
	end

	self.last_pos_dic[obj] = {x = x, y = y}
end