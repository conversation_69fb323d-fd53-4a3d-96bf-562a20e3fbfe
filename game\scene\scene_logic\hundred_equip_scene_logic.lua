--百倍爆装副本
HundredEquipSceneLogic = HundredEquipSceneLogic or BaseClass(CommonFbLogic)

function HundredEquipSceneLogic:__init()
	HundredEquipSceneLogic.Instance = self
end

function HundredEquipSceneLogic:__delete()
end

function HundredEquipSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)

        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		MainuiWGCtrl.Instance:SetFBNameState(true, Scene.Instance:GetSceneName())
		-- MainuiWGCtrl.Instance:SetFBName(Language.HundredEquip.FuBenName1)
		local time_value = HundredEquipWGData.Instance:GetOtherCfgByName("fb_time_s") or 0
		local mainui_time_stamp = time_value + TimeWGCtrl.Instance:GetServerTime()
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(mainui_time_stamp)
		HundredEquipWGCtrl.Instance:OpenHundredLogicView()

		local view = HundredEquipWGCtrl.Instance:GetSceneView()
		ViewManager.Instance:AddMainUIFuPingChangeList(view)
		ViewManager.Instance:AddMainUIRightTopChangeList(view)

		HundredEquipWGCtrl.Instance:SendHundredEquipRequest(HUNDREDFOLD_DROP_OPERATE_TYPE.CS_E_HUNDREDFOLD_DROP_CLIENT_OPERATE_TYPE_RANK_INFO)
	end)

	if not self.close_loading_view_event then
		self.close_loading_view_event = GlobalEventSystem:Bind(SceneEventType.CLOSE_LOADING_VIEW, BindTool.Bind(self.CloseLoadingCallBack, self))
	end
end

function HundredEquipSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self, old_scene_type, new_scene_type)
	HundredEquipWGCtrl.Instance:CloseHundredLogicView()
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTaskContents(true)
    MainuiWGCtrl.Instance:SetOtherContents(false)
    GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)

	local view = HundredEquipWGCtrl.Instance:GetSceneView()
	ViewManager.Instance:RemoveMainUIFuPingChangeList(view)
	ViewManager.Instance:RemoveMainUIRightTopChangeList(view)

	if self.close_loading_view_event then
		GlobalEventSystem:UnBind(self.close_loading_view_event)
		self.close_loading_view_event = nil
	end
end

function HundredEquipSceneLogic:CloseLoadingCallBack()
    Scene.Instance:SendFBLoadedScene()
end
