ChatLocation = ChatLocation or BaseClass(SafeBaseView)

function ChatLocation:__init()
	self:AddViewResource(0, "uis/view/chat_ui_prefab", "layout_location")
	self.location_cfg = Config.Location
	self.use_real_location = 0
	self.menu_select_type = 1
	self.province_index = 1
	self.city_index = 1
	self.is_need_sync_to_server = false
	self.select_memu_callback = BindTool.Bind1(self.SelectLocationIndexCallBack, self)
	self.notify_location_callback = BindTool.Bind1(self.OnNotifyLocationChangeCallBack, self)
end

function ChatLocation:__delete()
end

function ChatLocation:ReleaseCallBack()
end

function ChatLocation:CloseCallBack()
	if self.is_need_sync_to_server then
		if 1 == self.use_real_location then
			Location.Instance:UseRealLoaction()
		else
			local province_cfg = self.location_cfg[self.province_index]
			if nil ~= province_cfg and nil ~= province_cfg.city[self.city_index] then
				Location.Instance:UseSelectLocation(province_cfg.city[self.city_index])
			end
		end
	end
end

function ChatLocation:LoadCallBack()
	self:CalcLocationInfo()
	self.use_real_location = 0
	Location.Instance:NotifyCityNameChange(self.notify_location_callback)
	self.node_list["layout_btn_check_box"].button:AddClickListener(BindTool.Bind(self.ClickCheckBoxCallBack, self))
	self.node_list["layout_btn_city_1"].button:AddClickListener(BindTool.Bind2(self.OnOpenCitySelectMenu, self, 1))
	self.node_list["layout_btn_city_2"].button:AddClickListener(BindTool.Bind2(self.OnOpenCitySelectMenu, self, 2))
	-- self.node_list["btn_close_window"].button:AddClickListener(BindTool.Bind(self.CloseWindow, self))
	self:Flush()
end

function ChatLocation:CloseWindow() 
	self:Close()
end

function ChatLocation:ClickCheckBoxCallBack()
	--1->0
	self.use_real_location = 1 == self.use_real_location and 0 or 1

	--使用实际地址
	if 1 == self.use_real_location then
		Location.Instance:UseRealLoaction()
	end

	self.is_need_sync_to_server = true
	self.node_list["img_hook_0"]:SetActive(self.use_real_location == 1)

	self:Flush()
end

function ChatLocation:OnOpenCitySelectMenu(menu_select_type)
	if 1 == self.use_real_location then
		return
	end

	self.menu_select_type = menu_select_type

	local data_list = {}
	if 1 == self.menu_select_type then
		--self.node_list["layout_city_list"].rect.anchoredPosition = Vector2(20,293)
		for _, v in pairs(self.location_cfg) do
			table.insert(data_list, v.province)
		end
	else
		--self.node_list["layout_city_list"].rect.anchoredPosition = Vector2(20,-353)
		local province_cfg = self.location_cfg[self.province_index]
		if nil ~= province_cfg then
			data_list = province_cfg.city
		end
	end

	local default_index = 1 == self.menu_select_type and self.province_index or self.city_index
	local pos_flag = 1 == self.menu_select_type and 1 or 2
	ChatWGCtrl.Instance:OpenCitySceleMenu(data_list, default_index, self.select_memu_callback, pos_flag)
end

function ChatLocation:SelectLocationIndexCallBack(menu_index)
	local province_index = 1 
	local city_index = 1

	if self.menu_select_type == 1 then
		province_index = menu_index
		city_index = 1
	else
		province_index = self.province_index
		city_index = menu_index
	end

	local cfg = self.location_cfg[province_index]
	if nil == cfg or nil == cfg.city[city_index] then
		return
	end

	self.node_list["lbl_province"].text.text  = cfg.province
	self.node_list["lbl_city"].text.text  = cfg.city[city_index]

	ChatWGData.Instance:SetLocation(cfg.province, cfg.city[city_index])
	ChatWGData.Instance:SetLocationIndex(province_index, city_index)
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	main_role_vo.city_name = cfg.city[city_index]
	self.province_index = province_index
	self.city_index = city_index
	self.is_need_sync_to_server = true

end

function ChatLocation:OnNotifyLocationChangeCallBack()
	self:CalcLocationInfo()

	self:Flush()
end

function ChatLocation:CalcLocationInfo()
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	self.use_real_location = main_role_vo.is_real_location
	_, self.province_index, self.city_index = self:GetProvinceNameByCityName(main_role_vo.city_name)
end

function ChatLocation:OnFlush()
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()

	local is_show_real_location = (self.use_real_location == 1)

	
	local province_name = self:GetProvinceNameByCityName(main_role_vo.city_name)
	if "" ~= province_name then
		self.node_list["lbl_province"].text.text = (province_name)
		self.node_list["lbl_city"].text.text = (main_role_vo.city_name)
	end
	
end

function ChatLocation:GetProvinceNameByCityName(check_city_name)
	for p_index, v in ipairs(self.location_cfg) do
		local city_list = v.city
		for c_index, city_name in ipairs(city_list) do
			if city_name == check_city_name then
				return v.province, p_index, c_index
			end			
		end
	end

	return "", 1, 1
end