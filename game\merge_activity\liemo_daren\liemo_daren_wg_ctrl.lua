require("game/merge_activity/liemo_daren/liemo_daren_wg_data")

LieMoDaRenWGCtrl = LieMoDaRenWGCtrl or BaseClass(BaseWGCtrl)

function LieMoDaRenWGCtrl:__init()
	if LieMoDaRenWGCtrl.Instance ~= nil then
		ErrorLog("[LieMoDaRenWGCtrl] Attemp to create a singleton twice !")
	end
    LieMoDaRenWGCtrl.Instance = self

    self.data = LieMoDaRenWGData.New()

    self:RegisterAllProtocols()

    --self:BindGlobalEvent(RankEventType.PersonRankInfoChange, BindTool.Bind(self.OnPersonRankChange, self))
    self:BindGlobalEvent(RankEventType.GuildRankInfoChange, BindTool.Bind(self.OnGuildRankChange, self))
    self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.<PERSON>ui<PERSON>pen<PERSON>reate, self))
end

function LieMoDaRenWGCtrl:__delete()
    self.data:DeleteMe()
    self.data = nil

    LieMoDaRenWGCtrl.Instance = nil
end

function LieMoDaRenWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCCombineServerBossHunterInfo, "OnSCCombineServerBossHunterInfo")     --猎魔达人信息
    self:RegisterProtocol(SCCombineServerBossHunterRankList, "OnSCCombineServerBossHunterRankList") --猎魔个人排行信息
end

--猎魔达人信息
function LieMoDaRenWGCtrl:OnSCCombineServerBossHunterInfo(protocol)
    --print_error("猎魔达人个人信息", protocol)

    local view = ViewManager.Instance:GetView(GuideModuleName.MergeActivityView)
    if view and view:IsOpen() and view:GetShowIndex() == TabIndex.merge_activity_2119 then
        local old_info = self.data:GetLieMoInfo()
        if old_info then
            if old_info.score ~= protocol.score then
                RankWGCtrl.Instance:SendRankReq(RankKind.Person, PersonRankType.PERSON_RANK_TYPE_BOSS_HUNTER)     -- 請求个人排行榜信息
            end
            if old_info.guild_score ~= protocol.guild_score then
                RankWGCtrl.Instance:SendRankReq(RankKind.Guild, GuildRankType.GUILD_RANK_TYPE_CSA_BOSS_HUNTER)    -- 請求仙盟排行榜信息
            end
        end
    end

    self.data:SetLieMoInfo(protocol)
    MergeActivityWGData.Instance:GetActivityIsEvent(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_BOSS_HUNTER)
    self:FlushView()
    RemindManager.Instance:Fire(RemindName.MergeActivity_LieMoDaRen)
end

--猎魔个人排行信息
function LieMoDaRenWGCtrl:OnSCCombineServerBossHunterRankList(protocol)
    --print_error("猎魔个人排行信息", protocol)
    self.data:SetPersonRankData(protocol)
    self:FlushView()
end

local function LieMoDaRenOperate(opera_type, param_1, param_2)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_BOSS_HUNTER
	protocol.opera_type = opera_type or 0
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol:EncodeAndSend()
end

--领取奖励
function LieMoDaRenWGCtrl:SendFetchReward(seq)
    --print_error("领取猎魔达人奖励", seq)
    LieMoDaRenOperate(PERSON_LIEMO_DAREN_OPERATE_TYPE.FETCH_REWARD, seq)
end

--请求个人排行信息
function LieMoDaRenWGCtrl:ReqPersonRank()
    LieMoDaRenOperate(PERSON_LIEMO_DAREN_OPERATE_TYPE.REQ_RANK)
end

function LieMoDaRenWGCtrl:MainuiOpenCreate()
    --print_error("请求猎魔达人信息")
    LieMoDaRenOperate(PERSON_LIEMO_DAREN_OPERATE_TYPE.INFO)
end

--请求排行信息
function LieMoDaRenWGCtrl:RequestRankInfo()
    --RankWGCtrl.Instance:SendRankReq(RankKind.Person, PersonRankType.PERSON_RANK_TYPE_BOSS_HUNTER)     -- 請求个人排行榜信息
    self:ReqPersonRank()   -- 請求个人排行榜信息
    RankWGCtrl.Instance:SendRankReq(RankKind.Guild, GuildRankType.GUILD_RANK_TYPE_CSA_BOSS_HUNTER)    -- 請求仙盟排行榜信息
end

----个人排行数据改变
--function LieMoDaRenWGCtrl:OnPersonRankChange(protocol)
--    if protocol.rank_type ~= PersonRankType.PERSON_RANK_TYPE_BOSS_HUNTER then
--        return
--    end
--    --print_error("个人排行数据改变   ", protocol)
--    self.data:SetRankData(protocol)
--    self:FlushView()
--end

--仙盟排行数据改变
function LieMoDaRenWGCtrl:OnGuildRankChange(protocol)
    if protocol.rank_type ~= GuildRankType.GUILD_RANK_TYPE_CSA_BOSS_HUNTER then
        return
    end
    --print_error("仙盟排行数据改变", protocol)
    self.data:SetRankData(protocol)
    self:FlushView()
end

function LieMoDaRenWGCtrl:FlushView()
    ViewManager.Instance:FlushView(GuideModuleName.MergeActivityView, TabIndex.merge_activity_2119)
end