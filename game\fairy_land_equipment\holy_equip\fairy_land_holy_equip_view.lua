function FairyLandEquipmentView:InitHolyEquipView()
    if not self.he_bag_grid then
		self.he_bag_grid = AsyncBaseGrid.New()
		self.he_bag_grid:CreateCells({col = 4, cell_count = GOD_BODY_ENUM.BAG_GRID_NUM,
                        list_view = self.node_list.he_bag_grid, itemRender = HolyEquipBagCell,
                        assetBundle = "uis/view/fairy_land_equipment_ui_prefab", assetName = "holy_equip_bag_cell",})
        self.he_bag_grid:SetStartZeroIndex(false)
		self.he_bag_grid:SetSelectCallBack(BindTool.Bind(self.SelectHEBagCallBack, self))
	end

    self.is_cleanup_cd = false
    self.node_list.btn_bag_clearup_text.text.text = Language.FairyLandEquipment.OneKeyCleanUp

    XUI.AddClickEventListener(self.node_list.btn_bag_clearup, BindTool.Bind(self.ClearUpHolyEquipBag, self))
    XUI.AddClickEventListener(self.node_list.btn_gather_equip, BindTool.Bind(self.GoToGatherEquip, self))
    XUI.AddClickEventListener(self.node_list.btn_reslove, BindTool.Bind(self.OnClickReslove, self))
end

function FairyLandEquipmentView:DeleteHolyEquipView()
    if  self.he_bag_grid ~= nil then
        self.he_bag_grid:DeleteMe()
        self.he_bag_grid = nil
    end

	self:RemoveCleanUpCD()
end

-- 点击背包格子回调
function FairyLandEquipmentView:SelectHEBagCallBack(cell)
    if cell == nil or cell.data == nil then
        return
    end

    local data = cell.data
    if data.item_id == nil or data.item_id <= 0 then
        return
    end

    -- print_error("背包格子回调", data)
    local slot = data.slot
    local part = data.part
    local fle_data = FairyLandEquipmentWGData.Instance
    local part_is_wear = fle_data:GetHolyEquipPartIsWear(slot, part)
    local form_view = part_is_wear and ItemTip.FROM_HOLY_EQUIP_CONTRAST or ItemTip.FROM_HOLY_EQUIP_BAG
    local btn_callback_event = {}
    local put_on_fun = function ()
        local limit_color = FairyLandEquipmentWGData.Instance:GetEvolveUpColorLimit()
        if part_is_wear then
            local part_info = fle_data:GetHolyEquipWearPartInfo(slot, part)
            if part_info and part_info.color >= limit_color and data.color < limit_color then
                local color_limit = FairyLandEquipmentWGData.Instance:GetEvolveUpColorLimit()
                local color_name = Language.Common.ColorName[color_limit]
                local str = string.format(Language.FairyLandEquipment.EvolvePutOnEquipTips,color_name)
                TipWGCtrl.Instance:OpenAlertTips(str,function ()
                    -- self:Flush(TabIndex.fairy_land_eq_holy_equip,"to_ui_name",slot)
                    -- ViewManager.Instance:Open(GuideModuleName.FairyLandEquipmentView,
                    --                 TabIndex.fairy_land_eq_holy_equip,nil,{to_ui_name = data.slot})
                    FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.PUT_ON, data.slot, data.index)
                end)
                return
            end
        end
        -- ViewManager.Instance:Open(GuideModuleName.FairyLandEquipmentView,
        --                             TabIndex.fairy_land_eq_holy_equip,nil,{to_ui_name = data.slot})
        FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.PUT_ON, data.slot, data.index)
    end
    btn_callback_event[1] = {btn_text = Language.Tip.ButtonLabel[1], callback = put_on_fun}
    TipWGCtrl.Instance:OpenItem(data, form_view, nil, nil, btn_callback_event)
end

-- 刷新背包
function FairyLandEquipmentView:FlushHolyEquipBagPanel()
    local fle_data = FairyLandEquipmentWGData.Instance
    local bag_list = fle_data:GetHolyEquipBagListBySlot(self.select_slot_index)
    -- self.he_bag_grid:CancleAllSelectCell()
    self.he_bag_grid:SetDataList(bag_list)
end

function FairyLandEquipmentView:RemoveCleanUpCD()
    if CountDown.Instance:HasCountDown(self.clean_cd_quest) then
        CountDown.Instance:RemoveCountDown(self.clean_cd_quest)
        self.clean_cd_quest = nil
    end
end

function FairyLandEquipmentView:DoCleanUpCD(elapse_time, total_time)
    self.is_cleanup_cd = true
    self.node_list.btn_bag_clearup_text.text.text = total_time - math.modf(elapse_time)
end

-- 背包整理
function FairyLandEquipmentView:ClearUpHolyEquipBag()
    if self.is_cleanup_cd then
        return
    end

    local bag_list = FairyLandEquipmentWGData.Instance:GetHolyEquipBagListBySlot(self.select_slot_index)
    if #bag_list <= 0 then
        TipWGCtrl.Instance:ShowSystemMsg(Language.FairyLandEquipment.NoEquipTip)
        return
    end

    self:RemoveCleanUpCD()
    local total_time = 2
    self.node_list.btn_bag_clearup_text.text.text = total_time
    XUI.SetButtonEnabled(self.node_list.btn_bag_clearup, false)
    self.clean_cd_quest = CountDown.Instance:AddCountDown(total_time, 1,
                            BindTool.Bind(self.DoCleanUpCD, self),
                            function()
                                self.is_cleanup_cd = false
                                if self.node_list.btn_bag_clearup_text then
                                    self.node_list.btn_bag_clearup_text.text.text = Language.FairyLandEquipment.OneKeyCleanUp
                                end
                                XUI.SetButtonEnabled(self.node_list.btn_bag_clearup, true)
                            end)

    FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.CLEARUP_BAG, self.select_slot_index)
    TipWGCtrl.Instance:ShowSystemMsg(Language.FairyLandEquipment.OneKeyEquipTip)
end

function FairyLandEquipmentView:GoToGatherEquip()
    ViewManager.Instance:Open(GuideModuleName.WorldServer, FunName.XianJieBoss)
end

function FairyLandEquipmentView:OnClickReslove()
    local info = {
        bag_type = KNAPSACK_TYPE.XIANJIE_EQUIP_BAG,
        param_1 = self.select_slot_index,
    }
    RoleBagWGCtrl.Instance:OpenSpecialBagResloveView(info)
end
