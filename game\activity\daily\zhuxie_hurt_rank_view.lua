--boss死亡后排行奖励展示
Zhu<PERSON>ieHurtRankView = ZhuXieHurtRankView or BaseClass(SafeBaseView)
local CLOSE_TIME = 10
function ZhuXieHurtRankView:__init()
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/zhuxie_ui_prefab", "show_rank_view")
end

function ZhuXieHurtRankView:__delete()
end

function ZhuXieHurtRankView:LoadCallBack()
	self.hurt_rank_list = AsyncListView.New(ZhuXieShowHurtRankCell,self.node_list.rank_list_view)
	self.reward_list = {}
	self.node_list.close_btn.button:AddClickListener(BindTool.Bind(self.OnClickCloseBtn,self))
	self.node_list.queren_btn.button:AddClickListener(BindTool.Bind(self.OnClickCloseBtn,self))
end

-- 切换标签调用
function ZhuXieHurtRankView:ShowIndexCallBack()
	self:Flush()
end

function ZhuXieHurtRankView:ReleaseCallBack()
	if self.hurt_rank_list then
		self.hurt_rank_list:DeleteMe()
		self.hurt_rank_list = nil
	end

	if self.reward_list then
		for k,v in pairs(self.reward_list) do
			v:DeleteMe()
		end
		self.reward_list = nil
	end
	if CountDownManager.Instance:HasCountDown("show_hurt_rank") then
		CountDownManager.Instance:RemoveCountDown("show_hurt_rank")
	end
end

function ZhuXieHurtRankView:OnFlush()
	local rank_list = {}
	local role_name
	local rank_num, rank_info
	local server_id = GameVoManager.Instance:GetMainRoleVo().server_id
	local boss_info

	if IS_ON_CROSSSERVER then
		rank_list = ActivityWGData.Instance:GetKFZhuXieHurtRankList()
	 	role_name = RoleWGData.Instance:GetRoleVo().role_name
	 	role_name = string.format("%s_s%s",role_name,server_id)
	    rank_num, rank_info = ActivityWGData.Instance:GetKFZhuXieHurtRankInfoByName(role_name)
	    boss_info = ActivityWGData.Instance:GetKFZhuXieTaskInfo()
	else
		rank_list = ActivityWGData.Instance:GetHurtRankList()
	 	role_name = RoleWGData.Instance:GetRoleVo().role_name
	    rank_num, rank_info = ActivityWGData.Instance:GetHurtRankInfoByName(role_name)
	    boss_info = ActivityWGData.Instance:GetOneZhuXieBossInfo()
	end

    if not rank_num or not rank_info then
    	self.node_list.rank_num:SetActive(false)
    	self.node_list.rank_img:SetActive(false)
    	self.node_list.hurt_num.text.text = ""
    	self.node_list.role_name.text.text = Language.GuildAnswer.NoRank1
    else
    	self.node_list.rank_num:SetActive(rank_num > 3)
    	self.node_list.rank_img:SetActive(rank_num <= 3)
    	if rank_num <= 3 then
    		self.node_list.rank_img.image:LoadSprite(ResPath.GetCommonImages("icon_paiming" ..rank_num))
    	end
    	self.node_list.role_name.text.text = rank_info.role_name
    	self.node_list.hurt_num.text.text = CommonDataManager.ConverExpByThousand(rank_info.hurt)

    	for k,v in pairs(rank_info.item) do
    		self.reward_list[k] = ItemCell.New(self.node_list.content)
    		self.reward_list[k]:SetData(v)
    	end
    end

	if not IsEmptyTable(rank_list) and self.hurt_rank_list then
		self.hurt_rank_list:SetDataList(rank_list,3)
	end

	if CountDownManager.Instance:HasCountDown("show_hurt_rank") then
		CountDownManager.Instance:RemoveCountDown("show_hurt_rank")
	end
	self:ChangeTime(0,CLOSE_TIME)
	CountDownManager.Instance:AddCountDown("show_hurt_rank", BindTool.Bind1(self.ChangeTime, self), BindTool.Bind1(self.CompleteTime, self), nil, CLOSE_TIME, 1)
end

function ZhuXieHurtRankView:ChangeTime(elapse_time,total_time)
	local time = math.floor(total_time - elapse_time)
	if time > 0 then
		self.node_list.desc.text.text = string.format(Language.ZhuXie.Desc1,time)
	end
end

function ZhuXieHurtRankView:CompleteTime()
	self:Close()
end

-- 关闭前调用
function ZhuXieHurtRankView:CloseCallBack()
	-- override
end

function ZhuXieHurtRankView:OnClickCloseBtn()
	self:Close()
end

--------------------------------------------------ZhuXieShowHurtRankCell-----------------------------------------------------
ZhuXieShowHurtRankCell = ZhuXieShowHurtRankCell or BaseClass(BaseRender)

function ZhuXieShowHurtRankCell:__init()
end

function ZhuXieShowHurtRankCell:__delete()
	if self.reward_list then
		for k,v in pairs(self.reward_list) do
			v:DeleteMe()
		end
		self.reward_list = nil
	end
end

function ZhuXieShowHurtRankCell:OnFlush()
	if not self.data then return end

	self.node_list.rank_num:SetActive(self.index > 3)
	self.node_list.rank_img:SetActive(self.index <= 3)
	if self.index <= 3 then
		self.node_list.rank_img.image:LoadSprite(ResPath.GetCommonImages("icon_paiming" ..self.index))
	end
	self.node_list.rank_num.text.text = self.index
	self.node_list.role_name.text.text = self.data.role_name or self.data.name
	self.node_list.hurt_num.text.text = CommonDataManager.ConverExpByThousand(self.data.hurt)
	if not self.reward_list then
		self.reward_list = {}
	    for k,v in pairs(self.data.item) do
			self.reward_list[k] = ItemCell.New(self.node_list.content)
			self.reward_list[k]:SetData(v)
		end
	end
end
