OperationActPrefab = OperationActPrefab or BaseClass(OperationActRenderBase)
function OperationActPrefab:__delete()
    if self.show_head_cell then
        self.show_head_cell:DeleteMe()
        self.show_head_cell = nil
    end

    if self.show_bubble_cell then
        self.show_bubble_cell:Destroy()
        self.show_bubble_cell = nil
	end

	if self.spine_loader then
        self.spine_loader:Destroy()
        self.spine_loader = nil
	end

    if self.show_tujian_cell then
        self.show_tujian_cell:DeleteMe()
        self.show_tujian_cell = nil
    end

	if self.mingwen_item_cell then
        self.mingwen_item_cell:DeleteMe()
        self.mingwen_item_cell = nil
    end

	if self.item_cell_list ~= nil then
		for k,v in pairs(self.item_cell_list) do
			v:DeleteMe()
		end
		self.item_cell_list = nil
	end
end

function OperationActPrefab:Reset()
	if self.show_bubble_cell then
		self.show_bubble_cell:<PERSON>troy()
	end
	if self.spine_loader then
		self.spine_loader:Destroy()
	end
	self.node_list.head_root:SetActive(false)
	self.node_list.title_root:SetActive(false)
	self.node_list.bubble_root:SetActive(false)
    self.node_list.tujian_root:SetActive(false)
	if self.node_list.mingwen_item_root then
		self.node_list.mingwen_item_root:SetActive(false)
	end
	if self.node_list.spine_cell_root then
		self.node_list.spine_cell_root:SetActive(false)
	end
	self.node_list.special_root:SetActive(false)
    self.node_list["prefab_root"]:SetActive(false)
	self.node_list["prefab_effect_root"]:SetActive(false)
	for i = 0, 3 do
		if self.node_list["item_cell_" .. i] ~= nil then
			self.node_list["item_cell_" .. i]:SetActive(false)
		end
	end
end

--[[配置数据：
    ----[[预制体
        --有物品id的(称号、相框、气泡)
        item_id = 0,

        --没有物品id的prefab
        bundle_name = "",
        asset_name = "",

        render_type = OARenderType.Prefab,
        should_ani = false,
		model_click_func
		reward_show
    ----]]
--]]
function OperationActPrefab:Show()
	self.node_list["prefab_root"]:SetActive(true)
	if self.data.item_id and self.data.item_id ~= 0 then
		self:ShoWCommonModel()
	elseif self.data.bundle_name and self.data.bundle_name ~= "" and self.data.asset_name and self.data.asset_name ~= "" then
		self:ShoWSpecialModel()
	end

	self.node_list.click_btn:SetActive(self.data.model_click_func ~= nil)
	if self.data.model_click_func then
        self.node_list["click_btn"].event_trigger_listener:AddPointerClickListener(BindTool.Bind(self.OnClickPrefab, self))
    end
	self:FlushItemList()
end

function OperationActPrefab:ShoWCommonModel()
	self.node_list["prefab_root"]:SetActive(true)
	if not self.data.item_id or self.data.item_id == 0 then
		return
	end
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if item_cfg == nil then
		print_error("OperationActRender","ShowModel() item_cfg is a nil value: item_id = ", self.data.item_id)
		return
	end

    local tujian_cfg = ShanHaiJingWGData.Instance:GetTJResolveShowCfg(self.data.item_id)  --  图鉴
    if tujian_cfg then
        self:ShowTuJian(self.data.item_id)
        return
    end

	local display_type = item_cfg.is_display_role
	if nil == display_type or item_cfg.is_display_role == 0 or item_cfg.is_display_role == "" then
		return
	end

	if display_type == DisplayItemTip.Display_type.CHENGHAO then 				--称号
		self:ShowTitle(item_cfg)
	elseif display_type == DisplayItemTip.Display_type.BUBBLE then 				--气泡
		self:ShowBubble(item_cfg)
	elseif display_type == DisplayItemTip.Display_type.PHOTOFRAME then			--相框
		self:ShowPhotoFrame(item_cfg)
	elseif display_type == DisplayItemTip.Display_type.MINGQI then				--双修
		local res_id = ArtifactWGData.Instance:GetArtifactCfgByItemId(self.data.item_id).model_id
		local bundle, asset = ResPath.GetShuangXiuTipUI(res_id)
		self:SetSpineCell(bundle, asset)
	elseif display_type == DisplayItemTip.Display_type.MINGWEN then				--万魂幡
		self:ShowMingWenItem(item_cfg)
	end
end

function OperationActPrefab:ShoWSpecialModel()
	self.node_list["special_root"]:ChangeAsset(self.data.bundle_name, self.data.asset_name, false)
	self.node_list.special_root:SetActive(true)
end

function OperationActPrefab:OnClickPrefab()
	if self.data.model_click_func then
        self.data.model_click_func()
    end
end

function OperationActPrefab:SetSpineCell(bundle, asset)
	self.node_list.spine_cell_root:SetActive(true)

	if not self.spine_loader then
		local spine_loader = AllocAsyncLoader(self, "base_tip_spine_cell")
		spine_loader:SetIsUseObjPool(true)
		spine_loader:SetParent(self.node_list["spine_cell_root"].transform)
		self.spine_loader = spine_loader
	end

	self.spine_loader:Load(bundle, asset)

	if self.data.should_ani ~= nil and type(self.data.should_ani) == "boolean" then
		self.node_list["prefab_animator_node"].animator.enabled = self.data.should_ani
	end
end

function OperationActPrefab:ShowMingWenItem(item_cfg)
	if not self.mingwen_item_cell then
		self.mingwen_item_cell = MingWenItemRender.New(self.node_list["mingwen_item_root"])
	end

	self.node_list.mingwen_item_root:SetActive(true)
	self.mingwen_item_cell:SetIsShowItemTip(false)
	self.mingwen_item_cell:SetData({item_id = self.data.item_id})

	if self.data.should_ani ~= nil and type(self.data.should_ani) == "boolean" then
		self.node_list["prefab_animator_node"].animator.enabled = self.data.should_ani
	end
end

function OperationActPrefab:ShowPhotoFrame(item_cfg)
	if not self.show_head_cell then
		self.show_head_cell = BaseHeadCell.New(self.node_list["head_root"])
	end
	self.node_list.head_root:SetActive(true)

	local photoframe_id = item_cfg.param2
	local cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(SHIZHUANG_TYPE.PHOTOFRAME, photoframe_id)
	photoframe_id = cfg and cfg.resouce or photoframe_id
	local data = {fashion_photoframe = photoframe_id, set_bg_status = false}
	self.show_head_cell:SetImgBg(true)
	self.show_head_cell:SetData(data)

	if self.data.should_ani ~= nil and type(self.data.should_ani) == "boolean" then
		self.node_list["prefab_animator_node"].animator.enabled = self.data.should_ani
	end
end

function OperationActPrefab:ShowTuJian(item_id)
    if not self.show_tujian_cell then
		self.show_tujian_cell = BaseTuJianCell.New(self.node_list["tujian_root"])
	end

    self.node_list.tujian_root:SetActive(true)
    self.show_tujian_cell:SetData({item_id = item_id})
    if self.data.should_ani ~= nil and type(self.data.should_ani) == "boolean" then
        self.node_list["prefab_animator_node"].animator.enabled = self.data.should_ani
    end
end

function OperationActPrefab:ShowBubble(item_cfg)
	if not IsNil(self.node_list["bubble_root"].transform) then
		if not self.show_bubble_cell then
			self.show_bubble_cell = AllocAsyncLoader(self, "operation_act_new_bubble_cell")
			self.show_bubble_cell:SetIsUseObjPool(true)
			self.show_bubble_cell:SetParent(self.node_list["bubble_root"].transform)
		end
		self.node_list.bubble_root:SetActive(true)

		local bubble_id = item_cfg.param2
		local cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(SHIZHUANG_TYPE.BUBBLE, bubble_id)
		bubble_id = cfg and cfg.resouce or bubble_id
		local asset, bundle = ResPath.ChatBigBubbleBig(bubble_id)
		self.show_bubble_cell:Load(asset, bundle)

		if self.data.should_ani ~= nil and type(self.data.should_ani) == "boolean" then
			self.node_list["prefab_animator_node"].animator.enabled = self.data.should_ani
		end
	end
end

function OperationActPrefab:ShowTitle(item_cfg)
	local title_id = item_cfg.param1
	self.attr_cfg = TitleWGData.Instance.GetTitleConfig(title_id)
	local b,a = ResPath.GetTitleModel(title_id)
	self.node_list["title_root"]:ChangeAsset(b, a, false)
	self.node_list.title_root:SetActive(true)

	if self.data.should_ani ~= nil and type(self.data.should_ani) == "boolean" then
		self.node_list["prefab_animator_node"].animator.enabled = self.data.should_ani
	end
end

function OperationActPrefab:FlushItemList()
	for i = 0, 3 do
		self.node_list["item_cell_" .. i]:SetActive(false)
	end
	if self.data.reward_show and not IsEmptyTable(self.data.reward_show) then
		for i = 0, #self.data.reward_show do
			if self.node_list["item_cell_" .. i] then
				if self.item_cell_list == nil then
					self.item_cell_list = {}
				end

				if self.item_cell_list[i] == nil then
					self.item_cell_list[i] = ItemCell.New(self.node_list["item_cell_" .. i])
				end
				self.node_list["item_cell_" .. i]:SetActive(true)
				self.item_cell_list[i]:SetData(self.data.reward_show[i])
				if self.data.reward_show_pos[i] then
					self.node_list["item_cell_" .. i].rect.anchoredPosition = self.data.reward_show_pos[i]
				end
			else
				break
			end
		end
	end
end
