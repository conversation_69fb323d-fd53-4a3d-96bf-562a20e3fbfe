CrossConsumeView = CrossConsumeView or BaseClass(SafeBaseView)

function CrossConsumeView:__init()
    self:SetMaskBg()
    self.view_layer = UiLayer.Normal

    self:AddViewResource(0, "uis/view/cross_consume_ui_prefab", "layout_cross_consume_rank")
end

function CrossConsumeView:OpenCallBack()
    --请求协议
    CrossConsumeRankWGCtrl.Instance:SendCrossConsumeReq(CROSS_CONSUME_RANK_OPERATE_TYPE.RANK_INFO)
end

function CrossConsumeView:ReleaseCallBack()
    if self.consume_reward_list then
        self.consume_reward_list:DeleteMe()
        self.consume_reward_list = nil
    end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end

    if CountDownManager.Instance:HasCountDown("cross_consume_rank_down") then
        CountDownManager.Instance:RemoveCountDown("cross_consume_rank_down")
    end
end

function CrossConsumeView:LoadCallBack()
    --查看排行榜按钮
    self.node_list["btn_rank"].button:AddClickListener(BindTool.Bind(self.OnClickRankView, self))
    --退出按钮
    self.node_list["btn_close"].button:AddClickListener(BindTool.Bind(self.Close, self))
    --奖励列表
    self.consume_reward_list = AsyncListView.New(ConsumeRewardRender, self.node_list["day_list"])
    --活动时间文本的显示
    self.node_list["time_text"].text.text = Language.CorssConsume.ConsumeActTxt
    --consume_title_desc 消费说明显示
    -- self.node_list["cross_consume_title"].text.text = Language.CorssConsume.ConsumeRuleContent1
    -- self.node_list["consume_title_desc"].text.text = Language.CorssConsume.ConsumeRuleContent


    self:LoginTimeCountDown()
end

--计时器
function CrossConsumeView:LoginTimeCountDown()
    local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE
    .CROSS_CHANNEL_ACTIVITY_TYPE_CONSUME_RANK)
    if activity_data ~= nil then
        --结束时间错
        local invalid_time = activity_data.end_time
        --记录时间戳
        if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
            self.node_list["amount_down_time"].text.text = TimeUtil.FormatSecondDHM2(invalid_time -
            TimeWGCtrl.Instance:GetServerTime())
            CountDownManager.Instance:AddCountDown("cross_consume_rank_down", BindTool.Bind1(self.UpdateCountDown, self),
                BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
        end
    end
end

function CrossConsumeView:UpdateCountDown(elapse_time, total_time)
    local valid_time = total_time - elapse_time
    if valid_time > 0 then
        self.node_list["amount_down_time"].text.text = TimeUtil.FormatSecondDHM2(valid_time)
    end
end

function CrossConsumeView:OnComplete()
    self.node_list["amount_down_time"].text.text = TimeUtil.FormatSecondDHM2(0)
    self:Close()
end

function CrossConsumeView:OnFlush(param_t)
    self:FlushViewShow()
    for k, v in pairs(param_t) do
        if k == "rank_list" then
            self:LoadModel()
        end
    end
end

function CrossConsumeView:FlushViewShow()
    --获取自己的上榜情况
    local my_rank_data = CrossConsumeRankWGData.Instance:GetMyRankData()
    --获取到自己的累计消费灵玉
    local my_rank_value = CrossConsumeRankWGData.Instance:GetSelfRankValue()
    --拿到当前轮次
    local grade = CrossConsumeRankWGData.Instance:GetConsumeGrade()
    --拿到配置表信息
    local recharged_rank_list = CrossConsumeRankWGData.Instance:GetConsumeRankCfg(grade)
    if recharged_rank_list then
        self.consume_reward_list:SetDataList(recharged_rank_list)
    end

    --更新排名
    if my_rank_data > 0 then
        self.node_list["my_rank"].text.text = string.format(Language.CorssConsume.ConsumeRanking, my_rank_data)
    else
        self.node_list["my_rank"].text.text = Language.CorssConsume.ConsumeWeiShangBang
    end

    --更新累计金额
    self.node_list["my_total"].text.text = my_rank_value
end

function CrossConsumeView:OnClickRankView()
    CrossConsumeRankWGCtrl.Instance:OpenConsumeRankView()
end

--模型展示的方法
function CrossConsumeView:LoadModel()
    --获取到榜一的信息
    local model_data = CrossConsumeRankWGData.Instance:GetTopRankInfo()
    if model_data[1] == nil then
        return
    end
    if model_data[1].no_true_rank then
        --self.node_list["name"].text.text = Language.CorssConsume.XuWeiYiDai
        self.node_list["wu_menber"]:SetActive(true)
        self.node_list["norank_name"]:SetActive(true)
        self.node_list["display"]:SetActive(false)
    else
        self.node_list["wu_menber"]:SetActive(false)
        self.node_list["display"]:SetActive(true)
        self.node_list["norank_name"]:SetActive(false)

        local flush_fun = function(protocol)
            if not self.node_list then
                return
            end
            if nil == self.model_display then
                self.model_display = RoleModel.New()
                local display_data = {
                    parent_node = self.node_list["display"],
                    camera_type = MODEL_CAMERA_TYPE.BASE,
                    -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
                    rt_scale_type = ModelRTSCaleType.M,
                    can_drag = true,
                }
        
                self.model_display:SetRenderTexUI3DModel(display_data)
                -- self.model_display:SetUI3DModel(self.node_list["display"].transform,
                --     self.node_list["display"].event_trigger_listener,
                --     1, false, MODEL_CAMERA_TYPE.BASE)
            end

            if self.model_display then
                local ignore_table = { ignore_wing = true, ignore_jianzhen = true, ignore_halo = true,
                    ignore_shouhuan = true, ignore_tail = true, ignore_waist = true }
                self.model_display:SetModelResInfo(protocol, ignore_table)
            end

            local name_data = Split(model_data[1].rank_data.name, "_")
            local user_name = "[" .. name_data[2] .. "]" .. name_data[1]
            self.node_list["name"].text.text = user_name
        end

        BrowseWGCtrl.Instance:BrowRoelInfo(model_data[1].rank_data.uuid.temp_low, flush_fun)
    end
end

----------列表格子
ConsumeRewardRender = ConsumeRewardRender or BaseClass(BaseRender)

function ConsumeRewardRender:LoadCallBack()
    self.item_cell_list = {}
end

function ConsumeRewardRender:__delete()
    for _, v in pairs(self.item_cell_list) do
        v:DeleteMe()
    end
    self.item_cell_list = nil
end

function ConsumeRewardRender:OnFlush()
    if self.data == nil then
        return
    end
    -- print_error("self.data", self.data)

    local desc = ""
    if self.data.min_rank == 1 then
        desc = string.format(Language.CorssConsume.ConsumeRanking, self.data.min_rank)
    else
        desc = string.format(Language.CorssConsume.ConsumeRankTitle2, self.data.min_rank, self.data.max_rank)
    end

    self.node_list["rank_title"].text.text = string.format(Language.CorssConsume.ConsumeRankTitle1, desc,
        self.data.reach_value)

    local item_list = self.item_cell_list
    if #self.data.reward_item > #item_list then
        local cell_parent = self.node_list["reward_group"]
        for i = 0, #self.data.reward_item do
            item_list[i] = item_list[i] or ItemCell.New(cell_parent)
        end
        self.item_list = item_list
    end

    for i = 0, #item_list do
        if self.data.reward_item[i] then
            item_list[i]:SetData(self.data.reward_item[i])
            item_list[i]:SetActive(true)
        else
            item_list[i]:SetActive(false)
        end
    end
end
