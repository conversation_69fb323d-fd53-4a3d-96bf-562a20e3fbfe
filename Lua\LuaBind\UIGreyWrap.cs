﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class UIGreyWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UIGrey), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("GetIsGrey", GetIsGrey);
		<PERSON><PERSON>unction("SetIsGrey", SetIsGrey);
		L<PERSON>Function("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetIsGrey(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UIGrey obj = (UIGrey)ToLua.CheckObject<UIGrey>(L, 1);
			bool o = obj.GetIsGrey();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIsGrey(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UIGrey obj = (UIGrey)ToLua.CheckObject<UIGrey>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			UnityEngine.Material arg1 = (UnityEngine.Material)ToLua.CheckObject<UnityEngine.Material>(L, 3);
			obj.SetIsGrey(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

