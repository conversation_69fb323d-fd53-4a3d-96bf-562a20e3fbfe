﻿//------------------------------------------------------------------------------
// This file is part of MistLand project in Nirvana.
// Copyright © 2016-2016 Nirvana Technology Co., Ltd.
// All Right Reserved.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using Nirvana;
using UnityEngine;

public class UIPrefabLoaderAsync : UIPrefabLoader
{
    [SerializeField]
    [AssetType(typeof(GameObject))]
    private AssetID prefab;

    private GameObject instance;

    private Action<GameObject> waitLoad;

    public AssetID Prefab
    {
        get { return prefab; }
        set { prefab = value; }
    }

    /// <summary>
    /// Wait the instance.
    /// </summary>
    public override void Wait(Action<GameObject> wait)
    {
        if (this.instance == null)
        {
            this.waitLoad = wait;
        }
        else
        {
            wait(this.instance);
        }
    }

    private void OnEnable()
    {
        if (this.instance == null)
        {
            this.StartCoroutine(this.CreateInstance());
            
        }
    }

    private IEnumerator CreateInstance()
    {
        var wait = AssetManager.LoadObject(this.prefab, typeof(GameObject));
        yield return wait;

        var prefab = wait.GetObject<GameObject>();
        this.instance = GameObject.Instantiate(prefab, this.transform);
        if (this.waitLoad != null)
        {
            this.waitLoad(this.instance);
            this.waitLoad = null;
        }
    }
}
