--宝物概率
ActivitySecretAllRewardView = ActivitySecretAllRewardView or BaseClass(SafeBaseView)

function ActivitySecretAllRewardView:__init()
    self.view_layer = UiLayer.Normal
    self.view_name = "ActivitSecretAllRewardView"

    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(702, 458)})
    self:AddViewResource(0, "uis/view/activity_dragon_secret_ui_prefab", "reward_ranking_probability")
    self:SetMaskBg(true, true)

end

function ActivitySecretAllRewardView:ReleaseCallBack()
    if self.dragon_probability_list then
        self.dragon_probability_list:DeleteMe()
        self.dragon_probability_list = nil
    end

    if self.dragon_round_list then
        self.dragon_round_list:DeleteMe()
        self.dragon_round_list = nil
    end

    self.below_select_index = nil

end

function ActivitySecretAllRewardView:LoadCallBack()
    self.node_list["title_view_name"].text.text = Language.DragonSecret.DragonAllAward

    --轮次列表
    if not self.dragon_round_list  then
        self.dragon_round_list = AsyncListView.New(DragonRoundRender, self.node_list["rounds_label_list"])
        self.dragon_round_list:SetSelectCallBack(BindTool.Bind(self.RoundListCallBack, self))
       -- self.dragon_round_list:SetStartZeroIndex(true)
    end

    --物品列表
    if not self.dragon_probability_list  then
        self.dragon_probability_list = AsyncListView.New(DragonProbabilityRender, self.node_list["ph_pro_list"])
    end

    --退出按钮
    self.node_list["btn_close_window"].button:AddClickListener(BindTool.Bind(self.Close, self))
end

function ActivitySecretAllRewardView:ShowIndexCallBack(index)
     self.need_select_new = true   -- 是否需要重新选择
     self.below_select_index = -1
end

function ActivitySecretAllRewardView:OnFlush()  
    --通过配置表拿到标签数据
    local tags_data_list = ActivityDragonSecretWGData.Instance:GetRoundTagsList()
    local round = ActivityDragonSecretWGData.Instance:GetActDraRound()
    self.dragon_round_list:SetDataList(tags_data_list)

    if  self.need_select_new then
        local dafult_index = 1
        self.need_select_new = nil
        for k, v in ipairs(tags_data_list) do
            if v.seq == round then
                dafult_index = k
             --   print_error("v.seq ", v.seq,"k",k,"dafult_index",dafult_index )
                break
            end
        end

        self.dragon_round_list:JumpToIndex(dafult_index)  --列表跳转
        return
    end

    self:FlushBelowPanel()
end

function ActivitySecretAllRewardView:RoundListCallBack(cell)
    if not cell or not cell:GetData() then
        return
    end

    local index = cell:GetIndex()
    if index == self.below_select_index then
        return
    end

    self.dragon_select_data = cell:GetData()
    self.below_select_index = index

    --刷新下方数据信息
    self:FlushBelowPanel()
end

function ActivitySecretAllRewardView:FlushBelowPanel()
    local data = self.dragon_select_data
    if data == nil then
        return
    end
    
    --拿到档位
    local grade = ActivityDragonSecretWGData.Instance:GetActDraGrade()
    
    --根据点击的标签,拿到轮次信息
    local data_list = ActivityDragonSecretWGData.Instance:GetCurrentList(grade, data.seq)

    self.dragon_probability_list:SetDataList(data_list)

end







-----物品信息列表格子
DragonProbabilityRender = DragonProbabilityRender or BaseClass(BaseRender)

function DragonProbabilityRender:__delete()
end

function DragonProbabilityRender:LoadCallBack()
end
function DragonProbabilityRender:OnFlush()
    if self.data == nil then
        return
    end
    --获取到颜色
    local color = ItemWGData.Instance:GetItemColor(self.data.item.item_id)
    local item_name = ItemWGData.Instance:GetItemName(self.data.item.item_id) or ""

    self.node_list["index_text"].text.text = self.data.seq
    self.node_list["name_text"].text.text = ToColorStr(item_name, color)
    --概率显示
    local probability = string.format("%.1f", self.data.probability * 100)
    self.node_list["probability_text"].text.text = probability .."%"
end



---轮次信息列表
DragonRoundRender = DragonRoundRender or BaseClass(BaseRender)

function DragonRoundRender:__delete()
end

function DragonRoundRender:OnFlush()
    if self.data == nil then
        return
    end
    self.node_list["not_selete_txt"].text.text = string.format(Language.DragonSecret.DragonRound, self.data.seq + 1)
    self.node_list["selete_txt"].text.text = string.format(Language.DragonSecret.DragonRound, self.data.seq + 1)
end

function DragonRoundRender:OnSelectChange(is_select)
    self.node_list["not_selete_image"]:CustomSetActive(not is_select)
    self.node_list["selete_image"]:CustomSetActive(is_select)
end