---------------------------------------幻兽回退 start--------------------------------------
CSBeastBackListReq = CSBeastBackListReq or BaseClass(BaseProtocolStruct)
function CSBeastBackListReq:__init()
	self.msg_type = 16600
end

function CSBeastBackListReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteShort(self.list_len)

	for i = 1, self.list_len do
		if self.operate_list[i] then
			MsgAdapter.WriteShort(self.operate_list[i])
		end
	end
end
---------------------------------------幻兽回退 end--------------------------------------

--------------------------------任务召唤---------------
SCTaskCallInfo = SCTaskCallInfo or BaseClass(BaseProtocolStruct)
function SCTaskCallInfo:__init()
	self.msg_type = 16601
end

function SCTaskCallInfo:Decode()
	self.task_id = MsgAdapter.ReadInt()
	self.skill_list = {}
	for i = 1, 5 do
		local data = {}
		data.skill_id = MsgAdapter.ReadInt()
		data.last_perform_time = MsgAdapter.ReadLL()
		table.insert(self.skill_list, data)
	end
end

--------------------------------打怪QTE---------------
SCMonsterQTEInfo = SCMonsterQTEInfo or BaseClass(BaseProtocolStruct)
function SCMonsterQTEInfo:__init()
	self.msg_type = 16602
end

function SCMonsterQTEInfo:Decode()
	self.qte_uuid = MsgAdapter.ReadUUID()
	self.qte_start_time = MsgAdapter.ReadUInt()
	self.qte_end_time = MsgAdapter.ReadUInt()
	self.qte_type = MsgAdapter.ReadInt()
end

--------------------------新节日活动特惠商店Start-------------------------
SCOATuhuiShopInfo = SCOATuhuiShopInfo or BaseClass(BaseProtocolStruct)
function SCOATuhuiShopInfo:__init()
    self.msg_type = 16603
end

function SCOATuhuiShopInfo:Decode()
	self.grade = MsgAdapter.ReadUInt()
	self.purchase_shop_cost_times_list = {}
    for i = 0, 19 do
        self.purchase_shop_cost_times_list[i] = MsgAdapter.ReadUChar()
    end
	self.xianyu_shop_cost_times_list = {}
	for i = 0, 19 do
        self.xianyu_shop_cost_times_list[i] = MsgAdapter.ReadUChar()
    end
end
--------------------------新节日活动特惠商店End-------------------------

---------------------新节日活动 登录有礼start---------------------
SCJieRiDengLuDrawInfo = SCJieRiDengLuDrawInfo or BaseClass(BaseProtocolStruct)
function SCJieRiDengLuDrawInfo:__init()
    self.msg_type = 16604
end

function SCJieRiDengLuDrawInfo:Decode()
	self.cur_grade = MsgAdapter.ReadInt()
	self.can_draw_num = MsgAdapter.ReadInt()
	self.draw_num_list = {}
	for i = 1 , 24 do
		self.draw_num_list[i] = MsgAdapter.ReadUChar()
	end
end
---------------------新节日活动 登录有礼end---------------------

---------------------------------新节日活动-节日盛典Start---------------------------------
SCNewFestivalSDInfo = SCNewFestivalSDInfo or BaseClass(BaseProtocolStruct)
function SCNewFestivalSDInfo:__init()
	self.msg_type = 16605
end

function SCNewFestivalSDInfo:Decode()
	self.grade = MsgAdapter.ReadInt()							-- 档位
	self.is_skip_comic = MsgAdapter.ReadInt()					-- 跳过动画？
	self.leiji_reward_fetch_flag = MsgAdapter.ReadUInt()		-- 累计奖励领取标志
	self.leiji_point = MsgAdapter.ReadInt()						-- 累计积分值，一直加不会变
	self.cur_point = MsgAdapter.ReadInt()						-- 当前积分值
end

SCNewFestivalSDRewardInfo = SCNewFestivalSDRewardInfo or BaseClass(BaseProtocolStruct)			-- 奖品信息
function SCNewFestivalSDRewardInfo:__init()
    self.msg_type = 16606
end

function SCNewFestivalSDRewardInfo:Decode()
	self.big_reward_id = MsgAdapter.ReadInt()
	self.count = MsgAdapter.ReadInt()
	self.reward_list = {}
	for i = 1, self.count do
		self.reward_list[i] = {}
		self.reward_list[i].item_id = MsgAdapter.ReadItemId()
		self.reward_list[i].is_bind = MsgAdapter.ReadChar()
		self.reward_list[i].revers = MsgAdapter.ReadChar()
		self.reward_list[i].num = MsgAdapter.ReadInt()
	end
end
------------------------------------新节日活动-节日盛典End--------------------------------------

--------------------------新节日祈愿Start-------------------------------------
SCNewFestivalActPrayerAllInfo = SCNewFestivalActPrayerAllInfo or BaseClass(BaseProtocolStruct)
function SCNewFestivalActPrayerAllInfo:__init() -- 全部信息
    self.msg_type = 16607
end

function SCNewFestivalActPrayerAllInfo:Decode()
	self.prayer_count = MsgAdapter.ReadInt()
	self.advanced_prayer_flag = MsgAdapter.ReadInt()
	self.prayer_buy_limit = {}
	for i = 0, 9 do
		self.prayer_buy_limit[i] = MsgAdapter.ReadInt()
	end

	self.reward_flag = {}
	for i = 0, 255 do
		self.reward_flag[i] = MsgAdapter.ReadInt()
	end

	self.prayer_task_process = {}
	for i = 0, 63 do
		self.prayer_task_process[i] = MsgAdapter.ReadInt()
	end

	self.pray_task_flag = bit:ll2b_two(MsgAdapter.ReadUInt(), MsgAdapter.ReadUInt())
	self.grade = MsgAdapter.ReadInt()
end

SCNewFestivalActPrayerInfo = SCNewFestivalActPrayerInfo or BaseClass(BaseProtocolStruct)
function SCNewFestivalActPrayerInfo:__init() -- 祈愿信息
    self.msg_type = 16608
end

function SCNewFestivalActPrayerInfo:Decode()
	self.prayer_count = MsgAdapter.ReadInt()
	self.advanced_prayer_flag = MsgAdapter.ReadInt()
	self.prayer_buy_limit = {}
	for i = 0, 9 do
		self.prayer_buy_limit[i] = MsgAdapter.ReadInt()
	end

	self.reward_flag = {}
	for i = 0, 255 do
		self.reward_flag[i] = MsgAdapter.ReadInt()
	end
end

SCNewFestivalActTaskUpdate = SCNewFestivalActTaskUpdate or BaseClass(BaseProtocolStruct)
function SCNewFestivalActTaskUpdate:__init() -- 任务信息
    self.msg_type = 16609
end

function SCNewFestivalActTaskUpdate:Decode()
	self.pray_task_flag = bit:ll2b_two(MsgAdapter.ReadUInt(), MsgAdapter.ReadUInt())
	local data = {}
    data.one_task_process = MsgAdapter.ReadInt()
	data.task_id = MsgAdapter.ReadInt()
    self.change_data = data
end

--------------------------新节日祈愿End--------------------------------------

--------------------------新节日活动-消费返利Start-------------------------
SCOACounsumeRebateInfo = SCOACounsumeRebateInfo or BaseClass(BaseProtocolStruct)
function SCOACounsumeRebateInfo:__init()
    self.msg_type = 16610
end

function SCOACounsumeRebateInfo:Decode()
	self.rebate_reward = MsgAdapter.ReadInt()
end
--------------------------新节日活动-消费返利End-------------------------

-------------------------新节日活动-节日集结Start-------------------------
-- 全部信息
SCNAItemCollectAllInfo = SCNAItemCollectAllInfo or BaseClass(BaseProtocolStruct)
function SCNAItemCollectAllInfo:__init()
    self.msg_type = 16611
end

function SCNAItemCollectAllInfo:Decode()
	self.personal_item_count = MsgAdapter.ReadInt()					-- 个人奖励道具数量
	self.jieyi_item_count = MsgAdapter.ReadInt()					-- 结义奖励道具数量
	self.personal_reward_flag = MsgAdapter.ReadLL()					-- 个人奖励标记
	self.jieyi_reward_flag = MsgAdapter.ReadLL()					-- 结义奖励标记
end

-- 道具数量信息
SCNAItemCollectCountInfo = SCNAItemCollectCountInfo or BaseClass(BaseProtocolStruct)
function SCNAItemCollectCountInfo:__init()
    self.msg_type = 16612
end

function SCNAItemCollectCountInfo:Decode()
	self.personal_item_count = MsgAdapter.ReadInt()
	self.jieyi_item_count = MsgAdapter.ReadInt()
end
--------------------------新节日活动-节日收集End---------------------------------------------------新节日活动特惠商店End-------------------------


--------------------------新节日活动-集福活动Start-------------------------
-- 返回个人信息
SCOACollectCardSelfInfo = SCOACollectCardSelfInfo or BaseClass(BaseProtocolStruct)
function SCOACollectCardSelfInfo:__init()
    self.msg_type = 16613
end

function SCOACollectCardSelfInfo:Decode()
	self.grade = MsgAdapter.ReadInt()				-- 当前档位
	self.gain_times = MsgAdapter.ReadInt()			-- 当前向别人索要的次数
	self.be_times = MsgAdapter.ReadInt()			-- 当前被赠送次数
	self.times = MsgAdapter.ReadInt()				-- 当前主动赠送次数
	self.redemption_num = MsgAdapter.ReadInt()		-- 领取奖励次数
	self.request_list = {}							-- 别人的请求列表
	for i = 1, 30 do
		self.request_list[i] = {
			uuid = MsgAdapter.ReadInt(),
			item_id = MsgAdapter.ReadInt(),
			name = MsgAdapter.ReadName(),
		}
	end
end

-- 返回朋友列表信息
SCOACollectCardFriendInfo = SCOACollectCardFriendInfo or BaseClass(BaseProtocolStruct)
function SCOACollectCardFriendInfo:__init()
    self.msg_type = 16614
end

function SCOACollectCardFriendInfo:Decode()
	local count = MsgAdapter.ReadInt()
	self.friend_list = {}							-- 朋友列表
	for index = 1, count do
		self.friend_list[index] = {}
		self.friend_list[index].uuid = MsgAdapter.ReadInt()
		self.friend_list[index].name = MsgAdapter.ReadName()
		self.friend_list[index].be_time = MsgAdapter.ReadInt()
		self.friend_list[index].flag_list = {}
		for i = 1, 5 do
			local item_id = MsgAdapter.ReadInt()
			local flag = MsgAdapter.ReadInt()
			if item_id > 0 then
				self.friend_list[index].flag_list[item_id] = flag
			end
		end
	end
end

-- 更新单个朋友信息
SCOACollectCardUpdateInfo = SCOACollectCardUpdateInfo or BaseClass(BaseProtocolStruct)
function SCOACollectCardUpdateInfo:__init()
    self.msg_type = 16615
end

function SCOACollectCardUpdateInfo:Decode()
	self.update_uuid = MsgAdapter.ReadInt()
	self.update_name = MsgAdapter.ReadName()
	self.update_be_time = MsgAdapter.ReadInt()
	self.update_request_item_id = MsgAdapter.ReadInt()
	self.update_flag = MsgAdapter.ReadInt()
end
--------------------------新节日活动-集福活动End-------------------------

---------------------------新节日活动累充start---------------------------
SCNewFestivalActRecharge = SCNewFestivalActRecharge or BaseClass(BaseProtocolStruct)
function SCNewFestivalActRecharge:__init()
	self.msg_type = 16616
end

function SCNewFestivalActRecharge:Decode()
	self.grade = MsgAdapter.ReadInt()
	self.recharge_sum = MsgAdapter.ReadLL()	--这是累计充值数
	self.fetch_flag = MsgAdapter.ReadUInt()	--0是未领取/不可领取，1是已领取
end
---------------------------活动累充end---------------------------

--------------------------新节日活动-节日掉落Start-------------------------
SCOAMoreDroppingInfo = SCOAMoreDroppingInfo or BaseClass(BaseProtocolStruct)
function SCOAMoreDroppingInfo:__init()
    self.msg_type = 16617
end

function SCOAMoreDroppingInfo:Decode()
	self.drop_count = MsgAdapter.ReadInt()
end
--------------------------新节日活动-节日掉落End-------------------------

--------------------------新节日活动-节日达人Start-------------------------
SCCrossNewFestivalRankInfo = SCCrossNewFestivalRankInfo or BaseClass(BaseProtocolStruct)
local function GetNewFesInfoItem()
	local data = {}
	data.uuid = MsgAdapter.ReadUUID()
	data.name = MsgAdapter.ReadName()
	data.rank_value = MsgAdapter.ReadInt()
	data.rank = MsgAdapter.ReadInt()
	return data
end

function SCCrossNewFestivalRankInfo:__init()
	self.msg_type = 16618
end

function SCCrossNewFestivalRankInfo:Decode()
	self.rank_value = MsgAdapter.ReadInt()
	self.grade = MsgAdapter.ReadInt()
	self.count = MsgAdapter.ReadInt()
	local rank_list = {}
	for i = 1, self.count do
		local data = GetNewFesInfoItem()
		rank_list[data.rank] = data
	end

	self.rank_item_list = rank_list
end
--------------------------新节日活动-节日达人End-------------------------

--------------------------服务器跨服开服时间Start-------------------------
SCCrossActivityCalcOpenTime = SCCrossActivityCalcOpenTime or BaseClass(BaseProtocolStruct)
function SCCrossActivityCalcOpenTime:__init()
    self.msg_type = 16619
end

function SCCrossActivityCalcOpenTime:Decode()
	self.time = MsgAdapter.ReadUInt()
end
--------------------------服务器跨服时间End-------------------------



-------------------------------------------天梯争霸 开始--------------------------------------
--竞技场1v1操作.
CSCrossChallengeFieldOperate = CSCrossChallengeFieldOperate or BaseClass(BaseProtocolStruct)
function CSCrossChallengeFieldOperate:__init()
	self.msg_type = 16620
end

function CSCrossChallengeFieldOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	if type(self.param1) == "number" then
		MsgAdapter.WriteLL(self.param1)
	else
		MsgAdapter.WriteUUID(self.param1)
	end
	
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
	MsgAdapter.WriteInt(self.param4)
end

--竞技场1v1排行.
SCCrossChallengeRankListInfo = SCCrossChallengeRankListInfo or BaseClass(BaseProtocolStruct)
function SCCrossChallengeRankListInfo:__init()
	self.msg_type = 16621
end

function SCCrossChallengeRankListInfo:Decode()
	self.daily_like_count = MsgAdapter.ReadInt()        --每日点赞数量
	self.grade = MsgAdapter.ReadInt()                   --当前结算奖励阶段
	self.challenger_info = {}
	for i = 1, 101 do
		local data = {}
		data.uuid = MsgAdapter.ReadUUID()					--角色uuid.
		data.rank_pos = MsgAdapter.ReadInt()			--排名，0是第一名.
		data.have_like_count = MsgAdapter.ReadInt()		--被点赞数.
		data.capatility = MsgAdapter.ReadLL()			--战力.
		data.role_name = MsgAdapter.ReadName()			--角色名称.
		data.score = MsgAdapter.ReadUShort()            --积分
		data.is_like = MsgAdapter.ReadChar()            --是否点赞
		data.is_robot = MsgAdapter.ReadChar()             --是否机器人
		data.server_id = MsgAdapter.ReadLL()            -- 服务器id
		data.sex = MsgAdapter.ReadChar()
		MsgAdapter.ReadUChar()
		data.fashion_photoframe = MsgAdapter.ReadShort()				-- 头像
		data.prof = MsgAdapter.ReadInt()

		table.insert(self.challenger_info, data)		--排行榜信息，第11个是玩家自己.
	end
end

--竞技场1v1奖励信息.
SCCrossChallengeFieldRewardInfo = SCCrossChallengeFieldRewardInfo or BaseClass(BaseProtocolStruct)
function SCCrossChallengeFieldRewardInfo:__init()
	self.msg_type = 16622
end

function SCCrossChallengeFieldRewardInfo:Decode()
	self.count_reward_flag = MsgAdapter.ReadInt()		--次数奖励标记 bit.
	self.challenge_end_time = MsgAdapter.ReadUInt()     --赛季结束时间
end

--场景用户信息
SCCrossChallengeFieldStatus =  SCCrossChallengeFieldStatus or BaseClass(BaseProtocolStruct)
function SCCrossChallengeFieldStatus:__init()
	self.msg_type = 16623
	self.scene_user_list = {}
	self.status = 0
	self.next_time = 0
end

function SCCrossChallengeFieldStatus:Decode()
	self.scene_user_list = {}
	self.status = MsgAdapter.ReadInt()
	self.next_time = MsgAdapter.ReadUInt()
	for i=1,2 do
		local obj = {}
		obj.role_id = MsgAdapter.ReadInt()
		obj.obj_id = MsgAdapter.ReadUShort()
		obj.level = MsgAdapter.ReadShort()
		obj.name = MsgAdapter.ReadStrN(32)

		obj.camp = MsgAdapter.ReadChar()
		MsgAdapter.ReadUChar()
		obj.avatar = MsgAdapter.ReadChar()
		obj.sex = MsgAdapter.ReadChar()

		obj.hp = MsgAdapter.ReadLL()
		obj.max_hp = MsgAdapter.ReadLL()
		obj.mp = MsgAdapter.ReadLL()
		obj.max_mp = MsgAdapter.ReadLL()
		obj.speed = MsgAdapter.ReadLL()

		obj.pos_x = MsgAdapter.ReadShort()
		obj.pos_y = MsgAdapter.ReadShort()

		obj.dir = MsgAdapter.ReadFloat()
		obj.distance = MsgAdapter.ReadFloat()

		obj.capability = MsgAdapter.ReadLL()

		obj.guild_id = MsgAdapter.ReadInt()
		obj.guild_name = MsgAdapter.ReadStrN(32)
		obj.guild_post = MsgAdapter.ReadChar()
		MsgAdapter.ReadChar()
		MsgAdapter.ReadShort()
		obj.prof = MsgAdapter.ReadInt()
		table.insert(self.scene_user_list, obj)
	end
end

--获取玩家详细信息
CSCrossChallengeFieldGetOpponentInfo =  CSCrossChallengeFieldGetOpponentInfo or BaseClass(BaseProtocolStruct)
function CSCrossChallengeFieldGetOpponentInfo:__init()
	self.msg_type = 16624
	self.type = 0
end

function CSCrossChallengeFieldGetOpponentInfo:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteShort(self.type)
	MsgAdapter.WriteShort(0)
end

--玩家详细信息
SCCrossChallengeFieldOpponentInfo =  SCCrossChallengeFieldOpponentInfo or BaseClass(BaseProtocolStruct)
function SCCrossChallengeFieldOpponentInfo:__init()
	self.msg_type = 16625
end

function SCCrossChallengeFieldOpponentInfo:Decode()
	local function GetRoleVO()
		local role_vo = GameVoManager.Instance:CreateVo(RoleVo)
		role_vo.role_id = MsgAdapter.ReadUUID() --改成了uuid
		role_vo.level = MsgAdapter.ReadInt()
		role_vo.camp = MsgAdapter.ReadChar()
		MsgAdapter.ReadUChar()
		role_vo.sex = MsgAdapter.ReadChar()
		local is_robot = MsgAdapter.ReadChar()
		role_vo.name = MsgAdapter.ReadStrN(32)
		role_vo.capability = MsgAdapter.ReadLL()
		role_vo.appearance = ProtocolStruct.ReadRoleAppearance()
		local role_diy_appearance = ProtocolStruct.ReadRoleDiyAppearance()
		role_vo.prof = MsgAdapter.ReadInt()
		if is_robot == 0 then
			role_vo.role_diy_appearance = role_diy_appearance
		else
			local default_diy_data = RoleDiyAppearanceWGData.Instance:GetDefaultPresetDiyCfgBySexAndProf(role_vo.sex, role_vo.prof)
			if default_diy_data then
				role_vo.role_diy_appearance = default_diy_data
			end
		end

		return role_vo
	end

	local role_count = MsgAdapter.ReadInt()
	for i = 1, role_count do
		local role_vo = GetRoleVO()
		ArenaTianTiWGData.Instance:AddRoleInfo(role_vo)
	end
end

--获得用户信息和战报
CSCrossChallengeFieldGetUserInfo =  CSCrossChallengeFieldGetUserInfo or BaseClass(BaseProtocolStruct)
function CSCrossChallengeFieldGetUserInfo:__init()
	self.msg_type = 16626
end

function CSCrossChallengeFieldGetUserInfo:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end

--刷新挑战列表
CSCrossChallengeFieldResetOpponentList =  CSCrossChallengeFieldResetOpponentList or BaseClass(BaseProtocolStruct)
function CSCrossChallengeFieldResetOpponentList:__init()
	self.msg_type = 16627
end

function CSCrossChallengeFieldResetOpponentList:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end

--挑战请求
CSCrossChallengeFieldFightReq =  CSCrossChallengeFieldFightReq or BaseClass(BaseProtocolStruct)
function CSCrossChallengeFieldFightReq:__init()
	self.msg_type = 16628

	self.opponent_index = 0
	self.ignore_rank_pos = 0

	self.is_skip = 0
	self.rank_pos = 0
	self.opponent_uuid = 0
end

function CSCrossChallengeFieldFightReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)

	MsgAdapter.WriteShort(self.opponent_index)
	MsgAdapter.WriteChar(self.ignore_rank_pos)
	MsgAdapter.WriteChar(self.is_skip)
	MsgAdapter.WriteInt(self.rank_pos)
	MsgAdapter.WriteUUID(self.opponent_uuid)
end

--竞技场跳过战斗
CSCrossChallengeFieldSkipFighting = CSCrossChallengeFieldSkipFighting or BaseClass(BaseProtocolStruct)
function CSCrossChallengeFieldSkipFighting:__init()
	self.msg_type = 16629
end
function CSCrossChallengeFieldSkipFighting:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end

--挑战列表信息和个人信息
SCCrossChallengeFieldUserInfo =  SCCrossChallengeFieldUserInfo or BaseClass(BaseProtocolStruct)
function SCCrossChallengeFieldUserInfo:__init()
	self.msg_type = 16630
end

function SCCrossChallengeFieldUserInfo:Decode()
	self.user_info = {}

	self.user_info.rank_pos = MsgAdapter.ReadInt()
	self.user_info.rank = self.user_info.rank_pos + 1
	self.user_info.curr_opponent_idx = MsgAdapter.ReadInt()
	self.user_info.join_times = MsgAdapter.ReadInt()
	self.user_info.next_free_time = MsgAdapter.ReadUInt() --下一次恢复次数的时间戳
	--self.user_info.jifen = MsgAdapter.ReadInt()
	self.user_info.new_score = MsgAdapter.ReadUInt() --新积分
	self.user_info.old_score = MsgAdapter.ReadUInt() --旧积分
	--local flag = MsgAdapter.ReadInt()

	-- self.user_info.jifen_reward_flag = {}
	-- for i = 0, 31 do
	-- 	self.user_info.jifen_reward_flag[i] = (bit:_and(flag, bit:_lshift(1, i))) == 0
	-- end

	self.user_info.shengwang = MsgAdapter.ReadLL()
	self.user_info.reward_coin = MsgAdapter.ReadLL()
	self.user_info.reward_get_flag = MsgAdapter.ReadInt()

	self.user_info.liansheng = MsgAdapter.ReadInt()
	--local best_rank_pos = MsgAdapter.ReadInt() --改成了历史最高积分
	self.user_info.best_score = MsgAdapter.ReadUInt()  --历史最高积分
	self.user_info.challenge_cd_timer = MsgAdapter.ReadUInt()
	self.user_info.is_cd_timer_limit = MsgAdapter.ReadInt()
	self.user_info.break_fetch_reward_flag = MsgAdapter.ReadInt()
	local old_rankpos = MsgAdapter.ReadInt()
	self.user_info.old_rankpos = old_rankpos + 1
	--self.user_info.best_rank_pos = best_rank_pos + 1
	self.user_info.rank_list = {}
	self.user_info.top_rank_list = {}
	self.user_info.ten_oclock_level = MsgAdapter.ReadShort()
	self.user_info.is_up_level = MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()

	local function GetRoleVO(role_id)
		local role_vo = GameVoManager.Instance:CreateVo(RoleVo)
		role_vo.role_id = role_id--MsgAdapter.ReadUUID() --改成了uuid
		role_vo.level = MsgAdapter.ReadInt()
		role_vo.camp = MsgAdapter.ReadChar()
		MsgAdapter.ReadUChar()
		role_vo.sex = MsgAdapter.ReadChar()
		local is_robot = MsgAdapter.ReadChar()
		role_vo.name = MsgAdapter.ReadStrN(32)
		role_vo.capability = MsgAdapter.ReadLL()
		role_vo.appearance = ProtocolStruct.ReadRoleAppearance()
		local role_diy_appearance = ProtocolStruct.ReadRoleDiyAppearance()
		role_vo.prof = MsgAdapter.ReadInt()
		if is_robot == 0 then
			role_vo.role_diy_appearance = role_diy_appearance
		else
			local default_diy_data = RoleDiyAppearanceWGData.Instance:GetDefaultPresetDiyCfgBySexAndProf(role_vo.sex, role_vo.prof)
			if default_diy_data then
				role_vo.role_diy_appearance = default_diy_data
			end
		end

		return role_vo
	end

	for i = 1, 4 do
		local data = {}
		data.user_id = MsgAdapter.ReadUUID()  --uuid
		data.rank_pos = MsgAdapter.ReadInt()
		data.score = MsgAdapter.ReadUShort()
		data.win_score = MsgAdapter.ReadUShort()        --获胜积分
		data.server_id = MsgAdapter.ReadLL()
		--data.capatility = MsgAdapter.ReadLL()			--战力.
		data.index = i - 1 						-- 真实索引与服务端对应
		data.rank = data.rank_pos + 1 			-- 界面显示排名用这个
		self.user_info.rank_list[i] = data

		local role_vo = GetRoleVO(data.user_id)
		ArenaTianTiWGData.Instance:AddRoleInfo(role_vo)
	end
	table.sort(self.user_info.rank_list, function (a, b)
		return a.score > b.score
	end)

	local top_data = {}
	top_data.user_id = MsgAdapter.ReadUUID()  --uuid
	top_data.rank = MsgAdapter.ReadInt() + 1
	top_data.score = MsgAdapter.ReadUShort()
	top_data.win_score = MsgAdapter.ReadUShort()        --获胜积分
	top_data.server_id = MsgAdapter.ReadLL()
	--top_data.capatility = MsgAdapter.ReadLL()			--战力.
	table.insert(self.user_info.top_rank_list, top_data)
	local top_role_vo = GetRoleVO(top_data.user_id)
	--ArenaTianTiWGData.Instance:AddRoleInfo(top_role_vo)

	self.user_info.battle_count = MsgAdapter.ReadInt()			--赛季战斗次数.
	self.user_info.daily_battle_count = MsgAdapter.ReadInt()		--每天的战斗次数.
end

--反击信息返回
SCCrossChallengeFieldFightBackInfo = SCCrossChallengeFieldFightBackInfo or BaseClass(BaseProtocolStruct)

function SCCrossChallengeFieldFightBackInfo:__init()
	self.msg_type = 16631
end

function SCCrossChallengeFieldFightBackInfo:Decode()
	self.target_uuid = MsgAdapter.ReadUUID()							-- 对方id
	self.target_name = MsgAdapter.ReadStrN(32)						-- 对方名称
	self.sex = MsgAdapter.ReadChar()
	MsgAdapter.ReadUChar()
	self.fashion_photoframe = MsgAdapter.ReadShort()				-- 头像
	self.opponent_score = MsgAdapter.ReadUInt()                   -- 对手积分
	self.opponent_capability = MsgAdapter.ReadLL()				    -- 对手战力
	self.server_id = MsgAdapter.ReadLL()            			    -- 服务器id
	self.prof = MsgAdapter.ReadInt()
end


--战报
SCCrossChallengeFieldReportInfo =  SCCrossChallengeFieldReportInfo or BaseClass(BaseProtocolStruct)
function SCCrossChallengeFieldReportInfo:__init()
	self.msg_type = 16632
	self.report_info = {}
end

function SCCrossChallengeFieldReportInfo:Decode()
	self.report_info = {}
	local report_count = MsgAdapter.ReadInt()
	for i=1,report_count do
		local data = {}
		data.challenge_time = MsgAdapter.ReadUInt()
		data.target_uuid = MsgAdapter.ReadUUID()
		data.target_name = MsgAdapter.ReadStrN(32)
		data.is_sponsor = MsgAdapter.ReadChar()
		data.is_win = MsgAdapter.ReadChar()
		MsgAdapter.ReadShort()
		data.old_score = MsgAdapter.ReadUInt()  --战斗前积分
		data.new_score = MsgAdapter.ReadUInt()  --战斗后积分
		data.server_id = MsgAdapter.ReadLL()       -- 服务器id
		table.insert(self.report_info, data)
	end
	local sort_fun = function (a, b)
		return a.challenge_time > b.challenge_time
	end
	if #self.report_info ~= 0 then
		table.sort(self.report_info, sort_fun)
	end
end

--竞技场跳过战斗返回（场景外）
SCCrossChallengeFieldSkipResult = SCCrossChallengeFieldSkipResult or BaseClass(BaseProtocolStruct)
function SCCrossChallengeFieldSkipResult:__init()
	self.msg_type = 16633
end

function SCCrossChallengeFieldSkipResult:Decode()
	self.is_win = MsgAdapter.ReadInt()
end


SCCrossChallengeFieldSkipInfo = SCCrossChallengeFieldSkipInfo or BaseClass(BaseProtocolStruct)
function SCCrossChallengeFieldSkipInfo:__init()
	self.msg_type = 16666
end

function SCCrossChallengeFieldSkipInfo:Decode()
	self.is_can_skip = MsgAdapter.ReadInt()   --是否开启秒杀
end
-------------------------------------------天梯争霸 结束--------------------------------------

--------------------------双修start------------------------------------
-- 技能触发
SCArtifactSkillInfo = SCArtifactSkillInfo or BaseClass(BaseProtocolStruct)
function SCArtifactSkillInfo:__init()
    self.msg_type = 16634
end

function SCArtifactSkillInfo:Decode()
	self.skill_id = MsgAdapter.ReadInt() -- 技能id
end
--------------------------双修end------------------------------------

--------------------------仙修怒气技能 Start-------------------------
local function GetMsgXiuWeiNuQiLevel()
	local data = {}
	data.nuqi_upgrade_level = MsgAdapter.ReadInt()
	data.nuqi_buff_level = MsgAdapter.ReadInt()
	data.nuqi_buff_exp = MsgAdapter.ReadInt()
	data.nuqi_skill_level = {}
	for i=0,3 do
		data.nuqi_skill_level[i] = MsgAdapter.ReadInt()
	end
	return data
end

SCRoleXiuWeiNuqiLevelInfo = SCRoleXiuWeiNuqiLevelInfo or BaseClass(BaseProtocolStruct)
function SCRoleXiuWeiNuqiLevelInfo:__init()
    self.msg_type = 16635
end

function SCRoleXiuWeiNuqiLevelInfo:Decode()
	self.nuqi_type = MsgAdapter.ReadInt()
	self.nuqi_level = GetMsgXiuWeiNuQiLevel()
end
--------------------------仙修怒气技能 End-------------------------


------------------------抽奖礼包Start---------------------------
SCDrawGiftAllInfo = SCDrawGiftAllInfo or BaseClass(BaseProtocolStruct)
function SCDrawGiftAllInfo:__init()
	self.msg_type = 16636
end

function SCDrawGiftAllInfo:Decode()
	self.draw_gift_count_list = {}
	local info_len = MsgAdapter.ReadUInt()
	for i = 1, info_len do
		local item_id = MsgAdapter.ReadUShort()
		local count = MsgAdapter.ReadUShort()
		self.draw_gift_count_list[item_id] = count
	end
end

SCDrawGiftUpdate = SCDrawGiftUpdate or BaseClass(BaseProtocolStruct)
function SCDrawGiftUpdate:__init()
	self.msg_type = 16637
end

function SCDrawGiftUpdate:Decode()
    local data = {}
    data.item_id = MsgAdapter.ReadUShort()
    data.count = MsgAdapter.ReadUShort()
	self.change_data = data
end
------------------------抽奖礼包End---------------------------

------------------------悬赏任务Start---------------------------
-- 悬赏任务操作
CSBountyListOperate =  CSBountyListOperate or BaseClass(BaseProtocolStruct)
function CSBountyListOperate:__init()
	self.msg_type = 16638
	self.opera_type = 0;
	self.param2 = 0;
	self.param3 = 0;
end

function CSBountyListOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

-- 悬赏列表信息
SCBountyListInfo = SCBountyListInfo or BaseClass(BaseProtocolStruct)
function SCBountyListInfo:__init()
	self.msg_type = 16639
end

function SCBountyListInfo:Decode()
	self.cur_accept_bounty_id = MsgAdapter.ReadInt()					--// 当前接受的悬赏单
	self.today_accept_bounty_num = MsgAdapter.ReadInt()					--// 今日接受的数量
	self.today_free_fresh_num = MsgAdapter.ReadInt()					--// 今日刷新次数
	self.bounty_note_reward_flag = bit:d2b_two(MsgAdapter.ReadInt())	--// 手记奖励标记
	self.cur_finale_task_id = MsgAdapter.ReadInt()						--// 结局任务id
	self.cur_execulte_task_seq = MsgAdapter.ReadShort()					--// 当前任务进度
	self.cur_all_task_num = MsgAdapter.ReadShort()						--// 总任务数

	self.show_bounty_list_id = {}
	for i = 1, BOUNTY_OPERATE_ENUM.BOUNTY_LIST_SHOW_NUM do
		self.show_bounty_list_id[i] = {}
		self.show_bounty_list_id[i].bounty_id = MsgAdapter.ReadInt()
		self.show_bounty_list_id[i].state = MsgAdapter.ReadInt()
	end

	self.bounty_info = {}
	for i = 0, BOUNTY_OPERATE_ENUM.BOUNTY_LIST_MAX_NUM - 1 do
		self.bounty_info[i] = bit:d2b_two(MsgAdapter.ReadInt())
	end
end
------------------------悬赏任务End---------------------------

------------------------百亿补贴 Start---------------------------
--百亿补贴操作.
CSTenBillionSubsidyClientOperate =  CSTenBillionSubsidyClientOperate or BaseClass(BaseProtocolStruct)
function CSTenBillionSubsidyClientOperate:__init()
	self.msg_type = 16640
end

function CSTenBillionSubsidyClientOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
	MsgAdapter.WriteInt(self.param4)
	MsgAdapter.WriteLL(self.param5)
end

--百亿补贴总信息.
SCTenBillionSubsidyAllInfo = SCTenBillionSubsidyAllInfo or BaseClass(BaseProtocolStruct)
function SCTenBillionSubsidyAllInfo:__init()
	self.msg_type = 16641
end

function SCTenBillionSubsidyAllInfo:Decode()
	self.member_level = MsgAdapter.ReadUChar()										--会员等级
	self.today_use_pay_one_get_more_times = MsgAdapter.ReadUChar()					--今日已使用买一得多次数
	self.fetch_first_open_view_reward = MsgAdapter.ReadUChar()						--领取每日首次打开界面奖励标识 0:未领取 1:已领取
	self.first_pay_reward_flag = MsgAdapter.ReadUChar()								--首次单笔支付奖励状态(首笔满5返30) 0:未完成 1:已完成可领取 2:已领取
	self.today_used_free_ticket_num = MsgAdapter.ReadUChar()						--今日使用免单券数量
	self.today_no_limit_discount_ticket_used_times = MsgAdapter.ReadUChar()			--今日立减券使用次数
	self.today_return_reward_fetch_times = MsgAdapter.ReadUChar()					--今日满额返现奖励领取次数
	self.today_try_ticket_use_times = MsgAdapter.ReadUChar()						--试用券使用次数
	self.today_pay_one_get_more_shop_refresh_times = MsgAdapter.ReadUChar()			--今日买一得多商店刷新次数
	self.today_limit_discount_ticket_used_times = MsgAdapter.ReadUChar()			--今日满减券使用次数
	self.today_member_reward_fetch_flag = bit:d2b_two(MsgAdapter.ReadUChar())		--每日高级会员奖励领取状态    二进制 位序表示会员等级 0表示未领取 1表示已领取 低位到高位
	self.first_open_view_flag = MsgAdapter.ReadUChar()								--首次打开界面标识
	self.cumulate_order_num = MsgAdapter.ReadUShort()								--累计订单数
	self.cumulate_order_reward_can_fetch_times = MsgAdapter.ReadUShort()			--累计订单数奖励可领取次数
	self.high_price_shop_buy_flag = bit:d2b_two(MsgAdapter.ReadInt())				--大额直购购买信息 二进制 位序表示商品索引 0表示未购买 1表示已购买
	self.today_return_quota = MsgAdapter.ReadUInt()									--今日满额返现累计额度
	self.total_saved_chongzhi_num = MsgAdapter.ReadUInt()							--总节省的金额
	self.time_limited_discoun_shopt_start_timestamp = MsgAdapter.ReadUInt()			--限时折扣商店折扣开始时间(百亿补贴，限时折扣共用此时间)
	self.try_ticket_num = MsgAdapter.ReadUInt()										--试用期数量

	local max_tbs_discount_ticket_num = 16											--最大折扣券数量
	self.discount_ticket_list = {}													--折扣券信息
	for i = 0, max_tbs_discount_ticket_num - 1 do
		self.discount_ticket_list[i] = {}
		self.discount_ticket_list[i].data_seq = i									--数据索引下标
		self.discount_ticket_list[i].ticket_seq = MsgAdapter.ReadUChar()			--券索引
		self.discount_ticket_list[i].expend_times = MsgAdapter.ReadUChar()			--膨胀次数
		self.discount_ticket_list[i].reduce_quota = MsgAdapter.ReadUShort()			--减免金额
	end

	local max_tbs_ten_billion_subsidy_shop_item_num = 64							--百亿补贴商店最大商品数量
	self.ten_billion_subsidy_shop_buy_count_list = {}								--百亿补贴商店购买信息
	for i = 0, max_tbs_ten_billion_subsidy_shop_item_num - 1 do
		self.ten_billion_subsidy_shop_buy_count_list[i] = MsgAdapter.ReadUChar()
	end

	local max_tbs_time_limited_shop_item_num = 64									--限时折扣商店最大商品数量
	self.time_limited_discount_shop_buy_count_list = {}								--限时折扣商店购买信息
	for i = 0, max_tbs_time_limited_shop_item_num - 1 do
		self.time_limited_discount_shop_buy_count_list[i] = MsgAdapter.ReadUChar()
	end

	local max_tbs_pay_one_get_more_shop_item_num = 32								--买一得多商店最大商品数量
	self.pay_one_get_more_shop_item_list = {}										--买一得多商店商品信息
	for i = 0, max_tbs_pay_one_get_more_shop_item_num - 1 do
		self.pay_one_get_more_shop_item_list[i] = ProtocolStruct.ReadPayOneGetMoreShopItemInfo()
	end

	local max_tbs_gold_shop_item_num = 64											--灵玉商店商店最大商品数量
	self.gold_shop_buy_count_list = {}												--灵玉商店购买信息
	for i = 0, max_tbs_gold_shop_item_num - 1 do
		self.gold_shop_buy_count_list[i] = MsgAdapter.ReadUChar()
	end

	local max_tbs_daily_gift_item_num = 4											--最大每日礼包最大商品数量
	self.daily_gift_buy_count_list = {}												--最大每日礼包购买信息
	for i = 0, max_tbs_daily_gift_item_num - 1 do
		self.daily_gift_buy_count_list[i] = MsgAdapter.ReadUChar()
	end

	local max_tbs_try_ticket_shop_item_num = 8										--最大试用券商店商店数量
	self.try_ticket_shop_buy_count_list = {}										--试用券商店购买信息
	for i = 0, max_tbs_try_ticket_shop_item_num - 1 do
		self.try_ticket_shop_buy_count_list[i] = MsgAdapter.ReadUChar()
	end

	local max_tbs_quota_shop_item_num = 32											--定额商店购买信息数量
	self.quota_shop_buy_count_list = {}												--定额商店购买信息
	for i = 0, max_tbs_quota_shop_item_num - 1 do
		self.quota_shop_buy_count_list[i] = MsgAdapter.ReadUChar()
	end
end

--百亿补贴大额直购分享助力次数信息.
SCTenBillionSubsidyHighPriceShareHelpTimesInfo = SCTenBillionSubsidyHighPriceShareHelpTimesInfo or BaseClass(BaseProtocolStruct)
function SCTenBillionSubsidyHighPriceShareHelpTimesInfo:__init()
	self.msg_type = 16642
end

function SCTenBillionSubsidyHighPriceShareHelpTimesInfo:Decode()
	local max_tbs_high_price_rmb_buy_item_num = 8;									--最大大额直购商品数量
	self.share_help_num = {}														--下标:商品索引 值:当前助力次数
	for i = 0, max_tbs_high_price_rmb_buy_item_num - 1 do
		self.share_help_num[i] = MsgAdapter.ReadUChar()
	end
end

--百亿补贴首次打开界面标识更新.
SCTenBillionSubsidyFirstOpenViewFlagUpdate = SCTenBillionSubsidyFirstOpenViewFlagUpdate or BaseClass(BaseProtocolStruct)
function SCTenBillionSubsidyFirstOpenViewFlagUpdate:__init()
	self.msg_type = 16643
end

function SCTenBillionSubsidyFirstOpenViewFlagUpdate:Decode()
	self.first_open_view_flag = MsgAdapter.ReadUChar()								--首次打开界面标识
end

--百亿补贴参团商品总信息.
SCTenBillionSubsidyParticipateAllInfo = SCTenBillionSubsidyParticipateAllInfo or BaseClass(BaseProtocolStruct)
function SCTenBillionSubsidyParticipateAllInfo:__init()
	self.msg_type = 16644
end

function SCTenBillionSubsidyParticipateAllInfo:Decode()
	local max_tbs_participate_player_num = 8;										--最大参团玩家数量
	local max_tbs_participate_item_num = 24;										--最大参团商品数量
	self.item_info_list = {}														--所有商品团购信息
	for i = 0, max_tbs_participate_item_num - 1 do
		self.item_info_list[i] = {}
		self.item_info_list[i].item_seq = MsgAdapter.ReadUChar()					--商品索引,i = item_seq
		self.item_info_list[i].participate_player_num = MsgAdapter.ReadUChar()		--参团人数
		self.item_info_list[i].parparticipate_reward_fetch_flag = bit:d2b8_two(MsgAdapter.ReadUChar());		--团购奖励领取状态 index与 participate_info 的index对应，value:0可领取 1已领取
		self.item_info_list[i].reserve_ch = MsgAdapter.ReadChar()					--预留
		local player_list = {}
		for i = 0, max_tbs_participate_player_num - 1 do
			player_list[i] = ProtocolStruct.TenBillionSubsidyPlayerInfo()
		end
		self.item_info_list[i].participate_info = player_list						--参团玩家信息
	end
end

--百亿补贴总节省金额更新
SCTenBillionSubsidyParticipateInfoUpdate = SCTenBillionSubsidyParticipateInfoUpdate or BaseClass(BaseProtocolStruct)
function SCTenBillionSubsidyParticipateInfoUpdate:__init()
	self.msg_type = 16645
end

function SCTenBillionSubsidyParticipateInfoUpdate:Decode()
	self.total_saved_chongzhi_num = MsgAdapter.ReadUInt()							--总节省的金额
end

--百亿补贴会员奖励信息更新(领取试用券，折扣券)
SCTenBillionSubsidyMemberDailyRewardUpdate = SCTenBillionSubsidyMemberDailyRewardUpdate or BaseClass(BaseProtocolStruct)
function SCTenBillionSubsidyMemberDailyRewardUpdate:__init()
	self.msg_type = 16646
end

function SCTenBillionSubsidyMemberDailyRewardUpdate:Decode()
	self.fetch_first_open_view_reward = MsgAdapter.ReadUChar()						--领取每日首次打开界面奖励标识 0:未领取 1:已领取
	self.today_member_reward_fetch_flag = bit:d2b_two(MsgAdapter.ReadUChar())		--每日高级会员奖励领取状态
	self.member_level = MsgAdapter.ReadUChar()										--会员等级
end

--百亿补贴会员累计订单数量奖励信息更新(领取免单券)
SCTenBillionSubsidyCumulateOrderNumInfoUpdate = SCTenBillionSubsidyCumulateOrderNumInfoUpdate or BaseClass(BaseProtocolStruct)
function SCTenBillionSubsidyCumulateOrderNumInfoUpdate:__init()
	self.msg_type = 16647
end

function SCTenBillionSubsidyCumulateOrderNumInfoUpdate:Decode()
	self.cumulate_order_num = MsgAdapter.ReadUShort()								--累计订单数
	self.cumulate_order_reward_can_fetch_times = MsgAdapter.ReadUChar()				--累计订单数奖励可领取次数
end

-- 付一买三商店数据更新
SCTenBillionSubsidyPayOneGetMoreShopInfoUpdate = SCTenBillionSubsidyPayOneGetMoreShopInfoUpdate or BaseClass(BaseProtocolStruct)
function SCTenBillionSubsidyPayOneGetMoreShopInfoUpdate:__init()
	self.msg_type = 16653
end

function SCTenBillionSubsidyPayOneGetMoreShopInfoUpdate:Decode()
	self.today_use_pay_one_get_more_times = MsgAdapter.ReadUChar()						--今日已使用付一买三次数
	self.today_pay_one_get_more_shop_refresh_times = MsgAdapter.ReadUChar()				--今日付一买三商店刷新次数
	MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
	self.pay_one_get_more_shop_item_list = {}											--买一得多商店商品信息
	for i = 0, 31 do
		self.pay_one_get_more_shop_item_list[i] = ProtocolStruct.ReadPayOneGetMoreShopItemInfo()
	end
end

-- 百亿补贴会员单笔首付和满额返现信息更新
SCTenBillionSubsidyFirstPayAndReturnRewardInfo = SCTenBillionSubsidyFirstPayAndReturnRewardInfo or BaseClass(BaseProtocolStruct)
function SCTenBillionSubsidyFirstPayAndReturnRewardInfo:__init()
	self.msg_type = 16648
end

function SCTenBillionSubsidyFirstPayAndReturnRewardInfo:Decode()
	self.first_pay_reward_flag = MsgAdapter.ReadUChar()								--首次单笔支付奖励状态(首笔满5返30) 0:未完成 1:已完成可领取 2:已领取
	self.today_return_reward_fetch_times = MsgAdapter.ReadUChar()					--今日满额返现奖励领取次数
	for i = 0, 1 do
		local reserve_chs = MsgAdapter.ReadUChar()
	end

	self.today_return_quota = MsgAdapter.ReadUInt()									--今日满额返现累计额度
end

-- 百亿补贴优惠券使用次数更新
SCTenBillionSubsidyTicketUseLimitInfoUpdate = SCTenBillionSubsidyTicketUseLimitInfoUpdate or BaseClass(BaseProtocolStruct)
function SCTenBillionSubsidyTicketUseLimitInfoUpdate:__init()
	self.msg_type = 16649
end

function SCTenBillionSubsidyTicketUseLimitInfoUpdate:Decode()
	self.today_used_free_ticket_num = MsgAdapter.ReadUChar()						--今日使用免单券数量
	self.today_no_limit_discount_ticket_used_times = MsgAdapter.ReadUChar()			--今日立减券使用次数
	local reserve_ch = MsgAdapter.ReadChar()
	self.today_limit_discount_ticket_used_times = MsgAdapter.ReadUChar()			--今日满减券使用次数
end

-- 百亿补贴会员折扣券和试用券数据更新
SCTenBillionSubsidyAllTicketInfoUpdate = SCTenBillionSubsidyAllTicketInfoUpdate or BaseClass(BaseProtocolStruct)
function SCTenBillionSubsidyAllTicketInfoUpdate:__init()
	self.msg_type = 16650
end

function SCTenBillionSubsidyAllTicketInfoUpdate:Decode()
	self.try_ticket_num = MsgAdapter.ReadUInt()
	local max_tbs_discount_ticket_num = 16											--最大折扣券数量
	self.discount_ticket_list = {}													--折扣券信息
	for i = 0, max_tbs_discount_ticket_num - 1 do
		self.discount_ticket_list[i] = {}
		self.discount_ticket_list[i].data_seq = i									--数据索引下标
		self.discount_ticket_list[i].ticket_seq = MsgAdapter.ReadUChar()			--券索引
		self.discount_ticket_list[i].expend_times = MsgAdapter.ReadUChar()			--膨胀次数
		self.discount_ticket_list[i].reduce_quota = MsgAdapter.ReadUShort()			--减免金额
	end
end

-- 百亿补贴会员单个折扣券数据更新
SCTenBillionSubsidySingleDiscountTicketInfoUpdate = SCTenBillionSubsidySingleDiscountTicketInfoUpdate or BaseClass(BaseProtocolStruct)
function SCTenBillionSubsidySingleDiscountTicketInfoUpdate:__init()
	self.msg_type = 16651
end

function SCTenBillionSubsidySingleDiscountTicketInfoUpdate:Decode()
	self.ticket_index = MsgAdapter.ReadUChar()										-- 数组下标 从0开始
	local reserve_chs = MsgAdapter.ReadChar()
	local reserve_chs1 = MsgAdapter.ReadChar()
	local reserve_chs2 = MsgAdapter.ReadChar()
	self.discount_ticket_info = {}
	self.discount_ticket_info.data_seq = self.ticket_index					--数据索引下标
	self.discount_ticket_info.ticket_seq = MsgAdapter.ReadUChar()			--券索引
	self.discount_ticket_info.expend_times = MsgAdapter.ReadUChar()			--膨胀次数
	self.discount_ticket_info.reduce_quota = MsgAdapter.ReadUShort()		--减免金额
end

-- 试用券商店数据更新
SCMRSYTryTicketShopInfoUpdate = SCMRSYTryTicketShopInfoUpdate or BaseClass(BaseProtocolStruct)
function SCMRSYTryTicketShopInfoUpdate:__init()
	self.msg_type = 16652
end

function SCMRSYTryTicketShopInfoUpdate:Decode()
	self.try_ticket_num = MsgAdapter.ReadUInt()
	self.today_try_ticket_use_times = MsgAdapter.ReadUChar()
	for i = 0, 2 do
		local reserve_chs = MsgAdapter.ReadChar()
	end

	local max_tbs_try_ticket_shop_item_num = 8
	self.try_ticket_shop_buy_count_list = {}
	for i = 0, max_tbs_try_ticket_shop_item_num - 1 do
		self.try_ticket_shop_buy_count_list[i] = MsgAdapter.ReadUChar()
	end
end

-- 限时折扣商店开始折扣时间戳数据更新
SCXDZKStartDiscountTimestampUpdate = SCXDZKStartDiscountTimestampUpdate or BaseClass(BaseProtocolStruct)
function SCXDZKStartDiscountTimestampUpdate:__init()
	self.msg_type = 16654
end

function SCXDZKStartDiscountTimestampUpdate:Decode()
	self.time_limited_discoun_shopt_start_timestamp = MsgAdapter.ReadUInt()
end

-- 限时折扣商店单个商品数据更新
SCXDZKSingleItemInfoUpdate = SCXDZKSingleItemInfoUpdate or BaseClass(BaseProtocolStruct)
function SCXDZKSingleItemInfoUpdate:__init()
	self.msg_type = 16655
end

function SCXDZKSingleItemInfoUpdate:Decode()
	self.change_item_seq = MsgAdapter.ReadUChar()
	self.change_buy_count = MsgAdapter.ReadUChar()
	MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
end

-- 百亿补贴商店单个商品数据更新
SCBYBTSingleItemInfoUpdate = SCBYBTSingleItemInfoUpdate or BaseClass(BaseProtocolStruct)
function SCBYBTSingleItemInfoUpdate:__init()
	self.msg_type = 16656
end

function SCBYBTSingleItemInfoUpdate:Decode()
	self.change_item_seq = MsgAdapter.ReadUChar()
	self.change_buy_count = MsgAdapter.ReadUChar()
	MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
end

-- 百亿补贴会员每日礼包单个商品数据更新
SCTenBillionSubsidyDailyShopSingleItemInfoUpdate = SCTenBillionSubsidyDailyShopSingleItemInfoUpdate or BaseClass(BaseProtocolStruct)
function SCTenBillionSubsidyDailyShopSingleItemInfoUpdate:__init()
	self.msg_type = 16657
end

function SCTenBillionSubsidyDailyShopSingleItemInfoUpdate:Decode()
	self.vip_daily_gift_item_seq = MsgAdapter.ReadUChar()
	self.vip_daily_gift_buy_countg = MsgAdapter.ReadUChar()
	local reserve_chs = MsgAdapter.ReadChar()
end

-- 灵玉商店单个商品数据更新
SCLYSDSingleItemInfoUpdate = SCLYSDSingleItemInfoUpdate or BaseClass(BaseProtocolStruct)
function SCLYSDSingleItemInfoUpdate:__init()
	self.msg_type = 16658
end

function SCLYSDSingleItemInfoUpdate:Decode()
	self.change_item_seq = MsgAdapter.ReadUChar()						-- 商品索引
	self.change_buy_count = MsgAdapter.ReadUChar()						-- 购买次数
	MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
end

-- 定额商店(9块9专场)单个商品数据更新
SCTenBillionSubsidyQuotaShopSingleItemInfoUpdate = SCTenBillionSubsidyQuotaShopSingleItemInfoUpdate or BaseClass(BaseProtocolStruct)
function SCTenBillionSubsidyQuotaShopSingleItemInfoUpdate:__init()
	self.msg_type = 16659
end

function SCTenBillionSubsidyQuotaShopSingleItemInfoUpdate:Decode()
	self.item_seq = MsgAdapter.ReadUChar()						-- 商品索引
	self.buy_count = MsgAdapter.ReadUChar()						-- 购买次数
	MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
end

--百亿补贴大额直购购买信息.
SCTenBillionSubsidyHighPriceShopBuyFlagInfoUpdate = SCTenBillionSubsidyHighPriceShopBuyFlagInfoUpdate or BaseClass(BaseProtocolStruct)
function SCTenBillionSubsidyHighPriceShopBuyFlagInfoUpdate:__init()
	self.msg_type = 16660
end

function SCTenBillionSubsidyHighPriceShopBuyFlagInfoUpdate:Decode()
	self.high_price_shop_buy_flag = bit:d2b_two(MsgAdapter.ReadUInt()) --大额直购购买信息 二进制 位序表示商品索引 0表示未购买 1表示已购买
	self.high_price_shop_buy_times = MsgAdapter.ReadInt()	--购买次数
	self.high_price_shop_reduce_flag = bit:d2b_two(MsgAdapter.ReadLL())	--奖励领取标记
end
------------------------百亿补贴 End---------------------------

--------------------------------[跨服红包天降] star--------------------------------

local function GetCrossRedPaperFallingRoundSettlementItem()
	local data = {}
    data.item_id = MsgAdapter.ReadItemId()
	MsgAdapter.ReadUShort() -- re_sh
    data.num = MsgAdapter.ReadInt()
	return data
end
--跨服红包天降
CSCrossRedPaperFallingOperate =  CSCrossRedPaperFallingOperate or BaseClass(BaseProtocolStruct)
function CSCrossRedPaperFallingOperate:__init()
	self.msg_type = 16661
end

function CSCrossRedPaperFallingOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)

	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

--跨服红包天降 玩家信息下推	
SCCrossRedPaperFallingRewardInfo = SCCrossRedPaperFallingRewardInfo or BaseClass(BaseProtocolStruct)
function SCCrossRedPaperFallingRewardInfo:__init()
	self.msg_type = 16662
end

function SCCrossRedPaperFallingRewardInfo:Decode()
    local data = {}
    data.reward = GetCrossRedPaperFallingRoundSettlementItem()
	-- 红包当前轮数已获得奖励标识
	-- local flag = MsgAdapter.ReadLL()
	
    data.current_round_reward_flag  = bit:ll2b_two(MsgAdapter.ReadUInt(), MsgAdapter.ReadUInt())
    -- data.current_round_reward_flag  =  bit:d2b_l2h(flag)
	self.reward_info = data
end

--跨服红包天降 活动信息下推
SCCrossRedPaperFallingActivityInfo	 = SCCrossRedPaperFallingActivityInfo	 or BaseClass(BaseProtocolStruct)
function SCCrossRedPaperFallingActivityInfo	:__init()
	self.msg_type = 16663
end

function SCCrossRedPaperFallingActivityInfo	:Decode()
    local data = {}
    data.add_round  = MsgAdapter.ReadUInt()  			-- 额外增加轮数
    data.current_round  = MsgAdapter.ReadUInt() 		-- 当前轮数
    data.round_state  = MsgAdapter.ReadUInt() 			-- 当前状态 0:关闭 1:首轮开启准备中 2:抢红包中 3:下一轮间隔
    data.next_state_time  = MsgAdapter.ReadUInt() 		-- 下一次状态更改时间戳
    data.add_round_role_consume_num  = MsgAdapter.ReadUInt() 		-- 本轮额外加入的灵玉

	data.role_info = {}
    data.role_info.role_id  = MsgAdapter.ReadInt() 		-- role_id
    data.role_info.role_name  = MsgAdapter.ReadName() 	-- role_name
	data.is_role_item_open = MsgAdapter.ReadChar()		--0:系统活动默认开启 1:玩家使用道具开启.
	data.role_info.sex = MsgAdapter.ReadChar()
	data.enjoy_status = MsgAdapter.ReadChar()			--判断是否参与，防止路过的人中途加入.
	MsgAdapter.ReadChar()
	data.role_info.prof = MsgAdapter.ReadInt()
	data.grade = MsgAdapter.ReadInt()
	data.role_info.avatar_key_big = MsgAdapter.ReadUInt()
	data.role_info.avatar_key_small = MsgAdapter.ReadUInt()
	self.activity_info = data
end

--跨服红包天降 每轮结算下推
SCCrossRedPaperFallingRoundSettlement = SCCrossRedPaperFallingRoundSettlement or BaseClass(BaseProtocolStruct)
function SCCrossRedPaperFallingRoundSettlement:__init()
	self.msg_type = 16664
end

function SCCrossRedPaperFallingRoundSettlement:Decode()
    local count  = MsgAdapter.ReadInt()  	
	-- self.settlement = 	
	self.reward_list = {}	
	for i = 1, count do
		self.reward_list[i] = GetCrossRedPaperFallingRoundSettlementItem()
	end
end

--跨服红包天降 累计货币奖励
SCCrossRedPaperFallingRoleInfo = SCCrossRedPaperFallingRoleInfo or BaseClass(BaseProtocolStruct)
function SCCrossRedPaperFallingRoleInfo:__init()
	self.msg_type = 16665
end

function SCCrossRedPaperFallingRoleInfo:Decode()
	self.role_info = {}	

	-- 当前场次已增加次数
	self.role_info.add_round = MsgAdapter.ReadUShort()  	
	self.role_info.add_activity = MsgAdapter.ReadUShort()	--当日开始新活动次数.

	-- MAX_MONEY_COOUNT = 6
	self.role_info.gold_sum = MsgAdapter.ReadLL()  	
	self.role_info.yuanbao_sum = MsgAdapter.ReadLL()  -- 元宝(绑玉)
	self.role_info.coin_sum = MsgAdapter.ReadLL() 
	self.role_info.score = MsgAdapter.ReadLL() 
	MsgAdapter.ReadLL() 
	MsgAdapter.ReadLL() 
end


CSTaskWalkToPos = CSTaskWalkToPos or BaseClass(BaseProtocolStruct)
function CSTaskWalkToPos:__init()
	self.msg_type = 16667
end

function CSTaskWalkToPos:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end

--------------------------------染色协议 start--------------------------------
--// 染色所有信息
SCShizhuangDyeingInfo = SCShizhuangDyeingInfo or BaseClass(BaseProtocolStruct)
function SCShizhuangDyeingInfo:__init()
	self.msg_type = 16668
end

function SCShizhuangDyeingInfo:Decode()
	self.dyeing_item = {}

	for i = 1, SHIZHUANG_DYE_ENUM.MSG_MAX_SHIZHUANG_DYEING_COUNT do
		self.dyeing_item[i] = {}
		self.dyeing_item[i].curr_dyeing_index = MsgAdapter.ReadChar()
		self.dyeing_item[i].dyeing_project_flag = bit:d2b8_two(MsgAdapter.ReadChar())  
		MsgAdapter.ReadShort()
		self.dyeing_item[i].project_info = {}
		
		for j = 1, SHIZHUANG_DYE_ENUM.MSG_MAX_DYEING_PROJECT_COUNT do
			self.dyeing_item[i].project_info[j] = {}
			self.dyeing_item[i].project_info[j].name = MsgAdapter.ReadName()
			self.dyeing_item[i].project_info[j].info = MsgAdapter.ReadStrN(SHIZHUANG_DYE_ENUM.MSG_MAX_DYEING_PROJECT_INFO)
			local color_info = {}
			for m = 1, SHIZHUANG_DYE_ENUM.MSG_MAX_DYEING_PART_COUNT do
				if m <= SHIZHUANG_DYE_ENUM.MSG_CUR_DYEING_PART_COUNT then	-- 目前只取4个，服务器留了6个
					color_info[m] = {}
					color_info[m].r = MsgAdapter.ReadUChar()
					color_info[m].g = MsgAdapter.ReadUChar()
					color_info[m].b = MsgAdapter.ReadUChar()
					color_info[m].a = MsgAdapter.ReadUChar()
				else
					MsgAdapter.ReadUInt()
				end
			end

			self.dyeing_item[i].project_info[j].part_color = color_info
		end
	end
end

--// 单个染色方案信息
SCShizhuangDyeingUpdateInfo = SCShizhuangDyeingUpdateInfo or BaseClass(BaseProtocolStruct)
function SCShizhuangDyeingUpdateInfo:__init()
	self.msg_type = 16669
end

function SCShizhuangDyeingUpdateInfo:Decode()
	self.seq = MsgAdapter.ReadInt()  
	self.project_index = MsgAdapter.ReadShort()  
	self.curr_dyeing_index = MsgAdapter.ReadChar()  
	self.dyeing_project_flag = bit:d2b8_two(MsgAdapter.ReadChar())  
	self.dyeing_project = {}

	self.dyeing_project.name = MsgAdapter.ReadName()
	self.dyeing_project.info = MsgAdapter.ReadStrN(SHIZHUANG_DYE_ENUM.MSG_MAX_DYEING_PROJECT_INFO)

	local color_info = {}
	for m = 1, SHIZHUANG_DYE_ENUM.MSG_MAX_DYEING_PART_COUNT do
		if m <= SHIZHUANG_DYE_ENUM.MSG_CUR_DYEING_PART_COUNT then	-- 目前只取4个，服务器留了6个
			color_info[m] = {}
			color_info[m].r = MsgAdapter.ReadUChar()
			color_info[m].g = MsgAdapter.ReadUChar()
			color_info[m].b = MsgAdapter.ReadUChar()
			color_info[m].a = MsgAdapter.ReadUChar()
		else
			MsgAdapter.ReadUInt()
		end
	end

	self.dyeing_project.part_color = color_info
end

--染色请求
CSShizhuangDyeingOperate =  CSShizhuangDyeingOperate or BaseClass(BaseProtocolStruct)
function CSShizhuangDyeingOperate:__init()
	self.msg_type = 16670
end

function CSShizhuangDyeingOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)

	MsgAdapter.WriteInt(self.seq)
	MsgAdapter.WriteShort(self.project_index)
	MsgAdapter.WriteShort(self.is_consume)
	for m = 1, SHIZHUANG_DYE_ENUM.MSG_MAX_DYEING_PART_COUNT do
		MsgAdapter.WriteUChar(((self.part_color or {})[m] or {}).r or 0)
		MsgAdapter.WriteUChar(((self.part_color or {})[m] or {}).g or 0)
		MsgAdapter.WriteUChar(((self.part_color or {})[m] or {}).b or 0)
		MsgAdapter.WriteUChar(((self.part_color or {})[m] or {}).a or 0)
	end
end

--染色方案改变信息请求
CSShizhuangDyeingChangeInfoOper =  CSShizhuangDyeingChangeInfoOper or BaseClass(BaseProtocolStruct)
function CSShizhuangDyeingChangeInfoOper:__init()
	self.msg_type = 16671
end

function CSShizhuangDyeingChangeInfoOper:Encode()
	MsgAdapter.WriteBegin(self.msg_type)

	MsgAdapter.WriteInt(self.seq)
	MsgAdapter.WriteInt(self.project_index)
	MsgAdapter.WriteInt(string.len(self.name))
	MsgAdapter.WriteInt(string.len(self.info))
	MsgAdapter.WriteStrN(self.name, string.len(self.name))
	MsgAdapter.WriteStrN(self.info, string.len(self.info))
end

--// 其他人染色方案信息
SCShizhuangOtherRoleDyeingInfo = SCShizhuangOtherRoleDyeingInfo or BaseClass(BaseProtocolStruct)
function SCShizhuangOtherRoleDyeingInfo:__init()
	self.msg_type = 16672
end

function SCShizhuangOtherRoleDyeingInfo:Decode()
	self.seq = MsgAdapter.ReadInt()  
	self.dyeing_project = {}

	self.dyeing_project.name = MsgAdapter.ReadName()
	self.dyeing_project.info = MsgAdapter.ReadStrN(SHIZHUANG_DYE_ENUM.MSG_MAX_DYEING_PROJECT_INFO)

	local color_info = {}
	for m = 1, SHIZHUANG_DYE_ENUM.MSG_MAX_DYEING_PART_COUNT do
		if m <= SHIZHUANG_DYE_ENUM.MSG_CUR_DYEING_PART_COUNT then	-- 目前只取4个，服务器留了6个
			color_info[m] = {}
			color_info[m].r = MsgAdapter.ReadUChar()
			color_info[m].g = MsgAdapter.ReadUChar()
			color_info[m].b = MsgAdapter.ReadUChar()
			color_info[m].a = MsgAdapter.ReadUChar()
		else
			MsgAdapter.ReadUInt()
		end
	end

	self.dyeing_project.part_color = color_info
end

--跨服红包天降
CSShizhuangDyeingOper =  CSShizhuangDyeingOper or BaseClass(BaseProtocolStruct)
function CSShizhuangDyeingOper:__init()
	self.msg_type = 16673
end

function CSShizhuangDyeingOper:Encode()
	MsgAdapter.WriteBegin(self.msg_type)

	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end
--------------------------------染色协议 end--------------------------------


----------------市场拍卖绑玉---------------
SCRoleAuctionInfo = SCRoleAuctionInfo or BaseClass(BaseProtocolStruct)
function SCRoleAuctionInfo:__init()
	self.msg_type = 16674
end

function SCRoleAuctionInfo:Decode()
	self.week_active = MsgAdapter.ReadInt()
	self.week_shelves_count = MsgAdapter.ReadInt()
	self.jump_flag = MsgAdapter.ReadInt()
end

--------------------------------触发boss秒杀 start--------------------------------
SCMonsterSecKillInfo = SCMonsterSecKillInfo or BaseClass(BaseProtocolStruct)
function SCMonsterSecKillInfo:__init()
	self.msg_type = 16675
end

function SCMonsterSecKillInfo:Decode()
	self.sec_kill_uuid = MsgAdapter.ReadUUID()
	self.sec_kill_start_time = MsgAdapter.ReadUInt()
	self.sec_kill_end_time = MsgAdapter.ReadUInt()
	self.sec_kill_type = MsgAdapter.ReadInt()
	self.monster_obj_id = MsgAdapter.ReadUShort()
	MsgAdapter.ReadShort()
end
--------------------------------触发boss秒杀 end--------------------------------

--------------------------------修为任务信息 start--------------------------------
local function GetXiuWeiTaskData()
	local data = {}
	data.status = MsgAdapter.ReadChar()							-- 任务状态，0默认，1可领取，2完成
	local reserve_ch = MsgAdapter.ReadChar()
	local reserve_ch2 = MsgAdapter.ReadShort()
	data.progress = MsgAdapter.ReadLL()
	return data
end

SCRoleXiuWeiTaskInfo = SCRoleXiuWeiTaskInfo or BaseClass(BaseProtocolStruct)
function SCRoleXiuWeiTaskInfo:__init()
	self.msg_type = 16676
end

function SCRoleXiuWeiTaskInfo:Decode()
	self.task_data_list = {}
	for seq = 0, 9 do
		self.task_data_list[seq] = GetXiuWeiTaskData()
	end
end

SCRoleXiuWeiTaskUpdate = SCRoleXiuWeiTaskUpdate or BaseClass(BaseProtocolStruct)
function SCRoleXiuWeiTaskUpdate:__init()
	self.msg_type = 16677
end

function SCRoleXiuWeiTaskUpdate:Decode()
	self.change_seq = MsgAdapter.ReadInt()
	self.change_data = GetXiuWeiTaskData()
end
--------------------------------修为任务信息 end--------------------------------

--------------------------------豪掷千金 start--------------------------------
-- 豪掷千金
SCOARechargeRankBaseInfo = SCOARechargeRankBaseInfo or BaseClass(BaseProtocolStruct)
function SCOARechargeRankBaseInfo:__init()
	self.msg_type = 16678
end

function SCOARechargeRankBaseInfo:Decode()
	self.recharge_num = MsgAdapter.ReadLL()				-- 充值数
	self.daily_reward_flag = MsgAdapter.ReadInt()		-- 每日奖励标记 0: 未领取 1: 已领取
end

-- 本服消费榜
SCOAConsumeGoldRankBaseInfo = SCOAConsumeGoldRankBaseInfo or BaseClass(BaseProtocolStruct)
function SCOAConsumeGoldRankBaseInfo:__init()
	self.msg_type = 16679
end

function SCOAConsumeGoldRankBaseInfo:Decode()
	self.consume_num = MsgAdapter.ReadLL()				-- 消费数
	self.daily_reward_flag = MsgAdapter.ReadInt()		-- 每日奖励标记 0: 未领取 1: 已领取
end
--------------------------------豪掷千金 end--------------------------------

--------------------------------系统预告-升星赠礼 start--------------------------------
local MSG_USTAR_MAX_REWARD_COUNT = 32
SCTreasureUpstarGiftInfo = SCTreasureUpstarGiftInfo or BaseClass(BaseProtocolStruct)
function SCTreasureUpstarGiftInfo:__init()
	self.msg_type = 16680
end

function SCTreasureUpstarGiftInfo:Decode()
	self.grade = MsgAdapter.ReadInt()							-- 档位

	self.free_reward_list = {}
	for i = 0 , MSG_USTAR_MAX_REWARD_COUNT - 1 do
		self.free_reward_list[i] = MsgAdapter.ReadChar()		-- 免费奖励 0: 不可领取 1: 可领取 2: 已领取
	end
	
	self.pay_reward_flag = bit:d2b_two(MsgAdapter.ReadInt())	-- 付费奖励 bit

	self.upstar_gift_rmb_buy_flag = MsgAdapter.ReadChar()

	local char_re_ch = MsgAdapter.ReadChar()
	local short_re_ch = MsgAdapter.ReadShort()
end
--------------------------------系统预告-升星赠礼 end--------------------------------

-------------------------------转职——装备收集 start-------------------------------
SCZhuanZhiEquipCollectInfo = SCZhuanZhiEquipCollectInfo or BaseClass(BaseProtocolStruct)
function SCZhuanZhiEquipCollectInfo:__init()
	self.msg_type = 16681
end

function SCZhuanZhiEquipCollectInfo:Decode()
	self.equip_collect_info = {}
	for i = 0, 3 do
		local data = {}
		for j = 1, 8 do
			data[j] = {}
			data[j].item_id = MsgAdapter.ReadUShort()
			data[j].item_star = MsgAdapter.ReadChar()
			MsgAdapter.ReadChar()
		end
		self.equip_collect_info[i] = data
	end
end

SCZhuanZhiEquipCollectUpdate = SCZhuanZhiEquipCollectUpdate or BaseClass(BaseProtocolStruct)
function SCZhuanZhiEquipCollectUpdate:__init()
	self.msg_type = 16682
end

function SCZhuanZhiEquipCollectUpdate:Decode()
	self.suit_index  = MsgAdapter.ReadInt()
	local data = {}
	for i = 1, 8 do
		data[i] = {}
		data[i].item_id = MsgAdapter.ReadUShort()
		data[i].item_star = MsgAdapter.ReadChar()
		MsgAdapter.ReadChar()
	end

	self.change_data = data
end
-------------------------------转职——装备收集 end--------------------------------

-------------------------------幻兽皮肤 start--------------------------------

-- 皮肤信息
SCBeastSkinInfo = SCBeastSkinInfo or BaseClass(BaseProtocolStruct)
function SCBeastSkinInfo:__init()
	self.msg_type = 16684
end

function SCBeastSkinInfo:Decode()
	local list = {}
	for i = 0, 19 do
		list[i] = MsgAdapter.ReadShort() -- 皮肤等级
	end
	self.skin_level_list = list
end

-------------------------------幻兽皮肤 end--------------------------------

--------------------------------新衣橱 start------------------------------
-- 时装方案保存
CSShiZhuangProjectSave = CSShiZhuangProjectSave or BaseClass(BaseProtocolStruct)
function CSShiZhuangProjectSave:__init()
	self.msg_type = 16685
end

function CSShiZhuangProjectSave:Encode()
	MsgAdapter.WriteBegin(self.msg_type)

	MsgAdapter.WriteInt(self.project_id)
	for j = 0, WARDROBE_NEW_PART_TYPE.MAX_SHIZHUANG_PART do
		MsgAdapter.WriteInt(self.part_list[j] or 0)
	end
end

--时装方案信息
SCShiZhuangProjectInfo = SCShiZhuangProjectInfo or BaseClass(BaseProtocolStruct)
function SCShiZhuangProjectInfo:__init()
	self.msg_type = 16689
end

function SCShiZhuangProjectInfo:Decode()
	self.shizhuang_project = {}
	for i = 1, WARDROBE_NEW_PART_TYPE.MAX_SHIZHUANG_PROJECT_COUNT do
		self.shizhuang_project[i] = {}

		for j = 0, WARDROBE_NEW_PART_TYPE.MAX_SHIZHUANG_PART do
			self.shizhuang_project[i][j] = MsgAdapter.ReadInt()
		end
	end
end

-- 时装打造操作
CSShizhuangForgeOper = CSShizhuangForgeOper or BaseClass(BaseProtocolStruct)
function CSShizhuangForgeOper:__init()
	self.msg_type = 16686
end

function CSShizhuangForgeOper:Encode()
	MsgAdapter.WriteBegin(self.msg_type)

	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

-- 时装天赏信息
SCShizhuangForgeInfo = SCShizhuangForgeInfo or BaseClass(BaseProtocolStruct)
function SCShizhuangForgeInfo:__init()
	self.msg_type = 16687
end

function SCShizhuangForgeInfo:Decode()
	self.forge_item_list = {}
	for i = 1, WARDROBE_NEW_PART_TYPE.MAX_SHIZHUANG_FORGE_COUNT do
		local data = {}
		data.part_type = MsgAdapter.ReadInt()
		data.index = MsgAdapter.ReadInt()
		data.star = MsgAdapter.ReadInt()
		data.grade_level = MsgAdapter.ReadInt()

		data.stone_part_list = {}
		for j = 0, WARDROBE_NEW_PART_TYPE.MAX_STONE_PART_COUNT do
			data.stone_part_list[j] = MsgAdapter.ReadInt()
		end

		self.forge_item_list[i] = data
	end
end

-- 时装天赏信息(单个)
SCShizhuangForgeUpdateInfo = SCShizhuangForgeUpdateInfo or BaseClass(BaseProtocolStruct)
function SCShizhuangForgeUpdateInfo:__init()
	self.msg_type = 16688
end

function SCShizhuangForgeUpdateInfo:Decode()
	self.seq = MsgAdapter.ReadInt()

	local data = {}
	data.part_type = MsgAdapter.ReadInt()
	data.index = MsgAdapter.ReadInt()
	data.star = MsgAdapter.ReadInt()
	data.grade_level = MsgAdapter.ReadInt()

	data.stone_part_list = {}
	for j = 0, WARDROBE_NEW_PART_TYPE.MAX_STONE_PART_COUNT do
		data.stone_part_list[j] = MsgAdapter.ReadInt()
	end

	self.forge_info = data
end

--------------------------------新衣橱 end--------------------------------

SCCangJingShangPuInvestInfo = SCCangJingShangPuInvestInfo or BaseClass(BaseProtocolStruct)

function SCCangJingShangPuInvestInfo:__init()
	self.msg_type = 16683
end

function SCCangJingShangPuInvestInfo:Decode()
	self.invest_week = MsgAdapter.ReadInt()    -- 投资周期
	self.already_invest_num = MsgAdapter.ReadInt()  --已投资数
	self.invest_limit = MsgAdapter.ReadUInt()  --投资额度         可以投资的上限
	self.invest_interest = MsgAdapter.ReadShort()  --投资利息
	self.invest_task_flag = MsgAdapter.ReadShort()  -- 投资任务完成标记
	self.active_tequan_reward_flag = MsgAdapter.ReadChar()  --激活特权奖励标记
	self.can_fetch_invest_flag = MsgAdapter.ReadChar()   -- 是否可以领取投资标记s
	MsgAdapter.ReadShort()
end

-- 贵族信息
SCCangJingShangPuNobilityInfo = SCCangJingShangPuNobilityInfo or BaseClass(BaseProtocolStruct)

function SCCangJingShangPuNobilityInfo:__init()
	self.msg_type = 16690
end

function SCCangJingShangPuNobilityInfo:Decode()
	self.nobility_level = MsgAdapter.ReadInt()    -- 贵族等级
	self.nobility_exp = MsgAdapter.ReadInt()  --贵族经验
	self.nobility_daily_add_score = MsgAdapter.ReadInt()  --贵族每日获得积分经验

	local nobility_task = {}
	for i = 0, 29 do
		nobility_task[i] = {
			seq = i,
			progress = MsgAdapter.ReadUShort(),       -- 每次任务得进度 跟配置表param2比较
			complete_count = MsgAdapter.ReadChar(),    -- 但前完成的任务总次数 与配置表complete_limit 可完成次数比较
			re_ch = MsgAdapter.ReadChar(),
		}
	end
	self.nobility_task = nobility_task
end

-- 贵族任务更新
SCCangJingShangPuNobilityTaskUpdate = SCCangJingShangPuNobilityTaskUpdate or BaseClass(BaseProtocolStruct)

function SCCangJingShangPuNobilityTaskUpdate:__init()
	self.msg_type = 16691
end

function SCCangJingShangPuNobilityTaskUpdate:Decode()
	self.seq = MsgAdapter.ReadInt()
	self.nobility_task = {
		seq = self.seq,
		progress = MsgAdapter.ReadUShort(),
		complete_count = MsgAdapter.ReadChar(),
		re_ch = MsgAdapter.ReadChar(),
	}
end

--------------------------------------运营活动幻兽抽奖--------------------------------------------
SCOABeastDrawItemInfo = SCOABeastDrawItemInfo or BaseClass(BaseProtocolStruct)

function SCOABeastDrawItemInfo:__init()
	self.msg_type = 16692
end

function SCOABeastDrawItemInfo:Decode()
	local draw_item_list = {}
	
	for i = 0, 2 do
		draw_item_list[i] = {
			draw_times = MsgAdapter.ReadInt(),
			lucky = MsgAdapter.ReadLL()
		}
	end
	self.draw_item_list = draw_item_list
end

SCOABeastDrawItemUpdate = SCOABeastDrawItemUpdate or BaseClass(BaseProtocolStruct)

function SCOABeastDrawItemUpdate:__init()
	self.msg_type = 16693
end

function SCOABeastDrawItemUpdate:Decode()
	self.type = MsgAdapter.ReadInt()
	self.draw_item = {
		draw_times = MsgAdapter.ReadInt(),
		lucky = MsgAdapter.ReadLL()
	}
end

SCOABeastDrawResult = SCOABeastDrawResult or BaseClass(BaseProtocolStruct)

function SCOABeastDrawResult:__init()
	self.msg_type = 16694
end

function SCOABeastDrawResult:Decode()
	self.type = MsgAdapter.ReadInt()
	self.mode = MsgAdapter.ReadInt()
	self.baodi_item_id = MsgAdapter.ReadInt()
	self.count = MsgAdapter.ReadInt()

	local result_item_list = {}
	for i = 1, self.count do
		result_item_list[i] = {
			item_id = MsgAdapter.ReadInt(),
			bag_index = MsgAdapter.ReadInt()
		}
	end

	self.result_item_list = result_item_list
end

SCOABeastDrawRecordInfo = SCOABeastDrawRecordInfo or BaseClass(BaseProtocolStruct)

function SCOABeastDrawRecordInfo:__init()
	self.msg_type = 16695
end

function SCOABeastDrawRecordInfo:Decode()
	self.type = MsgAdapter.ReadInt()
	self.count = MsgAdapter.ReadInt()

	local result_item_list = {}
	for i = 1, self.count do
		result_item_list[i] = {
			beast_id = MsgAdapter.ReadInt(),
			time = MsgAdapter.ReadUInt()
		}
	end

	self.result_item_list = result_item_list
end

SCOABeastDrawRecordAdd = SCOABeastDrawRecordAdd or BaseClass(BaseProtocolStruct)

function SCOABeastDrawRecordAdd:__init()
	self.msg_type = 16696
end

function SCOABeastDrawRecordAdd:Decode()
	self.type = MsgAdapter.ReadInt()
	self.record_data = {
		beast_id = MsgAdapter.ReadInt(),
		time = MsgAdapter.ReadUInt()
	}
end

--------------------------------------幻兽属性丹 start------------------------------------
SCBeastPelletInfo = SCBeastPelletInfo or BaseClass(BaseProtocolStruct)
function SCBeastPelletInfo:__init()
	self.msg_type = 16697
end

function SCBeastPelletInfo:Decode()
	self.pellet_level_list = {}

	for i = 0, BEAST_DEFINE.BEASTS_MAX_USE_PELLET do
		self.pellet_level_list[i] = MsgAdapter.ReadInt()
	end
end
--------------------------------------幻兽属性丹 end------------------------------------


--------------------------------------副本组队通用 start------------------------------------
SCTeamCommonBossFBSceneInfo = SCTeamCommonBossFBSceneInfo or BaseClass(BaseProtocolStruct)
function SCTeamCommonBossFBSceneInfo:__init()
	self.msg_type = 16698
end

function SCTeamCommonBossFBSceneInfo:Decode()
	self.fb_seq = MsgAdapter.ReadInt()
	self.stage = MsgAdapter.ReadInt()
	self.next_stage_time = MsgAdapter.ReadUInt() --下个阶段开启时间
	self.start_stage_time = MsgAdapter.ReadUInt() -- 当前阶段开启时间
	self.kill_monster_count = MsgAdapter.ReadShort() -- 已击杀怪物数量
	self.is_end = MsgAdapter.ReadChar()   -- 0 未结束 1 结束
	self.is_pass = MsgAdapter.ReadChar()  -- 0 未通关 1 通关
	self.kick_out_time = MsgAdapter.ReadUInt() -- 踢出副本时间
	self.fb_end_time = MsgAdapter.ReadUInt() -- 副本结束时间
	self.fb_start_time  = MsgAdapter.ReadUInt() -- 副本开始时间
	self.fb_finish_time  = MsgAdapter.ReadUInt() -- 副本完成时间
	self.need_gather_times  = MsgAdapter.ReadInt() -- 需要采集数量
	self.has_gather_times  = MsgAdapter.ReadInt()  -- 已经采集数量
end

SCTeamCommonBossFBPlayerInfo = SCTeamCommonBossFBPlayerInfo or BaseClass(BaseProtocolStruct)
function SCTeamCommonBossFBPlayerInfo:__init()
	self.msg_type = 16699
end

function SCTeamCommonBossFBPlayerInfo:Decode()
	self.fb_seq = MsgAdapter.ReadInt()
	self.get_exp = MsgAdapter.ReadLL() 	
	self.team_leader_reward_list = {}
	for i = 1, 10 do
		local item = ProtocolStruct.ReadMsgItem()
		if item.item_id > 0 then
			table.insert(self.team_leader_reward_list, item)
		end
	end
	
	self.mvp_player_info = {}
	self.mvp_player_info.mvp_uuid  = MsgAdapter.ReadUUID()
	self.mvp_player_info.mvp_name = MsgAdapter.ReadStrN(32)
	self.mvp_player_info.mvp_is_robot = MsgAdapter.ReadChar() 
	self.mvp_player_info.sex = MsgAdapter.ReadChar() 
	MsgAdapter.ReadShort()
	self.mvp_player_info.avatar_timestamp = MsgAdapter.ReadLL()
	self.mvp_player_info.shizhuang_photoframe = MsgAdapter.ReadInt()
	self.mvp_player_info.prof = MsgAdapter.ReadInt()

	self.is_help = MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
	local count = MsgAdapter.ReadShort()
	self.reward_item_list = {}
    for i = 1, count do
        self.reward_item_list[i] = ProtocolStruct.ReadMsgItem()
    end
end

--------------------------------------副本组队通用 end------------------------------------