ArtifactWGData = ArtifactWGData or BaseClass()


function ArtifactWGData:__init()
	if ArtifactWGData.Instance then
		error("[ArtifactWGData] Attempt to create singleton twice!")
		return
	end
	ArtifactWGData.Instance = self

	-- 【配置】
	local cfg = ConfigManager.Instance:GetAutoConfig("artifact_cfg_auto")
	self.other_cfg = cfg.other[1]
	self.artifact_cfg = cfg.artifact
	self.artifact_type_cfg = ListToMapList(cfg.artifact, "type")
    self.artifact_seq_cfg = ListToMap(cfg.artifact, "seq")
    self.artifact_item_cfg = ListToMap(cfg.artifact, "item_id")
   	self.level_cfg = ListToMap(cfg.level, "seq", "level")
    --self.level_cost_cfg = ListToMap(cfg.level, "cost_item_id")
    self.star_level_cfg = ListToMap(cfg.star_level, "seq", "star_level")
    self.star_cost_cfg = ListToMap(cfg.star_level, "cost_item_id")
	self.replace_cost_cfg = ListToMap(cfg.star_level, "replace_item_id")
    --self.fetter_cfg = ListToMap(cfg.fetter, "seq")
	self.uplevel_item_cfg = cfg.up_level_item
	self.skill_cfg = ListToMap(cfg.skill, "seq", "skill_level")
	self.skill_seq_cfg = ListToMapList(cfg.skill, "seq")
	self.skill_id_cfg = ListToMap(cfg.skill, "skill_id")
	self.awake_cfg = ListToMap(cfg.awake, "seq", "awake_level")
	self.apply_cfg = ListToMap(cfg.apply, "seq")
	self.awake_skill_cfg = ListToMap(cfg.awake_skill, "seq", "skill_level")

	self.favor_level_cfg = ListToMap(cfg.favor, "seq", "favor_level")
	self.gift_cfg = ListToMap(cfg.gift, "item_id")

	self.travel_cfg = cfg.travel --同游配置
	self.travel_seq_cfg = ListToMap(cfg.travel, "scene_seq")
	self.travel_cost_cfg = ListToMap(cfg.travel, "consume_item") 
	self.travel_reward_cfg = ListToMap(cfg.travel_reward, "scene_seq", "seq") 
	self.travel_scene_cfg = ListToMap(cfg.travel_scene, "scene_seq", "seq") --同游地区解锁配置

    self.artifact_item_list = {}
    RemindManager.Instance:Register(RemindName.Artifact, BindTool.Bind(self.GetArtifacRemind, self))
	RemindManager.Instance:Register(RemindName.ArtifactUpLevel, BindTool.Bind(self.GetArtifacUpLevelRemind, self))
	RemindManager.Instance:Register(RemindName.ArtifactUpStar, BindTool.Bind(self.GetArtifacUpStarRemind, self))
	RemindManager.Instance:Register(RemindName.ArtifactBattle, BindTool.Bind(self.GetArtifactBattleRemind, self))
	RemindManager.Instance:Register(RemindName.ArtifactAffection, BindTool.Bind(self.GetArtifactAffectionRemind, self))
	RemindManager.Instance:Register(RemindName.ArtifactTravel, BindTool.Bind(self.GetTravelRemind, self))
end

function ArtifactWGData:__delete()
	ArtifactWGData.Instance = nil

	self.artifact_item_list = nil
	self.travel_select_artifact_seq = nil

	RemindManager.Instance:UnRegister(RemindName.Artifact)
	RemindManager.Instance:UnRegister(RemindName.ArtifactUpLevel)
	RemindManager.Instance:UnRegister(RemindName.ArtifactUpStar)
	RemindManager.Instance:UnRegister(RemindName.ArtifactBattle)
	RemindManager.Instance:UnRegister(RemindName.ArtifactTravel)
end

----协议数据
function ArtifactWGData:SetArtifactItemAllInfo(protocol)
	self.artifact_item_list = protocol.artifact_item_list
end

--单个信息变化
function ArtifactWGData:ArtifactItemUpdateInfo(protocol)
	local artifact_seq = protocol.artifact_seq
	local artifact_item = protocol.artifact_item

	self.artifact_item_list[artifact_seq] = artifact_item
end

-- 羁绊信息
function ArtifactWGData:SetArtifactApplyInfo(protocol)
	self.apply_list = protocol.apply_list
end

-- 获取出战信息
function ArtifactWGData:GetArtifactBattleInfo()
	return self.apply_list or {}
end

-- 获取某个是否出战
function ArtifactWGData:GetArtifactBattleBySeq(seq)
	if not self.apply_list then
		return false
	end
	for i, v in ipairs(self.apply_list) do
		if v.seq == seq then
			return true
		end
	end
	return false
end

-- 好感信息
function ArtifactWGData:SetArtifactFavorInfo(protocol)
	self.send_gift_count = protocol.send_gift_count
end

-- 获取可赠礼物次数
function ArtifactWGData:GetSendGiftCount()
	return self.send_gift_count or 0
end

-- 同游信息
function ArtifactWGData:SetArtifactTravelInfo(protocol)
	self.travel_item_list = protocol.travel_item_list
end

-- 同游信息
function ArtifactWGData:GetArtifactTravelInfo(artifact_seq)
	return (self.travel_item_list or {})[artifact_seq]
end

-- 同游信息
function ArtifactWGData:GetArtifactTravelListByScene(scene_seq)
	if not self.travel_item_list then
		return {}
	end
	local data_list = {}
	for k, v in pairs(self.travel_item_list) do
		local data = {}
		if scene_seq == v.scene_seq then
			table.insert(data_list, v)
		end
	end
	return data_list
end

-- 礼包是否可领
function ArtifactWGData:GetCanGetGiftBySeq(seq)
	local artifact_data = self:GetArtifactItemDataBySeq(seq)
	if artifact_data == nil then
		return false
	end
	return artifact_data.level ~= 0 and artifact_data.gift_reward_flag == 1
end

--获取神器数据byseq
function ArtifactWGData:GetArtifactItemDataBySeq(artifact_seq)
	return self.artifact_item_list[artifact_seq]
end

--获取羁绊数据byseq[废弃]
function ArtifactWGData:GetFetterDataBySeq(seq)
	return 1 --self.fetter_active_flag[seq]
end

--获取类型配置[废弃]
function ArtifactWGData:GetArtifactTypeCfg()
	return self.artifact_type_cfg
end

--根据类型获取对应信息[废弃]
function ArtifactWGData:GetShowListInfoByType(artifact_type)
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local show_list = self.artifact_type_cfg[artifact_type] or {}
	
	return show_list
end

--获取神器配置
function ArtifactWGData:GetArtifactCfgList()
	return self.artifact_cfg or {}
end

--获取神器配置根据seq
function ArtifactWGData:GetArtifactCfgBySeq(artifact_seq)
	return self.artifact_seq_cfg[artifact_seq]
end

--获取神器配置根据ItemId
function ArtifactWGData:GetArtifactCfgByItemId(item_id)
	return self.artifact_item_cfg[item_id]
end

-- 获取双修的Spine资源id
function ArtifactWGData:GetArtifactModelResId(item_id)
	local artifact_cfg = self:GetArtifactCfgByItemId(item_id)
	if artifact_cfg then
		return artifact_cfg.model_id
	end

	return 0
end

--获取等级配置
function ArtifactWGData:GetArtifactLevelCfg(artifact_seq,  level)
	return (self.level_cfg[artifact_seq] or {})[level]
end

--获取星级配置
function ArtifactWGData:GetArtifactStarCfg(artifact_seq,  star_level)
	return (self.star_level_cfg[artifact_seq] or {})[star_level]
end

--获取技能配置
function ArtifactWGData:GetArtifactSkillCfg(artifact_seq,  star_level)
	local skill_cfg_list = self.skill_cfg[artifact_seq] or {}
	local cur_cfg
	local cur_skill_level = 1
	for i, v in ipairs(skill_cfg_list) do
		if star_level >= v.star_level then
			cur_cfg = v
			cur_skill_level = i
		else
			break
		end
	end
	return cur_cfg, cur_skill_level
end

--获取技能配置列表
function ArtifactWGData:GetArtifactSkillCfgList(artifact_seq)
	return self.skill_seq_cfg[artifact_seq] or {}
end

function ArtifactWGData:GetSkillCfgBySkillLevel(artifact_seq,  skill_level)
	return (self.skill_cfg[artifact_seq] or {})[skill_level]
end

--获取技能配置by id
function ArtifactWGData:GetArtifactSkillCfgById(skill_id)
	return self.skill_id_cfg[skill_id]
end

--觉醒技能配置
function ArtifactWGData:GetAwakeSkillCfgList(artifact_seq)
	return self.awake_skill_cfg[artifact_seq] or {}
end

function ArtifactWGData:GetAwakeSkillCfgByLevel(artifact_seq,  skill_level)
	return (self.awake_skill_cfg[artifact_seq] or {})[skill_level]
end

--获取觉醒配置
function ArtifactWGData:GetArtifactAwakeCfg(artifact_seq,  awake_level)
	return (self.awake_cfg[artifact_seq] or {})[awake_level]
end

--获取觉醒名称
function ArtifactWGData:GetArtifactAwakeName(artifact_seq, awake_level)
	local awake_cfg = self:GetArtifactAwakeCfg(artifact_seq, awake_level)
	return awake_cfg and awake_cfg.awake_name or ""
end

-- 出战配置
function ArtifactWGData:GetArtifactApplyCfgBySeq(seq)
	return self.apply_cfg[seq] or {}
end

--等级消耗
function ArtifactWGData:GetLevelCostCfg(stuff_id)
	for i, v in ipairs(self.uplevel_item_cfg) do
		if v.item_id == stuff_id then
			return true
		end
	end
	return false
end

--星级消耗
function ArtifactWGData:GetStarCostCfg(stuff_id)
    return self.star_cost_cfg[stuff_id] ~= nil
end

--升星替代消耗
function ArtifactWGData:GetStarReplaceCostCfg(stuff_id)
    return self.replace_cost_cfg[stuff_id] ~= nil
end

--羁绊配置
-- function ArtifactWGData:GetFetterCfg(seq)
--     return self.fetter_cfg[seq]
-- end

--升级道具配置
function ArtifactWGData:GetUplevelItemCfg()
    return self.uplevel_item_cfg or {}
end

--是否达到升星条件[废弃]
function ArtifactWGData:IsCanUpStar(artifact_seq)
	local artifact_info = self.artifact_item_list[artifact_seq]
	if not artifact_info then
		return false
	end


	local cur_level = artifact_info.level
	local cur_star = artifact_info.star_level

	local cur_star_cfg = self:GetArtifactStarCfg(artifact_seq, cur_star)
    local next_star_cfg = self:GetArtifactStarCfg(artifact_seq, cur_star + 1)
    if not next_star_cfg or not cur_star_cfg then
    	return false
    end

    return false --cur_level >= cur_star_cfg.need_level
end

-- 获取当前索引所有属性（等级 + 星级）
-- 修改 升级升星不再相互影响; up_type: 1：升级 2：升星 3:觉醒
function ArtifactWGData:GetArtifactAllAttrList(artifact_seq, level, star_level, awake_level, up_type)
	local all_attr_list = {}
	--local is_can_star = self:IsCanUpStar(artifact_seq)
	local next_up_type = up_type or 1
	local cur_level_cfg = self:GetArtifactLevelCfg(artifact_seq, level)
	local next_level_cfg = self:GetArtifactLevelCfg(artifact_seq, level + 1)
	local attr_level_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_level_cfg, next_level_cfg, "attr_id", "attr_value", 1, 7)

	local cur_star_cfg = self:GetArtifactStarCfg(artifact_seq, star_level)
    local next_star_cfg = self:GetArtifactStarCfg(artifact_seq, star_level + 1)
    local attr_star_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_star_cfg, next_star_cfg, "attr_id", "attr_value", 1, 7)

	local cur_awake_cfg = self:GetArtifactAwakeCfg(artifact_seq, awake_level)
    local next_awake_cfg = self:GetArtifactAwakeCfg(artifact_seq, awake_level + 1)
    local attr_awake_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_awake_cfg, next_awake_cfg, "attr_id", "attr_value", 1, 7)

    --判断总属性是否存在该属性类型
    local is_new_attr = function (attr_tab)
    	for k, v in pairs(all_attr_list) do
    		if attr_tab.attr_str == v.attr_str then
    			return false, v
    		end
    	end

    	return true
    end

	local add_attr = function (attr_tab, add_type) -- 1等级属性 2星级属性
		local new_attr, same_attr = is_new_attr(attr_tab)
    	if new_attr then
    		local data = {
				attr_str = attr_tab.attr_str,
	            attr_value = attr_tab.attr_value,
	            -- add_value = add_type == 1 and (is_can_star and 0 or attr_tab.add_value) or (is_can_star and attr_tab.add_value or 0)
				add_value = add_type == next_up_type and attr_tab.add_value or 0
			}

			table.insert(all_attr_list, data)
    	else
    		same_attr.attr_value = same_attr.attr_value + attr_tab.attr_value
    		-- same_attr.add_value = add_type == 1 and (is_can_star and same_attr.add_value or attr_tab.add_value) or (is_can_star and attr_tab.add_value or same_attr.add_value)
			same_attr.add_value = add_type == next_up_type and attr_tab.add_value or same_attr.add_value
    	end
    end

     --插入等级属性
    for k, v in pairs(attr_level_list) do
    	add_attr(v, 1)
    end

    --插入星级属性
    for k, v in pairs(attr_star_list) do
		add_attr(v, 2)
    end

	--插入觉醒属性
	for k, v in pairs(attr_awake_list) do
		add_attr(v, 3)
	end

    return all_attr_list
end

--战力
function ArtifactWGData:GetArtifactCapabilityBySeq(artifact_seq, is_preview_cap)
	local artifact_cfg = self:GetArtifactCfgBySeq(artifact_seq)
	local cur_artifact_data = self:GetArtifactItemDataBySeq(artifact_seq)
	if IsEmptyTable(artifact_cfg) or IsEmptyTable(cur_artifact_data) then
		return 0
	end

	local capability = 0
	local attr_list = AttributePool.AllocAttribute()
	local all_attr
	if is_preview_cap then
		all_attr = self:GetArtifactAllAttrList(artifact_seq, 1, 0, 0)
	else
		all_attr = self:GetArtifactAllAttrList(artifact_seq, cur_artifact_data.level, cur_artifact_data.star_level, cur_artifact_data.awake_level)
	end

	for k, v in pairs(all_attr) do
		attr_list[v.attr_str] = attr_list[v.attr_str] + v.attr_value
	end

	capability = AttributeMgr.GetCapability(attr_list)

	return capability
end

--[[
--类型选择
function ArtifactWGData:GetSelectTypeIndex()
	local index = 0
	local show_info = self:GetArtifactTypeCfg()
	if not IsEmptyTable(show_info) then
		for i = 0, #show_info do
			for k, data in pairs(show_info[i]) do
				if ArtifactWGData.Instance:ArtifactRemindBySeq(data.seq) then
					return i
				end
			end
		end
	end

	return index
end
]]

function ArtifactWGData:MingQiIsActiveById(item_id)
	local cfg = self.artifact_item_cfg[item_id]
	if cfg then
		local data = self.artifact_item_list[cfg.seq]
		local level = data and data.level or 0
		return level > 0
	end

	return false
end

-- 获取可出战列表
function ArtifactWGData:GetArtifactBattleList()
	if not self.apply_list then
		return
	end
	local battle_list = {}
	local artifact_cfg = self:GetArtifactCfgList()
	for i, v in ipairs(artifact_cfg) do
		local is_battle = self:GetArtifactBattleBySeq(v.seq)
		local artifact_data = self:GetArtifactItemDataBySeq(v.seq)
		if artifact_data and artifact_data.level > 0 and not is_battle then
			table.insert(battle_list, v)
		end
	end
	return battle_list
end

-- 出战类型计数
function ArtifactWGData:GetBattleTypeCountList()
	local type_list = {}
	local artifact_cfg = self:GetArtifactCfgList()
	for i, v in ipairs(artifact_cfg) do
		local is_battle = self:GetArtifactBattleBySeq(v.seq)
		if is_battle then
			if not type_list[v.battle_type] then
				type_list[v.battle_type] = 1
			else
				type_list[v.battle_type] = type_list[v.battle_type] + 1
			end
		end
	end
	return type_list
end

-- 缓存出战槽位选择
function ArtifactWGData:GetSetSelectedPosSeq(battle_pos_seq)
	if battle_pos_seq then
		self.battle_pos_seq = battle_pos_seq
	else
		return self.battle_pos_seq
	end
end

function ArtifactWGData:GetGiftItemList(artifact_seq)
	local gift_item_list = {}
	for k, v in pairs(self.gift_cfg) do
		local data = {}
		data.item_id = v.item_id
		data.base_favor = v.base_favor
		data.exclusive_add = v.exclusive_add
		data.num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
		data.is_bind = 1
		data.favor_seq_list = {}
		data.sort = 0
		if v.exclusive_add_seq and v.exclusive_add_seq ~= "" then
			local seq_list = Split(v.exclusive_add_seq, ",")
			for i, seq in ipairs(seq_list) do
				local favort_seq = tonumber(seq)
				data.favor_seq_list[favort_seq] = true
				if favort_seq == artifact_seq then
					data.is_favor = true
				end
			end
		end
		data.sort = (data.num > 0 and 10000000 or 0) + (data.is_favor and 1000000 or 0) + v.item_id
		table.insert(gift_item_list, data)
	end
	table.sort(gift_item_list, SortTools.KeyUpperSorter("sort"))
	return gift_item_list
end

function ArtifactWGData:GetSendGiftItemCfg(item_id)
    return self.gift_cfg[item_id]
end

function ArtifactWGData:GetIsSendGiftItem(item_id)
    return self.gift_cfg[item_id] ~= nil
end

function ArtifactWGData:GetGetSendGiftCount(artifact_seq)
	local max_count = self.other_cfg.max_send_gift_num or 0
	return self.send_gift_count or 0, max_count
end

function ArtifactWGData:GetAffectionGiftJumpPath(artifact_seq)
	return self.other_cfg.add_send_gift_jump_path or ""
end

function ArtifactWGData:GetAffectionLevelCfgList(artifact_seq)
	return self.favor_level_cfg[artifact_seq] or {}
end

function ArtifactWGData:GetMaxAffectionLevelCfg(artifact_seq)
	if not self.favor_level_cfg[artifact_seq] then
		return {}
	end
	local length = #self.favor_level_cfg[artifact_seq]
	return self.favor_level_cfg[artifact_seq][length]
end

function ArtifactWGData:GetAffectionLevelCfg(artifact_seq, favor_level)
	return (self.favor_level_cfg[artifact_seq] or {})[favor_level]
end

function ArtifactWGData:GetAffectionLevelRewardFlag(artifact_seq, favor_level)
	local artifact_data = self:GetArtifactItemDataBySeq(artifact_seq)
	if not artifact_data then
		return 0
	end
	return artifact_data.favor_reward_flag[favor_level] or 0
end

function ArtifactWGData:GetTravelCfgList()
	return self.travel_cfg
end

function ArtifactWGData:GetTravelSceneCfg(scene_seq)
	return self.travel_seq_cfg[scene_seq]
end

function ArtifactWGData:GetTravelRewardCfgList(scene_seq)
	return self.travel_reward_cfg[scene_seq] or {}
end

function ArtifactWGData:GetTravelRewardCfg(scene_seq, reward_seq)
	return (self.travel_reward_cfg[scene_seq] or {})[reward_seq]
end

function ArtifactWGData:SetTravelSelectArtifactSeq(artifact_seq)
	self.travel_select_artifact_seq = artifact_seq
end

function ArtifactWGData:GetTravelSelectArtifactSeq()
	return self.travel_select_artifact_seq
end

function ArtifactWGData:GetIsUnlockScene(artifact_seq, scene_seq)
	local cfg = (self.travel_scene_cfg[scene_seq] or {})[artifact_seq]
	local artifact_data = self:GetArtifactItemDataBySeq(artifact_seq)
	if not cfg or not artifact_data or artifact_data.level <= 0 then 
		return false 
	end
	return artifact_data.favor_level >= cfg.favor_level
end

-- 场景选择红点
function ArtifactWGData:GetSceneRemind(artifact_seq, scene_seq)
	local travel_info = self:GetArtifactTravelInfo(artifact_seq)
	if not travel_info then
		return false
	end
	--local server_time = TimeWGCtrl.Instance:GetServerTime()
	if scene_seq == travel_info.scene_seq and travel_info.reward_seq >= 0 and travel_info.fetch_falg == 1  then
		return true
	end
	return false
end

function ArtifactWGData:GetIsTravelCostItem(item_id)
    return self.travel_cost_cfg[item_id] ~= nil
end

-----------------------------红点-----------------

function ArtifactWGData:GetArtifacRemind()
	for k, v in pairs(self.artifact_seq_cfg) do
		if self:ArtifactRemindBySeq(v.seq) then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ARTIFACT, 1, function ()
	            ViewManager.Instance:Open(GuideModuleName.ArtifactView)
	            return true
       		end)

			return 1
		end
	end
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ARTIFACT, 0)

	if self:GetArtifactBattleRemind() > 0 then
		return 1
	end
	
	return 0
end

function ArtifactWGData:GetArtifacUpLevelRemind()
	for k, v in pairs(self.artifact_seq_cfg) do
		if self:GetArtifactUpLevelRemind(v.seq) then
			return 1
		end
	end
	return 0
end

function ArtifactWGData:GetArtifacUpStarRemind()
	for k, v in pairs(self.artifact_seq_cfg) do
		if self:GetArtifactUpStarRemind(v.seq) or self:GetArtifactAwakeRemind(v.seq) then
			return 1
		end
	end
	return 0
end

-- 出战红点
function ArtifactWGData:GetArtifactBattleRemind() 
	local battle_info = self:GetArtifactBattleInfo()
	for i, v in ipairs(battle_info) do
		if self:GetCanBattleRed(v.pos_seq) then
			return 1
		end
	end
	return 0
end

-- 出战位是否可出战
function ArtifactWGData:GetCanBattleRed(pos_seq)
	local battle_info_list = self:GetArtifactBattleInfo()
	local cur_battle_info = battle_info_list[pos_seq + 1]
	if not cur_battle_info then
		return false
	end
	if cur_battle_info.seq >= 0 then
		return false
	end

	local battle_count_list = self:GetBattleTypeCountList()
	local battle_artifact_list = self:GetArtifactBattleList()

	for i, v in ipairs(battle_artifact_list) do
		if not battle_count_list[v.battle_type] or battle_count_list[v.battle_type] <= 0 then
			return true
		end
	end
	return false
end

--一个神器红点
function ArtifactWGData:ArtifactRemindBySeq(artifact_seq) 
	local artifact_cfg = self:GetArtifactCfgBySeq(artifact_seq)
	if IsEmptyTable(artifact_cfg) then
		return false
	end

	if self:GetArtifactUpLevelRemind(artifact_seq) then
		return true
	end

	if self:GetArtifactUpStarRemind(artifact_seq) then
		return true
	end

	if self:GetArtifactAwakeRemind(artifact_seq) then
		return true
	end

	if self:GetCanGetGiftBySeq(artifact_seq) then
		return true
	end

	if self:GetArtifactAffectionSingleRemind(artifact_seq) then
		return true
	end

	return false
end

--升级红点
function ArtifactWGData:GetArtifactUpLevelRemind(artifact_seq) 
	local cur_artifact_data = self:GetArtifactItemDataBySeq(artifact_seq)
	if not cur_artifact_data then
		return false
	end

	local cur_level_cfg = self:GetArtifactLevelCfg(artifact_seq, cur_artifact_data.level)
	local next_level_cfg = self:GetArtifactLevelCfg(artifact_seq, cur_artifact_data.level + 1)

	if next_level_cfg and cur_level_cfg then
		-- 解锁
		if cur_artifact_data.level == 0 then
			local artifact_cfg = self:GetArtifactCfgBySeq(artifact_seq)
			local had_num = ItemWGData.Instance:GetItemNumInBagById(artifact_cfg.item_id)
			if had_num >= artifact_cfg.cost_item_num then
				return true
			end
		else
			local uplevel_item_cfg = ArtifactWGData.Instance:GetUplevelItemCfg()
			for i = 1, 3 do
				local item_count = ItemWGData.Instance:GetItemNumInBagById(uplevel_item_cfg[i].item_id)
				if item_count > 0 then 
					return true
				end
			end
		end
	end

	return false
end

--升星红点
function ArtifactWGData:GetArtifactUpStarRemind(artifact_seq) 
	local cur_artifact_data = self:GetArtifactItemDataBySeq(artifact_seq)
	if not cur_artifact_data or cur_artifact_data.level == 0 then
		return false
	end

	local cur_star_cfg = self:GetArtifactStarCfg(artifact_seq, cur_artifact_data.star_level)
	local next_star_cfg = self:GetArtifactStarCfg(artifact_seq, cur_artifact_data.star_level + 1)
	if next_star_cfg and cur_star_cfg then
		local had_num = ItemWGData.Instance:GetItemNumInBagById(cur_star_cfg.cost_item_id)
		if had_num >= cur_star_cfg.cost_item_num then
			return true
		end
		local replace_item_had_num = ItemWGData.Instance:GetItemNumInBagById(cur_star_cfg.replace_item_id)
		if replace_item_had_num >= cur_star_cfg.replace_item_cost then
			return true
		end
	end

	return false
end

--觉醒红点
function ArtifactWGData:GetArtifactAwakeRemind(artifact_seq) 
	local cur_artifact_data = self:GetArtifactItemDataBySeq(artifact_seq)
	if not cur_artifact_data or cur_artifact_data.level == 0 then
		return false
	end

	local cur_awake_cfg = self:GetArtifactAwakeCfg(artifact_seq, cur_artifact_data.awake_level)
	local next_awake_cfg = self:GetArtifactAwakeCfg(artifact_seq, cur_artifact_data.awake_level + 1)
	if next_awake_cfg and cur_awake_cfg then
		if cur_artifact_data.star_level >= next_awake_cfg.need_star then
			return true
		end
	end

	return false
end



-- 好感度红点 单个
function ArtifactWGData:GetArtifactAffectionSingleRemind(artifact_seq) 
	if self:GetArtifactTravelRemind(artifact_seq) then
		return true
	end

	if self:GetAffectionRewardSingleRemind(artifact_seq) then
		return true
	end

	-- 珍宝匣
	if StrangeCatalogWGData.Instance:GetBigTypeRemind(artifact_seq) then
		return true
	end

	return false
end

-- 双修同游红点 单个
function ArtifactWGData:GetArtifactTravelRemind(artifact_seq)
	local travel_info = self:GetArtifactTravelInfo(artifact_seq)
	if not travel_info then
		return false
	end
	if travel_info.fetch_falg == 1 then
		return true
	end
	return false
end

-- 好感度奖励红点 单个
function ArtifactWGData:GetAffectionRewardSingleRemind(artifact_seq)
	local artifact_data = self:GetArtifactItemDataBySeq(artifact_seq)

	for i, v in ipairs(artifact_data.favor_reward_flag) do
		if v == 0 and i <= artifact_data.favor_level then
			return true
		end
	end
	return false
end

function ArtifactWGData:GetArtifactAffectionRemind()
	local remind_num = RemindManager.Instance:GetRemind(RemindName.ArtifactTravel)
	if remind_num > 0 then
		return 1
	end

	remind_num = RemindManager.Instance:GetRemind(RemindName.StrangeCatalog)
	if remind_num > 0 then
		return 1
	end
	
	if self:GetAffectionRewardRemind() > 0 then
		return 1
	end

	return 0
end


-- 双修同游功能红点
function ArtifactWGData:GetTravelRemind()
	local artifact_cfg_list = self:GetArtifactCfgList()
	for i, v in ipairs(artifact_cfg_list) do
		if self:GetArtifactTravelRemind(v.seq) then
			return 1
		end
	end
	return 0
end

-- 双修奖励红点
function ArtifactWGData:GetAffectionRewardRemind()
	local artifact_cfg_list = self:GetArtifactCfgList()
	for i, v in ipairs(artifact_cfg_list) do
		if self:GetAffectionRewardSingleRemind(v.seq) then
			return 1
		end
	end
	return 0
end