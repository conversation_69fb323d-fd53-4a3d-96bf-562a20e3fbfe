﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_AssetBundleFileInfoWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(Nirvana.AssetBundleFileInfo), typeof(System.Object));
		<PERSON><PERSON>unction("GetSize", GetSize);
		<PERSON><PERSON>unction("New", _CreateNirvana_AssetBundleFileInfo);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateNirvana_AssetBundleFileInfo(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				Nirvana.AssetBundleFileInfo obj = new Nirvana.AssetBundleFileInfo();
				ToLua.PushSealed(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: Nirvana.AssetBundleFileInfo.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetSize(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.AssetBundleFileInfo obj = (Nirvana.AssetBundleFileInfo)ToLua.CheckObject(L, 1, typeof(Nirvana.AssetBundleFileInfo));
			string arg0 = ToLua.CheckString(L, 2);
			int o = obj.GetSize(arg0);
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

