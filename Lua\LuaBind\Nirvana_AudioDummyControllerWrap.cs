﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_AudioDummyControllerWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(Nirvana.AudioDummyController), typeof(System.Object));
		<PERSON><PERSON>RegFunction("WaitFinish", WaitFinish);
		<PERSON><PERSON>RegFunction("Stop", Stop);
		<PERSON><PERSON>RegFunction("SetPosition", SetPosition);
		L.RegFunction("SetTransform", SetTransform);
		L.RegFunction("Play", Play);
		<PERSON><PERSON>RegFunction("Update", Update);
		L.RegFunction("FinshAudio", FinshAudio);
		L.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON>.<PERSON>ar("IsPlaying", get_IsPlaying, null);
		<PERSON>.Reg<PERSON>ar("LeftTime", get_LeftTime, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int WaitFinish(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.AudioDummyController obj = (Nirvana.AudioDummyController)ToLua.CheckObject(L, 1, typeof(Nirvana.AudioDummyController));
			System.Collections.IEnumerator o = obj.WaitFinish();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Stop(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.AudioDummyController obj = (Nirvana.AudioDummyController)ToLua.CheckObject(L, 1, typeof(Nirvana.AudioDummyController));
			obj.Stop();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetPosition(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.AudioDummyController obj = (Nirvana.AudioDummyController)ToLua.CheckObject(L, 1, typeof(Nirvana.AudioDummyController));
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.SetPosition(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetTransform(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.AudioDummyController obj = (Nirvana.AudioDummyController)ToLua.CheckObject(L, 1, typeof(Nirvana.AudioDummyController));
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
			obj.SetTransform(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Play(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.AudioDummyController obj = (Nirvana.AudioDummyController)ToLua.CheckObject(L, 1, typeof(Nirvana.AudioDummyController));
			obj.Play();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Update(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.AudioDummyController obj = (Nirvana.AudioDummyController)ToLua.CheckObject(L, 1, typeof(Nirvana.AudioDummyController));
			obj.Update();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FinshAudio(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.AudioDummyController obj = (Nirvana.AudioDummyController)ToLua.CheckObject(L, 1, typeof(Nirvana.AudioDummyController));
			obj.FinshAudio();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsPlaying(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.AudioDummyController obj = (Nirvana.AudioDummyController)o;
			bool ret = obj.IsPlaying;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsPlaying on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_LeftTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.AudioDummyController obj = (Nirvana.AudioDummyController)o;
			float ret = obj.LeftTime;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index LeftTime on a nil value");
		}
	}
}

