-- 在 require_list.lua 添加需要require的 xxx_wg_ctrl文件
-- 在 modules_wg_ctrl.lua 添加需要创建的QTEWGCtrl
require("game/qte/qte_view")

QTEWGCtrl = QTEWGCtrl or BaseClass(BaseWGCtrl)
function QTEWGCtrl:__init()
	if QTEWGCtrl.Instance then
		error("[QTEWGCtrl]:Attempt to create singleton twice!")
	end
    -- 单例
	QTEWGCtrl.Instance = self

    -- 创建data
    -- self.data = XXXWGData.New()
    -- 创建view
    -- 一般上需要配置支持跳转的，才需添加对应的模块名GuideModuleName.XXXView
    -- 见 guide_config.lua 关注 GuideModuleName表 FunName表 TabIndex表
    self.view = QTEView.New(GuideModuleName.QTEView)

    -- 注册协议
    self:RegisterAllProtocols()
    -- 注册事件监听
    self:RegisterAllEvents()
end

function QTEWGCtrl:__delete()
    self:UnRegisterAllEvents()

    -- 销毁data
    -- self.data:DeleteMe()
	-- self.data = nil
    -- 销毁view
	self.view:DeleteMe()
	self.view = nil

    QTEWGCtrl.Instance = nil
end

-- 注册协议
function QTEWGCtrl:RegisterAllProtocols()
    -- self:RegisterProtocol(CSXXXReq)
    -- 接收协议 与对应方法名绑定
	self:RegisterProtocol(SCMonsterQTEInfo, "OnSCMonsterQTEInfo")
end

-- 注册事件监听
function QTEWGCtrl:RegisterAllEvents()

end

-- 注销事件监听
function QTEWGCtrl:UnRegisterAllEvents()

end


-- 操作请求
-- function QTEWGCtrl:SendOperateReq(operate_type, param_1, param_2)
-- 	local protocol = ProtocolPool.Instance:GetProtocol(CSXXXReq)
-- 	protocol.operate_type = operate_type or 0
-- 	protocol.param_1 = param_1 or 0
-- 	protocol.param_2 = param_2 or 0
-- 	protocol:EncodeAndSend()
-- end

function QTEWGCtrl:MonsterQte()
    RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_DO_MONSTER_QTE)
end

-- 接收协议
function QTEWGCtrl:OnSCMonsterQTEInfo(protocol)
    if protocol.qte_uuid == RoleWGData.Instance:GetUUid() then
        if protocol.qte_type == MONSTER_QTE_TYPE.MONSTER_QTE_TYPE_ESOTERICA then
            self:OpenView(protocol.qte_type)
        elseif protocol.qte_type == MONSTER_QTE_TYPE.MONSTER_QTE_TYPE_NEWPLAYER_FB then
            UnityEngine.Time.timeScale = 0.2
            self:OpenView(protocol.qte_type)
        end
    end
end

-- 刷新界面
function QTEWGCtrl:FlushView(index, key, param_t)
	if self.view:IsOpen() then
		self.view:Flush(index, key, param_t)
	end
end

-- 打开界面
function QTEWGCtrl:OpenView(type)
    -- 直接调打开
    self.view:SetQTEType(type)
    self.view:Open()
end