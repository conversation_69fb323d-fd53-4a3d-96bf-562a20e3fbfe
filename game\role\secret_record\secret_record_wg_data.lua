SecretRecordWGData = SecretRecordWGData or BaseClass()

function SecretRecordWGData:__init()
	if SecretRecordWGData.Instance then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[SecretRecordWGData] Attempt to create singleton twice!")
		return
	end

	SecretRecordWGData.Instance = self

    self.bottle_info = {}
    self.capability_list = {}
    self.item_list = {}
    self.total_capability = 0
    self:InitConfig()
    -- 红点注册
	RemindManager.Instance:Register(RemindName.SecretRecord, BindTool.Bind(self.GetSecretRecordRemind, self))
end

function SecretRecordWGData:InitConfig()
    self.level_cfg = ConfigManager.Instance:GetAutoConfig("dailywork_auto").level
	self.cap_sort_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("zi_yun_mi_lu_cfg_auto").cap_sort, "index")
end

function SecretRecordWGData:__delete()
    SecretRecordWGData.Instance = nil
    RemindManager.Instance:UnRegister(RemindName.SecretRecord)
end

function SecretRecordWGData:GetCapSortCfgByIndex(index)
	return self.cap_sort_cfg[index]
end

function SecretRecordWGData:SetBottleInfo(info)
	self.bottle_info = info or {}
end

function SecretRecordWGData:SetSecretRecordInfo(protocol)
    self.total_capability = protocol.show_total_capability
	local capability_list = protocol.show_capability_list
    self.item_list = protocol.show_item_list

	self.capability_list = {}
	for k, v in pairs(capability_list) do
		local data = {}
		local cfg = self:GetCapSortCfgByIndex(k)
		data.cap = v
		data.sort = cfg.sort
		data.name = cfg.name
		table.insert(self.capability_list, data)
	end

	table.sort(self.capability_list, SortTools.KeyLowerSorter("sort"))
end

function SecretRecordWGData:GetTotalCapability()
	return self.total_capability
end

function SecretRecordWGData:GetCapabilityList()
	return self.capability_list
end

function SecretRecordWGData:GetItemList()
	return self.item_list
end

function SecretRecordWGData:GetBottleInfo()
	return self.bottle_info
end

function SecretRecordWGData:SetLevelInfo(info)
	self.bottle_info.level = info.level
	self.bottle_info.exp = info.exp
end

function SecretRecordWGData:GetLevelCfgByLevel(level)
	local level_cfg = {}
	for k, v in ipairs(self.level_cfg) do
		if v.level == level then
			level_cfg = v
			break
		end
	end

	return level_cfg
end

function SecretRecordWGData:GetMaxLevel()
	return #(self.level_cfg or {}) - 1
end

function SecretRecordWGData:GetSecretRecordRemind()
	local num = 0
	local is_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.SecretRecordView)
	if not is_open then

		return num
	end

	local level_info
	local bottle_info = self:GetBottleInfo()
	if bottle_info then
		level_info = self:GetLevelCfgByLevel(bottle_info.level)
	end
	if bottle_info and level_info and bottle_info.exp and level_info.need_exp and bottle_info.level < self:GetMaxLevel() then
		if bottle_info.exp >= level_info.need_exp then
			num = 1
		end
	end

	return num
end