-- X-新节日活动-节日祈愿.xls
local item_table={
[1]={item_id=28665,num=5,is_bind=1},
[2]={item_id=44182,num=1,is_bind=1},
[3]={item_id=31741,num=1,is_bind=1},
[4]={item_id=31484,num=1,is_bind=1},
[5]={item_id=28666,num=3,is_bind=1},
[6]={item_id=31699,num=5,is_bind=1},
[7]={item_id=27762,num=2,is_bind=1},
[8]={item_id=31485,num=1,is_bind=1},
[9]={item_id=44183,num=1,is_bind=1},
[10]={item_id=31699,num=10,is_bind=1},
[11]={item_id=31701,num=1,is_bind=1},
[12]={item_id=31486,num=1,is_bind=1},
[13]={item_id=27763,num=2,is_bind=1},
[14]={item_id=31699,num=15,is_bind=1},
[15]={item_id=27763,num=3,is_bind=1},
[16]={item_id=31487,num=1,is_bind=1},
[17]={item_id=28666,num=10,is_bind=1},
[18]={item_id=31699,num=20,is_bind=1},
[19]={item_id=44184,num=1,is_bind=1},
[20]={item_id=27762,num=3,is_bind=1},
[21]={item_id=31721,num=1,is_bind=1},
[22]={item_id=31488,num=1,is_bind=1},
[23]={item_id=27764,num=1,is_bind=1},
[24]={item_id=31489,num=1,is_bind=1},
[25]={item_id=44185,num=1,is_bind=1},
[26]={item_id=22076,num=100,is_bind=1},
[27]={item_id=22793,num=1,is_bind=1},
[28]={item_id=37145,num=1,is_bind=1},
[29]={item_id=46066,num=80,is_bind=1},
[30]={item_id=46066,num=20,is_bind=1},
}

return {
open_day={
{}
},

open_day_meta_table_map={
},
prayer_reward={
{normal_reward={[0]=item_table[1]},advanced_reward={[0]=item_table[2]},},
{seq=1,prayer_count=100,advanced_reward={[0]=item_table[3],[1]=item_table[4]},},
{seq=2,prayer_count=150,normal_reward={[0]=item_table[5]},advanced_reward={[0]=item_table[2]},},
{seq=3,prayer_count=200,normal_reward={[0]=item_table[6]},},
{seq=4,prayer_count=250,normal_reward={[0]=item_table[7]},advanced_reward={[0]=item_table[3],[1]=item_table[8]},},
{seq=5,prayer_count=300,advanced_reward={[0]=item_table[9]},},
{seq=6,prayer_count=350,normal_reward={[0]=item_table[10]},advanced_reward={[0]=item_table[11],[1]=item_table[12]},},
{seq=7,prayer_count=400,normal_reward={[0]=item_table[13]},},
{seq=8,prayer_count=450,normal_reward={[0]=item_table[14]},advanced_reward={[0]=item_table[9]},},
{seq=9,prayer_count=500,advanced_reward={[0]=item_table[11]},},
{seq=10,prayer_count=550,normal_reward={[0]=item_table[15]},advanced_reward={[0]=item_table[16]},},
{seq=11,prayer_count=600,normal_reward={[0]=item_table[17]},},
{seq=12,prayer_count=650,normal_reward={[0]=item_table[18]},advanced_reward={[0]=item_table[19]},},
{seq=13,prayer_count=700,normal_reward={[0]=item_table[20]},},
{seq=14,prayer_count=750,normal_reward={[0]=item_table[17]},advanced_reward={[0]=item_table[21],[1]=item_table[22]},},
{seq=15,prayer_count=800,},
{seq=16,prayer_count=850,normal_reward={[0]=item_table[23]},},
{seq=17,prayer_count=900,normal_reward={[0]=item_table[18]},advanced_reward={[0]=item_table[21],[1]=item_table[24]},},
{seq=18,prayer_count=950,normal_reward={[0]=item_table[23]},advanced_reward={[0]=item_table[25],[1]=item_table[26]},},
{seq=19,prayer_count=1000,advanced_reward={[0]=item_table[27],[1]=item_table[28]},guding_mark=1,}
},

prayer_reward_meta_table_map={
[16]=13,	-- depth:1
},
prayer_buy={
{discount=8,},
{seq=1,prayer_num=100,lingyu_num=6000,discount=8,},
{seq=2,prayer_num=150,lingyu_num=9000,},
{seq=3,prayer_num=200,lingyu_num=12000,},
{seq=4,prayer_num=350,lingyu_num=20000,},
{seq=5,prayer_num=500,lingyu_num=30000,}
},

prayer_buy_meta_table_map={
},
task_list={
{param1=4,},
{task_id=1,task_type=2,param1=1,task_description="参与击杀猎龙魔渊BOSS",open_panel="WorldServer#world_new_shenyuan_boss",},
{task_id=2,task_type=3,param1=10,task_description="击杀仙遗洞天BOSS",open_panel="boss#boss_vip",},
{task_id=3,task_type=5,param1=5,task_description="参与竞技场",open_panel="act_jjc",},
{task_id=4,task_type=7,task_description="完成双倍护送",open_panel="bizuo#bizuo_act_hall",},
{task_id=5,task_type=22,param1=6,task_description="充值6元",open_panel="recharge",},
{task_id=6,task_type=23,param1=10000,task_description="消耗10000仙玉",open_panel="shop",},
{task_id=7,task_type=10,param1=2,task_description="完成荒古神冢副本",open_panel="fubenpanel#fubenpanel_copper",},
{task_id=8,task_type=11,task_description="完成蓬莱仙境副本",open_panel="fubenpanel#fubenpanel_pet",},
{task_id=9,task_type=14,task_description="完成经验副本",open_panel="fubenpanel#fubenpanel_exp",},
{task_id=10,prayer_num=80,reset_type=1,task_type=21,task_description="连续充值3天",reward_list={[0]=item_table[29]},open_panel="recharge",},
{task_id=11,task_type=20,task_description="活动期间登录3天",open_panel="",},
{task_id=12,prayer_num=80,reset_type=1,param1=12,reward_list={[0]=item_table[29]},},
{task_id=13,task_type=2,task_description="参与击杀猎龙魔渊BOSS",open_panel="WorldServer#world_new_shenyuan_boss",},
{task_id=14,prayer_num=80,reset_type=1,task_type=3,param1=25,task_description="击杀仙遗洞天BOSS",reward_list={[0]=item_table[29]},open_panel="boss#boss_vip",}
},

task_list_meta_table_map={
[9]=8,	-- depth:1
[10]=8,	-- depth:1
[12]=11,	-- depth:1
[14]=11,	-- depth:1
},
model_show={
[1]={grade=1,}
},

model_show_meta_table_map={
},
open_day_default_table={start_day=1,end_day=9999,grade=1,rmb_type=182,rmb_seq=1,price=3960,},

prayer_reward_default_table={grade=1,seq=0,prayer_count=50,normal_reward={[0]=item_table[26]},advanced_reward={[0]=item_table[26]},guding_mark=0,},

prayer_buy_default_table={grade=1,seq=0,prayer_num=50,lingyu_num=3000,discount=7,limit_num=8,},

task_list_default_table={grade=1,task_id=0,prayer_num=20,level_limit=1,open_day_limit=1,reset_type=0,task_type=1,param1=3,param2=0,task_description="击杀猎魔深渊BOSS",reward_list={[0]=item_table[30]},open_panel="boss#boss_world",act_type="",desc_color="#60352b",},

model_show_default_table={grade=1,model_show_type=1,model_bundle_name="",model_asset_name="",model_show_itemid=37145,display_pos="460|-20",rotation="-1|35|-3",display_scale=1,display_pos_1="485|150",rotation_1="0|25|0",display_scale_1=1,}

}

