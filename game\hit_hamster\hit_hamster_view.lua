HitHamsterView = HitHamsterView or BaseClass(SafeBaseView)
local START_CHECK_TIME = 0.1    -- 每0.2秒检测一次

local HIT_COLOR = {
	COMBO_1 		= "#33ffb0",				-- 20连击以下
	COMBO_2 		= "#fff714",				-- 50连击以下
	COMBO_3		    = "#ffaa34",				-- 50连击往上
    COMBO_4		    = "#f97878",				-- 50连击往上
}

local MOLE_TYPE = {
    NORMAL = 1,         --普通
    HIGHT = 2,          --高分
    BOMB = 3,           --炸弹
    COOL = 4,           --冰冻
    SCARE = 5,          --惊吓
    BOX = 6,            --宝箱
}

function HitHamsterView:__init(view_name)
    self.view_style = ViewStyle.Full
    self:SetMaskBg(false)

    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
    self:AddViewResource(0, "uis/view/hit_hamster_prefab", "layout_hit_hamster")
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_light_common_top_panel")
end

function HitHamsterView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.HitHamster.Title
	-- 这里切换背景
    local bg_str = "a3_dds_bg_1"
	local bundle, asset = ResPath.GetRawImagesPNG(bg_str)
	if bundle and asset then
		self.node_list.RawImage_tongyong.raw_image:LoadSprite(bundle, asset, function ()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

	if not self.hit_hamster_rank_list then
	    self.hit_hamster_rank_list = AsyncListView.New(HamsterRankRender, self.node_list.hit_hamster_rank_list)
    end

    if not self.my_hit_hamster_rank then
        self.my_hit_hamster_rank = HamsterRankRender.New(self.node_list.my_hit_hamster_rank)
    end

    if self.hit_hamster_hole_list == nil then
        self.hit_hamster_hole_list = {}

        for i = 1, 9 do
            local cell_obj = self.node_list.hole_root_3x3:FindObj(string.format("hole_0%d", i))
            if cell_obj then
                local cell = HamsterHoleRender.New(cell_obj)
                cell:SetClickCallBack(BindTool.Bind1(self.OnSelectHamsterHole, self))
                cell:SetIndex(i)
                self.hit_hamster_hole_list[i] = cell
            end
        end
    end

    if self.hit_time_reward_list == nil then
        self.hit_time_reward_list = {}

        for i = 1, 3 do
            local cell_obj = self.node_list.hit_hamster_times_reward_root:FindObj(string.format("times_reward_0%d", i))
            if cell_obj then
                local cell = HamsterTimeRewardRender.New(cell_obj)
                cell:SetClickCallBack(BindTool.Bind1(self.OnSelectTimeReward, self))
                cell:SetIndex(i)
                self.hit_time_reward_list[i] = cell
            end
        end
    end

    if not self.time_reward_list then
        self.time_reward_list = AsyncListView.New(ItemCell, self.node_list.time_reward_list)
        self.time_reward_list:SetStartZeroIndex(true)
    end

    self.percent_txt_tween = self.node_list.percent_txt:GetComponent(typeof(UGUITweenScale))
    self.combo_txt_tween = self.node_list.combo_txt:GetComponent(typeof(UGUITweenScale))

    XUI.AddClickEventListener(self.node_list.start_game_btn, BindTool.Bind(self.OnClickStartGame, self))
    XUI.AddClickEventListener(self.node_list.hit_hamster_rank_btn, BindTool.Bind(self.OnClickShowRankView, self))
    XUI.AddClickEventListener(self.node_list.re_start_game_btn, BindTool.Bind(self.OnClickResetStartGame, self))
    XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind(self.OnClickCloseWindow, self))
    XUI.AddClickEventListener(self.node_list.close_show_time_reward, BindTool.Bind(self.OnClickShowTimeRewardList, self))
    HitHamsterWGCtrl.Instance:SendWhackMoleGameRank()
    Runner.Instance:AddRunObj(self, 8)

    self.node_list.desc_start_game_btn.text.text = Language.HitHamster.DescStartBtnTip
end

function HitHamsterView:ReleaseCallBack()
	if self.hit_hamster_rank_list then
        self.hit_hamster_rank_list:DeleteMe()
        self.hit_hamster_rank_list = nil
    end

    if self.my_hit_hamster_rank then
        self.my_hit_hamster_rank:DeleteMe()
        self.my_hit_hamster_rank = nil
    end

    if self.time_reward_list then
        self.time_reward_list:DeleteMe()
        self.time_reward_list = nil
    end

    if self.hit_hamster_hole_list and #self.hit_hamster_hole_list > 0 then
        for i, hamster_hole_cell in ipairs(self.hit_hamster_hole_list) do
            hamster_hole_cell:DeleteMe()
            hamster_hole_cell = nil
        end

        self.hit_hamster_hole_list = nil
    end 

    if self.hit_time_reward_list and #self.hit_time_reward_list > 0 then
        for i, time_reward_cell in ipairs(self.hit_time_reward_list) do
            time_reward_cell:DeleteMe()
            time_reward_cell = nil
        end

        self.hit_time_reward_list = nil
    end 
    
    if self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end	

    self.is_game_satrt = nil
    self.is_game_stop = nil
    self.game_time = nil
    self.stop_time = nil
    self.combo_status = nil
    self.percent_txt_tween = nil
    self.combo_txt_tween = nil
    self.hit_times = nil
    self.score_add = nil
    self:CleanActTimeDown()
    self:RemoveScareDelayTimer()
    Runner.Instance:RemoveRunObj(self)
end

-- 关闭前调用
function HitHamsterView:CloseCallBack()
    if self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end	

    if self.is_game_satrt then
        self:FlushGameEnd(true)
        HitHamsterWGCtrl.Instance:SendWhackMoleGameEnd()
    end

    self.show_master_rank_anim = false
end
----------------------------------------------------
-- 地鼠坑点击
function HitHamsterView:OnSelectHamsterHole(hole_cell)
	if nil == hole_cell or nil == hole_cell.data then
		return
	end

    if self.is_game_stop then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.HitHamster.GameCooled)
        return
    end

    local mole_cfg = HitHamsterWGData.Instance:GetAllMoleCfgById(hole_cell.data.id)
    if (not mole_cfg) then
        return
    end

    hole_cell:PlayerTouch()

    if hole_cell:CheckTouchNumForServer() then
        -- 当前的地鼠点击执行操作
        if hole_cell.data.id == MOLE_TYPE.SCARE then    --惊吓回洞
            self:RemoveScareDelayTimer()
            local change_ui_time = mole_cfg and (mole_cfg.delay_ms / 1000) or 0.4
            
            self.scare_timer = GlobalTimerQuest:AddDelayTimer(function ()
                self:ScareAllMole(hole_cell)
            end, change_ui_time)
        elseif hole_cell.data.id == MOLE_TYPE.COOL then
            self.is_game_stop = true
            self.stop_time = self.stop_time + (mole_cfg.control_ms / 1000) 
        end

        if self.is_experience then
            HitHamsterWGData.Instance:SetWhackMoleExperienceScore(hole_cell.data.id, self.is_experience)
            self:Flush(0, "score")
        else
            HitHamsterWGCtrl.Instance:SendWhackMoleGameTouch(hole_cell.data.index)
        end

        hole_cell:ExecuteExitAction(true, self.is_experience, self.node_list.score_txt.transform, self.node_list.full_effect_root.transform)
    end
end

-- 地鼠坑点击
function HitHamsterView:OnSelectTimeReward(time_reward_cell)
    if nil == time_reward_cell or nil == time_reward_cell.data then
		return
	end

    self.time_reward_list:SetDataList(time_reward_cell.data.reward_item)
    self.node_list.time_reward_list.transform.position = time_reward_cell.view.transform.position + Vector3(-13, -0.5, 0)
    self.node_list.close_show_time_reward:CustomSetActive(true)
end

-- 更新
function HitHamsterView:Update(now_time, elapse_time)
    if not self.is_game_satrt then
        return
    end

    if self.is_game_stop then   -- 游戏暂停
        self.stop_time = self.stop_time - elapse_time

        if self.stop_time <= 0 then
            self.is_game_stop = false
            self.stop_time = 0
        end
    end

	if self.check_time == nil then
		self.check_time = now_time + START_CHECK_TIME
		return
	end

    self.game_time = self.game_time + elapse_time
	if now_time > self.check_time then
		self.check_time = now_time + START_CHECK_TIME
        self:FlushCheckHoleUpdate(self.game_time)
        self:FlushGameTime()
	end
end
----------------------------------------------------
function HitHamsterView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
            self:FlushMessage()
            self:FlushGameEnd(true)
            self:FlushJoinReward()
        elseif k == "game_start" then
            self:FlushGameStart(v)
            self:FlushAllHoleMessage()
            self:FlushMessage()
        elseif k == "score" then  
            self:FlushMessage()
        elseif k == "rank" then  
            self:FlushGameRankList()
        elseif k == "game_end" then
            self:FlushMessage()
            self:FlushGameEnd()
        elseif k == "times_reward" then
            self:FlushJoinReward()
        end
    end
end

-------------------------------------------------------------------------
-- 刷新界面信息
function HitHamsterView:FlushMessage()
    local score = HitHamsterWGData.Instance:GetWhackMoleFinalScore(self.is_experience) or 0
    local act_end_time = HitHamsterWGData.Instance:GetActTimeCount()
    self.node_list.score_txt.text.text = string.format(Language.HitHamster.Score, score)
    -- self:FlushTimeCountDown(end_time)
    self:FlushHitTimes()
    self:FlushActTimeCountDown(act_end_time)
end

-- 刷新连击次数
function HitHamsterView:FlushHitTimes()
    local curr_hit_times = HitHamsterWGData.Instance:GetWhackMoleFinalHitTimes(self.is_experience) or 0
    self:ExcuteComboTween(curr_hit_times > 1) 

    if curr_hit_times > 1 then
        local color = HIT_COLOR.COMBO_1
        if curr_hit_times > 9 and curr_hit_times <= 19 then
            color = HIT_COLOR.COMBO_2
        elseif curr_hit_times > 19 then
            color = HIT_COLOR.COMBO_3
        end

        local curr_score_add = HitHamsterWGData.Instance:GetWhackMoleHitAddition(self.is_experience)
        self.node_list.percent_txt:CustomSetActive(curr_score_add > 1)
        self.node_list.percent_txt.text.text = string.format(Language.HitHamster.Score2, color, string.format("X%s", curr_score_add))
        self.node_list.combo_txt.text.text = string.format(Language.HitHamster.Combo, color, curr_hit_times)

        if curr_hit_times ~= self.hit_times and self.combo_txt_tween then
            self.combo_txt_tween:ResetToBeginning()
            self.hit_times = curr_hit_times
        end
    
        if curr_score_add ~= self.score_add and self.percent_txt_tween then
            self.percent_txt_tween:ResetToBeginning()
            self.score_add = curr_score_add
        end
    end
end

-- 重置地鼠洞
function HitHamsterView:ResetAllHoleMessage()
    for index, hamster_hole_cell in ipairs(self.hit_hamster_hole_list) do
        hamster_hole_cell:SetData(nil)
    end
end

-- 刷新地鼠洞
function HitHamsterView:FlushAllHoleMessage()
    for index, hamster_hole_cell in ipairs(self.hit_hamster_hole_list) do
        local data = HitHamsterWGData.Instance:GetMoleByHole(index)
        hamster_hole_cell:SetData(data)
    end
end

-- 游戏开始
function HitHamsterView:FlushGameStart(param_data)
    self.is_experience = param_data.is_experience
    self.is_game_satrt = true
    self.is_game_stop = false
    self.game_time = 0
    self.stop_time = 0
    self:ExcuteTweenAnim(true)
    self.node_list.start_game_btn:CustomSetActive(false)
    self.node_list.re_start_game_btn:CustomSetActive(true)
end

-- 游戏结束
function HitHamsterView:FlushGameEnd(is_first_flush)
    self.is_game_satrt = false
    self.is_game_stop = false
    self.stop_time = 0
    self:ExcuteTweenAnim(false)
    self:ExcuteComboTween(false)
    self:ShowGameResultView(is_first_flush)
    self:ResetAllHoleMessage()
    self.node_list.start_game_btn:CustomSetActive(true)
    self.node_list.re_start_game_btn:CustomSetActive(false)
end

-- 刷新排行榜
function HitHamsterView:FlushGameRankList()
    local rank_list = HitHamsterWGData.Instance:GetWhackMoleRankList()
    if rank_list and self.hit_hamster_rank_list then
        self.hit_hamster_rank_list:SetDataList(rank_list)
    end

    local my_rank_data = HitHamsterWGData.Instance:GetMyWhackMoleRank()
    self.my_hit_hamster_rank:SetData(my_rank_data)
end

-- 刷新地鼠洞(Update 过来的)
function HitHamsterView:FlushCheckHoleUpdate(cur_game_time)
    for index, hamster_hole_cell in ipairs(self.hit_hamster_hole_list) do
        local is_update, is_exit = HitHamsterWGData.Instance:UpdateMoleByHole(index, cur_game_time, self.is_experience)
        if is_update then
            local data = HitHamsterWGData.Instance:GetMoleByHole(index, false, self.is_experience)
            hamster_hole_cell:SetData(data)
        elseif is_exit then
            hamster_hole_cell:ExecuteExitAction(false)
        end
    end
end

-- 展示结结算界面
function HitHamsterView:ShowGameResultView(is_first_flush)
    if is_first_flush then
        return
    end
    local show_result_data = {}
    show_result_data.is_experience = self.is_experience
    HitHamsterWGCtrl.Instance:OpenGameResultView(show_result_data)
end

-----------------tween------------------------
-- 播放动画
local TWEEN_TIME = 0.5
function HitHamsterView:ExcuteTweenAnim(is_forward)
    local tween_rank_move_x = 0
    local origin_rank_x = -300

    local tween_score_move_y = 120
    local origin_score_y = 0
    if is_forward then
        tween_score_move_y = 0
        origin_score_y = 120
    end

    local tween_time_move_y = -150
    local origin_time_y = 0
    if is_forward then
        tween_time_move_y = 0
        origin_time_y = -150
    end

    local tween_times_reward_move_x = 0
    local origin_times_reward_move_x = 200
    if is_forward then
        tween_times_reward_move_x = 200
        origin_times_reward_move_x = 0
    end

    if not self.show_master_rank_anim then
        self.show_master_rank_anim = true
        self.node_list.hit_hamster_rank_root.transform.anchoredPosition = u3dpool.vec3(origin_rank_x, self.node_list.hit_hamster_rank_root.transform.anchoredPosition.y, 0)
        self.node_list.hit_hamster_rank_root.transform:DOAnchorPosX(tween_rank_move_x, TWEEN_TIME)
    end

    self.node_list.hit_hamster_score_root.transform.anchoredPosition = u3dpool.vec3(self.node_list.hit_hamster_score_root.transform.anchoredPosition.x, origin_score_y, 0)
    self.node_list.hit_hamster_score_root.transform:DOAnchorPosY(tween_score_move_y, TWEEN_TIME)
    self.node_list.hit_hamster_game_time_root.transform.anchoredPosition = u3dpool.vec3(self.node_list.hit_hamster_game_time_root.transform.anchoredPosition.x, origin_time_y, 0)
    self.node_list.hit_hamster_game_time_root.transform:DOAnchorPosY(tween_time_move_y, TWEEN_TIME)
    self.node_list.hit_hamster_times_reward_root.transform.anchoredPosition = u3dpool.vec3(origin_times_reward_move_x, self.node_list.hit_hamster_times_reward_root.transform.anchoredPosition.y, 0)
    self.node_list.hit_hamster_times_reward_root.transform:DOAnchorPosX(tween_times_reward_move_x, TWEEN_TIME)
end

-- 播放连击的Tween
function HitHamsterView:ExcuteComboTween(is_forward)
    if self.combo_status == is_forward then
        return
    end

    self.combo_status = is_forward
    local tween_combo_value_form = 1
    if is_forward then
        tween_combo_value_form = 0
    end

    local tween_combo_value_to = 0
    if is_forward then
        tween_combo_value_to = 1
    end

    self.node_list.hit_hamster_combo_root.canvas_group:DoAlpha(tween_combo_value_form, tween_combo_value_to, 0.3)
end

-- 惊吓回洞
function HitHamsterView:ScareAllMole(hole_cell)
    for index, hamster_hole_cell in ipairs(self.hit_hamster_hole_list) do
        if hamster_hole_cell ~= hole_cell  then
            hamster_hole_cell:ExecuteExitAction(false)
        end
    end
end

-- 刷新参与奖励
function HitHamsterView:FlushJoinReward()
    local cfg_data = HitHamsterWGData.Instance:GetJoinRewardCfg()

    for i, time_reward_cell in ipairs(self.hit_time_reward_list) do
        if time_reward_cell and cfg_data[i] then
            time_reward_cell:SetData(cfg_data[i])
        end
    end
end

-----------------倒计时-------------------
-- 游戏倒计时
function HitHamsterView:FlushGameTime()
    local end_time = HitHamsterWGData.Instance:GetWhackMoleEndTime(self.is_experience)
    local invalid_time = end_time - TimeWGCtrl.Instance:GetServerTime()
    if invalid_time < 0 then
        if self.is_experience then
            self:FlushGameEnd()
            GuideWGCtrl.Instance:SendCommonGuideSet(COMMON_GUIDE_OPERATE_BIT.HIT_HAMSTER, 1)
        end
    end

    self.node_list["game_time"].text.text = string.format(Language.HitHamster.TimeDown, TimeUtil.FormatSecondDHM8(invalid_time))
end

--- 活动结束倒计时
function HitHamsterView:CleanActTimeDown()
	if CountDownManager.Instance:HasCountDown("hit_hamster_act_time_down") then
		CountDownManager.Instance:RemoveCountDown("hit_hamster_act_time_down")
	end
end

function HitHamsterView:FlushActTimeCountDown(end_time)
	local invalid_time = end_time
	if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
		self:CleanActTimeDown()
		self.node_list["act_time"].text.text = string.format(Language.OpertionAcitvity.WateringFlowers.ActivityRemainTime, TimeUtil.FormatSecondDHM8(invalid_time - TimeWGCtrl.Instance:GetServerTime()))
		CountDownManager.Instance:AddCountDown("hit_hamster_act_time_down", BindTool.Bind1(self.UpdateActCountDown, self), BindTool.Bind1(self.OnActComplete, self), invalid_time, nil, 1)
	end
end

function HitHamsterView:UpdateActCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.node_list["act_time"].text.text = string.format(Language.OpertionAcitvity.WateringFlowers.ActivityRemainTime, TimeUtil.FormatSecondDHM8(valid_time))
	end
end

function HitHamsterView:OnActComplete()
    self.node_list["act_time"].text.text = ""
    self:Close()
    HitHamsterWGCtrl.Instance:CloseGameResultView()
end

--移除地鼠惊吓定时器
function HitHamsterView:RemoveScareDelayTimer()
    if self.scare_timer then
        GlobalTimerQuest:CancelQuest(self.scare_timer)
        self.scare_timer = nil
    end
end
----------------------------------------------------
-- 游戏开始
function HitHamsterView:OnClickStartGame()
    local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.HIT_HAMSTER)

    if IsEmptyTable(activity_info) or (activity_info and activity_info.status ~= ACTIVITY_STATUS.OPEN) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.HitHamster.ACTNotOpenTip)
        return
    end

    local status = GuideWGCtrl.Instance:GetCommonGuideSet(COMMON_GUIDE_OPERATE_BIT.HIT_HAMSTER)
    local is_experience = status == 0
    -- is_experience = true
    if is_experience then
        -- 这里做了一个特殊操作，设置了数据（因为单机所以这个设置了，勿模仿）
        HitHamsterWGData.Instance:HoleGameListStart()
        self:Flush(0, "game_start", {is_experience = true})
    else
        HitHamsterWGCtrl.Instance:SendWhackMoleGameStart()
    end
end

--  游戏重新开始
function HitHamsterView:OnClickResetStartGame()
    local execute_rebreed = function()
        if self.is_experience then
            self:OnClickStartGame()
        else
            HitHamsterWGCtrl.Instance:SendWhackMoleGameStart()
        end
    end

    -- 弹出二次确认提示框
    if self.alert_window == nil then
        self.alert_window = Alert.New()
    end
    self.alert_window:SetLableString(Language.HitHamster.ResetStart)
    self.alert_window:SetOkFunc(execute_rebreed)
    self.alert_window:Open()
end

--  游戏退出游戏
function HitHamsterView:OnClickCloseWindow()
    if not self.is_game_satrt then
        self:Close()
        return
    end

    local execute_rebreed = function()
        HitHamsterWGCtrl.Instance:SendWhackMoleGameEnd()
        self:Close()
    end

    -- 弹出二次确认提示框
    if self.alert_window == nil then
        self.alert_window = Alert.New()
    end
    self.alert_window:SetLableString(Language.HitHamster.ExitGame)
    self.alert_window:SetOkFunc(execute_rebreed)
    self.alert_window:Open()
end

-- 打开排行榜
function HitHamsterView:OnClickShowRankView()
    HitHamsterWGCtrl.Instance:OpenWhackMoleRankView()
end

-- 关闭参与次数奖励面板
function HitHamsterView:OnClickShowTimeRewardList()
    self.node_list.close_show_time_reward:CustomSetActive(false)
end
---------------------Render-------------------------
-- 地鼠排行Render
HamsterRankRender = HamsterRankRender or BaseClass(BaseRender)
function HamsterRankRender:OnFlush()
    if IsEmptyTable(self.data) then
        self.node_list.rank_text.text.text = self.data.rank or self.index
        self.node_list.rank_name.text.text = Language.Common.XuWeiYiDai
        self.node_list.rank_score.text.text = tostring(0)
    else
        if self.data.rank >= 999 then
            self.node_list.rank_text.text.text = Language.FuBenPanel.ManHuangNotRankStr
        else
            self.node_list.rank_text.text.text = self.data.rank
        end
        
        self.node_list.rank_name.text.text = self.data.name
        self.node_list.rank_score.text.text = self.data.score
    end
end


-- 地鼠坑Render
HamsterHoleRender = HamsterHoleRender or BaseClass(BaseRender)
function HamsterHoleRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.mole_image, BindTool.Bind2(self.HammerHitClick, self))
end

function HamsterHoleRender:__delete()
    self:KillMoleTween()
    self:KillHitScoreTween()
    self:KillHitItemTween()

    self:RemoveUnkennelDelayTimer()
    self:RemoveHammerDelayTimer()
    self:RemoveChangeImageDelayTimer()
    self.touch_num = 0
    self.hammer_tween = nil
end

-- 点击地鼠
function HamsterHoleRender:HammerHitClick()
    BaseRender.OnClick(self)
end

-- 刷新
function HamsterHoleRender:OnFlush()
    if self.data == nil then
        self.node_list.bubble_root:CustomSetActive(false)
        self:ExecuteExitAction(false)   --地鼠回洞
    else
        self:ResetTouchNum()            -- 重置点击次数
        self:MoleUnkennel()             -- 地鼠出洞
        self:SetMoleTreasure()          -- 设置地鼠宝物和需要点击次数
    end
end

-- 重置点击次数
function HamsterHoleRender:ResetTouchNum()
    self.touch_num = 0
end

-- 地鼠出洞
function HamsterHoleRender:MoleUnkennel()
    self.node_list.hammer_root_1:CustomSetActive(false)
    self.node_list.hammer_root_2:CustomSetActive(false)
    self.node_list.mole_root:CustomSetActive(true)
    local action_time = self.data.action_time
    self.node_list.bubble_txt.text.text = self.data.bubble_str or ""

    self:ShowMoleTween(true, action_time, function()
        local is_show_bubble = self.data.bubble_str ~= nil and self.data.bubble_str ~= ""
        self.node_list.bubble_root:CustomSetActive(is_show_bubble)
    end)
end

-- 出洞或者回去的动画
function HamsterHoleRender:ShowMoleTween(is_forward, tween_time, call_back)
    self:KillMoleTween()
    local aim_y = -20
    local aim_x = -20

    if self.data then
        if self.data.id == MOLE_TYPE.BOMB then
            aim_y = -48
            aim_x = -38
        elseif self.data.id == MOLE_TYPE.SCARE then
            aim_y = -32
        elseif self.data.id == MOLE_TYPE.BOX then    
            aim_y = -48
        end
    end

    local pos_y = is_forward and aim_y or -180
    self.node_list.mole_root.transform.localPosition = Vector3(aim_x, self.node_list.mole_root.transform.localPosition.y, 0)
    self.mole_root_tween = self.node_list.mole_root.transform:DOAnchorPosY(pos_y, tween_time)
    self.mole_root_tween:SetEase(DG.Tweening.Ease.OutCubic)

    self.mole_root_tween:OnComplete(function()
        if call_back then
            call_back()
        end
    end)
end

-- 去除地鼠上下洞的动画
function HamsterHoleRender:KillMoleTween()
	if self.mole_root_tween ~= nil then
		self.mole_root_tween:Kill()
		self.mole_root_tween = nil
	end
end

-- 设置地鼠宝物和需要点击次数
function HamsterHoleRender:SetMoleTreasure()
    if self.data == nil then
        return
    end

    local mole_cfg = HitHamsterWGData.Instance:GetAllMoleCfgById(self.data.id)
    if (not mole_cfg) then
        return
    end

    self.node_list.hit_item_image:CustomSetActive(false)
    local pos_y = 0
    if mole_cfg.ID > 1 then
        pos_y = 10
    end
    self.node_list.hit_item_image.transform.anchoredPosition = u3dpool.vec3(-20, pos_y, 0)
    self.node_list.hit_item_image.transform.localScale = u3dpool.vec3(1, 1, 1)
    local bundle, asset

    if mole_cfg.item_id ~= 0 then
        local mole_type_str = string.format("a3_mole_type_0%d", mole_cfg.item_id)
        bundle, asset = ResPath.GetHitHamsterImg(mole_type_str)
        self.node_list.hit_item_image.image:LoadSprite(bundle, asset)
    end

    local mole_image_str = string.format("a3_mole_image_0%d", mole_cfg.ID)
    bundle, asset = ResPath.GetHitHamsterImg(mole_image_str)
    self.node_list.mole_image:CustomSetActive(true)
    self.node_list.mole_image.image:LoadSprite(bundle, asset, function()
        self.node_list.mole_image.image:SetNativeSize()
    end)
    
    self:SetNeedTouchTimes()
end

-- 设置地鼠需要点击的次数
function HamsterHoleRender:SetNeedTouchTimes()
    if self.data == nil then
        return
    end

    local mole_cfg = HitHamsterWGData.Instance:GetAllMoleCfgById(self.data.id)
    if (not mole_cfg) then
        return
    end

    local show_touch_num = mole_cfg.touch_times - self.touch_num
    self.node_list.hit_root:CustomSetActive(mole_cfg.touch_times > 1)
    self.node_list.need_hit_txt.text.text = show_touch_num

    if mole_cfg.touch_times > 1 and self.touch_num ~= mole_cfg.touch_times then
        self.node_list.need_hit_txt.transform.localScale = Vector3(1.4, 1.4, 1.4)
        self.node_list.need_hit_txt.transform.transform:DOScale(Vector3(1, 1, 1), 0.4):SetEase(DG.Tweening.Ease.OutBack)
    end
end

-- 获取点击次数
function HamsterHoleRender:PlayerTouch()
    if self.data == nil then
        return
    end

    local mole_cfg = HitHamsterWGData.Instance:GetAllMoleCfgById(self.data.id)
    if not mole_cfg then
        return
    end

    self.touch_num = self.touch_num + 1
    if self.touch_num > mole_cfg.touch_times then
        return
    end

    self:SetNeedTouchTimes()
    self:ShowHammerAnim()
end

-- 播放锤子动画
function HamsterHoleRender:ShowHammerAnim(call_back)
    self.node_list.hammer_root_1:CustomSetActive(false)
    self.node_list.hammer_root_2:CustomSetActive(false)

    local str = string.format("hammer_root_%d", self.touch_num)
    local bundle_name, asset_name = ResPath.GetA2Effect("ui_dds_qiaoda")
    EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.hit_effect_root.transform,
    nil, Vector3(0, 0, 0), Quaternion.Euler(0, 0, 0))

    self:RemoveHammerDelayTimer()
    if self.node_list[str] then
        self.node_list[str]:CustomSetActive(true)
        self.show_hammer_timer = GlobalTimerQuest:AddDelayTimer(function ()
            self.node_list[str]:CustomSetActive(false)
            if call_back then
                call_back()
            end
        end, 0.3)
    end
end

-- 判断是否回应服务器
function HamsterHoleRender:CheckTouchNumForServer()
    if self.data == nil then
        return false
    end

    local mole_cfg = HitHamsterWGData.Instance:GetAllMoleCfgById(self.data.id)
    if not mole_cfg then
        return false
    end

    return self.touch_num == mole_cfg.touch_times
end

-- 执行退出行动
-- is_hit 是否是被锤退出
-- is_experience 是否为练习
-- score_trans 分数UI位置
function HamsterHoleRender:ExecuteExitAction(is_hit, is_experience, score_trans, full_effect_root)
    self.node_list.bubble_root:CustomSetActive(false)
    self.node_list.hit_root:CustomSetActive(false)
    local action_time = self.data and self.data.action_time or 0.4
    -- 退出动画
    local exit_fun = function()
        self:ShowMoleTween(false, action_time)
    end

    if not is_hit then
        if self.data == nil  then
            exit_fun()
            return
        end

        local mole_cfg = HitHamsterWGData.Instance:GetAllMoleCfgById(self.data.id)
        if self.touch_num == 0 or (mole_cfg and self.touch_num < mole_cfg.touch_times) then
            exit_fun()
        end

        return
    end

    if self.data == nil then
        return
    end

    local need_change_str = ""
    local need_hit_str = ""
    if self.data.id == MOLE_TYPE.BOX then
        need_change_str = string.format("a3_mole_image_0%d_1", self.data.id)
        need_hit_str = string.format("a3_mole_image_0%d_1", self.data.id)
    elseif self.data.id == MOLE_TYPE.SCARE then
        need_change_str = "ui_dds_yanhua"
        need_hit_str = "a3_dds_bz"
    elseif self.data.id == MOLE_TYPE.BOMB then
        need_change_str = "ui_dds_baozha"
        need_hit_str = "a3_dds_bz"
    elseif self.data.id == MOLE_TYPE.COOL then
        need_change_str = "ui_dds_bingdong"
        need_hit_str = "a3_mole_type_04"
    end

    if need_change_str ~= "" then
        --切换交互UI
        local mole_cfg = HitHamsterWGData.Instance:GetAllMoleCfgById(self.data.id)
        local change_ui_time = mole_cfg and (mole_cfg.delay_ms / 1000) or 0.4
        local bundle, asset = ResPath.GetHitHamsterImg(need_hit_str)

        self:RemoveChangeImageDelayTimer()
        if self.data.id == MOLE_TYPE.BOX or self.data.id == MOLE_TYPE.BOMB then
            self.show_change_img_timer = GlobalTimerQuest:AddDelayTimer(function ()
                self.node_list.mole_image.image:LoadSprite(bundle, asset, function()
                    self.node_list.mole_image.image:SetNativeSize()
                end)
            end, change_ui_time)
        end

        if self.data.id == MOLE_TYPE.SCARE or self.data.id == MOLE_TYPE.BOMB or self.data.id == MOLE_TYPE.COOL then
            local bundle_name, asset_name = ResPath.GetA2Effect(need_change_str)
            EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.nor_effect_root.transform,
            nil, Vector3(0, 0, 0), Quaternion.Euler(0, 0, 0))

            if self.data.id == MOLE_TYPE.COOL and full_effect_root ~= nil then
                local bundle_name, asset_name = ResPath.GetA2Effect("ui_dds_bingdong_quanpin")
                EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, full_effect_root,
                2, Vector3(0, 0, 0), Quaternion.Euler(0, 0, 0))
            end
        end
    end
    
    self:ShowHammerAnim(function()
        self.node_list.hit_item_image:CustomSetActive(false)
        self:ShowHitScoreTween(is_experience)
        exit_fun()

        if self.data == nil then
            return
        end

        local mole_cfg = HitHamsterWGData.Instance:GetAllMoleCfgById(self.data.id)
        if mole_cfg and mole_cfg.item_id > 0 and mole_cfg.item_id < 3 then
            self.node_list.hit_item_image:CustomSetActive(true)
            self:ShowHitItemTween(score_trans)
        end
    end)
end

-- 飘字的动画
function HamsterHoleRender:ShowHitScoreTween(is_experience)
    if self.data == nil then
        return
    end

    local curr_hit_times = HitHamsterWGData.Instance:GetWhackMoleFinalHitTimes(is_experience)
    local mole_cfg = HitHamsterWGData.Instance:GetAllMoleCfgById(self.data.id)
    if (not mole_cfg) or (mole_cfg.action_hit_num_time == 0) then
        return
    end

    local curr_score_add = HitHamsterWGData.Instance:GetWhackMoleHitAddition()
    local curr_score = math.floor(mole_cfg.score * curr_score_add)

    local str = string.format("%s%s", "+", curr_score)
    local color = HIT_COLOR.COMBO_1
    if curr_hit_times > 9 and curr_hit_times <= 19 then
        color = HIT_COLOR.COMBO_2
    elseif curr_hit_times > 19 then
        color = HIT_COLOR.COMBO_3
    end

    if mole_cfg.score < 0 then
        color = HIT_COLOR.COMBO_4
        str = mole_cfg.score
    end

    self.node_list.hit_score_txt:CustomSetActive(true)
    self.node_list.hit_score_txt.text.text = ToColorStr(str, color)

    if mole_cfg.hit_num_pos_x ~= 0 and mole_cfg.hit_num_pos_y ~= 0 then
        local x = mole_cfg.hit_num_pos_x
        local y = mole_cfg.hit_num_pos_y
        local move_y = y + 10
        local move_time = mole_cfg.action_hit_num_time / 1000

        local obj_transform = self.node_list.hit_score_txt.transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
        self.node_list.hit_score_txt.transform.anchoredPosition = u3dpool.vec3(x, y, 0)
        obj_transform.alpha = 1

        local tween_move = self.node_list.hit_score_txt.transform:DOAnchorPos(u3dpool.vec3(x, move_y, 0), move_time)
        local tween_alpha = obj_transform:DoAlpha(1, 0, move_time)
        self.node_list.hit_score_txt.transform.localScale = Vector3(1.4, 1.4, 1.4)
        local tween_scale = self.node_list.hit_score_txt.transform:DOScale(Vector3(1, 1, 1), move_time)
        self:KillHitScoreTween()
        self.hit_score_tween = DG.Tweening.DOTween.Sequence()
        self.hit_score_tween:SetEase(DG.Tweening.Ease.OutCubic)
        self.hit_score_tween:Join(tween_move)
        self.hit_score_tween:Join(tween_alpha)
        self.hit_score_tween:Join(tween_scale)

        self.hit_score_tween:OnComplete(function ()
            self.node_list.hit_score_txt:CustomSetActive(false)
        end)
    end
end

-- 去除飘字的动画
function HamsterHoleRender:KillHitScoreTween()
	if self.hit_score_tween ~= nil then
		self.hit_score_tween:Kill()
		self.hit_score_tween = nil
	end
end

-- 展示物品图标动画
function HamsterHoleRender:ShowHitItemTween(score_trans)
    if self.data == nil then
        return
    end

    local mole_cfg = HitHamsterWGData.Instance:GetAllMoleCfgById(self.data.id)
    if (not mole_cfg) or (mole_cfg.item_move_time == 0) then
        return
    end

    local move_time = mole_cfg.item_move_time / 1000
    if mole_cfg.item_type == 1 then
        self:KillHitItemTween()
        local tween_move = self.node_list.hit_item_image.transform:DOMove(score_trans.position, move_time)
        local tween_size = self.node_list.hit_item_image.transform:DOScale(Vector3(0, 0, 0), move_time)
        self.hit_score_tween = DG.Tweening.DOTween.Sequence()
        self.hit_score_tween:SetEase(DG.Tweening.Ease.OutCubic)
        self.hit_score_tween:Join(tween_move)
        self.hit_score_tween:Join(tween_size)
    else
        self.node_list.hit_item_image:CustomSetActive(false)
    end
end

-- 去除物品图标动画
function HamsterHoleRender:KillHitItemTween()
	if self.hit_item_tween ~= nil then
		self.hit_item_tween:Kill()
		self.hit_item_tween = nil
	end
end

-- 移除锤子动画定时器
function HamsterHoleRender:RemoveHammerDelayTimer()
    if self.show_hammer_timer then
        GlobalTimerQuest:CancelQuest(self.show_hammer_timer)
        self.show_hammer_timer = nil
    end
end

--移除延迟出现倒计时
function HamsterHoleRender:RemoveUnkennelDelayTimer()
    if self.show_unkennel_timer then
        GlobalTimerQuest:CancelQuest(self.show_unkennel_timer)
        self.show_unkennel_timer = nil
    end
end

--移除切换UI定时器
function HamsterHoleRender:RemoveChangeImageDelayTimer()
    if self.show_change_img_timer then
        GlobalTimerQuest:CancelQuest(self.show_change_img_timer)
        self.show_change_img_timer = nil
    end
end

--------------------------------------------------------------------------------------
-- 地鼠坑Render
HamsterTimeRewardRender = HamsterTimeRewardRender or BaseClass(BaseRender)
-- 刷新
function HamsterTimeRewardRender:OnFlush()
    if self.data == nil then
        return
    end

    local all_times = HitHamsterWGData.Instance:GetWhackMoleRewardTimes()
    self.node_list.geted:CustomSetActive(all_times >= self.data.times)
    self.node_list.reward_txt.text.text = string.format(Language.HitHamster.JoinTime, self.data.times) 
end