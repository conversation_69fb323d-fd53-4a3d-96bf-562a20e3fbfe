function MengLingView:InitMengLingStrengthPanel()
    if not self.mengling_strength_item then
        self.mengling_strength_item = ItemCell.New(self.node_list.mengling_strength_item)
        -- self.mengling_strength_item:SetItemTipFrom(ItemTip.FROM_MENGLING_EQUIP)
    end

    if not self.mengling_strength_attr_list then
        self.mengling_strength_attr_list = AsyncListView.New(CommonAddAttrRender, self.node_list.mengling_strength_attr_list)
     end

     if not self.mengling_strength_cost_item then
        self.mengling_strength_cost_item = ItemCell.New(self.node_list.mengling_strength_cost_item)
     end

     self.strength_up_seq = -1
     self.strength_up_slot = -1

     XUI.AddClickEventListener(self.node_list.btn_mengling_uplevel, BindTool.Bind(self.OnCLickMengLingUplevel, self))
     XUI.AddClickEventListener(self.node_list.btn_mengling_auto_uplevel, BindTool.Bind(self.OnCLickMengLingAutoUplevel, self))
     XUI.AddClickEventListener(self.node_list.btn_mengling_one_key_uplevel, BindTool.Bind(self.OnCLickMengLingOneKeyUplevel, self))
     XUI.AddClickEventListener(self.node_list.btn_mengling_equip_way, BindTool.Bind(self.OnClickGoToBtn, self))
end

function MengLingView:DeleteMengLingStrengthPanel()
    if self.mengling_strength_item then
        self.mengling_strength_item:DeleteMe()
        self.mengling_strength_item = nil
    end

    if self.mengling_strength_attr_list then
        self.mengling_strength_attr_list:DeleteMe()
        self.mengling_strength_attr_list = nil
    end

    if self.mengling_strength_cost_item then
        self.mengling_strength_cost_item:DeleteMe()
        self.mengling_strength_cost_item = nil
    end

    self.strength_up_seq = nil
    self.strength_up_slot = nil

    self:MengLingStrengthOneKeyStop()
end

function MengLingView:FlushMengLingStrengthPanel()
    if IsEmptyTable(self.select_equip_item_data) or self.select_equip_item_data.item_id <= 0 then
        self.node_list.mengling_no_wearequip:CustomSetActive(true)
        self.node_list.mengling_strength_panel:CustomSetActive(false)  
        return
    end

    self.node_list.mengling_no_wearequip:CustomSetActive(false)
    self.node_list.mengling_strength_panel:CustomSetActive(true)

    local strong_target_data = self.select_equip_item_data
    local seq = strong_target_data.seq
    local slot = strong_target_data.slot
    local level = strong_target_data.level
    self.mengling_strength_item:SetData(strong_target_data)

    local item_cfg = ItemWGData.Instance:GetItemConfig(strong_target_data.item_id)
    self.node_list.mengling_strength_item_name.text.text = item_cfg.name
    self.node_list.mengling_strength_level.text.text = string.format(Language.Common.LevelNormal, level)

    local level_cfg = MengLingWGData.Instance:GetMengLingSlotLevelCfg(seq, slot, level)
    local next_levele_cfg = MengLingWGData.Instance:GetMengLingSlotLevelCfg(seq, slot, level + 1)
    local data_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(level_cfg, next_levele_cfg, "attr_id", "attr_value", 1, 7)
    self.mengling_strength_attr_list:SetDataList(data_list)

    local is_max_level = IsEmptyTable(next_levele_cfg)
    self.node_list.mengling_strength_cost_panel:CustomSetActive(not is_max_level)
    self.node_list.mengling_strength_max_level_flag:CustomSetActive(is_max_level)

    if not is_max_level and not IsEmptyTable(level_cfg) then
        local cost_item_score = level_cfg.need_score
        local score = MengLingWGData.Instance:GetMengLingScore()
        local score_enough = cost_item_score <= score
        local str_color = score_enough and COLOR3B.C8 or COLOR3B.C10
        local cur_score_str = ToColorStr(CommonDataManager.ConverExpByThousand(score), str_color)
        local color_str = cur_score_str .. "/" .. CommonDataManager.ConverExpByThousand(cost_item_score)
		self.node_list.mengling_strength_cost_desc.text.text = color_str

        local cost_item_id = MengLingWGData.Instance:GetMengLingOtherCfg("decompose_item")
        self.mengling_strength_cost_item:SetData({item_id = cost_item_id})

        self.node_list.btn_mengling_auto_uplevel_remind:CustomSetActive(score_enough)
        self.node_list.btn_mengling_uplevel_remind:CustomSetActive(score_enough)
        self.node_list.btn_mengling_one_key_uplevel_remind:CustomSetActive(score_enough)
    end
end

-- 升级
function MengLingView:OnCLickMengLingUplevel()
    if MengLingWGData.Instance:GetMengLingStrengthAutoUpState() then
        return
    end

    if self:IsMengLingStrengthSlotCanUp(true) then
        MengLingWGCtrl.Instance:OnCSDreamSpiritOperate(DREAM_SPIRIT_OPERATE_TYPE.SLOT_UP_LEVEL, 
        self.select_equip_suit_seq, self.select_equip_item_slot)
    end
end

-- 一键升级
function MengLingView:OnCLickMengLingAutoUplevel()
    if MengLingWGData.Instance:GetMengLingStrengthAutoUpState() then
        self:MengLingStrengthOneKeyStop()
    else
        if self:IsMengLingStrengthSlotCanUp(true) then
            self.node_list.btn_mengling_auto_uplevel_text.text.text = Language.MengLing.MengLingStopDesc
            self.strength_up_seq = self.select_equip_suit_seq
            self.strength_up_slot = self.select_equip_item_slot

            MengLingWGData.Instance:SetMengLingStrengthAutoUpState(true)
            MengLingWGCtrl.Instance:OnCSDreamSpiritOperate(DREAM_SPIRIT_OPERATE_TYPE.SLOT_UP_LEVEL, 
                self.strength_up_seq, self.strength_up_slot)
        end
    end
end

function MengLingView:IsMengLingStrengthSlotCanUp(need_show_tip, seq, slot)
    if IsEmptyTable(self.select_equip_item_data) then
        return false
    end

    local target_seq = seq or self.select_equip_suit_seq
    local target_slot = slot or self.select_equip_item_slot
    local data_info = MengLingWGData.Instance:GetMengLingSuitSlotItemData(target_seq, target_slot)

    local seq = data_info.seq
    local slot = data_info.slot
    local level = data_info.level
    local level_cfg = MengLingWGData.Instance:GetMengLingSlotLevelCfg(seq, slot, level)
    local next_level_cfg = MengLingWGData.Instance:GetMengLingSlotLevelCfg(seq, slot, level + 1)

    if IsEmptyTable(next_level_cfg) then
        return false
    end

    local score = MengLingWGData.Instance:GetMengLingScore()
    if score < level_cfg.need_score then
        if need_show_tip then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.MengLing.MengLingJifenNotEnough)
            local cost_item_id = MengLingWGData.Instance:GetMengLingOtherCfg("decompose_item")
            TipWGCtrl.Instance:OpenItem({item_id = cost_item_id})
        end

        return false
    end

    return true
end

function MengLingView:MengLingStrengthOneKeyStop()
    self.strength_up_seq = nil
    self.strength_up_slot = nil

    if self.node_list.btn_mengling_auto_uplevel_text then
        self.node_list.btn_mengling_auto_uplevel_text.text.text = Language.MengLing.MengLingOneKeyStrengthDesc
    end
    MengLingWGData.Instance:SetMengLingStrengthAutoUpState(false)
end

function MengLingView:StartMengLingStrengthUpAmin()
    if self:GetShowIndex() == TabIndex.mengling_strong then
        TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_qianghua1, is_success = true, pos = Vector2(0, 0)})
        local seq_not_change = self.strength_up_seq == self.select_equip_suit_seq
        local slot_not_change = self.strength_up_slot == self.select_equip_item_slot

        if seq_not_change and slot_not_change and self:IsMengLingStrengthSlotCanUp(false, self.select_equip_suit_seq, self.select_equip_item_slot) then
            if MengLingWGData.Instance:GetMengLingStrengthAutoUpState() then
                ReDelayCall(self, function()
                    MengLingWGCtrl.Instance:OnCSDreamSpiritOperate(DREAM_SPIRIT_OPERATE_TYPE.SLOT_UP_LEVEL, 
                        self.select_equip_suit_seq, self.select_equip_item_slot)
                 end, 0.5, "mengling_strength_one_key")
            end
        else
            self:MengLingStrengthOneKeyStop()
        end
    else
        self:MengLingStrengthOneKeyStop()
    end
end

function MengLingView:OnCLickMengLingOneKeyUplevel()
    if self:IsMengLingStrengthSlotCanUp(true) then
        MengLingWGCtrl.Instance:OnCSDreamSpiritOperate(DREAM_SPIRIT_OPERATE_TYPE.SLOT_UP_LEVEL_ONE_KEY, self.select_equip_suit_seq)
    end
end