function FightSoulWGData:InitCfgData()
    local cfg
    -- 配置数据
	self.fight_soul_system_cfg = ConfigManager.Instance:GetAutoConfig("sixiang_system_cfg_auto")

	-- 其他配置
	self.other_cfg = self.fight_soul_system_cfg.other[1]

    -- 经验池配置
    cfg = self.fight_soul_system_cfg.exp_pool
    self.exp_pool_cfg = cfg
    self.exp_pool_max_level = cfg[#cfg].exp_pool_level or 0

    -- 上阵位配置
    cfg = self.fight_soul_system_cfg.sixiang_slot
    self.fight_slot_cfg = cfg
    self.slot_level_limit_list = {}
    self.slot_unlock_stuff_list = {}
    for k,v in pairs(cfg) do
        self.slot_level_limit_list[v.role_level_limit] = true
        if v.stuff_id > 0 and v.stuff_num > 0 then
            self.slot_unlock_stuff_list[v.stuff_id] = true
        end
    end

    -- 战魂类型配置
    cfg = self.fight_soul_system_cfg.sixiang_type
    self.fight_soul_type_cfg = ListToMap(cfg, "sixiang_type")
    self.fight_soul_type_byskill_cfg = ListToMap(cfg, "active_skill_list")
    self.break_stuff_type_list = {}
    self.break_stuff_id_list = {}
    for k,v in pairs(cfg) do
        self.break_stuff_type_list[v.sixiang_type] = v.break_consume_item
        self.break_stuff_id_list[v.break_consume_item] = true
    end

    -- 战魂道具配置
    self.fight_soul_item_cfg = self.fight_soul_system_cfg.sixiang
    self:CacheItemIdByParam()

    -- 战魂升级配置
    cfg = self.fight_soul_system_cfg.sixiang_uplevel
    self.fight_soul_level_cfg = cfg
    self.fight_soul_max_up_level = cfg[#cfg].level or 0

    -- 战魂突破配置
    cfg = self.fight_soul_system_cfg.sixiang_upgrade
    self.fight_soul_break_cfg = cfg
    self.break_and_level_cfg_list = {}
    local old_level = 0
    for i,v in ipairs(cfg) do
        local data = {}
        data.up_old_level = old_level
        data.up_max_level = cfg[i].up_max_level
        old_level = v.up_max_level
        self.break_and_level_cfg_list[v.grade] = data
    end
    self.fight_soul_max_grade = cfg[#cfg].grade or 0

    -- 战魂提示合成历程配置
    self.fight_soul_compose_tips_cfg = ListToMap(self.fight_soul_system_cfg.sixiang_com_show, "color", "star")

    -- 战魂合成配置
    self:GetComposeSplitCfg()


    -- 魂骨装备位
    cfg = self.fight_soul_system_cfg.hungu_slot
    self.bone_part_cfg = cfg
    self.bone_part_preview_list = {}
    self.bone_part_model_load_type_list = {}
    self.bone_part_model_single_res_list = {}
    for k,v in pairs(cfg) do
        self.bone_part_preview_list[v.hungu_part] = {}
        self.bone_part_model_load_type_list[v.hungu_part] = {}
        self.bone_part_model_single_res_list[v.hungu_part] = {}

        local preview_list = Split(v.display_item_list, "|")
        local type_list = Split(v.model_attachment_list, "|")
        local res_list = Split(v.model_single_show, "|")
        for i = 1, FIGHT_SOUL_TYPE.MAX do
            self.bone_part_preview_list[v.hungu_part][i] = tonumber(preview_list[i]) or 0
            self.bone_part_model_load_type_list[v.hungu_part][i] = tonumber(type_list[i]) or 0
            self.bone_part_model_single_res_list[v.hungu_part][i] = tonumber(res_list[i]) or 0
        end
    end

    -- 魂骨属性配置
    self.bone_attr_cfg = ListToMap(self.fight_soul_system_cfg.hungu, "hungu_part", "base_color", "base_star")

    -- 魂骨强化配置
    cfg = self.fight_soul_system_cfg.hungu_uplevel
    self.bone_strength_cfg = ListToMap(cfg, "hungu_part", "level")
    self.bone_strength_max_level_list = {}
    self.bone_strength_stuff = {}

    for part,list in pairs(self.bone_strength_cfg) do
        self.bone_strength_max_level_list[part] = list[#list].level
        for i,data in pairs(list) do
            self.bone_strength_stuff[data.stuff_id] = true
        end
    end

    -- 魂骨提示合成历程配置
    self.fs_bone_compose_tips_cfg = ListToMap(self.fight_soul_system_cfg.hungu_com_show, "color", "star")

    -- 魂骨合成配置
    self.bone_compose_cfg = ListToMap(self.fight_soul_system_cfg.hungu_up_color_star, "color", "star")

    -- 魂骨套装类型
    self.bone_suit_type_cfg = self.fight_soul_system_cfg.suit_type

    -- 魂骨套装配置
    self.bone_suit_cfg = ListToMap(self.fight_soul_system_cfg.hungu_suit, "sixiang_type", "suit_type", "base_color", "same_num")
end

function FightSoulWGData:GetComposeSplitCfg()
    local color, star = 0, 0
    self.fight_soul_compose_cfg = {}
    local split_fun = function (str)
        local list = Split(str, "|")
        local real_list = {}
        local value, show_value
        for k,v in pairs(list) do
            value = tonumber(v)
            real_list[value] = true
            if show_value == nil or show_value > value then
                show_value = value
            end
        end

        return real_list, show_value or 0
    end

    for k,v in pairs(self.fight_soul_system_cfg.sixiang_up_color_star) do
        color = v.color
        star = v.star
        if not self.fight_soul_compose_cfg[color] then
            self.fight_soul_compose_cfg[color] = {}
        end
        
        if not self.fight_soul_compose_cfg[color][star] then
            self.fight_soul_compose_cfg[color][star] = {}
        end

        local data = self.fight_soul_compose_cfg[color][star]
        data.target_color = v.target_color
        data.target_star = v.target_star
        data.same_card_num = v.same_card_num
        data.same_card_color_list, data.same_card_show_color = split_fun(v.same_card_color)
        data.same_card_star_list, data.same_card_show_star = split_fun(v.same_card_star)
        data.same_type_num = v.same_type_num
        data.same_type_color_list, data.same_type_show_color = split_fun(v.same_type_color)
        data.same_type_star_list, data.same_type_show_star = split_fun(v.same_type_star)
    end
end

function FightSoulWGData:GetAllCfg()
    return self.fight_soul_system_cfg
end

-- 获取上阵位配置
function FightSoulWGData:GetFightslotCfg()
    return self.fight_slot_cfg
end

-- 获取上阵位配置
function FightSoulWGData:GetFightslotCfgBySlot(slot)
    return self.fight_slot_cfg[slot]
end

-- 是否是阵位限制等级
function FightSoulWGData:IsSlotLimitLevel(level)
    return self.slot_level_limit_list[level]
end

-- 是否是阵位解锁材料
function FightSoulWGData:GetIsUnlockStuff(item_id)
    return self.slot_unlock_stuff_list[item_id]
end

-- 经验池最大等级
function FightSoulWGData:GetExpPoolMaxLevel()
	return self.exp_pool_max_level
end

-- 经验池是否最大等级
function FightSoulWGData:IsMaxExpPoolLevel()
	local exp_pool_max_level = self:GetExpPoolMaxLevel()
	return self.exp_pool_level >= exp_pool_max_level
end

-- 获取all战魂类型配置
function FightSoulWGData:GetAllFightSoulTypeCfg()
    return self.fight_soul_type_cfg
end

-- 获取战魂类型配置
function FightSoulWGData:GetFightSoulTypeCfg(type)
    return self.fight_soul_type_cfg[type]
end

-- 获取all战魂类型配置
function FightSoulWGData:GetFightSoulTypeCfgBySkill(skill_id)
    return self.fight_soul_type_byskill_cfg[skill_id]
end

-- 获取突破材料id
function FightSoulWGData:GetBreakStuffIdByType(type)
    return self.break_stuff_type_list[type]
end

-- 突破材料列表
function FightSoulWGData:GetBreakStuffList()
    return self.break_stuff_id_list
end

-- 是否战魂进阶消耗材料
function FightSoulWGData:IsFightSoulBreakStuff(item_id)
	return self.break_stuff_id_list[item_id]
end

-- 获取战魂道具配置
function FightSoulWGData:GetFightSoulItemCfg(item_id)
    return self.fight_soul_item_cfg[item_id]
end

-- 是否战魂道具
function FightSoulWGData:IsFightSoulItem(item_id)
    return self.fight_soul_item_cfg[item_id] ~= nil
end

-- 获取战魂道具星级
function FightSoulWGData:GetFightSoulItemStar(item_id)
    local cfg = self:GetFightSoulItemCfg(item_id)
    return cfg and cfg.base_star or 0
end

-- 战魂背包最大数
function FightSoulWGData:GetFightSoulBagMaxNum()
	return self.other_cfg.sixiang_bag_limit
end

-- 战魂装备背包最大数
function FightSoulWGData:GetBoneBagMaxNum()
	return self.other_cfg.sixiangzhuangbei_bag_limit
end

-- 战魂技能所需怒气
function FightSoulWGData:GetSkillNeedNuQi()
    local nuqi = self.other_cfg.max_nuqi or 1
    nuqi = nuqi > 0 and nuqi or 1
	return nuqi
end

-- 【养成】获取经验池配置
function FightSoulWGData:GetExpPoolCfg(exp_pool_level)
	return self.exp_pool_cfg[exp_pool_level]
end

-- 【养成】获取经验池上限
function FightSoulWGData:GetExpPoolLimit(exp_pool_level)
	local cfg = self.exp_pool_cfg[exp_pool_level]
	return cfg and cfg.exp_pool_limit or 0
end

-- 【养成】战魂经验物品id
function FightSoulWGData:GetFightSoulExpItemId()
	return self.other_cfg.sixiang_exp_item
end

-- 【养成】获取配置的最大等级
function FightSoulWGData:GetMaxUpLevel()
    return self.fight_soul_max_up_level
end

-- 【养成】获取配置的最大重数
function FightSoulWGData:GetMaxGrade()
    return self.fight_soul_max_grade
end

-- 【养成】获取当前重等级区间
function FightSoulWGData:GetCurGradeLevelRange(grade)
    local cfg = self.break_and_level_cfg_list[grade]
    local min = cfg and cfg.up_old_level or 0
    local max = cfg and cfg.up_max_level or 0
    return min, max
end

--【养成】 获取当前重最大能升的最大等级
function FightSoulWGData:GetCurGradeMaxUpLevel(grade)
    local cfg = self.break_and_level_cfg_list[grade]
    return cfg and cfg.up_max_level or 0
end

-- 【养成】获取等级配置
function FightSoulWGData:GetLevelCfgByLevel(level)
    return self.fight_soul_level_cfg[level]
end

-- 【养成】获取突破配置
function FightSoulWGData:GetBreakCfgByGrade(grade)
    return self.fight_soul_break_cfg[grade]
end

-- 【融合】统计经验、材料返还
function FightSoulWGData:GetReturnExpAndStuff(item_data)
    local exp_num, grade_stuff_id, grade_stuff_num = 0, 0, 0
    if IsEmptyTable(item_data) then
    	return exp_num, grade_stuff_id, grade_stuff_num
    end

    local fs_type = item_data.fight_soul_type
    local level = item_data.level
    local grade = item_data.grade
    local all_cfg = self:GetAllCfg()
    local level_cfg = all_cfg.sixiang_uplevel
    local grade_cfg = all_cfg.sixiang_upgrade
    if not IsEmptyTable(level_cfg) then
        for k,v in ipairs(level_cfg) do
            if level >= v.level then
                exp_num = exp_num + v.need_exp
            else
                break
            end
        end
    end

    if not IsEmptyTable(grade_cfg) then
        for k,v in ipairs(grade_cfg) do
            if grade > v.grade then
                grade_stuff_num = grade_stuff_num + v.consume_num
            else
                break
            end
        end
    end

    grade_stuff_id = self:GetBreakStuffIdByType(fs_type)
    return exp_num, grade_stuff_id, grade_stuff_num
end

-- 获取技能图标资源
function FightSoulWGData:GetSkillIconResByItemId(item_id)
    local bundle, asset = "", ""
    local fs_item_cfg = self:GetFightSoulItemCfg(item_id)
	if IsEmptyTable(fs_item_cfg) then
		return bundle, asset
	end

    local skill_client_cfg = SkillWGData.Instance:GetSkillClientConfig(fs_item_cfg.active_skill, fs_item_cfg.skill_level)
    if IsEmptyTable(skill_client_cfg) then
        return bundle, asset
    end

    return ResPath.GetSkillIconById(skill_client_cfg.icon_resource)
end

-- 【养成】当前升级属性显示列表  no_wear:未上阵
function FightSoulWGData:GetUpLevelShowAttr(cur_level, no_wear)
    local cur_cfg = self:GetLevelCfgByLevel(cur_level)
    local next_cfg = self:GetLevelCfgByLevel(cur_level + 1)

    local attr_list = EquipWGData.GetSortAttrListHaveNextByCfg(cur_cfg, next_cfg)
    if no_wear then
        for k, v in pairs(attr_list) do
            attr_list[k].attr_next_value = 0
            attr_list[k].add_value = 0
        end
    end

    return attr_list
end

-- 【养成】当前突破属性显示列表  no_wear:未上阵
function FightSoulWGData:GetBreakShowAttr(cur_grade, no_wear)
    local cur_cfg = self:GetBreakCfgByGrade(cur_grade)
    local next_cfg = self:GetBreakCfgByGrade(cur_grade + 1)

    local attr_list = EquipWGData.GetSortAttrListHaveNextByCfg(cur_cfg, next_cfg)
    if no_wear then
        for k, v in pairs(attr_list) do
            attr_list[k].attr_next_value = 0
            attr_list[k].add_value = 0
        end
    end

    return attr_list
end

-- 【养成】当前经验池属性显示列表
function FightSoulWGData:GetExpPoolShowAttr(level)
    local cur_cfg = self:GetExpPoolCfg(level)
    local attr_list = EquipWGData.GetSortAttrListByCfg(cur_cfg)
    return attr_list or {}
end

--【融合】获取战魂提示合成历程配置
function FightSoulWGData:GetComposeTipsCfg(color, star)
    return (self.fight_soul_compose_tips_cfg[color] or {})[star]
end

-- 【融合】获取战魂合成配置
function FightSoulWGData:GetComposeCfg(color, star)
    return (self.fight_soul_compose_cfg[color] or {})[star]
end

-- 【融合】根据当前品质、星级 获取
function FightSoulWGData:GetComposeTargetItemId(cur_type, cur_color, cur_star)
    local target_item_id = 0
    local compose_cfg = self:GetComposeCfg(cur_color, cur_star)

    if compose_cfg then
        target_item_id = self:GetItemIdByParam(compose_cfg.target_color, compose_cfg.target_star, cur_type)
    end

    return target_item_id
end

-- 【融合】融合界面属性列表
function FightSoulWGData:GetComposeAttr(cur_item_id, next_item_id, is_preview)
    if is_preview then
        cur_item_id = 17101
    end
    local cur_item_cfg = self:GetFightSoulItemCfg(cur_item_id)
    local next_item_cfg = self:GetFightSoulItemCfg(next_item_id)
    local attr_list = EquipWGData.GetSortAttrListHaveNextByCfg(cur_item_cfg, next_item_cfg)

    if is_preview then
        for k, v in pairs(attr_list) do
            attr_list[k].attr_value = 0
            attr_list[k].attr_next_value = 0
            attr_list[k].add_value = 0
        end
    end

    return attr_list
end

-- 【融合】根据品质 - 星级 - 类型 缓存道具id
function FightSoulWGData:CacheItemIdByParam()
    local all_cfg = self:GetAllCfg().sixiang
    self.skill_max_level_list = {}
    self.skill_level_need_list = {}
    self.cache_fs_param_item_list = {}
    self.cache_min_fs_skill_info = {}

    local color, star, type, item_cfg
    local skill_id, skill_level
    for k,v in ipairs(all_cfg) do
        item_cfg = ItemWGData.Instance:GetItemConfig(v.sixiang_id)
        if item_cfg then
            color = item_cfg.color
            star = v.base_star
            type = v.sixiang_type
            self.cache_fs_param_item_list[color] = self.cache_fs_param_item_list[color] or {}
            self.cache_fs_param_item_list[color][star] = self.cache_fs_param_item_list[color][star] or {}
            self.cache_fs_param_item_list[color][star][type] = v.sixiang_id

            skill_id = v.active_skill
            skill_level = v.skill_level
            -- 缓存技能最大等级
            self.skill_max_level_list[skill_id] = self.skill_max_level_list[skill_id] or skill_level
            if skill_level > self.skill_max_level_list[skill_id] then
                self.skill_max_level_list[skill_id] = skill_level
            end

            -- 缓存技能每级所需最低品质星级
            self.skill_level_need_list[skill_id] = self.skill_level_need_list[skill_id] or {}
            if not self.skill_level_need_list[skill_id][skill_level] then
                self.skill_level_need_list[skill_id][skill_level] = {color = color, star = star}
            end

            -- 缓存默认技能信息
            if self.cache_min_fs_skill_info[type] == nil then
                self.cache_min_fs_skill_info[type] = {skill_id = skill_id, skill_level = skill_level,}
            end
        end
    end
end

function FightSoulWGData:GetDefaultSkillInfoByFSType(fs_type)
    return self.cache_min_fs_skill_info[fs_type]
end

function FightSoulWGData:GetSkillMaxLevelBySkillId(skill_id)
    return (self.skill_max_level_list or {})[skill_id] or 0
end

function FightSoulWGData:GetSkillLevelNeedBySkillId(skill_id, skill_level)
    local empty_table = {}
    return ((self.skill_level_need_list or empty_table)[skill_id] or empty_table)[skill_level]
end

-- 【融合】通过品质 - 星级 - 类型 获取道具id
function FightSoulWGData:GetItemIdByParam(color, star, type)
    local empty_table = {}
    return ((self.cache_fs_param_item_list[color] or empty_table)[star] or empty_table)[type] or 0
end

-- 【融合】通过品质 - 星级    获取合成进程
function FightSoulWGData:GetFightSoulTipsShowComposeData(color, star)
    local empty_table = {}
    local temp_cfg = ((self.fightsoul_tips_compose_show_cfg or empty_table)[color] or empty_table)[star]

	if temp_cfg == nil then
        local compose_tips_cfg = self:GetComposeTipsCfg(color, star)
		if not IsEmptyTable(compose_tips_cfg) then
			local equip_list = {}
			for i = 1, 3 do
				local equip_str = compose_tips_cfg["sixiang_" .. i] or ""
				local show_list = Split(equip_str, ",")
				local c_color = tonumber(show_list[1])
				local c_star = tonumber(show_list[2])
				if c_color and c_star then
					equip_list[#equip_list + 1] = {color = c_color, star = c_star}
				end
			end

			self.fightsoul_tips_compose_show_cfg = self.fightsoul_tips_compose_show_cfg or {}
			self.fightsoul_tips_compose_show_cfg[color] = self.fightsoul_tips_compose_show_cfg[color] or {}
			self.fightsoul_tips_compose_show_cfg[color][star] = equip_list
		end
	end

    return ((self.fightsoul_tips_compose_show_cfg or empty_table)[color] or empty_table)[star]
end

-- 【魂骨】获取魂骨属性
function FightSoulWGData:GetBoneAttrCfg(bone_part, color, star)
    local empty_table = {}
    return ((self.bone_attr_cfg[bone_part] or empty_table)[color] or empty_table)[star]
end

-- 【魂骨】获取套装类型配置
function FightSoulWGData:GetBoneSuitTypeCfg(suit_type)
    return self.bone_suit_type_cfg[suit_type]
end

-- 【魂骨】获取套装名
function FightSoulWGData:GetBoneSuitName(suit_type, fs_type)
    fs_type = fs_type or 0
    return (self.bone_suit_type_cfg[suit_type] or {})["name_" .. fs_type] or ""
end

-- 【魂骨】获取套装标签底
function FightSoulWGData:GetBoneSuitFlagBgRes(suit_type)
    return (self.bone_suit_type_cfg[suit_type] or {}).bg_res or ""
end

-- 【魂骨】获取套装配置
function FightSoulWGData:GetBoneSuitCfg(fs_type, suit_type, color)
    local empty_table = {}
    return ((self.bone_suit_cfg[fs_type] or empty_table)[suit_type] or empty_table)[color]
end

-- 【魂骨】获取套装件数配置
function FightSoulWGData:GetBoneSuitNumCfg(fs_type, suit_type, color, num)
    local empty_table = {}
    return (((self.bone_suit_cfg[fs_type] or empty_table)[suit_type]
        or empty_table)[color] or empty_table)[num]
end

-- 【魂骨】获取部位配置
function FightSoulWGData:GetBonePartCfg(bone_part)
    return self.bone_part_cfg[bone_part]
end

-- 【魂骨】获取部位预览虚拟物品id
function FightSoulWGData:GetBonePartPreviewId(bone_part, fs_type)
    return (self.bone_part_preview_list[bone_part] or {})[fs_type]
end

-- 【魂骨】获取部位模型部位加载方式
function FightSoulWGData:GetBonePartModelLoadType(bone_part, fs_type)
    return (self.bone_part_model_load_type_list[bone_part] or {})[fs_type] or 0
end

-- 【魂骨】获取部位单个模型展示资源id
function FightSoulWGData:GetBonePartSingleModelId(bone_part, fs_type)
    return (self.bone_part_model_single_res_list[bone_part] or {})[fs_type] or 0
end

-- 【魂骨】当前魂骨强化配置
function FightSoulWGData:GetBoneStrengthCfg(bone_part, level)
    return (self.bone_strength_cfg[bone_part] or {})[level]
end

-- 【魂骨】魂骨强化材料列表
function FightSoulWGData:GetBoneStrengthStuffList()
    return self.bone_strength_stuff
end

-- 【魂骨】是否是魂骨强化材料
function FightSoulWGData:GetIsBoneStrengthStuffId(item_id)
    return self.bone_strength_stuff[item_id] ~= nil
end

-- 【魂骨】当前魂骨强化最大等级
function FightSoulWGData:GetBoneStrengthMaxLevel(bone_part)
    return self.bone_strength_max_level_list[bone_part] or 0
end

function FightSoulWGData:GetBoneStrengthIsMaxLevel(bone_part, level)
    level = level or 0
    local max_level = self:GetBoneStrengthMaxLevel(bone_part)
    return level >= max_level
end

-- 【魂骨】当前魂骨强化属性
function FightSoulWGData:GetBoneStrengthAttr(bone_part, level)
    local cur_cfg = self:GetBoneStrengthCfg(bone_part, level)
    local next_cfg = self:GetBoneStrengthCfg(bone_part, level + 1)
    local attr_list = EquipWGData.GetSortAttrListHaveNextByCfg(cur_cfg, next_cfg)
    return attr_list
end

-- 【魂骨】获取魂骨合成配置
function FightSoulWGData:GetBoneComposeCfg(color, star)
    return (self.bone_compose_cfg[color] or {})[star]
end

-- 【魂骨】融合界面属性列表
function FightSoulWGData:GetBoneComposeAttr(cur_item_data, next_item_data)
    local cur_item_cfg = self:GetBoneAttrCfg(cur_item_data.bone_part, cur_item_data.color, cur_item_data.star)
    local next_item_cfg = self:GetBoneAttrCfg(next_item_data.bone_part, next_item_data.color, next_item_data.star)
    local attr_list = EquipWGData.GetSortAttrListHaveNextByCfg(cur_item_cfg, next_item_cfg)

    return attr_list
end

--【魂骨】获取魂骨提示合成历程配置
function FightSoulWGData:GetBoneComposeTipsCfg(color, star)
    return (self.fs_bone_compose_tips_cfg[color] or {})[star]
end

-- 【魂骨】通过品质 - 星级    获取合成进程
function FightSoulWGData:GetFightSoulTipsShowBoneComposeData(color, star)
    local empty_table = {}
    local temp_cfg = ((self.fs_tips_bone_compose_show_cfg or empty_table)[color] or empty_table)[star]

	if temp_cfg == nil then
        local compose_tips_cfg = self:GetBoneComposeTipsCfg(color, star)
		if not IsEmptyTable(compose_tips_cfg) then
			local equip_list = {}
			for i = 1, 3 do
				local equip_str = compose_tips_cfg["sixiang_" .. i] or ""
				local show_list = Split(equip_str, ",")
				local c_color = tonumber(show_list[1])
				local c_star = tonumber(show_list[2])
				if c_color and c_star then
					equip_list[#equip_list + 1] = {color = c_color, star = c_star}
				end
			end

			self.fs_tips_bone_compose_show_cfg = self.fs_tips_bone_compose_show_cfg or {}
			self.fs_tips_bone_compose_show_cfg[color] = self.fs_tips_bone_compose_show_cfg[color] or {}
			self.fs_tips_bone_compose_show_cfg[color][star] = equip_list
		end
	end

    return ((self.fs_tips_bone_compose_show_cfg or empty_table)[color] or empty_table)[star]
end

-- 获取tips展示技能cfg
function FightSoulWGData:GetTipSkillCfgList(sixiang_type)
    local cfg_list = self.fight_soul_system_cfg.hungu_suit_skill
    if IsEmptyTable(cfg_list) then
        return
    end

    local data_list = {}
    for _,cfg in ipairs(cfg_list) do
        if cfg.sixiang_type == sixiang_type then
            data_list[cfg.same_num] = data_list[cfg.same_num] or {}
            table.insert(data_list[cfg.same_num], cfg)
        end
    end

    return data_list
end
