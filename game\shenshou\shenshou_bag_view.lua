ShenShouBagView = ShenShouBagView or BaseClass(SafeBaseView)

function ShenShouBagView:__init()
	self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(814, 524)})
	self:AddViewResource(0, "uis/view/shenshou_ui_prefab", "layout_shenshou_bag_view")
end

function ShenShouBagView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.ShenShou.SoulRingBagViewTitle

    if not self.storage_grid then
		self.storage_grid = ShenShouGrid.New()   
        self.storage_grid:SetStartZeroIndex(false)              
		self.storage_grid:CreateCells({col = 9, cell_count = 36, change_cells_num = 2, list_view = self.node_list["ph_bag_grid"], itemRender = ShenShouBagEquipCell})
		self.storage_grid:SetSelectCallBack(BindTool.Bind1(self.SelectBagCellCallBack, self))
    end

    XUI.AddClickEventListener(self.node_list.btn_equip_compose, BindTool.Bind(self.OnClickEquipComposeBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_get_equip, BindTool.Bind(self.OnClickGetEquipBtn, self))
end

function ShenShouBagView:ReleaseCallBack()
    if self.storage_grid then
        self.storage_grid:DeleteMe()
        self.storage_grid = nil
    end
end

function ShenShouBagView:OnFlush()
    local data_list = ShenShouWGData.Instance:GetShenShouBagDataList()
    self.storage_grid:SetDataList(data_list)
end

function ShenShouBagView:SelectBagCellCallBack(cell)
    
end

function ShenShouBagView:OnClickEquipComposeBtn()
    self:Close()
	FunOpen.Instance:OpenViewNameByCfg("other_compose#other_compose_eq_hecheng_shenshou")
end

function ShenShouBagView:OnClickGetEquipBtn()
    self:Close()
	FunOpen.Instance:OpenViewByName(GuideModuleName.WorldServer, "worserv_boss_mh")
end

---------------------------------------------------------------------------------
ShenShouBagEquipCell = ShenShouBagEquipCell or BaseClass(ShenShouEquipCell)

function ShenShouBagEquipCell:__init()
    self.item_tip_from = ShenShouEquipTip.FROM_SHENSHOUBAG

    ShenShouEquipCell.__init(self)
end