FightTopText = FightTopText or BaseClass(SafeBaseView)

function FightTopText:__init()
	self.view_layer = UiLayer.PopTop
	self.ui_config = {nil, "FightTopText"}
	self.text_t = {}
	self.active_close = false
	self.open_tween = nil
	self.close_tween = nil
end

function FightTopText:ReleaseCallBack()
	self.canvas_transform = nil
	self.canvas = nil
end

function FightTopText:LoadCallBack()
	if self.canvas then
		self.canvas_transform = self.canvas.transform
	end
end

function FightTopText:ShowIndexCallBack()
end

--属性变化
local attr_add_t = {}
local attr_text_is_loading = false
function FightTopText:ShowAttrText(attr, num)
	-- 小鸭疾走不要飘移动速度的飘字
	if attr == ATTR_ADD_TXT_T.move_speed and SceneType.KF_DUCK_RACE == Scene.Instance:GetSceneType() then
		return
	end

	if attr then
		table.insert(attr_add_t, {attr = attr, num = num})
		if #attr_add_t > 20 then
			table.remove(attr_add_t, 1)
		end

	end

	if not self:IsOpen() then
		self:IsOpen()
	end

	if self.attr_add_timer or attr_text_is_loading then
		return
	end

	local info = table.remove(attr_add_t, 1)
	if info == nil then
		return
	end

	if not self.canvas then
		return
	end

	attr_text_is_loading = true
	local res_async_loader = AllocAsyncLoader(self, "AttributeAdd" .. info.attr)
	res_async_loader:SetIsUseObjPool(true)
	res_async_loader:SetObjAliveTime(0.9)
	res_async_loader:SetParent(self.canvas_transform)
	res_async_loader:Load("uis/view/floatingtext_ui_prefab", "AttributeAdd", function(obj)
		attr_text_is_loading = false
		if not obj then
			return
		end

		if nil == MainCamera then
			if res_async_loader then
				res_async_loader:Destroy()
			end
			return
		end
		obj = U3DObject(obj)
		obj:SetActive(true)
		local obj_transform = obj.transform
		local image = obj_transform:Find("Image")

		local image_component = U3DObject(image.gameObject, obj_transform, self)
		if image_component then
			image_component.image:LoadSprite("uis/view/attribute_atlas", info.attr, function ()
				image_component.image:SetNativeSize()
			end)
		end
		local text = image:Find("Text")
		local text_component = text:GetComponent(typeof(UnityEngine.UI.Text))
		if text_component then
			text_component.text = info.num
		end
		self.attr_add_timer = GlobalTimerQuest:AddDelayTimer(function ()
			self.attr_add_timer = nil
			attr_text_is_loading = false
			self:ShowAttrText()
		end, 0.3)

		local animator = obj.animator
		animator:WaitEvent("exit", function(param)
			if self.text_t and self.text_t[obj.gameObject] then
				obj:SetActive(false)
				self.text_t[obj.gameObject] = nil
				if res_async_loader then
					res_async_loader:Destroy()
				end
			end
		end)
		obj.transform:SetParent(self.canvas_transform, false)
		obj.rect.anchoredPosition = Vector2(450, 0)
		self.text_t[obj.gameObject] = true
	end)
end

function FightTopText:DoUplevelText(text)
	if self.uplevel_delay == nil and text then
		local root_trans = Scene.Instance:GetMainRole().draw_obj:GetRoot().transform

	    local pos = root_trans.position
	    local bundle, asset = ResPath.GetEnvironmentCommonEffect("eff_levelup1")
	    EffectManager.Instance:PlayControlEffect(self, bundle, asset, Vector3(pos.x, pos.y, pos.z), nil, root_trans)
		self.uplevel_delay = GlobalTimerQuest:AddDelayTimer(function ()
	        self.uplevel_delay = nil
	        self:DoUplevelText()
	    end, 0.5)
	    AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.Levelup))
	end
end

--升级特效
function FightTopText:UplevelText(text)
	if not self.canvas then
		return
	end

	if not self:IsOpen() then
		self:Open()
	end

	local attach = Scene.Instance:GetMainRole().draw_obj:GetAttachPoint(AttachPoint.UI)
	if nil == attach then return end
	local attach_position = attach.position

	self.up_level_text_loader = self.up_level_text_loader or AllocAsyncLoader(self, "FightText_UpLevel_Effect")
	self.up_level_text_loader:SetParent(self.canvas_transform)
	self.up_level_text_loader:SetObjAliveTime(5)
	self.up_level_text_loader:Load("uis/view/floatingtext_ui_prefab", "UpLevel", function(obj)
		if not obj then
			return
		end

		if nil == MainCamera then
			if self.up_level_text_loader then
				self.up_level_text_loader:Destroy()
			end
			return
		end
		obj:SetActive(true)
		local text_obj = obj.transform:FindHard("Image/Text")
		local text_component = text_obj:GetComponent(typeof(UnityEngine.UI.Text))

		if text_component then
			text_component.text = string.format(Language.Common.LevelNormal, text)
		end
		local animator = obj:GetComponent(typeof(UnityEngine.Animator))
		animator:WaitEvent("exit", function(param)
			if self.text_t and self.text_t[obj] then
				obj:SetActive(false)
				self.text_t[obj] = nil
				if self.up_level_text_loader then
					self.up_level_text_loader:Destroy()
				end
			end
		end)

		obj.transform.localPosition = Vector3(0, 0, 0)
		self.text_t[obj] = true
	end)
end

function FightTopText:DoMoneyEffect(money_type, show_transform)
	show_transform = show_transform or self.canvas_transform
	local eff = MONEY_BAR_EFFECT[money_type]
	local bundle_name, asset_name = ResPath.GetEffectUi(eff.ZHAKAI)
	EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, show_transform, 1.5)
	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.EffectXianYuHuoDe, false, true))
end
