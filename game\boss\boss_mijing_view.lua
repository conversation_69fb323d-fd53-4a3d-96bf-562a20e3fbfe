MiJingBossReq = {
	INFO = 0,
	GET = 1,
	REMEDY = 2,
	BUY = 3
}

function BossView:DeleteMiJingBossView()
	if self.remedy_alert1 then
		self.remedy_alert1:DeleteMe()
		self.remedy_alert1 = nil
	end
	if self.remedy_alert2 then
		self.remedy_alert2:DeleteMe()
		self.remedy_alert2 = nil
	end
	if self.remedy_alert3 then
		self.remedy_alert3:DeleteMe()
		self.remedy_alert3 = nil
	end
end

function BossView:InitMiJingBossView()
	self.node_list.btn_mj_play_des.button:AddClickListener(BindTool.Bind(self.MiJingPlayInfo,self))
	self.node_list.btn_add.button:AddClickListener(BindTool.Bind(self.MiJingAddPower,self))
	self.node_list.btn_get.button:AddClickListener(BindTool.Bind(self.MiJingGetPower,self))

	local time_cfg = BossWGData.Instance:GetMiJingTimeShowCfg()
	local noon_cfg = time_cfg[1]
	local after_cfg = time_cfg[2]
	self.node_list.time_noon.text.text = string.format(Language.Boss.MiJingTimeNoon,noon_cfg[3],noon_cfg[4],noon_cfg[1],noon_cfg[2])
	self.node_list.time_afternoon.text.text = string.format(Language.Boss.MiJingTimeAfterNoon,after_cfg[3],after_cfg[4],after_cfg[1],after_cfg[2])

	self.remedy_alert1 = Alert.New()
	self.remedy_alert2 = Alert.New()
	self.remedy_alert3 = Alert.New()
end

function BossView:OnFlushMiJingBossView()
	if not self:IsOpen() then return end
	local is_right_time = BossWGData.Instance:GetMiJingTime()
	local is_now_right_time = BossWGData.Instance:GetNowRightTime()
	if not self.flag_delay then return end
	local can_get_power = BossWGData.Instance:GetMiJingPower()
	-- self.node_list.txt_btn_get.text.text = is_right_time and Language.Boss.GetMiJingPower or Language.Boss.RemedyMiJingPower
	if is_now_right_time then
		self.node_list.txt_btn_get.text.text = Language.Boss.RemedyMiJingPower
	end
	local get_flag = BossWGData.Instance:GetPhysicalFlag()
	if IsEmptyTable(get_flag) then return end

	-- print_error("is_right_time",is_right_time,"can_get_power",can_get_power,"get_flag",get_flag,"mj_key",mj_key)

	local mj_key = BossWGData.Instance:GetNowRightFlag()
	if mj_key == 0 then
		self.node_list.noon_tip.text.text = ""
		self.node_list.afternoon_tip.text.text = ""
		self.node_list.txt_btn_get.text.text = Language.Boss.GetMiJingPower
	elseif mj_key == 1 then
		if is_right_time then
			self.node_list.noon_tip.text.text = get_flag[1] == 0 and Language.Boss.CanGet or Language.Boss.HasGet
		else
			self.node_list.noon_tip.text.text = get_flag[1] == 0 and Language.Boss.CanRemedy or Language.Boss.HasGet
		end
		self.node_list.txt_btn_get.text.text = is_right_time and Language.Boss.GetMiJingPower or Language.Boss.RemedyMiJingPower
		self.node_list.afternoon_tip.text.text = ""
	else
		if is_right_time then
			self.node_list.noon_tip.text.text = get_flag[1] == 0 and Language.Boss.CanRemedy or Language.Boss.HasGet
			self.node_list.afternoon_tip.text.text = get_flag[2] == 0 and Language.Boss.CanGet or Language.Boss.HasGet
			if get_flag[2] == 0 then
				self.node_list.txt_btn_get.text.text = Language.Boss.GetMiJingPower
			else
				self.node_list.txt_btn_get.text.text = get_flag[1] == 0 and Language.Boss.RemedyMiJingPower or Language.Boss.GetMiJingPower
			end
		else
			self.node_list.noon_tip.text.text = get_flag[1] == 0 and Language.Boss.CanRemedy or Language.Boss.HasGet
			self.node_list.afternoon_tip.text.text = get_flag[2] == 0 and Language.Boss.CanRemedy or Language.Boss.HasGet
			local state = get_flag[1] == 1 or get_flag[2] == 0
			self.node_list.txt_btn_get.text.text = state and Language.Boss.RemedyMiJingPower or Language.Boss.GetMiJingPower
		end
	end


	XUI.SetGraphicGrey(self.node_list.btn_get,not can_get_power)
	self.flag_delay = false
end

function BossView:ChangeMJFalgDelay()
	self.flag_delay = true
end

function BossView:MiJingPlayInfo()
	RuleTip.Instance:SetContent(Language.Boss.MJPlayDes, Language.Boss.PlayTitle)
end

function BossView:MiJingAddPower()
	local role_vip = RoleWGData.Instance.role_vo.vip_level
	local cur_need_vip = BossWGData.Instance:GetMiJingVipByLayer()
	local can_get_ex_physical_vip = BossWGData.Instance:CanGetExPhysical()
	if role_vip < can_get_ex_physical_vip then
		self.remedy_alert1:SetLableString(string.format(Language.Boss.VipNotEnough,can_get_ex_physical_vip))
		self.remedy_alert1:SetOkString(Language.Boss.GoToVip)
		self.remedy_alert1:SetOkFunc(function()
			FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge, "recharge_vip")
		end)
		self.remedy_alert1:Open()
		self.remedy_alert1:UseOne()
	else
		local gold,power_num = BossWGData.Instance:GetAddCost()
		local rest_ex_times = BossWGData.Instance:GetRestExTimes()
		if rest_ex_times == nil then return end
		local color = rest_ex_times == 0 and COLOR3B.RED or COLOR3B.GREEN
		if gold == nil or power_num == nil then return end
		local str = string.format(Language.Boss.AddCost,gold,power_num,ToColorStr(rest_ex_times,color))
		str = role_vip < COMMON_CONSTS.MAX_VIP_LEVEL and str .. Language.Boss.AddCostEx or str
		self.remedy_alert2:SetTextLineSpace(1.8)
		self.remedy_alert2:SetLableString(str)
		self.remedy_alert2:SetOkFunc(function()
			BossWGCtrl.Instance:SendCSSecretBossPhysicalReq(MiJingBossReq.BUY)
		end)
		self.remedy_alert2:Open()
	end

end

function BossView:MiJingGetPower()
	local can_get_power = BossWGData.Instance:GetMiJingPower()
	if not can_get_power then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.NoMiJingPower)
		return
	end

	local own_power = BossWGData.Instance:GetNowOwnPower()
	local get_power_num,limit_power_num = BossWGData.Instance:GetCanGainPowerNum()

	local is_right_time = BossWGData.Instance:GetMiJingTime()
	local is_now_right_time = BossWGData.Instance:GetNowRightTime()
	if is_right_time then
		if is_now_right_time then
			self:RemedyPhysical()
			return
		end
		--领取体力
		if own_power + get_power_num > limit_power_num then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.LimitPower)
			return
		end
		BossWGCtrl.Instance:SendCSSecretBossPhysicalReq(MiJingBossReq.GET)
	else
		--补签体力
		self:RemedyPhysical()
	end
end

function BossView:SetMJPhysical()
	local own_power = BossWGData.Instance:GetNowOwnPower()
	local get_power_num,limit_power_num = BossWGData.Instance:GetCanGainPowerNum()
	if self.node_list.power_value then
		self.node_list.power_value.text.text = own_power .. "/" .. limit_power_num
	end
	self.flag_delay = true
end

function BossView:RemedyPhysical()
	local gold,power_num = BossWGData.Instance:GetRemedyCost()
	if gold == nil or power_num == nil then return end
	self.remedy_alert3:SetLableString(string.format(Language.Boss.RemedyCost,gold,power_num))
	self.remedy_alert3:SetOkFunc(function()
		BossWGCtrl.Instance:SendCSSecretBossPhysicalReq(MiJingBossReq.REMEDY)
	end)
	self.remedy_alert3:Open()
end