GloryCrystalCollectRewardView = GloryCrystalCollectRewardView or BaseClass(SafeBaseView)
function GloryCrystalCollectRewardView:__init()
	self:SetMaskBg(true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)

	self:AddViewResource(0, "uis/view/glory_crystal_ui_prefab", "layout_glory_crystal_collect_reward_view")
end

function GloryCrystalCollectRewardView:ReleaseCallBack()
	if self.task_list ~= nil then
		self.task_list:DeleteMe()
		self.task_list = nil
	end
end

function GloryCrystalCollectRewardView:LoadCallBack()
	self.task_list = AsyncListView.New(GloryCrystalCollectRewardTaskRender, self.node_list.collect_task_list)

	self:ChangeViewStyle()
end

function GloryCrystalCollectRewardView:ChangeViewStyle()
	local cfg = GloryCrystalWGData.Instance:GetCurViewStyle()
	if not cfg then
		return
	end

	local bundle, asset = ResPath.GetRawImagesPNG("a3_txxy_di5_" .. cfg.color_index)
	if self.node_list.collect_reward_bg then
		self.node_list.collect_reward_bg.raw_image:LoadSprite(bundle, asset, function()
			self.node_list.collect_reward_bg.raw_image:SetNativeSize()
		end)
	end
end

function GloryCrystalCollectRewardView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			self:FlushList()
		end
	end
end

function GloryCrystalCollectRewardView:FlushList()
	local task_list = GloryCrystalWGData.Instance:GetCollectTaskList()
	if IsEmptyTable(task_list) then
		return
	end

	self.task_list:SetDataList(task_list)
end

-------------------------------------------------------------------------------------------------------
GloryCrystalCollectRewardTaskRender = GloryCrystalCollectRewardTaskRender or BaseClass(BaseRender)
function GloryCrystalCollectRewardTaskRender:__init()
	self.reward_item_root = AsyncListView.New(ItemCell, self.node_list.reward_item_root)
	self.reward_item_root:SetStartZeroIndex(true)
end

function GloryCrystalCollectRewardTaskRender:__delete()
	if self.reward_item_root then
		self.reward_item_root:DeleteMe()
		self.reward_item_root = nil
	end
end

function GloryCrystalCollectRewardTaskRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_go, BindTool.Bind(self.OnClickGo, self))
	XUI.AddClickEventListener(self.node_list.btn_get_reward, BindTool.Bind(self.OnClickGetEveryTaskReward, self))
end

function GloryCrystalCollectRewardTaskRender:OnFlush()
	if not self.data then
		return
	end

	local reward_cfg = GloryCrystalWGData.Instance:GetCollectRewardListByTaskSeq(self.data.task_seq)
	if not reward_cfg then
		return
	end

	self.reward_item_root:SetDataList(reward_cfg.item_reward)

	self.node_list.desc.text.text = reward_cfg.task_des
	local num_str = (self.data.task_process >= self.data.task_num) and Language.GloryCrystal.TaskNumText1 or Language.GloryCrystal.TaskNumText2
	self.node_list.num_text.text.text = string.format(num_str, self.data.task_process, self.data.task_num)
	self.node_list.pro_reward_slider.slider.value = self.data.task_process / self.data.task_num

	self.node_list.btn_go:SetActive(self.data.flag == SYSTEM_FORCESHOW_TASK_STATUS.NONE)
	self.node_list.complete_flag:SetActive(self.data.flag == SYSTEM_FORCESHOW_TASK_STATUS.HAS_FETCH)
	self.node_list.btn_get_reward:SetActive(self.data.flag == SYSTEM_FORCESHOW_TASK_STATUS.CAN_FETCH)
	self.node_list.get_reward_remind:SetActive(self.data.flag == SYSTEM_FORCESHOW_TASK_STATUS.CAN_FETCH)
end

function GloryCrystalCollectRewardTaskRender:OnClickGo()
	if not self.data then
		return
	end

	local reward_cfg = GloryCrystalWGData.Instance:GetCollectRewardListByTaskSeq(self.data.task_seq)
	if not reward_cfg then
		return
	end

	FunOpen.Instance:OpenViewNameByCfg(reward_cfg.open_panel)
	GloryCrystalWGCtrl.Instance:CloseCollectRewardView()
end

function GloryCrystalCollectRewardTaskRender:OnClickGetEveryTaskReward()
	if not self.data then
		return
	end

	local reward_cfg = GloryCrystalWGData.Instance:GetCollectRewardListByTaskSeq(self.data.task_seq)
	if not reward_cfg then
		return
	end

	if self.data.flag == SYSTEM_FORCESHOW_TASK_STATUS.CAN_FETCH then
		GloryCrystalWGCtrl.Instance:ReqGloryCrystalInfo(OA_GLORY_CRYSTAL_OPERATE_TYPE.FETCH_COLLECT_REWARD, self.data.task_seq)
	end
end
