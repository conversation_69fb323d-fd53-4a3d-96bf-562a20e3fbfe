--------------------------------------------------
--自动穿装备
--------------------------------------------------
AutomaticUseEquipmentView = AutomaticUseEquipmentView or BaseClass(SafeBaseView)

function AutomaticUseEquipmentView:__init()
	self.view_name = "AutomaticUseEquipmentView"
	self.calc_active_close_ui_volume = false

	self.is_checkvisible_onopen = true
	self.call_back = nil
	self.item_list = {}
	self.is_xiaogui = false
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_third2_panel")
	self:AddViewResource(0, "uis/view/guide_ui_prefab", "layout_auto_equip_view")
	self.view_layer = UiLayer.PopWhite
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self:SetMaskBg(false)	
	self.item = self.node_list.item
	self.item_text = self.node_list.item_text
	self.equip_data_change_fun = BindTool.Bind(self.OnEquipDataChange, self)
end

function AutomaticUseEquipmentView:__delete()

end

function AutomaticUseEquipmentView:CloseCallBack()
	self.item_list = {}
	if self.timer_quest ~= nil then
		GlobalTimerQuest:CancelQuest(self.timer_quest)
	end
	self.timer_quest = nil
end

function AutomaticUseEquipmentView:ReleaseCallBack()
	EquipWGData.Instance:UnNotifyDataChangeCallBack(self.equip_data_change_fun)

	if self.equip_grid ~= nil then
		self.equip_grid:DeleteMe()
		self.equip_grid = nil
	end

	if self.xiao_gui_item then
		self.xiao_gui_item:DeleteMe()
		self.xiao_gui_item = nil
	end
	self.is_xiaogui = false
end

function AutomaticUseEquipmentView:OpenCallBack()

end

function AutomaticUseEquipmentView:LoadCallBack()
	EquipWGData.Instance:NotifyDataChangeCallBack(self.equip_data_change_fun, false)
end

function AutomaticUseEquipmentView:SetGridStyle()
	if not self.equip_grid then return end

	local celllist = {}
	for i = 0, 9 do
		celllist[i] = {bg = ResPath.GetCommon("cell_100"), bg_ta = RoleInfoView.GetEquipBg(i)}
	end
	self.equip_grid:SetCellSkinStyle(celllist)
end

function AutomaticUseEquipmentView:AddItem(index, item_data, equip_index, start_pos)
	for k,v in pairs(self.item_list) do
		if v.index == index then
			return
		end

		if v.equip_index == equip_index and EquipmentWGData.Instance:GetEquipPingfen(v.item_data) > EquipmentWGData.Instance:GetEquipPingfen(item_data) then
			return
		end
	end
	table.insert(self.item_list, 1, {index = index, id = item_data.item_id, equip_index = equip_index, item_data = item_data, start_pos = start_pos})

	if self.timer_quest ~= nil then
		GlobalTimerQuest:CancelQuest(self.timer_quest)
	end
	self.timer_quest = nil

	if #self.item_list > 0 and not self:IsOpen() then
		self:Open()
	end

	self:UpdateShow()
end

function AutomaticUseEquipmentView:OnFlush()
	if self.item_list[#self.item_list] == nil then
		self:WaitForClose()
		return
	end
end

function AutomaticUseEquipmentView:UpdateShow()
	if #self.item_list == 0 then
		self:WaitForClose()
		return
	end

	if not EquipWGData.Instance:GetIsBetterEquip(self.item_list[#self.item_list].item_data) then
		self.item_list[#self.item_list] = nil
		self:UpdateShow()
		return
	end

	self:Flush()
end

function AutomaticUseEquipmentView:ClickHandler()
	if self.item_list[#self.item_list] == nil then
		self:WaitForClose()
		return
	end

	local item_id = self.item_list[#self.item_list].id

	if item_id ~= nil then
		local bag_index = self.item_list[#self.item_list].index
		local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(item_id)
		if item_cfg ~= nil and bag_index ~= -1 and GameEnum.ITEM_BIGTYPE_EQUIPMENT == item_type then
			local func2 = function()
				local equip_index = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
				if equip_index ~= -1 then
					BagWGCtrl.Instance:SendUseItem(bag_index, 1, equip_index, item_cfg.need_gold)
				end
			end

			--印记装备替换处理
			local new_equip_data = ItemWGData.Instance:GetGridData(bag_index)
			-- local equip_index = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
			local equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(item_cfg)
			local cur_equip_data = EquipWGData.Instance:GetGridData(equip_body_index)
			local is_need_alert, alert_content = NewYinJiJiChengWGData.Instance:CheckIsNeedAlertWhenEquipLess(cur_equip_data, new_equip_data)
			if is_need_alert then
				TipWGCtrl.Instance:OpenAlertTips(alert_content, func2)
			else
				func2()
			end
		end
	end

	self.item_list[#self.item_list] = nil
	self:UpdateShow()
end

function AutomaticUseEquipmentView:GetDataList()
	local datalist = EquipWGData.Instance:GetDataList()
	
	-- if #datalist > 11 then
	-- 	for k = 12,#datalist do
	-- 		datalist[k] = nil
	-- 	end
	-- end
	local temp_list = {}
	if datalist and next(datalist) ~= nil then
		for i,v in pairs(datalist) do
			if v.index < GameEnum.EQUIP_INDEX_HUFU then
				temp_list[v.index] = v
			end
		end
	end
	return temp_list or {}
end

function AutomaticUseEquipmentView:WaitForClose()
	if self.timer_quest ~= nil then
		GlobalTimerQuest:CancelQuest(self.timer_quest)
	end

	self.timer_quest = GlobalTimerQuest:AddDelayTimer(function()
		self:Close()
	end, 2)
end

--主角身上的列表装备变化
function AutomaticUseEquipmentView:OnEquipDataChange(item_id, index, reason)
	if self:IsOpen() and self.equip_grid ~= nil then
		local item_data = EquipWGData.Instance:GetGridData(index)
		local is_select = false
		local cur_cell = nil
		if nil ~= item_data then
			cur_cell = self.equip_grid:GetCell(index)
			if nil ~= cur_cell then
				is_select = cur_cell:IsSelect()
			end
		end
		self.equip_grid:UpdateOneCell(index, item_data)
		if nil ~= cur_cell then
			cur_cell:SetSelect(is_select)
		end
	end
end