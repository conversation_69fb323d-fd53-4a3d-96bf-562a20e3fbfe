ControlBeastsCultivateWGView = ControlBeastsCultivateWGView or BaseClass(SafeBaseView)

function ControlBeastsCultivateWGView:__init()
	self:SetMaskBg(false, true)
    self.default_index = TabIndex.beasts_alchemy_strengthen

    local common_bundle = "uis/view/common_panel_prefab"
	local bundle_name = "uis/view/control_beasts_alchemy_ui_prefab"

	self:AddViewResource(0, bundle_name, "layout_beasts_alchemy_panel")
	self:AddViewResource(0, bundle_name, "layout_beasts_hole_grid")
	self:AddViewResource({TabIndex.beasts_alchemy_strengthen, TabIndex.beasts_alchemy_succinct}, bundle_name, "layout_beasts_alchemy_grid")
	self:AddViewResource(TabIndex.beasts_alchemy_strengthen, bundle_name, "********************************")
	self:AddViewResource(TabIndex.beasts_alchemy_succinct, bundle_name, "layout_beasts_alchemy_succinct")
	self:AddViewResource({TabIndex.beasts_alchemy_inherit, TabIndex.beasts_alchemy_break}, bundle_name, "layout_beasts_alchemy_bag_grid")
	self:AddViewResource(TabIndex.beasts_alchemy_inherit, bundle_name, "layout_beasts_alchemy_inherit")
	self:AddViewResource(TabIndex.beasts_alchemy_break, bundle_name, "layout_beasts_alchemy_break")
	self:AddViewResource(0, bundle_name, "VerticalTabbar")
	self:AddViewResource(0, bundle_name, "layout_beasts_alchemy_panel")

	self.remind_tab = {
		{RemindName.BeastsAlchemyStrengthen},
		{RemindName.BeastsAlchemySuccinct},
		{RemindName.BeastsAlchemyInherit},
		{RemindName.BeastsAlchemyBreak},
	}
end

function ControlBeastsCultivateWGView:__delete()
end

function ControlBeastsCultivateWGView:LoadCallBack()
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:Init(Language.ContralBeastsAlchemy.TabGrop, nil, "uis/view/control_beasts_alchemy_ui_prefab", nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.ControlBeastsCultivateWGView, self.tabbar)
	end

    if not self.beasts_hole_list then
		self.beasts_hole_list = AsyncListView.New(BeastsCultivateHoleRender,self.node_list.layout_beasts_hole_list)
		self.beasts_hole_list:SetSelectCallBack(BindTool.Bind(self.BeastsAlchemyHoleSelect,self))
		self.beasts_hole_list:SetLimitSelectFunc(BindTool.Bind(self.BeastsAlchemyHoleSelectLimit,self))
		self.beasts_hole_list:SetRefreshCallback(BindTool.Bind(self.BeastsAlchemyHoleRefresh,self))
	end

	XUI.AddClickEventListener(self.node_list.beasts_cultivate_rule_btn, BindTool.Bind(self.OnClickBeastsCultivateRuleBtn, self))
end

function ControlBeastsCultivateWGView:ReleaseCallBack()
	self:ReleaseStrengthenViewCallBack()
	self:ReleaseSuccinctViewCallBack()
	self:ReleaseInheritViewCallBack()
	self:ReleaseBreakViewCallBack()

	self.fight_slot_index = nil
	self.slot_index = nil
	self.fight_slot_select_index = nil

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.beasts_hole_list then
		self.beasts_hole_list:DeleteMe()
		self.beasts_hole_list = nil
	end
end

function ControlBeastsCultivateWGView:LoadIndexCallBack(index)
	if index == TabIndex.beasts_alchemy_strengthen then
		self:LoadStrengthenViewCallBack()
	elseif index == TabIndex.beasts_alchemy_succinct then
		self:LoadSuccinctViewCallBack()
	elseif index == TabIndex.beasts_alchemy_inherit then
		self:LoadInheritViewCallBack()
	elseif index == TabIndex.beasts_alchemy_break then
		self:LoadBreakViewCallBack()
	end
end

function ControlBeastsCultivateWGView:OpenCallBack()
	if self.show_index == TabIndex.beasts_alchemy_strengthen then
		self:OpenBattleViewCallBack()
	elseif self.show_index == TabIndex.beasts_alchemy_succinct then
		self:OpenCultureViewCallBack()
	elseif self.show_index == TabIndex.beasts_alchemy_inherit then
		self:OpenAlchemyViewCallBack()
	elseif self.show_index == TabIndex.beasts_alchemy_break then
		self:OpenBookViewCallBack()
	end
end

function ControlBeastsCultivateWGView:CloseCallBack()
	self:CloseStrengthenViewCallBack()
	self:CloseSuccinctViewCallBack()
	self:CloseInheritViewCallBack()
	self:CloseBreakViewCallBack()
end

function ControlBeastsCultivateWGView:ShowIndexCallBack(index)
	if index == TabIndex.beasts_alchemy_strengthen then
		self:ShowStrengthenViewCallBack()
	elseif index == TabIndex.beasts_alchemy_succinct then
		self:ShowSuccinctViewCallBack()
	elseif index == TabIndex.beasts_alchemy_inherit then		-- 合成时整理一下孵化背包
		self:ShowInheritViewCallBack()
	elseif index == TabIndex.beasts_alchemy_break then
		self:ShowBreakViewCallBack()
	end

	if index ~= TabIndex.beasts_alchemy_strengthen then		-- 调用一下强化的关闭，关掉自动
		self:CloseStrengthenViewCallBack()
	end
end

function ControlBeastsCultivateWGView:FlushCurShowView(param_t, key)
	self:Flush(self.show_index, key, param_t)
end

function ControlBeastsCultivateWGView:OnFlush(param_t, index)
	self:FlushNowHoleList()

	if index == TabIndex.beasts_alchemy_strengthen then
		self:FlushStrengthenViewCallBack(param_t)
	elseif index == TabIndex.beasts_alchemy_succinct then
		self:FlushSuccinctViewCallBack(param_t)
	elseif index == TabIndex.beasts_alchemy_inherit then
		self:FlushInheritViewCallBack(param_t)
	elseif index == TabIndex.beasts_alchemy_break then
		self:FlushBreakViewCallBack(param_t)
	end
end

-- 升级成功或突破成功特效
function ControlBeastsCultivateWGView:CultivateOperateFinalEffect(ui_effect_type)
	local node_root = self.node_list["operate_effect_root"]
	TipWGCtrl.Instance:ShowEffect({effect_type = ui_effect_type,
						is_success = true, pos = Vector2(0, 0), parent_node = node_root})

	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
end

-- 修炼规则
function ControlBeastsCultivateWGView:OnClickBeastsCultivateRuleBtn()
	local rule_tip = RuleTip.Instance
	local rule_title = Language.ContralBeastsAlchemy.RuleTitle
	local rule_content = Language.ContralBeastsAlchemy.RuleContent
	
	rule_tip:SetTitle(rule_title)
	rule_tip:SetContent(rule_content, nil, nil, nil, true)
end
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 传入的上阵位置和装备孔位位置
function ControlBeastsCultivateWGView:SetCurrSelectAlchemy(aim_hole_id, slot_index)
	self.fight_slot_index = aim_hole_id
	self.beast_slot_index = slot_index
end

-- 刷新阵位列表
function ControlBeastsCultivateWGView:FlushNowHoleList()
	local jump_fight_slot = COMMON_CONSTS.NUMBER_ONE
	local list = ControlBeastsWGData.Instance:GetAllBattleBattleList()

	if self.fight_slot_index ~= nil then
		for i, v in ipairs(list) do
			if v and v.hole_id == self.fight_slot_index then
				jump_fight_slot = i
			end
		end
	end

    self.beasts_hole_list:SetDataList(list)
    self.beasts_hole_list:JumpToIndex(jump_fight_slot, 6)
end

-- 当前的空位点击
function ControlBeastsCultivateWGView:BeastsAlchemyHoleSelect(beast_hole_item, cell_index, is_default, is_click)
    if beast_hole_item == nil or is_default or beast_hole_item.data == nil then
        return
    end

	if self.fight_slot_select_index == cell_index and is_click then
		return
	end

	if self.fight_slot_select_index ~= cell_index then
		self.beast_slot_index = nil
	end

	self.fight_slot_select_index = cell_index
    self.fight_slot_index = beast_hole_item.data.hole_id
    self.fight_slot_data = beast_hole_item.data
	self:FlushSelectChangeHole(is_click)
end

-- 点击位置条件限制
function ControlBeastsCultivateWGView:BeastsAlchemyHoleSelectLimit(beast_hole_item)
	if beast_hole_item == nil or beast_hole_item.data == nil then
        return false
    end

	local data = beast_hole_item.data
	if data.state == BEASTS_HOLE_STATUS.NOT_ACTIVE or data.beasts_bag_id == -1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeastsAlchemy.HoleSelectError)
		return true
	end

	if ControlBeastsCultivateWGData.Instance:GetFightBeastAllEquipNotHaveItem(data.hole_id) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeastsAlchemy.HoleSelectError3)
		return true
	end

	return false
end

-- 点击位置条件限制
function ControlBeastsCultivateWGView:BeastsAlchemyHoleRefresh(beast_hole_item)
	if beast_hole_item == nil or beast_hole_item.data == nil then
        return false
    end

	beast_hole_item:RefreshNowIndexRemind(self.show_index)
	return false
end


-- 切换出战位置刷新界面
function ControlBeastsCultivateWGView:FlushSelectChangeHole(is_click)
	if self.show_index == TabIndex.beasts_alchemy_strengthen then
		if is_click then
			self:CloseStrengthenViewCallBack()
		end
		self:ShowStrengthenViewChangeHole()
	elseif self.show_index == TabIndex.beasts_alchemy_succinct then
		self:ShowSuccinctViewChangeHole()
	elseif self.show_index == TabIndex.beasts_alchemy_inherit then		-- 合成时整理一下孵化背包
		self:ShowInheritViewChangeHole()
	elseif self.show_index == TabIndex.beasts_alchemy_break then
		self:ShowBreakViewChangeHole()
	end
end

----------------------------------------BeastsCultivateHoleRender---------------------------------
BeastsCultivateHoleRender = BeastsCultivateHoleRender or BaseClass(BaseRender)
function BeastsCultivateHoleRender:OnFlush()
    if not self.data then
        return 
    end

    self.node_list.lock:CustomSetActive(self.data.state == BEASTS_HOLE_STATUS.NOT_ACTIVE or self.data.beasts_bag_id == -1)
    self.node_list.un_lock:CustomSetActive(self.data.state == BEASTS_HOLE_STATUS.ACTIVE and self.data.beasts_bag_id ~= -1)
    
    if self.data.state == BEASTS_HOLE_STATUS.ACTIVE and self.data.beasts_bag_id ~= -1 then
        local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(self.data.beasts_bag_id)
		local beast_id = ((beast_data or {}).server_data or {}).beast_id or COMMON_CONSTS.NUMBER_ZERO
		local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(beast_id)
		local icon_name = string.format("a3_hs_head_%s", beast_cfg and beast_cfg.chang_head or 30450)
		local beast_name = beast_cfg and beast_cfg.beast_name or ""

		self.node_list.beast_nor_name.text.text = beast_name
		self.node_list.beast_sel_name.text.text = beast_name
		self.node_list.beast_head_icon.image:LoadSprite(ResPath.GetControlBeastsImg(icon_name))
    end
end

-- 刷新红点
function BeastsCultivateHoleRender:RefreshNowIndexRemind(show_index)
	local red = false
	if show_index == TabIndex.beasts_alchemy_strengthen then
		red = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipIsCanCultivateForHole(self.data.hole_id, true, false)
	elseif show_index == TabIndex.beasts_alchemy_succinct then
		red = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipIsCanCultivateForHole(self.data.hole_id, false, true)
	end

	self.node_list.remind:CustomSetActive(red)
end

-- 选中改变
function BeastsCultivateHoleRender:OnSelectChange(is_select)
	self.node_list.select:CustomSetActive(is_select)
	self.node_list.normal:CustomSetActive(not is_select)
	self.node_list.beast_nor_name:CustomSetActive(not is_select)
end