AnswerCircleFollow = AnswerCircleFollow or BaseClass(FollowUi)

function AnswerCircleFollow:__init(vo)
    FollowUi.__init(self, vo)
    self.follow_name_prefab_name = "SceneObjName"
end

function AnswerCircleFollow:OnRootCreateCompleteCallback()
   self:UpdateBgRes()
end

function AnswerCircleFollow:FlushBg()
    if self.vo and self.namebar and self.namebar.UpdateAnscircleImg then
        self.namebar:UpdateAnscircleImg(self.vo.bg_res)
    end
end

function AnswerCircleFollow:FlushVoInfo(vo)
    FollowUi.FlushVoInfo(self, vo)
	self:UpdateBgRes()
end

function AnswerCircleFollow:UpdateBgRes()
    local res = self.vo and self.vo.bg_res
    if res then
        self:FlushBg()
    end
end