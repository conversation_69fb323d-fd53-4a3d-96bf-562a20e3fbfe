﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_ImageToolWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>ginStaticLibs("ImageTool");
		<PERSON><PERSON>Function("IsPhotoPurview", IsPhotoPurview);
		<PERSON><PERSON>Function("RequestPhotoPurview", RequestPhotoPurview);
		<PERSON><PERSON>unction("AddToPhoto", AddToPhoto);
		<PERSON>.EndStaticLibs();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsPhotoPurview(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = Nirvana.ImageTool.IsPhotoPurview();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RequestPhotoPurview(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			System.Action<bool,string> arg0 = (System.Action<bool,string>)ToLua.CheckDelegate<System.Action<bool,string>>(L, 1);
			Nirvana.ImageTool.RequestPhotoPurview(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddToPhoto(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			string arg0 = ToLua.CheckString(L, 1);
			System.Action<bool,string> arg1 = (System.Action<bool,string>)ToLua.CheckDelegate<System.Action<bool,string>>(L, 2);
			Nirvana.ImageTool.AddToPhoto(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

