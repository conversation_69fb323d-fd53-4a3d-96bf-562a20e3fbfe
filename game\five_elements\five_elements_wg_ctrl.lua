require("game/five_elements/five_elements_view")
require("game/five_elements/five_elements_compose_view")
require("game/five_elements/five_elements_knapsack")
require("game/five_elements/five_elements_overview")
require("game/five_elements/five_elements_suit_view")
require("game/five_elements/five_elements_resolve_view")
require("game/five_elements/FEBagCell")
require("game/five_elements/five_elements_talent")
require("game/five_elements/five_elements_cangming_view")
require("game/five_elements/five_elements_wg_data")
require("game/five_elements/five_elements_treasury_wg_data")
require("game/five_elements/five_elements_treasury_view")
require("game/five_elements/five_elements_treasury_reward")
require("game/five_elements/five_elements_treasury_record")
require("game/five_elements/five_elements_treasury_library")
require("game/five_elements/five_elements_cangming_skill_view")
require("game/five_elements/five_elements_cangming_wg_data")
require("game/five_elements/five_elements_daohun_wg_data")
require("game/five_elements/five_elements_daohun_view")
require("game/five_elements/five_elements_qiantianlu_wg_data")
require("game/five_elements/five_elements_qiantianlu_view")

FiveElementsWGCtrl = FiveElementsWGCtrl or BaseClass(BaseWGCtrl)

function FiveElementsWGCtrl:__init()
	if FiveElementsWGCtrl.Instance ~= nil then
		print_error("[FiveElementsWGCtrl] attempt to create singleton twice!")
		return
	end

	FiveElementsWGCtrl.Instance = self
	self.view = FiveElementsView.New(GuideModuleName.FiveElementsView)
	self.compose_view = FiveElementsComposeView.New()
	self.suit_view = FiveElementsSuitView.New()
	self.resolve_view = FiveElementsResolveView.New()
	self.treasury_view = FiveElementsTreasuryView.New(GuideModuleName.FiveElementsTreasuryView)
	self.treasury_reward_view = FiveElementsTreasuryRewardView.New()
	self.treasury_record_view = FiveElementsTreasuryRecordView.New()
	self.treasury_library_view = FiveElementsTreasuryLibraryView.New()
	self.data = FiveElementsWGData.New()
	self.cangming_skill_veiw = FiveElementsCangMingSkillView.New()
	self.cangming_daohun_view = FiveElementsDaohunView.New()
	self.cangming_qiantianlu_view = FiveElementsQiantianluView.New()

	self:RegisterAllProtocals()

	self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
	self.item_data_init_callback = BindTool.Bind1(self.OnItemDataInitCallBack, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_init_callback, true)
end

function FiveElementsWGCtrl:__delete()
	FiveElementsWGCtrl.Instance = nil

	if self.item_data_change_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
		self.item_data_change_callback = nil
	end

	if self.item_data_init_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_init_callback)
		self.item_data_init_callback = nil
	end

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.compose_view then
		self.compose_view:DeleteMe()
		self.compose_view= nil
	end

	if self.suit_view then
		self.suit_view:DeleteMe()
		self.suit_view = nil
	end

	if self.resolve_view then
		self.resolve_view:DeleteMe()
		self.resolve_view = nil
	end

	if self.treasury_view then
		self.treasury_view:DeleteMe()
		self.treasury_view = nil
	end

	if self.treasury_reward_view then
		self.treasury_reward_view:DeleteMe()
		self.treasury_reward_view = nil
	end

	if self.treasury_record_view then
		self.treasury_record_view:DeleteMe()
		self.treasury_record_view = nil
	end

	if self.treasury_library_view then
		self.treasury_library_view:DeleteMe()
		self.treasury_library_view = nil
	end

	if self.cangming_skill_veiw then
		self.cangming_skill_veiw:DeleteMe()
		self.cangming_skill_veiw = nil
	end

	if self.cangming_daohun_view then
		self.cangming_daohun_view:DeleteMe()
		self.cangming_daohun_view = nil
	end

	if self.cangming_qiantianlu_view then
		self.cangming_qiantianlu_view:DeleteMe()
		self.cangming_qiantianlu_view = nil
	end

	if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end
end

function FiveElementsWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(CSFiveElementsOperate)
	self:RegisterProtocol(CSFiveElementsDecompsStone)
	self:RegisterProtocol(SCFiveElementsMsgBag, "OnSCFiveElementsMsgBag")
	self:RegisterProtocol(SCFiveElementsMsgGrid, "OnSCFiveElementsMsgGrid")
	self:RegisterProtocol(SCFiveElementsInfo, "OnSCFiveElementsInfo")
	self:RegisterProtocol(SCFiveElementsPartUpdate, "OnSCFiveElementsPartUpdate")
	self:RegisterProtocol(SCFiveElementsSkillUpdate, "OnSCFiveElementsSkillUpdate")
	self:RegisterProtocol(SCFiveElementsTalentUpdate, "OnSCFiveElementsTalentUpdate")
	self:RegisterProtocol(SCFiveElementsDrawUpdate, "OnSCFiveElementsDrawUpdate")
	self:RegisterProtocol(SCFiveElementsDrawResult, "OnSCFiveElementsDrawResult")
	self:RegisterProtocol(SCFiveElementsDrawRecordInfo, "OnSCFiveElementsDrawRecordInfo")
	self:RegisterProtocol(SCFiveElementsDrawRecordAdd, "OnSCFiveElementsDrawRecordAdd")

	--五行沧溟
	self:RegisterProtocol(CSWaistLightOperate)
	self:RegisterProtocol(SCWaistLightInfo, "OnSCWaistLightInfo")
	self:RegisterProtocol(SCWaistLightItemInfo, "OnSCWaistLightItemInfo")
	self:RegisterProtocol(SCWaistLightActive, "OnSCWaistLightActive")
	self:RegisterProtocol(SCWaistLightCur, "OnSCWaistLightCur")
	self:RegisterProtocol(SCWaistLightSkill, "OnSCWaistLightSkill")
	self:RegisterProtocol(SCWaistLightSKillState, "OnWaistLightSKillState")
	--五行沧溟拓展
	self:RegisterProtocol(SCWaistSoulActiviate, "OnSCWaistSoulActiviate")
	self:RegisterProtocol(SCWaistSoulLevelInfo, "OnSCWaistSoulLevelInfo")
	self:RegisterProtocol(SCWaistAchievementInfo, "OnSCWaistAchievementInfo")
	self:RegisterProtocol(SCWaistAllAchievementInfo, "OnSCWaistAllAchievementInfo")
end

function FiveElementsWGCtrl:OpenSuitView()
	if not self.suit_view:IsOpen() then
		self.suit_view:Open()
	end
end

function FiveElementsWGCtrl:OpenComposeView()
	if not self.compose_view:IsOpen() then
		self.compose_view:Open()
	end
end

function FiveElementsWGCtrl:OpenResolveView()
	if not self.resolve_view:IsOpen() then
		self.resolve_view:Open()
	end
end

function FiveElementsWGCtrl:OnSCFiveElementsMsgBag(protocol)
	FiveElementsWGData.Instance:SetFiveElementsBagInfo(protocol)
	self.data:CalculationKnapsackRemind()
	RemindManager.Instance:Fire(RemindName.FiveElement_Knapsack)

	if self.view:IsOpen() then
		self.view:Flush(TabIndex.five_elements_knapsack, "BagChange")
	end
end

function FiveElementsWGCtrl:OnSCFiveElementsMsgGrid(protocol)
	local new_data = protocol.grid_info
	local is_add, add_num = FiveElementsWGData.Instance:UpdateFiveElementsBagInfo(protocol)

	if not IsEmptyTable(new_data) and is_add and add_num > 0 then
		local name = ItemWGData.Instance:GetItemNameDarkColor(new_data.item_id)
		local str = string.format(Language.Bag.GetItemTxt, ToColorStr(name, ITEM_TIP_D_COLOR[new_data.color]), add_num)

    	GlobalTimerQuest:AddDelayTimer(function ()
        	SysMsgWGCtrl.Instance:ErrorRemind(str)
    	end, 0.1)
	end

	self.data:CalculationKnapsackRemind()
	RemindManager.Instance:Fire(RemindName.FiveElement_Knapsack)

	if self.view:IsOpen() then
		self.view:Flush(TabIndex.five_elements_knapsack, "BagChange")
	end

	if self.compose_view:IsOpen() then
		self.compose_view:Flush()
	end

	if self.resolve_view:IsOpen() then
		self.resolve_view:Flush()
	end
end

function FiveElementsWGCtrl:OnSCFiveElementsInfo(protocol)
	FiveElementsWGData.Instance:SetFiveElementsInfo(protocol)
	self.data:CalculationOverViewRemind()
	self.data:CalculationKnapsackRemind()  -- 升华面板提到了背包界面
	RemindManager.Instance:Fire(RemindName.FiveElement_Overview)
	RemindManager.Instance:Fire(RemindName.FiveElement_Talent)
end

-- 部位镶嵌发生变化/珠子的镶嵌变化/以及总览部位升级都走的这里  后端没区分  套装状态也在这
function FiveElementsWGCtrl:OnSCFiveElementsPartUpdate(protocol)
	FiveElementsWGData.Instance:UpdatePartCellInfo(protocol)
	self.data:CalculationOverViewRemind()
	self.data:CalculationKnapsackRemind()
	RemindManager.Instance:Fire(RemindName.FiveElement_Overview)
	RemindManager.Instance:Fire(RemindName.FiveElement_Knapsack)   -- 合成在背包模块内

	if self.view:IsOpen() then
		self.view:Flush(nil, "Part_cell_Change")
	end

	if self.compose_view:IsOpen() then
		self.compose_view:Flush()
	end

	if self.suit_view:IsOpen() then
		self.suit_view:Flush()
	end
end

function FiveElementsWGCtrl:OnSCFiveElementsSkillUpdate(protocol)
	FiveElementsWGData.Instance:SetOverviewSkillData(protocol)
	RemindManager.Instance:Fire(RemindName.FiveElement_Overview)
	ViewManager.Instance:FlushView(GuideModuleName.FiveElementsView, TabIndex.five_elements_overview, "UpdateSkill")
end

function FiveElementsWGCtrl:OnSCFiveElementsTalentUpdate(protocol)
	self.data:SetSingleTalentInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.FiveElementsView, TabIndex.five_elements_talent, "all", {effect = "talent_jihuo"})
	RemindManager.Instance:Fire(RemindName.FiveElement_Talent)
end

function FiveElementsWGCtrl:OnSCFiveElementsDrawUpdate(protocol)
	self.data:SetDrawPoolInfo(protocol)
end

function FiveElementsWGCtrl:OnSCFiveElementsDrawResult(protocol)
	self.data:SetTreasuryResultInfo(protocol)

	if self.treasury_view:IsOpen() then
		self.treasury_view:Flush(0, "play_ani")
    end
end

function FiveElementsWGCtrl:FlushToggle()
	if self.treasury_view:IsOpen() then
		self.treasury_view:FlushAniStatus()
    end
end

function FiveElementsWGCtrl:OnSCFiveElementsDrawRecordInfo(protocol)
	self.data:SetServerlog(protocol)
end

function FiveElementsWGCtrl:OnSCFiveElementsDrawRecordAdd(protocol)
	self.data:SetNewServerlog(protocol)
	self.data:UpdateNewServerlog()

	if self.treasury_record_view:IsOpen() then
		self.treasury_record_view:Flush()
	end
end

function FiveElementsWGCtrl:SendSFiveElementsRequest(opera_type, param_1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSFiveElementsOperate)
	protocol.opera_type = opera_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol:EncodeAndSend()
end

function FiveElementsWGCtrl:SendSFiveElementsDecompsStoneRequest(count, reslove_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSFiveElementsDecompsStone)
	protocol.count = count
	protocol.reslove_list = reslove_list or {}
	protocol:EncodeAndSend()
end

function FiveElementsWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		if self.data:GetIsTalentStuff(change_item_id) then
			ViewManager.Instance:FlushView(GuideModuleName.FiveElementsView, TabIndex.five_elements_talent)
			RemindManager.Instance:Fire(RemindName.FiveElement_Talent)
		end

		if self.data:CheckIsOverviewItem(change_item_id, change_reason) then
			-- self.data:CalculationOverViewRemind()	
			RemindManager.Instance:Fire(RemindName.FiveElement_Overview)
			ViewManager.Instance:FlushView(GuideModuleName.FiveElementsView, TabIndex.FiveElement_Overview, "OverviewItemChangeFlush")
		end
	
		if self.data:CheckIsKnapsackItem(change_item_id, change_reason) then
			self.data:CalculationKnapsackRemind()
			self.data:CalculationOverViewRemind()
			RemindManager.Instance:Fire(RemindName.FiveElement_Knapsack)
			ViewManager.Instance:FlushView(GuideModuleName.FiveElementsView, TabIndex.five_elements_knapsack, "KnapsackItemChangeFlush")
		end
		
		if self.data:GetIsWaistLightStuff(change_item_id) then
			RemindManager.Instance:Fire(RemindName.FiveElement_CangMing)
			ViewManager.Instance:FlushView(GuideModuleName.FiveElementsView, TabIndex.five_elements_cangming)
		end

		if self.data:GetIsDaohunUpgradeItem(change_item_id) and self.cangming_daohun_view:IsOpen() then
			self.cangming_daohun_view:Flush(0, "FlushConsumeItem")
		end
	end
end

function FiveElementsWGCtrl:OnItemDataInitCallBack(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if nil == change_item_id then
		self.data:CalculationOverViewRemind()
		self.data:CalculationKnapsackRemind()
		RemindManager.Instance:Fire(RemindName.FiveElement_Overview)
		RemindManager.Instance:Fire(RemindName.FiveElement_Knapsack)

		if self.view:IsOpen() then
			self.view:Flush()
		end
	end
end

--五行宝库抽奖弹窗
function FiveElementsWGCtrl:ClickUseDrawItem(index, func)
    local cfg = FiveElementsWGData.Instance:GetTreasuryDrawConsumeCfg()
    local cur_cfg = cfg[index]

    if cur_cfg == nil then
        return
    end
    
	local cost_item_id = cur_cfg.cost_item_id
	local cost_item_num = cur_cfg.cost_item_num
    local num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)

    if num < cost_item_num then
         if not self.alert then
             self.alert = Alert.New()
         end

         self.alert:ClearCheckHook()
         self.alert:SetShowCheckBox(true, "five_element_treasury")
         self.alert:SetCheckBoxDefaultSelect(false)
         local item_cfg = ItemWGData.Instance:GetItemConfig(cost_item_id)
         local name = ""

         if item_cfg ~= nil then
             name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
         end
 
         local cost = (cur_cfg.cost_gold / cost_item_num) * (cost_item_num - num)
         local str = string.format(Language.TianShenLingHe.DrawCostStr, name, cost)
         self.alert:SetLableString(str)
         self.alert:SetOkFunc(func)
         self.alert:Open()
    else
        func()
    end
 end

function FiveElementsWGCtrl:OpenTreasuryRewardView()
	self.treasury_reward_view:Open()
	self.treasury_reward_view:Flush()
end

function FiveElementsWGCtrl:PlayTreasuryNextRewardTween()
	self.treasury_reward_view:NextGetRewardTween()
end

function FiveElementsWGCtrl:OpenTreasuryRecordView()
	self.treasury_record_view:Open()
end

function FiveElementsWGCtrl:OpenTreasuryLibraryView()
	self.treasury_library_view:Open()
end

function FiveElementsWGCtrl:OpenFiveELementsTreasuryView()
	if not self.treasury_view:IsOpen() then
		self.treasury_view:Open()
    end
end

-----------------五行沧溟-------------------

-- 五行沧溟通用请求请求操作
function FiveElementsWGCtrl:SendWaistLightRequest(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSWaistLightOperate)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function FiveElementsWGCtrl:OnSCWaistLightInfo(protocol)
	--print_error("===all_info====",protocol)
	self.data:SetWaistLightAllInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.FiveElementsView, TabIndex.five_elements_cangming)
	RemindManager.Instance:Fire(RemindName.FiveElement_CangMing)

	self.data:SetDaohunInfoList(protocol)
	if self.cangming_qiantianlu_view:IsOpen() then
		self.cangming_qiantianlu_view:Flush()
	end
end

--更新单个五行沧溟
function FiveElementsWGCtrl:OnSCWaistLightItemInfo(protocol)
	--print_error("===single====",protocol)
	self.data:WaistLightItemUpdateInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.FiveElementsView, TabIndex.five_elements_cangming)
	RemindManager.Instance:Fire(RemindName.FiveElement_CangMing)

	if self.cangming_qiantianlu_view:IsOpen() then
		self.cangming_qiantianlu_view:Flush()
	end
end

--五行沧溟激活
function FiveElementsWGCtrl:OnSCWaistLightActive(protocol)
	--print_error("===Active====",protocol)
	self:OpenGetNewView(protocol.waist_id)
end

--当前使用五行沧溟
function FiveElementsWGCtrl:OnSCWaistLightCur(protocol)
	--print_error("===Cur====",protocol)
	self.data:SetCurUseWaistLight(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.FiveElementsView, TabIndex.five_elements_cangming, "flush_use_waist_light")
end

--更新佩戴的技能
function FiveElementsWGCtrl:OnSCWaistLightSkill(protocol)
	--print_error("===Skill====",protocol)
	self.data:SetCurUseWaistLightSkill(protocol)
	if self.cangming_skill_veiw:IsOpen() then
		self.cangming_skill_veiw:Flush()
	end
end

function FiveElementsWGCtrl:OpenCangMingSkillView()
	if not self.cangming_skill_veiw:IsOpen() then
        self.cangming_skill_veiw:Open()
    else
        self.cangming_skill_veiw:Flush()
    end
end

function FiveElementsWGCtrl:OpenGetNewView(type)
	local protocol = {appe_image_id = type, appe_type = ROLE_APPE_TYPE.CANGMING}
	AppearanceWGCtrl.Instance:OnGetNewAppearance(protocol)
end

function FiveElementsWGCtrl:OnWaistLightSKillState(protocol)
	self.data:SetCurHaloSkillAttackNum(protocol.num)
	MainuiWGCtrl.Instance:UpdataHaloSkillProgress()
end

----------------- 五行沧溟拓展 -------------------
function FiveElementsWGCtrl:OnSCWaistSoulActiviate(protocol)
	self.data:SetDaohunInfoItemActive(protocol)
	if self.cangming_daohun_view:IsOpen() then
		self.cangming_daohun_view:Flush()
	end
end

function FiveElementsWGCtrl:OnSCWaistSoulLevelInfo(protocol)
	self.data:SetDaohunInfoItemLevel(protocol)
	if self.cangming_daohun_view:IsOpen() then
		self.cangming_daohun_view:Flush(0, "FlushItem")
	end
end

function FiveElementsWGCtrl:OpenDaohunView()
	self.cangming_daohun_view:Open()
end

function FiveElementsWGCtrl:OnSCWaistAchievementInfo(protocol)
	self.data:SetQianTianLuItemActive(protocol)
	if self.cangming_qiantianlu_view:IsOpen() then
		self.cangming_qiantianlu_view:Flush()
	end
end

function FiveElementsWGCtrl:OnSCWaistAllAchievementInfo(protocol)
	self.data:SetQianTianLuActiveList(protocol)
	if self.cangming_qiantianlu_view:IsOpen() then
		self.cangming_qiantianlu_view:Flush()
	end
end

function FiveElementsWGCtrl:OpenQiantianluView()
	self.cangming_qiantianlu_view:Open()
end