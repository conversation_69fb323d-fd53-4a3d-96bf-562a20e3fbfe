
--市场竞拍子物体类

MarketAuctionCommonRender = MarketAuctionCommonRender or BaseClass(BaseRender)

local Money_Root_Pos = {--普通竞价的金钱移动
	Up = Vector3(4, 28, 0),
	Down = Vector3(4, 0, 0),
}

function MarketAuctionCommonRender:LoadCallBack()
	self.item_goods = ItemCell.New(self.node_list.item_pos)
	self.node_list.cur_buy_btn.button:AddClickListener(BindTool.Bind(self.OnClickBuyBtn, self, false))--竞价
	self.node_list.one_buy_btn.button:AddClickListener(BindTool.Bind(self.OnClickBuyBtn, self, true))--一口价

	self.normal_show_price = 0
end

function MarketAuctionCommonRender:ReleaseCallBack()
	if self.item_goods then
		self.item_goods:DeleteMe()
		self.item_goods = nil
	end
	if self.alert then
		self.alert:DeleteMe()
	end

	self:ClearCountDown()
	self.normal_show_price = nil
end

function MarketAuctionCommonRender:OnFlush()
	if not self.data then return end
	--商品格子信息
	self.item_goods:SetData({item_id = self.data.item_id})
	self.item_goods:SetRightBottomText(self.data.item_num)
	local item_name = ItemWGData.Instance:GetItemName(self.data.item_id)
	self.node_list.item_name.text.text = item_name

	--倒计时设置
	self.is_open_sell = true --是否已经开卖
	if self.data.sell_time and self.data.end_time then
		self:ClearCountDown()
		local time = -1
		local now_time = TimeWGCtrl.Instance:GetServerTime()
		if self.data.sell_time - now_time > 0 then -- 开卖时间
			time = self.data.sell_time - now_time
			self.is_open_sell = false
		elseif self.data.end_time - now_time > 0 then -- 竞拍结束时间
			time = self.data.end_time - now_time
		end
		if time > 0 then
			self:UpdateCallBack(0, time)
			self.count_down = CountDown.Instance:AddCountDown(time, 1, BindTool.Bind(self.UpdateCallBack, self), BindTool.Bind(self.CompleteCallBack, self))
		end
	end

	--竞拍中显示
	self.node_list["auctioning_img"]:SetActive(self.data.auction_times <= 0)
	self.node_list["auctioning_img2"]:SetActive(self.data.auction_times > 0)
	self.node_list.auctioning_img3:SetActive(false)
	--当前竞价信息设置
	local cur_cfg = MarketWGData.Instance:GetNewAuctionCfgByType(self.data.type, self.data.item_id)
	if cur_cfg then
		if self.node_list["one_price"] then
			self.node_list["one_price"]:SetActive(cur_cfg.auction_price_button == 0) --拍卖一口价 0为显示 1为不显示
		end
		--是否有初始竞拍价
		local is_has_nor_cost = cur_cfg.initial_price and cur_cfg.initial_price ~= 0 and cur_cfg.initial_price ~= ""
		self.node_list.cur_not_jingjia:SetActive(not is_has_nor_cost)	--无法竞价
		self.node_list.one_buy_btn:SetActive(self.is_open_sell)
		self.node_list.cur_price:SetActive(is_has_nor_cost)	--竞价根节点
		if is_has_nor_cost then
			if self.is_open_sell then
				--是否参加过此商品的竞拍
				if MarketWGData.Instance:GetIsMyAuctionByIndex(self.data.index) then
					local my_role_id = RoleWGData.Instance:GetUUid()
					local is_me = my_role_id == self.data.auction_uid
					--竞价状态:您的出价最高/竞价被超过
					self.node_list.cur_buy_btn:SetActive(not is_me)
					--self.node_list["cur_layout_money"].transform.anchoredPosition = not is_me and Money_Root_Pos.Up or Money_Root_Pos.Down
					--self.node_list.cur_state_text.text.text = is_me and Language.Market.AuctionPrice_TheHighest or Language.Market.AuctionPrice_BeOutdone

					--处理竞价状态
					self.node_list.jiangjia_img:SetActive(is_me)
					self.node_list.jingjiabeichao_img:SetActive(not is_me)

				else
					--self.node_list.cur_state_text.text.text = ""	--无竞价状态
					self.node_list.cur_buy_btn:SetActive(true)
					--self.node_list["cur_layout_money"].transform.anchoredPosition = Money_Root_Pos.Up
					self.node_list.jiangjia_img:SetActive(false)
					self.node_list.jingjiabeichao_img:SetActive(false)
				end

				--self.node_list["one_layout"].transform.anchoredPosition = Money_Root_Pos.Up
			else--未开卖时的特殊展示
				--self.node_list.cur_state_text.text.text = ""	--无竞价状态
				--self.node_list["cur_layout_money"].transform.anchoredPosition = Money_Root_Pos.Down
				self.node_list.cur_buy_btn:SetActive(false)
				self.node_list.jiangjia_img:SetActive(false)
				self.node_list.jingjiabeichao_img:SetActive(false)
				self.node_list.auctioning_img3:SetActive(true)
				--self.node_list["one_layout"].transform.anchoredPosition = Money_Root_Pos.Down
			end

			self.normal_show_price = self.data.auction_price == 0 and cur_cfg.initial_price or self.data.auction_price
			self.node_list.cur_price_text.text.text = self.normal_show_price	--竞价价格(有价格就显示)
		end

		--一口价信息
		self.node_list.one_price_text.text.text = cur_cfg.one_price or 0  --一口价
	end
end

function MarketAuctionCommonRender:OnClickBuyBtn(is_one_price)
	if not self.data then return end
	local main_role_vo = RoleWGData.Instance:GetRoleInfo()
	local cur_cfg = MarketWGData.Instance:GetNewAuctionCfgByType(self.data.type, self.data.item_id)
	if not main_role_vo or not cur_cfg then return end

	if not is_one_price then
		local cur_price_num = self.normal_show_price + cur_cfg.bid_price_add
		is_one_price = cur_price_num >= cur_cfg.one_price--普通竞拍出价已达一口价,则按照一口价购买
	end

	if is_one_price then
		if main_role_vo.gold < cur_cfg.one_price then
			VipWGCtrl.Instance:OpenTipNoGold()
			return--仙玉不足
		end

		if nil == self.alert then
			self.alert = Alert.New()
		end
		
		local my_role_id = RoleWGData.Instance:GetUUid()
		local is_me = my_role_id == self.data.auction_uid
		local auto_is_me = my_role_id == self.data.auto_bid_uid
		local show_str = string.format(Language.Market.Auction_Confirm_One_Price, cur_cfg.one_price)
		if auto_is_me then--当前 自动出价者 是否为本玩家
			show_str = string.format(Language.Market.Auction_Confirm_One_Price_2, cur_cfg.one_price - self.data.auto_bid_price)
		elseif is_me then--当前 普通出价者 是否为本玩家
			show_str = string.format(Language.Market.Auction_Confirm_One_Price_2, cur_cfg.one_price - self.normal_show_price)
		end

		local cur_index = self.data.index--防止打开此界面时,商品已经被购买,列表刷新index
		self.alert:SetLableString(show_str)
		self.alert:SetOkFunc(function()
			--一口价购买

			local is_corss_server = self.data.fix_type == AUCTION_TYPE.Guild_Battle
			MarketWGCtrl.Instance:SendCSNewAuctionOperate(is_corss_server, NEW_AUCTION_OPERATE_TYPE.BID, nil, 1, nil, cur_index)
		end)
		self.alert:Open()
		return
	end

	if main_role_vo.gold < self.normal_show_price then
		VipWGCtrl.Instance:OpenTipNoGold()
		return--仙玉不足
	end

	MarketWGCtrl.Instance:OpenAuctionComfirmView(self.data)
end

--设置剩余时间
function MarketAuctionCommonRender:UpdateCallBack(elapse_time, total_time)
	local time = math.floor(total_time - elapse_time)
	if time > 0 then
		if self.node_list.left_time then
			if self.is_open_sell then
				self.node_list.left_time.text.text = TimeUtil.FormatDToHAndMS(time)
			else
				self.node_list.left_time.text.text = TimeUtil.FormatDToHAndMS(time)
			end
		end
	end
end

function MarketAuctionCommonRender:CompleteCallBack()
	if self.is_open_sell then
		--已开卖,且倒计时为0,限制购买
		self.node_list.cur_not_jingjia:SetActive(true)	--无法竞价
		self.node_list.one_buy_btn:SetActive(false)
		self.node_list.cur_price:SetActive(false)	--竞价根节点
	end
	self:ClearCountDown()
	self:Flush()
end

function MarketAuctionCommonRender:ClearCountDown()
	if CountDown.Instance:HasCountDown(self.count_down) then
        CountDown.Instance:RemoveCountDown(self.count_down)
        self.count_down = nil
   	end
end

--------------------------------------------------------------------------------------------

--左侧小页签
------------------------------AuctionLeftBtnListRender-----start--------------------------------------------------------
AuctionLeftBtnListRender = AuctionLeftBtnListRender or BaseClass(BaseRender)
function AuctionLeftBtnListRender:LoadCallBack()
	self.view.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickItem, self))
end

function AuctionLeftBtnListRender:__delete()
	self.parent_click_flush = nil
end

function AuctionLeftBtnListRender:SetToggleIsOn(value)
	self.view.toggle.isOn = value
end

function AuctionLeftBtnListRender:OnClickItem(is_click)
	if is_click and self.parent_click_flush then
		self.parent_click_flush(self.data.type, self.data.sub_type)
	end
end
function AuctionLeftBtnListRender:OnFlush()
	if not self.data then return end
	self.node_list.btn_name.text.text = self.data.name
	self.node_list.hl_btn_name.text.text = self.data.name
end
------------------------------AuctionLeftBtnListRender-----end--------------------------------------------------------

--sub_type标签页.
------------------------------MarketAuctionTabRender-----start--------------------------------------------------------
MarketAuctionTabRender = MarketAuctionTabRender or BaseClass(BaseRender)
function MarketAuctionTabRender:OnFlush()
	if self.data then
		self.node_list["text"].text.text = self.data.sub_name
		self.node_list["texth"].text.text = self.data.sub_name
	end
end

function MarketAuctionTabRender:ShowHL(show_hl)
	self.node_list["texth"]:SetActive(show_hl)
	self.node_list["text"]:SetActive(not show_hl)
	self.node_list["hl"]:SetActive(show_hl)
end

-- 选择状态改变
function MarketAuctionTabRender:OnSelectChange(is_select)
	self:ShowHL(is_select)
end
------------------------------MarketAuctionTabRender-----end--------------------------------------------------------