BiZuoView = BiZuoView or BaseClass(SafeBaseView)

function BiZuoView:InitGetStrongerView()
	self.get_stronger_type_list = AsyncListView.New(GetStrongerTypeItem, self.node_list["get_stronger_type_list"])
	self.get_stronger_list = AsyncListView.New(GetStrongerItem, self.node_list["get_stronger_list"])
	self.select_stronger_type = 1

	-- 玩家等级改变监听
	if not self.stronger_role_data_change then
		self.stronger_role_data_change = BindTool.Bind1(self.FlushStrongerItemList, self)
		RoleWGData.Instance:NotifyAttrChange(self.stronger_role_data_change, {"level", "prof"})
	end

	-- 活动监听
	if not self.stronger_activity_change_callback then
		self.stronger_activity_change_callback = BindTool.Bind(self.FlushStrongerItemList,self)
		ActivityWGData.Instance:NotifyActChangeCallback(self.stronger_activity_change_callback)
	end
end

function BiZuoView:ReleaseGetStrongerView()
	if self.get_stronger_type_list then
		self.get_stronger_type_list:DeleteMe()
		self.get_stronger_type_list = nil
	end

	if self.get_stronger_list then
		self.get_stronger_list:DeleteMe()
		self.get_stronger_list = nil
	end

	if RoleWGData.Instance and self.stronger_role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.stronger_role_data_change)
		self.stronger_role_data_change = nil
	end

	if self.stronger_activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.stronger_activity_change_callback)
		self.stronger_activity_change_callback = nil
	end

end

function BiZuoView:FlushGetStrongerView()
	local type_list = {}
	local stronger_cfg = BiZuoWGData.Instance:GetStrongerCfg()
	for type, _ in pairs(stronger_cfg) do
		table.insert(type_list, type)
	end
	self.get_stronger_type_list:SetDataList(type_list)
	self.get_stronger_type_list:SetSelectCallBack(BindTool.Bind(self.OnClickStrongerType, self))
	self.get_stronger_type_list:SetRefreshCallback(BindTool.Bind(self.OnRefreshStrongerType, self))

	self:FlushStrongerItemList()
end

function BiZuoView:FlushStrongerItemList()
	local stronger_cfg_list = BiZuoWGData.Instance:GetStrongerCfgByType(self.select_stronger_type)
	table.sort(stronger_cfg_list, SortTools.KeyLowerSorter("sort"))
	self.get_stronger_list:SetDataList(stronger_cfg_list)
end

-- 点击变强类型
function BiZuoView:OnClickStrongerType(item)
	if item and item:GetData() then
		local data = item:GetData()
		self.select_stronger_type = data
	else
		self.select_stronger_type = 1
	end
	self:FlushGetStrongerView()

	-- 滚到最上面
	self.get_stronger_list:ReloadData(0)
end

function BiZuoView:OnRefreshStrongerType(item, cell_index)
	if item then
		item:SetCurSelectStrongerType(self.select_stronger_type)
	end
end
------------------------ 我要变强类型item  -----------------------
GetStrongerTypeItem = GetStrongerTypeItem or BaseClass(BaseRender)

function GetStrongerTypeItem:__init()
end

function GetStrongerTypeItem:OnFlush()
	--self.node_list["bg"].raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("get_stronger_type_" .. self.data))
	if self.data == 1 then
		self.node_list["nor_text"].text.text = Language.BiZuo.BianQiangTab1
		self.node_list["hl_text"].text.text = Language.BiZuo.BianQiangTab1
	elseif self.data == 2 then
		self.node_list["nor_text"].text.text = Language.BiZuo.BianQiangTab2
		self.node_list["hl_text"].text.text = Language.BiZuo.BianQiangTab2
	else
		self.node_list["nor_text"].text.text = Language.BiZuo.BianQiangTab3
		self.node_list["hl_text"].text.text = Language.BiZuo.BianQiangTab3
	end
	
end

function GetStrongerTypeItem:SetCurSelectStrongerType(stronger_type)
	self.node_list["hl"]:SetActive(self.data == stronger_type)
end


------------------------- 我要变强item ---------------------
GetStrongerItem = GetStrongerItem or BaseClass(BaseRender)

function GetStrongerItem:__init()
	XUI.AddClickEventListener(self.node_list["go_btn"], BindTool.Bind(self.OnClickGoBtn, self))
end

function GetStrongerItem:SetData(data)
	self.data = data
	self.stronger_data = BiZuoWGData.Instance:GetStrongerDataByCfg(self.data)
	BaseRender.SetData(self, data)
end

function GetStrongerItem:OnFlush()
	self.node_list["name"].text.text = self.stronger_data.cfg.name
	self.node_list["desc"].text.text = self.stronger_data.cfg.desc

	self.node_list["lv_icon"].image:LoadSprite(ResPath.GetCommon("a2_quality_text_" .. self.stronger_data.cfg.star))

	local is_open = BiZuoWGData.Instance:GetStrongerIsOpen(self.data)
	self.node_list["go_btn"]:SetActive(is_open)
	if self.stronger_data.open_desc == Language.Common.FunOpenTip then
		self.node_list["open_desc"]:SetActive(false)
		self.node_list["lock_desc"]:SetActive(true)
		self.node_list["lock_desc"].text.text = ToColorStr(self.stronger_data.open_desc, COLOR3B.RED)
	else
		self.node_list["open_desc"]:SetActive(true)
		self.node_list["lock_desc"]:SetActive(false)
		self.node_list["open_desc"].text.text = ToColorStr(self.stronger_data.open_desc, COLOR3B.GREEN)
	end
	self.node_list["icon"].image:LoadSpriteAsync(self.stronger_data.icon_bundle, self.stronger_data.icon_name, function()
		self.node_list["icon"].image:SetNativeSize()
 	end)
end

function GetStrongerItem:OnClickGoBtn()
	if self.stronger_data.cfg.subtype == BiZuoWGData.GetStrongerSubtype.Act then
		MainuiWGCtrl.Instance:ClickEnterActivity(self.stronger_data.cfg.act_type)
		ViewManager.Instance:Close(GuideModuleName.BiZuo)
	else
		if self.stronger_data.cfg.module_name == FunName.DailyTask then 						-- 赏金任务
			ViewManager.Instance:Open(GuideModuleName.TaskShangJinView, nil, "all", {select_task_type = GameEnum.TASK_TYPE_RI})
		elseif self.stronger_data.cfg.module_name == FunName.RoleSit then 						-- 打坐
			ViewManager.Instance:Close(GuideModuleName.BiZuo)
			GuajiWGCtrl.Instance:TryGoToSit()
		else
			FunOpen.Instance:OpenViewNameByCfg(self.stronger_data.open_panel)
		end
	end
end
