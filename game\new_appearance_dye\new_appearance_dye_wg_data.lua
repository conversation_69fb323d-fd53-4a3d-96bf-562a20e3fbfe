NewAppearanceDyeWGData = NewAppearanceDyeWGData or BaseClass()
HAIR_PART = 1

function NewAppearanceDyeWGData:__init()
	if NewAppearanceDyeWGData.Instance then
		error("[NewAppearanceDyeWGData] Attempt to create singleton twice!")
		return
	end
    
	NewAppearanceDyeWGData.Instance = self

	local shizhuangcfg = ConfigManager.Instance:GetAutoConfig("shizhuangcfg_auto")
	if shizhuangcfg then
		self.dyeing_project_cfg = shizhuangcfg.dyeing_project
		self.dyeing_consume_cfg = ListToMap(shizhuangcfg.dyeing_consume, "seq")
		self.dyeing_consume_index_cfg = ListToMap(shizhuangcfg.dyeing_consume, "index", "part_type")
		self.dyeing_consume_item_cfg = ListToMap(shizhuangcfg.dyeing_consume, "consume_item")
	end

	if self.dyeing_consume_cfg then
		self.dyeing_consume_info = {}
		self.part_defult_color_info = {}
		self.part_dye_index_list = {}
		self.part_dye_man_index_list = {}
		self.part_random_ai_list = {}
		self.project_body_list = {}
		self.project_hair_list = {}
		self.project_face_list = {}

		for _, consume_cfg in pairs(self.dyeing_consume_cfg) do
			if consume_cfg and consume_cfg.seq and consume_cfg.part_consume_num then
				-- 染色花费
				local part_spend_list = Split(consume_cfg.part_consume_num, "|")
				self.dyeing_consume_info[consume_cfg.seq] = {}

				for k, spend_str in ipairs(part_spend_list) do
					local spend_list = Split(spend_str, ",")
					local spend_data = {}
					spend_data.part = tonumber(spend_list[1]) or 0
					spend_data.consume_item = consume_cfg.consume_item
					spend_data.spend = tonumber(spend_list[2]) or 0
					self.dyeing_consume_info[consume_cfg.seq][spend_data.part] = spend_data
				end

				-- 染色默认颜色
				local defult_color_list = Split(consume_cfg.part_defult_color, "|")
				self.part_defult_color_info[consume_cfg.seq] = {}

				for k, defult_color_str in ipairs(defult_color_list) do
					local defult_ai_color_id = tonumber(defult_color_str) or 0
					self.part_defult_color_info[consume_cfg.seq][k] = defult_ai_color_id
				end

				-- 拓展方案材质——身体
				local project_body_str_list = Split(consume_cfg.project_body_list, "|")
				self.project_body_list[consume_cfg.seq] = {}

				for k, project_body_str in ipairs(project_body_str_list) do
					local project_body_id = tonumber(project_body_str) or 0
					self.project_body_list[consume_cfg.seq][k] = project_body_id
				end

				-- 拓展方案材质——头发
				local project_hair_str_list = Split(consume_cfg.project_hair_list, "|")
				self.project_hair_list[consume_cfg.seq] = {}

				for k, project_hair_str in ipairs(project_hair_str_list) do
					local project_hair_id = tonumber(project_hair_str) or 0
					self.project_hair_list[consume_cfg.seq][k] = project_hair_id
				end

				-- 拓展方案材质——脸
				local project_face_str_list = Split(consume_cfg.project_face_list, "|")
				self.project_face_list[consume_cfg.seq] = {}

				for k, project_face_str in ipairs(project_face_str_list) do
					local project_face_id = tonumber(project_face_str) or 0
					self.project_face_list[consume_cfg.seq][k] = project_face_id
				end

				--部位对应区域
				local dye_index_str_list = Split(consume_cfg.part_dye_index_list, "|")
				self.part_dye_index_list[consume_cfg.seq] = {}

				for k, dye_index_str in ipairs(dye_index_str_list) do
					local dye_index_list = Split(dye_index_str, ",")
					local defult_dye_index_data = {}

					for i, index_str in ipairs(dye_index_list) do
						if i == 1 then
							defult_dye_index_data.part = tonumber(index_str) or 0
						else
							if defult_dye_index_data.dye_index_list == nil then
								defult_dye_index_data.dye_index_list = {}
							end
				
							table.insert(defult_dye_index_data.dye_index_list, tonumber(index_str) or 0)
						end
					end
		
					self.part_dye_index_list[consume_cfg.seq][defult_dye_index_data.part] = defult_dye_index_data
				end

				--部位对应区域
				local part_dye_index_list_1 = Split(consume_cfg.part_dye_index_list_1, "|")
				self.part_dye_man_index_list[consume_cfg.seq] = {}

				for k, dye_man_index_str in ipairs(part_dye_index_list_1) do
					local dye_index_list = Split(dye_man_index_str, ",")
					local defult_man_dye_index_data = {}

					for i, index_str in ipairs(dye_index_list) do
						if i == 1 then
							defult_man_dye_index_data.part = tonumber(index_str) or 0
						else
							if defult_man_dye_index_data.dye_index_list == nil then
								defult_man_dye_index_data.dye_index_list = {}
							end
				
							table.insert(defult_man_dye_index_data.dye_index_list, tonumber(index_str) or 0)
						end
					end
		
					self.part_dye_man_index_list[consume_cfg.seq][defult_man_dye_index_data.part] = defult_man_dye_index_data
				end

				-- AI随心染
				local random_ai_str_list = Split(consume_cfg.part_random_ai, "|")
				self.part_random_ai_list[consume_cfg.seq] = {}

				for random_index, random_ai_str in ipairs(random_ai_str_list) do
					self.part_random_ai_list[consume_cfg.seq][random_index] = {}
					local ai_color_str_list = Split(random_ai_str, ",")
					local ai_color_data = {}
					local ai_color_index = 0

					for i = 1, #ai_color_str_list / 2 do
						local index = ai_color_index + 1
						ai_color_data[index] = {}
						ai_color_data[index].part = tonumber(ai_color_str_list[ai_color_index * 2 + 1]) or 0
						ai_color_data[index].hex_color = ai_color_str_list[ai_color_index * 2 + 2]
						ai_color_index = ai_color_index + 1
					end

					self.part_random_ai_list[consume_cfg.seq][random_index] = ai_color_data
				end
			end
		end
	end
end

function NewAppearanceDyeWGData:__delete()
    NewAppearanceDyeWGData.Instance = nil
end

------------------------- cfg start -------------------------
-- 获取所有方案信息
function NewAppearanceDyeWGData:GetDyeingProjectCfg()
	local empty = {}
	return self.dyeing_project_cfg
end

-- 获取方案配置信息
function NewAppearanceDyeWGData:GetDyeingProjectCfgById(project_id)
	local empty = {}
	return (self.dyeing_project_cfg or empty)[project_id]
end

-- 获取时装的染色信息(时装查找)
function NewAppearanceDyeWGData:GetConsumeCfgByIndex(index, part_type)
	local empty = {}
	return ((self.dyeing_consume_index_cfg or empty)[index] or empty)[part_type]
end

-- 获取时装的染色信息(seq查找)
function NewAppearanceDyeWGData:GetConsumeCfgBySeq(seq)
	local empty = {}
	return (self.dyeing_consume_cfg or empty)[seq]
end

-- 获取时装的染色部位消耗列表
function NewAppearanceDyeWGData:GetConsumeSpendCfgBySeq(seq)
	local empty = {}
	return (self.dyeing_consume_info or empty)[seq]
end

-- 获取时装的染色部位消耗列表
function NewAppearanceDyeWGData:GetConsumeSpendCfgBySeqPart(seq, part)
	local empty = {}
	return ((self.dyeing_consume_info or empty)[seq] or empty)[part]
end

-- 获取时装的染色部位消耗列表
function NewAppearanceDyeWGData:GetDefultColorCfgBySeq(seq)
	local empty = {}
	return (self.part_defult_color_info or empty)[seq]
end

-- 获取时装的默认色
function NewAppearanceDyeWGData:GetDefultColorCfgBySeqPart(seq, scheme, part, is_save)
	local empty = {}
	local defult_color = ((self.part_defult_color_info or empty)[seq] or empty)[scheme]

	if defult_color == nil or defult_color == 0 then
		if is_save then
			return nil
		else
			return COLOR3B.WHITE
		end
	end

	local list = self:GetDyeRandomAiDataBySeqRandom(seq, defult_color)
	if list then
		for i, v in ipairs(list) do
			if v.part == part then
				return v.hex_color
			end
		end
	end

	if is_save then
		return nil
	else
		return COLOR3B.WHITE
	end
end

-- 获取时装的方案使用材质 -身体
function NewAppearanceDyeWGData:GetProjectBodyIdCfgBySeq(seq, project_id)
	local empty = {}
	return ((self.project_body_list or empty)[seq] or empty)[project_id] or 0
end

-- 获取时装的方案使用材质 -头发
function NewAppearanceDyeWGData:GetProjectHairIdCfgBySeq(seq, project_id)
	local empty = {}
	return ((self.project_hair_list or empty)[seq] or empty)[project_id] or 0
end

-- 获取时装的方案使用材质 -脸
function NewAppearanceDyeWGData:GetProjectFaceIdCfgBySeq(seq, project_id)
	local empty = {}
	return ((self.project_face_list or empty)[seq] or empty)[project_id] or 0
end

-- 获取时装的染色对应区域
function NewAppearanceDyeWGData:GetDyeIndexListBySeq(seq)
	local empty = {}
	local sex = RoleWGData.Instance:GetRoleSex()
	local aim_list = sex == 1 and self.part_dye_man_index_list or self.part_dye_index_list
	return (aim_list or empty)[seq]
end

-- 获取时装的染色对应区域
function NewAppearanceDyeWGData:GetDyeIndexListBySeqPart(seq, part)
	local empty = {}
	local sex = RoleWGData.Instance:GetRoleSex()
	local aim_list = sex == 1 and self.part_dye_man_index_list or self.part_dye_index_list
	return ((aim_list or empty)[seq] or empty)[part]
end

-- -- 获取时装的染色对应区域(男)
-- function NewAppearanceDyeWGData:GetManDyeIndexListBySeq(seq)
-- 	local empty = {}
-- 	return (self.part_dye_man_index_list or empty)[seq]
-- end

-- -- 获取时装的染色对应区域(男)
-- function NewAppearanceDyeWGData:GetManDyeIndexListBySeqPart(seq, part)
-- 	local empty = {}
-- 	return ((self.part_dye_man_index_list or empty)[seq] or empty)[part]
-- end

-- 获取对应时装的ai染色列表
function NewAppearanceDyeWGData:GetDyeRandomAiListBySeq(seq)
	local empty = {}
	return (self.part_random_ai_list or empty)[seq]
end

-- 获取时装ai染色信息
function NewAppearanceDyeWGData:GetDyeRandomAiDataBySeqRandom(seq, random_index)
	local empty = {}
	return ((self.part_random_ai_list or empty)[seq] or empty)[random_index]
end

-- 获取时装ai染色信息
function NewAppearanceDyeWGData:IsDyeSpendItem(item_id)
	local empty = {}
	return (self.dyeing_consume_item_cfg or empty)[item_id] ~= nil
end

------------------------- cfg end -------------------------
------------------------- protocol start -------------------------
-- 染色所有信息
function NewAppearanceDyeWGData:SetShizhuangDyeingInfo(protocol)
	self.dyeing_project_flag = protocol.dyeing_project_flag
	self.dyeing_project_list = {}

	if not protocol.dyeing_item then
		return
	end

	for index, project_list in ipairs(protocol.dyeing_item) do
		local seq = index - 1
		self.dyeing_project_list[seq] = project_list
	end
end

-- 单个染色方案信息
function NewAppearanceDyeWGData:SetShizhuangDyeingUpdateInfo(protocol)
	local seq = protocol.seq
	local project_index = protocol.project_index + 1
	self.dyeing_project_list[seq].curr_dyeing_index = protocol.curr_dyeing_index
	self.dyeing_project_list[seq].dyeing_project_flag = protocol.dyeing_project_flag
	self.dyeing_project_list[seq].project_info[project_index] = protocol.dyeing_project
end

-- 其他人染色方案信息
function NewAppearanceDyeWGData:SetShizhuangOtherRoleDyeingInfo(protocol)
	self.other_role_dyeing_info = protocol
end

-- 获取孔位解锁信息
function NewAppearanceDyeWGData:GetDyeingProjectLockState(seq, hole)
	local empty = {}
	return (((self.dyeing_project_list or empty)[seq] or empty).dyeing_project_flag or empty)[hole] or 0
end

-- 获取当前的染色信息
function NewAppearanceDyeWGData:GetDyeingInfoBySeq(seq)
	local empty = {}
	return ((self.dyeing_project_list or empty)[seq] or empty).project_info
end

-- 获取当前的染色使用的染色方案
function NewAppearanceDyeWGData:GetDyeingIndexInfoBySeq(seq)
	local empty = {}
	return ((self.dyeing_project_list or empty)[seq] or empty).curr_dyeing_index or 0
end

-- 获取当前的染色方案信息
function NewAppearanceDyeWGData:GetDyeingInfoBySeqIndex(seq, project_index)
	local empty = {}
	return (((self.dyeing_project_list or empty)[seq] or empty).project_info or empty)[project_index]
end
------------------------- protocol end -------------------------
-- 获取当前时装的方案列表
function NewAppearanceDyeWGData:GetDyeingSchemeListByPartIndex(index, part_type)
	local list = self:GetDyeingProjectCfg()
	local cfg = self:GetConsumeCfgByIndex(index, part_type)
	local back_list = {}

	if not cfg then
		return back_list
	end

	for _, project in pairs(list) do
		if project and project.project_id then
			local data = {}
			data.pro_cfg = project
			data.seq = cfg.seq
			data.project_id = project.project_id
			data.dyeing_info = self:GetDyeingInfoBySeqIndex(cfg.seq, project.project_id + 1)
			table.insert(back_list, data)
		end
	end
	
	table.sort(back_list, SortTools.KeyLowerSorter("project_id"))
	return back_list
end
