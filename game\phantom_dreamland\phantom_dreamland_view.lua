-- 幻梦秘境
PhantomDreamlandView = PhantomDreamlandView or BaseClass(SafeBaseView)

function PhantomDreamlandView:__init()
	self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Half
	self:SetMaskBg()
	self.default_index = 10

	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
	self:AddViewResource(0, "uis/view/phantom_dreamland_ui_prefab", "layout_phantom_dreamland_view")
	self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar_Activity")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function PhantomDreamlandView:OpenCallBack(index)
	-- 打开界面获取一下排行榜
	PhantomDreamlandWGCtrl.Instance:RequestPhantomDreamlandRankInfo()
end

function PhantomDreamlandView:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.wave_item_list then
		for k, v in pairs(self.wave_item_list) do
			v:DeleteMe()
		end
		self.wave_item_list = nil
	end

	if self.show_reward_item_list then
		self.show_reward_item_list:DeleteMe()
		self.show_reward_item_list = nil
	end

	self.cur_selected_wave = nil

	self:RemoveCountDown()
end

function PhantomDreamlandView:CloseCallBack()

end

function PhantomDreamlandView:LoadCallBack()
	self:InitCommonUI()

	self.node_list["txt_no_reward_desc"].text.text = Language.PhantomDreamland.NoRewardTips

	-- 副本波次Item
	if not self.wave_item_list then
		self.wave_item_list = {}
		local node_num = self.node_list.wave_show_item_list.transform.childCount
		for i = 1, node_num do
			self.wave_item_list[i] = PhantomDreamlandFbWaveRender.New(self.node_list.wave_show_item_list:FindObj("wave_sel_item_" .. i))
			self.wave_item_list[i]:SetIndex(i)
			self.wave_item_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickWaveItemCallBack, self))
		end
	end

	-- 波次奖励
	if not self.show_reward_item_list then
		self.show_reward_item_list = AsyncListView.New(ItemCell, self.node_list.show_reward_list)
		self.show_reward_item_list:SetStartZeroIndex(true)
	end

	XUI.AddClickEventListener(self.node_list.btn_start_challenge, BindTool.Bind(self.OnClickChallenge, self))
	XUI.AddClickEventListener(self.node_list.btn_rank, BindTool.Bind(self.OnClickRank, self))
	XUI.AddClickEventListener(self.node_list.btn_get_reward, BindTool.Bind(self.OnClickGetRewardBtn, self))
	
	self:ActivityTimeCountDown()
end

function PhantomDreamlandView:InitCommonUI()
	self.node_list.title_view_name.text.text = Language.PhantomDreamland.TitleViewName

	local bundle, assert = ResPath.GetRawImagesPNG("a3_hhmj_bj1")
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, assert, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)

	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list, "VerticalTabbar_Activity")
		self.tabbar:SetVerTabbarCellName("VerticalTabbarCell_Activity")
		self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
		self.tabbar:Init(Language.PhantomDreamland.TabGroup, nil, ResPath.CommonBundleName)
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end


function PhantomDreamlandView:OnFlush(param_t)
	for k, v in pairs(param_t) do
		if k == "all" then
			self:FlushLevelMessage()
			self:FlushWaveList()
		elseif k == "flush_stage" then
			self:FlushLevelMessage()
		elseif k == "flush_wave_list" then
			self:FlushWaveList()
		end
	end
end

--活动时间倒计时
function PhantomDreamlandView:ActivityTimeCountDown()
	self:RemoveCountDown()
	local time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DREAM_SECRET)
	if time <= 0 then
		self:OnComplete()
		return
	else
		CountDownManager.Instance:AddCountDown("dreamland_fb_countdown",
			BindTool.Bind(self.UpdateTimeCallBack, self),
			BindTool.Bind(self.OnComplete, self),
			nil, time, 1)
	end
end

function PhantomDreamlandView:OnComplete()
	self.node_list["txt_activity_time"].text.text = ""
	self:Close()
end

function PhantomDreamlandView:RemoveCountDown()
	if CountDownManager.Instance:HasCountDown("dreamland_fb_countdown") then
		CountDownManager.Instance:RemoveCountDown("dreamland_fb_countdown")
	end
end

function PhantomDreamlandView:UpdateTimeCallBack(now_time, total_time)
	local time = math.ceil(total_time - now_time)
	local time_str = TimeUtil.FormatSecondDHM9(time)
	self.node_list["txt_activity_time"].text.text = string.format(Language.PhantomDreamland.ActivityTime, time_str)
end

-- 刷新波次列表
function PhantomDreamlandView:FlushWaveList()
	local wave_list = PhantomDreamlandWGData.Instance:GetWaveCfgList()
	for i, v in ipairs(self.wave_item_list) do
		v:SetData(wave_list[i] or {})
	end
    local is_remind, index = PhantomDreamlandWGData.Instance:GetRewardRemind()
	if is_remind then
        self:OnClickWaveItemCallBack(self.wave_item_list[index])
    else
		self:OnClickWaveItemCallBack(self.wave_item_list[self.cur_selected_wave or 1])
	end
	
	local cur_wave = PhantomDreamlandWGData.Instance:GetCurWave()
	self.node_list["sldr_progrss"].slider.value = (cur_wave - 1) / (#wave_list - 1)
end

-- 刷新关卡信息
function PhantomDreamlandView:FlushLevelMessage()
	local cfg = PhantomDreamlandWGData.Instance:GetLevelCfg()
	if not cfg then
		return
	end

	--[[
	--等级压制
	local role_lv = RoleWGData.Instance:GetRoleLevel()
	local color = role_lv >= cfg.need_level and COLOR3B.BLACK or COLOR3B.RED
	self.node_list.level_limit_text.text.text = ToColorStr(string.format(Language.OfflineRest.ExperienceFbLvLimit, cfg.need_level), color)
	]]

	-- 战斗力限制
	local role_cap = GameVoManager.Instance:GetMainRoleVo().capability
	color = role_cap >= cfg.need_cap and COLOR3B.GREEN or COLOR3B.RED
	local cap_str = string.format(Language.PhantomDreamland.CapLimit, ToColorStr(cfg.need_cap, color))
	self.node_list.txt_target_cap.text.text = cap_str

	-- 讨伐目标
	--[[
	local wave_cfg_list = PhantomDreamlandWGData.Instance:GetWaveCfgList()
	local monster_id = wave_cfg_list[#wave_cfg_list].monster_id
	local boss_info = BossWGData.Instance:GetMonsterInfo(monster_id)
    local boss_name = boss_info and boss_info.name or Language.Boss.Unkonwn
	]]
	self.node_list["txt_boss_name"].text.text = cfg.boss_name
	local bundle, asset = ResPath.GetRawImagesPNG(cfg.boss_img)
	self.node_list["rawimg_boss"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["rawimg_boss"].raw_image:SetNativeSize()
	end)

	-- 最高记录
	local harm_value = PhantomDreamlandWGData.Instance:GetBestHarmValue()
	self.node_list["txt_best_value"].text.text = string.format(Language.PhantomDreamland.BestValue, harm_value)

	-- 挑战灵玉消耗
	local consume_lingyu = PhantomDreamlandWGData.Instance:GetConsumeLingYu()
	self.node_list["txt_cost_amount"].text.text = consume_lingyu
end

-- 波数选择回调
function PhantomDreamlandView:OnClickWaveItemCallBack(item)
	if nil == item or nil == item.data then
		return
	end
	self.cur_selected_wave = item.data.wave

	local reward_item_data = item.data.pass_reward_item
	self.show_reward_item_list:SetDataList(reward_item_data)

	local cur_wave = PhantomDreamlandWGData.Instance:GetCurWave()
	local reward_flag = PhantomDreamlandWGData.Instance:GetWaveRewardFetchFlag(item.data.wave)
	local can_fecth = item.data.wave <= cur_wave and reward_flag ~= 1
    XUI.SetButtonEnabled(self.node_list["btn_get_reward"], can_fecth)
	self.node_list["img_get_reward_red"]:SetActive(can_fecth)
	self.node_list["btn_get_reward"]:SetActive(reward_flag ~= 1)
	self.node_list["reward_has_get_flag"]:SetActive(reward_flag == 1)

	local max_num = self.node_list["wave_show_item_list"].transform.childCount
    self.node_list["no_reward_hide"]:SetActive(item.index ~= max_num)
	self.node_list["txt_no_reward_desc"]:SetActive(item.index == max_num)

	for k, v in pairs(self.wave_item_list) do
		v:SetSelectIndex(self.cur_selected_wave)
	end
end

---------------------------------------------------------------------------

-- 开始挑战
function PhantomDreamlandView:OnClickChallenge()
	local cfg = PhantomDreamlandWGData.Instance:GetLevelCfg()
	if not cfg then
		return
	end

	local role_lv = RoleWGData.Instance:GetRoleLevel()
	local is_can_challenge = role_lv >= cfg.need_level

	if not is_can_challenge then
		TipWGCtrl.Instance:ShowSystemMsg(Language.PhantomDreamland.LevelLimit)
		return
	end

	PhantomDreamlandWGCtrl.Instance:RequestPhantomDreamlandDare()
end

-- 查看排行
function PhantomDreamlandView:OnClickRank()
	PhantomDreamlandWGCtrl.Instance:OpenDreamlandFbRankView()
end

-- 领取奖励
function PhantomDreamlandView:OnClickGetRewardBtn()
	if not self.cur_selected_wave then
		return
	end
	PhantomDreamlandWGCtrl.Instance:RequestPhantomDreamlandGetReward(self.cur_selected_wave)
end

----------------------------副本-----------------------------------------

-- 波次Item
PhantomDreamlandFbWaveRender = PhantomDreamlandFbWaveRender or BaseClass(BaseRender)

function PhantomDreamlandFbWaveRender:LoadCallBack()

end

function PhantomDreamlandFbWaveRender:__delete()

end

function PhantomDreamlandFbWaveRender:OnFlush()
	if not self.data then
		return
	end

	local cur_wave = PhantomDreamlandWGData.Instance:GetCurWave()
	local reward_flag = PhantomDreamlandWGData.Instance:GetWaveRewardFetchFlag(self.data.wave)
	local can_fecth = self.data.wave <= cur_wave and reward_flag ~= 1


	self.node_list["img_hl"]:SetActive(self.data.wave == cur_wave + 1)
	self.node_list["img_red"]:SetActive(can_fecth)
	self.node_list["has_reward_flag"]:SetActive(reward_flag == 1)

	local monster_cfg = PhantomDreamlandWGData.Instance:GetMonsterCfgByWave(self.data.wave)
	if self.node_list then
		local cfg = BossWGData.Instance:GetMonsterCfgByid(monster_cfg[1].monster_id)
		if cfg then
			local bundle, asset = ResPath.GetBossIcon("wrod_boss_" .. cfg.small_icon)
			self.node_list["img_target_icon"].image:LoadSprite(bundle, asset)
		end
	end
end

function PhantomDreamlandFbWaveRender:OnSelectChange(is_select)
	self.node_list["img_selected"]:CustomSetActive(is_select)
end
