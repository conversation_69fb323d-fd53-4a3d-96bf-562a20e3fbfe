--藏宝湾组队报名
BootybayTeamBaoMingView = BootybayTeamBaoMingView or BaseClass(SafeBaseView)
function BootybayTeamBaoMingView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
    self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_baoming_enter")
end
function BootybayTeamBaoMingView:ReleaseCallBack()
    if  self.member_info_list ~= nil then
        for i, v in pairs(self.member_info_list) do
            v:DeleteMe()
        end
        self.member_info_list = nil
    end
    if self.team_info_event then
    	GlobalEventSystem:UnBind(self.team_info_event)
    	self.team_info_event = nil
    end
end

function BootybayTeamBaoMingView:LoadCallBack()
	self.team_info_event = GlobalEventSystem:Bind(OtherEventType.TEAM_INFO_CHANGE, BindTool.Bind1(self.OnTeam<PERSON>hange, self))         -- 队伍
    self.node_list["title_view_name"].text.text = Language.NewTeam.BaoMingEnterViewName
	self:SetSecondView(nil, self.node_list["size"])
    --XUI.AddClickEventListener(self.node_list["btn_pingtai"], BindTool.Bind(self.OnClickPingTai, self))
    self.node_list["btn_pingtai"]:SetActive(false)
    XUI.AddClickEventListener(self.node_list["btn_enter"], BindTool.Bind(self.OnClickEnter, self))
    XUI.AddClickEventListener(self.node_list["btn_exp_rule"], BindTool.Bind(self.OnClickExpRule, self))
    self.node_list.btn_add_times:SetActive(false)
end

function BootybayTeamBaoMingView:CloseCallBack()
end

function BootybayTeamBaoMingView:ShowIndexCallBack()
   self:Flush()
end

function BootybayTeamBaoMingView:OnClickPingTai()
	NewTeamWGData.Instance:SetTeamTypeAndMode(0,1)
    ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_pingtai)
end
function BootybayTeamBaoMingView:OnClickEnter()
    if SocietyWGData.Instance:GetIsInTeam() == 1 then
    	--单人给提示
    	--多人弹准备界面
		-- if SocietyWGData.Instance:GetIsTeamLeader() == 1 then
            local member_count = SocietyWGData.Instance:GetTeamMemberCount()
            local enter_teamfb_times = BootyBayWGData.Instance:GetEnterTeamFBTimes()
			local other_cfg = BootyBayWGData.Instance:GetOtherConfig()
			local role_id = RoleWGData.Instance:GetRoleVo().role_id
            if (enter_teamfb_times - other_cfg.team_fb_enter_times_limit) == 0 then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.NotTimesEnter)
                return
            end
            if member_count == 1 then
            	local ok_func = function ()
            		FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.FBCT_WABAO_TEAM_FB)
                    self:Close()
            	end
                TipWGCtrl.Instance:OpenAlertTips(Language.BootyBay.TeamDesc,ok_func)
                return
            end

            if member_count > 1 and member_count < 3 then
            	local ok_func = function ()
					BootyBayWGCtrl.Instance:SendCSWabaoOpera(WABAO_OP.WABAO_OP_QUERY_TEAM_FB_OPEN_INFO, role_id, 0)
                    self:Close()
            	end
                TipWGCtrl.Instance:OpenAlertTips(Language.BootyBay.TeamDesc,ok_func)
                return
            end

			BootyBayWGCtrl.Instance:SendCSWabaoOpera(WABAO_OP.WABAO_OP_QUERY_TEAM_FB_OPEN_INFO, role_id, 0)
            self:Close()
		-- else
		-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.NotLeaderTip)
		-- end
	end
end



function BootybayTeamBaoMingView:OnClickExpRule()
    local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.NewTeam.EXP_rule_title)
	rule_tip:SetContent(Language.NewTeam.EXP_desc_rule)
end

function BootybayTeamBaoMingView:OnFlush(param_t)

    if not self.member_info_list then
        self.member_info_list = {}
    end
    local member_info_list = SocietyWGData.Instance:GetTeamMemberList()
    for i = 1, 3 do
        if not self.member_info_list[i] then
            self.member_info_list[i] = BootybayBaoMingMemberInfoRender.New(self.node_list["info"..i])
        end
        if member_info_list[i] then
            self.member_info_list[i]:SetData(member_info_list[i])
        else
            self.member_info_list[i]:SetData({})
        end
    end
    --界面名字为当前目标名
    -- local team_type, fb_mode = NewTeamWGData.Ins
    self.node_list["title_view_name"].text.text = Language.BootyBay.BootyBayFBTitle2
    --剩余次数
    local enter_teamfb_times = BootyBayWGData.Instance:GetEnterTeamFBTimes()
	local other_cfg = BootyBayWGData.Instance:GetOtherConfig()
    local times = other_cfg.team_fb_enter_times_limit - enter_teamfb_times
    local color = times >= 0 and COLOR3B.GREEN or COLOR3B.RED
    self.node_list.remain_times.text.text = string.format(Language.NewTeam.RemainRewardTimes, color, times, other_cfg.team_fb_enter_times_limit)

end

function BootybayTeamBaoMingView:OnTeamChange()
	self:Flush()
end


-------------------------------------------------------------------------------------------
--- BootybayBaoMingMemberInfoRender
-------------------------------------------------------------------------------------------

BootybayBaoMingMemberInfoRender = BootybayBaoMingMemberInfoRender or BaseClass(BaseRender)
function BootybayBaoMingMemberInfoRender:__init()
    self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
end
function BootybayBaoMingMemberInfoRender:__delete()
    if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end
function BootybayBaoMingMemberInfoRender:OnFlush()
    if not self.data then return end
    local is_empty_data = IsEmptyTable(self.data)

	--没有队员信息，隐藏相关信息
	self.node_list.no_data_hide:SetActive(not is_empty_data and self.data.role_id > 0)
    self.node_list.no_data_active:SetActive(is_empty_data)

    if is_empty_data then return end
    --队长标记
	self.node_list.leader_img:SetActive(false)

    if self.data.role_id > 0 then
        ----人物名字
        self:SetName()
		--Vip等级
        self.node_list.vip_level:SetActive(self.data.vip_level > 0)
		self.node_list.vip_level.text.text = string.format(Language.NewTeam.VipLevel,  self.data.vip_level)
		--人物等级
		local str = string.format(Language.NewTeam.PTLevel, self.data.level)
		EmojiTextUtil.ParseRichText(self.node_list["role_level"].emoji_text, str, 21, COLOR3B.DEFAULT)
        self:SetHeadCell()
	end
end

function BootybayBaoMingMemberInfoRender:SetName()
    local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
    --人物名字
    if GoalCrossType[team_type] then
        --内网无法请求PHP，先设置默认名字
        local temp_name = string.format(Language.WorldServer.ServerDefName, self.data.server_id)
        self.node_list.role_name.text.text = string.format(Language.NewTeam.ServerName, COLOR3B.RED, temp_name, self.data.name)
    else
        self.node_list.role_name.text.text = self.data.name
    end
end
function BootybayBaoMingMemberInfoRender:SetHeadCell()
    local data = {}
    data.role_id = self.data.orgin_role_id
    data.prof = self.data.prof
    data.sex = self.data.sex
    data.fashion_photoframe = self.data.shizhuang_photoframe
    self.head_cell:SetData(data)
end