DressingRoleDiyView = DressingRoleDiyView or BaseClass(SafeBaseView)

local CUR_FOCUS_SCALE = 
{
	[0] = Vector3(1, 1, 1),
}

local FOCUS_PART_FROM_POS = 
{
	[GameEnum.FEMALE] = {
		[GameEnum.ROLE_PROF_1] = Vector3(0, 1.5, -21.61),
		[GameEnum.ROLE_PROF_2] = Vector3(0, 1.5, -21.61),
		[GameEnum.ROLE_PROF_3] = Vector3(0, 1.5, -21.61),
		[GameEnum.ROLE_PROF_4] = Vector3(0, 1.5, -21.61),
	},

	[GameEnum.MALE] = {
		[GameEnum.ROLE_PROF_1] = Vector3(0, 1.5, -21.61),
		[GameEnum.ROLE_PROF_2] = Vector3(0, 1.5, -21.61),
		[GameEnum.ROLE_PROF_3] = Vector3(0, 1.5, -21.61),
		[GameEnum.ROLE_PROF_4] = Vector3(0, 1.5, -21.61),
	},
}

local FOCUS_PART_TO_POS = {
	[GameEnum.FEMALE] = {
		[GameEnum.ROLE_PROF_1] = Vector3(-0.9, 2.2, -9),
		[GameEnum.ROLE_PROF_2] = Vector3(-0.9, 2.2, -9),
		[GameEnum.ROLE_PROF_3] = Vector3(-0.9, 2.2, -9),
		[GameEnum.ROLE_PROF_4] = Vector3(-0.9, 2.2, -9),
	},

	[GameEnum.MALE] = {
		[GameEnum.ROLE_PROF_1] = Vector3(-0.9, 2.2, -9),
		[GameEnum.ROLE_PROF_2] = Vector3(-0.9, 2.2, -9),
		[GameEnum.ROLE_PROF_3] = Vector3(-0.9, 2.2, -9),
		[GameEnum.ROLE_PROF_4] = Vector3(-0.9, 2.2, -9),
	},
}

function DressingRoleDiyView:__init()
    self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
	self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
    self:SetMaskBg(false, true)
	
    self:AddViewResource(0, "uis/view/dressing_role_diy_ui_prefab", "layout_dressing_role_diy")
	self:SetTabShowUIScene(0, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.DressingDiy})
end

function DressingRoleDiyView:__delete()
end

function DressingRoleDiyView:OpenCallBack()
end

function DressingRoleDiyView:CloseCallBack()

end

function DressingRoleDiyView:LoadCallBack()
	if not self.role_model then
		self.role_model = RoleModel.New()
		self.role_model:SetPositionAndRotation(nil, Vector3(0, 180, 0), nil)
		self.role_model:SetUISceneModel(self.node_list["ph_display"].event_trigger_listener,
								MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.role_model, 0)
	end

	if not self.dressing_diy_list then
		self.dressing_diy_list = AsyncListView.New(DressingRoleDiyCell, self.node_list.dressing_diy_list)
    	self.dressing_diy_list:SetSelectCallBack(BindTool.Bind1(self.OnClickDressingDiyCell, self))
		self.dressing_diy_list:SetStartZeroIndex(true)
	end

	XUI.AddClickEventListener(self.node_list.hide_btn, BindTool.Bind(self.OnClickHideUi, self, 1))
	XUI.AddClickEventListener(self.node_list.hide_close_btn, BindTool.Bind(self.OnClickHideUi, self, 2))
	XUI.AddClickEventListener(self.node_list.lens_btn, BindTool.Bind(self.ClickEditLens, self))
	XUI.AddClickEventListener(self.node_list.use_diy_btn, BindTool.Bind(self.OnClickUseDiy, self))
	XUI.AddClickEventListener(self.node_list.jump_diy_btn, BindTool.Bind(self.ClickJumpDiy, self))
end

function DressingRoleDiyView:ReleaseCallBack()
	self:RemoveCoolDelayTimer()

	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	if self.dressing_diy_list then
        self.dressing_diy_list:DeleteMe()
        self.dressing_diy_list = nil
    end

	self.is_btn_cool = nil                          -- 是否还在冷却
	self.is_far = nil                               -- 是否是远景
	self.select_diy_data = nil
end

function DressingRoleDiyView:ShowIndexCallBack()
	self:FlushLensStatus()
end


function DressingRoleDiyView:OnFlush(param_t)
	local main_vo = GameVoManager.Instance:GetMainRoleVo()
	local sex = main_vo.sex
	local prof = main_vo.prof % 10

	local diy_list_info = DressingRoleDiyWGData.Instance:GetDiyAppearanceInfoBySexProf(sex, prof)
	if not diy_list_info then
		return
	end

	self.dressing_diy_list:SetDataList(diy_list_info.project_list)
	
	if not self.select_diy_data then
		self.dressing_diy_list:JumpToIndex(diy_list_info.use_project_id)
	end

	local set_num = 0
	for k, v in pairs(diy_list_info.project_list) do
		if v.is_set == 1 then
			set_num = set_num + 1
		end
	end

	self.node_list.app_num.text.text = string.format("%s/%s", set_num, (#diy_list_info.project_list + 1))
end

function DressingRoleDiyView:OnClickDressingDiyCell(cell)
	if nil == cell or nil == cell.data then
		return
	end

	if self.select_diy_data == cell.data then 
		return 
	end

	self.select_diy_data = cell.data
	self:FlushModelDisPlay()
end

function DressingRoleDiyView:OnClickHideUi(index)
	self.node_list["hide_close_btn"]:SetActive(index == 1)
	self.node_list["ui_panel"]:SetActive(index == 2)
end

-- 点击镜头切换
function DressingRoleDiyView:ClickEditLens()
    if self.is_btn_cool then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.OperateFrequencyTip)
        return
    end

    self.is_btn_cool = true
    self:RemoveCoolDelayTimer()
	self.show_cool_timer = GlobalTimerQuest:AddDelayTimer(function ()
		self.is_btn_cool = false
	end, 1)

    self:FlushLensStatus(not self.is_far)
end

--移除回调
function DressingRoleDiyView:RemoveCoolDelayTimer()
    if self.show_cool_timer then
        GlobalTimerQuest:CancelQuest(self.show_cool_timer)
        self.show_cool_timer = nil
    end
end

-- 刷新镜头设置
function DressingRoleDiyView:FlushLensStatus(is_force_far)
	self.is_far = is_force_far
    if self.role_model ~= nil then
		local main_vo = GameVoManager.Instance:GetMainRoleVo()
		local sex = main_vo.sex
		local prof = main_vo.prof % 10
		local focus_part_to_pos = (FOCUS_PART_TO_POS[sex] or {})[prof]
		local focus_part_from_pos = (FOCUS_PART_FROM_POS[sex] or {})[prof]
		local cur_focus_to_scale = CUR_FOCUS_SCALE[0]
		local cur_focus_from_scale = CUR_FOCUS_SCALE[0]
		self.role_model:SetModelFocus(focus_part_to_pos, focus_part_from_pos, cur_focus_to_scale, cur_focus_from_scale, 0.4)

		if self.is_far then
			self.role_model:ModelFocusMove(false)
		else
			self.role_model:ModelFocusMove(true)
		end
    end

    self.node_list.lens_far_img:CustomSetActive(not self.is_far)
    self.node_list.lens_near_img:CustomSetActive(self.is_far)
	self.node_list.lend_text.text.text = self.is_far and Language.DressingRoleDiyView.FarStr[0] or Language.DressingRoleDiyView.FarStr[1]
end

function DressingRoleDiyView:OnClickUseDiy()
	local main_vo = GameVoManager.Instance:GetMainRoleVo()
	local sex = main_vo.sex
	local prof = main_vo.prof % 10

	local diy_list_info = DressingRoleDiyWGData.Instance:GetDiyAppearanceInfoBySexProf(sex, prof)
	if not diy_list_info then
		return
	end

	if diy_list_info.use_project_id == self.select_diy_data.project_id then
		TipWGCtrl.Instance:ShowSystemMsg(Language.DressingRoleDiyView.ErrorTip[1])
		return
	end

	DressingRoleDiyWGCtrl.Instance:SendCSDiyAppearanceOperate(DIY_APPEARANCE_OPER_TYPE.USE_PROJECT, sex, prof, self.select_diy_data.project_id)
end

function DressingRoleDiyView:ClickJumpDiy()
	local main_vo = GameVoManager.Instance:GetMainRoleVo()
	local sex = main_vo.sex
	local prof = main_vo.prof % 10

	local data = {}
	data.operate_type = ROLE_DIY_APPEARANCE_TYPE.CHANGE_DIY
	data.sex = sex
	data.prof = prof
    RoleDiyAppearanceWGCtrl.Instance:SetViewDataAndOpen(data)
    self:Close()
end

function DressingRoleDiyView:FlushModelDisPlay()
	if not self.select_diy_data then
		return
	end

	local main_vo = GameVoManager.Instance:GetMainRoleVo()
	local sex = main_vo.sex
	local prof = main_vo.prof % 10
	local role_res_id = RoleWGData.GetJobModelId(sex, prof)
	local role_diy_appearance_info = self.select_diy_data.role_diy_appearance
	local extra_role_model_data = {
		prof = prof,
		sex = sex,
		
        d_face_res = self.select_diy_data.new_face,
        d_hair_res = self.select_diy_data.new_hair,
		d_body_res = self.select_diy_data.new_body,

		eye_size = role_diy_appearance_info.eye_size,
		eye_shadow_color = role_diy_appearance_info.eye_shadow_color,
		eye_position = role_diy_appearance_info.eye_position,

		left_pupil_type = role_diy_appearance_info.left_pupil_type,
		left_pupil_size = role_diy_appearance_info.left_pupil_size,
		left_pupil_color = role_diy_appearance_info.left_pupil_color,

		right_pupil_type = role_diy_appearance_info.right_pupil_type,
		right_pupil_size = role_diy_appearance_info.right_pupil_size,
		right_pupil_color = role_diy_appearance_info.right_pupil_color,

		mouth_size =  role_diy_appearance_info.mouth_size,
		mouth_position =  role_diy_appearance_info.mouth_position,
		mouth_color = role_diy_appearance_info.mouth_color,

		face_decal_id = role_diy_appearance_info.face_decal_id,
		preset_seq = role_diy_appearance_info.preset_seq,
		hair_color = role_diy_appearance_info.hair_color,
		animation_name = SceneObjAnimator.RoleDiyAppearance or SceneObjAnimator.UiIdle,
    }

	self.role_model:SetRoleResid(0, nil, extra_role_model_data)
end
---------------------------------- DressingRoleDiyCell------------------------------------
DressingRoleDiyCell = DressingRoleDiyCell or BaseClass(BaseRender)
function DressingRoleDiyCell:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.lock_btn, BindTool.Bind(self.ClickLock, self))
	XUI.AddClickEventListener(self.node_list.name_change_btn, BindTool.Bind(self.ClickChangeName, self))
end

function DressingRoleDiyCell:__delete()

end

function DressingRoleDiyCell:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.node_list.name_change_btn:SetActive(self.data.is_set == 1)
	self.node_list.lock_btn:SetActive(self.data.is_set ~= 1)
	if self.data.is_set == 1 then
		local name_str = self.data.project_name ~= "" and self.data.project_name or 
					string.format(Language.DressingRoleDiyView.DiyName[0], NumberToChinaNumber(self.data.project_id + 1))
		self.node_list.name_str.text.text = name_str
	else
		self.node_list.name_str.text.text = Language.DressingRoleDiyView.DiyName[1]
	end

	local main_vo = GameVoManager.Instance:GetMainRoleVo()
	local sex = main_vo.sex
	local prof = main_vo.prof % 10

	local diy_list_info = DressingRoleDiyWGData.Instance:GetDiyAppearanceInfoBySexProf(sex, prof)
	if not diy_list_info then
		return
	end

	self.node_list.use_flag:SetActive(diy_list_info and diy_list_info.use_project_id == self.data.project_id)
end

function DressingRoleDiyCell:OnSelectChange(is_select)
    self.node_list["hl_img"]:CustomSetActive(is_select)
end

function DressingRoleDiyCell:ClickLock()
	TipWGCtrl.Instance:ShowSystemMsg(Language.DressingRoleDiyView.ErrorTip[0])
end

function DressingRoleDiyCell:ClickChangeName()
	if IsEmptyTable(self.data) then
		return
	end

	if self.data.is_set ~= 1 then
		return
	end

	DressingRoleDiyWGCtrl.Instance:OpeRenameView(self.data)
end