-- 市场购买
function MarketView:BuyLoadCallBack()
	self.buy_big_type_list = AsyncListView.New(MarketBigTypeItem, self.node_list["buy_type_list"])
	self.buy_big_type_list:SetSelectCallBack(BindTool.Bind1(self.OnClickBuyBigType, self))
	self.buy_big_type_list:SetDataList(MarketWGData.Instance:GetBigTypeCfg())
	self.buy_big_id = 1
	self.buy_sub_type = -1

	--购买列表
	--self.buy_goods_list = AsyncListView.New(MarketBuyGoodsItem, self.node_list["buy_goods_list"])

	--local goods_list_info = self:CalGoodsListInfo(MarketWGData.Instance:GetGoodsListInfo(self.buy_big_id, self.buy_sub_type))	

	if not self.buy_goods_list then
		local bundle = "uis/view/market_ui_prefab" 
		local asset = "buy_goods_list_item"
		self.buy_goods_list = AsyncBaseGrid.New()
		self.buy_goods_list:CreateCells({col = 3, change_cells_num = 1, list_view = self.node_list["buy_goods_list"],
		assetBundle = bundle, assetName = asset, itemRender = MarketBuyGoodsItem, complement_col_item = true, })
	end

	if not self.buy_subtype_grid then
		self.buy_subtype_grid = MarketSubTypeGrid.New()
		self.buy_subtype_grid:SetStartZeroIndex(false)
		self.buy_subtype_grid:CreateCells({
			col = 3,
			list_view = self.node_list["buy_subtype_panel"],
			itemRender = MarketSubTypeRender,
			assetBundle = "uis/view/market_ui_prefab",
			assetName = "subtype_item_prefab",
			change_cells_num = 1,
			complement_col_item = true,
		})
		self.buy_subtype_grid:SetSelectCallBack(BindTool.Bind(self.OnClickBuySubType, self))
	end
	self.buy_sub_type_item_list = {}

	-- 密码筛选下拉框
	self.node_list["buy_password_dropdown"].dropdown.onValueChanged:AddListener(BindTool.Bind1(self.OnPasswordDropdownChange, self))
	self.password_dropdown_select = MARKET_PASSWORD_DROPDOWN_TYPE.NONE
	self.node_list["buy_password_dropdown"].dropdown.value = self.password_dropdown_select

	-- 品阶筛选下拉框
	self.node_list["buy_order_dropdown"].dropdown.onValueChanged:AddListener(BindTool.Bind1(self.OnOrderDropdownChange, self))
	self.order_dropdown_select = 0
	self.node_list["buy_order_dropdown"].dropdown.value = self.order_dropdown_select

	-- 品质筛选下拉框
	self.node_list["buy_color_dropdown"].dropdown.onValueChanged:AddListener(BindTool.Bind1(self.OnColorDropdownChange, self))
	self.color_dropdown_select = 0
	self.node_list["buy_color_dropdown"].dropdown.value = self.color_dropdown_select

	-- 搜索按钮
	XUI.AddClickEventListener(self.node_list["buy_search_btn"], BindTool.Bind1(self.OnClickBuySearch, self))
	self.node_list.buy_search_input.input_field.onSelect:AddListener(BindTool.Bind(self.OnInputFieldClickOrEndEdit, self, true, MarketViewIndex.Buy))
	self.node_list.buy_search_input.input_field.onEndEdit:AddListener(BindTool.Bind(self.OnInputFieldClickOrEndEdit, self, false, MarketViewIndex.Buy))

	--tab页签的箭头显示.
	self.node_list.buy_type_list.scroller.scrollerEndScrolled = BindTool.Bind(self.BuyScrollerEndScrolled, self)

	-- 按照单价排序
	--XUI.AddClickEventListener(self.node_list["buy_unit_price_sort_btn"], BindTool.Bind(self.OnBuyUnitPriceSort, self))
	self.buy_unit_sort_param = 0
	self.is_unit_sort = false
	self.time_sort_param = 1

	-- 按总价排序
	--XUI.AddClickEventListener(self.node_list["buy_total_price_sort_btn"], BindTool.Bind(self.OnBuyTotalPriceSort, self))
	self.buy_total_sort_param = 0
	self.is_total_sort = false
end

function MarketView:BuyReleaseCallBack()
	if self.buy_big_type_list ~= nil then
		self.buy_big_type_list:DeleteMe()
		self.buy_big_type_list = nil
	end

	if self.buy_sub_type_item_list then
		for i,v in ipairs(self.buy_sub_type_item_list) do
			v:DeleteMe()
		end
		self.buy_sub_type_item_list = {}
	end

	if self.buy_goods_list then
		self.buy_goods_list:DeleteMe()
		self.buy_goods_list = nil
	end

	if self.subtype_item_group then
		self.subtype_item_group:DeleteMe()
		self.subtype_item_group = nil
	end

	if nil ~= self.buy_subtype_grid then
		self.buy_subtype_grid:DeleteMe()
		self.buy_subtype_grid = nil
	end
end

function MarketView:BuyShowIndexCallBack()
end

function MarketView:BuyOnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			if v.open_param then
				-- 默认跳转
				local auction_cfg = MarketWGData.Instance:GetAuctionCfgByItemId(v.open_param)
				if auction_cfg then
					self.buy_big_type_list:JumpToIndex(auction_cfg.big_id, 9) 		-- 跳转到对应大类型
					self.buy_big_id = auction_cfg.big_id
					self.default_buy_sub_type = auction_cfg.small_id
					local sub_type_cfg = MarketWGData.Instance:GetSubTypeCfgBySubType(auction_cfg.small_id)
					self:OnClickFlushPanel(sub_type_cfg)
				end
			end
			self:FlushBuyAllPart()
		elseif k == "Part" then
			if v.flush_buy_goods then
				self:FlushBuyGoods()
			end
			if v.flush_sub_type_grid then
				self:FlushBuySubTypeGrid()
			end
		end
	end
end

function MarketView:FlushBuyAllPart()
	self:FlushBuyChildPanelActive()
	self:FlushBuySubTypeGrid()
	self:FlushBuyGoods()
end

--tab页签的箭头显示.
function MarketView:BuyScrollerEndScrolled()
	local val = self.node_list.buy_type_list.scroll_rect.horizontalNormalizedPosition
	self.node_list.market_buy_l_img:SetActive(val ~= 0 and val > 0.1)
	self.node_list.market_buy_r_img:SetActive(val ~= 0 and val < 0.9)
end

-- 子面板显隐
function MarketView:FlushBuyChildPanelActive()
	self.node_list["buy_subtype_panel"]:SetActive(self.buy_sub_type == -1)
	self.node_list["buy_goods_list_panel"]:SetActive(self.buy_sub_type ~= -1)
end

-- 刷新子类型grid
function MarketView:FlushBuySubTypeGrid()
	local sub_type_cfg = MarketWGData.Instance:GetSubTypeCfgForBuyView(self.buy_big_id)

	if IsEmptyTable(sub_type_cfg) then
		return
	end

	self.buy_subtype_grid:SetDataList(sub_type_cfg)
	if #sub_type_cfg <= 2 then
		self.buy_subtype_grid:JumpToIndexAndSelect(1)
	end
end

-- 刷新阶数筛选下拉框
function MarketView:FlushBuyOrderDrowDown()
	if self.buy_sub_type < 0 then
		self.node_list["buy_order_dropdown"]:SetActive(false)
		return
	end
	if IsEmptyTable(self.buy_order_list) then
		self.order_dropdown_select = 0
		self.node_list["buy_order_dropdown"]:SetActive(false)
		return
	end
	local list_string = System.Collections.Generic.List_string.New()
	list_string:Add(Language.Market.NameList1[0])
	for i, order in ipairs(self.buy_order_list) do
		list_string:Add(Language.Market.NameList1[order])
	end
	self.node_list["buy_order_dropdown"].dropdown:ClearOptions()
	self.node_list["buy_order_dropdown"].dropdown:AddOptions(list_string)
	self.node_list["buy_order_dropdown"].dropdown.value = self.order_dropdown_select
	self.node_list["buy_order_dropdown"]:SetActive(true)
end

-- 刷新品质筛选下拉框
function MarketView:FlushBuyColorDrowDown()
	if self.buy_sub_type < 0 then
		self.node_list["buy_color_dropdown"]:SetActive(false)
		return
	end
	if IsEmptyTable(self.buy_color_list) then
		self.color_dropdown_select = 0
		self.node_list["buy_color_dropdown"]:SetActive(false)
		return
	end
	local list_string = System.Collections.Generic.List_string.New()
	list_string:Add(Language.Market.NameList2[0])
	for i, color in ipairs(self.buy_color_list) do
		list_string:Add(Language.Market.NameList2[color])
	end
	self.node_list["buy_color_dropdown"].dropdown:ClearOptions()
	self.node_list["buy_color_dropdown"].dropdown:AddOptions(list_string)
	self.node_list["buy_color_dropdown"].dropdown.value = self.color_dropdown_select
	self.node_list["buy_color_dropdown"]:SetActive(true)
end

-- 根据大类型和子类型刷新商品列表
function MarketView:FlushBuyGoods()
	if self.buy_sub_type < 0 then
		return
	end

	local goods_list_info = self:CalGoodsListInfo(MarketWGData.Instance:GetGoodsListInfo(self.buy_big_id, self.buy_sub_type))
	if self.buy_goods_list then
		self.buy_goods_list:SetStartZeroIndex(false)
		self.buy_goods_list:SetDataList(goods_list_info)
		-- self.buy_goods_list:RefreshActiveCellViews()
	end

	--self.buy_goods_list:SetDataList(goods_list_info)
	--self.buy_goods_list:RefreshActiveCellViews()
	-- local bundel, asset
	-- local bundel1, asset1
	-- local bundel2, asset2
	-- local bundel3, asset3
	-- if self.is_unit_sort then
	-- 	if self.buy_unit_sort_param > 0 then
			
	-- 		bundel, asset = ResPath.GetF2CommonImages("t_paixu_xuanzhon")
	-- 		bundel1, asset1 = ResPath.GetF2CommonImages("t_paixu_moren")
	-- 	else
	-- 		bundel, asset = ResPath.GetF2CommonImages("t_paixu_moren")
	-- 		bundel1, asset1 = ResPath.GetF2CommonImages("t_paixu_xuanzhon")
	-- 	end
	-- 	self.is_unit_sort = false
	-- else
	-- 	bundel, asset = ResPath.GetF2CommonImages("t_paixu_moren")
	-- 	bundel1, asset1 = ResPath.GetF2CommonImages("t_paixu_moren")
	-- end

	-- if self.is_total_sort then
	-- 	if self.buy_total_sort_param > 0 then
	-- 		bundel2, asset2 = ResPath.GetF2CommonImages("t_paixu_xuanzhon")
	-- 		bundel3, asset3 = ResPath.GetF2CommonImages("t_paixu_moren")
	-- 	else
	-- 		bundel2, asset2 = ResPath.GetF2CommonImages("t_paixu_moren")
	-- 		bundel3, asset3 =  ResPath.GetF2CommonImages("t_paixu_xuanzhon")
	-- 	end
	-- 	self.is_total_sort = false
	-- else
	-- 	bundel2, asset2 = ResPath.GetF2CommonImages("t_paixu_moren")
	-- 	bundel3, asset3 =  ResPath.GetF2CommonImages("t_paixu_moren")
	-- end
	
	-- self.node_list["unit_down"].image:LoadSprite(bundel, asset, function ()
	-- 	self.node_list["unit_down"].image:SetNativeSize()
	-- end)

	-- self.node_list["unit_up"].image:LoadSprite(bundel1, asset1, function ()
	-- 	self.node_list["unit_up"].image:SetNativeSize()
	-- end)

	-- self.node_list["total_down"].image:LoadSprite(bundel2, asset2, function ()
	-- 	self.node_list["total_down"].image:SetNativeSize()
	-- end)

	-- self.node_list["total_up"].image:LoadSprite(bundel3, asset3, function ()
	-- 	self.node_list["total_up"].image:SetNativeSize()
	-- end)

	self.node_list["buy_empty_tips"]:SetActive(IsEmptyTable(goods_list_info))
end

-- 筛选商品
function MarketView:CalGoodsListInfo(goods_list)
	local result = {}
	for i,v in ipairs(goods_list) do
		if self:CanShowGoods(v) then
			v.sort_key = (v.total_price / v.item_data.num) * self.buy_unit_sort_param + v.total_price * self.buy_total_sort_param + self.time_sort_param * v.sale_time
			table.insert(result, v)
		end
	end
	table.sort(result, SortTools.KeyUpperSorter("sort_key"))
	return result
end

function MarketView:CanShowGoods(goods_info)
	-- 筛选密码
	if self.password_dropdown_select == MARKET_PASSWORD_DROPDOWN_TYPE.NO_PASSWORD then
		if goods_info.has_password then
			return false
		end
	elseif self.password_dropdown_select == MARKET_PASSWORD_DROPDOWN_TYPE.HAS_PASSWORD then
		if not goods_info.has_password then
			return false
		end
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(goods_info.item_data.item_id)
	-- 筛选阶数
	if self.order_dropdown_select ~= 0 then
		if item_cfg.order ~= self.buy_order_list[self.order_dropdown_select] then
			return false
		end
	end

	-- 筛选品质
	if self.color_dropdown_select ~= 0 then
		if item_cfg.color ~= self.buy_color_list[self.color_dropdown_select] then
			return false
		end
	end

	-- 筛选搜索文字内容
	if self.buy_search_name and self.buy_search_name ~= "" then
		if not string.find(item_cfg.name, self.buy_search_name) then
			return false
		end
	end

	return true
end


function MarketView:OnInputFieldClickOrEndEdit(is_need_clear, index)
	if is_need_clear then
		self.node_list["Placeholder" .. index].text.text = ""
	else
		self.node_list["Placeholder" .. index].text.text = Language.Market.Placeholder_Text
	end
end

-- 点击大类型
function MarketView:OnClickBuyBigType(big_type_item, select_index)
	self.buy_big_id = big_type_item.data.big_id
	-- 清空子类型
	self.buy_sub_type = self.default_buy_sub_type or -1
	self.default_buy_sub_type = nil

	-- 清空筛选下拉框选择
	self.buy_order_list = {}
	self.order_dropdown_select = 0
	self.buy_color_list = {}
	self.color_dropdown_select = 0

	self.node_list["buy_password_dropdown"].dropdown.value = MARKET_PASSWORD_DROPDOWN_TYPE.NONE

	self.buy_search_name = ""

	self:FlushBuyAllPart()

	TweenManager.Instance:ExecuteViewTween(GuideModuleName.Market, MarketViewIndex.Buy, self.node_list)
end

-- 点击子类型
function MarketView:OnClickBuySubType(cell)
	if not cell or not cell:GetData() or IsEmptyTable(cell:GetData()) then
		return
	end

	local sub_type_cfg = cell:GetData()
	self:OnClickFlushPanel(sub_type_cfg)
end

function MarketView:OnClickFlushPanel(sub_type_cfg)
	self:ClearBuySortParam()
	self.buy_sub_type = sub_type_cfg.small_id

	-- 判断是不是“全部”子类型
	if MarketWGData.Instance:IsAllSubtype(self.buy_sub_type) then
		MarketWGCtrl.Instance:SendCSRoleBigTypeAuctionItem(self.buy_big_id)
	else
		MarketWGCtrl.Instance:SendCSRoleAuctionItem(self.buy_big_id, self.buy_sub_type)
	end

	self.buy_order_list = MarketWGData.Instance:GetOrderList(self.buy_big_id, self.buy_sub_type)
	self.buy_color_list = MarketWGData.Instance:GetColorList(self.buy_big_id, self.buy_sub_type)
	self:FlushBuyChildPanelActive()
	self:FlushBuyOrderDrowDown()
	self:FlushBuyColorDrowDown()
	self:FlushBuyGoods()
end

-- 密码筛选下拉框更变
function MarketView:OnPasswordDropdownChange(index)
	self.password_dropdown_select = index
	self:FlushBuyGoods()
end

-- 阶数筛选下拉框更变
function MarketView:OnOrderDropdownChange(index)
	self.order_dropdown_select = index
	self:FlushBuyGoods()
end

-- 品质筛选下拉框更变
function MarketView:OnColorDropdownChange(index)
	self.color_dropdown_select = index
	self:FlushBuyGoods()
end

-- 点击搜索
function MarketView:OnClickBuySearch()
	self.buy_search_name = self.node_list["buy_search_input"].input_field.text
	self:FlushBuyGoods()
end

-- -- 点击按单价排序
-- function MarketView:OnBuyUnitPriceSort()
-- 	self.time_sort_param = 0
-- 	self.buy_total_sort_param = 0
-- 	self.is_unit_sort = true
-- 	self.is_total_sort = false
-- 	if self.buy_unit_sort_param == 0 then
-- 		self.buy_unit_sort_param = 1
-- 	else
-- 		self.buy_unit_sort_param = self.buy_unit_sort_param * -1
-- 	end
-- 	self:FlushBuyGoods()
-- end

-- 点击按总价排序
-- function MarketView:OnBuyTotalPriceSort()
-- 	self.time_sort_param = 0
-- 	self.buy_unit_sort_param = 0
-- 	self.is_unit_sort = false
-- 	self.is_total_sort = true
-- 	if self.buy_total_sort_param == 0 then
-- 		self.buy_total_sort_param = 1
-- 	else
-- 		self.buy_total_sort_param = self.buy_total_sort_param * -1
-- 	end
-- 	self:FlushBuyGoods()
-- end

-- 清理排序参数
function MarketView:ClearBuySortParam()
	self.buy_unit_sort_param = 0
	self.buy_total_sort_param = 0
	self.time_sort_param = 1
end

---------------------市场购买面板商品列表item------------------
MarketBuyGoodsItem = MarketBuyGoodsItem or BaseClass(BaseRender)

function MarketBuyGoodsItem:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list["item_cell"])
	XUI.AddClickEventListener(self.node_list["click"], BindTool.Bind1(self.OnClick, self))
end

function MarketBuyGoodsItem:__delete()
	self.item_cell:DeleteMe()
	self.item_cell = nil
end

function MarketBuyGoodsItem:OnFlush()
	self.node_list["fake_hide"]:CustomSetActive(not IsEmptyTable(self.data))

	if IsEmptyTable(self.data) then
		return
	end

	local other_cfg = MarketWGData.Instance:GetOtherCfg()
	local item_data = {}
	item_data.item_id = self.data.item_data.item_id
	item_data.is_bind = self.data.item_data.is_bind
	local item_count = (other_cfg.gold_bind_id == self.data.item_data.item_id) and (self.data.item_data.num * other_cfg.min_shelves_gond_bind_num) or self.data.item_data.num
	item_data.num = item_count
	item_data.star_level = ((self.data.item_data or {}).param or {}).star_level or 0

	self.item_cell:SetData(item_data)

	local is_mine_goods = self.data.seller_id == GameVoManager.Instance:GetMainRoleVo().role_id
	local has_password = self.data.has_password

	-- 物品名称
	self.node_list["item_name"].text.text = ItemWGData.Instance:GetItemName(self.data.item_data.item_id, nil, false)-- .. my_goods_str

	-- 评分
	local pingfen = EquipmentWGData.Instance:GetEquipPingfen(self.data.item_data)

	--本人出售商品
	self.node_list["img_mine"]:SetActive(is_mine_goods)

	--密码
	self.node_list["password_status"]:SetActive(has_password)

	-- 等级
	--self.node_list["item_level"].text.text = Language.Market.NameList1[item_cfg.order] --string.format(Language.Common.Level, item_cfg.limit_level)

	-- 单价
	--local num = self.data.item_data.num > 0 and self.data.item_data.num or 1					-- 防止num为0
	--self.node_list["unit_price"].text.text = math.ceil(self.data.total_price / num)

	--数量
	local sale_count = self.data.item_data.num > 0 and self.data.item_data.num or 1
	self.node_list["item_count"].text.text = string.format(Language.Market.SaleCount, sale_count)

	-- 总价
	self.node_list["total_price"].text.text = self.data.total_price

	local cfg = MarketWGData.Instance:GetAuctionCfgByItemId(self.data.item_data.item_id)
	local auction_price_type = cfg.auction_price_type
	local icon_name = AUCTION_PRICE_TYPE_ICON[auction_price_type]
	if icon_name then
		local bundel, asset = ResPath.GetCommonIcon(icon_name)
		self.node_list["total_money_type_icon"].image:LoadSprite(bundel, asset, function ()
			self.node_list["total_money_type_icon"].image:SetNativeSize()
		end)
	end
end

function MarketBuyGoodsItem:OnClick()
	if not self.data then
		return
	end

	local item_data = DeepCopy(self.data.item_data)
	if ControlBeastsCultivateWGData.Instance:IsBeastAlchemyEquipItem(self.data.item_data.item_id) then
		local param = self.data.item_data.param
		local words_list = {}

		for j = 0, BEAST_EQUIP_OPERATE_TYPE.MAX_BEAST_EQUIP_WORDS_SEQ - 1 do
			words_list[j] = {}
			words_list[j].words_seq = param.xianpin_type_list[j + 1] or -1			-- 词条 seq = words / 100 words_value = words % 100
		end

		local equip_data = {}
		equip_data.item_id = item_data.item_id
		equip_data.words_list = words_list
		item_data.equip_info = equip_data
		item_data.is_bag_equip = true
	end

	if self.data.seller_id == GameVoManager.Instance:GetMainRoleVo().role_id then
		TipWGCtrl.Instance:OpenItem(item_data)
	else
		TipWGCtrl.Instance:OpenItem(item_data, ItemTip.FROME_MARKET_GOUMAI)
	end
	-- if self.data.knapsack_type == KNAPSACK_TYPE.SHENSHOU then
	-- 	ShenShouWGCtrl.Instance:OpenShenShouEquipTip(self.data, ShenShouEquipTip.SHICHANG_GOUMAI)
	-- else
	-- 	TipWGCtrl.Instance:OpenItem(self.data.item_data, ItemTip.FROME_MARKET_GOUMAI)
	-- end
end

------------------------购买类型的列表------------------------
MarketSubTypeGrid = MarketSubTypeGrid or BaseClass(AsyncBaseGrid)

function MarketSubTypeGrid:GetCellBySmallId(small_id)
	if not small_id then
		return
	end

	small_id = tonumber(small_id)
	local cell_list = self:GetAllCell()

	for _, cell in ipairs(cell_list) do

		if small_id == cell:GetData().small_id then
			return cell
		end
	end

	return {}
end

------------------------------购买类型的物体------------------------------
MarketSubTypeRender = MarketSubTypeRender or BaseClass(BaseRender)

function MarketSubTypeRender:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list["item_cell"])
		self.item_cell:SetUseButton(false)
	end
end

function MarketSubTypeRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function MarketSubTypeRender:OnFlush()
	local has_data = not IsEmptyTable(self.data)
	self.node_list["fake_hide"]:CustomSetActive(has_data)

    if not has_data then
        return
    end

	self.node_list["sub_type_name"].text.text = self.data.small_name or ""
	self.item_cell:SetData({item_id = self.data.item_id})
	self.item_cell:SetGradumalMaskEnable(false)
	local goods_amount = MarketWGData.Instance:GetGoodsAmount(self.data.big_id, self.data.small_id)
	self.node_list["sub_type_sell_amount"].text.text = string.format(Language.Market.GoodsAmountStr, goods_amount)
end