YanYuGeWLTZFaceSlapView = YanYuGeWLTZFaceSlapView or BaseClass(SafeBaseView)

function YanYuGeWLTZFaceSlapView:__init()
    self:SetMaskBg(true, true)
    self.view_layer = UiLayer.Pop
    self.view_style = ViewStyle.Window

    self:AddViewResource(0, "uis/view/yanyuge_ui_prefab", "layout_yanyuge_wltz_face_slap_view")
end

function YanYuGeWLTZFaceSlapView:LoadCallBack()
    self.node_list.tween_root.canvas_group.alpha = 1
    XUI.AddClickEventListener(self.node_list.btn_open_wltz, BindTool.Bind(self.OnClickOpenWLTZBtn, self))
end

function YanYuGeWLTZFaceSlapView:OnClickOpenWLTZBtn()
    self.node_list.tween_root.canvas_group:DoAlpha(1, 0, 1.6)

    ReDelayCall(self, function ()
        ViewManager.Instance:Open(GuideModuleName.YanYuGePrivilegeView, TabIndex.yanyuge_privilege_wltz)
    end, 1, "YanYuGeWLTZFaceSlapView")

    local bundle, asset = ResPath.GetUIEffect("UI_yyxy_dakai")
    EffectManager.Instance:PlayAtTransform(bundle, asset, self.node_list["effect_root"].transform, 1.6, nil, nil, nil, nil, function ()
        self:Close()
    end)
end