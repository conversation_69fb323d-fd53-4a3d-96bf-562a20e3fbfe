---------F2VIP---------

local vip_panel_mark = {
	free_card = 1,	-- 免费卡界面
	gold_card = 2, 	-- 买过付费卡界面
}

local free_btn_state = {
	None = 0,
	daily_reward = 1,		-- 领取每日礼包奖励
	get_reward = 2,			-- 领取过每日奖励 or 领取过这张免费卡了
	free_card = 3,			-- 免费卡领取(在线卡和登录卡)
	buy_card = 4,			-- 付费购买卡
	not_time = 5,			-- 时间未到 or 登录天数不足
	not_enough = 6,			-- 条件未达成
	pass_day = 7,			-- VIP过期
	zero_buy = 8,			-- 零元购买
	buy_zs_gift = 9,		-- 购买VIP专属礼包
}

local special_tequan_level = 6
local tequan_vip_title_name = "vip_tequan_md_title_"

function RechargeView:InitVipPanel()
	self.show_vip_panel_type = 0
	self.select_card_info = nil
	self.select_vip_level = 0
	self.is_vip_show_model = false
	self.free_get_btn_state = 0
	self.daily_reward_list = {}
	self.show_daily_gift_mark = false
	self.tequan_item_id = {}

	self.vip_tq_model = OperationActRender.New(self.node_list.tequan_model_root_1)
	self.vip_tq_model:SetModelType(MODEL_CAMERA_TYPE.BASE)

	if nil == self.vip_tq_show_model then
		self.vip_tq_show_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["vip_tq_show_model_root"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = false,
		}
		
		self.vip_tq_show_model:SetRenderTexUI3DModel(display_data)
		-- self.vip_tq_show_model:SetUI3DModel(self.node_list.vip_tq_show_model_root.transform, nil, 1, true, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.vip_tq_show_model)
	end

	if not self.zhuanshu_cell_list then
		self.zhuanshu_cell_list = {}
	end

	self.vip_btn_item_list = AsyncListView.New(VipPanelBtnItemRender, self.node_list.vip_btn_list)
	self.vip_btn_item_list:SetSelectCallBack(BindTool.Bind(self.VipToggleCallBack, self))
	self.vip_card_item_list = AsyncListView.New(VipPanelCardItemRender, self.node_list.vip_card_item_list)
	self.vip_card_item_list:SetSelectCallBack(BindTool.Bind(self.VipCardSelectCallBack, self))

	self.node_list.daily_gift_get_btn.button:AddClickListener(BindTool.Bind(self.OnClickDailyGiftBox, self))
	self.node_list.free_get_btn.button:AddClickListener(BindTool.Bind(self.OnClickDailyFreeGetBtn, self))
	self.node_list.vip_go_recharge_btn.button:AddClickListener(BindTool.Bind(self.OnClickVipGoRechargeBtn, self))
	self.node_list.vip_more_info_btn.button:AddClickListener(BindTool.Bind(self.OnClickVipDescInfoBtn, self))
	self.node_list.vip_item_click_1.button:AddClickListener(BindTool.Bind(self.OnClickVipShowItem, self, 1))
	self.node_list.vip_show_tips_tbn.button:AddClickListener(BindTool.Bind(self.OnClickVipOpenAllPrivilegeTipsBtn, self))
end

function RechargeView:DeleteVipPanel()
	self.select_card_info = nil
	self.select_vip_level = 0
	self.is_vip_show_model = nil
	self.free_get_btn_state = 0
	self.show_vip_panel_type = 0
	self.old_select_vip_level = nil
	self.tequan_item_id = nil
	if self.vip_btn_item_list then
		self.vip_btn_item_list:DeleteMe()
		self.vip_btn_item_list = nil
	end
	if self.vip_card_item_list then
		self.vip_card_item_list:DeleteMe()
		self.vip_card_item_list = nil
	end

	if self.zhuanshu_cell_list then
		for k,v in pairs(self.zhuanshu_cell_list) do
			v:DeleteMe()
		end
		self.zhuanshu_cell_list = nil
	end

	if self.vip_tq_model then
		self.vip_tq_model:DeleteMe()
		self.vip_tq_model = nil
	end

	if self.vip_tq_show_model then
		self.vip_tq_show_model:DeleteMe()
		self.vip_tq_show_model = nil
	end

	if self.vip_daily_gift_box_tween then
		self.vip_daily_gift_box_tween:Kill()
		self.vip_daily_gift_box_tween = nil
	end

	CountDownManager.Instance:RemoveCountDown("vip_free_card")
	-- CountDownManager.Instance:RemoveCountDown("vip_remain_time")
end

function RechargeView:VipCardSelectCallBack(item, cell_index, is_default, is_click)
	local data = item:GetData()
	self.select_vip_level = data.vip_up_lv
	self.select_card_info = data

	local vip_gift_cfg = VipWGData.Instance:GetVIPGiftCfg(self.select_vip_level)
	self.is_vip_show_model = vip_gift_cfg and vip_gift_cfg.show_item_id > 0

	self:FlushFreeGetBtnState()
	self:FlushVipDescPanel()
	self:FlushVipGiftRewardPanel()
end

function RechargeView:VipToggleCallBack(item, cell_index, is_default, is_click)
	local data = item:GetData()
	self.select_vip_level = data.level
	local vip_gift_cfg = VipWGData.Instance:GetVIPGiftCfg(self.select_vip_level)
	self.is_vip_show_model = vip_gift_cfg and vip_gift_cfg.show_item_id > 0

	self:FlushFreeGetBtnState()
	self:FlushVipDescPanel()
	self:FlushVipGiftRewardPanel()

	TweenManager.Instance:ExecuteViewTween(GuideModuleName.Vip, TabIndex.recharge_vip, self.node_list)
end

-- 每日礼包宝箱点击
function RechargeView:OnClickDailyGiftBox()
	local vip_level = VipWGData.Instance:GetVipLevel()
	if vip_level < self.select_vip_level then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Vip.VipTips_4)

		local data_list =
		{
			view_type = RewardShowViewType.Normal,
			reward_item_list = self.daily_reward_list
		}
		RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
		return
	end

	if not VipWGData.Instance:CanGetDailyGiftReward() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Vip.VipTips_24)

		local data_list =
		{
			view_type = RewardShowViewType.Normal,
			reward_item_list = self.daily_reward_list
		}
		RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
		return
	end

	local vip_level = VipWGData.Instance:GetVipLevel()
	local is_gray = self.select_vip_level > vip_level
	VipWGCtrl.Instance:ShowVipDailyGift(self.daily_reward_list, is_gray, BindTool.Bind(self.OnClickDailyGiftBtn, self))

	self:OnClickDailyGiftBtn()
end

-- 领取每日礼包奖励
function RechargeView:OnClickDailyGiftBtn()
	local vip_level = VipWGData.Instance:GetVipLevel()
	if vip_level < self.select_vip_level or not VipWGData.Instance:CanGetDailyGiftReward() then
		return
	end

	self.show_daily_gift_mark = true

	VipWGCtrl.Instance:SendBuyVipTimeCard(BUY_VIPTIME_CARD_TYPE.OP_TYPE_FETCH_DAILY_REWARD)
end

-- 购买VIP专属礼包
function RechargeView:OnClickZhuanShuGiftBuy()
	if not VipWGData.Instance:IsVip() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Vip.VipTips_3)
		return
	end
    local vip_level = VipWGData.Instance:GetVipLevel()
    local select_vip_level = self.select_vip_level
    if select_vip_level > vip_level then
    	SysMsgWGCtrl.Instance:ErrorRemind(Language.Vip.VipTips_4)
    	return
    end
    local vip_gift_cfg = VipWGData.Instance:GetVIPGiftCfg(select_vip_level)
    if IsEmptyTable(vip_gift_cfg) then
    	print("选择的vip等级 = ",select_vip_level)
    	return
    end
    local gold = RoleWGData.Instance:GetAttr('gold')
    if gold < vip_gift_cfg.curr_cost then
		VipWGCtrl.Instance:OpenTipNoGold()
    	return
    end
	if vip_gift_cfg.curr_cost >= COMMON_CONSTS.AlertConst then
		TipWGCtrl.Instance:OpenAlertTips(string.format(Language.Common.CommonAlertFormat2, vip_gift_cfg.curr_cost, select_vip_level), function ()
			VipWGCtrl.Instance:CSBuyVipGiftBag(select_vip_level)
		end)
	else
		VipWGCtrl.Instance:CSBuyVipGiftBag(select_vip_level)
	end
end

-- 领取免费卡 or 买付费卡 or 买专属礼包
function RechargeView:OnClickDailyFreeGetBtn()
	if self.free_get_btn_state == free_btn_state.not_enough then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Vip.VipTips_4)
		return
	elseif self.free_get_btn_state == free_btn_state.pass_day then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Vip.VipTips_4)
		return
	elseif self.free_get_btn_state == free_btn_state.get_reward then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Vip.VipTips_24)
		return
	end

	if self.free_get_btn_state == free_btn_state.free_card then
		if self.select_card_info then
	    	VipWGCtrl.Instance:SendBuyVipTimeCard(BUY_VIPTIME_CARD_TYPE.OP_TYPE_ACTIVE_VIP_CARD, self.select_card_info.seq)
	    end
	elseif self.free_get_btn_state == free_btn_state.buy_card then
		if self.select_card_info then
			VipWGCtrl.Instance:BuyAndUseVipCard(self.select_card_info.seq)
		end
	elseif self.free_get_btn_state == free_btn_state.not_time then
		local card_cfg = self.select_card_info
		if card_cfg.active_condition == VipWGData.VipCardActiveType.online then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Vip.VipTips_6)
		elseif card_cfg.active_condition == VipWGData.VipCardActiveType.day then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Vip.VipTips_7)
		end
	elseif self.free_get_btn_state == free_btn_state.zero_buy then
		RechargeWGCtrl.Instance:Open(TabIndex.recharge_zerobuy)
	elseif self.free_get_btn_state == free_btn_state.daily_reward then
		VipWGCtrl.Instance:SendBuyVipTimeCard(BUY_VIPTIME_CARD_TYPE.OP_TYPE_FETCH_DAILY_REWARD)
	elseif self.free_get_btn_state == free_btn_state.buy_zs_gift then
		self:OnClickZhuanShuGiftBuy()
	end
end

-- 跳转界面.
function RechargeView:OnClickVipGoRechargeBtn()
	-- RechargeWGCtrl.Instance:Open(TabIndex.recharge_cz)
	ViewManager.Instance:Open(GuideModuleName.Market, TabIndex.shop_limit)
end

-- 打开特权总览界面.
function RechargeView:OnClickVipOpenAllPrivilegeTipsBtn()
	RechargeWGCtrl.Instance:OpenAllPrivilegeTips()
end

-- VIP特权
function RechargeView:OnClickVipDescInfoBtn()
	VipWGCtrl.Instance:OpenVipDescInfoView(self.select_vip_level)
end

function RechargeView:OnClickVipShowItem(index)
	if index == 1 then
		local item_id = self.tequan_item_id and self.tequan_item_id[1]
		if item_id then
			TipWGCtrl.Instance:OpenItem({item_id = item_id})
		end
	elseif index == 2 then
		local tujian_seq = VipWGData.Instance:GetVIPZeroBuyCfg("show_item_id") or 0
		local tujian_cfg = ShanHaiJingWGData.Instance:GetTJCfgBySeq(tujian_seq)
		if tujian_cfg then
			TipWGCtrl.Instance:OpenItem({item_id = tujian_cfg.active_item_id})
		end
	end
end

function RechargeView:FlushVipPanel(param_t)
	-- local is_buy_vip = VipWGData.Instance:IsBuyVip()
	-- local vip_level = VipWGData.Instance:GetVipLevel()
	-- local free_level = VipWGData.Instance:GetFreeCradMaxVipLevel()
	-- local uplevel_type = VipWGData.Instance:GetVipOtherInfo("uplevel_type")
	local is_gold_card = true
	-- if uplevel_type == 0 then
	-- 	is_gold_card = is_buy_vip or vip_level >= free_level
	-- end

	if is_gold_card then
		self:FlushVipBtnItemList(param_t)
		self:FlushBannerPanel_2()
	else
		self:FlushVipCardItemList(param_t)
	end

	---[[ 领取每日礼包后弹下奖励展示
	if param_t and param_t.daily_gift_info then
		local can_get = VipWGData.Instance:CanGetDailyGiftReward()
		if self.show_daily_gift_mark and not can_get then
			self:OnClickDailyGiftBtn()
		end
	end
	--]]

	self.node_list.vip_card_item_list:SetActive(not is_gold_card)
	self.node_list.banner_panel_2:SetActive(is_gold_card)
	self.node_list.vip_btn_list:SetActive(is_gold_card)

	self:FlushFreeGetBtnState()
	self:FlushVipGiftRewardPanel()
end

function RechargeView:FlushVipBtnItemList(param_t)
	local select_level = nil
	local need_jump = false
	if param_t then
		for k,v in pairs(param_t) do
			if k == "vip_level" then
				select_level = v.vip_level
			elseif k == "all" then
				for t,q in pairs(v) do
					if t == "to_ui_param" and q ~= 0 then
						select_level = tonumber(q)
						need_jump = true
						break
					end
				end
			end
		end
	end
	local gift_cfg_list = VipWGData.Instance:GetAllVipGiftCfg()
	self.vip_btn_item_list:SetDataList(gift_cfg_list)
	if self.show_vip_panel_type ~= vip_panel_mark.gold_card then
		select_level = select_level or VipWGData.Instance:GetVipLevel()
		select_level = math.max(select_level,1)
		self.vip_btn_item_list:JumpToIndex(select_level)
		self.show_vip_panel_type = vip_panel_mark.gold_card
	elseif need_jump and self.vip_btn_item_list then
		self.vip_btn_item_list:JumpToIndex(select_level)
	end
end

function RechargeView:FlushVipCardItemList(param_t)
	local select_seq = nil
	local select_level = nil
	if param_t then
		for k,v in pairs(param_t) do
			if k == "vip_level" then
				select_level = v.vip_level
			elseif k == "all" then
				for t,q in pairs(v) do
					if t == "to_ui_param" and q ~= 0 then
						select_level = q
						break
					end
				end
			elseif k == "card_seq" then
				select_seq = v.card_seq
			end
		end
	end

	local card_cfg_list = VipWGData.Instance:GetVipCardCfg()
	card_cfg_list = SortTableKey(card_cfg_list, true)
	self.vip_card_item_list:SetDataList(card_cfg_list)

	if self.show_vip_panel_type ~= vip_panel_mark.free_card or select_seq or select_level then
		if not select_seq and not select_level then
			select_seq = self:GetHasRemindCardSeq()
		end
		local index = 1
		for i=1,#card_cfg_list do
			if card_cfg_list[i].seq == select_seq or card_cfg_list[i].vip_up_lv == select_level then
				index = i
				break
			end
		end
		self.vip_card_item_list:SelectIndex(index)
		-- self.vip_card_item_list:JumpToIndex(index)
		self.show_vip_panel_type = vip_panel_mark.free_card
	end
end

function RechargeView:GetHasRemindCardSeq()
	local card_seq = 0
	if VipWGData.Instance:CanGetDailyGiftReward() then
		card_seq = VipWGData.Instance:GetLastCardSeq()
	else
		local mark,seq = VipWGData.Instance:CanGetFreeCard()
		card_seq = mark and seq or 0
	end
	return card_seq
end

function RechargeView:FlushVipDescPanel()
	local vip_level = self.select_vip_level
	self.node_list.special_tequan:SetActive(self.is_vip_show_model)
	local desc_1 = RechargeWGData.Instance:GetRechargeVipDesc(vip_level)
	self.node_list.desc_text_1.text.text = desc_1
	self:FlushTequanModel()
	--self:FlushTequanTujian()
	self.old_select_vip_level = self.select_vip_level
end

function RechargeView:FlushTequanModel()
	if not self.is_vip_show_model or self.select_vip_level == self.old_select_vip_level then
		return
	end

	local vip_gift_cfg = VipWGData.Instance:GetVIPGiftCfg(self.select_vip_level)
	if IsEmptyTable(vip_gift_cfg) or vip_gift_cfg.show_item_id == 0 then
		return
	end

	self.tequan_item_id[1] = vip_gift_cfg.show_item_id

	self.node_list.vip_tq_show_model_root:SetActive(vip_gift_cfg.show_model == VIP_MODEL_PART_TYPE.HALO)
	self.node_list.tequan_model_root_1:SetActive(vip_gift_cfg.show_model ~= VIP_MODEL_PART_TYPE.HALO)

	local model_root = vip_gift_cfg.show_model == VIP_MODEL_PART_TYPE.HALO
		and self.node_list.vip_tq_show_model_root or self.node_list.tequan_model_root_1

	if vip_gift_cfg.show_model == VIP_MODEL_PART_TYPE.HALO then -- 光环
		self.vip_tq_show_model:ClearLoadComplete()
		self.vip_tq_show_model:RemoveMain()
		self.vip_tq_show_model:RemoveWeapon()
		self.vip_tq_show_model:RemoveHalo()
		local role_res_id, weapon_res_id = AppearanceWGData.Instance:GetRoleResId()
		local extra_role_model_data = {
			weapon_res_id = weapon_res_id,
		}
		self.vip_tq_show_model:SetRoleResid(role_res_id, nil, extra_role_model_data)
		self.vip_tq_show_model:SetWeaponResid(weapon_res_id)
		self.vip_tq_show_model:SetHaloResid(vip_gift_cfg.resouce)
	else
		local info = {}
		info.should_ani = false
		info.need_wp_tween = true
		info.bundle_name = nil
		info.asset_name = nil
		info.item_id = vip_gift_cfg.show_item_id
		info.render_type = OARenderType.RoleModel
		info.model_rt_type = ModelRTSCaleType.L
		self.vip_tq_model:SetData(info)

		local item_cfg = ItemWGData.Instance:GetItemConfig(vip_gift_cfg.show_item_id)
		if item_cfg then
			if item_cfg.is_display_role == DisplayItemTip.Display_type.WUQI then
				local res_id, part_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
				if res_id and attr_cfg then
					self.vip_tq_model:DisplayRemoveAllModel()
					local weapon_res_id = AppearanceWGData.GetFashionWeaponIdByResViewId(attr_cfg.resouce)
					self.vip_tq_model:ShowCurRoleModel(nil, weapon_res_id, true, true)
				end
			end
		end
	end

	local scale = vip_gift_cfg.display_scale or 1
	Transform.SetLocalScaleXYZ(model_root.transform, scale, scale, scale)

	local pos_x, pos_y = 0, 0
	if vip_gift_cfg.display_pos and vip_gift_cfg.display_pos ~= "" then
		local pos_list = string.split(vip_gift_cfg.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end
	RectTransform.SetAnchoredPositionXY(model_root.rect, pos_x, pos_y)

	if vip_gift_cfg.rotation and vip_gift_cfg.rotation ~= "" then
		local rotation_tab = string.split(vip_gift_cfg.rotation, "|")
		model_root.transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2],
			rotation_tab[3])
	end

	--设置战力显示
	if vip_gift_cfg.show_item_id then
		local is_show_max = vip_gift_cfg.show_model ~= VIP_MODEL_PART_TYPE.FASHION
		local zhanli_num = ItemShowWGData.CalculateCapability(vip_gift_cfg.show_item_id, is_show_max)
		self.node_list.vip_zhanli_label.text.text = zhanli_num
	end

	-- --第二个节点显示控制
	-- self.node_list["vip_tuquan_item"]:SetActive(self.select_vip_level == special_tequan_level)
	-- self.node_list["vip_tuquan_item_2"]:SetActive(self.select_vip_level ~= special_tequan_level)
end

function RechargeView:PlayTequanModelAction()
	if self.vip_tq_model then
		self.vip_tq_model:ActModelPlayLastAction()
	end

	if self.vip_tq_show_model then
		self.vip_tq_show_model:PlayLastAction()
	end
end

function RechargeView:GetTequanModelResByType(model_type, item_id)
	if not model_type or not item_id then
		return nil
	end
	local res_id, part_type, image_cfg
	if model_type == VIP_MODEL_PART_TYPE.MOUNT or model_type == VIP_MODEL_PART_TYPE.LING_CHONG then
		res_id = NewAppearanceWGData.Instance:GetQiChongResIdAndActAttrCfg(item_id)
	elseif model_type == VIP_MODEL_PART_TYPE.HUA_KUN then
		local kun_base_cfg = NewAppearanceWGData.Instance:GetKunActCfgByItemId(item_id)
		if kun_base_cfg then
			res_id = kun_base_cfg.active_id
		end
	elseif model_type == VIP_MODEL_PART_TYPE.FASHION then
		res_id, part_type = NewAppearanceWGData.Instance:GetFashionResByItemId(item_id)
	elseif model_type == VIP_MODEL_PART_TYPE.XIAN_WA then
		image_cfg = MarryWGData.Instance:GetBabyActiveCfgByItemId(item_id)
		if image_cfg then
			res_id = image_cfg.appe_image_id
		end
	end

	return res_id, part_type
end

-- function RechargeView:FlushTequanTujian()
-- 	if self.select_vip_level ~= special_tequan_level then--V6才有图鉴显示
-- 		return
-- 	end
-- 	local tujian_seq = VipWGData.Instance:GetVIPZeroBuyCfg("show_item_id") or 0
-- 	local tujian_cfg = ShanHaiJingWGData.Instance:GetTJCfgBySeq(tujian_seq)
-- 	local stuff_item_cfg = tujian_cfg and ItemWGData.Instance:GetItemConfig(tujian_cfg.active_item_id)
-- 	if tujian_cfg and stuff_item_cfg then
-- 		self.node_list.vip_tujian_name.text.text = stuff_item_cfg.name

-- 		local bundle, asset = ResPath.GetF2RawImagesPNG("img_card_bg_" .. tujian_cfg.card_ID)
-- 		self.node_list.vip_tujian_icon.raw_image:LoadSprite(bundle, asset, function ()
-- 			self.node_list.vip_tujian_icon.raw_image:SetNativeSize()
-- 		end)

-- 		bundle, asset = ResPath.GetF2CommonImages("kuang_shj_" .. stuff_item_cfg.color)
-- 		self.node_list.vip_tujian_kuang.image:LoadSprite(bundle, asset)

-- 		bundle, asset = ResPath.GetEffectUi(EF_TYPE[tujian_cfg.color])
-- 		self.node_list.vip_tujian_effect:ChangeAsset(bundle, asset)

-- 		--设置战力显示
-- 		self.node_list["vip_zhanli_label_2"].text.text = ItemShowWGData.CalculateCapability(tujian_cfg.active_item_id)
-- 	end
-- end

function RechargeView:FlushVipGiftRewardPanel()
	local select_vip_level = self.select_vip_level
	if select_vip_level > 0 then
		local vip_level = VipWGData.Instance:GetVipLevel()
		local vip_gift_cfg = VipWGData.Instance:GetVIPGiftCfg(select_vip_level)
		local zhuanshu_reward_list = vip_gift_cfg.reward_item
		local daily_reward_list = vip_gift_cfg.daily_reward_item
		local show_value = vip_gift_cfg.show_value or 0

		if vip_level > select_vip_level then
			local gift_cfg = VipWGData.Instance:GetVIPGiftCfg(vip_level)
			daily_reward_list = gift_cfg.daily_reward_item
			show_value = gift_cfg.show_value or 0
		end

		self.daily_reward_list = SortTableKey(daily_reward_list)
		self.node_list.renwu_img:SetActive(not self.is_vip_show_model)
		local reward_item_list = {}
		for k,v in pairs(zhuanshu_reward_list) do
			table.insert(reward_item_list, v)
		end
		local tab = #zhuanshu_reward_list
		self.node_list["zhuanshu_reward_5"]:SetActive(tab > 3)
		self.node_list["zhuanshu_reward_6"]:SetActive(tab > 3)
		for i = 1, 6 do
			if self.zhuanshu_cell_list[i] == nil then
				self.zhuanshu_cell_list[i] = ItemCell.New(self.node_list["zhuanshu_reward_".. i])
			end

			if reward_item_list[i] then
				self.zhuanshu_cell_list[i]:SetData(reward_item_list[i])
				self.node_list["zhuanshu_reward_".. i]:SetActive(true)
			else
				self.node_list["zhuanshu_reward_".. i]:SetActive(false)
			end

			self.zhuanshu_cell_list[i]:SetLingQuVisible(self.free_get_btn_state == free_btn_state.get_reward)
		end
		
		local vip_num = select_vip_level <= vip_level and vip_level or select_vip_level
		--self.node_list.dailiy_gift_level.text.text = string.format(Language.Vip.Gift_Level, vip_num)
		self.node_list.daily_gift_value.text.text = show_value

		local can_get = VipWGData.Instance:CanGetDailyGiftReward()
		local is_remind = select_vip_level <= vip_level and can_get
		self.node_list.get_reward_img:SetActive(select_vip_level <= vip_level and not can_get)
		self.node_list.daily_gift_red_point:SetActive(is_remind)
		-- self.node_list.daily_gift_get_btn:SetActive(can_get or select_vip_level > vip_level)
		-- XUI.SetGraphicGrey(self.node_list.daily_gift_get_btn, select_vip_level > vip_level)

		if is_remind then
			if self.vip_daily_gift_box_tween then
				self.vip_daily_gift_box_tween:Restart()
			else
				if self.vip_daily_gift_box_tween then
					self.vip_daily_gift_box_tween:Kill()
					self.vip_daily_gift_box_tween = nil
				end

				self.vip_daily_gift_box_tween = DG.Tweening.DOTween.Sequence()
				UITween.ShakeAnimi(self.node_list.daily_gift_box.transform, self.vip_daily_gift_box_tween)
			end
		elseif self.vip_daily_gift_box_tween then
			self.vip_daily_gift_box_tween:Pause()
			self.node_list.daily_gift_box.transform.localRotation = Quaternion.identity
		end

		self:FlushVipZhuanShuGiftReward(select_vip_level, vip_gift_cfg)
	end
end

function RechargeView:FlushVipZhuanShuGiftReward(select_vip_level, vip_gift_cfg)
	self.node_list.title_text_1.text.text = string.format(Language.Vip.VIP_Libao, select_vip_level, Language.Vip.VIP_LibaoTitle[1])
	self.node_list.title_text_2.text.text = string.format(Language.Vip.VIP_Libao, select_vip_level, Language.Vip.VIP_LibaoTitle[2])
	self.node_list.zhuanshu_pre_price.text.text = vip_gift_cfg.orignal_cost or 0
	self.node_list.vip_lv_name.text.text = vip_gift_cfg.vip_title

end

function RechargeView:FlushBannerPanel_2()
	local vip_level = VipWGData.Instance:GetVipLevel()
	local max_level = VipWGData.Instance:GetMaxVIPLevel()
	local prog, full = VipWGData.Instance:GetRechargeCondition()
	local progress = 0
	if vip_level < max_level then
		progress = prog / full
	else
		progress = 1
	end
	self.node_list.banner_vip_num.text.text = vip_level
	self.node_list.vip_pregress_bg.slider.value = progress
	self.node_list.vip_pregress_label.text.text = string.format("%d/%d", prog, full)

	local uplevel_type = VipWGData.Instance:GetVipOtherInfo("uplevel_type")
	local show_beishu = 1
	local vip_str = ""
	if uplevel_type == 0 then
		vip_str = VipWGData.Instance:GetVipOtherInfo("consume_gold_to_vip_exp")
	else
		local vip_const = VipWGData.Instance:GetVipOtherInfo("chongzhi_gold_to_vip_exp")
		vip_const = vip_const / RECHARGE_BILI * show_beishu
		vip_str = RoleWGData.GetPayMoneyStr(vip_const)
	end

	local vip_daily_exp_lv = VipWGData.Instance:GetVipOtherInfo("vip_daily_exp_lv")
	local day_reward_vip_exp = VipWGData.Instance:GetVipOtherInfo("day_reward_vip_exp")
	local const_str = string.format(Language.Vip.VipTips_1[uplevel_type],
		vip_str, show_beishu, vip_daily_exp_lv, day_reward_vip_exp)
	self.node_list.vip_const_label.text.text = const_str
end

function RechargeView:FlushFreeGetBtnState()
	CountDownManager.Instance:RemoveCountDown("vip_free_card")
	if self.show_vip_panel_type == vip_panel_mark.gold_card then
		self:FlushRealVip()
	else
		self:FlushTempVip()
	end
end

function RechargeView:FlushRealVip()
	self.free_get_btn_state = free_btn_state.None
	local is_vip = VipWGData.Instance:IsVip()
	local vip_level = VipWGData.Instance:GetVipLevel()
	local select_vip_level = self.select_vip_level
	local is_buy_flag = VipWGData.Instance:GetVipBuyFlag(select_vip_level)
	if vip_level < select_vip_level then
		self.free_get_btn_state = free_btn_state.not_enough
	-- elseif VipWGData.Instance:CanGetDailyGiftReward() then
		-- self.free_get_btn_state = free_btn_state.daily_reward
	elseif not is_buy_flag then
		self.free_get_btn_state = free_btn_state.buy_zs_gift
	elseif is_vip then
		self.free_get_btn_state = free_btn_state.get_reward
	else
		self.free_get_btn_state = free_btn_state.pass_day
	end
	self:FlushFreeGetBtnShow()
end

function RechargeView:FlushTempVip()
	local card_cfg = self.select_card_info
	if not card_cfg then
		return
	end
	self.free_get_btn_state = free_btn_state.None

	local is_get_card = VipWGData.Instance:IsGetThisCardBySeq(card_cfg.seq)
	local last_card_seq = VipWGData.Instance:GetLastCardSeq()
	local is_vip = VipWGData.Instance:IsVip()

	if is_get_card then
		self.free_get_btn_state = free_btn_state.get_reward
	end
	if is_vip and last_card_seq >= card_cfg.seq then
		-- if VipWGData.Instance:CanGetDailyGiftReward() then
		-- 	self.free_get_btn_state = free_btn_state.daily_reward
		-- else
		-- 	self.free_get_btn_state = free_btn_state.get_reward
		-- end
		local is_buy_flag = VipWGData.Instance:GetVipBuyFlag(self.select_vip_level)
		if not is_buy_flag then
			self.free_get_btn_state = free_btn_state.buy_zs_gift
		end
	elseif card_cfg.active_condition == VipWGData.VipCardActiveType.online and not is_get_card then
		self.free_get_btn_state = free_btn_state.free_card
		local online_time_s = TimeWGCtrl.Instance:GetOnlineTimes()
		local time = card_cfg.active_value * 60 - online_time_s
		if time > 0 then
			self.free_get_btn_state = free_btn_state.not_time
			self:StarCountDownTime(time)
		end
	elseif card_cfg.active_condition == VipWGData.VipCardActiveType.day and not is_get_card then
		self.free_get_btn_state = free_btn_state.free_card
		local login_day_count = ServerActivityWGData.Instance:GetTotalLoginDays()
		if login_day_count < card_cfg.active_value then
			self.free_get_btn_state = free_btn_state.not_time
			self.node_list.free_time_label.text.text = string.format(Language.Vip.VipTips_5, card_cfg.active_value - login_day_count)
		end
	elseif card_cfg.active_condition == VipWGData.VipCardActiveType.glod then
		if card_cfg.vip_up_lv == special_tequan_level and RechargeWGData.Instance:HasVipZeroBuy() then -- V6卡改成跳0元购
			self.free_get_btn_state = free_btn_state.zero_buy
		else
			self.free_get_btn_state = free_btn_state.buy_card
		end
	end
	self:FlushFreeGetBtnShow()
end

function RechargeView:FlushFreeGetBtnShow()
	local card_cfg = self.select_card_info
	local vip_gift_cfg = VipWGData.Instance:GetVIPGiftCfg(self.select_vip_level)
	if not vip_gift_cfg then
		return
	end

	local btn_str = ""
	local free_get_btn_state = self.free_get_btn_state
	if free_get_btn_state == free_btn_state.not_enough then
		btn_str = vip_gift_cfg.curr_cost or 0
	elseif free_get_btn_state == free_btn_state.pass_day then
		btn_str = Language.Vip.VipTips_13
    elseif free_get_btn_state == free_btn_state.daily_reward then
		btn_str = Language.Vip.VipTips_18
	elseif free_get_btn_state == free_btn_state.free_card or free_get_btn_state == free_btn_state.not_time then
		btn_str = Language.Vip.VipTips_19
	elseif free_get_btn_state == free_btn_state.buy_card then
		local shop_cfg = ShopWGData.Instance:GetShopCfgItemId(card_cfg.active_value)
		self.node_list.buy_card_label.text.text = shop_cfg and shop_cfg.price or ""
		btn_str = Language.Vip.VipTips_20
	elseif free_get_btn_state == free_btn_state.zero_buy then
		local shop_cfg = ShopWGData.Instance:GetShopCfgItemId(card_cfg.active_value)
		self.node_list.zero_price_label.text.text = shop_cfg and shop_cfg.price or ""
		btn_str = Language.Vip.VipTips_21
	elseif free_get_btn_state == free_btn_state.buy_zs_gift then
		btn_str = vip_gift_cfg.curr_cost or 0--Language.Vip.VipTips_23
	elseif free_get_btn_state == free_btn_state.get_reward then
		btn_str = Language.Vip.VipTips_25
	end

	self.node_list.free_get_btn_label_1.text.text = btn_str
	self.node_list.free_get_btn_label_2.text.text = btn_str
	self.node_list.free_get_btn_label_1:SetActive(free_get_btn_state == free_btn_state.buy_zs_gift or free_get_btn_state == free_btn_state.not_enough)
	self.node_list.free_get_btn_label_2:SetActive(not (free_get_btn_state == free_btn_state.buy_zs_gift or free_get_btn_state == free_btn_state.not_enough))
	self.node_list.free_time_label:SetActive(free_get_btn_state == free_btn_state.not_time)
	self.node_list.free_get_btn_remind:SetActive(free_get_btn_state == free_btn_state.daily_reward or free_get_btn_state == free_btn_state.free_card)
	self.node_list.pre_price:SetActive(not (free_get_btn_state == free_btn_state.free_card or free_get_btn_state == free_btn_state.not_time or 
		free_get_btn_state == free_btn_state.daily_reward or free_get_btn_state == free_btn_state.zero_buy or free_get_btn_state ==  free_btn_state.buy_card))
	self.node_list.free_time_enough:SetActive(free_get_btn_state == free_btn_state.daily_reward or free_get_btn_state == free_btn_state.free_card)
	self.node_list.zero_price:SetActive(free_get_btn_state == free_btn_state.zero_buy)
	self.node_list.buy_card_price:SetActive(free_get_btn_state ==  free_btn_state.buy_card)

	self.node_list.price_bg:SetActive(not (free_get_btn_state == free_btn_state.get_reward))
	self.node_list.free_get_btn:SetActive(not (free_get_btn_state == free_btn_state.get_reward))
	self.node_list.gift_buy_img:SetActive(free_get_btn_state == free_btn_state.get_reward)
end

function RechargeView:StarCountDownTime(total_time)
	self.node_list.free_time_label.text.text = TimeUtil.FormatSecondDHM6(total_time)
	CountDownManager.Instance:AddCountDown("vip_free_card", function (time, total_time)
		self.node_list.free_time_label.text.text = TimeUtil.FormatSecondDHM6(total_time - time)
	end, function ()
		self:FlushVipPanel()
	end, nil, total_time + 1, 1)
end


--------------------------------------------------VipPanelCardItemRender--------------------------------------------------------------------------------
VipPanelCardItemRender = VipPanelCardItemRender or BaseClass(BaseRender)

function VipPanelCardItemRender:__init()
	self.select_mark = nil
end

function VipPanelCardItemRender:__delete()
	self.select_mark = nil
end

function VipPanelCardItemRender:LoadCallBack()

end

function VipPanelCardItemRender:OnSelectChange(is_select)
	if self.select_mark ~= is_select then
		self.node_list.select_bg:SetActive(is_select)
		--self.node_list.normal_bg:SetActive(not is_select)
		self.select_mark = is_select
	end
end

function VipPanelCardItemRender:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
		return
	end
	self.node_list.name_label.text.text = data.name
	self.node_list.name_label_spr.text.text = data.name
	self.node_list.name_label:SetActive(not (data.vip_up_lv == special_tequan_level))
	self.node_list.name_label_spr:SetActive(data.vip_up_lv == special_tequan_level)
	local is_get_card = VipWGData.Instance:IsGetThisCardBySeq(data.seq)
	local uplevel = data.vip_up_lv or 0
	local desc = ""
	if data.active_condition == VipWGData.VipCardActiveType.online then
		local mark = VipWGData.Instance:CanGetFreeCard(self.data.seq)
		desc = mark and Language.Vip.VipTips_15 or string.format(Language.Vip.VipTips_16, data.active_value, uplevel)
	elseif data.active_condition == VipWGData.VipCardActiveType.day then
		local mark = VipWGData.Instance:CanGetFreeCard(self.data.seq)
		desc = mark and Language.Vip.VipTips_15 or string.format(Language.Vip.VipTips_17, uplevel)
	elseif data.active_condition == VipWGData.VipCardActiveType.glod then
		local shop_cfg = ShopWGData.Instance:GetShopCfgItemId(data.active_value)
		self.node_list.price_label.text.text = shop_cfg and shop_cfg.price or ""
		self.node_list.price_label_spr.text.text = shop_cfg and shop_cfg.price or ""
	end
	self.node_list.bottom_desc.text.text = desc
	self.node_list.price_icon:SetActive(data.active_condition == VipWGData.VipCardActiveType.glod and (not (data.vip_up_lv == special_tequan_level)))
	self.node_list.price_icon_forever:SetActive(data.active_condition == VipWGData.VipCardActiveType.glod and (data.vip_up_lv == special_tequan_level))
	self.node_list.forever_img:SetActive(data.vip_up_lv == special_tequan_level)
	self:FlushRemind()
	local bundle, asset
	if data.vip_up_lv == special_tequan_level then
		bundle, asset = ResPath.GetVipImage("a1_zsk_jing")
	else
		bundle, asset = ResPath.GetVipImage("a1_zsk_jing")
	end
    self.node_list.normal_bg.image:LoadSprite(bundle, asset, function()
        self.node_list.normal_bg.image:SetNativeSize()
    end)
end

function VipPanelCardItemRender:FlushRemind()
	if self.node_list.remind_img then
		local mark = VipWGData.Instance:CanGetFreeCard(self.data.seq)
		if not mark then
			if VipWGData.Instance:GetLastCardSeq() == self.data.seq and VipWGData.Instance:CanGetDailyGiftReward() then
				mark = true
			end
		end
		self.node_list.remind_img:SetActive(mark)
	end
end

--------------------------------------------------VipPanelBtnItemRender--------------------------------------------------------------------------------
VipPanelBtnItemRender = VipPanelBtnItemRender or BaseClass(BaseRender)

function VipPanelBtnItemRender:__init()
	self.select_mark = nil
end

function VipPanelBtnItemRender:__delete()
	self.select_mark = nil
end

function VipPanelBtnItemRender:OnSelectChange(is_select)
	if self.select_mark ~= is_select then
		self.node_list.select_bg:SetActive(is_select)
		self.node_list.viph_image:SetActive(is_select)
		self.node_list.vip_image:SetActive(not is_select)
		self.node_list.normal_bg:SetActive(not is_select)
		self.select_mark = is_select
	end
end

function VipPanelBtnItemRender:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
		return
	end
	self.node_list.viph_image.text.text = string.format(Language.Vip.NameList, data.level)
	self.node_list.vip_image.text.text = string.format(Language.Vip.NameList, data.level)

	self.node_list.reward:SetActive(data.show_icon > 0)
	if data.show_icon > 0 then
		local bundle, asset = ResPath.GetItem(data.show_icon)
		self.node_list.icon.image:LoadSprite(bundle, asset, function()
			self.node_list.icon.image:SetNativeSize()
		end)
	end

	local vip_level = VipWGData.Instance:GetVipLevel()
	local can_get = VipWGData.Instance:CanGetDailyGiftReward()
	local is_remind = data.level == vip_level and can_get
	self.node_list.remind_img:SetActive(is_remind)
end
