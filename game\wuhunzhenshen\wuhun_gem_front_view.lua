WuHunGemFrontView = WuHunGemFrontView or BaseClass(SafeBaseView)

--进度条的进度值
local diezhen_propgress_value =
{
	0,
	0.06,
	0.17,
	0.32,
	0.5,
	0.67,
	0.83,
	0.935,
	1
}

local show_type =
{
	keyin = 1,
	diezhen = 2
}

local auto_strenge_time = 0.2

function WuHunGemFrontView:__init()
	
	self:SetMaskBg(false)
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true

	local bundle_name = "uis/view/wuhunzhenshen_prefab"
	local common_bundle_name = "uis/view/common_panel_prefab"
	self:AddViewResource(0, common_bundle_name, "layout_a2_common_panel")
	self:AddViewResource(0, bundle_name, "layout_wuhun_gem_front")
	self:AddViewResource(0, common_bundle_name, "layout_a2_common_top_panel")

	self.show_toggle_index = show_type.keyin --显示类型  1刻印  2叠阵
	self.gem_item_tab = {}                --6个宝石render
	self.left_wuhun_list_index = nil      --左边列表的武魂索引
	self.gem_index = 0                    --当前魂石的索引
	self.is_auto_grade = nil              --是否自动升品
	self.diezhen_timer_quest = nil
end

function WuHunGemFrontView:ReleaseCallBack()
	self.show_toggle_index = nil
	self.gem_item_tab = nil
	self.left_wuhun_list_index = nil
	self.hunzhen_index = nil
	self.gem_index = nil
	self.slt_diezhen_rateitem = nil
	self.frist_flag = nil
	self.is_auto_grade = nil

	if self.front_list_view then
		self.front_list_view:DeleteMe()
		self.front_list_view = nil
	end

	if self.hunzhen_list_view then
		self.hunzhen_list_view:DeleteMe()
		self.hunzhen_list_view = nil
	end

	if self.cambered_list then
		self.cambered_list:DeleteMe()
		self.cambered_list = nil
	end

	if self.attr_item_list then
		for key, value in pairs(self.attr_item_list) do
			value:DeleteMe()
		end
		self.attr_item_list = nil
	end

	if self.diezhen_cell1 then
		self.diezhen_cell1:DeleteMe()
	end

	if self.diezhen_cell2 then
		self.diezhen_cell2:DeleteMe()
	end

	self:CancelAutoGradeTimer()
end

function WuHunGemFrontView:LoadCallBack()
	self.show_toggle_index = show_type.keyin --显示类型  1刻印  2叠阵
	self.gem_item_tab = {}                --6个宝石render
	self.frist_flag = true                --首次打开界面
	self.is_auto_grade = false
	self.slt_diezhen_rateitem = false

	if not self.front_list_view then
		self.front_list_view = AsyncListView.New(TianShenWuHunRender, self.node_list.front_list_view)
		self.front_list_view:SetSelectCallBack(BindTool.Bind(self.OnSelectWuHunFrontCallBack, self))
	end

	if not self.hunzhen_list_view then
		self.hunzhen_list_view = AsyncListView.New(HunZhenRender, self.node_list.hunzhen_list_view)
		self.hunzhen_list_view:SetSelectCallBack(BindTool.Bind(self.OnSelectHunZhenRenderCallBack, self))
	end

	self.diezhen_cell1 = ItemCell.New(self.node_list.node_costitem1)
	self.diezhen_cell2 = ItemCell.New(self.node_list.node_costitem2)
	self.diezhen_cell2.root_node.transform:SetAsFirstSibling()

	XUI.AddClickEventListener(self.node_list.btn_keyin_onekey_equip, BindTool.Bind2(self.BtnKeyinOnekeyEquipClick, self))
	XUI.AddClickEventListener(self.node_list.btn_keyin_gongming, BindTool.Bind(self.OnFrontKeYinGongMingClick, self))
	XUI.AddClickEventListener(self.node_list.button_keyin, BindTool.Bind(self.OnFrontKeYinToggleClick, self))
	XUI.AddClickEventListener(self.node_list.button_diezhen, BindTool.Bind(self.OnFrontDieZhenToggleClick, self))
	XUI.AddClickEventListener(self.node_list.button_diezhen_add, BindTool.Bind(self.OnFrontDieZhenAddClick, self))
	XUI.AddClickEventListener(self.node_list.btn_now_upgrade, BindTool.Bind(self.OnFrontNowUpgradeClick, self))
	XUI.AddClickEventListener(self.node_list.btn_auto_upgrade, BindTool.Bind(self.OnFrontAutoUpgradeClick, self))
	XUI.AddClickEventListener(self.node_list.btn_slt_cost_item2, BindTool.Bind(self.OnFrontSltCostItemClick, self))
	XUI.AddClickEventListener(self.node_list.btn_front_gem_close, BindTool.Bind(self.OnFrontGemCloseClick, self))
	XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind(self.OnWuHunViewCloseClick, self))

	self.attr_item_list = {}
	for i = 1, 5 do
		local attr_obj = self.node_list["layout_diezhen_attr"]:FindObj(string.format("attr_%d", i))
		if attr_obj then
			local cell = CommonAddAttrRender.New(attr_obj)
			cell:SetIndex(i)
			cell:SetAttrNameNeedSpace(true)
			cell:SetRealHideNext(true)
			self.attr_item_list[i] = cell
		end
	end

	self:InitFrontGemList()
	self.node_list.title_view_name.text.text = Language.WuHunZhenShen.TitleName2
	local bundle, assert = ResPath.GetF2RawImagesPNG("a2_wh_sxd_bj")
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, assert, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)
end

function WuHunGemFrontView:OpenCallBack()
end

function WuHunGemFrontView:InitFrontGemList()
	local cambered_list_data = {
		item_render = WuHunFrontGemItem,
		asset_bundle = "uis/view/wuhunzhenshen_prefab",
		asset_name = "wuhun_front_gem_render",

		scroll_list = self.node_list.scroll_parent,
		center_x = 0,
		center_y = 0,
		radius_x = 300,
		radius_y = 120,           -- x 椭圆半长轴,y 椭圆半短轴
		angle_delta = Mathf.PI / 3,
		origin_rotation = Mathf.PI,
		is_drag_horizontal = true,
		scale_min = 0.6, -- 最小缩放比例
		need_change_scale = true,
		need_updown_click = true,
		up_drag_root = self.node_list.scroll_top_drag,
		down_drag_root = self.node_list.scroll_down_drag,
		arg_adjust = 0.9, -- 手动拖动时的速度控制
		is_assist = true,

		click_item_cb = BindTool.Bind(self.OnClickFrontGemItem, self),
		drag_to_next_cb = BindTool.Bind(self.OnDragGemToNextCallBack, self),
		drag_to_last_cb = BindTool.Bind(self.OnDragGemToLastCallBack, self),
		on_drag_end_cb = BindTool.Bind(self.OnDragGemEndCallBack, self),
		set_item_pos_cb = BindTool.Bind(self.SetGemItemPosCallBack, self),
	}

	self.cambered_list = CamberedList.New(cambered_list_data)
end

--这里设置外部选中索引值
function WuHunGemFrontView:SetFrontGemSelectIndexData(param)
	self.left_wuhun_list_index = param.cur_front_wuhun_index or 1
	self.hunzhen_index = param.cur_hunzhen_index or 0
	self.gem_index = param.cur_front_gem_index or 0
end

--打开界面的时候需要赋值已经选中的武魂
function WuHunGemFrontView:OnFlush(param_t, index)
	if param_t then
		for k_1, v_1 in pairs(param_t) do
			if k_1 == "set_data" and v_1.cur_front_wuhun_index then
				self.left_wuhun_list_index = v_1.cur_front_wuhun_index
				self.hunzhen_index = v_1.cur_hunzhen_index
				self.gem_index = v_1.cur_front_gem_index
			end
		end
	end

	if self.left_wuhun_list_index and self.hunzhen_index and self.gem_index then
		self:FlushViewInfo()
	end
end

--刷新界面界面
function WuHunGemFrontView:FlushViewInfo()
	local wuhun_list = WuHunFrontWGData.Instance:GetFrontWuHunListData(COMMON_GAME_ENUM.TWO)
	self.front_list_view:SetDataList(wuhun_list)
	self.front_list_view:SetDefaultSelectIndex(self.left_wuhun_list_index)
	self.select_front_data = wuhun_list[self.left_wuhun_list_index]

	--设置魂阵列表的显示
	self:SetFrontListShowInfo()
end

--设置魂阵列表的信息
function WuHunGemFrontView:SetFrontListShowInfo()
	local front_list = WuHunFrontWGData.Instance:GetFrontListByWuHunId(self.select_front_data.wuhun_id,
		COMMON_GAME_ENUM.TWO)
	self.hunzhen_list_view:SetDataList(front_list)

	--里面不用默认选中魂阵索引 按照w外部点击的魂阵来
	self.hunzhen_list_view:SetDefaultSelectIndex(self.hunzhen_index + 1) --列表索引是从1开始

	local front_hunshi_list = WuHunFrontWGData.Instance:GetFrontGemList(self.select_front_data.wuhun_id,
		self.hunzhen_index, COMMON_GAME_ENUM.TWO)
	self.cambered_list.angle_delta = Mathf.PI * 2 / #front_hunshi_list
	self.cambered_list:CreateCellList(#front_hunshi_list)

	--自动升阶下面需要转到对应界面
	if not self.is_auto_grade then
		--初始化重置魂石索引
		self:SetSltResetGemIndex()
	end
end

--设置页签的内容显示
function WuHunGemFrontView:SetToggleShow()
	self.node_list.tab_keyin_Image_hl:CustomSetActive(self.show_toggle_index == show_type.keyin)
	self.node_list.tab_keyin_Image_nor:CustomSetActive(self.show_toggle_index == show_type.diezhen)
	self.node_list.tab_diezhen_Image_hl:CustomSetActive(self.show_toggle_index == show_type.diezhen)
	self.node_list.tab_diezhen_Image_nor:CustomSetActive(self.show_toggle_index == show_type.keyin)
	self.node_list.node_slt_diezhen:CustomSetActive(self.show_toggle_index == show_type.diezhen)
	self.node_list.node_keyin_panel:CustomSetActive(self.show_toggle_index == show_type.keyin)
	self.node_list.node_diezhen_panel:CustomSetActive(self.show_toggle_index == show_type.diezhen)

	if self.show_toggle_index == show_type.keyin then
		self:SetKeYinInfo()
	else
		self:SetDieZhenInfo()
	end

	self.frist_flag = false
end

--设置魂石的信息显示
function WuHunGemFrontView:SetFrontGemShow()
	local wuhun_id = self.select_front_data.wuhun_id
	local gem_cfg = WuHunFrontWGData.Instance:GetFrontGemTab(wuhun_id, self.hunzhen_index, self.gem_index)
	local btn_item_list = self.cambered_list:GetRenderList()
	local front_hunshi_list = WuHunFrontWGData.Instance:GetFrontGemList(self.select_front_data.wuhun_id,
		self.hunzhen_index, COMMON_GAME_ENUM.TWO)
	for k, item_cell in ipairs(btn_item_list) do
		local gem_data = WuHunFrontWGData.Instance:GetWuHunFrontGemData(wuhun_id, self.hunzhen_index,
			front_hunshi_list[k].gem_index)
		front_hunshi_list[k].select_gem_index = self.gem_index
		front_hunshi_list[k].show_keyin = self.show_toggle_index == show_type.keyin
		front_hunshi_list[k].show_diezhen = self.show_toggle_index == show_type.diezhen
		front_hunshi_list[k].gem_level = gem_data.gem_level
		front_hunshi_list[k].gem_star = gem_data.gem_star

		item_cell:Flush("set_data", front_hunshi_list[k])
	end

	self.node_list.image_select_gemicon.image:LoadSprite(ResPath.GetWuHunZhenShenImage(gem_cfg.gem_icon))
	local active = WuHunFrontWGData.Instance:GetFrontGemIsActiveState(wuhun_id, self.hunzhen_index, self.gem_index)
	XUI.SetGraphicGrey(self.node_list.image_select_gemicon.image, not active)

	local keyin_red = WuHunFrontWGData.Instance:GetWuHunFrontGemKeYinRed(wuhun_id, self.hunzhen_index, self.gem_index)
	self.node_list.image_keyin_red:CustomSetActive(keyin_red)
	local diezhen_red = WuHunFrontWGData.Instance:GetWuHunFrontGemDieZhenRed(wuhun_id, self.hunzhen_index, self
	.gem_index)
	self.node_list.image_diezhen_red:CustomSetActive(diezhen_red)

	self:SetToggleShow()
end

--点击刻印页签
function WuHunGemFrontView:OnFrontKeYinToggleClick()
	if not self.frist_flag and self.show_toggle_index == show_type.keyin then
		return
	end
	self.show_toggle_index = show_type.keyin

	self:SetFrontGemShow()
end

--点击叠阵页签
function WuHunGemFrontView:OnFrontDieZhenToggleClick()
	if not self.frist_flag and self.show_toggle_index == show_type.diezhen then
		return
	end
	self.show_toggle_index = show_type.diezhen

	self:SetFrontGemShow()
end

--选中武魂的回调
function WuHunGemFrontView:OnSelectWuHunFrontCallBack(item, cell_index, is_default, is_click)
	if nil == item or nil == item.data then
		return
	end

	local data = item.data
	if self.left_wuhun_list_index == item.index then
		return
	end

	self.is_auto_grade = false
	self.left_wuhun_list_index = item.index
	self.select_front_data = data
	self:FlushViewInfo()
end

--点击了魂阵页签
function WuHunGemFrontView:OnSelectHunZhenRenderCallBack(item, cell_index, is_default, is_click)
	if self.hunzhen_index == cell_index - 1 then
		return
	end

	self.is_auto_grade = false
	self.hunzhen_index = cell_index - 1

	if is_click then
		self:SetSltResetGemIndex()
	end
end

--点击列表项
function WuHunGemFrontView:OnClickFrontGemItem(item, index, need_show_item)
	local old_index = self.gem_index
	if item then
		self.gem_index = item:GetIndex() - 1
	elseif index then
		self.gem_index = index - 1
	end

	if old_index ~= self.gem_index then
		self.is_auto_grade = false
	end

	self:OnSelectedBtnChange(function()
		if self:IsOpen() then
			self:SetFrontGemShow()
		end
	end, true)
end

--重置gem_index重置选中的魂石索引 刷新界面
function WuHunGemFrontView:SetSltResetGemIndex()
	self.gem_index = self.gem_index or 0
	local front_hunshi_list = WuHunFrontWGData.Instance:GetFrontGemList(self.select_front_data.wuhun_id,
		self.hunzhen_index, COMMON_GAME_ENUM.TWO)
	local init_index = COMMON_GAME_ENUM.FUYI
	for k_i, v_1 in ipairs(front_hunshi_list) do
		if v_1.show_red and init_index == COMMON_GAME_ENUM.FUYI then
			init_index = v_1.gem_index
		end

		if self.gem_index == v_1.gem_index and v_1.show_red then --自身有红点不改变当前的索引
			init_index = COMMON_GAME_ENUM.FUYI
			break
		end
	end

	if init_index ~= COMMON_GAME_ENUM.FUYI then
		self.gem_index = init_index
	end

	self:OnDragGemEndCallBack()
end

--拖拽到下一个
function WuHunGemFrontView:OnDragGemToNextCallBack()
	local front_list = WuHunFrontWGData.Instance:GetFrontGemList(self.select_front_data.wuhun_id, self.hunzhen_index)
	self.gem_index = self.gem_index + 1
	self.gem_index = self.gem_index + 1 > #front_list and 0 or self.gem_index
end

--拖拽到上一个
function WuHunGemFrontView:OnDragGemToLastCallBack()
	local front_list = WuHunFrontWGData.Instance:GetFrontGemList(self.select_front_data.wuhun_id, self.hunzhen_index)
	self.gem_index = self.gem_index - 1
	self.gem_index = self.gem_index < 1 and #front_list - 1 or self.gem_index
end

-- 拖拽完成回调
function WuHunGemFrontView:OnDragGemEndCallBack()
	local call_back = function()
		if self:IsOpen() then
			self:SetFrontGemShow()
		end
	end
	self.is_auto_grade = false
	self:OnSelectedBtnChange(call_back, nil, self.gem_index + 1)
end

-- 设置为之后的回调
function WuHunGemFrontView:SetGemItemPosCallBack()
end

-- 选中某一个
function WuHunGemFrontView:OnSelectedBtnChange(callback, is_click, drag_index)
	local to_index = drag_index ~= nil and drag_index or self.gem_index + 1 or 1
	self.cambered_list:ScrollToIndex(to_index, callback, is_click)
end

function WuHunGemFrontView:OnClickBtnWuHunTip()
	local rule_tip = RuleTip.Instance
	local rule_title = Language.WuHunZhenShen.TitleName2
	local rule_content = Language.WuHunZhenShen.WuhunFrontRuletips

	rule_tip:SetTitle(rule_title)
	rule_tip:SetContent(rule_content, nil, nil, nil, true)
end

function WuHunGemFrontView:OnFrontGemCloseClick()
	self:Close()
end

function WuHunGemFrontView:OnWuHunViewCloseClick()
	self:Close()

	local wuhun_view = WuHunWGCtrl.Instance.wuhun_view
	if wuhun_view and wuhun_view:IsOpen() then
		wuhun_view:Close()
	end
end

----------------------------------------------刻印 开始----------------------------------------------
--一键镶嵌
function WuHunGemFrontView:BtnKeyinOnekeyEquipClick()
	local wuhun_id = self.select_front_data.wuhun_id
	local gem_data = WuHunFrontWGData.Instance:GetWuHunFrontGemData(wuhun_id, self.hunzhen_index, self.gem_index)

	--星级大于-1  已激活
	if not gem_data.gem_star or gem_data.gem_star <= COMMON_GAME_ENUM.FUYI then
		TipWGCtrl.Instance:ShowSystemMsg(Language.WuHunZhenShen.WuhunFrontFuNengTip2)
		return
	end

	local list, has_change = WuHunFrontWGData.Instance:GetGemOneKeyXiangQian(self.select_front_data.wuhun_id,
		self.hunzhen_index, self.gem_index)
	if not has_change then
		TipWGCtrl.Instance:ShowSystemMsg(Language.WuHunZhenShen.WuhunFrontKeYinTxt3)
		return
	end

	WuHunWGCtrl.Instance:SendWuHunFrontOperate(
		WUHUN_FRONT_OPERATE_TYPE.ENGRAVE_SET,
		wuhun_id,
		self.hunzhen_index,
		self.gem_index,
		0,
		list
	)
end

--设置刻印的信息
function WuHunGemFrontView:SetKeYinInfo()
	local wuhun_id = self.select_front_data.wuhun_id
	local front_data = WuHunFrontWGData.Instance:GetFrontDataByFrontIndex(wuhun_id, self.hunzhen_index)
	local gem_list = front_data.gem_list or {}
	local gem_data = gem_list[self.gem_index] or {}
	local gem_star = gem_data.gem_star or COMMON_GAME_ENUM.FUYI
	local has_active = gem_star > COMMON_GAME_ENUM.FUYI

	for i = 1, 6 do
		if not self.gem_item_tab[i] then
			self.gem_item_tab[i] = WuHunBaoShiRender.New(self.node_list.gemstone_parent:FindObj("layout_gemstone_render" ..
			i))
		end

		local engrave_item = gem_data.engrave_list and gem_data.engrave_list[i] or 0
		local data = {}
		data.empty_type = true

		local list1 = WuHunFrontWGData.Instance:GetAllFrontGemEngrave(wuhun_id, self.hunzhen_index, self.gem_index, i)
		data.show_red = has_active and not IsEmptyTable(list1)
		data.gem_star = gem_star

		if engrave_item and engrave_item ~= 0 then
			if not data.show_red and has_active then
				local upgrade_cost = WuHunFrontWGData.Instance:ComputBaoShiUpgradePrice(engrave_item)
				data.show_red = upgrade_cost <= 0 --足够的金钱可升级
			end
			local cfg = WuHunFrontWGData.Instance:GetEngraveCfgByItemId(engrave_item)
			data.empty_type = false
			data.level = cfg.level
			data.item_id = engrave_item
			local attr_value1, nametxt1 = WuHunFrontWGData:GetAttrChangeValue(cfg.attr_id1, cfg.attr_value1)
			data.prop_txt1 = nametxt1 .. "+" .. attr_value1
			local attr_value2, nametxt2 = WuHunFrontWGData:GetAttrChangeValue(cfg.attr_id2, cfg.attr_value2)
			data.prop_txt2 = nametxt2 .. "+" .. attr_value2
		end

		local attr_show_type = math.ceil(i / 2)
		local click_callback = function()
			if not gem_data.gem_star or gem_data.gem_star == COMMON_GAME_ENUM.FUYI then
				TipWGCtrl.Instance:ShowSystemMsg(Language.WuHunZhenShen.WuhunFrontFuNengTip2)
				return
			end

			local param = {}
			param.wuhun_id = wuhun_id
			param.hunzhen_index = self.hunzhen_index
			param.front_gem_index = self.gem_index
			param.engrave_index = i
			param.engrave_list = gem_data.engrave_list or {}

			local list = WuHunFrontWGData.Instance:GetAllFrontGemEngrave(wuhun_id, self.hunzhen_index, self.gem_index, i)
			if IsEmptyTable(list) then
				if engrave_item and engrave_item ~= 0 then
					local next_stone = WuHunFrontWGData.Instance:GetEngraveLevelCfg(engrave_item)
					if not IsEmptyTable(next_stone) then
						param.old_item_id = engrave_item
						param.engrave_type = attr_show_type
						WuHunWGCtrl.Instance:OpenBaoShiUpgradeView(param)
					else
						TipWGCtrl.Instance:ShowSystemMsg(Language.WuHunZhenShen.WuhunFrontBaoshiTxt7)
					end
				else
					local baoshi_cfg = WuHunFrontWGData.Instance:GetFristBaoshiCfgByType(attr_show_type, 1)
					if baoshi_cfg then
						TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = baoshi_cfg.item_id })
					end
				end
			else
				param.list = list
				WuHunWGCtrl.Instance:OpenBaoShiSelectView(param)
			end
		end

		data.show_type = attr_show_type
		self.gem_item_tab[i]:SetClickCallback(click_callback)
		self.gem_item_tab[i]:SetData(data)
	end

	local min_grade = COMMON_GAME_ENUM.MAX
	for k_1, v_1 in pairs(gem_list) do
		if min_grade > v_1.gem_grade then
			min_grade = v_1.gem_grade
		end
	end
	local min_grade_cfg, max_cfg, next_cfg = WuHunFrontWGData.Instance:GetMaxCanGradeAttrCfg(min_grade)
	local has_max_grade = max_cfg.min_level <= min_grade

	if not has_max_grade then
		self.node_list.text_keyin_nexteffect1.text.text = string.format(Language.WuHunZhenShen.WuhunFrontKeYinTxt4,
			"ff9797", next_cfg.name)
		self.node_list.text_keyin_nexteffect2.text.text = string.format(Language.WuHunZhenShen.WuhunFrontKeYinTxt2,
			"ff9797", next_cfg.add)
	end
	self.node_list.text_keyin_noweffect1.text.text = string.format(Language.WuHunZhenShen.WuhunFrontKeYinTxt4, "9df5a7",
		min_grade_cfg.name)
	self.node_list.text_keyin_noweffect2.text.text = string.format(Language.WuHunZhenShen.WuhunFrontKeYinTxt2, "9df5a7",
		min_grade_cfg.add)
	self.node_list.node_keyin_nextlevel:CustomSetActive(not has_max_grade)

	local gongming_red = WuHunFrontWGData.Instance:GetFrontGemGongMingRed(wuhun_id, self.hunzhen_index)
	self.node_list.btn_gongming_red:CustomSetActive(gongming_red)

	local one_key_red = WuHunFrontWGData.Instance:GetFrontGemKeYinOneKeyRed(wuhun_id, self.hunzhen_index, self.gem_index)
	self.node_list.btn_keyin_onekey_equip_red:CustomSetActive(one_key_red)
end

--刻印共鸣按钮
function WuHunGemFrontView:OnFrontKeYinGongMingClick()
	local wuhun_id = self.select_front_data.wuhun_id
	if not WuHunWGData.Instance:GetWuHunIsActive(wuhun_id) then
		TipWGCtrl.Instance:ShowSystemMsg(Language.WuHunZhenShen.WuhunFrontFuNengTip1)
		return
	end

	local param = {}
	param.prop_showtype = COMMON_GAME_ENUM.TWO
	param.wuhun_id = wuhun_id
	param.front_index = self.hunzhen_index
	param.gem_index = self.gem_index
	WuHunWGCtrl.Instance:SetFrontPropertyTipShow(param)
end

-------------------------------------------------------刻印 结束----------------------------------------------------


-------------------------------------------------------叠阵 开始----------------------------------------------------
--设置叠阵的信息
function WuHunGemFrontView:SetDieZhenInfo()
	local wuhun_id = self.select_front_data.wuhun_id
	local active_cfg = WuHunFrontWGData.Instance:GetFrontGemTab(wuhun_id, self.hunzhen_index, self.gem_index)
	if IsEmptyTable(active_cfg) then
		return
	end

	local front_data = WuHunFrontWGData.Instance:GetFrontDataByFrontIndex(wuhun_id, self.hunzhen_index)
	local gem_data = {}
	local min_grade = COMMON_GAME_ENUM.MAX
	for k_1, v_1 in pairs(front_data.gem_list or {}) do
		if min_grade > v_1.gem_grade then
			min_grade = v_1.gem_grade
		end

		if v_1.front_gem_index == self.gem_index then
			gem_data = v_1
		end
	end
	local gem_grade = gem_data.gem_grade or 0
	local min_grade_cfg, max_cfg, next_cfg = WuHunFrontWGData.Instance:GetMaxCanGradeAttrCfg(min_grade)
	self.node_list.text_progress_comditon.text.text = string.format(Language.WuHunZhenShen.WuhunFrontKeYinTxt1, "9df5a7",
		min_grade_cfg.name)
	self.node_list.text_progress_effect.text.text = string.format(Language.WuHunZhenShen.WuhunFrontKeYinTxt2, "9df5a7",
		min_grade_cfg.add)

	local grade_cfg = WuHunFrontWGData.Instance:GetFrontGemGradeCfg(wuhun_id, self.hunzhen_index, self.gem_index,
		gem_grade)
	local next_grade_cfg = WuHunFrontWGData.Instance:GetFrontGemGradeCfg(wuhun_id, self.hunzhen_index, self.gem_index,
		gem_grade + 1)
	local attr_data = WuHunFrontWGData.Instance:GetAttrFunc(grade_cfg, next_grade_cfg)

	for i = 1, 5 do
		self.attr_item_list[i]:SetData(attr_data[i])
	end

	self.diezhen_cell1:SetData({ item_id = grade_cfg.item_id })
	local num = WuHunFrontWGData.Instance:GetItemNumInBagById(grade_cfg.item_id) or 0
	local is_have = num >= grade_cfg.item_num
	self.diezhen_cell1:SetRightBottomColorText(num .. "/" .. grade_cfg.item_num,
		is_have and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH)
	self.diezhen_cell1:SetRightBottomTextVisible(true)

	if grade_cfg.rate_num > 0 then
		self.diezhen_cell2:SetData({ item_id = grade_cfg.rate_id })
		local num1 = WuHunFrontWGData.Instance:GetItemNumInBagById(grade_cfg.rate_id)
		local is_have1 = num1 >= grade_cfg.rate_num
		self.diezhen_cell2:SetRightBottomColorText(num1 .. "/" .. grade_cfg.rate_num,
			is_have1 and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH)
		self.diezhen_cell2:SetRightBottomTextVisible(true)

		--如果概率材料不够 直接强制改成不能选中
		if not is_have1 then
			self.slt_diezhen_rateitem = false
		end
	end
	self.node_list.node_costitem2:CustomSetActive(grade_cfg.rate_num > 0)
	self.node_list.image_cost_select:CustomSetActive(self.slt_diezhen_rateitem and grade_cfg.rate_num > 0)

	local succ_rate = grade_cfg.succ_rate
	local txt = self.slt_diezhen_rateitem and
		string.format(Language.WuHunZhenShen.WuhunFrontDieZhenTxt4, succ_rate, 100 - succ_rate)
		or string.format(Language.WuHunZhenShen.WuhunFrontDieZhenTxt3, succ_rate)
	self.node_list.text_diezhen_succeed_value.text.text = txt

	local show_value = gem_grade % 10
	local value2 = math.ceil(gem_grade / 10)
	value2 = value2 == 0 and 1 or value2
	self.node_list.image_progress_bg.image.fillAmount = diezhen_propgress_value[show_value] or 0
	self.node_list.image_progress_bg.image:LoadSprite(ResPath.GetWuHunZhenShenImage("a2_wz_jd" .. value2))
	for i = 1, 9 do
		self.node_list["image_progress_star" .. i]:CustomSetActive(show_value >= i)
		self.node_list["image_progress_star" .. i].image:LoadSprite(ResPath.GetWuHunZhenShenImage("a2_wz_zs_jd" .. value2))
	end

	local grade_add_red = WuHunFrontWGData.Instance:GetFrontGemGradeAddPropRed(wuhun_id, self.hunzhen_index,
		self.gem_index)
	self.node_list.image_btn_diezhen_red:CustomSetActive(grade_add_red)

	local btn_red = WuHunFrontWGData.Instance:GetWuHunFrontGemDieZhenBtnCostRed(wuhun_id, self.hunzhen_index,
		self.gem_index)
	self.node_list.btn_now_upgrade_red:CustomSetActive(btn_red)
	self.node_list.btn_auto_upgrade_red:CustomSetActive(btn_red)
end

function WuHunGemFrontView:OnFrontSltCostItemClick()
	local wuhun_id = self.select_front_data.wuhun_id
	local gem_data = WuHunFrontWGData.Instance:GetWuHunFrontGemData(wuhun_id, self.hunzhen_index, self.gem_index)
	local grade_cfg = WuHunFrontWGData.Instance:GetFrontGemGradeCfg(wuhun_id, self.hunzhen_index, self.gem_index,
		gem_data.gem_grade)
	local has_num = WuHunFrontWGData.Instance:GetItemNumInBagById(grade_cfg.rate_id) or 0
	if grade_cfg.rate_num <= 0 then --不消化概率物品
		return
	end

	if has_num < grade_cfg.rate_num then
		TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = grade_cfg.rate_id })
		return
	end

	self.slt_diezhen_rateitem = not self.slt_diezhen_rateitem
	self.is_auto_grade = false
	self:SetGradeOperatorShow()

	self:SetDieZhenInfo()
end

function WuHunGemFrontView:OnFrontDieZhenAddClick()
	local wuhun_id = self.select_front_data.wuhun_id
	if not WuHunWGData.Instance:GetWuHunIsActive(wuhun_id) then
		TipWGCtrl.Instance:ShowSystemMsg(Language.WuHunZhenShen.WuhunFrontFuNengTip1)
		return
	end

	local gem_data = WuHunFrontWGData.Instance:GetWuHunFrontGemData(wuhun_id, self.hunzhen_index, self.gem_index)
	--星级大于-1  已激活
	if not gem_data or gem_data.gem_star <= COMMON_GAME_ENUM.FUYI then
		TipWGCtrl.Instance:ShowSystemMsg(Language.WuHunZhenShen.WuhunFrontFuNengTip2)
		return
	end

	local param = {}
	param.prop_showtype = COMMON_GAME_ENUM.THREE
	param.wuhun_id = wuhun_id
	param.front_index = self.hunzhen_index
	param.gem_index = self.gem_index
	WuHunWGCtrl.Instance:SetFrontPropertyTipShow(param)
end

function WuHunGemFrontView:OnFrontNowUpgradeClick()
	if self.is_auto_grade then
		return
	end

	if self:GetFrontGemIsCanGrade(self.gem_index) then
		WuHunWGCtrl.Instance:SendWuHunFrontOperate(
			WUHUN_FRONT_OPERATE_TYPE.SOUL_STONE_GRADE,
			self.select_front_data.wuhun_id,
			self.hunzhen_index,
			self.gem_index,
			self.slt_diezhen_rateitem and 1 or 0,
			{}
		)
	end
end

--自动升阶点击
function WuHunGemFrontView:OnFrontAutoUpgradeClick()
	self.is_auto_grade = not self.is_auto_grade
	self:SetGradeOperatorShow()

	if not self.is_auto_grade then
		return
	end

	self:ReqFrontGemGrade()
end

--返回是否自动强化
function WuHunGemFrontView:GetIsAutoGrade()
	return self.is_auto_grade
end

--得到最小可升阶的魂阵索引 有可能一个可升阶的都没有 -1表示没有
function WuHunGemFrontView:GetFrontGemMinLevel()
	local wuhun_id = self.select_front_data.wuhun_id
	local front_data = WuHunFrontWGData.Instance:GetFrontDataByFrontIndex(wuhun_id, self.hunzhen_index)
	local min_gem_index = self.gem_index       --默认值取自身
	local min_gem_grade = COMMON_GAME_ENUM.MAX --随机无比大数

	for k_1, v_1 in pairs(front_data.gem_list) do
		local gem_data = WuHunFrontWGData.Instance:GetWuHunFrontGemData(wuhun_id, self.hunzhen_index, v_1
		.front_gem_index)
		local grade_cfg = WuHunFrontWGData.Instance:GetFrontGemGradeCfg(wuhun_id, self.hunzhen_index, v_1
		.front_gem_index, gem_data.gem_grade)
		local num = WuHunFrontWGData.Instance:GetItemNumInBagById(grade_cfg.item_id)

		if v_1.gem_star > COMMON_GAME_ENUM.FUYI and v_1.gem_grade < min_gem_grade and num >= grade_cfg.item_num then
			min_gem_grade = v_1.gem_grade
			min_gem_index = v_1.front_gem_index
		end
	end

	return min_gem_index
end

--检查是否可以强化
function WuHunGemFrontView:GetFrontGemIsCanGrade(gem_index, no_tip)
	local min_index = gem_index or self:GetFrontGemMinLevel()
	if min_index == COMMON_GAME_ENUM.FUYI then
		return false
	end

	local wuhun_id = self.select_front_data.wuhun_id
	local gem_data = WuHunFrontWGData.Instance:GetWuHunFrontGemData(wuhun_id, self.hunzhen_index, min_index)
	local grade_cfg = WuHunFrontWGData.Instance:GetFrontGemGradeCfg(wuhun_id, self.hunzhen_index, min_index,
		gem_data.gem_grade)
	local rate_num = grade_cfg.rate_num
	local num = WuHunFrontWGData.Instance:GetItemNumInBagById(grade_cfg.item_id)
	local has_rate_num = WuHunFrontWGData.Instance:GetItemNumInBagById(grade_cfg.rate_id)

	--星级大于-1  已激活
	if gem_data.gem_star <= COMMON_GAME_ENUM.FUYI then
		TipWGCtrl.Instance:ShowSystemMsg(Language.WuHunZhenShen.WuhunFrontFuNengTip2)
		return false
	end

	--材料不足
	if num < grade_cfg.item_num or (self.slt_diezhen_rateitem and has_rate_num < rate_num) then
		if not no_tip then
			TipWGCtrl.Instance:ShowSystemMsg(Language.WuHunZhenShen.WuhunFrontFuNengTxt4)
		end
		self:SetGradeOperatorShow()
		return false
	end

	return true
end

function WuHunGemFrontView:FrontGradeTimerFunc()
	local min_index = self:GetFrontGemMinLevel()
	if self:GetFrontGemIsCanGrade(min_index, true) then
		self:CancelAutoGradeTimer()
		self.is_auto_grade = true
		self.diezhen_timer_quest = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.ReqFrontGemGrade, self, 1, true),
			auto_strenge_time)
	else
		self.is_auto_grade = false
		self:SetFrontGemShow()
		self:SetGradeOperatorShow()
	end
end

--取消自动强化倒计时
function WuHunGemFrontView:CancelAutoGradeTimer()
	if nil ~= self.diezhen_timer_quest then
		GlobalTimerQuest:CancelQuest(self.diezhen_timer_quest)
		self.diezhen_timer_quest = nil
	end
end

--设置自动升阶时显示
function WuHunGemFrontView:SetGradeOperatorShow()
	local btn_txt = self.is_auto_grade and Language.WuHunZhenShen.WuhunFrontDieZhenTxt6 or
	Language.WuHunZhenShen.WuhunFrontDieZhenTxt5
	self.node_list.btn_auto_upgrade_text.text.text = btn_txt

	if not self.is_auto_grade then
		self:CancelAutoGradeTimer()
	end
end

--自动强化的请求
function WuHunGemFrontView:ReqFrontGemGrade()
	if not self:IsOpen() then
		return
	end

	if self.show_toggle_index ~= show_type.diezhen or not self.is_auto_grade then
		self.is_auto_grade = false
		self:SetGradeOperatorShow()
		return
	end

	local min_index = self:GetFrontGemMinLevel()
	if self:GetFrontGemIsCanGrade(min_index) then
		self.gem_index = min_index

		--这里先转过去
		self:OnSelectedBtnChange(function()
				if not self:IsOpen() then
					return
				end
				self:SetFrontGemShow()
				WuHunWGCtrl.Instance:SendWuHunFrontOperate(
					WUHUN_FRONT_OPERATE_TYPE.SOUL_STONE_GRADE,
					self.select_front_data.wuhun_id,
					self.hunzhen_index,
					self.gem_index,
					self.slt_diezhen_rateitem and 1 or 0,
					{}
				)
			end,
			true
		)
	else
		self.is_auto_grade = false
		self:SetGradeOperatorShow()
	end
end

-------------------------------------------------------叠阵 结束----------------------------------------------------
