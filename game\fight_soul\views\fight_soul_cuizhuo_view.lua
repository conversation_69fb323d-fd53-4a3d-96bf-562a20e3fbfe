-- 淬琢
local show_image_pos = {
    [1] = {x = 16, y = 21},
    [2] = {x = 8, y = 19},
    [3] = {x = 50, y = 67},
    [4] = {x = 2, y = 51},
}

function FightSoulView:InitCuiZhuoView()
    if not self.s_up_level_costitem1 then
        self.s_up_level_costitem1 = ItemCell.New(self.node_list.s_up_level_costitem1)
    end

    if not self.s_up_level_costitem2 then
        self.s_up_level_costitem2 = ItemCell.New(self.node_list.s_up_level_costitem2)
    end

    self.is_cuizhuo_auto_uplevel = false

    self.cuizhuo_skill_item_list = {}
    for i = 1, 4 do
        self.cuizhuo_skill_item_list[i] = CuiZhuoSkillItemRender.New(self.node_list["ad_skill_" .. i])
        self.cuizhuo_skill_item_list[i]:SetIndex(i - 1)
    end

    XUI.AddClickEventListener(self.node_list.btn_s_wear, BindTool.Bind(self.ClickTrainWear, self))
    XUI.AddClickEventListener(self.node_list.btn_zhuotie, BindTool.Bind(self.ClickZhuoTie, self))
    XUI.AddClickEventListener(self.node_list.s_btn_uplevel, BindTool.Bind(self.ClickCuiZhuoUpLevel, self))
end

function FightSoulView:DeleteCuiZhuoView()
    if self.s_up_level_costitem1 then
        self.s_up_level_costitem1:DeleteMe()
        self.s_up_level_costitem1 = nil
    end

    if self.s_up_level_costitem2 then
        self.s_up_level_costitem2:DeleteMe()
        self.s_up_level_costitem2 = nil
    end

    if self.cuizhuo_skill_item_list then
        for k,v in pairs(self.cuizhuo_skill_item_list) do
            v:DeleteMe()
        end

        self.cuizhuo_skill_item_list = nil
    end

    self:StopCuiZhuoAutoUpLevel()
    self:ClearCuiZhuoSliderTween()
end

function FightSoulView:CuiZhuoShowIndexCallBack()
    self:ClearCuiZhuoSliderTween()
    self:StopCuiZhuoAutoUpLevel()
end

function FightSoulView:FlushCuiZhuoModelPart()
    local fs_data = self:GetCurFightSoulSlot()
    if IsEmptyTable(fs_data) then
        return
    end

    local is_wear = fs_data:GetIsWear()
    self.node_list.s_no_wear_part:SetActive(not is_wear)
    self.node_list.cuizhuo_spine_pos:SetActive(is_wear)
    self.node_list.s_skill_part:SetActive(is_wear)
    XUI.SetButtonEnabled(self.node_list.add, is_wear)
    self.node_list.btn_s_wear:SetActive(not is_wear)
    self.node_list.s_base_no_fight_desc:SetActive(not is_wear)
    self.node_list.s_base_level_part:SetActive(is_wear)

    if is_wear then
        for k,v in pairs(self.cuizhuo_skill_item_list) do
            v:SetData({is_cuizhuo = true, solt = self.select_slot_index})
        end

        local fs_type = fs_data:GetType()
        if self.show_fs_model_ani or self.cache_s_fs_type ~= fs_type then
            local bundle, asset = ResPath.GetFightSoulShowUI(fs_type)
            self.node_list.cuizhuo_spine_pos:ChangeAsset(bundle, asset, false)
            RectTransform.SetAnchoredPositionXY(self.node_list.cuizhuo_spine_pos.rect, show_image_pos[fs_type].x, show_image_pos[fs_type].y)
            self.cache_s_fs_type = fs_type
        end
    else
        self.node_list.s_remind_inlay:SetActive(FightSoulWGData.Instance:GetFightSoulCanWearByType(fs_data:GetSlotIndex()))
    end
end

function FightSoulView:FlushCuiZhuoUpLevelPart()
    local data = self:GetCurFightSoulSlot()
    if IsEmptyTable(data) then
        return
    end

    local data_info = FightSoulWGData.Instance:GetSiXiangQuenchInfoBySolt(self.select_slot_index)
    local grade_cfg = FightSoulWGData.Instance:GetCuiZhuoGradeCfg(self.select_slot_index, data_info.grade)
    local next_grade_cfg = FightSoulWGData.Instance:GetCuiZhuoGradeCfg(self.select_slot_index, data_info.grade + 1)
    local is_wear = data:GetIsWear()
    local is_max_level = IsEmptyTable(next_grade_cfg)

    self.node_list.s_base_level_arrow:SetActive(not is_max_level)
    self.node_list.s_base_next_img:SetActive(not is_max_level)
    self.node_list.s_base_cur_level.text.text = string.format(Language.FightSoul.CuiZhuoLevel, grade_cfg.grade)

    if not is_max_level then
        self.node_list.s_base_next_level.text.text = string.format(Language.FightSoul.CuiZhuoLevel, next_grade_cfg.grade)
    end

    local show_attr_list = FightSoulWGData.Instance:GetCuiZhuoUpGradeShowAttr(self.select_slot_index, data_info.grade, not is_wear or is_max_level)
    for i = 1, 5 do
        local attr_item_data = show_attr_list[i]
        if not IsEmptyTable(attr_item_data) then
            self.node_list["s_attr_" .. i]:SetActive(true)
            self.node_list["s_arrow_" .. i]:SetActive(is_wear and not is_max_level)
            self.node_list["s_next_value_" .. i]:SetActive(is_wear and not is_max_level)
            self.node_list["s_name_" .. i].text.text = attr_item_data.attr_str
            self.node_list["s_cur_value_" .. i].text.text = attr_item_data.attr_value
            self.node_list["s_next_value_" .. i].text.text = attr_item_data.add_value
        else
            self.node_list["s_attr_" .. i]:SetActive(false)
        end
    end

    self.node_list.s_uplevel_ismax:SetActive(is_wear and is_max_level)
    self.node_list.s_btn_uplevel:SetActive(not is_max_level)
    self.node_list.s_btn_uplevel_remind:SetActive(is_wear and FightSoulWGData.Instance:IsCuiZhuoItemCanUpGrade(self.select_slot_index))
    self.node_list.s_exp_progress:SetActive(not is_max_level)
    self.s_up_level_costitem1:SetActive(not is_max_level)
    self.s_up_level_costitem2:SetActive(not is_max_level)
    self.node_list.s_exp_progress.slider.value = data_info.exp / grade_cfg.need_exp
    self.node_list.s_exp_progress_text.text.text = data_info.exp .. "/" .. grade_cfg.need_exp
end

function FightSoulView:FlushCuiZhuoCostItem()
    local data_cfg = FightSoulWGData.Instance:GetCuiZhuoStuffCfg()

    if not IsEmptyTable(data_cfg) then
		local item_id1 = data_cfg[1].item_id
		local num1 = ItemWGData.Instance:GetItemNumInBagById(item_id1)
		self.s_up_level_costitem1:SetFlushCallBack(function ()
			self.s_up_level_costitem1:SetRightBottomColorText(num1)
			self.s_up_level_costitem1:SetRightBottomTextVisible(true)
		end)
		
		self.s_up_level_costitem1:SetData({item_id = item_id1, num = num1, is_bind = 0})
		local item_id2 = data_cfg[2].item_id
		local num2 = ItemWGData.Instance:GetItemNumInBagById(item_id2)
		self.s_up_level_costitem2:SetFlushCallBack(function ()
			self.s_up_level_costitem2:SetRightBottomColorText(num2)
			self.s_up_level_costitem2:SetRightBottomTextVisible(true)
		end)

		self.s_up_level_costitem2:SetData({item_id = item_id2, num = num2, is_bind = 0})
	end
end

function FightSoulView:ClickZhuoTie()
    FightSoulWGCtrl.Instance:OpenZhuoTieView()
end

function FightSoulView:FlushCuiZhuoRemind()
    local remind = FightSoulWGData.Instance:GetZhuoTieRemind()
    self.node_list.btn_zhuotie_remind:CustomSetActive(remind)
end

function FightSoulView:ClickCuiZhuoUpLevel()
    local data = self:GetCurFightSoulSlot()
    if IsEmptyTable(data) then
        return
    end

    if data:GetIsWear() then
    	if not self.is_cuizhuo_auto_uplevel then
            self:StartCuiZhuoAutoUpLevel()
        else
            self:StopCuiZhuoAutoUpLevel()
        end
    else
        TipWGCtrl.Instance:ShowSystemMsg(Language.FightSoul.UpLevelLimitTips1)
    end
end

----------------------------------------------一键升级---------------------------------------------
function FightSoulView:FlushCuiZhuoView(solt_select_change)
    if solt_select_change then
        self:StopCuiZhuoAutoUpLevel()
        self:ClearCuiZhuoSliderTween()
    end

    self:FlushCuiZhuoRemind()
    self:FlushCuiZhuoCostItem()

    local cuizhuo_info = FightSoulWGData.Instance:GetSiXiangQuenchInfoBySolt(self.select_slot_index)

    if not IsEmptyTable(cuizhuo_info) then
        local grade_cfg = FightSoulWGData.Instance:GetCuiZhuoGradeCfg(self.select_slot_index, cuizhuo_info.grade)

        if not IsEmptyTable(grade_cfg) then
            local next_grade_cfg = FightSoulWGData.Instance:GetCuiZhuoGradeCfg(self.select_slot_index, cuizhuo_info.grade + 1)
            local is_max_level = IsEmptyTable(next_grade_cfg)

			if is_max_level then
				if self.is_cuizhuo_auto_uplevel then
					self:PlayCuiZhuoSliderTween(0, cuizhuo_info.exp, grade_cfg.need_exp)
				else
					self.node_list.s_exp_progress.slider.value = 1
				end
	
				self.node_list.s_exp_progress_text.text.text = "-/-"
			else
				if self.is_cuizhuo_auto_uplevel then
					local add_level = cuizhuo_info.grade - self.cuizhuo_old_level
			   		self:PlayCuiZhuoSliderTween(add_level, cuizhuo_info.exp, grade_cfg.need_exp)
				else
					local need_exp = grade_cfg.need_exp
					local current_exp = cuizhuo_info.exp
					local slider_value = current_exp / need_exp
					self.node_list.s_exp_progress_text.text.text = current_exp .. "/" .. need_exp
					self.node_list.s_exp_progress.slider.value = slider_value
					self.node_list["s_btn_uplevel_text"].text.text = Language.FightSoul.CuiZhuoUpGradeBtnDesc[1]
				end
			end
	
			if not self.is_cuizhuo_auto_uplevel then
				self:CuiZhuoLevelChangeFlush()
			end
        end
    end
end

function FightSoulView:StartCuiZhuoAutoUpLevel(no_tips)
    local cuizhuo_info = FightSoulWGData.Instance:GetSiXiangQuenchInfoBySolt(self.select_slot_index)
    local next_grade_cfg = FightSoulWGData.Instance:GetCuiZhuoGradeCfg(self.select_slot_index, cuizhuo_info.grade + 1)
    local is_max_level = IsEmptyTable(next_grade_cfg)

    if IsEmptyTable(cuizhuo_info) or is_max_level then
		self:StopCuiZhuoAutoUpLevel()
		return
	end

	local has_material = FightSoulWGData.Instance:GetCuiZhuoStuffCanAddExp() > 0

	if not has_material then
		if not no_tips then
			local stuff_cfg = FightSoulWGData.Instance:GetCuiZhuoStuffCfg()
			local item_id = (stuff_cfg[1] or {}).item_id
			
			if item_id then
				TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
			end
		end

		self:StopCuiZhuoAutoUpLevel()
		return 
	end

    self.cuizhuo_old_level = cuizhuo_info.grade
    self.is_cuizhuo_auto_uplevel = true
    self.node_list["s_btn_uplevel_text"].text.text = Language.FightSoul.CuiZhuoUpGradeBtnDesc[2]
    FightSoulWGCtrl.Instance:SendFightSoulCuiZhuoOperate(SIXIANG_QUENCH_OPERATE_TYPE.SOLT_UPGRADE, self.select_slot_index)
end

function FightSoulView:PlayCuiZhuoSliderTween(add_level, exp_val, need_exp_val)
	if need_exp_val <= 0 then
		self:ClearCuiZhuoSliderTween()
		self:StopCuiZhuoAutoUpLevel()
		return
	end

	if add_level == 0 and exp_val == need_exp_val then
        self:ClearCuiZhuoSliderTween()
        local slider = self.node_list.s_exp_progress.slider
        local time = tonumber(string.format("%.2f", (1 - slider.value) * 0.5))
        self.cuizhuo_slider_tween = slider:DOValue(1, time)
        self.cuizhuo_slider_tween:OnComplete(function ()
			MainuiWGCtrl.Instance:DelayShowCachePower(0)
		end)

        self:StopCuiZhuoAutoUpLevel()
        return
    end

    local cuizhuo_info = FightSoulWGData.Instance:GetSiXiangQuenchInfoBySolt(self.select_slot_index)
    if IsEmptyTable(cuizhuo_info) then
         return
    end

    local real_level = cuizhuo_info.grade
    self.cuizhuo_slider_tween_func = function(progress)
        self:ClearCuiZhuoSliderTween()
        local before_uplevel_cfg = FightSoulWGData.Instance:GetCuiZhuoGradeCfg(self.select_slot_index, self.cuizhuo_old_level)

        if not before_uplevel_cfg then
            return
        end
        
        if progress <= 0 then
            if self.is_cuizhuo_auto_uplevel then
                if self.cuizhuo_old_level ~= real_level then
                    self.cuizhuo_old_level = real_level
                    self:CuiZhuoLevelChangeFlush()
                end

                self:StartCuiZhuoAutoUpLevel(true)
            end

            return
        end

        local is_up_one_level = false
        local slider = self.node_list.s_exp_progress.slider

		if progress > 1 then
			local time = tonumber(string.format("%.2f", (1 - slider.value) * 0.5))
			self.cuizhuo_slider_tween = slider:DOValue(1, time)
            is_up_one_level = true
            self.node_list.s_exp_progress_text.text.text = before_uplevel_cfg.need_exp .. "/" .. before_uplevel_cfg.need_exp
		else
			local time = tonumber(string.format("%.2f", (progress - slider.value) * 0.5))
			self.cuizhuo_slider_tween = slider:DOValue(progress, time)
            self.node_list.s_exp_progress_text.text.text = cuizhuo_info.exp .. "/" .. before_uplevel_cfg.need_exp
		end

        progress = progress - 1
        self.cuizhuo_slider_tween:OnComplete(function ()
			if progress >= 0 then
				slider.value = 0

                if is_up_one_level then
                    self.cuizhuo_old_level = self.cuizhuo_old_level + 1
                    self:CuiZhuoLevelChangeFlush()
                end
			end
			
			if progress < 1 then
				MainuiWGCtrl.Instance:DelayShowCachePower(0)
			end

			self.cuizhuo_slider_tween_func(progress)
		end)
    end

    local total_progress = add_level + exp_val / need_exp_val
	self.cuizhuo_slider_tween_func(total_progress)
end

function FightSoulView:CuiZhuoLevelChangeFlush()
    self:FlushCuiZhuoModelPart()
    self:FlushCuiZhuoUpLevelPart()
end

function FightSoulView:ClearCuiZhuoSliderTween()
    if self.cuizhuo_slider_tween then
        self.cuizhuo_slider_tween:Kill()
        self.cuizhuo_slider_tween = nil
    end
end

function FightSoulView:StopCuiZhuoAutoUpLevel()
    self.is_cuizhuo_auto_uplevel = false
    if self.node_list["s_btn_uplevel_text"] then
        self.node_list["s_btn_uplevel_text"].text.text = Language.FightSoul.CuiZhuoUpGradeBtnDesc[1]
    end
end

function FightSoulView:PlayCuiZhuoEffect(effect_type)
    TipWGCtrl.Instance:ShowEffect({effect_type = effect_type, is_success = true,pos = Vector2(0, 0), parent_node = self.node_list["s_effect_node"]})
end

CuiZhuoSkillItemRender = CuiZhuoSkillItemRender or BaseClass(BaseRender)
function CuiZhuoSkillItemRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.ad_skill_click, BindTool.Bind(self.ClickSkill, self))
end

function CuiZhuoSkillItemRender:OnFlush()
    if not self.data then
        return
    end
    
    local data_info = FightSoulWGData.Instance:GetSiXiangQuenchInfoBySolt(self.data.solt)
    if not IsEmptyTable(data_info) then
        local level = (data_info.skill_level_list or {}) [self.index]
        
        if level then
            local skill_data_cfg = FightSoulWGData.Instance:GetCuiZhuoSkillCfg(self.data.solt, self.index)

            if not IsEmptyTable(skill_data_cfg) then
                local data_cfg = skill_data_cfg[level >= 1 and level or 1]

                if not IsEmptyTable(data_cfg) then
                    local bundel, asset = ResPath.GetSkillIconById(data_cfg.skill_icon)
                    self.node_list.icon.image:LoadSprite(bundel, asset, function()
                        self.node_list.icon.image:SetNativeSize()
                    end)
    
                    local active = data_info.grade >= data_cfg.need_grade
                    local is_max_levle = level >= #skill_data_cfg
                    local has_num = ItemWGData.Instance:GetItemNumInBagById(data_cfg.cost_item_id)
                    self.node_list.remind:SetActive(active and not is_max_levle and has_num >= data_cfg.cost_item_num)
                    self.node_list.level_part:SetActive(active)
                    self.node_list.skill_level.text.text = string.format(Language.Common.LevelNormal, level)
                    XUI.SetGraphicGrey(self.node_list.ad_skill_click, not active)
                end 
            end
        end
    end  
end

function CuiZhuoSkillItemRender:ClickSkill()
    if not self.data then
        return
    end

    local data_info = FightSoulWGData.Instance:GetSiXiangQuenchInfoBySolt(self.data.solt)
    if not IsEmptyTable(data_info) then
        local level = (data_info.skill_level_list or {}) [self.index]

        if level then
            local skill_data_cfg = FightSoulWGData.Instance:GetCuiZhuoSkillCfg(self.data.solt, self.index)

            if not IsEmptyTable(skill_data_cfg) then 
                local data_cfg = skill_data_cfg[level >= 1 and level or 1]

                if not IsEmptyTable(data_cfg) then
                    local active = data_info.grade >= data_cfg.need_grade
                    local limit_text = ""
                    local next_skill_desc = ""
                    local is_max_level = level >= #skill_data_cfg

                    if self.data.is_cuizhuo and not active then
                        limit_text = string.format(Language.FightSoul.CuiZhuoSkillGradeActTips, data_cfg.need_grade)
                    end
    
                    if active and not is_max_level and data_info.grade > 0 then
                        local next_cfg = skill_data_cfg[level + 1]

                        if not IsEmptyTable(next_cfg) then
                            next_skill_desc = next_cfg.skill_desc
                        end
                    end

                    local view = ViewManager.Instance:GetView(GuideModuleName.FightSoulView)
                    local parent_rect = view.root_node.transform:GetComponent(typeof(UnityEngine.RectTransform))
                    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, self.view.transform.position)
                    local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, Vector2(0, 0))
                    local _,capability = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(data_cfg, "attr_id", "attr_value")
                    local show_data = {
                        solt = self.data.solt,
                        seq = self.index,
                        item_id = data_cfg.cost_item_id,
                        item_num = data_cfg.cost_item_num,
                        skill_box_type = SKILL_BOX_TYPE.CUI_ZHUO_SKILL,
                        skill_level = level >= 1 and level or 1,
                        icon = data_cfg.skill_icon,
                        top_text = data_cfg.skill_name,
                        body_text = data_cfg.skill_desc,
                        limit_text = limit_text,
                        next_skill_desc = next_skill_desc,
                        set_pos = true,
                        x = local_position_tbl.x,
                        y = local_position_tbl.y + 60,
                        capability = capability,
                    }
    
                    NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
                end
            end
        end
    end
end