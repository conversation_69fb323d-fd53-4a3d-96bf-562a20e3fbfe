﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class PostEffectsWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(PostEffects), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>RegFunction("DoBlurSpread", DoBlurSpread);
		<PERSON><PERSON>unction("DoWave", DoWave);
		<PERSON><PERSON>unction("DoMotionBlurStrength", DoMotionBlurStrength);
		<PERSON><PERSON>RegFunction("__eq", op_Equality);
		<PERSON><PERSON>RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("EnableBloom", get_EnableBloom, set_EnableBloom);
		<PERSON><PERSON>("EnableColorCurve", get_EnableColorCurve, set_EnableColorCurve);
		<PERSON>.Reg<PERSON>ar("EnableSaturation", get_EnableSaturation, set_EnableSaturation);
		<PERSON><PERSON>("EnableVignette", get_EnableVignette, set_EnableVignette);
		<PERSON><PERSON>("EnableBlur", get_EnableBlur, set_EnableBlur);
		<PERSON><PERSON>("BlurSpread", get_BlurSpread, set_BlurSpread);
		L.RegVar("WaveStrength", get_WaveStrength, set_WaveStrength);
		L.RegVar("EnableMotionBlur", get_EnableMotionBlur, set_EnableMotionBlur);
		L.RegVar("MotionBlurDist", null, set_MotionBlurDist);
		L.RegVar("MotionBlurStrength", null, set_MotionBlurStrength);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DoBlurSpread(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			PostEffects obj = (PostEffects)ToLua.CheckObject(L, 1, typeof(PostEffects));
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			obj.DoBlurSpread(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DoWave(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			PostEffects obj = (PostEffects)ToLua.CheckObject(L, 1, typeof(PostEffects));
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			obj.DoWave(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DoMotionBlurStrength(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			PostEffects obj = (PostEffects)ToLua.CheckObject(L, 1, typeof(PostEffects));
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			obj.DoMotionBlurStrength(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EnableBloom(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PostEffects obj = (PostEffects)o;
			bool ret = obj.EnableBloom;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index EnableBloom on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EnableColorCurve(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PostEffects obj = (PostEffects)o;
			bool ret = obj.EnableColorCurve;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index EnableColorCurve on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EnableSaturation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PostEffects obj = (PostEffects)o;
			bool ret = obj.EnableSaturation;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index EnableSaturation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EnableVignette(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PostEffects obj = (PostEffects)o;
			bool ret = obj.EnableVignette;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index EnableVignette on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EnableBlur(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PostEffects obj = (PostEffects)o;
			bool ret = obj.EnableBlur;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index EnableBlur on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_BlurSpread(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PostEffects obj = (PostEffects)o;
			float ret = obj.BlurSpread;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index BlurSpread on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_WaveStrength(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PostEffects obj = (PostEffects)o;
			float ret = obj.WaveStrength;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index WaveStrength on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EnableMotionBlur(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PostEffects obj = (PostEffects)o;
			bool ret = obj.EnableMotionBlur;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index EnableMotionBlur on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_EnableBloom(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PostEffects obj = (PostEffects)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.EnableBloom = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index EnableBloom on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_EnableColorCurve(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PostEffects obj = (PostEffects)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.EnableColorCurve = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index EnableColorCurve on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_EnableSaturation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PostEffects obj = (PostEffects)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.EnableSaturation = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index EnableSaturation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_EnableVignette(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PostEffects obj = (PostEffects)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.EnableVignette = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index EnableVignette on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_EnableBlur(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PostEffects obj = (PostEffects)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.EnableBlur = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index EnableBlur on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_BlurSpread(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PostEffects obj = (PostEffects)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.BlurSpread = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index BlurSpread on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_WaveStrength(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PostEffects obj = (PostEffects)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.WaveStrength = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index WaveStrength on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_EnableMotionBlur(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PostEffects obj = (PostEffects)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.EnableMotionBlur = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index EnableMotionBlur on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MotionBlurDist(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PostEffects obj = (PostEffects)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.MotionBlurDist = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MotionBlurDist on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MotionBlurStrength(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PostEffects obj = (PostEffects)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.MotionBlurStrength = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MotionBlurStrength on a nil value");
		}
	}
}

