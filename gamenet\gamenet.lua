
require("gamenet/msgadapter")
require("protocolcommon/baseprotocolstruct")
require("protocolcommon/protocol_struct")
require("protocolcommon/userprotocol/user_protocol")

GameNet = GameNet or BaseClass()
GameNet.DISCONNECT_REASON_FORBIDDEN = "forbidden" 	-- 禁止的
GameNet.DISCONNECT_REASON_NORMAL = "normal"

-- 与EnhancedNetClient.cs中的DisconnectReason对应
GameNet.DisconnectReason = {
	Manual = 0, 		-- 本地主动断开
	RemoteClosed = 1,	-- 远端主动关闭
	NotConnected = 2,	-- 套接字未连接（发送前检测到未连接）
	SendError = 3,		-- 发送过程中发生异常
	ReceiveError = 4,	-- 接收过程中发生异常
	ReceiveStartError = 5,	-- 开始接收时发生异常
}

GameNet.DisconnectReasonStr = {
	[0] = "本地主动断开",
	[1] = "远端主动关闭",
	[2] = "套接字未连接（发送前检测到未连接）",
	[3] = "发送过程中发生异常",
	[4] = "接收过程中发生异常",
	[5] = "开始接收时发生异常",
}

local ConnectState = {
	Disconnect = 0,
	Connecting = 1,
	Connected = 2,
}

--定义8秒连接超时操作
local NET_CONNECT_TIME_OUT = 8

function GameNet:__init()
	if GameNet.Instance ~= nil then
		print_error("[GameNet] attempt to create singleton twice!")
		return
	end
	GameNet.Instance = self

	ProtocolPool.New()

	-- login server 的链接信息
	self:ResetLoginServer()
	self.login_server_host_name = ""
	self.login_server_host_port = 0

	-- game server 的链接信息
	self:ResetGameServer()
	self.game_server_host_name = ""
	self.game_server_host_port = 0

	self.no_recv_time = 0							-- 最后收消息时间
	self.cur_net = nil

	self.msg_operate_table = {}						-- 协议处理函数表

	self.loginserver_connect_cache = {
		is_delay_connect = false,
		next_can_connect_time = 0,
		timeout = 0}

	self.gameserver_connect_cache = {
		is_delay_connect = false,
		next_can_connect_time= 0,
		timeout = 0}

	self.custom_disconnect_reason = GameNet.DISCONNECT_REASON_NORMAL
	self.custom_disconnect_notice_type = DISCONNECT_NOTICE_TYPE.INVALID
	
	Runner.Instance:AddRunObj(self, 6)
end

function GameNet:__delete()
	Runner.Instance:RemoveRunObj(self)

	ProtocolPool.Instance:DeleteMe()

	self:DisconnectLoginServer()
	self:ClearServerNet(self.login_server_net)
	self.login_server_net = nil

	self:DisconnectGameServer()
	self:ClearServerNet(self.game_server_net)
	self.game_server_net = nil

	self.msg_operate_table = {}

	-- 连接超时(断线重连使用, 连接超时)
	self:RemoveConnentDelayTimer()

	GameNet.Instance = nil
end

function GameNet:ClearServerNet(server_net)
	if nil ~= server_net then
		server_net:Clear()
	end
end

--移除回调
function GameNet:RemoveConnentDelayTimer()
    if self.connent_out_time_timer then
        GlobalTimerQuest:CancelQuest(self.connent_out_time_timer)
        self.connent_out_time_timer = nil
    end
end

function GameNet:Update(now_time, elapse_time)
	if self.cur_net == self.game_server_net then
		if elapse_time > 1.0 then
			self.no_recv_time = self.no_recv_time + 1.0
		else
			self.no_recv_time = self.no_recv_time + elapse_time
		end

		if self.no_recv_time >= 60 and not UnityEngine.Debug.isDebugBuild then
			if LoginWGCtrl ~= nil and LoginWGCtrl.Instance ~= nil then
				LoginWGCtrl.Instance:ExitReq()
			end

			self.no_recv_time = 0
		end
	end

	if self.loginserver_connect_cache.is_delay_connect and
		Status.NowTime > self.loginserver_connect_cache.next_can_connect_time then
		print_log("执行延迟登陆服的连接")
		self:ClearLoginServerConnectCache()
		self:ResetLoginServer()
		self:AsyncConnectLoginServer(self.loginserver_connect_cache.timeout)
	end

	if self.gameserver_connect_cache.is_delay_connect and
		Status.NowTime > self.gameserver_connect_cache.next_can_connect_time then
		print_log("执行延迟游戏服的连接")
		self:ClearGameServerConnectCache()
		self:ResetGameServer()
		self:AsyncConnectGameServer(self.gameserver_connect_cache.timeout)
	end
end

function GameNet:GetCurNet()
	return self.cur_net
end

function GameNet:OnRecvMsg(net, pack_data)
	if net ~= self.game_server_net and net ~= self.login_server_net then
		return
	end

	MsgAdapter.InitReadMsg(pack_data)
	local msg_type = MsgAdapter.ReadUShort()
	MsgAdapter.ReadUShort()

	self.no_recv_time = 0
	self.cur_net = net

	if nil ~= self.delay_disconnect_timer then
		GlobalTimerQuest:CancelQuest(self.delay_disconnect_timer)
		self.delay_disconnect_timer = nil
	end

	if GAMENET_RECV_DEBUG_SWITCH then
		print_error("<color=#00ff00>---- 接受到协议，协议号:</color>", msg_type, socket.gettime())
	end

	if BaseProtocolStruct.PRINT then
		if BulitInGMCtrl ~= nil and BulitInGMCtrl.Instance ~= nil then
			BulitInGMCtrl.Instance:AddProtocolMsg(msg_type, 0, socket.gettime())
		end
	end

	local cost_time = 0
	if BaseProtocolStruct.COLLECT_PRINT then
		cost_time = os.clock()
	end

	local oper_func = self.msg_operate_table[msg_type]
	if oper_func then
		local protocol = ProtocolPool.Instance:GetProtocolByType(msg_type)
		if protocol then
			protocol:Decode()
			AddProfileBeginSample("receive_msg " .. msg_type)
			oper_func(protocol)
			AddProfileEndSample()

			if is_develop then
				develop_mode:OnReceiveMsg(msg_type)
			end
		else
			print_log("Unknow protocol:[" .. msg_type .. "]")
		end
	end

	if BaseProtocolStruct.COLLECT_PRINT then
		ClientCmdWGCtrl.Instance:AddProtocolMsg(msg_type, os.clock() - cost_time)
	end
end


function GameNet:OnDisconnect(net, real_disconnect_reason, disconnect_detail)
	if self.login_server_net == net then
		self.login_connect_state = ConnectState.Disconnect
		self:ClearLoginServerConnectCache()
		self:ResetLoginServer()
		GlobalEventSystem:Fire(LoginEventType.LOGIN_SERVER_DISCONNECTED, self.custom_disconnect_reason, self.custom_disconnect_notice_type, real_disconnect_reason, disconnect_detail)
	elseif self.game_server_net == net then
		self.game_connect_state = ConnectState.Disconnect
		self:ClearGameServerConnectCache()
		self:ResetGameServer()
		GlobalEventSystem:Fire(LoginEventType.GAME_SERVER_DISCONNECTED, self.custom_disconnect_reason, self.custom_disconnect_notice_type, real_disconnect_reason, disconnect_detail)
	end

	self.custom_disconnect_reason = GameNet.DISCONNECT_REASON_NORMAL
	self.custom_disconnect_notice_type = DISCONNECT_NOTICE_TYPE.INVALID
end

function GameNet:RegisterMsgOperate(msg_type, msg_oper_func)		-- 注册协议处理函数
	self.msg_operate_table[msg_type] = msg_oper_func
end

function GameNet:UnRegisterMsgOperate(msg_type)
	self.msg_operate_table[msg_type] = nil
end

--------------------------------------------------------------------------------------
function GameNet:ResetLoginServer()
	self:ClearServerNet(self.login_server_net)
	self.login_server_net = EnhancedNetClient.New()
	self.login_server_net:ListenDisconnectWithReason(function(reason, detail)
		self:OnDisconnect(self.login_server_net, reason, detail)
	end)

	self.login_server_net:ListenMessage(
		BindTool.Bind(self.OnRecvMsg, self, self.login_server_net))

	self.login_connect_state = ConnectState.Disconnect
end

function GameNet:DisconnectLoginServer(custom_disconnect_reason)
	if self.login_connect_state == ConnectState.Connected then
		self.login_connect_state = ConnectState.Disconnect
		self.custom_disconnect_reason = custom_disconnect_reason or GameNet.DISCONNECT_REASON_NORMAL
		self.login_server_net:Disconnect()
	end
end

function GameNet:SetLoginServerInfo(host_name, host_port)
	self.login_server_host_name = host_name
	self.login_server_host_port = host_port
end

function GameNet:GetLoginServerIp()
	return self.login_server_host_name
end

function GameNet:GetLoginServerPort()
	return self.login_server_host_port
end

function GameNet:IsLoginServerConnected()
	return self.login_connect_state == ConnectState.Connected
end

function GameNet:IsLoginServerInAsyncConnect()
	return self.login_connect_state == ConnectState.Connecting
end

function GameNet:AsyncConnectLoginServer(timeout)
	if self.login_connect_state ~= ConnectState.Disconnect then
		print_log("重复连接登录服")
		return false
	end

	if "" == self.login_server_host_name or 0 == self.login_server_host_port then
		print_log("Please set login server info before async connect!")
		return false
	end

	if Status.NowTime < self.loginserver_connect_cache.next_can_connect_time then
		self.loginserver_connect_cache.is_delay_connect = true
		print_log("连接登录服务器频率过高，将延迟连接")
		return false
	end

	self.loginserver_connect_cache.timeout = timeout
	self.loginserver_connect_cache.is_delay_connect = false
	self.loginserver_connect_cache.next_can_connect_time = Status.NowTime + timeout

	self.login_connect_state = ConnectState.Connecting
	self.login_server_net:Connect(self.login_server_host_name, self.login_server_host_port, function(is_succ)
		if IsNil(self.login_server_net) then return end
		if is_succ then
			IS_ON_CROSSSERVER = false
			self:ClearLoginServerConnectCache()
			self.cur_net = self.login_server_net
			self.login_server_net:StartReceive()
			self.login_connect_state = ConnectState.Connected
		else
			self.login_connect_state = ConnectState.Disconnect
		end

		print_log("Async Connect to login server Ret: status ", is_succ)
		GlobalEventSystem:Fire(LoginEventType.LOGIN_SERVER_CONNECTED, is_succ)
	end)

	return true
end

function GameNet:ClearLoginServerConnectCache()
	self.loginserver_connect_cache.is_delay_connect = false
	self.loginserver_connect_cache.next_can_connect_time = 0
	self.loginserver_connect_cache.timeout = 0
end

function GameNet:GetLoginNet()
	return self.login_server_net
end

--------------------------------------------------------------------------------------
function GameNet:ResetGameServer()
	self:ClearServerNet(self.game_server_net)
	self.game_server_net = EnhancedNetClient.New()
	self.game_server_net:ListenDisconnectWithReason(function(reason, detail)
		self:OnDisconnect(self.game_server_net, reason, detail)
	end)
	self.game_server_net:ListenMessage(
		BindTool.Bind(self.OnRecvMsg, self, self.game_server_net))

	self.game_connect_state = ConnectState.Disconnect
end

function GameNet:DisconnectGameServer(custom_disconnect_reason)
	if self.game_connect_state == ConnectState.Connected then
		self.game_connect_state = ConnectState.Disconnect
		self.custom_disconnect_reason = custom_disconnect_reason or GameNet.DISCONNECT_REASON_NORMAL
		self.game_server_net:Disconnect()
	end
end

function GameNet:SetGameServerInfo(host_name, host_port)
	self.game_server_host_name = host_name
	self.game_server_host_port = host_port
end

function GameNet:IsGameServerConnected()
	return self.game_connect_state == ConnectState.Connected
end

function GameNet:IsGameServerDisconnect()
	return self.game_connect_state == ConnectState.Disconnect
end

function GameNet:IsGameServerInAsyncConnect()
	return self.game_connect_state == ConnectState.Connecting
end

function GameNet:AsyncConnectGameServer(timeout, call_back)
	if self.game_connect_state ~= ConnectState.Disconnect then
		if call_back then
			call_back(false)
		end
		return false
	end

	if "" == self.game_server_host_name or 0 == self.game_server_host_port then
		if call_back then
			call_back(false)
		end
		return
	end

	if Status.NowTime < self.gameserver_connect_cache.next_can_connect_time then
		self.gameserver_connect_cache.is_delay_connect = true
		if call_back then
			call_back(false)
		end
		return
	end

	--这里设置了超时处理，但是单次重连is_delay_connect为假则永远不会走单次的超时
	self.gameserver_connect_cache.timeout = timeout
	self.gameserver_connect_cache.is_delay_connect = false
	self.gameserver_connect_cache.next_can_connect_time = Status.NowTime + timeout

	-- 这个延时定时器检测重连状态
	self:RemoveConnentDelayTimer()
	self.game_connect_state = ConnectState.Connecting
	self.game_server_net:Connect(self.game_server_host_name, self.game_server_host_port, function(is_succ)
		if IsNil(self.game_server_net) then return end
		if is_succ then
			self:ClearGameServerConnectCache()
			self.cur_net = self.game_server_net
			self.no_recv_time = 0
			self.game_server_net:StartReceive()
			self.game_connect_state = ConnectState.Connected

			if call_back == nil then	---带有回调的属于自动重连的状态，这个时候可以不重置场景状态
				Scene.Instance:ResetIsEnterScene()
			end
		else
			self.game_connect_state = ConnectState.Disconnect
		end

		---带回调的是断线重连
		print_log("Async Connect to game server Ret: status ", is_succ)
		GlobalEventSystem:Fire(LoginEventType.GAME_SERVER_CONNECTED, is_succ)

		self:RemoveConnentDelayTimer()
		if call_back then
			call_back(is_succ)
		end
	end)

	if call_back then
		self.connent_out_time_timer = GlobalTimerQuest:AddDelayTimer(function ()
			-- print_error("定时器超时警告", self.game_connect_state == ConnectState.Connected)
			call_back(self.game_connect_state == ConnectState.Connected)
		end, NET_CONNECT_TIME_OUT)
	end

	return true
end

function GameNet:ClearGameServerConnectCache()
	self.gameserver_connect_cache.is_delay_connect = false
	self.gameserver_connect_cache.next_can_connect_time = 0
	self.gameserver_connect_cache.timeout = 0
end

function GameNet:GetGameServerNet()
	return self.game_server_net
end
