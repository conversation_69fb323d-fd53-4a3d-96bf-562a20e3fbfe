--上古遗迹、神魔禁地进入消耗物品界面
DabaoEnterConsumView = DabaoEnterConsumView or BaseClass(SafeBaseView)

function DabaoEnterConsumView:__init()
	self.is_modal = true
	self.is_any_click_close = true

	self:SetMaskBg(true)
    self.view_name = "EnterConsumView"
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(612, 364)})
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_enter_boss")
end

function DabaoEnterConsumView:ReleaseCallBack()
	if nil ~=  self.comsun_cell then
		self.comsun_cell:DeleteMe()
		self.comsun_cell = nil
	end

	self.tiky_item_id = nil
	self.enter_comsun = nil
	self.map_tip = nil
	self.consum_tip = nil
	self.ok_func = nil
end

function DabaoEnterConsumView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Common.AlertTitile
	self.node_list["btn_enter_dabao"].button:AddClickListener(BindTool.Bind1(self.OnBtnClick, self))
	XUI.AddClickEventListener(self.node_list.btn_cancle, BindTool.Bind1(self.Close, self))
	if nil ==  self.comsun_cell then
		self.comsun_cell = ItemCell.New(self.node_list["ph_cell"])
		self.comsun_cell:SetNeedItemGetWay(true)
	end
end

function DabaoEnterConsumView:ShowIndexCallBack()
	self:Flush()
end

function DabaoEnterConsumView:OnFlush()
    local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(self.tiky_id_seq)
    if IsEmptyTable(shop_cfg) then
        print_error("取不到商店配置 seq =",self.tiky_id_seq)
        return
    end
	self.tiky_item_id = shop_cfg.itemid
	local has_tiky_num = ItemWGData.Instance:GetItemNumInBagById(self.tiky_item_id)
	self.comsun_cell:SetData({item_id = self.tiky_item_id})
	local color = has_tiky_num >= self.enter_comsun and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
	self.comsun_cell:SetRightBottomColorText(has_tiky_num.."/"..self.enter_comsun, color)
	self.comsun_cell:SetRightBottomTextVisible(true)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.tiky_item_id)

    if item_cfg == nil then
        print_error("取不到物品配置 item_id =",self.tiky_item_id)
        return 
    end

    local bind_gold_num = RoleWGData.Instance.role_info.bind_gold
    local cost_text = Language.Common.GoldText
	local name = item_cfg.name
	local color_1 = ITEM_COLOR[item_cfg.color]
	color_1 = color_1 or COLOR3B.GREEN
	name = ToColorStr(name, color_1)
    self.node_list["lbl_consum_money"]:SetActive(has_tiky_num < self.enter_comsun)
    local need_num = self.enter_comsun - has_tiky_num
    
    if bind_gold_num >= need_num * shop_cfg.price then
        cost_text = Language.Common.BindGoldText
    end 

	self.node_list["lbl_consum_money"].tmp.text = string.format(self.consum_tip, cost_text, need_num * shop_cfg.price)
	local tips = Split(self.map_tip, "\n")
	self.node_list["text_dabao_comsun"].tmp.text = tips[1]
	self.node_list["consume_num"].tmp.text = string.format(tips[2], name, self.enter_comsun)
	--local item_price = ShopWGData.GetItemPrice(self.tiky_item_id)
end

function DabaoEnterConsumView:SetEnterBossComsunData(tiky_id_seq, enter_comsun, map_tip, consum_tip, ok_func)
	--self.tiky_item_id = tiky_item_id
	self.tiky_id_seq = tiky_id_seq
	self.enter_comsun = enter_comsun
	self.map_tip = map_tip
	self.consum_tip = consum_tip
	self.ok_func = ok_func
	self:Open()
end

function DabaoEnterConsumView:OnBtnClick()
	self.ok_func()
	self:Close()
end
