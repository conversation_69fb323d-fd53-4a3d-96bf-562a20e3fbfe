SecretAreaSceneLogic = SecretAreaSceneLogic or BaseClass(CommonFbLogic)

function SecretAreaSceneLogic:__init()

end

function SecretAreaSceneLogic:__delete()

end

function SecretAreaSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		-- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(false)
		MainuiWGCtrl.Instance:SetTaskContents(false)
		-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(true, Language.CountryMap.FBName)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)

		SecretAreaWGCtrl.Instance:OpenSecretAreaTaskView()
		SecretAreaWGCtrl.Instance:SendCorssSecretAreaOperate(SECRET_AREA_TYPE.XingTuMiJing_GetTask)
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end)

	local view = SecretAreaWGCtrl.Instance:GetTaskView()
	ViewManager.Instance:AddMainUIRightTopChangeList(view)
	ViewManager.Instance:AddMainUIFuPingChangeList(view)
end

function SecretAreaSceneLogic:Out()
	CommonFbLogic.Out(self)

	-- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(true)
	MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetOtherContents(false)
	SecretAreaWGCtrl.Instance:CloseSecretAreaTaskView()
	SecretAreaWGCtrl.Instance:CloseSecretAreaExpMdeicineView()
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)


	local view = SecretAreaWGCtrl.Instance:GetTaskView()
	ViewManager.Instance:RemoveMainUIRightTopChangeList(view)
	ViewManager.Instance:RemoveMainUIFuPingChangeList(view)
end