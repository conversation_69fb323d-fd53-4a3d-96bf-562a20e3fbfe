

function ActXianQiJieFengView:InitCapView(index)
	self:CapTimeCountDown()
	XUI.AddClickEventListener(self.node_list.cap_tip, BindTool.Bind(self.OnCapBtnTipClickHnadler,self))

	local theme_cfg = ActXianQiJieFengWGData.Instance:GetActivityThemeCfg(TabIndex.xianqi_jiefeng_cap)
	self.node_list.cap_tip_label.text.text = theme_cfg.rule_tip

	if index == TabIndex.xianqi_jiefeng_cap then
		self:InitRoleCap()
	elseif index == TabIndex.xianqi_jiefeng_cap2 then
		self:InitServerCap()
	elseif index == TabIndex.xianqi_jiefeng_cap3 then
		self:InitKanJia()
	end
end

function ActXianQiJieFengView:ReleaseCapView()
	if self.xqjf_cap_count_down and CountDownManager.Instance:HasCountDown(self.xqjf_cap_count_down) then
		CountDownManager.Instance:RemoveCountDown(self.xqjf_cap_count_down)
	end

	self:ReleaseRoleCap()
	self:ReleaseRoleServerCap()
	self:ReleaseKanJia()
end

function ActXianQiJieFengView:OnCapBtnTipClickHnadler()
	local role_tip = RuleTip.Instance
	if role_tip then
		local title,desc = ActXianQiJieFengWGData.Instance:GetActivityTip(TabIndex.xianqi_jiefeng_cap)
		if title ~= nil and desc ~= nil then
			role_tip:SetTitle(title)
			role_tip:SetContent(desc)
		end
	end
end

--有效时间倒计时
function ActXianQiJieFengView:CapTimeCountDown()
	self.xqjf_cap_count_down = "xqjf_cap_count_down"
	local invalid_time = ActXianQiJieFengWGData.Instance:GetActivityInValidTime(TabIndex.xianqi_jiefeng_cap)
	if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
		self.node_list.cap_time.text.text = TimeUtil.FormatSecondDHM2(invalid_time - TimeWGCtrl.Instance:GetServerTime())
		CountDownManager.Instance:AddCountDown(self.xqjf_cap_count_down, BindTool.Bind1(self.UpdateCapCountDown, self), BindTool.Bind1(self.OnCapComPlete, self), invalid_time, nil, 1)
	end
end

function ActXianQiJieFengView:UpdateCapCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.node_list.cap_time.text.text = TimeUtil.FormatSecondDHM2(valid_time)
	end
end

function ActXianQiJieFengView:OnCapComPlete()
	self.node_list.cap_time.text.text = Language.XianQiJieFengAct.ActivityStr1
end