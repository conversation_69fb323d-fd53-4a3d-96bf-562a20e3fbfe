require("game/mainui/camera_bubble_view")
require("game/mainui/main_efficienty_btn")
require("game/mainui/mainui_widgets/mainui_activity_btn")
require("game/shenji_notice/shenji_act_btn")
require("game/mainui/mainui_widgets/mainui_act_bubble_view")
require("game/mainui/new_server_recharge_tip")
require("game/sixiang_call/bailian_zhaohuan_main_btn")
require("game/tianshen/tianshen_linghe/linghe_draw_main_btn")
require("game/sixiang_call/shenji_tianci_main_btn")
require("game/god_purchase/god_purchase_tip")
require("game/level_recharge/level_recharge_tip")
require("game/longxi/dragon_king_token_tips")
-- require("game/dragon_temple/dragon_wild_buy_tip")
require("game/vientiane_tianyin/vientiane_tianyin_tip")
require("game/control_beasts/beasts_contract_main_btn")

MainUIView = MainUIView or BaseClass(SafeBaseView)

local ignore = {
	["daily_recharge"] = "daily_recharge",
	["counrty_map"] = "counrty_map",
}

local SHOW_SCALE = {
	[1] = 1,
	[2] = 0.6,
	[3] = 0.6,
	[4] = 0.6,
}

function MainUIView:__init()
	self.view_layer = UiLayer.MainUI
	self.is_safe_area_adapter = true
	self.can_do_fade = false
	self.cache_bottom_layout_ani_ison = nil
	self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
	
	self.config_tab = { -- 若改了这个config_tab的排序，必须修改MainUIView:SetFlyTaskIsHideMainUi
		{"TransparentMask", {0}},
		{"LowHpEffect", {0}},
		{"BottomJoystick",{0},},
		{"BottomPanel", {0},},
		{"TaskPanel", {0},},
		{"PlayerInfo", {0},},
		{"ChatInfoPanel", {0},},
		{"MainView", {0},},
		{"MainTopButtons", {0},},
		{"TargetInfoPanel", {0},},
		{"NormalSkillPanel", {0},},
		{"SkillTextPanel",{0},},
		{"GuildRedPanel",{0},},
		{"RevengePanel",{0},},
		{"MoveLeftPanel", {0},},
	}

	self:AddViewResource(0, "uis/view/main_ui_prefab", "TransparentMask")
	self:AddViewResource(0, "uis/view/main_ui_prefab", "LowHpEffect")
	self:AddViewResource(0, "uis/view/main_ui_prefab", "BottomJoystick")
	
	self:AddViewResource(0, "uis/view/main_ui_prefab", "TaskPanel")
	-- self:AddViewResource(0, "uis/view/main_ui_prefab", "CameraPanel")
	self:AddViewResource(0, "uis/view/main_ui_prefab", "PlayerInfo")
	self:AddViewResource(0, "uis/view/main_ui_prefab", "ChatInfoPanel")
	self:AddViewResource(0, "uis/view/main_ui_prefab", "MoveLeftPanel")
	self:AddViewResource(0, "uis/view/main_ui_prefab", "MainView")
	self:AddViewResource(0, "uis/view/main_ui_prefab", "MainTopButtons")
	self:AddViewResource(0, "uis/view/main_ui_prefab", "TargetInfoPanel")
	self:AddViewResource(0, "uis/view/main_ui_prefab", "NormalSkillPanel")
	-- self:AddViewResource(0, "uis/view/main_ui_prefab", "VIPStatePanel")
	self:AddViewResource(0, "uis/view/main_ui_prefab", "SkillTextPanel")
	self:AddViewResource(0, "uis/view/main_ui_prefab", "GuildRedPanel")
	self:AddViewResource(0, "uis/view/main_ui_prefab", "RevengePanel")
	self:AddViewResource(0, "uis/view/main_ui_prefab", "BottomPanel")
	self:AddViewResource(0, "uis/view/main_ui_prefab", "NormalSkillEffectShowPanel")

	self.active_close = false
	ActivityWGData.Instance:NotifyActChangeCallback(BindTool.Bind(self.ActivityChangeCallBack,self))

	self.remind_change = BindTool.Bind(self.RemindChangeCallBack, self)

	self:__initTask()
	self:__initSkill()
	self:__initActivity()
	self:__initChat()
	self:__initPlayer()

	self.strong_menu_view = MianuiStrongMenuView.New(GuideModuleName.MainUIStrongMenuView)
	self.strong_get_reward_view = MianuiStrongGetRewardView.New()
	self.main_ui_bubble_view = MainuiActBubbleView.New()
	self.main_ui_func_box_view = MainUIFunctionBoxView.New()
	self.call_back_list = {}
	self.call_back_list_in_loop = false
	self.is_auto_rotation_camera = XunLuStatus.None
	self.check_pack_space = BindTool.Bind(self.CheckPackageSpace, self)
	self.role_data_change_callback = BindTool.Bind1(self.RoleDataChangeCallback, self)

	self.main_icon_node_list = {}
	self.main_icon_node_list_top = {}
	self.icon_remind_list = {}
	self.icon_remind_list_num = {}
	self.icon_remind_list_top = {}
	self.icon_remind_list_top_num = {}
	self.vip_remind_list = {}
	self.vip_remind_list_num = {}
	self.all_use_red = {}
	self.unopen_red_fun = {}--未开启的功能红点表
	self.new_ser_rec_special_list = {}

	self.is_lock_camera_auto = false
	self.is_show_eq_target = false
	self.is_show_new_ser_tip = false
end

function MainUIView:__delete()
	if nil ~= self.strong_menu_view then
		self.strong_menu_view:DeleteMe()
		self.strong_menu_view = nil
	end

	if self.strong_get_reward_view then
		self.strong_get_reward_view:DeleteMe()
		self.strong_get_reward_view = nil
	end

	if self.main_ui_bubble_view ~= nil then
		self.main_ui_bubble_view:DeleteMe()
		self.main_ui_bubble_view = nil
	end

	if self.main_ui_func_box_view then
		self.main_ui_func_box_view:DeleteMe()
		self.main_ui_func_box_view = nil
	end

	if self.service_bubble_time_quest then
		GlobalTimerQuest:CancelQuest(self.service_bubble_time_quest)
		self.service_bubble_time_quest = nil
	end

	self:__deletePlayer()
	self:__deleteSkill()
	self:__deleteTask()

	self.is_lock_camera_auto = false
	self.view_root_rect = nil
end

function MainUIView:LoadCallBack()
	Runner.Instance:AddRunObj(self)
	self:SetTransparentMaskActive(false)
	self.toggle_menu = self.node_list["MenuIcon"]
	self.chat_toggle = self.node_list["ChatInfoPanel"]["ChatInfoPanel"]
	self.skill_toggle = self.node_list["NormalSkillPanelRoot"]
	self.top_button_toggle = self.node_list["TopButtonsPanel"]

	self.map_button = self.node_list["BelowPanel"]

	self.view_root_rect = self.root_node.transform:GetComponent(typeof(UnityEngine.RectTransform))

	-- 创建子View
	self.map_view = MainUIViewMap.New(self.map_button)
	self.map_view:NewInit(self.node_list["MapName"], self.node_list["MapPos"])
	self.main_ui_exp_view = MainUIViewExp.New(self.node_list.ExpInfo)
	self.main_ui_exp_view:SetEffectNode(self.node_list["ExpInfo_effect"])
	self.function_trailer = MainUIFunctiontrailer.New(self.node_list["FunctionTrailer"])
    self.guard_guide = MainUIGuardGuide.New(self.node_list["guard_guide_panel"])
    self.main_efficiency = MainUIEfficientyBtn.New(self.node_list["btn_main_efficiency"])

	self.node_list["fly_shoes_btn"].button:AddClickListener(BindTool.Bind(self.OnClickXunluFly, self))
	self.node_list["MapContent"].button:AddClickListener(BindTool.Bind1(self.OpenMap, self))
	self.toggle_menu.toggle:AddValueChangedListener(BindTool.Bind(self.OnToggleChange, self))

	self.power_change_view = TipsPowerChangeView.New(GuideModuleName.PowerChange)

	self.main_ui_exp_view:SetExpInfoTextNode(self.node_list.TextExp)

	self.change_head_icon = GlobalEventSystem:Bind(OtherEventType.CHANGE_HEAD_ICON, BindTool.Bind(self.ChangeHeadIcon, self))

	self:TaskLoadCallBack()
	self:ChatLoadCallBack()
	self:SkillLoadCallBack()
	self:PlayerLoadCallBack()
	self:ActivityLoadCallBack()
	self:TargetLoadCallBack()
	self:JoystickLoadCallBack()
    self:BottomLoadCallBack()
    self:InitRevenge()
    self.is_mainui_view_load = true
	self:NeedInit()
	self.mainui_room_info = MainUIViewRoom.New(self.node_list.RoomFrame)

	GlobalEventSystem:Fire(MainUIEventType.MAINUI_OPEN_COMLETE)  --mainui初始化完毕

	self:FulshChatView()

	local attr_t = {"capability", "avatar_key_big", "prof", "level", "hp", "max_hp", "vip_extra_role_exp", "attack_mode",
		"vip_level", "name", "guild_id", "guild_name", "special_appearance", "cash_point", "recharge_volume", "fight_mount_skill_level", "exp", "max_exp"}
	for k,v in pairs(attr_t) do
		self:RoleDataChangeCallback(v, RoleWGData.Instance.role_vo[v])
	end
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback, attr_t)

	--self:SetFreeFuhuoIcon()
	-- self:SetBossPrivrlegeIcon()
	self:FlushBossGodWarPrivrlege()
	self:SetBossExplodePrivrlegeIcon()
	self:SetBossKillEveryIcon()
	self:SetHundredEquipBtn()
	--self:SetBossMabiIcon()
	self.node_list["free_fuhuo_btn"].button:AddClickListener(BindTool.Bind(self.OnClickFreeFuHuoBtn, self))
	self.node_list["boss_privilege"].button:AddClickListener(BindTool.Bind(self.OnClickBossPrivilegeBtn, self)) --Boss特权
	self.node_list["boss_kill_every_btn"].button:AddClickListener(BindTool.Bind(self.OnClickBossKillEvery, self)) --Boss秒杀
	self.node_list["boss_godwar"].button:AddClickListener(BindTool.Bind(self.OnClickBossGodWarPrivilegeBtn, self)) --Boss战神特权
	self.node_list["boss_mustfull_privilege"].button:AddClickListener(BindTool.Bind(self.OnClickBossMustFallPrivilegeBtn, self)) --Boss 必爆
	self.node_list["btn_boss_mabi"].button:AddClickListener(BindTool.Bind(self.OnClickBossMaBiBtn, self)) --Boss 麻痹
	self.node_list["btn_redpacket"].button:AddClickListener(BindTool.Bind(self.OnClickOpenRedPacket, self))
	self.node_list["BtnVipService"].button:AddClickListener(BindTool.Bind(self.OnClickOpenVipServiceWindow, self))
	self.node_list["btn_to_pw"].button:AddClickListener(BindTool.Bind(self.OnClickToPositionalWarfareView, self))
	self.node_list["btn_honorhall_reward"].button:AddClickListener(BindTool.Bind(self.OnClickToHonorhallRewardView, self))
	self.node_list["btn_honorhall_shop"].button:AddClickListener(BindTool.Bind(self.OnClickToHonorhallShopView, self))
	self.node_list["kfkz_paimai_root"].button:AddClickListener(BindTool.Bind(self.OnClickOpenKFKZPaiMai, self))
	self.node_list["btn_kfkz_rank_reward"].button:AddClickListener(BindTool.Bind(self.OnClickOpenKFKZRankReward, self))
	self.node_list["btn_kfkz_challenge_progress"].button:AddClickListener(BindTool.Bind(self.OnClickOpenKFKZChallenge, self))
	self.node_list["btn_exp_guide"].button:AddClickListener(BindTool.Bind(self.onCLickExpGuideBtn, self))
	self.node_list["btn_privilege_collection_shtq"].button:AddClickListener(BindTool.Bind(self.OnClickPrivilegeCollectionSHTQBtn, self))
	self.node_list["hundred_equip_btn"].button:AddClickListener(BindTool.Bind(self.OnClickHundredEquip, self))
	self.node_list["he_open_up_btn"].button:AddClickListener(BindTool.Bind(self.OnClickHeOpenUpBtn, self))
	self.node_list["wangqi_skill_trigger"].button:AddClickListener(BindTool.Bind(self.OnClickSkillWangQi, self))
	FunOpen.Instance:RegisterFunUi(FunName.WangqiView, self.node_list["wangqi_skill_trigger"])  
	self.eh_load_quit = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind1(self.OnSceneLoadingQuite, self))
	self.main_role_xunlu = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, BindTool.Bind(self.OnMainRoleAutoXunluChange, self))
	self.day_pass = GlobalEventSystem:Bind(OtherEventType.PASS_DAY, BindTool.Bind(self.OnDayPass, self))
	self.chat_change_event = GlobalEventSystem:Bind(MainUIEventType.CHAT_CHANGE, BindTool.Bind1(self.FulshChatView, self))
	self.cg_event = GlobalEventSystem:Bind(ObjectEventType.CG_EVENT_END,BindTool.Bind(self.PlayCGEnd,self))
	self.real_recharge_event = GlobalEventSystem:Bind(ROLE_REAL_RECHARGE_NUM_CHANGE.REAL_RECHARGE, BindTool.Bind(self.RealRechargeChange, self))
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.MainUIView, self.get_guide_ui_event)
	RemindManager.Instance:Fire(RemindName.Shop)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.check_pack_space)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.check_pack_space, true)

	self.sundry_red_event = BindTool.Bind(self.FunOpenReBindRed, self)
	FunOpen.Instance:NotifyFunOpen(self.sundry_red_event)

	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	if main_role_vo.is_real_location == 1 then
		Location.Instance:UseRealLoaction()    --如果使用真实地址，则请求一次真实地址
	end
	self:FlushKfPKRenPoint()

	self.effect_root = self.node_list["Effect_Root"]

	self:TextWarmUp()

	XiaoGuiWGData.Instance:CheckLoginFirstOpenView()

	local obj = ResMgr:Instantiate(SafeBaseView.GetBaseViewParentTemplate(), true, GameObject.Find("GameRoot/UILayer").transform)
	obj.name = "SnapShotBackground"
	self.SnapShotBackground_obj = obj
	self:SetSnapShotBackgroundShow(true)

    local enable = self:GetIsEnableShow(Scene.Instance:GetSceneType())
    self:SetCanShowGuajiBtn(enable)

	self:FlushFlyShoesBtn()
	self:FlushCalendar()
	self:FlushSitShow()
	self:SetObservationState()
    self:Flush1V1Btn()
    self:FlushVipServiceBtn() --专属vip客服窗口
	self:FlushKFKZBtn()

	self:UpdateExpGuideBtn()
	self:UpdatePrivilegeCollectionShtqBtn()
	ViewManager.Instance:OpenWaitScreenShotView()
end


function MainUIView:ReleaseCallBack()
	self:Player__ReleaseCallBack()
	self:Task__ReleaseCallBack()
	self:Activity__ReleaseCallBack()
	self:Chat__ReleaseCallBack()
	self:Joystick__ReleaseCallBack()
	self:Target__ReleaseCallBack()
	self:Skill__ReleaseCallBack()
	self:Bottom__ReleaseCallBack()
	self:PlayerCloseCallBack()
	self:DeleteListRevenge()

	RemindManager.Instance:UnBind(self.remind_change)
	FunOpen.Instance:UnNotifyFunOpen(self.sundry_red_event)
	Runner.Instance:RemoveRunObj(self)
	RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)

	if self.SnapShotBackground_obj then
		ResMgr:Destroy(self.SnapShotBackground_obj)
		self.SnapShotBackground_obj = nil
	end

	self.toggle_menu = nil
	self.chat_toggle = nil
	self.Unlock = nil
	self.skill_toggle = nil
	self.top_button_toggle = nil
	self.map_button = nil
	self.strong_button = nil
	self.ui_clear_zdepth_img = nil
	self.is_mainui_view_load = false
	if self.map_view ~= nil then
		self.map_view:DeleteMe()
		self.map_view = nil
	end

	if nil ~= self.mainui_room_info then
		self.mainui_room_info:DeleteMe()
		self.mainui_room_info = nil
	end
	if self.main_ui_exp_view then
		self.main_ui_exp_view:DeleteMe()
		self.main_ui_exp_view = nil
	end
	if self.function_trailer then
		self.function_trailer:DeleteMe()
		self.function_trailer = nil
	end

	if self.guard_guide then
		self.guard_guide:DeleteMe()
		self.guard_guide = nil
	end

    if self.main_efficiency then
		self.main_efficiency:DeleteMe()
		self.main_efficiency = nil
	end

	if self.power_change_view then
		self.power_change_view:DeleteMe()
		self.power_change_view = nil
	end

	if self.assist_alert then
		self.assist_alert:DeleteMe()
		self.assist_alert = nil
	end

	if self.new_ser_rec_tip then
		self.new_ser_rec_tip:DeleteMe()
		self.new_ser_rec_tip = nil
	end 

	if self.god_purchase_tip then
		self.god_purchase_tip:DeleteMe()
		self.god_purchase_tip = nil
	end

	if self.king_token_tip then
		self.king_token_tip:DeleteMe()
		self.king_token_tip = nil
	end

	if self.vientiane_tianyin_tip then
		self.vientiane_tianyin_tip:DeleteMe()
		self.vientiane_tianyin_tip = nil
	end

	-- if self.wild_buy_tip then
	-- 	self.wild_buy_tip:DeleteMe()
	-- 	self.wild_buy_tip = nil
	-- end

	if self.level_recharge_tip then
		self.level_recharge_tip:DeleteMe()
		self.level_recharge_tip = nil
	end

	if nil ~= self.eh_load_quit then
		GlobalEventSystem:UnBind(self.eh_load_quit)
		self.eh_load_quit = nil
	end

	if nil ~= self.day_pass then
		GlobalEventSystem:UnBind(self.day_pass)
		self.day_pass = nil
	end

	if nil ~= self.chat_change_event then
		GlobalEventSystem:UnBind(self.chat_change_event)
		self.chat_change_event = nil
	end

	if nil ~= self.main_role_xunlu then
		GlobalEventSystem:UnBind(self.main_role_xunlu)
		self.main_role_xunlu = nil
	end

	if self.check_pack_space then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.check_pack_space)
		self.check_pack_space = nil
	end

	if self.cg_event then
		GlobalEventSystem:UnBind(self.cg_event)
		self.cg_event = nil
	end

	if self.real_recharge_event then
		GlobalEventSystem:UnBind(self.real_recharge_event)
		self.real_recharge_event = nil
	end

	self.call_back_list = {}
	-- self.auto_task_timer = nil
	if CountDownManager.Instance:HasCountDown("vipty_end_countdown") then
		CountDownManager.Instance:RemoveCountDown("vipcard_end_countdown")
	end

	self:RemoveKFKZPaiMaiCountDown()
	self:RemoveKFKZScoringCountDown()
	self.kfkz_seq_now_time = nil
	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.MainUIView, self.get_guide_ui_event)
	self.get_guide_ui_event = nil
	self:ResetSkillWangQiStatus()
end

local toggle_cacha = {}
function MainUIView:Update(now_time, elapse_time)
	self:UpdateChat(now_time, elapse_time)
	if next(toggle_cacha) and self.root_node and self.root_node.activeInHierarchy then
		for k,v in pairs(toggle_cacha) do
			if k.gameObject.activeInHierarchy then
				k.toggle.isOn = v
				toggle_cacha[k] = nil
			end
		end
	end

	if UnityEngine.Input.GetMouseButtonDown(0) then
		GlobalEventSystem:Fire(LayerEventType.MOUSE_BUTTON_DOWN, UnityEngine.Input.mousePosition)
	end
	if UnityEngine.Input.GetMouseButtonUp(0) then
		GlobalEventSystem:Fire(LayerEventType.MOUSE_BUTTON_UP, UnityEngine.Input.mousePosition)
	end

	self:UpdateRevenge()
	self:CheckSpecialHurtPanel()
end

function MainUIView:OnToggleChange(isOn)
	self.bottom_toggle_state = isOn
	if SettingWGCtrl.Instance then
		SettingWGCtrl.Instance:SetMainToggleFlag(isOn)
	end

	-- 一些界面会被关闭
	for k,v in pairs(OTHER_VIEW_OPEN_CLOSE_THESE_VIEW_LIST) do
		if ViewManager.Instance:IsOpen(k) then
			ViewManager.Instance:Close(k)
		end
	end

	-- self.node_list.btn_fp_close:CustomSetActive(isOn)

	self:DoFPBottomPanelBgAni(isOn)
	self:CheckBottomLayoutAni(isOn)
	GlobalEventSystem:Fire(MainUIEventType.MAIN_MENU_ICON_CHANGE, isOn)
end

-- 策划要求背景在按钮到达位置完整显示后，再出现，然后一起消失
function MainUIView:DoFPBottomPanelBgAni(isOn)
	if isOn then
		RectTransform.SetAnchoredPositionXY(self.node_list.fp_bom_right_bg.rect, -1000, 0)
		RectTransform.SetAnchoredPositionXY(self.node_list.fp_bom_left_bg.rect, 1000, 0)
		self.node_list.fp_bom_right_bg.canvas_group.alpha = 0
		self.node_list.fp_bom_left_bg.canvas_group.alpha = 0

		self.node_list.fp_bom_right_bg.rect:DOAnchorPosX(-168, 0.3)
		self.node_list.fp_bom_left_bg.rect:DOAnchorPosX(6, 0.3)

		self.node_list.fp_bom_right_bg.canvas_group:DoAlpha(0, 1, 0.5)
		self.node_list.fp_bom_left_bg.canvas_group:DoAlpha(0, 1, 0.5)
	end

	-- ReDelayCall(self, function ()
	-- 	if isOn then
	-- 		if self.node_list then
	-- 			self.node_list.fp_bom_right_bg.rect:DOAnchorPosX(-168, 0.3)
	-- 			self.node_list.fp_bom_left_bg.rect:DOAnchorPosX(167, 0.3)

	-- 			self.node_list.fp_bom_right_bg.canvas_group:DoAlpha(0, 1, 0.5)
	-- 			self.node_list.fp_bom_left_bg.canvas_group:DoAlpha(0, 1, 0.5)
	-- 		end
	-- 	end
	-- end, 0.3, "FPBottomPanelBgAni")
end

function MainUIView:CheckBottomLayoutAni(isOn)
	if self.cache_bottom_layout_ani_ison == isOn then
		return
	end

	self:CheckChangeBtnIsRemind()
	self.cache_bottom_layout_ani_ison = isOn
	self:SetFuPingIsHideMainUi(isOn)
	self.node_list["PlayerButtons"].animator:SetBool("fold", isOn)
	self.node_list["Menu"].animator:SetBool("fold", isOn)
end

function MainUIView:FlushGodPurchaseTip(param_t)
	local is_show_tip = GodPurchaseWGData.Instance:GetIsShowTip()
	local scene_type = Scene.Instance:GetSceneType()					-- 普通场景才显示
	self.node_list.god_purchase_tips_root:SetActive(false)

	if IS_AUDIT_VERSION or (not is_show_tip) or scene_type ~= SceneType.Common then
		if self.god_purchase_tip then
			self.god_purchase_tip:DeleteMe()
			self.god_purchase_tip = nil
			self:FlushTipShowPos()
		end
		return
	end

	if not self.god_purchase_tip then
		self.god_purchase_tip = GodPurchaseTip.New()
		self.god_purchase_tip:DoLoad(self.node_list.god_purchase_tips_root)
	end

	self.node_list.god_purchase_tips_root:SetActive(true)
	self:FlushTipShowPos()
end

function MainUIView:FlushVienTianeTianYinTip(param_t)
	local is_show_tip = VientianeTianyinData.Instance:GetIsShowTip()
	local scene_type = Scene.Instance:GetSceneType()					-- 普通场景才显示
	self.node_list.vientiane_tianyin_tips_root:SetActive(false)

	if IS_AUDIT_VERSION or (not is_show_tip) or scene_type ~= SceneType.Common then
		if self.vientiane_tianyin_tip then
			self.vientiane_tianyin_tip:DeleteMe()
			self.vientiane_tianyin_tip = nil
			self:FlushTipShowPos()
		end
		return
	end

	if not self.vientiane_tianyin_tip then
		self.vientiane_tianyin_tip = VientianeTianyinTip.New()
		self.vientiane_tianyin_tip:DoLoad(self.node_list.vientiane_tianyin_tips_root)
	end

	self.node_list.vientiane_tianyin_tips_root:SetActive(true)
	self:FlushTipShowPos()
end

function MainUIView:FlushDragonKingTokenTip()
	local is_show_tip = LongXiWGData.Instance:GetIsShowTip()
	local scene_type = Scene.Instance:GetSceneType()					-- 普通场景才显示
	self.node_list.dragonking_token_tips_root:SetActive(false)

	if IS_AUDIT_VERSION or (not is_show_tip) or scene_type ~= SceneType.Common then
		if self.king_token_tip then
			self.king_token_tip:DeleteMe()
			self.king_token_tip = nil
			self:FlushTipShowPos()
		end
		return
	end

	if not self.king_token_tip then
		self.king_token_tip = DragonKingTokenTip.New()
		self.king_token_tip:DoLoad(self.node_list.dragonking_token_tips_root)
	end

	self.node_list.dragonking_token_tips_root:SetActive(true)
	self:FlushTipShowPos()
end

function MainUIView:FlushDragonWildBuyTip()
	local is_show_tip = DragonTempleWGData.Instance:GetIsShowTip()
	local scene_type = Scene.Instance:GetSceneType()					-- 普通场景才显示
	self.node_list.dragon_wild_buy_tips_root:SetActive(false)

	if IS_AUDIT_VERSION or (not is_show_tip) or scene_type ~= SceneType.Common then
		if self.wild_buy_tip then
			self.wild_buy_tip:DeleteMe()
			self.wild_buy_tip = nil
			self:FlushTipShowPos()
		end
		return
	end
	if not self.wild_buy_tip then
		self.wild_buy_tip = DragonWildBuyTip.New()
		self.wild_buy_tip:DoLoad(self.node_list.dragon_wild_buy_tips_root)
		self.wild_buy_tip:Flush()
	else
		self.wild_buy_tip:Flush()
	end
	
	self.node_list.dragon_wild_buy_tips_root:SetActive(true)
	self:FlushTipShowPos()
end

function MainUIView:FlushDiscountPurchaseTip()
	local is_show_tip = DiscountPurchaseWGData.Instance:GetIsShowTip()
	local scene_type = Scene.Instance:GetSceneType()					-- 普通场景才显示
	self.node_list.discount_purchase_tips_root:SetActive(false)

	if IS_AUDIT_VERSION or (not is_show_tip) or scene_type ~= SceneType.Common then
		if self.discount_purchase_tip then
			self.discount_purchase_tip:DeleteMe()
			self.discount_purchase_tip = nil
			self:FlushTipShowPos()
		end
		return
	end

	if not self.discount_purchase_tip then
		self.discount_purchase_tip = DiscountPurchaseTip.New()
		self.discount_purchase_tip:DoLoad(self.node_list.discount_purchase_tips_root)
	end

	self.node_list.discount_purchase_tips_root:SetActive(true)
	self:FlushTipShowPos()
end

--特殊處理神藏和令牌同时出现
function MainUIView:FlushTipShowPos()
	local god_tip = self.god_purchase_tip
	local token_tip = self.king_token_tip
	-- local wilebuy_tip_state = self.wild_buy_tip
	local vientiane_tip = self.vientiane_tianyin_tip
	local discount_tip = self.discount_purchase_tip
	local tip_list = {}
	local fun = function (tip)
		if not tip then
			return
		end
		table.insert(tip_list,tip)
	end

	fun(god_tip)
	fun(token_tip)
	-- fun(wilebuy_tip)
	fun(vientiane_tip)
	fun(discount_tip)

	for i=1,#tip_list do
		local tip = tip_list[i]
		if tip and tip.SetParentActive then
			tip:SetParentActive(i<=2)
		end
	end

	local scale = SHOW_SCALE[#tip_list]
	self.node_list["god_purchase_tips_root"].transform.localScale = Vector3(scale, scale, scale)
	self.node_list["dragonking_token_tips_root"].transform.localScale = Vector3(scale, scale, scale)
	-- self.node_list["dragon_wild_buy_tips_root"].transform.localScale = Vector3(scale, scale, scale)
	self.node_list["vientiane_tianyin_tips_root"].transform.localScale = Vector3(scale, scale, scale)
	self.node_list["discount_purchase_tips_root"].transform.localScale = Vector3(scale, scale, scale)
end

--尝试打开首充优势
function MainUIView:TryOpenNewSerRecTip()
	self.is_show_new_ser_tip = false

	-- 配置开关控制
	if not MainuiWGData.Instance:GetFirstRechargeTipState() then
		if self.new_ser_rec_tip then
			self.new_ser_rec_tip:SetVisible(false)
		end
		return
	end

	local is_show_eq_target = self.is_show_eq_target 					-- 装备目标显示的时候隐藏
	local scene_type = Scene.Instance:GetSceneType()					-- 普通场景才显示
	local recharge_num = RechargeWGData.Instance:GetHistoryRecharge()		-- 充过钱数
	local is_open = FunOpen.Instance:GetFunIsOpened(FunName.ShouChong)	-- 首充是否开启
	local all_rec = ServerActivityWGData.Instance:IsAllGradeRecharge()	-- 所有首充档位都充值过
	local has_remind = ServerActivityWGData.Instance:GetSCXCRemind()		-- 有可以领的奖励

	if IS_AUDIT_VERSION or is_show_eq_target or scene_type ~= SceneType.Common or (recharge_num > 0 and is_open) or (all_rec and not is_open and has_remind == 0) then
		if self.new_ser_rec_tip then
			self.new_ser_rec_tip:SetVisible(false)
		end
		return
	end

	if not self.new_ser_rec_tip then
		self.new_ser_rec_tip = NewServerRechargeTip.New()
		self.new_ser_rec_tip:DoLoad(self.node_list.new_server_recharge_root)

	end

	self.new_ser_rec_tip:SetVisible(true)
	self.new_ser_rec_tip:Flush()
	self.is_show_new_ser_tip = true
end

function MainUIView:FlushNewSerRecTip(param_t)
	-- 2022/3/10 策划新增开关控制 bt要用首充的那个入口，国服和繁体要用这个等级直购的入口
	if not MainuiWGData.Instance:GetFirstRechargeTipState() then
		if self.new_ser_rec_tip then
			self.new_ser_rec_tip:SetVisible(false)
		end
		return
	end

	local is_show_eq_target = self.is_show_eq_target 					-- 装备目标显示的时候隐藏
	local scene_type = Scene.Instance:GetSceneType()					-- 普通场景才显示
	local recharge_num = RechargeWGData.Instance:GetHistoryRecharge()		-- 充过钱数
	local is_open = FunOpen.Instance:GetFunIsOpened(FunName.ShouChong)	-- 首充是否开启
	local all_rec = ServerActivityWGData.Instance:IsAllGradeRecharge()	-- 所有首充档位都充值过
	local has_remind = ServerActivityWGData.Instance:GetSCXCRemind()		-- 有可以领的奖励

	if IS_AUDIT_VERSION or is_show_eq_target or scene_type ~= SceneType.Common or (recharge_num > 0 and is_open) or (all_rec and not is_open and has_remind == 0) then
		if self.new_ser_rec_tip then
			self.new_ser_rec_tip:SetVisible(false)
		end
		return
	end
end

function MainUIView:IsShowNewRecTip()
	return self.is_show_new_ser_tip
end

-- function MainUIView:SetFreeFuhuoIcon()
-- 	local scene_cfg = Scene.Instance:GetCurFbSceneCfg()
-- 	local active = scene_cfg and scene_cfg.is_show_free_fuhuo == 1 and FunOpen.Instance:GetFunIsOpened(FunName.VIPTouZi)
-- 	self.node_list.free_fuhuo_root:SetActive(active)
-- 	self:FlushFreeFuhuoIcon()
-- end

-- function MainUIView:FlushFreeFuhuoIcon()
-- 	self.node_list.free_fuhuo_count.text.text = ""
-- 	self.node_list.free_fuhuo_active.text.text = ""
-- 	self.node_list.free_fuhuo_no_count.text.text = ""
-- 	local can_active = RechargeWGData.Instance:CanActiveTZCard(INVEST_CARD_TYPE.StorehouseCard)
-- 	if can_active then
-- 		self.node_list.free_fuhuo_active.text.text = Language.Mainui.NoFreeFuHuoActive
-- 	else
-- 		local count = RechargeWGData.Instance:GetFreeFuHuoCount()
-- 		if count > 0 then
-- 			self.node_list.free_fuhuo_count.text.text = string.format(Language.Mainui.FreeFuHuoCount, count)
-- 		else
-- 			self.node_list.free_fuhuo_no_count.text.text = Language.Mainui.NoFreeFuHuoCount
-- 		end
-- 	end

-- 	--self.node_list.free_fuhuo_noact:SetActive(not can_active)
-- end

-- 等级直购
function MainUIView:FlushLevelRechargeTips(param_t)
	local is_show_tip = LevelRechargeWGData.Instance:GetIsShowTip()
	local scene_type = Scene.Instance:GetSceneType()

	if IS_AUDIT_VERSION or (not is_show_tip) or scene_type ~= SceneType.Common then
		if self.level_recharge_tip then
			self.level_recharge_tip:SetVisible(false)
		end

		return
	end

	if not self.level_recharge_tip then
		self.level_recharge_tip = LevelRechargeTip.New()
		self.level_recharge_tip:DoLoad(self.node_list.task_btn_trans)
	else
		self.level_recharge_tip:Flush()
	end

	self.level_recharge_tip:SetVisible(true)
end

function MainUIView:FlushBossPrivrlegeIcon(scene_type)
	local is_open = BossPrivilegeWGData.Instance:GetActivateState() --是否开启
	-- local is_times = BossPrivilegeWGData.Instance:GetBossFbTimesCfg(scene_type) --当前场景剩余次数
	local is_active = BossPrivilegeWGData.Instance:GetBossPrivilegeIsActive() --是否激活过
	-- local is_shake
	-- --未开启，有次数或者未激活
	-- if is_open < 1 or (not is_active) then
	-- 	is_shake = true
	-- 	if is_times < 1 then
	-- 		is_shake = false
	-- 	end
	-- --已激活或者没有次数
	-- elseif is_open > 0 or is_times < 1 then
	-- 	is_shake = false
	-- end
	-- self.node_list["boss_privilege_icon_root"].animator:SetBool("is_shake", is_shake)
	self.node_list["boss_privilege_noact"]:SetActive(not is_active) --未激活
	self.node_list["boss_privilege_text_bg"]:SetActive(is_open < 1 and is_active) --激活了未开启
	self.node_list["boss_privilege_text_open"]:SetActive(is_open > 0 and is_active)
	self.node_list["boss_privilege_off"]:SetActive(is_open < 1 or (not is_active))
	self.node_list["boss_privilege_on"]:SetActive(is_open > 0 and is_active)
end

-- 刷新战神特权
function MainUIView:FlushBossGodWarPrivrlege()
	local scene_cfg = Scene.Instance:GetCurFbSceneCfg()
	local god_war_data = BossGodWarWGData.Instance:GetPrivilegeInfo()
	--已经购买也开启
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.boss_godwar) or (god_war_data and god_war_data.level > -1)
	self.node_list["boss_godwar_root"]:SetActive(is_open and scene_cfg.scene_type == SceneType.VIP_BOSS and god_war_data ~= nil)

	if scene_cfg.scene_type ~= SceneType.VIP_BOSS or (not god_war_data) then
		return
	end

	local is_open = god_war_data.open_status
	local total_count = god_war_data.total_count or 0 	--当前场景剩余次数
	local is_active = god_war_data.level ~= -1 		--是否激活过
	local is_fetch_daily_rewards = god_war_data.is_fetch_daily_rewards or 0
	local is_shake = true

	if is_active then		-- 激活有次数
		is_shake = true
		if total_count < 1 then
			is_shake = false
		end
	end

	-- self.node_list["boss_godwar_icon_root"].animator:SetBool("is_shake", is_shake)
	self.node_list["boss_godwar_noact"]:SetActive(not is_active) 						--未激活
	self.node_list["boss_godwar_text_bg"]:SetActive(is_open == 0 and is_active) 		--激活了未开启
	self.node_list["boss_godwar_text_open"]:SetActive(is_open == 1 and is_active)		--激活了且开启了
	self.node_list["boss_godwar_off"]:SetActive(not is_active)
	self.node_list["boss_godwar_on"]:SetActive(is_active)
	self.node_list["boss_godwar_red"]:SetActive(is_active and is_fetch_daily_rewards == 0)
end

function MainUIView:SetBossExplodePrivrlegeIcon()
	local scene_cfg = Scene.Instance:GetCurFbSceneCfg()
	local active = scene_cfg and scene_cfg.is_show_mush_fall == 1 or false
	local is_open = FunOpen.Instance:GetFunIsOpened(FunName.BossMustFallPrivilegeView)
	local is_show = active and is_open
	self.node_list["boss_mustfull_privilege_root"]:CustomSetActive(is_show)

	if is_show then
		self:FlushBossExplodePrivrlegeIcon(scene_cfg.scene_type)
	end
end

function MainUIView:FlushBossExplodePrivrlegeIcon(scene_type)
	local fun_is_open = FunOpen.Instance:GetFunIsOpened(FunName.BossMustFallPrivilegeView)
	local pri_is_open = BossMustFallPrivilegeWGData.Instance:GetPrivilegeState() == 1
	self.node_list.boss_mustfull_privilege_off:CustomSetActive(not (fun_is_open and pri_is_open))
	self.node_list.boss_mustfull_privilege_on:CustomSetActive(fun_is_open and pri_is_open)
	local str_index = fun_is_open and (pri_is_open and 2 or 1) or 0
	self.node_list.boss_mustfull_privilege_state_text.text.text = Language.BossMustFallPrivilege.MustFallPeivilegeStateStr[str_index]
end

--boss特权按钮
function MainUIView:SetBossPrivrlegeIcon()
	local scene_cfg = Scene.Instance:GetCurFbSceneCfg()
	local active = scene_cfg and scene_cfg.is_show_againdrop == 1 or false
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.boss_zaibaoyici)
	self.node_list["boss_privilege_root"]:SetActive(is_open and active)
	self:FlushBossPrivrlegeIcon(scene_cfg.scene_type)

	-- VipBoss增加战神特权
	self:FlushBossGodWarPrivrlege()
end

function MainUIView:SetBossMabiIcon()
	local scene_cfg = Scene.Instance:GetCurFbSceneCfg()
	local active = scene_cfg and scene_cfg.is_show_mabi == 1
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.boss_mabi_skill)
	self.node_list.btn_boss_mabi:CustomSetActive(is_open and active)
	self.node_list.desc_boss_mabi_exprience.text.text = Language.PersonMabiBoss.FBTopIconExprienceDesc
	self.node_list.img_boss_mabi_exprience:CustomSetActive(scene_cfg.scene_type == SceneType.PERSON_MABI_BOSS_DISPLAY)
end

function MainUIView:SetBossKillEveryIcon()
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.boss_kill_every)
	local scene_cfg = Scene.Instance:GetCurFbSceneCfg()
	local show_icon = scene_cfg and scene_cfg.is_show_boss_kill == 1
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local boss_kill_every_other_cfg = BossKillEveryWGData.Instance:GetOtherCfg()
	local open_boss_kill_every_level = boss_kill_every_other_cfg.open_level
	self.node_list["boss_kill_every_root"]:SetActive(is_open and show_icon and role_level >= open_boss_kill_every_level)
	if show_icon and role_level >= open_boss_kill_every_level then
		local cur_level = BossKillEveryWGData.Instance:GetCurLevel()
		self.node_list["boss_kill_every_open"]:SetActive(cur_level > 0)
		self.node_list["boss_kill_every_not_act"]:SetActive(cur_level <= 0)
		local day_kill_num = BossKillEveryWGData.Instance:GetCurDayKillCount()
    	local cur_level_cfg = BossKillEveryWGData.Instance:GetCurLevelCfg(cur_level)
		if cur_level_cfg then
			local show_count = BossKillEveryWGData.Instance:GetDayExtCount()
			local ext_emp_count = BossKillEveryWGData.Instance:GetDayExtEmpCount()
			local remain_count = cur_level_cfg.everyday_max_kill_count + show_count + ext_emp_count - day_kill_num
			self.node_list.boss_kill_every_num.text.text = string.format(Language.BossKillEvery.MiaoShaBossNum, remain_count)
		end
	end
end

-- 百倍爆装按钮
function MainUIView:SetHundredEquipBtn()
	local scene_cfg = Scene.Instance:GetCurFbSceneCfg()
	local active = scene_cfg and scene_cfg.is_show_hundred_equip == 1 or false
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.HundredEquipView)
	--self.node_list["hundred_equip_btn"]:SetActive(is_open and active)
	self.node_list["hundred_equip_content"]:SetActive(is_open and active)

	--策划 林海锋 非要搞特殊处理，说不听的.
	self.node_list["hundred_equip_content"].rect.anchoredPosition = scene_cfg.scene_type == SceneType.HIGH_TEAM_EQUIP_FB and  Vector3(0, 30, 0) or Vector3.zero

	self:FlushHundreaEquipBtn(scene_cfg.scene_type)
end

function MainUIView:FlushHundreaEquipBtn(scene_type)
	-- 普通升级倍率
    local level = HundredEquipWGData.Instance:GetLevelValue()
    local droptimes_data = HundredEquipWGData.Instance:GetDropTimesListCfg(level)
	local nor_rate = (droptimes_data[scene_type] or 0) / 100		-- 倍数

	-- 直购 倍率特权
	local buy_level = HundredEquipWGData.Instance:GetRmbBuyLevel()
    local level_cfg = HundredEquipWGData.Instance:GetRmbBuyCfg(buy_level) or {}
	local add_rate = ((level_cfg.drop_time_add_show or 0) / 100) * nor_rate	-- 额外倍数

	--守护特权.
	local shtq_add_value = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeSHTQHundredfoldDropPro()

	local cur_rate = nor_rate + add_rate + (shtq_add_value / 100)
	self.node_list.hundred_equip_rate.text.text = cur_rate == 0 and 1 or cur_rate

	local next_droptimes_data = HundredEquipWGData.Instance:GetDropTimesListCfg(level + 1)
	if next_droptimes_data then
		local next_rate = (next_droptimes_data[scene_type] or 0) / 100		-- 倍数
		local next_add_rate = ((level_cfg.drop_time_add_show or 0) / 100) * next_rate	-- 额外倍数
		next_rate = next_rate + next_add_rate + shtq_add_value / 100
		self.node_list.hundred_equip_next_rate.text.text = next_rate * 100
	end

	local is_empty = IsEmptyTable(next_droptimes_data)
	self.node_list.hundred_equip_next_rate:SetActive(not is_empty)
	self.node_list.next_rate_img:SetActive(not is_empty)
	self.node_list.hundred_equip_max_rate_img:SetActive(is_empty)

    local exp_per_time = HundredEquipWGData.Instance:GetExpPerTimes()
    local next_task_data =  HundredEquipWGData.Instance:GetLevelCfgByLevel(level + 1)
    local cur_level_cfg = HundredEquipWGData.Instance:GetLevelCfgByLevel(level)

    local now_num = exp_per_time
    local target_num = next_task_data and (next_task_data.exp_per_times_limit or 0) or cur_level_cfg.exp_per_times_limit

	if next_task_data then
		self.node_list.he_text_ratio_exp_progress.tmp.text = string.format(Language.HundredEquip.ExpText, now_num, target_num)
		self.node_list.he_slider_ratio_level.slider.value =  now_num / target_num
	else
		self.node_list.he_text_ratio_exp_progress.tmp.text = Language.HundredEquip.ExpText2
		self.node_list.he_slider_ratio_level.slider.value = 1
	end

    self.node_list.he_text_now_ratio_level.tmp.text = level

	local level_up = HundredEquipWGData.Instance:GetTaskLevelUpRed()
    self.node_list.he_open_up_btn:SetActive(level_up)
end

function MainUIView:OnClickOpenRedPacket()
	if IsEmptyTable(self.all_use_red) then
		return
	end
	local data = self.all_use_red[1]
	if data.is_use == 1 then
		if data.is_guid_packet then
			WelfareWGCtrl.Instance:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_RECEIVE_GUILD,data.info_index)
			GuildWGCtrl.Instance:OpenGuildRedPacketView(data,1)
		else
			WelfareWGCtrl.Instance:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_RECEIVE_WORLD,data.info_index)
			GuildWGCtrl.Instance:OpenGuildRedPacketView(data,2)
		end
	else
		if data.type == SPECIAL_SEND_TYPE.GUILD_ANSWER then
			WelfareWGCtrl.Instance:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_GUILD_SYSTEM_DISTRIBUTE,data.info_index)
		else
			WelfareWGCtrl.Instance:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_DISTRIBUTE_SYSTEM,data.info_index)
		end
	end
end

function MainUIView:OnClickFreeFuHuoBtn()
	ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_month_card)
end

--打开boss特权激活
function MainUIView:OnClickBossPrivilegeBtn()
	ViewManager.Instance:Open(GuideModuleName.BossNewPrivilegeView, TabIndex.boss_zaibaoyici)
end

function MainUIView:OnClickBossKillEvery()
	ViewManager.Instance:Open(GuideModuleName.BossNewPrivilegeView, TabIndex.boss_kill_every)
end

--打开boss战神特权激活
function MainUIView:OnClickBossGodWarPrivilegeBtn()
	ViewManager.Instance:Open(GuideModuleName.BossNewPrivilegeView, TabIndex.boss_godwar)
end

--打开boss必爆 策划要求功能未开启也要能进界面
function MainUIView:OnClickBossMustFallPrivilegeBtn()
	-- ViewManager.Instance:Open(GuideModuleName.BossMustFallPrivilegeView)
	BossMustFallPrivilegeWGCtrl.Instance:OpenMustFallPrivilegeView()
end

-- 打开boss麻痹界面
function MainUIView:OnClickBossMaBiBtn()
	ViewManager.Instance:Open(GuideModuleName.BossNewPrivilegeView, TabIndex.boss_mabi_skill)
end

function MainUIView:OnClickToPositionalWarfareView()
	ViewManager.Instance:Open(GuideModuleName.PositionalWarfareView)
end

function MainUIView:SetToPositionalWaefareViewBtnState(is_show)
	if self.node_list.btn_to_pw_root then
		self.node_list.btn_to_pw_root:CustomSetActive(is_show)
	end
end

function MainUIView:OnClickToHonorhallRewardView()
	KuafuHonorhallWGCtrl.Instance:OpenRewardView()
end

function MainUIView:OnClickToHonorhallShopView()
	KuafuHonorhallWGCtrl.Instance:OpenShopView()
end

function MainUIView:SetToHonorhallRewardState(is_show)
	if self.node_list.honorhall_reward_root then
		self.node_list.honorhall_reward_root:CustomSetActive(is_show)
	end
end

function MainUIView:SetToHonorhallShopBtnState(is_show)
	if self.node_list.honorhall_shop_root then
		self.node_list.honorhall_shop_root:CustomSetActive(is_show)
	end
end

---------------------------------------- k跨服空战 Start----------------------------------------
-- 设置跨服空战按钮状态
function MainUIView:FlushKFKZBtn()
	local scene_type = Scene.Instance:GetSceneType()
	self.node_list.kfkz_paimai_root:CustomSetActive(scene_type == SceneType.CROSS_AIR_WAR)
	self.node_list.btn_kfkz_rank_reward:CustomSetActive(scene_type == SceneType.CROSS_AIR_WAR or scene_type == SceneType.BOSS_INVASION)
	self.node_list.btn_kfkz_challenge_progress:CustomSetActive(scene_type == SceneType.CROSS_AIR_WAR)
	self.node_list.kfkz_time_score_root:CustomSetActive(scene_type == SceneType.CROSS_AIR_WAR)
	self:FlushKFKZPaiMaiStatus()
	self:FlushKFKZScoreStatus()
end

-- 打开拍卖
function MainUIView:OnClickOpenKFKZPaiMai()
	local status = CrossAirWarWGData.Instance:GetAirWarSceneStatus() 
	local is_paimai_status = status == CROSS_AIR_WAR_AUCTION_STATUS.STATUS_AUCTION

	if is_paimai_status then
		CrossAirWarWGCtrl.Instance:OpenAirWarAuctionView()
	else
		-- 打开仓库
		CrossAirWarWGCtrl.Instance:OpenAirWarAuctionBagView()
	end
end

-- 打开当前排名奖励
function MainUIView:OnClickOpenKFKZRankReward()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.CROSS_AIR_WAR then
		CrossAirWarWGCtrl.Instance:OpenRewardPreview(true)
	elseif scene_type == SceneType.BOSS_INVASION then
		BOSSInvasionWGCtrl.Instance:OpenRankRewardView()
	end
end

-- 打开当前挑战进度
function MainUIView:OnClickOpenKFKZChallenge()
	CrossAirWarWGCtrl.Instance:OpenAirWarProgView()
end

-- 打开升级引导界面
function MainUIView:onCLickExpGuideBtn()
	ExpGuideWGCtrl.Instance:OpenView()
end

--点击守护特权按钮
function MainUIView:OnClickPrivilegeCollectionSHTQBtn()
	local cur_seq = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeSeq()
	local next_cfg = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeCfgBySeq(cur_seq + 1)

	if IsEmptyTable(next_cfg) then
		local cur_cfg = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeCfgBySeq(cur_seq)
		next_cfg = cur_cfg
	end

	local other_cfg = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeOther()
	local is_show_tips = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeSHTQMainTipsFlag()
	local can_up_level, can_up_baifenbi = PrivilegeCollectionWGData.Instance:SaveExpCanUpLevel(next_cfg.seq >= 0 and next_cfg.seq or 0)
	--shtq_exp_desc 气泡显示时，隐藏气泡.策划 林海峰 特殊需求.
	if is_show_tips then-- and can_up_level >= other_cfg.exp_open_level then
		PrivilegeCollectionWGData.Instance:SetGuardPrivilegeSHTQMainTipsFlag(false)
		self.node_list.shtq_exp_desc:SetActive(false)
	end
	ViewManager.Instance:Open(GuideModuleName.PrivilegeCollectionView, TabIndex.pri_col_shtq)
end

function MainUIView:OnClickHundredEquip()
	local level_up = HundredEquipWGData.Instance:GetTaskLevelUpRed()
	if level_up then
		ViewManager.Instance:Open(GuideModuleName.HundredEquipView, 0, "all", {open_param = "showAward"})
	else
		ViewManager.Instance:Open(GuideModuleName.HundredEquipView)
	end
end

function MainUIView:OnClickHeOpenUpBtn()
	ViewManager.Instance:Open(GuideModuleName.HundredEquipView, 0, "all", {open_param = "showAward"})
end

-- 打开望气
local WANGQI_COOL_TIME = 10
function MainUIView:OnClickSkillWangQi()
	if self.is_wangqi_skill_click then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Role.WangQiCoolStatus)
		return
	end

	local main_role = Scene.Instance:GetMainRole()

	if not main_role then
		return
	end

    local is_show_wangqi = true
	local logic = Scene.Instance:GetSceneLogic()
    if logic then
        if logic and logic.IsShowAransformationWangQi then
            is_show_wangqi = logic:IsShowAransformationWangQi()
        end
    end

	if (not is_show_wangqi) or main_role:IsXiuWeiBianShen() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Role.WangQiCannotUse)
		return
	end

	if main_role and main_role.draw_obj then
		Scene.Instance:ShowWangQi(main_role.draw_obj.root_transform, 1)
	end

	self.node_list.wangqi_skill_cd_mark:CustomSetActive(true)
	self.node_list.wangqi_skill_cd_text:CustomSetActive(true)
	self.node_list.wangqi_skill_cd_text.text.text = WANGQI_COOL_TIME
	self.node_list.wangqi_skill_cd_mark.image.fillAmount = 1
	self.is_wangqi_skill_click = true

	local function cool_func(elapse_time, total_time)
		if self.node_list then
			self.node_list.wangqi_skill_cd_text.text.text = string.format("%.1f", (total_time - elapse_time))
			local fill_amount = 1 - elapse_time / total_time
			self.node_list.wangqi_skill_cd_mark.image.fillAmount = fill_amount
		end

		if elapse_time >= total_time then
			self:ResetSkillWangQiStatus()
		end
	end

	self.wangqi_cool_countdown = CountDown.Instance:AddCountDown(WANGQI_COOL_TIME, 0.03, cool_func)
end

-- 重置望气状态
function MainUIView:ResetSkillWangQiStatus()
	self:RemoveWangQiCoolTimer()
	Scene.Instance:CloseWangQi(1)
	self.is_wangqi_skill_click = false

	if self.node_list then
		self.node_list.wangqi_skill_cd_mark:CustomSetActive(false)
		self.node_list.wangqi_skill_cd_text:CustomSetActive(false)
	end
end

-- 移除望气冷却倒计时
function MainUIView:RemoveWangQiCoolTimer()
	if self.wangqi_cool_countdown then
		CountDown.Instance:RemoveCountDown(self.wangqi_cool_countdown)
		self.wangqi_cool_countdown = nil
	end
end

-- 刷新升级引导按钮 
function MainUIView:UpdateExpGuideBtn()
	local cfg = ConfigManager.Instance:GetAutoConfig("daily_activity_auto")
	cfg = cfg and cfg.daily and cfg.daily[ACTIVITY_TYPE.EXP_GUIDE]
	if not cfg or not self.node_list.btn_exp_guide then
		return
	end
	local role_lev = GameVoManager.Instance:GetMainRoleVo().level
	local is_show_btn = role_lev >= cfg.level and role_lev <= cfg.level_max
	self.node_list.btn_exp_guide:SetActive(false)--is_show_btn)		--策划 林海锋 要求屏蔽此功能.
end

-- 刷新守护特权按钮
function MainUIView:UpdatePrivilegeCollectionShtqBtn()
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.pri_col_shtq)
	local is_sell_out = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeIsSellOut()
	self.node_list.btn_privilege_collection_shtq:SetActive(is_open)-- and not is_sell_out)
	if is_open then-- and not is_sell_out then
		local cur_seq = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeSeq()
		local next_cfg = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeCfgBySeq(cur_seq + 1)

		if IsEmptyTable(next_cfg) then
			local cur_cfg = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeCfgBySeq(cur_seq)
			next_cfg = cur_cfg
		end

		local other_cfg = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeOther()
		local is_show_tips = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeSHTQMainTipsFlag()
		local can_up_level, can_up_baifenbi = PrivilegeCollectionWGData.Instance:SaveExpCanUpLevel(next_cfg.seq >= 0 and next_cfg.seq or 0)
		local save_exp = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeSaveExp()
		self.node_list.shtq_exp_desc:SetActive(is_show_tips and save_exp > 0)-- and can_up_level >= other_cfg.exp_open_level)

		if is_show_tips then-- and can_up_level >= other_cfg.exp_open_level then
			local save_exp_str = is_sell_out and Language.PrivilegeCollection.SHTQSaveExpStr2 or Language.PrivilegeCollection.SHTQSaveExpStr
			-- self.node_list.pc_shtq_up_lv_text.text.text = string.format(Language.PrivilegeCollection.SHTQUpLvStr2, can_up_level)
			self.node_list.pc_shtq_up_lv_text.text.text = string.format(save_exp_str, CommonDataManager.ConverExp(save_exp))
		end

		local bg_effect_str = can_up_level >= 1 and "UI_shtq_bg1" or "UI_shtq_bg"
		local bundle, asset = ResPath.GetEffectUi(bg_effect_str)
		self.node_list.pc_shtq_exp_group_bg_effect:ChangeAsset(bundle, asset)
	end
end

-- 刷新当前的拍卖
function MainUIView:FlushKFKZPaiMaiStatus()
	self:RemoveKFKZPaiMaiCountDown()
	local status = CrossAirWarWGData.Instance:GetAirWarSceneStatus() 
	local is_paimai_status = status == CROSS_AIR_WAR_AUCTION_STATUS.STATUS_AUCTION
	local cd_time_stamp = CrossAirWarWGData.Instance:GetAirWarSceneNextStatusTime()
	self.node_list.kfkz_paimai_red:CustomSetActive(is_paimai_status)

	if not is_paimai_status then
		self.node_list.kfkz_paimai_status.text.text = ToColorStr(Language.Skill.OpenTip_6, COLOR3B.RED)
		return
	end

	if cd_time_stamp then
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		local remain_time = cd_time_stamp - server_time
		if remain_time > 0 then
			local cur_max_round = CrossAirWarWGData.Instance:GetMaxAirWarAuctionInfoRound()
			local cur_choose_round = CrossAirWarWGData.Instance:GetCurAirWarAuctionInfoRound()
			-- 设置当前拍卖物进度
			local progress_str = string.format("(%s/%s)", cur_choose_round + 1, cur_max_round)
			self.node_list.kfkz_paimai_status.text.text = string.format(Language.CrossAirWar.AuctionTimeTips, progress_str, TimeUtil.FormatSecondDHM2(remain_time)) 
			CountDownManager.Instance:AddCountDown("air_war_boss_auction_btn_cd", 
				BindTool.Bind1(self.KFKZPaiMaiAuctionBtnCD, self),
				BindTool.Bind1(self.KFKZPaiMaiAuctionBtnCDComplete, self), 
				nil, remain_time, 1)
		end
	end
end

-- 移除倒计时
function MainUIView:RemoveKFKZPaiMaiCountDown()
	if CountDownManager.Instance:HasCountDown("air_war_boss_auction_btn_cd") then
		CountDownManager.Instance:RemoveCountDown("air_war_boss_auction_btn_cd")
	end
end

function MainUIView:KFKZPaiMaiAuctionBtnCD(elapse_time, total_time)
	local time = math.ceil(total_time - elapse_time)
	local cur_max_round = CrossAirWarWGData.Instance:GetMaxAirWarAuctionInfoRound()
	local cur_choose_round = CrossAirWarWGData.Instance:GetCurAirWarAuctionInfoRound()
	-- 设置当前拍卖物进度
	local progress_str = string.format("(%s/%s)", cur_choose_round + 1, cur_max_round)
	self.node_list.kfkz_paimai_status.text.text = string.format(Language.CrossAirWar.AuctionTimeTips, progress_str, TimeUtil.FormatSecondDHM2(time)) 
end

function MainUIView:KFKZPaiMaiAuctionBtnCDComplete()
	self.node_list.kfkz_paimai_status.text.text = ToColorStr(Language.Skill.OpenTip_6, COLOR3B.RED)
end

-- 刷新当前的阶段评分
function MainUIView:FlushKFKZScoreStatus()
	self:RemoveKFKZScoringCountDown()
	local moster_seq = CrossAirWarWGData.Instance:GetWarCurMonsterSeq()
	local scoring_list = CrossAirWarWGData.Instance:GetScoringCfgBySeq(moster_seq)
	local status = CrossAirWarWGData.Instance:GetAirWarSceneStatus() 

	self.node_list.kfkz_time_score_root:CustomSetActive(scoring_list ~= nil and status == CROSS_AIR_WAR_AUCTION_STATUS.STATUS_MONSTER)
	if scoring_list == nil or status ~= CROSS_AIR_WAR_AUCTION_STATUS.STATUS_MONSTER then
		return
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local seq_start_time = CrossAirWarWGData.Instance:GetWarCurMosterStartTime()
	self.kfkz_seq_now_time = server_time - seq_start_time
	local now_scoring_data = nil

	for i, v in ipairs(scoring_list) do
		if self.kfkz_seq_now_time < v.time then
			now_scoring_data = v
			break
		end
	end

	self.node_list.kfkz_time_score_root:CustomSetActive(now_scoring_data ~= nil)
	if now_scoring_data ~= nil then
		local remain_time = now_scoring_data.time - self.kfkz_seq_now_time
		local time_str = ToColorStr(now_scoring_data.time, COLOR3B.GLOD)
		local scoring_desc = ToColorStr(now_scoring_data.scoring_desc, COLOR3B.GLOD)
		local txt_str = string.format(Language.CrossAirWar.AirWarScringTimeStr, time_str, scoring_desc)
		self.node_list.kfkz_time_score_text.text.text = txt_str
		self.node_list.kfkz_time_score_img.image:LoadSprite(ResPath.GetCommon(string.format("a3_fb_ysz_%d", now_scoring_data.scoring_index)))

		if remain_time > 0 then
			self.node_list.kfkz_time_down_text.text.text = TimeUtil.FormatSecondDHM5(self.kfkz_seq_now_time)
			CountDownManager.Instance:AddCountDown("air_war_boss_time_score", 
				BindTool.Bind1(self.KFKZScoringUpdate, self),
				BindTool.Bind1(self.KFKZScoringComplete, self), 
				nil, remain_time, 1)
		end
	end
end

function MainUIView:KFKZScoringUpdate(elapse_time, total_time)
	self.node_list.kfkz_time_down_text.text.text = TimeUtil.FormatSecondDHM5(elapse_time + self.kfkz_seq_now_time)
end

function MainUIView:KFKZScoringComplete()
	self:FlushKFKZScoreStatus()
end

-- 移除倒计时
function MainUIView:RemoveKFKZScoringCountDown()
	if CountDownManager.Instance:HasCountDown("air_war_boss_time_score") then
		CountDownManager.Instance:RemoveCountDown("air_war_boss_time_score")
	end
end

---------------------------------------- k跨服空战 End----------------------------------------
function MainUIView:ShowRedBtn()
	local person_info = WelfareWGData.Instance:GetPersonRedpaperInfo()
	if IsEmptyTable(person_info) then
		if self.node_list["btn_redpacket"] ~= nil then
			self.node_list["btn_redpacket"]:SetActive(false)
		end
		return
	end

	local world_get,world_use = self:GetWorldCanUse()
	local guild_get,guild_use = self:GetGuildCanUse()
	local all_use_red = {}
	for k,v in pairs(world_get) do
		table.insert(all_use_red,v)
	end
	for k,v in pairs(world_use) do
		table.insert(all_use_red,v)
	end
	for k,v in pairs(guild_get) do
		table.insert(all_use_red,v)
	end
	for k,v in pairs(guild_use) do
		table.insert(all_use_red,v)
	end
	table.sort(all_use_red, SortTools.KeyUpperSorters("creat_timestamp"))

	if self.node_list["btn_redpacket"] ~= nil then
		self.all_use_red = all_use_red

		local vas = self.node_list.redpackage_group:GetActive()
		local role_level = RoleWGData.Instance:GetRoleLevel()
		local is_show = #all_use_red > 0 and not self.bianshen_btn_unflod and role_level > COMMON_CONSTS.XIN_SHOU_LEVEL and not IS_AUDIT_VERSION
		self.node_list["btn_redpacket"]:SetActive(is_show)
		self.node_list["partical_pos"]:SetActive(is_show)
		self.node_list["redpackage_group"]:SetActive(is_show)

		self.node_list["btn_redpacket_text"].text.text = #all_use_red
		if not vas and is_show then
			self.node_list.redpackage_group.rect.localScale = Vector3(0,0,0)
			local scale = Vector3(1, 1, 1)
			self.node_list.redpackage_group.rect:DOScale(scale,0.5)
			GlobalTimerQuest:AddDelayTimer(function()
				self.node_list["redpackage_group"].animator:SetBool("is_shake", true)
			end,0.5)

		else
			self.node_list["redpackage_group"].animator:SetBool("is_shake", is_show)
		end
	end
end

function MainUIView:OnClickOpenVipServiceWindow()
	VipServiceWindowWGCtrl.Instance:OpenVipServiceView()
end

function MainUIView:GetWorldCanUse()
	local world_red_info = WelfareWGData.Instance:GetWorldRedpaperAllInfo()
	local world_can_get = {}
	local world_can_use = {}
	local data_base_cfg = {}
	local red_cfg = ConfigManager.Instance:GetAutoConfig("redpaper_auto")
	local person_info = WelfareWGData.Instance:GetPersonRedpaperInfo()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local have_count = false

	for m,n in pairs(world_red_info) do
		if n.paper_type == 0 then
			data_base_cfg = WelfareWGData.Instance:GetWelfareCustomRedpaperSeqCfg(n.paper_level)
		else
			data_base_cfg = WelfareWGData.Instance:GetWelfareWorldRedpaperSeqCfg(n.paper_type,n.paper_level)
		end
		local is_get_over = false
		local have_get_count = #n.record_list

		for k,v in pairs(n.record_list) do

			if v.uid == role_id then
				is_get_over = true
				break
			end
		end
		local data_base_cfg_info = WelfareWGData.Instance:GetWelfareCfg().other_config[1]
		local role_vip = VipWGData.Instance:GetRoleVipLevel()
		if data_base_cfg and data_base_cfg.bind_gold > 0 then
			if role_vip >= data_base_cfg_info.double_fetch_vip then
				have_count = person_info.day_receive_gold_count < data_base_cfg_info.vip_gold_bind_max
			else
				have_count = person_info.day_receive_gold_count < data_base_cfg_info.gold_bind_max
			end

		else
			if role_vip >= data_base_cfg_info.double_fetch_vip then
				have_count = person_info.day_distribute_ticket_count < data_base_cfg_info.vip_sliver_ticket_max
			else
				have_count = person_info.day_distribute_ticket_count < data_base_cfg_info.sliver_ticket_max
			end
		end
		if data_base_cfg and have_get_count < data_base_cfg.num and not is_get_over and have_count then
			local temp = __TableCopy(n)
			temp.is_use = 1
			table.insert(world_can_get,temp)
		end
	end

	local data_lsit = WelfareWGData.Instance:GetGuildSystemRedpaperCfg(2)
	for k,v in pairs(data_lsit) do
		if v.sort_index == 0 then
			local temp = __TableCopy(v)
		temp.is_use = 2
			table.insert(world_can_use,temp)
		end
	end

 	return world_can_get,world_can_use
end
function MainUIView:GetGuildCanUse()
	if GuildWGData.Instance:IsHaveGuild() == 1 then
		return {},{}
	end
	local red_cfg = ConfigManager.Instance:GetAutoConfig("redpaper_auto")
	local person_info = WelfareWGData.Instance:GetPersonRedpaperInfo()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local world_can_get = {}
	local world_can_use = {}
	local data_base_cfg = {}
	local have_count = false

	local red_all_info = WelfareWGData.Instance:GetGuildRedpaperAllInfo()
	for m,n in pairs(red_all_info) do
		if n.paper_type == 0 then
			data_base_cfg = WelfareWGData.Instance:GetWelfareCustomRedpaperSeqCfg(n.paper_level)
		else
			data_base_cfg = WelfareWGData.Instance:GetWelfareGuildRedpaperTypeCfg(n.paper_type,n.paper_level)
		end

		local is_get_over = false
		local have_get_count = #n.record_list

		for k,v in pairs(n.record_list) do
			if v.uid == role_id then
				is_get_over = true
				break
			end
		end

		local data_base_cfg_info = WelfareWGData.Instance:GetWelfareCfg().other_config[1]
		local role_vip = VipWGData.Instance:GetRoleVipLevel()
		if data_base_cfg and data_base_cfg.bind_gold > 0 then
			if role_vip >= data_base_cfg_info.double_fetch_vip then
				have_count = person_info.day_receive_gold_count < data_base_cfg_info.vip_gold_bind_max
			else
				have_count = person_info.day_receive_gold_count < data_base_cfg_info.gold_bind_max
			end

		else
			if role_vip >= data_base_cfg_info.double_fetch_vip then
				have_count = person_info.day_distribute_ticket_count < data_base_cfg_info.vip_sliver_ticket_max
			else
				have_count = person_info.day_distribute_ticket_count < data_base_cfg_info.sliver_ticket_max
			end
		end
		if data_base_cfg and have_get_count < data_base_cfg.num and not is_get_over and have_count then
			local temp = __TableCopy(n)
			temp.is_use = 1
			table.insert(world_can_get,temp)
		end
	end

	local data_lsit = WelfareWGData.Instance:GetSendRedPaperListInfo(true)
	for k,v in pairs(data_lsit) do
		if v.sort_index == 0 then
			local temp = __TableCopy(v)
		temp.is_use = 2
			table.insert(world_can_use,temp)
		end
	end

	return world_can_get,world_can_use
end

function MainUIView:RemindChangeCallBack(remind_name, num)
	-- print_log("RemindChangeCallBack:",remind_name,num)
	if self.icon_remind_list[remind_name] then
		local btn_name = self.icon_remind_list[remind_name]

		local btn = self.main_icon_node_list[btn_name]
		-- print_log("###RemindChangeCallBack####", remind_name,num, btn_name, btn == nil, btn.remind == nil)
		if btn and btn.remind then

			btn.remind.gameObject:SetActive(num > 0)
			self.icon_remind_list_num[remind_name] = num
		end
	end

	local function SetRedmind(btn_name, num)
		local btn = self.main_icon_node_list_top[btn_name]
		if btn and btn.remind then
			btn.remind.gameObject:SetActive(num > 0)
			if not MainUITopShirnkIgnoreRemind[remind_name] then
				self.icon_remind_list_top_num[remind_name] = num
			end
		end
	end

	if self.icon_remind_list_top[remind_name] then
		local btn_name = self.icon_remind_list_top[remind_name]

		--因为没做功能开启判断
		local tab = self:GetOneSundryButton(btn_name)
		if tab then
			local view_name = FunOpen.Instance:GetFunNameByViewName(tab[1]) or tab[1]
			if not ignore[view_name] and not FunOpen.Instance:GetFunIsOpened(view_name) then
				if not self.unopen_red_fun[view_name] then
					self.unopen_red_fun[view_name] = remind_name
				end
				SetRedmind(btn_name, 0)--功能没开启的，初始化都不显示红点.
				return
			end
		end

		SetRedmind(btn_name, num)
		self:ActiviyAggregationPermanpentBtnRemind(btn_name, num)
	end

	--vip红点
	if self.vip_remind_list[remind_name] then
		local btn_name = self.vip_remind_list[remind_name]
		local btn = self.main_icon_node_list[btn_name]
		if btn and btn.remind then
			btn.remind.gameObject:SetActive(num > 0)
			self.vip_remind_list_num[remind_name] = num
		end
	end

	if remind_name == RemindName.NewTeam_MyTeam then
		self.node_list["task_team_remind"]:SetActive(num > 0)
		self.node_list["btn_task_team_remind"]:SetActive(num > 0)
	end

	if remind_name == RemindName.RoleView then
		self:SetFPRoleViewRemind(num > 0)
	end

	-- 日常红点刷新，外边修为没红点问题
	if remind_name == RemindName.XiuWeiMainEnter then
		self:FlushTaskTopPanelXiuWeiRemind()
	end

	if remind_name == RemindName.YanYuGe_Noble_Privilege then
		MainuiWGCtrl.Instance:FlushMainUiActBtnYanYuGeNobleTip()
	end

	self:CheckChangeBtnIsRemind()
	self:CheckChangeTopRemind()
	self:CheckChangeTopLeftRemind()
	self:CheckFuncBoxBtnRemind()
	self:SetFriendRemind()
	-- self:CheckChatLeftShrinkRemind()
	self:CheckTaskLeftTogRemind(remind_name, num)
end

--该红点不需要在展开图标里显示
local NotShowMainuiIconRemind = {
	[RemindName.Bag] = true,
	[RemindName.TreasureHunt] = true,

}

function MainUIView:CheckChangeBtnIsRemind()
	if not self.bottom_toggle_state then
		for k,v in pairs(self.icon_remind_list_num) do
			if v > 0 and not NotShowMainuiIconRemind[k] then
				self.node_list.MainuiIconRemind:SetActive(true)
				return
			end
		end
	end
	self.node_list.MainuiIconRemind:SetActive(false)
end

--主界面右上角箭头红点控制
function MainUIView:CheckChangeTopRemind()
	if self.move_dis_ison == true then
		self:ChengeMainTopRemindEnable(false)
		return
	end

	local is_show_top_remind = false
	--红点判断规则修改
	if self.is_move_top_fun then
		for k,v in pairs(self.icon_remind_list_top_num) do
			if v > 0 then
				is_show_top_remind = true
				break
			end
		end

		if not is_show_top_remind then
			for k,v in pairs(self.act_remindRecord) do
				if v > 0 then
					is_show_top_remind = true
					break
				end
			end
		end
	else
		for k,v in pairs(TopNeedHideBtn) do
			local need_hide = self:GetTopBtnNeedHideBtn(k)
			local tab = self:GetOneSundryButton(k)
			if tab and need_hide then
				if self.icon_remind_list_top_num[tab["remind"]] and self.icon_remind_list_top_num[tab["remind"]] > 0 then
					is_show_top_remind = true
					break
				end
			end
		end

		-- 活动类按钮红点
		if not is_show_top_remind then
			for act_id, v in pairs(self.activity_btn_list_2) do
				if not self:IsTopNeedShowAct(act_id) and v:RedPointIsShow() then
					is_show_top_remind = true
					break
				end
			end
		end

		if not is_show_top_remind then
			for act_id, v in pairs(self.activity_btn_list_3) do
				if not self:IsTopNeedShowAct(act_id) and v:RedPointIsShow() then
					is_show_top_remind = true
					break
				end
			end
		end

		if not is_show_top_remind then
			local mainui_func_box_cache = MainuiWGData.Instance:GetFuncBoxDataCache()
			if not IsEmptyTable(mainui_func_box_cache) then
				for act_id, data in pairs(mainui_func_box_cache) do
					if ActRemindList[data.act_type] then
						local remind_num = RemindManager.Instance:GetRemind(ActRemindList[data.act_type])
						
						if remind_num > 0 then
							is_show_top_remind = true
							break
						end
					end
				end
			end
		end
	end

	self:ChengeMainTopRemindEnable(is_show_top_remind)
end

function MainUIView:ChengeMainTopRemindEnable( enable )
	if self.is_show_top_remind ~= enable then
		self.is_show_top_remind = enable
		if self.node_list["ShrinkRemind"] then
			self.node_list["ShrinkRemind"]:SetActive(enable)
		end
	end
end

function MainUIView:CheckChangeTopLeftRemind()
	local is_show_top_left_remind = false
	if self.left_shrink_ison then
		self:ChengeMainTopLeftRemindEnable(is_show_top_left_remind)
		return
	end

	for act_id, v in pairs(self.activity_btn_list_4) do
		if v:RedPointIsShow() then
			is_show_top_left_remind = true
			break
		end
	end

	self:ChengeMainTopLeftRemindEnable(is_show_top_left_remind)
end

function MainUIView:ChengeMainTopLeftRemindEnable(enable)
	if self.is_show_top_left_remind ~= enable then
		self.is_show_top_left_remind = enable
		if self.node_list["ShrinkLeftRemind"] then
			self.node_list["ShrinkLeftRemind"]:SetActive(enable)
		end
	end
end

function MainUIView:OpenMap()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.GHOST_FB_GLOBAL or scene_type == SceneType.GHOST_FB_PERSON then
		TipsSystemManager.Instance:ShowSystemTips(Language.ZhuoGuiFuBen.NoCanOpenMap)
		return
	end

	MapWGCtrl.Instance:OpenView()
end

function MainUIView:OpenDragonTempleHatchView()
	ViewManager.Instance:Open(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_hatch)
end

function MainUIView:OpenStrongMenu()
	if not self.strong_menu_view then
		return
	end

	self.strong_menu_view:Open()
end

function MainUIView:SetDataAndOpenStrongMenu(data)
	if not self.strong_menu_view then
		return
	end

	self.strong_menu_view:SetDataAndOpen(data)
end

function MainUIView:CloseStrongMenu()
	if not self.strong_menu_view then
		return
	end

	self.strong_menu_view:Close()
end

function MainUIView:OpenStrongGetRewardView()
	if not self.strong_get_reward_view then
		return
	end

	self.strong_get_reward_view:Open()
end

function MainUIView:CloseStrongGetRewardView()
	if not self.strong_get_reward_view then
		return
	end

	self.strong_get_reward_view:Close()
end

function MainUIView:ChangeBottomOnOffBtnState()
	local is_visible = MainuiWGData.Instance:GetMianUiBottomBtnVisible()

	self.top_button_toggle.toggle.isOn = is_visible
end

--播放主界面动画
--true == 显示功能按钮
--false == 显示技能按钮
function MainUIView:SetMainIconAnimation(isOn)
	if self.node_list["MenuIcon"] then
		self.node_list["MenuIcon"].toggle.isOn = isOn
	end
	self:CheckBottomLayoutAni(isOn)
end

-- 顶部图标收缩按钮
function MainUIView:SetShrinkButtonIsOn(isOn)
	if self.node_list["ShrinkButton"] then
		self.node_list["ShrinkButton"].toggle.isOn = isOn
	end
end

function MainUIView:OnGuajiTypeChange(guaji_type)
	if self:IsRendering() then
		local logic = Scene.Instance:GetSceneLogic()

		-- 不可以取消挂机
		if logic and not logic:CanCancleAutoGuaji() then
			GuajiCache.guaji_type = GuajiType.Auto
			if self.show_guaji then
				self.show_guaji:SetValue(false)
			end
			return
		end

		self.show_guaji:SetValue(true)
		if GuajiType.Auto == guaji_type then
			self.show_guaji:SetValue(false)
		else
			self.show_guaji:SetValue(true)
		end
	end
end

--充值
function MainUIView:OpenRecharge()
	if IS_AUDIT_VERSION then
		ViewManager.Instance:Open(GuideModuleName.RechargeView)
	else
		ViewManager.Instance:Open(GuideModuleName.VipView)
	end
end

function MainUIView:ShowIndexCallBack()

end

--旧的一天过去了，新的一天要到来了
function MainUIView:OnDayPass()
	self:ResetActBtnEffect()
	self:FlushCalendar()
	self:UpdateActLvLimitIcons()
end

function MainUIView:PlayCGEnd(bundle_name, asset_name)
	if asset_name == "CG_ChuChang" then
		self:CheckSelectCameraModeViewOpen()
	end
end

function MainUIView:CheckSelectCameraModeViewOpen()
	if RoleWGData.Instance.role_vo.level == 1 and CAMERA_TYPE ~= CameraType.Free then
		ViewManager.Instance:Open(GuideModuleName.CameraBubbleView)
	end
end

function MainUIView:SelectCameraModeViewClose()
	ViewManager.Instance:Close(GuideModuleName.CameraBubbleView)
end

function MainUIView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "recharge_vip" then
			self:SetMainRoleVip()
		elseif k == "JumpState" then
			self:FlushJumpState(v[1])
		-- elseif k == "icon_group_1" then
			-- self:FlushGroup1Icon()
		elseif k == "trailerview" then
			self:ChangeFunctionTrailer()
		elseif k == "FlyTaskIsHide" then
			self:SetFlyTaskIsHideMainUi(v[1])
		elseif k == "call_help" then
			self:ChatCanShowCallHelp()
		elseif k == "set_marry_notice" then
			self:SetMarryNoticeBtnInfo()
		elseif k == "set_auction_tips" then
			self:SetAuctionBtnInfo()
		elseif k == "seven_day_icon" then
			self:SetSevenDayStatus()
			self:FlushSevenDayIcon()
		-- elseif k == "versions_advance_notice" then
		-- 	self:FlushVersionsAdvanceNoticeBtn(v)
		elseif k == "hide_bs_btn" then
			self:UpdateBianShenBtnState(false)
		elseif k == "hide_mian_ui" then
			self:MianUIVisibChange(v[1], v[2])
		elseif k == "xunyou_mian_ui_state" then
			self:SetXunYouMainUiHideState(v[1])
		elseif k == "duihua_mian_ui_state" then
			self:SetDuiHuaMainUiHideState(v[1])
		elseif k == "dujie_main_ui_state" then
			self:SetDujieMainUiHideState(v[1])
		elseif k == "team_cell_flush" then
			self:TeamCellFlush()
		elseif k == "sort_btn_list3" then
			self:SortBtnList3()
		elseif k == "sort_btn_list4" then
			self:SortBtnList4()
		elseif k == "FlushCurSpeak" then
			self:FlushCurSpeak()
		elseif k == "FlushCalendar" then
			self:FlushCalendar()
		elseif k == "guild_boss_shaizi_state" then 	--仙盟boss投骰子时，隐藏技能面板
			self:SetGuildBossShaiZiMainUiHideState(v[1])
		elseif k == "vip_zero_buy" then
			self:FlushVipZeroBuy()
		elseif k == "sit_state" then
			self:FlushSitShow()
        elseif k == "flush_ser_rec_tip" then -- 新服首充优势提醒
        	self:FlushNewSerRecTip(v)
        elseif k == "flush_god_purchase_tip" then -- 神藏购买提醒
        	self:FlushGodPurchaseTip(v)
		elseif k == "vientiane_tianyin_tip" then
			self:FlushVienTianeTianYinTip(v)
        elseif k == "subpackage_icon" then
			self:SetSubPackageIcon(v[1])
		elseif k == "subpackage_qipao_active" then
            self:SetSubPackageQiPaoActice(v[1])
		elseif k == "level_gift" then--等级礼包
			--     self:FlushLevelGift()
			self:FlushLevelGiftWelfareUpgrade()
		elseif k == "ShenJiNoticeBtnFlush" then
			--self:ShenJiNoticeBtnFlush()
		elseif k == "ShenJiNoticeBtnRelease" then
			--self:ShenJiNoticeBtnRelease()
		-- elseif k == "free_fuhuo_info" then
  --           self:FlushFreeFuhuoIcon()
        elseif k == "xianli_info" then
			self:FlushXianli()
		elseif k == "long_zhu" then
			self:FlushLongZhuSkill()
		elseif k == "BaiLianZhaoHuanMainBtnFlush" then
			self:BaiLianZhaoHuanMainBtnFlush()
		elseif k == "LingHeDrawMainBtnFlush" then
			self:LingHeDrawMainBtnFlush()
		elseif k == "ShenJiTianCiMainBtnFlush" then
			self:ShenJiTianCiMainBtnFlush()
		elseif k == "ObservationState" then
			self:SetObservationState()
		elseif k == "flush_boss_nuqi" then
			self:FlushBossNuQi()
		elseif k == "chaotic_purchase_over" then
			self:SetChaoticPurchaseStatus()
		elseif k == "chaotic_vip_special" then
			self:SetChaoticVipspecialStatus()
		elseif k == "honghuangclassic_over" then
			self:SetHongHuangClassicStatus()
		elseif k == "honghuanggoodcoremony_over" then
			self:SetHongHuangGoodCoremonyStatus()
		elseif k == "boss_privrlege_info" then
			self:FlushBossPrivrlegeIcon()
		elseif k == "boss_god_war_info" then
			self:FlushBossGodWarPrivrlege()
		elseif k == "boss_must_full_privrlege_info" then
			self:FlushBossExplodePrivrlegeIcon()
		elseif k == "down_load_web" then
			self:SetDownLoadWebStatus()
		elseif k == "longxi_notice" then
			-- self:FlushLongXINotice()
		-- elseif k == "flush_level_recharge_tip" then
		-- 	self:FlushLevelRechargeTips()
		elseif k == "king_token_tip" then
			self:FlushDragonKingTokenTip()
		-- elseif k == "wild_buy_tip" then		--龙战于野直购小弹窗 屏蔽
		-- 	self:FlushDragonWildBuyTip()
		elseif k == "boss_first_kill" then
			self:FlushBossFirstKillRed()
		elseif k == "dizang_redpack" then
			self:FlushDiZangRedPack(v)
		elseif k == "online_reward" then
			self:FlushOnlineRewardPart(v)
		elseif k == "hm_act_notice" then
			self:FlushHmGodNotice()
		elseif k == "system_force_icon" then
			--self:FlushSystemForceIcon()
		elseif k == "flush_xianjie_nuqi" then
			self:DoXianJieBoss()
		elseif k == "discount_purchase_tip" then
			self:FlushDiscountPurchaseTip()
		elseif k == "boss_kill_every" then
			self:SetBossKillEveryIcon()
		elseif k == "vip_extra_role_exp" then
			self:FlushBtnExpPoolViewState()
		elseif k == "load_everyday_shop" then
			self:FlushLoadEveryDayShopRed()
		elseif k == "mainui_xxym_tip" or k == "mainui_fxs_tip" then
			self:FlushSpecialACTPanelState()
		elseif k == "boss_invasion_privilege" then
			self:FlushBossInvasionPrivilege()
		elseif k == "boss_invasion_quality" then
			self:FlushBossInvasionQuality()
		elseif k == "mainui_kfkz_paimai_status"  then
			self:FlushKFKZPaiMaiStatus()
		elseif k == "mainui_kfkz_score_status"  then
			self:FlushKFKZScoreStatus()
		elseif k == "set_red_packet_rain" then
			self:SetRedPacketRainBtnInfo()
		elseif k == "flush_hundred_num" then
			self:SetHundredEquipBtn()
		elseif k == "BeastsContractBtnFlush" then
			self:BeastsContractBtnFlush()
		elseif k == "CrossCrossTreasureTask" then
			self:FlushrossLingZhuPanel()
		elseif k == "CrossCrossTreasureTaskOpen" then
			self:FlushrossLingZhuPanel(true)
		elseif k == "vip_service" then
			self:FlushVipServiceBtnRemind()
		end
	end

	self:CheckAuditVersion()
end

function MainUIView:SetJoystickShowState(show)
	self:HideJoystick(show)
end

function MainUIView:RoleDataChangeCallback(attr_name, value, old_value)
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	if attr_name == "capability" then				--战斗力
		if value == old_value then --前后两次战斗力相同
			return
		end

		value = value > 0 and value or 0
		self:SetMainRoleCapability(value)
		self:SetFPMainRoleCapability(value)
		local mainui_data = MainuiWGData.Instance
		local cache_data = mainui_data:GetCacheTable() --:GetCacheTable()
		if cache_data == nil or cache_data.time < Status.NowTime then
			self:ShowPowerChange(value, old_value)

			if cache_data then
				mainui_data:CacheTableClearData()
			end
		else
			mainui_data:CacheTableAddData({value = value, old_value = old_value})
		end
	elseif attr_name == "level" then
		self:FlushLocationPanel()
		self:SetMainRoleLevel(value)
		self:SetFPMainRoleLevel(value)
		self:UpdateActLvLimitIcons()
		-- self:ChangeFunctionTrailer()
		self:FlushActiveTaskItem()
		-- VipTyWGCtrl.Instance:CheckRoleLevel(value,old_value)--Vip部分监测人物等级
		self:UpdateBianShenSkillState()  					--更新变身技能状态
		self:IsShowJumpBtn()

		self:FlushLongXINotice()                       -- 刷新龙玺提醒

		MarryWGCtrl.Instance:FlushHunYanTask()
		if NewTeamWGData.Instance:GetIsInRoomScene() then
			self:FlushRoom()
		end
		WelfareWGCtrl.Instance:MainuiOpenCreate()
		--刷新成就
		-- AchievementWGData.Instance:SetAccordionTable()
		-- 屏幕右方飘字效果有背景的
		local drama_cfg = ConfigManager.Instance:GetAutoConfig("drama_auto").play or {}
		for k,v in pairs(drama_cfg) do
			SysMsgWGCtrl.Instance:RightNoticeRemind(v.str, v.role_level, v.direction, v.direction_x, v.direction_y)
		end
		if value >= COMMON_CONSTS.WORLD_LEVEL_OPEN then
			GlobalEventSystem:Fire(ObjectEventType.FIGHT_EFFECT_CHANGE)
		end
		local guild_battle_limit_level = GuildBattleRankedWGData.Instance:GetActivityLimitLevel()
		if old_value and old_value < guild_battle_limit_level and guild_battle_limit_level <= value then
			GuildBattleRankedWGCtrl.Instance:InitGuildBattleCallBack()
		end

		--新手期间，等级改变自动切回主线任务
		if TaskGuide.Instance:NoviceCheckTask() and not TaskFollow.Instance:CanDoZhuTask(old_value)
			and TaskGuide.Instance:CurrTaskType() ~= GameEnum.TASK_TYPE_ZHU then
			TaskGuide.Instance:CurrTaskType(GameEnum.TASK_TYPE_ZHU)
        	TaskGuide.Instance:CanAutoAllTask(true)
        	TaskGuide.Instance:SideTOStopTask(false)
		end

		if old_value and old_value == COMMON_CONSTS.XIN_SHOU_LEVEL then
			self:ShowRedBtn()
		end

		if old_value and old_value == (COMMON_CONSTS.STRENGTHEN_SHOW_LEVEL - 1) then
			self:BianQiangDelayTime()
		end

		self:FlushCalendar()
		self:SetZuoqiBtnState()

		local vip_open_level = tonumber(VipServiceWindowWGData.Instance:GetOpenLevel())
		if (old_value and old_value < vip_open_level) and vip_open_level <= value then
			self:FlushVipServiceBtn()
		end
	elseif attr_name == "hp" or  attr_name == "max_hp" then
		self:SetMainRoleHp(main_role_vo.hp, main_role_vo.max_hp)
	elseif attr_name == "attack_mode" then
		self:SetAttackMode(value)
	elseif attr_name == "vip_level" then
		self:FlushFlyShoesBtn()
	elseif attr_name == "guild_id" then
		self:CheckGuildEff()
		self:FlushLocationPanel()
		self:Flush(0, "FlushCurSpeak")
	elseif attr_name == "special_appearance" then
		self.node_list["btn_cancel_bianshen"]:SetActive(false)

		self:UpdateBianShenSkillState()
		self:FlushSitShow()
	elseif attr_name == "fight_mount_skill_level" then
		self:FlushSitShow()
	elseif attr_name == "cash_point" then
		self:FlushCashPoint()
	elseif attr_name == "recharge_volume" then
		self:FlushRechargeVolume()
	elseif attr_name == "exp" or attr_name == "max_exp" then
		self:SetFPmaonRoleExp()
		self:UpdatePrivilegeCollectionShtqBtn()
	elseif attr_name == "name" then
		self:SetFPMainRoleName()
	end
end

--是否显示功能预告
function MainUIView:ChangeFunctionTrailer()
	if self.function_trailer then
		self.function_trailer:FlushView()
	end
end

--是否显示挂机按钮
function MainUIView:SetCanShowGuajiBtn(enable)
	self.can_show_guaji_btn = enable
end

--是否显示挂机按钮
function MainUIView:GetCanShowGuajiBtn()
	return self.can_show_guaji_btn
end

function MainUIView:GetIsEnableShow(scene_type)
    local enable = scene_type ~= SceneType.KF_HotSpring and
        scene_type ~= SceneType.HotSpring and
        scene_type ~= SceneType.GUILD_ANSWER_FB and
        scene_type ~= SceneType.HunYanFb and
        scene_type ~= SceneType.KF_DUCK_RACE
    return enable
end

function MainUIView:OnSceneLoadingQuite(old_scene_type, new_scene_type)
	local enable = self:GetIsEnableShow(new_scene_type)
    self:ChangeFunctionTrailer()
	local fb_info = FuBenWGData.GetFbSceneConfig(new_scene_type)
	self.normal_mount_flag = fb_info and fb_info.pb_mount == 0
    self:SetZuoqiBtnState(self.normal_mount_flag)
    self:SetCanShowGuajiBtn(enable)
	self.node_list["btn_guaji"]:SetActive(self:GetCanShowGuajiBtn())
	self:UpdateBianShenSkillState()
	self:UpdateQucikItemBtnStateByScene(new_scene_type)
	self:ActivitySceneLoadingQuite(old_scene_type, new_scene_type)

	--回到普通场景才刷新主界面组队text
	if new_scene_type == SceneType.Common then
		self:ChangeTeamBtnName()
	end

	-- 非相关场景 关闭怒气踢出提示界面
	if self.is_boss_angry_cd 
	and	(old_scene_type == SceneType.DABAO_BOSS or new_scene_type == SceneType.XianJie_Boss
		or new_scene_type == SceneType.SG_BOSS or new_scene_type == SceneType.KFSHENYUN_FB)
	and (new_scene_type ~= SceneType.DABAO_BOSS and new_scene_type ~= SceneType.XianJie_Boss
		and new_scene_type ~= SceneType.SG_BOSS and new_scene_type ~= SceneType.KFSHENYUN_FB) then
		self:RemoveBossNuQiCountDownTime()
	end

	--self:SetFreeFuhuoIcon()
	-- self:SetBossPrivrlegeIcon()
	self:FlushBossGodWarPrivrlege()
	self:SetBossKillEveryIcon()
	self:SetBossExplodePrivrlegeIcon()
	--self:SetBossMabiIcon()
	self:SetHundredEquipBtn()
    self:FlushFlyShoesBtn()
    -- self:FlushLevelGift()
    -- self:FlushCountryBtnShow()
    self:FlushLocationPanel()
    self:Flush1V1Btn()
	self:FlushKFKZBtn()

    if self.main_efficiency then
        self.main_efficiency:FlushEffecticiency()
    end

    self:HideFindLatelyGatherBtn()
    self:PlayerChangeSceneBack(old_scene_type, new_scene_type)
    if self.guard_guide then
        self.guard_guide:OnXiaoGuiDataChange()
    end
    if self.map_view then
        self.map_view:OnSceneLoaded()
    end

	self.node_list.task_btn_trans:SetActive(self:GetMoveDisIson() and new_scene_type == SceneType.Common)

	self:FlushLevelGiftWelfareUpgrade()
	-- self:FlushTransferEquipCollectInfo()
end

function MainUIView:Flush1V1Btn()
    local scene_type = Scene.Instance:GetSceneType()
	self.node_list.btn_kfonevone_imag:SetActive( scene_type == SceneType.Kf_PvP_Prepare) --scene_type == SceneType.Kf_OneVOne_Prepare or
end

function MainUIView:FlushVipServiceBtn()
	local is_open = VipServiceWindowWGData.Instance:GetServiceIsOpen()
	self.node_list["BtnVipService"]:SetActive(is_open)

	if self.node_list["vip_service_Icon"] and is_open then
		local default_icon_name = "a3_zsfl_zjm_icon_fl"
		local show_icon_name = default_icon_name
		local icon = VipServiceWindowWGData.Instance:GetCurAgentServiceIcon()
		if icon then
			show_icon_name = icon
		end
		
		local bundle, asset = ResPath.GetMainUIIcon(show_icon_name)
		self.node_list["vip_service_Icon"].image:LoadSprite(bundle, asset, function()
			self.node_list["vip_service_Icon"].image:SetNativeSize()
		end)

		--VipServiceWindowWGCtrl.Instance:OpenVipServiceView(true)	--功能开启时，登录自动打脸弹出功能界面.
		local interval_time = VipServiceWindowWGData.Instance:GetBubbleIntervalTime()
		self:OnServiceBubbleUpdateTime()
		self.service_bubble_time_quest = GlobalTimerQuest:AddTimesTimer(
			BindTool.Bind(self.OnServiceBubbleUpdateTime, self), interval_time, 999999999)
	end
end

function MainUIView:FlushVipServiceBtnRemind()
	local daily_reward_flag = VipServiceWindowWGData.Instance:GetDailyRewardFlag()
	self.node_list["btn_vip_service_remind"]:SetActive(daily_reward_flag == 0)
end

function MainUIView:OnServiceBubbleUpdateTime()
	local cfg = VipServiceWindowWGData.Instance:GetServiceBubbleShowCfg()
	self.node_list["vip_service_bubble"]:SetActive(true)
	self.node_list["service_bubble_text"].text.text = cfg.bubble_text
	ReDelayCall(self, function()
		if self.node_list["vip_service_bubble"] then
			self.node_list["vip_service_bubble"]:SetActive(false)
		end
	end, cfg.pop_duration_time, "vip_service_bubble")
end

function MainUIView:UpdateBtnAssistStatus(scene_type)
	if scene_type == SceneType.WorldBoss or scene_type == SceneType.VIP_BOSS or
	scene_type == SceneType.KF_BOSS or scene_type == SceneType.MJ_BOSS then
		self.node_list["btn_assist"]:SetActive(true)
	else
		self.node_list["btn_assist"]:SetActive(false)
	end
end

function MainUIView:OnClickAssist()
	if not self.assist_alert then
		self.assist_alert = Alert.New()
		self.assist_alert:SetOkString(Language.Mainui.AssistOkStr)
		self.assist_alert:SetOkFunc(BindTool.Bind(self.SendGuildChatAssist, self))
	end
	local scene_name = Scene.Instance:GetSceneName()
	self.assist_alert:SetLableString(string.format(Language.Mainui.AssistStr, scene_name))
	self.assist_alert:Open()
end

function MainUIView:SendGuildChatAssist()
	local scene_name = Scene.Instance:GetSceneName()
	local main_role = Scene.Instance:GetMainRole()
	if not main_role then
		return
	end
	local scene_id = Scene.Instance:GetSceneId()
	local pos_x,pos_y = main_role:GetLogicPos()
	local help_str = string.format(Language.Mainui.ChatHelpStr, scene_name, scene_id, pos_x, pos_y)
	ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.GUILD, help_str, CHAT_CONTENT_TYPE.TEXT)
end

function MainUIView:SetToggleMenuIsOn( isOn )
	if not isOn and (FunctionGuide.Instance:GetIsGuide() or ViewManager.Instance:IsOpen(GuideModuleName.OpenFunFlyView)) then
		return false
	end
	if not isOn then
		local main_role = Scene.Instance:GetMainRole()
		if main_role and (main_role:IsQingGong() or main_role:IsMitsurugi()) then
			return false
		end
	end
	if self.toggle_menu.toggle.isOn ~= isOn then
		self.toggle_menu.toggle.isOn = isOn
	end
	self:CheckBottomLayoutAni(isOn)
end

--设置主界面为副本远古仙殿类型的模式
function MainUIView:SetYGXDMode(enable,x,y,h,w)
	if nil == self.node_list["TopButtonsPanel"] then return end
	-- self.node_list["TopButtonsPanel"]:SetActive(enable)
	-- self.node_list["NormalTask"]:SetActive(enable)
	-- self.node_list["TeamContent"]:SetActive(enable)
	self.node_list["ButtonZuoqi"]:SetActive(enable)
	-- self.node_list["ShrinkButtons"]:SetActive(enable)
	-- self.node_list["TabButtons"]:SetActive(enable)

	self:SetTaskPos22(x,y)
	if not enable then
		self.node_list["fb_time2"]:SetActive(true)
	end
end
function MainUIView:SetTaskPos22(x,y,h,w)
	-- self.node_list["TaskParent"].rect.anchoredPosition =  Vector2(x,y)
end

-- function MainUIView:SetTask(enable)
-- 	self.node_list["NormalTask"]:SetActive(enable)
-- end
function MainUIView:StartTeamMove(enable)
    self.anim = self.node_list["TrackInfoMask"].transform.gameObject:GetComponent(typeof(UnityEngine.Animator))
    self.anim:SetBool("fold",enable)
	-- self.node_list["NormalTask"]:SetActive(enable)
end

-- function MainUIView:SetTeamContenActive(enable)
-- 	if self.node_list["TeamContent"] then
-- 		self.node_list["TeamContent"]:SetActive(enable)
-- 	end
-- end

function MainUIView:ShowPowerChange( value, old_value )
	if not old_value then
		return
	end
	if value ~= old_value and old_value > -1 then
		if (nil ~= self.power_change_view) and (value > old_value) then
			-- 如果正在加载场景，则等一下再弹战力飘字
			if Scene.Instance:LoadingViewIsOpen() then
				local power_change_func = function()
					self:ShowPowerChange(value, old_value)
				end
				GlobalTimerQuest:AddDelayTimer(function()
					power_change_func()
				end, 1)
			else
				self.power_change_view:ShowView(value, old_value)
			end
		end
	end
end

function MainUIView:NeedInit()
	if self.ned_set_task_panel then
		self.ned_set_task_panel = false
		MainuiWGCtrl.Instance:SetTaskPanel(self.ned_task_tab._enable,self.ned_task_tab._x,self.ned_task_tab._y)
		self.ned_task_tab = nil
	end

	ReDelayCall(self, function ()
		local count = #self.call_back_list
		if count > 0 then
			local info = nil
			self.call_back_list_in_loop = true
			for i = count, 1, -1 do
				info = self.call_back_list[i]
				if info.callback then
					info.callback()
				end
				assert(#self.call_back_list == i, "callbacklist 在循环中被删除了元素")
				table.remove(self.call_back_list, i)
			end
			self.call_back_list_in_loop = false
		end

		if self.ned_show_mainui_menu then
			self.ned_show_mainui_menu = false
			MainuiWGCtrl.Instance:ShowMainuiMenu(self.mainui_menu_state)
		end

		self:CheckBottomLayoutAni(false)
	end, 0, "init_callback_time_quest") --为啥之前要延迟1秒？？有bug，导致执行顺序有问题
end

-- mainuiview 初始化后做回调
function MainUIView:AddInitCallBack(activity_type, callback)
	if nil == callback then
		return
	end
	table.insert(self.call_back_list, 1, {["callback"] = callback, activity_type = activity_type})
end

function MainUIView:RemoveInitCallBack( activity_type )
	if nil == activity_type or self.call_back_list_in_loop then
		return
	end
	if IsEmptyTable(self.call_back_list) then
		return
	end
	for k,v in ipairs(self.call_back_list) do
		if v.activity_type == activity_type then
			table.remove(self.call_back_list, k)
			v.callback = nil
			break
		end
	end
end

--容错
function MainUIView:SetNeedShowMainUiMenu(enable)
	self.ned_show_mainui_menu = true
	self.mainui_menu_state = enable
end

function MainUIView:SetNeedTaskPanel(enable,x,y)
	self.ned_set_task_panel = true
	self.ned_task_tab = {_enable = enable, _x = x, _y = y}
end

function MainUIView:UpdataVipTyTime(elapse_time, total_time)
	local time = total_time - elapse_time
	local format_time = TimeUtil.Format2TableDHMS(time)
	-- print_error("服务器的时间：",elapse_time,"结束时间:",total_time,"时间差",time,"日子",format_time.day,"小时",format_time.hour,"分钟",format_time.min,format_time.hour,"秒",format_time.s)
	local hour = format_time.hour + format_time.day*24
	local min = format_time.min
	local s = format_time.s
	if hour >= 1 then
		self.node_list["vipty_text"].text.text = string.format(Language.Recharge.VIPOneTyState,hour)..Language.Recharge.VIPTyStateEnd
	else
		self.node_list["vipty_text"].text.text = string.format(Language.Recharge.VIPTyStateDaoJi,min,s)..Language.Recharge.VIPTyStateEnd
	end
end

function MainUIView:CompleteVipTyCallBack()
	self.node_list["vipty_text"].text.text = ""
	self.node_list.VipTyState:SetActive(false)
end

function MainUIView:GetAllButtonName()
	local button_list = {}
	local bottom_button = self:GetBottomButton()
	local sundry_button = self:GetSundryButton()
	button_list[FunOpenIconFlyPos.Bottom] = bottom_button
	button_list[FunOpenIconFlyPos.Top] = sundry_button
	return button_list
end

function MainUIView:GetMenuButtonIsOn()
	if self.node_list["MenuIcon"] then
		return self.node_list["MenuIcon"].toggle.isOn
	end
end

function MainUIView:GetShrinkButtonIsOn()
	if self.node_list["ShrinkButton"] then
		return self.node_list["ShrinkButton"].toggle.isOn
	end
end

function MainUIView:GetMainUICanFlyButtonObj(cfg)
	local button_name = MainuiWGData.Instance:GetMainUIButtonNameAndPos(cfg)
	return self.node_list[button_name]
end

function MainUIView:GetMainUICanFlyButtonObj2(button_name)
	return self.node_list[button_name]
end

function MainUIView:GetRoleAutoXunluState()
	-- body
	return self.is_auto_rotation_camera
end

-- 自动寻路状态改变
function MainUIView:OnMainRoleAutoXunluChange(auto)
	if self.is_auto_rotation_camera == auto then return end
	self.is_auto_rotation_camera = auto
	self:SetAutoRotation()
end

function MainUIView:LockCameraAutoRotate()
	self.is_lock_camera_auto = true

	if not IsNil(MainCameraFollow) then
		MainCameraFollow.AutoRotation = false
	end
end

function MainUIView:UnLockCameraAutoRotate()
	self.is_lock_camera_auto = false

	if not IsNil(MainCameraFollow) then
		local scene_logic = Scene.Instance:GetSceneLogic()
		if not RoleWGData.Instance:IsHoldAngle() and not MainuiWGData.UserOperation and scene_logic and scene_logic:CanAutoRotation() then
			local flag = self.is_auto_rotation_camera == XunLuStatus.XunLu
			if not flag then
				local main_role = Scene.Instance:GetMainRole()
				if main_role ~= nil and not main_role:IsDeleted() and main_role:IsInXunYou() then
					flag = true
				end
			end

			MainCameraFollow.AutoRotation = flag
		else
			MainCameraFollow.AutoRotation = false
		end
	end
end

function MainUIView:SetAutoRotation()
	if not IsNil(MainCameraFollow) and not self.is_lock_camera_auto then
		local scene_logic = Scene.Instance:GetSceneLogic()
		if not RoleWGData.Instance:IsHoldAngle() and not MainuiWGData.UserOperation and scene_logic and scene_logic:CanAutoRotation() then
			local flag = self.is_auto_rotation_camera == XunLuStatus.XunLu
			if not flag then
				local main_role = Scene.Instance:GetMainRole()
				if main_role ~= nil and main_role:IsInXunYou() then
					flag = true
				end
			end

			MainCameraFollow.AutoRotation = flag
		else
			MainCameraFollow.AutoRotation = false
		end
	end
	self:SetXunLuStatus(self.is_auto_rotation_camera)
end

function MainUIView:FlushXunLuStates()
	self:SetXunLuStatus(self.is_auto_rotation_camera)
end

function MainUIView:SetRootVIPState( enable )
	if self.node_list["vip_root"] then
		self.node_list["vip_root"]:SetActive(enable)
	end
end

function MainUIView:SetRightBottomActive(is_active)
	self:SetVisibleNormalSkillPanel(is_active)
	self.node_list["btn_guaji"]:SetActive(is_active)
	self.node_list["Menu"]:SetActive(is_active)
end

function MainUIView:SetVisibleNormalSkillPanel(is_active)
	if nil == self.skill_toggle then
		self.skill_toggle = self.node_list["NormalSkillPanelRoot"]
	end
	if nil ~= self.skill_toggle then
		self.skill_toggle:SetActive(is_active)
	end
end

function MainUIView:GetGuideUiCallBack(ui_name, ui_param, try_times, guide_cfg)
	-- 主界面右上按钮
	-- 需要判断副屏是否打开，并且在副屏有无相关的按钮
	local act_id = nil

	if ui_name ~= nil and ui_name ~= "" then
		local ui_name_list = Split(ui_name, "#")

		if #ui_name_list > 1 then
			ui_name = ui_name_list[1]
			act_id = tonumber(ui_name_list[2]) or 0
		end
	end

	local top_right_btn_data = self:GetOneSundryButton(ui_name)
	local fp_btn_data = self:GetOneBottomButton(ui_name)
	local is_mainui_task_top_btn = self:GetIsMainuiTaskTopBtn(ui_name)
	if top_right_btn_data then
		-- 特殊处理逻辑
		local special_fun
		if ui_name == "BtnFuBen" then
			-- ui_param 参照:fubenpanel_welkin
			local tab_index = TabIndex[ui_param]
			special_fun = function()
				ViewManager.Instance:Open(GuideModuleName.FuBenPanel, tab_index)
			end
		elseif ui_name == GuideUIName.BtnActJjcView then
			special_fun = function()
				Field1v1WGCtrl.Instance:PickEnemyFieldFight()
				Field1v1WGData.Instance:SetNeedLeave1V1Scene(true)
			end
		elseif ui_name == GuideUIName.BtnFPActJJC then
			special_fun = function()
				ArenaTiantiWGCtrl.Instance:PickEnemyFieldFight()
				ArenaTianTiWGData.Instance:SetNeedLeave1V1Scene(true)
			end
		elseif ui_name == GuideUIName.BtnQiFuView then
			special_fun = function ()
				ViewManager.Instance:Open(GuideModuleName.QIFU, TabIndex.qifu_yunshi)
			end
		end
		
		-- 副屏打开
		if self.toggle_menu.toggle.isOn then
			local fun_name = ModuleNameToFunName[top_right_btn_data[1]] or top_right_btn_data[1]
			local fun_cfg = FunOpen.Instance:GetFunByName(fun_name)
			local fp_btn_name
			if fun_cfg.zjm_fp_btn_name and fun_cfg.zjm_fp_btn_name ~= "" then
				fp_btn_name = fun_cfg.zjm_fp_btn_name
			end

			fp_btn_data = self:GetOneBottomButton(fp_btn_name)
			if fp_btn_data then
				local func = special_fun or BindTool.Bind(BindTool.Bind(self.OnClickOpenView, self, fp_btn_data))
				return self.node_list[fp_btn_name], func
			else
				-- 关闭副屏
				self:SetMainIconAnimation(false)
				self:SetShrinkButtonIsOn(true)
				local func = special_fun or BindTool.Bind(BindTool.Bind(self.OnClickOpenView, self, top_right_btn_data))
				return self.node_list[ui_name], func
			end
		else
			self:SetShrinkButtonIsOn(true)
			local func = special_fun or BindTool.Bind(BindTool.Bind(self.OnClickOpenView, self, top_right_btn_data))
			return self.node_list[ui_name], func
		end

	elseif fp_btn_data then
		-- if Scene.Instance:GetSceneType() ~= SceneType.Common then
		-- 	return
		-- end
		-- 特殊处理逻辑
		local special_fun
		if ui_name == "BtnFuBen" then
			local tab_index = TabIndex[ui_param]
			special_fun = function()
				ViewManager.Instance:Open(GuideModuleName.FuBenPanel, tab_index)
			end
		elseif ui_name == GuideUIName.BtnActJjcView then
			special_fun = function()
				Field1v1WGCtrl.Instance:PickEnemyFieldFight()
				Field1v1WGData.Instance:SetNeedLeave1V1Scene(true)
			end
		elseif ui_name == GuideUIName.BtnFPActJJC then
			special_fun = function()
				ArenaTiantiWGCtrl.Instance:PickEnemyFieldFight()
				ArenaTianTiWGData.Instance:SetNeedLeave1V1Scene(true)
			end
		elseif ui_name == GuideUIName.BtnCultivationRealm then
			special_fun = function()
				local tab_index = TabIndex[ui_param]
				ViewManager.Instance:Open(GuideModuleName.ControlBeastsView, tab_index)
			end
		end

		-- 副屏打开
		if self.toggle_menu.toggle.isOn then
			local func = special_fun or BindTool.Bind(BindTool.Bind(self.OnClickOpenView, self, fp_btn_data))
			return self.node_list[ui_name], func
		else
			local fun_name = ModuleNameToFunName[fp_btn_data[1]] or fp_btn_data[1]
			local fun_cfg = FunOpen.Instance:GetFunByName(fun_name)
			local zjm_btn_name
			if fun_cfg.zjm_zp_btn_name and fun_cfg.zjm_zp_btn_name ~= "" then
				zjm_btn_name = fun_cfg.zjm_zp_btn_name
			end

			top_right_btn_data = self:GetOneSundryButton(zjm_btn_name)
			-- 在主界面有无相关的按钮
			if top_right_btn_data then
				self:SetShrinkButtonIsOn(true)
				local func = special_fun or BindTool.Bind(BindTool.Bind(self.OnClickOpenView, self, top_right_btn_data))
				return self.node_list[zjm_btn_name], func

			-- 主界面没有则打开副屏界面
			else
				self:SetMainIconAnimation(true)
				local func = special_fun or BindTool.Bind(BindTool.Bind(self.OnClickOpenView, self, fp_btn_data))
				return self.node_list[ui_name], func
			end
		end

	elseif is_mainui_task_top_btn then
		if not self:GetShrinkButtonIsOn() then
			self:SetShrinkButtonIsOn(true)
		end
		
		return self.node_list[ui_name]
		-- 头像
	elseif ui_name == GuideUIName.BtnPortraitLabel then
		return self.node_list[ui_name], BindTool.Bind(BindTool.Bind1(self.OnClickMainRoleHead, self))
	-- 活动
	elseif ui_name == GuideUIName.OpenServerAct and self.activity_button_list[ACTIVITY_TYPE.OPEN_SERVER] then
		self:SetMainIconAnimation(false)
		self:SetShrinkButtonIsOn(true)
		return self.activity_button_list[ACTIVITY_TYPE.OPEN_SERVER].view, BindTool.Bind(ActivityWGCtrl.OpenPopView, ActivityWGCtrl.Instance, ACTIVITY_TYPE.OPEN_SERVER)
	-- 挂机
	elseif ui_name == GuideUIName.MainuiGuaji then
		return self.node_list["btn_guaji"], BindTool.Bind(self.OnClickGuaji, self)
	-- 坐骑
	elseif ui_name == GuideUIName.ButtonZuoqi then
		return self.node_list["ButtonZuoqi"], BindTool.Bind(self.OnClickZuoQi, self)
	-- 跳跃
	elseif ui_name == GuideUIName.MainuiJumpButton then
		self:SetMainIconAnimation(false)
		-- local main_role = Scene.Instance:GetMainRole()
		--会导致引导到FlyJumpButton按钮 先屏蔽了
		-- if main_role and main_role:GetQingGongState() ~= QingGongState.None then
		-- 	return self.node_list["FlyJumpButton"], BindTool.Bind(self.OnClickJump, self)
		-- end
		return self.node_list["JumpButton"], BindTool.Bind(self.OnClickJump, self, true)
	elseif ui_name == GuideUIName.MainuiMenu then
		self:SetMainIconAnimation(false)
		return self.node_list["MenuIcon"], BindTool.Bind(function ()
			self:SetMainIconAnimation(true)
		end, self)
	--着陆
	elseif ui_name == GuideUIName.MainuiLandingButton then
		return self.node_list["LandingButtonDown"], BindTool.Bind(self.OnClickLanding, self, true)
	-- 小飞鞋
	elseif ui_name == GuideUIName.FlyShoesGuide then
		return self:GetFlyShoesGuide()
	--天神引导
	elseif ui_name == GuideUIName.MainUiTianShenSkillS then
		if self.toggle_menu.toggle.isOn then
			self.toggle_menu.toggle.isOn = false
			return
		end
	--天神技能
	elseif ui_name == GuideUIName.MainUiTianShenSkill then
		return self:GetTianshenSkillGuide(tonumber(ui_param))
	-- 修仙试炼
	elseif ui_name == GuideUIName.XiuXianBtn then
		local fun = function()
    		local cur_data_index = XiuXianShiLianWGData.Instance:GetSingleCanActiveSkillChapterId()
            ViewManager.Instance:Open(GuideModuleName.XiuXianShiLian, nil, "all", {open_param = cur_data_index})
		end
		return self.node_list[ui_name], fun
	-- 变强
	elseif ui_name == GuideUIName.BianQiang then
		return self.node_list["btn_bianqiang"], BindTool.Bind(self.OpenStrongMenu, self)
	-- 任务按钮
	elseif ui_name == GuideUIName.MainTaskBtn then
		local open_func = function()
			--ViewManager.Instance:Open(GuideModuleName.Boss, TabIndex.boss_vip)
			BossWGCtrl.Instance:GuideEnterVipBoss()
		end

		-- if MainuiWGCtrl.Instance:GetPlayTaskButtonStatus() then
		-- 	MainuiWGCtrl.Instance:PlayTaskButtonTween(false)
		-- end
		-- MainuiWGCtrl.Instance:SetTask(true)

		local main_task_view = self:FindTaskCellByTaskType(GameEnum.TASK_TYPE_ZHU)
		if main_task_view and main_task_view.node_list then
			return main_task_view.node_list.BtnSelf, open_func
		end

		return self.node_list[ui_name], open_func
	-- boss引导
	elseif ui_name == GuideUIName.BossNoticeCell then
		local notice_cell = self:FindNoticeBossCell()
		if notice_cell and notice_cell.node_list then
			BossWGData.Instance:ClearCurSelectBossID()
			local scene_logic = Scene.Instance:GetSceneLogic()
        		if scene_logic then
        		   	scene_logic:ClearGuaJiInfo()
        		end
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
			local ok_func = function ()
				notice_cell:OnClickBossRender()
			end

			return self.node_list["boss_notice_select_area"], ok_func
		end
		return
	elseif ui_name == GuideUIName.UseBeastsSkill then
		local cell = self.beasts_skill_list and self.beasts_skill_list[1]
		local click_func = nil

		if cell then
			click_func = function ()
				local data = cell.data

				local skill_pos = {}
				if guide_cfg and guide_cfg.trigger_type == GuideTriggerType.AcceptTask_NoAuto then
					local task_cfg = TaskWGData.Instance:GetTaskConfig(guide_cfg.trigger_param)
					if task_cfg and task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_27 then
						skill_pos.x = task_cfg.c_param2
						skill_pos.y = task_cfg.c_param3
					end
				end

				self:OnDoSkillByBeastsFunc(data, skill_pos)
			end
		end
			
		return self.node_list[ui_name], click_func
	elseif ui_name == GuideUIName.TriggerFirstTaskGuide then
		local curr_task_id = TaskWGData.Instance:CurrTaskId()
		local click_func = function ()
			self:DoTask(curr_task_id)
		end

		return self.node_list[ui_name], click_func
	-- elseif ui_name == GuideUIName.TriggerTaskActJjc then
	-- 	local special_fun = function()
	-- 		Field1v1WGCtrl.Instance:PickEnemyFieldFight()
	-- 		Field1v1WGData.Instance:SetNeedLeave1V1Scene(true)
	-- 	end

	-- 	return self.node_list[ui_name], special_fun
	elseif ui_name == GuideUIName.TriggerTaskActJjc then
		local special_fun = function()
			ArenaTiantiWGCtrl.Instance:PickEnemyFieldFight()
			ArenaTianTiWGData.Instance:SetNeedLeave1V1Scene(true)
		end
		return self.node_list[ui_name], special_fun
	elseif ui_name == GuideUIName.JumpButtonUp then
		local guide_func = function ()	-- 这里给一个空方法
		end
		return self.node_list[ui_name], guide_func
	elseif ui_name == "act" then
		if act_id ~= nil then
			local btn = MainuiWGCtrl.Instance:GetActivityButtonByActivityType(act_id)
			local view = btn and btn:GetView()
			return view, BindTool.Bind(ActivityWGCtrl.openPopView, ActivityWGCtrl.Instance, act_id)
		end
	else
		return self.node_list[ui_name]
	end
end

function MainUIView:CheckPackageSpace(change_item_id, change_item_index, change_reason)
	self:UpdateQucikItemBtnStateByItemId(change_item_id)

	if change_reason and change_reason ~= GameEnum.DATALIST_CHANGE_REASON_ADD then
		return
	end

	local empty_num = ItemWGData.Instance:GetEmptyNum()
	local stuff_empty_num = ItemWGData.Instance:GetStuffBagEmptyNum()

	if empty_num < 5 then
		if RoleBagWGCtrl.Instance:CheckAutoRongLian() then
			self.node_list.full_tip:SetActive(false)
			self.node_list.role_bag_icon.animator:SetBool("is_full", false)
			return
		end
	end

	if empty_num == 0 or stuff_empty_num == 0 then
		RoleBagWGCtrl.Instance:OpenRoleBagCleanTips(KNAPSACK_TYPE.NORMAL)
		self.node_list.full_tip:SetActive(true)
		self.node_list.role_bag_icon.animator:SetBool("is_full", true)
	else
		self.node_list.full_tip:SetActive(false)
		self.node_list.role_bag_icon.animator:SetBool("is_full", false)
	end
end

function MainUIView:SetRootNodeActive(value)
	if Scene.Instance:GetSceneType() == SceneType.Common then
		self.lock_unvisble = false
	end

	if self.lock_unvisble then
		return
	end

	SafeBaseView.SetRootNodeActive(self, value)

	if IsNil(self.ui_clear_zdepth_img) then
		self.ui_clear_zdepth_img = self.root_node.transform:Find("UIClearZDepth"):GetComponent(typeof(UnityEngine.UI.Image))
	end

	self.ui_clear_zdepth_img.enabled = value
end

function MainUIView:SetRootPointActive(show)
	if Scene.Instance then
		local scene_typ = Scene.Instance:GetSceneType()
		if scene_typ == SceneType.Field1v1 then
			return
		end

		if self.root_node_transform then
			self.root_node_transform.gameObject:SetActive(show)
		end
	end
	if self.mask_bg and not self.hide_mask then
		self.mask_bg:SetActive(show)
	end
end

function MainUIView:GetExpView()
	-- body
	return self.main_ui_exp_view
end

function MainUIView:TextWarmUp()
	local text_loader = AllocAsyncLoader(self, "TextPreload")
	text_loader:SetParent(G_UIObjLayer)
	text_loader:Load("misc_prefab", "TextPreload",
		function(text)
			if nil ~= text then
				local config_text = require("config/config_text")
				text.transform:Find("hkhtW7(P)18"):GetComponent(typeof(UnityEngine.UI.Text)).text = config_text
				text.transform:Find("hkhtW7(P)20"):GetComponent(typeof(UnityEngine.UI.Text)).text = config_text
			end
        end)
end

function MainUIView:SetTransparentMaskActive(is_active)
	self.node_list["TransparentMaskRoot"].image.enabled = is_active
end

function MainUIView:GetMainTopBtnObj(key)
	return self.main_icon_node_list_top[key]
end

function MainUIView:SetMainTopBtnObj(key, data)
	self.main_icon_node_list_top[key] = data
end

function MainUIView:ClearMainTopBtnObj()
	self.main_icon_node_list_top = {}
end

function MainUIView:GetMoveDisIson()
	return self.move_dis_ison
end

function MainUIView:SetMoveDisIson(move_dis_ison)
	self.move_dis_ison = move_dis_ison
end

function MainUIView:GetMainIconNodeListObj(key)
	return self.main_icon_node_list[key]
end

function MainUIView:SetMainIconNodeListObj(key, data)
	self.main_icon_node_list[key] = data
end

function MainUIView:ClearMainIconNodeListObj()
	self.main_icon_node_list = {}
end

function MainUIView:FunOpenReBindRed(fun_name)
	if not self.unopen_red_fun then
		return
	end

	if self.unopen_red_fun[fun_name] then
		RemindManager.Instance:Bind(self.remind_change, self.unopen_red_fun[fun_name])
		self.unopen_red_fun[fun_name] = nil
	end

	if fun_name == FunName.ShouChong then
		ServerActivityWGCtrl.Instance:SetFirstRechargeMainUiIconVisible()
	end

	if fun_name == FunName.ExpPoolView then
		self:FlushBtnExpPoolViewState()
	end

	if fun_name == FunName.XiuZhenRoadView or fun_name == FunName.XiuXianShiLian then
		self:FlushSpecialACTPanelState()
	end
end

-- 点击自动寻路的小飞鞋
function MainUIView:OnClickXunluFly()
	local scene_id = MoveCache.scene_id
	local x = MoveCache.x
	local y = MoveCache.y
	if scene_id and x and y and scene_id ~= 0 then
		TaskWGCtrl.Instance:JumpFly(scene_id, x, y)
	end
end

function MainUIView:FlushFlyShoesBtn()
	self.node_list["fly_shoes_btn"]:SetActive(VipWGData.Instance:GetVipLevel() >= 1 and Scene.Instance:GetSceneType() == SceneType.Common)
end

function MainUIView:CheckAuditVersion()
	-- 审核服
	if IS_AUDIT_VERSION and self.node_list then
		self.node_list["ButtonMode"]:SetActive(false)
		self.node_list["ChatButtons"]:SetActive(false)
		self.node_list["task_btn_trans"]:SetActive(false)
	end
end

-- 不想重构那些index了
function MainUIView:GetViewResourceGameObjByIndex(index)
	local cfg = self.config_tab[index]
	if nil == cfg then
		return nil
	end

	return self:GetViewResourceGameObj("uis/view/main_ui_prefab", cfg[1])
end

-- 【轻功状态】 界面信息隐藏
-- 2、3、4 左 5、16 下 6 部分消失 8 上 9消失 10 状态机 11、 12、13消失 15红包提示
local SetFlyTaskIsHideMainUiIsOn = false
function MainUIView:SetFlyTaskIsHideMainUi(isOn, is_move_joystick)
	if SetFlyTaskIsHideMainUiIsOn == isOn then
		return
	end
	SetFlyTaskIsHideMainUiIsOn = isOn

	local gameobj = nil

	if is_move_joystick then
		gameobj = self:GetViewResourceGameObjByIndex(3)
		if gameobj then
			gameobj.transform:DOLocalMoveY(isOn and -768 or 0, 0.5)
		end

		-- 极简模式需要隐藏摇杆不然点击摇杆区域还是会被拉回来
		self:HideJoystick(not isOn)
	end

	-- 右移
	for i,v in ipairs({4}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveX(isOn and 1334 or 0, 0.5)


			
		end
	end

	-- 左移
	for i,v in ipairs({5, 15}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveX(isOn and -1334 or 0, 0.5)
		end
	end

	-- 下移
	for i,v in ipairs({6, 7, 8}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveY(isOn and -768 or 0, 0.5)
		end
	end

	-- 上移
	for i,v in ipairs({9, 10}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveY(isOn and 768 or 0, 0.5)
		end
	end

	-- 显隐
	for i,v in ipairs({12, 13, 14}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj:SetActive(not isOn)
		end
	end

	self:SetMianUITargetState()
	VipTyWGCtrl.Instance:SetPreShowRootNodeActive(not isOn)
	self.node_list.RightBttomPanel.animator:SetBool("fold", isOn)
	self.node_list.PlayerButtons.canvas_group.blocksRaycasts = not isOn
	if isOn and self.toggle_menu.toggle.isOn then
		self.toggle_menu.toggle.isOn = false
	end
end

-- 用于给投放录视频用，其他地方禁止使用，枚举也不加了，直接写死
function MainUIView:MianUIVisibChange(isOn, hide_type)
	if isOn then
		ViewManager.Instance:CloseAll()
		MainuiWGCtrl.Instance:OpenMainUiModeView()
	end

	local gameobj = nil
	-- 右移
	for i,v in ipairs({4}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveX(isOn and 1334 or 0, 0.5)
		end
	end

	-- 左移
	for i,v in ipairs({3, 5, 15}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveX(isOn and -1334 or 0, 0.5)
		end
	end

	-- 下移
	for i,v in ipairs({3, 7, 8}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveY(isOn and -768 or 0, 0.5)
		end
	end

	local tab = {6, 9}
	if hide_type == 2 then
		tab = {9}
		gameobj = self:GetViewResourceGameObjByIndex(6)
		if gameobj then
			gameobj.transform:DOLocalMoveY(0, 0.5)
		end
	end

	-- 上移
	for i,v in ipairs(tab) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveY((isOn) and 768 or 0, 0.5)
		end
	end

	for i,v in ipairs({12, 13, 14}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj:SetActive(not isOn)
		end
	end

	self:SetMianUITargetState()
	VipTyWGCtrl.Instance:SetPreShowRootNodeActive(not isOn)
	self.node_list.RightBttomPanel.animator:SetBool("fold", isOn and hide_type == 1)
	self.node_list.JumpContent:SetActive(not isOn and hide_type == 1)
	self:SetVIPBtnState(not isOn)

	-- 极简模式需要隐藏摇杆不然点击摇杆区域还是会被拉回来
	self:HideJoystick(not isOn)

	if isOn and self.toggle_menu.toggle.isOn then
		self.toggle_menu.toggle.isOn = false
	end
end

-- 【巡游状态】 界面信息隐藏
local XunYouMianUiHideFlag = false
function MainUIView:SetXunYouMainUiHideState(isOn)
	if XunYouMianUiHideFlag == isOn then
		return
	end

	XunYouMianUiHideFlag = isOn
	local gameobj = nil
	for i,v in ipairs({4}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveX(isOn and 1334 or 0, 0.5)
		end
	end

	for i,v in ipairs({5, 15}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveX(isOn and -1334 or 0, 0.5)
		end
	end

	for i,v in ipairs({3, 8, 11}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveY(isOn and -768 or 0, 0.5)
		end
	end

	for i,v in ipairs({6, 9}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveY(isOn and 768 or 0, 0.5)
		end
	end

	for i,v in ipairs({12, 13, 14}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj:SetActive(not isOn)
		end
	end

	self.node_list["btn_guaji"]:SetActive(not isOn)
	VipTyWGCtrl.Instance:SetPreShowRootNodeActive(not isOn)
	--极简模式需要隐藏摇杆不然点击摇杆区域还是会被拉回来
	self:HideJoystick(not isOn)

	if isOn and self.toggle_menu.toggle.isOn then
		self.toggle_menu.toggle.isOn = false
	end
end

-- 【观战模式】 界面信息隐藏
local observation_flag = false
function MainUIView:SetObservationState()
	local isOn = WorldsNO1WGData.Instance:IsObservationStatus()
	if observation_flag == isOn then
		return
	end

	observation_flag = isOn
	local gameobj = nil

	for i,v in ipairs({4}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveX(isOn and 1334 or 0, 0.5)
		end
	end

	for i,v in ipairs({3, 5}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveX(isOn and -1334 or 0, 0.5)
		end
	end

	for i,v in ipairs({8, 11}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveY(isOn and -768 or 0, 0.5)
		end
	end

	for i,v in ipairs({6}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveY(isOn and 768 or 0, 0.5)
		end
	end

	for i,v in ipairs({12, 13, 14}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj:SetActive(not isOn)
		end
	end


	self.node_list["ShrinkLeftButton"]:SetActive(not isOn)
	VipTyWGCtrl.Instance:SetPreShowRootNodeActive(not isOn)
	self:SetCameraModeBtnState(not isOn)
	self:SetMianUIPlayerState(not isOn)

	--极简模式需要隐藏摇杆不然点击摇杆区域还是会被拉回来
	self:HideJoystick(not isOn)

	if isOn and self.toggle_menu.toggle.isOn then
		self.toggle_menu.toggle.isOn = false
	end
end

-- 【仙盟BOSS 摇骰子】界面信息隐藏
local GuildBossShaiZiUiHideFlag = false
function MainUIView:SetGuildBossShaiZiMainUiHideState(isOn)
	if GuildBossShaiZiUiHideFlag == isOn then
		return
	end

	GuildBossShaiZiUiHideFlag = isOn
	local gameobj = nil

	gameobj = self:GetViewResourceGameObjByIndex(11)
	if gameobj then
		gameobj:SetActive(not isOn)
	end

	self.node_list["Menu"]:SetActive(not isOn)
	self.node_list["OtherButten"]:SetActive(not isOn)
	if isOn and self.toggle_menu.toggle.isOn then
		self.toggle_menu.toggle.isOn = false
	end
end

function MainUIView:SetFuPingIsHideMainUi(isOn)
	local gameobj = nil

	-- 右移
	self.node_list.OtherButten.rect:DOAnchorPosX(isOn and 1334 or 0, 0.5)

	-- 左移
	for i,v in ipairs({5, 15}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveX(isOn and -1334 or 0, 0.5)
		end
	end

	-- 下移
	for i,v in ipairs({3, 6, 7, 8, 11}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveY(isOn and -768 or 0, 0.5)
		end
	end

	-- 上移
	for i,v in ipairs({9, 10}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveY(isOn and 768 or 0, 0.5)
		end
	end

	-- 显隐
	for i,v in ipairs({12, 13, 14}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj:SetActive(not isOn)
		end
	end

	self:SetMianUITargetState()
	VipTyWGCtrl.Instance:SetPreShowRootNodeActive(not isOn)
end

-- 【对话状态】 界面信息隐藏
local DuiHuaMianUiHideFlag = false
function MainUIView:SetDuiHuaMainUiHideState(isOn)
	if DuiHuaMianUiHideFlag == isOn then
		return
	end

	DuiHuaMianUiHideFlag = isOn
	local gameobj = nil
	for i,v in ipairs({4}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveX(isOn and 1334 or 0, 0.5)
		end
	end

	for i,v in ipairs({5, 15}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveX(isOn and -1334 or 0, 0.5)
		end
	end

	for i,v in ipairs({3, 7, 8, 11, 6}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveY(isOn and -768 or 0, 0.5)
		end
	end

	for i,v in ipairs({9}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveY(isOn and 768 or 0, 0.5)
		end
	end

	for i,v in ipairs({12, 13, 14}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj:SetActive(not isOn)
		end
	end

	self.node_list["btn_guaji"]:SetActive(not isOn)
	VipTyWGCtrl.Instance:SetPreShowRootNodeActive(not isOn)
	--极简模式需要隐藏摇杆不然点击摇杆区域还是会被拉回来
	self:HideJoystick(not isOn)

	if isOn and self.toggle_menu.toggle.isOn then
		self.toggle_menu.toggle.isOn = false
	end
end

-- 【渡劫状态】 界面信息隐藏
local DujieMianUiHideFlag = false
function MainUIView:SetDujieMainUiHideState(isOn)
	if DujieMianUiHideFlag == isOn then
		return
	end

	DujieMianUiHideFlag = isOn
	local gameobj = nil
	for i,v in ipairs({4}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveX(isOn and 1334 or 0, 0.5)
		end
	end

	for i,v in ipairs({5, 15}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveX(isOn and -1334 or 0, 0.5)
		end
	end

	for i,v in ipairs({3, 7, 8, 11, 6}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveY(isOn and -768 or 0, 0.5)
		end
	end

	for i,v in ipairs({9}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj.transform:DOLocalMoveY(isOn and 768 or 0, 0.5)
		end
	end

	for i,v in ipairs({12, 13, 14}) do
		gameobj = self:GetViewResourceGameObjByIndex(v)
		if gameobj then
			gameobj:SetActive(not isOn)
		end
	end

	self.node_list["btn_guaji"]:SetActive(not isOn)
	VipTyWGCtrl.Instance:SetPreShowRootNodeActive(not isOn)
	--极简模式需要隐藏摇杆不然点击摇杆区域还是会被拉回来
	self:HideJoystick(not isOn)

	-- if isOn and self.toggle_menu.toggle.isOn then
	-- 	self.toggle_menu.toggle.isOn = false
	-- end
end

function MainUIView:GetMainWuHunUIEffectRoot()
	return self.node_list.WuHunEffectRoot
end

--[[设置低于主界面的UI特效
	** 因为已出包，现有的后处理c#逻辑不支持扩展所需效果，先特效层叠处理
	** 特效的UIEffect组件 参数OrderOffset 需要设置负值
--]]
function MainUIView:SetUnderMainUIEffect(effect_bundle, effect_asset)
	self.node_list["under_mainui_effect"]:CustomSetActive(true)
	self.node_list["under_mainui_effect"]:ChangeAsset(effect_bundle, effect_asset)
end

function MainUIView:HideUnderMainUIEffect()
	self.node_list["under_mainui_effect"]:CustomSetActive(false)
end

function MainUIView:SetAransformationMainUIEffect(effect_bundle, effect_asset)
	self.node_list["aransformation_effect"]:CustomSetActive(true)
	self.node_list["aransformation_effect"]:ChangeAsset(effect_bundle, effect_asset)
end

function MainUIView:HideAransformationMainUIEffect()
	self.node_list["aransformation_effect"]:CustomSetActive(false)
end

function MainUIView:SetSnapShotBackgroundShow(is_show)
	if self.SnapShotBackground_obj then
		self.SnapShotBackground_obj:SetActive(is_show)
		ViewManager.Instance:SetSnapShotBackgroundState(is_show)
	end
end

function MainUIView:HadSnapShotBackground()
	return not IsNil(self.SnapShotBackground_obj)
end

-- 头像改变
function MainUIView:ChangeHeadIcon()
	self:PlayerChangeHeadIcon()
	self:FPChangeHeadIcon()
end

function MainUIView:ChangePosByTransFerEquipCollect(is_open_equip_collect)
	-- local root_pos_y = is_open_equip_collect and 282 or 150
	-- RectTransform.SetAnchoredPositionXY(self.node_list.player_info_widget_root.rect, 0, root_pos_y)

	local sec_kill_boss_y = is_open_equip_collect and -60 or -280
	RectTransform.SetAnchoredPositionXY(self.node_list.sec_kill_boss_btn.rect, 0, sec_kill_boss_y)
end

function MainUIView:Hide()
	self.view_root_rect:SetActive(true)
end

function MainUIView:Show()
	self.view_root_rect:SetActive(true)
end