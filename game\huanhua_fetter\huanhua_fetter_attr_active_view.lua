HuanHuaFetterAttrActiveView = HuanHuaFetterAttrActiveView or BaseClass(SafeBaseView)

function HuanHuaFetterAttrActiveView:SetDataInfo(data)
    self.data_info = data
end

function HuanHuaFetterAttrActiveView:__init()
    self.view_layer = UiLayer.Normal
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true

    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
    self:AddViewResource(0, "uis/view/huanhua_fetter_ui_prefab", "layout_fetters_show_view")
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_light_common_top_panel")
end

function HuanHuaFetterAttrActiveView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.HuanHuaFetter.AttrShowTitleBiewName

    local bundle, asset = ResPath.GetRawImagesPNG("a3_jb_bj2")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

    if not self.model then
        self.model = RoleModel.New()

        local display_data = {
			parent_node = self.node_list["fs_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}

		self.model:SetRenderTexUI3DModel(display_data)

        -- self.model:SetUISceneModel(self.node_list["fs_model"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
        -- self:AddUiRoleModel(self.model)
    end

    if not self.suit_num_list then
        self.suit_num_list = AsyncListView.New(HuanHuaFetterSuitNumListItemRender, self.node_list.suit_list)
    end

    if not self.attr_list then
        self.attr_list = AsyncBaseGrid.New()
        self.attr_list:CreateCells(
            {col = 2,
            change_cells_num = 1,
            list_view = self.node_list.attr_list,
			assetBundle = "uis/view/huanhua_fetter_ui_prefab",
            assetName = "ph_attr_list_tem",
            itemRender = HuanHuaFetterSuitAttrListItemRender}
        )
        self.attr_list:SetStartZeroIndex(false)
    end

    if not self.item then
        self.item = ItemCell.New(self.node_list.item_pos)
    end

    if not self.fc_bom_list then
        self.fc_bom_list = AsyncListView.New(HuanHuaFetterSuitBomItemRender, self.node_list.fc_bom_list)
    end

    XUI.AddClickEventListener(self.node_list.btn_fs_active, BindTool.Bind(self.OnClickAct, self))
    XUI.AddClickEventListener(self.node_list.btn_fs_jump, BindTool.Bind(self.OnClickJump, self))
    XUI.AddClickEventListener(self.node_list.btn_huanhua, BindTool.Bind(self.OnClickHuanHua, self))
    XUI.AddClickEventListener(self.node_list.btn_yihuanhua, BindTool.Bind(self.OnClickYiHuanHua, self))
end

function HuanHuaFetterAttrActiveView:ReleaseCallBack()
    if self.model then
        self.model:DeleteMe()
        self.model = nil
    end

    if self.suit_num_list then
        self.suit_num_list:DeleteMe()
        self.suit_num_list = nil
    end

    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end

    if self.attr_list then
        self.attr_list:DeleteMe()
        self.attr_list = nil
    end

    if self.fc_bom_list then
        self.fc_bom_list:DeleteMe()
        self.fc_bom_list = nil
    end
end

function HuanHuaFetterAttrActiveView:ShowIndexCallBack()
    self:FlushModel()
end

function HuanHuaFetterAttrActiveView:OnFlush()
    if IsEmptyTable(self.data_info) then
        return
    end

    local act_item_id = NewHuanHuaFetterWGData.Instance:GetActItemId(self.data_info)
    if nil == act_item_id or act_item_id <= 0 then
        return
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(act_item_id)
    if not IsEmptyTable(item_cfg) then
        self.node_list.name.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
        self.node_list.use_level.text.text = string.format(Language.Tip.ShiYongDengJiNew, F2_TIPS_COLOR.SOCRE, RoleWGData.GetLevelString(item_cfg.limit_level))
        self.node_list.item_desc.text.text = ItemWGData.Instance:GetItemConstDesc(item_cfg)
        self.node_list.model_name.text.text = item_cfg.name

        local need_get_sys, sys_type, replace_idx, show_cap_type = ItemShowWGData.Instance:GetIsNeedSysAttr(act_item_id, item_cfg.sys_attr_cap_location)
        local attr_desc, capability, attr_list = ItemShowWGData.Instance:GetItemAttrDescAndCap({item_id = act_item_id}, sys_type, false)
        self.node_list.cap_value.text.text = capability
        self.item:SetData({item_id = act_item_id})
        self.attr_list:SetDataList(attr_list)
    end

    local attr_data_list = NewHuanHuaFetterWGData.Instance:GetAttrDataListCfg(self.data_info.seq)
    local target_attr_data_list = {}
    local active_num, complete_num = NewHuanHuaFetterWGData.Instance:GetHuanHuaFetterActiveNum(self.data_info.seq)

    for k, v in pairs(attr_data_list) do
        table.insert(target_attr_data_list, {active_num = active_num, cfg = v})
    end

    self.suit_num_list:SetDataList(target_attr_data_list)

    local bom_data_list = NewHuanHuaFetterWGData.Instance:GetActivationDataListCfgBySeq(self.data_info.seq)
    local target_bom_data_iist = {}
    for k, v in pairs(bom_data_list) do
        if v.is_reward_item ~= 1 then
            table.insert(target_bom_data_iist, v)
        end
    end

    self.fc_bom_list:SetDataList(target_bom_data_iist)

    -- 判断能否激活套装
    local active_data = {}

    for k, v in pairs(attr_data_list) do
        if v.num <= complete_num and active_num < v.num then
            active_data = v
            break
        end
    end

    -- self.node_list.btn_fs_active:SetActive(not IsEmptyTable(active_data))

    local remind = not IsEmptyTable(active_data)
    self.node_list.btn_fs_active_remind:CustomSetActive(remind)
    XUI.SetButtonEnabled(self.node_list.btn_fs_active, remind)

    -- local active = NewHuanHuaFetterWGData.Instance:GetSuitPartStateByData(self.data_info.seq, self.data_info.part)
    -- local is_active = (active == REWARD_STATE_TYPE.FINISH or active == REWARD_STATE_TYPE.CAN_FETCH)
    -- local is_huanhua = NewHuanHuaFetterWGData.Instance:IsActItemIsHuanHua(self.data_info)
    -- self.node_list.btn_huanhua:CustomSetActive(is_active and not is_huanhua)
    -- self.node_list.btn_yihuanhua:CustomSetActive(is_active and is_huanhua)
end

function HuanHuaFetterAttrActiveView:FlushModel()
    if self.model then
        self.model:RemoveAllModel()
    end

    if not IsEmptyTable(self.data_info) and self.model then
        if self.data_info.type == WARDROBE_PART_TYPE.MOUNT then
            local act_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(self.data_info.param1)
            if act_cfg then
                local appe_id = act_cfg.appe_image_id or act_cfg.active_id
                local bundle, asset = ResPath.GetMountModel(appe_id)
                self.model:SetMainAsset(bundle, asset, function ()
                    self.model:PlayMountAction()
                end)
            end
        elseif self.data_info.type == WARDROBE_PART_TYPE.FASHION then
            local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(self.data_info.param1, self.data_info.param2)
            if fashion_cfg then
                local res_id = fashion_cfg.resouce
                local bundle, asset
                -- 法宝
                if self.data_info.param1 == SHIZHUANG_TYPE.FABAO then
                   bundle, asset = ResPath.GetFaBaoModel(res_id)
                elseif self.data_info.param1 == SHIZHUANG_TYPE.HALO then
                    bundle, asset = ResPath.GetHaloModel(res_id)
                end
    
                self.model:SetMainAsset(bundle, asset)
            end
        end

        local other_cfg = NewHuanHuaFetterWGData.Instance:GetOtherCfg()
        if other_cfg then
            local pos = other_cfg.pos
            if pos and pos ~= "" then
                local pos_data = Split(pos, "|")
                self.model:SetRTAdjustmentRootLocalPosition(tonumber(pos_data[1]) or 0, tonumber(pos_data[2]) or 0, tonumber(pos_data[3]) or 0)
            end

            local rot = other_cfg.rot
            if rot and rot ~= "" then
                local rot_data = Split(rot, "|")
                self.model:SetRTAdjustmentRootLocalRotation(tonumber(rot_data[1]) or 0, tonumber(rot_data[2]) or 0, tonumber(rot_data[3]) or 0)
            end

            local scale = other_cfg.scale
            if scale and scale ~= "" then
                self.model:SetRTAdjustmentRootLocalScale(scale)
            end
        end
    end
end

function HuanHuaFetterAttrActiveView:OnClickAct()
    if IsEmptyTable(self.data_info) then
        return
    end

    local bom_data_list = NewHuanHuaFetterWGData.Instance:GetActivationDataListCfgBySeq(self.data_info.seq)
    if not IsEmptyTable(bom_data_list) then
        for k, v in pairs(bom_data_list) do
            local active = NewHuanHuaFetterWGData.Instance:GetSuitPartStateByData(v.seq, v.part)

            if active == REWARD_STATE_TYPE.CAN_FETCH then
                NewHuanHuaFetterWGCtrl.Instance:SendHuanHuaFetterRequest(HUANHUA_FETTER_OPERATE_TYPE.ACTIVE_PART, v.seq, v.part)
            end
        end
    end

    -- NewHuanHuaFetterWGCtrl.Instance:SendHuanHuaFetterRequest(HUANHUA_FETTER_OPERATE_TYPE.ACTIVE_PART, self.data_info.seq, self.data_info.part)

    -- local attr_data_list = NewHuanHuaFetterWGData.Instance:GetAttrDataListCfg(self.data_info.seq)
    -- local active_num, complete_num = NewHuanHuaFetterWGData.Instance:GetHuanHuaFetterActiveNum(self.data_info.seq)
    -- local active_data = {}

    -- for k, v in pairs(attr_data_list) do
    --     if v.num <= complete_num and active_num < v.num then
    --         active_data = v
    --         break
    --     end
    -- end

    -- if not IsEmptyTable(active_data) then
    --     HuanHuaFetterWGCtrl.Instance:SendHuanHuaFetterRequest(HUANHUA_FETTER_OPERATE_TYPE.ACTIVE_PART, self.data_info.seq, self.data_info.part)
    -- end
end

function HuanHuaFetterAttrActiveView:OnClickJump()
    if self.data_info.act_jump_path and ""~= self.data_info.act_jump_path then
        FunOpen.Instance:OpenViewNameByCfg(self.data_info.act_jump_path)
   end
end

function HuanHuaFetterAttrActiveView:OnClickHuanHua()
    -- 坐骑幻化
    if self.data.type == WARDROBE_PART_TYPE.MOUNT then
        local cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(self.data.param1)
        -- 使用
        local act_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(self.data.param1)
        if act_cfg then
            local appe_id = act_cfg.appe_image_id or act_cfg.active_id
            NewAppearanceWGCtrl.Instance:SendMountReq(MOUNT_OPERA_TYPE.USE_IMAGE, appe_id, act_cfg.image_id or 0)
        end
    elseif self.data.type == WARDROBE_PART_TYPE.FASHION then
        NewAppearanceWGCtrl.Instance:OnUseFashion(self.data.param1, self.data.param2, 1)
    end
end

function HuanHuaFetterAttrActiveView:OnClickYiHuanHua()
    
end
-------------------------------------------------HuanHuaFetterSuitNumListItemRender------------------------------------------------
HuanHuaFetterSuitNumListItemRender = HuanHuaFetterSuitNumListItemRender or BaseClass(BaseRender)

function HuanHuaFetterSuitNumListItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local active = self.data.active_num >= self.data.cfg.num
    local color = active and COLOR3B.WHITE or "#3b3a38"
    self.node_list.desc_name.text.text = ToColorStr(string.format(Language.HuanHuaFetter.SuitAttrInfoTitleStr, self.data.cfg.num), color)

    local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(self.data.cfg.attr_id1)
    local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str, true)
    local value_str = AttributeMgr.PerAttrValue(attr_str, self.data.cfg.attr_value1)
    local attr_color = active and "#79FA82" or "#3b3a38"
    self.node_list.desc_value.text.text = ToColorStr(string.format(Language.HuanHuaFetter.SuitAttrInfoContentStr, attr_name, attr_color, "+" .. value_str), color)
end

-------------------------------------------------HuanHuaFetterSuitAttrListItemRender------------------------------------------------
HuanHuaFetterSuitAttrListItemRender = HuanHuaFetterSuitAttrListItemRender or BaseClass(BaseRender)

function HuanHuaFetterSuitAttrListItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.attr_name.text.text = self.data.attr_name
    self.node_list.attr_value.text.text = self.data.value_str
end

-------------------------------------------------HuanHuaFetterSuitBomItemRender------------------------------------------------
HuanHuaFetterSuitBomItemRender = HuanHuaFetterSuitBomItemRender or BaseClass(BaseRender)

function HuanHuaFetterSuitBomItemRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_item, BindTool.Bind(self.OnClickItem, self))
end

function HuanHuaFetterSuitBomItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local act_item_id = NewHuanHuaFetterWGData.Instance:GetActItemId(self.data)
    if nil == act_item_id or act_item_id <= 0 then
        return
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(act_item_id)
    if not IsEmptyTable(item_cfg) then
        self.node_list.name.text.text = item_cfg.name

        local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
        self.node_list.icon.image:LoadSprite(bundle, asset)
    end

    local active = NewHuanHuaFetterWGData.Instance:GetSuitPartStateByData(self.data.seq, self.data.part)
    local is_active = (active == REWARD_STATE_TYPE.FINISH or active == REWARD_STATE_TYPE.CAN_FETCH)
    -- local is_huanhua = NewHuanHuaFetterWGData.Instance:IsActItemIsHuanHua(self.data)

    self.node_list.act_bg:CustomSetActive(is_active)
    self.node_list.lock_bg:CustomSetActive(not is_active)
    self.node_list.lock:CustomSetActive(not is_active)
    -- self.node_list.name_bg:CustomSetActive(is_active)
end

function HuanHuaFetterSuitBomItemRender:OnClickItem()
    local act_item_id = NewHuanHuaFetterWGData.Instance:GetActItemId(self.data)

    if nil == act_item_id or act_item_id <= 0 then
        return
    end
    
    TipWGCtrl.Instance:OpenItem({item_id = act_item_id})
end