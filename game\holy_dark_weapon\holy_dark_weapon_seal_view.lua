function HolyDarkWeaponView:InitHolyDarkSealView()
    if not self.seal_cell_list then
        self.seal_cell_list = {}
        for i = 0, 3 do
            self.seal_cell_list[i] = HolyDarkSealCell.New(self.node_list["seal_act_cell_" .. i])
            self.seal_cell_list[i]:SetIndex(i)
        end

        if nil == self.seal_relic_show_model then
            self.cache_seal_seq = -1
            self.seal_relic_show_model = RoleModel.New()
            local display_data = {
                parent_node = self.node_list["seal_display"],
                camera_type = MODEL_CAMERA_TYPE.BASE,
                -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
                rt_scale_type = ModelRTSCaleType.M,
                can_drag = true,
            }
            
            self.seal_relic_show_model:SetRenderTexUI3DModel(display_data)
            -- self.seal_relic_show_model:SetUI3DModel(self.node_list["seal_display"].transform,
            --                             self.node_list["seal_EventTriggerListener"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
            self:AddUiRoleModel(self.seal_relic_show_model)
        end
    end

    XUI.AddClickEventListener(self.node_list["deb_lock_btn"], BindTool.Bind(self.ClickDebLock, self))
    XUI.AddClickEventListener(self.node_list["onekey_buy_btn"], BindTool.Bind(self.ClickOneKeyBuy, self))
end

function HolyDarkWeaponView:DeleteHolyDarkSealView()
    if self.seal_cell_list then
        for k, v in pairs(self.seal_cell_list) do
            v:DeleteMe()
        end
        self.seal_cell_list = nil
    end

    if self.seal_relic_show_model then
        self.seal_relic_show_model:DeleteMe()
        self.seal_relic_show_model = nil
    end

    self.cache_seal_seq = nil
end

function HolyDarkWeaponView:FlushHolyDarkSealView()
    local relic_seq = HolyDarkWeaponWGCtrl.Instance:GetHolyDarkViewSelectWeaponSeq()
    if relic_seq and relic_seq >= 0 then
        local seal_data = HolyDarkWeaponWGData.Instance:GetSealCfgBySeq(relic_seq)
        if not IsEmptyTable(seal_data) then
            for k, v in pairs(self.seal_cell_list) do
                v:SetData(seal_data[k] or {})
            end
        end

        local relic_cfg = HolyDarkWeaponWGData.Instance:GetWeaponCfgBySeq(relic_seq)

        if IsEmptyTable(relic_cfg) then
            return
        end

        self.node_list.seal_weapon_name.text.text = relic_cfg and relic_cfg.name
        self:FlushHolyDarkSealAttr(seal_data, relic_cfg)

        local show_lock_btn = true
        local have_seal = HolyDarkWeaponWGData.Instance:GetHaveSealActBySeq(relic_seq)
        local active_state = HolyDarkWeaponWGData.Instance:GetRelicSateBySeq(relic_seq) --圣装是否激活
        for i = 0, 3 do
            local seal_state = HolyDarkWeaponWGData.Instance:GetSealDataBySealIndex(relic_seq, i) --封印是否激活
            if not seal_state then
                show_lock_btn = false
                break
            end
        end

        self.node_list.deb_lock_btn:SetActive(not active_state and show_lock_btn)
        self.node_list.onekey_buy_btn:SetActive(not have_seal)
        self.node_list.seal_no_active_part:SetActive(not active_state)

        local cap = HolyDarkWeaponWGData.Instance:GetRelicCapabilityBySeq(relic_seq)
        self.node_list.seal_cap_value.text.text = cap
        self.node_list.seal_capability_pa:SetActive(cap > 0)

        local all_price = RoleWGData.GetPayMoneyStr(relic_cfg.seal_total_price, relic_cfg.rmb_type, relic_cfg.rmb_seq)
        self.node_list.total_price_txt.text.text = string.format(Language.HolyDarkWeapon.TotalPriceTxt, all_price)

        self.node_list.zhe_text.text.text = relic_cfg.zhekou_word or ""

        self.node_list.seal_scroll_view.scroll_rect.verticalNormalizedPosition = 1
        --self.node_list.seal_model_part:SetActive(active_state)
        self:FlushSealShowModel(relic_cfg)
    end
end

function HolyDarkWeaponView:FlushSealShowModel(relic_cfg)
    local relic_seq = HolyDarkWeaponWGCtrl.Instance:GetHolyDarkViewSelectWeaponSeq()
    if self.cache_seal_seq ~= relic_seq then
        local bundle, asset = ResPath.GetRelicModel(relic_cfg.model_id)
        self.seal_relic_show_model:SetMainAsset(bundle, asset)
        self.seal_relic_show_model:FixToOrthographic(self.root_node_transform)
        self.seal_relic_show_model:PlayJianZhenAction()
        self.cache_seal_seq = relic_cfg.seq
    end
end

function HolyDarkWeaponView:ClickDebLock()
    local relic_seq = HolyDarkWeaponWGCtrl.Instance:GetHolyDarkViewSelectWeaponSeq()
    if relic_seq and relic_seq >= 0 then
        local active_state = HolyDarkWeaponWGData.Instance:GetRelicSateBySeq(relic_seq) --圣装是否激活
        if active_state then
            TipWGCtrl.Instance:ShowSystemMsg(Language.HolyDarkWeapon.SealIsAct)
            return
        else
            HolyDarkWeaponWGCtrl.Instance:SendCSHolyDarkWeaponRequest(RELIC_OPERATE_TYPE.RELIC_ACTIVE, relic_seq)
        end
    end
end

function HolyDarkWeaponView:ClickOneKeyBuy()
    local relic_seq = HolyDarkWeaponWGCtrl.Instance:GetHolyDarkViewSelectWeaponSeq()
    if relic_seq and relic_seq >= 0 then
        local have_seal = HolyDarkWeaponWGData.Instance:GetHaveSealActBySeq(relic_seq)
        local relic_cfg = HolyDarkWeaponWGData.Instance:GetWeaponCfgBySeq(relic_seq)
        if not have_seal and (not IsEmptyTable(relic_cfg)) then
            HolyDarkWeaponWGCtrl.Instance:OpenHolyDarkSealOnceShopView(relic_cfg)
        end
    end
end

function HolyDarkWeaponView:FlushHolyDarkSealAttr(seal_data, relic_cfg)
    if IsEmptyTable(seal_data) or IsEmptyTable(relic_cfg) then
        return
    end

    for i = 0, 3 do
        local attr_list = ItemShowWGData.Instance:OutStrAttrListAndCapalityByAttrId(seal_data[i], "attr_id", "attr_value")
        local seal_state = HolyDarkWeaponWGData.Instance:GetSealDataBySealIndex(relic_cfg.seq, i) --封印是否激活
        local color = seal_state and  "#9DF5A7" or "#ffffff"
        self.node_list["seal_attr_" .. i].text.text = attr_list[1].attr_name .. "  " .. ToColorStr("+" .. attr_list[1].value_str, color)
        self.node_list["seal_attr_lock_" .. i]:SetActive(not seal_state)
    end

    self.node_list.seal_skill_desc.text.text = string.format(Language.HolyDarkWeapon.SealSkillDesc, relic_cfg.seal_skill_name, relic_cfg.seal_skill_desc)

end

function HolyDarkWeaponView:PlayRelicJiHuoEffect()
    if self.node_list["jihuo_succ_pos"] then
        TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo, is_success = true, pos = Vector2(0, 0),
                                parent_node = self.node_list["jihuo_succ_pos"]})
    end
end

function HolyDarkWeaponView:DoHolyDarkSealViewAnim()
    local tween_info = UITween_CONSTS.HolyDark

    for i = 0, 3 do
        RectTransform.SetLocalScale(self.node_list["seal_act_cell_"..i].rect, 0)
    end

    for i = 0, 3 do
        self.node_list["seal_act_cell_"..i].rect:DOScale(Vector3(1, 1, 1), tween_info.scale_time)
    end
end

------------
HolyDarkSealCell = HolyDarkSealCell or BaseClass(BaseRender)

function HolyDarkSealCell:LoadCallBack()
    self.cost_item = ItemCell.New(self.node_list["cost_item"])
    XUI.AddClickEventListener(self.node_list["go_shop_btn"], BindTool.Bind(self.ClickBuyView, self))
    XUI.AddClickEventListener(self.node_list["act_btn"], BindTool.Bind(self.ClickAct, self))
    XUI.AddClickEventListener(self.node_list["item_get_btn"], BindTool.Bind(self.ClickBuyView, self))
end

function HolyDarkSealCell:__delete()
    if self.cost_item then
        self.cost_item:DeleteMe()
        self.cost_item = nil
    end
end

function HolyDarkSealCell:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local bundle, asset = ResPath.GetRawImagesPNG("a2_cbg_daoju_" .. self.data.seal_index)
    self.node_list.seal_img.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list.seal_img.raw_image:SetNativeSize()
    end)

    self.cost_item:SetData({item_id = self.data.cost_item_id})
    local seal_state = HolyDarkWeaponWGData.Instance:GetSealDataBySealIndex(self.data.relic_seq, self.data.seal_index) --封印是否激活
    self.node_list.cost_item:SetActive(not seal_state)
    self.node_list.cost_item:SetActive(not seal_state)
    local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.cost_item_id)
    self.node_list.item_get_btn:SetActive(not seal_state and item_num < self.data.cost_item_num)
    self.node_list.act_btn:SetActive(not seal_state and item_num >= self.data.cost_item_num)
    self.node_list.desc_bg:SetActive(not seal_state)
    local price = RoleWGData.GetPayMoneyStr(self.data.price, self.data.rmb_type, self.data.rmb_seq)
    self.node_list.price_txt.text.text = seal_state and Language.HolyDarkWeapon.ActiveTxt or string.format(Language.HolyDarkWeapon.PriceTxt, price)
end

function HolyDarkSealCell:ClickBuyView()
    if IsEmptyTable(self.data) then
        return
    end

    HolyDarkWeaponWGCtrl.Instance:OpenHolyDarkSealBuyView(self.data)
end

function HolyDarkSealCell:ClickAct()
    if IsEmptyTable(self.data) then
        return
    end

    local seal_state = HolyDarkWeaponWGData.Instance:GetSealDataBySealIndex(self.data.relic_seq, self.data.seal_index) --封印是否激活
    if seal_state then
        TipWGCtrl.Instance:ShowSystemMsg(Language.HolyDarkWeapon.SealIsAct)
        return
    end

    local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.cost_item_id)
    if item_num < self.data.cost_item_num then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Common.ItemNotEnough)
        return
    end

    HolyDarkWeaponWGCtrl.Instance:SendCSHolyDarkWeaponRequest(RELIC_OPERATE_TYPE.UNLOCK_SEAL, self.data.relic_seq, self.data.seal_index)
end

