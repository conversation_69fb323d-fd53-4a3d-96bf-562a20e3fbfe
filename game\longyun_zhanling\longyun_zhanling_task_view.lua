LongYunZhanLingTaskView = LongYunZhanLingTaskView or BaseClass(SafeBaseView)
function LongYunZhanLingTaskView:__init()
	self.view_style = ViewStyle.Half
	self:SetMaskBg()
	self.open_source_view = "BtnLongYunZhanLingTask"

	local bundle_name = "uis/view/longyun_zhanling_ui_prefab"
	self:AddViewResource(0, bundle_name, "layout_longyun_zhanling_task")

	self.cur_select_quality = COUNTRY_TASK_QUALITY.LOW
	self.cur_select_index = 1
end

function LongYunZhanLingTaskView:ReleaseCallBack()
	if self.task_list ~= nil then
		self.task_list:DeleteMe()
		self.task_list = nil
	end

	if self.item_list ~= nil then
		for i = 1, #self.item_list do
			self.item_list[i]:DeleteMe()
		end
	end

	if self.task_type_list ~= nil then
		self.task_type_list:DeleteMe()
		self.task_type_list = nil
	end

	self.item_list = nil
end

function LongYunZhanLingTaskView:OpenCallBack()
end

function LongYunZhanLingTaskView:CloseCallBack()
	self.cur_select_quality = COUNTRY_TASK_QUALITY.LOW
	self.cur_select_index = 1
end

function LongYunZhanLingTaskView:LoadCallBack()
	LongYunZhanLingWGCtrl.Instance:SendCSCountryOperate(COUNTRY_OPERATE_TYPE.COUNTRY_OPERATE_TYPE_BASE_INFO)
	LongYunZhanLingWGCtrl.Instance:SendCSCountryOperate(COUNTRY_OPERATE_TYPE.COUNTRY_OPERATE_TYPE_TASK_INFO)

	self.cur_select_quality = COUNTRY_TASK_QUALITY.LOW
	self.cur_select_index = 1

	XUI.AddClickEventListener(self.node_list.go_btn, BindTool.Bind(self.OnClickGoBtn, self))
	XUI.AddClickEventListener(self.node_list.left_btn, BindTool.Bind(self.OnClickLeftBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_close_windows, BindTool.Bind1(self.Close, self))

	self.task_list = AsyncListView.New(LongYunZhanLingTaskRender, self.node_list.list_task)
	self.task_type_list = AsyncListView.New(LongYunZhanLingTaskTypeRender, self.node_list.list_task_type)
	self.task_type_list:SetSelectCallBack(BindTool.Bind(self.OnSelectTask, self))
	self.task_type_list:SetDefaultSelectIndex(1)
end

function LongYunZhanLingTaskView:LoadIndexCallBack(index)
end

function LongYunZhanLingTaskView:ShowIndexCallBack(index)
end

function LongYunZhanLingTaskView:OnSelectTask(item)
	if item ~= nil then
		local data = item:GetData()
		if data ~= nil and data.quality ~= nil then
			if self.cur_select_quality ~= data.quality then
				self.cur_select_quality = data.quality
				self.cur_select_index = item:GetIndex()
				self:Flush(0, "task_type")
			end
		end
	end
end

function LongYunZhanLingTaskView:OnClickGoBtn()
	LongYunZhanLingWGCtrl.Instance:OpenZhanLingView()
end

function LongYunZhanLingTaskView:OnClickLeftBtn()
	SysMsgWGCtrl.Instance:ErrorRemind(Language.LongYunZhanLing.DesTaskTips)
end

function LongYunZhanLingTaskView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			self:FlushBottomInfo()
			self:FlushTypeList()
			self:FlushList()
			if v.sub_view_name ~= nil then
				if self.task_type_list ~= nil then
					self.task_type_list:JumpToIndex(tonumber(v.sub_view_name))
				end
			end
		elseif k == "task_type" then
			self:FlushList()
		elseif k == "jump_type" and v.task_type ~= nil then
			if self.task_type_list ~= nil then
				self.task_type_list:JumpToIndex(tonumber(v.task_type))
			end
		elseif k == "info" then
			self:FlushBottomInfo()
		end
	end

	local zhanlin_is_open = LongYunZhanLingWGData.Instance:CheckZhanLingIsOpen()
	self.node_list.go_btn:SetActive(zhanlin_is_open)

	local remind_get = LongYunZhanLingWGData.Instance:GetZhanLingRemind() == 1
	self.node_list.go_btn_remind:SetActive(remind_get)
end

function LongYunZhanLingTaskView:FlushTypeList()
	if self.task_type_list ~= nil then
		local cfg_list = LongYunZhanLingWGData.Instance:GetTaskQualityList()
		self.task_type_list:SetDataList(cfg_list)
		local select_index = LongYunZhanLingWGData.Instance:GetTaskJumpToIndex(self.cur_select_index)
		self.task_type_list:JumpToIndex(select_index)
	end
end

function LongYunZhanLingTaskView:FlushList()
	if self.task_list ~= nil then
		self.task_list:SetDataList(LongYunZhanLingWGData.Instance:GetTaskList(self.cur_select_quality))
	end
end

function LongYunZhanLingTaskView:FlushBottomInfo()
	local cur_fame = LongYunZhanLingWGData.Instance:GetLongYunZhanLingFame()
	self.node_list.text_cur_prestige.text.text = cur_fame
	local data_list, max_value = LongYunZhanLingWGData.Instance:GetTerritoryFameList()
	local slider_value, act_light = LongYunZhanLingWGData.Instance:GetCurSliderProgress()
	max_value = max_value == 0 and 1 or max_value
	self.node_list.pro_reward.slider.value = slider_value

	local data_len = #data_list + 1 --key从0开始，len需要n+1.

	local need_create = false
	if self.item_list == nil then
		self.item_list = {}
		need_create = true
	else
		if #self.item_list < data_len then
			need_create = true
		end
	end

	if need_create then
		local res_async_loader = AllocResAsyncLoader(self, "task_cell")
		res_async_loader:Load("uis/view/longyun_zhanling_ui_prefab", "render_task_reward_cell", nil, function(obj)
			local item_index = #self.item_list + 1
			for i = 1, data_len - #self.item_list do
				local root = ResMgr:Instantiate(obj)
				self.item_list[item_index] = LongYunZhanLingTaskFameRender.New(root)
				self.item_list[item_index]:SetIndex(item_index)
				self.item_list[item_index]:SetInstanceParent(self.node_list.root_pos.transform)
				item_index = item_index + 1
			end

			local index = 1
			for i = 1, data_len do
				self.item_list[i]:SetActive(true)
				self.item_list[i]:SetData(data_list[i - 1])
				index = index + 1
			end

			if index < #self.item_list then
				for i = index, #self.item_list do
					self.item_list[i]:SetActive(false)
				end
			end
		end)
	else
		local index = 1
		for i = 1, data_len do
			self.item_list[i]:SetActive(true)
			self.item_list[i]:SetData(data_list[i - 1])
			index = index + 1
		end

		if index < #self.item_list then
			for i = index, data_len do
				self.item_list[i]:SetActive(false)
			end
		end
	end
end

-------------------------------------------------------------------------------------------------------
LongYunZhanLingTaskRender = LongYunZhanLingTaskRender or BaseClass(BaseRender)
function LongYunZhanLingTaskRender:__init()
	self.reward_item_root = AsyncListView.New(ItemCell, self.node_list.reward_item_root)
end

function LongYunZhanLingTaskRender:__delete()
	if self.reward_item_root then
		self.reward_item_root:DeleteMe()
		self.reward_item_root = nil
	end
end

function LongYunZhanLingTaskRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_go, BindTool.Bind(self.OnClickGo, self))
	XUI.AddClickEventListener(self.node_list.btn_get_reward, BindTool.Bind(self.OnClickGetEveryTaskReward, self))
end

function LongYunZhanLingTaskRender:OnClickGo()
	if self.data == nil or next(self.data) == nil then
		return
	end

	if not self.data.is_get then
		local is_finsh = self.data.value >= self.data.cfg.task_value
		if is_finsh then
			LongYunZhanLingWGCtrl.Instance:SendCSCountryOperate(
				COUNTRY_OPERATE_TYPE.COUNTRY_OPERATE_TYPE_FETCH_TASK_REWARD, self.data.task_index - 1)
		else
			if self.data.cfg.open_panel ~= nil and self.data.cfg.open_panel ~= "" then
				local t = Split(self.data.cfg.open_panel, "#")
				if #t > 1 and t[1] == GuideModuleName.LongYunZhanLingTaskView then
					ViewManager.Instance:Open(GuideModuleName.LongYunZhanLingTaskView, 0, "jump_type",
						{ task_type = tonumber(t[2]) })
				else
					if t[1] ~= nil and t[1] == GuideModuleName.Shop then
						ViewManager.Instance:Open(t[1], t[2])
					elseif t[1] ~= nil and t[1] == GuideModuleName.YunbiaoView then
						if NewTeamWGData.Instance:GetIsMatching() then
							SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.MatchingCanNotDo)
							return
						end

						if YunbiaoWGData.Instance:GetIsHuShong() then
							SysMsgWGCtrl.Instance:ErrorRemind(Language.YunBiao.IsHuSong)
							return
						end

						ViewManager.Instance:Close(GuideModuleName.LongYunZhanLingTaskView)
						ViewManager.Instance:Close(GuideModuleName.CountryMapMapView)
						TaskGuide.Instance:CanAutoAllTask(false)
						ActIvityHallWGCtrl.Instance:DoHuSong()
					else
						FunOpen.Instance:OpenViewNameByCfg(self.data.cfg.open_panel)
					end
				end
			end
		end
	end
end

function LongYunZhanLingTaskRender:OnClickGetEveryTaskReward()
	if self.data == nil or next(self.data) == nil then
		return
	end

	if not self.data.is_get then
		local is_finsh = self.data.value >= self.data.cfg.task_value
		if is_finsh then
			LongYunZhanLingWGCtrl.Instance:SendCSCountryOperate(
				COUNTRY_OPERATE_TYPE.COUNTRY_OPERATE_TYPE_FETCH_TASK_REWARD, self.data.task_index - 1)
		end
	end
end

function LongYunZhanLingTaskRender:OnFlush()
	if not self.data then return end

	local str = string.format(Language.LongYunZhanLing.ValueStr, self.data.value, self.data.cfg.task_value)
	local color = self.data.value >= self.data.cfg.task_value and COLOR3B.C2 or COLOR3B.C3
	self.node_list.text_context.text.text = self.data.cfg.task_des .. ToColorStr(str, color)
	self.node_list.btn_go:SetActive(not self.data.is_get)
	self.node_list.img_get:SetActive(self.data.is_get)
	if not self.data.is_get then
		local is_finsh = self.data.value >= self.data.cfg.task_value
		self.node_list.btn_go:SetActive(not is_finsh)
		self.node_list.btn_get_reward:SetActive(is_finsh)
	end

	self.node_list.img_red:SetActive(not self.data.is_get and self.data.value >= self.data.cfg.task_value)
	self.node_list.task_type_img.image:LoadSprite(ResPath.GetLongYunZhanLingImg("a3_bhxt_bq_" .. self.data.cfg.quality))

	self:FlushItem()
end

function LongYunZhanLingTaskRender:FlushItem()
	if self.data == nil or self.data.cfg == nil then
		return
	end

	local data_list = self.data.cfg.reward_item

	local reward_list = {}
	for k, v in pairs(data_list) do
		local temp_data = { item_id = v.item_id, is_bind = v.is_bind, num = v.num }
		table.insert(reward_list, temp_data)
	end

	if self.data.cfg.add_fame > 0 then
		local temp_data = { item_id = 40032, is_bind = 1, num = self.data.cfg.add_fame }
		table.insert(reward_list, temp_data)
	end

	if self.data.cfg.add_devote > 0 then
		local temp_data = { item_id = 40033, is_bind = 1, num = self.data.cfg.add_devote }
		table.insert(reward_list, temp_data)
	end

	self.reward_item_root:SetDataList(reward_list)
end

---------------------------------------------------任务----------------------------------------------------
LongYunZhanLingTaskFameRender = LongYunZhanLingTaskFameRender or BaseClass(BaseRender)

function LongYunZhanLingTaskFameRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_get, BindTool.Bind(self.OnClickGet, self))
	XUI.AddClickEventListener(self.node_list.root_item, BindTool.Bind(self.ShowItemTips, self))

	if not self.item_cell then
		self.item_cell = DiamondItemCell.New(self.node_list.item_cell)
	end

	self.is_first = true
end

function LongYunZhanLingTaskFameRender:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function LongYunZhanLingTaskFameRender:OnFlush()
	if self.is_first then
		self.is_first = false
		local reward_cfg = LongYunZhanLingWGData.Instance:GetDailyFameRewardCfg()
		local reward_len = #reward_cfg + 1 --key从0开始，len需要n+1.
		local scale = self.index == reward_len and 1.4 or 1
		Transform.SetLocalScaleXYZ(self.node_list.icon_group.transform, scale, scale, scale)
	end

	if self.data == nil or next(self.data) == nil then
		return
	end

	local reward_list = self.data.reward_item
	if reward_list and reward_list[0] then
		-- local item_cfg = ItemWGData.Instance:GetItemConfig(reward_list[0].item_id)
		-- if item_cfg then
		-- 	local icon_id = item_cfg.icon_id
		-- 	local bundle, asset = ResPath.GetItem(icon_id)
		-- 	self.node_list.root_item.image:LoadSprite(bundle, asset, function()
		-- 		self.node_list.root_item.image:SetNativeSize()
		-- 	end)
		-- end
		self.item_cell:SetData(reward_list[0])
		self.item_cell:SetRightBottomTextVisible(false)
		self.item_cell:SetBindIconVisible(false)
		self.node_list.num.text.text = reward_list[0].num
	end

	self.node_list.text_prestige.text.text = self.data.need_fame
	local is_can = LongYunZhanLingWGData.Instance:CheckDailyFameRewardCanGet(self.data.reward_index)
	self.node_list.mask:SetActive(not is_can)
	local role_fame = LongYunZhanLingWGData.Instance:GetLongYunZhanLingFame()
	self.node_list.btn_get:SetActive(role_fame >= self.data.need_fame and is_can)
	self.node_list.img_can_get:SetActive(role_fame >= self.data.need_fame and is_can)
	self.node_list.img_red:SetActive(role_fame >= self.data.need_fame and is_can)
end

function LongYunZhanLingTaskFameRender:OnClickGet()
	if self.data == nil or next(self.data) == nil then
		return
	end

	local is_can = LongYunZhanLingWGData.Instance:CheckDailyFameRewardCanGet(self.data.reward_index)
	local role_fame = LongYunZhanLingWGData.Instance:GetLongYunZhanLingFame()
	if role_fame >= self.data.need_fame and is_can then
		-- 领取所有可领的
		local data_list, max_value = LongYunZhanLingWGData.Instance:GetTerritoryFameList()
		for k, v in pairs(data_list) do
			local is_can = LongYunZhanLingWGData.Instance:CheckDailyFameRewardCanGet(v.reward_index)
			if role_fame >= v.need_fame and is_can then
				LongYunZhanLingWGCtrl.Instance:SendCSCountryOperate(
					COUNTRY_OPERATE_TYPE.COUNTRY_OPERATE_TYPE_FETCH_FAME_REWARD, v.reward_index)
			end

		end

		-- 领取当前的
		-- LongYunZhanLingWGCtrl.Instance:SendCSCountryOperate(
		-- 	COUNTRY_OPERATE_TYPE.COUNTRY_OPERATE_TYPE_FETCH_FAME_REWARD, self.data.reward_index)
	end
end

function LongYunZhanLingTaskFameRender:ShowItemTips()
	local reward_list = self.data.reward_item
	if reward_list and reward_list[0] then
		TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = reward_list[0].item_id })
	end
end

--------------------------------------------------------------------
LongYunZhanLingTaskTypeRender = LongYunZhanLingTaskTypeRender or BaseClass(BaseRender)
function LongYunZhanLingTaskTypeRender:__init()
end

function LongYunZhanLingTaskTypeRender:__delete()

end

function LongYunZhanLingTaskTypeRender:LoadCallBack()
end

function LongYunZhanLingTaskTypeRender:OnSelectChange(is_on)
	if nil ~= self.node_list.img_select then
		self.node_list.img_select:SetActive(is_on)
	end
	if nil ~= self.node_list.Image_nor then
		self.node_list.Image_nor:SetActive(not is_on)
	end

	if nil ~= self.node_list.title_hl then
		self.node_list.title_hl:SetActive(is_on)
	end
	if nil ~= self.node_list.title_nor then
		self.node_list.title_nor:SetActive(not is_on)
	end
end

function LongYunZhanLingTaskTypeRender:OnFlush()
	if not self.data then return end
	self.node_list.title_hl.text.text = self.data.name
	self.node_list.title_nor.text.text = self.data.name

	self.node_list.img_red:SetActive(LongYunZhanLingWGData.Instance:GetTaskRemindByQuality(self.data.quality))
end
