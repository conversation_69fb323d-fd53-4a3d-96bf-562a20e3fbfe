require("game/treasure_palace/treasure_palace_data")
require("game/treasure_palace/treasure_palace_view")
require("game/treasure_palace/treasure_palace_everyday_recharge")
require("game/treasure_palace/treasure_palace_lifetime_recharge")
require("game/treasure_palace/treasure_palace_enter_reward_view")
require("game/treasure_palace/treasure_palace_gift_view")
require("game/treasure_palace/treasure_palace_rebate_view")

TreasurePalaceCtrl = TreasurePalaceCtrl or BaseClass(BaseWGCtrl)

function TreasurePalaceCtrl:__init()
	if TreasurePalaceCtrl.Instance then
		error("[TreasurePalaceCtrl]:Attempt to create singleton twice!")
	end

    TreasurePalaceCtrl.Instance = self

    self.data = TreasurePalaceData.New()
    self.view = TreasurePalaceView.New(GuideModuleName.TreasurePalaceView)

    self:RegisterAllProtocols()
	self.open_fun_change = GlobalEventSystem:Bind(OpenFunEventType.OPEN_TRIGGER, BindTool.Bind(self.OpenFunEventChange, self))
end

function TreasurePalaceCtrl:__delete()
    if self.open_fun_change then
		GlobalEventSystem:UnBind(self.open_fun_change)
		self.open_fun_change = nil
	end

    self.data:DeleteMe()
	self.data = nil

    self.view:DeleteMe()
	self.view = nil

    TreasurePalaceCtrl.Instance = nil
end

function TreasurePalaceCtrl:RegisterAllProtocols()
    self:RegisterProtocol(CSZhenBaoDianClientReq)
    self:RegisterProtocol(SCZhenBaoDianAllInfo, "OnSCZhenBaoDianAllInfo")
    self:RegisterProtocol(SCZhenBaoDianRmbNumInfo, "OnSCZhenBaoDianRmbNumInfo")
    self:RegisterProtocol(SCZhenBaoDianRewardInfo, "OnSCZhenBaoDianRewardInfo")
    self:RegisterProtocol(SCZhenBaoDianEnterGameRewardInfo, "OnSCZhenBaoDianEnterGameRewardInfo")
    self:RegisterProtocol(SCZhenBaoDianVipGiftInfo, "OnSCZhenBaoDianVipGiftInfo")
end

function TreasurePalaceCtrl:OpenFunEventChange(check_all, fun_name, is_open)
    if check_all or fun_name == FunName.TreasurePalace then
		local fun_open = FunOpen.Instance:GetFunIsOpened(FunName.TreasurePalace)
        local state = fun_open and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE
        ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.TREASUREPALACE, state)
    end
end

function TreasurePalaceCtrl:SendZhenBaoDianClientReq(operate_type, param1, param2, param3)
    -- print_error("请求",operate_type, param1, param2, param3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSZhenBaoDianClientReq)
	protocol.operate_type = operate_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function TreasurePalaceCtrl:OnSCZhenBaoDianAllInfo(protocol)
    -- print_error("--------------OnSCZhenBaoDianAllInfo-------------", protocol)
    self.data:SetTreasurePalaceAllInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.TreasurePalaceView)
    RemindManager.Instance:Fire(RemindName.Treasurepalace_Lifetime_Recharge)
    RemindManager.Instance:Fire(RemindName.Treasurepalace_Everyday_Recharge)
    RemindManager.Instance:Fire(RemindName.Treasurepalace_Enter_Reward)
    RemindManager.Instance:Fire(RemindName.Treasurepalace_Gift)
end

function TreasurePalaceCtrl:OnSCZhenBaoDianRmbNumInfo(protocol)
    self.data:SetTreasurePalaceRecharge(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.TreasurePalaceView, nil, "recharge_info")
    RemindManager.Instance:Fire(RemindName.Treasurepalace_Lifetime_Recharge)
    RemindManager.Instance:Fire(RemindName.Treasurepalace_Everyday_Recharge)
end

function TreasurePalaceCtrl:OnSCZhenBaoDianRewardInfo(protocol)
    self.data:SetTreasurePalaceRewardInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.TreasurePalaceView, nil, "reward_info")
    RemindManager.Instance:Fire(RemindName.Treasurepalace_Lifetime_Recharge)
    RemindManager.Instance:Fire(RemindName.Treasurepalace_Everyday_Recharge)
end

function TreasurePalaceCtrl:OnSCZhenBaoDianEnterGameRewardInfo(protocol)
    -- print_error("--------------OnSCZhenBaoDianEnterGameRewardInfo-------------", protocol)
    self.data:UpdateZhenBaoDianEnterGameRewardInfo(protocol)
    RemindManager.Instance:Fire(RemindName.Treasurepalace_Enter_Reward)
    RemindManager.Instance:Fire(RemindName.Treasurepalace_Gift)
    ViewManager.Instance:FlushView(GuideModuleName.TreasurePalaceView)
end

function TreasurePalaceCtrl:OnSCZhenBaoDianVipGiftInfo(protocol)
    -- print_error("--------------OnSCZhenBaoDianVipGiftInfo-------------", protocol)
    self.data:UpdateZhenBaoDianVipGiftInfo(protocol)
    RemindManager.Instance:Fire(RemindName.Treasurepalace_Gift)
    ViewManager.Instance:FlushView(GuideModuleName.TreasurePalaceView)
end