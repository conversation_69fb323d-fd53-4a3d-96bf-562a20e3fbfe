function RechargeView:InitXFRechargeView()
    if not self.xiaofei_item_list then
		self.xiaofei_item_list = AsyncListView.New(RechargeXiaoFeiCell,self.node_list["xiaofei_item_list"])
	end
end

function RechargeView:ReleseXFRechargeView()
	if self.xiaofei_item_list then
		self.xiaofei_item_list:DeleteMe()
		self.xiaofei_item_list = nil
	end
end

function RechargeView:ExpenseDoAnimation()
    local tween_info = UITween_CONSTS.EveryDayRechargeSys
    UITween.FakeHideShow(self.node_list["xiaofei_root"])
    local tween = UITween.AlphaShow(GuideModuleName.Recharge, self.node_list["xiaofei_root"], 0, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
end

function RechargeView:FlushXFRechargeView()
    if not self.xiaofei_item_list then
		return
	end

    local list_data = ServerActivityWGData.Instance:GetXiaoFeiListdata()
    self.xiaofei_item_list:SetDataList(list_data)
end




RechargeXiaoFeiCell = RechargeXiaoFeiCell or BaseClass(BaseRender)
function RechargeXiaoFeiCell:LoadCallBack()
	self.item_cell_list = {}
	self.node_list.get_reward_btn.button:AddClickListener(BindTool.Bind(self.OnClickGetRewardBtn, self))
	self.node_list.go_btn.button:AddClickListener(BindTool.Bind(self.OnClickGoBtn, self))
end

function RechargeXiaoFeiCell:__delete()
    for _,v in pairs(self.item_cell_list) do
		v:DeleteMe()
	end
	self.item_cell_list = nil
end

function RechargeXiaoFeiCell:OnFlush()
	local data = self:GetData()
	local total_gold = ServerActivityWGData.Instance:GetEveryDayExpenseNum() --测试服务器数据
	local get_state = ServerActivityWGData.Instance:GetEveryDayRewardState(data.id) --是否已领取
	local is_get = total_gold >= data.consumer_num --是否小于消费总数
	local is_get_btn = total_gold >= data.consumer_num and not get_state--大于消费数量 且 该档位没有领取过 就可领
	--XUI.SetButtonEnabled(self.node_list.get_reward_btn, is_get)  --直接置灰
	self.node_list.red_point:SetActive(is_get_btn) --红点
	self.node_list.get_reward_img:SetActive(get_state)
	self.node_list.go_btn:SetActive(not is_get and not get_state)
	self.node_list.get_reward_btn:SetActive(not get_state and is_get) --领取 按钮隐藏
	local color = total_gold >= data.consumer_num and COLOR3B.L_GREEN or COLOR3B.L_RED
	local str_consumer = ToColorStr(total_gold, color)
	self.node_list.xiaofei_reward_desc.text.text = string.format(Language.EverydayRecharge.EveryDayExpenseDesc, str_consumer, data.consumer_num)
	
	local reward_list = OperationActivityWGData.Instance:SortDataByItemColor(data.reward_item)
	local item_list = self.item_cell_list
	if #reward_list > #item_list then
		local cell_parent = self.node_list.cell_root
		for i=1,#reward_list do
			item_list[i] = item_list[i] or ItemCell.New(cell_parent)
		end
		self.item_list = item_list
	end

	for i = 1,#item_list do
		if reward_list[i] then
			item_list[i]:SetData(reward_list[i])
			item_list[i]:SetLingQuVisible(get_state)
			item_list[i]:SetActive(true)
		else
			item_list[i]:SetActive(false)
		end
	end
end

function RechargeXiaoFeiCell:OnClickGetRewardBtn()
	local data = self:GetData()
	if data then
		ServerActivityWGCtrl.Instance:SendEveryDayExpense(tonumber(data.id))
	end
end

function RechargeXiaoFeiCell:OnClickGoBtn()
	ViewManager.Instance:Open(GuideModuleName.Market, TabIndex.shop_limit)
end
