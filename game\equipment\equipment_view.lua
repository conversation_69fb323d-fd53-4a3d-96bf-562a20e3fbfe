EquipmentView = EquipmentView or BaseClass(SafeBaseView)
function EquipmentView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true

	self:SetMaskBg(false)
	self.default_index = TabIndex.equipment_strength

	local common_path = "uis/view/common_panel_prefab"
	local equipemnt_path = "uis/view/equipment_ui_prefab"
	local equipemnt_suit_path = "uis/view/equipment_suit_ui_prefab"
	self:AddViewResource(0, common_path, "layout_a3_common_panel")
	self:AddViewResource(TabIndex.equipment_strength, equipemnt_path, "layout_strength")
	self:AddViewResource(TabIndex.equipment_baoshi, equipemnt_path, "layout_baoshi")
	self:AddViewResource(TabIndex.equipment_baoshi_jl, equipemnt_path, "layout_baoshi_jl")
	self:AddViewResource(TabIndex.equipment_lingyu, equipemnt_path, "layout_lingyu")
	self:AddViewResource(TabIndex.equipment_shengpin, "uis/view/equipfumo_ui_prefab", "layout_shengpin_view")
	self:AddViewResource(TabIndex.equipment_xilian, equipemnt_path, "layout_equip_baptize")
	-- self:AddViewResource({ TabIndex.equipment_suit, TabIndex.equipment_suit_zhumo,
	-- 	TabIndex.equipment_suit_one, TabIndex.equipment_suit_two }, equipemnt_suit_path, "layout_taozhuang")
	self:AddViewResource(TabIndex.equipment_suit, equipemnt_suit_path, "layout_taozhuang")
	self:AddViewResource(TabIndex.equipment_zhuanhua, equipemnt_path, "layout_equip_transsex") -- 背包转化移入
	self:AddViewResource(TabIndex.equipment_imperial_spirit_set, equipemnt_path, "layout_imperial_spirit_set")
	self:AddViewResource(TabIndex.equipment_imperial_spirit_strengtn, equipemnt_path, "layout_imperial_spirit_strength")
	self:AddViewResource(TabIndex.equipment_zhushen, equipemnt_path, "layout_equipment_zhushen")    --铸神
	self:AddViewResource(TabIndex.equipment_zhushentai, equipemnt_path, "layout_equipment_zhushentai") --铸神台

	self:AddViewResource(0, common_path, "VerticalTabbar")
	self:AddViewResource(0, equipemnt_path, "HorizontalTabbar")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
	self.tab_sub = { Language.Equip.TabSub1, Language.Equip.TabSub2, nil, nil, Language.Equip.TabSub7,
		Language.Equip.TabSub8 }
	self.remind_tab = {
		{ RemindName.Equipment_Strength,            RemindName.Equipment_Baptize,                 RemindName.Equipment_ShengPin },
		{ RemindName.Equipment_Stone_Inlay,         RemindName.Equipment_Stone_Refine,            RemindName.Equipment_LingYu_Inlay },
		-- { RemindName.Equipment_Suit_ZhuMo,          RemindName.Equipment_Suit_ZhuXian,            RemindName.Equipment_Suit_ZhuShen },
		{ RemindName.Equipment_Suit},
		{},
		{ RemindName.Equipment_Imperial_Spirit_Set, RemindName.Equipment_Imperial_Spirit_Strength },
		{ RemindName.Equipment_ZhuShen,             RemindName.Equipment_ZhuShenTai },
	}
	self.item_data_change_callback = BindTool.Bind(self.OnItemDataChange, self)
end

function EquipmentView:__delete()

end

function EquipmentView:HandleEffectRootHide()
	if self.node_list["strength_effect_root1"] then
		self.node_list["strength_effect_root1"]:SetActive(false)
	end

	if self.node_list["baptize_effect_root"] then
		self.node_list["baptize_effect_root"]:SetActive(false)
	end

	if self.node_list["shenpin_effect_root"] then
		self.node_list["shenpin_effect_root"]:SetActive(false)
	end

	if self.node_list["effect_root_baoshi"] then
		self.node_list["effect_root_baoshi"]:SetActive(false)
	end

	if self.node_list["bsjl_result_effect"] then
		self.node_list["bsjl_result_effect"]:SetActive(false)
	end

	if self.node_list["effect_root_suit"] then
		self.node_list["effect_root_suit"]:SetActive(false)
	end
end

function EquipmentView:LoadCallBack()
	self:InitTabbar()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.Equipment, self.get_guide_ui_event)
end

function EquipmentView:LoadIndexCallBack(index, loaded_times)
	if index == TabIndex.equipment_strength then -- 强化
		self:InitStrengthView()
	elseif index == TabIndex.equipment_xilian then
		self:InitBaptizeView()
	elseif index == TabIndex.equipment_shengpin then
		self:InitShengPinView()
	elseif index == TabIndex.equipment_baoshi then
		self:InitBaoShiView()
	elseif index == TabIndex.equipment_baoshi_jl then
		self:InitBaoShiJingLianView()
	elseif index == TabIndex.equipment_suit then
		-- or index == TabIndex.equipment_suit_zhumo
		-- or index == TabIndex.equipment_suit_one
		-- or index == TabIndex.equipment_suit_two then
		self:InitSutiView()
	elseif index == TabIndex.equipment_lingyu then
		self:InitLingYuView()
	elseif index == TabIndex.equipment_zhuanhua then
		self:InitEquipTranssexView()
	elseif index == TabIndex.equipment_imperial_spirit_set then
		self:InitImperialSpiritSetView()
	elseif index == TabIndex.equipment_imperial_spirit_strengtn then
		self:InitImperialSpiritStrengthView()
	elseif index == TabIndex.equipment_zhushen then
		self:InitZhuShenView()
	elseif index == TabIndex.equipment_zhushentai then
		self:InitZhuShenTaiView()
	end
end

function EquipmentView:OpenCallBack()
	TaskGuide.Instance:SideTOStopTask(true)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
end

function EquipmentView:InitTabbar()
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
		self.tabbar:SetCreateHorCallBack(BindTool.Bind(self.UpdateEquipmentTabbarLimit, self))
		self.tabbar:Init(Language.Equip.TabGrop, self.tab_sub, nil, nil, self.remind_tab)
		FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.Equipment, self.tabbar)
	end
end

function EquipmentView:ReleaseCallBack()
	self:StrengthDeleteMe()
	self:BaoShiDeleteMe()
	self:SutiViewDelete()
	self:BaptizeViewDelete()
	self:ShengPinViewDeleteMe()
	self:BaoShiJingLianDeleteMe()
	self:LingYuDeleteMe()

	self:DeleteEquipTranssexView()
	self:DeleteImperialSpiritStrengthView()
	self:DeleteImperialSpiritSetView()
	self:DeleteZhuShenView()
	self:DeleteZhuShenTaiView()

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	self.play_effect = nil

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end
end

function EquipmentView:ShowIndexCallBack(index)
	local bundle, asset = ResPath.GetRawImagesPNG("a3_ty_bg4")
	EquipmentWGData.Instance:SetFirstCostRecord(nil, nil)
	TipWGCtrl.Instance:ForceHideEffect()
	self:HandleEffectRootHide()
	self:StopAutoEquipStrength()

	if index == TabIndex.equipment_strength then
		self:ShowFlushEquipStrengthIndexCallBack()
	elseif index == TabIndex.equipment_xilian then
		self:BaptizeShowIndexCallBack()
		self:ClearBTSliderTween()
	elseif index == TabIndex.equipment_baoshi then
		self:BaoShiViewShowIndexCallBack()
	elseif index == TabIndex.equipment_baoshi_jl then
		self:ShowBaoShiJingLianView()
	elseif index == TabIndex.equipment_lingyu then
		self:ShowEquipLingYuView()
	elseif index == TabIndex.equipment_suit then
		-- or index == TabIndex.equipment_suit_zhumo
		-- or index == TabIndex.equipment_suit_one
		-- or index == TabIndex.equipment_suit_two then
		self:ShowFlushEquipSuit()
	-- elseif index == TabIndex.equipment_xilian then
	-- 	self:ClearBTSliderTween()
	elseif index == TabIndex.equipment_shengpin then
		self:ShengpinShowIndexCallBack()
	elseif index == TabIndex.equipment_zhuanhua then
		self:ShowIndexTranssexView()
	elseif index == TabIndex.equipment_imperial_spirit_set then
		self:ShowIndexImperialSpiritSetView()
	elseif index == TabIndex.equipment_imperial_spirit_strengtn then
		self:ShowIndexImperialSpiritStrengthView()
	elseif index == TabIndex.equipment_zhushen then
		
	elseif index == TabIndex.equipment_zhushentai then

	end

	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)
end

function EquipmentView:GetEquipmentShowIndex()
	return self.show_index
end

function EquipmentView:CloseCallBack(is_all)
	EquipmentWGData.Instance:SetFirstCostRecord(nil, nil)

	self:CancelAutoBaptizeEquip()
	self:BaptizeViewClose()

	MainuiWGCtrl.Instance:DelayShowCachePower(0)
	self.is_auto_euqip_qh = false
	self.is_auto_euqip_sz = false
	self.is_auto_euqip_cs = false
	self.is_auto_euqip_jl = false
	self.equip_qh_list_flag = nil
	if FunctionGuide.Instance:GetCurrentGuideName() == "equipstrength" then
		FunctionGuide.Instance:EndGuide()
	end
	TaskGuide.Instance:SideTOStopTask(false)

	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.Equipment, self.get_guide_ui_event)
	self.get_guide_ui_event = nil
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
end

function EquipmentView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			if index == TabIndex.equipment_strength then
				self:FlushEquipStrength(v)
			elseif index == TabIndex.equipment_baoshi then
				self:FlushEquipBaoShiView(v)
			elseif index == TabIndex.equipment_suit then
				-- or index == TabIndex.equipment_suit_zhumo
				-- or index == TabIndex.equipment_suit_one
				-- or index == TabIndex.equipment_suit_two then
				-- if v.jump_index and v.jump_index > 0 then
				-- 	self.suit_index = v.jump_index
				-- end
				self:FlushEquipSuitListInfo(v)
			elseif index == TabIndex.equipment_shengpin then --升品
				self:FlushShengPin()
			elseif index == TabIndex.equipment_xilian then
				self:FlushEquipBaptizeListDataSource(true)
			elseif index == TabIndex.equipment_baoshi_jl then
				self:FlushBaoShiJingLianView()
			elseif index == TabIndex.equipment_lingyu then
				self:FlushEquipLingYuView()
			elseif index == TabIndex.equipment_zhuanhua then
				self:FlushEquipTranssexView(param_t)
			elseif index == TabIndex.equipment_imperial_spirit_set then
				self:FlushEquipImperialSpiritSetView()
			elseif index == TabIndex.equipment_imperial_spirit_strengtn then
				self:FlushEquipImperialSpiritStrengthView()
			elseif index == TabIndex.equipment_zhushen then
				self:FlushEquipZhuShenView()
			elseif index == TabIndex.equipment_zhushentai then
				self:FlushEquipZhuShenTaiView(v.to_ui_param)
			end
		elseif k == "sp_do_effect" then
			self:FlushSpViewAndShowEffect(v.result_list)
		elseif k == "baptize_succe" then
			self:ShowBaptizeSuceeEffect()
		elseif k == "baptize_upgrade_succe" then
			-- self:ShowBaptizeSuceeEffect(true)
		elseif k == "strength_change_to_equip_body" then
			self:StrengthChangeToTargetEquipBody(v)
		elseif k == "baptize_change_to_equip_body" then
			self:BaptizeChangeToTargetEquipBody(v)
		elseif k == "equip_suit_change_to_equip_body" then
			self:EquipSuitChangeToTargetEquipBody(v)
		elseif k == "equip_baoshi_change_to_equip_body" then
			self:EquipBaoShiChangeToTargetEquipBody(v)
		elseif k == "equip_lingyu_change_to_equip_body" then
			self:LingYuChangeToTargetEquipBody(v)
		elseif k == "equip_bsjl_change_to_equip_body" then
			self:BSJLChangeToTargetEquipBody(v)
		elseif k == "OnEquipStrengthResult" then
			self:FlushEquipStrength()
		end

		self:UpdateEquipmentTabbarLimit()
	end
end

function EquipmentView:UpdateEquipmentTabbarLimit()
	local ver = math.modf(self.show_index / 10)

	if ver == 5 then
		local limit_click = IsEmptyTable(EquipmentWGData.Instance:GetQingHuaImperialSpiritList())
		self.tabbar:SetHorOtherBtn(TabIndex.equipment_imperial_spirit_strengtn, limit_click, BindTool.Bind(function ()
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Equipment.ClickStrengthen)
		end, self))
	else
		self.tabbar:SetHorOtherBtn(TabIndex.equipment_imperial_spirit_strengtn, false)
	end

	if ver == 2 then
		local has_ypxq_equip_body = EquipBodyWGData.Instance:IsHasCanYPJLEquipBody()
		self.tabbar:SetHorOtherBtn(TabIndex.equipment_baoshi_jl, not has_ypxq_equip_body, BindTool.Bind(function ()
			SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleEquipBody.NoEquipBodyCanYPJL)
		end, self))

		local has_xjxq_equip_body = EquipBodyWGData.Instance:IsHasCanXJXQEquipBody()
		self.tabbar:SetHorOtherBtn(TabIndex.equipment_lingyu, not has_xjxq_equip_body, BindTool.Bind(function ()
			SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleEquipBody.NoEquipBodyCanXJXQ)
		end, self))	
	else
		self.tabbar:SetHorOtherBtn(TabIndex.equipment_lingyu, false)
		self.tabbar:SetHorOtherBtn(TabIndex.equipment_baoshi_jl, false)
	end
end

function EquipmentView:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	local index = self:GetShowIndex()
	if index == TabIndex.equipment_imperial_spirit_set or index == TabIndex.equipment_imperial_spirit_strengtn then
		return
	end

	if old_num and new_num and new_num > old_num then
		self:Flush(index)
		if index == TabIndex.equipment_strength then
			self:FlushEquipStrengthListDataSource()
		end
	end

	self:OnTranssexItemChange()
end

function EquipmentView:GetGuideUiCallBack(ui_name, ui_param)
	if not self:IsLoaded() then
		return
	end

	if ui_name == GuideUIName.Tab and self.tabbar then
		local tab_index = math.floor(TabIndex[ui_param])
		if tab_index == self:GetShowIndex() then
			return NextGuideStepFlag
		else
			self:ShowIndex(tab_index)
			return
		end
	elseif ui_name == GuideUIName.EquipmentIntensifyBtn then
		return self:GetBtnEquipQhNode()
	elseif ui_name == GuideUIName.AutoEquipmentIntensifyBtn then
		return self:GetBtnEquipAutoQhNode()
	elseif ui_name == GuideUIName.BtnEquipQhAuto then
		return self:GetBtnEquipAutoQhNode()
	elseif ui_name == GuideUIName.CloseBtn then
		if not self.is_auto_euqip_qh then
			return self.node_list.btn_close_window, BindTool.Bind(self.Close, self)
		else
			return nil, nil
		end
	end

	if type(ui_param) == "table" and ui_param[1] == GuideUIName.EquipmentIntensifyList then
		return self:GetEquipmentIntensifyListCallBack()
	end

	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end
