function FuBenPanelView:InitWuhunView()
	if not self.wuhun_reward_list then
		self.wuhun_reward_list = AsyncListView.New(FuBenWuhunRewardCell, self.node_list["wuhun_reward_list"])
		self.wuhun_reward_list:SetStartZeroIndex(true)
	end

	self.node_list["layout_wuhun_combine_mark"].button:AddClickListener(BindTool.Bind1(self.OnClickWuhunCombine, self))
	self.node_list["btn_wuhun_enter"].button:AddClickListener(BindTool.Bind1(self.OnClickWuhunBaoMingEnter,self))
end

function FuBenPanelView:DeleteWuhunView()
	if self.wuhun_reward_list then
		self.wuhun_reward_list:DeleteMe()
		self.wuhun_reward_list = nil
	end
end

function FuBenPanelView:FlushWuhunView()
    local cfg = FuBenTeamCommonBossWGData.Instance:GetFuBenTypeModelCfg(GoalTeamType.FbWuHunType, 0)
	if not cfg then
		return 
	end

    self.node_list.desc_wuhun_ads.text.text = cfg.title_desc
    self.node_list.wuhun_fb_desc.text.text = cfg.fb_desc

    for i = 1, 5 do
		if Language.FuBenPanel.WuhunFuBenShuoming[i] then
			self.node_list["wuhun_fb_info_sign"..i]:SetActive(true)
			self.node_list["wuhun_fb_info"..i].text.text = Language.FuBenPanel.WuhunFuBenShuoming[i]
		else
			self.node_list["wuhun_fb_info_sign"..i]:SetActive(false)
		end
	end

	self.wuhun_reward_list:SetDataList(cfg.show_reward_item)
	self:FlushWuhunEnterCount()
end

function FuBenPanelView:FlushWuhunEnterCount()
    if self.node_list["wuhun_enter_count"] then
		local team_type_cfg =  FuBenTeamCommonBossWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbWuHunType)
		local enter_times = FuBenTeamCommonBossWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbWuHunType)
		local max_count = team_type_cfg.max_times or 0
		local remain_times = max_count - enter_times
		self.node_list["wuhun_enter_count"].text.color = Str2C3b(remain_times > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.L_RED)
        self.node_list["wuhun_enter_count"].text.text = string.format(Language.FuBenPanel.FuBenEnterTime, remain_times, max_count)
    end

    local hook_type = FuBenWGData.Instance:GetCombineStatus(FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB3)
    self.node_list.layout_wuhun_combine_hook:SetActive(hook_type == 1)
end

function FuBenPanelView:OnClickWuhunCombine()
    local combine_cfg = FuBenWGData.Instance:GetCombineCfg()
    local role_level = GameVoManager.Instance:GetMainRoleVo().level
    local need_level = combine_cfg[FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB3 + 1].level_limit

    if need_level >= role_level then
        local level_des = RoleWGData.GetLevelString(need_level)
        local str = string.format(Language.FuBenPanel.CombineLimitTips, level_des)
        SysMsgWGCtrl.Instance:ErrorRemind(str)
        return
    end

	local team_type_cfg =  FuBenTeamCommonBossWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbWuHunType)
	local enter_times = FuBenTeamCommonBossWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbWuHunType)
	local max_count = team_type_cfg.max_times or 0
	local remain_times = max_count - enter_times

    if remain_times < 2 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CountTooLess)
        return
    end
	
    local vas = self.node_list.layout_wuhun_combine_hook:GetActive()

    local is_combine = vas == true and 0 or 1
    if vas then
        FuBenWGCtrl.Instance:SendFBUseCombine(is_combine, FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB3)
    else
        local callback_func = function()
        end

        FuBenWGCtrl.Instance:ShowCombinePanel(FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB3, callback_func)
    end
end

function FuBenPanelView:OnClickWuhunBaoMingEnter()
	local team_type = GoalTeamType.FbWuHunType
	local fb_mode = 0

	local team_type_cfg =  FuBenTeamCommonBossWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbWuHunType)
	local enter_times = FuBenTeamCommonBossWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbWuHunType)
	local max_count = team_type_cfg.max_times or 0
	local remain_count = max_count - enter_times
    if remain_count <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.CanNoBuyTimes)
        return
    end

    NewTeamWGCtrl.Instance:F2SendTeamFuBenEnter(team_type, fb_mode, 5)
end
---------------------FuBenWuhunRewardCell--------------------------------
FuBenWuhunRewardCell = FuBenWuhunRewardCell or BaseClass(BaseRender)
function FuBenWuhunRewardCell:__init()
	if not self.base_cell then
		self.base_cell = ItemCell.New(self.node_list["pos"])
		self.base_cell:SetIsShowTips(true)
	end
end

function FuBenWuhunRewardCell:__delete()
	if self.base_cell then
		self.base_cell:DeleteMe()
		self.base_cell = nil
	end
end

function FuBenWuhunRewardCell:OnFlush()
	if not self.data then
		return
	end

	self.base_cell:SetData(self.data)
    self.node_list.three_flag:SetActive(false)
end