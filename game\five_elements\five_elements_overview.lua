function FiveElementsView:InitFiveElementOverview()
	XUI.AddClickEventListener(self.node_list.btn_juexing, BindTool.Bind(self.OnJueXingBtnClick ,self))
	XUI.AddClickEventListener(self.node_list.skill_item_icon, BindTool.Bind(self.OnClickSkillItem ,self))
	XUI.AddClickEventListener(self.node_list.skill_icon1, BindTool.Bind(self.OnClickSkillItem ,self))
	XUI.AddClickEventListener(self.node_list.skill_icon3, BindTool.Bind(self.OnClickNoDataSkillItem ,self))

	if not self.juexing_cost_item then
		self.juexing_cost_item = ItemCell.New(self.node_list.juexing_item_pos)
	end

	self.can_juexing_flag = false
	self.juexing_cost_item_id = 0

	if not self.property_item_list then
		self.property_item_list = {}

		for i = 0, 4 do
			local item_render = FiveElementsPartItemRenderer.New(self.node_list["property_item" .. i].gameObject)
			item_render.parent_view = self
			item_render:SetIndex(i)
			item_render:SetClickCallBack(BindTool.Bind(self.OnPartItemRendererClickCallBack,self))
			self.property_item_list[i] = item_render
		end
	end
end

function FiveElementsView:ShowFiveElementOverview()
	self.node_list["title_view_name"].text.text = Language.FiveElements.TabName[1]
	self:ChangeFiveElementsViewBg("a2_wx_bg3")
	self:DoFiveElementOverviewAnim()
end

function FiveElementsView:OverviewReleaseCallBack()
	if self.property_item_list then
		for k, v in pairs(self.property_item_list) do
			v:DeleteMe()
		end

		self.property_item_list =nil
	end

	if self.juexing_cost_item then
		self.juexing_cost_item:DeleteMe()
		self.juexing_cost_item = nil
	end

	self.juexing_cost_item = nil
	self.can_juexing_flag = nil
end

function FiveElementsView:OnFlushFiveElementOverview()
	local skilltipsindex = 1
	for i = 0, 4 do
		local data = FiveElementsWGData.Instance:GetPartItemData(i)
		if  data.color >= 5 and data.color <= 7 then
			skilltipsindex = skilltipsindex + 1
		end

		local skilltips = FiveElementsWGData.Instance:GetSlillTipsCfg(skilltipsindex)

		self.property_item_list[i]:SetData(data)
	    self.node_list.tips_text.text.text = skilltips
	end

	self.node_list.cap_value.text.text = FiveElementsWGData.Instance:GetFiveElementsSkillCapValue()

	self:FlushOverviewSkillItem()
	self:SetOverviewRemind()
	self:FlushOverviewJueXingPanel()
end

function FiveElementsView:OnPartItemRendererClickCallBack(item)
	if nil == item or IsEmptyTable(item.data) then
		return
	end

	local index = item:GetIndex()
	ViewManager.Instance:Open(GuideModuleName.FiveElementsView, TabIndex.five_elements_knapsack, "all", {jump_index = index})
end

function FiveElementsView:FlushOverviewJueXingPanel()
	self.can_juexing_flag = false
	local skill_cfg = FiveElementsWGData.Instance:GetOverViewActiveSkillInfo()
	local current_data, next_data = FiveElementsWGData.Instance:GetOverViewJueXingData()
	local has_skill_data = not IsEmptyTable(skill_cfg)
	local is_max_level = has_skill_data and IsEmptyTable(next_data)

	XUI.SetButtonEnabled(self.node_list.btn_juexing, has_skill_data and not is_max_level)
	self.node_list.juexing_nodata:SetActive(not has_skill_data)
	self.node_list.juexing_hasdata:SetActive(has_skill_data)
	self.node_list.btn_juexing:SetActive(not is_max_level)
	self.node_list.juexing_item_pos:SetActive(has_skill_data and not is_max_level)
	self.node_list.juexing_flag:SetActive(is_max_level)

	if has_skill_data then
		local icon_id = SkillWGData.Instance:GetSkillIconId(skill_cfg.skill_id)
		local bundle, name = ResPath.GetSkillIconById(icon_id)
		self.node_list.skill_icon1.image:LoadSprite(bundle, name, function()
			self.node_list.skill_icon1.image:SetNativeSize()
		end)

		local level = FiveElementsWGData.Instance:GetSkillAwakenLevel()
		local skill_info = SkillWGData.Instance:GetWuXingSkillById(skill_cfg.skill_id, level)
		local level = FiveElementsWGData.Instance:GetSkillAwakenLevel()
		self.node_list.attr_item_name3.text.text = skill_info.skill_name
		--self.node_list.juexing_title.text.text = string.format(Language.FiveElements.TitleAndLevel, skill_info.skill_name, level)

		for i = 1, 5 do
			local asset_name = (level > 5 and (i <= level % 5 or level % 5 == 0)) and "a2_ty_xx_fs" or "a2_ty_xx_d"
			self.node_list["attr_star_img1_" .. i]:SetActive(level > 5 or i <= level)
			self.node_list["attr_star_img1_" .. i].image:LoadSprite(ResPath.GetCommonImages(asset_name))
	   end

		if not is_max_level then
			local cost_item_id = current_data.cost_item_id
			self.juexing_cost_item_id = cost_item_id
			local item_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
			local cost_item_num = current_data.cost_item_num
			local enough = item_num >= cost_item_num
			self.juexing_cost_item:SetFlushCallBack(function ()
				
				local right_text = ToColorStr(item_num .. "/" .. cost_item_num, enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
				self.juexing_cost_item:SetRightBottomColorText(right_text)
				self.juexing_cost_item:SetRightBottomTextVisible(true)
			end)

			self.can_juexing_flag = enough
			self.node_list.juexing_btn_remind:SetActive(self.can_juexing_flag)
			self.juexing_cost_item:SetData({item_id = cost_item_id, num = cost_item_num, is_bind = 0})
		end
	end

	if not has_skill_data then
		local bundle, name = ResPath.GetSkillIconById(3085)
		self.node_list.skill_icon3.image:LoadSprite(bundle, name, function()
			self.node_list.skill_icon3.image:SetNativeSize()
		end)
		
		XUI.SetButtonEnabled(self.node_list["nodata_skill_bg"], has_skill_data)	
	end
end

function FiveElementsView:SetOverviewRemind()
	for i = 0, 4 do
		self.property_item_list[i]:FlushRemind()
	end
end

function FiveElementsView:OverviewItemChangeFlush()
	self:SetOverviewRemind()
	self:FlushOverviewJueXingPanel()
end

function FiveElementsView:UpdateSkill()
	self:FlushOverviewSkillItem()
	self:FlushOverviewJueXingPanel()
end

function FiveElementsView:FlushOverviewSkillItem()
	local skill_cfg = FiveElementsWGData.Instance:GetOverViewActiveSkillInfo()
	local bundle, name, icon_id

	if IsEmptyTable(skill_cfg) then
		--self.node_list.star_list:SetActive(false)
		local default_skill_info = FiveElementsWGData.Instance:GetDEfaultShowShill()
		-- 策划要求默认显示最低等级技能
		icon_id = SkillWGData.Instance:GetSkillIconId(default_skill_info.skill_id)
		bundle, name = ResPath.GetSkillIconById(icon_id)
		self.node_list.skill_name.text.text = Language.FiveElements.SkillActiveDesc
	else
		icon_id = SkillWGData.Instance:GetSkillIconId(skill_cfg.skill_id)
		bundle, name = ResPath.GetSkillIconById(icon_id)

		local level = FiveElementsWGData.Instance:GetSkillAwakenLevel()
		local skill_info = SkillWGData.Instance:GetWuXingSkillById(skill_cfg.skill_id, level)
		self.node_list.skill_name.text.text = skill_info.skill_name or ""
	end

	self.node_list.skill_item_icon.image:LoadSprite(bundle, name, function()
		self.node_list.skill_item_icon.image:SetNativeSize()
	end)
end

function FiveElementsView:OnJueXingBtnClick()
	if self.can_juexing_flag then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_juexing,
	    is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["overview_effect_root"]})
		FiveElementsWGCtrl.Instance:SendSFiveElementsRequest(FIVE_ELEMENTS_OPERATE_TYPE.FIVE_ELEMENTS_OPERATE_TYPE_SKILL_AWAKEN)
	else
		if self.juexing_cost_item_id > 0 then
			TipWGCtrl.Instance:OpenItem({item_id = self.juexing_cost_item_id})
		end
	end
end

function FiveElementsView:DoFiveElementOverviewAnim()
	local tween_info = UITween_CONSTS.FiveElements
	self.node_list.left_bg.canvas_group.alpha = 0
	RectTransform.SetAnchoredPositionXY(self.node_list.right_root.rect, 600, 0)
	RectTransform.SetAnchoredPositionXY(self.node_list.capability.rect, 0, -300)
	RectTransform.SetAnchoredPositionXY(self.node_list.tips_text.rect, 46, -300)
	self.node_list.left_bg.canvas_group:DoAlpha(0, 1, tween_info.canvas_group_show)
	self.node_list.right_root.rect:DOAnchorPos(Vector2(26, -16), tween_info.movetime)
	self.node_list.capability.rect:DOAnchorPos(Vector2(0, 0), tween_info.movetime)
	self.node_list.tips_text.rect:DOAnchorPos(Vector2(46, 54), tween_info.movetime)
end

function FiveElementsView:OnClickSkillItem()
	local show_data = FiveElementsWGData.Instance:GetJueXingSkillInfo(false)

	if IsEmptyTable(show_data) then
		local skill_info = FiveElementsWGData.Instance:GetDEfaultShowShill()
		show_data = FiveElementsWGData.Instance:GetJueXingSkillInfo(false, skill_info.skill_id, 1)
	end

	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

function FiveElementsView:OnClickNoDataSkillItem()
	local show_data = FiveElementsWGData.Instance:GetJueXingSkillInfo(false)

	if IsEmptyTable(show_data) then
		local skill_info = FiveElementsWGData.Instance:GetDEfaultShowShill()
		show_data = FiveElementsWGData.Instance:GetJueXingSkillInfo(false, skill_info.skill_id, 1)
	end

	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

------------------------------------------五行item start------------------------------------------
FiveElementsPartItemRenderer = FiveElementsPartItemRenderer or BaseClass(BaseRender)

function FiveElementsPartItemRenderer:__delete()
	if self.parent_view then
		self.parent_view = nil
	end
end

function FiveElementsPartItemRenderer:OnFlush()
	if self.data == nil then
		return
	end

	local asset_name = "a2_five_elements_" .. self.data.color
	local bundle, asset = ResPath.GetFiveElementsImg(asset_name)
	self.node_list.icon.image:LoadSprite(bundle, asset, function()
		self.node_list.icon.image:SetNativeSize()
	end)

--	self.node_list.level_bg:SetActive(data.color > GameEnum.ITEM_COLOR_WHITE)
--	self.node_list.level.text.text = string.format(Language.FiveElements.Level, data.level)
end

function FiveElementsPartItemRenderer:FlushRemind()
	if self.data == nil then
		return
	end

end
------------------------------------------五行item end------------------------------------------