BootyBayPreView = BootyBayPreView or BaseClass(SafeBaseView)

BootyBayPreView.Operate = {
    Left = 1,
    Right = 2
}

BootyBayPreView.MaxMember = 1
BootyBayPreView.NotFull = 2
BootyBayPreView.Single = 3

function BootyBayPreView:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(812, 574)})
    self:AddViewResource(0, "uis/view/bootybay_ui_prefab", "layout_bootybay_pre")
	self:SetMaskBg(true)
end

function BootyBayPreView:ReleaseCallBack()
    self.team_status = nil
    if self.cell_list then
        for i, v in pairs(self.cell_list) do
            v:DeleteMe()
        end
        self.cell_list = nil
    end
end

function BootyBayPreView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.BootyBay.PreTitle
    self.node_list["btn_left"].button:AddClickListener(BindTool.Bind(self.DoFuncStatus, self, BootyBayPreView.Operate.Left))
    self.node_list["btn_right"].button:AddClickListener(BindTool.Bind(self.DoFuncStatus, self, BootyBayPreView.Operate.Right))

    self.cell_list = {}
    for i = 1, 9 do
        self.cell_list[i] = ItemCell.New(self.node_list["cell" .. i])
    end

    self.team_status = BootyBayPreView.Single
end

function BootyBayPreView:ShowIndexCallBack()
    self:Flush()
end

function BootyBayPreView:OnFlush()
    self:FlushCell()
    self:FlushTimes()
    self:FlushButton()
end

function BootyBayPreView:FlushCell()
    local cfg = BootyBayWGData.Instance:GetOtherConfig()
    local data_list = cfg.zuduireward or {}
    for i = 1, 9 do
        if data_list[i - 1] then
            self.cell_list[i]:SetData(data_list[i - 1] or {})
            self.node_list["cell" .. i]:SetActive(true)
        else
            self.node_list["cell" .. i]:SetActive(false)
        end
    end
end

function BootyBayPreView:FlushTimes()
    local enter_teamfb_times = BootyBayWGData.Instance:GetEnterTeamFBTimes()
    local other_cfg = BootyBayWGData.Instance:GetOtherConfig()
    local times = other_cfg.team_fb_enter_times_limit - enter_teamfb_times
    local color = times >= 0 and COLOR3B.DEFAULT_NUM or COLOR3B.RED
    local str = ToColorStr(times .. "/" .. other_cfg.team_fb_enter_times_limit, color)
    self.node_list["enter_count"].text.text = string.format(Language.BootyBay.WeekTimes, str)
end

function BootyBayPreView:FlushButton()
    local is_in_team = SocietyWGData.Instance:GetIsInTeam() == 1
    local team_num = SocietyWGData.Instance:GetTeamMemberCount()
    if is_in_team and team_num == GameEnum.TEAM_MAX_COUNT then
        self.team_status = BootyBayPreView.MaxMember
        self.node_list["txt_btn_right"].text.text = Language.BootyBay.GoToWaBao--前往挖宝
    else
        if is_in_team then
            self.team_status = BootyBayPreView.NotFull
            self.node_list["txt_btn_left"].text.text = Language.BootyBay.StillGoTo--仍然前往
            self.node_list["txt_btn_right"].text.text = Language.BootyBay.GoToTeam--前往组队
        else
            self.team_status = BootyBayPreView.Single
            self.node_list["txt_btn_left"].text.text = Language.BootyBay.SingleGo--单独前往
            self.node_list["txt_btn_right"].text.text = Language.BootyBay.TeamGo--组队前往
        end
    end
end

function BootyBayPreView:DoFuncStatus(operate)
    if self.team_status == BootyBayPreView.MaxMember then
        if operate == BootyBayPreView.Operate.Left then
            self:OpenTeamPlate()
        else
            self:OnClickGoto()
        end
    else
        if operate == BootyBayPreView.Operate.Left then
            self:OnClickGoto()
        else
            self:OpenTeamPlate(self.team_status)
        end
    end
end

function BootyBayPreView:OpenTeamPlate(team_status)
    if team_status == BootyBayPreView.Single then
        local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
        NewTeamWGCtrl.Instance:SendCreateTeam(0, 1, min_level, max_level)--无目标
    end
    ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_my_team)
    NewTeamWGData.Instance:SetOpenTeamCallBack(function()
        --策划需求，改为打开组队面板后直接喊话到组队频道，无需打开喊话面板
        -- NewTeamWGCtrl.Instance:OpenTalkView(Language.BootyBay.TeamTalk)
        local function callback()
            NewTeamWGCtrl.Instance:SendTeamTargetShouting(0, 0, 0, 0, 1)
        end
        OperateFrequency.Operate(callback, "team_world_invite", 10)
    end)
    self:Close()
end

function BootyBayPreView:OnClickGoto()
    local bootybay_scene_id = BootyBayWGData.Instance:GetBootyBaySceneId()
    local scene_id = Scene.Instance:GetSceneId()
    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.BOOTYBAY_FB or scene_type == SceneType.TEAM_BOOTYBAY_FB then
        TipWGCtrl.Instance:ShowSystemMsg(Language.BootyBay.BaoTuDesc1)
        self:Close()
        return
    end

    --当前不在普通场景内
    if scene_type ~= SceneType.Common then
        TipWGCtrl.Instance:ShowSystemMsg(Language.BootyBay.BaoTuDesc2)
        self:Close()
        return
    end

    local bootybay_other_cfg = BootyBayWGData.Instance:GetOtherConfig()
    local role_level = RoleWGData.Instance.role_vo.level

    if bootybay_other_cfg.open_level > role_level then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Boss.CaveBossTips)
        self:Close()
        return
    end

    if scene_id ~= bootybay_scene_id then
        local scene_logic = Scene.Instance:GetSceneLogic()
        local data = BootyBayWGData.Instance:GetNowWabaoInfo()
        if scene_logic ~= nil and not IsEmptyTable(data) then
            local x, y = scene_logic:GetTargetScenePos(bootybay_scene_id)
            TaskWGCtrl.Instance:SendFlyByShoe(bootybay_scene_id, x or 0, y or 0, -1, false)
            BootyBayWGData.Instance:SetUseBaoTuInfo(data)
            BootyBayWGData.Instance:SetWaBaoType(WABAO_TYPE.WABAO_TYPE_BAOTU)
            BootyBayWGCtrl.Instance:FlushBootybaySceneFollowView()
        end
    else
        local data = BootyBayWGData.Instance:GetNowWabaoInfo()

        if IsEmptyTable(data) then
            return
        end
        --如果点击的是右侧要判断组队状态所以拦截一下
        BootyBayWGData.Instance:SetTempWaBaoInfo(data)
        BootyBayWGCtrl.Instance:SendCSWabaoOpera(WABAO_OP.WABAO_OP_GET_TEAM_STATUS)       --判断组队的队员是否满足状态
    end

    self:Close()
end
