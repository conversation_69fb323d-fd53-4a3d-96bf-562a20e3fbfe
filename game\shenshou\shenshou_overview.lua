ShenShouOverview = ShenShouOverview or BaseClass(SafeBaseView)

function ShenShouOverview:__init()
	self:SetMaskBg(false)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",{vector2 = Vector2(0, 11), sizeDelta = Vector2(1080, 612)})
	self:AddViewResource(0, "uis/view/shenshou_ui_prefab", "layout_shenshou_overview")
end

function ShenShouOverview:SetDataAndOpen(soul_ring_seq)
    self.soul_ring_seq = soul_ring_seq

    if self:IsOpen() then
        self:Flush()
    else
        self:Open()
    end
end

function ShenShouOverview:LoadCallBack()
    if not self.soul_ring_list then
        self.soul_ring_list = AsyncListView.New(SSOverviewSoulRingListCellRender, self.node_list.soul_ring_list)
    end

    self.node_list.title_view_name.text.text = Language.ShenShou.SoulRingOverviewTitle
end

function ShenShouOverview:ReleaseCallBack()
    if self.soul_ring_list then
        self.soul_ring_list:DeleteMe()
        self.soul_ring_list = nil
    end
end

function ShenShouOverview:OnFlush()
    local data_list = ShenShouWGData.Instance:GetSoulRingShowShenShouList(self.soul_ring_seq)
    self.soul_ring_list:SetDataList(data_list)

    local jump_index = 0

    if not IsEmptyTable(data_list) then
        for k, v in pairs(data_list) do
            local info = ShenShouWGData.Instance:GetShenShouInfoBySeq(v.soul_ring_seq)

            if not IsEmptyTable(info) then
                if info.shenshou_id == v.shou_id then
                    jump_index = k
                    break
                end
            end
        end
    end

    self.soul_ring_list:JumpToIndex(jump_index)
end

------------------------------------SSOverviewSoulRingListCellRender-----------------------------------
SSOverviewSoulRingListCellRender = SSOverviewSoulRingListCellRender or BaseClass(BaseRender)

function SSOverviewSoulRingListCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end
    
    local cfg = ShenShouWGData.Instance:GetSoulRingCfgBySoulRingSeq(self.data.soul_ring_seq)
	local bundle, asset = ResPath.GetShenShouImages("a3_soul_ring_icon_hl_" .. cfg.icon)
	self.node_list.img_icon.image:LoadSprite(bundle, asset, function()
		self.node_list.img_icon.image:SetNativeSize()
	end)

    self.node_list.desc_name.text.text = ToColorStr(self.data.name, ITEM_COLOR_DARK[self.data.quality])
    self.node_list.desc_pingfen.text.text = string.format(Language.ShenShou.SoulRingPingfen, TipWGData.Instance:GetCfgBasePingFenByAttrList(self.data))

    local info = ShenShouWGData.Instance:GetShenShouInfoBySeq(self.data.soul_ring_seq)
    if not IsEmptyTable(info) then
        local active = info.shenshou_id == self.data.shou_id
        self.node_list.flag_active:CustomSetActive(active)

        if not active then
            local active_num = 0
            local color = -1
            local star = -1
        
            for k, v in pairs(info.equip_list) do
                local shenshou_active_cfg = ShenShouWGData.Instance:GetQualityRequirementCfg(self.data.shou_id, k)
                
                if v.item_id > 0 then
                    local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)

                    if not IsEmptyTable(item_cfg) then
                        if item_cfg.color >= shenshou_active_cfg.slot_need_quality and v.start_level >= shenshou_active_cfg.slot_need_star then
                            active_num = active_num + 1
                        end
                    end
                end
    
                if color < 0 or (color > 0 and shenshou_active_cfg.slot_need_quality < color) then
                    color = shenshou_active_cfg.slot_need_quality
                 end
    
                 if star < 0 or (star > 0 and shenshou_active_cfg.slot_need_star < star) then
                     star = shenshou_active_cfg.slot_need_star
                  end
            end
    
            local color_str = color > 0 and Language.ShenShou.SoulRingYearName[color] or ""
            local star_str = star > 0 and string.format(Language.ShenShou.SoulRingActiveStar, star) or ""
            self.node_list.desc_active_tip.text.text = ToColorStr(string.format(Language.ShenShou.SoulRingActiveDesc, color_str, star_str, active_num, 5), COLOR3B.GRAY)
        else
            self.node_list.desc_active_tip.text.text = ""
        end
    end
    
	local attribute = AttributeMgr.GetAttributteByClass(self.data)
    local attr_data = {}

    for k,v in pairs(attribute) do
		if v > 0 then
			local vo  = {}
			vo.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(k,  true)
			vo.value = v
			attr_data[#attr_data + 1] = vo
		end
	end

    -- local attr_data = AttributeMgr.GetAttrNameAndValueByClass(self.data)
    for i = 1, 6 do
       local data = attr_data[i]
       local has_data = not IsEmptyTable(data)

       self.node_list["attr_" .. i]:CustomSetActive(has_data)

       if has_data then
            self.node_list["attr_name" .. i].text.text = data.attr_name
            self.node_list["attr_value" .. i].text.text = data.value
       end
    end
end