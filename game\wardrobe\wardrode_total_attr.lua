WardrobeTotalAttrView = WardrobeTotalAttrView or BaseClass(SafeBaseView)
function WardrobeTotalAttrView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/wardrobe_ui_prefab", "wardrobe_total_attr")
end

function WardrobeTotalAttrView:__delete()

end

function WardrobeTotalAttrView:ReleaseCallBack()
    if self.attr_list then
		self.attr_list:DeleteMe()
		self.attr_list = nil
	end
end

function WardrobeTotalAttrView:LoadCallBack()
    self.attr_list = AsyncListView.New(WardrobeTotalAttrRender, self.node_list.attr_list)
end

function WardrobeTotalAttrView:OnFlush()
    local data, cap = WardrobeWGData.Instance:GetAllActAttr()
    self.attr_list:SetDataList(data)
	self.node_list.cap_value.text.text = cap
end


------------------------------------WardrobeTotalAttrRender---------------------
WardrobeTotalAttrRender = WardrobeTotalAttrRender or BaseClass(BaseRender)
function WardrobeTotalAttrRender:OnFlush()
    if nil == self.data then
		return
	end

    local is_per = not EquipmentWGData.Instance:GetAttrIsPerByAttrStr(self.data.attr_str)
    local name = ""
    local show_star = false
    if self.data.attr_str == "add_per" then
        name = Language.Wardrobe.SpecialAttr
        show_star = true
    else
        name = Language.Common.AttrNameList2[self.data.attr_str]
    end

    local value = math.ceil(self.data.value)
    value = is_per and value or value * 0.01 .. "%"
    -- local attr_str = string.format("%s:  <color=%s>%s</color>",  name, COLOR3B.DEFAULT_NUM, value)
    -- self.node_list.attr.text.text = attr_str
    --self.node_list.icon:SetActive(show_star)

    self.node_list.attr_name.text.text = name
    self.node_list.attr_value.text.text = value
end
