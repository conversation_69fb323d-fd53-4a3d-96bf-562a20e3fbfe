function TianshenRoadView:InitDBView()
	XUI.AddClickEventListener(self.node_list.db_btn_tip, BindTool.Bind(self.OnDBBtn<PERSON>ip<PERSON><PERSON><PERSON><PERSON><PERSON>,self))

	local theme_cfg = TianshenRoadWGData.Instance:GetThemeCfgByTabIndex(TabIndex.tianshenroad_duobei)
	if theme_cfg ~= nil then
		self.node_list.db_tip_label.text.text = theme_cfg.rule_desc--theme_cfg.rule_tip
	end

	self.db_reward_list = AsyncListView.New(DuoBeiItemRender, self.node_list.db_list)

	self:FlushDBView()
	self:DBTimeCountDown()
end

function TianshenRoadView:ReleaseDBView()
	if self.db_reward_list then
		self.db_reward_list:DeleteMe()
		self.db_reward_list = nil
	end
	CountDownManager.Instance:RemoveCountDown("tianshenroad_duoibei_count_down")
end

function TianshenRoadView:TRDBShowIndexCallBack()
	self:DoTRDBAnim()
end

function TianshenRoadView:FlushDBView()
	self:FlushDBReward()
end

function TianshenRoadView:FlushDBReward()
	local info_list = TianshenRoadWGData.Instance:GetDuoBeiInfo()
	self.node_list.nodate:SetActive(IsEmptyTable(info_list))
	if info_list then
		self.db_reward_list:SetDataList(info_list)
		--self.node_list.db_list.scroll_rect.horizontalNormalizedPosition = 0
		self.node_list.db_list.scroll_rect.enabled = #info_list > 3
	end
end

function TianshenRoadView:OnDBBtnTipClickHnadler()
	local role_tip = RuleTip.Instance
	if role_tip then
		local title,desc = TianshenRoadWGData.Instance:GetActivityTip(TabIndex.tianshenroad_duobei)
		if title ~= nil and desc ~= nil then
			role_tip:SetTitle(title)
			role_tip:SetContent(desc)
		end
	end
end

--有效时间倒计时
function TianshenRoadView:DBTimeCountDown()
	CountDownManager.Instance:RemoveCountDown("tianshenroad_duoibei_count_down")
	local invalid_time = TianshenRoadWGData.Instance:GetActivityInValidTime(TabIndex.tianshenroad_duobei)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if invalid_time > server_time then
		self.node_list.db_time_label.text.text = TimeUtil.FormatSecondDHM2(invalid_time - server_time)
		CountDownManager.Instance:AddCountDown("tianshenroad_duoibei_count_down", BindTool.Bind1(self.UpdateDBCountDown, self), BindTool.Bind1(self.DBTimeCountDown, self), invalid_time, nil, 1)
	end
end

function TianshenRoadView:UpdateDBCountDown(elapse_time, total_time)
	self.node_list.db_time_label.text.text = TimeUtil.FormatSecondDHM2(total_time - elapse_time)
end

function TianshenRoadView:DoTRDBAnim()
	local tween_info = UITween_CONSTS.TianshenRoadView
    UITween.FakeHideShow(self.node_list["ts_duobei_root"])
    RectTransform.SetAnchoredPositionXY(self.node_list["db_list"].rect, 1200, -95)
   
    self.node_list["db_list"].rect:DOAnchorPos(Vector2(194, -95), tween_info.MoveTime)
    UITween.AlphaShow(GuideModuleName.TianShenRoadPanel, self.node_list["ts_duobei_root"], 0, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
 --    ReDelayCall(self, function()

	-- end, tween_info.AlphaDelay, "kaifu_bipin_panel_tween")
    --UITween.DoUpDownCrashTween(self.node_list["ts_duobei_title_img"])
end

-------------------------------------

DuoBeiItemRender = DuoBeiItemRender or BaseClass(BaseRender)

function DuoBeiItemRender:LoadCallBack()
	--2021/09/10 策划jxw 说屏蔽奖励展示
	--self.db_reward_item_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
	self.node_list.btn.button:AddClickListener(BindTool.Bind(self.OnBtnClickDuoBei, self))
end

function DuoBeiItemRender:__delete()
	-- if self.db_reward_item_list then
	-- 	self.db_reward_item_list:DeleteMe()
	-- 	self.db_reward_item_list = nil
	-- end
end

function DuoBeiItemRender:OnFlush()
	local data = self:GetData()
	if not data then
		return
	end

	local bundle, asset = ResPath.GetCommonIcon(data.cfg.icon)
	self.node_list.icon.image:LoadSpriteAsync(bundle, asset, function ()
		self.node_list.icon.image:SetNativeSize()
	end)
	--self.node_list.beishu.text.text = string.format("x%d", data.cfg.reward_mult)
	local beishu = CommonDataManager.GetDaXie(data.cfg.reward_mult)
	beishu = beishu == Language.TianShenRoad.twoconversion[1] and Language.TianShenRoad.twoconversion[2] or beishu
	--self.node_list.beishu.text.text = string.format(Language.TianShenRoad.beishudown,beishu)

	--self.node_list.name.text.text = data.cfg.wanfa_name

	-- if IsEmptyTable(data.cfg.reward_item) then
	-- 	local reward_list = TianshenRoadWGData.Instance:GetDuoBeiRewardListByTaskType(data.cfg.task_type)
	-- 	if reward_list then
	-- 		self.db_reward_item_list:SetDataList(reward_list)
	-- 	end
	-- else
	-- 	local list = SortTableKey(data.cfg.reward_item)
	-- 	self.db_reward_item_list:SetDataList(list)
	-- end
end

function DuoBeiItemRender:OnBtnClickDuoBei()
	local data = self:GetData()
	if data and data.cfg then
		FunOpen.Instance:OpenViewNameByCfg(data.cfg.panel)
	end
end