LordEveryDayShopConvertTips = LordEveryDayShopConvertTips or BaseClass(SafeBaseView)

function LordEveryDayShopConvertTips:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -9), sizeDelta = Vector2(679, 449)})
	self:AddViewResource(0, "uis/view/lord_every_day_shop_prefab", "layout_lord_shop_convert")
end

function LordEveryDayShopConvertTips:__delete()
end

function LordEveryDayShopConvertTips:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end	
end

function LordEveryDayShopConvertTips:OpenCallBack()
	self.amount = 1
end 

function LordEveryDayShopConvertTips:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["item_cell"])
        self.item_cell:SetIsShowTips(true)
    end

	XUI.AddClickEventListener(self.node_list["btn_OK"], BindTool.Bind(self.OnClickOKBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_minus"], BindTool.Bind(self.OnClickMinus, self))
	XUI.AddClickEventListener(self.node_list["btn_plus"], BindTool.Bind(self.OnClickPlus, self))
	self.node_list.num_slider.slider.onValueChanged:AddListener(BindTool.Bind(self.OnSliderValueChange, self))
end

function LordEveryDayShopConvertTips:ShowIndexCallBack()
    self.max_num = 1
    self.node_list.num_slider.slider.minValue = 0
	self.node_list.num_slider.slider.value = 1
end

function LordEveryDayShopConvertTips:SetDataAndOpen(convery_data)
	self.convery_seq = convery_data.convery_seq
    self.convery_buy_time = convery_data.buy_time or 0
    self:Open()
end

function LordEveryDayShopConvertTips:OnClickOKBtn()
    if not self.convery_seq then 
        return 
    end

    local cfg = LordEveryDayShopWGData.Instance:GetShopCfgBySeq(self.convery_seq)
    if not cfg then
        self:Close()
        return
    end

    local item_count = ItemWGData.Instance:GetItemNumInBagById(cfg.consume_item_id)
	local num = math.floor(self.num_value)
    local one_need = cfg.consume_num or 1

    if item_count < num * one_need then
        TipWGCtrl.Instance:ShowSystemMsg(Language.LordEveryDayShop.StuffNotEnough)
        self:Close()
        return
    end

	LordEveryDayShopWGCtrl.Instance:SendCSLordShopOperateRequest(LORD_EVERYDAY_SHOP_TYPE.SHOP_BUY, self.convery_seq, num)
	self:Close()
end

function LordEveryDayShopConvertTips:OnFlush()
	if not self.convery_seq then 
        return 
    end

	local cfg = LordEveryDayShopWGData.Instance:GetShopCfgBySeq(self.convery_seq)
    if not cfg then
        return
    end

    self:FlushConsume(self.amount)

    self.item_cell:SetData(cfg.reward_item[0])
    local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.reward_item[0].item_id)
    self.node_list.name.text.text = item_cfg and item_cfg.name or ""
    local item_count = ItemWGData.Instance:GetItemNumInBagById(cfg.consume_item_id)
    local one_need = cfg.consume_num or 1
    local max_value = math.floor(item_count / one_need) > 1 and math.floor(item_count / one_need) or 1
    local max_exchang_num = cfg.buy_limit - self.convery_buy_time
    max_value = math.min(max_value, max_exchang_num)
    self.max_num = max_value
    
    self.node_list.num_slider.slider.maxValue = max_value
end

function LordEveryDayShopConvertTips:FlushConsume(value)
    if not self.convery_seq then 
        return 
    end

	local cfg = LordEveryDayShopWGData.Instance:GetShopCfgBySeq(self.convery_seq)
    if not cfg then
        return
    end

    local item_count = ItemWGData.Instance:GetItemNumInBagById(cfg.consume_item_id)
    local one_need = cfg.consume_num or 1

	self.node_list.need_num.text.text = value * one_need
	self.node_list.has_num.text.text = item_count
	self.node_list.num.text.text = value
	self.num_value = value
end

function LordEveryDayShopConvertTips:OnSliderValueChange(value)
	self.amount = value
	self:FlushConsume(value)
end

function LordEveryDayShopConvertTips:OnClickMinus()
	self.amount = math.max(self.amount - 1, 1)
	self:FlushConsume(self.amount)
	self.node_list.num_slider.slider.value = self.amount
end

function LordEveryDayShopConvertTips:OnClickPlus()
	self.amount = math.min(self.amount + 1, self.max_num)
	self:FlushConsume(self.amount)
	self.node_list.num_slider.slider.value = self.amount
end
