Config = Config or {}
Config.SCENE_TILE_HEIGHT = 1						-- 一格的高度（米）
Config.SCENE_TILE_WIDTH = 1						-- 一格的宽度（米）

Config.SCENE_LOW_SPEED_TIME = 0.7					-- 角色慢速时间
Config.SCENE_LOW_SPEED_FACTOR = 0.75				-- 角色慢速系数
Config.SCENE_ROLE_MOVE_SPEED = 900					-- 角色模型默认移动(美术或策划调)
Config.SCENE_MOUNT_MOVE_SPEED = 1500				-- 坐骑模型默认移动(美术或策划调)
Config.SCENE_ROLE_JUMP_UP_MAX_HIGHT = 2000			-- 角色抬升高度最高值

chuangjue_bundle = "scenes/map/a3_cj_chuangjue_main"
chuangjue_asset = "A3_CJ_ChuangJue_Main"

-- 场景类型
SceneType = {
	Common = 0,										-- 普通场景
	GuildStation = 1,								-- 军团驻地
	ZhuXie = 2,										-- 诛邪战场
	SCENE_TYPE_CROSS_CHESTSHOP_BOSS = 3,			-- 寻宝boss
	CoinFb = 33333,									-- 铜钱副本 (废弃)
	TianShen3v3Prepare = 4, 						-- 天神3v3准备场景
	TianShen3v3 = 5, 								-- 天神3v3战斗场景
	WorldsNO1Prepare = 6, 							-- 天下第一准备场景
	WorldsNO1 = 7, 									-- 天下第一战斗场景
	GongChengZhan = 8,								-- 攻城战
	XianMengzhan = 9,								-- 仙盟战
	CampStation = 10,								-- 阵营驻地
	HunYanFb = 11,									-- 婚宴副本
	NationalBoss = 12,								-- 神兽禁地(全民boss)
	ChallengeFB = 13,								-- 挑战副本 （爬塔）
	GuildMonster = 14,								-- 仙盟神兽
	Field1v1 = 15, 									-- 1v1
	StoryFB = 16,									-- 剧情副本
	TeamFB = 17, 									-- 多人副本
	QingYuanFB = 18,								-- 情缘副本
	ZhanShenDianFB = 19,							-- 战神殿副本
	ShenMoZhiXiFB = 20,								-- 神魔之隙副本
	ChaosWar = 21,									-- 一战到底
	GuildMiJingFB = 23,								-- 仙盟秘境
	WuXingFB = 24,									-- 五行打宝
	TransferProfTask = 25,							-- 闭关之境
	MiGongXianFu = 26,								-- 迷宫仙府
	Kf_shuijing = 27,								-- 跨服水晶裂痕
	Kf_Honorhalls= 28,								-- 跨服青云之巅
	Kf_OneVOne= 29,									-- 跨服1v1
	Kf_PVP= 30,										-- 跨服3v3
	-- Fb_Welkin = 31,								-- 天宫试炼
	GuildBoss = 32,									-- 仙盟Boss
	YaoShouPlaza = 33,								-- 妖兽广场
	SuoYaoTa = 34,									-- 锁妖塔
	ShuiJing = 35,									-- 水晶
	ZhongKui = 36,									-- 钟馗捉鬼
	-- CampGaojiDuobao = 37,						-- 师门高级夺宝
	WUSHUANGFB = 37,								-- 无双副本
	Fb_Welkin = 40, 								-- 天仙阁
	LingyuFb = 41, 									-- 灵玉副本
	EquipFb = 42,									-- 装备本
	WelfareFb = 43,									-- 福利本
	WeaponFb = 44,									-- 武器本
	-- Fb_ExpFb = 46,								-- 经验副本
	ShiTuPata = 47,									-- 师徒爬塔
	DaBaoFb = 48,									-- 限时打宝副本
	GaoZhanFb = 49,									-- 高战副本
	HotSpring = 51, 	 							-- 温泉
	MultiChallenge = 54,							-- 团队爬塔
	XinShouMountFb = 55,							-- 新手坐骑副本
	XinShouFabaoFb = 56,							-- 新手法宝副本
	XinShouJuqingFb = 65,							-- 新手剧情副本
	ExpFb = 66,										-- 经验副本
	QunXianLuanDou = 67,							-- 三界战场

	--------------------------
	-- 新副本
	Wujinjitan = 45,								-- 无尽祭坛(组队经验本)
	KF_BOSS = 53,									-- 跨服Boss
	AnswerFb = 68,									-- 答题
	PAOKU = 69,										-- 跑酷
	Kf_OneVsN = 70,									-- 跨服1vn
	--TEAM_FB = 71,									-- 跨服组队本
	-- DreamDanceParty = 72,							-- 跨服梦幻舞会
	FeixianBoss = 73,								-- 飞仙boss
	-- ManyTowerFB = 75,								-- 多人塔防
	WorldBoss = 75,									-- 世界boss
	DABAO_BOSS = 76,								-- 打宝Boss
	VIP_BOSS = 77,									-- VipBoss --魔王巢穴

	KF_HotSpring = 78,								-- 温泉
	KF_DUCK_RACE = 79, 								-- 跨服小鸭疾走
	KF_AnswerFb = 179,								-- 答题

	ZHUANZHI_FB = 80,								-- 转职副本
	Fishing = 81,									-- 钓鱼
	GUILD_ANSWER_FB = 82,							-- 帮派答题
	COPPER_FB = 83,									-- 铜币副本
	PET_FB = 84,									-- 宠物副本

	DEVILCOME_FB = 85,								-- 魔王副本
	GAINT_FB = 86,									-- 擎天玉柱
	PROFAIRY_FB = 87,								-- 守护仙女
	KUNLUNTRIAL_FB = 88,							-- 昆仑试炼
	SCENE_TYPE_TASKFB_ZHILIAO = 89,					-- 任务副本-治疗怪
	TEAM_EQUIP_FB = 90,								-- 多人装备本
	DEMONS_FB = 91,									-- 心魔副本
	FBCT_NEWPLAYERFB = 92,							-- 新手副本
	PERSON_BOSS = 93,                               -- 个人副本
	HIGH_TEAM_EQUIP_FB = 94,                        -- 远古仙殿
	XQJF_XING_TIAN_LAI_XI = 95,                     -- 仙器解封--刑天来袭
	JIUSHEDAO_FB = 97,                          	-- 仙盟禁地
	INFINITEHELL_FB = 98,                           -- 无尽神狱
	YEZHANWANGCHENGFUBEN = 99,                      -- 夜战王城

	LUANDOU_BATTLE = 100,							-- 乱斗战场
	ZHUSHENTA_FB = 101 + 100000,					-- 诛神塔(没了被覆盖了)
	TIANSHEN_JIANLIN = 101,							-- 天神降临
	SHENGYU_BOSS = 102 + 100000,					-- 圣域boss(没了被覆盖了)
	XINGTIANLAIXI_FB = 102,							-- 魔王降临
	BATTLE_SOUL = 103 + 100000,						-- 战场之魂(没了被覆盖了)
	MOWU_JIANLIN = 103,								-- 魔物降临
	KFSHENYUN_FB = 104,								-- 陨落boss
	CROSS_LIEKUN = 105, 							-- 猎鲲副本
	MJ_BOSS = 106,									-- 秘境boss
	MJ_KF_BOSS = 107,								-- 秘境跨服boss
	GUIDE_BOSS = 108,								-- boss引导
	Kf_OneVOne_Prepare = 109,						-- 跨服1v1准备场景类型
	Kf_PvP_Prepare = 110,						    -- 跨服3v3准备场景类型
	LingHunGuangChang = 111, 						--灵魂广场
	KF_BOSS_FIGHT = 112, 							-- 首领乱斗
	YUGUXIANDIANYINDAO = 114,                       --远古仙殿引导
	BOOTYBAY_FB = 115,								--藏宝湾单人副本 / 蓬莱仙洞
	TEAM_BOOTYBAY_FB = 116,							--藏宝湾组队副本 / 蓬莱仙洞

	TIAN_SHEN_FB = 117, 							--天神副本

	BaGuaMiZhen_FB = 119, 							--八卦迷阵副本

	ManHuangGuDian_FB = 120, 						--蛮荒古殿副本

	HONG_MENG_SHEN_YU = 121,						-- 鸿蒙神域

	KFZhuXieZhanChang = 122,						-- 跨服诛邪战场

	FakeTianShenFb = 123,							-- 伪天神副本

	FakePetFb = 124,								-- 伪宠物副本

	SG_BOSS = 125,									-- 上古遗迹
    Shenyuan_boss = 126,							-- 深渊boss

	GuDaoJiZhan_FB = 128,							-- F2孤岛激战

	FengShenBang = 127,								-- 封神榜
	Guild_Invite = 129,								-- 仙盟争霸定级赛
	ETERNAL_NIGHT = 130,							-- 永夜之巅

	CROSS_TASK_CHAIN = 133,							-- 任务链活动场景
	CROSS_TASK_CHAIN_HUSONG = 134,					-- 任务链活动场景 护送
	CROSS_TASK_CHAIN_XUN_LUO_CAI_JI = 135,			-- 任务链活动场景 巡逻采集
	CROSS_TASK_CHAIN_CAI_JI = 136,					-- 任务链活动场景 采集
	CROSS_TASK_CHAIN_AVOID = 137,					-- 任务链活动场景 16宫格躲避
	CROSS_TASK_CHAIN_YUN_SONG_AVOID = 138,			-- 任务链活动场景 运送躲避
	CROSS_TASK_CHAIN_GUARD = 139,					-- 任务链活动场景 守护
	CROSS_TASK_CHAIN_BOSS = 140,					-- 任务链活动场景 boss

	ETERNAL_NIGHT_FINAL = 141,						-- 永夜之巅 决赛圈
    XianJie_Boss = 149,                             -- 仙界boss
	TowerDefend = 150,								-- 塔防
	DefenseFb = 151,								-- 塔防副本
	SCENE_TYPE_KF_COUNTRY_SECRET_AREA = 152,		-- 国家星图秘境
	CrossLongMai = 153,								-- 跨服龙脉
	SCENE_TYPE_CROSS_YANGLONG = 154,				-- 跨服养龙
	SPECIAL_PERSONAL_BOSS = 155,					-- 个人特殊BOSS
	GHOST_FB_GLOBAL = 156,							-- 捉鬼副本 - 公共
	GHOST_FB_PERSON = 157,							-- 捉鬼副本 - 个人
	CROSS_FLAG_GRABBING_BATTLEFIELD = 158,          -- 跨服夺旗战场
	CROSS_PK_LOVER = 160,							--跨服仙侣PK（PK场景）
	CROSS_PK_LOVER_READY = 161,						--跨服仙侣PK准备（初赛和周赛）
	PERSON_MABI_BOSS_DISPLAY = 162,                 -- 个人麻痹BOSS技能展示副本
	CROSS_DIVINE_DOMAIN = 163,						-- 跨服圣天神域
	CROSS_DIVINE_DOMAIN_MOBAI = 164,				-- 跨服圣天神域膜拜
	CROSS_ULTIMATE_BATTLE_READY = 166,				-- 终极战场（准备场景）
	CROSS_ULTIMATE_BATTLE = 167,				    -- 终极战场（战斗场景）
	CROSS_EVERYDAY_RECHARGE_BOSS = 168,             -- 每日真充boss
	WUHUN_TOWER_FB = 169,							-- 武魂塔副本场景
	CROSS_LAND_WAR = 170,                           -- 阵地战
	SCENE_TYPE_EXP_WEST_FB = 171,					-- 天山修炼副本场景
	PHANTOM_DREAMLAND_FB = 172,						-- 幻梦秘境
	WORLD_TREASURE_JIANLIN = 173,					-- 试炼副本(原101,活动改系统，后端不好弄，给了个新的)
	SCENE_TYPE_LAND_WAR_FB = 174,					-- 阵地战 - 副本

	CROSS_AIR_WAR = 175,							-- 跨服空战
	ARENA_TIANTI = 176,                             -- 天梯争霸 
	BOSS_INVASION = 177,                            -- boss入侵
	HUNDRED_EQUIP = 178,							-- 百倍爆装
	SCENE_TYPE_DRAGON_TRIALT_FB = 179,				-- 龙神试炼 - 副本
	TEAM_COMMON_BOSS_FB_1 = 180,                    -- 通用组队副本1(幻兽)
	TEAM_COMMON_BOSS_FB_2 = 181,                    -- 通用组队副本2(女神)
	TEAM_COMMON_BOSS_FB_3 = 182,                    -- 通用组队副本2(武魂)
	TEAM_COMMON_TOWER_FB_1 = 183,                    -- 组队符文塔
	TEAM_COMMON_TOWER_FB_2 = 184,                    -- 组队符文塔2（预留）
	QIXI_FESTIVAL_FB_1 = 185,						-- 七夕场景
}

-- 显示跨服模式场景类型
ShowSwitchServerModeSceneType = {
	SceneType.KF_BOSS,
	SceneType.SG_BOSS,
	SceneType.MJ_BOSS,
	SceneType.KF_BOSS_FIGHT,
	SceneType.HONG_MENG_SHEN_YU,
	SceneType.Shenyuan_boss,
    SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS,
    SceneType.XianJie_Boss,
	SceneType.SCENE_TYPE_CROSS_YANGLONG,
}

-- 显示宗门模式场景类型
ShowSwitchGuildModeSceneType = {
	SceneType.WorldBoss,
	SceneType.VIP_BOSS,
	SceneType.DABAO_BOSS,
	SceneType.KF_BOSS,
	SceneType.SG_BOSS,
	SceneType.MJ_BOSS,
	SceneType.KF_BOSS_FIGHT,
	SceneType.HONG_MENG_SHEN_YU,
	SceneType.Shenyuan_boss,
    SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS,
    SceneType.XianJie_Boss,
	SceneType.CrossLongMai,
}

XunLuStatus = {
	None = 0,
	XunLu = 1,
	AutoFight = 2,
	AutoWaBao = 3,
}

-- 恶名等级
EvilColorList = {
	NAME_COLOR_WHITE = 0,							-- 白色
	NAME_COLOR_RED_1 = 1,							-- 红色1
	NAME_COLOR_RED_2 = 2,							-- 红色2
	NAME_COLOR_RED_3 = 3,							-- 红色3
	NAME_COLOR_MAX = 4
}

-- 场景对象类型
SceneObjType = {
	Unknown = 0,
	Role = 1,										-- 角色
	Monster = 2,									-- 怪物
	FallItem = 3,									-- 掉落物
	GatherObj = 4,									-- 采集物
	ServerEffectObj = 6,							-- 服务端场景特效
	ShenShi = 9,									-- 战场神石
	MarryObj = 11, 									-- 结婚巡逻

	MainRole = 20,									-- 主角
	SpriteObj = 30,									-- 精灵
	Npc = 31,										-- NPC
	Door = 32,										-- 传送点
	Decoration = 33,								-- 装饰物
	EffectObj = 34,									-- 特效
	TruckObj = 35,									-- 镖车
	EventObj = 36, 									-- 世界事件物品
	Pet = 37, 										-- 宠物
	MultiMountObj = 38,								-- 双人坐骑
	GoddessObj = 39,								-- 女神
	JumpPoint = 41,									-- 跳跃点
	Trigger = 42,									-- 触发物
	MingRen = 43, 									-- 名人堂
	BeautyObj = 44,									-- 美人
	BoatObj = 45, 									-- 温泉皮艇
	FollowObj = 46, 								-- 跟随物
	MingJiangObj = 47, 								-- 名将
	TestRole = 48, 									-- 测试角色
	CoupleHaloObj = 49, 							-- 夫妻光环
	FakeNpc = 50, 									-- 客户端假npc
	SoulBoyObj = 51,								-- 灵童
	DefenseTowerObj = 52,							-- 防御塔
	BossStoneObj = 53,								-- BOSS墳墓
	GuardObj = 54,									-- 守护精灵
	Baby = 55, 										-- 孩子
	FollowNpc = 56, 								-- 跟随NPC
	Statue = 58, 									-- 雕像
	DogObj = 59,									-- 天神的狗
	FollowMingJiang = 60,							-- 跟随天神
	CityOwnerStatue = 61,							-- 城主雕像
	XiaoTianQuan = 62,								-- 哮天犬
	QiongQiLaiXi = 63,								-- 穷奇来袭
    SkillShower = 64,								-- 技能展示
    AnswerCircle = 65,								-- Aiboss答题的圈
	WuHunZhenShen = 66,								-- 武魂真身
	BeastObj = 67,									-- 灵兽
	ShuangShengTianShen = 68,						-- 双生神灵
	TaskCallInfoObj = 69,                           -- 任务召唤物
}

-- 场景对象状态
SceneObjState = {
	Stand = "idle",									-- 站立
	Move = "run",									-- 移动
	Dead = "die",									-- 死亡
	Atk = "atk",									-- 攻击
	Hurt = "hurt",									-- 受击
	Interactive = "Interactive",					-- 互动
	
}

-- 选择类型
SelectType = {
	All = 0,										-- 全部
	Friend = 1,										-- 友方
	Enemy = 2,										-- 敌方
	Alive = 3,										-- 活着的
}



ANIMATOR_PARAM = {
	STATUS = UnityEngine.Animator.StringToHash("status"),
	ATTACK1 = UnityEngine.Animator.StringToHash("attack1"),
	COMBO1_1 = UnityEngine.Animator.StringToHash("combo1_1"),
	COMBO1_2 = UnityEngine.Animator.StringToHash("combo1_2"),
	COMBO1_3 = UnityEngine.Animator.StringToHash("combo1_3"),
	HURT = UnityEngine.Animator.StringToHash("hurt"),
	REST = UnityEngine.Animator.StringToHash("rest"),
	SHOW = UnityEngine.Animator.StringToHash("show"),
	FIGHT = UnityEngine.Animator.StringToHash("fight"),
	COMBO1_1_BACK = UnityEngine.Animator.StringToHash("combo1_1_back"),
	COMBO1_2_BACK = UnityEngine.Animator.StringToHash("combo1_2_back"),
	COMBO1_3_BACK = UnityEngine.Animator.StringToHash("combo1_3_back"),
	Fight_Idle = UnityEngine.Animator.StringToHash("fight_idle"),
	BASE_LAYER = 0,
	FLY_LAYER = 1,
	MOUNT_LAYER = 2,
	FIGHTMOUNT_LAYER = 3,
	CHONGCI_LAYER = 4,

	SWIMMING_LAYER = 2,
	SWIMMINGACTION_LAYER = 3,
}

-- 部件
SceneObjPart = {
	Main = 0,										-- 主体
	Weapon = 1,										-- 武器
	Weapon2 = 2,									-- 武器2
	Wing = 3,										-- 翅膀
	Mount = 4,										-- 坐骑
	Particle = 5,									-- 特效
	Halo = 6,										-- 光环
	BaoJu = 8,										-- 宝具
	Mantle = 9,										-- 披风
	FaZhen = 10,									-- 法阵
	HoldBeauty = 11,								-- 抱美人
	Head = 12,										-- 头部
	Foot = 13,										-- 足迹
	QiLinBi = 14,									-- 麒麟臂
	Waist = 15,										-- 腰饰
	Mask = 16,										-- 面饰
	Tail = 17,										-- 尾巴
	ShouHuan = 18,									-- 手环
	Jianling = 19,									-- 剑阵

	GundamLArm = 20,								-- 高达 左臂
	GundamRArm = 21,								-- 高达 右臂
	GundamLLeg = 22,								-- 高达 左腿
	GundamRLeg = 23,								-- 高达 右腿
	GundamLWing = 24,								-- 高达 左翼
	GundamRWing = 25,								-- 高达 右翼

	SkillHalo = 34,									-- 技能光环
	GodOrDemonHalo = 35,							-- 一念神魔光环
	WuHunZhenShen = 36,								-- 武魂真身
	FightMount = 37,								-- 战斗坐骑
	ShuangShengTianShen = 38,						-- 双生武魂
	ShuangShengTianShenUI = 39,						-- 双生武魂(UI)
	SoulFormation = 40,								-- 魂阵
	RiderSword = 41,								-- 御剑

	FoorTrail = 50,                                 -- 足迹拖尾

	SoulRing1 = 61,                                  -- 魂环1
	SoulRing2 = 62,                                  -- 魂环2
	SoulRing3 = 63,                                  -- 魂环3
	SoulRing4 = 64,                                  -- 魂环4
	SoulRing5 = 65,                                  -- 魂环5
	SoulRing6 = 66,                                  -- 魂环6
	SoulRing7 = 67,                                  -- 魂环7
	SoulRing8 = 68,                                  -- 魂环8

	SoulRingSelect = 69,                                  -- 选中魂环
}

-- 挂点
AttachPoint = {
	UI = 0,											-- UI挂点
	BuffTop = 1,									-- BUFF挂点上
	BuffMiddle = 2,									-- BUFF挂点中
	BuffBottom = 3,									-- BUFF挂点下
	Hurt = 4,										-- 受击胸口挂点
	HurtRoot = 5,									-- 受击脚底挂点
	Weapon = 6,										-- 武器挂点
	Weapon2 = 7,									-- 武器挂点
	Mount = 8,										-- 坐骑挂点
	Wing = 9,										-- 翅膀挂点
	Root = 10,										-- root点(目前使用：法阵)
	Bao = 10,										-- 抱物品挂点
	LeftHand = 11,									-- 左手挂点
	RightHand = 12,									-- 右手挂点
	Head = 13,										-- 头挂点
	Tail = 14,										-- 尾巴挂点
	Waist = 15,										-- 腰饰挂点
	FaBao = 16,										-- 法宝挂点
}

-- Main身上挂的部件
PartAttachPoint = {
	-- [SceneObjPart.Weapon] = AttachPoint.Weapon,
	[SceneObjPart.Particle] = AttachPoint.Hurt,
	-- 2022/2/25 光环会随角色胸部运动动作诡异，暂时修改为AttachPoint.HurtRoot
	[SceneObjPart.Halo] = AttachPoint.HurtRoot,
	[SceneObjPart.QiLinBi] = AttachPoint.RightHand,
	[SceneObjPart.Waist] = AttachPoint.Waist,
	[SceneObjPart.Mask] = AttachPoint.Head,
	[SceneObjPart.Tail] = AttachPoint.Tail,
	[SceneObjPart.ShouHuan] = AttachPoint.LeftHand,
	[SceneObjPart.Jianling] = AttachPoint.Wing,
	[SceneObjPart.SkillHalo] = AttachPoint.HurtRoot,
	[SceneObjPart.WuHunZhenShen] = AttachPoint.HurtRoot,
	[SceneObjPart.ShuangShengTianShen] = AttachPoint.HurtRoot,
	[SceneObjPart.ShuangShengTianShenUI] = AttachPoint.HurtRoot,
	[SceneObjPart.GodOrDemonHalo] = AttachPoint.HurtRoot,
	[SceneObjPart.SoulFormation] = AttachPoint.BuffMiddle,
	[SceneObjPart.FoorTrail] = AttachPoint.Root,
	[SceneObjPart.SoulRing1] = AttachPoint.Root,
	[SceneObjPart.SoulRing2] = AttachPoint.Root,
	[SceneObjPart.SoulRing3] = AttachPoint.Root,
	[SceneObjPart.SoulRing4] = AttachPoint.Root,
	[SceneObjPart.SoulRing5] = AttachPoint.Root,
	[SceneObjPart.SoulRing6] = AttachPoint.Root,
	[SceneObjPart.SoulRing7] = AttachPoint.Root,
	[SceneObjPart.SoulRing8] = AttachPoint.Root,
	[SceneObjPart.SoulRingSelect] = AttachPoint.Root,
	[SceneObjPart.RiderSword] = AttachPoint.Root,
}

-- Role部件所需要的细节等级
RoleDetailLevel = {
	[SceneObjPart.Main] = SceneObjDetailLevel.Low,
	[SceneObjPart.Weapon] = SceneObjDetailLevel.Middle,
	[SceneObjPart.Wing] = SceneObjDetailLevel.Low,
	[SceneObjPart.Mount] = SceneObjDetailLevel.Low,
	[SceneObjPart.FightMount] = SceneObjDetailLevel.Low,
	[SceneObjPart.Particle] = SceneObjDetailLevel.High,
	[SceneObjPart.Halo] = SceneObjDetailLevel.Middle,
	[SceneObjPart.SkillHalo] = SceneObjDetailLevel.Middle,
	[SceneObjPart.GodOrDemonHalo] = SceneObjDetailLevel.Middle,
	[SceneObjPart.BaoJu] = SceneObjDetailLevel.High,
	[SceneObjPart.Mantle] = SceneObjDetailLevel.Middle,
	[SceneObjPart.FaZhen] = SceneObjDetailLevel.Middle,
	[SceneObjPart.HoldBeauty] = SceneObjDetailLevel.Low,
	[SceneObjPart.QiLinBi] = SceneObjDetailLevel.High,
	[SceneObjPart.ShouHuan] = SceneObjDetailLevel.High,
	[SceneObjPart.Mask] = SceneObjDetailLevel.High,
	[SceneObjPart.WuHunZhenShen] = SceneObjDetailLevel.Middle,
	[SceneObjPart.ShuangShengTianShen] = SceneObjDetailLevel.Middle,
	[SceneObjPart.ShuangShengTianShenUI] = SceneObjDetailLevel.Middle,
	[SceneObjPart.SoulFormation] = SceneObjDetailLevel.Low,
	[SceneObjPart.FoorTrail] = SceneObjDetailLevel.Low,
	[SceneObjPart.SoulRing1] = SceneObjDetailLevel.Low,
	[SceneObjPart.SoulRing2] = SceneObjDetailLevel.Low,
	[SceneObjPart.SoulRing3] = SceneObjDetailLevel.Low,
	[SceneObjPart.SoulRing4] = SceneObjDetailLevel.Low,
	[SceneObjPart.SoulRing5] = SceneObjDetailLevel.Low,
	[SceneObjPart.SoulRing6] = SceneObjDetailLevel.Low,
	[SceneObjPart.SoulRing7] = SceneObjDetailLevel.Low,
	[SceneObjPart.SoulRing8] = SceneObjDetailLevel.Low,
	[SceneObjPart.SoulRingSelect] = SceneObjDetailLevel.Low,
}

-- SoulBoy部件所需要的细节等级
SoulBoyDetailLevel = {
	[SceneObjPart.Main] = SceneObjDetailLevel.Middle,
	[SceneObjPart.Mount] = SceneObjDetailLevel.Middle,
}

-- Role部件屏蔽规则
RolePartQualityRules = {
	[SceneObjPart.Halo] = ShieldObjType.PartHalo,
	[SceneObjPart.Mask] = ShieldObjType.PartMask,
	[SceneObjPart.Waist] = ShieldObjType.PartWaist,
	[SceneObjPart.ShouHuan] = ShieldObjType.PartShouHuan,
	[SceneObjPart.Foot] = ShieldObjType.PartFoot,
	[SceneObjPart.BaoJu] = ShieldObjType.PartBaoJu,
	[SceneObjPart.Tail] = ShieldObjType.PartTail,
	[SceneObjPart.Wing] = ShieldObjType.PartWing,
	[SceneObjPart.Jianling] = ShieldObjType.PartJianling,
	[SceneObjPart.FaZhen] = ShieldObjType.PartFaZhen,
	[SceneObjPart.SkillHalo] = ShieldObjType.PartSkillHalo,
	[SceneObjPart.GodOrDemonHalo] = ShieldObjType.PartGodOrDemonHalo,
	[SceneObjPart.FoorTrail] = ShieldObjType.PartFootTrail,
	[SceneObjPart.SoulRing1] = ShieldObjType.PartSoulRing1,
	[SceneObjPart.SoulRing2] = ShieldObjType.PartSoulRing2,
	[SceneObjPart.SoulRing3] = ShieldObjType.PartSoulRing3,
	[SceneObjPart.SoulRing4] = ShieldObjType.PartSoulRing4,
	[SceneObjPart.SoulRing5] = ShieldObjType.PartSoulRing5,
	[SceneObjPart.SoulRing6] = ShieldObjType.PartSoulRing6,
	[SceneObjPart.SoulRing7] = ShieldObjType.PartSoulRing7,
	[SceneObjPart.SoulRing8] = ShieldObjType.PartSoulRing8,
	[SceneObjPart.SoulRingSelect] = ShieldObjType.PartSoulRingSelect,
}

-- Role部件特效屏蔽规则
RolePartEffectQualityRules = {
	[SceneObjPart.Weapon] = ShieldObjType.PartWeaponEffect,
	[SceneObjPart.Tail] = ShieldObjType.PartTailEffect,
	[SceneObjPart.Wing] = ShieldObjType.PartWingEffect,
	[SceneObjPart.Jianling] = ShieldObjType.PartJianlingEffect,
	[SceneObjPart.BaoJu] = ShieldObjType.PartBaoJuEffect,
	[SceneObjPart.Mount] = ShieldObjType.PartMountEffect,
	[SceneObjPart.FightMount] = ShieldObjType.PartMountEffect,
}

-- 动作
SceneObjAnimator = {
	Idle = "idle",
	CGIdle = "cg_idle",
	UiIdle = "ui_idle",
	Move = "run",
	FightMove = "fight_run",
	Die = "die",
	Dead = "dead",
	DeadImm = "die_imm",
	Hurt = "hurt",
	Atk0 = "attack0",
	Atk1 = "attack1",
	Atk2 = "attack2",
	Atk3 = "attack3",
	Atk4 = "attack4",
	Atk5 = "attack5",
	Atk6 = "attack6",
	Atk7 = "attack7",
	FallImm = "fall_imm",
	Combo1_1 = "combo1_1",
	Combo1_2 = "combo1_2",
	Combo1_3 = "combo1_3",
	CaiJi = "caiji",
	CaiJi2 = "caiji2",
	CaiJi2_Back = "caiji2_back",
	SwimCiji = "swim_caiji",
	Fish = "fish",
	ChongFeng = "chongfeng",
	Jump = "jump",
	Jump2 = "jump2",
	Jump3 = "jump3",
    Jump4 = "jump4",
    ShanhaiJump = "shanhai_jump",
	D_Jump = "jump",
	Kun_Idle = "kun_chengqi",
	Fly_Run = "fly_run",
	Fly_Idle = "fly_idle",
	Mount_Fight = "mount_fight",
	Fight = "fight",
	Rest = "rest",
	FallRest = "fall_rest",
    Action = "action",
    Mount_Jiehun = "huajiao_site",
	Idle_YouYong = "swim_idle",
	Run_YouYong = "swim_run",
	Run_WenQuan = "run_wq",
	TouZhi = "touzi",
	TouZhi2 = "touzi2",  --在水中
	SHUANG_XIU = "shuangxiu",
	SHUANG_XIU2 = "shuangxiu2",
	D_Rest = "rest",
	D_Attack1 = "attack1",
	D_Attack2 = "attack2",
	D_Attack3 = "attack3",
	D_Attack4 = "attack4",
	D_Combo1_1 = "combo1_1",
	Dizzy = "dizzy",
	Dizzy2 = "dizzy2",
	ChuChang = "chuchang",
	beipo_wq = "beipo_wq", --被泼
	by_massage = "by_massage", --被按摩
	massage = "massage", --按摩
	Pass = "chuangong_c",	-- 传功
	Pass2 = "chuangong_j",	-- 传功
	Xun_Luo_Move = "special_run", 		-- 怪物追赶移动
	baolie = "chuchang",	-- 宠物蛋爆裂
	doudong_1 = "doudong_1", -- 宠物蛋抖动1
	doudong_2 = "doudong_2", -- 宠物蛋抖动2
	Ghost_Run = "ghost_run",
	Ghost_Idle = "ghost_idle",
	Sit_Idle = "sit_idle",
	Mount_Idle_Nq02 = "mount_idle_nq02",
	Swim_Rest = "swim_rest",
	WeaponIdel = "weapon_idle",

	Mount_Run = "mount_run_1",		--骑马动作
	Mount_Idle = "mount_idle_1",	--骑马动作
	Mount_Run_2 = "mount_run_2",	--盘腿动作 
	Mount_Idle_2 = "mount_idle_2",	--盘腿动作
	Mount_Run_3 = "mount_run_3",	--站立动作
	Mount_Idle_3 = "mount_idle_3",	--站立动作
	Mount_Run_4 = "mount_run_4",	--骑摩托车
	Mount_Idle_4 = "mount_idle_4",	--骑摩托车
	Mount_Run_5 = "mount_run_5",	--漫步动作
	Mount_Idle_5 = "mount_idle_5",	--漫步动作
	Mount_Run_6 = "mount_run_6",	--飞仙动作
	Mount_Idle_6 = "mount_idle_6",	--飞仙动作
	Mount_Idle_7 = "mount_idle_7",	--
	Mount_Run_7 = "mount_run_7",	--
	Mount_Idle_8 = "mount_idle_8",	--
	Mount_Run_8 = "mount_run_8",	--
	Mount_Idle_9 = "mount_idle_9",	--
	Mount_Run_9 = "mount_run_9",	--
	Mount_Idle_10 = "mount_idle_10",	--
	Mount_Run_10 = "mount_run_10",	--
	Mount_Idle_11 = "mount_idle_11",	--
	Mount_Run_11 = "mount_run_11",	--
	Mount_Idle_12 = "mount_idle_12",	--
	Mount_Run_12 = "mount_run_12",	--

	Hug_Run = "hug_run",			-- 抱着跑
	Hug_Throw = "hug_throw",		-- 扔
	Hug_Dead = "hug_dead",			-- 抱着进入死亡
	Hug_Die = "hug_die",			-- 抱着死亡待机
	Hug_Idle = "hug_idle", 		-- 抱着待机

	Moster_Rest_Show = "Rest2",
	Front_Begin = "front_begin",	-- 技能前置

	Moster_XuRuo = "xuruo",    	--Boss虚弱
	RiderSwordUp = "rider_sword_up", 	-- 御剑
	Sword_Idle = "yu_jian",				--御剑飞行

	Walk = "walk",
	transformation = "transformation",			-- 变身
	Prizedraw = "prizedraw",					--角色抽奖
	RoleDiyAppearance = "role_diy_appearance",  -- 捏脸界面
	
	ChouJiang_Click = "choujiang_click",       	-- 点亮所有条件渡劫从打坐变为渡劫Idle
	ChousJiangIdle = "choujiang_idle",          -- 点亮所有推条件渡劫前的Idle

	CustomizedHit5 = "customized_hit_5",		--下跪
	CustomizedHit6 = "customized_hit_6",		--暂定
	CustomizedHit7 = "customized_hit_7",		--暂定
	CustomizedHit8 = "customized_hit_8",		--暂定
	CustomizedHit9 = "customized_hit_9",		--暂定
	CustomizedHit10 = "customized_hit_10",		--暂定
}

-- Animator 参数
AnimatorParameters =
{
	RiderSwordIng = "rider_sword_ing",
	RiderSwordDown = "rider_sword_down",
	IsMitsurugi = "is_mitsurugi",
	MitsurugiRun = "mitsurugi_run",
	MitsurugiRest = "mitsurugi_rest",
	MitsurugiRush = "mitsurugi_rush",
	MitsurugiRushSpeed = "mitsurugi_rush_speed",
	MitsurugiDown = "mitsurugi_down",
	MitsurugiLand = "mitsurugi_land",
	MitsurugiStop = "mitsurugi_stop",
}

CaijiType = {
	Nor = 0,
	Stand = 1,
	Fish = 2,
	TaskForce = 3,
}

-- Animator动作状态
ActionStatus = {
	Idle = 0,										-- 站立(闲置)
	Run = 1,										-- 跑步(奔跑)
	Die = 2,										-- 死亡
	Sit = 3,
	Gather = 5,										-- 采集
	ChongFeng = 9,									-- 冲锋
	Hug = 20,										-- 抱美人站立
	HugRun = 21,									-- 抱美人跑步
	ShuaiGan = 24,									-- 甩杆
	ShangGou = 25,									-- 上钩
	ShouGan = 26,									-- 收杆

	Status1 = 28,									-- 形象1
	Status1Stop = 29,								-- 形象1停止状态
	Status2 = 30,									-- 形象2
	Status2Stop = 31,								-- 形象2停止状态
}

-- 动作所处的步骤
ActionStep = {
	None = 0,
	Start = 1,
	Keep = 2,
	ReadyToEnd = 3,
	End = 4,
}

-- 帧时长
FrameTime = {
	Stand = 0.5,
	Move = 0.11,
	Atk = 0.0857,
	Dead = 0.15,
	Jump = 0.05,
	Jump2 = 0.15,

	RoleStand = 0.16,

	MountStand = 0.16,
	JingLingStand = 0.25,
	TruckStand = 0.08,

	Effect = 0.08,
	Decoration = 0.15,
	Door = 0.1,
	ModuleEffect = 0.12,
	Fish = 0.4,
	Bubble = 0.2,
	FbAddEff = 0.2,
	RoleGuanghuan = 0.1,
	FaZhen = 0.2,
	Wing = 0.16,
	PetHalo = 0.12,
	RoleCreateEffect = 0.08,
	Dance = 0.1,
	JobSelectEffect = 0.15,
	Face = 0.25
}

SceneConvertionArea = {
	SAFE_TO_WAY = 0,				-- 从安全区移动到野外
	WAY_TO_SAFE = 1,				-- 从野外移动到安全区
}

SceneIgnoreStatus = {
	MAIN_ROLE_IN_SAFE = "main_role_in_safe",			-- 忽略主角在安全区中
	OTHER_IN_SAFE = "other_in_safe",					-- 忽略其他对象在安全区中
}

SceneTargetSelectType = {
	SCENE = "scene",
	TASK = "task",
	SELECT = "select"
}

-- 传送点类型
SceneDoorType = {
	NORMAL = 0,
	FUBEN = 1,
	TEAM_FUBEN = 10,
	INVISIBLE = 100,
}

MASK_LAYER = {
	WALKABLE = 8,
	INVISIBLE = 31
}


-- 默认显示采集物名称的场景
ShowGatherFollowList = {
	[SceneType.KFSHENYUN_FB] = true,
    [SceneType.KF_BOSS] = true,
    [SceneType.Shenyuan_boss] = true,
}

-- 强制显示称号的场景
ShowTitleFollowList = {
	[SceneType.XianMengzhan] = true,
	[SceneType.YEZHANWANGCHENGFUBEN] = true,
	[SceneType.ZhuXie] = true,
	[SceneType.KFZhuXieZhanChang] = true,
	[SceneType.Kf_PVP] = true,
}

--场景倒计时显示位置特殊处理
SHOW_LEVEL_TIME_TYPE =
{
	[SceneType.HIGH_TEAM_EQUIP_FB] = true,
	[SceneType.COPPER_FB] = true,
	[SceneType.PET_FB] = true,
	[SceneType.FakePetFb] = true,
	[SceneType.DefenseFb] = true,
	[SceneType.ZHUSHENTA_FB] = true,
	[SceneType.PERSON_BOSS] = true,
	[SceneType.GuildMiJingFB] = true,
	[SceneType.YEZHANWANGCHENGFUBEN] = true,
	[SceneType.TIAN_SHEN_FB] = true,
	[SceneType.XianMengzhan] = true,
	[SceneType.GUILD_ANSWER_FB] = true,
	[SceneType.Kf_OneVOne_Prepare] = true,
	[SceneType.Kf_PvP_Prepare] = true,
	[SceneType.HotSpring] = true,
	[SceneType.KF_HotSpring] = true,
	[SceneType.Guild_Invite] = true,
	[SceneType.FBCT_NEWPLAYERFB] = true,
	[SceneType.KF_DUCK_RACE] = true,
    [SceneType.TianShen3v3Prepare] = true,
    [SceneType.WorldsNO1] = true,
    [SceneType.WorldsNO1Prepare] = true,
    [SceneType.SCENE_TYPE_KF_COUNTRY_SECRET_AREA] = true,
	[SceneType.CROSS_PK_LOVER_READY] = true,
	[SceneType.BOSS_INVASION] = true,
	[SceneType.TEAM_COMMON_BOSS_FB_1] = true,
	[SceneType.TEAM_COMMON_BOSS_FB_2] = true,
	[SceneType.TEAM_COMMON_BOSS_FB_3] = true,
	[SceneType.TEAM_COMMON_TOWER_FB_1] = true,
}

--活动开始倒计时需要和铭文阁开始方式相同的
 SCENE_STARETIME_TYPE = {
 	[SceneType.ZhuXie] = true,
 	[SceneType.LUANDOU_BATTLE] = true,
 	[SceneType.YEZHANWANGCHENGFUBEN] = true,
 	[SceneType.Kf_Honorhalls] = true,
 	[SceneType.Kf_OneVOne] = true,
 	[SceneType.Kf_OneVOne_Prepare] = true,
}

MOUNT_RIDING_TYPE = {
	[1] = SceneObjAnimator.Mount_Idle,		--骑马
	[2] = SceneObjAnimator.Mount_Idle_2,	--盘腿
	[3] = SceneObjAnimator.Mount_Idle_3,	--站立
	[4] = SceneObjAnimator.Mount_Idle_4,	--骑摩托车
	[5] = SceneObjAnimator.Mount_Idle_5,	--漫步动作
	[6] = SceneObjAnimator.Mount_Idle_6,	--飞仙动作
	[7] = SceneObjAnimator.Mount_Idle_7,	--
	[8] = SceneObjAnimator.Mount_Idle_8,	--
	[9] = SceneObjAnimator.Mount_Idle_9,	--
	[10] = SceneObjAnimator.Mount_Idle_10,	--
	[11] = SceneObjAnimator.Mount_Idle_11,	--
	[12] = SceneObjAnimator.Mount_Idle_12,	--
}

MOUNT_RIDING_RUN_TYPE = {
	[1] = SceneObjAnimator.Mount_Run,		--骑马
	[2] = SceneObjAnimator.Mount_Run_2,		--站立
	[3] = SceneObjAnimator.Mount_Run_3,		--盘腿
	[4] = SceneObjAnimator.Mount_Run_4,		--骑摩托车
	[5] = SceneObjAnimator.Mount_Run_5,		--漫步动作
	[6] = SceneObjAnimator.Mount_Run_6,		--飞仙动作
	[7] = SceneObjAnimator.Mount_Run_7,		--
	[8] = SceneObjAnimator.Mount_Run_8,		--
	[9] = SceneObjAnimator.Mount_Run_9,		--
	[10] = SceneObjAnimator.Mount_Run_10,		--
	[11] = SceneObjAnimator.Mount_Run_11,		--
	[12] = SceneObjAnimator.Mount_Run_12,		--
}

-- 普通复活不需要挂机
REALIVE_NO_GUAJI_SCENE_TYPE = {
	[SceneType.HONG_MENG_SHEN_YU] = true,
	[SceneType.KF_BOSS] = true,
	[SceneType.SG_BOSS] = true,
	[SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS] = true,
	[SceneType.Shenyuan_boss] = true,
	[SceneType.WorldBoss] = true,
	[SceneType.VIP_BOSS] = true,
	[SceneType.DABAO_BOSS] = true,
}

ROLE_SKIN_TYPE = {
	BODY = 2,
	FACE = 3,
	HAIR = 4,
}

PART_ENUM_TYPE = {
	[ROLE_SKIN_TYPE.BODY] = 0,
	[ROLE_SKIN_TYPE.FACE] = 1,
	[ROLE_SKIN_TYPE.HAIR] = 2,
}

-- 
RenderingLayerMaskType = {
	LightLayerDefault = 0,
	Reflection = 1,						-- 反射
}

-- 需要水面反射的部位
WaterReflectionPartList = {
	[SceneObjPart.Main] = true,
	[SceneObjPart.Weapon] = true,
	[SceneObjPart.Mount] = true,
	[SceneObjPart.FightMount] = true,
	[SceneObjPart.Wing] = true,
	[SceneObjPart.Jianling] = true,
	[SceneObjPart.Halo] = true,
	[SceneObjPart.Waist] = true,
	[SceneObjPart.Mask] = true,
	[SceneObjPart.ShouHuan] = true,
	[SceneObjPart.Tail] = true,
	[SceneObjPart.BaoJu] = true,
}

SCENE_QUALITY_TYPE = {
	BLOOM = 1,							-- bloom
	WATER_PLANAR = 2,					-- 水面反射
	COLOR_GRADING = 3,					-- 颜色层级
	WAVE = 4,							-- 水波纹
	DEPTH = 5,							-- 景深
}

MotionBlurStage = {
	None = 0,
	Slight = 1,							-- 轻微
	SpeedUp = 2,						-- 加速
	Spurt = 3,							-- 冲刺
	SpeedCut = 4,						-- 减速
}

JUMP_POINT_TYPE = {												-- 跳跃点类型
	JUMP = 0,													-- 跳跃					
	AIRCRAFT = 1,												-- 飞行器
	RIDER_SWORD = 2												-- 御剑
}

--[[
	跳跃点 额外的参数设定
	[场景id] = {
		[目标跳跃点id] = {
			(跳跃、御剑)
			y = 模型移动状态额外需要上升的高度,
			ready_up_time = 模型上升开始的时间,
			up_time = 模型上升所需的时间,
			(御剑)
			rw_pre_move_distance = 御剑第一段移动距离
			rw_pre_move_speed = 御剑第一段移动速度（不能为0）(s)
			rw_pre_total_do_time = 御剑第一段执行的总时间 (s)
			rw_down_time = 御剑到目标点 提前收剑的时间（s）
			add_carrier_time = 载体出现的时间（s）(从一开始计算)
			(跳跃)
			jump_time = 跳跃时间,
		}
	}
]]
-- 御剑基础参数
RIDER_SWORD_BASE_PARAM = {
	y = 7,							-- 额外抬的高度
	up_time = 0.3,					-- 目前取第一段动画起跳到最高点所需要的时间
	rw_pre_total_do_time = 0.97,   	-- 目前第一段动画大概踩剑的时间，（整个动画目前是1.7）
	rw_pre_move_distance = 14,		-- 这个距离大概是从台子边缘起跳
	rw_pre_move_speed = 14.4,		-- 14 / 0.97 = 14.4  这个大概是移动到起跳点需要的时间 
	rw_down_time = 0.67,			-- 动画片段从起跳到落地需要的大概时间
	rw_keep_move_speed = 40,		-- 御剑第二段持续飞行的运动速度
}

-- 御剑特殊参数
RIDER_SWORD_SPECAIL_PARAM = {
	-- [场景id] = {
	-- 	[目标跳跃点id] = {}
	-- }
	-- [1002] = {
	-- 	[16] = {
	-- 		y = 8,
	-- 		ready_up_time = 0.3,
	-- 		up_time = 0.3,

	-- 		rw_pre_move_distance = 14,
	-- 		rw_pre_move_speed = 14.4,
	-- 		rw_pre_total_do_time = 0.97,
	-- 		rw_down_time = 0.67,
	-- 		add_carrier_time = 0.67,

	-- 	}
	-- },



}

-- 跳跃点普通跳跃特殊参数
JUMP_SPECAIL_PARAM = {
	-- [场景id] = {
	-- 	[目标跳跃点id] = {}
	-- }

	[1002] = {
		[17] = {
			y = 8,
			up_time = 0.9,
		},
		[18] = {
			y = 3,
			up_time = 0.9,
		},

		[20] = {
			y = 4,
			up_time = 0.8,
		},
		[21] = {
			y = 4,
			up_time = 0.9,
		},
	},

}

-- 角色御剑当前状态
SPRITE_MOVE_STATUS = {
	IDLE = 0,			-- 空状态
	MOVE = 1,			-- 移动中
	EXPEDITE = 2,		-- 加速中
	STOP = 3,			-- 停止移动
	SPRINT = 4,			-- 冲刺
}