OperationFirstRechargeWGData = OperationFirstRechargeWGData or BaseClass()
OperationFirstRechargeWGData.ConfigPath= "config/auto_new/operation_activity_day_first_recharge_auto"
function OperationFirstRechargeWGData:__init()
	if OperationFirstRechargeWGData.Instance then
		ErrorLog("[OperationFirstRechargeWGData] Attemp to create a singleton twice !")
	end
	OperationFirstRechargeWGData.Instance = self
	self:IntFirstRechargeData()
	OperationActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.OPERATION_FIRST_RECHARGE, {[1] = OPERATION_EVENT_TYPE.LEVEL}, 
    	BindTool.Bind(self.GetActCanOpen, self), BindTool.Bind(self.IsShowFirstRechargeRedPoint, self))
	RemindManager.Instance:Register(RemindName.OperationFirstRecharge, BindTool.Bind(self.IsShowFirstRechargeRedPoint, self))

end

function OperationFirstRechargeWGData:__delete()
	OperationFirstRechargeWGData.Instance = nil 
	RemindManager.Instance:UnRegister(RemindName.OperationFirstRecharge)
end

function OperationFirstRechargeWGData:IntFirstRechargeData()
	self.operation_day_first_recharge_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_day_first_recharge_auto")
	self.day_first_reward_cfg = self.operation_day_first_recharge_cfg.reward
	self.day_first_interface_cfg = self.operation_day_first_recharge_cfg.interface
	self.day_first_param_cfg = self.operation_day_first_recharge_cfg.config_param
end

function OperationFirstRechargeWGData:SetFirstRechargeData(protocol)
	self.first_gift_state = protocol.gift_state                    --当前可领取状态
	self.open_serverday = protocol.open_serverday                    --当前活动开启时服务器开服天数
end


function OperationFirstRechargeWGData:GetFirstRechargeRewardCfg()
	return self.day_first_reward_cfg
end

function OperationFirstRechargeWGData:GetFirstRechargeState()
	return self.first_gift_state
end

function OperationFirstRechargeWGData:GetFirstRechargeopenServerDay()
	local open_day = OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.OPERATION_FIRST_RECHARGE)
	return open_day
end


--根据服务器开服天数获得活动配置grade
function OperationFirstRechargeWGData:GetGradeByServerDay()
    local open_day = self:GetFirstRechargeopenServerDay()
    local open_time = OperationActivityWGData.Instance:GetOpenTimeByAct(ACTIVITY_TYPE.OPERATION_FIRST_RECHARGE)
    local week = TimeUtil.FormatSecond3MYHM1(open_time)--获取是周几
	for k,v in pairs(self.day_first_param_cfg) do
		if open_day >= v.start_server_day and open_day < v.end_server_day and week == v.week_index then
			return v.grade
		end
	end
	return 0
end

-- 获取每日首充奖励配置 根据活动配置grade和开始天数 去获取奖励
function OperationFirstRechargeWGData:GetShouChongRewardCfgByDay(day)
	local cfg = self:GetFirstRechargeRewardCfg()
	local grade = self:GetGradeByServerDay()
	for k,v in pairs(cfg) do
		if day == v.day_index and v.grade == grade then
			return v
		end
	end
	return nil
end

--根据服务器开服天数获得界面配置
function OperationFirstRechargeWGData:GetInterfaceByServerDay()
    local open_day = self:GetFirstRechargeopenServerDay()
    local open_time = OperationActivityWGData.Instance:GetOpenTimeByAct(ACTIVITY_TYPE.OPERATION_FIRST_RECHARGE)
    local week = TimeUtil.FormatSecond3MYHM1(open_time)--获取是周几
	for k,v in pairs(self.day_first_param_cfg) do
		if open_day >= v.start_server_day and open_day < v.end_server_day and week == v.week_index then
			return v.interface
		end
	end
	return -1
end
--根据界面配置获取界面信息
function OperationFirstRechargeWGData:GetShouChongViewCfgByInterface()
	local interface = self:GetInterfaceByServerDay()
	for k,v in pairs(self.day_first_interface_cfg) do
		if v.interface == interface then
			return v
		end
	end
	return nil
end

--获取活动已开启的天数
function OperationFirstRechargeWGData:GetFirstDayRechargeOpenDay()
	local open_time = OperationActivityWGData.Instance:GetActOpenDay(ACTIVITY_TYPE.OPERATION_FIRST_RECHARGE)
	return open_time
end

function OperationFirstRechargeWGData:IsShowFirstRechargeRedPoint()
	local state = OperationActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.OPERATION_FIRST_RECHARGE)
	if not state then
		return 0
	end

	if not self:GetActCanOpen() then
		return 0
	end
	if self.first_gift_state == OPERATION_ACTIVITY_FIRST_RECHARGE.KLQ then
		return 1 
	end
	return 0
end


--根据服务器开服天数获得活动开启等级
function OperationFirstRechargeWGData:GetOpenLevelByServerDay()
	local open_day = self:GetFirstRechargeopenServerDay()
	if open_day ~= -1 then
		for k,v in pairs(self.day_first_param_cfg) do
			if open_day >= v.start_server_day and open_day <= v.end_server_day then
				return v.open_level
			end
		end
		return 0
	end
end

function OperationFirstRechargeWGData:GetActCanOpen()
	local vo = GameVoManager.Instance:GetMainRoleVo()
	local open_level = self:GetOpenLevelByServerDay()
	if open_level then
		if vo.level >= open_level then
			return true
		end
		return false
	end
end

