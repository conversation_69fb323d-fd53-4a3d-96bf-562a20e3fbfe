RemindWGData = RemindWGData or BaseClass()

RemindMaterialType = {
	FABAO = 1,
	YUYI = 2,
	SHENBING = 3,
	JIANLING = 4,
	TIANSHEN = 5,
	PET = 6,
	MOUNT = 7,
	MOUNT_HUANZHUANG = 8, -- 坐骑幻装
}

RemindWGData.RemindFlagType = {
	Type_1 = 1, -- 玩家没有材料
	Type_2 = 2, -- 玩家拥有的材料不足以升1级
	Type_3 = 3, -- 玩家拥有的材料可以升1级但数量少于5
	Type_4 = 4, -- 玩家拥有的材料可以升1级但数量大于5
	Type_5 = 5, -- 玩家拥有的材料可以升1级
}

RemindWGData.RemindSkillFlagType = {
	Type_1 = 1, -- 玩家不够红点显示条件
	Type_2 = 2, -- 玩家达到红点显示条件
}

function RemindWGData:__init()
	if RemindWGData.Instance then
		error("[RemindWGData] Attempt to create singleton twice!")
		return
	end
	RemindWGData.Instance = self

	self.red_point_logical_auto = ConfigManager.Instance:GetAutoConfig("red_point_logical_auto")
	-- 红点初始材料升级逻辑
	self.red_material_cfg = ListToMap(self.red_point_logical_auto.material, "type", "order")
	self.red_point_skill_level_auto = self.red_point_logical_auto.skill_level
end

function RemindWGData:__delete()
	RemindWGData.Instance = nil
end

--返回最初级的材料
function RemindWGData:GetPrimaryUpCfg(m_type)
	if self.red_material_cfg and self.red_material_cfg[m_type] then
		return self.red_material_cfg[m_type][1]
	end
	return nil
end

--返回该类型所有升级材料
function RemindWGData:GetPrimaryUpCfgList(m_type)
	if self.red_material_cfg then
		return self.red_material_cfg[m_type]
	end
	return nil
end

function RemindWGData:GetSkillUpCfg()
	local level = RoleWGData.Instance:GetAttr("level")

	for i,v in ipairs(self.red_point_skill_level_auto) do
		if level >= v.section_down and level <= v.section_top then
			return v
		end
	end
end
