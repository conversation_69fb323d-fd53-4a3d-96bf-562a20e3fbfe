SuoYaoTaSceneLogic = SuoYaoTaSceneLogic or BaseClass(CommonFbLogic)

function SuoYaoTaSceneLogic:__init()
	
end

function SuoYaoTaSceneLogic:__delete()
	
end

function SuoYaoTaSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	
	-- XuiBaseView.CloseAllView()

	FuBenWGCtrl.Instance:OpenTaskFollow()
	FuBenWGCtrl.Instance:SetLevaeFbContent(Language.Activity.OutFbTips)
	self:SetGuaiJi(GUAI_JI_TYPE.MONSTER)
end

function SuoYaoTaSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
	local status_t = ActivityWGData.Instance:GetDailyActStatus(Day_Act_Type.SuoYaoTa)
	if status_t and status_t.status == Daily_Fb_Status.Standby then
		FuBenWGCtrl.Instance:UpdataTaskFollow()
	end
end

function SuoYaoTaSceneLogic:OpenFbSceneCd()
	local data = ActivityWGData.Instance:GetDailyActStatus(Day_Act_Type.SuoYaoTa)
	local out_time = data and data.next_stop_time or 0
	if out_time > TimeWGCtrl.Instance:GetServerTime() then
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(out_time)
	end
end

-- 角色是否是敌人
function SuoYaoTaSceneLogic:IsRoleEnemy(target_obj, main_role)
	if nil == target_obj or target_obj:GetType() == SceneObjType.Role then
		return false
	end
	return true
end

-- 是否是挂机打怪的敌人
function SuoYaoTaSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:GetType() == SceneObjType.Role then
		return false
	end
	return true
end

function SuoYaoTaSceneLogic:Out()
	CommonFbLogic.Out(self)
	FuBenWGCtrl.Instance:CloseTaskFollow()
	ActivityWGData.Instance:InitSuoYaoTaInfo()
	FuBenWGCtrl.Instance:SetLevaeFbContent(Language.Dungeon.ConfirmLevelFB)
end