--寻缘发布
MarryXunYuanFaBuView = MarryXunYuanFaBuView or BaseClass(SafeBaseView)

function MarryXunYuanFaBuView:__init()
	self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_xunyuan_fabu")
    self.operate_type = 0
    self.content_info = nil
    self.is_first_fabu = false
    self.is_first_modify = false
end

function MarryXunYuanFaBuView:__delete()

end

function MarryXunYuanFaBuView:ReleaseCallBack()
    self.operate_type = 0

    if self.play_anim then
		self.play_anim:Kill()
		self.play_anim = nil
	end
end

function MarryXunYuanFaBuView:CloseCallBack()

end

function MarryXunYuanFaBuView:LoadCallBack()
    self.old_default_text_index = 0
    XUI.AddClickEventListener(self.node_list.btn_fabu, BindTool.Bind(self.OnClickFaBu, self))
    XUI.AddClickEventListener(self.node_list["btn_change_content"], BindTool.Bind(self.OnClickChangeContent, self))
end

function MarryXunYuanFaBuView:ShowIndexCallBack()
	self.node_list["root"].transform.anchoredPosition = Vector2(0, 0)
	self.node_list["root"].transform.localScale = Vector3(1, 1, 1)
end

function MarryXunYuanFaBuView:OnFlush()
    if self.operate_type == XUNYUAN_OPERATE_TYPE.MODIFY then
        self.node_list.content.input_field.text = self.content_info
        if self.is_first_modify then
            self.node_list.title.text.text = Language.Marry.ModifyContent
            self.node_list.btn_fabu_text.text.text = Language.Marry.ModifyOperate
            self.is_first_modify = false
        end
    elseif self.is_first_fabu then
        self.node_list.title.text.text = Language.Marry.FaBuContent
        self.node_list.btn_fabu_text.text.text = Language.Marry.FaBuOperate
        --self.node_list.content.input_field.text = self.content_info
        local xunyuan_random_cfg = MarryWGData.Instance:GetXunyuanRandomCfg()
        if not IsEmptyTable(xunyuan_random_cfg) then
            local default_text_index = math.random(1, #xunyuan_random_cfg)
            self.old_default_text_index = default_text_index
            self.node_list.content.input_field.text = xunyuan_random_cfg[default_text_index].conect
        end

        self.is_first_fabu = false
    end
end

function MarryXunYuanFaBuView:SetXunYuanModifyInfo(zhenghun_info)
    if self.operate_type ~= XUNYUAN_OPERATE_TYPE.MODIFY then
        self.operate_type = XUNYUAN_OPERATE_TYPE.MODIFY
        self.is_first_modify = true
    end
    self.content_info = zhenghun_info
end

function MarryXunYuanFaBuView:SetXunYuanFaBuInfo()
    if self.operate_type ~= XUNYUAN_OPERATE_TYPE.RELEASE then
        self.content_info = ""
        self.is_first_fabu = true
        self.operate_type = XUNYUAN_OPERATE_TYPE.RELEASE
    end
end

function MarryXunYuanFaBuView:OnClickFaBu()
	local content = self.node_list.content.input_field.text

    if content == "" then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.HaveNotContent)
		return
	end

	if ChatFilter.Instance:IsIllegal(content, false) then
        --有非法字符直接不让发
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.IllegalContent)
		return
    end

    if self.operate_type == XUNYUAN_OPERATE_TYPE.MODIFY then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.ModifySuccess)
    end

    local select_sex = MarryWGData.Instance:GetXunYuanSelectSex()
    MarryWGCtrl.Instance:SendXunYuanOperateReq(self.operate_type, select_sex, 0, string.len(content), content)

    self:PlayAnimPos()
end

function MarryXunYuanFaBuView:OnClickChangeContent()
    local xunyuan_random_cfg = MarryWGData.Instance:GetXunyuanRandomCfg()
    if not IsEmptyTable(xunyuan_random_cfg) then
        local max_len = #xunyuan_random_cfg
        local default_text_index = math.random(1, #xunyuan_random_cfg)
        if self.old_default_text_index == default_text_index then
            default_text_index = default_text_index < max_len and default_text_index + 1 or 1
        end
        self.old_default_text_index = default_text_index
        self.node_list.content.input_field.text = xunyuan_random_cfg[default_text_index].conect
    end
end

function MarryXunYuanFaBuView:PlayAnimPos()
    if self.play_anim then
		self.play_anim:Kill()
		self.play_anim = nil
	end

    self.play_anim = DG.Tweening.DOTween.Sequence()
    self.play_anim:Append(self.node_list.root.transform:DOScale(Vector3(0, 0, 0), 1):SetEase(DG.Tweening.Ease.InOutExpo))
	self.play_anim:Join(self.node_list.root.rect:DOAnchorPos(u3dpool.vec3(-288, 125, 0), 1):SetEase(DG.Tweening.Ease.InOutExpo))
    self.play_anim:OnComplete(function ()
	    self:Close()
	end)
end
