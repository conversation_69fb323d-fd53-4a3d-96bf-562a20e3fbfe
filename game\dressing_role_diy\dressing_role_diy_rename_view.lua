DressingRoleDiyRenameView = DressingRoleDiyRenameView or BaseClass(SafeBaseView)

function DressingRoleDiyRenameView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(600, 424)})
	self:AddViewResource(0, "uis/view/dressing_role_diy_ui_prefab", "layout_dressing_diy_rename")
end

function DressingRoleDiyRenameView:SetShowDataAndOpen(data)
	if data then
        self.show_data = data
        self:Open()
	end
end

function DressingRoleDiyRenameView:ReleaseCallBack()
    self.show_data = nil
end

function DressingRoleDiyRenameView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.DressingRoleDiyView.ChangeName

	self.node_list.name_input.input_field.onValueChanged:AddListener(BindTool.Bind(self.OnInputChange, self))

    XUI.AddClickEventListener(self.node_list["btn_rename_confirm"], BindTool.Bind1(self.OnConfirmRenameHandler, self))
	XUI.AddClickEventListener(self.node_list["btn_rename_cancel"], BindTool.Bind1(self.OnCancelRenameHandler, self))

    self.node_list.name_input.input_field.text = ""
end

function DressingRoleDiyRenameView:ShowIndexCallBack()

end

function DressingRoleDiyRenameView:OnFlush()
    if not self.show_data then
        return
    end

    local name_str = self.show_data.project_name ~= "" and self.show_data.project_name or 
    string.format(Language.DressingRoleDiyView.DiyName[0], NumberToChinaNumber(self.show_data.project_id + 1))
    self.node_list.cur_name.text.text = name_str
end

function DressingRoleDiyRenameView:OnInputChange()
	local change_name = self.node_list.name_input.input_field.text
	if change_name == "" then
		return
	end

	if ChatFilter.IsEmoji(change_name) then
		self.node_list.name_input.input_field.text = ""
		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.IllegalContent)
	end
end

-- 取消改名申请
function DressingRoleDiyRenameView:OnCancelRenameHandler()
    self:Close()
end

-- 发送改名申请
function DressingRoleDiyRenameView:OnConfirmRenameHandler()
	if not self.show_data then
        return
    end

    local text = self.node_list.name_input.input_field.text

    if text == "" then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Login.EditName)
		return
	end

    local name_length_ch = CheckCharactersHaveCh(text)
	local name_length_ev = CheckCharactersHaveBigEV(text)
    --一个汉字长度是3,但是策划要把它当成2来处理
    --一个大写英文长度是1,但是策划要把它当成2来处理
	if string.len(text) + name_length_ev - name_length_ch > 12 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.DressingRoleDiyView.limitContent)
		return
	end

    if ChatFilter.Instance:IsIllegal(text, true) or ChatFilter.IsEmoji(text) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.IllegalContent)
        return
    end

    local i, j = string.find(text, "*")
    if i ~= nil and j ~= nil then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.IllegalContent)
        return
    end

    local qukong_text = string.gsub(text, "%s", "")
    local qukong_text_len = string.len(qukong_text)
    local len = string.len(text)
    --判断输入的名字是否带空格
    if qukong_text_len ~= len then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.IllegalContent)
        return
    end
    
    local main_vo = GameVoManager.Instance:GetMainRoleVo()
	local sex = main_vo.sex
	local prof = main_vo.prof % 10

    DressingRoleDiyWGCtrl.Instance:SendCSDiyAppearanceProjectSetName(self.show_data.project_id, sex, prof, text)
    self:Close()
end