-----------------------------------灵兽背包格子信息-----------------------------------
BeastsBagItem = BeastsBagItem or BaseClass(BaseRender)

function BeastsBagItem:LoadCallBack()
    if not self.beast_item then
		self.beast_item = ItemCell.New(self.node_list.item_pos)
        self.beast_item:SetIsShowTips(false)
	end
end

function BeastsBagItem:__delete()
	if self.beast_item then
		self.beast_item:DeleteMe()
		self.beast_item = nil
	end
end

function BeastsBagItem:OnFlush()
    if (not self.data) or (not self.data.server_data) then 
        return 
    end

    self.beast_item:SetData(self.data)
    local server_data = self.data.server_data
    if not self.data.is_egg and server_data then
        self.beast_item:SetBeastBattleTypeIcon(server_data.stand_by_slot)
		local flair_score, score_index = ControlBeastsWGData.Instance:GetFlairScoreByServerData(server_data)
		self.beast_item:SetBeastFlairScoreIcon(score_index)
    end
    
	self.beast_item:SetRightTopImageText(server_data.beast_level)
end
----------------------------------灵兽孔位条件item-----------------------
BeststsHoleConditionRender = BeststsHoleConditionRender or BaseClass(BaseRender)

function BeststsHoleConditionRender:OnFlush()
    if not self.data then return end

    self.node_list.select:CustomSetActive(self.data.is_enough)
    self.node_list.lock:CustomSetActive(not self.data.is_enough)
    self.node_list.desc.text.text = self.data.desc
end


---------------------------------灵兽辅战出战位item-----------------------
BeststsItemRender = BeststsItemRender or BaseClass(BaseRender)
function BeststsItemRender:LoadCallBack()
    if not self.beast_item and self.node_list.item_pos then
		self.beast_item = ItemCell.New(self.node_list.item_pos)
        self.beast_item:SetIsShowTips(false)
        self.beast_item:SetClickCallBack(BindTool.Bind(self.OnClick, self))
	end
end

function BeststsItemRender:OnClick()
    BaseRender.OnClick(self)
end

function BeststsItemRender:__delete()
	if self.beast_item then
		self.beast_item:DeleteMe()
		self.beast_item = nil
	end

    self.old_beasts_bag_id = nil
end

function BeststsItemRender:OnFlush()
    if not self.data then return end

    self.node_list.remind:CustomSetActive(self.data.red)
    self.node_list.add_btn:CustomSetActive(self.data.state == BEASTS_HOLE_STATUS.ACTIVE and self.data.beasts_bag_id == -1)

    if self.node_list.item_pos then
        self.node_list.item_pos:CustomSetActive(self.data.state == BEASTS_HOLE_STATUS.ACTIVE and self.data.beasts_bag_id ~= -1)
    end

    if self.node_list.lock then
        self.node_list.lock:CustomSetActive(self.data.state == BEASTS_HOLE_STATUS.NOT_ACTIVE)
    end

    if self.node_list.lock_condition_text then
        self.node_list.lock_condition_text:CustomSetActive(self.data.state == BEASTS_HOLE_STATUS.NOT_ACTIVE)
    end

    -- 存在上阵，展示
    if self.data.beasts_bag_id ~= -1 then
        local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(self.data.beasts_bag_id)
        if beast_data and beast_data.server_data and self.beast_item then
            self.beast_item:SetData({item_id = beast_data.server_data.beast_id, is_beast = true})---这里需要其他的东西在加，看策划需求
            self.beast_item:SetRightTopImageText(beast_data.server_data.beast_level)
        end

        if self.old_beasts_bag_id and self.old_beasts_bag_id ~= self.data.beasts_bag_id then
            if self.node_list.item_pos then
                -- local bundle_name, asset_name = ResPath.GetA2Effect("UI_yushou_chuzhan1")
                -- EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.item_pos.transform,
                --                     nil, Vector3(0, 0, 0), Quaternion.Euler(0, 0, 0))
            end
        end
    else
        if self.node_list.lock_condition_text then
            -- 展示解锁条件
            local is_can_unlock, hole_data = ControlBeastsWGData.Instance:GetHoleStatusData(self.data.hole_id)
            if is_can_unlock then
                self.node_list.lock_condition_text.text.text = ToColorStr(Language.ContralBeasts.HoleTips19, COLOR3B.D_GREEN)
            else
                if hole_data then
                    local num_color = is_can_unlock and COLOR3B.D_GREEN or COLOR3B.RED
                    local new_str = string.format(hole_data.str, ToColorStr(hole_data.now, num_color), hole_data.aim)
                    self.node_list.lock_condition_text.text.text = new_str
                else
                    self.node_list.lock_condition_text.text.text = ""
                end
            end
        end
    end

    self.old_beasts_bag_id = self.data.beasts_bag_id
end

function BeststsItemRender:SetNotNeedRed()
    self.node_list.remind:CustomSetActive(false)
end

------------------------------------------------------------------------
----------------------------------灵兽已孵化培养item-----------------------
BeststsCultureItemRender = BeststsCultureItemRender or BaseClass(BaseRender)
function BeststsCultureItemRender:LoadCallBack()
    if not self.beast_item then
		self.beast_item = ItemCell.New(self.node_list.item_pos)
        self.beast_item:SetIsShowTips(true)
	end

    if self.fs_star_list == nil then
        self.fs_star_list = {}
        for i = 1, 5 do
            self.fs_star_list[i] = self.node_list["star" .. i]
        end
    end
end

function BeststsCultureItemRender:__delete()
	if self.beast_item then
		self.beast_item:DeleteMe()
		self.beast_item = nil
	end

    self.fs_star_list = nil
end

function BeststsCultureItemRender:OnFlush()
    if not self.data then return end

    if self.node_list.remind then
        self.node_list.remind:CustomSetActive((not self.data.is_preview) and (self.data.is_can_upgrade or self.data.is_can_learn_skill))
    end

	if IsEmptyTable(self.data) or self.data.is_preview then
        if self.node_list.show_root then
            self.node_list.show_root:SetActive(false)
        end
        if self.node_list.add_btn then
            self.node_list.add_btn:SetActive(true)
        end
		return
    else
        if self.node_list.show_root then
            self.node_list.show_root:SetActive(true)
        end
        if self.node_list.add_btn then
            self.node_list.add_btn:SetActive(false)
        end
	end

    if self.data.server_data then
        local server_data = self.data.server_data
        self.beast_item:SetData({item_id = server_data.beast_id, is_beast = true})---这里需要其他的东西在加，看策划需求
        self.beast_item:SetRightTopImageText(server_data.beast_level)
        self.beast_item:SetBeastBattleTypeIcon(server_data.stand_by_slot)
		local flair_score, score_index = ControlBeastsWGData.Instance:GetFlairScoreByServerData(server_data)
        self.beast_item:SetBeastFlairScoreIcon(score_index)
    end
end

function BeststsCultureItemRender:OnSelectChange(is_select)
    if self.beast_item then
        self.beast_item:SetSelectEffect(is_select)
    end
end
----------------------------------灵兽孵化池item-----------------------
BeststsIncubateRender = BeststsIncubateRender or BaseClass(BaseRender)
function BeststsIncubateRender:__init(instance, parent)
    self.parent_view = parent
    self.model_res_id = nil
    BaseRender.__init(self, instance)
end

function BeststsIncubateRender:SetViewCenterRoot(center_root)
    self.center_root = center_root
end

function BeststsIncubateRender:LoadCallBack()
	----[[模型展示
    if self.show_model == nil then
		self.show_model = RoleModel.New()
        local display_data = {
			parent_node = self.node_list["model_pos"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.S,
			can_drag = true,
		}

		self.show_model:SetRenderTexUI3DModel(display_data)
		-- self.show_model:SetUI3DModel(self.node_list["model_pos"].transform, self.node_list["model_pos"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
	end

    XUI.AddClickEventListener(self.node_list.item_button, BindTool.Bind2(self.OnSelectIncubateCB, self))
end

function BeststsIncubateRender:__delete()
    local incubate_time_down = string.format("incubate_time%d", self.index)
    self:CleanTimeDown(incubate_time_down)
    self.parent_view = nil

    if self.show_model then
        self.show_model:DeleteMe()
        self.show_model = nil
    end

    self.model_res_id = nil
    self.center_root = nil
end

function BeststsIncubateRender:OnSelectIncubateCB()
	if not self.data then
		return
	end
    if self.parent_view then
        self.parent_view:OnSelectIncubateCB(self, false, true)
    end
end

function BeststsIncubateRender:OnGoToControlBeastsContractView()
    ControlBeastsWGCtrl.Instance:ChangeToIndex(TabIndex.beasts_contract)
end

function BeststsIncubateRender:OnFlush()
	if not self.data then
		return
	end

    local is_none = self.data.status == BEASTS_BORN_STATUS.NONE or self.data.status == BEASTS_BORN_STATUS.CAN_BORN
    self.node_list.born_root:CustomSetActive(self.data.status == BEASTS_BORN_STATUS.BORN)
    self.node_list.un_born_root:CustomSetActive(self.data.status ~= BEASTS_BORN_STATUS.BORN)
    self.node_list.incubate_text:CustomSetActive(false)
    self.node_list.item_icon:CustomSetActive(self.data.status == BEASTS_BORN_STATUS.UNBORN)
    self.node_list.item_add:CustomSetActive(is_none)

    local incubate_time_down = string.format("incubate_time%d", self.index)
    self:CleanTimeDown(incubate_time_down)

    -- 未孵化
    if self.data.status ~= BEASTS_BORN_STATUS.BORN then
        -- 正在孵化
        if self.data.status == BEASTS_BORN_STATUS.UNBORN then
            self:FlushCountDownTime()
            -- 刷新物品图标
            if self.data.server_data == nil then
                return
            end
            local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.data.server_data.beast_id)
            if beast_cfg and beast_cfg.chang_head and beast_cfg.chang_head > 0 then
                self.node_list.item_icon.image:LoadSpriteAsync(ResPath.GetNoPackPNG(beast_cfg.chang_head))
            end
        end
    else-- 已孵化
        self:FlushBeastsModel()
    end

    self.node_list.un_born_remind:CustomSetActive(self.data.status == BEASTS_BORN_STATUS.CAN_BORN or 
                                                    (self.data.status == BEASTS_BORN_STATUS.UNBORN and self.data.can_quick))
    self.node_list.born_remind:CustomSetActive(self.data.status == BEASTS_BORN_STATUS.BORN)
end

-- 刷新模型
function BeststsIncubateRender:FlushBeastsModel()
    if not self.data then
		return
	end

    if self.data.server_data == nil then
		return
	end

    if self.data.server_data.beast_id > 0 then
        local res_id = ControlBeastsWGData.Instance:GetBeastModelResId(self.data.server_data.beast_id, self.data.server_data.use_skin)

        if self.model_res_id ~= res_id then
            self.model_res_id = res_id
    
            local bundle, asset = ResPath.GetBeastsModel(res_id)
            self.show_model:SetMainAsset(bundle, asset)
            self.show_model:FixToOrthographic(self.center_root)
            self.show_model:PlayRoleAction(SceneObjAnimator.Rest)
        end
    end
end

-- 刷新倒计时
function BeststsIncubateRender:FlushCountDownTime()
	if not self.data then
		return
	end

    if self.data.server_data then
        local server_data = self.data.server_data
        local cur_time_stamp = TimeWGCtrl.Instance:GetServerTime()
        local last_time = server_data.finish_timestamp - cur_time_stamp
        self.node_list.incubate_text:CustomSetActive(last_time > 0)
        local incubate_time_down = self:GetTimeDownStr()

        if last_time > 0 then   --已经孵化完成
            self:FlushStatusAndTime(0, last_time)
            CountDownManager.Instance:AddCountDown(incubate_time_down, 
            BindTool.Bind(self.FlushStatusAndTime, self), 
            BindTool.Bind(self.FlushStatusComplete, self), 
            nil, last_time, 1)
        end
    end
end

function BeststsIncubateRender:GetTimeDownStr()
    return string.format("incubate_time%d", self.index)
end

function BeststsIncubateRender:CleanTimeDown(time_down_str)
    if CountDownManager.Instance:HasCountDown(time_down_str) then
        CountDownManager.Instance:RemoveCountDown(time_down_str)
    end
end

---刷新倒计时
function BeststsIncubateRender:FlushStatusAndTime(elapse_time, total_time)
    if total_time - elapse_time > 0 then
        if self.node_list.incubate_text ~= nil then
            self.node_list.incubate_text.text.text = string.format(Language.ContralBeasts.Incubate18, TimeUtil.FormatSecond(total_time - elapse_time)) 
        end
    end
end

---刷新倒计时
function BeststsIncubateRender:FlushStatusComplete()
    local incubate_time_down = self:GetTimeDownStr()
    self:CleanTimeDown(incubate_time_down)
    ControlBeastsWGData.Instance:SetSingleBreedingInfoForSelf(self.data.bag_id)
    ViewManager.Instance:FlushView(GuideModuleName.ControlBeastsView, TabIndex.beasts_stable, "all")
    RemindManager.Instance:Fire(RemindName.BeastsStable)
end

-- 刷新选中状态
function BeststsIncubateRender:FlushSelectHl(is_select)
    self.node_list.born_status_hl:CustomSetActive(is_select)
    self.node_list.un_born_status_hl:CustomSetActive(is_select)
    -- self.node_list.born_status:CustomSetActive(not is_select)
    self.node_list.un_born_status:CustomSetActive(not is_select)
end

----------------------------------灵兽资质item-----------------------
BeststBagFlairItemRender = BeststBagFlairItemRender or BaseClass(BaseRender)
function BeststBagFlairItemRender:OnFlush()
    if not self.data then
        return
    end

    -- 获取资质加成属性数据
    local Flair_data = ControlBeastsWGData.Instance:GetFlairAttrDataById(self.index - 1)
    if Flair_data then
        self.node_list.attr_name.text.text = Flair_data.flair_name
    else
        self.node_list.attr_name.text.text = Language.ContralBeasts.SelectError4
    end

    if type(self.data) == "number" then 
        local integer_value, decimal_value = math.modf(self.data)
        local real_value = self.data
        if decimal_value > 0 then
            real_value = string.format("%.3f", self.data) 
        end
        self.node_list.attr_value.text.text = real_value

        if self.node_list.arrow then
            self.node_list.arrow:CustomSetActive(false)
        end
  
        if self.node_list.add_value then
            self.node_list.add_value:CustomSetActive(false)
        end
    else
        if self.data.preview_data then
            local proportion_num = 1
            local proportion_value = 0
            local add_value_str = ""
            local preview_data = self.data.preview_data

            if self.data.flair_value then
                proportion_value = self.data.flair_value / preview_data.max

                if proportion_value > 0.3 and proportion_value < 0.7 then
                    proportion_num = 2
                elseif proportion_value > 0.7 then
                    proportion_num = 5 
                end

                add_value_str = tostring(self.data.flair_value)
            else
                add_value_str = string.format("%s-%s", preview_data.min, preview_data.max)
            end

            self.node_list.attr_value.text.text = ""
            if self.node_list.arrow then
                self.node_list.arrow:CustomSetActive(true)
            end
      
            if self.node_list.add_value then
                self.node_list.add_value:CustomSetActive(true)
            end
            self.node_list.add_value.text.text = add_value_str
            local fill_str = string.format("a3_hs_jd_%d", proportion_num)

            if self.node_list.attr_progress then
                self.node_list.fill_image.image:LoadSprite(ResPath.GetControlBeastsImg(fill_str))
                self.node_list.attr_progress.slider.value = proportion_value
            end
        else
            self.node_list.attr_value.text.text = self.data.min 
            if self.node_list.arrow then
                self.node_list.arrow:CustomSetActive(true)
            end
      
            if self.node_list.add_value then
                self.node_list.add_value:CustomSetActive(true)
            end
            self.node_list.add_value.text.text = self.data.max
        end
    end

    if self.node_list.arrow_down then
        self.node_list.arrow_down:CustomSetActive(self.data.cur_value < self.data.compare_value)
    end

    if self.node_list.arrow_up then
        self.node_list.arrow_up:CustomSetActive(self.data.cur_value > self.data.compare_value)
    end
end

----------------------------------灵兽星阵item-----------------------
BeststsRefiningItemRender = BeststsRefiningItemRender or BaseClass(BaseRender)

function BeststsRefiningItemRender:OnFlush()
	if not self.data then
		return
	end

    self.node_list.normal:CustomSetActive(self.data.is_unlock ~= 1)
    self.node_list.lock:CustomSetActive(self.data.is_unlock ~= 1)
    self.node_list.normal_unlock:CustomSetActive(self.data.is_unlock == 1)
    self.node_list.cur_refining_root:CustomSetActive(true)

    if self.data.is_unlock == 1 then    --未解锁
        --local interval_times = NumberToChinaNumber(self.data.interval_times)
        self.node_list.cur_refining_text.text.text = string.format(Language.ContralBeasts.AttrWheel2, self.data.interval_times)
    else
        local temp_index = self:GetIndex()
        local str = ""
        if temp_index > 3 then
            str = string.format(Language.ContralBeasts.HoleTips2, temp_index - 3)
        else
            str = string.format(Language.ContralBeasts.HoleTips1, temp_index)
        end

        self.node_list.cur_refining_text.text.text = string.format(Language.ContralBeasts.AttrWhee23, str)
        return
    end
end

-- 刷新选中状态
function BeststsRefiningItemRender:FlushSelectHl(is_select)
    self.node_list.select:CustomSetActive(is_select)
end

----------------------------------灵兽星阵属性item-----------------------
BeststsRefiningAttrItemRender = BeststsRefiningAttrItemRender or BaseClass(BaseRender)

function BeststsRefiningAttrItemRender:ReleaseCallBack()
	self.opeaate_callback = nil
    self.interval_times = nil
    self.old_star_num = nil
end

function BeststsRefiningAttrItemRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.operate_button, BindTool.Bind2(self.ResetSelfAttr, self))
end

function BeststsRefiningAttrItemRender:SetOperateCallBack(callback)
	self.opeaate_callback = callback
end

function BeststsRefiningAttrItemRender:SetIntervalTimes(interval_times)
	self.interval_times = interval_times
end

function BeststsRefiningAttrItemRender:ResetOldStarNum()
    self.old_star_num = nil
end

-- 重置属性
function BeststsRefiningAttrItemRender:ResetSelfAttr()
    if self.opeaate_callback then
        self.opeaate_callback(self)
    end
end

function BeststsRefiningAttrItemRender:OnFlush()
	if not self.data then
		return
	end

    local star_circle_attr_cfg = ControlBeastsWGData.Instance:GetStarCircleAttrCfgById(self.data.id)
    if star_circle_attr_cfg then
        local is_per = EquipmentWGData.Instance:GetAttrIsPer(star_circle_attr_cfg.array_attr_id)
        local interval_times = self.interval_times or 0
        local per_desc = is_per and "%" or "" 
        local real_attr_value = star_circle_attr_cfg.array_attr_base_value * (self.data.star_num + (interval_times * 10))
        local value_str = is_per and real_attr_value / 100 or real_attr_value

        self.node_list.attr_name.text.text = EquipmentWGData.Instance:GetAttrName(star_circle_attr_cfg.array_attr_id, false, false)
        self.node_list.attr_value.text.text = string.format("%s%s%s", "+", value_str, per_desc)

        local pz_str = string.format("a3_hs_refining_0%d", star_circle_attr_cfg.array_attr_color)
        self.node_list.pz_image.image:LoadSprite(ResPath.GetControlBeastsImg(pz_str))
    end

    for i = 1, 10 do
        local str = i < 10 and string.format("star_0%d", i) or string.format("star_%d", i)
        local star_res_str = i <= self.data.star_num and "a3_ty_xx_zc" or "a3_ty_xx_zh"
        self.node_list[str].image:LoadSprite(ResPath.GetCommonImages(star_res_str))

        if self.old_star_num and self.old_star_num ~= self.data.star_num then
            if i > self.old_star_num and i <= self.data.star_num then   -- 和缓存的发生改变需要
                local bundle_name, asset_name = ResPath.GetA2Effect("ui_huanshouxx")
                EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list[str].transform,
                                    nil, Vector3(0, 0, 0), Quaternion.Euler(0, 0, 0))
            end
        end
    end

    self.old_star_num = self.data.star_num
end

----------------------------------灵兽资质item-----------------------
BeststComposeFlairItemRender = BeststComposeFlairItemRender or BaseClass(BaseRender)
function BeststComposeFlairItemRender:OnFlush()
    if not self.data then
        return
    end

    local Flair_data = ControlBeastsWGData.Instance:GetFlairAttrDataById(self.index - 1)
    if Flair_data then
        self.node_list.attr_name.text.text = Flair_data.flair_name
    else
        self.node_list.attr_name.text.text = Language.ContralBeasts.SelectError4
    end

    local nex_star = self.data.cur_star + 1
    self.node_list.arrow:CustomSetActive(nex_star <= BEAST_DEFINE.BEAST_STAR_COUNT_MAX)
    self.node_list.add_value:CustomSetActive(nex_star <= BEAST_DEFINE.BEAST_STAR_COUNT_MAX)
    local real_value = self.data.cur_value
    local max_value = self.data.beast_preview_value
    if self.index > 4 then
        real_value = string.format("%.3f", self.data.cur_value) 
        max_value = string.format("%.3f", self.data.beast_preview_value) 
    end

    self.node_list.attr_value.text.text = real_value
    self.node_list.add_value.text.text = string.format(Language.ContralBeasts.ComposeTips, max_value) 
end


----------------------------------灵兽孔位条件item-----------------------
BeststsKingConditionRender = BeststsKingConditionRender or BaseClass(BaseRender)

function BeststsKingConditionRender:OnFlush()
    if not self.data then return end
    self.node_list.desc.text.text = self.data.desc
end

----------------------------------灵兽资质item-----------------------
BeststKingAttrItemRender = BeststKingAttrItemRender or BaseClass(BaseRender)
function BeststKingAttrItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end

    local is_per = EquipmentWGData.Instance:GetAttrIsPer(self.data.attr_str)
    local per_desc = is_per and "%" or ""
    local value_str = is_per and self.data.attr_value / 100 or self.data.attr_value
    self.node_list.attr_name.text.text = EquipmentWGData.Instance:GetAttrName(self.data.attr_str, self.name_need_space, self.need_mao_hao)
    self.node_list.attr_value.text.text = string.format("+%s%s",value_str, per_desc)
end