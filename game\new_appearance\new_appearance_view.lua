NewAppearanceWGView = NewAppearanceWGView or BaseClass(SafeBaseView)

function NewAppearanceWGView:__init()
    self:SetMaskBg()
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true

    self.default_index = TabIndex.new_appearance_upgrade_wing
    ----[[进阶
    self.ad_tab_index_list = {
        [TabIndex.new_appearance_upgrade_wing] = true,
        [TabIndex.new_appearance_upgrade_fabao] = true,
        [TabIndex.new_appearance_upgrade_shenbing] = true,
        [TabIndex.new_appearance_upgrade_jianzhen] = true,
    }

    local ad_tab = {}
    for k, v in pairs(self.ad_tab_index_list) do
        table.insert(ad_tab, k)
    end
    --]]

    ----[[进阶 - 骑宠
    self.ug_qc_tab_index_list = {
        [TabIndex.new_appearance_upgrade_lingchong] = true,
        [TabIndex.new_appearance_upgrade_mount] = true,
    }
    local ug_qc_tab = {}
    for k, v in pairs(self.ug_qc_tab_index_list) do
        table.insert(ug_qc_tab, k)
    end
    --]]

    ----[[时装
    local fashion_tab = {}
    self.fashion_tab_index_list = {}

    for k, v in pairs(NEW_APPEARANCE_ZHUANGBAN_TAB_INDEX) do
        table.insert(fashion_tab, v)
        self.fashion_tab_index_list[v] = true
    end
    --]]

    ----[[珍稀 - 骑宠鲲
    self.qc_kun_tab_index_list = {
        [TabIndex.new_appearance_mount_upstar] = true,
        [TabIndex.new_appearance_mount_upgrade] = true,
        [TabIndex.new_appearance_lingchong_hualing] = true,
        [TabIndex.new_appearance_lingchong_upstar] = true,
        [TabIndex.new_appearance_lingchong_upgrade] = true,
        [TabIndex.new_appearance_mount_hualing] = true,
        [TabIndex.new_appearance_kun_upstar] = true,
        [TabIndex.new_appearance_kun_upgrade] = true,
        [TabIndex.new_appearance_kun_hualing] = true,
    }
    local qc_kun_tab = {}
    for k, v in pairs(self.qc_kun_tab_index_list) do
        table.insert(qc_kun_tab, k)
    end

    local view_bundle = "uis/view/new_appearance_ui_prefab"
    -- self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
    self:AddViewResource(0, view_bundle, "layout_display")
    self:AddViewResource(ad_tab, view_bundle, "layout_advanced")
    self:AddViewResource(ug_qc_tab, view_bundle, "layout_upgrade_qichong")
    self:AddViewResource(fashion_tab, view_bundle, "layout_fashion")
    self:AddViewResource(qc_kun_tab, view_bundle, "layout_mount_lingchong_kun")
    self:AddViewResource(TabIndex.new_appearance_multi_mount, view_bundle, "layout_multi_mount")
    self:AddViewResource(0, "uis/view/common_panel_prefab", "VerticalTabbar")
    self:AddViewResource(0, view_bundle, "HorizontalTabbar")
    self:AddViewResource(0, view_bundle, "layout_appearance_activity")
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_top_panel")

    self:SetTabShowUIScene(0, {type = UI_SCENE_TYPE.DEFAULT})
    self.tab_sub = {
        Language.NewAppearance.TabSub1,
        Language.NewAppearance.TabSub3,
        Language.NewAppearance.TabSub4,
        Language.NewAppearance.TabSub4,
        Language.NewAppearance.TabSub4,
        nil,
    }

    self.remind_tab = {
        {
            RemindName.NewAppearance_Upgrade_Mount,
            RemindName.NewAppearance_Upgrade_Wing,
            RemindName.NewAppearance_Upgrade_FaBao,
            RemindName.NewAppearance_Upgrade_ShenBing,
            RemindName.NewAppearance_Upgrade_JianZhen,
            RemindName.NewAppearance_Upgrade_LingChong,
        },
        {
            RemindName.NewAppearance_ZhuangBan_ShouHuan,
            RemindName.NewAppearance_ZhuangBan_Foot,
            RemindName.NewAppearance_ZhuangBan_PhotoFrame,
            RemindName.NewAppearance_ZhuangBan_Bubble,
        },
        {
            RemindName.NewAppearance_LingChong_UpstarAndEquip,
            RemindName.NewAppearance_LingChong_Upgrade,
            RemindName.NewAppearance_LingChong_HuaLing,
        },
        {
            RemindName.NewAppearance_Mount_UpstarAndEquip,
            RemindName.NewAppearance_Mount_Upgrade,
            RemindName.NewAppearance_Mount_HuaLing,
        },
        {
            RemindName.NewAppearance_Kun_UpstarAndEquip,
            RemindName.NewAppearance_Kun_Upgrade,
            RemindName.NewAppearance_Kun_HuaLing,
        },

        {RemindName.NewAppearance_Multi_Mount},
        -- {RemindName.NewAppearance_Wardrobe},
    }
end

function NewAppearanceWGView:ReleaseCallBack()
    -- Runner.Instance:RemoveRunObj(self)
    RemindManager.Instance:UnBind(self.remind_callback)
    -- self:ClearFashionFootPrint()
    self:CancelWeaponTween()

    self:ActReleaseCallBack()

    if self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end

    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

    if self.show_model then
        self.show_model:DeleteMe()
        self.show_model = nil
    end

    if self.show_head_cell then
        self.show_head_cell:DeleteMe()
        self.show_head_cell = nil
    end

    self.show_bubble_cell = nil
    if self.increase_time_tips then
        self.increase_time_tips:DeleteMe()
        self.increase_time_tips = nil
    end

    self:AdvancedReleaseCallBack()
    self:UGQiChongReleaseCallBack()
    self:FashionReleaseCallBack()
    self:QiChongReleaseCallBack()
    self:MultiMountReleaseCallBack()

    self.old_fashion_role_res = nil
    self.old_fashion_weapon_res = nil
    self.old_fashion_action_name = nil
    self.next_create_fashion_footprint_time = nil
    self.old_main_res_id = nil
    self.old_main_res_fun = nil
end

function NewAppearanceWGView:LoadCallBack()
    if not self.tabbar then
        self.tabbar_special_option_flag = false
        self.tabbar = NewAppearanceBar.New(self.node_list)
        local view_bundle = "uis/view/new_appearance_ui_prefab"
        self.tabbar:SetHorizTabbarIconStr("a3_sz_icon_")
        self.tabbar:SetHorizTabbarIconPath("uis/view/new_appearance_ui/images_atlas")
        self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
        self.tabbar:SetCreateHorCallBack(function(hor_index)
            self:SetTabbarInfo(hor_index)
        end)

        self.tabbar:Init(Language.NewAppearance.TabGrop, self.tab_sub, nil, view_bundle, self.remind_tab)
        FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.NewAppearanceWGView, self.tabbar)
    end

    if not self.money_bar then
        self.money_bar = MoneyBar.New()
        local bundle, asset = ResPath.GetWidgets("MoneyBar")
        local show_params = {
            show_gold = true,
            show_bind_gold = true,
            show_coin = true,
            show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
        self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
    end

    self.remind_callback = BindTool.Bind(self.OnRemindChange, self)
    local remind_list = {
        RemindName.LingZhi_Wing,
        RemindName.LingZhi_FaBao,
        RemindName.LingZhi_JianZhen,
        RemindName.LingZhi_ShenBing,
        RemindName.LingZhi_Wing_Act,
        RemindName.LingZhi_FaBao_Act,
        RemindName.LingZhi_JianZhen_Act,
        RemindName.LingZhi_ShenBing_Act,

        RemindName.MountEquipTotal,
        RemindName.LingChongEquipTotal,
        -- RemindName.HuaKunEquipTotal,
        RemindName.NewHuanHuaFetterView,
    }

    for k, v in pairs(remind_list) do
        RemindManager.Instance:Bind(self.remind_callback, v)
    end

    -- Runner.Instance:AddRunObj(self, 8)
   local bundle, asset = ResPath.GetRawImagesPNG(TONGYONG_RAWIMAGE_ENUM.HAVE_ROLEMODEL)
   if self.node_list.RawImage_tongyong then
    self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
        end)
    end
end

function NewAppearanceWGView:OpenCallBack()
end

function NewAppearanceWGView:LoadIndexCallBack(index)
    if self.ad_tab_index_list[index] then
        self:InitFashionModel()
        self:AdvancedInitView()
    elseif self.ug_qc_tab_index_list[index] then
        self:InitFashionModel()
        self:UGQiChongInitView()
    elseif self.fashion_tab_index_list[index] then
        self:InitFashionModel()
        self:FashionInitView()
    elseif self.qc_kun_tab_index_list[index] then
        self:InitFashionModel()
        self:QiChongInitView()
    elseif index == TabIndex.new_appearance_multi_mount then
        self:InitFashionModel()
        self:MultiMountLoadCallBack() 
    end

    self:ActLoadCallBack()
end

function NewAppearanceWGView:ShowIndexCallBack(index)
    self:CheckTabbarBtnSpecialOption()
    -- self.node_list["no_upgrade_bg"]:CustomSetActive(not is_ug_index)
    -- self.node_list["upgrade_bg"]:CustomSetActive(is_ug_index)
    self.node_list.title_view_name.text.text = Language.ViewName[GuideModuleName.Appearance]

    self.node_list["display_pos"]:CustomSetActive(index ~= TabIndex.new_appearance_zhuangban_photoframe and
    index ~= TabIndex.new_appearance_zhuangban_bubble)
    self.node_list["photo_frame_pos"]:CustomSetActive(index == TabIndex.new_appearance_zhuangban_photoframe)
    self.node_list["bubble_pos"]:CustomSetActive(index == TabIndex.new_appearance_zhuangban_bubble)

    self:ClearUGQCSliderTween()
    self:ClearQiChongSliderTween()

    if not self.ug_qc_tab_index_list[index] then
        self.ug_qc_select_type = nil
    end

    if not self.qc_kun_tab_index_list[index] then
        self.qichong_select_type = nil
    end

    -- 切换UI场景 配置
    if math.floor(index / 10) == 1 then
        self:SetTabShowUIScene(0, { type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.WAIGUAN_JINGJIE })
        self:CheckUISceneShow(index)
    else
        self:SetTabShowUIScene(0, { type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.WAIGUAN })
        self:CheckUISceneShow(index)
    end

    if self.ad_tab_index_list[index] then
        self.node_list["display_pos"].rect.sizeDelta = Vector2(536, 476)
        self.node_list["display_pos"].rect.anchoredPosition = Vector2(-186, 41)
        self.origin_display_pos = self.node_list["display_pos"].rect.anchoredPosition
        self.node_list.effect_pos.rect.anchoredPosition = Vector2(-186, 20)
        self:AdvancedShowIndexCallBack()
    elseif self.ug_qc_tab_index_list[index] then
        self.node_list["display_pos"].rect.sizeDelta = Vector2(536, 476)
        self.node_list["display_pos"].rect.anchoredPosition = Vector2(-186, 41)
        self.origin_display_pos = self.node_list["display_pos"].rect.anchoredPosition
        self.node_list.effect_pos.rect.anchoredPosition = Vector2(-186, 20)
        self:UGQiChongShowIndexCallBack()
    elseif self.fashion_tab_index_list[index] then
        self.node_list["display_pos"].rect.sizeDelta = Vector2(536, 476)
        self.node_list["display_pos"].rect.anchoredPosition = Vector2(-98, -30)
        self.origin_display_pos = self.node_list["display_pos"].rect.anchoredPosition
        self.node_list.effect_pos.rect.anchoredPosition = Vector2(-98, 20)
        self:FashionShowIndexCallBack()
    elseif self.qc_kun_tab_index_list[index] then
        self.node_list["display_pos"].rect.sizeDelta = Vector2(536, 476)
        self.node_list["display_pos"].rect.anchoredPosition = Vector2(-98, -30)
        self.origin_display_pos = self.node_list["display_pos"].rect.anchoredPosition
        self.node_list.effect_pos.rect.anchoredPosition = Vector2(-98, 20)
        self:QiChongShowIndexCallBack()
    elseif index == TabIndex.new_appearance_multi_mount then
        self:MultiMountShowIndexCallBack()
    end

    self:ActShowIndexCallBack(AppearanceActIndexList[index])

    -- if self.node_list.fs_btn_fetter then
    --     self.node_list.fs_btn_fetter:CustomSetActive(FunOpen.Instance:GetFunIsOpened(FunName.HuanHuaFetterView)
    --         and self.show_fetter_index_list[index])
    -- end

    -- if self.node_list.fs_btn_wardrobe then
    --     self.node_list.fs_btn_wardrobe:CustomSetActive(FunOpen.Instance:GetFunIsOpened(FunName.WardrobeView)
    --         and self.show_wardrobe_index_list[index])
    -- end
end

function NewAppearanceWGView:OnFlush(param_t, index)
    -- print_error("----OnFlush------", param_t , index)
    for k, v in pairs(param_t) do
        if k == "all" then
            if self.ad_tab_index_list[index] then
                self:AdvancedFlushView()
            elseif self.ug_qc_tab_index_list[index] then
                self:UGQiChongFlushView()
            elseif self.fashion_tab_index_list[index] then
                self:FashionFlushView(v)
            elseif self.qc_kun_tab_index_list[index] then
                self:QiChongFlushView()
            elseif index == TabIndex.new_appearance_multi_mount then
                self:MultiMountOnFlush(v)
            end
        elseif k == "flush_pm" and AppearanceActIndexList[index] then
            self:ActFlushMyRank(v)
        elseif k == "check_tabbar" then
            self:CheckTabbarBtnSpecialOption(true)
        elseif k == "flush_model" then
            if self.show_model then
                self.show_model:PlayLastAction()
            end
        end
    end



    self:ActOnFlush(AppearanceActIndexList[index])
    self:SetTabbarInfo(index)

    local add_per = 0
    local tequan_info = YanYuGeWGData.Instance:GetShowTeQuanList()
    if not IsEmptyTable(tequan_info) then
        for k, v in ipairs(tequan_info) do
            if v.act_flag == 1 then
                add_per = add_per + (v.cfg.appe_base_add_per / 10000)
            end
        end
    end

    local attr_str = add_per == 0 and "" or string.format(Language.NewAppearance.TeQuanAdd, add_per * 100)
    if self.node_list.ad_base_attr_text then
        self.node_list.ad_base_attr_text.text.text = string.format(Language.NewAppearance.BaseAttrTitle, attr_str)
    end

    if self.node_list.ug_qc_base_attr_text then
        self.node_list.ug_qc_base_attr_text.text.text = string.format(Language.NewAppearance.BaseAttrTitle, attr_str)
    end
end

-- 使用特效
function NewAppearanceWGView:PlayUseEffect()
    local bundle_name, asset_name = ResPath.GetEffect(Ui_Effect.UI_huanhua)
    EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["used_effect_pos"].transform, nil,
        Quaternion.Euler(-12, 0, 0))
end

-- 升级特效
function NewAppearanceWGView:PlayUpLevelEffect()
    TipWGCtrl.Instance:ShowEffect({
        effect_type = UIEffectName.s_shengji,
        is_success = true,
        pos = Vector2(0, 0),
        parent_node = self.node_list["effect_pos"]
    })
end

-- 升星特效
function NewAppearanceWGView:PlayUpStarEffect()
    TipWGCtrl.Instance:ShowEffect({
        effect_type = UIEffectName.s_shengxing,
        is_success = true,
        pos = Vector2(0, 0),
        parent_node = self.node_list["effect_pos"]
    })
end

-- 升阶特效
function NewAppearanceWGView:PlayUpGradeEffect()
    TipWGCtrl.Instance:ShowEffect({
        effect_type = UIEffectName.s_shengjie,
        is_success = true,
        pos = Vector2(0, 0),
        parent_node = self.node_list["effect_pos"]
    })
end

function NewAppearanceWGView:OpenIncreaseTimeTips(item_id, ok_callback)
    if nil == self.increase_time_tips then
        self.increase_time_tips = Alert.New(nil, nil, nil, nil, true)
        self.increase_time_tips:SetCheckBoxDefaultSelect(false)
    end

    local item_name = ItemWGData.Instance:GetItemName(item_id, nil, true)
    self.increase_time_tips:SetLableString(string.format(Language.NewAppearance.IncreaseTimeStr, item_name))
    self.increase_time_tips:SetOkFunc(ok_callback)
    self.increase_time_tips:Open()
end

----[[模型展示
function NewAppearanceWGView:InitFashionModel()
    if nil == self.show_model then
        self.origin_display_pos = self.node_list["display_pos"].rect.anchoredPosition

        self.show_model = RoleModel.New()
		self.show_model:SetUISceneModel(self.node_list["display_pos"].event_trigger_listener,
								MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.show_model, {
            TabIndex.new_appearance_upgrade_wing,				-- 进阶 - 羽翼
            TabIndex.new_appearance_upgrade_fabao,				-- 进阶 - 法宝
            TabIndex.new_appearance_upgrade_shenbing,			-- 进阶 - 神武
            TabIndex.new_appearance_upgrade_jianzhen,			-- 进阶 - 背饰
            TabIndex.new_appearance_upgrade_lingchong,			-- 进阶 - 灵宠
            TabIndex.new_appearance_upgrade_mount,				-- 进阶 - 坐骑
            TabIndex.new_appearance_zhuangban_mask,				-- 装扮 - 脸饰
            TabIndex.new_appearance_zhuangban_belt,				-- 装扮 - 腰饰
            TabIndex.new_appearance_zhuangban_shouhuan,			-- 装扮 - 手环
            TabIndex.new_appearance_zhuangban_foot,				-- 装扮 - 足迹
            TabIndex.new_appearance_lingchong_upstar,			-- 灵宠 - 升星
            TabIndex.new_appearance_lingchong_upgrade,			-- 灵宠 - 进阶
            TabIndex.new_appearance_lingchong_hualing,			-- 灵宠 - 化灵
            TabIndex.new_appearance_mount_upstar,				-- 坐骑 - 升星
            TabIndex.new_appearance_mount_upgrade,				-- 坐骑 - 进阶
            TabIndex.new_appearance_mount_hualing,				-- 坐骑 - 化灵
            TabIndex.new_appearance_kun_upstar,					-- 仙鲲 - 升星
            TabIndex.new_appearance_kun_upgrade,				-- 仙鲲	- 进阶
            TabIndex.new_appearance_kun_hualing,                -- 仙鲲 - 化灵
            TabIndex.new_appearance_multi_mount,                -- 双人坐骑
            }
        )
    end
end

function NewAppearanceWGView:CancelWeaponTween()
    if self.tween_weapon then
        self.tween_weapon:Kill()
        self.tween_weapon = nil

        local tween_root = self.node_list["display_pos"]
        if tween_root then
            tween_root.rect.anchoredPosition = self.origin_display_pos
        end
    end
end

function NewAppearanceWGView:PlayWeaponTween()
    if not self.tween_weapon then
        local tween_root = self.node_list["display_pos"].rect
        self.tween_weapon = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 50, 1)
        self.tween_weapon:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
    else
        self.tween_weapon:Restart()
    end
end

function NewAppearanceWGView:FlushFashionModel(part_type, res_id)
    if part_type ~= SHIZHUANG_TYPE.SHENBING then
        self:CancelWeaponTween()
    end

    -- self:ClearFashionFootPrint()
    self.show_model:ClearLoadComplete()
    self.show_model:RemoveHalo()
    self.show_model:RemoveWaist()
    self.show_model:RemoveMask()
    self.show_model:RemoveTail()
    self.show_model:RemoveShouHuan()
    self.show_model:RemoveJianZhen()
    self.show_model:RemoveFootTrail()
    self.model_need_weapon = false
    if part_type == SHIZHUANG_TYPE.BODY then -- 时装
        self.model_need_weapon = true
        self:ShowRoleFashionModel(part_type, res_id)
    elseif part_type == SHIZHUANG_TYPE.FOOT then -- 足迹
        self:ShowRoleFashionModel(part_type, nil, SceneObjAnimator.Move)
        self.show_model:SetFootTrailModel(res_id)
        self.show_model:SetRotation(MODEL_ROTATION_TYPE.FOOT)
        self.show_model:PlayRoleAction(SceneObjAnimator.Move)
        -- self.is_show_foot_print = true
        -- self.fashion_foot_effect_id = res_id
    elseif part_type == SHIZHUANG_TYPE.HALO then -- 光环
        -- self:ShowFashionMainModel(ResPath.GetHaloModel, res_id)
        self.model_need_weapon = true
        self:ShowRoleFashionModel(part_type)
        self.show_model:SetHaloResid(res_id)
    elseif part_type == SHIZHUANG_TYPE.WING then -- 仙翼
        -- self.show_model:SetLoadComplete(function()
        --     self.show_model:SetTrigger(SceneObjAnimator.Rest, true)
        -- end)
        self:ShowFashionMainModel(ResPath.GetWingModel, res_id, function ()
            self.show_model:PlayWingAction()
        end)
    elseif part_type == SHIZHUANG_TYPE.FABAO then -- 法宝
        self:ShowFashionMainModel(ResPath.GetFaBaoModel, res_id)
    elseif part_type == SHIZHUANG_TYPE.SHENBING then -- 神兵
        local weapon_res_id = RoleWGData.GetFashionWeaponId(nil, nil, res_id)
        self:ShowFashionMainModel(ResPath.GetWeaponModelRes, weapon_res_id)
        -- self:PlayWeaponTween()
    elseif part_type == SHIZHUANG_TYPE.PHOTOFRAME then -- 相框
        self:ShowPhotoFrameModel(res_id)
    elseif part_type == SHIZHUANG_TYPE.BUBBLE then -- 气泡
        self:ShowBubbleModel(res_id)
    elseif part_type == SHIZHUANG_TYPE.MASK then -- 面饰
        self.model_need_weapon = true
        self:ShowRoleFashionModel(part_type)
        self.show_model:SetMaskResid(res_id)
    elseif part_type == SHIZHUANG_TYPE.BELT then -- 腰饰
        self.model_need_weapon = true
        self:ShowRoleFashionModel(part_type)
        self.show_model:SetWaistResid(res_id)
    elseif part_type == SHIZHUANG_TYPE.WEIBA then -- 尾巴
        self.model_need_weapon = true
        self:ShowRoleFashionModel(part_type)
        self.show_model:SetTailResid(res_id)
    elseif part_type == SHIZHUANG_TYPE.SHOUHUAN then -- 手环
        self.model_need_weapon = true
        self:ShowRoleFashionModel(part_type)
        self.show_model:SetShouHuanResid(res_id)
    elseif part_type == SHIZHUANG_TYPE.JIANZHEN then -- 剑阵
        self.show_model:SetLoadComplete(function()
            self.show_model:SetTrigger(SceneObjAnimator.Rest, false)
        end)
        self:ShowFashionMainModel(ResPath.GetJianZhenModel, res_id)
    end

    if not self.model_need_weapon then
        self.show_model:RemoveWeapon()
    end

    -- 矫正
    -- if part_type ~= SHIZHUANG_TYPE.PHOTOFRAME
    --     and part_type ~= SHIZHUANG_TYPE.BUBBLE
    --     and part_type ~= SHIZHUANG_TYPE.WEIBA
    --     and part_type ~= SHIZHUANG_TYPE.FOOT then
        -- self.show_model:FixToOrthographic(self.root_node_transform)
    -- end
end

-- 角色模型
function NewAppearanceWGView:ShowRoleFashionModel(part_type, fashion_res, action_name)
    local role_res_id, weapon_res_id = AppearanceWGData.Instance:GetRoleResId()
    if fashion_res then
        role_res_id = AppearanceWGData.GetFashionBodyResIdByResViewId(fashion_res)
    end

    -- 武器
    if not self.model_need_weapon then
        weapon_res_id = 0
    end

    action_name = action_name or SceneObjAnimator.UiIdle

    if role_res_id ~= self.old_fashion_role_res or weapon_res_id ~= self.old_fashion_weapon_res then
        self.old_fashion_role_res = role_res_id
        self.old_fashion_weapon_res = weapon_res_id
        self.old_fashion_action_name = action_name

        local extra_role_model_data = {
            weapon_res_id = weapon_res_id,
            animation_name = action_name,
        }
        self.show_model:SetRoleResid(role_res_id, nil, extra_role_model_data)
    else
        if action_name ~= self.old_fashion_action_name then
            self.old_fashion_action_name = action_name
            self.show_model:PlayRoleAction(action_name)
            self.show_model:FakeHideDelayShow()
        end
    end

    -- 角度
    if part_type == SHIZHUANG_TYPE.FOOT then
        self.show_model:SetRotation(MODEL_ROTATION_TYPE.FOOT)
    elseif part_type == SHIZHUANG_TYPE.WEIBA then
        self.show_model:SetRotation(MODEL_ROTATION_TYPE.WEIBA)
    end

    self.old_main_res_id = nil
    self.old_main_res_fun = nil
end

-- 主模型
function NewAppearanceWGView:ShowFashionMainModel(res_path, res_id, callback)
    if self.old_main_res_id == res_id and self.old_main_res_fun == res_path then
        return
    end

    self.old_fashion_role_res = nil
    self.old_fashion_weapon_res = nil
    self.old_fashion_action_name = 0
    self:CancelWeaponTween()
    self.show_model:ClearLoadComplete()
    self.show_model:ClearPartCrossFadeAnimCache()
    self.show_model:RemoveAllModel()
    local bundle, asset = res_path(res_id)
    self.show_model:SetMainAsset(bundle, asset, callback)
    self.old_main_res_id = res_id
    self.old_main_res_fun = res_path
end

----[[ 足迹
-- function NewAppearanceWGView:Update(now_time, elapse_time)
--     if not self.is_show_foot_print then
--         return
--     end

--     if self.next_create_fashion_footprint_time == 0 then
--         self:CreateFashionFootPrint()
--         self.next_create_fashion_footprint_time = Status.NowTime + COMMON_CONSTS.FOOTPRINT_CREATE_GAP_TIME
--     end

--     if self.next_create_fashion_footprint_time == nil then --初生时也是位置改变，不播
--         self.next_create_fashion_footprint_time = 0
--     end

--     if self.next_create_fashion_footprint_time > 0 and now_time >= self.next_create_fashion_footprint_time then
--         self.next_create_fashion_footprint_time = 0
--     end

--     self:UpdateFashionFootprintPos()
-- end

-- 时装足迹 - 清除
-- function NewAppearanceWGView:ClearFashionFootPrint()
--     if not IsEmptyTable(self.fashion_footprint_list) then
--         for k, v in pairs(self.fashion_footprint_list) do
--             if v.obj ~= nil and not IsNil(v.obj) and v.role_model ~= nil then
--                 v.role_model:OnRemoveGameObject(v.obj)
--                 v.obj:SetActive(false)
--             end
--         end

--         self.fashion_footprint_list = {}
--     end

--     self.is_show_foot_print = nil
--     self.fashion_foot_effect_id = nil
-- end

-- 时装足迹 - 创建
-- function NewAppearanceWGView:CreateFashionFootPrint()
--     if nil == self.fashion_foot_effect_id then
--         return
--     end

--     if nil == self.fashion_footprint_list then
--         self.fashion_footprint_list = {}
--     end

--     local pos = self.show_model.draw_obj:GetRoot().transform
--     local bundle, asset = ResPath.GetUIFootEffect(self.fashion_foot_effect_id)
--     EffectManager.Instance:PlayControlEffect(self, bundle, asset,
--         Vector3(pos.position.x, pos.position.y, pos.position.z), nil, pos, nil, function(obj)
--         if obj then
--             if nil ~= obj and self.fashion_footprint_list then
--                 if self.show_model then
--                     obj.transform.localPosition = Vector3.zero
--                     obj:SetActive(false)
--                     obj:SetActive(true)
--                     table.insert(self.fashion_footprint_list, { obj = obj, role_model = self.show_model })
--                     self.show_model:OnAddGameobject(obj)
--                 else
--                     ResPoolMgr:Release(obj)
--                 end
--             end
--         end
--     end)

--     if #self.fashion_footprint_list > 2 then
--         local obj = table.remove(self.fashion_footprint_list, 1)
--         obj.role_model:OnRemoveGameObject(obj.obj)
--         if not IsNil(obj.obj) then
--             obj.obj:SetActive(false)
--         end
--     end
-- end

-- 时装足迹 - 位移
-- function NewAppearanceWGView:UpdateFashionFootprintPos()
--     if nil == self.fashion_footprint_list then
--         return
--     end

--     for k, v in pairs(self.fashion_footprint_list) do
--         if not IsNil(v.obj) then
--             local pos = v.obj.transform.localPosition
--             v.obj.transform.localPosition = Vector3(pos.x, pos.y, pos.z - 1.5)
--         end
--     end
-- end

-- 时装相框
function NewAppearanceWGView:ShowPhotoFrameModel(res_id)
    self.show_model:ClearPartCrossFadeAnimCache()
    self.old_fashion_action_name = nil
    self.old_fashion_weapon_res = 0

    if not self.show_head_cell then
        self.show_head_cell = BaseHeadCell.New(self.node_list["photo_frame_pos"])
    end

    local data = { fashion_photoframe = res_id }
    self.show_head_cell:SetImgBg(true)
    self.show_head_cell:SetData(data)
    self.show_head_cell:SetBgActive(false)
end

-- 时装气泡
function NewAppearanceWGView:ShowBubbleModel(res_id)
    self.show_model:ClearPartCrossFadeAnimCache()
    self.old_fashion_action_name = nil
    self.old_fashion_weapon_res = 0

    if not self.show_bubble_cell then
        self.show_bubble_cell = AllocAsyncLoader(self, "fashion_bubble_cell")
        self.show_bubble_cell:SetIsUseObjPool(true)
        self.show_bubble_cell:SetParent(self.node_list["bubble_pos"].transform)
    end

    local asset, bundle = ResPath.ChatBigBubbleBig(res_id)
    self.show_bubble_cell:Load(asset, bundle)
end

-- 骑宠模型
function NewAppearanceWGView:ShowQiChongModel(qc_type, appe_id)
    if not appe_id then
        return
    end

    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then -- 坐骑
        self:ShowFashionMainModel(ResPath.GetMountModel, appe_id, function ()
            self.show_model:PlayMountAction()
        end)
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then -- 灵宠
        self:ShowFashionMainModel(ResPath.GetPetModel, appe_id, function ()
            self.show_model:PlayLingJianAction()
        end)
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then -- 仙鲲
        self:ShowFashionMainModel(ResPath.GetMountModel, appe_id, function ()
            self.show_model:PlayMountAction()
        end)
    end

    -- self.show_model:FixToOrthographic(self.root_node_transform)
end

--]]

-- 红点回调
function NewAppearanceWGView:OnRemindChange(remind_name, num)
    if remind_name == RemindName.MountEquipTotal
        or remind_name == RemindName.LingChongEquipTotal then
        -- or remind_name == RemindName.HuaKunEquipTotal then
        self:FlushQiChongEquipRemind()
    end
    
    self:FlushAdvancedLingZhiRemind()
    self:OnNewAppearanceACTRemindChange(remind_name, num)
end

function NewAppearanceWGView:SetTabbarInfo(index)
    if not self.tabbar then
        return
    end

    self.tabbar:CheckBPActIsOpen(index)
end

--设置王者特权按钮信息.
--type:1 为 装备；type：2 为 骑宠.
function NewAppearanceWGView:SetKingVipBtnInfo(type)
    local king_vip_btn = nil
    local king_vip_text = nil
    local king_vip_go = nil
    -- local title2 = nil
    if type == 1 then
        king_vip_btn = self.node_list.king_vip_btn
        king_vip_text = self.node_list.king_vip_text
        king_vip_go = self.node_list.king_vip_go_text
        -- title2 = self.node_list.title2
    elseif type == 2 then
        king_vip_btn = self.node_list.qc_king_vip_btn
        king_vip_text = self.node_list.qc_king_vip_text
        king_vip_go = self.node_list.qc_king_vip_go_text
        -- title2 = self.node_list.qc_title2
    end

    if nil == king_vip_btn or nil == king_vip_text or nil == king_vip_go then-- or nil == title2 then
        return
    end

    local data = RechargeWGData.Instance:GetCurKingVipAddition()
    if not IsEmptyTable(data) then
        --取个位数idx.
        local attr_add = data["add_" .. (self.show_index - 1) % 10]
        attr_add = attr_add / 100

        king_vip_text.text.text = string.format(Language.NewAppearance.king_vip_text, attr_add)

        local is_active = RechargeWGCtrl.Instance:GetKingVipIsActive()

        king_vip_btn.button.interactable = not is_active

        local king_vip_go_text = ""

        if is_active then
            king_vip_go_text = Language.NewAppearance.king_vip_go_text2
        else
            king_vip_go_text = Language.NewAppearance.king_vip_go_text
        end
        king_vip_go.text.text = king_vip_go_text
    end

    king_vip_btn:SetActive(not IsEmptyTable(data))
    -- title2:SetActive(IsEmptyTable(data))
end

--王者特权前往激活.
function NewAppearanceWGView:OnClickKingVipBtn()
    RechargeWGCtrl.Instance:Open(TabIndex.recharge_king_vip)
end

function NewAppearanceWGView:CheckTabbarBtnSpecialOption(is_force)
    if not self.tabbar_special_option_flag or is_force then
        self.tabbar_special_option_flag = true
        self.tabbar:SetToggleVisible(40, true)
        -- local is_open = TianShenJuexingWGData.Instance:GetShenWuFuncIsOpen()
        -- self.tabbar:SetOtherBtn(40, not is_open, function()
        --     FunOpen.Instance:OpenViewByName(GuideModuleName.TianShenJuexing)
        -- end)
    end

    -- local lj_toggle_is_show = math.floor(self.show_index / 10) == 1
    -- self.tabbar:SetToggleVisible(TabIndex.new_appearance_upgrade_lingchong, true)
    -- local lj_upgrade_fun_is_open = FunOpen.Instance:GetFunIsOpened(FunName.NewAppearanceUpgradeLingChong)
    -- self.tabbar:SetHorOtherBtn(TabIndex.new_appearance_upgrade_lingchong, (lj_toggle_is_show and not lj_upgrade_fun_is_open),
    -- function ()
    --     FunOpen.Instance:OpenViewByName(GuideModuleName.TianShenJuexing)
    -- end)
end

-- 还原UI场景
function NewAppearanceWGView:FlushCheckUISceneShow()
    self:CheckUISceneShow(self.show_index)
    -- 切换UI场景 配置
    if math.floor(self.show_index / 10) == 1 then
        Scene.Instance:SetUISceneControllerConfigIndexByType(UI_SCENE_TYPE.DEFAULT, UI_SCENE_CONFIG_INDEX.WAIGUAN_JINGJIE)
    else
        Scene.Instance:SetUISceneControllerConfigIndexByType(UI_SCENE_TYPE.DEFAULT, UI_SCENE_CONFIG_INDEX.WAIGUAN)
    end
end

------------------------ NewAppearanceBar
NewAppearanceBar = NewAppearanceBar or BaseClass(Tabbar)

function NewAppearanceBar:CheckBPActIsOpen(index)
    local tabbar_v_index = math.floor(index / 10)
    self.act_list = {}
    for k, v in pairs(AppearanceActIndexList) do
        local v_index = math.floor(k / 10)
        if tabbar_v_index == v_index then
            if not self.act_list[v_index] then
                self.act_list[v_index] = {}
            end

            local h_index = k % 10
            table.insert(self.act_list[v_index], h_index)
        end
    end

    local ver_cell_list = self:GetVerCellList()
    local hor_cell_list = self:GetHorCellList()
    for k, v in pairs(ver_cell_list) do
        v:ShowActTag(false)
        for k1, v1 in pairs(AppearanceActIndexList) do
            local v_index = math.floor(k1 / 10)
            local is_open = NewAppearanceWGData.Instance:CheckBPActIsOpenByRushType(v1)
            if v_index == k and is_open then
                v:ShowActTag(is_open)
                break
            end
        end
    end

    for k, v in pairs(hor_cell_list) do
        v:ShowActTag(false)
        if self.act_list[tabbar_v_index] then
            for k1, v1 in pairs(AppearanceActIndexList) do
                local v_index = math.floor(k1 / 10)
                local h_index = k1 % 10
                if h_index == k and v_index == tabbar_v_index then
                    local is_open = NewAppearanceWGData.Instance:CheckBPActIsOpenByRushType(v1)
                    v:ShowActTag(is_open)
                end
            end
        end
    end
end

function NewAppearanceBar:SetToggleVisible(k, visible)
	local ver = math.floor(k / 10)
	local hor = k % 10

	if nil == self.tab_fun_list[ver] then
		self.tab_fun_list[ver] = {}
	end

	self.tab_fun_list[ver][hor] = visible
	local v_vis = visible
	if self.length[ver] then
		for i=1, (self.length[ver]) do
			if self.tab_fun_list[ver][i] ~= false then
				v_vis = true
				break
			end
		end
	end

    -- 策划谢子鹏需求，强制打开页签增加点击事件，在此特殊处理开关other_btn
	if self.ver_cell_list[ver] then
		self.ver_cell_list[ver]:SetActive(v_vis)
        if ver == 4 then
            self.ver_cell_list[ver]:SetOtherBtn(not v_vis)
        end
	end

	self.ver_fun_list[ver] = v_vis
	if self.hor_cell_list[hor] and ver == self.select_ver_index then
		self.hor_cell_list[hor]:SetActive(visible)
        if hor == TabIndex.new_appearance_upgrade_lingchong % 10 then
            self.hor_cell_list[hor]:SetOtherBtn(not visible)
        end
	end
end