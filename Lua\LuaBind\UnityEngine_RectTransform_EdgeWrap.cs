﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class UnityEngine_RectTransform_EdgeWrap
{
	public static void Register(LuaState L)
	{
		L.<PERSON>gin<PERSON>num(typeof(UnityEngine.RectTransform.Edge));
		<PERSON><PERSON>("Left", get_Left, null);
		<PERSON><PERSON>("Right", get_Right, null);
		<PERSON><PERSON>("Top", get_Top, null);
		<PERSON><PERSON>("Bottom", get_Bottom, null);
		<PERSON><PERSON>("IntToEnum", IntToEnum);
		<PERSON><PERSON>();
		TypeTraits<UnityEngine.RectTransform.Edge>.Check = CheckType;
		StackTraits<UnityEngine.RectTransform.Edge>.Push = Push;
	}

	static void Push(IntPtr L, UnityEngine.RectTransform.Edge arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(UnityEngine.RectTransform.Edge), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Left(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RectTransform.Edge.Left);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Right(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RectTransform.Edge.Right);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Top(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RectTransform.Edge.Top);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Bottom(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RectTransform.Edge.Bottom);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		UnityEngine.RectTransform.Edge o = (UnityEngine.RectTransform.Edge)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

