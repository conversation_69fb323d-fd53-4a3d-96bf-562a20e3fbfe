function ZhanLingView:InitShopPanel()
    if nil == self.shop_list then
        local bundle, asset = "uis/view/zhanling_ui_prefab", "shop_cell"
        self.shop_list = AsyncBaseGrid.New()
        self.shop_big_data, self.exchange_list = ZhanLingWGData.Instance:GetShopShowList()
        self.shop_list:CreateCells({
            col = 2,
            cell_count = #self.exchange_list,
            itemRender = ZhanLingShopRender,
            list_view = self.node_list["shop_list"],
            assetBundle = bundle,
            assetName = asset,
            is_no_data_hide = true
        })
        self.shop_list:SetSelectCallBack(BindTool.Bind(self.OnClickExchangeItem, self))
        self.shop_list:SetStartZeroIndex(false)

        XUI.AddClickEventListener(self.node_list["btn_exchange"], BindTool.Bind(self.OnClickExchangeBtn, self))
        XUI.AddClickEventListener(self.node_list["icon"], BindTool.Bind(self.OnClickExchangeItem, self))
        self.node_list.shop_list.scroll_rect.onValueChanged:AddListener(BindTool.Bind(self.OnShopScrollValueChanged, self))
        self:InitZhanLingModel()
    end
    -- self:InitExchangeItemImg()
end

function ZhanLingView:InitZhanLingModel()
    if nil == self.display_pos then
        self.display_pos = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["display_pos"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = true,
        }
        
        self.display_pos:SetRenderTexUI3DModel(display_data)
        -- self.display_pos:SetUI3DModel(self.node_list["display_pos"].transform,
        --     self.node_list["display_pos"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.display_pos)
    end
end

function ZhanLingView:InitExchangeItemImg()
    local exchange_item_cfg = ZhanLingWGData.Instance:GetExchangeItemCfg()
    if exchange_item_cfg then
        local bundle, asset = ResPath.GetItem(exchange_item_cfg.icon_id)
        self.node_list.shop_coin_img.image:LoadSpriteAsync(bundle, asset)
    end
end

function ZhanLingView:ReleaseShopPanel()
    if self.shop_list then
        self.shop_list:DeleteMe()
        self.shop_list = nil
    end
    if self.display_pos then
        self.display_pos:DeleteMe()
        self.display_pos = nil
    end

    self.shop_big_data = nil
    self.exchange_list = nil
end

function ZhanLingView:FlushShopPanel()
    self.shop_list:SetDataList(self.exchange_list)

    local item_cfg_data = ItemWGData.Instance:GetItemConfig(self.shop_big_data.item_id)

    --名称
    self.node_list.item_name.text.text = item_cfg_data and item_cfg_data.name or ""

    --兑换所需货币图标
    local exchange_item_cfg = ZhanLingWGData.Instance:GetExchangeItemCfg()
    self.node_list.icon:SetActive(false)
    if exchange_item_cfg then
        local bundle, asset = ResPath.GetItem(exchange_item_cfg.icon_id)
        self.node_list.icon.image:LoadSpriteAsync(bundle, asset, function()
            self.node_list.icon:SetActive(true)
        end)
    end



    local is_limit_type = self.shop_big_data.limit_type ~= ZhanLingShowType.NoLimit
    self.node_list.limit_str:SetActive(is_limit_type)
    local is_limit = false
    if is_limit_type then
        local has_exchange_num = ZhanLingWGData.Instance:GetLimitExchangeItemInfo(is_limit_type,
            self.shop_big_data.item_seq)
        is_limit = has_exchange_num >= self.shop_big_data.limit_count
        local str = self.shop_big_data.limit_type == ZhanLingShowType.DailyLimit and Language.ZhanLing
        .ExchangeLimitDesc1 or Language.ZhanLing.ExchangeLimitDesc2
        self.node_list.limit_str.text.text = string.format(str, has_exchange_num, self.shop_big_data.limit_count)
    end

    local had_num = ZhanLingWGData.Instance:GetExchangeItemNum()
    local is_enough = had_num >= self.shop_big_data.item_price
    --local str_color = is_enough and COLOR3B.GREEN or COLOR3B.PINK
    --兑换按钮文本
    self.node_list.need_str.text.text = string.format(Language.ZhanLing.ExchangeButton, self.shop_big_data.item_price) -- ToColorStr(had_num, str_color) .. "/" .. self.shop_big_data.item_price --数量
    XUI.SetButtonEnabled(self.node_list.btn_exchange, is_enough and not is_limit)
    -- self.node_list.shop_coin_str.text.text = had_num
    
    -- 模型
    if self.display_pos and self.shop_big_data then
        local data = {}
        data.item_id = self.shop_big_data.model_id
        local bundle, asset = ResPath.GetTianShenShenQiPath(data.item_id)
        self.display_pos:SetMainAsset(bundle, asset)
    end

    if self.display_pos then
		self.display_pos:PlayLastAction()
	end
end

function ZhanLingView:OnShopScrollValueChanged(pos)
    if pos then
        self.node_list.down_arrow:CustomSetActive(pos.y > 0.1)
    end
end

function ZhanLingView:OnClickExchangeBtn()
    if IsEmptyTable(self.shop_big_data) then
        return
    end

    local had_num = ZhanLingWGData.Instance:GetExchangeItemNum()
    if had_num < self.shop_big_data.item_price then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanLing.NotEnoughExchange)
        return
    end

    local empty_num = ItemWGData.Instance:GetEmptyNum()
    local stuff_empty_num = ItemWGData.Instance:GetStuffBagEmptyNum()
    if empty_num == 0 or stuff_empty_num == 0 then
        RoleBagWGCtrl.Instance:OpenRoleBagCleanTips(KNAPSACK_TYPE.NORMAL)
        return
    end

    ZhanLingWGCtrl.Instance:SendZhanLingRequest(ZhanLingOperType.RechangeGoods, self.shop_big_data.item_seq, 1)
end

function ZhanLingView:OnClickExchangeItem()
    local other_cfg = ZhanLingWGData.Instance:GetZhanLingOtherCfg()
    local exchange_item_id = other_cfg and other_cfg.reward_item_id or 0
    TipWGCtrl.Instance:OpenItem({ item_id = exchange_item_id })
end
