SiXiangCallView = SiXiangCallView or BaseClass(SafeBaseView)

function SiXiangCallView:__init(view_name)
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self.default_index = TabIndex.sixiang_call_sx

    local bundle_name = "uis/view/sixiang_call_prefab"
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_common_panel")
    self:AddViewResource(TabIndex.sixiang_call_sx, bundle_name, "layout_sixiang_call")
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_common_top_panel")
end

function SiXiangCallView:LoadCallBack()
    local bundle, asset = ResPath.GetRawImagesJPG("a2_ymgy_bg0")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

    self.node_list.title_view_name.text.text = Language.ViewName.YouMingMiBao
    if not self.item_data_event then
        self.item_data_event = BindTool.Bind1(self.OnItemDataChange, self)
        ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
    end
end

function SiXiangCallView:LoadIndexCallBack(index)
    if index == TabIndex.sixiang_call_sx then
        self:InitSummonView()
    end
end

function SiXiangCallView:ReleaseCallBack()
    if self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end

    if self.item_data_event then
        ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
        self.item_data_event = nil
    end

    if self.remind_callback then
        RemindManager.Instance:UnBind(self.remind_callback)
        self.remind_callback = nil
    end

    self:ReleaseSummonView()
end

function SiXiangCallView:OpenCallBack()
    local is_open = FunOpen.Instance:GetFunIsOpened("sixiang_call_sx")
    if is_open then
        SiXiangCallWGCtrl.Instance:SendSiXiangRequest(OP_YUAN_SHEN_ZHAO_HUAN_TYPE.INFO) -- 四象召唤信息
    end
end

function SiXiangCallView:ShowIndexCallBack(index)
    if index == TabIndex.sixiang_call_sx then
        self:ShowSummonView()
    end
end

function SiXiangCallView:OnFlush(param_t, index)
    if index == TabIndex.sixiang_call_sx then
        self:FlushSummonView(param_t)
    end
end

function SiXiangCallView:InitTabbar()
    if not self.tabbar then
        local remind_list = {
            {RemindName.SiXiangCall_SXSM},
        }
        self.tabbar = Tabbar.New(self.node_list)
        self.tabbar:Init(Language.SiXiangCall.ToggleNameList, nil, "uis/view/sixiang_call_prefab", nil, remind_list)
        self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
        FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.SiXiangCallView, self.tabbar)
    end

    if self.remind_callback == nil then
        self.remind_callback = BindTool.Bind(self.OnRemindChange, self)
        RemindManager.Instance:Bind(self.remind_callback, RemindName.FightSoul_Bone)
    end
end

function SiXiangCallView:OnItemDataChange( ... )
    local index = self:GetShowIndex()
    if not self:IsLoadedIndex(index) then
        return
    end

    if index == TabIndex.sixiang_call_sx then
        self:FlushSummonConst(...)
    end
end

function SiXiangCallView:OnRemindChange(remind_name, num)
    if remind_name == RemindName.FightSoul_Bone then
		if self.node_list.sixiang_btn_remind then
			self.node_list.sixiang_btn_remind:SetActive(num > 0)
		end
	end
end