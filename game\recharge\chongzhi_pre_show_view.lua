--首冲提示
FirstRechargePreShow = FirstRechargePreShow or BaseClass(SafeBaseView)
function FirstRechargePreShow:__init()
	--self:SetMaskBg(true,true,false)
	self.view_layer = UiLayer.MainUIHigh
	self:AddViewResource(0, "uis/view/rechargereward_ui_prefab", "First_recharge_pre_show")
	
	self.active_close = false
end

function FirstRechargePreShow:__delete()
	self.is_show_set_root_active = nil
	self.active = nil
end

function FirstRechargePreShow:ReleaseCallBack()
	self.is_show_set_root_active = nil
	self.active = nil
end

function FirstRechargePreShow:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_open"], BindTool.Bind1(self.OpenRechargeFun, self))

	self.load_complete = true

	if self.is_show_set_root_active and self:IsOpen() then
		self:SetRootNodeActive(self.active or true)
		self.is_show_set_root_active = nil
		self.active = nil
	end
end

function FirstRechargePreShow:OpenRechargeFun()
	FunOpen.Instance:OpenViewByName(GuideModuleName.FirstCharge)
end

function FirstRechargePreShow:OvSetRootNodeActive(is_active)
	if not self:IsOpen() then
		return
	end
	if not self.load_complete then
		self.is_show_set_root_active = true
		self.active = is_active
		return
	end

	self.root_node.gameObject:SetActive(is_active)
end