﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;


[CreateAssetMenu(menuName = "MySubMenue/Create YYSkyProfile ")]
public class YYSkyProfile : ScriptableObject
{

    public void UpdateSkySphere(Camera camera, Material material, GameObject skySphere, YYCelestialObject sun)
    {
        //if (material == null || YYLightManager.Instance == null || sun == null)
        //{
        //    return;
        //}
        //
        SetSkySphereScalesAndPositions(camera, skySphere);
        //

        SetShaderSkyParameters(camera, material, sun);
    }

    //public Gradient SkyGradient;
    //public Gradient SkyGroundGradient;

    public YYAtmosphereProfile AtmosphereProfile;

    private void SetShaderSkyParameters(Camera camera, Material material, YYCelestialObject sun)
    {
        //SetGlobalSkyParameters(SkyGradient, SkyGroundGradient, AtmosphereProfile.MieG, AtmosphereProfile.MieScatterCoefficient, AtmosphereProfile.RayleighScatterCoefficient,
        //       AtmosphereProfile.AtmosphereThickness, AtmosphereProfile.AtmosphereTurbidity,
        //       SkyExposure, SkyTintColor, SkyOuterRadius, SkyInnerRadius, camera.transform.position.y, LightWaveLengths,
        //       sun);
        SetGlobalSkyParameters(AtmosphereProfile.MieG,sun);
    }

    //internal static void SetGlobalSkyParameters(Gradient skyGradient, Gradient skyGroundGradient, float skyAtmosphereMie, float skyMieMultiplier, float skyRayleighMultiplier, float skyAtmosphereThickness,
    //      float skyAtmosphereTurbidity, float skyExposure, Color skyTintColor, float skyOuterRadius, float skyInnerRadius, float skyCameraHeight, Vector4 waveLength,
    //      YYCelestialObject sun)
      internal static void SetGlobalSkyParameters(float skyAtmosphereMie, YYCelestialObject sun)
    {
        if (sun == null)
        {
            return;
        }
        // global sky parameters
        float mieG = -skyAtmosphereMie;
        float mieG2 = mieG * mieG;

        //Vector4 v4 = new Vector4(1.5f * ((1.0f - mieG2) / (2.0f + mieG2)), 1.0f + mieG2, 2.0f + mieG, 0.0f);
        //Debug.Log("_WeatherMakerSkyMie " + v4);

        Shader.SetGlobalVector(WMS._WeatherMakerSkyMie, new Vector4(1.5f * ((1.0f - mieG2) / (2.0f + mieG2)), 1.0f + mieG2, 2.0f + mieG, 0.0f));

    }



    private void SetSkySphereScalesAndPositions(Camera camera, GameObject skySphere)
    {

        // adjust sky sphere position and scale
        float farPlane = camera.farClipPlane * 0.72f;
        Vector3 anchor = camera.transform.position;
        float scale = farPlane * 0.5f;

        

        // move sun back near the far plane and scale appropriately
        foreach (YYCelestialObject sun in YYLightManager.Instance.Suns)
        {
            if (sun != null && sun.OrbitTypeIsPerspective)
            {
                //Debug.Log("ppppppppppppp");
                Vector3 sunOffset = (sun.transform.forward * ((farPlane * 0.9f) - scale));
                sun.transform.position = anchor - sunOffset;
                sun.transform.localScale = new Vector3(scale, scale, scale);
            }
        }
    }
}
