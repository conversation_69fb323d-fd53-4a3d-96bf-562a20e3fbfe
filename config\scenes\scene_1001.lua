return {
    id = 1001,
    name = "云阙天宗",
    scene_type = 0,
    bundle_name = "scenes/map/a3_yw_xinshoucun_main",
    asset_name = "A3_YW_XinShouCun_Main",
    width = 315,
    height = 1110,
    origin_x = 133,
    origin_y = -76,
    levellimit = 1,
    is_forbid_pk = 0,
    skip_loading = 0,
    show_weather = 0,
    scene_broadcast = 1,
    scenex = 201,
    sceney = 36,
    npcs = {
		{id=10101, x=198, y=58, rotation_y = 160, is_walking = 0, paths = {}},
		{id=10102, x=203, y=147, rotation_y = 190, is_walking = 0, paths = {}},
		{id=10103, x=163, y=792, rotation_y = 180, is_walking = 0, paths = {}},
		{id=10104, x=165, y=433, rotation_y = 180, is_walking = 0, paths = {}},
		{id=10105, x=164, y=487, rotation_y = 180, is_walking = 0, paths = {}},
		{id=10108, x=220, y=974, rotation_y = 160, is_walking = 0, paths = {}},
		{id=10109, x=155, y=1078, rotation_y = 180, is_walking = 0, paths = {}},
		{id=10110, x=157, y=1019, rotation_y = 180, is_walking = 0, paths = {}},
		{id=10116, x=159, y=992, rotation_y = 0, is_walking = 0, paths = {}},
		{id=10119, x=157, y=987, rotation_y = 0, is_walking = 0, paths = {}},
		{id=10120, x=150, y=982, rotation_y = 0, is_walking = 0, paths = {}},
		{id=10121, x=185, y=1014, rotation_y = 0, is_walking = 0, paths = {}},
		{id=10123, x=183, y=1011, rotation_y = 0, is_walking = 0, paths = {}},
		{id=10126, x=136, y=1010, rotation_y = 0, is_walking = 0, paths = {}},
		{id=10127, x=131, y=1011, rotation_y = 0, is_walking = 0, paths = {}},
		{id=10129, x=173, y=988, rotation_y = 0, is_walking = 0, paths = {}},
		{id=10134, x=163, y=662, rotation_y = 180, is_walking = 0, paths = {}},
		{id=10135, x=188, y=394, rotation_y = 180, is_walking = 0, paths = {}},
		{id=10136, x=165, y=453, rotation_y = 180, is_walking = 0, paths = {}},
		{id=10158, x=78, y=947, rotation_y = 90, is_walking = 0, paths = {}},
		{id=10150, x=155, y=469, rotation_y = 160, is_walking = 0, paths = {}},
		{id=10151, x=167, y=438, rotation_y = 210, is_walking = 0, paths = {}},
		{id=10152, x=150, y=457, rotation_y = 99.99999, is_walking = 0, paths = {}},
		{id=10153, x=170, y=473, rotation_y = 305.9, is_walking = 0, paths = {}},
		{id=10155, x=146, y=445, rotation_y = 305.9, is_walking = 0, paths = {}},
		{id=10156, x=201, y=420, rotation_y = 305.9, is_walking = 0, paths = {}},
    },
    monsters = {
		{id=10101, x=199, y=131},
		{id=10101, x=196, y=126},
		{id=10101, x=203, y=128},
		{id=10101, x=203, y=131},
		{id=10101, x=199, y=128},
		{id=10101, x=207, y=126},
		{id=10101, x=207, y=130},
		{id=10101, x=196, y=130},
		{id=10104, x=153, y=985},
		{id=10104, x=152, y=990},
		{id=10104, x=158, y=996},
		{id=10104, x=165, y=989},
		{id=10104, x=160, y=990},
		{id=10104, x=166, y=992},
		{id=10107, x=170, y=984},
		{id=10105, x=160, y=588},
		{id=10105, x=165, y=588},
		{id=10105, x=168, y=586},
		{id=10105, x=157, y=583},
		{id=10105, x=157, y=586},
		{id=10105, x=168, y=583},
		{id=10105, x=160, y=585},
		{id=10105, x=165, y=585},
		{id=10103, x=157, y=719},
		{id=10103, x=156, y=714},
		{id=10103, x=173, y=719},
		{id=10103, x=162, y=717},
		{id=10103, x=162, y=722},
		{id=10103, x=168, y=717},
		{id=10103, x=167, y=722},
		{id=10103, x=173, y=715},
		{id=10108, x=192, y=350},
		{id=10108, x=196, y=350},
		{id=10108, x=189, y=348},
		{id=10108, x=198, y=348},
		{id=10108, x=192, y=347},
		{id=10108, x=196, y=347},
		{id=10108, x=189, y=346},
		{id=10108, x=198, y=346},
    },
    doors = {
		{id=1001, type=0, level=0, target_scene_id=1002, target_door_id=1002, offset={0, 0, 0}, rotation={0, 0, 0}, x=155, y=1099, door_target_x=41, door_target_y=218},
    },
    gathers = {
		{id=1004, x=179, y=386, disappear_after_gather=0},
		{id=1005, x=165, y=451, disappear_after_gather=0},
		{id=1006, x=201, y=155, disappear_after_gather=0},
		{id=1007, x=163, y=785, disappear_after_gather=0},
		{id=1803, x=80, y=947, disappear_after_gather=0},
    },
    jumppoints = {
		{id=0, target_id=1, range=2, x=201, y=178, jump_type=2, air_craft_id=0, is_show=1, jump_speed=2, jump_act=0,jump_tong_bu=0,jump_time=2,camera_fov=0,camera_rotation=0,offset={-2,0,0},play_cg=0,cgs={}},
		{id=1, target_id=-1, range=4, x=195, y=323, jump_type=2, air_craft_id=0, is_show=0, jump_speed=2, jump_act=0,jump_tong_bu=0,jump_time=2,camera_fov=0,camera_rotation=0,offset={0,0,0},play_cg=0,cgs={}},
		{id=2, target_id=3, range=2, x=196, y=304, jump_type=2, air_craft_id=0, is_show=1, jump_speed=2, jump_act=0,jump_tong_bu=0,jump_time=2,camera_fov=0,camera_rotation=0,offset={0,0,0},play_cg=0,cgs={}},
		{id=3, target_id=-1, range=4, x=200, y=167, jump_type=2, air_craft_id=0, is_show=0, jump_speed=2, jump_act=0,jump_tong_bu=0,jump_time=2,camera_fov=0,camera_rotation=0,offset={0,0,0},play_cg=0,cgs={}},
    },
    fences = {
    },
    effects = {
    },
    sounds = {
    },
    scene_way_points = {
		[0] = {id=0, target_id={1, },x=201, y=35},
		[1] = {id=1, target_id={0, 2, },x=202, y=148},
		[2] = {id=2, target_id={1, 3, },x=204, y=155},
		[3] = {id=3, target_id={2, 4, },x=207, y=165},
		[4] = {id=4, target_id={3, 5, },x=204, y=172},
		[5] = {id=5, target_id={4, 6, },x=201, y=176},
		[6] = {id=6, target_id={5, 7, },x=178, y=391},
		[7] = {id=7, target_id={6, 8, },x=162, y=424},
		[8] = {id=8, target_id={7, 9, },x=161, y=442},
		[9] = {id=9, target_id={8, 10, },x=163, y=572},
		[10] = {id=10, target_id={9, 11, },x=175, y=591},
		[11] = {id=11, target_id={10, 12, },x=183, y=604},
		[12] = {id=12, target_id={11, 13, },x=189, y=612},
		[13] = {id=13, target_id={12, 14, },x=192, y=623},
		[14] = {id=14, target_id={13, 15, },x=189, y=633},
		[15] = {id=15, target_id={14, 16, },x=182, y=644},
		[16] = {id=16, target_id={15, 17, },x=164, y=661},
		[17] = {id=17, target_id={16, 18, },x=164, y=883},
		[18] = {id=18, target_id={17, 19, },x=168, y=890},
		[19] = {id=19, target_id={18, 20, },x=190, y=890},
		[20] = {id=20, target_id={19, 21, },x=204, y=892},
		[21] = {id=21, target_id={20, 22, },x=217, y=901},
		[22] = {id=22, target_id={21, 23, },x=224, y=911},
		[23] = {id=23, target_id={24, },x=228, y=921},
		[24] = {id=24, target_id={23, 25, },x=231, y=949},
		[25] = {id=25, target_id={26, },x=230, y=973},
		[26] = {id=26, target_id={25, 27, },x=225, y=989},
		[27] = {id=27, target_id={26, 28, },x=215, y=999},
		[28] = {id=28, target_id={27, 29, },x=166, y=999},
		[29] = {id=29, target_id={28, 30, },x=158, y=1004},
		[30] = {id=30, target_id={29, },x=155, y=1102},
	},
    mask = "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",
}