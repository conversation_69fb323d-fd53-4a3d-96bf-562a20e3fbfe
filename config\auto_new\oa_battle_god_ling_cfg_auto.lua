-- Y-运营活动-战神令.xls
local item_table={
[1]={item_id=44053,num=1,is_bind=1},
[2]={item_id=44054,num=1,is_bind=1},
[3]={item_id=44055,num=1,is_bind=1},
[4]={item_id=27611,num=2,is_bind=1},
[5]={item_id=44049,num=1,is_bind=1},
[6]={item_id=39147,num=200,is_bind=1},
[7]={item_id=0,num=1,is_bind=0},
[8]={item_id=27611,num=6,is_bind=1},
[9]={item_id=39147,num=600,is_bind=1},
[10]={item_id=46501,num=2,is_bind=1},
[11]={item_id=44051,num=1,is_bind=1},
[12]={item_id=46048,num=2,is_bind=1},
[13]={item_id=44052,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
open_day={
{},
{start_day=8,end_day=9999,grade=2,}
},

open_day_meta_table_map={
},
rmb_buy={
{is_normal=1,},
{seq=1,rmb_seq=101,rmb_price=30,reward_item=item_table[1],},
{seq=2,rmb_seq=102,rmb_price=50,level=5,reward_item=item_table[2],},
{seq=3,rmb_seq=103,rmb_price=100,level=10,reward_item=item_table[3],},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,}
},

rmb_buy_meta_table_map={
[5]=1,	-- depth:1
[6]=2,	-- depth:1
[7]=3,	-- depth:1
[8]=4,	-- depth:1
},
level={
{reward_item_4=item_table[4],},
{level=2,reward_item_2=item_table[5],reward_item_3=item_table[6],reward_item_4=item_table[7],},
{level=3,reward_item_2=item_table[6],},
{level=4,reward_item_1=item_table[6],},
{level=5,reward_item_1=item_table[5],reward_item_3=item_table[5],reward_item_4=item_table[7],},
{level=6,reward_item_2=item_table[8],reward_item_4=item_table[7],},
{level=7,reward_item_4=item_table[8],},
{level=8,},
{level=9,reward_item_4=item_table[9],},
{level=10,reward_item_1=item_table[10],stage_reward=1,},
{level=11,reward_item_2=item_table[7],reward_item_3=item_table[7],},
{level=12,reward_item_4=item_table[6],},
{level=13,reward_item_2=item_table[7],},
{level=14,reward_item_4=item_table[4],},
{level=15,reward_item_2=item_table[5],},
{level=16,reward_item_4=item_table[6],},
{level=17,},
{level=18,reward_item_1=item_table[7],reward_item_3=item_table[6],reward_item_4=item_table[4],},
{level=19,reward_item_2=item_table[8],},
{level=20,reward_item_3=item_table[10],stage_reward=1,},
{level=21,reward_item_2=item_table[9],},
{level=22,reward_item_4=item_table[9],},
{level=23,reward_item_1=item_table[6],reward_item_2=item_table[10],},
{level=24,reward_item_3=item_table[6],},
{level=25,reward_item_1=item_table[11],reward_item_3=item_table[11],reward_item_4=item_table[6],},
{level=26,reward_item_2=item_table[6],},
{level=27,reward_item_2=item_table[6],reward_item_3=item_table[7],reward_item_4=item_table[4],},
{level=28,reward_item_2=item_table[11],},
{level=29,reward_item_4=item_table[7],},
{level=30,stage_reward=1,},
{level=31,reward_item_4=item_table[7],},
{level=32,reward_item_1=item_table[7],},
{level=33,reward_item_2=item_table[10],},
{level=34,reward_item_4=item_table[8],},
{level=35,reward_item_2=item_table[8],reward_item_4=item_table[8],},
{level=36,},
{level=37,reward_item_4=item_table[9],},
{level=38,reward_item_2=item_table[7],},
{level=39,reward_item_2=item_table[7],},
{level=40,reward_item_4=item_table[6],stage_reward=1,},
{level=41,},
{level=42,},
{level=43,},
{level=44,},
{level=45,},
{level=46,},
{level=47,reward_item_2=item_table[8],},
{level=48,},
{level=49,},
{level=50,reward_item_1=item_table[7],reward_item_4=item_table[9],},
{level=51,reward_item_2=item_table[10],},
{level=52,},
{level=53,reward_item_4=item_table[6],},
{level=54,reward_item_2=item_table[6],},
{level=55,reward_item_1=item_table[11],reward_item_3=item_table[11],},
{level=56,reward_item_2=item_table[11],},
{level=57,reward_item_3=item_table[7],reward_item_4=item_table[8],},
{level=58,reward_item_1=item_table[6],reward_item_3=item_table[7],},
{level=59,reward_item_3=item_table[7],},
{level=60,reward_item_1=item_table[10],reward_item_2=item_table[6],reward_item_3=item_table[10],},
{level=61,},
{level=62,reward_item_1=item_table[7],reward_item_4=item_table[8],},
{level=63,},
{level=64,reward_item_3=item_table[6],},
{level=65,reward_item_1=item_table[7],reward_item_3=item_table[11],},
{level=66,},
{level=67,},
{level=68,reward_item_2=item_table[6],},
{level=69,},
{level=70,reward_item_1=item_table[10],reward_item_3=item_table[10],reward_item_4=item_table[7],},
{level=71,},
{level=72,},
{level=73,reward_item_2=item_table[5],},
{level=74,reward_item_4=item_table[6],},
{level=75,reward_item_1=item_table[11],reward_item_2=item_table[7],reward_item_3=item_table[11],},
{level=76,reward_item_2=item_table[7],},
{level=77,reward_item_2=item_table[7],},
{level=78,reward_item_4=item_table[8],},
{level=79,reward_item_1=item_table[6],},
{level=80,reward_item_2=item_table[9],reward_item_4=item_table[9],},
{level=81,},
{level=82,reward_item_2=item_table[5],},
{level=83,},
{level=84,},
{level=85,reward_item_2=item_table[6],},
{level=86,},
{level=87,},
{level=88,reward_item_1=item_table[6],},
{level=89,},
{level=90,reward_item_1=item_table[12],reward_item_3=item_table[12],stage_reward=1,},
{grade=2,},
{grade=2,},
{grade=2,},
{level=4,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{level=9,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{level=15,},
{grade=2,},
{level=17,},
{grade=2,},
{level=19,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{level=36,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{level=41,},
{level=42,},
{level=43,},
{level=44,},
{grade=2,},
{level=46,},
{grade=2,},
{level=48,},
{level=49,},
{grade=2,},
{level=51,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{level=67,},
{grade=2,},
{grade=2,},
{grade=2,},
{level=71,},
{level=72,},
{grade=2,},
{level=74,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{level=81,},
{grade=2,},
{grade=2,},
{level=84,},
{grade=2,},
{level=86,},
{level=87,},
{grade=2,},
{level=89,},
{grade=2,}
},

level_meta_table_map={
[81]=1,	-- depth:1
[86]=82,	-- depth:1
[87]=81,	-- depth:2
[91]=1,	-- depth:1
[46]=87,	-- depth:3
[42]=46,	-- depth:4
[41]=86,	-- depth:2
[66]=42,	-- depth:5
[17]=41,	-- depth:3
[67]=17,	-- depth:4
[142]=52,	-- depth:1
[156]=66,	-- depth:6
[7]=6,	-- depth:1
[172]=82,	-- depth:1
[171]=156,	-- depth:7
[136]=171,	-- depth:8
[157]=172,	-- depth:2
[176]=157,	-- depth:3
[77]=7,	-- depth:2
[76]=81,	-- depth:2
[37]=76,	-- depth:3
[36]=37,	-- depth:4
[32]=26,	-- depth:1
[22]=26,	-- depth:1
[16]=26,	-- depth:1
[131]=176,	-- depth:4
[71]=6,	-- depth:1
[177]=136,	-- depth:9
[21]=22,	-- depth:2
[107]=131,	-- depth:5
[72]=7,	-- depth:2
[51]=81,	-- depth:2
[132]=177,	-- depth:10
[116]=26,	-- depth:1
[61]=51,	-- depth:3
[24]=58,	-- depth:1
[148]=58,	-- depth:1
[147]=57,	-- depth:1
[73]=24,	-- depth:2
[152]=62,	-- depth:1
[151]=61,	-- depth:4
[127]=37,	-- depth:4
[141]=151,	-- depth:5
[96]=6,	-- depth:1
[97]=7,	-- depth:2
[167]=77,	-- depth:3
[101]=11,	-- depth:1
[106]=16,	-- depth:2
[111]=21,	-- depth:3
[112]=22,	-- depth:2
[114]=24,	-- depth:2
[162]=97,	-- depth:3
[161]=96,	-- depth:2
[122]=32,	-- depth:2
[126]=127,	-- depth:5
[84]=73,	-- depth:3
[166]=76,	-- depth:3
[29]=24,	-- depth:2
[47]=62,	-- depth:1
[15]=75,	-- depth:1
[39]=24,	-- depth:2
[13]=58,	-- depth:1
[12]=11,	-- depth:1
[53]=24,	-- depth:2
[45]=15,	-- depth:2
[54]=24,	-- depth:2
[34]=18,	-- depth:1
[56]=57,	-- depth:1
[31]=32,	-- depth:2
[43]=84,	-- depth:4
[119]=29,	-- depth:3
[23]=18,	-- depth:1
[121]=31,	-- depth:3
[115]=25,	-- depth:1
[19]=34,	-- depth:2
[28]=29,	-- depth:3
[117]=27,	-- depth:1
[129]=39,	-- depth:3
[124]=34,	-- depth:2
[163]=73,	-- depth:3
[14]=13,	-- depth:2
[133]=163,	-- depth:4
[135]=45,	-- depth:3
[9]=13,	-- depth:2
[137]=47,	-- depth:2
[8]=9,	-- depth:3
[143]=53,	-- depth:3
[144]=54,	-- depth:3
[4]=2,	-- depth:1
[146]=56,	-- depth:2
[3]=5,	-- depth:1
[160]=70,	-- depth:1
[69]=4,	-- depth:2
[165]=75,	-- depth:1
[48]=19,	-- depth:3
[65]=21,	-- depth:3
[64]=65,	-- depth:4
[63]=48,	-- depth:4
[74]=54,	-- depth:3
[59]=74,	-- depth:4
[55]=27,	-- depth:1
[78]=39,	-- depth:3
[79]=64,	-- depth:5
[174]=133,	-- depth:5
[83]=74,	-- depth:4
[85]=25,	-- depth:1
[49]=64,	-- depth:5
[30]=70,	-- depth:1
[88]=19,	-- depth:3
[89]=88,	-- depth:4
[150]=60,	-- depth:1
[92]=2,	-- depth:1
[68]=4,	-- depth:2
[44]=83,	-- depth:5
[95]=5,	-- depth:1
[108]=18,	-- depth:1
[33]=18,	-- depth:1
[105]=135,	-- depth:4
[103]=13,	-- depth:2
[102]=12,	-- depth:2
[35]=25,	-- depth:1
[38]=23,	-- depth:2
[173]=83,	-- depth:5
[154]=64,	-- depth:5
[158]=68,	-- depth:3
[155]=65,	-- depth:4
[168]=78,	-- depth:4
[169]=79,	-- depth:6
[175]=85,	-- depth:2
[178]=88,	-- depth:4
[159]=69,	-- depth:3
[153]=63,	-- depth:5
[164]=173,	-- depth:6
[90]=21,	-- depth:3
[145]=55,	-- depth:2
[10]=14,	-- depth:3
[20]=47,	-- depth:2
[40]=60,	-- depth:1
[50]=40,	-- depth:2
[80]=40,	-- depth:2
[179]=178,	-- depth:5
[93]=3,	-- depth:2
[94]=159,	-- depth:4
[98]=8,	-- depth:4
[99]=98,	-- depth:5
[149]=59,	-- depth:5
[109]=153,	-- depth:6
[104]=14,	-- depth:3
[113]=23,	-- depth:2
[118]=28,	-- depth:4
[120]=30,	-- depth:2
[123]=33,	-- depth:2
[125]=35,	-- depth:2
[128]=38,	-- depth:3
[134]=164,	-- depth:7
[138]=109,	-- depth:7
[139]=154,	-- depth:6
[100]=10,	-- depth:4
[170]=80,	-- depth:3
[130]=40,	-- depth:2
[140]=50,	-- depth:3
[110]=20,	-- depth:3
[180]=90,	-- depth:4
},
task_list={
{task_type=1,param1=1,task_description="每日登录",},
{task_id=1,task_type=3,task_description="击杀神秘海域boss%s次",},
{task_id=2,task_type=4,task_weight=100,task_description="击杀蛮荒魔谷boss%s次",},
{task_id=3,task_type=5,task_weight=100,task_description="击杀心魔牢狱boss%s次",},
{task_id=4,task_type=6,task_weight=100,task_description="击杀混沌魔域boss%s次",},
{task_id=5,task_weight=100,},
{task_id=6,task_type=8,task_weight=100,task_description="击杀天魔深渊boss%s次",},
{task_id=7,task_type=9,task_weight=100,task_description="击杀九幽魔境boss%s次",},
{task_id=8,task_type=10,task_weight=100,task_description="击杀异界空间boss%s次",},
{task_id=9,task_type=11,task_weight=100,task_description="击杀星图秘境boss%s次",},
{task_id=10,task_type=12,param1=1,task_description="完成所有任务",},
{task_id=11,task_type=2,param1=7,task_description="累计登录%s次",},
{task_id=12,task_type=8,is_week_task=1,task_description="击杀天魔深渊boss%s次",},
{task_id=13,task_type=9,is_week_task=1,task_description="击杀九幽魔境boss%s次",},
{task_id=14,task_type=10,is_week_task=1,task_description="击杀异界空间boss%s次",},
{task_id=15,task_type=11,task_description="击杀星图秘境boss%s次",},
{task_id=16,is_week_task=1,},
{task_id=17,task_type=13,param1=1,task_description="完成所有任务",},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,}
},

task_list_meta_table_map={
[35]=17,	-- depth:1
[24]=6,	-- depth:1
[20]=2,	-- depth:1
[19]=1,	-- depth:1
[26]=8,	-- depth:1
[33]=15,	-- depth:1
[31]=13,	-- depth:1
[28]=10,	-- depth:1
[27]=9,	-- depth:1
[32]=14,	-- depth:1
[18]=13,	-- depth:1
[23]=5,	-- depth:1
[22]=4,	-- depth:1
[21]=3,	-- depth:1
[16]=18,	-- depth:2
[12]=13,	-- depth:1
[11]=7,	-- depth:1
[25]=7,	-- depth:1
[29]=11,	-- depth:2
[30]=12,	-- depth:2
[34]=16,	-- depth:3
[36]=18,	-- depth:2
},
other_default_table={refresh_task_cost=50,refresh_daily_task_times=5,fix_up_level_exp=100,exp_item=46047,daily_list_num=4,},

open_day_default_table={start_day=1,end_day=7,grade=1,},

rmb_buy_default_table={grade=1,seq=0,rmb_type=144,rmb_seq=100,rmb_price=0,level=0,is_normal=0,reward_item=item_table[13],},

level_default_table={grade=1,level=1,up_level_exp=100,reward_item_1=item_table[4],reward_item_2=item_table[4],reward_item_3=item_table[4],reward_item_4=item_table[5],stage_reward=0,},

task_list_default_table={grade=1,task_id=0,task_type=7,param1=2,task_weight=10000,task_exp=100,is_week_task=0,task_description="击杀神魔之井boss%s次",}

}

