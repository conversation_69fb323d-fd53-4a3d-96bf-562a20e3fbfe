----serveractivity_tab_view的配置文件

ServerActivityTabCfg = ServerActivityTabCfg or {
    {
        key = "kf_act",
        table_index = TabIndex.act_bipin,
        name = Language.OpenServer.Competition,-- 开服比拼 113（这是客户端自己定义的）
        actid = ACTIVITY_TYPE.OPENSERVER_COMPETITION,
        uniquekey = "open_server_competition", --唯一入口key用于唯一的入口（传闻等）
        remind_name = RemindName.KFCompetition,
        show_redpoint_func = function() --是否显示红点
            return ServerActivityWGData.Instance:GetOpenserverCommpetionRed()
        end,
        can_open_func = function() --是否可以打开
            return ServerActivityWGData.Instance:ActIsOpenByActId(ACTIVITY_TYPE.OPENSERVER_COMPETITION, true)
        end
    },

    -------------------------------------------------------------------------------------------------------------
    
    -- 开服礼包
    {
        key = "open_server", --入口key
        table_index = TabIndex.act_kf_gift,
        name = Language.OpenServer.KfGiftName,
        actid = -1,
        remind_name = "",
        show_redpoint_func = function() --是否显示红点
            return ServerActivityWGData.Instance:GetActKfGiftRemind()
        end,
        can_open_func = function() --是否可以打开
            return ServerActivityWGData.Instance:ActKfGiftIsOpen()
        end
    },

    {
        key = "open_server", --入口key
        table_index = TabIndex.act_xianmeng_fengbang,
        name = Language.OpenServer.ZuiqiangXianmeng,   -- 仙盟封榜
        actid = ACTIVITY_TYPE.OPENSERVER_KAIZONGLIPAI,
        uniquekey = "xian_meng_feng_bang",
        remind_name = RemindName.KFXianMengFengBang,
        show_redpoint_func = function() --是否显示红点
            return ServerActivityWGData.Instance:GetKaiZongLiPaiRemind()
        end,
        can_open_func = function() --是否可以打开
            return false-- ServerActivityWGData.Instance:ActIsOpenByActId(ACTIVITY_TYPE.OPENSERVER_KAIZONGLIPAI, true)
        end
    },

    -- 开宗立派
    {
        key = "open_server", --入口key
        table_index = TabIndex.act_kaizonglipai,
        name = Language.OpenServer.KZLP_Name,
        actid = ServerActClientId.OPEN_INNEGLECTABLE,
        remind_name = RemindName.KFKaiZongLiPai,
        show_redpoint_func = function() --是否显示红点
            return ServerActivityWGData.Instance:GetCreateGuildRemind()
        end,
        can_open_func = function() --是否可以打开
            return ServerActivityWGData.Instance:ActIsOpenByActId(ServerActClientId.OPEN_INNEGLECTABLE, true)
        end
    },

    -- boss猎人
    {
        key = "open_server", --入口key
        table_index = TabIndex.act_boss_lieren,
        name = Language.OpenServer.BossHunter,
        actid = ACTIVITY_TYPE.BOSS_LIEREN,
        uniquekey = "boss_lieren",
        remind_name = RemindName.KFBossHunter,
        show_redpoint_func = function() --是否显示红点
            return 0
        end,
        can_open_func = function() --是否可以打开
            return false--ServerActivityWGData.Instance:ActIsOpenByActId(ACTIVITY_TYPE.BOSS_LIEREN, true)
        end
    },

    -- 仙盟争霸
    -- {
    --     key = "open_server", --入口key
    --     table_index = TabIndex.act_xianmeng_zhengba,
    --     name = Language.OpenServer.GuildContendtitionView,
    --     actid = ACTIVITY_TYPE.GUILD_ZHENBA,
    --     remind_name = RemindName.KFXianMengZhengBa,
    --     show_redpoint_func = function() --是否显示红点
    --         return ServerActivityWGData.Instance:GetGuildContentRemind()
    --     end,
    --     can_open_func = function() --是否可以打开
    --         return ServerActivityWGData.Instance:ActIsOpenByActId(ACTIVITY_TYPE.GUILD_ZHENBA, true)
    --     end
    -- },

    {
        key = "open_server", --入口key
        table_index = TabIndex.act_kf_jizi,
        name = Language.ActivePlate.OpenServerJiZi,--开服集字  2123
        actid = ACTIVITY_TYPE.RAND_COLLECT_ITEMS,
        remind_name = RemindName.KaiFuJiZiRed,
        show_redpoint_func = function() --是否显示红点
            return ServerActivityWGData.Instance:RemindKiFuJiZiRed()
        end,
        can_open_func = function() --是否可以打开
            return ServerActivityWGData.Instance:ActIsOpenByActId(ACTIVITY_TYPE.RAND_COLLECT_ITEMS, true)
        end
    },

    -- 比翼双飞（整合完美情人和全场热恋）
    {
        key = "open_server", --入口key
        table_index = TabIndex.act_lovers, --act_kf_bysf
        name = Language.OpenServer.OpenServerLovers,
        actid = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY,
        remind_name = RemindName.KFLovers,
        show_redpoint_func = function() --是否显示红点
            return 0
        end,
        can_open_func = function() --是否可以打开
            return ServerActivityWGData.Instance:ActIsOpenByActId(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY, true)
        end
    },
    -- 比翼双飞（整合完美情人和全场热恋）
    {
        key = "open_server", --入口key
        table_index = TabIndex.city_love,  --act_kf_bysf
        name = Language.OpenServer.OpenServerLovers,
        actid = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY,
        remind_name = RemindName.KFCityLove,
        show_redpoint_func = function() --是否显示红点
            return ActivePerfertQingrenWGData.Instance:GetCityLoveRemind()
        end,
        can_open_func = function() --是否可以打开
            return ServerActivityWGData.Instance:ActIsOpenByActId(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY, true)
        end
    },

    -- 开服嗨点
    {
        key = "open_server", --入口key
        table_index = TabIndex.act_highpoint,
        name = Language.OpenServer.OpenServerHighPoint,
        actid = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CRAZY_HIGH_POINT,
        remind_name = RemindName.OSA_High_Point,
        show_redpoint_func = function() --是否显示红点
            return 0
        end,
        can_open_func = function() --是否可以打开
            return OpenServerAssistWGData.Instance:GetRandActivityCanShowByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CRAZY_HIGH_POINT)
        end
    },

        -- 七日累充
    {
        key = "open_server",
        table_index = TabIndex.act_sevenday_recharge,
        name = Language.OpenServer.SevenDayRecharge,
        actid = ACTIVITY_TYPE.SEVENDAY_RECHARGE,
        uniquekey = "seven_day_recharge",
        remind_name = RemindName.KFSevendayRecharge,
        show_redpoint_func = function() --是否显示红点
            return ServerActivityWGData.Instance:GetTotalChongzhiRemind()
        end,
        can_open_func = function() --是否可以打开
            return ServerActivityWGData.Instance:ActIsOpenByActId(ACTIVITY_TYPE.SEVENDAY_RECHARGE, true)
        end
    },

    -- 开服集卡
    {
        key = "open_server",
        table_index = TabIndex.server_collect_card,
        name = Language.OpenServer.SevenCollectCard,
        actid = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_COLLECT_CARD,
        uniquekey = "seven_collect_card",
        remind_name = RemindName.CollectCardRed,
        show_redpoint_func = function() --是否显示红点
            return ActivityCollectCardWGData.Instance:GetActCollectCardRemind()
        end,
        can_open_func = function() --是否可以打开
            return ServerActivityWGData.Instance:ActIsOpenByActId(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_COLLECT_CARD, true)
        end
    },
    
}
