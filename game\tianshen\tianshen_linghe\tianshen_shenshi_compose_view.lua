-------------------------------------
-- 已屏蔽
-- 诸神-神饰合成
-------------------------------------
TianShenShenShiComposeView = TianShenShenShiComposeView or BaseClass(SafeBaseView)

function TianShenShenShiComposeView:__init()
    self:SetMaskBg()
	self.view_layer = UiLayer.Normal
    self.view_cache_time = 0

    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",{vector2 = Vector2(0, 0), sizeDelta = Vector2(706, 486)})
	self:AddViewResource(0, "uis/view/tianshen_linghe_ui_prefab", "layout_shenshi_compose_view")
end

function TianShenShenShiComposeView:Load<PERSON>allBack()
    self.node_list.title_view_name.text.text = Language.TianShenLingHe.ComposeViewName
	self.left_linghe_cell = BaseLingHeCell.New(nil, self.node_list.ph_cell_1)
	self.right_linghe_cell = BaseLingHeCell.New(nil, self.node_list.ph_cell_2)
    self.have_equip_list_view = AsyncListView.New(TSEquipBlockRender, self.node_list["have_equip_list"])
    XUI.AddClickEventListener(self.node_list.btn_upgrade, BindTool.Bind(self.OnClickCPCompose, self, false))
end

function TianShenShenShiComposeView:ReleaseCallBack()
	if self.left_linghe_cell then
		self.left_linghe_cell:DeleteMe()
		self.left_linghe_cell = nil
	end

	if self.right_linghe_cell then
		self.right_linghe_cell:DeleteMe()
		self.right_linghe_cell = nil
	end

	if self.have_equip_list_view then
		self.have_equip_list_view:DeleteMe()
		self.have_equip_list_view = nil
	end

	self.enough_price = nil
	self.still_need_price = nil
	self.need_stone_desc = nil

    self.product_id = nil
end

function TianShenShenShiComposeView:OnFlush()
    if not self.product_id then return end
    local target_cfg = TianShenLingHeWGData.Instance:GetItemComposeCfg(self.product_id)
    local stuff_id = target_cfg.stuff_id
    local target_id = target_cfg.product_id

    self:SetLeftCellData(stuff_id)
    self:SetRightCellData(target_id)

    local stuff_had_num = TianShenLingHeWGData.Instance:GetItemNum(stuff_id)
	if self.have_equip_list_view then
        local data_list = { { item_id = stuff_id, num = stuff_had_num } }
		self.have_equip_list_view:SetDataList(data_list)
		self.have_equip_list_view:CancelSelect()
	end
end

function TianShenShenShiComposeView:SetLeftCellData(item_id)
	self.node_list.lbl_name_1.text.text = ItemWGData.Instance:GetItemName(item_id, nil, true)
    local attr_des = DeleteStrSpace(ItemShowWGData.Instance:OnlyGetAttrDesc(item_id, COLOR3B.DEFAULT, COLOR3B.DEFAULT_NUM))
    local new_str = string.gsub(attr_des, "><","> <")
	self.node_list.lbl_attr_1.text.text = new_str
	self.left_linghe_cell:SetData({ item_id = item_id })
end

function TianShenShenShiComposeView:SetRightCellData(item_id)
    self.node_list.lbl_name_2.text.text = ItemWGData.Instance:GetItemName(item_id, nil, true)
    local attr_des = DeleteStrSpace(ItemShowWGData.Instance:OnlyGetAttrDesc(item_id, COLOR3B.DEFAULT, COLOR3B.DEFAULT_NUM))
    local new_str = string.gsub(attr_des, "><","> <")
	self.node_list.lbl_attr_2.text.text = new_str
	self.right_linghe_cell:SetData({ item_id = item_id })
end

-- 设置要合成的目标物品
function TianShenShenShiComposeView:SetComposeItem(item_index)
    self.product_id = item_index
end

-- 合成成功特效
function TianShenShenShiComposeView:ShowComposeSucessEffect()
	if self.node_list["cp_success_root"] then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_hecheng, is_success = true,
                                        pos = Vector2(0, 0), parent_node = self.node_list["cp_success_root"]})

		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
	end
end

-- 点击合成
function TianShenShenShiComposeView:OnClickCPCompose(quick_compose)
    local target_cfg = TianShenLingHeWGData.Instance:GetItemComposeCfg(self.product_id)
    if target_cfg == nil then return end
    local stuff_id = target_cfg.stuff_id

    local had_num = TianShenLingHeWGData.Instance:GetItemNum(stuff_id)
    if had_num >= target_cfg.stuff_count then
        self:ShowComposeSucessEffect()
    end

    if quick_compose then
        TianShenLingHeWGCtrl.Instance:SendOperateReq(TianShenLingHeWGData.OPERA_TYPE.QUICKCOMPOSE, self.product_id)
    else
        TianShenLingHeWGCtrl.Instance:SendOperateReq(TianShenLingHeWGData.OPERA_TYPE.COMPOSE, self.product_id)
    end
end


-------------------------------------
-- 天神神饰装备ItemRender
-------------------------------------
TSEquipBlockRender = TSEquipBlockRender or BaseClass(BaseRender)
function TSEquipBlockRender:__init()
	self.show_item = ItemCell.New(self.node_list.item_node)
    self.show_item:SetHideRightDownBgLessNum(-1)
end

function TSEquipBlockRender:__delete()
	if self.show_item then
		self.show_item:DeleteMe()
		self.show_item = nil
	end
end

function TSEquipBlockRender:OnFlush()
	self.show_item:SetData(self.data)
end