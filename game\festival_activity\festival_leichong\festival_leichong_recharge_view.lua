--动态运营活动 限时累充
function FestivalActivityView:InitLeiChongView()
	self.leichong_reward_list = nil
end

function FestivalActivityView:LoadIndexCallBackLeiChongView()
	self:DTYYFlushLeiChongView()
	-- self:DTYYLeiChongLoadImage()
	self:SetLeiChongImg()

	--[[if self.box_tween == nil then
		local tween_root = self.node_list["leichong_box"].rect
		self.box_tween = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 25, 1)
		self.box_tween:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	end--]]
end

function FestivalActivityView:SetLeiChongImg()
  	local leichong_box_bundle, leichong_box_asset = ResPath.GetFestivalRawImages("xslc")
 	self.node_list["leichong_box"].raw_image:LoadSprite(leichong_box_bundle, leichong_box_asset, function ()
 		self.node_list["leichong_box"].raw_image:SetNativeSize()
  	end)

 	local leichong_icon_bundle, leichong_icon_asset = ResPath.GetFestivalActImages("a2_jrkh_bx")
 	self.node_list["leichong_icon"].image:LoadSprite(leichong_icon_bundle, leichong_icon_asset, function ()
 		self.node_list["leichong_icon"].image:SetNativeSize()
  	end)
 	
	local leichong_list_bg_bundle, leichong_list_bg_asset = ResPath.GetFestivalActImages("a2_jrkh_di1")
 	self.node_list["leichong_list_bg"].image:LoadSprite(leichong_list_bg_bundle, leichong_list_bg_asset)
end

function FestivalActivityView:DTYYReleaseLeiChongView()
	if self.leichong_reward_list then
		self.leichong_reward_list:DeleteMe()
		self.leichong_reward_list = nil
    end
	if self.box_tween then
		self.box_tween:Kill()
		self.box_tween = nil
	end
    if self.leichong_zhanshi_list then
        for k,v in pairs(self.leichong_zhanshi_list) do
            v:DeleteMe()
        end
		self.leichong_zhanshi_list = nil
    end
    if CountDownManager.Instance:HasCountDown("LeiChongCountDown") then
        CountDownManager.Instance:RemoveCountDown("LeiChongCountDown")
    end
end

function FestivalActivityView:DTYYLeiChongLoadImage()
	local model_cfg = FestivalLeiChongRechargeWGData.Instance:GetModelInfo()
	if model_cfg == nil then
		return
    end
	self:FlushFashionModelLeiChong(model_cfg)
end

function FestivalActivityView:DTYYFlushLeiChongView()
	 --刷新累充list
	self:DTYYFlushLeiChongList()
	local cur_xianyu = FestivalLeiChongRechargeWGData.Instance:GetOwnXianYu()
    --获取当前档需要充值的仙玉
	local need_value, cur_stage_value ,pre_stage_value = FestivalLeiChongRechargeWGData.Instance:GetNeedRechargeXianYU()
	local slide_percent = 0

	if need_value == 0 then
		slide_percent = 1
	else
		local cur_stage_value_pre = cur_stage_value > 0 and cur_stage_value or 1
		slide_percent = cur_xianyu / cur_stage_value_pre
	end
	
	--self.node_list["total_recharge_value"].text.text = need_value
	--self.node_list["total_slide_value"].text.text = string.format("%d/%d",cur_xianyu, cur_stage_value)
	--self.node_list["total_slide"].slider.value = slide_percent 
	-- self.node_list["total_finish"]:SetActive(FestivalLeiChongRechargeWGData.Instance:IsRechargeTargetFinish())
	-- self.node_list["total_leichong"]:SetActive(not FestivalLeiChongRechargeWGData.Instance:IsRechargeTargetFinish())
end

function FestivalActivityView:OpenIndexCallBackLeiChong()
	FestivalLeiChongRechargeWGCtrl.Instance:SendLeiChongRechargeReq(FESTIVAL_XIANSHILEICHONG_TYPE.TYPE_INFO)
end

function FestivalActivityView:FlushFashionModelLeiChong(param_cfg)
	if IsEmptyTable(param_cfg) then
		return
	end
	local data = {}
	data.reward_show = param_cfg.reward_item
    if param_cfg.reward_position and param_cfg.reward_position ~= nil then
		local show_pos_str_list = Split(param_cfg.reward_position, "#")
		data.reward_show_pos = {}
		for i, pos_str in ipairs(show_pos_str_list) do
			local pos = Split(pos_str, "|")
			data.reward_show_pos[i] = Vector2(tonumber(pos[1]), tonumber(pos[2]))
        end
    end
    
    if not self.leichong_zhanshi_list then
        self.leichong_zhanshi_list = {}
        for i = 1, 4 do
            if self.node_list["merge_leichong_cell_"..i] then
                self.leichong_zhanshi_list[i] = ItemCell.New(self.node_list["merge_leichong_cell_"..i])
            end
        end
    end
    local idx = 1
    if self.leichong_zhanshi_list then
        for k, v in pairs(data.reward_show) do
            if self.leichong_zhanshi_list[idx] then
                self.leichong_zhanshi_list[idx]:SetData(v)
                self.node_list["merge_leichong_cell_" .. idx].rect.anchoredPosition = data.reward_show_pos[idx]
                idx = idx + 1
            end
        end
    end
    if self.need_leichong_tween then
        UITween.CleanAlphaShow(GuideModuleName.FestivalActivityView)
        self.need_leichong_tween = false
        for i = 1, #data.reward_show + 1 do --配置是从0开始的
            UITween.FakeHideShow(self.node_list["merge_leichong_cell_"..i])
        end
        for i = 1, #data.reward_show + 1 do
            ReDelayCall(self, function()
                if self.node_list and self.node_list["merge_leichong_cell_"..i] then
                    UITween.AlphaShow(GuideModuleName.FestivalActivityView, self.node_list["merge_leichong_cell_"..i], 0.3, 1, 0.3)
                    local end_position = self.node_list["merge_leichong_cell_"..i].transform.anchoredPosition
                    self.node_list["merge_leichong_cell_"..i].transform.anchoredPosition = Vector2(0, 0)
                    self.node_list["merge_leichong_cell_"..i].transform:DOAnchorPos(end_position, 0.3)
                end
            end,  0.2 * i , "merge_leichong_cell_" .. i)
        end
    end
end

function FestivalActivityView:DTYYFlushLeiChongList()
	if not self.leichong_reward_list then
		self.leichong_reward_list = AsyncListView.New(FestivalLeiChongRender, self.node_list["total_reward_list"])
	end

	local list = FestivalLeiChongRechargeWGData.Instance:GetLeiChongRewardList()
    self.leichong_reward_list:SetDataList(list)
    self.leichong_reward_list:SetIsDelayFlush(false)
end

function FestivalActivityView:OnFlushLeiChongTipsAndRule()
    local cfg = FestivalLeiChongRechargeWGData.Instance:GetModelInfo()
	self:SetRuleInfo(cfg.activity_des, Language.MergeLeiChongRecharge.TipsActivityHint)
    self:SetOutsideRuleTips(Language.MergeLeiChongRecharge.LeiChongRechargeState)
    self.need_leichong_tween = true
    -- self:DTYYLeiChongLoadImage()
    -- self:SetLeiChongCountDown()
end

-- function FestivalActivityView:SetLeiChongCountDown(index, end_time)
-- 	local activity_type = FestivalActivityWGData.Instance:GetCurSelectActivityType(TabIndex.festival_activity_2263)
-- 	local act_end_time = FestivalActivityWGData.Instance:GetActivityInValidTime(activity_type)

-- 	if CountDownManager.Instance:HasCountDown("LeiChongCountDown") then
--         CountDownManager.Instance:RemoveCountDown("LeiChongCountDown")
--     end

--     if act_end_time ~= 0 then
-- 		-- self.node_list.merge_leichong_time_txt:SetActive(true)
--         local time = act_end_time - TimeWGCtrl.Instance:GetServerTime()
--         if time > 0 then
--             CountDownManager.Instance:AddCountDown("LeiChongCountDown",
--                     BindTool.Bind(self.LeichongCommonUpdateTime, self),
--                     BindTool.Bind(self.LeichongCommonCompleteTime, self), nil, time, 1)
--         end
--     end
-- end

-- function FestivalActivityView:LeichongCommonUpdateTime(elapse_time, total_time)
-- 	local temp_seconds = GameMath.Round(total_time - elapse_time)
--     local str = TimeUtil.FormatDToHAndMS(temp_seconds)
--     if self.node_list and self.node_list.merge_leichong_time_txt then
--         self.node_list.merge_leichong_time_txt.text.text = str
--     end
-- end

-- function FestivalActivityView:LeichongCommonCompleteTime(elapse_time, total_time)
--     if self.node_list and self.node_list.merge_leichong_time_txt then
--         self.node_list.merge_leichong_time_txt.text.text = "00:00:00"
--     end
-- end


----------------------------- FestivalLeiChongRender ----------------------
FestivalLeiChongRender = FestivalLeiChongRender or BaseClass(BaseRender)
function FestivalLeiChongRender:__init()
	
end

function FestivalLeiChongRender:LoadCallBack()
	self.item_list = {}
	self.btn_lingqu = self.node_list["btn_lingqu"]
	self.btn_recharge = self.node_list["btn_recharge"]
	self.btn_lingqu.button:AddClickListener(BindTool.Bind1(self.OnClickRewardHnadler, self))
    self.btn_recharge.button:AddClickListener(BindTool.Bind1(self.OnClickRechargeHnadler, self))
   
	self.item_list_view = AsyncListView.New(ItemCell,self.node_list["item_list"])

	self.node_list["btn_lingqu"].image:LoadSprite(ResPath.GetFestivalActImages("a2_jrkh_btn_huang"))
	local leichong_item_left_bundle, leichong_item_left_asset = ResPath.GetFestivalActImages("a2_jrkh_lbbt")
    self.node_list["left_item_bg"].image:LoadSprite(leichong_item_left_bundle, leichong_item_left_asset)

	
	local leichong_item_bg_bundle, leichong_item_bg_asset = ResPath.GetFestivalActImages("a2_jrkh_lb")
    self.node_list["item_bg"].image:LoadSprite(leichong_item_bg_bundle, leichong_item_bg_asset)

    local model_cfg = FestivalLeiChongRechargeWGData.Instance:GetModelInfo()
	if model_cfg == nil then
		return
    end
    local text_color = model_cfg.text_color
    self.node_list.recharge_tip.text.color = Str2C3b(text_color) 
end

function FestivalLeiChongRender:ReleaseCallBack()
	if self.item_list then
		for k,v in pairs(self.item_list) do
			v:DeleteMe()
		end
		self.item_list = nil
		self.btn_lingqu = nil
		self.btn_recharge = nil
	end

	if self.item_list_view then
		self.item_list_view:DeleteMe()
		self.item_list_view = nil
    end
end

function FestivalLeiChongRender:OnFlush()
	if not self.data then
		return
	end 

	--是否显示返利
	--local is_show = self.data.cfg.special_frame == SPECIAL_FRAME.CAN_SHOW
	--self.node_list["total_fanlidi"]:SetActive(is_show)
	--self.node_list["jinshukuang"]:SetActive(is_show)
	--self.node_list["biaoqian"]:SetActive(is_show)
	-- if is_show then
	-- 	--self.node_list["total_fanli_txt"].text.text = self.data.cfg.special_content
	-- 	--self.node_list["biaoqian_num"].text.text = self.data.cfg.reward_item[0].num
	-- end

	local model_cfg = FestivalLeiChongRechargeWGData.Instance:GetModelInfo()
	if model_cfg == nil then
		return
    end

	local cur_xianyu = FestivalLeiChongRechargeWGData.Instance:GetOwnXianYu()
	local color = cur_xianyu >= self.data.cfg.stage_value and COLOR3B.L_GREEN or COLOR3B.L_RED  --<font color='#ffff00'>10万铜币</font>
	self.node_list["recharge_value"].text.text = string.format(Language.FestivalActivity.KL, color, cur_xianyu, model_cfg.text_color, self.data.cfg.stage_value)
	self.node_list["btn_recharge"]:SetActive(cur_xianyu < self.data.cfg.stage_value and self.data.receive_state == 0)
	self.node_list["btn_lingqu"]:SetActive(cur_xianyu >= self.data.cfg.stage_value and self.data.receive_state == 1)
	self.node_list["btn_yilingqu"]:SetActive(self.data.receive_state == 2)

	local reward_data = {}
	if self.data.cfg.reward_item then
		for i=0,#self.data.cfg.reward_item do
			table.insert(reward_data,self.data.cfg.reward_item[i])
		end
	end
	reward_data = SortDataByItemColor(reward_data)
	self.item_list_view:SetDataList(reward_data)
end

function FestivalLeiChongRender:OnClickRewardHnadler(sender)
	FestivalLeiChongRechargeWGCtrl.Instance:SendLeiChongRechargeReq(FESTIVAL_XIANSHILEICHONG_TYPE.TYPE_DRAW,self.data.cfg.ID)
end

function FestivalLeiChongRender:OnClickRechargeHnadler(sender)
	FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge)
end


