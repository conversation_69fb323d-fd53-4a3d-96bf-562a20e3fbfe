GodOfWealthWGData = GodOfWealthWGData or BaseClass()
--/jy_gm wealthgodgmoperate:4 10000 0 0  设置奖池灵玉
--/jy_gm wealthgodgmoperate:5 0 0 0      奖池结算
--/jy_gm wealthgodgmoperate:3 11867226 0 0 增加奖池玩家
--/jy_gm wealthgodgmoperate:1 10 0 0  重设抽奖次数

function GodOfWealthWGData:__init()
	if GodOfWealthWGData.Instance ~= nil then
		ErrorLog("[GodOfWealthWGData] attempt to create singleton twice!")
		return
	end
    GodOfWealthWGData.Instance = self

    self.can_draw_times = 0
    self.has_draw_times = 0
    self.rank_count = 0
    self.rank_item_list = {}
    self.current_rnb_buy_cfg = {}
    self.current_rnb_buy_seq = 0
    self.current_get_per = 1
    self.beishu_cfg = {}
    self.draw_beishu_cfg = {}
    self.max_per = 0
    self.is_draw_now = false
    self.pool_summary_time = 0
    self.all_buy_reward = 0
    
    self.wealthgod_cfg = ConfigManager.Instance:GetAutoConfig("wealth_god_cfg_auto")
    self.rmb_buy_cfg = ListToMap(self.wealthgod_cfg.rmb_buy, "seq")
    self.rmb_buy2_cfg = ListToMap(self.wealthgod_cfg.rmb_buy2, "seq")
    self.draw_cfg = ListToMap(self.wealthgod_cfg.draw, "draw_times")
    self.reward_level_cfg = ListToMap(self.wealthgod_cfg.wealth_god_reward, "level")
    self.item_random_desc = self.wealthgod_cfg.item_random_desc
    self.other_cfg = self.wealthgod_cfg.other[1]

    self:DealWithDrawCfg()
    self:InitLuckyDrawData()
    RemindManager.Instance:Register(RemindName.Wealth_god_fanyu, BindTool.Bind(self.GetGodOfWealthRemind, self))
    RemindManager.Instance:Register(RemindName.Wealth_god_lucky_wealth, BindTool.Bind(self.GetLuckyWealthRemind, self))
end

function GodOfWealthWGData:__delete()
    GodOfWealthWGData.Instance = nil
    RemindManager.Instance:UnRegister(RemindName.Wealth_god_fanyu)
    RemindManager.Instance:UnRegister(RemindName.Wealth_god_lucky_wealth)
end

function GodOfWealthWGData:DealWithDrawCfg()
    local draw_beishu_cfg = {}
    for k, v in pairs(self.draw_cfg) do
        local data = {}
        data.draw_times = v.draw_times
        data.base_gold = v.base_gold
        data.reward_item = v.reward_item
        data.max_per = 1
        local per_list = {}
        local weight_item = string.split(v.weight, "|")

        for k, v in pairs(weight_item) do
            local per = tonumber(string.split(v, ":")[1])
            per_list[k] = {beishu = per}
            if per > data.max_per then
                data.max_per = per
            end
        end

        data.per_list = per_list
        draw_beishu_cfg[k] = data
    end

    self.draw_beishu_cfg = draw_beishu_cfg
end

function GodOfWealthWGData:GetHasFreeGift()
    local data_list = self:GetReChargeRmbBuyCfg()
    for k, v in pairs(data_list) do
        if (not v.bug_flag) and v.is_free == 1 then
            return true
        end
    end
    return false
end

function GodOfWealthWGData:GetGodOfWealthRemind()
    if not FunOpen.Instance:GetFunIsOpenedByTabName("god_of_wealth") then
        return 0
    end

    --赐福红点
    if self:GetBlessingAllRed() then
        return 1
    end

    if self:GetHasFreeGift() then
        return 1
    end
    -- if self:GetLuckyStarRemind() then
    --     return 1
    -- end

    if self:AllBuyRewardFlagRemind() then
        return 1
    end

    return self.can_draw_times > 0 and 1 or 0
end

function GodOfWealthWGData:GetLuckyWealthRemind()
    if not FunOpen.Instance:GetFunIsOpenedByTabName("god_of_wealth_lucky_star") then
        return 0
    end

    if self:GetLuckyStarRemind() then
        return 1
    end

    return 0
end

function GodOfWealthWGData:SetSkipAniFlag()
    if self.skip_to_ani == 1 then
        self.skip_to_ani = 0
    else
        self.skip_to_ani = 1
    end

    PlayerPrefsUtil.SetInt("wealth_of_god_skip_to_ani" .. RoleWGData.Instance:GetRoleInfo().role_id, self.skip_to_ani)
end

function GodOfWealthWGData:GetSkipAniFlag()
    local flag = PlayerPrefsUtil.GetInt("wealth_of_god_skip_to_ani"..RoleWGData.Instance:GetRoleInfo().role_id)

    if flag then
        self.skip_to_ani = flag
    else
        self.skip_to_ani = 0
    end

	return self.skip_to_ani == 1
end

function GodOfWealthWGData:SetWarlthGodDraw(protocol)
    self.current_get_per = protocol.param2
end

--赐福等级配置
function GodOfWealthWGData:GetBlessingLevelCfg(level)
    return self.reward_level_cfg[level] or {}
end

--是否达到最大等级
function GodOfWealthWGData:GetBlessingIsMaxLevel()
    local max_level = self.reward_level_cfg[#self.reward_level_cfg].level
    local cur_level = self:GetBlessingLevel()
    return cur_level >= max_level
end

-- 获取抽检结果
function GodOfWealthWGData:GetGodOfWealthCurIndex()
    local index = 1
    for k, v in pairs(self.beishu_cfg) do
        if v.beishu == self.current_get_per then
            index = k
           break
        end
    end

    return index, self.current_get_per
end

function GodOfWealthWGData:GetMaxPer()
    return self.max_per
end

function GodOfWealthWGData:SetWealthGodInfo(protocol)
    self.can_draw_times = protocol.can_draw_times
    self.has_draw_times = protocol.has_draw_times
    self.rmb_buy_flag = bit:d2b_l2h(protocol.rmb_buy_flag, nil, true)
    self.rmb_buy_per_flag = bit:d2b_l2h(protocol.rmb_buy_per_flag, nil, true)
    self.rmb_buy_times = protocol.rmb_buy_times
    self.is_reward_received = protocol.is_reward_received
    self.reserve_ch = protocol.reserve_ch
    self.reward_level = protocol.reward_level

    local current_rnb_buy_cfg = {}
    for k, v in pairs(self.rmb_buy_cfg) do
        if not self:GetWealthGodBugFlag(v.seq) then
            current_rnb_buy_cfg = v
            self.current_rnb_buy_seq = v.seq
            break
        end
    end

    self.current_rnb_buy_cfg = current_rnb_buy_cfg

    local draw_time = self.has_draw_times + 1 
    self.next_reward_item = (self.draw_beishu_cfg[draw_time] or {}).reward_item or {}
    if draw_time > #self.draw_beishu_cfg then
        draw_time = #self.draw_beishu_cfg
    end

    local cfg = self.draw_beishu_cfg[draw_time]
    self.max_per = cfg.max_per
    self.beishu_cfg = cfg.per_list
end

-- 已抽奖次数
function GodOfWealthWGData:GetCurDrawTimes()
    return (self.has_draw_times or 0) + 1 
end

-- 下次必得
function GodOfWealthWGData:GetNextDrawRewardShowItem()
    return self.next_reward_item or {}
end

function GodOfWealthWGData:GetDrawCfg(draw_times)
    return self.draw_cfg[draw_times] or self.draw_cfg[#self.draw_cfg]
end

function GodOfWealthWGData:GetDrawCfgReal(draw_times)
    return self.draw_cfg[draw_times] or {}
end

function GodOfWealthWGData:GetCanDrawTime()
    return self.can_draw_times
end

function GodOfWealthWGData:GetCurrentRmbBuyCfg()
    return self.current_rnb_buy_cfg, self.rmb_buy_cfg[#self.rmb_buy_cfg]
end

function GodOfWealthWGData:GetReChargeRmbBuyCfg()
    local data_list = {}

    -- 未购买  免费  可买  已买

    if not IsEmptyTable(self.rmb_buy_cfg) then
        for k, v in pairs(self.rmb_buy_cfg) do
            local bug_flag = self:GetWealthGodBugFlag(v.seq)
            local can_buy = v.seq == self.current_rnb_buy_seq
            local data = {cfg = v, bug_flag = bug_flag, can_buy = can_buy, is_free = v.is_free}
            table.insert(data_list, data)
        end

        table.sort(data_list, function (a, b)
            if a.bug_flag == b.bug_flag then
                if a.is_free == b.is_free then
                    if a.can_buy == b.can_buy then
                        return a.cfg.seq < b.cfg.seq
                    else
                        return a.can_buy
                    end
                else
                    return a.is_free > b.is_free
                end
            else
                return b.bug_flag
            end
        end)
    end

    return data_list
end

--今日赐福是否领取
function GodOfWealthWGData:GetBlessingRewardIsGet()
    return self.is_reward_received == 1 or false
end

--当前赐福等级
function GodOfWealthWGData:GetBlessingLevel()
    return self.reward_level or 0
end

--当前迎财神次数
function GodOfWealthWGData:GetBlessingBuyTimes()
    return self.rmb_buy_times or 0
end

--赐福每日领取红点
function GodOfWealthWGData:GetBlessingLQRed()
    local is_get = self:GetBlessingRewardIsGet()
    local reward_level = self:GetBlessingLevel()
    if not is_get and reward_level > 0 then
        return true
    end

    return false
end

--赐福升级红点
function GodOfWealthWGData:GetBlessingUpRed()
    local reward_level = GodOfWealthWGData.Instance:GetBlessingLevel()
    local cur_level_cfg = GodOfWealthWGData.Instance:GetBlessingLevelCfg(reward_level)
    local buy_times = GodOfWealthWGData.Instance:GetBlessingBuyTimes()
    if cur_level_cfg == nil then
        return false
    end

    if self:GetBlessingIsMaxLevel() then
        return false
    end

    return buy_times >= cur_level_cfg.rmb_buy_time
end

--财神赐福红点
function GodOfWealthWGData:GetBlessingAllRed()
    if not self:IsCanShowRechargePanel() then
        return false
    end

    --赐福领取
    if self:GetBlessingLQRed() then
        return true
    end

    --赐福升级
    if self:GetBlessingUpRed() then
        return true
    end

    return false
end

function GodOfWealthWGData:GetBlessingRuleDesc()
    return self.other_cfg.desc
end

function GodOfWealthWGData:GetAutoRenBuyNeed()
    local need_num = 0
    local need_count = 0
    for k, v in pairs(self.rmb_buy_cfg) do
        if v.seq >= self.current_rnb_buy_seq and v.is_free == 0 then
            need_num = need_num + v.price
            need_count = need_count + 1
        end
    end

    return need_num, need_count
end

function GodOfWealthWGData:GetWealthGodBugFlag(seq)
    return self.rmb_buy_flag[seq] == 1
end

function GodOfWealthWGData:GetWealthGodIsHaveRewardChance()
    for k, v in pairs(self.rmb_buy_cfg) do
        if v.is_calc_pool == 1 and self:GetWealthGodBugFlag(v.seq) then
            return true
        end
    end

    return false
end

function GodOfWealthWGData:GetWealthGodBugPerFlag(seq)
    return self.rmb_buy_per_flag[seq] == 1
end

function GodOfWealthWGData:GetRMBBuyPerCfg()
    return self.rmb_buy2_cfg
end

function GodOfWealthWGData:GetCurrentMultiplayCfg()
    return self.beishu_cfg
end

function GodOfWealthWGData:SetWealthGodRankInfo(protocol)
    self.rank_count = protocol.count
    table.sort(protocol.rank_item_list, function (a, b)
        return a.rank < b.rank
    end)

    self.rank_item_list = protocol.rank_item_list
end

function GodOfWealthWGData:GetWealthGodRankInfo()
    return self.rank_count, self.rank_item_list
end

function GodOfWealthWGData:SetWealthGodPoolInfo(protocol)
    self.pool_summary_time = protocol.pool_summary_time
    self.pool_gold = protocol.pool_gold
end

function GodOfWealthWGData:GetWealthGodIsSummaryToday()
    local str = self:GetDrawPoolTime()
    local hour = string.sub(str, 1, 2)
    local min = string.sub(str, 3, 4)
    local now_time = TimeWGCtrl.Instance:GetServerTime()
	local format_time = os.date("*t", now_time)
	local summary_time = os.time({
		year = format_time.year,
		month = format_time.month,
		day = format_time.day,
		hour = tonumber(hour),
		min = tonumber(min),
		sec = 0
	})

    local final_time = TimeWGCtrl.Instance:NowDayTimeEnd(now_time)
    local is_summary = now_time >= summary_time and now_time < final_time
    return is_summary
end

function GodOfWealthWGData:GetWealthGodPoolInfo()
    return self.pool_gold
end

function GodOfWealthWGData:GetPoolSummaryTime()
    return self.pool_summary_time
end

function GodOfWealthWGData:SetDrawState(is_draw_now)
    self.is_draw_now = is_draw_now
end

function GodOfWealthWGData:GetDrawState()
    return self.is_draw_now
end

function GodOfWealthWGData:IsCanShowRechargePanel()
    local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    return server_day >= self.other_cfg.open_day
end

function GodOfWealthWGData:GetAllReChargeRmbBuyFlag()
    local rmb_buy_list = self:GetReChargeRmbBuyCfg()
    local buy_flag = true
    for k, v in pairs(rmb_buy_list) do
        if not v.bug_flag then
            buy_flag = false 
            break
        end
    end
    return buy_flag
end

function GodOfWealthWGData:SetAllBuyRewardFlag(protocol)
    self.all_buy_reward = protocol.today_is_get
end

function GodOfWealthWGData:AllBuyRewardFlagRemind()
    local all_buy = self:GetAllReChargeRmbBuyFlag()
    if all_buy then
        if self.all_buy_reward == 0 then
            return true
        else
            return false
        end
    end
    return false
end

function GodOfWealthWGData:GetAllBuyRewardList()
    return self.other_cfg.reward_item
end

function GodOfWealthWGData:GetAllBuyDesc()
    return self.other_cfg.yjyc_desc
end

function GodOfWealthWGData:GetGaiLvInfo()
	return self.item_random_desc
end

function GodOfWealthWGData:GetDrawItemId()
	return self.other_cfg.draw_item_id
end

function GodOfWealthWGData:GetDrawPoolTime()
	return self.other_cfg.pool_time
end