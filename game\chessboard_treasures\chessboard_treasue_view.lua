ChessBoardTreasueView = ChessBoardTreasueView or BaseClass(SafeBaseView)

local CHESSBOARD_TREASUE_SYSTEM_TYPE = {
	Fashion = 1,  --时装
	Mount = 2,    --坐骑
	Pet = 3,      --宠物
	Huakun = 4,   --化坤
	WuShen = 5,   --武神
	FaZheng = 6,  --法阵
	CangMing = 7, --沧溟
	Other = 8,    --通用
}

local SHOW_REWARD_NUM = 5  --展示奖励数量

function ChessBoardTreasueView:__init()
    self.view_layer = UiLayer.Normal
    self.view_style = ViewStyle.Half

    self:SetMaskBg()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_activity_panel")
    self:AddViewResource(0, "uis/view/chessboard_treasue_ui_prefab", "layout_chessboard_treasures")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function ChessBoardTreasueView:OpenCallBack()
    ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHESSBOARD, OA_RECHARGE_SCORE_OPERATE_TYPE.INFO)
end

function ChessBoardTreasueView:LoadCallBack()
	self:InitMoneyBar()

	local bundle, asset = ResPath.GetRawImagesPNG("a3_zztt_bj")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

    if not self.chess_pieces_reward_list then
        self.chess_pieces_reward_list = AsyncListView.New(ChessPiecesRewardItemCellRender, self.node_list.reward_list)
    end

	if not self.chess_pieces_jump_list then
        self.chess_pieces_jump_list = AsyncListView.New(ChessPiecesJumpItemCellRender, self.node_list.jump_list)
    end

    --人物模型
    if self.role_model == nil then
		self.role_model = RoleModel.New()
        local display_data = {
			parent_node = self.node_list["role_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = true,
		}

		self.role_model:SetRenderTexUI3DModel(display_data)
		local display_node = self.node_list.role_display
		-- display_node.event_trigger_listener:AddPointerClickListener(BindTool.Bind(self.OnDisplayClick, self))
        self:AddUiRoleModel(self.role_model)
	end

    -- 坐骑模型
    if self.mount_model == nil then
		self.mount_model = RoleModel.New()
        local display_data = {
			parent_node = self.node_list["mount_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = true,
		}

		self.mount_model:SetRenderTexUI3DModel(display_data)
		local display_node = self.node_list.mount_display
		-- display_node.event_trigger_listener:AddPointerClickListener(BindTool.Bind(self.OnDisplayClick, self))
        self:AddUiRoleModel(self.mount_model)
	end

    -- 宠物模型
    if self.pet_model == nil then
		self.pet_model = RoleModel.New()
        local display_data = {
			parent_node = self.node_list["pet_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}

		self.pet_model:SetRenderTexUI3DModel(display_data)
		local display_node = self.node_list.pet_display
		-- display_node.event_trigger_listener:AddPointerClickListener(BindTool.Bind(self.OnDisplayClick, self))
        self:AddUiRoleModel(self.pet_model)
	end

    -- 武魂模型
    if self.wuhun_model == nil then
		self.wuhun_model = RoleModel.New()
        local display_data = {
			parent_node = self.node_list["wuhun_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}

		self.wuhun_model:SetRenderTexUI3DModel(display_data)
		local display_node = self.node_list.wuhun_display
		-- display_node.event_trigger_listener:AddPointerClickListener(BindTool.Bind(self.OnDisplayClick, self))
        self:AddUiRoleModel(self.wuhun_model)
	end

    -- 法阵模型
    if self.faZheng_model == nil then
		self.faZheng_model = RoleModel.New()
        local display_data = {
			parent_node = self.node_list["faZheng_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}

		self.faZheng_model:SetRenderTexUI3DModel(display_data)
		local display_node = self.node_list.faZheng_display
		-- display_node.event_trigger_listener:AddPointerClickListener(BindTool.Bind(self.OnDisplayClick, self))
        self:AddUiRoleModel(self.faZheng_model)
	end

    self.mount_res_id = nil

    if self.display_model == nil then
		self.display_model = OperationActRender.New(self.node_list.display_model)
		self.display_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

    self.reward_cells = {}
	for i = 1, SHOW_REWARD_NUM do
		self.reward_cells[i] = ChessPiecesBigRewardItemCellRender.New(self.node_list["big_reward_pos_" .. i])
		self.reward_cells[i]:SetIndex(i)
	end

    self:FlushEndTime()

    self.node_list.title_view_name.text.text = Language.ChessboardField.ViewTitleName

    XUI.AddClickEventListener(self.node_list.btn_show_reward_panel, BindTool.Bind(self.ChangePanel, self, 1))
    XUI.AddClickEventListener(self.node_list.btn_obtain, BindTool.Bind(self.ChangePanel, self, 2))
    XUI.AddClickEventListener(self.node_list.btn_receive, BindTool.Bind(self.OnClickReceive, self))
    XUI.AddClickEventListener(self.node_list.btn_show_all_reward, BindTool.Bind(self.OpenChessBoardTreasueRewardView, self))

	self:ChangePanel(1)
end

function ChessBoardTreasueView:ReleaseCallBack()
    self.mount_res_id = nil

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

    if self.chess_pieces_reward_list then
        self.chess_pieces_reward_list:DeleteMe()
        self.chess_pieces_reward_list = nil 
    end

	if self.chess_pieces_jump_list then
		self.chess_pieces_jump_list:DeleteMe()
		self.chess_pieces_jump_list = nil
	end

	if self.role_model then
        self.role_model:DeleteMe()
        self.role_model = nil
    end

	if self.mount_model then
        self.mount_model:DeleteMe()
        self.mount_model = nil
    end

	if self.pet_model then
        self.pet_model:DeleteMe()
        self.pet_model = nil
    end

	if self.wuhun_model then
        self.wuhun_model:DeleteMe()
        self.wuhun_model = nil
    end

	if self.faZheng_model then
        self.faZheng_model:DeleteMe()
        self.faZheng_model = nil
    end

    if self.display_model then
		self.display_model:DeleteMe()
		self.display_model = nil
	end

    if self.reward_cells then
		for k, v in pairs(self.reward_cells) do
			v:DeleteMe()
		end
        self.reward_cells = {}
	end

    -- if Runner.Instance:IsExistRunObj(self) then
    --     Runner.Instance:RemoveRunObj(self)
    -- end

    if CountDownManager.Instance:HasCountDown("chess_end_time") then
        CountDownManager.Instance:RemoveCountDown("chess_end_time")
    end
end

function ChessBoardTreasueView:InitMoneyBar()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")

		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_cangjin_score = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end

function ChessBoardTreasueView:OnDisplayClick()
	local model_cfg = ChessBoardTreasueData.Instance:GetCurRoundmodelCfg()
    if not IsEmptyTable(model_cfg) then
        TipWGCtrl.Instance:OpenItem({item_id = model_cfg.item_id})
    end
end

function ChessBoardTreasueView:ShowIndexCallBack()
	ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.ChessBoardTreasueView, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHESSBOARD)
end

function ChessBoardTreasueView:OnFlush()
    local round_cfg = ChessBoardTreasueData.Instance:GetCurRoundRewardCfg()
    if  IsEmptyTable(round_cfg) then
        return
    end

    self:FlushModel()

    self.chess_pieces_reward_list:SetDataList(round_cfg)

    -- 定位到所在位置
    local cur_location = ChessBoardTreasueData.Instance:GetCurLocation()
    self.chess_pieces_reward_list:JumpToIndex(cur_location)

    local open_panel_list = ChessBoardTreasueData.Instance:GetCurOpenpanelCfg()
	self.chess_pieces_jump_list:SetDataList(open_panel_list)

    local round_num = ChessBoardTreasueData.Instance:GetCurRoundNum()
    self.node_list.cur_round_num.text.text = string.format(Language.ChessboardField.ChessCurRound, round_num + 1)

    local is_remind = ChessBoardTreasueData.Instance:GetReceiveRemind()
    self.node_list.btn_remind:SetActive(is_remind)

    local exchange_type, rate, title_img = ChessBoardTreasueData.Instance:GetCurDayExchangeTypeAndRate()
    local str = ""
    if exchange_type == 0 then
        str = Language.ChessboardField.ChessBoardAdsTip1
    elseif exchange_type == 1 then
        str = Language.ChessboardField.ChessBoardAdsTip2
	elseif exchange_type == 2 then
        str = Language.ChessboardField.ChessBoardAdsTip3
    end
    self.node_list.desc_chess_board_ads_tip.text.text = string.format(str, rate)

    local big_reward_list = ChessBoardTreasueData.Instance:GetCurRoundBigRewardList()

    for i = 1, SHOW_REWARD_NUM do
		self.node_list["big_reward_pos_" .. i]:SetActive(not IsEmptyTable(big_reward_list[i]))
		if big_reward_list[i] then
			self.reward_cells[i]:SetData(big_reward_list[i])
		end

        self.reward_cells[i]:PlayFloatTween()
	end

	local bundle, asset = ResPath.GetRawImagesPNG(title_img)
	XUI.SetNodeImage(self.node_list.title_img, bundle, asset)

    self:FlushSlider()
    self:FlushCap()
end

function ChessBoardTreasueView:ChangePanel(panel_index)
	self.node_list.reward_panel:SetActive(panel_index == 1)
	self.node_list.jump_panel:SetActive(panel_index == 2)
end

function ChessBoardTreasueView:OnClickReceive()
    ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHESSBOARD, OA_RECHARGE_SCORE_OPERATE_TYPE.QUICK)
end

function ChessBoardTreasueView:OnClickTip()
    RuleTip.Instance:SetContent(Language.ChessboardField.ChessBoardRule, Language.ChessboardField.ChessBoardRuleTitle)
end

function ChessBoardTreasueView:FlushSlider()
    local round_data_list = ChessBoardTreasueData.Instance:GetCurMaxRoundDataList()

    if IsEmptyTable(round_data_list) then
        return
    end

    local accumulate_score = ChessBoardTreasueData.Instance:GetCurScore()                     -- 拥有积分
    local slider_index = 0
    local slider_value = 0
    local start_value = 0

    local length = #round_data_list
    for i = 1, length do
        local target_score = round_data_list[i - 1].need_score or -1
        self.node_list["round_sign_text" .. i].text.text = target_score >= 0 and target_score or ""
        local score_enough = target_score >= 0 and accumulate_score >= target_score
        self.node_list["round_sign" .. i]:CustomSetActive(score_enough)

        if score_enough then
            slider_index = slider_index + 1
            start_value = target_score
        end
    end

    local max_score = round_data_list[length].need_score or -1
    self.node_list.pro_text.text.text = accumulate_score .. "/" .. max_score
    self.node_list.cur_score_num.text.text = accumulate_score

    if accumulate_score >= max_score then
        slider_value = 1
    else
        local cur_score = slider_index > 0 and (round_data_list[slider_index - 1]).need_score or 0
        local next_score = (round_data_list[slider_index]).need_score

        local diff = next_score - cur_score
        slider_value = slider_index * 1 / 4 + (accumulate_score - cur_score) / diff * 1 / 4
    end

    self.node_list.round_slider.slider.value = slider_value
end

function ChessBoardTreasueView:FlushCap()
    local show_list = ChessBoardTreasueData.Instance:GetCurRoundDisplayModelCfg()
    local cap = 0

    if not IsEmptyTable(show_list) then
        for k, v in pairs(show_list) do
            if v.item_id and v.item_id > 0 then
                local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
                if item_cfg then
                    local need_get_sys, sys_type, replace_idx, show_cap_type = ItemShowWGData.Instance:GetIsNeedSysAttr(item_cfg.id, item_cfg.sys_attr_cap_location)
                    local _, capability = ItemShowWGData.Instance:GetItemAttrDescAndCap({ item_id = v.item_id }, sys_type)
                    cap = cap + capability
                end
            end
        end
    end

    self.node_list.cap_value.text.text = cap
end

function ChessBoardTreasueView:OpenChessBoardTreasueRewardView()
    -- ChessBoardTreasueCtrl.Instance:OpenChessBoardTreasueRewardView()

	local data_list = ChessBoardTreasueData.Instance:GetShowRewardList()
	if not data_list then
		return
	end

	local data_list =
	{
		view_type = RewardShowViewType.Normal,
		reward_item_list = data_list
	}
	RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
end

------------------------------------------------COUNT_DOWN_TIME_START------------------------------------------------------
function ChessBoardTreasueView:FlushEndTime()
    local time, next_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHESSBOARD)
    if CountDownManager.Instance:HasCountDown("chess_end_time") then
        CountDownManager.Instance:RemoveCountDown("chess_end_time")
    end
    if time > 0 then
        CountDownManager.Instance:AddCountDown("chess_end_time",
            BindTool.Bind(self.UpdateCountDown, self),
            BindTool.Bind(self.OnComplete, self),
            nil, time, 1)
    else
        self:OnComplete()
    end
end

function ChessBoardTreasueView:UpdateCountDown(elapse_time, total_time)
    if self.node_list.activity_time then
        local time = math.ceil(total_time - elapse_time)
        local time_str = TimeUtil.FormatSecondDHM8(time)
        self.node_list.activity_time.text.text = string.format(Language.ChessboardField.ChessActTime, time_str)
    end
end

function ChessBoardTreasueView:OnComplete()
    if self.node_list.activity_time then
        self.node_list.activity_time.text.text = ""
    end

    self:Close()
end
------------------------------------------------COUNT_DOWN_TIME_END--------------------------------------------------------

---------------------------------------------------MODLE_START-------------------------------------------------------------
--刷新中间模型区域
function ChessBoardTreasueView:FlushModel()
	local show_list = ChessBoardTreasueData.Instance:GetCurRoundDisplayModelCfg()

    if IsEmptyTable(show_list) then
        return
    end

	self.role_model:RemoveAllModel()
	self.node_list["role_display"]:CustomSetActive(true)
	self.node_list["mount_display"]:CustomSetActive(false)
	self.node_list["faZheng_display"]:CustomSetActive(false)
	self.node_list["pet_display"]:CustomSetActive(false)
	self.node_list["wuhun_display"]:CustomSetActive(false)
	self.node_list["display_model"]:CustomSetActive(false)
	
	local fashion_cfg, body_res_id, set_role_model = nil, nil, true
	for k, data in pairs(show_list) do
		if data.sys_id == CHESSBOARD_TREASUE_SYSTEM_TYPE.Fashion and data.param1 == SHIZHUANG_TYPE.BODY then		-- 时装大类
			fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
			if fashion_cfg then-- 时装	
				local prof = GameVoManager.Instance:GetMainRoleVo().prof
				body_res_id = ResPath.GetFashionModelId(prof, fashion_cfg.resouce)
			end
        elseif data.sys_id == CHESSBOARD_TREASUE_SYSTEM_TYPE.Pet then		-- 宠物
            fashion_cfg = NewAppearanceWGData.Instance:GetLingChongActCfgByImageId(data.param1)
            if fashion_cfg then
                self:SetPetModelData(fashion_cfg.appe_image_id)
            end
		elseif data.sys_id == CHESSBOARD_TREASUE_SYSTEM_TYPE.WuShen then --武魂
			local item_id = data.item_id or 0 --((cfg.reward_item or {})[data.index] or {}).item_id or 0
			self:SetWuhunModelData(item_id)
			set_role_model = false
			break
		elseif data.sys_id == CHESSBOARD_TREASUE_SYSTEM_TYPE.FaZheng then --法阵
			local item_id = data.item_id or 0 --((cfg.reward_item or {})[data.index] or {}).item_id or 0
			self:SetFaZhengModelData(item_id)
			break
		elseif data.sys_id == CHESSBOARD_TREASUE_SYSTEM_TYPE.CangMing then --沧溟
			local item_id = data.item_id or 0 --((cfg.reward_item or {})[data.index] or {}).item_id or 0
			self:SetCangMingModelData(item_id)
			set_role_model = false
			break
        elseif data.sys_id == CHESSBOARD_TREASUE_SYSTEM_TYPE.Other then
            local item_id = data.item_id or 0
            self:FlushLeftModelInfo(data)
            self.node_list["role_display"]:CustomSetActive(false)
            self.node_list["display_model"]:CustomSetActive(true)
		end
	end

	--武魂等系统不需要显示角色模型
	if set_role_model then
		if body_res_id then
            local extra_role_model_data = {
                no_need_do_anim = true,
            }

			self.role_model:SetRoleResid(body_res_id, function()
					-- self.role_model:PlayRoleShowAction()
				end, extra_role_model_data)
		else
			local role_vo = GameVoManager.Instance:GetMainRoleVo()
			self.role_model:SetModelResInfo(role_vo, nil, function()
			-- self.role_model:PlayRoleShowAction()
			end)
		end
	end

    self.mount_res_id = nil
	for k, v in pairs(show_list) do
		self:ShowModelByData(v)
	end
    if self.mount_res_id then
        self.role_model:SetMountResid(self.mount_res_id)
        local action_type = MOUNT_RIDING_TYPE[1]
        local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(self.mount_res_id)
        if not IsEmptyTable(action_cfg) then
            action_type = MOUNT_RIDING_TYPE[action_cfg.action]
        end
        self.role_model:PlayStartAction(action_type)
    end

    local tran_cfg = ChessBoardTreasueData.Instance:GetCurRoundmodelCfg()
    self:SetModelTrans(tran_cfg)
    self.role_model:FixToOrthographic(self.root_node_transform)
end

function ChessBoardTreasueView:ShowModelByData(data)
	if IsEmptyTable(data) then
		return
	end
	local res_id, fashion_cfg
	if data.sys_id == CHESSBOARD_TREASUE_SYSTEM_TYPE.Fashion then				-- 时装大类
		fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
		if fashion_cfg then
			res_id = fashion_cfg.resouce
			if data.param1 == SHIZHUANG_TYPE.MASK then			-- 脸饰
				self.role_model:SetMaskResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.BELT then 		-- 腰饰
				self.role_model:SetWaistResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.WEIBA then		-- 尾巴
				self.role_model:SetTailResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.SHOUHUAN then	-- 手环
				self.role_model:SetShouHuanResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.HALO then	-- 光环
				self.role_model:SetHaloResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.WING then		-- 羽翼
				self.role_model:SetWingResid(res_id, true)
			elseif data.param1 == SHIZHUANG_TYPE.FABAO then		-- 法宝
				self.role_model:SetBaoJuResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.JIANZHEN then	-- 剑阵
				self.role_model:SetJianZhenResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.SHENBING then	-- 武器
				res_id = AppearanceWGData.GetFashionWeaponIdByResViewId(res_id)
				self.role_model:SetWeaponResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.FOOT then		-- 足迹
                self.role_model:SetFootTrailModel(res_id)
				self.role_model:SetRotation(MODEL_ROTATION_TYPE.FOOT)

				-- self.is_foot_view = true
				-- self.foot_effect_id = res_id
				-- if not self.use_update then
				-- 	Runner.Instance:AddRunObj(self, 8)
				-- 	self.use_update = true
				-- end
			end
		end
    elseif data.sys_id == CHESSBOARD_TREASUE_SYSTEM_TYPE.Mount or data.sys_id == CHESSBOARD_TREASUE_SYSTEM_TYPE.Huakun then
        local fashion_cfg, res_id = nil, 0
        if data.sys_id == CHESSBOARD_TREASUE_SYSTEM_TYPE.Huakun then
			fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
            if fashion_cfg then
                local kun_base_cfg = NewAppearanceWGData.Instance:GetKunActCfgByItemId(fashion_cfg.active_need_item_id)
                if kun_base_cfg then
                    res_id = kun_base_cfg.active_id
                end
            end 
        else
			fashion_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByImageId(MOUNT_LINGCHONG_APPE_TYPE.MOUNT, data.param1)
            if fashion_cfg then
                res_id = NewAppearanceWGData.Instance:GetQiChongResIdAndActAttrCfg(fashion_cfg.active_item_id)
            end 
        end

        self.mount_res_id = res_id
        self.role_model:SetMountResid(res_id)
	end
end

function ChessBoardTreasueView:SetModelTrans(trans_data)
    if IsEmptyTable(trans_data) then
        return
    end

    --设置人物模型transform
    self:SetModelData(trans_data, "tzsx", self.role_model)

	--设置坐骑模型transform
    self:SetModelData(trans_data, "tzsx", self.mount_model)
	
    --设置宠物模型transform
    self:SetModelData(trans_data, "tzsx", self.pet_model)

	--设置武魂模型transform
    self:SetModelData(trans_data, "tzsx", self.wuhun_model)

	--设置法阵模型transform
    self:SetModelData(trans_data, "tzsx", self.faZheng_model)
end

function ChessBoardTreasueView:SetPetModelData(res_id)
	if IsEmptyTable(self.pet_model) then
		return
	end

	self.node_list["pet_display"]:CustomSetActive(true)
	self.pet_model:ClearModel()
	local bundle, asset = ResPath.GetPetModel(res_id)

	self.pet_model:SetMainAsset(bundle, asset, function()
		self.pet_model:PlaySoulAction()
	end)

	self.pet_model:FixToOrthographic(self.root_node_transform)
end

function ChessBoardTreasueView:SetMountModelData(res_id)
	self.mount_model:ClearModel()
	if IsEmptyTable(self.mount_model) then
		return
	end

	self.node_list["mount_display"]:CustomSetActive(true)

	local bundle, asset = ResPath.GetMountModel(res_id)
	self.mount_model:SetMainAsset(bundle, asset, function()
		self.mount_model:PlayMountAction()
	end)
	self.mount_model:FixToOrthographic(self.root_node_transform)
end

function ChessBoardTreasueView:SetWuhunModelData(item_id)
	self.wuhun_model:RemoveAllModel()
	local wuhun_cfg = WuHunWGData.Instance:GetWuhunResByItemId(item_id)
	if IsEmptyTable(wuhun_cfg) or IsEmptyTable(self.wuhun_model) then
		return
	end

	self.node_list["wuhun_display"]:CustomSetActive(true)

	local appe_id = wuhun_cfg and wuhun_cfg.appe_image_id or 10114
	local bundle, asset = ResPath.GetWuHunModel(appe_id)
    self.wuhun_model:SetMainAsset(bundle, asset, function ()
		self.wuhun_model:PlayRoleAction(SceneObjAnimator.Rest1)
	end)

	local front_use_index = WuHunFrontWGData.Instance:GetWuHunFrontHuanHuaIndex(wuhun_cfg.wuhun_id)
	local cfg = WuHunFrontWGData.Instance:GetSoulFormationAppimageCfg(wuhun_cfg.wuhun_id, front_use_index)

    if cfg then
        self.wuhun_model:SetSoulFormationResid(cfg.app_image_id,wuhun_cfg.wuhun_id)
    else
        self.wuhun_model:RemoveSoulFormation()
    end
end

--设置模型的transform数据
function ChessBoardTreasueView:SetModelData(trans_data, cfg_key_word, model_render)
	local cfg = trans_data[cfg_key_word .. "_pos"]
    if cfg and cfg ~= "" then
		local pos_x, pos_y, pos_z = 0, 0, 0
		local pos_list = string.split(cfg, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
		pos_z = tonumber(pos_list[3]) or pos_z
        model_render:SetRTAdjustmentRootLocalPosition(pos_x, pos_y, pos_z)
	end

	cfg = trans_data[cfg_key_word .. "_rot"]
	if cfg and cfg ~= "" then
		local rotation_tab = string.split(cfg, "|")

        model_render:SetRTAdjustmentRootLocalRotation(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	end

	cfg = trans_data["main_scale"]
	if cfg and cfg ~= "" then
        model_render:SetRTAdjustmentRootLocalScale(cfg)
	end
end

function ChessBoardTreasueView:SetFaZhengModelData(item_id)
	self.faZheng_model:RemoveAllModel()
	local cfg = SupremeFieldsWGData.Instance:GetFootStoneByItemID(item_id)
	if IsEmptyTable(self.faZheng_model) or IsEmptyTable(cfg) then
		return
	end

	self.node_list["faZheng_display"]:CustomSetActive(true)
	local bundle, asset = ResPath.GetSkillFaZhenModel(cfg.type)
    self.faZheng_model:SetMainAsset(bundle, asset)
end

function ChessBoardTreasueView:SetCangMingModelData(item_id)
	local cfg = FiveElementsWGData.Instance:GetWaistStoneByItemID(item_id)
	if IsEmptyTable(self.role_model) or IsEmptyTable(cfg) then
		return
	end

	self.role_model:RemoveAllModel()
    local role_vo = GameVoManager.Instance:GetMainRoleVo()
	self.role_model:SetModelResInfo(role_vo, nil, function()
		-- self.role_model:PlayRoleShowAction()
	end)

	self.role_model:SetSkillHaloResid(cfg.type)
	self.role_model:FixToOrthographic(self.root_node_transform)
end

function ChessBoardTreasueView:FlushLeftModelInfo(data)
    local show_id = data.item_id
	local display_data = {}
	display_data.should_ani = true
	if show_id ~= 0 and show_id ~= "" then
		local split_list = string.split(show_id, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = show_id
		end
	end

	local bundle_name = data.model_bundle_name
	local asset_name = data.model_asset_name

	display_data.bundle_name = bundle_name
	display_data.asset_name = asset_name
	display_data.render_type = data.model_show_type
	display_data.model_rt_type = ModelRTSCaleType.L
    -- display_data.model_click_func = function ()
    --     TipWGCtrl.Instance:OpenItem({item_id = data.item_id})
    -- end

    local tran_cfg = ChessBoardTreasueData.Instance:GetCurRoundmodelCfg()

	if tran_cfg.tzsx_pos and tran_cfg.tzsx_pos ~= "" then
		local pos_list = string.split(tran_cfg.tzsx_pos, "|")
		local pos_x = tonumber(pos_list[1]) or 0
		local pos_y = tonumber(pos_list[2]) or 0
		local pos_z = tonumber(pos_list[3]) or 0

		display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
	end

	if tran_cfg.tzsx_rot and tran_cfg.tzsx_rot ~= "" then
		local rot_list = string.split(tran_cfg.tzsx_rot, "|")
		local rot_x = tonumber(rot_list[1]) or 0
		local rot_y = tonumber(rot_list[2]) or 0
		local rot_z = tonumber(rot_list[3]) or 0

		display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
	end

	if tran_cfg.main_scale and tran_cfg.main_scale ~= "" then
		display_data.model_adjust_root_local_scale = tran_cfg.main_scale
	end

	self.display_model:SetData(display_data)
end

----------------------------------------------------MODLE_END--------------------------------------------------------------

--------------------------------------------ChessPiecesRewardItemCellRender--------------------------------------------
ChessPiecesRewardItemCellRender = ChessPiecesRewardItemCellRender or BaseClass(BaseRender)

function ChessPiecesRewardItemCellRender:LoadCallBack()
    if not self.reward_item then
        self.reward_item = ItemCell.New(self.node_list["item_node"])
    end

    self.node_list["get_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGetReward, self))
end

function ChessPiecesRewardItemCellRender:__delete()
    if self.reward_item then
        self.reward_item:DeleteMe()
        self.reward_item = nil
    end
end

function ChessPiecesRewardItemCellRender:OnFlush()
    if not self.data then
        return
    end

    local item_id = self.data.item.item_id
    local num = self.data.item.num
    self.reward_item:SetData({item_id = item_id, num = num})
    self.reward_item:SetDefaultEff(false)

    local is_get = ChessBoardTreasueData.Instance:GetRewardStateBySeq(self.data.seq)
    self.reward_item:SetLingQuVisible(is_get)

    local need_score = self.data.need_score
    self.node_list.score_num.text.text = need_score

    local score = ChessBoardTreasueData.Instance:GetCurScore()
    self.node_list.item_red:SetActive(not is_get and need_score <= score)
	self.reward_item:SetRedPointEff(not is_get and need_score <= score)
    self.node_list.get_btn:SetActive(need_score <= score)
    self.node_list.progress_img:SetActive(need_score <= score)
    self.node_list.progress:SetActive(self.index > 1)
end

function ChessPiecesRewardItemCellRender:OnClickGetReward()
    if not self.data then
        return
    end

    local is_get = ChessBoardTreasueData.Instance:GetRewardStateBySeq(self.data.seq)
    local score = ChessBoardTreasueData.Instance:GetCurScore()
    local need_score = self.data.need_score

    if is_get then
        TipWGCtrl.Instance:ShowSystemMsg(Language.ChessboardField.ChessIsGetReward)
        return
    end

    if score >= need_score then
        ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHESSBOARD, OA_RECHARGE_SCORE_OPERATE_TYPE.REWARD, self.data.seq)
    else
        TipWGCtrl.Instance:ShowSystemMsg(Language.ChessboardField.ChessScoreLack)
    end
end

--------------------------------ChessPiecesBigRewardItemCellRender------------------------------------------------
ChessPiecesBigRewardItemCellRender = ChessPiecesBigRewardItemCellRender or BaseClass(BaseRender)

function ChessPiecesBigRewardItemCellRender:LoadCallBack()
	self.star_pos = self.node_list["reward_root"].rect.anchoredPosition
	self.node_list["click_btn"].button:AddClickListener(BindTool.Bind(self.OnClickRewardCell,self))
	self:PlayFloatTween()
end

function ChessPiecesBigRewardItemCellRender:ReleaseCallBack()
	RectTransform.SetAnchoredPositionXY(self.node_list.reward_root.rect, 0, 0)

	if self.tweener then
		self.tweener:Kill()
		self.tweener = nil
	end
end

function ChessPiecesBigRewardItemCellRender:PlayMoveTween(target_pos)
	if self.tweener then
		self.tweener:Kill()
		self.tweener = nil
	end

	local tween_root = self.node_list["reward_root"].rect
	self.tweener = tween_root:DOMove(target_pos,2.2)
	self.tweener:OnComplete(function ()
		self.node_list["reward_root"]:CustomSetActive(false)
	end)
	self.tweener:SetEase(DG.Tweening.Ease.OutExpo)
end

function ChessPiecesBigRewardItemCellRender:PlayFloatTween()
	if self.tweener then
		self.tweener:Kill()
		self.tweener = nil
	end
	if not self.tweener then
		self.node_list["reward_root"]:CustomSetActive(true)
		local tween_root = self.node_list["reward_root"].rect
		tween_root.anchoredPosition = self.star_pos
		local random_time = math.random(900, 1200)
		self.tweener = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 8, random_time / 1000)
		self.tweener:SetEase(DG.Tweening.Ease.InOutSine)
		self.tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	end
end

function ChessPiecesBigRewardItemCellRender:OnFlush()
	if not self.data then
		return
	end

	local cfg = ItemWGData.Instance:GetItemConfig(self.data.item.item_id)
	if cfg then
		local bundle, asset = ResPath.GetItem(cfg.icon_id)
		self.node_list.icon_img.image:LoadSpriteAsync(bundle, asset, function()
			self.node_list.icon_img.image:SetNativeSize()
		end)
	end

	--总共就给4张品质图.....真滴烦策划jxw.
	local color_idx = 5
	if cfg.color < 5 then
		color_idx = 5
	elseif cfg.color > 8 then
		color_idx = 8
	else
		color_idx = cfg.color
	end
	local bg_bundle, bg_asset = ResPath.GetRawImagesPNG("a3_zztt_y" .. color_idx)
	XUI.SetNodeImage(self.node_list.bg, bg_bundle, bg_asset)

	self.node_list.score.text.text = string.format(Language.ChessboardField.CurNeedDesc2, self.data.need_score)

	local is_get = ChessBoardTreasueData.Instance:GetRewardStateBySeq(self.data.seq)
    self.node_list.mask:SetActive(is_get)
    self.node_list.score:SetActive(not is_get)

    local need_score = self.data.need_score
    local score = ChessBoardTreasueData.Instance:GetCurScore()
    self.node_list.remind:SetActive(not is_get and need_score <= score)
end

function ChessPiecesBigRewardItemCellRender:OnClickRewardCell()
	if not self.data then
        return
    end

    local is_get = ChessBoardTreasueData.Instance:GetRewardStateBySeq(self.data.seq)
    local score = ChessBoardTreasueData.Instance:GetCurScore()
    local need_score = self.data.need_score

    if is_get then
		TipWGCtrl.Instance:OpenItem({item_id = self.data.item.item_id, num = 1})
        return
    end

    if score >= need_score then
        ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHESSBOARD, OA_RECHARGE_SCORE_OPERATE_TYPE.REWARD, self.data.seq)
    else
		TipWGCtrl.Instance:OpenItem({item_id = self.data.item.item_id, num = 1})
    end
end


--------------------------------------------ChessPiecesJumpItemCellRender--------------------------------------------
ChessPiecesJumpItemCellRender = ChessPiecesJumpItemCellRender or BaseClass(BaseRender)

function ChessPiecesJumpItemCellRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.jump_btn, BindTool.Bind(self.OnClickJump, self))
end

function ChessPiecesJumpItemCellRender:OnFlush()
    if not self.data then
        return
    end

	local bundle, asset = ResPath.GetMainUIIcon(self.data.res_name)
    XUI.SetNodeImage(self.node_list.icon, bundle, asset)
end

function ChessPiecesJumpItemCellRender:OnClickJump()
    if not self.data then
        return
    end

	if self.data.open_panel and self.data.open_panel ~= "" then
		if self.data.act_type == "" then --非活动
			FunOpen.Instance:OpenViewNameByCfg(self.data.open_panel)
		else
			local is_act_open = ActivityWGData.Instance:GetActivityIsOpen(self.data.act_type) --活动是否开启
			if is_act_open then
				FunOpen.Instance:OpenViewNameByCfg(self.data.open_panel)
			else
				TipWGCtrl.Instance:ShowSystemMsg(Language.Common.ActivateNoOpen)
			end
		end
	end
end
