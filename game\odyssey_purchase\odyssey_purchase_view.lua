OdysseyPurchaseView = OdysseyPurchaseView or BaseClass(SafeBaseView)

function OdysseyPurchaseView:__init()
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/odyssey_purchase_ui_prefab", "layout_odyssey_purchase")
	self.cur_show_index = 1
end

function OdysseyPurchaseView:__delete()

end

function OdysseyPurchaseView:LoadCallBack()
	self:FlushEndTime()
	self:StartQuest()

	if not self.left_reward then
		self.left_reward = AsyncListView.New(ItemCell, self.node_list.left_reward)
		self.left_reward:SetStartZeroIndex(true)
	end

	if not self.right_reward then
		self.right_reward = AsyncListView.New(ItemCell, self.node_list.right_reward)
		self.right_reward:SetStartZeroIndex(true)
	end

	if self.left_model == nil then
		self.left_model = OperationActRender.New(self.node_list["left_model"])
	end

	if self.right_model == nil then
		self.right_model = OperationActRender.New(self.node_list["right_model"])
	end

	XUI.AddClickEventListener(self.node_list["daily_reward"], BindTool.Bind(self.OnBtnfreeBtn, self))
	XUI.AddClickEventListener(self.node_list["left_btn"], BindTool.Bind(self.OnBuyBtn, self, true))
	XUI.AddClickEventListener(self.node_list["right_btn"], BindTool.Bind(self.OnBuyBtn, self, false))
	XUI.AddClickEventListener(self.node_list["go_btn"], BindTool.Bind(self.OnClickActBtn, self))
end

function OdysseyPurchaseView:ReleaseCallBack()
	self:ModelCancelQuest()

	if self.left_reward then
		self.left_reward:DeleteMe()
		self.left_reward = nil
	end

	if self.right_reward then
		self.right_reward:DeleteMe()
		self.right_reward = nil
	end

	if self.left_model then
		self.left_model:DeleteMe()
		self.left_model = nil
	end

	if self.right_model then
		self.right_model:DeleteMe()
		self.right_model = nil
	end

	if self.gift_box_tween then
		self.gift_box_tween:Kill()
		self.gift_box_tween = nil
	end

	if CountDownManager.Instance:HasCountDown("chess_end_time") then
		CountDownManager.Instance:RemoveCountDown("chess_end_time")
	end
end

function OdysseyPurchaseView:OnFlush()
	self:FlushReward()
	self:FlushModel()

	local left_flag, right_flag = OdysseyPurchaseData.Instance:GetRewardState()
	local left_limit, right_limit = OdysseyPurchaseData.Instance:GetGradeLimit()
	self.node_list.left_quota_num.text.text = string.format(Language.OdysseyPurchaseDesc.QuotaNum, left_limit - left_flag,
		left_limit)
	self.node_list.right_quota_num.text.text = string.format(Language.OdysseyPurchaseDesc.QuotaNum,
		right_limit - right_flag, right_limit)
	self.node_list["left_price"].transform.anchoredPosition = Vector2(-40, 0)
	self.node_list["right_price"].transform.anchoredPosition = Vector2(-40, 0)

	if left_flag == left_limit then
		self.node_list.left_price.text.text = Language.OdysseyPurchaseDesc.SoldOut
		self.node_list["left_price"].transform.anchoredPosition = Vector2(-6, 0)
		self.node_list.left_original_price:SetActive(false)
		self.node_list.left_quota_num.text.text = ToColorStr(
		string.format(Language.OdysseyPurchaseDesc.QuotaNum, left_limit - left_flag, left_limit), COLOR3B.RED)
	end

	if right_flag == right_limit then
		self.node_list.right_price.text.text = Language.OdysseyPurchaseDesc.SoldOut
		self.node_list["right_price"].transform.anchoredPosition = Vector2(-6, 0)
		self.node_list.right_original_price:SetActive(false)
		self.node_list.right_quota_num.text.text = ToColorStr(
		string.format(Language.OdysseyPurchaseDesc.QuotaNum, right_limit - right_flag, right_limit), COLOR3B.RED)
	end

	local is_buy_free = OdysseyPurchaseData.Instance:GetShopIsBuyFlag()
	self.node_list.reward_remind:SetActive(not is_buy_free)
	self.node_list["daily_reward"]:SetActive(not is_buy_free)
	if not is_buy_free then
		if self.gift_box_tween then
			self.gift_box_tween:Restart()
		else
			if self.gift_box_tween then
				self.gift_box_tween:Kill()
				self.gift_box_tween = nil
			end

			self.gift_box_tween = DG.Tweening.DOTween.Sequence()
			UITween.ShakeAnimi(self.node_list.daily_reward.transform, self.gift_box_tween)
		end
	elseif self.gift_box_tween then
		self.gift_box_tween:Pause()
		self.node_list.daily_reward.transform.localRotation = Quaternion.identity
	end
end

function OdysseyPurchaseView:FlushReward()
	local left_reward_cfg, right_reward_cfg = OdysseyPurchaseData.Instance:GetGradeRewardCfg()
	if IsEmptyTable(left_reward_cfg) or IsEmptyTable(right_reward_cfg) then
		return
	end

	self.left_reward:SetDataList(left_reward_cfg.reward_item)
	local left_price = RoleWGData.GetPayMoneyStr(left_reward_cfg.rmb_number, left_reward_cfg.rmb_type,
		left_reward_cfg.reward_type)
	self.node_list.left_price.text.text = string.format(Language.OdysseyPurchaseDesc.MiaoShaPrice, left_price)
	self.node_list.left_original_price.text.text = left_reward_cfg.original_price

	self.right_reward:SetDataList(right_reward_cfg.reward_item)
	local right_price = RoleWGData.GetPayMoneyStr(right_reward_cfg.rmb_number, right_reward_cfg.rmb_type,
		right_reward_cfg.reward_type)
	self.node_list.right_price.text.text = string.format(Language.OdysseyPurchaseDesc.MiaoShaPrice, right_price)
	self.node_list.right_original_price.text.text = right_reward_cfg.original_price
end

function OdysseyPurchaseView:FlushModel()
	local left_model_cfg, right_model_cfg = OdysseyPurchaseData.Instance:GetGradeModelCfg()
	if IsEmptyTable(left_model_cfg) or IsEmptyTable(right_model_cfg) then
		return
	end

	local left_cfg_index = left_model_cfg[self.cur_show_index]
	local right_cfg_index = right_model_cfg[self.cur_show_index]
	local left_item_name = ItemWGData.Instance:GetItemNameDarkColor(left_cfg_index.model_show_itemid)
	local right_item_name = ItemWGData.Instance:GetItemNameDarkColor(right_cfg_index.model_show_itemid)
	self.node_list.left_name.text.text = left_item_name
	self.node_list.right_name.text.text = right_item_name
	self:ModelInformation(self.node_list["left_model"], self.left_model, left_cfg_index)
	self:ModelInformation(self.node_list["right_model"], self.right_model, right_cfg_index)
end

function OdysseyPurchaseView:ModelInformation(point, cur_model, cfg_index)
	local display_data = {}

	display_data.should_ani = true
	if cfg_index.model_show_itemid ~= 0 and cfg_index.model_show_itemid ~= "" then
		local split_list = string.split(cfg_index.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = cfg_index.model_show_itemid
		end
	end

	display_data.bundle_name = cfg_index["model_bundle_name"]
	display_data.asset_name = cfg_index["model_asset_name"]
	local model_show_type = tonumber(cfg_index["model_show_type"]) or 1
	display_data.render_type = model_show_type - 1

	cur_model:SetData(display_data)
	local scale = cfg_index["display_scale"]
	Transform.SetLocalScaleXYZ(point.transform, scale, scale, scale)
	local pos_x, pos_y = 0, 0
	if cfg_index.display_pos and cfg_index.display_pos ~= "" then
		local pos_list = string.split(cfg_index.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	RectTransform.SetAnchoredPositionXY(cur_model.rect, pos_x, pos_y)

	if cfg_index.rotation and cfg_index.rotation ~= "" then
		local rotation_tab = string.split(cfg_index.rotation, "|")
		point.transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	end
end

function OdysseyPurchaseView:StartQuest()
	if not self.notice_timer_quest then
		self.notice_timer_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.OnFlushModelSwitch, self), 8)
	end
end

function OdysseyPurchaseView:ModelCancelQuest()
	if self.notice_timer_quest then
		GlobalTimerQuest:CancelQuest(self.notice_timer_quest)
		self.notice_timer_quest = nil
	end
end

function OdysseyPurchaseView:OnFlushModelSwitch()
	if IsEmptyTable(self.left_model) or IsEmptyTable(self.right_model) then
		return
	end

	local max_num = 7
	if self.cur_show_index then
		self.cur_show_index = self.cur_show_index < max_num and self.cur_show_index + 1 or 1
	else
		self.cur_show_index = 1
	end

	self:FlushModel()
end

function OdysseyPurchaseView:FlushEndTime()
	local time, next_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE
	.RAND_ACTIVIYY_TYPE_OA_ODYSSEYRMB)
	if CountDownManager.Instance:HasCountDown("chess_end_time") then
		CountDownManager.Instance:RemoveCountDown("chess_end_time")
	end
	if time > 0 then
		CountDownManager.Instance:AddCountDown("chess_end_time",
			BindTool.Bind(self.UpdateCountDown, self),
			BindTool.Bind(self.OnComplete, self),
			nil, time, 1)
	else
		self:OnComplete()
	end
end

function OdysseyPurchaseView:UpdateCountDown(elapse_time, total_time)
	local time = math.ceil(total_time - elapse_time)
	local time_str = TimeUtil.FormatSecondDHM8(time)
	self.node_list["activity_time"].text.text = string.format(Language.OdysseyPurchaseDesc.OdysseyActTime, time_str)
end

function OdysseyPurchaseView:OnComplete()
	self.node_list.activity_time.text.text = ""
	self:Close()
end

function OdysseyPurchaseView:OnBtnfreeBtn()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVIYY_TYPE_OA_ODYSSEYRMB)
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		local is_buy_free = OdysseyPurchaseData.Instance:GetShopIsBuyFlag()
		if is_buy_free then
			TipWGCtrl.Instance:ShowSystemMsg(Language.OdysseyPurchaseDesc.AllFreeShopBuy)
			return
		end

		ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVIYY_TYPE_OA_ODYSSEYRMB)
	end
end

function OdysseyPurchaseView:OnBuyBtn(is_left)
	local left_grade, right_grade = OdysseyPurchaseData.Instance:GetGradeRewardCfg()
	if IsEmptyTable(left_grade) or IsEmptyTable(right_grade) then
		return
	end

	local left_flag, right_flag = OdysseyPurchaseData.Instance:GetRewardState()
	if is_left then
		if left_flag < left_grade.buy_limit then
			RechargeWGCtrl.Instance:Recharge(left_grade.rmb_number, left_grade.rmb_type, left_grade.reward_type)
		else
			TipWGCtrl.Instance:ShowSystemMsg(Language.OdysseyPurchaseDesc.QuotaDesc)
		end
	else
		if right_flag < right_grade.buy_limit then
			RechargeWGCtrl.Instance:Recharge(right_grade.rmb_number, right_grade.rmb_type, right_grade.reward_type)
		else
			TipWGCtrl.Instance:ShowSystemMsg(Language.OdysseyPurchaseDesc.QuotaDesc)
		end
	end
end

function OdysseyPurchaseView:OnClickActBtn()
	local act_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_A_LIFELONG_LOVE_TASK)
	if not act_open then
		TipWGCtrl.Instance:ShowSystemMsg(Language.OdysseyPurchaseDesc.NoOpenBuyActTips)
		return
	end

	ViewManager.Instance:Open(GuideModuleName.LifeTimeOfLoveView)
end
