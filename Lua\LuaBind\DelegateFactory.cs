﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using System.Collections.Generic;
using LuaInterface;

public class DelegateFactory
{
	public delegate Delegate DelegateCreate(LuaFunction func, LuaTable self, bool flag);
	public static Dictionary<Type, DelegateCreate> dict = new Dictionary<Type, DelegateCreate>();
	static DelegateFactory factory = new DelegateFactory();

	public static void Init()
	{
		Register();
	}

	public static void Register()
	{
		dict.Clear();
		dict.Add(typeof(System.Action), factory.System_Action);
		dict.Add(typeof(UnityEngine.Events.UnityAction), factory.UnityEngine_Events_UnityAction);
		dict.Add(typeof(System.Predicate<int>), factory.System_Predicate_int);
		dict.Add(typeof(System.Action<int>), factory.System_Action_int);
		dict.Add(typeof(System.Comparison<int>), factory.System_Comparison_int);
		dict.Add(typeof(System.Func<int,int>), factory.System_Func_int_int);
		dict.Add(typeof(System.Action<float>), factory.System_Action_float);
		dict.Add(typeof(System.Action<string>), factory.System_Action_string);
		dict.Add(typeof(System.Action<int,float,float>), factory.System_Action_int_float_float);
		dict.Add(typeof(System.Action<string,UnityEngine.AnimatorStateInfo>), factory.System_Action_string_UnityEngine_AnimatorStateInfo);
		dict.Add(typeof(System.Action<bool,string>), factory.System_Action_bool_string);
		dict.Add(typeof(DG.Tweening.TweenCallback), factory.DG_Tweening_TweenCallback);
		dict.Add(typeof(NetClientExtensions.ReceiveMessageDelegate), factory.NetClientExtensions_ReceiveMessageDelegate);
		dict.Add(typeof(DG.Tweening.EaseFunction), factory.DG_Tweening_EaseFunction);
		dict.Add(typeof(UnityEngine.RectTransform.ReapplyDrivenProperties), factory.UnityEngine_RectTransform_ReapplyDrivenProperties);
		dict.Add(typeof(UnityEngine.Canvas.WillRenderCanvases), factory.UnityEngine_Canvas_WillRenderCanvases);
		dict.Add(typeof(UnityEngine.Events.UnityAction<bool>), factory.UnityEngine_Events_UnityAction_bool);
		dict.Add(typeof(UnityEngine.Events.UnityAction<UnityEngine.Vector2>), factory.UnityEngine_Events_UnityAction_UnityEngine_Vector2);
		dict.Add(typeof(UnityEngine.UI.InputField.OnValidateInput), factory.UnityEngine_UI_InputField_OnValidateInput);
		dict.Add(typeof(UnityEngine.Events.UnityAction<string>), factory.UnityEngine_Events_UnityAction_string);
		dict.Add(typeof(UnityEngine.Events.UnityAction<int>), factory.UnityEngine_Events_UnityAction_int);
		dict.Add(typeof(System.Action<bool>), factory.System_Action_bool);
		dict.Add(typeof(UnityEngine.Events.UnityAction<float>), factory.UnityEngine_Events_UnityAction_float);
		dict.Add(typeof(UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene,UnityEngine.SceneManagement.LoadSceneMode>), factory.UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_LoadSceneMode);
		dict.Add(typeof(UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene>), factory.UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene);
		dict.Add(typeof(UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene,UnityEngine.SceneManagement.Scene>), factory.UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_Scene);
		dict.Add(typeof(System.Action<UnityEngine.Playables.PlayableDirector>), factory.System_Action_UnityEngine_Playables_PlayableDirector);
		dict.Add(typeof(System.Action<UnityEngine.AsyncOperation>), factory.System_Action_UnityEngine_AsyncOperation);
		dict.Add(typeof(System.Func<int,string,TMPro.TMP_FontAsset>), factory.System_Func_int_string_TMPro_TMP_FontAsset);
		dict.Add(typeof(System.Func<int,string,TMPro.TMP_SpriteAsset>), factory.System_Func_int_string_TMPro_TMP_SpriteAsset);
		dict.Add(typeof(System.Action<TMPro.TMP_TextInfo>), factory.System_Action_TMPro_TMP_TextInfo);
		dict.Add(typeof(TMPro.TMP_InputField.OnValidateInput), factory.TMPro_TMP_InputField_OnValidateInput);
		dict.Add(typeof(System.Action<float,float>), factory.System_Action_float_float);
		dict.Add(typeof(System.Action<UnityEngine.Texture2D>), factory.System_Action_UnityEngine_Texture2D);
		dict.Add(typeof(System.Action<QingGongState>), factory.System_Action_QingGongState);
		dict.Add(typeof(ClickManager.ClickGroundDelegate), factory.ClickManager_ClickGroundDelegate);
		dict.Add(typeof(System.Action<bool,int>), factory.System_Action_bool_int);
		dict.Add(typeof(System.Action<UnityEngine.GameObject>), factory.System_Action_UnityEngine_GameObject);
		dict.Add(typeof(System.Action<UnityEngine.Camera,UnityEngine.Vector3,UnityEngine.Quaternion>), factory.System_Action_UnityEngine_Camera_UnityEngine_Vector3_UnityEngine_Quaternion);
		dict.Add(typeof(Nirvana.SignalDelegate), factory.Nirvana_SignalDelegate);
		dict.Add(typeof(Nirvana.EventTriggerListener.PointerEventDelegate), factory.Nirvana_EventTriggerListener_PointerEventDelegate);
		dict.Add(typeof(Nirvana.EventTriggerListener.BaseEventDelegate), factory.Nirvana_EventTriggerListener_BaseEventDelegate);
		dict.Add(typeof(Nirvana.EventTriggerListener.AxisEventDelegate), factory.Nirvana_EventTriggerListener_AxisEventDelegate);
		dict.Add(typeof(System.Action<UnityEngine.Texture>), factory.System_Action_UnityEngine_Texture);
		dict.Add(typeof(System.Action<int,string>), factory.System_Action_int_string);
		dict.Add(typeof(System.Action<bool,int,string>), factory.System_Action_bool_int_string);
		dict.Add(typeof(System.Action<bool,string,string>), factory.System_Action_bool_string_string);
		dict.Add(typeof(System.Action<string,string>), factory.System_Action_string_string);
		dict.Add(typeof(Nirvana.EnhancedNetClient.DisconnectDelegate), factory.Nirvana_EnhancedNetClient_DisconnectDelegate);
		dict.Add(typeof(Nirvana.EnhancedNetClient.ReceiveDelegate), factory.Nirvana_EnhancedNetClient_ReceiveDelegate);
		dict.Add(typeof(Nirvana.EnhancedNetClient.ConnectDelegate), factory.Nirvana_EnhancedNetClient_ConnectDelegate);
		dict.Add(typeof(Nirvana.EnhancedNetClient.SendDelegate), factory.Nirvana_EnhancedNetClient_SendDelegate);
		dict.Add(typeof(Nirvana.EnhancedNetClient.DisconnectWithReasonDelegate), factory.Nirvana_EnhancedNetClient_DisconnectWithReasonDelegate);
		dict.Add(typeof(Nirvana.ListView.GetCellDelegate), factory.Nirvana_ListView_GetCellDelegate);
		dict.Add(typeof(Nirvana.ListView.CellCountDelegate), factory.Nirvana_ListView_CellCountDelegate);
		dict.Add(typeof(Nirvana.ListView.GetCellSizeDelegate), factory.Nirvana_ListView_GetCellSizeDelegate);
		dict.Add(typeof(Nirvana.ListView.RecycleCellDelegate), factory.Nirvana_ListView_RecycleCellDelegate);
		dict.Add(typeof(UIDrag.DropCallBackAction), factory.UIDrag_DropCallBackAction);
		dict.Add(typeof(ListViewDelegate.NumberOfCellsDelegate), factory.ListViewDelegate_NumberOfCellsDelegate);
		dict.Add(typeof(ListViewDelegate.CellViewSizeDelegate), factory.ListViewDelegate_CellViewSizeDelegate);
		dict.Add(typeof(ListViewDelegate.CellViewDelegate), factory.ListViewDelegate_CellViewDelegate);
		dict.Add(typeof(ListViewCell.RefreshCell), factory.ListViewCell_RefreshCell);
		dict.Add(typeof(System.Func<float,bool>), factory.System_Func_float_bool);
		dict.Add(typeof(EnhancedUI.EnhancedScroller.CellViewVisibilityChangedDelegate), factory.EnhancedUI_EnhancedScroller_CellViewVisibilityChangedDelegate);
		dict.Add(typeof(EnhancedUI.EnhancedScroller.CellViewWillRecycleDelegate), factory.EnhancedUI_EnhancedScroller_CellViewWillRecycleDelegate);
		dict.Add(typeof(EnhancedUI.EnhancedScroller.ScrollerScrolledDelegate), factory.EnhancedUI_EnhancedScroller_ScrollerScrolledDelegate);
		dict.Add(typeof(EnhancedUI.EnhancedScroller.ScrollerSnappedDelegate), factory.EnhancedUI_EnhancedScroller_ScrollerSnappedDelegate);
		dict.Add(typeof(EnhancedUI.EnhancedScroller.ScrollerScrollingChangedDelegate), factory.EnhancedUI_EnhancedScroller_ScrollerScrollingChangedDelegate);
		dict.Add(typeof(EnhancedUI.EnhancedScroller.ScrollerTweeningChangedDelegate), factory.EnhancedUI_EnhancedScroller_ScrollerTweeningChangedDelegate);
		dict.Add(typeof(EnhancedUI.EnhancedScroller.ScrollerScrollingEndDelegate), factory.EnhancedUI_EnhancedScroller_ScrollerScrollingEndDelegate);
		dict.Add(typeof(EnhancedUI.EnhancedScroller.ScrollerSnapJumpToCenterDelegate), factory.EnhancedUI_EnhancedScroller_ScrollerSnapJumpToCenterDelegate);
		dict.Add(typeof(ListViewSimpleDelegate.NumberOfCellsDelegate), factory.ListViewSimpleDelegate_NumberOfCellsDelegate);
		dict.Add(typeof(ListViewSimpleDelegate.CellSizeDelegate), factory.ListViewSimpleDelegate_CellSizeDelegate);
		dict.Add(typeof(ListViewSimpleDelegate.CellRefreshDelegate), factory.ListViewSimpleDelegate_CellRefreshDelegate);
		dict.Add(typeof(PageViewSimpleDelegate.NumberOfCellsDelegate), factory.PageViewSimpleDelegate_NumberOfCellsDelegate);
		dict.Add(typeof(PageViewSimpleDelegate.CellRefreshDelegate), factory.PageViewSimpleDelegate_CellRefreshDelegate);
		dict.Add(typeof(PageViewSimpleDelegate.CellRecycleDelegate), factory.PageViewSimpleDelegate_CellRecycleDelegate);
		dict.Add(typeof(System.Action<UnityEngine.GameObject,int>), factory.System_Action_UnityEngine_GameObject_int);
		dict.Add(typeof(ListenTrigger.TriggerEnterDelegate), factory.ListenTrigger_TriggerEnterDelegate);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.TouchCancelHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_TouchCancelHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.Cancel2FingersHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_Cancel2FingersHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.TouchStartHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_TouchStartHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.TouchDownHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_TouchDownHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.TouchUpHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_TouchUpHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.SimpleTapHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_SimpleTapHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.DoubleTapHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_DoubleTapHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.LongTapStartHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_LongTapStartHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.LongTapHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_LongTapHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.LongTapEndHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_LongTapEndHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.DragStartHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_DragStartHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.DragHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_DragHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.DragEndHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_DragEndHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.SwipeStartHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_SwipeStartHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.SwipeHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_SwipeHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.SwipeEndHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_SwipeEndHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.TouchStart2FingersHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_TouchStart2FingersHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.TouchDown2FingersHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_TouchDown2FingersHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.TouchUp2FingersHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_TouchUp2FingersHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.SimpleTap2FingersHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_SimpleTap2FingersHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.DoubleTap2FingersHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_DoubleTap2FingersHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.LongTapStart2FingersHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_LongTapStart2FingersHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.LongTap2FingersHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_LongTap2FingersHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.LongTapEnd2FingersHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_LongTapEnd2FingersHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.TwistHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_TwistHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.TwistEndHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_TwistEndHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.PinchHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_PinchHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.PinchInHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_PinchInHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.PinchOutHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_PinchOutHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.PinchEndHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_PinchEndHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.DragStart2FingersHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_DragStart2FingersHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.Drag2FingersHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_Drag2FingersHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.DragEnd2FingersHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_DragEnd2FingersHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.SwipeStart2FingersHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_SwipeStart2FingersHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.Swipe2FingersHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_Swipe2FingersHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.SwipeEnd2FingersHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_SwipeEnd2FingersHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.EasyTouchIsReadyHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_EasyTouchIsReadyHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.OverUIElementHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_OverUIElementHandler);
		dict.Add(typeof(HedgehogTeam.EasyTouch.EasyTouch.UIElementTouchUpHandler), factory.HedgehogTeam_EasyTouch_EasyTouch_UIElementTouchUpHandler);
		dict.Add(typeof(UnityEngine.Events.UnityAction<string,string,UnityEngine.Object>), factory.UnityEngine_Events_UnityAction_string_string_UnityEngine_Object);
		dict.Add(typeof(UIGlassBlurCtrl.OnRenderTextureSetup), factory.UIGlassBlurCtrl_OnRenderTextureSetup);
		dict.Add(typeof(Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateSingle), factory.Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateSingle);
		dict.Add(typeof(Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateMultiple), factory.Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateMultiple);
		dict.Add(typeof(Spine.Unity.SkeletonGraphic.SkeletonRendererDelegate), factory.Spine_Unity_SkeletonGraphic_SkeletonRendererDelegate);
		dict.Add(typeof(Spine.Unity.SkeletonGraphic.InstructionDelegate), factory.Spine_Unity_SkeletonGraphic_InstructionDelegate);
		dict.Add(typeof(Spine.Unity.ISkeletonAnimationDelegate), factory.Spine_Unity_ISkeletonAnimationDelegate);
		dict.Add(typeof(Spine.Unity.UpdateBonesDelegate), factory.Spine_Unity_UpdateBonesDelegate);
		dict.Add(typeof(Spine.Unity.MeshGeneratorDelegate), factory.Spine_Unity_MeshGeneratorDelegate);
		dict.Add(typeof(Spine.AnimationState.TrackEntryDelegate), factory.Spine_AnimationState_TrackEntryDelegate);
		dict.Add(typeof(Spine.AnimationState.TrackEntryEventDelegate), factory.Spine_AnimationState_TrackEntryEventDelegate);
		dict.Add(typeof(System.Predicate<string>), factory.System_Predicate_string);
		dict.Add(typeof(System.Comparison<string>), factory.System_Comparison_string);
		dict.Add(typeof(System.Predicate<UnityEngine.Sprite>), factory.System_Predicate_UnityEngine_Sprite);
		dict.Add(typeof(System.Action<UnityEngine.Sprite>), factory.System_Action_UnityEngine_Sprite);
		dict.Add(typeof(System.Comparison<UnityEngine.Sprite>), factory.System_Comparison_UnityEngine_Sprite);
		dict.Add(typeof(UnityEngine.Events.UnityAction<UnityEngine.SpriteRenderer>), factory.UnityEngine_Events_UnityAction_UnityEngine_SpriteRenderer);
		dict.Add(typeof(System.Action<UnityEngine.ReflectionProbe,UnityEngine.ReflectionProbe.ReflectionProbeEvent>), factory.System_Action_UnityEngine_ReflectionProbe_UnityEngine_ReflectionProbe_ReflectionProbeEvent);
		dict.Add(typeof(System.Action<UnityEngine.Cubemap>), factory.System_Action_UnityEngine_Cubemap);
		dict.Add(typeof(CharacterShadows.CharacterShadowManager.OnLightChanged), factory.CharacterShadows_CharacterShadowManager_OnLightChanged);
		dict.Add(typeof(System.Action<UnityEngine.Rendering.CommandBuffer,UnityEngine.Renderer>), factory.System_Action_UnityEngine_Rendering_CommandBuffer_UnityEngine_Renderer);
		dict.Add(typeof(FancyScrollView.FancyScrollView<FancyScrollView.NullContext>.CellRefreshDelegate), factory.FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellRefreshDelegate);
		dict.Add(typeof(FancyScrollView.FancyScrollView<FancyScrollView.NullContext>.CellSelectDelegate), factory.FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellSelectDelegate);
		dict.Add(typeof(FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshCellDelegate), factory.FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshCellDelegate);
		dict.Add(typeof(FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshPosDelegate), factory.FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshPosDelegate);
		dict.Add(typeof(EasingCore.EasingFunction), factory.EasingCore_EasingFunction);
		dict.Add(typeof(FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellRefreshDelegate), factory.FancyScrollView_FancyScrollView_FancyScrollView_Context_CellRefreshDelegate);
		dict.Add(typeof(FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellSelectDelegate), factory.FancyScrollView_FancyScrollView_FancyScrollView_Context_CellSelectDelegate);
		dict.Add(typeof(System.Action<int,FancyScrollView.MovementDirection>), factory.System_Action_int_FancyScrollView_MovementDirection);
		dict.Add(typeof(FancyScrollView.FancyCell<FancyScrollView.Context>.RefreshCellDelegate), factory.FancyScrollView_FancyCell_FancyScrollView_Context_RefreshCellDelegate);
		dict.Add(typeof(FancyScrollView.FancyCell<FancyScrollView.Context>.RefreshPosDelegate), factory.FancyScrollView_FancyCell_FancyScrollView_Context_RefreshPosDelegate);
		dict.Add(typeof(System.Func<UnityEngine.LogType,object,bool>), factory.System_Func_UnityEngine_LogType_object_bool);
		dict.Add(typeof(DG.Tweening.Core.DOGetter<float>), factory.DG_Tweening_Core_DOGetter_float);
		dict.Add(typeof(DG.Tweening.Core.DOSetter<float>), factory.DG_Tweening_Core_DOSetter_float);
		dict.Add(typeof(DG.Tweening.Core.DOGetter<double>), factory.DG_Tweening_Core_DOGetter_double);
		dict.Add(typeof(DG.Tweening.Core.DOSetter<double>), factory.DG_Tweening_Core_DOSetter_double);
		dict.Add(typeof(DG.Tweening.Core.DOGetter<int>), factory.DG_Tweening_Core_DOGetter_int);
		dict.Add(typeof(DG.Tweening.Core.DOSetter<int>), factory.DG_Tweening_Core_DOSetter_int);
		dict.Add(typeof(DG.Tweening.Core.DOGetter<uint>), factory.DG_Tweening_Core_DOGetter_uint);
		dict.Add(typeof(DG.Tweening.Core.DOSetter<uint>), factory.DG_Tweening_Core_DOSetter_uint);
		dict.Add(typeof(DG.Tweening.Core.DOGetter<long>), factory.DG_Tweening_Core_DOGetter_long);
		dict.Add(typeof(DG.Tweening.Core.DOSetter<long>), factory.DG_Tweening_Core_DOSetter_long);
		dict.Add(typeof(DG.Tweening.Core.DOGetter<ulong>), factory.DG_Tweening_Core_DOGetter_ulong);
		dict.Add(typeof(DG.Tweening.Core.DOSetter<ulong>), factory.DG_Tweening_Core_DOSetter_ulong);
		dict.Add(typeof(DG.Tweening.Core.DOGetter<string>), factory.DG_Tweening_Core_DOGetter_string);
		dict.Add(typeof(DG.Tweening.Core.DOSetter<string>), factory.DG_Tweening_Core_DOSetter_string);
		dict.Add(typeof(DG.Tweening.Core.DOGetter<UnityEngine.Vector2>), factory.DG_Tweening_Core_DOGetter_UnityEngine_Vector2);
		dict.Add(typeof(DG.Tweening.Core.DOSetter<UnityEngine.Vector2>), factory.DG_Tweening_Core_DOSetter_UnityEngine_Vector2);
		dict.Add(typeof(DG.Tweening.Core.DOGetter<UnityEngine.Vector3>), factory.DG_Tweening_Core_DOGetter_UnityEngine_Vector3);
		dict.Add(typeof(DG.Tweening.Core.DOSetter<UnityEngine.Vector3>), factory.DG_Tweening_Core_DOSetter_UnityEngine_Vector3);
		dict.Add(typeof(DG.Tweening.Core.DOGetter<UnityEngine.Vector4>), factory.DG_Tweening_Core_DOGetter_UnityEngine_Vector4);
		dict.Add(typeof(DG.Tweening.Core.DOSetter<UnityEngine.Vector4>), factory.DG_Tweening_Core_DOSetter_UnityEngine_Vector4);
		dict.Add(typeof(DG.Tweening.Core.DOGetter<UnityEngine.Quaternion>), factory.DG_Tweening_Core_DOGetter_UnityEngine_Quaternion);
		dict.Add(typeof(DG.Tweening.Core.DOSetter<UnityEngine.Quaternion>), factory.DG_Tweening_Core_DOSetter_UnityEngine_Quaternion);
		dict.Add(typeof(DG.Tweening.Core.DOGetter<UnityEngine.Color>), factory.DG_Tweening_Core_DOGetter_UnityEngine_Color);
		dict.Add(typeof(DG.Tweening.Core.DOSetter<UnityEngine.Color>), factory.DG_Tweening_Core_DOSetter_UnityEngine_Color);
		dict.Add(typeof(DG.Tweening.Core.DOGetter<UnityEngine.Rect>), factory.DG_Tweening_Core_DOGetter_UnityEngine_Rect);
		dict.Add(typeof(DG.Tweening.Core.DOSetter<UnityEngine.Rect>), factory.DG_Tweening_Core_DOSetter_UnityEngine_Rect);
		dict.Add(typeof(DG.Tweening.Core.DOGetter<UnityEngine.RectOffset>), factory.DG_Tweening_Core_DOGetter_UnityEngine_RectOffset);
		dict.Add(typeof(DG.Tweening.Core.DOSetter<UnityEngine.RectOffset>), factory.DG_Tweening_Core_DOSetter_UnityEngine_RectOffset);
		dict.Add(typeof(DG.Tweening.TweenCallback<int>), factory.DG_Tweening_TweenCallback_int);
		dict.Add(typeof(UnityEngine.Camera.CameraCallback), factory.UnityEngine_Camera_CameraCallback);
		dict.Add(typeof(UnityEngine.Application.AdvertisingIdentifierCallback), factory.UnityEngine_Application_AdvertisingIdentifierCallback);
		dict.Add(typeof(UnityEngine.Application.LowMemoryCallback), factory.UnityEngine_Application_LowMemoryCallback);
		dict.Add(typeof(UnityEngine.Application.LogCallback), factory.UnityEngine_Application_LogCallback);
		dict.Add(typeof(System.Func<bool>), factory.System_Func_bool);
		dict.Add(typeof(System.Action<UnityEngine.PhysicsScene,Unity.Collections.NativeArray<UnityEngine.ModifiableContactPair>>), factory.System_Action_UnityEngine_PhysicsScene_Unity_Collections_NativeArray_UnityEngine_ModifiableContactPair);
		dict.Add(typeof(UnityEngine.AudioClip.PCMReaderCallback), factory.UnityEngine_AudioClip_PCMReaderCallback);
		dict.Add(typeof(UnityEngine.AudioClip.PCMSetPositionCallback), factory.UnityEngine_AudioClip_PCMSetPositionCallback);
		dict.Add(typeof(UnityEngine.Video.VideoPlayer.EventHandler), factory.UnityEngine_Video_VideoPlayer_EventHandler);
		dict.Add(typeof(UnityEngine.Video.VideoPlayer.ErrorEventHandler), factory.UnityEngine_Video_VideoPlayer_ErrorEventHandler);
		dict.Add(typeof(UnityEngine.Video.VideoPlayer.TimeEventHandler), factory.UnityEngine_Video_VideoPlayer_TimeEventHandler);
		dict.Add(typeof(UnityEngine.Video.VideoPlayer.FrameReadyEventHandler), factory.UnityEngine_Video_VideoPlayer_FrameReadyEventHandler);
		dict.Add(typeof(EventDelegate.Callback), factory.EventDelegate_Callback);

		DelegateTraits<System.Action>.Init(factory.System_Action);
		DelegateTraits<UnityEngine.Events.UnityAction>.Init(factory.UnityEngine_Events_UnityAction);
		DelegateTraits<System.Predicate<int>>.Init(factory.System_Predicate_int);
		DelegateTraits<System.Action<int>>.Init(factory.System_Action_int);
		DelegateTraits<System.Comparison<int>>.Init(factory.System_Comparison_int);
		DelegateTraits<System.Func<int,int>>.Init(factory.System_Func_int_int);
		DelegateTraits<System.Action<float>>.Init(factory.System_Action_float);
		DelegateTraits<System.Action<string>>.Init(factory.System_Action_string);
		DelegateTraits<System.Action<int,float,float>>.Init(factory.System_Action_int_float_float);
		DelegateTraits<System.Action<string,UnityEngine.AnimatorStateInfo>>.Init(factory.System_Action_string_UnityEngine_AnimatorStateInfo);
		DelegateTraits<System.Action<bool,string>>.Init(factory.System_Action_bool_string);
		DelegateTraits<DG.Tweening.TweenCallback>.Init(factory.DG_Tweening_TweenCallback);
		DelegateTraits<NetClientExtensions.ReceiveMessageDelegate>.Init(factory.NetClientExtensions_ReceiveMessageDelegate);
		DelegateTraits<DG.Tweening.EaseFunction>.Init(factory.DG_Tweening_EaseFunction);
		DelegateTraits<UnityEngine.RectTransform.ReapplyDrivenProperties>.Init(factory.UnityEngine_RectTransform_ReapplyDrivenProperties);
		DelegateTraits<UnityEngine.Canvas.WillRenderCanvases>.Init(factory.UnityEngine_Canvas_WillRenderCanvases);
		DelegateTraits<UnityEngine.Events.UnityAction<bool>>.Init(factory.UnityEngine_Events_UnityAction_bool);
		DelegateTraits<UnityEngine.Events.UnityAction<UnityEngine.Vector2>>.Init(factory.UnityEngine_Events_UnityAction_UnityEngine_Vector2);
		DelegateTraits<UnityEngine.UI.InputField.OnValidateInput>.Init(factory.UnityEngine_UI_InputField_OnValidateInput);
		DelegateTraits<UnityEngine.Events.UnityAction<string>>.Init(factory.UnityEngine_Events_UnityAction_string);
		DelegateTraits<UnityEngine.Events.UnityAction<int>>.Init(factory.UnityEngine_Events_UnityAction_int);
		DelegateTraits<System.Action<bool>>.Init(factory.System_Action_bool);
		DelegateTraits<UnityEngine.Events.UnityAction<float>>.Init(factory.UnityEngine_Events_UnityAction_float);
		DelegateTraits<UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene,UnityEngine.SceneManagement.LoadSceneMode>>.Init(factory.UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_LoadSceneMode);
		DelegateTraits<UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene>>.Init(factory.UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene);
		DelegateTraits<UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene,UnityEngine.SceneManagement.Scene>>.Init(factory.UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_Scene);
		DelegateTraits<System.Action<UnityEngine.Playables.PlayableDirector>>.Init(factory.System_Action_UnityEngine_Playables_PlayableDirector);
		DelegateTraits<System.Action<UnityEngine.AsyncOperation>>.Init(factory.System_Action_UnityEngine_AsyncOperation);
		DelegateTraits<System.Func<int,string,TMPro.TMP_FontAsset>>.Init(factory.System_Func_int_string_TMPro_TMP_FontAsset);
		DelegateTraits<System.Func<int,string,TMPro.TMP_SpriteAsset>>.Init(factory.System_Func_int_string_TMPro_TMP_SpriteAsset);
		DelegateTraits<System.Action<TMPro.TMP_TextInfo>>.Init(factory.System_Action_TMPro_TMP_TextInfo);
		DelegateTraits<TMPro.TMP_InputField.OnValidateInput>.Init(factory.TMPro_TMP_InputField_OnValidateInput);
		DelegateTraits<System.Action<float,float>>.Init(factory.System_Action_float_float);
		DelegateTraits<System.Action<UnityEngine.Texture2D>>.Init(factory.System_Action_UnityEngine_Texture2D);
		DelegateTraits<System.Action<QingGongState>>.Init(factory.System_Action_QingGongState);
		DelegateTraits<ClickManager.ClickGroundDelegate>.Init(factory.ClickManager_ClickGroundDelegate);
		DelegateTraits<System.Action<bool,int>>.Init(factory.System_Action_bool_int);
		DelegateTraits<System.Action<UnityEngine.GameObject>>.Init(factory.System_Action_UnityEngine_GameObject);
		DelegateTraits<System.Action<UnityEngine.Camera,UnityEngine.Vector3,UnityEngine.Quaternion>>.Init(factory.System_Action_UnityEngine_Camera_UnityEngine_Vector3_UnityEngine_Quaternion);
		DelegateTraits<Nirvana.SignalDelegate>.Init(factory.Nirvana_SignalDelegate);
		DelegateTraits<Nirvana.EventTriggerListener.PointerEventDelegate>.Init(factory.Nirvana_EventTriggerListener_PointerEventDelegate);
		DelegateTraits<Nirvana.EventTriggerListener.BaseEventDelegate>.Init(factory.Nirvana_EventTriggerListener_BaseEventDelegate);
		DelegateTraits<Nirvana.EventTriggerListener.AxisEventDelegate>.Init(factory.Nirvana_EventTriggerListener_AxisEventDelegate);
		DelegateTraits<System.Action<UnityEngine.Texture>>.Init(factory.System_Action_UnityEngine_Texture);
		DelegateTraits<System.Action<int,string>>.Init(factory.System_Action_int_string);
		DelegateTraits<System.Action<bool,int,string>>.Init(factory.System_Action_bool_int_string);
		DelegateTraits<System.Action<bool,string,string>>.Init(factory.System_Action_bool_string_string);
		DelegateTraits<System.Action<string,string>>.Init(factory.System_Action_string_string);
		DelegateTraits<Nirvana.EnhancedNetClient.DisconnectDelegate>.Init(factory.Nirvana_EnhancedNetClient_DisconnectDelegate);
		DelegateTraits<Nirvana.EnhancedNetClient.ReceiveDelegate>.Init(factory.Nirvana_EnhancedNetClient_ReceiveDelegate);
		DelegateTraits<Nirvana.EnhancedNetClient.ConnectDelegate>.Init(factory.Nirvana_EnhancedNetClient_ConnectDelegate);
		DelegateTraits<Nirvana.EnhancedNetClient.SendDelegate>.Init(factory.Nirvana_EnhancedNetClient_SendDelegate);
		DelegateTraits<Nirvana.EnhancedNetClient.DisconnectWithReasonDelegate>.Init(factory.Nirvana_EnhancedNetClient_DisconnectWithReasonDelegate);
		DelegateTraits<Nirvana.ListView.GetCellDelegate>.Init(factory.Nirvana_ListView_GetCellDelegate);
		DelegateTraits<Nirvana.ListView.CellCountDelegate>.Init(factory.Nirvana_ListView_CellCountDelegate);
		DelegateTraits<Nirvana.ListView.GetCellSizeDelegate>.Init(factory.Nirvana_ListView_GetCellSizeDelegate);
		DelegateTraits<Nirvana.ListView.RecycleCellDelegate>.Init(factory.Nirvana_ListView_RecycleCellDelegate);
		DelegateTraits<UIDrag.DropCallBackAction>.Init(factory.UIDrag_DropCallBackAction);
		DelegateTraits<ListViewDelegate.NumberOfCellsDelegate>.Init(factory.ListViewDelegate_NumberOfCellsDelegate);
		DelegateTraits<ListViewDelegate.CellViewSizeDelegate>.Init(factory.ListViewDelegate_CellViewSizeDelegate);
		DelegateTraits<ListViewDelegate.CellViewDelegate>.Init(factory.ListViewDelegate_CellViewDelegate);
		DelegateTraits<ListViewCell.RefreshCell>.Init(factory.ListViewCell_RefreshCell);
		DelegateTraits<System.Func<float,bool>>.Init(factory.System_Func_float_bool);
		DelegateTraits<EnhancedUI.EnhancedScroller.CellViewVisibilityChangedDelegate>.Init(factory.EnhancedUI_EnhancedScroller_CellViewVisibilityChangedDelegate);
		DelegateTraits<EnhancedUI.EnhancedScroller.CellViewWillRecycleDelegate>.Init(factory.EnhancedUI_EnhancedScroller_CellViewWillRecycleDelegate);
		DelegateTraits<EnhancedUI.EnhancedScroller.ScrollerScrolledDelegate>.Init(factory.EnhancedUI_EnhancedScroller_ScrollerScrolledDelegate);
		DelegateTraits<EnhancedUI.EnhancedScroller.ScrollerSnappedDelegate>.Init(factory.EnhancedUI_EnhancedScroller_ScrollerSnappedDelegate);
		DelegateTraits<EnhancedUI.EnhancedScroller.ScrollerScrollingChangedDelegate>.Init(factory.EnhancedUI_EnhancedScroller_ScrollerScrollingChangedDelegate);
		DelegateTraits<EnhancedUI.EnhancedScroller.ScrollerTweeningChangedDelegate>.Init(factory.EnhancedUI_EnhancedScroller_ScrollerTweeningChangedDelegate);
		DelegateTraits<EnhancedUI.EnhancedScroller.ScrollerScrollingEndDelegate>.Init(factory.EnhancedUI_EnhancedScroller_ScrollerScrollingEndDelegate);
		DelegateTraits<EnhancedUI.EnhancedScroller.ScrollerSnapJumpToCenterDelegate>.Init(factory.EnhancedUI_EnhancedScroller_ScrollerSnapJumpToCenterDelegate);
		DelegateTraits<ListViewSimpleDelegate.NumberOfCellsDelegate>.Init(factory.ListViewSimpleDelegate_NumberOfCellsDelegate);
		DelegateTraits<ListViewSimpleDelegate.CellSizeDelegate>.Init(factory.ListViewSimpleDelegate_CellSizeDelegate);
		DelegateTraits<ListViewSimpleDelegate.CellRefreshDelegate>.Init(factory.ListViewSimpleDelegate_CellRefreshDelegate);
		DelegateTraits<PageViewSimpleDelegate.NumberOfCellsDelegate>.Init(factory.PageViewSimpleDelegate_NumberOfCellsDelegate);
		DelegateTraits<PageViewSimpleDelegate.CellRefreshDelegate>.Init(factory.PageViewSimpleDelegate_CellRefreshDelegate);
		DelegateTraits<PageViewSimpleDelegate.CellRecycleDelegate>.Init(factory.PageViewSimpleDelegate_CellRecycleDelegate);
		DelegateTraits<System.Action<UnityEngine.GameObject,int>>.Init(factory.System_Action_UnityEngine_GameObject_int);
		DelegateTraits<ListenTrigger.TriggerEnterDelegate>.Init(factory.ListenTrigger_TriggerEnterDelegate);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchCancelHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_TouchCancelHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.Cancel2FingersHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_Cancel2FingersHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchStartHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_TouchStartHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchDownHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_TouchDownHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchUpHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_TouchUpHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.SimpleTapHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_SimpleTapHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.DoubleTapHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_DoubleTapHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapStartHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_LongTapStartHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_LongTapHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapEndHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_LongTapEndHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.DragStartHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_DragStartHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.DragHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_DragHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.DragEndHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_DragEndHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeStartHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_SwipeStartHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_SwipeHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeEndHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_SwipeEndHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchStart2FingersHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_TouchStart2FingersHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchDown2FingersHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_TouchDown2FingersHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchUp2FingersHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_TouchUp2FingersHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.SimpleTap2FingersHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_SimpleTap2FingersHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.DoubleTap2FingersHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_DoubleTap2FingersHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapStart2FingersHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_LongTapStart2FingersHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTap2FingersHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_LongTap2FingersHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapEnd2FingersHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_LongTapEnd2FingersHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TwistHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_TwistHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TwistEndHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_TwistEndHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.PinchHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_PinchHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.PinchInHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_PinchInHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.PinchOutHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_PinchOutHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.PinchEndHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_PinchEndHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.DragStart2FingersHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_DragStart2FingersHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.Drag2FingersHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_Drag2FingersHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.DragEnd2FingersHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_DragEnd2FingersHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeStart2FingersHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_SwipeStart2FingersHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.Swipe2FingersHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_Swipe2FingersHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeEnd2FingersHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_SwipeEnd2FingersHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.EasyTouchIsReadyHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_EasyTouchIsReadyHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.OverUIElementHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_OverUIElementHandler);
		DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.UIElementTouchUpHandler>.Init(factory.HedgehogTeam_EasyTouch_EasyTouch_UIElementTouchUpHandler);
		DelegateTraits<UnityEngine.Events.UnityAction<string,string,UnityEngine.Object>>.Init(factory.UnityEngine_Events_UnityAction_string_string_UnityEngine_Object);
		DelegateTraits<UIGlassBlurCtrl.OnRenderTextureSetup>.Init(factory.UIGlassBlurCtrl_OnRenderTextureSetup);
		DelegateTraits<Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateSingle>.Init(factory.Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateSingle);
		DelegateTraits<Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateMultiple>.Init(factory.Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateMultiple);
		DelegateTraits<Spine.Unity.SkeletonGraphic.SkeletonRendererDelegate>.Init(factory.Spine_Unity_SkeletonGraphic_SkeletonRendererDelegate);
		DelegateTraits<Spine.Unity.SkeletonGraphic.InstructionDelegate>.Init(factory.Spine_Unity_SkeletonGraphic_InstructionDelegate);
		DelegateTraits<Spine.Unity.ISkeletonAnimationDelegate>.Init(factory.Spine_Unity_ISkeletonAnimationDelegate);
		DelegateTraits<Spine.Unity.UpdateBonesDelegate>.Init(factory.Spine_Unity_UpdateBonesDelegate);
		DelegateTraits<Spine.Unity.MeshGeneratorDelegate>.Init(factory.Spine_Unity_MeshGeneratorDelegate);
		DelegateTraits<Spine.AnimationState.TrackEntryDelegate>.Init(factory.Spine_AnimationState_TrackEntryDelegate);
		DelegateTraits<Spine.AnimationState.TrackEntryEventDelegate>.Init(factory.Spine_AnimationState_TrackEntryEventDelegate);
		DelegateTraits<System.Predicate<string>>.Init(factory.System_Predicate_string);
		DelegateTraits<System.Comparison<string>>.Init(factory.System_Comparison_string);
		DelegateTraits<System.Predicate<UnityEngine.Sprite>>.Init(factory.System_Predicate_UnityEngine_Sprite);
		DelegateTraits<System.Action<UnityEngine.Sprite>>.Init(factory.System_Action_UnityEngine_Sprite);
		DelegateTraits<System.Comparison<UnityEngine.Sprite>>.Init(factory.System_Comparison_UnityEngine_Sprite);
		DelegateTraits<UnityEngine.Events.UnityAction<UnityEngine.SpriteRenderer>>.Init(factory.UnityEngine_Events_UnityAction_UnityEngine_SpriteRenderer);
		DelegateTraits<System.Action<UnityEngine.ReflectionProbe,UnityEngine.ReflectionProbe.ReflectionProbeEvent>>.Init(factory.System_Action_UnityEngine_ReflectionProbe_UnityEngine_ReflectionProbe_ReflectionProbeEvent);
		DelegateTraits<System.Action<UnityEngine.Cubemap>>.Init(factory.System_Action_UnityEngine_Cubemap);
		DelegateTraits<CharacterShadows.CharacterShadowManager.OnLightChanged>.Init(factory.CharacterShadows_CharacterShadowManager_OnLightChanged);
		DelegateTraits<System.Action<UnityEngine.Rendering.CommandBuffer,UnityEngine.Renderer>>.Init(factory.System_Action_UnityEngine_Rendering_CommandBuffer_UnityEngine_Renderer);
		DelegateTraits<FancyScrollView.FancyScrollView<FancyScrollView.NullContext>.CellRefreshDelegate>.Init(factory.FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellRefreshDelegate);
		DelegateTraits<FancyScrollView.FancyScrollView<FancyScrollView.NullContext>.CellSelectDelegate>.Init(factory.FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellSelectDelegate);
		DelegateTraits<FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshCellDelegate>.Init(factory.FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshCellDelegate);
		DelegateTraits<FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshPosDelegate>.Init(factory.FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshPosDelegate);
		DelegateTraits<EasingCore.EasingFunction>.Init(factory.EasingCore_EasingFunction);
		DelegateTraits<FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellRefreshDelegate>.Init(factory.FancyScrollView_FancyScrollView_FancyScrollView_Context_CellRefreshDelegate);
		DelegateTraits<FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellSelectDelegate>.Init(factory.FancyScrollView_FancyScrollView_FancyScrollView_Context_CellSelectDelegate);
		DelegateTraits<System.Action<int,FancyScrollView.MovementDirection>>.Init(factory.System_Action_int_FancyScrollView_MovementDirection);
		DelegateTraits<FancyScrollView.FancyCell<FancyScrollView.Context>.RefreshCellDelegate>.Init(factory.FancyScrollView_FancyCell_FancyScrollView_Context_RefreshCellDelegate);
		DelegateTraits<FancyScrollView.FancyCell<FancyScrollView.Context>.RefreshPosDelegate>.Init(factory.FancyScrollView_FancyCell_FancyScrollView_Context_RefreshPosDelegate);
		DelegateTraits<System.Func<UnityEngine.LogType,object,bool>>.Init(factory.System_Func_UnityEngine_LogType_object_bool);
		DelegateTraits<DG.Tweening.Core.DOGetter<float>>.Init(factory.DG_Tweening_Core_DOGetter_float);
		DelegateTraits<DG.Tweening.Core.DOSetter<float>>.Init(factory.DG_Tweening_Core_DOSetter_float);
		DelegateTraits<DG.Tweening.Core.DOGetter<double>>.Init(factory.DG_Tweening_Core_DOGetter_double);
		DelegateTraits<DG.Tweening.Core.DOSetter<double>>.Init(factory.DG_Tweening_Core_DOSetter_double);
		DelegateTraits<DG.Tweening.Core.DOGetter<int>>.Init(factory.DG_Tweening_Core_DOGetter_int);
		DelegateTraits<DG.Tweening.Core.DOSetter<int>>.Init(factory.DG_Tweening_Core_DOSetter_int);
		DelegateTraits<DG.Tweening.Core.DOGetter<uint>>.Init(factory.DG_Tweening_Core_DOGetter_uint);
		DelegateTraits<DG.Tweening.Core.DOSetter<uint>>.Init(factory.DG_Tweening_Core_DOSetter_uint);
		DelegateTraits<DG.Tweening.Core.DOGetter<long>>.Init(factory.DG_Tweening_Core_DOGetter_long);
		DelegateTraits<DG.Tweening.Core.DOSetter<long>>.Init(factory.DG_Tweening_Core_DOSetter_long);
		DelegateTraits<DG.Tweening.Core.DOGetter<ulong>>.Init(factory.DG_Tweening_Core_DOGetter_ulong);
		DelegateTraits<DG.Tweening.Core.DOSetter<ulong>>.Init(factory.DG_Tweening_Core_DOSetter_ulong);
		DelegateTraits<DG.Tweening.Core.DOGetter<string>>.Init(factory.DG_Tweening_Core_DOGetter_string);
		DelegateTraits<DG.Tweening.Core.DOSetter<string>>.Init(factory.DG_Tweening_Core_DOSetter_string);
		DelegateTraits<DG.Tweening.Core.DOGetter<UnityEngine.Vector2>>.Init(factory.DG_Tweening_Core_DOGetter_UnityEngine_Vector2);
		DelegateTraits<DG.Tweening.Core.DOSetter<UnityEngine.Vector2>>.Init(factory.DG_Tweening_Core_DOSetter_UnityEngine_Vector2);
		DelegateTraits<DG.Tweening.Core.DOGetter<UnityEngine.Vector3>>.Init(factory.DG_Tweening_Core_DOGetter_UnityEngine_Vector3);
		DelegateTraits<DG.Tweening.Core.DOSetter<UnityEngine.Vector3>>.Init(factory.DG_Tweening_Core_DOSetter_UnityEngine_Vector3);
		DelegateTraits<DG.Tweening.Core.DOGetter<UnityEngine.Vector4>>.Init(factory.DG_Tweening_Core_DOGetter_UnityEngine_Vector4);
		DelegateTraits<DG.Tweening.Core.DOSetter<UnityEngine.Vector4>>.Init(factory.DG_Tweening_Core_DOSetter_UnityEngine_Vector4);
		DelegateTraits<DG.Tweening.Core.DOGetter<UnityEngine.Quaternion>>.Init(factory.DG_Tweening_Core_DOGetter_UnityEngine_Quaternion);
		DelegateTraits<DG.Tweening.Core.DOSetter<UnityEngine.Quaternion>>.Init(factory.DG_Tweening_Core_DOSetter_UnityEngine_Quaternion);
		DelegateTraits<DG.Tweening.Core.DOGetter<UnityEngine.Color>>.Init(factory.DG_Tweening_Core_DOGetter_UnityEngine_Color);
		DelegateTraits<DG.Tweening.Core.DOSetter<UnityEngine.Color>>.Init(factory.DG_Tweening_Core_DOSetter_UnityEngine_Color);
		DelegateTraits<DG.Tweening.Core.DOGetter<UnityEngine.Rect>>.Init(factory.DG_Tweening_Core_DOGetter_UnityEngine_Rect);
		DelegateTraits<DG.Tweening.Core.DOSetter<UnityEngine.Rect>>.Init(factory.DG_Tweening_Core_DOSetter_UnityEngine_Rect);
		DelegateTraits<DG.Tweening.Core.DOGetter<UnityEngine.RectOffset>>.Init(factory.DG_Tweening_Core_DOGetter_UnityEngine_RectOffset);
		DelegateTraits<DG.Tweening.Core.DOSetter<UnityEngine.RectOffset>>.Init(factory.DG_Tweening_Core_DOSetter_UnityEngine_RectOffset);
		DelegateTraits<DG.Tweening.TweenCallback<int>>.Init(factory.DG_Tweening_TweenCallback_int);
		DelegateTraits<UnityEngine.Camera.CameraCallback>.Init(factory.UnityEngine_Camera_CameraCallback);
		DelegateTraits<UnityEngine.Application.AdvertisingIdentifierCallback>.Init(factory.UnityEngine_Application_AdvertisingIdentifierCallback);
		DelegateTraits<UnityEngine.Application.LowMemoryCallback>.Init(factory.UnityEngine_Application_LowMemoryCallback);
		DelegateTraits<UnityEngine.Application.LogCallback>.Init(factory.UnityEngine_Application_LogCallback);
		DelegateTraits<System.Func<bool>>.Init(factory.System_Func_bool);
		DelegateTraits<System.Action<UnityEngine.PhysicsScene,Unity.Collections.NativeArray<UnityEngine.ModifiableContactPair>>>.Init(factory.System_Action_UnityEngine_PhysicsScene_Unity_Collections_NativeArray_UnityEngine_ModifiableContactPair);
		DelegateTraits<UnityEngine.AudioClip.PCMReaderCallback>.Init(factory.UnityEngine_AudioClip_PCMReaderCallback);
		DelegateTraits<UnityEngine.AudioClip.PCMSetPositionCallback>.Init(factory.UnityEngine_AudioClip_PCMSetPositionCallback);
		DelegateTraits<UnityEngine.Video.VideoPlayer.EventHandler>.Init(factory.UnityEngine_Video_VideoPlayer_EventHandler);
		DelegateTraits<UnityEngine.Video.VideoPlayer.ErrorEventHandler>.Init(factory.UnityEngine_Video_VideoPlayer_ErrorEventHandler);
		DelegateTraits<UnityEngine.Video.VideoPlayer.TimeEventHandler>.Init(factory.UnityEngine_Video_VideoPlayer_TimeEventHandler);
		DelegateTraits<UnityEngine.Video.VideoPlayer.FrameReadyEventHandler>.Init(factory.UnityEngine_Video_VideoPlayer_FrameReadyEventHandler);
		DelegateTraits<EventDelegate.Callback>.Init(factory.EventDelegate_Callback);

		TypeTraits<System.Action>.Init(factory.Check_System_Action);
		TypeTraits<UnityEngine.Events.UnityAction>.Init(factory.Check_UnityEngine_Events_UnityAction);
		TypeTraits<System.Predicate<int>>.Init(factory.Check_System_Predicate_int);
		TypeTraits<System.Action<int>>.Init(factory.Check_System_Action_int);
		TypeTraits<System.Comparison<int>>.Init(factory.Check_System_Comparison_int);
		TypeTraits<System.Func<int,int>>.Init(factory.Check_System_Func_int_int);
		TypeTraits<System.Action<float>>.Init(factory.Check_System_Action_float);
		TypeTraits<System.Action<string>>.Init(factory.Check_System_Action_string);
		TypeTraits<System.Action<int,float,float>>.Init(factory.Check_System_Action_int_float_float);
		TypeTraits<System.Action<string,UnityEngine.AnimatorStateInfo>>.Init(factory.Check_System_Action_string_UnityEngine_AnimatorStateInfo);
		TypeTraits<System.Action<bool,string>>.Init(factory.Check_System_Action_bool_string);
		TypeTraits<DG.Tweening.TweenCallback>.Init(factory.Check_DG_Tweening_TweenCallback);
		TypeTraits<NetClientExtensions.ReceiveMessageDelegate>.Init(factory.Check_NetClientExtensions_ReceiveMessageDelegate);
		TypeTraits<DG.Tweening.EaseFunction>.Init(factory.Check_DG_Tweening_EaseFunction);
		TypeTraits<UnityEngine.RectTransform.ReapplyDrivenProperties>.Init(factory.Check_UnityEngine_RectTransform_ReapplyDrivenProperties);
		TypeTraits<UnityEngine.Canvas.WillRenderCanvases>.Init(factory.Check_UnityEngine_Canvas_WillRenderCanvases);
		TypeTraits<UnityEngine.Events.UnityAction<bool>>.Init(factory.Check_UnityEngine_Events_UnityAction_bool);
		TypeTraits<UnityEngine.Events.UnityAction<UnityEngine.Vector2>>.Init(factory.Check_UnityEngine_Events_UnityAction_UnityEngine_Vector2);
		TypeTraits<UnityEngine.UI.InputField.OnValidateInput>.Init(factory.Check_UnityEngine_UI_InputField_OnValidateInput);
		TypeTraits<UnityEngine.Events.UnityAction<string>>.Init(factory.Check_UnityEngine_Events_UnityAction_string);
		TypeTraits<UnityEngine.Events.UnityAction<int>>.Init(factory.Check_UnityEngine_Events_UnityAction_int);
		TypeTraits<System.Action<bool>>.Init(factory.Check_System_Action_bool);
		TypeTraits<UnityEngine.Events.UnityAction<float>>.Init(factory.Check_UnityEngine_Events_UnityAction_float);
		TypeTraits<UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene,UnityEngine.SceneManagement.LoadSceneMode>>.Init(factory.Check_UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_LoadSceneMode);
		TypeTraits<UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene>>.Init(factory.Check_UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene);
		TypeTraits<UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene,UnityEngine.SceneManagement.Scene>>.Init(factory.Check_UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_Scene);
		TypeTraits<System.Action<UnityEngine.Playables.PlayableDirector>>.Init(factory.Check_System_Action_UnityEngine_Playables_PlayableDirector);
		TypeTraits<System.Action<UnityEngine.AsyncOperation>>.Init(factory.Check_System_Action_UnityEngine_AsyncOperation);
		TypeTraits<System.Func<int,string,TMPro.TMP_FontAsset>>.Init(factory.Check_System_Func_int_string_TMPro_TMP_FontAsset);
		TypeTraits<System.Func<int,string,TMPro.TMP_SpriteAsset>>.Init(factory.Check_System_Func_int_string_TMPro_TMP_SpriteAsset);
		TypeTraits<System.Action<TMPro.TMP_TextInfo>>.Init(factory.Check_System_Action_TMPro_TMP_TextInfo);
		TypeTraits<TMPro.TMP_InputField.OnValidateInput>.Init(factory.Check_TMPro_TMP_InputField_OnValidateInput);
		TypeTraits<System.Action<float,float>>.Init(factory.Check_System_Action_float_float);
		TypeTraits<System.Action<UnityEngine.Texture2D>>.Init(factory.Check_System_Action_UnityEngine_Texture2D);
		TypeTraits<System.Action<QingGongState>>.Init(factory.Check_System_Action_QingGongState);
		TypeTraits<ClickManager.ClickGroundDelegate>.Init(factory.Check_ClickManager_ClickGroundDelegate);
		TypeTraits<System.Action<bool,int>>.Init(factory.Check_System_Action_bool_int);
		TypeTraits<System.Action<UnityEngine.GameObject>>.Init(factory.Check_System_Action_UnityEngine_GameObject);
		TypeTraits<System.Action<UnityEngine.Camera,UnityEngine.Vector3,UnityEngine.Quaternion>>.Init(factory.Check_System_Action_UnityEngine_Camera_UnityEngine_Vector3_UnityEngine_Quaternion);
		TypeTraits<Nirvana.SignalDelegate>.Init(factory.Check_Nirvana_SignalDelegate);
		TypeTraits<Nirvana.EventTriggerListener.PointerEventDelegate>.Init(factory.Check_Nirvana_EventTriggerListener_PointerEventDelegate);
		TypeTraits<Nirvana.EventTriggerListener.BaseEventDelegate>.Init(factory.Check_Nirvana_EventTriggerListener_BaseEventDelegate);
		TypeTraits<Nirvana.EventTriggerListener.AxisEventDelegate>.Init(factory.Check_Nirvana_EventTriggerListener_AxisEventDelegate);
		TypeTraits<System.Action<UnityEngine.Texture>>.Init(factory.Check_System_Action_UnityEngine_Texture);
		TypeTraits<System.Action<int,string>>.Init(factory.Check_System_Action_int_string);
		TypeTraits<System.Action<bool,int,string>>.Init(factory.Check_System_Action_bool_int_string);
		TypeTraits<System.Action<bool,string,string>>.Init(factory.Check_System_Action_bool_string_string);
		TypeTraits<System.Action<string,string>>.Init(factory.Check_System_Action_string_string);
		TypeTraits<Nirvana.EnhancedNetClient.DisconnectDelegate>.Init(factory.Check_Nirvana_EnhancedNetClient_DisconnectDelegate);
		TypeTraits<Nirvana.EnhancedNetClient.ReceiveDelegate>.Init(factory.Check_Nirvana_EnhancedNetClient_ReceiveDelegate);
		TypeTraits<Nirvana.EnhancedNetClient.ConnectDelegate>.Init(factory.Check_Nirvana_EnhancedNetClient_ConnectDelegate);
		TypeTraits<Nirvana.EnhancedNetClient.SendDelegate>.Init(factory.Check_Nirvana_EnhancedNetClient_SendDelegate);
		TypeTraits<Nirvana.EnhancedNetClient.DisconnectWithReasonDelegate>.Init(factory.Check_Nirvana_EnhancedNetClient_DisconnectWithReasonDelegate);
		TypeTraits<Nirvana.ListView.GetCellDelegate>.Init(factory.Check_Nirvana_ListView_GetCellDelegate);
		TypeTraits<Nirvana.ListView.CellCountDelegate>.Init(factory.Check_Nirvana_ListView_CellCountDelegate);
		TypeTraits<Nirvana.ListView.GetCellSizeDelegate>.Init(factory.Check_Nirvana_ListView_GetCellSizeDelegate);
		TypeTraits<Nirvana.ListView.RecycleCellDelegate>.Init(factory.Check_Nirvana_ListView_RecycleCellDelegate);
		TypeTraits<UIDrag.DropCallBackAction>.Init(factory.Check_UIDrag_DropCallBackAction);
		TypeTraits<ListViewDelegate.NumberOfCellsDelegate>.Init(factory.Check_ListViewDelegate_NumberOfCellsDelegate);
		TypeTraits<ListViewDelegate.CellViewSizeDelegate>.Init(factory.Check_ListViewDelegate_CellViewSizeDelegate);
		TypeTraits<ListViewDelegate.CellViewDelegate>.Init(factory.Check_ListViewDelegate_CellViewDelegate);
		TypeTraits<ListViewCell.RefreshCell>.Init(factory.Check_ListViewCell_RefreshCell);
		TypeTraits<System.Func<float,bool>>.Init(factory.Check_System_Func_float_bool);
		TypeTraits<EnhancedUI.EnhancedScroller.CellViewVisibilityChangedDelegate>.Init(factory.Check_EnhancedUI_EnhancedScroller_CellViewVisibilityChangedDelegate);
		TypeTraits<EnhancedUI.EnhancedScroller.CellViewWillRecycleDelegate>.Init(factory.Check_EnhancedUI_EnhancedScroller_CellViewWillRecycleDelegate);
		TypeTraits<EnhancedUI.EnhancedScroller.ScrollerScrolledDelegate>.Init(factory.Check_EnhancedUI_EnhancedScroller_ScrollerScrolledDelegate);
		TypeTraits<EnhancedUI.EnhancedScroller.ScrollerSnappedDelegate>.Init(factory.Check_EnhancedUI_EnhancedScroller_ScrollerSnappedDelegate);
		TypeTraits<EnhancedUI.EnhancedScroller.ScrollerScrollingChangedDelegate>.Init(factory.Check_EnhancedUI_EnhancedScroller_ScrollerScrollingChangedDelegate);
		TypeTraits<EnhancedUI.EnhancedScroller.ScrollerTweeningChangedDelegate>.Init(factory.Check_EnhancedUI_EnhancedScroller_ScrollerTweeningChangedDelegate);
		TypeTraits<EnhancedUI.EnhancedScroller.ScrollerScrollingEndDelegate>.Init(factory.Check_EnhancedUI_EnhancedScroller_ScrollerScrollingEndDelegate);
		TypeTraits<EnhancedUI.EnhancedScroller.ScrollerSnapJumpToCenterDelegate>.Init(factory.Check_EnhancedUI_EnhancedScroller_ScrollerSnapJumpToCenterDelegate);
		TypeTraits<ListViewSimpleDelegate.NumberOfCellsDelegate>.Init(factory.Check_ListViewSimpleDelegate_NumberOfCellsDelegate);
		TypeTraits<ListViewSimpleDelegate.CellSizeDelegate>.Init(factory.Check_ListViewSimpleDelegate_CellSizeDelegate);
		TypeTraits<ListViewSimpleDelegate.CellRefreshDelegate>.Init(factory.Check_ListViewSimpleDelegate_CellRefreshDelegate);
		TypeTraits<PageViewSimpleDelegate.NumberOfCellsDelegate>.Init(factory.Check_PageViewSimpleDelegate_NumberOfCellsDelegate);
		TypeTraits<PageViewSimpleDelegate.CellRefreshDelegate>.Init(factory.Check_PageViewSimpleDelegate_CellRefreshDelegate);
		TypeTraits<PageViewSimpleDelegate.CellRecycleDelegate>.Init(factory.Check_PageViewSimpleDelegate_CellRecycleDelegate);
		TypeTraits<System.Action<UnityEngine.GameObject,int>>.Init(factory.Check_System_Action_UnityEngine_GameObject_int);
		TypeTraits<ListenTrigger.TriggerEnterDelegate>.Init(factory.Check_ListenTrigger_TriggerEnterDelegate);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchCancelHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_TouchCancelHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.Cancel2FingersHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_Cancel2FingersHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchStartHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_TouchStartHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchDownHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_TouchDownHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchUpHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_TouchUpHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.SimpleTapHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_SimpleTapHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.DoubleTapHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_DoubleTapHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapStartHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_LongTapStartHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_LongTapHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapEndHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_LongTapEndHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.DragStartHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_DragStartHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.DragHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_DragHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.DragEndHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_DragEndHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeStartHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_SwipeStartHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_SwipeHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeEndHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_SwipeEndHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchStart2FingersHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_TouchStart2FingersHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchDown2FingersHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_TouchDown2FingersHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchUp2FingersHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_TouchUp2FingersHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.SimpleTap2FingersHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_SimpleTap2FingersHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.DoubleTap2FingersHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_DoubleTap2FingersHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapStart2FingersHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_LongTapStart2FingersHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTap2FingersHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_LongTap2FingersHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapEnd2FingersHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_LongTapEnd2FingersHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.TwistHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_TwistHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.TwistEndHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_TwistEndHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.PinchHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_PinchHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.PinchInHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_PinchInHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.PinchOutHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_PinchOutHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.PinchEndHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_PinchEndHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.DragStart2FingersHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_DragStart2FingersHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.Drag2FingersHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_Drag2FingersHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.DragEnd2FingersHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_DragEnd2FingersHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeStart2FingersHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_SwipeStart2FingersHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.Swipe2FingersHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_Swipe2FingersHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeEnd2FingersHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_SwipeEnd2FingersHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.EasyTouchIsReadyHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_EasyTouchIsReadyHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.OverUIElementHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_OverUIElementHandler);
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.UIElementTouchUpHandler>.Init(factory.Check_HedgehogTeam_EasyTouch_EasyTouch_UIElementTouchUpHandler);
		TypeTraits<UnityEngine.Events.UnityAction<string,string,UnityEngine.Object>>.Init(factory.Check_UnityEngine_Events_UnityAction_string_string_UnityEngine_Object);
		TypeTraits<UIGlassBlurCtrl.OnRenderTextureSetup>.Init(factory.Check_UIGlassBlurCtrl_OnRenderTextureSetup);
		TypeTraits<Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateSingle>.Init(factory.Check_Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateSingle);
		TypeTraits<Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateMultiple>.Init(factory.Check_Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateMultiple);
		TypeTraits<Spine.Unity.SkeletonGraphic.SkeletonRendererDelegate>.Init(factory.Check_Spine_Unity_SkeletonGraphic_SkeletonRendererDelegate);
		TypeTraits<Spine.Unity.SkeletonGraphic.InstructionDelegate>.Init(factory.Check_Spine_Unity_SkeletonGraphic_InstructionDelegate);
		TypeTraits<Spine.Unity.ISkeletonAnimationDelegate>.Init(factory.Check_Spine_Unity_ISkeletonAnimationDelegate);
		TypeTraits<Spine.Unity.UpdateBonesDelegate>.Init(factory.Check_Spine_Unity_UpdateBonesDelegate);
		TypeTraits<Spine.Unity.MeshGeneratorDelegate>.Init(factory.Check_Spine_Unity_MeshGeneratorDelegate);
		TypeTraits<Spine.AnimationState.TrackEntryDelegate>.Init(factory.Check_Spine_AnimationState_TrackEntryDelegate);
		TypeTraits<Spine.AnimationState.TrackEntryEventDelegate>.Init(factory.Check_Spine_AnimationState_TrackEntryEventDelegate);
		TypeTraits<System.Predicate<string>>.Init(factory.Check_System_Predicate_string);
		TypeTraits<System.Comparison<string>>.Init(factory.Check_System_Comparison_string);
		TypeTraits<System.Predicate<UnityEngine.Sprite>>.Init(factory.Check_System_Predicate_UnityEngine_Sprite);
		TypeTraits<System.Action<UnityEngine.Sprite>>.Init(factory.Check_System_Action_UnityEngine_Sprite);
		TypeTraits<System.Comparison<UnityEngine.Sprite>>.Init(factory.Check_System_Comparison_UnityEngine_Sprite);
		TypeTraits<UnityEngine.Events.UnityAction<UnityEngine.SpriteRenderer>>.Init(factory.Check_UnityEngine_Events_UnityAction_UnityEngine_SpriteRenderer);
		TypeTraits<System.Action<UnityEngine.ReflectionProbe,UnityEngine.ReflectionProbe.ReflectionProbeEvent>>.Init(factory.Check_System_Action_UnityEngine_ReflectionProbe_UnityEngine_ReflectionProbe_ReflectionProbeEvent);
		TypeTraits<System.Action<UnityEngine.Cubemap>>.Init(factory.Check_System_Action_UnityEngine_Cubemap);
		TypeTraits<CharacterShadows.CharacterShadowManager.OnLightChanged>.Init(factory.Check_CharacterShadows_CharacterShadowManager_OnLightChanged);
		TypeTraits<System.Action<UnityEngine.Rendering.CommandBuffer,UnityEngine.Renderer>>.Init(factory.Check_System_Action_UnityEngine_Rendering_CommandBuffer_UnityEngine_Renderer);
		TypeTraits<FancyScrollView.FancyScrollView<FancyScrollView.NullContext>.CellRefreshDelegate>.Init(factory.Check_FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellRefreshDelegate);
		TypeTraits<FancyScrollView.FancyScrollView<FancyScrollView.NullContext>.CellSelectDelegate>.Init(factory.Check_FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellSelectDelegate);
		TypeTraits<FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshCellDelegate>.Init(factory.Check_FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshCellDelegate);
		TypeTraits<FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshPosDelegate>.Init(factory.Check_FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshPosDelegate);
		TypeTraits<EasingCore.EasingFunction>.Init(factory.Check_EasingCore_EasingFunction);
		TypeTraits<FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellRefreshDelegate>.Init(factory.Check_FancyScrollView_FancyScrollView_FancyScrollView_Context_CellRefreshDelegate);
		TypeTraits<FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellSelectDelegate>.Init(factory.Check_FancyScrollView_FancyScrollView_FancyScrollView_Context_CellSelectDelegate);
		TypeTraits<System.Action<int,FancyScrollView.MovementDirection>>.Init(factory.Check_System_Action_int_FancyScrollView_MovementDirection);
		TypeTraits<FancyScrollView.FancyCell<FancyScrollView.Context>.RefreshCellDelegate>.Init(factory.Check_FancyScrollView_FancyCell_FancyScrollView_Context_RefreshCellDelegate);
		TypeTraits<FancyScrollView.FancyCell<FancyScrollView.Context>.RefreshPosDelegate>.Init(factory.Check_FancyScrollView_FancyCell_FancyScrollView_Context_RefreshPosDelegate);
		TypeTraits<System.Func<UnityEngine.LogType,object,bool>>.Init(factory.Check_System_Func_UnityEngine_LogType_object_bool);
		TypeTraits<DG.Tweening.Core.DOGetter<float>>.Init(factory.Check_DG_Tweening_Core_DOGetter_float);
		TypeTraits<DG.Tweening.Core.DOSetter<float>>.Init(factory.Check_DG_Tweening_Core_DOSetter_float);
		TypeTraits<DG.Tweening.Core.DOGetter<double>>.Init(factory.Check_DG_Tweening_Core_DOGetter_double);
		TypeTraits<DG.Tweening.Core.DOSetter<double>>.Init(factory.Check_DG_Tweening_Core_DOSetter_double);
		TypeTraits<DG.Tweening.Core.DOGetter<int>>.Init(factory.Check_DG_Tweening_Core_DOGetter_int);
		TypeTraits<DG.Tweening.Core.DOSetter<int>>.Init(factory.Check_DG_Tweening_Core_DOSetter_int);
		TypeTraits<DG.Tweening.Core.DOGetter<uint>>.Init(factory.Check_DG_Tweening_Core_DOGetter_uint);
		TypeTraits<DG.Tweening.Core.DOSetter<uint>>.Init(factory.Check_DG_Tweening_Core_DOSetter_uint);
		TypeTraits<DG.Tweening.Core.DOGetter<long>>.Init(factory.Check_DG_Tweening_Core_DOGetter_long);
		TypeTraits<DG.Tweening.Core.DOSetter<long>>.Init(factory.Check_DG_Tweening_Core_DOSetter_long);
		TypeTraits<DG.Tweening.Core.DOGetter<ulong>>.Init(factory.Check_DG_Tweening_Core_DOGetter_ulong);
		TypeTraits<DG.Tweening.Core.DOSetter<ulong>>.Init(factory.Check_DG_Tweening_Core_DOSetter_ulong);
		TypeTraits<DG.Tweening.Core.DOGetter<string>>.Init(factory.Check_DG_Tweening_Core_DOGetter_string);
		TypeTraits<DG.Tweening.Core.DOSetter<string>>.Init(factory.Check_DG_Tweening_Core_DOSetter_string);
		TypeTraits<DG.Tweening.Core.DOGetter<UnityEngine.Vector2>>.Init(factory.Check_DG_Tweening_Core_DOGetter_UnityEngine_Vector2);
		TypeTraits<DG.Tweening.Core.DOSetter<UnityEngine.Vector2>>.Init(factory.Check_DG_Tweening_Core_DOSetter_UnityEngine_Vector2);
		TypeTraits<DG.Tweening.Core.DOGetter<UnityEngine.Vector3>>.Init(factory.Check_DG_Tweening_Core_DOGetter_UnityEngine_Vector3);
		TypeTraits<DG.Tweening.Core.DOSetter<UnityEngine.Vector3>>.Init(factory.Check_DG_Tweening_Core_DOSetter_UnityEngine_Vector3);
		TypeTraits<DG.Tweening.Core.DOGetter<UnityEngine.Vector4>>.Init(factory.Check_DG_Tweening_Core_DOGetter_UnityEngine_Vector4);
		TypeTraits<DG.Tweening.Core.DOSetter<UnityEngine.Vector4>>.Init(factory.Check_DG_Tweening_Core_DOSetter_UnityEngine_Vector4);
		TypeTraits<DG.Tweening.Core.DOGetter<UnityEngine.Quaternion>>.Init(factory.Check_DG_Tweening_Core_DOGetter_UnityEngine_Quaternion);
		TypeTraits<DG.Tweening.Core.DOSetter<UnityEngine.Quaternion>>.Init(factory.Check_DG_Tweening_Core_DOSetter_UnityEngine_Quaternion);
		TypeTraits<DG.Tweening.Core.DOGetter<UnityEngine.Color>>.Init(factory.Check_DG_Tweening_Core_DOGetter_UnityEngine_Color);
		TypeTraits<DG.Tweening.Core.DOSetter<UnityEngine.Color>>.Init(factory.Check_DG_Tweening_Core_DOSetter_UnityEngine_Color);
		TypeTraits<DG.Tweening.Core.DOGetter<UnityEngine.Rect>>.Init(factory.Check_DG_Tweening_Core_DOGetter_UnityEngine_Rect);
		TypeTraits<DG.Tweening.Core.DOSetter<UnityEngine.Rect>>.Init(factory.Check_DG_Tweening_Core_DOSetter_UnityEngine_Rect);
		TypeTraits<DG.Tweening.Core.DOGetter<UnityEngine.RectOffset>>.Init(factory.Check_DG_Tweening_Core_DOGetter_UnityEngine_RectOffset);
		TypeTraits<DG.Tweening.Core.DOSetter<UnityEngine.RectOffset>>.Init(factory.Check_DG_Tweening_Core_DOSetter_UnityEngine_RectOffset);
		TypeTraits<DG.Tweening.TweenCallback<int>>.Init(factory.Check_DG_Tweening_TweenCallback_int);
		TypeTraits<UnityEngine.Camera.CameraCallback>.Init(factory.Check_UnityEngine_Camera_CameraCallback);
		TypeTraits<UnityEngine.Application.AdvertisingIdentifierCallback>.Init(factory.Check_UnityEngine_Application_AdvertisingIdentifierCallback);
		TypeTraits<UnityEngine.Application.LowMemoryCallback>.Init(factory.Check_UnityEngine_Application_LowMemoryCallback);
		TypeTraits<UnityEngine.Application.LogCallback>.Init(factory.Check_UnityEngine_Application_LogCallback);
		TypeTraits<System.Func<bool>>.Init(factory.Check_System_Func_bool);
		TypeTraits<System.Action<UnityEngine.PhysicsScene,Unity.Collections.NativeArray<UnityEngine.ModifiableContactPair>>>.Init(factory.Check_System_Action_UnityEngine_PhysicsScene_Unity_Collections_NativeArray_UnityEngine_ModifiableContactPair);
		TypeTraits<UnityEngine.AudioClip.PCMReaderCallback>.Init(factory.Check_UnityEngine_AudioClip_PCMReaderCallback);
		TypeTraits<UnityEngine.AudioClip.PCMSetPositionCallback>.Init(factory.Check_UnityEngine_AudioClip_PCMSetPositionCallback);
		TypeTraits<UnityEngine.Video.VideoPlayer.EventHandler>.Init(factory.Check_UnityEngine_Video_VideoPlayer_EventHandler);
		TypeTraits<UnityEngine.Video.VideoPlayer.ErrorEventHandler>.Init(factory.Check_UnityEngine_Video_VideoPlayer_ErrorEventHandler);
		TypeTraits<UnityEngine.Video.VideoPlayer.TimeEventHandler>.Init(factory.Check_UnityEngine_Video_VideoPlayer_TimeEventHandler);
		TypeTraits<UnityEngine.Video.VideoPlayer.FrameReadyEventHandler>.Init(factory.Check_UnityEngine_Video_VideoPlayer_FrameReadyEventHandler);
		TypeTraits<EventDelegate.Callback>.Init(factory.Check_EventDelegate_Callback);

		StackTraits<System.Action>.Push = factory.Push_System_Action;
		StackTraits<UnityEngine.Events.UnityAction>.Push = factory.Push_UnityEngine_Events_UnityAction;
		StackTraits<System.Predicate<int>>.Push = factory.Push_System_Predicate_int;
		StackTraits<System.Action<int>>.Push = factory.Push_System_Action_int;
		StackTraits<System.Comparison<int>>.Push = factory.Push_System_Comparison_int;
		StackTraits<System.Func<int,int>>.Push = factory.Push_System_Func_int_int;
		StackTraits<System.Action<float>>.Push = factory.Push_System_Action_float;
		StackTraits<System.Action<string>>.Push = factory.Push_System_Action_string;
		StackTraits<System.Action<int,float,float>>.Push = factory.Push_System_Action_int_float_float;
		StackTraits<System.Action<string,UnityEngine.AnimatorStateInfo>>.Push = factory.Push_System_Action_string_UnityEngine_AnimatorStateInfo;
		StackTraits<System.Action<bool,string>>.Push = factory.Push_System_Action_bool_string;
		StackTraits<DG.Tweening.TweenCallback>.Push = factory.Push_DG_Tweening_TweenCallback;
		StackTraits<NetClientExtensions.ReceiveMessageDelegate>.Push = factory.Push_NetClientExtensions_ReceiveMessageDelegate;
		StackTraits<DG.Tweening.EaseFunction>.Push = factory.Push_DG_Tweening_EaseFunction;
		StackTraits<UnityEngine.RectTransform.ReapplyDrivenProperties>.Push = factory.Push_UnityEngine_RectTransform_ReapplyDrivenProperties;
		StackTraits<UnityEngine.Canvas.WillRenderCanvases>.Push = factory.Push_UnityEngine_Canvas_WillRenderCanvases;
		StackTraits<UnityEngine.Events.UnityAction<bool>>.Push = factory.Push_UnityEngine_Events_UnityAction_bool;
		StackTraits<UnityEngine.Events.UnityAction<UnityEngine.Vector2>>.Push = factory.Push_UnityEngine_Events_UnityAction_UnityEngine_Vector2;
		StackTraits<UnityEngine.UI.InputField.OnValidateInput>.Push = factory.Push_UnityEngine_UI_InputField_OnValidateInput;
		StackTraits<UnityEngine.Events.UnityAction<string>>.Push = factory.Push_UnityEngine_Events_UnityAction_string;
		StackTraits<UnityEngine.Events.UnityAction<int>>.Push = factory.Push_UnityEngine_Events_UnityAction_int;
		StackTraits<System.Action<bool>>.Push = factory.Push_System_Action_bool;
		StackTraits<UnityEngine.Events.UnityAction<float>>.Push = factory.Push_UnityEngine_Events_UnityAction_float;
		StackTraits<UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene,UnityEngine.SceneManagement.LoadSceneMode>>.Push = factory.Push_UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_LoadSceneMode;
		StackTraits<UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene>>.Push = factory.Push_UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene;
		StackTraits<UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene,UnityEngine.SceneManagement.Scene>>.Push = factory.Push_UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_Scene;
		StackTraits<System.Action<UnityEngine.Playables.PlayableDirector>>.Push = factory.Push_System_Action_UnityEngine_Playables_PlayableDirector;
		StackTraits<System.Action<UnityEngine.AsyncOperation>>.Push = factory.Push_System_Action_UnityEngine_AsyncOperation;
		StackTraits<System.Func<int,string,TMPro.TMP_FontAsset>>.Push = factory.Push_System_Func_int_string_TMPro_TMP_FontAsset;
		StackTraits<System.Func<int,string,TMPro.TMP_SpriteAsset>>.Push = factory.Push_System_Func_int_string_TMPro_TMP_SpriteAsset;
		StackTraits<System.Action<TMPro.TMP_TextInfo>>.Push = factory.Push_System_Action_TMPro_TMP_TextInfo;
		StackTraits<TMPro.TMP_InputField.OnValidateInput>.Push = factory.Push_TMPro_TMP_InputField_OnValidateInput;
		StackTraits<System.Action<float,float>>.Push = factory.Push_System_Action_float_float;
		StackTraits<System.Action<UnityEngine.Texture2D>>.Push = factory.Push_System_Action_UnityEngine_Texture2D;
		StackTraits<System.Action<QingGongState>>.Push = factory.Push_System_Action_QingGongState;
		StackTraits<ClickManager.ClickGroundDelegate>.Push = factory.Push_ClickManager_ClickGroundDelegate;
		StackTraits<System.Action<bool,int>>.Push = factory.Push_System_Action_bool_int;
		StackTraits<System.Action<UnityEngine.GameObject>>.Push = factory.Push_System_Action_UnityEngine_GameObject;
		StackTraits<System.Action<UnityEngine.Camera,UnityEngine.Vector3,UnityEngine.Quaternion>>.Push = factory.Push_System_Action_UnityEngine_Camera_UnityEngine_Vector3_UnityEngine_Quaternion;
		StackTraits<Nirvana.SignalDelegate>.Push = factory.Push_Nirvana_SignalDelegate;
		StackTraits<Nirvana.EventTriggerListener.PointerEventDelegate>.Push = factory.Push_Nirvana_EventTriggerListener_PointerEventDelegate;
		StackTraits<Nirvana.EventTriggerListener.BaseEventDelegate>.Push = factory.Push_Nirvana_EventTriggerListener_BaseEventDelegate;
		StackTraits<Nirvana.EventTriggerListener.AxisEventDelegate>.Push = factory.Push_Nirvana_EventTriggerListener_AxisEventDelegate;
		StackTraits<System.Action<UnityEngine.Texture>>.Push = factory.Push_System_Action_UnityEngine_Texture;
		StackTraits<System.Action<int,string>>.Push = factory.Push_System_Action_int_string;
		StackTraits<System.Action<bool,int,string>>.Push = factory.Push_System_Action_bool_int_string;
		StackTraits<System.Action<bool,string,string>>.Push = factory.Push_System_Action_bool_string_string;
		StackTraits<System.Action<string,string>>.Push = factory.Push_System_Action_string_string;
		StackTraits<Nirvana.EnhancedNetClient.DisconnectDelegate>.Push = factory.Push_Nirvana_EnhancedNetClient_DisconnectDelegate;
		StackTraits<Nirvana.EnhancedNetClient.ReceiveDelegate>.Push = factory.Push_Nirvana_EnhancedNetClient_ReceiveDelegate;
		StackTraits<Nirvana.EnhancedNetClient.ConnectDelegate>.Push = factory.Push_Nirvana_EnhancedNetClient_ConnectDelegate;
		StackTraits<Nirvana.EnhancedNetClient.SendDelegate>.Push = factory.Push_Nirvana_EnhancedNetClient_SendDelegate;
		StackTraits<Nirvana.EnhancedNetClient.DisconnectWithReasonDelegate>.Push = factory.Push_Nirvana_EnhancedNetClient_DisconnectWithReasonDelegate;
		StackTraits<Nirvana.ListView.GetCellDelegate>.Push = factory.Push_Nirvana_ListView_GetCellDelegate;
		StackTraits<Nirvana.ListView.CellCountDelegate>.Push = factory.Push_Nirvana_ListView_CellCountDelegate;
		StackTraits<Nirvana.ListView.GetCellSizeDelegate>.Push = factory.Push_Nirvana_ListView_GetCellSizeDelegate;
		StackTraits<Nirvana.ListView.RecycleCellDelegate>.Push = factory.Push_Nirvana_ListView_RecycleCellDelegate;
		StackTraits<UIDrag.DropCallBackAction>.Push = factory.Push_UIDrag_DropCallBackAction;
		StackTraits<ListViewDelegate.NumberOfCellsDelegate>.Push = factory.Push_ListViewDelegate_NumberOfCellsDelegate;
		StackTraits<ListViewDelegate.CellViewSizeDelegate>.Push = factory.Push_ListViewDelegate_CellViewSizeDelegate;
		StackTraits<ListViewDelegate.CellViewDelegate>.Push = factory.Push_ListViewDelegate_CellViewDelegate;
		StackTraits<ListViewCell.RefreshCell>.Push = factory.Push_ListViewCell_RefreshCell;
		StackTraits<System.Func<float,bool>>.Push = factory.Push_System_Func_float_bool;
		StackTraits<EnhancedUI.EnhancedScroller.CellViewVisibilityChangedDelegate>.Push = factory.Push_EnhancedUI_EnhancedScroller_CellViewVisibilityChangedDelegate;
		StackTraits<EnhancedUI.EnhancedScroller.CellViewWillRecycleDelegate>.Push = factory.Push_EnhancedUI_EnhancedScroller_CellViewWillRecycleDelegate;
		StackTraits<EnhancedUI.EnhancedScroller.ScrollerScrolledDelegate>.Push = factory.Push_EnhancedUI_EnhancedScroller_ScrollerScrolledDelegate;
		StackTraits<EnhancedUI.EnhancedScroller.ScrollerSnappedDelegate>.Push = factory.Push_EnhancedUI_EnhancedScroller_ScrollerSnappedDelegate;
		StackTraits<EnhancedUI.EnhancedScroller.ScrollerScrollingChangedDelegate>.Push = factory.Push_EnhancedUI_EnhancedScroller_ScrollerScrollingChangedDelegate;
		StackTraits<EnhancedUI.EnhancedScroller.ScrollerTweeningChangedDelegate>.Push = factory.Push_EnhancedUI_EnhancedScroller_ScrollerTweeningChangedDelegate;
		StackTraits<EnhancedUI.EnhancedScroller.ScrollerScrollingEndDelegate>.Push = factory.Push_EnhancedUI_EnhancedScroller_ScrollerScrollingEndDelegate;
		StackTraits<EnhancedUI.EnhancedScroller.ScrollerSnapJumpToCenterDelegate>.Push = factory.Push_EnhancedUI_EnhancedScroller_ScrollerSnapJumpToCenterDelegate;
		StackTraits<ListViewSimpleDelegate.NumberOfCellsDelegate>.Push = factory.Push_ListViewSimpleDelegate_NumberOfCellsDelegate;
		StackTraits<ListViewSimpleDelegate.CellSizeDelegate>.Push = factory.Push_ListViewSimpleDelegate_CellSizeDelegate;
		StackTraits<ListViewSimpleDelegate.CellRefreshDelegate>.Push = factory.Push_ListViewSimpleDelegate_CellRefreshDelegate;
		StackTraits<PageViewSimpleDelegate.NumberOfCellsDelegate>.Push = factory.Push_PageViewSimpleDelegate_NumberOfCellsDelegate;
		StackTraits<PageViewSimpleDelegate.CellRefreshDelegate>.Push = factory.Push_PageViewSimpleDelegate_CellRefreshDelegate;
		StackTraits<PageViewSimpleDelegate.CellRecycleDelegate>.Push = factory.Push_PageViewSimpleDelegate_CellRecycleDelegate;
		StackTraits<System.Action<UnityEngine.GameObject,int>>.Push = factory.Push_System_Action_UnityEngine_GameObject_int;
		StackTraits<ListenTrigger.TriggerEnterDelegate>.Push = factory.Push_ListenTrigger_TriggerEnterDelegate;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchCancelHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_TouchCancelHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.Cancel2FingersHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_Cancel2FingersHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchStartHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_TouchStartHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchDownHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_TouchDownHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchUpHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_TouchUpHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.SimpleTapHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_SimpleTapHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.DoubleTapHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_DoubleTapHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapStartHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_LongTapStartHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_LongTapHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapEndHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_LongTapEndHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.DragStartHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_DragStartHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.DragHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_DragHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.DragEndHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_DragEndHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeStartHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_SwipeStartHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_SwipeHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeEndHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_SwipeEndHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchStart2FingersHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_TouchStart2FingersHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchDown2FingersHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_TouchDown2FingersHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchUp2FingersHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_TouchUp2FingersHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.SimpleTap2FingersHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_SimpleTap2FingersHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.DoubleTap2FingersHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_DoubleTap2FingersHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapStart2FingersHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_LongTapStart2FingersHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTap2FingersHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_LongTap2FingersHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapEnd2FingersHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_LongTapEnd2FingersHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.TwistHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_TwistHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.TwistEndHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_TwistEndHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.PinchHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_PinchHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.PinchInHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_PinchInHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.PinchOutHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_PinchOutHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.PinchEndHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_PinchEndHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.DragStart2FingersHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_DragStart2FingersHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.Drag2FingersHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_Drag2FingersHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.DragEnd2FingersHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_DragEnd2FingersHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeStart2FingersHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_SwipeStart2FingersHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.Swipe2FingersHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_Swipe2FingersHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeEnd2FingersHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_SwipeEnd2FingersHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.EasyTouchIsReadyHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_EasyTouchIsReadyHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.OverUIElementHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_OverUIElementHandler;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.UIElementTouchUpHandler>.Push = factory.Push_HedgehogTeam_EasyTouch_EasyTouch_UIElementTouchUpHandler;
		StackTraits<UnityEngine.Events.UnityAction<string,string,UnityEngine.Object>>.Push = factory.Push_UnityEngine_Events_UnityAction_string_string_UnityEngine_Object;
		StackTraits<UIGlassBlurCtrl.OnRenderTextureSetup>.Push = factory.Push_UIGlassBlurCtrl_OnRenderTextureSetup;
		StackTraits<Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateSingle>.Push = factory.Push_Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateSingle;
		StackTraits<Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateMultiple>.Push = factory.Push_Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateMultiple;
		StackTraits<Spine.Unity.SkeletonGraphic.SkeletonRendererDelegate>.Push = factory.Push_Spine_Unity_SkeletonGraphic_SkeletonRendererDelegate;
		StackTraits<Spine.Unity.SkeletonGraphic.InstructionDelegate>.Push = factory.Push_Spine_Unity_SkeletonGraphic_InstructionDelegate;
		StackTraits<Spine.Unity.ISkeletonAnimationDelegate>.Push = factory.Push_Spine_Unity_ISkeletonAnimationDelegate;
		StackTraits<Spine.Unity.UpdateBonesDelegate>.Push = factory.Push_Spine_Unity_UpdateBonesDelegate;
		StackTraits<Spine.Unity.MeshGeneratorDelegate>.Push = factory.Push_Spine_Unity_MeshGeneratorDelegate;
		StackTraits<Spine.AnimationState.TrackEntryDelegate>.Push = factory.Push_Spine_AnimationState_TrackEntryDelegate;
		StackTraits<Spine.AnimationState.TrackEntryEventDelegate>.Push = factory.Push_Spine_AnimationState_TrackEntryEventDelegate;
		StackTraits<System.Predicate<string>>.Push = factory.Push_System_Predicate_string;
		StackTraits<System.Comparison<string>>.Push = factory.Push_System_Comparison_string;
		StackTraits<System.Predicate<UnityEngine.Sprite>>.Push = factory.Push_System_Predicate_UnityEngine_Sprite;
		StackTraits<System.Action<UnityEngine.Sprite>>.Push = factory.Push_System_Action_UnityEngine_Sprite;
		StackTraits<System.Comparison<UnityEngine.Sprite>>.Push = factory.Push_System_Comparison_UnityEngine_Sprite;
		StackTraits<UnityEngine.Events.UnityAction<UnityEngine.SpriteRenderer>>.Push = factory.Push_UnityEngine_Events_UnityAction_UnityEngine_SpriteRenderer;
		StackTraits<System.Action<UnityEngine.ReflectionProbe,UnityEngine.ReflectionProbe.ReflectionProbeEvent>>.Push = factory.Push_System_Action_UnityEngine_ReflectionProbe_UnityEngine_ReflectionProbe_ReflectionProbeEvent;
		StackTraits<System.Action<UnityEngine.Cubemap>>.Push = factory.Push_System_Action_UnityEngine_Cubemap;
		StackTraits<CharacterShadows.CharacterShadowManager.OnLightChanged>.Push = factory.Push_CharacterShadows_CharacterShadowManager_OnLightChanged;
		StackTraits<System.Action<UnityEngine.Rendering.CommandBuffer,UnityEngine.Renderer>>.Push = factory.Push_System_Action_UnityEngine_Rendering_CommandBuffer_UnityEngine_Renderer;
		StackTraits<FancyScrollView.FancyScrollView<FancyScrollView.NullContext>.CellRefreshDelegate>.Push = factory.Push_FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellRefreshDelegate;
		StackTraits<FancyScrollView.FancyScrollView<FancyScrollView.NullContext>.CellSelectDelegate>.Push = factory.Push_FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellSelectDelegate;
		StackTraits<FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshCellDelegate>.Push = factory.Push_FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshCellDelegate;
		StackTraits<FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshPosDelegate>.Push = factory.Push_FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshPosDelegate;
		StackTraits<EasingCore.EasingFunction>.Push = factory.Push_EasingCore_EasingFunction;
		StackTraits<FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellRefreshDelegate>.Push = factory.Push_FancyScrollView_FancyScrollView_FancyScrollView_Context_CellRefreshDelegate;
		StackTraits<FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellSelectDelegate>.Push = factory.Push_FancyScrollView_FancyScrollView_FancyScrollView_Context_CellSelectDelegate;
		StackTraits<System.Action<int,FancyScrollView.MovementDirection>>.Push = factory.Push_System_Action_int_FancyScrollView_MovementDirection;
		StackTraits<FancyScrollView.FancyCell<FancyScrollView.Context>.RefreshCellDelegate>.Push = factory.Push_FancyScrollView_FancyCell_FancyScrollView_Context_RefreshCellDelegate;
		StackTraits<FancyScrollView.FancyCell<FancyScrollView.Context>.RefreshPosDelegate>.Push = factory.Push_FancyScrollView_FancyCell_FancyScrollView_Context_RefreshPosDelegate;
		StackTraits<System.Func<UnityEngine.LogType,object,bool>>.Push = factory.Push_System_Func_UnityEngine_LogType_object_bool;
		StackTraits<DG.Tweening.Core.DOGetter<float>>.Push = factory.Push_DG_Tweening_Core_DOGetter_float;
		StackTraits<DG.Tweening.Core.DOSetter<float>>.Push = factory.Push_DG_Tweening_Core_DOSetter_float;
		StackTraits<DG.Tweening.Core.DOGetter<double>>.Push = factory.Push_DG_Tweening_Core_DOGetter_double;
		StackTraits<DG.Tweening.Core.DOSetter<double>>.Push = factory.Push_DG_Tweening_Core_DOSetter_double;
		StackTraits<DG.Tweening.Core.DOGetter<int>>.Push = factory.Push_DG_Tweening_Core_DOGetter_int;
		StackTraits<DG.Tweening.Core.DOSetter<int>>.Push = factory.Push_DG_Tweening_Core_DOSetter_int;
		StackTraits<DG.Tweening.Core.DOGetter<uint>>.Push = factory.Push_DG_Tweening_Core_DOGetter_uint;
		StackTraits<DG.Tweening.Core.DOSetter<uint>>.Push = factory.Push_DG_Tweening_Core_DOSetter_uint;
		StackTraits<DG.Tweening.Core.DOGetter<long>>.Push = factory.Push_DG_Tweening_Core_DOGetter_long;
		StackTraits<DG.Tweening.Core.DOSetter<long>>.Push = factory.Push_DG_Tweening_Core_DOSetter_long;
		StackTraits<DG.Tweening.Core.DOGetter<ulong>>.Push = factory.Push_DG_Tweening_Core_DOGetter_ulong;
		StackTraits<DG.Tweening.Core.DOSetter<ulong>>.Push = factory.Push_DG_Tweening_Core_DOSetter_ulong;
		StackTraits<DG.Tweening.Core.DOGetter<string>>.Push = factory.Push_DG_Tweening_Core_DOGetter_string;
		StackTraits<DG.Tweening.Core.DOSetter<string>>.Push = factory.Push_DG_Tweening_Core_DOSetter_string;
		StackTraits<DG.Tweening.Core.DOGetter<UnityEngine.Vector2>>.Push = factory.Push_DG_Tweening_Core_DOGetter_UnityEngine_Vector2;
		StackTraits<DG.Tweening.Core.DOSetter<UnityEngine.Vector2>>.Push = factory.Push_DG_Tweening_Core_DOSetter_UnityEngine_Vector2;
		StackTraits<DG.Tweening.Core.DOGetter<UnityEngine.Vector3>>.Push = factory.Push_DG_Tweening_Core_DOGetter_UnityEngine_Vector3;
		StackTraits<DG.Tweening.Core.DOSetter<UnityEngine.Vector3>>.Push = factory.Push_DG_Tweening_Core_DOSetter_UnityEngine_Vector3;
		StackTraits<DG.Tweening.Core.DOGetter<UnityEngine.Vector4>>.Push = factory.Push_DG_Tweening_Core_DOGetter_UnityEngine_Vector4;
		StackTraits<DG.Tweening.Core.DOSetter<UnityEngine.Vector4>>.Push = factory.Push_DG_Tweening_Core_DOSetter_UnityEngine_Vector4;
		StackTraits<DG.Tweening.Core.DOGetter<UnityEngine.Quaternion>>.Push = factory.Push_DG_Tweening_Core_DOGetter_UnityEngine_Quaternion;
		StackTraits<DG.Tweening.Core.DOSetter<UnityEngine.Quaternion>>.Push = factory.Push_DG_Tweening_Core_DOSetter_UnityEngine_Quaternion;
		StackTraits<DG.Tweening.Core.DOGetter<UnityEngine.Color>>.Push = factory.Push_DG_Tweening_Core_DOGetter_UnityEngine_Color;
		StackTraits<DG.Tweening.Core.DOSetter<UnityEngine.Color>>.Push = factory.Push_DG_Tweening_Core_DOSetter_UnityEngine_Color;
		StackTraits<DG.Tweening.Core.DOGetter<UnityEngine.Rect>>.Push = factory.Push_DG_Tweening_Core_DOGetter_UnityEngine_Rect;
		StackTraits<DG.Tweening.Core.DOSetter<UnityEngine.Rect>>.Push = factory.Push_DG_Tweening_Core_DOSetter_UnityEngine_Rect;
		StackTraits<DG.Tweening.Core.DOGetter<UnityEngine.RectOffset>>.Push = factory.Push_DG_Tweening_Core_DOGetter_UnityEngine_RectOffset;
		StackTraits<DG.Tweening.Core.DOSetter<UnityEngine.RectOffset>>.Push = factory.Push_DG_Tweening_Core_DOSetter_UnityEngine_RectOffset;
		StackTraits<DG.Tweening.TweenCallback<int>>.Push = factory.Push_DG_Tweening_TweenCallback_int;
		StackTraits<UnityEngine.Camera.CameraCallback>.Push = factory.Push_UnityEngine_Camera_CameraCallback;
		StackTraits<UnityEngine.Application.AdvertisingIdentifierCallback>.Push = factory.Push_UnityEngine_Application_AdvertisingIdentifierCallback;
		StackTraits<UnityEngine.Application.LowMemoryCallback>.Push = factory.Push_UnityEngine_Application_LowMemoryCallback;
		StackTraits<UnityEngine.Application.LogCallback>.Push = factory.Push_UnityEngine_Application_LogCallback;
		StackTraits<System.Func<bool>>.Push = factory.Push_System_Func_bool;
		StackTraits<System.Action<UnityEngine.PhysicsScene,Unity.Collections.NativeArray<UnityEngine.ModifiableContactPair>>>.Push = factory.Push_System_Action_UnityEngine_PhysicsScene_Unity_Collections_NativeArray_UnityEngine_ModifiableContactPair;
		StackTraits<UnityEngine.AudioClip.PCMReaderCallback>.Push = factory.Push_UnityEngine_AudioClip_PCMReaderCallback;
		StackTraits<UnityEngine.AudioClip.PCMSetPositionCallback>.Push = factory.Push_UnityEngine_AudioClip_PCMSetPositionCallback;
		StackTraits<UnityEngine.Video.VideoPlayer.EventHandler>.Push = factory.Push_UnityEngine_Video_VideoPlayer_EventHandler;
		StackTraits<UnityEngine.Video.VideoPlayer.ErrorEventHandler>.Push = factory.Push_UnityEngine_Video_VideoPlayer_ErrorEventHandler;
		StackTraits<UnityEngine.Video.VideoPlayer.TimeEventHandler>.Push = factory.Push_UnityEngine_Video_VideoPlayer_TimeEventHandler;
		StackTraits<UnityEngine.Video.VideoPlayer.FrameReadyEventHandler>.Push = factory.Push_UnityEngine_Video_VideoPlayer_FrameReadyEventHandler;
		StackTraits<EventDelegate.Callback>.Push = factory.Push_EventDelegate_Callback;
	}
    
    public static Delegate CreateDelegate(Type t, LuaFunction func = null)
    {
        DelegateCreate Create = null;

        if (!dict.TryGetValue(t, out Create))
        {
            throw new LuaException(string.Format("Delegate {0} not register", LuaMisc.GetTypeName(t)));            
        }

        if (func != null)
        {
            LuaState state = func.GetLuaState();
            LuaDelegate target = state.GetLuaDelegate(func);
            
            if (target != null)
            {
                return Delegate.CreateDelegate(t, target, target.method);
            }  
            else
            {
                Delegate d = Create(func, null, false);
                target = d.Target as LuaDelegate;
                state.AddLuaDelegate(target, func);
                return d;
            }       
        }

        return Create(null, null, false);        
    }
    
    public static Delegate CreateDelegate(Type t, LuaFunction func, LuaTable self)
    {
        DelegateCreate Create = null;

        if (!dict.TryGetValue(t, out Create))
        {
            throw new LuaException(string.Format("Delegate {0} not register", LuaMisc.GetTypeName(t)));
        }

        if (func != null)
        {
            LuaState state = func.GetLuaState();
            LuaDelegate target = state.GetLuaDelegate(func, self);

            if (target != null)
            {
                return Delegate.CreateDelegate(t, target, target.method);
            }
            else
            {
                Delegate d = Create(func, self, true);
                target = d.Target as LuaDelegate;
                state.AddLuaDelegate(target, func, self);
                return d;
            }
        }

        return Create(null, null, true);
    }
    
    public static Delegate RemoveDelegate(Delegate obj, LuaFunction func)
    {
        LuaState state = func.GetLuaState();
        Delegate[] ds = obj.GetInvocationList();

        for (int i = 0; i < ds.Length; i++)
        {
            LuaDelegate ld = ds[i].Target as LuaDelegate;

            if (ld != null && ld.func == func)
            {
                obj = Delegate.Remove(obj, ds[i]);
                state.DelayDispose(ld.func);
                break;
            }
        }

        return obj;
    }
    
    public static Delegate RemoveDelegate(Delegate obj, Delegate dg)
    {
        LuaDelegate remove = dg.Target as LuaDelegate;

        if (remove == null)
        {
            obj = Delegate.Remove(obj, dg);
            return obj;
        }

        LuaState state = remove.func.GetLuaState();
        Delegate[] ds = obj.GetInvocationList();        

        for (int i = 0; i < ds.Length; i++)
        {
            LuaDelegate ld = ds[i].Target as LuaDelegate;

            if (ld != null && ld == remove)
            {
                obj = Delegate.Remove(obj, ds[i]);
                state.DelayDispose(ld.func);
                state.DelayDispose(ld.self);
                break;
            }
        }

        return obj;
    }

	class System_Action_Event : LuaDelegate
	{
		public System_Action_Event(LuaFunction func) : base(func) { }
		public System_Action_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call()
		{
			func.Call();
		}

		public void CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action System_Action(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action fn = delegate() { };
			return fn;
		}

		if(!flag)
		{
			System_Action_Event target = new System_Action_Event(func);
			System.Action d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_Event target = new System_Action_Event(func, self);
			System.Action d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action), L, pos);
	}

	void Push_System_Action(IntPtr L, System.Action o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_Events_UnityAction_Event : LuaDelegate
	{
		public UnityEngine_Events_UnityAction_Event(LuaFunction func) : base(func) { }
		public UnityEngine_Events_UnityAction_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call()
		{
			func.Call();
		}

		public void CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			func.EndPCall();
		}
	}

	public UnityEngine.Events.UnityAction UnityEngine_Events_UnityAction(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.Events.UnityAction fn = delegate() { };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_Events_UnityAction_Event target = new UnityEngine_Events_UnityAction_Event(func);
			UnityEngine.Events.UnityAction d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_Events_UnityAction_Event target = new UnityEngine_Events_UnityAction_Event(func, self);
			UnityEngine.Events.UnityAction d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_Events_UnityAction(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.Events.UnityAction), L, pos);
	}

	void Push_UnityEngine_Events_UnityAction(IntPtr L, UnityEngine.Events.UnityAction o)
	{
		ToLua.Push(L, o);
	}

	class System_Predicate_int_Event : LuaDelegate
	{
		public System_Predicate_int_Event(LuaFunction func) : base(func) { }
		public System_Predicate_int_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public bool Call(int param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			bool ret = func.CheckBoolean();
			func.EndPCall();
			return ret;
		}

		public bool CallWithSelf(int param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			bool ret = func.CheckBoolean();
			func.EndPCall();
			return ret;
		}
	}

	public System.Predicate<int> System_Predicate_int(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Predicate<int> fn = delegate(int param0) { return false; };
			return fn;
		}

		if(!flag)
		{
			System_Predicate_int_Event target = new System_Predicate_int_Event(func);
			System.Predicate<int> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Predicate_int_Event target = new System_Predicate_int_Event(func, self);
			System.Predicate<int> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Predicate_int(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Predicate<int>), L, pos);
	}

	void Push_System_Predicate_int(IntPtr L, System.Predicate<int> o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_int_Event : LuaDelegate
	{
		public System_Action_int_Event(LuaFunction func) : base(func) { }
		public System_Action_int_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(int param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(int param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<int> System_Action_int(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<int> fn = delegate(int param0) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_int_Event target = new System_Action_int_Event(func);
			System.Action<int> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_int_Event target = new System_Action_int_Event(func, self);
			System.Action<int> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_int(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<int>), L, pos);
	}

	void Push_System_Action_int(IntPtr L, System.Action<int> o)
	{
		ToLua.Push(L, o);
	}

	class System_Comparison_int_Event : LuaDelegate
	{
		public System_Comparison_int_Event(LuaFunction func) : base(func) { }
		public System_Comparison_int_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public int Call(int param0, int param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			int ret = (int)func.CheckNumber();
			func.EndPCall();
			return ret;
		}

		public int CallWithSelf(int param0, int param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			int ret = (int)func.CheckNumber();
			func.EndPCall();
			return ret;
		}
	}

	public System.Comparison<int> System_Comparison_int(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Comparison<int> fn = delegate(int param0, int param1) { return 0; };
			return fn;
		}

		if(!flag)
		{
			System_Comparison_int_Event target = new System_Comparison_int_Event(func);
			System.Comparison<int> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Comparison_int_Event target = new System_Comparison_int_Event(func, self);
			System.Comparison<int> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Comparison_int(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Comparison<int>), L, pos);
	}

	void Push_System_Comparison_int(IntPtr L, System.Comparison<int> o)
	{
		ToLua.Push(L, o);
	}

	class System_Func_int_int_Event : LuaDelegate
	{
		public System_Func_int_int_Event(LuaFunction func) : base(func) { }
		public System_Func_int_int_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public int Call(int param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			int ret = (int)func.CheckNumber();
			func.EndPCall();
			return ret;
		}

		public int CallWithSelf(int param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			int ret = (int)func.CheckNumber();
			func.EndPCall();
			return ret;
		}
	}

	public System.Func<int,int> System_Func_int_int(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Func<int,int> fn = delegate(int param0) { return 0; };
			return fn;
		}

		if(!flag)
		{
			System_Func_int_int_Event target = new System_Func_int_int_Event(func);
			System.Func<int,int> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Func_int_int_Event target = new System_Func_int_int_Event(func, self);
			System.Func<int,int> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Func_int_int(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Func<int,int>), L, pos);
	}

	void Push_System_Func_int_int(IntPtr L, System.Func<int,int> o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_float_Event : LuaDelegate
	{
		public System_Action_float_Event(LuaFunction func) : base(func) { }
		public System_Action_float_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(float param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(float param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<float> System_Action_float(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<float> fn = delegate(float param0) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_float_Event target = new System_Action_float_Event(func);
			System.Action<float> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_float_Event target = new System_Action_float_Event(func, self);
			System.Action<float> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_float(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<float>), L, pos);
	}

	void Push_System_Action_float(IntPtr L, System.Action<float> o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_string_Event : LuaDelegate
	{
		public System_Action_string_Event(LuaFunction func) : base(func) { }
		public System_Action_string_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(string param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(string param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<string> System_Action_string(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<string> fn = delegate(string param0) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_string_Event target = new System_Action_string_Event(func);
			System.Action<string> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_string_Event target = new System_Action_string_Event(func, self);
			System.Action<string> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_string(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<string>), L, pos);
	}

	void Push_System_Action_string(IntPtr L, System.Action<string> o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_int_float_float_Event : LuaDelegate
	{
		public System_Action_int_float_float_Event(LuaFunction func) : base(func) { }
		public System_Action_int_float_float_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(int param0, float param1, float param2)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(int param0, float param1, float param2)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<int,float,float> System_Action_int_float_float(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<int,float,float> fn = delegate(int param0, float param1, float param2) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_int_float_float_Event target = new System_Action_int_float_float_Event(func);
			System.Action<int,float,float> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_int_float_float_Event target = new System_Action_int_float_float_Event(func, self);
			System.Action<int,float,float> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_int_float_float(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<int,float,float>), L, pos);
	}

	void Push_System_Action_int_float_float(IntPtr L, System.Action<int,float,float> o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_string_UnityEngine_AnimatorStateInfo_Event : LuaDelegate
	{
		public System_Action_string_UnityEngine_AnimatorStateInfo_Event(LuaFunction func) : base(func) { }
		public System_Action_string_UnityEngine_AnimatorStateInfo_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(string param0, UnityEngine.AnimatorStateInfo param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PushValue(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(string param0, UnityEngine.AnimatorStateInfo param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PushValue(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<string,UnityEngine.AnimatorStateInfo> System_Action_string_UnityEngine_AnimatorStateInfo(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<string,UnityEngine.AnimatorStateInfo> fn = delegate(string param0, UnityEngine.AnimatorStateInfo param1) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_string_UnityEngine_AnimatorStateInfo_Event target = new System_Action_string_UnityEngine_AnimatorStateInfo_Event(func);
			System.Action<string,UnityEngine.AnimatorStateInfo> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_string_UnityEngine_AnimatorStateInfo_Event target = new System_Action_string_UnityEngine_AnimatorStateInfo_Event(func, self);
			System.Action<string,UnityEngine.AnimatorStateInfo> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_string_UnityEngine_AnimatorStateInfo(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<string,UnityEngine.AnimatorStateInfo>), L, pos);
	}

	void Push_System_Action_string_UnityEngine_AnimatorStateInfo(IntPtr L, System.Action<string,UnityEngine.AnimatorStateInfo> o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_bool_string_Event : LuaDelegate
	{
		public System_Action_bool_string_Event(LuaFunction func) : base(func) { }
		public System_Action_bool_string_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(bool param0, string param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(bool param0, string param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<bool,string> System_Action_bool_string(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<bool,string> fn = delegate(bool param0, string param1) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_bool_string_Event target = new System_Action_bool_string_Event(func);
			System.Action<bool,string> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_bool_string_Event target = new System_Action_bool_string_Event(func, self);
			System.Action<bool,string> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_bool_string(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<bool,string>), L, pos);
	}

	void Push_System_Action_bool_string(IntPtr L, System.Action<bool,string> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_TweenCallback_Event : LuaDelegate
	{
		public DG_Tweening_TweenCallback_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_TweenCallback_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call()
		{
			func.Call();
		}

		public void CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			func.EndPCall();
		}
	}

	public DG.Tweening.TweenCallback DG_Tweening_TweenCallback(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.TweenCallback fn = delegate() { };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_TweenCallback_Event target = new DG_Tweening_TweenCallback_Event(func);
			DG.Tweening.TweenCallback d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_TweenCallback_Event target = new DG_Tweening_TweenCallback_Event(func, self);
			DG.Tweening.TweenCallback d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_TweenCallback(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.TweenCallback), L, pos);
	}

	void Push_DG_Tweening_TweenCallback(IntPtr L, DG.Tweening.TweenCallback o)
	{
		ToLua.Push(L, o);
	}

	class NetClientExtensions_ReceiveMessageDelegate_Event : LuaDelegate
	{
		public NetClientExtensions_ReceiveMessageDelegate_Event(LuaFunction func) : base(func) { }
		public NetClientExtensions_ReceiveMessageDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(LuaInterface.LuaByteBuffer param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(LuaInterface.LuaByteBuffer param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public NetClientExtensions.ReceiveMessageDelegate NetClientExtensions_ReceiveMessageDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			NetClientExtensions.ReceiveMessageDelegate fn = delegate(LuaInterface.LuaByteBuffer param0) { };
			return fn;
		}

		if(!flag)
		{
			NetClientExtensions_ReceiveMessageDelegate_Event target = new NetClientExtensions_ReceiveMessageDelegate_Event(func);
			NetClientExtensions.ReceiveMessageDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			NetClientExtensions_ReceiveMessageDelegate_Event target = new NetClientExtensions_ReceiveMessageDelegate_Event(func, self);
			NetClientExtensions.ReceiveMessageDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_NetClientExtensions_ReceiveMessageDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(NetClientExtensions.ReceiveMessageDelegate), L, pos);
	}

	void Push_NetClientExtensions_ReceiveMessageDelegate(IntPtr L, NetClientExtensions.ReceiveMessageDelegate o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_EaseFunction_Event : LuaDelegate
	{
		public DG_Tweening_EaseFunction_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_EaseFunction_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public float Call(float param0, float param1, float param2, float param3)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.Push(param3);
			func.PCall();
			float ret = (float)func.CheckNumber();
			func.EndPCall();
			return ret;
		}

		public float CallWithSelf(float param0, float param1, float param2, float param3)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.Push(param3);
			func.PCall();
			float ret = (float)func.CheckNumber();
			func.EndPCall();
			return ret;
		}
	}

	public DG.Tweening.EaseFunction DG_Tweening_EaseFunction(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.EaseFunction fn = delegate(float param0, float param1, float param2, float param3) { return 0; };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_EaseFunction_Event target = new DG_Tweening_EaseFunction_Event(func);
			DG.Tweening.EaseFunction d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_EaseFunction_Event target = new DG_Tweening_EaseFunction_Event(func, self);
			DG.Tweening.EaseFunction d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_EaseFunction(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.EaseFunction), L, pos);
	}

	void Push_DG_Tweening_EaseFunction(IntPtr L, DG.Tweening.EaseFunction o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_RectTransform_ReapplyDrivenProperties_Event : LuaDelegate
	{
		public UnityEngine_RectTransform_ReapplyDrivenProperties_Event(LuaFunction func) : base(func) { }
		public UnityEngine_RectTransform_ReapplyDrivenProperties_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.RectTransform param0)
		{
			func.BeginPCall();
			func.PushSealed(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.RectTransform param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushSealed(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public UnityEngine.RectTransform.ReapplyDrivenProperties UnityEngine_RectTransform_ReapplyDrivenProperties(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.RectTransform.ReapplyDrivenProperties fn = delegate(UnityEngine.RectTransform param0) { };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_RectTransform_ReapplyDrivenProperties_Event target = new UnityEngine_RectTransform_ReapplyDrivenProperties_Event(func);
			UnityEngine.RectTransform.ReapplyDrivenProperties d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_RectTransform_ReapplyDrivenProperties_Event target = new UnityEngine_RectTransform_ReapplyDrivenProperties_Event(func, self);
			UnityEngine.RectTransform.ReapplyDrivenProperties d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_RectTransform_ReapplyDrivenProperties(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.RectTransform.ReapplyDrivenProperties), L, pos);
	}

	void Push_UnityEngine_RectTransform_ReapplyDrivenProperties(IntPtr L, UnityEngine.RectTransform.ReapplyDrivenProperties o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_Canvas_WillRenderCanvases_Event : LuaDelegate
	{
		public UnityEngine_Canvas_WillRenderCanvases_Event(LuaFunction func) : base(func) { }
		public UnityEngine_Canvas_WillRenderCanvases_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call()
		{
			func.Call();
		}

		public void CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			func.EndPCall();
		}
	}

	public UnityEngine.Canvas.WillRenderCanvases UnityEngine_Canvas_WillRenderCanvases(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.Canvas.WillRenderCanvases fn = delegate() { };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_Canvas_WillRenderCanvases_Event target = new UnityEngine_Canvas_WillRenderCanvases_Event(func);
			UnityEngine.Canvas.WillRenderCanvases d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_Canvas_WillRenderCanvases_Event target = new UnityEngine_Canvas_WillRenderCanvases_Event(func, self);
			UnityEngine.Canvas.WillRenderCanvases d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_Canvas_WillRenderCanvases(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.Canvas.WillRenderCanvases), L, pos);
	}

	void Push_UnityEngine_Canvas_WillRenderCanvases(IntPtr L, UnityEngine.Canvas.WillRenderCanvases o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_Events_UnityAction_bool_Event : LuaDelegate
	{
		public UnityEngine_Events_UnityAction_bool_Event(LuaFunction func) : base(func) { }
		public UnityEngine_Events_UnityAction_bool_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(bool param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(bool param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public UnityEngine.Events.UnityAction<bool> UnityEngine_Events_UnityAction_bool(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.Events.UnityAction<bool> fn = delegate(bool param0) { };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_Events_UnityAction_bool_Event target = new UnityEngine_Events_UnityAction_bool_Event(func);
			UnityEngine.Events.UnityAction<bool> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_Events_UnityAction_bool_Event target = new UnityEngine_Events_UnityAction_bool_Event(func, self);
			UnityEngine.Events.UnityAction<bool> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_Events_UnityAction_bool(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.Events.UnityAction<bool>), L, pos);
	}

	void Push_UnityEngine_Events_UnityAction_bool(IntPtr L, UnityEngine.Events.UnityAction<bool> o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_Events_UnityAction_UnityEngine_Vector2_Event : LuaDelegate
	{
		public UnityEngine_Events_UnityAction_UnityEngine_Vector2_Event(LuaFunction func) : base(func) { }
		public UnityEngine_Events_UnityAction_UnityEngine_Vector2_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.Vector2 param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.Vector2 param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public UnityEngine.Events.UnityAction<UnityEngine.Vector2> UnityEngine_Events_UnityAction_UnityEngine_Vector2(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.Events.UnityAction<UnityEngine.Vector2> fn = delegate(UnityEngine.Vector2 param0) { };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_Events_UnityAction_UnityEngine_Vector2_Event target = new UnityEngine_Events_UnityAction_UnityEngine_Vector2_Event(func);
			UnityEngine.Events.UnityAction<UnityEngine.Vector2> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_Events_UnityAction_UnityEngine_Vector2_Event target = new UnityEngine_Events_UnityAction_UnityEngine_Vector2_Event(func, self);
			UnityEngine.Events.UnityAction<UnityEngine.Vector2> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_Events_UnityAction_UnityEngine_Vector2(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.Events.UnityAction<UnityEngine.Vector2>), L, pos);
	}

	void Push_UnityEngine_Events_UnityAction_UnityEngine_Vector2(IntPtr L, UnityEngine.Events.UnityAction<UnityEngine.Vector2> o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_UI_InputField_OnValidateInput_Event : LuaDelegate
	{
		public UnityEngine_UI_InputField_OnValidateInput_Event(LuaFunction func) : base(func) { }
		public UnityEngine_UI_InputField_OnValidateInput_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public char Call(string param0, int param1, char param2)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			char ret = (char)func.CheckNumber();
			func.EndPCall();
			return ret;
		}

		public char CallWithSelf(string param0, int param1, char param2)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			char ret = (char)func.CheckNumber();
			func.EndPCall();
			return ret;
		}
	}

	public UnityEngine.UI.InputField.OnValidateInput UnityEngine_UI_InputField_OnValidateInput(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.UI.InputField.OnValidateInput fn = delegate(string param0, int param1, char param2) { return '\0'; };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_UI_InputField_OnValidateInput_Event target = new UnityEngine_UI_InputField_OnValidateInput_Event(func);
			UnityEngine.UI.InputField.OnValidateInput d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_UI_InputField_OnValidateInput_Event target = new UnityEngine_UI_InputField_OnValidateInput_Event(func, self);
			UnityEngine.UI.InputField.OnValidateInput d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_UI_InputField_OnValidateInput(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.UI.InputField.OnValidateInput), L, pos);
	}

	void Push_UnityEngine_UI_InputField_OnValidateInput(IntPtr L, UnityEngine.UI.InputField.OnValidateInput o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_Events_UnityAction_string_Event : LuaDelegate
	{
		public UnityEngine_Events_UnityAction_string_Event(LuaFunction func) : base(func) { }
		public UnityEngine_Events_UnityAction_string_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(string param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(string param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public UnityEngine.Events.UnityAction<string> UnityEngine_Events_UnityAction_string(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.Events.UnityAction<string> fn = delegate(string param0) { };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_Events_UnityAction_string_Event target = new UnityEngine_Events_UnityAction_string_Event(func);
			UnityEngine.Events.UnityAction<string> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_Events_UnityAction_string_Event target = new UnityEngine_Events_UnityAction_string_Event(func, self);
			UnityEngine.Events.UnityAction<string> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_Events_UnityAction_string(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.Events.UnityAction<string>), L, pos);
	}

	void Push_UnityEngine_Events_UnityAction_string(IntPtr L, UnityEngine.Events.UnityAction<string> o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_Events_UnityAction_int_Event : LuaDelegate
	{
		public UnityEngine_Events_UnityAction_int_Event(LuaFunction func) : base(func) { }
		public UnityEngine_Events_UnityAction_int_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(int param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(int param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public UnityEngine.Events.UnityAction<int> UnityEngine_Events_UnityAction_int(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.Events.UnityAction<int> fn = delegate(int param0) { };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_Events_UnityAction_int_Event target = new UnityEngine_Events_UnityAction_int_Event(func);
			UnityEngine.Events.UnityAction<int> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_Events_UnityAction_int_Event target = new UnityEngine_Events_UnityAction_int_Event(func, self);
			UnityEngine.Events.UnityAction<int> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_Events_UnityAction_int(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.Events.UnityAction<int>), L, pos);
	}

	void Push_UnityEngine_Events_UnityAction_int(IntPtr L, UnityEngine.Events.UnityAction<int> o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_bool_Event : LuaDelegate
	{
		public System_Action_bool_Event(LuaFunction func) : base(func) { }
		public System_Action_bool_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(bool param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(bool param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<bool> System_Action_bool(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<bool> fn = delegate(bool param0) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_bool_Event target = new System_Action_bool_Event(func);
			System.Action<bool> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_bool_Event target = new System_Action_bool_Event(func, self);
			System.Action<bool> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_bool(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<bool>), L, pos);
	}

	void Push_System_Action_bool(IntPtr L, System.Action<bool> o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_Events_UnityAction_float_Event : LuaDelegate
	{
		public UnityEngine_Events_UnityAction_float_Event(LuaFunction func) : base(func) { }
		public UnityEngine_Events_UnityAction_float_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(float param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(float param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public UnityEngine.Events.UnityAction<float> UnityEngine_Events_UnityAction_float(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.Events.UnityAction<float> fn = delegate(float param0) { };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_Events_UnityAction_float_Event target = new UnityEngine_Events_UnityAction_float_Event(func);
			UnityEngine.Events.UnityAction<float> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_Events_UnityAction_float_Event target = new UnityEngine_Events_UnityAction_float_Event(func, self);
			UnityEngine.Events.UnityAction<float> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_Events_UnityAction_float(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.Events.UnityAction<float>), L, pos);
	}

	void Push_UnityEngine_Events_UnityAction_float(IntPtr L, UnityEngine.Events.UnityAction<float> o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_LoadSceneMode_Event : LuaDelegate
	{
		public UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_LoadSceneMode_Event(LuaFunction func) : base(func) { }
		public UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_LoadSceneMode_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.SceneManagement.Scene param0, UnityEngine.SceneManagement.LoadSceneMode param1)
		{
			func.BeginPCall();
			func.PushValue(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.SceneManagement.Scene param0, UnityEngine.SceneManagement.LoadSceneMode param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushValue(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene,UnityEngine.SceneManagement.LoadSceneMode> UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_LoadSceneMode(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene,UnityEngine.SceneManagement.LoadSceneMode> fn = delegate(UnityEngine.SceneManagement.Scene param0, UnityEngine.SceneManagement.LoadSceneMode param1) { };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_LoadSceneMode_Event target = new UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_LoadSceneMode_Event(func);
			UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene,UnityEngine.SceneManagement.LoadSceneMode> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_LoadSceneMode_Event target = new UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_LoadSceneMode_Event(func, self);
			UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene,UnityEngine.SceneManagement.LoadSceneMode> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_LoadSceneMode(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene,UnityEngine.SceneManagement.LoadSceneMode>), L, pos);
	}

	void Push_UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_LoadSceneMode(IntPtr L, UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene,UnityEngine.SceneManagement.LoadSceneMode> o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_Event : LuaDelegate
	{
		public UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_Event(LuaFunction func) : base(func) { }
		public UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.SceneManagement.Scene param0)
		{
			func.BeginPCall();
			func.PushValue(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.SceneManagement.Scene param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushValue(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene> UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene> fn = delegate(UnityEngine.SceneManagement.Scene param0) { };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_Event target = new UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_Event(func);
			UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_Event target = new UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_Event(func, self);
			UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene>), L, pos);
	}

	void Push_UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene(IntPtr L, UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene> o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_Scene_Event : LuaDelegate
	{
		public UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_Scene_Event(LuaFunction func) : base(func) { }
		public UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_Scene_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.SceneManagement.Scene param0, UnityEngine.SceneManagement.Scene param1)
		{
			func.BeginPCall();
			func.PushValue(param0);
			func.PushValue(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.SceneManagement.Scene param0, UnityEngine.SceneManagement.Scene param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushValue(param0);
			func.PushValue(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene,UnityEngine.SceneManagement.Scene> UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_Scene(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene,UnityEngine.SceneManagement.Scene> fn = delegate(UnityEngine.SceneManagement.Scene param0, UnityEngine.SceneManagement.Scene param1) { };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_Scene_Event target = new UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_Scene_Event(func);
			UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene,UnityEngine.SceneManagement.Scene> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_Scene_Event target = new UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_Scene_Event(func, self);
			UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene,UnityEngine.SceneManagement.Scene> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_Scene(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene,UnityEngine.SceneManagement.Scene>), L, pos);
	}

	void Push_UnityEngine_Events_UnityAction_UnityEngine_SceneManagement_Scene_UnityEngine_SceneManagement_Scene(IntPtr L, UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene,UnityEngine.SceneManagement.Scene> o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_UnityEngine_Playables_PlayableDirector_Event : LuaDelegate
	{
		public System_Action_UnityEngine_Playables_PlayableDirector_Event(LuaFunction func) : base(func) { }
		public System_Action_UnityEngine_Playables_PlayableDirector_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.Playables.PlayableDirector param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.Playables.PlayableDirector param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<UnityEngine.Playables.PlayableDirector> System_Action_UnityEngine_Playables_PlayableDirector(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<UnityEngine.Playables.PlayableDirector> fn = delegate(UnityEngine.Playables.PlayableDirector param0) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_UnityEngine_Playables_PlayableDirector_Event target = new System_Action_UnityEngine_Playables_PlayableDirector_Event(func);
			System.Action<UnityEngine.Playables.PlayableDirector> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_UnityEngine_Playables_PlayableDirector_Event target = new System_Action_UnityEngine_Playables_PlayableDirector_Event(func, self);
			System.Action<UnityEngine.Playables.PlayableDirector> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_UnityEngine_Playables_PlayableDirector(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<UnityEngine.Playables.PlayableDirector>), L, pos);
	}

	void Push_System_Action_UnityEngine_Playables_PlayableDirector(IntPtr L, System.Action<UnityEngine.Playables.PlayableDirector> o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_UnityEngine_AsyncOperation_Event : LuaDelegate
	{
		public System_Action_UnityEngine_AsyncOperation_Event(LuaFunction func) : base(func) { }
		public System_Action_UnityEngine_AsyncOperation_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.AsyncOperation param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.AsyncOperation param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<UnityEngine.AsyncOperation> System_Action_UnityEngine_AsyncOperation(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<UnityEngine.AsyncOperation> fn = delegate(UnityEngine.AsyncOperation param0) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_UnityEngine_AsyncOperation_Event target = new System_Action_UnityEngine_AsyncOperation_Event(func);
			System.Action<UnityEngine.AsyncOperation> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_UnityEngine_AsyncOperation_Event target = new System_Action_UnityEngine_AsyncOperation_Event(func, self);
			System.Action<UnityEngine.AsyncOperation> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_UnityEngine_AsyncOperation(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<UnityEngine.AsyncOperation>), L, pos);
	}

	void Push_System_Action_UnityEngine_AsyncOperation(IntPtr L, System.Action<UnityEngine.AsyncOperation> o)
	{
		ToLua.Push(L, o);
	}

	class System_Func_int_string_TMPro_TMP_FontAsset_Event : LuaDelegate
	{
		public System_Func_int_string_TMPro_TMP_FontAsset_Event(LuaFunction func) : base(func) { }
		public System_Func_int_string_TMPro_TMP_FontAsset_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public TMPro.TMP_FontAsset Call(int param0, string param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			TMPro.TMP_FontAsset ret = (TMPro.TMP_FontAsset)func.CheckObject(typeof(TMPro.TMP_FontAsset));
			func.EndPCall();
			return ret;
		}

		public TMPro.TMP_FontAsset CallWithSelf(int param0, string param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			TMPro.TMP_FontAsset ret = (TMPro.TMP_FontAsset)func.CheckObject(typeof(TMPro.TMP_FontAsset));
			func.EndPCall();
			return ret;
		}
	}

	public System.Func<int,string,TMPro.TMP_FontAsset> System_Func_int_string_TMPro_TMP_FontAsset(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Func<int,string,TMPro.TMP_FontAsset> fn = delegate(int param0, string param1) { return null; };
			return fn;
		}

		if(!flag)
		{
			System_Func_int_string_TMPro_TMP_FontAsset_Event target = new System_Func_int_string_TMPro_TMP_FontAsset_Event(func);
			System.Func<int,string,TMPro.TMP_FontAsset> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Func_int_string_TMPro_TMP_FontAsset_Event target = new System_Func_int_string_TMPro_TMP_FontAsset_Event(func, self);
			System.Func<int,string,TMPro.TMP_FontAsset> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Func_int_string_TMPro_TMP_FontAsset(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Func<int,string,TMPro.TMP_FontAsset>), L, pos);
	}

	void Push_System_Func_int_string_TMPro_TMP_FontAsset(IntPtr L, System.Func<int,string,TMPro.TMP_FontAsset> o)
	{
		ToLua.Push(L, o);
	}

	class System_Func_int_string_TMPro_TMP_SpriteAsset_Event : LuaDelegate
	{
		public System_Func_int_string_TMPro_TMP_SpriteAsset_Event(LuaFunction func) : base(func) { }
		public System_Func_int_string_TMPro_TMP_SpriteAsset_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public TMPro.TMP_SpriteAsset Call(int param0, string param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			TMPro.TMP_SpriteAsset ret = (TMPro.TMP_SpriteAsset)func.CheckObject(typeof(TMPro.TMP_SpriteAsset));
			func.EndPCall();
			return ret;
		}

		public TMPro.TMP_SpriteAsset CallWithSelf(int param0, string param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			TMPro.TMP_SpriteAsset ret = (TMPro.TMP_SpriteAsset)func.CheckObject(typeof(TMPro.TMP_SpriteAsset));
			func.EndPCall();
			return ret;
		}
	}

	public System.Func<int,string,TMPro.TMP_SpriteAsset> System_Func_int_string_TMPro_TMP_SpriteAsset(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Func<int,string,TMPro.TMP_SpriteAsset> fn = delegate(int param0, string param1) { return null; };
			return fn;
		}

		if(!flag)
		{
			System_Func_int_string_TMPro_TMP_SpriteAsset_Event target = new System_Func_int_string_TMPro_TMP_SpriteAsset_Event(func);
			System.Func<int,string,TMPro.TMP_SpriteAsset> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Func_int_string_TMPro_TMP_SpriteAsset_Event target = new System_Func_int_string_TMPro_TMP_SpriteAsset_Event(func, self);
			System.Func<int,string,TMPro.TMP_SpriteAsset> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Func_int_string_TMPro_TMP_SpriteAsset(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Func<int,string,TMPro.TMP_SpriteAsset>), L, pos);
	}

	void Push_System_Func_int_string_TMPro_TMP_SpriteAsset(IntPtr L, System.Func<int,string,TMPro.TMP_SpriteAsset> o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_TMPro_TMP_TextInfo_Event : LuaDelegate
	{
		public System_Action_TMPro_TMP_TextInfo_Event(LuaFunction func) : base(func) { }
		public System_Action_TMPro_TMP_TextInfo_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(TMPro.TMP_TextInfo param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(TMPro.TMP_TextInfo param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<TMPro.TMP_TextInfo> System_Action_TMPro_TMP_TextInfo(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<TMPro.TMP_TextInfo> fn = delegate(TMPro.TMP_TextInfo param0) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_TMPro_TMP_TextInfo_Event target = new System_Action_TMPro_TMP_TextInfo_Event(func);
			System.Action<TMPro.TMP_TextInfo> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_TMPro_TMP_TextInfo_Event target = new System_Action_TMPro_TMP_TextInfo_Event(func, self);
			System.Action<TMPro.TMP_TextInfo> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_TMPro_TMP_TextInfo(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<TMPro.TMP_TextInfo>), L, pos);
	}

	void Push_System_Action_TMPro_TMP_TextInfo(IntPtr L, System.Action<TMPro.TMP_TextInfo> o)
	{
		ToLua.Push(L, o);
	}

	class TMPro_TMP_InputField_OnValidateInput_Event : LuaDelegate
	{
		public TMPro_TMP_InputField_OnValidateInput_Event(LuaFunction func) : base(func) { }
		public TMPro_TMP_InputField_OnValidateInput_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public char Call(string param0, int param1, char param2)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			char ret = (char)func.CheckNumber();
			func.EndPCall();
			return ret;
		}

		public char CallWithSelf(string param0, int param1, char param2)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			char ret = (char)func.CheckNumber();
			func.EndPCall();
			return ret;
		}
	}

	public TMPro.TMP_InputField.OnValidateInput TMPro_TMP_InputField_OnValidateInput(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			TMPro.TMP_InputField.OnValidateInput fn = delegate(string param0, int param1, char param2) { return '\0'; };
			return fn;
		}

		if(!flag)
		{
			TMPro_TMP_InputField_OnValidateInput_Event target = new TMPro_TMP_InputField_OnValidateInput_Event(func);
			TMPro.TMP_InputField.OnValidateInput d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			TMPro_TMP_InputField_OnValidateInput_Event target = new TMPro_TMP_InputField_OnValidateInput_Event(func, self);
			TMPro.TMP_InputField.OnValidateInput d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_TMPro_TMP_InputField_OnValidateInput(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(TMPro.TMP_InputField.OnValidateInput), L, pos);
	}

	void Push_TMPro_TMP_InputField_OnValidateInput(IntPtr L, TMPro.TMP_InputField.OnValidateInput o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_float_float_Event : LuaDelegate
	{
		public System_Action_float_float_Event(LuaFunction func) : base(func) { }
		public System_Action_float_float_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(float param0, float param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(float param0, float param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<float,float> System_Action_float_float(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<float,float> fn = delegate(float param0, float param1) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_float_float_Event target = new System_Action_float_float_Event(func);
			System.Action<float,float> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_float_float_Event target = new System_Action_float_float_Event(func, self);
			System.Action<float,float> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_float_float(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<float,float>), L, pos);
	}

	void Push_System_Action_float_float(IntPtr L, System.Action<float,float> o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_UnityEngine_Texture2D_Event : LuaDelegate
	{
		public System_Action_UnityEngine_Texture2D_Event(LuaFunction func) : base(func) { }
		public System_Action_UnityEngine_Texture2D_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.Texture2D param0)
		{
			func.BeginPCall();
			func.PushSealed(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.Texture2D param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushSealed(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<UnityEngine.Texture2D> System_Action_UnityEngine_Texture2D(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<UnityEngine.Texture2D> fn = delegate(UnityEngine.Texture2D param0) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_UnityEngine_Texture2D_Event target = new System_Action_UnityEngine_Texture2D_Event(func);
			System.Action<UnityEngine.Texture2D> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_UnityEngine_Texture2D_Event target = new System_Action_UnityEngine_Texture2D_Event(func, self);
			System.Action<UnityEngine.Texture2D> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_UnityEngine_Texture2D(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<UnityEngine.Texture2D>), L, pos);
	}

	void Push_System_Action_UnityEngine_Texture2D(IntPtr L, System.Action<UnityEngine.Texture2D> o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_QingGongState_Event : LuaDelegate
	{
		public System_Action_QingGongState_Event(LuaFunction func) : base(func) { }
		public System_Action_QingGongState_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(QingGongState param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(QingGongState param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<QingGongState> System_Action_QingGongState(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<QingGongState> fn = delegate(QingGongState param0) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_QingGongState_Event target = new System_Action_QingGongState_Event(func);
			System.Action<QingGongState> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_QingGongState_Event target = new System_Action_QingGongState_Event(func, self);
			System.Action<QingGongState> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_QingGongState(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<QingGongState>), L, pos);
	}

	void Push_System_Action_QingGongState(IntPtr L, System.Action<QingGongState> o)
	{
		ToLua.Push(L, o);
	}

	class ClickManager_ClickGroundDelegate_Event : LuaDelegate
	{
		public ClickManager_ClickGroundDelegate_Event(LuaFunction func) : base(func) { }
		public ClickManager_ClickGroundDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.RaycastHit param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.RaycastHit param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public ClickManager.ClickGroundDelegate ClickManager_ClickGroundDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			ClickManager.ClickGroundDelegate fn = delegate(UnityEngine.RaycastHit param0) { };
			return fn;
		}

		if(!flag)
		{
			ClickManager_ClickGroundDelegate_Event target = new ClickManager_ClickGroundDelegate_Event(func);
			ClickManager.ClickGroundDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			ClickManager_ClickGroundDelegate_Event target = new ClickManager_ClickGroundDelegate_Event(func, self);
			ClickManager.ClickGroundDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_ClickManager_ClickGroundDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(ClickManager.ClickGroundDelegate), L, pos);
	}

	void Push_ClickManager_ClickGroundDelegate(IntPtr L, ClickManager.ClickGroundDelegate o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_bool_int_Event : LuaDelegate
	{
		public System_Action_bool_int_Event(LuaFunction func) : base(func) { }
		public System_Action_bool_int_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(bool param0, int param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(bool param0, int param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<bool,int> System_Action_bool_int(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<bool,int> fn = delegate(bool param0, int param1) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_bool_int_Event target = new System_Action_bool_int_Event(func);
			System.Action<bool,int> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_bool_int_Event target = new System_Action_bool_int_Event(func, self);
			System.Action<bool,int> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_bool_int(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<bool,int>), L, pos);
	}

	void Push_System_Action_bool_int(IntPtr L, System.Action<bool,int> o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_UnityEngine_GameObject_Event : LuaDelegate
	{
		public System_Action_UnityEngine_GameObject_Event(LuaFunction func) : base(func) { }
		public System_Action_UnityEngine_GameObject_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.GameObject param0)
		{
			func.BeginPCall();
			func.PushSealed(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.GameObject param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushSealed(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<UnityEngine.GameObject> System_Action_UnityEngine_GameObject(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<UnityEngine.GameObject> fn = delegate(UnityEngine.GameObject param0) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_UnityEngine_GameObject_Event target = new System_Action_UnityEngine_GameObject_Event(func);
			System.Action<UnityEngine.GameObject> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_UnityEngine_GameObject_Event target = new System_Action_UnityEngine_GameObject_Event(func, self);
			System.Action<UnityEngine.GameObject> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_UnityEngine_GameObject(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<UnityEngine.GameObject>), L, pos);
	}

	void Push_System_Action_UnityEngine_GameObject(IntPtr L, System.Action<UnityEngine.GameObject> o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_UnityEngine_Camera_UnityEngine_Vector3_UnityEngine_Quaternion_Event : LuaDelegate
	{
		public System_Action_UnityEngine_Camera_UnityEngine_Vector3_UnityEngine_Quaternion_Event(LuaFunction func) : base(func) { }
		public System_Action_UnityEngine_Camera_UnityEngine_Vector3_UnityEngine_Quaternion_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.Camera param0, UnityEngine.Vector3 param1, UnityEngine.Quaternion param2)
		{
			func.BeginPCall();
			func.PushSealed(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.Camera param0, UnityEngine.Vector3 param1, UnityEngine.Quaternion param2)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushSealed(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<UnityEngine.Camera,UnityEngine.Vector3,UnityEngine.Quaternion> System_Action_UnityEngine_Camera_UnityEngine_Vector3_UnityEngine_Quaternion(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<UnityEngine.Camera,UnityEngine.Vector3,UnityEngine.Quaternion> fn = delegate(UnityEngine.Camera param0, UnityEngine.Vector3 param1, UnityEngine.Quaternion param2) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_UnityEngine_Camera_UnityEngine_Vector3_UnityEngine_Quaternion_Event target = new System_Action_UnityEngine_Camera_UnityEngine_Vector3_UnityEngine_Quaternion_Event(func);
			System.Action<UnityEngine.Camera,UnityEngine.Vector3,UnityEngine.Quaternion> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_UnityEngine_Camera_UnityEngine_Vector3_UnityEngine_Quaternion_Event target = new System_Action_UnityEngine_Camera_UnityEngine_Vector3_UnityEngine_Quaternion_Event(func, self);
			System.Action<UnityEngine.Camera,UnityEngine.Vector3,UnityEngine.Quaternion> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_UnityEngine_Camera_UnityEngine_Vector3_UnityEngine_Quaternion(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<UnityEngine.Camera,UnityEngine.Vector3,UnityEngine.Quaternion>), L, pos);
	}

	void Push_System_Action_UnityEngine_Camera_UnityEngine_Vector3_UnityEngine_Quaternion(IntPtr L, System.Action<UnityEngine.Camera,UnityEngine.Vector3,UnityEngine.Quaternion> o)
	{
		ToLua.Push(L, o);
	}

	class Nirvana_SignalDelegate_Event : LuaDelegate
	{
		public Nirvana_SignalDelegate_Event(LuaFunction func) : base(func) { }
		public Nirvana_SignalDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(object[] param0)
		{
			func.BeginPCall();

			for (int i = 0; i < param0.Length; i++)
			{
				func.Push(param0[i]);
			}

			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(object[] param0)
		{
			func.BeginPCall();
			func.Push(self);

			for (int i = 0; i < param0.Length; i++)
			{
				func.Push(param0[i]);
			}

			func.PCall();
			func.EndPCall();
		}
	}

	public Nirvana.SignalDelegate Nirvana_SignalDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			Nirvana.SignalDelegate fn = delegate(object[] param0) { };
			return fn;
		}

		if(!flag)
		{
			Nirvana_SignalDelegate_Event target = new Nirvana_SignalDelegate_Event(func);
			Nirvana.SignalDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			Nirvana_SignalDelegate_Event target = new Nirvana_SignalDelegate_Event(func, self);
			Nirvana.SignalDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_Nirvana_SignalDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(Nirvana.SignalDelegate), L, pos);
	}

	void Push_Nirvana_SignalDelegate(IntPtr L, Nirvana.SignalDelegate o)
	{
		ToLua.Push(L, o);
	}

	class Nirvana_EventTriggerListener_PointerEventDelegate_Event : LuaDelegate
	{
		public Nirvana_EventTriggerListener_PointerEventDelegate_Event(LuaFunction func) : base(func) { }
		public Nirvana_EventTriggerListener_PointerEventDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.EventSystems.PointerEventData param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.EventSystems.PointerEventData param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public Nirvana.EventTriggerListener.PointerEventDelegate Nirvana_EventTriggerListener_PointerEventDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			Nirvana.EventTriggerListener.PointerEventDelegate fn = delegate(UnityEngine.EventSystems.PointerEventData param0) { };
			return fn;
		}

		if(!flag)
		{
			Nirvana_EventTriggerListener_PointerEventDelegate_Event target = new Nirvana_EventTriggerListener_PointerEventDelegate_Event(func);
			Nirvana.EventTriggerListener.PointerEventDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			Nirvana_EventTriggerListener_PointerEventDelegate_Event target = new Nirvana_EventTriggerListener_PointerEventDelegate_Event(func, self);
			Nirvana.EventTriggerListener.PointerEventDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_Nirvana_EventTriggerListener_PointerEventDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(Nirvana.EventTriggerListener.PointerEventDelegate), L, pos);
	}

	void Push_Nirvana_EventTriggerListener_PointerEventDelegate(IntPtr L, Nirvana.EventTriggerListener.PointerEventDelegate o)
	{
		ToLua.Push(L, o);
	}

	class Nirvana_EventTriggerListener_BaseEventDelegate_Event : LuaDelegate
	{
		public Nirvana_EventTriggerListener_BaseEventDelegate_Event(LuaFunction func) : base(func) { }
		public Nirvana_EventTriggerListener_BaseEventDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.EventSystems.BaseEventData param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.EventSystems.BaseEventData param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public Nirvana.EventTriggerListener.BaseEventDelegate Nirvana_EventTriggerListener_BaseEventDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			Nirvana.EventTriggerListener.BaseEventDelegate fn = delegate(UnityEngine.EventSystems.BaseEventData param0) { };
			return fn;
		}

		if(!flag)
		{
			Nirvana_EventTriggerListener_BaseEventDelegate_Event target = new Nirvana_EventTriggerListener_BaseEventDelegate_Event(func);
			Nirvana.EventTriggerListener.BaseEventDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			Nirvana_EventTriggerListener_BaseEventDelegate_Event target = new Nirvana_EventTriggerListener_BaseEventDelegate_Event(func, self);
			Nirvana.EventTriggerListener.BaseEventDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_Nirvana_EventTriggerListener_BaseEventDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(Nirvana.EventTriggerListener.BaseEventDelegate), L, pos);
	}

	void Push_Nirvana_EventTriggerListener_BaseEventDelegate(IntPtr L, Nirvana.EventTriggerListener.BaseEventDelegate o)
	{
		ToLua.Push(L, o);
	}

	class Nirvana_EventTriggerListener_AxisEventDelegate_Event : LuaDelegate
	{
		public Nirvana_EventTriggerListener_AxisEventDelegate_Event(LuaFunction func) : base(func) { }
		public Nirvana_EventTriggerListener_AxisEventDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.EventSystems.AxisEventData param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.EventSystems.AxisEventData param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public Nirvana.EventTriggerListener.AxisEventDelegate Nirvana_EventTriggerListener_AxisEventDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			Nirvana.EventTriggerListener.AxisEventDelegate fn = delegate(UnityEngine.EventSystems.AxisEventData param0) { };
			return fn;
		}

		if(!flag)
		{
			Nirvana_EventTriggerListener_AxisEventDelegate_Event target = new Nirvana_EventTriggerListener_AxisEventDelegate_Event(func);
			Nirvana.EventTriggerListener.AxisEventDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			Nirvana_EventTriggerListener_AxisEventDelegate_Event target = new Nirvana_EventTriggerListener_AxisEventDelegate_Event(func, self);
			Nirvana.EventTriggerListener.AxisEventDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_Nirvana_EventTriggerListener_AxisEventDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(Nirvana.EventTriggerListener.AxisEventDelegate), L, pos);
	}

	void Push_Nirvana_EventTriggerListener_AxisEventDelegate(IntPtr L, Nirvana.EventTriggerListener.AxisEventDelegate o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_UnityEngine_Texture_Event : LuaDelegate
	{
		public System_Action_UnityEngine_Texture_Event(LuaFunction func) : base(func) { }
		public System_Action_UnityEngine_Texture_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.Texture param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.Texture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<UnityEngine.Texture> System_Action_UnityEngine_Texture(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<UnityEngine.Texture> fn = delegate(UnityEngine.Texture param0) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_UnityEngine_Texture_Event target = new System_Action_UnityEngine_Texture_Event(func);
			System.Action<UnityEngine.Texture> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_UnityEngine_Texture_Event target = new System_Action_UnityEngine_Texture_Event(func, self);
			System.Action<UnityEngine.Texture> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_UnityEngine_Texture(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<UnityEngine.Texture>), L, pos);
	}

	void Push_System_Action_UnityEngine_Texture(IntPtr L, System.Action<UnityEngine.Texture> o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_int_string_Event : LuaDelegate
	{
		public System_Action_int_string_Event(LuaFunction func) : base(func) { }
		public System_Action_int_string_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(int param0, string param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(int param0, string param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<int,string> System_Action_int_string(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<int,string> fn = delegate(int param0, string param1) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_int_string_Event target = new System_Action_int_string_Event(func);
			System.Action<int,string> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_int_string_Event target = new System_Action_int_string_Event(func, self);
			System.Action<int,string> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_int_string(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<int,string>), L, pos);
	}

	void Push_System_Action_int_string(IntPtr L, System.Action<int,string> o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_bool_int_string_Event : LuaDelegate
	{
		public System_Action_bool_int_string_Event(LuaFunction func) : base(func) { }
		public System_Action_bool_int_string_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(bool param0, int param1, string param2)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(bool param0, int param1, string param2)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<bool,int,string> System_Action_bool_int_string(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<bool,int,string> fn = delegate(bool param0, int param1, string param2) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_bool_int_string_Event target = new System_Action_bool_int_string_Event(func);
			System.Action<bool,int,string> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_bool_int_string_Event target = new System_Action_bool_int_string_Event(func, self);
			System.Action<bool,int,string> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_bool_int_string(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<bool,int,string>), L, pos);
	}

	void Push_System_Action_bool_int_string(IntPtr L, System.Action<bool,int,string> o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_bool_string_string_Event : LuaDelegate
	{
		public System_Action_bool_string_string_Event(LuaFunction func) : base(func) { }
		public System_Action_bool_string_string_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(bool param0, string param1, string param2)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(bool param0, string param1, string param2)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<bool,string,string> System_Action_bool_string_string(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<bool,string,string> fn = delegate(bool param0, string param1, string param2) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_bool_string_string_Event target = new System_Action_bool_string_string_Event(func);
			System.Action<bool,string,string> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_bool_string_string_Event target = new System_Action_bool_string_string_Event(func, self);
			System.Action<bool,string,string> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_bool_string_string(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<bool,string,string>), L, pos);
	}

	void Push_System_Action_bool_string_string(IntPtr L, System.Action<bool,string,string> o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_string_string_Event : LuaDelegate
	{
		public System_Action_string_string_Event(LuaFunction func) : base(func) { }
		public System_Action_string_string_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(string param0, string param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(string param0, string param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<string,string> System_Action_string_string(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<string,string> fn = delegate(string param0, string param1) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_string_string_Event target = new System_Action_string_string_Event(func);
			System.Action<string,string> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_string_string_Event target = new System_Action_string_string_Event(func, self);
			System.Action<string,string> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_string_string(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<string,string>), L, pos);
	}

	void Push_System_Action_string_string(IntPtr L, System.Action<string,string> o)
	{
		ToLua.Push(L, o);
	}

	class Nirvana_EnhancedNetClient_DisconnectDelegate_Event : LuaDelegate
	{
		public Nirvana_EnhancedNetClient_DisconnectDelegate_Event(LuaFunction func) : base(func) { }
		public Nirvana_EnhancedNetClient_DisconnectDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(Nirvana.EnhancedNetClient.DisconnectReason param0, string param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(Nirvana.EnhancedNetClient.DisconnectReason param0, string param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public Nirvana.EnhancedNetClient.DisconnectDelegate Nirvana_EnhancedNetClient_DisconnectDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			Nirvana.EnhancedNetClient.DisconnectDelegate fn = delegate(Nirvana.EnhancedNetClient.DisconnectReason param0, string param1) { };
			return fn;
		}

		if(!flag)
		{
			Nirvana_EnhancedNetClient_DisconnectDelegate_Event target = new Nirvana_EnhancedNetClient_DisconnectDelegate_Event(func);
			Nirvana.EnhancedNetClient.DisconnectDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			Nirvana_EnhancedNetClient_DisconnectDelegate_Event target = new Nirvana_EnhancedNetClient_DisconnectDelegate_Event(func, self);
			Nirvana.EnhancedNetClient.DisconnectDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_Nirvana_EnhancedNetClient_DisconnectDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(Nirvana.EnhancedNetClient.DisconnectDelegate), L, pos);
	}

	void Push_Nirvana_EnhancedNetClient_DisconnectDelegate(IntPtr L, Nirvana.EnhancedNetClient.DisconnectDelegate o)
	{
		ToLua.Push(L, o);
	}

	class Nirvana_EnhancedNetClient_ReceiveDelegate_Event : LuaDelegate
	{
		public Nirvana_EnhancedNetClient_ReceiveDelegate_Event(LuaFunction func) : base(func) { }
		public Nirvana_EnhancedNetClient_ReceiveDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(byte[] param0, uint param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(byte[] param0, uint param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public Nirvana.EnhancedNetClient.ReceiveDelegate Nirvana_EnhancedNetClient_ReceiveDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			Nirvana.EnhancedNetClient.ReceiveDelegate fn = delegate(byte[] param0, uint param1) { };
			return fn;
		}

		if(!flag)
		{
			Nirvana_EnhancedNetClient_ReceiveDelegate_Event target = new Nirvana_EnhancedNetClient_ReceiveDelegate_Event(func);
			Nirvana.EnhancedNetClient.ReceiveDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			Nirvana_EnhancedNetClient_ReceiveDelegate_Event target = new Nirvana_EnhancedNetClient_ReceiveDelegate_Event(func, self);
			Nirvana.EnhancedNetClient.ReceiveDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_Nirvana_EnhancedNetClient_ReceiveDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(Nirvana.EnhancedNetClient.ReceiveDelegate), L, pos);
	}

	void Push_Nirvana_EnhancedNetClient_ReceiveDelegate(IntPtr L, Nirvana.EnhancedNetClient.ReceiveDelegate o)
	{
		ToLua.Push(L, o);
	}

	class Nirvana_EnhancedNetClient_ConnectDelegate_Event : LuaDelegate
	{
		public Nirvana_EnhancedNetClient_ConnectDelegate_Event(LuaFunction func) : base(func) { }
		public Nirvana_EnhancedNetClient_ConnectDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(bool param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(bool param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public Nirvana.EnhancedNetClient.ConnectDelegate Nirvana_EnhancedNetClient_ConnectDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			Nirvana.EnhancedNetClient.ConnectDelegate fn = delegate(bool param0) { };
			return fn;
		}

		if(!flag)
		{
			Nirvana_EnhancedNetClient_ConnectDelegate_Event target = new Nirvana_EnhancedNetClient_ConnectDelegate_Event(func);
			Nirvana.EnhancedNetClient.ConnectDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			Nirvana_EnhancedNetClient_ConnectDelegate_Event target = new Nirvana_EnhancedNetClient_ConnectDelegate_Event(func, self);
			Nirvana.EnhancedNetClient.ConnectDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_Nirvana_EnhancedNetClient_ConnectDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(Nirvana.EnhancedNetClient.ConnectDelegate), L, pos);
	}

	void Push_Nirvana_EnhancedNetClient_ConnectDelegate(IntPtr L, Nirvana.EnhancedNetClient.ConnectDelegate o)
	{
		ToLua.Push(L, o);
	}

	class Nirvana_EnhancedNetClient_SendDelegate_Event : LuaDelegate
	{
		public Nirvana_EnhancedNetClient_SendDelegate_Event(LuaFunction func) : base(func) { }
		public Nirvana_EnhancedNetClient_SendDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call()
		{
			func.Call();
		}

		public void CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			func.EndPCall();
		}
	}

	public Nirvana.EnhancedNetClient.SendDelegate Nirvana_EnhancedNetClient_SendDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			Nirvana.EnhancedNetClient.SendDelegate fn = delegate() { };
			return fn;
		}

		if(!flag)
		{
			Nirvana_EnhancedNetClient_SendDelegate_Event target = new Nirvana_EnhancedNetClient_SendDelegate_Event(func);
			Nirvana.EnhancedNetClient.SendDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			Nirvana_EnhancedNetClient_SendDelegate_Event target = new Nirvana_EnhancedNetClient_SendDelegate_Event(func, self);
			Nirvana.EnhancedNetClient.SendDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_Nirvana_EnhancedNetClient_SendDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(Nirvana.EnhancedNetClient.SendDelegate), L, pos);
	}

	void Push_Nirvana_EnhancedNetClient_SendDelegate(IntPtr L, Nirvana.EnhancedNetClient.SendDelegate o)
	{
		ToLua.Push(L, o);
	}

	class Nirvana_EnhancedNetClient_DisconnectWithReasonDelegate_Event : LuaDelegate
	{
		public Nirvana_EnhancedNetClient_DisconnectWithReasonDelegate_Event(LuaFunction func) : base(func) { }
		public Nirvana_EnhancedNetClient_DisconnectWithReasonDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(int param0, string param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(int param0, string param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public Nirvana.EnhancedNetClient.DisconnectWithReasonDelegate Nirvana_EnhancedNetClient_DisconnectWithReasonDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			Nirvana.EnhancedNetClient.DisconnectWithReasonDelegate fn = delegate(int param0, string param1) { };
			return fn;
		}

		if(!flag)
		{
			Nirvana_EnhancedNetClient_DisconnectWithReasonDelegate_Event target = new Nirvana_EnhancedNetClient_DisconnectWithReasonDelegate_Event(func);
			Nirvana.EnhancedNetClient.DisconnectWithReasonDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			Nirvana_EnhancedNetClient_DisconnectWithReasonDelegate_Event target = new Nirvana_EnhancedNetClient_DisconnectWithReasonDelegate_Event(func, self);
			Nirvana.EnhancedNetClient.DisconnectWithReasonDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_Nirvana_EnhancedNetClient_DisconnectWithReasonDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(Nirvana.EnhancedNetClient.DisconnectWithReasonDelegate), L, pos);
	}

	void Push_Nirvana_EnhancedNetClient_DisconnectWithReasonDelegate(IntPtr L, Nirvana.EnhancedNetClient.DisconnectWithReasonDelegate o)
	{
		ToLua.Push(L, o);
	}

	class Nirvana_ListView_GetCellDelegate_Event : LuaDelegate
	{
		public Nirvana_ListView_GetCellDelegate_Event(LuaFunction func) : base(func) { }
		public Nirvana_ListView_GetCellDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public UnityEngine.GameObject Call(int param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			UnityEngine.GameObject ret = (UnityEngine.GameObject)func.CheckObject(typeof(UnityEngine.GameObject));
			func.EndPCall();
			return ret;
		}

		public UnityEngine.GameObject CallWithSelf(int param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			UnityEngine.GameObject ret = (UnityEngine.GameObject)func.CheckObject(typeof(UnityEngine.GameObject));
			func.EndPCall();
			return ret;
		}
	}

	public Nirvana.ListView.GetCellDelegate Nirvana_ListView_GetCellDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			Nirvana.ListView.GetCellDelegate fn = delegate(int param0) { return null; };
			return fn;
		}

		if(!flag)
		{
			Nirvana_ListView_GetCellDelegate_Event target = new Nirvana_ListView_GetCellDelegate_Event(func);
			Nirvana.ListView.GetCellDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			Nirvana_ListView_GetCellDelegate_Event target = new Nirvana_ListView_GetCellDelegate_Event(func, self);
			Nirvana.ListView.GetCellDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_Nirvana_ListView_GetCellDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(Nirvana.ListView.GetCellDelegate), L, pos);
	}

	void Push_Nirvana_ListView_GetCellDelegate(IntPtr L, Nirvana.ListView.GetCellDelegate o)
	{
		ToLua.Push(L, o);
	}

	class Nirvana_ListView_CellCountDelegate_Event : LuaDelegate
	{
		public Nirvana_ListView_CellCountDelegate_Event(LuaFunction func) : base(func) { }
		public Nirvana_ListView_CellCountDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public int Call()
		{
			func.BeginPCall();
			func.PCall();
			int ret = (int)func.CheckNumber();
			func.EndPCall();
			return ret;
		}

		public int CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			int ret = (int)func.CheckNumber();
			func.EndPCall();
			return ret;
		}
	}

	public Nirvana.ListView.CellCountDelegate Nirvana_ListView_CellCountDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			Nirvana.ListView.CellCountDelegate fn = delegate() { return 0; };
			return fn;
		}

		if(!flag)
		{
			Nirvana_ListView_CellCountDelegate_Event target = new Nirvana_ListView_CellCountDelegate_Event(func);
			Nirvana.ListView.CellCountDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			Nirvana_ListView_CellCountDelegate_Event target = new Nirvana_ListView_CellCountDelegate_Event(func, self);
			Nirvana.ListView.CellCountDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_Nirvana_ListView_CellCountDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(Nirvana.ListView.CellCountDelegate), L, pos);
	}

	void Push_Nirvana_ListView_CellCountDelegate(IntPtr L, Nirvana.ListView.CellCountDelegate o)
	{
		ToLua.Push(L, o);
	}

	class Nirvana_ListView_GetCellSizeDelegate_Event : LuaDelegate
	{
		public Nirvana_ListView_GetCellSizeDelegate_Event(LuaFunction func) : base(func) { }
		public Nirvana_ListView_GetCellSizeDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public UnityEngine.Vector2 Call(int param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			UnityEngine.Vector2 ret = func.CheckVector2();
			func.EndPCall();
			return ret;
		}

		public UnityEngine.Vector2 CallWithSelf(int param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			UnityEngine.Vector2 ret = func.CheckVector2();
			func.EndPCall();
			return ret;
		}
	}

	public Nirvana.ListView.GetCellSizeDelegate Nirvana_ListView_GetCellSizeDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			Nirvana.ListView.GetCellSizeDelegate fn = delegate(int param0) { return default(UnityEngine.Vector2); };
			return fn;
		}

		if(!flag)
		{
			Nirvana_ListView_GetCellSizeDelegate_Event target = new Nirvana_ListView_GetCellSizeDelegate_Event(func);
			Nirvana.ListView.GetCellSizeDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			Nirvana_ListView_GetCellSizeDelegate_Event target = new Nirvana_ListView_GetCellSizeDelegate_Event(func, self);
			Nirvana.ListView.GetCellSizeDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_Nirvana_ListView_GetCellSizeDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(Nirvana.ListView.GetCellSizeDelegate), L, pos);
	}

	void Push_Nirvana_ListView_GetCellSizeDelegate(IntPtr L, Nirvana.ListView.GetCellSizeDelegate o)
	{
		ToLua.Push(L, o);
	}

	class Nirvana_ListView_RecycleCellDelegate_Event : LuaDelegate
	{
		public Nirvana_ListView_RecycleCellDelegate_Event(LuaFunction func) : base(func) { }
		public Nirvana_ListView_RecycleCellDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(int param0, UnityEngine.GameObject param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PushSealed(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(int param0, UnityEngine.GameObject param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PushSealed(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public Nirvana.ListView.RecycleCellDelegate Nirvana_ListView_RecycleCellDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			Nirvana.ListView.RecycleCellDelegate fn = delegate(int param0, UnityEngine.GameObject param1) { };
			return fn;
		}

		if(!flag)
		{
			Nirvana_ListView_RecycleCellDelegate_Event target = new Nirvana_ListView_RecycleCellDelegate_Event(func);
			Nirvana.ListView.RecycleCellDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			Nirvana_ListView_RecycleCellDelegate_Event target = new Nirvana_ListView_RecycleCellDelegate_Event(func, self);
			Nirvana.ListView.RecycleCellDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_Nirvana_ListView_RecycleCellDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(Nirvana.ListView.RecycleCellDelegate), L, pos);
	}

	void Push_Nirvana_ListView_RecycleCellDelegate(IntPtr L, Nirvana.ListView.RecycleCellDelegate o)
	{
		ToLua.Push(L, o);
	}

	class UIDrag_DropCallBackAction_Event : LuaDelegate
	{
		public UIDrag_DropCallBackAction_Event(LuaFunction func) : base(func) { }
		public UIDrag_DropCallBackAction_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(object param0, UnityEngine.GameObject param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PushSealed(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(object param0, UnityEngine.GameObject param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PushSealed(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public UIDrag.DropCallBackAction UIDrag_DropCallBackAction(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UIDrag.DropCallBackAction fn = delegate(object param0, UnityEngine.GameObject param1) { };
			return fn;
		}

		if(!flag)
		{
			UIDrag_DropCallBackAction_Event target = new UIDrag_DropCallBackAction_Event(func);
			UIDrag.DropCallBackAction d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UIDrag_DropCallBackAction_Event target = new UIDrag_DropCallBackAction_Event(func, self);
			UIDrag.DropCallBackAction d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UIDrag_DropCallBackAction(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UIDrag.DropCallBackAction), L, pos);
	}

	void Push_UIDrag_DropCallBackAction(IntPtr L, UIDrag.DropCallBackAction o)
	{
		ToLua.Push(L, o);
	}

	class ListViewDelegate_NumberOfCellsDelegate_Event : LuaDelegate
	{
		public ListViewDelegate_NumberOfCellsDelegate_Event(LuaFunction func) : base(func) { }
		public ListViewDelegate_NumberOfCellsDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public int Call()
		{
			func.BeginPCall();
			func.PCall();
			int ret = (int)func.CheckNumber();
			func.EndPCall();
			return ret;
		}

		public int CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			int ret = (int)func.CheckNumber();
			func.EndPCall();
			return ret;
		}
	}

	public ListViewDelegate.NumberOfCellsDelegate ListViewDelegate_NumberOfCellsDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			ListViewDelegate.NumberOfCellsDelegate fn = delegate() { return 0; };
			return fn;
		}

		if(!flag)
		{
			ListViewDelegate_NumberOfCellsDelegate_Event target = new ListViewDelegate_NumberOfCellsDelegate_Event(func);
			ListViewDelegate.NumberOfCellsDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			ListViewDelegate_NumberOfCellsDelegate_Event target = new ListViewDelegate_NumberOfCellsDelegate_Event(func, self);
			ListViewDelegate.NumberOfCellsDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_ListViewDelegate_NumberOfCellsDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(ListViewDelegate.NumberOfCellsDelegate), L, pos);
	}

	void Push_ListViewDelegate_NumberOfCellsDelegate(IntPtr L, ListViewDelegate.NumberOfCellsDelegate o)
	{
		ToLua.Push(L, o);
	}

	class ListViewDelegate_CellViewSizeDelegate_Event : LuaDelegate
	{
		public ListViewDelegate_CellViewSizeDelegate_Event(LuaFunction func) : base(func) { }
		public ListViewDelegate_CellViewSizeDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public int Call(int param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			int ret = (int)func.CheckNumber();
			func.EndPCall();
			return ret;
		}

		public int CallWithSelf(int param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			int ret = (int)func.CheckNumber();
			func.EndPCall();
			return ret;
		}
	}

	public ListViewDelegate.CellViewSizeDelegate ListViewDelegate_CellViewSizeDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			ListViewDelegate.CellViewSizeDelegate fn = delegate(int param0) { return 0; };
			return fn;
		}

		if(!flag)
		{
			ListViewDelegate_CellViewSizeDelegate_Event target = new ListViewDelegate_CellViewSizeDelegate_Event(func);
			ListViewDelegate.CellViewSizeDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			ListViewDelegate_CellViewSizeDelegate_Event target = new ListViewDelegate_CellViewSizeDelegate_Event(func, self);
			ListViewDelegate.CellViewSizeDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_ListViewDelegate_CellViewSizeDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(ListViewDelegate.CellViewSizeDelegate), L, pos);
	}

	void Push_ListViewDelegate_CellViewSizeDelegate(IntPtr L, ListViewDelegate.CellViewSizeDelegate o)
	{
		ToLua.Push(L, o);
	}

	class ListViewDelegate_CellViewDelegate_Event : LuaDelegate
	{
		public ListViewDelegate_CellViewDelegate_Event(LuaFunction func) : base(func) { }
		public ListViewDelegate_CellViewDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public EnhancedUI.EnhancedScroller.EnhancedScrollerCellView Call(EnhancedUI.EnhancedScroller.EnhancedScroller param0, int param1, int param2)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			EnhancedUI.EnhancedScroller.EnhancedScrollerCellView ret = (EnhancedUI.EnhancedScroller.EnhancedScrollerCellView)func.CheckObject(typeof(EnhancedUI.EnhancedScroller.EnhancedScrollerCellView));
			func.EndPCall();
			return ret;
		}

		public EnhancedUI.EnhancedScroller.EnhancedScrollerCellView CallWithSelf(EnhancedUI.EnhancedScroller.EnhancedScroller param0, int param1, int param2)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			EnhancedUI.EnhancedScroller.EnhancedScrollerCellView ret = (EnhancedUI.EnhancedScroller.EnhancedScrollerCellView)func.CheckObject(typeof(EnhancedUI.EnhancedScroller.EnhancedScrollerCellView));
			func.EndPCall();
			return ret;
		}
	}

	public ListViewDelegate.CellViewDelegate ListViewDelegate_CellViewDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			ListViewDelegate.CellViewDelegate fn = delegate(EnhancedUI.EnhancedScroller.EnhancedScroller param0, int param1, int param2) { return null; };
			return fn;
		}

		if(!flag)
		{
			ListViewDelegate_CellViewDelegate_Event target = new ListViewDelegate_CellViewDelegate_Event(func);
			ListViewDelegate.CellViewDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			ListViewDelegate_CellViewDelegate_Event target = new ListViewDelegate_CellViewDelegate_Event(func, self);
			ListViewDelegate.CellViewDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_ListViewDelegate_CellViewDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(ListViewDelegate.CellViewDelegate), L, pos);
	}

	void Push_ListViewDelegate_CellViewDelegate(IntPtr L, ListViewDelegate.CellViewDelegate o)
	{
		ToLua.Push(L, o);
	}

	class ListViewCell_RefreshCell_Event : LuaDelegate
	{
		public ListViewCell_RefreshCell_Event(LuaFunction func) : base(func) { }
		public ListViewCell_RefreshCell_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call()
		{
			func.Call();
		}

		public void CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			func.EndPCall();
		}
	}

	public ListViewCell.RefreshCell ListViewCell_RefreshCell(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			ListViewCell.RefreshCell fn = delegate() { };
			return fn;
		}

		if(!flag)
		{
			ListViewCell_RefreshCell_Event target = new ListViewCell_RefreshCell_Event(func);
			ListViewCell.RefreshCell d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			ListViewCell_RefreshCell_Event target = new ListViewCell_RefreshCell_Event(func, self);
			ListViewCell.RefreshCell d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_ListViewCell_RefreshCell(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(ListViewCell.RefreshCell), L, pos);
	}

	void Push_ListViewCell_RefreshCell(IntPtr L, ListViewCell.RefreshCell o)
	{
		ToLua.Push(L, o);
	}

	class System_Func_float_bool_Event : LuaDelegate
	{
		public System_Func_float_bool_Event(LuaFunction func) : base(func) { }
		public System_Func_float_bool_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public bool Call(float param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			bool ret = func.CheckBoolean();
			func.EndPCall();
			return ret;
		}

		public bool CallWithSelf(float param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			bool ret = func.CheckBoolean();
			func.EndPCall();
			return ret;
		}
	}

	public System.Func<float,bool> System_Func_float_bool(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Func<float,bool> fn = delegate(float param0) { return false; };
			return fn;
		}

		if(!flag)
		{
			System_Func_float_bool_Event target = new System_Func_float_bool_Event(func);
			System.Func<float,bool> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Func_float_bool_Event target = new System_Func_float_bool_Event(func, self);
			System.Func<float,bool> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Func_float_bool(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Func<float,bool>), L, pos);
	}

	void Push_System_Func_float_bool(IntPtr L, System.Func<float,bool> o)
	{
		ToLua.Push(L, o);
	}

	class EnhancedUI_EnhancedScroller_CellViewVisibilityChangedDelegate_Event : LuaDelegate
	{
		public EnhancedUI_EnhancedScroller_CellViewVisibilityChangedDelegate_Event(LuaFunction func) : base(func) { }
		public EnhancedUI_EnhancedScroller_CellViewVisibilityChangedDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(EnhancedUI.EnhancedScroller.EnhancedScrollerCellView param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(EnhancedUI.EnhancedScroller.EnhancedScrollerCellView param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public EnhancedUI.EnhancedScroller.CellViewVisibilityChangedDelegate EnhancedUI_EnhancedScroller_CellViewVisibilityChangedDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			EnhancedUI.EnhancedScroller.CellViewVisibilityChangedDelegate fn = delegate(EnhancedUI.EnhancedScroller.EnhancedScrollerCellView param0) { };
			return fn;
		}

		if(!flag)
		{
			EnhancedUI_EnhancedScroller_CellViewVisibilityChangedDelegate_Event target = new EnhancedUI_EnhancedScroller_CellViewVisibilityChangedDelegate_Event(func);
			EnhancedUI.EnhancedScroller.CellViewVisibilityChangedDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			EnhancedUI_EnhancedScroller_CellViewVisibilityChangedDelegate_Event target = new EnhancedUI_EnhancedScroller_CellViewVisibilityChangedDelegate_Event(func, self);
			EnhancedUI.EnhancedScroller.CellViewVisibilityChangedDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_EnhancedUI_EnhancedScroller_CellViewVisibilityChangedDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(EnhancedUI.EnhancedScroller.CellViewVisibilityChangedDelegate), L, pos);
	}

	void Push_EnhancedUI_EnhancedScroller_CellViewVisibilityChangedDelegate(IntPtr L, EnhancedUI.EnhancedScroller.CellViewVisibilityChangedDelegate o)
	{
		ToLua.Push(L, o);
	}

	class EnhancedUI_EnhancedScroller_CellViewWillRecycleDelegate_Event : LuaDelegate
	{
		public EnhancedUI_EnhancedScroller_CellViewWillRecycleDelegate_Event(LuaFunction func) : base(func) { }
		public EnhancedUI_EnhancedScroller_CellViewWillRecycleDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(EnhancedUI.EnhancedScroller.EnhancedScrollerCellView param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(EnhancedUI.EnhancedScroller.EnhancedScrollerCellView param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public EnhancedUI.EnhancedScroller.CellViewWillRecycleDelegate EnhancedUI_EnhancedScroller_CellViewWillRecycleDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			EnhancedUI.EnhancedScroller.CellViewWillRecycleDelegate fn = delegate(EnhancedUI.EnhancedScroller.EnhancedScrollerCellView param0) { };
			return fn;
		}

		if(!flag)
		{
			EnhancedUI_EnhancedScroller_CellViewWillRecycleDelegate_Event target = new EnhancedUI_EnhancedScroller_CellViewWillRecycleDelegate_Event(func);
			EnhancedUI.EnhancedScroller.CellViewWillRecycleDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			EnhancedUI_EnhancedScroller_CellViewWillRecycleDelegate_Event target = new EnhancedUI_EnhancedScroller_CellViewWillRecycleDelegate_Event(func, self);
			EnhancedUI.EnhancedScroller.CellViewWillRecycleDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_EnhancedUI_EnhancedScroller_CellViewWillRecycleDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(EnhancedUI.EnhancedScroller.CellViewWillRecycleDelegate), L, pos);
	}

	void Push_EnhancedUI_EnhancedScroller_CellViewWillRecycleDelegate(IntPtr L, EnhancedUI.EnhancedScroller.CellViewWillRecycleDelegate o)
	{
		ToLua.Push(L, o);
	}

	class EnhancedUI_EnhancedScroller_ScrollerScrolledDelegate_Event : LuaDelegate
	{
		public EnhancedUI_EnhancedScroller_ScrollerScrolledDelegate_Event(LuaFunction func) : base(func) { }
		public EnhancedUI_EnhancedScroller_ScrollerScrolledDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(EnhancedUI.EnhancedScroller.EnhancedScroller param0, UnityEngine.Vector2 param1, float param2)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(EnhancedUI.EnhancedScroller.EnhancedScroller param0, UnityEngine.Vector2 param1, float param2)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			func.EndPCall();
		}
	}

	public EnhancedUI.EnhancedScroller.ScrollerScrolledDelegate EnhancedUI_EnhancedScroller_ScrollerScrolledDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			EnhancedUI.EnhancedScroller.ScrollerScrolledDelegate fn = delegate(EnhancedUI.EnhancedScroller.EnhancedScroller param0, UnityEngine.Vector2 param1, float param2) { };
			return fn;
		}

		if(!flag)
		{
			EnhancedUI_EnhancedScroller_ScrollerScrolledDelegate_Event target = new EnhancedUI_EnhancedScroller_ScrollerScrolledDelegate_Event(func);
			EnhancedUI.EnhancedScroller.ScrollerScrolledDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			EnhancedUI_EnhancedScroller_ScrollerScrolledDelegate_Event target = new EnhancedUI_EnhancedScroller_ScrollerScrolledDelegate_Event(func, self);
			EnhancedUI.EnhancedScroller.ScrollerScrolledDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_EnhancedUI_EnhancedScroller_ScrollerScrolledDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(EnhancedUI.EnhancedScroller.ScrollerScrolledDelegate), L, pos);
	}

	void Push_EnhancedUI_EnhancedScroller_ScrollerScrolledDelegate(IntPtr L, EnhancedUI.EnhancedScroller.ScrollerScrolledDelegate o)
	{
		ToLua.Push(L, o);
	}

	class EnhancedUI_EnhancedScroller_ScrollerSnappedDelegate_Event : LuaDelegate
	{
		public EnhancedUI_EnhancedScroller_ScrollerSnappedDelegate_Event(LuaFunction func) : base(func) { }
		public EnhancedUI_EnhancedScroller_ScrollerSnappedDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(EnhancedUI.EnhancedScroller.EnhancedScroller param0, int param1, int param2, EnhancedUI.EnhancedScroller.EnhancedScrollerCellView param3)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.Push(param3);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(EnhancedUI.EnhancedScroller.EnhancedScroller param0, int param1, int param2, EnhancedUI.EnhancedScroller.EnhancedScrollerCellView param3)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.Push(param3);
			func.PCall();
			func.EndPCall();
		}
	}

	public EnhancedUI.EnhancedScroller.ScrollerSnappedDelegate EnhancedUI_EnhancedScroller_ScrollerSnappedDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			EnhancedUI.EnhancedScroller.ScrollerSnappedDelegate fn = delegate(EnhancedUI.EnhancedScroller.EnhancedScroller param0, int param1, int param2, EnhancedUI.EnhancedScroller.EnhancedScrollerCellView param3) { };
			return fn;
		}

		if(!flag)
		{
			EnhancedUI_EnhancedScroller_ScrollerSnappedDelegate_Event target = new EnhancedUI_EnhancedScroller_ScrollerSnappedDelegate_Event(func);
			EnhancedUI.EnhancedScroller.ScrollerSnappedDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			EnhancedUI_EnhancedScroller_ScrollerSnappedDelegate_Event target = new EnhancedUI_EnhancedScroller_ScrollerSnappedDelegate_Event(func, self);
			EnhancedUI.EnhancedScroller.ScrollerSnappedDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_EnhancedUI_EnhancedScroller_ScrollerSnappedDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(EnhancedUI.EnhancedScroller.ScrollerSnappedDelegate), L, pos);
	}

	void Push_EnhancedUI_EnhancedScroller_ScrollerSnappedDelegate(IntPtr L, EnhancedUI.EnhancedScroller.ScrollerSnappedDelegate o)
	{
		ToLua.Push(L, o);
	}

	class EnhancedUI_EnhancedScroller_ScrollerScrollingChangedDelegate_Event : LuaDelegate
	{
		public EnhancedUI_EnhancedScroller_ScrollerScrollingChangedDelegate_Event(LuaFunction func) : base(func) { }
		public EnhancedUI_EnhancedScroller_ScrollerScrollingChangedDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(EnhancedUI.EnhancedScroller.EnhancedScroller param0, bool param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(EnhancedUI.EnhancedScroller.EnhancedScroller param0, bool param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public EnhancedUI.EnhancedScroller.ScrollerScrollingChangedDelegate EnhancedUI_EnhancedScroller_ScrollerScrollingChangedDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			EnhancedUI.EnhancedScroller.ScrollerScrollingChangedDelegate fn = delegate(EnhancedUI.EnhancedScroller.EnhancedScroller param0, bool param1) { };
			return fn;
		}

		if(!flag)
		{
			EnhancedUI_EnhancedScroller_ScrollerScrollingChangedDelegate_Event target = new EnhancedUI_EnhancedScroller_ScrollerScrollingChangedDelegate_Event(func);
			EnhancedUI.EnhancedScroller.ScrollerScrollingChangedDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			EnhancedUI_EnhancedScroller_ScrollerScrollingChangedDelegate_Event target = new EnhancedUI_EnhancedScroller_ScrollerScrollingChangedDelegate_Event(func, self);
			EnhancedUI.EnhancedScroller.ScrollerScrollingChangedDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_EnhancedUI_EnhancedScroller_ScrollerScrollingChangedDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(EnhancedUI.EnhancedScroller.ScrollerScrollingChangedDelegate), L, pos);
	}

	void Push_EnhancedUI_EnhancedScroller_ScrollerScrollingChangedDelegate(IntPtr L, EnhancedUI.EnhancedScroller.ScrollerScrollingChangedDelegate o)
	{
		ToLua.Push(L, o);
	}

	class EnhancedUI_EnhancedScroller_ScrollerTweeningChangedDelegate_Event : LuaDelegate
	{
		public EnhancedUI_EnhancedScroller_ScrollerTweeningChangedDelegate_Event(LuaFunction func) : base(func) { }
		public EnhancedUI_EnhancedScroller_ScrollerTweeningChangedDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(EnhancedUI.EnhancedScroller.EnhancedScroller param0, bool param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(EnhancedUI.EnhancedScroller.EnhancedScroller param0, bool param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public EnhancedUI.EnhancedScroller.ScrollerTweeningChangedDelegate EnhancedUI_EnhancedScroller_ScrollerTweeningChangedDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			EnhancedUI.EnhancedScroller.ScrollerTweeningChangedDelegate fn = delegate(EnhancedUI.EnhancedScroller.EnhancedScroller param0, bool param1) { };
			return fn;
		}

		if(!flag)
		{
			EnhancedUI_EnhancedScroller_ScrollerTweeningChangedDelegate_Event target = new EnhancedUI_EnhancedScroller_ScrollerTweeningChangedDelegate_Event(func);
			EnhancedUI.EnhancedScroller.ScrollerTweeningChangedDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			EnhancedUI_EnhancedScroller_ScrollerTweeningChangedDelegate_Event target = new EnhancedUI_EnhancedScroller_ScrollerTweeningChangedDelegate_Event(func, self);
			EnhancedUI.EnhancedScroller.ScrollerTweeningChangedDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_EnhancedUI_EnhancedScroller_ScrollerTweeningChangedDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(EnhancedUI.EnhancedScroller.ScrollerTweeningChangedDelegate), L, pos);
	}

	void Push_EnhancedUI_EnhancedScroller_ScrollerTweeningChangedDelegate(IntPtr L, EnhancedUI.EnhancedScroller.ScrollerTweeningChangedDelegate o)
	{
		ToLua.Push(L, o);
	}

	class EnhancedUI_EnhancedScroller_ScrollerScrollingEndDelegate_Event : LuaDelegate
	{
		public EnhancedUI_EnhancedScroller_ScrollerScrollingEndDelegate_Event(LuaFunction func) : base(func) { }
		public EnhancedUI_EnhancedScroller_ScrollerScrollingEndDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(EnhancedUI.EnhancedScroller.EnhancedScroller param0, int param1, int param2)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(EnhancedUI.EnhancedScroller.EnhancedScroller param0, int param1, int param2)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			func.EndPCall();
		}
	}

	public EnhancedUI.EnhancedScroller.ScrollerScrollingEndDelegate EnhancedUI_EnhancedScroller_ScrollerScrollingEndDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			EnhancedUI.EnhancedScroller.ScrollerScrollingEndDelegate fn = delegate(EnhancedUI.EnhancedScroller.EnhancedScroller param0, int param1, int param2) { };
			return fn;
		}

		if(!flag)
		{
			EnhancedUI_EnhancedScroller_ScrollerScrollingEndDelegate_Event target = new EnhancedUI_EnhancedScroller_ScrollerScrollingEndDelegate_Event(func);
			EnhancedUI.EnhancedScroller.ScrollerScrollingEndDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			EnhancedUI_EnhancedScroller_ScrollerScrollingEndDelegate_Event target = new EnhancedUI_EnhancedScroller_ScrollerScrollingEndDelegate_Event(func, self);
			EnhancedUI.EnhancedScroller.ScrollerScrollingEndDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_EnhancedUI_EnhancedScroller_ScrollerScrollingEndDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(EnhancedUI.EnhancedScroller.ScrollerScrollingEndDelegate), L, pos);
	}

	void Push_EnhancedUI_EnhancedScroller_ScrollerScrollingEndDelegate(IntPtr L, EnhancedUI.EnhancedScroller.ScrollerScrollingEndDelegate o)
	{
		ToLua.Push(L, o);
	}

	class EnhancedUI_EnhancedScroller_ScrollerSnapJumpToCenterDelegate_Event : LuaDelegate
	{
		public EnhancedUI_EnhancedScroller_ScrollerSnapJumpToCenterDelegate_Event(LuaFunction func) : base(func) { }
		public EnhancedUI_EnhancedScroller_ScrollerSnapJumpToCenterDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(EnhancedUI.EnhancedScroller.EnhancedScroller param0, int param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(EnhancedUI.EnhancedScroller.EnhancedScroller param0, int param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public EnhancedUI.EnhancedScroller.ScrollerSnapJumpToCenterDelegate EnhancedUI_EnhancedScroller_ScrollerSnapJumpToCenterDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			EnhancedUI.EnhancedScroller.ScrollerSnapJumpToCenterDelegate fn = delegate(EnhancedUI.EnhancedScroller.EnhancedScroller param0, int param1) { };
			return fn;
		}

		if(!flag)
		{
			EnhancedUI_EnhancedScroller_ScrollerSnapJumpToCenterDelegate_Event target = new EnhancedUI_EnhancedScroller_ScrollerSnapJumpToCenterDelegate_Event(func);
			EnhancedUI.EnhancedScroller.ScrollerSnapJumpToCenterDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			EnhancedUI_EnhancedScroller_ScrollerSnapJumpToCenterDelegate_Event target = new EnhancedUI_EnhancedScroller_ScrollerSnapJumpToCenterDelegate_Event(func, self);
			EnhancedUI.EnhancedScroller.ScrollerSnapJumpToCenterDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_EnhancedUI_EnhancedScroller_ScrollerSnapJumpToCenterDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(EnhancedUI.EnhancedScroller.ScrollerSnapJumpToCenterDelegate), L, pos);
	}

	void Push_EnhancedUI_EnhancedScroller_ScrollerSnapJumpToCenterDelegate(IntPtr L, EnhancedUI.EnhancedScroller.ScrollerSnapJumpToCenterDelegate o)
	{
		ToLua.Push(L, o);
	}

	class ListViewSimpleDelegate_NumberOfCellsDelegate_Event : LuaDelegate
	{
		public ListViewSimpleDelegate_NumberOfCellsDelegate_Event(LuaFunction func) : base(func) { }
		public ListViewSimpleDelegate_NumberOfCellsDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public int Call()
		{
			func.BeginPCall();
			func.PCall();
			int ret = (int)func.CheckNumber();
			func.EndPCall();
			return ret;
		}

		public int CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			int ret = (int)func.CheckNumber();
			func.EndPCall();
			return ret;
		}
	}

	public ListViewSimpleDelegate.NumberOfCellsDelegate ListViewSimpleDelegate_NumberOfCellsDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			ListViewSimpleDelegate.NumberOfCellsDelegate fn = delegate() { return 0; };
			return fn;
		}

		if(!flag)
		{
			ListViewSimpleDelegate_NumberOfCellsDelegate_Event target = new ListViewSimpleDelegate_NumberOfCellsDelegate_Event(func);
			ListViewSimpleDelegate.NumberOfCellsDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			ListViewSimpleDelegate_NumberOfCellsDelegate_Event target = new ListViewSimpleDelegate_NumberOfCellsDelegate_Event(func, self);
			ListViewSimpleDelegate.NumberOfCellsDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_ListViewSimpleDelegate_NumberOfCellsDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(ListViewSimpleDelegate.NumberOfCellsDelegate), L, pos);
	}

	void Push_ListViewSimpleDelegate_NumberOfCellsDelegate(IntPtr L, ListViewSimpleDelegate.NumberOfCellsDelegate o)
	{
		ToLua.Push(L, o);
	}

	class ListViewSimpleDelegate_CellSizeDelegate_Event : LuaDelegate
	{
		public ListViewSimpleDelegate_CellSizeDelegate_Event(LuaFunction func) : base(func) { }
		public ListViewSimpleDelegate_CellSizeDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public int Call(int param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			int ret = (int)func.CheckNumber();
			func.EndPCall();
			return ret;
		}

		public int CallWithSelf(int param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			int ret = (int)func.CheckNumber();
			func.EndPCall();
			return ret;
		}
	}

	public ListViewSimpleDelegate.CellSizeDelegate ListViewSimpleDelegate_CellSizeDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			ListViewSimpleDelegate.CellSizeDelegate fn = delegate(int param0) { return 0; };
			return fn;
		}

		if(!flag)
		{
			ListViewSimpleDelegate_CellSizeDelegate_Event target = new ListViewSimpleDelegate_CellSizeDelegate_Event(func);
			ListViewSimpleDelegate.CellSizeDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			ListViewSimpleDelegate_CellSizeDelegate_Event target = new ListViewSimpleDelegate_CellSizeDelegate_Event(func, self);
			ListViewSimpleDelegate.CellSizeDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_ListViewSimpleDelegate_CellSizeDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(ListViewSimpleDelegate.CellSizeDelegate), L, pos);
	}

	void Push_ListViewSimpleDelegate_CellSizeDelegate(IntPtr L, ListViewSimpleDelegate.CellSizeDelegate o)
	{
		ToLua.Push(L, o);
	}

	class ListViewSimpleDelegate_CellRefreshDelegate_Event : LuaDelegate
	{
		public ListViewSimpleDelegate_CellRefreshDelegate_Event(LuaFunction func) : base(func) { }
		public ListViewSimpleDelegate_CellRefreshDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(ListViewCell param0, int param1, int param2)
		{
			func.BeginPCall();
			func.PushSealed(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(ListViewCell param0, int param1, int param2)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushSealed(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			func.EndPCall();
		}
	}

	public ListViewSimpleDelegate.CellRefreshDelegate ListViewSimpleDelegate_CellRefreshDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			ListViewSimpleDelegate.CellRefreshDelegate fn = delegate(ListViewCell param0, int param1, int param2) { };
			return fn;
		}

		if(!flag)
		{
			ListViewSimpleDelegate_CellRefreshDelegate_Event target = new ListViewSimpleDelegate_CellRefreshDelegate_Event(func);
			ListViewSimpleDelegate.CellRefreshDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			ListViewSimpleDelegate_CellRefreshDelegate_Event target = new ListViewSimpleDelegate_CellRefreshDelegate_Event(func, self);
			ListViewSimpleDelegate.CellRefreshDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_ListViewSimpleDelegate_CellRefreshDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(ListViewSimpleDelegate.CellRefreshDelegate), L, pos);
	}

	void Push_ListViewSimpleDelegate_CellRefreshDelegate(IntPtr L, ListViewSimpleDelegate.CellRefreshDelegate o)
	{
		ToLua.Push(L, o);
	}

	class PageViewSimpleDelegate_NumberOfCellsDelegate_Event : LuaDelegate
	{
		public PageViewSimpleDelegate_NumberOfCellsDelegate_Event(LuaFunction func) : base(func) { }
		public PageViewSimpleDelegate_NumberOfCellsDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public int Call()
		{
			func.BeginPCall();
			func.PCall();
			int ret = (int)func.CheckNumber();
			func.EndPCall();
			return ret;
		}

		public int CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			int ret = (int)func.CheckNumber();
			func.EndPCall();
			return ret;
		}
	}

	public PageViewSimpleDelegate.NumberOfCellsDelegate PageViewSimpleDelegate_NumberOfCellsDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			PageViewSimpleDelegate.NumberOfCellsDelegate fn = delegate() { return 0; };
			return fn;
		}

		if(!flag)
		{
			PageViewSimpleDelegate_NumberOfCellsDelegate_Event target = new PageViewSimpleDelegate_NumberOfCellsDelegate_Event(func);
			PageViewSimpleDelegate.NumberOfCellsDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			PageViewSimpleDelegate_NumberOfCellsDelegate_Event target = new PageViewSimpleDelegate_NumberOfCellsDelegate_Event(func, self);
			PageViewSimpleDelegate.NumberOfCellsDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_PageViewSimpleDelegate_NumberOfCellsDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(PageViewSimpleDelegate.NumberOfCellsDelegate), L, pos);
	}

	void Push_PageViewSimpleDelegate_NumberOfCellsDelegate(IntPtr L, PageViewSimpleDelegate.NumberOfCellsDelegate o)
	{
		ToLua.Push(L, o);
	}

	class PageViewSimpleDelegate_CellRefreshDelegate_Event : LuaDelegate
	{
		public PageViewSimpleDelegate_CellRefreshDelegate_Event(LuaFunction func) : base(func) { }
		public PageViewSimpleDelegate_CellRefreshDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(int param0, UnityEngine.GameObject param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PushSealed(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(int param0, UnityEngine.GameObject param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PushSealed(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public PageViewSimpleDelegate.CellRefreshDelegate PageViewSimpleDelegate_CellRefreshDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			PageViewSimpleDelegate.CellRefreshDelegate fn = delegate(int param0, UnityEngine.GameObject param1) { };
			return fn;
		}

		if(!flag)
		{
			PageViewSimpleDelegate_CellRefreshDelegate_Event target = new PageViewSimpleDelegate_CellRefreshDelegate_Event(func);
			PageViewSimpleDelegate.CellRefreshDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			PageViewSimpleDelegate_CellRefreshDelegate_Event target = new PageViewSimpleDelegate_CellRefreshDelegate_Event(func, self);
			PageViewSimpleDelegate.CellRefreshDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_PageViewSimpleDelegate_CellRefreshDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(PageViewSimpleDelegate.CellRefreshDelegate), L, pos);
	}

	void Push_PageViewSimpleDelegate_CellRefreshDelegate(IntPtr L, PageViewSimpleDelegate.CellRefreshDelegate o)
	{
		ToLua.Push(L, o);
	}

	class PageViewSimpleDelegate_CellRecycleDelegate_Event : LuaDelegate
	{
		public PageViewSimpleDelegate_CellRecycleDelegate_Event(LuaFunction func) : base(func) { }
		public PageViewSimpleDelegate_CellRecycleDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(int param0, UnityEngine.GameObject param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PushSealed(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(int param0, UnityEngine.GameObject param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PushSealed(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public PageViewSimpleDelegate.CellRecycleDelegate PageViewSimpleDelegate_CellRecycleDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			PageViewSimpleDelegate.CellRecycleDelegate fn = delegate(int param0, UnityEngine.GameObject param1) { };
			return fn;
		}

		if(!flag)
		{
			PageViewSimpleDelegate_CellRecycleDelegate_Event target = new PageViewSimpleDelegate_CellRecycleDelegate_Event(func);
			PageViewSimpleDelegate.CellRecycleDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			PageViewSimpleDelegate_CellRecycleDelegate_Event target = new PageViewSimpleDelegate_CellRecycleDelegate_Event(func, self);
			PageViewSimpleDelegate.CellRecycleDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_PageViewSimpleDelegate_CellRecycleDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(PageViewSimpleDelegate.CellRecycleDelegate), L, pos);
	}

	void Push_PageViewSimpleDelegate_CellRecycleDelegate(IntPtr L, PageViewSimpleDelegate.CellRecycleDelegate o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_UnityEngine_GameObject_int_Event : LuaDelegate
	{
		public System_Action_UnityEngine_GameObject_int_Event(LuaFunction func) : base(func) { }
		public System_Action_UnityEngine_GameObject_int_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.GameObject param0, int param1)
		{
			func.BeginPCall();
			func.PushSealed(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.GameObject param0, int param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushSealed(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<UnityEngine.GameObject,int> System_Action_UnityEngine_GameObject_int(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<UnityEngine.GameObject,int> fn = delegate(UnityEngine.GameObject param0, int param1) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_UnityEngine_GameObject_int_Event target = new System_Action_UnityEngine_GameObject_int_Event(func);
			System.Action<UnityEngine.GameObject,int> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_UnityEngine_GameObject_int_Event target = new System_Action_UnityEngine_GameObject_int_Event(func, self);
			System.Action<UnityEngine.GameObject,int> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_UnityEngine_GameObject_int(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<UnityEngine.GameObject,int>), L, pos);
	}

	void Push_System_Action_UnityEngine_GameObject_int(IntPtr L, System.Action<UnityEngine.GameObject,int> o)
	{
		ToLua.Push(L, o);
	}

	class ListenTrigger_TriggerEnterDelegate_Event : LuaDelegate
	{
		public ListenTrigger_TriggerEnterDelegate_Event(LuaFunction func) : base(func) { }
		public ListenTrigger_TriggerEnterDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.GameObject param0)
		{
			func.BeginPCall();
			func.PushSealed(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.GameObject param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushSealed(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public ListenTrigger.TriggerEnterDelegate ListenTrigger_TriggerEnterDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			ListenTrigger.TriggerEnterDelegate fn = delegate(UnityEngine.GameObject param0) { };
			return fn;
		}

		if(!flag)
		{
			ListenTrigger_TriggerEnterDelegate_Event target = new ListenTrigger_TriggerEnterDelegate_Event(func);
			ListenTrigger.TriggerEnterDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			ListenTrigger_TriggerEnterDelegate_Event target = new ListenTrigger_TriggerEnterDelegate_Event(func, self);
			ListenTrigger.TriggerEnterDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_ListenTrigger_TriggerEnterDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(ListenTrigger.TriggerEnterDelegate), L, pos);
	}

	void Push_ListenTrigger_TriggerEnterDelegate(IntPtr L, ListenTrigger.TriggerEnterDelegate o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_TouchCancelHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_TouchCancelHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_TouchCancelHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.TouchCancelHandler HedgehogTeam_EasyTouch_EasyTouch_TouchCancelHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.TouchCancelHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_TouchCancelHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_TouchCancelHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.TouchCancelHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_TouchCancelHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_TouchCancelHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.TouchCancelHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_TouchCancelHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.TouchCancelHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_TouchCancelHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.TouchCancelHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_Cancel2FingersHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_Cancel2FingersHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_Cancel2FingersHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.Cancel2FingersHandler HedgehogTeam_EasyTouch_EasyTouch_Cancel2FingersHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.Cancel2FingersHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_Cancel2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_Cancel2FingersHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.Cancel2FingersHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_Cancel2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_Cancel2FingersHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.Cancel2FingersHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_Cancel2FingersHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.Cancel2FingersHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_Cancel2FingersHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.Cancel2FingersHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_TouchStartHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_TouchStartHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_TouchStartHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.TouchStartHandler HedgehogTeam_EasyTouch_EasyTouch_TouchStartHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.TouchStartHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_TouchStartHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_TouchStartHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.TouchStartHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_TouchStartHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_TouchStartHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.TouchStartHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_TouchStartHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.TouchStartHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_TouchStartHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.TouchStartHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_TouchDownHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_TouchDownHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_TouchDownHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.TouchDownHandler HedgehogTeam_EasyTouch_EasyTouch_TouchDownHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.TouchDownHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_TouchDownHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_TouchDownHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.TouchDownHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_TouchDownHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_TouchDownHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.TouchDownHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_TouchDownHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.TouchDownHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_TouchDownHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.TouchDownHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_TouchUpHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_TouchUpHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_TouchUpHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.TouchUpHandler HedgehogTeam_EasyTouch_EasyTouch_TouchUpHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.TouchUpHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_TouchUpHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_TouchUpHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.TouchUpHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_TouchUpHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_TouchUpHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.TouchUpHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_TouchUpHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.TouchUpHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_TouchUpHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.TouchUpHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_SimpleTapHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_SimpleTapHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_SimpleTapHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.SimpleTapHandler HedgehogTeam_EasyTouch_EasyTouch_SimpleTapHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.SimpleTapHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_SimpleTapHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_SimpleTapHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.SimpleTapHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_SimpleTapHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_SimpleTapHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.SimpleTapHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_SimpleTapHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.SimpleTapHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_SimpleTapHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.SimpleTapHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_DoubleTapHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_DoubleTapHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_DoubleTapHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.DoubleTapHandler HedgehogTeam_EasyTouch_EasyTouch_DoubleTapHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.DoubleTapHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_DoubleTapHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_DoubleTapHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.DoubleTapHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_DoubleTapHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_DoubleTapHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.DoubleTapHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_DoubleTapHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.DoubleTapHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_DoubleTapHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.DoubleTapHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_LongTapStartHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_LongTapStartHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_LongTapStartHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.LongTapStartHandler HedgehogTeam_EasyTouch_EasyTouch_LongTapStartHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.LongTapStartHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_LongTapStartHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_LongTapStartHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.LongTapStartHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_LongTapStartHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_LongTapStartHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.LongTapStartHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_LongTapStartHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.LongTapStartHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_LongTapStartHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.LongTapStartHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_LongTapHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_LongTapHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_LongTapHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.LongTapHandler HedgehogTeam_EasyTouch_EasyTouch_LongTapHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.LongTapHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_LongTapHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_LongTapHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.LongTapHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_LongTapHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_LongTapHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.LongTapHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_LongTapHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.LongTapHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_LongTapHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.LongTapHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_LongTapEndHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_LongTapEndHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_LongTapEndHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.LongTapEndHandler HedgehogTeam_EasyTouch_EasyTouch_LongTapEndHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.LongTapEndHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_LongTapEndHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_LongTapEndHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.LongTapEndHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_LongTapEndHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_LongTapEndHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.LongTapEndHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_LongTapEndHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.LongTapEndHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_LongTapEndHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.LongTapEndHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_DragStartHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_DragStartHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_DragStartHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.DragStartHandler HedgehogTeam_EasyTouch_EasyTouch_DragStartHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.DragStartHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_DragStartHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_DragStartHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.DragStartHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_DragStartHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_DragStartHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.DragStartHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_DragStartHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.DragStartHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_DragStartHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.DragStartHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_DragHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_DragHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_DragHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.DragHandler HedgehogTeam_EasyTouch_EasyTouch_DragHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.DragHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_DragHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_DragHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.DragHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_DragHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_DragHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.DragHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_DragHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.DragHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_DragHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.DragHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_DragEndHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_DragEndHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_DragEndHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.DragEndHandler HedgehogTeam_EasyTouch_EasyTouch_DragEndHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.DragEndHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_DragEndHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_DragEndHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.DragEndHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_DragEndHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_DragEndHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.DragEndHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_DragEndHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.DragEndHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_DragEndHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.DragEndHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_SwipeStartHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_SwipeStartHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_SwipeStartHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.SwipeStartHandler HedgehogTeam_EasyTouch_EasyTouch_SwipeStartHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.SwipeStartHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_SwipeStartHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_SwipeStartHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.SwipeStartHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_SwipeStartHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_SwipeStartHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.SwipeStartHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_SwipeStartHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.SwipeStartHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_SwipeStartHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.SwipeStartHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_SwipeHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_SwipeHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_SwipeHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.SwipeHandler HedgehogTeam_EasyTouch_EasyTouch_SwipeHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.SwipeHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_SwipeHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_SwipeHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.SwipeHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_SwipeHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_SwipeHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.SwipeHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_SwipeHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.SwipeHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_SwipeHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.SwipeHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_SwipeEndHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_SwipeEndHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_SwipeEndHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.SwipeEndHandler HedgehogTeam_EasyTouch_EasyTouch_SwipeEndHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.SwipeEndHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_SwipeEndHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_SwipeEndHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.SwipeEndHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_SwipeEndHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_SwipeEndHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.SwipeEndHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_SwipeEndHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.SwipeEndHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_SwipeEndHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.SwipeEndHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_TouchStart2FingersHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_TouchStart2FingersHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_TouchStart2FingersHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.TouchStart2FingersHandler HedgehogTeam_EasyTouch_EasyTouch_TouchStart2FingersHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.TouchStart2FingersHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_TouchStart2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_TouchStart2FingersHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.TouchStart2FingersHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_TouchStart2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_TouchStart2FingersHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.TouchStart2FingersHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_TouchStart2FingersHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.TouchStart2FingersHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_TouchStart2FingersHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.TouchStart2FingersHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_TouchDown2FingersHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_TouchDown2FingersHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_TouchDown2FingersHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.TouchDown2FingersHandler HedgehogTeam_EasyTouch_EasyTouch_TouchDown2FingersHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.TouchDown2FingersHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_TouchDown2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_TouchDown2FingersHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.TouchDown2FingersHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_TouchDown2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_TouchDown2FingersHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.TouchDown2FingersHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_TouchDown2FingersHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.TouchDown2FingersHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_TouchDown2FingersHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.TouchDown2FingersHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_TouchUp2FingersHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_TouchUp2FingersHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_TouchUp2FingersHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.TouchUp2FingersHandler HedgehogTeam_EasyTouch_EasyTouch_TouchUp2FingersHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.TouchUp2FingersHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_TouchUp2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_TouchUp2FingersHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.TouchUp2FingersHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_TouchUp2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_TouchUp2FingersHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.TouchUp2FingersHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_TouchUp2FingersHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.TouchUp2FingersHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_TouchUp2FingersHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.TouchUp2FingersHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_SimpleTap2FingersHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_SimpleTap2FingersHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_SimpleTap2FingersHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.SimpleTap2FingersHandler HedgehogTeam_EasyTouch_EasyTouch_SimpleTap2FingersHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.SimpleTap2FingersHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_SimpleTap2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_SimpleTap2FingersHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.SimpleTap2FingersHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_SimpleTap2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_SimpleTap2FingersHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.SimpleTap2FingersHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_SimpleTap2FingersHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.SimpleTap2FingersHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_SimpleTap2FingersHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.SimpleTap2FingersHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_DoubleTap2FingersHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_DoubleTap2FingersHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_DoubleTap2FingersHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.DoubleTap2FingersHandler HedgehogTeam_EasyTouch_EasyTouch_DoubleTap2FingersHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.DoubleTap2FingersHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_DoubleTap2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_DoubleTap2FingersHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.DoubleTap2FingersHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_DoubleTap2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_DoubleTap2FingersHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.DoubleTap2FingersHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_DoubleTap2FingersHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.DoubleTap2FingersHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_DoubleTap2FingersHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.DoubleTap2FingersHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_LongTapStart2FingersHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_LongTapStart2FingersHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_LongTapStart2FingersHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.LongTapStart2FingersHandler HedgehogTeam_EasyTouch_EasyTouch_LongTapStart2FingersHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.LongTapStart2FingersHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_LongTapStart2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_LongTapStart2FingersHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.LongTapStart2FingersHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_LongTapStart2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_LongTapStart2FingersHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.LongTapStart2FingersHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_LongTapStart2FingersHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.LongTapStart2FingersHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_LongTapStart2FingersHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.LongTapStart2FingersHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_LongTap2FingersHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_LongTap2FingersHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_LongTap2FingersHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.LongTap2FingersHandler HedgehogTeam_EasyTouch_EasyTouch_LongTap2FingersHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.LongTap2FingersHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_LongTap2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_LongTap2FingersHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.LongTap2FingersHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_LongTap2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_LongTap2FingersHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.LongTap2FingersHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_LongTap2FingersHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.LongTap2FingersHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_LongTap2FingersHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.LongTap2FingersHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_LongTapEnd2FingersHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_LongTapEnd2FingersHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_LongTapEnd2FingersHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.LongTapEnd2FingersHandler HedgehogTeam_EasyTouch_EasyTouch_LongTapEnd2FingersHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.LongTapEnd2FingersHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_LongTapEnd2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_LongTapEnd2FingersHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.LongTapEnd2FingersHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_LongTapEnd2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_LongTapEnd2FingersHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.LongTapEnd2FingersHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_LongTapEnd2FingersHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.LongTapEnd2FingersHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_LongTapEnd2FingersHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.LongTapEnd2FingersHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_TwistHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_TwistHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_TwistHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.TwistHandler HedgehogTeam_EasyTouch_EasyTouch_TwistHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.TwistHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_TwistHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_TwistHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.TwistHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_TwistHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_TwistHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.TwistHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_TwistHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.TwistHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_TwistHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.TwistHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_TwistEndHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_TwistEndHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_TwistEndHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.TwistEndHandler HedgehogTeam_EasyTouch_EasyTouch_TwistEndHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.TwistEndHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_TwistEndHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_TwistEndHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.TwistEndHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_TwistEndHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_TwistEndHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.TwistEndHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_TwistEndHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.TwistEndHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_TwistEndHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.TwistEndHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_PinchHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_PinchHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_PinchHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.PinchHandler HedgehogTeam_EasyTouch_EasyTouch_PinchHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.PinchHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_PinchHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_PinchHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.PinchHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_PinchHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_PinchHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.PinchHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_PinchHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.PinchHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_PinchHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.PinchHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_PinchInHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_PinchInHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_PinchInHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.PinchInHandler HedgehogTeam_EasyTouch_EasyTouch_PinchInHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.PinchInHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_PinchInHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_PinchInHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.PinchInHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_PinchInHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_PinchInHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.PinchInHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_PinchInHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.PinchInHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_PinchInHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.PinchInHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_PinchOutHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_PinchOutHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_PinchOutHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.PinchOutHandler HedgehogTeam_EasyTouch_EasyTouch_PinchOutHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.PinchOutHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_PinchOutHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_PinchOutHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.PinchOutHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_PinchOutHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_PinchOutHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.PinchOutHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_PinchOutHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.PinchOutHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_PinchOutHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.PinchOutHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_PinchEndHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_PinchEndHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_PinchEndHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.PinchEndHandler HedgehogTeam_EasyTouch_EasyTouch_PinchEndHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.PinchEndHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_PinchEndHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_PinchEndHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.PinchEndHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_PinchEndHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_PinchEndHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.PinchEndHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_PinchEndHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.PinchEndHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_PinchEndHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.PinchEndHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_DragStart2FingersHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_DragStart2FingersHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_DragStart2FingersHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.DragStart2FingersHandler HedgehogTeam_EasyTouch_EasyTouch_DragStart2FingersHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.DragStart2FingersHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_DragStart2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_DragStart2FingersHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.DragStart2FingersHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_DragStart2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_DragStart2FingersHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.DragStart2FingersHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_DragStart2FingersHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.DragStart2FingersHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_DragStart2FingersHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.DragStart2FingersHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_Drag2FingersHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_Drag2FingersHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_Drag2FingersHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.Drag2FingersHandler HedgehogTeam_EasyTouch_EasyTouch_Drag2FingersHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.Drag2FingersHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_Drag2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_Drag2FingersHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.Drag2FingersHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_Drag2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_Drag2FingersHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.Drag2FingersHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_Drag2FingersHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.Drag2FingersHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_Drag2FingersHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.Drag2FingersHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_DragEnd2FingersHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_DragEnd2FingersHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_DragEnd2FingersHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.DragEnd2FingersHandler HedgehogTeam_EasyTouch_EasyTouch_DragEnd2FingersHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.DragEnd2FingersHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_DragEnd2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_DragEnd2FingersHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.DragEnd2FingersHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_DragEnd2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_DragEnd2FingersHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.DragEnd2FingersHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_DragEnd2FingersHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.DragEnd2FingersHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_DragEnd2FingersHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.DragEnd2FingersHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_SwipeStart2FingersHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_SwipeStart2FingersHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_SwipeStart2FingersHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.SwipeStart2FingersHandler HedgehogTeam_EasyTouch_EasyTouch_SwipeStart2FingersHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.SwipeStart2FingersHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_SwipeStart2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_SwipeStart2FingersHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.SwipeStart2FingersHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_SwipeStart2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_SwipeStart2FingersHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.SwipeStart2FingersHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_SwipeStart2FingersHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.SwipeStart2FingersHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_SwipeStart2FingersHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.SwipeStart2FingersHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_Swipe2FingersHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_Swipe2FingersHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_Swipe2FingersHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.Swipe2FingersHandler HedgehogTeam_EasyTouch_EasyTouch_Swipe2FingersHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.Swipe2FingersHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_Swipe2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_Swipe2FingersHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.Swipe2FingersHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_Swipe2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_Swipe2FingersHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.Swipe2FingersHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_Swipe2FingersHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.Swipe2FingersHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_Swipe2FingersHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.Swipe2FingersHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_SwipeEnd2FingersHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_SwipeEnd2FingersHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_SwipeEnd2FingersHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.SwipeEnd2FingersHandler HedgehogTeam_EasyTouch_EasyTouch_SwipeEnd2FingersHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.SwipeEnd2FingersHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_SwipeEnd2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_SwipeEnd2FingersHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.SwipeEnd2FingersHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_SwipeEnd2FingersHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_SwipeEnd2FingersHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.SwipeEnd2FingersHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_SwipeEnd2FingersHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.SwipeEnd2FingersHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_SwipeEnd2FingersHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.SwipeEnd2FingersHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_EasyTouchIsReadyHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_EasyTouchIsReadyHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_EasyTouchIsReadyHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call()
		{
			func.Call();
		}

		public void CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.EasyTouchIsReadyHandler HedgehogTeam_EasyTouch_EasyTouch_EasyTouchIsReadyHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.EasyTouchIsReadyHandler fn = delegate() { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_EasyTouchIsReadyHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_EasyTouchIsReadyHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.EasyTouchIsReadyHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_EasyTouchIsReadyHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_EasyTouchIsReadyHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.EasyTouchIsReadyHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_EasyTouchIsReadyHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.EasyTouchIsReadyHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_EasyTouchIsReadyHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.EasyTouchIsReadyHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_OverUIElementHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_OverUIElementHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_OverUIElementHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.OverUIElementHandler HedgehogTeam_EasyTouch_EasyTouch_OverUIElementHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.OverUIElementHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_OverUIElementHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_OverUIElementHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.OverUIElementHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_OverUIElementHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_OverUIElementHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.OverUIElementHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_OverUIElementHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.OverUIElementHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_OverUIElementHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.OverUIElementHandler o)
	{
		ToLua.Push(L, o);
	}

	class HedgehogTeam_EasyTouch_EasyTouch_UIElementTouchUpHandler_Event : LuaDelegate
	{
		public HedgehogTeam_EasyTouch_EasyTouch_UIElementTouchUpHandler_Event(LuaFunction func) : base(func) { }
		public HedgehogTeam_EasyTouch_EasyTouch_UIElementTouchUpHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(HedgehogTeam.EasyTouch.Gesture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public HedgehogTeam.EasyTouch.EasyTouch.UIElementTouchUpHandler HedgehogTeam_EasyTouch_EasyTouch_UIElementTouchUpHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			HedgehogTeam.EasyTouch.EasyTouch.UIElementTouchUpHandler fn = delegate(HedgehogTeam.EasyTouch.Gesture param0) { };
			return fn;
		}

		if(!flag)
		{
			HedgehogTeam_EasyTouch_EasyTouch_UIElementTouchUpHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_UIElementTouchUpHandler_Event(func);
			HedgehogTeam.EasyTouch.EasyTouch.UIElementTouchUpHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			HedgehogTeam_EasyTouch_EasyTouch_UIElementTouchUpHandler_Event target = new HedgehogTeam_EasyTouch_EasyTouch_UIElementTouchUpHandler_Event(func, self);
			HedgehogTeam.EasyTouch.EasyTouch.UIElementTouchUpHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_HedgehogTeam_EasyTouch_EasyTouch_UIElementTouchUpHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(HedgehogTeam.EasyTouch.EasyTouch.UIElementTouchUpHandler), L, pos);
	}

	void Push_HedgehogTeam_EasyTouch_EasyTouch_UIElementTouchUpHandler(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.UIElementTouchUpHandler o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_Events_UnityAction_string_string_UnityEngine_Object_Event : LuaDelegate
	{
		public UnityEngine_Events_UnityAction_string_string_UnityEngine_Object_Event(LuaFunction func) : base(func) { }
		public UnityEngine_Events_UnityAction_string_string_UnityEngine_Object_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(string param0, string param1, UnityEngine.Object param2)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(string param0, string param1, UnityEngine.Object param2)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			func.EndPCall();
		}
	}

	public UnityEngine.Events.UnityAction<string,string,UnityEngine.Object> UnityEngine_Events_UnityAction_string_string_UnityEngine_Object(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.Events.UnityAction<string,string,UnityEngine.Object> fn = delegate(string param0, string param1, UnityEngine.Object param2) { };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_Events_UnityAction_string_string_UnityEngine_Object_Event target = new UnityEngine_Events_UnityAction_string_string_UnityEngine_Object_Event(func);
			UnityEngine.Events.UnityAction<string,string,UnityEngine.Object> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_Events_UnityAction_string_string_UnityEngine_Object_Event target = new UnityEngine_Events_UnityAction_string_string_UnityEngine_Object_Event(func, self);
			UnityEngine.Events.UnityAction<string,string,UnityEngine.Object> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_Events_UnityAction_string_string_UnityEngine_Object(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.Events.UnityAction<string,string,UnityEngine.Object>), L, pos);
	}

	void Push_UnityEngine_Events_UnityAction_string_string_UnityEngine_Object(IntPtr L, UnityEngine.Events.UnityAction<string,string,UnityEngine.Object> o)
	{
		ToLua.Push(L, o);
	}

	class UIGlassBlurCtrl_OnRenderTextureSetup_Event : LuaDelegate
	{
		public UIGlassBlurCtrl_OnRenderTextureSetup_Event(LuaFunction func) : base(func) { }
		public UIGlassBlurCtrl_OnRenderTextureSetup_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.Texture param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.Texture param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public UIGlassBlurCtrl.OnRenderTextureSetup UIGlassBlurCtrl_OnRenderTextureSetup(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UIGlassBlurCtrl.OnRenderTextureSetup fn = delegate(UnityEngine.Texture param0) { };
			return fn;
		}

		if(!flag)
		{
			UIGlassBlurCtrl_OnRenderTextureSetup_Event target = new UIGlassBlurCtrl_OnRenderTextureSetup_Event(func);
			UIGlassBlurCtrl.OnRenderTextureSetup d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UIGlassBlurCtrl_OnRenderTextureSetup_Event target = new UIGlassBlurCtrl_OnRenderTextureSetup_Event(func, self);
			UIGlassBlurCtrl.OnRenderTextureSetup d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UIGlassBlurCtrl_OnRenderTextureSetup(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UIGlassBlurCtrl.OnRenderTextureSetup), L, pos);
	}

	void Push_UIGlassBlurCtrl_OnRenderTextureSetup(IntPtr L, UIGlassBlurCtrl.OnRenderTextureSetup o)
	{
		ToLua.Push(L, o);
	}

	class Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateSingle_Event : LuaDelegate
	{
		public Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateSingle_Event(LuaFunction func) : base(func) { }
		public Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateSingle_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.Mesh param0, UnityEngine.Material param1, UnityEngine.Texture param2)
		{
			func.BeginPCall();
			func.PushSealed(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.Mesh param0, UnityEngine.Material param1, UnityEngine.Texture param2)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushSealed(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			func.EndPCall();
		}
	}

	public Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateSingle Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateSingle(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateSingle fn = delegate(UnityEngine.Mesh param0, UnityEngine.Material param1, UnityEngine.Texture param2) { };
			return fn;
		}

		if(!flag)
		{
			Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateSingle_Event target = new Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateSingle_Event(func);
			Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateSingle d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateSingle_Event target = new Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateSingle_Event(func, self);
			Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateSingle d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateSingle(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateSingle), L, pos);
	}

	void Push_Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateSingle(IntPtr L, Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateSingle o)
	{
		ToLua.Push(L, o);
	}

	class Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateMultiple_Event : LuaDelegate
	{
		public Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateMultiple_Event(LuaFunction func) : base(func) { }
		public Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateMultiple_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(int param0, UnityEngine.Mesh[] param1, UnityEngine.Material[] param2, UnityEngine.Texture[] param3)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.Push(param3);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(int param0, UnityEngine.Mesh[] param1, UnityEngine.Material[] param2, UnityEngine.Texture[] param3)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.Push(param3);
			func.PCall();
			func.EndPCall();
		}
	}

	public Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateMultiple Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateMultiple(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateMultiple fn = delegate(int param0, UnityEngine.Mesh[] param1, UnityEngine.Material[] param2, UnityEngine.Texture[] param3) { };
			return fn;
		}

		if(!flag)
		{
			Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateMultiple_Event target = new Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateMultiple_Event(func);
			Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateMultiple d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateMultiple_Event target = new Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateMultiple_Event(func, self);
			Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateMultiple d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateMultiple(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateMultiple), L, pos);
	}

	void Push_Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateMultiple(IntPtr L, Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateMultiple o)
	{
		ToLua.Push(L, o);
	}

	class Spine_Unity_SkeletonGraphic_SkeletonRendererDelegate_Event : LuaDelegate
	{
		public Spine_Unity_SkeletonGraphic_SkeletonRendererDelegate_Event(LuaFunction func) : base(func) { }
		public Spine_Unity_SkeletonGraphic_SkeletonRendererDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(Spine.Unity.SkeletonGraphic param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(Spine.Unity.SkeletonGraphic param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public Spine.Unity.SkeletonGraphic.SkeletonRendererDelegate Spine_Unity_SkeletonGraphic_SkeletonRendererDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			Spine.Unity.SkeletonGraphic.SkeletonRendererDelegate fn = delegate(Spine.Unity.SkeletonGraphic param0) { };
			return fn;
		}

		if(!flag)
		{
			Spine_Unity_SkeletonGraphic_SkeletonRendererDelegate_Event target = new Spine_Unity_SkeletonGraphic_SkeletonRendererDelegate_Event(func);
			Spine.Unity.SkeletonGraphic.SkeletonRendererDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			Spine_Unity_SkeletonGraphic_SkeletonRendererDelegate_Event target = new Spine_Unity_SkeletonGraphic_SkeletonRendererDelegate_Event(func, self);
			Spine.Unity.SkeletonGraphic.SkeletonRendererDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_Spine_Unity_SkeletonGraphic_SkeletonRendererDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(Spine.Unity.SkeletonGraphic.SkeletonRendererDelegate), L, pos);
	}

	void Push_Spine_Unity_SkeletonGraphic_SkeletonRendererDelegate(IntPtr L, Spine.Unity.SkeletonGraphic.SkeletonRendererDelegate o)
	{
		ToLua.Push(L, o);
	}

	class Spine_Unity_SkeletonGraphic_InstructionDelegate_Event : LuaDelegate
	{
		public Spine_Unity_SkeletonGraphic_InstructionDelegate_Event(LuaFunction func) : base(func) { }
		public Spine_Unity_SkeletonGraphic_InstructionDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(Spine.Unity.SkeletonRendererInstruction param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(Spine.Unity.SkeletonRendererInstruction param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public Spine.Unity.SkeletonGraphic.InstructionDelegate Spine_Unity_SkeletonGraphic_InstructionDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			Spine.Unity.SkeletonGraphic.InstructionDelegate fn = delegate(Spine.Unity.SkeletonRendererInstruction param0) { };
			return fn;
		}

		if(!flag)
		{
			Spine_Unity_SkeletonGraphic_InstructionDelegate_Event target = new Spine_Unity_SkeletonGraphic_InstructionDelegate_Event(func);
			Spine.Unity.SkeletonGraphic.InstructionDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			Spine_Unity_SkeletonGraphic_InstructionDelegate_Event target = new Spine_Unity_SkeletonGraphic_InstructionDelegate_Event(func, self);
			Spine.Unity.SkeletonGraphic.InstructionDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_Spine_Unity_SkeletonGraphic_InstructionDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(Spine.Unity.SkeletonGraphic.InstructionDelegate), L, pos);
	}

	void Push_Spine_Unity_SkeletonGraphic_InstructionDelegate(IntPtr L, Spine.Unity.SkeletonGraphic.InstructionDelegate o)
	{
		ToLua.Push(L, o);
	}

	class Spine_Unity_ISkeletonAnimationDelegate_Event : LuaDelegate
	{
		public Spine_Unity_ISkeletonAnimationDelegate_Event(LuaFunction func) : base(func) { }
		public Spine_Unity_ISkeletonAnimationDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(Spine.Unity.ISkeletonAnimation param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(Spine.Unity.ISkeletonAnimation param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public Spine.Unity.ISkeletonAnimationDelegate Spine_Unity_ISkeletonAnimationDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			Spine.Unity.ISkeletonAnimationDelegate fn = delegate(Spine.Unity.ISkeletonAnimation param0) { };
			return fn;
		}

		if(!flag)
		{
			Spine_Unity_ISkeletonAnimationDelegate_Event target = new Spine_Unity_ISkeletonAnimationDelegate_Event(func);
			Spine.Unity.ISkeletonAnimationDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			Spine_Unity_ISkeletonAnimationDelegate_Event target = new Spine_Unity_ISkeletonAnimationDelegate_Event(func, self);
			Spine.Unity.ISkeletonAnimationDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_Spine_Unity_ISkeletonAnimationDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(Spine.Unity.ISkeletonAnimationDelegate), L, pos);
	}

	void Push_Spine_Unity_ISkeletonAnimationDelegate(IntPtr L, Spine.Unity.ISkeletonAnimationDelegate o)
	{
		ToLua.Push(L, o);
	}

	class Spine_Unity_UpdateBonesDelegate_Event : LuaDelegate
	{
		public Spine_Unity_UpdateBonesDelegate_Event(LuaFunction func) : base(func) { }
		public Spine_Unity_UpdateBonesDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(Spine.Unity.ISkeletonAnimation param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(Spine.Unity.ISkeletonAnimation param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public Spine.Unity.UpdateBonesDelegate Spine_Unity_UpdateBonesDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			Spine.Unity.UpdateBonesDelegate fn = delegate(Spine.Unity.ISkeletonAnimation param0) { };
			return fn;
		}

		if(!flag)
		{
			Spine_Unity_UpdateBonesDelegate_Event target = new Spine_Unity_UpdateBonesDelegate_Event(func);
			Spine.Unity.UpdateBonesDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			Spine_Unity_UpdateBonesDelegate_Event target = new Spine_Unity_UpdateBonesDelegate_Event(func, self);
			Spine.Unity.UpdateBonesDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_Spine_Unity_UpdateBonesDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(Spine.Unity.UpdateBonesDelegate), L, pos);
	}

	void Push_Spine_Unity_UpdateBonesDelegate(IntPtr L, Spine.Unity.UpdateBonesDelegate o)
	{
		ToLua.Push(L, o);
	}

	class Spine_Unity_MeshGeneratorDelegate_Event : LuaDelegate
	{
		public Spine_Unity_MeshGeneratorDelegate_Event(LuaFunction func) : base(func) { }
		public Spine_Unity_MeshGeneratorDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(Spine.Unity.MeshGeneratorBuffers param0)
		{
			func.BeginPCall();
			func.PushValue(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(Spine.Unity.MeshGeneratorBuffers param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushValue(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public Spine.Unity.MeshGeneratorDelegate Spine_Unity_MeshGeneratorDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			Spine.Unity.MeshGeneratorDelegate fn = delegate(Spine.Unity.MeshGeneratorBuffers param0) { };
			return fn;
		}

		if(!flag)
		{
			Spine_Unity_MeshGeneratorDelegate_Event target = new Spine_Unity_MeshGeneratorDelegate_Event(func);
			Spine.Unity.MeshGeneratorDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			Spine_Unity_MeshGeneratorDelegate_Event target = new Spine_Unity_MeshGeneratorDelegate_Event(func, self);
			Spine.Unity.MeshGeneratorDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_Spine_Unity_MeshGeneratorDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(Spine.Unity.MeshGeneratorDelegate), L, pos);
	}

	void Push_Spine_Unity_MeshGeneratorDelegate(IntPtr L, Spine.Unity.MeshGeneratorDelegate o)
	{
		ToLua.Push(L, o);
	}

	class Spine_AnimationState_TrackEntryDelegate_Event : LuaDelegate
	{
		public Spine_AnimationState_TrackEntryDelegate_Event(LuaFunction func) : base(func) { }
		public Spine_AnimationState_TrackEntryDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(Spine.TrackEntry param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(Spine.TrackEntry param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public Spine.AnimationState.TrackEntryDelegate Spine_AnimationState_TrackEntryDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			Spine.AnimationState.TrackEntryDelegate fn = delegate(Spine.TrackEntry param0) { };
			return fn;
		}

		if(!flag)
		{
			Spine_AnimationState_TrackEntryDelegate_Event target = new Spine_AnimationState_TrackEntryDelegate_Event(func);
			Spine.AnimationState.TrackEntryDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			Spine_AnimationState_TrackEntryDelegate_Event target = new Spine_AnimationState_TrackEntryDelegate_Event(func, self);
			Spine.AnimationState.TrackEntryDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_Spine_AnimationState_TrackEntryDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(Spine.AnimationState.TrackEntryDelegate), L, pos);
	}

	void Push_Spine_AnimationState_TrackEntryDelegate(IntPtr L, Spine.AnimationState.TrackEntryDelegate o)
	{
		ToLua.Push(L, o);
	}

	class Spine_AnimationState_TrackEntryEventDelegate_Event : LuaDelegate
	{
		public Spine_AnimationState_TrackEntryEventDelegate_Event(LuaFunction func) : base(func) { }
		public Spine_AnimationState_TrackEntryEventDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(Spine.TrackEntry param0, Spine.Event param1)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PushObject(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(Spine.TrackEntry param0, Spine.Event param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PushObject(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public Spine.AnimationState.TrackEntryEventDelegate Spine_AnimationState_TrackEntryEventDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			Spine.AnimationState.TrackEntryEventDelegate fn = delegate(Spine.TrackEntry param0, Spine.Event param1) { };
			return fn;
		}

		if(!flag)
		{
			Spine_AnimationState_TrackEntryEventDelegate_Event target = new Spine_AnimationState_TrackEntryEventDelegate_Event(func);
			Spine.AnimationState.TrackEntryEventDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			Spine_AnimationState_TrackEntryEventDelegate_Event target = new Spine_AnimationState_TrackEntryEventDelegate_Event(func, self);
			Spine.AnimationState.TrackEntryEventDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_Spine_AnimationState_TrackEntryEventDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(Spine.AnimationState.TrackEntryEventDelegate), L, pos);
	}

	void Push_Spine_AnimationState_TrackEntryEventDelegate(IntPtr L, Spine.AnimationState.TrackEntryEventDelegate o)
	{
		ToLua.Push(L, o);
	}

	class System_Predicate_string_Event : LuaDelegate
	{
		public System_Predicate_string_Event(LuaFunction func) : base(func) { }
		public System_Predicate_string_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public bool Call(string param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			bool ret = func.CheckBoolean();
			func.EndPCall();
			return ret;
		}

		public bool CallWithSelf(string param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			bool ret = func.CheckBoolean();
			func.EndPCall();
			return ret;
		}
	}

	public System.Predicate<string> System_Predicate_string(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Predicate<string> fn = delegate(string param0) { return false; };
			return fn;
		}

		if(!flag)
		{
			System_Predicate_string_Event target = new System_Predicate_string_Event(func);
			System.Predicate<string> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Predicate_string_Event target = new System_Predicate_string_Event(func, self);
			System.Predicate<string> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Predicate_string(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Predicate<string>), L, pos);
	}

	void Push_System_Predicate_string(IntPtr L, System.Predicate<string> o)
	{
		ToLua.Push(L, o);
	}

	class System_Comparison_string_Event : LuaDelegate
	{
		public System_Comparison_string_Event(LuaFunction func) : base(func) { }
		public System_Comparison_string_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public int Call(string param0, string param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			int ret = (int)func.CheckNumber();
			func.EndPCall();
			return ret;
		}

		public int CallWithSelf(string param0, string param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			int ret = (int)func.CheckNumber();
			func.EndPCall();
			return ret;
		}
	}

	public System.Comparison<string> System_Comparison_string(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Comparison<string> fn = delegate(string param0, string param1) { return 0; };
			return fn;
		}

		if(!flag)
		{
			System_Comparison_string_Event target = new System_Comparison_string_Event(func);
			System.Comparison<string> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Comparison_string_Event target = new System_Comparison_string_Event(func, self);
			System.Comparison<string> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Comparison_string(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Comparison<string>), L, pos);
	}

	void Push_System_Comparison_string(IntPtr L, System.Comparison<string> o)
	{
		ToLua.Push(L, o);
	}

	class System_Predicate_UnityEngine_Sprite_Event : LuaDelegate
	{
		public System_Predicate_UnityEngine_Sprite_Event(LuaFunction func) : base(func) { }
		public System_Predicate_UnityEngine_Sprite_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public bool Call(UnityEngine.Sprite param0)
		{
			func.BeginPCall();
			func.PushSealed(param0);
			func.PCall();
			bool ret = func.CheckBoolean();
			func.EndPCall();
			return ret;
		}

		public bool CallWithSelf(UnityEngine.Sprite param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushSealed(param0);
			func.PCall();
			bool ret = func.CheckBoolean();
			func.EndPCall();
			return ret;
		}
	}

	public System.Predicate<UnityEngine.Sprite> System_Predicate_UnityEngine_Sprite(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Predicate<UnityEngine.Sprite> fn = delegate(UnityEngine.Sprite param0) { return false; };
			return fn;
		}

		if(!flag)
		{
			System_Predicate_UnityEngine_Sprite_Event target = new System_Predicate_UnityEngine_Sprite_Event(func);
			System.Predicate<UnityEngine.Sprite> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Predicate_UnityEngine_Sprite_Event target = new System_Predicate_UnityEngine_Sprite_Event(func, self);
			System.Predicate<UnityEngine.Sprite> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Predicate_UnityEngine_Sprite(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Predicate<UnityEngine.Sprite>), L, pos);
	}

	void Push_System_Predicate_UnityEngine_Sprite(IntPtr L, System.Predicate<UnityEngine.Sprite> o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_UnityEngine_Sprite_Event : LuaDelegate
	{
		public System_Action_UnityEngine_Sprite_Event(LuaFunction func) : base(func) { }
		public System_Action_UnityEngine_Sprite_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.Sprite param0)
		{
			func.BeginPCall();
			func.PushSealed(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.Sprite param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushSealed(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<UnityEngine.Sprite> System_Action_UnityEngine_Sprite(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<UnityEngine.Sprite> fn = delegate(UnityEngine.Sprite param0) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_UnityEngine_Sprite_Event target = new System_Action_UnityEngine_Sprite_Event(func);
			System.Action<UnityEngine.Sprite> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_UnityEngine_Sprite_Event target = new System_Action_UnityEngine_Sprite_Event(func, self);
			System.Action<UnityEngine.Sprite> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_UnityEngine_Sprite(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<UnityEngine.Sprite>), L, pos);
	}

	void Push_System_Action_UnityEngine_Sprite(IntPtr L, System.Action<UnityEngine.Sprite> o)
	{
		ToLua.Push(L, o);
	}

	class System_Comparison_UnityEngine_Sprite_Event : LuaDelegate
	{
		public System_Comparison_UnityEngine_Sprite_Event(LuaFunction func) : base(func) { }
		public System_Comparison_UnityEngine_Sprite_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public int Call(UnityEngine.Sprite param0, UnityEngine.Sprite param1)
		{
			func.BeginPCall();
			func.PushSealed(param0);
			func.PushSealed(param1);
			func.PCall();
			int ret = (int)func.CheckNumber();
			func.EndPCall();
			return ret;
		}

		public int CallWithSelf(UnityEngine.Sprite param0, UnityEngine.Sprite param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushSealed(param0);
			func.PushSealed(param1);
			func.PCall();
			int ret = (int)func.CheckNumber();
			func.EndPCall();
			return ret;
		}
	}

	public System.Comparison<UnityEngine.Sprite> System_Comparison_UnityEngine_Sprite(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Comparison<UnityEngine.Sprite> fn = delegate(UnityEngine.Sprite param0, UnityEngine.Sprite param1) { return 0; };
			return fn;
		}

		if(!flag)
		{
			System_Comparison_UnityEngine_Sprite_Event target = new System_Comparison_UnityEngine_Sprite_Event(func);
			System.Comparison<UnityEngine.Sprite> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Comparison_UnityEngine_Sprite_Event target = new System_Comparison_UnityEngine_Sprite_Event(func, self);
			System.Comparison<UnityEngine.Sprite> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Comparison_UnityEngine_Sprite(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Comparison<UnityEngine.Sprite>), L, pos);
	}

	void Push_System_Comparison_UnityEngine_Sprite(IntPtr L, System.Comparison<UnityEngine.Sprite> o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_Events_UnityAction_UnityEngine_SpriteRenderer_Event : LuaDelegate
	{
		public UnityEngine_Events_UnityAction_UnityEngine_SpriteRenderer_Event(LuaFunction func) : base(func) { }
		public UnityEngine_Events_UnityAction_UnityEngine_SpriteRenderer_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.SpriteRenderer param0)
		{
			func.BeginPCall();
			func.PushSealed(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.SpriteRenderer param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushSealed(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public UnityEngine.Events.UnityAction<UnityEngine.SpriteRenderer> UnityEngine_Events_UnityAction_UnityEngine_SpriteRenderer(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.Events.UnityAction<UnityEngine.SpriteRenderer> fn = delegate(UnityEngine.SpriteRenderer param0) { };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_Events_UnityAction_UnityEngine_SpriteRenderer_Event target = new UnityEngine_Events_UnityAction_UnityEngine_SpriteRenderer_Event(func);
			UnityEngine.Events.UnityAction<UnityEngine.SpriteRenderer> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_Events_UnityAction_UnityEngine_SpriteRenderer_Event target = new UnityEngine_Events_UnityAction_UnityEngine_SpriteRenderer_Event(func, self);
			UnityEngine.Events.UnityAction<UnityEngine.SpriteRenderer> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_Events_UnityAction_UnityEngine_SpriteRenderer(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.Events.UnityAction<UnityEngine.SpriteRenderer>), L, pos);
	}

	void Push_UnityEngine_Events_UnityAction_UnityEngine_SpriteRenderer(IntPtr L, UnityEngine.Events.UnityAction<UnityEngine.SpriteRenderer> o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_UnityEngine_ReflectionProbe_UnityEngine_ReflectionProbe_ReflectionProbeEvent_Event : LuaDelegate
	{
		public System_Action_UnityEngine_ReflectionProbe_UnityEngine_ReflectionProbe_ReflectionProbeEvent_Event(LuaFunction func) : base(func) { }
		public System_Action_UnityEngine_ReflectionProbe_UnityEngine_ReflectionProbe_ReflectionProbeEvent_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.ReflectionProbe param0, UnityEngine.ReflectionProbe.ReflectionProbeEvent param1)
		{
			func.BeginPCall();
			func.PushSealed(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.ReflectionProbe param0, UnityEngine.ReflectionProbe.ReflectionProbeEvent param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushSealed(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<UnityEngine.ReflectionProbe,UnityEngine.ReflectionProbe.ReflectionProbeEvent> System_Action_UnityEngine_ReflectionProbe_UnityEngine_ReflectionProbe_ReflectionProbeEvent(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<UnityEngine.ReflectionProbe,UnityEngine.ReflectionProbe.ReflectionProbeEvent> fn = delegate(UnityEngine.ReflectionProbe param0, UnityEngine.ReflectionProbe.ReflectionProbeEvent param1) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_UnityEngine_ReflectionProbe_UnityEngine_ReflectionProbe_ReflectionProbeEvent_Event target = new System_Action_UnityEngine_ReflectionProbe_UnityEngine_ReflectionProbe_ReflectionProbeEvent_Event(func);
			System.Action<UnityEngine.ReflectionProbe,UnityEngine.ReflectionProbe.ReflectionProbeEvent> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_UnityEngine_ReflectionProbe_UnityEngine_ReflectionProbe_ReflectionProbeEvent_Event target = new System_Action_UnityEngine_ReflectionProbe_UnityEngine_ReflectionProbe_ReflectionProbeEvent_Event(func, self);
			System.Action<UnityEngine.ReflectionProbe,UnityEngine.ReflectionProbe.ReflectionProbeEvent> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_UnityEngine_ReflectionProbe_UnityEngine_ReflectionProbe_ReflectionProbeEvent(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<UnityEngine.ReflectionProbe,UnityEngine.ReflectionProbe.ReflectionProbeEvent>), L, pos);
	}

	void Push_System_Action_UnityEngine_ReflectionProbe_UnityEngine_ReflectionProbe_ReflectionProbeEvent(IntPtr L, System.Action<UnityEngine.ReflectionProbe,UnityEngine.ReflectionProbe.ReflectionProbeEvent> o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_UnityEngine_Cubemap_Event : LuaDelegate
	{
		public System_Action_UnityEngine_Cubemap_Event(LuaFunction func) : base(func) { }
		public System_Action_UnityEngine_Cubemap_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.Cubemap param0)
		{
			func.BeginPCall();
			func.PushSealed(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.Cubemap param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushSealed(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<UnityEngine.Cubemap> System_Action_UnityEngine_Cubemap(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<UnityEngine.Cubemap> fn = delegate(UnityEngine.Cubemap param0) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_UnityEngine_Cubemap_Event target = new System_Action_UnityEngine_Cubemap_Event(func);
			System.Action<UnityEngine.Cubemap> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_UnityEngine_Cubemap_Event target = new System_Action_UnityEngine_Cubemap_Event(func, self);
			System.Action<UnityEngine.Cubemap> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_UnityEngine_Cubemap(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<UnityEngine.Cubemap>), L, pos);
	}

	void Push_System_Action_UnityEngine_Cubemap(IntPtr L, System.Action<UnityEngine.Cubemap> o)
	{
		ToLua.Push(L, o);
	}

	class CharacterShadows_CharacterShadowManager_OnLightChanged_Event : LuaDelegate
	{
		public CharacterShadows_CharacterShadowManager_OnLightChanged_Event(LuaFunction func) : base(func) { }
		public CharacterShadows_CharacterShadowManager_OnLightChanged_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call()
		{
			func.Call();
		}

		public void CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			func.EndPCall();
		}
	}

	public CharacterShadows.CharacterShadowManager.OnLightChanged CharacterShadows_CharacterShadowManager_OnLightChanged(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			CharacterShadows.CharacterShadowManager.OnLightChanged fn = delegate() { };
			return fn;
		}

		if(!flag)
		{
			CharacterShadows_CharacterShadowManager_OnLightChanged_Event target = new CharacterShadows_CharacterShadowManager_OnLightChanged_Event(func);
			CharacterShadows.CharacterShadowManager.OnLightChanged d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			CharacterShadows_CharacterShadowManager_OnLightChanged_Event target = new CharacterShadows_CharacterShadowManager_OnLightChanged_Event(func, self);
			CharacterShadows.CharacterShadowManager.OnLightChanged d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_CharacterShadows_CharacterShadowManager_OnLightChanged(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(CharacterShadows.CharacterShadowManager.OnLightChanged), L, pos);
	}

	void Push_CharacterShadows_CharacterShadowManager_OnLightChanged(IntPtr L, CharacterShadows.CharacterShadowManager.OnLightChanged o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_UnityEngine_Rendering_CommandBuffer_UnityEngine_Renderer_Event : LuaDelegate
	{
		public System_Action_UnityEngine_Rendering_CommandBuffer_UnityEngine_Renderer_Event(LuaFunction func) : base(func) { }
		public System_Action_UnityEngine_Rendering_CommandBuffer_UnityEngine_Renderer_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.Rendering.CommandBuffer param0, UnityEngine.Renderer param1)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.Rendering.CommandBuffer param0, UnityEngine.Renderer param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<UnityEngine.Rendering.CommandBuffer,UnityEngine.Renderer> System_Action_UnityEngine_Rendering_CommandBuffer_UnityEngine_Renderer(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<UnityEngine.Rendering.CommandBuffer,UnityEngine.Renderer> fn = delegate(UnityEngine.Rendering.CommandBuffer param0, UnityEngine.Renderer param1) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_UnityEngine_Rendering_CommandBuffer_UnityEngine_Renderer_Event target = new System_Action_UnityEngine_Rendering_CommandBuffer_UnityEngine_Renderer_Event(func);
			System.Action<UnityEngine.Rendering.CommandBuffer,UnityEngine.Renderer> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_UnityEngine_Rendering_CommandBuffer_UnityEngine_Renderer_Event target = new System_Action_UnityEngine_Rendering_CommandBuffer_UnityEngine_Renderer_Event(func, self);
			System.Action<UnityEngine.Rendering.CommandBuffer,UnityEngine.Renderer> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_UnityEngine_Rendering_CommandBuffer_UnityEngine_Renderer(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<UnityEngine.Rendering.CommandBuffer,UnityEngine.Renderer>), L, pos);
	}

	void Push_System_Action_UnityEngine_Rendering_CommandBuffer_UnityEngine_Renderer(IntPtr L, System.Action<UnityEngine.Rendering.CommandBuffer,UnityEngine.Renderer> o)
	{
		ToLua.Push(L, o);
	}

	class FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellRefreshDelegate_Event : LuaDelegate
	{
		public FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellRefreshDelegate_Event(LuaFunction func) : base(func) { }
		public FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellRefreshDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(FancyScrollView.FancyCell<FancyScrollView.NullContext> param0, int param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(FancyScrollView.FancyCell<FancyScrollView.NullContext> param0, int param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public FancyScrollView.FancyScrollView<FancyScrollView.NullContext>.CellRefreshDelegate FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellRefreshDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			FancyScrollView.FancyScrollView<FancyScrollView.NullContext>.CellRefreshDelegate fn = delegate(FancyScrollView.FancyCell<FancyScrollView.NullContext> param0, int param1) { };
			return fn;
		}

		if(!flag)
		{
			FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellRefreshDelegate_Event target = new FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellRefreshDelegate_Event(func);
			FancyScrollView.FancyScrollView<FancyScrollView.NullContext>.CellRefreshDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellRefreshDelegate_Event target = new FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellRefreshDelegate_Event(func, self);
			FancyScrollView.FancyScrollView<FancyScrollView.NullContext>.CellRefreshDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellRefreshDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(FancyScrollView.FancyScrollView<FancyScrollView.NullContext>.CellRefreshDelegate), L, pos);
	}

	void Push_FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellRefreshDelegate(IntPtr L, FancyScrollView.FancyScrollView<FancyScrollView.NullContext>.CellRefreshDelegate o)
	{
		ToLua.Push(L, o);
	}

	class FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellSelectDelegate_Event : LuaDelegate
	{
		public FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellSelectDelegate_Event(LuaFunction func) : base(func) { }
		public FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellSelectDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(int param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(int param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public FancyScrollView.FancyScrollView<FancyScrollView.NullContext>.CellSelectDelegate FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellSelectDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			FancyScrollView.FancyScrollView<FancyScrollView.NullContext>.CellSelectDelegate fn = delegate(int param0) { };
			return fn;
		}

		if(!flag)
		{
			FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellSelectDelegate_Event target = new FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellSelectDelegate_Event(func);
			FancyScrollView.FancyScrollView<FancyScrollView.NullContext>.CellSelectDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellSelectDelegate_Event target = new FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellSelectDelegate_Event(func, self);
			FancyScrollView.FancyScrollView<FancyScrollView.NullContext>.CellSelectDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellSelectDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(FancyScrollView.FancyScrollView<FancyScrollView.NullContext>.CellSelectDelegate), L, pos);
	}

	void Push_FancyScrollView_FancyScrollView_FancyScrollView_NullContext_CellSelectDelegate(IntPtr L, FancyScrollView.FancyScrollView<FancyScrollView.NullContext>.CellSelectDelegate o)
	{
		ToLua.Push(L, o);
	}

	class FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshCellDelegate_Event : LuaDelegate
	{
		public FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshCellDelegate_Event(LuaFunction func) : base(func) { }
		public FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshCellDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call()
		{
			func.Call();
		}

		public void CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			func.EndPCall();
		}
	}

	public FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshCellDelegate FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshCellDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshCellDelegate fn = delegate() { };
			return fn;
		}

		if(!flag)
		{
			FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshCellDelegate_Event target = new FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshCellDelegate_Event(func);
			FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshCellDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshCellDelegate_Event target = new FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshCellDelegate_Event(func, self);
			FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshCellDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshCellDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshCellDelegate), L, pos);
	}

	void Push_FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshCellDelegate(IntPtr L, FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshCellDelegate o)
	{
		ToLua.Push(L, o);
	}

	class FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshPosDelegate_Event : LuaDelegate
	{
		public FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshPosDelegate_Event(LuaFunction func) : base(func) { }
		public FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshPosDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(float param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(float param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshPosDelegate FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshPosDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshPosDelegate fn = delegate(float param0) { };
			return fn;
		}

		if(!flag)
		{
			FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshPosDelegate_Event target = new FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshPosDelegate_Event(func);
			FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshPosDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshPosDelegate_Event target = new FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshPosDelegate_Event(func, self);
			FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshPosDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshPosDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshPosDelegate), L, pos);
	}

	void Push_FancyScrollView_FancyCell_FancyScrollView_NullContext_RefreshPosDelegate(IntPtr L, FancyScrollView.FancyCell<FancyScrollView.NullContext>.RefreshPosDelegate o)
	{
		ToLua.Push(L, o);
	}

	class EasingCore_EasingFunction_Event : LuaDelegate
	{
		public EasingCore_EasingFunction_Event(LuaFunction func) : base(func) { }
		public EasingCore_EasingFunction_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public float Call(float param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			float ret = (float)func.CheckNumber();
			func.EndPCall();
			return ret;
		}

		public float CallWithSelf(float param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			float ret = (float)func.CheckNumber();
			func.EndPCall();
			return ret;
		}
	}

	public EasingCore.EasingFunction EasingCore_EasingFunction(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			EasingCore.EasingFunction fn = delegate(float param0) { return 0; };
			return fn;
		}

		if(!flag)
		{
			EasingCore_EasingFunction_Event target = new EasingCore_EasingFunction_Event(func);
			EasingCore.EasingFunction d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			EasingCore_EasingFunction_Event target = new EasingCore_EasingFunction_Event(func, self);
			EasingCore.EasingFunction d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_EasingCore_EasingFunction(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(EasingCore.EasingFunction), L, pos);
	}

	void Push_EasingCore_EasingFunction(IntPtr L, EasingCore.EasingFunction o)
	{
		ToLua.Push(L, o);
	}

	class FancyScrollView_FancyScrollView_FancyScrollView_Context_CellRefreshDelegate_Event : LuaDelegate
	{
		public FancyScrollView_FancyScrollView_FancyScrollView_Context_CellRefreshDelegate_Event(LuaFunction func) : base(func) { }
		public FancyScrollView_FancyScrollView_FancyScrollView_Context_CellRefreshDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(FancyScrollView.FancyCell<FancyScrollView.Context> param0, int param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(FancyScrollView.FancyCell<FancyScrollView.Context> param0, int param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellRefreshDelegate FancyScrollView_FancyScrollView_FancyScrollView_Context_CellRefreshDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellRefreshDelegate fn = delegate(FancyScrollView.FancyCell<FancyScrollView.Context> param0, int param1) { };
			return fn;
		}

		if(!flag)
		{
			FancyScrollView_FancyScrollView_FancyScrollView_Context_CellRefreshDelegate_Event target = new FancyScrollView_FancyScrollView_FancyScrollView_Context_CellRefreshDelegate_Event(func);
			FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellRefreshDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			FancyScrollView_FancyScrollView_FancyScrollView_Context_CellRefreshDelegate_Event target = new FancyScrollView_FancyScrollView_FancyScrollView_Context_CellRefreshDelegate_Event(func, self);
			FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellRefreshDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_FancyScrollView_FancyScrollView_FancyScrollView_Context_CellRefreshDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellRefreshDelegate), L, pos);
	}

	void Push_FancyScrollView_FancyScrollView_FancyScrollView_Context_CellRefreshDelegate(IntPtr L, FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellRefreshDelegate o)
	{
		ToLua.Push(L, o);
	}

	class FancyScrollView_FancyScrollView_FancyScrollView_Context_CellSelectDelegate_Event : LuaDelegate
	{
		public FancyScrollView_FancyScrollView_FancyScrollView_Context_CellSelectDelegate_Event(LuaFunction func) : base(func) { }
		public FancyScrollView_FancyScrollView_FancyScrollView_Context_CellSelectDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(int param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(int param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellSelectDelegate FancyScrollView_FancyScrollView_FancyScrollView_Context_CellSelectDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellSelectDelegate fn = delegate(int param0) { };
			return fn;
		}

		if(!flag)
		{
			FancyScrollView_FancyScrollView_FancyScrollView_Context_CellSelectDelegate_Event target = new FancyScrollView_FancyScrollView_FancyScrollView_Context_CellSelectDelegate_Event(func);
			FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellSelectDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			FancyScrollView_FancyScrollView_FancyScrollView_Context_CellSelectDelegate_Event target = new FancyScrollView_FancyScrollView_FancyScrollView_Context_CellSelectDelegate_Event(func, self);
			FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellSelectDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_FancyScrollView_FancyScrollView_FancyScrollView_Context_CellSelectDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellSelectDelegate), L, pos);
	}

	void Push_FancyScrollView_FancyScrollView_FancyScrollView_Context_CellSelectDelegate(IntPtr L, FancyScrollView.FancyScrollView<FancyScrollView.Context>.CellSelectDelegate o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_int_FancyScrollView_MovementDirection_Event : LuaDelegate
	{
		public System_Action_int_FancyScrollView_MovementDirection_Event(LuaFunction func) : base(func) { }
		public System_Action_int_FancyScrollView_MovementDirection_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(int param0, FancyScrollView.MovementDirection param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(int param0, FancyScrollView.MovementDirection param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<int,FancyScrollView.MovementDirection> System_Action_int_FancyScrollView_MovementDirection(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<int,FancyScrollView.MovementDirection> fn = delegate(int param0, FancyScrollView.MovementDirection param1) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_int_FancyScrollView_MovementDirection_Event target = new System_Action_int_FancyScrollView_MovementDirection_Event(func);
			System.Action<int,FancyScrollView.MovementDirection> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_int_FancyScrollView_MovementDirection_Event target = new System_Action_int_FancyScrollView_MovementDirection_Event(func, self);
			System.Action<int,FancyScrollView.MovementDirection> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_int_FancyScrollView_MovementDirection(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<int,FancyScrollView.MovementDirection>), L, pos);
	}

	void Push_System_Action_int_FancyScrollView_MovementDirection(IntPtr L, System.Action<int,FancyScrollView.MovementDirection> o)
	{
		ToLua.Push(L, o);
	}

	class FancyScrollView_FancyCell_FancyScrollView_Context_RefreshCellDelegate_Event : LuaDelegate
	{
		public FancyScrollView_FancyCell_FancyScrollView_Context_RefreshCellDelegate_Event(LuaFunction func) : base(func) { }
		public FancyScrollView_FancyCell_FancyScrollView_Context_RefreshCellDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call()
		{
			func.Call();
		}

		public void CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			func.EndPCall();
		}
	}

	public FancyScrollView.FancyCell<FancyScrollView.Context>.RefreshCellDelegate FancyScrollView_FancyCell_FancyScrollView_Context_RefreshCellDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			FancyScrollView.FancyCell<FancyScrollView.Context>.RefreshCellDelegate fn = delegate() { };
			return fn;
		}

		if(!flag)
		{
			FancyScrollView_FancyCell_FancyScrollView_Context_RefreshCellDelegate_Event target = new FancyScrollView_FancyCell_FancyScrollView_Context_RefreshCellDelegate_Event(func);
			FancyScrollView.FancyCell<FancyScrollView.Context>.RefreshCellDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			FancyScrollView_FancyCell_FancyScrollView_Context_RefreshCellDelegate_Event target = new FancyScrollView_FancyCell_FancyScrollView_Context_RefreshCellDelegate_Event(func, self);
			FancyScrollView.FancyCell<FancyScrollView.Context>.RefreshCellDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_FancyScrollView_FancyCell_FancyScrollView_Context_RefreshCellDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(FancyScrollView.FancyCell<FancyScrollView.Context>.RefreshCellDelegate), L, pos);
	}

	void Push_FancyScrollView_FancyCell_FancyScrollView_Context_RefreshCellDelegate(IntPtr L, FancyScrollView.FancyCell<FancyScrollView.Context>.RefreshCellDelegate o)
	{
		ToLua.Push(L, o);
	}

	class FancyScrollView_FancyCell_FancyScrollView_Context_RefreshPosDelegate_Event : LuaDelegate
	{
		public FancyScrollView_FancyCell_FancyScrollView_Context_RefreshPosDelegate_Event(LuaFunction func) : base(func) { }
		public FancyScrollView_FancyCell_FancyScrollView_Context_RefreshPosDelegate_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(float param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(float param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public FancyScrollView.FancyCell<FancyScrollView.Context>.RefreshPosDelegate FancyScrollView_FancyCell_FancyScrollView_Context_RefreshPosDelegate(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			FancyScrollView.FancyCell<FancyScrollView.Context>.RefreshPosDelegate fn = delegate(float param0) { };
			return fn;
		}

		if(!flag)
		{
			FancyScrollView_FancyCell_FancyScrollView_Context_RefreshPosDelegate_Event target = new FancyScrollView_FancyCell_FancyScrollView_Context_RefreshPosDelegate_Event(func);
			FancyScrollView.FancyCell<FancyScrollView.Context>.RefreshPosDelegate d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			FancyScrollView_FancyCell_FancyScrollView_Context_RefreshPosDelegate_Event target = new FancyScrollView_FancyCell_FancyScrollView_Context_RefreshPosDelegate_Event(func, self);
			FancyScrollView.FancyCell<FancyScrollView.Context>.RefreshPosDelegate d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_FancyScrollView_FancyCell_FancyScrollView_Context_RefreshPosDelegate(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(FancyScrollView.FancyCell<FancyScrollView.Context>.RefreshPosDelegate), L, pos);
	}

	void Push_FancyScrollView_FancyCell_FancyScrollView_Context_RefreshPosDelegate(IntPtr L, FancyScrollView.FancyCell<FancyScrollView.Context>.RefreshPosDelegate o)
	{
		ToLua.Push(L, o);
	}

	class System_Func_UnityEngine_LogType_object_bool_Event : LuaDelegate
	{
		public System_Func_UnityEngine_LogType_object_bool_Event(LuaFunction func) : base(func) { }
		public System_Func_UnityEngine_LogType_object_bool_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public bool Call(UnityEngine.LogType param0, object param1)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			bool ret = func.CheckBoolean();
			func.EndPCall();
			return ret;
		}

		public bool CallWithSelf(UnityEngine.LogType param0, object param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.PCall();
			bool ret = func.CheckBoolean();
			func.EndPCall();
			return ret;
		}
	}

	public System.Func<UnityEngine.LogType,object,bool> System_Func_UnityEngine_LogType_object_bool(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Func<UnityEngine.LogType,object,bool> fn = delegate(UnityEngine.LogType param0, object param1) { return false; };
			return fn;
		}

		if(!flag)
		{
			System_Func_UnityEngine_LogType_object_bool_Event target = new System_Func_UnityEngine_LogType_object_bool_Event(func);
			System.Func<UnityEngine.LogType,object,bool> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Func_UnityEngine_LogType_object_bool_Event target = new System_Func_UnityEngine_LogType_object_bool_Event(func, self);
			System.Func<UnityEngine.LogType,object,bool> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Func_UnityEngine_LogType_object_bool(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Func<UnityEngine.LogType,object,bool>), L, pos);
	}

	void Push_System_Func_UnityEngine_LogType_object_bool(IntPtr L, System.Func<UnityEngine.LogType,object,bool> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOGetter_float_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOGetter_float_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOGetter_float_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public float Call()
		{
			func.BeginPCall();
			func.PCall();
			float ret = (float)func.CheckNumber();
			func.EndPCall();
			return ret;
		}

		public float CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			float ret = (float)func.CheckNumber();
			func.EndPCall();
			return ret;
		}
	}

	public DG.Tweening.Core.DOGetter<float> DG_Tweening_Core_DOGetter_float(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOGetter<float> fn = delegate() { return 0; };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOGetter_float_Event target = new DG_Tweening_Core_DOGetter_float_Event(func);
			DG.Tweening.Core.DOGetter<float> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOGetter_float_Event target = new DG_Tweening_Core_DOGetter_float_Event(func, self);
			DG.Tweening.Core.DOGetter<float> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOGetter_float(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOGetter<float>), L, pos);
	}

	void Push_DG_Tweening_Core_DOGetter_float(IntPtr L, DG.Tweening.Core.DOGetter<float> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOSetter_float_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOSetter_float_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOSetter_float_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(float param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(float param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public DG.Tweening.Core.DOSetter<float> DG_Tweening_Core_DOSetter_float(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOSetter<float> fn = delegate(float param0) { };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOSetter_float_Event target = new DG_Tweening_Core_DOSetter_float_Event(func);
			DG.Tweening.Core.DOSetter<float> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOSetter_float_Event target = new DG_Tweening_Core_DOSetter_float_Event(func, self);
			DG.Tweening.Core.DOSetter<float> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOSetter_float(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOSetter<float>), L, pos);
	}

	void Push_DG_Tweening_Core_DOSetter_float(IntPtr L, DG.Tweening.Core.DOSetter<float> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOGetter_double_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOGetter_double_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOGetter_double_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public double Call()
		{
			func.BeginPCall();
			func.PCall();
			double ret = (double)func.CheckNumber();
			func.EndPCall();
			return ret;
		}

		public double CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			double ret = (double)func.CheckNumber();
			func.EndPCall();
			return ret;
		}
	}

	public DG.Tweening.Core.DOGetter<double> DG_Tweening_Core_DOGetter_double(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOGetter<double> fn = delegate() { return 0; };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOGetter_double_Event target = new DG_Tweening_Core_DOGetter_double_Event(func);
			DG.Tweening.Core.DOGetter<double> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOGetter_double_Event target = new DG_Tweening_Core_DOGetter_double_Event(func, self);
			DG.Tweening.Core.DOGetter<double> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOGetter_double(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOGetter<double>), L, pos);
	}

	void Push_DG_Tweening_Core_DOGetter_double(IntPtr L, DG.Tweening.Core.DOGetter<double> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOSetter_double_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOSetter_double_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOSetter_double_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(double param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(double param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public DG.Tweening.Core.DOSetter<double> DG_Tweening_Core_DOSetter_double(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOSetter<double> fn = delegate(double param0) { };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOSetter_double_Event target = new DG_Tweening_Core_DOSetter_double_Event(func);
			DG.Tweening.Core.DOSetter<double> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOSetter_double_Event target = new DG_Tweening_Core_DOSetter_double_Event(func, self);
			DG.Tweening.Core.DOSetter<double> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOSetter_double(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOSetter<double>), L, pos);
	}

	void Push_DG_Tweening_Core_DOSetter_double(IntPtr L, DG.Tweening.Core.DOSetter<double> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOGetter_int_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOGetter_int_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOGetter_int_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public int Call()
		{
			func.BeginPCall();
			func.PCall();
			int ret = (int)func.CheckNumber();
			func.EndPCall();
			return ret;
		}

		public int CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			int ret = (int)func.CheckNumber();
			func.EndPCall();
			return ret;
		}
	}

	public DG.Tweening.Core.DOGetter<int> DG_Tweening_Core_DOGetter_int(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOGetter<int> fn = delegate() { return 0; };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOGetter_int_Event target = new DG_Tweening_Core_DOGetter_int_Event(func);
			DG.Tweening.Core.DOGetter<int> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOGetter_int_Event target = new DG_Tweening_Core_DOGetter_int_Event(func, self);
			DG.Tweening.Core.DOGetter<int> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOGetter_int(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOGetter<int>), L, pos);
	}

	void Push_DG_Tweening_Core_DOGetter_int(IntPtr L, DG.Tweening.Core.DOGetter<int> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOSetter_int_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOSetter_int_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOSetter_int_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(int param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(int param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public DG.Tweening.Core.DOSetter<int> DG_Tweening_Core_DOSetter_int(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOSetter<int> fn = delegate(int param0) { };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOSetter_int_Event target = new DG_Tweening_Core_DOSetter_int_Event(func);
			DG.Tweening.Core.DOSetter<int> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOSetter_int_Event target = new DG_Tweening_Core_DOSetter_int_Event(func, self);
			DG.Tweening.Core.DOSetter<int> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOSetter_int(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOSetter<int>), L, pos);
	}

	void Push_DG_Tweening_Core_DOSetter_int(IntPtr L, DG.Tweening.Core.DOSetter<int> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOGetter_uint_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOGetter_uint_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOGetter_uint_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public uint Call()
		{
			func.BeginPCall();
			func.PCall();
			uint ret = (uint)func.CheckNumber();
			func.EndPCall();
			return ret;
		}

		public uint CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			uint ret = (uint)func.CheckNumber();
			func.EndPCall();
			return ret;
		}
	}

	public DG.Tweening.Core.DOGetter<uint> DG_Tweening_Core_DOGetter_uint(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOGetter<uint> fn = delegate() { return 0; };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOGetter_uint_Event target = new DG_Tweening_Core_DOGetter_uint_Event(func);
			DG.Tweening.Core.DOGetter<uint> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOGetter_uint_Event target = new DG_Tweening_Core_DOGetter_uint_Event(func, self);
			DG.Tweening.Core.DOGetter<uint> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOGetter_uint(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOGetter<uint>), L, pos);
	}

	void Push_DG_Tweening_Core_DOGetter_uint(IntPtr L, DG.Tweening.Core.DOGetter<uint> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOSetter_uint_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOSetter_uint_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOSetter_uint_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(uint param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(uint param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public DG.Tweening.Core.DOSetter<uint> DG_Tweening_Core_DOSetter_uint(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOSetter<uint> fn = delegate(uint param0) { };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOSetter_uint_Event target = new DG_Tweening_Core_DOSetter_uint_Event(func);
			DG.Tweening.Core.DOSetter<uint> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOSetter_uint_Event target = new DG_Tweening_Core_DOSetter_uint_Event(func, self);
			DG.Tweening.Core.DOSetter<uint> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOSetter_uint(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOSetter<uint>), L, pos);
	}

	void Push_DG_Tweening_Core_DOSetter_uint(IntPtr L, DG.Tweening.Core.DOSetter<uint> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOGetter_long_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOGetter_long_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOGetter_long_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public long Call()
		{
			func.BeginPCall();
			func.PCall();
			long ret = func.CheckLong();
			func.EndPCall();
			return ret;
		}

		public long CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			long ret = func.CheckLong();
			func.EndPCall();
			return ret;
		}
	}

	public DG.Tweening.Core.DOGetter<long> DG_Tweening_Core_DOGetter_long(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOGetter<long> fn = delegate() { return 0; };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOGetter_long_Event target = new DG_Tweening_Core_DOGetter_long_Event(func);
			DG.Tweening.Core.DOGetter<long> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOGetter_long_Event target = new DG_Tweening_Core_DOGetter_long_Event(func, self);
			DG.Tweening.Core.DOGetter<long> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOGetter_long(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOGetter<long>), L, pos);
	}

	void Push_DG_Tweening_Core_DOGetter_long(IntPtr L, DG.Tweening.Core.DOGetter<long> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOSetter_long_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOSetter_long_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOSetter_long_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(long param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(long param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public DG.Tweening.Core.DOSetter<long> DG_Tweening_Core_DOSetter_long(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOSetter<long> fn = delegate(long param0) { };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOSetter_long_Event target = new DG_Tweening_Core_DOSetter_long_Event(func);
			DG.Tweening.Core.DOSetter<long> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOSetter_long_Event target = new DG_Tweening_Core_DOSetter_long_Event(func, self);
			DG.Tweening.Core.DOSetter<long> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOSetter_long(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOSetter<long>), L, pos);
	}

	void Push_DG_Tweening_Core_DOSetter_long(IntPtr L, DG.Tweening.Core.DOSetter<long> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOGetter_ulong_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOGetter_ulong_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOGetter_ulong_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public ulong Call()
		{
			func.BeginPCall();
			func.PCall();
			ulong ret = func.CheckULong();
			func.EndPCall();
			return ret;
		}

		public ulong CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			ulong ret = func.CheckULong();
			func.EndPCall();
			return ret;
		}
	}

	public DG.Tweening.Core.DOGetter<ulong> DG_Tweening_Core_DOGetter_ulong(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOGetter<ulong> fn = delegate() { return 0; };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOGetter_ulong_Event target = new DG_Tweening_Core_DOGetter_ulong_Event(func);
			DG.Tweening.Core.DOGetter<ulong> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOGetter_ulong_Event target = new DG_Tweening_Core_DOGetter_ulong_Event(func, self);
			DG.Tweening.Core.DOGetter<ulong> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOGetter_ulong(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOGetter<ulong>), L, pos);
	}

	void Push_DG_Tweening_Core_DOGetter_ulong(IntPtr L, DG.Tweening.Core.DOGetter<ulong> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOSetter_ulong_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOSetter_ulong_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOSetter_ulong_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(ulong param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(ulong param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public DG.Tweening.Core.DOSetter<ulong> DG_Tweening_Core_DOSetter_ulong(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOSetter<ulong> fn = delegate(ulong param0) { };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOSetter_ulong_Event target = new DG_Tweening_Core_DOSetter_ulong_Event(func);
			DG.Tweening.Core.DOSetter<ulong> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOSetter_ulong_Event target = new DG_Tweening_Core_DOSetter_ulong_Event(func, self);
			DG.Tweening.Core.DOSetter<ulong> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOSetter_ulong(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOSetter<ulong>), L, pos);
	}

	void Push_DG_Tweening_Core_DOSetter_ulong(IntPtr L, DG.Tweening.Core.DOSetter<ulong> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOGetter_string_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOGetter_string_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOGetter_string_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public string Call()
		{
			func.BeginPCall();
			func.PCall();
			string ret = func.CheckString();
			func.EndPCall();
			return ret;
		}

		public string CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			string ret = func.CheckString();
			func.EndPCall();
			return ret;
		}
	}

	public DG.Tweening.Core.DOGetter<string> DG_Tweening_Core_DOGetter_string(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOGetter<string> fn = delegate() { return null; };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOGetter_string_Event target = new DG_Tweening_Core_DOGetter_string_Event(func);
			DG.Tweening.Core.DOGetter<string> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOGetter_string_Event target = new DG_Tweening_Core_DOGetter_string_Event(func, self);
			DG.Tweening.Core.DOGetter<string> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOGetter_string(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOGetter<string>), L, pos);
	}

	void Push_DG_Tweening_Core_DOGetter_string(IntPtr L, DG.Tweening.Core.DOGetter<string> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOSetter_string_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOSetter_string_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOSetter_string_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(string param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(string param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public DG.Tweening.Core.DOSetter<string> DG_Tweening_Core_DOSetter_string(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOSetter<string> fn = delegate(string param0) { };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOSetter_string_Event target = new DG_Tweening_Core_DOSetter_string_Event(func);
			DG.Tweening.Core.DOSetter<string> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOSetter_string_Event target = new DG_Tweening_Core_DOSetter_string_Event(func, self);
			DG.Tweening.Core.DOSetter<string> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOSetter_string(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOSetter<string>), L, pos);
	}

	void Push_DG_Tweening_Core_DOSetter_string(IntPtr L, DG.Tweening.Core.DOSetter<string> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOGetter_UnityEngine_Vector2_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOGetter_UnityEngine_Vector2_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOGetter_UnityEngine_Vector2_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public UnityEngine.Vector2 Call()
		{
			func.BeginPCall();
			func.PCall();
			UnityEngine.Vector2 ret = func.CheckVector2();
			func.EndPCall();
			return ret;
		}

		public UnityEngine.Vector2 CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			UnityEngine.Vector2 ret = func.CheckVector2();
			func.EndPCall();
			return ret;
		}
	}

	public DG.Tweening.Core.DOGetter<UnityEngine.Vector2> DG_Tweening_Core_DOGetter_UnityEngine_Vector2(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOGetter<UnityEngine.Vector2> fn = delegate() { return default(UnityEngine.Vector2); };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOGetter_UnityEngine_Vector2_Event target = new DG_Tweening_Core_DOGetter_UnityEngine_Vector2_Event(func);
			DG.Tweening.Core.DOGetter<UnityEngine.Vector2> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOGetter_UnityEngine_Vector2_Event target = new DG_Tweening_Core_DOGetter_UnityEngine_Vector2_Event(func, self);
			DG.Tweening.Core.DOGetter<UnityEngine.Vector2> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOGetter_UnityEngine_Vector2(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOGetter<UnityEngine.Vector2>), L, pos);
	}

	void Push_DG_Tweening_Core_DOGetter_UnityEngine_Vector2(IntPtr L, DG.Tweening.Core.DOGetter<UnityEngine.Vector2> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOSetter_UnityEngine_Vector2_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOSetter_UnityEngine_Vector2_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOSetter_UnityEngine_Vector2_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.Vector2 param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.Vector2 param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public DG.Tweening.Core.DOSetter<UnityEngine.Vector2> DG_Tweening_Core_DOSetter_UnityEngine_Vector2(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOSetter<UnityEngine.Vector2> fn = delegate(UnityEngine.Vector2 param0) { };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOSetter_UnityEngine_Vector2_Event target = new DG_Tweening_Core_DOSetter_UnityEngine_Vector2_Event(func);
			DG.Tweening.Core.DOSetter<UnityEngine.Vector2> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOSetter_UnityEngine_Vector2_Event target = new DG_Tweening_Core_DOSetter_UnityEngine_Vector2_Event(func, self);
			DG.Tweening.Core.DOSetter<UnityEngine.Vector2> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOSetter_UnityEngine_Vector2(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOSetter<UnityEngine.Vector2>), L, pos);
	}

	void Push_DG_Tweening_Core_DOSetter_UnityEngine_Vector2(IntPtr L, DG.Tweening.Core.DOSetter<UnityEngine.Vector2> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOGetter_UnityEngine_Vector3_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOGetter_UnityEngine_Vector3_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOGetter_UnityEngine_Vector3_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public UnityEngine.Vector3 Call()
		{
			func.BeginPCall();
			func.PCall();
			UnityEngine.Vector3 ret = func.CheckVector3();
			func.EndPCall();
			return ret;
		}

		public UnityEngine.Vector3 CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			UnityEngine.Vector3 ret = func.CheckVector3();
			func.EndPCall();
			return ret;
		}
	}

	public DG.Tweening.Core.DOGetter<UnityEngine.Vector3> DG_Tweening_Core_DOGetter_UnityEngine_Vector3(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOGetter<UnityEngine.Vector3> fn = delegate() { return default(UnityEngine.Vector3); };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOGetter_UnityEngine_Vector3_Event target = new DG_Tweening_Core_DOGetter_UnityEngine_Vector3_Event(func);
			DG.Tweening.Core.DOGetter<UnityEngine.Vector3> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOGetter_UnityEngine_Vector3_Event target = new DG_Tweening_Core_DOGetter_UnityEngine_Vector3_Event(func, self);
			DG.Tweening.Core.DOGetter<UnityEngine.Vector3> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOGetter_UnityEngine_Vector3(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOGetter<UnityEngine.Vector3>), L, pos);
	}

	void Push_DG_Tweening_Core_DOGetter_UnityEngine_Vector3(IntPtr L, DG.Tweening.Core.DOGetter<UnityEngine.Vector3> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOSetter_UnityEngine_Vector3_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOSetter_UnityEngine_Vector3_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOSetter_UnityEngine_Vector3_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.Vector3 param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.Vector3 param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public DG.Tweening.Core.DOSetter<UnityEngine.Vector3> DG_Tweening_Core_DOSetter_UnityEngine_Vector3(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOSetter<UnityEngine.Vector3> fn = delegate(UnityEngine.Vector3 param0) { };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOSetter_UnityEngine_Vector3_Event target = new DG_Tweening_Core_DOSetter_UnityEngine_Vector3_Event(func);
			DG.Tweening.Core.DOSetter<UnityEngine.Vector3> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOSetter_UnityEngine_Vector3_Event target = new DG_Tweening_Core_DOSetter_UnityEngine_Vector3_Event(func, self);
			DG.Tweening.Core.DOSetter<UnityEngine.Vector3> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOSetter_UnityEngine_Vector3(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOSetter<UnityEngine.Vector3>), L, pos);
	}

	void Push_DG_Tweening_Core_DOSetter_UnityEngine_Vector3(IntPtr L, DG.Tweening.Core.DOSetter<UnityEngine.Vector3> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOGetter_UnityEngine_Vector4_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOGetter_UnityEngine_Vector4_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOGetter_UnityEngine_Vector4_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public UnityEngine.Vector4 Call()
		{
			func.BeginPCall();
			func.PCall();
			UnityEngine.Vector4 ret = func.CheckVector4();
			func.EndPCall();
			return ret;
		}

		public UnityEngine.Vector4 CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			UnityEngine.Vector4 ret = func.CheckVector4();
			func.EndPCall();
			return ret;
		}
	}

	public DG.Tweening.Core.DOGetter<UnityEngine.Vector4> DG_Tweening_Core_DOGetter_UnityEngine_Vector4(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOGetter<UnityEngine.Vector4> fn = delegate() { return default(UnityEngine.Vector4); };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOGetter_UnityEngine_Vector4_Event target = new DG_Tweening_Core_DOGetter_UnityEngine_Vector4_Event(func);
			DG.Tweening.Core.DOGetter<UnityEngine.Vector4> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOGetter_UnityEngine_Vector4_Event target = new DG_Tweening_Core_DOGetter_UnityEngine_Vector4_Event(func, self);
			DG.Tweening.Core.DOGetter<UnityEngine.Vector4> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOGetter_UnityEngine_Vector4(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOGetter<UnityEngine.Vector4>), L, pos);
	}

	void Push_DG_Tweening_Core_DOGetter_UnityEngine_Vector4(IntPtr L, DG.Tweening.Core.DOGetter<UnityEngine.Vector4> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOSetter_UnityEngine_Vector4_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOSetter_UnityEngine_Vector4_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOSetter_UnityEngine_Vector4_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.Vector4 param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.Vector4 param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public DG.Tweening.Core.DOSetter<UnityEngine.Vector4> DG_Tweening_Core_DOSetter_UnityEngine_Vector4(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOSetter<UnityEngine.Vector4> fn = delegate(UnityEngine.Vector4 param0) { };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOSetter_UnityEngine_Vector4_Event target = new DG_Tweening_Core_DOSetter_UnityEngine_Vector4_Event(func);
			DG.Tweening.Core.DOSetter<UnityEngine.Vector4> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOSetter_UnityEngine_Vector4_Event target = new DG_Tweening_Core_DOSetter_UnityEngine_Vector4_Event(func, self);
			DG.Tweening.Core.DOSetter<UnityEngine.Vector4> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOSetter_UnityEngine_Vector4(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOSetter<UnityEngine.Vector4>), L, pos);
	}

	void Push_DG_Tweening_Core_DOSetter_UnityEngine_Vector4(IntPtr L, DG.Tweening.Core.DOSetter<UnityEngine.Vector4> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOGetter_UnityEngine_Quaternion_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOGetter_UnityEngine_Quaternion_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOGetter_UnityEngine_Quaternion_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public UnityEngine.Quaternion Call()
		{
			func.BeginPCall();
			func.PCall();
			UnityEngine.Quaternion ret = func.CheckQuaternion();
			func.EndPCall();
			return ret;
		}

		public UnityEngine.Quaternion CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			UnityEngine.Quaternion ret = func.CheckQuaternion();
			func.EndPCall();
			return ret;
		}
	}

	public DG.Tweening.Core.DOGetter<UnityEngine.Quaternion> DG_Tweening_Core_DOGetter_UnityEngine_Quaternion(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOGetter<UnityEngine.Quaternion> fn = delegate() { return default(UnityEngine.Quaternion); };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOGetter_UnityEngine_Quaternion_Event target = new DG_Tweening_Core_DOGetter_UnityEngine_Quaternion_Event(func);
			DG.Tweening.Core.DOGetter<UnityEngine.Quaternion> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOGetter_UnityEngine_Quaternion_Event target = new DG_Tweening_Core_DOGetter_UnityEngine_Quaternion_Event(func, self);
			DG.Tweening.Core.DOGetter<UnityEngine.Quaternion> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOGetter_UnityEngine_Quaternion(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOGetter<UnityEngine.Quaternion>), L, pos);
	}

	void Push_DG_Tweening_Core_DOGetter_UnityEngine_Quaternion(IntPtr L, DG.Tweening.Core.DOGetter<UnityEngine.Quaternion> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOSetter_UnityEngine_Quaternion_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOSetter_UnityEngine_Quaternion_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOSetter_UnityEngine_Quaternion_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.Quaternion param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.Quaternion param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public DG.Tweening.Core.DOSetter<UnityEngine.Quaternion> DG_Tweening_Core_DOSetter_UnityEngine_Quaternion(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOSetter<UnityEngine.Quaternion> fn = delegate(UnityEngine.Quaternion param0) { };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOSetter_UnityEngine_Quaternion_Event target = new DG_Tweening_Core_DOSetter_UnityEngine_Quaternion_Event(func);
			DG.Tweening.Core.DOSetter<UnityEngine.Quaternion> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOSetter_UnityEngine_Quaternion_Event target = new DG_Tweening_Core_DOSetter_UnityEngine_Quaternion_Event(func, self);
			DG.Tweening.Core.DOSetter<UnityEngine.Quaternion> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOSetter_UnityEngine_Quaternion(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOSetter<UnityEngine.Quaternion>), L, pos);
	}

	void Push_DG_Tweening_Core_DOSetter_UnityEngine_Quaternion(IntPtr L, DG.Tweening.Core.DOSetter<UnityEngine.Quaternion> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOGetter_UnityEngine_Color_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOGetter_UnityEngine_Color_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOGetter_UnityEngine_Color_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public UnityEngine.Color Call()
		{
			func.BeginPCall();
			func.PCall();
			UnityEngine.Color ret = func.CheckColor();
			func.EndPCall();
			return ret;
		}

		public UnityEngine.Color CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			UnityEngine.Color ret = func.CheckColor();
			func.EndPCall();
			return ret;
		}
	}

	public DG.Tweening.Core.DOGetter<UnityEngine.Color> DG_Tweening_Core_DOGetter_UnityEngine_Color(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOGetter<UnityEngine.Color> fn = delegate() { return default(UnityEngine.Color); };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOGetter_UnityEngine_Color_Event target = new DG_Tweening_Core_DOGetter_UnityEngine_Color_Event(func);
			DG.Tweening.Core.DOGetter<UnityEngine.Color> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOGetter_UnityEngine_Color_Event target = new DG_Tweening_Core_DOGetter_UnityEngine_Color_Event(func, self);
			DG.Tweening.Core.DOGetter<UnityEngine.Color> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOGetter_UnityEngine_Color(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOGetter<UnityEngine.Color>), L, pos);
	}

	void Push_DG_Tweening_Core_DOGetter_UnityEngine_Color(IntPtr L, DG.Tweening.Core.DOGetter<UnityEngine.Color> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOSetter_UnityEngine_Color_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOSetter_UnityEngine_Color_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOSetter_UnityEngine_Color_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.Color param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.Color param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public DG.Tweening.Core.DOSetter<UnityEngine.Color> DG_Tweening_Core_DOSetter_UnityEngine_Color(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOSetter<UnityEngine.Color> fn = delegate(UnityEngine.Color param0) { };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOSetter_UnityEngine_Color_Event target = new DG_Tweening_Core_DOSetter_UnityEngine_Color_Event(func);
			DG.Tweening.Core.DOSetter<UnityEngine.Color> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOSetter_UnityEngine_Color_Event target = new DG_Tweening_Core_DOSetter_UnityEngine_Color_Event(func, self);
			DG.Tweening.Core.DOSetter<UnityEngine.Color> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOSetter_UnityEngine_Color(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOSetter<UnityEngine.Color>), L, pos);
	}

	void Push_DG_Tweening_Core_DOSetter_UnityEngine_Color(IntPtr L, DG.Tweening.Core.DOSetter<UnityEngine.Color> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOGetter_UnityEngine_Rect_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOGetter_UnityEngine_Rect_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOGetter_UnityEngine_Rect_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public UnityEngine.Rect Call()
		{
			func.BeginPCall();
			func.PCall();
			UnityEngine.Rect ret = (UnityEngine.Rect)func.CheckObject(typeof(UnityEngine.Rect));
			func.EndPCall();
			return ret;
		}

		public UnityEngine.Rect CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			UnityEngine.Rect ret = (UnityEngine.Rect)func.CheckObject(typeof(UnityEngine.Rect));
			func.EndPCall();
			return ret;
		}
	}

	public DG.Tweening.Core.DOGetter<UnityEngine.Rect> DG_Tweening_Core_DOGetter_UnityEngine_Rect(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOGetter<UnityEngine.Rect> fn = delegate() { return default(UnityEngine.Rect); };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOGetter_UnityEngine_Rect_Event target = new DG_Tweening_Core_DOGetter_UnityEngine_Rect_Event(func);
			DG.Tweening.Core.DOGetter<UnityEngine.Rect> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOGetter_UnityEngine_Rect_Event target = new DG_Tweening_Core_DOGetter_UnityEngine_Rect_Event(func, self);
			DG.Tweening.Core.DOGetter<UnityEngine.Rect> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOGetter_UnityEngine_Rect(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOGetter<UnityEngine.Rect>), L, pos);
	}

	void Push_DG_Tweening_Core_DOGetter_UnityEngine_Rect(IntPtr L, DG.Tweening.Core.DOGetter<UnityEngine.Rect> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOSetter_UnityEngine_Rect_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOSetter_UnityEngine_Rect_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOSetter_UnityEngine_Rect_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.Rect param0)
		{
			func.BeginPCall();
			func.PushValue(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.Rect param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushValue(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public DG.Tweening.Core.DOSetter<UnityEngine.Rect> DG_Tweening_Core_DOSetter_UnityEngine_Rect(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOSetter<UnityEngine.Rect> fn = delegate(UnityEngine.Rect param0) { };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOSetter_UnityEngine_Rect_Event target = new DG_Tweening_Core_DOSetter_UnityEngine_Rect_Event(func);
			DG.Tweening.Core.DOSetter<UnityEngine.Rect> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOSetter_UnityEngine_Rect_Event target = new DG_Tweening_Core_DOSetter_UnityEngine_Rect_Event(func, self);
			DG.Tweening.Core.DOSetter<UnityEngine.Rect> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOSetter_UnityEngine_Rect(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOSetter<UnityEngine.Rect>), L, pos);
	}

	void Push_DG_Tweening_Core_DOSetter_UnityEngine_Rect(IntPtr L, DG.Tweening.Core.DOSetter<UnityEngine.Rect> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOGetter_UnityEngine_RectOffset_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOGetter_UnityEngine_RectOffset_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOGetter_UnityEngine_RectOffset_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public UnityEngine.RectOffset Call()
		{
			func.BeginPCall();
			func.PCall();
			UnityEngine.RectOffset ret = (UnityEngine.RectOffset)func.CheckObject(typeof(UnityEngine.RectOffset));
			func.EndPCall();
			return ret;
		}

		public UnityEngine.RectOffset CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			UnityEngine.RectOffset ret = (UnityEngine.RectOffset)func.CheckObject(typeof(UnityEngine.RectOffset));
			func.EndPCall();
			return ret;
		}
	}

	public DG.Tweening.Core.DOGetter<UnityEngine.RectOffset> DG_Tweening_Core_DOGetter_UnityEngine_RectOffset(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOGetter<UnityEngine.RectOffset> fn = delegate() { return null; };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOGetter_UnityEngine_RectOffset_Event target = new DG_Tweening_Core_DOGetter_UnityEngine_RectOffset_Event(func);
			DG.Tweening.Core.DOGetter<UnityEngine.RectOffset> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOGetter_UnityEngine_RectOffset_Event target = new DG_Tweening_Core_DOGetter_UnityEngine_RectOffset_Event(func, self);
			DG.Tweening.Core.DOGetter<UnityEngine.RectOffset> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOGetter_UnityEngine_RectOffset(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOGetter<UnityEngine.RectOffset>), L, pos);
	}

	void Push_DG_Tweening_Core_DOGetter_UnityEngine_RectOffset(IntPtr L, DG.Tweening.Core.DOGetter<UnityEngine.RectOffset> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_Core_DOSetter_UnityEngine_RectOffset_Event : LuaDelegate
	{
		public DG_Tweening_Core_DOSetter_UnityEngine_RectOffset_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_Core_DOSetter_UnityEngine_RectOffset_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.RectOffset param0)
		{
			func.BeginPCall();
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.RectOffset param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushObject(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public DG.Tweening.Core.DOSetter<UnityEngine.RectOffset> DG_Tweening_Core_DOSetter_UnityEngine_RectOffset(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.Core.DOSetter<UnityEngine.RectOffset> fn = delegate(UnityEngine.RectOffset param0) { };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_Core_DOSetter_UnityEngine_RectOffset_Event target = new DG_Tweening_Core_DOSetter_UnityEngine_RectOffset_Event(func);
			DG.Tweening.Core.DOSetter<UnityEngine.RectOffset> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_Core_DOSetter_UnityEngine_RectOffset_Event target = new DG_Tweening_Core_DOSetter_UnityEngine_RectOffset_Event(func, self);
			DG.Tweening.Core.DOSetter<UnityEngine.RectOffset> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_Core_DOSetter_UnityEngine_RectOffset(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.Core.DOSetter<UnityEngine.RectOffset>), L, pos);
	}

	void Push_DG_Tweening_Core_DOSetter_UnityEngine_RectOffset(IntPtr L, DG.Tweening.Core.DOSetter<UnityEngine.RectOffset> o)
	{
		ToLua.Push(L, o);
	}

	class DG_Tweening_TweenCallback_int_Event : LuaDelegate
	{
		public DG_Tweening_TweenCallback_int_Event(LuaFunction func) : base(func) { }
		public DG_Tweening_TweenCallback_int_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(int param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(int param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public DG.Tweening.TweenCallback<int> DG_Tweening_TweenCallback_int(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			DG.Tweening.TweenCallback<int> fn = delegate(int param0) { };
			return fn;
		}

		if(!flag)
		{
			DG_Tweening_TweenCallback_int_Event target = new DG_Tweening_TweenCallback_int_Event(func);
			DG.Tweening.TweenCallback<int> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			DG_Tweening_TweenCallback_int_Event target = new DG_Tweening_TweenCallback_int_Event(func, self);
			DG.Tweening.TweenCallback<int> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_DG_Tweening_TweenCallback_int(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(DG.Tweening.TweenCallback<int>), L, pos);
	}

	void Push_DG_Tweening_TweenCallback_int(IntPtr L, DG.Tweening.TweenCallback<int> o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_Camera_CameraCallback_Event : LuaDelegate
	{
		public UnityEngine_Camera_CameraCallback_Event(LuaFunction func) : base(func) { }
		public UnityEngine_Camera_CameraCallback_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.Camera param0)
		{
			func.BeginPCall();
			func.PushSealed(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.Camera param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushSealed(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public UnityEngine.Camera.CameraCallback UnityEngine_Camera_CameraCallback(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.Camera.CameraCallback fn = delegate(UnityEngine.Camera param0) { };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_Camera_CameraCallback_Event target = new UnityEngine_Camera_CameraCallback_Event(func);
			UnityEngine.Camera.CameraCallback d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_Camera_CameraCallback_Event target = new UnityEngine_Camera_CameraCallback_Event(func, self);
			UnityEngine.Camera.CameraCallback d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_Camera_CameraCallback(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.Camera.CameraCallback), L, pos);
	}

	void Push_UnityEngine_Camera_CameraCallback(IntPtr L, UnityEngine.Camera.CameraCallback o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_Application_AdvertisingIdentifierCallback_Event : LuaDelegate
	{
		public UnityEngine_Application_AdvertisingIdentifierCallback_Event(LuaFunction func) : base(func) { }
		public UnityEngine_Application_AdvertisingIdentifierCallback_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(string param0, bool param1, string param2)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(string param0, bool param1, string param2)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			func.EndPCall();
		}
	}

	public UnityEngine.Application.AdvertisingIdentifierCallback UnityEngine_Application_AdvertisingIdentifierCallback(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.Application.AdvertisingIdentifierCallback fn = delegate(string param0, bool param1, string param2) { };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_Application_AdvertisingIdentifierCallback_Event target = new UnityEngine_Application_AdvertisingIdentifierCallback_Event(func);
			UnityEngine.Application.AdvertisingIdentifierCallback d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_Application_AdvertisingIdentifierCallback_Event target = new UnityEngine_Application_AdvertisingIdentifierCallback_Event(func, self);
			UnityEngine.Application.AdvertisingIdentifierCallback d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_Application_AdvertisingIdentifierCallback(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.Application.AdvertisingIdentifierCallback), L, pos);
	}

	void Push_UnityEngine_Application_AdvertisingIdentifierCallback(IntPtr L, UnityEngine.Application.AdvertisingIdentifierCallback o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_Application_LowMemoryCallback_Event : LuaDelegate
	{
		public UnityEngine_Application_LowMemoryCallback_Event(LuaFunction func) : base(func) { }
		public UnityEngine_Application_LowMemoryCallback_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call()
		{
			func.Call();
		}

		public void CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			func.EndPCall();
		}
	}

	public UnityEngine.Application.LowMemoryCallback UnityEngine_Application_LowMemoryCallback(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.Application.LowMemoryCallback fn = delegate() { };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_Application_LowMemoryCallback_Event target = new UnityEngine_Application_LowMemoryCallback_Event(func);
			UnityEngine.Application.LowMemoryCallback d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_Application_LowMemoryCallback_Event target = new UnityEngine_Application_LowMemoryCallback_Event(func, self);
			UnityEngine.Application.LowMemoryCallback d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_Application_LowMemoryCallback(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.Application.LowMemoryCallback), L, pos);
	}

	void Push_UnityEngine_Application_LowMemoryCallback(IntPtr L, UnityEngine.Application.LowMemoryCallback o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_Application_LogCallback_Event : LuaDelegate
	{
		public UnityEngine_Application_LogCallback_Event(LuaFunction func) : base(func) { }
		public UnityEngine_Application_LogCallback_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(string param0, string param1, UnityEngine.LogType param2)
		{
			func.BeginPCall();
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(string param0, string param1, UnityEngine.LogType param2)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.Push(param1);
			func.Push(param2);
			func.PCall();
			func.EndPCall();
		}
	}

	public UnityEngine.Application.LogCallback UnityEngine_Application_LogCallback(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.Application.LogCallback fn = delegate(string param0, string param1, UnityEngine.LogType param2) { };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_Application_LogCallback_Event target = new UnityEngine_Application_LogCallback_Event(func);
			UnityEngine.Application.LogCallback d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_Application_LogCallback_Event target = new UnityEngine_Application_LogCallback_Event(func, self);
			UnityEngine.Application.LogCallback d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_Application_LogCallback(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.Application.LogCallback), L, pos);
	}

	void Push_UnityEngine_Application_LogCallback(IntPtr L, UnityEngine.Application.LogCallback o)
	{
		ToLua.Push(L, o);
	}

	class System_Func_bool_Event : LuaDelegate
	{
		public System_Func_bool_Event(LuaFunction func) : base(func) { }
		public System_Func_bool_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public bool Call()
		{
			func.BeginPCall();
			func.PCall();
			bool ret = func.CheckBoolean();
			func.EndPCall();
			return ret;
		}

		public bool CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			bool ret = func.CheckBoolean();
			func.EndPCall();
			return ret;
		}
	}

	public System.Func<bool> System_Func_bool(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Func<bool> fn = delegate() { return false; };
			return fn;
		}

		if(!flag)
		{
			System_Func_bool_Event target = new System_Func_bool_Event(func);
			System.Func<bool> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Func_bool_Event target = new System_Func_bool_Event(func, self);
			System.Func<bool> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Func_bool(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Func<bool>), L, pos);
	}

	void Push_System_Func_bool(IntPtr L, System.Func<bool> o)
	{
		ToLua.Push(L, o);
	}

	class System_Action_UnityEngine_PhysicsScene_Unity_Collections_NativeArray_UnityEngine_ModifiableContactPair_Event : LuaDelegate
	{
		public System_Action_UnityEngine_PhysicsScene_Unity_Collections_NativeArray_UnityEngine_ModifiableContactPair_Event(LuaFunction func) : base(func) { }
		public System_Action_UnityEngine_PhysicsScene_Unity_Collections_NativeArray_UnityEngine_ModifiableContactPair_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.PhysicsScene param0, Unity.Collections.NativeArray<UnityEngine.ModifiableContactPair> param1)
		{
			func.BeginPCall();
			func.PushValue(param0);
			func.PushValue(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.PhysicsScene param0, Unity.Collections.NativeArray<UnityEngine.ModifiableContactPair> param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushValue(param0);
			func.PushValue(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public System.Action<UnityEngine.PhysicsScene,Unity.Collections.NativeArray<UnityEngine.ModifiableContactPair>> System_Action_UnityEngine_PhysicsScene_Unity_Collections_NativeArray_UnityEngine_ModifiableContactPair(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			System.Action<UnityEngine.PhysicsScene,Unity.Collections.NativeArray<UnityEngine.ModifiableContactPair>> fn = delegate(UnityEngine.PhysicsScene param0, Unity.Collections.NativeArray<UnityEngine.ModifiableContactPair> param1) { };
			return fn;
		}

		if(!flag)
		{
			System_Action_UnityEngine_PhysicsScene_Unity_Collections_NativeArray_UnityEngine_ModifiableContactPair_Event target = new System_Action_UnityEngine_PhysicsScene_Unity_Collections_NativeArray_UnityEngine_ModifiableContactPair_Event(func);
			System.Action<UnityEngine.PhysicsScene,Unity.Collections.NativeArray<UnityEngine.ModifiableContactPair>> d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			System_Action_UnityEngine_PhysicsScene_Unity_Collections_NativeArray_UnityEngine_ModifiableContactPair_Event target = new System_Action_UnityEngine_PhysicsScene_Unity_Collections_NativeArray_UnityEngine_ModifiableContactPair_Event(func, self);
			System.Action<UnityEngine.PhysicsScene,Unity.Collections.NativeArray<UnityEngine.ModifiableContactPair>> d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_System_Action_UnityEngine_PhysicsScene_Unity_Collections_NativeArray_UnityEngine_ModifiableContactPair(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(System.Action<UnityEngine.PhysicsScene,Unity.Collections.NativeArray<UnityEngine.ModifiableContactPair>>), L, pos);
	}

	void Push_System_Action_UnityEngine_PhysicsScene_Unity_Collections_NativeArray_UnityEngine_ModifiableContactPair(IntPtr L, System.Action<UnityEngine.PhysicsScene,Unity.Collections.NativeArray<UnityEngine.ModifiableContactPair>> o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_AudioClip_PCMReaderCallback_Event : LuaDelegate
	{
		public UnityEngine_AudioClip_PCMReaderCallback_Event(LuaFunction func) : base(func) { }
		public UnityEngine_AudioClip_PCMReaderCallback_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(float[] param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(float[] param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public UnityEngine.AudioClip.PCMReaderCallback UnityEngine_AudioClip_PCMReaderCallback(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.AudioClip.PCMReaderCallback fn = delegate(float[] param0) { };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_AudioClip_PCMReaderCallback_Event target = new UnityEngine_AudioClip_PCMReaderCallback_Event(func);
			UnityEngine.AudioClip.PCMReaderCallback d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_AudioClip_PCMReaderCallback_Event target = new UnityEngine_AudioClip_PCMReaderCallback_Event(func, self);
			UnityEngine.AudioClip.PCMReaderCallback d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_AudioClip_PCMReaderCallback(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.AudioClip.PCMReaderCallback), L, pos);
	}

	void Push_UnityEngine_AudioClip_PCMReaderCallback(IntPtr L, UnityEngine.AudioClip.PCMReaderCallback o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_AudioClip_PCMSetPositionCallback_Event : LuaDelegate
	{
		public UnityEngine_AudioClip_PCMSetPositionCallback_Event(LuaFunction func) : base(func) { }
		public UnityEngine_AudioClip_PCMSetPositionCallback_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(int param0)
		{
			func.BeginPCall();
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(int param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.Push(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public UnityEngine.AudioClip.PCMSetPositionCallback UnityEngine_AudioClip_PCMSetPositionCallback(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.AudioClip.PCMSetPositionCallback fn = delegate(int param0) { };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_AudioClip_PCMSetPositionCallback_Event target = new UnityEngine_AudioClip_PCMSetPositionCallback_Event(func);
			UnityEngine.AudioClip.PCMSetPositionCallback d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_AudioClip_PCMSetPositionCallback_Event target = new UnityEngine_AudioClip_PCMSetPositionCallback_Event(func, self);
			UnityEngine.AudioClip.PCMSetPositionCallback d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_AudioClip_PCMSetPositionCallback(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.AudioClip.PCMSetPositionCallback), L, pos);
	}

	void Push_UnityEngine_AudioClip_PCMSetPositionCallback(IntPtr L, UnityEngine.AudioClip.PCMSetPositionCallback o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_Video_VideoPlayer_EventHandler_Event : LuaDelegate
	{
		public UnityEngine_Video_VideoPlayer_EventHandler_Event(LuaFunction func) : base(func) { }
		public UnityEngine_Video_VideoPlayer_EventHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.Video.VideoPlayer param0)
		{
			func.BeginPCall();
			func.PushSealed(param0);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.Video.VideoPlayer param0)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushSealed(param0);
			func.PCall();
			func.EndPCall();
		}
	}

	public UnityEngine.Video.VideoPlayer.EventHandler UnityEngine_Video_VideoPlayer_EventHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.Video.VideoPlayer.EventHandler fn = delegate(UnityEngine.Video.VideoPlayer param0) { };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_Video_VideoPlayer_EventHandler_Event target = new UnityEngine_Video_VideoPlayer_EventHandler_Event(func);
			UnityEngine.Video.VideoPlayer.EventHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_Video_VideoPlayer_EventHandler_Event target = new UnityEngine_Video_VideoPlayer_EventHandler_Event(func, self);
			UnityEngine.Video.VideoPlayer.EventHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_Video_VideoPlayer_EventHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.Video.VideoPlayer.EventHandler), L, pos);
	}

	void Push_UnityEngine_Video_VideoPlayer_EventHandler(IntPtr L, UnityEngine.Video.VideoPlayer.EventHandler o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_Video_VideoPlayer_ErrorEventHandler_Event : LuaDelegate
	{
		public UnityEngine_Video_VideoPlayer_ErrorEventHandler_Event(LuaFunction func) : base(func) { }
		public UnityEngine_Video_VideoPlayer_ErrorEventHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.Video.VideoPlayer param0, string param1)
		{
			func.BeginPCall();
			func.PushSealed(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.Video.VideoPlayer param0, string param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushSealed(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public UnityEngine.Video.VideoPlayer.ErrorEventHandler UnityEngine_Video_VideoPlayer_ErrorEventHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.Video.VideoPlayer.ErrorEventHandler fn = delegate(UnityEngine.Video.VideoPlayer param0, string param1) { };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_Video_VideoPlayer_ErrorEventHandler_Event target = new UnityEngine_Video_VideoPlayer_ErrorEventHandler_Event(func);
			UnityEngine.Video.VideoPlayer.ErrorEventHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_Video_VideoPlayer_ErrorEventHandler_Event target = new UnityEngine_Video_VideoPlayer_ErrorEventHandler_Event(func, self);
			UnityEngine.Video.VideoPlayer.ErrorEventHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_Video_VideoPlayer_ErrorEventHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.Video.VideoPlayer.ErrorEventHandler), L, pos);
	}

	void Push_UnityEngine_Video_VideoPlayer_ErrorEventHandler(IntPtr L, UnityEngine.Video.VideoPlayer.ErrorEventHandler o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_Video_VideoPlayer_TimeEventHandler_Event : LuaDelegate
	{
		public UnityEngine_Video_VideoPlayer_TimeEventHandler_Event(LuaFunction func) : base(func) { }
		public UnityEngine_Video_VideoPlayer_TimeEventHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.Video.VideoPlayer param0, double param1)
		{
			func.BeginPCall();
			func.PushSealed(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.Video.VideoPlayer param0, double param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushSealed(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public UnityEngine.Video.VideoPlayer.TimeEventHandler UnityEngine_Video_VideoPlayer_TimeEventHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.Video.VideoPlayer.TimeEventHandler fn = delegate(UnityEngine.Video.VideoPlayer param0, double param1) { };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_Video_VideoPlayer_TimeEventHandler_Event target = new UnityEngine_Video_VideoPlayer_TimeEventHandler_Event(func);
			UnityEngine.Video.VideoPlayer.TimeEventHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_Video_VideoPlayer_TimeEventHandler_Event target = new UnityEngine_Video_VideoPlayer_TimeEventHandler_Event(func, self);
			UnityEngine.Video.VideoPlayer.TimeEventHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_Video_VideoPlayer_TimeEventHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.Video.VideoPlayer.TimeEventHandler), L, pos);
	}

	void Push_UnityEngine_Video_VideoPlayer_TimeEventHandler(IntPtr L, UnityEngine.Video.VideoPlayer.TimeEventHandler o)
	{
		ToLua.Push(L, o);
	}

	class UnityEngine_Video_VideoPlayer_FrameReadyEventHandler_Event : LuaDelegate
	{
		public UnityEngine_Video_VideoPlayer_FrameReadyEventHandler_Event(LuaFunction func) : base(func) { }
		public UnityEngine_Video_VideoPlayer_FrameReadyEventHandler_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call(UnityEngine.Video.VideoPlayer param0, long param1)
		{
			func.BeginPCall();
			func.PushSealed(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}

		public void CallWithSelf(UnityEngine.Video.VideoPlayer param0, long param1)
		{
			func.BeginPCall();
			func.Push(self);
			func.PushSealed(param0);
			func.Push(param1);
			func.PCall();
			func.EndPCall();
		}
	}

	public UnityEngine.Video.VideoPlayer.FrameReadyEventHandler UnityEngine_Video_VideoPlayer_FrameReadyEventHandler(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			UnityEngine.Video.VideoPlayer.FrameReadyEventHandler fn = delegate(UnityEngine.Video.VideoPlayer param0, long param1) { };
			return fn;
		}

		if(!flag)
		{
			UnityEngine_Video_VideoPlayer_FrameReadyEventHandler_Event target = new UnityEngine_Video_VideoPlayer_FrameReadyEventHandler_Event(func);
			UnityEngine.Video.VideoPlayer.FrameReadyEventHandler d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			UnityEngine_Video_VideoPlayer_FrameReadyEventHandler_Event target = new UnityEngine_Video_VideoPlayer_FrameReadyEventHandler_Event(func, self);
			UnityEngine.Video.VideoPlayer.FrameReadyEventHandler d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_UnityEngine_Video_VideoPlayer_FrameReadyEventHandler(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(UnityEngine.Video.VideoPlayer.FrameReadyEventHandler), L, pos);
	}

	void Push_UnityEngine_Video_VideoPlayer_FrameReadyEventHandler(IntPtr L, UnityEngine.Video.VideoPlayer.FrameReadyEventHandler o)
	{
		ToLua.Push(L, o);
	}

	class EventDelegate_Callback_Event : LuaDelegate
	{
		public EventDelegate_Callback_Event(LuaFunction func) : base(func) { }
		public EventDelegate_Callback_Event(LuaFunction func, LuaTable self) : base(func, self) { }

		public void Call()
		{
			func.Call();
		}

		public void CallWithSelf()
		{
			func.BeginPCall();
			func.Push(self);
			func.PCall();
			func.EndPCall();
		}
	}

	public EventDelegate.Callback EventDelegate_Callback(LuaFunction func, LuaTable self, bool flag)
	{
		if (func == null)
		{
			EventDelegate.Callback fn = delegate() { };
			return fn;
		}

		if(!flag)
		{
			EventDelegate_Callback_Event target = new EventDelegate_Callback_Event(func);
			EventDelegate.Callback d = target.Call;
			target.method = d.Method;
			return d;
		}
		else
		{
			EventDelegate_Callback_Event target = new EventDelegate_Callback_Event(func, self);
			EventDelegate.Callback d = target.CallWithSelf;
			target.method = d.Method;
			return d;
		}
	}

	bool Check_EventDelegate_Callback(IntPtr L, int pos)
	{
		return TypeChecker.CheckDelegateType(typeof(EventDelegate.Callback), L, pos);
	}

	void Push_EventDelegate_Callback(IntPtr L, EventDelegate.Callback o)
	{
		ToLua.Push(L, o);
	}

}

