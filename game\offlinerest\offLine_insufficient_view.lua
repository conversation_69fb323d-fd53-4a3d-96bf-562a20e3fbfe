
-- 离线时间不足
----------------------------------
OffLineInsufficient = OffLineInsufficient or BaseClass(SafeBaseView)

function OffLineInsufficient:__init()
	self:LoadConfig()
end

function OffLineInsufficient:LoadConfig()
	self:AddViewResource(0, "uis/view/offlinerest_ui_prefab", "layout_insufficient")
end

function OffLineInsufficient:ReleaseCallBack()
	if self.cell ~= nil then
		self.cell:DeleteMe()
		self.cell = nil
	end
end

function OffLineInsufficient:LoadCallBack(index, loaded_times)
	self.cell = ItemCell.New()
	XUI.AddClickEventListener(self.node_list.btn_look, BindTool.Bind1(self.OnClickLook, self))
	self.close_delay = GlobalTimerQuest:AddDelayTimer(BindTool.Bind1(self.Close, self), 7)
end

function OffLineInsufficient:OnFlush()
	self.cell:SetData({item_id = 22537})
	local color = ItemWGData.Instance:GetItemColor(22537)
end

function OffLineInsufficient:OnClickLook()
	if 0 ~= ItemWGData.Instance:GetItemNumInBagById(22536) then
		OfflineRestWGCtrl.Instance:OpenUserOfflineView(22536)
	elseif 0 ~= ItemWGData.Instance:GetItemNumInBagById(22537) then
		OfflineRestWGCtrl.Instance:OpenUserOfflineView(22537)
	else
		FunOpen.Instance:OpenViewByName(GuideModuleName.Shop)
	end
end

function OffLineInsufficient:ShowIndexCallBack()
	self:Flush()
end

function OffLineInsufficient:OpenCallBack()

end

function OffLineInsufficient:CloseCallBack(is_all)
	if self.close_delay then
		GlobalTimerQuest:CancelQuest(self.close_delay)
		self.close_delay = nil
	end
end