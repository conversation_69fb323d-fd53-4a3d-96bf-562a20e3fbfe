--战队战令界面
ZhanDuiZhanLingView = ZhanDuiZhanLingView or BaseClass(SafeBaseView)
function ZhanDuiZhanLingView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
        { vector2 = Vector2(0, 0), sizeDelta = Vector2(690, 474) })
    self:AddViewResource(0, "uis/view/zhandui_ui_prefab", "layout_zhanling")
end

function ZhanDuiZhanLingView:ReleaseCallBack()
    --[[if self.zhanling_grid then
        self.zhanling_grid:DeleteMe()
        self.zhanling_grid = nil
    end
    ]]
    if self.zhan_dui_info_change_event then
        GlobalEventSystem:UnBind(self.zhan_dui_info_change_event)
        self.zhan_dui_info_change_event = nil
    end
    self.cur_select_data = nil
end

function ZhanDuiZhanLingView:LoadCallBack()
    self.node_list["title_view_name"].text.text = Language.ZhanDui.ZhanLingViewName
    --self:SetSecondView(nil, self.node_list["size"])

    --[[self.zhanling_grid = AsyncBaseGrid.New()
    self.zhanling_grid:CreateCells({col = 3, change_cells_num = 1, list_view = self.node_list["zhanling_list"],
        itemRender = ZhanDuiZhanLingItenRender,
        assetBundle = "uis/view/zhandui_ui_prefab",
        assetName = "zhanling_item_render",
    })
    self.zhanling_grid:SetSelectCallBack(BindTool.Bind(self.SelectCellCallBack, self))
    self.zhanling_grid:SetStartZeroIndex(false)
    ]]

    local zhan_dui_info = ZhanDuiWGData.Instance:GetZhanDuiInfo()
    local data_list = KF3V3WGData.Instance:GetZhanLingDataList()
    if zhan_dui_info == nil or data_list == nil then return end
    local default_select_index = 1
    for i, v in ipairs(data_list) do
        if v.lingpai_id == zhan_dui_info.zhandui_lingpai_id then
            default_select_index = i
        end
    end

    --self.zhanling_grid:SetSelectCellIndex(default_select_index)
    local cfg = KF3V3WGData.Instance:GetZhanLingCfg(zhan_dui_info.zhandui_lingpai_id)
    self.cur_select_data = cfg
    --local bundel, asset = ZhanDuiWGData.Instance:GetZhanLingResPath(cfg.icon)
    local grade_cfg = KF3V3WGData.Instance:GetDuanWeiCfgAll(zhan_dui_info.score)
    self.node_list.zhanling_img.image:LoadSprite(ResPath.GetCommon("a3_jjc_hz" .. grade_cfg.grade))
    self.node_list.zhanling_img.image:SetNativeSize()
    local star_res_list = GetStarImgResByStar(grade_cfg.star)
    for i = 1, 5 do
        self.node_list["star" .. i]:SetActive(true)
        self.node_list["star" .. i].image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
    end

    self.node_list.cur_name.text.text = zhan_dui_info.zhandui_lingpai_name
    if data_list.lingpai_id then
        self.node_list.redpoint_img:SetActive(data_list.lingpai_id)
    end

    XUI.AddClickEventListener(self.node_list.btn_save, BindTool.Bind(self.OnClickChangeZhanLingText, self))
    self.node_list.name_inputfield.input_field.onValueChanged:AddListener(BindTool.Bind(self.OnEditValueChange, self))
    self.node_list.name_inputfield.input_field.onEndEdit:AddListener(BindTool.Bind(self.OnEndEdit, self))

    self.zhan_dui_info_change_event = GlobalEventSystem:Bind(OtherEventType.ZhanDui_Info_Change,
        BindTool.Bind(self.OnZhanDuiInfoChange, self)) --战队信息改变
end

function ZhanDuiZhanLingView:ShowIndexCallBack()
    self:Flush()
end

function ZhanDuiZhanLingView:OnFlush()
    --local data_list = KF3V3WGData.Instance:GetZhanLingDataList()
    --self.zhanling_grid:SetDataList(data_list, 3)

    self:FlushZhanLingName()

    self:FlushSaveBtn()
end

-- 刷新保存按钮
function ZhanDuiZhanLingView:FlushSaveBtn()
    XUI.SetGraphicGrey(self.node_list.btn_save, false)
    if not self.cur_select_data then
        return
    end
    XUI.SetGraphicGrey(self.node_list.btn_save,
        not ZhanDuiWGData.Instance:GetIsParticipationBySeq(self.cur_select_data.lingpai_id))
end

function ZhanDuiZhanLingView:FlushZhanLingName()
    local zhandui_info = ZhanDuiWGData.Instance:GetZhanDuiInfo()
    self.node_list.zhanling_text.text.text = zhandui_info.zhandui_lingpai_name
    self.node_list.cur_name.text.text = zhandui_info.zhandui_lingpai_name
    --local cfg = KF3V3WGData.Instance:GetZhanLingCfg(zhandui_info.zhandui_lingpai_id)
    --ZhanDuiWGCtrl.ChangeZhanLingTextColor(self.node_list.zhanling_text.text, cfg.color_type)
end

--[[function ZhanDuiZhanLingView:SelectCellCallBack(cell)
	if cell == nil or cell:GetData() == nil then
		return
	end
	local cell_data = cell:GetData()
    self.cur_select_data = cell_data
    ZhanDuiWGData.Instance:RemoveNewZhanLingFlag(cell_data.lingpai_id)
    cell:FlushRedPoint()
    self.node_list.time_text.text.text = cell_data.desc
    local bundel, asset = ZhanDuiWGData.Instance:GetZhanLingResPath(cell_data.icon)
    self.node_list.zhanling_img.image:LoadSprite(bundel, asset, function ()
        self.node_list.zhanling_img.image:SetNativeSize()
    end)
    local color_def = ZhanDuiWGData.Instance:GetZhanLingTextOutLine(cell_data.icon)
    -- if not IsEmptyTable(color_def) and color_def.outline then
    --     self.node_list.zhanling_text.out_line.effectColor = StrToColor(color_def.outline)                --.effectColor
    -- end
    local cfg = KF3V3WGData.Instance:GetZhanLingCfg(cell_data.lingpai_id)
	--ZhanDuiWGCtrl.ChangeZhanLingTextColor(self.node_list.zhanling_text.text, cfg.color_type)
    self:FlushSaveBtn()
end
]]

function ZhanDuiZhanLingView:OnClickChangeZhanLingText()
    if not ZhanDuiWGData.Instance:GetIsZhanDuiCaptain() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.NotIsZhanduiCaptainCantDo)
        return
    end
    local str = self.node_list.name_inputfield.input_field.text
    if ChatFilter.Instance:IsIllegal(str, true) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.IllegalContent)
        return
    end
    if not str then
        return
    end

    -- 检查令牌是否激活
    if not ZhanDuiWGData.Instance:GetIsParticipationBySeq(self.cur_select_data.lingpai_id) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ZhanlingUnactivated)
        return
    end

    ZhanDuiWGCtrl.Instance:ModifyZhanDuiZhanLingText(str)
    ZhanDuiWGCtrl.Instance:SendChooseZhanduiLingpai(self.cur_select_data.lingpai_id)
end

function ZhanDuiZhanLingView:OnEditValueChange(str)
    local len, table = CheckStringLen(str, 3)
    if not len then
        if table then
            local str = ""
            for i = 1, #table do
                str = str .. table[i]
            end
            local is_c_str = CheckChineseCharacters(str)
            if is_c_str then
                self.node_list.name_inputfield.input_field.text = str
                self.node_list.zhanling_text.text.text = str
            else
                SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.OnlyChineseCharacters)
                self.node_list.name_inputfield.input_field.text = ""
                self:FlushZhanLingName()
            end
        end
        if self.last_edit_time and self.last_edit_time > Status.NowTime then
            return
        end
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.ZhanLingMaxLenth)
        self.last_edit_time = Status.NowTime + 0.5
    else
        local len = StringLen(str)
        if len > 0 then
            local is_c_str = CheckChineseCharacters(str)
            if is_c_str then
                self.node_list.name_inputfield.input_field.text = str
                self.node_list.zhanling_text.text.text = str
            else
                self.node_list.name_inputfield.input_field.text = ""
                self:FlushZhanLingName()
                if self.last_edit_time and self.last_edit_time > Status.NowTime then
                    return
                end
                SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.OnlyChineseCharacters)
                self.last_edit_time = Status.NowTime + 0.5
            end
        end
    end
end

function ZhanDuiZhanLingView:OnEndEdit(str)
end

function ZhanDuiZhanLingView:OnZhanDuiInfoChange(notify_reason)
    if notify_reason == NotifyZhanduiInfoReason.ModifyName then
        self:FlushZhanLingName()
    end
end

--------------------------------------------------------------------------------
ZhanDuiZhanLingItenRender = ZhanDuiZhanLingItenRender or BaseClass(BaseRender)
function ZhanDuiZhanLingItenRender:__init()
end

function ZhanDuiZhanLingItenRender:OnFlush()
    local zhandui_info = ZhanDuiWGData.Instance:GetZhanDuiInfo() -- 自身战队信息

    -- self.node_list.TestText.text.text = self.data.lingpai_id

    -- 令牌名称
    self.node_list.zhanling_text.text.text = self.data.lingpai_name

    -- 装备中标签
    self.node_list.wear:SetActive(self.data.lingpai_id == zhandui_info.zhandui_lingpai_id)

    local bundel, assert = ZhanDuiWGData.Instance:GetZhanLingResPath(self.data.icon)
    self.node_list.icon.image:LoadSprite(bundel, assert, function()
        self.node_list.icon.image:SetNativeSize()
    end)
    self:FlushRedPoint()
    local is_active = ZhanDuiWGData.Instance:GetIsParticipationBySeq(self.data.lingpai_id)
    XUI.SetGraphicGrey(self.node_list.icon.image, not is_active)
end

function ZhanDuiZhanLingItenRender:FlushRedPoint()
    --local new_flag = ZhanDuiWGData.Instance:GetNewZhanLingFlag(self.data.lingpai_id)
    --self.node_list.redpoint_img:SetActive(new_flag)
end

function ZhanDuiZhanLingItenRender:OnSelectChange(is_select)
    self.node_list.select:SetActive(is_select)
end
