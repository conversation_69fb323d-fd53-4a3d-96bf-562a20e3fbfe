NewAppearanceWGData = NewAppearanceWGData or BaseClass()

local empty_table = {}
function NewAppearanceWGData:__init()
    if NewAppearanceWGData.Instance then
		error("[NewAppearanceWGData] Attempt to create singleton twice!")
		return
	end

	NewAppearanceWGData.Instance = self

    self:InitAttrStoreCfg()
    self:InitAdvancedParam()
    self:InitFashionParam()
    self:InitQiChongParam()
    self:InitAdvancedCfg()
    self:InitFashionCfg()
    self:InitQiChongCfg()
    self:InitHuaLingCfg()

    local rm = RemindManager.Instance
    rm:Register(RemindName.NewAppearance_WaiGuan_Body, BindTool.Bind(self.GetFashionRemind, self, TabIndex.new_appearance_waiguan_body))
    rm:Register(RemindName.NewAppearance_WaiGuan_Weapon, BindTool.Bind(self.GetFashionRemind, self, TabIndex.new_appearance_waiguan_weapon))
    rm:Register(RemindName.NewAppearance_WaiGuan_Wing, BindTool.Bind(self.GetFashionRemind, self, TabIndex.new_appearance_waiguan_wing))
    rm:Register(RemindName.NewAppearance_WaiGuan_FaBao, BindTool.Bind(self.GetFashionRemind, self, TabIndex.new_appearance_waiguan_fabao))
    rm:Register(RemindName.NewAppearance_WaiGuan_ShenBing, BindTool.Bind(self.GetFashionRemind, self, TabIndex.new_appearance_waiguan_shenbing))
    rm:Register(RemindName.NewAppearance_WaiGuan_JianZhen, BindTool.Bind(self.GetFashionRemind, self, TabIndex.new_appearance_waiguan_jianzhen))
    rm:Register(RemindName.NewAppearance_ZhuangBan_Mask, BindTool.Bind(self.GetFashionRemind, self, TabIndex.new_appearance_zhuangban_mask))
    rm:Register(RemindName.NewAppearance_ZhuangBan_Belt, BindTool.Bind(self.GetFashionRemind, self, TabIndex.new_appearance_zhuangban_belt))
    rm:Register(RemindName.NewAppearance_ZhuangBan_WeiBa, BindTool.Bind(self.GetFashionRemind, self, TabIndex.new_appearance_zhuangban_weiba))
    rm:Register(RemindName.NewAppearance_ZhuangBan_ShouHuan, BindTool.Bind(self.GetFashionRemind, self, TabIndex.new_appearance_zhuangban_shouhuan))
    rm:Register(RemindName.NewAppearance_ZhuangBan_Foot, BindTool.Bind(self.GetFashionRemind, self, TabIndex.new_appearance_zhuangban_foot))
    rm:Register(RemindName.NewAppearance_ZhuangBan_PhotoFrame, BindTool.Bind(self.GetFashionRemind, self, TabIndex.new_appearance_zhuangban_photoframe))
    rm:Register(RemindName.NewAppearance_ZhuangBan_Bubble, BindTool.Bind(self.GetFashionRemind, self, TabIndex.new_appearance_zhuangban_bubble))

    rm:Register(RemindName.NewAppearance_WaiGuan_Halo, BindTool.Bind(self.GetFashionRemind, self, NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_HALO))
    self:CalcFashionStuffIdList()
end

function NewAppearanceWGData:__delete()
    local rm = RemindManager.Instance
    rm:UnRegister(RemindName.NewAppearance_WaiGuan_Body)
    rm:UnRegister(RemindName.NewAppearance_WaiGuan_Weapon)
    rm:UnRegister(RemindName.NewAppearance_WaiGuan_Halo)
    rm:UnRegister(RemindName.NewAppearance_WaiGuan_Wing)
    rm:UnRegister(RemindName.NewAppearance_WaiGuan_FaBao)
    rm:UnRegister(RemindName.NewAppearance_WaiGuan_ShenBing)
    rm:UnRegister(RemindName.NewAppearance_WaiGuan_JianZhen)
    rm:UnRegister(RemindName.NewAppearance_ZhuangBan_Mask)
    rm:UnRegister(RemindName.NewAppearance_ZhuangBan_Belt)
    rm:UnRegister(RemindName.NewAppearance_ZhuangBan_WeiBa)
    rm:UnRegister(RemindName.NewAppearance_ZhuangBan_ShouHuan)
    rm:UnRegister(RemindName.NewAppearance_ZhuangBan_Foot)
    rm:UnRegister(RemindName.NewAppearance_ZhuangBan_PhotoFrame)
    rm:UnRegister(RemindName.NewAppearance_ZhuangBan_Bubble)

    self:DeleteAdvancedData()
    self:DeleteQiChongData()
    self:DeleteHuaLingData()
    self:DeleteAttrStoreData()
    NewAppearanceWGData.Instance = nil
end

----[[======================时装==============================
function NewAppearanceWGData:InitFashionParam()
    self.fashion_item_list = {}
	self.fashion_end_time_list = {}
    self.fashion_show_list = {}
    self.appearance_reslove_info = {}

    self.fs_fix_fail_count_cache = {}
    self.fs_per_fail_count_cache = {}
    self.fs_up_star_consume_cache = {}
end

function NewAppearanceWGData:InitFashionCfg()
    local fashion_cfg = ConfigManager.Instance:GetAutoConfig("shizhuangcfg_auto")

    self.fashion_map_cfg = ListToMap(fashion_cfg.cfg, "part_type", "index")
    self.fashion_res_map_cfg = ListToMap(fashion_cfg.cfg, "part_type", "resouce")
    self.fashion_uplevel_map_cfg = ListToMap(fashion_cfg.uplevel, "part_type", "index")
    self.fashion_uplevel_stuff_map_cfg = ListToMap(fashion_cfg.uplevel, "stuff_id")
    self.bubble_map_cfg = ListToMap(fashion_cfg.frame_photo, "qipao_id")
    self.fashion_other_cfg = fashion_cfg.other[1]
    local upgrade_cfg = ConfigManager.Instance:GetAutoConfig("upgrade_auto")
    self.fashion_skill_cfg = ListToMap(upgrade_cfg.image_skill, "type", "index", "skill_level")
    self.fashion_skill_effect_cfg = upgrade_cfg.skill_effect
    self.spine_tx_frame_cfg = ListToMap(fashion_cfg.spine_tx_frame, "index")

    self.reslove_map_cfg = ListToMap(fashion_cfg.melt_stuff, "item_id")
    self.reslove_level_map_cfg = ListToMap(fashion_cfg.melt_level, "level")
    self.show_all_suit_cfg = fashion_cfg.show_all_suit

    -- 时装新百分比升星数据
    self.fs_fix_fail_count_cfg = ListToMapList(fashion_cfg.fix_fail_count, "fix_fail_index")
    self.fs_per_fail_count_cfg = ListToMapList(fashion_cfg.per_fail_count, "per_fail_index")
    self.fs_up_star_consume_cfg = ListToMapList(fashion_cfg.up_star_consume, "up_star_index")
    self.fs_up_star_per_cfg = ListToMapList(fashion_cfg.attr_add_per, "attr_add_index")

    self.fs_fix_fail_count_cache = self:CalAttrCfgPack(self.fs_fix_fail_count_cfg, "attr_per")
    self.fs_per_fail_count_cache = self:CalAttrCfgPack(self.fs_per_fail_count_cfg, "attr_per")
    self.fs_up_star_consume_cache = self:CalAttrCfgPack(self.fs_up_star_consume_cfg, "consume_num")
    self.fs_up_star_per_cache = self:CalAttrCfgPack(self.fs_up_star_per_cfg, "attr_add_per")
end

----[[ 时装基础配置
function NewAppearanceWGData:GetFashionCfgByIndex(part_type, index)
    return (self.fashion_map_cfg[part_type] or empty_table)[index]
end

function NewAppearanceWGData:GetFashionCfgByProtocolIndex(part_type, protocol_index)
    local index = math.floor(protocol_index / 100)
    local fashion_cfg = (self.fashion_map_cfg[part_type] or empty_table)[index] or nil

    -- 当时装表没查到当前时装，检查是否为炫彩时装
    if fashion_cfg == nil then
        fashion_cfg = NewAppearanceColorfulWGData.Instance:GetShiZhuangExtendCfg2(part_type, index)
    end

    return fashion_cfg
end

function NewAppearanceWGData:GetFashionCfgByItemId(item_id)
    local compose_cfg = ComposeWGData.Instance:GetComposeCfgByStuffId1(item_id)
	if not IsEmptyTable(compose_cfg) then
		item_id = compose_cfg.product_id
	end

    local level_cfg = self:GetFashionUpLevelCfgById(item_id)
    if level_cfg then
        return self:GetFashionCfgByIndex(level_cfg.part_type, level_cfg.index)
    end

    return nil
end

function NewAppearanceWGData:GetFashionCfgByResId(part_type, resouce)
    return (self.fashion_res_map_cfg[part_type] or empty_table)[resouce]
end

-- 获取时装资源 by 物品id
function NewAppearanceWGData:GetFashionResByItemId(item_id)
    local cfg = self:GetFashionCfgByItemId(item_id)
    if cfg then
        return cfg.resouce, cfg.part_type, cfg
    end

    return nil, nil, nil
end

-- 获取时装 物品id
function NewAppearanceWGData:GetFashionItemId(part_type, index)
	local cfg = self:GetFashionUpLevelCfg(part_type, index)
    return cfg and cfg.stuff_id or 0
end

-- 获取时装类型 by 物品id
function NewAppearanceWGData:GetFashionTypeByItemId(item_id)
	local cfg = self:GetFashionCfgByItemId(item_id)
	return cfg and cfg.part_type or -1
end

--]]

--获取角色身体部位资源 face, hair, body
function NewAppearanceWGData:GetRolePartResByResId(resouce, sex, prof)
	if prof > 10 then
		prof = prof % 10
	end

	local face_res_id = 0
	local hair_res_id = 0
	local body_res_id = 0
    local job_cfg = RoleWGData.Instance:GetJobConfig(sex, prof)
    if job_cfg then
        hair_res_id = job_cfg.default_hair
        body_res_id = job_cfg.default_body
        face_res_id = job_cfg.default_face
    end

    local cfg = self:GetFashionCfgByResId(1, resouce)
    if cfg == nil then
        return face_res_id, hair_res_id, body_res_id
    end

    body_res_id = RoleWGData.GetPartModelRes(ROLE_SKIN_TYPE.BODY, cfg.body_id, sex, prof)
    face_res_id = RoleWGData.GetPartModelRes(ROLE_SKIN_TYPE.FACE, cfg.face_id, sex, prof)
    hair_res_id = RoleWGData.GetPartModelRes(ROLE_SKIN_TYPE.HAIR, cfg.hair_id, sex, prof)

    -- 【职业改动修改】
    --  职业 和尚无头发
    -- if not (prof == GameEnum.ROLE_PROF_2 and sex == GameEnum.MALE) then
    --     hair_res_id = RoleWGData.GetPartModelRes(ROLE_SKIN_TYPE.HAIR, cfg.hair_id, sex, prof)
    -- end

    return face_res_id, hair_res_id, body_res_id
end

----[[ 时装升级配置
-- 时装升级配置
function NewAppearanceWGData:GetFashionUpLevelFixFailCfg(index, level)
    return (self.fs_fix_fail_count_cache[index] or {})[level]
end

function NewAppearanceWGData:GetFashionUpLevelPerFailCfg(index, level)
    return (self.fs_per_fail_count_cache[index] or {})[level]
end

function NewAppearanceWGData:GetFashionUpLevelConsumeCfg(index, level)
    return (self.fs_up_star_consume_cache[index] or {})[level]
end

function NewAppearanceWGData:GetFashionUpLevelPerAddCfg(index, level)
    return (self.fs_up_star_per_cache[index] or {})[level]
end

-- 单条配置
function NewAppearanceWGData:GetFashionUpLevelCfg(part_type, index)
    return (self.fashion_uplevel_map_cfg[part_type] or empty_table)[index]
    -- return ((self.fashion_uplevel_map_cfg[part_type] or empty_table)[index] or empty_table)[level]
end

-- 属性百分比加成获取(万分比加成值)
function NewAppearanceWGData:GetFashionPerAddValue(part_type, index, level)
    local level_cfg = self:GetFashionUpLevelCfg(part_type, index)
    local add_per_value = 0
    if level > level_cfg.max_up_level then
        return add_per_value
    end

    for i = 1, level do
        add_per_value = add_per_value + self:GetFashionUpLevelPerAddCfg(level_cfg.attr_add_index, i)
    end
    return add_per_value
end

-- 属性百分比加成获取(属性条目)
function NewAppearanceWGData:GetFashionPerAddAttr(part_type, index, level)
    local level_cfg = self:GetFashionUpLevelCfg(part_type, index)
    local attr_data = {}
    if level > level_cfg.max_up_level then
        return attr_data
    end
    local add_per_value = 0
    local next_add_per_value = 0
    for i = 1, level + 1 do
        if i <= level then
            add_per_value = add_per_value + self:GetFashionUpLevelPerAddCfg(level_cfg.attr_add_index, i)
        end
        next_add_per_value = next_add_per_value + (self:GetFashionUpLevelPerAddCfg(level_cfg.attr_add_index, i) or 0)
    end
    local attr_title_str = string.format(Language.NewAppearance.AttrAddPer, Language.NewAppearance.FashionPerAttrGroup[part_type])
    attr_data.attr_name = ToColorStr(attr_title_str, COLOR3B.GLOD)
    -- Language.NewAppearanc.TabSub1
    attr_data.is_per = true
    attr_data.attr_value = add_per_value
    attr_data.attr_next_value = next_add_per_value
    attr_data.add_value = next_add_per_value - add_per_value

    return attr_data
end

-- 等级属性获取
function NewAppearanceWGData:GetFashionUpLevelAttrCfg(part_type, index, level)
    local level_cfg = self:GetFashionUpLevelCfg(part_type, index)
    local attr_data = {}

    if level > level_cfg.max_up_level then
        return attr_data
    end

    local t_attr_id
    for i = 1, level do
        local fix_fail_value = self:GetFashionUpLevelFixFailCfg(level_cfg.fix_fail_index, i)
        local per_fail_value = self:GetFashionUpLevelPerFailCfg(level_cfg.per_fail_index, i)

        for i = 1, 6 do
            t_attr_id = level_cfg["attr_id" .. i]
            if t_attr_id > 0 then
                local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(t_attr_id)
                local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(attr_str)
                local base_value = level_cfg["attr_value" .. i]
                local target_value = 0

                if is_per then
                    target_value = math.floor(base_value * per_fail_value / 10000)
                else
                    target_value = math.floor(base_value * fix_fail_value / 10000)
                end

                if nil == attr_data[attr_str] then
                    attr_data[attr_str] = 0
                end

                attr_data[attr_str] = attr_data[attr_str] + target_value
            end
        end 
    end

    return attr_data
end

function NewAppearanceWGData:GetCurFashionPartAddPerValue(part_type)
    local add_per_total_value = 0
    local all_list = self.fashion_item_list[part_type] or {}
    local mark_list = {}
    if not IsEmptyTable(all_list.active_flag)then
        for key, value in pairs(all_list.active_flag) do
            if value > 0 then
                mark_list[key] = value
            end
        end
    end

    if not IsEmptyTable(mark_list) then
        for key, value in pairs(mark_list) do
            add_per_total_value = add_per_total_value + self:GetFashionPerAddValue(part_type, key, value)
        end
        add_per_total_value = add_per_total_value / 10000
    end
    return add_per_total_value 
end

-- 等级升级消耗获取
function NewAppearanceWGData:GetFashionUpLevelCostStuffNumCfg(part_type, index, level)
    local level_cfg = self:GetFashionUpLevelCfg(part_type, index)
    return self:GetFashionUpLevelConsumeCfg(level_cfg.up_star_index, level)
end

function NewAppearanceWGData:GetFashionUpLevelCfgById(item_id) -- , level
    return self.fashion_uplevel_stuff_map_cfg[item_id]
    -- return (self.fashion_uplevel_stuff_map_cfg[item_id] or empty_table)[level]
end

-- 获取时装最大等级 by id
function NewAppearanceWGData:GetFashionMaxLevelById(item_id)
    local cfg = self.fashion_uplevel_stuff_map_cfg[item_id]
    return cfg and cfg.max_up_level or 0
end

-- 时装是否可以飞
function NewAppearanceWGData:GetFashionIsCanFly(part_type, index)
    return ((self.fashion_map_cfg[part_type] or empty_table)[index] or empty_table)["is_fly"] == 1
end

-- 时装技能
function NewAppearanceWGData:GetFashionSkillCfg(part_type, index, skill_level)
    skill_level = skill_level or 1
    local advanced_type = SHIZHUANG_2_ADVANCED_TYPE[part_type]
    return ((self.fashion_skill_cfg[advanced_type] or empty_table)[index] or empty_table)[skill_level]
end

-- 时装技能特效 by skill_id
function NewAppearanceWGData:GetFashionSkillEffectCfgBySkillId(skill_id)
    return self.fashion_skill_effect_cfg[skill_id]
end

--]]

-- 获取气泡显示配置
function NewAppearanceWGData:GetBubbleCfg(qipao_id)
    return self.bubble_map_cfg[qipao_id]
end

-- 获取其他配置
function NewAppearanceWGData:GetFashionOtherCfgByKey(key)
	return self.fashion_other_cfg[key]
end

-- 获取动态头像框配置
function NewAppearanceWGData:GetSpineTXFrameCfgByIndex(index)
    return self.spine_tx_frame_cfg[index]
end

function NewAppearanceWGData:SetFashionInfo(protocol)
    self:ClearFashionShowList()
    for k, v in pairs(protocol.item_list) do
        self.fashion_item_list[v.part] = v
    end
end

function NewAppearanceWGData:SetFashionTimeItemInfo(protocol)
    self:ClearFashionShowList()
	self.fashion_end_time_list = protocol.fashion_end_time_list
end

function NewAppearanceWGData:SetSingleUpdateInfo(protocol)
    self:ClearFashionShowList()
    local change_data = protocol.change_data
    for k, v in pairs(self.fashion_item_list) do
        if v.part == change_data.part then
            v.level_list[change_data.index] = change_data.level
            v.active_flag[change_data.index] = change_data.is_active
        end
    end

    if self.fashion_end_time_list[change_data.part] == nil then
        self.fashion_end_time_list[change_data.part] = {}
    end

    self.fashion_end_time_list[change_data.part][change_data.index] = change_data.timelimit_szinfo
end

-- 获取限时时装结束时间
function NewAppearanceWGData:GetFashionEndTime(part_type, index)
	return (self.fashion_end_time_list[part_type] or empty_table)[index] or 0
end

-- 时装是否激活
function NewAppearanceWGData:GetFashionIsAct(part_type, index)
    return ((self.fashion_item_list[part_type] or empty_table)["active_flag"] or empty_table)[index] == 1
end

function NewAppearanceWGData:GetFashionIsActByItemId(item_id)
    local cfg = self:GetFashionCfgByItemId(item_id)
    if cfg then
        return self:GetFashionIsAct(cfg.part_type, cfg.index)
    end

    return false
end

-- 时装使用index
function NewAppearanceWGData:GetFashionUseIndex(part_type)
    local data = self.fashion_item_list[part_type]
	if data == nil then
        return 0, 0
    end

	return data.use_idx, data.level
end

-- 时装使用index
function NewAppearanceWGData:GetFashionKillUseIndex(part_type)
    return (self.fashion_item_list[part_type] or empty_table)["use_skill_index"] or 0
end

-- 获取时装等级
function NewAppearanceWGData:GetFashionLevel(part_type, index)
    return ((self.fashion_item_list[part_type] or empty_table)["level_list"] or empty_table)[index] or 0
end

-- 获取时装等级 by id
function NewAppearanceWGData:GetFashionLevelByItemId(item_id)
	local fashion_cfg = self:GetFashionCfgByItemId(item_id)
	if fashion_cfg == nil then
		return 0
	end

    return self:GetFashionLevel(fashion_cfg.part_type, fashion_cfg.index)
end

-- 清除时装展示列表
function NewAppearanceWGData:ClearFashionShowList(tab_index)
    if tab_index then
        self.fashion_show_list[tab_index] = nil
    else
        self.fashion_show_list = {}
    end
end

-- 获取时装展示列表
function NewAppearanceWGData:GetShowListByTabIndex(tab_index, ignore_condition)
    local tab_data = NEW_APPEARANCE_TAB_DATA[tab_index]
    local show_list = {}
    if tab_data == nil then
        return show_list
    end

    if self.fashion_show_list[tab_index] then
        return self.fashion_show_list[tab_index]
    end

    local is_shenbing = tab_data.is_shenbing
    local cfg = self.fashion_map_cfg[tab_data.fashion_type] or {}
    local part_type, index
    for k, base_cfg in pairs(cfg) do
        if ((not is_shenbing and base_cfg.shifoushengb ~= 1) or (is_shenbing and base_cfg.shifoushengb == 1))
        and (self:GetFashionItemIsCanShow(base_cfg) or ignore_condition) then
            part_type = base_cfg.part_type
            index = base_cfg.index
            local active_stuff_id = self:GetFashionItemId(part_type, index)
            local item_cfg = ItemWGData.Instance:GetItemConfig(active_stuff_id)

            local data = {
                part_type = part_type,
                index = index,
                active_stuff_id = active_stuff_id,
                color = item_cfg and item_cfg.color or 0,
                sort = 0,
            }
            		
            data.is_remind = self:GetFashionSingleRemind(part_type, index)
            table.insert(show_list, data)
        end
    end

    self.fashion_show_list[tab_index] = show_list
    return show_list
end

function NewAppearanceWGData:GetSortShowListByTabIndex(tab_index, is_sort_desc)
    local show_list = self:GetShowListByTabIndex(tab_index)

    local is_act, act_weight, color_weight
    for k,v in pairs(show_list) do
        is_act = self:GetFashionIsAct(v.part_type, v.index)
        
        if is_sort_desc then
            act_weight = (v.is_remind or is_act) and 10000 or 0
        else
            act_weight = (v.is_remind or is_act) and 0 or 10000
        end

        local item_cfg = ItemWGData.Instance:GetItemConfig(v.active_stuff_id)
        color_weight = item_cfg and item_cfg.color * 100 or 0
        v.sort = act_weight + color_weight + v.index
    end

    -- 默认升序
    if is_sort_desc then
        SortTools.SortDesc(show_list, "sort")    
    else
        SortTools.SortAsc(show_list, "sort")       
    end

    return show_list
end

function NewAppearanceWGData:GetSortShowListByTabIndexAndItemColor(tab_index)
    local show_list = self:GetShowListByTabIndex(tab_index)

    local data_list = {}
    for k,v in pairs(show_list) do
        local item_cfg = ItemWGData.Instance:GetItemConfig(v.active_stuff_id)

        if item_cfg then
            local color = item_cfg.color

            if not data_list[color] then
                data_list[color] = {}
            end

            table.insert(data_list[color], v)
        end
    end

    if not IsEmptyTable(data_list) then
        for k, v in pairs(data_list) do
            if not IsEmptyTable(v) then
               for i, u in pairs(v) do
                local is_act = self:GetFashionIsAct(u.part_type, u.index)
                local act_weight = 1000000

                if not is_act and u.is_remind then
                    act_weight = 0
                elseif is_act or u.is_remind then
                    act_weight = 1000
                end

                -- local act_weight = (u.is_remind or is_act) and 0 or 100000

                local item_cfg = ItemWGData.Instance:GetItemConfig(u.active_stuff_id)
                local color_weight = item_cfg and item_cfg.color * 10000 or 0
                u.sort = act_weight + color_weight + u.index    
               end 
            end

            SortTools.SortAsc(v, "sort")
        end
    end

    return data_list
end

-- 时装显示限制
function NewAppearanceWGData:GetFashionItemIsCanShow(data)
	if not data then
		return false
	end

    local active_stuff_id = self:GetFashionItemId(data.part_type, data.index)
	local num = ItemWGData.Instance:GetItemNumInBagById(active_stuff_id)
	if num > 0 then
		return true
	end

	local is_act = self:GetFashionIsAct(data.part_type, data.index)
	if is_act then
		return true
	end

    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local role_level = RoleWGData.Instance:GetRoleLevel()
    if data.is_default_show == 1 then
        if data.condition_type == 2 then
			return open_day >= data.skynumber_show and role_level >= data.level_show
		else
			return open_day >= data.skynumber_show or role_level >= data.level_show
		end
    end

    return false
end

function NewAppearanceWGData:GetFashionSingleRemind(part_type, index)
    local fs_cfg = self:GetFashionCfgByIndex(part_type, index)
    if not fs_cfg then
        return false
    end

    local level = self:GetFashionLevel(part_type, index)
    if level <= 0 or fs_cfg.shizhuang_type == 2 then
        local zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
        local active_stuff_id = self:GetFashionItemId(part_type, index)
        local num = ItemWGData.Instance:GetItemNumInBagById(active_stuff_id)
        return num > 0 and zhuan >= fs_cfg.zhuanzhi_level
    else
        local level_cfg = self:GetFashionUpLevelCfg(part_type, index)
        if level_cfg and level < level_cfg.max_up_level then
            local num = ItemWGData.Instance:GetItemNumInBagById(level_cfg.stuff_id)
            local stuff_num = self:GetFashionUpLevelCostStuffNumCfg(part_type, index, level + 1)
            return num >= stuff_num
        end
    end

    return false
end

function NewAppearanceWGData:GetFashionRemind(tab_index)
    local tab_data = NEW_APPEARANCE_TAB_DATA[tab_index]
    if tab_data == nil then
        return 0
    end
    

    -- 判断功能开放
    local fun_name = tab_data.fun_name or FunName.NewAppearanceWGView
    if not FunOpen.Instance:GetFunIsOpened(fun_name) then
        return 0
    end

    local jump_index = tab_data.jump_index or tab_index
	local param_t = {open_param = tab_data.open_param}
    local guide_module_name = tab_data.guide_module_name or GuideModuleName.NewAppearanceWGView

    local show_list = self:GetShowListByTabIndex(tab_index)
    if show_list == nil then
        MainuiWGCtrl.Instance:InvateTip(tab_data.strengthen_type, 0)
        return 0
    end

    for k,v in pairs(show_list) do
        if v.is_remind then
            MainuiWGCtrl.Instance:InvateTip(tab_data.strengthen_type, 1, function ()
                FunOpen.Instance:OpenViewByName(guide_module_name, jump_index, param_t)
                return true
            end)

            return 1
        end
    end
    
    MainuiWGCtrl.Instance:InvateTip(tab_data.strengthen_type, 0)
    return 0
end

-- 统计时装关联物品
function NewAppearanceWGData:CalcFashionStuffIdList()
    self.fashion_stuff_id_list = {}
    self.fashion_show_level_list = {}
    local part_type, index
    for k,v in pairs(NEW_WARDROBE_FASHION_TYPE) do
        local tab_data = NEW_APPEARANCE_TAB_DATA[v]
        if tab_data then
            local is_shenbing = tab_data.is_shenbing

            -- 基础配置
            local index_list = self.fashion_map_cfg[tab_data.fashion_type] or {}
            for _, base_cfg in pairs(index_list) do
                self.fashion_show_level_list[base_cfg.level_show] = true
                if (not is_shenbing or (is_shenbing and base_cfg.shifoushengb == 1)) then
                    part_type = base_cfg.part_type
                    index = base_cfg.index
                    local active_stuff_id = self:GetFashionItemId(part_type, index)
                    self.fashion_stuff_id_list[active_stuff_id] = {remind_name = tab_data.remind_name, tab_index = v}
    
                    -- 升级配置
                    local level_cfg = self:GetFashionUpLevelCfg(part_type, index, 1)
                   if level_cfg then
                        self.fashion_stuff_id_list[level_cfg.stuff_id] = {remind_name = tab_data.remind_name, tab_index = v}
                   end
                end
            end
        end
    end

    for k,v in pairs(NEW_APPEARANCE_ZHUANGBAN_TAB_INDEX) do
        local tab_data = NEW_APPEARANCE_TAB_DATA[v]
        if tab_data then
            local is_shenbing = tab_data.is_shenbing

            -- 基础配置
            local index_list = self.fashion_map_cfg[tab_data.fashion_type] or {}
            for _, base_cfg in pairs(index_list) do
                self.fashion_show_level_list[base_cfg.level_show] = true
                if (not is_shenbing or (is_shenbing and base_cfg.shifoushengb == 1)) then
                    part_type = base_cfg.part_type
                    index = base_cfg.index
                    local active_stuff_id = self:GetFashionItemId(part_type, index)
                    self.fashion_stuff_id_list[active_stuff_id] = {remind_name = tab_data.remind_name, tab_index = v}
    
                    -- 升级配置
                    local level_cfg = self:GetFashionUpLevelCfg(part_type, index, 1)
                   if level_cfg then
                        self.fashion_stuff_id_list[level_cfg.stuff_id] = {remind_name = tab_data.remind_name, tab_index = v}
                   end
                end
            end
        end
    end
end

function NewAppearanceWGData:CheckFashionStuffRemind(item_id)
    local data = self.fashion_stuff_id_list[item_id]
    if data then
        self:ClearFashionShowList(data.tab_index)
        RemindManager.Instance:Fire(data.remind_name)
        return true
    end

    return false
end

-- 减少等级改变引起的红点刷新
function NewAppearanceWGData:CheckFashionLevelIsFireRemind(old_level, new_level)
    old_level = old_level or 0
    new_level = new_level or 0
    for k,v in pairs(self.fashion_show_level_list) do
        if k >= old_level and k <= new_level then
            return true
        end
    end

    return false
end

-- 获取某品质及以上时装激活的数量
function NewAppearanceWGData:GetActivaCountByQuality(quality)
    local count = 0
    local cfg = self.fashion_map_cfg[SHIZHUANG_TYPE.BODY] or {}
    for key, value in pairs(cfg) do
        if value.quality >= quality then
            if self:GetFashionIsAct(SHIZHUANG_TYPE.BODY, value.index) then
                count = count + 1
            end
        end
    end
    return count
end


--==================  时装  end =============================
--]]


----[[ 外观回收
function NewAppearanceWGData:GetAppearanceResloveItemCfg(item_id)
    return self.reslove_map_cfg[item_id]
end

function NewAppearanceWGData:GetAppearanceResloveLevelCfg(level)
    return self.reslove_level_map_cfg[level]
end

function NewAppearanceWGData:GetAppearanceResloveMaxLevel()
    local cfg = self.reslove_level_map_cfg[#self.reslove_level_map_cfg]
    return cfg and cfg.level or 0
end

-- 回收信息
function NewAppearanceWGData:SetAppearanceResloveInfo(protocol)
    self.appearance_reslove_info = {}
	self.appearance_reslove_info.melt_level = protocol.melt_level
	self.appearance_reslove_info.melt_exp = protocol.melt_exp
end

function NewAppearanceWGData:GetAppearanceResloveInfo()
    return self.appearance_reslove_info
end

-- 回收 - 分解列表
function NewAppearanceWGData:GetAppearanceResloveBagList()
	local bag_data = {}
	local bag_stuff_list = ItemWGData.Instance:GetBagItemDataList()
	for k,v in pairs(bag_stuff_list) do
		if self:GetAppearanceResloveItemCfg(v.item_id) and self:GetAppearanceIsMaxStarByItemId(v.item_id) then
			table.insert(bag_data, v)
		end
	end

	return bag_data
end
--]]

function NewAppearanceWGData:GetAppearanceIsMaxStarByItemId(item_id)
    -- 时装
    local cfg = self:GetFashionCfgByItemId(item_id)
    if cfg then
        local level = self:GetFashionLevel(cfg.part_type, cfg.index)
        -- local uplevel_cfg = self:GetFashionUpLevelCfgById(item_id, level + 1)
        local uplevel_cfg = self:GetFashionUpLevelCfgById(item_id)

        if level >= uplevel_cfg.max_up_level then
            return true
        end

        -- if uplevel_cfg == nil then
        --     return true
        -- end
    end

    -- 骑宠
    local qc_type, image_id = self:GetQiChongTypeByItemId(item_id)
    if qc_type >= 0 then
        local star_level = self:GetSpecialQiChongStarLevel(qc_type, image_id)
        local max_level = self:GetSpecialQiChongMaxStarLevel(qc_type, image_id)

        if star_level >= max_level then
            return true
        end
    end

    return false
end

-- 把类型转换成天道石使用的类型
function NewAppearanceWGData:TransformAdvancedTypeToCharmType(type)
    if type == ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_WING then
        return CHARM_RATE_TYPE.WING --仙翼进阶
    elseif type == ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_FABAO then
        return CHARM_RATE_TYPE.FABAO -- 法宝进阶
    elseif type == ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_SHENBING then
        return CHARM_RATE_TYPE.SEHNBING -- 武器进阶
    elseif type == ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_JIANZHEN then
        return CHARM_RATE_TYPE.JIANZHEN  --背饰进阶
    end
    return nil
end

-- 把类型转换成天道石使用的类型
function NewAppearanceWGData:TransformQiChongTypeToCharmType(type)
    if type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        return CHARM_RATE_TYPE.LINGCHONG -- 灵剑进阶
    elseif type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
        return CHARM_RATE_TYPE.Mount -- 灵骑进阶
    end
    return nil
end

function NewAppearanceWGData:GetModelIsShowAllSuit(resouce)
    return self.show_all_suit_cfg[resouce] ~= nil
end

function NewAppearanceWGData:CalAttrCfgPack(target_data_list, target_attr_name)
    local data_list = {}

    for k, v in pairs(target_data_list) do
        data_list[k] = data_list[k] or {}

        for index, data in pairs(v) do
            for i = data.min_star, data.max_star do
                data_list[k][i] = data[target_attr_name]
            end
        end
    end

    return data_list
end

function NewAppearanceWGData:CalcFashionAttrCapWithAddPer(attr_list, percent)
    local cap_val = 0
    if percent and (not IsEmptyTable(attr_list)) then
        if percent > 0 then
            if attr_list then
                local attr = AttributeMgr.MulAttribute(attr_list, percent)
                cap_val = AttributeMgr.GetCapability(attr)
            end
        end
    end
    return cap_val
end