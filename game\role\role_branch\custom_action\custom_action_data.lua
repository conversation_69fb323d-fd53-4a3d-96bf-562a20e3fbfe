CustomActionData = CustomActionData or BaseClass()
function CustomActionData:__init()
	if CustomActionData.Instance then
		error("[CustomActionData] Attempt to create singleton twice!")
		return
	end

	CustomActionData.Instance = self

    self.action_list = {}
    self.action_show_list = {}
    self:InitConfig()
    RemindManager.Instance:Register(RemindName.CustomAction, BindTool.Bind(self.GetActionRemind, self))
end

function CustomActionData:__delete()
    CustomActionData.Instance = nil
    RemindManager.Instance:UnRegister(RemindName.CustomAction)
end

function CustomActionData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("role_action_cfg_auto")
    self.action_other_cfg = cfg.other[1]
    self.action_type_cfg = cfg.action_type
    self.action_cfg = cfg.action
    self.action_level_map_cfg = ListToMap(cfg.action_level, "action_id", "level")

    self.action_cfg_item_id_map_cfg = {}
    for k,v in pairs(self.action_cfg) do
        self.action_cfg_item_id_map_cfg[v.item_id] = v
    end
end

function CustomActionData:GetActionOtherCfg()
    return self.action_other_cfg
end

function CustomActionData:GetActionTypeCfg()
    return self.action_type_cfg
end

function CustomActionData:GetActionCfg(action_id)
    return self.action_cfg[action_id]
end

function CustomActionData:GetActionCfgByItemID(item_id)
    return self.action_cfg_item_id_map_cfg[item_id]
end

function CustomActionData:GetActionLevelCfg(action_id, level)
    return (self.action_level_map_cfg[action_id] or {})[level]
end





function CustomActionData:SetRoleActionAllInfo(protocol)
    self.action_list = protocol.action_list
    self:UpdateActionShowList()
end

function CustomActionData:SetRoleActionInfo(protocol)
    self.action_list[protocol.action_id] = protocol.action_data
    self:UpdateActionShowList()
end

function CustomActionData:GetRoleActionData(action_id)
    return self.action_list[action_id]
end

function CustomActionData:GetRoleActionLevel(action_id)
    return (self.action_list[action_id] or {}).level or -1
end




function CustomActionData:UpdateActionShowList()
    local show_list = {}
    for action_type, v in ipairs(self.action_type_cfg) do
        local action_type_list = {}
        local big_remind = false
        for action_id, cfg in pairs(self.action_cfg) do
            if action_type == cfg.action_type and self:GetActionIsCanShow(cfg) then
                local is_remind = self:GetSingleActionRemind(action_id)
                if is_remind then
                    big_remind = true
                end
                
                local data = {
                    action_id = action_id,
                    sort = action_id,
                    is_remind = is_remind,
                    cfg = cfg,
                }
                table.insert(action_type_list, data)
            end
        end

        if #action_type_list > 0 then
            SortTools.SortAsc(action_type_list, "action_id")
            local data = {
                cfg = v,
                is_remind = big_remind,
                child_list = action_type_list,
            }
            table.insert(show_list, data)
        end
    end

    self.action_show_list = show_list
end

function CustomActionData:GetActionShowList()
    return self.action_show_list
end

function CustomActionData:GetActionIsCanShow(cfg)
	if not cfg then
		return false
	end

    if cfg.is_stay_tuned == 1 then
        return true
    end

	local num = ItemWGData.Instance:GetItemNumInBagById(cfg.item_id)
	if num >= cfg.item_num then
		return true
	end

	local level = self:GetRoleActionLevel(cfg.action_id)
	if level >= 0 then
		return true
	end

    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local role_level = RoleWGData.Instance:GetRoleLevel()
    if cfg.is_default_show == 1 then
        if cfg.condition_type == 2 then
			return open_day >= cfg.skynumber_show and role_level >= cfg.level_show
		else
			return open_day >= cfg.skynumber_show or role_level >= cfg.level_show
		end
    end

    return false
end

function CustomActionData:GetSingleActionRemind(action_id)
    local level = self:GetRoleActionLevel(action_id)
	if level < 0 then
		local cfg = self:GetActionCfg(action_id)
        if cfg ~= nil then
            local num = ItemWGData.Instance:GetItemNumInBagById(cfg.item_id)
            if num >= cfg.item_num then
                return true
            end
        end
    else
        local next_cfg = self:GetActionLevelCfg(action_id, level + 1)
        if not next_cfg then
            return false
        end

        local cfg = self:GetActionLevelCfg(action_id, level)
        if cfg ~= nil then
            local num = ItemWGData.Instance:GetItemNumInBagById(cfg.item_id)
            if num >= cfg.item_num then
                return true
            end
        end
	end

    return false
end

function CustomActionData:GetActionRemind()
    for action_type, action_type_data in pairs(self.action_show_list) do
        if action_type_data.is_remind then
            return 1
        end
    end

    return 0
end

-- 获取所有属性
function CustomActionData:GetActionAllAttrList()
    local attr_data_list = {}
    local function add_attr_info(attr_data)
        local attr_id, attr_value = 0, 0
        local data_len = 6
    
        for i = 1, data_len do
            attr_id = attr_data["attr_id" .. i]
            attr_value = attr_data["attr_value" .. i]
            if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
                if not attr_data_list[attr_id] then
                    attr_data_list[attr_id] = attr_value
                else
                    attr_data_list[attr_id] = attr_data_list[attr_id] + attr_value
                end
            end
        end
    end

    for k,v in pairs(self.action_cfg) do
        local level = self:GetRoleActionLevel(v.action_id)
        local cfg = self:GetActionLevelCfg(v.action_id, level)
        if cfg ~= nil then
            add_attr_info(cfg)
        end
    end

    local attr_list = {}
    for k,v in pairs(attr_data_list) do
        local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(k)
        local data = {
            attr_str = attr_str,
            attr_value = v,
            attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
        }

        table.insert(attr_list, data)
    end

	if not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

    return attr_list
end

function CustomActionData:GetActionCapality(action_id)
    local level = self:GetRoleActionLevel(action_id)
    if level < 0 then
        return 0
    end

    local cfg = self:GetActionLevelCfg(action_id, level)
    if not cfg then
        return 0
    end

	local attribute = AttributePool.AllocAttribute()
	for i = 1, 6 do
		local attr_id = cfg["attr_id" .. i] or 0
		local attr_value = cfg["attr_value" .. i] or 0
		if attr_id > 0 and attr_value > 0 then
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
			if attribute[attr_str] then
				attribute[attr_str] = attribute[attr_str] + attr_value
			end
		end
	end

	local capability = AttributeMgr.GetCapability(attribute)
	return capability
end