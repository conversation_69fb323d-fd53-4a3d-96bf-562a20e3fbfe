TipsBuyItemView = TipsBuyItemView or BaseClass(SafeBaseView)

function TipsBuyItemView:__init(view_name)
	self.view_name = "TipsBuyItemView"
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(false, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_buy_item_tips_panel")
end

function TipsBuyItemView:LoadCallBack()
	self.node_list.title_view_name.text.text = self.data.title_view_name or Language.Common.BuyItemTipsTitle

	XUI.AddClickEventListener(self.node_list.btn_cancel, BindTool.Bind1(self.ListenCancelCallBack, self))
	XUI.AddClickEventListener(self.node_list.btn_ok, BindTool.Bind1(self.ListenOkCallBack, self))
	XUI.AddClickEventListener(self.node_list.layout_nolonger_tips, BindTool.Bind1(self.NoLongerClick, self))

	self.item_cell = ItemCell.New(self.node_list.item_cell)
end

function TipsBuyItemView:ReleaseCallBack()
	if self.ok_cb then
		self.ok_cb = nil
	end

	if self.cancel_cb then
		self.cancel_cb = nil
	end

	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

	self.data = nil
	self.check_status = nil
end

function TipsBuyItemView:SetData(info, Ok_CB, Cancel_CB)
	self.data = info
	self.ok_cb = Ok_CB
	self.cancel_cb = Cancel_CB

	self:CheckOpenTips()
end

-- 检测提示框
function TipsBuyItemView:CheckOpenTips()
	if self.data == nil then
		return
	end

	if self.data.has_checkbox then
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid --存储用uid
		local str_day = string.format("%s/%s", self.data.checkbox_str, main_role_id)
		local str_check_box = string.format("%s/%s_checkbox", self.data.checkbox_str, main_role_id)
		local save_day = PlayerPrefsUtil.GetInt(str_day, 0)
		local check_status = PlayerPrefsUtil.GetInt(str_check_box, 0)
		self.check_status = check_status
		-- 当前天数等于保存天数，且勾选今日不再提示
		if open_day == save_day and check_status == 1 then
			self:ExecuteOkFunc()
			return
		else
			PlayerPrefsUtil.SetInt(str_check_box, 0)
		end
	end

	self:Open()
end

function TipsBuyItemView:ExecuteOkFunc()
	-- 物品个数
	local need_num = self.data.expend_item_num or 0
	-- 已拥有物品个数
	local has_num = ItemWGData.Instance:GetItemNumInBagById(self.data.expend_item_id) --拥有的材料数量
	local is_enough = has_num >= need_num

	if is_enough then
		if self.ok_cb then
			self.ok_cb()
		end

		return true
	else
		local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(self.data.expend_item_id)
		local item_name = item_cfg and item_cfg.name or ""
		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.Common.ItemNoEnough, item_name))
	end

	return false
end

function TipsBuyItemView:ExecuteCancelFunc()
	if self.cancel_cb then
		self.cancel_cb()
	end
end

--[[
local tips_data = {
	title_view_name,		-- 弹窗标题（默认-购买提示）
	item_id,				-- 物品ID
	expend_item_id,			-- 需要消耗的物品ID
	expend_item_num,		-- 需要消耗的物品数量（单价）
	has_checkbox,			-- 是否有提示今日弹出勾选框
	checkbox_str,			-- 今日弹出勾选复选框唯一识别字段
}
--]]
function TipsBuyItemView:OnFlush()
	if self.data == nil then
		return
	end

	-- 物品个数
	local need_num = self.data.expend_item_num or 0
	local need_num_str = ToColorStr(need_num, COLOR3B.GREEN)
	-- 已拥有物品个数
	local has_num = ItemWGData.Instance:GetItemNumInBagById(self.data.expend_item_id) --拥有的材料数量
	local is_enough = has_num >= need_num
	-- 物品配置
	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	local item_name = item_cfg and item_cfg.name or ""
	local item_color = item_cfg and item_cfg.color or ""
	local name_str = ToColorStr(item_name, ITEM_COLOR[item_color])

	local ex_item_cfg, ex_item_type = ItemWGData.Instance:GetItemConfig(self.data.expend_item_id)
	local ex_item_name = ex_item_cfg and ex_item_cfg.name or ""
	local ex_item_color = ex_item_cfg and ex_item_cfg.color or ""
	local ex_name_str = ToColorStr(ex_item_name, ITEM_COLOR[ex_item_color])

	self.node_list.stuff_obj_tips.text.text = string.format(Language.Common.BuyItemTips, need_num_str, ex_name_str,
		name_str)

	self.item_cell:SetData({ item_id = self.data.item_id })

	self.node_list.spend_root:CustomSetActive(not is_enough)
	self.node_list.layout_nolonger_tips:CustomSetActive(self.data.has_checkbox)

	if self.data.has_checkbox then
		self:FlushNoLonger()
	end
end

-- 刷新复选框
function TipsBuyItemView:FlushNoLonger()
	self.check_status = self.check_status or 0
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid --存储用uid
	local str_check_box = string.format("%s/%s_checkbox", self.data.checkbox_str, main_role_id)
	PlayerPrefsUtil.SetInt(str_check_box, self.check_status)
	self.node_list.img_nohint_hook:CustomSetActive(self.check_status == 1)

	if self.check_status == 1 then
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local str_day = string.format("%s/%s", self.data.checkbox_str, main_role_id)
		PlayerPrefsUtil.SetInt(str_day, open_day)
	end
end

------------------------------------------------------------------------------------------
-- 取消点击
function TipsBuyItemView:ListenCancelCallBack()
	self:ExecuteCancelFunc()
	self:Close()
end

-- 确认点击
function TipsBuyItemView:ListenOkCallBack()
	if self:ExecuteOkFunc() then
		self:Close()
	end
end

function TipsBuyItemView:NoLongerClick()
	self.check_status = self.check_status == 0 and 1 or 0
	self:FlushNoLonger()
end
