SocietyRemoveView = SocietyRemoveView or BaseClass(SafeBaseView)

function SocietyRemoveView:__init()
	-- self:SetModal(true)
	self:AddViewResource(0, "uis/view/society_ui_prefab", "layout_addremove")

	self.flush_checkbox1 = true
	self.flush_checkbox2 = true
	self.flush_checkbox3 = true

	self.delnamelist = {}
end

function SocietyRemoveView:__delete()
	self.delnamelist = {}
end

function SocietyRemoveView:ReleaseCallBack()
	self.flush_checkbox1 = true
	self.flush_checkbox2 = true
	self.flush_checkbox3 = true
end

function SocietyRemoveView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_intimacy"], BindTool.Bind2(self.OnBtnHandler, self, 1))
	XUI.AddClickEventListener(self.node_list["btn_level"], BindTool.Bind2(self.OnBtn<PERSON><PERSON><PERSON>, self, 2))
	XUI.AddClickEventListener(self.node_list["btn_day"], BindTool.Bind2(self.OnBtnHand<PERSON>, self, 3))
	XUI.AddClickEventListener(self.node_list["btn_auto_clear"], BindTool.Bind1(self.OnBtnAutoClear, self))
	self.node_list["tog_check1"].toggle:AddValueChangedListener(BindTool.Bind2(self.FlushCheckBox, self, 1))
	self.node_list["tog_check2"].toggle:AddValueChangedListener(BindTool.Bind2(self.FlushCheckBox, self, 2))
	self.node_list["tog_check3"].toggle:AddValueChangedListener(BindTool.Bind2(self.FlushCheckBox, self, 3))
	
	self.intimacy_keyboard = NumKeypad.New()
	self.intimacy_keyboard:SetOkCallBack(BindTool.Bind1(self.OnClickEnterIntimacy, self))
	self.intimacy_keyboard:SetMaxValue(99999)

	self.level_keyboard = NumKeypad.New()
	self.level_keyboard:SetOkCallBack(BindTool.Bind1(self.OnClickEnterLevel, self))
	self.level_keyboard:SetMaxValue(1000)

	self.day_keyboard = NumKeypad.New()
	self.day_keyboard:SetOkCallBack(BindTool.Bind1(self.OnClickEnterDay, self))
	self.day_keyboard:SetMaxValue(100)
end

function SocietyRemoveView:CloseCallBack()
	
end

function SocietyRemoveView:ShowIndexCallBack()
	-- self.node_list.label_intimacy.text.text = "1"
	-- self.node_list.label_level.text.text = "1"
	-- self.node_list.label_day.text.text = "1"
end

function SocietyRemoveView:OnFlush()
	
end

function SocietyRemoveView:OnBtnHandler(paarm)
	if paarm == 1 then
		self.intimacy_keyboard:Open()
	elseif paarm == 2 then
		self.level_keyboard:Open()
	elseif paarm == 3 then
		self.day_keyboard:Open()
	end
end

-- 点击确定亲密度
function SocietyRemoveView:OnClickEnterIntimacy(num)
	if nil ~= num and 0 ~= num then
		self.node_list["label_intimacy"].text.text = num
	end
end

-- 点击确定等级数
function SocietyRemoveView:OnClickEnterLevel(num)
	if nil ~= num and 0 ~= num then 
		self.node_list["label_level"].text.text = num
	end
end

-- 点击确定离开天数
function SocietyRemoveView:OnClickEnterDay(num)
	if nil ~= num and 0 ~= num then 
		self.node_list["label_day"].text.text = num
	end
end

function SocietyRemoveView:OnBtnAutoClear()
	-- print_error("OnBtnAutoClear------------------")
	local friend_list = SocietyWGData.Instance:GetFriendList()
	local intimacy_text = self.node_list["label_intimacy"].text.text
	local level_text = self.node_list["label_level"].text.text
	local day_text = self.node_list["label_day"].text.text


	local delnamelist = {}

	if self.flush_checkbox1 == false and self.flush_checkbox2 == false and self.flush_checkbox3 == false then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.IsSelectNull)
		return
	end
	if friend_list == nil then return end
	print_error("can alert-----")
	-- 弹出二次确认提示框
	if self.alert_window == nil then
		self.alert_window = Alert.New()
	end
	self.alert_window:SetLableString(Language.Society.IsRemover)
	self.alert_window:SetOkFunc(BindTool.Bind1(function ()
		for k, v in pairs(friend_list) do
			local flag = true

			if self.flush_checkbox1 and v.intimacy >= tonumber(intimacy_text) then
				flag = false
			end
			if self.flush_checkbox2 and v.level >= tonumber(level_text) then
				flag = false
			end

			local day_time = v.last_logout_timestamp
	 		local count_time = math.floor(math.max((TimeWGCtrl.Instance:GetServerTime() - day_time)/(24 * 3600), 0))
			if self.flush_checkbox3 and (count_time < tonumber(day_text) or day_time == 0) then
				flag = false
			end

			if flag then
				delnamelist[#delnamelist + 1] = v.user_id
			end
		end
		for _,v in pairs(delnamelist) do
			SocietyWGCtrl.Instance:DeleteFriend(v)
		end
	end, self))
	self.alert_window:Open()

end

function SocietyRemoveView:FlushCheckBox(index, isOn)
	self["flush_checkbox"..index] = isOn
end

