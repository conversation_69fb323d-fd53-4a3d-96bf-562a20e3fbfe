
BossStoneObj = BossStoneObj or BaseClass(SceneObj)

function BossStoneObj:__init(stone_vo)
	self.obj_type = SceneObjType.BossStoneObj
	self.draw_obj:SetObjType(self.obj_type)
	self.last_check_time = 0
	self.res_id = "7223"
	self.name_text = ""
	self.follow_ui_visible = false
end

function BossStoneObj:__delete()
	self.name_text = nil
end

function BossStoneObj:InitAppearance()
	self:ChangeModel(SceneObjPart.Main, ResPath.GetGatherModel(self.res_id))
	self:CreateFollowUi()
    local point = self.draw_obj:GetAttachPoint(AttachPoint.UI)
    self.follow_ui:SetFollowTarget(point, self.draw_obj:GetName())
	local monster_data =  ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.vo.boss_id]

	if not monster_data then return end

	self.name_text = ToColorStr(monster_data.name .." Lv.".. monster_data.level,COLOR3B.YELLOW)
	self.follow_ui_visible = true
end

function BossStoneObj:GetVo()
	return self.vo
end

function BossStoneObj:Update(now_time, elapse_time)
	SceneObj.Update(self, now_time, elapse_time)

	if self.vo.live_time == nil then
		self.vo.live_time = 0
	end

	local sever_time = TimeWGCtrl.Instance:GetServerTime()
	if now_time >= self.last_check_time + 0.2 and self.vo.live_time > sever_time then
		self.last_check_time = now_time

		if self:CheckRangeByMainRole() == false then
			if self.follow_ui_visible == false then return end
			self.follow_ui_visible = false
			if self.follow_ui then
				self.follow_ui:ForceSetVisible(false)
			end
			return
		end

		if self.follow_ui_visible == false then
			self.follow_ui_visible = true
			if self.follow_ui then
				self.follow_ui:ForceSetVisible(true)
			end
		end
		local time = self.vo.live_time - sever_time
		if time > 3600 then
			time = TimeUtil.FormatSecond(time)
		else
			time = TimeUtil.FormatSecond(time, 2)
		end
		local time_text = time .. Language.Boss.LiveTime
		if Scene.Instance:GetSceneType() == SceneType.ZhuXie then
			self.vo.name = ToColorStr(self.name_text)
		else
			self.vo.name = ToColorStr(self.name_text .. '\n' .. time_text ,COLOR3B.RED)
		end
		self:ReloadUIName()

	elseif self.vo.live_time - 1 <= sever_time then
		if self.parent_scene then
			self.parent_scene:DeleteObj(self.vo.obj_id,0)
		end
	end
end

function BossStoneObj:UpdateSpriteFrame()

end

-- 刷新动画
function BossStoneObj:RefreshAnimation()

end

function BossStoneObj:OnClick()
	SceneObj.OnClick(self)
end

function BossStoneObj:SetBossStoneInfo(live_time)
	self.vo.live_time = live_time
end

function BossStoneObj:CheckRangeByMainRole()
	local role = Scene.Instance:GetMainRole()
	if role == nil then
		return false
	end
	local role_x,role_y = role:GetLogicPos()
	local self_x,self_y = self:GetLogicPos()
	if math.abs(role_x - self_x) <= 15 and math.abs(role_y - self_y) <= 15 then
		return true
	end
	return false
end
--主动销毁墓碑
function BossStoneObj:DeleteBossStone()
	if self.parent_scene then
		self.parent_scene:DeleteObj(self.vo.obj_id,0)
	end
end

function BossStoneObj:IsBossStone()
	return true
end