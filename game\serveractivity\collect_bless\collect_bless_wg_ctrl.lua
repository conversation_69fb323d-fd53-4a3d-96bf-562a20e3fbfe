require("game/serveractivity/collect_bless/collect_bless_wg_data")
require("game/serveractivity/collect_bless/collect_bless_view")

CollectBlessWGCtrl = CollectBlessWGCtrl or BaseClass(BaseWGCtrl)

function CollectBlessWGCtrl:__init()
	if CollectBlessWGCtrl.Instance ~= nil then
		print("[CollectBlessWGCtrl]error:create a singleton twice")
	end
	CollectBlessWGCtrl.Instance = self

	self.view = CollectBlessView.New()
	self.data = CollectBlessWGData.New()

	self:RegisterAllProtocols()
end

function CollectBlessWGCtrl:__delete()
	if nil ~= self.view then
		self.view:DeleteMe()
	end

	if nil ~= self.show_view then
		self.show_view:DeleteMe()
	end

	if nil ~= self.data then
		self.data:DeleteMe()
	end

	CollectBlessWGCtrl.Instance = nil
end

function CollectBlessWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCRACollectyBlessingInfo, "OnSCRACollectyBlessingInfo")

	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))
end

function CollectBlessWGCtrl:OnSCRACollectyBlessingInfo(protocol)
	self.data:SetCollectyBlessingInfo(protocol)
	self.view:Flush()
end

function CollectBlessWGCtrl:Open()
	self.view:Open()
end

function CollectBlessWGCtrl:MainuiOpenCreate()
	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_COLLECTBLESS) then
		return
	end
	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_COLLECTBLESS,
		opera_type = RA_COLLECT_BLESSING_OPEAR_TYPE.RA_COLLECT_BLESSING_OPERA_TYPE_QUERY_INFO,
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end