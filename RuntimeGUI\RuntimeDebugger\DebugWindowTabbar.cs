﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class DebugWindowTabbar: IDebugWindow
{
    private List<KeyValuePair<string, IDebugWindow>> debugWindows;
    private int selectIndex = 0;
    private string[] debuggerWindowNames;
    private bool isVaild = false;

    public DebugWindowTabbar()
    { 
    }
    
    public void Init()
    {
        debugWindows = new List<KeyValuePair<string, IDebugWindow>>();
        selectIndex = 0;
        debuggerWindowNames = null;
        isVaild = true;
    }
    
    public void Destroy()
    {    
        isVaild = false;
        if (debugWindows != null)
        {
            foreach (var valuePair in debugWindows)
            {
                valuePair.Value.Destroy();
            }
            debugWindows.Clear();
        }
        
        selectIndex = 0;
        debuggerWindowNames = null;
    }

    public IDebugWindow SelectWindow
    {
        get
        {
            if (!isVaild)
            {
                return null;
            }
            if (selectIndex >= debugWindows.Count)
            {
                return null;
            }
            return debugWindows[selectIndex].Value;
        }
    }

    public int SelectIndex
    {
        get { return selectIndex; }
        set { selectIndex = value; }
    }

    public string[] GetDebuggerWindowNames()
    {
        return debuggerWindowNames;
    }

    private void RefreshDebuggerWindowNames()
    {
        debuggerWindowNames = new string[debugWindows.Count];
        int index = 0;
        foreach (KeyValuePair<string, IDebugWindow> debuggerWindow in debugWindows)
        {
            debuggerWindowNames[index++] = debuggerWindow.Key;
        }
    }

    public void RegisterWindow(string name, IDebugWindow window)
    {    
        if (!isVaild)
        {
            Debug.LogError("[DebugWindowTabbar] is invalid.");
            return;
        }
        
        if (string.IsNullOrEmpty(name))
        {
            throw new Exception("name is invalid.");
        }

        int pos = name.IndexOf('/');
        if (pos < 0 || pos >= name.Length - 1)
        {
            if (InternalGetDebuggerWindow(name) != null)
            {
                throw new Exception("重复注册窗口！ " + name);
            }
            debugWindows.Add(new KeyValuePair<string, IDebugWindow>(name, window)); 
            RefreshDebuggerWindowNames();
        }
        else
        {
            string debuggerWindowGroupName = name.Substring(0, pos);
            string leftPath = name.Substring(pos + 1);
            DebugWindowTabbar debuggerWindowGroup = (DebugWindowTabbar)InternalGetDebuggerWindow(debuggerWindowGroupName);
            if (debuggerWindowGroup == null)
            {
                if (InternalGetDebuggerWindow(debuggerWindowGroupName) != null)
                {
                    throw new Exception("重复注册窗口！ " + debuggerWindowGroupName);
                }

                debuggerWindowGroup = new DebugWindowTabbar();
                debuggerWindowGroup.Init();
                debugWindows.Add(new KeyValuePair<string, IDebugWindow>(debuggerWindowGroupName, debuggerWindowGroup));
                RefreshDebuggerWindowNames();
            }

            debuggerWindowGroup.RegisterWindow(leftPath, window);
        }
    }
    
    private IDebugWindow InternalGetDebuggerWindow(string name)
    {
        foreach (KeyValuePair<string, IDebugWindow> debuggerWindow in debugWindows)
        {
            if (debuggerWindow.Key == name)
            {
                return debuggerWindow.Value;
            }
        }

        return null;
    }

    private bool InternalSelectDebuggerWindow(string name)
    {
        for (int i = 0; i < debugWindows.Count; i++)
        {
            if (debugWindows[i].Key == name)
            {
                selectIndex = i;
                return true;
            }
        }

        return false;
    }

    public void OnEnter()
    {
    }

    public void OnLeave()
    {
    }

    public void OnDraw()
    {
    }

    public void Update()
    {
        if (!isVaild)
        {
            return;
        }
        if (SelectWindow != null)
        {
            SelectWindow.Update();
        }
    }
}