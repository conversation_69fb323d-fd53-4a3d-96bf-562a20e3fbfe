KuafuYeZhanWangChengWGData = KuafuYeZhanWangChengWGData or BaseClass()
KuafuYeZhanWangChengWGData.NotifyReason = {
	Update = 0,
	Reward = 1
}
-- 阵营类型
KuafuYeZhanWangChengWGData.SideType = {
	Blue = 1,
	Red = 2,
	Yellow = 3,
}

function KuafuYeZhanWangChengWGData:__init()
	if KuafuYeZhanWangChengWGData.Instance then
		ErrorLog("[KuafuYeZhanWangChengWGData] attempt to create singleton twice!")
		return
	end
	KuafuYeZhanWangChengWGData.Instance =self
    self.turn = -1
    self.rank_count = 0
	self.all_rank_info = {}

	local cfg = ConfigManager.Instance:GetAutoConfig("nightfightfb_auto")
	self.other_cfg = cfg.other[1]
	self.camp_reward_cfg = ListToMap(cfg.result_reward, "turn")
	self.count_reward_cfg = cfg.count_reward
	self.guaji_pos_cfg = cfg.find_road or {}
	self.buff_reflush_pos_cfg = ListToMap(cfg.buff_reflush_pos, "seq")
	self.count_reward_data_list = {}

	self.buff_refresh_time = 0
	self.get_buff_time = 0
	self.tianxuan_buff_role_uid = 0
	self.role_head_data = {}
	self.buff_pos_x = 0
	self.buff_pos_y = 0

	-- 红点注册
	RemindManager.Instance:Register(RemindName.YeZhanWangCheng, BindTool.Bind(self.GetYeZhanWangChengRed,self))
end

function KuafuYeZhanWangChengWGData:__delete()
	KuafuYeZhanWangChengWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.YeZhanWangCheng)
end

function KuafuYeZhanWangChengWGData:GetLimitLevel()
	return self.other_cfg.limit_level
end

function KuafuYeZhanWangChengWGData:GetJieMianReward()
	local cfg = ConfigManager.Instance:GetAutoConfig("nightfightfb_auto").other
	if nil == cfg or nil == cfg[1] then return end
	local tab =	cfg[1].reward_item

	return tab
end

function KuafuYeZhanWangChengWGData:GetPersonalReward()
	local cfg = ConfigManager.Instance:GetAutoConfig("nightfightfb_auto").score_reward
	local turn = self.turn > 1 and self.turn or 3
	local list = {}
	for i, v in ipairs(cfg) do
		if v.turn == turn then
			table.insert(list, v)
		end
	end
	return list
end

function KuafuYeZhanWangChengWGData:GetCampReward()
	local cfg = ConfigManager.Instance:GetAutoConfig("nightfightfb_auto").result_reward
	local turn = self.turn > 1 and self.turn or 3
	local t = {}
	for i, v in pairs(cfg) do
		if v.turn == turn then
			table.insert(t, v.first_reward_item)
			table.insert(t, v.second_reward_item)
			table.insert(t, v.third_reward_item)
		end
	end
	if t[1] then
		t[1].is_win = 1
	end
	return t
end

-- 次数奖励
function KuafuYeZhanWangChengWGData:GetCountRewardDataList()
	return self.count_reward_data_list
end

-- 参与次数
function KuafuYeZhanWangChengWGData:GetYZWCEnterCount()
	return self.enter_count or 0
end

function KuafuYeZhanWangChengWGData:SetSCCrossNightFightInfo(protocol)
	self.enter_count = protocol.enter_count
	local data_list = {}
	for k, v in pairs(self.count_reward_cfg) do
		local data = {}
		data.index = k
		data.cfg = v
		data.reward_flag = protocol.count_reward_flag[k]
		data.can_fetch = data.reward_flag ~= 1 and self.enter_count >= v.battle_count
		table.insert(data_list, data)
	end
	table.sort(data_list, SortTools.KeyLowerSorter("index"))
	self.count_reward_data_list = data_list
end

function KuafuYeZhanWangChengWGData:NightFightSceneInfo(protocol)
	if protocol == nil then
		return self.scene_info or {}
	end
	if not self.scene_info then
		self.scene_info = {}
	end
	self.scene_info.next_redistribute_time = protocol.next_redistribute_time
	self.scene_info.kick_out_time = protocol.kick_out_time
	self.scene_info.fight_start_time = protocol.fight_start_time
	self.scene_info.buff_seq = protocol.buff_seq
end

function KuafuYeZhanWangChengWGData:NightFightRankInfo(protocol)
	if protocol == nil then
		local t = {}
		t.rank_info_list = self.rank_info_list
		return t
	end
	self.rank_count = protocol.rank_count
	self.turn = protocol.turn > 3 and 3 or protocol.turn
	table.sort(protocol.rank_info_list, SortTools.KeyUpperSorter("score"))
	if protocol.rank_info_list[1] then
		protocol.rank_info_list[1].mvp = 1
	end
	local first_camp_mvp = protocol.rank_info_list[1] and protocol.rank_info_list[1].side_type or 1
	for i, v in pairs(protocol.rank_info_list) do
		v.rank = i
		if first_camp_mvp and protocol.notify_reason ~= KuafuYeZhanWangChengWGData.NotifyReason.Update then
			if v.side_type ~= first_camp_mvp then
				v.mvp = 1
				first_camp_mvp = nil
			end
		end
		local head_key = v.user_key.temp_low .. v.user_key.temp_high
		AvatarManager.Instance:SetAvatarKey(head_key, v.avatar_key_big, v.avatar_key_small)
	end

	self.side_info_list = protocol.side_info_list
	self.rank_info_list = protocol.rank_info_list
	-- self.red_score = protocol.red_score
	-- self.blue_score = protocol.blue_score
	-- self.red_team_count = protocol.red_team_count
	-- self.blue_team_count = protocol.blue_team_count
	self.red_score = protocol.side_info_list[2].score
	self.blue_score = protocol.side_info_list[1].score
	self.yellow_score = protocol.side_info_list[3].score
	self.red_team_count = protocol.side_info_list[2].team_count
	self.blue_team_count = protocol.side_info_list[1].team_count
	self.yellow_team_count = protocol.side_info_list[3].team_count

	--客戶端临时处理结算数据
	if not IsEmptyTable(protocol.rank_info_list) then
		self.cur_reward_list = protocol.rank_info_list
		self.cur_turn_red_score = protocol.side_info_list[2].score --protocol.red_score
		self.cur_turn_blue_score = protocol.side_info_list[1].score --protocol.blue_score
		self.cur_turn_yellow_score = protocol.side_info_list[3].score
		self.all_rank_info[protocol.turn] = {}
		self.all_rank_info[protocol.turn].rank_info_list = protocol.rank_info_list
		self.all_rank_info[protocol.turn].red_score = protocol.side_info_list[2].score --protocol.red_score
		self.all_rank_info[protocol.turn].blue_score = protocol.side_info_list[1].score --protocol.blue_score
		self.all_rank_info[protocol.turn].yellow_score = protocol.side_info_list[3].score --protocol.blue_score
	end
end

function KuafuYeZhanWangChengWGData:GetResultInfoByStage(turn)
    if not self.all_rank_info[turn] then
        return {}
    end

	local list = self.all_rank_info[turn].rank_info_list

    local camp_score_table = {
        { ["side_type"] = KuafuYeZhanWangChengWGData.SideType.Blue, ["score"] = self.all_rank_info[turn].blue_score },
        { ["side_type"] = KuafuYeZhanWangChengWGData.SideType.Yellow, ["score"] = self.all_rank_info[turn].yellow_score },
		{ ["side_type"] = KuafuYeZhanWangChengWGData.SideType.Red, ["score"] = self.all_rank_info[turn].red_score },
    }
    table.sort(camp_score_table, SortTools.KeyUpperSorter("score"))
	--local blue_score = self.all_rank_info[turn].blue_score
	--local red_score = self.all_rank_info[turn].red_score
	--local red_win = red_score > blue_score 								-- 是否红方胜利

    local reward_list = self:GetZcResultReward(turn)  --个人积分排名奖励表

    for i, cfg in ipairs(reward_list) do
        local temp_list = {}
        for i = cfg.min_rank, cfg.max_rank do
            if IsEmptyTable(temp_list) then
                for i = 0, #cfg.reward_item do
                    table.insert(temp_list, cfg.reward_item[i])
                end
            end

            if list[i + 1] then
                list[i + 1].reward_list = temp_list

                -- 阵营奖励
                -- local self_is_win = false
                -- if list[i + 1].is_red_side == 1 then
                -- 	self_is_win = red_win
                -- else
                -- 	self_is_win = not red_win
                -- end
				local camp_rank = 0
				for index, value in ipairs(camp_score_table) do
					if list[i + 1].side_type == value["side_type"] then
                        camp_rank = index
						break
					end
				end

                local camp_reward = self:GetCampRewardCfg(turn, camp_rank)
                for k = 0, #camp_reward do
                    table.insert(list[i + 1].reward_list, camp_reward[k])
                end
                list[i + 1].reward_list = self:CombineItemList(list[i + 1].reward_list)
            else
                break
            end
        end
    end
    return list
end

-- 合并数组里相同的物品
function KuafuYeZhanWangChengWGData:CombineItemList(tab)
	local result_list = {}
	local temp_list = {}
	for i, v in ipairs(tab) do
		local index = temp_list[v.item_id]
		if not index then
			local data = {}
			for k, v in pairs(v) do 				-- 避免修改到配置表
				data[k] = v
			end
			table.insert(result_list, data)

			temp_list[v.item_id] = #result_list
		else
			result_list[index].num = result_list[index].num + v.num 
		end
	end
	return result_list
end

-- 阵营奖励配置
function KuafuYeZhanWangChengWGData:GetCampRewardCfg(turn, rank)
	local cfg = self.camp_reward_cfg[turn]
	if cfg == nil then
		return {}
	end
	if rank == 1 then
		return cfg.first_reward_item
	elseif rank == 2 then
		return cfg.second_reward_item
	else
		return cfg.third_reward_item
	end
end

function KuafuYeZhanWangChengWGData:GetCurRewardInfo()
	return self.cur_reward_list or {}
end

function KuafuYeZhanWangChengWGData:GetRewardRoleInfoByUserKey(user_key)
	if not self.rank_info_list then
		return {}
	end
	for i, v in ipairs(self.rank_info_list) do
		if v.user_key == user_key then
			return v
		end
	end
	return {}
end

function KuafuYeZhanWangChengWGData:GetCurTurn()
	return self.turn
end

function KuafuYeZhanWangChengWGData:GetResultScoreInfo(stage)
	local rank_info = self.all_rank_info[stage]
	local t = {}
	t.red_score = rank_info.red_score
	t.blue_score = rank_info.blue_score
	t.yellow_score = rank_info.yellow_score
	--[[
	local is_win
	local self_info = self:GetSetSelfInfo()
	if t.red_score == t.blue_score then
		is_win = 2
	else
		if self_info.is_red_side == 1 then
			is_win = t.red_score > t.blue_score and 1 or 0
		else
			is_win = t.red_score < t.blue_score and 1 or 0
		end
	end
	t.is_win = is_win
	]]
	return t
end

function KuafuYeZhanWangChengWGData:GetSetSelfInfo(info)
	if info == nil then
		return self.yewc_role_info or {}
	end
	self.yewc_role_info = info
end

function KuafuYeZhanWangChengWGData:GetSelfSideType()
	return self:GetSetSelfInfo().side_type
end

function KuafuYeZhanWangChengWGData:GetRoleInfoByUserKey(user_key)
	if not self.rank_info_list then
		return {}
	end
	for i, v in ipairs(self.rank_info_list) do
		if v.user_key == user_key then
			return v
		end
	end
	return {}
end

--获取阵营人数级阵营分数
function KuafuYeZhanWangChengWGData:GetNightFightSideListInfo()
	if not self.side_info_list then
		return {}
	end
	--[[local red_count = 0
	for i, v in pairs(self.rank_info_list) do
		if v.is_red_side == 1 then
			red_count = red_count + 1
		end
	end--]]

	-- local info = {}
	-- info.red_side_count = self.red_team_count
	-- info.red_score = self.red_score
	-- info.blue_score = self.blue_score
	-- info.blue_side_count = self.blue_team_count
	-- return info
	return self.side_info_list
end

function KuafuYeZhanWangChengWGData:SetNightFightAllRoleScoreInfo(protocol)
	if not self.score_info_list then
		self.score_info_list = {}
	end
	self.score_info_list[protocol.obj_id] = protocol
end

function KuafuYeZhanWangChengWGData:GetScoreByObjId(obj_id)
	return self.score_info_list[obj_id] or {}
end

function KuafuYeZhanWangChengWGData:ClearScore()
	self.score_info_list = {}
	self.yewc_role_info = {}
	self.turn = -1
	self.red_score = 0
	self.blue_score = 0
	self.red_team_count = 0
	self.blue_team_count = 0
	self.rank_info_list = {}
end

function KuafuYeZhanWangChengWGData:GetRedistributeTime()
	local cfg = ConfigManager.Instance:GetAutoConfig("nightfightfb_auto").other
	if cfg then
		return cfg[1].redistribute_interval_time_s, cfg[1].report_interval_s
	end
	return 0, 30
end

function KuafuYeZhanWangChengWGData:GetRoleTitle(mul_kill_num)
	if not self.title_cfg then
		self.title_cfg = ConfigManager.Instance:GetAutoConfig("nightfightfb_auto").multi_kill_title
	end
	local info = {}
	mul_kill_num = tonumber(mul_kill_num)
	for i, v in ipairs(self.title_cfg) do
		if mul_kill_num >= v.kill_num then
			info = v
		else
			break
		end
	end
	return info
end

function KuafuYeZhanWangChengWGData:GetTianXuanTitle()
	return self.other_cfg.tianxuan_title_res
end

function KuafuYeZhanWangChengWGData:GetTipsShowInfo()
	local cfg = ConfigManager.Instance:GetAutoConfig("nightfightfb_auto").other
	return cfg and cfg[1]
end

function KuafuYeZhanWangChengWGData:CheckActIsStart()
	local scene_type = Scene.Instance:GetSceneType()
	local scene_info = KuafuYeZhanWangChengWGData.Instance:NightFightSceneInfo()
	local act = IS_ON_CROSSSERVER and ACTIVITY_TYPE.KUAFUYEZHANWANGCHENG or ACTIVITY_TYPE.YEZHANWANGCHENG
	--local is_open = ActivityWGData.Instance:GetActivityIsOpen(act)
	local act_status = ActivityWGData.Instance:GetActivityStatuByType(act)
	local is_not_ready = act_status and act_status.status ~= ACTIVITY_STATUS.STANDY or false
	local cur_turn = self:GetCurTurn()
	return cur_turn > 0 and is_not_ready and scene_type == SceneType.YEZHANWANGCHENGFUBEN
end

function KuafuYeZhanWangChengWGData:GetZcResultReward(turn)
	local cfg = ConfigManager.Instance:GetAutoConfig("nightfightfb_auto").score_reward
	local list = {}
	for i, v in ipairs(cfg) do
		if v.turn == turn then
			table.insert(list, v)
		end
	end
	return list
end

--显示活动时间，又不用了
function KuafuYeZhanWangChengWGData:GetActTimeCfg(act_type)
	local cfg = ConfigManager.Instance:GetAutoConfig("nightfightfb_auto").other
	local time = 0
	local turn_time = cfg[1].redistribute_interval_time_s
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(act_type)
	local scene_info = KuafuYeZhanWangChengWGData.Instance:NightFightSceneInfo()

	if IsEmptyTable(scene_info) then
		return
	end

	if act_info.status == ACTIVITY_STATUS.STANDY then
		time = scene_info.fight_start_time + turn_time * 3--一次打3轮
	else
		local cur_turn = KuafuYeZhanWangChengWGData.Instance:GetCurTurn()
		cur_turn = cur_turn > 0 and cur_turn or 0
		time = scene_info.next_redistribute_time ~= 0 and scene_info.next_redistribute_time or scene_info.fight_start_time
		time = scene_info.next_redistribute_time + (3 - cur_turn) * turn_time
	end

	return cfg[1].delay_kick_out_time + time
end

function KuafuYeZhanWangChengWGData:GetGuajiPos(pos_x, pos_y)
	local delta_pos, dis
	local distance = 99999
	local target_pos = {find_postion = 0, fpos_x = pos_x, fpos_y = pos_y}
	for i, v in pairs(self.guaji_pos_cfg) do
		delta_pos = u3d.v2Sub(u3d.vec2(v.fpos_x, v.fpos_y), u3d.vec2(pos_x, pos_y))
		dis = u3d.v2Length(delta_pos)
		if dis < distance then
			target_pos = v
			distance = dis
		end
	end

	return target_pos
end

function KuafuYeZhanWangChengWGData:GetNextGuajiPos(last_pos)
	return self.guaji_pos_cfg[last_pos.find_postion + 1] or self.guaji_pos_cfg[1]
end

function KuafuYeZhanWangChengWGData:GetRamdomGuajiPos()
	local seq = 0
	if not IsEmptyTable(self.scene_info) then
		seq = self.scene_info.buff_seq
	end
	local cfg = self.buff_reflush_pos_cfg[self.buff_seq] or self.buff_reflush_pos_cfg[0]
	local random_x = cfg.pos_x + (math.random() * cfg.range * 2) - cfg.range
	local random_y = cfg.pos_y + (math.random() * cfg.range * 2) - cfg.range
	return random_x, random_y
end

-- 是否有可领取奖励
function KuafuYeZhanWangChengWGData:GetYeZhanWangChengHasReward()
	local data_list = self:GetCountRewardDataList()
	for k, v in pairs(data_list) do
		if v.can_fetch then
			return true
		end
	end
	return false
end

-- 天玑迷城奖励红点
function KuafuYeZhanWangChengWGData:GetYeZhanWangChengRed()
	if self:GetYeZhanWangChengHasReward() then
		return 1
	end
	return 0
end

function KuafuYeZhanWangChengWGData:SetTianXuanBuffInfo(protocol) 
	self.buff_refresh_time = protocol.buff_refresh_time
	self.get_buff_time = protocol.get_buff_time
	self.tianxuan_buff_role_uid = protocol.uid

	local role_head_data = {
		fashion_photoframe = protocol.fashion_photoframe,
		role_id = protocol.uid,
		prof = protocol.prof,
		sex = protocol.sex,
	}

	self.role_head_data = role_head_data
	self.buff_pos_x = protocol.pos_x
	self.buff_pos_y = protocol.pos_y
end

function KuafuYeZhanWangChengWGData:GetTianXuanBuffRefreshTime()
	return self.buff_refresh_time
end

function KuafuYeZhanWangChengWGData:GetTianXuanBuffGetTime()
	return self.get_buff_time
end

function KuafuYeZhanWangChengWGData:GetTianXuanBuffRoleUid()
	return self.tianxuan_buff_role_uid
end

function KuafuYeZhanWangChengWGData:GetTianXuanBuffTime()
	return self.other_cfg.tianxuan_buff_time
end

function KuafuYeZhanWangChengWGData:GetRoleHeadData()
	return self.role_head_data
end

function KuafuYeZhanWangChengWGData:GetBuffPosData()
	return self.buff_pos_x, self.buff_pos_y
end