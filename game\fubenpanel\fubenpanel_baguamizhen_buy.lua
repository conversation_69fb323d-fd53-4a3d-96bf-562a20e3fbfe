BaGuaMiZhenBuyView = BaGuaMiZhenBuyView or BaseClass(DayCountChangeView)

function BaGuaMiZhenBuyView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Normal
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_baguamizhen_msg")
	self.role_data_change_callback =  BindTool.Bind1(self.OnRoleDataChange, self)
end

function BaGuaMiZhenBuyView:__delete()

end

function BaGuaMiZhenBuyView:ReleaseCallBack()
	DayCountChangeView.ReleaseCallBack(self)
	RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)
end

function BaGuaMiZhenBuyView:CloseCallBack()
end

--观察人物身上的vip属性是否变化
function BaGuaMiZhenBuyView:OnRoleDataChange(attr_name, value, old_value)
	if attr_name == "vip_level" then
		self:Flush()
	end
end

function BaGuaMiZhenBuyView:LoadCallBack()
	DayCountChangeView.LoadCallBack(self)
	self.node_list["title_view_name"].text.text = Language.ViewName.CiShuZengJia
	self:SetSecondView(Vector2(589,412))
	self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind(self.OnClinkCancelHandler, self))
	self.node_list["btn_buy_count"].button:AddClickListener(BindTool.Bind(self.OnClinkBuyCount, self))
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback, {"vip_level"})
end

function BaGuaMiZhenBuyView:ShowIndexCallBack()
	self:Flush()
end

function BaGuaMiZhenBuyView:OnFlush()
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	local vip_buy_cfg = FuBenPanelWGData.Instance:GetVipBuyBaGuaZhenCfg()
	local next_vip =  FuBenPanelWGData.Instance:GetVipNextBaGuaMiZhenCount(role_vip)
	-- local fb_info = FuBenPanelWGData.Instance:GetPetAllInfo()
	local buy_times = FuBenPanelWGData.Instance:GetBaGuaMiZhenBuyTimes()
	local remain_count = vip_buy_cfg["param_" .. role_vip] - buy_times
	local color = remain_count > 0 and COLOR3B.GREEN or COLOR3B.RED
   	local des = string.format(Language.FuBenPanel.FuBenBuyTips, color, vip_buy_cfg["param_" .. role_vip] - buy_times, vip_buy_cfg["param_" .. role_vip])
	local comsume_gold = FuBenPanelWGData.Instance:GetBaGuaMiZhenBuyComsumeGold(buy_times + 1)
	local max_vip = VipWGData.Instance:GetMaxVIPLevel()
	if comsume_gold then
		local comsume_str = string.format(Language.FuBenPanel.CopperBuyNum, comsume_gold)
		self.node_list["rich_buy_desc"].text.text = comsume_str
	end

	self.node_list["rich_buy_des_1"].text.text = des

	if role_vip < max_vip and vip_buy_cfg["param_" .. role_vip + 1] then
		if vip_buy_cfg["param_" .. role_vip + 1] > vip_buy_cfg["param_" .. role_vip] then
			des = string.format(Language.FuBenPanel.CopperBuyTips2, vip_buy_cfg["param_" .. role_vip + 1])
		else
			for i = 1, max_vip do
				if vip_buy_cfg["param_" .. i] > vip_buy_cfg["param_" .. role_vip] then
					des = string.format(Language.FuBenPanel.CopperBuyTips2, vip_buy_cfg["param_" .. i])
					next_vip = i

					break
				end
			end
		end
	else
		des = Language.FuBenPanel.CopperBuyTips3
	end
	if role_vip >= next_vip then
		des = Language.FuBenPanel.CopperBuyTips3
	end

	self.node_list["rich_buy_des_2"].text.text = des


	if self.node_list["img_vip_curlevel"] and self.node_list["img_vip_nexlevel"] then
		self.node_list["img_vip_curlevel"].text.text = "V"..role_vip

		if role_vip < next_vip then
			self.node_list["img_vip_nexlevel"].text.text = "V"..next_vip

			-- self.copper_vip_num2:SetNumber(next_vip)

			self.node_list["layout_next_vip"]:SetActive(true)
			self.node_list["img_arrow"]:SetActive(true)


		else
			self.node_list["img_vip_nexlevel"].text.text = "V"..role_vip

			self.node_list["layout_next_vip"]:SetActive(true)
			self.node_list["img_arrow"]:SetActive(true)


		end
	end
end

function BaGuaMiZhenBuyView:OnClinkCancelHandler()
	self:Close()
end

function BaGuaMiZhenBuyView:OnClinkBuyCount()
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	--可购买次数
	local vip_buy_cfg = FuBenPanelWGData.Instance:GetVipBuyBaGuaZhenCfg()
	--购买次数
	local buy_times = FuBenPanelWGData.Instance:GetBaGuaMiZhenBuyTimes()
	--剩余购买次数
	local times = vip_buy_cfg["param_" .. role_vip] - buy_times
	--是否到最大购买次数
	local is_max_count = vip_buy_cfg["param_" .. 15] == buy_times
	if 0 == times then
		if is_max_count then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.BuyMaxCount)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.VIPTooLow)
		end
	elseif 1 == times then
		FuBenPanelWGCtrl.Instance:BaGuaMiZhenFbBuyTimesReq()
	else
		FuBenPanelWGCtrl.Instance:BaGuaMiZhenFbBuyTimesReq()
	end
end
