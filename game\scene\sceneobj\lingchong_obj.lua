LingChong = LingChong or BaseClass(FollowObj)

function LingChong:__init(vo)
	self.obj_type = SceneObjType.Pet
	self.draw_obj:SetObjType(self.obj_type)
	self.shield_obj_type = ShieldObjType.Pet
	self.shield_effect_type = ShieldObjType.PetEffect

	self.owner_obj_id = vo.owner_objid
	self.res_id = self.vo.lingchong_appeid

    self.can_bubble = false
    self.record_time = self.now_time or 0
    self.is_talk = false

    self.is_wander = true
	self.followui_hide_when_self_hide = true
	self.shadow_hide_when_self_hide = true

	self.bundle_name = ""
	self.asset_name = ""

	self.next_pet_atk_time = 0
	self.fight_continue_time = 1.5
end

function LingChong:__delete()
	local owner_name = ""
	if nil ~= self.owner_obj and self.owner_obj:IsRole() then
		self.owner_obj:RemovePet(self)
		owner_name = self.owner_obj.vo and self.owner_obj.vo.name or "nil"
	end
	self.owner_obj = nil

	self.delete_traceback = owner_name .. "  " .. debug.traceback()
end

function LingChong:InitInfo()
	FollowObj.InitInfo(self)
	self:GetFollowUi()
	self:ReloadUIName()
	self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("pet", self.res_id))
end

function LingChong:InitAppearance()
	local bundle, asset = ResPath.GetPetModel(self.res_id)
	self.bundle_name = bundle
	self.asset_name = asset
	self:ChangeModel(SceneObjPart.Main, bundle, asset)

    self.exist_time = ConfigManager.Instance:GetAutoConfig("bubble_list_auto").other[1].exist_time
    self.interval = ConfigManager.Instance:GetAutoConfig("bubble_list_auto").other[1].interval
    self.record_time = self.record_time + self.interval
end

function LingChong:Update(now_time, elapse_time)
	FollowObj.Update(self, now_time, elapse_time)

    if not self.can_bubble or not self:GetVisiable() then
        return
    end
    if nil ~= self.follow_ui and self:IsOnwerPet() then
        if now_time > self.record_time and self.is_talk == false then
            self.is_talk = true
            self:UpdataBubble()
            self.follow_ui:ForceSetVisible(true)
            self.follow_ui:ShowBubble()
            self.bobble_timer_quest = GlobalTimerQuest:AddDelayTimer(function()
            	if self.follow_ui then
	                self.follow_ui:CancelForceSetVisible()
	                self.follow_ui:HideBubble()
            	end
                self.is_talk = false
                self.record_time = now_time + self.interval
            end, self.exist_time)
        end
    end
end

function LingChong:UpdataBubble()
    if nil ~= self.follow_ui then
        local text = self:GetRandBubbletext()
        self.follow_ui:ChangeBubble(text)
    end
end

function LingChong:GetRandBubbletext()
    local bubble_cfg = NewAppearanceWGData.Instance:GetLingChongBubbleList(Scene.Instance:GetSceneType())
    if #bubble_cfg > 0 then
        math.randomseed(os.time())
        local can_show_cfg = {}
        local role_level = RoleWGData.Instance:GetRoleLevel()
        for i,v in ipairs(bubble_cfg) do
        	if role_level >= v.level then
        		table.insert(can_show_cfg, v)
        	end
       	end

       	if IsEmptyTable(can_show_cfg) then
       		return ""
       	end

        local bubble_text_index = math.random(1, #can_show_cfg)
        return can_show_cfg[bubble_text_index].pet_talk
    else
        return ""
    end
end


function LingChong:SetAttr(key, value)
	FollowObj.SetAttr(self, key, value)
    if self.vo.lingchong_appeid > 0 and self.res_id ~= self.vo.lingchong_appeid then
    	self.res_id = self.vo.lingchong_appeid
    	self:InitAppearance()
    	self:ReloadUIName()
    end
end

function LingChong:OnEnterScene()
	FollowObj.OnEnterScene(self)
	-- self:CreateShadow()
    local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    self.can_bubble = fb_scene_cfg.is_bubble and fb_scene_cfg.is_bubble == 1 or false
end

function LingChong:EnterStateAttack(anim_name)
	local anim_name = SceneObjAnimator.Atk1
	local skill_cfg = SkillWGData.Instance:GetSkillClientConfig(self.attack_skill_id)
	if nil ~= skill_cfg then
		anim_name = "attack" .. skill_cfg.action
	end
	FollowObj.EnterStateAttack(self, anim_name)
end

-- 战斗屏蔽随机移动
function LingChong:ShieldWadnerForce(target)
	return self:IsFightState() or not self:GetVisiable()
end

function LingChong:ReloadUIName()
	if self.follow_ui ~= nil then
		local appe_id = self.vo.lingchong_appeid
		local name
		local cfg = NewAppearanceWGData.Instance:GetLingChongBaseUpStarCfgByAppeId(appe_id, 0)
		if cfg then
			name = cfg.lingchong_name
		end

		cfg = NewAppearanceWGData.Instance:GetLingChongActCfgByAppeId(appe_id)
		if cfg then
			name = cfg.image_name
		end

		if name then
			self.follow_ui:SetName(name, self)
		end
	end
end

function LingChong:IsPet()
	return true
end

function LingChong:IsCharacter()
	return false
end

--是否是自己的宠物
function LingChong:IsOnwerPet()
	return self.owner_obj_id == GameVoManager.Instance:GetMainRoleVo().obj_id
end

function LingChong:CreateFollowUi()
    self.follow_ui = PetFollow.New(self.vo)
    self.follow_ui:OnEnterScene(self.is_enter_scene)
    self.follow_ui:Create(SceneObjType.Pet)
    if self.draw_obj then
        self.follow_ui:SetFollowTarget(self.draw_obj.root.transform, self.draw_obj:GetName())
    end
end

function LingChong:SetOwnerObj(owner_obj)
	self.owner_obj = owner_obj
end

function LingChong:GetOwnerObj()
	if nil == self.owner_obj or self.owner_obj:IsDeleted() then
		self.owner_obj = Scene.Instance:GetObjectByObjId(self.owner_obj_id)
		if nil ~= self.owner_obj and self.owner_obj:IsRole() then
			self.owner_obj:SetPet(self)
		end
	end
	return self.owner_obj
end

function LingChong:PartEffectVisibleChanged(part, visible)
	if self:IsDeleted() then
		return
	end
	
    self.draw_obj:SetPartIsDisableAttachEffect(part, not visible)
end

function LingChong:UpdateQualityLevel()
	if self:IsDeleted() then
		return
	end

	local base_offset = -1
	local owner_obj = self:GetOwnerObj()
	if owner_obj and owner_obj.IsMainRole and owner_obj:IsMainRole() then
		base_offset = 1
	end

	local model_offset = base_offset
	-- if self.bundle_name and "" ~= self.bundle_name and self.asset_name and "" ~= self.asset_name then
	--     model_offset = model_offset + QualityWGData.Instance:GetModelQualityOffsetConfig(self.bundle_name, self.asset_name)
	-- end
	self:SetQualityLevelOffset(model_offset)

	local effect_offset = base_offset
	-- if self.bundle_name and "" ~= self.bundle_name and self.asset_name and "" ~= self.asset_name then
	--     effect_offset = effect_offset + QualityWGData.Instance:GetModelEffectQualityOffsetConfig(self.bundle_name, self.asset_name)
	-- end
	self:SetEffectQualityLevelOffset(effect_offset)

	-- 模型材质球品质偏移值。
    -- 偏移值越小则材质球品质越高

    if self.draw_obj == nil then
    	print_error(owner_obj.vo.name, self.delete_traceback)
    	return
    end
end

function LingChong:TryUsePetSkill(target_x, target_y, target_obj_id)
	if self:IsDeleted() then
		return
	end

	if not target_x or not target_y or not target_obj_id then
		return
	end

	local target_obj = GuajiCache.target_obj
	if not target_obj or target_obj:IsDeleted() then
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	if not main_role then
		return
	end

	local pet_skill_id = 400 -- 目前只有普攻
	local skill_cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(pet_skill_id, 1)
	if not skill_cfg then
		return
	end

	local target_x, target_y = target_obj:GetLogicPos()
	local m_pos_x, m_pos_y = self:GetLogicPos()
	local target_dis = GameMath.GetDistance(m_pos_x, m_pos_y, target_x, target_y)
	local skill_dis = skill_cfg.distance or 1
	if target_obj and target_obj.checks and target_obj.obj_scale then
		local targetModleR = target_obj.checks / 2 - 1
		skill_dis = math.max(targetModleR, skill_dis)
	end

	if target_dis <= skill_dis * skill_dis then

		SceneObj.SetDirectionByXY(self, target_x, target_y)

		if self.next_pet_atk_time <= Status.NowTime * 1000 then
			self:DoAttack(pet_skill_id, target_x, target_y, target_obj_id)
			FightWGCtrl.Instance:SendPetSkillReq(target_obj_id)
			self.next_pet_atk_time = Status.NowTime * 1000 + skill_cfg.cd_s
		else
			self:DoStand()
		end
	else
		local r_pos_x, r_pos_y = main_role:GetLogicPos()
		local dir = u3d.v2Normalize({x = r_pos_x, y = r_pos_y})
		local move_x = math.floor(target_x + skill_dis * dir.x)
		local move_y = math.floor(target_y + skill_dis * dir.y)
		if not self:IsFightState() then
			self:StopMove()
			Character.DoMove(self, move_x, move_y, OBJ_MOVE_REASON.LING_CHONG_FIGHT)
		else
			if not self:IsMove() then
				Character.DoMove(self, move_x, move_y, OBJ_MOVE_REASON.LING_CHONG_FIGHT)
			else
				local reason = self:GetMoveReason()
				if reason == nil or reason ~= OBJ_MOVE_REASON.LING_CHONG_FIGHT then
					self:StopMove()
					Character.DoMove(self, move_x, move_y, OBJ_MOVE_REASON.LING_CHONG_FIGHT)
				end
			end
		end
	end

	self:EnterFight()
end

function LingChong:CreateShadow()
	FollowObj.CreateShadow(self)
	
	if self.shadow then
		self.shadow:AddShieldRule(ShieldRuleWeight.Max, function()
			return not self:GetVisiable()
		end)
	end
end
