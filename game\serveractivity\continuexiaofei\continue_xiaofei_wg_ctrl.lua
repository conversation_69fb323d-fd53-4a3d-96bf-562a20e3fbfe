require("game/serveractivity/continuexiaofei/continue_xiaofei_wg_data")

-- 控制器
ContinuousXiaoFeiWGCtrl = ContinuousXiaoFeiWGCtrl or BaseClass(BaseWGCtrl)

function ContinuousXiaoFeiWGCtrl:__init()
	if ContinuousXiaoFeiWGCtrl.Instance then
        error("[ContinuousXiaoFeiWGCtrl]:Attempt to create singleton twice!")
	end
	ContinuousXiaoFeiWGCtrl.Instance = self
	self.data = ContinuousXiaoFeiWGData.New()
	self:RegisterAllProtocals()
end

function ContinuousXiaoFeiWGCtrl:__delete()
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	ContinuousXiaoFeiWGCtrl.Instance = nil
end

function ContinuousXiaoFeiWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(SCRATotalConsumeInfo, "OnSCRATotalConsumeInfo")					-- 累消返利信息
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))
end

function ContinuousXiaoFeiWGCtrl:MainuiOpenCreate()
	local act_cornucopia_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.TotalConsume) or {}
	if act_cornucopia_info.status == ACTIVITY_STATUS.OPEN then
		local param_t = {
			rand_activity_type = ACTIVITY_TYPE.TotalConsume,
			opera_type = TOTAL_CONSUME_OP_TYPE.TOTAL_CONSUME_OP_TYPE_INFO,
		}
		ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
	end
end

function ContinuousXiaoFeiWGCtrl:OnSCRATotalConsumeInfo(protocol)
	self.data:SetContinueXiaoFeiInfo(protocol)
	ServerActivityWGCtrl.Instance:UpdataViewAndRemind()
	RemindManager.Instance:Fire(RemindName.rand_act_continue_consume)
end