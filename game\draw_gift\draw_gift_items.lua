DrawGiftRewardRender = DrawGiftRewardRender or BaseClass(BaseRender)
function DrawGiftRewardRender:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["item_pos"])
        self.item_cell:SetIsShowTips(false)
    end
end

function DrawGiftRewardRender:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function DrawGiftRewardRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.item_cell:SetData({item_id = self.data.item_id, num = self.data.num, is_bind = self.data.is_bind})
    local prob_value = tonumber(self.data.prob) or 0
    local prob_str = string.format("%s%%", prob_value / 100)
    self.node_list.prob_value.text.text = prob_str

    local name = ItemWGData.Instance:GetItemName(self.data.item_id)
	local color = ItemWGData.Instance:GetItemColor(self.data.item_id)
	self.node_list.item_name.text.text = ToColorStr(name,color)
end

---------------DrawGiftBaoDiRender------------
DrawGiftBaoDiRender = DrawGiftBaoDiRender or BaseClass(BaseRender)
function DrawGiftBaoDiRender:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["item_pos"])
        self.item_cell:SetIsShowTips(false)
    end
end

function DrawGiftBaoDiRender:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function DrawGiftBaoDiRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.item_cell:SetData({item_id = self.data.item_id, num = self.data.num, is_bind = self.data.is_bind})

    local next_need_count, slect_index = DrawGiftWGData.Instance:GetDrawGiftBaoDiNextNeedCount(self.data.gift_id)
    self.node_list.select_img:SetActive(self.index == slect_index)
    self.node_list.count.text.text = string.format(Language.DrawGift.NeedCount, self.data.need_count)
    local baodi_reward_list = DrawGiftWGData.Instance:GetDrawGiftBaoDiRewardInfo(self.data.gift_id)
    self.node_list.arrow:SetActive(self.index ~= #baodi_reward_list)
end