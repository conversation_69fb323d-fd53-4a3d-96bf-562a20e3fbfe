ChainDrawWGData = ChainDrawWGData or BaseClass()
ChainDrawWGData.MAX_REWARD_COUNT = 16 -- 普通奖励最大值
ChainDrawWGData.MAX_SPE_REWARD_COUNT = 9 -- 特殊奖励奖励最大值

function ChainDrawWGData:__init()
	if ChainDrawWGData.Instance ~= nil then
		<PERSON>rrorLog("[ChainDrawWGData] attempt to create singleton twice!")
		return
	end

	ChainDrawWGData.Instance = self

	self:InitCfg()
	self.grade = 1 -- 档次
    self.task_list = {}
    self.reward_pool_flag = {}
    self.special_reward_flag = {}

    RemindManager.Instance:Register(RemindName.ChainDraw, BindTool.Bind(self.ShowChainDrawRemind, self))
end

function ChainDrawWGData:__delete()
	ChainDrawWGData.Instance = nil
	self.grade = nil
    self.task_list = nil
    self.reward_pool_flag = nil
    self.special_reward_flag = nil
end

function ChainDrawWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_chain_draw_auto")
	self.reward_cfg = ListToMap(cfg.reward_pool, "grade", "seq")
	self.consume_cfg = ListToMapList(cfg.consume, "grade")
	self.task_cfg = ListToMapList(cfg.task, "grade", "task_type")
    self.spe_reward_cfg = ListToMap(cfg.special_reward, "grade", "seq")
	self.other_cfg = cfg.other[1]
end

--全部数据
function ChainDrawWGData:SetAllChainDrawInfo(protocol)
	self.grade = protocol.grade
	self.reward_pool_flag = protocol.reward_pool_flag
	self.special_reward_flag = protocol.special_reward_flag
end

--全部任务数据
function ChainDrawWGData:SetAllTaskInfo(protocol)
    self.task_list = protocol.task_list
end

--获取普通奖励是否领取
function ChainDrawWGData:GetRewardflag(seq)
    return self.reward_pool_flag[seq] and self.reward_pool_flag[seq] == 1 or false
end

--获取特殊奖励状态是否领取
function ChainDrawWGData:GetSpeRewardflag(seq)
    return self.special_reward_flag[seq] and self.special_reward_flag[seq] == 1 or false
end

--获取全部特殊奖励状态是否领取
function ChainDrawWGData:GetAllSpeRewardflag()
    return self.special_reward_flag
end
-- 单个任务信息改变
function ChainDrawWGData:SetSingleTaskInfo(protocol)
    local data = protocol.change_data
    for i, v in pairs(self.task_list) do
        if v.task_id == data.task_id then
            self.task_list[v.task_id] = data
            return
        end
    end
end

function ChainDrawWGData:GetAllTaskList()
	local task_data_list = {}
	local cur_grade_task_cfg = self.task_cfg[self.grade]
	if not IsEmptyTable(cur_grade_task_cfg) then
		for k, v in pairs(cur_grade_task_cfg) do
			local cfg = v[#v]
			if #v > 1 then
				for k1, v1 in ipairs(v) do
					if self.task_list[v1.task_id] and self.task_list[v1.task_id].status ~= REWARD_STATE_TYPE.FINISH then
						cfg = v1
						break
					end
				end
			end
	
			local task_type = cfg.task_type
			local task_id = cfg.task_id
			task_data_list[task_type] = {}
			task_data_list[task_type].task_type = cfg.task_type
			task_data_list[task_type].task_id = cfg.task_id
			task_data_list[task_type].item_list = cfg.item_list
			task_data_list[task_type].des = cfg.des
			task_data_list[task_type].target = cfg.target
			task_data_list[task_type].open_panel = cfg.open_panel
			local status = self.task_list[cfg.task_id] and self.task_list[cfg.task_id].status or REWARD_STATE_TYPE.UNDONE
			task_data_list[task_type].status = status
			task_data_list[task_type].progress_num = self.task_list[cfg.task_id] and self.task_list[cfg.task_id].progress_num or 0
	
			local sort_index = 0
			if status == REWARD_STATE_TYPE.UNDONE then
				task_data_list[task_type].sort_index = 100
			elseif status == REWARD_STATE_TYPE.CAN_FETCH then
				task_data_list[task_type].sort_index = 10
			elseif status == REWARD_STATE_TYPE.FINISH then
				task_data_list[task_type].sort_index = 1000
			end
		end
	end

	table.sort(task_data_list, SortTools.KeyLowerSorters("sort_index", "task_id"))
	return task_data_list
end

function ChainDrawWGData:GetAllRewardCfg()
	return self.reward_cfg[self.grade] or {}
end

function ChainDrawWGData:GetAllSpeRewardCfg()
	return self.spe_reward_cfg[self.grade] or {}
end

function ChainDrawWGData:GetSpeRewardCfgBySeq(seq)
	return (self.spe_reward_cfg[self.grade] or {})[seq]
end

function ChainDrawWGData:GetOtherCfg()
	return self.other_cfg
end

--获取当前步数
function ChainDrawWGData:GetCurTime()
	local time = 0
	for k, v in pairs(self.reward_pool_flag) do
		if v == 1 then
			time = time + 1
		end
	end

	return time
end

-- 根据步数获取消耗数量
function ChainDrawWGData:GetTimeCostNum(times)
	local cur_grade_consume_cfg = self.consume_cfg[self.grade] or {}
	local count = 0
	for i, v in ipairs(cur_grade_consume_cfg) do
		if times == v.times then
			count = v.cost_item_num
		end
	end

	return count
end

--判断特殊奖励能不能领取
function ChainDrawWGData:IsCanGetSpeReward(seq)
    local state = self:GetSpeRewardflag(seq)
    if state then
        return false
    end

    local is_can_get = true
    local cfg = self:GetSpeRewardCfgBySeq(seq)
    if cfg then
        if cfg.is_rare == 0 then
            local reward_seq_list = Split(cfg.condition,"|")
            for k, v in pairs(reward_seq_list) do
                local reward_seq = tonumber(v)
                if not self:GetRewardflag(reward_seq) then
                    is_can_get = false
                    break
                end
            end
        elseif cfg.is_rare == 1 then
            local spe_reward_seq_list = Split(cfg.condition,"|")
            for k, v in pairs(spe_reward_seq_list) do
                local spe_reward_seq = tonumber(v)
                if not self:GetSpeRewardflag(spe_reward_seq) then
                    is_can_get = false
                    break
                end
            end
        end
    end

    return is_can_get
end

---红点--
function ChainDrawWGData:ShowChainDrawRemind()
	if self:ShowTimeBtnRemind() then
		return 1
	end

	if self:ShowTaskRemind() then
		return 1
	end

    if self:ShowSpeRewardRemind() then
        return 1
    end

	return 0
end

---按钮红点--
function ChainDrawWGData:ShowTimeBtnRemind()
	local remind = false
	local cur_time = self:GetCurTime()
	if cur_time < ChainDrawWGData.MAX_REWARD_COUNT then
		local other_cfg = self:GetOtherCfg()
	    local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.cost_item_id)
	    local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.cost_item_id)
	    local cost_num = self:GetTimeCostNum(cur_time + 1)
	    if item_num >= cost_num then
	    	remind = true
	    end
	end

	return remind
end

--任务红点
function ChainDrawWGData:ShowTaskRemind()
	local remind = false
	for i, v in ipairs(self.task_list) do
		if v.status == REWARD_STATE_TYPE.CAN_FETCH then
			remind = true
			break
		end
	end

	return remind
end

---特殊奖励红点
function ChainDrawWGData:ShowSpeRewardRemind()
	local remind = false
	local spe_reward_cfg = self:GetAllSpeRewardCfg()
	if spe_reward_cfg then
		for k, v in pairs(spe_reward_cfg) do
			if self:IsCanGetSpeReward(v.seq) then
                remind = true
                break
            end
		end
	end

	return remind
end