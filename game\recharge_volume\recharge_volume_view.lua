RechargeVolumeView = RechargeVolumeView or BaseClass(SafeBaseView)

function RechargeVolumeView:__init()
    self.view_style = ViewStyle.Full
	self:SetMaskBg(false)
	self.full_screen = true
	self.is_safe_area_adapter = true
	self.is_big_view = true
    self.default_index = TabIndex.recharge_volume_exchange
    self.open_source_view = "btn_recharge_volume"

    local common_bundle = "uis/view/common_panel_prefab"
    local bundle_name = "uis/view/recharge_volume_ui_prefab"
	self:AddViewResource(0, common_bundle, "layout_a3_common_panel")
	self:AddViewResource(0, bundle_name, "recharge_volume_main_view")
    self:AddViewResource(0, common_bundle, "layout_a3_light_common_top_panel")
end

local LIST_ENUM = {
    PRODUCE_COUNT = 2,
    GET_WAY_COUNT = 2,
    RECHARGE_COUNT = 4,
}

local INDEX_LIST = {
    GB_INDEX = 2,           --女神祝福index
    RV_LIMIT_INDEX = 1,     --仙府纳财index
    RV_REWARD_INDEX = 3,    --提取奖励index
}

function RechargeVolumeView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.RechargeVolume.TitleName
    local bundle, asset = ResPath.GetRawImagesPNG("a3_czj_bg")
    self.node_list.RawImage_tongyong.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
    end)

    -- 创建货币栏
    if not self.money_bar then
        self.money_bar = MoneyBar.New()
        bundle, asset = ResPath.GetWidgets("MoneyBar")
        local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_volume = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
        self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
    end

    if not self.produce_list then
        self.produce_list = {}

        for i = 1, LIST_ENUM.PRODUCE_COUNT do
            local cell_obj = self.node_list.produce_list:FindObj(string.format("produce_%d", i))
            if cell_obj then
                local cell = ProduceVolumeItemRender.New(cell_obj)
                cell:SetClickCallBack(BindTool.Bind1(self.OnSelectProduceCB, self))
                cell:SetIndex(i)
                self.produce_list[i] = cell
            end
        end
    end

    if not self.get_way_list then
        self.get_way_list = AsyncListView.New(GetWayVolumeItemRender, self.node_list.get_way_list)
        self.get_way_list:SetSelectCallBack(BindTool.Bind(self.OnSelectGetwayCB, self))
        self.get_way_list:SetStartZeroIndex(false)
    end

    if not self.recharge_list then
        self.recharge_list = {}

        for i = 1, LIST_ENUM.RECHARGE_COUNT do
            local cell_obj = self.node_list.recharge_list:FindObj(string.format("recharge_render_%d", i))
            if cell_obj then
                local cell = RechargeVolumeItemRender.New(cell_obj)
                cell:SetClickCallBack(BindTool.Bind1(self.OnSelectRechargeCB, self))
                cell:SetIndex(i)
                self.recharge_list[i] = cell
            end
        end
    end

    self.node_list.desc_limit_tip.text.text = Language.RechargeVolume.TodayLimitTip

    XUI.AddClickEventListener(self.node_list.btn_more, BindTool.Bind(self.OnClickOpenGetWayList, self))
    XUI.AddClickEventListener(self.node_list.btn_block, BindTool.Bind(self.OnClickCloseGetWayList, self))
    XUI.AddClickEventListener(self.node_list.btn_recharge_up, BindTool.Bind(self.OnClickRechargeBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_recharge_volume, BindTool.Bind(self.OnClickRechargeVolumeBtn, self))
    XUI.AddClickEventListener(self.node_list.rv_reward_btn, BindTool.Bind(self.OnClickRvRewardBtn, self))
    XUI.AddClickEventListener(self.node_list.open_god_wealth_btn, BindTool.Bind(self.OnClickOpenGodWealthBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_yy_privilege, BindTool.Bind(self.OnClickYYPrivilegeBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_up_use, BindTool.Bind(self.OnClickUpUseBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_close_account_tip, BindTool.Bind(self.OnClickCloseAccountTipBtn, self))

    self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
    FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.RechargeVolumeView, self.get_guide_ui_event)

    self.remind_callback = BindTool.Bind(self.OtherRemindCallBack, self)
    RemindManager.Instance:Bind(self.remind_callback, RemindName.YanYuGe_Privilege)
end

function RechargeVolumeView:ReleaseCallBack()
    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

    if self.produce_list and #self.produce_list > 0 then
        for i, cell in ipairs(self.produce_list) do
            cell:DeleteMe()
        end

        self.produce_list = nil
    end

    if self.get_way_list then
		self.get_way_list:DeleteMe()
		self.get_way_list = nil
	end

    if self.recharge_list and #self.recharge_list > 0 then
        for i, cell in ipairs(self.recharge_list) do
            cell:DeleteMe()
        end

        self.recharge_list = nil
    end

    FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.RechargeVolumeView, self.get_guide_ui_event)
    self.get_guide_ui_event = nil

    if self.remind_callback then
        RemindManager.Instance:UnBind(self.remind_callback)
        self.remind_callback = nil
    end

    self.select_index = nil
    self.select_data = nil
end

function RechargeVolumeView:CloseCallBack()
    self.select_index = nil
    self.select_data = nil
end

-- function RechargeVolumeView:ShowExchangeViewCallBack()
-- end

-- 点击跳转获取
function RechargeVolumeView:OnSelectProduceCB(produce_item)
    if (not produce_item) or (not produce_item.data) then
		return
	end

    local data = produce_item.data
    if data.view_path ~= nil and data.view_path ~= "" then
        FunOpen.Instance:OpenViewNameByCfg(data.view_path)
    end

    -- if produce_item.index == 1 then
    --     local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	--     local year, month, day = TimeUtil.FormatSecond5MYHM1(TimeWGCtrl.Instance:GetServerTime())
    --     PlayerPrefsUtil.SetInt("RechargeVolumeWGCtrl" .. main_role_id .. year .. month .. day, 1)
    --     RechargeVolumeWGData.Instance:SetDailyOpenFlag(true)
    --     for i, cell in ipairs(self.produce_list) do
    --         cell:Flush()
    --     end
    -- end
end

-- 点击跳转获取路径
function RechargeVolumeView:OnSelectGetwayCB(get_way_item, cell_index, is_default, is_click)
    if (not get_way_item) or (not get_way_item.data) or not is_click then
		return
	end

    local data = get_way_item.data
    if data.view_path ~= nil and data.view_path ~= "" then
        FunOpen.Instance:OpenViewNameByCfg(data.view_path)
    end

    self.node_list.get_way_content:SetActive(false)
end

-- 点击对应档位兑换
function RechargeVolumeView:OnSelectRechargeCB(recharge_item)
    if (not recharge_item) or (not recharge_item.data) then
		return
	end

    self.select_index = recharge_item.index
    self.select_data = recharge_item.data
    self:ChangeSelectIndex()

    -- 计算每日使用额度
    local type = VIRTUAL_GOLD_TYPE.RECHARGE_VOLUME
    local max_num = RechargeWGData.Instance:GetAllVirtualGoldMaxUseNum(type)
    --local time_limit_num = RechargeWGData.Instance:GetVirtualGoldLimitTimeLimited(type)
    local use_num = RechargeWGData.Instance:GetVirtualGoldUseNum(type)
    local day_total_num = max_num
    local can_use_num = day_total_num - use_num

    local recharge_volume_num = RoleWGData.Instance:GetAttr("recharge_volume") or 0
    if (recharge_volume_num < recharge_item.data.consume_num) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.RechargeVolume.RechargeError1)
        return
    end

    if (can_use_num - self.select_data.consume_num < 0) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.RechargeVolume.RechargeError2)
        -- local is_buy = RechargeVolumeWGData.Instance:GetIsBuyAllGoddessBlessing()
        local is_buy = YanYuGeWGData.Instance:IsAllZanZhuTeQuanUnLock()
        if not is_buy then
            -- RechargeVolumeWGCtrl.Instance:OpenGoddessBlessingView()
            ViewManager.Instance:Open(GuideModuleName.PrivilegeCollectionView, TabIndex.pri_col_zztq)
        end

        return
    end

    local bundle_name, asset_name = ResPath.GetEffectUi("UI_czj_lingyu_diaoluo")
	EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["lingyu_diaoluo_effect"].transform, 4)

    RechargeWGCtrl.Instance:OnSendVirualGold2Req(VIRUAL_GOLD_OPERA_TYPE.ExchangeGold, recharge_item.data.exchange_seq)
    --RechargeWGCtrl.Instance:Recharge(recharge_item.data.money, 0, recharge_item.data.money)

    -- if self.select_index == recharge_item.index then
    --     return
    -- end
end

-- 刷新界面
function RechargeVolumeView:OnFlush(param_t)
	for k, v in pairs(param_t) do
		if k == "all" then
			self:FlushProduceGetWayView()
            self:FlushRechargeLimitMessage()
            self:FlushRechargeExchangeListView()
            self:FlushRechargeVolumeReward()
		end
	end
end

function RechargeVolumeView:FlushRechargeVolumeReward()
    local is_open1 = FunOpen.Instance:GetFunIsOpened(GuideModuleName.RechargeVolumeRewardView)
    self.node_list.rv_reward_btn:SetActive(is_open1)
    if is_open1 then
        self.node_list.rv_reward_name.text.text = Language.RechargeVolume.TitleName2
        self.node_list.rv_reward_str.text.text = Language.RechargeVolume.RvRewardStr
        local red_flag = RechargeVolumeWGData.Instance:GetRemind() == 1
        self.node_list.rv_reward_red:SetActive(red_flag)
    end

    local is_open2 = FunOpen.Instance:GetFunIsOpened(GuideModuleName.GodOfWealthView)
    self.node_list.open_god_wealth_btn:SetActive(is_open2)
    if is_open2 then
        self.node_list.god_wealth_name.text.text = Language.RechargeVolume.TitleName3
        self.node_list.god_wealth_str.text.text = Language.RechargeVolume.GodWealthStr
        local red_flag1 = GodOfWealthWGData.Instance:GetGodOfWealthRemind() == 1
        local red_flag2 = GodOfWealthWGData.Instance:GetLuckyWealthRemind() == 1
        local red_flag = red_flag1 or red_flag2
        self.node_list.god_wealth_red:SetActive(red_flag)
    end

    local is_open3 = FunOpen.Instance:GetFunIsOpened(GuideModuleName.YanYuGePrivilegeView)
    self.node_list.btn_yy_privilege:SetActive(is_open3)
    if is_open3 then
        self.node_list.yy_privilege_name.text.text = Language.RechargeVolume.TitleName4
        self.node_list.yy_privilege_str.text.text = Language.RechargeVolume.YyPrivilegeStr
    end

    self.node_list.recharge_volume_reward_title:SetActive(is_open1 or is_open2 or is_open3)
end

function RechargeVolumeView:SetTitleState(bool)
    if self.node_list.type_title then
        self.node_list.type_title:SetActive(bool)
    end
end

-- 刷新提额方式和获取方式
function RechargeVolumeView:FlushProduceGetWayView()
    local list = RechargeVolumeWGData.Instance:GetRechargeGetWayList()
    self.get_way_list:SetDataList(list)

    local list2 = RechargeVolumeWGData.Instance:GetRechargeProduceList()
    for i, cell in ipairs(self.produce_list) do
        if list2[i] then
            cell:SetVisible(true)
            cell:SetData(list2[i])
        else
            cell:SetVisible(false)
        end
    end
end

-- 刷新限额信息
function RechargeVolumeView:FlushRechargeLimitMessage()
    --self.node_list.btn_more_red:CustomSetActive(RechargeVolumeWGData.Instance:GetCanUseRemind() == 1)
    self.node_list.btn_recharge_volume_remind:CustomSetActive(RechargeVolumeWGData.Instance:GetCanUseRemind() == 1)
    local type = VIRTUAL_GOLD_TYPE.RECHARGE_VOLUME

    -- 计算每日使用额度
    local max_num = RechargeWGData.Instance:GetAllVirtualGoldMaxUseNum(type)
    local time_limit_num = RechargeWGData.Instance:GetVirtualGoldLimitTimeLimited(type)
    --local max_add_num = RechargeWGData.Instance:GetVirtualGoldMaxAddNum(type)
    local use_num = RechargeWGData.Instance:GetVirtualGoldUseNum(type)
    local day_total_num = max_num
    --local use_color = use_num >= day_total_num and "#0c8c33" or COLOR3B.C5

    -- 计算每日可获得的额度
    -- local day_limit = RechargeVolumeWGData.Instance:GetEveryDayLimit()
    -- local get_color = time_limit_num >= day_limit and COLOR3B.GREEN or COLOR3B.WHITE

    -- 每日使用额度展示
    local limit_use_str = string.format("%s/%s", use_num, day_total_num)
    self.node_list.one_limit_use_desc.text.text = Language.RechargeVolume.TodayLimitChange
    self.node_list.one_limit_use_str.text.text = limit_use_str

    -- 每日获得额度展示
    -- local time_limit_str = ToColorStr(string.format("%s/%s", time_limit_num, day_limit), get_color)
    self.node_list.today_limit_str.text.text = string.format(Language.RechargeVolume.TodayGetTimeLimit, time_limit_num)

    -- local is_all_buy = YanYuGeWGData.Instance:IsAllZanZhuTeQuanUnLock()
    -- self.node_list.btn_up_use_eff:SetActive(not is_all_buy)
end
  
-- 刷新档位信息和金额额度信息
function RechargeVolumeView:FlushRechargeExchangeListView()
    local recharge_data = RechargeWGData.Instance:GetRechargeList()
    local exchange_cfg = RechargeVolumeWGData.Instance:GetExchangeCfg()


    local up_quota = RechargeWGData.Instance:GetRechargeVolumeShowOther("money_seq")
    local up_quota_list = Split(up_quota, "/")
    local is_defult_select = true
    local defult_select = 1
    local recharge_volume_num = RoleWGData.Instance:GetAttr("recharge_volume") or 0

    for i, cell in ipairs(self.recharge_list) do
        if exchange_cfg and exchange_cfg[i - 1] then
            cell:SetVisible(true)
            cell:SetData(exchange_cfg[i - 1])
        else
            cell:SetVisible(false)
        end
    end

    -- if self.select_index ~= nil then
    --     is_defult_select = false
    -- end

    -- for i, cell in ipairs(self.recharge_list) do
    --     local up_quota_index = up_quota_list[i]
    --     local recharge_index = tonumber(up_quota_index) or 1
    --     if recharge_data[recharge_index] then
    --         local need_value = recharge_data[recharge_index].money or 0

    --         if is_defult_select and recharge_volume_num >= need_value then
    --             defult_select = i
    --         end

    --         cell:SetVisible(true)
    --         cell:SetData(recharge_data[recharge_index])
    --     else
    --         cell:SetVisible(false)
    --     end
    -- end

    -- if is_defult_select then
    --     self.select_index = defult_select
    --     local up_quota_index = up_quota_list[self.select_index]
    --     local recharge_index = tonumber(up_quota_index) or 1
    --     self.select_data = recharge_data[recharge_index]
    -- end

    -- self:ChangeSelectIndex()
end

-- 切换选中
function RechargeVolumeView:ChangeSelectIndex()
    for i, cell in ipairs(self.recharge_list) do
        cell:ChangeSelect(i == self.select_index)
    end
end
----------------------------------------------------------------
-- 更多的兑换方式
function RechargeVolumeView:OnClickGoMoreBtn()
    local jump_path = RechargeVolumeWGData.Instance:GetJumpPath()
    FunOpen.Instance:OpenViewNameByCfg(jump_path)
end

-- 打开获取提额道具列表
function RechargeVolumeView:OnClickOpenGetWayList()
    self.node_list.get_way_content:SetActive(true)
    self.node_list.btn_block:SetActive(true)
end

-- 关闭获取提额道具列表
function RechargeVolumeView:OnClickCloseGetWayList()
    self.node_list.get_way_content:SetActive(false)
    self.node_list.btn_block:SetActive(false)
end

-- 升级
function RechargeVolumeView:OnClickRechargeBtn()
    RechargeVolumeWGCtrl.Instance:OpenVolumeLimitView()
end

--打开提取奖励界面
function RechargeVolumeView:OnClickRvRewardBtn()
    RechargeVolumeWGCtrl.Instance:OpenVolumeRewardView()
end

--打开招财进宝界面
function RechargeVolumeView:OnClickOpenGodWealthBtn()
    ViewManager.Instance:Open(GuideModuleName.GodOfWealthView)
end

--打开烟雨特权界面
function RechargeVolumeView:OnClickYYPrivilegeBtn()
    ViewManager.Instance:Open(GuideModuleName.YanYuGePrivilegeView)
end

function RechargeVolumeView:OtherRemindCallBack(remind_name, num)
    if remind_name == RemindName.YanYuGe_Privilege then
        self.node_list.yy_privilege_red:SetActive(num > 0)
    end
end

function RechargeVolumeView:OnClickUpUseBtn()
    ViewManager.Instance:Open(GuideModuleName.PrivilegeCollectionView, TabIndex.pri_col_zztq)
end

-- 点击对应档位兑换
function RechargeVolumeView:OnClickRechargeVolumeBtn()
    local recharge_volume_num = RoleWGData.Instance:GetAttr("recharge_volume") or 0

    if recharge_volume_num <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.RechargeVolume.RechargeError)
        return
    end

    local type = VIRTUAL_GOLD_TYPE.RECHARGE_VOLUME

    -- 计算每日使用额度
    local max_num = RechargeWGData.Instance:GetAllVirtualGoldMaxUseNum(type)
    --local time_limit_num = RechargeWGData.Instance:GetVirtualGoldLimitTimeLimited(type)
    local use_num = RechargeWGData.Instance:GetVirtualGoldUseNum(type)
    local day_total_num = max_num
    local can_use_num = day_total_num - use_num

    if can_use_num <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.RechargeVolume.RechargeError)
        return
    end

    local target_recharge_volume_num = (can_use_num > recharge_volume_num) and recharge_volume_num or can_use_num
    local str = string.format(Language.RechargeVolume.RechargeExtractLingYu, target_recharge_volume_num, target_recharge_volume_num * 10)
    TipWGCtrl.Instance:OpenAlertTips(str, function ()
        self:PlayRechargeVolumeEffect()
        RechargeWGCtrl.Instance:OnSendVirualGold2Req(VIRUAL_GOLD_OPERA_TYPE.TaskAllGold)
    end)

    -- if not self.select_data then
    --    return 
    -- end

    -- local recharge_volume_num = RoleWGData.Instance:GetAttr("recharge_volume") or 0
    -- if recharge_volume_num < self.select_data.money then
    --     SysMsgWGCtrl.Instance:ErrorRemind(Language.RechargeVolume.RechargeError)
    --     return
    -- end

    -- RechargeWGCtrl.Instance:Recharge(self.select_data.money, 0, self.select_data.money)
end

-- 点击对应档位兑换
function RechargeVolumeView:PlayRechargeVolumeEffect()
    if self.node_list and self.node_list.effect_center_root then
        local bundle_name, asset_name = ResPath.GetA2Effect("UI_lingyu_get")
        EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.effect_center_root.transform)
    end
end

function RechargeVolumeView:GetGuideUiCallBack(ui_name, ui_param)
    if ui_name == "recharge_render_1" then
        return self.node_list[ui_name], BindTool.Bind(self.OnSelectRechargeCB, self, self.recharge_list[1])
    elseif ui_name == "produce_2" then
        return self.node_list[ui_name], BindTool.Bind(self.OnSelectProduceCB, self, self.produce_list[2])
    end

    -- if ui_name == GuideUIName.Tab and self.tabbar then
	-- 	local tab_index = math.floor(TabIndex[ui_param])
	-- 	if tab_index == self:GetShowIndex() then
	-- 		return NextGuideStepFlag
	-- 	else
	-- 		self:ShowIndex(tab_index)
	-- 		return NextGuideStepFlag
	-- 	end
	-- elseif ui_name == GuideUIName.XXXBtn then
    --     return -- function()
	-- end

	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

function RechargeVolumeView:OpenAccountTipContent()
    UITween.CleanAlphaShow(GuideModuleName.RechargeVolumeView)
    self.node_list.account_tip_content.canvas_group.alpha = 1
    local cfg = RechargeWGData.Instance:GetVirtualGoldUseChangeValCfg()
    if cfg then
        ReDelayCall(self, function()
            UITween.AlphaShow(GuideModuleName.RechargeVolumeView, self.node_list.account_tip_content, 1, 0.1, 0.2, DG.Tweening.Ease.Linear, function ()
                self.node_list.account_tip_content:SetActive(false)
            end)
        end, 2, "account_tip_content")

        self.node_list.account_tip_content:SetActive(true)
        self.node_list.account_tip_desc.text.text = string.format(Language.RechargeVolume.AccountTipDesc, cfg.consume_num, cfg.lingyu_num)
        AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.ChongZhi, nil, true)) 	-- 播放音频
    end
end

function RechargeVolumeView:OnClickCloseAccountTipBtn()
    self.node_list.account_tip_content:SetActive(false)
end

-------------------------------------------槽位格子---------------------------------------------------
ProduceVolumeItemRender = ProduceVolumeItemRender or BaseClass(BaseRender)

function ProduceVolumeItemRender:OnFlush()
    if self.data == nil then
        return
    end

    self.node_list.func_name.text.text = self.data.add_name
    local get_num = ""

    if self.index == INDEX_LIST.RV_LIMIT_INDEX then
        local is_all_finish = RechargeVolumeWGData.Instance:GetIsFinishAllTask()
        get_num = is_all_finish and self.data.get_num2 or self.data.get_num
    else
        get_num = self.data.get_num
    end
    self.node_list.produce_str.text.text = get_num

    local red_flag = false
    if self.index == INDEX_LIST.GB_INDEX then
        -- red_flag = not RechargeVolumeWGData.Instance:GetDailyOpenFlag()
        red_flag = PrivilegeCollectionWGData.Instance:GetZZTQRedPoint() > 0
    elseif self.index == INDEX_LIST.RV_LIMIT_INDEX then
        red_flag = YanYuGeWGData.Instance:GetNoblePrivilegeRedPoint() == 1
    -- elseif self.index == INDEX_LIST.RV_REWARD_INDEX then
    --     red_flag = RechargeVolumeWGData.Instance:GetRemind() == 1
    end

    self.node_list.produce_red:SetActive(red_flag)
end

-------------------------------------------槽位格子---------------------------------------------------
GetWayVolumeItemRender = GetWayVolumeItemRender or BaseClass(BaseRender)

function GetWayVolumeItemRender:OnFlush()
    if self.data == nil then
        return
    end

    local icon_bundle, icon_asset = ResPath.GetCumulateRechargeImage(self.data.func_img)
    self.node_list.icon.image:LoadSprite(icon_bundle, icon_asset, function()
        self.node_list.icon.image:SetNativeSize()
    end)

    self.node_list.name.text.text = self.data.func_name

end
----------------------------------------------兑换格子-----------------------------------------------
RechargeVolumeItemRender = RechargeVolumeItemRender or BaseClass(BaseRender)

function RechargeVolumeItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local is_remind = RechargeVolumeWGData.Instance:GetCanUseRemindBySeq(self.data.exchange_seq)
    self.node_list.red:SetActive(is_remind)

    self.node_list.change_txt.text.text = string.format(Language.RechargeVolume.RechargeText, self.data.lingyu_num)
    self.node_list.desc_txt.text.text = string.format(Language.RechargeVolume.RechargeLimitTips, self.data.consume_num)
end

function RechargeVolumeItemRender:ChangeSelect(is_select)
    -- self.node_list.select:CustomSetActive(is_select)
end