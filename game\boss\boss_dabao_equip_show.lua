local Max_Index = 10

local State_Tb = {
    Shrink = 0,
    Expand = 1
}
DabaoBossEquipShow = DabaoBossEquipShow or BaseClass(SafeBaseView)
function DabaoBossEquipShow:__init()
    self.is_safe_area_adapter = true
    self.view_name = "DabaoBossEquipShow"
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_dabao_book_chips")
    self.view_layer = UiLayer.MainUIHigh
end

function DabaoBossEquipShow:CloseCallBack()
    if CountDownManager.Instance:HasCountDown("dabao_boss_equip_hide") then
        CountDownManager.Instance:RemoveCountDown("dabao_boss_equip_hide")
    end
    self:CancelTween()
end

function DabaoBossEquipShow:LoadCallBack()
    self.obj_die_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_DEAD, BindTool.Bind(self.MainRoleDie, self))

    XUI.AddClickEventListener(self.node_list["shrink_btn"], BindTool.Bind(self.PlayTween, self, false))
    XUI.AddClickEventListener(self.node_list["open_charm_btn"], BindTool.Bind(self.OnClickBookBtn, self))
    XUI.AddClickEventListener(self.node_list["total_btn"], BindTool.Bind(self.OnClickGoToEquip, self))

    self.chips_item_list = {}
    local list_node = self.node_list["chip_list"]
    for i = 0, Max_Index do
        self.chips_item_list[i] = BossDaBaoChipEquipRender.New(list_node:FindObj("chip_" .. i))
        self.chips_item_list[i]:SetIndex(i)
    end

    self.state = State_Tb.Expand
    self:PlayTween(true)

    if self.load_callback then
		self.load_callback()
        self.load_callback = nil
    else
        self:SetAutoHide(6)
    end

    MainuiWGCtrl.Instance:AddObjToMainUI(self.node_list["open_charm_btn"], "charm_mainui_root")
end

function DabaoBossEquipShow:ShowIndexCallBack()
    
end

function DabaoBossEquipShow:OnClickGoToEquip()
    FunOpen.Instance:OpenViewByName(GuideModuleName.MultiFunctionView)
end

function DabaoBossEquipShow:MainRoleDie()
	self:Flush()
end

function DabaoBossEquipShow:FlushData(show_func)
    if self:IsOpen() and self:IsLoaded() then
        if self.state == State_Tb.Shrink then
            self:OnClickBookBtn()
        end
        show_func()
    else
        self:SetLoadCallBack(show_func)
        self:Open()
    end
end

function DabaoBossEquipShow:UpdataTime(elapse_time, total_time)
	
end

function DabaoBossEquipShow:TimeCompleteCallBack()
    if self.state == State_Tb.Expand and not self.enter_play_tween then
        self:PlayTween(false)
    end
end

function DabaoBossEquipShow:SetAutoHide(time)
    if CountDownManager.Instance:HasCountDown("dabao_boss_equip_hide") then
        CountDownManager.Instance:RemoveCountDown("dabao_boss_equip_hide")
    end

    time = time or 4
    CountDownManager.Instance:AddCountDown("dabao_boss_equip_hide", BindTool.Bind(self.UpdataTime, self), BindTool.Bind(self.TimeCompleteCallBack, self), nil, time, 1)
end

function DabaoBossEquipShow:CancelTween()
    if self.enter_play_tween then
        self.enter_play_tween:Kill()
        self.enter_play_tween = nil
    end
end

function DabaoBossEquipShow:GetState()
    return self.state
end

function DabaoBossEquipShow:OnClickBookBtn()
    self:PlayTween(self.state ~= State_Tb.Expand)       -- 合起天书
end

function DabaoBossEquipShow:PlayTween(is_enter)
    self.state = is_enter and State_Tb.Expand or State_Tb.Shrink
    if is_enter then
        if CountDownManager.Instance:HasCountDown("dabao_boss_equip_hide") then
            CountDownManager.Instance:RemoveCountDown("dabao_boss_equip_hide")
        end
    end

    self:CancelTween()
    self.node_list["open_charm_btn"]:SetActive(true)
    if not is_enter then
        self.node_list["shrink_btn"]:SetActive(is_enter)
    end

    self.enter_play_tween = DG.Tweening.DOTween.Sequence()
    local from = is_enter and 0 or 1
    local to = is_enter and 1 or 0
    self.node_list.chip_list.canvas_group.alpha = from
    self.node_list.book_content.transform.localScale = Vector3(from, from, from)
    local tween_alpha = self.node_list.chip_list.canvas_group:DoAlpha(from, to, 0.3)
    local tween_scale = self.node_list.book_content.rect:DOScale(Vector3(to, to, to), 0.3)
    if is_enter then
        self.enter_play_tween:Append(tween_scale)
        self.enter_play_tween:Append(tween_alpha)
    else
        self.enter_play_tween:Append(tween_alpha)
        self.enter_play_tween:Append(tween_scale)
    end

    if not is_enter then --收缩
        self.enter_play_tween:OnComplete(function()
            self.node_list["shrink_btn"]:SetActive(is_enter)
            BossWGCtrl.Instance:MoveXianJieRevengeHorizontal(is_enter)
            self.enter_play_tween = nil
        end)
    else --展开
        self.enter_play_tween:OnComplete(function()
            self.node_list["shrink_btn"]:SetActive(true)
            self.enter_play_tween = nil
        end)
    end

    self.enter_play_tween:Play()
end


function DabaoBossEquipShow:SetLoadCallBack(callback)
	self.load_callback = callback
end

function DabaoBossEquipShow:ReleaseCallBack()
    if self.node_list and self.node_list["open_charm_btn"] then
        self.node_list["open_charm_btn"].transform:SetParent(self.root_node_transform, false)
    end

    if not IsEmptyTable(self.chips_item_list) then
        for k, v in pairs(self.chips_item_list) do
            v:DeleteMe()
        end
        self.chips_item_list = nil
    end

    if self.move_tip_tween then
        self.move_tip_tween:Kill()
        self.move_tip_tween = nil
    end

    if CountDownManager.Instance:HasCountDown("dabao_boss_equip_hide") then
        CountDownManager.Instance:RemoveCountDown("dabao_boss_equip_hide")
    end

    if self.obj_die_event then
		GlobalEventSystem:UnBind(self.obj_die_event)
		self.obj_die_event = nil
	end

    self:CancelTween()
    self.load_callback = nil
end

function DabaoBossEquipShow:OnFlush()
    local cur_layer = BossWGData.Instance:GetDaBaoBossCurLayer()
	local show_order_num, show_quality_num = BossWGData.Instance:GetDaboBossShowIsEneter(cur_layer)
	local equip_show_list = CultivationWGData.Instance:GetCharmEquipShowCfg()
	if not equip_show_list or cur_layer == nil then
		return
	end

	local show_list = equip_show_list[show_order_num]
    if IsEmptyTable(show_list) then
		return 
	end

	local list = {}
    local is_insert_spe_list = false --0槽位特殊处理

	for k, v in pairs(show_list) do
		local _, item_color = ItemWGData.Instance:GetItemColor(v.item_id)
		if v.solt ~= 0 and item_color == show_quality_num then
			table.insert(list, v)
        elseif v.solt == 0 and item_color >= show_quality_num and (not is_insert_spe_list) then
            is_insert_spe_list = true
            table.insert(list, 1, v)
		end
	end

	for k, v in pairs(self.chips_item_list) do
        v:SetData(list)
    end
end






BossDaBaoChipEquipRender = BossDaBaoChipEquipRender or BaseClass(BaseRender)
function BossDaBaoChipEquipRender:__init()
end

function BossDaBaoChipEquipRender:LoadCallBack()
	self.can_get_flag = false
	self.get_flag = false
    self.node_list["page_self"].button:AddClickListener(BindTool.Bind(self.OnClickCell,self))
end

function BossDaBaoChipEquipRender:__delete()
	self.can_get_flag = nil
	self.get_flag = nil
end

function BossDaBaoChipEquipRender:OnFlush()
    if self.data == nil then
        return
    end

    local slot = self.index
    local bundle, asset
	local bg_bundle, bg_asset

	local solt_bag_data =  CultivationWGData.Instance:GetCharmOneKeyBagCache(slot)
	local have_flag = false
	local equip_cfg =  CultivationWGData.Instance:GetCharmEquipByItemId(self.data[slot + 1].item_id)
	local _, equip_color = ItemWGData.Instance:GetItemColor(self.data[slot + 1].item_id)
	if solt_bag_data then
		for k, v in pairs(solt_bag_data) do
			if v.order >= equip_cfg.order and v.color >= equip_color then
				have_flag = true
			end
		end
	else
		bundle, asset = ResPath.GetBossUI("a2_charm_holy_seal_paper" .. slot)
		bg_bundle, bg_asset = ResPath.GetBossUI("a2_charm_holy_seal_item_bg0")
	end
	
	local chip_data = CultivationWGData.Instance:GetCharmSoltDataBySolt(slot)

	if chip_data and chip_data.item_id ~= 0 then
		local chip_equip_cfg =  CultivationWGData.Instance:GetCharmEquipByItemId(chip_data.item_id)
		local _, chip_color = ItemWGData.Instance:GetItemColor(chip_data.item_id)
		self.get_flag = chip_equip_cfg.order >= equip_cfg.order and chip_color >= equip_color 
	end

	self.can_get_flag = self.get_flag or have_flag
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data[slot + 1].item_id)
	local _, item_color = ItemWGData.Instance:GetItemColor(self.data[slot + 1].item_id)
	if self.can_get_flag and item_cfg then
		bundle, asset = ResPath.GetItem(item_cfg.icon_id)
		bg_bundle, bg_asset = ResPath.GetBossUI("a2_charm_holy_seal_item_bg" .. item_color)
	else
		bundle, asset = ResPath.GetBossUI("a2_charm_holy_seal_paper" .. slot)
		bg_bundle, bg_asset = ResPath.GetBossUI("a2_charm_holy_seal_item_bg0")
	end
	self.node_list.bg.image:LoadSprite(bg_bundle, bg_asset)
    self.node_list.icon.image:LoadSprite(bundle, asset)
end

function BossDaBaoChipEquipRender:OnClickCell()
    if self.data == nil then
        return
    end

	local slot = self.index
    TipWGCtrl.Instance:OpenItem({item_id = self.data[slot + 1].item_id})
end