function FightSoulView:InitComposeView()
    self.select_compose_index_inbag = -1

    if nil == self.compose_bag_grid then
        self.compose_bag_grid = AsyncBaseGrid.New()
        self.compose_bag_grid:SetStartZeroIndex(false)
        self.compose_bag_grid:CreateCells({
            col = 4,
            cell_count = 20,
            list_view = self.node_list["us_bag_list"],
            itemRender = FightSoulBagItem,
            assetBundle = "uis/view/fight_soul_ui_prefab",
            assetName = "fight_soul_bag_item",
            change_cells_num = 2,
        })
        self.compose_bag_grid:SetSelectCallBack(BindTool.Bind1(self.OnComposeBagSelectCB, self))
    end

    if nil == self.us_cur_item then
        self.us_cur_item = ItemCell.New(self.node_list.us_cur_item)
    end

    if nil == self.us_next_item then
        self.us_next_item = ItemCell.New(self.node_list.us_next_item)
    end

    if nil == self.us_attr_list then
        self.us_attr_list = {}
        local attr_num = self.node_list.us_attr_part.transform.childCount
        for i = 1, attr_num do
            local cell = FightSoulAttrRender.New(self.node_list.us_attr_part:FindObj("attr_" .. i))
            cell:SetIndex(i)
            self.us_attr_list[i] = cell
        end
    end

    if nil == self.us_stuff_item_list then
        self.us_stuff_item_list = {}
        local stuff_num = self.node_list.us_item_part.transform.childCount
        for i = 1, stuff_num do
            local cell = FightSoulComposeStuffItem.New(self.node_list.us_item_part:FindObj("item_" .. i))
            cell:SetClickCallBack(BindTool.Bind(self.OnClickComposeStuff, self))
            cell:SetIndex(i)
            self.us_stuff_item_list[i] = cell
        end
    end

    XUI.AddClickEventListener(self.node_list.btn_upstar, BindTool.Bind(self.OnClickCompose, self))
    XUI.AddClickEventListener(self.node_list.us_cur_skill, BindTool.Bind(self.OnClickComposeCurSkill, self))
    XUI.AddClickEventListener(self.node_list.us_next_skill, BindTool.Bind(self.OnClickComposeNextSkill, self))

    -- self:InitComposeTweenPos()
end

function FightSoulView:DeleteComposeView()
    if nil ~= self.compose_bag_grid then
        self.compose_bag_grid:DeleteMe()
        self.compose_bag_grid = nil
    end

    if nil ~= self.us_attr_list then
        for k,v in pairs(self.us_attr_list) do
            v:DeleteMe()
        end
        self.us_attr_list = nil
    end

    if nil ~= self.us_stuff_item_list then
        for k,v in pairs(self.us_stuff_item_list) do
            v:DeleteMe()
        end
        self.us_stuff_item_list = nil
    end

    if nil ~= self.us_cur_item then
        self.us_cur_item:DeleteMe()
        self.us_cur_item = nil
    end

    if nil ~= self.us_next_item then
        self.us_next_item:DeleteMe()
        self.us_next_item = nil
    end

    if self.compose_remind_alert then
        self.compose_remind_alert:DeleteMe()
        self.compose_remind_alert = nil
    end

    self.select_compose_index_inbag = nil
    self.us_model_bundle = nil
    self.us_model_asset = nil
    self.compose_meet_list = nil
	self.compose_bag_list = nil
end

-- 刷新背包
function FightSoulView:FlushComposeBag()
    -- self:DoComposeTween()

    if self.compose_bag_grid then
        self.compose_bag_list = FightSoulWGData.Instance:GetComposeFightSoulBagList()
        if not IsEmptyTable(self.compose_bag_list) then
            self.compose_bag_grid:CancleAllSelectCell()
            self.compose_bag_grid:SetDataList(self.compose_bag_list)

            -- 沙雕 AsyncBaseGrid 只有点击回调，没有选择回调
            local sort_data
            local select_cell_index = 1
            if self.select_compose_index_inbag ~= nil then
                for k,v in ipairs(self.compose_bag_list) do
                    if v.item_data and v.item_data.index == self.select_compose_index_inbag then
                        select_cell_index = k
                        break
                    end
                end
            end

            self.compose_bag_grid:SetSelectCellIndex(select_cell_index)
            local sort_data = self.compose_bag_list[select_cell_index].item_data
            self.select_compose_index_inbag = sort_data.index
            self:SetComposeStuffSelect(sort_data, true)
        else
            -- 列表默认操作
            self.select_compose_index_inbag = -1
        end
    end

    self:FlushComposeView()
end

function FightSoulView:InitComposeTweenPos()
    RectTransform.SetAnchoredPositionXY(self.node_list.us_bag_part.rect, 0, -410)
    RectTransform.SetAnchoredPositionXY(self.node_list.us_right_part.rect, 320, 0)
end

function FightSoulView:DoComposeTween()
    if not self.do_compose_tween_flag then
        return
    end
    self.do_compose_tween_flag = false

    local tween_info = UITween_CONSTS.SiXiangSys
    UITween.CleanAllTween(GuideModuleName.FightSoulView)
    UITween.FakeHideShow(self.node_list.us_name_part)
    UITween.FakeHideShow(self.node_list.us_right_tween_node)

    self:InitComposeTweenPos()
    self.node_list.us_right_part.rect:DOAnchorPos(Vector2(-10, 0), tween_info.MoveTime)
    self.node_list.us_bag_part.rect:DOAnchorPos(Vector2(0, -57), tween_info.MoveTime)
    ReDelayCall(self, function()
        UITween.AlphaShow(GuideModuleName.FightSoulView, self.node_list.us_name_part, 0, 1, tween_info.AlphaTime)
        UITween.AlphaShow(GuideModuleName.FightSoulView, self.node_list.us_right_tween_node, 0, 1, tween_info.AlphaTime)
    end, tween_info.MoveTime, "fs_compose_tween")
end

-- 背包点击回调
function FightSoulView:OnComposeBagSelectCB(cell)
    if cell == nil or cell.data == nil or cell.data.item_data == nil then
        return
    end

    if self.select_compose_index_inbag == cell.data.item_data.index then
        TipWGCtrl.Instance:OpenItem(cell.data.item_data, ItemTip.FROM_NORMAL, nil)
        return
    end

    self.select_compose_index_inbag = cell.data.item_data.index
    self:SetComposeStuffSelect(cell.data.item_data, true)
    self:FlushComposeView()
end

-- 获取当前选择的数据
function FightSoulView:GetComposeSelectData()
    return FightSoulWGData.Instance:GetFightSoulItemByBagIndex(self.select_compose_index_inbag)
end

function FightSoulView:SetComposeStuffSelect(item_data, auto_select)
    item_data = item_data or self:GetComposeSelectData()
    self.compose_meet_list = FightSoulWGData.Instance:GetMeetComposeStuffSortList(item_data, auto_select)
    self:UpdateComposeGridSelect()
end

function FightSoulView:UpdateComposeGridSelect()
    if IsEmptyTable(self.compose_bag_list) then
    	return
    end

    if self.compose_bag_grid then
        local cell_list = self.compose_bag_grid:GetAllCell()
        for index, cell in pairs(cell_list) do
            cell:FlushCheckState()
        end
    end

    self:FlushComposeStuffAndBtn()
end

-- 刷新界面
function FightSoulView:FlushComposeView()
    local select_data = self:GetComposeSelectData()
    if IsEmptyTable(select_data) then
        -- 无数据
        self.node_list.us_cap_value.text.text = 0
        self.node_list.us_level_text.text.text = 0
        self.node_list.us_name_text.text.text = ""

        self.us_cur_item:ClearData()
        self.us_cur_item:SetItemIcon(ResPath.GetCommonImages("a1_suo_bb2"))
        self.node_list.us_arrow:SetActive(false)
        self.node_list.us_next_item:SetActive(false)

        local attr_list = FightSoulWGData.Instance:GetComposeAttr(0, 0, true)
        for k,v in pairs(self.us_attr_list) do
            v:SetData(attr_list[k])
        end
        return
    end

    -- 名字
    self.node_list.us_level_text.text.text = select_data.level
    local item_cfg = ItemWGData.Instance:GetItemConfig(select_data.item_id)
    self.node_list.us_name_text.text.text = item_cfg and item_cfg.name or ""

    -- 战力
    self.node_list.us_cap_value.text.text = FightSoulWGData.Instance:GetFightSoulBaseCapability(select_data.item_id)

    -- 目标
    local next_item_id = FightSoulWGData.Instance:GetComposeTargetItemId(select_data.fight_soul_type, select_data.color, select_data.star)
    local cur_show_data, next_show_data = FightSoulWGData.Instance:GetComposeShowItemData(select_data, next_item_id)
    self.us_cur_item:SetData(cur_show_data)
    self.us_next_item:SetData(next_show_data)
    self.node_list.us_arrow:SetActive(next_item_id > 0)
    self.node_list.us_next_item:SetActive(next_item_id > 0)

    -- 技能
    local cur_fs_item_cfg = FightSoulWGData.Instance:GetFightSoulItemCfg(select_data.item_id)
    local next_fs_item_cfg = FightSoulWGData.Instance:GetFightSoulItemCfg(next_item_id)
    local cur_skill_level = cur_fs_item_cfg and cur_fs_item_cfg.skill_level or 0
    local next_skill_level = next_fs_item_cfg and next_fs_item_cfg.skill_level or 0
    local had_next_skill = next_skill_level > cur_skill_level
    self.node_list.us_skill_arrow:SetActive(had_next_skill)
    self.node_list.us_next_skill:SetActive(had_next_skill)
    self.node_list.us_cur_skill_level.text.text = cur_skill_level.."级"
    local c_bundle, c_asset = FightSoulWGData.Instance:GetSkillIconResByItemId(select_data.item_id)
    self.node_list.us_cur_skill_icon.image:LoadSprite(c_bundle, c_asset, function()
        self.node_list.us_cur_skill_icon.image:SetNativeSize()
    end)

    if had_next_skill then
        self.node_list.us_next_skill_level.text.text = next_skill_level.."级"
        local n_bundle, n_asset = FightSoulWGData.Instance:GetSkillIconResByItemId(next_item_id)
        self.node_list.us_next_skill_icon.image:LoadSprite(n_bundle, n_asset, function()
            self.node_list.us_next_skill_icon.image:SetNativeSize()
        end)
    end

    -- 属性
    local attr_list = FightSoulWGData.Instance:GetComposeAttr(select_data.item_id, next_item_id)
    for k,v in pairs(self.us_attr_list) do
        v:SetData(attr_list[k])
    end
end

-- 刷新合成拆料 和 按钮
function FightSoulView:FlushComposeStuffAndBtn()
    local stuff_list, is_meet_list, is_max = FightSoulWGData.Instance:GetComposeShowStuffList(
                                    self.select_compose_index_inbag, self.compose_meet_list)

    if self.us_stuff_item_list then
        for k,v in ipairs(self.us_stuff_item_list) do
            v:SetData(stuff_list[k])
        end
    end

    local is_all_meet = true
    if IsEmptyTable(is_meet_list) then
    	is_all_meet = false
    else
    	for k,v in pairs(is_meet_list) do
    		if not v then
    			is_all_meet = false
    			break
    		end
    	end
    end
    self.node_list.btn_upstar:SetActive(not is_max)
    self.node_list.us_max_star:SetActive(is_max)
    self.node_list.btn_us_remind:SetActive(is_all_meet and not is_max)
end

-- 点击合成材料
function FightSoulView:OnClickComposeStuff(cell)
    if cell == nil or cell.data == nil or self.compose_meet_list == nil then
        return
    end

    self:OpenSelectStuff(cell.data.stuff_type)
end

function FightSoulView:OpenSelectStuff(stuff_type)
	if self.compose_meet_list == nil then
		return
	end

    FightSoulWGCtrl.Instance:OpenStuffSelectView(stuff_type, self:GetComposeSelectData(),
                                self.compose_meet_list)
end

function FightSoulView:OnClickCompose()
    local fs_data = FightSoulWGData.Instance
    local stuff_list, is_meet_list, is_max = fs_data:GetComposeShowStuffList(
                                    self.select_compose_index_inbag, self.compose_meet_list)

    if is_max or IsEmptyTable(is_meet_list) then
		return
	end

    for k,v in pairs(is_meet_list) do
        if not v then
            self:OpenSelectStuff(k)
            return
        end
    end

    local bag_index_list = {}
    local cache_list = fs_data:GetComposeSelectCache()
    local had_up_stuff_list = {}
    for k,v in pairs(cache_list) do
        bag_index_list[#bag_index_list + 1] = k
        local item_data = fs_data:GetFightSoulItemByBagIndex(k)
        if item_data and (item_data.level > 0 or item_data.grade > 0) then
            table.insert(had_up_stuff_list, item_data)
        end
    end

    if #had_up_stuff_list > 0 then
        local exp_num, grade_stuff_id, grade_stuff_num = 0, 0, 0
        local temp_exp_num, temp_grade_stuff_id, temp_grade_stuff_num = 0, 0, 0
        for k,v in pairs(had_up_stuff_list) do
            temp_exp_num, temp_grade_stuff_id, temp_grade_stuff_num = fs_data:GetReturnExpAndStuff(v)
            exp_num = exp_num + temp_exp_num
            grade_stuff_num = grade_stuff_num + temp_grade_stuff_num
            grade_stuff_id = temp_grade_stuff_id
        end

        if nil == self.compose_remind_alert then
            self.compose_remind_alert = Alert.New(nil, nil, nil, nil, true)
            self.compose_remind_alert:SetCheckBoxDefaultSelect(false)
        end

        local exp_str, grade_stuff_str = "", ""
        if exp_num > 0 then
            local item_id = FightSoulWGData.Instance:GetFightSoulExpItemId()
            local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
            local item_name = Language.FightSoul.FightSoulExpName
            if item_cfg then
                exp_str = string.format("\n<color=%s>%s：</color>%s", ITEM_COLOR[item_cfg.color],
                            item_name, CommonDataManager.ConverNumber(exp_num))
            end
        end

        if grade_stuff_id > 0 and grade_stuff_num > 0 then
            local item_cfg = ItemWGData.Instance:GetItemConfig(grade_stuff_id)
            if item_cfg then
                local temp_str = exp_num > 0 and "      " or "\n"
                grade_stuff_str = string.format("%s<color=%s>%s：</color>%s", temp_str, ITEM_COLOR[item_cfg.color],
                                                item_cfg.name, grade_stuff_num)
            end
        end

        self.compose_remind_alert:SetLableString(string.format(Language.FightSoul.ComposeReturnTips, exp_str, grade_stuff_str))
        self.compose_remind_alert:SetOkFunc(function()
            FightSoulWGCtrl.Instance:ReqFightSoulComposeOp(FIGHT_SOUL_COMPOSE_TYPE.FIGHT_SOUL,
                                                        self.select_compose_index_inbag,
                                                        bag_index_list)
    	end)

    	self.compose_remind_alert:Open()
        return
    end

    FightSoulWGCtrl.Instance:ReqFightSoulComposeOp(FIGHT_SOUL_COMPOSE_TYPE.FIGHT_SOUL,
                                                self.select_compose_index_inbag,
                                                bag_index_list)
end

function FightSoulView:OnClickComposeCurSkill()
    local select_data = self:GetComposeSelectData()
    if IsEmptyTable(select_data) then
        return
    end

    local is_wear, slot = FightSoulWGData.Instance:GetTypeIsWear(select_data.fight_soul_type)
    FightSoulWGCtrl.Instance:OpenSkillTipsByItemId(select_data.item_id, slot)
end

function FightSoulView:OnClickComposeNextSkill()
    local select_data = self:GetComposeSelectData()
    if IsEmptyTable(select_data) then
        return
    end

    local next_item_id = FightSoulWGData.Instance:GetComposeTargetItemId(select_data.fight_soul_type,
                                                                        select_data.color, select_data.star)
    if next_item_id > 0 then
        local is_wear, slot = FightSoulWGData.Instance:GetTypeIsWear(select_data.fight_soul_type)
        FightSoulWGCtrl.Instance:OpenSkillTipsByItemId(next_item_id, slot)
    end
end
