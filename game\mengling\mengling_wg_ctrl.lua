require("game/mengling/mengling_view")
require("game/mengling/mengling_wg_data")
require("game/mengling/mengling_bag_panel")
require("game/mengling/mengling_baptism_panel")
require("game/mengling/mengling_compose_view")
require("game/mengling/mengling_inlay_panel")
require("game/mengling/mengling_resolve_view")
require("game/mengling/mengling_select_store_view")
require("game/mengling/mengling_strength_panel")
require("game/mengling/mengling_stuff_list_view")
require("game/mengling/mengling_suit_view")
require("game/mengling/mengling_up_store_view")
require("game/mengling/mengling_upgrade_panel")
require("game/mengling/mengling_common_left_view")
require("game/mengling/mengling_suit_panel")

MengLingWGCtrl = MengLingWGCtrl or BaseClass(BaseWGCtrl)

function MengLingWGCtrl:__init()
	if MengLingWGCtrl.Instance then
		print_error("[MengLingWGCtrl] Attemp to create a singleton twice !")
	end
	MengLingWGCtrl.Instance = self

	if not self.view then
		self.view = MengLingView.New(GuideModuleName.MengLingView)
	end

	if not self.data then
		self.data = MengLingWGData.New()
	end

    if not self.menglling_resolve_view then
        self.menglling_resolve_view = MengLingResolveView.New()
    end

    if not self.menglling_suit_view then
        self.menglling_suit_view = MengLingSuitView.New()
    end

    if not self.mengling_stuff_list_view then
        self.mengling_stuff_list_view = MengLingComposeStuffListView.New()
    end


    self:RegisterProtocol(CSDreamSpiritEquip)
    self:RegisterProtocol(CSDreamSpiritOperate)
    self:RegisterProtocol(CSDreamSpiritEquipDecompos)
    self:RegisterProtocol(CSDreamSpiritEquipCompos)
    self:RegisterProtocol(SCMengLingBag, "OnSCMengLingBag")
    self:RegisterProtocol(SCMengLingBagUpdate, "OnSCMengLingBagUpdate")
    self:RegisterProtocol(SCDreamSpiritInfo, "OnSCDreamSpiritInfo")
    self:RegisterProtocol(SCDreamSpiritItemInfo, "OnSCDreamSpiritItemInfo")
    self:RegisterProtocol(SCDreamSpiritItemUpdate, "OnSCDreamSpiritItemUpdate")

    self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})

	self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback)

    self.day_pass = GlobalEventSystem:Bind(OtherEventType.PASS_DAY, BindTool.Bind(self.OnDayPass, self))
    -- if not self.mengling_up_store_view then
    --     self.mengling_up_store_view = MengLingUpStroeView.New()
    -- end

    -- if not self.mengling_inlay_store_view then
    --     self.mengling_inlay_store_view = MengLingInlayStroeView.New()
    -- end
end

function MengLingWGCtrl:__delete()
	MengLingWGCtrl.Instance = nil

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

    if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end

	if self.item_data_change_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
		self.item_data_change_callback = nil
	end

    -- if self.mengling_up_store_view then
    --     self.mengling_up_store_view:DeleteMe()
    --     self.mengling_up_store_view = nil
    -- end

    -- if self.mengling_inlay_store_view then
    --     self.mengling_inlay_store_view:DeleteMe()
    --     self.mengling_inlay_store_view = nil
    -- end

    if self.menglling_suit_view then
        self.menglling_suit_view:DeleteMe()
        self.menglling_suit_view = nil
    end

    if self.menglling_resolve_view then
        self.menglling_resolve_view:DeleteMe()
        self.menglling_resolve_view = nil
    end

    if self.mengling_stuff_list_view then
        self.mengling_stuff_list_view:DeleteMe()
        self.mengling_stuff_list_view = nil
    end


    if nil ~= self.day_pass then
		GlobalEventSystem:UnBind(self.day_pass)
		self.day_pass = nil
	end

    if self.tunshi_timer then
		GlobalTimerQuest:CancelQuest(self.tunshi_timer)
		self.tunshi_timer = nil
	end
end


-------------------------------------protocol_start-------------------------------------------
function MengLingWGCtrl:OnCSDreamSpiritOperate(operate_type, param1, param2, param3, param4)
    -- print_error("执行操作",operate_type, param1, param2, param3, param4)
	local protocol = ProtocolPool.Instance:GetProtocol(CSDreamSpiritOperate)
	protocol.operate_type = operate_type or 0
 	protocol.param1 = param1 or 0
 	protocol.param2 = param2 or 0
 	protocol.param3 = param3 or 0
    protocol.param4 = param4 or 0
 	protocol:EncodeAndSend()
end

function MengLingWGCtrl:OnCSDreamSpiritEquipDecompos(count, msg_item)
    -- 分解装备
    -- print_error("--分解装备--- ", count, msg_item)
    local protocol = ProtocolPool.Instance:GetProtocol(CSDreamSpiritEquipDecompos)
    protocol.count = count or 0
    protocol.msg_item = msg_item or {}
    protocol:EncodeAndSend()
end

function MengLingWGCtrl:OnCSDreamSpiritEquip(seq, count, msg_item)
    -- print_error("--穿戴装备--- ", seq, count, msg_item)
    local protocol = ProtocolPool.Instance:GetProtocol(CSDreamSpiritEquip)
    protocol.seq = seq or 0
    protocol.count = count or 0
    protocol.msg_item = msg_item or {}
    protocol:EncodeAndSend()
end

function MengLingWGCtrl:OnCSDreamSpiritEquipCompos(item_id, count, msg_item)
    -- print_error("--合成装备--- ", item_id, count, msg_item)
    local protocol = ProtocolPool.Instance:GetProtocol(CSDreamSpiritEquipCompos)
    protocol.item_id = item_id or 0
    protocol.count = count or 0
    protocol.msg_item = msg_item or {}
    protocol:EncodeAndSend()
end

function MengLingWGCtrl:OnSCMengLingBag(protocol)
    -- print_error("------梦灵背包-----------", protocol.grid_count, protocol.grid_list)

    self.data:SetMengLingBagInfo(protocol)
    RemindManager.Instance:Fire(RemindName.MengLingBag)
    RemindManager.Instance:Fire(RemindName.MengLingCompose)
    ViewManager.Instance:FlushView(GuideModuleName.MengLingView)
end

function MengLingWGCtrl:OnSCMengLingBagUpdate(protocol)
    -- print_error("----------梦灵背包更新----------", protocol.grid_info)
    local new_data = protocol.grid_info
    local is_add, add_num = self.data:UpdateMengLingBagInfo(protocol)

    if not IsEmptyTable(new_data) and new_data.item_id > 0 and new_data.num > 0 then
        if is_add and add_num > 0 then
            local name = ItemWGData.Instance:GetItemNameDarkColor(new_data.item_id)
            local str = string.format(Language.Bag.GetItemTxt, ToColorStr(name, ITEM_TIP_COLOR[new_data.color]), add_num)
            GlobalTimerQuest:AddDelayTimer(function()
                SysMsgWGCtrl.Instance:ErrorRemind(str)
            end, 0.2)

            -- 自动分解
            local _, item_color = ItemWGData.Instance:GetItemColor(new_data.item_id)

            if self:GetIsAutoTunShi() and item_color <= GameEnum.ITEM_COLOR_PURPLE and self.data:CheckCanResolve(new_data.item_id) then
                if not self.tunshi_timer then --自动吞噬延迟一秒  防止卡顿
                    self.tunshi_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.AutoTunShiEquip, self), 1)
                end
            else
                -- 快速使用弹窗
                local equip_cfg = MengLingWGData.Instance:GetMengLingEquipCfgByItemId(new_data.item_id)

                if not IsEmptyTable(equip_cfg) then
                    local up_flag = MengLingWGData.Instance:GetMengLingItemUpFlag(equip_cfg.seq, equip_cfg.slot, equip_cfg.item_id)

                    if up_flag then
                        FunctionGuide.Instance:OpenMengLingeyUseView({item_id = equip_cfg.item_id, index = new_data.index, slot = equip_cfg.slot})
                    end
                end
            end
        end
    end

    RemindManager.Instance:Fire(RemindName.MengLingBag)
    RemindManager.Instance:Fire(RemindName.MengLingCompose)
    ViewManager.Instance:FlushView(GuideModuleName.MengLingView)

    if self.menglling_resolve_view and self.menglling_resolve_view:IsOpen() then
        self.menglling_resolve_view:Flush()
    end
end

function MengLingWGCtrl:OnSCDreamSpiritInfo(protocol)
    -- print_error("-----------OnSCDreamSpiritInfo---梦灵积分及属性丹--------",protocol)

    local old_score = self.data:GetMengLingScore()
    local new_score = protocol.score

    if old_score >= 0 and new_score > old_score then
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.MengLing.MengLingGetScoreDesc, new_score - old_score))
    end

    self.data:SetDreamSpiritInfo(protocol)
    RemindManager.Instance:Fire(RemindName.MengLingSXD)
    RemindManager.Instance:Fire(RemindName.MengLingStrong)
    ViewManager.Instance:FlushView(GuideModuleName.MengLingView)
end

function MengLingWGCtrl:OnSCDreamSpiritItemInfo(protocol)
    -- print_error("-----------OnSCDreamSpiritItemInfo-----------",protocol)
    self.data:SetDreamSpiritItemInfo(protocol)
    RemindManager.Instance:Fire(RemindName.MengLingBag)
    RemindManager.Instance:Fire(RemindName.MengLingStrong)

    -- self.data:SetDreamSpiritItemInfo(protocol)
    -- RemindManager.Instance:Fire(RemindName.MengLingView)
end

function MengLingWGCtrl:OnSCDreamSpiritItemUpdate(protocol)
    -- print_error("-----------OnSCDreamSpiritItemUpdate-----------",protocol)
    local seq = protocol.mengling_seq
    local old_mengling_item_data = self.data:GetMengLingSuitItemDataBy(seq)
    local new_mengling_item_data = protocol.mengling_suit_item

    local old_skill_unlock = self.data:IsMengLingSuitSkillActive(seq)
    local old_suit_data = self.data:GetMengLingSuitInfo(seq)

    self.data:UpdateDreamSpiritItemInfo(protocol)
    RemindManager.Instance:Fire(RemindName.MengLingBag)
    RemindManager.Instance:Fire(RemindName.MengLingStrong)

    local new_skill_unlock = self.data:IsMengLingSuitSkillActive(seq)
    local new_suit_data = self.data:GetMengLingSuitInfo(seq)
    ---------------------------------------------
    -- 技能激活
    local active_skill = false
    if not old_skill_unlock and new_skill_unlock then
        active_skill = true
    end

    -- 套装激活
    local active_data = {}
    local has_active_suit = false

    for i = 1, 2 do
        local act_data = {active = false, color = GameEnum.ITEM_COLOR_WHITE}
        local old_data = old_suit_data[i]
        local new_data = new_suit_data[i]

        if IsEmptyTable(old_data) and not IsEmptyTable(new_data) then
            act_data.active = true
            act_data.color = new_data.color
            has_active_suit = true
        else
            local old_color = old_data and old_data.color or GameEnum.ITEM_COLOR_WHITE
            local new_color = new_data and new_data.color or GameEnum.ITEM_COLOR_WHITE

            if new_color > old_color then
                -- act_data.active = true
                act_data.color = new_data.color
                -- has_active_suit = true
            end
        end

        active_data[i] = act_data
    end

    local show_unlock_skill = self:CheckMengLingSkillUnLock(seq, old_mengling_item_data, new_mengling_item_data)

    local call_back = nil

    if active_skill or has_active_suit then
        if show_unlock_skill then
            call_back = function()
                ViewManager.Instance:FlushView(GuideModuleName.MengLingView, nil, "view_operate_effect", {active_skill = active_skill, active_data = active_data})
            end
        else
            ViewManager.Instance:FlushView(GuideModuleName.MengLingView, nil, "view_operate_effect", {active_skill = active_skill, active_data = active_data})
        end
    else
        ViewManager.Instance:FlushView(GuideModuleName.MengLingView)
    end

    if show_unlock_skill then
        local grade_cfg = self.data:GetGradeCfgBySeq(seq)
        
        local skill_cfg = {
            icon = grade_cfg.get_skill_icon_res,
            res_fun = ResPath.GetMengLingImg,
            name = grade_cfg.skill_name,
            call_back = call_back,
        }
    
        TipWGCtrl.Instance:ShowGetNewSkillView2(skill_cfg)
    end

    ---------------------------------------------

    local level_cahnge = false
    for i = 0, 10 do
        local cur_level = (old_mengling_item_data[i] or {}).level or 0
        local next_level = (new_mengling_item_data[i] or {}).level or 0

        if (cur_level ~= next_level) and (next_level - cur_level == 1) then
            level_cahnge = true
            break
        end
    end

    if level_cahnge then
        self.view:StartMengLingStrengthUpAmin()
    end
end

function MengLingWGCtrl:OnComposeResuit(protocol)
    if protocol.result == 0 then
        TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.f_hecheng, is_success = false})
    elseif protocol.result == 1 then
        TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_hecheng, is_success = true})
    end

    ViewManager.Instance:FlushView(GuideModuleName.MengLingView, TabIndex.mengling_compose)
end

function MengLingWGCtrl:GetMengLingViewSelectSeq()
    if self.view then
        return self.view.select_equip_suit_seq 
    end
end
--------------------------------------protocol_end--------------------------------------------
--获得是否选中自动吞噬
function MengLingWGCtrl:GetIsAutoTunShi()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	return PlayerPrefsUtil.GetInt("mengling_auto_tunshi" .. role_id) == 1
end

--设置自动吞噬
function MengLingWGCtrl:SetIsAutoTunShi()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local save_num = PlayerPrefsUtil.GetInt("mengling_auto_tunshi" .. role_id)
	local value = save_num == COMMON_GAME_ENUM.ONE and COMMON_GAME_ENUM.ZERO or COMMON_GAME_ENUM.ONE
	PlayerPrefsUtil.SetInt("mengling_auto_tunshi" .. role_id, value)
end

--设置蓝紫装备的自动吞噬
function MengLingWGCtrl:AutoTunShiEquip()
	local destroy_item_list = {}
    local bag_item_list = self.data:GetMengLingBagDataList()

	for k,v in pairs(bag_item_list) do
        local _, item_color = ItemWGData.Instance:GetItemColor(v.item_id)
        if item_color <= GameEnum.ITEM_COLOR_PURPLE and self.data:CheckCanResolve(v.item_id) then
            table.insert(destroy_item_list, {item_id = v.item_id, bag_index = v.index, count = v.num})
        end
	end

    if not IsEmptyTable(destroy_item_list) then
        MengLingWGCtrl.Instance:OnCSDreamSpiritEquipDecompos(#destroy_item_list, destroy_item_list)
    end

    if self.tunshi_timer then
		GlobalTimerQuest:CancelQuest(self.tunshi_timer)
		self.tunshi_timer = nil
	end
end

function MengLingWGCtrl:OnRoleAttrChange(attr_name, value, old_value)
	if attr_name == "level" then
        ViewManager.Instance:FlushView(GuideModuleName.MengLingView)
        RemindManager.Instance:Fire(RemindName.MengLingBag)
        RemindManager.Instance:Fire(RemindName.MengLingStrong)
	end
end

function MengLingWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
        if self.data:IsMengLingSXDItem(change_item_id) then
            RemindManager.Instance:Fire(RemindName.MengLingSXD)
            ViewManager.Instance:FlushView(GuideModuleName.MengLingView)
        end

        if self.data:IsMengLingComposeItem(change_item_id) then
            RemindManager.Instance:Fire(RemindName.MengLingCompose)
            ViewManager.Instance:FlushView(GuideModuleName.MengLingView, TabIndex.mengling_compose)
        end

        -- if self.data:IsMengLingCostItem(change_item_id) then
        --     self.data:CalculationMengLingSuitRemind()
        --     RemindManager.Instance:Fire(RemindName.MengLingView)
        --     ViewManager.Instance:FlushView(GuideModuleName.MengLingView)
        -- end
	end
end

function MengLingWGCtrl:OnDayPass()
    ViewManager.Instance:FlushView(GuideModuleName.MengLingView)
    RemindManager.Instance:Fire(RemindName.MengLingBag)
    RemindManager.Instance:Fire(RemindName.MengLingStrong)
end

function MengLingWGCtrl:CheckMengLingSkillUnLock(seq, old_data, new_data)
    local grade_cfg = self.data:GetGradeCfgBySeq(seq)

    if IsEmptyTable(grade_cfg) then
        return false
    end

    local old_unlock = self.data:IsMengLingSuitSkillUnLock(old_data, grade_cfg)
    if old_unlock then
        return false
    end

    local new_unlock = self.data:IsMengLingSuitSkillUnLock(new_data, grade_cfg)
    if not new_unlock then
        return false
    end

    return true
end
--------------------------------------open_view_start--------------------------------------------
-- function MengLingWGCtrl:OpenMengLingUpStoreView(data_info)
--     if self.mengling_up_store_view then
--         self.mengling_up_store_view:SetData(data_info)

--         if not self.mengling_up_store_view:IsOpen() then
--             self.mengling_up_store_view:Open()
--         else
--             self.mengling_up_store_view:Flush()
--         end
--     end
-- end

-- function MengLingWGCtrl:OpenMengLingInlayStoreView(seq, slot, hole, data_info)
--     if self.mengling_inlay_store_view then
--         self.mengling_inlay_store_view:SetData(seq, slot, hole, data_info)

--         if not self.mengling_inlay_store_view:IsOpen() then
--             self.mengling_inlay_store_view:Open()
--         else
--             self.mengling_inlay_store_view:Flush()
--         end
--     end
-- end

function MengLingWGCtrl:OpenMengLingStuffListView(data_list, grid_item, call_back)
    if self.mengling_stuff_list_view then
        self.mengling_stuff_list_view:SetData(data_list, grid_item, call_back)

        if not self.mengling_stuff_list_view:IsOpen() then
            self.mengling_stuff_list_view:Open()
        else
            self.mengling_stuff_list_view:Flush()
        end
    end
end

-- function MengLingWGCtrl:OpenMengLingComposeView()
--     -- if self.menglling_compose_view then
--     --     if not self.menglling_compose_view:IsOpen() then
--     --         self.menglling_compose_view:Open()
--     --     else
--     --         self.menglling_compose_view:Flush()
--     --     end
--     -- end
-- end

function MengLingWGCtrl:OpenMengLingSuitView(seq)
    if self.menglling_suit_view then
        self.menglling_suit_view:SetDataAndOpen(seq)
    end
end

function MengLingWGCtrl:OpenMengLingResolveView()
    if self.menglling_resolve_view then
        if not self.menglling_resolve_view:IsOpen() then
            self.menglling_resolve_view:Open()
        else
            self.menglling_resolve_view:Flush()
        end
    end
end

function MengLingWGCtrl:GetSelectMengLingSuitIndex()
    return self.view:GetSelectMengLingSuitIndex()
end

function MengLingWGCtrl:GetMengLingShowIndex()
    return self.view.show_index
end

--------------------------------------open_view_end--------------------------------------------