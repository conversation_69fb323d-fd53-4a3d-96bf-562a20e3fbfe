-- 排行界面
PositionalWarfareRankView = PositionalWarfareRankView or BaseClass(SafeBaseView)

function PositionalWarfareRankView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(1030, 594)})
    self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_positional_warfare_rank_view")
end

function PositionalWarfareRankView:OpenCallBack()
    PositionalWarfareWGCtrl.Instance:SendCrossLandWarOperate(CROSS_LAND_WAR_OPERATE_TYPE.RANK_INFO, CROSS_LAND_WAR_RANK_TYPE.DEVOTE)
    PositionalWarfareWGCtrl.Instance:SendCrossLandWarOperate(CROSS_LAND_WAR_OPERATE_TYPE.RANK_INFO, CROSS_LAND_WAR_RANK_TYPE.KILL)
end

function PositionalWarfareRankView:SetSelectRankType(is_select_score_rank)
    self.is_select_score_rank = is_select_score_rank
end

function PositionalWarfareRankView:LoadCallBack()
    if not self.score_menber_list then
        self.score_menber_list = AsyncListView.New(PWScoreRankItemCellRender, self.node_list.score_menber_list)
    end

    if not self.defeat_menber_list then
        self.defeat_menber_list = AsyncListView.New(PWDefeatRankItemCellRender, self.node_list.defeat_menber_list)
    end

    if not self.score_reward_list then
        self.score_reward_list = AsyncListView.New(ItemCell, self.node_list.score_reward_list)
        self.score_reward_list:SetStartZeroIndex(true)
    end
    
    if not self.defeat_reward_list then
        self.defeat_reward_list = AsyncListView.New(ItemCell, self.node_list.defeat_reward_list)
        self.defeat_reward_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list.btn_score_tip, BindTool.Bind(self.OnClickScoreTip, self))
    XUI.AddClickEventListener(self.node_list.btn_defeat_tip, BindTool.Bind(self.OnClickDefeatTip, self))

    self.node_list.title_view_name.text.text = Language.PositionalWarfare.RankView
    self.node_list.score_rank_tip.text.text = Language.PositionalWarfare.ScoreRankTip
    self.node_list.defeat_rank_tip.text.text = Language.PositionalWarfare.DefeatRankTip
end

function PositionalWarfareRankView:ReleaseCallBack()
    if self.score_menber_list then
        self.score_menber_list:DeleteMe()
        self.score_menber_list = nil
    end

    if self.defeat_menber_list then
        self.defeat_menber_list:DeleteMe()
        self.defeat_menber_list = nil
    end

    if self.score_reward_list then
        self.score_reward_list:DeleteMe()
        self.score_reward_list = nil
    end

    if self.defeat_reward_list then
        self.defeat_reward_list:DeleteMe()
        self.defeat_reward_list = nil
    end
end

function PositionalWarfareRankView:ShowIndexCallBack()
    self:SetTogSelect()
end

function PositionalWarfareRankView:SetTogSelect()
    local is_select_score_rank = self.is_select_score_rank or false
    self.node_list.tog_score_rank.toggle.isOn = is_select_score_rank
    self.node_list.rog_defeat_rank.toggle.isOn = not is_select_score_rank
end

function PositionalWarfareRankView:OnFlush()
    -- local score_rank_data_list = PositionalWarfareWGData.Instance:GetRankDataListByRankType(CROSS_LAND_WAR_RANK_TYPE.DEVOTE)
    -- local defeat_rank_data_list = PositionalWarfareWGData.Instance:GetRankDataListByRankType(CROSS_LAND_WAR_RANK_TYPE.KILL)

    local score_rank_data_list = PositionalWarfareWGData.Instance:GetPersonScoreRankRewardDataListCfg()
    local defeat_rank_data_list = PositionalWarfareWGData.Instance:GetPersonKillRankRewardDataListCfg()
    local no_score_rank_data = IsEmptyTable(score_rank_data_list)
    local no_defeat_rank_data = IsEmptyTable(defeat_rank_data_list)

    self.node_list.score_menber_no_data:CustomSetActive(no_score_rank_data)
    self.node_list.defeat_menber_no_data:CustomSetActive(no_defeat_rank_data)
    self.node_list.score_menber_list:CustomSetActive(not no_score_rank_data)
    self.node_list.defeat_menber_list:CustomSetActive(not no_defeat_rank_data)

    if not no_score_rank_data then
        self.score_menber_list:SetDataList(score_rank_data_list)
    end

    if not no_defeat_rank_data then
        self.defeat_menber_list:SetDataList(defeat_rank_data_list)
    end

    self:FlushMyScoreRankData()
    self:FlushMyDefeatRankData()
end

function PositionalWarfareRankView:FlushMyScoreRankData()
    local data_list = PositionalWarfareWGData.Instance:GetRankDataListByRankType(CROSS_LAND_WAR_RANK_TYPE.DEVOTE)
    local my_uuid = RoleWGData.Instance:GetUUid()
    local my_data = {}
    local reward_data_list = {}

    if not IsEmptyTable(data_list) then
        for k, v in pairs(data_list) do
            if v.uuid == my_uuid then
                my_data = v
                break
            end
        end
    end

    if IsEmptyTable(my_data) then
        self.node_list.score_rank_id.text.text = "--"
        self.node_list.score_rank_id:CustomSetActive(true)
        self.node_list.score_rank_icon:CustomSetActive(false)
        self.node_list.score_score.text.text = "--"
    else
        local is_top_three = my_data.rank_id <= 3
        self.node_list.score_rank_id:CustomSetActive(not is_top_three)
        self.node_list.score_rank_icon:CustomSetActive(is_top_three)
        self.node_list.score_score.text.text = my_data.rank_value

        if is_top_three then
            self.node_list.score_rank_icon.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. my_data.rank_id))
        else
            self.node_list.score_rank_id.text.text = my_data.rank_id
        end

        local rank_cfg = PositionalWarfareWGData.Instance:GetPersonScoreRankRewardCfg( my_data.rank_id)
        reward_data_list = rank_cfg and rank_cfg.reward_item or {}
    end

    self.score_reward_list:SetDataList(reward_data_list)
    self.node_list.score_server_id.text.text = RoleWGData.Instance:GetCurServerId()
    self.node_list.score_role_name.text.text = RoleWGData.Instance:GetAttr("name")
end

function PositionalWarfareRankView:FlushMyDefeatRankData()
    local data_list = PositionalWarfareWGData.Instance:GetRankDataListByRankType(CROSS_LAND_WAR_RANK_TYPE.KILL)
    local my_uuid = RoleWGData.Instance:GetUUid()
    local my_data = {}
    local reward_data_list = {}

    if not IsEmptyTable(data_list) then
        for k, v in pairs(data_list) do
            if v.uuid == my_uuid then
                my_data = v
                break
            end
        end
    end

    if IsEmptyTable(my_data) then
        self.node_list.defeat_rank_id.text.text = "--"
        self.node_list.defeat_rank_id:CustomSetActive(true)
        self.node_list.defeat_rank_icon:CustomSetActive(false)
        self.node_list.defeat_defeat_value.text.text = "--"
    else
        local is_top_three = my_data.rank_id <= 3
        self.node_list.defeat_rank_id:CustomSetActive(not is_top_three)
        self.node_list.defeat_rank_icon:CustomSetActive(is_top_three)
        self.node_list.defeat_defeat_value.text.text = my_data.rank_value

        if is_top_three then
            self.node_list.defeat_rank_icon.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. my_data.rank_id))
        else
            self.node_list.defeat_rank_id.text.text = my_data.rank_id
        end

        local rank_cfg = PositionalWarfareWGData.Instance:GetPersonKillRankRewardCfg(my_data.rank_id)
        reward_data_list = rank_cfg and rank_cfg.reward_item or {}
    end

    self.defeat_reward_list:SetDataList(reward_data_list)
    self.node_list.defeat_server_id.text.text = RoleWGData.Instance:GetCurServerId()
    self.node_list.defeat_role_name.text.text = RoleWGData.Instance:GetAttr("name")
end

function PositionalWarfareRankView:OnClickScoreTip()
    RuleTip.Instance:SetContent(Language.PositionalWarfare.ScoreRankTipContent, Language.PositionalWarfare.ScoreRankTipTitle)
end

function PositionalWarfareRankView:OnClickDefeatTip()
    RuleTip.Instance:SetContent(Language.PositionalWarfare.DefeatRankTipContent, Language.PositionalWarfare.DefeatRankTipTitle)
end

-------------------------------------------------PWScoreRankItemCellRender----------------------------------------------
PWScoreRankItemCellRender = PWScoreRankItemCellRender or BaseClass(BaseRender)

function PWScoreRankItemCellRender:LoadCallBack()
    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(true)
    end
end

function PWScoreRankItemCellRender:__delete()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function PWScoreRankItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local bundle, asset
    local is_top_three = self.data.rank_id <= 3

    if is_top_three then
        bundle, asset = ResPath.GetCommonImages("a3_ty_list_" .. self.data.rank_id)

        self.node_list.rank_icon.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. self.data.rank_id))
    else
        bundle, asset = ResPath.GetCommonImages("a3_ty_bg1_3")
    end

    self.node_list.bg.image:LoadSprite(bundle, asset)
    self.node_list.rank_id:CustomSetActive(not is_top_three)
    self.node_list.rank_icon:CustomSetActive(is_top_three)
    self.node_list.rank_id.text.text = self.data.rank_id
    self.reward_list:SetDataList(self.data.cfg.reward_item)

    local role_rank_data = PositionalWarfareWGData.Instance:GetRankInfoData(CROSS_LAND_WAR_RANK_TYPE.DEVOTE, self.data.rank_id)
    if IsEmptyTable(role_rank_data) then
        self.node_list.server_id.text.text = "--"
        self.node_list.role_name.text.text = Language.PositionalWarfare.DescRankNoRoleDataStr
        self.node_list.score.text.text = 0
    else
        self.node_list.server_id.text.text = role_rank_data.usid.temp_low
        self.node_list.role_name.text.text = role_rank_data.name
        self.node_list.score.text.text = role_rank_data.rank_value
    end

    -- self.node_list.server_id.text.text = self.data.usid.temp_low
    -- self.node_list.role_name.text.text = self.data.name
    -- self.node_list.score.text.text = self.data.rank_value
end

-------------------------------------------------PWDefeatRankItemCellRender----------------------------------------------
PWDefeatRankItemCellRender = PWDefeatRankItemCellRender or BaseClass(BaseRender)

function PWDefeatRankItemCellRender:LoadCallBack()
    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(true)
    end
end

function PWDefeatRankItemCellRender:__delete()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function PWDefeatRankItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local bundle, asset
    local is_top_three = self.data.rank_id <= 3

    if is_top_three then
        bundle, asset = ResPath.GetCommonImages("a3_ty_list_" .. self.data.rank_id)

        self.node_list.rank_icon.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. self.data.rank_id))
    else
        bundle, asset = ResPath.GetCommonImages("a3_ty_bg1_3")
    end

    self.node_list.bg.image:LoadSprite(bundle, asset)
    self.node_list.rank_id:CustomSetActive(not is_top_three)
    self.node_list.rank_icon:CustomSetActive(is_top_three)
    self.node_list.rank_id.text.text = self.data.rank_id
    self.reward_list:SetDataList(self.data.cfg.reward_item)

    local role_rank_data = PositionalWarfareWGData.Instance:GetRankInfoData(CROSS_LAND_WAR_RANK_TYPE.KILL, self.data.rank_id)
    if IsEmptyTable(role_rank_data) then
        self.node_list.server_id.text.text = "--"
        self.node_list.role_name.text.text = Language.PositionalWarfare.DescRankNoRoleDataStr
        self.node_list.defeat_value.text.text = 0
    else
        self.node_list.server_id.text.text = role_rank_data.usid.temp_low
        self.node_list.role_name.text.text = role_rank_data.name
        self.node_list.defeat_value.text.text = role_rank_data.rank_value
    end

    -- self.node_list.server_id.text.text = self.data.usid.temp_low
    -- self.node_list.role_name.text.text = self.data.name
    -- self.node_list.defeat_value.text.text = self.data.rank_value
end