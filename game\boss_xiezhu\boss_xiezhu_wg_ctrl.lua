require("game/boss_xiezhu/boss_xiezhu_wg_data")
require("game/boss_xiezhu/boss_xiezhu_list_view")
require("game/boss_xiezhu/boss_xiezhu_thank_view")
require("game/boss_xiezhu/boss_xiezhu_info_view")
require("game/boss_xiezhu/boss_xiezhu_rule_view")


-- boss协助 新 （现在策划的需求只针对蛮荒boss）
BossXiezhuWGCtrl = BossXiezhuWGCtrl or BaseClass(BaseWGCtrl)

function BossXiezhuWGCtrl:__init()
	if BossXiezhuWGCtrl.Instance then
		error("[BossXiezhuWGCtrl]:Attempt to create singleton twice!")
	end
	BossXiezhuWGCtrl.Instance = self

	self.boss_xiezhu_data = BossXiezhuWGData.New()
	self.boss_xiezhu_list_view = BossXiezhuListView.New(GuideModuleName.BossXiezhuListView)
	self.boss_xiezhu_thank_view = BossXiezhuThanksView.New(GuideModuleName.BossXiezhuThanksView)
	self.boss_xiezhu_info_view = BossXiezhuInfoView.New(GuideModuleName.BossXiezhuInfoView)
	self.boss_xiezhu_rule_view = BossXiezhuRuleView.New(GuideModuleName.BossXiezhuRuleView)

	self:RegisterAllProtocals()
end

function BossXiezhuWGCtrl:__delete()
	BossXiezhuWGCtrl.Instance = nil

	if self.boss_xiezhu_data then
		self.boss_xiezhu_data:DeleteMe()
		self.boss_xiezhu_data = nil
	end

	if self.boss_xiezhu_list_view then
		self.boss_xiezhu_list_view:DeleteMe()
		self.boss_xiezhu_list_view = nil
	end

	if self.boss_xiezhu_thank_view then
		self.boss_xiezhu_thank_view:DeleteMe()
		self.boss_xiezhu_thank_view = nil
	end

	if self.boss_xiezhu_info_view then
		self.boss_xiezhu_info_view:DeleteMe()
		self.boss_xiezhu_info_view = nil
	end

	if self.boss_xiezhu_rule_view then
		self.boss_xiezhu_rule_view:DeleteMe()
		self.boss_xiezhu_rule_view = nil
	end

end

function BossXiezhuWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(CSMonsterAssistReq)
	self:RegisterProtocol(SCMonsterAssistHelpmeRole,"OnSCMonsterAssistHelpmeRole")
	self:RegisterProtocol(SCMonsterAssistInvokeChange,"OnSCMonsterAssistInvokeChange")
	self:RegisterProtocol(SCMonsterAssistInvokeListInfo,"OnSCMonsterAssistInvokeListInfo")
	self:RegisterProtocol(SCMonsterAssistRoleStatus,"OnSCMonsterAssistRoleStatus")
	self:RegisterProtocol(SCMonsterAssistThankNotice,"OnSCMonsterAssistThankNotice")
	self:RegisterProtocol(SCMonsterAssistRoleHurt,"OnSCMonsterAssistRoleHurt")
end

function BossXiezhuWGCtrl:SendCSMonsterAssistReq(opera_type,param_0,param_1,param_2,param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSMonsterAssistReq)
	protocol.opera_type = opera_type
	protocol.param_0 = param_0 or 0
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol:EncodeAndSend()
end

function BossXiezhuWGCtrl:OnSCMonsterAssistHelpmeRole(protocol)
	self.boss_xiezhu_data:SetSCMonsterAssistHelpmeRole(protocol)
	if self.boss_xiezhu_info_view:IsOpen() then
		self.boss_xiezhu_info_view:Flush()
	end

	local state = BossXiezhuWGData.Instance:GetXiezhuStatus()
	local role_list = Scene.Instance:GetRoleList()
	if state == ASSIST_STATUS.ASSIST_STATUS_INVOKE and not IsEmptyTable(role_list) then
		for k,v in pairs(role_list) do
			if not v:IsDeleted() then
				v:SetAttr("xiezhu_ing")
			end
		end
	end
	
end

function BossXiezhuWGCtrl:OnSCMonsterAssistInvokeChange(protocol)
	self.boss_xiezhu_data:SetSCMonsterAssistInvokeChange(protocol)
	if self.boss_xiezhu_list_view:IsOpen() then
		self.boss_xiezhu_list_view:Flush()
	end
	self:BossXiezhuInvateTip()
end

function BossXiezhuWGCtrl:OnSCMonsterAssistInvokeListInfo(protocol)
	self.boss_xiezhu_data:SetSCMonsterAssistInvokeListInfo(protocol)
	if self.boss_xiezhu_list_view:IsOpen() then
		self.boss_xiezhu_list_view:Flush()
	end
	self:BossXiezhuInvateTip()
end

function BossXiezhuWGCtrl:OnSCMonsterAssistRoleStatus(protocol)
	local old_status = BossXiezhuWGData.Instance:GetXiezhuStatus()
	local cancel_reason = protocol.cancel_reason
	self.boss_xiezhu_data:SetSCMonsterAssistRoleStatus(protocol)
	if old_status ~= protocol.status and old_status == ASSIST_STATUS.ASSIST_STATUS_HELP_OTHER then
		BossAssistWGCtrl.Instance:ClearNormalHurtList()
		BossXiezhuWGCtrl.Instance:CloseBossXiezhuInfoView()
		BossXiezhuWGData.Instance:ClearHelpMeList()
		self:CancelXiezhuStatusReason(cancel_reason)
	elseif old_status ~= protocol.status and old_status == ASSIST_STATUS.ASSIST_STATUS_INVOKE then
		BossXiezhuWGData.Instance:ClearHelpMeList()
		BossXiezhuWGData.Instance:ClearXiezhuHurt()
	end
	
	if self.boss_xiezhu_info_view:IsOpen() then
		self.boss_xiezhu_info_view:Flush()
	else
		if protocol.status == ASSIST_STATUS.ASSIST_STATUS_HELP_OTHER then
			if not TaskWGCtrl.Instance:IsFly() then
				self:OpenBossXiezhuInfoView()
			else
				TaskWGCtrl.Instance:AddFlyUpList(BindTool.Bind(self.OpenBossXiezhuInfoView,self))
			end
		end
	end
	if self.boss_xiezhu_list_view:IsOpen() then
		self.boss_xiezhu_list_view:Flush()
	end
	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		main_role:SetAttr("xiezhu_ing")
	end

	local role_list = Scene.Instance:GetRoleList()
	if not IsEmptyTable(role_list) then
		for k,v in pairs(role_list) do
			if not v:IsDeleted() then
				v:SetAttr("xiezhu_ing")
			end
		end
	end

	self:BossXiezhuInvateTip()
	BossAssistWGCtrl.Instance:FlushNormalHurtXiezhu()
end

function BossXiezhuWGCtrl:OnSCMonsterAssistThankNotice()
	self.boss_xiezhu_thank_view:Open()
end

function BossXiezhuWGCtrl:OnSCMonsterAssistRoleHurt(protocol)
	self.boss_xiezhu_data:SetSCMonsterAssistRoleHurt(protocol)
end

--协助者取消协助状态
function BossXiezhuWGCtrl:CancelXiezhuStatusReason(cancel_reason)
	if cancel_reason == CANCEL_STATUS_REASON.CANCEL_STATUS_REASON_BOSS_DIE 
		or cancel_reason == CANCEL_STATUS_REASON.CANCEL_STATUS_REASON_INVOKE
		or cancel_reason == CANCEL_STATUS_REASON.CANCEL_STATUS_REASON_SELF
		or cancel_reason == CANCEL_STATUS_REASON.CANCEL_STATUS_REASON_TIMESTAMP
		or cancel_reason == CANCEL_STATUS_REASON.CANCEL_STATUS_REASON_LEAVE_FB then
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
		local scene_logic = Scene.Instance:GetSceneLogic()
		if scene_logic ~= nil then
			scene_logic:ClearGuaJiInfo()
		end
		if cancel_reason == CANCEL_STATUS_REASON.CANCEL_STATUS_REASON_INVOKE then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.BossXiezhu.ZhaoJiCancelTip)
		elseif cancel_reason == CANCEL_STATUS_REASON.CANCEL_STATUS_REASON_BOSS_DIE then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.BossXiezhu.XieZhuChengGong)
		end
	-- elseif cancel_reason == CANCEL_STATUS_REASON.CANCEL_STATUS_REASON_INVOKE then
	-- 	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
	-- 	local scene_logic = Scene.Instance:GetSceneLogic()
	-- 	if scene_logic ~= nil then
	-- 		scene_logic:ClearGuaJiInfo()
	-- 	end
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.BossXiezhu.ZhaoJiCancelTip)
	elseif cancel_reason == CANCEL_STATUS_REASON.CANCEL_STATUS_REASON_ATTACK then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BossXiezhu.AttakOtherBoss)
	end
end

function BossXiezhuWGCtrl:RemoveInvokeRoleInfo(uid,index)
	self.boss_xiezhu_data:RemoveInvokeRoleInfo(uid,index)
	if self.boss_xiezhu_list_view:IsOpen() then
		self.boss_xiezhu_list_view:Flush()
	end
	self:BossXiezhuInvateTip()
end

function BossXiezhuWGCtrl:BossXiezhuInvateTip()
	local is_empty = self.boss_xiezhu_data:GetIsEmptyInvokeList()
	local xiezhu_count = BossXiezhuWGData.Instance:GetAssistCount()
    local xiezhu_num = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_BOSS_ASSIST)
    local state = BossXiezhuWGData.Instance:GetXiezhuStatus()
    local can_xiezhu = xiezhu_num < xiezhu_count
	if not is_empty and can_xiezhu then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.BOSS_XIEZHU, 1,function ()
			ViewManager.Instance:Open(GuideModuleName.BossXiezhuListView)
			return true
		end)
	else
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.BOSS_XIEZHU,0)
	end
end

function BossXiezhuWGCtrl:OpenBossXiezhuInfoView()
	local is_xiezhu_scene = BossXiezhuWGData.Instance:IsInXieZhuScene()
	if is_xiezhu_scene then
		if not self.boss_xiezhu_info_view:IsOpen() then
			self.boss_xiezhu_info_view:Open()
		end
	end
end

function BossXiezhuWGCtrl:CloseBossXiezhuInfoView()
	local state = BossXiezhuWGData.Instance:GetXiezhuStatus()
	if state ~= ASSIST_STATUS.ASSIST_STATUS_HELP_OTHER then
		if self.boss_xiezhu_info_view:IsOpen() then
			self.boss_xiezhu_info_view:Close()
		end
	end
end

function BossXiezhuWGCtrl:GoToXizhuBoss()
	local xiezhu_role_id = BossXiezhuWGData.Instance:GetGoXiezhuRoleId()
	local target_info = BossXiezhuWGData.Instance:GetInvokeInfoByRoleId(xiezhu_role_id)

	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic ~= nil then
		scene_logic:ClearGuaJiInfo()
	end

	if xiezhu_role_id > 0 and not IsEmptyTable(target_info) then
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
		MoveCache.SetEndType(MoveEndType.FightByMonsterId)
		local pos_x = 0
		local pos_y = 0
		local scene_id = target_info.scene_id
		if target_info.boss_type == ASSIST_BOSS_TYPE.ASSIST_BOSS_TYPE_CROSS_BOSS then
			local boss_cfg = BossWGData.Instance:GetBossInfoByBossId(target_info.monster_id)
			local pos = target_info.pos
			pos_x = pos.pos_x
			pos_y = pos.pos_y
			if pos_x <= 0 and pos_y <= 0 then
				pos_x = boss_cfg.flush_pos_x
				pos_y = boss_cfg.flush_pos_y
			end
		end
		MoveCache.param1 = target_info.monster_id
		MoveCache.param2 = target_info.monster_key
		if pos_x <= 0 and pos_y <= 0 then
			print_error(">>>>>>>>>>boss 配置获取坐标为空!!!",target_info)
		end

		local range = BossWGData.Instance:GetMonsterRangeByid(target_info.monster_id)
		GuajiWGCtrl.Instance:SetMoveToPosCallBack(function()
			local scene_logic = Scene.Instance:GetSceneLogic()
			if scene_logic ~= nil then
				scene_logic:ClearGuaJiInfo()
				local mian_role = Scene.Instance:GetMainRole()
				if mian_role ~= nil and not mian_role:IsDeleted() then
					local m_pos_x, m_pos_y = mian_role:GetLogicPos()
					if GameMath.GetDistance(pos_x, pos_y, m_pos_x, m_pos_y, false) <= range * range then
						scene_logic:SetGuaJiInfoPos(m_pos_x, m_pos_y, range, target_info.monster_id)
					end
				end
			end
		end)

		GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_x, pos_y, range)
		BossXiezhuWGCtrl.Instance:OpenBossXiezhuInfoView()
		BossAssistWGCtrl.Instance:OpenNormalHurtList()
	end
end

function BossXiezhuWGCtrl:GoToXiezhuBossPos(scene_id,boss_id,boss_type)
	BossWGData.Instance:ClearCurSelectBossID()
	BossWGData.Instance:SetCurSelectBossID(0, 0, GameEnum.BossXiezhu)
    if Scene.Instance:GetSceneId() == scene_id then
		self:GoToXizhuBoss()
		return
	end

	if boss_type == ASSIST_BOSS_TYPE.ASSIST_BOSS_TYPE_CROSS_BOSS then
		BossWGCtrl.Instance:HelpGuildKFBoss(scene_id)
	end
end

function BossXiezhuWGCtrl:SendXiezhuBossInfo(invoke_info)
	if IsEmptyTable(invoke_info) then 
		print_error(">>>>>>>>>> 请求协助的数据有问题!!!!")
		return 
    end
    -- local main_role = Scene.Instance:GetMainRole()
    -- if main_role and main_role:IsFightState() then
    --     SysMsgWGCtrl.Instance:ErrorRemind(Language.BossXiezhu.CanNotAssistInFight)
    --     return 
    -- end

	--普通场景  (别的国家主城不给跳) 世界boss 魔王巢穴 蛮荒神兽 才能发送协助请求
	local scene_type = Scene.Instance:GetSceneType()
	if (scene_type == SceneType.Common and not IS_ON_CROSSSERVER) or scene_type == SceneType.WorldBoss or 
		scene_type == SceneType.VIP_BOSS or scene_type == SceneType.KF_BOSS then

		local opera_type = MONSTER_ASSIST_REQ.MONSTER_ASSIST_REQ_REPLY_HELP
		local param_0 = invoke_info.uid
		local state = BossXiezhuWGData.Instance:GetXiezhuStatus()
		local target_info = BossXiezhuWGData.Instance:GetTargetRoleNode()
		local target_uid = target_info and target_info.uid or 0

		local send_xiezhu_fun = function ()
			BossXiezhuWGCtrl.Instance:SendCSMonsterAssistReq(opera_type,param_0)
			BossXiezhuWGData.Instance:SetGoXiezhuRoleId(invoke_info.uid)
			local scene_id = invoke_info.scene_id
			BossXiezhuWGCtrl.Instance:GoToXiezhuBossPos(scene_id,invoke_info.monster_id,invoke_info.boss_type)
			if ViewManager.Instance:IsOpen(GuideModuleName.BossXiezhuListView) then
				ViewManager.Instance:Close(GuideModuleName.BossXiezhuListView)
			end
		end

		--如果处于战斗状态 且 目标是boss 弹出确认框
		local main_role = Scene.Instance:GetMainRole()
		if main_role:IsFightState() and GuajiCache.target_obj and GuajiCache.target_obj:IsBoss() 
		and (state == ASSIST_STATUS.ASSIST_STATUS_NOTHING or state == ASSIST_STATUS.ASSIST_STATUS_INVOKE) then
			local str = Language.BossXiezhu.InFightOtherBoss
			TipWGCtrl.Instance:OpenAlertTips(str,send_xiezhu_fun)
			return
  		end
		
		if state == ASSIST_STATUS.ASSIST_STATUS_HELP_OTHER and target_uid ~= invoke_info.uid then
			local target_name = target_info and target_info.name or ""
			local str = string.format(Language.BossXiezhu.InXiezhuOtherRole,target_name,invoke_info.name)
			TipWGCtrl.Instance:OpenAlertTips(str,send_xiezhu_fun)
		else
			send_xiezhu_fun()
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BossXiezhu.CurSceneNoXiezhu)
	end
end

function BossXiezhuWGCtrl:CancelXiezhuBoss()
	local target_info = BossXiezhuWGData.Instance:GetTargetRoleNode()
	local role_id = target_info and target_info.uid or 0
	local opera_type = MONSTER_ASSIST_REQ.MONSTER_ASSIST_REQ_CANCEL_REPLY_HELP
	BossXiezhuWGCtrl.Instance:SendCSMonsterAssistReq(opera_type)
	BossXiezhuWGCtrl.Instance:RemoveInvokeRoleInfo(role_id)
	BossXiezhuWGData.Instance:SetGoXiezhuRoleId(0)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
end

function BossXiezhuWGCtrl:ShowOrHideXieZhuRule()
	if not self.boss_xiezhu_rule_view:IsOpen() then
		self.boss_xiezhu_rule_view:Open()
	else
		self.boss_xiezhu_rule_view:Close()
	end
end

function BossXiezhuWGCtrl:ShowOrHideXiezhuInfoBtn(enable)
	if self.boss_xiezhu_info_view:IsOpen() and self.boss_xiezhu_info_view:IsLoaded() then
		self.boss_xiezhu_info_view:ShowOrHideXiezhuInfoBtn(enable)
	end
end

function BossXiezhuWGCtrl:GetKFBossXieZhuInfoView()
	return self.boss_xiezhu_info_view
end
