DayNightWGData = DayNightWGData or BaseClass()

local CONTROL_TYPE = {
	CONTROL_TASK = 1,
	CONTROL_REGULAR = 2,
}


function DayNightWGData:__init()
	if DayNightWGData.Instance then
		error("[DayNightWGData] Attempt to create singleton twice!")
		return
	end
	DayNightWGData.Instance = self

	self.day_night_cfg = ConfigManager.Instance:GetAutoConfig("day_night_cfg_auto")
	self.scene_task_cfg = ListToMapList(self.day_night_cfg.scene_task, "scene_id")
	self.base_cfg = self.day_night_cfg.other[1]
	self.scene_regular_cfg = self.day_night_cfg.scen_regular
	self.time_day_night_cfg = self.day_night_cfg.time_day_night

	self.split_ignore_scene_list = {}
	if self.base_cfg and self.base_cfg.ignore_scene_id then
		local ignore_id_list = Split(self.base_cfg.ignore_scene_id, ",")

		if #ignore_id_list > 1 then
			for i, scene_id_str in ipairs(ignore_id_list) do
				local scene_id = tonumber(scene_id_str) or 0
				self.split_ignore_scene_list[scene_id] = true
			end
		else
			self.split_ignore_scene_list[self.base_cfg.ignore_scene_id] = true
		end
	end
end

function DayNightWGData:__delete()
	DayNightWGData.Instance = nil
end

-- 获取当前对应场景的配置列表
function DayNightWGData:GetControlTaskList(scene_id)
	local empty = {}
	return (self.scene_task_cfg or empty)[scene_id]
end

-- 是否是忽略的场景
function DayNightWGData:GetIsIgnoreScene(scene_id)
	local empty = {}
	return (self.split_ignore_scene_list or empty)[scene_id]
end

-- 获取当前对应场景的配置列表
function DayNightWGData:GetControlRegular(scene_id)
	local empty = {}
	return (self.scene_regular_cfg or empty)[scene_id]
end

-- 检测是否是忽略的场景
function DayNightWGData:CheckDayNightChangeForIgnoreScene()
	local scene_id = Scene.Instance:GetSceneId()
	local is_ignore = self:GetIsIgnoreScene(scene_id)
	return is_ignore
end

-- 检测是否存在变化
function DayNightWGData:CheckDayNightChangeForTask()
	--优先检测任务已接取区间
	local scene_id = Scene.Instance:GetSceneId()
	local list = self:GetControlTaskList(scene_id)
	if list and #list > 0 then
		for i, task_data in ipairs(list) do
			-- 没有完成最大的且接受了最小的或者完成了最小的任务
			if (not TaskWGData.Instance:GetTaskIsCompleted(task_data.task_end)) and 
				TaskWGData.Instance:GetTaskIsCompleted(task_data.task_start) or TaskWGData.Instance:GetTaskIsAccepted(task_data.task_start) then
				return task_data.moment_index
			end
		end
	end

	return nil
end

-- 是否固定
function DayNightWGData:CheckDayNightChangeForRegular()
	local scene_id = Scene.Instance:GetSceneId()
	local data = self:GetControlRegular(scene_id)
	if data and data.regular == 1 then
		return data.moment_index
	end

	return nil
end

-- 走时间
function DayNightWGData:CheckDayNightChangeForTime()
	if not self.time_day_night_cfg then
		return nil
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()

	for k, time in pairs(self.time_day_night_cfg) do
		if time.start_time and time.end_time and server_time then
			local start_str_per = string.format("%02d:%02d",time.start_time / 100, time.start_time % 100)
			local start_per = TimeToSecond(start_str_per)	--开始的时间戳
	
			local end_str_per = string.format("%02d:%02d",time.end_time / 100, time.end_time % 100)
			local end_per = TimeToSecond(end_str_per)	--结束的时间戳

			if start_per and end_per and server_time >= start_per and server_time < end_per then
				return time.moment_index
			end
		end
	end

	return nil
end