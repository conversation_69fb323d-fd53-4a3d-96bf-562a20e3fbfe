HeFuJuLingWGData = HeFuJuLingWGData or BaseClass()

function HeFuJuLingWGData:__init()
	if HeFuJuLingWGData.Instance then
		ErrorLog("[HeFuJuLingWGData] Attemp to create a singleton twice !")
	end

	HeFuJuLingWGData.Instance = self

	self.juhun_cfg = ConfigManager.Instance:GetAutoConfig("combine_server_activity_juling_zhuzhen_auto")

	self:CreateJuLingInfoList()

	--todo
	MergeActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_JULINGZHUZHEN, {[1] = OPERATION_EVENT_TYPE.LEVEL},
										BindTool.Bind(self.GetHeFuZhuLingIsOpen, self), BindTool.Bind(self.GetJuLingZhuZhenRemind, self))
	--todo
	RemindManager.Instance:Register(RemindName.HeFu_JuLingZhuZhen, BindTool.Bind(self.GetJuLingZhuZhenRemind, self))

	self.garden_flag = 0
end

function HeFuJuLingWGData:__delete()
    self.cache_invite_cd_list = nil
	HeFuJuLingWGData.Instance = nil
    RemindManager.Instance:UnRegister(RemindName.HeFu_JuLingZhuZhen)
    if CountDownManager.Instance and CountDownManager.Instance:HasCountDown("merge_juling_invite_cd_time") then
        CountDownManager.Instance:RemoveCountDown("merge_juling_invite_cd_time")
    end
end

function HeFuJuLingWGData:CreateJuLingInfoList()
	--个人信息（自己）初始化
	self.juling_info = {}
	self.juling_info.lingbao_list = {}
	self.juling_info.zhulingtai_list = {}
	self.juling_info.activity_start_time = 0
	self.juling_info.today_total_help_other_times = 0
	self.juling_info.today_total_help_lover_timse = 0
	self.juling_info.uid = 0
	self.juling_info.name = ""

	--个人信息（其他人）初始化
	self.other_juling_info = {}
	self.other_juling_info.lingbao_list = {}
	self.other_juling_info.zhulingtai_list = {}
	self.other_juling_info.activity_start_time = 0
	self.other_juling_info.today_total_help_other_times = 0
	self.other_juling_info.uid = 0
	self.other_juling_info.name = ""

	--我帮浇过的人信息初始化
	self.help_friend_info = {}
	self.help_friend_info.count = 0
	self.help_friend_info.help_param_list = {}

	--一些人的帮浇次数信息初始化
	self.help_me_info = {}
	self.help_me_info.count = 0
	self.help_me_info.help_me_times_list = {}

	--其他人得花盆信息列表初始化
	self.other_role_info_list = {}
	self.other_role_info_list.count = 0
	self.other_role_info_list.other_role_info = {}

	--自己的浇水日志
	self.zhuling_log_list = {}
	self.zhuling_log_list.count = 0
	self.zhuling_log_list.record_list = {}

	for i=1,6 do
		self.zhuling_log_list.record_list[i] = {}
		self.zhuling_log_list.record_list[i].name = ""
		self.zhuling_log_list.record_list[i].uid = 0
		self.zhuling_log_list.record_list[i].role_info = {}
	end

	--其他人的
	self.other_zhuling_log_list = {}
	self.other_zhuling_log_list.count = 0
	self.other_zhuling_log_list.record_list = {}
	for i=1,6 do
		self.other_zhuling_log_list.record_list[i] = {}
		self.other_zhuling_log_list.record_list[i].name = ""
		self.other_zhuling_log_list.record_list[i].uid = 0
		self.other_zhuling_log_list.record_list[i].role_info = {}
	end

	self.friend_list = {}

	self.member_list = {}

end

-- 10401 个人信息下发
function HeFuJuLingWGData:SetJuLingZhuZhenInfo(protocol)
	self.juling_info.lingbao_list = protocol.lingbao_list
	self.juling_info.zhulingtai_list = protocol.zhulingtai_list
	self.juling_info.activity_start_time = protocol.activity_start_time
	self.juling_info.today_total_help_other_times = protocol.today_total_help_other_times
	self.juling_info.today_total_help_lover_timse = protocol.today_total_help_lover_timse
	self.juling_info.uid = protocol.uid
	self.juling_info.name = protocol.name
end

-- 10404 下发一些人的帮浇次数
function HeFuJuLingWGData:SetJuLingZhuZhenHelpMeInfo(protocol)
	self.help_me_info.count = protocol.count
	self.help_me_info.help_me_times_list = protocol.help_me_times_list
end

-- 10405 通知被某人邀请注灵
function HeFuJuLingWGData:SetJuLingZhuZhenInviteHelpOtherAck(protocol)
    --print_error("通知被某人邀请注灵")
	if self.other_role_info_list.other_role_info[protocol.uid] then
		self.other_role_info_list.other_role_info[protocol.uid].invite_me = protocol.type
    end
    self:UpdateJuLingFriendList()
    self:UpdateJuLingMemberList()
end

-- 10407 其他玩家盆栽信息
function HeFuJuLingWGData:SetJuLingZhuZhenOtherRoleLingBao(protocol)
	self.other_role_info_list.count = protocol.count
	for i=1,protocol.count do
		self.other_role_info_list.other_role_info[protocol.other_role_info[i].uid] = {}
		self.other_role_info_list.other_role_info[protocol.other_role_info[i].uid] = protocol.other_role_info[i]
	end


	self:UpdateJuLingFriendList()
	self:UpdateJuLingMemberList()
end

-- 10408 其他玩家盆栽信息
function HeFuJuLingWGData:SetOtherJuLingZhuZhenSingleRoleInfo(protocol)
	self.other_juling_info.lingbao_list = protocol.lingbao_list
	self.other_juling_info.zhulingtai_list = protocol.zhulingtai_list
	self.other_juling_info.activity_start_time = protocol.activity_start_time
	self.other_juling_info.today_total_help_other_times = protocol.today_total_help_other_times
	self.other_juling_info.uid = protocol.uid
	self.other_juling_info.name = protocol.name
end

--自己的注灵日志
function HeFuJuLingWGData:SetJuLingZhuZhenZhuLingLog(protocol)
	self.zhuling_log_list.count = protocol.count
	for i=1,6 do
		if protocol.record_list[i] and protocol.record_list[i].uid > 0 then
			self.zhuling_log_list.record_list[i].uid = protocol.record_list[i].uid
			self.zhuling_log_list.record_list[i].name = protocol.record_list[i].name
		else
			self.zhuling_log_list.record_list[i].uid = 0
			self.zhuling_log_list.record_list[i].name = ""
		end
		self.zhuling_log_list.record_list[i].role_info = {}
	end

end

--别人的注灵日志
function HeFuJuLingWGData:SetJuLingZhuZhenOtherZhuLingLog(protocol)
	self.other_zhuling_log_list.count = protocol.count
	for i=1,6 do
		if protocol.record_list[i] and protocol.record_list[i].uid > 0 then
			self.other_zhuling_log_list.record_list[i].uid = protocol.record_list[i].uid
			self.other_zhuling_log_list.record_list[i].name = protocol.record_list[i].name
		else
			self.other_zhuling_log_list.record_list[i].uid = 0
			self.other_zhuling_log_list.record_list[i].name = ""
		end
		self.other_zhuling_log_list.record_list[i].role_info = {}
	end

end

--自己的注灵日志角色信息
function HeFuJuLingWGData:QueryRoleInfoCallBack(role_info)
	for i=1,#self.zhuling_log_list.record_list do
		if role_info.role_id == self.zhuling_log_list.record_list[i].uid then
			self.zhuling_log_list.record_list[i].role_info = role_info
		end
	end
	MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2127)

end

--别人的注灵日志角色信息
function HeFuJuLingWGData:QueryOtherRoleInfoCallBack(role_info)
	for i=1,#self.other_zhuling_log_list.record_list do
		if role_info.role_id == self.other_zhuling_log_list.record_list[i].uid then
			self.other_zhuling_log_list.record_list[i].role_info = role_info
		end
	end
	MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2127)
end

function HeFuJuLingWGData:GetJuLingInfo()
	return self.juling_info
end

function HeFuJuLingWGData:GetJuLingHelpMeInfo()
	return self.help_me_info
end

function HeFuJuLingWGData:GetJuLingZhuLingLog()
	return self.zhuling_log_list
end

function HeFuJuLingWGData:GetJuLingotherZhuLingLog()
	return self.other_zhuling_log_list
end

function HeFuJuLingWGData:GetOtherJuLingInfo()
	return self.other_juling_info
end

function HeFuJuLingWGData:GetCurGardenUid()
	if self.garden_flag == 0 then
		return self.juling_info.uid
	else
		return self.other_juling_info.uid
	end
end

--获取注灵台数量 传入自己的信息或其他人的信息
function HeFuJuLingWGData:GetZhuLingTaiCount(data)
	local zhulingtai_count = 0
	if not data or not data.record_list then
		return zhulingtai_count
	end

	for i=1,6 do
		if data.record_list[i] and tonumber(data.record_list[i].uid) > 0 then
			zhulingtai_count = zhulingtai_count + 1
		end
	end

	return zhulingtai_count
end

--根据uid获取其他人的花盆信息
function HeFuJuLingWGData:GetOtherRoleJuLingInfoByUid(uid)
	if self.other_role_info_list.other_role_info[uid] then
		return self.other_role_info_list.other_role_info[uid]
	end
end

--根据花盆类型获取帮我浇过水的玩家信息列表
--todo
function HeFuJuLingWGData:GetHlepMeDataList(pot_type)
	local help_info = self:GetJuLingHelpMeInfo()

	local data_list = {}
	if help_info.count == 0 then
		return data_list
	end

	local count = 0
	for i=1,6 do
		data_list[i] = {}
		if help_info.help_me_times_list[i] then
			local fetch_times = help_info.help_me_times_list[i].fetch_reward_times[pot_type]
			local help_times = help_info.help_me_times_list[i].help_me_times[pot_type]
			local times = help_times - fetch_times

			if times > 0 then
				data_list[i].uid = help_info.help_me_times_list[i].uid
				data_list[i].name = help_info.help_me_times_list[i].name
			else
				data_list[i].uid = 0
				data_list[i].name = ""
			end
		else
			data_list[i].uid = 0
			data_list[i].name = ""
		end
	end
	return data_list
end

--获取今天剩余注灵次数（自己浇自己）
--todo
function HeFuJuLingWGData:GetTodayRemainZhuLingTimes()

	local total_zhuling_times = 0
	local cur_zhuling_times = 0

	local zhuling_cfg = self:GetZhuLingCfgByType(0)

	if zhuling_cfg then
		total_zhuling_times = total_zhuling_times + zhuling_cfg.self_times
	end

	if self.juling_info.lingbao_list[1] then
		cur_zhuling_times = cur_zhuling_times + self.juling_info.lingbao_list[1].today_self_zhuling_times
	end

	return total_zhuling_times - cur_zhuling_times
end

--根据类型获取今天剩余被注灵次数
--todo 注灵台没满就能邀请
function HeFuJuLingWGData:GetTodayRemainBeZhuLingTimesByType(type)
	local remain_times = 0
	local max_times = 0

	local zhuling_cfg = self:GetZhuLingCfgByType(type - 1)
	if self.juling_info.lingbao_list[type] and zhuling_cfg then
		remain_times = zhuling_cfg.other_times - self.juling_info.lingbao_list[type].today_other_zhuling_times
		max_times = zhuling_cfg.other_times
	end

	return remain_times, max_times
end

-- 自己今日剩余注灵次数
--todo 配置 服务器字段
function HeFuJuLingWGData:GetTodayRemainZhuLingTimesByType(type)
	local pot_cfg = self:GetZhuLingCfgByType(type - 1)
	local remain_times, max_times = 0, 0

	if not pot_cfg then
		return remain_times, max_times
	end

	if not self.juling_info.lingbao_list[type] then
		return remain_times, max_times
	end

	max_times = pot_cfg.self_times
	remain_times = pot_cfg.self_times - self.juling_info.lingbao_list[type].today_self_zhuling_times

	return remain_times, max_times

end

--获取自己今天剩余协助次数(助阵他人总次数)
-- todo	服务器字段 配置字段
function HeFuJuLingWGData:GetTodayRemainHelpOtherTimes()
	local  other_cfg = self:GetOtherCfg()
	local times = 0

	if not other_cfg then
		return times
	end

	local time = 0
	local max_times = other_cfg.help_others_reward_times

	if max_times - self.juling_info.today_total_help_other_times > 0 then
		time = max_times - self.juling_info.today_total_help_other_times
	end

	return time, max_times
end

--更新好友列表
function HeFuJuLingWGData:UpdateJuLingFriendList()
	self.friend_list = {}

	local friend_list = SocietyWGData.Instance:GetFriendList2()
	if not friend_list or IsEmptyTable(friend_list) then
		return
	end

	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0

	for k,v in pairs(friend_list) do
		self.friend_list[k] = {}
		self.friend_list[k].is_lover = 0
		self.friend_list[k].is_online = v.is_online
		self.friend_list[k].uid = v.user_id
		self.friend_list[k].name = v.gamename
		self.friend_list[k].sex = v.sex
		self.friend_list[k].prof = v.prof
		self.friend_list[k].intimacy = v.intimacy
		self.friend_list[k].level = v.level
		self.friend_list[k].is_be_invited = 0
		self.friend_list[k].today_total_help_other_times = 0
		self.friend_list[k].common_pot_info = {}
		self.friend_list[k].lover_pot_info = {}
		self.friend_list[k].help_me_times = 0
		self.friend_list[k].common_help_me_times = 0
		self.friend_list[k].lover_help_me_times = 0
		self.friend_list[k].offline_time = v.last_logout_timestamp
		self.friend_list[k].list_type = 1
		self.friend_list[k].help_target_role_times = 0

		if lover_id == v.user_id then
			self.friend_list[k].is_lover = 1
		end

		local other_role_info = self:GetOtherRoleJuLingInfoByUid(v.user_id)

		if other_role_info then
			self.friend_list[k].is_be_invited = other_role_info.invite_me 										--是否被邀请
			self.friend_list[k].help_me_times = other_role_info.help_me_times 									--帮特定某人(自己)浇水次数
			self.friend_list[k].today_total_help_other_times = other_role_info.today_total_help_other_times		--今天协助别人总次数
			self.friend_list[k].common_pot_info = other_role_info.lingbao_list[1]								--普通花盆信息
			self.friend_list[k].lover_pot_info = other_role_info.lingbao_list[2]								--情侣花盆信息
			self.friend_list[k].common_help_me_times = other_role_info.help_me_times_list[1]					--帮特定某人(自己)普通盆栽浇水次数
			self.friend_list[k].lover_help_me_times = other_role_info.help_me_times_list[2]						--帮特定某人(自己)情侣盆栽浇水次数
			self.friend_list[k].help_target_role_times = other_role_info.help_target_role_times[1]					--主角帮这个人浇水次数
		end
	end

	table.sort(self.friend_list, SortTools.KeyUpperSorters("is_lover", "is_online", "is_be_invited", "intimacy", "level"))
end

function HeFuJuLingWGData:GetJuLingFriendList()
	return self.friend_list
end

--更新仙盟成员列表
function HeFuJuLingWGData:UpdateJuLingMemberList()
	self.member_list = {}

    local guild_id = RoleWGData.Instance.role_vo.guild_id
	local member_list = GuildDataConst.GUILD_MEMBER_LIST
    if guild_id == 0 or not member_list or not member_list.list or IsEmptyTable(member_list.list) then
        self.member_list = {}
		return
	end

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	local index = 0
	for k,v in pairs(member_list.list) do
		if v.uid ~= role_id then
			index = index + 1
			self.member_list[index] = {}
			self.member_list[index].is_lover = 0
			self.member_list[index].is_online = v.is_online
			self.member_list[index].uid = v.uid
			self.member_list[index].name = v.role_name
			self.member_list[index].sex = v.sex
			self.member_list[index].prof = v.prof
			self.member_list[index].level = v.level
			self.member_list[index].is_be_invited = 0
			self.member_list[index].today_total_help_other_times = 0
			self.member_list[index].help_me_times = 0
			self.member_list[index].common_help_me_times = 0
			self.member_list[index].lover_help_me_times = 0
			self.member_list[index].common_pot_info = {}
			self.member_list[index].lover_pot_info = {}
			self.member_list[index].offline_time = v.last_login_time
			self.member_list[index].list_type = 2
			self.member_list[index].help_target_role_times = 0

			if lover_id == v.uid then
				self.member_list[index].is_lover = 1
			end

			local other_role_info = self:GetOtherRoleJuLingInfoByUid(v.uid)

			if other_role_info then
				self.member_list[index].is_be_invited = other_role_info.invite_me 										--是否被邀请
				self.member_list[index].today_total_help_other_times = other_role_info.today_total_help_other_times		--今天协助别人总次数
				self.member_list[index].common_pot_info = other_role_info.lingbao_list[1]								--普通花盆信息
				self.member_list[index].lover_pot_info = other_role_info.lingbao_list[2]									--情侣花盆信息
				self.member_list[index].common_help_me_times = other_role_info.help_me_times_list[1]					--帮特定某人(自己)普通盆栽浇水次数
				self.member_list[index].lover_help_me_times = other_role_info.help_me_times_list[2]						--帮特定某人(自己)情侣盆栽浇水次数
				self.member_list[index].help_target_role_times = other_role_info.help_target_role_times[1]					--主角帮这个人浇水次数
			end
		end
	end

	table.sort(self.member_list, SortTools.KeyUpperSorters("is_lover", "is_online", "is_be_invited", "level"))
end

function HeFuJuLingWGData:GetJuLingMemberList()
	return self.member_list
end

-- 这个人（data）是否能被邀请
-- todo 服务器字段 配置
function HeFuJuLingWGData:GetIsCanInvite(data)
	local is_can_invite = false
    local log_type = 0
	if not data then
		return is_can_invite, log_type
	end

	local other_cfg = self:GetOtherCfg()

	if not other_cfg then
		return is_can_invite, log_type
	end

	local common_pot_cfg = self:GetZhuLingCfgByType(0)

	if not common_pot_cfg then
		return is_can_invite, log_type
	end

	-- 自己没有种植或已成熟或已采摘					
	-- 自己注灵台满了					置灰邀请
	-- 这个人是给自己注灵过					

	-- 自己盆栽不处于成长期
	if self.juling_info.lingbao_list then
        if self.juling_info.lingbao_list[1].status ~= LINGBAO_STATUS.LINGBAO_STATUS_GROWING then
            log_type = 1
			return is_can_invite, log_type
		end
	end


	-- 自己注灵台满了
	local zhulinglog_info = self:GetJuLingZhuLingLog()
	local zhulingtai_count = self:GetZhuLingTaiCount(zhulinglog_info)
    if zhulingtai_count >= 6 then
        log_type = 2
		return is_can_invite, log_type
	end

    if data.common_help_me_times >= common_pot_cfg.help_other_times then
        log_type = 3
		return is_can_invite, log_type
	end

	is_can_invite = true
	return is_can_invite
end

-- 自己是否能帮这个人（data）注灵
function HeFuJuLingWGData:GetIsCanHelpZhuLing(data)
	local log_type = 0
	local is_can_zhuling = false

	if not data then
		return is_can_zhuling, log_type
	end

	local other_cfg = self:GetOtherCfg()

	if not other_cfg then
		return is_can_zhuling, log_type
	end

	local common_pot_cfg = self:GetZhuLingCfgByType(0)

	if not common_pot_cfg then
		return is_can_zhuling, log_type
	end


	--todo					
	-- 对方盆栽不处于成长期
	if data.common_pot_info.status ~= LINGBAO_STATUS.LINGBAO_STATUS_GROWING then
		log_type = 3
		return is_can_zhuling, log_type
	end

	-- 自己是否帮助过这个人
    if data.help_target_role_times >= common_pot_cfg.help_other_times then
        log_type = 2
		return is_can_zhuling, log_type
	end
	-- 对方注灵台满了
    if data.common_pot_info.today_other_zhuling_times >= 6 then
        log_type = 1
		return is_can_zhuling, log_type
	end

	is_can_zhuling = true
	return is_can_zhuling, log_type
end

--根据类型获取是否被邀请
--type:1(好友列表) 2(盟友列表)
function HeFuJuLingWGData:GetIsBeInveiteByType(type)
	local is_be_invited = false
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	if self.juling_info.uid ~= role_id then
		return is_be_invited
	end

	local data_list

	if type == 1 then
		data_list = self:GetJuLingFriendList()
	else
		data_list = self:GetJuLingMemberList()
	end
    for k, v in pairs(data_list) do
        local is_can_invite = self:GetIsCanHelpZhuLing(v)
        --print_error(type,"is_can_invite",k,is_can_invite,"v.is_be_invited", v.is_be_invited)
		if is_can_invite and v.is_be_invited == 1 then
			is_be_invited = true
			return is_be_invited
		end
	end

	return is_be_invited
end


--todo
function HeFuJuLingWGData:GetJuLingZhuZhenRemind()
	local is_remind = 0

	local status = MergeActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_JULINGZHUZHEN)

	if not status then
		return is_remind
	end

	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_JULINGZHUZHEN) then
		return is_remind
	end

	local act_cfg = self:GetOtherCfg()
	if act_cfg == nil then
		return is_remind
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()

	if role_level < act_cfg.open_role_level then
		return is_remind
	end

	if IsEmptyTable(self.juling_info.lingbao_list) then
		return is_remind
	end


	--无种植或可领取给红点
    if self.juling_info.lingbao_list[1].status == LINGBAO_STATUS.LINGBAO_STATUS_NOTHING then
		is_remind = 1
		return is_remind
	end

	--成熟未领给红点
	if self.juling_info.lingbao_list[1].status == LINGBAO_STATUS.LINGBAO_STATUS_RIPE then
        is_remind = 1
		return is_remind
	else
		--有帮助奖励可领给红点
		local help_remind = self:GetHelpListRemind()
		if help_remind == 1 then
            is_remind = 1
			return is_remind
		end
	end

	--被邀请给红点
    for i=1,2 do
        --print_error("被邀请给红点",i,self:GetIsBeInveiteByType(i))
		if self:GetIsBeInveiteByType(i) then
            is_remind = 1
			return is_remind
        end	
    end


	return is_remind
end


function HeFuJuLingWGData:GetHelpListRemind()
    local data_list = HeFuJuLingWGData.Instance:GetHlepMeDataList(1)
    local remind = 0
    for k = 1, 6 do
        if data_list[k] and data_list[k].uid > 0 then
            remind = 1
            break
        end
    end
    return remind
end

--设置一个花园标记，区分现在是在别人的花园还是自己的花园
--flag == 1 and 别人的花园 or 自己的花园
function HeFuJuLingWGData:SetGardenFlag(garden_flag)
	self.garden_flag = garden_flag
end

function HeFuJuLingWGData:GetGardenFlag()
	return self.garden_flag
end

--根据花盆类型获取花盆配置
function HeFuJuLingWGData:GetZhuLingCfgByType(type)
	local openserver_day_cfg = self:GetOpenServerDayCfg()

	if not openserver_day_cfg then
		return
	end

	local grade = openserver_day_cfg.grade

	for i,v in ipairs(self.juhun_cfg.lingbao_cfg) do
		if grade == v.grade and type == v.seq then
			return v
		end
	end
end

--获取活动开启当天的配置
function HeFuJuLingWGData:GetOpenServerDayCfg()
	local cfg = self.juhun_cfg.openserver_day_cfg

	local activity_open_day = MergeActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_JULINGZHUZHEN)
	for k,v in pairs(cfg) do
		if activity_open_day >= v.start_server_day and activity_open_day <= v.end_server_day then
			return v
		end
	end
end

--获取其他配置
function HeFuJuLingWGData:GetOtherCfg()
	local cfg = self.juhun_cfg.other
	local openserver_day_cfg = self:GetOpenServerDayCfg()
	if not openserver_day_cfg then
		return
	end

	for k,v in pairs(cfg) do
		if openserver_day_cfg.grade == v.grade then
			return v
		end
	end
end

--根据类型和剩余时间获取花的模型
function HeFuJuLingWGData:GetFlowerModelCfgByTypeAndRemainTime(type, remain_time)
	local flower_model_cfg = self.juhun_cfg.lingbao_model

	for i,v in ipairs(flower_model_cfg) do
		if v.lingbao_type == type - 1 and remain_time <= v.remain_time and remain_time > v.remain_time_1 then
			return v
		end
	end
end

function HeFuJuLingWGData:GetHeFuZhuLingIsOpen()
	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_JULINGZHUZHEN) then
		return false
	end

	local act_cfg = self:GetOtherCfg()
	if act_cfg == nil then
		return false
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	local need_level = act_cfg.open_role_level
	return role_level >= need_level
end


-- 缓存邀请人员的cd列表
function HeFuJuLingWGData:AddCacheCDList(role_id, time)
    if not self.cache_invite_cd_list then
        self.cache_invite_cd_list = {}
    end
    if CountDownManager.Instance:HasCountDown("merge_juling_invite_cd_time") then
        CountDownManager.Instance:RemoveCountDown("merge_juling_invite_cd_time")
    end
    CountDownManager.Instance:AddCountDown("merge_juling_invite_cd_time", BindTool.Bind(self.UpdateInviteTime,self), 
    BindTool.Bind(self.CheckInviteTime, self), 15 + TimeWGCtrl.Instance:GetServerTime())
    local cfg_time = self:GetOtherCfg() and self:GetOtherCfg().cd or 10
    time = time or cfg_time
    self.cache_invite_cd_list[role_id] = time
    HeFuJuLingWGCtrl.Instance:FlushTextInvite()
end

function HeFuJuLingWGData:GetCacheCDByRoleid(role_id)
    if not self.cache_invite_cd_list then
        return 0
    end
    return self.cache_invite_cd_list[role_id] or 0
end

function HeFuJuLingWGData:CheckInviteTime()
    if IsEmptyTable(self.cache_invite_cd_list) then
        CountDownManager.Instance:RemoveCountDown("merge_juling_invite_cd_time")
    else
        CountDownManager.Instance:AddCountDown("merge_juling_invite_cd_time", BindTool.Bind(self.UpdateInviteTime,self),
        BindTool.Bind(self.CheckInviteTime,self), 15 + TimeWGCtrl.Instance:GetServerTime())
    end
end

-- 缓存邀请人员的cd列表
function HeFuJuLingWGData:UpdateInviteTime(elapse_time, total_time)
    if IsEmptyTable(self.cache_invite_cd_list) then
        CountDownManager.Instance:RemoveCountDown("merge_juling_invite_cd_time")
        return
    end

    for k, v in pairs(self.cache_invite_cd_list) do
        self.cache_invite_cd_list[k] = v - 1
        if v <= 0 then
            self.cache_invite_cd_list[k] = nil
            table.remove(self.cache_invite_cd_list, k)
        end
        HeFuJuLingWGCtrl.Instance:FlushTextInvite()
    end
end