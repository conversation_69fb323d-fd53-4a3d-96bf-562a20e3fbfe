LoginNoticView = LoginNoticView or BaseClass(SafeBaseView)

function LoginNoticView:__init()
	self.view_layer = UiLayer.SceneLoadingPop
	self:AddViewResource(0, "uis/view/login_ui_prefab", "layout_login_notice")
	self:SetMaskBg()
end


function LoginNoticView:ReleaseCallBack()
	-- if nil ~= self.small_list then
	-- 	self.small_list:DeleteMe()
	-- 	self.small_list = nil
	-- end

	if nil ~= self.big_list then
		self.big_list:DeleteMe()
		self.big_list = nil
	end

	self.select_big_data = nil
	self.default_select_index = nil
	self.select_big_cell = nil
	self.big_default_index = nil
end

function LoginNoticView:LoadCallBack()
	-- if not self.small_list then
	-- 	self.small_list = AsyncListView.New(LoginNoticSmallRender, self.node_list["small_list"])
	-- 	self.small_list:SetSelectCallBack(BindTool.Bind(self.OnSelectSmall, self))
	-- 	self.small_list:SetDefaultSelectIndex(nil)
	-- end

	if not self.big_list then
		self.big_list = AsyncListView.New(LoginNoticBigRender, self.node_list["big_list"])
		self.big_list:SetSelectCallBack(BindTool.Bind(self.OnSelectBig, self))		
	end
end

function LoginNoticView:OpenCallBack()

end

function LoginNoticView:CloseCallBack()
	-- ReportManager:Step(Report.STEP_CLOSE_ANNOUNCEMENT)
end

function LoginNoticView:ReloadBigList()
	if not self.big_list then
		return
	end

	local data = SettingWGData.Instance:GetNoticData()
	self.big_list:SetDataList(data)
	if not self.big_default_index then
		self.big_default_index = 1
		self.big_list:JumpToIndex(self.big_default_index, 5)
	end
end

function LoginNoticView:OnSelectBig(cell,index)
	if not cell or not cell.data then
		return
	end

	local old_select_big_data = self.select_big_data
	self.select_big_data = cell.data
	self.select_big_cell = cell

	local small_data_list = self.select_big_data.sub_notice or {}
	if self.select_big_data.is_open == 0 then
		small_data_list = {}
	end
	--self.small_list:SetDataList(small_data_list)

	self.node_list.layout_big:SetActive(#small_data_list <= 0)
	--self.node_list.layout_small:SetActive(#small_data_list > 0)
	--self.node_list.small_list_bg:SetActive(#small_data_list > 0)

	-- local bundle,asset = ResPath.GetPngRawImage("bg_login_notice_" .. (#small_data_list > 0 and 1 or 2))
	-- self.node_list["content_bg"].raw_image:LoadSprite(bundle, asset)

	if not old_select_big_data or (not self.select_big_data) or old_select_big_data.id ~= self.select_big_data.id then
		self.default_select_index = nil
	end

	PlayerPrefsUtil.SetInt("LoginMainNoticeHasRead_" .. self.select_big_data.id, 1)
	ViewManager.Instance:FlushView(GuideModuleName.Login, 0, "flush_gonggao_red")
	self:FlushBigView()
end

function LoginNoticView:OnSelectSmall(cell,index)
	if not cell or not cell.data then
		return
	end
	self.select_small_data = cell.data

	if self.select_big_cell then
		self.select_big_cell:Flush()
	end

	self:FlushSmallView()
end

function LoginNoticView:OnFlush()
	local data = SettingWGData.Instance:GetNoticData()
	if not data then return end

	self.node_list.big_list.scroll_rect.enabled = #data > 4
	self:ReloadBigList()
end

function LoginNoticView:FlushBigView()
	local data = self.select_big_data
	if IsEmptyTable(data) then 
		return 
	end

	--local main_notice_flag = PlayerPrefsUtil.GetInt("LoginMainNoticeHasRead_" .. data.id)
	-- 存在红点
	self.node_list.big_title_name:SetActive(data.title and data.title ~= "")
	if data.is_open == 0 then -- and (not main_notice_flag or main_notice_flag < 1 or #data.sub_notice < 1)
		--PlayerPrefsUtil.SetInt("LoginMainNoticeHasRead_" .. data.id, 1)
		ViewManager.Instance:FlushView(GuideModuleName.Login, 0, "flush_gonggao_red")

		if self.select_big_cell then
			self.select_big_cell:Flush()
		end

		--self.node_list.title_name.text.text = data.title
		self.node_list.big_title_name.text.text = data.title
		--self.node_list.content.text.text = data.content
		self.node_list.big_content.text.text = data.content
		--self.small_list:CancelSelect()
	else
		self.node_list.big_title_name.text.text = data.title
		self.node_list.big_content.text.text = data.content
		
	    -- 选中子页签红点
	    if not self.default_select_index then
		    self.default_select_index = 1
		end
	end

	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["content"].rect)
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["big_content"].rect)
	--self.node_list.content.rect.anchoredPosition3D = u3dpool.vec3(0, 0, 0)
	self.node_list.big_content.rect.anchoredPosition3D = u3dpool.vec3(0, 0, 0)

end

function LoginNoticView:FlushSmallView()
	-- local data = self.select_small_data
	-- if not data then 
	-- 	return 
	-- end

	--self.node_list.title_name.text.text = data.title
	--self.node_list.content.text.text = data.content
	--UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["content"].rect)
	--self.node_list.content.rect.anchoredPosition3D = u3dpool.vec3(0, 0, 0)
end

LoginNoticSmallRender = LoginNoticSmallRender or BaseClass(BaseRender)

function LoginNoticSmallRender:OnFlush()
	local data = self.data 
	if not data or not next(data) then
		return
	end

	self.node_list.name.text.text = data.title
	local notice_flag = PlayerPrefsUtil.GetInt("LoginSmallNoticeHasRead_" .. data.id)
	if not notice_flag or notice_flag < 1 then
		self.node_list.red:SetActive(false)
	else
		self.node_list.red:SetActive(false)
	end
end

function LoginNoticSmallRender:OnSelectChange(is_select)
	local data = self.data
	if data and data.id and is_select then 
		PlayerPrefsUtil.SetInt("LoginSmallNoticeHasRead_" .. data.id,1)
		ViewManager.Instance:FlushView(GuideModuleName.Login, 0, "flush_gonggao_red")
		self.node_list.red:SetActive(false)
	end
	-- 高亮
	self.node_list.high_light:SetActive(is_select)
end

LoginNoticBigRender = LoginNoticBigRender or BaseClass(BaseRender)

function LoginNoticBigRender:OnFlush()
	local data = self.data 
	if not data or not next(data) then
		return
	end

	self.node_list.name1.text.text = data.title
	self.node_list.name2.text.text = data.title

	-- local flag = SettingWGData.Instance:GetNoticMainHasRemind(data)
	self.node_list.red:SetActive(false)
end

function LoginNoticBigRender:OnSelectChange(is_select)
	local data = self.data
	if data and data.id and is_select then 
		-- local flag = SettingWGData.Instance:GetNoticMainHasRemind(data)
		self.node_list.red:SetActive(false)
	end

	self.node_list.high_light:SetActive(is_select)
	self.node_list.normal:SetActive(not is_select)
	
end
