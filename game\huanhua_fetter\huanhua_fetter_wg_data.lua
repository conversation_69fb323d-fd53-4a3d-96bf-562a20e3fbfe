HuanHuaFetterWGData = HuanHuaFetterWGData or BaseClass()

function HuanHuaFetterWGData:__init()
	if HuanHuaFetterWGData.Instance ~= nil then
		ErrorLog("[HuanHuaFetterWGData] attempt to create singleton twice!")
		return
	end

	HuanHuaFetterWGData.Instance = self
    self.suit_list = {}
    self.type_toggle_list = {}

    local cfg = ConfigManager.Instance:GetAutoConfig("huanhua_fetter_cfg_auto")
    self.theme_map_cfg = ListToMap(cfg.theme, "seq")
    self.theme_map_type_cfg = ListToMap(cfg.theme, "label_type", "big_type", "small_type")
	self.suit_act_map_cfg = ListToMap(cfg.activation, "seq", "part")
	self.suit_attr_map_cfg = ListToMapList(cfg.attr, "seq")
    self.toggle_show_cfg = ListToMap(cfg.toggle_show, "label_type", "big_type", "small_type")

    local rm = RemindManager.Instance
	rm:Register(RemindName.HuanhuaFetterWaiguan, BindTool.Bind(self.GetFetterAllRemind, self, TabIndex.huanhua_fetter_waiguan))
    rm:Register(RemindName.HuanhuaFetterLingchong, BindTool.Bind(self.GetFetterAllRemind, self, TabIndex.huanhua_fetter_lingchong))
    rm:Register(RemindName.HuanhuaFetterMount, BindTool.Bind(self.GetFetterAllRemind, self, TabIndex.huanhua_fetter_mount))
    rm:Register(RemindName.HuanhuaFetterKun, BindTool.Bind(self.GetFetterAllRemind, self, TabIndex.huanhua_fetter_kun))

    self:InitPartActItemList()
end

function HuanHuaFetterWGData:__delete()
    HuanHuaFetterWGData.Instance = nil

    local rm = RemindManager.Instance
    rm:UnRegister(RemindName.HuanhuaFetterWaiguan)
    rm:UnRegister(RemindName.HuanhuaFetterLingchong)
    rm:UnRegister(RemindName.HuanhuaFetterMount)
    rm:UnRegister(RemindName.HuanhuaFetterKun)
end

function HuanHuaFetterWGData:InitPartActItemList()
	self.part_act_item_list = {}
	local item_id
	for suit, part_list in pairs(self.suit_act_map_cfg) do
		self.part_act_item_list[suit] = {}
		for part, data in pairs(part_list) do
			item_id = self:GetActItemId(data)
			self.part_act_item_list[suit][data.part] = item_id
		end
	end
end

function HuanHuaFetterWGData:GetPartActItemId(suit, part)
	return (self.part_act_item_list[suit] or {})[part] or 0
end

function HuanHuaFetterWGData:GetActItemId(data)
	local act_id = 0
	if IsEmptyTable(data) then
		return act_id
	end

	local cfg
	if data.type == WARDROBE_PART_TYPE.FASHION then
		cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
		act_id = NewAppearanceWGData.Instance:GetFashionItemId(data.param1, data.param2)
	elseif data.type == WARDROBE_PART_TYPE.MOUNT then
		cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(data.param1)
		act_id = cfg and cfg.active_item_id or 0
	elseif data.type == WARDROBE_PART_TYPE.LING_CHONG then
		cfg = NewAppearanceWGData.Instance:GetLingChongActCfgByImageId(data.param1)
		act_id = cfg and cfg.active_item_id or 0
	elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then
		cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
		act_id = cfg and cfg.active_need_item_id or 0
	elseif data.type == WARDROBE_PART_TYPE.XIAN_WA then
		cfg = MarryWGData.Instance:GetActiveCfgByTypeAndId(data.param1, data.param2)
		act_id = cfg and cfg.item_id or 0
	end

	return act_id, cfg
end

function HuanHuaFetterWGData:SetAllSuitStateInfo(protocol)
	self.suit_list = protocol.suit_list
    self:SetToggleList()
end

function HuanHuaFetterWGData:SetSingleSuitStateInfo(protocol)
	self.suit_list[protocol.update_suit] = protocol.part_list
    self:UpdateToggleList(protocol.update_suit)
end

function HuanHuaFetterWGData:GetPartStateByData(suit, part)
	return (self.suit_list[suit] or {})[part] or 0
end

function HuanHuaFetterWGData:GetThemeTypeCfg(label_type, big_type, small_type)
	return ((self.theme_map_type_cfg[label_type] or {})[big_type] or {})[small_type]
end

function HuanHuaFetterWGData:GetToggleShowCfg(label_type, big_type, small_type)
	return ((self.toggle_show_cfg[label_type] or {})[big_type] or {})[small_type]
end

function HuanHuaFetterWGData:GetThemeCfgBySuit(suit)
	return self.theme_map_cfg[suit]
end

function HuanHuaFetterWGData:GetActivationPartList(suit)
	return self.suit_act_map_cfg[suit]
end

-- 设置菜单列表
function HuanHuaFetterWGData:SetToggleList()
    self.type_toggle_list = {}
    local cfg = ConfigManager.Instance:GetAutoConfig("huanhua_fetter_cfg_auto").toggle_show
    local label_type, big_type, small_type = 0, 0, 0
    for k,v in ipairs(cfg) do
        label_type = v.label_type
        big_type = v.big_type
        small_type = v.small_type
        if not self.type_toggle_list[label_type] then
            self.type_toggle_list[label_type] = {}
        end

        if small_type == 0 then
            self.type_toggle_list[label_type][big_type] = {name = v.suit_name, type = big_type, child_list = {}, remind_num = 0, open_toggle_num = 0}
        else
            if self.type_toggle_list[label_type][big_type] then
                local theme_cfg = self:GetThemeTypeCfg(label_type, big_type, small_type)
				if theme_cfg then
					local suit = theme_cfg.seq
					local info = self:GetOneSuitDataBySuit(suit)

					local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
					local role_level = RoleWGData.Instance:GetRoleLevel()
					local can_open = false
					if theme_cfg.condition_type == 1 then
						can_open = open_day >= theme_cfg.skynumber_show and role_level >= theme_cfg.level_show
					elseif theme_cfg.condition_type == 2 then
						can_open = open_day >= theme_cfg.skynumber_show or role_level >= theme_cfg.level_show
					end

					if can_open then
						self.type_toggle_list[label_type][big_type].open_toggle_num = self.type_toggle_list[label_type][big_type].open_toggle_num + 1
						if info.can_act then
							self.type_toggle_list[label_type][big_type].remind_num = self.type_toggle_list[label_type][big_type].remind_num + 1
						end
					end

					local data = {name = v.suit_name, type = small_type, info = info, suit = suit, can_open = can_open}
					table.insert(self.type_toggle_list[label_type][big_type].child_list, data)
				end
            end
        end
    end
end

function HuanHuaFetterWGData:UpdateToggleList(suit)
    local suit_list, small_type = self:GetToggleListBySuit(suit)
    if (suit_list == nil) or (suit_list.child_list == nil) then
        return
    end

	local suit_remind = 0
    for k,v in pairs(suit_list.child_list) do
        local info = self:GetOneSuitDataBySuit(v.suit)
        if v.suit == suit and v.type == small_type then
            v.info = info
        end

        if v.can_open and info.can_act then
            suit_remind = suit_remind + 1
		end
    end

    suit_list.remind_num = suit_remind
end

-- 获取菜单列表
function HuanHuaFetterWGData:GetToggleList(seq)
    return self.type_toggle_list[seq] or {}
end

-- 获取菜单列表
function HuanHuaFetterWGData:GetToggleListBySuit(suit)
    local theme_cfg = self:GetThemeCfgBySuit(suit)
    if IsEmptyTable(theme_cfg) then
        return {}
    end

    local label_type = theme_cfg.label_type
    local big_type = theme_cfg.big_type
    local small_type = theme_cfg.small_type
    return (self.type_toggle_list[label_type] or {})[big_type], small_type
end

function HuanHuaFetterWGData:GetOneSuitDataBySuit(suit)
	suit = suit or 0
	local temp_data = {}

	local theme_cfg = self:GetThemeCfgBySuit(suit)

	local act_part_num = 0 -- 激活部位数量
	local total_part_num = 0 --当前部位数量
	local can_act = false   -- 能否激活
	local can_fetch_num = 0  --可激活数量
	temp_data.suit = suit
	temp_data.icon = 0
	temp_data.part_list = {}
	local part_cfg = self:GetActivationPartList(suit)
	if not IsEmptyTable(part_cfg) then
		local part
		for k, v in pairs(part_cfg) do
			part = v.part
			local state = self:GetPartStateByData(suit, part)
			local show_item_id = self:GetPartActItemId(suit, part)

			local part_data = {}
			part_data = {}
			part_data.part = part
			part_data.type = v.type
			part_data.param1 = v.param1
			part_data.param2 = v.param2
			part_data.state = state
			part_data.suit_seq = v.seq

			part_data.show_item_id = show_item_id
			temp_data.part_list[part] = part_data

			if v.icon > 0 then
				temp_data.icon = show_item_id
			end

			total_part_num = total_part_num + 1
			if state == REWARD_STATE_TYPE.CAN_FETCH then
				can_fetch_num = can_fetch_num + 1
			elseif state == REWARD_STATE_TYPE.FINISH then
				act_part_num = act_part_num + 1
			end
		end
	end
	local suit_cfg = self.suit_attr_map_cfg[suit]
	local suit_less_need = 0

	if can_fetch_num > 0 then
		if IsEmptyTable(suit_cfg) then
			print_error("拿不到配置属性 seq = ", suit)
		else
			for k,v in pairs(suit_cfg) do
				if act_part_num < v.num and (act_part_num + can_fetch_num) >= v.num then --判断能不能激活下一套装属性
					if suit_less_need == 0 then
						suit_less_need = v.num
					else
						suit_less_need = suit_less_need < v.num and suit_less_need or v.num
					end

					can_act = true
				end
			end
		end
	end

	temp_data.can_act = can_act
	temp_data.real_act_num = act_part_num + can_fetch_num--实际激活数
	temp_data.act_part_num = act_part_num
	temp_data.total_part_num = total_part_num
	temp_data.suit_less_need = suit_less_need
	return temp_data
end

function HuanHuaFetterWGData:GetAttrBySuit(suit)
    local attr_list = {}
	local attr_cfg = self.suit_attr_map_cfg[suit]
	if IsEmptyTable(attr_cfg) then
		return attr_list
	end

    local info = self:GetOneSuitDataBySuit(suit)
	local act_part_num = info and info.act_part_num or 0

    for k, v in ipairs(attr_cfg) do
        local temp_data = {}
        temp_data.need_num = v.num
		temp_data.is_act = act_part_num >= v.num
        temp_data.attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(v, nil, "attr_id", "attr_value", 1, 1)
        table.insert(attr_list, temp_data)
    end

    return attr_list
end


--红点
function HuanHuaFetterWGData:GetFetterAllRemind(tab_index)
	if not FunOpen.Instance:GetFunIsOpened(FunName.NewHuanHuaFetterView) then
		return 0
	end

    local toggle_list = self:GetToggleList(math.floor(tab_index / 10))
    if IsEmptyTable(toggle_list) then
        return 0
    end

	local can_open = self:GetTabbarVerOrToggleCanOpen(tab_index)
	if not can_open then
		return 0
	end

    for k, v in ipairs(toggle_list) do
        if v.remind_num > 0 then
            return 1
        end
    end

    return 0
end

function HuanHuaFetterWGData:GetTabbarVerOrToggleCanOpen(tab_index, big_type)
	local toggle_list = self:GetToggleList(math.floor(tab_index / 10))
    if IsEmptyTable(toggle_list) then
        return false
    end

	for k, v in pairs(toggle_list) do
		for k1, v1 in pairs(v.child_list) do
			if (big_type and big_type == k and v1.can_open) or (not big_type and v1.can_open) then
				return v1.can_open
			end
		end
	end

	return false
end