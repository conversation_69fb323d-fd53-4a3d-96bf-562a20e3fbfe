CrossLongMaiSceneView = CrossLongMaiSceneView or BaseClass(SafeBaseView)
function CrossLongMaiSceneView:__init()
    self.view_cache_time = 0
    self.active_close = false
    self.is_safe_area_adapter = true
    self.view_layer = UiLayer.MainUIHigh

	self:AddViewResource(0, "uis/view/longmai_ui_prefab", "layout_fb_cross_longmai")
end

function CrossLongMaiSceneView:ReleaseCallBack()
    if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end

    self:CleanRefreshGatherTimer()
    self:CleanRefreshBossTimer()

	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end

    if self.boss_list then
        for k,v in pairs(self.boss_list) do
            v:DeleteMe()
        end

        self.boss_list =  nil
    end

    if self.rank_list then
        self.rank_list:DeleteMe()
        self.rank_list = nil
    end
end

function CrossLongMaiSceneView:LoadCallBack()
    self.node_list["icon_close"]:SetActive(true)
    self.node_list["icon_open"]:SetActive(false)
    RectTransform.SetAnchoredPositionXY(self.node_list["hurt_panel"].rect, 0, 0)
    
    XUI.AddClickEventListener(self.node_list.btn_goto_gather, BindTool.Bind(self.OnClickGoToGather, self))
    XUI.AddClickEventListener(self.node_list.btn_find_boss, BindTool.Bind(self.OnClickFindBoss, self))
    XUI.AddClickEventListener(self.node_list.btn_find_boss2, BindTool.Bind(self.OnClickFindBoss, self))
    XUI.AddClickEventListener(self.node_list.btn_hurt_title, BindTool.Bind(self.OnClickHurtTween, self))
    XUI.AddClickEventListener(self.node_list.btn_rule_tip, BindTool.Bind(self.OnClickRuleTip, self))
    XUI.AddClickEventListener(self.node_list.btn_longmai_shop, BindTool.Bind(self.OnClickLomhMaiShop, self))

	--MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))

    -- 物品改变
    if not self.item_data_change then
        self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
        ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)
    end

    local stuff_list = CrossLongMaiWGData.Instance:GetDeadDropCfg()
    for i = 1, #stuff_list do
        local consume_id = stuff_list[i].item_id
        local item_cfg = ItemWGData.Instance:GetItemConfig(consume_id)
        if item_cfg then
            self.node_list["icon_" .. i].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
        end
	end

    if not self.boss_list then
        self.boss_list = {}
        for i = 1, 3 do
            self.boss_list[i] = LongMaiBossRender.New(self.node_list["boss_" .. i])
        end
    end

    if not self.rank_list then
        self.rank_list = AsyncListView.New(LongMaiBossHurtRender, self.node_list.hurt_list)
    end
    self.node_list["desc"].text.text = Language.CrossLongMai.HurtDes
end

function CrossLongMaiSceneView:InitCallBack()
	local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
	if self.node_list["task_content"] then
		self.obj = self.node_list["task_content"].gameObject
		self.obj.transform:SetParent(parent.gameObject.transform)
		self.obj.transform.localPosition = Vector3(0, 0, 0)
		self.obj.transform.localScale = Vector3.one
	end
end

function CrossLongMaiSceneView:CloseCallBack()

end

function CrossLongMaiSceneView:OnFlush(param_t)
    for k,v in pairs(param_t) do
		if k == "all" then
            self:FlushBossDesc()
            self:FlushNextGatherTimeDesc()
            self:FlushGatherDesc()
            self:FlushShopStuff()
            self:FlushBossList()
        elseif k == "boss_time" then
            self:FlushBossDesc()
            self:FlushNextGatherTimeDesc()
            self:FlushBossList()
        elseif k == "gather_times" then
            self:FlushGatherDesc()
        elseif k == "boss_list" then
            self:FlushBossList()
        elseif k == "hurt_list" then
            self:FlushLeftHurt()
        end
    end
end

function CrossLongMaiSceneView:CleanRefreshBossTimer()
    if self.refresh_timer and CountDown.Instance:HasCountDown(self.refresh_timer) then
        CountDown.Instance:RemoveCountDown(self.refresh_timer)
        self.refresh_timer = nil
    end
end

function CrossLongMaiSceneView:FlushLeftHurt()
    local all_hurt_info = BossAssistWGData.Instance:GetHurtInfo()
    local main_role_hurt = all_hurt_info.main_role_hurt or 0
    self.node_list.my_damate.text.text = CommonDataManager.ConverNumber(main_role_hurt)
    self.node_list.per_bg.slider.value = main_role_hurt / BossAssistWGData.Instance:GetNormalHurtInfoMaxValue()

    local damage_list = all_hurt_info.hurt_info or {}
    self.node_list.common_no_data_panel:SetActive(IsEmptyTable(damage_list))
    if self.rank_list ~= nil then
        self.rank_list:SetDataList(damage_list)
    end
end

function CrossLongMaiSceneView:FlushBossDesc()
    local boss_list = CrossLongMaiWGData.Instance:GetBossInfoList()
    local boss_data = boss_list[1]

    self:CleanRefreshBossTimer()
    if boss_data and boss_data.is_live == 1 then
        self.node_list["boss_desc"].text.text = Language.CrossLongMai.BossDesc[2]
        return
    end

    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local refresh_time = CrossLongMaiWGData.Instance:GetBossNextRefreshTime()
    local rest_time = refresh_time - server_time
    if rest_time > 0 then
        self.refresh_timer = CountDown.Instance:AddCountDown(rest_time, 0.5,
        BindTool.Bind(self.FlushBossTime, self),
        function()
            if self.node_list["boss_desc"] then
                self.node_list["boss_desc"].text.text = Language.CrossLongMai.BossDesc[2]
            end
        end
        )

        return
    end

    self.node_list["boss_desc"].text.text = Language.CrossLongMai.BossDesc[3]
end

function CrossLongMaiSceneView:FlushBossTime(elapse_time, total_time)
	local time = math.floor(total_time - elapse_time)
	if self.node_list["boss_desc"] then
		if time > 0 then
            self.node_list["boss_desc"].text.text = string.format(Language.CrossLongMai.BossDesc[1], TimeUtil.FormatSecondDHM8(time))
        else
            self.node_list["boss_desc"].text.text = Language.CrossLongMai.BossDesc[2]
       	end
    end
end

function CrossLongMaiSceneView:CleanRefreshGatherTimer()
    if self.refresh_gather_timer and CountDown.Instance:HasCountDown(self.refresh_gather_timer) then
        CountDown.Instance:RemoveCountDown(self.refresh_gather_timer)
        self.refresh_gather_timer = nil
    end
end

function CrossLongMaiSceneView:FlushNextGatherTimeDesc()
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local refresh_time = CrossLongMaiWGData.Instance:GetGatherNextRefreshTime()
    local rest_time = refresh_time - server_time
    self.node_list["gather_refresh_time"]:CustomSetActive(false)
    self:CleanRefreshGatherTimer()
    if rest_time > 0 then
        self.refresh_gather_timer = CountDown.Instance:AddCountDown(rest_time, 0.5,
        function(elapse_time, total_time)
            local time = math.floor(total_time - elapse_time)
            if time <= 60 then
                if self.node_list["gather_rest_time_value"] then
                    self.node_list["gather_rest_time_value"].text.text = time
                end

                if self.node_list["gather_refresh_time"] then
                    self.node_list["gather_refresh_time"]:CustomSetActive(time > 0)
                end
            end
        end,
        function()
            if self.node_list["gather_refresh_time"] then
                self.node_list["gather_refresh_time"]:SetActive(false)
            end
        end)
    else
        self.node_list["gather_refresh_time"]:CustomSetActive(false)
    end
end

function CrossLongMaiSceneView:FlushGatherDesc()
    local max_num = CrossLongMaiWGData.Instance:GetMaxGatherNum()
    local uesd_num = CrossLongMaiWGData.Instance:GetGatherTimes()
    local rest_num = max_num - uesd_num
    local str_color = rest_num > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED
    self.node_list["gather_desc"].text.text = string.format(Language.CrossLongMai.GatherDesc, ToColorStr(rest_num, str_color), max_num)
end

-- 前往采集
function CrossLongMaiSceneView:OnClickGoToGather()
    local lm_data = CrossLongMaiWGData.Instance
    local max_num = lm_data:GetMaxGatherNum()
    local uesd_num = lm_data:GetGatherTimes()
    if uesd_num >= max_num then
        TipWGCtrl.Instance:ShowSystemMsg(Language.CrossLongMai.GatherNoTimes)
        return
    end

    local main_role = Scene.Instance:GetMainRole()
    if main_role and main_role:GetIsGatherState() then
        TipWGCtrl.Instance:ShowSystemMsg(Language.CrossLongMai.IsGatheringStr)
        return
    end

    -- 采集
    local gather_obj = lm_data:SelectRandGatherObj()
    if not gather_obj then
        TipWGCtrl.Instance:ShowSystemMsg(Language.CrossLongMai.NoGatherTips)
        return
    end

    GuajiWGCtrl.Instance:OnSelectObj(gather_obj, SceneTargetSelectType.SCENE)
end

-- 前往击杀至尊BOSS
function CrossLongMaiSceneView:OnClickFindBoss()
    local boss_list = CrossLongMaiWGData.Instance:GetBossInfoList()
    local boss_data = boss_list[1]
    if not boss_data then
        return
    end

    if boss_data.is_live ~= 1 then
        local server_time = TimeWGCtrl.Instance:GetServerTime()
        local refresh_time = CrossLongMaiWGData.Instance:GetBossNextRefreshTime()
        local rest_time = refresh_time - server_time
        if rest_time > 0 then
            TipWGCtrl.Instance:ShowSystemMsg(Language.CrossLongMai.BossNoRefresh)
            return
        else
            TipWGCtrl.Instance:ShowSystemMsg(Language.CrossLongMai.BossIsKilled)
            return
        end
    end

    -- 前往BOSS
    local monster_id = boss_data.monster_id
    GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
    local sence_id = Scene.Instance:GetSceneId()
    local mian_role = Scene.Instance:GetMainRole()
    if not mian_role then
        return
    end

    local target_obj = Scene.Instance:GetMonstObjByMonstID(monster_id)
    MoveCache.SetEndType(MoveEndType.FightByMonsterId)
    GuajiCache.monster_id = monster_id
    local range = BossWGData.Instance:GetMonsterRangeByid(monster_id)
    GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
    if target_obj then
        GuajiWGCtrl.Instance:MoveToObj(target_obj, range, true)
    else
        local point_cfg = CrossLongMaiWGData.Instance:GetBossRefreshPoint(monster_id)
        local pos_x = point_cfg and point_cfg.pos_x or 0
        local pos_y = point_cfg and point_cfg.pos_y or 0
        GuajiWGCtrl.Instance:MoveToPos(sence_id, pos_x, pos_y, range)
    end
end

function CrossLongMaiSceneView:OnClickHurtTween()
    local cur_status = self.node_list["icon_close"]:GetActive()
    local move_y = cur_status and 210 or 0
    self.node_list["hurt_panel"].rect:DOAnchorPosY(move_y, 0.5)

    self.node_list["icon_close"]:SetActive(not cur_status)
    self.node_list["icon_open"]:SetActive(cur_status)
end

function CrossLongMaiSceneView:OnItemDataChange(change_item_id)
	if CrossLongMaiWGData.Instance:GetIsShopStuff(change_item_id) then
        self:FlushShopStuff()
	end
end

function CrossLongMaiSceneView:FlushShopStuff()
    local stuff_list = CrossLongMaiWGData.Instance:GetDeadDropCfg()
    for i = 1, #stuff_list do
        local consume_id = stuff_list[i].item_id
        self.node_list["stuff_count_" .. i].text.text = ItemWGData.Instance:GetItemNumInBagById(consume_id)
	end
end

function CrossLongMaiSceneView:FlushBossList()
    local boss_list = CrossLongMaiWGData.Instance:GetBossInfoList()
    for k,v in ipairs(self.boss_list) do
        v:SetData(boss_list[k])
    end
end

function CrossLongMaiSceneView:OnClickRuleTip()
    CrossLongMaiWGCtrl.Instance:OpenRuleTipsView()
end

function CrossLongMaiSceneView:OnClickLomhMaiShop()
    ViewManager.Instance:Open(GuideModuleName.LongMaiShop)
end

---------------------------------------
LongMaiBossRender = LongMaiBossRender or BaseClass(BaseRender)
function LongMaiBossRender:LoadCallBack()
    XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClickBoss, self))
end

local boss_status = {
    no_flush = 1,
    flush = 2,
    is_guishu = 3,
    no_guishu = 4,
    can_gather = 5,
}

function LongMaiBossRender:OnFlush()
    self.cur_status = boss_status.no_flush
    local desc = Language.CrossLongMai.BossDescList[1]
    local close_boss_flag = false
    self.node_list["gs_flag"]:CustomSetActive(false)
    self.node_list["box_flag"]:CustomSetActive(false)
    self.node_list["ld_flag"]:CustomSetActive(false)
    
    local boss_icon_id = 9999
    if not IsEmptyTable(self.data) then
        local is_live = self.data.is_live == 1
        if is_live then
            local gs_info = CrossLongMaiWGData.Instance:GetOwnBossInfo(self.data.obj_id)
            if gs_info and gs_info.own_obj_uuid and gs_info.own_obj_uuid.temp_low ~= 0 then
                local role_uuid = RoleWGData.Instance:GetUUid()
                local is_gui_shu = role_uuid == gs_info.own_obj_uuid
                if is_gui_shu then
                    -- 已获归属
                    desc = Language.CrossLongMai.BossDescList[3]
                    self.cur_status = boss_status.is_guishu
                    self.node_list["gs_flag"]:CustomSetActive(true)
                    close_boss_flag = true
                else
                    -- 掠夺归属
                    desc = Language.CrossLongMai.BossDescList[4]
                    self.cur_status = boss_status.no_guishu
                    self.node_list["ld_flag"]:CustomSetActive(true)
                    close_boss_flag = true
                end
            else
                -- 已刷新
                desc = Language.CrossLongMai.BossDescList[2]
                self.cur_status = boss_status.flush
            end
        else
            -- 未刷新
        end

        -- 宝箱可领
        if not is_live and self.data.is_boss == 1 then
            local gather_info = CrossLongMaiWGData.Instance:GetBossGatherInfo(self.data.monster_id)
            if gather_info and gather_info.has_gather == 0 then
                self.cur_status = boss_status.can_gather
                local server_id = gather_info.owner_usid.temp_low
                local plat_type = gather_info.owner_usid.temp_high
                local cross_server_group_seq = CrossServerWGData.Instance:GetCrossServerData(server_id, plat_type)

                desc = string.format(Language.CrossLongMai.BossDescList[5], cross_server_group_seq)
                self.node_list["box_flag"]:CustomSetActive(true)
                close_boss_flag = true
            end
        end

        local mon_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.data.monster_id]
        if mon_cfg then
            boss_icon_id = mon_cfg.small_icon
        end
    end
    self.node_list["boss_flag"]:CustomSetActive(not close_boss_flag)
    self.node_list["desc"].text.text = desc
    local bundle, asset = ResPath.GetBossIcon("wrod_boss_" .. boss_icon_id)
    self.node_list["boss_icon"].image:LoadSprite(bundle, asset, function()
			self.node_list["boss_icon"].image:SetNativeSize()
		end)
end

function LongMaiBossRender:OnClickBoss()
    if self.cur_status == boss_status.no_flush then
        TipWGCtrl.Instance:ShowSystemMsg(Language.CrossLongMai.BossRemindList[1])
        return
    elseif self.cur_status == boss_status.is_guishu then
        TipWGCtrl.Instance:ShowSystemMsg(Language.CrossLongMai.BossRemindList[2])
        return
    else
        if not IsEmptyTable(self.data) then
            local move_to_attack = function ()
                GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
                local monster_id = self.data.monster_id
                local sence_id = Scene.Instance:GetSceneId()
                local monster_obj_list = Scene.Instance:GetMonsterList()
                local target_obj = monster_obj_list[monster_id]
                MoveCache.SetEndType(MoveEndType.FightByMonsterId)
                GuajiCache.monster_id = monster_id
                GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
                local range = BossWGData.Instance:GetMonsterRangeByid(monster_id)
                if target_obj then
                    GuajiWGCtrl.Instance:MoveToObj(target_obj, range, true)
                else
                    GuajiWGCtrl.Instance:MoveToPos(sence_id, self.data.pos_x, self.data.pos_y, range)
                end
            end

            -- 攻击boss
            if self.cur_status == boss_status.flush then
                move_to_attack()
            -- 掠夺归属
            elseif self.cur_status == boss_status.no_guishu then
                local gs_info = CrossLongMaiWGData.Instance:GetOwnBossInfo(self.data.obj_id)
                local role_uuid = RoleWGData.Instance:GetUUid()
                if gs_info == nil or role_uuid == gs_info.own_obj_uuid then
                    return
                end

                local obj = Scene.Instance:GetRoleByUUID(gs_info.own_obj_uuid)
                if obj then
                    GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
                    local is_enemy, str = Scene.Instance:IsEnemy(obj)
                    if is_enemy then
                        local guaji_state = GuajiCache.guaji_type == GuajiType.Auto
                        GuajiWGCtrl.Instance:CancelSelect()
                        GuajiWGCtrl.Instance:StopGuaji()
                        GuajiWGCtrl.Instance:ClearTemporary()
                        MoveCache.SetEndType(MoveEndType.AttackTarget)
                        GuajiCache.target_obj = obj
                        GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj, SceneTargetSelectType.SCENE)
                        if not guaji_state then
                            GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
                        end
                    else
                        if str then
                            SysMsgWGCtrl.Instance:ErrorRemind(str)
                        else
                            SysMsgWGCtrl.Instance:ErrorRemind(Language.Fight.ModeCantAttack2)
                        end

                        -- 策划xzp要求不可攻击玩家，需要去打boss
                        move_to_attack()
                    end
                else
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.NotInHorizon)

                    -- 策划xzp要求不在范围内导航到boss点打boss
                    move_to_attack()
                end
            -- 采集至尊boss龙脉宝箱
            elseif self.cur_status == boss_status.can_gather then
                local gather_info = CrossLongMaiWGData.Instance:GetBossGatherInfo(self.data.monster_id)
                if gather_info == nil then
                    return
                end

                local role_cur_server_group_seq = CrossServerWGData.Instance:RoleCrossServerGroupSeq()
                local server_id = gather_info.owner_usid.temp_low
                local plat_type = gather_info.owner_usid.temp_high
                local cross_server_group_seq = CrossServerWGData.Instance:GetCrossServerData(server_id, plat_type)
                if role_cur_server_group_seq ~= cross_server_group_seq then
                    SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.CrossLongMai.BossRemindList[4], cross_server_group_seq))
                    return
                end

                local gather_obj_list = Scene.Instance:GetObjListByType(SceneObjType.GatherObj)
                local gather_obj = gather_obj_list[gather_info.obj_id]
                if gather_obj then
                    GuajiWGCtrl.Instance:OnSelectObj(gather_obj, SceneTargetSelectType.SCENE)
                end
            end
        end
    end
end

------------------------------------------------------------------
LongMaiBossHurtRender = LongMaiBossHurtRender or BaseClass(BaseRender)

function LongMaiBossHurtRender:OnFlush()
    if not self.data then
        return
    end

    local name = self.data.name
    local name_str = self.data.is_mine and ToColorStr(name, COLOR3B.D_GLOD) or name
    self.node_list.name.text.text = name_str
    self.node_list.num.text.text = self.index
    local damage = CommonDataManager.ConverNumber(self.data.hurt)
    self.node_list.damage.text.text = damage
    self.node_list.per_bg.slider.value = self.data.hurt / BossAssistWGData.Instance:GetNormalHurtInfoMaxValue()
end