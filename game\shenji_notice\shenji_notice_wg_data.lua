ShenJiNoticeWGData = ShenJiNoticeWGData or BaseClass()

function ShenJiNoticeWGData:__init()
    if nil ~= ShenJiNoticeWGData.Instance then
        ErrorLog("[ShenJiNoticeWGData]:Attempt to create singleton twice!")
    end
    ShenJiNoticeWGData.Instance = self

    self:InitConfig()

    --主界面红点
    RemindManager.Instance:Register(RemindName.ShenJiNotice, BindTool.Bind(self.IsShowShenJiNoticeRedPoint, self))
    --用于背包 > 神机装备格子红点
    RemindManager.Instance:Register(RemindName.ShenJiXianShi, BindTool.Bind(self.IsShowShenJiXianShiRedPoint, self))

    self.task_list = {}
    self.task_map = {}
end

function ShenJiNoticeWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.ShenJiNotice)
    RemindManager.Instance:UnRegister(RemindName.ShenJiXianShi)
    ShenJiNoticeWGData.Instance = nil
end

function ShenJiNoticeWGData:InitConfig()
    local all_cfg = ConfigManager.Instance:GetAutoConfig("shenjisystemopenyugaocfg_auto")

    self:MakeXianShiDataList(all_cfg.task)
    self.other_cfg = all_cfg.other[1]
end

--构造神机现世数据
function ShenJiNoticeWGData:MakeXianShiDataList(cfg_list)
    self.task_cfg_list = {}
    self.task_cfg_map = {}
    self.special_anim_show_item = nil

    for i, v in ipairs(cfg_list) do
        local data = {}
        data.task_id = v.task_id
        data.need_role_level = v.need_role_level
        data.need_server_open_day = v.need_server_open_day
        data.item_list = {}

        local fake_item_list = v.fakeitem
        for i = 0, #fake_item_list do
            if i == 0 then
                local item_data = self:GetVirtualItemInfo(fake_item_list[i].item_id)
                self.special_anim_show_item = item_data
                self.special_anim_show_item.is_bind = fake_item_list.is_bind
                self.special_anim_show_item.num = fake_item_list.num
                -- table.insert(data.item_list, self.special_anim_show_item)
            -- else
                -- table.insert(data.item_list, fake_item_list[i])
            end
            table.insert(data.item_list, fake_item_list[i])
        end

        self.task_cfg_map[v.task_id] = data
        table.insert(self.task_cfg_list, data)
    end
end

--获取任务配置
function ShenJiNoticeWGData:GetTaskCfgById(task_id)
    local cfg = self.task_cfg_map[task_id]
    if not cfg then
        print_error("[ShenJiNoticeWGData] GetTaskCfgById 获取不到配置 :", task_id)
    end

    return cfg
end

--获取任务列表
function ShenJiNoticeWGData:GetJiangShiDataList()
    return self.task_cfg_list
end

--获取动画状态的格子数据
function ShenJiNoticeWGData:GetSpecialAnimShowItem()
    return self.special_anim_show_item
end

function ShenJiNoticeWGData:GetOtherCfg()
    return self.other_cfg
end

function ShenJiNoticeWGData:GetSale2NameList()
    local list = {}
    table.insert(list, self.other_cfg.left_name)
    table.insert(list, self.other_cfg.middle_name)
    table.insert(list, self.other_cfg.right_name)
    return list
end





--region 协议数据

--设置神机预告全部信息
function ShenJiNoticeWGData:SetShenJiNoticeInfo(protocol)
    self.task_list = protocol.task_list
    self.task_map = protocol.task_map
    self.special_sale_info = protocol.special_sale_info
    self.special_sale_open_timestamp = protocol.special_sale_open_timestamp
    self:UpdateFunOpenStatus(protocol.function_is_open)     --功能是否开启
end

--更新任务信息
function ShenJiNoticeWGData:UpdateShenJiTaskStatus(protocol)
    for i, task_item in ipairs(protocol.task_change_list) do
        if not self.task_map[task_item.task_id] then
            print_error("不存在的任务状态改变？", task_item.task_id)
        else
            --更新map
            self.task_map[task_item.task_id].task_status = task_item.task_status
            --更新list
            for i, v in ipairs(self.task_list) do
                if v.task_id == task_item.task_id then
                    v.task_status = task_item.task_status
                end
            end
        end
    end
end

--更新特卖信息
function ShenJiNoticeWGData:UpdateShenJiSpecialSaleStatus(protocol)
    self.special_sale_info = protocol.special_sale_info
end

--更新功能是否开启
function ShenJiNoticeWGData:UpdateFunOpenStatus(function_is_open)
    self.function_is_open = function_is_open
end

function ShenJiNoticeWGData:IsAllTaskFetched()
    local is_all_task_fetched = true
    for i, v in ipairs(self.task_list) do
        if v.task_status ~= SHEN_JI_YU_GAO_TASK_STATUS.HAS_GET_REWARD then
            is_all_task_fetched = false
            break
        end
    end
    return is_all_task_fetched
end

--功能是否开启
function ShenJiNoticeWGData:GetNoticeFunIsOpen()
    -- return self.function_is_open == 1
    return self:IsAllTaskFetched()
end

function ShenJiNoticeWGData:GetTaskById(task_id)
    local task_item = self.task_map[task_id]
    if not task_item then
        print_error("神机预告，获取不到任务数据：", task_id, self.task_map)
    end
    return task_item
end

--获取当前特卖id
function ShenJiNoticeWGData:GetNowSpecialSaleId()
    if not self.special_sale_info then
        return 1
    end

    return self.special_sale_info.now_special_sale_id
end

--获取当前特卖状态
function ShenJiNoticeWGData:GetNowSpecialSaleStatus()
    if not self.special_sale_info then
        return SHEN_JI_YU_GAO_SPECIAL_SALE_STATUS.CLOSE
    end

    return self.special_sale_info.special_sale_status
end

--获取当前特卖id状态
function ShenJiNoticeWGData:GetNowSpecialSaleIdStatus()
    if not self.special_sale_info then
        return SHEN_JI_YU_GAO_SPECIAL_SALE_STATUS.CLOSE
    end

    return self.special_sale_info.now_special_sale_id_status
end

--获取第一个特卖开始时间
function ShenJiNoticeWGData:GetNowSpecialSaleStartTime()
    return self.special_sale_open_timestamp or 0
end

function ShenJiNoticeWGData:GetFunOpenStartTime()
    local task_cfg_list = self:GetJiangShiDataList()
    local time = 0
    if IsEmptyTable(task_cfg_list) then return time end
    local end_task = task_cfg_list[#task_cfg_list]
    local end_day = end_task.need_server_open_day
    local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local lerp_day = end_day - cur_day
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local now_day_time = TimeWGCtrl.Instance:NowDayTimeStart(server_time)
    time = now_day_time + lerp_day * 86400
    return time
end

--获取当前特卖结束时间
function ShenJiNoticeWGData:GetNowSpecialSaleEndTime()
    if not self.special_sale_info then
        return 0
    end

    return self.special_sale_info.now_special_sale_end_time
end

--获取特卖1剩余时间（仅供神机百炼使用）
function ShenJiNoticeWGData:GetSale1EndTime()
    local is_open_special_sale = ShenJiNoticeWGData.Instance:GetNowSpecialSaleStatus() ~= SHEN_JI_YU_GAO_SPECIAL_SALE_STATUS.CLOSE
    local now_special_sale_id = ShenJiNoticeWGData.Instance:GetNowSpecialSaleId()
    local now_special_sale_id_status = ShenJiNoticeWGData.Instance:GetNowSpecialSaleIdStatus()
    local is_buy = now_special_sale_id_status == SHEN_JI_YU_GAO_SPECIAL_SALE_ID_STATUS.HAS_GET
    local time = 0
    --当前在特卖 并且当前在特卖1 并且还没买
    if is_open_special_sale and now_special_sale_id == 1 and not is_buy then
        time = ShenJiNoticeWGData.Instance:GetNowSpecialSaleEndTime()
    end
    return time
end

--endregion


--region 红点
--用于主界面按钮判断红点
function ShenJiNoticeWGData:IsShowShenJiNoticeRedPoint()
    -- if not self.special_sale_info then
    --     return 0
    -- end

    -- local is_open_special_sale = self:GetNowSpecialSaleStatus() ~= SHEN_JI_YU_GAO_SPECIAL_SALE_STATUS.CLOSE
    -- if is_open_special_sale then
    --     return self:IsShowShenJiSpecialSaleRedPoint()
    -- else
    --     return self:IsShowShenJiXianShiRedPoint()
    -- end
    return self:IsShowShenJiXianShiRedPoint()
end

--神机现世红点
function ShenJiNoticeWGData:IsShowShenJiXianShiRedPoint()
    if IsEmptyTable(self.task_list) then
        return 0
    end

    -- if not self.special_sale_info then
    --     return 0
    -- end

    --开了特卖，不显示红点
    -- local is_open_special_sale = self:GetNowSpecialSaleStatus() ~= SHEN_JI_YU_GAO_SPECIAL_SALE_STATUS.CLOSE
    -- if is_open_special_sale then
    --     return 0
    -- end

    local open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local role_level = RoleWGData.Instance:GetRoleLevel()
    for i, v in ipairs(self.task_list) do
        local task_cfg = self:GetTaskCfgById(v.task_id)
        --服务器的任务状态为COMPLETE ,天数不满足还是不能领取奖励
        if v.task_status == SHEN_JI_YU_GAO_TASK_STATUS.COMPLETE and task_cfg and open_server_day >= task_cfg.need_server_open_day and role_level >= task_cfg.need_role_level then
            return 1
        end
    end

    return 0
end

--神机特卖红点
function ShenJiNoticeWGData:IsShowShenJiSpecialSaleRedPoint()
    if IsEmptyTable(self.special_sale_info) then
        return 0
    end

    local special_sale_status = self.special_sale_info.special_sale_status
    local special_sale_id_status = self.special_sale_info.now_special_sale_id_status

    if special_sale_status ~= SHEN_JI_YU_GAO_SPECIAL_SALE_STATUS.OPEN then
        return 0
    end

    -- 当玩家有未领取奖励时有小红点
    -- 当玩家未购买时，有小红点
    if special_sale_id_status ~= SHEN_JI_YU_GAO_SPECIAL_SALE_ID_STATUS.HAS_GET then
        return 1
    end

    return 0
end

--神机百炼使用
function ShenJiNoticeWGData:GetSale1Remind()
    if IsEmptyTable(self.special_sale_info) then
        return 0
    end

    --不是特卖1， 返回
    local now_special_sale_id = ShenJiNoticeWGData.Instance:GetNowSpecialSaleId()
    if now_special_sale_id ~= 1 then
        return 0
    end

    local special_sale_id_status = self.special_sale_info.now_special_sale_id_status
    if special_sale_id_status == SHEN_JI_YU_GAO_SPECIAL_SALE_ID_STATUS.NOT_GET then
        return 1
    end

    return 0
end
--endregion

--获取虚拟物品信息
function ShenJiNoticeWGData:GetVirtualItemInfo(item_id)
    local equip = self:GetItemInfo(item_id)
    local data = {
        item_id = item_id,
        equip = equip,
        is_virtual = true           --这个不可修改
    }

    return data
end

--获取虚拟物品信息
function ShenJiNoticeWGData:GetItemInfo(item_id)
    local data = {
        item_id = item_id,            --这个不可修改
          base_color = 5,                    --虚拟物品品质颜色
          base_star = 6,                     --虚拟物品星级
          level = 5,                    --虚拟物品等级
          grade = 1,                    --虚拟物品阶级
          special_effect_level = 0,     --虚拟物品觉醒等级
          max_rand_index = 0,           --虚拟物品刻铭上限索引

          --虚拟物品随机属性
          rand_attr_list = {
              0,
              0,
              0,
              0,
              0,
              0,
          },

          is_virtual = true,            --这个不可修改
    }
    return data
end

function ShenJiNoticeWGData:GetPrintLog()
    local tab_str = "ShenJiNoticeWGData:GetPrintLog\n"
    local is_all_task_fetched = ShenJiNoticeWGData.Instance:IsAllTaskFetched()
    local is_open = FunOpen.Instance:GetFunIsOpened(FunName.ShenJiNotice)
    local is_show = not is_all_task_fetched and is_open
    tab_str = tab_str .. "is_all_task_fetched:".. tostring(is_all_task_fetched) .. "\n"
    tab_str = tab_str .. "is_open:".. tostring(is_open) .. "\n"
    return tab_str
end
