PierreDirectPurchaseWGData = PierreDirectPurchaseWGData or BaseClass()
local this = PierreDirectPurchaseWGData

function this:__init()
	if this.Instance then
		error("[PierreDirectPurchaseWGData] Attempt to create singleton twice!")
		return
	end

	this.Instance = self

	self.daliy_reward_flag = 1
	self.zhigou_list = {}
	self.buy_count = {}
	self:InitCfg()

	RemindManager.Instance:Register(RemindName.PierreDirectPurchase, BindTool.Bind(self.GetRemind, self))
end

function this:__delete()
	RemindManager.Instance:UnRegister(RemindName.PierreDirectPurchase)

	this.Instance = nil
end

function this:InitCfg()
	self.all_cfg = ConfigManager.Instance:GetAutoConfig("zhenpingzhigoucfg_auto")
	self.direct_purchase_item_cfg = ListToMap(self.all_cfg.zhigou_item_cfg, "buy_type", "seq")
	self.zhigou_list_cfg = ListToMap(self.all_cfg.zhigou_cfg,"buy_type")
	local open_day, real_close_day, close_day, min_role_level, min_vip_level
	for k, v in ipairs(self.all_cfg.zhigou_cfg) do
		if open_day == nil or v.open_day < open_day then
			open_day = v.open_day
		end

		real_close_day = v.open_day + v.close_day
		if close_day == nil or real_close_day > close_day then
			close_day = real_close_day
		end

		if min_role_level == nil or v.level < min_role_level then
			min_role_level = v.min_role_level
		end

		if min_vip_level == nil or v.vip_level < min_vip_level then
			min_vip_level = v.min_vip_level
		end
	end
	self.act_open_day_data = {open_day = open_day or 0, close_day = close_day or 0,
								min_role_level = min_role_level or 0,
								min_vip_level = min_vip_level or 0,}

	self.daily_reward_item = self.all_cfg.reward_cfg[1].reward_list[0]
end

function this:GetDirectPurchaseCfg()
	return self.all_cfg.zhigou_cfg
end

function this:GetDirectPurchaseCfgByType(buy_type)
	for k,v in pairs(self.all_cfg.zhigou_cfg) do
		if v.buy_type == buy_type then
			return v
		end
	end

	return {}
end

function this:GetOtherCfg()
	return self.all_cfg.other
end

function this:GetActDayData()
	return self.act_open_day_data.open_day
			, self.act_open_day_data.close_day
			, self.act_open_day_data.min_role_level
			, self.act_open_day_data.min_vip_level
end

function this:SetBuyInfo(protocol)
	self.daliy_reward_flag = protocol.daliy_reward_flag
	self.zhigou_list = protocol.zhigou_list
	self.buy_count = protocol.buy_count
end

function this:GetIsNoSendProtocal()
	return IsEmptyTable(self.zhigou_list)
end

function this:GetIsGetDaliyReward()
	return self.daliy_reward_flag == 1
end

function this:GetDirectPurchaseInfoByType(buy_type)
	return self.zhigou_list[buy_type]
end

function this:GetBuyCountBySeq(index)
	return self.buy_count[index] or 0
end

function this:GetDirectPurchaseItemInfoByType(buy_type)
	return self.direct_purchase_item_cfg[buy_type]
end

function this:GetDirectPurchaseSeqCfg(buy_type, seq)
	return (self.direct_purchase_item_cfg[buy_type] or {})[seq]
end

function this:GetZhigouListInfoByType(buy_type)
	return self.zhigou_list_cfg[buy_type] or {}
end

function this:GetBuyTypeTimestamp(buy_type)
	local info = self:GetDirectPurchaseInfoByType(buy_type)
	local close_timestamp = info and info.close_timestamp or 0
	return close_timestamp
end

function this:GetBuyTypeSeqCanBuy(buy_type, seq)
	local cfg = self:GetDirectPurchaseSeqCfg(buy_type, seq)
	if cfg == nil then
		return false
	end

	local buy_count = self:GetBuyCountBySeq(cfg.index)
	return cfg.buy_count_limit > buy_count
end

function this:GetBuyTypeCanBuy(buy_type)
	local buy_data = self:GetDirectPurchaseItemInfoByType(buy_type)
	if buy_data then
		for k,v in ipairs(buy_data) do
			if self:GetBuyTypeSeqCanBuy(buy_type, v.seq) then
				return true
			end
		end
	end

	return false
end

function this:GetBuyTypeAllNotBuy(buy_type)
	local buy_data = self:GetDirectPurchaseItemInfoByType(buy_type)
	if buy_data then
		for k, v in ipairs(buy_data) do
			if not self:GetBuyTypeSeqCanBuy(buy_type, v.seq) then
				return false
			end
		end
	end

	return true
end

function this:CheckActIsOpen()
	local shield_high_charge = RoleWGData.GetIsShieldHighCharge()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local total_open_day, total_close_day = self:GetActDayData()
	if server_day < total_open_day or server_day >= total_close_day then
		return false
	end

	-- 满足开启
	local cfg = self:GetDirectPurchaseCfg()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local vip_level = RoleWGData.Instance:GetAttr("vip_level") or 0
	local role_level = RoleWGData.Instance:GetAttr("level") or 0
	local acc_total_gold = RechargeWGData.Instance:GetHistoryRecharge()

	local level_meet, day_meet, recharge_meet, buy_meet, shield_meet = false, false, false, false, false
	local open_day, real_close_day, close_day, close_timestamp
	for k, v in ipairs(cfg) do
		open_day = v.open_day
		close_day = v.close_day
		real_close_day = open_day + close_day

		level_meet = role_level >= v.level and vip_level >= v.vip_level
		day_meet = server_day >= open_day and server_day < real_close_day
		recharge_meet = acc_total_gold >= v.gold_total
		
		if v.last_buy_type > 0 then
			buy_meet = not self:GetBuyTypeCanBuy(v.last_buy_type)
		else
			buy_meet = true
		end

		shield_meet = shield_high_charge and (v.high_charge_shield == 1)
		if level_meet and day_meet and recharge_meet and buy_meet and not shield_meet then
			close_timestamp = self:GetBuyTypeTimestamp(v.buy_type)
			if server_time < close_timestamp then
				return true
			end
		end
	end

	return false
end

function this:GetShowList()
	local show_list = {}
	local shield_high_charge = RoleWGData.GetIsShieldHighCharge()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local total_open_day, total_close_day = self:GetActDayData()
	if server_day < total_open_day or server_day >= total_close_day then
		return show_list
	end

	local cfg = self:GetDirectPurchaseCfg()
	local vip_level = RoleWGData.Instance:GetAttr("vip_level") or 0
	local role_level = RoleWGData.Instance:GetAttr("level") or 0
	local acc_total_gold = RechargeWGData.Instance:GetHistoryRecharge()

	local open_day, real_close_day, close_day
	local level_meet, day_meet, recharge_meet, buy_meet, shield_meet = false, false, false, false, false
	for k, v in ipairs(cfg) do
		open_day = v.open_day
		close_day = v.close_day
		real_close_day = open_day + close_day

		level_meet = role_level >= v.level and vip_level >= v.vip_level
		day_meet = server_day >= open_day and server_day < real_close_day
		recharge_meet = acc_total_gold >= v.gold_total
		if v.last_buy_type > 0 then
			buy_meet = not self:GetBuyTypeCanBuy(v.last_buy_type)
		else
			buy_meet = true
		end

		local is_can_buy = self:GetBuyTypeCanBuy(v.buy_type)

		shield_meet = shield_high_charge and (v.high_charge_shield == 1)
		if level_meet and day_meet and recharge_meet and buy_meet and not shield_meet then
			local data = {buy_type = v.buy_type, is_buy_all = v.buy_all == 1,
						buy_all_price = v.buy_all_price, rmb_type = v.rmb_type,
						title_image = v.title_image,
						toggle_name = v.toggle_name, advertisement = v.advertisement,
						title_effect = v.title_effect, special_show_img = v.special_show_img,
						rebate_value = v.rebate_value, is_can_buy = is_can_buy}
			table.insert(show_list, data)
		end
	end

	return show_list
end

function this:GetRemind()
	if not self:CheckActIsOpen() then
		return 0
	end

	if self:GetIsGetDaliyReward() then
		return 0
	end

	return 1
end

function this:SetTest()
	if not self.is_test then
		self.is_test = true
		self.test_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	end
end

function this:GetShowRewardByType(buy_type)
	local show_special = false
	local buy_data = self:GetDirectPurchaseItemInfoByType(buy_type)
	if IsEmptyTable(buy_data) then
		return show_special, {}
	end

	if #buy_data > 1 then
		return show_special, buy_data
	end

	local model_show_type2 = buy_data[1].model_show_type2
	if model_show_type2 and model_show_type2 ~= "" then
		show_special = true
	end

	return show_special, buy_data
end

function this:GetBuyRewardList(buy_type, buy_seq)
	local show_reward_list = {}
	local buy_data = self:GetDirectPurchaseItemInfoByType(buy_type)
	if IsEmptyTable(buy_data) then
		return show_reward_list
	end

	local reward_list = {}
	local is_buy_all = buy_seq == 0
	local gold_id = COMMON_CONSTS.VIRTUAL_ITEM_GOLD
	local function get_reward_list(data)
		if IsEmptyTable(data) then
			return
		end

		local return_gold = data.return_gold or 0
		if return_gold > 0 then
			reward_list[gold_id] = reward_list[gold_id] or {}
			reward_list[gold_id][0] = reward_list[gold_id][0] or 0
			reward_list[gold_id][0] = reward_list[gold_id][0] + return_gold
		end

		local data_list = data.buy_items
		for i = 0, #data_list do
			local temp_data = data_list[i]
			if temp_data then
				reward_list[temp_data.item_id] = reward_list[temp_data.item_id] or {}
				reward_list[temp_data.item_id][temp_data.is_bind] = reward_list[temp_data.item_id][temp_data.is_bind] or 0
				reward_list[temp_data.item_id][temp_data.is_bind] = reward_list[temp_data.item_id][temp_data.is_bind] + temp_data.num
			end
		end
	end

	for k, v in ipairs(buy_data) do
		if is_buy_all or v.seq == buy_seq then
			get_reward_list(v)
		end
	end

	for k, v in pairs(reward_list) do
		for i, j in pairs(v) do
			local temp_data = {}
			local item_cfg = ItemWGData.Instance:GetItemConfig(k)
			if item_cfg then
				temp_data.item_id = k
				temp_data.is_bind = i
				temp_data.num = j
				temp_data.sort = k == gold_id and 999 or item_cfg.color
				table.insert(show_reward_list, temp_data)
			end
		end
	end

	if not IsEmptyTable(show_reward_list) then
		table.sort(show_reward_list, SortTools.KeyUpperSorter("sort"))
	end

	return show_reward_list
end

function this:GetBugTypeIsOpen(buy_type)
	local buy_cfg = self.zhigou_list_cfg and self.zhigou_list_cfg[buy_type] or {}
	local reason_str = Language.XianLingGuZhen.CanNotJump[1]
	if IsEmptyTable(buy_cfg) then
		return false, reason_str
	end

	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local my_vip_level = RoleWGData.Instance.role_vo.vip_level
	local level = RoleWGData.Instance.role_vo.level

	if buy_cfg.open_day > open_day then
		reason_str = string.format(Language.XianLingGuZhen.CanNotJump[2], buy_cfg.open_day)
		return false, reason_str
	end

	if buy_cfg.open_day + buy_cfg.close_day < open_day then
		reason_str = Language.XianLingGuZhen.CanNotJump[3]
		return false, reason_str
	end

	if my_vip_level < buy_cfg.vip_level or level < buy_cfg.level then
		reason_str = Language.XianLingGuZhen.CanNotJump[4]
		return false, reason_str
	end

	local acc_total_gold = RechargeWGData.Instance:GetHistoryRecharge()
	local recharge_meet = acc_total_gold >= buy_cfg.gold_total
	if not recharge_meet then
		return false ,""
	end

	local buy_meet = true
	if buy_cfg.last_buy_type > 0 then
		buy_meet = not self:GetBuyTypeCanBuy(buy_cfg.last_buy_type)
	end

	if not buy_meet then
		return false ,""
	end

	return true, ""
end

-- 搞特殊, 写死
function this:GetIsShowMainSpecialBubble()
	local can_buy = self:GetBuyTypeSeqCanBuy(1, 1)
	if not can_buy then
		return false
	end

	local cfg = self:GetDirectPurchaseSeqCfg(1, 1)
	if not cfg then
		return false
	end

	for k,v in pairs(cfg.buy_items) do
		if v.item_id == 22116 then
			return true
		end
	end

	return false
end

--获取商品的结束时间.
function this:GetItemEndTimeByType(buy_type)
	local end_time = 0		--结束时间.

	local cfg = self.zhigou_list_cfg and self.zhigou_list_cfg[buy_type]
	if not cfg then
		return end_time
	end

	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local real_close_day = cfg.open_day + cfg.close_day

	if server_day >= cfg.open_day and server_day < real_close_day then
		local today_rest_time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())  --获取当天剩余时间
		--开服时间 < real_close_day结束天数，所以还得-1再+当天剩余时间.
		end_time = (real_close_day - server_day - 1) * 24 * 3600 + today_rest_time
	end

	return end_time
end