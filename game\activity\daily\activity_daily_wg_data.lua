ActivityWGData = ActivityWGData or BaseClass(BaseWGCtrl)
Day_Act_Type = {
	Daily = 1,
	<PERSON><PERSON> = 2,
	<PERSON><PERSON><PERSON><PERSON> = 3,
	<PERSON><PERSON> = 4,
	<PERSON><PERSON><PERSON> = 5,
	<PERSON><PERSON><PERSON> = 6,
	<PERSON><PERSON>heng = 7,
	<PERSON><PERSON><PERSON> = 8,
	GuardStatue = 9,
	<PERSON><PERSON><PERSON><PERSON> = 10,
	<PERSON><PERSON><PERSON>onorhalls = 11,
	KfPvp = 12,
	<PERSON>f<PERSON>huiJing = 13,
	<PERSON>un<PERSON>ianDou = 14,
	<PERSON><PERSON>houPlaza = 15,
	Swim = 17,
}

Day_Act_BigType = {
	Nor = 0,
	Daily = 1,
	Camp = 2,
	Cross = 3,
	Husong = 4,
}

Daily_Fb_Status = {
	Standby = 1,
	Start = 2,
	Stop = 3
}

SYT_Task_Type = {
	Monster = 1,
	Gather = 2,
}

ActivityWGData.Daily_Index = 1
ActivityWGData.Husong_Index = 2
ActivityWGData.Camp_Index = 5
ActivityWGData.Cross_Index = 7

function ActivityWGData:DailyInit()
	self.daily_act_status = {}
	self.yaoshou_plaza_info = {
		reason = 0,
		wave_index = 0,
		role_num = 0,
		monster_num = 0,
		fb_lv = 0,
		user_list = {},
		own_score = 0,
	}
	self.suoyaota_info = {
		reason = 0,
		fb_lv = 0,
		task_list = {},
		user_list = {},
		own_score = 0,
	}
	self.tab_str_list = nil
	self.tab_data_list = nil
	self.zhuxie_info = {
		boss_info = {boss_id = 0,
					next_boss_refresh_time = 0,
					boss_cur_hp = 0,
					boss_max_hp = 1,
					},
		taskinfo_list = {}
	}
	self.zhuxie_boss = {
		boss_id = 0,
		boss_hp = 0,
		max_hp = 1,
	}

	self.zhongkuizhuagui_info = {
		zhuagui_day_gethuoli = 0,
		zhuagui_day_catch_count = 0,
	}
	self.zhongkuizhuagui_fb_info = {
		reason = 0,
		monster_count = 0,
		ishave_boss = 0,
		boss_isdead = 0,
		zhuagui_info_list = {},
	}

	self.kf_zhuxie_info = {}
	self.kf_zhuxie_boss_info = {}
	self.kf_zhuxie_cur_task_id = nil
end

function ActivityWGData.GetEndViewData()
	local data = {}
	data.type = 0
	data.score = 0
	data.exp = 0
	data.bind_coin = 0
	data.item_list = {}
	return data
end

function ActivityWGData:InitTabDataList()
	self.tab_str_list = {}
	self.tab_data_list = {}
	self.type_list = {}
	local daily_act_cfg = ConfigManager.Instance:GetAutoConfig("daily_act_cfg_auto")
	local function getOneIndex()
		for i = 1, 100 do
			if self.tab_str_list[i] == nil then
				return i
			end
		end
		return 1
	end
	for i,v in ipairs(daily_act_cfg.show_cfg) do
		if v.big_type > 0 then
			if self.type_list[v.big_type] == nil then
				local index = getOneIndex()
				self.type_list[v.big_type] = index
				self.tab_data_list[index] = {}
				self.tab_str_list[index] = v.type_name
				self.tab_data_list[index][0] = v
			else
				local index = self.type_list[v.big_type]
				self.tab_data_list[index][#self.tab_data_list[index] + 1] = v
			end
		else
			local index = getOneIndex()
			self.tab_str_list[index] = v.act_name
			self.tab_data_list[index] = v
		end
	end
end

-- 获取tab名字列表
function ActivityWGData:GetActTabStringList()
	if nil == self.tab_str_list then
		self:InitTabDataList()
	end
	return self.tab_str_list
end

-- 根据索引获取活动数据
function ActivityWGData:GetActDataByIndex(index)
	if nil == self.tab_data_list then
		self:InitTabDataList()
	end
	return self.tab_data_list[index]
end

-- 根据act_type获取活动数据
function ActivityWGData:GetActDataByActType(act_type)
	local daily_act_cfg = ConfigManager.Instance:GetAutoConfig("daily_act_cfg_auto").show_cfg
	for k,v in pairs(daily_act_cfg) do
		if v.act_type == act_type then
			return v
		end
	end
end

function ActivityWGData:GetLevelCfgByType(act_type)
	if nil == act_type then return nil end
	local daily_act_cfg = ConfigManager.Instance:GetAutoConfig("daily_act_cfg_auto")
	local lv = RoleWGData.Instance.role_vo.level
	for i,v in ipairs(daily_act_cfg.level_cfg) do
		if act_type == v.act_type and lv >= v.level_min and lv <= v.level_max then
			return v
		end
	end
	return nil
end

-- 设置妖兽广场活动状态
function ActivityWGData:SetDailyActStatus(type, info)
	self.daily_act_status[type] = {}
	self.daily_act_status[type].status = info.status
	self.daily_act_status[type].next_time = info.next_status_time
	self.daily_act_status[type].next_standby_time = info.next_standby_time
	self.daily_act_status[type].next_stop_time = info.next_stop_time
	if info.datais_valid == 1 then
		self.daily_act_status[type].syt_max_score = info.syt_max_score
		self.daily_act_status[type].quanfu_topscore = info.quanfu_topscore
		self.daily_act_status[type].quanfu_topscore_uid = info.quanfu_topscore_uid
		self.daily_act_status[type].quanfu_topscore_name = info.quanfu_topscore_name
		self.daily_act_status[type].next_freetimes_invalid_time = info.next_freetimes_invalid_time
	end
	if (Scene.Instance:GetSceneType() == SceneType.YaoShouPlaza and Day_Act_Type.YaoShouPlaza == type)
	or (Scene.Instance:GetSceneType() == SceneType.SuoYaoTa and Day_Act_Type.SuoYaoTa == type) then
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(info.next_stop_time)
	end
end

-- 获取某一个日常活动状态
function ActivityWGData:GetDailyActStatus(type)
	return self.daily_act_status[type]
end

-- 获取所有日常活动状态
function ActivityWGData:GetAllDailyActStatus()
	return self.daily_act_status
end

function ActivityWGData:GetEnterCount(act_type)
	local enter_count = 0
	if act_type == Day_Act_Type.YaoShouPlaza then
		enter_count = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_JOIN_YAOSHOUGUANGCHANG)
	elseif act_type == Day_Act_Type.SuoYaoTa then
		enter_count = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_JOIN_SUOYAOTA)
	end
	local status = self.daily_act_status[act_type] or {}
	local next_freetimes_invalid_time = status.next_freetimes_invalid_time or 0
	if next_freetimes_invalid_time > TimeWGCtrl.Instance:GetServerTime() then
		enter_count = math.max(enter_count - 1, 0)
	end
	return enter_count
end

-- 妖兽广场信息
function ActivityWGData:SetYaoShouPlazaInfo(info)
	self.yaoshou_plaza_info.reason = info.reason
	self.yaoshou_plaza_info.wave_index = info.wave_index
	self.yaoshou_plaza_info.fb_lv = info.fb_lv
	self.yaoshou_plaza_info.role_num = info.role_num
	self.yaoshou_plaza_info.monster_num = info.monster_num
	self.yaoshou_plaza_info.user_list = info.user_list
	for k,v in pairs(self.yaoshou_plaza_info.user_list) do
		if v.uid == GameVoManager.Instance:GetMainRoleVo().role_id then
			self.yaoshou_plaza_info.own_score = v.score
		end
	end
end

function ActivityWGData:GetYaoShouPlazaInfo()
	return self.yaoshou_plaza_info
end

function ActivityWGData:InitYaoShouPlazaInfo()
	self.yaoshou_plaza_info = {
		reason = 0,
		wave_index = 0,
		fb_lv = 0,
		user_list = {},
		own_score = 0,
	}
end

-- 锁妖塔信息
function ActivityWGData:SetSuoYaoTaInfo(info)
	self.suoyaota_info.reason = info.reason
	self.suoyaota_info.fb_lv = info.fb_lv
	self.suoyaota_info.task_list = info.task_list
	self.suoyaota_info.user_list = info.user_list
	for k,v in pairs(self.suoyaota_info.user_list) do
		if v.uid == GameVoManager.Instance:GetMainRoleVo().role_id then
			self.suoyaota_info.own_score = v.score
		end
	end
end

function ActivityWGData:GetSuoYaoTaInfo()
	return self.suoyaota_info
end

function ActivityWGData:InitSuoYaoTaInfo()
	self.suoyaota_info = {
		reason = 0,
		fb_lv = 0,
		task_list = {},
		user_list = {},
		own_score = 0,
	}
end

function ActivityWGData:GetSuoYaoTaTaskCfg(fb_lv, task_index)
	local task_list = ConfigManager.Instance:GetAutoConfig("yaoshouguangchang_auto").task_list
	for k,v in pairs(task_list) do
		if v.lv_idx == fb_lv and v.task_index == task_index then
			return v
		end
	end
end

function ActivityWGData:GetCurSYTTaskInfo()
	local task_list = self.suoyaota_info.task_list or {}
	local task_info
	for i,v in ipairs(task_list) do
		if v.param_num < v.param_max then
			return v
		end
		task_info = v
	end
	return task_info
end

function ActivityWGData:GetYaoShouPlazaData()
	local fb_task_data = {}
	local status_t = self.daily_act_status[Day_Act_Type.YaoShouPlaza]
	if status_t and status_t.status == Daily_Fb_Status.Standby then
		fb_task_data[1] = {
			tab_img = "word_fuben_info",
			text_t = {
				{str = string.format(Language.FuBen.FuBenRole, self.yaoshou_plaza_info.role_num),},
				{str = Language.FuBen.StandbyTime, timer = status_t.next_time},
			},
		}
	else
		fb_task_data[1] = {
			tab_img = "word_fuben_info",
			text_t = {
				{str = string.format(Language.FuBen.OwnScore, self.yaoshou_plaza_info.own_score),},
				{str = string.format(Language.FuBen.CurWaveNumber, self.yaoshou_plaza_info.wave_index),},
				{str = string.format(Language.FuBen.CurLeftMonster, self.yaoshou_plaza_info.monster_num),},
				{str = Language.FuBen.YSGCKillDec,},
			},
		}
	end
	return fb_task_data
end

function ActivityWGData:GetSuoYaoTaData()
local fb_task_data = {}
	local status_t = self.daily_act_status[Day_Act_Type.SuoYaoTa]
	local role_num = 0
	local move_list = Scene.Instance:GetObjMoveInfoList()
	for _,v in pairs(move_list) do
		local vo = v:GetVo()
		if vo.obj_type == SceneObjType.Role then
			role_num = role_num + 1
		end
	end
	if status_t and status_t.status == Daily_Fb_Status.Standby then
		fb_task_data[1] = {
			tab_img = "word_fuben_info",
			text_t = {
				{str = string.format(Language.FuBen.FuBenRole, role_num),},
				{str = Language.FuBen.StandbyTime, timer = status_t.next_time},
			},
		}
	else
		local task_info = self:GetCurSYTTaskInfo()
		if nil == task_info then return fb_task_data end
		local obj_cfg
		if task_info.task_type == SYT_Task_Type.Monster then
			obj_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[task_info.param_id]
		else
			obj_cfg = ConfigManager.Instance:GetAutoConfig("gather_auto").gather_list[task_info.param_id]
		end
		local obj_name = obj_cfg and obj_cfg.name or ""
		fb_task_data[1] = {
			tab_img = "word_fuben_info",
			text_t = {
				{str = string.format(Language.FuBen.OwnScore, self.suoyaota_info.own_score),},
				{str = string.format(Language.FuBen.SuoYaoTaTaskDec[task_info.task_index], obj_name, task_info.param_num, task_info.param_max),},
			},
		}
	end
	return fb_task_data
end

function ActivityWGData:GetShuiJingData()
	local fb_task_data = {}
	local max_caiji = ConfigManager.Instance:GetAutoConfig("activityshuijing_auto").other[1].gather_max_times
	local caiji_count = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_SHUIJING_GATHER)
	fb_task_data[1] = {
		tab_img = "word_fuben_info",
		text_t = {
			{str = string.format(Language.FuBen.LeaveCount, math.max(max_caiji - caiji_count, 0)),},
		},
	}
	return fb_task_data
end

function ActivityWGData:SetZhuXieInfo(info)
	self.zhuxie_info.boss_info.boss_id = info.boss_id
	self.zhuxie_info.boss_info.next_boss_refresh_time = info.next_boss_refresh_time
	self.zhuxie_info.boss_info.boss_cur_hp = info.boss_cur_hp
	self.zhuxie_info.boss_info.boss_max_hp = info.boss_max_hp
	self.zhuxie_info.taskinfo_list = info.taskinfo_list
	self.zhuxie_info.double_task_id_1 = info.double_task_id_1
	self.zhuxie_info.double_task_id_2 = info.double_task_id_2
	self.zhuxie_info.double_task_end_time = info.double_task_end_time

end

function ActivityWGData:SortTaskList( task_list)
	if not self.old_task_list then
		for i=1,#task_list do
			if task_list[i].is_fetch_reward == 1 then
				task_list[i].sort_index = 1
			elseif task_list[i].is_double == 1 then
				task_list[i].sort_index = 5
			else
				task_list[i].sort_index = 3
			end
		end
	else
		for i=1,#task_list do
			if task_list[i].is_fetch_reward == 1 then
				task_list[i].sort_index = 1
			elseif task_list[i].is_double == 1 then
				task_list[i].sort_index = 5
			else
				task_list[i].sort_index = 3
			end
			if self.old_task_list[i].param_value < task_list[i].param_value and task_list[i].is_fetch_reward ~= 1 then
				task_list[i].sort_index = 8
			end
		end
	end
	self.old_task_list = task_list
	table.sort(task_list, SortTools.KeyUpperSorter("sort_index"))
	return task_list
end

function ActivityWGData:GetZhuXieTaskList()
	local task_list = {}
	local zhuxie_task_cfg = ConfigManager.Instance:GetAutoConfig("zhuxieconfig_auto").task
	for i,v in ipairs(zhuxie_task_cfg) do
		local task_info_list = self.zhuxie_info.taskinfo_list[i] or {}
		local temp_list = {}
		temp_list.cfg = v
		temp_list.param_value = task_info_list.param_value or 0
		temp_list.is_fetch_reward = task_info_list.is_fetch_reward or 0
		local double_task_id_1 = self.zhuxie_info.double_task_id_1 or 0
		local double_task_id_2 = self.zhuxie_info.double_task_id_2 or 0
		if double_task_id_1 == v.task_id or double_task_id_2 == v.task_id then
			temp_list.is_double = 1
			temp_list.double_task_end_time = self.zhuxie_info.double_task_end_time or 0
		else
			temp_list.is_double = 0
			temp_list.double_task_end_time = 0
		end
		table.insert(task_list,temp_list)
	end
	task_list = self:SortTaskList(task_list)
	return task_list
end

function ActivityWGData:SetZhuXieBossHp(protocol)
	self.max_hurt = 0
	self.zhuxie_boss.boss_id = protocol.boss_id
	self.zhuxie_boss.boss_hp = protocol.boss_hp
	self.zhuxie_boss.max_hp = protocol.max_hp
	self.zhuxie_boss.hurt_rank_list = protocol.hurt_rank_list
	for i,v in ipairs(protocol.hurt_rank_list) do
		self.max_hurt = math.max(self.max_hurt,v.hurt) 
	end
	
end

function ActivityWGData:GetZhuXieProValue(value)
	if self.max_hurt == 0 then
		return 0
	end
	return (value/self.max_hurt)
end

function ActivityWGData:GetHurtRankList()
	local list = {}
	if not self.zhuxie_boss.hurt_rank_list then
		return list
	end

	table.sort(self.zhuxie_boss.hurt_rank_list, SortTools.KeyUpperSorter("hurt"))

	for i,v in ipairs(self.zhuxie_boss.hurt_rank_list) do
		if v.role_name ~= "" then
			local temp_list = TableCopy(v)
			local item = self:GetRankRewardByBossID(i, self.zhuxie_info.boss_info.boss_id)
			temp_list.item = item
			table.insert(list,temp_list)
		end
	end

	return list
end

function ActivityWGData:GetRankRewardByBossID(rank_num, boss_id)
	local kill_hurt_rank_reward = ConfigManager.Instance:GetAutoConfig("zhuxieconfig_auto").kill_hurt_rank_reward

	for i,v in ipairs(kill_hurt_rank_reward) do
		if v.rank == rank_num and boss_id == v.boss_id then
			return v.item
		end
	end
	return {}
end

function ActivityWGData:GetHurtRankInfoByName(role_name)
	local rank_list = self:GetHurtRankList()
	if IsEmptyTable(rank_list) then return end
	for i,v in ipairs(rank_list) do
		if v.role_name == role_name then
			return i,v
		end
	end
end

function ActivityWGData:GetZhuxieBossHP()
	return self.zhuxie_boss
end

function ActivityWGData:GetOneZhuXieTaskInfo(task_id)
	local task_list = self:GetZhuXieTaskList()
	for k,v in pairs(self.zhuxie_info.taskinfo_list) do
		if v.task_id == task_id then
			for m,n in pairs(task_list) do
				if v.task_id == n.cfg.task_id and n.cfg.param_max_value ~= nil then
					v["max_value"] = n.cfg.param_max_value
				end
			end
			return v
		end
	end
	return nil
end

function ActivityWGData:GetZhuXieTaskInfoByid(task_id)
	local task_list = self:GetZhuXieTaskList()
	for k,v in pairs(task_list) do
		if v.cfg.task_id == task_id then
			return v
		end
	end
end

function ActivityWGData:GetOneZhuXieBossInfo()
	return self.zhuxie_info.boss_info
end

function ActivityWGData:GetZhuXieComptltedTaskNum()
	local task_list = self:GetZhuXieTaskList()
	local num = 0
	for k,v in pairs(task_list) do
		if v.is_fetch_reward == 1 then
			num = num + 1
		end
	end
	return num, #task_list
end


------------------ 钟馗抓鬼 ------------------
-- 设置当天捉鬼信息
function ActivityWGData:SetCurDayZhuaGuiInfo(info)
	self.zhongkuizhuagui_info.zhuagui_day_gethuoli = info.zhuagui_day_gethuoli
	self.zhongkuizhuagui_info.zhuagui_day_catch_count = info.zhuagui_day_catch_count
end

-- 获取当天捉鬼信息
function ActivityWGData:GetCurDayZhuaGuiInfo()
	return self.zhongkuizhuagui_info
end

-- 设置抓鬼副本信息
function ActivityWGData:SetZhuaGuiFBInfo(info)
	self.zhongkuizhuagui_fb_info.reason = info.reason
	self.zhongkuizhuagui_fb_info.monster_count = info.monster_count
	self.zhongkuizhuagui_fb_info.ishave_boss = info.ishave_boss
	self.zhongkuizhuagui_fb_info.boss_isdead = info.boss_isdead
	self.zhongkuizhuagui_fb_info.zhuagui_info_list = info.zhuagui_info_list
end

-- 获取抓鬼副本信息
function ActivityWGData:GetZhuaGuiFBInfo()
	return self.zhongkuizhuagui_fb_info
end

-- 获取本次副本自己的活力
function ActivityWGData:GetCurFBSelfHuoLi()
	local self_huoli = 0
	if nil ~= self.zhongkuizhuagui_fb_info.zhuagui_info_list then
		for k,v in pairs(self.zhongkuizhuagui_fb_info.zhuagui_info_list) do
			if v.uid == GameVoManager.Instance:GetMainRoleVo().role_id then
				self_huoli = v.get_huoli
			end
		end
	end

	return self_huoli
end

-- 获取是否出现特殊鬼
function ActivityWGData:GetIsHasSpecialMonster()
	return 1 == self.zhongkuizhuagui_fb_info.ishave_boss
end

-- 诛邪战场
function ActivityWGData:GetZhuxieBossReward(index)
	local item_cfg = ConfigManager.Instance:GetAutoConfig("zhuxieconfig_auto").show_reward
	return 	item_cfg[index]
end

function ActivityWGData:GetZhuxieTaskReward(task_id)
	local item_cfg = ConfigManager.Instance:GetAutoConfig("zhuxieconfig_auto").task_reward
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local reward = {}
	for k, v in pairs(item_cfg) do
		if v.task_id == task_id and v.role_level == role_level then
			reward = v
			break
		end
	end
	return reward
end

function ActivityWGData:GetLevelRank(type,list)
	if IsEmptyTable(list) then
		return
	end

	local len_num = #list
	if type == 1 and len_num >= 10 then
		self.level = list[10].level
	elseif type == 1 and len_num <10 then
		self.level = list[len_num].level
	end
end

function ActivityWGData:GetBossInfo(index)
	local boss_cfg = ConfigManager.Instance:GetAutoConfig("zhuxieconfig_auto").show_reward
	for i = 1,#boss_cfg do
		if boss_cfg[i].world_max_level >= self.level and boss_cfg[i].world_min_level <= self.level then
			return boss_cfg[i].item[index - 1]
		elseif boss_cfg[i].world_max_level <self.level then
			return boss_cfg[#boss_cfg].item[index - 1]
		elseif boss_cfg[i].world_min_level > self.level then
			return boss_cfg[1].item[index - 1]
		end
	end
end

--获取诛邪战场boss掉落配置
function ActivityWGData:GetZhuXieBossCfg()
	local boss_cfg = ConfigManager.Instance:GetAutoConfig("zhuxieconfig_auto").show_reward
	for i = 1,#boss_cfg do
		if boss_cfg[i].world_max_level >= self.level and boss_cfg[i].world_min_level <= self.level then
			return boss_cfg[i]
		elseif boss_cfg[i].world_max_level <self.level then
			return boss_cfg[#boss_cfg]
		elseif boss_cfg[i].world_min_level > self.level then
			return boss_cfg[1]
		end
	end
end

function ActivityWGData:GetBossHeadIcon(boss_id)
	local head_cfg = ConfigManager.Instance:GetAutoConfig("zhuxieconfig_auto").head_cfg
	for k,v in pairs(head_cfg) do
		if boss_id == v.boss_id then
			return v.boss_head
		end
	end
end

function ActivityWGData:GetZhuXieBossRefreshTime()
	if not self.zhuxie_boss_time then
		local other_cfg = ConfigManager.Instance:GetAutoConfig("zhuxieconfig_auto").other[1]
		self.zhuxie_boss_time = other_cfg.refresh_boss_time_gap
	end
	return self.zhuxie_boss_time or 1
end

---------------------------------------------------跨服诛邪战场--------------------------------------------------------------
-- self.kf_zhuxie_info
-- self.kf_zhuxie_boss_info
function ActivityWGData:SetKuafuZhuXieProtocol(protocol)
	self.kf_zhuxie_info.boss_id = protocol.boss_id or 0
	self.kf_zhuxie_info.next_boss_refresh_time = protocol.next_boss_refresh_time
	self.kf_zhuxie_info.boss_cur_hp = protocol.boss_cur_hp or 0
	self.kf_zhuxie_info.boss_max_hp = protocol.boss_max_hp or 0
	self.kf_zhuxie_info.task_list = protocol.task_list
	self.kf_zhuxie_info.double_task_id_1 = protocol.double_task_id_1 or 0
	self.kf_zhuxie_info.double_task_id_2 = protocol.double_task_id_2 or 0
	self.kf_zhuxie_info.double_task_end_time = protocol.double_task_end_time or 0

	self.kf_zhuxie_boss_info.next_boss_refresh_time = protocol.next_boss_refresh_time
end

function ActivityWGData:SetKuafuZhuXieBossProtocol(protocol)
	self.max_hurt = 0
	self.kf_zhuxie_boss_info.boss_id = protocol.boss_id or 0
	self.kf_zhuxie_boss_info.boss_cur_hp = protocol.boss_cur_hp or 0
	self.kf_zhuxie_boss_info.boss_max_hp = protocol.boss_max_hp or 0
	self.kf_zhuxie_boss_info.rank_item_list = protocol.rank_item_list
	for i,v in ipairs(protocol.rank_item_list) do
		self.max_hurt = math.max(self.max_hurt,v.hurt) 
	end
	
end

function ActivityWGData:GetKFZhuXieTaskCfg()
	local task_cfg = ConfigManager.Instance:GetAutoConfig("cross_zhuxie_auto").task
	return task_cfg
end

function ActivityWGData:GetKFZhuXieTaskInfo()
	return self.kf_zhuxie_info
end

function ActivityWGData:GetKFZhuXieBossInfo()
	return self.kf_zhuxie_boss_info
end

function ActivityWGData:SetCurTaskid(task_id)

	self.kf_zhuxie_cur_task_id = task_id
end

function ActivityWGData:GetCurTaskid()
	return self.kf_zhuxie_cur_task_id
end

function ActivityWGData:GetKFZhuXieTaskList()
	local task_data_list = self.kf_zhuxie_info.task_list or {}

	local task_cfg = self:GetKFZhuXieTaskCfg()
	local data_list = {}
	for i=1,#task_cfg do
		--if task_data_list and task_data_list[i] and task_data_list[i].task_id == task_cfg[i].task_id then
			-- data_list[i] = TableCopy(task_cfg[i])
			local info_task = {}
			if task_data_list[i] and task_data_list[i].task_id then
				info_task = task_data_list[i].task_id == task_cfg[i].task_id and task_data_list[i] or {}
			end
			data_list[i] = {}
			data_list[i].cfg = task_cfg[i]
			data_list[i].param_value = info_task.num or 0
			data_list[i].is_fetched = info_task.is_fetched or 0
			local double_task_id_1 = self.kf_zhuxie_info.double_task_id_1 or 0
			local double_task_id_2 = self.kf_zhuxie_info.double_task_id_2 or 0
			if info_task.task_id == double_task_id_1 or
				info_task.task_id == double_task_id_2 then
				data_list[i].is_double = 1
				data_list[i].double_task_end_time = self.kf_zhuxie_info.double_task_end_time or 0
			else
				data_list[i].is_double = 0
				data_list[i].double_task_end_time = 0
			end
		--end
	end
	data_list = self:SortKFZhuXieTaskList(data_list)
	return data_list
end

function ActivityWGData:GetKFZhuXieTaskInfoByTaskId(task_id)
	local data_list = self:GetKFZhuXieTaskList()
	for k,v in ipairs(data_list) do
		if v.cfg.task_id == task_id then
			return v
		end
	end
end

function ActivityWGData:SortKFZhuXieTaskList(task_list)
	if not self.kf_zhuxie_old_task_list then
		for i=1,#task_list do
			if task_list[i].is_fetched == 1 then
				task_list[i].sort_index = 1
			elseif task_list[i].is_double == 1 then
				task_list[i].sort_index = 5
			else
				task_list[i].sort_index = 3
			end
		end
	else
		for i=1,#task_list do
			if task_list[i].is_fetched == 1 then
				task_list[i].sort_index = 1
			elseif task_list[i].is_double == 1 then
				task_list[i].sort_index = 5
			else
				task_list[i].sort_index = 3
			end
			if self.kf_zhuxie_old_task_list[i].param_value < task_list[i].param_value and task_list[i].is_fetched ~= 1 then
				task_list[i].sort_index = 8
			end
		end
	end
	self.kf_zhuxie_old_task_list = task_list
	table.sort(task_list, SortTools.KeyUpperSorter("sort_index"))
	return task_list

end

--诛邪战场先关信息处理
--获取一个未完成的task_id
function ActivityWGData:GetKFZhuXieANotCompleteTaskID()
	local task_list = self:GetKFZhuXieTaskList()
	for i,v in ipairs(task_list) do
		if v.is_fetched == 0 and v.cfg.task_type ~= ZhuXieFollow.Task_Type.Role then
			return v.cfg.task_id
		end
	end
end

function ActivityWGData:GetKfZhuXieCompleteTaskNum()
	local task_list = self.kf_zhuxie_info.task_list
	if not task_list then
		local task_cfg = self:GetKFZhuXieTaskCfg()
		return 0, #task_cfg
	end
	local complete_num = 0
	for i,v in ipairs(task_list) do
		if v.is_fetched == 1 then
			complete_num = complete_num + 1
		end
	end
	return complete_num, #task_list
end

function ActivityWGData:GetKFZhuXieShowRewardCfg()
	local show_rewrad_cfg = ConfigManager.Instance:GetAutoConfig("cross_zhuxie_auto").show_reward
	local world_level = RankWGData.Instance:GetWordLevel()
	for i = 1,#show_rewrad_cfg do
		if show_rewrad_cfg[i].world_max_level >= world_level and show_rewrad_cfg[i].world_min_level <= world_level then
			return show_rewrad_cfg[i]
		elseif show_rewrad_cfg[i].world_max_level <world_level then
			return show_rewrad_cfg[#show_rewrad_cfg]
		elseif show_rewrad_cfg[i].world_min_level > world_level then
			return show_rewrad_cfg[1]
		end
	end
end

function ActivityWGData:GetKFZhuXieHurtRankList()
	local list = {}
	if self.kf_zhuxie_boss_info and self.kf_zhuxie_boss_info.rank_item_list then
		table.sort(self.kf_zhuxie_boss_info.rank_item_list, SortTools.KeyUpperSorter("hurt"))

		for i,v in ipairs(self.kf_zhuxie_boss_info.rank_item_list) do
			if v.name ~= "" then
				local temp_list = TableCopy(v)
				local item = self:GetKFRankRewardByBossID(i, self.kf_zhuxie_info.boss_id)
				temp_list.item = item
				table.insert(list,temp_list)
			end
		end
	end
	return list
end

function ActivityWGData:GetKFRankRewardByBossID(rank_num, boss_id)
	local kill_hurt_rank_reward = ConfigManager.Instance:GetAutoConfig("cross_zhuxie_auto").kill_hurt_rank_reward

	for i,v in ipairs(kill_hurt_rank_reward) do
		if v.rank == rank_num and boss_id == v.boss_id then
			return v.item
		end
	end
	return {}
end

function ActivityWGData:GetKFZhuXieHurtRankInfoByName(role_name)
	local rank_list = self:GetKFZhuXieHurtRankList()
	if IsEmptyTable(rank_list) then return end
	for i,v in ipairs(rank_list) do
		if v.name == role_name then
			return i,v
		end
	end
end

function ActivityWGData:GetKFZhuxieTaskReward(task_id)
	local item_cfg = ConfigManager.Instance:GetAutoConfig("cross_zhuxie_auto").task_reward
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local reward = {}
	for k, v in pairs(item_cfg) do
		if v.task_id == task_id and v.role_level == role_level then
			reward = v
			break
		end
	end
	return reward
end

function ActivityWGData:GetKFZhuXieInfoByid(task_id)
	local task_list = self:GetKFZhuXieTaskList()
	for i,v in pairs(task_list) do
		if v.cfg.task_id == task_id then
			return v
		end
	end
end

function ActivityWGData:GetKFZhuXieBossIconByid(boss_id)
	local head_cfg = ConfigManager.Instance:GetAutoConfig("cross_zhuxie_auto").head_cfg
	for i,v in pairs(head_cfg) do
		if boss_id == v.boss_id then
			return v.boss_head
		end
	end
end

function ActivityWGData:GetFBMltiKillTitle(mul_kill_num)
	local title_cfg = ConfigManager.Instance:GetAutoConfig("cross_zhuxie_auto").multi_kill_title

	local info = {}
	mul_kill_num = tonumber(mul_kill_num)
	for i, v in ipairs(title_cfg) do
		if mul_kill_num >= v.kill_num then
			info = v
		else
			break
		end
	end
	return info
end

function ActivityWGData:GetKFZhuXieBossRefreshTime()
	if not self.zhuxie_boss_time then
		local other_cfg = ConfigManager.Instance:GetAutoConfig("cross_zhuxie_auto").other[1]
		self.zhuxie_boss_time = other_cfg.refresh_boss_time_gap
	end
	return self.zhuxie_boss_time or 1
end

--获取诛邪奖励预览
function ActivityWGData:GetZhuXieRewardShow(boss_id)
	local zhuxie_reward_show = self.zhuxie_reward_show[boss_id]
	local reward_show = {}
	local num = 1
	if zhuxie_reward_show and not IsEmptyTable(zhuxie_reward_show) then
		for k,v in pairs(zhuxie_reward_show) do
			local cfg = self.zhuxie_reward[v.mingci_1]
			if cfg then
				reward_show[num] = {}
				reward_show[num].reward = cfg.item
				reward_show[num].mingci_1 = v.mingci_1
				reward_show[num].mingci_2 = v.mingci_2
				num = num + 1
			end
		end
	end

	if not IsEmptyTable(reward_show) then
		table.sort(reward_show, SortTools.KeyLowerSorter("mingci_1"))
	end

	return reward_show
end

--获跨服诛邪奖励预览
function ActivityWGData:GetKfZhuXieRewardShow(boss_id)
	local zhuxie_reward_show = self.kf_zhuxie_reward_show[boss_id]
	local reward_show = {}
	local num = 1
	if zhuxie_reward_show and not IsEmptyTable(zhuxie_reward_show) then
		for k,v in pairs(zhuxie_reward_show) do
			local cfg = self.kf_zhuxie_reward[v.mingci_1]
			if cfg then
				reward_show[num] = {}
				reward_show[num].reward = cfg.item
				reward_show[num].mingci_1 = v.mingci_1
				reward_show[num].mingci_2 = v.mingci_2
				num = num + 1
			end
		end
	end

	if not IsEmptyTable(reward_show) then
		table.sort(reward_show, SortTools.KeyLowerSorter("mingci_1"))
	end

	return reward_show
end
