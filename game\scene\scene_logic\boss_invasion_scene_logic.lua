-- BOSS入侵
BOSSInvasionLogic = BOSSInvasionLogic or BaseClass(CommonFbLogic)

function BOSSInvasionLogic:__delete()
	self:DestoryAnswerAreaEffect()
end

function BOSSInvasionLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		-- MainuiWGCtrl.Instance:SetFBNameState(true, Language.BOSSInvasion.BOSSInvasionFBName)
		MainuiWGCtrl.Instance:SetFBNameState(false)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)

		BOSSInvasionWGCtrl.Instance:OpenSceneView()
		-- self:CheckEnterSHowPrivilegeFace()
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)

		local coin_guwu_times, gold_guwu_times = BOSSInvasionWGData.Instance:GetCurGuwuTime()
		local t = {}
		t.scene_type = SceneType.BOSS_INVASION
		t.coin_guwu_count = coin_guwu_times
		t.gold_guwu_count = gold_guwu_times
		GlobalEventSystem:Fire(OtherEventType.FB_GUWU_CHANGE, t)

		local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_BOSS_INVASION)

		if activity_info.status == ACTIVITY_STATUS.STANDY or activity_info.status == ACTIVITY_STATUS.OPEN then
			local server_time = TimeWGCtrl.Instance:GetServerTime()
			if activity_info and activity_info.end_time and activity_info.end_time > server_time then
				MainuiWGCtrl.Instance:SetFbIconEndCountDown(activity_info.end_time)
			end
		end
	end)

	local status = BOSSInvasionWGData.Instance:GetCurActStatus()
	if status == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS then
		BOSSInvasionWGCtrl.Instance:PlaySceneBgBGM()
	end

    local view = BOSSInvasionWGCtrl.Instance:GetSceneView()
	ViewManager.Instance:AddMainUIFuPingChangeList(view)
	ViewManager.Instance:AddMainUIRightTopChangeList(view)

	self:CreateAnswerAreaEffect()
	self:UpdataMainRoleBossInvasionHuDun()

	-- MainuiWGCtrl.Instance:SetMianUITargetPos(0, -50)
	-- FuBenWGCtrl.Instance:SetLevaeFbContent(Language.BOSSInvasion.BOSSInvasionOutFbTips)
end

function BOSSInvasionLogic:CheckEnterSHowPrivilegeFace()
	if not BOSSInvasionWGData.Instance:IsGetPrivilege() then
		local role_id = RoleWGData.Instance:InCrossGetOriginUid()
		local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	
		if PlayerPrefsUtil.GetInt("buy_privilege_tip_flag" .. role_id .. cur_day, 0) == 0 then
			BOSSInvasionWGCtrl.Instance:OpenPrivilegeView()
			PlayerPrefsUtil.SetInt("buy_privilege_tip_flag" .. role_id .. cur_day, 1)
		end
	end
end

function BOSSInvasionLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)

	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		main_role:DestroyBossInvasionHuDun()
	end

    BOSSInvasionWGCtrl.Instance:CloseSceneView()
    MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	-- MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetOtherContents(false)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)

	local view = BOSSInvasionWGCtrl.Instance:GetSceneView()
	ViewManager.Instance:RemoveMainUIFuPingChangeList(view)
	ViewManager.Instance:RemoveMainUIRightTopChangeList(view)

	self:DestoryAnswerAreaEffect()

	-- MainuiWGCtrl.Instance:SetMianUITargetPos(0, 0)

	-- FuBenWGCtrl.Instance:SetLevaeFbContent(Language.Dungeon.ConfirmLevelFB)
end

function BOSSInvasionLogic:ShowFindLately(gather_obj_id)
	local is_gather_flag = BOSSInvasionWGData.Instance:IsGatherFlag()
	if is_gather_flag then
		return false
	end

	local can_get_end_reward = BOSSInvasionWGData.Instance:CanGetBossEndReward()
	if not can_get_end_reward then
		return false
	end

	local gather_time_enough = BOSSInvasionWGData.Instance:GetCanGatherFlag()
	if not gather_time_enough then
		return false
	end

	return true
end

function BOSSInvasionLogic:SetAnswerAreaEffectState(state)
	if self.answer_a_effect and self.answer_a_effect.activeSelf ~= state then
		self.answer_a_effect:SetActive(state)
	end
	
	if self.answer_b_effect and self.answer_b_effect.activeSelf ~= state then
		self.answer_b_effect:SetActive(state)
	end
end

function BOSSInvasionLogic:CreateAnswerAreaEffect()
	local function CreateSingleAnswerAreaEffect(new_obj, name, pos_list, effect_name)
		local obj = ResMgr:Instantiate(new_obj)
		obj.name = name
		obj.transform:SetParent(G_SceneObjLayer)
		local pos_list = string.split(pos_list,",")
		local pos_x, pos_y, pos_z = tonumber(pos_list[1]), tonumber(pos_list[2]), tonumber(pos_list[3])
		obj.transform.localPosition = Vector3(pos_x, pos_y, pos_z)
		local bundle, asset = ResPath.GetEnvironmentCommonEffect(effect_name)
		local attach = obj:GetComponent(typeof(Game.GameObjectAttach))
		attach.BundleName = bundle
		attach.AssetName = asset
		local status = BOSSInvasionWGData.Instance:GetCurActStatus()
		obj:SetActive(status == CROSS_BOSS_STRIKE_STATUS.STATUS_QUESTION or status == CROSS_BOSS_STRIKE_STATUS.STATUS_QUESTION_SHOW)

		return obj
	end

	if not self.answer_a_effect or not self.answer_b_effect then
		local res_async_loader = AllocResAsyncLoader(self, "boss_invasion_answer_effect_load" )
		if res_async_loader then
			res_async_loader:Load("uis/view/boss_invasion_ui_prefab", "answer_area_point_effect", nil,
			function(new_obj)
				local cur_boss_cfg = BOSSInvasionWGData.Instance:GetCurBossCfg()

				if not self.answer_a_effect then
					self.answer_a_effect = CreateSingleAnswerAreaEffect(new_obj, "boss_invasion_answer_a_area_efffect", cur_boss_cfg.answer1_effect_pos, "BOSS_laixi_quan_lan")
				end

				if not self.answer_b_effect then
					self.answer_b_effect = CreateSingleAnswerAreaEffect(new_obj, "boss_invasion_answer_b_area_efffect", cur_boss_cfg.answer2_effect_pos, "BOSS_laixi_quan_jin")
				end
			end)
		end
	end
end

function BOSSInvasionLogic:DestoryAnswerAreaEffect()
	if self.answer_a_effect then
		self.answer_a_effect:Destroy()
		self.answer_a_effect = nil
	end
	
	if self.answer_b_effect then
		self.answer_b_effect:Destroy()
		self.answer_b_effect = nil
	end
end

function BOSSInvasionLogic:UpdataMainRoleBossInvasionHuDun()
	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		local privilege = BOSSInvasionWGData.Instance:IsGetPrivilege()
		if privilege then
			local status = BOSSInvasionWGData.Instance:GetCurActStatus()
			main_role:SetBossInvasionHuDunVisiable(status == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS)
		end
	end
end