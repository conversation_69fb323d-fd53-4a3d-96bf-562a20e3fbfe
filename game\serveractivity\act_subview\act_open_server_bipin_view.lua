local act_is_over = false    -- 活动是否结束
local act_is_jiesuan = false -- 活动是否结算中

function KfActivityView:BiPinLoadCallBack()
	self.bipin_select_index = 1
	self.kf_activity_enter = true
	self.node_list.binpin_mianui_show_rank.toggle.isOn = ServerActivityWGData.Instance:GetMainUiIsShowBiPinRank()
	self.bipin_rank_list = AsyncListView.New(OpenServerRankItem, self.node_list.ph_rankitem_role)

	if not self.bipin_rank_item_list then
		self.bipin_rank_item_list = {}
		for i = 1, 3 do
			self.bipin_rank_item_list[i] = OpenServerRankItemRender.New(self.node_list["bipin_rank_item_" .. i])
		end
	end

	if not self.ph_buy_act_list then
		self.ph_buy_act_list = AsyncListView.New(OpenServerRankActItem, self.node_list.ph_buy_act_list)
		self.ph_buy_act_list:SetSelectCallBack(BindTool.Bind(self.OnSelectClickActItem, self))
	end

	self.reward_item_list = AsyncListView.New(OpenServerRankRewardItem, self.node_list.bipin_rank_reward_item_list)
	self.rank_reward_show_list = AsyncListView.New(ItemCell, self.node_list.rank_reward_show_list)
	self.rank_reward_show_list:SetStartZeroIndex(true)
	self.node_list["toggle_bp_rank_reward"].toggle:AddClickListener(BindTool.Bind(self.OnClickTaskToggle, self, 1)) --排行榜toggle
	self.node_list["toggle_bp_rank_target"].toggle:AddClickListener(BindTool.Bind(self.OnClickTaskToggle, self, 2)) --奖励信息toggle
	self.node_list.binpin_mianui_show_rank.toggle:AddValueChangedListener(BindTool.Bind(
	self.BiPinOnClickMainUiShowRankToggle, self))
	self.node_list.help_rank_btn.button:AddClickListener(BindTool.Bind(self.OpenHelpRankview, self))
	self.node_list.suit_show_btn.button:AddClickListener(BindTool.Bind(self.PlayBPCG, self))
	--self.node_list.rank_reward_btn.button:AddClickListener(BindTool.Bind(self.OpenRankRewardview, self))
	self.node_list.binpin_show_rank_btn.button:AddClickListener(BindTool.Bind(self.BiPinOnClickShowRankBtn, self))
	self.node_list.binpin_show_reward_btn.button:AddClickListener(BindTool.Bind(self.BiPinOnClickShowRewardBtn, self))
	self.node_list.suit_attr_btn.button:AddClickListener(BindTool.Bind(self.OnClickGoAttr, self))
	self.bipin_toggle_type = 1
	self.jump_to_index = false
	self:BiPinInitTopBtnList()
	self:SetBiPinTopBtnData()
	self.bipin_box_list = {}
	self.bipin_box_red = {}
	self.bipin_box_txt = {}
	self.bipin_box_cell = {}
	self.bipin_box_ylq = {}
	self.bipin_box_btn = {}
	self.bipin_box_ylqbg = {}
	for i = 1, 7 do
		self.bipin_box_list[i] = self.node_list.total_reward_list_root:FindObj("box_" .. i)
		self.bipin_box_red[i] = self.bipin_box_list[i]:FindObj("remind")
		self.bipin_box_txt[i] = self.bipin_box_list[i]:FindObj("txt")
		self.bipin_box_cell[i] = ItemCell.New(self.bipin_box_list[i]:FindObj("cell"))
		self.bipin_box_ylq[i] = self.bipin_box_list[i]:FindObj("ylq_img")
		self.bipin_box_btn[i] = self.bipin_box_list[i]:FindObj("btn")
		self.bipin_box_ylqbg[i] = self.bipin_box_list[i]:FindObj("ylq_bg")
		self.bipin_box_list[i]:SetActive(false)
		XUI.AddClickEventListener(self.bipin_box_btn[i], BindTool.Bind2(self.OnClickGetBPRewardBtn, self, i))
	end

	if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list.kf_server_model)
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	if not self.soul_ring_model then
		self.soul_ring_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["soul_ring_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = false,
		}

		self.soul_ring_model:SetRenderTexUI3DModel(display_data)

		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local special_status_table = {ignore_wing = true, ignore_halo = true, ignore_fazhen = true, ignore_mantle = true, ignore_tail = true, ignore_jianzhen = true}
		self.soul_ring_model:SetModelResInfo(role_vo, special_status_table)
    end

	--self:OpenBPSuitShowView()
	XUI.AddClickEventListener(self.node_list.go_btn, BindTool.Bind(self.OnClickGoBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_rule_tips, BindTool.Bind(self.OnClickBtnTips, self))
	-- XUI.AddClickEventListener(self.node_list.btn_op_da, BindTool.Bind(self.OnClickOGADailyReward, self)) -- 前往活动
end

function KfActivityView:OnClickGoBtn()
	local cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(self:BiPinGetSelectRushType())
	if cfg and cfg.turn_link ~= "" then
		FunOpen.Instance:OpenViewNameByCfg(cfg.turn_link)
		return
	end
end

function KfActivityView:OnClickBtnTips()
    RuleTip.Instance:SetContent(Language.Rank.RuleContent, Language.Rank.RuleTitle)
end

function KfActivityView:OnClickGetBPRewardBtn(index)
	local data = self.data_list[index]
	if data then
		local goal_reward_flag = ServerActivityWGData.Instance:GetOpenServerGoalFlags(data.rush_type, data.seq)
		local goal_reward_fetch = ServerActivityWGData.Instance:GetOpenServerGoalFetchFlags(data.rush_type, data.seq)
		local can_reward = goal_reward_flag == 1 and goal_reward_fetch == 0
		local seq = data.seq
		if ServerActivityWGData.Instance:IsSpecialRank(data.rush_type) then
			seq = data.seq + 1
			if not can_reward then
				local cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(data.rush_type)
				if cfg and cfg.turn_link ~= "" then
					FunOpen.Instance:OpenViewNameByCfg(cfg.turn_link)
					return
				end
			end
		end

		ServerActivityWGCtrl.Instance:SendCSOGARushRankOp(OGA_RUSH_RANK_OP_TYPE.RUSH_RANK_REWARD_TYPE_DAILY_FETCH,
			data.rush_type, seq)
	end
end

function KfActivityView:OnClickTaskToggle(type, is_on)
	if is_on then
		self.bipin_toggle_type = type
		if type == 2 then
			local rush_type = self:BiPinGetSelectRushType()
			local opengame_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(rush_type)
			RankWGCtrl.Instance:SendActRankListReq(opengame_cfg.rank_type)
		else
			self:BiPinFlushDailyBuyPanel()
		end
	end
end

function KfActivityView:SetBinPinMainuiShowRankToggle(isOn)
	self.node_list.binpin_mianui_show_rank.toggle.isOn = isOn
end

function KfActivityView:BiPinReleaseCallBack()
	self.bipin_select_index = 1
	self.bp_rank_type = nil
	self.last_play_index = nil

	if self.ph_buy_act_list then
		self.ph_buy_act_list:DeleteMe()
		self.ph_buy_act_list = nil
	end

	if self.bipin_top_btn_list then
		self.bipin_top_btn_list:DeleteMe()
		self.bipin_top_btn_list = nil
	end

	if self.bipin_rank_list then
		self.bipin_rank_list:DeleteMe()
		self.bipin_rank_list = nil
	end

	if self.bipin_rank_item_list then
		for i, v in pairs(self.bipin_rank_item_list) do
			v:DeleteMe()
			v = nil
		end
		self.bipin_rank_item_list = nil
	end

	for k, v in pairs(self.bipin_box_cell) do
		v:DeleteMe()
	end

	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

	if self.soul_ring_model then
        self.soul_ring_model:DeleteMe()
        self.soul_ring_model = nil
    end

	if self.reward_item_list then
		self.reward_item_list:DeleteMe()
		self.reward_item_list = nil
	end

	if self.rank_reward_show_list then
		self.rank_reward_show_list:DeleteMe()
		self.rank_reward_show_list = nil
	end

	self.bipin_box_cell = nil

	CountDownManager.Instance:RemoveCountDown("bipin_end_time")
end

function KfActivityView:BiPinShowCallBack()
	ServerActivityWGCtrl.Instance:SendCSOGARushRankOp(OGA_RUSH_RANK_OP_TYPE.RUSH_RANK_REWARD_TYPE_GOAL_FETCH)
	self.jump_to_index = true
	-- self:BiPinSelectTopBtn()
	-- self:DoBiPinPanelAnim()
end

function KfActivityView:BiPinFlushRinkInfo(data)
	if self.bp_rank_type and self.bp_rank_type ~= data.rank_type then
		return
	end

	-- 排行榜
	if self.bipin_rank_list then
		local data_list = {}
		for i = 1, 100 do
			data_list[i] = data.rank_list[i]
			if data_list[i] then
				data_list[i].real_rank = i
			else
				data_list[i] = { real_rank = i, rank_value = 0 }
			end
		end
		self.bipin_rank_list:SetDataList(data_list)
		self:BiPinFlushMyRank(data)
	end

	if self.bipin_rank_item_list then
		for i = 1, 3 do
			self.bipin_rank_item_list[i]:SetData(data.rank_list[i])
		end
	end

	-- 奖励信息
	local rush_type = self:BiPinGetSelectRushType()
	local cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(rush_type)
	local value_str = ""
	if cfg and cfg.ranking_title and cfg.ranking_name then
		if Language.Common.GradeText == cfg.ranking_title then -- 如果是阶数
			if data.self_value <= 0 then
				value_str = string.format(Language.Rank.Jie, math.floor(data.self_value / 10), 0)
			else
				local _, temp = math.modf(data.self_value / 10)
				local value = data.self_value % 10
				if value == 0 then
					value = 10
				end
				if temp > 0 then
					value_str = string.format(Language.Rank.Jie, math.floor(data.self_value / 10) + 1, value)
				else
					value_str = string.format(Language.Rank.Jie, math.floor(data.self_value / 10), value)
				end
			end
		else
			value_str = data.self_value
		end
		self.node_list.my_level.text.text = value_str
	end
end

function KfActivityView:BiPinFlushMyRank(protocol)
	local rush_type = self:BiPinGetSelectRushType()
	if protocol.self_rank > 0 then
		local rank_str = protocol.self_rank
		self.node_list.rich_my_rank.text.text = string.format(Language.OpenServer.MyRank, rank_str)
		self.node_list.bipin_rich_my_rank.text.text = string.format(Language.OpenServer.MyRank, rank_str)
	else
		local rush_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(rush_type)
		local reward_cfg_list = ServerActivityWGData.Instance:GetOpenServerActivityRewardConfig(rush_type)
		if reward_cfg_list and rush_cfg and rush_cfg.rank_condition ~= "" then
			local reward_cfg = reward_cfg_list[#reward_cfg_list]
			local rank_str = ""
			if ServerActivityWGData.Instance:IsSpecialRank(rush_type) then
				local body_show_type = rush_type == 3 and MOUNT_LINGCHONG_APPE_TYPE.MOUNT or MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG
				local cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(body_show_type, reward_cfg.reach_value) or
				{}
				rank_str = string.format(rush_cfg.rank_condition, cfg.grade_num or 0, cfg.star_num or 0)
			else
				rank_str = string.format(rush_cfg.rank_condition, reward_cfg.reach_value)
			end

			self.node_list.rich_my_rank.text.text = string.format(Language.OpenServer.RankCondition, rank_str)
		else
			self.node_list.rich_my_rank.text.text = string.format(Language.OpenServer.MyRank, Language.Rank.NoRank)
		end
		self.node_list.bipin_rich_my_rank.text.text = string.format(Language.OpenServer.MyRank, Language.Rank.NoRank)
	end

	local data = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(rush_type)
	local value_str = ""
	if data and data.ranking_title and data.ranking_name then
		if Language.Common.GradeText == data.ranking_title then -- 如果是阶数
			if protocol.self_value <= 0 then
				value_str = string.format(Language.Rank.Jie, math.floor(protocol.self_value / 10), 0)
			else
				local _, temp = math.modf(protocol.self_value / 10)
				local value = protocol.self_value % 10
				if value == 0 then
					value = 10
				end
				if temp > 0 then
					value_str = string.format(Language.Rank.Jie, math.floor(protocol.self_value / 10) + 1, value)
				else
					value_str = string.format(Language.Rank.Jie, math.floor(protocol.self_value / 10), value)
				end
			end
		else
			value_str = protocol.self_value
		end

		self.node_list.rich_my_level.text.text = string.format(Language.Rank.My, data.ranking_title, value_str)
		--self.node_list.title_view_name.text.text = data.ranking_name
		self.node_list.label_listname.text.text = data.ranking_title
	end
end

function KfActivityView:BiPinOnFlush(param_t)
	for k, v in pairs(param_t) do
		if k == "bipin" then
			self.bipin_top_btn_list:RefreshActiveCellViews()
			self.bipin_top_btn_list:JumpToIndex(self.bipin_select_index)
		elseif k == "rank" and v.protocol_id == 10055 then
			self:BiPinFlushMyRankInfo()
		elseif k == "daychange" then
			self.bipin_top_btn_list:RefreshActiveCellViews()
			self:BiPinSelectTopBtn()
		elseif k == "flush_pm" then
			self:BiPinFlushRinkInfo(v.protocol)
		elseif k == "jump_to" then
			self.bipin_top_btn_list:RefreshActiveCellViews()
			self:BiPinSelectTopBtn(v.rush_type)
		end

		local help_rank_red_flag = HelpRankWGData.Instance:HelpRankRedShow()
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local data_list = self.top_btn_data_list
		local select_index = self.bipin_select_index
		local is_close = open_day > data_list[select_index].cfg.close_day_index
		self.node_list.help_rank_red:SetActive(help_rank_red_flag == 1 and not is_close)
		local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE
		.RAND_ACTIVITY_TYPE_OA_HELP_RANK)
		self.node_list.help_rank_btn:SetActive(activity_info and activity_info.status == ACTIVITY_STATUS.OPEN)

		self.node_list.btn_rule_tips:SetActive(data_list[select_index].cfg.rush_type == 17)	--万魂幡
	end

	if self.kf_activity_enter then
		self.kf_activity_enter = false
		self.node_list.toggle_bp_rank_target.toggle.isOn = true
	end

	self:SetBiPinTopBtnData()
	if self.jump_to_index then
		self.jump_to_index = false
		self:BiPinSelectTopBtn()
	end

	local seq = ServerActivityWGData.Instance:GetBiPinSuitSeq()
	local suit_data = GuiXuDreamWGData.Instance:GetGuiXuDreamSuitInfoBySuit(seq)
	if IsEmptyTable(suit_data) then
		return
	end

	self.node_list.suit_red:SetActive(suit_data.can_act)
end

function KfActivityView:BiPinInitTopBtnList()
	if not self.bipin_top_btn_list then
		self.bipin_top_btn_list = AsyncListView.New(OpenServerBiPinTopBtnItem, self.node_list.binpin_select_list)
		self.bipin_top_btn_list:SetSelectCallBack(BindTool.Bind1(self.BiPinOnClickTopBtn, self))

		self.bipin_top_btn_list.SelectIndex = function(top_btn_list, index)
			if self:BiPinOpenTipsFlag(index) then
				AsyncListView.SelectIndex(top_btn_list, index)
			end
		end

		self.bipin_top_btn_list.JumpToIndex = function(top_btn_list, index)
			if self:BiPinOpenTipsFlag(index) then
				AsyncListView.JumpToIndex(top_btn_list, index)
			end
		end

		self.bipin_top_btn_list.ListEventCallback = function(top_btn_list, item_cell)
			if self:BiPinOpenTipsFlag(item_cell:GetIndex()) then
				AsyncListView.ListEventCallback(top_btn_list, item_cell)
			end
		end
	end
end

function KfActivityView:SetBiPinTopBtnData()
	local data_list = ServerActivityWGData.Instance:GetOpenGameActivityDataList()
	-- local open_act_cfg = ServerActivityWGData.Instance:GetOpenGameActivityConfig()
	-- if open_act_cfg and open_act_cfg.rush_rank_type then
	-- 	local data = nil
	-- 	for i = 1, COMMON_CONSTS.OpenServerBiPinNum do
	-- 		data = open_act_cfg.rush_rank_type[i]
	-- 		if data and data.close_day_index > 0 then
	-- 			data_list[#data_list + 1] = data
	-- 		end
	-- 	end
	-- end
	-- table.sort(data_list, function(a, b)
	-- 	return a.close_day_index < b.close_day_index
	-- end)
	self.top_btn_data_list = data_list
	self.bipin_top_btn_list:SetDataList(data_list)

	self:FlushBuyTabPart()
end

function KfActivityView:FlushBuyTabPart()
	local rush_type = self:BiPinGetSelectRushType()
	local tb_temp = {}
	local tb_tab_enum = Language.OpenServer.ActTabEnum
	local tb_index = {
		TabIndex.act_bipin_normal,
		-- TabIndex.act_bipin_purchase,
		-- TabIndex.act_daily_recharge,
		-- TabIndex.act_equip_cloud_buy,
		-- TabIndex.act_equip_lottery, 
	}
	for key, value in pairs(tb_index) do
		table.insert(tb_temp, { tab_index = value, tab_name = tb_tab_enum[value], rush_type = rush_type})
	end

	if not IsEmptyTable(tb_temp) then
		table.sort(tb_temp, SortTools.KeyLowerSorter("tab_index"))
	end

	self.node_list.oga_top_bg:CustomSetActive(#tb_temp > 1)
	self.ph_buy_act_list:SetDataList(tb_temp)
end

function KfActivityView:OnSelectClickActItem(cell)
	if not cell then return end
	local data = cell:GetData()

	if data.tab_index then
		self:ChangeToIndex(data.tab_index)
	end
end

function KfActivityView:BiPinSelectTopBtn(rush_type)
	if self.bipin_top_btn_list and self.top_btn_data_list then
		if rush_type then
			self.bipin_select_index = self:GetIndexByRushType(rush_type)
		else
			local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
			local can_get_reward = false
			local select_index = nil

			for i, data in ipairs(self.top_btn_data_list) do
				can_get_reward = ServerActivityWGData.Instance:OpenServerCompetitonSingleReward(data.cfg.rush_type)
				if open_day >= data.cfg.open_day_index and can_get_reward then
					select_index = i
					break
				end
			end

			if not select_index then
				for i, data in ipairs(self.top_btn_data_list) do
					if open_day >= data.cfg.open_day_index and open_day <= data.cfg.close_day_index then
						select_index = i
						break
					end
				end
			end

			self.bipin_select_index = select_index or 1
		end

		self.bipin_top_btn_list:JumpToIndex(self.bipin_select_index)
	end
end

function KfActivityView:GetIndexByRushType(rush_type)
	local data_list = self.top_btn_data_list
	if data_list then
		for k, v in pairs(data_list) do
			if v.cfg.rush_type == rush_type then
				return k
			end
		end
	end
end

function KfActivityView:BiPinGetSelectRushType()
	local data_list = self.top_btn_data_list
	local select_index = self.bipin_select_index
	if data_list and select_index and data_list[select_index] then
		return data_list[select_index].cfg.rush_type
	end
	return 1
end

function KfActivityView:BiPinOpenTipsFlag(index)
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if open_day >= 7 then
		return true
	end
	local data_list = self.top_btn_data_list -- self.bipin_top_btn_list:GetDataList()
	local data = data_list and data_list[index].cfg
	if data and open_day < data.open_day_index then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.OpenServer.NextOpenSprintActName, data.open_day_index))
		return false
	end
	return true
end

function KfActivityView:BiPinOnClickShowRankBtn()
	local rush_type = self:BiPinGetSelectRushType()
	local opengame_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(rush_type)
	RankWGCtrl.Instance:OpenOpenServerRankView(rush_type, opengame_cfg.rank_type)
end

function KfActivityView:BiPinOnClickShowRewardBtn()
	local rush_type = self:BiPinGetSelectRushType()
	ServerActivityWGCtrl.Instance:OpenKFRewardTips(rush_type)
end

function KfActivityView:BiPinOnClickMainUiShowRankToggle(is_on)
	ServerActivityWGData.Instance:SetMainUiIsShowBiPinRank(is_on and 1 or 0)
end

function KfActivityView:OnClickGoAttr()
	local seq = ServerActivityWGData.Instance:GetBiPinSuitSeq()
	if not seq then
		return
	end

	GuiXuDreamWGCtrl.Instance:OpenGGuiXuDreamAttrView(seq, true)
end

function KfActivityView:OpenHelpRankview()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HELP_RANK)
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		self.node_list.help_rank_red:SetActive(false)
		ViewManager.Instance:Open(GuideModuleName.HelpRankView)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.HuoDongWeiKaiQi)
	end
end

-- function KfActivityView:OpenRankRewardview()
-- 	ViewManager.Instance:Open(GuideModuleName.KfRewardActivityView)
-- end

function KfActivityView:OpenBPSuitShowView()
	if not ServerActivityWGData.Instance:BiPinActIsFirstOpen() then
		local callback_func = function()
			ServerActivityWGData.Instance:BiPinActIsFirstOpen(true)
		end

		self:PlayBPCG(callback_func)
	end
end

function KfActivityView:PlayBPCG(callback_func)
	-- 播放CG
	local cg_bundle = "cg/a2_zx_xinshoucun_prefab"
	local cg_asset = "CG_Zuoqi_Battle001"
	CgManager.Instance:Play(UICg.New(cg_bundle, cg_asset), callback_func)
end

function KfActivityView:BiPinOnClickTopBtn(item, index)
	self.bipin_select_index = index
	local data = item.data.cfg
	if data and data.rank_type then
		self.bp_rank_type = data.rank_type
		RankWGCtrl.Instance:SendActRankListReq(data.rank_type)
	end

	if ServerActivityWGData.Instance:SetBinPinBindGiftIsRemind(data.rush_type) then
		item:FlushRemind()
	end

	ServerActivityWGData.Instance:BiPinGetSelectRushType(data.rush_type)
	self:BiPinFlushCountDownTime()
	self:BiPinFlushMyRankInfo()
	self:BiPinFlushRewardPanel()
	self:BiPinFlushDailyBuyPanel()
	self:ShowOGATopPanel()

	if self.ph_buy_act_list then
		self.ph_buy_act_list:JumpToIndex(1)
	end
end

function KfActivityView:BiPinFlushCountDownTime()
	CountDownManager.Instance:RemoveCountDown("bipin_end_time")
	local rush_type = self:BiPinGetSelectRushType()
	local opengame_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(rush_type)
	local close_day = opengame_cfg.close_day_index
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	local format_time = os.date("*t", now_time)
	local end_time = os.time({
		year = format_time.year,
		month = format_time.month,
		day = format_time.day + close_day - open_day + 1,
		hour = 0,
		min = 0,
		sec = 0
	})
	act_is_jiesuan = now_time < end_time and now_time > end_time - 3600
	act_is_over = now_time > end_time
	local count_down_time = end_time - 3600 -- 23:00结算
	if now_time < count_down_time then
		self:BiPinUpdateCountDown(now_time, count_down_time)
		CountDownManager.Instance:AddCountDown(
			"bipin_end_time",
			BindTool.Bind(self.BiPinUpdateCountDown, self),
			BindTool.Bind(self.BiPinCompleteCountDown, self),
			count_down_time,
			nil,
			1
		)
	end

	if act_is_jiesuan then
		self.node_list.binpin_count_down_desc.text.text = Language.OpenServer.BiPinJieSuan
		self.node_list.binpin_count_down_label.text.text = Language.OpenServer.BiPinMailReward
	elseif act_is_over then
		self.node_list.binpin_count_down_desc.text.text = Language.OpenServer.BiPinDaojishi
		self.node_list.binpin_count_down_label.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
	else
		self.node_list.binpin_count_down_desc.text.text = Language.OpenServer.BiPinDaojishi
	end
	self.node_list.binpin_count_down_label.text.color = act_is_over and Str2C3b(COLOR3B.C10) or Str2C3b(COLOR3B.A1)
end

function KfActivityView:BiPinUpdateCountDown(elapse_time, total_time)
	local str = TimeUtil.FormatSecondDHM8(total_time - elapse_time)
	self.node_list.binpin_count_down_label.text.text = str
end

function KfActivityView:BiPinCompleteCountDown()
	ServerActivityWGCtrl.Instance:SendCSOGARushRankOp(OGA_RUSH_RANK_OP_TYPE.RUSH_RANK_REWARD_TYPE_GOAL_FETCH)
end

function KfActivityView:BiPinFlushMyRankInfo()
	local rush_type = self:BiPinGetSelectRushType()
	local opengame_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(rush_type)
	-- if act_is_over then
	-- 	self.node_list.binpin_my_rank_label.text.text = Language.OpenServer.RollActivityEnd
	-- 	self.node_list.binpin_my_rank_attr_label.text.text = Language.OpenServer.RollActivityEnd
	-- end
	if opengame_cfg then
		local rank_data = RankWGData.Instance:GetActMyRank(opengame_cfg.rank_type)
		if rank_data and rank_data.my_rank > 0 then
			self.node_list.binpin_my_rank_label.text.text = rank_data.my_rank
		else
			self.node_list.binpin_my_rank_label.text.text = Language.OpenServer.NotRank
		end
		self.node_list.binpin_my_rank_attr_label.text.text = rank_data and rank_data.rank_value or 0
	end

	self.node_list.binpin_my_rank_attr_desc.text.text = string.format(Language.OpenServer.RankDesc,
		Language.OpenServer.LingQuLimit3[rush_type])
	self.node_list["binpin_specail_tip"]:SetActive(rush_type == 10) --战力提升榜
	self.node_list["binpin_specail_tip"].text.text = Language.OpenServer.BiPinSpecailTip[rush_type]
end

function KfActivityView:BiPinFlushRewardPanel()
	local rush_type = self:BiPinGetSelectRushType()
	-- 达标奖励
	local model_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfigByRankType(rush_type)
	local target_reward_data_list = ServerActivityWGData.Instance:GetRushLastRewardItemListCfg(rush_type)
	local target_value_list = ServerActivityWGData.Instance:GetRushReachGoalList(rush_type)
	local data_list = {}
	for i = 1, #target_reward_data_list do
		data_list[i] = { rush_type = rush_type, target_value = target_value_list[i - 1],
			reward_data = target_reward_data_list[i], seq = i - 1 }
	end
	local traget_remind = false
	local is_special_rank = ServerActivityWGData.Instance:IsSpecialRank(rush_type)
	local temp_list = {}

	for k, data in pairs(data_list) do
		if is_special_rank or not traget_remind then
			local goal_reward_flag = ServerActivityWGData.Instance:GetOpenServerGoalFlags(data.rush_type, data.seq)
			local goal_reward_fetch = ServerActivityWGData.Instance:GetOpenServerGoalFetchFlags(data.rush_type, data.seq)

			if is_special_rank then
				temp_list[goal_reward_fetch * 200 + k] = data
			end

			if not traget_remind then
				traget_remind = goal_reward_flag == 1 and goal_reward_fetch == 0
			end
		end
	end

	if is_special_rank then
		data_list = SortTableKey(temp_list)
	end

	self.data_list = data_list
	local num = 0
	for k, v in pairs(data_list) do
		local desc = ""
		if ServerActivityWGData.Instance:IsSpecialRank(v.rush_type) then
			local body_show_type = v.rush_type == 3 and MOUNT_LINGCHONG_APPE_TYPE.MOUNT or MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG
			local cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(body_show_type, v.target_value) or {}
			local rank_name = Language.OpenServer.LingQuLimit2[v.rush_type] or ""
			desc = string.format(Language.OpenServer.BiPinRewardDesc_3, cfg.grade_num or 0, cfg.star_num or 0)
			local my_desc = string.format(Language.OpenServer.My_Type, rank_name)
			self.node_list.my_type_name.text.text = my_desc
		else
			local rank_name = Language.OpenServer.LingQuLimit2[v.rush_type] or ""
			self.node_list.my_type_name.text.text = rank_name
			desc = v.target_value
		end

		local goal_reward_fetch = ServerActivityWGData.Instance:GetOpenServerGoalFetchFlags(v.rush_type, v.seq)
		local goal_reward_flag = ServerActivityWGData.Instance:GetOpenServerGoalFlags(v.rush_type, v.seq)
		local can_reward = goal_reward_flag == 1 and goal_reward_fetch == 0
		if self.bipin_box_list[k] then
			self.bipin_box_list[k]:SetActive(true)
			self.bipin_box_red[k]:SetActive(can_reward)
			self.bipin_box_txt[k].text.text = desc
			self.bipin_box_ylq[k]:SetActive(goal_reward_fetch == 1)
			self.bipin_box_ylqbg[k]:SetActive(goal_reward_fetch == 1)
			self.bipin_box_btn[k]:SetActive(can_reward)
			local reward_list = SortTableKey(v.reward_data)
			self.bipin_box_cell[k]:SetData(reward_list[1])
		end

		if goal_reward_flag == 1 then
			num = num + 1
		end
	end

	num = (num - 1) <= 0 and 0 or (num - 1)
	local slider_value = { [0] = 0, 0.33, 0.66, 1 }
	self.node_list["binpin_slider"].slider.value = slider_value[num]
	self.node_list.toggle_rank_reward_remind:SetActive(traget_remind)
	self.node_list.binpin_show_reward_btn_red:SetActive(traget_remind)
	self:FlushKFServerModel(model_cfg)
	local model_name = ItemWGData.Instance:GetItemName(model_cfg.show_item_id)
	self.node_list.lbl_model_name.text.text = model_name
end

function KfActivityView:BiPinFlushDailyBuyPanel()
	-- local rush_type = self:BiPinGetSelectRushType()
	-- local data_list = ServerActivityWGData.Instance:GetRushDailyBuyCfgByRushType(rush_type)
	-- local temp_list = {}
	-- local buy_times = 0
	-- -- 排序 限购绑玉 > 限购仙玉 > 价格高到低
	-- for i,data in ipairs(data_list) do
	-- 	if data_list[i].is_limit_time then -- 限购绑玉
	-- 		temp_list[10000 + i] = data
	-- 	else
	-- 		buy_times = ServerActivityWGData.Instance:GetCompetitionDailyBuyTimes(data.rush_type, data.index)
	-- 		if buy_times < data.daily_buy_times then
	-- 			temp_list[data.gold_type * 2000 - data.need_gold + i] = data
	-- 		else
	-- 			temp_list[i] = data
	-- 		end
	-- 	end
	-- end
	-- data_list = SortTableKey(temp_list, true)
	local rush_type = self:BiPinGetSelectRushType()
	if self.reward_item_list then
		local rank_reward_data_list = ServerActivityWGData.Instance:GetOpenServerActivityRewardConfig(rush_type)
		self.reward_item_list:SetDataList(rank_reward_data_list)
	end

	if self.rank_reward_show_list then
		local rank_reward_data_list = ServerActivityWGData.Instance:GetOpenServerActivityRewardConfig(rush_type)
		local rank_reward_show_list = rank_reward_data_list[1].reward_item
		self.rank_reward_show_list:SetDataList(rank_reward_show_list)
	end
	--self.bipin_daily_limit_buy_list:SetDataList(data_list)
end

function KfActivityView:DoBiPinPanelAnim()
	local tween_info = UITween_CONSTS.KfActivityView
	UITween.FakeHideShow(self.node_list["open_server_bipin_root"])
	UITween.FakeHideShow(self.node_list["binpin_get_way_tween_root"])
	UITween.FakeHideShow(self.node_list["binpin_show_rank_btn_tween_root"]) --查看排行按钮
	RectTransform.SetAnchoredPositionXY(self.node_list["binpin_get_way_panel"].rect, 420, 0)
	RectTransform.SetAnchoredPositionXY(self.node_list["binpin_buy_good_panel"].rect, 420, -229)
	RectTransform.SetAnchoredPositionXY(self.node_list["binpin_show_rank_btn"].rect, 370, -150)

	self.node_list["binpin_get_way_panel"].rect:DOAnchorPos(Vector2(0, 0), tween_info.MoveTime)
	self.node_list["binpin_buy_good_panel"].rect:DOAnchorPos(Vector2(0, -229), tween_info.MoveTime)
	self.node_list["binpin_show_rank_btn"].rect:DOAnchorPos(Vector2(370, -5), tween_info.MoveTime)
	UITween.AlphaShow(GuideModuleName.KfActivityView, self.node_list["open_server_bipin_root"], 0, tween_info.ToAlpha,
		tween_info.AlphaTime, tween_info.AlphaShowType)

	ReDelayCall(self, function()
		UITween.AlphaShow(GuideModuleName.KfActivityView, self.node_list["binpin_get_way_tween_root"], 0,
			tween_info.ToAlpha, tween_info.AlphaTime)
		UITween.AlphaShow(GuideModuleName.KfActivityView, self.node_list["binpin_show_rank_btn_tween_root"], 0,
			tween_info.ToAlpha, tween_info.AlphaTime)
	end, tween_info.AlphaDelay, "kaifu_bipin_panel_tween")
end

function KfActivityView:FlushKFServerModel(model_cfg)
	if IsEmptyTable(model_cfg) then
		return
	end

	if model_cfg.show_type == 3 then
        self.node_list.soul_ring_model:SetActive(true)
        self.node_list.kf_server_model:SetActive(false)
		if model_cfg.rotation and "" ~= model_cfg.rotation then
			local pos_list = string.split(model_cfg.rotation, "|")
			local x = tonumber(pos_list[1]) or 0
			local y = tonumber(pos_list[2]) or 0
			local z = tonumber(pos_list[3]) or 0

			self.soul_ring_model:SetRTAdjustmentRootLocalRotation(x, y, z)
		end

		if model_cfg.display_scale and "" ~= model_cfg.display_scale then
			self.soul_ring_model:SetRTAdjustmentRootLocalScale(model_cfg.display_scale, model_cfg.display_scale, model_cfg.display_scale)
		end

        local target_data = {}
        if model_cfg.soul_ring_id and "" ~= model_cfg.soul_ring_id then
			local soul_ring_id_list = string.split(model_cfg.soul_ring_id, "|")
            for k, v in pairs(soul_ring_id_list) do
                local cfg = ShenShouWGData.Instance:GetShenShouCfg(tonumber(v))
                target_data[k - 1] = {soul_ring_effect = cfg.soul_ring_effect}
            end

            self.soul_ring_model:SetTotalSoulRingResid(target_data, false, #soul_ring_id_list)
		end
	else
		self.node_list.soul_ring_model:SetActive(false)
        self.node_list.kf_server_model:SetActive(true)
		-- 形象展示
		local display_data = {}
		display_data.should_ani = true
		if model_cfg.show_item_id ~= 0 and model_cfg.show_item_id ~= "" then
			local split_list = string.split(model_cfg.show_item_id, "|")
			if #split_list > 1 then
				local list = {}
				for k, v in pairs(split_list) do
					list[tonumber(v)] = true
				end
				display_data.model_item_id_list = list
			else
				display_data.item_id = model_cfg.show_item_id
			end
		end

		display_data.hide_model_block = true
		display_data.bundle_name = model_cfg.model_bundle_name
		display_data.asset_name = model_cfg.model_asset_name
		display_data.render_type = model_cfg.show_type - 1
		display_data.need_wp_tween = true
		display_data.not_show_active = true

		if model_cfg.display_pos and model_cfg.display_pos ~= "" then
			local pos_list = string.split(model_cfg.display_pos, "|")
			local pos_x = tonumber(pos_list[1]) or 0
			local pos_y = tonumber(pos_list[2]) or 0
			local pos_z = tonumber(pos_list[3]) or 0

			display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
		end

		if model_cfg.rotation and model_cfg.rotation ~= "" then
			local rot_list = string.split(model_cfg.rotation, "|")
			local rot_x = tonumber(rot_list[1]) or 0
			local rot_y = tonumber(rot_list[2]) or 0
			local rot_z = tonumber(rot_list[3]) or 0

			display_data.role_rotation = Vector3(rot_x, rot_y, rot_z)
		end

		if model_cfg.model_scale and model_cfg.model_scale ~= "" then
			display_data.model_adjust_root_local_scale = model_cfg.model_scale
		end

		display_data.model_rt_type = ModelRTSCaleType.L

		self.model_display:SetData(display_data)
	end
end

function KfActivityView:ShowBiPinRightPanel(is_show)
	is_show = is_show or false
	self.node_list["op_ser_middle_root"]:SetActive(is_show)
	self.node_list["op_ser_right_panel"]:SetActive(is_show)
	if is_show then
		local rush_type = self:BiPinGetSelectRushType()
		local model_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfigByRankType(rush_type)
		self:FlushKFServerModel(model_cfg)
	end
end

function KfActivityView:ShowOGATopPanel()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local rush_type = self:BiPinGetSelectRushType()
	local index = self:GetIndexByRushType(rush_type)
	local cur_data = self.top_btn_data_list and (self.top_btn_data_list[index].cfg or {}) or {}
	local is_show_oga_top = false
	if not IsEmptyTable(cur_data) then
		is_show_oga_top = open_day == cur_data.close_day_index
	end
	--self.node_list["oga_top_bg"]:SetActive(is_show_oga_top)
end

function KfActivityView:FlushOGADailyReward()
	-- local flag = ServerActivityWGData.Instance:GetOGADailyRewardFlag()
	-- self.node_list["btn_op_da"]:SetActive(flag == 0)
end

function KfActivityView:OnClickOGADailyReward()
	ServerActivityWGCtrl.Instance:SendOGAExtendReqOperate(OGA_EXTEND_OPERATE_TYPE_ENUM.FETCH_DAILY_REWARD)
end



------------------------------------------- OpenServerRankRewardItem -------------------------------------------
OpenServerRankRewardItem = OpenServerRankRewardItem or BaseClass(BaseRender)

function OpenServerRankRewardItem:__delete()
	for k,v in pairs(self.item_cell_list) do
		v:DeleteMe()
	end
	self.item_cell_list = nil
end

function OpenServerRankRewardItem:LoadCallBack()
	self.item_cell_list = {}
	self.node_list.get_reward_btn.button:AddClickListener(BindTool.Bind(self.OnClickGetRewardBtn, self))
end

function OpenServerRankRewardItem:OnFlush()
	local data = self:GetData()

	self:SetItemCellList(data.reward_item)

	if ServerActivityWGData.Instance:IsSpecialRank(data.rush_type) then
		self:FlsuhSpecialDesc()
	else
		self:FlsuhDesc()
	end

	self:FlushBtn()
end

function OpenServerRankRewardItem:FlsuhDesc()
	local data = self:GetData()
	local rank_name = Language.OpenServer.LingQuLimit4[data.rush_type] or ""
	local rank_unit = Language.OpenServer.Unit[data.rush_type] or ""
	if data.rush_type == 10 then -- 战力提升榜
		if data.min_rank == data.max_rank then
			self.node_list.desc_label.text.text = string.format(Language.OpenServer.BiPinZhanliDesc, data.min_rank)
			self.node_list.lbl_condition.text.text = string.format(Language.OpenServer.BiPinZhanliCondition, data.reach_value)
		else
			self.node_list.desc_label.text.text = string.format(Language.OpenServer.BiPinRewardZhanliDesc, data.min_rank, data.max_rank)
			self.node_list.lbl_condition.text.text = string.format(Language.OpenServer.BiPinZhanliCondition, data.reach_value)
		end
	elseif data.rush_type == 12 then -- 神武提升榜
		if data.min_rank == data.max_rank then
			self.node_list.desc_label.text.text = string.format(Language.OpenServer.BiPinRankDesc, rank_name, data.min_rank)
			self.node_list.lbl_condition.text.text = string.format(Language.OpenServer.BiPinZhanliCondition2, data.reach_value, rank_unit)
		else
			self.node_list.desc_label.text.text = string.format(Language.OpenServer.BiPinRewardDesc, rank_name, data.min_rank, data.max_rank)
			self.node_list.lbl_condition.text.text = string.format(Language.OpenServer.BiPinZhanliCondition2, data.reach_value, rank_unit)
		end
	else
		if data.min_rank == data.max_rank then
			self.node_list.desc_label.text.text = string.format(Language.OpenServer.BiPinRankDesc, rank_name, data.min_rank)
			self.node_list.lbl_condition.text.text = string.format(Language.OpenServer.BiPinRankConditionDesc, rank_name, data.reach_value, rank_unit)
		else
			self.node_list.desc_label.text.text = string.format(Language.OpenServer.BiPinRewardDesc, rank_name, data.min_rank, data.max_rank)
			self.node_list.lbl_condition.text.text = string.format(Language.OpenServer.BiPinRankConditionDesc, rank_name, data.reach_value, rank_unit)
		end
	end
end

function OpenServerRankRewardItem:FlsuhSpecialDesc()
	local data = self:GetData()
	local body_show_type = data.rush_type == 3 and MOUNT_LINGCHONG_APPE_TYPE.MOUNT or MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG
	local cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(body_show_type, data.reach_value) or {}
	local rank_name = Language.OpenServer.LingQuLimit2[data.rush_type] or ""
	if data.min_rank == data.max_rank then
		self.node_list.desc_label.text.text = string.format(Language.OpenServer.BiPinRankDesc, rank_name, data.min_rank)
		self.node_list.lbl_condition.text.text = string.format(Language.OpenServer.BiPinRankConditionDesc2, rank_name, cfg.grade_num or 0, cfg.star_num or 0)
	else
		self.node_list.desc_label.text.text = string.format(Language.OpenServer.BiPinRewardDesc, rank_name, data.min_rank, data.max_rank)
		self.node_list.lbl_condition.text.text = string.format(Language.OpenServer.BiPinRankConditionDesc2, rank_name, cfg.grade_num or 0, cfg.star_num or 0)
	end
end

function OpenServerRankRewardItem:SetItemCellList(reward_data)
	local reward_list = SortTableKey(reward_data)
	local item_cell_list = self.item_cell_list
	if #item_cell_list < #reward_list then
		local cell_parent = self.node_list.reward_content
		for i = #item_cell_list + 1, #reward_list do
			item_cell_list[i] = ItemCell.New(cell_parent)
		end
		self.item_cell_list = item_cell_list
	end

	for i,cell in ipairs(item_cell_list) do
		if reward_list[i] then
			cell:SetData(reward_list[i])
			cell:SetActive(true)
		else
			cell:SetActive(false)
		end
	end
end

function OpenServerRankRewardItem:FlushBtn()
	local data = self:GetData()
	--local rank_reward_rank = ServerActivityWGData.Instance:GetOpenServerBiPinRankRewardFlag(data.rush_type)
	local rank_reward_fetch = ServerActivityWGData.Instance:GetOpenServerBiPinRankRewardFetch(data.rush_type)
	local opengame_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(data.rush_type)
	local rank_data = RankWGData.Instance:GetActMyRank(opengame_cfg and opengame_cfg.rank_type)
	--local is_show = rank_data and rank_data.my_rank >= data.min_rank and rank_data.my_rank <= data.max_rank
	self.node_list.pass_day_img:SetActive(act_is_over and rank_reward_fetch == 0)
	if rank_reward_fetch >= data.min_rank and rank_reward_fetch <= data.max_rank then
		self.node_list.get_reward_img:SetActive(true)
	else
		self.node_list.get_reward_img:SetActive(false)
	end
end

function OpenServerRankRewardItem:OnClickGetRewardBtn()
	ServerActivityWGCtrl.Instance:SendCSOGARushRankOp(
			OGA_RUSH_RANK_OP_TYPE.RUSH_RANK_REWARD_TYPE_RANK_FETCH,
			self.data.rush_type,
			self:GetIndex() - 1
		)
end

function OpenServerRankRewardItem:PalyOSBPRewardItemAnim(item_index)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.KfActivityView.ListCellRender

    UITween.FakeHideShow(self.node_list["tween_root"])
    ReDelayCall(self, function()
        if self.node_list and self.node_list["tween_root"] then
            UITween.RotateAlphaShow(GuideModuleName.KfActivityView, self.node_list["tween_root"], tween_info)
        end

    end, tween_info.NextDoDelay * wait_index, "osbp_reward_item_" .. wait_index)
end