CultivationBreakFailView = CultivationBreakFailView or BaseClass(SafeBaseView)

function CultivationBreakFailView:__init()
	self.mask_alpha = MASK_BG_ALPHA_TYPE.Normal
    self.view_style = ViewStyle.Window
	self:SetMaskBg(true,true)
    -- self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:AddViewResource(0, "uis/view/cultivation_ui_prefab", "layout_cultivation_break_fail_view")
end

function CultivationBreakFailView:LoadCallBack()

    XUI.AddClickEventListener(self.node_list.btn_ok, BindTool.Bind(self.OnClickBtnOk, self))
end

function CultivationBreakFailView:OnClickBtnOk()
    self:Close()
end

