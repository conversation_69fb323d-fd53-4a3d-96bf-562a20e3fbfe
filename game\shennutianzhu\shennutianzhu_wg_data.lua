ShenNuTianZhuData = ShenNuTianZhuData or BaseClass()

function ShenNuTianZhuData:__init()
    if ShenNuTianZhuData.Instance then
		error("[ShenNuTianZhuData] Attempt to create singleton twice!")
        return
    end

    ShenNuTianZhuData.Instance = self

    local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_shennutianzhu_cfg_auto")
    self.shennutianzhu_cfg = cfg.rmb_buy

    self.grade = -1
    self.reward_flag = {}
end

function ShenNuTianZhuData:__delete()
    ShenNuTianZhuData.Instance = nil
end

function ShenNuTianZhuData:GetShenNuTianZhuCfg()
    return self.shennutianzhu_cfg[self.grade]
end

function ShenNuTianZhuData:SetSntzBuyInfo(protocol)
    self.grade = protocol.grade
    self.reward_flag = protocol.reward_flag
end

function ShenNuTianZhuData:GetShenNuTianZhuCurGrade()
    return self.grade
end

function ShenNuTianZhuData:GetCurProductIsBuy()
    if self.grade == -1 or IsEmptyTable(self.reward_flag) then
        return false
    end

    local cfg = self:GetShenNuTianZhuCfg()
    if not cfg then
        return false
    end

    return self.reward_flag[cfg.seq] > 0
end

function ShenNuTianZhuData:GetCurRewardList()
    local cfg = self:GetShenNuTianZhuCfg()
    local temp_list = {}
    if cfg then
        local icon_list = Split(cfg.kuang_icon or "", "|")
        for i = 0, #cfg.reward_item do
            local item_id = cfg.reward_item[i]
            local data = {item_id = item_id, kuang_icon = icon_list[i + 1]}
            table.insert(temp_list, data)
        end
    end

    return temp_list
end

function ShenNuTianZhuData:GetCurModelList()
    local cfg = self:GetShenNuTianZhuCfg()
    if not cfg then
        return {}
    end

    local model_list = {}
    local is_tianshen = cfg.skill_id > 0            -- 为0是天命所归模型
    local temp_data = Split(cfg.model_list or "", "|")
    local scale_data = Split(cfg.model_list_scale or "", "|")
    local pos_uix_data = Split(cfg.pos_ui_x or "", "|")
    local pos_uiy_data = Split(cfg.pos_ui_y or "", "|")
    local pos_modelx_data = Split(cfg.pos_model_x or "", "|")
    local pos_modely_data = Split(cfg.pos_model_y or "", "|")
    local pos_modelz_data = Split(cfg.pos_model_z or "", "|")

    if not IsEmptyTable(temp_data) then
        for i, v in ipairs(temp_data) do
            model_list[i] = {}
            model_list[i].is_tianshen = is_tianshen
            model_list[i].model_index = tonumber(v)
            model_list[i].model_scale = tonumber(scale_data[i]) or 0.5
            model_list[i].pos_ui_x = tonumber(pos_uix_data[i]) or 0
            model_list[i].pos_ui_y = tonumber(pos_uiy_data[i]) or 0
            model_list[i].pos_model_x = tonumber(pos_modelx_data[i]) or 0
            model_list[i].pos_model_y = tonumber(pos_modely_data[i]) or 0
            model_list[i].pos_model_z = tonumber(pos_modelz_data[i]) or 0
            if is_tianshen then
                model_list[i].active = TianShenWGData.Instance:IsActivation(tonumber(v))
            else
                local artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(tonumber(v))
                model_list[i].active = artifact_data and artifact_data.level > 0
            end
        end
    end

    return model_list
end

function ShenNuTianZhuData:GetCurProductCapability()
    local cfg = self:GetShenNuTianZhuCfg()
    if not cfg then
        return 0
    end

    local cap = 0
    local temp_data = Split(cfg.model_list or "", "|")
    if cfg.skill_id > 0 then
        -- 天神激活的基础战力
        if not IsEmptyTable(temp_data) then
	    	for i, v in ipairs(temp_data) do
                local act_item_cfg = TianShenWGData.Instance:GetTianshenItemCfgByIndex(tonumber(v))
                if act_item_cfg then
                    local attr_list, tianshen_cap = ItemShowWGData.Instance:GetTianShenAttrByData(act_item_cfg.act_item_id)
                    cap = cap + tianshen_cap
                end
	    	end
	    end

        -- 天神合击技能的战力,表配的固定战力
        local heji_skill_id = TianShenWGData.Instance:GetUnionSkillCfgById(cfg.skill_id).tianshen_heji_skill_id
        local heji_skill_cap = SkillWGData.Instance:GetTianShenHeJiSkillById(heji_skill_id, 1).capability_inc
        cap = cap + heji_skill_cap
    else
        for k, v in pairs(temp_data) do
            local artifact_cap = ArtifactWGData.Instance:GetArtifactCapabilityBySeq(tonumber(v), true)
            cap = cap + artifact_cap
        end
    end

    local cap_item_list
    if cfg.cap_item_list and cfg.cap_item_list ~= "" then
        cap_item_list = Split(cfg.cap_item_list, "|")
    end

    local extra_item_cap = 0
    if cap_item_list then
        for k, v in pairs(cap_item_list) do
            local item_cap = ItemShowWGData.Instance.CalculateCapability(tonumber(v))
            extra_item_cap = extra_item_cap + item_cap
        end
    end

    return cap + extra_item_cap
end

