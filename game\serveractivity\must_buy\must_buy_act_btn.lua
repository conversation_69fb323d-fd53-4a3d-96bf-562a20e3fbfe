MustBuyActivityBtn = MustBuyActivityBtn or BaseClass(MainActivityBtn)

MUSTBUY_ACT_BTN = {
	SHOW_TIP = 1,
	SHOW_NEW = 2,
	SHOW_COUNT = 3,
	CHANGE_STATUS = 4,
}

function MustBuyActivityBtn:__init()
	self:BindGlobalEvent(MUSTBUY_EVENT.BUTTON_CHANGE, BindTool.Bind(self.ButtonChange, self))
end

function MustBuyActivityBtn:__delete()
	self:CancelCountDown()
	self.show_time_bg = nil
	self.act_id = nil
end

function MustBuyActivityBtn:CreateBtnAsset(parent, activity_type)
	self:LoadAsset("uis/view/main_ui_prefab", "must_buy_button", parent.transform, function(obj)
		obj.name = "id:" .. activity_type
		self.act_id = activity_type
 	end)
end

function MustBuyActivityBtn:OnFlush(param_t)
	MainActivityBtn.OnFlush(self, param_t)
	if self:GetActive() == true then
		self:FlushActive()
	end
end

function MustBuyActivityBtn:FlushActive()
	local item_data = MustBuyWGData.Instance:GetItemList()
	self:SetActive(#item_data > 0)
end

function MustBuyActivityBtn:SetTxtNewStatus(status)
	if not self.node_list then
		return
	end
	self.node_list["txt_new"]:SetActive(status)
end

function MustBuyActivityBtn:ButtonChange(reason, arg1)
	if self:IsNil() then
		return
	end
	
	if reason == MUSTBUY_ACT_BTN.CHANGE_STATUS then
		self:SetActive(arg1)
	elseif reason == MUSTBUY_ACT_BTN.SHOW_NEW then
		self:SetTxtNewStatus(arg1)
	elseif reason == MUSTBUY_ACT_BTN.SHOW_COUNT then
		if arg1 >= 9999999999 or arg1 < TimeWGCtrl.Instance:GetServerTime() then
			self:EndCountDown()
		else
			self:SetCountDown(arg1, BindTool.Bind(self.UpdateCountDown, self),
				BindTool.Bind(self.CompleteCountDown, self))
		end
	end
end

function MustBuyActivityBtn:UpdateCountDown(elapse_time, total_time)
	if not self.show_bubble_time then
		self.show_bubble_time = MustBuyWGData.Instance:GetShowBubbleTime()
	end

	local off_time = MustBuyWGData.Instance:ShowButtonNextTime()
	if off_time == 9999999999 then
		self:EndCountDown()
		return
	end
	local time = off_time - TimeWGCtrl.Instance:GetServerTime()
	local limit_time = MustBuyWGData.Instance:GetShowCountTime()
	local time_str = ""
	if time > 0 then
		if time < limit_time then
			local hour = math.floor(time / 3600)
			local minute = math.floor(time / 60)
			local second = math.floor(time % 60)
			if hour == 0 then
				time_str = string.format("%02d:%02d", minute, second)
			elseif hour > 24 then
				time_str = string.format(Language.Common.ShowTime2, math.ceil(hour / 24))
			else
				time_str = string.format(Language.Common.ShowTime3, hour)
			end
		else
			self:CloseBottom()
			return
		end
	else
		self:CompleteCountDown()
		return
	end

	self:ChangeTime(ToColorStr(time_str, COLOR3B.GREEN))
end

function MustBuyActivityBtn:ChangeTime(str)
	if not self.node_list then
		return
	end
	if not self.show_time_bg then
		self.show_time_bg = true
	end
	self:SetTime(str)
end

--倒计时结束请求协议刷新按钮状态
function MustBuyActivityBtn:CompleteCountDown()
	self:CloseBottom()

	if ViewManager.Instance:IsOpen(GuideModuleName.MustBuy) then
		return
	end
	MustBuyWGCtrl.Instance:SendOffSale()
end

--调时间后没有倒计时（9999999999），结束倒计时
function MustBuyActivityBtn:EndCountDown()
	self:CancelCountDown()
	self:CloseBottom()
end

function MustBuyActivityBtn:CloseBottom()
	if not self.show_time_bg then
		return
	end

	self.show_time_bg = false
	self:SetTime("")
end

function MustBuyActivityBtn:CancelCountDown()
	if CountDownManager.Instance:HasCountDown("must_buy_btn") then
		CountDownManager.Instance:RemoveCountDown("must_buy_btn")
	end
end

function MustBuyActivityBtn:SetCountDown(time, update_func, complete_func)
	local CD = CountDownManager.Instance
	if CD:HasCountDown("must_buy_btn") then
		CD:RemoveCountDown("must_buy_btn")
	end
	CD:AddCountDown("must_buy_btn", update_func, complete_func, time, nil, 0.3)
end