require("core/draw_part_render")
ResPreload = require "core.res_preload"
ResPreload.init()

local typeActorRender = typeof(ActorRender)

DrawPart = DrawPart or BaseClass()
local UnityMaterial = typeof(UnityEngine.Material)
-- local UnityTrailRenderer = typeof(UnityEngine.TrailRenderer)
local UnitySkinnedMeshRenderer = typeof(UnityEngine.SkinnedMeshRenderer)
local UnityMeshRenderer = typeof(UnityEngine.MeshRenderer)
local UnityParticleSystem = typeof(UnityEngine.ParticleSystem)
-- local UnityEngineLayerMask = UnityEngine.LayerMask
local GeometryRendererQueue = 2000
local AlphaTestQueue = 2450

local Action_Time = 0.2
local localScale = Vector3(1, 1, 1)
local localRotate = Quaternion.Euler(0, 0, 0)

local instantiateQueue = InstantiateQueue.New()
InstantiateQueue.Global = instantiateQueue
HeadCustomizationType = ChangeSkin.HeadCustomizationType

DRAW_MODEL_TYPE = {
    ROLE = 1,
}

function DrawPart:__init()
    self.obj = nil
    self.asset_bundle = nil
    self.asset_name = nil
    self.loading = false
    self.load_priority = 0
    self.load_complete = nil
    self.remove_callback = nil
    self.dynamic_bone = nil

    self.parent = nil
    self.click_listener = nil
    self.attack_target = nil
    self.play_attach_effect = false

    self.animator_triggers = nil
    self.animator_bools = nil
    self.animator_ints = nil
    self.animator_floats = nil
    self.animator_listener = nil
    self.animator_plays = nil
    self.animator_handle = nil

    self.is_main_role = false
    self.enable_mount_up = false
    self.is_visible = true
    self.part = 0
    self.offset_y = 0
    self.fixed_transition_duration = Action_Time        -- 动画过渡的持续时间

    self.add_occlusion_material_list = nil

    self.is_use_objpool = false
    self.is_in_queque_load = false

    self.is_hd_texture = false
    self.multi_color = nil
    self.is_disable_all_attach_effects = false
    self.is_always_animate = false
    self.is_lerp_probe = false

    self.cur_detail_level = SceneObjDetailLevel.High    -- 当前的细节等级
    self.need_detail_level = SceneObjDetailLevel.Low    -- 需要的细节等级，只有当前等级大于需要的等级时，才会加载
    self.draw_part_render = DrawPartRender.New()

    self.real_active_call = nil
    self.quality_override_level = -1
    self.adjust_pint = nil
    self.mount_fazhen_point_obj = nil
    self.draw_obj = nil
    self.part_scale = localScale
    self.part_rotate = localRotate
    self.set_ro = nil
    self.obj_transform = nil
    self.set_scale = nil
    self.is_force_max_lod = false
    self.is_show_fade = false
    self.skinned_mesh_renderers = nil
    self.set_rlm_list = {}
    self.draw_model_list = nil
    self.change_skin_comp = nil

    self:ResetDrawModelInfo()
    self:InitSkinPartLoadHighList()
end

function DrawPart:__delete()
    self:Clear()

    if self.draw_part_render then
        self.draw_part_render:DeleteMe()
        self.draw_part_render = nil
    end
end

function DrawPart:TryInitDrawModelList()
    if not self.draw_model_list then
        self:InitDrawModelList()
    end
end

function DrawPart:InitDrawModelList()
    self.draw_model_list = {}
    for k, model_type in pairs(DRAW_MODEL_TYPE) do
        if self.draw_model_type == model_type then
            local part_list
            if model_type == DRAW_MODEL_TYPE.ROLE then
                part_list = ROLE_SKIN_TYPE
            end
    
            if part_list then
                for key, sub_type in pairs(part_list) do
                    self.draw_model_list[sub_type] = {
                        is_init = false,
                        draw_model_type = model_type,
                        sub_type = sub_type,
                    }
                end
            end
        end
    end
end

function DrawPart:RestDrawModelTypeListInitStatus()
    if not self.draw_model_list then return end
    for k,v in pairs(self.draw_model_list) do
        v.is_init = false
    end
end

function DrawPart:GetDrawModelTypeListAllInit(model_type)
    if not self.draw_model_list then return false end

    for k,v in pairs(self.draw_model_list) do
        if not v.is_init then
            return false
        end
    end

    return true
end

function DrawPart:GetDrawModelListData(sub_type)
    if not self.draw_model_list then return nil end
    return self.draw_model_list[sub_type]
end

function DrawPart:Clear()
    self:RemoveDynamicBone()
    self:ResetAnimatorSpeed()
    self:CancleForceLodMaxLevel()
    self:ResetFade()

    if self.animator_handle then
        for _, v in pairs(self.animator_handle) do
            v:Dispose()
        end
    end
    self.animator_handle = nil
    self:RemoveModel()
    self:ResetDrawModelInfo()
    self:InitSkinPartLoadHighList()

    self.obj = nil
    self.asset_bundle = nil
    self.asset_name = nil
    self.loading = false
    self.load_priority = 0
    self.load_complete = nil
    self.remove_callback = nil
    self.dynamic_bone = nil

    self.parent = nil
    self.click_listener = nil
    self.attack_target = nil
    self.play_attach_effect = false

    self.animator_triggers = nil
    self.animator_bools = nil
    self.animator_ints = nil
    self.animator_floats = nil
    self.animator_listener = nil
    self.animator_plays = nil
    self.animator_handle = nil
    self.change_skin_comp = nil

    self.is_main_role = false
    self.enable_mount_up = false
    self.is_visible = true
    self.part = 0
    self.offset_y = 0
    self.fixed_transition_duration = Action_Time

    self.add_occlusion_material_list = nil

    self.is_use_objpool = false
    self.is_in_queque_load = false

    self.material_quality = 0
    self.is_hd_texture = false
    self.multi_color = nil
    self.is_disable_all_attach_effects = false
    self.is_always_animate = false

    self.cur_detail_level = SceneObjDetailLevel.High    -- 当前的细节等级
    self.need_detail_level = SceneObjDetailLevel.Low    -- 需要的细节等级，只有当前等级大于需要的等级时，才会加载

    self.real_active_call = nil
    self.quality_override_level = -1
    self.adjust_pint = nil
    self.mount_fazhen_point_obj = nil
    self.draw_obj = nil
    self.set_ro = nil
    self.obj_transform = nil
    self.set_scale = nil
    self.skinned_mesh_renderers = nil
    self.set_rlm_list = {}
    self.now_cache_body_render = nil
    self.now_cache_hair_render = nil
    self.now_cache_face_render = nil
    self.load_skin_callback = nil
end

function DrawPart:DestoryObj(obj)
    if IsNil(obj) then
        return
    end

    -- self:ResetAnimatorStatus()
    if self.is_use_objpool then
        self:RemoveOcclusion()

        -- 如果需要优化Animaor,则回Act池
        if self.draw_part_render and self.draw_part_render:IsNeedAnimatorOptimize() then
            ResPoolMgr:Release(obj, ResPoolReleasePolicy.Culling)
        else
            ResPoolMgr:Release(obj)
        end

        if self.draw_part_render then
            self.draw_part_render:Reset()
            self.draw_part_render:SetActorRender(nil)
        end
    else
        if self.draw_part_render then
            self.draw_part_render:SetActorRender(nil)
        end

        ResMgr:Destroy(obj)
    end
end

function DrawPart:Reset(obj)
    local object = nil
    self.fixed_transition_duration = Action_Time
    if obj == nil then
        object = self.obj
    else
        if type(obj) == "userdata" then
            object = U3DObject(obj)
        else
            object = obj
        end
    end

    self.change_skin_comp = nil
    self:ReleaseDrawModelObj()
    if object ~= nil and not IsNil(object.gameObject) then
        if object.attach_obj ~= nil then
            object.attach_obj:CleanAttached()
        end

        if object.attach_skin_obj ~= nil then
            object.attach_skin_obj:ResetBone()
        end

        if self.old_animate_culling_mode then
            object.animator.cullingMode = self.old_animate_culling_mode
            self.old_animate_culling_mode = nil
        end

        Transform.SetLocalScaleXYZ(object.transform, self.part_scale.x, self.part_scale.y, self.part_scale.z)
        object.transform.localRotation = Quaternion.Euler(self.part_rotate.x, self.part_rotate.y, self.part_rotate.z)
    end
end

-- 还原对象池中的物体动作成idle状态 by.cqe
function DrawPart:ResetAnimatorStatus()
    if self.obj == nil or IsNil(self.obj.animator) then
        return
    end

    self.obj.animator:CrossFadeInFixedTime(SceneObjAnimator.Idle, 0)
end

function DrawPart:GetObj()
    return self.obj
end

function DrawPart:SetDrawObj(draw_obj)
    self.draw_obj = draw_obj
end


function DrawPart:GetDrawObj()
    return self.draw_obj
end

function DrawPart:SetMainRole(is_main_role)
    self.is_main_role = is_main_role
end

function DrawPart:SetParent(parent)
    self.parent = parent
    if self.obj ~= nil then
        self:_FlushParent(self.obj)
    end
end

function DrawPart:_FlushParent(obj)
    if self.parent ~= nil then
        obj.transform:SetParent(self.parent.transform, false)
    else
        obj.transform:SetParent(nil)
    end
end

function DrawPart:SetMaterialQuality(material_quality)
    self.material_quality = material_quality
    if nil ~= self.draw_part_render and not self.is_show_fade then
        self.draw_part_render:SetMaterialQuality(self.material_quality)
    end
end

function DrawPart:SetIsUseObjPool(is_use_objpool)
    self.is_use_objpool = is_use_objpool
end

function DrawPart:SetIsInQueueLoad(is_in_queque_load)
    self.is_in_queque_load = is_in_queque_load
end

function DrawPart:SetRemoveCallback(callback)
    self.remove_callback = callback
end

function DrawPart:GetSkinnedMeshRenderers()
    if self.obj == nil and IsNil(self.obj.gameObject) then
        return nil
    end

    if IsNil(self.skinned_mesh_renderers) then
        self.skinned_mesh_renderers = self.obj:GetComponentsInChildren(UnitySkinnedMeshRenderer)
    end
    
    return self.skinned_mesh_renderers
end

function DrawPart:SetMainTexture(image)
    if self.draw_part_render ~= nil then
        self.draw_part_render:TrySetMainTexture(image)
    end
end

function DrawPart:TryResetMainTexture()
    if self.draw_part_render ~= nil then
        self.draw_part_render:TryResetMainTexture()
    end
end

function DrawPart:GetIsLoaded()
    return self.obj ~= nil
end

function DrawPart:ModelIsLoaded(bundle, asset)
    if not bundle or not asset or bundle == "" or asset == "" then
        return false
    end

    local cur_bundle, cur_asset = self:GetCurAssetPath()
    if cur_bundle == bundle and cur_asset == asset and self.loading then
        return true
    end

    return false
end

-- 【好J8无语的远古代码】
function DrawPart:SetActive(value)
    if self.obj ~= nil then
        if not IsNil(self.obj.animator) and self.obj.animator.isActiveAndEnabled then
            self.obj.animator:SetActive(value)
        end
    end
end

function DrawPart:SetObjActive(value)
    if self.obj ~= nil and value ~= nil then
         -- print_error("--DrawPart.SetObjActive--", SceneObjPartStr[self.part], value)
        self.obj:SetActive(value)
        if value then
            self:CheckAniParam()
        end
    end
end

function DrawPart:SetNeedDetailLevel(level)
    self.need_detail_level = level
    self:SetVisible()
end

function DrawPart:SetCurDetailLevel(level)
    self.cur_detail_level = level
    self:SetVisible()
end

function DrawPart:IsShowDetail()
    return self.cur_detail_level >= self.need_detail_level
end

function DrawPart:SetVisible(visible)
    if visible ~= nil then
        self.is_visible = visible
    end
    
    visible = self.is_visible and self:IsShowDetail()
    if visible then
        if self.obj == nil and self.asset_bundle ~= nil and self.asset_name ~= nil then
            local asset_bundle = self.asset_bundle
            local asset_name = self.asset_name
            local draw_model_type = self.draw_model_type
            local extra_model_data = self.extra_model_data
            self.asset_bundle = nil
            self.asset_name = nil
            self:ResetDrawModelInfo()
            self:ChangeModel(asset_bundle, asset_name, nil, draw_model_type, extra_model_data, self.load_skin_callback)
        end
    else
        if self.obj ~= nil then
            if self.remove_callback ~= nil then
                self.remove_callback(self.draw_obj, self.obj, self.part, self)
            end
            self:Reset()

            self:DestoryObj(self.obj.gameObject)
            self.obj = nil
            self.obj_transform = nil
        end
    end
end

function DrawPart:GetVisible()
    return self.is_visible
end


function DrawPart:SetIsDisableAllAttachEffects(is_disable_all_attach_effects)
    self.is_disable_all_attach_effects = is_disable_all_attach_effects
    self.draw_part_render:SetIsDisableAllAttachEffects(self.is_disable_all_attach_effects)
end

-- 重载AttachEffects上的QualityControl
function DrawPart:SetQualityControlOverrideLevel(quality_override_level)
    self.quality_override_level = quality_override_level
    self.draw_part_render:SetQualityControlOverrideLevel(self.quality_override_level)
end

-- 投射影子
function DrawPart:SetIsCastShadow(is_cast_shadow)
    self.is_cast_shadow = is_cast_shadow
    self.draw_part_render:SetIsCastShadow(is_cast_shadow)
end

function DrawPart:SetIsHdTexture(is_hd_texture)
    self.is_hd_texture = is_hd_texture
end

--[[
    extra_model_data = {
        role_body_res = 身体资源id
        role_face_res = 脸资源id
        role_hair_res = 头发资源id
        eye_size = 眼眶大小 - BlendShape - number
        eye_position  = 眼眶上下 - BlendShape - number
        eye_angle = 眼眶角度 - BlendShape - number
        eye_close = 眼眶开合 - BlendShape - number
        eyebrow_angle = 眉毛角度 - BlendShape - number
        eye_shadow_color = 眼影颜色 - 材质属性 table (r=255,g=255,b=255,a=255)
        left_pupil_type = 左-瞳孔款式 number
        left_iris_size = 左-虹膜大小 - 材质属性 - number
        left_pupil_size = 左-瞳孔大小 - 材质属性 - number
        left_pupil_color = 左-瞳孔颜色 - 材质属性 - table (r=255,g=255,b=255,a=255)
        -- 瞳孔材质相同，赋值左眼即可，右眼可不赋值
        right_pupil_type = 右-瞳孔款式  number
        right_iris_size = 右-虹膜大小 - 材质属性 - number
        right_pupil_size = 右-瞳孔大小 - 材质属性 - number
        right_pupil_color = 右-瞳孔颜色 - 材质属性 - table (r=255,g=255,b=255,a=255)
        nose_size = 鼻子大小 - BlendShape - number
        nose_angle = 鼻头角度 - BlendShape - number
        mouth_size = 嘴巴大小 - BlendShape - number
        mouth_position = 嘴巴上下 - BlendShape - number
        mouth_angle = 嘴角角度 - BlendShape - number
        mouth_color = 嘴巴颜色 - 材质属性 - table (r=255,g=255,b=255,a=255)
        cheek_size = 脸颊大小 - BlendShape - number
        chin_length = 下巴长度 - BlendShape - number
        face_decal_id = 脸部贴花 number
        hair_color = 头发颜色 - 材质属性 - table (r=255,g=255,b=255,a=255)
    }
]]
function DrawPart:ChangeModel(asset_bundle, asset_name, callback, draw_model_type, extra_model_data, load_skin_callback)
    -- if SceneObjPart.Main == self.part then--string.find(asset_bundle, "arm") then -- self.is_main_role and 
    --     print_error("-----ChangeModel----", SceneObjPartStr[self.part], asset_bundle, asset_name, extra_model_data)
    -- end

    if not self.is_visible then
        self.asset_bundle = asset_bundle
        self.asset_name = asset_name
        self.draw_model_type = draw_model_type
        self.extra_model_data = extra_model_data
        return
    end

    self.load_skin_callback = load_skin_callback

    if self.asset_bundle == asset_bundle and self.asset_name == asset_name then
        self.load_callback = callback

        local is_all_same = true
        self.draw_model_type = draw_model_type
        if draw_model_type and SceneObjPart.Main == self.part then
            if not IsSameTable(self.extra_model_data, extra_model_data) then
                is_all_same = false
            end

            self.extra_model_data = extra_model_data
            self:ChangeAllDrawModel()
        end

        if is_all_same then
            if self.change_wait_sync_anim_part_cache_call_back then
                self.change_wait_sync_anim_part_cache_call_back(self.part, SENCE_OBJ_LOADED_STATUS.LOADED)
            end

            if self.loaded_entirety_call_back then
                self.loaded_entirety_call_back(self.part, true)
            end
        end

        if self.load_callback then
            self.load_callback()
        end

        if self.load_complete and self.obj ~= nil and not IsNil(self.obj.gameObject) then
            self.load_complete(self.draw_obj, self.obj, self.part, self, self.asset_bundle, self.asset_name)
        end
        return
    end

    self.draw_model_type = draw_model_type
    self.extra_model_data = extra_model_data

    self.is_need_all_draw_model_loaded = true
    local asset_change = self.asset_bundle ~= asset_bundle or self.asset_name ~= asset_name
    self.asset_bundle = asset_bundle
    self.asset_name = asset_name

    if self.loading and not asset_change then
        return
    end

    self.load_callback = callback
    if self.asset_bundle ~= nil and self.asset_name ~= nil then
        self.loading = true
        self:LoadModel(self.asset_bundle, self.asset_name, false)
    elseif self.obj ~= nil then
        if self.remove_callback ~= nil then
            self.remove_callback(self.draw_obj, self.obj, self.part, self)
        end
        self:Reset()
        self:DestoryObj(self.obj.gameObject)
        self.obj = nil
        self.obj_transform = nil
    end
end

function DrawPart:GetCurAssetPath()
    return self.asset_bundle or "", self.asset_name or ""
end

function DrawPart:RemoveModel()
    self.asset_bundle = nil
    self.asset_name = nil
    self.cur_play_anim = nil
    self:CancleLoadInQueue()

    if self.obj ~= nil and not IsNil(self.obj.gameObject) then
        if self.remove_callback ~= nil then
            self.remove_callback(self.draw_obj, self.obj, self.part, self)
        end

        self:Reset()
        self:DestoryObj(self.obj.gameObject)
        self.obj = nil
        self.obj_transform = nil
    end
end

function DrawPart:CancleLoadInQueue()
    if nil ~= self.load_queue_seesion then
        ResPoolMgr:__CancleGetInQueue(self.load_queue_seesion)
        self.load_queue_seesion = nil
    end
end

function DrawPart:LoadModel(asset_bundle, asset_name, is_reload)
    local cbdata = DrawPart.GetCBData()
    cbdata[1] = self
    cbdata[2] = asset_bundle
    cbdata[3] = asset_name
    cbdata[4] = is_reload

    if self.is_use_objpool then
        if self.is_main_role then
            if SceneObjPart.Main == self.part then
                ResPoolMgr:__GetDynamicObjSync(asset_bundle, asset_name, DrawPart._OnLoadComplete, nil, cbdata)
            else
                ResPoolMgr:__GetDynamicObjAsync(asset_bundle,
                                            asset_name,
                                            DrawPart._OnLoadComplete,
                                            nil,
                                            ResLoadPriority.high,
                                            cbdata)
            end
        elseif self.is_in_queque_load then
            local load_priority = ResLoadPriority.low
            if SceneObjPart.Main == self.part then
                load_priority = ResLoadPriority.mid
            end
            self:CancleLoadInQueue()
            self.load_queue_seesion = ResPoolMgr:__GetDynamicObjAsync(asset_bundle,
                                                                     asset_name,
                                                                     DrawPart._OnLoadComplete,
                                                                     nil,
                                                                     load_priority,
                                                                     cbdata)
        else
             self.load_queue_seesion = ResPoolMgr:__GetDynamicObjAsync(asset_bundle,
                                             asset_name,
                                             DrawPart._OnLoadComplete,
                                             nil,
                                             ResLoadPriority.high,
                                             cbdata)
        end
    else
      ResMgr:LoadGameobjAsync(
            asset_bundle,
            asset_name,
            DrawPart._OnLoadComplete,
            nil,
            cbdata,
            ResLoadPriority.low)
    end
end

function DrawPart:SetLoadComplete(complete, part)
    self.load_complete = complete
    self.part = part
end

function DrawPart:SetChangeWaitSyncAnimPartCacheCallBack(call_back)
    self.change_wait_sync_anim_part_cache_call_back = call_back
end

function DrawPart:SetLoadedEntiretyCallBack(call_back)
    self.loaded_entirety_call_back = call_back
end

function DrawPart._OnLoadComplete(obj, cbdata)
    local self = cbdata[1]
    local asset_bundle = cbdata[2]
    local asset_name = cbdata[3]
    local is_reload = cbdata[4]
    DrawPart.ReleaseCBData(cbdata)

    if IsNil(obj) then
        self.loading = false
        print_warning("Load model failed: ", asset_bundle, asset_name)
        if is_reload then
            return
        end
    end

    if self.obj ~= nil then
        if self.remove_callback ~= nil then
            self.remove_callback(self.draw_obj, self.obj, self.part, self)
        end
        self:Reset()
        self:DestoryObj(self.obj.gameObject)
        self.obj = nil
        self.obj_transform = nil
    end

    if IsNil(obj) or self.asset_bundle ~= asset_bundle or self.asset_name ~= asset_name then
        if not IsNil(obj) then
            self:Reset(obj)
            self:DestoryObj(obj)
            self.obj = nil
            self.obj_transform = nil
        end

        if self.asset_bundle ~= nil and self.asset_name ~= nil then
            --bundle、asset改变，重新加载
            self:LoadModel(self.asset_bundle, self.asset_name, true)
        else
            self.loading = false
        end

        return
    end

    self.loading = false
    self.obj = U3DObject(obj)
    -- print_error("--DrawPart._OnLoadComplete 部位加载完成--", SceneObjPartStr[self.part])
    self.obj_transform = self.obj.transform
    self.adjust_pint = nil
    self.mount_fazhen_point_obj = nil

    -- 获取特殊节点
    if (self.part == SceneObjPart.Mount or self.part == SceneObjPart.FightMount or self.part == SceneObjPart.Wing) then
        local point = self.obj.transform:Find("AdjustPoint")
        if not IsNil(point) then
            local pos = point.localPosition
            self.adjust_pint = {x = pos.x, y = pos.y, z = pos.z}
        end
    end

    if self.part == SceneObjPart.Mount or self.part == SceneObjPart.FightMount then
        self.mount_fazhen_point_obj = self.obj.transform:Find("FaZhenPoint")
    end

    self.part_scale = self.obj.transform.localScale
    self.part_rotate = self.obj.transform.localRotation

    local visible = self.is_visible and self:IsShowDetail()
    self:SetVisible()
    if not visible then
        return
    end

    if not self.draw_model_type then
        self:SetDrawPartRenderParam()
        if self.cur_play_anim then
            local is_sync_anim_part = self.draw_obj:GetIsWaitSyncAnimPart(self.part)
            if (not is_sync_anim_part) or (is_sync_anim_part and self.draw_obj.wait_sync_anim_data == nil) then
                self:CrossFade(self.cur_play_anim, false, 0, true)
            end
        end
    end

    self:UpdateAnimatorAlwaysAnimate()
    self:ResetAnimatorSpeed()
    self:SetForceLodMaxLevel(self.is_force_max_lod)
    self:_FlushParent(self.obj)

    if self.draw_obj.parent_obj and self.draw_obj.parent_obj.character_shadow then
        self.draw_obj.parent_obj.character_shadow:Validate()
    end

    self:_FlushClickListener(self.obj)

    local attachment = self.obj.actor_attachment
    if attachment ~= nil then
        attachment:SetMountUpTriggerEnable(self.enable_mount_up)
    end

    if not self.draw_model_type then
        self:FlushAllAnimatorParameters()
    end

    if self.draw_model_type then
        if self.part == SceneObjPart.Main then
            self:TryInitDrawModelList()
        end

        -- print_error("--DrawPart._OnLoadComplete111--", SceneObjPartStr[self.part], self.is_need_all_draw_model_loaded)
        if self.is_need_all_draw_model_loaded then
            self.obj:SetActive(false)
        end

        self:RestDrawModelTypeListInitStatus()
        self:ChangeAllDrawModel()
    else
        self:AddOcclusion()

        if self.load_complete then
            self.load_complete(self.draw_obj, self.obj, self.part, self, self.asset_bundle, self.asset_name)
        end

        if self.load_callback then
            self.load_callback(self.obj)
            self.load_callback = nil
        end
    end

    if self.offset_y > 0 then
        self.obj.transform.localPosition = u3dpool.vec3(self.obj.transform.localPosition.x,
        self.offset_y, self.obj.transform.localPosition.z)
    end
end

function DrawPart:SetDrawPartRenderParam()
    if not self.draw_part_render or not self.obj then
        return
    end

    self.draw_part_render:SetActorRender(self.obj:GetOrAddComponent(typeActorRender))
    self.draw_part_render:SetIsHdTexture(self.is_hd_texture)
    self.draw_part_render:SetIsDisableAllAttachEffects(self.is_disable_all_attach_effects)
    self.draw_part_render:SetQualityControlOverrideLevel(self.quality_override_level)
    self.draw_part_render:SetMaterialQuality(self.material_quality)
    self.draw_part_render:SetIsCastShadow(self.is_main_role)
    self.draw_part_render:SetIsCulled(false)
    self.draw_part_render:TrySetLerpProbe(self.is_lerp_probe)
    self.draw_part_render:SetIsGray(false)
    if self.multi_color ~= nil then
        self.draw_part_render:SetIsMultiColor(self.multi_color)
    end

    self:SetCahceRendererRenderingLayerMask()
end




----[[
function DrawPart:GetChangeSkinComponent()
    if self.obj == nil or IsNil(self.obj.gameObject) then
        return
    end

    if not self.change_skin_comp then
        self.change_skin_comp = self.obj.gameObject:GetComponent(typeof(ChangeSkin))
    end
    return self.change_skin_comp
end

----- 身体部位使用高模（应用场景创角）
function DrawPart:InitSkinPartLoadHighList()
    self.skin_load_high_list = {
        [ROLE_SKIN_TYPE.BODY] = false,
        [ROLE_SKIN_TYPE.FACE] = false,
        [ROLE_SKIN_TYPE.HAIR] = false,
    }
end

function DrawPart:SetSkinPartLoadHigh(part, is_high)
    self.skin_load_high_list[part] = is_high
end

function DrawPart:GetSkinPartLoadHigh(part)
    return self.skin_load_high_list[part]
end


function DrawPart:ResetDrawModelInfo()
    self.draw_model_type = nil
    self.extra_model_data = nil
    -- self.is_need_all_draw_model_loaded = true

    self:ReleaseDrawModelObj()
end

function DrawPart:ReleaseDrawModelObj()
    if self.draw_model_list then
        for k,v in pairs(self.draw_model_list) do
            if v.res_prefab ~= nil then
                ResPoolMgr:Release(v.res_prefab)
            end
        end
        self.draw_model_list = nil
    end
end

function DrawPart:ChangeAllDrawModel()
    if SceneObjPart.Main ~= self.part or not self.extra_model_data then
        return
    end

    local function getResid(model_type, sub_type, data)
        local res_id
        if model_type == DRAW_MODEL_TYPE.ROLE then
            if sub_type == ROLE_SKIN_TYPE.BODY then
                res_id = data.role_body_res
            elseif sub_type == ROLE_SKIN_TYPE.FACE then
                res_id = data.role_face_res
            elseif sub_type == ROLE_SKIN_TYPE.HAIR then
                res_id = data.role_hair_res
            end
        end

        return res_id
    end

    if self.draw_model_type == DRAW_MODEL_TYPE.ROLE then
        for k,v in pairs(ROLE_SKIN_TYPE) do
            local res_id = getResid(self.draw_model_type, v, self.extra_model_data)
            self:ChangeDrawModel(v, res_id, function()
                if self.load_skin_callback then
                    self.load_skin_callback(v)
                end
            end)
        end
    end
end

function DrawPart:ChangeDrawModel(sub_type, res_id, callback)
    if res_id == nil then return end

    local data = self:GetDrawModelListData(sub_type)
    if not data then return end

    local visible = self.is_visible
    if not visible or self.loading then
        data.res_id = res_id
        data.allback = callback
        data.is_init = false
        return
    end

    if self.obj == nil then
        return
    end

    if data.res_id == res_id then
        if self.draw_model_type == DRAW_MODEL_TYPE.ROLE then
            if sub_type == ROLE_SKIN_TYPE.HAIR then
                self:UpdateChangeSkinHairData()
            elseif sub_type == ROLE_SKIN_TYPE.FACE then
                self:UpdateChangeSkinFaceData()
            end
        end

        data.callback = nil
        if callback then
            callback()
        end

        return
    end

    data.res_id = res_id
    data.callback = callback
    data.main_bundle = self.asset_bundle
    data.main_asset = self.asset_name

    self:ChangeDrawModelByData(data)
end

local function ChangeDrawModelCallBack(prefab, cbdata)
    local self = cbdata[1]
    local change_data = cbdata[2]
    DrawPart.ReleaseCBData(cbdata)

    if prefab == nil or not change_data then
        return
    end

    local skin = self:GetChangeSkinComponent()
    if IsNil(skin) then
        return
    end

    -- 处理 异步加载导致旧数据后加载
    local res_name = prefab.transform.name or ""
    if res_name ~= tostring(change_data.res_id) or change_data.main_bundle ~= self.asset_bundle or change_data.main_asset ~= self.asset_name then
        ResPoolMgr:Release(prefab)
        return
    end

    if change_data.res_prefab ~= nil and not IsNil(change_data.res_prefab.gameObject) then
        ResPoolMgr:Release(change_data.res_prefab)
        change_data.res_prefab = nil
    end

    change_data.res_prefab = prefab
    change_data.is_init = true
    if not IsNil(prefab.gameObject) then
        if change_data.draw_model_type == DRAW_MODEL_TYPE.ROLE then
            local actor_render = prefab:GetComponent(typeof(ActorRender))
            if not IsNil(actor_render) then
                if change_data.sub_type == ROLE_SKIN_TYPE.BODY then
                    self.now_cache_body_render = actor_render
                    skin:ChangeBody(actor_render)
                elseif change_data.sub_type == ROLE_SKIN_TYPE.HAIR then
                    self.now_cache_hair_render = actor_render
                    skin:ChangeHair(actor_render)
                    self:UpdateChangeSkinHairData()
                elseif change_data.sub_type == ROLE_SKIN_TYPE.FACE then
                    self.now_cache_face_render = actor_render
                    skin:ChangeFaceAndEyeball(actor_render)
                    self:UpdateChangeSkinFaceData()
                end
            end

            if change_data.callback then
                change_data.callback()
                change_data.callback = nil
            end
        end
    end

    self:OnDrawModelAllLoaded()
end

function DrawPart:ChangeDrawModelByData(change_data)
    if not change_data then
        return
    end

    local bundle_name, asset_name
    local sub_type = change_data.sub_type
    local extra_data = self.extra_model_data

    if change_data.draw_model_type == DRAW_MODEL_TYPE.ROLE then
        local path_func, high_path_func
        if sub_type == ROLE_SKIN_TYPE.BODY then
            path_func = ResPath.GetBodyModel
            high_path_func = ResPath.GetBodyHighModel

            if extra_data and extra_data.is_realm then
                path_func = ResPath.GetRealmBodyModel
            end
        elseif sub_type == ROLE_SKIN_TYPE.HAIR then
            high_path_func = ResPath.GetHairHighModel
            path_func = ResPath.GetHairModel

            if extra_data and extra_data.is_realm then
                path_func = ResPath.GetRealmHairModel
            end
        elseif sub_type == ROLE_SKIN_TYPE.FACE then
            high_path_func = ResPath.GetFaceHighModel
            path_func = ResPath.GetFaceModel

            if extra_data and extra_data.is_realm then
                path_func = ResPath.GetRealmFaceModel
            end
        end

        if self:GetSkinPartLoadHigh(sub_type) then
            bundle_name, asset_name = high_path_func(change_data.res_id)
        else
            bundle_name, asset_name = path_func(change_data.res_id)
        end
    end

    local cbdata = DrawPart.GetCBData()
    cbdata[1] = self
    cbdata[2] = change_data

    local prefab = ResPoolMgr:GetPrefab(bundle_name, asset_name, ChangeDrawModelCallBack, true, cbdata)
end

function DrawPart:OnDrawModelAllLoaded()
    if not self:GetDrawModelTypeListAllInit(self.draw_model_type) then
        return
    end

    if self.obj == nil or IsNil(self.obj.gameObject) then
        return
    end

    if not self.is_need_all_draw_model_loaded then
        if self.load_complete then
            self.load_complete(self.draw_obj, self.obj, self.part, self, self.asset_bundle, self.asset_name)
        end
    
        if self.load_callback then
            self.load_callback(self.obj)
            self.load_callback = nil
        end
        return
    end

    self.obj:SetActive(self:GetVisible())

    self:SetDrawPartRenderParam()
    local is_sync_anim_part = self.draw_obj:GetIsWaitSyncAnimPart(self.part)
    if (not is_sync_anim_part) or (is_sync_anim_part and self.draw_obj.wait_sync_anim_data == nil) then
        self:CheckAniPlay()
    end

    self:FlushAllAnimatorParameters()
    self:AddOcclusion()

    if self.load_complete then
        self.load_complete(self.draw_obj, self.obj, self.part, self, self.asset_bundle, self.asset_name)
    end

    if self.load_callback then
        self.load_callback(self.obj)
        self.load_callback = nil
    end

    self.is_need_all_draw_model_loaded = false
end
--]]









----[[ 动作、动画
function DrawPart:GetIsCurrentAnimatorStateName(name)
    if name and self.obj ~= nil and not IsNil(self.obj.animator) then
        return self.obj.animator:GetCurrentAnimatorStateInfo(0):IsName(name)
    end

    return false
end

function DrawPart:GetIsNotCurrentAnimatorStateName(name)
    if name and self.obj ~= nil and not IsNil(self.obj.animator) then
        return not self.obj.animator:GetCurrentAnimatorStateInfo(0):IsName(name)
    end

    return false
end

function DrawPart:SetAnimatorEnabled(state)
    if self.obj ~= nil and not IsNil(self.obj.animator) then
        self.obj.animator.enabled = state
    end
end

-- param: had animator, animator enabled
function DrawPart:GetAnimatorEnabled()
    if self.obj ~= nil and not IsNil(self.obj.animator) then
        return true, self.obj.animator.enabled
    end

    return false, false
end

-- 动画 动画播放的更新
function DrawPart:SetIsAlwaysAnimate(is_always_animate)
    self.is_always_animate = is_always_animate
    self:UpdateAnimatorAlwaysAnimate()
end

-- 动画 动画播放的更新
function DrawPart:UpdateAnimatorAlwaysAnimate()
    if self.obj == nil or IsNil(self.obj.animator) then
        return
    end

    if self.is_always_animate then
        self.old_animate_culling_mode = self.old_animate_culling_mode or self.obj.animator.cullingMode
        self.obj.animator.cullingMode = AnimatorCullingMode.AlwaysAnimate
    elseif self.old_animate_culling_mode then
        self.obj.animator.cullingMode = self.old_animate_culling_mode
        self.old_animate_culling_mode = nil
    end
end

function DrawPart:SetAnimatorSpeed(value)
    if self.obj ~= nil and not IsNil(self.obj.animator) then
        self.obj.animator.speed = value
    end
end

function DrawPart:ResetAnimatorSpeed()
    if self.obj ~= nil and not IsNil(self.obj.animator) then
        self.obj.animator.speed = 1
    end
end

function DrawPart:SetLayer(layer, value)
end

function DrawPart:ClearCachePlayAnim()
    self.cur_play_anim = nil
end

function DrawPart:Play(key, layer, value)
    -- print_error("---DrawPart:Play----", SceneObjPartStr[self.part], key, layer, value)

    if nil == self.animator_plays then
        self.animator_plays = {}
    end

    self.animator_plays[key] = {layer = layer, value = value}
    if self.obj ~= nil and not IsNil(self.obj.animator) then
        self.obj.animator:Play(key, layer, value)
    end
end

function DrawPart:CheckAniPlay()
    if self.obj ~= nil and self.cur_play_anim ~= nil then
        self:CrossFade(self.cur_play_anim, false, 0, true)
    end
end

function DrawPart:SetTrigger(key)
    -- print_error("---DrawPart:SetTrigger----", SceneObjPartStr[self.part], key)
    
    self.cur_play_anim = nil
    if self.obj ~= nil then
        if not IsNil(self.obj.animator) and self.obj.animator.isActiveAndEnabled then
            self.obj.animator:SetTrigger(key)
        end
    elseif self.is_visible then
        if nil == self.animator_triggers then
            self.animator_triggers = {}
        end
        self.animator_triggers[key] = true
    end
end

function DrawPart:ResetTrigger(key)
    if self.obj ~= nil then
        if not IsNil(self.obj.animator) and self.obj.animator.isActiveAndEnabled then
            self.obj.animator:ResetTrigger(key)
        end
    elseif self.is_visible then
        if nil ~= self.animator_triggers and self.animator_triggers[key] then
            self.animator_triggers[key] = nil
        end
    end
end

function DrawPart:GetBool(key)
    if self.obj ~= nil and not IsNil(self.obj.animator) and self.obj.animator.isActiveAndEnabled then
        return self.obj.animator:GetBool(key)
    end

    return nil
end

function DrawPart:SetBool(key, value)
    -- print_error("---DrawPart:SetBool----", SceneObjPartStr[self.part], key, value)

    if nil == self.animator_bools then
        self.animator_bools = {}
    end

    self.animator_bools[key] = value
    if self.obj ~= nil and not IsNil(self.obj.animator) and self.obj.animator.isActiveAndEnabled then
        self.obj.animator:SetBool(key, value)
    end
end

function DrawPart:SetInteger(key, value)
    -- print_error("---DrawPart:SetInteger----", SceneObjPartStr[self.part], key, value)

    if nil == self.animator_ints then
        self.animator_ints = {}
    end

    self.animator_ints[key] = value
    if self.obj ~= nil and not IsNil(self.obj.animator) and self.obj.animator.isActiveAndEnabled then
        self.obj.animator:SetInteger(key, value)
    end
end

function DrawPart:GetInteger(key)
    if self.obj ~= nil and not IsNil(self.obj.animator) and self.obj.animator.isActiveAndEnabled then
        return self.obj.animator:GetInteger(key)
    end
end

function DrawPart:SetFloat(key, value)
    -- print_error("---DrawPart:SetFloat----", SceneObjPartStr[self.part], key, value)

    if nil == self.animator_floats then
        self.animator_floats = {}
    end

    self.animator_floats[key] = value
    if self.obj ~= nil and not IsNil(self.obj.animator) and self.obj.animator.isActiveAndEnabled then
        self.obj.animator:SetFloat(key, value)
    end
end

function DrawPart:GetFloat(key)
    if self.obj ~= nil and not IsNil(self.obj.animator) and self.obj.animator.isActiveAndEnabled then
        return self.obj.animator:GetFloat(key)
    end
end

function DrawPart:ListenEvent(event_name, callback)
    self:UnListenEvent(event_name)

    if nil == self.animator_listener then
        self.animator_listener = {}
    end

    self.animator_listener[event_name] = callback
    if self.obj ~= nil and not IsNil(self.obj.animator) then
        if nil == self.animator_handle then
            self.animator_handle = {}
        end

        self.animator_handle[event_name] = self.obj.animator:ListenEvent(event_name, callback)
    end
end

function DrawPart:UnListenEvent(event_name)
    if nil == self.animator_listener then
        return
    end

    self.animator_listener[event_name] = nil

    if nil ~= self.animator_handle and nil ~= self.animator_handle[event_name] then
        self.animator_handle[event_name]:Dispose()
        self.animator_handle[event_name] = nil
    end
end

function DrawPart:FlushAllAnimatorParameters()
    if self.obj == nil then
        return
    end

    local animator = self.obj.animator
    if IsNil(self.obj.animator) or not animator.isActiveAndEnabled then
        return
    end

    if nil ~= self.animator_triggers then
        for k, v in pairs(self.animator_triggers) do
            animator:SetTrigger(k)
        end
        self.animator_triggers = nil
    end

    if nil ~= self.animator_bools then
        for k, v in pairs(self.animator_bools) do
            animator:SetBool(k, v)
        end
    end

    if nil ~= self.animator_ints then
        for k, v in pairs(self.animator_ints) do
            animator:SetInteger(k, v)
        end
    end

    if nil ~= self.animator_floats then
        for k, v in pairs(self.animator_floats) do
            animator:SetFloat(k, v)
        end
    end

    if nil ~= self.animator_listener then
        if nil == self.animator_handle then
            self.animator_handle = {}
        end

        for k, v in pairs(self.animator_listener) do
            if nil ~= self.animator_handle[k] then
                self.animator_handle[k]:Dispose()
                self.animator_handle[k] = nil
            end

            self.animator_handle[k] = self.obj.animator:ListenEvent(k, v)
        end
    end
end

-- 【为什么会有个这个么少的】
function DrawPart:CheckAniParam()
    if self.obj == nil then
        return
    end

    local animator = self.obj.animator
    if not IsNil(animator) then
        if nil ~= self.animator_bools then
            for k, v in pairs(self.animator_bools) do
                animator:SetBool(k, v)
            end
        end

        if nil ~= self.animator_ints then
            for k, v in pairs(self.animator_ints) do
                animator:SetInteger(k, v)
            end
        end

        if nil ~= self.animator_floats then
            for k, v in pairs(self.animator_floats) do
                animator:SetFloat(k, v)
            end
        end
    end
end

function DrawPart:PlayAction(action_name)
    if self.obj ~= nil then
        if not IsNil(self.obj.animator) and self.obj.animator.isActiveAndEnabled then
            self.obj.animator:Play(action_name)
        end
    end
end

function DrawPart:GetClipLength(animation)
    if not self.obj or IsNil(self.obj.animator) or not self.obj.animator.runtimeAnimatorController then
        return 0.1
    end

    local clip = self.obj.animator:GetAnimationClip(animation)
    if clip then
        return clip.length or 0.1
    end

    return 0.1
end

-- local filter_anim = {
    -- ["idle"] = true,
    -- ["run"] = true,
-- }

function DrawPart:CrossFade(anima_name, check_same_action, time, is_force)
    if not anima_name then
        return
    end

    -- if self.part == SceneObjPart.Main and not filter_anim[anima_name] and not self.is_main_role then
    --     print_error("-----CrossFade----", self.obj == nil, SceneObjPartStr[self.part], anima_name)
    -- end

    if self.obj == nil then -- and or self.delay_cross_timer (self.draw_obj == nil or not self.draw_obj:GetIsRoleModel()) then
        self.cur_play_anim = anima_name
        self.fixed_transition_duration = time
        return
    end

    --界面模型播放动画时，不做延迟处理
    -- if self.draw_obj and self.draw_obj:GetIsRoleModel() then
    --     GlobalTimerQuest:CancelQuest(self.delay_cross_timer)
    --     self.delay_cross_timer = nil
    -- end

    if self.is_main_role then
        local mian_role = Scene.Instance and Scene.Instance:GetMainRole() or nil
        -- 这里不能判mian_role空后return， 这会导致类似死亡立即复活这种，少执行动作
        if mian_role and (mian_role:IsQingGong() and not mian_role:IsOnGround()) then
            return
        end
    end

    local can_play = true
    if check_same_action ~= nil then
        if check_same_action then
            if self.cur_play_anim ~= anima_name then
                can_play = false
            end
        end
    else
        if self.cur_play_anim == anima_name then
            can_play = false
        end
    end

    if is_force then
        can_play = true
    end

    -- if self.part == SceneObjPart.Main and not filter_anim[anima_name] and self.is_main_role then
    --     print_error("-----CrossFadeInFixedTime111----", SceneObjPartStr[self.part], anima_name, time, can_play, not IsNil(self.obj.animator) and self.obj.animator.isActiveAndEnabled)
    -- end

    if not can_play then
        return
    end

    if self.obj ~= nil and not IsNil(self.obj.animator) and self.obj.animator.isActiveAndEnabled then
        time = time or Action_Time

        -- 去除UI 模型播动画的过渡时间
        if self.draw_obj:GetIsRoleModel() then
            time = 0
        end

        self.fixed_transition_duration = time

        -- if anima_name == "idle"
        -- and self.obj.animator:GetBool("fight")
        -- and self.obj.animator:HasState(ANIMATOR_PARAM.BASE_LAYER, ANIMATOR_PARAM.Fight_Idle) then
        --     anima_name = "fight_idle"
        -- end

        self.cur_play_anim = anima_name
        -- if self.part == SceneObjPart.Main and not filter_anim[anima_name] and self.is_main_role then
            -- print_error("-----CrossFadeInFixedTime222----", SceneObjPartStr[self.part], anima_name, time)
        -- end

        self.obj.animator:CrossFadeInFixedTime(anima_name, time)
    else
        self.cur_play_anim = anima_name
        self.fixed_transition_duration = time
    end
end
-- 动作、动画 end]]


----[[ 设置 RenderingLayerMask
    function DrawPart:SetCahceRendererRenderingLayerMask()
        for k,v in pairs(self.set_rlm_list) do
            self:SetRendererRenderingLayerMask(k, v)
        end
    end

    -- 设置遮罩层layer开关
    function DrawPart:SetRendererRenderingLayerMask(layer, isOpen)
        self.set_rlm_list[layer] = isOpen
        self.draw_part_render:SetRendererRenderingLayerMask(layer, isOpen)
    end

    -- 设置遮罩层layer开关 反射层layer 反射水面用
    function DrawPart:SetLayerMaskReflectionSwitch(isOpen)
        self:SetRendererRenderingLayerMask(RenderingLayerMaskType.Reflection, isOpen)
    end
--]]


----[[ ActorAttachment
function DrawPart:EnableMountUpTrigger(enabled)
    self.enable_mount_up = enabled
    if self.obj ~= nil then
        local attachment = self.obj.actor_attachment
        if attachment ~= nil then
            attachment:SetMountUpTriggerEnable(enabled)
        end
    end
end

function DrawPart:GetEnabelMountUp()
    return self.enable_mount_up
end

function DrawPart:GetAttachPoint(point)
    if self.obj == nil or IsNil(self.obj.gameObject) then
        return nil
    end

    local attachment = self.obj.actor_attachment
    if attachment == nil then
        return nil
    end

    return attachment:GetAttachPoint(point)
end
-- ActorAttachment end]]





-- 溶解效果
function DrawPart:PlayDissolveEffect()
    if self.draw_part_render == nil then
        return
    end

    self.draw_part_render:PlayDissolveEffect()
end





----[[ 点击监听
function DrawPart:ListenClick(listener)
    self.click_listener = listener
    if self.obj ~= nil then
        self:_FlushClickListener(self.obj)
    end
end

function DrawPart:OnClickListener()
    if self.click_listener ~= nil then
        self.click_listener()
    end
end

function DrawPart:_FlushClickListener(obj)
    local clickable = obj.clickable_obj
    if clickable == nil then
        return
    end

    if self.click_listener ~= nil then
        clickable:SetClickListener(self.click_listener)
        clickable:SetClickable(true)
    else
        clickable:SetClickListener(nil)
        clickable:SetClickable(false)
    end
end

function DrawPart:SetClickableObjLocalPos(pos)
    if self.obj == nil then
        return
    end

    local clickable = self.obj.clickable_obj
    if clickable == nil then
        return
    end

    clickable.gameObject.transform.localPosition = pos
end
-- 点击监听 end]]




----[[ LOD
function DrawPart:SetForceLodMaxLevel(is_max)
    self.is_force_max_lod = is_max
    if self.obj ~= nil and not IsNil(self.obj.lod_group) then
        if is_max then
            self.obj.lod_group:ForceLOD(0)
        else
            self.obj.lod_group:ForceLOD(-1)
        end
    end
end

function DrawPart:CancleForceLodMaxLevel()
    self.is_force_max_lod = false
    if self.obj ~= nil and not IsNil(self.obj.lod_group) then
        self.obj.lod_group:ForceLOD(-1)
    end
end
-- LOD end]]




----[[ 遮挡剔除
-- 遮挡剔除 Remove
function DrawPart:RemoveOcclusion()
    if self:IsCanAddOcclusion() then
        self:RemoveOcclusionMaterial(self:GetSkinnedMeshRenderers(), false)
        if self.quality_change_handle ~= nil then
            QualityConfig.UnlistenQualtiy(self.quality_change_handle)
            self.quality_change_handle = nil
        end
    end
end

-- 遮挡剔除 add
function DrawPart:AddOcclusion()
    if self:IsCanAddOcclusion() then
        self:AddOcclusionMaterial(self:GetSkinnedMeshRenderers(), false)

        if nil == self.quality_change_handle then
             self.quality_change_handle = QualityConfig.ListenQualityChanged(function()
                self:RemoveOcclusionMaterial(self:GetSkinnedMeshRenderers(), false)
                self:AddOcclusionMaterial(self:GetSkinnedMeshRenderers(), false)
            end)
        end
    end
end

-- 遮挡剔除
-- 只有主角身上的基础部位才加。加得越多，DC越多，绝对控制好
function DrawPart:IsCanAddOcclusion()
    -- if self.is_main_role and self.obj then
    --     if SceneObjPart.Main == self.part or SceneObjPart.Mount == self.part or self.part == SceneObjPart.FightMount then
    --        return true
    --     end
    -- end

    return false
end

-- 遮挡剔除
function DrawPart:AddOcclusionMaterial(renderers, skip_particle_system)
    if renderers ~= nil then
        for i = 0, renderers.Length - 1 do
            local renderer = renderers[i]
            self:AddMaterial(renderer, ResPreload.role_occlusion, skip_particle_system)
        end
    end
end

-- 遮挡剔除 移除材质
function DrawPart:RemoveOcclusionMaterial(renderers, skip_particle_system)
    if renderers ~= nil then
        for i = 0, renderers.Length - 1 do
            local renderer = renderers[i]
            self:RemoveMaterial(renderer, ResPreload.role_occlusion, skip_particle_system)
        end
    end
end

function DrawPart:AddMaterial(renderer, occlusion_material, skip_particle_system)
    if nil == self.add_occlusion_material_list then
        self.add_occlusion_material_list = {}
    end

    if nil ~= renderer and not self.add_occlusion_material_list[renderer] and not IsNil(renderer.gameObject) then
        if not skip_particle_system or renderer:GetComponent(UnityParticleSystem) == nil then
            -- 影子不添加
            local obj_name = renderer.gameObject.name
            if obj_name == "ObjShadow" then
                return
            end

            local materials = MaterialMgr.Instance:GetClonedMaterials(renderer)
            if nil == materials then
                return
            end

            local new_materials = {}
            local len = materials.Length

            if len > 0 then
                for j = 0, len - 1 do
                    local material = materials[j]
                    if material then
                        table.insert(new_materials, material)
                        if material.renderQueue < AlphaTestQueue then
                            material.renderQueue = GeometryRendererQueue + 2
                        end
                    end
                end

                table.insert(new_materials, occlusion_material)
                renderer.materials = new_materials
                self.add_occlusion_material_list[renderer] = true
            end
        end
    end
end

function DrawPart:RemoveMaterial(renderer, occlusion_material, skip_particle_system)
    if nil == self.add_occlusion_material_list then
        return
    end

    if nil ~= renderer and not self.add_occlusion_material_list[renderer] and not IsNil(renderer.gameObject) then
        if not skip_particle_system or renderer:GetComponent(UnityParticleSystem) == nil then
            local materials = MaterialMgr.Instance:GetClonedMaterials(renderer)
            if nil == materials then
                return
            end

            local new_materials = {}
            local len = materials.Length

            if len > 1 then
                for j = 0, len - 2 do
                    local material = materials[j]
                    if material then
                        table.insert(new_materials, material)
                    end
                end

                renderer.materials = new_materials
                self.add_occlusion_material_list[renderer] = nil
            end
        end
    end
end
--]]


----[[ 动态骨骼
function DrawPart:CreateDynamicBone(sex_type)
    self:RemoveDynamicBone()

    if self.obj == nil or IsNil(self.obj.gameObject) then
        return
    end
    
    if IsNil(self.dynamic_bone) then
        self.dynamic_bone = self.obj.gameObject:AddComponent(typeof(DynamicBone))
    end

    if nil ~= self.dynamic_bone then
        self.dynamic_bone:InitDynamicBone(sex_type == 0 and RoleType.Woman or RoleType.Man)
    end
end

function DrawPart:RemoveDynamicBone()
    if not IsNil(self.dynamic_bone) then
        GameObject.DestroyImmediate(self.dynamic_bone)
        self.dynamic_bone = nil
    end
end
--]]






----[[ 淡出
function DrawPart:PlayFade(is_fade, a)
    if is_fade == self.is_show_fade then
        return
    end

    if not is_fade or self.obj == nil or IsNil(self.obj.gameObject) then
        self:ResetFade()
        self:AddOcclusion()
        return
    end

    self.is_show_fade = is_fade
    a = a or 0.5
    local renderers = self:GetSkinnedMeshRenderers()
    if renderers == nil or renderers.Length == 0 then
        return
    end

    self:RemoveOcclusion()
    for i = 0, renderers.Length - 1 do
        local renderer = renderers[i]
        local materials = MaterialMgr.Instance:GetClonedMaterials(renderer)
        if nil == materials then
            return
        end

        local len = materials.Length
        if len >= 1 then
            for j = 0, len - 1 do
                local material = materials[j]
                if material then
                    material:SetFloat("_SrcBlend", 5)
                    material:SetFloat("_DstBlend", 10)
                    material:SetFloat("_ZWrite", 1)
                    material:SetFloat("_CullMode", 2)
                    material.shaderKeywords = {"ENABLE_MAIN_TEX_USE_CHANNEL_RGB", "ENABLE_ALPHA_BLEND"}
                    if material:HasProperty("_MainColor") then
                        local color = material:GetColor("_MainColor")
                        color.a = a
                        material:SetColor("_MainColor", color)
                    else
                        local color = material:GetColor("_Color")
                        color.a = a
                        material:SetColor("_Color", color)
                    end
                end
            end
        end
    end
end

function DrawPart:ResetFade()
    local state = self.is_show_fade
    self.is_show_fade = false
    if state and self.obj ~= nil and not IsNil(self.obj.gameObject) then
        local renderers = self:GetSkinnedMeshRenderers()
        if renderers == nil or renderers.Length == 0 then
            return
        end

        for i = 0, renderers.Length - 1 do
            local renderer = renderers[i]
            MaterialMgr.Instance:ResumeSharedMaterials(renderer)
        end
    end

    if self.draw_part_render then
        self.draw_part_render:SetMaterialQuality(self.material_quality)
    end
end
--]]




----[[ 其他效果
function DrawPart:SetIsMultiColor(multi_color)
    self.multi_color = multi_color
    self.draw_part_render:SetIsMultiColor(multi_color)
end

function DrawPart:SetIsGray(value)
    self.draw_part_render:SetIsGray(value)
end

function DrawPart:SetIsLerpProbe(is_lerp, is_force)
    self.is_lerp_probe = is_lerp
    if is_force and self.draw_part_render ~= nil then
        self.draw_part_render:TrySetLerpProbe(is_lerp, true)
    end
end
--]]




----[[ 位置、尺寸
function DrawPart:SetOffsetY(offset)
    self.offset_y = offset or 0
    if self.obj ~= nil then
        self.obj_transform.localPosition = u3dpool.vec3(self.obj_transform.localPosition.x,
        self.offset_y, self.obj_transform.localPosition.z)
    end
end

function DrawPart:ReSetOffsetY()
    if self.obj ~= nil then
        self.obj_transform.localPosition = u3dpool.vec3(self.obj_transform.localPosition.x,
        0, self.obj_transform.localPosition.z)
    end

    self.offset_y = 0
end

function DrawPart:CheckIsNeedResetY()
    if self.offset_y ~= 0 then
        self:ReSetOffsetY()
    end
end

function DrawPart:SetRotateX(x)
    self.set_ro = x
    if self.obj ~= nil then
        self.obj_transform.localRotation = Quaternion.Euler(self.set_ro, self.part_rotate.y, self.part_rotate.z)
    end
end

function DrawPart:ReSetRotateX()
    if self.obj ~= nil then
        self.obj_transform.localRotation = self.part_rotate
    end

    self.set_ro = nil
end

function DrawPart:SetRotation(rotation)
    self.part_rotate = rotation
    self:ReSetRotateX()
end

function DrawPart:SetPosition(position)
    if self.obj ~= nil then
        self.obj_transform.localPosition = u3dpool.vec3(position.x, position.y, position.z)
        self.offset_y = position.y
    end
end

function DrawPart:CheckIsNeedResetRoatateX()
    if self.set_ro ~= nil then
        self:ReSetRotateX()
    end
end

function DrawPart:SetPartScale(x, y, z)
    self.set_scale = {x = x, y = y, z = z}
    if self.obj ~= nil then
        Transform.SetLocalScaleXYZ(self.obj.transform, self.set_scale.x, self.set_scale.y, self.set_scale.z)
    end
end

function DrawPart:ReSetPartScale()
    if self.obj ~= nil then
        Transform.SetLocalScaleXYZ(self.obj.transform, self.part_scale.x, self.part_scale.y, self.part_scale.z)
    end

    self.set_scale = nil
end

function DrawPart:CheckIsNeedResetPartScale()
    if self.set_scale ~= nil then
        self:ReSetPartScale()
    end
end

-- 修改自身的物体渲染层级
function DrawPart:TrySetMainGameObjectLayer(layer)
    if self.draw_part_render then
        self.draw_part_render:TrySetMainGameObjectLayer(layer)
    end
end

-- 还原自身的物体渲染层级
function DrawPart:TrySetMainDefaultGameObjectLayer()
    if self.draw_part_render then
        self.draw_part_render:TrySetMainDefaultGameObjectLayer()
    end
end
--]]



----[[ 特殊 节点
function DrawPart:GetAdjustPoint()
	return self.adjust_pint
end

function DrawPart:GetMountFaZhenPoint()
    return self.mount_fazhen_point_obj
end
--]]


----[[改角色变头部数据
---===============================================================
---===============================================================
---===============================================================
---
---改变单个头部数据
--- type = ChangeSkin.HeadCustomizationType
function DrawPart:ChangeHeadSingleCustomization(type, val)
    if self.part ~= SceneObjPart.Main then return end
    local change_skin = self:GetChangeSkinComponent()
    if IsNil(change_skin) then return end
    if HeadCustomizationType.FaceDecalTex == type then
        self:SetHeadFaceDecalTex(type, val)
    elseif HeadCustomizationType.PupilType_Left == type then
        self:SetHeadPupilTex(type, val)
    elseif HeadCustomizationType.PupilType_Right == type then
        self:SetHeadPupilTex(type, val)
    else
        change_skin:SetHeadCustomization(type, val)
    end
end

function DrawPart:SetHeadFaceDecalTex(type, val)
    local data = self.extra_model_data
    if not data then return end

    local batch_data = {}
    if data.face_decal_id then
        local cfg = RoleDiyAppearanceWGData.Instance:GetFaceDecalByIdCfg(data.face_decal_id)
        if cfg then
            local sex = data.sex or 0
            local st_value = sex == 1 and cfg.nan_tex_st_value or cfg.nv_tex_st_value
            local fade_value = sex == 1 and cfg.nan_fade_value or cfg.nv_fade_value
            local mirror_value = sex == 1 and cfg.nan_mirror_value or cfg.nv_mirror_value
            local st_value_list = string.split(st_value, "|")

            batch_data[HeadCustomizationType.FaceDecalTex] = val
            batch_data[HeadCustomizationType.FaceDecalTexST] = Vector4(st_value_list[1] or 0, st_value_list[2] or 0, st_value_list[3] or 0, st_value_list[4] or 0)
            batch_data[HeadCustomizationType.FaceDecalFade] = fade_value
            batch_data[HeadCustomizationType.FaceDecalMirror] = mirror_value  
        end
    end

    -- 批量处理
    if not IsEmptyTable(batch_data) then
        local change_skin = self:GetChangeSkinComponent()
        if not IsNil(change_skin) then
            change_skin:BatchSetHeadCustomization(batch_data)
        end
    end
end

function DrawPart:SetHeadPupilTex(type, val)
    local data = self.extra_model_data
    if not data then return end

    local batch_data = {}

    if HeadCustomizationType.PupilType_Left == type then
        batch_data[HeadCustomizationType.PupilType_Left] = val

        if data.left_pupil_size then
            batch_data[HeadCustomizationType.PupilSize_Left] = data.left_pupil_size
        end

        if data.left_pupil_color then
            batch_data[HeadCustomizationType.PupilColor_Left] = DrawPart.RGBATabToColor(data.left_pupil_color)
        end
    elseif HeadCustomizationType.PupilType_Right == type then
        batch_data[HeadCustomizationType.PupilType_Right] = val
        
        if data.right_pupil_size then
            batch_data[HeadCustomizationType.PupilSize_Right] = data.right_pupil_size
        end

        if data.right_pupil_color then
            batch_data[HeadCustomizationType.PupilColor_Right] = DrawPart.RGBATabToColor(data.right_pupil_color)
        end
    end

    -- 批量处理
    if not IsEmptyTable(batch_data) then
        local change_skin = self:GetChangeSkinComponent()
        if not IsNil(change_skin) then
            change_skin:BatchSetHeadCustomization(batch_data)
        end
    end
end

---改变单个头部type数据 - 材质颜色rgba
------ type = ChangeSkin.HeadCustomizationType
function DrawPart:ChangeSkinHeadMatColorByRGBA(type, r, g, b, a)
    a = a or 255
    if self.part ~= SceneObjPart.Main or not r or not g or not b then
        return
    end

    local color = Color.New(r / 255, g / 255, b / 255, a / 255)
    self:ChangeHeadSingleCustomization(type, color)
end

function DrawPart.RGBATabToColor(rgba_table)
    if not rgba_table then return nil end

    local r = rgba_table.r or 255
    local g = rgba_table.g or 255
    local b = rgba_table.b or 255
    local a = rgba_table.a or 255
    return Color.New(r / 255, g / 255, b / 255, a / 255)
end

---改变单个头部type数据 - 材质颜色rgba
------ type = ChangeSkin.HeadCustomizationType
function DrawPart:ChangeSkinHeadMatColorByRGBATable(type, rgba_table)
    if self.part ~= SceneObjPart.Main then
        return
    end
    
    local color = DrawPart.RGBATabToColor(rgba_table)
    if not color then return end
    self:ChangeHeadSingleCustomization(type, color)
end

---改变单个头部type数据 - 材质颜色convert hex
------ type = ChangeSkin.HeadCustomizationType
function DrawPart:ChangeSkinHeadMatColorByHex(type, hex)
    if self.part ~= SceneObjPart.Main or not hex then return end
    local color = UtilU3d.ConvertHexToColor(hex)
    self:ChangeHeadSingleCustomization(type, color)
end

local function LoadChangeSkinHeadTexCallBack(texture, cbdata)
    local self = cbdata[1]
    local type = cbdata[2]
    local callback = cbdata[3]
    local special_param = cbdata[4]
    DrawPart.ReleaseCBData(cbdata)

    if texture == nil or not type then
        return
    end

    if callback then
        callback(type, texture)
        if special_param and type == HeadCustomizationType.PupilType_Left then
            callback(HeadCustomizationType.PupilType_Right, texture)
        end
    end
end

-- local TypeUnityTexture = typeof(UnityEngine.Texture)
---改变单个头部type数据 - 材质贴图
------ type = ChangeSkin.HeadCustomizationType
function DrawPart:ChangeSkinHeadMatTex(type, tex_bundle, tex_asset, special_param)
    if self.part ~= SceneObjPart.Main or not tex_bundle or not tex_asset then
        return
    end
    -- local loader = AllocResSyncLoader(self, string.format("%s#%s", tex_bundle, tex_asset))
    -- loader:Load(tex_bundle, tex_asset, TypeUnityTexture,
    --     function(texture)
    --         if texture ~= nil then
    --             self:ChangeHeadSingleCustomization(type, texture)
    --         end
    --     end
    -- )

    local cbdata = DrawPart.GetCBData()
    cbdata[1] = self
    cbdata[2] = type
    cbdata[3] = function (cb_type, tex)
                    self:ChangeHeadSingleCustomization(cb_type, tex)
                end
    cbdata[4] = special_param
    local texture = ResPoolMgr:GetTexture(tex_bundle, tex_asset, LoadChangeSkinHeadTexCallBack, true, cbdata)
end

---改变单个头部type数据 - 材质数值
function DrawPart:ChangeSkinHeadMatVal(type, val)
    if self.part ~= SceneObjPart.Main or not val then
        return
    end

    self:ChangeHeadSingleCustomization(type, val)
end

---改变单个头部type数据 - BlendShape
function DrawPart:ChangeSkinHeadRendererBlendShape(type, val)
    if self.part ~= SceneObjPart.Main or not val then
        return
    end

    self:ChangeHeadSingleCustomization(type, val)
end

-- 
function DrawPart:UpdateChangeSkinFaceData()
    local data = self.extra_model_data
    if not data then return end

    local batch_data = {}
    if data.eye_size then
        batch_data[HeadCustomizationType.EyeSize] = data.eye_size
    end

    if data.eye_position then
        batch_data[HeadCustomizationType.EyePosition] = data.eye_position
        batch_data[HeadCustomizationType.EyeballPosition] = data.eye_position
    end

    if data.eye_angle then
        batch_data[HeadCustomizationType.EyeAngle] = data.eye_angle
    end

    if data.eye_close then
        batch_data[HeadCustomizationType.EyeClose] = data.eye_close
    end

    if data.eyebrow_angle then
        batch_data[HeadCustomizationType.EyebrowAngle] = data.eyebrow_angle
    end

    if data.nose_size then
        batch_data[HeadCustomizationType.NoseSize] = data.nose_size
    end

    if data.nose_angle then
        batch_data[HeadCustomizationType.NoseAngle] = data.nose_angle
    end

    if data.mouth_size then
        batch_data[HeadCustomizationType.MouthSize] = data.mouth_size
    end

    if data.mouth_position then
        batch_data[HeadCustomizationType.MouthPosition] = data.mouth_position
    end

    if data.mouth_angle then
        batch_data[HeadCustomizationType.MouthAngle] = data.mouth_angle
    end

    if data.cheek_size then
        batch_data[HeadCustomizationType.CheekSize] = data.cheek_size
    end

    if data.chin_length then
        batch_data[HeadCustomizationType.ChinLength] = data.chin_length
    end

    if data.eye_shadow_color then
        batch_data[HeadCustomizationType.EyeShadowColor] = DrawPart.RGBATabToColor(data.eye_shadow_color)
    end

    if data.mouth_color then
        batch_data[HeadCustomizationType.MouthColor] = DrawPart.RGBATabToColor(data.mouth_color)
    end

    if data.left_iris_size then
        batch_data[HeadCustomizationType.IrisSize_Left] = data.left_iris_size
    end

    if data.right_iris_size then
        batch_data[HeadCustomizationType.IrisSize_Right] = data.right_iris_size
    end

    -- 批量处理
    if not IsEmptyTable(batch_data) then
        local change_skin = self:GetChangeSkinComponent()
        if not IsNil(change_skin) then
            change_skin:BatchSetHeadCustomization(batch_data)
        end
    end

    -- 贴图需要被从AB加载后再传参，不能走批量处理
    -- 瞳孔材质相同，赋值左眼即可，右眼可不赋值
    local is_same_pupil_asset = true
    if data.left_pupil_type then
        if data.right_pupil_type and data.left_pupil_type ~= data.right_pupil_type then
            is_same_pupil_asset = false
        end

        local left_pupil_type_bundle, left_pupil_type_asset = ResPath.GetPupilTypeImg(data.left_pupil_type)
        if left_pupil_type_bundle and left_pupil_type_asset then
            self:ChangeSkinHeadMatTex(HeadCustomizationType.PupilType_Left, left_pupil_type_bundle, left_pupil_type_asset, is_same_pupil_asset)
        end
    end

    if not is_same_pupil_asset and data.right_pupil_type then
        local right_pupil_type_bundle, right_pupil_type_asset = ResPath.GetPupilTypeImg(data.right_pupil_type)
        if right_pupil_type_bundle and right_pupil_type_asset then
            self:ChangeSkinHeadMatTex(HeadCustomizationType.PupilType_Right, right_pupil_type_bundle, right_pupil_type_asset)
        end
    end

    if data.face_decal_id then
        local sex = data.sex or 0
        local face_decal_bundle, face_decal_asset = ResPath.GetFaceDecalImg(data.face_decal_id, sex)
        if face_decal_bundle and face_decal_asset then
            self:ChangeSkinHeadMatTex(HeadCustomizationType.FaceDecalTex, face_decal_bundle, face_decal_asset)
        end
    end
end

function DrawPart:UpdateChangeExtraModelData(type, val)
    local data = self.extra_model_data
    if not data then return end
    if type == HeadCustomizationType.HairColor then
        data["hair_color"] = val
    elseif type == HeadCustomizationType.EyeSize then
        data["eye_size"] = val
    elseif type == HeadCustomizationType.EyePosition then
        data["eye_position"] = val
    elseif type == HeadCustomizationType.EyeAngle then
        data["eye_angle"] = val
    elseif type == HeadCustomizationType.EyeClose then
        data["eye_close"] = val
    elseif type == HeadCustomizationType.EyebrowAngle then
        data["eyebrow_angle"] = val
    elseif type == HeadCustomizationType.NoseSize then
        data["nose_size"] = val
    elseif type == HeadCustomizationType.NoseAngle then
        data["nose_angle"] = val
    elseif type == HeadCustomizationType.MouthSize then
        data["mouth_size"] = val
    elseif type == HeadCustomizationType.MouthPosition then
        data["mouth_position"] = val
    elseif type == HeadCustomizationType.MouthAngle then
        data["mouth_angle"] = val
    elseif type == HeadCustomizationType.CheekSize then
        data["cheek_size"] = val
    elseif type == HeadCustomizationType.ChinLength then
        data["chin_length"] = val
    elseif type == HeadCustomizationType.EyeShadowColor then
        data["eye_shadow_color"] = val
    elseif type == HeadCustomizationType.PupilColor_Left then
        data["left_pupil_color"] = val
    elseif type == HeadCustomizationType.PupilColor_Right then
        data["right_pupil_color"] = val
    elseif type == HeadCustomizationType.MouthColor then
        data["mouth_color"] = val
    elseif type == HeadCustomizationType.IrisSize_Left then
        data["left_iris_size"] = val
    elseif type == HeadCustomizationType.PupilSize_Left then
        data["left_pupil_size"] = val
    elseif type == HeadCustomizationType.IrisSize_Right then
        data["right_iris_size"] = val
    elseif type == HeadCustomizationType.PupilSize_Right then
        data["right_pupil_size"] = val
    elseif type == HeadCustomizationType.PupilType_Left then
        data["left_pupil_type"] = val
    elseif type == HeadCustomizationType.PupilType_Right then
        data["right_pupil_type_bundle"] = val
    elseif type == HeadCustomizationType.FaceDecalTex then
        data["face_decal_id"] = val
    end
end

-- 
function DrawPart:UpdateChangeSkinHairData()
    local data = self.extra_model_data
    if not data then return end
    if data.hair_color then
        self:ChangeSkinHeadMatColorByRGBATable(HeadCustomizationType.HairColor, data.hair_color)
    end
end

-- 改变方案配置材质
function DrawPart:ChangeRoleProjectMaterials(skin_type, res_id, material_res_id, callback)
    local change_skin = self:GetChangeSkinComponent()
    if IsNil(change_skin) then return end
	local bundle, asset

    if skin_type == ROLE_SKIN_TYPE.BODY then
        bundle, asset = ResPath.GetBodyDyeMaterialAsset(res_id, string.format("%s0%s", res_id, material_res_id))
    elseif skin_type == ROLE_SKIN_TYPE.FACE then
		bundle, asset = ResPath.GetFaceDyeMaterialAsset(res_id, string.format("%s0%s", res_id, material_res_id))
    elseif skin_type == ROLE_SKIN_TYPE.HAIR then
        bundle, asset = ResPath.GetHairDyeMaterialAsset(res_id, string.format("%s0%s", res_id, material_res_id))
    end

	if bundle ~= nil and asset ~= nil then
        ResPoolMgr:GetActorDyeItem(bundle, asset, function (asset)
			if asset and asset.Materials then
				if skin_type == ROLE_SKIN_TYPE.BODY then
                    change_skin:ChangeBodyMats(asset.Materials)
				elseif skin_type == ROLE_SKIN_TYPE.FACE then
                    change_skin:ChangeFaceMats(asset.Materials)
				elseif skin_type == ROLE_SKIN_TYPE.HAIR then	
                    change_skin:ChangeHairMats(asset.Materials)
				end

                if callback then
                    callback()
                end
			end
        end)
	end
end

-- 改变方案配置材质
function DrawPart:ResetRoleProjectMaterials(skin_type)
    local change_skin = self:GetChangeSkinComponent()
    if IsNil(change_skin) then return end
	local bundle, asset

    if skin_type == ROLE_SKIN_TYPE.BODY then
        if IsNil(self.now_cache_body_render) then return end
        change_skin:ChangeBody(self.now_cache_body_render)
    elseif skin_type == ROLE_SKIN_TYPE.FACE then
        if IsNil(self.now_cache_face_render) then return end
        change_skin:ChangeFaceAndEyeball(self.now_cache_face_render)
    elseif skin_type == ROLE_SKIN_TYPE.HAIR then
        if IsNil(self.now_cache_hair_render) then return end
        change_skin:ChangeHair(self.now_cache_hair_render)
    end
end
---===============================================================
---===============================================================
---===============================================================
---]]






--------------------------------------------------------------

DrawPart.loader_list = {}
function DrawPart.Pop()
    local draw_part = table.remove(DrawPart.loader_list)
    if nil == draw_part then
        draw_part = DrawPart.New()
    end

    return draw_part
end

function DrawPart.Release(draw_part)
    if #DrawPart.loader_list < 1000 then
        draw_part:Clear()
        table.insert(DrawPart.loader_list, draw_part)
    else
        draw_part:DeleteMe()
    end
end

local empty_table = {}
DrawPart.cbdata_list = {}
function DrawPart.GetCBData()
    local cbdata = table.remove(DrawPart.cbdata_list)
    if nil == cbdata then
        --[1]self[2]asset_bundle[3]asset_name[4]is_reload
        cbdata = {empty_table, true, true, false}
    end

    return cbdata
end

function DrawPart.ReleaseCBData(cbdata)
    cbdata[1] = empty_table
    cbdata[2] = true
    cbdata[3] = true
    cbdata[4] = false
    table.insert(DrawPart.cbdata_list, cbdata)
end