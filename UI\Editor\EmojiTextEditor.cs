﻿using UnityEditor;
using UnityEngine;

[CustomEditor(typeof(EmojiText), true)]
[CanEditMultipleObjects]
public class EmojiTextEditor : UnityEditor.UI.GraphicEditor
{
    SerializedProperty m_Text;
    SerializedProperty m_FontData;
    SerializedProperty m_workingRowWidth;
    SerializedProperty m_minRowWidth;
    SerializedProperty m_lineMode;
    SerializedProperty m_lineScale;
    SerializedProperty m_lineOffset;
    SerializedProperty m_iconScale;
    SerializedProperty m_bigIconScale;
    SerializedProperty m_buttonScale;
    SerializedProperty m_padding;
    SerializedProperty m_autoPadding;

    protected override void OnEnable()
    {
        base.OnEnable();
        m_Text = serializedObject.FindProperty("m_Text");
        m_FontData = serializedObject.FindProperty("m_FontData");
        m_workingRowWidth = serializedObject.FindProperty("_workingRowWidth");
        m_minRowWidth = serializedObject.FindProperty("_minRowWidth");
        m_lineMode = serializedObject.FindProperty("_lineMode");
        m_lineScale = serializedObject.FindProperty("_lineScale");
        m_lineOffset = serializedObject.FindProperty("_lineOffset");
        m_iconScale = serializedObject.FindProperty("_iconScale");
        m_bigIconScale = serializedObject.FindProperty("_bigIconScale");
        m_buttonScale = serializedObject.FindProperty("_buttonScale");
        m_padding = serializedObject.FindProperty("_padding");
        m_autoPadding = serializedObject.FindProperty("_autoPadding");
    }

    public override void OnInspectorGUI()
    {
        serializedObject.Update();
        string oldStr = m_Text.stringValue;
        EditorGUILayout.PropertyField(m_Text);
        EditorGUILayout.PropertyField(m_FontData);
        EditorGUILayout.PropertyField(m_workingRowWidth);
        EditorGUILayout.PropertyField(m_minRowWidth);
        EditorGUILayout.PropertyField(m_lineMode);
        EditorGUILayout.PropertyField(m_lineScale);
        EditorGUILayout.PropertyField(m_lineOffset);
        EditorGUILayout.PropertyField(m_iconScale);
        EditorGUILayout.PropertyField(m_bigIconScale);
        EditorGUILayout.PropertyField(m_buttonScale);
        EditorGUILayout.PropertyField(m_padding);
        EditorGUILayout.PropertyField(m_autoPadding);
        AppearanceControlsGUI();
        RaycastControlsGUI();
        serializedObject.ApplyModifiedProperties();
        string newStr = m_Text.stringValue;
        if (oldStr != newStr)
        {
            EmojiText emojiText = target as EmojiText;
            emojiText.ForceSetAllDirty();
        }
        EmojiText.drawRect = GUILayout.Toggle(EmojiText.drawRect, new GUIContent("DrawRect"));
    }
}
