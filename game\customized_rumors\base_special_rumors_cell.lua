-- 特殊传闻
--[[
    {
        rumor_type = 1,   -- raw_image 背景
        desc_rumors_content = "",   -- 内容
        show_yoyo_tween = false 
         
    }
]]

BaseSpecialRumorsCell = BaseSpecialRumorsCell or BaseClass(BaseRender)

function BaseSpecialRumorsCell:__init(instance)
    if nil == self.root_node then
        local bundle, asset = ResPath.GetCustomizedRumorsWidgets("BaseSpecialRumorsCell")
        self:LoadAsset(bundle, asset, instance.transform, function ()
            self.root_node = self.node_list.root_cell
            self.img_rumors_bg = self.node_list.img_rumors_bg
            self.mask_rumors_content = self.node_list.mask_rumors_content
            self.desc_rumors_content = self.node_list.desc_rumors_content
            self.start_pos_y = self.root_node.rect.anchoredPosition.y
            -- self.is_use_objpool = true

            -- 滑动速度 每秒100 单位
            self.rumors_seppd = 100
            self.rumors_start_offset_x = 1000
            self.rumors_desc_content_cache = ""
        end)
    end

    -- if self.is_use_objpool then
	-- 	self:Reset()
	-- end
end

function BaseSpecialRumorsCell:__delete()
    -- self:Reset(true)

    self.rumors_desc_content = nil
    self.root_node = nil
    self.img_rumors_bg = nil
    self.mask_rumors_content = nil
    self.desc_rumors_content = nil
    self.start_pos_y = nil

    -- if self.is_use_objpool and not self:IsNil() then
	-- 	ResPoolMgr:Release(self.view.gameObject, ResPoolReleasePolicy.Defualt)
	-- end
end

-- function BaseSpecialRumorsCell:Reset(is_delete)
--     self:StopRumorsAni()

--     if self.yoyo_tween then
--         self.yoyo_tween:Kill()
--         self.yoyo_tween = nil
--     end

--     if not is_delete then
--         self:SetBgActive(false)
--     end
-- end

function BaseSpecialRumorsCell:SetData(...)
    BaseRender.SetData(self, ...)
end

function BaseSpecialRumorsCell:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local show_cfg = CustomizedRumorsWGData.Instance:GetRumorShowInfoByRumorType(self.data.rumor_type)

    if IsEmptyTable(show_cfg) then
        local rumor_type = self.data.rumor_type or "nil"
        print_error("不存在rumor_type 为" .. rumor_type .."的传闻信息配置,检查自定义传闻表")
        return
    end

    if show_cfg.size_show_area then
        local size = string.split(show_cfg.size_show_area, "|")
        RectTransform.SetSizeDeltaXY(self.node_list["mask_rumors_content"].rect, size[1], size[2])
    end

    if show_cfg.pos_show_area then
        local size = string.split(show_cfg.pos_show_area, "|")
        RectTransform.SetAnchoredPositionXY(self.node_list["mask_rumors_content"].rect, size[1], size[2])
    end

    if show_cfg.desc_rumors_content then
        self.node_list.desc_rumors_content.text.text = show_cfg.desc_rumors_content
        UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.desc_rumors_content.rect)
    end

    if show_cfg.rumor_bg then
        local bg_bundle, bg_asset = ResPath.GetRawImagesPNG(show_cfg.rumor_bg)
        self.node_list.img_rumors_bg.raw_image:LoadSprite(bg_bundle, bg_asset, function ()
            self.node_list.img_rumors_bg.raw_image:SetNativeSize()
            self:SetBgActive(true)
        end)
    end

    if show_cfg.sign_bg and "" ~= show_cfg.sign_bg then
        local sign_bundle, sign_asset = ResPath.GetCustomizedRumorsImg(show_cfg.sign_bg)
        self.node_list.sign_img.image:LoadSprite(sign_bundle, sign_asset, function ()
            self.node_list.sign_img.image:SetNativeSize()
        end)

        if show_cfg.sign_pos then
            local sign_pos = string.split(show_cfg.sign_pos, "|")
            RectTransform.SetAnchoredPositionXY(self.node_list.sign_img.rect, sign_pos[1], sign_pos[2])
        end

        self.node_list.sign_img:CustomSetActive(true)
    else
        self.node_list.sign_img:CustomSetActive(false)
    end

    self.node_list.effect:CustomSetActive(false)
    if show_cfg.effect and show_cfg.effect ~= "" then
        self.node_list.effect:CustomSetActive(true)
        self.node_list.effect:ChangeAsset(ResPath.GetA2Effect(show_cfg.effect))
    end

    if self.data.show_yoyo_tween then
        if not self.yoyo_tween then
            self.yoyo_tween = self.root_node.rect:DOAnchorPosY(self.start_pos_y + 50, 1.5):SetEase(DG.Tweening.Ease.OutCubic):SetLoops(-1, DG.Tweening.LoopType.Yoyo)
        end
    else
        if self.yoyo_tween then
            self.yoyo_tween:Kill()
            self.yoyo_tween = nil
        end
    end

    self:TryPlayRumorsAni()
end

function BaseSpecialRumorsCell:TryPlayRumorsAni()
    local desc_rumors_content = self.data.desc_rumors_content

    if nil ~= desc_rumors_content and "" ~= desc_rumors_content then
        if self.rumors_desc_content_cache ~= desc_rumors_content then
            self.desc_rumors_content.text.text = desc_rumors_content
            self.rumors_desc_content_cache = desc_rumors_content
            -- self.desc_rumors_content.rect.anchoredPosition = Vector2(self.rumors_start_offset_x, 0)

            self:StopRumorsAni()

            self.calculate_time_quest = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(function ()
                local width = self.desc_rumors_content.rect.sizeDelta.x / 2
                width = width + self.mask_rumors_content.rect.sizeDelta.x / 2 + 100
                self.desc_rumors_content.rect.anchoredPosition = Vector2(width, 0)
                local duration = width / self.rumors_seppd
                self.rumors_content_tween = self.desc_rumors_content.rect:DOAnchorPosX(-width, duration, false)
                self.rumors_content_tween:SetEase(DG.Tweening.Ease.Linear)
                self.rumors_content_tween:SetLoops(-1, DG.Tweening.LoopType.Restart)
            end, self), 0.5)
        end
    else
        self:StopRumorsAni()
    end
end

function BaseSpecialRumorsCell:StopRumorsAni()
    if self.calculate_time_quest then
        GlobalTimerQuest:CancelQuest(self.calculate_time_quest)
        self.calculate_time_quest = nil
    end

    if self.rumors_content_tween then
        self.rumors_content_tween:Kill()
        self.rumors_content_tween = nil
    end

    if self.desc_rumors_content then
        self.desc_rumors_content.rect.anchoredPosition = Vector2(self.rumors_start_offset_x, 0)
    end
end

-- 设置显示区域
function BaseSpecialRumorsCell:SetDescContentPos(pos_x, pos_y)
    if self.mask_rumors_content and pos_x and pos_y then
        RectTransform.SetAnchoredPositionXY(self.mask_rumors_content.rect, pos_x, pos_y)
    end
end

-- 设置显示区域大小
function BaseSpecialRumorsCell:SetDescContentSizeDetail(x, y)
    if self.mask_rumors_content and x and y then
        local size_detail = self.mask_rumors_content.rect.sizeDelta
        
        if size_detail.x ~= x or size_detail.y ~= y then
            RectTransform.SetSizeDeltaXY(self.mask_rumors_content.rect, x, y)
        end
    end
end

function BaseSpecialRumorsCell:SetBgActive(flag)
    if self.img_rumors_bg then
        self.img_rumors_bg:CustomSetActive(flag)
    end
end

function BaseSpecialRumorsCell:SetSpecialRumorsCellScale(scale)
    if self.root_node and not IsNil(self.root_node.gameObject) then
        self.root_node.gameObject.transform:SetLocalScale(scale, scale, scale)
    end
end