-- 天神3v战斗3场景面板
TianShen3v3SceneView = TianShen3v3SceneView or BaseClass(SafeBaseView)
function TianShen3v3SceneView:__init()
	self:AddViewResource(0, "uis/view/tianshen_3v3_ui_prefab", "layout_tianshen_3v3_scene_view")
	self.view_cache_time = 0
    self.view_layer = UiLayer.MainUI
    self.is_safe_area_adapter = true
    self.active_close = false
end

function TianShen3v3SceneView:ReleaseCallBack()
	ResMgr:Destroy(self.node_list["left_panel"].gameObject)
	ResMgr:Destroy(self.node_list["map_panel"].gameObject)

	if self.map_role_head_icon_list then
		for i,v in ipairs(self.map_role_head_icon_list) do
			ResMgr:Destroy(v.obj.gameObject)
			v.head_cell:DeleteMe()
		end
	end
	self.map_role_head_icon_list = nil

	if self.buff_cell_list then
		for i,v in ipairs(self.buff_cell_list) do
			v:DeleteMe()
		end
		self.buff_cell_list = nil
	end

	self.occupy_point_map_icon_list = nil

	self.role_head_list = nil

	self.last_pos_dic = nil

	if self.point_list then
		self.point_list:DeleteMe()
		self.point_list = nil
	end
	
	if self.role_info_list then
		self.role_info_list:DeleteMe()
		self.role_info_list = nil
	end

   	if self.main_role_move_event then
   		GlobalEventSystem:UnBind(self.main_role_move_event)
   		self.main_role_move_event = nil
  	end

	if self.arrow_click_event then
		GlobalEventSystem:UnBind(self.arrow_click_event)
		self.arrow_click_event = nil
	end

	CountDownManager.Instance:RemoveCountDown("TianShen3v3WGData:FlushPKCountDown")

	if self.map_flush_timer then
		GlobalTimerQuest:CancelQuest(self.map_flush_timer)
		self.map_flush_timer = nil
	end

	if self.flush_buff_list_timer then
		GlobalTimerQuest:CancelQuest(self.flush_buff_list_timer)
		self.flush_buff_list_timer = nil
	end
	
	if self.delay_setactive_top_tips_timer then
		GlobalTimerQuest:CancelQuest(self.delay_setactive_top_tips_timer)
		self.delay_setactive_top_tips_timer = nil
	end
end

function TianShen3v3SceneView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["open_map_btn"], BindTool.Bind(self.OnClickOpenMapBtn, self))
	XUI.AddClickEventListener(self.node_list["close_map_btn"], BindTool.Bind(self.OnClickCloseMapBtn, self))


	-- 挂到主界面上
	local left_parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
	local right_parent = MainuiWGCtrl.Instance:GetSkillRightOtherContent()
	self.node_list["left_panel"].transform:SetParent(left_parent.transform, false)
	self.node_list["left_panel"].rect.anchoredPosition = Vector2(145, -128)
	self.node_list["map_panel"].transform:SetParent(right_parent.transform, false)
	-- MainuiWGCtrl.Instance:SetTaskButtonTrue()

	self.point_list = AsyncListView.New(TianShen3v3Point, self.node_list["point_list"])
	-- 排序
	local cfg_list = {}
	for i,v in ipairs(TianShen3v3WGData.Instance:GetPointCfg()) do
		table.insert(cfg_list, v)
	end
	table.sort(cfg_list, SortTools.KeyLowerSorter("side_" .. TianShen3v3WGData.Instance:GetMySide() .. "_sort"))
	self.point_list:SetDataList(cfg_list)

	self.role_info_list = AsyncListView.New(TianShen3v3RoleInfoItem, self.node_list["role_info_list"])

	self.main_role_move_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_POS_CHANGE, BindTool.Bind(self.OnMainRoleMove, self))
    self.arrow_click_event = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind1(self.TopPanelAniCallBack, self))

	-- 地图
	local mini_camera = Scene.Instance:GetSceneMiniMapCamera()
	if not IsNil(mini_camera) then
		self.node_list["map_raw"].raw_image.texture = mini_camera.MapTexture
		-- self.node_list["map_raw"].raw_image:SetNativeSize()
	end	

	local size_delta = self.node_list["map_raw"].rect.sizeDelta
	self.map_width = size_delta.x
	self.map_height = size_delta.y
	self.node_list["map_raw"].event_trigger_listener:AddPointerClickListener(BindTool.Bind(self.OnClickMiniMap, self))

	self.map_flush_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.FlushMapPanel, self), 1)

	self.node_list["map_icon_prefab"]:SetActive(false)

	-- buff列表
	self.flush_buff_list_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.FlushBuffList, self), 1)
	self.node_list["buff_cell_prefab"]:SetActive(false)
	self.buff_cell_list = {}

	-- 顶部提示
	self.node_list["top_tips_bg"]:SetActive(true)
	self.delay_setactive_top_tips_timer = GlobalTimerQuest:AddDelayTimer(function() 
		self.node_list["top_tips_bg"]:SetActive(false)
	end, 15)
end


function TianShen3v3SceneView:OpenCallBack()
end

function TianShen3v3SceneView:CloseCallBack()
end

function TianShen3v3SceneView:ShowIndexCallBack()
end

function TianShen3v3SceneView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushAllPanel()
		elseif k == "map" then
			self:FlushMapPanel()
		elseif k == "flush_role_list" then
			self:FlushRoleList()
		elseif k == "fly_to_buff_list" then
			self:FlyToBuffList()
		elseif k == "fly_to_score" then
			self:FlyToScore(v.side)
		elseif k == "fly_to_hp_bar" then
			self:FlyToHpBar()
		elseif k == "scene_info" then
			self:FlushOccupySlider()
			self:FlushPointList()
			self:FlushOther()
			self:FlushPKCountDown()
		elseif k == "show_end_label" then
			self:ShowEndLabel(v.side)
		end
	end
end

function TianShen3v3SceneView:FlushAllPanel()
	self:FlushRoleList()
	self:FlushMapPanel()
	self:FlushOccupySlider()
	self:FlushPointList()
	self:FlushOther()
	self:FlushPKCountDown()
end

function TianShen3v3SceneView:FlushRoleList()
	if TianShen3v3WGData.Instance:GetMySideSceneRoleInfoList() then
		self.role_info_list:SetDataList(TianShen3v3WGData.Instance:GetMySideSceneRoleInfoList()) 			-- 队友实时信息
	end
end

function TianShen3v3SceneView:FlushOther()
	local max_score = TianShen3v3WGData.Instance:GetOtherCfg().win_score
	-- 双方积分
	local scene_info = TianShen3v3WGData.Instance:GetSceneInfo()
	if scene_info then
		for k, side in pairs(TS3V3_SIDE) do
			self.node_list["score_" .. side].text.text = string.format(Language.TianShen3v3.SceneViewSideScore[side], scene_info.side_scene_score[side], max_score)
		end
	end

	-- 提醒
	self.node_list["slider_tips"].text.text = TianShen3v3WGData.Instance:GetOtherCfg().slider_tips

	-- 上方提醒
	self.node_list["top_tips"].text.text = TianShen3v3WGData.Instance:GetOtherCfg().top_tips
end

-- 刷新占领点列表
function TianShen3v3SceneView:FlushPointList()
	self.point_list:RefreshActiveCellViews()--SetDataList(TianShen3v3WGData.Instance:GetPointCfg())
end

-- 刷新点位占领进度
function TianShen3v3SceneView:FlushOccupySlider()
	local main_role = Scene.Instance:GetMainRole()
	local x, y = main_role:GetLogicPos()
	local in_point, point_cfg = TianShen3v3WGData.Instance:InPoint(x, y)
	self.node_list["occupy_slider_panel"]:SetActive(in_point)
	self.node_list["occupy_slider_" .. TianShen3v3WGData.Instance:GetMySide()].transform:SetAsLastSibling()
	if in_point then
		local point_info = TianShen3v3WGData.Instance:GetOccupyPointInfoByIndex(point_cfg.index)
		for k, side in pairs(TS3V3_SIDE) do
			if point_info then
				local progress = point_info["side_" .. side .. "_occupy_progress"]
				self.node_list["occupy_slider_" .. side].slider:DOValue(progress, 1):SetEase(DG.Tweening.Ease.Linear)
				self.node_list["side_" .. side .. "_slider_effect"]:SetActive(progress >= 0.5)

				local enemy_side_is_owner = point_info.occupy_side == TianShen3v3WGData.Instance:GetEnemySide(side)
				self.node_list["side_" .. side .. "_broken_icon"]:SetActive(enemy_side_is_owner)
				self.node_list["side_" .. side .. "_normal_icon"]:SetActive(not enemy_side_is_owner)
				self.node_list["side_" .. side .. "_occupy_label"]:SetActive(point_info.occupy_side == side)
			end
		end
	end
end

-- 刷新比赛倒计时
function TianShen3v3SceneView:FlushPKCountDown()
	local scene_info = TianShen3v3WGData.Instance:GetSceneInfo()
	if scene_info then
		if CountDownManager.Instance:HasCountDown("TianShen3v3WGData:FlushPKCountDown") then
			CountDownManager.Instance:RemoveCountDown("TianShen3v3WGData:FlushPKCountDown")
		end
		self:UpdatePKCountDown()
		if scene_info.pk_end_timestamp > TimeWGCtrl.Instance:GetServerTime() then
			CountDownManager.Instance:AddCountDown("TianShen3v3WGData:FlushPKCountDown", BindTool.Bind(self.UpdatePKCountDown, self), nil, scene_info.pk_end_timestamp, nil, 1)
		end
	end
end

function TianShen3v3SceneView:UpdatePKCountDown()
	local scene_info = TianShen3v3WGData.Instance:GetSceneInfo()
	if scene_info then
		local second = math.max(0, math.floor(scene_info.pk_end_timestamp - TimeWGCtrl.Instance:GetServerTime())) 
		if second >= 1 and second <= 15 then
			ViewManager.Instance:Open(GuideModuleName.TianShen3v3EndCountdownView)
		end
		if self.node_list["pk_countdown"] then
			self.node_list["pk_countdown"].text.text = TimeUtil.FormatSecondDHM4(second)
		end
	end
end

-- 刷新地图
function TianShen3v3SceneView:FlushMapPanel()
	self:TryCreateOccupyPointMapIcon() 										-- 创建占领点图标
	self:FlushMapOccupyPoint() 												-- 刷新地图占领点

	self:TryCreateRoleHead() 												-- 创建小地图头像
	self:FlushMapHeadList() 												-- 刷新地图角色头像
end

function TianShen3v3SceneView:OnClickMapRoleIcon(role_head_info)

end

-- 创建地图头像icon
function TianShen3v3SceneView:TryCreateRoleHead()
	if self.map_role_head_icon_list == nil then
		self.map_role_head_icon_list = {}
	end

	local prefab = self.node_list["map_icon_prefab"].gameObject
	for i, v in ipairs(TianShen3v3WGData.Instance:GetMapRoleInfoList()) do
		if not self.map_role_head_icon_list[v.uuid_str] then
			local object = ResMgr:Instantiate(prefab)
			object:SetActive(true)
			local name_table = U3DNodeList(object:GetComponent(typeof(UINameTable)), self)
            local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(v.tianshen_index)
            if tianshen_cfg then
            	local bundle, asset = ResPath.GetItem(tianshen_cfg.head_id)
				name_table["icon"].image:LoadSpriteAsync(bundle, asset, function ()
					name_table["icon"]:SetActive(true)
					name_table["icon"].image:SetNativeSize()
				end)
            end
			
            name_table["icon_hl"]:SetActive(v.uuid_str == RoleWGData.Instance:GetUUIDStr())

			-- if v.call_back then
			-- 	XUI.AddClickEventListener(name_table["icon"], function() v.call_back(v) end)
			-- end
			self.map_role_head_icon_list[v.uuid_str] = {obj = object, info = v, name_table = name_table}
		end
	end
end

-- 创建占领点图标
function TianShen3v3SceneView:TryCreateOccupyPointMapIcon()
	if self.occupy_point_map_icon_list == nil then
		self.occupy_point_map_icon_list = {}
		local prefab = self.node_list["map_icon_prefab"].gameObject
		-- 遍历所有占领点
		for _, cfg in ipairs(TianShen3v3WGData.Instance:GetPointCfg()) do
			local object = ResMgr:Instantiate(prefab)
			object:SetActive(true)
			local name_table = U3DNodeList(object:GetComponent(typeof(UINameTable)), self)
			name_table["map_bg"].image.enabled = false	
            name_table["icon_hl"]:SetActive(false)	
			object.transform:SetParent(self.node_list["map_raw"].transform, false)
			object.transform.localScale = Vector3(2.5, 2.5, 2.5)
			local _, x, y = TianShen3v3WGData.Instance:GetPointPos(cfg.index)
			local ui_x, ui_y = self:LogicToUI(x, y)
			object.transform:SetLocalPosition(ui_x, ui_y, 0)
			table.insert(self.occupy_point_map_icon_list, {obj = object, name_table = name_table, cfg = cfg})
		end
	end
end

-- 设置地图头像位置
function TianShen3v3SceneView:FlushMapHeadPos(obj, x, y)
	if not obj then
		return
	end

	if not self.last_pos_dic then
		self.last_pos_dic = {}
	end

	local last_pos = self.last_pos_dic[obj]
	if not last_pos or last_pos.x ~= x or last_pos.y ~= y then
		obj.transform:SetParent(self.node_list["map_raw"].transform, false)
		obj.transform.localScale = Vector3(2.5, 2.5, 2.5)
		local ui_x, ui_y = self:LogicToUI(x, y)
		-- obj.transform:SetLocalPosition(ui_x, ui_y, 0)
		local distance = 0
		if last_pos then
 			distance = u3d.v2Distance({x = last_pos.x, y = last_pos.y}, {x = x, y = y})
		end

		if not last_pos or distance > 15 then
			obj.transform.localPosition = u3dpool.vec3(ui_x, ui_y, 0)
		else
			obj.transform:DOLocalMove(u3dpool.vec3(ui_x, ui_y, 0), 1):SetEase(DG.Tweening.Ease.Linear)
		end
		obj.transform:SetAsLastSibling()
	end
	self.last_pos_dic[obj] = {x = x, y = y}
end

function TianShen3v3SceneView:FlushMapHead(head_data)
	head_data.name_table["map_bg"].image:LoadSpriteAsync(ResPath.GetTianShen3v3Img("circle_bg_" .. head_data.info.side))
end

function TianShen3v3SceneView:FlushMapHeadList()
	if self.map_role_head_icon_list then
		for i,v in pairs(self.map_role_head_icon_list) do
			local map_role_info = TianShen3v3WGData.Instance:GetMapRoleInfoDic()[v.info.uuid_str]
			v.obj:SetActive(map_role_info ~= nil)
			if map_role_info then
				self:FlushMapHeadPos(v.obj, map_role_info.pos_x, map_role_info.pos_y)
				self:FlushMapHead(v)
			end
		end
	end
end

function TianShen3v3SceneView:FlushMapOccupyPoint()
	for i,v in ipairs(self.occupy_point_map_icon_list) do
		local point_info = TianShen3v3WGData.Instance:GetOccupyPointInfoByIndex(v.cfg.index)
		if point_info then
			-- 占领点图标
			v.name_table["icon"].image:LoadSpriteAsync(ResPath.GetTianShen3v3Img("tszc_zjm_point_" .. point_info.occupy_side))
		end
	end
end

-- 刷新buff列表
function TianShen3v3SceneView:FlushBuffList()
	for i, v in ipairs(self.buff_cell_list) do
		if self.buff_cell_list[i].view then
			self.buff_cell_list[i].view.gameObject:SetActive(false)
		end
	end

	local buff_info_list = TianShen3v3WGData.Instance:GetVaildBuffInfoList()
	for i, v in ipairs(buff_info_list) do
		if not self.buff_cell_list[i] then
			local prefab = self.node_list["buff_cell_prefab"].gameObject
			local obj = ResMgr:Instantiate(prefab)
			obj:SetActive(true)
			obj.transform:SetParent(self.node_list["buff_list"].transform, false)
			self.buff_cell_list[i] = TianShen3v3BuffIcon.New(obj)
		end

		self.buff_cell_list[i]:SetData(v)
		self.buff_cell_list[i].view.gameObject:SetActive(true)
	end
end

-- 播放飞行特效到buff列表
function TianShen3v3SceneView:FlyToBuffList()
	local bundle, asset = ResPath.GetUIEffect("Effect_guangqiu")
	TipWGCtrl.Instance:ShowFlyEffectManager("TianShen3v3SceneView:FlyToBuffList", bundle, asset, self.node_list["fly_effect_start_pos"], 
		self.node_list["buff_list_effect_pos"], DG.Tweening.Ease.OutCubic, 0.6, nil, nil, 1, 50, nil, true, nil, nil, 80)
end

-- 播放飞行特效到积分
function TianShen3v3SceneView:FlyToScore(side)
	local bundle, asset = ResPath.GetUIEffect("Effect_guangqiu")
	TipWGCtrl.Instance:ShowFlyEffectManager("TianShen3v3SceneView:FlyToScore", bundle, asset, self.node_list["fly_effect_start_pos"], 
		self.node_list["score_" .. side .. "_effect_pos"], DG.Tweening.Ease.OutCubic, 0.6, nil, nil, 1, 50, nil, true, nil, nil, 80)
end

-- 播放飞行特效到角色血条
function TianShen3v3SceneView:FlyToHpBar()
	local bundle, asset = ResPath.GetUIEffect("Effect_guangqiu")
	TipWGCtrl.Instance:ShowFlyEffectManager("TianShen3v3SceneView:FlyToHpBar", bundle, asset, self.node_list["fly_effect_start_pos"], 
		self.node_list["hp_bar_effect_pos"], DG.Tweening.Ease.OutCubic, 0.6, nil, nil, 1, 50, nil, true, nil, nil, 80)
end

-- 显示战斗结束标签
function TianShen3v3SceneView:ShowEndLabel(side)
	self.node_list["side_" .. side .. "_win_label"]:SetActive(true)
end

function TianShen3v3SceneView:OnClickMiniMap(event)
	-- 当前场景无法移动
	MapWGCtrl.Instance:CloseMonsterTip()
	local logic = Scene.Instance:GetSceneLogic()
	if logic and not logic:CanCancleAutoGuaji() then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Rune.CanNotCancleGuaji)
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	if main_role:CantPlayerDoMove(true) then
		return
	end

    if main_role:IsJump() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotMoveInJump)
		return
	end

	local ok, localPosition = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(
		self.node_list["map_raw"].rect, event.position, event.pressEventCamera, Vector2(0, 0))
	if not ok then
		return
	end

	local logic_x, logic_y = self:UIToLogic(localPosition.x, localPosition.y)
	if AStarFindWay:IsBlock(logic_x, logic_y) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotMoveInBlock)
		return
	end
	self:ClearOldGuaJiOperator()
	GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), logic_x, logic_y, 0)
	GlobalEventSystem:Fire(OtherEventType.MOVE_BY_CLICK)
end

--清楚当前的挂机操作
function TianShen3v3SceneView:ClearOldGuaJiOperator()
	GuajiWGCtrl.Instance:ResetMoveCache()
	GuajiWGCtrl.Instance:ClearGuajiCache()
	TaskGuide.Instance:CanAutoAllTask(false)
	GuajiWGCtrl.Instance:SetMoveToPosCallBack(nil)
	GuajiWGCtrl.Instance:StopGuaji()
end

function TianShen3v3SceneView:OnMainRoleMove()
	self:FlushOccupySlider()
end

function TianShen3v3SceneView:TopPanelAniCallBack(is_on)
	self.node_list["top_panel"]:SetActive(not is_on)
end

function TianShen3v3SceneView:OnClickOpenMapBtn()
	self.node_list["map_content"]:SetActive(true)
	self.node_list["open_map_btn"]:SetActive(false)
end

function TianShen3v3SceneView:OnClickCloseMapBtn()
	self.node_list["map_content"]:SetActive(false)
	self.node_list["open_map_btn"]:SetActive(true)
end


-- logic坐标转ui坐标
function TianShen3v3SceneView:LogicToUI(logic_x, logic_y)
	local mini_camera = Scene.Instance:GetSceneMiniMapCamera()
	if IsNil(mini_camera) then
		return 0, 0
	end

	if logic_x == nil or logic_y == nil then
		return 0, 0
	end

	local wx, wy = GameMapHelper.LogicToWorld(logic_x, logic_y)
	local uipos = mini_camera:TransformWorldToUV(Vector3(wx, 0, wy))
	local ui_x, ui_y = self.map_width * uipos.x, self.map_height * uipos.y
	return ui_x, ui_y
end

-- ui坐标转logic坐标
function TianShen3v3SceneView:UIToLogic(ui_x, ui_y)
	local mini_camera = Scene.Instance:GetSceneMiniMapCamera()
	if IsNil(mini_camera) then
		return 0, 0
	end

	local uipos_x = ui_x / self.map_width
	local uipos_y =  ui_y / self.map_height
	local world_pos = mini_camera:TransformUVToWorld(Vector2(uipos_x, uipos_y))
	local logic_x, logic_y = GameMapHelper.WorldToLogic(world_pos.x, world_pos.z)
	return logic_x, logic_y
end
------------------------ 角色信息 --------------------------------------
TianShen3v3RoleInfoItem = TianShen3v3RoleInfoItem or BaseClass(BaseRender)
function TianShen3v3RoleInfoItem:__init()
	XUI.AddClickEventListener(self.node_list["btn"], BindTool.Bind(self.OnClick, self))
end

function TianShen3v3RoleInfoItem:__delete()
	self.slider_relive_tween = nil
end

function TianShen3v3RoleInfoItem:OnFlush()
	-- 天神头像
	local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(self.data.tianshen_index)
	XUI.SetGraphicGrey(self.node_list["head_icon"], self.data.is_dead)
	if self.last_head_id ~= tianshen_cfg.head_id then
		local bundle, asset = ResPath.GetItem(tianshen_cfg.head_id)
		self.node_list["head_icon"].image:LoadSpriteAsync(bundle, asset, function ()
			self.node_list["head_icon"]:SetActive(true)
			-- self.node_list["head_icon"].image:SetNativeSize()
			XUI.SetGraphicGrey(self.node_list["head_icon"], self.data.is_dead)
		end)
		self.last_head_id = tianshen_cfg.head_id
	end

	-- 死亡标签
	self.node_list["is_dead"]:SetActive(self.data.is_dead)

	-- 复活进度
	self.node_list["slider_relive"]:SetActive(self.data.is_dead)
	if self.slider_relive_tween == nil and self.data.is_dead then
		self.node_list["slider_relive"].slider.value = 0
		self.slider_relive_tween = self.node_list["slider_relive"].slider:DOValue(1, self.data.relive_time - TimeWGCtrl.Instance:GetServerTime())
		self.slider_relive_tween:SetEase(DG.Tweening.Ease.Linear)
		self.slider_relive_tween:OnComplete(
			function()
				self.slider_relive_tween = nil
			end
		)
	end

	-- 背景颜色
	if self.data.side >= 0 then
		self.node_list["bg"].image:LoadSpriteAsync(ResPath.GetTianShen3v3Img("role_info_bg_" .. self.data.side))
	end

	-- 血条
	self.node_list["slider_blood"]:SetActive(not self.data.is_dead)
	if self.data.side >= 0 then
		self.node_list["slider_blood_fill"].image:LoadSpriteAsync(ResPath.GetTianShen3v3Img("slider_fill_" .. self.data.side))
	end
	self.node_list["slider_blood"].slider.value = self.data.cur_hp_rate / 100

	-- 角色名称
	self.node_list["role_name"].text.text = "[s" .. self.data.server_id .. "]" .. self.data.role_name

	-- 五行icon
	local wuxing_index = TianShenWGData.Instance:GetTianShenWuXingByIndex(self.data.tianshen_index)
	if wuxing_index ~= 0 then
		local bundle,asset = ResPath.GetF2CommonImages("wuxing_small_" .. wuxing_index)
		self.node_list["wuxing_icon"].image:LoadSprite(bundle, asset)
		self.node_list["wuxing_icon"]:SetActive(true)
		self.node_list["wuxing_icon"].image:SetNativeSize()
	else
		self.node_list["wuxing_icon"]:SetActive(false)
	end
end

function TianShen3v3RoleInfoItem:OnClick()
	local target_role_obj = Scene.Instance:GetRoleByUUIDStr(self.data.uuid_str)
	if target_role_obj then
		GuajiWGCtrl.Instance:StopGuaji(nil, true)
		local x, y = target_role_obj:GetLogicPos()
		GuajiWGCtrl.Instance:MoveToPos(TianShen3v3WGData:GetOtherCfg().pk_scene_id, x, y, 2)
	end
end
------------------------ 占领点位信息 --------------------------------------
TianShen3v3Point = TianShen3v3Point or BaseClass(BaseRender)
function TianShen3v3Point:__init()
	XUI.AddClickEventListener(self.node_list["btn"], BindTool.Bind(self.OnClick, self))
	self.arrow_start_x = self.node_list["arrow_tips"].transform.localPosition.x
	self.arrow_is_show = false
end

function TianShen3v3Point:__delete()
	self.tweener = nil
end

function TianShen3v3Point:SetIndex(index)
	BaseRender.SetIndex(self, index)

	-- 提示箭头
	if self.index == 1 and not self.arrow_is_show then
		if self.tweener then
			self.tweener:Kill()
		end
		self.arrow_is_show = true
		self.node_list["arrow_tips"]:SetActive(true)
		self.tweener = self.node_list["arrow_tips"].transform:DOLocalMoveX(self.arrow_start_x - 30, 0.5):SetLoops(-1, DG.Tweening.LoopType.Yoyo)
		GlobalTimerQuest:AddDelayTimer(function() 
			if self.node_list and self.node_list["arrow_tips"] then
				self.node_list["arrow_tips"]:SetActive(false)
			end
		end, 20)
	else
		self.node_list["arrow_tips"]:SetActive(false)
	end
end

function TianShen3v3Point:OnFlush()
	local point_info = TianShen3v3WGData.Instance:GetOccupyPointInfoByIndex(self.data.index)
	if point_info then
		-- 占领点图标
		self.node_list["point_icon"].image:LoadSpriteAsync(ResPath.GetTianShen3v3Img("tszc_zjm_point_" .. point_info.occupy_side))

		-- 占领点名称
		self.node_list["point_name"].text.text = self.data.occupy_name

		local occupy_side_change = point_info.occupy_state == TianShen3v3PointState.Occupyed and self.last_occupy_side ~= point_info.occupy_side

		-- 背景
		if point_info.occupy_side >= 0 then
			if not occupy_side_change then
				self.node_list["bg"].image:LoadSpriteAsync(ResPath.GetTianShen3v3Img("role_info_bg_" .. point_info.occupy_side))
				self.node_list["bg"]:SetActive(true)
			else
				self.node_list["side_" .. point_info.occupy_side .. "_capture_effect"]:SetActive(true)
				GlobalTimerQuest:AddDelayTimer(function ()
					if self.node_list and self.node_list["bg"] and self.data then
						local point_info = TianShen3v3WGData.Instance:GetOccupyPointInfoByIndex(self.data.index)
						self.node_list["bg"]:SetActive(true)
						self.node_list["bg"].image:LoadSpriteAsync(ResPath.GetTianShen3v3Img("role_info_bg_" .. point_info.occupy_side))
					end
				end, 1.5)
			end
		else
			self.node_list["bg"]:SetActive(false)
		end

		-- 占领状态
		self.node_list["occupying"]:SetActive(point_info.occupy_state == TianShen3v3PointState.Occupying)
		self.node_list["unoccupy"]:SetActive(point_info.occupy_state == TianShen3v3PointState.Unoccupy)
		for k, side in pairs(TS3V3_SIDE) do
			self.node_list["side_" .. side .. "_capture"]:SetActive(point_info.occupy_state == TianShen3v3PointState.Occupyed and point_info.occupy_side == side)
		end
		self.last_occupy_side = point_info.occupy_side

		-- 每秒积分
		self.node_list["score_desc"].text.text = string.format(Language.TianShen3v3.SecondAddScore, self.data.occupy_score)
	end

end

function TianShen3v3Point:OnClick()
	TianShen3v3WGCtrl.Instance:MoveToOccupyPoint(self.data.index)
	self.node_list["arrow_tips"]:SetActive(false)
end

------------------------ buff图标 --------------------------------------
TianShen3v3BuffIcon = TianShen3v3BuffIcon or BaseClass(BaseRender)
function TianShen3v3BuffIcon:__init()
	self.node_list["buff_panel"]:SetActive(false)
	XUI.AddClickEventListener(self.node_list["buff_icon"], BindTool.Bind(self.OnClickIcon, self))
	XUI.AddClickEventListener(self.node_list["block"], BindTool.Bind(self.OnClickBuffPanelBlock, self))
end

function TianShen3v3BuffIcon:__delete()

end

function TianShen3v3BuffIcon:OnFlush()
	if self.data then
		local buff_cfg = TianShen3v3WGData.Instance:GetBuffCfg(self.data.buff_type)
		if buff_cfg.continue_time == 0 then
			buff_cfg.continue_time = 1
		end
		local progress_value = (self.data.end_timestamp - TimeWGCtrl.Instance:GetServerTime()) / (buff_cfg.continue_time / 1000)
		self.node_list["buff_slider"].slider:DOValue(progress_value, 1):SetEase(DG.Tweening.Ease.Linear)

		if self.last_icon ~= buff_cfg.buff_icon then
			self.node_list["buff_icon"].image:LoadSprite(ResPath.GetTianShen3v3Img(buff_cfg.buff_icon))
			self.node_list["buff_icon"].image:SetNativeSize()
		end
		self.last_icon = buff_cfg.buff_icon

		-- tips面板的buff图标
		self.node_list["buff_tips_icon"].image:LoadSpriteAsync(ResPath.GetSkillIconById(buff_cfg.buff_skill_icon_id or 0))

		-- buff描述
		self.node_list["buff_desc"].text.text = buff_cfg.buff_desc or ""
	end
end

function TianShen3v3BuffIcon:OnClickIcon()
	self.node_list["buff_panel"]:SetActive(true)
end

function TianShen3v3BuffIcon:OnClickBuffPanelBlock()
	self.node_list["buff_panel"]:SetActive(false)
end