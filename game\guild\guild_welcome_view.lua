--第一次加入仙盟的欢迎界面
GuildWelcomeView = GuildWelcomeView or BaseClass(SafeBaseView)
function GuildWelcomeView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_guild_welcome")
    self.view_layer = UiLayer.Pop
    self.view_name = "GuildWelcomeView"
end

function GuildWelcomeView:LoadCallBack()
    self.node_list.btn_sure.button:AddClickListener(BindTool.Bind1(self.Close, self))
    self.node_list.btn_close.button:AddClickListener(BindTool.Bind1(self.Close, self))
end


function GuildWelcomeView:OnFlush()
    local guildvo = GuildDataConst.GUILDVO
    local role_vo = RoleWGData.Instance:GetRoleInfo()
    self.node_list.txt_name.text.text = string.format(Language.Guild.WelcomeTextName, role_vo.name)
    self.node_list.txt_welcome.text.text = string.format(Language.Guild.WelcomeTextGuildName, guildvo.guild_name)
    self.node_list.txt_mengzhuname.text.text = string.format(Language.Guild.MengzhuTxt, guildvo.tuanzhang_name)
    
    local time = TimeWGCtrl.Instance:GetServerTime()
    local time_str = os.date("%Y-%m-%d  %H:%M", time)
    self.node_list.txt_time.text.text = time_str
    for i = 1, 4 do
        self.node_list["txt_des_"..i].text.text = Language.Guild.WelcomeText[i]
    end
end