-----------------------ExchangeShopItem-----------------------------------
ExchangeShopItem = ExchangeShopItem or BaseClass(BaseRender)

function ExchangeShopItem:__init()
	-- XUI.AddClickEventListener(self.node_list.click_cell, BindTool.Bind(self.OnClick, self))
	XUI.AddClickEventListener(self.node_list.btn_exchange, BindTool.Bind(self.ClickExchange, self))
	XUI.AddClickEventListener(self.node_list.item_icon, BindTool.Bind(self.ClickStuff, self))
	
	self.item_cell = ExchangeShopItemCell.New(self.node_list.item)
end

function ExchangeShopItem:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function ExchangeShopItem:OnFlush()
	if self.data == nil then
		self.view:SetActive(false)
		return
	end
	self.view:SetActive(true)
	-- local exchange_data = ExchangeShopWGData.Instance:GetData()
	local num = ItemWGData.Instance:GetItemNumInBagById(self.data.stuff_id)
	--local color = OperationActivityWGData.Instance:GetCurThemeColor()
	self.node_list.name.text.text = self.data.name --ToColorStr(self.data.name, color)
	self.node_list.cost.text.text = GetRightColor(num .. "/" .. self.data.cost, num >= self.data.cost, COLOR3B.L_GREEN, COLOR3B.L_RED)
	local duihuan_num = math.floor(num / self.data.cost)
	self.node_list.exchange_num.text.text = string.format(Language.OpertionAcitvity.ExchangeStr, GetRightColor(duihuan_num, duihuan_num > 0, COLOR3B.L_GREEN, COLOR3B.L_RED), GetRightColor(self.data.can_exchange_times, self.data.can_exchange_times > 0,
	COLOR3B.L_GREEN, COLOR3B.L_RED))

	self.node_list.remind:SetActive(self.data.can_exchange_times > 0 and num >= self.data.cost)
	local config = ItemWGData.Instance:GetItemConfig(self.data.stuff_id)
	if config then
		local bundle, asset = ResPath.GetItem(config.icon_id)
		self.node_list.item_icon.image:LoadSprite(bundle, asset, function ()
			self.node_list.item_icon.image:SetNativeSize()
		end)
	else
		print_error("[ExchangeShopItem] item_config is invaild, item_id is:", self.data.stuff_id)
	end

	local item_bg_name = OperationActivityWGData.Instance:GetCurTypeImgName("a3_xskh_di_13")
	local bundle1, asset1 = ResPath.GetRawImagesPNG(item_bg_name)
	self.node_list["item_bg"].raw_image:LoadSprite(bundle1, asset1, function()
        self.node_list["item_bg"].raw_image:SetNativeSize()
    end)

	self.node_list.btn_exchange.button.interactable = self.data.can_exchange_times ~= 0
	self.node_list.word_sold_out:SetActive(self.data.can_exchange_times == 0)

	self.item_cell:SetData(self.data.item)

	local index = OperationActivityWGData.Instance:GetCurTypeId()
	self.node_list.btn_exchange_text.text.color = Str2C3b(index == 1 and "#a71f19" or "#ffffff")
end

function ExchangeShopItem:SetClickCallBack(event)
	self.event = event
end

function ExchangeShopItem:OnClick()
	if self.event then
		self.event(self)
	end
end

function ExchangeShopItem:ClickExchange()
	local num = self.data.item.num
	local empty = ItemWGData.Instance:GetEmptyNum()
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item.item_id)

	if empty < num and item_cfg and item_cfg.not_put_flag == 0 then
		RoleBagWGData.Instance:SetRoleBagCleanFlag(true)
		RoleBagWGCtrl.Instance:OpenRoleBagCleanTips(KNAPSACK_TYPE.NORMAL)
		return
	end
	
	local buy_func = function(num)
		TipWGCtrl.Instance:ShowGetItem(self.data.item)
		local buy_num = num or 1
		ExchangeShopWGCtrl.Instance:SendOperExchange(OA_ACT_COMMON_CONVERT_OPERA_TYPE.CONVERT, self.data.seq, buy_num)
	end

	local tips_data = {}
	tips_data.title_view_name = Language.Common.ExchangeItemTipsTitle
	tips_data.item_id = self.data.item.item_id
	tips_data.expend_item_id = self.data.stuff_id
	tips_data.expend_item_num = self.data.cost
	tips_data.max_buy_count = self.data.can_exchange_times
	tips_data.is_show_limit = true
	TipWGCtrl.Instance:OpenCustomBuyItemTipsView(tips_data, buy_func)
end

function ExchangeShopItem:ClickStuff()
	TipWGCtrl.Instance:OpenItem({item_id = self.data.stuff_id}, ItemTip.FROM_NORMAL, nil)
end

ExchangeShopItemCell = ExchangeShopItemCell or BaseClass(ItemCell)

function ExchangeShopItemCell:OnFlush()
	ItemCell.OnFlush(self)
	if not self.data then
		return 
	end
	-- self:ShowItemActive()
end