WorldsNO1RewardView = WorldsNO1RewardView or BaseClass(SafeBaseView)

function WorldsNO1RewardView:__init()
    self:SetMaskBg()
	self:LoadConfig()
    self.default_index = TabIndex.worlds_no1_single_match_reward
end

function WorldsNO1RewardView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/worlds_no1_ui_prefab", "VerticalTabbar")
	self:AddViewResource(0, "uis/view/worlds_no1_ui_prefab", "layout_worlds_no1_reward")
end

function WorldsNO1RewardView:ReleaseCallBack()
    if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
	if self.single_match_reward_list then
		self.single_match_reward_list:DeleteMe()
		self.single_match_reward_list = nil
	end
	if self.season_reward_list then
		self.season_reward_list:DeleteMe()
		self.season_reward_list = nil
	end
end

function WorldsNO1RewardView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.WorldsNO1.RewardTitle
	self:SetSecondView(nil, self.node_list["size"])
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:Init(Language.WorldsNO1.RewardTabGroup, nil, "uis/view/worlds_no1_ui_prefab")
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		self.tabbar:SetInstanceParent(self.node_list["TabbarContainer"])
		self.tabbar:SetLocalPosition(0, 0, 0)
	end
	self.single_match_reward_list = AsyncListView.New(WorldsNO1RewardItem, self.node_list["single_match_reward_list"]) 		-- 单场奖励
	self.season_reward_list = AsyncListView.New(WorldsNO1RewardItem, self.node_list["season_reward_list"]) 					-- 赛季奖励
end

function WorldsNO1RewardView:ShowIndexCallBack(index)
	self:Flush(index)
end

function WorldsNO1RewardView:OnFlush()
	local index = self:GetShowIndex()
	local data_list = {}
	if index == TabIndex.worlds_no1_single_match_reward then
		self.node_list["single_match_reward_list"]:SetActive(true)
		self.node_list["season_reward_list"]:SetActive(false)
		data_list = WorldsNO1WGData.Instance:GetSingleMatchRewardCfg()
		self.single_match_reward_list:SetDataList(data_list)

	elseif index == TabIndex.worlds_no1_season_reward then
		self.node_list["season_reward_list"]:SetActive(true)
		self.node_list["single_match_reward_list"]:SetActive(false)
		data_list = WorldsNO1WGData.Instance:GetSeasonRewardCfg()
		self.season_reward_list:SetDataList(data_list)
	end
end


------------------------------- 排名奖励item ----------------------------------------
WorldsNO1RewardItem = WorldsNO1RewardItem or BaseClass(BaseRender)
function WorldsNO1RewardItem:__init()
	self.item_list = AsyncListView.New(WorldsNO1RewardItemCell, self.node_list.item_list)
end

function WorldsNO1RewardItem:__delete()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end

function WorldsNO1RewardItem:OnFlush()
	--奖励物品
	local data_list = {}
	for i = 0, #self.data.reward_item do
		table.insert(data_list, self.data.reward_item[i])
	end
	self.item_list:SetDataList(data_list)

	-- 排名
	if self.data.rank_min == self.data.rank_max then
		local rank = self.data.rank_min
		self.node_list["rank_text"].text.text = string.format(Language.WorldsNO1.Rank, rank)
	else
		self.node_list["rank_text"].text.text = string.format(Language.WorldsNO1.RankRnage, self.data.rank_min, self.data.rank_max)
	end
end


------------------------------- 单个物品格子 ----------------------------------------
WorldsNO1RewardItemCell = WorldsNO1RewardItemCell or BaseClass(BaseRender)
function WorldsNO1RewardItemCell:__init()
	self.cell = ItemCell.New(self.node_list.pos)
end

function WorldsNO1RewardItemCell:__delete()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end

function WorldsNO1RewardItemCell:OnFlush()
	self.cell:SetData(self.data)
end