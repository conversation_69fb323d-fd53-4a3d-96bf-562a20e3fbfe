﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

using UnityEngine.Rendering;

public class YYFullScreenClouds : MonoBehaviour {


    [Header("Full Screen Clouds - profile")]
    [Tooltip("Cloud profile")]
    [SerializeField]
    private YYCloudProfile _CloudProfile;

    public Material Material;

    public CameraEvent RenderQueue = CameraEvent.BeforeImageEffectsOpaque;

    private YYFullScreenEffect effect;

    private System.Action<WeatherCommandBuffer> preSetupCommandBufferAction;
    private void OnEnable()
    {
        preSetupCommandBufferAction = PreSetupCommandBuffer;
        EnsureProfile();
        if (YYCommandBufferManager.Instance != null)
        {
            YYCommandBufferManager.Instance.RegisterPreCull(CameraPreCull, this);
        }
    }

    private void OnDisable()
    {
        if (effect != null)
        {
            effect.Dispose();
        }

        //WeatherMapRenderTexture = WeatherMakerFullScreenEffect.DestroyRenderTexture(WeatherMapRenderTexture);
        //CloudShadowRenderTexture = WeatherMakerFullScreenEffect.DestroyRenderTexture(CloudShadowRenderTexture);
        //weatherMapMaterialCopy.Dispose();

        //if (weatherMapCommandBuffer != null)
        //{
        //    weatherMapCommandBuffer.Clear();
        //    weatherMapCommandBuffer.Release();
        //    weatherMapCommandBuffer = null;
        //}

    }

    private void EnsureProfile()
    {
        if (effect == null)
        {
            effect = new YYFullScreenEffectClouds// new YYFullScreenEffect
            {
                CommandBufferName = "WeatherMakerFullScreenCloudsScript",
                RenderQueue = RenderQueue
            };
        }
    }

    ///   /////////////////////


    private void LateUpdate()  // 更新参数
    {
        //preSetupCommandBufferAction()
        //effect.SetupEffect(Material,preSetupCommandBufferAction,false);
        effect.SetupEffect(Material,null, null, false, preSetupCommandBufferAction);
    }

    private void CameraPreCull(Camera camera)  //更新渲染
    {
        effect.PreCullCamera(camera);
    }


    private void PreSetupCommandBuffer(WeatherCommandBuffer cmdBuffer)
    {
        //if(cmdBuffer.Material==null)
        //{
        //    Debug.Log("PreSetupCommandBuffer erro ");
        //}
        ///
        RenderCelestialObject(YYLightManager.Instance.SunPerspective, cmdBuffer.CommandBuffer);
    }

    private void RenderCelestialObject(YYCelestialObject obj, CommandBuffer cmd)
    {
        if (obj.MeshFilter == null || obj.MeshFilter.sharedMesh == null || obj.Renderer == null || obj.Renderer.sharedMaterial == null)
        {
            Debug.Log("RenderCelestialObject fail");
            return;
        }

        cmd.DrawMesh(obj.MeshFilter.sharedMesh, Matrix4x4.TRS(obj.transform.position, obj.transform.rotation, obj.transform.lossyScale), obj.Renderer.sharedMaterial);

    }

    private static YYFullScreenClouds instance;
    /// <summary>
    /// Shared instance of full screen clouds script
    /// </summary>
    public static YYFullScreenClouds Instance
    {
        get { return YYWeather.FindOrCreateInstance(ref instance); }
    }

    public YYCloudProfile CloudProfile
    {
        get { return _CloudProfile; }
        //set { ShowCloudsAnimated(value, 0.0f, 5.0f); }
    }
}
