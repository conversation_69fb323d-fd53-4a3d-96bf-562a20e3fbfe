function YanYuGePrivilegeView:ZZTQLoadCallBack()
    if not self.zztq_privilege_list then
        self.zztq_privilege_list = AsyncListView.New(YYGZZTQItemCellRender, self.node_list.zztq_privilege_list)
    end
end

function YanYuGePrivilegeView:ZZTQShowIndexCallBack()
end

function YanYuGePrivilegeView:ZZTQReleaseCallBack()
    if self.zztq_privilege_list then
        self.zztq_privilege_list:DeleteMe()
        self.zztq_privilege_list = nil
    end
end

function YanYuGePrivilegeView:ZZTQOnFlush(param_t)
    local data_list = YanYuGeWGData.Instance:GetZZTQDataList()
    self.zztq_privilege_list:SetDataList(data_list)
end

--------------------------YYGZZTQItemCellRender-----------------------
YYGZZTQItemCellRender = YYGZZTQItemCellRender or BaseClass(BaseRender)

function YYGZZTQItemCellRender:LoadCallBack()
    if not self.desc_tip_list then
        self.desc_tip_list = AsyncListView.New(YYGZZTQDescTipItemCellRender, self.node_list.desc_tip_list)
    end

    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        -- AsyncBaseGrid.New()
        -- local t = {col = 3,
        --     change_cells_num = 1,
        --     list_view = self.node_list.reward_list,
        --     itemRender = ItemCell
        -- }

        -- self.reward_list:CreateCells(t)
        self.reward_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list["btn_buy"],BindTool.Bind(self.OnClickBuy, self))
end

function YYGZZTQItemCellRender:__delete()
    if self.desc_tip_list then
        self.desc_tip_list:DeleteMe()
        self.desc_tip_list = nil
    end

    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function YYGZZTQItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local effect_name = self.data.effect_name

    if effect_name and "" ~= effect_name then
        local bundle, asset = ResPath.GetUIEffect(effect_name)
        self.node_list.effect:ChangeAsset(bundle, asset)
        self.node_list.effect:CustomSetActive(true)
    else
        self.node_list.effect:CustomSetActive(false)
    end

    
    local bundle, asset = ResPath.GetRawImagesPNG(self.data.bg)
    self.node_list["bg"].raw_image:LoadSprite(bundle, asset, function()
        self.node_list["bg"].raw_image:SetNativeSize()
    end)

    local privilege_lable = string.split(self.data.privilege_lable, "|")
    local desc_tab = {}
    if not IsEmptyTable(privilege_lable) then
        for k, v in pairs(privilege_lable) do
            if "" ~= v then
                table.insert(desc_tab, {desc = v, sign_bg = self.data.sign_icon})        
            end
        end
    end

    self.desc_tip_list:SetDataList(desc_tab)

    local is_get_privilege = YanYuGeWGData.Instance:IsTeQuanUnLockBySeq(self.data.seq)
    local is_get_tequan_reward = YanYuGeWGData.Instance:IsGetTequanReward(self.data.seq)
    local is_has_day_reward = YanYuGeWGData.Instance:IsTequanHasDayReward(self.data.seq)
    local is_get_reward = YanYuGeWGData.Instance:IsGetTeQuanEveryDayReward(self.data.seq)

    self.node_list.flag_is_get:CustomSetActive(is_get_privilege and is_get_tequan_reward and (not is_has_day_reward or (is_has_day_reward and is_get_reward)))
    self.node_list.btn_buy:CustomSetActive(not is_get_privilege or not is_get_tequan_reward or (is_has_day_reward and not is_get_reward))
    self.node_list.btn_buy_remind:CustomSetActive(is_get_privilege and (not is_get_reward or (is_has_day_reward and not is_get_reward)))
    self.node_list.desc_fanli_tip.tmp.text = self.data.rebate_lable
    self.node_list.desc_fanli_tip:CustomSetActive(not is_get_privilege or not is_get_tequan_reward or (is_has_day_reward and not is_get_reward)) 

    if is_get_privilege then
        local reward_data = self.data.day_reward_item
        -- 领取特权奖励
        if not is_get_tequan_reward then
            reward_data = self.data.reward_item
            self.node_list.desc_buy.tmp.text = Language.YanYuGe.TeQuanCanLingQuStr
        elseif is_has_day_reward and not is_get_reward then
            -- 每日奖励
            self.node_list.desc_buy.tmp.text = Language.YanYuGe.TeQuanDayRewardCanLingQuStr
        end

        self.reward_list:SetDataList(reward_data)
    else
        local desc_buy_str = ""
        if self.data.last_seq >= 0 and not YanYuGeWGData.Instance:IsTeQuanUnLockBySeq(self.data.last_seq) then 
            local tequan_cfg = YanYuGeWGData.Instance:GetTeQuanCfgBySeq(self.data.last_seq)
            desc_buy_str = string.format(Language.YanYuGe.TeQuanUnLockLastTipStr, tequan_cfg.name)
        else
            if self.data.buy_type == 1 then
                desc_buy_str = RoleWGData.GetPayMoneyStr(self.data.price, self.data.rmb_type, self.data.rmb_seq)
            elseif self.data.buy_type == 2 then
                desc_buy_str = string.format(Language.YanYuGe.ScoreStr, self.data.price)
            elseif self.data.buy_type == 3 then
                desc_buy_str = string.format(Language.YanYuGe.ZZTQRealRechargeNumStr, RoleWGData.GetPayMoneyStr(self.data.price, self.data.rmb_type, self.data.rmb_seq))
            end
        end
    
        self.node_list.desc_buy.tmp.text = desc_buy_str
        self.reward_list:SetDataList(self.data.reward_item)
    end


    -- if is_get_privilege then
    --     self.node_list.btn_buy_remind:CustomSetActive(false)
    -- else
    --     local desc_buy_str = ""

    --     if self.data.buy_type == 1 then
    --         desc_buy_str = RoleWGData.GetPayMoneyStr(self.data.price, self.data.rmb_type, self.data.rmb_seq)
    --     else
    --         desc_buy_str = string.format(Language.YanYuGe.ScoreStr, self.data.price)
    --     end
    
    --     self.node_list.desc_buy.tmp.text = desc_buy_str

    --     local score = YanYuGeWGData.Instance:GetCurScore()
    --     self.node_list.btn_buy_remind:CustomSetActive(self.data.buy_type == 2 and score >= self.data.price)
    -- end
end

function YYGZZTQItemCellRender:OnClickBuy()
    -- if self.data.buy_type == 1 then
    --     RechargeWGCtrl.Instance:Recharge(self.data.price, self.data.rmb_type, self.data.rmb_seq)
    -- else
    --     local score = YanYuGeWGData.Instance:GetCurScore()
    --     if score >= self.data.price then
    --         YanYuGeWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.BUY_TEQUAN, self.data.seq)
    --     else
    --         SysMsgWGCtrl.Instance:ErrorRemind(Language.YanYuGe.NoEnoughScore)
    --     end
    -- end

    if self.data.last_seq >= 0 then
        if not YanYuGeWGData.Instance:IsTeQuanUnLockBySeq(self.data.last_seq) then
            local tequan_cfg = YanYuGeWGData.Instance:GetTeQuanCfgBySeq(self.data.last_seq)
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.YanYuGe.TeQuanUnLockLastTipStr, tequan_cfg.name))
            return
        end
    end

    local is_get_privilege = YanYuGeWGData.Instance:IsTeQuanUnLockBySeq(self.data.seq)

    if not is_get_privilege then
        if self.data.buy_type == 1 then
            RechargeWGCtrl.Instance:Recharge(self.data.price, self.data.rmb_type, self.data.rmb_seq)
        elseif self.data.buy_type == 2 then
            local score = YanYuGeWGData.Instance:GetCurScore()
            if score >= self.data.price then
                YanYuGeWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.BUY_TEQUAN, self.data.seq)
            else
                SysMsgWGCtrl.Instance:ErrorRemind(Language.YanYuGe.NoEnoughScore)
            end
        elseif self.data.buy_type == 3 then
            local real_recharge_num = YanYuGeWGData.Instance:GetRealRechargeNum()

            if real_recharge_num < self.data.price then
                SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.YanYuGe.RealRechargeNumNotEnoughStr, RoleWGData.GetPayMoneyStr(self.data.price, self.data.rmb_type, self.data.rmb_seq)))
                ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz)
            end
        end
    else
        local is_get_tequan_reward = YanYuGeWGData.Instance:IsGetTequanReward(self.data.seq)
        if not is_get_tequan_reward then
            YanYuGeWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.FETCH_TEQUAN_REWARD, self.data.seq)
        else
            local is_has_day_reward = YanYuGeWGData.Instance:IsTequanHasDayReward(self.data.seq)
            local is_get_privilege_reward = YanYuGeWGData.Instance:IsGetTeQuanEveryDayReward(self.data.seq)
            if is_has_day_reward and not is_get_privilege_reward then
                YanYuGeWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.FETCH_EVERY_DAY_REWARD, self.data.seq)
            end
        end
    end
end

-----------------------------YYGZZTQDescTipItemCellRender-------------------------
YYGZZTQDescTipItemCellRender = YYGZZTQDescTipItemCellRender or BaseClass(BaseRender)

function YYGZZTQDescTipItemCellRender:OnFlush()
        if IsEmptyTable(self.data) then
        return
    end

    self.node_list.desc_content.tmp.text = self.data.desc
end