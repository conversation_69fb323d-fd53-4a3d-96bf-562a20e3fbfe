ProfessSelectFriendView = ProfessSelectFriendView or BaseClass(SafeBaseView)

function ProfessSelectFriendView:__init()
	self.view_layer = UiLayer.Pop
    self:SetMaskBg(true)
    self.view_name = "ProfessSelectFriendView"
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(814, 578)})
    self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_profess_select_friend_view")
    self.scroller_data = {}
end

function ProfessSelectFriendView:ReleaseCallBack()
	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view = nil
	end

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
	-- 清理变量和对象
	self.scroller = nil
	self.view_index = nil
end

function ProfessSelectFriendView:LoadCallBack()
	-- self.node_list["layout_commmon_second_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
	-- self.node_list["layout_commmon_second_root"].rect.sizeDelta =self.node_list.size.rect.sizeDelta
	-- if not self.tabbar then
	-- 	self.tabbar = Tabbar.New(self.node_list)
	-- 	self.tabbar:Init(nil, Language.ProfessWall.TabGrop2)
	-- 	self.tabbar:SetSelectCallback(BindTool.Bind(self.OnClickToggle, self))
	-- end
	self.list_view = AsyncListView.New(ProfessFriendListCell, self.node_list["ph_fbfriend_list"])
	self.list_view:SetSelectCallBack(BindTool.Bind1(self.OnClickListViewCallBack, self))

	XUI.AddClickEventListener(self.node_list["btn_invite_friend"], BindTool.Bind(self.SureOnClick, self))
	XUI.AddClickEventListener(self.node_list["btn_flush"], BindTool.Bind(self.OnClickRefresh, self))
end

function ProfessSelectFriendView:ShowIndexCallBack(index)
	self.view_index = index
	if index == TabIndex.profess_wall_select_friend then
		self.node_list["title_view_name"].text.text = Language.ProfessWall.SelectFriendTitle
		self.scroller_data =  ProfessWallWGCtrl.Instance:GetMyFriendList()--SocietyWGData.Instance:GetFriendList()
	elseif index == TabIndex.send_flower_obj then
		self.node_list["title_view_name"].text.text = Language.Flower.ViewPanelName
		self.scroller_data =  SocietyWGData.Instance:GetFriendList2()--SocietyWGData.Instance:GetFriendList()
		table.insert(self.scroller_data, 1, self:CreatSelfData())
	end
	
	--SocietyWGCtrl.Instance:SendGetRandomRoleList()
	--self.select_type_index = TabIndex.profess_wall_select_friend                           --好友
	self.list_view:CancelSelect()
	self:Flush()
end

function ProfessSelectFriendView:OnClickListViewCallBack( item )
	local data = item:GetData()
	self:SetSelectIndex(1)
	self:SetSelectFriend(data)
end

function ProfessSelectFriendView:CloseCallBack()
	self.select_index = nil
end

function ProfessSelectFriendView:SetCallBack(callback)
	self.callback = callback
end

function ProfessSelectFriendView:SureOnClick()
	if not self.select_index then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.SelectAddFriendItemTips)
		return
	end
	self.callback(self.select_friend_info)
	self:Close()
end

-- function ProfessSelectFriendView:OnClickToggle(index)
-- 	--self.select_type_index = index
-- 	self:OnClickRefresh()
-- end

function ProfessSelectFriendView:SetSelectIndex(index)
	self.select_index = index
end

function ProfessSelectFriendView:SetSelectFriend(info)
	self.select_friend_info = info
end

function ProfessSelectFriendView:OnFlush()
	self.node_list.no_friend:SetActive(#self.scroller_data <= 0)
	self.node_list.ph_fbfriend_list:SetActive(#self.scroller_data > 0)
	self.list_view:SetDataList(self.scroller_data)
	if self.scroller_data[1] and 1 == self.scroller_data[1].is_love_id then
		self.list_view:SelectIndex(1) --默认选中情侣
	end
end

function ProfessSelectFriendView:OnClickRefresh()
	--if self.select_type_index == TabIndex.profess_wall_select_friend then
	if self.view_index == TabIndex.profess_wall_select_friend then
		self.scroller_data =  ProfessWallWGCtrl.Instance:GetMyFriendList()
	elseif self.view_index == TabIndex.send_flower_obj then
		self.scroller_data =  SocietyWGData.Instance:GetFriendList2()
		table.insert(self.scroller_data, 1, self:CreatSelfData())
	end
	-- else
	-- 	SocietyWGCtrl.Instance:SendGetRandomRoleList()
	-- 	self.scroller_data = SocietyWGData.Instance:GetAutoaddfriendList()
	-- end
	self:Flush()
end

function ProfessSelectFriendView:CreatSelfData()
	local role_vo = RoleWGData.Instance.role_vo
	local self_data = {}
	self_data.is_myself = true
	self_data.is_online = 1
	self_data.is_love_id = 0
	self_data.gamename = role_vo.name
	self_data.user_id = role_vo.origin_uid
	self_data.sex = role_vo.sex
	self_data.prof = role_vo.prof
	self_data.level = role_vo.level
	self_data.charm = role_vo.all_charm
	self_data.fashion_photoframe = role_vo.appearance.fashion_photoframe
	return self_data
end

----------------------------------------------------------------------------
--ProfessFriendListCell 		好友滚动条格子
----------------------------------------------------------------------------

ProfessFriendListCell = ProfessFriendListCell or BaseClass(BaseRender)

function ProfessFriendListCell:__init()
	-- 获取变量
end

function ProfessFriendListCell:OnFlush()
	if self.data == nil then
		return
	end
	self.node_list["lbl_name"].text.text = self.data.gamename

	XUI.UpdateRoleHead(self.node_list["icon"], self.node_list["custom_role_head"], self.data.user_id, self.data.sex,self.data.prof, false,nil,true)
	local is_vis, level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
	self.node_list["dianfen_img"]:SetActive(is_vis)
	if is_vis then
		self.node_list["TextLevel"].text.text = level
	else
		self.node_list["TextLevel"].text.text = level ..Language.ProfessWall.level
	end
	
	self.node_list["qinmidu"].text.text = self.data.intimacy or "-"
	self.node_list["is_lover_image"]:SetActive(self.data.is_love_id == 1)
	self.node_list["label_prof"]:SetActive(self.data.is_love_id ~= 1)
	self.node_list["label_prof"].text.text = self.data.is_myself and Language.Society.Relationship[2] or Language.Society.Relationship[1]
	--self.node_list.img9_bg:SetActive(self.index % 2 == 0 )
end

function ProfessFriendListCell:OnSelectChange(is_select)
	self.node_list["img_choose"]:SetActive(is_select)--高亮
end