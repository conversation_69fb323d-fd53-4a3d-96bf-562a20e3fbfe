--武魂特权抽奖概率展示面板.
WuHunPrerogativeProbabilityView = WuHunPrerogativeProbabilityView or BaseClass(SafeBaseView)

function WuHunPrerogativeProbabilityView:__init()
	self.view_layer = UiLayer.Normal
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
		{ sizeDelta = Vector2(866, 516) })
	self:AddViewResource(0, "uis/view/wuhunzhenshen_prefab", "layout_wuhun_prerogative_probability")
	self:SetMaskBg(true, true)
end

function WuHunPrerogativeProbabilityView:ReleaseCallBack()
	if self.probability_list then
		self.probability_list:DeleteMe()
		self.probability_list = nil
	end
end

function WuHunPrerogativeProbabilityView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.WuHunPrerogative.ProbabilityTitle
	if not self.probability_list then
		self.probability_list = AsyncListView.New(WuHunPrerogative, self.node_list.ph_pro_list)
	end
end

function WuHunPrerogativeProbabilityView:OnFlush()
	local info = WuHunWGData.Instance:GetWuhunPrerogativeDrawProbabilityCfg()
	if info then
		self.probability_list:SetDataList(info)
	end
end

----------------------------------------------------------------------------------
WuHunPrerogative = WuHunPrerogative or BaseClass(BaseRender)
function WuHunPrerogative:OnFlush()
	if not self.data then
		return
	end

	self.node_list.bg:SetActive(self.index % 2 == 1)
	self.node_list.index_text.text.text = self.index
	local color = ItemWGData.Instance:GetItemColor(self.data.item.item_id)
	local item_name = ItemWGData.Instance:GetItemName(self.data.item.item_id)
	self.node_list.name_text.text.text = string.format(Language.WuHunPrerogative.GaiLvText,
		ToColorStr(item_name, color), self.data.item.num)
	self.node_list.probability_text.text.text = self.data.random_count .. "%"
end
