ControlBeastsRefiningLevelView = ControlBeastsRefiningLevelView or BaseClass(SafeBaseView)

function ControlBeastsRefiningLevelView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_beasts_root_level_view")
end

function ControlBeastsRefiningLevelView:__delete()

end

function ControlBeastsRefiningLevelView:ReleaseCallBack()
    if self.refining_level_list then
		self.refining_level_list:DeleteMe()
		self.refining_level_list = nil
	end
end

function ControlBeastsRefiningLevelView:LoadCallBack()
    self.refining_level_list = AsyncListView.New(RefiningLevelRender, self.node_list.ScrollView)
end


function ControlBeastsRefiningLevelView:OnFlush()
    local data = ControlBeastsWGData.Instance:GetStarCircleBeastLevelCfg()
    self.refining_level_list:SetDataList(data)
end

---------------------------------------------------------
RefiningLevelRender = RefiningLevelRender or BaseClass(BaseRender)

function RefiningLevelRender:OnFlush()
	if not self.data then return end

	if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end

    self.node_list.lunhui_text.text.text = string.format(Language.ContralBeasts.LunHuiText, self.data.total_array_level_end)
    self.node_list.level_text.text.text = string.format(Language.ContralBeasts.MaxlevelText, self.data.beast_max_level)
end

