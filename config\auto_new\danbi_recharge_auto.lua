-- D-单笔充值.xls
local item_table={
[1]={item_id=22012,num=1,is_bind=1},
[2]={item_id=26353,num=1,is_bind=1},
[3]={item_id=26351,num=1,is_bind=1},
[4]={item_id=26516,num=1,is_bind=1},
[5]={item_id=26501,num=1,is_bind=1},
[6]={item_id=46044,num=5,is_bind=1},
[7]={item_id=46048,num=1,is_bind=1},
[8]={item_id=22615,num=1,is_bind=1},
[9]={item_id=30424,num=5,is_bind=1},
[10]={item_id=44184,num=1,is_bind=1},
[11]={item_id=26409,num=5,is_bind=1},
[12]={item_id=26191,num=1,is_bind=1},
[13]={item_id=26354,num=1,is_bind=1},
[14]={item_id=26363,num=1,is_bind=1},
[15]={item_id=46561,num=1,is_bind=1},
[16]={item_id=48071,num=2,is_bind=1},
[17]={item_id=43805,num=5,is_bind=1},
[18]={item_id=26504,num=1,is_bind=1},
[19]={item_id=46502,num=5,is_bind=1},
[20]={item_id=48071,num=3,is_bind=1},
[21]={item_id=26409,num=10,is_bind=1},
[22]={item_id=39107,num=5,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
danbi_cfg={
{reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{id=1,danbi_recharge_num=30,reward_item={[0]=item_table[7],[1]=item_table[8],[2]=item_table[9],[3]=item_table[10],[4]=item_table[11],[5]=item_table[6]},},
{id=2,danbi_recharge_num=68,reward_item={[0]=item_table[12],[1]=item_table[13],[2]=item_table[14],[3]=item_table[15],[4]=item_table[16],[5]=item_table[17]},},
{id=3,danbi_recharge_num=98,},
{id=4,danbi_recharge_num=128,},
{id=5,danbi_recharge_num=198,},
{id=6,danbi_recharge_num=328,},
{id=7,danbi_recharge_num=648,},
{id=8,danbi_recharge_num=1000,},
{id=9,danbi_recharge_num=2000,},
{id=10,danbi_recharge_num=5000,},
{id=11,danbi_recharge_num=10000,},
{id=12,danbi_recharge_num=20000,},
{limit_server_day_min=7,limit_server_day_max=13,},
{limit_server_day_min=7,limit_server_day_max=13,},
{limit_server_day_min=7,limit_server_day_max=13,},
{id=3,danbi_recharge_num=98,},
{id=4,danbi_recharge_num=128,},
{id=5,danbi_recharge_num=198,},
{id=6,danbi_recharge_num=328,},
{id=7,danbi_recharge_num=648,},
{id=8,danbi_recharge_num=1000,},
{id=9,danbi_recharge_num=2000,},
{id=10,danbi_recharge_num=5000,},
{id=11,danbi_recharge_num=10000,},
{limit_server_day_min=7,limit_server_day_max=13,},
{limit_server_day_min=14,limit_server_day_max=9999,},
{limit_server_day_min=14,limit_server_day_max=9999,},
{limit_server_day_min=14,limit_server_day_max=9999,},
{limit_server_day_min=14,limit_server_day_max=9999,},
{id=4,danbi_recharge_num=128,},
{id=5,danbi_recharge_num=198,},
{id=6,danbi_recharge_num=328,},
{id=7,danbi_recharge_num=648,},
{id=8,danbi_recharge_num=1000,},
{id=9,danbi_recharge_num=2000,},
{id=10,danbi_recharge_num=5000,},
{id=11,danbi_recharge_num=10000,},
{limit_server_day_min=14,limit_server_day_max=9999,}
},

danbi_cfg_meta_table_map={
[27]=1,	-- depth:1
[14]=27,	-- depth:2
[30]=4,	-- depth:1
[31]=30,	-- depth:2
[32]=30,	-- depth:2
[36]=30,	-- depth:2
[34]=30,	-- depth:2
[35]=30,	-- depth:2
[37]=30,	-- depth:2
[33]=30,	-- depth:2
[26]=13,	-- depth:1
[20]=26,	-- depth:2
[24]=26,	-- depth:2
[23]=26,	-- depth:2
[22]=26,	-- depth:2
[21]=26,	-- depth:2
[38]=30,	-- depth:2
[19]=26,	-- depth:2
[18]=26,	-- depth:2
[17]=26,	-- depth:2
[25]=26,	-- depth:2
[39]=26,	-- depth:2
[28]=2,	-- depth:1
[29]=3,	-- depth:1
[16]=29,	-- depth:2
[15]=28,	-- depth:2
},
other_default_table={show_fabao_id=10106,},

danbi_cfg_default_table={limit_server_day_min=1,limit_server_day_max=6,id=0,danbi_recharge_num=6,reward_item={[0]=item_table[18],[1]=item_table[19],[2]=item_table[20],[3]=item_table[21],[4]=item_table[17],[5]=item_table[22]},}

}

