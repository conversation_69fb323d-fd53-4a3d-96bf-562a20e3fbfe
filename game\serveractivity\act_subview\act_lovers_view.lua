----------------------------------------------------
--开服活动 -- 完美情人
----------------------------------------------------

function ServerActivityTabView:LoversReleaseCallBack()
	if self.lovers_competition_list ~= nil then 
		self.lovers_competition_list:DeleteMe()
		self.lovers_competition_list = nil
	end
	if self.lovers_item_list then
		for k,v in pairs(self.lovers_item_list) do
			v:DeleteMe()
		end
		self.lovers_item_list = nil
	end

	if self.xianwa_display then
		self.xianwa_display:DeleteMe()
		self.xianwa_display = nil
	end
	CountDownManager.Instance:RemoveCountDown("Lovers_countdown")
end

function ServerActivityTabView:LoversLoadCallBack()
	self.node_list.lover_version_act_des.text.text = Language.OpenServer.LoverActDesc
	self:LoversInitLeftPanel()
	self:LoversCreateListView()
	self:LoversFlushCountDownTime()
	ServerActivityWGData.Instance:SetLoginLoversRemind(false)
end

function ServerActivityTabView:LoversCreateListView()
	self.lovers_competition_list = AsyncListView.New(OpenServerLoversItemRender, self.node_list.lover_ph_list)
	XUI.AddClickEventListener(self.node_list.lover_btn_marry, BindTool.Bind1(self.LoversOnClickMarry, self))
end

function ServerActivityTabView:WMQRShowIndexCallBack()
	self:DoLoversAnim()
end

function ServerActivityTabView:LoversFlushCountDownTime()
	CountDownManager.Instance:RemoveCountDown("Lovers_countdown")
	local count_down_time = ServerActivityWGData.Instance:GetOpenServerToSevenDayTime(ACTIVITY_TYPE.LOVERS_FOREVER)
	if count_down_time > 0 then
		self.node_list.lover_lbl_activity_time.text.text = TimeUtil.FormatSecondDHM8(count_down_time)
		CountDownManager.Instance:AddCountDown(
			"Lovers_countdown",
			BindTool.Bind(self.LoversCountDownTimeCallBack, self),
			BindTool.Bind(self.LoversFlushCountDownTime, self),
			nil,
			count_down_time,
			1
		)
	else
		self.node_list.lover_lbl_activity_time.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
		self.node_list.lover_lbl_activity_time.text.color = Str2C3b(COLOR3B.RED)
	end
end

function ServerActivityTabView:LoversCountDownTimeCallBack(elapse_time, total_time)
	self.node_list.lover_lbl_activity_time.text.text = TimeUtil.FormatSecondDHM8(total_time - elapse_time)
end

function ServerActivityTabView:LoversInitLeftPanel()
	local opengame_cfg = ServerActivityWGData.Instance:GetOpenGameActivityConfig()
	local perfect_lover_cfg = opengame_cfg.perfect_lover[1]

	-- if perfect_lover_cfg.reward_item then
	-- 	local item_list = {}
	-- 	local reward_list = SortTableKey(perfect_lover_cfg.reward_item)
	-- 	for i=1,#reward_list do
	-- 		item_list[i] = ItemCell.New(self.node_list.lover_cell_root)
	-- 		item_list[i]:SetData(reward_list[i])
	-- 	end
	-- 	self.lovers_item_list = item_list
	-- end
	
	local title_bundle,title_asset = ResPath.GetRoleTitle(perfect_lover_cfg.title_id)
	self.node_list["title_root"].image:LoadSprite(title_bundle, title_asset, function()
		self.node_list["title_root"].image:SetNativeSize()
	end)

	self.node_list.lover_lbl_zhandoiuli.text.text = perfect_lover_cfg.capability_show or 0

	self.xianwa_display = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["lover_cell_root"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.S,
		can_drag = true,
	}
	
	self.xianwa_display:SetRenderTexUI3DModel(display_data)
	-- self.xianwa_display:SetUI3DModel(self.node_list["lover_cell_root"].transform, self.node_list["lover_cell_root"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
	self:AddUiRoleModel(self.xianwa_display)

	if self.xianwa_display then
		local bundle, asset = ResPath.GetHaiZiModel(perfect_lover_cfg.resource_id)
		if bundle == nil and asset == nil then
			return
		end

		self.xianwa_display:SetMainAsset(bundle, asset)
	end
end

function ServerActivityTabView:LoversOnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" or (k == "act_info" and v.protocol_id == 2718) then
			self:LoversRefreshView()
			break
		end
	end
end

function ServerActivityTabView:LoversRefreshView()
	-- 排行的数据
	-- local opengame_info = ServerActivityWGData.Instance:GetOpenServerData()
	-- local data_list = {}
	-- for i=1,10 do
	-- 	data_list[i] = opengame_info.oga_couple_list[i] or {}
	-- end
	--结婚三个档次的数据
	local marry_cfg = MarryWGData.Instance:GetMarryCfg() or {}
	self.lovers_competition_list:SetDataList(marry_cfg)


	local opengame_info = ServerActivityWGData.Instance:GetOpenServerData()
	local marry_flag = opengame_info.oga_marry_type_record_flag --当前已结婚次数
	local total_times = #marry_cfg --需要结婚的次数
	local is_finish = marry_flag >= total_times

	self.node_list.lover_btn_marry:SetActive(not is_finish)
	self.node_list.lover_get_reward_img:SetActive(is_finish)
	local color = COLOR3B.D_GREEN
	local str_times = ToColorStr(marry_flag, color)
	self.node_list.marry_txt.text.text = string.format(Language.Marry.ActLoversViewTimes, str_times, total_times)
	
end

function ServerActivityTabView:LoversOnClickMarry()
	MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_LOVER_INFO_REQ)
    MarryWGCtrl.Instance:OpenTiQinView()
end

function ServerActivityTabView:DoLoversAnim()
	local tween_info = UITween_CONSTS.ServerActivityTab
    UITween.FakeHideShow(self.node_list["act_lovers_root"])
    UITween.AlphaShow(GuideModuleName.ServerActivityTabView, self.node_list["act_lovers_root"], 0, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)

	--self:DoLoversCellsAnim()
end

function ServerActivityTabView:DoLoversCellsAnim()
    local tween_info = UITween_CONSTS.ServerActivityTab.ListCellRender
    self.node_list["lover_ph_list"]:SetActive(false)
    ReDelayCall(self, function()
        self.node_list["lover_ph_list"]:SetActive(true)
        local list = self.lovers_competition_list:GetAllItems()
        local sort_list = ServerActivityWGData.Instance:GetSortListView(list)
        local count = 0
        for k,v in ipairs(sort_list) do
            if 0 ~= v.index then
                count = count + 1
            end
            v.item:PalyLoversItemAnim(count)
        end
    end, tween_info.DelayDoTime, "Lovers_Cell_Tween")
end
----------------------------------------------------------------------------------------------------
----------------------------------------------------------------------------------------------------
OpenServerLoversItemRender = OpenServerLoversItemRender or BaseClass(BaseRender)

function OpenServerLoversItemRender:LoadCallBack()
	-- self.node_list.name_label_left.button:AddClickListener(BindTool.Bind(self.OnClickName, self, 1))
	-- self.node_list.name_label_right.button:AddClickListener(BindTool.Bind(self.OnClickName, self, 2))
end

function OpenServerLoversItemRender:SetItemShow(item_data)
	---[[ 排序时装>称号剩余的品质排
    local temp_list = {}
    local item_cfg = nil
    for k,v in pairs(item_data) do
    	item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
    	if item_cfg then
    		if item_cfg.is_display_role and item_cfg.is_display_role > 0 then
    			if item_cfg.is_display_role == 8 then -- 称号
    				temp_list[1000 + k] = v
    			else
    				temp_list[2000 + k] = v
    			end
    		else
    			temp_list[100 * item_cfg.color + k] = v
    		end
    	end
    end
    item_data = SortTableKey(temp_list, true)
	--]]
	if nil == self.marry_item_slot then
		self.marry_item_slot = {}
	end
	local item_num = #item_data
	local item_alearday_num = #self.marry_item_slot
	for i=1,item_num do
		if nil == self.marry_item_slot[i] then
			self.marry_item_slot[i] = ItemCell.New(self.node_list["ph_cell"])
		end
		self.marry_item_slot[i]:SetData(item_data[i])
	end
	for i=1,item_alearday_num do
		if nil ~= self.marry_item_slot[i] then
			self.marry_item_slot[i]:SetActive(i <= item_num)
		end
	end
end

function OpenServerLoversItemRender:OnFlush()
	if not self.data then return end

	local is_aleardy_tiqin = MarryWGData.Instance:GetCurRoleIsTiQin(self.data.marry_type)
	local reward_data_1 = {}

	if is_aleardy_tiqin then
		reward_data_1 = self.data.after_reward_item
	else
		reward_data_1 = self.data.reward_item
	end

	local reward_data = MarryWGData.Instance:ExculeRewardInfo(reward_data_1, nil, true)

	self:SetItemShow(reward_data)

	self.node_list["bg_img"].raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("a1_kfsss_di" .. self.index))
	self.node_list["tab_img"].image:LoadSprite(ResPath.GetKaiFuChongBangUi("a1_kfsss_titlebg_" .. self.index))
	self.node_list["tab_name"].text.text = Language.Marry.MarryTypeTitle[self.index]
	local title_bundle,title_asset = ResPath.GetRoleTitle(self.data.title_item_id)
	self.node_list["img_title"].image:LoadSprite(title_bundle, title_asset, function()
		self.node_list["img_title"].image:SetNativeSize()
	end)
end


function OpenServerLoversItemRender:PalyLoversItemAnim(item_index)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.ServerActivityTab.ListCellRender

    UITween.FakeHideShow(self.node_list["tween_root"])
    ReDelayCall(self, function()
        if self.node_list and self.node_list["tween_root"] then
            UITween.RotateAlphaShow(GuideModuleName.ServerActivityTabView, self.node_list["tween_root"], tween_info)
        end

    end, tween_info.NextDoDelay * wait_index, "lovers_item_" .. wait_index)
end


------------- 完美情人进度
MarryScheduleView = MarryScheduleView or BaseClass(SafeBaseView)

function MarryScheduleView:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "layout_schedule")
	self.record_flag = nil
end

function MarryScheduleView:__delete()
	
end

function MarryScheduleView:LoadCallBack()
	self.mask_bg.image.color = Color.New(0,0,0,0)
end

function MarryScheduleView:ShowIndexCallBack(index)
	self:Flush()
end

function MarryScheduleView:ReleaseCallBack()
	self.record_flag = nil
end

function MarryScheduleView:OnFlush(param_list, index)
	local lover_name = RoleWGData.Instance.role_vo.lover_name
	if lover_name == "" then
		self.node_list.lbl_lover_name.text.text = Language.Common.ZanWu
	else
		self.node_list.lbl_lover_name.text.text = lover_name
	end

	local marry_flag = ServerActivityWGData.Instance:GetOpenServerData().oga_marry_type_record_flag
	if self.record_flag then
		marry_flag = self.record_flag
	end
	local marry_flag_list = {}
	marry_flag_list = bit:d2b(marry_flag)

	for i=1,3 do
		local color = marry_flag_list[33 - i] == 1 and COLOR3B.D_GREEN or COLOR3B.RED
		self.node_list["lbl_falg_" .. i].text.text = ToColorStr(marry_flag_list[33 - i],color) 
	end
end

function MarryScheduleView:SetRecordFlag(flag)
	self.record_flag = flag
end