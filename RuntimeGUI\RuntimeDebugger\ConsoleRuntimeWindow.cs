﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using UnityEngine;

public class ConsoleRuntimeWindow:IDebugWindow
{    
    private static int s_RuneTimeErrorTotalCount = 0;
    private static StringBuilder s_StringBuilder = new StringBuilder();
    private LinkedList<DebugLogItem> m_Logs = new LinkedList<DebugLogItem>();
    private Vector2 m_LogScrollPosition = Vector2.zero;
    private Vector2 m_StackScrollPosition = Vector2.zero;
    private int m_InfoCount = 0;
    private int m_WarningCount = 0;
    private int m_ErrorCount = 0;
    private int m_ExceptionCount = 0;
    private float m_AutoSaveTime = 0;
    private LinkedListNode<DebugLogItem> m_SelectedNode = null;
    private DebugLogWriter m_Writer = new DebugLogWriter();
    private Action m_LogErrorCallBack = null;
    
    [SerializeField]
    private bool m_LockScroll = false;

    [SerializeField]
    private int m_MaxLine = 99;

    [SerializeField]
    private string m_DateTimeFormat = "[HH:mm:ss.fff] ";

    [SerializeField]
    private Color32 m_InfoColor = Color.white;

    [SerializeField]
    private Color32 m_WarningColor = Color.yellow;

    [SerializeField]
    private Color32 m_ErrorColor = Color.red;

//    [SerializeField]
//    private Color32 m_ExceptionColor = Color.red;
    
    [SerializeField]
    private bool m_InfoFilter = true;

    [SerializeField]
    private bool m_WarningFilter = true;

    [SerializeField]
    private bool m_ErrorFilter = true;

    [SerializeField]
//    private bool m_ExceptionFilter = true;
    
    private bool m_LastInfoFilter = true;
    private bool m_LastWarningFilter = true;
    private bool m_LastErrorFilter = true;
    private bool m_LastExceptionFilter = true;

    public void Init()
    {
        Application.logMessageReceived += OnLogMessageReceived;

        m_InfoFilter = m_LastInfoFilter = PlayerPrefHelper.GetBool("runtime_debug_log_filter", true);
        m_WarningFilter = m_LastWarningFilter = PlayerPrefHelper.GetBool("runtime_debug_warning_filter", true);
        m_ErrorFilter = m_LastErrorFilter = PlayerPrefHelper.GetBool("runtime_debug_error_filter", true);
//        m_ExceptionFilter = m_LastExceptionFilter = PlayerPrefHelper.GetBool("runtime_debug_exception_filter", true);
    }

    public void Destroy()
    {
        if (s_RuneTimeErrorTotalCount > 0)
        {
            Debug.Log("create error log file success ");
            Write();
            m_Writer.CopyFile();
            s_RuneTimeErrorTotalCount = 0;
        }
        Application.logMessageReceived -= OnLogMessageReceived;
        m_AutoSaveTime = 0;
        m_LogErrorCallBack = null;
    }

    public void OnDraw()
    {
       RefreshCount();
       GUILayout.BeginHorizontal();
       {
           if (GUILayout.Button("Clear All", GUILayout.Width(100f)))
           {
               Clear();
           }
           
           if (GUILayout.Button("Save", GUILayout.Width(100f)))
           {
               Write();
           }
           
           if (GUILayout.Button("Open Log File", GUILayout.Width(200)))
           {
               OpenLogFile();
           }
           
           if (GUILayout.Button("Open Folder", GUILayout.Width(200)))
           {
               OpenFolder();
           }
           
           m_LockScroll = GUILayout.Toggle(m_LockScroll, "Lock Scroll", GUILayout.Width(90f));
           GUILayout.FlexibleSpace();
           m_InfoFilter = GUILayout.Toggle(m_InfoFilter, string.Format("Info ({0})", m_InfoCount.ToString()), GUILayout.Width(90f));
           m_WarningFilter = GUILayout.Toggle(m_WarningFilter, string.Format("Warning ({0})", m_WarningCount.ToString()), GUILayout.Width(90f));
           m_ErrorFilter = GUILayout.Toggle(m_ErrorFilter, string.Format("Error ({0})", m_ErrorCount.ToString()), GUILayout.Width(90f));
           //m_ExceptionFilter = GUILayout.Toggle(m_ExceptionFilter, string.Format("Fatal ({0})", m_ExceptionCount.ToString()), GUILayout.Width(90f));
        }
       GUILayout.EndHorizontal();

       GUILayout.BeginVertical("box");
       {
           if (m_LockScroll)
           {
               m_LogScrollPosition.y = float.MaxValue;
           }

            m_LogScrollPosition = GUILayout.BeginScrollView(m_LogScrollPosition);
            {
                bool selected = false;
                for (LinkedListNode<DebugLogItem> i = m_Logs.First; i != null; i = i.Next)
                {
                    switch (i.Value.LogType)
                    {
                        case LogType.Log:
                            if (!m_InfoFilter)
                            {
                                continue;
                            }
                            break;
                        case LogType.Warning:
                            if (!m_WarningFilter)
                            {
                                continue;
                            }
                            break;
                        case LogType.Error:
                            if (!m_ErrorFilter)
                            {
                                continue;
                            }
                            break;
//                        case LogType.Exception:
//                            if (!m_ExceptionFilter)
//                            {
//                                continue;
//                            }
//                            break;
                    }
                    if (GUILayout.Toggle(m_SelectedNode == i, GetLogString(i.Value)))
                    {
                        selected = true;
                        if (m_SelectedNode != i)
                        {
                            m_SelectedNode = i;
                            m_StackScrollPosition = Vector2.zero;
                        }
                    }
                }
                if (!selected)
                {
                    m_SelectedNode = null;
                }
            }
            GUILayout.EndScrollView();
        }
       GUILayout.EndVertical();


       GUILayout.BeginVertical("box");
       {
           m_StackScrollPosition = GUILayout.BeginScrollView(m_StackScrollPosition, GUILayout.Height(300));
           {
               if (m_SelectedNode != null)
               {
                   GUILayout.BeginHorizontal();
                   Color32 color = GetLogStringColor(m_SelectedNode.Value.LogType);
                   GUILayout.Label(string.Format("<color=#{0}{1}{2}{3}><b><size=18>{4}</size></b></color>", color.r.ToString("x2"), color.g.ToString("x2"), color.b.ToString("x2"), color.a.ToString("x2"), m_SelectedNode.Value.LogMessage));
                   if (GUILayout.Button("复制", GUILayout.Width(60f), GUILayout.Height(30f)))
                   {
                       TextEditor textEditor = new TextEditor();
                       textEditor.text = string.Format("{0}\n\n{1}", m_SelectedNode.Value.LogMessage, m_SelectedNode.Value.StackTrack);
                       textEditor.OnFocus();
                       textEditor.Copy();
                   }
                   GUILayout.EndHorizontal();
                   GUILayout.Label(string.Format("<size=18>{0}</size>", m_SelectedNode.Value.StackTrack));
               }
               GUILayout.EndScrollView();
           }
       }
       GUILayout.EndVertical();
    }

    public void OnEnter()
    {
    }

    public void OnLeave()
    {
    }

    public void Update()
    {
        if (m_LastInfoFilter != m_InfoFilter)
        {
            m_LastInfoFilter = m_InfoFilter;
            PlayerPrefHelper.SetBool("runtime_debug_log_filter", m_InfoFilter);
        }
        
        if (m_LastWarningFilter != m_WarningFilter)
        {
            m_LastWarningFilter = m_WarningFilter;
            PlayerPrefHelper.SetBool("runtime_debug_warning_filter", m_WarningFilter);
        }

        if (m_LastErrorFilter != m_ErrorFilter)
        {
            m_LastErrorFilter = m_ErrorFilter;
            PlayerPrefHelper.SetBool("runtime_debug_error_filter", m_ErrorFilter);
        }

//        if (m_LastExceptionFilter != m_ExceptionFilter)
//        {
//            m_LastExceptionFilter = m_ExceptionFilter;
//            PlayerPrefHelper.SetBool("runtime_debug_exception_filter", m_ExceptionFilter);
//        }
    }
    
    private void Clear()
    {
        m_Logs.Clear();
    }

    public int InfoCount
    {
        get { return m_InfoCount; }
    }
    public int WarningCount
    {
        get { return m_WarningCount; }
    }
    public int ErrorCount
    {
        get { return m_ErrorCount; }
    }
    public int ExceptionCount
    {
        get { return m_ExceptionCount; }
    }

    public void RefreshCount()
    {
        m_InfoCount = 0;
        m_WarningCount = 0;
        m_ErrorCount = 0;
        for (LinkedListNode<DebugLogItem> i = m_Logs.First; i != null; i = i.Next)
        {
            switch (i.Value.LogType)
            {
                case LogType.Log:
                    m_InfoCount++;
                    break;
                case LogType.Warning:
                    m_WarningCount++;
                    break;
                case LogType.Error:
                    m_ErrorCount++;
                    break;
            }
        }
    }

    public int GetCount(LogType logType)
    {
        switch (logType)
        {
            case LogType.Error:
                return m_ErrorCount;
            case LogType.Warning:
                return m_WarningCount;
            case LogType.Log:
                return m_InfoCount;
            default:
                return 0;
        }
    }

    private void OnLogMessageReceived(string logMessage, string stackTrace, LogType logType)
    {
        if (logType == LogType.Assert || logType == LogType.Exception)
        {
            logType = LogType.Error;
        }

        DebugLogItem item = new DebugLogItem(logType, logMessage, stackTrace);
        if (logType == LogType.Error)
        {
            AppendLog(item);
            s_RuneTimeErrorTotalCount++;
            if (m_LogErrorCallBack != null)
            {
                m_LogErrorCallBack();
            }
        }
        
        m_Logs.AddLast(item);

        RefreshCount();
        if (GetCount(logType) > m_MaxLine)
        {
            RemoveLog(logType);
        }

        CheckAutoSave();
    }

    private void RemoveLog(LogType logType)
    {    
        for (LinkedListNode<DebugLogItem> i = m_Logs.First; i != null; i = i.Next)
        {
            if (i.Value.LogType == logType)
            {
                m_Logs.Remove(i.Value);
            }
        }
    }
    
    private string GetLogString(DebugLogItem debugLogItem)
    {
        Color32 color = GetLogStringColor(debugLogItem.LogType);
        return string.Format("<color=#{0}{1}{2}{3}><size=18>{4}{5}</size></color>",
            color.r.ToString("x2"), color.g.ToString("x2"), color.b.ToString("x2"), color.a.ToString("x2"),
            debugLogItem.LogTime.ToString(m_DateTimeFormat), debugLogItem.FirstRowMessage);
    }

    internal Color32 GetLogStringColor(LogType logType)
    {
        Color32 color = Color.white;
        switch (logType)
        {
            case LogType.Log:
                color = m_InfoColor;
                break;
            case LogType.Warning:
                color = m_WarningColor;
                break;
            case LogType.Error:
                color = m_ErrorColor;
                break;
        }

        return color;
    }

    private void OpenLogFile()
    {
        m_Writer.OpenFile();
    }
    
    private void OpenFolder()
    {
        m_Writer.OpenFileFolder();
    }


    private void Write()
    {
        m_Writer.Write(s_StringBuilder.ToString());
        ClearLogCache();
    }

    private void ClearLogCache()
    {
        s_StringBuilder.Remove(0, s_StringBuilder.Length);
    }
    
    public void AppendLog(DebugLogItem item)
    {
        s_StringBuilder.Append(item.LogTime.ToString(m_DateTimeFormat) + item.LogMessage + "\n");
        s_StringBuilder.Append(item.StackTrack + "\n\n\n");
    }
    
    private void CheckAutoSave()
    {
        if (m_AutoSaveTime < Time.realtimeSinceStartup)
        {
            Write();
            m_AutoSaveTime = Time.realtimeSinceStartup + 15f;
        }
    }

    public void SetErrorListener(Action action)
    {
        m_LogErrorCallBack = action;
    }
}