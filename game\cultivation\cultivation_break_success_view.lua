CultivationBreakSuccessView = CultivationBreakSuccessView or BaseClass(SafeBaseView)

local stage_res = {[0]="a3_jj_tx_tbhq", [1]="a3_jj_tx_tbqx2", [2]="a3_jj_tx_tbzq2"}
function CultivationBreakSuccessView:__init()
	self:SetMaskBg(true,true)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:AddViewResource(0, "uis/view/cultivation_ui_prefab", "layout_cultivation_break_success_view")
end

function CultivationBreakSuccessView:LoadCallBack()
    if not self.attr_list then
		self.attr_list = AsyncListView.New(CommonAddAttrRender, self.node_list.attr_list)
	end
    XUI.AddClickEventListener(self.node_list.btn_ok, BindTool.Bind(self.OnClickBtnOk, self))
end

function CultivationBreakSuccessView:ReleaseCallBack()
    if self.attr_list then
        self.attr_list:DeleteMe()
        self.attr_list = nil
    end
end

function CultivationBreakSuccessView:CloseCallBack()
    local cur_level_cfg = CultivationWGData.Instance:GetCurXiuWeiStageCfg()
    local close_view = (cur_level_cfg or {}).close_view or 0
    
    if close_view == 1 then
        ViewManager.Instance:Close(GuideModuleName.XiuWeiView)
    end
end

function CultivationBreakSuccessView:OnFlush()
    if CultivationWGData.Instance.stage <= 1 then
        self:Close()
    end
    -- 数据
    local old_stage_cfg = CultivationWGData.Instance:GetXiuWeiStageCfgByState(CultivationWGData.Instance.stage - 1)
	local cur_stage_cfg = CultivationWGData.Instance:GetCurXiuWeiStageCfg()

    local is_change_stage = old_stage_cfg.client_stage ~= cur_stage_cfg.client_stage
    self.node_list.stage_level_change_root:CustomSetActive(not is_change_stage)
    self.node_list.stage_change_root:CustomSetActive(is_change_stage)

    if is_change_stage then
        self.node_list.old_stage_image.image:LoadSprite(ResPath.GetXiuWeiImg(old_stage_cfg.stage_title_icon))
        self.node_list.new_stage_img.image:LoadSprite(ResPath.GetXiuWeiImg(cur_stage_cfg.stage_title_icon))
    else
        self.node_list.img_old_stage.image:LoadSprite(ResPath.GetXiuWeiImg(old_stage_cfg.stage_title_icon))
        self.node_list.img_new_stage.image:LoadSprite(ResPath.GetXiuWeiImg(cur_stage_cfg.stage_title_icon))

        local xiuwei_data_list = XiuWeiWGData.Instance:GetShowSmallStageList(cur_stage_cfg.client_stage)

        for k, v in pairs(xiuwei_data_list) do
            if v.stage == old_stage_cfg.stage then
                self.node_list.desc_old_stage.text.text = string.format(Language.XiuWei.Level, NumberToChinaNumber(k))
            end

            if v.stage == cur_stage_cfg.stage then
                self.node_list.desc_new_stage.text.text = string.format(Language.XiuWei.Level, NumberToChinaNumber(k))
            end
        end
    end

    local cur_level_cfg = CultivationWGData.Instance:GetCurXiuWeiLevelCfg()
    local old_level_cfg = CultivationWGData.Instance:GetLastXiuWeiLevelCfg()

	local jinjie_attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(old_stage_cfg, cur_stage_cfg, nil, nil, 1, 7)
	local xiuwei_attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(old_level_cfg, cur_level_cfg, nil, nil, 1, 7)

    local total_attr_list = EquipWGData.MergeAttrList(jinjie_attr_list,xiuwei_attr_list)
    
    -- 没增加的不显示
    local temp_attr_list ={}
    for k, v in pairs(total_attr_list) do
        if v.add_value > 0 then

            table.insert(temp_attr_list,v)
        end
    end
    -- 增加的属性显示改为下级属性
    for key, value in pairs(temp_attr_list) do
        value.add_value = value.attr_next_value
    end
    -- 突破界面属性
    self.attr_list:SetDataList(temp_attr_list)
    -- 境界图标
	-- local jinjir_asset, jinjie_asset = ResPath.GetCultivationImg(cur_stage_cfg.stage_title_icon)
	-- self.node_list.jinjie_icon.image:LoadSprite(jinjir_asset, jinjie_asset, function ()
	-- 	self.node_list.jinjie_icon.image:SetNativeSize()
	-- end)


	-- 前中后期显示
	-- local stage = cur_stage_cfg.stage % 3
    -- local jinjir_asset_1, jinjie_asset_1 = ResPath.GetCultivationImg(stage_res[stage])
    -- self.node_list.img_stage.image:LoadSprite(jinjir_asset_1, jinjie_asset_1, function ()
    --     self.node_list.img_stage.image:SetNativeSize()
    -- end)

    -- 收益显示
	-- self.node_list.text_palsy_per.text.text = (cur_stage_cfg.attr_value4 / 100).."%"
    -- local cur_count = CultivationWGData.Instance:GetSlotCountByStage(cur_stage_cfg.stage)
	-- self.node_list.text_td.text.text = cur_count
	-- self.node_list.text_anger.text.text = cur_stage_cfg.nuqi_skill_level
end

function CultivationBreakSuccessView:OnClickBtnOk()
    self:Close()
end