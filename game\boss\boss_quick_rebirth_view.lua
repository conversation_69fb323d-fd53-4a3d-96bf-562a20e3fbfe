BossQuickRebirthView = BossQuickRebirthView or BaseClass(SafeBaseView)

function BossQuickRebirthView:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(false, true)

    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(600, 420)})
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_quick_rebirth_view")
end

function BossQuickRebirthView:OpenCallBack()

end

function BossQuickRebirthView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.confirm_btn, BindTool.Bind2(self.Close, self))
    XUI.AddClickEventListener(self.node_list.status, BindTool.Bind2(self.ChangeCurrStatus, self))
    XUI.AddClickEventListener(self.node_list.today_status, BindTool.Bind2(self.ChangeTodayTipsCurrStatus, self))
    XUI.AddClickEventListener(self.node_list.daily_status, BindTool.Bind2(self.ChangeCurrTodayNoticeStatus, self))
end

function BossQuickRebirthView:ReleaseCallBack()
	if self.boss_card_cell_list then
        for k, v in pairs(self.boss_card_cell_list) do
            v:DeleteMe()
        end
		self.boss_card_cell_list = nil
    end
end

function BossQuickRebirthView:OnFlush()
    self:RefreshBossItemCellsData()
end

function BossQuickRebirthView:RefreshBossItemCellsData()
    for i = 1, 3 do
        local item_id = MainuiWGData.Instance:GetBossRefreshItemId(i)
        if not self.boss_card_cell_list then
            self.boss_card_cell_list = {}
        end
        if not self.boss_card_cell_list[i] then
            self.boss_card_cell_list[i] = BossRefreshCard.New(self.node_list[string.format("boss_card_%d", i)])
        end
        local num = ItemWGData.Instance:GetItemNumInBagById(item_id)
        self.boss_card_cell_list[i]:SetData({item_id = item_id, idx = i, num = num})
        self.boss_card_cell_list[i]:SetRightBottomTextVisible(true)
		self.boss_card_cell_list[i]:SetRightBottomColorText(num)

        self.node_list["boss_card_" .. i]:CustomSetActive(item_id ~= nil)
    end
    
    self:FlushCurrStatus()
    self:FlushCurrTodayNoticeStatus(false)
end

-- 改变状态
function BossQuickRebirthView:ChangeCurrStatus()
    local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local rebirth_reflush_status = PlayerPrefsUtil.GetInt("boss_quick_rebirth_reflush" .. main_role_id)

    if rebirth_reflush_status == 1 then
        rebirth_reflush_status = 0
    else
        rebirth_reflush_status = 1
    end

    PlayerPrefsUtil.SetInt("boss_quick_rebirth_reflush" .. main_role_id, rebirth_reflush_status)
    self:FlushCurrStatus()
end

-- 改变今日提示状态
function BossQuickRebirthView:ChangeTodayTipsCurrStatus()
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local today_notips_open_day = PlayerPrefsUtil.GetInt("boss_quick_rebirth_tips" .. main_role_id)

    if today_notips_open_day == open_day then
        today_notips_open_day = 0
    else
        today_notips_open_day = open_day
    end

    PlayerPrefsUtil.SetInt("boss_quick_rebirth_tips" .. main_role_id, today_notips_open_day)
    self:FlushCurrStatus()
end

-- 刷新状态
function BossQuickRebirthView:FlushCurrStatus()
    local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local rebirth_reflush_status = PlayerPrefsUtil.GetInt("boss_quick_rebirth_reflush" .. main_role_id)
    self.node_list.status_select:CustomSetActive(rebirth_reflush_status == 1)

    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local has_today_flag = PlayerPrefsUtil.GetInt("boss_quick_rebirth_tips" .. main_role_id) == open_day
    self.node_list.today_status_select:CustomSetActive(has_today_flag)
end

function BossQuickRebirthView:ChangeCurrTodayNoticeStatus(is_click)
    self:FlushCurrTodayNoticeStatus(true)
end

-- 刷新状态
function BossQuickRebirthView:FlushCurrTodayNoticeStatus(is_click)
    local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
    local scene_type = Scene.Instance:GetSceneType()
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    if not is_click then
        local save_str = PlayerPrefsUtil.GetString("boss_quick_rebirth_".. scene_type .. role_id)
        local save_open_day
        if nil ~= save_str then
            local list = Split(save_str, "|")
            save_open_day = list[1]
            if save_open_day == open_day then
                self.mark_val = tonumber(list[2]) == 1
            else
                self.mark_val = false
            end

            save_str = string.format("%s|%s", open_day, self.mark_val and 1 or 0)
            PlayerPrefsUtil.SetString("boss_quick_rebirth_".. scene_type .. role_id, save_str)
        else
            save_str = string.format("%s|%s", open_day, self.mark_val and 1 or 0)
            PlayerPrefsUtil.SetString("boss_quick_rebirth_".. scene_type .. role_id, save_str)
            self.mark_val = false
        end
    else
        self.mark_val = not self.mark_val
        local save_str = string.format("%s|%s", open_day, self.mark_val and 1 or 0)
        PlayerPrefsUtil.SetString("boss_quick_rebirth_".. scene_type .. role_id, save_str)
    end

    self.node_list.daily_status_select:CustomSetActive(self.mark_val)
end