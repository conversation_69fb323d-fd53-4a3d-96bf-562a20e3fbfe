﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class ShadowProjectorWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(ShadowProjector), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("SetTarget", SetTarget);
		<PERSON><PERSON>ction("SetIsReplaceShadow", SetIsReplaceShadow);
		<PERSON><PERSON>Function("SetLightForward", SetLightForward);
		<PERSON><PERSON>RegFunction("SetCullingMasks", SetCullingMasks);
		L.RegFunction("__eq", op_Equality);
		<PERSON><PERSON>RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("shadowReplaceShader", get_shadowReplaceShader, set_shadowReplaceShader);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetTarget(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ShadowProjector obj = (ShadowProjector)ToLua.CheckObject<ShadowProjector>(L, 1);
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
			obj.SetTarget(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIsReplaceShadow(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ShadowProjector obj = (ShadowProjector)ToLua.CheckObject<ShadowProjector>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetIsReplaceShadow(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetLightForward(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ShadowProjector obj = (ShadowProjector)ToLua.CheckObject<ShadowProjector>(L, 1);
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.SetLightForward(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetCullingMasks(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ShadowProjector obj = (ShadowProjector)ToLua.CheckObject<ShadowProjector>(L, 1);
			string[] arg0 = ToLua.CheckStringArray(L, 2);
			obj.SetCullingMasks(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_shadowReplaceShader(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ShadowProjector obj = (ShadowProjector)o;
			UnityEngine.Shader ret = obj.shadowReplaceShader;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shadowReplaceShader on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_shadowReplaceShader(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ShadowProjector obj = (ShadowProjector)o;
			UnityEngine.Shader arg0 = (UnityEngine.Shader)ToLua.CheckObject(L, 2, typeof(UnityEngine.Shader));
			obj.shadowReplaceShader = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shadowReplaceShader on a nil value");
		}
	}
}

