-- 数字键盘
-- max_value不为空时，max_length无效

NumKeypad = NumKeypad or BaseClass(SafeBaseView)
NumKeypad.MaxVaule = 99999999999999

function NumKeypad:__init(view_name, max_length, max_value)
	self.view_layer = UiLayer.PopTop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/itemtip_ui_prefab", "layout_num_keypad")

	self.ok_callback = nil							-- 确定回调
	self.max_value = 999							-- 可输入的最大值
	self.min_value = 0
	self.input_num = 0								-- 输入的数字
	self.input_str = ""
	self.is_conver_number = false					--是否需要转换为 以万 亿为单位
	self.min_unit = 1								--输入的最小单位

	if nil ~= max_length then
		local temp_max = math.pow(10, max_length)
		if nil == max_value or max_value > temp_max then
			max_value = temp_max
		end
	end
	if nil ~= max_value then
		self:SetMaxValue(max_value)
	end
	self.show_state = nil --提示内容
	self.min_value_tips = nil -- 输入数值小于最小值提示内容
end

function NumKeypad:__delete()
	self.show_state = nil --提示内容
end

function NumKeypad:CloseCallBack()
	self.min_value_tips = nil
	self.show_state = nil 
	self.is_conver_number = false					--是否需要转换为 以万 亿为单位
	self.min_unit = 1								--输入的最小单位
	self.input_str = ""
	self.max_value = 999							-- 可输入的最大值
	self.min_value = 0
	self.input_num = 0	
end

-- 设置确定按钮回调事件
function NumKeypad:SetOkCallBack(func)
	if "function" == type(func) then
		self.ok_callback = func
	else
		ErrorLog("[NumKeypad] set ok callback is not func")
	end
end

-- 设置输入最大值
function NumKeypad:SetMaxValue(max_value, lock)
	self.is_lock = lock or false
	if nil ~= max_value and max_value >= 0 then
		self.max_value = max_value > 99999999999 and 99999999999 or max_value
	end
end

-- 设置输入最小值
function NumKeypad:SetMinValue(min_value)
	if nil ~= min_value and min_value >= 0 then
		self.min_value = min_value
		if self.input_num < self.min_value then
			self.input_num = self.min_value
		end
	end
end

--设置提示内容
function NumKeypad:SetShowState(str)
	self.show_state = str
end

--设置最小值提示内容
function NumKeypad:SetMinValueTips(str)
	self.min_value_tips = str or Language.Common.MinValue2
end

function NumKeypad:LoadCallBack()	
	self:RegisterAllEvents()
end

function NumKeypad:RegisterAllEvents()
	for i = 0, 9 do
		XUI.AddClickEventListener(self.node_list["btn_num_" .. i], BindTool.Bind2(self.OnClickBtn, self, i))
	end
	XUI.AddClickEventListener(self.node_list["btn_num_del"], BindTool.Bind1(self.OnClickDel, self))
	XUI.AddClickEventListener(self.node_list["btn_num_ok"], BindTool.Bind1(self.OnClickOK, self))
end

function NumKeypad:OpenCallBack()
	self.min_value = 0
	self:SetNum(0)
	self.first_open = true
end

function NumKeypad:ShowIndexCallBack()
	
end

function NumKeypad:OnFlush()
	if self.input_str ~= "" then
		 self.node_list["lbl_pop_num"].text.text = self.input_str
	else
		local num = self.input_num * self.min_unit
		local str = ""
		if self.is_conver_number then
			local value,unit = CommonDataManager.ConverMoneyBar(num,false)
			str = value..unit
		else
			str = tostring(num)
		end
		self.node_list["lbl_pop_num"].text.text = str
	end
end

function NumKeypad:OnClickBtn(num)
	if self.first_open then
		self.input_num = num
		self.first_open = false
	else
		self.input_num = self.input_num * 10 + num
	end
	
	if self.is_lock then
		if self.input_num > self.max_value then
			self.input_num = math.floor(self.input_num / 10)
		end
	else
		if self.input_num > self.max_value then
			self.input_num = self.max_value
			SysMsgWGCtrl.Instance:ErrorRemind(self.show_state or Language.Common.MaxValue)
		end
	end

	self:SetNum(self.input_num)
	self:SetPopString("")
end

function NumKeypad:OnClickDel()
	self:SetNum(math.floor(self.input_num / 10))
end

-- 点击确定按钮
function NumKeypad:OnClickOK()
	if nil ~= self.ok_callback then
		local num = self:GetNum()
		num = num * self.min_unit
		if num < self.min_value then
			num = self.min_value
			SysMsgWGCtrl.Instance:ErrorRemind(self.min_value_tips or Language.Common.MinValue2)
		end
		self.ok_callback(num)
	end

	self:Close()
end

function NumKeypad:GetText()
	return tostring(self:GetNum())
end

function NumKeypad:SetText(text)
	self:SetNum(tonumber(text))
end

function NumKeypad:GetNum()
	return self.input_num
end

function NumKeypad:SetNum(num)
	if num <= 0 then
		num = 0
	end
	self.input_num = num
	self:Flush()
end

function NumKeypad:SetPopString(str)
	self.input_str = str
	self:Flush()
end

function NumKeypad:SetMaskBgColor( r, g, b, a )
	self.mask_bg.image.color = Color.New(r, g, b, a)
end

--是否需要转换为 以万 亿为单位
function NumKeypad:SetIsConverNumber(flag)
	self.is_conver_number = flag
end

--设置输入的单位值
function NumKeypad:SetMinUnit(num)
	self.min_unit = num
end