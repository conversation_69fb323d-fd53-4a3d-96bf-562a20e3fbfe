BaoXiangCodeView = BaoXiangCodeView or BaseClass(SafeBaseView)

function BaoXiangCodeView:__init()
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:SetMaskBg(true)
	self:LoadConfig()
end

-- 加载配置
function BaoXiangCodeView:LoadConfig()
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_baoxiang_code")
end

function BaoXiangCodeView:__delete()

end

function BaoXiangCodeView:ReleaseCallBack()
	if not IsEmptyTable(self.code_lists) then
		for k,v in pairs(self.code_lists) do
			v:DeleteMe()
		end
		self.code_lists = {}
	end

end

function BaoXiangCodeView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["cancel_btn"], BindTool.Bind(self.ClickCancelBtn,self))
	XUI.AddClickEventListener(self.node_list["ok_btn"], BindTool.Bind(self.ClickOKBtn,self))
	self:InitCodeList()
end

function BaoXiangCodeView:InitCodeList()
	if self.code_lists == nil then
		self.code_lists = {}
	end

	local info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
	if IsEmptyTable(info) then return end 
	local quality = info.quality
	local treasure_cfg = GuildBaoXiangWGData.Instance:GetTreasureCfgByQuality(quality)
	if IsEmptyTable(treasure_cfg) then
		return
	end
	local code_num = treasure_cfg.password_bit
	local num = 1
	if not self.code_lists[1] then
		self.code_lists[1] = BaoXiangCodeListItem.New(self.node_list["code_list_item1"])
	end
	self.code_lists[1]:SetIndex(1)
	self.code_lists[1]:Flush()
	self.code_lists[1]:Reload()

	for i=2,4 do
		if code_num > i then
			self.node_list["code_list_item"..i]:SetActive(true)
			num = num + 1
			if not self.code_lists[num] then
				self.code_lists[num] = BaoXiangCodeListItem.New(self.node_list["code_list_item"..i])
			else
				self.code_lists[num]:DeleteMe()
				self.code_lists[num] = BaoXiangCodeListItem.New(self.node_list["code_list_item"..i])
				-- self.code_lists[num]:Reload()
			end
			self.code_lists[num]:SetIndex(num)
			self.code_lists[num]:Flush()
			self.code_lists[num]:Reload()
		else
			self.node_list["code_list_item"..i]:SetActive(false)
		end
	end
	num = num + 1
	if not self.code_lists[num] then
		self.code_lists[num] = BaoXiangCodeListItem.New(self.node_list["code_list_item5"])
	end
	self.code_lists[num]:SetIndex(num)
	self.code_lists[num]:Flush()
	self.code_lists[num]:Reload()

	local info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
	if IsEmptyTable(info) then return end 
	local password_bit = info.password_bit
	local password_list = info.password_list
	local cur_password = password_list[password_bit]
	local konw_str = string.format(Language.BiZuoBaoXiang.KonwCodeStr,password_bit,cur_password)
	self.node_list["cur_konw_code"].text.text = konw_str
end

function BaoXiangCodeView:CloseCallBack()
	self:SaveCode()
	GuildWGCtrl.Instance:GuildViewFlush(TabIndex.guild_baoxiang)
end

function BaoXiangCodeView:ShowIndexCallBack()
	UITween.ScaleShowPanel(self.node_list["root"].gameObject)
	-- self:InitCodeList()
end

function BaoXiangCodeView:OnFlush()

end

function BaoXiangCodeView:ClickCancelBtn()
	self:Close()
end

function BaoXiangCodeView:ClickOKBtn()
	local info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
	if IsEmptyTable(info) then return end 
	local quality = info.quality
	local password = info.password
	local treasure_cfg = GuildBaoXiangWGData.Instance:GetTreasureCfgByQuality(quality)
	if IsEmptyTable(treasure_cfg) then
		return
	end
	local code_num = treasure_cfg.password_bit
	local num_str = ""

	for i=1,#self.code_lists do
		local num = self.code_lists[i]:GetPageNow() or 1
		num_str = num_str .. num
	end


	num_str = tonumber(num_str)
	GuildBaoXiangWGData.Instance:SetInputCode(num_str)
	if num_str == password then
		local ok_fun = function ()
			GuildWGCtrl.Instance:SendCSDailyTreasureOperate(DAILY_TREASURE_OPERATE_TYPE.DAILY_TREASURE_OPERATE_TYPE_ENTER_PASSWARD,num_str)
			GuildWGCtrl.Instance:SendCSDailyTreasureOperate(DAILY_TREASURE_OPERATE_TYPE.DAILY_TREASURE_OPERATE_TYPE_OPEN_TREASURE)
			-- ViewManager.Instance:Open(GuideModuleName.BaoXiangGetRewardView)

			local guild_view = GuildWGCtrl.Instance:GetGuildView()
			if guild_view and guild_view:IsLoadedIndex(TabIndex.guild_baoxiang) then
				--guild_view:PlayOpenBXEffect()
			end
			SysMsgWGCtrl.Instance:ErrorRemind(Language.BiZuoBaoXiang.CodeSucc)
			self:Close()
		end
		local max_quality = GuildBaoXiangWGData.Instance:GetMaxQuality()
		if quality < max_quality then
			TipWGCtrl.Instance:OpenAlertTips(Language.BiZuoBaoXiang.OpenBaoXiangTips,ok_fun)
		else
			ok_fun()
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BiZuoBaoXiang.CodeError)
	end
end

function BaoXiangCodeView:SaveCode()
	local info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
	if IsEmptyTable(info) then return end 
	local quality = info.quality
	local treasure_cfg = GuildBaoXiangWGData.Instance:GetTreasureCfgByQuality(quality)
	if IsEmptyTable(treasure_cfg) then
		return
	end
	local code_num = treasure_cfg.password_bit
	local num_str = ""

    if not IsEmptyTable(self.code_lists) then
        for i=1, #self.code_lists do
            if self.code_lists[i] and self.code_lists[i].GetPageNow then
                local num = self.code_lists[i]:GetPageNow() or 1
                num_str = num_str .. num
            end
        end
    end
	GuildBaoXiangWGData.Instance:SetInputCode(num_str)
end

-------------------BaoXiangCodeListItem-------------------
local NumData = {[1] = 0,[2] = 1,[3] = 2,[4] = 3,[5] = 4,[6] = 5,[7] = 6,[8] = 7,[9] = 8,[10] = 9}

BaoXiangCodeListItem = BaoXiangCodeListItem or BaseClass(BaseRender)

function BaoXiangCodeListItem:__init()
	self.cur_konw_flag = false
	self.scroller_snap_jump_center = false
	self.page_scroll_event = BindTool.Bind(self.OnPageScrollValueChanged, self)
	self.node_list["list_view"].scroll_rect.onValueChanged:AddListener(self.page_scroll_event)
	self.save_code_index = 0
	self.list_view = AsyncListView.New(RandomCode,self.node_list["list_view"])
    local scroller = self.node_list["list_view"].scroller
    scroller.snapJumpToCenterScrolled = BindTool.Bind(self.ScrollerSnapJumpToCenter, self)
    scroller.scrollerEndScrolled = BindTool.Bind(self.ScrollerEndScrolled, self)
    -- scroller.scrollerScrollingChanged = BindTool.Bind(self.ScrollerScrollingChanged, self)
end

function BaoXiangCodeListItem:ScrollerEndScrolled()
end

function BaoXiangCodeListItem:ScrollerSnapJumpToCenter(obj,jump_index)
	jump_index = jump_index + 1
	if jump_index > 10 then
		jump_index = 1
	end
    local cell = self.list_view:GetItemAt(jump_index)
    self.save_code_index = jump_index
    self.scroller_snap_jump_center = true
end

function BaoXiangCodeListItem:Reload()
	-- self.node_list["list_view"].scroller:RefreshAndReloadActiveCellViews(true)
end

function BaoXiangCodeListItem:__delete()
	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view = nil
	end
	self.node_list["list_view"].scroll_rect.onValueChanged:RemoveListener(self.page_scroll_event)

end

function BaoXiangCodeListItem:OnPageScrollValueChanged(value)
	local cale_fun = function ()
		local all_items = self.list_view.cell_list
	    local center_tran = self.node_list["list_view"].scroll_rect.content.transform
	    local center_pos = center_tran:InverseTransformPoint(self.node_list["list_view"].transform.position)
	    local off_len = 0
	    local checkIndex = 0
	    for k, v in pairs(all_items) do
	        local pos_y = v.view.transform.localPosition.y
	        local len = math.abs(pos_y - center_pos.y)
	       	if off_len == 0 or off_len > len then
	       		off_len = len
	            checkIndex = v:GetIndex()
	       	end
	    end

		if self.last_page == nil then
			self.last_page = checkIndex
		end
		if self.last_page ~= checkIndex then
			self:FlushCodeNumShow(checkIndex)
		end
		self.last_page = checkIndex
		self.save_code_index = checkIndex
	end
	TryDelayCall(self, function ()
		cale_fun()
	end, 0, "cale_fun_delay")
end


function BaoXiangCodeListItem:FlushCodeNumShow(page)
	local cell_list = self.list_view.cell_list
	for k,v in pairs(cell_list) do
		v:FlushShow(page,self.index)
	end
end

function BaoXiangCodeListItem:GetNumData()
	local num_data = {}
	for i=0,9 do
		local data = {}
		data.num = i
		data.code_index = self.index
		table.insert(num_data,data)
	end
	return num_data
end

function BaoXiangCodeListItem:OnFlush()
	self.list_view:SetDataList(self:GetNumData())

	local info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
	if IsEmptyTable(info) then return end 
	local password_bit = info.password_bit
	local treasure_cfg = GuildBaoXiangWGData.Instance:GetTreasureCfgByQuality(info.quality)
	local cfg_password_bit = treasure_cfg and treasure_cfg.password_bit or 5
	local cur_index = self.index

	if cur_index == password_bit then
		local password_list = info.password_list
		local cur_password = password_list[password_bit]
		local index = cur_password - 1
		self.save_code_index = cur_password + 1
	    self:SetAllGrayCell(true,cur_password)
	else
		local input_code = GuildBaoXiangWGData.Instance:GetInputCode()
		if input_code then
			input_code = tonumber(input_code)
			local percent = IntgerAnalyze(input_code, cfg_password_bit - cur_index + 1)
			local index = percent - 1
			self.save_code_index = percent + 1
		    local tween_type = self.node_list["list_view"].scroller.snapTweenType
		    TryDelayCall(self, function ()
		    	self.first_jump_index = true
		    	if self.node_list["list_view"] and self.node_list["list_view"].gameObject.activeInHierarchy then
			    	self.node_list["list_view"].scroller:JumpToDataIndex(index)
			    	TryDelayCall(self,function ()
			    		self.node_list["list_view"].scroller:Snap()
			    	end,0,"jump_to_index_snap_delay")
			    end
		    end, 0.5, "jump_to_index_delay")
			
		end
		self:SetAllGrayCell(false,0)
	end
end

function BaoXiangCodeListItem:SetAllGrayCell(flag,password)
    self.cur_konw_flag = flag
    self.node_list["gray"]:SetActive(flag)
	self.node_list["ui_block"]:SetActive(flag)
    self.node_list["konw_num_text"].text.text = password
    self.node_list["list_view"]:SetActive(not flag)
end

function BaoXiangCodeListItem:GetPageNow()
	if self.cur_konw_flag then
		local info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
		if IsEmptyTable(info) then return 1 end 
		local password_bit = info.password_bit
		local password_list = info.password_list
		local cur_password = password_list[password_bit]
		return cur_password
	else
		local cell = self.list_view:GetItemAt(self.save_code_index)
		 if cell then
		 	return cell.data.num
		 end
		 return NumData[self.save_code_index]
	end
end


------------RandomNum-----
RandomCode = RandomCode or BaseClass(BaseRender)

function RandomCode:__init()
end

function RandomCode.__delete()
end

function RandomCode:OnFlush()
	if not self.data then return end
	self.node_list["code_num"].text.text = self.data.num
	local code_index = self.data.code_index
end

function RandomCode:FlushShow(page)
	if self:GetActive() then
		if page ~= self.index then
			self.node_list["img"].transform.localScale = u3dpool.vec3(1, 0.8, 1)
			self.node_list["img"].canvas_group.alpha = 0.8
		else
			self.node_list["img"].transform.localScale = u3dpool.vec3(1, 1, 1)
			self.node_list["img"].canvas_group.alpha = 1
		end
	end
end
