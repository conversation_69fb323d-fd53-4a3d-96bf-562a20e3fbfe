require("game/honghuang_good_ceremony/honghuang_good_ceremony_view")
require("game/honghuang_good_ceremony/honghuang_good_ceremony_wg_data")

HongHuangGoodCoremonyWGCtrl = HongHuangGoodCoremonyWGCtrl or BaseClass(BaseWGCtrl)
function HongHuangGoodCoremonyWGCtrl:__init()
    if nil ~= HongHuangGoodCoremonyWGCtrl.Instance then
        ErrorLog("[HongHuangGoodCoremonyWGCtrl]:Attempt to create singleton twice!")
    end
    HongHuangGoodCoremonyWGCtrl.Instance = self

	self.view = HongHuangGoodCoremonyView.New(GuideModuleName.HongHuangGoodCoremonyView)
	self.data = HongHuangGoodCoremonyWGData.New()
	self:RegisterAllProtocals()
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
end

function HongHuangGoodCoremonyWGCtrl:__delete()
    HongHuangGoodCoremonyWGCtrl.Instance = nil

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end
	
	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end
end

function HongHuangGoodCoremonyWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(CSChaoticGiftyOperate)
	self:RegisterProtocol(SCChaoticGifInfo, "OnSCChaoticGifInfo")
end

function HongHuangGoodCoremonyWGCtrl:SendHongHuangGoodCoremonyReq(opera_type, param_1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSChaoticGiftyOperate)
	protocol.opera_type = opera_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol:EncodeAndSend()
end

function HongHuangGoodCoremonyWGCtrl:OnSCChaoticGifInfo(protocol)
	self.data:SetChaoticGifInfo(protocol)
	MainuiWGCtrl.Instance:FlushView(0,"honghuanggoodcoremony_over")
	if self.view:IsOpen() then
		self.view:Flush()
	end
end

--天数改变
function HongHuangGoodCoremonyWGCtrl:OnPassDay()
	self:SendHongHuangGoodCoremonyReq(CHAOTIC_GIFT_OPERATE_TYPE.CHAOTIC_GIFT_OPERATE_TYPE_GIFT_INFO)
	self:SendHongHuangGoodCoremonyReq(CHAOTIC_GIFT_OPERATE_TYPE.CHAOTIC_GIFT_OPERATE_TYPE_RMB_BUY_INFO)
	self:SendHongHuangGoodCoremonyReq(CHAOTIC_GIFT_OPERATE_TYPE.CHAOTIC_GIFT_OPERATE_TYPE_SPECIAL_INFO)
	self:SendHongHuangGoodCoremonyReq(CHAOTIC_GIFT_OPERATE_TYPE.VIP_SPECIAL_INFO)
end