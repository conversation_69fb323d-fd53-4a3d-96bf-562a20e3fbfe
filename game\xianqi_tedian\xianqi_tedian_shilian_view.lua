XianQiTeDianShiLianView = XianQiTeDianShiLianView or BaseClass(SafeBaseView)

function XianQiTeDianShiLianView:__init(view_name)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/sixiang_call_prefab", "xqzl_shilian_view")
	self:SetMaskBg(true, true)
end

function XianQiTeDianShiLianView:LoadCallBack()
	self:InitParam()
	self:InitPanel()
	self:InitListener()
end

function XianQiTeDianShiLianView:ReleaseCallBack()
	if self.xianqi_item_list then
		for k,v in pairs(self.xianqi_item_list) do
			v:DeleteMe()
		end
		self.xianqi_item_list = nil
	end

	CountDownManager.Instance:RemoveCountDown("xianqi_tedian_shilian")
end

function XianQiTeDianShiLianView:CloseCallBack()
	if self.title_tween then
		self.title_tween:Kill()
		self.title_tween = nil
	end
	if self.center_item_tween then
		self.center_item_tween:Kill()
		self.center_item_tween = nil
	end
	if self.tween_yoyo then
		self.tween_yoyo:Kill()
		self.tween_yoyo = nil
	end
	if self.xianqi_item_list then
		for k,v in pairs(self.xianqi_item_list) do
			v:RestTween()
		end
	end
end

function XianQiTeDianShiLianView:OnFlush(param_t)
	self:RefreshView()
end

function XianQiTeDianShiLianView:InitParam()
	self.cur_act_id = 0
	self.cur_act_open_state = false
	self.title_tween = nil
	self.center_item_tween = nil
	self.tween_yoyo = nil
end

function XianQiTeDianShiLianView:InitPanel()
	local show_id_list = SiXiangCallWGData.Instance:GetXQZLShowItemList()
	if IsEmptyTable(show_id_list) then
		return
	end
	local center_num = #show_id_list / 2
	local item_parent = self.node_list.item_list
	local xianqi_item_list = {}

	for i = #show_id_list, 1, -1 do
		local item = XianQiTeDianShiLianItem.New()
		item:SetIndex(i)
		if i <= center_num then
			item:SavePos(-194 - 120 * (i - 1), 0)
		else
			item:SavePos(194 + 120 * (i - center_num - 1), 0)
		end
		item:DoLoad(item_parent)
		item:SetData(show_id_list[i])
		xianqi_item_list[i] = item
	end

	self.xianqi_item_list = xianqi_item_list
end

function XianQiTeDianShiLianView:InitListener()
	XUI.AddClickEventListener(self.node_list.buy_btn, BindTool.Bind1(self.OnClickBuyBtn, self))
	XUI.AddClickEventListener(self.node_list.tip_btn, BindTool.Bind1(self.OnClickTipBtn, self))
end

function XianQiTeDianShiLianView:OnClickTipBtn()
	RuleTip.Instance:SetContent(Language.SiXiangCall.XQZLShiLianRuleContent, Language.SiXiangCall.XQZLShiLianRuleTitle)
end

function XianQiTeDianShiLianView:OnClickBuyBtn()
	if self.cur_act_open_state then
		XianQiTeDianWGCtrl.Instance:BuyXianQiTeDian(self.cur_act_id)
	else
		self:Close()--领取完了--点击关闭
	end
	
end

function XianQiTeDianShiLianView:ShowIndexCallBack()
	self:PlayShowViewTween()
end

function XianQiTeDianShiLianView:RefreshView()
	self.cur_act_id = XianQiTeDianWGData.Instance:GetCurShiLian()
	self.cur_act_open_state = XianQiTeDianWGData.Instance:CheckSubActIsOpen(self.cur_act_id)
	self:FlushDiscount()
	self:FlushActTime()
	self:FlushBuyBtnState()
end

-- 刷新折扣
function XianQiTeDianShiLianView:FlushDiscount()
	self.node_list["discount_bg"]:SetActive(self.cur_act_open_state)
	if not self.cur_act_open_state then
		return
	end
	
	local res_name = ""
	local discount = ""
	if self.cur_act_id == XianQiTeDianSubActId.ShiLian1 then
		discount = 1
	elseif self.cur_act_id == XianQiTeDianSubActId.ShiLian2 then
		discount = 2
	end

	self.node_list.discount_lbl.text.text = discount

	local res_name = "shilian_discount_" .. discount
	local bundle, asset = ResPath.GetSiXiangCallImg(res_name)
	self.node_list.title_img.image:LoadSprite(bundle, asset, function ()
		self.node_list.title_img.image:SetNativeSize()
	end)
end

-- 刷新购买按钮
function XianQiTeDianShiLianView:FlushBuyBtnState()
	local sale_info = XianQiTeDianWGData.Instance:GetSubActSaleInfo(self.cur_act_id, 1)
	local cfg_list = XianQiTeDianWGData.Instance:GetActSaleCfg(self.cur_act_id)
	local btn_text = Language.SiXiangCall.HasFetch--确认
    local is_gray = false
	if self.cur_act_open_state and sale_info and not IsEmptyTable(cfg_list) then
		local cfg_data = cfg_list[1]
	    if sale_info.status == YuanShenSaleSubActSaleStatus.NotBuy then
	        --未购买
	        if cfg_data.price_type == SiXiangBuyType.RMB then
	        	btn_text = string.format(Language.SiXiangCall.BuyText, cfg_data.special_sale_price)
	        elseif cfg_data.price_type == SiXiangBuyType.XY then
	        	btn_text = string.format(Language.SiXiangCall.BuyText2, cfg_data.special_sale_price)
	        end
	    elseif sale_info.status == YuanShenSaleSubActSaleStatus.HasBuyNotFetch then
	        --已购买未领取（rmb商品会用到）
	        btn_text = Language.SiXiangCall.CurCanFetch
	    elseif sale_info.status == YuanShenSaleSubActSaleStatus.HasBuyAndFetched then
	        --已购买已领取
	        btn_text = Language.SiXiangCall.HasFetch
	        is_gray = true
	    end
	end

    XUI.SetGraphicGrey(self.node_list.buy_btn, is_gray)
    self.node_list.buy_btn_label.text.text = btn_text
end

---[[ 活动结束倒计时
function XianQiTeDianShiLianView:FlushActTime()
	CountDownManager.Instance:RemoveCountDown("xianqi_tedian_shilian")
    local sever_time = TimeWGCtrl.Instance:GetServerTime()
    local end_time = XianQiTeDianWGData.Instance:GetTeDianEndTimeStamp()
    if end_time <= sever_time then
        self.node_list.time_label.text.text = ""
        return
    end

    self:UpdateCountDown(sever_time, end_time)

    CountDownManager.Instance:AddCountDown(
        "xianqi_tedian_shilian",
        BindTool.Bind(self.UpdateCountDown, self),
        BindTool.Bind(self.FlushActTime, self),
        end_time,
        nil,
        1
    )
end

function XianQiTeDianShiLianView:UpdateCountDown(elapse_time, total_time)
	local time_str = TimeUtil.FormatSecondDHM8(math.floor(total_time - elapse_time))
	self.node_list.time_label.text.text = string.format(Language.SiXiangCall.CloseTimeDesc, time_str)
end
--]]

---[[ 动画相关
function XianQiTeDianShiLianView:PlayShowViewTween()
	if self.xianqi_item_list then
		local item_list = self.xianqi_item_list
		local center_num = #item_list / 2
		for i=1,#item_list do
			item_list[i]:PlayShowItemTween(center_num)
		end
	end
	self:PlayTitleImgTween()
	self:PlayCenterItemTween()
end

function XianQiTeDianShiLianView:PlayTitleImgTween()
	if self.title_tween then
		return
	end
	self.node_list.title_img.transform:SetLocalScale(1.5, 1.5, 1.5)
	self.title_tween = self.node_list.title_img.transform:DOScale(1, 0.4)
	self.title_tween:SetEase(DG.Tweening.Ease.InQuad)
	self.title_tween:OnComplete(function ()
		self.title_tween = nil
	end)
end

function XianQiTeDianShiLianView:PlayCenterItemTween()
	if self.center_item_tween then
		return
	end

	RectTransform.SetAnchoredPositionXY(self.node_list.center_item_root.rect, 0, -40)
	self.node_list.center_item_root.canvas_group.alpha = 0

	local tween_sequence = DG.Tweening.DOTween.Sequence()
	local tween_move = self.node_list.center_item_root.rect:DOAnchorPosY(0, 0.5)
	local tween_alpha = self.node_list.center_item_root.canvas_group:DoAlpha(0, 1, 0.5)
	
	tween_sequence:Append(tween_move)
	tween_sequence:Join(tween_alpha)
	tween_sequence:OnComplete(function ()
		if not self.tween_yoyo then
			self.tween_yoyo = self.node_list.center_item_root.rect:DOAnchorPosY(20, 1.5)
			self.tween_yoyo:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
		end
		self.center_item_tween = nil
	end)

	self.center_item_tween = tween_sequence
end
--]]

--------------------------------------------------------------------------------

XianQiTeDianShiLianItem = XianQiTeDianShiLianItem or BaseClass(XianQiZhenLianItem)

function SiXiangRewardItem:__delete()
	if self.m_tween then
		self.m_tween:Kill()
		self.m_tween = nil
	end
end

function XianQiTeDianShiLianItem:PlayShowItemTween(center_num)
	if self.m_tween or not self.node_list then
		return
	end

	local pos_x, pos_y = self:GetPos()
	RectTransform.SetAnchoredPositionXY(self.node_list.tween_root.rect, -pos_x, pos_y)
	self.node_list.tween_root.canvas_group.alpha = 0

	local index = self:GetIndex()
	if index > center_num then
		index = index - center_num
	end
	local time = 0.15 * index

	local tween_sequence = DG.Tweening.DOTween.Sequence()
	local tween_move = self.node_list.tween_root.rect:DOAnchorPosX(0, time)
	local tween_alpha = self.node_list.tween_root.canvas_group:DoAlpha(0, 1, time + 0.3)

	tween_sequence:Append(tween_move)
	tween_sequence:Join(tween_alpha)
	tween_sequence:OnComplete(function ()
		self.m_tween = nil
	end)
	tween_sequence:SetEase(DG.Tweening.Ease.Linear)

	self.m_tween = tween_sequence
end

function XianQiTeDianShiLianItem:RestTween()
	RectTransform.SetAnchoredPositionXY(self.node_list.tween_root.rect, 0, 0)
	self.node_list.tween_root.canvas_group.alpha = 1
	if self.m_tween then
		self.m_tween:Kill()
		self.m_tween = nil
	end
end