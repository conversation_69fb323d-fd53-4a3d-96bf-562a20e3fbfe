-----------------------------------
--双修 升级
-----------------------------------

local LEVEL_DELT_TIME = 0.2 --0.5s升级一次

function ArtifactView:UpLevelLoadIndexCallBack()
	if self.ul_artifact_attr_list == nil then
		self.ul_artifact_attr_list = {}
		local node_num = self.node_list["ul_artifact_attr_list"].transform.childCount
		for i = 1, node_num do
			self.ul_artifact_attr_list[i] = CommonAddAttrRender.New(self.node_list["ul_artifact_attr_list"]:FindObj("attr_" .. i))
			self.ul_artifact_attr_list[i]:SetAttrNameNeedSpace(true)
		end
	end

	if not self.active_item_cell then
		self.active_item_cell = ItemCell.New(self.node_list["active_cost_pos"])
	end

	if not self.consume_list then
		self.consume_list = {}
		for i=1,3 do
			self.consume_list[i] = ItemCell.New(self.node_list["cost_item_root_" .. i])
			self.consume_list[i]:SetHideRightDownBgLessNum(-1)
		end
	end

	XUI.AddClickEventListener(self.node_list["act_btn"], BindTool.Bind(self.OnBtnActive, self))         --解锁
	XUI.AddClickEventListener(self.node_list["auto_level_btn"], BindTool.Bind(self.OnBtnAutoUpLevel, self)) --自动升级
	XUI.AddClickEventListener(self.node_list["up_level_skill_btn"], BindTool.Bind(self.OnClickULSkillShowBtn, self)) --技能展示

	self.is_auto_level = false
end

function ArtifactView:UpLevelReleaseCallBack()
	if self.ul_artifact_attr_list then
		for k, v in pairs(self.ul_artifact_attr_list) do
			v:DeleteMe()
		end
		self.ul_artifact_attr_list = nil
	end

	if self.active_item_cell then
		self.active_item_cell:DeleteMe()
		self.active_item_cell = nil
	end

	if self.consume_list then
		for k, v in pairs(self.consume_list) do
			v:DeleteMe()
		end
		self.consume_list = nil
	end

	self.is_auto_level = false
	self.seq_cache = nil
	self.level_cache = nil
end

function ArtifactView:UpLevelShowIndexCallBack()
	self:PlayShowUpLevelAni()
end

function ArtifactView:UpLevelCloseCallBack()
	if self:IsAutoUpLevel() then
		self:StopLevelOperator()
	end
end

function ArtifactView:UpLevelOnFlush(param_t)
	self:FlushRightPart()
end

function ArtifactView:UpLevelOnClickArtifactCell(cell)
	self:StopLevelOperator()
	self:FlushRightPart()
end

--刷新右边信息
function ArtifactView:FlushRightPart()
	if not self:CheckFlushCondition() then
		return
	end

	local artifact_cfg = ArtifactWGData.Instance:GetArtifactCfgBySeq(self.select_artifact_seq)
	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.select_artifact_seq)
	local is_act = cur_artifact_data.level > 0

	self:FlushUpLevelAtrrInfo()

	self.node_list.act_remind:SetActive(false)
	self.node_list.auto_up_level_remind:SetActive(false)

	local cur_level_cfg = ArtifactWGData.Instance:GetArtifactLevelCfg(self.select_artifact_seq, cur_artifact_data.level)
	local next_level_cfg = ArtifactWGData.Instance:GetArtifactLevelCfg(self.select_artifact_seq, cur_artifact_data.level + 1)
	local max_level = next_level_cfg == nil

	self.node_list["actived"]:SetActive(is_act)
	self.node_list["no_act"]:SetActive(not is_act)
	self.node_list["cur_level_text"].text.text = cur_artifact_data.level
	self.node_list["txt_cur_exp"]:SetActive(is_act)
	if not is_act then
		self.active_item_cell:SetData({ item_id = artifact_cfg.item_id })
		local cost_num = ItemWGData.Instance:GetItemNumInBagById(artifact_cfg.item_id)
		local color = cost_num >= artifact_cfg.cost_item_num and COLOR3B.D_GREEN or COLOR3B.D_RED
		local cost_str = cost_num .. "/" .. artifact_cfg.cost_item_num
		self.node_list["active_cost_num"].text.text = ToColorStr(cost_str, color)
		self.node_list["act_remind"]:SetActive(cost_num >= artifact_cfg.cost_item_num)
	else
		local uplevel_item_cfg = ArtifactWGData.Instance:GetUplevelItemCfg()
		for i = 1, 3 do
			local item_count = ItemWGData.Instance:GetItemNumInBagById(uplevel_item_cfg[i].item_id)
			self.consume_list[i]:SetData({item_id = uplevel_item_cfg[i].item_id, num = item_count})
		end
		self.node_list["auto_level_btn"]:SetActive(not max_level)
		self.node_list["level_max"]:SetActive(max_level)
		
		local exp_str = max_level and "--/--" or string.format(Language.Artifact.Exp, cur_artifact_data.exp, next_level_cfg.need_exp)
		self.node_list["txt_cur_exp"].text.text = exp_str
	end

	--红点
	local uplevel_remind = ArtifactWGData.Instance:GetArtifactUpLevelRemind(self.select_artifact_seq)
	self.node_list["auto_up_level_remind"]:SetActive(uplevel_remind)
end

-- 刷新属性
function ArtifactView:FlushUpLevelAtrrInfo()
	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.select_artifact_seq)
	local attr_list = ArtifactWGData.Instance:GetArtifactAllAttrList(
		self.select_artifact_seq,
		cur_artifact_data.level,
		cur_artifact_data.star_level,
		cur_artifact_data.awake_level, 1)
	
	local need_show_effect = false
	if self.seq_cache == self.select_artifact_seq and self.level_cache ~= cur_artifact_data.level then
		need_show_effect = true
	end
	self.seq_cache = self.select_artifact_seq
	self.level_cache = cur_artifact_data.level

	for k, v in pairs(self.ul_artifact_attr_list) do
		if need_show_effect then
			local data = v:GetData()
			if data and data.add_value and data.add_value > 0 then
				v:PlayAttrValueUpEffect()
			end
		end
		v:SetData(attr_list[k])
	end
end

-- 激活
function ArtifactView:OnBtnActive()
	if not self:CheckFlushCondition() then
		return
	end

	local artifact_cfg = ArtifactWGData.Instance:GetArtifactCfgBySeq(self.select_artifact_seq)
	local had_num = ItemWGData.Instance:GetItemNumInBagById(artifact_cfg.item_id)
	if had_num < artifact_cfg.cost_item_num then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = artifact_cfg.item_id})
		return
	end
	
	ArtifactWGCtrl.Instance:SendCSArtifactOperateRequest(ARTIFACT_OPERATE_TYPE.ACTIVE, self.select_artifact_seq)
end

function ArtifactView:OnBtnUpLevel(one_key, is_auto)
	if not self:CheckFlushCondition() then
		return
	end

	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.select_artifact_seq)
	if self:IsAutoUpLevel() and not is_auto then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Artifact.LevelBtnTip)
		return
	end

	local cur_level_cfg = ArtifactWGData.Instance:GetArtifactLevelCfg(self.select_artifact_seq, cur_artifact_data.level)
	local next_level_cfg = ArtifactWGData.Instance:GetArtifactLevelCfg(self.select_artifact_seq, cur_artifact_data.level + 1)
	if next_level_cfg and cur_level_cfg then
		-- 解锁
		if cur_artifact_data.level == 0 then
			local artifact_cfg = self:GetArtifactCfgBySeq(artifact_seq)
			local had_num = ItemWGData.Instance:GetItemNumInBagById(artifact_cfg.item_id)
			if had_num >= artifact_cfg.cost_item_num then
				ArtifactWGCtrl.Instance:SendCSArtifactOperateRequest(ARTIFACT_OPERATE_TYPE.ACTIVE, self.select_artifact_seq)
				if is_auto then
					self:SetLevelButtonEnabled(true)
				end
			else
				self:StopLevelOperator()
				TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = artifact_cfg.item_id })
			end
		else
			local uplevel_item_cfg = ArtifactWGData.Instance:GetUplevelItemCfg()
			local is_enough = false
			for i = 1, 3 do
				local item_count = ItemWGData.Instance:GetItemNumInBagById(uplevel_item_cfg[i].item_id)
				if item_count > 0 then 
					is_enough = true
					break
				end
			end
			if is_enough then
				ArtifactWGCtrl.Instance:SendCSArtifactOperateRequest(ARTIFACT_OPERATE_TYPE.UPLEVEL, self.select_artifact_seq)
				if is_auto then
					self:SetLevelButtonEnabled(true)
				end
			else
				self:StopLevelOperator()
				TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = uplevel_item_cfg[1].item_id })
			end
		end
	else
		self:StopLevelOperator()
	end
end

function ArtifactView:OnClickULSkillShowBtn()
	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.select_artifact_seq)
	local skill_cfg = ArtifactWGData.Instance:GetArtifactSkillCfg(self.select_artifact_seq, cur_artifact_data.star_level)

	local is_locked = cur_artifact_data.star_level < skill_cfg.star_level
	local show_data = {
		skill_box_type = SKILL_BOX_TYPE.ARTIFACT_SKILL,
		skill_type = 1, -- 升星技能
		x = 0,
		y = 0,
		set_pos2 = true,
		seq = self.select_artifact_seq,
		icon = skill_cfg.skill_id,
		top_text = skill_cfg.skill_name,
		skill_level = skill_cfg.skill_level,
		hide_next = true,
		body_text = skill_cfg.skill_desc,
		limit_text = is_locked and string.format(Language.Artifact.SkillTipsShow, cfg.star_level) or nil
	}
	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

function ArtifactView:OnBtnAutoUpLevel()
	if self:IsAutoUpLevel() then --正在自动升级则取消强化
		self:StopLevelOperator()
		return
	end

	self:OnBtnUpLevel(1, true)
end

--取消自动升级倒计时
function ArtifactView:CancelAutoLevelTimer()
	if nil ~= self.auto_level_timer_quest then
		GlobalTimerQuest:CancelQuest(self.auto_level_timer_quest)
		self.auto_level_timer_quest = nil
	end
end

-- 自动升级操作
function ArtifactView:AutoUpLevelUpOnce()
	self:CancelAutoLevelTimer()

	if not self:CheckFlushCondition() then
		return
	end

	if self:IsAutoUpLevel() then
		self.auto_level_timer_quest = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.OnBtnUpLevel, self, 1, true), LEVEL_DELT_TIME)
	end
end

function ArtifactView:IsAutoUpLevel()
	return self.is_auto_level
end

function ArtifactView:StopLevelOperator()
	self:SetLevelButtonEnabled(false)
end

-- 设置强化按钮是否可用
function ArtifactView:SetLevelButtonEnabled(enabled)
	self.is_auto_level = enabled
	self:SetAutoLevelBtnNameStr(not enabled and Language.Artifact.AutoUpLevel or Language.Artifact.UpLevelBtnStop)
	if false == enabled then
		self:CancelAutoLevelTimer()
	end
end

function ArtifactView:SetAutoLevelBtnNameStr(strength_btn_str)
	if self.node_list["auto_up_level_text"] then
		self.node_list["auto_up_level_text"].text.text = strength_btn_str
	end
end

function ArtifactView:PlayShowUpLevelAni()
	if self.node_list["ul_right_panel"] then
		self.node_list["ul_right_panel"].animation_player:Play("ShowUpLevelPanel")
	end
end