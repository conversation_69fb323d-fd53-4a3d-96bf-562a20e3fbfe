FollowNameBar = FollowNameBar or BaseClass(BaseRender)

local TypeText = typeof(TMPro.TextMeshProUGUI)

function FollowNameBar:__init()
	self.obj_type = nil
	self.is_root_created = false
	self.name = ""
	self.name_color = COLOR3B.WHITE
	self.old_offset_x = 0
	self.old_offset_y = 0
	self.bleed_show_state = false
	self.timer_str = nil
	self.time_value = nil
    self.timer_obj_id = nil
    self.xianjie_equip_info = {}
    self.old_xianjie_equip_id = nil
end

function FollowNameBar:__delete()
	if not self:IsNil() then
		self:ResumeDefaultLoacalUI()
		self:ResumeLoverName()
		self:ResumeGuildName()
		self:ResumeDefaultPosAndScale()
		self:ResumeSpecialImage()
		self:ResumeGatherImg()
		self:ResumeFaceIcon()
		self:ResumeBoxBossAngry()
		self:ResumeDefaultScoreTxt()
		self:ResumeRedSideIcon()
		self:ResumeIconActive()
		self:ResumeTitle()
		self:ResumeVip()
		self:ResumeGodOrDemon()
		self:ResumeNameContainerActive()
		self:ResumeEternalNight()
		self:ResumeComboKill()
		self:ResumeBleedShow()
		self:ResumeTianShen3v3Side()
		self:ResumeDrunkLabel()
        self:ResumeShowTimer()
        self:ResumeAnscircleImg()
        self:ResumeXiezhuIngVisible()
        self:ResumeXianJieVisible()
        self:ResumeAreaIndexImg()
        self:ResumeYZBuffTime()
		self:ResumeFGBIcon()
		self:ResumeUltimateState()
		self:ResumePWCampIcon()
		self:ResumeElementImg()
		self:ResumeBeastCatchMessage()
	end

	if nil ~= self.buff_obj_list then
        for k,v in pairs(self.buff_obj_list) do
            v:DeleteMe()
        end
        self.buff_obj_list = nil
    end

	if self.follow_role_name_obj then
		self.follow_role_name_obj:DeleteMe()
		self.follow_role_name_obj = nil
	end

	if self.follow_petonwer_name_obj then
		self.follow_petonwer_name_obj:DeleteMe()
		self.follow_petonwer_name_obj = nil
	end

	if self.follow_guild_name_obj then
		self.follow_guild_name_obj:DeleteMe()
		self.follow_guild_name_obj = nil
	end

	if self.follow_lover_name_obj then
		self.follow_lover_name_obj:DeleteMe()
		self.follow_lover_name_obj = nil
	end

	if self.follow_server_name_obj then
		self.follow_server_name_obj:DeleteMe()
		self.follow_server_name_obj = nil
	end

    if self.timer_obj_id then
        if CountDown.Instance:HasCountDown("follow_ui_name_timer"..self.timer_obj_id) then
            CountDown.Instance:RemoveCountDown("follow_ui_name_timer"..self.timer_obj_id)
        end
    end

	self.icon_list = nil
	self.name_text = nil
	self.guild_name = nil
	-- self.fame_rank_icon_id = nil
	self.lover_name = nil
	self.vip_level = nil
	self.is_active_vip = nil
	self.is_hide_vip = nil
	self.is_show_leader_flag = nil
	self.eternal_night_equip_list = nil
	self.eternal_night_equip_data = nil
	self.combo_kill_num = 0
	self.timer_str = nil
    self.time_value = nil
    self.timer_obj_id = nil
    self.answer_circle_name = nil
    self.tianshen_3v3_side = nil
    self.is_drunk = false
    self.xianjie_equip_info = nil
    self.old_xianjie_equip_id = nil
    self.area_index = nil
    self.old_area_index = nil
    self.is_show_area_index = nil
	self.xiuwei_icon = nil
	self.ultimate_data = nil
	self.god_or_demon_type = nil
	self.god_or_demon_level = nil
	self.pw_camp_id = nil
	self.old_beast_catch_slider = nil
	self.beast_catch_slider = nil
	self.beast_catch_duration = nil
end

local function LoadSpriteCallBack(cbdata)
	local obj = cbdata[1]
	FollowUi.ReleaseCBData(cbdata)

	-- 策划说先关闭这个
	if not IsNil(obj.gameObject) then
		obj:SetActive(true)
		obj.image:SetNativeSize()
	end
end

local function CreateRootCallBack(gameobj, cbdata)
	local self = cbdata[1]
	local prefab_name = cbdata[2]
	local follow_parent = cbdata[3]
	FollowUi.ReleaseCBData(cbdata)

	if IsNil(gameobj) then
		return
	end

	self:SetInstance(gameobj)
	self.view.rect.anchoredPosition = Vector2(0, 68)

	-- 保存名字默认的Position和Scale
	self.old_offset_x = self.node_list["SceneObjName"].transform.localPosition.x
	self.old_offset_y = self.node_list["SceneObjName"].transform.localPosition.y

	self.name_text = self.node_list["SceneObjName"]:GetComponent(TypeText)

	if self.obj_type == SceneObjType.Role then
		self.node_list["GuildNameObj"].gameObject:SetActive(false)
		self.node_list["LoverNameContainer"].gameObject:SetActive(false)
	end
    if self.obj_type ~= SceneObjType.MarryObj then
        self:CreateAttachName(prefab_name, follow_parent)
    end

	self:UpdateActive()
	self:UpdateLoacalUI()
	self:UpdateName()
	self:UpdateGuildName()
	-- self:UpdateJinjieIcon()
	self:UpdateServerInfo()
	self:UpdateLoverName()
	self:UpdateVip()
	self:UpdateSpecialImage()
	self:UpdateGatherImg()
	self:UpdateFaceIcon()
	self:UpdateBoxBossAngry()
	self:UpdateScore()
	self:UpdateRedSideIcon()
	self:UpdateIconActive()
	self:UpdateTitle()
	self:UpdateZhuanzhiIconVisible()
	self:UpdateNameContainerVisible()
	self:UpdateNPCIcon()
	self:UpdateMonsterIcon()
	self:UpdateNPCTitle()
	self:UpdateFloatIcon()
	self:UpdatePetOnwerName()
	self:UpdateZhanDuiName()
	self:UpdateZhanDuiZhanLingIcon()
	--self:UpdateZhanDuiZhanLingText()
	self:UpdateLeaderFlag()
	self:UpdateEternalNightVisible()
	self:UpdateComboKillVisible()
	self:UpdateWuXing()
	self:UpdateTianShen3v3Side()
	self:UpdateIsDrunk()
	self:UpdateBleedShow(self.bleed_show_state, true)
	self:UpdateXunYouLoverName()
    self:TryShowTimer()
    self:UpdateAnscircleImg()
    self:UpdateXiezhuIngVisible()
    self:UpdateXianjieEquipInfo()
    self:UpdateAreaIndexImg()
    self:UpdateHuSongName()
    self:UpdateHuSongImg()
	self:UpdateFGBTeamIcon()
	self:UpdateBeastName()
	self:UpdateXiuWeiIcon()
	self:UpdateUltimateState(self.ultimate_data)
	self:UpdateGodOrDemon()
	self:UpdatePWCampIcon()
	self:UpdateElementImg()
	self:UpdateBeastCatchMessage()
end

-- 在真正需要用实体时再创建
function FollowNameBar:CreateRootNode(prefab_name, obj_type, follow_parent)
	if self.is_root_created then
		return
	end

	self.is_root_created = true
	self.obj_type = obj_type
	local async_loader = AllocAsyncLoader(self, "root_loader")
	async_loader:SetIsUseObjPool(true)
	async_loader:SetIsInQueueLoad(true)
	async_loader:SetParent(follow_parent.transform, false)

	local cbdata = FollowUi.GetCBData()
	cbdata[1] = self
	cbdata[2] = prefab_name
	cbdata[3] = follow_parent
	async_loader:Load("uis/view/miscpre_load_prefab", prefab_name, CreateRootCallBack, cbdata)
end

function FollowNameBar:SetIconList(icon_list)
	self.icon_list = icon_list
end

function FollowNameBar:SetName(name, scene_obj, name_color)
	self.name = name
	self.name_color = name_color or COLOR3B.WHITE

	if scene_obj then
		local vo =  scene_obj:GetVo()
		local obj_type = scene_obj:GetType()
		if obj_type == SceneObjType.Npc then
			self.name_color = COLOR3B.DEFAULT
		elseif obj_type == SceneObjType.Monster then
			if vo ~= nil and vo.level then
				local monster_id = scene_obj:GetMonsterId()
				local str = ""
				if scene_obj:IsTower() then
					for i=1,5 do--塔的五种颜色
						if string.find(name, tostring(i)) then
							self.name_color = ITEM_COLOR[i]
							break
						end
					end
					str = name
				elseif scene_obj:IsDuckRaceMonster() then
					local cfg = DuckRaceWGData.Instance:GetDuckTraitCfgByMonsterId(scene_obj.vo.monster_id)
					str = string.format(Language.DuckRace.DuckNameInDarkView[cfg.duck_index], name)
				else
					str = string.format(Language.Common.FollowNameLevel, name, tostring(vo.level))
					if scene_obj:IsBoss() then
						self.name_color = COLOR3B.RED
					end
				end

				self.name = str
			end
		end
	end

	self:UpdateName()
end

function FollowNameBar:UpdateName()
	if nil ~= self.name_text then
		self.name_text.text = self.name
		local color = Str2C3b(self.name_color)
		self.name_text.color = color

		if self.follow_role_name_obj then
			self.follow_role_name_obj:SetName(self.name, color)
		end
	end
end

function FollowNameBar:SetGuildName(guild_name)
	self.guild_name = guild_name
	self:UpdateGuildName()
end

function FollowNameBar:GetGuideName()
	return self.guild_name
end

function FollowNameBar:UpdateGuildName()
	if not self:IsNil() then
		local guild_name_obj = self.node_list["GuildName"]
		if nil == guild_name_obj then
			return
		end

		local guide_name_parent = self.node_list["GuildNameObj"]
		if nil == guide_name_parent then
			return
		end

		if self.guild_name and self.guild_name ~= "" then
			guild_name_obj.gameObject:SetActive(true)
			guide_name_parent.gameObject:SetActive(true)
			guild_name_obj:GetComponent(TypeText).text = self.guild_name
		else
			guild_name_obj.gameObject:SetActive(false)
			guide_name_parent.gameObject:SetActive(false)
		end

		if self.follow_guild_name_obj then
			self.follow_guild_name_obj:SetName(self.guild_name)
		end
	end
end

function FollowNameBar:SetHuSongName(husong_name)
	self.husong_name = husong_name
	self:UpdateHuSongName()
end

function FollowNameBar:SetHuSongImg(bundle, asset)
	self.husong_asset = asset
	self.husong_bundle = bundle
	self:UpdateHuSongImg()
end

function FollowNameBar:UpdateHuSongName()
	if not self:IsNil() then
		local husong_container = self.node_list["HuSongContainer"]
		local husong_name = self.node_list["husong_name"]
		if self.husong_name and self.husong_name ~= "" then
			if husong_container ~= nil then
				husong_container.gameObject:SetActive(true)
			end
			if husong_name ~= nil then
				husong_name:GetComponent(TypeText).text = self.husong_name
			end
		else
			if husong_container ~= nil then
				husong_container.gameObject:SetActive(false)
			end
		end
	end
end


function FollowNameBar:UpdateHuSongImg()
	if not self:IsNil() then
		local husong_container = self.node_list["HuSongContainer"]
		local husong_img = self.node_list["husong_img"]
		if self.husong_asset and self.husong_bundle then
			if husong_container ~= nil then
				husong_container.gameObject:SetActive(true)
			end
			if husong_img ~= nil then
    			husong_img.image:LoadSprite(self.husong_bundle, self.husong_asset)
			end
		else
			if husong_container ~= nil then
				husong_container.gameObject:SetActive(false)
			end
		end
	end
end

function FollowNameBar:SetZhanDuiName(zhandui3v3_name)
	self.zhandui3v3_name = zhandui3v3_name
	self:UpdateZhanDuiName()
end
function FollowNameBar:SetZhanDuiZhanLingId(zhandui3v3_lingpai_id)
	self.zhandui3v3_lingpai_id = zhandui3v3_lingpai_id
	self:UpdateZhanDuiZhanLingIcon()
end
function FollowNameBar:SetZhanDuiZhanLingText(zhandui3v3_lingpai_name)
	self.zhandui3v3_lingpai_name = zhandui3v3_lingpai_name
	--self:UpdateZhanDuiZhanLingText()
end
function FollowNameBar:UpdateZhanDuiName()
	if not self:IsNil() then
		local zhandui_container = self.node_list["ZhanDuiContainer"]
		local zhandui_name = self.node_list["zhandui_name"]
		if self.zhandui3v3_name and self.zhandui3v3_name ~= "" then
			if zhandui_container ~= nil then
				zhandui_container.gameObject:SetActive(true)
			end
			if zhandui_name ~= nil then
				zhandui_name:GetComponent(TypeText).text = self.zhandui3v3_name
			end
		else
			if zhandui_container ~= nil then
				zhandui_container.gameObject:SetActive(false)
			end
		end
	end
end

function FollowNameBar:UpdateZhanDuiZhanLingIcon()
	if not self:IsNil() then
		local zhandui_container = self.node_list["ZhanDuiContainer"]
		local zhanling_icon = self.node_list["zhanling_icon"]

		if self.zhandui3v3_lingpai_id then
			if zhandui_container ~= nil then
				zhandui_container.gameObject:SetActive(true)
			end
			if zhanling_icon ~= nil then
				local zhan_dui_info = ZhanDuiWGData.Instance:GetZhanDuiInfo()
				local grade_cfg = KF3V3WGData.Instance:GetDuanWeiCfgAll(zhan_dui_info.score)
				ZhanDuiWGCtrl.SetZhanDuiDuanWeiImage(zhanling_icon, grade_cfg)
			end
		else
			if zhandui_container ~= nil then
				zhandui_container.gameObject:SetActive(false)
			end
		end
	end
end
function FollowNameBar:UpdateZhanDuiZhanLingText()
	if not self:IsNil() then
		local zhandui_container = self.node_list["ZhanDuiContainer"]
		local zhanling_text = self.node_list["zhanling_text"]

		if self.zhandui3v3_lingpai_name then
			if zhandui_container ~= nil then
				zhandui_container.gameObject:SetActive(true)
			end
			if zhanling_text ~= nil then
				local text_obj = zhanling_text:GetComponent(TypeText)
				if self.zhandui3v3_lingpai_id then
					local cfg = KF3V3WGData.Instance:GetZhanLingCfg(self.zhandui3v3_lingpai_id)
					local color_def = ZhanDuiWGData.Instance:GetZhanLingTextOutLine(cfg.icon)
					if not IsEmptyTable(color_def) and color_def.outline then
					    local outline = text_obj:GetOrAddComponent(typeof(UnityEngine.UI.Outline))
						outline.effectColor = StrToColor(color_def.outline)
					end
					-- ZhanDuiWGCtrl.ChangeZhanLingTextColor(text_obj, cfg.color_type)
				end
			end
		else
			if zhandui_container ~= nil then
				zhandui_container.gameObject:SetActive(false)
			end
		end
	end
end

-- function FollowNameBar:SetJinjieIcon(icon_id)
-- 	self.fame_rank_icon_id = icon_id
-- 	self:UpdateJinjieIcon()
-- end

-- function FollowNameBar:UpdateJinjieIcon()
-- 	if not self:IsNil() then
-- 		local jinjie_icon = self.node_list["jinjie_icon"]
-- 		if nil == jinjie_icon then
-- 			return
-- 		end

-- 		if self.fame_rank_icon_id and self.fame_rank_icon_id > 0 then
-- 			local bundle_name, asset = ResPath.GetCommonIcon("a2_officer_" .. self.fame_rank_icon_id)
-- 			local cbdata = FollowUi.GetCBData()
-- 			cbdata[1] = jinjie_icon
-- 			jinjie_icon.image:LoadSpriteAsync(bundle_name, asset, LoadSpriteCallBack, cbdata)
-- 		else
-- 			jinjie_icon.gameObject:SetActive(false)
-- 		end
-- 	end
-- end

function FollowNameBar:SetFloatIconAsset(bundle, asset)
	self.float_asset = asset
	self.float_bundle = bundle
	self:UpdateFloatIcon()
end

function FollowNameBar:UpdateFloatIcon()
	if not self:IsNil() then
		local float_icon = self.node_list["float_icon"]
		if nil == float_icon then
			return
		end
		if self.float_asset and self.float_bundle then
			local cbdata = FollowUi.GetCBData()
			cbdata[1] = float_icon
			float_icon.image:LoadSpriteAsync(self.float_bundle, self.float_asset, LoadSpriteCallBack, cbdata)
		else
			float_icon:SetActive(false)
		end
	end
end

function FollowNameBar:SetNPCIcon(npc_icon_id, status)
	self.npc_icon_enable = status
	self.npc_icon_id = npc_icon_id
	self:UpdateNPCIcon()
end

function FollowNameBar:UpdateNPCIcon()
	if not self:IsNil() then
		local npc_icon = self.node_list["npc_icon"]
		if nil == npc_icon then
			return
		end
		if self.npc_icon_enable ~= nil and self.npc_icon_id > 0 then
			local bundle, asset = ResPath.GetNpcIcon("a3_npc_icon_" .. self.npc_icon_id)
			local cbdata = FollowUi.GetCBData()
			cbdata[1] = npc_icon
			npc_icon.image:LoadSpriteAsync(bundle, asset, LoadSpriteCallBack, cbdata)
		elseif self.npc_asset and self.npc_bundle then
			local cbdata = FollowUi.GetCBData()
			cbdata[1] = npc_icon
			npc_icon.image:LoadSpriteAsync(self.npc_bundle, self.npc_asset, LoadSpriteCallBack, cbdata)
		else
			npc_icon:SetActive(false)
		end
	end
end

function FollowNameBar:SetMonsterIconAsset(bundle_name, asset_name)
	self.monster_icon_bundle = bundle_name
	self.monster_icon_asset = asset_name
	self:UpdateMonsterIcon()
end

-- 怪物头顶图标
function FollowNameBar:UpdateMonsterIcon(bundle_name, asset_name)
	if not self:IsNil() then
		if self.node_list["monster_icon"] then
			self.node_list["monster_icon"]:SetActive(false)
			if not self.monster_icon_bundle or not self.monster_icon_asset then
				return
			end
			self.node_list["monster_icon"].image:LoadSprite(self.monster_icon_bundle, self.monster_icon_asset)
			self.node_list["monster_icon"].image:SetNativeSize()
			self.node_list["monster_icon"]:SetActive(true)
		end
	end
end

-- NPC名字上面的头衔（称号）
function FollowNameBar:SetNPCTitle(npc_title, is_show)
	self.npc_title_enable = is_show
	self.npc_title = npc_title or ""
	self:UpdateNPCTitle()
end

function FollowNameBar:UpdateNPCTitle()
	if not self:IsNil() then
		local npc_title = self.node_list["npc_title"]
		if nil == npc_title then
			return
		end

		npc_title:SetActive(self.npc_title_enable)

		if self.npc_title_enable ~= nil and self.npc_title ~= "" then
			npc_title.text.text = self.npc_title
		end
	end
end

function FollowNameBar:SetRoleServerInfo(server_info)
	self.server_info = server_info
	self:UpdateServerInfo()
end

function FollowNameBar:GetServerInfo()
	return self.server_info
end

function FollowNameBar:GetServerInfoActive()
	if self:IsNil() then return false end
	local server_name_obj_container = self.node_list["ServerNameContainer"]
	if nil == server_name_obj_container then
		return false
	end
	return server_name_obj_container.gameObject.activeInHierarchy
end

function FollowNameBar:UpdateServerInfo()
	if not self:IsNil() then
		local server_name_obj = self.node_list["ServerName"]
		local server_name_obj_container = self.node_list["ServerNameContainer"]
		if nil == server_name_obj then
			return
		end
		
		if nil == server_name_obj_container then
			return
		end

		if self.server_info and self.server_info ~= "" then
			server_name_obj_container.gameObject:SetActive(true)
			server_name_obj:GetComponent(TypeText).text = string.format(Language.Common.ServerName, self.server_info)
		else
			server_name_obj_container.gameObject:SetActive(false)
		end

		if self.follow_server_name_obj then
			self.follow_server_name_obj:SetName(string.format(Language.Common.ServerName, self.server_info))
		end
	end
end

function FollowNameBar:ResumeGuildName()
	if self.node_list["GuildName"] then
		self.node_list["GuildName"]:GetComponent(TypeText).text = ""
	end
end

function FollowNameBar:SetRoleVip(vip_level, is_active_vip, is_hide_vip)
	self.vip_level = vip_level
	self.is_active_vip = is_active_vip
	self.is_hide_vip = is_hide_vip
	self:UpdateVip()
end


function FollowNameBar:UpdateVip()
	if not self:IsNil() then
		local vip_obj = self.node_list["vip"]
		if nil == vip_obj then
			return
		end

		if self.vip_level and self.vip_level ~= "" and self.vip_level > 0 then
			--设置VIP隐藏
			local bundle, asset, minWidth
			if self.is_hide_vip then
				bundle, asset = ResPath.GetCommonIcon("a2_vip_hide")
				minWidth = 20
			else
				bundle, asset = ResPath.GetCommonIcon("a2_vip" .. self.vip_level)
				minWidth = 50
			end
			vip_obj.layout_element.minWidth = minWidth
			local cbdata = FollowUi.GetCBData()
			cbdata[1] = vip_obj
			vip_obj.image:LoadSpriteAsync(bundle, asset, LoadSpriteCallBack, cbdata)
		else
			vip_obj.gameObject:SetActive(false)
		end
	end
end

function FollowNameBar:ResumeVip()
	if self.node_list["vip"] then
		self.node_list["vip"]:SetActive(false)
	end
end

function FollowNameBar:SetRoleGodOrDemon(god_or_demon_type, god_or_demon_level)
	self.god_or_demon_type = god_or_demon_type
	self.god_or_demon_level = god_or_demon_level
	self:UpdateGodOrDemon()
end

function FollowNameBar:UpdateGodOrDemon()
	if not self:IsNil() then
		local godordemon_obj = self.node_list["godordemon_img"]
		if nil == godordemon_obj then
			return
		end

		if self.god_or_demon_type and self.god_or_demon_type > 0 and self.god_or_demon_level and self.god_or_demon_level > 0  then
			local bundle, asset
			bundle, asset = ResPath.GetRawImagesPNG("a2_ryxh_" .. self.god_or_demon_type .. "_" .. self.god_or_demon_level)
			local cbdata = FollowUi.GetCBData()
			cbdata[1] = godordemon_obj
			godordemon_obj.raw_image:LoadSpriteAsync(bundle, asset, function ()
				if not IsNil(godordemon_obj.gameObject) then
					godordemon_obj.gameObject:SetActive(true)
					godordemon_obj.raw_image:SetNativeSize()
				end
			end)
		else
			godordemon_obj.gameObject:SetActive(false)
		end
	end
end

function FollowNameBar:ResumeGodOrDemon()
	if self.node_list["godordemon_img"] then
		self.node_list["godordemon_img"]:SetActive(false)
	end
end

function FollowNameBar:SetLeaderFlag(is_show_leader_flag)
	self.is_show_leader_flag = is_show_leader_flag
	self:UpdateLeaderFlag()
end

function FollowNameBar:UpdateLeaderFlag()
	if not self:IsNil() then
		local leader_flag_img = self.node_list["leader_flag_img"]
		if leader_flag_img then
			leader_flag_img:SetActive(self.is_show_leader_flag or false)
		end
	end
end

function FollowNameBar:SetPWCampIcon(camp_id)
	self.pw_camp_id = camp_id
	self:UpdatePWCampIcon()
end

function FollowNameBar:UpdatePWCampIcon()
	if not self:IsNil() then
		local pw_camp_icon = self.node_list["pw_camp_icon"]
		if nil == pw_camp_icon then
			return
		end

		if nil ~= self.pw_camp_id and self.pw_camp_id >= 0 then
			local camp_cfg = PositionalWarfareWGData.Instance:GetCurCampCfg(self.pw_camp_id)

			if not IsEmptyTable(camp_cfg) then
				pw_camp_icon.image:LoadSprite(ResPath.GetPositionalWarfareImg("a3_zdz_tt_" .. camp_cfg.camp_sign))

				pw_camp_icon.gameObject:SetActive(true)
			else
				pw_camp_icon.gameObject:SetActive(false)
			end
		else
			pw_camp_icon.gameObject:SetActive(false)
		end
	end
end

function FollowNameBar:ResumePWCampIcon()
	if self.node_list["pw_camp_icon"] then
		self.node_list["pw_camp_icon"]:SetActive(false)
	end
end

function FollowNameBar:SetLoverName(lover_name)
	self.lover_name = lover_name
	self:UpdateLoverName()
end

function FollowNameBar:GetLoverName()
	return self.lover_name
end

function FollowNameBar:UpdateLoverName()
	if not self:IsNil() then
		local lover_name_obj = self.node_list["LoverName"]
		local lover_name_container_obj = self.node_list["LoverNameContainer"]
		if nil == lover_name_obj then
			return
		end

		if self.lover_name and self.lover_name ~= "" then
			lover_name_container_obj.gameObject:SetActive(true)
			lover_name_obj:GetComponent(TypeText).text = self.lover_name
		else
			lover_name_container_obj.gameObject:SetActive(false)
		end

		if self.follow_lover_name_obj then
			self.follow_lover_name_obj:SetName(self.lover_name)
		end
	end
end

function FollowNameBar:ResumeLoverName()
	if self.node_list["LoverName"] then
		self.node_list["LoverName"]:GetComponent(TypeText).text = ""
	end
end

function FollowNameBar:SetIsActive(is_root_active)
	self.is_root_active = is_root_active
	self:UpdateActive()
end

function FollowNameBar:UpdateActive()
	if nil ~= self.is_root_active and not self:IsNil() then
		self:SetActive(self.is_root_active)
	end
end

function FollowNameBar:SetSpecialImage(is_active, bundle, asset)
	if nil == bundle or nil == asset then
		is_active = false
	end
	self.special_image_info = {is_active = is_active, bundle = bundle, asset = asset}
	self:UpdateSpecialImage()
end

function FollowNameBar:UpdateSpecialImage()
	if self:IsNil() or nil == self.special_image_info then
		return
	end

	if self.special_image_info.asset == "tired_max_icon" then	---过滤疲劳
		return
	end

	local special_image_obj = self.node_list["Image"]
	if nil == special_image_obj then
		return
	end

	if self.node_list.drop then
		self.node_list.drop:SetActive(self.special_image_info.is_active)
	end

	if self.special_image_info.is_active then
		special_image_obj.image:LoadSprite(self.special_image_info.bundle, self.special_image_info.asset, function()
			special_image_obj:SetActive(true)
			special_image_obj.image:SetNativeSize()
		end)
	else
		special_image_obj:SetActive(false)
	end
end

function FollowNameBar:ResumeSpecialImage()
	if self.node_list.drop then
		self.node_list.drop:SetActive(false)
	end

	if self.node_list["Image"] then
		self.node_list["Image"]:SetActive(false)
	end
end

-- 正在采集图片
function FollowNameBar:SetGatherImg(is_active)
	self.gather_img_info = {is_active = is_active}
	self:UpdateGatherImg()
end

function FollowNameBar:UpdateGatherImg()
	if self.gather_img_info and self.node_list and self.node_list["gather_img"] then
		self.node_list["gather_img"]:SetActive(self.gather_img_info.is_active)
	end
end

function FollowNameBar:ResumeGatherImg()
	if self.node_list and self.node_list["gather_img"] then
		self.node_list["gather_img"]:SetActive(false)
	end
end


function FollowNameBar:SetFaceIcon(is_active, asset, bundle, str_tip, str_num, show_commit_task, show_bg)
	if nil == bundle or nil == asset then
		is_active = false
	end
	self.face_icon_info = {is_active = is_active, bundle = bundle, asset = asset, str_tip = str_tip, str_num = str_num, show_commit_task = show_commit_task, show_bg = show_bg}
	self:UpdateFaceIcon()
end

function FollowNameBar:UpdateFaceIcon()
	if self:IsNil() or nil == self.face_icon_info then
		return
	end

	local face_icon_obj = self.node_list["FaceIcon"]
	if not face_icon_obj then
		return
	end

	local face_root = self.node_list.face_root
	if self.face_icon_info.is_active then
		face_icon_obj.image:LoadSpriteAsync(self.face_icon_info.asset, self.face_icon_info.bundle, function()
			if face_icon_obj then
				face_icon_obj.image:SetNativeSize()
			end

			if face_root then
				face_root:SetActive(true)
			end

			if self.node_list.str_tip then
				self.node_list.str_tip.text.text = self.face_icon_info.str_tip or ""
			end

			if self.node_list.str_num then
				self.node_list.str_num.text.text = self.face_icon_info.str_num or ""
			end

			self.node_list.commit_task:SetActive(self.face_icon_info.show_commit_task == true)

			if self.node_list["LoverNameContainer"] then
				self.node_list["LoverNameContainer"]:SetActive(false)
			end

			if self.node_list.face_bg then
				self.node_list.face_bg:SetActive(self.face_icon_info.show_bg ~= nil and self.face_icon_info.show_bg == true)
			end
		end)
	else
		face_root:SetActive(false)
		if self.node_list["LoverNameContainer"] and self.lover_name and "" ~= self.lover_name then
			self.node_list["LoverNameContainer"]:SetActive(true)
		end
	end
end

function FollowNameBar:ResumeFaceIcon()
	if self.node_list["face_root"] then
		self.node_list["face_root"]:SetActive(false)
	end
end

function FollowNameBar:GetFaceIconObj()
	if self.node_list == nil then
		return
	end

	local face_icon_obj = self.node_list["FaceIcon"]
	if not face_icon_obj then
		return
	end

	return face_icon_obj.gameObject
end

function FollowNameBar:ResumeDefaultPosAndScale()
	if not self:IsNil() then
		self.node_list["SceneObjName"].transform.localPosition = Vector3(self.old_offset_x, self.old_offset_y, 0)
	end
end

function FollowNameBar:SetAnchoredPosition(x, y)
	if nil ~= self.anchored_pos and self.anchored_pos.x == x and self.anchored_pos.y == y then
		return
	end

	self.anchored_pos = {x = x, y = y}
	self:UpdateLoacalUI()
end

function FollowNameBar:UpdateLoacalUI()
	if nil ~= self.anchored_pos and nil ~= self.view then
		if nil == self.default_anchored_pos then
			self.default_anchored_pos = self.view.rect.anchoredPosition
		end

		self.view.rect.anchoredPosition = Vector2(self.anchored_pos.x, self.anchored_pos.y)
	end
end

function FollowNameBar:ResumeDefaultLoacalUI()
	if nil ~= self.default_anchored_pos and nil ~= self.view then
		self.view.rect.anchoredPosition = self.default_anchored_pos
	end
end

function FollowNameBar:SetBoxBossAngry(asset, bundle, angry)
	local is_active = true
	if asset == nil or bundle == nil then
		is_active = false
	end
	self.box_boss_angry_info = {is_active = is_active, asset = asset, bundle = bundle, angry = angry}
	self:UpdateBoxBossAngry()
end

function FollowNameBar:UpdateBoxBossAngry()
	if self:IsNil() or nil == self.box_boss_angry_info then
		return
	end

	local boss_angry = self.node_list["BossAngry"]
	if nil == boss_angry then
		return
	end

	if self.box_boss_angry_info.is_active then
		boss_angry:SetActive(true)
		local img_angry = self.node_list["AngryImg"]
		local cbdata = FollowUi.GetCBData()
		cbdata[1] = img_angry
		img_angry.image:LoadSpriteAsync(self.box_boss_angry_info.asset, self.box_boss_angry_info.bundle, LoadSpriteCallBack, cbdata)
		self.node_list["AngryValue"]:GetComponent(TypeText).text = self.box_boss_angry_info.angry
	else
		boss_angry:SetActive(false)
	end
end

function FollowNameBar:ResumeBoxBossAngry()
	if not self:IsNil() then
		local boss_angry = self.node_list["BossAngry"]
		if boss_angry then
			boss_angry:SetActive(false)
		end
	end
end

function FollowNameBar:SetYZWCBuffShow(buff_list)
	if self:IsNil() or self.node_list == nil or self.node_list.yzwc_buff == nil then
        return
    end

    if not self.buff_obj_list then
		self.buff_obj_list = {}
		local num = self.node_list.yzwc_buff.transform.childCount
		for i = 1, num do
            self.buff_obj_list[i] = BuffFollowRender.New(self.node_list["yzwc_slider_" .. i])
			self.buff_obj_list[i]:SetIndex(i)
        end
	end

	if IsEmptyTable(buff_list) then
        self:ResumeYZBuffTime(false)
        return
    end 

 	self:ResumeYZBuffTime(true)
	for i,v in ipairs(self.buff_obj_list) do
		local index = 1
    	if buff_list[i] then
    		v:SetActive(true)
        	v:SetData(buff_list[i])
        	index = index + 1
        else
        	v:SetActive(false)
        end
    end
end

function FollowNameBar:ResumeYZBuffTime(is_act)
    if self.node_list.yzwc_buff ~= nil then
        self.node_list.yzwc_buff:SetActive(is_act)
    end
end

function FollowNameBar:SetScoreStr(score_str)
	self.score_str = score_str
	self:UpdateScore()
end

function FollowNameBar:UpdateScore()
	if nil == self.score_str or self:IsNil() then
		return
	end

	local score_obj = self.node_list["ScoreTxt"]
	if nil ~= score_obj then
		local parent = score_obj.transform.parent
		if nil == self.default_score_txt_act then
			self.default_score_txt_act = parent.gameObject.activeSelf
		end
		UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.ScoreParent.rect)
		parent.gameObject:SetActive("" ~= self.score_str)
		score_obj.text.text = self.score_str
	end
end

function FollowNameBar:ResumeDefaultScoreTxt()
	if nil ~= self.default_score_txt_act and nil ~= self.node_list["ScoreTxt"] then
		local parent = self.node_list["ScoreTxt"].transform.parent
		parent.gameObject:SetActive(self.default_score_txt_act)
	end
end

function FollowNameBar:SetRedSideIcon(bundle_name, asset_name)
	local is_active = true
	if nil == bundle_name or nil == asset_name then
		is_active = false
	end

	self.red_side_info = {is_active = is_active, bundle_name = bundle_name, asset_name = asset_name}
	self:UpdateRedSideIcon()
end

function FollowNameBar:UpdateRedSideIcon()
	if nil == self.red_side_info or self:IsNil() then
		return
	end

	local icon_obj = self.node_list["yz_team_icon"]
	if nil ~= icon_obj then
		if self.red_side_info.is_active then
			local cbdata = FollowUi.GetCBData()
			cbdata[1] = icon_obj
			icon_obj.image:LoadSpriteAsync(self.red_side_info.bundle_name, self.red_side_info.asset_name, LoadSpriteCallBack, cbdata)
			self:SetIconActive(true)
		else
			self:SetIconActive(false)
		end
	end
end

function FollowNameBar:ResumeRedSideIcon()
	self:SetIconActive(false)
end

function FollowNameBar:SetIconActive(is_active)
	self.icon_active = is_active
	self:UpdateIconActive()
end

function FollowNameBar:UpdateIconActive()
	if self:IsNil() then
		return
	end

	if nil ~= self.icon_active and self.node_list["LeftIcon"] then
		if nil == self.default_icon_act then
			self.default_icon_act = self.node_list["LeftIcon"].gameObject.activeSelf
		end
		self.node_list["LeftIcon"]:SetActive(self.icon_active)
	end
end

function FollowNameBar:ResumeIconActive()
	if self.default_icon_act and self.node_list["LeftIcon"] then
		self.node_list["LeftIcon"]:SetActive(self.default_icon_act)
	end
end

function FollowNameBar:SetHasTitle(has_title)
	self.has_title = has_title
	self:UpdateTitle()
end

function FollowNameBar:UpdateTitle()
	if self:IsNil() then
		return
	end

	local title = self.node_list["Title"]
	if not title then
		return
	end

	if self.has_title then
		title:SetActive(true)
	else
		title:SetActive(false)
	end
end

function FollowNameBar:ResumeTitle()
	local title = self.node_list["Title"]
	if not title then
		return
	end

	title:SetActive(false)
end

function FollowNameBar:SetZhuanzhiIconVisible(value)
	self.zhuanzhi_icon_visible = value
	self:UpdateZhuanzhiIconVisible()
end

function FollowNameBar:UpdateZhuanzhiIconVisible()
	if nil == self.zhuanzhi_icon_visible or self:IsNil() then
		return
	end

	if self.node_list["zhuanzhi_icon"] and self.node_list["zhuanzhi_icon"].activeSelf ~= self.zhuanzhi_icon_visible then
		self.node_list["zhuanzhi_icon"]:SetActive(self.zhuanzhi_icon_visible)
	end
end

function FollowNameBar:SetNameContainerVisible(value)
	self.name_container_visible = value
	self:UpdateNameContainerVisible()
end

function FollowNameBar:UpdateNameContainerVisible()
	if nil == self.name_container_visible or self:IsNil() then
		return
	end

	if self.node_list["NameContainer"] then
		if nil == self.default_namecontainer_act then
			self.default_namecontainer_act = self.node_list["NameContainer"].gameObject.activeSelf
		end
		self.node_list["NameContainer"]:SetActive(self.name_container_visible)
	end
end


function FollowNameBar:ResumeNameContainerActive()
	if self.default_namecontainer_act and self.node_list["NameContainer"] then
		self.node_list["NameContainer"]:SetActive(self.default_namecontainer_act)
	end
end

function FollowNameBar:SetPetOnwerName(value)
	self.pet_onwer_name = value
	self:UpdatePetOnwerName()
end

function FollowNameBar:SetXunYouLoverName(value)
	self.marry_lover_name = value
	self:UpdateXunYouLoverName()
end

function FollowNameBar:UpdateXunYouLoverName()
    if not self:IsNil() then
		if self.node_list["SceneObjLoverName"] then
			self.node_list["SceneObjLoverName"].text.text = self.marry_lover_name or "nil"
		end
	end
end

function FollowNameBar:UpdatePetOnwerName()
	if not self:IsNil() then
		if self.node_list["pet_onwer_name"] then
			local name = string.format(Language.Pet.WhoPet, self.pet_onwer_name) or ""
			self.node_list["pet_onwer_name"].text.text = name

			if self.follow_petonwer_name_obj then
				self.follow_petonwer_name_obj:SetName(name)
			end
		end
	end
end

function FollowNameBar:SetBeastName(value)
	self.pet_onwer_name = value
	self:UpdateBeastName()
end

function FollowNameBar:UpdateBeastName()
	if not self:IsNil() then
		if self.node_list["pet_onwer_name"] then
			local name = self.pet_onwer_name or ""
			self.node_list["pet_onwer_name"].text.text = name

			if self.follow_petonwer_name_obj then
				self.follow_petonwer_name_obj:SetName(name)
			end
		end
	end
end

--永夜之巅头顶显示

-- function FollowNameBar:SetEternalNightVisible(value)
-- 	self.eternal_night_visible = value
-- 	self:UpdateEternalNightVisible()
-- end

function FollowNameBar:SetEternalNightEquipList(equip_data)
	self.eternal_night_equip_data = equip_data
	self.eternal_night_visible = equip_data and not IsEmptyTable(equip_data)
	self:UpdateEternalNightVisible()
end

function FollowNameBar:UpdateEternalNightVisible()
	if nil == self.eternal_night_visible or self:IsNil() then
		return
	end
	if self.node_list["EternalNightContainer"] and self.node_list["EternalNightContainer"].activeSelf ~= self.eternal_night_visible then
		if self.eternal_night_equip_list == nil then
			self.eternal_night_equip_list = {}
			self.eternal_night_equip_list_cell = {}
			local obj = self.node_list["eternal_night_equip_list"].transform
			for i=1,3 do
				self.eternal_night_equip_list[i] = U3DObject(obj:Find("equip_icon"..i).gameObject, obj:Find("equip_icon"..i), self)
				self.eternal_night_equip_list_cell[i] = ItemCell.New(self.eternal_night_equip_list[i])
				self.eternal_night_equip_list_cell[i]:SetIsShowTips(false)
				self.eternal_night_equip_list_cell[i]:SetItemTipFrom(ItemTip.FROM_ETERNAL_NIGHT)
			end
		end
		if self.eternal_night_equip_data and not IsEmptyTable(self.eternal_night_equip_data) then
			for i=1,3 do
				local data = self.eternal_night_equip_data[i]
				if data then
					self.eternal_night_equip_list[i]:SetActive(true)
					local cell_data = {item_id = data}
					self.eternal_night_equip_list_cell[i]:SetData(cell_data)
				else
					self.eternal_night_equip_list[i]:SetActive(false)
				end
			end
		end
		local has_data = self.eternal_night_equip_data and not IsEmptyTable(self.eternal_night_equip_data)
		self.node_list["EternalNightContainer"]:SetActive(self.eternal_night_visible and has_data)
	end
end

function FollowNameBar:GetEternalNightEquipData()
	return self.eternal_night_equip_data
end


function FollowNameBar:SetComboKillNum(kill_num)
	if self.node_list and self.node_list["combo_kill_text"] then
		self.combo_kill_num = kill_num
		self.combo_kill_visible = self.combo_kill_num and self.combo_kill_num > 0
		self:UpdateComboKillVisible()
	end
end

function FollowNameBar:UpdateComboKillVisible()
	if nil == self.combo_kill_visible or self:IsNil() then
		return
	end
	if self.node_list["combo_kill"] and self.node_list["combo_kill"].activeSelf ~= self.combo_kill_visible then
		self.node_list["combo_kill"]:SetActive(self.combo_kill_visible)
		self.node_list["combo_kill_text"].text.text = string.format(Language.Common.ComboKillStr,self.combo_kill_num)
	end
end

function FollowNameBar:ResumeEternalNight()
	if self.node_list["EternalNightContainer"] then
		self.node_list["EternalNightContainer"]:SetActive(false)
		if self.eternal_night_equip_list_cell then
			for k,v in pairs(self.eternal_night_equip_list_cell) do
				v:DeleteMe()
			end
			self.eternal_night_equip_list_cell = nil
		end
	end
end

function FollowNameBar:ResumeComboKill()
	if self.node_list["combo_kill"] then
		self.node_list["combo_kill"]:SetActive(false)
	end
end

--仙界装备头顶显示
function FollowNameBar:SetXianjieEquipImg(xianjie_equip_info)
    self.xianjie_equip_info = xianjie_equip_info
	if self.node_list and self.node_list["xianjie_equip_icon"] then
		self:UpdateXianjieEquipInfo()
	end
end

function FollowNameBar:UpdateXianjieEquipInfo()
    if self:IsNil() or not self.node_list then
        return
    end
    if IsEmptyTable(self.xianjie_equip_info) then
        self:ResumeXiezhuIngVisible()
		return
    end
    if self.node_list["xianjie_equip_container"] then
        local cfg = ItemWGData.Instance:GetItemConfig(self.xianjie_equip_info.item_id)
        if cfg and self.xianjie_equip_info.is_show then
            if self.old_xianjie_equip_id ~= self.xianjie_equip_info.item_id then
                local b, a = ResPath.GetItem(cfg.icon_id)
                self.node_list["xianjie_equip_icon"].image:LoadSprite(b, a, function()
                    self.old_xianjie_equip_id = self.xianjie_equip_info.item_id
                    self.node_list["xianjie_equip_icon"].image:SetNativeSize()
                end)

                local bundle, asset = ResPath.GetCommonImages(string.format("a3_ty_wpk_%d", cfg.color))
                self.node_list["xianjie_equip_bg"].image:LoadSprite(bundle, asset, function()
                    self.node_list["xianjie_equip_bg"].image:SetNativeSize()
                end)
            end
        end
        if self.node_list["xianjie_equip_container"].activeSelf ~= self.xianjie_equip_info.is_show then
            self.node_list["xianjie_equip_container"]:SetActive(self.xianjie_equip_info.is_show)
        end
	end
end

function FollowNameBar:ResumeXianJieVisible()
	if self.node_list["xianjie_equip_container"] then
		self.node_list["xianjie_equip_container"]:SetActive(false)
	end
end

------------------------------------------

--战区标记头顶显示
function FollowNameBar:SetAreaIndexImg(area_index)
    self.area_index = area_index
	if self.node_list and self.node_list["area_index_img"] then
		self:UpdateAreaIndexImg()
	end
end

function FollowNameBar:UpdateAreaIndexImg()
    if self:IsNil() or not self.node_list then
        return
    end
    if not self.area_index or self.area_index == -1 then
        self:ResumeAreaIndexImg()
		return
    end
    if self.node_list["area_index_img"] then
        if self.old_area_index ~= self.area_index then
            local bundle, asset = ResPath.GetF2CommonImages("area_index"..self.area_index)
            self.node_list["area_index_img"].image:LoadSprite(bundle, asset, function()
                self.node_list["area_index_img"].image:SetNativeSize()
                self.old_area_index = self.area_index
                self.node_list["area_index_img"]:SetActive(true)
            end)
        end
	end
end

function FollowNameBar:ResumeAreaIndexImg()
    self.old_area_index = nil
	if self.node_list["area_index_img"] then
		self.node_list["area_index_img"]:SetActive(false)
	end
end

------------------------------------------

function FollowNameBar:SetXiezhuIngVisible(flag)
	self.xiezhu_ing_visible = flag
	if self.node_list and self.node_list["xiezhu_ing"] then
		self:UpdateXiezhuIngVisible()
	end
end

function FollowNameBar:UpdateXiezhuIngVisible()
	if nil == self.xiezhu_ing_visible or self:IsNil() then
		return
	end
	if self.node_list["xiezhu_ing"] and self.node_list["xiezhu_ing"].activeSelf ~= self.xiezhu_ing_visible then
		self.node_list["xiezhu_ing"]:SetActive(self.xiezhu_ing_visible)
	end
end

function FollowNameBar:ResumeXiezhuIngVisible()
	if self.node_list["xiezhu_ing"] then
		self.node_list["xiezhu_ing"]:SetActive(false)
	end
end

function FollowNameBar:UpdateBleedShow(is_show, is_init)
	if self:IsNil() then
		return
	end

	if self.bleed_show_state == is_show and not is_init then
		return
	end

	self.bleed_show_state = is_show
	if self.node_list.img_bleed ~= nil then
		self.node_list.img_bleed:SetActive(is_show)
	end
end

function FollowNameBar:ResumeBleedShow()
	self.bleed_show_state = false
	if self.node_list["img_bleed"] then
		self.node_list["img_bleed"]:SetActive(false)
	end
end

local scale = Vector3(1, 0, 1)
function FollowNameBar:CreateAttachName(prefab_name, follow_parent)
	local parent_root = follow_parent.transform.parent

	self.node_list["SceneObjName"].transform.localScale = scale
	self.follow_role_name_obj = FollowName.New(prefab_name, self.node_list["SceneObjName"].transform, parent_root)

	if self.node_list["GuildName"] then
		self.node_list["GuildName"].transform.localScale = scale
		self.follow_guild_name_obj = FollowName.New("GuildName", self.node_list["GuildName"].transform, parent_root)
	end

	if self.node_list["LoverName"] then
		self.node_list["LoverName"].transform.localScale = scale
		self.follow_lover_name_obj = FollowName.New("LoverName", self.node_list["LoverName"].transform, parent_root)
	end

	if self.node_list["ServerName"] then
		self.node_list["ServerName"].transform.localScale = scale
		self.follow_server_name_obj = FollowName.New("ServerName", self.node_list["ServerName"].transform, parent_root)
	end

	if self.node_list["pet_onwer_name"] then
		self.node_list["pet_onwer_name"].transform.localScale = scale
		self.follow_petonwer_name_obj = FollowName.New(prefab_name, self.node_list["pet_onwer_name"].transform, parent_root)
	end
end

function FollowNameBar:SetTianShen3v3Side(tianshen_3v3_side)
	self.tianshen_3v3_side = tianshen_3v3_side or -1
	self:UpdateTianShen3v3Side()
end


function FollowNameBar:UpdateTianShen3v3Side()
	if not self:IsNil() then
		local icon = self.node_list["tianshen_3v3_side_icon"]
		if nil == icon then
			return
		end

		if self.tianshen_3v3_side and self.tianshen_3v3_side >= 0 then
			local bundle, asset = ResPath.GetF2CommonImages("tianshen_3v3_side_icon_" .. self.tianshen_3v3_side)
			local cbdata = FollowUi.GetCBData()
			cbdata[1] = icon
			icon.image:LoadSpriteAsync(bundle, asset, LoadSpriteCallBack, cbdata)
			icon.gameObject:SetActive(true)
		else
			icon.gameObject:SetActive(false)
		end
	end
end
function FollowNameBar:ResumeTianShen3v3Side()
	if self.node_list["tianshen_3v3_side_icon"] then
		self.node_list["tianshen_3v3_side_icon"]:SetActive(false)
	end
end

function FollowNameBar:SetIsDrunk(is_drunk)
	self.is_drunk = is_drunk
	self:UpdateIsDrunk()
end


function FollowNameBar:UpdateIsDrunk()
	if not self:IsNil() then
		local icon = self.node_list["drunk_label"]
		if nil == icon then
			return
		end
		icon.gameObject:SetActive(self.is_drunk)
	end
end


function FollowNameBar:ResumeDrunkLabel()
	if self.node_list["drunk_label"] then
		self.node_list["drunk_label"]:SetActive(false)
	end
end
------------------------------------------------------------角色五行属性
function FollowNameBar:SetRoleWuXing(wuxing_type)
	self.wuxing_type = wuxing_type
	self:UpdateWuXing()
end


function FollowNameBar:UpdateWuXing()
	if not self:IsNil() then
		local wuxing_cion_obj = self.node_list["wuxing_cion"]
		if nil == wuxing_cion_obj then
			return
		end

		if self.wuxing_type and self.wuxing_type ~= "" and self.wuxing_type > 0 then
			local bundle, asset = ResPath.GetF2CommonImages("wuxing_small_" .. self.wuxing_type)
			local cbdata = FollowUi.GetCBData()
			cbdata[1] = wuxing_cion_obj
			wuxing_cion_obj.image:LoadSpriteAsync(bundle, asset, LoadSpriteCallBack, cbdata)
			wuxing_cion_obj.gameObject:SetActive(true)
		else
			wuxing_cion_obj.gameObject:SetActive(false)
		end
	end
end

function FollowNameBar:GetRoleRootNodeWidthAndHeight()
	local width, height = 0, 0
	local namebar_root_node = self.node_list and self.node_list["role_namebar_root"]
	if namebar_root_node then
		width = namebar_root_node.rect.rect.width
		height = namebar_root_node.rect.rect.height
	end
	return width, height
end

function FollowNameBar:GetTitleVisable()
	local is_vis = false
	if self.node_list ~= nil and self.node_list["Title"] then
		is_vis = self.node_list["Title"].gameObject.activeSelf
	end
	return is_vis
end

function FollowNameBar:GetTitleHeight()
	local height = 0
	if self.node_list ~= nil and self.node_list["Title"] then
		height = self.node_list["Title"].layout_element.minHeight
	end
	return height
end

function FollowNameBar:ResumeShowTimer()
    if self.timer_obj_id then
        if CountDown.Instance:HasCountDown("follow_ui_name_timer"..self.timer_obj_id) then
            CountDown.Instance:RemoveCountDown("follow_ui_name_timer"..self.timer_obj_id)
        end
    end
    self.timer_obj_id = nil
	self.timer_str = nil
	self.time_value = nil
	if self.node_list.root_countdown ~= nil then
		self.node_list.root_countdown:SetActive(false)
	end
end

function FollowNameBar:TryShowTimer()
	if self.timer_str == nil or self.time_value == nil or self.timer_obj_id == nil then
		return
	end

	self:SetShowTimer(self.timer_str, self.time_value, self.timer_obj_id)
end

function FollowNameBar:SetShowTimer(timer_str, time_value, timer_obj_id)
	if CountDown.Instance:HasCountDown("follow_ui_name_timer"..timer_obj_id) then
		CountDown.Instance:RemoveCountDown("follow_ui_name_timer"..timer_obj_id)
	end

	if timer_str == nil or time_value == nil or timer_obj_id == nil  then
		self:ResumeShowTimer()
		return
	end

	if time_value <= 0 or time_value <= TimeWGCtrl.Instance:GetServerTime() then
		self:ResumeShowTimer()
		return
	end

	self.timer_str = timer_str
	self.time_value = time_value
    self.timer_obj_id = timer_obj_id
	if self:IsNil() then
		return
	end

	if self.node_list == nil then
		return
	end

	local has_time = math.floor(self.time_value)
	self:AddTimer(has_time, self.timer_str, self.timer_obj_id)
end

function FollowNameBar:AddTimer(time, str, timer_obj_id)
	if CountDownManager.Instance:HasCountDown("follow_ui_name_timer"..timer_obj_id) then
		CountDownManager.Instance:RemoveCountDown("follow_ui_name_timer"..timer_obj_id)
	end

	if time == nil or str == nil or timer_obj_id == nil then
		return
	end

	if time <= 0 or str == "" then
		return
	end

	if self.node_list.root_countdown == nil then
		return
	end

	self.node_list.root_countdown:SetActive(true)
	self:FlushCountDown(0, time - TimeWGCtrl.Instance:GetServerTime())
	CountDownManager.Instance:AddCountDown("follow_ui_name_timer"..timer_obj_id, BindTool.Bind1(self.FlushCountDown, self), BindTool.Bind1(self.CompleteCountDown, self), time, nil, 1)
end

function FollowNameBar:FlushCountDown(elapse_time, total_time)
	if self.node_list == nil then
		if self.timer_obj_id ~= nil and CountDownManager.Instance:HasCountDown("follow_ui_name_timer"..self.timer_obj_id) then
			CountDownManager.Instance:RemoveCountDown("follow_ui_name_timer"..self.timer_obj_id)
		end

        self.timer_obj_id = nil
		self.timer_str = nil
		self.time_value = nil
		return
	end

	if self.node_list.text_coundown ~= nil and self.timer_str ~= nil and self.timer_str ~= "" then
		local value = math.floor(total_time - elapse_time)
		value = string.format(self.timer_str, value)

		self.node_list.text_coundown.text.text = value
	end
end

function FollowNameBar:CompleteCountDown()
    if self.timer_obj_id then
        if CountDownManager.Instance:HasCountDown("follow_ui_name_timer"..self.timer_obj_id) then
            CountDownManager.Instance:RemoveCountDown("follow_ui_name_timer"..self.timer_obj_id)
        end
    end

	if self.node_list ~= nil and self.node_list.root_countdown ~= nil then
		self.node_list.root_countdown:SetActive(false)
	end
end

function FollowNameBar:UpdateAnscircleImg(res_name)
    if res_name then
        if not self.answer_circle_name or self.answer_circle_name ~= res_name then
            self.answer_circle_name = res_name
        end
    end

    if not self:IsNil() and self.answer_circle_name and self.answer_circle_name ~= "" then
        local answer_circle_bg = self.node_list["answer_circle_bg"]
		if nil == answer_circle_bg then
			return
        end

        local bundle, asset = ResPath.GetLoadingPath(self.answer_circle_name)
        answer_circle_bg.image:LoadSprite(bundle, asset)
        answer_circle_bg:SetActive(true)
    end
end

function FollowNameBar:ResumeAnscircleImg()
	if self.answer_circle_name then
		self.answer_circle_name = nil
	end

	if self.node_list.answer_circle_bg ~= nil then
		self.node_list.answer_circle_bg:SetActive(false)
	end
end

function FollowNameBar:SetRoleFGBTeamInfo(fgb_team_id)
	self.fgb_team_id = fgb_team_id
	self:UpdateFGBTeamIcon()
end

function FollowNameBar:UpdateFGBTeamIcon()
	if not self:IsNil() then
		local fgb_obj = self.node_list["fgb_team_container"]
		local fgb_icon_obj = self.node_list["fgb_team_flag"]
		local fgb_text_obj = self.node_list["fgb_team_name"]

		if nil == fgb_obj or nil == fgb_icon_obj or nil == fgb_text_obj then
			return
		end
		
		if self.fgb_team_id and self.fgb_team_id >= 0 then
			local bundle, asset = ResPath.GetCrossFGBPathImg("a3_gjzz_mc" .. self.fgb_team_id)
			fgb_icon_obj.image:LoadSprite(bundle, asset, function ()
				fgb_icon_obj.image:SetNativeSize()
			end)

			local camp_cfg = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBCampCfgByCampId(self.fgb_team_id)
			local team_name = camp_cfg and camp_cfg.camp_name or ""
			fgb_text_obj.text.text = team_name
			fgb_obj.gameObject:SetActive(true)
		else
			fgb_obj.gameObject:SetActive(false)
		end
	end
end

function FollowNameBar:ResumeFGBIcon()
	if self.node_list["fgb_team_container"] then
		self.node_list["fgb_team_container"]:SetActive(false)
	end
end

------------------------------------孔位item列表------------------------
BuffFollowRender = BuffFollowRender or BaseClass(BaseRender)

function BuffFollowRender:__init()

end

function BuffFollowRender:__delete()
	if CountDown.Instance:HasCountDown("YZWCBuff" .. self.index) then
    	CountDown.Instance:RemoveCountDown("YZWCBuff" .. self.index)
	end
end

function BuffFollowRender:LoadCallBack()

end

function BuffFollowRender:OnFlush()
	if IsEmptyTable(self.data) or self.node_list == nil then
		return
	end

	if CountDown.Instance:HasCountDown("YZWCBuff" .. self.index) then
    	CountDown.Instance:RemoveCountDown("YZWCBuff" .. self.index)
	end

	local bundle, asset = ResPath.GetLoadingPath("a3_buff_" .. self.data.info.client_effect_type)
	self.node_list.buff_icon.image:LoadSprite(bundle, asset, function()
		self.node_list.buff_icon.image:SetNativeSize()
	end)

	if self.data.info.cd_time > 0 then
		self.node_list.yzwc_slider:SetActive(true)
		self.next_buff_time = self.data.info.remaining_time + TimeWGCtrl.Instance:GetServerTime()
		CountDownManager.Instance:AddCountDown("YZWCBuff" .. self.index, BindTool.Bind(self.UpdateYZBuffTime, self), BindTool.Bind1(self.CompleteYZCallBack, self), nil, self.data.info.remaining_time, 1)
	end
end

function BuffFollowRender:CompleteYZCallBack()
	if CountDown.Instance:HasCountDown("YZWCBuff" .. self.index) then
    	CountDown.Instance:RemoveCountDown("YZWCBuff" .. self.index)
	end

	if self.node_list and self.node_list.yzwc_slider ~= nil then
		self.node_list.yzwc_slider.slider.value = 1
	end

	KuafuYeZhanWangChengWGCtrl.Instance:OnYZWCBuffChange()
end

function BuffFollowRender:UpdateYZBuffTime(elapse_time, total_time)
	if self.node_list == nil or not self.node_list.yzwc_slider then
		return
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	self.node_list.yzwc_slider.slider.value = (self.data.info.cd_time - elapse_time) / total_time
end

-------------------------------------修为ICON----------------------------------
function FollowNameBar:SetXiuWeiIcon(icon_id)
	self.xiuwei_icon = icon_id
	self:UpdateXiuWeiIcon()
end

function FollowNameBar:UpdateXiuWeiIcon()
	if not self:IsNil() then
		local xiuwei_icon_root = self.node_list["xiuwei_icon_root"]
		local xiuwei_icon = self.node_list["xiuwei_icon"]
		if nil == xiuwei_icon or nil == xiuwei_icon_root then
			return
		end

		if self.xiuwei_icon then
			local stage_cfg = CultivationWGData.Instance:GetXiuWeiStageCfgByState(self.xiuwei_icon)

			if not IsEmptyTable(stage_cfg) then
				local bundle_name, asset = ResPath.GetCultivationStageIcon(stage_cfg.stage_icon)
				local cbdata = FollowUi.GetCBData()
				cbdata[1] = xiuwei_icon
				xiuwei_icon.image:LoadSpriteAsync(bundle_name, asset, LoadSpriteCallBack, cbdata)
				xiuwei_icon_root.gameObject:SetActive(true)
			else
				xiuwei_icon_root.gameObject:SetActive(false)
			end
		else
			xiuwei_icon_root.gameObject:SetActive(false)
		end
	end
end

function FollowNameBar:UpdateUltimateState(ultimate_data)
	if self.node_list == nil or self.node_list.UltimateContainer == nil or ultimate_data == nil then
		return
	end

	if ultimate_data then
		local color = ultimate_data.camp == 0 and TIPS_COLOR.GETWAY_VALUE or COLOR3B.PINK
		self.node_list.UltimateContainer:CustomSetActive(true)
		self.node_list.ultimate_server.text.text = ToColorStr(string.format(Language.Kuafu1V1.SeverID, ultimate_data.server_id), color) 
		self.node_list.ultimate_name.text.text = ToColorStr(ultimate_data.camp_name, color) 
		self.node_list.ultimate_icon.image:LoadSprite(ResPath.GetCountryUltimateImg(string.format("a2_zjzc_tb%d", ultimate_data.camp)))
	else
		self.node_list.UltimateContainer:CustomSetActive(false)
	end
end

-- 设置终极战场人物名称上面的数据
function FollowNameBar:SetUltimateState(ultimate_data)
	self.ultimate_data = ultimate_data
	self:UpdateUltimateState(ultimate_data)
end

function FollowNameBar:ResumeUltimateState()
	if self.node_list and self.node_list.UltimateContainer then
		self.node_list.UltimateContainer:CustomSetActive(false)
	end
end

-- 设置场景幻兽的元素图标
function FollowNameBar:SetBeastElementImg(beast_element)
	self.beast_element = beast_element
	self:UpdateElementImg()
end

function FollowNameBar:UpdateElementImg()
	if self.node_list == nil or self.beast_element == nil then
		return
	end

	self.node_list.ElementImg:CustomSetActive(true)
	local bundle, asset = ResPath.GetLoadingPath(string.format("a3_hs_bq_small_%d", self.beast_element))
	self.node_list.ElementImg.image:LoadSpriteAsync(bundle, asset)
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.beast_layout_root.rect)
end

function FollowNameBar:ResumeElementImg()
	if self.node_list and self.node_list.ElementImg then
		self.node_list.ElementImg:CustomSetActive(false)
	end
end

-- 设置场景上幻兽采集进度
function FollowNameBar:SetBeastCatchMessage(beast_catch_slider, beast_catch_duration)
	self.old_beast_catch_slider = self.beast_catch_slider or 0
	self.beast_catch_slider = beast_catch_slider
	self.beast_catch_duration = beast_catch_duration
	self:UpdateBeastCatchMessage()
end

--更新捕捉信息
function FollowNameBar:UpdateBeastCatchMessage()
	if self.node_list == nil or self.beast_catch_slider == nil then
		return
	end

	self.node_list.beast_catch_root:CustomSetActive(true)
	self.node_list.beast_catch_slider.slider.value = self.old_beast_catch_slider or 0
	local aim_value = self.beast_catch_slider or 1
	local time = self.beast_catch_duration or 0.5
	self.node_list.beast_catch_icon.animator:CrossFadeInFixedTime("beast_catch_shake", 0)
	self.node_list.beast_catch_slider.slider:DOValue(aim_value, time)
end

--重置捕捉信息
function FollowNameBar:ResumeBeastCatchMessage()
	self.old_beast_catch_slider = nil
	self.beast_catch_slider = nil
	self.beast_catch_duration = nil

	if self.node_list and self.node_list.beast_catch_root then
		self.node_list.beast_catch_root:CustomSetActive(false)
	end
end