FiveElementsWGData.DaohunAllAttrNum = 6

----五行沧溟拓展 道魂
function FiveElementsWGData:InitDaoHunData()
	local cfg = self.waistlight_cfg_auto
	if cfg then
		self.daohun_cfg = ListToMap(cfg.waist_soul_activiate, "type")
		self.daohun_attr_cfg_list = ListToMap(cfg.waist_soul_upgrade_attr, "type", "level")
		self.daohun_upgrade_item_cfg = ListToMap(cfg.waist_soul_upgrade_attr, "item_id")
		self.daohun_skill_cfg_list = ListToMapList(cfg.waist_soul_skill_improve, "type")
		self.daohun_factor_cfg = ListToMap(cfg.waist_suit_soul_attr_factor, "type", "star")
	end
end

---------------------------------协议数据 start---------------------------------
function FiveElementsWGData:SetDaohunInfoList(protocol)
	--print_error("SetDaohunInfoList", protocol.all_daohun_list)
	self.daohun_info_list = protocol.all_daohun_list
end

function FiveElementsWGData:SetDaohunInfoItemActive(protocol)
	--print_error("SetDaohunInfoItemActive", protocol)
	if self.daohun_info_list[protocol.cangming_id] then
		self.daohun_info_list[protocol.cangming_id].is_active = true
	end
end

function FiveElementsWGData:SetDaohunInfoItemLevel(protocol)
	--print_error("SetDaohunInfoItemLevel", protocol)
	if self.daohun_info_list[protocol.cangming_id] then
		self.daohun_info_list[protocol.cangming_id].daohun_level = protocol.daohun_level
	end
end

function FiveElementsWGData:GetDaohunInfoByType(type)
	return self.daohun_info_list and self.daohun_info_list[type] or {}
end
---------------------------------协议数据 end---------------------------------

---------------------------------配置数据 start---------------------------------
function FiveElementsWGData:GetDaohunFactor(type)
	local cfg = self.daohun_factor_cfg and self.daohun_factor_cfg[type]
	if IsEmptyTable(cfg) then
		return 0
	end

	local cangming_level = self:GetWaistLightOpen(type) or 0--这个沧溟类型 最小等级的孔位的 等级

	return (cfg[cangming_level] or {}).factor or 0
end

function FiveElementsWGData:GetDaohunFactorCfgList(type)
	return self.daohun_factor_cfg and self.daohun_factor_cfg[type]
end

function FiveElementsWGData:GetDaohunLevel(type)
	return (self:GetDaohunInfoByType(type) or {}).daohun_level or 0
end

function FiveElementsWGData:GetDaohunAttrCfgByType(type)
	return self.daohun_attr_cfg_list and self.daohun_attr_cfg_list[type] or {}
end

function FiveElementsWGData:GetEffectNameByType(type)
	if IsEmptyTable(self.daohun_cfg) then
		return ""
	end
	
	return (self.daohun_cfg[type] or {}).effect_name or ""
end

function FiveElementsWGData:GetDaohunCfg()
	if IsEmptyTable(self.daohun_cfg) then
		return {}
	end

	local cfg_list = {}
	local is_can_show
	for k, v in pairs(self.daohun_cfg) do
		is_can_show =  self:GetWaistItemIsCanShow(v.type)
		--print_error(v.type, is_can_show)
		if is_can_show then
			table.insert(cfg_list, v)
		end
	end

	return cfg_list
end

function FiveElementsWGData:GetIsDaohunUpgradeItem(item_id)
	--print_error("GetIsDaohunUpgradeItem", item_id, self.daohun_upgrade_item_cfg)
	local cfg = self.daohun_upgrade_item_cfg and self.daohun_upgrade_item_cfg[item_id]
	return not IsEmptyTable(cfg)
end

function FiveElementsWGData:GetDaohunCurrentLvAttrCfg(type)
	local level = self:GetDaohunLevel(type)
	return (self:GetDaohunAttrCfgByType(type) or {})[level] or {}
end

function FiveElementsWGData:GetDaohunCfgByType(type)
	return (self:GetDaohunCfg() or {})[type] or {}
end

function FiveElementsWGData:GetDaohunSkillCfg(type)
	local cfg = (self.daohun_skill_cfg_list or {})[type]
	if IsEmptyTable(cfg) then
		return {}
	end

	local daohun_level = self:GetDaohunLevel(type)
	for k, v in pairs(cfg) do
		if v.min_level <= daohun_level and  daohun_level <= v.max_level then
			return v
		end
	end

	return {}
end

function FiveElementsWGData:GetDaohunMaxLevel(type)
	local cfg_list = self:GetDaohunAttrCfgByType(type)
	return cfg_list and (cfg_list[#cfg_list] or {}).level or 0
end
---------------------------------配置数据 end---------------------------------

--该沧溟所有部位达到N星，可激活对应道魂
function FiveElementsWGData:GetDaohunIsActive(type)
	local cfg = self:GetDaohunCfgByType(type)
	local info = self:GetDaohunInfoByType(type)
	if IsEmptyTable(cfg) or IsEmptyTable(info) then
		return false
	end
	
	local cangming_level = self:GetWaistLightOpen(type)--这个沧溟类型 最小等级的孔位的 等级
	--print_error(type, cangming_level , cfg.star, info.is_active)
	return cangming_level >= cfg.star and info.is_active
end

--根据道魂ID获取对应属性列表
function FiveElementsWGData:GetDaohunAttrList(type)
	local cfg = self:GetDaohunAttrCfgByType(type)
	if IsEmptyTable(cfg) then
		return nil, nil
	end

	local daohun_level = self:GetDaohunLevel(type) or 0
	local max_level = self:GetDaohunMaxLevel(type)
	return self:GetDaohunAttrListInternal(cfg, FiveElementsWGData.DaohunAllAttrNum, max_level, daohun_level)
end

function FiveElementsWGData:GetDaohunAttrListInternal(attr_cfg, attr_count, max_level, daohun_level)
	local attr_id, attr_value = 0, 0
	local em_data = EquipmentWGData.Instance
	local attr_list = {}
	local show_level, show_attr_value
	for i = 1, attr_count do
		show_level,show_attr_value = -1, nil
		
        attr_id = attr_cfg[daohun_level] and attr_cfg[daohun_level]["attr_id" .. i]
        attr_value = attr_cfg[daohun_level] and attr_cfg[daohun_level]["attr_value" .. i]
		show_attr_value = attr_value

		if attr_value and attr_value <= 0 then
			show_level = self:GetNextSpecialAttrLevel(attr_cfg, daohun_level, i)--特殊属性要达到特定等级才激活
        	show_attr_value = attr_cfg[show_level] and attr_cfg[show_level]["attr_value" .. i] or 0
		end

		if attr_id and attr_id > 0 then
			local attr_str = em_data:GetAttrStrByAttrId(attr_id)
            local data = {}
			data.attr_id = attr_id
			data.attr_value = show_attr_value--用于在界面上展示
			data.attr_str = attr_str
			data.real_attr_value = attr_value--用于计算战力
			if show_level > 0 then
				data.add_value_str = string.format(Language.FiveElements.DaohunSpecialAttr, show_level)
			elseif daohun_level < max_level then
				data.add_value = attr_cfg[daohun_level + 1] and attr_cfg[daohun_level + 1]["attr_value" .. i] or 0
			else
				data.add_value = 0
			end

			data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
            table.insert(attr_list, data)
		end
	end

	return attr_list
end

--获取下一个值不为0的特殊属性的等级
function FiveElementsWGData:GetNextSpecialAttrLevel(attr_cfg, curLevel, index)
	if IsEmptyTable(attr_cfg) then
		return -1
	end
	
	for i = curLevel, #attr_cfg do
		local attrValue = attr_cfg[i] and attr_cfg[i]["attr_value" .. index]
		if attrValue > 0 then
			return i
		end
	end
	
	return -1
end

--计算战力
function FiveElementsWGData:GetDaohunCap(type)
	local attribute = AttributePool.AllocAttribute()
	local cap = 0
	local info = self:GetDaohunInfoByType(type)
	local cfg = self.daohun_factor_cfg and self.daohun_factor_cfg[type]
	if IsEmptyTable(info) or IsEmptyTable(cfg) then
		return cap
	end
	local factor = self:GetDaohunFactor(type)
	if info.daohun_level > 0 then
		local attr_list = self:GetDaohunAttrList(type)
		for i, v in pairs(attr_list) do
            attribute[v.attr_str] = attribute[v.attr_str] + v.real_attr_value
		end

		attribute = attribute * (1 + (factor / 10000))
		cap = AttributeMgr.GetCapability(attribute)
	end

	return cap
end

--获取减少的普攻累计计数
function FiveElementsWGData:GetSkillDisNum(type)
	local cfg = self:GetDaohunSkillCfg(type)
	local cangming_cfg = self:GetWaistLightOtherCfg()
	if IsEmptyTable(cfg) or IsEmptyTable(cangming_cfg) then
		return 0
	end
	
	local atk_num = cangming_cfg.attack_num or 0
	local count = cfg.count or 0
	return atk_num - count
end