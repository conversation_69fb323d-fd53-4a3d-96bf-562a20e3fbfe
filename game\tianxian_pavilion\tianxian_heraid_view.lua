TianxianHeraidView = TianxianHeraidView or BaseClass(SafeBaseView)

function TianxianHeraidView:__init()
	self:AddViewResource(0, "uis/view/tianxian_pavilion_ui_prefab", "layout_tianxian_heraid")
	self:SetMaskBg(false, true)
end

function TianxianHeraidView:__delete()

end

function TianxianHeraidView:ReleaseCallBack()
	if self.display_model then
		self.display_model:DeleteMe()
		self.display_model = nil
	end
end

function TianxianHeraidView:LoadCallBack()
	if not self.display_model then
		self.display_model = OperationActRender.New(self.node_list["display_model"])
		self.display_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	self:FlushModel()
end

function TianxianHeraidView:FlushModel()
	local heraid_info = TianxianPavilionWGData.Instance:GetHeraidCfg()
	if IsEmptyTable(heraid_info) then
		return
	end

	local heraid_data = {}
	heraid_data.should_ani = true
	if heraid_info.model_heraid_itemid ~= 0 and heraid_info.model_heraid_itemid ~= "" then
		local split_list = string.split(heraid_info.model_heraid_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			heraid_data.model_item_id_list = list
		else
			heraid_data.item_id = heraid_info.model_heraid_itemid
		end
	end

	heraid_data.bundle_name = heraid_info["model_bundle_name"]
	heraid_data.asset_name = heraid_info["model_asset_name"]
	local model_heraid_type = tonumber(heraid_info["model_heraid_type"]) or 1
	heraid_data.render_type = model_heraid_type - 1

	self.display_model:SetData(heraid_data)
	local scale = heraid_info["heraid_scale"]
	Transform.SetLocalScaleXYZ(self.node_list["display_model"].transform, scale, scale, scale)
	local pos_x, pos_y = 0, 0
	if heraid_info.heraid_pos and heraid_info.heraid_pos ~= "" then
		local pos_list = string.split(heraid_info.heraid_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	RectTransform.SetAnchoredPositionXY(self.node_list.display_model.rect, pos_x, pos_y)

	if heraid_info.heraid_rot and heraid_info.heraid_rot ~= "" then
		local heraid_rot_tab = string.split(heraid_info.heraid_rot, "|")
		self.node_list["display_model"].transform.rotation = Quaternion.Euler(heraid_rot_tab[1], heraid_rot_tab[2],
			heraid_rot_tab[3])
	end
end

function TianxianHeraidView:OnFlush()

end
