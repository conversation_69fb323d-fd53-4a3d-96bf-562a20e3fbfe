--报名过的活动自动进入该活动的活动场景的枚举
local LAST_TIME = 34

SIGNUPED_AUTO_JOIN_ACT_TYPE = {

	--20
	[ACTIVITY_TYPE.HOTSPRING] = function (act_type)
		local ok_func = function ()
			ActivityWGCtrl.Instance:SendActivityEnterReq(act_type)
		end
		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)

		if not act_cfg then
			return
		end

		local content_txt = string.format(Language.Activity.ActOpneTip,act_cfg.name)
		TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt,ok_func,nil,Language.Boss.QianWang,LAST_TIME)

	end,

	--3
	[ACTIVITY_TYPE.HUSONG] = function (act_type)

		local ok_func = function ()
			ActIvityHallWGCtrl.Instance:DoHuSong()
		end
		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)

		if not act_cfg then
			return
		end
		local times = YunbiaoWGData.Instance:GetHusongRemainTimes()
		if times > 0 then
			local content_txt = string.format(Language.Activity.ActOpneTip,act_cfg.name)
			ActivityWGCtrl.Instance:OpenSecondConfirmationView(content_txt,ok_func,nil,Language.Boss.QianWang,LAST_TIME)
		end
	end,

	--4
	[ACTIVITY_TYPE.TIANSHENJIANLIN] = function (act_type)
		TianshenRoadWGCtrl.Instance:GotoShiLian()
		--[[local ok_func = function ()
			TianshenRoadWGCtrl.Instance:GotoShiLian()
		end

		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)
		if not act_cfg then
			return
		end
		local content_txt = string.format(Language.Activity.ActOpneTip,act_cfg.name)
		TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt, ok_func, nil, Language.Boss.QianWang, LAST_TIME)
		--]]
	end,

	--5
	[ACTIVITY_TYPE.XINGTIANLAIXI] = function (act_type)
		QuanMinBeiZhanWGCtrl.Instance:GotoShiLian()
		--[[
		local ok_func = function ()
			QuanMinBeiZhanWGCtrl.Instance:GotoShiLian()
		end
		
		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)
		if not act_cfg then
			return
		end
		local content_txt = string.format(Language.Activity.ActOpneTip,act_cfg.name)
		TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt,ok_func, nil, Language.Boss.QianWang, LAST_TIME)
		--]]
	end,

	[ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI_CHILD] = function (act_type)
		ActXianQiJieFengWGCtrl.Instance:GotoShiLian()
	end,

	--3073
	-- [ACTIVITY_TYPE.KF_HONORHALLS] = function (act_type)
	-- 	-- local cur_openserver_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	-- 	-- local other_cfg = DailyWGData.Instance:GetDailyOtherConfig()
	-- 	local ok_func = function ()
	-- 		KuafuHonorhallWGCtrl.Instance:EnterXiuLuoTower()
	-- 	end
	-- 	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)
	-- 	local act_name = act_cfg and act_cfg.name or ""
	-- 	local content_txt = string.format(Language.Activity.ActOpneTip, act_name)
	-- 	TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt, ok_func, nil, Language.Boss.QianWang, LAST_TIME)

	-- end,
	--28
	[ACTIVITY_TYPE.GUILD_ANSWER] = function (act_type)
		if RoleWGData.Instance.role_vo.guild_id > 0 then
			local ok_func = function ()
				FunOpen.Instance:OpenViewNameByCfg("guildanswer")
			end
			local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)

			if not act_cfg then
				return
			end

			local content_txt = string.format(Language.Activity.ActOpneTip,act_cfg.name)
			TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt,ok_func,nil,Language.Boss.QianWang,LAST_TIME)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotEnterNoGuild)
		end
	end,
	--27
	[ACTIVITY_TYPE.GUILD_FB] = function(act_type)
		if RoleWGData.Instance.role_vo.guild_id > 0 then
			local ok_func = function ()
				GuildWGCtrl.Instance:SendGuildFbEnterReq()
			end
			local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)

			if not act_cfg then
				return
			end

			local content_txt = string.format(Language.Activity.ActOpneTip,act_cfg.name)
			TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt,ok_func,nil,Language.Boss.QianWang,LAST_TIME)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotEnterNoGuild)
		end
	end,

	--36
	[ACTIVITY_TYPE.GUILD_CHUAN_GONG] = function(act_type)
		if RoleWGData.Instance.role_vo.guild_id > 0 then
			local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)
			if not act_cfg then
				return
			end
			
			local ok_func = function ()
				FunOpen.Instance:OpenViewNameByCfg(act_cfg.open_panel_name)
			end

			local content_txt = string.format(Language.Activity.ActOpneTip,act_cfg.name)
			TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt, ok_func, nil, Language.Boss.QianWang, LAST_TIME)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotEnterNoGuild)
		end
	end,

	--3074
	[ACTIVITY_TYPE.KF_ONEVONE] = function(act_type,scene_type)
		if act_type == ACTIVITY_TYPE.KF_ONEVONE and scene_type ~= SceneType.Kf_OneVOne_Prepare then
			local ok_func = function ()
				Field1v1WGCtrl.Instance:EnterFieldPrepareScene(ACTIVITY_TYPE.KF_ONEVONE)
			end
			local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)

			if not act_cfg then
				return
			end

			local content_txt = string.format(Language.Activity.ActOpneTip,act_cfg.name)
			TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt,ok_func,nil,Language.Boss.QianWang,LAST_TIME)
		end
	end,
	--3075
	[ACTIVITY_TYPE.KF_PVP] = function(act_type,scene_type)
		if act_type == ACTIVITY_TYPE.KF_PVP and scene_type ~= SceneType.Kf_PvP_Prepare then
			local ok_func = function ()
				KF3V3WGCtrl.Instance:EnterPrepareScene()
				-- KF3V3WGCtrl.Instance:GoToNpc()
			end
			local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)

			if not act_cfg then
				return
			end

			local content_txt = string.format(Language.Activity.ActOpneTip,act_cfg.name)
			TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt,ok_func,nil,Language.Boss.QianWang,LAST_TIME)
		end
	end,
	--3083
	[ACTIVITY_TYPE.KF_GUILDBATTLE] = function(act_type)
		if RoleWGData.Instance.role_vo.guild_id > 0 then
			local ok_func = function ()
				ActivityWGCtrl.Instance:SendActivityEnterReq(act_type, 0)
			end
			local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)

			if not act_cfg then
				return
			end

			local content_txt = string.format(Language.Activity.ActOpneTip,act_cfg.name)
			TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt,ok_func,nil,Language.Boss.QianWang,LAST_TIME)
		end
	end,
	--1
	[ACTIVITY_TYPE.ZHUXIE] = function(act_type)
		local ok_func = function ()
			ActivityWGData.Instance:OnEnterRoom(act_type)
		end
		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)

		if not act_cfg then
			return
		end

		local content_txt = string.format(Language.Activity.ActOpneTip,act_cfg.name)
		TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt,ok_func,nil,Language.Boss.QianWang,LAST_TIME)
	end,
	--3097
	[ACTIVITY_TYPE.KF_ZHUXIE] = function(act_type)
		local ok_func = function ()
			CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_ZHUXIE)
		end
		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)

		if not act_cfg then
			return
		end

		local content_txt = string.format(Language.Activity.ActOpneTip,act_cfg.name)
		TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt,ok_func,nil,Language.Boss.QianWang,LAST_TIME)
	end,
	--3076
	[ACTIVITY_TYPE.KF_XIANMENGZHAN] = function(act_type)
		if RoleWGData.Instance.role_vo.guild_id > 0 then
			local ok_func = function ()
				local is_guild_war = GuildWGData.Instance:GetMyCanJoinGuildWar()
				if not is_guild_war then
					SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.CanNotWar)
					return
				end

				local my_guild_opponent_guild = GuildWGData.Instance:GetKfMyGuildPpponentGuild()
				if my_guild_opponent_guild and my_guild_opponent_guild.guild_id <= 0 then
					SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.LunKongTip)
					return
				end
				local is_over = GuildWGData.Instance:GetMyGuildWarIsOver()
				if is_over then
					SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.MyGuildWarIsOver)
					return
				end
				GuildWGCtrl.Instance:ReqCrossXianmengzhanOpera(CROSS_XIANMENGZHAN_OPERA_TYPE.CROSS_XIANMENGZHAN_OPERA_TYPE_REQ_ENTER_FB)
			end
			local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)

			if not act_cfg then
				return
			end

			local content_txt = string.format(Language.Activity.ActOpneTip,act_cfg.name)
			TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt,ok_func,nil,Language.Boss.QianWang,LAST_TIME)
		end
	end,
	--33
	[ACTIVITY_TYPE.YEZHANWANGCHENG] = function(act_type)
		local level = GameVoManager.Instance:GetMainRoleVo().level
	    local benfu_act_cfg = DailyWGData.Instance:GetActivityConfig(ACTIVITY_TYPE.YEZHANWANGCHENG)
        if level > benfu_act_cfg.level then
			local ok_func = function ()
    		    KuafuYeZhanWangChengWGCtrl.Instance:SendNightFightEnterReq()
			end
			local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)

			if not act_cfg then
				return
			end

			local content_txt = string.format(Language.Activity.ActOpneTip,act_cfg.name)
			TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt,ok_func,nil,Language.Boss.QianWang,LAST_TIME)
        end
	end,
	--3091
	[ACTIVITY_TYPE.KF_LIEKUN] = function()
		if RoleWGData.Instance.role_vo.guild_id > 0 then
			local ok_func = function ()
				CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_LIEKUN, 0)
			end
			local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.KF_LIEKUN)

			if not act_cfg then
				return
			end

			local content_txt = string.format(Language.Activity.ActOpneTip,act_cfg.name)
			TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt,ok_func,nil,Language.Boss.QianWang,LAST_TIME)
		end
	end,
	--3084
	[ACTIVITY_TYPE.KF_HOTSPRING] = function ()
		local ok_func = function ()
			local act_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_DUCK_RACE)
			if act_data and act_data.status == ACTIVITY_STATUS.OPEN then
				CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_DUCK_RACE)
			else
				CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_HOTSPRING)
			end
		end
		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.KF_HOTSPRING)

		if not act_cfg then
			return
		end

		local content_txt = string.format(Language.Activity.ActOpneTip,act_cfg.name)
		TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt,ok_func,nil,Language.Boss.QianWang,LAST_TIME)
	end,
	--3085
	[ACTIVITY_TYPE.KF_DUCK_RACE] = function ()
		local ok_func = function ()
			CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_DUCK_RACE)
		end
		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.KF_HOTSPRING)

		if not act_cfg then
			return
		end

		local content_txt = string.format(Language.Activity.ActOpneTip,act_cfg.name)
		TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt,ok_func,nil,Language.Boss.QianWang,LAST_TIME)
	end,

	--3087
	[ACTIVITY_TYPE.KUAFUYEZHANWANGCHENG] = function ()
		local ok_func = function ()
			-- CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KUAFUYEZHANWANGCHENG)
			KuafuYeZhanWangChengWGCtrl.Instance:SendNightFightEnterReq()
		end
		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.KUAFUYEZHANWANGCHENG)

		if not act_cfg then
			return
		end

		local content_txt = string.format(Language.Activity.ActOpneTip,act_cfg.name)
		TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt,ok_func,nil,Language.Boss.QianWang,LAST_TIME)
	end,
	--39
	[ACTIVITY_TYPE.ACTIVITY_TYPE_GUILD_INVITE] = function(act_type)
		if RoleWGData.Instance.role_vo.guild_id > 0 then
			local ok_func = function ()
				GuildInviteWGCtrl.Instance:SendCSGuildDingJiEnterScene()
			end
			local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)

			if not act_cfg then
				return
			end

			local content_txt = string.format(Language.Activity.ActOpneTip,act_cfg.name)
			TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt,ok_func,nil,Language.Boss.QianWang,LAST_TIME)
		end
	end,

	--40  永夜之巅
	[ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT] = function(act_type)
		local ok_func = function ()
			EternalNightWGCtrl.Instance:SendCSEternalNightEnter(ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT)
		end
		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)

		if not act_cfg then
			return
		end

		local content_txt = string.format(Language.Activity.ActOpneTip,act_cfg.name)
		TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt,ok_func,nil,Language.Boss.QianWang,LAST_TIME)
	end,


	--3102  永夜之巅 跨服
	[ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT_KF] = function(act_type)
		local ok_func = function ()
			EternalNightWGCtrl.Instance:SendCSEternalNightEnter(ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT_KF)
		end
		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)

		if not act_cfg then
			return
		end

		local content_txt = string.format(Language.Activity.ActOpneTip,act_cfg.name)
		TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt,ok_func,nil,Language.Boss.QianWang,LAST_TIME)
    end,
    
	--37  仙盟神兽
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUILD_BOSS] = function(act_type)
       
        local ok_func = function ()
            GuildBossWGCtrl.SendGuildBossEnterReq()
		end
		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)

		if not act_cfg then
			return
		end

		local content_txt = string.format(Language.Activity.ActOpneTip, act_cfg.name)
		TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt,ok_func,nil,Language.Boss.QianWang,LAST_TIME)
	end,

	-- 封神榜
	[ACTIVITY_TYPE.FengShenBang] = function (act_type)
		ViewManager.Instance:Open(GuideModuleName.FengShenBang)
	end,

	--魔物降临 2241
	[ACTIVITY_TYPE.CLIENT_MOWUJINGLIN] = function ()
		MoWuJiangLinWGCtrl.Instance:GotoShiLian()
	end,

	--任务链 2239
	[ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN] = function ()
		OperationTaskChainWGCtrl.Instance:OperaGo()
	end,

	--天神降临 2216
	[ACTIVITY_TYPE.GOD_JIANGLIN] = function ()
		ActIvityHallWGCtrl.Instance:OpenActivity(ACTIVITY_TYPE.GOD_JIANGLIN)
	end,

	--刑天来袭 2221
	[ACTIVITY_TYPE.RAND_ACT_BEIZHAN_XINGTIANLAIXI] = function ()
		ActIvityHallWGCtrl.Instance:OpenActivity(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_XINGTIANLAIXI)
	end,

	--刑天来袭 2252
	[ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI] = function ()
		ActIvityHallWGCtrl.Instance:OpenActivity(ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI)
	end,

	[ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_ULTIMATE_BATTLE] = function (act_type)
		local ok_func = function ()
			if UltimateBattlefieldWGData.Instance:CheckActIsOpen() then
				CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_ULTIMATE_BATTLE)
			else
				SysMsgWGCtrl.Instance:ErrorRemind(Language.UltimateBattlefield.ActivityNotEnter)
			end
		end
		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)

		if not act_cfg then
			return
		end

		local content_txt = string.format(Language.Activity.ActOpneTip,act_cfg.name)
		TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt,ok_func,nil,Language.Boss.QianWang,LAST_TIME)
	end,

	-- 仙侣PK
	[ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_LOVERPK] = function (act_type)
		local ok_func = function ()
			CrossServerWGCtrl.Instance.SendCrossStartReq(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_LOVERPK)
		end
		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)

		if not act_cfg then
			return
		end

		local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
		if lover_id <= 0 then
			return
		end

		local content_txt = string.format(Language.Activity.ActOpneTip, act_cfg.name)
		TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt, ok_func, nil, Language.Boss.QianWang, LAST_TIME)
	end,
	
	-- boss入侵
	[ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_BOSS_INVASION] = function (act_type)
		local ok_func = function ()
			CrossServerWGCtrl.Instance.SendCrossStartReq(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_BOSS_INVASION)
		end

		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)
		if not act_cfg then
			return
		end

		local status = BOSSInvasionWGData.Instance:GetCurActStatus()
		if status == CROSS_BOSS_STRIKE_STATUS.STATUS_END then
			return
		end

		local content_txt = string.format(Language.Activity.ActOpneTip, act_cfg.name)
		TipWGCtrl.Instance:OpenSecondConfirmationView(content_txt, ok_func, nil, Language.Boss.QianWang, LAST_TIME)
	end,
}
