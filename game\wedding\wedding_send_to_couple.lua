WeddingSendCoupleView = WeddingSendCoupleView or BaseClass(SafeBaseView)
--祝福界面
function WeddingSendCoupleView:__init()
	self:LoadConfig()
	self:SetMaskBg(false)
	self.view_layer = UiLayer.Pop
	self.setingchooselist = {
		status_select = 0,
	}

    self.wedding_id = 0
    self.item_data_event = BindTool.Bind1(self.ItemDataChangeCallback, self)
end

function WeddingSendCoupleView:ReleaseCallBack()
	if self.zhufu_list_view then
		for k,v in pairs(self.zhufu_list_view) do
			v:DeleteMe()
		end
		self.zhufu_list_view = nil
	end

	self.setingchooselist.status_select = 0

	if self.send_window then
  		self.send_window:DeleteMe()
   		self.send_window = nil
    end
    self.role_photo_frame = nil
    self.lover_photo_frame = nil

    if self.cell_list then
		for k,v in pairs(self.cell_list) do
			v:DeleteMe()
		end
		self.cell_list = nil
    end
    if ItemWGData.Instance ~= nil then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
	end
end


function WeddingSendCoupleView:LoadConfig()
	self.view_name = "WeddingSendCoupleView"
    self:AddViewResource(0, "uis/view/wedding_ui_prefab", "layout_send_to_couple")
end

function WeddingSendCoupleView:LoadCallBack()

	self.node_list["img_select_left"]:SetActive(false)
	self.node_list["img_select_right"]:SetActive(false)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
	for i = 0, 2 do
		if i == 0 then
			self.node_list["img_selected_" .. i]:SetActive(true)

		else
			self.node_list["img_selected_" .. i]:SetActive(false)
		end
		XUI.AddClickEventListener(self.node_list["btn_select_bg_" .. i], BindTool.Bind2(self.ClickSelected, self, i))
	end
	-- 关闭面板
	XUI.AddClickEventListener(self.node_list["btn_send"], BindTool.Bind1(self.OnBtnSendHandler, self))

	--self.node_list["img_head"].button:AddClickListener(BindTool.Bind(self.OnClickHeadHandler, self, 1))
	--self.node_list["img_head2"].button:AddClickListener(BindTool.Bind(self.OnClickHeadHandler, self, 2))
	--self.node_list["custom_role_head_1"].button:AddClickListener(BindTool.Bind(self.OnClickHeadHandler, self, 1))
	--self.node_list["custom_role_head_2"].button:AddClickListener(BindTool.Bind(self.OnClickHeadHandler, self, 2))
	self.node_list["left_name_bg"].button:AddClickListener(BindTool.Bind(self.OnClickHeadHandler, self, 1))
	self.node_list["right_name_bg"].button:AddClickListener(BindTool.Bind(self.OnClickHeadHandler, self, 2))
    self:OnClickHeadHandler(1)
    
    self.cell_list = {}
	for i = 1, 3 do
        self.cell_list[i] = SendItemCell.New(self.node_list["cell_"..i], self)
        self.cell_list[i]:SetHideRightDownBgLessNum(-1)
        self.cell_list[i]:SetIndex(i - 1)
	end
end

function WeddingSendCoupleView:ItemDataChangeCallback(item_id, index, reason, put_reason, old_num, new_num)
    self:FlushCellNum()
end

function WeddingSendCoupleView:ShowIndexCallBack(index)
	self:Flush()
end

function WeddingSendCoupleView:FlushCellNum()
    for i = 1, 3 do
        local info = MarryWGData.Instance:GetMarryWeddItemIdBySeq(1, i - 1)
        if info then
            local data = {}
            data.item_id = info.param
            local num = ItemWGData.Instance:GetItemNumInBagById(data.item_id)
            data.num = num or 0
            if self.cell_list and self.cell_list[i] then
                self.cell_list[i]:SetData(data)
                self.cell_list[i]:SetVisible(true)
            end
        else
            if self.cell_list and self.cell_list[i] then
                self.cell_list[i]:SetVisible(false)
            end
        end
    end
end

function WeddingSendCoupleView:OnFlush()
    self:FlushCellNum()
	local marry_info = MarryWGData.Instance:GetCurWeddingInfo()
	if IsEmptyTable(marry_info) then     -- 协议信息可能还没下发
		return
	end

    self:UpdateRoleHead()

	local role_vo = RoleWGData.Instance:GetRoleVo()
	if marry_info.role_id == role_vo.role_id or role_vo.role_id == marry_info.lover_role_id then
		local role_uid = GameVoManager.Instance:GetMainRoleVo().origin_uid
        local index = role_uid == marry_info.role_id and 2 or 1
        self:OnClickHeadHandler(index, true)  --如果是婚宴主人只能强制选中自己对象
	else
        --XUI.SetButtonEnabled(self.node_list["btn_send"], true)
        self.node_list["btn_send"].button.interactable = true
        --self.node_list["btn_send"].image:LoadSprite(ResPath.GetJieHunImg("jh_ann"))
	end

	self:SetDataZhuFu()
end

function WeddingSendCoupleView:UpdateRoleHead()
	local marry_info = MarryWGData.Instance:GetCurWeddingInfo()
	if IsEmptyTable(marry_info) then     -- 协议信息可能还没下发
		return
	end
	if nil == self.lover_photo_frame then
		BrowseWGCtrl.Instance:BrowRoelInfo(marry_info.lover_role_id, BindTool.Bind1(self.GetLoverInfoCallBack, self))
		BrowseWGCtrl.Instance:BrowRoelInfo(marry_info.role_id, BindTool.Bind1(self.GetLoverInfoCallBack, self))
		return
	end

	self:UpdateRoleImage()
end

function WeddingSendCoupleView:GetLoverInfoCallBack( protocol )
	local marry_info = MarryWGData.Instance:GetCurWeddingInfo()
	if marry_info.role_id == protocol.role_id then
		self.role_photo_frame = protocol.appearance.fashion_photoframe
	elseif marry_info.lover_role_id == protocol.role_id then
		self.lover_photo_frame = protocol.appearance.fashion_photoframe
	end
	self:UpdateRoleImage()
end

function WeddingSendCoupleView:UpdateRoleImage()
	local marry_info = MarryWGData.Instance:GetCurWeddingInfo()
	if nil ~= marry_info and not IsEmptyTable(marry_info) then
        if self.node_list and self.node_list["lbl_headname_left"] then --男左女右
            self.node_list["lbl_headname_left"].text.text = marry_info.role_name
            --XUI.UpdateRoleHead(self.node_list["img_head"], self.node_list["custom_role_head_1"],marry_info.role_id,marry_info.role_sex,marry_info.role_prof,false,nil,true)
            self.node_list["lbl_headname_right"].text.text = marry_info.lover_role_name
			--XUI.UpdateRoleHead(self.node_list["img_head2"], self.node_list["custom_role_head_2"],marry_info.lover_role_id,marry_info.lover_role_sex,marry_info.lover_role_prof,false,nil,true)
   --      elseif self.node_list and self.node_list["lbl_headname_left"] then
   --          self.node_list["lbl_headname_right"].text.text = marry_info.role_name
   --          XUI.UpdateRoleHead(self.node_list["img_head2"], self.node_list["custom_role_head_2"],marry_info.role_id,marry_info.role_sex,marry_info.role_prof,false,nil,true)
   --          self.node_list["lbl_headname_left"].text.text = marry_info.lover_role_name
			-- XUI.UpdateRoleHead(self.node_list["img_head"], self.node_list["custom_role_head_1"],marry_info.lover_role_id,marry_info.lover_role_sex,marry_info.lover_role_prof,false,nil,true)
        end
	end
end

function WeddingSendCoupleView:SetDataZhuFu()
	local bundle_name = "uis/view/wedding_ui_prefab"
	local asset_name = "ph_zhufu_item"
	local blessing_info = WeddingWGData.Instance:GetBlessingRecordInfo()
	if nil == self.zhufu_list_view then
		self.zhufu_list_view = {}
	end
	for k,v in pairs(blessing_info) do
		if nil == self.zhufu_list_view[k] then
			self.zhufu_list_view[k] = ZhuFuListItemRender.New()
			self.zhufu_list_view[k]:LoadAsset(bundle_name, asset_name, self.node_list["ph_zhufu_list"].transform)
		end
		self.zhufu_list_view[k]:SetIndex(k)
		self.zhufu_list_view[k]:SetData(v)
	end
end

function WeddingSendCoupleView:ClickSelected(index)
	self.setingchooselist.status_select = index
	self:SetImgHookUnVisible(self.setingchooselist.status_select)
end

function WeddingSendCoupleView:SetImgHookUnVisible(index)
	local unvisible_index = 0
	if index == nil then
		unvisible_index = 0
	else
		unvisible_index = index
	end

	for i = 0, 2 do
		local hook_name = "img_selected_" .. i
		if nil ~= self.node_list[hook_name] then
			self.node_list[hook_name]:SetActive(i == unvisible_index)
		end
	end
end

function WeddingSendCoupleView:OnClickHeadHandler(index, is_force)
    local marry_info = MarryWGData.Instance:GetCurWeddingInfo()
	if IsEmptyTable(marry_info) then     -- 协议信息可能还没下发
		return
	end
    local role_id = RoleWGData.Instance:GetRoleVo().role_id
    if not is_force then
        if marry_info.role_id == role_id or role_id == marry_info.lover_role_id then --不能操作自己
            return
        end
    end
    --if marry_info.role_sex == 1 then --marry_info.role_id 在左边
        if index == 1 then
            self.wedding_id = marry_info.role_id
        else
            self.wedding_id = marry_info.lover_role_id
        end
    --else  --marry_info.role_id 在右边
  --       if index == 1 then
		-- 	self.wedding_id = marry_info.lover_role_id
		-- else
		-- 	self.wedding_id = marry_info.role_id
		-- end
    --end
 	self.node_list["img_select_left"]:SetActive(index == 1)
	self.node_list["img_select_right"]:SetActive(index == 2)
end

function WeddingSendCoupleView:OnBtnSendHandler()
	if self.wedding_id > 0 then
		if self.setingchooselist.status_select > 2 then
			if nil == self.send_window then
				self.send_window = Alert.New(nil, nil, nil, nil, true)
			end
			local blessing_data = WeddingWGData.Instance:GetBlessingDesc(self.setingchooselist.status_select - 3)
			local str = string.format(Language.Wedding.WeddingSend, blessing_data.param)
			self.send_window:SetLableString(str)
			self.send_window:SetOkFunc(BindTool.Bind(self.OnClickSendGold, self))
			self.send_window:Open()
		else
			local blessing_cfg = MarryWGData.Instance:GetMarryWeddItemIdBySeq(1, self.setingchooselist.status_select)			
			local num = ItemWGData.Instance:GetItemNumInBagById(blessing_cfg.param)
			if num > 0 then
				WeddingWGCtrl.Instance:SendCSMarryHunyanOpera(HUNYAN_OPERA_TYPE.HUNYAN_OPERA_TYPE_FOLWER, self.wedding_id, self.setingchooselist.status_select)
			else
				WeddingWGCtrl.Instance:WeddingSetQuickBuyId(blessing_cfg.param, self.wedding_id, blessing_cfg.shop_seq)
			end
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.SendTips)
	end
end

function WeddingSendCoupleView:OnClickSendGold()
	WeddingWGCtrl.Instance:SendCSMarryHunyanOpera(HUNYAN_OPERA_TYPE.HUNYAN_OPERA_TYPE_RED_BAG, self.wedding_id, self.setingchooselist.status_select - 3)
end

function WeddingSendCoupleView:SetListHight(index,hight)
	if index == 1 then
		self.hight_list = {}
		self.node_list["ph_zhufu_list"].enabled = false
	end
	self.hight_list[index] = hight
	if #self.hight_list == #self.zhufu_list_view then
		local all_hight = 0
		for k,v in pairs(self.hight_list) do
			all_hight = all_hight + v
        end
        -- self.node_list.scroll.scroll_rect.enabled = all_hight > 358
		-- self.node_list["ph_zhufu_list"].rect.sizeDelta = Vector2(326,all_hight)
		-- --因为控件的高度完全是有代码计算的，只调整高度触发不了控件的适应，所以设置完之后要做一个显隐触发一下控件的适应
		-- self.node_list["ph_zhufu_list"]:SetActive(false)
		-- self.node_list["ph_zhufu_list"]:SetActive(true)
	end
end


---------------------------------
--ZhuFuListItemRender
ZhuFuListItemRender = ZhuFuListItemRender or BaseClass(BaseRender)
function ZhuFuListItemRender:__init()

end

function ZhuFuListItemRender:__delete()
	self.text_hight = nil
end

function ZhuFuListItemRender:CreateChild()
	BaseRender.CreateChild(self)
end

function ZhuFuListItemRender:OnFlush()
	if not self.data then return end
	local str = ""
	if self.data.bless_type == 0 then
		local blessing_cfg = WeddingWGData.Instance:GetBlessingListCfg(self.data.param)
		str = string.format(Language.Wedding.BlessingTips1, self.data.role_name, self.data.to_role_name, self.data.param)
	elseif self.data.bless_type == 1 then
		local blessing_cfg = WeddingWGData.Instance:GetBlessingListCfg(self.data.param)
		local item_cfg = ItemWGData.Instance:GetItemConfig(blessing_cfg.param)
		str = string.format(Language.Wedding.BlessingTips2, self.data.role_name, self.data.to_role_name, item_cfg.name)
	else
		local item_id = 0
		if self.data.param > 10 then
			item_id = 22101
		else
			item_id = 22100
		end
		local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
		str = string.format(Language.Wedding.BlessingTips3, self.data.role_name, item_cfg.name, self.data.param)
	end
	self.node_list["rich_zhufu_desc"].text.text = str
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["rich_zhufu_desc"].rect)
	local text_hight = self.node_list.rich_zhufu_desc.rect.sizeDelta.y + 4 --间距
	self.node_list["ph_zhufu_item"].rect.sizeDelta = Vector2(480,text_hight)
	WeddingWGCtrl.Instance:SetListHight(self.index, text_hight)
end


SendItemCell = SendItemCell or BaseClass(ItemCell)

function SendItemCell:__init(instance, parent_view)
    self.parent_view = parent_view
    ItemCell.__init(self, instance)
end

function SendItemCell:OnClick()
    if self.index ~= self.parent_view.setingchooselist.status_select then
        self.parent_view:ClickSelected(self.index)
    else
        ItemCell.OnClick(self)
    end
end

function SendItemCell:ReleaseCallBack()
    self.parent_view = nil
end