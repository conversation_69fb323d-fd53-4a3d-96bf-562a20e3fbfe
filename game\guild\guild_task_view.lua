GuildBuildTaskView = GuildBuildTaskView or BaseClass(SafeBaseView)
function GuildBuildTaskView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_guild_task")
end

function GuildBuildTaskView:ShowIndexCallBack()
	self.node_list.title_view_name.text.text = Language.Guild.BuildViewName
	self:SetSecondView(nil,self.node_list["size"])
	self.node_list.btn_task_lq.button:AddClickListener(BindTool.Bind(self.OnClickLqTask, self))
	self.node_list.btn_task_qx.button:AddClickListener(BindTool.Bind(self.OnClickQxTask, self))
	self.node_list.btn_task_qw.button:AddClickListener(BindTool.Bind(self.OnClickQwTask, self))
	self.node_list.btn_task_flush.button:AddClickListener(BindTool.Bind(self.OnClickFlushTask, self))
	self.node_list.btn_guild_task_states.button:AddClickListener(BindTool.Bind(self.OnClickTaskStates, self))

	if nil == self.task_reward_list then
		self.task_reward_list = AsyncListView.New(GuildTaskRewardItem,self.node_list.ph_reward_item_list)
	end
	
	if nil == self.guild_task_item_list then
		self.guild_task_item_list = AsyncBaseGrid.New()
		local bundle = "uis/view/guild_ui_prefab"
		local asset = "ph_guild_task_item"
		self.guild_task_item_list:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list["ph_task_item_list"],
			assetBundle = bundle, assetName = asset, itemRender = GuildTaskItemRender})
		self.guild_task_item_list:SetSelectCallBack(BindTool.Bind1(self.OnClickCItemRenderCalllBack, self))
		self.guild_task_item_list:SetStartZeroIndex(false)
	end
	local task_info = GuildWGData.Instance:GetGuildTaskInfo()
	if task_info and not IsEmptyTable(task_info) then
		for k,v in pairs(task_info) do
			if v.task_states == GameEnum.TASK_STATUS_COMMIT or v.task_states == GameEnum.TASK_STATUS_ACCEPT_PROCESS then
				self.cur_select_index = k
				break
			end
		end
	end
	self.cur_accept_task_id = 0
	-- self.is_lingqu_task = false
	self:OnFlushGuildTaskView()
end

function GuildBuildTaskView:ReleaseCallBack()
	if self.task_reward_list then
		self.task_reward_list:DeleteMe()
		self.task_reward_list = nil
	end

	if self.guild_task_item_list then
		self.guild_task_item_list:DeleteMe()
		self.guild_task_item_list = nil
	end

	if self.qx_task_alert then
		self.qx_task_alert:DeleteMe()
		self.qx_task_alert = nil
	end

	if self.flush_task_alert then
		self.flush_task_alert:DeleteMe()
		self.flush_task_alert = nil
	end

	if self.flush_task_hint_alert then
		self.flush_task_hint_alert:DeleteMe()
		self.flush_task_hint_alert = nil
	end

	if self.flush_task_hint_s then
		self.flush_task_hint_s:DeleteMe()
		self.flush_task_hint_s = nil
	end

	if nil ~= self.flush_task_timer then
		GlobalTimerQuest:CancelQuest(self.flush_task_timer)
		self.flush_task_timer = nil
	end

	if  self.btn_tween then
		self.btn_tween:Kill()
		self.btn_tween = nil
	end
	self.task_data = nil
	self.cur_select_index = nil
	self.is_flush = nil
	self.is_flush_animator = nil
	self.cur_accept_task_id = nil
	-- self.is_lingqu_task = nil

end

function GuildBuildTaskView:SetCurSelectIndex(task_info)
	local task_quality = 0
	local cur_select_index = 6 --默认给一个最大索引

	--优先判断已完成
	for k,v in pairs(task_info) do
		if v.task_states == GameEnum.TASK_STATUS_COMMIT or v.task_states == GameEnum.TASK_STATUS_ACCEPT_PROCESS  then
			self.cur_select_index = k
			break
		end
		if v.task_states ~= GameEnum.TASK_STATUS_FINISH then
			if v.other_info.quality > task_quality or (v.other_info.quality == task_quality and k < cur_select_index) then
				cur_select_index = k
				self.cur_select_index = k
				task_quality = v.other_info.quality
			end
		end
	end
end

function GuildBuildTaskView:OnFlushGuildTaskView(from)
	if nil == self.guild_task_item_list then
		return
	end

	local task_info = GuildWGData.Instance:GetGuildTaskInfo()
	local finish_task_num = GuildWGData.Instance:GetBuildTaskFinishNum()
	local max_task_num = GuildWGData.Instance:GetDayTaskMaxNum()

	if task_info and not IsEmptyTable(task_info) then
		self.guild_task_item_list:SetDataList(task_info,0)

		-- if from and from == "from_guild_protocol" and self.is_lingqu_task and finish_task_num < max_task_num then
		-- 	self:SetCurSelectIndex(task_info, true)
		-- 	self.is_lingqu_task = false
		-- end

		self:SetCurSelectIndex(task_info)
		self.cur_select_index = self.cur_select_index or 1
		local cell = self.guild_task_item_list:GetCell(self.cur_select_index)
		if cell then
			self.guild_task_item_list:SelectCellHandler(cell)
		else
			self.guild_task_item_list:SetSelectCellIndex(self.cur_select_index)
			self.task_data = task_info[self.cur_select_index]
			local reward_item = GuildWGData.Instance:GetGuildBuildTaskReward(self.task_data.old_info.task_id)
			self.task_reward_list:SetDataList(reward_item)
			self.node_list.img_reward_icon:SetActive(self.task_data.other_info.quality == GUILD_BUILD_TASK_RATE_TYPE.RATES)
		end
		
	end

	local states = GuildWGData.Instance:GetGuildBuildTaskStates(self.cur_accept_task_id)

	if  self.task_data and self.cur_accept_task_id == self.task_data.old_info.task_id and states == GameEnum.TASK_STATUS_ACCEPT_PROCESS then
		self.cur_accept_task_id = 0
		self:OnClickQwTask()
	end

	self:FlushTaskInfo()
end

function GuildBuildTaskView:OnClickLqTask()
	if nil == self.task_data then
		return
	end 

	if self.task_data.task_states == GameEnum.TASK_STATUS_CAN_ACCEPT then --可接但未接
		--1853 接受任务
		local finish_task_num = GuildWGData.Instance:GetBuildTaskFinishNum()
		local max_task_num = GuildWGData.Instance:GetDayTaskMaxNum()
		local shengyu_num = max_task_num - finish_task_num

		if shengyu_num <= 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.TaskFinishHint)
			return
		end

		GuildWGData.Instance:NeedAutoTaskFlag(self.task_data.old_info.task_id)
		self.cur_accept_task_id = self.task_data.old_info.task_id
		TaskWGCtrl.Instance:SendTaskAccept(self.task_data.old_info.task_id)
	elseif self.task_data.task_states == GameEnum.TASK_STATUS_COMMIT then --已完成待提交
		--1854 提交任务
		-- self.is_lingqu_task = true
		local guild_build_accept = TaskWGData.Instance:GetTaskGuildBuildCfg()
		-- TaskWGCtrl.Instance:OperateFollowTask(guild_build_accept)
		-- if nil ~= self.task_data.old_info.commit_npc then
		-- 	self:Close()
		-- end
		TaskWGCtrl.Instance:SendTaskCommit(self.task_data.old_info.task_id)
	end
end

function GuildBuildTaskView:OnClickQxTask()
	if nil == self.qx_task_alert then
		self.qx_task_alert = Alert.New()
		self.qx_task_alert:SetShowCheckBox(true)
	end
	self.qx_task_alert:Open()
	self.qx_task_alert:SetLableString(Language.Guild.QxTaskHint)
	self.qx_task_alert:SetOkFunc(function ()
		GuildWGCtrl.Instance:SendCancelGuildBuildTask(self.task_data.old_info.task_id)
		self:FlushBtnDeal(false)
	end)
end

function GuildBuildTaskView:OnClickQwTask()
	local guild_build_accept = TaskWGData.Instance:GetTaskGuildBuildCfg() 
	local task_id = 0
	local condition = 0

	if guild_build_accept and guild_build_accept.task_id then
		task_id = guild_build_accept.task_id
	end

	if self.task_data and self.task_data.old_info and self.task_data.old_info.condition then
		condition = self.task_data.old_info.condition
	end

	local states = GuildWGData.Instance:GetGuildBuildTaskStates(task_id)
	if self.task_data and self.task_data.old_info.task_type == GameEnum.TASK_TYPE_GUILD_BUILD 
		and states == GameEnum.TASK_STATUS_ACCEPT_PROCESS and condition == GameEnum.TASK_COMPLETE_CONDITION_6 then
		--已接任务(特殊)
		TaskWGCtrl.Instance:OperateFollowTask(guild_build_accept)
	end

	if self.task_data then
		--已接去任务
		if states == GameEnum.TASK_STATUS_ACCEPT_PROCESS and condition ~= GameEnum.TASK_COMPLETE_CONDITION_6 then
			TaskWGCtrl.Instance:OperateOnProcessStataus(guild_build_accept)--操作正在进行中状态的任务
		end
	end
	ViewManager.Instance:CloseAll()
end

function GuildBuildTaskView:OnClickFlushTask()
	if self.is_flush and not GuildWGData.Instance:GetBuildTaskIsAllFinish() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.FlushQuicklyHint)
		return
	end
	
	--暂时不需要

	-- if GuildWGData.Instance:GetBuildTaskIsAllFinish() then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.TaskFinishHint)
	-- 	return
	-- end

	local flush_need_gold = GuildWGData.Instance:GetFlushTaskNeedGold()
	if flush_need_gold > RoleWGData.Instance:GetMoney(MoneyType.XianYu) then
		GuildWGCtrl.Instance:SendFlushGuildBuildTask()
		return
	end

	--当前有s级的任务时候需要弹窗做提示
	local is_have_s = GuildWGData.Instance:IsHaveSTask()
	local is_have_s_check =false
	if nil == self.flush_task_hint_s then
		self.flush_task_hint_s = Alert.New()
		self.flush_task_hint_s:SetShowCheckBox(true, "guild_task_s")
	end

	if self.flush_task_hint_s then
		is_have_s_check = self.flush_task_hint_s:GetIsNeedOpenViewFlag()
	end

	if is_have_s and not is_have_s_check then
		self.flush_task_hint_s:SetLableString(Language.Guild.FlushGuildTaskHintS)
		self.flush_task_hint_s:SetOkFunc(BindTool.Bind1(self.SendFlushTask, self))
		self.flush_task_hint_s:Open()
		return
	end

	local flush_num = GuildWGData.Instance:GetCanFlushTaskNum()
	if flush_num > 0 then
		if nil == self.flush_task_hint_alert then
			self.flush_task_hint_alert = Alert.New()
			self.flush_task_hint_alert:SetShowCheckBox(false)
		end

		local accept_task = TaskWGData.Instance:GetTaskGuildBuildCfg()
		if accept_task then
			self.flush_task_hint_alert:SetLableString(Language.Guild.CancelBuildTaskHint)
			self.flush_task_hint_alert:SetOkFunc(BindTool.Bind1(self.SendFlushTask, self))
			self.flush_task_hint_alert:Open()
		else
			self:SendFlushTask()
		end
		return
	end
	
	if nil == self.flush_task_alert then
		self.flush_task_alert = Alert.New()
		self.flush_task_alert:SetShowCheckBox(true, "guild_task")
	end

	local xiaohao_hint = string.format(Language.Guild.FlushTaskXiaoHaoHint,flush_need_gold)
	self.flush_task_alert:SetLableString(xiaohao_hint)
	self.flush_task_alert:SetOkFunc(BindTool.Bind1(self.SendFlushTask, self))
	self.flush_task_alert:Open()
end

function GuildBuildTaskView:SendFlushTask()
	GuildWGCtrl.Instance:SendFlushGuildBuildTask()
	self:FlushBtnDeal(true)
end

function GuildBuildTaskView:FlushBtnDeal(enalbe)
	self.is_flush_animator = true
	if self.is_flush_animator then
		self:FlushTaskItemAnimator()
	end
	self.is_flush = enalbe
	-- self:FlushTaskBtnAnimator()
	XUI.SetGraphicGrey(self.node_list["btn_task_flush"], self.is_flush)
	-- self.node_list.effect:SetActive(not self.is_flush)
	if nil ~= self.flush_task_timer then
		GlobalTimerQuest:CancelQuest(self.flush_task_timer)
		self.flush_task_timer = nil
	end
	self.flush_task_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind1(self.FlushTaskList,self), 0.7)--延时处理
end

function GuildBuildTaskView:FlushTaskList()
	self.is_flush = false
	-- if nil ~= self.cur_rotation and self.cur_rotation == -360 then
	-- 	self.node_list["btn_task_flush"].transform.localRotation = Quaternion.identity
	-- end
	local task_info = GuildWGData.Instance:GetGuildTaskInfo()
	if task_info and not IsEmptyTable(task_info) then
		self.guild_task_item_list:SetDataList(task_info,0)
	end
	XUI.SetGraphicGrey(self.node_list["btn_task_flush"], self.is_flush)
	-- self.node_list.effect:SetActive(not self.is_flush)
end

function GuildBuildTaskView:FlushTaskItemAnimator()
	local all_gride_item = self.guild_task_item_list:GetAllCell()
	for k,v in pairs(all_gride_item) do
		v:GuildTaskItemAnimator()
	end
	self.is_flush_animator = false
end

function GuildBuildTaskView:OnClickTaskStates()
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetTitle(Language.Guild.GuildBuildTaskTips)
		role_tip:SetContent(Language.Guild.GuildBuildTaskStateTips)
	end
end

function GuildBuildTaskView:OnClickCItemRenderCalllBack(item)
	if nil == item or nil == item:GetData() then
		return
	end
	self.task_data = item:GetData()
	self.cur_select_index = item.index
	self.node_list.text_task_content.text.text = self.task_data.other_info.task_content
	self.node_list.text_1.text.text = self.task_data.old_info.task_name
	
	local reward_item = GuildWGData.Instance:GetGuildBuildTaskReward(self.task_data.old_info.task_id)
	self.task_reward_list:SetDataList(reward_item,0)
	self.node_list.img_reward_icon:SetActive(self.task_data.other_info.quality == GUILD_BUILD_TASK_RATE_TYPE.RATES)
	self:FlushTaskInfo()
end

function GuildBuildTaskView:FlushTaskInfo()
	if nil == self.task_data then
		return
	end
	--获取任务状态
	if self.task_data.task_states == GameEnum.TASK_STATUS_CAN_ACCEPT then --可接但未接
		self.node_list.text_lq.text.text = Language.Guild.LqTaskBtnName
		self.node_list.btn_task_lq:SetActive(true)
		self.node_list.btn_parent:SetActive(false)

	elseif self.task_data.task_states == GameEnum.TASK_STATUS_ACCEPT_PROCESS then --未完成进行中
		self.node_list.btn_task_lq:SetActive(false)
		self.node_list.btn_parent:SetActive(true)

	elseif self.task_data.task_states == GameEnum.TASK_STATUS_COMMIT then --已完成待提交
		self.node_list.text_lq.text.text = Language.Guild.LqRewardBtnName
		self.node_list.btn_task_lq:SetActive(true)
		self.node_list.btn_parent:SetActive(false)
	elseif self.task_data.task_states == GameEnum.TASK_STATUS_FINISH then --已完成已提交
		self.node_list.btn_task_lq:SetActive(false)
		self.node_list.btn_parent:SetActive(false)
	end
	local finish_task_num = GuildWGData.Instance:GetBuildTaskFinishNum()
	local max_task_num = GuildWGData.Instance:GetDayTaskMaxNum()
	local shengyu_num = max_task_num - finish_task_num
	local color = shengyu_num > 0 and COLOR3B.GREEN or COLOR3B.RED
	self.node_list.text_task_num.text.text = ToColorStr(shengyu_num.."/"..max_task_num,color) 
	local desc = ""
	if self.task_data.old_info.c_param2 > 0 then
		desc = string.format("（%s/%s）",self.task_data.progress_num,self.task_data.old_info.c_param2)
	 	desc = ToColorStr(desc, COLOR3B.GREEN)
	end
	self.node_list.text_task_process.text.text = string.gsub(self.task_data.old_info.accept_desc,"'#.-'","'#453550'")..desc
	self.node_list.img_ylq:SetActive(self.task_data.task_states == GameEnum.TASK_STATUS_FINISH)
	local free_num = GuildWGData.Instance:GetCanFlushTaskNum()
	self.node_list.img_flush:SetActive(free_num <= 0)
	if free_num > 0 then
        self.node_list.btn_txt_flush.text.text = Language.Guild.FlushFree
        self.node_list.text_flush.text.text = Language.Guild.FlushTaskHint
		self.node_list.text_flush_num.text.text = free_num
	else
		local flush_need_gold = GuildWGData.Instance:GetFlushTaskNeedGold()
        self.node_list.btn_txt_flush.text.text = Language.Guild.FlushNotFree
        self.node_list.text_flush.text.text = Language.Guild.FlushTaskNeedHint
		self.node_list.text_flush_num.text.text = flush_need_gold	
	end
	-- XUI.SetGraphicGrey(self.node_list["btn_task_flush"], finish_task_num == max_task_num)
	XUI.SetGraphicGrey(self.node_list["btn_task_lq"], finish_task_num == max_task_num)
	self.node_list.img_red:SetActive(self.task_data.task_states == GameEnum.TASK_STATUS_COMMIT)
	--local btn_icon_name = self.task_data.task_states == GameEnum.TASK_STATUS_COMMIT and "btn_big_common_02" or "btn_big_common_01"
	--self.node_list["btn_task_lq"].image:LoadSprite(ResPath.GetCommonButtonToggle_atlas(btn_icon_name))
end
------------------------------------------------GuildTaskItemRender---------------------------------------------------------------------------
GuildTaskItemRender = GuildTaskItemRender or BaseClass(BaseGridRender)
function GuildTaskItemRender:__init()
end

function GuildTaskItemRender:__delete()
	if  self.btn_tween then
		self.btn_tween:Kill()
		self.btn_tween = nil
	end
end

function GuildTaskItemRender:LoadCallBack()
end

function GuildTaskItemRender:OnFlush()
	if nil == self.data then 
		return 
	end
	--评级
	local asset_name,bundle_name = ResPath.GetGuildSystemImage("guild_task_rate_"..self.data.other_info.quality)
	self.node_list.img_rate.image:LoadSprite(asset_name,bundle_name,function ()
		self.node_list.img_rate.image:SetNativeSize()
	end)
		--评级底
	local asset_name1,bundle_name1 = ResPath.GetF2RawImagesPNG("guild_task_rate_bg_"..self.data.other_info.quality)
	self.node_list.img_rate_bg.raw_image:LoadSprite(asset_name1,bundle_name1,function ()
		self.node_list.img_rate_bg.raw_image:SetNativeSize()
	end)
    --任务状态
    local states_show = self.data.task_states == GameEnum.TASK_STATUS_ACCEPT_PROCESS or self.data.task_states == GameEnum.TASK_STATUS_COMMIT
	local task_name = self.data.task_states == GameEnum.TASK_STATUS_COMMIT and Language.Guild.Can_Get or Language.Guild.Doing
	self.node_list.states_txt.text.text = task_name
	self.node_list.img_task_states:SetActive(states_show)
    
	--任务图标
	local asset_name2,bundle_name2 = ResPath.GetF2RawImagesPNG(self.data.other_info.img_icon_name)
	self.node_list.img_task_icon.raw_image:LoadSprite(asset_name2,bundle_name2,function ()
		self.node_list.img_task_icon.raw_image:SetNativeSize()
	end)
	self.node_list.task_name.text.text = ToColorStr(self.data.old_info.task_name,ITEM_COLOR[self.data.other_info.quality])
	self.node_list["img_ywc"]:SetActive(self.data.task_states == GameEnum.TASK_STATUS_FINISH)
end

function GuildTaskItemRender:GuildTaskItemAnimator()
	-- if self.data.other_info.quality == GUILD_BUILD_TASK_RATE_TYPE.RATES then
	-- 	return
	-- end
	if  self.btn_tween then
		self.btn_tween:Kill()
		self.btn_tween = nil
	end

	local btn_tween = function (trans)
		local sequence = DG.Tweening.DOTween.Sequence()
		-- self.node_list.item_render2.canvas_group.alpha = 0
		sequence:Append(trans:DOScale(Vector3(1, 1, 1),0):SetEase(DG.Tweening.Ease.Linear))
		sequence:Append(trans:DOScale(Vector3(0.02, 1, 1),0.3):SetEase(DG.Tweening.Ease.Linear))
		sequence:Append(trans:DOScale(Vector3(1, 1, 1),0.3):SetEase(DG.Tweening.Ease.Linear))
		-- sequence:Append(self.node_list.item_render2.canvas_group:DoAlpha(0.3, 1, 0.3):SetEase(DG.Tweening.Ease.OutCubic))
		-- sequence:AppendInterval(0)
		return sequence
	end
	self.btn_tween = btn_tween(self.node_list.item_render.transform):SetLoops(1)
end

function GuildTaskItemRender:OnSelectChange(is_on)
	if nil ~= self.node_list.select_hight then
		self.node_list.select_hight:SetActive(is_on)
	end
end


-------------------------------------------GuildTaskRewardItem-------------------------------------------------------
GuildTaskRewardItem = GuildTaskRewardItem or BaseClass(BaseRender)
function GuildTaskRewardItem:__init()

end

function GuildTaskRewardItem:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function GuildTaskRewardItem:OnFlush()
	if nil == self.data then 
		return 
	end
	if nil == self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.cell_item)
	end
	self.item_cell:SetData(self.data)

end