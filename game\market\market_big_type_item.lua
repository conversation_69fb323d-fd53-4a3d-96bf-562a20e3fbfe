
---------------------市场大类型item------------------
MarketBigTypeItem = MarketBigTypeItem or BaseClass(BaseRender)
function MarketBigTypeItem:__init()
end

function MarketBigTypeItem:__delete()
end

function MarketBigTypeItem:OnFlush()
	if self.data then
		self.node_list["text"].text.text = self.data.big_name
        self.node_list["texth"].text.text = self.data.big_name
	end
end

function MarketBigTypeItem:ShowHL(show_hl)
    self.node_list["texth"]:SetActive(show_hl)
    self.node_list["text"]:SetActive(not show_hl)
	self.node_list["hl"]:SetActive(show_hl)
end

-- 选择状态改变
function MarketBigTypeItem:OnSelectChange(is_select)
	self:ShowHL(is_select)
end