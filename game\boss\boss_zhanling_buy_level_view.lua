-- 购买战令等级界面
BossZhanLingBuyLevelView = BossZhanLingBuyLevelView or BaseClass(SafeBaseView)

function BossZhanLingBuyLevelView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_boss_zhanling_buy_level_view")
end

function BossZhanLingBuyLevelView:LoadCallBack()
    if not self.zhanling_level_list then
        self.zhanling_level_list = AsyncListView.New(BossZhanLingLevelCellItemRender, self.node_list.zhanling_level_list)
    end
end

function BossZhanLingBuyLevelView:ReleaseCallBack()
    if self.zhanling_level_list then
        self.zhanling_level_list:DeleteMe()
        self.zhanling_level_list = nil
    end
end

function BossZhanLingBuyLevelView:OnFlush()
    local score = BossZhanLingWGData.Instance:GetScore()
    local max_level_cfg = BossZhanLingWGData.Instance:GetZhanLingMaxLevelCfg()

    if IsEmptyTable(max_level_cfg) then
        return
    end

    self.node_list.cur_score.text.text = string.format(Language.BossZhanLing.BuyZhanLingLevelCurExp, score)
    local target_exp = max_level_cfg.need_score - score
    target_exp = target_exp > 0 and target_exp or 0
    self.node_list.need_score.text.text = string.format(Language.BossZhanLing.BuyZhanLingLevelToMax, target_exp)

    local level_list = BossZhanLingWGData.Instance:GetZhanLingBuyLevelCfg()
	self.zhanling_level_list:SetDataList(level_list)
end

---------------------------------------BossZhanLingLevelCellItemRender--------------------------------------------------
BossZhanLingLevelCellItemRender = BossZhanLingLevelCellItemRender or BaseClass(BaseRender)
function BossZhanLingLevelCellItemRender:__init()
    XUI.AddClickEventListener(self.node_list["btn_buy"], BindTool.Bind(self.OnClickBuyLevelBtn, self))
    XUI.AddClickEventListener(self.node_list["cell_node_btn"], BindTool.Bind(self.OnClickBuyLevelBtn, self))
end

function BossZhanLingLevelCellItemRender:ReleaseCallBack()
	if self.alert then
		self.alert:DeleteMe()
		self.alert = nil
	end
end

function BossZhanLingLevelCellItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.add_level_str.text.text = string.format(Language.BossZhanLing.ZhanLingBuyLevel, self.data.score)
    local price = RoleWGData.GetPayMoneyStr(self.data.price, self.data.rmb_type, self.data.rmb_seq)
    self.node_list.now_price_text.text.text = string.format(Language.BossZhanLing.ZhanLingBuyLevelBtn, price)

    local score = BossZhanLingWGData.Instance:GetScore()
    local target_level = BossZhanLingWGData.Instance:GetZhanLingLevelByDevote(score + self.data.score)
    local cur_zhanling_level = BossZhanLingWGData.Instance:GetZhanLingLevel()
    local can_up_level = target_level - cur_zhanling_level
    self.node_list.buy_desc_1.text.text = can_up_level > 0 and string.format(Language.BossZhanLing.BuyZhanLingLevelCanUpLevel, can_up_level) or ""

    local return_lingyu = self.data.return_lingyu
    self.node_list.buy_desc_2.text.text = return_lingyu > 0 and string.format(Language.BossZhanLing.BuyZhanLingLevelBackLingYu, return_lingyu) or ""
end

function BossZhanLingLevelCellItemRender:OnClickBuyLevelBtn()
    if IsEmptyTable(self.data) then
        return
    end

    local zhanling_exp = BossZhanLingWGData.Instance:GetScore()
	local max_level_cfg = BossZhanLingWGData.Instance:GetZhanLingMaxLevelCfg()

	if IsEmptyTable(max_level_cfg) then
		return
	end

	local target_exp = max_level_cfg.need_score - zhanling_exp
	target_exp = target_exp > 0 and target_exp or 0

	if self.data.score > target_exp then
		if not self.alert then
			self.alert = Alert.New()
		end

		self.alert:ClearCheckHook()
		self.alert:SetShowCheckBox(true, "lm_zhanling_level_cell")
		self.alert:SetCheckBoxDefaultSelect(false)

		local func = function()
			RechargeWGCtrl.Instance:Recharge(self.data.price, self.data.rmb_type, self.data.rmb_seq)
		end

		local str = string.format(Language.BossZhanLing.BuyZhanLingLevelYiChu, target_exp)

		self.alert:SetLableString(str)
		self.alert:SetOkFunc(func)
		self.alert:Open()
	else
        RechargeWGCtrl.Instance:Recharge(self.data.price, self.data.rmb_type, self.data.rmb_seq)
    end
end