DailyCoinFbSceneLogic = DailyCoinFbSceneLogic or BaseClass(CommonFbLogic)

function DailyCoinFbSceneLogic:__init()
	
end

function DailyCoinFbSceneLogic:__delete()
	
end

function DailyCoinFbSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	
	DailyWGCtrl.Instance:Close()

	FuBenWGCtrl.Instance:OpenTaskFollow()
	
	-- XuiBaseView.CloseAllView()
	-- UiInstanceMgr.Instance:OpenRewardAction(ResPath.GetScene("coin_reward"))
end

function DailyCoinFbSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
end

function DailyCoinFbSceneLogic:Out()
	CommonFbLogic.Out(self)
	FuBenWGCtrl.Instance:CloseTaskFollow()
	-- UiInstanceMgr.Instance:CloseRewardAction()
end