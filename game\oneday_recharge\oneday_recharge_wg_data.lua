OneDayRechargeWGData = OneDayRechargeWGData or BaseClass()

function OneDayRechargeWGData:__init()
	if OneDayRechargeWGData.Instance then 
		ErrorLog("[OneDayRechargeWGData] Attemp to create a singleton twice !")
	end

	OneDayRechargeWGData.Instance = self

    local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_daily_recharge_auto")
    self.reward_cfg = ListToMapList(cfg.reward, "grade")
    self.reward_seq_cfg = ListToMap(cfg.reward, "grade", "seq")
	self.other_cfg = ListToMap(cfg.open_day, "grade")

	self.reward_flag = {}
	RemindManager.Instance:Register(RemindName.OneDayRecharge, BindTool.Bind(self.GetOneDayRechargeRed, self)) --DIY1
end


function OneDayRechargeWGData:__delete()
	OneDayRechargeWGData.Instance = nil

	RemindManager.Instance:UnRegister(RemindName.OneDayRecharge)
end


function OneDayRechargeWGData:SetAllInfo(protocol)
	self.grade = protocol.grade
	self.chongzhi_gold = protocol.chongzhi_gold
	self.reward_flag = protocol.reward_flag
end

function OneDayRechargeWGData:GetCurGrade()
	return self.grade or 1
end

function OneDayRechargeWGData:GetChongzhiGold()
	return self.chongzhi_gold or 0
end

function OneDayRechargeWGData:GetRewardCfg()
	local grade = self:GetCurGrade()
	return self.reward_cfg[grade] or {}
end

function OneDayRechargeWGData:GetGradeTabList()
	local cfg = self:GetRewardCfg()
	local data_list = {}
	if cfg == nil then
		return data_list
	end

	for k, v in pairs(cfg) do
		data_list[v.seq] = v
	end

	return data_list
end

function OneDayRechargeWGData:GetRewardSeqCfg(seq)
	local grade = self:GetCurGrade()
	return ((self.reward_seq_cfg[grade] or {})[seq] or {})
end

function OneDayRechargeWGData:GetShowModelCfg()
	local grade = self:GetCurGrade()
	return ((self.other_cfg[grade] or {}).display_model or 0)
end

function OneDayRechargeWGData:GetRewardGetSeq(seq)
	return (self.reward_flag or 0)[seq] or 0
end

function OneDayRechargeWGData:GetRewardIsGet(seq)
	local reward_seq = self:GetRewardGetSeq(seq)
	return reward_seq > 0
end

--单个红点
function OneDayRechargeWGData:GetRewardSeqRed(seq)
	local reward_cfg = self:GetRewardSeqCfg(seq)
	local chongzhi_gold = self:GetChongzhiGold()
	local is_get = self:GetRewardIsGet(seq)
 
	if reward_cfg == nil or reward_cfg.need_chongzhi == nil then
		return false
	end

	if chongzhi_gold >= reward_cfg.need_chongzhi and (not is_get) then
		return true
	end

	return false
end

--主界面红点
function OneDayRechargeWGData:GetOneDayRechargeRed()
	local cfg = self:GetRewardCfg()
	local chongzhi_gold = self:GetChongzhiGold()
	for k, v in pairs(cfg) do
		if self:GetRewardSeqRed(v.seq) then
			return 1
		end
	end

	return 0
end

--跳转索引
function OneDayRechargeWGData:GetJumpRewardSeq()
	local jump_seq = 0
	local cfg = self:GetGradeTabList()
	for k, v in pairs(cfg) do
		if self:GetRewardSeqRed(v.seq) then
			return k
		end

		if jump_seq == nil then
			jump_seq = k
		end
	end

	return jump_seq
end
