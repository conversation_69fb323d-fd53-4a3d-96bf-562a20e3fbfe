CustomizedSuitView = CustomizedSuitView or BaseClass(SafeBaseView)

function CustomizedSuitView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:SetMaskBg(false)

	-- self:AddViewResource(0, "uis/view/customized_suit_ui_prefab", "layout_customized_suit_bg")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/customized_suit_ui_prefab", "layout_customized_suit")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
	

	self.suit_list = nil          --套装部件列表
	self.cur_suit_data = nil      --当前选择套装数据
	self.jump_suit_seq = nil      --跳转到这个套装
	self.jump_suit_part = nil     --跳转到这个套装某个部件
	self.select_list_index = -1   --当前套装列表选中id
	self.select_suit_seq = -1     --当前套装id
	self.select_suit_part = -1    --当前套装的部件id
	self.select_suit_part_index = -1 --当前套装部件列表选中id
	self.is_show_attr_panel = nil --是否展示了属性

	self.attr_item_list = {}      --激活属性列表
	self.owner_attr_item_list = nil --自定义属性列表
end

function CustomizedSuitView:__delete()

end

function CustomizedSuitView:OpenCallBack()
	CustomizedSuitWGData.Instance:RefreshAllThemeRedbySuit() ---刷新一下红点
end

function CustomizedSuitView:CloseCallBack()
	self.select_list_index = -1   --当前套装列表选中id
	self.select_suit_seq = -1     --当前套装id
	self.select_suit_part = -1    --当前套装的部件id
	self.select_suit_part_index = -1 --当前套装部件列表选中id
	self.is_show_attr_panel = nil
	self.jump_suit_seq = nil      --跳转到这个套装
	self.jump_suit_part = nil     --跳转到这个套装某个部件
end

function CustomizedSuitView:LoadCallBack()

	local bundle, asset = ResPath.GetRawImagesPNG("a3_ystz_bg1")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

	if not self.suit_list then
		self.suit_list = AsyncListView.New(CustomizedSuitCell, self.node_list.suit_list)
		self.suit_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectSuitItemCB, self))
	end

	if not self.part_list then
		self.part_list = AsyncListView.New(CustomizedSuitPartRender, self.node_list.part_list)
		self.part_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectSuitPartCB, self))
		self.part_list:SetStartZeroIndex(true)
		self.part_list.default_select_index = -1
		self.part_list.cur_select_index = -1
	end

	if self.owner_attr_item_list == nil then
		self.owner_attr_item_list = {}
		for i = 1, 4 do
			local skill_obj = self.node_list.layout_skill_attr:FindObj(string.format("attr_%d", i))
			if skill_obj then
				local cell = CustomizedSkillAttrRender.New(skill_obj)
				cell:SetIndex(i)
				self.owner_attr_item_list[i] = cell
			end
		end
	end

	--加载模型时装
	if nil == self.user_model then
		self.user_model = CommonUserModelRender.New(self.node_list["ph_display"])
		self.user_model:AddUiRoleModel(self)
	end

	XUI.AddClickEventListener(self.node_list["skill_yulan_btn"], BindTool.Bind(self.SkillYuLanBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_preview_skill"], BindTool.Bind(self.SkillYuLanBtn, self))
	XUI.AddClickEventListener(self.node_list["god_act_btn"], BindTool.Bind(self.OnClickGoAct, self))
	XUI.AddClickEventListener(self.node_list["active_suit_attr_btn"], BindTool.Bind(self.OnClickWearAll, self)) ---激活套装属性
	XUI.AddClickEventListener(self.node_list["info_skill"], BindTool.Bind(self.OnClickSkill, self))
	XUI.AddClickEventListener(self.node_list["reset_attr_btn"], BindTool.Bind(self.OnBindSkillAttr, self))
	XUI.AddClickEventListener(self.node_list["customized_star_up_btn"], BindTool.Bind(self.OnClickStarUp, self))     ---套装升星
	-- 暂时屏蔽
	-- XUI.AddClickEventListener(self.node_list["customized_colorful_btn"], BindTool.Bind(self.OnClickPartColorful, self)) ---套装部件炫彩
end

function CustomizedSuitView:FulshShowBigBg()
	local suit_data = CustomizedSuitWGData.Instance:GetCustomizedSuitInfoBySuit(self.select_suit_seq)
	if suit_data then
		local bundle, asset = ResPath.GetRawImagesPNG("a3_ystz_bg"..suit_data.badground)
		if self.node_list.RawImage_tongyong then
			self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
				self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
			end)
		end
	end
end

function CustomizedSuitView:ReleaseCallBack()
	self.jump_suit_seq = nil --跳转到这个套装
	self.jump_suit_part = nil --跳转到这个套装某个部件
	self.select_list_index = nil
	self.select_suit_seq = nil
	self.cur_suit_data = nil --当前选择套装数据
	self.select_suit_part = nil
	self.select_suit_part_index = nil

	if self.suit_list then
		self.suit_list:DeleteMe()
		self.suit_list = nil
	end

	if self.part_list then
		self.part_list:DeleteMe()
		self.part_list = nil
	end

	if self.user_model then
		self.user_model:DeleteMe()
		self.user_model = nil
	end

	if self.lingchong_model then
		self.lingchong_model:DeleteMe()
		self.lingchong_model = nil
	end

	if self.xianwa_model then
		self.xianwa_model:DeleteMe()
		self.xianwa_model = nil
	end

	if self.mount_model then
		self.mount_model:DeleteMe()
		self.mount_model = nil
	end

	if self.attr_item_list then
		for k, v in pairs(self.attr_item_list) do
			v.cell:DeleteMe()
		end
		self.attr_item_list = nil
	end

	if self.owner_attr_item_list then
		for k, v in pairs(self.owner_attr_item_list) do
			v:DeleteMe()
		end
		self.owner_attr_item_list = nil
	end
end

function CustomizedSuitView:ShowIndexCallBack(index)
	self:DoSuitCellsAnim()

	UITween.MoveToShowPanel(GuideModuleName.CustomizedSuitView, self.node_list.part_bg, Vector2(-36, -86),
	Vector2(-36, 74, 0), 0.4, DG.Tweening.Ease.Linear)
	UITween.MoveToShowPanel(GuideModuleName.CustomizedSuitView, self.node_list.img_suit_root,Vector2(100, 102), 
	Vector2(-139, 102, 0), 0.4, DG.Tweening.Ease.Linear)
end

---套装部件点击
function CustomizedSuitView:OnSelectSuitPartCB(part_item)
	if nil == part_item or nil == part_item.data then
		return
	end

	--拿到当前选中的格子下标
	local cell_index = part_item:GetIndex()
	if self.select_suit_part_index == cell_index then
		part_item:OnShowItemClick()
		return
	end

	self.select_suit_part = part_item.data.part
	self.select_suit_part_index = cell_index
	---这里需要执行一下当前模型展示方式
	if not self.is_show_attr_panel then
		self:ShowAttrPanel(true)
		self.is_show_attr_panel = true
		self:FlushSuitModel(true)
		self:FlushThemeRes()
	end

	self:RefreshOwnerAttrList()
end

-- 炫彩更新
function CustomizedSuitView:OnSelectXuanCaiUpdate()
	self:FlushSuitModel(true)
end

---套装列表点击
function CustomizedSuitView:OnSelectSuitItemCB(item)
	if nil == item or nil == item.data then
		return
	end

	--拿到当前选中的格子下标
	local cell_index = item:GetIndex()
	if self.select_list_index == cell_index then
		return
	end

	self.cur_suit_data = item.data
	self.select_list_index = cell_index
	self.select_suit_seq = self.cur_suit_data.suit

	self.select_suit_part = nil
	self.select_suit_part_index = nil
	self.part_list.default_select_index = -1
	self.part_list.cur_select_index = -1
	self.is_show_attr_panel = nil
	self:ShowAttrPanel()
	local callback_func = function()
		self:FlushSuitModel()
		self:FlushLowerPanel()
		self:FlushAttrList()
		self:FlushSkill()
		self:FlushThemeRes()
		self:FulshShowBigBg()
	end

	-- 播放CG
	local cg_bundle, cg_asset = self.cur_suit_data.cg_bundle, self.cur_suit_data.cg_asset
	if cg_bundle and cg_asset and (cg_bundle ~= "" and cg_asset ~= "") then
		CgManager.Instance:Play(UICg.New(cg_bundle, cg_asset), callback_func)
		return
	end

	callback_func()
end

function CustomizedSuitView:OnFlush(param_t, index)
	local to_suit = nil
	for k_1, v_1 in pairs(param_t or {}) do
		if k_1 == "jump_suit" then
			to_suit = v_1.jump_suit
			break
		end
	end
	---刷新列表
	self:FlushSuitList(to_suit)
	self:FlushSkill()
	self:RefreshOwnerAttrList()
	self:FlushThemeRes()
end

function CustomizedSuitView:FlushThemeRes()
	if self.is_show_attr_panel then
		self.node_list.img_suit_name:CustomSetActive(false)
		self.node_list.img_suit_wb:CustomSetActive(false)
		self.node_list.btn_preview_skill:CustomSetActive(false)
	else
		local suit_data = CustomizedSuitWGData.Instance:GetCustomizedSuitInfoBySuit(self.select_suit_seq)
		if IsEmptyTable(suit_data) then
			return
		end

		local str = string.format("a3_suit_name_%s", suit_data.theme_res_id)
		local bundle, asset = ResPath.GetF2RawImagesPNG(str)
		self.node_list.img_suit_name.raw_image:LoadSprite(bundle, asset, function()
			self.node_list.img_suit_name.raw_image:SetNativeSize()
		end)
	
		local str2 = string.format("a3_suit_wb_%s", suit_data.theme_res_id)
		local bundle2, asset2 = ResPath.GetF2RawImagesPNG(str2)
		self.node_list.img_suit_wb.raw_image:LoadSprite(bundle2, asset2, function()
			self.node_list.img_suit_wb.raw_image:SetNativeSize()
		end)
	
		-- 技能预览
		self.node_list.img_preview_skill_icon.image:LoadSprite(ResPath.GetSkillIconById(suit_data.skill_preview_icon))

		self.node_list.img_suit_name:CustomSetActive(true)
		self.node_list.img_suit_wb:CustomSetActive(true)
		self.node_list.btn_preview_skill:CustomSetActive(true)
	end

end

function CustomizedSuitView:FlushSuitList(to_suit)
	local show_list = CustomizedSuitWGData.Instance:GetSuitShowList()

	if self.jump_suit_seq == nil then
		self.jump_suit_seq = 0

		for index, temp_data in ipairs(show_list) do
			local suit_skill_red = CustomizedSuitWGData.Instance:GetOneThemeRed(temp_data.suit, false)
			local suit_data = WardrobeWGData.Instance:GetSuitAllListBySuit(temp_data.suit)
			local suit_red = suit_data and suit_data.can_act and temp_data.state ~= REWARD_STATE_TYPE.UNDONE

			if to_suit then
				if to_suit == temp_data.suit then
					self.jump_suit_seq = index
					break
				end
			else
				if suit_skill_red or suit_red then
					self.jump_suit_seq = index
					break
				end
			end
		end
	end

	self.node_list.suit_list:SetActive(show_list and #show_list > 0)

	if self.suit_list ~= nil then
		self.suit_list:SetDataList(show_list)
		if self.jump_suit_seq then
			if self.jump_suit_seq == 0 then
				self.suit_list:JumpToIndex(1, 5)
				self.jump_suit_seq = 100
			elseif self.jump_suit_seq ~= 100 then
				self.suit_list:JumpToIndex(self.jump_suit_seq, 5)
				self.jump_suit_seq = 100
			end
		end
	end

	self:FlushLowerPanel()
	self:FlushAttrList()
end

function CustomizedSuitView:FlushLowerPanel()
	if not self.select_suit_seq or self.select_suit_seq < 0 then
		return
	end

	local suit_data = CustomizedSuitWGData.Instance:GetCustomizedSuitInfoBySuit(self.select_suit_seq)
	if IsEmptyTable(suit_data) then
		return
	end

	self.part_list:SetDataList(suit_data.part_list)

	if self.jump_suit_part == nil then
		self.jump_suit_part = -1

		for index, temp_data in pairs(suit_data.part_list) do
			local part_data = CustomizedSuitWGData.Instance:GetThemeAttrBySuitPart(self.select_suit_seq, temp_data.part)
			if part_data and part_data.red then
				self.jump_suit_part = index
				break
			end
		end
	end

	if self.jump_suit_part then
		if self.jump_suit_part ~= 100 then
			self.part_list:JumpToIndex(self.jump_suit_part, 2)
			self.jump_suit_part = 100
		end
	end

	self:FlushCapStr(suit_data.part_list)
	self.node_list.suit_red:SetActive(suit_data.can_act)
	self.node_list.suit_attr_red:SetActive(suit_data.can_act)

	local suit_data = WardrobeWGData.Instance:GetSuitAllListBySuit(self.select_suit_seq)
	if IsEmptyTable(suit_data) then
		return
	end

	local str = suit_data.total_part_num == suit_data.act_part_num and Language.Wardrobe.WearbtnText[1] or
	Language.Wardrobe.WearbtnText[2]
	self.node_list.suit_attr_btn_text.text.text = str
end

function CustomizedSuitView:FlushCapStr(info)
	local capability = 0
	for k, v in pairs(info) do
		local item_cfg = ItemWGData.Instance:GetItemConfig(v.show_item_id)
		if item_cfg then
			capability = capability + ItemShowWGData.CalculateCapability(v.show_item_id)
		end

		-- 定制属性
		local part_data = CustomizedSuitWGData.Instance:GetThemeAttrBySuitPart(self.select_suit_seq, v.part)
		local part_attr_list = CustomizedSuitWGData.Instance:GetCurrPartAttrList(self.select_suit_seq, v.part, part_data)
		if part_attr_list then
			local cap = CustomizedSuitWGData.Instance:GetCustomizedAttrCap(part_attr_list)
			capability = capability + cap
		end
	end

	-- 技能属性
	local cur_data = CustomizedSuitWGData.Instance:GetThemeSkillBySuit(self.select_suit_seq)
	if not cur_data then
		return
	end

	local cur_star_attr_data, next_star_attr_data = CustomizedSuitWGData.Instance:GetCustomizedSuitSatrAttr(
	self.select_suit_seq, cur_data.star)
	local skill_attr_cap = CustomizedSuitWGData.Instance:GetCustomizedAttrCap(cur_star_attr_data)
	capability = capability + skill_attr_cap

	-- 技能战力
	local cur_skill_data = CustomizedSuitWGData.Instance:GetSelectSkillBySkillId(cur_data.skill_id, cur_data.skill_level)
	local skill_cap = cur_skill_data and cur_skill_data.cap_value or 0
	capability = capability + skill_cap

	self.node_list.cap_value.text.text = capability
end

function CustomizedSuitView:FlushSkill()
	local skill_data = GuiXuDreamWGData.Instance:GetSkillBySeq(self.select_suit_seq)
	self.node_list.info_skill:SetActive(not IsEmptyTable(skill_data))
	if IsEmptyTable(skill_data) then
		return
	end

	--是否可以定制
	local is_red, theme_skill_data = CustomizedSuitWGData.Instance:GetOneThemeSelectSkillRed(self.select_suit_seq)
	self.node_list.skill_lock:SetActive(false)
	self.node_list.img_skill_add:SetActive(theme_skill_data and theme_skill_data.skill_id == 0)
	self.node_list.img_skill_icon:SetActive(theme_skill_data and theme_skill_data.skill_id ~= 0)
	self.node_list.skill_red:SetActive(is_red)
	if theme_skill_data then
		if theme_skill_data.skill_id ~= 0 then
			local skill_data = CustomizedSuitWGData.Instance:GetSelectSkillBySkillId(theme_skill_data.skill_id,
				theme_skill_data.skill_level)
			if skill_data then
				local bundle, asset = ResPath.GetSkillIconById(skill_data.skill_icon)
				self.node_list.img_skill_icon.image:LoadSprite(bundle, asset, function()
					self.node_list.img_skill_icon.image:SetNativeSize()
				end)
				self.node_list.skill_name.text.text = skill_data.skill_name
				self.node_list.skill_level.text.text = string.format(Language.Common.LevelNormal, theme_skill_data.skill_level) 
			end
		else
			self.node_list.skill_name.text.text = Language.Customized.SuitSkillStr
			self.node_list.skill_level.text.text = Language.Customized.SuitSkillStr2
		end
	end

	self.node_list.customized_star_up_red:SetActive(CustomizedSuitWGData.Instance:GetOneThemeStarUpRed(self
	.select_suit_seq))
end

function CustomizedSuitView:OnClickSkill()
	--打开技能定制
	CustomizedSuitWGCtrl.Instance:OpenCustomizedSkillPanel(self.select_suit_seq)
end

function CustomizedSuitView:OnClickGoAct()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		ViewManager.Instance:Open(GuideModuleName.GodPchaseView)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HmGosView.ActEnd)
	end
end

---展示属性面板
function CustomizedSuitView:ShowAttrPanel(is_follow)
	if is_follow then
		UITween.MoveToShowPanel(GuideModuleName.CustomizedSuitView, self.node_list.customized_attr, Vector2(400, -42),
			Vector2(-17, -42, 0), 0.4, DG.Tweening.Ease.Linear)
		UITween.MoveToShowPanel(GuideModuleName.CustomizedSuitView, self.node_list.customized_operate_btn_root,
			Vector2(234, -460), Vector2(234, -307, 0), 0.4, DG.Tweening.Ease.Linear)		
	else
		RectTransform.SetAnchoredPositionXY(self.node_list.customized_attr.rect, 400, -42)
		RectTransform.SetAnchoredPositionXY(self.node_list.customized_operate_btn_root.rect, 234, -460)
	end
end

--数据属性列表
function CustomizedSuitView:FlushAttrList()
	if not self.attr_item_list then
		self.attr_item_list = {}
	end
	local attr_data = WardrobeWGData.Instance:GetAttrBySuit(self.select_suit_seq)

	for i, v in ipairs(attr_data) do
		if self.attr_item_list[i] then
			if self.attr_item_list[i].loaded_flag then
				self.attr_item_list[i].cell:SetData(v)
			end
		else
			local async_loader = AllocAsyncLoader(self, "customized_attr" .. i)
			self.attr_item_list[i] = {}
			self.attr_item_list[i].loaded_flag = false
			async_loader:SetParent(self.node_list.attr_list.transform)
			async_loader:Load("uis/view/customized_suit_ui_prefab", "customized_suit_attr_cell", function(obj)
				local cell = GuiXuDreamAttrRender.New(obj)
				cell:SetIndex(i)
				cell:SetData(v)
				self.attr_item_list[i].cell = cell
				self.attr_item_list[i].loaded_flag = true
			end)
		end
	end

	local active_num = #attr_data
	for i, v in ipairs(self.attr_item_list) do
		if v.loaded_flag then
			v.cell:SetActive(i <= active_num)
		end
	end

	-- 暂时屏蔽
	-- self.node_list["customized_colorful_btn"]:CustomSetActive(WardrobeWGData.Instance:GetHaveXuanCaiBySuit(self
	-- .select_suit_seq))
end

---刷新定制属性
function CustomizedSuitView:RefreshOwnerAttrList()
	local part_data = CustomizedSuitWGData.Instance:GetThemeAttrBySuitPart(self.select_suit_seq, self.select_suit_part)
	self.node_list.reset_attr_btn:SetActive(true)

	if not part_data then
		return
	end

	self.node_list.no_skill_attr:SetActive(part_data.attr2_id == nil or part_data.attr2_id == 0)
	self.node_list.attr_scroll:SetActive(part_data.attr2_id and part_data.attr2_id ~= 0)
	local str = (part_data.attr2_id and part_data.attr2_id ~= 0) and Language.Customized.reset_attr_lable or
	Language.Customized.special_attr_lable
	self.node_list.reset_attr_text.text.text = str
	self.node_list.reset_attr_red:SetActive(part_data and part_data.red)

	if part_data then
		local part_attr_list = CustomizedSuitWGData.Instance:GetCurrPartAttrList(self.select_suit_seq,
			self.select_suit_part, part_data)
		if self.owner_attr_item_list and part_attr_list then
			for index, owner_attr_cell in ipairs(self.owner_attr_item_list) do
				owner_attr_cell:SetData(part_attr_list[index])
			end
		end
	end
end

---重置或打开属性选择界面
function CustomizedSuitView:OnBindSkillAttr()
	local part_data = CustomizedSuitWGData.Instance:GetThemeAttrBySuitPart(self.select_suit_seq, self.select_suit_part)
	if part_data then
		if part_data.attr2_id == nil or part_data.attr2_id == 0 then
			CustomizedSuitWGCtrl.Instance:OpenCustomizedSkillAttrPanel(self.select_suit_seq, self.select_suit_part)
		else
			CustomizedSuitWGCtrl.Instance:SendSkillTypeAttrReset(self.select_suit_seq, self.select_suit_part, 0)
		end
	else
		CustomizedSuitWGCtrl.Instance:OpenCustomizedSkillAttrPanel(self.select_suit_seq, self.select_suit_part)
	end
end

---套装升星
function CustomizedSuitView:OnClickStarUp()
	local cur_data = CustomizedSuitWGData.Instance:GetThemeSkillBySuit(self.select_suit_seq)
	if cur_data == nil or cur_data.skill_id == 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Customized.CanNotOpenStarUp)
		return
	end

	CustomizedSuitWGCtrl.Instance:OpenStarUpPanel(self.select_suit_seq)
end

---套装炫彩
function CustomizedSuitView:OnClickPartColorful()
	CustomizedSuitWGCtrl.Instance:OpenPartColorfulPanel(self.select_suit_seq, self.select_suit_part)
end

-- 一键穿戴
function CustomizedSuitView:OnClickWearAll()
	if self.select_suit_seq == nil then
		return
	end

	local suit_data = WardrobeWGData.Instance:GetSuitAllListBySuit(self.select_suit_seq)
	if IsEmptyTable(suit_data) then
		return
	end
	local part_list = suit_data.part_list

	if suit_data.total_part_num == suit_data.act_part_num then --所有已激活
		for k, v in pairs(part_list) do
			if v.state == REWARD_STATE_TYPE.FINISH then
				self:SendReqByData(v)
			end
		end
		TipWGCtrl.Instance:ShowSystemMsg(Language.GuiXuDream.WearTips)
	end

	if suit_data.can_act then --判断激活下一套装属性
		local need_num = suit_data.suit_less_need - suit_data.act_part_num
		for k, v in pairs(part_list) do
			if v.state == REWARD_STATE_TYPE.CAN_FETCH then
				WardrobeWGCtrl.Instance:SendWardrobeRequest(WARDROBE_OP_TYPE.WARDROBE_OPERATE_TYPE_ACTIVE_PART,
					self.select_suit_seq, v.part)
				need_num = need_num - 1
				if need_num == 0 then
					break
				end
			end
		end
	end
end

-- 请求穿戴
function CustomizedSuitView:SendReqByData(data)
	if IsEmptyTable(data) then
		return
	end

	local is_same_res = false
	local fashion_cfg, res_id, index_id
	if data.type == WARDROBE_PART_TYPE.FASHION then -- 时装大类
		fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
		if fashion_cfg then
			res_id = fashion_cfg.resouce
			index_id = fashion_cfg.index

			-- 检测是否存在炫彩
			local new_res = CustomizedSuitWGData.Instance:GetXuanCaiAppimageId(self.select_suit_seq, data.part, res_id)
			if new_res ~= res_id then
				fashion_cfg = NewAppearanceColorfulWGData.Instance:GetShiZhuangExtendCfg3(new_res, data.param1, index_id)

				if fashion_cfg then
					res_id = fashion_cfg.resouce
					index_id = fashion_cfg.index
				end
			end

			is_same_res = WardrobeWGData.Instance:CheckIsSameRes(data.type, data.param1, res_id)
			if not is_same_res then
				if data.param1 == SHIZHUANG_TYPE.WING then -- 羽翼
					NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_IMAGE,
						ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_WING, res_id, index_id)
				elseif data.param1 == SHIZHUANG_TYPE.FABAO then -- 法宝
					NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_IMAGE,
						ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_FABAO, res_id, index_id)
				elseif data.param1 == SHIZHUANG_TYPE.JIANZHEN then -- 剑阵
					NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_IMAGE,
						ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_JIANZHEN, res_id, index_id)
				elseif data.param1 == SHIZHUANG_TYPE.SHENBING then -- 武器
					NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_IMAGE,
						ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_SHENBING, res_id, index_id)
				else
					NewAppearanceWGCtrl.Instance:OnUseFashion(data.param1, index_id, 1)
				end
			end
		end
	elseif data.type == WARDROBE_PART_TYPE.MOUNT then -- 坐骑
		fashion_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(data.param1)
		if fashion_cfg then
			res_id = fashion_cfg.appe_image_id
			index_id = fashion_cfg.image_id

			-- 检测是否存在炫彩
			local new_res = CustomizedSuitWGData.Instance:GetXuanCaiAppimageId(self.select_suit_seq, data.part, res_id)
			if new_res ~= res_id then
				fashion_cfg = NewAppearanceColorfulWGData.Instance:GetMountExtendCfg2(new_res)

				if fashion_cfg then
					res_id = fashion_cfg.appe_image_id
					index_id = 0
				end
			end

			is_same_res = WardrobeWGData.Instance:CheckIsSameRes(data.type, nil, res_id)
			if not is_same_res then
				NewAppearanceWGCtrl.Instance:SendMountReq(MOUNT_OPERA_TYPE.USE_IMAGE, res_id, index_id)
			end
		end
	elseif data.type == WARDROBE_PART_TYPE.LING_CHONG then -- 灵宠
		fashion_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(data.param1)
		if fashion_cfg then
			res_id = fashion_cfg.appe_image_id
			index_id = fashion_cfg.image_id

			-- 检测是否存在炫彩
			local new_res = CustomizedSuitWGData.Instance:GetXuanCaiAppimageId(self.select_suit_seq, data.part, res_id)
			is_same_res = WardrobeWGData.Instance:CheckIsSameRes(data.type, nil, new_res)
			if not is_same_res then
				NewAppearanceWGCtrl.Instance:SendLingChongReq(MOUNT_OPERA_TYPE.USE_IMAGE, new_res)
			end
		end
	elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then -- 化鲲
		fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
		if fashion_cfg then
			-- 检测是否存在炫彩
			res_id = fashion_cfg.active_id
			index_id = fashion_cfg.id
			local new_res = CustomizedSuitWGData.Instance:GetXuanCaiAppimageId(self.select_suit_seq, data.part, res_id)
			local have_xuancai = new_res ~= res_id

			is_same_res = WardrobeWGData.Instance:CheckIsSameRes(data.type, nil, res_id)
			if not is_same_res then
				if have_xuancai then
					NewAppearanceWGCtrl.Instance:SendKunOperaReq(KUN_OPERA_TYPE.USE_KUN, new_res)
				else
					NewAppearanceWGCtrl.Instance:SendKunOperaReq(KUN_OPERA_TYPE.USE_KUN, index_id)
				end
			end
		end
	end
end

-- 激活成功特效
function CustomizedSuitView:DoActiveEffect(suit, part)
	if self.suit_seq == nil then
		return
	end

	local suit_data = WardrobeWGData.Instance:GetSuitAllListBySuit(suit)

	if suit_data and suit_data.suit_less_need == 0 or suit_data.suit_less_need > suit_data.act_part_num then
		TipWGCtrl.Instance:ShowEffect({
			effect_type = UIEffectName.s_jihuo,
			is_success = true,
			pos = Vector2(0, 0),
			parent_node = self.node_list.effect_root
		})
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
	end
end

-- 刷新模型
function CustomizedSuitView:FlushSuitModel(is_show_all)
	if not self.user_model then
		return
	end

	local show_list = WardrobeWGData.Instance:GetActivationPartList(self.select_suit_seq)
	if IsEmptyTable(show_list) then
		return
	end

	local user_model_data = {}
	local mount_model_data = {}
	local lc_model_data = {}

	self.node_list["lc_root"]:SetActive(false)
	self.node_list["xw_root"]:SetActive(false)
	self.node_list["mount_root"]:SetActive(false)

	user_model_data.body_res_id = AppearanceWGData.Instance:GetRoleResId()
	local fashion_cfg

	for k, data in pairs(show_list) do
		if data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.BODY then -- 时装大类
			fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
			if fashion_cfg then                                                          -- 时装
				local prof = GameVoManager.Instance:GetMainRoleVo().prof
				local new_resource_id = self:ReSetFashionId(data.part, fashion_cfg.resouce)
				user_model_data.body_res_id = ResPath.GetFashionModelId(prof, new_resource_id)
			end
		end

		if is_show_all then
			if data.type == WARDROBE_PART_TYPE.MOUNT then
				fashion_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByImageId(
				MOUNT_LINGCHONG_APPE_TYPE.MOUNT, data.param1)
				if fashion_cfg then
					user_model_data.mount_res_id = fashion_cfg.appe_image_id
					user_model_data.mount_res_id = CustomizedSuitWGData.Instance:GetXuanCaiAppimageId(
					self.select_suit_seq, data.part, user_model_data.mount_res_id)
					user_model_data.mount_action = MOUNT_RIDING_TYPE[1]
					local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(user_model_data.mount_res_id)
					if not IsEmptyTable(action_cfg) then
						user_model_data.mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
					end
				end
			elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then -- 化鲲
				fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
				if fashion_cfg then
					user_model_data.mount_res_id = fashion_cfg.active_id
					user_model_data.mount_res_id = CustomizedSuitWGData.Instance:GetXuanCaiAppimageId(
					self.select_suit_seq, data.part, user_model_data.mount_res_id)
					user_model_data.mount_action = MOUNT_RIDING_TYPE[1]
					local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(user_model_data.mount_res_id)
					if not IsEmptyTable(action_cfg) then
						user_model_data.mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
					end
				end
			end
		end
	end

	for k, v in pairs(show_list) do
		self:ShowModelByData(v, is_show_all, user_model_data)
	end

	user_model_data = self:ChangeModelShowScale(is_show_all, user_model_data)
	self.user_model:SetData(user_model_data)
end

function CustomizedSuitView:ShowModelByData(data, is_show_all, user_model_data)
	if IsEmptyTable(data) then
		return
	end

	local fashion_cfg = nil
	if data.type == WARDROBE_PART_TYPE.FASHION then -- 时装大类
		WardrobeWGData.Instance:AssembleRoleModelDataByTypeIndex(data.param1, data.param2, user_model_data)
		self:SetXuanCaiRecordId(data.part, data.param1, user_model_data)
	elseif data.type == WARDROBE_PART_TYPE.LING_CHONG and not is_show_all then -- 灵宠
		fashion_cfg = NewAppearanceWGData.Instance:GetLingChongActCfgByImageId(data.param1)
		if fashion_cfg then
			local appe_image_id = CustomizedSuitWGData.Instance:GetXuanCaiAppimageId(self.select_suit_seq, data.part,
				fashion_cfg.appe_image_id)
			appe_image_id = CustomizedSuitWGData.Instance:GetXuanCaiAppimageId(self.select_suit_seq, data.part,
				appe_image_id)
			self:SetLingChongModelData(WARDROBE_PART_TYPE.LING_CHONG, appe_image_id)
		end
	elseif data.type == WARDROBE_PART_TYPE.XIAN_WA and not is_show_all then -- 仙娃
		fashion_cfg = MarryWGData.Instance:GetActiveCfgByTypeAndId(data.param1, data.param2)
		if fashion_cfg then
			local appe_image_id = CustomizedSuitWGData.Instance:GetXuanCaiAppimageId(self.select_suit_seq, data.part,
				fashion_cfg.appe_image_id)
			appe_image_id = CustomizedSuitWGData.Instance:GetXuanCaiAppimageId(self.select_suit_seq, data.part,
				appe_image_id)
			self:SetXianWaModelData(appe_image_id)
		end
	elseif data.type == WARDROBE_PART_TYPE.MOUNT and not is_show_all then -- 坐骑
		fashion_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(data.param1)
		if fashion_cfg then
			local appe_image_id = CustomizedSuitWGData.Instance:GetXuanCaiAppimageId(self.select_suit_seq, data.part,
				fashion_cfg.appe_image_id)
			appe_image_id = CustomizedSuitWGData.Instance:GetXuanCaiAppimageId(self.select_suit_seq, data.part,
				appe_image_id)
			self:SetMountModelData(appe_image_id)
		end
	elseif data.type == WARDROBE_PART_TYPE.HUA_KUN and not is_show_all then -- 化鲲
		fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
		if fashion_cfg then
			local appe_image_id = CustomizedSuitWGData.Instance:GetXuanCaiAppimageId(self.select_suit_seq, data.part,
				fashion_cfg.active_id)
			appe_image_id = CustomizedSuitWGData.Instance:GetXuanCaiAppimageId(self.select_suit_seq, data.part,
				appe_image_id)
			self:SetMountModelData(appe_image_id)
		end
	end
end

-- 设置当前保存的特殊炫彩形象
function CustomizedSuitView:SetXuanCaiRecordId(suit_part, part_type, export_data)
	if part_type == SHIZHUANG_TYPE.MASK then      -- 脸饰
		export_data.mask_id = self:ReSetFashionId(suit_part, export_data.mask_id)
	elseif part_type == SHIZHUANG_TYPE.BELT then  -- 腰饰
		export_data.belt_id = self:ReSetFashionId(suit_part, export_data.belt_id)
	elseif part_type == SHIZHUANG_TYPE.WEIBA then -- 尾巴
		export_data.tail_id = self:ReSetFashionId(suit_part, export_data.tail_id)
	elseif part_type == SHIZHUANG_TYPE.SHOUHUAN then -- 手环
		export_data.shou_huan_id = self:ReSetFashionId(suit_part, export_data.shou_huan_id)
	elseif part_type == SHIZHUANG_TYPE.HALO then  -- 光环
		export_data.halo_id = self:ReSetFashionId(suit_part, export_data.halo_id)
	elseif part_type == SHIZHUANG_TYPE.WING then  -- 羽翼
		export_data.wing_id = self:ReSetFashionId(suit_part, export_data.wing_id)
	elseif part_type == SHIZHUANG_TYPE.FABAO then -- 法宝
		export_data.fabao_id = self:ReSetFashionId(suit_part, export_data.fabao_id)
	elseif part_type == SHIZHUANG_TYPE.JIANZHEN then -- 剑阵
		export_data.jianzhen_id = self:ReSetFashionId(suit_part, export_data.jianzhen_id)
	elseif part_type == SHIZHUANG_TYPE.SHENBING then -- 武器
		export_data.weapon_id = self:ReSetFashionId(suit_part, export_data.weapon_id)
	elseif part_type == SHIZHUANG_TYPE.FOOT then  -- 足迹
		export_data.foot_effect_id = self:ReSetFashionId(suit_part, export_data.foot_effect_id)
	end
end

-- 重新设置时装形象id
function CustomizedSuitView:ReSetFashionId(suit_part, old_id)
	local new_id = CustomizedSuitWGData.Instance:GetXuanCaiAppimageId(self.select_suit_seq, suit_part, old_id)
	return new_id
end

-- 获取星级对应的其他特殊形象
function CustomizedSuitView:GetStartLevelSpecialAppeImageId(image_id, qichong_select_type)
	local wardrobe_image_id = image_id

	local info = NewAppearanceWGData.Instance:GetSpecialQiChongInfo(qichong_select_type, image_id)
	if info then
		local star_level = info.star_level or info.star
		local part_type = WARDROBE_PART_TYPE.LING_CHONG
		if self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
			part_type = WARDROBE_PART_TYPE.MOUNT
		elseif self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
			part_type = WARDROBE_PART_TYPE.LING_CHONG
		elseif self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
			part_type = WARDROBE_PART_TYPE.HUA_KUN
		end

		wardrobe_image_id = WardrobeWGData.Instance:TryGetSpecialShowItemId(WARD_ROBE_GET_NEW_TYPE.NEW_SHOW_ID,
			star_level, part_type,
			image_id, 0, image_id)
	end

	return wardrobe_image_id
end

function CustomizedSuitView:SetLingChongModelData(type, res_id)
	self.node_list["lc_root"]:SetActive(true)
	if nil == self.lingchong_model then
		self.lingchong_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["lc_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.S,
			can_drag = false,
		}

		self.lingchong_model:SetRenderTexUI3DModel(display_data)
		-- self.lingchong_model:SetUI3DModel(self.node_list["lc_display"].transform,
		-- 	nil, 1, true, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.lingchong_model)
	else
		if self.lingchong_model then
			self.lingchong_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetPetModel(res_id)

	self.lingchong_model:SetMainAsset(bundle, asset, function()
		self.lingchong_model:PlaySoulAction()
	end)

	self.lingchong_model:FixToOrthographic(self.root_node_transform)
end

function CustomizedSuitView:SetXianWaModelData(res_id)
	self.node_list["xw_root"]:SetActive(true)
	if nil == self.xianwa_model then
		self.xianwa_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["xw_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.S,
			can_drag = false,
		}

		self.xianwa_model:SetRenderTexUI3DModel(display_data)
		-- self.xianwa_model:SetUI3DModel(self.node_list["xw_display"].transform,
		-- 	nil, 1, true, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.xianwa_model)
	else
		if self.xianwa_model then
			self.xianwa_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetHaiZiModel(res_id)
	self.xianwa_model:SetMainAsset(bundle, asset, function()
		self.xianwa_model:PlaySoulAction()
	end)

	self.xianwa_model:FixToOrthographic(self.root_node_transform)
end

function CustomizedSuitView:SetMountModelData(res_id)
	self.node_list["mount_root"]:SetActive(true)
	if nil == self.mount_model then
		self.mount_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["mount_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = false,
		}

		self.mount_model:SetRenderTexUI3DModel(display_data)
		-- self.mount_model:SetUI3DModel(self.node_list["mount_display"].transform,
		-- 	nil, 1, true, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.mount_model)
	else
		if self.mount_model then
			self.mount_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetMountModel(res_id)

	self.mount_model:SetMainAsset(bundle, asset, function()
		self.mount_model:PlayMountAction()
	end)

	self.mount_model:FixToOrthographic(self.root_node_transform)
end

function CustomizedSuitView:ChangeModelShowScale(is_show_all,user_model_data)
	local data = WardrobeWGData.Instance:GetThemeCfgBySuit(self.select_suit_seq)
	if IsEmptyTable(data) then
		return user_model_data
	end

	local display_pos_str = data.main_whole_display_pos
	local pos_str = data.main_pos
	local rotate_str = data.main_rot


	if is_show_all then
		---这里设置角色的所有展示（切换到角色展示的类型）
		local display_pos2 = data.main_whole_display_pos2
		local pos_str2 = data.main_pos2
		local rotate_str2 = data.main_rot2
		if display_pos2 and display_pos2 ~= "" then
			local pos = Split(display_pos2, "|")
			RectTransform.SetAnchoredPosition3DXYZ(self.node_list.ph_display.rect, tonumber(pos[1]) or 0, tonumber(pos[2]) or 0, tonumber(pos[3]) or 0)
		end

		if pos_str2 and pos_str2 ~= "" then
			local pos = Split(pos_str2, "|")
			user_model_data.model_adjust_root_local_position = Vector3(tonumber(pos[1]) or 0, tonumber(pos[2]) or 0, tonumber(pos[3]) or 0)
		end

		if rotate_str2 and rotate_str2 ~= "" then
			local rot = Split(rotate_str2, "|")
			user_model_data.role_rotation = u3dpool.vec3(tonumber(rot[1]) or 0, tonumber(rot[2]) or 0, tonumber(rot[3]) or 0)
		end

		local scale2 = data.main_scale2
		if scale2 and scale2 ~= "" then
			user_model_data.model_adjust_root_local_scale = scale2
		end
		user_model_data.model_rt_type = ModelRTSCaleType.L
		return user_model_data
	end

	-- 角色模型
	user_model_data.model_rt_type = ModelRTSCaleType.L
	if display_pos_str and display_pos_str ~= "" then
		local pos = Split(display_pos_str, "|")
		RectTransform.SetAnchoredPosition3DXYZ(self.node_list.ph_display.rect, tonumber(pos[1]) or 0, tonumber(pos[2]) or 0, tonumber(pos[3]) or 0)
	end

	if pos_str and pos_str ~= "" then
		local pos = Split(pos_str, "|")
		user_model_data.model_adjust_root_local_position = Vector3(tonumber(pos[1]) or 0, tonumber(pos[2]) or 0, tonumber(pos[3]) or 0)
	end

	if rotate_str and rotate_str ~= "" then
		local rot = Split(rotate_str, "|")
		user_model_data.model_rot = Vector3(tonumber(rot[1]) or 0, tonumber(rot[2]) or 0, tonumber(rot[3]) or 0)
	end

	local scale = data.main_scale
	if scale and scale ~= "" then
		user_model_data.model_adjust_root_local_scale = scale
		-- RectTransform.SetLocalScale(self.node_list.ph_display.rect, scale)
	end


	--灵宠
	if self.node_list["lc_root"]:GetActive() then
		display_pos_str = data.pet_whole_display_pos
		if display_pos_str and display_pos_str ~= "" then
			local pos = Split(display_pos_str, "|")
			RectTransform.SetAnchoredPosition3DXYZ(self.node_list.lc_display.rect, tonumber(pos[1]) or 0, tonumber(pos[2]) or 0, tonumber(pos[3]) or 0)
		end

		pos_str = data.pet_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			self.lingchong_model:SetRTAdjustmentRootLocalPosition(tonumber(pos[1]) or 0, tonumber(pos[2]) or 0, tonumber(pos[3]) or 0)
		end

		rotate_str = data.pet_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.lingchong_model then
				self.lingchong_model:SetRTAdjustmentRootLocalRotation(tonumber(rot[1]) or 0, tonumber(rot[2]) or 0, tonumber(rot[3]) or 0)
			end
		end

		scale = data.lc_scale
		if scale and scale ~= "" then
			self.lingchong_model:SetRTAdjustmentRootLocalScale(scale)
		end
		
	end

	--仙娃
	if self.node_list["xw_root"]:GetActive() then
		display_pos_str = data.xw_whole_display_pos
		if display_pos_str and display_pos_str ~= "" then
			local pos = Split(display_pos_str, "|")
			RectTransform.SetAnchoredPosition3DXYZ(self.node_list.xw_display.rect, tonumber(pos[1]) or 0, tonumber(pos[2]) or 0, tonumber(pos[3]) or 0)
		end

		pos_str = data.xw_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			self.xianwa_model:SetRTAdjustmentRootLocalPosition(tonumber(pos[1]) or 0, tonumber(pos[2]) or 0, tonumber(pos[3]) or 0)
		end

		rotate_str = data.xw_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.xianwa_model then
				self.xianwa_model:SetRTAdjustmentRootLocalRotation(tonumber(rot[1]) or 0, tonumber(rot[2]) or 0, tonumber(rot[3]) or 0)
			end
		end

		scale = data.xw_scale
		if scale and scale ~= "" then
			self.xianwa_model:SetRTAdjustmentRootLocalScale(scale)
		end
	end

	--坐骑
	if self.node_list["mount_display"]:GetActive() then
		display_pos_str = data.mount_whole_display_pos
		if display_pos_str and display_pos_str ~= "" then
			local pos = Split(display_pos_str, "|")
			RectTransform.SetAnchoredPosition3DXYZ(self.node_list.mount_display.rect, tonumber(pos[1]) or 0, tonumber(pos[2]) or 0,
				pos[3] or 0)
		end

		pos_str = data.mount_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			self.mount_model:SetRTAdjustmentRootLocalPosition(tonumber(pos[1]) or 0, tonumber(pos[2]) or 0, tonumber(pos[3]) or 0)
		end

		rotate_str = data.mount_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.mount_model then
				self.mount_model:SetRTAdjustmentRootLocalRotation(tonumber(rot[1]) or 0, tonumber(rot[2]) or 0, tonumber(rot[3]) or 0)
			end
		end

		scale = data.mount_scale
		if scale and scale ~= "" then
			self.mount_model:SetRTAdjustmentRootLocalScale(scale)
		end
	end

	return user_model_data
end

--如果列表滑动箭头提示
function CustomizedSuitView:OnAttrScrollValueChanged()
	local show_list = GuiXuDreamWGData.Instance:GetSuitShowList()
	if IsEmptyTable(show_list) then
		return
	end

	local length = #show_list
	local value = self.node_list["suit_list"].scroll_rect.horizontalNormalizedPosition
	if length > 4 then
		self.node_list.left_jiantou:SetActive(value > 0)
		self.node_list.right_jiantou:SetActive(value < 1)
	else
		self.node_list.left_jiantou:SetActive(false)
		self.node_list.right_jiantou:SetActive(false)
	end
end

function CustomizedSuitView:SkillYuLanBtn()
	if not self.select_suit_seq then
		return
	end

	local data = {}
	data.suit = self.select_suit_seq
	CommonSkillShowCtrl.Instance:SetCustomizedSkillViewDataAndOpen(data)
end


function CustomizedSuitView:DoSuitCellsAnim()
    local tween_info = UITween_CONSTS.CustomizedSuitView.ListCellRender
    self.node_list["suit_list"]:SetActive(false)
    ReDelayCall(self, function()
        self.node_list["suit_list"]:SetActive(true)
        local list =  self.suit_list:GetAllItems()
        local sort_list = CustomizedSuitWGData.Instance:GetSortListView(list)
        local count = 0
        for k,v in ipairs(sort_list) do
            if 0 ~= v.index then
                count = count + 1
            end
            v.item:PalySuitItemAnim(count,self.select_list_index)
        end
    end, tween_info.DelayDoTime, "Suit_Cell_Tween")
end
