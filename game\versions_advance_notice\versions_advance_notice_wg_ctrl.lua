require("game/versions_advance_notice/versions_advance_notice_enum")
require("game/versions_advance_notice/versions_advance_notice_wg_data")
require("game/versions_advance_notice/versions_advance_notice_view")

VersionsAdvanceNoticeWGCtrl = VersionsAdvanceNoticeWGCtrl or BaseClass(BaseWGCtrl)
function VersionsAdvanceNoticeWGCtrl:__init()
	if VersionsAdvanceNoticeWGCtrl.Instance then
		error("[VersionsAdvanceNoticeWGCtrl]:Attempt to create singleton twice!")
	end
	VersionsAdvanceNoticeWGCtrl.Instance = self

	self.old_btn_notice_state = nil
	self.old_btn_notice_type = 0
    self.data = VersionsAdvanceNoticeWGData.New()
    self.view = VersionsAdvanceNoticeView.New(GuideModuleName.VersionsAdvanceNoticeView)

	-- 功能开启监听
	-- self.open_fun_change = GlobalEventSystem:Bind(OpenFunEventType.OPEN_TRIGGER, BindTool.Bind(self.OpenFunEventChange, self))

	-- 活动状态监听
	-- self.act_change = BindTool.Bind(self.ActivityChange, self)
	-- ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)

	-- 天数监听
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.DayChange, self))

	-- 等级变化监听
	self.role_attr_change = BindTool.Bind1(self.RoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_attr_change, {"level", "guild_id", "vip_level"})

	-- 玩家充值信息监听
	self.recharge_event = GlobalEventSystem:Bind(AuditEvent.RECHARGE_CHANGE, BindTool.Bind(self.RechargeChange, self))

	self.mainui_create_complete = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreateCallBack, self))
end

function VersionsAdvanceNoticeWGCtrl:__delete()
    self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	if self.mainui_create_complete then
		GlobalEventSystem:UnBind(self.mainui_create_complete)
		self.mainui_create_complete = nil
	end

	-- GlobalEventSystem:UnBind(self.open_fun_change)

	-- ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
	-- self.act_change = nil

	RoleWGData.Instance:UnNotifyAttrChange(self.role_attr_change)
	self.role_attr_change = nil

	-- GlobalEventSystem:UnBind(self.recharge_event)
	-- self.recharge_event = nil

	self.old_btn_notice_state = nil
	self.old_btn_notice_type = nil
	VersionsAdvanceNoticeWGCtrl.Instance = nil
end

function VersionsAdvanceNoticeWGCtrl:OpenView()
    self.view:Open()
end

function VersionsAdvanceNoticeWGCtrl:FlushView(key)
	if self.view:IsOpen() then
    	self.view:Flush(nil, key)
	end
end

-- function VersionsAdvanceNoticeWGCtrl:OpenFunEventChange(check_all, fun_name, is_open)
-- 	if check_all or self.data:GetIsFun(fun_name) then
-- 		self:UpDateEntranceShow()
-- 	end
-- end

-- function VersionsAdvanceNoticeWGCtrl:UpDateEntranceShow(from_red_num)
-- 	local is_show = self.data:CheckEntranceIsShow()
-- 	local notice_type = self.data:GetCurNotice()
-- 	local cfg = self.data:GetNoticeCfgByType(notice_type)
-- 	local show_icon = cfg and cfg.show_icon or ""

-- 	if self.old_btn_notice_state == nil or self.old_btn_notice_state ~= is_show then
--         self.old_btn_notice_state = is_show
--         local red_num = self:GetTotalRedNum()
-- 		MainuiWGCtrl.Instance:FlushView(0, "versions_advance_notice", {is_show = is_show, show_icon = show_icon, red_num = red_num})
-- 	else
-- 		if is_show and notice_type ~= self.old_btn_notice_type then
--             self.old_btn_notice_type = notice_type
--             local red_num = self:GetTotalRedNum()
-- 			MainuiWGCtrl.Instance:FlushView(0, "versions_advance_notice", {is_show = is_show, show_icon = show_icon, red_num = red_num})
-- 		end
--     end
--     if from_red_num then
--         local red_num = self:GetTotalRedNum()
--         MainuiWGCtrl.Instance:FlushView(0, "versions_advance_notice", {is_show = is_show, show_icon = show_icon, red_num = red_num})
--     end
-- end

function VersionsAdvanceNoticeWGCtrl:GetTotalRedNum()
    local list = VersionsAdvanceNoticeWGData.Instance:GetShowList()
    local red_num = 0
    for k, v in pairs(list) do
        if v.type then
            local role_id = RoleWGData.Instance:InCrossGetOriginUid()
            local values = PlayerPrefsUtil.GetString("VERSIONS_ADVANCE_NOTICE"..role_id..v.type)
            if values == nil or values ~= "1" then
                red_num = red_num + 1
            end
        end
    end
    return red_num
end

function VersionsAdvanceNoticeWGCtrl:ActivityChange(activity_type, status, next_time, open_type)
	if self.data:GetIsAct(activity_type) then
		self:FlushView()
	end
end

function VersionsAdvanceNoticeWGCtrl:DayChange()
	--[[
	self:UpDateEntranceShow()
	self:FlushView("day_change")
	]]

	-- 副屏广告
	--MainuiWGCtrl.Instance:FlushFPAdvanceNotice()
	MainuiWGCtrl.Instance:FlushTaskAdvanceNotice()

	----[[ 功能打脸
	local activity_notice_list = {}
	local cfg = self.data:GetActivityNoticeCfg()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local role_level = RoleWGData.Instance:GetRoleLevel()

	for k,v in ipairs(cfg) do
		if v.send_type == 1 then
			if v.conition_type == 0 then
				if server_day == v.open_time and role_level == v.level then
					if not self:CheckIsActivityNoticeClose(v.open_panel_name, v.act) then
						table.insert(activity_notice_list, v)
					end
				end
			elseif v.conition_type == 1 then
				if server_day == v.open_time then
					if not self:CheckIsActivityNoticeClose(v.open_panel_name, v.act) then
						table.insert(activity_notice_list, v)
					end
				end
			end
		end
	end

	if #activity_notice_list > 0 then
		for k,v in ipairs(activity_notice_list) do
			if v.act > 0 then
				if ActivityWGData.Instance:GetActivityIsOpen(v.act) then
					FunOpen.Instance:OpenViewNameByCfg(v.open_panel_name)
				end
			else
				FunOpen.Instance:OpenViewNameByCfg(v.open_panel_name)
			end
		end
	end
	--]]
end

function VersionsAdvanceNoticeWGCtrl:RoleAttrChange(attr_name, value, old_value)
	--[[
	if attr_name == "level" and value > old_value then
		if value == self.data:GetEntranceShowLevel() then
			self:UpDateEntranceShow()
		end

		self:FlushView()
	elseif attr_name == "guild_id" then
		local role_level = RoleWGData.Instance.role_vo.level
		if role_level >= self.data:GetEntranceShowLevel() then
			self:UpDateEntranceShow()
		end

		self:FlushView()
	end
	]]

	-- 副屏广告
	if attr_name == "level" then
		--MainuiWGCtrl.Instance:FlushFPAdvanceNotice()
		MainuiWGCtrl.Instance:FlushTaskAdvanceNotice()
	elseif attr_name == "vip_level" then
		--MainuiWGCtrl.Instance:FlushFPAdvanceNotice()
	end

	----[[ 功能打脸
	local activity_notice_list = {}
	if attr_name == "level" then
		local cfg = self.data:GetActivityNoticeCfg()
		local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		for k,v in ipairs(cfg) do
			if v.send_type == 1 then
				if v.conition_type == 0 then
					if server_day == v.open_time and old_value < v.level and value >= v.level then
						if not self:CheckIsActivityNoticeClose(v.open_panel_name, v.act) then
							table.insert(activity_notice_list, v)
						end
					end
				elseif v.conition_type == 1 then
					if old_value < v.level and value >= v.level then
						if not self:CheckIsActivityNoticeClose(v.open_panel_name, v.act) then
							table.insert(activity_notice_list, v)
						end
					end
				end
			end
		end
	end

	if #activity_notice_list > 0 then
		for k,v in ipairs(activity_notice_list) do
			if v.act > 0 then
				if ActivityWGData.Instance:GetActivityIsOpen(v.act) then
					FunOpen.Instance:OpenViewNameByCfg(v.open_panel_name)
				end
			else
				FunOpen.Instance:OpenViewNameByCfg(v.open_panel_name)
			end
		end
	end
	--]]
end

-- 提前关闭，活动推送不再推送
function VersionsAdvanceNoticeWGCtrl:CheckIsActivityNoticeClose(view_name, activity_type)
	if view_name == GuideModuleName.LevelRechargeView then
		if LevelRechargeWGData.Instance:GetIsBuy(1) then
			return true
		end
	end

	return false
end

function VersionsAdvanceNoticeWGCtrl:RechargeChange(acc_total_gold)
	-- print_error("【-----累充----】：", acc_total_gold)
	--MainuiWGCtrl.Instance:FlushFPAdvanceNotice()
	MainuiWGCtrl.Instance:FlushTaskAdvanceNotice()
end

function VersionsAdvanceNoticeWGCtrl:MainuiOpenCreateCallBack()
	-- 延迟10s 等待活动 和 自定义活动信息全同步
	TryDelayCall(self, function ()
		self:CheckActivityNotice()
	end, 10, "activity_notice_on_login")
end

function VersionsAdvanceNoticeWGCtrl:CheckActivityNotice()
	----[[ 功能打脸
	local activity_notice_list = {}
	local cfg = self.data:GetActivityNoticeCfg()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local role_level = RoleWGData.Instance:GetRoleLevel()

	for k,v in ipairs(cfg) do
		if v.send_type == 2 then
			if v.conition_type == 0 then
				if server_day == v.open_time and role_level == v.level then
					if not self:CheckIsActivityNoticeClose(v.open_panel_name, v.act) then
						table.insert(activity_notice_list, v)
					end
				end
			elseif v.conition_type == 1 then
				if server_day == v.open_time or role_level == v.level then
					if not self:CheckIsActivityNoticeClose(v.open_panel_name, v.act) then
						table.insert(activity_notice_list, v)
					end
				end
			elseif v.conition_type == 2 then
				if server_day <= v.open_time and role_level >= v.level then
					if not self:CheckIsActivityNoticeClose(v.open_panel_name, v.act) then
						table.insert(activity_notice_list, v)
					end
				end
			end
		end
	end

	if #activity_notice_list > 0 then
		for k,v in ipairs(activity_notice_list) do
			if v.act > 0 then
				if ActivityWGData.Instance:GetActivityIsOpen(v.act) then
					FunOpen.Instance:OpenViewNameByCfg(v.open_panel_name)
				end
			else
				FunOpen.Instance:OpenViewNameByCfg(v.open_panel_name)
			end
		end
	end
	--]]
end