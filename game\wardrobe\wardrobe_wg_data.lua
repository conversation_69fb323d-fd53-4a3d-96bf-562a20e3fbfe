WardrobeWGData = WardrobeWGData or BaseClass()

local suit_attr_list = {
	["shengming_jc_per"] = true,
	["gongji_jc_per"] = true,
}

NOW_FASHION_TYPE = {
	[1] = NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_BODY,
	[2] = NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_SHENBING,
	[3] = NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_WING,
}

NOW_JEWELRY_TYPE = {
	[1] = NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_MASK,
	[2] = NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_BELT,
	[3] = NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_WING,
	[4] = NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_FABAO,
}

NOW_EFFECT_TYPE = {
	[1] = NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_FOOT,
	[2] = NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_SHOUHUAN,
}

function WardrobeWGData:__init()
	if WardrobeWGData.Instance ~= nil then
		ErrorLog("[WardrobeWGData] attempt to create singleton twice!")
		return
	end

	WardrobeWGData.Instance = self

	self.suit_list = {}
	self.item_all_list = {}
	self.item_show_list = {}
    local wardrobe_cfg = ConfigManager.Instance:GetAutoConfig("wardrobe_cfg_auto")
    self.theme_map_cfg = ListToMap(wardrobe_cfg.theme, "seq")
	self.suit_act_map_cfg = ListToMap(wardrobe_cfg.activation, "seq", "part")
	self.suit_attr_map_cfg = ListToMapList(wardrobe_cfg.attr, "seq")
	self.suit_xuancai_map_cfg = ListToMap(wardrobe_cfg.xuancai, "seq", "level")
	self.suit_extra_skill_cfg = wardrobe_cfg.extra_skill

	local fashion_cfg = ConfigManager.Instance:GetAutoConfig("shizhuangcfg_auto")
	self.forge_star_cfg = fashion_cfg.forge_star
	self.forge_star_stuff_cfg = ListToMap(fashion_cfg.forge_star, "consume_id")
	self.forge_grade_cfg = fashion_cfg.forge_grade
	self.forge_grade_stuff_cfg = ListToMap(fashion_cfg.forge_grade, "consume_id")
	self.forge_special_effect_cfg = ListToMapList(fashion_cfg.forge_special_effect, "part_type", "index")
	self.stone_cfg = fashion_cfg.stone
	self.stone_item_cfg = ListToMap(fashion_cfg.stone, "stone_item_id")
	self.stone_quality_cfg = ListToMap(fashion_cfg.stone, "stone_quality", "stone_part")
	self.stone_type_cfg = fashion_cfg.stone_type
	self.stone_compose_cfg =  ListToMap(fashion_cfg.stone_compose, "stone_item_id")
	self.stone_compose_inverse_cfg = ListToMap(fashion_cfg.stone_compose, "new_stone_item_id")
	self.model_config_cfg = fashion_cfg.model_config

	self:InitPartActItemList()
	self:InitAllList()
	self:InitSpecialPartResourceItem()
	self:InitForgeStoneCache()

    RemindManager.Instance:Register(RemindName.WardrobeSuit, BindTool.Bind(self.GetTotalRemindAndSuit, self))
    RemindManager.Instance:Register(RemindName.WardrobeFashion, BindTool.Bind(self.GetWardrobeFashionRemind, self))
	RemindManager.Instance:Register(RemindName.WardrobeJewelry, BindTool.Bind(self.GetWardrobeJewelryRemind, self))
	-- RemindManager.Instance:Register(RemindName.WardrobeEffect, BindTool.Bind(self.GetWardrobeEffectRemind, self))
end

function WardrobeWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.WardrobeSuit)
    WardrobeWGData.Instance = nil
end

function WardrobeWGData:InitPartActItemList()
	self.part_act_item_list = {}
	self.item_can_act_suit_list = {}
	local item_id
	for suit, part_list in pairs(self.suit_act_map_cfg) do
		self.part_act_item_list[suit] = {}
		for part, data in pairs(part_list) do
			item_id = self:GetActItemId(data)
			self.part_act_item_list[suit][data.part] = item_id
			self.item_can_act_suit_list[item_id] = suit
		end
	end
end

-- 初始化所有的按星级特殊展示的缓存对象，部件达到多少星切换
function WardrobeWGData:InitSpecialPartResourceItem()
	for _, part_list in pairs(self.suit_act_map_cfg) do
		for _, part_data in pairs(part_list) do
			self:CreateCacheData(part_data)
		end
	end
end

-- 初始化特殊缓存
function WardrobeWGData:CreateCacheData(part_data)
	if not self.special_show then
		self.special_show = {}
	end

	if (not part_data) or (not part_data.new_resource) or (part_data.new_resource == -1) then
		return
	end

	if not self.special_show[part_data.type] then
		self.special_show[part_data.type] = {}
	end

	if not self.special_show[part_data.type][part_data.param1] then
		self.special_show[part_data.type][part_data.param1] = {}
	end

	local temp_data = {}
	local resurce_list = Split(part_data.new_resource, ",")
	temp_data.resurce_list = {}
	for _, res_str in ipairs(resurce_list) do
		local value = tonumber(res_str) or 0
		table.insert(temp_data.resurce_list, value)
	end

	local star_list = Split(part_data.star_level, ",")
	temp_data.star_list = {}
	for _, star_str in ipairs(star_list) do
		local value = tonumber(star_str) or 0
		table.insert(temp_data.star_list, value)
	end

	local item_id_list = Split(part_data.new_item_id, ",")
	temp_data.item_id_list = {}
	for _, item_id_str in ipairs(item_id_list) do
		local value = tonumber(item_id_str) or 0
		table.insert(temp_data.item_id_list, value)
	end

	self.special_show[part_data.type][part_data.param1][part_data.param2] = temp_data
end

-- 获取特殊缓存数据
function WardrobeWGData:GetSpecialCacheData(fashion_type, part_type, fashion_index)
	local emtry = {}
	return (((self.special_show or emtry)[fashion_type] or emtry)[part_type] or emtry)[fashion_index] or nil
end

-- 初始化所有的衣橱列表
function WardrobeWGData:InitAllList()
	self.item_all_list = {}
	if IsEmptyTable(self.theme_map_cfg) then
		return
	end

	for suit, v in pairs(self.theme_map_cfg) do
		self.item_all_list[suit] = self:CreateSuitDataBySuit(suit, v, true)
	end
end

-- 获取所有的衣橱列表
function WardrobeWGData:GetSuitAllList()
	return self.item_all_list
end

-- 获取单个的衣橱数据
function WardrobeWGData:GetSuitSingtonData(suit)
	return self.item_all_list[suit]
end

-- 获取单个的衣橱部件数据
function WardrobeWGData:GetSuitPartSingtonData(suit, part)
	local suit_data = self:GetSuitSingtonData(suit)

	if suit_data and suit_data.part_list then
		return suit_data.part_list[part] or nil
	end

	return nil
end

-- 初始化展示列表
function WardrobeWGData:InitShowList()
	self.item_show_list = {}
	local all_list = self:GetSuitAllList()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if all_list then
		for suit, suit_data in pairs(all_list) do
			local is_open = self:CheckSuitIsOpen(suit)
			if suit_data.theme_type == WARDROBE_THEME_TYPE.WARDROBE and is_open and server_day >= suit_data.open_day then
				self.item_show_list[#self.item_show_list + 1] = suit_data
			end
		end
	end

	if not IsEmptyTable(self.item_show_list) then
		table.sort(self.item_show_list, SortTools.KeyLowerSorter("sort_index"))
	end
end

function WardrobeWGData:GetSuitShowList()
	-- if not self.item_show_list or #self.item_show_list <= 0 then
	-- 	self:InitShowList()
	-- end
	self:InitShowList()

	table.sort(self.item_show_list, SortTools.KeyUpperSorters("suit_color"))
	return self.item_show_list
end

-- 创建一个衣橱对象
function WardrobeWGData:CreateSuitDataBySuit(suit, theme_cfg, need_sort)
	local temp_data = {}

	if not theme_cfg then
		return temp_data
	end

	local act_part_num = 0
	local total_part_num = 0
	local can_act = false
	local can_fetch_num = 0


	temp_data.suit = suit
	temp_data.hm_is_insert_data = false						-- 类型为2的会插入一个下标为零的数据作为大标签，这边这一个标记表示是否插入过
	temp_data.suit_name = theme_cfg.name
	temp_data.theme_type = theme_cfg.theme_type or 0
	temp_data.suit_color = theme_cfg.color or 1
	temp_data.suit_cell_title = theme_cfg.cell_title or ""
	temp_data.suit_type = theme_cfg.suit_type or 0
	temp_data.open_day = theme_cfg.open_day or 0
	temp_data.sort_index = theme_cfg.sort_index or 0
	temp_data.icon = 0
	temp_data.cg_bundle = theme_cfg.cg_bundle
	temp_data.cg_asset = theme_cfg.cg_asset
	temp_data.badground = theme_cfg.badground
	temp_data.cell_bg = theme_cfg.cell_bg or 1
	temp_data.theme_res_id = theme_cfg.theme_res_id or 0
	temp_data.skill_preview_icon = theme_cfg.skill_preview_icon or 0
	temp_data.ui_scene_config_index = theme_cfg.ui_scene_config_index or 1
	temp_data.zwtq_ui_bg = theme_cfg.zwtq_ui_bg or ""
	temp_data.zwtq_ui_icon = theme_cfg.zwtq_ui_icon or 1
	temp_data.zwtq_ui_title = theme_cfg.zwtq_ui_title or ""

	temp_data.part_list = {}

	local part_cfg = self:GetActivationPartList(suit)
	if part_cfg then
		for k, v in pairs(part_cfg) do
			local part = v.part
			local state = self:GetPartStateByData(suit, part)
			local show_item_id = self:GetPartActItemId(suit, part)
			local part_data = {}
			part_data.part = part
			part_data.type = v.type
			part_data.param1 = v.param1
			part_data.part_type = v.param1
			part_data.param2 = v.param2
			part_data.state = state
			part_data.suit_seq = v.seq
			part_data.icon_type = v.icon_type
			part_data.show_item_id = show_item_id
			temp_data.part_list[part] = part_data

			if v.icon > 0 then
				temp_data.icon = show_item_id
			end

			total_part_num = total_part_num + 1
			if state == REWARD_STATE_TYPE.CAN_FETCH then
				can_fetch_num = can_fetch_num + 1
			elseif state == REWARD_STATE_TYPE.FINISH then
				act_part_num = act_part_num + 1
			end
		end
	end

	local suit_cfg = self.suit_attr_map_cfg[suit]
	local suit_less_need = 0
	if can_fetch_num > 0 then
		if IsEmptyTable(suit_cfg) then
			print_error("衣橱拿不到配置属性 seq = ", suit)
		else
			for k,v in pairs(suit_cfg) do
				if act_part_num < v.num and (act_part_num + can_fetch_num) >= v.num then --判断能不能激活下一套装属性
					if suit_less_need == 0 then
						suit_less_need = v.num
					else
						suit_less_need = suit_less_need < v.num and suit_less_need or v.num
					end
					
					can_act = true
				end
			end
		end
	end

	temp_data.sort_index = suit
	if need_sort then
		local item_cfg = ItemWGData.Instance:GetItemConfig(temp_data.icon)
		if item_cfg then
			temp_data.sort_index = item_cfg.color * 100 + suit
		end
	end

	temp_data.can_act = can_act
	temp_data.real_act_num = act_part_num + can_fetch_num--实际激活数
	temp_data.act_part_num = act_part_num
	temp_data.total_part_num = total_part_num
	temp_data.suit_less_need = suit_less_need

	return temp_data
end

function WardrobeWGData:GetPartActItemId(suit, part)
	return (self.part_act_item_list[suit] or {})[part] or 0
end

--通过物品id找到对应suit和partid
function WardrobeWGData:GetSuitPartidByItemId(item_id)
	for k_1, v_1 in pairs(self.part_act_item_list) do
		for k_2, v_2 in pairs(v_1) do
			if v_2 == item_id then
				return k_1, k_2
			end
		end
	end
end

function WardrobeWGData:IsWardrobeSuitByItemId(item_id)
	return self.item_can_act_suit_list[item_id]
end

function WardrobeWGData:GetActItemId(data)
	local act_id = 0
	if IsEmptyTable(data) then
		return act_id
	end

	local cfg
	if data.type == WARDROBE_PART_TYPE.FASHION then
		cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
		act_id = NewAppearanceWGData.Instance:GetFashionItemId(data.param1, data.param2)
	elseif data.type == WARDROBE_PART_TYPE.MOUNT then
		cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(data.param1)
		act_id = cfg and cfg.active_item_id or 0
	elseif data.type == WARDROBE_PART_TYPE.LING_CHONG then
		cfg = NewAppearanceWGData.Instance:GetLingChongActCfgByImageId(data.param1)
		act_id = cfg and cfg.active_item_id or 0
	elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then
		cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
		act_id = cfg and cfg.active_need_item_id or 0
	elseif data.type == WARDROBE_PART_TYPE.XIAN_WA then
		cfg = MarryWGData.Instance:GetActiveCfgByTypeAndId(data.param1, data.param2)
		act_id = cfg and cfg.item_id or 0
	end

	return act_id, cfg
end

function WardrobeWGData:GetThemeCfg()
	return self.theme_map_cfg
end

function WardrobeWGData:GetThemeCfgBySuit(suit)
	return self.theme_map_cfg[suit]
end

function WardrobeWGData:GetActivationPartList(suit)
	return self.suit_act_map_cfg[suit]
end

function WardrobeWGData:GetPartActivationCfg(suit, part)
	return (self.suit_act_map_cfg[suit] or {})[part]
end

function WardrobeWGData:GetPartStateByData(suit, part)
	return (self.suit_list[suit] or {})[part] or 0
end

function WardrobeWGData:CheckSuitIsOpen(suit)
	local is_open = false
	local icon, show_item_id, cfg
	local part_cfg = self:GetActivationPartList(suit)
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	if not IsEmptyTable(part_cfg) then
		for k, v in pairs(part_cfg) do
			show_item_id, cfg = self:GetActItemId(v)
			if cfg then
				local is_act = NewAppearanceWGData.Instance:GetFashionIsAct(cfg.part_type or 0, cfg.index or 0)
				if is_act then
					is_open = true
				end

				if cfg.is_default_show == 1 then
					if cfg.condition_type == 1
						and (server_day >= cfg.skynumber_show
						or role_level >= cfg.level_show) then
						is_open = true
					elseif cfg.condition_type == 2
						and server_day >= cfg.skynumber_show
						and role_level >= cfg.level_show then
						is_open = true
					end
				end
			end

			if v.icon > 0 then
				icon = show_item_id
			end
		end
	end

	return is_open, icon
end

function WardrobeWGData:IsShowSuitList(suit)
	local cfg = self:GetSuitAllListBySuit(suit)
	if not IsEmptyTable(cfg) then
		local part_list = cfg.part_list
		for k,v in pairs(part_list) do
			if v.state ~= REWARD_STATE_TYPE.UNDONE and (not v.is_extra) then
				return true
			end
		end
	end

	return false
end

-- 跳转
function WardrobeWGData:GetJumpIndexOnShowList(suit)
	local cfg = self:GetSuitShowList()
	local index = 1
	if IsEmptyTable(cfg) then
		return index
	end

	for k, v in pairs(cfg) do
		if v.suit == suit then
			index = k
			break
		end
	end

	return index
end

function WardrobeWGData:GetSuitAllListBySuit(suit)
	local show_list = self:GetSuitAllList()
	return show_list and show_list[suit]
end



function WardrobeWGData:GetAttrBySuit(suit)
	local attr_list = {}
	local attr_cfg = self.suit_attr_map_cfg[suit]
	if IsEmptyTable(attr_cfg) then
		return attr_list
	end

	local show_data = self:GetSuitAllListBySuit(suit)
	local act_part_num = show_data and show_data.act_part_num or 0

	-- 获取配置的配属性字段列表
-- cfg_map_attr_list = {[配置表配的属性字段] = 属性表的有效字段}
-- attr_map_cfg_list = {[属性表的有效字段] = 配置表配的属性字段}
	local cfg_map_attr_list = AttributeMgr.GetUsefulAttributteByClass(attr_cfg[1])



	for k, v in ipairs(attr_cfg) do
		local temp_data = {}
		temp_data.need_num = v.num
		temp_data.is_act = act_part_num >= v.num
		temp_data.all_act_attr = false
		temp_data.attr_list = {}

		for key, value in pairs(v) do
			local name = cfg_map_attr_list[key]
			if name and value > 0 and not suit_attr_list[key] then
				table.insert(temp_data.attr_list, {attr_type = name, value = value, sort_index = AttributeMgr.GetSortAttributeIndex(name)})
			end
		end

		if not IsEmptyTable(temp_data.attr_list) then
			table.sort(temp_data.attr_list, SortTools.KeyLowerSorter("sort_index"))
		end

		if v.extra_skill_id and v.extra_skill_id > 0 then
			table.insert(temp_data.attr_list, {attr_type = "attr_skill", value = v.extra_skill_id})
		end

		table.insert(attr_list, temp_data)

		if v.add_per and v.add_per > 0 then
			local all_act_data = {}
			all_act_data.need_num = 999--v.num + 1
			all_act_data.is_act = act_part_num >= v.num
			all_act_data.attr_list = {}
			all_act_data.all_act_attr = true

			for key, value in pairs(v) do
				local name = cfg_map_attr_list[key]
				if suit_attr_list[key] and name and value > 0 then
					table.insert(all_act_data.attr_list, {attr_type = name, value = value, sort_index = AttributeMgr.GetSortAttributeIndex(name)})
				end
			end
			table.insert(all_act_data.attr_list, {attr_type = "add_per", value = v.add_per, sort_index = 999})
			table.insert(attr_list, all_act_data)
		end
	end

	if not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("need_num"))
	end

	return attr_list
end

-- 获取总属性
function WardrobeWGData:GetAllActAttr()
	local attr_list = {}
	local add_per = 0
	local show_data, act_part_num
	local base_attribute, temp_attribute
	for suit, list in pairs(self.suit_attr_map_cfg) do
		show_data = self:GetSuitAllListBySuit(suit)
		act_part_num = show_data and show_data.act_part_num or 0
		for k, data in pairs(list) do
			if act_part_num >= data.num then
				temp_attribute = AttributeMgr.GetAttributteValueByClass(data)
				if base_attribute == nil then
					base_attribute = temp_attribute
				else
					base_attribute = AttributeMgr.AddAttributeAttr(base_attribute, temp_attribute)
				end

				if data.add_per > 0 then
					add_per = add_per + data.add_per
				end
			end
		end
	end

	if add_per > 0 then
		table.insert(attr_list, {attr_str = "add_per", value = add_per, sort_index = 0})
	end

	local calc_cap_lsit = AttributePool.AllocDirtyAttribute()
	for k, v in pairs(base_attribute) do
		if v > 0 then
			local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(k)
			local value = is_per and v or v * (1 + (add_per * 0.0001))
			local temp_data = {
				attr_str = k,
				value = value,
				sort_index = AttributeMgr.GetSortAttributeIndex(k),
			}
			calc_cap_lsit[k] = value
			table.insert(attr_list, temp_data)
		end
	end

	if not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("sort_index"))
	end

	return attr_list, AttributeMgr.GetCapability(calc_cap_lsit)
end

-- 单套的战力计算
function WardrobeWGData:CalcSuitCapBySuit(select_suit)
	local all_base_attr_list = {}
	local add_per = 0
	local show_data, act_part_num
	local temp_attribute
	local base_attribute = AttributePool.AllocDirtyAttribute()
	-- 统计所有套的激活属性值
	for suit, list in pairs(self.suit_attr_map_cfg) do
		show_data = self:GetSuitAllListBySuit(suit)
		act_part_num = show_data and show_data.act_part_num or 0
		for k, data in pairs(list) do
			if act_part_num >= data.num then
				temp_attribute = AttributeMgr.GetAttributteValueByClass(data)
				base_attribute = AttributeMgr.AddAttributeAttr(base_attribute, temp_attribute)

				if select_suit == suit and data.add_per > 0 then
					add_per = add_per + data.add_per
				end
			end
		end
	end

	-- 当前套的激活属性值
	local cur_attr_list = AttributePool.AllocDirtyAttribute()
	local cur_suit_attr_cfg = self.suit_attr_map_cfg[select_suit] or {}
	show_data = self:GetSuitAllListBySuit(select_suit)
	act_part_num = show_data and show_data.act_part_num or 0
	for k, data in pairs(cur_suit_attr_cfg) do
		if act_part_num >= data.num then
			temp_attribute = AttributeMgr.GetAttributteValueByClass(data)
			cur_attr_list = AttributeMgr.AddAttributeAttr(cur_attr_list, temp_attribute)
		end
	end

	-- 衣橱基础加成对所有套激活基础属性的加成后
	for k, v in pairs(base_attribute) do
		if v > 0 then
			local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(k)
			if not is_per then
				all_base_attr_list[k] = v * ((add_per * 0.0001))
			end
		end
	end

	cur_attr_list = AttributeMgr.AddAttributeAttr(cur_attr_list, all_base_attr_list)
	return AttributeMgr.GetCapability(cur_attr_list)
end

function WardrobeWGData:GetSuitHaveOneAct()
	local show_data, act_part_num
	for suit, list in pairs(self.suit_attr_map_cfg) do
		for k, data in pairs(list) do
			show_data = self:GetSuitAllListBySuit(suit)
			act_part_num = show_data and show_data.act_part_num or 0
			if act_part_num >= data.num then
				return true
			end
		end
	end

	return false
end

function WardrobeWGData:ShowWardrobeBeStronger(value) --衣橱变强
	value = value or 0
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.WARDROBE_UPGRADE, value, function ()
         ViewManager.Instance:Open(GuideModuleName.WardrobeView)
     end)
end

function WardrobeWGData:GetTotalRemindAndSuit()
	local cfg = self:GetSuitShowList()
	if IsEmptyTable(cfg) then
		self:ShowWardrobeBeStronger(0)
		return 0, 1
	end

	if not FunOpen.Instance:GetFunIsOpened(FunName.WardrobeView) then
		self:ShowWardrobeBeStronger(0)
		return 0, 1
	end

	local length = #cfg
	for i = 1, length do
		-- if cfg[i].can_act then
		-- 	self:ShowWardrobeBeStronger(1)
		-- 	return 1, i
		-- end

		local is_remind = self:GetWardRobeRemindBySuit(cfg[i].part_list)
		if is_remind then
			return 1, i
		end
	end
	self:ShowWardrobeBeStronger(0)
	return 0, 1
end

-- 获取红点信息根据时装套装
function WardrobeWGData:GetWardRobeRemindBySuit(part_list)
	for _, part_data in pairs(part_list) do
		local is_remind = self:GetWardRobeRemindByPartData(part_data)

		if is_remind then
			return true
		end
	end

	return false
end

-- 获取对应时装的红点信息
function WardrobeWGData:GetWardRobeFashionRemindByPartIndex(part_type, part_index)
	local is_remind = false
	local is_act = false

	local level = NewAppearanceWGData.Instance:GetFashionLevel(part_type, part_index)
	local level_cfg = NewAppearanceWGData.Instance:GetFashionUpLevelCfg(part_type, part_index)
	local is_max = level >= level_cfg.max_up_level
	local stuff_num = NewAppearanceWGData.Instance:GetFashionUpLevelCostStuffNumCfg(part_type, part_index, level) or 1
	is_act = NewAppearanceWGData.Instance:GetFashionIsAct(part_type, part_index)

	if level_cfg.stuff_id and level_cfg.stuff_id > 0 then
		local num = ItemWGData.Instance:GetItemNumInBagById(level_cfg.stuff_id)
		is_remind = num >= stuff_num and (not is_max)
	end

	return is_remind, is_act
end

-- 获取红点信息根据时装部件
function WardrobeWGData:GetWardRobeRemindByPartData(suit_data)
	if not suit_data then
		return false
	end

	local is_remind = false
	local is_act = false
	if suit_data.type == WARDROBE_PART_TYPE.FASHION then -- 时装大类
		is_remind, is_act = self:GetWardRobeFashionRemindByPartIndex(suit_data.param1, suit_data.param2)
	else
		local qichong_select_type = nil
		if suit_data.type == WARDROBE_PART_TYPE.MOUNT then
			qichong_select_type = MOUNT_LINGCHONG_APPE_TYPE.MOUNT
		else
			qichong_select_type = MOUNT_LINGCHONG_APPE_TYPE.KUN
		end

		local info = NewAppearanceWGData.Instance:GetSpecialQiChongInfo(qichong_select_type, suit_data.param1)
		local act_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByImageId(qichong_select_type, suit_data.param1)

		if info and act_cfg then
			local star_level = info.star_level or info.star
			local max_level = NewAppearanceWGData.Instance:GetSpecialQiChongMaxStarLevel(qichong_select_type, suit_data.param1)
			local is_max = star_level >= max_level
			is_act = NewAppearanceWGData.Instance:GetSpecialQiChongIsAct(qichong_select_type, suit_data.param1)

			-- 消耗
			local need_item_id, need_num = 0, 1
			-- 限时道具激活的限时
			local is_limit_time_type = act_cfg.timed_type == 2

			if is_limit_time_type or (not is_max and not is_act) then
				need_item_id = act_cfg.active_item_id or act_cfg.active_need_item_id
			elseif not is_max and is_act then
				local up_star_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongUpStarCfg(qichong_select_type, suit_data.param1)
				need_item_id = up_star_cfg.need_item or up_star_cfg.item_id
				need_num = NewAppearanceWGData.Instance:GetSpecialQiChongUpStarCostNum(qichong_select_type, suit_data.param1, star_level)
			end

			if need_item_id > 0 then
				local num = ItemWGData.Instance:GetItemNumInBagById(need_item_id)
				is_remind = num >= need_num
			end
		end
	end

	return is_remind, is_act
end

-- 时装红点
function WardrobeWGData:GetWardrobeFashionRemind()
	for i, v in ipairs(NOW_FASHION_TYPE) do
		if self:GetWardrobeAllPartRemindByIndex(v, true) then
			return 1
		end
	end

	return 0
end

-- 饰品红点
function WardrobeWGData:GetWardrobeJewelryRemind()
	for i, v in ipairs(NOW_JEWELRY_TYPE) do
		if self:GetWardrobeAllPartRemindByIndex(v, false) then
			return 1
		end
	end

	return 0
end

-- 特效红点
function WardrobeWGData:GetWardrobeEffectRemind()
	for i, v in ipairs(NOW_EFFECT_TYPE) do
		if self:GetWardrobeAllPartRemindByIndex(v) then
			return 1
		end
	end

	return 0
end

-- 获取部位红点
function WardrobeWGData:GetWardrobeAllPartRemindByIndex(tab_index, is_wing)
	local list = NewAppearanceWGData.Instance:GetSortShowListByTabIndex(tab_index)
	local new_list = {}

	if NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_WING == tab_index then
		for i, v in ipairs(list) do
			local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(v.part_type, v.index)
			if fashion_cfg then
				if is_wing and fashion_cfg.is_wing == 1 then
					table.insert(new_list, v)
				end

				if (not is_wing) and fashion_cfg.is_wing ~= 1 then
					table.insert(new_list, v)
				end
			end
		end
	else
		new_list = list
	end

	for m, n in ipairs(new_list) do
		if n and n.is_remind then
			return true
		end
	end

	-- 	if n and self:GetWardrobeFashionCollectRed(n.part_type, n.index) then
	-- 		return true
	-- 	end
	-- end

	return false
end

-- 获取部位打造红点
function WardrobeWGData:GetWardrobeFashionCollectRed(fashion_part_type, fashion_index)
	local forge_data = self:GetShizhuangForgeInfoByPartType(fashion_part_type, fashion_index)
	local is_act = NewAppearanceWGData.Instance:GetFashionIsAct(fashion_part_type, fashion_index)
	local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(fashion_part_type, fashion_index)
	if (not fashion_cfg) or (fashion_cfg.is_open_forge ~= 1) or (not is_act) then
		return false
	end

	local grade_level = forge_data and forge_data.grade_level or 0
	local list = forge_data and forge_data.stone_part_list or {}
	local star = forge_data and forge_data.star or 0
	local star_grade = math.floor(star / 10)
	local star_show = star % 10
	local real_shar_show = star_show
	local is_need_upgrade = false

	if star_show == 0 and star_grade ~= 0 then
		if star_grade ~= grade_level then	-- 未突破
			real_shar_show = 10	
			is_need_upgrade = true
		else	-- 已突破
			real_shar_show = star_show
		end
	end

	local spend_cfg = nil
	if is_need_upgrade then
		spend_cfg = self:GetForgeGradeCfgByLevel(grade_level)
	else
		spend_cfg = self:GetForgeStarCfgByStar(star)
	end

	local is_full = spend_cfg == nil
	local is_have_item = false

	if spend_cfg then
		local star_up_item_id = spend_cfg.consume_id
		local star_up_item_num = spend_cfg.consume_num
		local item_num = ItemWGData.Instance:GetItemNumInBagById(star_up_item_id)
		is_have_item = item_num >= star_up_item_num
	end

	local grade_cfg = WardrobeWGData.Instance:GetForgeGradeCfgByLevel(grade_level)
	local stone_quality = grade_cfg and grade_cfg.stone_quality or 0
	local is_have_stone = false

	for j = 0, WARDROBE_NEW_PART_TYPE.MAX_STONE_PART_COUNT do
		if list[j] ~= nil and list[j] == -1 then
			local now_stone_cfg = WardrobeWGData.Instance:GetStoneCfgByQualityPart(stone_quality, j)
			local aim_stone_cfg = nil
			local aim_stone_item = nil
			local is_can_put = false
	
			for i = stone_quality, 1, -1 do
				aim_stone_cfg = WardrobeWGData.Instance:GetStoneCfgByQualityPart(i, j)
				aim_stone_item = aim_stone_cfg and aim_stone_cfg.stone_item_id or 0
		
				if aim_stone_item ~= 0 then
					local item_num = ItemWGData.Instance:GetItemNumInBagById(aim_stone_item)
		
					if item_num >= 1 then
						is_can_put = true
						break
					end
				end
			end
	
			if is_can_put and aim_stone_cfg then
				if aim_stone_item == now_stone_cfg.stone_item_id then	-- 有宝石未镶嵌
					is_have_stone = true
				end
			end
		end
	end

	return ((not is_full) and is_have_item) or is_have_stone
end

function WardrobeWGData:CheckIsSameRes(type, small_type, res_id)
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local appearance = role_vo.appearance

	if IsEmptyTable(role_vo) then
		return false
	end

	local wear_res_id = 0
	if type == WARDROBE_PART_TYPE.FASHION then				-- 时装大类
		if small_type == SHIZHUANG_TYPE.WING then			-- 羽翼
			wear_res_id = appearance and appearance.wing_appeid or role_vo.wing_appeid
		elseif small_type == SHIZHUANG_TYPE.FABAO then		-- 法宝
			wear_res_id = appearance and appearance.fabao_appeid or role_vo.fabao_appeid
		elseif small_type == SHIZHUANG_TYPE.JIANZHEN then	-- 剑阵
			wear_res_id = appearance and appearance.jianzhen_appeid or role_vo.jianzhen_appeid
		elseif small_type == SHIZHUANG_TYPE.SHENBING then	-- 武器
			if appearance.shenwu_appeid and 0 ~= appearance.shenwu_appeid then
    			wear_res_id = appearance.shenwu_appeid
			elseif appearance.fashion_wuqi and 0 ~= appearance.fashion_wuqi then
				wear_res_id = appearance.fashion_wuqi
			end
		elseif small_type == SHIZHUANG_TYPE.BODY then		-- 时装
			wear_res_id = appearance.fashion_body
		elseif small_type == SHIZHUANG_TYPE.MASK then		-- 脸饰
			wear_res_id = appearance.mask
		elseif small_type == SHIZHUANG_TYPE.BELT then 		-- 腰饰
			wear_res_id = appearance.waist
		elseif small_type == SHIZHUANG_TYPE.WEIBA then		-- 尾巴
			wear_res_id = appearance.tail
		elseif small_type == SHIZHUANG_TYPE.SHOUHUAN then	-- 手环
			wear_res_id = appearance.shouhuan
		elseif small_type == SHIZHUANG_TYPE.HALO then	-- 光环
			wear_res_id = appearance.fashion_guanghuan
		elseif small_type == SHIZHUANG_TYPE.FOOT then		-- 足迹
			wear_res_id = appearance.fashion_foot
		elseif small_type == SHIZHUANG_TYPE.PHOTOFRAME then	-- 相框
			wear_res_id = appearance.fashion_photoframe
		elseif small_type == SHIZHUANG_TYPE.BUBBLE then		-- 气泡
			wear_res_id = appearance.fashion_bubble
		end
	elseif type == WARDROBE_PART_TYPE.MOUNT then			-- 坐骑
		local info = NewAppearanceWGData.Instance:GetQiChongInfo(MOUNT_LINGCHONG_APPE_TYPE.MOUNT)
		wear_res_id = info and info.used_imageid
	elseif type == WARDROBE_PART_TYPE.LING_CHONG then		-- 灵宠
		local info = NewAppearanceWGData.Instance:GetQiChongInfo(MOUNT_LINGCHONG_APPE_TYPE.MOUNT)
		wear_res_id = info and info.used_imageid
	elseif type == WARDROBE_PART_TYPE.HUA_KUN then			-- 化鲲
		local info = NewAppearanceWGData.Instance:GetQiChongInfo(MOUNT_LINGCHONG_APPE_TYPE.MOUNT)
		wear_res_id = info and info.used_imageid
	elseif type == WARDROBE_PART_TYPE.XIAN_WA then			-- 仙娃
		wear_res_id = MarryWGData.Instance:GetUseBabyAppeImageId()
	end

	return wear_res_id == res_id
end

function WardrobeWGData:SetAllSuitStateInfo(protocol)
	self.suit_list = protocol.suit_list
	self:UpdateAllSuitList()
end

function WardrobeWGData:SetSingleSuitStateInfo(protocol)
	self.suit_list[protocol.update_suit] = protocol.part_list
	self:UpdateSingleSuitList(protocol.update_suit)
end

function WardrobeWGData:UpdateAllSuitList()
	local all_list = self:GetSuitAllList()
	if all_list then
		for _, suit_data in pairs(all_list) do
			self:FlushOneSuitDataBySuit(suit_data)
		end
	end
end

-- 刷新一个衣橱套装
function WardrobeWGData:FlushOneSuitDataBySuit(suit_data)
	local act_part_num = 0
	local total_part_num = 0
	local can_act = false
	local can_fetch_num = 0
	local is_part_red = false

	for _, part_data in pairs(suit_data.part_list) do
		if not suit_data.hm_is_insert_data then
			local part = part_data.part
			local state = self:GetPartStateByData(suit_data.suit, part)
			part_data.state = state
	
			total_part_num = total_part_num + 1
			if state == REWARD_STATE_TYPE.CAN_FETCH then
				can_fetch_num = can_fetch_num + 1
			elseif state == REWARD_STATE_TYPE.FINISH then
				act_part_num = act_part_num + 1
			end
		end
	end

	local suit_cfg = self.suit_attr_map_cfg[suit_data.suit]
	local suit_less_need = 0

	if can_fetch_num > 0 then
		if IsEmptyTable(suit_cfg) then
			print_error("衣橱拿不到配置属性 seq = ", suit_data.suit)
		else
			for k,v in pairs(suit_cfg) do
				if act_part_num < v.num and (act_part_num + can_fetch_num) >= v.num then --判断能不能激活下一套装属性
					if suit_less_need == 0 then
						suit_less_need = v.num
					else
						suit_less_need = suit_less_need < v.num and suit_less_need or v.num
					end
					
					can_act = true
				end
			end
		end
	end

	suit_data.can_act = can_act
	suit_data.real_act_num = act_part_num + can_fetch_num--实际激活数
	suit_data.act_part_num = act_part_num
	suit_data.total_part_num = total_part_num
	suit_data.suit_less_need = suit_less_need
end

function WardrobeWGData:UpdateSingleSuitList(suit)
	local suit_data = self:GetSuitSingtonData(suit)
	if suit_data then
		self:FlushOneSuitDataBySuit(suit_data)
	end
end

--某些套装达到某个星数使用不同的表现形象
function WardrobeWGData:TryGetSpecialShowItemId(get_type, aim_start_level, fashion_type, part_type, fashion_index, old_id)
	local cache_data = self:GetSpecialCacheData(fashion_type, part_type, fashion_index)
	if not cache_data then
		return old_id
	end

	local real_id = old_id
	local get_show_index = 0
	for index, star_level in ipairs(cache_data.star_list) do
		if aim_start_level >= star_level then
			get_show_index = index
		end
	end

	local get_table = nil
	if get_type == WARD_ROBE_GET_NEW_TYPE.NEW_ITEM then
		get_table = cache_data.item_id_list
	else
		get_table = cache_data.resurce_list
	end

	if get_show_index ~= 0 and get_table ~= nil then
		real_id = get_table[get_show_index]
	end

	return real_id
end

-- 衣橱封装时装套装数据
function WardrobeWGData:AssembleRoleModelDataByTypeIndex(part_type, index, export_data, start_level)
	local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(part_type, index)

	if fashion_cfg then
		local res_id = fashion_cfg.resouce

		if start_level ~= nil and start_level ~= 0 then
			local _, new_resouce = NewAppearanceColorfulWGData.Instance:GetAppearanceColorfulCfgByOld(
				WARDROBE_PART_TYPE.FASHION, 
				part_type, index, 
				start_level, 
				res_id
			)

			res_id = new_resouce
		end

		if part_type == SHIZHUANG_TYPE.MASK then			-- 脸饰
			export_data.mask_id = res_id
		elseif part_type == SHIZHUANG_TYPE.BELT then 		-- 腰饰
			export_data.belt_id = res_id
		elseif part_type == SHIZHUANG_TYPE.WEIBA then		-- 尾巴
			export_data.tail_id = res_id
		elseif part_type == SHIZHUANG_TYPE.SHOUHUAN then	-- 手环
			export_data.shou_huan_id = res_id
		elseif part_type == SHIZHUANG_TYPE.HALO then	-- 光环
			export_data.halo_id = res_id
		elseif part_type == SHIZHUANG_TYPE.WING then		-- 羽翼
			export_data.wing_id = res_id
		elseif part_type == SHIZHUANG_TYPE.FABAO then		-- 法宝
			export_data.fabao_id = res_id
		elseif part_type == SHIZHUANG_TYPE.JIANZHEN then	-- 剑阵
			export_data.jianzhen_id = res_id
		elseif part_type == SHIZHUANG_TYPE.SHENBING then	-- 武器
			export_data.weapon_id = res_id
		elseif part_type == SHIZHUANG_TYPE.FOOT then		-- 足迹
			export_data.foot_effect_id = res_id
		end
	end
end

-- 衣橱依据套装星级获取当前的炫彩等级
function WardrobeWGData:GetXuanCaiBySuitLevel(suit, suit_level)
	if (not self.suit_xuancai_map_cfg) or (not self.suit_xuancai_map_cfg[suit]) then
		return nil
	end

	local cfg = nil
	for _, xuancai_data in pairs(self.suit_xuancai_map_cfg[suit]) do
		if suit_level >= xuancai_data.level then
			cfg = xuancai_data
		end
	end

	return cfg
end

-- 衣橱依据套装是否存在炫彩
function WardrobeWGData:GetHaveXuanCaiBySuit(suit)
	if (not self.suit_xuancai_map_cfg) or (not self.suit_xuancai_map_cfg[suit]) then
		return false
	end

	local index = 0
	for _, _ in pairs(self.suit_xuancai_map_cfg[suit]) do
		index = index + 1
	end

	return index > 1
end

------------------------------------新衣橱-----------------------------------------
-- 初始化对应缓存
function WardrobeWGData:InitForgeStoneCache()
	if self.forge_star_cfg then
		self.forge_star_suc_per = {}

		for _, v in pairs(self.forge_star_cfg) do
			if v and v.star and v.star_weight then
				local star_weight_list = Split(v.star_weight, "|")
				for _, n in ipairs(star_weight_list) do
					local star_weight = Split(n, ",")
					local add_star = tonumber(star_weight[1]) or 0
					local suc_per = tonumber(star_weight[2]) or 0

					if add_star > 0 then
						self.forge_star_suc_per[v.star] = suc_per
						break
					end
				end
			end
		end
	end

	if self.stone_type_cfg then
		self.part_use_stone = {}
		for _, v in ipairs(self.stone_type_cfg) do
			if v.part_type and v.stone_type then
				local part_type_list = Split(v.part_type, "|")
				for _, n in ipairs(part_type_list) do
					local part_type = tonumber(n) or 0
					self.part_use_stone[part_type] = v.stone_type
				end
			end
		end
	end

	if self.model_config_cfg then
		self.model_cfg_cache = {}
		local get_pos_vec = function(pos_str)
			local min_pos = Split(pos_str, "|")
			local pos_x = tonumber(min_pos[1]) or 0
			local pos_y = tonumber(min_pos[2]) or 0
			local pos_z = tonumber(min_pos[3]) or 0
			local pos_data = Vector3(pos_x, pos_y, pos_z)
			return pos_data
		end

		for i, v in ipairs(self.model_config_cfg) do
			self.model_cfg_cache[i] = {}

			if v and v.min_pos then
				self.model_cfg_cache[i].min_pos = get_pos_vec(v.min_pos)
			end

			if v and v.max_pos then
				self.model_cfg_cache[i].max_pos = get_pos_vec(v.max_pos)
			end

			self.model_cfg_cache[i].min_scale = Vector3(v.min_scale, v.min_scale, v.min_scale)
			self.model_cfg_cache[i].max_scale = Vector3(v.max_scale, v.max_scale, v.max_scale)
		end
	end
end

-- 获取时装天赏星级配置
function WardrobeWGData:GetForgeStarCfgByStar(star)
	local empty = {}
	return (self.forge_star_cfg or empty)[star]
end

-- 获取时装天赏阶级配置
function WardrobeWGData:GetForgeGradeCfgByLevel(grade_lv)
	local empty = {}
	return (self.forge_grade_cfg or empty)[grade_lv]
end

-- 获取时装天赏的特殊效果
function WardrobeWGData:GetForgeEffectCfgByPartIndex(part_type, index)
	local empty = {}
	return ((self.forge_special_effect_cfg or empty)[part_type] or empty)[index]
end

-- 获取时装天赏的天石配置
function WardrobeWGData:GetStoneCfgBySeq(stone_seq)
	local empty = {}
	return (self.stone_cfg or empty)[stone_seq]
end

-- 获取时装天赏的天石配置
function WardrobeWGData:GetStoneCfgByItemId(stone_item_id)
	local empty = {}
	return (self.stone_item_cfg or empty)[stone_item_id]
end

-- 获取时装天赏的天石配置根据质量和部位
function WardrobeWGData:GetStoneCfgByQualityPart(stone_quality, stone_part)
	local empty = {}
	return ((self.stone_quality_cfg or empty)[stone_quality] or empty)[stone_part]
end

-- 获取时装部位使用的天石类型
function WardrobeWGData:GetStoneTypeByPartType(part_type)
	local empty = {}
	return (self.part_use_stone or empty)[part_type]
end

-- 获取当前道具的合成升级配置
function WardrobeWGData:GetStoneComposeByItemId(item_id)
	local empty = {}
	return (self.stone_compose_cfg or empty)[item_id]
end

-- 获取当前道具的合成升级配置（反推）
function WardrobeWGData:GetStoneComposeInverseByItemId(item_id)
	local empty = {}
	return (self.stone_compose_inverse_cfg or empty)[item_id]
end

-- 获取当前天赏打造星级打造的成功率
function WardrobeWGData:GetForgeSucPerByStar(star)
	local empty = {}
	return (self.forge_star_suc_per or empty)[star]
end

-- 获取当前模型放大缩小配置
function WardrobeWGData:GetModelCfgCacheByType(part_type)
	local empty = {}
	return (self.model_cfg_cache or empty)[part_type]
end

-- 获取当前道具的合成升级配置
function WardrobeWGData:GetIsStarOrGradeItem(item_id)
	local empty = {}
	local item = (self.forge_star_stuff_cfg or empty)[item_id]
	local item2 = (self.forge_grade_stuff_cfg or empty)[item_id]

	if item ~= nil or item2 ~= nil then
		return true
	end

	return false
end

-- 获取当前道具的合成升级配置
function WardrobeWGData:GetExtraSkillCfgBySkillId(extra_skill_id)
	local empty = {}
	return (self.suit_extra_skill_cfg or empty)[extra_skill_id]
end
----------------------------------------------------

-- 设置时装预览方案
function WardrobeWGData:SetShiZhuangProjectInfo(protocol)
	self.shizhuang_project = protocol.shizhuang_project
end

-- 获取时装保存配置
function WardrobeWGData:GetShiZhuangProjectInfoById(priject_id)
	local empty = {}
	return (self.shizhuang_project or empty)[priject_id]
end

-- 获取时装部件保存配置
function WardrobeWGData:GetShiZhuangProjectInfoByIdPart(priject_id, part_type)
	local empty = {}
	return ((self.shizhuang_project or empty)[priject_id] or {})[part_type]
end

-- 设置时装天赏信息
function WardrobeWGData:SetShizhuangForgeInfo(protocol)
	if protocol.forge_item_list then
		for i, v in ipairs(protocol.forge_item_list) do
			self:SetOneShizhuangForgeInfo(i, v)
		end
	end
end

-- 设置时装天赏信息(单个)
function WardrobeWGData:SetShizhuangForgeUpdateInfo(protocol)
	local client_index = protocol.seq + 1
	self:SetOneShizhuangForgeInfo(client_index, protocol.forge_info)
end

-- 更新一个时装打造
function WardrobeWGData:SetOneShizhuangForgeInfo(index, info)
	if not self.forge_item_list then
		self.forge_item_list = {}
	end

	if not self.forge_index_item_list then
		self.forge_index_item_list = {}
	end

	if not self.forge_item_list[info.part_type] then
		self.forge_item_list[info.part_type] = {}
	end

	if not self.forge_index_item_list[info.part_type] then
		self.forge_index_item_list[info.part_type] = {}
	end

	self.forge_item_list[info.part_type][info.index] = info
	self.forge_index_item_list[info.part_type][info.index] = index
end

-- 获取时装的天赏信息
function WardrobeWGData:GetShizhuangForgeInfoByPartType(part_type, index)
	local empty = {}
	return ((self.forge_item_list or empty)[part_type] or empty)[index]
end

-- 获取时装的天赏的对应下标
function WardrobeWGData:GetShizhuangForgeIndexByPartType(part_type, index)
	local empty = {}
	return ((self.forge_index_item_list or empty)[part_type] or empty)[index]
end

----------------------------------------------------
-- 获取衣橱穿搭保存数据
function WardrobeWGData:GetShiZhuangProjectList()
	if not self.shizhuang_project then
		return {}
	end

	local scheme_data = {}

	-- 0号位置不取
	for i, shizhuang_list in ipairs(self.shizhuang_project) do
		scheme_data[i] = {}

		for j, v in ipairs(shizhuang_list) do
			local data = {}
			data.part_type = j
			data.used_index = v

			if v ~= 0 then
				local level_cfg = NewAppearanceWGData.Instance:GetFashionUpLevelCfg(j, v)
				data.stuff_id = level_cfg.stuff_id
			end
			
			scheme_data[i][j] = data
		end
	end

	return scheme_data
end

-- 获取当前时装的所有属性
function WardrobeWGData:GetShiZhuangForgeAttrList(part_type, index)
	local attr_list = {}
	local sort_list = {}

	local forge_data = self:GetShizhuangForgeInfoByPartType(part_type, index)
	local grade_level = forge_data and forge_data.grade_level or 0
	local next_grade_level = grade_level + 1
	-- 组装阶段属性
	local grade_cfg = self:GetForgeGradeCfgByLevel(grade_level)
	local next_grade_cfg = self:GetForgeGradeCfgByLevel(next_grade_level)
	self:AssembleShiZhuangForgeAttr(grade_cfg, next_grade_cfg, attr_list, 1, 1, 5)
	-- 组装星级属性
	local star = forge_data and forge_data.star or 0
	local next_star = star + 1
	local star_cfg = self:GetForgeStarCfgByStar(star)
	local next_star_cfg = self:GetForgeStarCfgByStar(next_star)
	self:AssembleShiZhuangForgeAttr(star_cfg, next_star_cfg, attr_list, 1, 1, 5)

	local per = grade_cfg and grade_cfg.add_stone_per or 10000
	local per_value = per / 10000

	-- 先拿到当前已获得属性
	if grade_level > 0 then
		for i = 0, grade_level - 1 do
			local temp_grade_cfg = self:GetForgeGradeCfgByLevel(i)
			local stone_quality = temp_grade_cfg and temp_grade_cfg.stone_quality or 0

			for j = 0, WARDROBE_NEW_PART_TYPE.MAX_STONE_PART_COUNT do
				local stone_cfg = self:GetStoneCfgByQualityPart(stone_quality, j)
				self:AssembleShiZhuangForgeAttr(stone_cfg, nil, attr_list, per_value, 1, 5)
			end
		end
	end

	if forge_data then
		for j = 0, WARDROBE_NEW_PART_TYPE.MAX_STONE_PART_COUNT do
			local stone_seq = forge_data.stone_part_list and forge_data.stone_part_list[j] or -1
			if stone_seq ~= -1 then
				local stone_cfg = self:GetStoneCfgBySeq(stone_seq)
				self:AssembleShiZhuangForgeAttr(stone_cfg, nil, attr_list, per_value, 1, 5)
			end
		end
	end

	for _, attr_data in pairs(attr_list) do
		if attr_data and attr_data.attr_str and attr_data.attr_str ~= 0 then
			table.insert(sort_list, attr_data)
		end
	end

	table.sort(sort_list, function (a, b)
		return a.attr_str < b.attr_str
	end)
	
	return sort_list
end

function WardrobeWGData:AssembleShiZhuangForgeAttr(cfg, next_cfg, attr_list, per, star_index, end_index)
	-- 获取阶级属性
	for i = star_index, end_index do
		local key = cfg["attr_id" .. i]
		if key then
			local attr_value = cfg["attr_value" .. i]

			if attr_list[key] then
				attr_list[key].attr_value = attr_list[key].attr_value + math.floor(attr_value * per)
			else
				attr_list[key] = {}
				attr_list[key].attr_str = key
				attr_list[key].attr_value = (attr_value * per)
			end

			if next_cfg and next_cfg["attr_id" .. i] then
				local gap_value = next_cfg["attr_value" .. i] - cfg["attr_value" .. i]
				gap_value = math.floor(gap_value * per)

				if attr_list[key].add_value then
					attr_list[key].add_value = attr_list[key].add_value + gap_value 
				else
					attr_list[key].add_value = gap_value
				end
			end
		end
	end
end

-- 特殊职业加-30°
function WardrobeWGData:GetSpecialWardrobeOffsetBySexProf()
	local prof = RoleWGData.Instance:GetRoleProf()
	local sex = RoleWGData.Instance:GetRoleSex()

	if sex == 0 and prof == 2 then
		return 0
	end

	return 30
end

------------------------------------新衣橱 end-------------------------------------