require("game/long_zhu/long_zhu_wg_data")
require("game/long_zhu/long_zhu_view")
require("game/long_zhu/long_zhu_tip")

LongZhuWGCtrl = LongZhuWGCtrl or BaseClass(BaseWGCtrl)

function LongZhuWGCtrl:__init()
	if LongZhuWGCtrl.Instance then
		error("[LongZhuWGCtrl]:Attempt to create singleton twice!")
		return
	end
	LongZhuWGCtrl.Instance = self

	self.data = LongZhuWGData.New()
	-- self.view = LongZhuView.New(GuideModuleName.LongZhuView)
	self.tip_view = LongZhuTip.New()

	self.skill_old_level = 0
	
	self:RegisterAllProtocals()

	self:BindGlobalEvent(OtherEventType.PASS_DAY2, BindTool.Bind1(self.DayChangeOrOpen, self))
end

function LongZhuWGCtrl:__delete()
	-- self.view:DeleteMe()
	-- self.view = nil

	self.data:DeleteMe()
	self.data = nil

	self.tip_view:DeleteMe()
	self.tip_view = nil

	LongZhuWGCtrl.Instance = nil
end

function LongZhuWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(CSLongZhuOper)
	self:RegisterProtocol(SCLongZhuInfo, "OnSCLongZhuInfo")
end

function LongZhuWGCtrl:OnSCLongZhuInfo(protocol)
	self.data:SetLongZhuInfo(protocol)
	-- if self.view:IsOpen() then
	-- 	self.view:Flush(0, "long_zhu_info")
	-- end
	if self.tip_view:IsOpen() then
		self.tip_view:Flush(0)
	end
	-- RoleBagWGCtrl.Instance:Flush(TabIndex.rolebag_longzhu, "long_zhu")
	RoleBagWGCtrl.Instance:Flush(TabIndex.rolebag_longzhu,"long_zhu_info")
	MainuiWGCtrl.Instance:FlushView(0, "long_zhu")
	RemindManager.Instance:Fire(RemindName.LongZhu)
	self:CheckSkillLevelUpTip()
end

function LongZhuWGCtrl:SendLongZhuOper(oper_type, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSLongZhuOper)
	protocol.oper_type = oper_type or 0
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

function LongZhuWGCtrl:DayChangeOrOpen()
	self:SendLongZhuOper(LONGZHU_OPER_TYPE.LONGZHU_OPER_TYPE_ALL_INFO)
end

function LongZhuWGCtrl:OpenLongZhuTip()
	self.tip_view:Open()
end

function LongZhuWGCtrl:CloseLongZhuTip()
	self.tip_view:Close()
end

function LongZhuWGCtrl:CheckSkillLevelUpTip()
	local longzhu_level = self.data:GetLongZhuSkillLevel()
	if self.skill_old_level <= 0 then
		self.skill_old_level = longzhu_level
	elseif longzhu_level > self.skill_old_level then
		local desc_cfg_list = self.data:GetLongZhuDescCfgList()
		local desc_cfg = nil
		for i=1,#desc_cfg_list do
			if desc_cfg_list[i].active_skill_level == longzhu_level then
				desc_cfg = desc_cfg_list[i]
				break
			end
		end
		if desc_cfg then
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Role.SkillUpgradeHint, desc_cfg.skill_name, desc_cfg.skill_level))
		end
		self.skill_old_level = longzhu_level
	end
end

-- 触发元神变身效果
function LongZhuWGCtrl:OnLongZhuSkillEffect()
	MainuiWGCtrl.Instance:SetBeastBuffIcon(false)
	AddDelayCall(self, function ()
		MainuiWGCtrl.Instance:PlayBeastBuffAni("a3_longzhu_effect")
	end, 0.5)
end