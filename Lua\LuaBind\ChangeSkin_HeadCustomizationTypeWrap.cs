﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class ChangeSkin_HeadCustomizationTypeWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>gin<PERSON>(typeof(ChangeSkin.HeadCustomizationType));
		<PERSON><PERSON>("EyeSize", get_EyeSize, null);
		<PERSON><PERSON>("EyePosition", get_EyePosition, null);
		<PERSON><PERSON>("EyeAngle", get_EyeAngle, null);
		<PERSON><PERSON>("EyeClose", get_EyeClose, null);
		<PERSON><PERSON>("EyebrowAngle", get_EyebrowAngle, null);
		<PERSON><PERSON>("EyeShadowColor", get_EyeShadowColor, null);
		<PERSON><PERSON>("EyeballPosition", get_EyeballPosition, null);
		<PERSON><PERSON>("PupilType_Left", get_PupilType_Left, null);
		<PERSON><PERSON>("IrisSize_Left", get_IrisSize_Left, null);
		<PERSON><PERSON>("PupilSize_Left", get_PupilSize_Left, null);
		<PERSON><PERSON>("PupilColor_Left", get_PupilColor_Left, null);
		<PERSON><PERSON>("PupilType_Right", get_PupilType_Right, null);
		L.RegVar("IrisSize_Right", get_IrisSize_Right, null);
		L.RegVar("PupilSize_Right", get_PupilSize_Right, null);
		L.RegVar("PupilColor_Right", get_PupilColor_Right, null);
		L.RegVar("NoseSize", get_NoseSize, null);
		L.RegVar("NoseAngle", get_NoseAngle, null);
		L.RegVar("MouthSize", get_MouthSize, null);
		L.RegVar("MouthPosition", get_MouthPosition, null);
		L.RegVar("MouthAngle", get_MouthAngle, null);
		L.RegVar("MouthColor", get_MouthColor, null);
		L.RegVar("CheekSize", get_CheekSize, null);
		L.RegVar("ChinLength", get_ChinLength, null);
		L.RegVar("FaceDecalTex", get_FaceDecalTex, null);
		L.RegVar("FaceDecalTexST", get_FaceDecalTexST, null);
		L.RegVar("FaceDecalFade", get_FaceDecalFade, null);
		L.RegVar("FaceDecalMirror", get_FaceDecalMirror, null);
		L.RegVar("HairColor", get_HairColor, null);
		L.RegVar("FaceDecalColor", get_FaceDecalColor, null);
		L.RegVar("FaceBaseTex", get_FaceBaseTex, null);
		L.RegVar("BodyBaseColor", get_BodyBaseColor, null);
		L.RegFunction("IntToEnum", IntToEnum);
		L.EndEnum();
		TypeTraits<ChangeSkin.HeadCustomizationType>.Check = CheckType;
		StackTraits<ChangeSkin.HeadCustomizationType>.Push = Push;
	}

	static void Push(IntPtr L, ChangeSkin.HeadCustomizationType arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(ChangeSkin.HeadCustomizationType), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EyeSize(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.EyeSize);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EyePosition(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.EyePosition);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EyeAngle(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.EyeAngle);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EyeClose(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.EyeClose);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EyebrowAngle(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.EyebrowAngle);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EyeShadowColor(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.EyeShadowColor);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EyeballPosition(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.EyeballPosition);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PupilType_Left(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.PupilType_Left);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IrisSize_Left(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.IrisSize_Left);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PupilSize_Left(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.PupilSize_Left);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PupilColor_Left(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.PupilColor_Left);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PupilType_Right(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.PupilType_Right);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IrisSize_Right(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.IrisSize_Right);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PupilSize_Right(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.PupilSize_Right);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PupilColor_Right(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.PupilColor_Right);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_NoseSize(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.NoseSize);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_NoseAngle(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.NoseAngle);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MouthSize(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.MouthSize);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MouthPosition(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.MouthPosition);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MouthAngle(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.MouthAngle);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MouthColor(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.MouthColor);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CheekSize(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.CheekSize);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ChinLength(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.ChinLength);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_FaceDecalTex(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.FaceDecalTex);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_FaceDecalTexST(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.FaceDecalTexST);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_FaceDecalFade(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.FaceDecalFade);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_FaceDecalMirror(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.FaceDecalMirror);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_HairColor(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.HairColor);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_FaceDecalColor(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.FaceDecalColor);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_FaceBaseTex(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.FaceBaseTex);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_BodyBaseColor(IntPtr L)
	{
		ToLua.Push(L, ChangeSkin.HeadCustomizationType.BodyBaseColor);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		ChangeSkin.HeadCustomizationType o = (ChangeSkin.HeadCustomizationType)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

