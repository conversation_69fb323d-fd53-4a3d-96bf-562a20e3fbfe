ActivityLimitBuyView = ActivityLimitBuyView or BaseClass(SafeBaseView)

function ActivityLimitBuyView:__init()
    self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
    self:SetMaskBg()

    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
    self:AddViewResource(0, "uis/view/chaotic_purchase_ui_prefab", "layout_activity_limit_buy")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function ActivityLimitBuyView:__delete()
end

function ActivityLimitBuyView:ReleaseCallBack()
    if self.reward_item_list then
        for _, v in pairs(self.reward_item_list) do
            v:DeleteMe()
        end
        self.reward_item_list = nil
    end

    if self.select_reward_list then
        self.select_reward_list:DeleteMe()
        self.select_reward_list = nil
    end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
	end

    if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

    if CountDownManager.Instance:HasCountDown("act_limit_buy_time") then
        CountDownManager.Instance:RemoveCountDown("act_limit_buy_time")
    end

    self.select_page_index = 0
end

function ActivityLimitBuyView:OpenCallBack()
    ActivityLimitBuyWGCtrl.Instance:ReqActivityLimitBuyInfo(OA_LIMIT_RMB_BUY_OPERATE_TYPE.INFO)
end

function ActivityLimitBuyView:LoadCallBack()
	local bundle, asset = ResPath.GetRawImagesJPG("a3_sxth_bj")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
	self.node_list.title_view_name.text.text = Language.ChaoticPurchase.ViewName

	self:InitMoneyBar()

    self.select_page_index = 0

    self.node_list["show_text"].tmp.text = Language.ChaoticPurchase.ShowText

    if not self.reward_item_list then
        self.reward_item_list = {}
        for i = 1, ActivityLimitBuyWGData.Max_Page_Count do
            self.reward_item_list[i] = ActivityLimitRewardItem.New(self.node_list["reward_item_" .. i])
            self.reward_item_list[i]:SetIndex(i)
            self.reward_item_list[i]:AddClickEventListener(BindTool.Bind(self.OnClickPage, self))
        end
    end

    if not self.select_reward_list then
        self.select_reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
    end

    if not self.model_display then
        self.model_display = OperationActRender.New(self.node_list.model_display)
        self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

    XUI.AddClickEventListener(self.node_list.buy_btn, BindTool.Bind(self.OnClickBuy, self))
end

function ActivityLimitBuyView:ShowIndexCallBack()
	ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.ActivityLimitBuyView, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_BUY)
end

function ActivityLimitBuyView:OnFlush(param_t, index)
    local data_list = ActivityLimitBuyWGData.Instance:GetCurDayBuyList()
    if IsEmptyTable(data_list) then
        return
    end

    table.sort(data_list, SortTools.KeyLowerSorters("sort_index", "seq"))
    local reward_list = self.reward_item_list
    for i = 1, #reward_list do
        reward_list[i]:SetData(data_list[i])
    end

    if self.select_page_index <= 0 then
        local select_index = 1
        for i, v in ipairs(data_list) do
            local all_buy = ActivityLimitBuyWGData.Instance:GetBuyStateBySeq(v)
            if not all_buy then
                select_index = i
                break
            end
        end

        self:OnClickPage(reward_list[select_index])
    end

    self:FlushModel()
    self:FlushCapStr()
    self:FlushRewardPanel()
    self:FlushTimeCount()
end

function ActivityLimitBuyView:InitMoneyBar()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end

function ActivityLimitBuyView:OnClickPage(page)
    if self.select_page_index == page:GetIndex() then
        return
    end

    self.select_page_index = page:GetIndex()

    for _, v in pairs(self.reward_item_list) do
        v:SetSelect(v.index == self.select_page_index)
    end

    self:FlushModel()
    self:FlushCapStr()
    self:FlushRewardPanel()
end

function ActivityLimitBuyView:FlushRewardPanel()
    local data = ActivityLimitBuyWGData.Instance:GetCurDayBuyInfoBySeq(self.select_page_index)
    if IsEmptyTable(data) then
        return
    end
    self.select_reward_list:SetDataList(data.reward_item)

    local already_buy_times = ActivityLimitBuyWGData.Instance:GetBuyCountBySeq(data.seq)
    self.node_list.limit_buy_text.tmp.text = string.format(Language.ChaoticPurchase.BuyTimesStr2, data.buy_times)
    local price_str = RoleWGData.GetPayMoneyStr(data.rmb_price, data.rmb_type, data.rmb_seq)
    self.node_list.buy_btn_text.tmp.text = string.format(Language.ChaoticPurchase.BuyText, price_str)

    self.node_list.not_buy_flag:SetActive(data.buy_times <= already_buy_times)
    self.node_list.buy_btn:SetActive(data.buy_times > already_buy_times)
end

function ActivityLimitBuyView:FlushModel()
    local shop_info = ActivityLimitBuyWGData.Instance:GetCurDayBuyInfoBySeq(self.select_page_index)
	if IsEmptyTable(shop_info) then
		return
	end
	
	local display_data = {}
	display_data.should_ani = true
	if shop_info.model_show_itemid ~= 0 and shop_info.model_show_itemid ~= "" then
		local split_list = string.split(shop_info.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = shop_info.model_show_itemid
		end
	end
	
	display_data.bundle_name = shop_info["model_bundle_name"]
    display_data.asset_name = shop_info["model_asset_name"]
    local model_show_type = tonumber(shop_info["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1

	if shop_info.model_pos and shop_info.model_pos ~= "" then
		local pos_list = string.split(shop_info.model_pos, "|")
		local pos_x = tonumber(pos_list[1]) or 0
		local pos_y = tonumber(pos_list[2]) or 0
		local pos_z = tonumber(pos_list[3]) or 0

		display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
	end

	if shop_info.model_rot and shop_info.model_rot ~= "" then
		local rot_list = string.split(shop_info.model_rot, "|")
		local rot_x = tonumber(rot_list[1]) or 0
		local rot_y = tonumber(rot_list[2]) or 0
		local rot_z = tonumber(rot_list[3]) or 0

		display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
	end

	if shop_info.model_scale and shop_info.model_scale ~= "" then
		display_data.model_adjust_root_local_scale = shop_info.model_scale
	end

	display_data.model_rt_type = ModelRTSCaleType.L
    self.model_display:SetData(display_data)

	self.node_list.model_name.tmp.text = shop_info.name
end

function ActivityLimitBuyView:FlushCapStr()
    local shop_info = ActivityLimitBuyWGData.Instance:GetCurDayBuyInfoBySeq(self.select_page_index)
	if IsEmptyTable(shop_info) then
		return
	end

	local capability = 0
	local item_data = SortDataByItemColor(shop_info.reward_item)
	for k, v in pairs(item_data) do
		local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		if item_cfg then
			capability = capability + ItemShowWGData.CalculateCapability(v.item_id)
		end
	end

	self.node_list.cap_value.text.text = capability
end

function ActivityLimitBuyView:FlushTimeCount()
    --local time, next_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_BUY)
    local time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
    if time > 0 then
        if CountDownManager.Instance:HasCountDown("act_limit_buy_time") then
            CountDownManager.Instance:RemoveCountDown("act_limit_buy_time")
        end

        CountDownManager.Instance:AddCountDown("act_limit_buy_time", 
            BindTool.Bind(self.FinalUpdateTimeCallBack, self), 
            BindTool.Bind(self.OnComplete, self), 
            nil, time, 1)
    else
        self:OnComplete()
    end
end

function ActivityLimitBuyView:FinalUpdateTimeCallBack(now_time, total_time)
    local time = math.ceil(total_time - now_time)
    local time_str = TimeUtil.FormatSecondDHM8(time)
    self.node_list["act_time"].tmp.text = string.format(Language.ChaoticPurchase.ActivityTime, time_str) 
end

function ActivityLimitBuyView:OnComplete()
    self.node_list.act_time.tmp.text = ""
end

function ActivityLimitBuyView:OnClickBuy()
    local data = ActivityLimitBuyWGData.Instance:GetCurDayBuyInfoBySeq(self.select_page_index)
    if IsEmptyTable(data) then
        return
    end

    local already_buy_times = ActivityLimitBuyWGData.Instance:GetBuyCountBySeq(data.seq)
    if already_buy_times >= data.buy_times then
        TipWGCtrl.Instance:ShowSystemMsg(Language.ChaoticPurchase.AllBuy)
        return
    end

    RechargeWGCtrl.Instance:Recharge(data.rmb_price, data.rmb_type, data.rmb_seq)
end

--------------------------------------------------------------------------------------
ActivityLimitRewardItem = ActivityLimitRewardItem or BaseClass(BaseRender)

function ActivityLimitRewardItem:LoadCallBack()

end

function ActivityLimitRewardItem:OnFlush()
    local already_buy_times = ActivityLimitBuyWGData.Instance:GetBuyCountBySeq(self.data.seq)
    self.node_list.not_buy_flag:SetActive(self.data.buy_times <= already_buy_times)

    local flag_str = self.data.jiaobiao_text
    self.node_list.flag:SetActive(nil ~= flag_str and "" ~= flag_str)
    self.node_list.flag_text.tmp.text = flag_str or ""

    local bundle, asset = ResPath.GetLimitBuyImg(self.data.icon)
    self.node_list.show_image.image:LoadSprite(bundle, asset, function()
        self.node_list.show_image.image:SetNativeSize()
    end)
end

function ActivityLimitRewardItem:OnSelectChange(is_select)
    self.node_list.select:SetActive(is_select)
end