TeamInviteAlert = TeamInviteAlert or BaseClass(Alert)

function TeamInviteAlert:SetDataAndOpen(data)
    if not self.target_data_list then
        self.target_data_list = {}
    end

    table.insert(self.target_data_list, data)

    if not self:IsOpen() then
        self:Open()
        self:ResetPanel()
    end
end

function TeamInviteAlert:ResetPanel()
    local data = table.remove(self.target_data_list, 1)

    self:SetLableString(data.lable_str)
    self:SetOkString(data.ok_str)
	self:SetOkFunc(data.ok_func)
	self:SetCancelFunc(data.cancel_func)
	self:SetCountDownFunc(20, nil, data.countdown_func)
end

function TeamInviteAlert:Close()
    if self.target_data_list and #self.target_data_list > 0 then
        self:ResetPanel()
        return
    end

    if self.close_type == -1 then
		self.close_type = 2
	end

	SafeBaseView.Close(self)
end