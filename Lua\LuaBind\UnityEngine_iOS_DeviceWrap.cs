﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

#if UNITY_IOS
public class UnityEngine_iOS_DeviceWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.iOS.Device), typeof(System.Object));
		<PERSON><PERSON>unction("SetNoBackupFlag", SetNoBackupFlag);
		<PERSON><PERSON>Function("ResetNoBackupFlag", ResetNoBackupFlag);
		<PERSON><PERSON>unction("RequestStoreReview", RequestStoreReview);
		<PERSON><PERSON>Function("New", _CreateUnityEngine_iOS_Device);
		<PERSON><PERSON>Function("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("systemVersion", get_systemVersion, null);
		L.Reg<PERSON>ar("generation", get_generation, null);
		<PERSON>.Reg<PERSON>ar("vendorIdentifier", get_vendorIdentifier, null);
		<PERSON><PERSON>("advertisingIdentifier", get_advertisingIdentifier, null);
		<PERSON><PERSON>("advertisingTrackingEnabled", get_advertisingTrackingEnabled, null);
		<PERSON><PERSON>("hideHomeButton", get_hideHomeButton, set_hideHomeButton);
		L.RegVar("lowPowerModeEnabled", get_lowPowerModeEnabled, null);
		L.RegVar("wantsSoftwareDimming", get_wantsSoftwareDimming, set_wantsSoftwareDimming);
		L.RegVar("iosAppOnMac", get_iosAppOnMac, null);
		L.RegVar("deferSystemGesturesMode", get_deferSystemGesturesMode, set_deferSystemGesturesMode);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateUnityEngine_iOS_Device(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				UnityEngine.iOS.Device obj = new UnityEngine.iOS.Device();
				ToLua.PushSealed(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: UnityEngine.iOS.Device.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetNoBackupFlag(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			UnityEngine.iOS.Device.SetNoBackupFlag(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResetNoBackupFlag(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			UnityEngine.iOS.Device.ResetNoBackupFlag(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RequestStoreReview(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = UnityEngine.iOS.Device.RequestStoreReview();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_systemVersion(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, UnityEngine.iOS.Device.systemVersion);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_generation(IntPtr L)
	{
		try
		{
			ToLua.Push(L, UnityEngine.iOS.Device.generation);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_vendorIdentifier(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, UnityEngine.iOS.Device.vendorIdentifier);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_advertisingIdentifier(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, UnityEngine.iOS.Device.advertisingIdentifier);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_advertisingTrackingEnabled(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, UnityEngine.iOS.Device.advertisingTrackingEnabled);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_hideHomeButton(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, UnityEngine.iOS.Device.hideHomeButton);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_lowPowerModeEnabled(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, UnityEngine.iOS.Device.lowPowerModeEnabled);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_wantsSoftwareDimming(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, UnityEngine.iOS.Device.wantsSoftwareDimming);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iosAppOnMac(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, UnityEngine.iOS.Device.iosAppOnMac);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_deferSystemGesturesMode(IntPtr L)
	{
		try
		{
			ToLua.Push(L, UnityEngine.iOS.Device.deferSystemGesturesMode);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_hideHomeButton(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			UnityEngine.iOS.Device.hideHomeButton = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_wantsSoftwareDimming(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			UnityEngine.iOS.Device.wantsSoftwareDimming = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_deferSystemGesturesMode(IntPtr L)
	{
		try
		{
			UnityEngine.iOS.SystemGestureDeferMode arg0 = (UnityEngine.iOS.SystemGestureDeferMode)ToLua.CheckObject(L, 2, typeof(UnityEngine.iOS.SystemGestureDeferMode));
			UnityEngine.iOS.Device.deferSystemGesturesMode = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

#endif
