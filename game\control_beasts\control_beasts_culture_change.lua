-- 灵兽培养灵兽转换资质
ControlBeastsCultureChange = ControlBeastsCultureChange or BaseClass(SafeBaseView)

function ControlBeastsCultureChange:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(830, 590)})
    self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_beasts_root_culture_change")
    self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_beasts_effect_center_view")
    self:SetMaskBg(true)
end

function ControlBeastsCultureChange:ReleaseCallBack()
    self.beast_bag_id = nil
    self.select_data = nil
    self.need_arrange = nil

    if self.left_show_item then
        self.left_show_item:DeleteMe()
        self.left_show_item = nil
    end
    
    if self.spend_item then
        self.spend_item:DeleteMe()
        self.spend_item = nil
    end

    if self.alert_tip then
		self.alert_tip:DeleteMe()
		self.alert_tip = nil
	end	

    if self.left_flair_attrlist and #self.left_flair_attrlist > 0 then
		for _, flair__cell in ipairs(self.left_flair_attrlist) do
			flair__cell:DeleteMe()
			flair__cell = nil
		end

		self.left_flair_attrlist = nil
	end

    if self.right_flair_attrlist and #self.right_flair_attrlist > 0 then
		for _, flair__cell in ipairs(self.right_flair_attrlist) do
			flair__cell:DeleteMe()
			flair__cell = nil
		end

		self.right_flair_attrlist = nil
	end

    self:CleanChangeEffectCDTimer()
end

function ControlBeastsCultureChange:SetSelectData(beast_bag_id)
    self.beast_bag_id = beast_bag_id
end

function ControlBeastsCultureChange:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.ContralBeasts.TitleName7

    -- 资质属性
    if self.left_flair_attrlist == nil then
        self.left_flair_attrlist = {}
        for i = 1, 5 do
            local attr_obj = self.node_list.left_flair_attr:FindObj(string.format("attr_%d", i))
            if attr_obj then
                local cell = BeststChangeItemRender.New(attr_obj)
                cell:SetIndex(i)
                self.left_flair_attrlist[i] = cell
            end
        end
    end

    -- 资质属性
    if self.right_flair_attrlist == nil then
        self.right_flair_attrlist = {}
        for i = 1, 5 do
            local attr_obj = self.node_list.right_flair_attr:FindObj(string.format("attr_%d", i))
            if attr_obj then
                local cell = BeststChangeItemRender.New(attr_obj)
                cell:SetIndex(i)
                self.right_flair_attrlist[i] = cell
            end
        end
    end

    if not self.left_show_item then
        self.left_show_item = BeststsSwallowRender.New(self.node_list.left_item_cell)
    end

    if not self.spend_item then
        self.spend_item = BeststsSwallowRender.New(self.node_list.right_item_cell)
        self.spend_item:SetClickCallBack(BindTool.Bind1(self.OnClickSelectBestst, self))
    end

    self.node_list["btn_OK"].button:AddClickListener(BindTool.Bind(self.OnClickChange, self))
end

function ControlBeastsCultureChange:CloseCallBack()
    -- 关闭的时候有协议变更需要整理背包
    -- if self.need_arrange then
    --     ControlBeastsWGCtrl.Instance:SendOperateTypeSortBeastBag()
    -- end

    self.select_data = nil
end

function ControlBeastsCultureChange:OnClickSelectBestst()
    if not self.beast_bag_id then
        return
    end

    local left_show_data = ControlBeastsWGData.Instance:GetBeastDataById(self.beast_bag_id)
    if left_show_data and left_show_data.server_data then
        local server_data = left_show_data.server_data
        local show_data = {}
        show_data.aim_beast_id = server_data.beast_id
        show_data.aim_bag_id = self.beast_bag_id
        show_data.special_list = {self.select_data}
        show_data.same_list = nil
        show_data.is_special = false
        show_data.is_all = false
        show_data.need_num = 1

        ControlBeastsWGCtrl.Instance:SetSelectSwallowSelectOkCallBack(BindTool.Bind1(self.OnBatchOkCallBack, self))
        ControlBeastsWGCtrl.Instance:OpenBeastsSwallowSelectView(show_data)
    end
end

-- 材料选择回调
function ControlBeastsCultureChange:OnBatchOkCallBack(is_special, select_list)
    if select_list and #select_list > 0 then
        self.select_data = select_list[1]

        self:FlushLeftMessage()
        self:FlushRightMessage()
    end
end

function ControlBeastsCultureChange:OnFlush()
    self:FlushLeftMessage()
    self:FlushRightMessage()
end

function ControlBeastsCultureChange:FlushLeftMessage()
    if not self.beast_bag_id then
        return
    end

    local left_show_data = ControlBeastsWGData.Instance:GetBeastDataById(self.beast_bag_id)
    if left_show_data then
        self.left_show_item:SetData(left_show_data)

        local server_data = left_show_data.server_data
        if server_data then
            -- local beast_skill_num = 0

            -- if server_data.skill_ids then
            --     for i = 1, BEAST_DEFINE.BEAST_PASSIVE_SKILL_COUNT_MAX do
            --         if server_data.skill_ids[i] ~= -1 then
            --             beast_skill_num = beast_skill_num + 1
            --         end
            --     end
            -- end

            self.node_list.left_item_level.text.text = string.format(Language.Rank.Level, ToColorStr(server_data.beast_level, COLOR3B.GREEN)) 

            local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
            if beast_cfg then
                self.node_list.left_desc.text.text = beast_cfg.beast_name
            end

            local beast_map_data = ControlBeastsWGData.Instance:GetBeastDatMapaById(server_data.beast_id)
            local beast_flair_preview = nil
            if beast_map_data and beast_map_data.beast_flair_preview then
                beast_flair_preview = beast_map_data.beast_flair_preview
            end

            for index, bag_flair_cell in ipairs(self.left_flair_attrlist) do
                local temp_data = {}
                temp_data.beast_preview_value = beast_flair_preview and beast_flair_preview[index] and beast_flair_preview[index].max or 0

                if server_data.flair_values[index] then
                    temp_data.curr_vaule = server_data.flair_values[index]

                    if self.select_data and self.select_data.beast_data and self.select_data.beast_data.server_data then
                        local select_server_data = self.select_data.beast_data.server_data
                        temp_data.compare_value = select_server_data.flair_values[index]
                    end

                    bag_flair_cell:SetData(temp_data)
                else
                    temp_data.curr_vaule = server_data.effort_value / 10000

                    if self.select_data and self.select_data.beast_data and self.select_data.beast_data.server_data then
                        local select_server_data = self.select_data.beast_data.server_data
                        temp_data.compare_value = select_server_data.effort_value / 10000
                    end

                    bag_flair_cell:SetData(temp_data)
                end
            end
        end
    end
end

function ControlBeastsCultureChange:FlushRightMessage()
    self.node_list.right_item_no_data:CustomSetActive(self.select_data == nil)
    self.node_list.message_root:CustomSetActive(self.select_data ~= nil)
    self.node_list.btn_OK:CustomSetActive(self.select_data ~= nil)
    self.node_list.desc_bg:CustomSetActive(self.select_data ~= nil)
    
    if self.select_data then
        self.spend_item:SetData(self.select_data)
        local beast_data = self.select_data.beast_data
        if beast_data and beast_data.server_data then
            local server_data = beast_data.server_data
            if server_data then
                -- local beast_skill_num = 0
    
                -- if server_data.skill_ids then
                --     for i = 1, BEAST_DEFINE.BEAST_PASSIVE_SKILL_COUNT_MAX do
                --         if server_data.skill_ids[i] ~= -1 then
                --             beast_skill_num = beast_skill_num + 1
                --         end
                --     end
                -- end
    
                self.node_list.right_item_level.text.text = string.format(Language.Rank.Level, ToColorStr(server_data.beast_level, COLOR3B.GREEN)) 

                local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
                if beast_cfg then
                    self.node_list.right_desc.text.text = beast_cfg.beast_name
                end

                local beast_map_data = ControlBeastsWGData.Instance:GetBeastDatMapaById(server_data.beast_id)
                local beast_flair_preview = nil
                if beast_map_data and beast_map_data.beast_flair_preview then
                    beast_flair_preview = beast_map_data.beast_flair_preview
                end

                for index, bag_flair_cell in ipairs(self.right_flair_attrlist) do
                    local temp_data = {}
                    temp_data.beast_preview_value = beast_flair_preview and beast_flair_preview[index] and beast_flair_preview[index].max or 0

                    if server_data.flair_values[index] then
                        temp_data.curr_vaule = server_data.flair_values[index]
                        bag_flair_cell:SetData(temp_data)
                    else
                        temp_data.curr_vaule = server_data.effort_value / 10000
                        bag_flair_cell:SetData(temp_data)
                    end
                end
            end
        end
    else
        self.spend_item:SetData({})
    end
end


-- 延迟播放受击
function ControlBeastsCultureChange:CleanChangeEffectCDTimer()
    if self.beast_culture_change_timer then
        GlobalTimerQuest:CancelQuest(self.beast_culture_change_timer)
        self.beast_culture_change_timer = nil
    end
end

-- 展示特效
function ControlBeastsCultureChange:ShowChangeSuccessEffect()
    self:CleanChangeEffectCDTimer()
    self.node_list.change_effect_root:CustomSetActive(true)
    -- 延时2秒关闭这个
    self.beast_group_skill_hit_timer = GlobalTimerQuest:AddDelayTimer(function ()
        self.node_list.change_effect_root:CustomSetActive(false)
    end, 2)
end

function ControlBeastsCultureChange:OnClickChange()
    if not self.beast_bag_id then
        return
    end

    if self.select_data then
        local execute_change = function()
            local beast_list = {}
            local beast_data = self.select_data.beast_data
            if beast_data then
                table.insert(beast_list, beast_data.bag_id) 
                self.need_arrange = true
                ControlBeastsWGCtrl.Instance:SendCSRoleBeastChangeFlair(self.beast_bag_id, beast_list)
                self.select_data = nil
            end
        end

        local is_need_alert = false
        local beast_data = self.select_data.beast_data
        local data = ControlBeastsWGData.Instance:GetBeastDataById(beast_data.bag_id)
        local server_data = data.server_data
        if server_data then
            local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
            is_need_alert = beast_cfg and beast_cfg.beast_color >= GameEnum.ITEM_COLOR_ORANGE
        end

        if is_need_alert then
            -- 弹出二次确认提示框
            if self.alert_tip == nil then
                self.alert_tip = Alert.New()
            end
            self.alert_tip:SetLableString(Language.ContralBeasts.SelectTips4)
            self.alert_tip:SetOkFunc(execute_change)
            self.alert_tip:Open()
        else
            execute_change()
        end
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip14)
    end
end

-- 升级成功或突破成功特效
function ControlBeastsCultureChange:BeastOperateFinalEffect(ui_effect_type, is_center)
	local node_root = self.node_list["operate_effect_root"]
	if is_center then
		node_root = self.node_list["layout_a2_common_top_panel"]
	end
	TipWGCtrl.Instance:ShowEffect({effect_type = ui_effect_type,
						is_success = true, pos = Vector2(0, 0), parent_node = node_root})

	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
end



----------------------------------灵兽资质转换item-----------------------
BeststChangeItemRender = BeststChangeItemRender or BaseClass(BaseRender)
function BeststChangeItemRender:OnFlush()
    if not self.data then
        return
    end

    -- 获取资质加成属性数据
    local Flair_data = ControlBeastsWGData.Instance:GetFlairAttrDataById(self.index - 1)
    if Flair_data then
        self.node_list.attr_name.text.text = Flair_data.flair_name
    else
        self.node_list.attr_name.text.text = Language.ContralBeasts.SelectError4
    end


    local integer_value, decimal_value = math.modf(self.data.curr_vaule)
    local real_value = self.data.curr_vaule
    if decimal_value > 0 then
        real_value = string.format("%.3f", self.data.curr_vaule) 
    end
    self.node_list.attr_value.text.text = real_value

    local proportion_value = self.data.curr_vaule / self.data.beast_preview_value
    local proportion_num = 1
    if proportion_value > 0.3 and proportion_value < 0.7 then
        proportion_num = 2
    elseif proportion_value > 0.7 then
        proportion_num = 5 
    end

    local fill_str = string.format("a3_hs_jd_%d", proportion_num)

    if self.node_list.attr_progress then
        self.node_list.fill_image.image:LoadSprite(ResPath.GetControlBeastsImg(fill_str))
        self.node_list.attr_progress.slider.value = proportion_value
    end

    if self.node_list.arrow_down then
        if self.data.compare_value then
            self.node_list.arrow_down:CustomSetActive(self.data.curr_vaule < self.data.compare_value)
        else
            self.node_list.arrow_down:CustomSetActive(false)
        end
    end

    if self.node_list.arrow_up then
        if self.data.compare_value then
            self.node_list.arrow_up:CustomSetActive(self.data.curr_vaule > self.data.compare_value)
        else
            self.node_list.arrow_up:CustomSetActive(false)
        end
    end
end