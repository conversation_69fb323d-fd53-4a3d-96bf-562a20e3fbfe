LuckyGiftBagComfirmView = LuckyGiftBagComfirmView or BaseClass(SafeBaseView)

function LuckyGiftBagComfirmView:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(1, 12), sizeDelta = Vector2(632,382)})
    self:AddViewResource(0, "uis/view/lucky_gift_bag_ui_prefab", "lucky_gift_bag_comfirm_view")
	self:SetMaskBg(true, true)
end

function LuckyGiftBagComfirmView:ReleaseCallBack()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function LuckyGiftBagComfirmView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.LuckyGiftBag.ConfirmTitle
    self.node_list.giftinfo_tips.text.text = Language.LuckyGiftBag.GiftinfoTips
    if nil ==  self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
    end

    for i = 1, 3 do
        if self.node_list["btn_buy_type" .. i] then
            XUI.AddClickEventListener(self.node_list["btn_buy_type" .. i], BindTool.Bind(self.OnBuyBtnClick, self, i))
        end
    end
end

function LuckyGiftBagComfirmView:OnFlush()
    local grade, seq, gift_name = LuckyGiftBagWgData.Instance:GetCurrentSelectInfo()
    self.node_list.purchase_tips.text.text = string.format(Language.LuckyGiftBag.PurchaseTips, gift_name or "")
    if grade and seq then
        local RewardData = LuckyGiftBagWgData.Instance:GetRewardPoolItemDataList(grade, seq)
        self.reward_list:SetDataList(RewardData)
    end

    self:FlushBtns()
end

function LuckyGiftBagComfirmView:FlushBtns()
    local grade, seq = LuckyGiftBagWgData.Instance:GetCurrentSelectInfo()
    local data_list = LuckyGiftBagWgData.Instance:GetGradeCfgInfo(grade, seq)
    for i = 1, #data_list do
        if self.node_list["btn_buy_type" .. i] then
            --XUI.AddClickEventListener(self.node_list["btn_buy_type" .. i], BindTool.Bind(self.OnBuyBtnClick, self, i))
            self.node_list["btn_buy_moneycost" .. i].text.text = data_list[i][1].need_cost
            self.node_list["btn_text"..i].text.text = string.format(Language.LuckyGiftBag.ComfirmBugBtn, data_list[i][1].times)
        end
    end
end

function LuckyGiftBagComfirmView:OnBuyBtnClick(prchase_mode)
    LuckyGiftBagWgData.Instance:SetBeforePrchaseMode(prchase_mode)
    LuckyGiftBagWgCtrl.Instance:BugLuckyGiftBagReq(prchase_mode)
end