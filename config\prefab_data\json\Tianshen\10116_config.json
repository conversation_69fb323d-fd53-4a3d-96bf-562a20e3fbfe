{"actorController": {"projectiles": [], "hurts": []}, "actorTriggers": {"effects": [{"triggerEventName": "combo1_1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10116_acttack", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10116/10116_acttack_prefab", "AssetName": "10116_acttack", "AssetGUID": "aa8a1ff6453fb254ebeeefadeac40889", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1", "playerAtPos": false, "ignoreParentScale": true}, {"triggerEventName": "combo1_2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10116_acttack", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10116/10116_acttack_prefab", "AssetName": "10116_acttack", "AssetGUID": "aa8a1ff6453fb254ebeeefadeac40889", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo2", "playerAtPos": false, "ignoreParentScale": true}, {"triggerEventName": "combo1_3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10116_acttack", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10116/10116_acttack_prefab", "AssetName": "10116_acttack", "AssetGUID": "aa8a1ff6453fb254ebeeefadeac40889", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo3", "playerAtPos": false, "ignoreParentScale": true}, {"triggerEventName": "attack1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10116_skill1", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10116/10116_skill1_prefab", "AssetName": "10116_skill1", "AssetGUID": "d6cd74a81f2599b43acb09add2776eeb", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1", "playerAtPos": false, "ignoreParentScale": true}, {"triggerEventName": "attack2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10116_skill2", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10116/10116_skill2_prefab", "AssetName": "10116_skill2", "AssetGUID": "7c2d7f79628037f45a396a39e8a556ca", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2", "playerAtPos": false, "ignoreParentScale": true}, {"triggerEventName": "attack3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10116_skill3", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10116/10116_skill3_prefab", "AssetName": "10116_skill3", "AssetGUID": "8bdce1ada720760428e6b74489609920", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack3", "playerAtPos": false, "ignoreParentScale": true}, {"triggerEventName": "rest/begin", "triggerDelay": 0.4, "triggerFreeDelay": 0.0, "effectGoName": "10116_rest", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10116/10116_rest_prefab", "AssetName": "10116_rest", "AssetGUID": "b8714f63333204b459573eb88264d3a0", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "root", "isAttach": true, "isRotation": false, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": -7.0, "triggerStopEvent": "[none]", "effectBtnName": "rest", "playerAtPos": false, "ignoreParentScale": false}], "sounds": [{"soundEventName": "combo1_1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10114", "AssetName": "MingJiangcombo_1_1", "AssetGUID": "cb2c84b3bc78fd748a9d97cb9d2b6d17", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_1", "soundBtnName": "combo1", "soundIsMainRole": false}, {"soundEventName": "combo1_2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10114", "AssetName": "MingJiangcombo_1_2", "AssetGUID": "9604cdcf17c38414a9b0846d7317c32d", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_2", "soundBtnName": "combo2", "soundIsMainRole": false}, {"soundEventName": "combo1_3/begin", "soundDelay": 0.5, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10114", "AssetName": "MingJiangcombo_1_3", "AssetGUID": "a3b168e346afd1947a17c347274a4cd1", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_3", "soundBtnName": "combo3", "soundIsMainRole": false}, {"soundEventName": "attack1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10114", "AssetName": "MingJiangattack1", "AssetGUID": "5637b92e5bfa5d54b8733af495d5b8fd", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack1", "soundBtnName": "attack1", "soundIsMainRole": false}, {"soundEventName": "attack2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10114", "AssetName": "MingJiangattack2", "AssetGUID": "a1fa93ec155e8f0499722ff8f14d94eb", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack2", "soundBtnName": "attack2", "soundIsMainRole": false}, {"soundEventName": "attack3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10114", "AssetName": "MingJiangattack3", "AssetGUID": "f1197b9d0b47e954f985b35949141a28", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack3", "soundBtnName": "attack3", "soundIsMainRole": false}], "cameraShakes": [{"CameraShakeBtnName": "attack3", "eventName": "attack3/begin", "numberOfShakes": 6, "distance": 2.0, "speed": 1000.0, "delay": 1.0, "decay": 0.0}], "radialBlurs": []}}