InterludePopDialogView = InterludePopDialogView or BaseClass(SafeBaseView)

function InterludePopDialogView:__init()
	self:SetMaskBg()
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/interlude_pop_dialog_prefab", "interlude_pop_dialog")

	self.interlude_id = nil
	self.hole_delay_timer = nil
end

function InterludePopDialogView:__delete()
end

function InterludePopDialogView:OpenCallBack()
	if self.interlude_id == nil then
		return
	end

	local data = InterludePopDialogWGData.Instance:GetInterludeCfgById(self.interlude_id)

	if data ~= nil then
		self:CancelTimer()
		self.hole_delay_timer = GlobalTimerQuest:AddDelayTimer(function()
			self:Close()
		end, data.interlude_time)
	end
end

function InterludePopDialogView:CloseCallBack()
	self.interlude_id = nil
	self:CancelTimer()
end

function InterludePopDialogView:CancelTimer()
	GlobalTimerQuest:CancelQuest(self.hole_delay_timer)
	self.hole_delay_timer = nil
end

function InterludePopDialogView:LoadCallBack()
end

function InterludePopDialogView:ReleaseCallBack()
	self.interlude_id = nil
	self:CancelTimer()
end

function InterludePopDialogView:ShowIndexCallBack(index)
end


function InterludePopDialogView:SetInterludeId(interlude_id)
	self.interlude_id = tonumber(interlude_id)
end


function InterludePopDialogView:OnFlush()
	if self.interlude_id == nil then
		return
	end

	local data = InterludePopDialogWGData.Instance:GetInterludeCfgById(self.interlude_id)
	if data ~= nil then
		self.node_list["txt_title"].text.text = data.interlude_title
		self.node_list["txt_content"].text.text = data.interlude_txt
	end
end

