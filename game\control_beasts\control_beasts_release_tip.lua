ControlBeastsReleaseTip = ControlBeastsReleaseTip or BaseClass(SafeBaseView)

function ControlBeastsReleaseTip:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -11), sizeDelta = Vector2(738, 530)})
	self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_beasts_release_tips")
	self:SetMaskBg(true)

	self.tips_data = nil
end

function ControlBeastsReleaseTip:__delete()
	self.tips_data = nil
end

function ControlBeastsReleaseTip:ReleaseCallBack()
	self.tips_data = nil

    if self.release_beast then
		self.release_beast:DeleteMe()
		self.release_beast = nil
	end

	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function ControlBeastsReleaseTip:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.ContralBeasts.Incubate20 or ""
	XUI.AddClickEventListener(self.node_list.btn_OK, BindTool.Bind2(self.OnClinkOkHandler, self))
	XUI.AddClickEventListener(self.node_list.btn_cancel, BindTool.Bind2(self.Close, self))

	if not self.release_beast then
		self.release_beast = BeststsReleaseItemRender.New(self.node_list.aim_root)
	end

	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list.list_reward)
	end
end


function ControlBeastsReleaseTip:SetTipsData(tips_data)
	self.tips_data = tips_data
end

function ControlBeastsReleaseTip:OnFlush()
	if not self.tips_data then
		return
	end

	self.release_beast:SetData({beast_id = self.tips_data.beast_id})
	local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.tips_data.beast_id)
	if beast_cfg then
		local call_back_data = {}
		if beast_cfg.release_back then
			for _, release_data in pairs(beast_cfg.release_back) do
				if release_data then
					table.insert(call_back_data, release_data)
				end
			end
		end

		self.node_list.no_reward:CustomSetActive(#call_back_data <= 0)
		self.node_list.list_reward:CustomSetActive(#call_back_data > 0)
		self.reward_list:SetDataList(call_back_data)
		self.node_list.beast_name.text.text = ToColorStr(beast_cfg.beast_name, ITEM_COLOR[beast_cfg.beast_color])
		self.node_list.beast_level.text.text = self.tips_data.level
		self.node_list.beast_skill_num.text.text = self.tips_data.beast_skill_num
	end
end

function ControlBeastsReleaseTip:OnClinkOkHandler()
	if not self.tips_data then
		return
	end

	if self.tips_data.is_incubate then
		ControlBeastsWGCtrl.Instance:SendOperateTypeIncubateRelease(self.tips_data.is_incubate, self.tips_data.incubate_index)
	else
		ControlBeastsWGCtrl.Instance:SendOperateTypeIncubateRelease(self.tips_data.is_incubate, self.tips_data.bag_id)
	end

	self:Close()
end

------------------------------------------------------------------------------------

----------------------------------灵兽放生item-----------------------
BeststsReleaseItemRender = BeststsReleaseItemRender or BaseClass(BaseRender)
function BeststsReleaseItemRender:LoadCallBack()
    if not self.beast_item then
		self.beast_item = ItemCell.New(self.node_list.item_pos)
        self.beast_item:SetIsShowTips(false)
	end

    if self.fs_star_list == nil then
        self.fs_star_list = {}
        for i = 1, 5 do
            self.fs_star_list[i] = self.node_list["star" .. i]
        end
    end
end

function BeststsReleaseItemRender:__delete()
	if self.beast_item then
		self.beast_item:DeleteMe()
		self.beast_item = nil
	end

    self.fs_star_list = nil
end

function BeststsReleaseItemRender:OnFlush()
    if not self.data then return end

	local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.data.beast_id)
	if beast_cfg then
		local star_res_list = GetStarImgResByStar(beast_cfg.beast_star)
		for k,v in ipairs(self.fs_star_list) do
			v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
		end

		self.node_list.element.image:LoadSprite(ResPath.GetControlBeastsImg(string.format("a2_ys_bq_%d", beast_cfg.beast_element)))
	end

	self.beast_item:SetData({item_id = self.data.beast_id, is_beast = true})---这里需要其他的东西在加，看策划需求
end
