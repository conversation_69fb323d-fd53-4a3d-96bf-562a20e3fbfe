---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by 123.
--- DateTime: 2019/11/23 19:54
---
local TeamWorldInvite = "team_world_invite" --喊话

TeamTalkView = TeamTalkView or BaseClass(SafeBaseView)
function TeamTalkView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
    self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_talk")
    self.view_name = "TeamTalkView"
end

function TeamTalkView:ReleaseCallBack()
    self.last_edit_time = nil
    self.data = nil
end

function TeamTalkView:LoadCallBack()
    self.node_list["title_view_name"].text.text = Language.NewTeam.ViewNameTalk
	self.node_list["layout_commmon_second_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
    self.node_list["layout_commmon_second_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
    XUI.AddClickEventListener(self.node_list.btn_change, BindTool.Bind(self.OnClickChange, self))
    self.node_list.talk_input_field.input_field.onValueChanged:AddListener(BindTool.Bind(self.OnEditValueChange, self))
end

function TeamTalkView:ShowIndexCallBack()
    self:SetDefaultTextValue()
end

--team_type, fb_mode, limit_min_level, limit_max_level
function TeamTalkView:SetData(data)
    self.data = data
end

function TeamTalkView:SetDefaultTextValue()
    if type(self.data) == "string" then
        self.node_list.talk_input_field.input_field.text = self.data
    else
        local goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(self.data.team_type, self.data.fb_mode)
        if goal_info and goal_info.default_talk_content then
            local min_level_str = NewTeamWGData.Instance:GetLevelStr(self.data.limit_min_level)
            local max_level_str = NewTeamWGData.Instance:GetLevelStr(self.data.limit_max_level)
            self.node_list.talk_input_field.input_field.text = string.format(goal_info.default_talk_content, min_level_str, max_level_str)
        end
    end
end

function TeamTalkView:OnEditValueChange(str)
    local len, table = CheckStringLen(str, COMMON_CONSTS.ZHAN_DUI_MAX_NOTICE_LEN)
    if not len then
        if table then
            local str = ""
            for i = 1, #table do
                str = str .. table[i]
            end
            self.node_list.talk_input_field.input_field.text = str
        end
        if self.last_edit_time and self.last_edit_time > Status.NowTime then
            return
        end
        SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.WorldTalkMaxNum)
        self.last_edit_time = Status.NowTime + 0.5
    end
end

function TeamTalkView:OnClickChange()
    local str = string.gsub(self.node_list.talk_input_field.input_field.text, "^[ \t\n\r]+", "")	--过滤空字符串
    local len, table = CheckStringLen(str, COMMON_CONSTS.ZHAN_DUI_MAX_NOTICE_LEN)
    if not len then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.WorldTalkMaxNum)
        return
    end

	local function callback()
		if str == "" then
			TipsSystemManager.Instance:ShowSystemTips(Language.Common.TeamTalkCanNotSendEmpty)
			return
		end
		--GlobalEventSystem:Fire(TeamWorldTalk.MAIN_WORLD_TALK)
        NewTeamWGCtrl.Instance:SendWordTalk()
	end
	OperateFrequency.Operate(callback, TeamWorldInvite, 10)
end
