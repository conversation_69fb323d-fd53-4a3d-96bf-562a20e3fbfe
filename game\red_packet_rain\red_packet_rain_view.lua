-- 跨服红包天降
RedPacketRainView = RedPacketRainView or BaseClass(SafeBaseView)

-- 红包雨时间
local rain_time = 90

function RedPacketRainView:__init()
	self.view_style = ViewStyle.Half
	self.is_safe_area_adapter = true
	self.view_layer = UiLayer.PopTop
    self:SetMaskBg(false, false)

    self:AddViewResource(0, "uis/view/red_packet_rain_ui_prefab", "layout_red_packet_rain")

end

function RedPacketRainView:ReleaseCallBack()
	if self.red_packet_item_list then
		for k, v in pairs(self.red_packet_item_list) do
			v:DeleteMe()
		end
		self.red_packet_item_list = nil
	end

	if self.red_packet_num_list then
		for k, v in pairs(self.red_packet_num_list) do
			v:DeleteMe()
		end
		self.red_packet_num_list = nil
	end

	if self.head_cell then

		self.head_cell:DeleteMe()
		self.head_cell = nil
	end

	if self.sequence then
		self.sequence:Kill()
		self.sequence = nil
	end

	if self.time_sequence then
		self.time_sequence:Kill()
		self.time_sequence = nil
	end

	if self.regional_barrage_area then
        self.regional_barrage_area:DeleteMe()
        self.regional_barrage_area = nil
    end
end

function RedPacketRainView:RecordPos(item)
	self.cur_red_packet_pos = Vector2(item.view.rect.anchoredPosition.x,item.node_list.node.rect.anchoredPosition.y)
end

function RedPacketRainView:LoadCallBack()
	self.red_packet_item_list = {}
	for i = 1, 10 do
		self.red_packet_item_list[i] = RedPacketRainRender.New()
		self.red_packet_item_list[i]:LoadAsset("uis/view/red_packet_rain_ui_prefab", "red_packet_render", self.node_list.node_group.transform)
		self.red_packet_item_list[i]:SetClickCallBack(BindTool.Bind(self.RecordPos, self, self.red_packet_item_list[i]))
	end

	self.red_packet_num_list = {}
	for i = 1, 10 do
		self.red_packet_num_list[i] = RedPacketRainNumRender.New()
		self.red_packet_num_list[i]:LoadAsset("uis/view/red_packet_rain_ui_prefab", "red_packet_num_render", self.node_list.node_group.transform)
	end

	if not self.regional_barrage_area then
        self.regional_barrage_area = RegionalBarrage.New(self.node_list.regional_barrage_area)
        self.regional_barrage_area:SetGetDanMuInfoFunc(BindTool.Bind(function ()
            return RedPacketRainWGData.Instance:GetABarrageMessage()
        end, self))
    end

	self.head_cell = BaseHeadCell.New(self.node_list.role_head)

	XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind(self.Close, self))

	self.group_width = self.node_list.right_node.rect.anchoredPosition.x
end

function RedPacketRainView:OpenCallBack()
	
end

function RedPacketRainView:CloseCallBack()
	RedPacketRainWGCtrl.Instance:RedPacketOperate(CROSS_RED_PAPER_FALLING_OPERATE_TYPE.CROSS_RED_PAPER_FALLING_OPERATE_TYPE_ROLE_INFO)
	RedPacketRainWGCtrl.Instance:RedPacketOperate(CROSS_RED_PAPER_FALLING_OPERATE_TYPE.CROSS_RED_PAPER_FALLING_OPERATE_TYPE_JOIN_GET_REWARD, 0)
	self:CleanTimer1()
	self:CleanTimerWait()
end

function RedPacketRainView:ShowIndexCallBack()
	-- 开始3秒倒计时
	self:StarWait(3)
end

function RedPacketRainView:OnFlush(param_t, index)
	for k,v in pairs(param_t) do
		if k == "all" then
 
		elseif k == "flush_count" then
			self:ShowCount(v.count)

			self:ShowNum()
        end
    end

end

function RedPacketRainView:CreateRoleHeadCell()

end


function RedPacketRainView:CleanTimerWait()
    if self.timer_wait and CountDown.Instance:HasCountDown(self.timer_wait) then
        CountDown.Instance:RemoveCountDown(self.timer_wait)
        self.timer_wait = nil
    end
end

function RedPacketRainView:StarWait(wait_time)
	local activity_info = RedPacketRainWGData.Instance:GetAcitivityInfo()

	if not activity_info or activity_info.role_info.role_id == 0 then
		self.node_list.buttom:SetActive(false)
	else
		self.node_list.buttom:SetActive(true)
		self.node_list.text_name.text.text = string.format(Language.RedPacketRain.SendRole2, activity_info.role_info.role_name)

		self.head_cell:SetData(activity_info.role_info)
		self.head_cell:SetBgActive(false)
	end
	
	
	-- 策划说写死一个展示值
	self.node_list.text_num.text.text =string.format(Language.RedPacketRain.AddGold, 1000000 + activity_info.add_round_role_consume_num) 

	self:CleanTimerWait()
	self.node_list.read_layer:SetActive(true)
	self.node_list.red_packet_layer:SetActive(false)
	self.node_list.red_packet_cound_down:SetActive(false)
    if wait_time then
		self.node_list.text_time.text.text = wait_time
        -- local complete_time = nil -- 完成的时间戳（与 total_time 选一即可）
        self.timer_wait = CountDown.Instance:AddCountDown(wait_time,1,
            -- 回调方法
            function(elapse_time, total_time)
				self.node_list.text_time.text.text = math.ceil(total_time - elapse_time)
				self:TimeAnim()
            end,
            -- 倒计时完成回调方法
            function()
				self:StarRain()
            end)
    end
end

-- 清除倒计时器1
function RedPacketRainView:CleanTimer1()
    if self.timer_1 and CountDown.Instance:HasCountDown(self.timer_1) then
        CountDown.Instance:RemoveCountDown(self.timer_1)
        self.timer_1 = nil
    end
end

function RedPacketRainView:ShowNum()
	for k, v in pairs(self.red_packet_num_list) do
		if not v:IsUseing() then
			v.view.rect.anchoredPosition = self.cur_red_packet_pos
			local rreward_info = RedPacketRainWGData.Instance:GetRewardInfo()
			v:SetData(rreward_info)
			v:StarFall()
			break
		end
	end
end

function RedPacketRainView:StarRain()
	self:ShowCount(0)
	self.node_list.read_layer:SetActive(false)
	self.node_list.red_packet_layer:SetActive(true)
	self.node_list.red_packet_cound_down:SetActive(true)

	self:CleanTimer1()
	local index = 0

	local round_continue_s = RedPacketRainWGData.Instance:GetOtherCfg("round_continue_s")
	self.node_list.red_packet_cound_down.text.text = string.format(Language.RedPacketRain.CountDownStr, TimeUtil.FormatSecondDHM9(round_continue_s))

	self.timer_1 = CountDown.Instance:AddCountDown(round_continue_s, 0.5,
	-- 回调方法
	function(elapse_time, total_time)
		for k, v in pairs(self.red_packet_item_list) do
			if not v:IsUseing() then
				local pos_x =  math.random(-self.group_width, self.group_width)
				v.view.rect.anchoredPosition = Vector2(pos_x, 0)
				v:SetIndex(index)
				v:StarFall()
				index = index + 1
				break
			end
		end

		self.node_list.red_packet_cound_down.text.text = string.format(Language.RedPacketRain.CountDownStr, TimeUtil.FormatSecondDHM9(total_time - elapse_time))
	end,
	-- 倒计时完成回调方法
	function()
		self.node_list.red_packet_cound_down.text.text = ""
		self:Close()
	end)

	if self.regional_barrage_area then
		RedPacketRainWGCtrl.Instance:RedPacketOperate(CROSS_RED_PAPER_FALLING_OPERATE_TYPE.CROSS_RED_PAPER_FALLING_OPERATE_TYPE_JOIN_GET_REWARD, 1)

		local desc_show_cd = RedPacketRainWGData.Instance:GetOtherCfg("desc_show_cd")
        self.regional_barrage_area:StartShowDanMu(desc_show_cd)
    end
end

function RedPacketRainView:ShowCount(count)
	-- CultivationWGData.Instance:Get
	self.node_list.text_count.text.text = count;

	if count ~= 0 then
		if not self.sequence then
			if self.sequence then
				self.sequence:Kill()
				self.sequence = nil
			end

			self.sequence = DG.Tweening.DOTween.Sequence()
			self.sequence:SetAutoKill(false)
			self.sequence:Append(self.node_list.img_red_packet.rect:DOScale(Vector3(0.35, 0.35, 0.35), 0.1):SetEase(DG.Tweening.Ease.Linear))
			self.sequence:Append(self.node_list.img_red_packet.rect:DOScale(Vector3(0.25, 0.25, 0.25), 0.1):SetEase(DG.Tweening.Ease.Linear))
		else
			self.sequence:Restart()
		end
	end

end

function RedPacketRainView:TimeAnim()
	if not self.time_sequence then
		self.node_list["img_time"].transform.localScale = Vector3(3,3,3)
		if self.time_sequence then
			self.time_sequence:Kill()
			self.time_sequence = nil
		end

		self.time_sequence = DG.Tweening.DOTween.Sequence()
		self.time_sequence:SetAutoKill(false)
		self.time_sequence:Append(self.node_list.img_time.rect:DOScale(Vector3(1, 1, 1), 0.2):SetEase(DG.Tweening.Ease.OutExpo))
		-- self.time_sequence:Append(self.node_list.img_time.canvas_group:DoAlpha(0.6,1, 0.3):SetEase(DG.Tweening.Ease.OutExpo))

	else
		self.node_list["img_time"].transform.localScale = Vector3(3,3,3)
		self.time_sequence:Restart()
	end

end

function RedPacketRainView:AddOneSpecialDanMu(data)
    if self.regional_barrage_area then
		local text_color = COLOR3B.WHITE
		local self_uuid = RoleWGData.Instance:GetUUid()
		if data.uuid == self_uuid then
			text_color = COLOR3B.C7
		end

		local emoji = MoneyTypeEmojiByItemID[data.item_id] or MoneyType.CangJinScore
		local desc_content = string.format(Language.RedPacketRain.BarrageText, data.role_name, data.num, emoji)

		local desc_show_speed = RedPacketRainWGData.Instance:GetOtherCfg("desc_show_speed")
        local danmu_info = {
            desc_content = desc_content,
            need_random_color = false,
            color_data_list = ITEM_COLOR_DARK,
			text_color = text_color,
            move_speed = desc_show_speed,
			is_shield_bg = true
		}

        self.regional_barrage_area:AddOneTemporaryDanMu(danmu_info)
    end
end

-----------------------------红包Render-------------------------------
RedPacketRainRender = RedPacketRainRender or BaseClass(BaseRender)

function RedPacketRainRender:__init()
    
end

function RedPacketRainRender:__delete()
	self:Recycle()
	self.useing = nil
end

function RedPacketRainRender:LoadCallBack()
    -- item_render = self.item_render.New()
	-- self.item_render_tab[i] = item_render
	-- item_render:LoadAsset(self.asset_bundle, self.asset_name, self.view.transform)
	XUI.AddClickEventListener(self.node_list.btn_click, BindTool.Bind(self.OnClick, self))


end

function RedPacketRainRender:OnFlush(param_t, index)
	if not self.data then
		return 
	end

end

function RedPacketRainRender:SetClickCallBack(callback)
	self.click_callback = callback
end



function RedPacketRainRender:OnClick()
	-- self.index
	RedPacketRainWGCtrl.Instance:RedPacketOperate(CROSS_RED_PAPER_FALLING_OPERATE_TYPE.CROSS_RED_PAPER_FALLING_OPERATE_TYPE_GET_REWARD, self.index)
	if self.click_callback then
		self.click_callback()
	end
	self:Recycle()
	
end

function RedPacketRainRender:IsUseing()
	return self.useing
end

function RedPacketRainRender:KillTween()
	if self.fall_tweener then
		self.fall_tweener:Kill()
		self.fall_tweener = nil
	end
end


function RedPacketRainRender:StarFall()
	self.useing = true
    local size = math.random(1,2)
	self.node_list.node:SetActive(true)

	if size == 1 then
		self.node_list.node.transform.localScale = Vector3(1, 1, 1)
	else
		self.node_list.node.transform.localScale = Vector3(0.8, 0.8, 0.8)
	end
	self.node_list.node.rect.anchoredPosition = Vector2(0, 0)

	self:KillTween()
	local time = math.random(2,3)
	self.fall_tweener = self.node_list.node.rect:DOLocalMoveY(-1300, time):OnComplete(function()
		self:Recycle()
	end):SetEase(DG.Tweening.Ease.Linear)
end

function RedPacketRainRender:Recycle()
	self:KillTween()
	self.node_list.node:SetActive(false)
	self.useing = false
end

-----------------------------奖励飘字-------------------------------
RedPacketRainNumRender = RedPacketRainNumRender or BaseClass(BaseRender)

function RedPacketRainNumRender:__init()
    
end

function RedPacketRainNumRender:__delete()
	if self.sequence then
		self.sequence:Kill()
		self.sequence = nil
	end

	self:Recycle()
	self.useing = nil
end

function RedPacketRainNumRender:LoadCallBack()

end

function RedPacketRainNumRender:OnFlush(param_t, index)
	if not self.data then
		return 
	end
	
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.reward.item_id)
	self.node_list.img_icon.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
	self.node_list.text_num.tmp.text = self.data.reward.num
end


function RedPacketRainNumRender:IsUseing()
	return self.useing
end

function RedPacketRainNumRender:KillTween()
	if self.fall_tweener then
		self.fall_tweener:Kill()
		self.fall_tweener = nil
	end
end


function RedPacketRainNumRender:StarFall()
	self.useing = true
	self.node_list.node:SetActive(true)
	self.node_list.effect:SetActive(true)
	self.node_list.node.rect.anchoredPosition = Vector2(0, 0)
	self.node_list.node.canvas_group.alpha = 1
	if not self.sequence then
		if self.sequence then
			self.sequence:Kill()
			self.sequence = nil
		end

		self.sequence = DG.Tweening.DOTween.Sequence()
		self.sequence:SetAutoKill(false)
		self.sequence:Append(self.node_list.node.rect:DOLocalMoveY(40, 0.3):SetEase(DG.Tweening.Ease.Linear))
		self.sequence:Append(self.node_list.node.canvas_group:DoAlpha(1, 0 , 0.5):SetEase(DG.Tweening.Ease.Linear))
		self.sequence:AppendCallback(function ()
			self:Recycle()
		end)
	else

		self.sequence:Restart()
	end
end

function RedPacketRainNumRender:Recycle()
	self:KillTween()
	self.node_list.node:SetActive(false)
	self.node_list.effect:SetActive(false)
	self.useing = false
end
