KF3V3WGData = KF3V3WGData or BaseClass()
function KF3V3WGData:__init()
    if KF3V3WGData.Instance then
        ErrorLog("[KuafuPVPWGData] attempt to create singleton twice!")
        return
    end
    KF3V3WGData.Instance =self

    self:InitConfig()

    --上个赛季
    self.last_season_rank_list = {}
    self.last_season_my_rank = {}
    self.last_season_my_rank.score = 0
    self.last_season_my_rank.rank = 0
    --当前赛季
    self.cur_season_rank_list = {}
    self.cur_season_my_rank = {}
    self.cur_season_my_rank.score = 0
    self.cur_season_my_rank.rank = 0

    --击杀队列
    self.kill_info_queue = {}

    self.zhandui_view_is_opened = false

    -- 开始匹配时间
    self.start_match_time = 0

    --RemindManager.Instance:Register(RemindName.KF3V3)
    RemindManager.Instance:Register(RemindName.ActPVPbArena, BindTool.Bind(self.IsShowActPVPbArenaRedPoint, self))  -- 3v3
    RemindManager.Instance:Register(RemindName.KF3V3, BindTool.Bind(self.IsShow3V3RedPoint, self))  -- 3v3
    RemindManager.Instance:Register(RemindName.ZhanDui, BindTool.Bind(self.IsShowZhanDuiRedPoint, self))  -- 3v3
    RemindManager.Instance:Register(RemindName.JoinZhanDui, BindTool.Bind(self.JoinZhanduiRemind, self))  -- 3v3

    self:RegisterActPVPbArenaRemindInBag(RemindName.ActPVPbArena, RemindName.KF3V3)
end

function KF3V3WGData:__delete()
    self.last_season_rank_list = {}
    self.last_season_my_rank = {}
    self.cur_season_rank_list = {}
    self.cur_season_my_rank = {}
    RemindManager.Instance:UnRegister(RemindName.ActPVPbArena)
    RemindManager.Instance:UnRegister(RemindName.KF3V3)
    RemindManager.Instance:UnRegister(RemindName.ZhanDui)
	KF3V3WGData.Instance = nil
	self.kuafu_tvt_cfg = nil
end

function KF3V3WGData:RegisterActPVPbArenaRemindInBag(...)
    local map = {}

    local upgrade_cfg = ConfigManager.Instance:GetAutoConfig("battleflag_cfg_auto").upgrade
    for _, v in pairs(upgrade_cfg) do
        for _, item in pairs(v.consume_item) do
            map[item.item_id] = true
        end
    end

    local item_id_list = {}
    for k,v in pairs(map) do
        table.insert(item_id_list, k)
    end

    local remind_name_list = {...}
    for _, remind_name in pairs(remind_name_list) do
        BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
    end
end

function KF3V3WGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("kuafu_tvt_auto")
    -------旧配置表考虑需不需要用到
    -- 旗子坐标
    self.flag_pos_cfg = ListToMap(cfg.flag_pos, "flag_id")
    -- 每日参与场次奖励
    self.join_times_reward_cfg = cfg.flag_pos.join_times_reward
    -- 其他配置
    self.other_cfg = cfg.other[1]

    --新开的配置
    self.new_cfg = ConfigManager.Instance:GetAutoConfig("cross3v3new_auto")
    ----赛季排名奖励
    --self.season_rank_reward_cfg = ListToMap(self.new_cfg.season_rank_reward, "seq")
    ----赛季段位奖励
    --self.season_score_reward_cfg = ListToMap(self.new_cfg.season_score_reward, "seq")
    self.open_day = self.new_cfg.open[1].openserver_day
    self.ghost_model_id = self.new_cfg.pk[1].dead_model
    self.ghost_model_id_nv = self.new_cfg.pk[1].dead_model_nv

    self.match_times_reward_list = {}
    for i, v in ipairs(self.new_cfg.match_times_reward) do
        table.insert(self.match_times_reward_list, v)
    end
end

function KF3V3WGData:GetOtherCfg()
    return self.other_cfg
end

function KF3V3WGData:GetNewOtherCfg()
    return self.new_cfg.other[1]
end

function KF3V3WGData:GetDuanWeiCfgByIndex(cfg_index)
    return self.new_cfg.grade[cfg_index]
end

--这里返回总积分
function KF3V3WGData:GetDuanWeiCfgAll(jifen)
    local grade_cfg = self.new_cfg.grade
	for k = #grade_cfg,1, -1 do
		if grade_cfg[k] and grade_cfg[k].score <= jifen then
            if grade_cfg[k + 1] ~= nil then
                --还没满， 把总积分返回出去
                return grade_cfg[k], grade_cfg[k + 1].score, false, k
            else
                --满段位
                return grade_cfg[k], 0, true, k

            end
		end
	end
	return grade_cfg[1], grade_cfg[2].score, false, 1
end

function KF3V3WGData:GetDuanWeiCfg(jifen)
	local grade_cfg = self.new_cfg.grade
	for k = #grade_cfg,1, -1 do
		if grade_cfg[k] and grade_cfg[k].score <= jifen then
            if grade_cfg[k + 1] ~= nil then
                --还没满， 把积分差返回出去
                return grade_cfg[k], grade_cfg[k + 1].score - grade_cfg[k].score, false, k
            else
                --满段位
                return grade_cfg[k], 0, true, k

            end
		end
	end
	return grade_cfg[1], grade_cfg[2].score - grade_cfg[1].score, false, 1
end

--获取赛季奖励列表
function KF3V3WGData:GetSeasonRewardList()
    local data_list = {}
    for i, v in ipairs(self.new_cfg.season_rank_reward) do
        table.insert(data_list, v)
    end
    return data_list
end

-- 获得玩家当前排名可获取的赛季奖励配置
function KF3V3WGData:GetCurSeasonReward()
    local my_rank = self:GetMyRank().rank or 9999
    if my_rank == 0 then
        return self.new_cfg.season_rank_reward[#self.new_cfg.season_rank_reward]
    else
        for i, v in ipairs(self.new_cfg.season_rank_reward) do
            if my_rank >= v.lower_rank and my_rank <= v.upper_rank then
                return v
            end
        end
    end
    return nil
end

--获取段位奖励列表
function KF3V3WGData:GetDuanWeiRewardList()
    local data_list = {}
    for i, v in ipairs(self.new_cfg.season_score_reward) do
        table.insert(data_list, v)
    end
    return data_list
end

-- 获得玩家当前段位可获取的赛季奖励配置
function KF3V3WGData:GetCurDuanWeiReward()
    local zhandui_info = ZhanDuiWGData.Instance:GetZhanDuiInfo()
    local score = zhandui_info.scroe or 0
    for i, v in ipairs(self.new_cfg.season_score_reward) do
        if score >= v.lower_score and score <= v.upper_score then
            return v
        end
    end
    return nil
end


--获取战队令牌列表
function KF3V3WGData:GetZhanLingDataList()
    local cfg = self.new_cfg.zhandui_lingpai
    local data_list = {}
    for i, v in ipairs(cfg) do
        table.insert(data_list, v)
    end
    return data_list
end

function KF3V3WGData:GetZhanLingCfg(lingpai_id)
    local cfg = self.new_cfg.zhandui_lingpai
    for i, v in ipairs(cfg) do
        if v.lingpai_id == lingpai_id then
            return v
        end
    end
    --print_error("传入错误的令牌id ::: ", lingpai_id)
    return cfg[1]
end

function KF3V3WGData:GetPKRewardCfg()
    return self.new_cfg.pk_reward[1]
end

function KF3V3WGData:GetOpenDay()
    return self.open_day
end

function KF3V3WGData:GetMatchTimesRewardList()
    return self.match_times_reward_list
end

function KF3V3WGData:GetSortMatchTimesRewardList()
    local role_info = ZhanDuiWGData.Instance:GetRoleInfo()
    local flag_list = ZhanDuiWGData.Instance:GetFetchFlagList()
    --local data_list = {}
    local sort_list = {}
    --for i, v in ipairs(self.match_times_reward_list) do
    --    table.insert(data_list, v)
    --end
    for i, v in ipairs(self.match_times_reward_list) do
        local data = TableCopyCfg(v)
        local has_fetch = flag_list[32 - data.seq] == 1
        local can_fetch = role_info.today_match_times >= data.match_times and not has_fetch
        if has_fetch then
            data.sort = 1000
        else
            if can_fetch then
                data.sort = 10
                data.can_fetch = true
            else
                data.sort = 100
            end
        end
        table.insert(sort_list, data)
    end
    table.sort(sort_list, SortTools.KeyLowerSorters("sort", "seq"))
    return sort_list
end

function KF3V3WGData:GetPKItemList(is_win, is_mvp, match_times, mvp_times)
    local pk_reward = self:GetPKRewardCfg()
    local data_list = {}
    if is_win then
        if match_times <= pk_reward.reward_time_limit then
            for i = 0, 20 do
                if pk_reward.win_reward[i] then
                    table.insert(data_list, pk_reward.win_reward[i])
                else
                    break
                end
            end
        end
        --mvp
        if mvp_times <= pk_reward.mvp_reward_time_limit then
            if is_mvp then
                for i = 0, 20 do
                    if pk_reward.mvp_reward[i] then
                        table.insert(data_list, pk_reward.mvp_reward[i])
                    else
                        break
                    end
                end
            end
        end
    else
        if match_times <= pk_reward.reward_time_limit then
            for i = 0, 20 do
                if pk_reward.lose_reward[i] then
                    table.insert(data_list, pk_reward.lose_reward[i])
                else
                    break
                end
            end
        end
    end
    return data_list
end

function KF3V3WGData:GetCanGetMatchTimesRewardMaxCount()
    local max_can_get_reward_index = 0
    local role_info = ZhanDuiWGData.Instance:GetRoleInfo()
    local flag_list = ZhanDuiWGData.Instance:GetFetchFlagList()
    if role_info and flag_list then
        for i, v in ipairs(self.match_times_reward_list) do
            local has_fetch = flag_list[32 - v.seq] == 1
            local can_fetch = role_info.today_match_times >= v.match_times and not has_fetch
            if has_fetch or can_fetch then
                max_can_get_reward_index = i
            end
        end
    end

    return max_can_get_reward_index
end


--------------协议数据start---------------
--设置匹配信息
function KF3V3WGData:SetMatcthInfo(protocol)
    self.my_side = protocol.my_side
    RoleWGData.Instance:SetAttr("cross3v3_side", self.my_side)
    GlobalEventSystem:Fire(OtherEventType.MAIN_ROLE_3V3_SIDE_CHANGE)
    
    self.match_info = {}
    for i, v in ipairs(protocol.side_list) do
        for k = 1, COMMON_CONSTS.CROSS3V3_PLAYER_COUNT_PER_SIDE do
            local data = {}
            data.side = i == 1 and EnumKF3V3SideType.RED or EnumKF3V3SideType.BLUE
            data.uid = v.member_list[k].uid
            data.uuid = v.member_list[k].uuid
            data.name = v.member_list[k].name
            data.score = v.member_list[k].score
            data.zhandui_name = v.member_list[k].zhandui_name
            data.avatar_key_big = v.member_list[k].avatar_key_big
            data.avatar_key_small = v.member_list[k].avatar_key_small
            data.photoframe = v.member_list[k].photoframe       	            -- 头像框
            data.sex = v.member_list[k].sex              	                -- 职业
            data.prof = v.member_list[k].prof              	                -- 职业
            data.is_client_ready = v.member_list[k].is_client_ready          -- 客户端准备状态
            data.capability = v.member_list[k].capability
            table.insert(self.match_info, data)
        end
    end
    if protocol.notify_reason == Cross3V3MatchingInfoNotifyReason.StartMatching then
        self.is_matching = true
    elseif protocol.notify_reason == Cross3V3MatchingInfoNotifyReason.CancelMatching then
		self.is_matching = false
	elseif protocol.notify_reason == Cross3V3MatchingInfoNotifyReason.TimeOut then
		self.is_matching = false
	elseif protocol.notify_reason == Cross3V3MatchingInfoNotifyReason.MatchingSucc then
		self.is_matching = false
    end
end

--设置场景信息
function KF3V3WGData:SetSceneInfo(protocol)
    self.scene_other_info = {}
    self.scene_other_info.pk_state = protocol.pk_state
    self.scene_other_info.next_state_time = protocol.next_state_time
    self.scene_info = protocol.side_list
end

--设置击杀信息
function KF3V3WGData:SetKillInfo(protocol)
    local data = {}
    data.killer = protocol.killer
    data.killer.is_be_kill = false
    data.be_killer = protocol.dead
    data.be_killer.is_be_kill = true
    table.insert(self.kill_info_queue, data)
end

function KF3V3WGData:SetJieSuanInfo(protocol)
    self.jiesuan_info = protocol
end

--设置排行信息
function KF3V3WGData:SetRankInfo(protocol)
    if protocol.is_last_season == EnumCross3V3RankType.LastSeason then
        --上个赛季
        self.last_season_rank_list = protocol.rank_list
        self.last_season_my_rank = protocol.my_zhandui_rank_item
    else
        --当前赛季
        self.cur_season_rank_list = protocol.rank_list
        self.cur_season_my_rank = protocol.my_zhandui_rank_item
    end
end

function KF3V3WGData:SetPrepareSceneInfo(protocol)
    self.prepare_scene_info = {}
    self.prepare_scene_info.interval_reward_exp = protocol.interval_reward_exp
end

function KF3V3WGData:SetStatueInfo(info, index)
    if not info then
		self.statue_info_list = {}
	else
        if not self.statue_info_list then
            self.statue_info_list = {}
        end
		self.statue_info_list[index] = {}
		self.statue_info_list[index].role_name = info.role_name
		self.statue_info_list[index].appearance = info.appearance
		self.statue_info_list[index].sex = info.sex
		self.statue_info_list[index].prof = info.prof
	end
end

function KF3V3WGData:GetStatueInfoByIndex(index)
    if not self.statue_info_list then
        return nil
    end
	return self.statue_info_list[index]
end

--获取匹配数据
function KF3V3WGData:GetMatchInfo()
    return self.match_info
end

function KF3V3WGData:GetMySide()
    return self.my_side
end

--是否匹配中
function KF3V3WGData:GetIsMatching()
    return self.is_matching
end

function KF3V3WGData:GetSceneInfo()
    return self.scene_info
end

function KF3V3WGData:GetSceneOtherInfo()
    return self.scene_other_info
end

function KF3V3WGData:GetPrepareSceneInfo()
    return self.prepare_scene_info
end

function KF3V3WGData:GetJieSuanInfo()
    return self.jiesuan_info
end

--获取场景内玩家信息列表
function KF3V3WGData:GetScenePlayerList()
    if not self.scene_info then return nil end
    local player_list = {}
    local remain_count_list = {}
    remain_count_list[EnumKF3V3SideType.RED] = 0
    remain_count_list[EnumKF3V3SideType.BLUE] = 0
    for i, side in ipairs(self.scene_info) do
        for k = 1, #side do
            local data = {}
            data.uid = side[k].uid
            data.origin_uid = side[k].origin_uid
            data.name = side[k].name
            data.cur_hp = side[k].cur_hp
            data.max_hp = side[k].max_hp
            data.is_offline = side[k].is_offline
            data.is_ghost = side[k].is_ghost
            data.side = i == 1 and EnumKF3V3SideType.RED or EnumKF3V3SideType.BLUE
            if data.cur_hp > 0 then
               remain_count_list[data.side] = remain_count_list[data.side] + 1
            end
            table.insert(player_list, data)
        end
    end
    return player_list, remain_count_list
end

function KF3V3WGData:GetMainRoleSide()
    if not IsEmptyTable(self.scene_info) then
        local main_role_uuid = RoleWGData.Instance:InCrossGetOriginUid()
        for k1,v1 in pairs(self.scene_info) do
            for k2,v2 in pairs(v1) do
                if main_role_uuid == v2.origin_uid then
                    return k1 - 1
                end
            end
        end
    end
    return nil
end

--获取排行信息
function KF3V3WGData:GetRankList(rank_type)
    rank_type = rank_type or EnumCross3V3RankType.CurSeason
    if rank_type == EnumCross3V3RankType.LastSeason then
        return self.last_season_rank_list
    elseif rank_type == EnumCross3V3RankType.CurSeason then
        return self.cur_season_rank_list
    end
end

--获取自己的排行信息
function KF3V3WGData:GetMyRank(rank_type)
    rank_type = rank_type or EnumCross3V3RankType.CurSeason
    if rank_type == EnumCross3V3RankType.LastSeason then
        return self.last_season_my_rank
    elseif rank_type == EnumCross3V3RankType.CurSeason then
        return self.cur_season_my_rank
    end
end

function KF3V3WGData:GetKillInfoQueue()
    return self.kill_info_queue
end
function KF3V3WGData:ClearKillInfoQueue()
    self.kill_info_queue = {}
end

function KF3V3WGData:CacheLeaderPos(scene_id, pos_x, pos_y)
    self.leader_pos_info = {}
    self.leader_pos_info.scene_id = scene_id
    self.leader_pos_info.pos_x = pos_x
    self.leader_pos_info.pos_y = pos_y
end

function KF3V3WGData:GetLeaderPosInfo()
    return self.leader_pos_info
end
function KF3V3WGData:ClearLeaderPosInfo()
    self.leader_pos_info = nil
end

function KF3V3WGData:SetStartMatchTime()
    self.start_match_time = TimeWGCtrl.Instance:GetServerTime()
end

-- 获得当前已匹配时间
function KF3V3WGData:GetMatchTime()
    if not self:GetIsMatching() then
        return 0
    end
    return math.floor(TimeWGCtrl.Instance:GetServerTime() - self.start_match_time)
end
--------------协议数据end---------------




---------红点--------------------------
--3v3红点
function KF3V3WGData:IsShowActPVPbArenaRedPoint()
    if ShowRedPoint.SHOW_RED_POINT == self:IsShow3V3RedPoint() then
        return 1
    end
    if ShowRedPoint.SHOW_RED_POINT == self:IsShowZhanDuiRedPoint() then
        return 1
    end
	return 0
end
--3v3信息
function KF3V3WGData:IsShow3V3RedPoint()
    local flag = self:CheckCanFetchReward()
    if flag == 1 then
        return 1
    end
    return 0
end
--3v3战队
function KF3V3WGData:IsShowZhanDuiRedPoint()
    if ZhanDuiWGData.Instance:GetApplyCount() > 0 then
        return 1
    end
	return 0
end

-- 如果没有加入战队，则提醒红点，点击消失
function KF3V3WGData:JoinZhanduiRemind()
    if not ZhanDuiWGData.Instance:GetIsInZhanDui() and not self.zhandui_view_is_opened then
        return 1
    end
    return 0
end

function KF3V3WGData:SetZhanDuiViewIsOpened()
    self.zhandui_view_is_opened = true
    RemindManager.Instance:Fire(RemindName.JoinZhanDui)
end

function KF3V3WGData:CheckCanFetchReward()
    local flag_list = ZhanDuiWGData.Instance:GetFetchFlagList()
    local reward_list = KF3V3WGData.Instance:GetMatchTimesRewardList()
    local role_info = ZhanDuiWGData.Instance:GetRoleInfo()
    if not role_info or IsEmptyTable(role_info) then
        return 0
    end
    for i = 1, 10 do
        if not reward_list[i] then
            break
        end
        --达到场次、
        if role_info.today_match_times >= reward_list[i].match_times and flag_list[32 - reward_list[i].seq] == 0 then
            return 1
        end
    end
    return 0
end

-- 男鬼魂
function KF3V3WGData:GetGhostModelResId()
    return self.ghost_model_id
end

-- 女鬼魂
function KF3V3WGData:GetGhostModelResIdNv()
    return self.ghost_model_id_nv
end

-------------------------------------