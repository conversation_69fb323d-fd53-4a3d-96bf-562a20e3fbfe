-- 天下第一准备场景
WorldsNO1ScenePrepareLogic = WorldsNO1ScenePrepareLogic or BaseClass(CrossServerSceneLogic)

function WorldsNO1ScenePrepareLogic:__init()
end

function WorldsNO1ScenePrepareLogic:__delete()
end

-- 进入场景
function WorldsNO1ScenePrepareLogic:Enter(old_scene_type, new_scene_type)
	CrossServerSceneLogic.Enter(self, old_scene_type, new_scene_type)

	ViewManager.Instance:CloseAll()
	MainuiWGCtrl.Instance.view:SetAttackMode(ATTACK_MODE.PEACE)
    MainuiWGCtrl.Instance:SetOtherContents(true)
    MainuiWGCtrl.Instance:SetTaskContents(false)
	ViewManager.Instance:Open(GuideModuleName.WorldsNO1SceneView)

	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.WORLDS_NO1)
	if activity_status then
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(activity_status.next_time)
	end

	-- 摄像机切成自由旋转
	if CAMERA_TYPE ~= CameraType.Free then
        MainuiWGCtrl.Instance:ChangeCameraMode()
    end
end

function WorldsNO1ScenePrepareLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self,old_scene_type, new_scene_type)
	if old_scene_type ~= new_scene_type then
		ViewManager.Instance:CloseAll()
        MainuiWGCtrl.Instance:SetOtherContents(false)
        MainuiWGCtrl.Instance:SetTaskContents(true)
		MainuiWGCtrl.Instance:ResetTaskPanel()
		-- MainuiWGCtrl.Instance.view:SetTaskCallBack(nil)
		ViewManager.Instance:Close(GuideModuleName.WorldsNO1SceneView)
	end
end

function WorldsNO1ScenePrepareLogic:IsEnemy()
	return false
end

function WorldsNO1ScenePrepareLogic:IsRoleEnemy(target_obj, main_role)
	return false
end