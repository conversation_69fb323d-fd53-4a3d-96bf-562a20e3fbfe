ArtifactAttrView = ArtifactAttrView or BaseClass(SafeBaseView)

function ArtifactAttrView:__init()
	self:SetMaskBg(true, true)
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_attr_tip")
end

function ArtifactAttrView:__delete()

end

function ArtifactAttrView:ReleaseCallBack()
	if self.artifact_attr_list then
		for k, v in pairs(self.artifact_attr_list) do
			v:DeleteMe()
		end
		self.artifact_attr_list = nil
	end
end

function ArtifactAttrView:LoadCallBack()
	if not self.artifact_attr_list then
		self.artifact_attr_list = {}
		local node_num = self.node_list["attr_list"].transform.childCount
		for i = 1, node_num do
			self.artifact_attr_list[i] = CommonAddAttrRender.New(self.node_list["attr_list"]:FindObj("attr_" .. i))
			self.artifact_attr_list[i]:SetAttrNameNeedSpace(true)
		end
	end
end

function ArtifactAttrView:SetDataAndOpen(data)
	self.attr_show_type = data.attr_show_type
	self.artifact_seq = data.artifact_seq
	if not self:IsOpen() then
		self:Open()
	else
		self:Flush()
	end
end

function ArtifactAttrView:OnFlush(param_t)
	local view_title
	if self.attr_show_type == 2 then
		view_title = Language.Artifact.UpStarAttrView
	elseif self.attr_show_type == 3 then
		view_title = Language.Artifact.AwakeAttrView
	end
	self.node_list.attr_title_name.text.text = view_title
	self:FlushUpLevelAtrrInfo()
end

-- 刷新属性
function ArtifactAttrView:FlushUpLevelAtrrInfo()
	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.artifact_seq)
	local attr_list = ArtifactWGData.Instance:GetArtifactAllAttrList(self.artifact_seq,
		cur_artifact_data.level, cur_artifact_data.star_level, cur_artifact_data.awake_level,  self.attr_show_type)

	for k, v in pairs(self.artifact_attr_list) do
		v:SetData(attr_list[k])
	end
	
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["attr_root"].rect)
end