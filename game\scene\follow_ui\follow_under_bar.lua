FollowUnderBar = FollowUnderBar or BaseClass(BaseRender)

function FollowUnderBar:__init()
	self.obj_type = nil
	self.follow_parent = nil

	self.show_type = FOLLOW_UI_UNDER_BAR_TYPE.TIP
	self.call_back = nil
	self.show_param = nil
	self.is_loader = false
	self.timer_str = ""
	self.under_bar_vis = false
end

function FollowUnderBar:__delete()
	if CountDown.Instance:HasCountDown("follow_under_bar_timer") then
		CountDown.Instance:RemoveCountDown("follow_under_bar_timer")
	end

	self.show_type = FOLLOW_UI_UNDER_BAR_TYPE.TIP
	self.call_back = nil
	self.show_param = nil
	self.is_loader = false
	self.timer_str = ""
	self.follow_parent = nil
	self.under_bar_vis = false
end

function FollowUnderBar:SetFollowParent(obj_type, follow_parent)
	self.obj_type = obj_type
	if follow_parent then
		self.follow_parent = follow_parent.transform
	end

	if self.is_loader then
		return
	end

	if self.show_param == nil then
		return
	end

	self:CreateRootNode()
end

function FollowUnderBar:CreateRootNode()
	if IsNil(self.follow_parent) then
		return
	end

	self.is_loader = true
	self.async_loader = AllocAsyncLoader(self, "root_loader")
	self.async_loader:SetIsUseObjPool(true)
	self.async_loader:SetLoadPriority(ResLoadPriority.low)
	self.async_loader:SetParent(self.follow_parent, false)
	self.async_loader:Load("uis/view/miscpre_load_prefab", "UnderBar",
		function (gameobj)
			if IsNil(self.follow_parent) then
				self.async_loader:Destroy()
				self.is_loader = false
				return
			end

			self:SetInstance(gameobj)

			self.view:SetLocalPosition(0, -10, 0)
			self.view:SetActive(true)
			self:SetUnderBarVisiable(true)

			self:UpdateInfo()
		end)
end

function FollowUnderBar:SetShowInfo(show_type, param, timer_str, call_back)
	if CountDown.Instance:HasCountDown("follow_under_bar_timer") then
		CountDown.Instance:RemoveCountDown("follow_under_bar_timer")
	end

	if show_type == nil or param == nil then
		return
	end

	self.show_type = show_type
	self.call_back = call_back
	self.show_param = param
	self.timer_str = timer_str

	if not self.is_loader then
		self:CreateRootNode()
		return
	end

	self:UpdateInfo()
end

function FollowUnderBar:UpdateInfo()
	if CountDown.Instance:HasCountDown("follow_under_bar_timer") then
		CountDown.Instance:RemoveCountDown("follow_under_bar_timer")
	end

	if self:IsNil() or not self:UnderBarIsVisiable()then
		return
	end

	if not self.is_loader then
		return
	end

	if self.show_type == nil or self.show_param == nil then
		return
	end

	if self.show_type == FOLLOW_UI_UNDER_BAR_TYPE.TIP then
		self.node_list.silder_root:SetActive(false)
		self.node_list.str_root:SetActive(true)

		self.node_list.str_tip.text.text = self.show_param
	elseif self.show_type == FOLLOW_UI_UNDER_BAR_TYPE.TIMER then
		self.node_list.silder_root:SetActive(false)
		self.node_list.str_root:SetActive(true)
		if self.show_param > 0 and self.timer_str ~= nil and self.timer_str ~= "" then
			self:AddTimer(self.show_param, self.timer_str)
		end
	elseif self.show_type == FOLLOW_UI_UNDER_BAR_TYPE.SLIDER then
		self.node_list.silder_root:SetActive(true)
		self.node_list.str_root:SetActive(false)

		self.node_list.silder_root.slider.value = self.show_param
		self.node_list.str_slider.text.text = self.timer_str
	end
end

function FollowUnderBar:AddTimer(time, str)
	if CountDown.Instance:HasCountDown("follow_under_bar_timer") then
		CountDown.Instance:RemoveCountDown("follow_under_bar_timer")
	end

	if time == nil or str == nil then
		return
	end

	if time == 0 or str == "" then
		return
	end

	CountDownManager.Instance:AddCountDown("follow_under_bar_timer", BindTool.Bind1(self.FlushCountDown, self), BindTool.Bind1(self.CompleteCountDown, self), time, nil, 1)
end

function FollowUnderBar:FlushCountDown(elapse_time, total_time)
	if self.node_list == nil then
		if CountDown.Instance:HasCountDown("follow_under_bar_timer") then
			CountDown.Instance:RemoveCountDown("follow_under_bar_timer")
		end

		return
	end

	if self.node_list.str_tip ~= nil and self.timer_str ~= nil and self.timer_str ~= "" then
		local value = math.floor(total_time - elapse_time)
		value = string.format(self.timer_str, value)

		self.node_list.str_tip.text.text = value
	end
end

function FollowUnderBar:CompleteCountDown()
	if CountDown.Instance:HasCountDown("follow_under_bar_timer") then
		CountDown.Instance:RemoveCountDown("follow_under_bar_timer")
	end

	if self.show_type ~= nil and self.show_type == FOLLOW_UI_UNDER_BAR_TYPE.TIMER then
		if self.call_back then
			self.call_back()
		end
	end
end

function FollowUnderBar:SetUnderBarVisiable(value)
	if CountDown.Instance:HasCountDown("follow_under_bar_timer") then
		CountDown.Instance:RemoveCountDown("follow_under_bar_timer")
	end

	local old_vis = self.under_bar_vis
	self.under_bar_vis = value
	if not old_vis and value then
		self:UpdateInfo()
	end
end

function FollowUnderBar:UnderBarIsVisiable()
	return self.under_bar_vis
end