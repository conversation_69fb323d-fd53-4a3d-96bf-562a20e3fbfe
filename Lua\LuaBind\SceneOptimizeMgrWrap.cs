﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class SceneOptimizeMgrWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(SceneOptimizeMgr), typeof(System.Object));
		<PERSON><PERSON>Function("SetStaticBatchThreadshold", SetStaticBatchThreadshold);
		<PERSON><PERSON>unction("StaticBatch", StaticBatch);
		<PERSON><PERSON>unction("SyncLODLightMap", SyncLODLightMap);
		L<PERSON>RegFunction("SetCenterPoint", SetCenterPoint);
		<PERSON><PERSON>RegFunction("SetCullDistances", SetCullDistances);
		<PERSON><PERSON>Function("SetCullDistanceFactor", SetCullDistanceFactor);
		<PERSON><PERSON>unction("GetIsEnableGPUInstancing", GetIsEnableGPUInstancing);
		<PERSON><PERSON>RegFunction("SetIsEnableGPUInstancing", SetIsEnableGPUInstancing);
		<PERSON><PERSON>unction("SetCullEnable", SetCullEnable);
		<PERSON><PERSON>RegFunction("UpdatePlanarReflectionShow", UpdatePlanarReflectionShow);
		<PERSON><PERSON>ction("New", _CreateSceneOptimizeMgr);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateSceneOptimizeMgr(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				SceneOptimizeMgr obj = new SceneOptimizeMgr();
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: SceneOptimizeMgr.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetStaticBatchThreadshold(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
			SceneOptimizeMgr.SetStaticBatchThreadshold(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StaticBatch(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			SceneOptimizeMgr.StaticBatch();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SyncLODLightMap(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			SceneOptimizeMgr.SyncLODLightMap();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetCenterPoint(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 1);
			SceneOptimizeMgr.SetCenterPoint(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetCullDistances(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			float[] arg0 = ToLua.CheckNumberArray<float>(L, 1);
			SceneOptimizeMgr.SetCullDistances(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetCullDistanceFactor(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 1);
			SceneOptimizeMgr.SetCullDistanceFactor(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetIsEnableGPUInstancing(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = SceneOptimizeMgr.GetIsEnableGPUInstancing();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIsEnableGPUInstancing(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
			SceneOptimizeMgr.SetIsEnableGPUInstancing(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetCullEnable(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
				SceneOptimizeMgr.SetCullEnable(arg0);
				return 0;
			}
			else if (count == 2)
			{
				bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
				bool arg1 = LuaDLL.luaL_checkboolean(L, 2);
				SceneOptimizeMgr.SetCullEnable(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: SceneOptimizeMgr.SetCullEnable");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UpdatePlanarReflectionShow(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
			SceneOptimizeMgr.UpdatePlanarReflectionShow(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

