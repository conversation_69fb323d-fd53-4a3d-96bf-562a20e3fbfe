MarryWGData = MarryWGData or BaseClass()
MarryWGData.ProposeInfo =
{
	marry_type = 0, 			--结婚类型
	req_uid = 0,			--求婚人id
	GameName = "",				--名字
	shizhuang_photoframe = 0,
	prof = "",
	sex = "",					--性别
	lover_marry_type_flag = {}, --对方是否购买过标识
	marry_reward_limit_item_id = {}, --获得的物品
}

MarryGatherId = 7025  --酒席id

MarryWGData.CurSelectSlot = -1
function MarryWGData:__init()
	if MarryWGData.Instance ~= nil then
		ErrorLog("[MarryWGData] Attemp to create a singleton twice !")
	end
	MarryWGData.Instance = self
	self.ring_info_list = {}
	self.equip_lover_data_list = {}
	self:InitRingList()
	self.all_attr = {"gongji", "maxhp", "fangyu", "mingzhong", "shanbi", "baoji", "jianren"}
	self.notify_data_change_callback_list = {}

	self.select_time_seq = nil
	self.qy_fb_scene_info = {}
	-- self.qy_fb_info = {}
	self.uplevel_gold = 0
	self.mate_qingyuan_value = 0
	self.cur_yaoqing_listindex_value = 0
	self.seeking_data_list = {}
	self.qingyuan_card_level_list = {}
	self.lovebox_buy_flag = 0
	self.fetch_reward_type = 0
	self.cd_end_time = 0
	self.lyl_change_role_cd_end_time = 0

	self.xunyou_pos = {
		is_own = 0,
		x = 0,
		y = 0,
		obj_id = 0,
		scene_id = 0,
	}

	--------------------------------宝宝-------------------------------------------
	self.baby_type_list = {}
	self.baby_flag_list = {}

	--------------------------------宝宝-------------------------------------------

	self.all_halo_active_flag = {}

	self.marry_title_flag = {}

	----------- 印记 -------------------
	self.love_stamp_level = 1
	self.love_stamp_star = 1
	self.current_score = 0
	self.own_love_stamp_score = 0
    self.lover_is_online = 0

	self.love_mark_cfg = ConfigManager.Instance:GetAutoConfig("lovestamp_auto").stamp or {}
	self.mark_img_cfg = ConfigManager.Instance:GetAutoConfig("lovestamp_auto").img or {}

	self.use_img_id = 0
	----------- 印记 -------------------

	self.card_level_list = {}

	---------------结婚预约--------------
	self.role_msg_info = {}
	self.yuyue_list_info = {}

	-------------------------------------
	self.wedding_info = {}

	self.applicantInfo_info = {}
	self.wedding_role_info = {}

	self.lover_info = {}
	self.common_win_data = {}

	self.want_yaoqing_binke = {}
	self.cur_select_love = {}  --当前选中的求婚对象

	self.qingyuancfg = ConfigManager.Instance:GetAutoConfig("qingyuanconfig_auto")
	self.baby_auto_cfg = ConfigManager.Instance:GetAutoConfig("baby_cfg_auto")
	self.baby_auto_map_cfg = ListToMap(self.baby_auto_cfg.active, "type", "id")
	self.xunyou_path = self.qingyuancfg.hunyan_xunyou_path

    self.profess_gift_cfg = self.qingyuancfg.profess_gift
	self.equip_uplevel = ListToMap(self.qingyuancfg.uplevel, "equip_id", "star")

	--表白墙相关
	self.profess_cfg = ListToMap(self.qingyuancfg.profess_grade, "level")
	self.tongxinsuo_skill_cfg = ListToMap(self.qingyuancfg.upskill, "skill_seq","level")
	self.profess_other_cfg = self.qingyuancfg.profess_other[1]


    self.qingyuan_fb_monster = self.qingyuancfg.lover_fb_flush_monster
	--同心锁相关
	self.m_equipment_auto = ConfigManager.Instance:GetAutoItemConfig("equipment_auto")

	--结婚折扣相关
	self.marry_discount_cfg = ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto")

	--寻缘
	self.xunyuan_random_cfg = self.qingyuancfg.xunyuan_random

	-- 送花积分
	self.flower_score_upgrade_cfg = self.qingyuancfg.flower_score_upgrade
	self.flower_score_upgrade_level = -1

	self:RegisterRemind()
	--当日爱情宝匣是否提醒过了
	self.is_remind_red = false
	--日期改变监听
    self.marry_love_event = GlobalEventSystem:Bind(OtherEventType.ROLE_ONLINE_CHANGE,BindTool.Bind(self.OnOtherRoleOnlineChange, self))
	--仙娃最大阶数
	self.baby_max_grade = self:GetBabyMaxGrade() or 0
	--是否巡游参数
	self.is_trans_to_xunyou_scence = 0
	--背包物品改变监听
	self:InitItemDataChange()
	self.marry_fb_view_opened = false
	self.society_gift_view_opened = false

	self.marry_recommend_role_list = {}
	self.xunyuan_select_sex = 2

	self:RegisterLevelUpRemindInBag(RemindName.Marry_Equip)
    self:RegisterBabyRemindInBag(RemindName.Marry_Baby)
    self:RegisterProposeRemindInBag(RemindName.Marry_Propose)
    self:RegisterFlowerSendRemindInBag(RemindName.Marry_Send_Flowers)
end

function MarryWGData:__delete()
	MarryWGData.Instance = nil
	self.notify_data_change_callback_list = {}
	self.qingyuancfg = nil
	self.baby_auto_cfg = nil
	self.is_remind_red = nil
	self.has_set_profess_info = nil
	self.common_win_data = {}
	self.want_yaoqing_binke = nil
	self.lyl_change_role_cd_end_time = 0
	self.is_had_show_main_ui_chat = false
	self.marry_fb_view_opened = false
	self.society_gift_view_opened = false
    if self.marry_love_event then
		GlobalEventSystem:UnBind(self.marry_love_event)
		self.marry_love_event = nil
	end
	local remindmanager_instance = RemindManager.Instance

	remindmanager_instance:UnRegister(RemindName.Marry_GetMarry)
	remindmanager_instance:UnRegister(RemindName.Marry_Equip)
	remindmanager_instance:UnRegister(RemindName.Marry_Baby)
	remindmanager_instance:UnRegister(RemindName.Marry_BaoXia)
	remindmanager_instance:UnRegister(RemindName.Marry_Copy)
	remindmanager_instance:UnRegister(RemindName.Marry_Propose)
	remindmanager_instance:UnRegister(RemindName.HUNYAN)
	remindmanager_instance:UnRegister(RemindName.MarryNotice)
	remindmanager_instance:UnRegister(RemindName.Marry_PaiQi)
	remindmanager_instance:UnRegister(RemindName.Marry_Flowers)
	remindmanager_instance:UnRegister(RemindName.Marry_Send_Flowers)


	if CountDownManager.Instance:HasCountDown("getbaby_redhint") then
		CountDownManager.Instance:RemoveCountDown("getbaby_redhint")
	end

	if self.bind_day_pass then
		GlobalEventSystem:UnBind(self.bind_day_pass)
		self.bind_day_pass = nil
	end
	self.is_trans_to_xunyou_scence = nil
	if self.item_data_event then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
		self.item_data_event = nil
	end
	if self.fun_open_event then
		FunOpen.Instance:UnNotifyFunOpen(self.fun_open_event)
		self.fun_open_event = nil
	end
end

function MarryWGData:RegisterRemind()
	local remindmanager_instance = RemindManager.Instance

	remindmanager_instance:Register(RemindName.Marry_GetMarry, BindTool.Bind(self.GetMarryJiehunRemind, self)) --结婚
	remindmanager_instance:Register(RemindName.Marry_Equip, BindTool.Bind(self.CanLevelUpRemind, self)) 		--情缘装备
	remindmanager_instance:Register(RemindName.Marry_Baby, BindTool.Bind(self.GetBabyRemind, self)) 		--宝宝
	remindmanager_instance:Register(RemindName.Marry_BaoXia, BindTool.Bind(self.CanBaoXiaRemind, self)) 	--宝匣
	remindmanager_instance:Register(RemindName.Marry_Copy, BindTool.Bind(self.CanFBTiaoZhanRemind, self))	--情缘副本
	remindmanager_instance:Register(RemindName.Marry_Propose, BindTool.Bind(self.GetProposeRemind, self))	--表白
	remindmanager_instance:Register(RemindName.HUNYAN, BindTool.Bind(self.GetWeddingApplicantRemind, self)) 	--婚宴/申请参加婚宴的红点提示
	remindmanager_instance:Register(RemindName.MarryNotice, BindTool.Bind(self.GetMarryNoticeRemind, self)) 	--结婚预告
	remindmanager_instance:Register(RemindName.Marry_PaiQi, BindTool.Bind(self.GetMarryPaiQiRemind, self)) 	--排期
	remindmanager_instance:Register(RemindName.Marry_Flowers, BindTool.Bind(self.GetSendFlowersUpgradeRemind, self)) 	--赠花进阶
	remindmanager_instance:Register(RemindName.Marry_Send_Flowers, BindTool.Bind(self.GetSendFlowersItemRemind, self)) 	--情缘赠花
	
end

function MarryWGData:RegisterLevelUpRemindInBag(remind_name)
    local stuff_id = self.qingyuancfg.uplevel_stuff[1].stuff_id
    local item_id_list = {stuff_id}
    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function MarryWGData:RegisterBabyRemindInBag(remind_name)
    local map = {}

    for _, v in pairs(self.baby_auto_cfg.active) do
        map[v.item_id] = true
    end

    local baby_other_cfg = self:GetBabyOtherCfg()
	map[baby_other_cfg.add_bless_item_id] = true

    local item_id_list = {}
    for k,v in pairs(map) do
        table.insert(item_id_list, k)
    end

    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function MarryWGData:RegisterProposeRemindInBag(remind_name)
    local map = {}

    for _, v in pairs(self.profess_gift_cfg) do
        map[v.gift_id] = true
    end
    local item_id_list = {}
    for k,v in pairs(map) do
        table.insert(item_id_list, k)
    end
    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function MarryWGData:RegisterFlowerSendRemindInBag(remind_name)
	local item_id_list = {}
	local slower_data = self:GetSendFlowerCfg()
	for k, v in pairs(slower_data) do
		table.insert(item_id_list, v.item_id)
	end

    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end



function MarryWGData:GetFirstBabyInfo()
	return self.baby_auto_cfg.active[1]
end

function MarryWGData:GetIsBabyInfo(item_id)
	local baby_list_cfg = self.baby_auto_cfg.active
	for k,v in pairs(baby_list_cfg) do
		if v.item_id == item_id then
			return true
		end
	end
	return false
end

function MarryWGData:GetActiveCfgByTypeAndId(type, id)
	return self.baby_auto_map_cfg[type] and self.baby_auto_map_cfg[type][id]
end

function MarryWGData:InitRingList()
	for i = 0, 3 do
		self.ring_info_list[i] = CommonStruct.ItemDataWrapper()
	end
end

-- 设置仙侣情缘值
function MarryWGData:SetMateQyValue(value)
	if value then
		self.mate_qingyuan_value = value
	end
end

-- 设置仙侣情缘值
function MarryWGData:GetMateQyValue()
	return self.mate_qingyuan_value	or 0
end

function MarryWGData:GetQingyuanCfg()
	if self.qingyuancfg then
		return self.qingyuancfg
	end
	return nil
end



function MarryWGData:SetQyFbSceneInfo(info)
	if nil == info then return end
	self.qy_fb_scene_info.kill_monster_num = info.kill_monster_num						-- 当前杀怪数
	self.qy_fb_scene_info.kill_boss_num = info.kill_boss_num							-- 当前杀boss数
	self.qy_fb_scene_info.is_pass = info.is_pass										-- 是否通关 默认值0
	self.qy_fb_scene_info.is_finish = info.is_finish									-- 是否完成 默认值0
	self.qy_fb_scene_info.choose_end_timestamp = info.choose_end_timestamp	            -- 选择倒计时时间
	self.qy_fb_scene_info.fb_time_out = info.fb_time_out	            				-- 剩余副本计时时时间
	self.qy_fb_scene_info.is_same = info.is_same                                        -- 选择是否一样
	self.qy_fb_scene_info.get_exp = info.get_exp                                        -- 获得经验
	self.qy_fb_scene_info.rand_num_list = info.rand_num_list					        -- 心有灵犀
	self.qy_fb_scene_info.prepare_end_timestamp = info.prepare_end_timestamp
	self.qy_fb_scene_info.is_helper	= info.is_helper
	self.qy_fb_scene_info.assist_time = info.assist_time
    self.qy_fb_scene_info.is_couple = info.is_couple
    self.qy_fb_scene_info.wave_num = info.wave_num
	self:SetXieZhuTimes(info.assist_time)
end

function MarryWGData:GetQyFbSceneInfo()
	return self.qy_fb_scene_info
end

function MarryWGData:GetFbTimeOut()
	return self.qy_fb_scene_info.fb_time_out or 0
end

function MarryWGData:SetQyFbInfo(info)
	if nil == info then return end
	self.qy_fb_info = {}
	self.qy_fb_info.join_fb_times = info.join_fb_times						-- 进入副本次数
	-- self.qy_fb_info.buy_fb_join_times = info.buy_fb_join_times				-- 购买次数
	self.qy_fb_info.self_buy_jion_fb_times = info.self_buy_jion_fb_times	-- 购买次数限制
	self.qy_fb_info.lover_buy_fb_times = info.lover_buy_fb_times			-- 伴侣购买次数
	self.qy_fb_info.help_fb_times = info.help_fb_times 						-- 协助次数
    self.qy_fb_info.total_lover_buy_fb_times = info.total_lover_buy_fb_times-- 情缘结婚后购买次数 为0带
	self:SetXieZhuTimes(info.help_fb_times)
end

function MarryWGData:GetQyFbInfo()
	return self.qy_fb_info
end

function MarryWGData:SetXieZhuTimes(num)
	self.help_fb_times = num
end

function MarryWGData:GetXieZhuTimes()
	return self.help_fb_times or 0
end

function MarryWGData:GetXieZhuTotalTimes()
	return self:GetMarryOtherCfg().help_times
end

function MarryWGData:GetEnterTimes()
	local fb_info = self:GetQyFbInfo()
	if nil == fb_info then
		return 0
	end
	return fb_info.join_fb_times
end

function MarryWGData:GetTotalTimes()
	local fb_info = self:GetQyFbInfo()
	if nil == fb_info then
		return 0
	end
	local other_cfg = self:GetMarryOtherCfg()
	return other_cfg.fb_free_times_limit + fb_info.self_buy_jion_fb_times + fb_info.total_lover_buy_fb_times
end

-- 设置缘装备信息
function MarryWGData:SetRingInfo(info_list)
	self.ring_info_list = info_list.equip_data_list
	self.uplevel_gold = info_list.need_cost_gold
	self.equip_lover_data_list = info_list.equip_lover_data_list
	for k,v in pairs(self.notify_data_change_callback_list) do  --物品有变化，通知观察者，带消息体
		v()
	end
end

-- 服务器bug,客户端先解决，以后请删除
function MarryWGData:IsCanPutOnRing(index)
	if nil == self.ring_info_list[index - 1] then
		return true
	end

	local prve_item_id = self.ring_info_list[index - 1].item_id
	if prve_item_id > 0 then
		return true
	end

	return false
end

--绑定数据改变时的回调方法.用于任意物品有更新时进行回调
function MarryWGData:NotifyDataChangeCallBack(callback)
	self:UnNotifyDataChangeCallBack(callback)
	self.notify_data_change_callback_list[#self.notify_data_change_callback_list + 1] = callback
end

--移除绑定回调
function MarryWGData:UnNotifyDataChangeCallBack(callback)
	for k,v in pairs(self.notify_data_change_callback_list) do
		if v == callback then
			self.notify_data_change_callback_list[k] = nil
			return
		end
	end
end

-- 获取婚戒
function MarryWGData:GetRingInfo(index)
	-- local equip_info = EquipWGData.Instance:GetGridData(GameEnum.EQUIP_INDEX_HUNJIE)
	-- if equip_info then
	-- 	return self.ring_info_list[0]
    -- end
    if index == 0 and self.ring_info_list[0].item_id == 0 then
        self.ring_info_list[0].item_id = MARRY_OTHER_TYPE.ITEM_ID_TXS  --要加显示未激活属性面板，不得已而为之，，
    end
    -- print_error("FFF===== self.ring_info_list[index]", index, self.ring_info_list[index])
	return self.ring_info_list[index]
end

function MarryWGData:GetAllRingInfo()
	return self.ring_info_list
end

-- 获取装备升级元宝
function MarryWGData:GetRingUpGold()
	return self.uplevel_gold
end
-- 获取某情缘装备位置信息
function MarryWGData:GetQyEquipInfoBySlot(slot)
	if nil ~= slot and self.ring_info_list then
		return self.ring_info_list[slot]
	end

	return nil
end

-- 获取情侣某情缘装备位置信息
function MarryWGData:GetLoverQyEquipInfoBySlot(slot)
	if nil ~= slot and self.equip_lover_data_list then
		return self.equip_lover_data_list[slot]
	end

	return nil
end

-- 查看某位置是否有情缘装备
function MarryWGData:CheckHasEquipBySlot(slot)
	if nil == slot then return false end
	local equip_info = self.ring_info_list[slot]
	if equip_info and equip_info.item_id > 0 then
		return true
	end
	return false
end

-- 查看情侣某位置是否有情缘装备
function MarryWGData:CheckLoverHasEquipBySlot(slot)
	if nil == slot then return false end
	local equip_info = self.equip_lover_data_list[slot]
	if equip_info and equip_info.equip_id > 0 then
		return true
	end
	return false
end

-- 根据装备id是否比穿戴的装备更好
function MarryWGData:GetIsBatterEquip( id )
	local equip_index = self:GetEquipCfgById(id).eq_type
	local equip_info = self:GetQyEquipInfoBySlot(equip_index)
	local cur_capability = 0
	local dress_capability = 0
	local is_up = true
	if equip_info and equip_info.item_id > 0 then
		--当前id的评分
		local star_level = 1
		local qingyuan_equip_cfg = self:GetEquipCfgById(id, star_level)
		if nil ~= qingyuan_equip_cfg then
			cur_capability = AttributeMgr.GetCapability(AttributeMgr.GetAttributteByClass(qingyuan_equip_cfg))
		end
		local cfg = self:GetEquipCfgById(equip_info.item_id, equip_info.param.star_level)
		--身上装备的评分
		if nil ~= cfg then
			dress_capability = AttributeMgr.GetCapability(AttributeMgr.GetAttributteByClass(cfg))
		end
		is_up = cur_capability > dress_capability
	end
	return is_up
end

-- 获取结婚配置信息
function MarryWGData:GetMarryCfg()
	return self.qingyuancfg.marry_cfg
end

-- 获取结婚折扣配置信息
function MarryWGData:GetMarryDiscountCfg()
	return self.marry_discount_cfg.marry_rebate
end

-- 获取婚礼祝福配置信息
function MarryWGData:GetMarryWeddItemIdBySeq(type, seq)
	local cfg = self.qingyuancfg.wedding_blessing
	for i,v in ipairs(cfg) do
		if v.blessing_type == type and v.seq == seq then
			return v
		end
	end
end

-- 根据结婚类型获取结婚配置
function MarryWGData:GetOneMarryCfgByType(type)
	if nil == type then return end
	for _,v in pairs(self.qingyuancfg.marry_cfg) do
		if v and v.marry_type == type then
			return v
		end
	end
	print_error("connot find this type of gift!!,type == ",type)
	return nil
end

-- 获取结婚其他配置类标
function MarryWGData:GetMarryOtherCfg()
	return self.qingyuancfg.other[1]
end

-- 通过戒指id获取结婚类型
function MarryWGData:GetMarryTypeById(item_id)
	if item_id then
		for k,v in pairs(self.qingyuancfg.marry_cfg) do
			if v and v.reward_item
				and v.reward_item.item_id == item_id then
				return v.marry_type
			end
		end
	end
	return 0
end

-- 根据id获取情缘装备配置
function MarryWGData:GetEquipCfgById(id, star)
	if nil == id then return end
	star = star or 1

	if self.equip_uplevel[id] then
		return self.equip_uplevel[id][star]
	end
	return nil
end

-- 根据装备位置获取情缘装备配置
function MarryWGData:GetEquipCfgByIndex(index, level)
	if not index then
		return nil
	end
	for _,v in pairs(self.qingyuancfg.uplevel) do
		if v.eq_type == index and (v.level == level or nil == level) then
			return v
		end
	end
	return nil
end

-- 根据id获取情缘装备配置
function MarryWGData:GetEquipIsMax(id, star)
	local cur_cfg = self:GetEquipCfgById(id, star)
	if cur_cfg then
		for k,v in pairs(self.qingyuancfg.uplevel) do
			if cur_cfg.level + 1 == v.level then
				return false
			end
		end
		return true
	end
	return false
end

function MarryWGData:GetMaxWaveNum(qingyuanValue)
	local maxWaves = 0
	for k,v in pairs(self.qingyuancfg.waves_count) do
		if v.intimacy <= qingyuanValue then
			maxWaves = v.waves_num
		else
			return maxWaves
		end
	end
	return maxWaves
end


-- 副本波数tips提示
function MarryWGData:GetFbTipsList()
	local list = {}
	for _,v in pairs(self.qingyuancfg.waves_count) do
		local can_add = true
		for k,v1 in pairs(list) do
			if v1.intimacy == v.intimacy then
				if v.waves_num > v1.waves_num then
					list[k] = v
				end
				can_add = false
			end
		end
		if can_add then
			table.insert(list, v)
		end
	end
	return list
end

-- 获取情缘装备属性
function MarryWGData:GetRquipAttr(id)
	if nil == id then return end
	local attr_list = {}
	local equip_cfg = self:GetEquipCfgById(id)
	if equip_cfg then
		for _,v in pairs(self.all_attr) do
			if equip_cfg[v] and equip_cfg[v] > 0 then
				attr_list[v] = equip_cfg[v]
			end
		end
	end
	return attr_list
end

-- 设置自动打造
function MarryWGData:SetingEquipChoose(choose_list)
	if nil == choose_list then return end
	self.auto_build_list = choose_list
end

-- 检查是否能够结婚
function MarryWGData:CheckCanMarry(user_info, ignore_lv)
	if nil == user_info then
		return false, Language.Marry.MarryNoLoverTips
	end
	local lover_name = user_info.gamename or ""

	local qy_cfg_other = self.qingyuancfg.other[1] or {}
	local lv_lim = qy_cfg_other.marry_limit_level or 0
	local role_lv = RoleWGData.Instance.role_vo.level or 0
	local lover_lv = ignore_lv and lv_lim or user_info.level or 0
	if role_lv < lv_lim or lover_lv < lv_lim then
		local lan = role_lv < lv_lim and Language.Marry.MarryMyLevelLimit or Language.Marry.MarryLoverLevelLimit
		local dec = string.format(lan, RoleWGData.GetLevelString(lv_lim))		-- 等级因素
		return false, dec
	end

	local role_sex = RoleWGData.Instance.role_vo.sex or 0
	local lover_sex = user_info.sex or 0
	if role_sex == lover_sex then
		return false, Language.Marry.MarrySexLimit							 	 -- 性别因素
	end

	local role_lover = RoleWGData.Instance.role_vo.lover_uid or 0
	if role_lover ~= 0 then
		return false, Language.Marry.MarryOnlyLimit 							 -- 重婚因素
	end

	local role_id = RoleWGData.Instance:InCrossGetOriginUid() or 0
	local lover_id = user_info.user_id or 0
	if role_id == lover_id then
		return false, Language.Marry.MarrySelfLimit 							 -- 自婚因素
	end

	return true, lover_name
end

-- 征婚列表信息
function MarryWGData:SetMarriageSeekingInfo(protocol)
	self.seeking_data_list = protocol.marriage_seeking_list
end

function MarryWGData:GetMarriageSeekingInfo()
	table.sort(self.seeking_data_list, MarryWGData.XunYuanKeySorters("is_online", "time_stamp"))
	return self.seeking_data_list
end

function MarryWGData:SetMarrySeekingReq(protocol)
	self.cd_end_time = protocol.cd_end_time
end

function MarryWGData:GetMarrySeekingReq()
	return self.cd_end_time
end

function MarryWGData:GetIsCanSeeking()
	return self.cd_end_time - TimeWGCtrl.Instance:GetServerTime() <= 0
end

function MarryWGData:GetLoveboxBuyRet()
	return self.lovebox_buy_flag
end

function MarryWGData:GetLoveboxBuyLoverRet()
	return self.lovebox_buy_loverflag
end

function MarryWGData:GetLoveboxBuyTimeRet()
	--加1秒因为服务器时间稍微有点延迟，影响表现
	if nil == self.lovebox_buytime then
		return 0
	end
	return TimeWGCtrl.Instance:GetServerTime() + 1 - self.lovebox_buytime
end

function MarryWGData:GetLoveboxBuyLoverTimeRet()
	--加1秒因为服务器时间稍微有点延迟，影响表现
	if nil == self.lovebox_lover_buytime then
		return 0
	end
	return TimeWGCtrl.Instance:GetServerTime() + 1 - self.lovebox_lover_buytime
end

-- 宝匣领取信息
function MarryWGData:SetLoveboxFetchReward(protocol)
	self.lovebox_buy_flag = protocol.lovebox_buy_flag
	self.lovebox_buy_loverflag = protocol.lovebox_buy_loverflag
	self.lovebox_buytime = protocol.lovebox_buytime
	self.lovebox_lover_buytime = protocol.lovebox_lover_buytime
	self.fetch_lovebox_buy_reward_flag = protocol.fetch_lovebox_buy_reward_flag
	self.fetch_lovebox_day_reward_flag = bit:d2b(protocol.fetch_lovebox_day_reward_flag)
	self.fetch_lovebox_lover_day_reward_flag = bit:d2b(protocol.fetch_lovebox_lover_day_reward_flag)
	-- print_error("6775==","自己信息",self.lovebox_buy_flag,self.lovebox_buytime,self.fetch_lovebox_buy_reward_flag ,self.fetch_lovebox_day_reward_flag,"\n","对方信息",
	-- 	self.lovebox_buy_loverflag,self.lovebox_lover_buytime,self.fetch_lovebox_lover_day_reward_flag)

end

function MarryWGData:GetLoveboxBuyFetchReward()
	return self.fetch_lovebox_buy_reward_flag
end

function MarryWGData:GetLoveboxDayFetchReward()
	if nil == self.fetch_lovebox_day_reward_flag then
		return -1
	end
	local buy_time = self:GetLoveboxBuyTimeRet()
	local cur_day = math.floor(buy_time/86400) > 29 and 29 or math.floor(buy_time/86400)
	return self.fetch_lovebox_day_reward_flag[32-cur_day]
end

function MarryWGData:GetLoveboxLoverDayFetchReward()
	if nil == self.fetch_lovebox_lover_day_reward_flag then
		return -1
	end
	local buy_time = self:GetLoveboxBuyLoverTimeRet()
	local cur_day = math.floor(buy_time/86400) > 29 and 29 or math.floor(buy_time/86400)
	return self.fetch_lovebox_lover_day_reward_flag[32-cur_day]
end

function MarryWGData:XunYuanKeySorters(sort_key_name1, sort_key_name2)
	return function(a, b)
		local order_a = 100
		local order_b = 100
		if a[sort_key_name1] > b[sort_key_name1] then
			order_a = order_a + 10
		elseif a[sort_key_name1] < b[sort_key_name1] then
			order_b = order_b + 10
		end

		if nil == sort_key_name2 then  return order_a > order_b end

		if a[sort_key_name2] < b[sort_key_name2] then
			order_a = order_a + 1
		elseif a[sort_key_name2] > b[sort_key_name2] then
			order_b = order_b + 1
		end

		return order_a > order_b
	end
end

-- 根据协议更新情缘卡牌等级信息
function MarryWGData:SetQingyuanCardUpdate(protocol)
	self.card_level_list[protocol.card_id] = {index = protocol.card_id, level = protocol.card_level}
end

-- 根据协议更新情缘卡牌列表 4724
function MarryWGData:SetQingyuanCardLevelList(protocol)
	self.card_level_list = protocol.card_level_list
end

-- 获取情缘卡牌等级信息 4724
function MarryWGData:GetQingyuanCardLevelListInfo()
	return self.card_level_list or {}
end

function MarryWGData:GetQingyuanCardLevel(index)
	self.ceshi = self.card_level_list[index]
	return self.ceshi
end


-- 设置情缘卡牌等级信息 014
-- function MarryWGData:SetQiyuanCardInfo(protocol)
-- 	self.qingyuan_card_level_list = protocol.lover_info.qingyuan_card_level_list

-- end

-- -- 获取情缘卡牌等级信息 014
-- function MarryWGData:GetQiyuanCardInfo()
-- 	return self.qingyuan_card_level_list or {}
-- end

-- 根据卡组ID和等级获取卡牌信息
function MarryWGData:GetCardInfoByIdAndLevel(card_id, card_level)
	for k, v in pairs(self.qingyuancfg.qingyuan_card_cfg) do
		if v.card_id == card_id and v.card_level == card_level then
			return v
		end
	end
	return nil
end

function MarryWGData:GetCardAttrByIdAndLevel(qingyuan_card_level_list)
	local attr = AttributePool.AllocAttribute()
	if nil == qingyuan_card_level_list then return attr end
	for k,v in pairs(qingyuan_card_level_list) do
		local card_info = self:GetCardInfoByIdAndLevel(k, v) or{}
		local card_attr = AttributeMgr.GetAttributteByClass(card_info)
		attr = AttributeMgr.AddAttributeAttr(attr, card_attr)
	end

	return attr
end

--获取宝匣购买所需要金钱
function MarryWGData:GetBaoXiaInfo()
	return self.qingyuancfg.love_box[1].need_gold or 0
end

--获取宝匣奖励的绑玉和道具
function MarryWGData:GetBaoXiaRewardBindGold()
	local bind_gold = self.qingyuancfg.love_box[1].bind_gold or 0
	local bind_gold_id = self.qingyuancfg.love_box[1].bind_gold_id or 0
	local reward_item = self.qingyuancfg.love_box[1].reward_item or {}
	return bind_gold,bind_gold_id,reward_item
end

--获取宝匣奖励物品信息
function MarryWGData:GetBaoXiaRewardInfo(day)
	for k,v in pairs(self.qingyuancfg.love_box_daily_reward) do
		if day == v.day then
			return v.show_item
		end
	end
end
--获取爱情宝匣每日刷新时间
function MarryWGData:GetMarryBaoXiaFlushTime()
	return self.qingyuancfg.love_box[1].refresh_oclock or 0
end


-- 获取情缘圣地卡组数量
function MarryWGData:GetQingyuanCardzuSum()
	local max_id = 0
	for  k, v in pairs(self.qingyuancfg.qingyuan_card_cfg) do
		if max_id < v.card_id then
			max_id = v.card_id
		end
	end
	return max_id + 1
end

--获取背包中所有婚戒
function MarryWGData:GetAllRing()
	local all_rings = {}
	for k,v in pairs(ItemWGData.Instance:GetBagItemDataList()) do
		if ItemWGData.Instance:GetIsQingyuanEquip(v.item_id) then
			all_rings[#all_rings + 1] = v
		end
	end

	local item = table.remove(all_rings, 1)
	all_rings[0] = item
	return all_rings
end

 --------------夫妻特效----------
function MarryWGData:SetQingyuanCoupleHaloInfo(protocol)
	self.equiped_couple_halo_type =	protocol.equiped_couple_halo_type
	self.couple_halo_max_type = protocol.couple_halo_max_type or 0
	self.couple_halo_activate_status_list = protocol.couple_halo_activate_status_list or {}
	-- self:GetTotalAttr()
end

function MarryWGData:GetEquipedHalo()
	return self.equiped_couple_halo_type or 1
end

function MarryWGData:GetHaloMaxNum()
	return self.couple_halo_max_type or 0
end

function MarryWGData:GetActiveStatusList()
	return self.couple_halo_activate_status_list or {}
end

function MarryWGData:GetActiveStatusListByIndex(index)
	local couple_halo_activate_status_list = self:GetActiveStatusList()
	return couple_halo_activate_status_list[index]
end
----是否激活了光环
function MarryWGData:GetSelectHaloActiveFlagByIndex(index)
	local couple_halo_activate_status_list = self:GetActiveStatusList()
	if nil == couple_halo_activate_status_list[index] then return 0 end
	local active_flag = couple_halo_activate_status_list[index]
	for k,v in pairs(active_flag) do
		if v == 0 then
			return 0
		end
	end
	return 1
end
 -----所有光环激活标志
function MarryWGData:GetAllHaloActivieFlag()
	local halo_max_num = self:GetHaloMaxNum()
	for i=1,halo_max_num do
		self.all_halo_active_flag[i] = self:GetSelectHaloActiveFlagByIndex(i)
	end
	return self.all_halo_active_flag
end

function MarryWGData:GetHaloConsumeItemCfg(halo_index, item_index)
	local couple_halo_cfg = self.qingyuancfg.couple_halo or {}

	for k,v in pairs(couple_halo_cfg) do
		if v.halo_type == halo_index and v.icon_index == item_index then
			return v
		end
	end
	return {}
end

function MarryWGData:GetTotalAttr()
	local couple_halo_cfg = self.qingyuancfg.couple_halo or {}

	local sum_attribute = AttributePool.AllocAttribute()
	local couple_halo_activate_status_list = self:GetActiveStatusList()
	if nil == next(couple_halo_activate_status_list) or nil == next(couple_halo_cfg) then return sum_attribute end
	for k, v in pairs(couple_halo_cfg) do
		if 1 == couple_halo_activate_status_list[v.halo_type][v.icon_index+1] then
			local attribute = AttributeMgr.GetAttributteByClass(v)
			sum_attribute = AttributeMgr.AddAttributeAttr(sum_attribute, attribute)
		end
	end
	return sum_attribute
end

function MarryWGData:GetHaloEffectResID(halo_type)
	local couple_halo_cfg = self.qingyuancfg.couple_halo or {}

	for k,v in pairs(couple_halo_cfg) do
		if v.halo_type == halo_type then
			return v.res_id
		end
	end
	return 0
end

--是否激活同心锁
function MarryWGData:IsActiveHunjie()
    local equip_info = self:GetRingInfo(0)
    local ring_cfg = MarryWGData.Instance:GetEquipCfgById(equip_info.item_id, equip_info.param.star_level)
    return not IsEmptyTable(ring_cfg)
end

----------------------------------------------------
-- 婚戒升级 红点提示
function MarryWGData:CanLevelUpRemind()
	--是否可以继续升级
	local limit_level, enable = MarryWGData.Instance:GetTongXinSuoLimintLevel()
	if not enable then
		return 0
    end
    if not self:IsActiveHunjie() then
        local stuff_num = ItemWGData.Instance:GetItemNumInBagById(MARRY_OTHER_TYPE.ITEM_ID_TXS)
        if stuff_num >= 1 then
            return 1
        else
            return 0
        end
    end

	local num = 0
	for i = 0, 3 do
		local data = self:GetRingInfo(i)
		if data.item_id == 0 then
			break
		end
		local jihuo_up  = self:RemindHunJie()
		if jihuo_up == 1 then
			num = 1
			break
		end

		--不是最大级
		if not self:GetEquipIsMax(data.item_id, data.param.star_level) and data.param.param1 then
			local cur_level_cfg = self:GetEquipCfgById(data.item_id, data.param.star_level)
			local up_ring_cfg = self.qingyuancfg.uplevel_stuff[1]
			local stuff_num = ItemWGData.Instance:GetItemNumInBagById(up_ring_cfg.stuff_id)
			local total_add_exp = up_ring_cfg.add_exp * stuff_num
			local need_up_level_exp = cur_level_cfg.exp - data.param.param1
			if total_add_exp >= need_up_level_exp then
				num = 1
				break
			end
		end
	end

	-- self:SetMainViewInvateTip(num, MAINUI_TIP_TYPE.Marry_Equip, "marry_hunjie")
	return num
end

-- 副本可以挑战
function MarryWGData:CanFBTiaoZhanRemind()
	local is_open = FunOpen.Instance:GetFunIsOpenedByMouduleName(GuideModuleName.HomesView)
	--策划需求，情缘副本红点本次登录打开界面后消失
	if self.marry_fb_view_opened or not is_open then
		return 0
	end

	local fb_info = self:GetQyFbInfo() or {}
	local join_num = 0
	local num = 0
	local other_cfg = self:GetMarryOtherCfg()
    if next(fb_info) then
        local can_buy_num = other_cfg.fb_buy_times_limit - fb_info.self_buy_jion_fb_times --自己能否购买
        local other_can_buy_num = other_cfg.fb_buy_times_limit - fb_info.lover_buy_fb_times --情缘能否购买
        if can_buy_num > 0 then
            return 1
        end
        join_num = self:GetTotalTimes() - fb_info.join_fb_times
        num = join_num > 0 and 1 or 0
    end
    return num
end

function MarryWGData:SetMarryFBViewOpened()
	self.marry_fb_view_opened = true
	RemindManager.Instance:Fire(RemindName.Marry_Copy)
	HomesWGCtrl.Instance:FlushHomesView()
end

function MarryWGData:SetSocietyGiftOpened(is_open)
	self.society_gift_view_opened = is_open
end

function MarryWGData:GetIsSocietyGiftOpened()
	return self.society_gift_view_opened
end

-- 宝匣每日可以领取
function MarryWGData:CanBaoXiaRemind()
	local love_box_bug = self:GetLoveboxBuyRet() -- 自己
	local buy_fetch = self:GetLoveboxBuyFetchReward()
	local day_fetch = self:GetLoveboxDayFetchReward()
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	local love_box_lover = self:GetLoveboxBuyLoverRet()  -- 对方
	local day_lover_fetch = self:GetLoveboxLoverDayFetchReward()
	local num = 0
	if 1 == love_box_bug and (0 == buy_fetch or 0 == day_fetch) then
		num = 1
	elseif lover_id > 0 and 1 == love_box_lover and 0 == day_lover_fetch then
		num = 1
	elseif lover_id > 0 and 0 == love_box_bug and not self.is_remind_red and RoleWGData.Instance:GetDayFirstLoginFlag() then
		num = 1
	end
	-- self:SetMainViewInvateTip(num, MAINUI_TIP_TYPE.Marry_Equip, "marry_baoxia")
	return num
end

-- 光环可以激活
function MarryWGData:CanLevelGuangHuanRemind()
	local num = 0
	local couple_halo_cfg = self.qingyuancfg.couple_halo or {}

	for k,v in pairs(couple_halo_cfg) do
		local item_num = ItemWGData.Instance:GetItemNumInBagById(v.stuff_id)
		local count = v.stuff_count
		if item_num >= count and self:GuangHuanActivate(v.halo_type, v.icon_index) < 1 then
			num = num + 1
		end
	end
	return num
end

function MarryWGData:GuangHuanActivate(halo_type,icon_index)
	local couple_halo_activate_status_list = self:GetActiveStatusList()
	if nil == couple_halo_activate_status_list[halo_type] then return 0 end
	return couple_halo_activate_status_list[halo_type][icon_index + 1]
end

-- 婚宴可举行 xxxx
function MarryWGData:MarryWeddingRemind()
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	local wedding = self:GetQyFbInfo() or {}
	if lover_id > 0 and wedding.is_hunyan_already_open ~= nil and wedding.is_hunyan_already_open == 0 then
		return 1
	end
	return self:GetMarryTitleRemind()
end

function MarryWGData:GetMarryYuyueRemind()
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	if lover_id <= 0 then
		return 0
	end

    local role_msg_info = self:GetYuYueRoleInfo()
    local times = role_msg_info.marry_count or 0
    if times <= 0 then
        return 0
    end
    local yuyue_data, seq_fornt = self:GetMarryYuYueInfo()
    local today_times = 0
    for k, v in pairs(yuyue_data) do
        if v.seq == role_msg_info.param_ch4 and v.seq == role_msg_info.param_ch8 then
            today_times = today_times +  1
        end
    end
    if today_times >= 2 then
        return 0
    end

    for k, v in pairs(yuyue_data) do
        if v.is_yuyue ~= 1 and v.yuyue_time ~= 1 then
            return 1
        end
    end
    return 0
end

function MarryWGData:GetMarryJiehunRemind()
    if self:GetMarryYuyueRemind() == 1 then
        MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.HunYan_Yuyue, 1, function ()
            MarryWGCtrl.Instance:OpenYuYueView()
            return true
        end)
    else
        MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.HunYan_Yuyue, 0)
    end

    if self:GetMarryTitleRemind() == 1 then
        return 1
    end

	if self:CanBaoXiaRemind() == 1 then
		return 1
	end

    return 0
end

function MarryWGData:GetMarryTitleRemind()
	local get_baby_timestamp, get_baby_flag = MarryWGData.Instance:GetBabyLingQuInfo()
	if get_baby_timestamp > 0 and get_baby_timestamp <= TimeWGCtrl.Instance:GetServerTime() and get_baby_flag == 0 then
		return 1
	end
    if self:GetMarryTitleRemindShow() == 1 then
        return 1
    end
	return 0
end

--此方法暂时不调用，但是后期可能会重新打开，暂不删除
function MarryWGData:GetMarryTitleRemindShow()
	local role_equip_level = self:GetMarryEquipLevel()
	local marry_title_cfg = self:GetMarryTitleCfg()
	local marry_title_flag = self:GetMarryTitleInfo()
	local num = 0
	if marry_title_flag then
		for k,v in pairs(marry_title_cfg) do
			if v.baby_limit == 2 then
				if role_equip_level >= v.need_equip_level and v.title_flag == 0 and v.intimacy >= v.need_intimacy then
					num = 1
					break
				end
			else
				local baby_flag = self:GetBabyListFlag(v.baby_limit)
				if role_equip_level >= v.need_equip_level and baby_flag[32 - v.baby_id] == 1 and v.title_flag == 0
					and v.intimacy >= v.need_intimacy then
					num = 1
					break
				end
			end
		end
	end
	return num
end
--------------------------- 点唇 ---------------------------
-- 设置伴侣点唇状态
function MarryWGData:SetFereDianChunStatus(status)
	self.fere_dianchun_status = status
end

-- 获取伴侣点唇状态
function MarryWGData:GetFereDianChunStatus()
	return self.fere_dianchun_status or 0
end

-- 设置夫妻点唇信息
function MarryWGData:SetCoupleDianChunInfo(protocol)
	self.couple_dianchun_info = protocol
end

-- 获取夫妻点唇信息
function MarryWGData:GetCoupleDianChunInfo()
	return self.couple_dianchun_info
end

-- 设置奖励信息
function MarryWGData:SetCoupleDianChunRewardInfo(dianchun_reward_info)
	self.dianchun_reward_info = dianchun_reward_info
end

-- 获取奖励信息
function MarryWGData:GetCoupleDianChunRewardInfo()
	return self.dianchun_reward_info
end

-- 获取领取奖励标记
function MarryWGData:GetCoupleDianChunGetRewardFlage()
	if nil == self.dianchun_reward_info then
		return 1
	end
	return self.dianchun_reward_info.get_reward_flage
end

-- 获取点唇剩余次数
function MarryWGData:GetDianChunRemainNum()
	local other_cfg = self:GetMarryOtherCfg()
	if nil ~= self.couple_dianchun_info then
		-- return other_cfg.couple_dianchun_free_count + self.couple_dianchun_info.buy_couple_dianchun_count - self.couple_dianchun_info.couple_dianchun_count --去掉vip购买
		return other_cfg.couple_dianchun_free_count - self.couple_dianchun_info.couple_dianchun_count
	end
	return 0
end

-- 获取消除CD所需元宝
function MarryWGData:GetRemoveCDGold()
	local cd_dianchun = self.qingyuancfg.cd_dianchun
	if nil ~= self.couple_dianchun_info then
		for k,v in pairs(cd_dianchun) do
			if v.cd_count == self.couple_dianchun_info.couple_dianchun_count then
				return v.use_gold
			end
		end
	end
	return 99999
end

-- 获取购买所需元宝
function MarryWGData:GetBuyNumGold()
	local other_cfg = self:GetMarryOtherCfg()
	return other_cfg.vip_dian_chun_count
end

-- 获取点唇奖励列表
function MarryWGData:GetChunRewardList()
	local couple_dianchun = TableCopy(self.qingyuancfg.couple_dianchun)
	table.sort(couple_dianchun, SortTools.KeyUpperSorter("dianchun_count"))
	return couple_dianchun
end

-- 获取婚介升级物品
function MarryWGData:GetHunJieItemList()
	local uplevel_cfg = self.qingyuancfg.uplevel_stuff
	return uplevel_cfg[1].stuff_id
end

-- 获取单次点唇价格
function MarryWGData:GetDianChunSingleGold()
	local other_cfg = self:GetMarryOtherCfg()
	return other_cfg.dian_chun_use_gold
end

-- 获取点唇提醒数量
function MarryWGData:GetDianChunRemindNum()
	if nil ~= self.couple_dianchun_info then
		local couple_dianchun_info = self:GetCoupleDianChunInfo()
		if nil == couple_dianchun_info or nil == IsEmptyTable(couple_dianchun_info) then
			-- self:SetMainViewInvateTip(0, MAINUI_TIP_TYPE.Marry_DianChun, "marry_dianchun")
			return 0
		end

		if self:GetCoupleDianChunGetRewardFlage() == 0 then
			-- self:SetMainViewInvateTip(1, MAINUI_TIP_TYPE.Marry_DianChun, "marry_dianchun")
			return 1
		end
		local dianchun_cd_time = self.couple_dianchun_info.couple_dianchun_cd_time_stamp
		if (dianchun_cd_time - TimeWGCtrl.Instance:GetServerTime()) < 0 and self:GetDianChunRemainNum() > 0 then
			-- self:SetMainViewInvateTip(1, MAINUI_TIP_TYPE.Marry_DianChun, "marry_dianchun")
			return 1
		end
	end
	-- self:SetMainViewInvateTip(0, MAINUI_TIP_TYPE.Marry_DianChun, "marry_dianchun")
	return 0
end

-- 获取VIP购买剩余次数
function MarryWGData:GetVipBuyRemainNum()
	-- local other_cfg = self:GetMarryOtherCfg()
	-- if nil ~= self.couple_dianchun_info then
	-- 	return other_cfg.couple_dianchun_free_count - self.couple_dianchun_info.buy_couple_dianchun_count
	-- end
	return 0
end


-- 印记数据开始 --------------------
-- protocol -----------------------------------
function MarryWGData:SetLoveMarkAllInfo(protocol)
	self.love_stamp_level = protocol.love_stamp_level
	self.love_stamp_star = protocol.love_stamp_star
	self.current_score = protocol.current_score
	self.own_love_stamp_score = protocol.own_love_stamp_score
	self.use_img_id = protocol.img_id
end

-- /protocol ----------------------------------
-- 印记开始 -----------------------------------
function MarryWGData:GetLoveMarkLevel()
	return self.love_stamp_level
end

function MarryWGData:GetLoveMarkStar()
	return self.love_stamp_star
end
--当前进度积分
function MarryWGData:GetLoveMarkCurrentScore()
	return self.current_score
end
--当前拥有积分
function MarryWGData:GetLoveMarkOwnScore()
	return self.own_love_stamp_score
end

function MarryWGData:GetLoveMarkCurrentTotalStar()
	return (self.love_stamp_level - 1) * 10 + self.love_stamp_star
end

function MarryWGData:GetLoveMarkAttrByTotalStar(total_star)
	if total_star == nil then return end
	local attr = AttributePool.AllocAttribute()
	for k,v in pairs(self.love_mark_cfg) do
		local t_star = (v.level - 1) * 10 + v.star
		if t_star == total_star then
			return AttributeMgr.GetAttributteByClass(v)
		end
	end
	return attr
end

function MarryWGData:GetMaxLoveMarkLevel()
	local max_level = 0
	for k,v in pairs(self.love_mark_cfg ) do
		max_level = v.level > max_level and v.level or max_level
	end
	return max_level
end

function MarryWGData:GetMaxLoveMarkTotalStar()
	return #self.love_mark_cfg
end

function MarryWGData:GetLoveMarkUpNeedScoreByTotalStar(total_star)
	if nil == total_star then return end
	local need_score = 0
	for k,v in pairs(self.love_mark_cfg) do
		local t_star = ( v.level - 1 ) * 10 + v.star
		if t_star == total_star then
			return v.up_star_need_score
		end
	end
	return need_score
end

function MarryWGData:GetLoveMarkCostScoreByTotalStar(total_star)
	if nil == total_star then return end
	local cost_score = 0
	for k,v in pairs(self.love_mark_cfg) do
		local t_star = ( v.level - 1 ) * 10 + v.star
		if t_star == total_star then
			return v.cost_score
		end
	end
	return cost_score
end

function MarryWGData:GetLoveMarkImgIDByLevel(level)
	if nil == level then return 0 end
	local img_id = 0
	for k,v in pairs(self.mark_img_cfg) do
		if v.img_active_level == level then
			return v.img_id
		end
	end
	return img_id
end

function MarryWGData:GetLoveMarkRemind()
	local curr_level = self:GetLoveMarkCurrentTotalStar()
	local need_score = self:GetLoveMarkCostScoreByTotalStar(curr_level)
	local curr_score = self:GetLoveMarkOwnScore()
	if curr_level >= self:GetMaxLoveMarkTotalStar() then
		return 0
	end
	return need_score <= curr_score and 1 or 0
end

-------------------- 印记结束 ---------------------------

-------------------选择印记-------------------
function MarryWGData:GetImgAllInfo()
	return self.mark_img_cfg
end
-- 印记数据结束 --------------------


------------------------预约时间-----------------------
-- 获取预约时间配置信息
function MarryWGData:GetMarryYuYueCfg(is_tomorrow)
	local yuyue_cfg = {}
    local day_time = is_tomorrow and 3600 * 24 or 0
	local wedding_yuyue_time = self.qingyuancfg.wedding_yuyue_time
	for k,v in ipairs(wedding_yuyue_time) do
		local time_table = os.date("*t", TimeWGCtrl.Instance:GetServerTime() + day_time)
		local data = TableCopyCfg(v)
		data.first_flag = nil
		data.is_yuyue = self:GetYuYueListInfo(v.seq)
		local time = os.time({year = time_table.year, month = time_table.month, day = time_table.day, hour = math.floor(v.apply_time / 100), min = v.apply_time % 100, sec = 0})
		data.yuyue_time = TimeWGCtrl.Instance:GetServerTime() > time and 1 or 0
		yuyue_cfg[#yuyue_cfg + 1] = data
	end
	table.sort(yuyue_cfg, MarryWGData.MarryYuYueListSorters("yuyue_time", "is_yuyue", "seq"))
	return yuyue_cfg
end

--今日最后一场的结束时间
function MarryWGData:GetMarryYuYueTodayEndTime()
    local time_table = os.date("*t", TimeWGCtrl.Instance:GetServerTime())
    local wedding_yuyue_time = self.qingyuancfg.wedding_yuyue_time
    local last_cfg = wedding_yuyue_time[#wedding_yuyue_time]
    local time = os.time({year = time_table.year, month = time_table.month, day = time_table.day, hour = math.floor(last_cfg.end_time / 100), min = last_cfg.end_time % 100, sec = 0})
    return time
end

--今天是否能预约明天的婚宴
function MarryWGData:GetMarryTodayCanOrderTomorrow()
    -- local marry_end_time = self:GetMarryYuYueTodayEndTime()  --预约的结束时间
    -- local cur_time = TimeWGCtrl.Instance:GetServerTime()
    -- local time_table = os.date("*t", cur_time)
    -- local today_time = os.time({year = time_table.year, month = time_table.month, day = time_table.day, hour = 0, min = 0, sec = 0})
    -- local tomorrow_time = today_time + 3600 * 24
    -- return cur_time >= marry_end_time and cur_time < tomorrow_time
    return false
end

function MarryWGData.MarryYuYueListSorters(sort_key_name1, sort_key_name2, sort_key_name3)
	return function(a, b)
		local order_a = 100000
		local order_b = 100000
		if a[sort_key_name1] > b[sort_key_name1] then
			order_a = order_a + 10000
		elseif a[sort_key_name1] < b[sort_key_name1] then
			order_b = order_b + 10000
		end

		if nil == sort_key_name2 then  return order_a < order_b end

		if a[sort_key_name2] < b[sort_key_name2] then
			order_a = order_a + 1000
		elseif a[sort_key_name2] > b[sort_key_name2] then
			order_b = order_b + 1000
		end

		if nil == sort_key_name3 then  return order_a < order_b end

		if a[sort_key_name3] > b[sort_key_name3] then
			order_a = order_a + 100
		elseif a[sort_key_name3] < b[sort_key_name3] then
			order_b = order_b + 100
		end

		return order_a < order_b
	end
end

function MarryWGData:GetMarryYuYueInfo()
    local can_order = self:GetMarryTodayCanOrderTomorrow()

	local yuyue_cfg = self:GetMarryYuYueCfg(can_order)
	local param_ch4 = self.role_msg_info.param_ch4
	local param_ch8 = self.role_msg_info.param_ch8
	local seq_fornt = 0 --记录最靠前的场次

	local one_item_data = nil
	local two_item_data = nil

	for k,v in pairs(yuyue_cfg) do
		if v.seq == param_ch4 then
			one_item_data = table.remove(yuyue_cfg,k)
			break
		end
	end
	for k,v in pairs(yuyue_cfg) do
		if v.seq == param_ch8 then
			two_item_data = table.remove(yuyue_cfg,k)
			break
		end
	end

	for k,v in pairs(yuyue_cfg) do
		if 1 ~= v.is_yuyue and 0 == v.yuyue_time then --因为已经排好序了
			v.first_flag = v.seq
			seq_fornt = v.seq
			break
		end
	end

	if two_item_data then
		table.insert(yuyue_cfg, 1, two_item_data)
	end

	if one_item_data then
		table.insert(yuyue_cfg, 1, one_item_data)
	end

	--原序不可动会影响原逻辑的赋值
	if yuyue_cfg and not IsEmptyTable(yuyue_cfg) then
		table.sort(yuyue_cfg, SortTools.KeyLowerSorter("seq"))
	end
	return yuyue_cfg, seq_fornt
end

function MarryWGData.YuYueKeySort(sort_key_name1, sort_key_name2)
	return function(a, b)
		local order_a = 100000
		local order_b = 100000
		if a[sort_key_name1] > b[sort_key_name1] then
			order_a = order_a + 10000
		elseif a[sort_key_name1] < b[sort_key_name1] then
			order_b = order_b + 10000
		end

		if nil == sort_key_name2 then  return order_a < order_b end

		if a[sort_key_name2] < b[sort_key_name2] then
			order_a = order_a + 1000
		elseif a[sort_key_name2] > b[sort_key_name2] then
			order_b = order_b + 1000
		end

		return order_a < order_b
	end
end

function MarryWGData:SetYuYueListInfo(protocol)
	self.yuyue_list_info = bit:d2b(protocol.param2)
end

function MarryWGData:GetYuYueListInfo(seq)
	return self.yuyue_list_info[32 - seq] or -1
end

function MarryWGData:GetYuYueTime(seq)
	local wedding_cfg = {}
	for k,v in pairs(self.qingyuancfg.wedding_yuyue_time) do
		if seq == v.seq then
			wedding_cfg = v
		end
	end
	return wedding_cfg
end

function MarryWGData:GetHunYanCfgByReDu(liveness)
	local marry_cfg = self.qingyuancfg.wedding_liveness
	for k,v in pairs(marry_cfg) do
		if liveness < v.liveness_var then
			return v
		end
	end
	return marry_cfg[#marry_cfg]
end

--主人获取宾客信息
function MarryWGData:SetInviteGuests(protocol)
	self.invite_guests_info = {}
	self.invite_guests_info = protocol.wedding_list
	self.wedding_count = protocol.wedding_count 				--婚宴场数：服务器下发的永远都是 2
end

--寻缘全部信息
function MarryWGData:SetAllXunYuanInfo(protocol)
	self.xunyuan_page_count = protocol.page_count
	self.xunyuan_info_flag = protocol.zhenghun_info_flag
	self.show_xunyuan_info_list = protocol.show_zhenghun_info
end

function MarryWGData:SetXunYuanSelectSex(select_sex)
	self.xunyuan_select_sex = select_sex
end

function MarryWGData:GetXunYuanSelectSex()
	return self.xunyuan_select_sex
end

function MarryWGData:GetAllXunYuanInfo()
	return self.show_xunyuan_info_list
end

function MarryWGData:GetXunYuanPageCount()
	return self.xunyuan_page_count
end

function MarryWGData:GetXunYuanInfoFlag()
	return self.xunyuan_info_flag
end

--获取当前婚礼场数:第几场婚礼
function MarryWGData:GetCurWeddingSequence()
	if IsEmptyTable(self.role_msg_info) then
		return MARRY_WEDDING_TYPE.NONE
    end
	--优先满足条件：第一场婚宴
    if self.role_msg_info.param_ch4 > -1 and self.role_msg_info.marry_type > 0 and self.role_msg_info.marry_state > 0 then
		return MARRY_WEDDING_TYPE.WEDDING_ONE
	end
	--如果第一场婚礼没有的话就满足第二场婚宴
	if self.role_msg_info.param_ch8 > -1 and self.role_msg_info.marry_type2 > 0 and self.role_msg_info.marry_state2 > 0 then
		return MARRY_WEDDING_TYPE.WEDDING_TWO
	end
	return MARRY_WEDDING_TYPE.NONE
end

function MarryWGData:GetInviteGuests()
	-- print_error("获取宾客列表！！",self.invite_guests_info)
	return self.invite_guests_info
end

function MarryWGData:SetYuYueRoleInfo(protocol)
	-- print_error("设置婚宴信息",protocol)
	--第一场婚宴
	self.role_msg_info.marry_type = protocol.param_ch1
	self.role_msg_info.marry_count = protocol.param_ch2 + protocol.param_ch6
	self.role_msg_info.marry_state = protocol.param_ch3
	self.role_msg_info.param_ch4 = protocol.param_ch4
	--print_error("第一场:类型：状态：seq：",self.role_msg_info.marry_type,self.role_msg_info.marry_state,self.role_msg_info.param_ch4)
	--第二场婚宴
	self.role_msg_info.marry_type2 = protocol.param_ch5
	self.role_msg_info.marry_count2 = protocol.param_ch6
	self.role_msg_info.marry_state2 = protocol.param_ch7
	self.role_msg_info.param_ch8 = protocol.param_ch8
	--print_error("第二场:类型：状态：seq：",self.role_msg_info.marry_type2,self.role_msg_info.marry_state2,self.role_msg_info.param_ch8)
end

function MarryWGData:GetYuYueRoleInfo()
	-- print_error("获取婚宴信息", self.role_msg_info)
	return self.role_msg_info
end

--根据婚宴场数获取婚宴宾客列表
function MarryWGData:GetInviteGuestsByWeddingSequence( sequence )
	local sequence = sequence or self:GetCurWeddingSequence()

	local tab = self:getInviteGustsTabTable()
	if self.invite_guests_info and self.invite_guests_info[sequence] then
		tab.role_id = self.invite_guests_info[sequence].role_id
		tab.lover_role_id = self.invite_guests_info[sequence].lover_role_id
		tab.wedding_type = self.invite_guests_info[sequence].wedding_type
		tab.has_num = self.invite_guests_info[sequence].has_invite_guests_num
		tab.can_num = self.invite_guests_info[sequence].can_invite_guest_num
		tab.wedding_yuyue_seq = self.invite_guests_info[sequence].wedding_yuyue_seq
		tab.count = self.invite_guests_info[sequence].count
		tab.data = self.invite_guests_info[sequence].data
	end
	return tab
end

--根据seq判断婚宴的场次
function MarryWGData:GetHunYanNum( seq )
	if self.invite_guests_info then
		for k,v in pairs(self.invite_guests_info) do
			if v.wedding_yuyue_seq == seq then
				return k
			end
		end
	end
	return MARRY_WEDDING_TYPE.WEDDING_ONE
end

--获取宾客列表的类
--内部调用，不可在外面访问
function MarryWGData:getInviteGustsTabTable()
	local tab = {}
	tab.role_id = 0
	tab.lover_role_id = 0
	tab.wedding_type = -1
	tab.has_num = 0
	tab.can_num = 0
	tab.wedding_yuyue_seq = 0
	tab.count = 0
	tab.data = {}
	return tab
end

--宾客获取主人信息
function MarryWGData:SetCurWeddingInfo(protocol)
	self.wedding_info.role_id = protocol.role_id
	self.wedding_info.role_name = protocol.role_name
	self.wedding_info.lover_role_id = protocol.lover_role_id
	self.wedding_info.lover_role_name = protocol.lover_role_name
	self.wedding_info.role_prof = protocol.role_prof
    self.wedding_info.lover_role_prof = protocol.lover_role_prof
    self.wedding_info.role_sex = protocol.role_sex
	self.wedding_info.lover_role_sex = protocol.lover_role_sex
	self.wedding_info.seq = protocol.cur_wedding_seq
	self.wedding_info.role_fashion_photforame = protocol.role_fashion_photforame
	self.wedding_info.lover_fashion_photforame = protocol.lover_fashion_photforame
	self.wedding_info.wedding_index = protocol.wedding_index
	self.wedding_info.count = protocol.count
	self.wedding_info.data = protocol.guests_uid
	self.wedding_info.wedding_type = protocol.wedding_type
end

function MarryWGData:GetCurWeddingInfo()
	return self.wedding_info
end

function MarryWGData:SetWeddingApplicantInfo(protocol)
	self.applicantInfo_info[protocol.seq] = protocol.applicant_list
end

function MarryWGData:GetWeddingApplicantInfo(seq)
	return self.applicantInfo_info[seq] or {}
end

--获取当前申请者信息是否需要展示红点（仅仅用于判断红点其他地方不要调用也不要改动）
function MarryWGData:GetWeddingApplicantRed(seq)
	local data = self:GetWeddingApplicantInfo(seq)
	local want_yaoqing_binke = self:GetYaoQingBinKe()

	if want_yaoqing_binke and not IsEmptyTable(want_yaoqing_binke) then
		for k,v in pairs(data) do
			if nil == want_yaoqing_binke[v.user_id] then
				return 1
			end
		end
	else
		return #data
	end

	return 0
end

--红点功能
function MarryWGData:GetWeddingApplicantRemind()
	local seq = self:GetInviteGuestsByWeddingSequence().wedding_yuyue_seq
	local applincant_data_num = self:GetWeddingApplicantRed(seq)
	return applincant_data_num
end

function MarryWGData:SetWeddingRoleInFo(protocol)
	self.wedding_role_info.wedding_liveness = protocol.wedding_liveness
	self.wedding_role_info.is_baitang = protocol.is_baitang
	self.wedding_role_info.time = protocol.is_in_red_bag_fulsh_time
	self.wedding_role_info.has_gather_num = protocol.has_gather_num
	self.wedding_role_info.has_gather_red_bag = protocol.has_gather_red_bag
	self.wedding_role_info.total_exp = protocol.total_exp
	self.wedding_role_info.today_food_count = protocol.today_food_count

	self.has_gather_list = protocol.hunyan_food_id_list
end

function MarryWGData:GetHasGatherRedBag()
	return self.wedding_role_info.has_gather_red_bag or -1
end

-- 是否采集过
function MarryWGData:GetIsHasGather(id)
	if self.has_gather_list then
		for k,v in pairs(self.has_gather_list) do
			if v == id then
				return true
			end
		end
	end
	return false
end

function MarryWGData:SetCurLoverName(str)
    self.lover_name = str
end

function MarryWGData:GetCurLoverName()
    return self.lover_name or ""
end

function MarryWGData:GetWeddingRoleInfo()
	return self.wedding_role_info
end

function MarryWGData:SetLoverInfo(protocol)
	self.lover_info.info_type = protocol.info_type
	self.lover_info.param_ch1 = protocol.param_ch1
	self.lover_info.param_ch2 = protocol.param_ch2
	self.lover_info.param_ch3 = protocol.param_ch3
	self.lover_info.param_ch4 = protocol.param_ch4
	self.lover_info.param2 = protocol.param2
	self.lover_info.role_name = protocol.role_name
end

function MarryWGData:GetLoverInfo()
	return self.lover_info.param_ch1
end

function MarryWGData:SetLoverInfo2( protocol )
	if not protocol then
		return
	end

	self.lover_info2 = {}
	self.lover_info2.appearance = protocol.appearance
	self.lover_info2.role_id = protocol.role_id
	self.lover_info2.role_name = protocol.role_name
	self.lover_info2.prof = protocol.prof
	self.lover_info2.sex = protocol.sex
	self.lover_info2.lover_use_baby_id = protocol.lover_info.lover_use_baby_id
	self.lover_info2.is_online = protocol.is_online
	self.lover_info2.fabao_resid = 0
	if protocol.updateattr and protocol.updateattr[1] and protocol.updateattr[1].use_image_id then
		self.lover_info2.fabao_resid = protocol.updateattr[1].use_image_id
	end
end

function MarryWGData:SetSingleList(protocol)
	self.single_list = protocol.single_list
end

function MarryWGData:GetSingleList()
	return self.single_list or {}
end

function MarryWGData:GetLoverInfo2()
	return self.lover_info2
end

------------------获取婚礼礼包数据---------------------
function MarryWGData:GetRewardItemData()
	local hunyan_cfg = self.qingyuancfg.hunyan_cfg[1].wedding_owner_reward
	return hunyan_cfg
end

function MarryWGData:GetWeedingSceneCfg()
	local red_id = self.qingyuancfg.other[1].gather_rb_id --红包id
	local red_max = self.qingyuancfg.wedding_liveness[1].every_turn_gather_limit --每轮采集的红包个数限制
	local hunyan_cfg = self.qingyuancfg.hunyan_cfg[1]
	local jiuxi_id = hunyan_cfg.gather_id --酒桌id
	local jiuxi_max = hunyan_cfg.gather_max --酒桌采集数量限制
	local today_jiuxi_num = self.qingyuancfg.hunyan_cfg[1].gather_day_max_stuff_num --每天能采集的最大数量
	return red_id, red_max, jiuxi_id, jiuxi_max, tonumber(today_jiuxi_num)
end
--记录当前采集的id
function MarryWGData:RemberGatnId(id)
	self.cur_gath_id = id
end

function MarryWGData:GetRemberGatnId()
	return self.cur_gath_id
end


-------------------亲密度小界面-------------------------
function MarryWGData:GetIntimacyCfg()
	return ConfigManager.Instance:GetAutoConfig("friendcfg_auto").team_intimacy_buff
end

function MarryWGData:GetIntimacyLevelByIntimacy(intimacy)
	local intimacy_cfg = self:GetIntimacyCfg()
	for k,v in pairs(intimacy_cfg) do
		if intimacy < v.intimacy then
			return intimacy_cfg[k - 1]
		end
	end
	return intimacy_cfg[#intimacy_cfg]
end

function MarryWGData:GetIntimacyCfgByLevel(level)
	local intimacy_cfg = self:GetIntimacyCfg()
	for k,v in pairs(intimacy_cfg) do
		if level == v.buff_level then
			return v
		end
	end
	return nil
end

function MarryWGData:GetCurIntimacyProgress(intimacy_value)  --亲密度进度条
	local cur_progress = 0
	local cfg = self:GetIntimacyCfg()
	local cur_intimacy_value = tonumber(intimacy_value)
	if next(cfg) == nil or cur_intimacy_value == nil then
		return cur_progress
	end
	local progress_list = {0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1}			--对应的进度条值

	for k, v in pairs(progress_list) do
		local seq = k - 1
		local length = #progress_list
		local cur_need = cfg[seq] and cfg[seq].intimacy or 0
		local next_need = cfg[seq + 1] and cfg[seq + 1].intimacy or cfg[#cfg].intimacy
		local cur_value = progress_list[seq] and progress_list[seq] or 0
		local next_value = progress_list[seq + 1] and progress_list[seq + 1] or progress_list[length]
		if cur_intimacy_value > cur_need and cur_intimacy_value <= next_need then
			cur_progress = (cur_intimacy_value - cur_need) * (next_value - cur_value) / (next_need - cur_need) + cur_value
			break
		elseif cur_intimacy_value > cfg[#cfg].intimacy then
			cur_progress = progress_list[length]
			break
		end
	end
	return cur_progress
end
--------------------------------------------------------


-----------------------宝宝------------------------------
function MarryWGData:GetBabyListCfg(baby_type)
	local baby_list = {}
	local baby_list_cfg = self.baby_auto_cfg.active
    for k,v in pairs(baby_list_cfg) do
        local info = {}
        info.type = v.type
        info.id = v.id
        info.item_id = v.item_id
        info.auto_active_need_baby_id = v.auto_active_need_baby_id
        info.auto_active_need_baby_grade = v.auto_active_need_baby_grade
        info.appe_image_id = v.appe_image_id
        info.is_default_show = v.is_default_show
        info.condition_type = v.condition_type
        info.skynumber_show = v.skynumber_show
        info.dispose_id = v.dispose_id
        info.num = v.num
        info.shop_tips_scale = v.shop_tips_scale
        info.shop_tips_position = v.shop_tips_position
        info.shop_tips_rotation = v.shop_tips_rotation
        info.baby_name = v.baby_name
        info.scale_x_y = v.scale_x_y
        info.level_show = v.level_show or 0

		if info.type == baby_type then
			table.insert(baby_list, info)
		end
	end
	return baby_list
end

function MarryWGData:IsCanShowDecomposeBabyCard(itemid)
    local baby_list_cfg = self.baby_auto_cfg.active
    if ItemWGData.Instance:GetItemNumInBagById(itemid) > 0 then
        for k,v in pairs(baby_list_cfg) do
            if v.item_id == itemid then
                local is_active = self:GetBabyActiveByItemId(itemid)
                return is_active
            end
        end
    end
	return false
end

function MarryWGData:IsBabyActiveCard(itemid)
	local baby_list_cfg = self.baby_auto_cfg.active
	for k,v in pairs(baby_list_cfg) do
        if v.item_id == itemid then
            return true
        end
    end
	return false
end

function MarryWGData:GetBabyListCfgByID(id, type)
	local baby_list_cfg = self.baby_auto_cfg.active

	for k,v in pairs(baby_list_cfg) do
		if v.type == tonumber(type) and v.id == tonumber(id) then
			return v
		end
	end

	return nil
end

function MarryWGData:GetBabyNewListCfg(baby_type)
	local list = {}
	local baby_list = self:GetBabyListCfg(baby_type)
	local baby_flag = self:GetBabyListFlag(baby_type)
	for k,v in pairs(baby_list) do
		if k == 1 then
			table.insert(list, v)
		end
		if baby_flag[32 - v.id] == 1 then
			if baby_list[k + 1] then
				table.insert(list, baby_list[k + 1])
			end
		end
	end
	return list
end

--通过默认传来的索引获取列表的索引，因为有排序
function MarryWGData:GetSelectIndexByRealId(baby_type, id)
    local list = self:GetForViewBabyNewListCfg(baby_type)
    if not IsEmptyTable(list) then
        for k, v in pairs(list) do
            if v.id == id then
                return k
            end
        end
    end
    return 1
end

--通过默认传来的索引获取列表的索引，因为有排序
function MarryWGData:GetSelectIndexByItemId(baby_type, baby_id)
    local list = self:GetForViewBabyNewListCfg(baby_type)
    if not IsEmptyTable(list) then
        for k, v in pairs(list) do
            if v.item_id == baby_id then
                return k
            end
        end
    end
    return 1
end


function MarryWGData:GetForViewBabyNewListCfg(baby_type)
	local list = {}
	local baby_list = self:GetBabyListCfg(baby_type)
	local baby_flag = self:GetBabyListFlag(baby_type)
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or 0
	local role_level = RoleWGData.Instance:GetRoleLevel() or 0
	if nil == baby_list or nil == baby_flag then
		return list
	end

	--宝宝卡片显示操作
    for k,v in pairs(baby_list) do
        v.real_index = k
        local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
        v.color = 0
        if item_cfg and item_cfg.color then
            v.color = - item_cfg.color + 10
        end
        local is_baby_active = baby_flag[32 - v.id]			--判断宝宝是否激活
        v.is_baby_active = is_baby_active

        if v.is_default_show == 0 then
            if is_baby_active == 1 then
                v.is_can_active = 0
				table.insert(list, v)
			else
                local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id) or 0
                if item_num > 0 and is_baby_active == 0 then
                    v.is_can_active = 1
                else
                    v.is_can_active = 0
                end
				--达成任一条件
				if v.condition_type == BABY_CARD_SHOW.ONE_CON and (item_num  > 0 or open_day >= v.skynumber_show or role_level >= v.level_show) then
					table.insert(list, v)

				--达成所有条件
				elseif v.condition_type == BABY_CARD_SHOW.ALL_CON and item_num  > 0 and open_day >= v.skynumber_show and role_level >= v.level_show then
					table.insert(list, v)
				end

			end
        else
            local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id) or 0
            if item_num > 0 and is_baby_active == 0 then
                v.is_can_active = 1
            else
                v.is_can_active = 0
            end
            
			table.insert(list, v)
		end
    end
    if baby_type == 1 then --女娃
        table.sort(list, SortTools.KeyUpperSorters("is_can_active", "is_baby_active","color"))
    end
	return list
end

function MarryWGData:GetBabyAttrCfg(baby_type, baby_id, baby_grade)
	--local love_attr_value = self:GetMarryAllLoveAttr()              -- 情缘装备界面的仙侣属性值
	local love_value = 1
	local baby_attr_list = {
		gongji = -1,
		maxhp = -1,
		pojia = -1,
		fangyu = -1,
		fa_fangyu = -1,
		howlong_per = -1,
        bless_value = -1,
        shengming_jc_per = -1,
	}
	if baby_grade == -1 then
		baby_attr_list.gongji = 0
		baby_attr_list.maxhp = 0
		baby_attr_list.pojia = 0
		baby_attr_list.fangyu = 0
		baby_attr_list.fa_fangyu = 0
		baby_attr_list.howlong_per = 0
        baby_attr_list.bless_value = 0
        baby_attr_list.shengming_jc_per = 0
		return baby_attr_list
	end
	local baby_attr_cfg = self.baby_auto_cfg.upgrade
	for k,v in pairs(baby_attr_cfg) do
		if v.type == baby_type and v.id == baby_id and baby_grade == v.grade then
			baby_attr_list.gongji = math.floor(v.gongji * love_value)
			baby_attr_list.maxhp = math.floor(v.maxhp * love_value)
			baby_attr_list.pojia = math.floor(v.pojia * love_value)
			baby_attr_list.fangyu = math.floor(v.fangyu * love_value)
			baby_attr_list.fa_fangyu = math.floor(v.fa_fangyu * love_value)
			baby_attr_list.howlong_per = v.howlong_per
            baby_attr_list.bless_value = v.bless_value
            baby_attr_list.shengming_jc_per = math.floor(v.shengming_jc_per * love_value)
		end
	end
	return baby_attr_list
end

function MarryWGData:GetBabyOtherCfg()
	local baby_other_cfg = self.baby_auto_cfg.other[1]
	return baby_other_cfg
end
--获取仙侣萌娃最大阶数
function MarryWGData:GetBabyMaxGrade()
	local baby_other_cfg = self.baby_auto_cfg.other[1]
	return baby_other_cfg.baby_max_grade
end

function MarryWGData:GetBabyItemListRemind()
	local list = {}
	for i=0, 1 do
		local active_card_cfg = self:GetBabyActiveCard(i)
		table.insert(list, active_card_cfg.active_card_id)
	end
	table.insert(list, self:GetBabyOtherCfg().add_bless_item_id)
	return list
end

function MarryWGData:GetBabySkillCfg(baby_type, baby_id)
	local baby_skill_list = {}
	local baby_skill_cfg = self.baby_auto_cfg.skill
	for k,v in pairs(baby_skill_cfg) do
		if v.type == baby_type and v.id == baby_id then
			table.insert(baby_skill_list, v)
		end
	end
	return baby_skill_list
end

function MarryWGData:GetBabySkillAddAttr(attr_str, attr_value)
	local attribute = AttributePool.AllocAttribute()
	if type(attr_str) == "number" then
		attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_str)
		attribute[attr_str] = attr_value
	end

	return attribute
end

-- 宠物技能属性加成
function MarryWGData:GetBabySkillAttrList(baby_type, baby_id, baby_grade)
	local skill_attr_list = {
		gongji = 0,
		maxhp = 0,
		pojia = 0,
		fangyu = 0,
		shengming_jc_per = 0
	}
	-- local baby_skill_list = self:GetBabySkillCfg(baby_type, baby_id)
	-- for k,v in pairs(baby_skill_list) do
	-- 	if baby_grade >= v.act_need_grade then
	-- 		skill_attr_list.gongji = skill_attr_list.gongji + gongji
	-- 		skill_attr_list.maxhp = skill_attr_list.maxhp + maxhp
	-- 		skill_attr_list.pojia = skill_attr_list.pojia + pojia
	-- 		skill_attr_list.fangyu = skill_attr_list.fangyu + fangyu
	-- 		skill_attr_list.fa_fangyu = skill_attr_list.fa_fangyu + fa_fangyu
	-- 	end
	-- end
	return skill_attr_list
end

function MarryWGData:GetBabyNameCfg(baby_type, baby_id)
	local baby_name_cfg = self.baby_auto_cfg.active
	for k,v in pairs(baby_name_cfg) do
		if v.type == baby_type and v.id == baby_id then
			return v
		end
	end
end

function MarryWGData:GetUseBabyAppeImageId()
	return self.baby_type_list.use_baby_id
end

function MarryWGData:GetBabyNameCfgByUseId(use_baby_id)
    local use_baby_id = use_baby_id or self.baby_type_list.use_baby_id
	local baby_name_cfg = self.baby_auto_cfg.active
	for k,v in pairs(baby_name_cfg) do
		if v.appe_image_id == use_baby_id  then
			return v
		end
	end
end


function MarryWGData:SetBabyTypeList(protocol)
	for i = 0, 1 do
		self.baby_flag_list[i] = bit:d2b(protocol.baby_type_list[i].baby_act_flag)
	end
	self.baby_type_list = protocol.baby_type_list
	self.baby_type_list.role_qy_equip_level = protocol.role_qy_equip_level
	self.baby_type_list.lover_qy_equip_level = protocol.lover_qy_equip_level
	self.baby_type_list.use_baby_id = protocol.use_baby_id
	self.baby_type_list.get_baby_timestamp = protocol.get_baby_timestamp
	self.baby_type_list.get_baby_flag = protocol.get_baby_flag
	self:GetBabyIsCanLiqnqu(protocol.get_baby_timestamp,protocol.get_baby_flag)
end

function MarryWGData:GetBabyListInfoByType(baby_type, index)
	if nil == self.baby_type_list[baby_type] then
		return
	end
	local baby_list = self.baby_type_list[baby_type].baby_list
	if nil == baby_list then
		return
	end
	for k,v in pairs(baby_list) do
		if k == index then
			return v
		end
	end
end

function MarryWGData:GetMarryEquipLevel()
	return self.baby_type_list.role_qy_equip_level or 0, self.baby_type_list.lover_qy_equip_level or 0
end

function MarryWGData:GetBabyListFlag(baby_type)
	return self.baby_flag_list[baby_type] or {}
end

function MarryWGData:GetBabyDescCfg(baby_type, baby_id)
	local baby_list_cfg = self:GetBabyListCfg(baby_type)
	for k,v in pairs(baby_list_cfg) do
		if v.id == baby_id then
			return v
		end
	end
end

function MarryWGData:GetBabyActiveCard(baby_type)
	local active_card_cfg = self.baby_auto_cfg.active_card
	for k,v in pairs(active_card_cfg) do
		if baby_type == v.type then
			return v
		end
	end
end

function MarryWGData:SetOneBabyInfo(protocol)
	for i = 0, 1 do
		self.baby_flag_list[i] = bit:d2b(protocol.baby_act_flag[i])
	end
	self.baby_type_list[protocol.baby_type].baby_list[protocol.baby_id].bless_value = protocol.bless_value
	self.baby_type_list[protocol.baby_type].baby_list[protocol.baby_id].grade = protocol.grade
	self.baby_type_list[protocol.baby_type].baby_list[protocol.baby_id].skill_act_flag = protocol.skill_act_flag
end

-- 判断是否有切换界面特效（貌似无用顺手改了一下）
function MarryWGData:GetBabyTypeFlag()
	local role_equip_level, lover_equip_level = self:GetMarryEquipLevel()
	for i = 0, 1 do
		local baby_list = self:GetBabyListCfg(i)
		for k,v in pairs(baby_list) do
			if self.baby_flag_list[i][32 - v.id] == 0 and v.id == 0 then
				local active_card_cfg = self:GetBabyActiveCard(i)
				local baby_desc_cfg = self:GetBabyDescCfg(i, v.id)
				if active_card_cfg and baby_desc_cfg then
					local item_num = ItemWGData.Instance:GetItemNumInBagById(active_card_cfg.active_card_id)
					local role_level = RoleWGData.Instance.role_vo.level
					if item_num >= 1 and role_level >= active_card_cfg.use_min_role_level then
						return i
					elseif role_equip_level >= baby_desc_cfg.condition_value and lover_equip_level >= baby_desc_cfg.condition_value then
						return i
					end
				end
			elseif self.baby_flag_list[i][32 - v.id] == 0 then
				local baby_list_info = self:GetBabyListInfoByType(i, v.id - 1)
				if baby_list_info then
					if baby_list_info.grade == self.baby_max_grade then
						return i
					end
				end
			end
		end
	end
end

function MarryWGData:GetBabyRemind()
	local role_equip_level, lover_equip_level = self:GetMarryEquipLevel()
	local num = 0
	if not IsEmptyTable(self.baby_flag_list) then
		for i = 0, 1 do
			num = self:GetBabyRemindByBabyType(i)
			if num > 0 then
				break
			end
		end
	end
	-- self:SetMainViewInvateTip(num, MAINUI_TIP_TYPE.Marry_Baby, "marry_baby")
	return num
end

function MarryWGData:OnOtherRoleOnlineChange(other_role_id, is_online)
    local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0	--伴侣ID
    if lover_id == 0 then --没结婚有材料一直显示
        self.lover_is_online = 1
    elseif lover_id == other_role_id then --结婚后伴侣在线且有材料一直显示
        self.lover_is_online = is_online == 0 and 0 or 1
    end
    RemindManager.Instance:Fire(RemindName.Marry_Propose)
    ViewManager.Instance:FlushView(GuideModuleName.Marry)
    HomesWGCtrl.Instance:FlushHomesView()
	LoverPkWGCtrl.Instance:FlushLoverPKPrepareSceneView()
    self:IsShowLongHunWay()
end

function MarryWGData:GetProposeRemind()
    local data_list = self.profess_gift_cfg or {}
    for k, v in pairs(data_list) do
        local num = ItemWGData.Instance:GetItemNumInBagById(v.gift_id)
        if num > 0 and self.lover_is_online == 1 then
            return 1
        end
    end
	return 0
end

function MarryWGData:GetBabyRemindByBabyType( baby_type )
	--只对已经展示的做红点判断
	local baby_list = self:GetForViewBabyNewListCfg(baby_type)

	if nil == baby_list or IsEmptyTable(baby_list) then
		return 0
	end
    for k,v in pairs(baby_list) do
        if baby_type == 1 then --女娃
            if 0 == self.baby_flag_list[baby_type][32-v.id] then --未激活
                local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
                if item_num >= 1 then
                    return 1
                end
            else
                local remind_flag = self:CheckBabyMatLevelUp(baby_type, v.id)
                if remind_flag > 0 then
                	return 1
                end
            end
        else
            if 0 == self.baby_flag_list[baby_type][32-v.id] then --未激活
                local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
                if v.id == 0 and item_num >= 1 then --只有第一个使用道具激活，其他的会在前一个10阶后自动激活
                    return 1
                end
            else
                local remind_flag = self:CheckBabyMatLevelUp(baby_type, v.id)
                if remind_flag > 0 then
                	return 1
                end
            end
        end
	end
	return 0
end

--仙娃:材料满足升1级判断
function MarryWGData:CheckBabyMatLevelUp(baby_type, baby_id)
	local baby_other_cfg = self:GetBabyOtherCfg()
	local baby_list_info = self:GetBabyListInfoByType(baby_type, baby_id)
    if baby_other_cfg and baby_list_info and self.baby_max_grade ~= baby_list_info.grade then--信息满级判断
    	local baby_cur_cfg = self:GetBabyAttrCfg(baby_type, baby_id, baby_list_info.grade)
    	if baby_cur_cfg then
    		local level_num = ItemWGData.Instance:GetItemNumInBagById(baby_other_cfg.add_bless_item_id)
            local need_up_level_exp = baby_cur_cfg.bless_value - baby_list_info.bless_value
            local total_add_exp = level_num * baby_other_cfg.add_bless_value
            if total_add_exp >= need_up_level_exp  then--材料满足升1级
            	return 1
            end
    	end
    end
    return 0
end

-- 获取默认展示的baby索引
function MarryWGData:GetDefaultIndexByBabyType(baby_type)
	local baby_list = self:GetForViewBabyNewListCfg(baby_type)
	if nil == baby_list or IsEmptyTable(baby_list) then
		return 0
	end
	for k,v in pairs(baby_list) do
		if 0 == self.baby_flag_list[baby_type][32-v.id] then
			local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
			if item_num >= 1 then
				return  k
			end
		else
			local baby_other_cfg = self:GetBabyOtherCfg()
			local baby_list_info = self:GetBabyListInfoByType(baby_type, v.id)
            if baby_other_cfg and baby_list_info and self.baby_max_grade ~= baby_list_info.grade then--信息满级判断              
                local baby_cur_cfg = self:GetBabyAttrCfg(baby_type, v.id, baby_list_info.grade)
                if baby_cur_cfg then
                    local level_num = ItemWGData.Instance:GetItemNumInBagById(baby_other_cfg.add_bless_item_id)
                    local need_up_level_exp = baby_cur_cfg.bless_value - baby_list_info.bless_value
                    local total_add_exp = level_num * baby_other_cfg.add_bless_value
                    if total_add_exp >= need_up_level_exp  then--材料满足升1级
                        return k
                    end
                end
			end
		end
	end
	return 0
end

-- 根据宝宝物品id获取资源id
function MarryWGData:GetBabyResByItemId(item_id)
	if nil == item_id then
		return nil
	end

	local baby_type, baby_id, baby_cfg = self:GetBabyTypeItemId(item_id)
	local baby_name_cfg = self:GetBabyNameCfg(baby_type, baby_id)
	if not baby_name_cfg then
		print_error("can not find the baby_data:baby_type == ",baby_type,"baby_id == ",baby_id)
		return
	end
	local cfg = self:GetCfg(baby_type, baby_id)
	return baby_cfg.appe_image_id, cfg, baby_cfg
end

function MarryWGData:GetBabyTypeItemId( item_id )
	local baby_type = -1
	local baby_id = -1
	local baby_cfg = {}
	local active_cfg = self.baby_auto_cfg.active
	for k,v in pairs(active_cfg) do
		if item_id == v.item_id then
			baby_type = v.type
			baby_id = v.id
			baby_cfg = v
		end
	end
	return baby_type, baby_id, baby_cfg
end

function MarryWGData:GetCfg(baby_type, baby_id)
	local cfg = {}
	local baby_attr_cfg = self.baby_auto_cfg.upgrade
	for k ,v in ipairs(baby_attr_cfg) do
		if v.type == baby_type and v.id == baby_id and v.grade == 1 then
			cfg.gongji = v.gongji
			cfg.maxhp = v.maxhp
			cfg.fa_fangyu = v.fa_fangyu
			cfg.pojia = v.pojia
			cfg.fangyu = v.fangyu
		end
	end
	return cfg
end

--更具模型资源获取配置
function MarryWGData:GetBabyCfgByImageId( imageid )
	local baby_attr_cfg = self.baby_auto_cfg.active
	for k, v in pairs(baby_attr_cfg) do
		if v.appe_image_id == imageid then
			return v
		end
	end
end

--判断宝宝是否激活
function MarryWGData:GetBabyActiveByItemId(item_id)
	local baby_type, baby_id = self:GetBabyTypeItemId(item_id)
	if (baby_type ~= -1) and (baby_id ~= -1) then
		local baby_flag = self:GetBabyListFlag(baby_type)
		local baby_notactive = baby_flag[32 - baby_id] == 0
		return not baby_notactive
	end
	return false
end

--根据宝宝类型判断宝宝是否激活
function MarryWGData:GetBabyActiveByTypeId(baby_type,baby_id)
	if (baby_type ~= -1) and (baby_id ~= -1) then
		local baby_flag = self:GetBabyListFlag(baby_type)
		local baby_notactive = baby_flag[32 - baby_id] == 0
		return not baby_notactive
	end
	return false
end

--获取宝宝等级
function MarryWGData:GetBabyLevel(item_id)
	local baby_type,baby_id = self:GetBabyTypeItemId(item_id)
	local baby_list_info = self:GetBabyListInfoByType(baby_type, baby_id)
	if baby_list_info then
		return baby_list_info.grade
	end
	return 0
end

-----------------------宝宝------------------------------

function MarryWGData:GetMarryTitleCfg()
	local intimacy = 0
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	local friend_list = SocietyWGData.Instance:GetFriendList()
	for k,v in pairs(friend_list) do
		if lover_id == v.user_id then
			intimacy = v.intimacy
		end
	end

	local marry_title_cfg = TableCopy(self.qingyuancfg.lover_title)
	for k,v in pairs(marry_title_cfg) do
		v.intimacy = intimacy
		v.title_flag = self:GetMarryTitleInfo()[33 - k] or 0
	end
	--table.sort(marry_title_cfg, SortTools.KeyLowerSorters("title_flag", "baby_id"))
	return marry_title_cfg
end

function MarryWGData:SetMarryTitleInfo(flag)
	self.marry_title_flag = bit:d2b(flag)
end

function MarryWGData:GetMarryTitleInfo()
	return self.marry_title_flag
end

function MarryWGData:GetMarryFbCfg()
	return self.qingyuancfg.lovers_fb_monster
end

function MarryWGData:GetNeedGoldAuto(index)
	local needgold_cfg = self.qingyuancfg.equip_buy_puton_limit
	for k,v in pairs(needgold_cfg) do
		if v.index == index then
			return v.buy_gold
		end
	end
	return 1000000
end

function MarryWGData:RemindHunJie()
	local hunjie_info  = self:GetAllRingInfo()
	local num = 0
	for k,v in pairs(hunjie_info) do
		if v.item_id > 0 then
			num = num +1
		end
	end
	local index = num
	if index > 0 and index <= 3 and not self:CheckHasEquipBySlot(index) then
		local up_level = self:QianYuanEquipmentLevel(index)
		local equipment_level = self:GetQingYuannEquipmentAllLevel(index)

		local bind_gold = RoleWGData.Instance:GetRoleInfo().bind_gold or 0
		local gold = RoleWGData.Instance:GetRoleInfo().gold or 0
		local need_gold = self:GetNeedGoldAuto(index)
		if up_level ~= nil and equipment_level ~= nil and equipment_level >= up_level then
			--设置红点
			if bind_gold + gold >= need_gold then
				return 1
			end
		end
	end
	return 0
end


function MarryWGData:QianYuanEquipmentLevel(index)
	-- local equipment_level  =  self.qingyuancfg.equip_buy_puton_limit
	-- for k,v in ipairs(equipment_level) do
	-- 	if v.index == index then
	-- 		return v.need_level_sum
	-- 	end
	-- end
	return 1000000
end

function MarryWGData:GetQingYuannEquipmentAllLevel(index)
	if index == nil then return 0 end
	local all_level = 0
	local item_cfg = nil
	for k, v in pairs(self.ring_info_list)do
		if k <= index then
			item_cfg = self:GetEquipCfgById(v.item_id, v.param.star_level)
			if item_cfg ~= nil then
				all_level = all_level + item_cfg.level
			end
		end
	end

	return all_level
end

function MarryWGData:SetMarryTimeSeq(seq)
	self.select_time_seq = seq
end

function MarryWGData:GetMarryTimeSeq()
	return self.select_time_seq
end

function MarryWGData:SetCommonWinData(type, fb_data_list, exp)
	self.common_win_data.type = type
	self.common_win_data.data = fb_data_list
	self.common_win_data.exp = exp
end

function MarryWGData:GetCommonWinData()
	local data = TableCopy(self.common_win_data)
	self.common_win_data = {}
	return data
end

----------------------------------------------------------------------------
--婚宴巡游
function MarryWGData:XunYouInfo( protocol )
	if nil == protocol then
		return self.marry_xunyou_info
	end
	self.marry_xunyou_info = {}
	self.marry_xunyou_info.red_pack_count = protocol.param_ch1 		--撒红包次数
	self.marry_xunyou_info.buy_red_pack_count = protocol.param_ch2 	--撒红包购买次数
	self.marry_xunyou_info.flower_count = protocol.param_ch3 		--撒花次数
	self.marry_xunyou_info.buy_flower_count = protocol.param_ch4 	--撒花购买次数
	self.marry_xunyou_info.gather_red_pack_count = protocol.param_ch5 --采集巡游红包次数
	self.marry_xunyou_info.wedding_type = protocol.param_ch6 		--婚宴类型

	--是否巡游参数
	self.is_trans_to_xunyou_scence = protocol.is_trans_to_xunyou_scence
end

--获取红包和鲜花的价格
function MarryWGData:GetXunYouRedPackAndFlowerPrice()
	local cur_wedding_type = -1
	if self.wedding_info and self.wedding_info.wedding_type then
		cur_wedding_type = self.wedding_info.wedding_type
	end

	if cur_wedding_type == -1 then
		return 0, 0
	end

	local cfg = self.qingyuancfg.hunyan_xunyou
	if nil == cfg or nil == cfg[cur_wedding_type + 1] then
		return 0, 0
	end
	return cfg[cur_wedding_type + 1].red_bag_need_gold or 0, cfg[cur_wedding_type + 1].flower_need_gold or 0
end

--剩余撒红包个数
function MarryWGData:GetSprinkleRedPackCount()
	local marry_xunyou_info = self:XunYouInfo()
	if nil == marry_xunyou_info then
		return 0, 0
	end
	local cfg = self.qingyuancfg.hunyan_xunyou
	-- local wedding_type = marry_xunyou_info.wedding_type + 1
	local wedding_type = 3
	local red_pack_count = cfg[wedding_type].free_sa_red_bag_count + marry_xunyou_info.buy_red_pack_count - marry_xunyou_info.red_pack_count
	local flower_count = cfg[wedding_type].free_give_flower_count + marry_xunyou_info.buy_flower_count - marry_xunyou_info.flower_count
	return red_pack_count, flower_count
end

---------------------设置坐标-------------------------
function MarryWGData:SetXunYouPos(protocol)
	self.xunyou_pos.is_own = protocol.param_ch1
	self.xunyou_pos.x = protocol.param2
	self.xunyou_pos.y = protocol.param3
	self.xunyou_pos.obj_id = protocol.param4
	self.xunyou_pos.scene_id = protocol.param5
end

function MarryWGData:GetXunYouPos()
	return self.xunyou_pos
end

--是否婚宴主人
function MarryWGData:IsMarryUser()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local marry_cfg = self:GetCurWeddingInfo()
	if role_id == marry_cfg.role_id or role_id == marry_cfg.lover_role_id then
		return true
	end
	if self.xunyou_pos.is_own and self.xunyou_pos.is_own ~= 0 then
		return true
	end
	return false
end

function MarryWGData:IsMarry()
	self.marry_role_info = self:GetYuYueRoleInfo()

	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	if lover_id > 0 or self.marry_role_info.marry_state == 1 then
		return true
	end

	return false
end

-- 获取当前自己是否在巡游
function MarryWGData:GetOwnIsXunyou()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.HUNYAN)
	if activity_info and activity_info.status == HUNYAN_STATE_TYPE.HUNYAN_STATE_TYPE_XUNYOU and self:IsMarryUser() and self.is_trans_to_xunyou_scence ~= 0 then
		return true
	end
	return false
end

-- 如果自己是在巡游状态中对象的足迹也要屏蔽
function MarryWGData:GetRoleIsshowFoot(role_id)
	local is_xunyou = self:GetOwnIsXunyou()
	if not is_xunyou or nil == role_id then
		return false
	end

	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0	--伴侣ID
	if role_id == lover_id then
		return true
	end

	return false
end

--获取巡游路径
function MarryWGData:GetXunYouPath()
	return self.xunyou_path
end

function MarryWGData:GetShowXuyouTime(seq)
	local yuyue_cfg = self:GetMarryYuYueCfg()
	for _,v in pairs(yuyue_cfg) do
		if v.seq == seq then
			local begin1, begin2 = math.modf(v.begin_time / 100)
			local end1, end2 = math.modf(v.xunyou_end_time / 100)
			local begin_time = begin1 .. ":" .. begin2 * 100
			local end_time = end1 .. ":" .. string.format("%02d", end2 * 100 > 5 and math.floor(end2 * 100) or math.ceil(end2 * 100))
			local str = begin_time .. "-" .. end_time
			return str
		end
	end
	return ""
end

function MarryWGData:GetShowYuYueTime(seq)
	local yuyue_cfg = self:GetMarryYuYueCfg()
	local Wedding_cfg = WeddingWGData.Instance:GetHunyanCfg()
	local standby_s = math.floor(Wedding_cfg.standby_s / 60)
	local start_s = math.floor(Wedding_cfg.start_s / 60)
	for _,v in pairs(yuyue_cfg) do
		if v.seq == seq then
			local begin1, begin2 = math.modf(v.apply_time / 100)
			local end1, end2 = math.modf(v.end_time / 100)
			begin2 = begin2 * 100
			begin2 = begin2 + standby_s
			if begin2 < 10 then
				begin2 = "0" .. begin2
			elseif begin2 >= 60 then
				begin1 = begin1 + 1
				begin2 = begin2 - 60
			end
			
			local begin_time = begin1 .. ":" .. begin2
			local end_time = end1 .. ":" .. begin2 + start_s
			-- local end_time = end1 .. ":" .. string.format("%02d", end2 * 100 > 5 and math.floor(end2 * 100) or math.ceil(end2 * 100))
			local str = begin_time .. "-" .. end_time
			return str, begin_time, end_time
		end
	end
	return "", "", ""
end

function MarryWGData:DianChunBtnState( type )
	if nil == type then
		return self.dianchun_btn_state or DIANCHUN_START
	end
	self.dianchun_btn_state = type
end

function MarryWGData:SetMainViewInvateTip( num, invate_name_index, name_index, view_name )
	if num == 1 then
		MainuiWGCtrl.Instance:InvateTip(invate_name_index,1,function ()
			FunOpen.Instance:OpenViewByName(view_name and view_name or GuideModuleName.Marry, name_index)
			return true
		end)
	else
		MainuiWGCtrl.Instance:InvateTip(invate_name_index,0)
	end
end

function MarryWGData:GetYueLaoNpcId()
	return self.qingyuancfg.hunyan_cfg[1].npc_id
end

function MarryWGData:GetYueLaoCfg()
	local cfg = self.qingyuancfg.hunyan_cfg[1]
	local position = string.split(cfg.npc_pos, ",")
	local npc_cfg = {scene_id = cfg.xunyou_scene_id, x = position[1], y = position[2]}

	return npc_cfg
end


function MarryWGData:GetXunYouSceneIdAndPos()
	local cfg = self.qingyuancfg.hunyan_cfg[1]
	local position = string.split(cfg.xunyou_init_pos, ",")
	local xun_cfg = {scene_id = cfg.xunyou_scene_id, x = position[1], y = position[2]}

	return xun_cfg
end

------------------------------------------- 表白墙 -------------------------------------------
--表白墙
function MarryWGData:GetProfessFashionImage()
	if self.profess_cfg ~= nil then
		local fashion_item_id = self.profess_cfg[1].res_id
		return fashion_item_id
	end
end

--表白等级信息
function MarryWGData:SetProfessLevelInfo(protocol)
	self.profess_level_info = {}
	local info = {}
	info.my_grade = protocol.my_grade
	info.my_exp = protocol.my_exp
	info.other_grade = protocol.other_grade
	info.other_exp = protocol.other_exp
	info.all_have_shizhuang = protocol.all_have_shizhuang
	self.profess_level_info = info
	self.has_set_profess_info = true
end

--初始化
function MarryWGData:InitProfessInfo()
	if nil == self.profess_level_info then
		local info = {}
		info.my_grade = 0
		info.my_exp = 0
		info.other_grade = 0
		info.other_exp = 0
		info.all_have_shizhuang = 0
		self.profess_level_info = info
	end
end

function MarryWGData:GetProfessLevelInfo()
	self:InitProfessInfo()
	return self.profess_level_info,self.has_set_profess_info
end

function MarryWGData:GetProfessCfgByLevel(level)
	return self.profess_cfg[level]
end

function MarryWGData:GetOtherProfessCfg()
	return self.profess_other_cfg
end

function MarryWGData:GetQingyuanFbMonsterPosCfg()
	return self.qingyuan_fb_monster
end

function MarryWGData:GetQingyuanFbMonsterPos(wave)
    local wave = wave and wave - 1 or 0 --配置从0开始 boss是在第一波
    local cfg = self:GetQingyuanFbMonsterPosCfg()
    for k, v in pairs(cfg) do
        if v.wave_num == wave then
            return v.pos_x, v.pos_y
        end
    end
end
-------------------------------祝福-------------------------
function MarryWGData:SaveBlessingInfo( info )
	if nil == self.marry_blessing_info then
		self.marry_blessing_info = {}
	end
	-- local index = info.server_marry_times
	local index = #self.marry_blessing_info + 1
	self.marry_blessing_info[index]                    = {}
	self.marry_blessing_info[index].uid1               = info.uid1
	self.marry_blessing_info[index].name1              = info.name1
	self.marry_blessing_info[index].sex1              = info.sex1
    self.marry_blessing_info[index].prof1              = info.prof1
	self.marry_blessing_info[index].uid2               = info.uid2
	self.marry_blessing_info[index].name2              = info.name2
	self.marry_blessing_info[index].sex2              = info.sex2
    self.marry_blessing_info[index].prof2              = info.prof2
	self.marry_blessing_info[index].server_marry_times = info.server_marry_times
end

function MarryWGData:GetBlessingInfo( index )
	if index == nil then
		return self.marry_blessing_info
	end

	if nil == self.marry_blessing_info then
		return nil
	end

	return self.marry_blessing_info[index]
end

function MarryWGData:SetBlessingInfo( index, info )
	self.marry_blessing_info[index] = info
end

function MarryWGData:ClearBlessingInfo()
	self.marry_blessing_info = {}
end

function MarryWGData:SetCurLoginBlessingNoTips(bool)
	self.cur_login_blessing_notips = bool
end

function MarryWGData:GetCurLoginBlessingNoTips()
	return self.cur_login_blessing_notips
end

--获取免费领取baby的时间和免费领取标识
function MarryWGData:GetBabyLingQuInfo()
	return 	self.baby_type_list.get_baby_timestamp or 0, self.baby_type_list.get_baby_flag or 0
end

--监听baby是否可以领取
function MarryWGData:GetBabyIsCanLiqnqu(get_baby_timestamp,get_baby_flag)
	if get_baby_timestamp == 0 or get_baby_flag == 1  or get_baby_timestamp <= TimeWGCtrl.Instance:GetServerTime() then
		return
	end
	if CountDownManager.Instance:HasCountDown("getbaby_redhint") then
		return
	end
	CountDownManager.Instance:AddCountDown("getbaby_redhint", nil, BindTool.Bind1(self.EndTimeCallBack, self), get_baby_timestamp, nil, 1)
end

function MarryWGData:EndTimeCallBack()
	RemindManager.Instance:Fire(RemindName.Marry_GetMarry) --结婚
	HomesWGCtrl.Instance:FlushHomesView()
end

function MarryWGData:GetBabyActiveCardCfg()
	local active_card_cfg = self.baby_auto_cfg.active_card
	return active_card_cfg
end




function MarryWGData:SetQingYuanNoticeChange(protocol)
	self.cur_states = protocol.cur_states
	self.fetch_reward_flag = protocol.fetch_reward_flag
	self.is_change = protocol.is_change
end

function MarryWGData:GetCurNoticeStates()
	return self.cur_states or 0
end

function MarryWGData:GetCurNoticeRewardFlag()
	return self.fetch_reward_flag or 0
end

function MarryWGData:GetCurNoticeReward()
	local advance_notice_cfg = self.qingyuancfg.advance_notice[1]
	if self.cur_states then
		if self.cur_states == QINGYUAN_ADVANCE_NOTICE_STATUS.QINGYUAN_ADVANCE_NOTICE_STATUS_MARRY then
			return advance_notice_cfg.marry_item
		elseif self.cur_states == QINGYUAN_ADVANCE_NOTICE_STATUS.QINGYUAN_ADVANCE_NOTICE_STATUS_BABY then
			return advance_notice_cfg.baby_item
		end
    end
    print_error("仙缘预告数据异常 self.cur_states =", self.cur_states)
	return {}
end

function MarryWGData:GetAdvanceNoticeCfg()
	return self.qingyuancfg.advance_notice[1] or {}
end

function MarryWGData:GetCurNoticeDes()
	local advance_notice_cfg = self.qingyuancfg.advance_notice[1]
	if self.cur_states then
		if self.cur_states == QINGYUAN_ADVANCE_NOTICE_STATUS.QINGYUAN_ADVANCE_NOTICE_STATUS_MARRY then
			return advance_notice_cfg.marry_notice_desc
		elseif self.cur_states == QINGYUAN_ADVANCE_NOTICE_STATUS.QINGYUAN_ADVANCE_NOTICE_STATUS_BABY then
			return advance_notice_cfg.baby_notice_desc
		end
	end
end

function MarryWGData:GetMarryNoticeRemind()
	if self:IsMarryNoticeLevelLimit() then
		return 0
	end

	if self:IsCanGetMarryNoticeFlag() > 0 then
		return 1
	end

	-- if self.cur_states and self.fetch_reward_flag then
	-- 	if (self.cur_states == QINGYUAN_ADVANCE_NOTICE_STATUS.QINGYUAN_ADVANCE_NOTICE_STATUS_MARRY
	-- 		or self.cur_states == QINGYUAN_ADVANCE_NOTICE_STATUS.QINGYUAN_ADVANCE_NOTICE_STATUS_BABY)
	-- 		and self.fetch_reward_flag == 0 then

	-- 		return 1
	-- 	end
	-- end

	return 0
end

function MarryWGData:IsCanGetMarryNoticeFlag()
	if self.cur_states and self.fetch_reward_flag then
		if (self.cur_states == QINGYUAN_ADVANCE_NOTICE_STATUS.QINGYUAN_ADVANCE_NOTICE_STATUS_MARRY
			or self.cur_states == QINGYUAN_ADVANCE_NOTICE_STATUS.QINGYUAN_ADVANCE_NOTICE_STATUS_BABY)
			and self.fetch_reward_flag == 0 then
			return 1
		end
	end

	return 0
end

function MarryWGData:IsMarryNoticeLevelLimit()
	local advance_notice_cfg = self:GetAdvanceNoticeCfg()
	if not IsEmptyTable(advance_notice_cfg) then
		local role_level = RoleWGData.Instance:GetRoleLevel() or 0

		if role_level >= advance_notice_cfg.reward_limit then
			return false
		end
	end

	return true
end

--日期改变的时候刷新一下红点
function MarryWGData:DayChange()
	self.is_remind_red = false
	RemindManager.Instance:Fire(RemindName.Marry_BaoXia) 	--宝匣
	HomesWGCtrl.Instance:FlushHomesView()
end

----------------------------------------------------结婚模块修改------------------------------------------------
function MarryWGData:GetTiQinLimitMinLevel()
	local qy_cfg_other = self.qingyuancfg.other[1] or {}
	return qy_cfg_other.marry_limit_level or 0
end
--设置我自己那三个档次是否有过一次求婚
function MarryWGData:SetCurRoleIsTiQin(param2)
	self.tiqin_flag = bit:d2b(param2)
end
--获取我自己那三个档次是否有过一次求婚
function MarryWGData:GetCurRoleIsTiQin(type)
	if self.tiqin_flag then
		return self.tiqin_flag[32 - type] == 1
	end
	return false
end

--获取送花配置
function MarryWGData:GetSendFlowerCfg()
	return self.qingyuancfg.send_flower_cfg
end

function MarryWGData:SetCurInviteSeq(seq)
	self.cur_invite_seq = seq
end

--
function MarryWGData:GetCurInviteSeq()
	return self.cur_invite_seq
end

--自己准备邀请的宾客
function MarryWGData:AddYaoQingBinKe(data, seq)
	if nil == data then
		return
    end
    local seq = seq or self:GetCurWeddingSequence()
	local hunyan_num = MarryWGData.Instance:GetHunYanNum(seq)
	local guests_data = MarryWGData.Instance:GetInviteGuestsByWeddingSequence(hunyan_num)
	local has_num = 0
	if guests_data then
		has_num = guests_data.can_num - guests_data.has_num 	--剩余邀请人数
	end
	local index = 0
	if self.want_yaoqing_binke then
		for k,v in pairs(self.want_yaoqing_binke) do
			index = index + 1
		end
	end
	if index >= has_num then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.YaoQingWeiZhiNo)
		return
	end

	local role_id =  data.uid or data.user_id
	self.want_yaoqing_binke[role_id] = data
	RemindManager.Instance:Fire(RemindName.HUNYAN)
	RemindManager.Instance:Fire(RemindName.Marry_PaiQi)
	HomesWGCtrl.Instance:FlushHomesView()
end

--移除自己想要邀请的宾客
function MarryWGData:RemoveYaoQing(data)
	if nil == data then
		return
	end
	
	local role_id = data.uid or data.user_id
	if self.want_yaoqing_binke[role_id] then
		self.want_yaoqing_binke[role_id] = nil
	end

	RemindManager.Instance:Fire(RemindName.HUNYAN)
	RemindManager.Instance:Fire(RemindName.Marry_PaiQi)
	HomesWGCtrl.Instance:FlushHomesView()
end

function MarryWGData:SetYaoQingBinKe()
	self.want_yaoqing_binke = {}
	RemindManager.Instance:Fire(RemindName.HUNYAN)
	RemindManager.Instance:Fire(RemindName.Marry_PaiQi)
	HomesWGCtrl.Instance:FlushHomesView()
end

function MarryWGData:GetYaoQingBinKe()
	return self.want_yaoqing_binke or {}
end

function MarryWGData:IsInYaoQingBinKeList(data)
	local role_id = data.uid or data.user_id
	local yaoqing_id = 0
	if self.want_yaoqing_binke and self.want_yaoqing_binke[role_id] then
		yaoqing_id = self.want_yaoqing_binke[role_id].uid or self.want_yaoqing_binke[role_id].user_id
	end 
	return yaoqing_id > 0 and yaoqing_id == role_id or false
end

function MarryWGData:IsEnemy(data)
	-- 这里的相关操作服务端说都是原服的人，比如申请参加婚宴之类的，所以用主角的平台ID
	local plat_type = RoleWGData.Instance:GetPlatType()
	local rold_id = data.uid or data.user_id
	local enemy_list = SocietyWGData.Instance:GetEnemyList()
	if nil == enemy_list or (enemy_list and IsEmptyTable(enemy_list)) then
		return 0 --不是敌人（为了排序方便）
	end
	for k,v in pairs(enemy_list) do
		if plat_type == v.uuid.temp_low and plat_type == v.uuid.temp_high then
			return 1
		end
	end
	return 0
end

--获取和对方亲密度
function MarryWGData:GetAndRoleIntimacy(user_id)
	local friend_list = SocietyWGData.Instance:GetFriendList()
	if nil == user_id then
		return nil
	end
	for k,v in pairs(friend_list) do
		if nil ~= v and v.user_id == user_id then
			return v.intimacy
		end
	end
	return 0
end

--设置婚宴排期数据
function MarryWGData:SetWeddingScheduleInfo(protocol)
	if nil == protocol.schedule_list then
		return
	end
	self.schedule_list = protocol.schedule_list
end

--获取婚宴排期数据
function MarryWGData:GetWeddingScheduleInfo()
	return self.schedule_list or {}
end
--获取婚宴排期展示奖励
function MarryWGData:GetPaiQiRewardItemData()
	local paiqi_cfg = self.qingyuancfg.hunyan_cfg[1].paiqi_reward
	return paiqi_cfg
end

--主动邀请自己的婚宴信息
function MarryWGData:SetWeddingInviteAckInfo(protocol)
	if nil == self.auto_yaoqing_list then
		self.auto_yaoqing_list = {}
	end
	local list_index = #self.auto_yaoqing_list + 1
	local list = {}
	list.seq = protocol.seq
	list.invite_role_id = protocol.invite_role_id
	list.lover_role_id = protocol.lover_role_id
    list.invite_sex = protocol.invite_sex
    list.invite_prof = protocol.invite_prof
    list.lover_prof = protocol.lover_prof
	list.lover_sex = protocol.lover_sex
	list.invite_name = protocol.invite_name
	list.lover_name = protocol.lover_name
	self.auto_yaoqing_list[list_index] = list
end

function MarryWGData:GetWeddingInviteAckInfo()
	return self.auto_yaoqing_list or {}
end
--清除数据（true——全清除，false 一个一个清理）
function MarryWGData:DelWeddingInviteAckInfo(enable)
	if self.auto_yaoqing_list then
		if enable then
			self.auto_yaoqing_list = {}
		else
			if #self.auto_yaoqing_list > 0 then
				table.remove(self.auto_yaoqing_list,1)
			end
		end
	end
end

--获取一个随机弹幕
function MarryWGData:GetSuiJiDanMu()
	local barrage_cfg = self.qingyuancfg.barrage_cfg
	if barrage_cfg then
		local num  = #barrage_cfg
		local seq = math.random(1, num)
		if barrage_cfg[seq] then
			return barrage_cfg[seq].desc
		end
	end
	return ""
end

--获取情缘副本界面奖励的展示
function MarryWGData:GetQingYuanFbViewReward()
	local qingyuanfb_reward_cfg = self.qingyuancfg.qingyuanfb_view_reward
	return qingyuanfb_reward_cfg
end
--获取助战声望
function MarryWGData:GetQingYuanZhuZhanShengWang()
	local other = self.qingyuancfg.other[1]
	return other.help_reward_shengwang or 0
end

--已经获取过的物品（同心锁和称号）
function MarryWGData:SetMarryRewardLimitInfo(protocol)
	self.marry_reward_limit_item_id = protocol.marry_reward_limit_item_id
end

function MarryWGData:GetMarryRewardLimitInfo()
	return self.marry_reward_limit_item_id or {}
end

--排除已经获取过的物品并且计算出战力（主动，装备）need_power是否需要计算战力的标识
function MarryWGData:ExculeRewardInfo(item_data, get_data, need_power)
	local reward_data = {}
	local power = 0
	local index = 0

	if nil == item_data or IsEmptyTable(item_data) then
		return reward_data, power
	end

	if get_data and not IsEmptyTable(get_data) then
		for k,v in pairs(item_data) do
			local is_get = false
			for m,n in pairs(get_data) do
				if v.item_id == n then
					is_get = true
					break
				end
			end

			if not is_get then
				reward_data[index] = v
				index = index + 1
				if need_power then
					power = power + self:GetItemPower(v.item_id)
				end
			end
		end
		return reward_data, power
	end

	reward_data = item_data
	if need_power then
		for k,v in pairs(reward_data) do
			power = power + self:GetItemPower(v.item_id)
		end
	end

	return reward_data, power

end

--根据物品的id获取到对应的战力
function MarryWGData:GetItemPower(item_id)
	local power = 0
	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(item_id)
	if nil == item_cfg or nil == item_type then
		return power
	end

	if item_type == GameEnum.ITEM_BIGTYPE_EXPENSE then			--主动
		power = ItemShowWGData.CalculateCapability(item_id)
	elseif item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then	--装备
		power = item_cfg.regular_score or 0
	end

	return power

end

function MarryWGData:GetMarryPaiQiRemind()
	local seq = self:GetInviteGuestsByWeddingSequence().wedding_yuyue_seq
	local applincant_data_num = MarryWGData.Instance:GetWeddingApplicantRed(seq)
	return applincant_data_num
end

--获取同心锁技能配置
function MarryWGData:GetTongXinSuoSkillCfg()
	local max_skill_level = self:GetTongXinSuoSkillMaxLevel()
	local ring_level = self:GetTongXinSuoLevel()
	local num = self.qingyuancfg.other[1].tongxinsuo_max_skill_seq or 0
	local skill_data = {}
	for i = 0,num do
		local cur_skill = {}
		for k = max_skill_level,1,- 1 do
		 	local cur_skill_cfg = self:GetTongXinSuoNextSkillCfg(i,k)
		 	if cur_skill_cfg and cur_skill_cfg.act_need_grade <= ring_level then
		 		--激活标志
		 		cur_skill["is_jh"] = 1
		 		cur_skill["data"] = cur_skill_cfg
		 		break
		 	end
		end
		if IsEmptyTable(cur_skill) then
		 	cur_skill["is_jh"] = 0
		 	cur_skill["data"] = self:GetTongXinSuoNextSkillCfg(i,1)
		 end
		if not IsEmptyTable(cur_skill)then
		 	table.insert( skill_data, cur_skill )
		end
	end
	return skill_data
end

--获取同心锁下一等级技能配置
function MarryWGData:GetTongXinSuoNextSkillCfg(skill_seq,level)
	if self.tongxinsuo_skill_cfg[skill_seq] and self.tongxinsuo_skill_cfg[skill_seq][level] then
		return self.tongxinsuo_skill_cfg[skill_seq][level]
	end
end

--获取同心锁等级
function MarryWGData:GetTongXinSuoLevel()
	local equip_info = self:GetRingInfo(0)
	if nil == equip_info then
		return 0
	end
	local ring_cfg = self:GetEquipCfgById(equip_info.item_id, equip_info.param.star_level)
	if nil == ring_cfg then
		return 0
	end
	return ring_cfg.level
end

--获取同心锁阶级
function MarryWGData:GetTongXinSuoOrder()
	local equip_info = self:GetRingInfo(0)
	if nil == equip_info then
		return 0
	end

	local cfg = self.m_equipment_auto[equip_info.item_id]
	if cfg then
		return cfg.order
	end

	return 0
end

--获取同心锁提升限制
function MarryWGData:GetTongXinSuoLimintLevel()
	local order = self:GetTongXinSuoOrder()
	local role_level = RoleWGData.Instance:GetRoleLevel() or 0

	local cfg = EquipWGData.Instance:GetEquipForgeCfg(GameEnum.EQUIP_INDEX_HUNJIE)
	if cfg and cfg[order] then
		local limit_level = cfg[order].role_need_min_level
		return limit_level, role_level >= limit_level
	end

	return 0, true
end

--获取同心锁技能最大等级
function MarryWGData:GetTongXinSuoSkillMaxLevel()
	return self.qingyuancfg.other[1].beskill_maxlevel
end

--获取可免费邀请的宾客人数
function MarryWGData:GetCanFreeInviteNum()
	return self.qingyuancfg.other[1].wedding_guest_num or 0
end

-- 设置变强途径的仙侣按钮
function MarryWGData:IsShowLongHunWay()
	--需要有功能开启得拦截不然会提前打开
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName("marry")
	if not is_open then return end
	if ShowRedPoint.SHOW_RED_POINT == self:GetMarryTitleRemind() then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIAN_LV, 1, function ()
	  			MarryWGCtrl.Instance:Open(TabIndex.marry_jiehun)
	  			return true
			  end)
		return
	end

	if ShowRedPoint.SHOW_RED_POINT == self:CanLevelUpRemind() then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIAN_LV, 1, function ()
	  			MarryWGCtrl.Instance:Open(TabIndex.marry_hunjie)
	  			return true
			  end)
		return
	end

	if ShowRedPoint.SHOW_RED_POINT == self:GetBabyRemind() then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIAN_LV, 1, function ()
	  			MarryWGCtrl.Instance:Open(TabIndex.marry_baby)
	  			return true
			  end)
		return
	end

	if ShowRedPoint.SHOW_RED_POINT == self:CanBaoXiaRemind() then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIAN_LV, 1, function ()
	  			-- MarryWGCtrl.Instance:Open(TabIndex.marry_baoxia)
				  ViewManager.Instance:Open(GuideModuleName.MarryBaoXiaView)
	  			return true
			  end)
		return
    end

    if ShowRedPoint.SHOW_RED_POINT == self:GetProposeRemind() then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIAN_LV, 1, function ()
	  			MarryWGCtrl.Instance:Open(TabIndex.marry_profess_wall)
	  			return true
			  end)
		return
    end

	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIAN_LV, 0)
end

--背包物品发生改变
function MarryWGData:InitItemDataChange()
	self.item_data_change_map = {}

	local up_ring_cfg = self.qingyuancfg.uplevel_stuff[1]
	self.item_data_change_map[up_ring_cfg.stuff_id] = true

	for _, v in pairs(self.baby_auto_cfg.active) do
	    self.item_data_change_map[v.item_id] = true
	end

	local baby_other_cfg = self:GetBabyOtherCfg()
	self.item_data_change_map[baby_other_cfg.add_bless_item_id] = true


    local data_list = self.profess_gift_cfg or {}
    for k, v in pairs(data_list) do
        self.item_data_change_map[v.gift_id] = true
    end

	self.item_data_change_map[0] = nil

	self.item_data_event = BindTool.Bind1(self.BagItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
end

function MarryWGData:BagItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if self.item_data_change_map[change_item_id] then
		self:IsShowLongHunWay()
	end
end

function MarryWGData:GetBabyActiveCfgByItemId(item_id)
	local baby_list_cfg = self.baby_auto_cfg.active

	for k,v in pairs(baby_list_cfg) do
		if v.item_id == item_id then
			return v
		end
	end

	return nil
end

function MarryWGData:GetBabySkillData(data)
	local data_list = {}

	if not data or IsEmptyTable(data) then
		return data_list
	end
	local baby_active_cfg = self:GetBabyActiveCfgByItemId(data.item_id)

	if not baby_active_cfg then
		return data_list
	end


	local cfg_list = self:GetBabySkillCfg(baby_active_cfg.type, baby_active_cfg.id)

	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)

	if not cfg_list then
		return data_list
	end
	data_list.data_list_1 = {}
	data_list.data_list_2 = {}
	data_list.data_list_3 = {}
	if #cfg_list > 0 then
		data_list.tab_title_name_3 = Language.Common.BeiDong
	end
	--仙蛙只有被动技能
	for i=1,#cfg_list do
		data_list.data_list_3[i] = {}
		local bundle,asset = ResPath.GetSkillIconById(cfg_list[i].skill_image)
		data_list.data_list_3[i].bundle = bundle
		data_list.data_list_3[i].asset = asset
		data_list.data_list_3[i].color = item_cfg.color
		data_list.data_list_3[i].click_func = BindTool.Bind(self.ClickSkillFunc, self, cfg_list[i], item_cfg.color)
	end
	data_list.data_list_3.title_img = "a3_sl_bq_bd"
	return data_list
end

function MarryWGData:ClickSkillFunc(cfg, color)
	local limit_text = Language.Marry.SkillActive
	local skill_describe = cfg.skill_describe
	local open_grade = 0
	local is_jh = false
	local baby_list_info = MarryWGData.Instance:GetBabyListInfoByType(cfg.type, cfg.id)

	if baby_list_info then
		open_grade = baby_list_info.grade
	end

	if open_grade < cfg.act_need_grade then
		is_jh = false
		limit_text = string.format(Language.Marry.MLSkillOpetText, cfg.act_need_grade)
	else
		limit_text = Language.Marry.SkillActive
	end

	local capability = 0
	local skill_attribute = self:GetBabySkillAddAttr(cfg.param1, cfg.param2)
	if skill_attribute ~= nil then
		capability = AttributeMgr.GetCapability(skill_attribute)
	end

	local show_data = {
		is_jh = false,
		limit_text = limit_text,
		skill_desc = skill_describe,
		skill_image = cfg.skill_image,
		skill_name = cfg.skill_name,
		skill_type = cfg.type_cfg,
		skill_color = color,
		capability = capability,
	}

	MarryWGCtrl.Instance:OpenSkillTip(show_data)
end

--婚宴开启界面判断是否需要展示巡游按钮
function MarryWGData:IsShowXunYouBtn()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.HUNYAN)
	local is_zhuren = self:IsMarryUser()

	--巡游状态 (婚宴开启后显示)
	if activity_info and activity_info.status ~= HUNYAN_STATE_TYPE.HUNYAN_STATE_TYPE_START then
		return false
	end

	return true
end

--婚宴开启界面自动进入倒计时
function MarryWGData:AuToEnterHunYanTime()
	return self.qingyuancfg.other[1].marry_enter_cd or 0
end

--当前的活动状态是否是巡游
function MarryWGData:CurHunYanState()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.HUNYAN)
	--故意返回为空的为了在空值的时候不做操作
	if activity_info then
		return
	end

	if activity_info.status == HUNYAN_STATE_TYPE.HUNYAN_STATE_TYPE_XUNYOU then
		return true
	end
	return false
end

--退出情缘副本是否需要打开仙侣的副本界面
function MarryWGData:IsNeedOpenFbView()
	local fb_info = self:GetQyFbInfo()
	local other_cfg = self:GetMarryOtherCfg()
	if nil == fb_info or IsEmptyTable(fb_info) or nil == other_cfg then
		return false
	end

	local can_buy_num = other_cfg.fb_buy_times_limit - fb_info.self_buy_jion_fb_times
	local fb_num = other_cfg.fb_free_times_limit + fb_info.self_buy_jion_fb_times + fb_info.total_lover_buy_fb_times - fb_info.join_fb_times
	if can_buy_num > 0 or fb_num > 0 then
		return true
	end

	return false
end

function MarryWGData:IsSingleEnterFB()
	local team_list = SocietyWGData.Instance:GetTeamMemberList()
	return IsEmptyTable(team_list) or #team_list == 1
end

function MarryWGData:OnSCLiaoYiLiaoObjInfo(protocol)
	--print_error("OnSCLiaoYiLiaoObjInfo", protocol)
	self.lyl_obj_info = protocol

	if ViewManager.Instance:IsOpenByIndex(GuideModuleName.Marry, MARRY_TAB_TYPE.JH) then
		ViewManager.Instance:FlushView(GuideModuleName.Marry, MARRY_TAB_TYPE.JH)
	end

	self:IsShowMainUIChatIcon(false)
end

function MarryWGData.GetLiaoYiLiaoInteractionVo()
	return {
		role_id = 0,
		target_role_id = 0,
		friend_flag = 0,
		is_harass = 0,
		content_ids = {},
	}
end

function MarryWGData:OnSCLiaoYiLiaoInteraction(protocol)
	--print_error("OnSCLiaoYiLiaoInteraction", protocol)
	-- self.lyl_interaction = protocol
	if not self.lyl_interaction then
		self.lyl_interaction = {}
	end

	local target_role_id = protocol.target_role_id == RoleWGData.Instance:InCrossGetOriginUid() and protocol.role_id or protocol.target_role_id
	local vo = MarryWGData.GetLiaoYiLiaoInteractionVo()
	vo.role_id = protocol.role_id
	vo.target_role_id = protocol.target_role_id
	vo.friend_flag = protocol.friend_flag
	vo.is_harass = protocol.is_harass
	for k,v in pairs(protocol.content_ids) do
		local content_info = {}
		content_info.sender_role_id = v.sender_role_id
		content_info.content_id = v.content_id
		content_info.is_lyl_harass = v.is_lyl_harass
		content_info.msg_timestamp = v.msg_timestamp
		vo.content_ids[k] = content_info
	end

	self.lyl_interaction[target_role_id] = vo
	--print_error("self.lyl_interaction[target_role_id] =", target_role_id, self.lyl_interaction[target_role_id])
	SocietyWGData.Instance:OnSCLiaoYiLiaoInteraction(vo)
end

function MarryWGData:OnSCLiaoYiLiaoRoleStatusChange(protocol)
	SocietyWGData.Instance:UpdateLYLRoleInfoOnLine(protocol.role_id,protocol.status_flag)
end

function MarryWGData:GetLiaoYiLiaoInteraction(role_id)
	return self.lyl_interaction and self.lyl_interaction[role_id]
end

function MarryWGData:RemoveLiaoYiLiaoInteraction(role_id)
	if not IsEmptyTable(self.lyl_interaction) and self.lyl_interaction[role_id] then
		self.lyl_interaction[role_id] = nil
	end
end

function MarryWGData:GetLiaoYiLiaoObjInfo()
	return self.lyl_obj_info
end

function MarryWGData:GetIsLYLChangeRoleInCD()
	return self.lyl_change_role_cd_end_time >= Status.NowTime
end

function MarryWGData:UpdateLYLChangeRoleCD()
	if not self.lyl_change_role_cfg_cd_end_time then
		local cfg = ConfigManager.Instance:GetAutoConfig("liao_yi_liao_auto")
		self.lyl_change_role_cfg_cd_end_time = (cfg and cfg.other) and cfg.other[1].fresh_cd
	end

	self.lyl_change_role_cd_end_time = Status.NowTime + self.lyl_change_role_cfg_cd_end_time
end

function MarryWGData:GetLYLChangeRoleRemainCD()
	local cd = self.lyl_change_role_cd_end_time - Status.NowTime
	return cd > 0 and cd or 0
end

function MarryWGData:IsShowMainUIChatIcon(is_send_req_when_info_empty)
	if not self.lyl_chat_icon_show_level_limit then
		local cfg = ConfigManager.Instance:GetAutoConfig("liao_yi_liao_auto")
		self.lyl_chat_icon_show_level_limit = (cfg and cfg.other) and cfg.other[1].level_limit or 0
	end

	-- print_error("self.lyl_chat_icon_show_level_limit, self.is_had_show_main_ui_chat =", self.lyl_chat_icon_show_level_limit, self.is_had_show_main_ui_chat)
	if not self.is_had_show_main_ui_chat 
		and RoleWGData.Instance:GetRoleLevel() < self.lyl_chat_icon_show_level_limit
		and FunOpen.Instance:GetFunIsOpened("marry")
		and RoleWGData.Instance:GetRoleVo().lover_uid <= 0 then
		if self.lyl_obj_info and self.lyl_obj_info.role_id > 0 then
			self.is_had_show_main_ui_chat = true
			-- MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.LIAOYILIAO, 1, function()
			-- 	ViewManager.Instance:Open(GuideModuleName.Marry, MARRY_TAB_TYPE.JH)
			-- 	--MainuiWGCtrl.Instance:RemoveTipIconByIconObj(MAINUI_TIP_TYPE.LIAOYILIAO)
			-- end)
		elseif is_send_req_when_info_empty then
			MarryWGCtrl.Instance:ReqChangeLYLRole(true)
		end

		if self.fun_open_event then
			FunOpen.Instance:UnNotifyFunOpen(self.fun_open_event)
			self.fun_open_event = nil
		end
	else
		if not self.fun_open_event and not FunOpen.Instance:GetFunIsOpened("marry") then
			self.fun_open_event = BindTool.Bind(self.FunOpenEvent, self)
			FunOpen.Instance:NotifyFunOpen(self.fun_open_event)
		end
	end
end

function MarryWGData:FunOpenEvent(fun_name)
	if fun_name == "marry" then
		self:IsShowMainUIChatIcon(true)
	end
end

--============================================================================================
--赠花进阶信息
function MarryWGData:SetSendFlowersUpgradeData(protocol)
	self.flower_score_upgrade_level = protocol.flower_score_upgrade_level
	self.send_flower_num = protocol.send_flower_num
end

function MarryWGData:GetFlowerScoreUpgradeLevel()
	return self.flower_score_upgrade_level
end

function MarryWGData:GetSendFlowerNum()
	return self.send_flower_num or 0
end

function MarryWGData:GetSendFlowersUpgradeByLevel(level)
	return self.flower_score_upgrade_cfg[level]
end

function MarryWGData:GetSendFlowersItemList()
	local list = {}
	local cfg = self:GetSendFlowerCfg()
	for k,v in ipairs(cfg) do
		if v.is_show == 1 then
			table.insert(list, {item_id = v.item_id})
		end
	end

	return list
end

function MarryWGData:GetSendFlowersUpgradeMaxLevel()
	return self.flower_score_upgrade_cfg[#self.flower_score_upgrade_cfg].level or 0
end

function MarryWGData:GetSendFlowersUpgradeRemind()
	if not FunOpen.Instance:GetFunIsOpened(FunName.Marry) then
		return 0
	end

	if self.flower_score_upgrade_level == -1 then
		return 0
	end

	local send_flower_cfg = self:GetSendFlowersUpgradeByLevel(self.flower_score_upgrade_level + 1)
	if send_flower_cfg then
		local remind = self.send_flower_num >= send_flower_cfg.need_num and 1 or 0
		self:SendFlowersUpgradeToBeStrengthen(remind)
		return remind
	end

	self:SendFlowersUpgradeToBeStrengthen(0)
	return 0
end

function MarryWGData:GetSendFlowersItemRemind()
	local slower_data = self:GetSendFlowerCfg()
	for k, v in pairs(slower_data) do
		local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
		if item_num > 0 then
			self:SendFlowersItemToBeStrengthen(1)
			return 1
		end
	end

	self:SendFlowersItemToBeStrengthen(0)
	return 0
end

function MarryWGData:GetSendFlowersItemRemindByType(type)
	local slower_data = self:GetSendFlowerCfg()
	for k, v in pairs(slower_data) do
		if v.type == type then
			local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
			if item_num > 0 then
				self:SendFlowersItemToBeStrengthen(1)
				return 1
			end
		end
	end

	self:SendFlowersItemToBeStrengthen(0)
	return 0
end

function MarryWGData:SendFlowersUpgradeToBeStrengthen(remind)
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.SEND_FLOWER_UP_GRADE, remind, function ()
		ViewManager.Instance:Open(GuideModuleName.Flower, TabIndex.flower_upgrade)
		--MarryWGCtrl.Instance:Open(TabIndex.marry_flowers)
		return true
	end)
end

function MarryWGData:SendFlowersItemToBeStrengthen(remind)
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIANLV_ZENGSONG, remind, function ()
		ViewManager.Instance:Open(GuideModuleName.Flower, TabIndex.flower_send)
		return true
	end)
end

function MarryWGData:IsCanGetTargetGatheObj(target_obj_id)
	local scene_ins = Scene.Instance
	local obj_list = scene_ins:GetGatherList()
	if not obj_list or IsEmptyTable(obj_list) then
		return false
	end

	local target_obj = obj_list[target_obj_id]
	if target_obj and not self:GetIsHasGather(target_obj_id) then
		GuajiWGCtrl.Instance:MoveToPos(scene_ins:GetSceneId(), target_obj.vo.pos_x, target_obj.vo.pos_y, 3, nil, nil, nil, function()
		    if nil ~= target_obj and not target_obj:IsDeleted() then
		        target_obj:OnClick()
		        GuajiWGCtrl.Instance:CheckCanGather(target_obj)
		    end
		end)

		return true
    end

	local weeding_info = self:GetWeddingRoleInfo() or {}
	if weeding_info and not IsEmptyTable(weeding_info) then
		local red_id, red_max, jiuxi_id, jiuxi_max, today_jiuxi_num = self:GetWeedingSceneCfg()
		if red_id and red_max and jiuxi_id and jiuxi_max and today_jiuxi_num then
			local can_gather_jiuxi = weeding_info.has_gather_num < tonumber(jiuxi_max) --是否可以继续采集酒席
			local can_gather_red_bag = weeding_info.has_gather_red_bag < red_max 		--是否可以继续采集喜酒/糖果
			if not can_gather_jiuxi and not can_gather_red_bag then
				return false
			end

			local weeding_info = self:GetWeddingRoleInfo() or {}
			if IsEmptyTable(weeding_info) then
				return false
			end

			local main_role = scene_ins:GetMainRole()
		    local main_role_x, main_role_y = main_role:GetLogicPos()
		    local target_distance = 10000
		    local target_x, target_y, distance = 0, 0, 0
			for k, v in pairs(obj_list) do
				local obj_id = v:GetObjId()
				local vo = v:GetVo()
			    target_x, target_y = v:GetLogicPos()
			    distance = GameMath.GetDistance(main_role_x, main_role_y, target_x, target_y, false)
			    if distance < target_distance then
					if can_gather_jiuxi and vo.gather_id == jiuxi_id then
						if not self:GetIsHasGather(obj_id) then
							target_obj = v
							target_distance = distance
						end
					elseif can_gather_red_bag and vo.gather_id == red_id then
						target_obj = v
						target_distance = distance
					end
			    end
			end
			
			if target_obj then
				GuajiWGCtrl.Instance:MoveToPos(scene_ins:GetSceneId(), target_obj.vo.pos_x, target_obj.vo.pos_y, 3, nil, nil, nil, function()
				    if nil ~= target_obj and not target_obj:IsDeleted() then
				        target_obj:OnClick()
				        GuajiWGCtrl.Instance:CheckCanGather(target_obj)
				    end
				end)

				return true
		    end
		end
	end

	return false
end

-------------------------------------推荐情缘------------------------------------------
function MarryWGData:SetRandomSingleRoleList(protocol)
	self.marry_recommend_role_list = protocol.role_list
end

function MarryWGData:GetRecommendRoleList()
	return self.marry_recommend_role_list
end

-------------------------------------寻缘------------------------------------------
function MarryWGData:GetXunyuanRandomCfg()
	return self.xunyuan_random_cfg
end
