GuildInviteSceneLogic = GuildInviteSceneLogic or BaseClass(CommonFbLogic)

function GuildInviteSceneLogic:__init()
	self.interval_time = 0
end

function GuildInviteSceneLogic:__delete()
	if self.fake_boss_obj then
		self.fake_boss_obj:DeleteMe()
		self.fake_boss_obj = nil
	end
end

function GuildInviteSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	local main_role = Scene.Instance:GetMainRole()
    ViewManager.Instance:Open(GuideModuleName.GuildInviteSceneView)
    -- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(false)
	self:SetLeaveFbTip(true)
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	self.interval_time = 0
end

function GuildInviteSceneLogic:Out()
	CommonFbLogic.Out(self)
	ViewManager.Instance:Close(GuideModuleName.GuildInviteSceneView)
	self.interval_time = 0
	-- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(true)
end


function GuildInviteSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
	self.interval_time = self.interval_time + elapse_time 
	-- if self.interval_time > 1 then
		local other_cfg = GuildInviteWGData.Instance:GetOtherCfg()
		local duration_time = other_cfg.duration_time
		local boss_fresh_timestamp = GuildInviteWGData.Instance:GetBossFreshTimestamp()
		if boss_fresh_timestamp and boss_fresh_timestamp + duration_time > 0 then
			local total_time = boss_fresh_timestamp + duration_time
			local time = TimeWGCtrl.Instance:GetServerTime()
			self:UpdateInviteCallBack(time,total_time)
		end
		if boss_fresh_timestamp and TimeWGCtrl.Instance:GetServerTime() < boss_fresh_timestamp then
			UiInstanceMgr.Instance:ShowFBStartDown2(boss_fresh_timestamp)
		end
		self.interval_time = 0
	-- end
end

function GuildInviteSceneLogic:UpdateInviteCallBack(elapse_time, total_time)
	if total_time - elapse_time > 0 then
		local boss_obj = self:GetFakeBossObj()
		if boss_obj then
			local other_cfg = GuildInviteWGData.Instance:GetOtherCfg()
			local duration_time = other_cfg.duration_time
			local boss_fresh_timestamp = GuildInviteWGData.Instance:GetBossFreshTimestamp()
			local max_hp = boss_fresh_timestamp + duration_time
			local time = TimeWGCtrl.Instance:GetServerTime()
			local hp = duration_time - (time - boss_fresh_timestamp)
			boss_obj.vo.hp = hp
			boss_obj.vo.max_hp = duration_time
			GlobalEventSystem:Fire(ObjectEventType.TARGET_HP_CHANGE, boss_obj) 
		end
	end
end

function GuildInviteSceneLogic:UpdateInviteComplete()

end

function GuildInviteSceneLogic:GetFakeBossObj()
	if self.fake_boss_obj == nil then
		local other_cfg = GuildInviteWGData.Instance:GetOtherCfg()
		local monster_id = other_cfg and other_cfg.boss_id or 0
		local scene_obj = Scene.Instance:GetMonsterByMonsterId(monster_id)
		if scene_obj == nil then
			return
		end
		local scene_obj_vo = scene_obj.vo
		local monster_vo = GameVoManager.Instance:CreateVo(MonsterVo)
		monster_vo.obj_id = scene_obj_vo.obj_id
	    monster_vo.monster_id = scene_obj_vo.monster_id
	    monster_vo.is_has_owner = scene_obj_vo.is_has_owner
	    monster_vo.pos_x = scene_obj_vo.pos_x
	    monster_vo.pos_y = scene_obj_vo.pos_y
	    monster_vo.hp = scene_obj_vo.hp
	    monster_vo.level = scene_obj_vo.level
	    monster_vo.max_hp = scene_obj_vo.max_hp
	    monster_vo.move_speed = scene_obj_vo.move_speed
	    monster_vo.dir = scene_obj_vo.dir
	    monster_vo.distance = scene_obj_vo.distance
	    -- monster_vo.buff_mark_low = scene_obj_vo.buff_mark_low
	    -- monster_vo.buff_mark_high = scene_obj_vo.buff_mark_high
	    monster_vo.buff_flag = scene_obj_vo.buff_flag
	    monster_vo.lose_owner_time = scene_obj_vo.lose_owner_time
	    monster_vo.gamer_name = scene_obj_vo.gamer_name
	    monster_vo.special_param = scene_obj_vo.special_param
	    monster_vo.summoner_obj_id = scene_obj_vo.summoner_obj_id
	    monster_vo.wabao_owner_uuid = scene_obj_vo.wabao_owner_uuid
	    monster_vo.show_fake_hp = true
		self.fake_boss_obj = Boss.New(monster_vo)
	end
	return self.fake_boss_obj
end

function GuildInviteSceneLogic:OnClickHeadHandler(is_show)
	CommonActivityLogic.OnClickHeadHandler(self, is_show)
end
