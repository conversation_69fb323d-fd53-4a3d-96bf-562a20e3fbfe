NewPlayerFbBattle1SceneLogic = NewPlayerFbBattle1SceneLogic or BaseClass(NewPlayerFbSceneLogic)
local new_player_battle_scene_id = 155 --新手战斗场景id

function NewPlayerFbBattle1SceneLogic:__init()
	self.whale_obj = nil
end

function NewPlayerFbBattle1SceneLogic:__delete()

end

function NewPlayerFbBattle1SceneLogic:Enter(old_scene_type, new_scene_type)
	NewPlayerFbSceneLogic.Enter(self, old_scene_type, new_scene_type)
	--[[
	local bundle, asset = ResPath.GetMountModel(1007)
	self.kun_loader = AllocAsyncLoader(self, "kun_model_loader")
	self.kun_loader:SetIsUseObjPool(true)
	self.kun_loader:SetParent(G_SceneObjLayer)
	self.kun_loader:Load(bundle, asset, function(obj)
        if IsNil(obj) then
            self.kun_loader:Destroy()
			return
		end
		self.whale_obj = U3DObject(obj)
		self.whale_obj.transform.position = Vector3(195, 119, -235)
		self.whale_obj.transform:SetLocalScale(7, 7, 7)
		self.whale_obj.transform:DOLocalMoveZ(-50, 18)
	end)

	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.Kun, nil, true))
	]]
end

function NewPlayerFbBattle1SceneLogic:Update(now_time, elapse_time)
	NewPlayerFbSceneLogic.Update(self, now_time, elapse_time)
end

function NewPlayerFbBattle1SceneLogic:Out()
	NewPlayerFbSceneLogic.Out(self)
    -- if self.kun_loader then
    --     self.kun_loader:Destroy()
    --     self.whale_obj = nil
    -- end
end

function NewPlayerFbBattle1SceneLogic:GuaiJiMonsterUpdate(now_time, elapse_time)
	self.guai_ji_next_move_time = Status.NowTime - 3

	-- if BaseSceneLogic.GuaiJiMonsterUpdate(self, now_time, elapse_time) then
	-- 	return
	-- end

	-- local list = Scene.Instance:GetMonsterList()
	-- if not IsEmptyTable(list) then return end

	-- local pos_x, pos_y = GuajiWGCtrl.Instance:GetPickPos()
	-- print_error(pos_x, pos_y)
	-- if not (pos_x == 0 and pos_y == 0) then return end

	-- local gather_list = Scene.Instance:GetGatherList()
	-- local _, gather_obj = next(gather_list)
	-- if nil ~= gather_obj and MainuiWGData.Instance:GetTargetObj() == nil then
	-- 	-- self:MoveToObj(gather_obj)
	-- 	-- MainuiWGCtrl.Instance:SetTargetObj(gather_obj)
	-- 	GuajiWGCtrl.Instance:StopGuaji()
	-- 	local scene_id = Scene.Instance:GetSceneId()
	-- 	local gjc = GuajiWGCtrl.Instance
	-- 	local x, y = gather_obj:GetLogicPos()
	-- 	gjc:SetMoveToPosCallBack(self.start_gather_event)
	-- 	gjc:MoveToPos(scene_id, x, y)
	-- 	self.move_to_gather = true
	-- end
end

function NewPlayerFbBattle1SceneLogic:StartGather()
	-- local gather_list = Scene.Instance:GetGatherList()
	-- local _, gather_obj = next(gather_list)
	-- if gather_obj then
		-- GuajiWGCtrl.Instance:OnSelectObj(gather_obj, SceneTargetSelectType.SCENE)
		-- AtkCache.is_valid = false
	-- end
end

function NewPlayerFbBattle1SceneLogic:GetMonsterJumpLogicPos(monster_obj)
	local fuben_data = FuBenWGData.Instance:GetNewPlayerFBInfo()
	if not fuben_data or not next(fuben_data) then
		return nil, nil
	end
	local seq = monster_obj.vo.special_param % 10000
	local wave = (monster_obj.vo.special_param - seq) / 10000
	local cfg = FuBenWGData.Instance:GetNewPlayerFbMonsterCfg(fuben_data.fb_type, wave, seq)
	if cfg and cfg.jumppos_y ~= -1 and cfg.jumppos_x ~= -1 then
		self.monster_index = self.monster_index + 1
		return cfg.jumppos_x, cfg.jumppos_y
	end
	return nil, nil
end

function NewPlayerFbBattle1SceneLogic:CanMonsterDoJump()
	local fuben_data = FuBenWGData.Instance:GetNewPlayerFBInfo()
	if not fuben_data or not next(fuben_data) then
		return false
	end
	local cfg = FuBenWGData.Instance:GetNewPlayerFbMonsterCfg(fuben_data.fb_type)
	if cfg then
		return true
	end
	return false
end
