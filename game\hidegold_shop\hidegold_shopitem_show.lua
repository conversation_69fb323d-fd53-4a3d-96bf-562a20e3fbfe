HideGoldShopItemShowView = HideGoldShopItemShowView or BaseClass(SafeBaseView)
function HideGoldShopItemShowView:__init()
    self.view_layer = UiLayer.Normal
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(650, 460)})
    self:AddViewResource(0, "uis/view/hidegold_shop_ui_prefab", "layout_hidegold_show_shop")
    self:SetMaskBg(true, true)
end

function HideGoldShopItemShowView:ReleaseCallBack()
    if self.probability_list then
        self.probability_list:DeleteMe()
        self.probability_list = nil
    end
end

function HideGoldShopItemShowView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.HideGoldShop.ShopTipsTitle
     if not self.probability_list then
        self.probability_list = AsyncListView.New(HideGoldShopItemShowRender, self.node_list.ph_pro_list) 
    end
end

function HideGoldShopItemShowView:OnFlush()
    local info = HideGoldShopWGData.Instance:GetShopCfg()
    self.probability_list:SetDataList(info)
end

----------------------------------------------------------------------------------
HideGoldShopItemShowRender = HideGoldShopItemShowRender or BaseClass(BaseRender)
function HideGoldShopItemShowRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list.index_text.text.text = self.data.seq + 1
    local color = ItemWGData.Instance:GetItemColor(self.data.item.item_id)
    local item_name = ItemWGData.Instance:GetItemName(self.data.item.item_id)
    self.node_list.name_text.text.text = ToColorStr(item_name, color)

    local is_limit = HideGoldShopWGData.Instance:GetIsLimitShop(self.data.seq)
    if is_limit then --是限购商品
        local already_buy_times = HideGoldShopWGData.Instance:GetShopLimitTimes(self.data.seq)
        local times_color = (self.data.times_limit - already_buy_times) <= 0 and COLOR3B.RED or COLOR3B.GREEN
        local str_col = ToColorStr(string.format(Language.HideGoldShop.LimitTimes, (self.data.times_limit - already_buy_times), self.data.times_limit), times_color)
        self.node_list.probability_text.text.text = str_col
    else
        self.node_list.probability_text.text.text = Language.HideGoldShop.NoLimitShopItem
    end
    
end
