-- 仙盟签到红包

function GuildView:InitGuildSignView()
	if not self.sigh_reward_list then
		self.sigh_reward_list = AsyncListView.New(ItemCell, self.node_list["sigh_reward_list"])
		self.sigh_reward_list:SetStartZeroIndex(false)
	end

	if not self.red_packet_item_list then
		self.red_packet_item_list = {}
        local node_num = self.node_list.red_packet_list.transform.childCount
        for i = 1, node_num do
            self.red_packet_item_list[i] = GuildSignRedPacketItem.New(self.node_list.red_packet_list:FindObj("red_packet_" .. i))
        end
	end
end

function GuildView:GuildSignReleaseCallBack()
	if self.sigh_reward_list then
		self.sigh_reward_list:DeleteMe()
		self.sigh_reward_list = nil
	end

	if self.red_packet_item_list then
		for k, v in pairs(self.red_packet_item_list) do
			v:DeleteMe()
		end
		self.red_packet_item_list = nil
	end
end

function GuildView:GuildSignShowIndexCallBack()

end

function GuildView:OnFlushGuildSignView(param_t)
	local guild_sign_num = GuildWGData.Instance:GetGuildSignNum()
	self.node_list["txt_sign_member_count"].text.text = guild_sign_num

	local sing_redpacket_cfg = GuildWGData.Instance:GetSignRedPaperCfg()
	local max_sign_cfg = sing_redpacket_cfg[#sing_redpacket_cfg]
	self.node_list["sldr_sign_progress"].slider.value = guild_sign_num / max_sign_cfg.sign_times

	for i, v in ipairs(self.red_packet_item_list) do
		v:SetData(sing_redpacket_cfg[i])
	end

	local sign_reward_list = GuildWGData.Instance:GetSignRewardList()
	self.sigh_reward_list:SetDataList(sign_reward_list)

	local sign_flag = GuildWGData.Instance:GetGuildSignFlag()
	self.node_list["sign_flag"]:SetActive(sign_flag == 1)
end

-----------------------------------
-- 仙盟签到红包Item
-----------------------------------
GuildSignRedPacketItem = GuildSignRedPacketItem or BaseClass(BaseRender)

function GuildSignRedPacketItem:__init()
end

function GuildSignRedPacketItem:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_send"], BindTool.Bind1(self.OnClickSendRedpacketBtn, self))
end

function GuildSignRedPacketItem:ReleaseCallBack()

end

function GuildSignRedPacketItem:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end
	
	local send_flag = GuildWGData.Instance:GetGuildSignRedPaperFlag(self.data.seq)
	self.node_list["txt_sended"]:SetActive(send_flag == 1)
	self.node_list["btn_send"]:SetActive(send_flag == 0)

	local guild_sign_num = GuildWGData.Instance:GetGuildSignNum()
	local is_act = guild_sign_num >= self.data.sign_times
	local str = is_act and "已激活" or (self.data.sign_times .. "人激活")
	self.node_list["txt_send_condition"].text.text = str
	--self.node_list["txt_red_amount"].text.text = 
end

function GuildSignRedPacketItem:OnClickSendRedpacketBtn()
	if IsEmptyTable(self.data) then
		return
	end

	local send_flag = GuildWGData.Instance:GetGuildSignRedPaperFlag(self.data.seq)
	if send_flag == 1 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.SendRedPacketTips)
		return
	end

	local guild_post = RoleWGData.Instance.role_vo.guild_post
	if guild_post ~= GUILD_POST.TUANGZHANG and guild_post ~= GUILD_POST.FU_TUANGZHANG then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.SendRedPacketCondition1)
		return
	end

	local guild_sign_num = GuildWGData.Instance:GetGuildSignNum()
	if guild_sign_num < self.data.sign_times then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.SendRedPacketCondition2)
		return
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local other_cfg = GuildWGData.Instance:GetGuildOtherCfg()
	local can_send_time = TimeUtil.FormatCfgTimestamp(other_cfg.sign_redpaper_time)
	if server_time < can_send_time then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.SendRedPacketCondition3)
		return
	end

	GuildWGCtrl.Instance:SendCSGuildOperateRequest(GUILD_OPERATE_TYPE.DISTRIBUTE_SIGN_REDPAPER, self.data.seq)
	local redpacket_cfg = WelfareWGData.Instance:GetWelfareGuildRedpaperTypeCfg(SPECIAL_SEND_TYPE.GUILD_SIGN, self.data.redpaper_level)
	if redpacket_cfg then
		local content = redpacket_cfg.Chanel_descript .. "{openLink;119}"
		ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.GUILD, content, CHAT_CONTENT_TYPE.TEXT, nil, nil, true)
	end
end