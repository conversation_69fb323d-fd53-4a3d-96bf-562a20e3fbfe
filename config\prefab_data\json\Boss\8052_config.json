{"actorController": {"projectiles": [], "hurts": [], "beHurtEffecct": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "hurtEffectName": "", "beHurtNodeName": "", "beHurtAttach": false}, "actorTriggers": {"effects": [{"triggerEventName": "attack1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "boss8052_attack", "effectAsset": {"BundleName": "effects/prefab/model/boss/8052/boss8052_attack_prefab", "AssetName": "boss8052_attack", "AssetGUID": "1dc8b82fd6d9ef941947398eead5a985", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": false, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "boss8052_skill", "effectAsset": {"BundleName": "effects/prefab/model/boss/8052/boss8052_skill_prefab", "AssetName": "boss8052_skill", "AssetGUID": "79aa7d4f6c42cf24dba9196946dbc1bd", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": false, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2", "playerAtPos": false, "ignoreParentScale": false}], "sounds": [], "cameraShakes": [], "radialBlurs": []}}