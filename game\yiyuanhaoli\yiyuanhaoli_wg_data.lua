YiYuanHaoLiWGData = YiYuanHaoLiWGData or BaseClass()
function YiYuanHaoLiWGData:__init()
	if YiYuanHaoLiWGData.Instance then
		error("[YiYuanHaoLiWGData] Attempt to create singleton twice!")
		return
	end
	YiYuanHaoLiWGData.Instance = self

	--配置--目前只有一档
	-- local yyhl_all_cfg = ConfigManager.Instance:GetAutoConfig("onemoneybuycfg_auto")
	-- self.yyhl_all_cfg_by_index = ListToMap(yyhl_all_cfg.reward_cfg, "index")

	--参数
	self.yyhl_is_buy_flag = nil
	self.yyhl_activity_time = nil
	self.yyhl_rmb_seq = nil

end

function YiYuanHaoLiWGData:__delete()

	self.yyhl_is_buy_flag = nil
	self.yyhl_activity_time = nil
	self.yyhl_rmb_seq = nil

	YiYuanHaoLiWGData.Instance = nil
end

function YiYuanHaoLiWGData:OnSConeMoneyBuyInfo(protocol)
	self.yyhl_is_buy_flag = bit:d2b_two(protocol.flag or 0)
	self.yyhl_rmb_seq = protocol.rmb_seq or 0
	self.yyhl_activity_time = protocol.activity_time
end

function YiYuanHaoLiWGData:GetYiYuanHaoLiIsBuy()
	return self.yyhl_is_buy_flag[self.yyhl_rmb_seq] == 1
end

function YiYuanHaoLiWGData:GetYiYuanHaoLiActTime()
	return self.yyhl_activity_time
end

function YiYuanHaoLiWGData:GetYiYuanHaoLiCfg()
	local yyhl_all_cfg = ConfigManager.Instance:GetAutoConfig("onemoneybuycfg_auto")
	return yyhl_all_cfg and yyhl_all_cfg.reward_cfg[self.yyhl_rmb_seq + 1]
end

function YiYuanHaoLiWGData:GetYiYuanHaoLiRewardList()
	--奖励列表
	local show_data = {}
	local cfg = self:GetYiYuanHaoLiCfg()
	if cfg and not IsEmptyTable(cfg.reward_list) then
		local temp_data = SortDataByItemColor(cfg.reward_list)
		for k, v in pairs(temp_data) do
			show_data[k] = {}
			show_data[k].item_id = v.item_id
			show_data[k].num = v.num
			show_data[k].is_bind = v.is_bind
			if v.item_id == cfg.show_special then
				show_data[k].subscrip_flag = Language.Common.JveBan
				show_data[k].subscrip_biaoqian_flag = 3
			end
		end
	end

	return show_data
end