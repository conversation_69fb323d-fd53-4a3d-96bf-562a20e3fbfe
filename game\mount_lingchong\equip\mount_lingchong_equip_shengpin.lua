--骑宠装备升品
MountLingChongEquipView = MountLingChongEquipView or BaseClass(SafeBaseView)

function MountLingChongEquipView:InitShengPinView()
    self.node_list.btn_quick_upgrade.button:AddClickListener(BindTool.Bind(self.OnClickQuickSp, self))--快捷升品
    self.node_list.btn_upgrade.button:AddClickListener(BindTool.Bind(self.OnClickSp, self))--升品
    self.node_list.btn_add_item.button:AddClickListener(BindTool.Bind(self.OnClickSelectBtn, self))--打开选择装备界面
    self.node_list.mask_btn.button:AddClickListener(BindTool.Bind(self.OnClickSelectBtn, self, true))--打开选择装备界面
    
    if not self.quality_stuff_item then --必定需要的
        self.quality_stuff_item = ItemCell.New(self.node_list.upgrade_item_pos_1)
    end
    -- if not self.cur_quality_item then
    --     self.cur_quality_item = MountPetStarItem.New(self.node_list.cur_shengpin_item, true)
    --     --self.cur_quality_item:SetIsShowTips(false)
    -- end
    if not self.target_quality_item then
        self.target_quality_item = MountPetStarItem.New(self.node_list.target_shengpin_item)
    end
end

function MountLingChongEquipView:ShowShengPinCallBack()
    self.shengpin_item_id = 0
end

function MountLingChongEquipView:OnSelectEquipQualityItemHandler()
    local cur_item_data = self:GetCurSelectData()
    if not cur_item_data then
        return
    end

    if self.shengpin_item_id == cur_item_data.item_id then
        return
    end

    MountLingChongEquipWGData.Instance:SaveShengpinSelectList({})
    self.shengpin_item_id = cur_item_data.item_id
    self:FlushShengPinView()
end

function MountLingChongEquipView:PlayShengPinSuccess()
    if self.node_list.quality_effect_root then
        self.node_list.quality_effect_root:SetActive(true)
        TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengpin, is_success = true, pos = Vector2(0, 0),
            parent_node = self.node_list["quality_effect_root"]})
    end
end

function MountLingChongEquipView:FlushShengPinView()
    local cur_item_data = self:GetCurSelectData()
    if not cur_item_data then
        return
    end

    --目标item
    local target_cfg = MountLingChongEquipWGData.Instance:GetComposeCfgByItemId(cur_item_data.item_id)
    if not IsEmptyTable(target_cfg) then
        local data = {}
        data.item_id = target_cfg.compose_equip_id
        self.target_quality_item:SetData(data)
        self.target_quality_item:SetLockImgEnable(false)
        self.node_list.sp_max_state:SetActive(false)
        self.node_list.sp_nomax_state:SetActive(true)
        self.node_list.btn_upgrade:CustomSetActive(true)
        self.node_list.target_shengpin_item_name.text.text = ItemWGData.Instance:GetItemName(target_cfg.compose_equip_id)
    else
        local data = {}
        data.item_id = 0
        data.is_bind = cur_item_data.is_bind
        self.target_quality_item:SetData(data)
        self.target_quality_item:SetLockImgEnable(true)
        self.node_list.sp_max_state:SetActive(true)
        self.node_list.sp_nomax_state:SetActive(false)
        self.node_list.btn_upgrade:CustomSetActive(false)
        self.node_list.target_shengpin_item_name.text.text = ""
    end
    
    self:FlushQualityStuffItem()
    self:FlushQualityAttrs()
    local remind = MountLingChongEquipWGData.Instance:EquipShengpinRemind(self.cur_show_type) == 1
    self.node_list.quick_upgrade_remind:SetActive(remind)  
end

function MountLingChongEquipView:DeleteShengPinView()
    if self.quality_stuff_item then
        self.quality_stuff_item:DeleteMe()
    end
    self.quality_stuff_item = nil
    -- if self.cur_quality_item then
    --     self.cur_quality_item:DeleteMe()
    -- end
    -- self.cur_quality_item = nil
    if self.target_quality_item then
        self.target_quality_item:DeleteMe()
    end
    self.shengpin_item_id = nil
    self.target_quality_item = nil
end

--刷新右边属性 sp_strength_attrt_item2  sp_lbl_now_text_4  sp_lbl_next_text_5
function MountLingChongEquipView:FlushQualityAttrs()
    local cur_item_data = self:GetCurSelectData()
    if not cur_item_data then
        return
    end

    local target_cfg = MountLingChongEquipWGData.Instance:GetComposeCfgByItemId(cur_item_data.item_id)
    local is_max = target_cfg == nil
    local equip_cfg = MountLingChongEquipWGData.Instance:GetMountLingChongEquipData(cur_item_data.item_id)
    local now_attr = MountLingChongEquipWGData.Instance:ConvertAttrType(equip_cfg)
    local target_equip_cfg, next_attr, lerp_attr
    if not is_max then
        target_equip_cfg = MountLingChongEquipWGData.Instance:GetMountLingChongEquipData(target_cfg.compose_equip_id)
        next_attr = MountLingChongEquipWGData.Instance:ConvertAttrType(target_equip_cfg)
        lerp_attr = AttributeMgr.LerpAttributeAttr(now_attr, next_attr)
    end

    local index = 1
    local cur_percent =  equip_cfg and equip_cfg.precentage or 0
    local target_percent
    if not is_max then
        target_percent = target_equip_cfg and target_equip_cfg.precentage
    end

    for k, v in pairs(now_attr) do
        local attr_name = Language.Common.AttrNameList2[k]
        if attr_name then
            self.node_list["sp_strength_attrt_item" .. index]:SetActive(true)
            self.node_list["sp_lbl_now_text_"..index].text.text = v
            self.node_list["sp_lbl_name_text_"..index].text.text = attr_name
            if not is_max then
                self.node_list["sp_lbl_next_text_"..index].text.text = ToColorStr(lerp_attr[k], COLOR3B.GREEN)
            else
                self.node_list["sp_lbl_next_text_"..index].text.text = ToColorStr(Language.MountPetEquip.QualityMax, COLOR3B.GREEN)
            end
        end
        index = index + 1
    end

    if target_percent and target_percent == 0 and cur_percent == 0 then
        self.node_list["sp_strength_attrt_item" .. index]:SetActive(false)
    else
        if self.node_list["sp_strength_attrt_item" .. index] then
            local attr_name = Language.MountPetEquip.QualityAddition
            self.node_list["sp_strength_attrt_item" .. index]:SetActive(true)
            self.node_list["sp_lbl_now_text_"..index].text.text = cur_percent/100 .."%"
            self.node_list["sp_lbl_name_text_"..index].text.text = attr_name
            if not is_max then
                self.node_list["sp_lbl_next_text_"..index].text.text = ToColorStr(target_percent/100 .."%", COLOR3B.GREEN)
            else
                self.node_list["sp_lbl_next_text_"..index].text.text = ToColorStr(Language.MountPetEquip.QualityMax, COLOR3B.GREEN)
            end
        end
    end

    for i = index + 1, 5 do
		self.node_list["sp_strength_attrt_item" .. i]:SetActive(false)
    end
end

function MountLingChongEquipView:FlushQualityStuffItem()
    if self:IsOpen() and self:IsLoadedIndex(MountLingChongEquipViewIndex[self.cur_show_type].ShengPin) then
        local cur_item_data = self:GetCurSelectData()
        if not cur_item_data then
            return
        end

        local cur_item_id = cur_item_data.item_id or 0
        local target_cfg = MountLingChongEquipWGData.Instance:GetComposeCfgByItemId(cur_item_id)
        if not IsEmptyTable(target_cfg) then
            local first_stuff_enough = true
            local fisrt_stuff_num = 0
            if target_cfg.consume_num_1 == 0 then
                self.node_list.upgrade_item_pos_1:SetActive(false)
            else
                self.node_list.upgrade_item_pos_1:SetActive(true)
                local data = {}
                data.item_id = target_cfg.consume_equip_1
                data.num = 1
                fisrt_stuff_num = MountLingChongEquipWGData.Instance:GetItemNumInBagById(self.cur_show_type, data.item_id)
                self.quality_stuff_item:SetData(data)
                local color = fisrt_stuff_num >= 1 and COLOR3B.GREEN or COLOR3B.PINK
                first_stuff_enough = fisrt_stuff_num > 0
                self.quality_stuff_item:SetRightBottomTextVisible(true)
                self.quality_stuff_item:SetRightBottomText(ToColorStr(fisrt_stuff_num .. "/"..1 , color))
            end
            local remind1 = MountLingChongEquipWGData.Instance:GetPartWearCanShengPin(self.cur_show_type, cur_item_data.part) == 1
            self.node_list.upgrade_other_stuff_remind:SetActive(remind1 and first_stuff_enough)
            self.node_list.cur_upgrade_remind:SetActive(false)
            self.node_list.other_quality_icon:SetActive(false)
            self.node_list.other_stuff_icon:SetActive(false)
            self.node_list.btn_add_item:SetActive(true)
            local count, first_grid_idx = MountLingChongEquipWGData.Instance:GetShengpinSelectListCount()
            if target_cfg.consume_num_1 == 1 and count ~= 0 and fisrt_stuff_num > 0 then --因为批量界面已经把必定的放进去了
                count = count - 1
            end
            local need_count = target_cfg.consume_num_2
            local is_enough = count >= need_count
            local color = is_enough and COLOR3B.GREEN or COLOR3B.PINK
            self.node_list.other_stuff_num.text.text = ToColorStr(count .. "/".. need_count , color)
            --if is_enough then
                self.node_list.upgrade_other_stuff_remind:SetActive(remind1 and not is_enough)
                self.node_list.cur_upgrade_remind:SetActive(remind1 and fisrt_stuff_num and is_enough)
                if first_grid_idx  then
                    local first_item_id = MountLingChongEquipWGData.Instance:GetItemIdInBagByGridIndex(self.cur_show_type, first_grid_idx)
                    local item_cfg = ItemWGData.Instance:GetItemConfig(first_item_id)
                    if item_cfg and item_cfg.icon_id and item_cfg.icon_id > 0 then
                        self.node_list.btn_add_item:SetActive(false)
                        self.node_list.other_quality_icon:SetActive(true)
                        self.node_list.other_stuff_icon:SetActive(true)
                        local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
                        self.node_list.other_stuff_icon.image:LoadSprite(bundle, asset)
                        local bundle1, asset1 = "", ""
                        if item_cfg.color >= GameEnum.ITEM_COLOR_GREEN then
                            local item_color = item_cfg.color
                            if item_cfg.color >= GameEnum.ITEM_COLOR_GREEN then
                                local item_bg_type = "a3_ty_wpk_%d"
                                bundle1, asset1 = ResPath.GetCommonImages(string.format(item_bg_type, item_color))
                                self.node_list.other_quality_icon.image:LoadSprite(bundle1, asset1)
                            end
                        end      
                    end
                end     
            --end
        end
    end
end

function MountLingChongEquipView:OnClickQuickSp()
    MountLingchongEquipWGCtrl.Instance:OpenQuickSpView(self.cur_show_type)
end

function MountLingChongEquipView:OnClickSp()
    local cur_item_data = self:GetCurSelectData()
    if not cur_item_data then
        return
    end

    local cur_item_id = cur_item_data.item_id or 0
    local target_cfg = MountLingChongEquipWGData.Instance:GetComposeCfgByItemId(cur_item_id)
    if not IsEmptyTable(target_cfg) then
        local stuff_id = target_cfg.consume_equip_1
        local num = MountLingChongEquipWGData.Instance:GetItemNumInBagById(self.cur_show_type, stuff_id)
        if num <= 0 and target_cfg.consume_num_1 > 0 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.MountPetEquip.StuffNotEnough)
            return
        end
        local consume_grid_index_list = MountLingChongEquipWGData.Instance:GetShengpinSelectList()
        if IsEmptyTable(consume_grid_index_list) then
            local remind1 = MountLingChongEquipWGData.Instance:GetPartWearCanShengPin(self.cur_show_type, cur_item_data.part) == 1
            if remind1 then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.MountPetEquip.SelectStuff)
            end
            return
        end
        local part = cur_item_data.part
        MountLingchongEquipWGCtrl.Instance:SendMountPetEquipShengPinReq(self.cur_show_type, target_cfg.compose_equip_id, part, consume_grid_index_list)
    end
end

--is_selected 是否已经选择过
function MountLingChongEquipView:OnClickSelectBtn(is_selected)
    local cur_item_data = self:GetCurSelectData()
    if not cur_item_data then
        return
    end

    local cur_item_id = cur_item_data.item_id or 0
    local target_cfg = MountLingChongEquipWGData.Instance:GetComposeCfgByItemId(cur_item_id)
    if not IsEmptyTable(target_cfg) then
        MountLingchongEquipWGCtrl.Instance:OpenBatchShengPin(cur_item_data.item_id)
    end
end

