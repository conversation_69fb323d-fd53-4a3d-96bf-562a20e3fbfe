-- 场景特定区域场景特效控制
SceneAreaEffectWGCtrl = SceneAreaEffectWGCtrl or BaseClass(BaseWGCtrl)
function SceneAreaEffectWGCtrl:__init()
	if SceneAreaEffectWGCtrl.Instance ~= nil then
		ErrorLog("[SceneAreaEffectWGCtrl] attempt to create singleton twice!")
		return
	end
	SceneAreaEffectWGCtrl.Instance = self

	self.update_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.Update, self), 1.5)

    self.is_playing = false
	self.async_loader_1 = nil
	self.async_loader_2 = nil
end

function SceneAreaEffectWGCtrl:__delete()
	SceneAreaEffectWGCtrl.Instance = nil

	GlobalTimerQuest:CancelQuest(self.update_timer)
	self.update_timer = nil

	self:DestroyEffects()
end

function SceneAreaEffectWGCtrl:Update()
	self:CheckEnterAreaTrigger()
end

-- 检查移动到某个区域触发
function SceneAreaEffectWGCtrl:CheckEnterAreaTrigger()
	if self.pos_list == nil or self.scene_id == nil then
		local area_info = Split(TaskWGData.Instance:GetOtherInfo().area_pos_list, "|")
		self.scene_id = tonumber(area_info[1])
		local pos_str_list = Split(area_info[2], ":")
		self.pos_list = {}
		for i, v in ipairs(pos_str_list) do
			local pos = {}
			local pos_str = Split(v, ",")
			pos.x = tonumber(pos_str[1])
			pos.y = tonumber(pos_str[2])
			table.insert(self.pos_list, pos)
		end
	end

	if self.scene_id ~= Scene.Instance:GetSceneId() then
		self:DestroyEffects()
		return
	end

	if #self.pos_list < 3 then
		return
	end

	local need_play_effect_task_id = TaskWGData.Instance:GetOtherInfo().special_task

    if not TaskWGData.Instance:GetTaskIsCompleted(need_play_effect_task_id) then
    	return
    end

	local role_x, role_y = Scene.Instance:GetMainRole():GetLogicPos()
	if GameMath.IsInPolygon(self.pos_list, {x = role_x, y = role_y}) then
		if not self.is_playing then
			self:DestroyEffects()
	    	self.is_playing = true
	    	-- GlobalTimerQuest:AddDelayTimer(function() 
	    	-- 	self.is_playing = false
	    	-- end, 60)

	        local pos_cfg = Split(TaskWGData.Instance:GetOtherInfo().special_pos, "#")
	        local pos_x, pos_z = GameMapHelper.LogicToWorldEx(tonumber(pos_cfg[1]), tonumber(pos_cfg[2]))
	        local pos_y = tonumber(pos_cfg[3])

	        local bundle, asset = ResPath.GetEffect("effects_chaunsongmen_01")
	        self.async_loader_1 = AllocAsyncLoader(self, "SceneAreaEffectWGCtrl" .. bundle .. asset)
			self.async_loader_1:SetIsUseObjPool(true)
			self.async_loader_1:SetParent(G_EffectLayer)
			self.async_loader_1:Load(bundle, asset, function(obj)
				if IsNil(obj) then
					return
				end
				obj.transform.position = Vector3(pos_x, pos_y, pos_z)
				
			end)

	        local bundle, asset = ResPath.GetEffect("effects_chaunsongmen_02")
	        self.async_loader_2 = AllocAsyncLoader(self, "SceneAreaEffectWGCtrl" .. bundle .. asset)
			self.async_loader_2:SetIsUseObjPool(true)
			self.async_loader_2:SetParent(G_EffectLayer)
			self.async_loader_2:Load(bundle, asset, function(obj)
				if IsNil(obj) then
					return
				end
				obj.transform.position = Vector3(pos_x, pos_y, pos_z)
				
			end)
	    end
	else
		self:DestroyEffects()
		self.is_playing = false
	end
end

function SceneAreaEffectWGCtrl:DestroyEffects()
	if self.async_loader_1 then
		self.async_loader_1:Destroy()
		self.async_loader_1 = nil
	end
	if self.async_loader_2 then
		self.async_loader_2:Destroy()
		self.async_loader_2 = nil
	end
end