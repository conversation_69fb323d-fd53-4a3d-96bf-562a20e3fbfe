PremiumGiftWGData = PremiumGiftWGData or BaseClass()

function PremiumGiftWGData:__init()
	if PremiumGiftWGData.Instance ~= nil then
		print_error("[PremiumGiftWGData] attempt to create singleton twice!")
		return
	end
	PremiumGiftWGData.Instance = self

	local extinct_gift_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_overflow_gift_auto")
	self.main_task_cfg = ListToMapList(extinct_gift_cfg.maintask, "grade")
	self.sub_task_cfg = ListToMapList(extinct_gift_cfg.subtask, "grade", "activity_day")
	self.sub_task_reward = ListToMapList(extinct_gift_cfg.grade, "grade", "activity_day")
	self.open_day_cfg = extinct_gift_cfg.open_day
	self.other_cfg = extinct_gift_cfg.other[1]

    self.grade = -1
	self.main_long_get = 0
	self.sub_reward_flag = {}
	self.main_status_flag = {}
	self.sub_all_flag_list = {}
	self.select_title_attr_lits = {}

	if self.other_cfg and self.other_cfg.select_title_attr_lits then
		self.select_title_attr_lits = Split(self.other_cfg.select_title_attr_lits, ",")
	end
	
    RemindManager.Instance:Register(RemindName.PremiumGift, BindTool.Bind(self.GetExtinctGiftRed, self))
end

function PremiumGiftWGData:__delete()
	PremiumGiftWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.PremiumGift)
end

--------------------------------------protocol_start----------------------------------------
function PremiumGiftWGData:SetExtinctGiftInfo(protocol)
    self.grade = protocol.grade
	self.main_long_get = protocol.main_long_get
	self.sub_reward_flag = protocol.sub_reward_flag
	self.main_status_flag = protocol.main_status_flag
	self.sub_all_flag_list = protocol.sub_all_flag_list
end
---------------------------------------protocol_end-----------------------------------------

--------------------------------------common_get_start----------------------------------------
function PremiumGiftWGData:GetExtinctGrade()
	return self.grade
end

function PremiumGiftWGData:IsMainLongGet()
	return self.main_long_get > 0
end

function PremiumGiftWGData:GetSubRewardFlag(day)
	return (self.sub_reward_flag[day] or 0) < 1
end

function PremiumGiftWGData:GetMainStatusFlag(seq)
	return (self.main_status_flag[seq] or 0) < 1
end

function PremiumGiftWGData:GetSubAllFlagBuDayAndSeq(day, seq)
	return (self.sub_all_flag_list[day] or {})[seq] or 0
end

function PremiumGiftWGData:GetSubTaskRewardBuGradeAndDay(grade, day)
	return (self.sub_task_reward[grade] or {})[day] or {}
end

function PremiumGiftWGData:GetSubTaskCfg(act_day)
	local grade = self:GetExtinctGrade()
	return (self.sub_task_cfg[grade] or {})[act_day] or {}
end

function PremiumGiftWGData:GetopenDayGradeCfg()
	local grade = self:GetExtinctGrade()
	return self.open_day_cfg[grade]
end

function PremiumGiftWGData:GetSubTaskRewardCfg(day)
	local grade = self:GetExtinctGrade()
	return self:GetSubTaskRewardBuGradeAndDay(grade, day)
end

function PremiumGiftWGData:ForbidSkill()
	return ((self.other_cfg or {}).forbid_skill or 0) == 0
end

function PremiumGiftWGData:IsShowSubTask()
	return ((self.other_cfg or {}).is_show_subtask or 0) == 1
end
---------------------------------------common_get_end-----------------------------------------

---------------------------------------remind_start-------------------------------------------
function PremiumGiftWGData:GetExtinctGiftRed()
	local main_red = self:GetMainTaskRed()
	if main_red > 0 then
		return 1
	end

	if not self:NotHasSubTask() then
		local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.PREMIUM_GIFT)
		for day = 1, act_day do
			local sub_red = self:GetSubTaskRed(day)
			if sub_red > 0 then
				return 1
			end
		end
	end

	return 0
end

function PremiumGiftWGData:GetMainTaskRed()
	local main_cfg = self:GetMainTaskCfg()

	if main_cfg == nil then
		return 0
	end

	for i, v in ipairs(main_cfg) do	
		if self:GetMainTaskState(v.seq) < 1 or self:IsMainLongGet() then
			return 0
		end
	end

	return 1
end

function PremiumGiftWGData:GetSubTaskRed(day)
	local sub_task_list = self:GetSubTaskList(day)
	if sub_task_list == nil then
		return 0
	end
	local sub_get_state = self:GetSubTaskRewardState(day)
	for k, v in pairs(sub_task_list) do
		if not v.is_get or sub_get_state > 0 then
			return 0
		end
	end

	return 1
end
----------------------------------------remind_end--------------------------------------------

function PremiumGiftWGData:GetMainTaskState(seq)
	if self:GetMainStatusFlag(seq) then
		return 0
	end

	return 1
end

function PremiumGiftWGData:GetSubTaskRewardState(day)
	if self:GetSubRewardFlag(day) then
		return 0
	end
	
	return 1
end

function PremiumGiftWGData:GetSubTaskList(day)
	local sub_task_list = self:GetSubTaskCfg(day)
	local data_list = {}

	if IsEmptyTable(sub_task_list) then
		return data_list
	end

	for i, v in ipairs(sub_task_list) do
		local task_is_get = self:GetSubAllFlagBuDayAndSeq(day, v.seq) > 0

		local data = {
			seq = v.seq,
			type = v.type,
			param1 = v.param1,
			param2 = v.param2,
			show_open_decs = v.show_open_decs,
			open_panel = v.open_panel,
			act_type = v.act_type,
			show_icon_ground = v.show_icon_ground,
			is_get = task_is_get,
		}

		table.insert(data_list, data)
	end

	return data_list
end

function PremiumGiftWGData:GetBigRewardCfg()
	local grade = self:GetExtinctGrade()
	local item_id = 0
	local img_type = 1

	for k, v in pairs(self.open_day_cfg) do
		if v.grade == grade then
			item_id = v.maintask_item_list[0].item_id
			img_type = v.show_pic
		end
	end
	
	return item_id, img_type
end

function PremiumGiftWGData:GetJumpRedTab()
	local index = 0

	if not self:NotHasSubTask() then
		local day_list = self:GetSubTaskActDayList()
		local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.PREMIUM_GIFT)
		for tab_index, day in pairs(day_list) do
			if day <= act_day then
				local sub_red = self:GetSubTaskRed(day)
				if sub_red > 0 then
					return tab_index
				end
			end
		end
	end

	return index
end

function PremiumGiftWGData:GetMainTaskCfg()
	local grade = self:GetExtinctGrade()
	return self.main_task_cfg[grade] or {}
end

function PremiumGiftWGData:GetSubTaskActDayList()
	local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.PREMIUM_GIFT)
	local day_list = {}
	day_list[1] = act_day

	local act_day_list = {1, 2, 3, 4}
	if act_day_list[act_day] then
		table.remove(act_day_list, act_day)
	end

	for k, v in pairs(act_day_list) do
		table.insert(day_list, v)
	end

	return day_list
end

function PremiumGiftWGData:GetSubTaskActTabDay(index)
	local day_list = self:GetSubTaskActDayList()
	return day_list[index] or 0
end

function PremiumGiftWGData:GetMainTaskList()
	local data_list = {}
	local main_cfg = self:GetMainTaskCfg()

	if main_cfg == nil then
		return data_list
	end

	for i, v in ipairs(main_cfg) do
		local is_get = self:GetMainTaskState(v.seq)

		local data = {
			is_get = is_get,
			seq = v.seq,
			type = v.type,
			param1 = v.param1,
			param2 = v.param2,
			show_icon_ground = v.show_icon_ground,
			show_open_decs = v.show_open_decs,
			open_panel = v.open_panel,
			act_type = v.act_type,
			show_icon = v.show_icon
		}

		table.insert(data_list, data)
	end

	return data_list
end

function PremiumGiftWGData:GetMainFlowerList()
	local data_list = {}
	local main_cfg = self:GetMainTaskCfg()

	if main_cfg == nil then
		return data_list
	end

	for i, v in ipairs(main_cfg) do
		local data = {
			is_get = self:GetMainTaskState(v.seq)
		}
		table.insert(data_list, data)
	end

	table.sort(data_list, SortTools.KeyUpperSorter("is_get"))
	return data_list
end

function PremiumGiftWGData:GetSubTaskActDayTabShow()
	local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.PREMIUM_GIFT)
	local show_tab = {}
	local no_sub_task = self:NotHasSubTask()

	for day = 1, 4 do
		  show_tab[day] = (act_day >= day and not no_sub_task) and 1 or 0
	end

	return show_tab
end

function PremiumGiftWGData:GetBigRewardSkillCfg(data)
	local data_list = {}
	if IsEmptyTable(data) then
        return data_list
    end

	local act_cfg = NewAppearanceWGData.Instance:GetMountActCfgByItemId(data.item_id)
	if not act_cfg then
        return data_list
    end

    local skill_list = NewAppearanceWGData.Instance:GetSpecialMountSkillList(act_cfg.image_id)
	if skill_list[0] then
		data_list = skill_list[0]
	end

	return data_list
end

function PremiumGiftWGData:GetSubTaskDayByIndex(_day)
	local day_list = self:GetSubTaskActDayList()

	for index, day in pairs(day_list) do
		if _day == day then
			return index
		end
	end

	return 0
end

function PremiumGiftWGData:NotHasSubTask()
	return IsEmptyTable(self.sub_task_cfg) or IsEmptyTable(self.sub_task_cfg) or not self:IsShowSubTask()
end

--判断是否为筛选显示的属性.
function PremiumGiftWGData:CheckIsSelectTitleAttr(attr_name)
	local attr_name = AttributeMgr.GetAttributteKey(attr_name)

	for key, value in pairs(self.select_title_attr_lits) do
		local temp_name = AttributeMgr.GetAttributteKey(value)
		if attr_name == temp_name then
			return true
		end
	end

	return false
end