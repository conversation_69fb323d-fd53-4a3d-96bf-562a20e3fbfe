MainUIFunctionBoxView = MainUIFunctionBoxView or BaseClass(SafeBaseView)

function MainUIFunctionBoxView:__init()
	self.view_layer = UiLayer.Pop
    self.view_name = "MainUIFunctionBoxView"
    self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
	self:SetMaskBg(true, true)
	self.mask_alpha = 0
    self.open_tween = nil
    self.close_tween = nil
	self:AddViewResource(0, "uis/view/main_ui_prefab", "layout_func_box_view")
end

function MainUIFunctionBoxView:LoadCallBack()
	if not self.func_list then
		self.func_list = AsyncListView.New(FuncBoxActItemCellRender, self.node_list.func_list)
		self.func_list:SetDefaultSelectIndex(nil)
		self.func_list:SetStartZeroIndex(false)
		self.func_list:SetSelectCallBack(BindTool.Bind(self.OnClickOpen, self))
	end

    if not self.view_open_event then
        self.view_open_event = GlobalEventSystem:Bind(OtherEventType.VIEW_OPEN, BindTool.Bind(self.ViewOpenHandler, self))
    end
end

function MainUIFunctionBoxView:ReleaseCallBack()
	if self.func_list then
		self.func_list:DeleteMe()
		self.func_list = nil
	end

    if self.view_open_event then
		GlobalEventSystem:UnBind(self.view_open_event)
		self.view_open_event = nil
	end

    if self.tween_open then
        self.tween_open:Kill()
        self.tween_open = nil
    end

    if self.tween_close then
        self.tween_close:Kill()
        self.tween_close = nil
    end
end

function MainUIFunctionBoxView:ShowIndexCallBack()
    local MOVE_TIME = 0.3
	local SCALE = 0.9

	local sequence = DG.Tweening.DOTween.Sequence()
    self.node_list.tween_root.canvas_group.alpha = 0
    
    if self.tween_open then
        self.tween_open:Kill()
        self.tween_open = nil
    end

    self.node_list.tween_root.transform.anchoredPosition = Vector3(600, 60, 0)
    sequence:Join(self.node_list.tween_root.transform:DOAnchorPosX(0, MOVE_TIME))
    sequence:Join(self.node_list.tween_root.canvas_group:DoAlpha(0, 1, MOVE_TIME))

    self.tween_open = sequence
end

function MainUIFunctionBoxView:Close()
    local MOVE_TIME = 0.3
	local SCALE = 0.9

    if self.tween_close then
        self.tween_close:Kill()
        self.tween_close = nil
    end

    if self.node_list and self.node_list.tween_root then
        local sequence = DG.Tweening.DOTween.Sequence()
        self.node_list.tween_root.canvas_group.alpha = 1
        self.node_list.tween_root.transform.anchoredPosition = Vector3(0, 60, 0)
        sequence:Join(self.node_list.tween_root.transform:DOAnchorPosX(600, MOVE_TIME))
        sequence:Join(self.node_list.tween_root.canvas_group:DoAlpha(1, 0, MOVE_TIME))
        sequence:OnComplete(function ()
            SafeBaseView.Close(self)
        end)
        
        self.tween_close = sequence
    else
        SafeBaseView.Close(self)
    end
end

function MainUIFunctionBoxView:OnFlush()
	local mainui_func_box_cache = MainuiWGData.Instance:GetFuncBoxDataCache()
	self.func_list:SetDataList(mainui_func_box_cache)
end

function MainUIFunctionBoxView:OnClickOpen()
	
end

function MainUIFunctionBoxView:ViewOpenHandler(view)
    if view and view.view_name and view.view_name ~= "MainUIFunctionBoxView" then
        self:Close()
    end
end

----------------------------------FuncBoxActItemCellRender-----------------------------------
FuncBoxActItemCellRender = FuncBoxActItemCellRender or BaseClass(BaseRender)

function FuncBoxActItemCellRender:__delete()
    if self.act_cell then
        self.act_cell:DeleteMe()
        self.act_cell = nil
    end
end

function FuncBoxActItemCellRender:OnFlush()
	if self.data == nil then
        return
    end

	local act_type = self.data.act_type
    local act_info = ActivityWGData.Instance:GetActivityStatuByType(act_type)
    local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)
    if act_info == nil or act_cfg == nil then
        return
    end

    if self.act_cell == nil then
        self.act_cell = MainActivityBtn.New()
        self.act_cell:CreateBtnAsset(self.node_list.act_btn_root, act_type)
        self.act_cell:AddClickEventListener(BindTool.Bind(self.OnClickActIcon, self, self.act_cell))
    end

    self.act_cell:SetData(act_type)
    self.act_cell:Flush("SetSprite", {act_cfg.res_name, act_cfg.act_name_res, act_cfg.bg_res})
    self.act_cell:SetActCfg(act_cfg)
    self.act_cell:Flush("CheckSpecialEffectBtn", {act_type})
    self.act_cell:ShowActiveButton()

    local end_time = act_info.next_time
    local status =  act_info.status
    if status == ACTIVITY_STATUS.STANDY then
        local time_tab = os.date("*t", end_time)
        if time_tab.min % 10 == 9 then
            time_tab.min = time_tab.min + 1
            if time_tab.min >= 60 then
                time_tab.min = 0
                time_tab.hour = time_tab.hour + 1
            end
        elseif time_tab.min % 10 == 1 then
            time_tab.min = time_tab.min - 1
        end

        local open_time_str = string.format("%d:%02d%s", time_tab.hour, time_tab.min, Language.Activity.KaiQi)
        self.act_cell:Flush("SetTime", {open_time_str})
        self.act_cell:Flush("SetActivityStateFlag", {act_type, status})
    elseif status == ACTIVITY_STATUS.OPEN or status == ACTIVITY_STATUS.XUNYOU then
        if end_time and act_cfg.show_time == 1 then --显示倒计时
            self.act_cell:Flush("SetEndTime", {end_time})
            self.act_cell:Flush("SetActivityStateFlag", {act_type, status})
        else
            self.act_cell:Flush("SetBottomContent", {""})
            self.act_cell:Flush("SetActivityStateFlag", {act_type, ACTIVITY_STATUS.CLOSE})
            self.act_cell:RemoveCountDown()
        end
    end

    if status == ACTIVITY_STATUS.OPEN or status == ACTIVITY_STATUS.XUNYOU then
        self.act_cell:Flush("ShowAni", {act_cfg.is_show_ani == 1})
        self.act_cell:Flush("SetFoldEffect",{true})
    else
        self.act_cell:Flush("SetFoldEffect",{false})
    end

    if ActRemindList[act_type] then
        local remind_num = RemindManager.Instance:GetRemind(ActRemindList[act_type])
        self.act_cell:Flush("SetRedPoint", {remind_num > 0})
    end
end

function FuncBoxActItemCellRender:OnClickActIcon()
    if self.data == nil then
        return
    end

    MainuiWGCtrl.Instance:ClickEnterActivity(self.data.act_type)
    MainuiWGCtrl.Instance:CloseMainUIFuncBoxView()
end