JingJieWGData = JingJieWGData or BaseClass()

function JingJieWGData:__init()
	if JingJieWGData.Instance then
		error("[JingJieWGData] Attempt to create singleton twice!")
		return
	end
	JingJieWGData.Instance = self
	-- 境界消耗材料
	self.jingjie_stuff_map = nil
	-- 穴位配置表(穴位, 索引id, 经脉， 穴位)
	self.jingmai_acupoint_map_cfg = nil
	-- 经脉激活属性表
	self.meridians_attr_cfg = nil
	-- 通脉成功率，vip提升表
	self.vip_up_cfg = nil
	---当前的经脉信息
	self.curr_meridians_info = nil
	--其他配置.
	self.other_cfg = nil
	--突破配置.
	self.break_up_cfg = nil

	-- 配置初始化
	self:InitCfg()

	-- 协议数据初始化
	self.jingjie_level = 0

	-- 红点注册
	RemindManager.Instance:Register(RemindName.Jing<PERSON><PERSON>, BindTool.Bind(self.GetJingJieRemind, self))
	RemindManager.Instance:Register(RemindName.Jing<PERSON>ai, BindTool.Bind(self.GetJingMaiRemind, self))

	-- 红点回调绑定
	self.jingjie_remind_callback = BindTool.Bind(self.JingJieRemindCallBack, self)
	RemindManager.Instance:Bind(self.jingjie_remind_callback, RemindName.JingJie)

	-- 物品更变提醒注册
	self:RegisterJingJieRemindInBag(RemindName.JingJie)
end

-- 初始化配置表
function JingJieWGData:InitCfg()
	self.jingjie_stuff_map = {}
	for k, v in pairs(self:GetJingJieCfg()) do
		if v.stuff_id > 0 then
			self.jingjie_stuff_map[v.stuff_id] = true
		end
	end

	local cfg = ConfigManager.Instance:GetAutoConfig("meridians_auto")
	if cfg then
		-- 穴位配置表(穴位, 索引id, 经脉， 穴位)
		self.jingmai_acupoint_map_cfg = ListToMap(cfg.acupoint, "whole_body_id", "meridians_id", "acupoint_id")
		-- 经脉激活属性表
		self.meridians_attr_cfg = cfg.meridians_attr
		-- 通脉成功率，vip提升表
		self.vip_up_cfg = ListToMap(cfg.vip_up, "vip_level")
		--其他配置.
		self.other_cfg = cfg.other[1]
		--突破配置.
		self.break_up_cfg = ListToMap(cfg.break_up, "whole_body_id", "meridians_id")
		self.break_item_cfg = ListToMap(cfg.break_up, "consume_item")
	end
end

-- 物品更变提醒注册
function JingJieWGData:RegisterJingJieRemindInBag(remind_name)
	local item_id_list = {}
    for k,v in pairs(self.jingjie_stuff_map) do
        table.insert(item_id_list, k)
    end
	BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end


function JingJieWGData:__delete()
	self.jingjie_stuff_map = nil
	self.jingmai_acupoint_map_cfg = nil
	self.meridians_attr_cfg = nil
	self.vip_up_cfg = nil
	self.curr_meridians_info = nil

	JingJieWGData.Instance = nil

	RemindManager.Instance:UnRegister(RemindName.JingJie)
	RemindManager.Instance:UnRegister(RemindName.JingMai)

	RemindManager.Instance:UnBind(self.jingjie_remind_callback)
end

-- 设置境界协议信息
function JingJieWGData:SetJingJieInfo(protocol)
	self.jingjie_level = protocol.jingjie_level
end

-- 获取境界等级
function JingJieWGData:GetJingJieLevel()
	return self.jingjie_level
end

-- 获取境界配置
function JingJieWGData:GetJingJieCfg()
	return ConfigManager.Instance:GetAutoConfig("rolejingjie_auto").jingjie
end

-- 获取最大境界等级
function JingJieWGData:GetJingJieMaxLevel()
	local cfg = self:GetJingJieCfg()
	return cfg[#cfg].jingjie_level
end

function JingJieWGData:GetJingJieMaxLevelCfg()
	local cfg = self:GetJingJieCfg()
	return cfg[#cfg]
end

-- 获取单个境界配置
function JingJieWGData:GetJingJieCfgBylevel(jingjie_level)
	jingjie_level = jingjie_level or self:GetJingJieLevel()
	return self:GetJingJieCfg()[jingjie_level]
end

local attr_name_t = {"shengming_max", "gongji", "fangyu", "pojia", "shanghai_jc_per"}
function JingJieWGData:GetJingJieAttrListCfgBylevel(jingjie_level, is_show_up)
	jingjie_level = jingjie_level or self:GetJingJieLevel()
	local cfg = self:GetJingJieCfg()[jingjie_level]
	local attr_t = {}
	local attr_t_cap = {}
	for k,v in pairs(attr_name_t) do
		if cfg[v] and cfg[v] > 0 then
			attr_t[k] = {}
			attr_t[k].attr_name = v
			attr_t[k].attr_value = cfg[v]
			attr_t[k].is_show_up = is_show_up

			attr_t_cap[v] = cfg[v]
		end
	end
	return attr_t, attr_t_cap
end

function JingJieWGData:GetJingJieAttrAddListCfgBylevel(jingjie_level)
	jingjie_level = jingjie_level or self:GetJingJieLevel()
	local last_level = jingjie_level - 1
	last_level = last_level > 0 and last_level or 0

	local cfg = self:GetJingJieCfgBylevel(jingjie_level)
	local last_cfg = self:GetJingJieCfgBylevel(last_level)
	local attr_t = {}

	for k,v in pairs(attr_name_t) do
		if cfg[v] and cfg[v] > 0 then
			attr_t[k] = {}
			attr_t[k].attr_name = v
			local last_value = last_cfg[v] or 0
			attr_t[k].attr_value = cfg[v]
			attr_t[k].last_value = last_value
			attr_t[k].add_value = cfg[v] - last_value
		end
	end

	return attr_t
end

-- 根据等级计算战斗力
function JingJieWGData:GetCapabilityByID(jingjie_level)
	local jinjie_cfg = self:GetJingJieCfg()
	local temp_cfg = (jinjie_cfg or {})[jingjie_level] or {}

	if not IsEmptyTable(temp_cfg) then
		local temp_attr2 = AttributeMgr.GetAttributteByClass(temp_cfg)
		return AttributeMgr.GetCapability(temp_attr2)
	end

	return 0
end

-- 获取境界功能开启等级
function JingJieWGData:GetJingJieOpenCondition()
	local other = ConfigManager.Instance:GetAutoConfig("rolejingjie_auto").other
	return other and other[1].open_jingjie_level
end

-- 判断道具是否是境界升级消耗品
function JingJieWGData:IsJingJieStuff(item_id)
	return self.jingjie_stuff_map[item_id]
end

-- 境界红点
function JingJieWGData:GetJingJieRemind()
	local cfg = self:GetJingJieCfgBylevel(self:GetJingJieLevel() + 1)
	if not cfg then
		return 0
	end
	
	local need_cap = cfg.cap_limit
	local need_stuff = cfg.stuff_id
	local jingjie_red = false

	-- 判断境界红点
	if need_stuff > 0 then
		local has_amount = ItemWGData.Instance:GetItemNumInBagById(need_stuff)
		if has_amount >= cfg.stuff_num and RoleWGData.Instance:GetOriginCapability() >= need_cap then
			jingjie_red = true
		end
	end

	if jingjie_red then
		return 1
	end

	return 0
end

-- 经脉红点
function JingJieWGData:GetJingMaiRemind()
	-- 判断经脉红点
	local jingmai_red = self:GetMeridiansRemind()
	if jingmai_red then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ROLE_JINMAI, 1, function ()
			ViewManager.Instance:Open(GuideModuleName.RoleView, TabIndex.jingmai)
		end)
		return 1
	end

	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ROLE_JINMAI, 0)
	return 0
end

-- 境界红点更变回调
function JingJieWGData:JingJieRemindCallBack(remind_name, num)
	if num > 0 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ROLE_JINJIE, 1, function ()
			ViewManager.Instance:Open(GuideModuleName.RoleView)
			return true
		end)
	else
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ROLE_JINJIE, 0)
	end
end

--获取当前境界升级所需材料
-- JingJieWGData.Instance:GetCurJingJieStuff()
function JingJieWGData:GetCurJingJieStuff()
	local cfg = self:GetJingJieCfgBylevel(self:GetJingJieLevel() + 1)
	if not cfg then
		return 99999--满级了
	end
	return cfg.stuff_id or 0
end

-- 获取一个穴位信息
function JingJieWGData:GetAcupointCfgById(whole_body_id, meridians_id, acupoint_id)
    local aim_cfg = {}
    return (((self.jingmai_acupoint_map_cfg or aim_cfg)[whole_body_id] or aim_cfg)[meridians_id] or aim_cfg)[acupoint_id] or nil
end

-- 获取一个所有穴位的长度信息
function JingJieWGData:GetAcupointCfgCountById(whole_body_id, meridians_id)
    local aim_cfg = {}
    local list = ((self.jingmai_acupoint_map_cfg or aim_cfg)[whole_body_id] or aim_cfg)[meridians_id] or nil

	if list ~= nil then
		return #list
	end

	return 0 
end

-- 获取一个所有通体的长度信息
function JingJieWGData:GetWholeCfgCount()
	if not self.jingmai_acupoint_map_cfg then
		return 0
	end

	return #self.jingmai_acupoint_map_cfg
end

-- 获取一个通体穴位的长度信息
function JingJieWGData:GetMeridiansCfgCountById(whole_body_id)
    local aim_cfg = {}
    local list = (self.jingmai_acupoint_map_cfg or aim_cfg)[whole_body_id] or nil

	if list ~= nil then
		return #list
	end

	return 0 
end


-- 获取一个穴位属性根据id
function JingJieWGData:GetMeridiansAttrById(whole_body_id, meridians_id)
	if not self.meridians_attr_cfg then
		return nil
	end

	for _, attr_data in pairs(self.meridians_attr_cfg) do
		if attr_data.whole_body_min <= whole_body_id and attr_data.whole_body_max >= whole_body_id and attr_data.meridians_index == meridians_id then
			return attr_data
		end
	end

	return nil
end

-- 获取Vip的百分比
function JingJieWGData:GetSucPerByVipForCfg(cur_vip_level)
	local per = 0
	if not self.vip_up_cfg then
		return per
	end

	for _, vip_data in pairs(self.vip_up_cfg) do
		if cur_vip_level >= vip_data.vip_level then
			if per < vip_data.vip_per then
				per = vip_data.vip_per
			end
		end
	end
	
	return per
end

-- 同步服务器信息
function JingJieWGData:SyncMeridiansInfo(server_data)
	if self.curr_meridians_info == nil then
		self.curr_meridians_info = {}
	end

	self.curr_meridians_info.whole_body_id = server_data and server_data.whole_body_id or 1	--通体ID.
	self.curr_meridians_info.meridians_id = server_data and server_data.meridians_id or 1	--经脉ID.
	self.curr_meridians_info.acupoint_id = server_data and server_data.acupoint_id or 1		--穴位ID.
	self.curr_meridians_info.longhun_exp = server_data and server_data.longhun_exp or 0		--龙魂经验值.
	self.curr_meridians_info.break_state = server_data and server_data.break_state or 0		--突破状态.

	self.curr_meridians_info.condensate_per = server_data and server_data.condensate_per or 0	--废弃等删.
	self.curr_meridians_info.skill_state = server_data and server_data.skill_state or 0			--废弃等删.

	local whole_count = self:GetWholeCfgCount()
	self.curr_meridians_info.is_max = self.curr_meridians_info.whole_body_id > whole_count

	if self.curr_meridians_info.is_max then	---最大化取最大值
		self.curr_meridians_info.whole_body_id = whole_count
		self.curr_meridians_info.meridians_id = self:GetMeridiansCfgCountById(whole_count)
		self.curr_meridians_info.acupoint_id = self:GetAcupointCfgCountById(whole_count, self.curr_meridians_info.meridians_id)
	end
end

-- 同步服务器信息
function JingJieWGData:SyncCondensateInfo(server_data)
	if self.curr_meridians_info == nil then
		return
	end
	self.curr_meridians_info.condensate_per = server_data and server_data.condensate_per or 0
end

function JingJieWGData:GetCurrMeridiansList()
	local aim_table = {}
	for i = 1, #Language.JingMai.MeridiansGrop1 do
		aim_table[i] = {}
		aim_table[i].index = i
		aim_table[i].normal_name = Language.JingMai.MeridiansGrop1[i]
		aim_table[i].select_name = Language.JingMai.MeridiansGrop2[i]
	end
	return aim_table
end

function JingJieWGData:GetAcupointList(whole_body_id, meridians_id)
	local acupoint_list = {}
	if self.jingmai_acupoint_map_cfg[whole_body_id] and self.jingmai_acupoint_map_cfg[whole_body_id][meridians_id] then
		local list = self.jingmai_acupoint_map_cfg[whole_body_id][meridians_id]
		for _, acupoint_data in ipairs(list) do
			local temp_data = {}
			temp_data.is_lock = true
			temp_data.is_select = false
			if self.curr_meridians_info and acupoint_data then
				local whole_body_unlock = self.curr_meridians_info.whole_body_id == whole_body_id
				local meridians_unlock = self.curr_meridians_info.meridians_id == meridians_id
				if meridians_id < self.curr_meridians_info.meridians_id then
					meridians_unlock = true
					temp_data.is_lock = not (meridians_unlock and whole_body_unlock)
				else
					local acupoint_unlock = acupoint_data.acupoint_id <= self.curr_meridians_info.acupoint_id
					local un_lock = whole_body_unlock and meridians_unlock and acupoint_unlock
					temp_data.is_lock = not un_lock
					temp_data.is_select = whole_body_unlock and meridians_unlock and acupoint_data.acupoint_id == self.curr_meridians_info.acupoint_id + 1
				end
			end
			table.insert(acupoint_list, temp_data)
		end
	end
	return acupoint_list
end

function JingJieWGData:GetMeridiansAttr(whole_body_id, select_meridians_id, is_up_meridians)
	local list = self.jingmai_acupoint_map_cfg[whole_body_id]
	local operate_attr_data = {}
	local attr_data_list = {}

	if (not list) or (not self.curr_meridians_info) then
		return nil
	end

	local is_cur = select_meridians_id == self.curr_meridians_info.meridians_id			-- 当前（拥有箭头）

	--通脉.
	for meridians_id, meridians_list in pairs(list) do
		local is_get = meridians_id == select_meridians_id
		if is_get and is_cur then
			operate_attr_data = self:SetMeridiansAttrList(operate_attr_data, meridians_list, false, false)
			break
		end
	end

	--突破.
	local up_data = (self.break_up_cfg or nil)[whole_body_id]
	local max_up_data = nil

	if self.curr_meridians_info.meridians_id == #up_data then
		local next_break_up_list = (self.break_up_cfg or nil)[whole_body_id + 1]
		max_up_data = next_break_up_list and next_break_up_list[1]
	end

	if up_data then
		if is_up_meridians then
			self:SetMeridiansAttrList(operate_attr_data, up_data, false, false, true, true, max_up_data)
		else
			self:SetMeridiansAttrList(operate_attr_data, up_data, false, false, true, false, max_up_data)
		end
	end

	for _, attr_data in pairs(operate_attr_data) do
		if attr_data and attr_data.attr_str and attr_data.attr_str ~= 0 then
			table.insert(attr_data_list, attr_data)
		end
	end

	table.sort(attr_data_list, function (a, b)
		return a.attr_str < b.attr_str
	end)

	return attr_data_list
end

function JingJieWGData:GetAllMeridiansAttr()
	if not self.jingmai_acupoint_map_cfg then
		return nil
	end

	local attr_data_list = {}
	local operate_attr_data = {}

	for whole_body_id, list in pairs(self.jingmai_acupoint_map_cfg) do

		if (not list) or (not self.curr_meridians_info) then
			return nil
		end
	
		for meridians_id, meridians_list in pairs(list) do
			local is_get = false

			if whole_body_id < self.curr_meridians_info.whole_body_id then
				is_get = true
			elseif whole_body_id == self.curr_meridians_info.whole_body_id then
				is_get = meridians_id <= self.curr_meridians_info.meridians_id
			end

			if is_get then
				if whole_body_id < self.curr_meridians_info.whole_body_id then
					-- 整合下技能
					operate_attr_data = self:SetMeridiansSkillAttrData(operate_attr_data, whole_body_id, meridians_id)
				elseif whole_body_id == self.curr_meridians_info.whole_body_id then
					if meridians_id < self.curr_meridians_info.meridians_id then
						-- 整合下技能
						operate_attr_data = self:SetMeridiansSkillAttrData(operate_attr_data, whole_body_id, meridians_id)
					else
						operate_attr_data = self:SetMeridiansAttrList(operate_attr_data, meridians_list, false, false)
						--突破.
						local up_data = (self.break_up_cfg or nil)[whole_body_id]
						if up_data then
							self:SetMeridiansAttrList(operate_attr_data, up_data, false, false, true, true)
						end
					end
				end
			end
		end
	end

	for _, attr_data in pairs(operate_attr_data) do
		if attr_data and attr_data.attr_str and attr_data.attr_str ~= 0 and attr_data.attr_value and attr_data.attr_value ~= 0 then
			table.insert(attr_data_list, attr_data)
		end
	end

	table.sort(attr_data_list, function (a, b)
		return a.attr_str < b.attr_str
	end)

	return attr_data_list
end

function JingJieWGData:SetMeridiansAttrList(attr_data, aim_list, is_zero, is_unlock, is_up_meridians, is_need_add_up_meridians, max_break_data)
	if (not aim_list) or (not self.curr_meridians_info) then
		return attr_data
	end

	--加成差值.
	function SubAddAttr(cur_data)
		if cur_data then
			for i = 1, 6 do
				local key = cur_data["attr_id" .. i]
				if not key then
					break
				end
				if attr_data[key] then
					local per = 1
					local break_up_data = self:GetBreakUpCfgById(self.curr_meridians_info.whole_body_id, self.curr_meridians_info.meridians_id)
					if not IsEmptyTable(break_up_data) and break_up_data.sys_attr_per then
						per = (break_up_data.sys_attr_per / 10000) + 1
					end

					attr_data[key].add_value = attr_data[key].add_value - math.floor(per * cur_data["attr_value" .. i])
				end
			end

			if is_up_meridians and is_need_add_up_meridians and cur_data["sys_attr_per"] and attr_data[RoleView.LongMaiJiaChengAttr] then
				attr_data[RoleView.LongMaiJiaChengAttr].add_value =
					attr_data[RoleView.LongMaiJiaChengAttr].add_value - cur_data["sys_attr_per"]
			end
		end
	end

	local cur_data = nil
	if is_up_meridians then
		for _, meridians_data in pairs(aim_list) do
			if meridians_data.meridians_id == self.curr_meridians_info.meridians_id then
				cur_data = meridians_data
				attr_data = self:SetMeridiansAttrData(attr_data, meridians_data, is_zero, is_need_add_up_meridians)
			elseif is_need_add_up_meridians and meridians_data.meridians_id == self.curr_meridians_info.meridians_id + 1 then
				attr_data = self:SetMeridiansAttrAddData(attr_data, meridians_data, is_need_add_up_meridians)
				--突破的加成差值.
				SubAddAttr(cur_data)
			elseif max_break_data ~= nil then
				attr_data = self:SetMeridiansAttrAddData(attr_data, max_break_data, is_need_add_up_meridians)
				--突破的加成差值.
				SubAddAttr(cur_data)
			end
		end
	else
		for _, acupoint_data in pairs(aim_list) do
			if acupoint_data.acupoint_id == self.curr_meridians_info.acupoint_id then
				cur_data = acupoint_data
				attr_data = self:SetMeridiansAttrData(attr_data, acupoint_data, is_zero)
			elseif acupoint_data.acupoint_id == self.curr_meridians_info.acupoint_id + 1 then
				attr_data = self:SetMeridiansAttrAddData(attr_data, acupoint_data)
				--通脉的加成差值.
				SubAddAttr(cur_data)
			end
		end
	end

	return attr_data
end

function JingJieWGData:SetMeridiansAttrData(cur_table, aim_data, is_zero, is_need_add_up_meridians)
	local per = 1
	local break_up_data = self:GetBreakUpCfgById(self.curr_meridians_info.whole_body_id, self.curr_meridians_info.meridians_id)
	if not IsEmptyTable(break_up_data) and break_up_data.sys_attr_per then
		per = (break_up_data.sys_attr_per / 10000) + 1
	end

	for i = 1, 6 do
		local key = aim_data["attr_id" .. i]
		if not key then
			break
		end

		if not cur_table[key] and key ~= 0 then
			cur_table[key] = {}
			cur_table[key].attr_str = aim_data["attr_id" .. i]
			local real_value = is_zero and 0 or math.floor(per * aim_data["attr_value" .. i])
			cur_table[key].attr_value = real_value
		else
			if cur_table[key] then
				if is_zero then
					cur_table[key].attr_value = 0
				else
					cur_table[key].attr_value = cur_table[key].attr_value + math.floor(per * aim_data["attr_value" .. i])
				end
			end
		end
	end

	if is_need_add_up_meridians and aim_data["sys_attr_per"] then
		cur_table[RoleView.LongMaiJiaChengAttr] = {}
		cur_table[RoleView.LongMaiJiaChengAttr].attr_str = RoleView.LongMaiJiaChengAttr
		cur_table[RoleView.LongMaiJiaChengAttr].attr_value = aim_data["sys_attr_per"]
	end

	return cur_table
end

function JingJieWGData:SetMeridiansAttrAddData(cur_table, aim_data, is_need_add_up_meridians)
	local per = 1
	local break_up_data = self:GetBreakUpCfgById(self.curr_meridians_info.whole_body_id, self.curr_meridians_info.meridians_id)
	if not IsEmptyTable(break_up_data) and break_up_data.sys_attr_per then
		per = (break_up_data.sys_attr_per / 10000) + 1
	end

	for i = 1, 6 do
		local key = aim_data["attr_id" .. i]
		if not key then
			break
		end

		if cur_table[key] then
			cur_table[key].add_value = math.floor(per * aim_data["attr_value" .. i])
		else
			cur_table[key] = {}
			cur_table[key].attr_value = 0
			cur_table[key].attr_str = aim_data["attr_id" .. i]
			cur_table[key].add_value = math.floor(per * aim_data["attr_value" .. i])
		end
	end

	if is_need_add_up_meridians and aim_data["sys_attr_per"] and cur_table[RoleView.LongMaiJiaChengAttr] then
		cur_table[RoleView.LongMaiJiaChengAttr].add_value = aim_data["sys_attr_per"]
	end

	return cur_table
end

function JingJieWGData:SetMeridiansSkillAttrData(cur_table, whole_body_id, meridians_id)
	local meridians_attr_cfg = JingJieWGData.Instance:GetMeridiansAttrById(whole_body_id, meridians_id)
	local skill_attr_data = {}
	if meridians_attr_cfg then
		for i = 1, 2 do
			if not skill_attr_data[meridians_attr_cfg["attr_id" .. i]] then
				skill_attr_data[meridians_attr_cfg["attr_id" .. i]] = meridians_attr_cfg["attr_value" .. i]
			else
				skill_attr_data[meridians_attr_cfg["attr_id" .. i]] = skill_attr_data[meridians_attr_cfg["attr_id" .. i]] + meridians_attr_cfg["attr_value" .. i]
			end
		end
	end

	local check_have_same = function (attr_str, attr_value)
		for i, table_data in ipairs(cur_table) do
			if table_data.attr_str == attr_str then
				table_data.attr_value = table_data.attr_value + attr_value
				return true
			end
		end
		return false
	end

	for attr_str, attr_value in pairs(skill_attr_data) do
		local is_have_same = check_have_same(attr_str, attr_value)
		if not is_have_same then
			local index = #cur_table + 1
			cur_table[index] = {}
			cur_table[index].attr_value = attr_value
			cur_table[index].attr_str = attr_str
		end
	end

	return cur_table
end

-- 获取信息
function JingJieWGData:GetMeridiansInfo()
	return self.curr_meridians_info
end

-- 获取经脉红点状态
function JingJieWGData:GetMeridiansRemind()
    if not self.curr_meridians_info then
        return false
    end

	local is_full = false

    local acupoint_id = self.curr_meridians_info.acupoint_id + 1
    local cfg = JingJieWGData.Instance:GetAcupointCfgById(self.curr_meridians_info.whole_body_id, self.curr_meridians_info.meridians_id, acupoint_id)

    if cfg then
		-- 境界状态
		local curr_jingjie_level = JingJieWGData.Instance:GetJingJieLevel()
		local is_jingjie_enougth = curr_jingjie_level >= cfg.jingjie_level

        -- 消耗状态
		local coin = RoleWGData.Instance.role_info.coin or 0
        local is_spend_enougth = coin >= cfg.coin_num

        if cfg.yuanbao_num and cfg.yuanbao_num > 0 then
            local bind_gold = RoleWGData.Instance.role_info.bind_gold or 0
            is_spend_enougth = bind_gold >= cfg.yuanbao_num
        end

		-- is_spend_enougth = self.curr_meridians_info.longhun_exp >= cfg.longhun_exp

		is_full = is_jingjie_enougth and is_spend_enougth
	end

	if not is_full then
		is_full = self:GetMeridiansUpRemind()
	end

	return is_full
end

-- 获取经脉突破红点状态
function JingJieWGData:GetMeridiansUpRemind()
	local is_full = false
	local acupoint_id = self.curr_meridians_info.acupoint_id + 1
	local length = self:GetAcupointCfgCountById(self.curr_meridians_info.whole_body_id, self.curr_meridians_info.meridians_id)

	if acupoint_id > length and self.curr_meridians_info.break_state == 1 then
		local break_up_data = self:GetBreakUpCfgById(self.curr_meridians_info.whole_body_id, self.curr_meridians_info.meridians_id)
		if not IsEmptyTable(break_up_data) then
			local item_num = ItemWGData.Instance:GetItemNumInBagById(break_up_data.consume_item)
			is_full = item_num >= break_up_data.suc_consume_num
		end
	end

	return is_full
end

---获取一个背景的战力
function JingJieWGData:GetJingMaiAttrCap(meridians_attr_list)
	if not meridians_attr_list then
		return 0
	end

	local attribute = AttributePool.AllocAttribute()
	local function add_tab(attr_str, value)
		if attr_str == nil or value == nil then
			return
		end
		if attribute[attr_str] then
			attribute[attr_str] = attribute[attr_str] + value
		end
	end

    for index, attr_cell in ipairs(meridians_attr_list) do
		if attr_cell.attr_str == RoleView.LongMaiJiaChengAttr then
			break
		end

		if attr_cell.attr_str > 0 and attr_cell.attr_value > 0 then
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_cell.attr_str)
			add_tab(attr_str, attr_cell.attr_value)
		end
    end

	local cap = AttributeMgr.GetCapability(attribute)
	return cap
end

--判断穴位是否已满.
function JingJieWGData:CheckCurAcupointIsMax()
	if not self.curr_meridians_info then
		return false
	end

	local is_full = false
	local acupoint_id = self.curr_meridians_info.acupoint_id == 0 and 1 or self.curr_meridians_info.acupoint_id + 1
	local length = JingJieWGData.Instance:GetAcupointCfgCountById(self.curr_meridians_info.whole_body_id, self.curr_meridians_info.meridians_id)
	if acupoint_id > length then
		is_full = true
	end

	return is_full
end

function JingJieWGData:GetOtherCfg()
	return self.other_cfg
end

-- 获取突破信息.
function JingJieWGData:GetBreakUpCfgById(whole_body_id, meridians_id)
	local aim_cfg = {}
	return ((self.break_up_cfg or aim_cfg)[whole_body_id] or aim_cfg)[meridians_id] or aim_cfg
end

function JingJieWGData:IsJingMaiBreakItem(item_id)
	return self.break_item_cfg[item_id] ~= nil
end