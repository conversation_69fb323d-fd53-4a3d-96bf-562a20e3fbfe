MarketView = MarketView or BaseClass(SafeBaseView)

MarketViewIndex = {
	Shop =
	{
		shop_limit = TabIndex.shop_limit, --商城-每周限购
		shop_moyu = TabIndex.shop_moyu, --商城-绑定
		shop_daoju = TabIndex.shop_daoju, --商城-道具
		shop_fashion = TabIndex.shop_fashion, --商城-时装
		shop_hot = TabIndex.shop_hot,   --商城-元宝
		shop_honor = TabIndex.shop_honor, --商城-荣誉
		shop_shengwang = TabIndex.shop_shengwang, --商城-声望
		shop_chivalrous = TabIndex.shop_chivalrous, --商城-侠义值
	},

	Buy = TabIndex.market_buy,    -- 市场购买界面
	Sell = TabIndex.market_sell,  -- 市场出售界面
	Record = TabIndex.market_record, -- 市场交易记录

	--WantList = TabIndex.market_want_list, 					-- 市场求购列表
	--MyWant = TabIndex.market_my_want, 						-- 市场我的求购

	KFPM = TabIndex.market_cross_server, -- 市场-拍卖-跨国拍卖
	GJPM = TabIndex.market_country_auction, -- 市场-拍卖-国家拍卖
	XXPM = TabIndex.market_guild_auction, -- 市场-拍卖-仙盟拍卖
	WDJP = TabIndex.market_my_auction,   -- 市场-拍卖-我的竞拍
	PMJL = TabIndex.market_auction_record, -- 市场-拍卖-拍卖纪录
}

function MarketView:__init()
	self.view_style = ViewStyle.Full
	self.view_name = GuideModuleName.Market
	self.default_index = MarketViewIndex.Shop.shop_limit
	self.is_safe_area_adapter = true
	self.open_source_view = "BtnShopView"
	self:SetMaskBg()

	local assetbundle = "uis/view/market_ui_prefab"

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
	self:AddViewResource(0, assetbundle, "VerticalTabbar")
	self:AddViewResource(0, assetbundle, "HorizontalTabbar")
	self:AddViewResource(0, assetbundle, "market_jump_btn")

	self:AddViewResource(MarketViewIndex.Shop, "uis/view/shop_ui_prefab", "layout_shop")

	self:AddViewResource(MarketViewIndex.Buy, assetbundle, "layout_market_buy")
	self:AddViewResource(MarketViewIndex.Sell, assetbundle, "layout_market_sell")
	self:AddViewResource(MarketViewIndex.Record, assetbundle, "layout_market_record")

	--市场拍卖
	self:AddViewResource(MarketViewIndex.KFPM, assetbundle, "layout_market_cross_server")
	self:AddViewResource(MarketViewIndex.GJPM, assetbundle, "layout_market_country_auction")
	self:AddViewResource(MarketViewIndex.XXPM, assetbundle, "layout_market_guild_auction")
	self:AddViewResource(MarketViewIndex.WDJP, assetbundle, "layout_market_my_auction")
	self:AddViewResource(MarketViewIndex.PMJL, assetbundle, "layout_market_auction_log")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")

	self:MarketShopInit()
	
end

function MarketView:__delete()

end

function MarketView:OpenCallBack()
	MarketWGCtrl.Instance:SendReqWantInfo()
	MarketWGCtrl.Instance:SendReqAllGoodsAmountInfo() -- 请求所有商品的数目
	MarketWGCtrl.Instance:SendReqAllSelfGoodsInfo() -- 请求自己上架的商品

	self:MarketShopOpenCallBack()
	--请求所有正在拍卖的商品数据--
end

function MarketView:CloseCallBack()
	self:MarketShopCloseCallBack()
end

function MarketView:LoadCallBack()
	self:InitTabbar()

	local bundle, asset = ResPath.GetCommonButton("a3_ty_hretutn")
	self.node_list["btn_close_window_img"].image:LoadSprite(bundle, asset, function()
		self.node_list["btn_close_window_img"].image:SetNativeSize()
	end)
	self.node_list.title_view_name.text.color = Str2C3b('#FCF8DD')



	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
			show_shengwang_xinjing = true,
			show_chivalrous = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	self.sub_type_item_prefab = nil
	self.sub_type_item_prefab_loading = false
	self.sub_type_item_prefab_callback_list = {}

	self:MarketShopLoadCallBack()


	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.Market, self.get_guide_ui_event)
	--屏蔽特效.
	-- local game_obj_attach = self.node_list["RawImage_tongyong"].gameObject:GetOrAddComponent(typeof(Game.GameObjectAttach))
	-- if game_obj_attach then
	-- 	local bundle1, asset1 = ResPath.GetUIEffect("UI_jiaoyi_guang")
	-- 	game_obj_attach.BundleName = bundle1
	-- 	game_obj_attach.AssetName = asset1
	-- end
end

function MarketView:InitTabbar()
	if not self.tabbar then
		local remind_tab = {
			{},
			{},
			{ RemindName.Market_Cross_Server, RemindName.Market_Country_Auction, RemindName.Market_Guild_Auction },
		}
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:SetVerTabbarIconStr("market_view")

		local assetbundle = "uis/view/market_ui_prefab"
		self.tabbar:Init(Language.Market.VerTabGroup,
			{ Language.Market.TabGroup1, Language.Market.TabGroup2, Language.Market.TabGroup3 },
			assetbundle, assetbundle, remind_tab)

		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))

		local shop_type_cfg = ShopWGData.Instance:GetShopTypeCfg()
		if IsEmptyTable(shop_type_cfg) then
			return
		end
		for key, value in pairs(shop_type_cfg) do
			for k, v in pairs(MarketViewIndex.Shop) do
				if value.shop_type + 10 == v and value.is_show_in_shop <= 0 then
					self.tabbar:SetToggleVisible(v, false)
				end
			end
		end
	end
	FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.Market, self.tabbar)
end

function MarketView:ReleaseCallBack()
	self.sub_type_item_prefab = nil
	self.sub_type_item_prefab_loading = false
	self.sub_type_item_prefab_callback_list = {}

	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.Market, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end
	self:BuyReleaseCallBack()
	self:SellReleaseCallBack()
	self:RecordReleaseCallBack()
	self:MarketShopReleaseCallBack()
	--self:WantListReleaseCallBack()
	--self:MyWantReleaseCallBack()

	--拍卖注销相关
	self:CrossServerMarketReleaseCallBack()
	self:CountryAuctionReleaseCallBack()
	self:GuildAuctionReleaseCallBack()
	self:MyAuctionReleaseCallBack()
	self:AuctionRecordReleaseCallBack()
end

function MarketView:LoadIndexCallBack(index)
	if index == MarketViewIndex.Buy then     -- 市场购买界面
		self:BuyLoadCallBack()
	elseif index == MarketViewIndex.Sell then -- 市场出售界面
		self:SellLoadCallBack()
	elseif index == MarketViewIndex.Record then -- 市场交易记录
		self:RecordLoadCallBack()

	--elseif index == MarketViewIndex.WantList then 			-- 市场求购列表
		--	self:WantListLoadCallBack()

	--elseif index == MarketViewIndex.MyWant then 			-- 市场我的求购
		--	self:MyWantLoadCallBack()
	elseif index == MarketViewIndex.KFPM then -- 市场-拍卖-跨国拍卖
		self:CrossServerMarketLoadCallBack()
	elseif index == MarketViewIndex.GJPM then -- 市场-拍卖-国家拍卖
		self:CountryAuctionLoadCallBack()
	elseif index == MarketViewIndex.XXPM then -- 市场-拍卖-仙盟拍卖
		self:GuildAuctionLoadCallBack()
	elseif index == MarketViewIndex.WDJP then -- 市场-拍卖-我的竞拍
		self:MyAuctionLoadCallBack()
	elseif index == MarketViewIndex.PMJL then -- 市场-拍卖-拍卖纪录
		self:AuctionRecordLoadCallBack()
	else
		for key, value in pairs(MarketViewIndex.Shop) do
			if index == value then
				self:MarketShopLoadIndexCallBack()
			end
		end
	end
	
	XUI.AddClickEventListener(self.node_list["recharge_btn"], BindTool.Bind(self.OnRechargeBtnClick, self))
	XUI.AddClickEventListener(self.node_list["privilege_shop_btn"], BindTool.Bind(self.OnPrivilegeShopBtnClick, self))
	XUI.AddClickEventListener(self.node_list["vip_czjj_btn"], BindTool.Bind(self.OnVipCZJJBtnClick, self))
	XUI.AddClickEventListener(self.node_list["wealth_god_btn"], BindTool.Bind(self.OnWealthGodBtnClick, self))
end

function MarketView:ShowIndexCallBack(index)
	local is_show_shop_btn = false

	if index == MarketViewIndex.Buy then     -- 市场购买界面
		self:BuyShowIndexCallBack()
	elseif index == MarketViewIndex.Sell then -- 市场出售界面
		self:SellShowIndexCallBack()
	elseif index == MarketViewIndex.Record then -- 市场交易记录
		self:RecordShowIndexCallBack()
	--elseif index == MarketViewIndex.WantList then 			-- 市场求购列表
		--	self:WantListShowIndexCallBack()
	--elseif index == MarketViewIndex.MyWant then 			-- 市场我的求购
		--	self:MyWantShowIndexCallBack()
	elseif index == MarketViewIndex.KFPM then				-- 市场-拍卖-跨服竞拍
		self:CrossServerMarketShowIndexCallBack()
	elseif index == MarketViewIndex.GJPM then -- 市场-拍卖-国家拍卖
		self:CountryAuctionShowIndexCallBack()
	elseif index == MarketViewIndex.XXPM then -- 市场-拍卖-仙盟拍卖
		self:GuildAuctionShowIndexCallBack()
	elseif index == MarketViewIndex.WDJP then -- 市场-拍卖-我的竞拍
		self:MyAuctionShowIndexCallBack()
	else
		for key, value in pairs(MarketViewIndex.Shop) do
			if index == value then
				is_show_shop_btn = true
				self:MarketShopShowIndexCallBack(index)
				break
			end
		end
	end

	local bundle, asset = ResPath.GetRawImagesJPG("a3_jy_bg")
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)

	self.node_list.vip_czjj_btn:SetActive(is_show_shop_btn)
	local is_open_active = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GOD_OF_WEALTH)
	self.node_list.wealth_god_btn:SetActive(is_open_active and is_show_shop_btn)
	self.node_list.privilege_shop_btn:SetActive(is_show_shop_btn)
end

function MarketView:OnFlush(param_list, index)
	if index == MarketViewIndex.Buy then     -- 市场购买界面
		self:BuyOnFlush(param_list)
	elseif index == MarketViewIndex.Sell then -- 市场出售界面
		self:SellOnFlush(param_list)
	elseif index == MarketViewIndex.Record then -- 市场交易记录
		self:RecordOnFlush(param_list)
	--elseif index == MarketViewIndex.WantList then 			-- 市场求购列表
		--	self:WantListOnFlush(param_list)
	--elseif index == MarketViewIndex.MyWant then 			-- 市场我的求购
		--	self:MyWantOnFlush(param_list)
	elseif index == MarketViewIndex.KFPM then -- 市场-拍卖-跨服拍卖
		self:CrossServerMarketOnFlush(param_list)
	elseif index == MarketViewIndex.GJPM then -- 市场-拍卖-国家拍卖
		self:CountryAuctionOnFlush(param_list)
	elseif index == MarketViewIndex.XXPM then -- 市场-拍卖-仙盟拍卖
		self:GuildAuctionOnFlush(param_list)
	elseif index == MarketViewIndex.WDJP then -- 市场-拍卖-我的竞拍
		self:MyAuctionOnFlush(param_list)
	elseif index == MarketViewIndex.PMJL then -- 市场-拍卖-拍卖纪录
		self:AuctionRecordOnFlush(param_list)
	else
		for key, value in pairs(MarketViewIndex.Shop) do
			if index == value then
				self:MarketShopOnFlush(param_list, index)
			end
		end
	end
end

-- 加载子类型item预制体
function MarketView:LoadSubTypeItemPrefab(loaded_callback)
	-- 如果未加载
	if self.sub_type_item_prefab == nil and not self.sub_type_item_prefab_loading then
		table.insert(self.sub_type_item_prefab_callback_list, loaded_callback)
		self.sub_type_item_prefab_loading = true
		local res_async_loader = AllocResAsyncLoader(self, "MarketView:LoadSubTypeItemPrefab")
		res_async_loader:Load("uis/view/market_ui_prefab", "subtype_item_prefab", nil, function(new_obj)
			self.sub_type_item_prefab_loading = false
			self.sub_type_item_prefab = new_obj
			for i, v in ipairs(self.sub_type_item_prefab_callback_list) do
				v(self.sub_type_item_prefab)
			end
			self.sub_type_item_prefab_callback_list = {}
		end)
		-- 如果正在加载中
	elseif self.sub_type_item_prefab_loading then
		table.insert(self.sub_type_item_prefab_callback_list, loaded_callback)
		-- 如果已经加载完毕
	else
		loaded_callback(self.sub_type_item_prefab)
	end
end

function MarketView:GetGuideUiCallBack(ui_name, ui_param, try_times)
	return self:MarketShopGetGuideUiCallBack(ui_name, ui_param)
end

function MarketView:OnRechargeBtnClick()
	ShopWGData.Instance:SetRechargeRedPoint()
	RemindManager.Instance:Fire(RemindName.Shop)
	self:FlushRechargeRemind()
	FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge)
end

function MarketView:OnPrivilegeShopBtnClick()
	FunOpen.Instance:OpenViewByName(GuideModuleName.PrivilegeCollectionView)
	self:Close()
end

function MarketView:OnVipCZJJBtnClick()
	ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_tqtz)
	self:Close()
end

function MarketView:OnWealthGodBtnClick()
	ViewManager.Instance:Open(GuideModuleName.GodOfWealthView, TabIndex.god_of_wealth)
	self:Close()
end