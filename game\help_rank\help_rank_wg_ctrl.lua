require("game/help_rank/help_rank_view")
require("game/help_rank/help_rank_wg_data")

HelpRankWGCtrl = HelpRankWGCtrl or BaseClass(BaseWGCtrl)

function HelpRankWGCtrl:__init()
	if HelpRankWGCtrl.Instance then
		<PERSON>rror<PERSON><PERSON>("[HelpRankWGCtrl] attempt to create singleton twice!")
		return
	end

	HelpRankWGCtrl.Instance = self
	self.data = HelpRankWGData.New()
    self.view = HelpRankView.New(GuideModuleName.HelpRankView)
  
    self:RegisterAllProtocols()

	-- 活动监听
	self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)
end

function HelpRankWGCtrl:__delete()
	HelpRankWGCtrl.Instance = nil

	self.view:DeleteMe()
	self.view = nil

    self.data:DeleteMe()
	self.data = nil

	if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
		self.activity_change_callback = nil
	end
end

function HelpRankWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOAHelpRankInfo,"OnSCOAHelpRankInfo")
end

function HelpRankWGCtrl:ReqHelpRankInfo(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HELP_RANK
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function HelpRankWGCtrl:OnSCOAHelpRankInfo(protocol)
	--print_error("========冲榜========",protocol)
	self.data:SetAllInfo(protocol)
	RemindManager.Instance:Fire(RemindName.HelpRankRed)
	ViewManager.Instance:FlushView(GuideModuleName.HelpRankView)
	ViewManager.Instance:FlushView(GuideModuleName.KfActivityView)

	if self.view and not self.view:IsOpen() then
		self:ChangeHelpRankActStatus()
	end
end

function HelpRankWGCtrl:ChangeHelpRankActStatus()
	local info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HELP_RANK)
	local is_all_buy = self.data:GetCurShopIsAllBuy()
	
	if is_all_buy and info and info.status == ACTIVITY_STATUS.OPEN then
		self:SetHelpRankActiveStatus(true)
		ActivityWGData.Instance:SetOperationActivityActState(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HELP_RANK, 
			ACTIVITY_STATUS.CLOSE, info.next_time, info.start_time, info.end_time, info.open_type
		)
	elseif not is_all_buy and self.is_set_help_rank_activte_status and info and info.status == ACTIVITY_STATUS.CLOSE then
		self:SetHelpRankActiveStatus(false)
		ActivityWGData.Instance:SetOperationActivityActState(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HELP_RANK, 
			ACTIVITY_STATUS.OPEN, info.next_time, info.start_time, info.end_time, info.open_type
		)
	end
end

function HelpRankWGCtrl:SetHelpRankActiveStatus(status)
	self.is_set_help_rank_activte_status = status
end

function HelpRankWGCtrl:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HELP_RANK then
		RemindManager.Instance:Fire(RemindName.HelpRankRed)
		if status == ACTIVITY_STATUS.OPEN then
			-- 冲榜助力，买完就关闭
			self:ChangeHelpRankActStatus()
		end
	end
end