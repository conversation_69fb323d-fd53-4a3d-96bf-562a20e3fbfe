--AiBoss的答题的圈
AnswerCircle = AnswerCircle or BaseClass(SceneObj)

function AnswerCircle:__init(vo)
    self.obj_type = SceneObjType.AnswerCircle
    self.followui_class = AnswerCircleFollow
    self.draw_obj:SetObjType(self.obj_type)
    self.vo = vo
end

function AnswerCircle:__delete()
    if self.effect_loader then
        self.effect_loader:Destroy()
        self.effect_loader = nil
    end

    if self.enter_effect_loader then
        self.enter_effect_loader:Destroy()
        self.enter_effect_loader = nil
    end

    if self.play_effect_timer then
        GlobalTimerQuest:CancelQuest(self.play_effect_timer)
        self.play_effect_timer = nil
    end
end

function AnswerCircle:InitAppearance()
    SceneObj.InitAppearance(self)
    local follow_ui = self:GetFollowUi()
    if follow_ui then
        follow_ui:SetHpVisiable(false)
        follow_ui:ForceSetVisible(true)
    end
    local bundle, name = nil, nil
    bundle, name = ResPath.GeBufftEffect(self.vo.res_name)
    self:ChangeModel(SceneObjPart.Main, bundle, name)

    local follow_ui = self:GetFollowUi()
    if follow_ui then
        follow_ui:FlushVoInfo(self.vo)
        follow_ui:ForceSetVisible(not self.vo.is_shield)
    end
end

function AnswerCircle:CreateFollowUi()
    SceneObj.CreateFollowUi(self)
    local follow_ui = self:GetFollowUi()
    if self.draw_obj and follow_ui then
        local point = self.draw_obj:GetAttachPoint(AttachPoint.UI)
        follow_ui:FlushVoInfo(self.vo)
        follow_ui:SetFollowTarget(point, self.draw_obj:GetName())
    end
end

function AnswerCircle:SetVo(vo)
    self.vo = vo
    if not self:IsDeleted() then
        self:InitAppearance()
        self:SetLogicPos(self.vo.pos_x, self.vo.pos_y)
        self:ReloadUIName()
        self:VisibleChanged(not self.vo.is_shield)
        local follow_ui = self:GetFollowUi()
        if follow_ui then
            follow_ui:FlushVoInfo(self.vo)
            follow_ui:ForceSetVisible(not self.vo.is_shield)
        end
        if self.vo.is_shield then
            if self.enter_effect_loader then
                self.enter_effect_loader:SetActive(false)
                self.enter_effect_loader:Destroy()
                self.enter_effect_loader = nil
            end
        end
    end
end

function AnswerCircle:CreateAnswerEffect()
    if not self:IsDeleted() and not self.vo.is_shield then
        if nil == self.effect_loader then
			self.effect_loader = AllocAsyncLoader(self, "effect_loader")
			self.effect_loader:SetParent(self.draw_obj:GetRoot().transform)
			self.effect_loader:SetIsUseObjPool(true)
            local b,a = ResPath.GeBufftEffect(self.vo and self.vo.effect_name)
            if a and b then
                self.effect_loader:Load(b,a,function ()
                    --self.effect_loader:SetLocalPosition(Vector3(0, 0.1, 0))
                end)
            end
		end
		self.effect_loader:SetActive(true)
    end

    self.play_effect_timer = GlobalTimerQuest:AddDelayTimer(function()
        if self.effect_loader then
            self.effect_loader:Destroy()
            self.effect_loader = nil
        end
    end, 3)
end



function AnswerCircle:SetNumEffect(num, res_name)
    if not self:IsDeleted() and not self.vo.is_shield then
        if num > 0 then
            if nil == self.enter_effect_loader then
                self.enter_effect_loader = AllocAsyncLoader(self, "enter_effect_loader")
                self.enter_effect_loader:SetParent(self.draw_obj:GetRoot().transform)
                self.enter_effect_loader:SetIsUseObjPool(true)
                local b,a = ResPath.GeBufftEffect(res_name)
                if a and b then
                    self.enter_effect_loader:Load(b,a)
                end
                self.enter_effect_loader:SetActive(true)
            end
        else
            if self.enter_effect_loader then
                self.enter_effect_loader:SetActive(false)
                self.enter_effect_loader:Destroy()
                self.enter_effect_loader = nil
            end
        end
    end
end
