UltimateBattleAnswerView = UltimateBattleAnswerView or BaseClass(SafeBaseView)

function UltimateBattleAnswerView:__init()
	self:AddViewResource(0, "uis/view/country_map_ui/ultimate_battlefield_prefab", "layout_ultimate_talent_answer")
	self:SetMaskBg(false, true)
end

function UltimateBattleAnswerView:LoadCallBack()
    if not self.answer_option_list then
        self.answer_option_list = {}

        for i = 1, 4 do
            local answer_option_obj = self.node_list.answer_root:FindObj(string.format("answer_option_%d", i))
            if answer_option_obj then
                local cell = AnswerOptionItemRender.New(answer_option_obj)
                cell:SetIndex(i)
                cell:SetClickCallBack(BindTool.Bind(self.ClickSetleAnswer, self))
                self.answer_option_list[i] = cell
            end
        end
    end

    if not self.answer_right_reward then
        self.answer_right_reward = AnswerOptionRewardRender.New(self.node_list.answer_right_reward)
    end

    if not self.answer_wrong_reward then
        self.answer_wrong_reward = AnswerOptionRewardRender.New(self.node_list.answer_wrong_reward)
    end
end

function UltimateBattleAnswerView:ReleaseCallBack()
    if self.answer_option_list and #self.answer_option_list > 0 then
		for _, incubate_cell in ipairs(self.answer_option_list) do
			incubate_cell:DeleteMe()
			incubate_cell = nil
		end

		self.answer_option_list = nil
	end

	if self.answer_right_reward then
		self.answer_right_reward:DeleteMe()
		self.answer_right_reward = nil
	end

	if self.answer_wrong_reward then
		self.answer_wrong_reward:DeleteMe()
		self.answer_wrong_reward = nil
	end

    self:CleanTimeDown()
end


-- 选中答案（发送协议）
function UltimateBattleAnswerView:ClickSetleAnswer(answer_cell)
    if not answer_cell.data then
        return
    end

    if answer_cell.data.is_select then
        return
    end

    if self.answer_option_list then
		for _, incubate_cell in ipairs(self.answer_option_list) do
            incubate_cell:SetSelectStatus(answer_cell.index)
		end
	end

    UltimateBattlefieldWGCtrl.Instance:RequestAnswer(answer_cell.index)
end


function UltimateBattleAnswerView:OnFlush()
	-- 刷新列表-刷新奖励
    self:FlushQuestionInfo()
    -- 刷新倒计时
    self:FlushTimeCountDownEnter()
end

-- 刷新答题相关
function UltimateBattleAnswerView:FlushQuestionInfo()
    local question_info = UltimateBattlefieldWGData.Instance:GetQuestionInfo()
    local base_cfg = UltimateBattlefieldWGData.Instance:GetBaseCfg()

    if (not question_info) or (not base_cfg) then
        return
    end

    local str = string.format(Language.UltimateBattlefield.AnswerCurProgress, question_info.question_num, base_cfg.question_num)
    self.node_list.answer_title.text.text = str
    str = Language.UltimateBattlefield.AnswerTitle
    self.node_list.answer_title_name.text.text = str
    local ram = math.random(1, #Language.UltimateBattlefield.AnswerTips)
    str = Language.UltimateBattlefield.AnswerTips[ram]
    self.node_list.answer_tips_desc.text.text = str

	local right_number = UltimateBattlefieldWGData.Instance:GetQuestionRightNumber()
	local cfg = UltimateBattlefieldWGData.Instance:GetTalentRefreshCfgBySeq(right_number)
    if cfg then
        self.node_list.answer_result_txt.text.text = string.format(Language.UltimateBattlefield.AnswerRightRefresh, right_number, cfg.refresh_times or 0)  
    else
        self.node_list.answer_result_txt.text.text = string.format(Language.UltimateBattlefield.AnswerRightRefresh, 0, 0)  
    end

    -- 刷新奖励和列表
    self:FlushQuestionList(question_info.question_seq)
    self:FlushQuestionReward(question_info.question_seq)
end

-- 刷新答案列表
function UltimateBattleAnswerView:FlushQuestionList(question_seq)
    local question_cfg = UltimateBattlefieldWGData.Instance:GetQuestionCfgBySeq(question_seq)

    if not question_cfg then
        return
    end

    self.node_list.answer_question.text.text = question_cfg.question

    -- 刷新四个答案
    local list = {}
    for i = 1, 4 do
        local answer_data = {}
        answer_data.status = 0
        answer_data.question = question_cfg[string.format("answer%d", i)]
        answer_data.correct_answer = question_cfg.correct_answer
        answer_data.is_select = false
        list[i] = answer_data
    end

    if self.answer_option_list and list then
		for i, incubate_cell in ipairs(self.answer_option_list) do
            if incubate_cell and list[i] then
                incubate_cell:SetData(list[i])
            end
		end
	end
end

-- 刷新奖励
function UltimateBattleAnswerView:FlushQuestionReward(question_seq)
    local question_cfg = UltimateBattlefieldWGData.Instance:GetQuestionCfgBySeq(question_seq)

    if not question_cfg then
        return
    end

    local reward_data = {}

    if self.answer_right_reward then
        reward_data.reward_item = question_cfg.succ_reward_item
        self.answer_right_reward:SetData(reward_data)
	end

	if self.answer_wrong_reward then
        reward_data.reward_item = question_cfg.fail_reward_item
        self.answer_wrong_reward:SetData(reward_data)
	end
end

-----------------活动时间倒计时-------------------
function UltimateBattleAnswerView:FlushTimeCountDownEnter()
    local question_info = UltimateBattlefieldWGData.Instance:GetQuestionInfo()

    if not question_info then
        return
    end

    -- 这里减1秒是防止误差，这边关了界面和协议打开在同一秒
    self:FlushTimeCountDown(question_info.question_time - 1)
end

function UltimateBattleAnswerView:CleanTimeDown()
	if CountDownManager.Instance:HasCountDown("ultimate_battle_answer_view_down") then
		CountDownManager.Instance:RemoveCountDown("ultimate_battle_answer_view_down")
	end
end

function UltimateBattleAnswerView:FlushTimeCountDown(end_time)
	local invalid_time = end_time
	if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
		self:CleanTimeDown()
		self.node_list.answer_time_txt.text.text = TimeUtil.FormatSecondDHM6(invalid_time - TimeWGCtrl.Instance:GetServerTime())
		CountDownManager.Instance:AddCountDown("ultimate_battle_answer_view_down", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
	end
end

function UltimateBattleAnswerView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.node_list.answer_time_txt.text.text = TimeUtil.FormatSecondDHM6(valid_time)
	end
end

function UltimateBattleAnswerView:OnComplete()
    self.node_list.answer_time_txt.text.text = ""
	self:Close()
end

------------------------------四个选项item-----------------------
AnswerOptionItemRender = AnswerOptionItemRender or BaseClass(BaseRender)
function AnswerOptionItemRender:OnFlush()
    if not self.data then
        return
    end

    local str = "a2_bgyx_dt_bg5"
    if self.data.status == 1 then
        str = "a2_bgyx_dt_bg4"
    elseif self.data.status == 2 then
        str = "a2_bgyx_dt_bg3"
    end
 
    self.node_list["option_bg"].image:LoadSprite(ResPath.GetCommonImages(str))
    self.node_list["option_desc"].text.text = self.data.question
    self.node_list["option_status_right"]:CustomSetActive(self.data.status == 1)
    self.node_list["option_status_wrong"]:CustomSetActive(self.data.status == 2)
end

-- 设置状态
function AnswerOptionItemRender:SetSelectStatus(cur_select)
    if not self.data then
        return
    end

    if self.index == self.data.correct_answer then
        self.data.status = 1
    elseif self.index == cur_select then
        self.data.status = 2
    end

    self.data.is_select = true
    self:Flush()
end


----------奖励-------------
AnswerOptionRewardRender = AnswerOptionRewardRender or BaseClass(BaseRender)
function AnswerOptionRewardRender:__init()
    if not self.item_list then
	    self.item_list = AsyncListView.New(ItemCell, self.node_list.reward_item_list)
    end
end

function AnswerOptionRewardRender:__delete()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end

function AnswerOptionRewardRender:OnFlush()
    if not self.data then
        return 
    end

    local real_reward_item = {}
    for _, reward_data in pairs(self.data.reward_item) do
        if reward_data and reward_data.item_id then
            table.insert(real_reward_item, reward_data)
        end
    end

    self.item_list:SetDataList(real_reward_item)
end