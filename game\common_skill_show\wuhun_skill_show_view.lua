WuHunSkillShowView = WuHunSkillShowView or BaseClass(SafeBaseView)

function WuHunSkillShowView:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self:SetMaskBg(false)
	self:SetMaskBgAlpha(0)
    self:AddViewResource(0, "uis/view/skill_show_ui_prefab", "layout_wuhun_skill_show_view")
    self:AddViewResource(0, "uis/view/skill_show_ui_prefab", "layout_skill_desc_view")
end

function WuHunSkillShowView:LoadCallBack()
	--self:InitEnemy()
    -- self:InitSkillInfo()
    self.skill_play_timestemp = 0
    self.cur_select_wh_index = -1
    if not self.wuhun_show_list then
        self.wuhun_show_list = AsyncListView.New(SkillShowWuHunRender, self.node_list["wuhun_list_view"])
        self.wuhun_show_list:SetSelectCallBack(BindTool.Bind(self.OnSelectWuHunCallBack, self))
        self.wuhun_show_list:SetLimitSelectFunc(BindTool.Bind(self.IsListLimitClick, self))
    end

    self.skill_btn = SkillShowSkillRender.New(self.node_list.skill_btn)
    self.skill_btn:SetNeedChangeSkillBtnPos(false)
    self.skill_btn:SetClickCallBack(BindTool.Bind(self.OnClickSkillBtn, self, self.skill_btn))

    if not self.skill_pre_view then
		self.skill_pre_view = SkillPreview.New(self.node_list.skill_pre_root, self.node_list.skill_pre_rawimage, true)
	end

    self.message_root_tween = self.node_list.skill_desc_root:GetComponent(typeof(UGUITweenPosition))
    XUI.AddClickEventListener(self.node_list.bag_forward_button, BindTool.Bind2(self.PlayBeastsMessagePositionTween, self, false))
    XUI.AddClickEventListener(self.node_list.bag_reverse_button, BindTool.Bind2(self.PlayBeastsMessagePositionTween, self, true))
end

function WuHunSkillShowView:ReleaseCallBack()
    self:CleanSkillBtnCDTimer()
    self:CleanEffectViewCDTimer()

    self.cur_select_wh_index = -1
    self.skill_play_timestemp = nil

    if self.wuhun_show_list then
        self.wuhun_show_list:DeleteMe()
        self.wuhun_show_list = nil
    end

    if self.skill_btn then
        self.skill_btn:DeleteMe()
        self.skill_btn = nil
    end

    if self.skill_pre_view then
		self.skill_pre_view:DeleteMe()
		self.skill_pre_view = nil
	end

    self.message_root_tween = nil
end

function WuHunSkillShowView:SetShowDataAndOpen(data)
	self.show_data = data
	self:Open()
end

function WuHunSkillShowView:IsListLimitClick(cell)
    local is_playing_skill = Status.NowTime < self.skill_play_timestemp
    if is_playing_skill then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Skill.ShowSkillLimitClickStr)
    end

    return is_playing_skill
end

-- 列表选择返回
function WuHunSkillShowView:OnSelectWuHunCallBack(item)
    if self:IsLimitClick() then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Skill.ShowSkillLimitClickStr)
        return
    end

	if nil == item or nil == item.data then
		return
	end

    local data = item.data
    if self.cur_select_wh_index == data.wuhun_id then
        return
    end

    local wuhun_id = data.wuhun_id
    self.cur_select_wh_index = wuhun_id

    -- 刷新技能格子
    local cfg = WuHunWGData.Instance:GetWuHunActiveCfg(wuhun_id)
    if cfg then
        self.skill_btn:SetData({skill_id = cfg.skill_id, skill_level = cfg.skill_level,})
        self:FlushSkillInfo(cfg.skill_id, 1, cfg.wh_name, cfg.wuhun_story)
    end

    self:OnClickSkillBtn(self.skill_btn)
end

-- 点击技能
function WuHunSkillShowView:OnClickSkillBtn(item)
    if self:IsLimitClick() then
		return
	end

    local data = item:GetData()
    if not data then
        return
    end

    self:CleanEffectViewCDTimer()
    self:CleanSkillBtnCDTimer()

    self.show_data.skill_id = data.skill_id
    self.show_data.level = data.skill_level

    -- 技能文字界面
    local wuhun_data = WuHunWGData.Instance:GetWuHunSkillActiveCfg(data.skill_id)
    if wuhun_data then
        WuHunWGCtrl.Instance:OpenSkillShowEffetView(wuhun_data.wuhun_id)
    end
    -- 关闭技能文字界面
    self.skill_effect_view_timer = GlobalTimerQuest:AddDelayTimer(function ()
        WuHunWGCtrl.Instance:CloseSkillShowEffetView()
    end, 3)

    -- 技能按钮CD
    local skill_show_time = 10
    self.skill_play_timestemp = Status.NowTime + skill_show_time
    self:SetAllSkillBtnCD(string.format("%.1f", skill_show_time), skill_show_time)
    self.skill_btn_cd_timer = CountDown.Instance:AddCountDown(skill_show_time, 0.1,
        function(elapse_time, total_time)
            self:SetAllSkillBtnCD(string.format("%.1f", total_time - elapse_time), skill_show_time)
        end,
        function()
            self:SetAllSkillBtnCD(0, skill_show_time)
        end
    )

    if self.skill_pre_view:GetPreviewIsLoaded() then
		self.skill_pre_view:PlaySkill(data.skill_id)
	else
		self.skill_pre_view:SetPreviewLoadCb(function()
			self.skill_pre_view:PlaySkill(data.skill_id)
		end)
	end
end


function WuHunSkillShowView:SetAllSkillBtnCD(time, total_time)
    if self.skill_btn then
        self.skill_btn:SetSkillBtnCD(time, total_time)
    end
end

-- 技能按钮倒计时
function WuHunSkillShowView:CleanSkillBtnCDTimer()
    if self.skill_btn_cd_timer and CountDown.Instance:HasCountDown(self.skill_btn_cd_timer) then
        CountDown.Instance:RemoveCountDown(self.skill_btn_cd_timer)
        self.skill_btn_cd_timer = nil
    end
end

-- 技能文字界面倒计时
function WuHunSkillShowView:CleanEffectViewCDTimer()
    if self.skill_effect_view_timer then
        GlobalTimerQuest:CancelQuest(self.skill_effect_view_timer)
        self.skill_effect_view_timer = nil
    end
end

function WuHunSkillShowView:IsLimitClick(no_tips)
    local is_playing_skill = Status.NowTime < self.skill_play_timestemp
    if not no_tips and is_playing_skill then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Skill.ShowSkillLimitClickStr)
    end

    return is_playing_skill
end

function WuHunSkillShowView:OnFlush()
    if not self.show_data then
        return
    end

    local wuhun_list_cfg = WuHunWGData.Instance:GetAllWuhunlist()
    if IsEmptyTable(wuhun_list_cfg) then
        return
    end

    if self.wuhun_show_list then
        self.wuhun_show_list:SetDataList(wuhun_list_cfg)
    end

    local show_wuhun_id = self.show_data.wuhun_id
    if self.cur_select_wh_index ~= show_wuhun_id then
        local jump_index = 1
        for k,v in pairs(wuhun_list_cfg) do
            if show_wuhun_id == v.wuhun_id then
                jump_index = k
                break
            end
        end

        self.wuhun_show_list:JumpToIndex(jump_index)
    end
end

--刷新技能描述.
function WuHunSkillShowView:FlushSkillInfo(skill_id, skill_level, skill_name, skill_desc)
	local client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, skill_level)
	if client_cfg then
		self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(client_cfg.icon_resource))
		self.node_list.skill_nane.text.text = skill_name
		self.node_list.skill_desc.text.text = skill_desc
	end
end

--- 播放孵化详情动画
function WuHunSkillShowView:PlayBeastsMessagePositionTween(is_forward)
    self.node_list.bag_forward_button:CustomSetActive(is_forward)
    self.node_list.bag_reverse_button:CustomSetActive(not is_forward)

    if not IsNil(self.message_root_tween) then
        if is_forward then
            self.message_root_tween:PlayForward()
        else
            self.message_root_tween:PlayReverse()
        end
    end
end


--===================================================================
SkillShowWuHunRender = SkillShowWuHunRender or BaseClass(BaseRender)
function SkillShowWuHunRender:OnFlush()
    local bundle, asset = ResPath.GetSkillShowImg("wh_" .. self.data.wuhun_icon)
    self.node_list.icon.image:LoadSprite(bundle, asset, function()
        self.node_list.icon.image:SetNativeSize()
    end)

    self.node_list.name.text.text = self.data.wh_name
end

function SkillShowWuHunRender:OnSelectChange(is_select)
    self.node_list["select_bg"]:SetActive(is_select)
end