
require("game/operation_activity/duobei/activity_duobei_wg_data")

-- 运营活动-多倍有礼
OperationActDuoBeiWGCtrl = OperationActDuoBeiWGCtrl or BaseClass(BaseWGCtrl)

function OperationActDuoBeiWGCtrl:__init()
	if OperationActDuoBeiWGCtrl.Instance then
		ErrorLog("[OperationActDuoBeiWGCtrl] Attemp to create a singleton twice !")
	end
	OperationActDuoBeiWGCtrl.Instance = self

	self.data = OperationActDuoBeiWGData.New()
	OperationActivityWGCtrl.Instance:ListenHotUpdate("config/auto_new/operaact_duobei_cfg_auto", BindTool.Bind(self.OnHotUpdate, self))
end

function OperationActDuoBeiWGCtrl:__delete()
	OperationActDuoBeiWGCtrl.Instance = nil
	self.data:DeleteMe()
	self.data = nil
end

--多倍来袭返回
function OperationActDuoBeiWGCtrl:SCDuoBeiJiangLi(protocol)
	self.data:SetDuoBeiInfo(protocol)
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_duobei, "RefreshDuoBei")
end

function OperationActDuoBeiWGCtrl:OnHotUpdate()
	self.data:LoadConfig()
    OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_duobei)
end