require("game/role/skill/skill_wg_data")
require("game/role/skill/role_exp_efficiency")
require("game/role/skill/skill_cfg_view")
require("game/role/skill/role_skill_pre_view")

--------------------------------------------------------------
--技能相关
--------------------------------------------------------------
SkillWGCtrl = SkillWGCtrl or BaseClass(BaseWGCtrl)
function SkillWGCtrl:__init()
	if SkillWGCtrl.Instance then
		ErrorLog("[SkillWGCtrl] Attemp to create a singleton twice !")
	end
	SkillWGCtrl.Instance = self
	self.skill_data = SkillWGData.New()
	self.exp_efficiency_view = ExpEfficiencyView.New(GuideModuleName.ExpEfficiencyView)
	self.exp_efficiency_world_view = ExpEfficiencyWorldView.New()
	self.skill_cfg_view = SkillCfgView.New()
	self.role_skill_pre_view = RoleSkillPreView.New()

	self:RegisterAllProtocols()

	self.other_skill_info = {}
end

function SkillWGCtrl:__delete()
	SkillWGCtrl.Instance = nil

	self.skill_data:DeleteMe()
	self.skill_data = nil

	if self.exp_efficiency_view then
		self.exp_efficiency_view:DeleteMe()
		self.exp_efficiency_view = nil
	end
	if self.exp_efficiency_world_view then
		self.exp_efficiency_world_view:DeleteMe()
		self.exp_efficiency_world_view = nil
	end

	if self.skill_cfg_view then
		self.skill_cfg_view:DeleteMe()
		self.skill_cfg_view = nil
	end

	self.is_roleskill_remind_id = nil
	self.need_check_tianshen_bianshen_skill = nil
end

function SkillWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCSkillListInfoAck, "OnSkillListInfoAck")
	self:RegisterProtocol(SCSkillInfoAck, "OnSkillInfoAck")
	self:RegisterProtocol(SCSkillOtherSkillInfo, "OnSkillOtherSkillInfo")
	self:RegisterProtocol(SCCareerUpgradeInfo, "OnSCCareerUpgradeInfo")

	self:RegisterProtocol(CSRoleSkillLearnReq)
	self:RegisterProtocol(CSCareerUpgradeOpera)

	-- 经验效率
	self:RegisterProtocol(SCExpEfficiencyInfo, "OnSCExpEfficiencyInfo")
	self:RegisterProtocol(CSExpEfficiencyReq)

	-- 离线经验效率
	self:RegisterProtocol(OfflineHangExpExtra, "OnOfflineHangExpExtra")
	self:RegisterProtocol(CSOfflineHangExpExtra)
end

--经验效率加成请求
function SkillWGCtrl:SendExpEfficiencyReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSExpEfficiencyReq)
	protocol:EncodeAndSend()
end

function SkillWGCtrl:OnSCExpEfficiencyInfo(protocol)
	if self.exp_efficiency_view then
		self.exp_efficiency_view:FlushView(protocol)
	end
	self.skill_data:SetExpEfficiencyInfo(protocol)
	ExpAdditionWGData.Instance:CreatDataList()
	if RoleWGCtrl.Instance.exp_addititon_view:IsOpen() then
		RoleWGCtrl.Instance.exp_addititon_view:Flush()
	end

	GlobalEventSystem:Fire(MainUIEventType.EXP_EFFICIENCY_INFO)
end


--离线经验效率加成请求
function SkillWGCtrl:SendOfflineExpEfficiencyReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSOfflineHangExpExtra)
	protocol:EncodeAndSend()
end

function SkillWGCtrl:OnOfflineHangExpExtra(protocol)
	self.skill_data:SetOfflineExpEfficiencyInfo(protocol)
	ExpAdditionWGData.Instance:CreatOfflineDataList()
	if RoleWGCtrl.Instance.exp_addititon_view:IsOpen() then
		RoleWGCtrl.Instance.exp_addititon_view:Flush()
	end
	OfflineRestWGCtrl.Instance:FlushOfflineView()
end

function SkillWGCtrl:OpenExpEfficiencyView()
	if self.exp_efficiency_view then
		self.exp_efficiency_view:Open()
	end
end

function SkillWGCtrl:OpenExpEfficiencyWorldView()
	if self.exp_efficiency_world_view then
		self.exp_efficiency_world_view:Open()
	end
end

function SkillWGCtrl:OpenSkillCfgView(skill_type)
	if self.skill_cfg_view then
		self.skill_cfg_view:SetSkillType(skill_type)
		self.skill_cfg_view:Open()
	end
end

function SkillWGCtrl:SkillCfgViewIsOpen()
	if self.skill_cfg_view then
		return self.skill_cfg_view:IsOpen()
	end
end

function SkillWGCtrl:OnSkillListInfoAck(protocol)
	-- print_error("【----获取角色技能列表-----】：", protocol.skill_list)
	-- 如果天赋减cd协议没有下发
	if RoleWGData.Instance:GetRoleTalentSkillLevel(SkillWGData.TianFu_Skill_Id_202) == nil then
		RoleWGData.Instance:AddTalentProtcalCallBack(function ()
			self:OnSkillListInfoAck(protocol)
		end)
		return
	end

	self.skill_data:CheckIsNew(protocol.skill_list, protocol.is_init)
	local sd = SkillWGData.Instance
	-- 处理精灵技能。清理精灵技能列表（下面重新构建），考虑是否移除当前使用的jingling_skill_index
	--[[
	sd:CelarJinglingSkillIndexList()
	local old_jingling_skill_index = sd:GetActJinglingSkillIndex()
	local is_clear_old_jingling_skill_index = true
	for k, v in pairs(protocol.skill_list) do
		if v.index == old_jingling_skill_index and old_jingling_skill_index >= 0 then
			is_clear_old_jingling_skill_index = false
			break
		end
	end

	if is_clear_old_jingling_skill_index then
		sd:ClearJinglingSkillIndex()
	end
	]]
	sd:SetDefaultSkillIndex(protocol.default_skill_index)

	local new_skill_list = {}
	local skill_id = 0
	for k, v in pairs(protocol.skill_list) do
		skill_id = v.skill_id
		local skill_cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_id, 1)
		if skill_cfg and skill_cfg.skill_type == SKILL_TYPE.SKILL_2 then
			if protocol.is_init == 0 and sd:GetSkillInfoByIndex(v.index) == nil then
				new_skill_list[#new_skill_list] = v
			end

			local old_skill_info = self.skill_data:GetSkillInfoByIndex(v.index)
			if nil ~= old_skill_info and v.level > old_skill_info.level then
				local skill_cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_id, v.level)
				if nil ~= skill_cfg then
					SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Role.SkillUpgradeHint, skill_cfg.skill_name, v.level))

					if nil ~= RoleWGCtrl.Instance then
						RoleWGCtrl.Instance.skill_view:CreateSkillUpGradeSucessEffect()
					end
				end
			end
		end

		sd:ChangeSkillInfo(v)
		-- self:PlaySkill(v)
	end

	--天神羁绊技能未解锁
	if protocol.tianshen_no_act_skill and protocol.tianshen_no_act_skill[1] and protocol.tianshen_no_act_skill[1] > 0 then
		local skill_temp = {}
		skill_temp.index = 5
        skill_temp.skill_id = protocol.tianshen_no_act_skill[1]
        skill_temp.level = 1
        skill_temp.last_perform = 0
        skill_temp.skill_exp = 0
        skill_temp.awake_level = 0
        skill_temp.cd_past_time = 0
        skill_temp.is_temp_show = true
        sd:ChangeJiBanLockInfo(skill_temp)
    else
    	sd:ChangeJiBanLockInfo({})
	end

	--差异化找出减少的技能
	local skill_list = sd:GetSkillList()
	for k,v in pairs(skill_list) do
		if nil == protocol.skill_list[v.index] then
			sd:DeleteSkill(v.index)
			FunOpen.Instance:DelOpenFunCfgByName("skill_type#skill_" .. v.skill_id)
		end
	end

	-- --刷新主界面技能列表
	-- GlobalEventSystem:Fire(MainUIEventType.ROLE_SKILL_LIST)

	for k, v in pairs(new_skill_list) do
		local can_add_new, cell = MainuiWGCtrl.Instance:AddNewSkill(v.index)
		if can_add_new then
			local skill_ui = cell
			local icon_path =  ResPath.GetSkillIconById(SkillWGData.Instance:GetSkillIconId(v.skill_id))
			FunOpen.Instance:OpenFunByDefine("skill_type#skill_" .. v.skill_id, icon_path, skill_ui)
		end
	end

	if self.is_roleskill_remind_id == nil then
		self.is_roleskill_remind_id = true
	end

	GlobalEventSystem:Fire(SkillEventType.FLUSH_SKILL_LIST, true)
	RemindManager.Instance:Fire(RemindName.SkillUpLevel)
	RemindManager.Instance:Fire(RemindName.SkillUpLevel_Inside)
	RemindManager.Instance:Fire(RemindName.SkillAwake)

	if self.need_check_tianshen_bianshen_skill then
		local tianshen_show_skill_id = -1
		for k, v in pairs(protocol.skill_list) do
			if TianShenWGData.Instance:GetTianShenChuChangSkill(v.skill_id) then
				tianshen_show_skill_id = v.skill_id
				break
			end
		end
	
		if tianshen_show_skill_id >= 0 then
			self:PlayTianShenShowSkill(tianshen_show_skill_id)
			self.need_check_tianshen_bianshen_skill = nil
		end
	end
end

function SkillWGCtrl:SetCheckTianShenBianShenSkill(check_state)
	self.need_check_tianshen_bianshen_skill = check_state
end

-- 天神出场播放动作
function SkillWGCtrl:PlayTianShenShowSkill(skill_id)
	local main_role = Scene.Instance:GetMainRole()
	local main_role_x, main_role_y = main_role:GetLogicPos()
	FightWGCtrl.SendPerformSkillReq(
				SkillWGData.Instance:GetRealSkillIndex(skill_id),
				1,
				main_role_x,
				main_role_y,
				COMMON_CONSTS.INVALID_OBJID, --服务器无效objid是-1
				false,
				main_role_x,
				main_role_y)

	local skill_id, attack_index = SkillWGData.ChangeSkillID(skill_id)
	main_role:SetAttackIndex(attack_index)
	-- 不明白为什么还要多发一条使用技能，先屏蔽
	-- main_role:DoAttackForNoTarget(skill_id)
end

function SkillWGCtrl:PlaySkill(skill_info)
	local skill_list = SkillWGData.Instance:GetSkillList()
	-- body
	for k,v in pairs(skill_list) do
		if TianShenWGData.Instance:GetTianShenChuChangSkill(v.skill_id) then

			local main_role = Scene.Instance:GetMainRole()
			local main_role_x, main_role_y = main_role:GetLogicPos()
			FightWGCtrl.SendPerformSkillReq(
						SkillWGData.Instance:GetRealSkillIndex(v.skill_id),
						1,
						main_role_x,
						main_role_y,
						COMMON_CONSTS.INVALID_OBJID, --服务器无效objid是-1
						false,
						main_role_x,
						main_role_y)

			local skill_id, attack_index = SkillWGData.ChangeSkillID(v.skill_id)
			main_role:SetAttackIndex(attack_index)
			-- 不明白为什么还要多发一条使用技能，先屏蔽
			-- main_role:DoAttackForNoTarget(skill_id)
		end
	end
end

function SkillWGCtrl:OnSkillInfoAck(protocol)
	local skill_info = protocol.skill_info
	if TianShenWGData.Instance:GetIsTianShenNormalSkill(skill_info.skill_id) then
		return
	end

	SkillWGData.Instance:ChangeSkillInfo(skill_info)
	--刷新主界面技能
	GlobalEventSystem:Fire(MainUIEventType.ROLE_SKILL_LIST)
end

function SkillWGCtrl:OnNoTargetCD(skill_id)
	-- body
	if SkillWGData.GetSkillBigType(skill_id) == SKILL_BIG_TYPE.NORMAL then return end
	local skill_info = self.skill_data:GetSkillInfoById(skill_id)
	if not skill_info then return end
	skill_info.last_perform = TimeWGCtrl.Instance:GetCurPiTime()
	skill_info.cd_past_time = 0
	SkillWGData.Instance:ChangeSkillInfo(skill_info)
	GlobalEventSystem:Fire(MainUIEventType.ROLE_SKILL_LIST)
end

--刺客暴击
function SkillWGCtrl:OnSkillOtherSkillInfo(protocol)
	self.skill_data:SetSkillOtherSkillInfo(protocol)
end

function SkillWGCtrl:OnSCCareerUpgradeInfo(protocol)
	local skill_grade = self.skill_data:GetCurUpGradeInfo()
    self.skill_data:SetCurUpGradeInfo(protocol)
	if skill_grade ~= nil and protocol.grade > skill_grade then
		
	elseif skill_grade == 3 and protocol.grade < skill_grade then
		self:CheckResetSkillCfg()
	end

	ViewManager.Instance:FlushView(GuideModuleName.SkillView, TabIndex.skill_upgrade)
	--RemindManager.Instance:Fire(RemindName.SkillUpGrade)
end

function SkillWGCtrl:SendCareerUpgrade(opera_type, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCareerUpgradeOpera)
    protocol.opera_type = opera_type or 0
    protocol.param1 = param1 or 0
    protocol.param2 = param2 or 0
    protocol:EncodeAndSend()
end

--技能学习 one_key_learn 1 一键学习
function SkillWGCtrl:SendRoleSkillLearnReq(skill_id, one_key_learn)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRoleSkillLearnReq)
	protocol.skill_id = skill_id
	if one_key_learn then
		protocol.one_key_learn = one_key_learn
	end
	protocol:EncodeAndSend()
end

function SkillWGCtrl:CheckResetSkillCfg()
	local skill_order,skill_flag  = RoleWGData.Instance:GetSkillCustomInfo()
	local now_skill = SkillWGData.Instance:GetNowSkillInfo()
	local temp_skill_id_list = {}
	for _, skill_info in pairs(now_skill) do
		local is_repeat = false
		for _, v in pairs(skill_order) do
			if skill_info.skill_id == tonumber(v) then
				is_repeat = true
				break
			end
		end
		if not is_repeat then
			table.insert(temp_skill_id_list, skill_info.skill_id)
		end
	end

	table.sort(temp_skill_id_list)

	for i, v in pairs(skill_order) do
		local need_change = true
		for _, skill_info in pairs(now_skill) do
			if skill_info.skill_id == tonumber(v) then
				need_change = false
				break
			end
		end
		if need_change then
			skill_order[i] = table.remove(temp_skill_id_list, 1)
		end
	end
	RoleWGData.Instance:SetCustomInfo(skill_order,skill_flag)
end

-- 打开技能预览
function SkillWGCtrl:OpenRoleSkillPreView(item_index)
	self.role_skill_pre_view:SetNowShowIndex(item_index)

	if not self.role_skill_pre_view:IsOpen() then
		self.role_skill_pre_view:Open()
	else
		self.role_skill_pre_view:Flush()
	end
end