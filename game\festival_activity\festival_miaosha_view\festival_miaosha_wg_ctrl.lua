require("game/festival_activity/festival_miaosha_view/festival_miaosha_wg_data")
FestivalMiaoShaWGCtrl = FestivalMiaoShaWGCtrl or BaseClass(BaseWGCtrl)

function FestivalMiaoShaWGCtrl:__init()
	if FestivalMiaoShaWGCtrl.Instance then
		ErrorLog("[FestivalMiaoShaWGCtrl] Attemp to create a singleton twice !")
	end
	FestivalMiaoShaWGCtrl.Instance = self
	self.festival_miaosha_data = FestivalMiaoshaWGData.New()

	self:RegisterAllProtocols()

	self.refresh_remind_flag_list = {}

end

function FestivalMiaoShaWGCtrl:__delete()
	FestivalMiaoShaWGCtrl.Instance = nil

	if self.festival_miaosha_data ~= nil then
		self.festival_miaosha_data:DeleteMe()
		self.festival_miaosha_data = nil
	end

end

function FestivalMiaoShaWGCtrl:RegisterAllProtocols()
	-----------------------------------限时秒杀协议------------------------------------
	self:RegisterProtocol(SCFestivalSpikeInfo, "OnSCHeFuTimedSpikeInfo")
	self:RegisterProtocol(SCFestivalSpikeItemUpdate, "OnSCHeFuTimedSpikeItemUpdate")
	self:RegisterProtocol(SCFestivalSpikeQuotaUpdate, "OnSCHeFuTimedSpikeQuotaUpdate")
	self:RegisterProtocol(SCFestivalSpikeBuyTimesInfo, "OnSCHeFuTimedSpikeBuyTimesInfo")
	--self:RegisterProtocol(SCTimedSpikeNewTagInfo, "OnSCTimedSpikeNewTagInfo")--10420（所有的秒杀活动）限时秒杀新字标记
	-----------------------------------------------------------------------------------
end

-----------------------------------------------------------限时秒杀----------------------------------------------------------------

--10090// 合服活动-限时秒杀信息
function FestivalMiaoShaWGCtrl:OnSCHeFuTimedSpikeInfo(protocol)
	self.festival_miaosha_data:SetSCHeFuTimedSpikeInfo(protocol)

	--8.1 策划说把秒杀提醒屏蔽
	-- self:CreateOpenMiaoShaTipsCountDown()

	-- self:CretaeRemindDelayFunc(protocol.type)

	FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2271)
	RemindManager.Instance:Fire(RemindName.Festival_MiaoSha)
end

--10091// 合服活动-限时秒杀商品更新
function FestivalMiaoShaWGCtrl:OnSCHeFuTimedSpikeItemUpdate(protocol)
	self.festival_miaosha_data:SetSCHeFuTimedSpikeItemUpdate(protocol)
	--8.1 策划说把秒杀提醒屏蔽
	-- self:CreateOpenMiaoShaTipsCountDown()
	-- self:CretaeRemindDelayFunc(protocol.type)

	FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2271)
	RemindManager.Instance:Fire(RemindName.Festival_MiaoSha)
end

--10092// 合服活动-限时秒杀额度奖励更新
function FestivalMiaoShaWGCtrl:OnSCHeFuTimedSpikeQuotaUpdate(protocol)
	self.festival_miaosha_data:SetSCHeFuTimedSpikeQuotaUpdate(protocol)
	FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2271)
	RemindManager.Instance:Fire(RemindName.Festival_MiaoSha)
end

--10093,// 合服活动-限时秒杀购买次数更新
function FestivalMiaoShaWGCtrl:OnSCHeFuTimedSpikeBuyTimesInfo(protocol)
	self.festival_miaosha_data:SetSCHeFuTimedSpikeBuyTimesInfo(protocol)

	FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2271)
	RemindManager.Instance:Fire(RemindName.Festival_MiaoSha)
end

--创建红点延迟，用于红点显示
function FestivalMiaoShaWGCtrl:CretaeRemindDelayFunc(type)
	if self.refresh_remind_flag_list[type] then
		GlobalTimerQuest:CancelQuest(self.refresh_remind_flag_list[type])
		self.refresh_remind_flag_list[type] = nil
	end
	local refresh_time_list = self.festival_miaosha_data:GetRefreshTimeList()

	if refresh_time_list[type] == nil or refresh_time_list[type] <= 0 then
		return
	end

	--延迟到刷新时间把红点标记设置为true
	if self.refresh_remind_flag_list[type] == nil then
		self.refresh_remind_flag_list[type] = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.DelayRefreshRemindFlag, self, type), refresh_time_list[type] - 1)
	end
end


function FestivalMiaoShaWGCtrl:DelayRefreshRemindFlag(type)
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	PlayerPrefsUtil.SetInt("refresh_remind_flag" .. type .. role_id, 1)
end

--延迟剩余30秒内打开提示框
function FestivalMiaoShaWGCtrl:CreateOpenMiaoShaTipsCountDown()
	if self.hefu_miaosha_tips_quset then
		GlobalTimerQuest:CancelQuest(self.hefu_miaosha_tips_quset)
		self.hefu_miaosha_tips_quset = nil
	end

	local time

	local refresh_time_list = self.festival_miaosha_data:GetRefreshTimeList()
	--拿到最小的时间进行计时
	for i=1,3 do
		if refresh_time_list[i] ~= nil and refresh_time_list[i] > 0 then
			if time == nil then
				time = refresh_time_list[i]
			elseif time > refresh_time_list[i] then
				time = refresh_time_list[i]
			end
		end
	end

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	-- --是否勾选今日不在展示提示框 :1(勾选) 0(未勾选)
	-- local value = PlayerPrefsUtil.GetInt("hefu_miaosha_tips_" .. role_id .. cur_day)
	--是否勾选界面内的提醒选项 :0(勾选) 1(未勾选)
	local value1 = PlayerPrefsUtil.GetInt("hefu_miaosha_toggle_" .. role_id)
	if value1 == 1 then --or value == 1 then
		return
	end

	if time == nil or time <= 0 then
		return
	end

	local temp_time = 0
	local count_down_time = 0

	if time <= 30 then
		temp_time = 0
		count_down_time = time
	else
		temp_time = time - 30
		count_down_time = 30
	end

	--延迟剩余30秒内打开提示框
	if self.hefu_miaosha_tips_quset == nil then
		self.hefu_miaosha_tips_quset = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.OpenHeFuMiaoshaTips, self, count_down_time), temp_time)
	end
end

--打开限时秒杀提示框
function FestivalMiaoShaWGCtrl:OpenHeFuMiaoshaTips(time)
	if CgManager.Instance:IsCgIng() then
		return
	end
	local state = FestivalActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.FESTIVAL_ACT_OA_TIMED_SPIKE_2)

	if not state then
		return
	end

	local yes_callback = function ()
		--打开限时秒杀界面
		FestivalActivityWGCtrl.Instance:Open(TabIndex.festival_activity_2271)
	end

	if XianShiMiaoShaWGCtrl.Instance.xianshi_miaosha_tips and not XianShiMiaoShaWGCtrl.Instance.xianshi_miaosha_tips:IsOpen() and not OperationActivityWGCtrl.Instance:GetViewIsOpen() then
		XianShiMiaoShaWGCtrl.Instance.xianshi_miaosha_tips:SetData(Language.HeFuMiaoShaDesc.MiaoShaTips, time, yes_callback)
		XianShiMiaoShaWGCtrl.Instance.xianshi_miaosha_tips:Open()
	end
end