
BiZuoRewardView = BiZuoRewardView or BaseClass(SafeBaseView)
function BiZuoRewardView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/bizuo_ui_prefab", "layout_zhaohui_result")
	self.count_num = 0
end

function BiZuoRewardView:__delete()

end

function BiZuoRewardView:ReleaseCallBack()
	if self.ph_cell_list then
		self.ph_cell_list:DeleteMe()
		self.ph_cell_list = nil
	end
	BiZuoWGCtrl.Instance.can_open_view = false
	self.count_num = 0

end

-- 切换标签调用
function BiZuoRewardView:ShowIndexCallBack()
	self:FulshCellList()
end

function BiZuoRewardView:LoadCallBack()
	self.node_list.Button_Close.button:AddClickListener(BindTool.Bind(self.OnckickClose, self))
	-- self:FulshCellList()
end

function BiZuoRewardView:OnckickClose()
	BiZuoWGCtrl.Instance.can_open_view = false
	BiZuoWGCtrl.Instance.view:FlushDta()
	self.count_num = 0 
	if self.ph_cell_list then
		self.ph_cell_list:DeleteMe()
		self.ph_cell_list = nil
	end
	self:Close()

end

function BiZuoRewardView:GetData(data,itemnum)
	self.data = data
	self.itemnum = itemnum	
	-- print_error(#self.data)
end

function BiZuoRewardView:FulshCellList()
	if  self.ph_cell_list==nil then
		self.ph_cell_list = AsyncBaseGrid.New()
		self.ph_cell_list:SetStartZeroIndex(false)

	end
	if  self.ph_cell_list and self.count_num <= self.itemnum then
		self.ph_cell_list:CreateCells({col = 2, cell_count = self.itemnum, list_view = self.node_list["ph_list"]})
		self.count_num = self.itemnum
		--所有数据严格按照预制体，预制体一旦改变则需要改变当前的数据
		local num = math.floor(self.count_num/2)
		local rect = self.node_list["ph_list"].rect
		local y = self.count_num==1 and 0 or 50.4
		if num < self.count_num/2 then
			num =  num + 1
		end
		local change_width = num*110--预制体的宽度
		if change_width > 929 then
			change_width = 929
		end
		if rect then
			rect.sizeDelta = Vector2(change_width,261)
			rect.anchoredPosition = Vector2(4.4,y)
		end

		
	end
	if self.ph_cell_list and self.data~=nil and not IsEmptyTable(self.data) then
		self.ph_cell_list:SetDataList(self.data,0)
	end
end