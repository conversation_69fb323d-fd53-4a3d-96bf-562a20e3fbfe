--------------------------------------------------------------
--角色天赋
--------------------------------------------------------------
RoleWGCtrl = RoleWGCtrl or BaseClass(BaseWGCtrl)
function RoleWGCtrl:InitRoleTalentWGCtrl()
	self:RegisterProtocol(CSRoleTelentOperate)
	self:RegisterProtocol(SCRoleTelentInfo, "OnRoleTelentInfo")
end

function RoleWGCtrl:DeleteRoleTalentWGCtrl()

end

function RoleWGCtrl:OnRoleTelentInfo(protocol)
	self.role_data:SetRoleTelentInfo(protocol)
	GlobalEventSystem:Fire(SkillEventType.FLUSH_TALENT_INFO)
	--RemindManager.Instance:Fire(RemindName.SkillView)
	RemindManager.Instance:Fire(RemindName.TalentSkill)
	-- cocos项目的 先屏蔽
	-- -- Remind.Instance:DoRemind(RemindId.role_transfer)
	if self.role_talent_tips:IsOpen() then
		self.role_talent_tips:FlushData()
	end
end

function RoleWGCtrl:SendRoleTelentOperate(opera_type, param_1,param_2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRoleTelentOperate)
	protocol.opera_type = opera_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol:EncodeAndSend()
end
