HolyHeavenlyDomainCountryTip = HolyHeavenlyDomainCountryTip or BaseClass(SafeBaseView)

function HolyHeavenlyDomainCountryTip:__init()
	self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/holy_heavenly_domain_ui_prefab", "layout_holy_heavenly_domain_country_tip")
	self.show_data = nil
end

function HolyHeavenlyDomainCountryTip:SetData(data)
	self.show_data = data
	if self.show_data ~= nil then
		local city_cfg = HolyHeavenlyDomainWGData.Instance:GetCityCfgByCitySeq(self.show_data.seq)
		if IsEmptyTable(city_cfg) then
			return
		end

		HolyHeavenlyDomainWGCtrl.Instance:OnDivineDomainOperate(CROSS_DIVINE_DOMAIN_OPERATE_TYPE.MONSTER_INFO, city_cfg.seq)

		if not self:IsOpen() then
			self:Open()
		else
			self:Flush()
		end
	end
end

function HolyHeavenlyDomainCountryTip:LoadCallBack()
    if not self.boss_list then
        self.boss_list = AsyncListView.New(HHDCountryTipBossItemRender, self.node_list.boss_list)
		self.boss_list:SetStartZeroIndex(false)
		self.boss_list:SetSelectCallBack(BindTool.Bind(self.OnSelectBossHandler, self))
    end

	if not self.score_list then
		self.score_list = AsyncListView.New(HHDCountryTipScoreItemRender, self.node_list.score_list)
	end

    if not self.reward_item_list then
        self.reward_item_list = AsyncListView.New(ItemCell, self.node_list.reward_item_list)
		self.reward_item_list:SetStartZeroIndex(true)
    end

	self.select_boss_data = {}
	self.select_boss_index = 1
	XUI.AddClickEventListener(self.node_list["btn_guanzhu"],BindTool.Bind(self.OnClickGuanZhuHandler, self))
    XUI.AddClickEventListener(self.node_list["btn_zhaoji"],BindTool.Bind(self.OnClickZhaoJi, self))
    XUI.AddClickEventListener(self.node_list["btn_target"],BindTool.Bind(self.OnClickTarget, self))
    XUI.AddClickEventListener(self.node_list["btn_war_reward"],BindTool.Bind(self.OnClickWarReward, self))
    XUI.AddClickEventListener(self.node_list["btn_siege"],BindTool.Bind(self.OnClickSiege, self))
	XUI.AddClickEventListener(self.node_list["btn_city_occupy_reward"],BindTool.Bind(self.OnClickCityOccupyReward, self))
	XUI.AddClickEventListener(self.node_list["btn_tiaozhan"],BindTool.Bind(self.OnClickTiaoZhan, self))
end

function HolyHeavenlyDomainCountryTip:ShowIndexCallBack()
	RectTransform.SetAnchoredPositionXY(self.node_list.tween_root.rect, 1000, 0)
	self.node_list.tween_root.rect:DOAnchorPos(Vector2(0 , 0), 0.3)
end

function HolyHeavenlyDomainCountryTip:ReleaseCallBack()
    if self.boss_list then
        self.boss_list:DeleteMe()
        self.boss_list = nil
    end

	if self.score_list then
        self.score_list:DeleteMe()
        self.score_list = nil
    end

    if self.reward_item_list then
        self.reward_item_list:DeleteMe()
        self.reward_item_list = nil
    end
end

function HolyHeavenlyDomainCountryTip:OnFlush()
    if self.show_data == nil then
		return
	end

	local city_cfg = HolyHeavenlyDomainWGData.Instance:GetCityCfgByCitySeq(self.show_data.seq)
	if IsEmptyTable(city_cfg) then
		return
	end

	local city_seq = city_cfg.seq

	-- boss数据
	local boss_data_list = HolyHeavenlyDomainWGData.Instance:GetCountryTipBossDataList(city_seq)
	self.boss_list:SetDataList(boss_data_list)
	-- 奖励列表
	-- local reward_data_list = HolyHeavenlyDomainWGData.Instance:GetCountryTipRewardDataList(city_seq)
	-- self.reward_item_list:SetDataList(reward_data_list)

	local is_main_city = city_cfg.type == 4
	local city_info = HolyHeavenlyDomainWGData.Instance:GetCityInfoBySeq(city_seq)
	local owner_server_name
	local owner_camp
	
	if is_main_city then
		local camp = city_cfg.relegation_camp
		owner_server_name = HolyHeavenlyDomainWGData.Instance:GetCampServerIdByCampSeq(camp)
		owner_camp = camp
		self.node_list.desc_act_progress.text.text = Language.HolyHeavenlyDomain.CityCannotBeAttack
	else
		owner_server_name = city_info and city_info.owner_usid

		if owner_server_name then
			owner_camp = HolyHeavenlyDomainWGData.Instance:GetCountryIdByServerUsid(owner_server_name)
		end
	end

	local city_owener_str = owner_server_name and owner_server_name or Language.HolyHeavenlyDomain.Neutral
	self.node_list.desc_zhanlin.text.text = string.format(Language.HolyHeavenlyDomain.CityOwer, city_owener_str)

	local city_owner_bundle, city_owner_asset = ResPath.GetHolyHeavenlyDomainImg("a2_stsy_zhanyyy16")
	if owner_camp then
		local camp_cfg = HolyHeavenlyDomainWGData.Instance:GetCampCfgByCamp(owner_camp)

		if not IsEmptyTable(camp_cfg) then
			city_owner_bundle, city_owner_asset = ResPath.GetHolyHeavenlyDomainImg("a2_stsy_zhanyyy" .. camp_cfg.camp_icon)
		end
	end

	self.node_list.img_camp.image:LoadSprite(city_owner_bundle, city_owner_asset, function ()
        self.node_list.img_camp.image:SetNativeSize()
    end)

	local attention_flag = HolyHeavenlyDomainWGData.Instance:GetCountryTipAttentionFlag(city_seq, self.select_boss_data.seq)
	self.node_list.btn_boss_focus_gou:CustomSetActive(attention_flag)

	self.node_list.desc_score_details.text.text = string.format(Language.HolyHeavenlyDomain.CaptureTired, city_cfg.capture_tired)
	self.node_list.btn_zhaoji:CustomSetActive(not is_main_city)
	self.node_list.btn_target:CustomSetActive(not is_main_city)
	self.node_list.btn_guanzhu:CustomSetActive(not is_main_city)

	if IsEmptyTable(city_info) then
		self.node_list.desc_act_progress.text.text = Language.HolyHeavenlyDomain.CityCannotBeAttack
		self.node_list.score_no_data:CustomSetActive(true)
		self.score_list:SetDataList({})
		self.node_list.btn_city_occupy_reward_remind:CustomSetActive(false)
		return
	end

	local owner_usid = city_info.owner_usid
	local my_sid = RoleWGData.Instance:GetOriginalUSIDStr()
	-- local owner_country_id =  HolyHeavenlyDomainWGData.Instance:GetCountryIdByServerUsid(owner_usid) or -1
	local siege_info_list = city_info.server_siege_list

	local offender_value = 0  -- 攻
	local defender_value = 0  --守
	local total_value = 0
	local score_data_list = {}
	-- local is_my_city = owner_usid == my_sid

	if not IsEmptyTable(siege_info_list) then
		for k, v in pairs(siege_info_list) do
			if v > 0 then
				-- 获取攻击方得服ID
				local usid = HolyHeavenlyDomainWGData.Instance:GetCampServerIdByCampSeq(k)

				if nil ~= usid then
					if owner_usid == usid then
						defender_value = defender_value + v
					else
						offender_value = offender_value + v
					end
			
					local camp_cfg = HolyHeavenlyDomainWGData.Instance:GetCampCfgByCamp(k)
					table.insert(score_data_list, {is_atk = owner_usid ~= usid, value = v, city_seq = city_seq, camp_name = camp_cfg.camp_name})
				end

				total_value = total_value + v
			end
		end
	end

	table.sort(score_data_list, function (a, b)
		if not a.is_atk then
			return true
		end

		if a.is_atk and b.is_atk then
			return a.value > b.value
		end
	end)

	self.score_list:SetDataList(score_data_list)
	self.node_list.score_no_data:CustomSetActive(IsEmptyTable(score_data_list))
	
	if not is_main_city then
		local act_progress = 0

		if total_value > 0 then
			act_progress = offender_value / total_value * 100
			act_progress = act_progress > 100 and 100 or act_progress
		end

		self.node_list.desc_act_progress.text.text = string.format(Language.HolyHeavenlyDomain.AttackersOccupyProgress, string.format("%.2f", act_progress))
	end

	-- local camp_cfg = HolyHeavenlyDomainWGData.Instance:GetCampCfgByCamp(owner_country_id)
	-- local is_neutral = IsEmptyTable(camp_cfg)
	-- local OccupationSituation = is_neutral and Language.HolyHeavenlyDomain.Neutral or camp_cfg.camp_name
    -- self.node_list["desc_zhanlin"].text.text = string.format(Language.HolyHeavenlyDomain.OccupationSituation, OccupationSituation)
    -- self.node_list["desc_btn_zhaoji"].text.text = Language.HolyHeavenlyDomain.InitiateAssembly
	-- local is_set_target = false
    -- self.node_list["desc_btn_target"].text.text = is_set_target and Language.HolyHeavenlyDomain.BtnHasSetTarget or Language.HolyHeavenlyDomain.BtnSetTarget

    -- self.node_list["desc_score_details"].text.text = Language.HolyHeavenlyDomain.PointsDetails
	-- local defend_value = 0
	-- local attack_value = 0

	-- if total_value > 0 then
	-- 	defend_value = (defender_value / total_value) * 100
	-- 	defend_value = defend_value > 100 and 100 or defend_value
		
	-- 	attack_value = (offender_value / total_value) * 100
	-- 	attack_value = attack_value > 100 and 100 or attack_value
	-- end

    -- self.node_list["desc_attack_value"].text.text = string.format(Language.HolyHeavenlyDomain.DefensiveRatio, defend_value)
    -- self.node_list["desc_defend_value"].text.text = string.format(Language.HolyHeavenlyDomain.AttackRatio, attack_value)

	-- local slider_value = defender_value / total_value
	-- if offender_value == 0 or (offender_value == 0 and defender_value == 0) then
	-- 	slider_value = 1
	-- end

	-- self.node_list.slider_attack.slider.value = slider_value

	local capture_remind = false
	if my_sid == owner_usid then
		capture_remind = HolyHeavenlyDomainWGData.Instance:GetCaptureRewardFlagBySeq(city_seq) == 0
	end

	self.node_list.btn_city_occupy_reward_remind:CustomSetActive(capture_remind)
end

function HolyHeavenlyDomainCountryTip:OnSelectBossHandler(item)
	if nil == item or IsEmptyTable(item.data) then
		return
	end

	local data = item.data
	self.select_boss_data = data
	self.select_boss_index = item.index + 1
	local score = data.kill_score

	local attention_flag = HolyHeavenlyDomainWGData.Instance:GetCountryTipAttentionFlag(data.city_seq, data.seq)
	self.node_list.btn_boss_focus_gou:CustomSetActive(attention_flag)

	local privilege_add_per, peivilege_add_per_str = HolyHeavenlyDomainWGData.Instance:GetPrivilegeAddValue()
	local _, _, _, tired_reduce_value = HolyHeavenlyDomainWGData.Instance:GetMapTopInfo()
	local siege_value = score * (tired_reduce_value + privilege_add_per)
	local war_value = score -- * (1 + privilege_add_per)

	self.reward_item_list:SetDataList(data.boss_reward_item)

    self.node_list["desc_siege_value"].text.text = string.format(Language.HolyHeavenlyDomain.SiegeTipValue, siege_value)
    self.node_list["desc_war_value"].text.text = string.format(Language.HolyHeavenlyDomain.WarTipValue, war_value)
end

function HolyHeavenlyDomainCountryTip:OnClickZhaoJi()
	if self.show_data == nil then
		return
	end

	local city_cfg = HolyHeavenlyDomainWGData.Instance:GetCityCfgByCitySeq(self.show_data.seq)
	if IsEmptyTable(city_cfg) then
		return
	end

	local city_seq = city_cfg.seq
	local is_zhaoji_target = HolyHeavenlyDomainWGData.Instance:GetAssembleCityFlagBySeq(city_seq)

	if is_zhaoji_target then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HolyHeavenlyDomain.IsAssembleCityNow)
	else
		local id_cd_time, _, time = HolyHeavenlyDomainWGData.Instance:IsAssembleCityCD()
		if not id_cd_time then
			HolyHeavenlyDomainWGCtrl.Instance:OnDivineDomainOperate(CROSS_DIVINE_DOMAIN_OPERATE_TYPE.ASSEMBLE_CITY, city_seq)
			self:Close()
		else
			local time_str = TimeUtil.FormatTimeLanguage2(time)
			local reason = string.format(Language.HolyHeavenlyDomain.AssembleCityCd, time_str)
			SysMsgWGCtrl.Instance:ErrorRemind(reason)
		end
	end
end

function HolyHeavenlyDomainCountryTip:OnClickTarget()
	local city_cfg = HolyHeavenlyDomainWGData.Instance:GetCityCfgByCitySeq(self.show_data.seq)
	if IsEmptyTable(city_cfg) then
		return
	end

	local city_seq = city_cfg.seq
	local can_set_target, reason = HolyHeavenlyDomainWGData.Instance:IsCityCanSetTarget(city_seq)
	if can_set_target then
		local is_convene = HolyHeavenlyDomainWGData.Instance:IsConveneCity(city_seq)
		if is_convene then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HolyHeavenlyDomain.IsConveneCityNow)
		else
			HolyHeavenlyDomainWGCtrl.Instance:OnDivineDomainOperate(CROSS_DIVINE_DOMAIN_OPERATE_TYPE.CONVENE_CITY, city_seq)
			

			self:Close()
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(reason)
	end
end

function HolyHeavenlyDomainCountryTip:OnClickWarReward()
	if IsEmptyTable(self.select_boss_data) then
		return
	end

	local data = self.select_boss_data
	local base_score = data.kill_score
	local _, peivilege_add_per_str = HolyHeavenlyDomainWGData.Instance:GetPrivilegeAddValue()
	local content = string.format(Language.HolyHeavenlyDomain.WarRewardContent, base_score, peivilege_add_per_str)
    RuleTip.Instance:SetContent(content, Language.HolyHeavenlyDomain.WarRewardTitle)
end

function HolyHeavenlyDomainCountryTip:OnClickSiege()
	if IsEmptyTable(self.select_boss_data) then
		return
	end

	local data = self.select_boss_data
	local base_score = data.kill_score
	local _, peivilege_add_per_str = HolyHeavenlyDomainWGData.Instance:GetPrivilegeAddValue()
	local _, _, _, _, _, _, tired_reduce_value_str= HolyHeavenlyDomainWGData.Instance:GetMapTopInfo()
	local content = string.format(Language.HolyHeavenlyDomain.SiegeContent, base_score, tired_reduce_value_str,peivilege_add_per_str)
	RuleTip.Instance:SetContent(content, Language.HolyHeavenlyDomain.SiegeTitle)
end

function HolyHeavenlyDomainCountryTip:OnClickCityOccupyReward()
	if self.show_data == nil then
		return
	end

	local city_cfg = HolyHeavenlyDomainWGData.Instance:GetCityCfgByCitySeq(self.show_data.seq)
	if IsEmptyTable(city_cfg) then
		return
	end

	local city_seq = city_cfg.seq
	local city_info = HolyHeavenlyDomainWGData.Instance:GetCityInfoBySeq(city_seq)
	if IsEmptyTable(city_info) then
		return
	end

	local owner_usid = city_info.owner_usid
	-- local my_sid = RoleWGData.Instance:GetOriginalUSIDStr()
	local my_sid = RoleWGData.Instance:GetOriginalUSIDStr()
	if my_sid == owner_usid then
		local can_get = HolyHeavenlyDomainWGData.Instance:GetCaptureRewardFlagBySeq(city_seq) == 0

		if can_get then
			HolyHeavenlyDomainWGCtrl.Instance:OnDivineDomainOperate(CROSS_DIVINE_DOMAIN_OPERATE_TYPE.FETCH_CAPTURE_REWARD, city_seq)
			local sort_data = SortDataByItemColor(city_cfg.capture_reward_item)
			TipWGCtrl.Instance:ShowGetReward(nil, sort_data)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HolyHeavenlyDomain.IsGetCaptureReward)
			local data_list =
			{
				view_type = RewardShowViewType.Normal,
				reward_item_list = city_cfg.capture_reward_item
			}
			RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HolyHeavenlyDomain.UnaccupidThisCity)
		local data_list =
		{
			view_type = RewardShowViewType.Normal,
			reward_item_list = city_cfg.capture_reward_item
		}
		RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
	end
end

function HolyHeavenlyDomainCountryTip:OnClickGuanZhuHandler()
	if self.show_data == nil or self.select_boss_data == nil then
		return
	end

	local city_cfg = HolyHeavenlyDomainWGData.Instance:GetCityCfgByCitySeq(self.show_data.seq)
	if IsEmptyTable(city_cfg) then
		return
	end

	local is_main_city = city_cfg.type == 4
	local owen_server = 0
	if is_main_city then
		local camp = city_cfg.relegation_camp
		owen_server = HolyHeavenlyDomainWGData.Instance:GetCampServerIdByCampSeq(camp) or 0
	else
		local city_info = HolyHeavenlyDomainWGData.Instance:GetCityInfoBySeq(city_cfg.seq)
		owen_server = city_info and city_info.owner_usid or 0
	end

	local my_server_id = RoleWGData.Instance:GetOriginalUSIDStr()
	if my_server_id == owen_server then
		local city_seq = city_cfg.seq
		local attention_flag = HolyHeavenlyDomainWGData.Instance:GetCountryTipAttentionFlag(city_seq, self.select_boss_data.seq)
		local is_concern = attention_flag and 0 or 1
		HolyHeavenlyDomainWGCtrl.Instance:OnDivineDomainOperate(CROSS_DIVINE_DOMAIN_OPERATE_TYPE.CONCERN_CITY, city_seq, self.select_boss_data.seq, is_concern)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HolyHeavenlyDomain.CityConcernFail)
	end
end

function HolyHeavenlyDomainCountryTip:OnClickTiaoZhan()
	if self.show_data == nil then
		return
	end

	local city_cfg = HolyHeavenlyDomainWGData.Instance:GetCityCfgByCitySeq(self.show_data.seq)

	if IsEmptyTable(city_cfg) then
		return
	end

	-- 策划说基地不是自己的也不让进
	if city_cfg.type == 4 then
		local my_camp_seq = HolyHeavenlyDomainWGData.Instance:GetMyCampSeq()

		if my_camp_seq ~= city_cfg.relegation_camp then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HolyHeavenlyDomain.CityCannotBeAttack)
			return
		end
	end

	-- 休赛期
	local is_in_the_offseason = HolyHeavenlyDomainWGData.Instance:IsInTheOffSeason()
	if is_in_the_offseason then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HolyHeavenlyDomain.IsIntTheOffSeasonNoew)
		return
	end

	-- 时间
	local act_open, reason = HolyHeavenlyDomainWGData.Instance:IsInActDuration()
	if not act_open then
		SysMsgWGCtrl.Instance:ErrorRemind(reason)
		return
	end

	-- 未设置目标不给进
	local is_set_convene_city = HolyHeavenlyDomainWGData.Instance:IsSetConveneCity()
	if not is_set_convene_city then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HolyHeavenlyDomain.NotSetConveneCity)
		HolyHeavenlyDomainWGCtrl.Instance:OpenSetTargetView()
		self:Close()
		return
	end

	-- 中间两城池只能占领者能进
	if city_cfg.type == 3 then
		local city_info = HolyHeavenlyDomainWGData.Instance:GetCityInfoBySeq(city_cfg.seq)

		if IsEmptyTable(city_info) then
			return
		end

		local owner_usid = city_info.owner_usid
		local my_sid = RoleWGData.Instance:GetOriginalUSIDStr()

		if not owner_usid or owner_usid ~= my_sid then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HolyHeavenlyDomain.CanNotAtkSpecialCity)
			return
		end
	end

	CrossServerWGCtrl.Instance.SendCrossStartReq(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_DIVINE_DOMAIN, city_cfg.seq)
	HolyHeavenlyDomainWGData.Instance:SetEnterFbCache(self.select_boss_index, city_cfg.seq, self.select_boss_data)
end

---------------------------------HHDCountryTipBossItemRender------------------------------
HHDCountryTipBossItemRender = HHDCountryTipBossItemRender or BaseClass(BaseRender)

function HHDCountryTipBossItemRender:LoadCallBack()
	self.refresh_event = nil
end

function HHDCountryTipBossItemRender:__delete()
	if self.refresh_event then
		GlobalTimerQuest:CancelQuest(self.refresh_event)
		self.refresh_event = nil
    end
end

function HHDCountryTipBossItemRender:OnFlush()
	if not self.data then
		return
	end

	local boss_cfg = BossWGData.Instance:GetMonsterCfgByid(self.data.monster_id)
	self.node_list.boss_name.text.text = boss_cfg and boss_cfg.name or ""

	local bundle, asset = ResPath.GetHolyHeavenlyDomainImg("a2_ymgy_di" .. self.data.bg_type)
	self.node_list.bg.image:LoadSprite(bundle, asset, function ()
	end)

	local boss_bundle, boss_asset = ResPath.GetBossIcon("wrod_boss_" .. boss_cfg.small_icon)
	self.node_list.boss_icon.image:LoadSprite(boss_bundle, boss_asset, function ()
		self.node_list.boss_icon.image:SetNativeSize()
	end)

	-- local jieshu = boss_cfg.boss_jieshu or 0
	-- self.node_list.boss_order.text.text = string.format(Language.Common.Order, jieshu)

	self:RefreshRemainTime()
	if self.refresh_event then
		GlobalTimerQuest:CancelQuest(self.refresh_event)
		self.refresh_event = nil
	end

	self.refresh_event = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.RefreshRemainTime,self),0.5)
end

function HHDCountryTipBossItemRender:RefreshRemainTime()
	if not self.data then
		return
	end

	local boss_info = HolyHeavenlyDomainWGData.Instance:GetMonsterCfgByCitySeqAndBossSeq(self.data.city_seq, self.data.seq)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local next_refresh_time = boss_info and boss_info.next_refresh_time or 0
	local time_str = ""

	if next_refresh_time > 0 and (next_refresh_time - server_time > 1) then
		time_str =  ToColorStr(TimeUtil.FormatSecond(next_refresh_time - server_time, 3), COLOR3B.RED)
	else
		if self.refresh_event then
			GlobalTimerQuest:CancelQuest(self.refresh_event)
			self.refresh_event = nil
		end

		time_str = ToColorStr(Language.CountryMap.CountryMapBossActiveState, COLOR3B.GREEN)
	end

	self.node_list.boss_reflush_time.text.text = time_str
end

function HHDCountryTipBossItemRender:OnSelectChange(is_select)
	self.node_list.select:CustomSetActive(is_select)
end

-----------------------------HHDCountryTipScoreItemRender----------------------------
HHDCountryTipScoreItemRender = HHDCountryTipScoreItemRender or BaseClass(BaseRender)

function HHDCountryTipScoreItemRender:OnFlush()
	if nil == self.data then
		return
	end

	self.node_list.atk_sige:CustomSetActive(self.data.is_atk)
	self.node_list.defen_sige:CustomSetActive(not self.data.is_atk)
	self.node_list.desc_sige_value.text.text = self.data.value
	self.node_list.desc_sige_name.text.text = self.data.camp_name
end