PierreDirectPurchaseView = PierreDirectPurchaseView or BaseClass(SafeBaseView)
local VIEWPORT_COUNT = 3
local MODEL_MAX_NUM = 2
local time_key = "pierre_direct_purchase_time"

function PierreDirectPurchaseView:__init()
	self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:SetMaskBg(false, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

	-- self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/pierre_direct_purchase_ui_prefab", "layout_pierre_direct_purchase_view")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
	self:SetTabShowUIScene(0, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.PIERRE_DIRECT_PURCHASE})
end

function PierreDirectPurchaseView:__delete()
end

function PierreDirectPurchaseView:OpenCallBack()
	if PierreDirectPurchaseWGData.Instance:GetIsNoSendProtocal() then
		local opera_type = PIERRE_DIRECT_PURCHASE_OP_TYPE.GET_ALL
		PierreDirectPurchaseWGCtrl.Instance:SendRequest(opera_type)
	end
end

function PierreDirectPurchaseView:CloseCallBack()

end

function PierreDirectPurchaseView:ReleaseCallBack()
	if self.big_reward_list then
		for k, v in pairs(self.big_reward_list) do
			v:DeleteMe()
		end
		self.big_reward_list = nil
	end

	if self.single_reward then
		self.single_reward:DeleteMe()
		self.single_reward = nil
	end

	if self.bt_alert_window ~= nil then
		self.bt_alert_window:DeleteMe()
		self.bt_alert_window = nil
	end
	if self.left_select_list then
		self.left_select_list:DeleteMe()
		self.left_select_list = nil
	end

	if self.alert then
		self.alert:DeleteMe()
		self.alert = nil
	end

	self:CleanBoxTween()
	self:CleanTween()
	self:CleanCDTime()
	self.btn_select_index = nil
	self.btn_select_type = -1
	self.btn_jump_index = nil
	self.show_data = nil
	self.open_view_flag = nil
	self.jump_to_type = nil
end

function PierreDirectPurchaseView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.PierreDirectPurchase.TitleName

	-- local bundle, asset = ResPath.GetRawImagesJPG("a3_kfms_bj")
	-- if self.node_list.RawImage_tongyong then
	-- 	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
	-- 		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	-- 	end)
	-- end

	self.open_view_flag = false
	self.btn_select_index = -1
	self.btn_select_type = -1
	self.btn_jump_type = 1

	self.left_select_list = AsyncListView.New(PierreDirectPurchaseBtnCell, self.node_list.ph_btn_select_list)
	self.left_select_list:SetSelectCallBack(BindTool.Bind1(self.OnClickActBtn, self))

	-- self.big_reward_list = {}
	-- local node_num = self.node_list.big_reward_list.transform.childCount
	-- for i = 1, node_num do
	-- 	self.big_reward_list[i] = PierreDirectPurchaseRewardCell.New(self.node_list.big_reward_list:FindObj("reward_" .. i))
	-- 	self.big_reward_list[i]:SetPrent(self)
	-- end

	self.single_reward = PierreDirectPurchaseRewardCell.New(self.node_list.single_reward)
	self.single_reward:SetPrent(self)

	XUI.AddClickEventListener(self.node_list.btn_get, BindTool.Bind(self.OnClickDaliyReward, self))
	XUI.AddClickEventListener(self.node_list.btn_pick_buy, BindTool.Bind(self.OnClickPickBuy, self))
	--XUI.AddClickEventListener(self.node_list.btn_tips, BindTool.Bind(self.OnClickTips, self))
end

function PierreDirectPurchaseView:ShowIndexCallBack()
	self.open_view_flag = true
end

function PierreDirectPurchaseView:ChangeJumpType()
	local jump_type
	for k, v in ipairs(self.show_data) do
		if jump_type == nil then
			jump_type = v.buy_type
		end

		if self.btn_select_type == -1 then
			local can_buy = PierreDirectPurchaseWGData.Instance:GetBuyTypeCanBuy(v.buy_type)
			if can_buy then
				jump_type = v.buy_type
				break
			end
		elseif self.btn_select_type == v.buy_type then
			jump_type = v.buy_type
		end
	end
	self.btn_jump_type = jump_type
end

function PierreDirectPurchaseView:GetShowData()
	return self.show_data and self.show_data[self.btn_select_index]
end

function PierreDirectPurchaseView:OnFlush(param_t)
	self.show_data = PierreDirectPurchaseWGData.Instance:GetShowList()
	if IsEmptyTable(self.show_data) then
		self:Close()
		return
	end

	for k, v in pairs(param_t) do
		if k == "all" then
			if v.open_param then
				self.jump_to_type = tonumber(v.open_param)
				self:FlushBtnList(true)
			elseif self.open_view_flag then
				self.open_view_flag = false
				self:FlushBtnList()
			else
				self:FlushBtnList()
			end
		elseif k == "flag_change" then
			self:FlushBtnList()
		elseif k == "data_change" then
			self:FlushBtnList(true)
		elseif k == "jump_index" and type(v[1]) == "number" then
			self.jump_to_type = v[1]
			self:FlushBtnList(true)
		end
	end
end

-- 刷新滑动列表
function PierreDirectPurchaseView:FlushBtnList(flush_jump_type)
	if self.left_select_list == nil or self.show_data == nil then
		return
	end

	local data_num = #self.show_data
	local act_num = self.left_select_list:GetListViewNumbers()

	local diff_num = data_num ~= act_num

	if self.jump_to_type then
		self.btn_jump_type = self.jump_to_type
		self.jump_to_type = nil
	elseif flush_jump_type or diff_num then
		self:ChangeJumpType()
	end

	self.left_select_list:SetDataList(self.show_data)
	--print_error("【----1-----】：", #self.show_data, self.btn_jump_type)
	if self.btn_jump_type then
		self:AutoJumpToBtn()
	else
		self:FlushView()
	end
end

-- 自动跳转
function PierreDirectPurchaseView:AutoJumpToBtn()
	if self.left_select_list == nil or self.show_data == nil then
		return
	end

	local jump_index = 1
	local left_data_list = self.left_select_list:GetDataList()
	--local btn_item_list = self.cambered_list:GetRenderList()
	for k, item_cell in ipairs(left_data_list) do
		local item_data = self.show_data[k]
		if item_data and item_data.buy_type == self.btn_jump_type then
			jump_index = k
		end
	end

	-- print_error("【----3-----】：", #self.show_data, self.btn_jump_type, jump_cell == nil)
	self.btn_jump_type = nil
	self.left_select_list:JumpToIndex(jump_index, 3)
end

function PierreDirectPurchaseView:CleanBoxTween()
	if self.box_tween ~= nil then
		self.box_tween:Kill()
		self.box_tween = nil
	end
end

function PierreDirectPurchaseView:FlushRightView(is_click)
	local show_data = self:GetShowData()
	-- print_error("【-----FlushRightView----】：", show_data)
	if IsEmptyTable(show_data) then
		return
	end

	local buy_type = show_data.buy_type
	local pdp_data = PierreDirectPurchaseWGData.Instance

	local asset_name, bundle_name = ResPath.GetRawImagesPNG(show_data.title_image)
	self.node_list.title_img2.raw_image:LoadSprite(asset_name, bundle_name, function()
		self.node_list.title_img2.raw_image:SetNativeSize()
	end)

	self.node_list.rebate_value.text.text = string.format(Language.PierreDirectPurchase.RebateDesc, show_data.rebate_value)

	-- 每日奖励
	local is_get_daliy_reward = pdp_data:GetIsGetDaliyReward(buy_type)
	self.node_list.free_item:SetActive(not is_get_daliy_reward)
	self.node_list.free_item_red.image.enabled = not is_get_daliy_reward
	if is_get_daliy_reward then
		self:CleanBoxTween()
		self.node_list.btn_get.transform.localRotation = Quaternion.Euler(0, 0, 0)
	else
		if self.box_tween == nil then
			self.box_tween = DG.Tweening.DOTween.Sequence()
			UITween.ShakeAnimi(self.node_list.btn_get.transform, self.box_tween, 2)
		end
	end

	-- 商品
	local no_buy = pdp_data:GetBuyTypeAllNotBuy(buy_type)
	local show_special, buy_data = pdp_data:GetShowRewardByType(buy_type)
	self.node_list.big_reward_list:SetActive(not show_special and #buy_data ~= 1)
	self.node_list.single_reward:SetActive(show_special or #buy_data == 1)
	local is_show = no_buy and show_data.is_buy_all

	if show_special then
		self.single_reward:SetModelSpecial(true)
		self.single_reward:SetData(buy_data[1])
		self.single_reward:ShowPower(not is_show)
	elseif #buy_data == 1 then
		self.single_reward:SetModelSpecial(false)
		self.single_reward:SetData(buy_data[1])
		self.single_reward:ShowPower(not is_show)
	-- else
	-- 	for k, v in ipairs(self.big_reward_list) do
	-- 		local temp_data = buy_data[k]
	-- 		v:ChangePos(#buy_data <= 1, temp_data)
	-- 		v:ShowPower(not is_show)
	-- 		v:SetData(temp_data)
	-- 	end
	end

	-- 打包
	self.node_list.pick_buy_text.text.text = RoleWGData.GetPayMoneyStr(show_data.buy_all_price, show_data.rmb_type,
		show_data.index)
	local shield_high_charge = RoleWGData.GetIsShieldHighCharge()
	self.node_list.btn_pick_buy:SetActive(no_buy and show_data.is_buy_all and not shield_high_charge)

	self.ui_scene_change_config_index = buy_data[1].ui_scene_config_index
    Scene.Instance:ChangeUISceneController(UI_SCENE_TYPE.DEFAULT, UI_SCENE_CONFIG_INDEX.PIERRE_DIRECT_PURCHASE, buy_data[1].ui_scene_config_index)
end

function PierreDirectPurchaseView:CleanCDTime()
	if CountDownManager.Instance:HasCountDown(time_key) then
		CountDownManager.Instance:RemoveCountDown(time_key)
	end
end

function PierreDirectPurchaseView:FlushRestTime()
	local show_data = self:GetShowData()
	if IsEmptyTable(show_data) then
		return
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local close_timestamp = PierreDirectPurchaseWGData.Instance:GetBuyTypeTimestamp(show_data.buy_type)
	local res_time = close_timestamp - server_time

	self:CleanCDTime()
	if res_time > 0 then
		self:PanelFlushTime(TimeUtil.FormatSecondDHM2(res_time), res_time > 86400)
		CountDownManager.Instance:AddCountDown(time_key, BindTool.Bind(self.UpdateRestTime, self),
			nil, nil, res_time, 1)
	else
		self:PanelFlushTime("00:00", false)
	end
end

function PierreDirectPurchaseView:UpdateRestTime(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self:PanelFlushTime(TimeUtil.FormatSecondDHM2(valid_time), valid_time >= 86400)
	else
		self:PanelFlushTime("00:00", false)
		self:CleanZhanLingCDTime()
	end
end

function PierreDirectPurchaseView:PanelFlushTime(str, is_green)
	-- if self.node_list.rest_time then
	-- 	self.node_list.rest_time.text.text = ToColorStr(str, is_green and COLOR3B.D_GREEN or COLOR3B.D_RED)
	-- end
end

function PierreDirectPurchaseView:FlushView(is_click)
	self:FlushRightView(is_click)

	-- if is_click then
	-- 	self:FlushRestTime()
	-- end
end

function PierreDirectPurchaseView:CleanTween()
	if self.tween_1 then
		self.tween_1:Kill()
		self.tween_1 = nil
	end
end

-- 点击item回调
function PierreDirectPurchaseView:OnClickActBtn(item_cell, index)
	if item_cell == nil then
		return
	end

	local select_index = item_cell:GetIndex()
	local select_data = item_cell:GetData()

	if select_data == nil then
		return
	end

	--print_error("【-----点击----】：", item_cell:GetIndex(), self.btn_select_index, select_data.buy_type, self.btn_select_type, not index)
	self.btn_select_index = select_index
	if self.btn_select_type == select_data.buy_type then
		return
	end

	self.btn_select_type = select_data.buy_type
	self:FlushView(true)
end

-- 每日奖励
function PierreDirectPurchaseView:OnClickDaliyReward()
	-- if true then
	-- 	PierreDirectPurchaseWGData.Instance:SetTest()
	-- 	self:Flush(nil, "data_change", {is_diff = true})
	-- 	return

	-- 	local data = {result = 1, param1 = 1, param2 = 0}
	-- 	PierreDirectPurchaseWGCtrl.Instance:OnBuyCallBack(data)
	-- end

	local show_data = self:GetShowData()
	if show_data == nil then
		return
	end

	local is_get_daliy_reward = PierreDirectPurchaseWGData.Instance:GetIsGetDaliyReward(show_data.buy_type)
	if is_get_daliy_reward then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.PierreDirectPurchase.DailyRewardTips)
	else
		local opera_type = PIERRE_DIRECT_PURCHASE_OP_TYPE.GET_DAILY
		PierreDirectPurchaseWGCtrl.Instance:SendRequest(opera_type)
	end
end

-- 打包购买
function PierreDirectPurchaseView:OnClickPickBuy()
	local show_data = self:GetShowData()
	if show_data == nil then
		return
	end

	local buy_type = show_data.buy_type
	local pdp_data = PierreDirectPurchaseWGData.Instance
	local buy_data = pdp_data:GetDirectPurchaseItemInfoByType(buy_type)
	if not buy_data then
		return
	end

	for k, v in ipairs(buy_data) do
		if not pdp_data:GetBuyTypeSeqCanBuy(buy_type, v.seq) then
			return
		end
	end

	-- SysMsgWGCtrl.SendGmCommand("addrmb", string.format("%s %s %s", show_data.rmb_type, show_data.buy_all_price, show_data.buy_type))
	RechargeWGCtrl.Instance:Recharge(show_data.buy_all_price, show_data.rmb_type, show_data.index)
end

function PierreDirectPurchaseView:OpenAlertView(content_str, ok_fun)
	local show_data = self:GetShowData()
	if show_data == nil then
		return
	end

	-- if nil == self.bt_alert_window then
	-- 	self.bt_alert_window = PierreDirectPurchaseAlert.New(nil, nil, nil, nil, true)
	-- 	self.bt_alert_window:SetCheckBoxDefaultSelect(false)
	-- 	self.bt_alert_window:SetCancelString(Language.PierreDirectPurchase.AlertCancelBtnText)
	-- end

	-- 商品
	local pdp_data = PierreDirectPurchaseWGData.Instance
	local no_buy = pdp_data:GetBuyTypeAllNotBuy(show_data.buy_type)

	if no_buy and show_data.is_buy_all then
		content_str = string.format(Language.PierreDirectPurchase.BuyTips2, content_str)
	end

	-- 打包
	self.node_list.pick_buy_text.text.text = RoleWGData.GetPayMoneyStr(show_data.buy_all_price, show_data.rmb_type,
		show_data.index)
	self.node_list.btn_pick_buy:SetActive(no_buy and show_data.is_buy_all)

	-- local is_buy_all = Language.PierreDirectPurchase.BuyTips2 or ""
	-- self.bt_alert_window:SetLableString(content_str)
	-- self.bt_alert_window:SetOkFunc(ok_fun)
	-- self.bt_alert_window:Open()

	if self.alert == nil then
		self.alert = Alert.New()
	end
	self.alert:SetLableString(content_str)

	self.alert:SetOkFunc(ok_fun)
	self.alert:SetShowCheckBox(true)
	self.alert:SetCheckBoxDefaultSelect(false)
	-- self.alert:SetCheckBoxText(Language.ShenShou.CheckBoxText)
	self.alert:SetCancelString(Language.PierreDirectPurchase.AlertCancelBtnText)
	self.alert:Open()
end

function PierreDirectPurchaseView:OnClickTips()
	local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.PierreDirectPurchase.TipsTitle)
	rule_tip:SetContent(Language.PierreDirectPurchase.TipsContent, nil, nil, nil, true)
end

--------------------------------------------------------------------------------
PierreDirectPurchaseBtnCell = PierreDirectPurchaseBtnCell or BaseClass(BaseRender)
function PierreDirectPurchaseBtnCell:__init()
	self.click_callback = nil
end

function PierreDirectPurchaseBtnCell:__delete()
	self.click_callback = nil
end

function PierreDirectPurchaseBtnCell:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.cell_root, BindTool.Bind(self.OnClick, self))
end

function PierreDirectPurchaseBtnCell:ReleaseCallBack()
	self:CleanTimer()
end

-- 设置点击回调
function PierreDirectPurchaseBtnCell:SetClickCallback(click_callback)
	self.click_callback = click_callback
end

function PierreDirectPurchaseBtnCell:OnClick()
	if self.click_callback then
		self.click_callback()
	end
end

function PierreDirectPurchaseBtnCell:OnFlush()
	if not self.data then
		return
	end

	self.node_list.name.text.text = self.data.toggle_name
	self.node_list.name_hl.text.text = self.data.toggle_name
	
	-- self.node_list.advertisement.text.text = self.data.advertisement
	local show_special, buy_data = PierreDirectPurchaseWGData.Instance:GetShowRewardByType(self.data.buy_type)
	self.node_list.discount_text.text.text = string.format(Language.PierreDirectPurchase.DiscountDesc, buy_data[1].discount_num)

	self.show_data = PierreDirectPurchaseWGData.Instance:GetShowList()
	local img_bundle, img_asset = ResPath.GetKFMSUi(self.data.special_show_img)
	self.node_list.show_img.image:LoadSprite(img_bundle, img_asset, function()
		self.node_list.show_img.image:SetNativeSize()
	end)

	self.node_list.ylq_flag:SetActive(not self.data.is_can_buy)

	self:LoginTimeCountDown()
end

function PierreDirectPurchaseBtnCell:OnSelectChange(is_select)
	self.node_list.img_normal:SetActive(not is_select)
	self.node_list.img_selected:SetActive(is_select)
end

--活动时间倒计时
function PierreDirectPurchaseBtnCell:LoginTimeCountDown()
	local end_time = PierreDirectPurchaseWGData.Instance:GetItemEndTimeByType(self.data.buy_type)
	self.node_list.advertisement.text.text = string.format(Language.PierreDirectPurchase.CountDown, TimeUtil.FormatSecond2HMS(end_time))

	self:CleanTimer()
	if not self.purchase_cell_down and end_time > 0 then
		self.purchase_cell_down = CountDown.Instance:AddCountDown(end_time, 1,
			-- 回调方法
			function(elapse_time, total_time)
				local valid_time = total_time - elapse_time
				self.node_list.advertisement.text.text = string.format(Language.PierreDirectPurchase.CountDown, TimeUtil.FormatSecond2HMS(valid_time))
			end,
			-- 倒计时完成回调方法
			function()
				self.node_list.advertisement.text.text = ""
			end
		)
	end
end

function PierreDirectPurchaseBtnCell:CleanTimer()
	if self.purchase_cell_down and CountDown.Instance:HasCountDown(self.purchase_cell_down) then
		CountDown.Instance:RemoveCountDown(self.purchase_cell_down)
		self.purchase_cell_down = nil
	end
end

--------------------------------------------------------------------------------
PierreDirectPurchaseRewardCell = PierreDirectPurchaseRewardCell or BaseClass(BaseRender)
function PierreDirectPurchaseRewardCell:__init()
	self.is_show_power = true
	self.model_special = false
end

function PierreDirectPurchaseRewardCell:LoadCallBack()
	self.cur_show_index = 1

	XUI.AddClickEventListener(self.node_list["btn_left"], BindTool.Bind(self.OnClickModelSwitch, self, true))
	XUI.AddClickEventListener(self.node_list["btn_right"], BindTool.Bind(self.OnClickModelSwitch, self, false))
	-- self.fixed_item_list = {}
	-- local node_num = self.node_list.item_list.transform.childCount
	-- for i = 1, node_num do
	-- 	local node = self.node_list.item_list:FindObj("item" .. i)
	-- 	self.fixed_item_list[i] = {}
	-- 	self.fixed_item_list[i].node = node
	-- 	self.fixed_item_list[i].cell = ItemCell.New(node)
	-- end

	if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(false)
    end

	if not self.special_reward_list then
        self.special_reward_list = AsyncListView.New(PierreDirectPurchaseSpecialRewardCell, self.node_list.special_reward_list)
        self.special_reward_list:SetStartZeroIndex(false)
    end

	XUI.AddClickEventListener(self.node_list.btn_buy, BindTool.Bind(self.OnClickBuy, self))
end

function PierreDirectPurchaseRewardCell:InitModel()
	if self.model_display1 == nil then
		self.model_display1 = OperationActRender.New(self.node_list.display_model1)
		self.model_display1:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
		-- self.parent:AddUiRoleModel(self.model_display1, 0)
	end

	if self.role_model1 == nil then
		self.role_model1 = RoleModel.New()
		local display_data = {
			parent_node = self.node_list.role_model1,
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = false,
		}

		self.role_model1:SetRenderTexUI3DModel(display_data)
		-- local role_vo = GameVoManager.Instance:GetMainRoleVo()
		-- local special_status_table = {ignore_wing = true, ignore_halo = true, ignore_fazhen = true, ignore_mantle = true, ignore_tail = true, ignore_jianzhen = true}
		-- self.role_model1:SetModelResInfo(role_vo, special_status_table)
		
		-- self.role_model1:SetUISceneModel(self.node_list["role_model1"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		-- self.parent:AddUiRoleModel(self.role_model1, 0)
	end

	if self.model_display2 == nil and self.node_list.display_root2 then
		self.model_display2 = OperationActRender.New(self.node_list.display_model2)
		self.model_display2:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
		-- self.parent:AddUiRoleModel(self.model_display2, 0)
	end

	if self.role_model2 == nil then
		self.role_model2 = RoleModel.New()
		local display_data = {
			parent_node = self.node_list.role_model2,
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = false,
		}

		self.role_model2:SetRenderTexUI3DModel(display_data)
		-- local role_vo = GameVoManager.Instance:GetMainRoleVo()
		-- local special_status_table = {ignore_wing = true, ignore_halo = true, ignore_fazhen = true, ignore_mantle = true, ignore_tail = true, ignore_jianzhen = true}
		-- self.role_model2:SetModelResInfo(role_vo, special_status_table)

		-- self.role_model2:SetUISceneModel(self.node_list["role_model2"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		-- self.parent:AddUiRoleModel(self.role_model2, 0)
	end
end

function PierreDirectPurchaseRewardCell:StartQuest()
    self:ModelCancelQuest()
	if not self.notice_timer_quest then
		self.notice_timer_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.OnFlushModelSwitch, self),
			5)
	end
end

function PierreDirectPurchaseRewardCell:ModelCancelQuest()
	if self.notice_timer_quest then
		GlobalTimerQuest:CancelQuest(self.notice_timer_quest)
		self.notice_timer_quest = nil
	end
end

function PierreDirectPurchaseRewardCell:OnFlushModelSwitch()
	self.cur_show_index = self.cur_show_index < MODEL_MAX_NUM and self.cur_show_index + 1 or 1
	self:FlushShowModel()
	self:FlushModel(self.cur_show_index)
end

function PierreDirectPurchaseRewardCell:OnClickModelSwitch(is_left)
	self.cur_show_index = is_left and math.max(self.cur_show_index - 1, 1) or math.min(self.cur_show_index + 1, MODEL_MAX_NUM)
	self:FlushShowModel()
	self:StartQuest()
	self:FlushModel(self.cur_show_index)
end

function PierreDirectPurchaseRewardCell:FlushShowModel()
	self.node_list["display_root1"]:SetActive(1 == self.cur_show_index)
	self.node_list["display_root2"]:SetActive(2 == self.cur_show_index)

	self.node_list["btn_left"]:SetActive(1 ~= self.cur_show_index)
	self.node_list["btn_right"]:SetActive(2 ~= self.cur_show_index)

	for i = 1, MODEL_MAX_NUM do
		self.node_list["normal_hole_img" .. i]:SetActive(i ~= self.cur_show_index)
		self.node_list["select_hole_img" .. i]:SetActive(i == self.cur_show_index)
	end
end

function PierreDirectPurchaseRewardCell:__delete()
	if self.model_display1 then
		self.model_display1:DeleteMe()
		self.model_display1 = nil
	end

	if self.model_display2 then
		self.model_display2:DeleteMe()
		self.model_display2 = nil
	end

	if self.role_model1 then
		self.role_model1:DeleteMe()
		self.role_model1 = nil
	end

	if self.role_model2 then
		self.role_model2:DeleteMe()
		self.role_model2 = nil
	end
	-- if self.fixed_item_list ~= nil then
	-- 	for k, v in pairs(self.fixed_item_list) do
	-- 		v.cell:DeleteMe()
	-- 	end
	-- 	self.fixed_item_list = nil
	-- end

	if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end

	if self.special_reward_list then
        self.special_reward_list:DeleteMe()
        self.special_reward_list = nil
    end

	self:ModelCancelQuest()

	self.parent = nil
	self.is_show_power = true
	self.model_special = false
	self.cur_show_index = nil
end

function PierreDirectPurchaseRewardCell:SetPrent(parent)
	self.parent = parent
	self:InitModel()
end

function PierreDirectPurchaseRewardCell:SetModelSpecial(bool)
	self.model_special = bool
end

-- function PierreDirectPurchaseRewardCell:ChangePos(is_single, data)
-- 	if not self.node_list.root or not self.node_list.reward then
-- 		return
-- 	end

-- 	if not data or data.seq ~= 1 then
-- 		return
-- 	end

-- 	-- if is_single then
-- 	-- 	RectTransform.SetAnchoredPositionXY(self.node_list.reward_bg.rect, -376, -25)
-- 	-- else
-- 	-- 	RectTransform.SetAnchoredPositionXY(self.node_list.reward_bg.rect, -185, -25)
-- 	-- end
-- end

function PierreDirectPurchaseRewardCell:ShowPower(bool)
	self.is_show_power = bool
end

function PierreDirectPurchaseRewardCell:OnFlush()
	if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end

	self.view:SetActive(true)

	self.node_list.discount_text.text.text = string.format(Language.PierreDirectPurchase.DiscountDesc, self.data.discount_num)
	local zhigou_list_cfg = PierreDirectPurchaseWGData.Instance:GetZhigouListInfoByType(self.data.buy_type)
	--self.node_list.advertisement.text.text = zhigou_list_cfg.advertisement or ""

	-- 价格
	local price_str = RoleWGData.GetPayMoneyStr(self.data.rmb_price, self.data.rmb_type, self.data.index)
	self.node_list.btn_buy_text.text.text = price_str

	local old_price_str = RoleWGData.GetPayMoneyStr(self.data.old_price)
	self.node_list.old_price_text.text.text = string.format(Language.PierreDirectPurchase.OldPriceDesc, old_price_str)

	local can_buy = PierreDirectPurchaseWGData.Instance:GetBuyTypeSeqCanBuy(self.data.buy_type, self.data.seq)
	--self.node_list.btn_buy:SetActive(can_buy)
	self.node_list.can_buy_content:SetActive(can_buy)
	self.node_list.all_sell_img:SetActive(not can_buy)

	local gear_flag = ServerActivityWGData.Instance:GetRCGearFlag(1)
	self.node_list.buy_tip:SetActive(not gear_flag)

	local buy_count = PierreDirectPurchaseWGData.Instance:GetBuyCountBySeq(self.data.index)
	--self.node_list.quota_text:SetActive(can_buy)
	self.node_list.quota_text.text.text = string.format(Language.PierreDirectPurchase.BuyLimitDesc, buy_count,
		self.data.buy_count_limit)

	--local show_name = self.data.special_show_name
	local show_id = self.data.model_show_itemid
	-- local reward_list = self.data.buy_items

	-- 名字
	-- local name_str = ""
	-- if show_name ~= nil and show_name ~= "" then
	-- 	name_str = show_name
	-- elseif show_id ~= 0 and type(show_id) ~= "string" then
	-- 	local item_cfg = ItemWGData.Instance:GetItemConfig(show_id)
	-- 	name_str = item_cfg and item_cfg.name or name_str
	-- end

	--self.node_list.name_part:SetActive(name_str ~= "" and can_buy)
	--self.node_list.name.text.text = name_str

	-- if self.data.special_text and self.data.special_text ~= "" then
	-- 	self.node_list.special_text.text.text = self.data.special_text
	-- 	self.node_list.special_text:SetActive(true)
	-- else
	-- 	self.node_list.special_text:SetActive(false)
	-- end

	-- 奖励物品
	local show_reward_list = PierreDirectPurchaseWGData.Instance:GetBuyRewardList(self.data.buy_type, self.data.seq)

	local special_buy_items = SortTableKey(self.data.special_buy_items)
	local normal_buy_items = SortTableKey(self.data.normal_buy_items)
	-- for k, v in ipairs(self.fixed_item_list) do
	-- 	local data = show_reward_list[k]
	-- 	if data then
	-- 		v.node:SetActive(true)
	-- 		v.cell:SetData(data)
	-- 	else
	-- 		v.node:SetActive(false)
	-- 	end
	-- end

	self.reward_list:SetDataList(normal_buy_items)
	self.special_reward_list:SetDataList(special_buy_items)

	self.cur_show_index = 1
	self:FlushModel(1)

	if self.node_list.display_root2 then
		if self.model_special then
			self.node_list.many_model_content:SetActive(true)
			self.node_list.display_root2:SetActive(true)
			self.cur_show_index = 2
			self:FlushModel(2)
			self:FlushShowModel()
			self:StartQuest()
		else
			self.node_list.many_model_content:SetActive(false)
			self.node_list.display_root2:SetActive(false)
			self.node_list.display_root1:SetActive(true)
			self:ModelCancelQuest()
		end
	end
	-- 形象展示
	local display_data = {}
	display_data.should_ani = true
	if show_id ~= 0 and show_id ~= "" then
		local split_list = string.split(show_id, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = show_id
		end
	end

	-- 战力
	local capability, show_max_cap = 0, false
	local show_item_id = display_data.item_id
	local show_item_list = display_data.model_item_id_list
	if self.data.model_show_type == 1 then
		if show_item_id then
			if ItemWGData.GetIsXiaogGui(show_item_id) then
				_, capability = ItemShowWGData.Instance:GetShouHuAttrByData(show_item_id)
				-- elseif HiddenWeaponWGData.Instance:GetOnlyEquipCfgById(show_item_id) then
				-- 	capability = HiddenWeaponWGData.Instance:GetItemCapability(show_item_id)
			else
				local item_cfg = ItemWGData.Instance:GetItemConfig(show_item_id)
				if item_cfg then
					local need_get_sys, sys_type = ItemShowWGData.Instance:GetIsNeedSysAttr(show_item_id,
						item_cfg.sys_attr_cap_location)
					if sys_type == ITEMTIPS_SYSTEM.MOUNT or sys_type == ITEMTIPS_SYSTEM.LING_CHONG
						or sys_type == ITEMTIPS_SYSTEM.HUA_KUN or sys_type == ITEMTIPS_SYSTEM.TS_HUANHUA
						or sys_type == ITEMTIPS_SYSTEM.XIAN_WA then
						show_max_cap = true
						capability = ItemShowWGData.CalculateCapability(show_item_id, show_max_cap)
					else
						capability = ItemShowWGData.CalculateCapability(show_item_id)
					end
				end
			end
		elseif show_item_list then
			for k, v in pairs(show_item_list) do
				local item_cfg = ItemWGData.Instance:GetItemConfig(k)
				if item_cfg then
					local need_get_sys, sys_type = ItemShowWGData.Instance:GetIsNeedSysAttr(k,
						item_cfg.sys_attr_cap_location)
					if sys_type == ITEMTIPS_SYSTEM.MOUNT or sys_type == ITEMTIPS_SYSTEM.LING_CHONG
						or sys_type == ITEMTIPS_SYSTEM.HUA_KUN or sys_type == ITEMTIPS_SYSTEM.TS_HUANHUA
						or sys_type == ITEMTIPS_SYSTEM.XIAN_WA then
						show_max_cap = true
						capability = capability + ItemShowWGData.CalculateCapability(k, show_max_cap)
					else
						capability = capability + ItemShowWGData.CalculateCapability(k)
					end
				end
			end
		end
	end

	self.node_list.cap_value.text.text = capability
	self.node_list.max_cap_value.text.text = capability
	self.node_list.cap_part:SetActive(capability > 0)
	--self.node_list.capability_part:SetActive(capability > 0 and not show_max_cap)
	self.node_list.capability_part:SetActive(false)
	--self.node_list.max_capability_part:SetActive(capability > 0 and show_max_cap)
	self.node_list.max_capability_part:SetActive(self.data.is_hide_zhanli == 0)

	-- 特殊气泡
	if self.node_list["special_bubble"] then
		local is_show = false
		if self.data.buy_type == 1 and self.data.seq == 1 and PierreDirectPurchaseWGData.Instance:GetIsShowMainSpecialBubble() then
			is_show = true
			self.node_list["special_bubble_text"].text.text = Language.PierreDirectPurchase.SpecialAcTBtnDesc
		end

		self.node_list["special_bubble"]:CustomSetActive(is_show)
	end
end

function PierreDirectPurchaseRewardCell:FlushModel(index)
	index = index or 1
	local attr_key = index == 1 and "" or index
	local cur_role_model = self["role_model" .. index]
	local cur_display_model_node = self.node_list["display_model" .. index]
	local cur_role_model_node = self.node_list["role_model" .. index]

	local pos_x, pos_y, pos_z = 0, 0, 0
	local display_pos = self.data["display_pos" .. attr_key]
	if display_pos and display_pos ~= "" then
		local pos_list = string.split(display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
		pos_z = tonumber(pos_list[3]) or pos_z
	end

	local rot_x, rot_y, rot_z = 0, 0, 0
	local display_rotation = self.data["display_rotation" .. attr_key]
	if display_rotation and display_rotation ~= "" then
		local rot_list = string.split(display_rotation, "|")
		rot_x = tonumber(rot_list[1]) or rot_x
		rot_y = tonumber(rot_list[2]) or rot_y
		rot_z = tonumber(rot_list[3]) or rot_z
	end

	local scale = self.data["display_scale" .. attr_key]
	scale = (scale and scale ~= "" and scale > 0) and scale or 1

	local show_id = self.data["model_show_itemid" .. attr_key] or 0

	local model_type = self.data["model_type" .. attr_key]
	if not model_type or model_type == "" or model_type == 1 then
		-- 形象展示
		cur_display_model_node:SetActive(true)
		cur_role_model_node:SetActive(false)
		-- cur_role_model:ClearModel()

		local display_data = {}
		display_data.should_ani = true
		if show_id ~= 0 and show_id ~= "" then
			local split_list = string.split(show_id, "|")
			if #split_list > 1 then
				local list = {}
				for k, v in pairs(split_list) do
					list[tonumber(v)] = true
				end
				display_data.model_item_id_list = list
			else
				display_data.item_id = show_id
			end
		end

		if self.data["animation_effects" .. attr_key] and self.data["animation_effects" .. attr_key] == 1 then
			display_data.should_ani = true
		else
			display_data.should_ani = false
		end

		-- display_data.is_ui_scene_model = true
		display_data.hide_model_block = true
		display_data.can_drag = false
		display_data.bundle_name = self.data["model_bundle_name" .. attr_key]
		display_data.asset_name = self.data["model_asset_name" .. attr_key]
		display_data.image_effect_bundle = self.data["extra_effect_bundle_name" .. attr_key]
		display_data.image_effect_asset = self.data["extra_effect_asset_name" .. attr_key]
		local model_show_type = self.data["model_show_type" .. attr_key] or 1
		display_data.render_type = model_show_type - 1
		display_data.need_wp_tween = false
		--self.model_display_list[index]:SetData(display_data)

		display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
		display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
		display_data.model_adjust_root_local_scale = scale

		display_data.model_rt_type = ModelRTSCaleType.L
		
		if index == 1 then
			-- self.model_display2:Reset()
			self.model_display1:SetData(display_data)
		elseif index == 2 then
			-- self.model_display1:Reset()
			self.model_display2:SetData(display_data)
		end
	else
		cur_role_model:ClearModel()
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local special_status_table = {ignore_wing = true, ignore_halo = true, ignore_fazhen = true, ignore_mantle = true, ignore_tail = true, ignore_jianzhen = true}
		cur_role_model:SetModelResInfo(role_vo, special_status_table)

		-- for i = 1, 2 do
		-- 	self["model_display" .. index]:Reset()
		-- end
		cur_display_model_node:SetActive(false)
		cur_role_model_node:SetActive(true)

		cur_role_model:SetRTAdjustmentRootLocalRotation(rot_x, rot_y, rot_z)
		cur_role_model:SetRTAdjustmentRootLocalScale(scale, scale, scale)

		if model_type == 2 then
			local res_id, part_type = NewAppearanceWGData.Instance:GetFashionResByItemId(show_id)
			if part_type == SHIZHUANG_TYPE.HALO then
				cur_role_model:SetHaloResid(res_id)
			end
		else
			local target_data = {}
			local soul_ring_id = self.data["soul_ring_id" .. attr_key]
			if soul_ring_id and "" ~= soul_ring_id then
				local soul_ring_id_list = string.split(soul_ring_id, "|")
				for k, v in pairs(soul_ring_id_list) do
					local cfg = ShenShouWGData.Instance:GetShenShouCfg(tonumber(v))
					target_data[k - 1] = {soul_ring_effect = cfg.soul_ring_effect}
				end

				cur_role_model:SetTotalSoulRingResid(target_data, false, #soul_ring_id_list)
			end
		end

		RectTransform.SetAnchoredPositionXY(cur_role_model_node.rect, pos_x, pos_y)
	end
end

function PierreDirectPurchaseRewardCell:OnClickBuy()
	if IsEmptyTable(self.data) then
		return
	end

	local can_buy = PierreDirectPurchaseWGData.Instance:GetBuyTypeSeqCanBuy(self.data.buy_type, self.data.seq)
	if not can_buy then
		return
	end

	local content_str = self.data.special_show_name
	--local reward_list = self.data.buy_items
	-- local show_reward_list = PierreDirectPurchaseWGData.Instance:GetBuyRewardList(self.data.buy_type, self.data.seq)

	-- local reward_num = #show_reward_list
	-- for k, v in ipairs(show_reward_list) do
	-- 	local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
	-- 	if item_cfg then
	-- 		if k < reward_num then
	-- 			content_str = string.format("%s<color=%s>%s*%s</color>、", content_str, ITEM_COLOR[item_cfg.color],
	-- 				item_cfg.name, v.num)
	-- 		else
	-- 			content_str = string.format("%s<color=%s>%s*%s</color>", content_str, ITEM_COLOR[item_cfg.color],
	-- 				item_cfg.name, v.num)
	-- 		end
	-- 	end
	-- end

	local function ok_fun()
		-- SysMsgWGCtrl.SendGmCommand("addrmb", string.format("%s %s %s", self.data.rmb_type, self.data.rmb_price, self.data.buy_type))
		RechargeWGCtrl.Instance:Recharge(self.data.rmb_price, self.data.rmb_type, self.data.index)
	end

	if self.parent ~= nil then
		local price_str = RoleWGData.GetPayMoneyStr(self.data.rmb_price, self.data.rmb_type, self.data.index)
		content_str = string.format(Language.PierreDirectPurchase.BuyTips1, price_str, content_str)
		self.parent:OpenAlertView(content_str, ok_fun)
	end
end

--------------------------------------------------------------------------------
PierreDirectPurchaseSpecialRewardCell = PierreDirectPurchaseSpecialRewardCell or BaseClass(BaseRender)

function PierreDirectPurchaseSpecialRewardCell:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.item_cell)
end

function PierreDirectPurchaseSpecialRewardCell:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function PierreDirectPurchaseSpecialRewardCell:OnFlush()
	self.item_cell:SetData(self.data)
end
