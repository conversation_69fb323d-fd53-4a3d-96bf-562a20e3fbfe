DragonTrialSceneLogic = DragonTrialSceneLogic or BaseClass(CommonFbLogic)

function DragonTrialSceneLogic:__init()
	DragonTrialSceneLogic.Instance = self
	self.open_view = false
	self.is_pass = 1
end

function DragonTrialSceneLogic:__delete()
end

function DragonTrialSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	DragonTrialWGCtrl.Instance:OpenDragonTrialTaskView()

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		if DragonTrialWGData.Instance:GetCurrentPassSeq() > 0 then
			DragonTrialWGCtrl.Instance:SceneEnterCallback()
		end

		if Scene.Instance:GetSceneType() == SceneType.SCENE_TYPE_DRAGON_TRIALT_FB then
			MainuiWGCtrl.Instance:SetTaskContents(false)
			MainuiWGCtrl.Instance:SetOtherContents(true)
		end
	end)
end

function DragonTrialSceneLogic:CloseLoadingCallBack()

end

function DragonTrialSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)
    if old_scene_type ~= new_scene_type then
		FuBenPanelCountDown.Instance:CloseViewHandler()
		UiInstanceMgr.Instance:ColseFBStartDown()

		if self.start_down_event then
			GlobalEventSystem:UnBind(self.start_down_event)
			self.start_down_event = nil
		end

		MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
			if Scene.Instance:GetSceneType() == SceneType.SCENE_TYPE_DRAGON_TRIALT_FB then
				MainuiWGCtrl.Instance:SetTaskContents(true)
                MainuiWGCtrl.Instance:SetOtherContents(false)
			end
		end)

		DragonTrialWGCtrl.Instance:CloseDragonTrialTaskView()
	end
end

function DragonTrialSceneLogic:OpenFbSceneCd()

end
