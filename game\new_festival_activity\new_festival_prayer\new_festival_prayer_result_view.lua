NewFestivalPrayerResultView = NewFestivalPrayerResultView or BaseClass(SafeBaseView)

function NewFestivalPrayerResultView:__init(view_name)
	self.view_layer = UiLayer.Pop
	self.full_screen = false
	self:AddViewResource(0, "uis/view/new_festival_activity_ui_prefab", "layout_prayer_result")
	self:SetMaskBg(true, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
end

function NewFestivalPrayerResultView:LoadCallBack()
    if not self.noraml_reward_list then
        self.noraml_reward_list = AsyncListView.New(ItemCell, self.node_list.noraml_reward_list)
    end

    if not self.special_reward_list then
        self.special_reward_list = AsyncListView.New(ItemCell, self.node_list.special_reward_list)
    end

    XUI.AddClickEventListener(self.node_list.btn_sure, BindTool.Bind(self.OnClickSureBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_buy, BindTool.Bind(self.OnClickBuyBtn, self))
end

function NewFestivalPrayerResultView:ReleaseCallBack()
	if self.noraml_reward_list then
		self.noraml_reward_list:DeleteMe()
		self.noraml_reward_list = nil
	end

	if self.special_reward_list then
		self.special_reward_list:DeleteMe()
		self.special_reward_list = nil
	end

    self.show_reward_list = nil
    self.spe_show_reward_list = nil
end

function NewFestivalPrayerResultView:SetDataAndOpen(show_reward_list, spe_show_reward_list)
	self.show_reward_list = show_reward_list
	self.spe_show_reward_list = spe_show_reward_list
    self:Open()
end

function NewFestivalPrayerResultView:OnFlush(param_t)
    self:FlushPanelInfo()
end

function NewFestivalPrayerResultView:FlushPanelInfo()
    self:FlushActiveStatus()
	self:FlushRewardList()
end

function NewFestivalPrayerResultView:FlushActiveStatus()
    local is_buy_advanced = NewFestivalPrayerWGData.Instance:GetIsBuyAdvancedFlag()
    self.node_list.btn_buy:SetActive(not is_buy_advanced)
    self.node_list.special_reward_list:SetActive(not is_buy_advanced)
    self.node_list.desc_lbl:SetActive(not is_buy_advanced)

    if is_buy_advanced then
		RectTransform.SetAnchoredPositionXY(self.node_list.noraml_reward_list.rect, 0, 43)
		RectTransform.SetSizeDeltaXY(self.node_list.root_bg.rect, 1334, 300)
	end
end

function NewFestivalPrayerResultView:FlushRewardList()
    self.noraml_reward_list:SetDataList(self.show_reward_list)
    self.special_reward_list:SetDataList(self.spe_show_reward_list)
end

function NewFestivalPrayerResultView:OnClickSureBtn()
	self:Close()
end

function NewFestivalPrayerResultView:OnClickBuyBtn()
    NewFestivalPrayerWGCtrl.Instance:OpenAdvancedView()
	self:Close()
end