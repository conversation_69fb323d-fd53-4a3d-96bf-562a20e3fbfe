function YanYuGePrivilegeView:WLTZLoadCallBack()
    if not self.wltz_time_grid_list then
        self.wltz_time_grid_list = AsyncListView.New(WLTZTimeGridItemCellRender, self.node_list.wltz_time_grid_list)
        self.wltz_time_grid_list:SetSelectCallBack(BindTool.Bind(self.OnSelectWLTZTimeGridListCallBack, self))
    end

    XUI.AddClickEventListener(self.node_list.btn_jump1, BindTool.Bind(self.OnClickWLTZJumpBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_jump2, BindTool.Bind(self.OnClickWLTZJumpBtn, self))
    XUI.AddClickEventListener(self.node_list.desc_tsce, BindTool.Bind(self.OnClickWLTZTSCEBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_wltz_send, BindTool.Bind(self.OnClickWLTZSendBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_wltz_lingqu, BindTool.Bind(self.OnClickWLTZLingQuBtn, self))

    self:FlushWLTZTime()
    self.select_wltz_index = -1
    self.is_cur_day = false
    self.select_wltz_data = {}
end

function YanYuGePrivilegeView:WLTZShowIndexCallBack()

end

function YanYuGePrivilegeView:WLTZReleaseCallBack()
    if self.wltz_time_grid_list then
        self.wltz_time_grid_list:DeleteMe()
        self.wltz_time_grid_list = nil
    end

    if CountDownManager.Instance:HasCountDown("yanyuge_wltz_time") then
        CountDownManager.Instance:RemoveCountDown("yanyuge_wltz_time")
    end

    self.select_wltz_index = nil
    self.select_wltz_data = nil
end

function YanYuGePrivilegeView:WLTZOnFlush(param_t)
    local week_data_list = YanYuGeWGData.Instance:GetCurWeekShowCfgList()
    self.wltz_time_grid_list:SetDataList(week_data_list)

    -- 进来默认选中
    if self.select_wltz_index < 0 then
        if not IsEmptyTable(week_data_list) then
            local cur_week_day = YanYuGeWGData.Instance:GetTodayWeekDay()
            local jump_index = #week_data_list

            for k, v in pairs(week_data_list) do
                if cur_week_day == v.week_day then
                    jump_index = k
                    break
                end
            end

            self.wltz_time_grid_list:JumpToIndex(jump_index)
        end
    else
        self:FlushWLTZInfo()
    end
end

function YanYuGePrivilegeView:FlushWLTZTime()
    if CountDownManager.Instance:HasCountDown("yanyuge_wltz_time") then
        CountDownManager.Instance:RemoveCountDown("yanyuge_wltz_time")
    end
    
    local time = YanYuGeWGData.Instance:GetWLTZGetRewardTime()
    local server_time = TimeWGCtrl.Instance:GetServerTime()

    if time > 0 and time > server_time then
        CountDownManager.Instance:AddCountDown("yanyuge_wltz_time",
            function(elapse_time, total_time)
                if self.node_list.desc_wltz_time_tip then
                    local time_count = math.floor(total_time - elapse_time)
                    self.node_list.desc_wltz_time_tip.text.text = string.format(Language.YanYuGe.WLTZTimeStr, TimeUtil.FormatSecondDHM2(time_count))
                end
            end,
            function()
                if self.node_list.desc_wltz_time_tip then
                    self.node_list.desc_wltz_time_tip.text.text = ""
                    self:FlushWLTZInfo()
                end
            end, 
        time, nil, 1)
    else
        self.node_list.desc_wltz_time_tip.text.text = ""
    end
end

function YanYuGePrivilegeView:OnSelectWLTZTimeGridListCallBack(item)
    if nil == item or IsEmptyTable(item.data) then
        return
    end

    local data = item.data
    self.select_wltz_index = item.index
    self.select_wltz_data = data
    self.is_cur_day = false
    self:FlushWLTZInfo()
end

function YanYuGePrivilegeView:FlushWLTZInfo()
    if IsEmptyTable(self.select_wltz_data) then
        return
    end

    local day = self.select_wltz_data.week_day
    local is_cur_day = false
    local is_day_pass = false

    local cur_week = YanYuGeWGData.Instance:GetInvestWeek() 
    self.node_list.wltz_week2:CustomSetActive(cur_week == 1)
    self.node_list.wltz_week1:CustomSetActive(cur_week ~= 1)

    if YanYuGeWGData.Instance:IsOpenServerWeek() then
        local open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
        is_cur_day = open_server_day == day
        is_day_pass = open_server_day < day
    else
        local server_time = TimeWGCtrl.Instance:GetServerTime()
        local w_day = TimeUtil.FormatSecond3MYHM1(server_time)
        is_cur_day = w_day == day
        is_day_pass = w_day < day
    end

    self.is_cur_day = is_cur_day

    self.node_list.cur_lixi_day:CustomSetActive(is_cur_day)
    self.node_list.cur_cr_day:CustomSetActive(is_cur_day)
    self.node_list.not_cur_lixi_day:CustomSetActive(not is_cur_day)
    self.node_list.not_cur_cr_day:CustomSetActive(not is_cur_day)

    local cur_day_total_quota = YanYuGeWGData.Instance:GetCurDayTotalQuota()
    local total_quota_str = ""

    local rate = 10000 
    for i = 1, 4 do
        if cur_day_total_quota / rate >= 1 then
            break
        end 

        total_quota_str = total_quota_str .. "0"
        rate = rate / 10
    end

    -- 累计
    self.node_list.desc_wltz_total_num.text.text = total_quota_str .. cur_day_total_quota

    local can_get_invest = YanYuGeWGData.Instance:IsCanGetTouZiMoney()  -- 能否领奖
    local is_get_invest = YanYuGeWGData.Instance:IsGetTouZiMoney()      -- 已领取奖励
    local is_join_invest = YanYuGeWGData.Instance:IsJoinInvest()  -- 是否投资了

    self.node_list.btn_wltz_send:CustomSetActive(not can_get_invest)
    self.node_list.btn_wltz_lingqu:CustomSetActive(can_get_invest and not is_get_invest and is_join_invest)
    self.node_list.flag_wltz_get:CustomSetActive(can_get_invest and is_get_invest)
    self.node_list.flag_wltz_not_join:CustomSetActive(can_get_invest and not is_get_invest and not is_join_invest)

    if is_cur_day then
        local can_invest_quota = YanYuGeWGData.Instance:GetInvestLimit()
        if can_invest_quota > 0 then
            self.node_list.desc_btn_wltz_send.text.text = string.format(Language.YanYuGe.WLTZCanInvestQuotaValue, can_invest_quota)
        else
            self.node_list.desc_btn_wltz_send.text.text = Language.YanYuGe.WLTZToDayInvestQuotaLimit
        end

        -- 利息
        -- 初始利息
        local init_interest = YanYuGeWGData.Instance:GetOtherCfgAttrValue("init_interest")
        -- 总利息
        local total_interest = YanYuGeWGData.Instance:GetInvestInterest()
        -- 额外利息
        local extra_interest = total_interest - init_interest
        -- 累计利息   
        self.node_list.desc_ljlx.text.text = string.format(Language.YanYuGe.WLTZLeiJiLiXiStr, COLOR3B.C1, init_interest / 100, extra_interest / 100)

        -- 今日可提升
        local cur_extra_week_cfg = YanYuGeWGData.Instance:GetCurExtraScoreCfg()
        -- 今日可额外获得的利息
        local cur_extra_value = 0
        if not IsEmptyTable(cur_extra_week_cfg) then
            for k, v in pairs(cur_extra_week_cfg) do
                if v.get_type == 2 then
                    cur_extra_value = cur_extra_value + v.reward
                end
            end
        end

        -- 初始积分表
        local cur_init_score_cfg = YanYuGeWGData.Instance:GetCurInitScoreCfgByWeek()
        local cur_init_score = cur_init_score_cfg and cur_init_score_cfg.init_score or 0
        -- 额外积分 get_type = 1 的 reward （完成的任务）的相加
        local task_extra_score, can_add_extra_score = YanYuGeWGData.Instance:GetTaskExtraScore()

        -- 总投资钱
        local already_invest_num = YanYuGeWGData.Instance:GetAlreadyInvestNum()
        -- 额外获得钱
        local extra_get_coin = math.floor(already_invest_num * cur_extra_value / 10000)  -- 今日所有 * cur_extra_value/ 100
        -- local extra_get_coin = cur_extra_value / 10000 * (cur_init_score + task_extra_score)

        if extra_get_coin <= 0 then
            self.node_list.desc_jrts.text.text = string.format(Language.YanYuGe.WLTZJinRiKeTiShengStr, COLOR3B.C2, cur_extra_value / 100)
        else
            self.node_list.desc_jrts.text.text = string.format(Language.YanYuGe.WLTZJinRiKeTiShengExtraGetStr, COLOR3B.C2, cur_extra_value / 100, extra_get_coin)
        end

        -- 提升利息
        local cur_week_show_cfg = YanYuGeWGData.Instance:GetCurWeekShowCfg()

        for i = 1, 2 do
            local title = (cur_week_show_cfg or {})["jump_title" .. i]
            local jump = (cur_week_show_cfg or {})["jump_path" .. i]
            
            if title and "" ~= title and jump and "" ~= jump then
                self.node_list["btn_jump" .. i].text.text = string.format("<u>%s</u>", title) 
            end
        end

        -- 存入
        -- 今日存额
        self.node_list.desc_jrce.text.text = string.format(Language.YanYuGe.WLTZJinRiCunEr, COLOR3B.C1, cur_init_score, task_extra_score)

        -- 今日额外存额
        self.node_list.desc_jrewce.text.text = string.format(Language.YanYuGe.WLTZJinRiErWaiCunEr, COLOR3B.C2, can_add_extra_score)

        -- 提升额度 
        local up_value_str = YanYuGeWGData.Instance:GetTiShengErDuStr()
        self.node_list.desc_tsce_title.text.text = Language.YanYuGe.WLTZTiShengErDu
        self.node_list.desc_tsce.text.text = string.format("<u>%s</u>", up_value_str) 
    else
        local str = is_day_pass and Language.YanYuGe.WLTZDayNotOpen or Language.YanYuGe.WLTZXiuShi
        self.node_list.desc_btn_wltz_send.text.text = str
        self.node_list.desc_not_cur_lixi_day.text.text = str
        self.node_list.desc_not_cur_cr_day.text.text = str
    end
end

function YanYuGePrivilegeView:OnClickWLTZSendBtn()
    if self.is_cur_day then
        -- 如果还有余额就打开投资界面
        local can_invest_value = YanYuGeWGData.Instance:GetInvestLimit()

        if can_invest_value > 0 then
            local score = YanYuGeWGData.Instance:GetCurScore()

            if score > 0 then
                YanYuGeWGCtrl.Instance:OpenYanYuGeWLTZTip(math.min(can_invest_value, score))
            else
                SysMsgWGCtrl.Instance:ErrorRemind(Language.YanYuGe.NoEnoughScore)
            end
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.YanYuGe.WLTZInvestValueNotEnough)
        end
    else
        local day = self.select_wltz_data.week_day
        local day_pass = false

        if YanYuGeWGData.Instance:IsOpenServerWeek() then
            local open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
            day_pass = open_server_day > day
        else
            local server_time = TimeWGCtrl.Instance:GetServerTime()
            local w_day = TimeUtil.FormatSecond3MYHM1(server_time)
            day_pass = w_day > day
        end

        if day_pass then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.YanYuGe.WLTZXiuShiError) 
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.YanYuGe.WLTZDayNotOpenError)
        end
    end
end

function YanYuGePrivilegeView:OnClickWLTZLingQuBtn()
    YanYuGeWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.FETCH_INVEST)
end

function YanYuGePrivilegeView:OnClickWLTZTSCEBtn()
    ViewManager.Instance:Open(GuideModuleName.YanYuGeWLTZTaskView, 0, "jump_panel", {panel_index = 1})  
end

function YanYuGePrivilegeView:OnClickWLTZJumpBtn(jump_index)
    -- local cur_week_show_cfg = YanYuGeWGData.Instance:GetCurWeekShowCfg()
    -- local jump = (cur_week_show_cfg or {})["jump_path" .. jump_index]

    -- if jump and "" ~= jump then
    --     FunOpen.Instance:OpenViewNameByCfg(jump)
    -- end 
    ViewManager.Instance:Open(GuideModuleName.YanYuGeWLTZTaskView, 0, "jump_panel", {panel_index = 2})
end

---------------------------WLTZTimeGridItemCellRender-----------------------------
WLTZTimeGridItemCellRender = WLTZTimeGridItemCellRender or BaseClass(BaseRender)

function WLTZTimeGridItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local title = self.data.title
    self.node_list.desc_normal.text.text = title
    self.node_list.desc_hl.text.text = title
    self.node_list.desc_end.text.text = title

    -- local day = self.data.week_day
    -- local cur_week = YanYuGeWGData.Instance:GetInvestWeek() 
    -- local is_cur_day = false
    -- local is_day_pass = false

    -- if YanYuGeWGData.Instance:IsOpenServerWeek() then
    --     local open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    --     is_cur_day = open_server_day == day
    --     is_day_pass = open_server_day < day
    -- else
    --     local server_time = TimeWGCtrl.Instance:GetServerTime()
    --     local w_day = TimeUtil.FormatSecond3MYHM1(server_time)
    --     is_cur_day = w_day == day
    --     is_day_pass = w_day < day
    -- end

    -- self.node_list.lock_flag:CustomSetActive(not is_cur_day)
end

function WLTZTimeGridItemCellRender:OnSelectChange(is_select)
    local week_data_list = YanYuGeWGData.Instance:GetCurWeekShowCfgList()
    local len = #week_data_list

    self.node_list.end_bg:CustomSetActive(not is_select and self.index == len)
    self.node_list.bg:CustomSetActive(not is_select and self.index ~= len)
    self.node_list.hl:CustomSetActive(is_select)
end