OfflinerestEffectView = OfflinerestEffectView or BaseClass(SafeBaseView)

function OfflinerestEffectView:__init()
    self:SetMaskBg(false, false)
    self:SetMaskBgAlpha(0)
	self.view_layer = UiLayer.PopWhite
	local bundle_name = "uis/view/offlinerest_ui_prefab"
    self:AddViewResource(0, bundle_name, "layout_offlinerest_exp_effect")
end

function OfflinerestEffectView:CloseCallBack()
	self:RemoveEffectDelayTimer()
end

function OfflinerestEffectView:ReleaseCallBack()
	self:RemoveEffectDelayTimer()
end

--移除回调
function OfflinerestEffectView:RemoveEffectDelayTimer()
    if self.show_effect_timer then
        GlobalTimerQuest:CancelQuest(self.show_effect_timer)
        self.show_effect_timer = nil
    end
end

function OfflinerestEffectView:OnFlush(param_t, index)
	self:RemoveEffectDelayTimer()
	self.show_effect_timer = GlobalTimerQuest:AddDelayTimer(function ()
		self:Close()
	end, 0.6)

	self.node_list.ExpInfo_performance_effect:CustomSetActive(true)
end