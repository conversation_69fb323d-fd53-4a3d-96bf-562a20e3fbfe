function BillionSubsidyView:LYSDLoadCallBack()
    if not self.lingyu_shop_list then
        self.lingyu_shop_list = AsyncBaseGrid.New()
        self.lingyu_shop_list:CreateCells(
            {col = 4,
            change_cells_num = 1,
            complement_col_item = true,
            list_view = self.node_list.lingyu_shop_list,
			assetBundle = "uis/view/billion_subsidy_ui_prefab",
            assetName = "lingyu_shop_item",
            itemRender = BillionSubsidyLYSDShopItem}
        )
        self.lingyu_shop_list:SetStartZeroIndex(false)
    end

    XUI.AddClickEventListener(self.node_list["lingyu_active_vip_btn"], BindTool.Bind(self.OnClickLingyuActiveVipBtn, self))
    XUI.AddClickEventListener(self.node_list["lingyu_receive_all_reward_btn"], BindTool.Bind(self.OnClickLingyuReceiveAllRewardBtn, self))
end

function BillionSubsidyView:LYSDReleaseCallBack()
    if self.lingyu_shop_list then
        self.lingyu_shop_list:DeleteMe()
        self.lingyu_shop_list = nil
    end
end

function BillionSubsidyView:LYSDYOnFlush()
    local data_list = BillionSubsidyWGData.Instance:GetLYSDShopItemGradeData()
    local is_can_receive = BillionSubsidyWGData.Instance:GetLYSDIsCanReceiveAllReward()
    local is_receive = BillionSubsidyWGData.Instance:GetLYSDIsAlreadyReceiveAllReward()
    if not data_list then
        return
    end

    self.lingyu_shop_list:SetDataList(data_list)
    self.node_list.lingyu_shop_list_scroll_bar:CustomSetActive(#data_list > 7)

    self.node_list.lingyu_tip_active_vip_content:CustomSetActive(not is_can_receive)
    self.node_list.lingyu_receive_all_reward_btn:CustomSetActive(is_can_receive and not is_receive)
    self.node_list.lingyu_receive_all_reward_flag:CustomSetActive(is_can_receive and is_receive)
end

function BillionSubsidyView:OnClickLingyuActiveVipBtn()
	BillionSubsidyWGCtrl.Instance:OpenActiceVipView()
end

function BillionSubsidyView:OnClickLingyuReceiveAllRewardBtn()
    BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.GOLD_SHOP_BUY, -1, -1)
end

------------------------------- BillionSubsidyLYSDShopItem 商品item
BillionSubsidyLYSDShopItem = BillionSubsidyLYSDShopItem or BaseClass(BaseRender)
function BillionSubsidyLYSDShopItem:LoadCallBack()
    if not self.item_cell then

        self.item_cell = ItemCell.New(self.node_list.item_pos)
    end

    XUI.AddClickEventListener(self.node_list.use_btn, BindTool.Bind(self.OnClickUseBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_add_shop_cart, BindTool.Bind(self.OnClickAddShopCartBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_receive, BindTool.Bind(self.OnClickReceiveBtn, self))
end

function BillionSubsidyLYSDShopItem:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function BillionSubsidyLYSDShopItem:OnFlush()
    local is_hide = IsEmptyTable(self.data)
    self.node_list.root:CustomSetActive(not is_hide)
    if is_hide then
        return
    end

    local item_data = self.data.reward[0]
    self.item_cell:SetData(item_data)
    self.node_list.shop_name.text.text = ItemWGData.Instance:GetItemName(item_data.item_id)

    local buy_count = BillionSubsidyWGData.Instance:GetLYSDItemDataBySeq(self.data.item_seq)
    self.node_list.buy_done_flag:CustomSetActive(self.data.is_buy_done)
    self.node_list.btn_add_shop_cart:CustomSetActive(not self.data.is_buy_done)
    self.node_list.limit_count_panel:CustomSetActive(self.data.buy_limit > 0 and not self.data.is_buy_done)
    self.node_list.official_subsidy_panel:CustomSetActive(self.data.subsidy > 0 and not self.data.is_buy_done)

    if not self.data.is_buy_done then
        local num_in_cart = BillionSubsidyWGData.Instance:GetShopNumInCart(BillionSubsidyWGData.ShopType.LYSD, self.data.item_seq)
        self.node_list.limit_count.text.text = string.format(Language.BillionSubsidy.BYBTLimitCount, buy_count + num_in_cart, self.data.buy_limit)
        if self.data.subsidy > 0 then
            self.node_list.official_subsidy_txt.text.text = string.format(Language.BillionSubsidy.LYSDOfficialSubsidy, self.data.subsidy)
        end

        self.node_list.btn_add_shop_cart:CustomSetActive(buy_count + num_in_cart < self.data.buy_limit)
    else
        self.node_list.btn_add_shop_cart:CustomSetActive(false)

    end

    local member_level = BillionSubsidyWGData.Instance:GetMemberLevel()
    local is_receive = member_level >= self.data.free_member_level
    self.node_list.use_btn:SetActive(not is_receive)
    self.node_list.btn_receive:SetActive(is_receive)

    self.node_list.price.text.text = self.data.price
    XUI.SetButtonEnabled(self.node_list.use_btn, not self.data.is_buy_done)
    XUI.SetButtonEnabled(self.node_list.btn_receive, not self.data.is_buy_done)
end

function BillionSubsidyLYSDShopItem:OnClickUseBtn()
    if IsEmptyTable(self.data) then
        return
    end

    local buy_count = BillionSubsidyWGData.Instance:GetLYSDItemDataBySeq(self.data.item_seq)
    if self.data.is_buy_done then
        TipWGCtrl.Instance:ShowSystemMsg(Language.BillionSubsidy.BYBTShopBuyDone)
        return
    end

    local is_enough = RoleWGData.Instance:GetIsEnoughAllGold(self.data.price)
    if is_enough then
        local item_data = self.data.reward[0]

        local buy_func = function(num)
            TipWGCtrl.Instance:ShowGetItem(item_data)
            local buy_num = num or 1
            BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.GOLD_SHOP_BUY, self.data.item_seq, buy_num)
        end
    
        local tips_data = {}
        tips_data.title_view_name = Language.Common.BuyItemTipsTitle
        tips_data.item_id = item_data.item_id
        tips_data.expend_item_id = COMMON_CONSTS.VIRTUAL_ITEM_GOLD
        tips_data.expend_item_num = self.data.price
        tips_data.max_buy_count = self.data.buy_limit - buy_count
        tips_data.is_show_limit = true
        TipWGCtrl.Instance:OpenCustomBuyItemTipsView(tips_data, buy_func)
    else
        VipWGCtrl.Instance:OpenTipNoGold()
    end
end

function BillionSubsidyLYSDShopItem:OnClickAddShopCartBtn()
    if IsEmptyTable(self.data) then
        return
    end
    
    local is_add = BillionSubsidyWGCtrl.Instance:AddItemToShopCart(BillionSubsidyWGData.ShopType.LYSD, self.data.item_seq, 1, self.data.buy_limit, true)
    if is_add then
        local pos, target_pos = BillionSubsidyWGCtrl.Instance:GetNodeInScreenPos(self.node_list.item_pos)
        BillionSubsidyWGCtrl.Instance:PlaySCAddItemAnim(self.data.reward[0].item_id, pos, target_pos)
    end

end

function BillionSubsidyLYSDShopItem:OnClickReceiveBtn()
    if IsEmptyTable(self.data) then
        return
    end

    local buy_count = BillionSubsidyWGData.Instance:GetLYSDItemDataBySeq(self.data.item_seq)
    local receive_count = self.data.buy_limit - buy_count
    BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.GOLD_SHOP_BUY, self.data.item_seq, receive_count)
end