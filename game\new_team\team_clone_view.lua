local CustomPos = {
	Vector2(500,-248),
	Vector2(800,-248),
	Vector2(1100,-248)
}

function TeamView:CloneLoadCallBack()
    XUI.AddClickEventListener(self.node_list.team_clone_to_my, BindTool.Bind1(self.OnClickToMyTeam, self))
    XUI.AddClickEventListener(self.node_list.btn_learn_clone, BindTool.Bind1(self.OnClickLearnClone, self))
    XUI.AddClickEventListener(self.node_list.btn_clone_reward, BindTool.Bind1(self.OnClickCloneReward, self))

    self.clone_role_model = RoleModel.New()
    local display_data = {
        parent_node = self.node_list["clone_role_display"],
        camera_type = MODEL_CAMERA_TYPE.BASE,
        -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
        rt_scale_type = ModelRTSCaleType.M,
        can_drag = false,
    }
    
    self.clone_role_model:SetRenderTexUI3DModel(display_data)
	-- self.clone_role_model:SetUI3<PERSON>odel(self.node_list["clone_role_display"].transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE)
end

function TeamView:CloneReleaseCallBack()
	if self.clone_role_model then
		self.clone_role_model:DeleteMe()
		self.clone_role_model = nil
	end
end

function TeamView:CloneShowIndexCallBack()

end

function TeamView:CloneOnFlush()
    -- 是否解锁分身功能
    local lock_clone = NewTeamWGData.Instance:GetFenshenFuncIsOpen()

    self.node_list.clone_not_open:CustomSetActive(not lock_clone)
    self.node_list.clone_open:CustomSetActive(lock_clone)

    if lock_clone then
        self:FlushCloneOpenNotInTeam()
    end
end

--未处于组队中
function TeamView:FlushCloneOpenNotInTeam()
    local vo = GameVoManager.Instance:GetMainRoleVo()
    local special_status_table = {ignore_wing = true, ignore_fazhen = true, ignore_jianzhen = true, ignore_halo = true}
    self.clone_role_model:SetModelResInfo(vo, special_status_table)

    local bundle, asset = ResPath.GetCommonImages(RoleWGData.GetProfIcon(vo.prof, vo.sex))
    self.node_list["clone_role_prof"].image:LoadSprite(bundle, asset, function ()
        self.node_list["clone_role_prof"].image:SetNativeSize()
    end)

    self.node_list.clone_role_name.text.text = vo.role_name

    local is_dianfeng, show_level = RoleWGData.Instance:GetDianFengLevel(vo.level)
    self.node_list.clone_dianfeng_img:CustomSetActive(is_dianfeng)
    self.node_list.clone_level_limit_text.text.text = show_level
end

function TeamView:OnClickToMyTeam()
    local is_team
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        is_team = CrossTeamWGData.Instance:GetIsInTeam() == 1
    else
        is_team = SocietyWGData.Instance:GetIsInTeam() == 1
    end

    if is_team then
        self:ChangeToIndex(TabIndex.team_my_team)
    else
        self:ChangeToIndex(TabIndex.team_pingtai)
    end
end

function TeamView:OnClickLearnClone()
    --ViewManager.Instance:Open(GuideModuleName.CultivationView, TabIndex.esoterica)
    ViewManager.Instance:Open(GuideModuleName.EsotericaDetailView)
end

function TeamView:OnClickCloneReward()
    NewTeamWGCtrl.Instance:OpenCloneHelpRewardView()
end