-----------------------------------------
--赏金任务-恶魔降临(85)
-----------------------------------------
FuBenBountyDevilLogic = FuBenBountyDevilLogic or BaseClass(CommonFbLogic)

function FuBenBountyDevilLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
	self.show_word_img = 0
end

function FuBenBountyDevilLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
end

function FuBenBountyDevilLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	FuBenPanelWGCtrl.Instance:OpenBountyTaskView()
    GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)

end

function FuBenBountyDevilLogic:GetGuiJiMonsterEnemy()
	CommonFbLogic.GetGuiJiMonsterEnemy(self)
	local target_id = 20000
	local target_obj = nil
	local monster_list = Scene.Instance:GetMonsterList()
	for k, v in pairs(monster_list) do
		local vo = v:GetVo()
		local monster_id = vo.monster_id
		if monster_id < target_id then
			target_obj = v
			target_id = monster_id
		end
	end
	if target_obj ~= nil and (target_obj:GetVo().monster_id == 11252 or target_obj:GetVo().monster_id == 11253) and self.show_word_img < target_obj:GetVo().monster_id then
		FuBenPanelWGCtrl.Instance:VistWordImg(target_obj:GetVo().monster_id)
		self.show_word_img = target_obj:GetVo().monster_id
	end

	if target_obj == nil then
		self:MoveToPos(59, 26)
	end
	return target_obj
end

function FuBenBountyDevilLogic:Out()
	CommonFbLogic.Out(self)
	self.show_word_img = 0
	FuBenPanelWGCtrl.Instance:VistWordImg(0)	
	FuBenPanelWGCtrl.Instance:CloseBountyTaskView()
    GuajiWGCtrl.Instance:StopGuaji()
end