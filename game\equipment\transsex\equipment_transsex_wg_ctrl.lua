EquipmentWGCtrl = EquipmentWGCtrl or BaseClass(BaseWGCtrl)

function EquipmentWGCtrl:RegisterProtocel()
	self:RegisterProtocol(CSChangeItemSexOper)
	self:RegisterProtocol(SCChangeItemSexRet, "OnSCChangeItemSexRet") 	-- 道具转性结果
	self:RegisterProtocol(SCChangeItemSexInfo, "OnSCChangeItemSexInfo") 	-- 道具转性结果
end

---------------------------- 转性 ---------------------------------
-- 请求道具转性
function EquipmentWGCtrl:SendTranssexItem(item_bag_index, num)
	local protocol = ProtocolPool.Instance:GetProtocol(CSChangeItemSexOper)
	protocol.grid_index = item_bag_index or 0
	protocol.num = num or 1
	protocol:EncodeAndSend()
end

-- 道具转性结果
function EquipmentWGCtrl:OnSCChangeItemSexRet(protocol)
	-- 成功
	if protocol.ret == 1 then
		ItemWGData.Instance:UpdateComposeEquipList(true)
		ViewManager.Instance:FlushView(GuideModuleName.Equipment, TabIndex.equipment_zhuanhua, "clear_stuff_slot")
	-- 失败
	elseif protocol.ret == 2 then

	end
end

function EquipmentWGCtrl:OnSCChangeItemSexInfo(protocol)
	EquipmentWGData.Instance:SetTranssexIsFree(protocol.is_free)
	ViewManager.Instance:FlushView(GuideModuleName.Equipment, TabIndex.equipment_zhuanhua)
end
---------------------------- 转性End ---------------------------------