Luaunit = require("debug/luaunit")

local function Init()
    --dofile("game/hidden_weapon/utils/hidden_weapon_role_info_handler.lua")
    --dofile("gameui/widgets/role_info_view.lua")
    --RoleBagWGCtrl.Instance.view.role_info_widget:DeleteMe()
    --RoleBagWGCtrl.Instance.view:CreateRoleInfoWidget()
    dofile("game/hidden_weapon/utils/hidden_weapon_remind_manager")
    dofile("game/hidden_weapon/strengthen/hidden_weapon_keming_handler")
    dofile("game/hidden_weapon/build/hidden_weapon_equip_select_view")
    dofile("game/hidden_weapon/build/hidden_weapon_material_render")
    dofile("game/hidden_weapon/build/hidden_weapon_equip_select_view")
    ExecuteHotUpdate("game.hidden_weapon.build.hidden_weapon_equip_select_view")
    dofile("game/hidden_weapon/hw_build_item_render.lua")
    ExecuteHotUpdate("game.hidden_weapon.hw_build_item_render")
    ExecuteHotUpdate("game.hidden_weapon.utils.hidden_weapon_request")
    ExecuteHotUpdate("game.hidden_weapon.detail.hw_detail_item_render")
    ExecuteHotUpdate("game.hidden_weapon.detail.hw_detail_skill_render")

    ExecuteHotUpdate("game.hidden_weapon.build.hidden_weapon_build_view")
    ExecuteHotUpdate("game.hidden_weapon.hidden_weapon_strengthen_view")
    ExecuteHotUpdate("game.hidden_weapon.detail.hidden_weapon_detail_view")
    ExecuteHotUpdate("game.hidden_weapon.hidden_weapon_view")

    ExecuteHotUpdate("game.hidden_weapon.hidden_weapon_data")
    ExecuteHotUpdate("game.hidden_weapon.hidden_weapon_ctrl")

    if HiddenWeaponWGCtrl.Instance and HiddenWeaponWGCtrl.Instance.view then
        HiddenWeaponWGCtrl.Instance.view:CloseForTest()
        HiddenWeaponWGCtrl.Instance.equip_choose_view:CloseForTest()
        if HiddenWeaponWGCtrl.Instance.DeleteMe then
            HiddenWeaponWGCtrl.Instance:DeleteMe()
        end
    end
    HiddenWeaponWGData.Instance = nil
    HiddenWeaponWGCtrl.Instance = nil
    HiddenWeaponWGCtrl.New()

    FunOpen.Instance:OpenViewByName(GuideModuleName.HiddenWeaponView)
end

function ZYTestAddPositive()
    Luaunit.assertEquals(2, 2)
    Luaunit.assertIsNil(HiddenWeaponWGData.Instance:GetBaseEquipVo("错误的文本id"))
    Luaunit.assertIsNil(HiddenWeaponWGData.Instance:GetBaseEquipVo(11111111))
    --使用string类型的正确id，考虑兼容
    Luaunit.assertNotIsInf(HiddenWeaponWGData.Instance:GetBaseEquipVo("16501").equip.name)
    Luaunit.assertNotIsInf(HiddenWeaponWGData.Instance:GetBaseEquipVo(16502).equip.name)
end

Init()
--Luaunit.LuaUnit.run()
