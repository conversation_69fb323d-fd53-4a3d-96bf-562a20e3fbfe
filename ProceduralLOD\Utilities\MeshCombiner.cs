#if UNITY_EDITOR
using BrainFailProductions.PolyFewRuntime;
using UnityEngine;

namespace ProceduralLOD
{
    public class MeshCombiner
    {
        public static MeshRenderer Build(GameObject target)
        {
            MeshRenderer[] mrs = target.GetComponentsInChildren<MeshRenderer>();

            Vector3 rawPosition = target.transform.localPosition;
            Quaternion rawRotation = target.transform.localRotation;
            Vector3 rawScale = target.transform.localScale;

            target.transform.position = Vector3.zero;
            target.transform.rotation = Quaternion.identity;
            target.transform.localScale = new Vector3(1 / target.transform.lossyScale.x, 1 / target.transform.lossyScale.y, 1 / target.transform.lossyScale.z);

            BrainFailProductions.PolyFewRuntime.MeshCombiner.generateUV2 = true;
            
            GameObject combinedObj = PolyfewRuntime.CombineMeshesFromRenderers(target.transform, mrs, null, null);
            
            for (int i = target.transform.childCount - 1; i >= 0; i--)
            {
                GameObject.DestroyImmediate(target.transform.GetChild(i).gameObject);
            }

            GameObject parent = new GameObject();
            parent.transform.SetParent(target.transform);
            parent.transform.localPosition = Vector3.zero;
            parent.transform.localRotation = Quaternion.identity;
            parent.transform.localScale = Vector3.one;
            
            MeshRenderer combinedRenderer = parent.AddComponent<MeshRenderer>();
            MeshFilter combinedMeshFilter = parent.AddComponent<MeshFilter>();
            combinedRenderer.sharedMaterials = combinedObj.GetComponentInChildren<MeshRenderer>().sharedMaterials;
            combinedMeshFilter.sharedMesh = combinedObj.GetComponentInChildren<MeshFilter>().sharedMesh;
            
            // combinedRenderer.transform.SetParent(target.transform);
            // combinedRenderer.transform.localPosition = Vector3.zero;
            // combinedRenderer.transform.localRotation = Quaternion.identity;
            // combinedRenderer.transform.localScale = Vector3.one;
            // combinedRenderer.name = "";
            
            GameObject.DestroyImmediate(combinedObj);
            
            target.transform.localPosition = rawPosition;
            target.transform.localRotation = rawRotation;
            target.transform.localScale = rawScale;

            return combinedRenderer;
        }
        
    }
}

#endif