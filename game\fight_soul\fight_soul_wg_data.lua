FightSoulWGData = FightSoulWGData or BaseClass()

function FightSoulWGData:__init()
	if FightSoulWGData.Instance then
		error("[FightSoulWGData] Attempt to create singleton twice!")
		return
	end
	FightSoulWGData.Instance = self

	self.cache_compose_list = {}
	self:ClearSingleComposeRemindCache()

	self:InitParam()
    self:InitCfgData()
	self:InitBoneData()
	self:InitFightSoulSlot()
	self:InitCuiZhuoCfg()

    RemindManager.Instance:Register(RemindName.FightSoul_Train, BindTool.Bind(self.GetTrainRemind, self))
	RemindManager.Instance:Register(RemindName.FightSoul_Compose, BindTool.Bind(self.GetComposeRemind, self))
	RemindManager.Instance:Register(RemindName.FightSoul_Bone, BindTool.Bind(self.GetBoneRemind, self))
	RemindManager.Instance:Register(RemindName.FightSoul_CuiZhuo, BindTool.Bind(self.GetCuiZhuoRem<PERSON>, self))

	self:RegisterBreakRemindInBag(RemindName.FightSoul_Train)
end

function FightSoulWGData:__delete()
	for k, v in pairs(self.fight_soul_slot_list) do
		v:DeleteMe()
	end
	self.fight_soul_slot_list = nil

	RemindManager.Instance:UnRegister(RemindName.FightSoul_Train)
	RemindManager.Instance:UnRegister(RemindName.FightSoul_Compose)
	RemindManager.Instance:UnRegister(RemindName.FightSoul_Bone)
	RemindManager.Instance:UnRegister(RemindName.FightSoul_CuiZhuo)
	FightSoulWGData.Instance = nil
end

function FightSoulWGData:InitParam()
	self.exp_pool_exp = 0
	self.exp_pool_level = 0
	self.out_fight_index = -1

	self.slot_unlock_flag = {}
	self.fight_soul_bag_list = {}
	self.type_is_wear_list = {}
	self.item_is_wear_list = {}
	self.compose_select_cache = {}
end

function FightSoulWGData:RegisterBreakRemindInBag(remind_name)
	local map = {}
	local map = self:GetBreakStuffList()
	local item_id_list = {}
	for k, v in pairs(map) do
		table.insert(item_id_list, k)
	end

	BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list)
end

-- 初始化战魂
function FightSoulWGData:InitFightSoulSlot()
	self.fight_soul_slot_list = {}
	local slot_cfg = self:GetFightslotCfg()
	for k, v in pairs(slot_cfg) do
		local index = v.sixiang_slot_index
		self.fight_soul_slot_list[index] = FightSoulSlot.New()
		self.fight_soul_slot_list[index]:SetSlotIndex(index)
	end
end

-- 战魂列表
function FightSoulWGData:GetAllFightSoulSlotList()
	return self.fight_soul_slot_list
end

-- 获取战魂
function FightSoulWGData:GetFightSoulSlot(slot)
	return self.fight_soul_slot_list[slot]
end

-- 获取展示的排序列表
function FightSoulWGData:GetFightSoulShowList()
	local fs_slot_cfg = self:GetFightslotCfg()
	local show_list = {}
	local slot, sort_weight = 0, 0
	for k,v in pairs(fs_slot_cfg) do
		local data = {}
		slot = v.sixiang_slot_index
		data.sixiang_slot_index = slot
		sort_weight = 0--self:GetSlotIsLock(slot) and 999 or 0
		data.sort_index = slot + sort_weight
		table.insert(show_list, data)
	end

	if not IsEmptyTable(show_list) then
		table.sort(show_list, SortTools.KeyLowerSorter("sort_index"))
	end

	return show_list
end

-- 战魂位更新
function FightSoulWGData:UpdateFightSoulSlotInfo(protocol)
	self.exp_pool_exp = protocol.exp_pool_exp
	self.exp_pool_level = protocol.exp_pool_level
	self.out_fight_index = protocol.out_fight_index
	self.slot_unlock_flag = protocol.slot_unlock_flag
	local need_change_data = protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.LOGIN or
							protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.OPERA or
							protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.PUT_ON or
							protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.UPLEVEL or
							protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.UPGRADE or
							protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.REPLACE

	if need_change_data then
		self.type_is_wear_list = {}
		self.item_is_wear_list = {}
	end

	-- 槽位数据状态改变 改变融合材料数据缓存
	if protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.LOGIN or
		protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.OPERA or
		protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.PUT_ON or
		protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.REPLACE or
		protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.OUT_FIGHT then

		for k,v in pairs(self.fight_soul_slot_list) do
			local soul_index = v:GetSlotIndex()
			local old_bag_index = v:GetInBagIndex()
			local new_bag_index = protocol.slot_list[soul_index]
			if old_bag_index ~= new_bag_index then
				local old_item_data = self:GetFightSoulItemByBagIndex(old_bag_index)
				local new_item_data = self:GetFightSoulItemByBagIndex(new_bag_index)
				self:ChangeSingleComposeCache(FIGHT_SOUL_BAG_SEND_REASON.ADD_ITEM, old_item_data)
				self:ChangeSingleComposeCache(FIGHT_SOUL_BAG_SEND_REASON.REMOVE_ITEM, new_item_data)
			end
		end
	end

	-- 槽位数据更新
	local fight_soul_type
	for k,v in pairs(self.fight_soul_slot_list) do
		local soul_index = v:GetSlotIndex()
		-- 槽位数据改变
		if need_change_data then
			local bag_index = protocol.slot_list[soul_index]
			local item_data = self:GetFightSoulItemByBagIndex(bag_index)
			v:SetData(item_data)
			fight_soul_type = v:GetType()
			if fight_soul_type >= 0 then
				self.type_is_wear_list[fight_soul_type] = soul_index
			end

			if bag_index >= 0 then
				self.item_is_wear_list[bag_index] = soul_index
			end
		end

		v:SetOutFightFlag(soul_index == self.out_fight_index)
	end

	-- 背包协议 比阵位信息先下发  影响融合红点（上阵信息判断）
	-- 上阵数据改变也需改变背包排序
	if protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.LOGIN
	or 	protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.PUT_ON or
		protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.REPLACE then
		self:SortComposeFightSoulBagList()
	end
end

-- 获取槽位解锁标记
function FightSoulWGData:GetSlotLockFlag(slot)
	slot = slot or 0
	local flag = self.slot_unlock_flag[32 - slot] or 0
	return flag == 1
end

-- 获取槽位是否上锁
function FightSoulWGData:GetSlotIsLock(slot)
	local slot_cfg = self:GetFightslotCfgBySlot(slot)
	if slot_cfg then
		local role_level = RoleWGData.Instance:GetRoleLevel()
		local need_level = slot_cfg.role_level_limit
		local level_meet = role_level >= need_level
		local stuff_meet = true
		if slot_cfg.stuff_id > 0 and slot_cfg.stuff_num > 0 then
			stuff_meet = self:GetSlotLockFlag(slot)
		end

		return not (level_meet and stuff_meet)
	end

	return true
end

-- 检测是否有战魂出战
function FightSoulWGData:CheckFightSoulHaveFight()
	local list = self:GetAllFightSoulSlotList()
	if IsEmptyTable(list) then
		return false
	end

	for k,v in pairs(list) do
		if v:GetOutFightFlag() then
			return true
		end
	end

	return false
end

-- 获取解锁阵位数
function FightSoulWGData:GetUnlockSlotNum()
	local list = self:GetAllFightSoulSlotList()
	local unlock_num = 0
	local max_num = 0
	if IsEmptyTable(list) then
		return unlock_num, max_num
	end

	for k,v in pairs(list) do
		max_num = max_num + 1
		if not v:IsLock() then
			unlock_num = unlock_num + 1
		end
	end

	return unlock_num, max_num
end

-- 获取出战槽位
function FightSoulWGData:GetOutFightIndex()
	return self.out_fight_index
end

-- 经验池当前经验
function FightSoulWGData:GetExp()
	return self.exp_pool_exp
end

-- 经验池等级
function FightSoulWGData:GetExpPoolLevel()
	return self.exp_pool_level or 0
end

-- 经验池最大经验
function FightSoulWGData:GetMaxExp()
	return self:GetExpPoolLimit(self.exp_pool_level)
end

-- 获取战魂类型是否上阵
function FightSoulWGData:GetTypeIsWear(type)
	local slot_index = self.type_is_wear_list[type]
	return slot_index ~= nil, slot_index
end

-- 根据位置 获取战魂背包的是否上阵
function FightSoulWGData:GetBagIndexIsWear(index)
	local slot_index = self.item_is_wear_list[index]
	return slot_index ~= nil, slot_index
end

-- 战魂背包更新
function FightSoulWGData:UpdateFightSoulBagInfo(protocol)
	--全部
	if protocol.send_reason == FIGHT_SOUL_BAG_SEND_REASON.ALL then
		self.fight_soul_bag_list = {}
		for k,v in pairs(protocol.bag_change_list) do
			self.fight_soul_bag_list[v.index] = v.item_data
		end
		self:ChangeAllComposeCache()
	--获得
	elseif protocol.send_reason == FIGHT_SOUL_BAG_SEND_REASON.ADD_ITEM
	 	or protocol.send_reason == FIGHT_SOUL_BAG_SEND_REASON.SIXIANG_CALL_GET then
		for k,v in pairs(protocol.bag_change_list) do
			self:ChangeSingleComposeCache(FIGHT_SOUL_BAG_SEND_REASON.ADD_ITEM, v.item_data)
			self.fight_soul_bag_list[v.index] = v.item_data
		end

	--移除
	elseif protocol.send_reason == FIGHT_SOUL_BAG_SEND_REASON.REMOVE_ITEM then
		for k,v in pairs(protocol.bag_change_list) do
			local old_item_data = self:GetFightSoulItemByBagIndex(v.index)
			self:ChangeSingleComposeCache(FIGHT_SOUL_BAG_SEND_REASON.REMOVE_ITEM, old_item_data)
			self.fight_soul_bag_list[v.index] = nil
		end
	--升级or突破
	elseif protocol.send_reason == FIGHT_SOUL_BAG_SEND_REASON.UPLEVEL_OR_UPGRADE then
		for k,v in pairs(protocol.bag_change_list) do
			self.fight_soul_bag_list[v.index] = v.item_data
		end
	--融合成功
	elseif protocol.send_reason == FIGHT_SOUL_BAG_SEND_REASON.UP_COLOR_STAR then
		for k,v in pairs(protocol.bag_change_list) do
			local is_wear, slot_index = self:GetBagIndexIsWear(v.index)
			local old_item_data = self:GetFightSoulItemByBagIndex(v.index)
			if not is_wear then
				self:ChangeSingleComposeCache(FIGHT_SOUL_BAG_SEND_REASON.REMOVE_ITEM, old_item_data)
				self:ChangeSingleComposeCache(FIGHT_SOUL_BAG_SEND_REASON.ADD_ITEM, v.item_data)
			end
			self.fight_soul_bag_list[v.index] = v.item_data

			if is_wear then
				local slot_data = self:GetFightSoulSlot(slot_index)
				if not IsEmptyTable(slot_data) then
					slot_data:SetData(v.item_data)
				end
			end
		end
	end

	-- 背包协议 比阵位信息先下发  影响融合红点（上阵信息判断）
	-- 减少逻辑 （移除操作不执行，正常情况只有在融合成功才会移除）
	if protocol.send_reason ~= FIGHT_SOUL_BAG_SEND_REASON.ALL
	 and protocol.send_reason ~= FIGHT_SOUL_BAG_SEND_REASON.REMOVE_ITEM then
		self:SortComposeFightSoulBagList()
	end
end

-- 获取战魂背包
function FightSoulWGData:GetFightSoulBagList()
	return self.fight_soul_bag_list
end

-- 获取战魂背包的战魂
function FightSoulWGData:GetFightSoulItemByBagIndex(bag_index)
	return self.fight_soul_bag_list[bag_index]
end

function FightSoulWGData.FightSoulBagItemData()
	return {index = -1, item_id = 0, is_bind = 0, grade = 0,
			level = 0, star = 0, capability = 0, sort_index = 0,
			fight_soul_type = 0, color = 0,}
end

-- 获取当前槽位有更好的战魂可穿戴
function FightSoulWGData:GetFightSoulCanWearByType(slot, to_be_strengthen)
	local slot_data = self:GetFightSoulSlot(slot)
	if IsEmptyTable(slot_data) then
		return false
	end

	-- 限制
	if slot_data:IsLock() then
		if to_be_strengthen and self:GetDropToUnlockRemind(slot) then
			return true
		end

		return false
	end

	local is_wear_fs = slot_data:GetIsWear()
	local type = slot_data:GetType()
	local wear_color = slot_data:GetColor()
	local wear_star = slot_data:GetStar()

	local select_list = {}
	local fs_type, type_is_wear, index_is_wear
	local bag_list = self:GetFightSoulBagList()
	local wear_data
	for k,v in pairs(bag_list) do
		index_is_wear = self:GetBagIndexIsWear(v.index)
		if not index_is_wear then
			fs_type = v.fight_soul_type
			type_is_wear = self:GetTypeIsWear(fs_type)
			if is_wear_fs then
				if (type == fs_type and type_is_wear) or (type ~= fs_type and not type_is_wear) then
					if v.color > wear_color or (v.color == wear_color and v.star > wear_star) then
						return true
					end
				end
			else
				if not type_is_wear then
					return true
				end
			end
		end
	end

	return false
end

-- 上阵选择列表 type：槽位上的四象类型
function FightSoulWGData:GetFightSoulSelectListByType(type)
	local select_list = {}
	local fs_type, is_wear, index_is_wear
	local bag_list = self:GetFightSoulBagList()
	local max_bag_num = self:GetFightSoulBagMaxNum()
	for k,v in pairs(bag_list) do
		index_is_wear = self:GetBagIndexIsWear(v.index)
		if not index_is_wear then
			fs_type = v.fight_soul_type
			is_wear = self:GetTypeIsWear(fs_type)
			if (type == fs_type and is_wear) or (type ~= fs_type and not is_wear) then
				local data = {}
				local temp_data = FightSoulWGData.FightSoulBagItemData()
				for i,j in pairs(temp_data) do
					if v[i] then
						temp_data[i] = v[i]
					end
				end

				data.item_data = temp_data
				data.capability = self:GetNoEquipCapability(v)
				data.sort_index = v.color * 10000 + v.star * 1000 + (max_bag_num - v.index)
				table.insert(select_list, data)
			end
		end
	end

	if not IsEmptyTable(select_list) then
		table.sort(select_list, SortTools.KeyUpperSorters("sort_index"))
	end

	return select_list
end

-- 获取单个战魂基础战力
function FightSoulWGData:GetFightSoulBaseCapability(item_id)
    local fs_item_cfg = self:GetFightSoulItemCfg(item_id)
	if IsEmptyTable(fs_item_cfg) then
		return 0
	end

	local base_attribute = AttributeMgr.GetAttributteByClass(fs_item_cfg)
    return AttributeMgr.GetCapability(base_attribute)
end

-- 获取战魂技能战力
function FightSoulWGData:GetSkillCapabilityByItemId(item_id)
	local capability = 0
	local fs_item_cfg = self:GetFightSoulItemCfg(item_id)
	if IsEmptyTable(fs_item_cfg) then
		return capability
	end

	local skill_cfg = SkillWGData.Instance:GetSiXiangSkillById(fs_item_cfg.active_skill, fs_item_cfg.skill_level)
	if IsEmptyTable(skill_cfg) then
		return capability
	end

	capability = AttributeMgr.GetCapability(nil, skill_cfg)
	return capability
end

-- 获取战魂除了装备的所有战力
function FightSoulWGData:GetNoEquipCapability(data)
	local capability = 0
	if IsEmptyTable(data) then
		return capability
	end

	local fs_item_cfg = self:GetFightSoulItemCfg(data.item_id)
	if IsEmptyTable(fs_item_cfg) then
		return capability
	end

	local base_attribute = AttributePool.AllocAttribute()

	-- 基础
	local attr_str
	for k,v in pairs(fs_item_cfg) do
		attr_str = AttributeMgr.GetAttributteKey(k)
		if base_attribute[attr_str] ~= nil and v > 0 then
			base_attribute[attr_str] = base_attribute[attr_str] + v
		end
	end

	-- 等级
	local level_cfg = self:GetLevelCfgByLevel(data.level)
	if not IsEmptyTable(level_cfg) then
		for k,v in pairs(level_cfg) do
			attr_str = AttributeMgr.GetAttributteKey(k)
			if base_attribute[attr_str] ~= nil and v > 0 then
				base_attribute[attr_str] = base_attribute[attr_str] + v
			end
		end
	end

	-- 突破
	local grade_cfg = self:GetBreakCfgByGrade(data.grade)
	if not IsEmptyTable(grade_cfg) then
		for k,v in pairs(grade_cfg) do
			attr_str = AttributeMgr.GetAttributteKey(k)
			if base_attribute[attr_str] ~= nil and v > 0 then
				base_attribute[attr_str] = base_attribute[attr_str] + v
			end
		end
	end

	-- 技能
	local temp_skill_cfg
	local skill_cfg = SkillWGData.Instance:GetSiXiangSkillById(fs_item_cfg.active_skill, fs_item_cfg.skill_level)
	if not IsEmptyTable(skill_cfg) then
		temp_skill_cfg = {}
		temp_skill_cfg.attack_power = skill_cfg.attack_power
		temp_skill_cfg.defence_power = skill_cfg.defence_power
		temp_skill_cfg.capability_inc = skill_cfg.capability_inc
	end

	capability = AttributeMgr.GetCapability(base_attribute, temp_skill_cfg)
	return capability
end

-- 【养成】单个基础红点
function FightSoulWGData:GetSlotSingleLevelRemind(slot)
	local slot_data = self:GetFightSoulSlot(slot)
	if IsEmptyTable(slot_data) then
		return false
	end

	if not slot_data:GetIsWear() then
		return false
	end

	local level = slot_data:GetLevel()
	local is_max = slot_data:GetIsMaxLevel()
	local is_grade_max = slot_data:GetIsCurGradeMaxLevel()
	if not is_max and not is_grade_max then
		local level_cfg = self:GetLevelCfgByLevel(level + 1)
		local cur_exp = self:GetExp()
		if level_cfg and cur_exp >= level_cfg.need_exp then
			return true
		end
	end

	return false
end

-- 【养成】单个突破红点
function FightSoulWGData:GetSlotSingleBreakRemind(slot)
	local slot_data = self:GetFightSoulSlot(slot)
	if IsEmptyTable(slot_data) then
		return false
	end

	if not slot_data:GetIsWear() then
		return false
	end

	if slot_data:GetIsBreakLimit() then
		return false
	end

	local grade = slot_data:GetGrade()
	local is_max = slot_data:GetIsMaxGrade()
	local grade_cfg = self:GetBreakCfgByGrade(grade)
	if not is_max and grade_cfg then
		local consume_item = self:GetBreakStuffIdByType(slot_data:GetType())
		local num = ItemWGData.Instance:GetItemNumInBagById(consume_item)
		if num >= grade_cfg.consume_num then
			return true
		end
	end

	return false
end

-- 出战提醒
function FightSoulWGData:GetSingleOutFightRemind(slot)
	local slot_data = self:GetFightSoulSlot(slot)
	if IsEmptyTable(slot_data) then
		return false
	end

	if slot_data:IsLock() or not slot_data:GetIsWear() then
		return false
	end

	local had_out_fight = false
	local slot_list = self:GetAllFightSoulSlotList() or {}
	for k,v in pairs(slot_list) do
		if v:GetOutFightFlag() then
			had_out_fight = true
			break
		end
	end

	return not had_out_fight
end

-- 【养成】单个阵位红点
function FightSoulWGData:GetSingleSlotRemind(slot, ignore_better, to_be_strengthen)
	local slot_data = self:GetFightSoulSlot(slot)
	local jump_uplock = false
	if IsEmptyTable(slot_data) then
		return false, jump_uplock
	end

	-- 限制
	if slot_data:IsLock() then
		-- 道具解锁
		if self:GetDropToUnlockRemind(slot) then
			return true, true
		else
			return false, jump_uplock
		end
	end

	-- 出战提醒
	if self:GetSingleOutFightRemind(slot) then
		if to_be_strengthen then
			self:TrainToBeStrengthen(1)
		end
		return true, jump_uplock
	end

	if self:GetSlotSingleLevelRemind(slot) then
		if to_be_strengthen then
			self:TrainToBeStrengthen(1)
		end
		return true, jump_uplock
	end

	if self:GetSlotSingleBreakRemind(slot) then
		if to_be_strengthen then
			self:TrainToBeStrengthen(1)
		end
		return true, jump_uplock
	end

	if ignore_better then
		return false, jump_uplock
	end

	local can_wear = self:GetFightSoulCanWearByType(slot)
	return can_wear, jump_uplock
end

function FightSoulWGData:GetDropToUnlockRemind(slot)
	local cfg = self:GetFightslotCfgBySlot(slot)
	if IsEmptyTable(cfg) then
		return false
	end

	local is_unlock = self:GetSlotLockFlag(slot)
	if not is_unlock and cfg.stuff_id > 0 and cfg.stuff_num > 0 then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg.stuff_id)
		return item_num >= cfg.stuff_num
	end

	return false
end

-- 【养成】红点跳转
function FightSoulWGData:GetJumpSlotIndex(index)
	local jump_slot = 0
	if IsEmptyTable(self.fight_soul_slot_list) then
		return jump_slot
	end

	local is_remind, jump_uplock = false, false
	for k,v in pairs(self.fight_soul_slot_list) do
		if v:GetOutFightFlag() then
			jump_slot = v:GetSlotIndex()
			if index == TabIndex.fight_soul_train then
				is_remind, jump_uplock = self:GetSingleSlotRemind(jump_slot)
			elseif index == TabIndex.fight_soul_bone then
				is_remind, jump_uplock = self:GetBoneSingleSlotRemind(jump_slot)
			elseif index == TabIndex.fight_soul_cuizhuo then
				is_remind, jump_uplock = self:GetCuiZhuoSingleSlotRemind(jump_slot)
			end

			if is_remind and not jump_uplock then
				return jump_slot
			end
		end
	end

	local length = #self.fight_soul_slot_list
	for i = 0, length do
		local data = self.fight_soul_slot_list[i]
		if not data:GetOutFightFlag() then
			if index == TabIndex.fight_soul_train then
				is_remind, jump_uplock = self:GetSingleSlotRemind(data:GetSlotIndex())
			elseif index == TabIndex.fight_soul_bone then
				is_remind, jump_uplock = self:GetBoneSingleSlotRemind(data:GetSlotIndex())
			elseif index == TabIndex.fight_soul_cuizhuo then
				is_remind, jump_uplock = self:GetCuiZhuoSingleSlotRemind(data:GetSlotIndex())
			end

			if is_remind and not jump_uplock then
				return data:GetSlotIndex()
			end
		end
	end

	return jump_slot
end

-- 【养成】获取红点
function FightSoulWGData:GetTrainRemind()
	if not FunOpen.Instance:GetFunIsOpened(FunName.FightSoulView) then
		return 0
	end

	-- 四象上阵
	for slot,v in pairs(self.fight_soul_slot_list) do
		if self:GetFightSoulCanWearByType(slot, true) then
			self:TrainToBeStrengthen(1, MAINUI_TIP_TYPE.FIGHT_SOUL_WEAR)
			return 1
		end
	end
	self:TrainToBeStrengthen(0, MAINUI_TIP_TYPE.FIGHT_SOUL_WEAR)

	-- 四象养成
	for k,v in pairs(self.fight_soul_slot_list) do
		if self:GetSingleSlotRemind(k, true, true) then
			return 1
		end
	end
	self:TrainToBeStrengthen(0)

	return 0
end

function FightSoulWGData:TrainToBeStrengthen(remind, remind_type)
	remind_type = remind_type or MAINUI_TIP_TYPE.FIGHT_SOUL_TRAIN
	MainuiWGCtrl.Instance:InvateTip(remind_type, remind, function ()
		FightSoulWGCtrl.Instance:OpenFightSoulView(TabIndex.fight_soul_train)
		return true
	end)
end

--=============================================================================
--=============================================================================
--=======================          融合        =================================
--=============================================================================
--=============================================================================

-- 【战魂背包】根据品质、星级、类型 缓存对应的所有能当材料的数量 (ps.上阵的不缓存)
function FightSoulWGData:ChangeAllComposeCache()
	local bag_list = self:GetFightSoulBagList()
	for k,v in pairs(bag_list) do
		self:ChangeSingleComposeCache(FIGHT_SOUL_BAG_SEND_REASON.ADD_ITEM, v)
	end
end

-- 【战魂背包】根据品质、星级、类型 缓存对应能当材料的数量 (ps.上阵的不缓存)
function FightSoulWGData:ChangeSingleComposeCache(send_reason, item_data)
	if not item_data then
		return
	end

	local color = item_data.color
	local star = item_data.star
	local type = item_data.fight_soul_type

	self.cache_compose_list[type] = self.cache_compose_list[type] or {}
	self.cache_compose_list[type][color] = self.cache_compose_list[type][color] or {}
	self.cache_compose_list[type][color][star] = self.cache_compose_list[type][color][star] or 0
	local cur_num = self.cache_compose_list[type][color][star]

	if send_reason == FIGHT_SOUL_BAG_SEND_REASON.ADD_ITEM then					--获得
		cur_num = cur_num + 1
	elseif send_reason == FIGHT_SOUL_BAG_SEND_REASON.REMOVE_ITEM then			--移除
		cur_num = cur_num - 1
		cur_num = cur_num > 0 and cur_num or 0
	end

	self.cache_compose_list[type][color][star] = cur_num
end

-- 【战魂背包】根据品质、星级、类型 获取缓存的对应数量
function FightSoulWGData:GetComposeCacheNum(color, star, type)
	local empty_table = {}
	return ((self.cache_compose_list[type] or empty_table)[color] or empty_table)[star] or 0
end

-- 【战魂背包】根据品质、星级、类型 获取缓存的对应数量
function FightSoulWGData:GetComposeCacheNumByList(color_list, star_list, type)
	local empty_table = {}
	local data = self.cache_compose_list[type]
	if data == nil then
		return 0
	end

	local num, total = 0, 0
	for color,_ in pairs(color_list) do
		for star,_ in pairs(star_list) do
			num = (data[color] or empty_table)[star] or 0
			total = total + num
		end
	end
	
	return total
end

-- 【融合】获取战魂背包数据（排序：已穿戴（槽位序号） > 品质  > 星级 > 反背包index）
function FightSoulWGData:SortComposeFightSoulBagList()
	self.sort_compose_bag_list = {}
	local max_bag_num = self:GetFightSoulBagMaxNum()
	local wear_weight, level_weight, type_weight, color_weight, star_weight
	local bag_list = self:GetFightSoulBagList()
	local max_type = 4
	for k,v in pairs(bag_list) do
		local data = {}
		data.item_data = v
		-- 排序
		local is_wear, slot_index = self:GetBagIndexIsWear(v.index)
		wear_weight = is_wear and (max_type - slot_index) * 10000000 or 0
		level_weight = (not is_wear and (v.level > 0 or v.grade > 0)) and -10000000 or 0
		color_weight = is_wear and 0 or v.color * 1000000
		star_weight = is_wear and 0 or v.star * 100000
		type_weight = is_wear and 0 or (max_type - v.fight_soul_type) * 10000

		data.bag_sort_index = wear_weight + level_weight + type_weight + color_weight + star_weight + (max_bag_num - v.index)
		data.stuff_sort_index = 0
		data.select_state = {}
		for i = FIGHT_SOUL_STUFF_TYPE.SAME_TYPE, FIGHT_SOUL_STUFF_TYPE.NOSAME_TYPE_1 do
			data.select_state[i] = false
		end

		table.insert(self.sort_compose_bag_list, data)
	end

	if not IsEmptyTable(self.sort_compose_bag_list) then
		table.sort(self.sort_compose_bag_list, SortTools.KeyUpperSorter("bag_sort_index"))
	end
end

function FightSoulWGData:GetComposeFightSoulBagList()
	return self.sort_compose_bag_list or {}
end

function FightSoulWGData:ClearSingleComposeRemindCache()
	self.single_compose_remind_cache = {}
end

-- 【融合】获取战魂单个物品是否能合成
function FightSoulWGData:GetFightSoulComposeRemind(item_data)
	if item_data == nil then
		return false
	end

	local item_color = item_data.color
	local item_star = item_data.star
	local is_wear = self:GetBagIndexIsWear(item_data.index)

	if self.single_compose_remind_cache[item_color] == nil then
		self.single_compose_remind_cache[item_color] = {}
	end
		
		
	if self.single_compose_remind_cache[item_color][item_star] ~= nil then
		return self.single_compose_remind_cache[item_color][item_star], is_wear
	end
	
	local compose_cfg = self:GetComposeCfg(item_color, item_star)
	if IsEmptyTable(compose_cfg) then
		return false
	end

	local same_need_type = item_data.fight_soul_type
	-- 同类型1
	local same_card_num = compose_cfg.same_card_num
	local same_card_color_list = compose_cfg.same_card_color_list
	local same_card_star_list = compose_cfg.same_card_star_list
	-- 同类型2
	local same_type_num = compose_cfg.same_type_num
	local same_type_color_list = compose_cfg.same_type_color_list
	local same_type_star_list = compose_cfg.same_type_star_list


	local remind = false
	local same_num, same_num2, num = 0, 0, 0
	if same_card_num > 0 and same_type_num > 0 then
		local need_same_card_num = same_card_num
		local need_same_type_num = same_type_num
		local jiaoji_num = 0
		for color,_ in pairs(same_card_color_list) do
			for star,_ in pairs(same_card_star_list) do
				if same_type_color_list[color] and same_type_star_list[star] then
					num = self:GetComposeCacheNum(color, star, same_need_type)
					jiaoji_num = jiaoji_num + num
					-- 自身不当材料(上阵的未加进缓存列表)
					if num > 0 and (not is_wear) and item_color == color and item_star == star then
						jiaoji_num = jiaoji_num - 1
					end
				else
					num = self:GetComposeCacheNum(color, star, same_need_type)
					need_same_card_num = need_same_card_num - num
					-- 自身不当材料(上阵的未加进缓存列表)
					if num > 0 and (not is_wear) and item_color == color and item_star == star then
						need_same_card_num = need_same_card_num + 1
					end
				end
			end
		end

		for color,_ in pairs(same_type_color_list) do
			for star,_ in pairs(same_type_star_list) do
				if not (same_card_color_list[color] and same_card_star_list[star]) then
					num = self:GetComposeCacheNum(color, star, same_need_type)
					need_same_type_num = need_same_type_num - num
					-- 自身不当材料(上阵的未加进缓存列表)
					if num > 0 and (not is_wear) and item_color == color and item_star == star then
						need_same_type_num = need_same_type_num + 1
					end
				end
			end
		end

		if need_same_card_num < 0 and need_same_type_num < 0 then
			remind = true
			self.single_compose_remind_cache[item_color][item_star] = remind
			return remind, is_wear
		end

		need_same_card_num = need_same_card_num > 0 and need_same_card_num or 0
		need_same_type_num = need_same_type_num > 0 and need_same_type_num or 0

		remind = jiaoji_num >= (need_same_card_num + need_same_type_num)
		self.single_compose_remind_cache[item_color][item_star] = remind
		return remind, is_wear

	elseif same_card_num > 0 then
		same_num = self:GetComposeCacheNumByList(same_card_color_list, same_card_star_list, same_need_type)
		-- 自身不当材料(上阵的未加进缓存列表)
		if not is_wear and same_card_color_list[item_color] and same_card_star_list[item_star] then
			same_num = same_num - 1
		end

		remind = same_num >= same_card_num
		self.single_compose_remind_cache[item_color][item_star] = remind
		return remind, is_wear
	elseif same_type_num > 0 then
		same_num2 = self:GetComposeCacheNumByList(same_type_color_list, same_type_star_list, same_need_type)
		-- 自身不当材料(上阵的未加进缓存列表)
		if not is_wear and same_type_color_list[item_color] and same_type_star_list[item_star] then
			same_num2 = same_num2 - 1
		end

		remind = same_num2 >= same_type_num
		self.single_compose_remind_cache[item_color][item_star] = remind
		return remind, is_wear
	end

	self.single_compose_remind_cache[item_color][item_star] = remind
	return false, is_wear
end

-- 【融合】获取展示的当前和下级物品数据
function FightSoulWGData:GetComposeShowItemData(cur_item_data, next_item_id)
	local cur_data = {
		item_id = cur_item_data.item_id or 0,
		is_bind = cur_item_data.is_bind or 0,
		star = cur_item_data.star or 0,
		level = 0,
	}

	local fs_item_cfg = self:GetFightSoulItemCfg(next_item_id)
	local next_data = {
		item_id = next_item_id or 0,
		is_bind = cur_data.is_bind,
		star = fs_item_cfg and fs_item_cfg.base_star or 0,
		level = 0,
	}

	return cur_data, next_data
end

function FightSoulWGData.StuffItemShowData()
	return {item_id = 0, is_bind = 0, star = 0, need_num = 0, had_num = 0, color = 0, stuff_type = 0, icon = 0}
end

-- 【融合】获得合成界面材料展示
-- 依赖排序背包的数据
function FightSoulWGData:GetComposeShowStuffList(bag_index, meet_list)
	local stuff_list = {}
	local is_meet_list = {}
	local is_max = false
	local item_data = self:GetFightSoulItemByBagIndex(bag_index)
	if IsEmptyTable(item_data) then
		return stuff_list, is_meet_list, is_max
	end

	local compose_cfg = self:GetComposeCfg(item_data.color, item_data.star)
	if IsEmptyTable(compose_cfg) then
		is_max = true
		return stuff_list, is_meet_list, is_max
	end

	local function get_had_num(type, list)
		local num = 0
		if IsEmptyTable(list) then
			return num
		end

		for k,v in pairs(list) do
			if v.select_state[type] then
				num = num + 1
			end
		end

		return num
	end

	local is_meet_same, is_meet_nosame, is_meet_nosame2 = false, false, false
	local stuff_type = FIGHT_SOUL_STUFF_TYPE.SAME_TYPE

	-- 材料1
	if compose_cfg.same_card_num > 0 then
		stuff_type = FIGHT_SOUL_STUFF_TYPE.SAME_TYPE
		local temp_data = FightSoulWGData.StuffItemShowData()
		temp_data.star = compose_cfg.same_card_show_star
		temp_data.color = compose_cfg.same_card_show_color
		temp_data.item_id = self:GetItemIdByParam(temp_data.color, temp_data.star, item_data.fight_soul_type)
		temp_data.need_num = compose_cfg.same_card_num
		temp_data.had_num = get_had_num(stuff_type, meet_list[stuff_type])
		temp_data.stuff_type = stuff_type
		table.insert(stuff_list, temp_data)
		is_meet_same = temp_data.had_num >= temp_data.need_num
		is_meet_list[stuff_type] = is_meet_same
	else
		is_meet_same = true
	end

	-- 材料2
	if compose_cfg.same_type_num > 0 then
		stuff_type = FIGHT_SOUL_STUFF_TYPE.NOSAME_TYPE_1
		local temp_data = FightSoulWGData.StuffItemShowData()
		temp_data.star = compose_cfg.same_type_show_star
		temp_data.color = compose_cfg.same_type_show_color
		temp_data.item_id = self:GetItemIdByParam(temp_data.color, temp_data.star, item_data.fight_soul_type)
		temp_data.need_num = compose_cfg.same_type_num
		temp_data.had_num = get_had_num(stuff_type, meet_list[stuff_type])
		temp_data.stuff_type = stuff_type
		table.insert(stuff_list, temp_data)
		is_meet_nosame = temp_data.had_num >= temp_data.need_num
		is_meet_list[stuff_type] = is_meet_nosame
	else
		is_meet_nosame = true
	end

	return stuff_list, is_meet_list, is_max
end

-- 【融合】筛选出当前道具的全部合成材料（排序：类型（和当前相同的 到不同的） > 等级（从小到大））
function FightSoulWGData:GetMeetComposeStuffSortList(item_data, auto_select)
	local meet_list = {}
	for i = FIGHT_SOUL_STUFF_TYPE.SAME_TYPE, FIGHT_SOUL_STUFF_TYPE.NOSAME_TYPE_1 do
		meet_list[i] = {}
	end
	if IsEmptyTable(item_data) then
		return meet_list
	end

	local bag_list = self:GetComposeFightSoulBagList()
	local compose_cfg = self:GetComposeCfg(item_data.color, item_data.star)
	if IsEmptyTable(compose_cfg) then
		for k,v in pairs(bag_list) do
			for i,j in pairs(v.select_state) do
				v.select_state[i] = false
			end
		end

		return meet_list
	end

	-- 筛选+排序
	local need_same = compose_cfg.same_card_num > 0
	local need_same2 = compose_cfg.same_type_num > 0
	-- 同类型1
	local same_card_color_list = compose_cfg.same_card_color_list
	local same_card_star_list = compose_cfg.same_card_star_list
	-- 同类型2
	local same_type_color_list = compose_cfg.same_type_color_list
	local same_type_star_list = compose_cfg.same_type_star_list

	local no_stuff = false
	local is_had_select = false
	local stuff_type = FIGHT_SOUL_STUFF_TYPE.SAME_TYPE
	local color, star, level, grade, index, fight_soul_type

	local weight, level_weifht = 0, 0
	for k,v in pairs(bag_list) do
		color = v.item_data.color
		star = v.item_data.star
		level = v.item_data.level
		grade = v.item_data.grade
		index = v.item_data.index
		fight_soul_type = v.item_data.fight_soul_type
		no_stuff = self:GetBagIndexIsWear(index) or item_data.index == index

		-- dog sun 任意类型的材料 和相同类型的材料存在同品质同星级的情况
		is_had_select = false
		for i,j in pairs(v.select_state) do
			if auto_select then
				v.select_state[i] = false
			else
				if j then
					is_had_select = true
					break
				end
			end
		end
		
		if need_same and not no_stuff and fight_soul_type == item_data.fight_soul_type then
			if same_card_color_list[color] and same_card_star_list[star] then
				stuff_type = FIGHT_SOUL_STUFF_TYPE.SAME_TYPE
				if not is_had_select or (is_had_select and v.select_state[stuff_type]) then
					level_weifht = (grade > 0 or level > 0) and 100000 or 0
					weight = color * 10000 + star * 100
					v.stuff_sort_index =  weight + level_weifht + index
					table.insert(meet_list[stuff_type], v)
				end
			end
		end

		if need_same2 and not no_stuff and fight_soul_type == item_data.fight_soul_type then
			if same_type_color_list[color] and same_type_star_list[star] then
				stuff_type = FIGHT_SOUL_STUFF_TYPE.NOSAME_TYPE_1
				if not is_had_select or (is_had_select and v.select_state[stuff_type]) then
					level_weifht = (grade > 0 or level > 0) and 100000 or 0
					weight = color * 10000 + star * 100
					v.stuff_sort_index =  weight + level_weifht + index
					table.insert(meet_list[stuff_type], v)
				end
			end
		end
	end

	for i = FIGHT_SOUL_STUFF_TYPE.SAME_TYPE, FIGHT_SOUL_STUFF_TYPE.NOSAME_TYPE_1 do
		if not IsEmptyTable(meet_list[i]) then
			table.sort(meet_list[i], SortTools.KeyLowerSorter("stuff_sort_index"))
		end
	end

	-- 帮玩家自动选择
	if auto_select then
		meet_list = self:ComposeBagAutoSelect(compose_cfg, meet_list)
	end

	return meet_list
end

-- dog shit 任意类型的材料 和相同类型的材料存在同品质同星级的情况, 选择过的不能再选
function FightSoulWGData:ComposeBagAutoSelect(compose_cfg, meet_list)
	if IsEmptyTable(compose_cfg) then
		return meet_list
	end

	local stuff_num_list = {
		[FIGHT_SOUL_STUFF_TYPE.SAME_TYPE] = compose_cfg.same_card_num,
		[FIGHT_SOUL_STUFF_TYPE.NOSAME_TYPE_1] = compose_cfg.same_type_num,
	}

	self:CleanComposeSelectCache()
	local stuff_need_num = 0
	local add_num_flag = 0
	local is_had_select = false

	for stuff_type, list in ipairs(meet_list) do
		add_num_flag = 0
		stuff_need_num = stuff_num_list[stuff_type]
		for k,data in ipairs(list) do
			is_had_select = FightSoulWGData.CheckCurStuffIsSelect(data.select_state)

			if not is_had_select then
				if add_num_flag < stuff_need_num then
					add_num_flag = add_num_flag + 1
					data.select_state[stuff_type] = true
					self:AddComposeSelectCache(data.item_data.index)
				end
			end
		end
	end

	-- 有一方选了 就要在另一方将材料中移除
	local length = 0
	local other_select = false
	for stuff_type, list in ipairs(meet_list) do
		length = #list
		for i = length, 1, -1 do
			local data = list[i]
			other_select = false
			for i,j in pairs(data.select_state) do
				if i ~= stuff_type and j then
					other_select = true
					break
				end
			end

			if other_select then
				table.remove(meet_list[stuff_type], i)
			end
		end
	end

	return meet_list
end

-- 【融合】清除 融合界面选择缓存
function FightSoulWGData:CleanComposeSelectCache()
	self.compose_select_cache = {}
end

-- 【融合】获取 融合界面选择缓存
function FightSoulWGData:GetComposeSelectCache()
	return self.compose_select_cache
end

-- 【融合】添加 融合界面选择缓存
function FightSoulWGData:AddComposeSelectCache(bag_index)
	self.compose_select_cache[bag_index] = true
end

-- 【融合】移除 融合界面选择缓存
function FightSoulWGData:RemoveComposeSelectCache(bag_index)
	self.compose_select_cache[bag_index] = nil
end

-- 【融合】获取融合红点
function FightSoulWGData:GetComposeRemind()
	if not FunOpen.Instance:GetFunIsOpened(FunName.FightSoulView) then
		return 0
	end

	local bag_list = self:GetComposeFightSoulBagList()
	local remind, is_wear
	for k,v in ipairs(bag_list) do
		remind, is_wear = self:GetFightSoulComposeRemind(v.item_data)
		if remind then
			self:ComposeToBeStrengthen(is_wear and 1 or 0)
			return 1
		end
	end

	self:ComposeToBeStrengthen(0)
	return 0
end

function FightSoulWGData:ComposeToBeStrengthen(remind)
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FIGHT_SOUL_COMPOSE, remind, function ()
		FightSoulWGCtrl.Instance:OpenFightSoulView(TabIndex.fight_soul_compose)
		return true
	end)
end

function FightSoulWGData:GetSkillNuQiData()
	local role_vo = RoleWGData.Instance:GetRoleVo()
	local cur_nuqi = role_vo.nuqi or 0
	local need_nuqi = self:GetSkillNeedNuQi()
	return cur_nuqi, need_nuqi, cur_nuqi >= need_nuqi
end