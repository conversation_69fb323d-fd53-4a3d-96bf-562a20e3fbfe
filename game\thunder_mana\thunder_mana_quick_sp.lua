-- 雷法快捷升品
ThunderManaQuickSp = ThunderManaQuickSp or BaseClass(SafeBaseView)

function ThunderManaQuickSp:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(706, 486)})
    self:AddViewResource(0, "uis/view/thunder_mana_ui_prefab", "layout_thunder_mana_quick_compose")
    self:SetMaskBg(true)
end

function ThunderManaQuickSp:ReleaseCallBack()
    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end

    if nil ~= self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end

    self.is_click_sure = false
    self.curr_list = nil
    self.select_all = true
end

function ThunderManaQuickSp:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.ContralBeasts.ComposeTips7
    self.node_list.tip_text.text.text = Language.ThunderMana.ComposeSpTips1
    self.node_list.lbl_tips.text.text = Language.ThunderMana.ComposeSpTips2

    self.item_list = AsyncListView.New(ThunderManaShengPinRender, self.node_list.ph_item_list)
    XUI.AddClickEventListener(self.node_list["sure_btn"], BindTool.Bind(self.OnClickSure, self))
    XUI.AddClickEventListener(self.node_list["all_mark"], BindTool.Bind(self.OnClickAllSelect, self))
end

function ThunderManaQuickSp:OnSelectShengPinCB(beasts_item)
    if not beasts_item.data then
        return
    end

    self:SetSelectStatusByIndex(beasts_item.index, beasts_item.data.select)
    self:FlushList()
end

function ThunderManaQuickSp:OnClickAllSelect()
    local select_all = not self.select_all
    self:SetSelectStatus(select_all)
    self:FlushList()
end

function ThunderManaQuickSp:OnFlush()
    self.curr_list = ThunderManaWGData.Instance:GetQuickBagComposeData()
    self:FlushList()
end

function ThunderManaQuickSp:FlushList()
    if not self.curr_list then
        return
    end
    
    self.item_list:SetDataList(self.curr_list)
    self.node_list.img_no_record:SetActive(#self.curr_list == 0)
    self.node_list.all_mark:SetActive(#self.curr_list > 0)

    self.select_all = self:GetIsSelectFull()
    self.node_list.gou:SetActive(self.select_all)
end

function ThunderManaQuickSp:SetSelectStatus(is_select)
    if not self.curr_list then
        return
    end
    
    for index, data in ipairs(self.curr_list) do
        data.select = is_select
    end
end

function ThunderManaQuickSp:SetSelectStatusByIndex(select_index, select)
    if not self.curr_list then
        return
    end
    
    for index, data in ipairs(self.curr_list) do
        if select_index == index then
            data.select = select
        end
    end
end

function ThunderManaQuickSp:GetIsSelectFull()
    if not self.curr_list then
        return true
    end
    
    for _, data in ipairs(self.curr_list) do
        if data and (not data.select) then
            return false
        end
    end

    return true
end


function ThunderManaQuickSp:OnClickSure()
    if not self.curr_list then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ThunderMana.ComposeSpTips2)
        return
    end

    local compose_list = {}
    for _, compose_data in ipairs(self.curr_list) do
        if compose_data and compose_data.select then
            local stuff_table = {}
            if compose_data.special_list then
                for i, v in ipairs(compose_data.special_list) do
                    local stuff_data = {}
                    stuff_data.bag_index = ThunderManaWGData.Instance:GetItemIndex(v.cur_item_id)
                    stuff_data.item_num = v.cur_item_num
                    table.insert(stuff_table, stuff_data)
                end
            end
    
            if compose_data.star_list then
                for k, v in pairs(compose_data.star_list) do
                    for m, n in ipairs(v) do
                        local stuff_data = {}
                        stuff_data.bag_index = ThunderManaWGData.Instance:GetItemIndex(n.cur_item_id)
                        stuff_data.item_num = n.cur_item_num
                        table.insert(stuff_table, stuff_data)
                    end
                end
            end

            local final_data = {}
            final_data.seq = compose_data.equip_seq
            final_data.stuff_count = #stuff_table
            final_data.param = compose_data.param
            final_data.stuff_item_list = stuff_table
            table.insert(compose_list, final_data)
        end
    end

    ThunderManaWGCtrl.Instance:SendCSThunderEquipUpStar(#compose_list, compose_list)
end

-------------------------------------------------------------------------------------------
ThunderManaShengPinRender = ThunderManaShengPinRender or BaseClass(BaseRender)

function ThunderManaShengPinRender:__delete()
	if self.cell_list then
		for k,v in pairs(self.cell_list) do
			v:DeleteMe()
		end
		self.cell_list = nil
    end

    if self.target_item then
        self.target_item:DeleteMe()
        self.target_item = nil
    end

    if self.must_item then
        self.must_item:DeleteMe()
        self.must_item = nil
    end
end

function ThunderManaShengPinRender:LoadCallBack()
	self.cell_list = {}
	for i = 1, 4 do
		self.cell_list[i] = ItemCell.New(self.node_list["content"])
	end
    self.target_item = ItemCell.New(self.node_list["target_item"])
    self.must_item = ItemCell.New(self.node_list["must_item"])

    XUI.AddClickEventListener(self.node_list["check_mark"], BindTool.Bind(self.ClickMark, self))
end

function ThunderManaShengPinRender:ClickMark()
    self.data.select = not self.data.select
    self.node_list.gou:SetActive(self.data.select)

    if ThunderManaWGCtrl.Instance.thunder_mana_quick_sp then
        ThunderManaWGCtrl.Instance.thunder_mana_quick_sp:OnSelectShengPinCB(self)
    end
end

function ThunderManaShengPinRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list.gou:SetActive(self.data.select)
    local must = {}
    must.item_id = self.data.self_item_id
    self.must_item:SetData(must)
    self.node_list.must_item_is_equip:CustomSetActive(self.data.equip_seq ~= -1)

	local cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(self.data.self_item_id)
    if cfg and cfg.next_star_id then
        local target = {}
        target.item_id = cfg.next_star_id
        self.target_item:SetData(target)
    end

    local stuff_table = {}
    if self.data.special_list then
        for _, special_beast in ipairs(self.data.special_list) do
            local stuff_data = {}
            stuff_data.item_id = special_beast.cur_item_id
            stuff_data.num = special_beast.cur_item_num
            table.insert(stuff_table, stuff_data)
        end
    end

    if self.data.star_list then
        for satr, star_list in pairs(self.data.star_list) do
            for _, star_data in pairs(star_list) do
                local stuff_data = {}
                stuff_data.item_id = star_data.cur_item_id
                stuff_data.num = star_data.cur_item_num
                table.insert(stuff_table, stuff_data)
            end
        end
    end

    for i = 1, 4 do
        if stuff_table[i] then
            self.cell_list[i]:SetActive(true) 
            self.cell_list[i]:SetData(stuff_table[i])
        else
            self.cell_list[i]:SetActive(false) 
        end
    end
end