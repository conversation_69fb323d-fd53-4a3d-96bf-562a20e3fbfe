-- 兑换商店
XunYuanFreshPoolShop = XunYuanFreshPoolShop or BaseClass(SafeBaseView)

function XunYuanFreshPoolShop:__init()
    -- self.view_layer = UiLayer.Pop
    self:AddViewResource(0, "uis/view/xuyuan_freshpool_ui_prefab", "xuyuan_freshpool_shop")
    self:SetMaskBg(true, true)
end

function XunYuanFreshPoolShop:ReleaseCallBack()
    if self.goods_list then
        self.goods_list:DeleteMe()
        self.goods_list = nil
    end
end

function XunYuanFreshPoolShop:LoadCallBack()
    if not self.goods_list then
        self.goods_list = AsyncBaseGrid.New()
        self.goods_list:SetStartZeroIndex(false)
        local bundle, asset = "uis/view/xuyuan_freshpool_ui_prefab", "goods_item"
		self.goods_list:CreateCells({
			col = 4,
			change_cells_num = 1,
			list_view = self.node_list.goods_list,
			assetBundle = bundle,
			assetName = asset,
			itemRender = XuYuanGoodsItem
		})
    end

    XUI.AddClickEventListener(self.node_list.buy_btn, BindTool.Bind(self.BuyBigAward, self))
    XUI.AddClickEventListener(self.node_list.assets_icon, BindTool.Bind(self.OnClickAssets, self))
end

function XunYuanFreshPoolShop:OnFlush()
    local big_award, data_list = XuYuanFreshPoolWGData.Instance:GetShopData()
    self.goods_list:SetDataList(data_list)
    self:FlushBigAward(big_award)
    self:FlushOwnAssets()
end

function XunYuanFreshPoolShop:FlushBigAward(big_award)
    if not big_award then
        return
    end
    local bundle, asset = ResPath.GetRawImagesPNG(big_award.big_reward)
    self.node_list.show_big_award.raw_image:LoadSprite(bundle, asset, function()
        self.node_list.show_big_award.raw_image:SetNativeSize()
    end)

    local item_id = big_award.stuff_id
    local score_itemcfg = ItemWGData.Instance:GetItemConfig(item_id)
    if score_itemcfg then
        local bundle, asset = ResPath.GetItem(score_itemcfg.icon_id)
        self.node_list.price_icon.image:LoadSprite(bundle, asset)
    end
    self.node_list.price.text.text = string.format("%s", big_award.stuff_count)
    
    local buyed_num = XuYuanFreshPoolWGData.Instance:GetGoodsBuyNum(big_award.seq)
    local can_buy_num = big_award.limit_num - buyed_num
    self.node_list.buy_btn:CustomSetActive(can_buy_num > 0)
    self.node_list.sold_out_tag:CustomSetActive(can_buy_num <= 0)

    local str = can_buy_num
    local color = is_sold_out and COLOR3B.RED or COLOR3B.GREEN
    str = ToColorStr(str, color)
    str = string.format(Language.XuYuanFreshPool.BuyNum, str, big_award.limit_num)
    self.node_list.limit_num.text.text = str
end

function XunYuanFreshPoolShop:BuyBigAward()
    local data, data_list = XuYuanFreshPoolWGData.Instance:GetShopData()
    local item_info = data.reward_item[0]
    if data and item_info then
        local can_buy_num = data.limit_num - XuYuanFreshPoolWGData.Instance:GetGoodsBuyNum(data.seq)
        -- if can_buy_num <= 0 then
        --     TipWGCtrl.Instance:ShowSystemMsg(Language.WorldTreasure.Soldout)
        --     return
        -- end
    
        local num = item_info.num
        local empty = ItemWGData.Instance:GetEmptyNum()
        local item_cfg = ItemWGData.Instance:GetItemConfig(item_info.item_id)
    
        if empty < num and item_cfg and item_cfg.not_put_flag == 0 then
            RoleBagWGData.Instance:SetRoleBagCleanFlag(true)
            RoleBagWGCtrl.Instance:OpenRoleBagCleanTips(KNAPSACK_TYPE.NORMAL)
            return
        end
        
        local buy_func = function(num)
            TipWGCtrl.Instance:ShowGetItem(item_info)
            local buy_num = num or 1
            XuYuanFreshPoolWGCtrl.Instance:SendReq(RA_YANHUA_SHENGDIAN3_OP_TYPE.EXCHANGE_SHOP, data.seq, buy_num)
        end
    
        local tips_data = {}
        tips_data.title_view_name = Language.Common.ExchangeItemTipsTitle
        tips_data.item_id = item_info.item_id
        tips_data.expend_item_id = data.stuff_id
        tips_data.expend_item_num = data.stuff_count
        tips_data.max_buy_count = can_buy_num
        tips_data.is_show_limit = true
        TipWGCtrl.Instance:OpenCustomBuyItemTipsView(tips_data, buy_func)
    end
end

function XunYuanFreshPoolShop:FlushOwnAssets()
    local item_id = XuYuanFreshPoolWGData.Instance:GetShopAssets()
    local score_itemcfg = ItemWGData.Instance:GetItemConfig(item_id)
    if score_itemcfg then
        local bundle, asset = ResPath.GetItem(score_itemcfg.icon_id)
        self.node_list.assets_icon.image:LoadSprite(bundle, asset)
    end

    local own_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
    self.node_list.assets_num.text.text = own_num
end

function XunYuanFreshPoolShop:OnClickAssets()
    local item_id = XuYuanFreshPoolWGData.Instance:GetShopAssets()
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
end

------------------------------ 兑换商店item ----------------------------------------
XuYuanGoodsItem = XuYuanGoodsItem or BaseClass(BaseRender)

function XuYuanGoodsItem:ReleaseCallBack()
    if self.item_obj then
        self.item_obj:DeleteMe()
        self.item_obj = nil
    end
end

function XuYuanGoodsItem:LoadCallBack()
    self.item_obj = ItemCell.New(self.node_list.item)
    self.item_obj:NeedDefaultEff(false)
    self.item_obj:SetCellBgEnabled(false)
    self.item_obj:SetShowCualityBg(false)
    
    XUI.AddClickEventListener(self.node_list.buy_btn, BindTool.Bind(self.BuyGoods, self))
end

function XuYuanGoodsItem:OnFlush()
    local data = self.data
    if not data then
        return
    end

    local buyed_num = XuYuanFreshPoolWGData.Instance:GetGoodsBuyNum(data.seq)
    local is_sold_out = buyed_num >= data.limit_num
    self.node_list.sold_out_tag:CustomSetActive(is_sold_out)
    self.node_list.buy_btn:CustomSetActive(not is_sold_out)
    local str = data.limit_num - buyed_num
    local color = is_sold_out and COLOR3B.RED or COLOR3B.GREEN
    str = ToColorStr(str, color)
    str = string.format("%s/%s", str, data.limit_num)
    self.node_list.limit_num.text.text = str
    local stuff_item_id = data.stuff_id
    -- local own_num = ItemWGData.Instance:GetItemNumInBagById(stuff_item_id)
    -- local color = own_num >= self.data.stuff_count and COLOR3B.GREEN or COLOR3B.RED
    self.node_list.price.text.text = string.format("%s", data.stuff_count)
    local score_itemcfg = ItemWGData.Instance:GetItemConfig(stuff_item_id)
    if score_itemcfg then
        local bundle, asset = ResPath.GetItem(score_itemcfg.icon_id) --icon_id
        self.node_list.price_icon.image:LoadSprite(bundle, asset)
    end

    local bundle, asset = ResPath.GetXuYuanFleshPoolImg("a3_sslx_dhsd_djdi_" .. data.color)
    self.node_list.bg.image:LoadSprite(bundle, asset, function()
        self.node_list.bg.image:SetNativeSize()
    end)
    
    local item_info = data.reward_item[0]
    self.item_obj:SetData(item_info)
    self.node_list.goods_name.text.text = ItemWGData.Instance:GetItemName(item_info and item_info.item_id, nil, false)
end

function XuYuanGoodsItem:BuyGoods()
    local data = self.data
    local item_info = data.reward_item[0]
    if data and item_info then
        local buyed_num = XuYuanFreshPoolWGData.Instance:GetGoodsBuyNum(data.seq)
        local can_buy_num = data.limit_num - buyed_num
        if can_buy_num <= 0 then
            TipWGCtrl.Instance:ShowSystemMsg(Language.WorldTreasure.Soldout)
            return
        end
    
        local num = item_info.num
        local empty = ItemWGData.Instance:GetEmptyNum()
        local item_cfg = ItemWGData.Instance:GetItemConfig(item_info.item_id)
    
        if empty < num and item_cfg and item_cfg.not_put_flag == 0 then
            RoleBagWGData.Instance:SetRoleBagCleanFlag(true)
            RoleBagWGCtrl.Instance:OpenRoleBagCleanTips(KNAPSACK_TYPE.NORMAL)
            return
        end
        
        local buy_func = function(num)
            TipWGCtrl.Instance:ShowGetItem(item_info)
            local buy_num = num or 1
            XuYuanFreshPoolWGCtrl.Instance:SendReq(RA_YANHUA_SHENGDIAN3_OP_TYPE.EXCHANGE_SHOP, data.seq, buy_num)
        end
    
        local tips_data = {}
        tips_data.title_view_name = Language.Common.ExchangeItemTipsTitle
        tips_data.item_id = item_info.item_id
        tips_data.expend_item_id = data.stuff_id
        tips_data.expend_item_num = data.stuff_count
        tips_data.max_buy_count = can_buy_num
        tips_data.is_show_limit = true
        TipWGCtrl.Instance:OpenCustomBuyItemTipsView(tips_data, buy_func)
    end
end