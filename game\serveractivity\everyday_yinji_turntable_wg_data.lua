EveryDayYinJiTurnTableWGData = EveryDayYinJiTurnTableWGData or BaseClass()

function EveryDayYinJiTurnTableWGData:__init()
    if nil ~= EveryDayYinJiTurnTableWGData.Instance then
        ErrorLog("[EveryDayYinJiTurnTableWGData]:Attempt to create singleton twice!")
    end
    EveryDayYinJiTurnTableWGData.Instance = self
    self:InitConfig()
    self.first_login = true
    self.is_jump_ani = false
    self.yinji_recharge_count = 0
    self.count_draw = 0
    RemindManager.Instance:Register(RemindName.DailyRecharge_YinJI, BindTool.Bind(self.IsShowYinJiTurnTableRed, self))
end

function EveryDayYinJiTurnTableWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.DailyRecharge_YinJI)

    EveryDayYinJiTurnTableWGData.Instance = nil
end

function EveryDayYinJiTurnTableWGData:InitConfig()
	self.yinji_turntable_config = ConfigManager.Instance:GetAutoConfig("yinjizhuanpan_auto")

	self.yinji_reward_cfg = ListToMap(self.yinji_turntable_config.rewardpool,"reward_id")

	self.yinji_dailyrecharge_cfg = ListToMap(self.yinji_turntable_config.dailyrecharge,"id")

	self.yinji_other_cfg = self.yinji_turntable_config.other[1]
	self.item_random_desc_cfg = self.yinji_turntable_config.item_random_desc
end

function EveryDayYinJiTurnTableWGData:IsShowYinJiTurnTableRed()
	if not FunOpen.Instance:GetFunIsOpened(FunName.EveryDayYinJiTurnTable) then
		return 0
	end

	if self.yinji_dailyrecharge_cfg then
		local recharge_num = self:GetYinJiRechargeCount()
		for k,v in pairs(self.yinji_dailyrecharge_cfg) do
			if recharge_num >= v.price and not self:GetTaskIsGet(v.id) then
				return 1
			end
		end
	end

	local cost_item = EveryDayYinJiTurnTableWGData.Instance:GetCostItem()
	if cost_item > 0 then
		local have_num = ItemWGData.Instance:GetItemNumInBagById(cost_item)
		if have_num > 0 then
			return 1
		end
	end

	local left_count = EveryDayYinJiTurnTableWGData.Instance:GetLeftCanGetCount()
	if left_count > 0 then
		return 1
	end

	-- if self.first_login then
	-- 	return 1
	-- end

	if self:GetYinJiDailyRewardFlag() == 0 then
		return 1
	end

    return 0
end

function EveryDayYinJiTurnTableWGData:SetFirstOpen(value)
	if self.first_login then
		RemindManager.Instance:Fire(RemindName.DailyRecharge_YinJI)
	end
	self.first_login = value
end

function EveryDayYinJiTurnTableWGData:OnSCSYinJiZhuanPanInfo(protocol)
	self.yinji_recharge_count = protocol.price_recharge
	self.count_draw = protocol.count_draw
	self.get_count_leichou = protocol.get_count_leichou
	self.person_draw_log = protocol.person_draw_log
	self.daily_reward_record = protocol.daily_reward_record
	self.server_draw_log_count = protocol.server_draw_log_count
	self.server_draw_log = protocol.server_draw_log
end

function EveryDayYinJiTurnTableWGData:OnSCYinJiZhuanPanDailyReward(protocol)
	local get_reward_task_id = protocol.id
	if not self.daily_reward_record then
		self.daily_reward_record = {}
	end
	self.daily_reward_record[get_reward_task_id] = 1
end

function EveryDayYinJiTurnTableWGData:OnSCYinJiZhuanPanDrawReward(protocol)
	self.count_draw = protocol.count_draw
	self.cur_count_draw = protocol.reward_count
	self.reward_info = protocol.reward_info
	local nine_index = -1
	local ten_index = -1
	if self.cur_count_draw == 10 then
		if self.reward_info[9] > self.reward_info[10] then
			local index = self.reward_info[10]
			self.reward_info[10] = self.reward_info[9]
			self.reward_info[9] = index
		end
	end

	if not self.person_draw_log then
		self.person_draw_log = protocol.person_draw_log
	elseif not IsEmptyTable(protocol.person_draw_log) then
		for k,v in pairs(self.person_draw_log) do	
			table.insert(protocol.person_draw_log,v)
		end
		self.person_draw_log = protocol.person_draw_log
	end

end

function EveryDayYinJiTurnTableWGData:ClearLastDrawCountInfo()
	self.cur_count_draw = 0
	self.reward_info = nil
end


function EveryDayYinJiTurnTableWGData:GetYinJiCurDrawCount()
	return self.cur_count_draw or 0
end

function EveryDayYinJiTurnTableWGData:GetNeedShowHLEffect(turn_time,index)
	if turn_time > 1 and self.reward_info then
		turn_time = (turn_time - 2) * 2
		if self.reward_info[turn_time + 1] == index then
			return true,self.cur_count_draw == 1
		end
		if self.reward_info[turn_time + 2] == index then
			return true,turn_time + 2 == 10
		end
	end
	return false,false
end

function EveryDayYinJiTurnTableWGData:OnSCYinJiZhuanPanLeiChouReward(protocol)
	self.get_count_leichou = protocol.count_total
	local once_get_count = protocol.count --一次性领取了多少次累计奖励
end

function EveryDayYinJiTurnTableWGData:OnSCYinJiZhuanPanNewServerLog(protocol)
	if not self.server_draw_log then
		self.server_draw_log = protocol.server_draw_log
	elseif not IsEmptyTable(protocol.server_draw_log) then
		for k,v in pairs(self.server_draw_log) do
			table.insert(protocol.server_draw_log,v)
		end
		self.server_draw_log = protocol.server_draw_log
	end
end

function EveryDayYinJiTurnTableWGData:OnSCYinJiZhuanPanDailyRecharge(protocol)
	self.yinji_recharge_count = self.yinji_recharge_count + protocol.recharge
end

function EveryDayYinJiTurnTableWGData:OnSCYinJiZhuanPanDailyLoginReward(protocol)
	self.yinji_daily_reward_flag = protocol.reward_flag
end

function EveryDayYinJiTurnTableWGData:GetYinJiDailyRewardFlag()
	return self.yinji_daily_reward_flag or 0
end

function EveryDayYinJiTurnTableWGData:GetYinJiRechargePrice()
	return self.yinji_recharge_count or 0
end

function EveryDayYinJiTurnTableWGData:GetYinJiDrawCount()
	return self.count_draw or 0
end

function EveryDayYinJiTurnTableWGData:GetYinJiGetDrawCount()
	return self.get_count_leichou or 0
end

function EveryDayYinJiTurnTableWGData:GetDailyYinJiRewardID()
	return self.daily_reward_item or 0
end

function EveryDayYinJiTurnTableWGData:GetDailyLeiChouRewardGetCount()
	return self.count or 0 ,self.count_total or 0
end

function EveryDayYinJiTurnTableWGData:GetYinJiTurnTableRewardPool()
	return self.yinji_reward_cfg or {}
end

function EveryDayYinJiTurnTableWGData:GetLastRewardIndex()
	local last_index = -1
	if self.cur_count_draw == 10 then
		last_index = self.yinji_reward_cfg[self.reward_info[10]].reward_seq
	else
		last_index = self.yinji_reward_cfg[self.reward_info[1]].reward_seq
	end

	return last_index
end

function EveryDayYinJiTurnTableWGData:GetRechargeTaskListCfg()
	local finish_list = {}
	local not_finish_list = {}
	local daily_reward_list = self:GetDailyRewardList()

	if self:GetYinJiDailyRewardFlag() == 1 then
		table.insert(finish_list,daily_reward_list)
	else
		table.insert(not_finish_list,daily_reward_list)
	end

	for k,v in pairs(self.yinji_dailyrecharge_cfg) do
		if self:GetTaskIsGet(v.id) then
			table.insert(finish_list,v)
		else
			table.insert(not_finish_list,v)
		end
	end

	for t,v in pairs(finish_list) do
		table.insert(not_finish_list,v)
	end
	return not_finish_list
end

function EveryDayYinJiTurnTableWGData:GetTaskIsGet(id)
	if self.daily_reward_record and self.daily_reward_record[id] == 1 then
		return true
	end
	return false
end

function EveryDayYinJiTurnTableWGData:GetTenCostNum()
	return self.yinji_other_cfg.multi_consume.num or 9
end

function EveryDayYinJiTurnTableWGData:GetOneCostNum()
	return self.yinji_other_cfg.single_consume.num or 0
end

function EveryDayYinJiTurnTableWGData:GetOneCostXianYu()
	return self.yinji_other_cfg.xianyu_count or 40
end

function EveryDayYinJiTurnTableWGData:GetCostItem()
	return self.yinji_other_cfg.single_consume.item_id or 0
end

function EveryDayYinJiTurnTableWGData:GetYinJiRechargeCount()
	return self.yinji_recharge_count or 0
end

function EveryDayYinJiTurnTableWGData:GetDailyRewardList()
	return self.yinji_other_cfg.reward_list or {}
end

function EveryDayYinJiTurnTableWGData:GetPerSonalRecord()
	return self.person_draw_log or {}
end

function EveryDayYinJiTurnTableWGData:GetServerRecord()
	return self.server_draw_log or {}
end

function EveryDayYinJiTurnTableWGData:SetIsJumpAnim(value)
	self.is_jump_ani = value
end

function EveryDayYinJiTurnTableWGData:GetIsJumpAnim()
	return self.is_jump_ani
end

function EveryDayYinJiTurnTableWGData:SaveTurnTimeAndIsJump(turn_time,is_jump_ani)
	self.cur_turn_time = turn_time
	self.is_jump_ani = is_jump_ani
end

function EveryDayYinJiTurnTableWGData:GetRewardDelayTime()
	if self.is_jump_ani then
		return 0
	end

	if self.cur_turn_time == 1 then
		return 2.5
	end

	if self.cur_turn_time == 10 then
		return 5.5
	end
	return 0
end

function EveryDayYinJiTurnTableWGData:GetCountRewardItem()
	return self.yinji_other_cfg.count_item or {}
end

function EveryDayYinJiTurnTableWGData:GetLeftCanGetCount()
	if not self.get_count_leichou or not self.count_draw then
		return 0
	end

	local need_count = self.yinji_other_cfg.count or 100
	local more_time = self.count_draw - self.get_count_leichou * need_count
	return math.floor(more_time / need_count)
end

function EveryDayYinJiTurnTableWGData:GetNeedTurnTimesToGet()
	local need_count = self.yinji_other_cfg.count or 100
	return need_count - self.count_draw % need_count
end

function EveryDayYinJiTurnTableWGData:GetTotalTurnTimesToGet()
	local total_count = self.yinji_other_cfg.count or 100
	return total_count
end

--星星阶段次数
function EveryDayYinJiTurnTableWGData:GetStarValue()
	local total_count = self.yinji_other_cfg.count or 100
	total_count = total_count / 10
	local str_value = {}
	for i = 1, 10 do
		str_value[i] = i * total_count
	end

	return str_value
end

function EveryDayYinJiTurnTableWGData:ShowGetRewardPanel(again_func)

	if not self.reward_info then return end
	local data_list = {}
	for k,v in pairs(self.reward_info) do
		if self.yinji_reward_cfg[v] then
			data_list[k] = self.yinji_reward_cfg[v].reward_item or {}
		end
	end
	if IsEmptyTable(data_list) then return end
	local other_info = {}
	other_info.again_btn = true
	local cur_count = self:GetYinJiCurDrawCount()
	cur_count = cur_count > 1 and 2 or 1
	other_info.again_text = Language.TreasureHunt.BtnText[cur_count]
 	TipWGCtrl.Instance:ShowGetReward(nil, data_list, nil, again_func, other_info, nil, true)
 	RemindManager.Instance:Fire(RemindName.Equipment_NewYinJi)
end

function EveryDayYinJiTurnTableWGData:GetRewardIDGetEffectName(reward_id) --抽到的时候的特效
	return self.yinji_reward_cfg[reward_id].tx_cj or ""
end

function EveryDayYinJiTurnTableWGData:GetRewardIDLightEffectName(reward_id) --一直亮的特效
	return self.yinji_reward_cfg[reward_id].tx_dl or ""
end

function EveryDayYinJiTurnTableWGData:GetRewardIDShowEffectName(reward_id) --随机扩散特效
	return self.yinji_reward_cfg[reward_id].tx_ks or ""
end

function EveryDayYinJiTurnTableWGData:GetRandomGaiLvinfo()
	return self.item_random_desc_cfg
end