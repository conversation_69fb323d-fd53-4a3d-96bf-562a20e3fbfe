-------------------------------------------
--管理主角角色数据
--------------------------------------------
RoleWGData = RoleWGData or BaseClass(BaseEvent)
RoleWGData.ATTR_EVENT = "role_attr_event"	--角色属性变化
local ServerListRoleAttr = "ServerListRoleAttr"
local empty_table = {}
function RoleWGData:__init()
	if RoleWGData.Instance then
		ErrorLog("[RoleWGData] Attemp to create a singleton twice !")
	end
	RoleWGData.Instance = self
	self:AddEvent(RoleWGData.ATTR_EVENT)
	self.role_info_is_ok = false							--是否已收到主角信息
	self.role_vo = GameVoManager.Instance:GetMainRoleVo()	--角色公共信息，如血，经验等
	self.role_info = self.role_vo							--这里的做法是兼容2D项目的写法
	self.exp_extra_per = 0
	self.exp_delta = 0
	self.exp_reason = 0
	self.day_first_login_flag = true                        --当日首次登录
	self.today_kanjia_cnt = 0                               --今日砍价次数

	self.origin_capability = 0 								--保存服务端下发的战力

	self.old_gold_value = 0
	self.old_bind_gold_value = 0
	self.old_silver_ticket_value = 0
	self.old_coin_value = 0
	self.old_shengwang_value = 0
	self.old_chivalrous_value = 0
	self.old_cash_point_value = 0


	self.notify_callback_map = {}
	self.capability_list = {}
	self.role_skill_order = {}
	self.role_skill_play_flag = {}
	self.role_bianshenskill_order = {}
	self.role_bianshenskill_play_flag = {}
	self.role_kanjia_gift_list = {}
	-- cocos2d项目暂时屏蔽
	self:InitRoleTalentData()
	self:InitSkillUpData()

	self.custom_icon_path = {}
	-- 这些属性有改变的话，就保存到缓存
	self.need_cache_attr = {
		level = 1,
		prof = 1,
		-- guild_id = 1,
		vip_level = 1,

		--------这些属性在其它系统里---------
		guild_level = 1, -- 仙盟等级
		word_level = 1, -- 世界等级
		cur_day = 1, -- 开服天数
		--------这些属性在其它系统里---------
	}

	self.notify_callback_list = {}
	self.attr_to_notify_map = {}
	self.wait_invoke_list = {}
	self.wait_invoke_map = {}
	self.head_act_info = {}
	self.first_use_diy_head_photo_flag = 0
	self.diy_head_photo_protocol_flag = 0

	self.bubble_cfg = ConfigManager.Instance:GetAutoConfig("bubble_list_auto")
	self.bubble_talk_list = ListToMap(self.bubble_cfg.talk_list, "talk_id")
	self.bubble_npc_list = ListToMapList(self.bubble_cfg.bubble_npc_list, "npc_id")

	self.head_origin_cfg = ConfigManager.Instance:GetAutoConfig("attrheadconfig_auto")
	self.head_icon_cfg = ListToMap(self.head_origin_cfg.headportrait, "index")
	self.head_icon_cfg_by_item_id = ListToMap(self.head_origin_cfg.headportrait, "active_stuff_id")
	self.job_map_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("job_auto").job, "sex", "prof")
	self:InitHeadActStuffList()

	Runner.Instance:AddRunObj(self, 16)

	self.role_arr = {}
	self.role_arr[ROLE_SPECIAL_ATTR_TYPE.BASE_ATTR] = {
		"shengming_max",			--"生命"
		"gongji",					--"攻击"
		"fangyu",					--"防御"
		"pojia",					--"破甲"
		"baoji_shanghai",			--"暴击"
		"kangbao_shanghai",			--"抗暴"
		"yuansu_sh",				--"五行伤害"
		"yuansu_hj",				--"五行护甲"
		"shengming_qq",				--生命窃取
		"fangtan",					--反弹伤害
		"shanghai_zs",				--真实伤害
		"fangyu_zs",				--真实防御
		"shengming_hf",  			--生命恢复
		"shanghai_jn",      		--技能伤害
		"boss_zhenshang",           -- boss真伤
	}

	self.role_arr[ROLE_SPECIAL_ATTR_TYPE.SPECIAL_ATTR] = {
		"shanbi_per",				--闪避几率
		"mingzhong_per",			--命中几率
		"baoji_per",				--暴击率
		"kangbao_per",				--抗暴率
		"lianji_per",				--连击率
		"lianjikang_per",			--连击抵抗
		"jichuan_per",				--击穿率
		"jichuankang_per",			--击穿抵抗
		"gedang_per",				--格挡率
		"podang_per",				--破档率
		"baoji_shanghai_per",		--暴击伤害
		"baoji_shanghai_jm_per",	--暴击伤害减免
		"lianji_shanghai_per",		--连击伤害比例
		"lianji_shanghai_jm_per",	--连击伤害减免
		"gedang_ms_per",			--格挡免伤
		"gedang_ms_my_per",			--格挡免伤免疫
		"shanghai_jc_per",			--伤害加成
		"shanghai_jm_per",			--伤害减免
		"yuansu_jk_per",			--元素减抗
		"yuansu_kx_per",			--元素抗性
		"jineng_shanghai_zj_per",	--技能伤害增加
		"jineng_shanghai_jm_per",	--技能伤害减免
		"shanghai_quan_jc_per",		--全属性伤害加成
		"shanghai_quan_jm_per",		--全属性伤害减免
		"zengshang_boss_per",		--首领增伤
		"jianshang_boss_per",		--首领减伤
		"zengshang_guaiwu_per",		--怪物增伤
		"jianshang_guaiwu_per",		--怪物减伤
		"zengshang_per",			--玩家增伤
		"jianshang_per",			--玩家减伤
		"zengshang_bs_per",			--变身增伤
		"jianshang_bs_per",			--变身减伤
		"mingzhong_yc_per",			--异常状态命中
		"dikang_yc_per",			--异常状态抵抗
		"zengshang_yc_per",			--异常状态增伤
		"zhiliaoxiaoguo_per",		--治疗效果
		"zengshang_gx_per",			--高血增伤
		"zengshang_xr_per",			--虚弱增伤
		"shengming_hf_per",			--生命回复比例
		"boss_palsy_per",           --boss麻痹
		"boss_seckill_per",         --boss秒杀
		}

	RemindManager.Instance:Register(RemindName.Role_Head, BindTool.Bind(self.GetRoleHeadRemind, self))		-- 红点
end

function RoleWGData:__delete()
	self:RemoveTime()
	self:DeleteRoleTalentData()
	self:DeleteSkillUpData()
	Runner.Instance:RemoveRunObj(self)
	RemindManager.Instance:UnRegister(RemindName.Role_Head)
	RoleWGData.Instance = nil
end

--0：女  1：男
function RoleWGData:GetRoleSex()
	return nil ~= self.role_vo and self.role_vo.sex or GameVoManager.Instance:GetMainRoleVo().sex
end

--改完转职之后，prof就可以不用%10了
--1：剑士（不分男女） 3：琴师（不分男女）
function RoleWGData:GetRoleProf()
	local prof = nil ~= self.role_vo and self.role_vo.prof or GameVoManager.Instance:GetMainRoleVo().prof
	return prof % 10
end

function RoleWGData:GetZhuanZhiNumber()
	local prof = nil ~= self.role_vo and self.role_vo.prof or GameVoManager.Instance:GetMainRoleVo().prof
	return math.floor(prof / 10)
end

function RoleWGData:GetRoleSexProf()
	return self:GetRoleSex(), self:GetRoleProf()
end

-- function RoleWGData.GetProfIcon(prof, sex)
-- 	prof = prof or RoleWGData.Instance:GetRoleProf()
-- 	local new_prof = prof % 10
-- 	sex = sex or RoleWGData.Instance:GetRoleSex()
-- 	-- 【职业改动修改】
-- 	if new_prof == 0 then
-- 		print_error("【为什么职业为0】", prof)
-- 		new_prof = 1
-- 	end

-- 	return string.format("a2_prof_icon_%s_%s", new_prof, sex)
-- end

function RoleWGData.GetProfIcon(prof, sex)
	prof = prof or RoleWGData.Instance:GetRoleProf()
	local new_prof = prof % 10
	sex = sex or RoleWGData.Instance:GetRoleSex()
	-- 【职业改动修改】
	if new_prof == 0 then
		print_error("【为什么职业为0】", prof)
		new_prof = 1
	end

	return string.format("a3_zd_jytb_%s_%s", new_prof, sex)
end

function RoleWGData:GetBubbleNpcList()
	return self.bubble_npc_list
end

function RoleWGData:RoleInfoIsOk()
	return self.role_info_is_ok
end

function RoleWGData:RoleInfoOk()
	self.role_info_is_ok = true
end

function RoleWGData:GetRoleInfo()
	return self.role_info
end

function RoleWGData:GetRoleVo()
	return self.role_vo
end

function RoleWGData:GetLoverRoleId()
	return self.role_vo.lover_uid
end

function RoleWGData:GetAttr(key)
	return self.role_vo[key]
end

function RoleWGData:GetOriginUid()
	return self.role_vo.origin_uid or 0
end

function RoleWGData:Update()
	for i = 1, 2 do
		local t = table.remove(self.wait_invoke_list)
		if nil ~= t then
			self.wait_invoke_map[t.callback][t.key] = nil
			if nil ~= self.notify_callback_list[t.callback] then
				-- value取最新的，因为在等待过程中，value可能再次发生变化
				local value = self.role_vo[t.key]
				Trycall(function ()
					t.callback(t.key, value, t.old_value)
				end)

				RoleWGData.ReleaseNotifyData(t)
			end
		else
			break
		end
	end
end

local type_number = "number"
-- 以后项目都用此写法，不再用2D项目的方式去写
function RoleWGData:SetAttr(key, value)
	-- 没有变化的value不做处理
	local old_value = self.role_vo[key]
	if nil ~= old_value and nil ~= value then
		if type(value) == type_number then
			if old_value == value then
				return
			end
		end
    end

	self.role_vo[key] = value
	-- self.role_info[key] = value		-- 兼容2D项目写法
	-- self:CacheAttr(key, value)

	self:AttrChanged(key, value, old_value)
end

function RoleWGData:AttrChanged(key, value, old_value)
	if RemindByAttrChange[key] then
		for k,v in pairs(RemindByAttrChange[key]) do
			RemindManager.Instance:Fire(v)
		end
	end

	local list = self.attr_to_notify_map[key]
	if list then
		for callback, _ in pairs(list) do
			self:InvokeCallBack(callback, key, value, old_value)
		end
	end
end

function RoleWGData:InvokeCallBack(callback, key, value, old_value)
	if self.wait_invoke_map[callback] and self.wait_invoke_map[callback][key] then
		return
	end

	self.wait_invoke_map[callback] = self.wait_invoke_map[callback] or {}
	self.wait_invoke_map[callback][key] = true
	local data = RoleWGData.GetNotifyData()
	data.callback = callback
	data.key = key
	data.value = value
	data.old_value = old_value
	table.insert(self.wait_invoke_list, 1, data)
end

--观察数据改变
function RoleWGData:NotifyAttrChange(callback, attr_list)
	if nil == attr_list or #attr_list == 0 then
		print_error("[RoleWGData]attr_list为空，会造成额外的性能开销，请检查!")
		return
	end

	if nil ~= self.notify_callback_list[callback] then
		print_error("[RoleWGData]重复监听事件")
		return
	end

	self.notify_callback_map[callback] = callback
	self.notify_callback_list[callback] = {}
	for _, attr in ipairs(attr_list) do
		table.insert(self.notify_callback_list[callback], attr)
		self.attr_to_notify_map[attr] = self.attr_to_notify_map[attr] or {}
		self.attr_to_notify_map[attr][callback] = true
	end
end

function RoleWGData:UnNotifyAttrChange(callback)
	if not callback then
		return
	end
	if self.notify_callback_map[callback] then
		self.notify_callback_map[callback] = nil
	end
	local attr_list = self.notify_callback_list[callback]
	if nil == attr_list then
		return
	end

	for _, attr in ipairs(attr_list) do
		if nil ~= self.attr_to_notify_map[attr] then
			self.attr_to_notify_map[attr][callback] = nil
		end
	end

	self.notify_callback_list[callback] = nil
end

function RoleWGData:IsExistsListen(callback)
	if BaseEvent.IsExistsListen(self, callback) then
		return true
	end

	return nil ~= self.notify_callback_map[callback]
end

-- 缓存属性
function RoleWGData:CacheAttr(key, value)
	-- body
	-- if not self.need_cache_attr[key] then return end
	-- if not self.cache_attr then
	-- 	self:GetCacheAttr()
	-- end

	-- if self.cache_attr[key] == value then return end
	-- self.cache_attr.role_id = self.role_vo.role_id
	-- self.cache_attr[key] = value
	-- -- 下一帧才写进缓存
	-- self:RemoveTime()
	-- self.time_quest = GlobalTimerQuest:AddDelayTimer(function ()
	-- 	self:SetCacheAttr()
	-- 	self:RemoveTime()
	-- 	RemindManager.Instance:Fire(RemindName.Shop)
	-- 	end,0)
end

function RoleWGData:RemoveTime()
	-- body
	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end
end

function RoleWGData:GetCacheAttr()
end

function RoleWGData:SetCacheAttr()
end

-- 用户自定义数据存储
function RoleWGData:SetGuaJiAndCustomInfo(protocol)
	local custom_info = protocol.custom_info
	-- print_error("----用户自定义数据存储-----", custom_info)
	if custom_info == '' then
		self:SetDefaultSkillInfo()
		return
	end
	self:SaveGuaJiAndCustomInfo(custom_info)
end

function RoleWGData:SaveGuaJiAndCustomInfo(custom_info)
	local data = Split(custom_info, "|")
	local split_data
	for i,v in ipairs(data) do
		split_data = Split(v, ":")
		if split_data and split_data[1] == 'skill_order' then
			self.role_skill_order = Split(split_data[2], ",")
		end
		if split_data and split_data[1] == 'skill_flag' then
			self.role_skill_play_flag = split_data[2] and Split(split_data[2], ",") or {}
		end
		-- if split_data and split_data[1] == 'bianshen_order' then
		-- 	self.role_bianshenskill_order = Split(split_data[2], ",")
		-- end
		-- if split_data and split_data[1] == 'bianshen_flag' then
		-- 	self.role_bianshenskill_play_flag = Split(split_data[2], ",")
		-- end

	end
end


-- 设置默认技能数据
function RoleWGData:SetDefaultSkillInfo(force)
	local data =  SkillWGData.Instance:GetSkillListByType(1, force)
	-- print_error("---设置默认技能数据data---", data)
	local str = 'skill_order:'
	for i,v in ipairs(data) do
		str = str .. v.skill_id .. ','
	end
	str = string.sub(str,1,-2)
	str = str .. '|'
	str = str .. 'skill_flag:'
	for i=1,#data do
		str = str .. data[i].skill_id .. ","
	end
	str = string.sub(str,1,-2)
	-- data = SkillWGData.Instance:GetBianShenSkill()
	-- str = str .. '|'
	-- str = str .. 'bianshen_order:'
	-- for i,v in ipairs(data) do
	-- 	str = str .. v.skill_id .. ','
	-- end
	-- str = string.sub(str,1,-2)
	-- str = str .. '|'
	-- str = str .. 'bianshen_flag:'
	-- for i=1,#data do
	-- 	str = str .. '1,'
	-- end
	-- str = string.sub(str,1,-2)
	self:SaveGuaJiAndCustomInfo(str)
	RoleWGCtrl.Instance.SetCustomInfo(str)
end

-- 获取技能用户自定义数据
function RoleWGData:GetSkillCustomInfo()
	return self.role_skill_order, self.role_skill_play_flag
end

function RoleWGData:GetBianShenSkillCustomInfo()
	return self.role_bianshenskill_order,self.role_bianshenskill_play_flag
end

function RoleWGData:GetSkillIndexBySkillId(skill_id)
	if not self.role_skill_order or 0 == #self.role_skill_order then
		return 0
	end

	for k,v in pairs(self.role_skill_order) do
		if tonumber(v) == skill_id then
			return k
		end
	end
	return 0
end

--保存自定义数据
function RoleWGData:SetCustomInfo(skill_order, skill_flag, bianshen_order, bianshen_flag)
	skill_order = skill_order or self.role_skill_order
	skill_flag = skill_flag or self.role_skill_play_flag
	bianshen_order = bianshen_order or self.role_bianshenskill_order
	bianshen_flag = bianshen_flag or self.role_bianshenskill_play_flag

	local str = 'skill_order:'
	for i,v in ipairs(skill_order) do
		str = str .. v .. ','
	end
	str = string.sub(str,1,-2)
	str = str .. '|'
	str = str .. 'skill_flag:'
	for i=1,#skill_flag do
		str = str .. skill_flag[i] .. ','
	end
	str = string.sub(str,1,-2)

	-- str = str .. '|'
	-- str = str .. 'bianshen_order:'
	-- for i,v in ipairs(bianshen_order) do
	-- 	str = str .. v .. ','
	-- end
	-- str = string.sub(str,1,-2)
	-- str = str .. '|'
	-- str = str .. 'bianshen_flag:'
	-- for i=1,#bianshen_flag do
	-- 	str = str .. bianshen_flag[i] .. ','
	-- end
	-- str = string.sub(str,1,-2)
	RoleWGCtrl.Instance.SetCustomInfo(str)

end

--设置基础属性值，属性改变进行广播
function RoleWGData:SetMainRoleInfoValue(key, value)
	self:SetAttr(key, value)
end

--设置公共属性值，属性改变进行广播
function RoleWGData:SetMainRoleVoValue(key, value)
	self:SetAttr(key, value)
end

function RoleWGData:SetExpExtraPer(value)
	self.exp_extra_per = value
end

function RoleWGData:GetExpExtraPer()
	return self.exp_extra_per
end

function RoleWGData:SetExpDelta(value)
	self.exp_delta = value
end

function RoleWGData:GetExpDelta()
	return self.exp_delta or 0
end

function RoleWGData:SetExpReason(value)
	self.exp_reason = value
end

function RoleWGData:GetExpReason()
	return self.exp_reason
end

--获得职业名字
function RoleWGData:GetProfNameByType(prof_type, sex)
	return Language.Common.ProfName[sex][prof_type]
end

--获得阵营编号
function RoleWGData:GetCampColorByType(camp_type)
	return camp_type
end

--根据属性类型获得属性名字。名字参照role中的vo
function RoleWGData:GetRoleAttrNameByType(type)
	return self:GetServerRoleAttrNameByType(type)
end

--根据属性类型获得服务端属性名字。名字参照role中的vo
function RoleWGData:GetServerRoleAttrNameByType(type)
	if self.sever_attr_name == nil then
		self.sever_attr_name = {}
		if self.attr_transformation == nil then
			self:GetRoleAttrTypeByName()
		end
		for k,v in pairs(self.attr_transformation[2]) do
			self.sever_attr_name[v] = k
		end
	end
	return type and self.sever_attr_name[type]
end


--根据属性名获取属性类型
function RoleWGData:GetRoleAttrTypeByName(attr_name)
	if self.attr_transformation == nil then
		self.attr_transformation = {
			[1] = {
				["maxhp"] = GameEnum.BASE_CHARINTATTR_TYPE_MAXHP,
				["max_hp"] = GameEnum.BASE_CHARINTATTR_TYPE_MAXHP,
				["shanbi"] = GameEnum.BASE_CHARINTATTR_TYPE_SHANBI_PER,
				["mingzhong"] = GameEnum.BASE_CHARINTATTR_TYPE_MINGZHONG_PER,
				["baoji"] = GameEnum.BASE_CHARINTATTR_TYPE_BAOJI_PER,
				["jianren"] = GameEnum.BASE_CHARINTATTR_TYPE_JIANREN_PER,
				["movespeed"] = GameEnum.BASE_CHARINTATTR_TYPE_MOVE_SPEED,
				["per_maxhp"] = GameEnum.BASE_CHARINTATTR_TYPE_SHENGMING_JC_PER,
				["per_gongji"] = GameEnum.BASE_CHARINTATTR_TYPE_GONGJI_JC_PER,
				["gong_ji"] = GameEnum.BASE_CHARINTATTR_TYPE_GONGJI,													-- 攻击
				["fang_yu"] = GameEnum.BASE_CHARINTATTR_TYPE_FANGYU,													-- 防御
				["po_jia"] = GameEnum.BASE_CHARINTATTR_TYPE_POJIA,														-- 破甲
				["baoji_shanghai"] = GameEnum.BASE_CHARINTATTR_TYPE_BAOJI_SHANGHAI,										-- 暴击伤害固定值
				["kangbao_shanghai"] = GameEnum.BASE_CHARINTATTR_TYPE_KANGBAO_SHANGHAI,									-- 抗暴伤害固定值

				["move_speed"] = GameEnum.FIGHT_CHARINTATTR_TYPE_MOVE_SPEED,
			},
			[2] = {
				["shengming_max"] = GameEnum.BASE_CHARINTATTR_TYPE_MAXHP,												-- 最大生命
				["gongji"] = GameEnum.BASE_CHARINTATTR_TYPE_GONGJI,														-- 攻击
				["fangyu"] = GameEnum.BASE_CHARINTATTR_TYPE_FANGYU,														-- 防御
				["pojia"] = GameEnum.BASE_CHARINTATTR_TYPE_POJIA,														-- 破甲
				["yuansu_sh"] = GameEnum.BASE_CHARINTATTR_TYPE_YUANSU_SH,												-- 元素伤害
				["yuansu_hj"] = GameEnum.BASE_CHARINTATTR_TYPE_YUANSU_HJ,												-- 元素护甲
				["shanbi_per"] = GameEnum.BASE_CHARINTATTR_TYPE_SHANBI_PER,												-- 闪避率
				["mingzhong_per"] = GameEnum.BASE_CHARINTATTR_TYPE_MINGZHONG_PER,										-- 命中率
				["baoji_per"] = GameEnum.BASE_CHARINTATTR_TYPE_BAOJI_PER,												-- 暴击率
				["kangbao_per"] = GameEnum.BASE_CHARINTATTR_TYPE_KANGBAO_PER,											-- 抗暴率
				["lianji_per"] = GameEnum.BASE_CHARINTATTR_TYPE_LIANJI_PER,												-- 连击率
				["lianjikang_per"] = GameEnum.BASE_CHARINTATTR_TYPE_LIANJIKANG_PER,										-- 连击抵抗
				["jichuan_per"] = GameEnum.BASE_CHARINTATTR_TYPE_JICHUANG_PER,											-- 击穿率
				["jichuankang_per"] = GameEnum.BASE_CHARINTATTR_TYPE_JICHUANGKANG_PER,									-- 击穿抵抗
				["zhuagnbei_sm_jc_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_ZHUAGNBEI_SM_JC_PER,							-- 装备生命加成
				["zhuagnbei_gj_jc_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_ZHUAGNBEI_GJ_JC_PER,							-- 装备攻击加成
				["zhuagnbei_fy_jc_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_ZHUAGNBEI_FY_JC_PER,							-- 装备防御加成
				["zhuagnbei_pj_jc_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_ZHUAGNBEI_PJ_JC_PER,							-- 装备破甲加成
				["shengming_jc_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_SHENGMING_JC_PER,								-- 生命加成
				["gongji_jc_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_GONGJI_JC_PER,										-- 攻击加成
				["fangyu_jc_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_JIANREN_PER,										-- 坚韧加成
				["pojia_jc_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_CHUANTOU_PER,										-- 穿透加成
				["yuansu_sh_jc_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_YUANSU_SH_JC_PER,								-- 元素伤害加成
				["yuansu_hj_jc_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_YUANSU_HJ_JC_PER,								-- 元素护甲加成
				["shengming_qq"] =  GameEnum.BASE_CHARINTATTR_TYPE_SHENGMING_QQ,										-- 生命窃取
				["fangtan"] =  GameEnum.BASE_CHARINTATTR_TYPE_FANGTAN,													-- 反弹伤害
				["shanghai_zs"] =  GameEnum.BASE_CHARINTATTR_TYPE_SHANGHAI_ZS,											-- 真实伤害
				["fangyu_zs"] =  GameEnum.BASE_CHARINTATTR_TYPE_FANGYU_ZS,												-- 真实防御
				["shengming_qq_jc_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_SHENGMING_QQ_JC_PER,							-- 生命窃取加成
				["fangtan_jc_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_FANGTAN_JC_PER,									-- 反弹伤害加成
				["shanghai_zs_jc_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_SHANGHAI_ZS_JC_PER,							-- 真实伤害加成
				["fangyu_zs_jc_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_FANGYU_ZS_JC_PER,								-- 真实防御加成
				["zengshang_boss_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_ZENGSHANG_BOSS_PER,							-- 首领增伤
				["jianshang_boss_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_JIANSHANG_BOSS_PER,							-- 首领减伤
				["zengshang_guaiwu_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_ZENGSHANG_GUAIWU_PER,						-- 怪物增伤
				["jianshang_guaiwu_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_JIANSHANG_GUAIWU_PER,						-- 怪物减伤
				["zengshang_boss_dk_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_ZENGSHANG_BOSS_DK_PER,						-- 首领增伤抵抗
				["jianshang_boss_dk_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_JIANSHANG_BOSS_DK_PER,						-- 首领减伤抵抗
				["zengshang_guaiwu_dk_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_ZENGSHANG_GUAIWU_DK_PER,					-- 怪物增伤抵抗
				["jianshang_guaiwu_dk_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_JIANSHANG_GUAIWU_DK_PER,					-- 怪物减伤抵抗
				["zengshang_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_ZENGSHANG_PER,										-- 玩家增伤
				["jianshang_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_JIANSHANG_PER,										-- 玩家减伤
				["zengshang_bs_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_ZENGSHANG_BS_PER,								-- 变身增伤
				["jianshang_bs_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_JIANSHANG_BS_PER,								-- 变身减伤
				["mingzhong_yc_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_MINGZHONG_YC_PER,								-- 异常状态命中
				["dikang_yc_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_DIKANG_YC_PER,										-- 异常状态抵抗
				["zhiliaoxiaoguo_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_ZHILIAOXIAOGUO_PER,							-- 治疗效果
				["zengshang_yc_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_ZENGSHANG_YC_PER,								-- 异常状态增伤
				["gedang_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_GEDANG_PER,											-- 格挡率
				["podang_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_PODANG_PER,											-- 破档率
				["baoji_shanghai_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_BAOJI_SHANGHAI_PER,							-- 暴击伤害
				["gedang_ms_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_GEDANG_MS_PER,										-- 格挡免伤
				["shengming_hf"] =  GameEnum.BASE_CHARINTATTR_TYPE_SHENGMING_HF,										-- 生命回复
				["gongji_sd"] =  GameEnum.BASE_CHARINTATTR_TYPE_GONGJI_SD,												-- 攻击速度
				["shanghai_jn"] =  GameEnum.BASE_CHARINTATTR_TYPE_SHANGHAI_JN,											-- 技能伤害
				["move_speed_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_MOVE_SPEED,										-- 移动速度
				["shengming_hf_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_SHENGMING_HF_PER,								-- 生命回复比例
				["zengshang_gx_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_ZENGSHANG_GX_PER,								-- 高血增伤
				["zengshang_xr_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_ZENGSHANG_XR_PER,								-- 虚弱增伤
				["shanghai_jc_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_SHANGHAI_JC_PER,									-- 伤害加成
				["shanghai_quan_jc_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_SHANGHAI_QUAN_JC_PER,						-- 全属性伤害加成
				["shanghai_jm_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_SHANGHAI_JM_PER,									-- 伤害减免
				["shanghai_quan_jm_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_SHANGHAI_QUAN_JM_PER,						-- 全属性伤害减免
				["yuansu_jk_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_YUANSU_JK_PER,										-- 元素减抗
				["yuansu_kx_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_YUANSU_KX_PER,										-- 元素抗性
				["lianji_shanghai_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_LIANJI_SHANGHAI_PER,							-- 连击伤害比例
				["shaguai_jb_diaoluo_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_SHAGUAI_JB_DIAOLUO_PER,					-- 杀怪金币掉落
				["jineng_shanghai_zj_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_JINENG_SHANGHAI_ZJ_PER,					-- 技能伤害增加
				["jineng_shanghai_jm_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_JINENG_SHANGHAI_JM_PER,					-- 技能伤害减免
				["equip_qianghua_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_EQUIP_QIANGHUA_PER,							-- 装备强化加成
				["boss_zhenshang"] =  GameEnum.BASE_CHARINTATTR_TYPE_BOSS_ZHEN_SHANG,									-- BOSS真伤
				["boss_palsy_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_BOSS_PALSY_PER,									-- BOSS麻痹
				["boss_seckill_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_BOSS_SECKILL_PER,								-- BOSS秒杀
				["lei_shanghai_zj_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_LEI_SHANGHAI_ZJ_PER,							-- 雷罚职业伤害增加
				["lei_shanghai_jm_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_LEI_SHANGHAI_JM_PER,							-- 雷罚职业伤害减免
				["gedang_ms_my_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_GEDANG_MS_MY_PER,								-- 格挡免伤免疫
				["baoji_shanghai_jm_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_BAOJI_SHANGHAI_JM_PER,						-- 暴击伤害减免
				["lianji_shanghai_jm_per"] =  GameEnum.BASE_CHARINTATTR_TYPE_LIANJI_SHANGHAI_JM_PER,					-- 连击伤害减免
				["baoji_shanghai"] = GameEnum.BASE_CHARINTATTR_TYPE_BAOJI_SHANGHAI,										-- 暴击伤害固定值
				["kangbao_shanghai"] = GameEnum.BASE_CHARINTATTR_TYPE_KANGBAO_SHANGHAI,									-- 抗暴伤害固定值
				["shengming_zb_role_jc_per"] = GameEnum.BASE_CHARINTATTR_TYPE_SHENGMING_ZB_ROLE_JC_PER,					-- 生命加成 = 装备 + 人物等级属性
				["gongji_zb_role_jc_per"] = GameEnum.BASE_CHARINTATTR_TYPE_GONGJI_ZB_ROLE_JC_PER,						-- 攻击加成 = 装备 + 人物等级属性
				["fangyu_zb_role_jc_per"] = GameEnum.BASE_CHARINTATTR_TYPE_FANGYU_ZB_ROLE_JC_PER,						-- 坚韧-防御加成 = 装备 + 人物等级属性
				["pojia_zb_role_jc_per"] = GameEnum.BASE_CHARINTATTR_TYPE_POJIA_ZB_ROLE_JC_PER,							-- 穿透-破甲加成 = 装备 + 人物等级属性
				["gongji_wuqi_jc_per"] = GameEnum.BASE_CHARINTATTR_TYPE_GONGJI_WUQI_JC_PER,								-- 主武器/副武器 - 攻击加成
				["pojia_wuqi_jc_per"] = GameEnum.BASE_CHARINTATTR_TYPE_POJIA_WUQI_JC_PER,								-- 主武器/副武器 - 破甲加成
				["gongji_shiping_jc_per"] = GameEnum.BASE_CHARINTATTR_TYPE_GONGJI_SHIPIN_JC_PER,						-- 仙链/仙坠/仙戒/仙镯 - 攻击加成
				["kill_monster_per_exp"] = GameEnum.BASE_CHARINTATTR_TYPE_KILL_MONSTER_PER_EXP,							-- 打怪经验加成万份比
				["baoji_jc_per"] = GameEnum.BASE_CHARINTATTR_TYPE_BAOJI_JC_PER,											-- 暴击加成
				["kangbao_jc_per"] = GameEnum.BASE_CHARINTATTR_TYPE_KANGBAO_JC_PER,										-- 抗暴加成
				["rare_exterior_rate_per"] = GameEnum.BASE_CHARINTATTR_TYPE_RARE_EXTERIOR_RATE_PER,						-- 珍稀外观掉落加成
				["rare_equip_rate_per"] = GameEnum.BASE_CHARINTATTR_TYPE_RARE_EQUIP_RATE_PER,							-- 珍稀装备掉落加成

				["hp"] = GameEnum.FIGHT_CHARINTATTR_TYPE_HP,															-- 生命
				["max_hp"] =  GameEnum.FIGHT_CHARINTATTR_TYPE_MAXHP,													-- 生命
				["gong_ji"] =  GameEnum.FIGHT_CHARINTATTR_TYPE_GONGJI,													-- 攻击
				["fang_yu"] =  GameEnum.FIGHT_CHARINTATTR_TYPE_FANGYU,													-- 防御
				["po_jia"] =  GameEnum.FIGHT_CHARINTATTR_TYPE_POJIA,													-- 破甲
				["move_speed"] =  GameEnum.FIGHT_CHARINTATTR_TYPE_MOVE_SPEED,											-- 移动速度
			}
		}
	end
	if attr_name then
		return self.attr_transformation[1][attr_name] or self.attr_transformation[2][attr_name]
	end
end

--是否足够灵玉和元宝，优先使用绑定的情况
function RoleWGData:GetIsEnoughAllGold(cost_gold)
	if nil == cost_gold then
		return false
	end
	local all_gold = self.role_info.bind_gold + self.role_info.gold
	return all_gold >= cost_gold
end

--是否足够元宝（并不知道上面那条是否已经废弃，所以加多一个）
function RoleWGData:GetIsEnoughBindGold(cost_gold)
	if nil == cost_gold then
		return false
	end
	local gold = self.role_info.bind_gold
	return gold >= cost_gold
end

--是否足够灵玉
function RoleWGData:GetIsEnoughUseGold(cost_gold)
	if nil == cost_gold then
		return false
	end
	local gold = self.role_info.gold
	return gold >= cost_gold
end

--是否足够绑定和非绑定铜币，优先使用绑定的情况
function RoleWGData.GetIsEnoughAllCoin(cost_coin)
	if nil == cost_coin then
		return false
	end
	local coin = RoleWGData.Instance.role_info.coin or 0
	local bind_coin = RoleWGData.Instance.role_info.bind_coin or 0
	local all_coin = coin + bind_coin
	return all_coin >= cost_coin
end

--是否足够非绑定铜币
function RoleWGData:GetIsEnoughUseCoin(cost_coin)
	if nil == cost_coin then
		return false
	end
	local coin = self.role_info.coin
	return coin >= cost_coin
end

function RoleWGData:GetIsMainRole(role_id)
	return role_id == self.role_vo.role_id
end

function RoleWGData:GetMainRoleId()
	return self.role_vo.role_id
end

function RoleWGData.ParseCrossServerUserName(user_name)
	if nil == user_name then
		return ""
	end
	local show_name = user_name
	local i, j = string.find(user_name, "_s%d+$")
	if nil ~= i and nil ~= j then
		local server_id = tonumber(string.sub(user_name, i + 2, j))
		if server_id > 1500 and server_id < 2000 then
			local show_server_id = server_id - 1500
			show_name = string.gsub(user_name, server_id, show_server_id)
		end
	end
	return show_name
end

function RoleWGData:GetRoleLevel()
	return self.role_vo.level or 0
end


---判断巅峰等级
function RoleWGData:GetDianFengLevel(level)
	--先屏蔽飞升
	if level then
		local dianfeng_level = ConfigManager.Instance:GetAutoConfig('zhuanzhicfg_auto').other[1].role_level_to_prof_level4
		local role_level = level - dianfeng_level
		if role_level > 0 then
			return true, role_level
		else
			return false, level
		end
	end

	return false, 0
end

-- 把等级转换成巅峰等级
function RoleWGData:TransToDianFengLevelStr(level)
	if level then
		local is_dianfeng, level = self:GetDianFengLevel(level)
		if is_dianfeng then
			return Language.Common.DianFengIcon .. level
		else
			return level
		end
	end
	return 0
end

-- 巅峰%d
-- %d
function RoleWGData.GetLevelString(level)
	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(level)
	if is_vis then
		return string.format(Language.Role.FeiXianDesc2, role_level)
	else
		return string.format(Language.Common.LevelFormat, role_level)
	end
end

-- 巅峰%d级
-- %d级
function RoleWGData.GetLevelString2(level)
	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(level)
	if is_vis then
		return string.format(Language.Role.FeiXianDesc4, role_level)
	else
		return string.format(Language.Role.NorLevelDesc, role_level)
	end
end

-- [223]%d
-- %d级
function RoleWGData.GetLevelStringImg(level)
	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(level)
	if is_vis then
		return string.format(Language.Role.FeiXianDesc3, role_level)
	else
		return string.format(Language.Role.NorLevelDesc, role_level)
	end
end

-- [223]%d级
-- %d级
function RoleWGData.GetLevelStringImg2(level)
	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(level)
	if is_vis then
		return string.format(Language.Role.FeiXianDesc5, role_level)
	else
		return string.format(Language.Role.NorLevelDesc, role_level)
	end
end

-- [223]%d
-- Lv.%d
function RoleWGData.GetLevelStringImg3(level)
	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(level)
	if is_vis then
		return string.format(Language.Role.FeiXianDesc3, role_level)
	else
		return string.format(Language.Role.FeiXianDesc6, role_level)
	end
end

function RoleWGData:SetDayRevivalTimes(day_revival_times)
	self.day_revival_times = day_revival_times
end

-- 每日复活次数
function RoleWGData:GetDayRevivalTimes()
	return self.day_revival_times
end

-- 获取角色等级属性经验配置
function RoleWGData.GetRoleExpCfgByLv(lv)
	lv = lv or RoleWGData.Instance.role_vo.level
	local exp_tab = ConfigManager.Instance:GetAutoConfig("roleexp_auto").exp_config
	return exp_tab[lv]
end

-- 角色最大等级
function RoleWGData.GetRoleMaxLevel()
	return ConfigManager.Instance:GetAutoConfig("roleexp_auto").other[1].max_level
end

-- 角色经验修正
function RoleWGData:GetRoleExpCorrection()
	local exp_per = 1
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if self.exp_fix_max_day and server_day > self.exp_fix_max_day then
		return exp_per
	end

	if not self.role_exp_fix_cfg then
		self.role_exp_fix_cfg = {}
	end

	if not self.role_exp_fix_cfg[server_day] then
		self.exp_fix_max_day = 0
		local exp_fix_cfg = ConfigManager.Instance:GetAutoConfig("roleexp_auto").exp_fix
		for k,v in ipairs(exp_fix_cfg) do
			if v.open_day == server_day then
				if not self.role_exp_fix_cfg[server_day] then
					self.role_exp_fix_cfg[server_day] = {}
				end

				local level_range_list = Split(v.level_range, ",")
				local min_level = tonumber(level_range_list[1]) or 99999
				local max_level = tonumber(level_range_list[2]) or 0
				table.insert(self.role_exp_fix_cfg[server_day], {min_level = min_level, max_level = max_level, exp_per = v.exp_per})
			end

			if v.open_day > self.exp_fix_max_day then
				self.exp_fix_max_day = v.open_day
			end
		end
	end

	if self.role_exp_fix_cfg[server_day] then
		local role_level = self:GetRoleLevel()
		for k,v in pairs(self.role_exp_fix_cfg[server_day]) do
			if role_level >= v.min_level and role_level <= v.max_level then
				exp_per = v.exp_per / 10000
				break
			end
		end
	end

	return exp_per
end

function RoleWGData:SetCapabilityList(capability_list)
	capability_list = capability_list or {}
	for k, v in pairs(capability_list) do
		self.capability_list[k] = v
	end
end

function RoleWGData:GetCapabilityByType(type)
	return self.capability_list[type] or 0
end

-- 属性文字调整 （4个字属性，不足补空格）
function RoleWGData.FormatAttrString(string, ending_str)
	local wold_len = string.len("，")
	local str_len = string.len(string)
	if str_len >= 4 * wold_len then
		if nil ~= ending_str then
			string = string .. ending_str
		end
		return string
	else
		local world_num = math.floor(str_len / wold_len)
		local lack_num = 4 - world_num
		local once_add_num = 0
		if 1 == lack_num then     -- 获取到的中文长度对不上找不到问题，无奈写死只处理两个字或三个字
			once_add_num = 1
		elseif 2 == lack_num then
			once_add_num = 6
		end

		local end_str = ""
		for i = 1, world_num do
			local word = string.sub(string, i * 3 - 2, i * 3)
			end_str = end_str .. word
			if i ~= world_num then
				for i = 1, once_add_num do
					end_str = end_str .. " "
				end
			end
		end

		if nil ~= ending_str then
			end_str = end_str .. ending_str
		end

		return end_str
	end
end

---------------------装备攻略--------------------------
function RoleWGData:GetEquipGongLveCfg()
	local role_prof = self:GetRoleProf()
	local prof_zhuan = self:GetZhuanZhiNumber()
	local gonglve_cfg = ConfigManager.Instance:GetAutoConfig("gonglueconfig_auto").other_cfg
	for k,v in pairs(gonglve_cfg) do
		if role_prof == v.prof and prof_zhuan == v.zhuanzhi_level then
			return v
		end
	end
end

function RoleWGData:GetGongLveAttrList()
	local gonglve_attr_cfg = ConfigManager.Instance:GetAutoConfig("gonglueconfig_auto").describe_show
	return gonglve_attr_cfg
end

function RoleWGData:GetGongLveLegendAttr(color)
	local legend_attr_list = {}
	local gonglve_legend_attr = ConfigManager.Instance:GetAutoConfig("equipforge_auto").equip_rand_attr
	for k,v in pairs(gonglve_legend_attr) do
		if color == v.color and v.attr_type > 6 and v.attr_type < 10 and v.big_type == 1 then
			table.insert(legend_attr_list, v)
		end
	end
	return legend_attr_list
end


function RoleWGData:GetTitleRemind()
	local use_title_id = TitleWGData.Instance:GetUsedTitleId()

	local get_title = TitleWGData.Instance:GetTitleIdList()
	if get_title and use_title_id then
		if #use_title_id < 3 and #get_title >= 3 then
			return 1
		end
	end
	return 0
end

--适用于(优先消耗绑玉)的消费
function RoleWGData:GetMoneyTypeText(price, num)
	local num = num and num or 1
	local cost_text = Language.Common.GoldText
	local bind_gold = self:GetRoleInfo().bind_gold

	if bind_gold >= price * num then
		cost_text = Language.Common.BindGoldText
	end

	return cost_text
end

function RoleWGData:SetDayFirstLoginFlag(flag)
	self.day_first_login_flag = flag
end

function RoleWGData:GetDayFirstLoginFlag()
	return self.day_first_login_flag
end

function RoleWGData:GetRoleAttr(type)
	return self.role_arr and self.role_arr[type]
end

function RoleWGData:IsHoldAngle()
	return self.role_vo.hold_beauty_npcid and self.role_vo.hold_beauty_npcid > 0
end

function RoleWGData:GetNewRoleAttr(type)
	local role_arr_list = {}
	local arr_list = self:GetRoleAttr(type)
	local vo = GameVoManager.Instance:GetMainRoleVo()
	local vo_tab = {}
	local attr_data = {}
	for k,v in pairs(arr_list) do
		vo_tab[v] = vo[v] + FightWGData.Instance:GetAttrValue(v)
		if EquipmentWGData.Instance:GetAttrIsPerByAttrStr((v)) then
			vo_tab[v] = tonumber(v) == 0 and v .. "%" or tonumber(vo[v]) / 100 .. "%"
		end
		local name = Language.Common.AttrNameList2[v]
 		if name then
			attr_data = {name = name, cur_value = vo_tab[v]}
		end
		table.insert(role_arr_list,attr_data)
	end
	
	return role_arr_list
end

function RoleWGData:GetNewRoleAttrAmount(type)
	local arr_list = self:GetRoleAttr(type)
	return #arr_list
end

------------------------------  头像  ------------------------------------------
function RoleWGData:InitHeadActStuffList()
	self.head_act_stuff_list = {}
	for k,v in pairs(self.head_icon_cfg) do
		self.head_act_stuff_list[v.active_stuff_id] = true
	end
end

function RoleWGData:GetHeadActStuffList()
	return self.head_act_stuff_list
end

function RoleWGData:IsHeadActStuff(item_id)
	return self.head_act_stuff_list[item_id]
end

function RoleWGData:GetHeadCfgByIndex(index)
	return self.head_icon_cfg[index]
end

function RoleWGData:GetHeadCfgByItemId(item_id)
	return self.head_icon_cfg_by_item_id[item_id]
end

function RoleWGData:GetHeadOtherCfg()
	return self.head_origin_cfg.other and self.head_origin_cfg.other[1] or {}
end

function RoleWGData:SetHeadIconInfo(protocol)
	self.click_diy_head_btn_flag = protocol.click_diy_btn_flag
	self.avatar_key_big = protocol.avatar_key_big
	self.avatar_key_small = protocol.avatar_key_small
	self.first_use_diy_head_photo_flag = protocol.first_use_diy_head_photo_flag
	self.diy_head_photo_protocol_flag = protocol.diy_head_photo_protocol_flag
	self.head_act_info = protocol.act_list
	self:SortHeadCfgList()
end

function RoleWGData:IsClickDiyHeadBtn()
	return self.click_diy_head_btn_flag == 1
end

function RoleWGData:GetCustomHeadInfo()
	return {avatar_key_big = self.avatar_key_big or 0,
			avatar_key_small = self.avatar_key_small or 0,
		}
end

function RoleWGData:GetCurUseHead()
	return self.avatar_key_big or self:GetAttr("avatar_key_big") or -1
end

function RoleWGData:GetHeadIsActByIndex(index)
	return self.head_act_info[index] ~= nil
end

function RoleWGData:IsFirstChangeDiyHead()
	return self.first_use_diy_head_photo_flag == 0
end

function RoleWGData:IsAgreeHeadProtocol()
	return self.diy_head_photo_protocol_flag == 1
end

-- is_act, status
function RoleWGData:GetHeadStatusByIndex(index)
	local is_act = false
	if index == 0 then
		is_act = true
	else
		is_act = self:GetHeadIsActByIndex(index)
	end

	local status = GOODS_STATE_TYPE.UNACT
	local cur_use = self:GetCurUseHead()
	if is_act then
		status = index == cur_use and GOODS_STATE_TYPE.USING or GOODS_STATE_TYPE.NORMAL
	else
		if self:CanActiveHeadByIndex(index) then
			status = GOODS_STATE_TYPE.CAN_ACT
		end
	end

	return status
end

function RoleWGData:SortHeadCfgList()
	self.head_sort_cfg_list = {}
	local status, no_act
	local cfg = self.head_origin_cfg.headportrait
	for i,v in pairs(cfg) do
		local data = {}
		status = self:GetHeadStatusByIndex(v.index)
		data.cfg = v
		no_act = status == GOODS_STATE_TYPE.UNACT or status == GOODS_STATE_TYPE.CAN_ACT
		data.status = status
		data.sort = no_act and 1000 + v.index or v.index
		table.insert(self.head_sort_cfg_list, data)
	end

	table.sort(self.head_sort_cfg_list, SortTools.KeyLowerSorter("sort"))
end

function RoleWGData:GetAllHeadSortCfg()
	return self.head_sort_cfg_list or {}
end

function RoleWGData:GetCurHeadIndex()
	local list = self:GetAllHeadSortCfg()
	-- 跳红点
	for k,v in pairs(list) do
		if v.status == GOODS_STATE_TYPE.CAN_ACT then
			return k
		end
	end

	-- 跳使用中
	for k,v in pairs(list) do
		if v.status == GOODS_STATE_TYPE.USING then
			return k
		end
	end

	return 1
end

function RoleWGData:CanActiveHeadByIndex(index)
	local is_active = self:GetHeadIsActByIndex(index)
	if is_active or index == 0 or index >= GameEnum.CUSTOM_HEAD_ICON then
		return false
	end

	local cfg = self:GetHeadCfgByIndex(index)
	if cfg then
		if cfg.zhuanzhi_revolution > 0 then
			local zhuanzhi_num = self:GetZhuanZhiNumber()
			if zhuanzhi_num < cfg.zhuanzhi_revolution then
				return false
			end
		end

		if cfg.active_stuff_id > 0 and cfg.count > 0 then
			local num = ItemWGData.Instance:GetItemNumInBagById(cfg.active_stuff_id)
			return num >= cfg.count
		end
	end

	return false
end

function RoleWGData:GetHeadAttrList(index)
	local attr_list = {}
	local cfg = self:GetHeadCfgByIndex(index)
	if cfg == nil then
		return attr_list
	end

	attr_list = EquipWGData.GetSortAttrListByCfg(cfg)
	return attr_list
end

function RoleWGData:CanActiveHead()
	local list = self:GetAllHeadSortCfg()

	for k,v in pairs(list) do
		if v.status == GOODS_STATE_TYPE.CAN_ACT then
			return true
		end
	end

	return false
end

function RoleWGData:GetRoleHeadRemind()
	-- 开启判断
	if not FunOpen.Instance:GetFunIsOpened(FunName.ChangeHeadView) then
		return 0
	end

	-- 首次自定义
	local limit = self:GetLimitCustomAvatar()
	if not limit then
		local is_click = self:IsClickDiyHeadBtn()
		if not is_click and self:IsFirstChangeDiyHead() then
			return 1
		end
	end

	-- 激活
	if self:CanActiveHead() then
		return 1
	end

	return 0
end

--获取自定义头像是否开启限制
function RoleWGData:GetLimitCustomAvatar()
	--return GLOBAL_CONFIG.param_list.limit_custom_avatar == 1
	--策划要求关闭自定义头像
	return true
end

function RoleWGData:GetHeadIconResByIdnex(index, role_sex, role_prof, is_big)
    local cfg = self:GetHeadCfgByIndex(index)
	if not cfg then
        return "", ""
    end

	is_big = true
    if role_prof == nil then
        print_error("role_prof = nil", "加载头像数据有问题")
        role_prof = GameEnum.ROLE_PROF_1
    end

    role_prof = role_prof % 10

	local resouce = cfg["resouce" .. role_prof] or 0
	if is_big then
		return ResPath.GetNewHeadBigIcon(role_sex, resouce)
	else
		return ResPath.GetNewHeadIcon(role_sex, resouce)
	end
end

--------------------------------------------------------------------------------

function RoleWGData:GetDeubgListenCount(t)
	t.attr_listen_count = self:GetTotalEventNum()
end


function RoleWGData:GetTransferedUpLevel()
	local role_vo = RoleWGData.Instance.role_vo
	local transfer_num = RoleWGData.Instance:GetZhuanZhiNumber()
	local temp_level = 0
	local temp_exp = role_vo.exp
	local dianfeng_level = ConfigManager.Instance:GetAutoConfig('zhuanzhicfg_auto').other[1].role_level_to_prof_level4
	if transfer_num < 4 then
		if role_vo.level == dianfeng_level then
			local value = RoleWGData.Instance.GetRoleExpCfgByLv(role_vo.level + temp_level).exp
			while(temp_exp >= value and temp_level < 630) do
				temp_level = temp_level + 1
			end
		end
	end
	return temp_level
end

--设置主角战力
function RoleWGData:SetMainRoleCap(cap)
	self.main_role_cap = cap
end

--获取主角战力
function RoleWGData:GetMainRoleCap()
	local scene_type = Scene.Instance:GetSceneType()
	--这些场景用的是假战力展示
	if scene_type == SceneType.ETERNAL_NIGHT or scene_type == SceneType.ETERNAL_NIGHT_FINAL then
        local origin_capability = self:GetOriginCapability()
        return origin_capability
    end
	if self.main_role_cap ~= nil then
		return self.main_role_cap
	end
	return 0
end

function RoleWGData:SetKanJiaGiftList(list)
	self.role_kanjia_gift_list = list
end

function RoleWGData:GetKanJiaGiftItem(gift_id)
	if self.role_kanjia_gift_list[gift_id] ~= nil then
		return self.role_kanjia_gift_list[gift_id]
	end
	return nil
end

function RoleWGData:SetKanJiaGiftItem(gift_id,kanjia_count)
	if self.role_kanjia_gift_list ~= nil then
		self.role_kanjia_gift_list[gift_id] = kanjia_count
	end
end

function RoleWGData:SetTodayKanJiaCount(value)
	self.today_kanjia_cnt = value
end

function RoleWGData:GetTodayKanJiaCount()
	return self.today_kanjia_cnt or 0
end

--在跨服中获取本服uid
function RoleWGData:InCrossGetOriginUid()
	local role_id = self.role_vo.origin_uid > 0 and self.role_vo.origin_uid or self.role_vo.role_id
	return role_id
end

function RoleWGData:GetMergeServerId()
	return self.role_vo.merge_server_id
end

function RoleWGData:GetMergePlatType()
	return self.role_vo.merge_plat_type
end

function RoleWGData:GetOriginServerId()
	return self.role_vo.origin_server_id
end

function RoleWGData:CheckCurServerIsOrigin()
	return self.role_vo.current_server_id == self.role_vo.origin_server_id 
end

-- 获取当前所在服的服务器id
function RoleWGData:GetCurServerId()
	return self.role_vo.current_server_id
end

-- 主角原服的usid
function RoleWGData:GetOriginalUSIDStr()
	local temp_high = self:GetPlatType()
	local temp_low = self:GetOriginServerId()
	return ToLLStr(temp_high, temp_low)
end

-- 主角所在服的usid
function RoleWGData:GetCurUSIDStr()
	local temp_high = self:GetCurPlatType()
	local temp_low = self:GetCurServerId()
	return ToLLStr(temp_high, temp_low)
end

-- 是否在其他服
function RoleWGData:InOtherServer()
	return self:GetCurServerId() ~= self:GetOriginServerId() or self:GetPlatType() ~= self:GetCurPlatType()
end

-- 判断是不是同一个服的人
function RoleWGData:IsSameServer(other_role_vo)
	if not other_role_vo then
		return false
	end
	if other_role_vo.is_main_role then
		return other_role_vo.merge_server_id == self:GetMergeServerId() and other_role_vo.merge_plat_type == self:GetPlatType()
	elseif other_role_vo.is_auto_task_robot then
		return true
	else
		return other_role_vo.merge_server_id == self:GetMergeServerId() and other_role_vo.merge_plat_type == self:GetPlatType()
	end
end

-- 原服平台类型
function RoleWGData:GetPlatType()
	return self.role_vo.plat_type
end

-- 当前所在服的平台类型
function RoleWGData:GetCurPlatType()
	return self.role_vo.current_plat_type
end

function RoleWGData:GetUUid()
	return self.role_vo.uuid
end

--low role_id   high 平台
function RoleWGData:GetUUIDStr()
	local temp_low = self:GetUUid().temp_low
	local temp_high = self:GetUUid().temp_high
	return ToLLStr(temp_high, temp_low)
end

--本地缓存数值
function RoleWGData.SetRolePlayerPrefsInt(key, value)
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	PlayerPrefsUtil.SetInt(key .. role_id, value)
end

--获取本地缓存数值
function RoleWGData.GetRolePlayerPrefsInt(key)
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	return PlayerPrefsUtil.GetInt(key .. role_id)
end

--删除本地缓存数值
function RoleWGData.DeleteRolePlayerPrefsInt(key)
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	return PlayerPrefsUtil.DeleteKey(key .. role_id)
end

function RoleWGData:CheckCanMount()
	local main_role = Scene.Instance:GetMainRole()
	if 0 < main_role:GetTaskToHeighMount() then
		return false, Language.Task.task_mount01
	end

	if 0 < main_role:GetTaskToBianShen() then
        return false, Language.Task.task_bianshen01
    end

	-- 轻功或御剑下落中不能乘骑坐骑
	if main_role.has_play_qinggong_land then
		return false
	end

	local special_appearance = self.role_vo.special_appearance
    if special_appearance == SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM
	or special_appearance == SPECIAL_APPEARANCE_TYPE.GUNDAM then
    	return false
    end

    return true
end

function RoleWGData:GetMoney(money_type)
	local ret_value = 0
	local role_info = self:GetRoleInfo()
	if money_type == MoneyType.XianYu then
		ret_value = role_info.gold
	elseif money_type == MoneyType.BangYu then
		ret_value = role_info.bind_gold
	elseif money_type == MoneyType.YuanBao then
		ret_value = role_info.silver_ticket
	elseif money_type == MoneyType.ShengWang then
		ret_value = role_info.shengwang
	elseif money_type == MoneyType.JinBi then
		ret_value = role_info.coin
	elseif money_type == MoneyType.ZhanLing then
		ret_value = role_info.zhan_ling
	elseif money_type == MoneyType.ZhanHun then
		ret_value = role_info.zhan_hun
	elseif money_type == MoneyType.Chivalrous then
		ret_value = role_info.chivalrous
	elseif money_type == MoneyType.CASH_POINT then
		ret_value = role_info.cash_point
	end
	return ret_value
end

function RoleWGData:SetBeforeLastMoney(gold, bind_gold, silver_ticket, coin, shengwang, chivalrous, cash_point, is_first)
	local role_info = RoleWGData.Instance:GetRoleInfo()
	if gold and gold >= 0 then
		if is_first then
			self.old_gold_value = gold
		else
			self.old_gold_value = role_info.gold
		end
	end

	if bind_gold and bind_gold >= 0 then
		if is_first then
			self.old_bind_gold_value = bind_gold
		else
			self.old_bind_gold_value = role_info.bind_gold
		end
	end

	if silver_ticket and silver_ticket >= 0 then
		if is_first then
			self.old_silver_ticket_value = silver_ticket
		else
			self.old_silver_ticket_value = role_info.silver_ticket
		end
	end

	if coin and coin >= 0 then
		if is_first then
			self.old_coin_value = coin
		else
			self.old_coin_value = role_info.coin
		end
	end

	if shengwang and shengwang >= 0 then
		if is_first then
			self.old_shengwang_value = shengwang
		else
			self.old_shengwang_value = role_info.shengwang
		end
	end

	if chivalrous and chivalrous >= 0 then
		if is_first then
			self.old_chivalrous_value = chivalrous
		else
			self.old_chivalrous_value = role_info.chivalrous
		end
	end

	if cash_point and cash_point >= 0 then
		if is_first then
			self.old_chivalrous_value = cash_point
		else
			self.old_cash_point_value = role_info.cash_point
		end
	end
end

function RoleWGData:GetOldMoney(money_type)
	local ret_value = 0
	if money_type == MONNEY_TYPE_1[GameEnum.MONEY_BAR.GOLD] then
		ret_value = self.old_gold_value
	elseif money_type == MONNEY_TYPE_1[GameEnum.MONEY_BAR.BIND_GOLD] then
		ret_value = self.old_bind_gold_value
	elseif money_type == MONNEY_TYPE_1[GameEnum.MONEY_BAR.SILVER_TICKET] then
		ret_value = self.old_silver_ticket_value
	elseif money_type == MONNEY_TYPE_1[GameEnum.MONEY_BAR.COIN] then
		ret_value = self.old_coin_value
	elseif money_type == MONNEY_TYPE_1[GameEnum.MONEY_BAR.SHENGWANG] then
		ret_value = self.old_shengwang_value
	elseif money_type == MONNEY_TYPE_1[GameEnum.MONEY_BAR.CHIVALROUS] then
		ret_value = self.old_chivalrous_value
	elseif money_type == MONNEY_TYPE_1[GameEnum.MONEY_BAR.CASH_POINT] then
		ret_value = self.old_cash_point_value
	end

	return ret_value
end

-- 获取武器模型组装资源名
-- 90【性别】【职业】00001 + 资源id * 100
function RoleWGData.GetFashionWeaponId(sex, prof, resouce)
	prof = prof or RoleWGData.Instance:GetRoleProf()
	prof = prof % 10
	sex = sex or RoleWGData.Instance:GetRoleSex()
	resouce = tonumber(resouce) or 1
	local new_resouce = 900000001 + resouce * 100
	new_resouce = new_resouce + prof * 100000
	new_resouce = new_resouce + sex * 1000000

    return new_resouce, 0
end

function RoleWGData:GetJobConfig(sex, prof)
	if prof > 10 then
		prof = prof % 10
	end

	return (self.job_map_cfg[sex] or {})[prof]
end

-- 获取职业描述
function RoleWGData:GetJobProfDesc(sex, prof)
	local cfg = self:GetJobConfig(sex, prof)

	if cfg then
		return Split(cfg.des, "|")
	end

	return nil
end

function RoleWGData:GetJobProfEffect(sex, prof)
	local name = ""

	if sex == 0 then
		if prof == 1 then
			name = "UI_logo_suimeng"
		elseif prof == 2 then
			name = "UI_logo_yingren"
		elseif prof == 3 then
			name = "UI_logo_danqing"
		end
	else
		if prof == 1 then
			name = "UI_logo_shuofeng"
		elseif prof == 2 then
			name = ""
		elseif prof == 3 then
			name = "UI_logo_xuanji"
		end
	end

	if name == "" then
		return nil, nil
	end

	local bundle_name = string.lower(name)
	return string.format("effects/prefab/ui/%s_prefab", bundle_name), name
end

-- 获取名称
function RoleWGData:GetJobName1(sex, prof)
	local cfg = self:GetJobConfig(sex, prof)
	return cfg and cfg.name1 or ""
end

-- 组装资源id
function RoleWGData.GetPartModelRes(part_type, res_id, sex, prof)
	res_id = res_id or 0
    prof = prof or 1
    if prof > 10 then
    	prof = prof % 10
    end

	local real_res = 0

    --使用配置表默认资源id
	if res_id == 0 then
		real_res = RoleWGData.GetDefRoleModelRes(sex, prof, part_type)
	else
		local sex_dif_id = sex == GameEnum.MALE and 1 or 3
		local part_dif_id = 0
		local prof_dif_id = 1
		if part_type == ROLE_SKIN_TYPE.BODY then
			part_dif_id = 2
		elseif part_type == ROLE_SKIN_TYPE.FACE then
			part_dif_id = 3
		elseif part_type == ROLE_SKIN_TYPE.HAIR then
			part_dif_id = 4
		end

		real_res = sex_dif_id * 1000000 + part_dif_id * 100000 + prof_dif_id * 1000 + res_id
	end

    return real_res
end

-- 组装资源id
function RoleWGData.GetRealmPartModelRes(part_type, res_id, sex, prof)
	res_id = res_id or 0
    prof = prof or 1
    if prof > 10 then
    	prof = prof % 10
    end

	local real_res = 0

    --使用配置表默认资源id
	if res_id == 0 then
		--real_res = RoleWGData.GetDefRoleModelRes(sex, prof, part_type)
		res_id = 1101
		print_error("这里取了默认的变身数据", part_type, res_id, sex, prof, debug.traceback())
	end

	local sex_dif_id = sex == GameEnum.MALE and 1 or 3
	real_res = sex_dif_id * 1000000 + res_id

    return real_res
end

-- 获取资源编号（非组装资源id
function RoleWGData.GetServerUsePartModelRes(res_id)
	return res_id % 1000
end

-- 是否屏蔽职业
function RoleWGData:GetIsShieldSexAndProf(sex, prof)
	local cfg = self:GetJobConfig(sex, prof)
	if not cfg then
		return true
	end

	return cfg.is_shield == 1
end

-- 获取角色模型默认组装资源
function RoleWGData.GetJobModelId(sex, prof)
    if prof > 10 then
        prof = prof % 10
    end

    local job_cfg = RoleWGData.Instance:GetJobConfig(sex, prof)
    if job_cfg then
        return job_cfg.default_model
    end
	
    return sex == GameEnum.FEMALE and 3101001 or 1101001
end

-- 获取武器默认组装资源
function RoleWGData.GetJobWeaponId(sex, prof)
    if prof > 10 then
        prof = prof % 10
    end

    local job_cfg = RoleWGData.Instance:GetJobConfig(sex, prof)
    if job_cfg then
        return job_cfg.default_weapon, 0
    end

    return 900100101, 0
end

--获取三个部位（头发、脸、身体）的默认组装资源
function RoleWGData.GetDefRoleModelRes(sex, prof, part)
    local res_id = 0
    if prof == nil or part == nil then
        return res_id
    end

    if prof > 10 then
    	prof = prof % 10
    end

    local cfg = RoleWGData.Instance:GetJobConfig(sex, prof)
    if cfg == nil then
        return res_id
    end

    if part == ROLE_SKIN_TYPE.HAIR then
        res_id = cfg.default_hair or 0
    elseif part == ROLE_SKIN_TYPE.BODY then
        res_id = cfg.default_body or 0
    elseif part == ROLE_SKIN_TYPE.FACE then
        res_id = cfg.default_face or 0
    end

    return res_id
end

-- 获取部位默认编号（非组装资源id
function RoleWGData:GetAllDefaultPartID(sex, prof)
	if prof > 10 then
    	prof = prof % 10
    end

    local job_cfg = self:GetJobConfig(sex, prof)
    local default_face, default_hair, default_body = 1, 1, 1
    if job_cfg then
        default_face = RoleWGData.GetServerUsePartModelRes(job_cfg.default_face)
        default_hair = RoleWGData.GetServerUsePartModelRes(job_cfg.default_hair)
        default_body = RoleWGData.GetServerUsePartModelRes(job_cfg.default_body)
    end

    return default_face, default_hair, default_body
end

-- 获取部位默认编号（非组装资源id
function RoleWGData:GetSinglePartDefaultID(sex, prof, part)
	if prof > 10 then
    	prof = prof % 10
    end

    local job_cfg = self:GetJobConfig(sex, prof)
    if not job_cfg then
		return 0
    end

	if part == ROLE_SKIN_TYPE.HAIR then
        return RoleWGData.GetServerUsePartModelRes(job_cfg.default_hair)
    elseif part == ROLE_SKIN_TYPE.BODY then
        return RoleWGData.GetServerUsePartModelRes(job_cfg.default_body)
    elseif part == ROLE_SKIN_TYPE.FACE then
        return RoleWGData.GetServerUsePartModelRes(job_cfg.default_face)
    end

    return 0
end

-- 检测默认部位资源是否合法
function RoleWGData:CheckDefaultResIsLegal(sex, prof, face_res, hair_res, body_res)
	if not face_res or not hair_res or not body_res then
		return false
	end

	if prof > 10 then
    	prof = prof % 10
    end
	
	local default_face, default_hair, default_body = self:GetAllDefaultPartID(sex, prof)
	if default_face == face_res and default_hair == hair_res and default_body == body_res then
		return true
	end

	local check_func = function (part_type, res_id)
		local can_select_list = LoginWGData.Instance:GetCreateRoleDIYCfg(part_type, sex, prof)
		for k,v in pairs(can_select_list) do
			if v.resouce_id == res_id then
				return true
			end
		end

		return false
	end

	local check_face = check_func(ROLE_SKIN_TYPE.FACE, face_res)
	local check_hair = check_func(ROLE_SKIN_TYPE.HAIR, hair_res)
	local check_body = check_func(ROLE_SKIN_TYPE.BODY, body_res)

	return check_face and check_hair and check_body
end

-- 获取模型三部位展示的资源  让玩家选的自定义的face贯穿整个游戏
function RoleWGData.GetShowRoleSkinPartRes(sex, prof, fashion_id, def_body, def_face, def_hair)
	fashion_id = fashion_id or 0
	def_body = def_body or 0
	def_face = def_face or 0
	def_hair = def_hair or 0

	local face_res = RoleWGData.GetPartModelRes(ROLE_SKIN_TYPE.FACE, def_face, sex, prof)
	local hair_res = RoleWGData.GetPartModelRes(ROLE_SKIN_TYPE.HAIR, def_hair, sex, prof)
	local body_res = RoleWGData.GetPartModelRes(ROLE_SKIN_TYPE.BODY, def_body, sex, prof)

	if fashion_id == 2 and sex ~= 0 then
		print_error("角色取时装资源发生错误，男角色取到了女角色的时装", debug.traceback())
	end

	if fashion_id ~= 0 then
		local fs_face_res, fs_hair_res, fs_body_res = NewAppearanceWGData.Instance:GetRolePartResByResId(fashion_id, sex, prof)
		-- 特殊时装 使用时装全套资源
		if NewAppearanceWGData.Instance:GetModelIsShowAllSuit(fashion_id) then
			face_res = fs_face_res
			hair_res = fs_hair_res
			body_res = fs_body_res
		-- 只用时装的头和身体
		else
			hair_res = fs_hair_res
			body_res = fs_body_res
		end
	end

	return body_res, face_res, hair_res
end

-- 获取模型三部位展示的资源  让玩家选的自定义的face贯穿整个游戏
function RoleWGData.GetShowRoleRealmSkinPartRes(sex, prof, def_body, def_face, def_hair)
	def_body = def_body or 0
	def_face = def_face or 0
	def_hair = def_hair or 0

	local face_res = RoleWGData.GetRealmPartModelRes(ROLE_SKIN_TYPE.FACE, def_face, sex, prof)
	local hair_res = RoleWGData.GetRealmPartModelRes(ROLE_SKIN_TYPE.HAIR, def_hair, sex, prof)
	local body_res = RoleWGData.GetRealmPartModelRes(ROLE_SKIN_TYPE.BODY, def_body, sex, prof)

	return body_res, face_res, hair_res
end

-- 获取玩家的自定义部位
function RoleWGData:GetMainRoleDefSkinPartRes()
	local appe_data = self.role_vo.appearance or {}
	return appe_data.default_body_res_id, appe_data.default_face_res_id, appe_data.default_hair_res_id
end

function RoleWGData:GetMainRoleDiyAppearanceInfo()
	local appe_data = self.role_vo.role_diy_appearance or {}
	return appe_data
end

--保存服务端下发下来的战力
--有一些场景需要客户端做假的战力展示
function RoleWGData:SetOriginCapability(cap)
	self.origin_capability = cap
end

function RoleWGData:GetOriginCapability()
	return self.origin_capability
end

-- 战力打印开关
function RoleWGData:GetShowCapPrintSwitch()
	return self.cap_attr_show_switch
end

function RoleWGData:ChangeShowCapPrintSwitch()
	self.cap_attr_show_switch = not self.cap_attr_show_switch
end

RoleWGData.NeedFlushModelAppeType = {
	[ROLE_APPE_TYPE.WING] = 1,
	[ROLE_APPE_TYPE.FABAO] = 2,
	[ROLE_APPE_TYPE.SHENBING] = 3,
	[ROLE_APPE_TYPE.LINGGONG] = 4,
	[ROLE_APPE_TYPE.LINGYI] = 5,
	[ROLE_APPE_TYPE.FASHION] = 6,
	[ROLE_APPE_TYPE.FOOT] = 7,
	[ROLE_APPE_TYPE.HALO] = 8,
	[ROLE_APPE_TYPE.FACE] = 9,
	[ROLE_APPE_TYPE.YAO] = 10,
	[ROLE_APPE_TYPE.WEI] = 11,
	[ROLE_APPE_TYPE.HAND] = 12,
	[ROLE_APPE_TYPE.JIANZHEN] = 13,
}

function RoleWGData.IsNeedFlushModelAppeType(appe_type)
	return RoleWGData.NeedFlushModelAppeType[appe_type] ~= nil
end

RoleWGData.notify_data_list = {}
function RoleWGData.GetNotifyData()
    local data = table.remove(RoleWGData.notify_data_list)
    if nil == data then
        data = {}
    end

    return data
end

function RoleWGData.ReleaseNotifyData(data)
	data.callback = nil
    table.insert(RoleWGData.notify_data_list, data)
end

-- 获取货币类型
function RoleWGData.GetPayMoneyType()
	return GLOBAL_CONFIG.param_list.pay_money_type or PAY_MONEY_TYPES.RMB
	--return PAY_MONEY_TYPES.DOLLAR
	--return PAY_MONEY_TYPES.GAME_COUNT
end

-- 货币兑换
function RoleWGData.GetPayMoneyChange(money_num, recharge_type, recharge_seq)
	money_num = money_num or 0
	local map_cfg = RechargeWGData.Instance:GetRechargeMapCfg(money_num, recharge_type, recharge_seq)
	if map_cfg then
		local type = RoleWGData.GetPayMoneyType()
		if type == PAY_MONEY_TYPES.DOLLAR then
			money_num = map_cfg.USD
		end
	else
		money_num = RoleWGData.GetFakePayMoneyChange(money_num)
	end

	return money_num
end

-- 假货币兑换(多用于原价显示)
function RoleWGData.GetFakePayMoneyChange(money_num)
	money_num = money_num or 0
	local type = RoleWGData.GetPayMoneyType()
	if type == PAY_MONEY_TYPES.DOLLAR then
		money_num = math.ceil(money_num / 6)
	end

	return money_num
end

-- 获取货币描述
function RoleWGData.GetPayMoneyStr(money_num, recharge_type, recharge_seq, color)
	local map_cfg = RechargeWGData.Instance:GetRechargeMapCfg(money_num, recharge_type, recharge_seq)

	if map_cfg then
		local is_cangjinshangpu_score = ((map_cfg or {}).is_cangjinshangpu_score or 0) == 1
		if is_cangjinshangpu_score then
			if color then
				return string.format(Language.Common.ColorMoneyTypes[3], color, money_num)
			else
				return string.format(Language.Common.MoneyTypes[3], money_num)
			end
		end
	end

	local type = RoleWGData.GetPayMoneyType()
	if not recharge_type or not recharge_seq then
		money_num = RoleWGData.GetFakePayMoneyChange(money_num)
	else
		money_num = RoleWGData.GetPayMoneyChange(money_num, recharge_type, recharge_seq)
	end

	if color then
		return string.format(Language.Common.ColorMoneyTypes[type], color, money_num)
	else
		return string.format(Language.Common.MoneyTypes[type], money_num)
	end
end

-- 是否屏蔽高额充值
function RoleWGData.GetIsShieldHighCharge()
	return GLOBAL_CONFIG.param_list.shield_high_charge == 1
end

-- 获取玩家php地区类型
function RoleWGData.GetAreaType()
	return GLOBAL_CONFIG.param_list.area_type or AREA_TYPES.DEFAULT
end

-- 获取当前的角色染色方案配置信息
function RoleWGData.GetRoleMaterialsByProjectId(role_res_id, body_id, face_id, hair_id)
	return GLOBAL_CONFIG.param_list.area_type or AREA_TYPES.DEFAULT
end
