ActivityTipsView = ActivityTipsView or BaseClass(SafeBaseView)

function ActivityTipsView:__init()
	self.is_modal = true
	self.view_layer = UiLayer.MainUIHigh
	self.view_name = "ActivityTipsView"
	self:AddViewResource(0, "uis/view/activity_tips_ui_prefab", "layout_activity_tips")
    self.cur_act_list = {}
end

function ActivityTipsView:__delete()
end

function ActivityTipsView:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("activity_time") then
        CountDownManager.Instance:RemoveCountDown("activity_time")
    end

	if self.day_count_change_event then
		GlobalEventSystem:UnBind(self.day_count_change_event)
		self.day_count_change_event = nil
	end
end

function ActivityTipsView:CloseCallBack()
	self.cur_act_list = {}
	self.cur_show_data = nil
	if CountDownManager.Instance:HasCountDown("activity_time") then
        CountDownManager.Instance:RemoveCountDown("activity_time")
    end
end

function ActivityTipsView:LoadCallBack()
    self.cur_show_data = nil
	self.node_list["btn_center_act"].button:AddClickListener(BindTool.Bind1(self.ClickActEnterBtn, self))
    self.node_list["btn_close"].button:AddClickListener(BindTool.Bind1(self.ClickBtnClose, self))

	self.day_count_change_event = GlobalEventSystem:Bind(OtherEventType.DAY_COUNT_CHANGE, BindTool.Bind(self.DayCounterChange, self))
end

function ActivityTipsView:DayCounterChange(day_counter_id)
	local times = YunbiaoWGData.Instance:GetHusongRemainTimes()

	if times <= 0 then
		self:SelfRemoveActivity(ACTIVITY_TYPE.HUSONG)
	end
end

function ActivityTipsView:ShowIndexCallBack()
	self:FlushActivity()
end

function ActivityTipsView:FlushActivity()
	if CountDownManager.Instance:HasCountDown("activity_time") then
		CountDownManager.Instance:RemoveCountDown("activity_time")
	end

	if #self.cur_act_list > 0 then
		self.cur_show_data = table.remove(self.cur_act_list, 1)
		if self.cur_show_data then
            --名字
			self.node_list["act_name"].text.text = self.cur_show_data.title

			-- 图标
			if self.cur_show_data.act_tips_icon then
				local act_cfg = ActivityWGData.Instance:GetDailyActivityDailyCfg(self.cur_show_data.act_type)
				if act_cfg then
					local bundle, asset = ResPath.GetMainUIIcon(act_cfg.res_name)
					-- local bundle, asset = ResPath.GetNoPackPNG()
					self.node_list["act_tips_icon"].image:LoadSprite(bundle, asset, function()
						self.node_list["act_tips_icon"].image:SetNativeSize()
					end)
				end
			end

            -- 标签
			if self.cur_show_data.act_tips_flag then
				local bundle, asset = ResPath.GetActTipsFlag(self.cur_show_data.act_tips_flag)
				self.node_list["act_tips_flag"].image:LoadSprite(bundle, asset, function()
					self.node_list["act_tips_flag"].image:SetNativeSize()
				end)
			end

            self.node_list.act_tips_flag_text.text.text = Language.ActivityTips.FlagName[self.cur_show_data.act_tips_flag]
		end
	end

    if self.cur_show_data == nil then
        return
    end

	-- 1vn增加别的提示
	if self.cur_show_data.act_type == ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_ULTIMATE_BATTLE then
		local act_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_ULTIMATE_BATTLE)
		local time_tab = os.date("*t", act_status.end_time)
		-- 如果后端时间戳误差1则取整
		if time_tab.min % 10 == 9 then
			time_tab.min = time_tab.min + 1
			if time_tab.min >= 60 then
				time_tab.min = 0
				time_tab.hour = time_tab.hour + 1
			end
		elseif time_tab.min % 10 == 1 then
			time_tab.min = time_tab.min - 1
		end

		local open_time_str = string.format("%d:%02d", time_tab.hour, time_tab.min)
		self.node_list["act_time_quest"].text.text = string.format(Language.CangMingBuy.ActivityMateTime, open_time_str)
		return
	end

	local time = ActivityWGData.Instance:GetActivityResidueTime(self.cur_show_data.act_type)

    if time <= 0 then
        self:OnComplete()
        return
    else
        CountDownManager.Instance:AddCountDown("activity_time",
            BindTool.Bind(self.FinalUpdateTimeCallBack, self),
            BindTool.Bind(self.OnComplete, self),
            nil, time, 1)
    end
end

function ActivityTipsView:OnComplete()
    self.node_list.act_time_quest.text.text = ""
    self:ClickBtnClose()
end

function ActivityTipsView:FinalUpdateTimeCallBack(now_time, total_time)
    local time = math.ceil(total_time - now_time)
    local time_str = TimeUtil.FormatSecondDHM9(time)
    self.node_list["act_time_quest"].text.text = string.format(Language.CangMingBuy.ActivityTime, time_str)
end

function ActivityTipsView:SetActivityData(data)
	if not data or (self.cur_show_data and self.cur_show_data.act_type == data.act_type) then
		return
	end

	for i,v in ipairs(self.cur_act_list) do
		if v.act_type == data.act_type then
			return
		end
	end

	table.insert(self.cur_act_list, data)
end

function ActivityTipsView:RemoveActivity(act_type)
	for i,v in ipairs(self.cur_act_list) do
		if v.act_type == act_type then
			table.remove(self.cur_act_list, i)
			break
		end
	end

	if #self.cur_act_list == 0 and not self.cur_show_data then
		self:Close()
	end
end

function ActivityTipsView:SelfRemoveActivity(act_type)
	for i,v in ipairs(self.cur_act_list) do
		if v.act_type == act_type then
			table.remove(self.cur_act_list, i)
			break
		end
	end

	if act_type == ACTIVITY_TYPE.HUSONG then
		self.cur_show_data = nil
	end

	if #self.cur_act_list == 0 and not self.cur_show_data then
		self:Close()
		return
	end
	
	if self:IsOpen() then
		self:FlushActivity()
	end

end

function ActivityTipsView:ClickActEnterBtn()
	if not self.cur_show_data then
		return
	end

	if self.cur_show_data.act_type == ACTIVITY_TYPE.KF_HONORHALLS then
		ViewManager.Instance:Open(GuideModuleName.ConquestWarView, TabIndex.honorhalls_tower)
	else
		ActivityWGCtrl.Instance:AutoQuickJoinActByType(self.cur_show_data.act_type)
	end

	self:ClickBtnClose()
-- 	if #self.cur_act_list > 0 then
-- 		self:FlushActivity()
-- 	else
-- 		self:Close()
-- 	end
end

function ActivityTipsView:ClickBtnClose()
    if #self.cur_act_list > 0 then
		self:FlushActivity()
	else
		self:Close()
	end
end
