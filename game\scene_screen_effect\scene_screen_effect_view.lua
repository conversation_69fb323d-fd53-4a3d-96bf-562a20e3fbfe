SceneScreenEffectView = SceneScreenEffectView or BaseClass(SafeBaseView)

function SceneScreenEffectView:__init()
	self.view_layer = UiLayer.MainUILow
	self.view_name = GuideModuleName.SceneScreenEffectView
	self.open_tween = nil
	self.close_tween = nil
	self.active_close = false

	self:AddViewResource(0, "uis/view/scene_screen_effect_prefab", "layout_scene_screen_effect")
end

function SceneScreenEffectView:__delete()
end

function SceneScreenEffectView:OpenCallBack()

end

function SceneScreenEffectView:CloseCallBack()
end

function SceneScreenEffectView:LoadCallBack()
end

function SceneScreenEffectView:ReleaseCallBack()
	self.data = nil
end


function SceneScreenEffectView:ShowIndexCallBack(index)
end


function SceneScreenEffectView:OnFlush(param_list, index)
	for k,v in pairs(param_list) do
		if k == "all" then
			self.data = v.data
		end
	end
	if not self.data then
		return
	end

	self.node_list["screen_effect"]:ChangeAsset(ResPath.GetEffectUi(self.data.effcet_name))
end