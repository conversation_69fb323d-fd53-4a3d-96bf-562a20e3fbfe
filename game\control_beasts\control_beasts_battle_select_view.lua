ControlBeastsBattleSelectView = ControlBeastsBattleSelectView or BaseClass(SafeBaseView)

function ControlBeastsBattleSelectView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(950, 590)})
	self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_beasts_battle_select")

	self.battle_data = nil
end

function ControlBeastsBattleSelectView:__delete()

end

function ControlBeastsBattleSelectView:SetSelectData(battle_data)
	self.battle_data = battle_data
end


function ControlBeastsBattleSelectView:ReleaseCallBack()
	self.battle_data = nil
	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.ControlBeastsBattleSelectView, self.get_guide_ui_event)
	self.get_guide_ui_event = nil

	if self.select_list_view then
		self.select_list_view:DeleteMe()
		self.select_list_view = nil
	end
end

function ControlBeastsBattleSelectView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.ContralBeasts.TitleName3

	if nil == self.select_list_view then
        self.select_list_view = AsyncBaseGrid.New()
        local bundle = "uis/view/control_beasts_ui_prefab"
        local asset = "layout_select_battle_cell"
        self.select_list_view:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list["ph_select_baoshi_list"],
            assetBundle = bundle, assetName = asset, itemRender = BeastsBattleItem})
		self.select_list_view:SetStartZeroIndex(false)
	end

	XUI.AddClickEventListener(self.node_list.beasts_battle_select_get, BindTool.Bind2(self.BeastsBattleSelectGet, self))
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.ControlBeastsBattleSelectView, self.get_guide_ui_event)
end

function ControlBeastsBattleSelectView:OnFlush(param_t)
	if not self.battle_data then
		return
	end

	if #self.battle_data <= 0  then
		self.node_list["beast_tips"]:SetActive(true)
	else
		self.node_list["beast_tips"]:SetActive(false)
	end

	self.select_list_view:SetDataList(self.battle_data)
end

-- 获取途径
function ControlBeastsBattleSelectView:BeastsBattleSelectGet()
	ControlBeastsWGCtrl.Instance:OpenBeastsPrizeDraw()
	self:Close()
end

-- 引导选取第一个上阵
function ControlBeastsBattleSelectView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == "battle_select_guide_btn" then
		return self.node_list[ui_name], BindTool.Bind(self.OperateSelectFirstApplyBeast, self)
	end
end

-- 选取第一个上阵
function ControlBeastsBattleSelectView:OperateSelectFirstApplyBeast()
	if (not self.battle_data) or (not self.battle_data[1]) then
		return
	end

	local data = self.battle_data[1]
	if not data then return end

	if data.beast_data and data.stand_by_slot then
		ControlBeastsWGCtrl.Instance:SendOperateTypeApplyBeast(data.beast_data.bag_id, data.stand_by_slot)
	end
	self:Close()
end
-------------------------------------------------
-------------------------------------------------

BeastsBattleItem = BeastsBattleItem  or BaseClass(BaseRender)

function BeastsBattleItem:LoadCallBack()
	if not self.beast_item then
		self.beast_item = ItemCell.New(self.node_list.item_pos)
		self.beast_item:SetIsShowTips(true)
	end

	XUI.AddClickEventListener(self.node_list.btn_click, BindTool.Bind(self.OnClick, self))
	XUI.AddClickEventListener(self.node_list.btn_disboard, BindTool.Bind(self.OnClick, self))
end

function BeastsBattleItem:ReleaseCallBack()
	if self.beast_item then
		self.beast_item:DeleteMe()
		self.beast_item = nil
	end
end

function BeastsBattleItem:OnClick()
	--看看策划是否判读替换或是上阵
	--发送协议
	--关闭弹窗
	if not self.data then return end

	if self.data.beast_data and self.data.beast_data.server_data then
		local server_data = self.data.beast_data.server_data

		if server_data.stand_by_slot ~= nil and server_data.stand_by_slot ~= -1 then	--下阵
			ControlBeastsWGCtrl.Instance:SendOperateTypeRetreatBeast(server_data.stand_by_slot)
		else
			if self.data.beast_data and self.data.stand_by_slot then
				ControlBeastsWGCtrl.Instance:SendOperateTypeApplyBeast(self.data.beast_data.bag_id, self.data.stand_by_slot)
			end
		end
	end

	ControlBeastsWGCtrl.Instance:CloseBeastsBattleSelectView()
end

function BeastsBattleItem:OnFlush()
	if not self.data then return end

	if self.data.beast_data and self.data.beast_data.server_data then
		local server_data = self.data.beast_data.server_data
		self.beast_item:SetData({item_id = server_data.beast_id, is_beast = true, bag_id = self.data.beast_data.bag_id})---这里需要其他的东西在加，看策划需求
		self.beast_item:SetBeastBattleTypeIcon(server_data.stand_by_slot)

		self.node_list.level_txt.text.text = string.format(Language.Common.Level1, server_data.beast_level)
		self.node_list.cap_value.text.text = 0	--这里获取一下战斗力

		local cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
		if cfg then
			self.node_list.lbl_name.text.text = ToColorStr(cfg.beast_name, ITEM_COLOR[cfg.beast_color])
		end

		-- local cap, attribute = ControlBeastsWGData.Instance:GetBeastsCapValue(server_data)
		self.node_list.common_capability:CustomSetActive(self.data.cap_value > 0)

		if self.data.cap_value > 0 then
			self.node_list.cap_value.text.text = self.data.cap_value
		end

		local str = self.data.is_change and Language.ContralBeasts.SelectError3 or Language.ContralBeasts.SelectError2
		self.node_list.btn_disboard:CustomSetActive(server_data.stand_by_slot ~= nil and server_data.stand_by_slot ~= -1)
		self.node_list.btn_click:CustomSetActive(not (server_data.stand_by_slot ~= nil and server_data.stand_by_slot ~= -1))

		self.node_list.btn_txt.text.text = str
	end
end

