
ShopItemRender = ShopItemRender or BaseClass(BaseRender)

function ShopItemRender:__init()

end

function ShopItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function ShopItemRender:LoadCallBack()
	self.img_lock = nil

	self.item_cell = ItemCell.New(self.node_list["ph_item"])
	self.item_cell:SetClickCallBack(BindTool.Bind(self.OnClick, self))
	self.item_cell:SetIsShowTips(false)
end

function ShopItemRender:OnFlush()
	if nil == self.data then
		return
	end

	self:ResetUI()
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.itemid)
	if nil == item_cfg then
		print_error("没有配置 " , self.data.itemid)
		return
	end

	self.item_cell:SetData({item_id = self.data.itemid, is_bind = self.data.is_bind})
	self.item_cell:SetDefaultEff(true)
	self.item_cell:SetBindIconVisible(false)
	self.node_list.lbl_item_name.text.text = item_cfg.name

	local is_vip_limit, vip_limit_str, is_can_buy = ShopWGData.Instance:IsVipLimit(self.data.seq)
	self.node_list["img_vip"]:SetActive(is_vip_limit)
	if is_vip_limit and vip_limit_str then
		self.node_list["text_vip"].text.text = vip_limit_str
	end

	self.node_list["img_shop_icon"]:SetActive(0 ~= self.data.jiaobiao_icon)
	self.node_list.zhekou.text.text = self.data.jiaobiao_text or ""

	---[[ 活动期间打折
	local discount = ShopWGData.Instance:GetActDiscountByItemSeq(self.data.seq)
	if discount > 0 then
		self.node_list["img_shop_icon"]:SetActive(true)
		self.node_list.zhekou.text.text = string.format(Language.SmallGoal.Zhe, CommonDataManager.GetDaXie(discount))
	end
	--]]
 	
	local list = ShopWGData.Instance:ExplainComposeStr(self.data.seq)
	local is_every = list[1] and ShopWGData.Instance:IsEvery(list[1])
 	self.item_cell:SetRightBottomTextVisible(false)
	if list[1] then
		self.node_list["text_condition1"].text.text = list[1].str
	else
		self.node_list["text_condition1"].text.text = Language.Shop.NoLimitBuy
	end

	local is_limit = ShopWGData.Instance:IsLimit(self.data.seq)
	is_limit = is_limit and (list[2] ~= nil)
	local is_can_cell, sell_str = ShopWGData.Instance:IsCanCell(self.data.seq, true)
	if not is_can_cell or is_limit then
		self.node_list["text_empty"].text.text = sell_str or list[2].str
		self.node_list["img_empty"]:SetActive(true)
	else
		self.node_list["img_empty"]:SetActive(false)
	end
	
	local can_cell, back_str, condition_type = ShopWGData.Instance:CheckItemCondition(self.data.seq, 1, 1)

	self.node_list.limit_panel:SetActive(back_str and not is_vip_limit)
	if back_str and not is_vip_limit then
		self.node_list["text_condition2"].text.text = back_str

		self.node_list.limit_detail:SetActive(condition_type == ConditionManager.Check_Type.Type109)
		if condition_type == ConditionManager.Check_Type.Type109 then
			local cur_level = FuBenPanelWGData.Instance:GetPassLevel()
			self.node_list.text_condition3.text.text = string.format(Language.Shop.FuBenWelkinLimitDes2, cur_level)
		end
	end

	self:FlushPrice()
end

function ShopItemRender:FlushPrice()
	local bundel, asset = ResPath.GetCommonIcon(Shop_Money[self.data.price_type].url)
	local scale = Shop_Money[self.data.price_type].scale
	local vector3 = Vector3(scale, scale, scale)
	local price = self.data.price

	-- 活动期间打折
	local discount = ShopWGData.Instance:GetActDiscountByItemSeq(self.data.seq)
	if discount > 0 then
		price = price * discount / 10
	end

	self.node_list["img_money2"].image:LoadSprite(bundel, asset, function ()
		self.node_list["img_money2"].image:SetNativeSize()
		self.node_list["img_money2"].transform.localScale = vector3
	end)
	self.node_list["img_old_money2"].image:LoadSprite(bundel, asset, function ()
		self.node_list["img_old_money2"].image:SetNativeSize()
		self.node_list["img_old_money2"].transform.localScale = vector3
	end)

	self.node_list["img_money2"]:SetActive(price > 0)

	self.node_list["text_money2"].text.text = price > 0 and price or Language.Shop.FreeBuy
	self.node_list["text_old_money2"].text.text = price
	
	if 0 ~= self.data.old_price or discount > 0 then
		self.node_list["text_money1"].text.text = discount > 0 and self.data.price or self.data.old_price
		self.node_list["layout_gold_old_show"]:SetActive(true)
		self.node_list["layout_gold_show"]:SetActive(false)
	else
		self.node_list["layout_gold_show"]:SetActive(true)
		self.node_list["layout_gold_old_show"]:SetActive(false)
	end
end

function ShopItemRender:OnClick()
	if nil == self.data then
		print_error("当前Item没有数据")
		return
	end
	BaseRender.OnClick(self)
	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(self.data.itemid)
	if not item_cfg then
		print_error("the item don't exist:::", self.data.itemid)
		return
	end

	local check_cond, back_str = ShopWGData.Instance:CheckItemCondition(self.data.seq)
	local can_cell = true
	if check_cond then
		can_cell = ShopWGData.Instance:IsCanCell(self.data.seq)
	end

	local is_vip_limit = ShopWGData.Instance:CheckConditionState(self.data.seq, 1, ConditionManager.Check_Type.Type103)
 	if not can_cell or (not check_cond and not is_vip_limit) then
 		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(self.data.itemid)
 		if big_type == GameEnum.ITEM_BIGTYPE_EXPENSE then
 			TipWGCtrl.Instance:OpenItem({item_id = self.data.itemid}, ItemTip.FROM_EQUIPBROWSE, nil)
 		else
 			local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(self.data.seq)
			TipWGCtrl.Instance:OpenItem({item_id = self.data.itemid, shop_type = shop_cfg.shop_type}, ItemTip.SHOP_BUY, nil)
		end
 		return
 	end

 	if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT and not ItemWGData.GetIsXiaogGui(self.data.itemid) then
 		local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(self.data.seq)

 		local special_buy_info = {}
 		special_buy_info.buy_count = 1
 		special_buy_info.max_buy_count = ShopWGData.Instance:GetMaxBuyNum(self.data.seq)
		special_buy_info.price = shop_cfg.price
		special_buy_info.price_type = shop_cfg.price_type
		special_buy_info.itemid = self.data.itemid

		local buy_func = function (buy_count)
			local function ok_func()
				local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(self.data.seq)
					--你策划要求仙玉>=100就弹多一个二次确认框
				-- local buy_count = self.base_tips:GetBuyCount()
				if buy_count * shop_cfg.price >= 100 and shop_cfg.price_type == Shop_Money_Type.Type1 then
					local name_str = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
					local item_id = item_cfg.id
					TipWGCtrl.Instance:OpenAlertTips(string.format(Language.Common.CommonAlertFormat1, shop_cfg.price * buy_count, name_str, buy_count), function ()
						ShopWGCtrl.Instance:SendShopBuy(item_id, buy_count, 0, 0, self.data.seq)
					end)
				else
					ShopWGCtrl.Instance:SendShopBuy(item_cfg.id, buy_count, 0, 0, self.data.seq)
				end
			end
			if not ShopWGData.Instance:CheckBangYuToBuy(self.data.seq, buy_count) then
				TipWGCtrl.Instance:OpenAlertByNotBindYu(ok_func)
			else
				ok_func()
			end
		end

 		TipWGCtrl.Instance:OpenItem({item_id = self.data.itemid, buy_limit = self.data.buy_limit,special_buy_info = special_buy_info,special_buy_call_back = buy_func}, ItemTip.FROM_SHOP_EQUIP)
 		return
 	end

	local is_bind_gold = self.data.consume_type == GameEnum.CONSUME_TYPE_BIND
	local num = ShopWGData.Instance:GetMaxBuyNum(self.data.seq)
	local def_buy_count = self:GetDefaultBuyCount()
	ShopTip.Instance:SetData(item_cfg, self.data.consume_type, GameEnum.SHOP, def_buy_count , self.data.seq, self.data.buy_limit, num)

	local view = ViewManager.Instance:GetView(GuideModuleName.Shop)
	if view then
		view:ClearItemIndex()
	end
end

function ShopItemRender:ResetUI()
	self.node_list["img_vip"]:SetActive(false)
	self.node_list["img_empty"]:SetActive(false)
	self.node_list["limit_panel"]:SetActive(false)
 	self.node_list["layout_gold_show"]:SetActive(false)
 	self.node_list["img_shop_icon"]:SetActive(false)
 	self.node_list["text_condition1"].text.text = ""
 	self.node_list["text_condition2"].text.text = ""
end

--策划需求:特殊商品类型,购买时默认显示玩家当前最大可购买量
function ShopItemRender:GetDefaultBuyCount()
	local count = 1
	local cur_shop_data = ShopWGData.Instance:GetShopCfgSeq(self.data.seq)
	if cur_shop_data then
		if cur_shop_data.shop_type == SHOP_BIG_TYPE.SHOP_TYPE_6 	--声望商店
		or cur_shop_data.shop_type == SHOP_BIG_TYPE.SHOP_TYPE_9 then--青云商店

			local temp_max_num = ShopWGData.Instance:GetMaxBuyNum(self.data.seq)
			local my_cur_type_money = 0
			my_cur_type_money = RoleWGData.Instance:GetMoney(cur_shop_data.price_type)
			count = math.floor(my_cur_type_money / cur_shop_data.price)
			if temp_max_num < count then
				count = temp_max_num
			elseif count <= 0 then
				count = 1
			end

		end
	end
	return count
end