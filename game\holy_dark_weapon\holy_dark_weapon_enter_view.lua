HolyDarkWeaponEnterView = HolyDarkWeaponEnterView or BaseClass(SafeBaseView)

function HolyDarkWeaponEnterView:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(false)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
    self:ClearViewTween()
	self:AddViewResource(0, "uis/view/holy_dark_ui_prefab", "layout_cangbaoge_enter")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_common_top_panel")
end

function HolyDarkWeaponEnterView:ReleaseCallBack()
    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end
end

function HolyDarkWeaponEnterView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.HolyDarkWeapon.EnterViewName
    XUI.AddClickEventListener(self.node_list.holy_weapon_enter_btn, BindTool.Bind(self.OnClickEnterBtn, self, true))
    XUI.AddClickEventListener(self.node_list.dark_weapon_enter_btn, BindTool.Bind(self.OnClickEnterBtn, self, false))
end

function HolyDarkWeaponEnterView:ShowIndexCallBack()
    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end

    self:ResetTweenRoot()
end

function HolyDarkWeaponEnterView:OnFlush()
    local holy_remind = HolyDarkWeaponWGData.Instance:GetRelicRemind(HolyDarkWeaponWGData.Weapon_type.HolyType)
    local dark_remind = HolyDarkWeaponWGData.Instance:GetRelicRemind(HolyDarkWeaponWGData.Weapon_type.DarkType)
    self.node_list.holy_remind:SetActive(holy_remind > 0)
    self.node_list.dark_remind:SetActive(dark_remind > 0)
end

function HolyDarkWeaponEnterView:OnClickEnterBtn(is_holy_weapon)
    self.is_holy_weapon = is_holy_weapon
    self:PlayTweenAnimation()
end

function HolyDarkWeaponEnterView:ResetTweenRoot()
    RectTransform.SetAnchoredPositionXY(self.node_list.cangbaoge_enter_left_txt.rect, 139, 102)
    RectTransform.SetAnchoredPositionXY(self.node_list.cangbaoge_enter_right_txt.rect, -144, 18)
    RectTransform.SetAnchoredPositionXY(self.node_list.cangbaoge_enter_right_root.rect, 0, 0)
    RectTransform.SetAnchoredPositionXY(self.node_list.cangbaoge_enter_left_root.rect, 0, 0)
    RectTransform.SetAnchoredPositionXY(self.node_list.cangbaoge_enter_sword.rect, -7, 0)
    self.node_list.cangbaoge_enter_mid.canvas_group.alpha = 1
	self.node_list.cangbaoge_enter_ball.rect.rotation = Quaternion.Euler(0, 0, 0)
    self.node_list.holy_weapon_enter_btn:SetActive(true)
    self.node_list.dark_weapon_enter_btn:SetActive(true)
end

function HolyDarkWeaponEnterView:PlayTweenAnimation()
    if not self.sequence then
        local weapon_type = self.is_holy_weapon and HolyDarkWeaponWGData.Weapon_type.HolyType or HolyDarkWeaponWGData.Weapon_type.DarkType
        local view_name = self.is_holy_weapon and GuideModuleName.HolyWeapon or GuideModuleName.DarkWeapon
        HolyDarkWeaponWGData.Instance:SetCurWeaponType(weapon_type)
        local is_open_view = FunOpen.Instance:OpenViewByName(view_name)

        if not is_open_view then
            return
        end

        self.node_list.holy_weapon_enter_btn:SetActive(false)
        self.node_list.dark_weapon_enter_btn:SetActive(false)

        self.sequence = DG.Tweening.DOTween.Sequence()
        local total_time = 1.5
        local dir = self.is_holy_weapon and 45 or -45

        local left_txt_tween = self.node_list.cangbaoge_enter_left_txt.rect:DOAnchorPosX(-1861, total_time)
        local right_txt_tween = self.node_list.cangbaoge_enter_right_txt.rect:DOAnchorPosX(1856, total_time)
        local ball_tween = self.node_list.cangbaoge_enter_ball.rect:DORotate(Vector3(0, 0, dir), total_time / 2, DG.Tweening.RotateMode.FastBeyond360):OnComplete(function()
            UITween.AlpahShowPanel(self.node_list.cangbaoge_enter_mid, false, total_time / 2)
        end)
        local sword_tween = self.node_list.cangbaoge_enter_sword.rect:DOAnchorPosY(-1000, total_time)
        local right_root_tween = self.node_list.cangbaoge_enter_right_root.rect:DOAnchorPosX(2000, total_time)
        local left_root_tween = self.node_list.cangbaoge_enter_left_root.rect:DOAnchorPosX(-2000, total_time):OnComplete(function()
            self:Close()
        end)

        self.sequence:Join(left_txt_tween)
        self.sequence:Join(right_txt_tween)
        self.sequence:Join(ball_tween)
        self.sequence:Join(sword_tween)
        self.sequence:Join(right_root_tween)
        self.sequence:Join(left_root_tween)
    end
end