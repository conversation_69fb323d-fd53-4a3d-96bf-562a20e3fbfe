HolyDarkMonthBuyView = HolyDarkMonthBuyView or BaseClass(SafeBaseView)
function HolyDarkMonthBuyView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/holy_dark_ui_prefab", "layout_holy_dark_month_buy")
end

function HolyDarkMonthBuyView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["buy_btn"], BindTool.Bind(self.OnClickBuy, self))
end

function HolyDarkMonthBuyView:ReleaseCallBack()
	self.relic_type = nil
end

function HolyDarkMonthBuyView:SetDataAndOpen(relic_type)
	self.relic_type = relic_type
    self:Open()
end

function HolyDarkMonthBuyView:OnFlush()
	if not self.relic_type then
		return
	end

	self:FlushPanelView()
end

function HolyDarkMonthBuyView:FlushPanelView()
	local info = HolyDarkWeaponWGData.Instance:GetRelicPowerItemInfoBySeq(self.relic_type)
	local month_cfg = HolyDarkWeaponWGData.Instance:GetRelicMonthBuyInfo(self.relic_type)
	if IsEmptyTable(info) or IsEmptyTable(month_cfg) then
		return
	end

	local state = info.power_flag ~= 0 -- 是否购买
	if state then
		self.node_list.price_text.text.text = Language.HolyDarkWeapon.MonthIsBuy
	else
		local price = RoleWGData.GetPayMoneyStr(month_cfg.price, month_cfg.rmb_type, month_cfg.rmb_seq)	
		self.node_list.price_text.text.text = price --购买价格
	end

	XUI.SetButtonEnabled(self.node_list.buy_btn, not state)
	local bundle, asset = ResPath.GetHolyDarkImg("a1_sq_sqyka_" .. self.relic_type)
    self.node_list.month_type_title.image:LoadSprite(bundle, asset, function()
        self.node_list.month_type_title.image:SetNativeSize()
    end)
end

function HolyDarkMonthBuyView:OnClickBuy()
	if not self.relic_type then
		return
	end

	local info = HolyDarkWeaponWGData.Instance:GetRelicPowerItemInfoBySeq(self.relic_type)
	local month_cfg = HolyDarkWeaponWGData.Instance:GetRelicMonthBuyInfo(self.relic_type)
	if IsEmptyTable(info) or IsEmptyTable(month_cfg) then
		return
	end

	local state = info.power_flag ~= 0 -- 是否购买
	if state then
		TipWGCtrl.Instance:ShowSystemMsg(Language.HolyDarkWeapon.MonthIsBuy)
		return
	end

	RechargeWGCtrl.Instance:Recharge(month_cfg.price, month_cfg.rmb_type, month_cfg.rmb_seq)
end
