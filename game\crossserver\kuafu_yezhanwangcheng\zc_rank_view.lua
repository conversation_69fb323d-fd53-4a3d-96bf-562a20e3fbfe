ZCRankView = ZCRankView or BaseClass(SafeBaseView)
ZCRankView.FromView = {
    YZWC = 10,
}

function ZCRankView:__init()
    self.view_style = ViewStyle.Half
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:SetMaskBg(true, true)
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_jiesuan_panel")
    self:AddViewResource(0, "uis/view/zc_result_ui_prefab", "zc_result_view")
end

function ZCRankView:__delete()

end

function ZCRankView:ReleaseCallBack()
    if self.rank_list then
        self.rank_list:DeleteMe()
        self.rank_list = nil
    end
end

function ZCRankView:LoadCallBack()
    self.node_list["btn_ok"].button:AddClickListener(BindTool.Bind(self.OnClickBtnOk, self))
    self.rank_list = AsyncListView.New(<PERSON>CRank<PERSON><PERSON>, self.node_list["rank_list"])

    self.node_list.victory:SetActive(true)
    self.node_list.lose:SetActive(false)
	local bundle, asset = ResPath.GetCommonPanel("a3_gxhd_lbl_pm")
    self.node_list["title_img"].image:LoadSprite(bundle, asset, function()
        self.node_list["title_img"].image:SetNativeSize()
    end)
end

function ZCRankView:OnClickBtnOk()
    self:Close()
end

function ZCRankView:OnFlush(param, ...)
    for i, v in pairs(param) do
        if i == ZCRankView.FromView.YZWC then
            self:FlushYeZhanWangCheng()
        end
    end
end

function ZCRankView:ShowFrontInfo(info, index)
    if info == nil then
        self.node_list["head_content_" .. index]:SetActive(false)
        return
    end
    self.node_list["head_content_" .. index]:SetActive(true)
    local name = BiZuoWGData.Instance:GetSetverNameFormat(info.user_name)
    self.node_list["name_" .. index].text.text = name
    self.node_list["score_" .. index].text.text = Language.YeZhanWangCheng.ScoreDesc .. info.score
    local head_key = info.user_key.temp_low .. info.user_key.temp_high
    XUI.UpdateRoleHead(self.node_list["img_head_" .. index], self.node_list["custom_head_" .. index], head_key, info.sex, info.prof, nil, true)
end

function ZCRankView:FlushYeZhanWangCheng()
    local list = KuafuYeZhanWangChengWGData.Instance:NightFightRankInfo()
    if not list.rank_info_list then
        return
    end
    local data = list.rank_info_list or {}
    self.rank_list:SetDataList(data)

    local role_info = RoleWGData.Instance:GetRoleInfo()
    local self_info1 = KuafuYeZhanWangChengWGData.Instance:GetSetSelfInfo()
    local self_info2 = KuafuYeZhanWangChengWGData.Instance:GetRoleInfoByUserKey(self_info1.user_key)

    --内网无法请求PHP，先设置默认名字
    local temp_name = string.format(Language.WorldServer.ServerDefName, self_info2.origin_server_id)
    self.node_list["self_guild_name"].text.text = temp_name

    self.node_list["self_rank"].text.text = self_info2.rank
    local name = BiZuoWGData.Instance:GetSetverNameFormat(self_info2.user_name)
    self.node_list["self_name"].text.text = name
    --self.node_list["self_guild_name"].text.text = self_info2.guild_name or empty ----power
    self.node_list["self_kill_num"].text.text = self_info2.kill_role_num
    --self.node_list["self_assist_num"].text.text = self_info2.assist_role_num
    self.node_list["self_assist_num"].text.text = self_info2.capability
    self.node_list["self_dead_num"].text.text = self_info2.die_num
    self.node_list["self_score"].text.text = self_info2.score

    if self_info2.rank and self_info2.rank < 4 then
        self.node_list["self_icon"].image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. self_info2.rank))
        self.node_list["self_icon"]:SetActive(true)
    else
        self.node_list["self_icon"]:SetActive(false)
    end
end

-----------------------------------------------------------------------------------------------------------------
ZCRankRender = ZCRankRender or BaseClass(BaseRender)
function ZCRankRender:OnFlush()
    if not self.data then
        return
    end
    local empty = ""
    self.node_list["rank"].text.text = self.data.rank
    local name = BiZuoWGData.Instance:GetSetverNameFormat(self.data.user_name)
    self.node_list["name"].text.text = name
    self.node_list["kill_num"].text.text = self.data.kill_role_num
    self.node_list["assist_num"].text.text = self.data.capability
    self.node_list["dead_num"].text.text = self.data.die_num
    self.node_list["score"].text.text = self.data.score

    if self.data.rank < 4 then
        self.node_list["icon"].image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. self.index))
        self.node_list["icon"]:SetActive(true)
        self.node_list["rank"].text.text = ""
    else
        self.node_list["icon"]:SetActive(false)
        self.node_list["rank"].text.text = self.data.rank
    end

    --内网无法请求PHP，先设置默认名字
    local temp_name = string.format(Language.WorldServer.ServerDefName, self.data.origin_server_id)
    self.node_list["guild_name"].text.text = temp_name
end
