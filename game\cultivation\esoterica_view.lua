EsotericaView = EsotericaView or BaseClass(SafeBaseView)
function EsotericaView:__init()
	self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true

    self:SetMaskBg(false, true)
    local common_bundle = "uis/view/common_panel_prefab"
    local view_bundle = "uis/view/cultivation_ui_prefab"
    self:AddViewResource(0, common_bundle, "layout_a3_common_panel")
    self:AddViewResource(0, view_bundle, "layout_esoterica_view")
    self:AddViewResource(0, common_bundle, "layout_a3_common_top_panel")
end

function EsotericaView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.Esoterica.TitleName

    local bundle, asset = ResPath.GetRawImagesPNG("a3_xf_bj")
    self.node_list.RawImage_tongyong.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
    end)

    if not self.esoterica_list then
		self.esoterica_list = AsyncFancyAnimView.New(EsotericaItemRender, self.node_list.esoterica_list)
        self.esoterica_list:SetStartZeroIndex(false)
        self.esoterica_list:SetSelectCallBack(BindTool.Bind(self.OnSelectEsotericaList, self))
	end

    XUI.AddClickEventListener(self.node_list.btn_open_detail_view, BindTool.Bind(self.OnClickOpenDetailViewBtn, self))

    self.select_index = 1
end

function EsotericaView:ShowIndexCallBack()
    local data_list = CultivationWGData.Instance:GetEsotericaShowList()
    self.esoterica_list:SetDataList(data_list)
    local jump_index = 0
    for k, v in pairs(data_list) do
        if v.is_remind then
            jump_index = k
            break
        end
    end

    jump_index = jump_index == 0 and 3 or jump_index    --打开界面如果没红点需要滑动2格让玩家知道可以滑动
    self.esoterica_list:SelectCell(jump_index)
end

function EsotericaView:ReleaseCallBack()
    if self.esoterica_list then
		self.esoterica_list:DeleteMe()
		self.esoterica_list = nil
	end
end

function EsotericaView:OnFlush(param_t)
end

function EsotericaView:OnSelectEsotericaList(cell)
    if (not cell) or (not cell.data) then
		return
	end

    local data = cell.data
    self.select_index = cell.index

    self.node_list.btn_open_detail_view_remind:SetActive(data.is_remind)
end

function EsotericaView:OnClickOpenDetailViewBtn()
    CultivationWGCtrl.Instance:SetDataAndOpenEsotericaDetailView(self.select_index)
end

function EsotericaView:FlushEsotericaList()
    local data_list = CultivationWGData.Instance:GetEsotericaShowList()
    if self.esoterica_list then
        self.esoterica_list:SetDataList(data_list)
        self.esoterica_list:SelectCell(self.select_index)
    end
end

---------------------------------------------------------
EsotericaItemRender = EsotericaItemRender or BaseClass(BaseRender)
function EsotericaItemRender:__init()

end

function EsotericaItemRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_open, BindTool.Bind(self.OnClickBtnOpen, self))
end

function EsotericaItemRender:ReleaseCallBack()

end

function EsotericaItemRender:OnFlush()
    if not self.data then return end

    self.node_list.remind:SetActive(self.data.is_remind)
    local bundle, asset = ResPath.GetRawImagesPNG("a3_xf_rwlh" .. self.data.img_id)
    self.node_list.esoterica_select_img.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["esoterica_select_img"].raw_image:SetNativeSize()
    end)

    bundle, asset = ResPath.GetRawImagesPNG("a3_xf_rwlh_unsel" .. self.data.img_id)
    self.node_list.esoterica_unselect_img.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["esoterica_unselect_img"].raw_image:SetNativeSize()
    end)

    bundle, asset = ResPath.GetRawImagesPNG("a3_xf_wb" .. self.data.skill_img)
    self.node_list.skill_img.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["skill_img"].raw_image:SetNativeSize()
    end)

    local pos_x, pos_y = 0, 0
    if self.data.img_pos and self.data.img_pos ~= "" then
		local pos_list = string.split(self.data.img_pos, "|")
        pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
        RectTransform.SetAnchoredPositionXY(self.node_list.esoterica_select_img.rect, pos_x, pos_y)
        RectTransform.SetAnchoredPositionXY(self.node_list.esoterica_unselect_img.rect, pos_x, pos_y)
	end

    local scale = self.data.img_scale
    if scale and scale ~= "" then
        Transform.SetLocalScaleXYZ(self.node_list.esoterica_select_img.transform, scale, scale, scale)
        Transform.SetLocalScaleXYZ(self.node_list.esoterica_unselect_img.transform, scale, scale, scale)
	end

    bundle, asset = ResPath.GetA2Effect(self.data.ui_effect_asset)
    self.node_list.effect:ChangeAsset(bundle, asset)

    if self.data.skill_label and self.data.skill_label ~= "" then
        self.node_list.esoterica_label_bg:SetActive(true)
		local split_list = string.split(self.data.skill_label, "|")
        if split_list[1] then
            self.node_list["esoterica_label"].text.text = split_list[1]
        end
    else
        self.node_list.esoterica_label_bg:SetActive(false)
	end
end

function EsotericaItemRender:OnClickBtnOpen()
    CultivationWGCtrl.Instance:SetDataAndOpenEsotericaDetailView(self.index)
end