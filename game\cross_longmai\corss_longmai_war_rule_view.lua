CrossLongMaiWarRuleView = CrossLongMaiWarRuleView or BaseClass(SafeBaseView)

local TOGGLE_MAX = 4

function CrossLongMaiWarRuleView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", 
                        {vertor2 = Vector2(-6, -3), sizeDelta = Vector2(821, 495)})
	self:AddViewResource(0, "uis/view/longmai_ui_prefab", "layout_longmai_war_rule")
end

function CrossLongMaiWarRuleView:ReleaseCallBack()
	if self.rule_list_view then
		for k,v in pairs(self.rule_list_view) do
			for i,f in pairs(v) do
				f:DeleteMe()
			end
		end
		self.rule_list_view = {}
	end
end

function CrossLongMaiWarRuleView:LoadCallBack()
    self.rule_list_view = {}

    self.node_list.title_view_name.text.text = Language.CrossLongMai.LongMaiWarRuleTitle
    local data_cfg = CrossLongMaiWGData.Instance:GetBattleRuleCfg()
    self.accor_list = {}
    for i = 1, TOGGLE_MAX do
        local btn_obj = self.node_list["LongMai_SelectBtn" .. i]
        local data = data_cfg[i]
        if data then
            self.node_list["LongMai_SelectBtn" .. i]:SetActive(true)
            if btn_obj then
                self.accor_list[i] = {}
                self.accor_list[i].text_name = btn_obj:FindObj("nor/BtnText" .. i .. "_nor")
		        self.accor_list[i].high_text_name = btn_obj:FindObj("hl/text_high_btn" .. i)
                self.accor_list[i].btn = self.node_list["LongMai_SelectBtn" .. i]
                self.accor_list[i].select_btn = self.node_list["LongMai_SelectBtn" .. i].gameObject:GetComponent(typeof(UnityEngine.UI.Toggle))
                self.accor_list[i].select_btn:AddValueChangedListener(BindTool.Bind(self.AccorBtnClickCallback, self, i))
                self.accor_list[i].text_name.text.text = data_cfg[i][1].title
		    	self.accor_list[i].high_text_name.text.text = data_cfg[i][1].title
            end
            
            if #data > 1 and self.accor_list[i] then
				self.accor_list[i].list = self.node_list["LongMai_List" .. i]
				self.accor_list[i].list_item_cell = {}
				self:LoadCell(i, data_cfg[i])
			end
        else
			self.node_list["LongMai_SelectBtn" .. i]:SetActive(false)
        end
    end
end

function CrossLongMaiWarRuleView:LoadCell(index, data) -- 加载左边list中的 cell
    self.rule_list_view[index] = {}
    local prefab_obj = self.node_list["item_type_prefab"].gameObject
    for i = 1, #data do
		local obj = ResMgr:Instantiate(prefab_obj)
		local obj_transform = obj.transform
		if self.accor_list[index].list then
			obj_transform:SetParent(self.accor_list[index].list.transform, false)
			obj:GetComponent("Toggle").group = self.accor_list[index].list.toggle_group
			self.accor_list[index].list_item_cell[i] = obj
		end
		local item_cell = LongMaiWarRuleItem.New(obj)
		item_cell:SetClickCallBack(BindTool.Bind(self.ClickRuleItem,self))
		item_cell:SetData(data[i])
		self.rule_list_view[index][i] = item_cell
		self.accor_list[1].select_btn.isOn = true
	end
end

function CrossLongMaiWarRuleView:AccorBtnClickCallback(index)
    local cfg = CrossLongMaiWGData.Instance:GetRuleByIndexCfg(index)
    if cfg and #cfg > 1 then
		local obj = self.accor_list[index].list_item_cell[1]
		if obj ~= nil then
			self:HideAllItemType(index)
			obj:GetComponent("Toggle").isOn = true
		end
	else
		self:FlushContentInfo(cfg[1])
	end
end

function CrossLongMaiWarRuleView:ClickRuleItem(data)
	self:FlushContentInfo(data)
end

function CrossLongMaiWarRuleView:HideAllItemType(index)
	if self.rule_list_view[index] then
		for k,v in pairs(self.rule_list_view[index]) do
			v:SetToggleIsOn(false)
		end
	end
end


function CrossLongMaiWarRuleView:FlushContentInfo(data)
	self.node_list["longmai_rule_text"].text.text = data.content
	self.node_list["longmai_title"].text.text = data.child_title
	self.node_list["longmai_img"]:SetActive(data.img_id > 0)
	self.node_list["bg"]:SetActive(data.img_id > 0)

	if data.img_id > 0 then
		self.node_list["longmai_img"].raw_image:LoadSprite(ResPath.GetRawImagesPNG("a1_longmai_rt_" .. data.img_id))
		RectTransform.SetAnchoredPositionXY(self.node_list["desc_buttom"].rect, 0, -129)
	else
		RectTransform.SetAnchoredPositionXY(self.node_list["desc_buttom"].rect, 0, 138)
	end

end


LongMaiWarRuleItem = LongMaiWarRuleItem or BaseClass(BaseRender)

function LongMaiWarRuleItem:__init()
end

function LongMaiWarRuleItem:LoadCallBack()
	self.ningju_name = self.node_list["Name"]
	self.view.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickItem, self))
end

function LongMaiWarRuleItem:SetToggleIsOn(value)
	self.view.toggle.isOn = value
end

function LongMaiWarRuleItem:OnClickItem(is_on)
	if is_on then
		if self.click_callback then
			self.click_callback(self.data)
		end
	end
end

function LongMaiWarRuleItem:OnFlush()
	if self.data == nil then return end
	self.node_list["Name"].text.text = self.data.child_title
	self.node_list["hl_name"].text.text = self.data.child_title
end