-- 天神3V3
TianShen3v3View = TianShen3v3View or BaseClass(SafeBaseView)
JOIN_REWARD_SHOW_AMOUNT = 4 -- 一页展示的参与奖励的个数
function TianShen3v3View:__init()
	self.view_style = ViewStyle.Full
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)

	self:AddViewResource(0, "uis/view/tianshen_3v3_ui_prefab", "layout_tianshen_3v3")
end

function TianShen3v3View:__delete()

end

function TianShen3v3View:OpenCallBack()
	TianShenWGCtrl.Instance:SendRequestCompleteRate()
end

function TianShen3v3View:CloseCallBack()
end

function TianShen3v3View:SetData()
end

function TianShen3v3View:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["rule_btn"], BindTool.Bind(self.OnClickRule, self))           -- 规则按钮
	XUI.AddClickEventListener(self.node_list["reward_btn"], BindTool.Bind(self.OnClickReward, self))       -- 奖励预览按钮
	XUI.AddClickEventListener(self.node_list["season_rank_btn"], BindTool.Bind(self.OnClickSeasonRank, self)) -- 赛季排名按钮
	XUI.AddClickEventListener(self.node_list["select_btn"], BindTool.Bind(self.OnClickSelectTianShen, self)) -- 选择按钮
	XUI.AddClickEventListener(self.node_list["enter_btn"], BindTool.Bind(self.OnClickEnter, self))         -- 进入按钮
	XUI.AddClickEventListener(self.node_list["complete_rate_rule_btn"], BindTool.Bind(self.OnRateRule, self)) -- 养成度规则按钮
	self.tianshen_active_audio_play_t = {}

	-- 当前选中的天神index
	self.select_tianshen_index = TianShen3v3WGData.Instance:GetSelectTianShenIndex()

	-- 模型
	if not self.role_model then
		self.role_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["acti_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}
		
		self.role_model:SetRenderTexUI3DModel(display_data)
		-- self.role_model:SetUI3DModel(self.node_list["acti_display"].transform,
		-- 	self.node_list["block"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.role_model)
	end

	-- 天神头像
	self.node_list["head_prefab"]:SetActive(false)
	self.head_list = nil

	-- 技能列表
	self.skill_list = AsyncListView.New(TianShen3v3SkillItem, self.node_list["skill_list"])

	-- 参与奖励宝箱
	self.join_reward_list = {}
	for i = 1, JOIN_REWARD_SHOW_AMOUNT do
		self.join_reward_list[i] = TianShen3v3JoinRewardItem.New(self.node_list["join_reward_" .. i])
	end
	self.original_pos_x = self.node_list["join_reward_panel"].rect.anchoredPosition.x

	-- 奖励列表
	self.display_reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])

	self.remind_callback = BindTool.Bind(self.OnRemindChange, self)
	RemindManager.Instance:Bind(self.remind_callback, RemindName.TianShen3v3BtnRemind)
end

function TianShen3v3View:ReleaseCallBack()
	if self.head_list then
		for i, v in ipairs(self.head_list) do
			v:DeleteMe()
		end
	end
	self.head_list = nil

	for i, v in ipairs(self.join_reward_list) do
		v:DeleteMe()
	end
	self.join_reward_list = nil

	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	if self.display_reward_list then
		self.display_reward_list:DeleteMe()
		self.display_reward_list = nil
	end

	if self.skill_list then
		self.skill_list:DeleteMe()
		self.skill_list = nil
	end

	RemindManager.Instance:UnBind(self.remind_callback)

	self.last_show_tianshen_id = nil
	self.original_pos_x = nil
end

function TianShen3v3View:OnFlush(param_t)
	for k, v in pairs(param_t) do
		if k == "all" then
			self:FlushAllView()
		elseif k == "join_reward_anim" then
			local cfg_list = TianShen3v3WGData.Instance:GetJoinRewardCfg()
			if (v.seq + 1) % JOIN_REWARD_SHOW_AMOUNT == 0 and v.seq ~= cfg_list[#cfg_list].seq then
				self:FlushJoinReward(true)
			end
		end
	end
end

function TianShen3v3View:FlushAllView()
	self:FlushTianShenHeadList()
	self:FlushMiddlePanel()
	self:FlushRightPanel()
end

-- 刷新所有天神头像
function TianShen3v3View:FlushTianShenHeadList()
	local tianshen_cfg = TianShenWGData.Instance:GetTianShenImageCfg()
	-- 排序，激活的排前面
	local cfg_list = {}
	local active_count = 0
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local level = main_role_vo.level
	for i = 0, #tianshen_cfg do
		local image_cfg = tianshen_cfg[i]
		if image_cfg.show_level <= level then
			local is_activate = TianShenWGData.Instance:IsActivation(image_cfg.index)
			local tianshen_act_cfg = TianShenWGData.Instance:GetTianshenItemCfgByIndex(image_cfg.index)
			local item_id = tianshen_act_cfg.act_item_id
			local _, color = ItemWGData.Instance:GetItemColor(item_id)
			local cap = TianShen3v3WGData.Instance:GetCapability(image_cfg.index)
			local data = {}
			data.cfg = image_cfg
			data.sort_key = 0
			data.sort_key = data.sort_key +
			(TianShen3v3WGData.Instance:GetSelectTianShenIndex() == image_cfg.index and 10 ^ 14 or 0)
			data.sort_key = data.sort_key + (is_activate and 10 ^ 13 or 0)
			data.sort_key = data.sort_key + cap * 10 ^ -2
			data.sort_key = data.sort_key + -color
			table.insert(cfg_list, data)
		end
	end
	table.sort(cfg_list, SortTools.KeyUpperSorter("sort_key"))

	if self.head_list == nil then
		self.head_list = {}
		for i, v in ipairs(cfg_list) do
			local image_cfg = v.cfg
			if self.head_list[i] == nil then
				local prefab = self.node_list["head_prefab"].gameObject
				local go = ResMgr:Instantiate(prefab)
				go.transform:SetParent(self.node_list["head_group"].transform, false)
				go.transform.localPosition = Vector3.zero
				go:SetActive(true)
				self.head_list[i] = TianShen3v3Head.New(go)
				self.head_list[i]:SetIndex(image_cfg.index)
				self.head_list[i]:SetSelectIndexCallback(BindTool.Bind(self.GetCurSelectTianShenIndex, self))
				self.head_list[i]:SetClickCallBack(BindTool.Bind(self.SetSelectTianShenIndex, self))
				self.head_list[i]:SetData(image_cfg)
			end
		end
	else
		for i, v in ipairs(self.head_list) do
			self.head_list[i]:SetData(cfg_list[i].cfg)
		end
	end
end

-- 刷新中间面板
function TianShen3v3View:FlushMiddlePanel()
	local is_activate = TianShenWGData.Instance:IsActivation(self:GetCurSelectTianShenIndex())
	local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(self:GetCurSelectTianShenIndex())
	if not tianshen_cfg then
		print_error("没有找到天神配置请检查，tianshen_index:", self:GetCurSelectTianShenIndex(),
			TianShen3v3WGData.Instance:GetSelectTianShenIndex())
		return
	end
	self.node_list["cap_panel"]:SetActive(is_activate)
	self.node_list["complete_rate_panel"]:SetActive(is_activate)

	-- 战力
	self.node_list["cap_value"].text.text = TianShen3v3WGData.Instance:GetCapability(self:GetCurSelectTianShenIndex())

	-- 天神描述
	self.node_list["tianshen_desc"].text.text = tianshen_cfg.tianshen_desc

	-- 养成度
	local complete_rate = TianShenWGData.Instance:GetCompleteRate(self:GetCurSelectTianShenIndex())
	self.node_list["complete_rate_slider"].slider.value = complete_rate / 100
	self.node_list["rate_slider_text"].text.text = complete_rate .. " / 100 "

	local dingwei_text = TianShenWGData.Instance:GetTianShenDingWeiText(self:GetCurSelectTianShenIndex())
	if dingwei_text and dingwei_text ~= "" then
		self.node_list["dingwei_text"].text.text = dingwei_text
		self.node_list["dingwei_bg"]:SetActive(true)
	else
		self.node_list["dingwei_bg"]:SetActive(false)
	end

	self:FlushTianShenModel()
	self:FlushGetWay()
	self:FlushSkillList()
end

function TianShen3v3View:FlushRightPanel()
	-- 剩余可获得积分的场次
	self.node_list["can_get_score_times"].text.text = string.format(Language.TianShen3v3.CanFetchDesc,
		TianShen3v3WGData.Instance:GetSurplusCanGetScoreTimes())
	-- 赛季总积分
	self.node_list["season_score"].text.text = string.format(Language.TianShen3v3.SeasonScoreStr,
		TianShen3v3WGData.Instance:GetSeasonScore())
	-- 进阶所需积分
	self.node_list["advance_need_score"].text.text = string.format(Language.TianShen3v3.AdvanceNeedScore,
		TianShen3v3WGData.Instance:GetAdvanceNeedScore())
	-- 胜场
	self.node_list["win_times"].text.text = string.format(Language.TianShen3v3.WinTimesStr,
		TianShen3v3WGData.Instance:GetWinTimes())

	local grade_cfg, next_grade_cfg = TianShen3v3WGData.Instance:GetGradeCfgByScore(TianShen3v3WGData.Instance
	:GetSeasonScore())
	if grade_cfg then
		-- 段位名称
		self.node_list["stage_name"].text.text = grade_cfg.grade_name
		ChangeToQualityText(self.node_list["stage_name"].text, RankGradeEnum[grade_cfg.grade])
		-- 星数
		for i = 1, 5 do
			self.node_list["star_" .. i]:SetActive(i <= grade_cfg.star)
		end
		-- 进度条
		local max_score = next_grade_cfg.score - grade_cfg.score
		local cur = TianShen3v3WGData.Instance:GetSeasonScore() - grade_cfg.score
		self.node_list["stage_progress"].image.fillAmount = cur / max_score
	else
		print_error("没取到段位配置，请检查！， score:", TianShen3v3WGData.Instance:GetSeasonScore())
	end
	-- 段位图标
	self.node_list["stage_icon"].image:LoadSpriteAsync(ResPath.GetTianShen3v3Img("duanwei_icon" .. grade_cfg.grade))
	-- 赛季时间
	self.node_list["season_time"].text.text = string.format(Language.TianShen3v3.SeasonTime,
		TianShen3v3WGData.Instance:GetSeasonTimeStr())
	-- 按钮红点
	self.node_list["red_point"]:SetActive(RemindManager.Instance:GetRemind(RemindName.TianShen3v3BtnRemind) > 0)

	self:FlushDisplayReward()
	self:FlushJoinReward()
	self:FlushBtn()
end

-- 刷新展示用奖励
function TianShen3v3View:FlushDisplayReward()
	local reward_array = {}
	local display_reward_cfg = TianShen3v3WGData.Instance:GetOtherCfg().display_reward
	for i = 0, #display_reward_cfg do
		table.insert(reward_array, display_reward_cfg[i])
	end
	self.display_reward_list:SetDataList(reward_array)
end

-- 刷新参与奖励
function TianShen3v3View:FlushJoinReward(show_anim)
	if not self.node_list or not self.node_list["join_slider"] then
		return
	end
	-- 是否需要播动画
	if show_anim then
		local move_left_tweener = self.node_list["join_reward_panel"].rect:DOAnchorPosX(self.original_pos_x - 350, 0.3)
		move_left_tweener:OnComplete(function()
			if not self.node_list or not self.node_list["join_reward_panel"] or not self.original_pos_x then
				return
			end
			self:FlushJoinReward()
			local pos = self.node_list["join_reward_panel"].rect.anchoredPosition
			self.node_list["join_reward_panel"].rect.anchoredPosition = Vector2(self.original_pos_x + 350, pos.y)
			self.node_list["join_reward_panel"].rect:DOAnchorPosX(self.original_pos_x, 0.3)
		end)
	else
		local join_reward_cfg = TianShen3v3WGData.Instance:GetJoinRewardCfg()
		local first_unreceived_seq = TianShen3v3WGData.Instance:GetFirstUnreceivedJoinRewardSeq() -- 第一个还未领取的参与奖励seq
		if first_unreceived_seq < 0 then
			first_unreceived_seq = #join_reward_cfg + 1 - JOIN_REWARD_SHOW_AMOUNT
		end

		local cfg_list = {}
		local cfg_start_seq = math.floor(first_unreceived_seq / JOIN_REWARD_SHOW_AMOUNT) * JOIN_REWARD_SHOW_AMOUNT
		local cfg_max_start_seq = #join_reward_cfg + 1 - JOIN_REWARD_SHOW_AMOUNT
		local cfg_start_seq = math.min(cfg_start_seq, cfg_max_start_seq)

		for seq = cfg_start_seq, cfg_start_seq + JOIN_REWARD_SHOW_AMOUNT - 1 do
			table.insert(cfg_list, join_reward_cfg[seq])
		end
		local cur_seq = TianShen3v3WGData.Instance:GetJoinRewardCfgSeqByTimes(TianShen3v3WGData.Instance:GetJoinTimes())
		self.node_list["join_slider"].slider.value = (cur_seq - (cfg_list[1].seq)) /
		(cfg_list[#cfg_list].seq - cfg_list[1].seq)

		for i, v in ipairs(self.join_reward_list) do
			if v:GetView() then
				if cfg_list[i] then
					v:SetData(cfg_list[i])
					v:GetView():SetActive(true)
				else
					v:GetView():SetActive(false)
				end
			end
		end
	end
end

function TianShen3v3View:FlushBtn()
	local is_activate = TianShenWGData.Instance:IsActivation(self:GetCurSelectTianShenIndex())
	XUI.SetGraphicGrey(self.node_list["select_btn"], not is_activate)
end

-- 刷新获取途径
function TianShen3v3View:FlushGetWay()
	local is_activate = TianShenWGData.Instance:IsActivation(self:GetCurSelectTianShenIndex())
	local get_way_data_list = TianShenWGData.Instance:GetTianShenGetWayList(self:GetCurSelectTianShenIndex())
	local show_get_way_panel = not is_activate and not IsEmptyTable(get_way_data_list)
	self.node_list["get_way_panel"]:SetActive(show_get_way_panel)
	if show_get_way_panel then
		for i = 1, 3 do
			self.node_list["way_point_" .. i]:SetActive(false)
			self.node_list["way_" .. i]:SetActive(false)
			local get_way_data = get_way_data_list[i]
			if get_way_data then
				local has_jump_way = get_way_data.jump_way and get_way_data.jump_way ~= ""
				self.node_list["way_" .. i]:SetActive(true)
				self.node_list["way_" .. i].text.text = get_way_data.name
				self.node_list["way_" .. i].button:AddClickListener(BindTool.Bind(self.OnClickTianShenGetWay, self,
					get_way_data))
				self.node_list["way_" .. i].button.interactable = has_jump_way
				self.node_list["way_line_" .. i]:SetActive(has_jump_way)
				if self.node_list["way_point_" .. i - 1] then
					self.node_list["way_point_" .. i - 1]:SetActive(true)
				end
			end
		end
	end
end

function TianShen3v3View:FlushSkillList()
	local zhu_skill = TianShenWGData.Instance:GetTianShenZhuSkill(self.select_tianshen_index)
	local skill_data_list = {}
	for i, skill_id in ipairs(zhu_skill) do
		table.insert(skill_data_list, { skill_id = tonumber(skill_id), tianshen_index = self.select_tianshen_index })
	end
	self.skill_list:SetDataList(skill_data_list)
end

-- 刷新模型展示
function TianShen3v3View:FlushTianShenModel()
	local cfg = self:GetSelectTianShenCfg()
	if cfg then
		local audio = self.tianshen_active_audio_play_t[cfg.index] == nil and cfg.show_audio or nil
		self.tianshen_active_audio_play_t[cfg.index] = 1
		if self.last_show_tianshen_id and cfg.appe_image_id == self.last_show_tianshen_id then
			return
		end
		self.last_show_tianshen_id = cfg.appe_image_id
		self.role_model:SetTianShenModel(cfg.appe_image_id, cfg.index, true, audio, SceneObjAnimator.Rest)
	end
end

function TianShen3v3View:SetSelectTianShenIndex(cell)
	local index = cell.data.index
	self.select_tianshen_index = index
	self:FlushAllView()
end

-- 获取当前选中的天神index
function TianShen3v3View:GetCurSelectTianShenIndex()
	return self.select_tianshen_index
end

-- 获取当前选中天神的配置
function TianShen3v3View:GetSelectTianShenCfg()
	return TianShenWGData.Instance:GetTianShenCfg(self:GetCurSelectTianShenIndex())
end

-- 点击天神获取途径
function TianShen3v3View:OnClickTianShenGetWay(get_way_data)
	if get_way_data.is_open then
		FunOpen.Instance:OpenViewNameByCfg(get_way_data.jump_way)
	else
		if get_way_data.special_tip then
			TipWGCtrl.Instance:ShowSystemMsg(get_way_data.special_tip)
		else
			TipWGCtrl.Instance:ShowSystemMsg(Language.TianShen.TianShenGetJumpTip[1])
		end
	end
end

-- 点击规则按钮
function TianShen3v3View:OnClickRule()
	ViewManager.Instance:Open(GuideModuleName.TianShen3v3RuleView)
end

-- 点击奖励预览
function TianShen3v3View:OnClickReward()
	ViewManager.Instance:Open(GuideModuleName.TianShen3v3RewardView)
end

-- 点击赛季奖励按钮
function TianShen3v3View:OnClickSeasonRank()
	ViewManager.Instance:Open(GuideModuleName.TianShen3v3SeasonRankView)
end

-- 点击确认选择
function TianShen3v3View:OnClickSelectTianShen()
	local is_activate = TianShenWGData.Instance:IsActivation(self:GetCurSelectTianShenIndex())
	if not is_activate then
		TipWGCtrl.Instance:ShowSystemMsg(Language.TianShen3v3.Unactive)
		return
	end
	if self:GetCurSelectTianShenIndex() == TianShen3v3WGData.Instance:GetSelectTianShenIndex() then
		TipWGCtrl.Instance:ShowSystemMsg(Language.TianShen3v3.IsChuzhan)
		return
	end
	TianShen3v3WGCtrl.Instance:SendSelectTianShen(self.select_tianshen_index)
	if Scene.Instance:GetSceneType() == SceneType.TianShen3v3Prepare then
		self:Close()
	end
end

-- 点击进入场景
function TianShen3v3View:OnClickEnter()
	if Scene.Instance:GetSceneType() == SceneType.TianShen3v3Prepare then
		self:Close()
	else
		CrossServerWGCtrl.Instance.SendCrossStartReq(ACTIVITY_TYPE.TIANSHEN_3V3)
	end
end

-- 点击养成度规则按钮
function TianShen3v3View:OnRateRule()
	RuleTip.Instance:SetContent(TianShen3v3WGData.Instance:GetOtherCfg().complete_rate_rule,
		Language.TianShen3v3.CompleteRateRuleTitle)
end

-- 红点回调
function TianShen3v3View:OnRemindChange(remind_name, num)
	self.node_list["red_point"]:SetActive(num > 0)
end

--------------------单个天神头像----------------------------
TianShen3v3Head = TianShen3v3Head or BaseClass(BaseRender)
function TianShen3v3Head:__init()
	self.last_head_id = nil
end

function TianShen3v3Head:__delete()

end

function TianShen3v3Head:OnFlush()
	if self.data and self.select_index_callback then
		local cur_select_tianshen_index = self.select_index_callback()
		self.node_list["hl"]:SetActive(cur_select_tianshen_index == self.data.index) -- 选中高亮

		local tianshen_act_cfg = TianShenWGData.Instance:GetTianshenItemCfgByIndex(self.data.index)
		local item_id = tianshen_act_cfg.act_item_id
		local _, color = ItemWGData.Instance:GetItemColor(item_id)
		self.node_list["circle_bg"].image:LoadSpriteAsync(ResPath.GetTianShen3v3Img("ts_bg_" .. color))

		-- 天神头像
		if self.last_head_id ~= self.data.head_id then
			local bundle, asset = ResPath.GetItem(self.data.head_id)
			self.node_list["head_icon"].image:LoadSpriteAsync(bundle, asset, function()
				self.node_list["head_icon"]:SetActive(true)
				self.node_list["head_icon"].image:SetNativeSize()
			end)
			self.last_head_id = self.data.head_id
		end
		local is_activate = TianShenWGData.Instance:IsActivation(self.data.index)
		XUI.SetGraphicGrey(self.node_list["head_icon"], not is_activate)

		-- 五行图标
		local wuxing_index = TianShenWGData.Instance:GetTianShenWuXingByIndex(self.data.index)
		if wuxing_index ~= 0 then
			local bundle, asset = ResPath.GetF2CommonImages("wuxing_small_" .. wuxing_index)
			self.node_list["wuxing_icon"].image:LoadSprite(bundle, asset)
			self.node_list["wuxing_icon"]:SetActive(true)
			self.node_list["wuxing_icon"].image:SetNativeSize()
		else
			self.node_list["wuxing_icon"]:SetActive(false)
		end

		-- 是否出战中
		self.node_list["fight_label"]:SetActive(TianShen3v3WGData.Instance:GetSelectTianShenIndex() == self.data.index)
	end
end

function TianShen3v3Head:SetSelectIndexCallback(select_index_callback)
	self.select_index_callback = select_index_callback
end

--------------------单个天神技能---------------------------
TianShen3v3SkillItem = TianShen3v3SkillItem or BaseClass(BaseRender)
function TianShen3v3SkillItem:__init()
	XUI.AddClickEventListener(self.node_list["Img"], BindTool.Bind(self.OnClick, self))
	XUI.AddClickEventListener(self.node_list.skill_lock, BindTool.Bind(self.OnClick, self))
end

function TianShen3v3SkillItem:__delete()
end

function TianShen3v3SkillItem:OnFlush()
	if self.data then
		local skill_id = self.data.skill_id                                       -- 技能id
		local tianshen_index = self.data.tianshen_index                           -- 天神下标
		local tianshen_is_active = TianShenWGData.Instance:IsActivation(tianshen_index) -- 天神是否激活
		local tianshen_shenshi_data = TianShenWGData.Instance:GetShenShiEquipInfo(tianshen_index)
		local shenshi_rank = tianshen_shenshi_data.jingshen
		local skill_level = TianShenWGData.Instance:GetTianShenSkillLv(tianshen_index, shenshi_rank, skill_id) or
		1                                                                                                     -- 技能等级
		local skill_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, skill_level)                    -- 技能配置
		local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(tianshen_index)                           -- 天神配置
		local is_jiban_skill = skill_id == tianshen_cfg.jiban_skill
		local jiban_tishen_active = false
		self.node_list["skill_lock"]:SetActive(not tianshen_is_active)
		if is_jiban_skill then
			local jiban_tianshen_index = TianShenWGData.Instance:GetTianShenJiBanSkillAct(tianshen_index, shenshi_rank,
				skill_id)
			jiban_tishen_active = jiban_tianshen_index and TianShenWGData.Instance:IsActivation(jiban_tianshen_index) or
			false
			self.node_list["skill_lock"]:SetActive(not jiban_tishen_active or not tianshen_is_active)
		end
		if skill_cfg then
			local bundel, asset = ResPath.GetSkillIconById(skill_cfg.icon_resource)
			self.node_list["skill_icon"].image:LoadSprite(bundel, asset, function() -- 技能图标
				self.node_list["skill_icon"].rect.sizeDelta = Vector2(COMMON_CONSTS.ItemCellSize,
					COMMON_CONSTS.ItemCellSize)
			end)
		end

		local ts_skill_cfg = SkillWGData.Instance:GetTianShenSkillCfg(skill_id, skill_level)
		local skill_name = ToColorStr(ts_skill_cfg.skill_name, TIANSHEN_DARK_COLOR3B[tianshen_cfg.series])
		self.node_list["skill_name"].text.text = skill_name      -- 技能名称
		self.node_list["skill_level"].text.text = "Lv." .. skill_level -- 技能等级


		local skill_type_tab = SkillWGData.Instance:GetSkillAttackType(skill_id)
		self.node_list["skill_type"].image:LoadSprite(ResPath.GetF2CommonImages("n_skill_type" .. skill_type_tab[1])) -- 技能类型

		self.tips_data = {
			icon = skill_cfg.icon_resource,
			top_text = skill_name,
			tianshen_index = self.data.tianshen_index,
			skill_id = self.data.skill_id,
			x = 0,
			y = 0,
			set_pos2 = true,
			capability = ts_skill_cfg.capability_inc,
			skill_box_type = SKILL_BOX_TYPE.TIANSHEN_SKILL
		}
	end
end

function TianShen3v3SkillItem:OnClick()
	if not self.tips_data then
		return
	end
	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(self.tips_data)
end

---------------------------------- 参与奖励宝箱 ---------------------------------------
TianShen3v3JoinRewardItem = TianShen3v3JoinRewardItem or BaseClass(BaseRender)
function TianShen3v3JoinRewardItem:__init()
	XUI.AddClickEventListener(self.node_list["btn_click"], BindTool.Bind(self.OnClick, self))
end

function TianShen3v3JoinRewardItem:__delete()

end

function TianShen3v3JoinRewardItem:OnFlush()
	local join_cfg = self.data
	if join_cfg then
		local can_fetch = TianShen3v3WGData.Instance:GetJoinRewarCanFetch(join_cfg.seq)
		self.node_list["effect"]:SetActive(can_fetch)
		self.node_list["red_point"]:SetActive(can_fetch)
		self.node_list["oversort"].animator:SetBool("is_shake", can_fetch)
		local fetched = TianShen3v3WGData.Instance:GetJoinRewardFetched(join_cfg.seq)
		self.node_list["yilingqu"]:SetActive(fetched)
		self.node_list["count"].text.text = string.format(Language.TianShen3v3.RewardJoinTimes, join_cfg.match_times)
		XUI.SetGraphicGrey(self.node_list["btn_click"], fetched)
		XUI.SetGraphicGrey(self.node_list["kuang"], fetched)
	end
end

function TianShen3v3JoinRewardItem:OnClick()
	if TianShen3v3WGData.Instance:GetJoinRewarCanFetch(self.data.seq) then
		TianShen3v3WGCtrl.Instance:SendFetchJoinReward(self.data.seq)
	else
		TipWGCtrl.Instance:OpenItem({ item_id = self.data.reward_gift }, ItemTip.FROM_NORMAL)
	end
end
