FightSoulStuffSelectView = FightSoulStuffSelectView or BaseClass(SafeBaseView)
function FightSoulStuffSelectView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	self:LoadConfig()
end

-- 加载配置
function FightSoulStuffSelectView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(886, 554)})
	self:AddViewResource(0, "uis/view/fight_soul_ui_prefab", "layout_stuff_select_view")
end

function FightSoulStuffSelectView:__delete()

end

function FightSoulStuffSelectView:ReleaseCallBack()
	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view = nil
	end

    self.stuff_type = nil
    self.select_data = nil
    self.meet_list = nil
	self.to_top_falg = nil
end

function FightSoulStuffSelectView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.FightSoul.SelectStuffTitle

    self.list_view = AsyncListView.New(FightSoulStuffItem, self.node_list["list_view"])
    self.list_view:SetSelectCallBack(BindTool.Bind1(self.OnSelectItemCB, self))

    XUI.AddClickEventListener(self.node_list.btn_sure, BindTool.Bind(self.Close, self))
    XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind(self.Close, self))
end

function FightSoulStuffSelectView:SetDataAndOpen(stuff_type, select_data, meet_all_list)
    self.stuff_type = stuff_type
    self.select_data = select_data
    self.meet_list = meet_all_list[stuff_type]
	self.to_top_falg = true
    self:Open()
end

function FightSoulStuffSelectView:OnFlush()
    if IsEmptyTable(self.meet_list) then
        self:ChangeShowContent(false)
        return
    end

    local need_num, need_star, need_color = self:GetCurNeedCondition()
    if need_num <= 0 then
        self:ChangeShowContent(false)
        return
    end

    self:ChangeShowContent(true)
    if nil ~= self.list_view then
		if not IsEmptyTable(self.meet_list) then
			table.sort(self.meet_list, SortTools.KeyLowerSorter("stuff_sort_index"))
		end

        self.list_view:SetDataList(self.meet_list)
		if self.to_top_falg then
			self.list_view:JumpToTop()
			self.to_top_falg = false
		end
    end

    local cur_num = self:GetCurSelectNum()
    local quality_str = Language.Common.ColorName[need_color] or ""
    quality_str = ToColorStr(quality_str, ITEM_COLOR[need_color] or COLOR3B.DEFAULT)
    local color_str = cur_num >= need_num and COLOR3B.DEFAULT_NUM or COLOR3B.RED
	self.node_list.remind_tips.text.text = Language.FightSoul.StuffSelectDesc1
    self.node_list.need_text.text.text = string.format(Language.FightSoul.StuffNumDesc2,
                                            need_num, need_star, quality_str, color_str, cur_num, need_num)
end

function FightSoulStuffSelectView:GetCurNeedCondition()
    local need_num, need_star, need_color = 0, 0, 0
    if self.select_data == nil then
        return need_num, need_star, need_color
    end

    local compose_cfg = FightSoulWGData.Instance:GetComposeCfg(self.select_data.color, self.select_data.star)
    if IsEmptyTable(compose_cfg) then
        return need_num, need_star, need_color
    end

    if self.stuff_type == FIGHT_SOUL_STUFF_TYPE.SAME_TYPE then
        need_num = compose_cfg.same_card_num
        need_star = compose_cfg.same_card_show_color
        need_color = compose_cfg.same_card_show_star
    elseif self.stuff_type == FIGHT_SOUL_STUFF_TYPE.NOSAME_TYPE_1 then
        need_num = compose_cfg.same_type_num
        need_star = compose_cfg.same_type_show_star
        need_color = compose_cfg.same_type_show_color
    end

    return need_num, need_star, need_color
end

function FightSoulStuffSelectView:GetCurSelectNum()
    local cur_num = 0
    if IsEmptyTable(self.meet_list) then
        return cur_num
    end

    for k,v in pairs(self.meet_list) do
        if v.select_state[self.stuff_type] then
            cur_num = cur_num + 1
        end
    end

    return cur_num
end

function FightSoulStuffSelectView:ChangeShowContent(had_data)
    self.node_list.no_data:SetActive(not had_data)
    self.node_list.had_data:SetActive(had_data)
end

-- 选择列表项回调
function FightSoulStuffSelectView:OnSelectItemCB(item, cell_index, is_default, is_click)
	if not is_click then
		return
	end

	if nil == item or nil == item.data then
		return
	end

    local need_num = self:GetCurNeedCondition()
    local had_num = self:GetCurSelectNum()
    local bag_index = item.data.item_data.index
    if item.data.select_state[self.stuff_type] then
        FightSoulWGData.Instance:RemoveComposeSelectCache(bag_index)
        self:ChangeMeetListData(bag_index, false)
    else
        if had_num >= need_num then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.FightSoul.SelectLimit2)
            return
        else
            FightSoulWGData.Instance:AddComposeSelectCache(bag_index)
            self:ChangeMeetListData(bag_index, true)
        end
    end

    self:Flush()
    FightSoulWGCtrl.Instance:FlushComposeBagCheckState()
end

function FightSoulStuffSelectView:ChangeMeetListData(bag_index, is_select)
    if IsEmptyTable(self.meet_list) then
        return
    end

    for k,v in pairs(self.meet_list) do
        if bag_index == v.item_data.index then
            self.meet_list[k].select_state[self.stuff_type] = is_select
        end
    end
end

-----------------------------------------------------------------------------
