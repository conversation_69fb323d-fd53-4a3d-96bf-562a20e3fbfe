﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class ActorBlinkerWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(ActorBlinker), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>RegFunction("Blink", Blink);
		L<PERSON>RegFunction("__eq", op_Equality);
		L<PERSON>unction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("FadeIn", get_FadeIn, null);
		<PERSON>.<PERSON>ar("FadeHold", get_FadeHold, null);
		<PERSON>.<PERSON>ar("FadeOut", get_FadeOut, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Blink(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			ActorBlinker obj = (ActorBlinker)ToLua.CheckObject(L, 1, typeof(ActorBlinker));
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
			obj.Blink(arg0, arg1, arg2);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_FadeIn(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ActorBlinker obj = (ActorBlinker)o;
			float ret = obj.FadeIn;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index FadeIn on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_FadeHold(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ActorBlinker obj = (ActorBlinker)o;
			float ret = obj.FadeHold;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index FadeHold on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_FadeOut(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ActorBlinker obj = (ActorBlinker)o;
			float ret = obj.FadeOut;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index FadeOut on a nil value");
		}
	}
}

