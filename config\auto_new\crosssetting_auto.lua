-- K-跨服通用.xls
local item_table={
[1]={item_id=50425,num=1,is_bind=1},
[2]={item_id=32494,num=1,is_bind=1},
[3]={item_id=26129,num=1,is_bind=1},
[4]={item_id=26348,num=1,is_bind=1},
[5]={item_id=26357,num=1,is_bind=1},
[6]={item_id=26356,num=1,is_bind=1},
[7]={item_id=26355,num=1,is_bind=1},
[8]={item_id=46573,num=1,is_bind=1},
[9]={item_id=30447,num=1,is_bind=1},
[10]={item_id=62001,num=1,is_bind=1},
[11]={item_id=30424,num=1,is_bind=1},
[12]={item_id=62000,num=1,is_bind=1},
[13]={item_id=28665,num=1,is_bind=1},
[14]={item_id=56316,num=1,is_bind=1},
[15]={item_id=26200,num=1,is_bind=1},
[16]={item_id=26203,num=1,is_bind=1},
[17]={item_id=26415,num=1,is_bind=1},
[18]={item_id=57837,num=1,is_bind=1},
[19]={item_id=57836,num=1,is_bind=1},
[20]={item_id=26130,num=1,is_bind=1},
[21]={item_id=30447,num=7,is_bind=1},
[22]={item_id=65542,num=1,is_bind=1},
[23]={item_id=65535,num=1,is_bind=1},
[24]={item_id=63000,num=1,is_bind=1},
[25]={item_id=26504,num=1,is_bind=1},
[26]={item_id=26519,num=1,is_bind=1},
[27]={item_id=57834,num=1,is_bind=1},
[28]={item_id=57833,num=1,is_bind=1},
[29]={item_id=57832,num=1,is_bind=1},
[30]={item_id=26369,num=1,is_bind=1},
[31]={item_id=26363,num=1,is_bind=1},
[32]={item_id=26380,num=1,is_bind=1},
[33]={item_id=40212,num=1,is_bind=1},
[34]={item_id=40045,num=1,is_bind=1},
[35]={item_id=46572,num=1,is_bind=1},
[36]={item_id=30424,num=5,is_bind=1},
[37]={item_id=22532,num=1,is_bind=1},
[38]={item_id=22733,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
cross_activity={
{activity_icon="act_5",is_activity=0,day_open=3,},
{index=2,activity_name="魔域空间",activity_icon="act_53",activity_type=41,},
{index=3,activity_name="瑶池仙境",activity_icon="act_26",activity_type=3084,},
{index=4,activity_name="永夜幻都",activity_icon="act_22",activity_type=3073,},
{index=5,activity_name="永夜战场",activity_icon="act_34",activity_type=3087,},
{index=6,activity_name="竞技场",activity_icon="act_2",is_activity=0,activity_type=2,is_show=0,},
{index=7,activity_name="诛仙战场",activity_icon="act_35",activity_type=3102,},
{index=8,activity_name="沧海夺锋",activity_icon="act_19",activity_type=3074,},
{index=9,activity_name="逐鹿仙缘",activity_icon="act_50",activity_type=3075,},
{index=10,activity_name="仙盟争霸",activity_icon="act_33",activity_type=3076,},
{index=11,activity_name="夺仙宝图",activity_icon="act_51",open_condition=2,is_activity=0,activity_type=54,},
{index=12,activity_name="夺仙争霸",activity_icon="act_52",open_condition=4,activity_type=3103,},
{index=13,activity_name="国家首领",activity_icon="act_55",open_condition=4,activity_type=3078,is_show=0,},
{index=14,activity_name="龙门镖局",activity_icon="act_66",open_condition=2,activity_type=3106,is_show=0,},
{index=15,activity_name="夜战皇城",activity_icon="act_58",open_condition=8,activity_type=3107,is_show=0,},
{index=16,activity_name="神灵战场",activity_icon="act_67",activity_type=3081,is_show=0,},
{index=17,activity_name="天下第一",activity_icon="act_68",activity_type=3082,},
{index=18,activity_name="异域魂界",open_condition=8,is_activity=0,activity_type=61,is_show=0,},
{index=19,activity_name="仙城龙脉",activity_type=3110,}
},

cross_activity_meta_table_map={
[3]=16,	-- depth:1
[5]=16,	-- depth:1
[2]=1,	-- depth:1
[17]=15,	-- depth:1
},
cross_activity_open_time={
{open_weekday="2,5",},
{cross_activity_type=3074,open_weekday="1,3,5,0",},
{cross_activity_type=3075,min_day=3,},
{cross_activity_type=3076,open_weekday=0,},
{cross_activity_type=3084,},
{cross_activity_type=3085,},
{cross_activity_type=3087,open_weekday="3,6,0",},
{cross_activity_type=3106,open_weekday="2,4,6",},
{cross_activity_type=3078,},
{cross_activity_type=3103,},
{cross_activity_type=3107,},
{cross_activity_type=3081,},
{cross_activity_type=3082,open_weekday="1,3",},
{cross_activity_type=3109,},
{cross_activity_type=3110,},
{cross_activity_type=3102,open_weekday="1,4",},
{cross_activity_type=3095,},
{cross_activity_type=3120,min_day=2,},
{cross_activity_type=3123,}
},

cross_activity_open_time_meta_table_map={
[12]=8,	-- depth:1
[10]=4,	-- depth:1
[3]=8,	-- depth:1
[16]=18,	-- depth:1
[2]=18,	-- depth:1
},
cross_activity_open_time1={
{activity_ready_time=1958,activity_start_time=2000,activity_end_time=2015,certain_open_openserver_day=2,},
{cross_activity_type=3074,certain_open_openserver_day="1,3",},
{cross_activity_type=3075,certain_open_openserver_day=2,},
{cross_activity_type=3076,activity_ready_time=2025,},
{cross_activity_type=3084,activity_ready_time=1157,activity_start_time=1200,activity_end_time=1225,},
{cross_activity_type=3085,activity_ready_time=1204,activity_start_time=1205,activity_end_time=1225,},
{cross_activity_type=3087,certain_open_openserver_day=3,},
{cross_activity_type=3106,activity_ready_time=2057,activity_start_time=2100,activity_end_time=2130,},
{cross_activity_type=3078,activity_ready_time=2127,activity_start_time=2130,activity_end_time=2200,},
{cross_activity_type=3103,},
{cross_activity_type=3107,},
{cross_activity_type=3081,},
{cross_activity_type=3082,},
{cross_activity_type=3109,activity_ready_time=800,activity_start_time=810,activity_end_time=2300,},
{cross_activity_type=3110,activity_ready_time=1702,activity_start_time=1705,activity_end_time=1725,},
{cross_activity_type=3102,certain_open_openserver_day=1,},
{cross_activity_type=3095,activity_ready_time=1755,activity_start_time=1800,activity_end_time=1900,},
{cross_activity_type=3120,certain_open_openserver_day=2,},
{cross_activity_type=3123,activity_end_time=1830,certain_open_openserver_day="1,2,3",}
},

cross_activity_open_time1_meta_table_map={
[7]=1,	-- depth:1
[16]=1,	-- depth:1
[18]=8,	-- depth:1
[19]=17,	-- depth:1
},
big_cross_activity_open_time={
{},
{open_weekday=2,},
{open_weekday=5,}
},

big_cross_activity_open_time_meta_table_map={
},
cross_activity_limit={
{},
{cross_activity_type=3074,},
{cross_activity_type=3075,},
{cross_activity_type=3076,},
{cross_activity_type=3084,},
{cross_activity_type=3085,},
{cross_activity_type=3087,},
{cross_activity_type=3106,role_level_limit=300,},
{cross_activity_type=3107,role_level_limit=310,},
{cross_activity_type=3103,},
{cross_activity_type=3081,},
{cross_activity_type=3123,role_level_limit=130,}
},

cross_activity_limit_meta_table_map={
[10]=8,	-- depth:1
},
origin_act_open_time={
{},
{}
},

origin_act_open_time_meta_table_map={
},
origin_act_open_time_2={
{},
{}
},

origin_act_open_time_2_meta_table_map={
},
combine_cap={
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{}
},

combine_cap_meta_table_map={
},
conquest_war_view_display={
{time_1="周一、周四",},
{id=3073,time_1="周二、周五",activity_illustrate="在永夜幻都战场中，总共有9层战场，玩家需要各自为阵，在每层中获得一定的击杀数才可进入下一层。最先登顶的玩家将获得限时最强称号。",activity_title="永夜幻都",activity_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6],[6]=item_table[7]},},
{id=3087,time_1="周三、周六",activity_illustrate="在天玑迷城战场中，玩家们会分成三个阵营进行对决，击杀敌方、争夺天选冠冕可获得积分，倒计时结束时，总积分更高的阵营获胜。",activity_title="天玑迷城",activity_item={[0]=item_table[8],[1]=item_table[3],[2]=item_table[9],[3]=item_table[4],[4]=item_table[10],[5]=item_table[11],[6]=item_table[12]},},
{id=3112,time_1="周六、周日",time_2="20:30-20:45",activity_illustrate="在冠绝征战战场中，玩家们分成两个阵营争夺据点获得积分，战场倒计时结束时，总积分更高的阵营获胜。",activity_title="冠绝征战",activity_item={[0]=item_table[13],[1]=item_table[14],[2]=item_table[15],[3]=item_table[16],[4]=item_table[17]},},
{id=3123,time_2="18:00-18:30",activity_illustrate="在神龙来袭战场中，玩家们先进行答题，根据答对数量获得伤害提升buff。\n答题结束后神龙出现，请玩家们合力击败，对神龙造成伤害可获得奖励，活动结束时，根据个人伤害排名发放丰厚奖励。",activity_title="神龙来袭",activity_item={[0]=item_table[18],[1]=item_table[19],[2]=item_table[20],[3]=item_table[3],[4]=item_table[21],[5]=item_table[22],[6]=item_table[23]},},
{id=3120,time_2="21:00-21:30",activity_illustrate="在大闹天宫中，修士可与其它诸天的修士一同挑战天界神灵，一步一步闯入天宫。\n击败各阶段的神灵首领后，修士个人可获得对应首领的掉落以及评分奖励，击败首领的速度越快评分越高。\n击败全部首领后，天宫会刷新海量宝箱奖励供修士们采集，并在随后开启珍宝拍卖，敬请参与！",activity_title="大闹天宫",activity_item={[0]=item_table[24],[1]=item_table[25],[2]=item_table[26],[3]=item_table[27],[4]=item_table[28],[5]=item_table[29],[6]=item_table[30],[7]=item_table[31],[8]=item_table[32],[9]=item_table[33],[10]=item_table[17],[11]=item_table[34],[12]=item_table[9]},}
},

conquest_war_view_display_meta_table_map={
},
other_default_table={},

cross_activity_default_table={index=1,activity_name="猎龙魔渊",activity_icon="act_69",open_condition=1,is_activity=1,activity_type=52,is_show=1,cs_num_not_show="",day_open=1,},

cross_activity_open_time_default_table={cross_activity_type=3073,min_day=1,max_day=999999,open_weekday="1,2,3,4,5,6,0",},

cross_activity_open_time1_default_table={cross_activity_type=3073,activity_ready_time=2027,activity_start_time=2030,activity_end_time=2100,certain_open_openserver_day=0,},

big_cross_activity_open_time_default_table={cross_activity_type=3112,open_weekday=0,activity_ready_time=2057,activity_start_time=2100,activity_end_time=2115,param1=0,param2=0,},

cross_activity_limit_default_table={cross_activity_type=3073,role_level_limit=160,},

origin_act_open_time_default_table={},

origin_act_open_time_2_default_table={},

combine_cap_default_table={},

conquest_war_view_display_default_table={id=3102,time_1="每天",time_2="20:00-20:15",activity_illustrate="在诛仙战场中，玩家自由作战，通过击杀小怪和魔王或者其他玩家获取积分，活动结算时积分最高的玩家获胜。",activity_title="诛仙战场",activity_item={[0]=item_table[35],[1]=item_table[3],[2]=item_table[9],[3]=item_table[36],[4]=item_table[37],[5]=item_table[15],[6]=item_table[38]},}

}

