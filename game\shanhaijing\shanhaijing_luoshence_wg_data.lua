ShanHaiJingLSCWGData = ShanHaiJingLSCWGData or BaseClass()

function ShanHaiJingLSCWGData:__init()
    ShanHaiJingLSCWGData.Instance = self
    local cfg = ConfigManager.Instance:GetAutoConfig("drawings_compose_cfg_auto")
    self.luoshence_chip_attribute_cfg = ListToMap(cfg.chip_attribute, "type", "drawings_id", "chip_slot")
    self.luoshence_chip_item_cfg = ListToMap(cfg.chip_attribute, "chip_id")
    self.luoshence_suit_attribute_cfg = ListToMap(cfg.suit_attribute, "type", "drawings_id", "suit_num")
    self.luoshence_upgrade_num_cfg = ListToMap(cfg.upgrade_num, "level")
    self.luoshence_card_group_cfg = ListToMap(cfg.card_group, "type", "drawings_id")
    self.luoshence_other_cfg = cfg.other[1]
    self.luoshence_decrease_attribute_cfg = ListToMap(cfg.decrease_attribute, "level")

    self.drawings_list = {}
    self.luoshence_remind = false
    self.luoshence_remind_list = {}
    self.luoshence_item_cache = {}
    self.luoshence_card_attr_cache = {}

    self:CalculationLuoShenCeItemCache()
    RemindManager.Instance:Register(RemindName.ShanHaiJing_LuoShenCe, BindTool.Bind(self.GetShanHaiJingLuoShenCeRemind,self))
end

function ShanHaiJingLSCWGData:__delete()
    ShanHaiJingLSCWGData.Instance = nil
    RemindManager.Instance:UnRegister(RemindName.ShanHaiJing_LuoShenCe)
end

function ShanHaiJingLSCWGData:GetShanHaiJingLuoShenCeRemind()
    if not FunOpen.Instance:GetFunIsOpened(FunName.ShanHaiJingLSCView) then
        return 0
    end
    
    if self.luoshence_remind then
        return 1
    end

    return 0
end

function ShanHaiJingLSCWGData:SetLuoShenCeInfo(protocol)
    self.drawings_list = protocol.drawings_list
    self:CalculationLuoShenCeRemind()
end

function ShanHaiJingLSCWGData:UpdateLuoShenCeInfo(protocol)
    local is_act = false
    local is_up = false
    local type = protocol.type
    local rawings_id = protocol.rawings_id
    local slot_idx = protocol.slot_idx
    local old_level = self:GetLuoShenCeCellData(type, rawings_id, slot_idx)
    local new_level = protocol.level

    if self.drawings_list[type] and self.drawings_list[type][rawings_id] then
        self.drawings_list[type][rawings_id][slot_idx] = new_level
    end

    is_act = old_level == -1 and new_level == 0
    is_up = old_level >= 0 and new_level > old_level
    self:UpdateLuoShenCeRemind(type, rawings_id, slot_idx)

    return is_act, is_up
end

function ShanHaiJingLSCWGData:GetLuoShenCeCellData(type, rawings_id, slot_idx)
    return ((self.drawings_list[type] or {})[rawings_id] or {})[slot_idx] or -1
end

function ShanHaiJingLSCWGData:GetLuoShenCeCellAttrInfo(type, rawings_id, slot_idx)
    return ((self.luoshence_card_attr_cache[type] or {})[rawings_id] or {})[slot_idx] or {}
end

function ShanHaiJingLSCWGData:CalculationLuoShenCeItemCache()
    local luoshence_item_cache = {}
    local luoshence_card_attr_cache = {}
    local cfg = self:GetLuoShenCeCardGroupCfg()

    for k, v in pairs(cfg) do
        for i, u in pairs(v) do
            local slot_cfg = self:GetLuoShenCeChipCellListCfg(u.type, u.drawings_id)
            for m, n in pairs(slot_cfg) do
                local item_id = n.chip_id

                if nil == luoshence_item_cache[item_id] then
                    luoshence_item_cache[item_id] = item_id
                end

                luoshence_card_attr_cache[n.type] = luoshence_card_attr_cache[n.type] or {}
                luoshence_card_attr_cache[n.type][n.drawings_id] = luoshence_card_attr_cache[n.type][n.drawings_id] or {}
                luoshence_card_attr_cache[n.type][n.drawings_id][n.chip_slot] = EquipmentWGData.Instance:OutAttrInfoByAttrId(n, 1 , 8)
            end
        end
    end

    self.luoshence_item_cache = luoshence_item_cache
    self.luoshence_card_attr_cache = luoshence_card_attr_cache
end

function ShanHaiJingLSCWGData:GetLuoShenCeCardGroupCfg()
    return self.luoshence_card_group_cfg
end

function ShanHaiJingLSCWGData:GetLuoShenCeCardGroupTypeCfg(type)
    return self.luoshence_card_group_cfg[type] or {}
end

function ShanHaiJingLSCWGData:GetLuoShenCeCardGroupCellCfg(type, drawings_id)
    return (self.luoshence_card_group_cfg[type] or {})[drawings_id] or {}
end

function ShanHaiJingLSCWGData:GetLuoShenCeChipCellListCfg(type, drawings_id)
    return (self.luoshence_chip_attribute_cfg[type] or {})[drawings_id] or {}
end

function ShanHaiJingLSCWGData:GetLuoShenCeChipCellCfg(type, drawings_id, chip_slot)
    return ((self.luoshence_chip_attribute_cfg[type] or {})[drawings_id] or {})[chip_slot] or {}
end

function ShanHaiJingLSCWGData:CheckLscImgIsOpen(type)
    local item_show_list = {}
	local card_group_cfg = self:GetLuoShenCeCardGroupTypeCfg(type)
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    if IsEmptyTable(card_group_cfg) then
        return item_show_list
    end

    local count = 0
    for i = 0, #card_group_cfg do
        local card_info = card_group_cfg[i]
        local is_show_condition = server_day >= card_info.skynumber_show and role_level >= card_info.level_show
        local slot_cfg = self:GetLuoShenCeChipCellListCfg(card_info.type, card_info.drawings_id)
        local have_stuff = false
        if not IsEmptyTable(slot_cfg) then
            for k, v in pairs(slot_cfg) do
                local slot_level =self:GetLuoShenCeCellData(v.type, v.drawings_id, v.chip_slot)
                local item_count = ItemWGData.Instance:GetItemNumInBagById(v.chip_id)
                if slot_level >= 0 or item_count > 0 then
                    have_stuff = true
                    break
                end
            end
        end

        if have_stuff or is_show_condition then
            item_show_list[count] = card_info
            count = count + 1
        end
    end


	return item_show_list
end

function ShanHaiJingLSCWGData:CalculationLuoShenCeRemind()
    local luoshence_remind_list = {}
    local luoshence_remind = false
    local cfg = self:GetLuoShenCeCardGroupCfg()
    for k, v in pairs(cfg) do
        for i, u in pairs(v) do
            local slot_cfg = self:GetLuoShenCeChipCellListCfg(u.type, u.drawings_id)
            for m, n in pairs(slot_cfg) do
                luoshence_remind_list[n.type] = luoshence_remind_list[n.type] or {remind = false}
                luoshence_remind_list[n.type][n.drawings_id] = luoshence_remind_list[n.type][n.drawings_id] or {remind = false}
                luoshence_remind_list[n.type][n.drawings_id][n.chip_slot] = luoshence_remind_list[n.type][n.drawings_id][n.chip_slot] or {remind = false}
                local level = self:GetLuoShenCeCellData(n.type, n.drawings_id, n.chip_slot)
                local next_level_cfg = self:GetLuoShenCeUpGradeNum(level + 1)
                local is_max_level = IsEmptyTable(next_level_cfg)

                if not is_max_level then
                    local item_num = ItemWGData.Instance:GetItemNumInBagById(n.chip_id)
                    local cost_item_num = self:GetLuoShenCeActiveCardNeedNum(level)

                    if cost_item_num <= item_num then
                        luoshence_remind_list[n.type][n.drawings_id][n.chip_slot].remind = true
                        luoshence_remind_list[n.type][n.drawings_id].remind = true
                        luoshence_remind_list[n.type].remind = true
                        luoshence_remind = true
                    end
                end
            end
        end
    end

    self.luoshence_remind_list = luoshence_remind_list
    self.luoshence_remind = luoshence_remind
end

function ShanHaiJingLSCWGData:UpdateLuoShenCeRemind(type, rawings_id, slot_idx)
    local level = self:GetLuoShenCeCellData(type, rawings_id, slot_idx)
    local next_level_cfg = self:GetLuoShenCeUpGradeNum(level + 1)
    local is_max_level = IsEmptyTable(next_level_cfg)

    local cfg = self:GetLuoShenCeChipCellCfg(type, rawings_id, slot_idx)
    local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg.chip_id)
    local cost_item_num = self:GetLuoShenCeActiveCardNeedNum(level)

    if not is_max_level and cost_item_num <= item_num then
        self.luoshence_remind_list[type][rawings_id][slot_idx].remind = true
        self.luoshence_remind_list[type][rawings_id].remind = true
        self.luoshence_remind_list[type].remind = true
        self.luoshence_remind = true
    else
        self.luoshence_remind_list[type][rawings_id][slot_idx].remind = false
        local draw_cfg = self:GetLuoShenCeCardGroupTypeCfg(type)
        local type_remind = false
        for k, v in pairs(draw_cfg) do
            local slot_cfg = self:GetLuoShenCeChipCellListCfg(type, rawings_id)
            
            -- 计算当前小碎片红点
            local slot_remind = false
            for m, n in pairs(slot_cfg) do
                if self.luoshence_remind_list[type][rawings_id][n.chip_slot].remind then
                    slot_remind = true
                    break
                end
            end
            self.luoshence_remind_list[type][rawings_id].remind = slot_remind

            -- 计算当前8碎片的image 红点
            for i = 0, 7 do
                if self.luoshence_remind_list[type][i].remind then
                    type_remind = true
                end
            end
        end

        self.luoshence_remind_list[type].remind = type_remind

        local remind = false
        for k, v in pairs(self.luoshence_remind_list) do
            if v.remind then
                remind = true
                break
            end
        end

        self.luoshence_remind = remind
    end
end

function ShanHaiJingLSCWGData:GetLuoShenCeDecreaseAttributeCfg(level)
    return self.luoshence_decrease_attribute_cfg[level] or {}
end

function ShanHaiJingLSCWGData:GetLuoShenCeCap()
    local cap = 0
    local all_attr_per = 0
    local base_attribute = AttributePool.AllocAttribute()
    local all_attribute = AttributePool.AllocAttribute()

    local cfg = self:GetLuoShenCeCardGroupCfg()
    for k, v in pairs(cfg) do
        for i, u in pairs(v) do

            local slot_cfg = self:GetLuoShenCeChipCellListCfg(u.type, u.drawings_id)
            for m, n in pairs(slot_cfg) do

                local level = self:GetLuoShenCeCellData(n.type, n.drawings_id, n.chip_slot)
                if level >= 0 then
                    local attr_info = self:GetLuoShenCeCellAttrInfo(n.type, n.drawings_id, n.chip_slot)
                    base_attribute = base_attribute + attr_info
                    all_attribute = all_attribute + attr_info

                    if level >= 1 then
                        for i = 1, level do
                            local dec_cfg = self:GetLuoShenCeDecreaseAttributeCfg(i - 1)
                            local rate = 1 - (dec_cfg.decrease_num / 10000)
                            all_attribute = all_attribute + EquipmentWGData.Instance:OutAttrInfoByAttrId(n, 1, 8, rate)
                        end
                    end
                end
            end

            local cfg = self:GetLuoShenCeSuitAttrItemCfg(u.type, u.drawings_id)
            local active_num = self:GetLuoShenCeSuitActiveNum(u.type, u.drawings_id)
            local active_attr_data = {}

            for k, v in pairs(cfg) do
                if v.suit_num > active_num then
                    break
                end

                active_attr_data = v
            end

            if not IsEmptyTable(active_attr_data) then
                if active_attr_data.attr_per > 0 then
                    all_attr_per = all_attr_per + active_attr_data.attr_per
                end
                
                all_attribute = all_attribute + EquipmentWGData.Instance:OutAttrInfoByAttrId(active_attr_data, 1, 2)
            end
        end
    end

    cap = AttributeMgr.GetCapability(all_attribute) + AttributeMgr.GetCapability(base_attribute) * (all_attr_per / 10000)
    cap = math.floor(cap)
    return cap
end

function ShanHaiJingLSCWGData:GetLuoShenCeOtherCfg()
    return self.luoshence_other_cfg
end

function ShanHaiJingLSCWGData:GetLuoShenCeUpGradeNum(level)
    return self.luoshence_upgrade_num_cfg[level] or {}
end

function ShanHaiJingLSCWGData:GetLuoShenCeActiveCardNeedNum(level)
    if level < 0 then
        return self.luoshence_other_cfg.active_num
    else
        local cfg = self:GetLuoShenCeUpGradeNum(level)

        if not IsEmptyTable(cfg) then
            return cfg.need_num
        end
    end
end

function ShanHaiJingLSCWGData:IsLuoSHenCeItem(change_item_id)
    return nil ~= self.luoshence_item_cache[change_item_id]
end

function ShanHaiJingLSCWGData:GetLuoShenCeItemCfgByItemId(change_item_id)
    return self.luoshence_chip_item_cfg[change_item_id]
end

function ShanHaiJingLSCWGData:GetLuoShenCeTypeRemind(type)
    return (self.luoshence_remind_list[type] or {}).remind or false
end

function ShanHaiJingLSCWGData:GetLuoShenCeTypeCellRemind(type, rawings_id)
    return ((self.luoshence_remind_list[type] or {})[rawings_id] or {}).remind or false
end

function ShanHaiJingLSCWGData:GetLuoShenCeCellSlotRemind(type, rawings_id, slot_index)
    return (((self.luoshence_remind_list[type] or {})[rawings_id] or {})[slot_index] or {}).remind or false
end

function ShanHaiJingLSCWGData:GetLuoShenCeSuitAttrTitle(type, drawings_id)
    local name = ""
    local cfg = self:GetLuoShenCeCardGroupCellCfg(type, drawings_id)
	if not IsEmptyTable(cfg) then
        local active_num = self:GetLuoShenCeSuitActiveNum(type, drawings_id)
        local color = active_num >= 8 and COLOR3B.D_GREEN or COLOR3B.D_RED
        name = string.format(Language.LuoShenCe.SuitTitleName, cfg.name, color, active_num)
	end

    return name
end

function ShanHaiJingLSCWGData:GetLuoShenCeSuitActiveNum(type, drawings_id)
    local num = 0
    for i = 0, 7 do
        if self:GetLuoShenCeCellData(type, drawings_id, i) >= 0 then
            num = num + 1
        end
    end

    return num
end

function ShanHaiJingLSCWGData:GetLuoShenCeSuitAttrItemCfg(type, drawings_id)
    return (self.luoshence_suit_attribute_cfg[type] or {})[drawings_id] or {}
end

function ShanHaiJingLSCWGData:GetLuoShenCeSuitAttrDataList(type, drawings_id)
    local data_list = {}
    local cfg = self:GetLuoShenCeSuitAttrItemCfg(type, drawings_id)
    local active_num = self:GetLuoShenCeSuitActiveNum(type, drawings_id)

    for k, v in pairs(cfg) do
        local data = {}
        data.suit_num = v.suit_num
        data.active_num = active_num
        data.cfg = v

        table.insert(data_list, data)
    end

    return data_list
end