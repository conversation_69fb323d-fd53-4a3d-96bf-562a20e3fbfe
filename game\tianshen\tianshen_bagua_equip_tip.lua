TianShenBaGuaEquipTip = TianShenBaGuaEquipTip or BaseClass(SafeBaseView)

function TianShenBaGuaEquipTip:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/tianshen_prefab", "layout_bagua_equip_tip")
end

function TianShenBaGuaEquipTip:__delete()
end

function TianShenBaGuaEquipTip:ReleaseCallBack()

	if self.bagua_equip_list then
		self.bagua_equip_list:DeleteMe()
		self.bagua_equip_list = nil
	end

end

function TianShenBaGuaEquipTip:LoadCallBack()
	self.bagua_equip_list = AsyncListView.New(BaGuaEquipCell, self.node_list["bagua_equip_list"])
end

function TianShenBaGuaEquipTip:ShowIndexCallBack()

end

function TianShenBaGuaEquipTip:OnFlush()
	local wear_type,wear_list = TianShenBaGuaWGData.Instance:GetWearableEquip(self.cur_bagua_index,self.equip_data.index-1)
	if wear_type == 0 then
		if #wear_list == 0 then
			local data_list = TianShenBaGuaWGData.Instance:GetAdviseShowCfg(self.equip_data.index-1)
			self.node_list.title_text.text.text = string.format(Language.TianShen.NotEnoughBaGua, data_list[1].name)
			for k,v in pairs(data_list) do
				v.cur_bagua_index = self.cur_bagua_index
				v.show_item = 0
			end
			local base_cfg = TianShenBaGuaWGData.Instance:GetBaGuaCfgByIndexStarPart(self.cur_bagua_index,1,self.equip_data.index-1)
			data_list[1].show_item = base_cfg.itemid or 0
			self.bagua_equip_list:SetDataList(data_list)
		else
			self.node_list.title_text.text.text = Language.TianShen.SelectToEquipBaGua
			self.bagua_equip_list:SetDataList(wear_list)
		end
	else
		self.node_list.title_text.text.text = Language.TianShen.SelectToEquipBaGua
		self.bagua_equip_list:SetDataList(wear_list)
	end
end


function TianShenBaGuaEquipTip:SetEquipData(data)
	self.equip_data = data
	self.cur_bagua_index = TianShenBaGuaWGData.Instance:GetCurSelectBaGua()
end

BaGuaEquipCell = BaGuaEquipCell or BaseClass(BaseRender)

function BaGuaEquipCell:__init()
	self.item_cell = ItemCell.New(self.node_list["item_cell"])
	self.node_list["go_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGoBtn,self))

end

function BaGuaEquipCell:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function BaGuaEquipCell:OnFlush()
	if self.data then
		if self.data.icon then
			self.node_list["item_cell"]:SetActive(false)
			self.node_list["item_icon"]:SetActive(false)
			self.node_list["up_arrow"]:SetActive(false)
			self.node_list["down_arrow"]:SetActive(false)
			self.node_list["power_panel"]:SetActive(false)
			if self.data.show_item == 0 then
				self.node_list["item_icon"]:SetActive(true)
				self.node_list.item_icon.image:LoadSprite(ResPath.GetMainUIIcon(self.data.icon))
			else
				self.node_list["item_cell"]:SetActive(true)
				self.item_cell:SetData({item_id = self.data.show_item})
			end
			self.node_list.middle_name.text.text = Language.TianShen.GoToBaGuaShop
			self.node_list.name_text.text.text = ""

		else
			self.node_list["item_cell"]:SetActive(true)
			self.node_list["item_icon"]:SetActive(false)
			self.node_list["power_panel"]:SetActive(true)
			self.node_list["up_arrow"]:SetActive(self.data.batter)
			self.node_list["down_arrow"]:SetActive(not self.data.batter and not self.data.same)
			self.item_cell:SetData(self.data)
			local base_cfg = TianShenBaGuaWGData.Instance:GetBaGuaBaseInfo(self.data.item_id)
			if base_cfg then
				self.item_cell:SetLeftTopImg(base_cfg.star or 0)
			else
				self.item_cell:SetLeftTopImg(0)
			end

			local cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
			self.node_list.name_text.text.text = ToColorStr(cfg.name or "",ITEM_COLOR[cfg.color])
			self.node_list.middle_name.text.text = ""
			local attr_list,is_pre,power = TianShenBaGuaWGData.Instance:GetBaGuaEquipAttrAndZhanLi(self.data.item_id,true)
			self.node_list["cap_value"].text.text = power

			self.attr_str = {}
			local str2 = ""
			local num = 0
		end
	end
end

function BaGuaEquipCell:OnClickGoBtn()
	if self.data and self.data.icon then
		TianShenWGCtrl.Instance:OpenBaGuaShopView()
		TianShenWGCtrl.Instance:CloseBaGuaEquipTis()
	elseif self.data and self.data.index then
		local ok_func = function ()
			TianShenWGCtrl.Instance:SendBaGuaOpera(TIANSHEN_BAGUA_REQ.TIANSHEN_BAGUA_REQ_PUT,self.data.index)
			TianShenWGCtrl.Instance:CloseBaGuaEquipTis()
		end
		if self.data.batter then
			ok_func()
		else
			TipWGCtrl.Instance:OpenConfirmAlertTips(Language.TianShen.NoBatterEquip, ok_func, Language.Common.Confirm1, false)
		end
	end
end