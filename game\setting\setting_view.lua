SettingView = SettingView or BaseClass(SafeBaseView)

SettingViewIndex = {
	Guaji = TabIndex.setting_guaji,
	--Gm = TabIndex.setting_contect,
	LiBao = TabIndex.welfare_libao,
	Screen = TabIndex.setting_screen,
}


local QUALITY_VALUE =
{
	3,	--低端
	2,	--普通
	1,	--良好
	0,	--最佳
}
function SettingView:__init()
	self:SetMaskBg(false)
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self.view_name = GuideModuleName.Setting
	self.is_align_right = true						-- 是否向右对齐
	self.default_index = SettingViewIndex.Screen

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "VerticalTabbar")
	self:AddViewResource(SettingViewIndex.Screen, "uis/view/setting_ui_prefab", "layout_setting_common")
	self:AddViewResource(SettingViewIndex.Screen, "uis/view/setting_ui_prefab", "layout_sys_screen")
	self:AddViewResource(SettingViewIndex.Guaji, "uis/view/setting_ui_prefab", "layout_guaji_set")
	-- self:AddViewResource(SettingViewIndex.Guaji, "uis/view/setting_ui_prefab", "layout_guaji")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
	--self:AddViewResource(SettingViewIndex.Gm, "uis/view/setting_ui_prefab", "layout_contect")
	-- self:AddViewResource(SettingViewIndex.LiBao, "uis/view/setting_ui_prefab", "layout_libao")
	self.OPTION_COUNT = #SettingPanel1
	self.GUAJI_OPTION_COUNT = 24

	self.SCREEN_COUNT = 12

	self.is_req_info = false

	self.is_init_common = false

	self.set_flag = {}
	self.guaji_set_flag = {}
	self.hp_percent = 0
	self.mp_percent = 0

	self.title_edit = nil
	self.content_edit = nil

	self.register_view = nil

	self.is_show_mode_list1 = false
	self.cur_select_pick = 1

	self.is_show_mode_list2 = false
	self.cur_select_break = 1

	self.is_show_mode_list3 = false
	self.cur_select_card = 1

	self.is_show_mode_list4 = false
	self.cur_select_treasure = 1

	self.default_index = SettingViewIndex.Screen

	self.can_change_quality_time = 0
	self.is_toggle_flush = false
end

function SettingView:__delete()
	if nil ~= self.register_view then
		self.register_view:DeleteMe()
		self.register_view = nil
	end
	if self.countdonw then
		CountDownManager.Instance:RemoveCountDown("tlks_cds")
		self.countdonw = nil
	end

	if self.fsfk_count_down then
		CountDownManager.Instance:RemoveCountDown("fsfk_cds")
		self.fsfk_count_down = nil
	end
end

function SettingView:InitTabbar()

	-- if IS_AUDIT_VERSION or IS_FREE_VERSION then
	-- 	self.tabbar:SetToggleVisible(SettingViewIndex.Gm, false)
	-- end

	if nil == self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:SetVerTabbarIconStr("shezhi")
		self.tabbar:Init(Language.Setting.TabGrop, nil)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	end
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.Setting, self.get_guide_ui_event)
end

function SettingView:LoadCallBack(index, loaded_times)
	self.node_list.title_view_name.text.text = Language.Setting.title_view_name
	self:InitTabbar()
	self.confirm_dialog = Alert.New()

	if not self.is_req_info then
		self.is_req_info = true
		SettingWGCtrl.Instance:SendHotkeyInfoReq()
	end

	local bundle, asset = ResPath.GetRawImagesPNG("a3_ty_bg4")
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)
end

function SettingView:LoadIndexCallBack(index)
	if SettingViewIndex.Guaji == index then
		self:InitGuaJi()
	--elseif SettingViewIndex.Gm == index then
	--	self:InitContectGm()
	--[[elseif SettingViewIndex.LiBao == index then
		self:InitGiftView()--]]
	elseif SettingViewIndex.Screen == index then
		self:InitCommon()
		self:InitSettingScreen()
	end
	self:ChangeHeadIcon()
end

function SettingView:OpenCallBack()

end

function SettingView:CloseCallBack()
	local list = {}
	if 0 ~= #self.set_flag then
		list[1] = {HOT_KEY.SYS_SETTING_1, bit:b2d(self.set_flag)}
	end

	if 0 ~= #self.guaji_set_flag then
		list[#list + 1] = {HOT_KEY.SYS_SETTING_2, bit:b2d(self.guaji_set_flag)}
	end

	if not IsEmptyTable(list) then
		SettingWGCtrl.Instance:SendChangeHotkeyReq(list)
	end
	if self.node_list.yxSlider and self.node_list.yySlider then
		local sound_effect_volume = self.node_list.yxSlider.slider.value
		local bg_music_volume = self.node_list.yySlider.slider.value
		PlayerPrefsUtil.SetFloat("sound_effect_volume", sound_effect_volume)
		PlayerPrefsUtil.SetFloat("bg_music_volume", bg_music_volume)
	end

	local hp_percent, mp_percent = SettingWGData.Instance:GetSupplyData()
	if hp_percent ~= self.hp_percent or mp_percent ~= self.mp_percent then
		SettingWGCtrl.Instance:ChangeSupplySetting(self.hp_percent, self.mp_percent)
	end
	local pickup_color, break_color, card_color, treasure_color = SettingWGData.Instance:GetPickBreaKData()
	if pickup_color ~=self.cur_select_pick or break_color ~= self.cur_select_break or card_color ~= self.cur_select_card or treasure_color ~= self.cur_select_treasure then
		SettingWGCtrl.Instance:ChangePickBreakSetting(self.cur_select_pick, self.cur_select_break, self.cur_select_card, self.cur_select_treasure)
	end

	if nil ~= self.sound_prog_blood then
		local percent = SettingWGData.Instance:GetSettingData(HOT_KEY.SYS_SOUND) or 100
		local percent_num = 100 - math.ceil(self.sound_prog_blood:getPercent())
		if nil ~= percent and self.sound_prog_blood and percent ~= percent_num then
			SettingWGCtrl.Instance:ChangeSoundSetting(percent_num)
		end
	end

	if nil ~= self.bg_sound_prog_blood then
		local bg_percent = SettingWGData.Instance:GetSettingData(HOT_KEY.SYS_BG_SOUND) or 100
		local bg_percent_num = 100 - math.ceil(self.bg_sound_prog_blood:getPercent())
		if nil ~= bg_percent and self.bg_sound_prog_blood and bg_percent ~= bg_percent_num then
			SettingWGCtrl.Instance:ChangeBgSoundSetting(bg_percent_num)
		end
	end

	self.is_toggle_flush = false
end

function SettingView:ReleaseCallBack()
	if nil ~= self.confirm_dialog then
		self.confirm_dialog:DeleteMe()
		self.confirm_dialog = nil
	end
	self.is_init_common = false
	self.title_edit = nil
	self.content_edit = nil
	self.is_show_mode_list1 = false
	self.is_show_mode_list2 = false
	self.is_show_mode_list3 = false
	self.is_show_mode_list4 = false
	self:DeleteSettingGuaji()
	--self:DescoryGift()
	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.role_head_cell then
		self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
	end

	self.sound_prog_blood = nil
	self.bg_sound_prog_blood = nil

	if self.destroy_list then
		self.destroy_list:DeleteMe()
		self.destroy_list = nil
	end

	if nil ~= self.choice_quality_alert then
        self.choice_quality_alert:DeleteMe()
        self.choice_quality_alert = nil
    end

	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.Setting, self.get_guide_ui_event)
	self.get_guide_ui_event = nil
end

function SettingView:InitCommon()
	local funs = {BindTool.Bind1(self.OnClickToLogin, self),
				  BindTool.Bind1(self.OnClickToChang, self),}
	local tag1, tag2 = 1, 2  --对应后面两个按钮

	self.node_list["btn_tologin_text"].text.text = Language.Setting.BtnNameList[tag1]
	XUI.AddClickEventListener(self.node_list["btn_tologin"], funs[tag1])
	XUI.AddClickEventListener(self.node_list["btn_confirm"], funs[tag2])
end

function SettingView:InitSetting()
	for i = 1, self.OPTION_COUNT do
		if i ~= 5 and i ~= 7 then
			XUI.AddClickEventListener(self.node_list["layout_setting_option"..i], BindTool.Bind(self.OnClickSysSetting, self, i))
		end
	end

	self:RefreshCheckBox()

end


function SettingView:InitSettingScreen()
	for i = 1, self.OPTION_COUNT do
		if i ~= 5 and i ~= 7 then
			XUI.AddClickEventListener(self.node_list["layout_screen_option"..i], BindTool.Bind(self.OnClickScreenSetting, self, i))
		end
	end

	local recommend_value = SettingWGData.Instance:GetRecommendQuality()
	for i = 1, 4 do
		self.node_list["tj_icon" .. i]:SetActive(recommend_value == QUALITY_VALUE[i])
		self.node_list["qualityconfig_screen_" .. i].toggle:AddClickListener(BindTool.Bind(self.QualityToggleClick, self, QUALITY_VALUE[i]))
	end

	self.node_list.tj_hight:SetActive(recommend_value <= QUALITY_VALUE[3])
	local str = Language.Setting.HuaZhiType[recommend_value]
	self.node_list.tuijian_content.text.text = string.format(Language.Setting.Tuijian, str)
	self.node_list.yxSlider.slider.onValueChanged:AddListener(BindTool.Bind(self.OnYXSoundValueChange, self))
	self.node_list.yySlider.slider.onValueChanged:AddListener(BindTool.Bind(self.OnYYSoundValueChange, self))
	self.node_list["TlksBtn"].button:AddClickListener(BindTool.Bind(self.OnClickTlksBtn, self))
	self:FlushScreenHl(PlayerPrefsUtil.GetInt("a3_custom_quality_level"))
	self:setCheckBox()
	self:RefreshScreenCheckBox()
	self:SetTlksTimeCd()
	self:FlushSlider()
end

function SettingView:FlushSlider()
	self.is_paly_soud_effect = self.node_list["img_screen_hook13"]:GetActive()
	self.is_play_bg_music = self.node_list["img_screen_hook14"]:GetActive()
	local sound_effect_volume = 0
	local bg_music_volume = 0

	if PlayerPrefsUtil.HasKey("sound_effect_volume") and self.is_paly_soud_effect then
		sound_effect_volume = PlayerPrefsUtil.GetFloat("sound_effect_volume")
		sound_effect_volume = sound_effect_volume > 0 and sound_effect_volume or 0.8
	elseif self.is_paly_soud_effect then
		sound_effect_volume = 0.8
		PlayerPrefsUtil.SetFloat("sound_effect_volume", sound_effect_volume)
	end
	if PlayerPrefsUtil.HasKey("bg_music_volume") and self.is_play_bg_music then
		bg_music_volume = PlayerPrefsUtil.GetFloat("bg_music_volume")
		bg_music_volume = bg_music_volume > 0 and bg_music_volume or 0.8
	elseif self.is_play_bg_music then
		bg_music_volume = 0.8
		PlayerPrefsUtil.SetFloat("bg_music_volume", bg_music_volume)
	end

	self.node_list.yySlider.slider.value = bg_music_volume
	self.node_list.yxSlider.slider.value = sound_effect_volume
	self.node_list.yx_num.text.text = math.floor(sound_effect_volume * 100) 
	self.node_list.yy_num.text.text = math.floor(bg_music_volume * 100)
end

function SettingView:OnYXSoundValueChange(sender)
	self.node_list.yx_num.text.text = math.floor(sender*100)
	if (sender > 0 and not self.is_paly_soud_effect) or (sender <= 0 and self.is_paly_soud_effect) then
		self:OnClickScreenSetting(13)
	end
	AudioService.Instance:SetSFXVolume(sender)
end

function SettingView:OnYYSoundValueChange(sender)
	self.node_list.yy_num.text.text = math.floor(sender*100)
	if (sender > 0 and not self.is_play_bg_music) or (sender <= 0 and self.is_play_bg_music) then
		self:OnClickScreenSetting(14)
	end
	AudioService.Instance:SetMusicVolume(sender)
end

function SettingView:QualityToggleClick(i, is_on)
	if is_on and not self.is_toggle_flush then
		if self.can_change_quality_time >= Status.NowTime then
			local time = math.ceil(self.can_change_quality_time - Status.NowTime)
			local index = QualityConfig.QualityLevel
			if index ~= i then
				SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.IsQualityChanging, time))
				self:FlushScreenHl(index)
			end
			return
		end

		if QualityConfig.QualityLevel == i then
			return
		end

		local recommend_value = SettingWGData.Instance:GetRecommendQuality()
		if recommend_value > i then
			self:FlushScreenHl(QualityConfig.QualityLevel)
			if not self.choice_quality_alert then
			    self.choice_quality_alert = Alert.New()
			end

			local str = Language.Setting.HuaZhiType[recommend_value]
			self.choice_quality_alert:SetShowCheckBox(true, "quality_click")
			self.choice_quality_alert:SetCheckBoxText(Language.Setting.DontTipToday)
			self.choice_quality_alert:SetOkFunc(function()
				self:QualityClick(i)
				self:FlushScreenHl(i)
			end)

			self.choice_quality_alert:SetLableString(string.format(Language.Setting.TuijianDesc, str))
			self.choice_quality_alert:SetCheckBoxDefaultSelect(false)
			self.choice_quality_alert:SetLableRectWidth(580)
			self.choice_quality_alert:Open()
		else
			self:QualityClick(i)
		end
	end
end

function SettingView:QualityClick(quality_value)
	local is_need_save = PlayerPrefsUtil.GetInt("a3_custom_quality_level") ~= quality_value
	self.can_change_quality_time = Status.NowTime + 5
	QualityConfig.QualityLevel = quality_value
	PlayerPrefsUtil.SetInt("a3_custom_quality_level", quality_value)

	if GlobalEventSystem then
		GlobalEventSystem:Fire(ObjectEventType.QUALITY_CHANGE)
	end
	if is_need_save then
		PlayerPrefsUtil.Save()
	end
end

function SettingView:FlushHl(quality_value)
	for i=1,4 do
		self.node_list["qualityconfig_" .. i].toggle.isOn = i-1 == 3-quality_value
	end
end

function SettingView:FlushScreenHl(quality_value)
	self.is_toggle_flush = true
	self.node_list["qualityconfig_screen_" .. 4 - quality_value].toggle.isOn = true
	self.is_toggle_flush = false
end
function SettingView:setCheckBox()

end

function SettingView:OnSoundValueChange(sender, event_type)
	local percent_num = 100 - math.ceil(self.sound_prog_blood:getPercent())
	GlobalEventSystem:Fire(SettingEventType.SOUND_SETTING_CHANGE, percent_num)
end

function SettingView:OnBgSoundValueChange(sender, event_type)
	local percent_num = 100 - math.ceil(self.bg_sound_prog_blood:getPercent())
	GlobalEventSystem:Fire(SettingEventType.BG_SOUND_SETTING_CHANGE, percent_num)
end

function SettingView:RefreshCheckBox()
	for i,v in pairs(SettingPanel1) do
		local flag = SettingWGData.Instance:GetSettingData(v)
			if i ~= 5 and i ~= 7 then
				self.node_list["img_setting_hook"..i]:SetActive(flag or false)
			end
	end
end

function SettingView:RefreshScreenCheckBox()
	for i,v in pairs(SettingPanel1) do
		local flag = SettingWGData.Instance:GetSettingData(v)
		if i == 6 or i == 16 or i == 18 then
			self.node_list["img_screen_hook"..i]:SetActive(flag)
			self.node_list["img_screen_check" .. i]:SetActive(not flag)
		elseif i == 13 or i == 14 then
			self.node_list["img_screen_hook"..i]:SetActive(not flag)
			self.node_list["img_screen_check" .. i]:SetActive(flag)
		elseif i ~= 5 and i ~= 7  then
			self.node_list["img_screen_hook"..i]:SetActive(flag or false)
		end
	end
end

function SettingView:InitSettingGuaJi()
	for i = 1, self.GUAJI_OPTION_COUNT do
		if self.node_t_list["layout_guaji_option"..i] then
			XUI.AddClickEventListener(self.node_list["layout_guaji_option"..i].node, BindTool.Bind(self.OnClickGuijiSetting, self, i))
		end
	end

	for i=1,4 do
		self.node_t_list["layout_btn_get" .. i].node:setTouchEnabled(true)
		self.node_t_list["layout_btn_get" .. i].node:addClickEventListener(BindTool.Bind(self.OnSelectGetProp, self, i))
	end
	self.node_t_list["layout_btn_fusion"].node:setTouchEnabled(true)
	self.node_t_list["layout_btn_fusion"].node:addClickEventListener(BindTool.Bind(self.OnSelectDestroyProp, self))

	self.node_t_list["img9_get4_bg"].node:setVisible(false)
	self.node_t_list["img9_destroy_bg"].node:setVisible(false)

	self:CreateDestroyPropList()
	self:OnFlushDestroyPropText()

	self:RefreshCheckBoxGuaJi()
	self:RefreshSkillIcon()
end

function SettingView:RefreshSkillIcon()

end

function SettingView:OnSelectGetProp(btn_index)

end

function SettingView:OpenGetPropModelList(btn_index)

end

function SettingView:SelectGetPropModel(btn_index)

end

-- 装备回收
function SettingView:OnSelectDestroyProp()

end

function SettingView:OpenDestroyPropModelList(btn_index)

end

function SettingView:SelectDestroyPropModel(btn_index)

end

function SettingView:OnFlushDestroyPropText()
	if not self.node_t_list["layout_btn_fusion"].lbl_type then return end
	local btn_index = self.cur_select_break
end

function SettingView:CreateDestroyPropList()
	if self.destroy_list then return end
	local ph = self.node_list["ph_list_destory"]
	local list_view = AsyncListView.New(DestroyPropRender, ph)
	list_view:SetSelectCallBack(BindTool.Bind(self.SelectDestroyListCallback, self))
	self.destroy_list = list_view
end

function SettingView:SetDestroyPropListData()
	if not self.destroy_list then return end
	local btn_index = self.cur_select_break
	local data_list = {}
	for i=1,10 do
		if i ~= btn_index then
			data_list[#data_list + 1] = {btn_index = i}
		end
	end
	self.destroy_list:SetDataList(data_list)
end

function SettingView:SelectDestroyListCallback(cur_select_item, index)
	if not cur_select_item then return end
	local data = cur_select_item:GetData()
	self:SelectDestroyPropModel(data.btn_index)
end


function SettingView:RefreshCheckBoxGuaJi()

end

function SettingView:InitContectGm()
	for i = 1, 3 do
		self.node_list["layout_gm_option".. i].toggle:AddClickListener(BindTool.Bind(self.OnClickContectGmOption, self, i))
	end
	self:OnClickContectGmOption(1)
	self.node_list["label_contect_info"].text.text = Language.Setting.ContectInfoText

	XUI.AddClickEventListener(self.node_list["btn_send"], BindTool.Bind1(self.OnClickSend, self))
end


function SettingView:CreateEditBox()

end



-- 刷新
function SettingView:OnFlush(param_list, index)
	if index == SettingViewIndex.System then
		self:RefreshCheckBox()
	elseif index == SettingViewIndex.Guaji then
		self:RefreshGuaJi()
	elseif index == SettingViewIndex.Screen then
		self:RefreshScreenCheckBox()
		self:FlushSlider()
	end
end

-- 返回登录
-- 这里不应该不走sdk接口，因为是直接返回到选角界面，并不需要注销账号
function SettingView:OnClickToLogin()
	local function ok_callback()
		GlobalEventSystem:Fire(LoginEventType.LOGOUT)
	end

	self.confirm_dialog:SetOkString(Language.Common.Confirm)
	self.confirm_dialog:SetCancelString(Language.Common.Cancel)
	self.confirm_dialog:SetLableString(Language.Common.BackToLogin)
	self.confirm_dialog:SetOkFunc(ok_callback)
	self.confirm_dialog:Open()
end

function SettingView:OnClickToChang()
	local function ok_callback()
		UtilU3d.CacheData("select_role_state", 1)
		local user_vo = GameVoManager.Instance:GetUserVo()
		UtilU3d.CacheData("utilu3d_cahce_account_user_id", user_vo.account_user_id)
		UtilU3d.CacheData("select_role_state_plat_server_id", user_vo.plat_server_id)
		GameRestart()
	end

	self.confirm_dialog:SetOkString(Language.Common.Confirm)
	self.confirm_dialog:SetCancelString(Language.Common.Cancel)
	self.confirm_dialog:SetLableString(Language.Common.BackToLogin1)
	self.confirm_dialog:SetOkFunc(ok_callback)
	self.confirm_dialog:Open()
end

-- -- 联系GM
-- function SettingView:FindGm()

-- end

-- 公告
function SettingView:SystemAnnouncement()
	FunOpen.Instance:OpenViewByName(GuideModuleName.UpdateAffiche)
end

-- 管理中心
function SettingView:ManageCenter()
	if AgentAdapter.OpenManagerCenter then
		AgentAdapter:OpenManagerCenter()
	end
end

-- 升级账号
function SettingView:LevelUpAccount()
	self:Close()
end

-- 确定
function SettingView:OnConfirm()
	self:Close()
end

-- 系统设置项
function SettingView:OnClickSysSetting(index)
	local img_hook = self.node_list["img_setting_hook" .. index]

	local flag = not img_hook:GetActive()
	for _,v in pairs(FixBugSettting) do
		if v == SettingPanel1[index] then
			SettingWGData.Instance:SetBugFixRecordValue(SettingPanel1[index], flag)
		end
	end
	img_hook:SetActive(flag)
	SettingWGData.Instance:SetSettingData1(SettingPanel1[index], flag, true)

end

-- 系统设置项
function SettingView:OnClickScreenSetting(index)
	local recommend_value = SettingWGData.Instance:GetRecommendQuality()
	local img_hook = self.node_list["img_screen_hook" .. index]
	local flag = not img_hook:GetActive()
	if index == 15 and flag and recommend_value > QUALITY_VALUE[4] then 
		if not self.choice_quality_alert then
		    self.choice_quality_alert = Alert.New()
		end

		self.choice_quality_alert:SetShowCheckBox(true, "hight_click")
		self.choice_quality_alert:SetCheckBoxText(Language.Setting.DontTipToday)
		self.choice_quality_alert:SetOkFunc(function()
			self:OnSetScreenSetting(index)
		end)
		self.choice_quality_alert:SetLableString(Language.Setting.HightDesc)
		self.choice_quality_alert:SetCheckBoxDefaultSelect(false)
		self.choice_quality_alert:SetLableRectWidth(580)
		self.choice_quality_alert:Open()
	else
		self:OnSetScreenSetting(index)
	end
end

function SettingView:OnSetScreenSetting(index)
	local img_hook = self.node_list["img_screen_hook" .. index]
	local flag = not img_hook:GetActive()
	if index == 6 or index == 13 or index == 14 or index == 16 or index == 18 then
		self.node_list["img_screen_check" .. index]:SetActive(not flag)
	end

	for _,v in pairs(FixBugSettting) do
		if v == SettingPanel1[index] then
			SettingWGData.Instance:SetBugFixRecordValue(SettingPanel1[index], flag)
		end
	end

	img_hook:SetActive(flag)
	if SettingPanel1[index] == SETTING_TYPE.CLOSE_SOUND_EFFECT then
		self.is_paly_soud_effect = flag
		local sound_effect_volume = 0
		if self.is_paly_soud_effect then 
			local now_volume = self.node_list.yxSlider.slider.value
			sound_effect_volume = (now_volume == 0 and 0.8) or now_volume
		else
			sound_effect_volume = 0
		end
		self.node_list.yxSlider.slider.value = sound_effect_volume
		self.node_list.yx_num.text.text = math.floor(sound_effect_volume * 100)
		PlayerPrefsUtil.SetFloat("sound_effect_volume", sound_effect_volume)
		flag = not flag
	elseif SettingPanel1[index] == SETTING_TYPE.CLOSE_BG_MUSIC then
		self.is_play_bg_music = flag
		local bg_music_volume = 0
		if self.is_play_bg_music then
			local now_volume = self.node_list.yySlider.slider.value
			bg_music_volume = (now_volume == 0 and 0.8) or now_volume
		else
			bg_music_volume = 0
		end
		self.node_list.yySlider.slider.value = bg_music_volume
		self.node_list.yy_num.text.text = math.floor(bg_music_volume * 100)
		PlayerPrefsUtil.SetFloat("bg_music_volume", bg_music_volume)
		flag = not flag
	end

	SettingWGData.Instance:SetSettingData1(SettingPanel1[index], flag, true)
end

function SettingView:OnHpSliderEvent(sender, percent)
	self.hp_percent = math.floor(percent)
end

function SettingView:OnMpSliderEvent(sender, percent)
	self.mp_percent = math.floor(percent)
end

-- 联系Gm项
function SettingView:OnClickContectGmOption(index)
	self.contact_gm_select_index = index

end

-- 发送
function SettingView:OnClickSend()
	local issue_subject = self.node_list["title_text"].text.text
	local issue_content = self.node_list["label_content"].text.text
	if "" == issue_subject then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Setting.InputTitle)
		return
	end

	if "" == issue_content or " " == issue_content then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Setting.InputContent)
		return
	end
	local list = {}
	local vo = GameVoManager.Instance:GetMainRoleVo()
	list.zone_id = GLOBAL_CONFIG.local_package_info.config.agent_id
	list.server_id = GameVoManager.Instance:GetUserVo().plat_server_id
	list.user_id = vo.role_id
	list.role_id = vo.role_id
	list.role_name = vo.role_name
	list.role_level = vo.level
	list.role_gold = vo.gold
	list.role_scene = vo.scene_id
	list.issue_type = self.contact_gm_select_index
	list.issue_subject = issue_subject
	list.issue_content = issue_content
	SettingWGCtrl.Instance:SendRequest(list)

	self.node_list["title_text"].text.text = ""
	self.node_list["label_content"].text.text = ""

	self:CreateCountDown()
end

-----------------------------------------------------------------------------------------------------
function SettingView:CreateCountDown()
	if self.fsfk_count_down then
		CountDownManager.Instance:RemoveCountDown("fsfk_cds")
		self.fsfk_count_down = nil
	end
	SettingWGData.Instance:SetFSFKClickTime()
	self:SetFSFKTimeCd()
end

function SettingView:SetFSFKTimeCd()
	if self.fsfk_count_down then
		return
	end
	local fsfk_cds = SettingWGData.Instance:GetFSFKClickTime()
	local cd_s = fsfk_cds - Status.NowTime
	if cd_s > 0 then
		XUI.SetButtonEnabled(self.node_list["TlksBtn"], false)
		self.fsfk_count_down = CountDownManager.Instance:AddCountDown("fsfk_cds", function(elapse_time, total_time)
			local time = math.floor(total_time - elapse_time)
			if time <= 0 then
				if self.node_list["fs_times"] and self.node_list["btn_send"] then
					self.node_list["fs_times"].text.text = Language.Guild.Send
					XUI.SetButtonEnabled(self.node_list["btn_send"], true)
				end
				CountDownManager.Instance:RemoveCountDown("fsfk_cds")
				self.fsfk_count_down = nil
			else
				if self.node_list["fs_times"] then
					self.node_list["fs_times"].text.text = Language.Guild.Send .. string.format("(%s)", time)
					XUI.SetButtonEnabled(self.node_list["btn_send"], false)
				end
			end
		end,nil,nil,cd_s,0.1)
	else
		XUI.SetButtonEnabled(self.node_list["btn_send"], true)
	end
end

-----------------------------------------------------------------------------------------------------

function SettingView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.Tab and self.tabbar then
		local tab_index = math.floor(TabIndex[ui_param])
		if tab_index == self:GetShowIndex() then
			return NextGuideStepFlag
		else
			self:ShowIndex(tab_index)
			return
		end
	elseif ui_name == GuideUIName.SettingAddTime then
		return self.node_list["btn_add_time"], BindTool.Bind(self.OnClickAddTime, self)
	elseif ui_name == GuideUIName.SettingGuaJiRonglian then
		return self.node_list["layout_guaji_option" .. GUAJI_SETTING_TYPE.GUAJI_RONGLIAN], BindTool.Bind(self.OnClickGuijiSetting, self, GUAJI_SETTING_TYPE.GUAJI_RONGLIAN)
	end
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end


function SettingView:OnClickTlksBtn()
	local main_role = Scene.Instance:GetMainRole()
	if main_role and main_role:IsFightState() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Setting.FightNotTlks)
		return

	elseif SceneType.Common ~= Scene.Instance:GetSceneType() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Setting.SenceNotTlks)
		return
	end

	local func = function()
		if self.countdonw then
			CountDownManager.Instance:RemoveCountDown("tlks_cds")
			self.countdonw = nil
		end
		SettingWGData.Instance:SetTlksClickTime()
		self:SetTlksTimeCd()
		SettingWGCtrl.Instance:SendRoleReturnReAlivePosi()
	end
	local describe = Language.Setting.TlksDescribe
	TipWGCtrl.Instance:OpenAlertTips(describe, func)
end

function SettingView:SetTlksTimeCd()
	if self.countdonw and (not self.node_list["TlksBtn"]) then
		return
	end
	local tlks_cds = SettingWGData.Instance:GetTlksClickTime()
	local cd_s = tlks_cds - Status.NowTime
	if cd_s > 0 then
		XUI.SetButtonEnabled(self.node_list["TlksBtn"], false)
		self.countdonw = CountDownManager.Instance:AddCountDown("tlks_cds",function(elapse_time, total_time)
			local time = math.floor(total_time - elapse_time)
			if time <= 0 then
				if self.node_list["Tlkstime"] and self.node_list["TlksBtn"] then
					self.node_list["Tlkstime"].text.text = Language.Setting.TuoLi1
					XUI.SetButtonEnabled(self.node_list["TlksBtn"], true)
				end
				CountDownManager.Instance:RemoveCountDown("tlks_cds")
				self.countdonw = nil
			else
				if self.node_list["Tlkstime"] then
					local cd = TimeUtil.FormatSecond(time, 4)
					self.node_list["Tlkstime"].text.text = string.format(Language.Setting.TuoLi, cd)
				end
			end
		end, nil, nil, cd_s, 0.1)
	else
		XUI.SetButtonEnabled(self.node_list["TlksBtn"], true)
	end
end

function SettingView:ChangeHeadIcon()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local servername = LoginWGData.Instance:GetShowServerNameById(role_vo.server_id)
	self:GetRoleHeadCell()
	if self.node_list["name"] and self.node_list["fu"] then 
		self.node_list["name"].text.text = role_vo.name
		self.node_list["fu"].text.text = servername
		local appearance = role_vo and role_vo.appearance
		local data = {fashion_photoframe = appearance.fashion_photoframe}
		data.role_id = RoleWGData.Instance:InCrossGetOriginUid()
		data.prof = role_vo.prof
		data.sex = role_vo.sex
		data.is_show_main = true

		self.role_head_cell:SetImgBg(appearance and appearance.fashion_photoframe > 0)
		self.role_head_cell:SetData(data)
	end
end

function SettingView:GetRoleHeadCell()
	if not self.role_head_cell and self.node_list["head_pos"] then
		self.role_head_cell = BaseHeadCell.New(self.node_list["head_pos"])
	end
end

-------------------------------
-- DestroyPropRender
-------------------------------
DestroyPropRender = DestroyPropRender or BaseClass(BaseRender)
function DestroyPropRender:__init()
end

function DestroyPropRender:__delete()
end

function DestroyPropRender:LoadCallBack()
	self.node_list["img_btn_destroy1"]:SetActive(false)
end

function DestroyPropRender:OnFlush()
	if not self.data then return end
	self.node_list["lbl_type"].text.text = string.format(Language.Setting.FusionBtnText, self.data.btn_index)
end

function DestroyPropRender:CreateSelectEffect()
end
