﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;



public enum WeatherOrbitType
{
    /// <summary>
    /// Orbit as viewed from Earth (3D)
    /// </summary>
    FromEarth = 0,

    /// <summary>
    /// Orthographic xy plane
    /// </summary>
    OrthographicXY = 1,

    /// <summary>
    /// Orthographic xz plane
    /// </summary>
    OrthographicXZ = 2,

    /// <summary>
    /// Orbit is controlled by external script (orthographic)
    /// </summary>
    CustomOrthographic = 42,

    /// <summary>
    /// Orbit is controlled by external script (perspective)
    /// </summary>
    CustomPerspective = 43
}

/// <summary>
/// Blur shader type
/// </summary>
public enum BlurShaderType
{
    /// <summary>
    /// No blur
    /// </summary>
    None,

    /// <summary>
    /// Gaussian blur 7 tap
    /// </summary>
    GaussianBlur7,

    /// <summary>
    /// Gaussian blur 17 tap
    /// </summary>
    GaussianBlur17,

    /// <summary>
    /// Bilateral blur
    /// </summary>
    Bilateral
}


[ExecuteInEditMode]
public partial class YYLightManager : MonoBehaviour {

    [Header("Lights")]
    /// <summary>How intense is the scatter of directional light in the fog.</summary>
    [Tooltip("How intense is the scatter of directional light in the fog.")]
    [Range(0.0f, 100.0f)]
    public float FogDirectionalLightScatterIntensity = 2.0f;


    private static YYLightManager instance;


    private readonly List<YYCelestialObject> suns = new List<YYCelestialObject>();
    public IList<YYCelestialObject> Suns { get { return suns; } }
    /// <summary>
    /// Shared instance of light manager script
    /// </summary>
    public static YYLightManager Instance
    {
        get { return YYWeather.FindOrCreateInstance(ref instance, true); }
    }

    private YYCelestialObject sunPerspective;
    public YYCelestialObject SunPerspective
    {
        get
        {
            if (sunPerspective == null)
            {
                //sunPerspective = Suns[0];
                foreach (YYCelestialObject sun in Suns)
                {
                    if (sun != null && sun.OrbitTypeIsPerspective)
                    {
                        sunPerspective = sun;
                        break;
                    }
                }
            }
            return sunPerspective;
        }
    }

    //public static YYCelestialObject SunForCamera(Camera camera)
    //{
    //    if (Instance == null)
    //    {
    //        return null;
    //    }

    //    return (camera == null || !camera.orthographic ? Instance.SunPerspective : Instance.SunOrthographic);
    //}

    private void OnEnable()
    {
        if (YYCommandBufferManager.Instance != null)
        {
            // light manager pre-render is high priority as it sets a lot of global state
            YYCommandBufferManager.Instance.RegisterPreRender(CameraPreRender, this, true);
        }
    }

    private void CameraPreRender(Camera camera)
    {
        UpdateShaderVariables(camera, YYShaderProperties.Global, null);
    }

    private void Start()
    {
        UpdateAllLights();
    }

    private void LateUpdate()
    {
        Shader.SetGlobalFloat(WMS._WeatherMakerFogDirectionalLightScatterIntensity, FogDirectionalLightScatterIntensity);

        SetGlobalShaders();
    }

    public void UpdateShaderVariables(Camera camera, YYShaderProperties material, Collider collider)
    {
        if (camera == null && collider == null)
        {
            Debug.Log("Must pass camera or collider to UpdateShaderVariables method");
            return;
        }

        // 先放在 start 在后面再调整
        //if (!Application.isPlaying)
        //{
        //    UpdateAllLights();
        //}


        // add lights for each type
        SetLightsByTypeToShader(camera, material);


        ///
        SetShaderSunProperties(SunPerspective, material);


    }

    private void SetShaderSunProperties(YYCelestialObject sun, YYShaderProperties m)
    {
        // return;
        if (sun == null)
        {
            return;
        }

        Light sunLight = sun.Light;
        Vector3 sunPosition = sun.transform.position;
        Vector3 sunForward = sun.transform.forward;
        Vector3 sunForward2D = Quaternion.AngleAxis(-90.0f, Vector3.right) * sunForward;
        Vector4 sunColor = (new Vector4(sunLight.color.r, sunLight.color.g, sunLight.color.b, sunLight.intensity));
        Vector4 sunTintColor = new Vector4(sun.TintColor.r, sun.TintColor.g, sun.TintColor.b, sun.TintColor.a * sun.TintIntensity);
        float sunHorizonScaleMultiplier = Mathf.Clamp(Mathf.Abs(sunForward.y) * 3.0f, 0.5f, 1.0f);
        sunHorizonScaleMultiplier = Mathf.Min(1.0f, sun.Scale / sunHorizonScaleMultiplier);

        //Debug.Log("sunPosition is " + sunPosition);


        m.SetVector(WMS._WeatherMakerSunDirectionUp, -sunForward);
        m.SetVector(WMS._WeatherMakerSunDirectionUp2D, -sunForward2D);
        m.SetVector(WMS._WeatherMakerSunDirectionDown, sunForward);
        m.SetVector(WMS._WeatherMakerSunDirectionDown2D, sunForward2D);
        m.SetVector(WMS._WeatherMakerSunPositionNormalized, sunPosition.normalized);
        m.SetVector(WMS._WeatherMakerSunPositionWorldSpace, sunPosition);
        m.SetVector(WMS._WeatherMakerSunColor, sunColor);
        m.SetVector(WMS._WeatherMakerSunTintColor, sunTintColor);
        m.SetVector(WMS._WeatherMakerSunLightPower, new Vector4(sun.LightPower, sun.LightMultiplier, sunLight.shadowStrength, 1.0f - sunLight.shadowStrength));


        //Vector4 v4 = new Vector4(sunHorizonScaleMultiplier, Mathf.Pow(sunLight.intensity, 0.5f), Mathf.Pow(sunLight.intensity, 0.75f), sunLight.intensity * sunLight.intensity);
        //Debug.Log("_WeatherMakerSunVar1 " + v4);


        m.SetVector(WMS._WeatherMakerSunVar1, new Vector4(sunHorizonScaleMultiplier, Mathf.Pow(sunLight.intensity, 0.5f), Mathf.Pow(sunLight.intensity, 0.75f), sunLight.intensity * sunLight.intensity));

    }





}
