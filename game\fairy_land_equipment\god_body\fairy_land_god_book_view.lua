function FairyLandEquipmentView:InitGodBookView()
    self.cur_select_page = -1
    self.old_select_page = -1
    self.gb_old_select_slot = -1

    self.page_chip_list = {}
    local list_node = self.node_list["chip_list"]
    local chip_num = list_node.transform.childCount
    for i = 0, chip_num - 1 do
        self.page_chip_list[i] = GBPageChipRender.New(list_node:FindObj("chip_" .. i))
        self.page_chip_list[i]:SetIndex(i)
    end

    self.book_small_list = {}
    local small_num = self.node_list.book_small_list.transform.childCount
    for i = 0, small_num - 1 do
        self.book_small_list[i] = GBSmallRender.New(self.node_list.book_small_list:FindObj("small_" .. i))
        self.book_small_list[i]:SetIndex(i)
        self.book_small_list[i]:SetClickCallBack(BindTool.Bind(self.SmallJumpTo, self))
    end

    -- 属性列表
    if not self.book_attr_list then
        self.book_attr_list = {}
        local parent_node = self.node_list["god_book_attr_list"]
        local attr_num = parent_node.transform.childCount
        for i = 1, attr_num do
            local cell = CommonAddAttrRender.New(parent_node:FindObj("attr_" .. i))
            cell:SetAttrNameNeedSpace(true)
            cell:SetIndex(i)
            self.book_attr_list[i] = cell
        end
    end

    XUI.AddClickEventListener(self.node_list.btn_gb_goto_gather, BindTool.Bind(self.GoToGatherChip, self))
    --XUI.AddClickEventListener(self.node_list.btn_rule_tips, BindTool.Bind(self.OpenPlayTips, self))
    --XUI.AddClickEventListener(self.node_list.btn_act_page, BindTool.Bind(self.ClickActPage, self))
    XUI.AddClickEventListener(self.node_list.btn_act_god_body, BindTool.Bind(self.ClickActGodBody, self))
    XUI.AddClickEventListener(self.node_list.btn_book_get, BindTool.Bind(self.ClickGetBook, self))
end

function FairyLandEquipmentView:DeleteGodBookView()
    if self.page_chip_list then
        for k, v in pairs(self.page_chip_list) do
            v:DeleteMe()
        end
        self.page_chip_list = nil
    end

    if self.book_small_list then
        for k, v in pairs(self.book_small_list) do
            v:DeleteMe()
        end
        self.book_small_list = nil
    end

    if self.book_attr_list then
        for k,v in pairs(self.book_attr_list) do
            v:DeleteMe()
        end
        self.book_attr_list = nil
    end

    self.cur_select_page = nil
    self.old_select_page = nil
    self.gb_old_select_slot = nil
    self.old_book_progress = nil
    self.old_read_finish_callback = nil
end

function FairyLandEquipmentView:FlushGodBookView()
    local data = self:GetCurSelectSlotData()
    if data == nil then
        return
    end

    local fle_data = FairyLandEquipmentWGData.Instance
    local slot = data:GetSlotIndex()
    local slot_cfg = FairyLandEquipmentWGData.Instance:GetGodBodyCfg(slot)

    -- 属性
    local attr_list = fle_data:GetGBAllActAttrList(slot)
    for k,v in ipairs(self.book_attr_list) do
        v:SetData(attr_list[k])
    end

    -- 战力
    self.node_list.gb_book_cap.text.text = fle_data:GetGBAllActAttrCap(slot)
    --灵体名字
    if slot_cfg then
        self.node_list["book_title_text"].text.text = slot_cfg.short_name
    end
end

function FairyLandEquipmentView:FlushGodBookChip()
    local data = self:GetCurSelectSlotData()
    if data == nil then
        return
    end

    local fle_data = FairyLandEquipmentWGData.Instance
    local slot = data:GetSlotIndex()
    local page = self.cur_select_page
    local is_act = data:GetIsAct()

    -- 小天书
    for k, v in pairs(self.book_small_list) do
        v:SetData({slot = slot, cur_page = page})
    end

    -- 激活按钮
    local act_body_remind = fle_data:GetGodBodyActRemind(slot)
    local act_page_remind = fle_data:GetPageActRemind(slot, page)
    --self.node_list.btn_act_page:SetActive(act_page_remind and not act_body_remind)
    self.node_list["btn_act_god_body_remind"]:SetActive(act_page_remind)
    self.node_list.lingti_state_text.text.text = is_act == true and Language.FairyLandEquipment.IsActiveDes or Language.FairyLandEquipment.NotActiveDes
    -- XUI.SetButtonEnabled(self.node_list.btn_act_god_body, is_act or act_body_remind)
    
    local need_data = {slot = slot, page = page}
    if self.page_chip_list ~= nil then
        for k, v in pairs(self.page_chip_list) do
            v:SetData(need_data)
        end
    end
end

function FairyLandEquipmentView:FlushChipList()
    local data = self:GetCurSelectSlotData()
    if data == nil then
        return
    end

    local slot = data:GetSlotIndex()
    local page = self.cur_select_page
    local need_data = {slot = slot, page = page}
    if self.page_chip_list ~= nil then
        for k, v in pairs(self.page_chip_list) do
            v:SetData(need_data)
        end
    end
end

function FairyLandEquipmentView:JumpToPage(page)
    local slot = self:GetCurSelectSlot()
    local max_page = FairyLandEquipmentWGData.Instance:GetActBodyNeedNum()
    max_page = max_page - 1
    page = page > 0 and page or 0
    page = page < max_page and page or max_page
    local new_page_state = FairyLandEquipmentWGData.Instance:GetPageActState(slot, page)
    if new_page_state == GOODS_STATE_TYPE.UNACT then
        local need_act_page = page - 1
        need_act_page = need_act_page > 0 and need_act_page or 0
        local page_cfg = FairyLandEquipmentWGData.Instance:GetGodBookCfg(need_act_page)
        TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.FairyLandEquipment.ClickGodBodyLimitDesc1, page_cfg and page_cfg.page_name or ""))
        return
    end

    if self.gb_old_select_slot == slot and self.cur_select_page == page then
        self:FlushGodBookChip()
        return
    end

    self.cur_select_page = page
    self.old_select_page = page
    self:FlushGodBookChip()
    self.gb_old_select_slot = slot
end

function FairyLandEquipmentView:SmallJumpTo(cell)
    if cell == nil then
        return
    end

    self:JumpToPage(cell.index)
end

function FairyLandEquipmentView:GoToGatherChip()
    ViewManager.Instance:Open(GuideModuleName.WorldServer, FunName.XianJieBoss)
end

-- 激活书页
-- function FairyLandEquipmentView:ClickActPage()
--     FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.ACT_PAGE, self:GetCurSelectSlot(), self.cur_select_page)
-- end

function FairyLandEquipmentView:ClickGetBook()
    ViewManager.Instance:Open(GuideModuleName.WorldServer, FunName.XianJieBoss)
end

-- 激活书页或神体
function FairyLandEquipmentView:ClickActGodBody()
    local data = self:GetCurSelectSlotData()
    if data == nil then
        return
    end

    local slot = data:GetSlotIndex()
    local page = self.cur_select_page
    local is_act = data:GetIsAct()

    -- 激活按钮
    local act_body_remind = FairyLandEquipmentWGData.Instance:GetGodBodyActRemind(slot)
    local act_page_remind = FairyLandEquipmentWGData.Instance:GetPageActRemind(slot, page)
    if act_page_remind and not act_body_remind then
        FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.ACT_PAGE, self:GetCurSelectSlot(), self.cur_select_page)
    else
        if is_act then
            TipWGCtrl.Instance:ShowSystemMsg(Language.FairyLandEquipment.AlreadyActBodyDesc)
            return
        end

        local page_state = FairyLandEquipmentWGData.Instance:GetPageActState(slot, page)

        if (page_state == GOODS_STATE_TYPE.UNACT or page_state == GOODS_STATE_TYPE.READY_ACT) and not act_page_remind then
            TipWGCtrl.Instance:ShowSystemMsg(Language.FairyLandEquipment.NotActPageDesc)
            return
        end

        if page_state == GOODS_STATE_TYPE.NORMAL then
            TipWGCtrl.Instance:ShowSystemMsg(Language.FairyLandEquipment.AlreadyActPageDesc)
            return
        end

        if not act_body_remind then
            TipWGCtrl.Instance:ShowSystemMsg(Language.FairyLandEquipment.NotActBodyDesc)
            return
        end
        -- local max_page_num = FairyLandEquipmentWGData.Instance:GetActBodyNeedNum()
        FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.ACT_BODY, self:GetCurSelectSlot())--, max_page_num - 1)
    end
end