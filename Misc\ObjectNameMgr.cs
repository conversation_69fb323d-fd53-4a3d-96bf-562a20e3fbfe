﻿using Nirvana;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ObjectNameMgr : Nirvana.Singleton<ObjectNameMgr>
{
    private const int MAX_SLOT_SIZE = 8192;

    private Dictionary<int, int> slotIndexDic = new Dictionary<int, int>(MAX_SLOT_SIZE);
    private string[] nameSlots = new string[MAX_SLOT_SIZE];
    private int curSlotIndex = 0;

    public string GetObjectName(Object obj)
    {
        int instanceId = obj.GetInstanceID();
        int slotIndex;
        if (!slotIndexDic.TryGetValue(instanceId, out slotIndex))
        {
            slotIndex = curSlotIndex;
            slotIndexDic.Add(instanceId, slotIndex);
            nameSlots[curSlotIndex++] = obj.name;
            if (curSlotIndex >= MAX_SLOT_SIZE)
            {
                slotIndexDic.Clear();
                curSlotIndex = 0;
            }
        }

        return nameSlots[slotIndex];
    }

    public void SetObjectName(Object obj, string name)
    {
        int instanceId = obj.GetInstanceID();
        int slotIndex;
        if (slotIndexDic.TryGetValue(instanceId, out slotIndex))
        {
            nameSlots[slotIndex] = name;
        }

        obj.name = name;
    }
}
