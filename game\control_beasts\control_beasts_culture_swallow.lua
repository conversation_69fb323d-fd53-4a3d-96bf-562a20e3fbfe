-- 灵兽培养吞噬转换资质
ControlBeastsCultureSwallow = ControlBeastsCultureSwallow or BaseClass(SafeBaseView)

function ControlBeastsCultureSwallow:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(702, 484)})
    self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_beasts_root_culture_swallow")
    self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_beasts_effect_center_view")
    self:SetMaskBg(true)
end

function ControlBeastsCultureSwallow:ReleaseCallBack()
    self.beast_bag_id = nil
    self.need_arrange = nil

    if self.left_show_item then
        self.left_show_item:DeleteMe()
        self.left_show_item = nil
    end
    
    if self.spend_item then
        self.spend_item:DeleteMe()
        self.spend_item = nil
    end

    if self.spend_flair_item then
        self.spend_flair_item:DeleteMe()
        self.spend_flair_item = nil
    end

    if self.flair_attrlist and #self.flair_attrlist > 0 then
		for _, flair__cell in ipairs(self.flair_attrlist) do
			flair__cell:DeleteMe()
			flair__cell = nil
		end

		self.flair_attrlist = nil
	end

    if self.alert_tip then
		self.alert_tip:DeleteMe()
		self.alert_tip = nil
	end	

    self.select_list = nil
    self.old_flair_score = nil
    self:RemoveCurrDelayTimer()
end

function ControlBeastsCultureSwallow:SetSelectData(beast_bag_id)
    self.beast_bag_id = beast_bag_id
end

function ControlBeastsCultureSwallow:CloseCallBack()
    -- 关闭的时候整理一下背包
    -- if self.need_arrange then
    --     ControlBeastsWGCtrl.Instance:SendOperateTypeSortBeastBag()
    -- end

    self.old_flair_score = nil
    self.select_list = nil

    if self.flair_attrlist and #self.flair_attrlist > 0 then
		for _, flair__cell in ipairs(self.flair_attrlist) do
			flair__cell:ResetOldData()
		end
	end
end

function ControlBeastsCultureSwallow:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.ContralBeasts.TitleName6

    -- 资质属性
    if self.flair_attrlist == nil then
        self.flair_attrlist = {}
        for i = 1, 6 do
            local attr_obj = self.node_list.layout_flair_attr:FindObj(string.format("attr_%d", i))
            if attr_obj then
                local cell = BeststSwallowFlairItemRender.New(attr_obj)
                cell:SetIndex(i)
                self.flair_attrlist[i] = cell
            end
        end
    end

    if not self.left_show_item then
        self.left_show_item = BeststsSwallowRender.New(self.node_list.left_item_cell)
    end

    if not self.spend_item then
        self.spend_item = BeststsSwallowRender.New(self.node_list.spend_item_cell)
        self.spend_item:SetClickCallBack(BindTool.Bind1(self.OnClickSelectBestst, self))
    end

    if not self.spend_flair_item then
        self.spend_flair_item = ItemCell.New(self.node_list.spend_flair_item_cell)
    end

    self.node_list["btn_OK"].button:AddClickListener(BindTool.Bind(self.OnClickSwallow, self))
    XUI.AddClickEventListener(self.node_list.flair_item_add_btn, BindTool.Bind2(self.OperateUseItem, self, 1))    
    XUI.AddClickEventListener(self.node_list.flair_item_minus_btn, BindTool.Bind2(self.OperateUseItem, self, -1))                    	-- 图鉴
end

function ControlBeastsCultureSwallow:OnClickSelectBestst()
    if not self.beast_bag_id then
        return
    end

    local left_show_data = ControlBeastsWGData.Instance:GetBeastDataById(self.beast_bag_id)
    if left_show_data and left_show_data.server_data then
        local server_data = left_show_data.server_data
        local show_data = {}
        show_data.aim_beast_id = server_data.beast_id
        show_data.aim_bag_id = self.beast_bag_id
        show_data.special_list = self.select_list
        show_data.same_list = nil
        show_data.is_special = false
        show_data.is_all = true
        show_data.need_num = 10000

        ControlBeastsWGCtrl.Instance:SetSelectSwallowSelectOkCallBack(BindTool.Bind1(self.OnBatchOkCallBack, self))
        ControlBeastsWGCtrl.Instance:OpenBeastsSwallowSelectView(show_data)
    end
end

-- 材料选择回调
function ControlBeastsCultureSwallow:OnBatchOkCallBack(is_special, select_list)
    self.select_list = select_list

    self:RefreshBeastFlairList()
    self:RefreshSelectBeast(true, self.select_num)
end

function ControlBeastsCultureSwallow:OnFlush()
    self:RefreshBeastMessage()
    self:RefreshBeastFlairList()
    self:RefreshUseBeastStarItemFlairNum()
    self:RefreshSelectBeast(true, self.select_num)
end

-- 刷新左侧驭兽信息
function ControlBeastsCultureSwallow:RefreshBeastMessage()
    if not self.beast_bag_id then
        return
    end

    local left_show_data = ControlBeastsWGData.Instance:GetBeastDataById(self.beast_bag_id)
    if left_show_data then
        self.left_show_item:SetData(left_show_data)
    end

    if left_show_data and left_show_data.server_data and left_show_data.server_data.beast_id then
        self.node_list.spend_flair_item_root:CustomSetActive(true)
        self.node_list.line_flair_item:CustomSetActive(true)
        local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(left_show_data.server_data.beast_id)
        if beast_cfg then
            local per_data = ControlBeastsWGData.Instance:GetBeastStarItemFlairPerByStar(beast_cfg.beast_star)
            self.spend_flair_item:SetData({item_id = per_data.item_id or 0})
        end
    else
        self.node_list.spend_flair_item_root:CustomSetActive(false)
        self.node_list.line_flair_item:CustomSetActive(false)
    end
end

-- 刷新数量
function ControlBeastsCultureSwallow:RefreshUseBeastStarItemFlairNum()
    if not self.select_num then
        self.select_num = 0
    end

    self.node_list.flair_item_num.text.text = self.select_num
end

-- 刷新资质列表
function ControlBeastsCultureSwallow:RefreshBeastFlairList()
    if not self.beast_bag_id then
        return
    end

    local left_show_data = ControlBeastsWGData.Instance:GetBeastDataById(self.beast_bag_id)
    if left_show_data then
        self.left_show_item:SetData(left_show_data)
        local flair_score = 0

        local server_data = left_show_data.server_data
        if server_data and server_data.flair_values then

            local beast_map_data = ControlBeastsWGData.Instance:GetBeastDatMapaById(server_data.beast_id)
            local beast_flair_preview = nil
            if beast_map_data and beast_map_data.beast_flair_preview then
                beast_flair_preview = beast_map_data.beast_flair_preview
            end
    
            -- 资质刷新
            if server_data.flair_values then
                for index, bag_flair_cell in ipairs(self.flair_attrlist) do
                    local temp_data = {}
                    temp_data.beast_preview_value = beast_flair_preview and beast_flair_preview[index] and beast_flair_preview[index].max or 0
    
                    if server_data.flair_values[index] then
                        flair_score = flair_score + server_data.flair_values[index]
                        temp_data.cur_value = server_data.flair_values[index]
                        bag_flair_cell:SetData(temp_data)
                    else
                        temp_data.cur_value = server_data.effort_value / 10000
                        bag_flair_cell:SetData(temp_data)    -- 成长值
                    end
                end
            end

            flair_score = math.floor(flair_score * (server_data.effort_value / 10000)) 
            -- 计算资质评级
            local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)

            if beast_cfg then
                local score_index = ControlBeastsWGData.Instance:GetFlairScoreByScore(beast_cfg.beast_star, flair_score)
                local temp_str = ""
                if self.old_flair_score then
                    if flair_score - self.old_flair_score > 0 then
                        local add_score = flair_score - self.old_flair_score
                        temp_str = ToColorStr(string.format("+%d", add_score), COLOR3B.GREEN)
                        temp_str = string.format("%s%s", string.format(Language.ContralBeasts.AttrTitle5, self.old_flair_score), temp_str)
                        self.node_list.flair_txt.text.text = temp_str
    
                        self:RemoveCurrDelayTimer()
                        self.show_timer = GlobalTimerQuest:AddDelayTimer(function ()
                            self.node_list.flair_txt.text.text = string.format(Language.ContralBeasts.AttrTitle5, flair_score) 
                            self.old_flair_score = flair_score
                        end, 2)
                    end
                else
                    self.node_list.flair_txt.text.text = string.format(Language.ContralBeasts.AttrTitle5, flair_score) 
                    self.old_flair_score = flair_score
                end
        
                local battle_type_str = string.format("a3_hs_bs_pz%d", score_index) 
                local bundle, asset = ResPath.GetControlBeastsImg(battle_type_str)
                self.node_list.flair_pz.image:LoadSprite(bundle, asset)
            end
        end
    end
end

--移除回调
function ControlBeastsCultureSwallow:RemoveCurrDelayTimer()
    if self.flair_show_timer then
        GlobalTimerQuest:CancelQuest(self.flair_show_timer)
        self.flair_show_timer = nil
    end
end

-- 刷新选中数据
function ControlBeastsCultureSwallow:RefreshSelectBeast(is_set_str, cur_select_num)
    local pro = 0
    local aim_star = 0

    local left_show_data = ControlBeastsWGData.Instance:GetBeastDataById(self.beast_bag_id)
    if left_show_data then
        if left_show_data and left_show_data.server_data then
            local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(left_show_data.server_data.beast_id)
            if beast_cfg then
                aim_star = beast_cfg.beast_star
            end
        end
    end

    if self.select_list and #self.select_list > 0 and aim_star ~= 0 then
        local temp_data = self.select_list[1]
        if temp_data then
            self.spend_item:SetData(temp_data)
        end

        for _, temp_data in ipairs(self.select_list) do
            if temp_data and temp_data.beast_data and temp_data.beast_data.server_data then
                local server_data = temp_data.beast_data.server_data
                local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
                if beast_cfg then
                    local temp_por = ControlBeastsWGData.Instance:GetBeastStarFlairPerByStar(aim_star, beast_cfg.beast_star)
                    pro = pro + temp_por / 100
                end
            end
        end
    else
        self.spend_item:SetData({})
    end

    if aim_star ~= 0 then
        local per_data = ControlBeastsWGData.Instance:GetBeastStarItemFlairPerByStar(aim_star)
        local item_per = cur_select_num * (per_data and per_data.per_num or 0)
        pro = pro + item_per
    end

    local _, decimal = math.modf(pro)
    local str = tostring(pro)
    if decimal > 0 then
        str = string.format(string.format("%.3f", pro))
    end

    if is_set_str then
        self.node_list.swallow_pro.text.text = string.format(Language.ContralBeasts.CultureText3, str)
    end

    return pro
end

-- 吞噬
function ControlBeastsCultureSwallow:OnClickSwallow()
    if not self.beast_bag_id then
        return
    end

    if (self.select_list and #self.select_list > 0) or self.select_num ~= 0 then
        local execute_swalow = function()
            local beast_list = {}

            if self.select_list then
                for _, temp_data in ipairs(self.select_list) do
                    if temp_data and temp_data.beast_data then
                        local beast_data = temp_data.beast_data
                        table.insert(beast_list, beast_data.bag_id)
                    end
                end
            end
    
            self.need_arrange = true
            self.select_list = nil
            ControlBeastsWGCtrl.Instance:SendCSRoleBeastChangeFlair(self.beast_bag_id, beast_list, self.select_num)
            self.select_num = nil
        end

        local is_need_alert = false
        if self.select_list then
            for _, temp_data in ipairs(self.select_list) do
                if temp_data and temp_data.beast_data then
                    local beast_data = temp_data.beast_data
                    local data = ControlBeastsWGData.Instance:GetBeastDataById(beast_data.bag_id)
                    local server_data = data.server_data
                    if server_data then
                        local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
                        is_need_alert = beast_cfg and beast_cfg.beast_color >= GameEnum.ITEM_COLOR_ORANGE
                        if is_need_alert == true then
                            break
                        end
                    end
                end
            end
        end

        if is_need_alert then
            -- 弹出二次确认提示框
            if self.alert_tip == nil then
                self.alert_tip = Alert.New()
            end
            self.alert_tip:SetLableString(Language.ContralBeasts.SelectTips4)
            self.alert_tip:SetOkFunc(execute_swalow)
            self.alert_tip:Open()
        else
            execute_swalow()
        end
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip13)
    end
end

-- 吞噬道具数量选择
function ControlBeastsCultureSwallow:OperateUseItem(num)
    if not self.select_num then
        self.select_num = 0
    end

    self.select_num = self.select_num + num

    if self.select_num <= 0 then
        self.select_num = 0
        self:RefreshSelectBeast(true, self.select_num)
        self:RefreshUseBeastStarItemFlairNum()
        return
    end

    local left_show_data = ControlBeastsWGData.Instance:GetBeastDataById(self.beast_bag_id)
    if left_show_data and left_show_data.server_data and left_show_data.server_data.beast_id then
        local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(left_show_data.server_data.beast_id)
        if beast_cfg then
            local per_data = ControlBeastsWGData.Instance:GetBeastStarItemFlairPerByStar(beast_cfg.beast_star)
            local item_id = per_data.item_id or 0
            local item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)

            if self.select_num > item_num then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.Prop_No_Enough)
                self.select_num = item_num
                return
            end
        end
    end

    local pro = self:RefreshSelectBeast(false, self.select_num - 1)
    if pro >= 100 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip19)
        self.select_num = self.select_num - 1
    end

    self:RefreshSelectBeast(true, self.select_num)
    self:RefreshUseBeastStarItemFlairNum()
end

-- 升级成功或突破成功特效
function ControlBeastsCultureSwallow:BeastOperateFinalEffect(ui_effect_type, is_center)
	local node_root = self.node_list["operate_effect_root"]
	if is_center then
		node_root = self.node_list["layout_a2_common_top_panel"]
	end
	TipWGCtrl.Instance:ShowEffect({effect_type = ui_effect_type,
						is_success = true, pos = Vector2(0, 0), parent_node = node_root})

	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
end

-----------------------------------------------------------------------------------------------------

----------------------------------灵兽已孵化培养item-----------------------
BeststsSwallowRender = BeststsSwallowRender or BaseClass(BaseRender)
function BeststsSwallowRender:LoadCallBack()
    if not self.beast_item then
		self.beast_item = ItemCell.New(self.node_list.item_pos)
        self.beast_item:SetIsShowTips(false)
        self.beast_item:SetClickCallBack(BindTool.Bind(self.OnClick, self))
	end
end

function BeststsSwallowRender:__delete()
	if self.beast_item then
		self.beast_item:DeleteMe()
		self.beast_item = nil
	end
end

function BeststsSwallowRender:OnClick()
    BaseRender.OnClick(self)
end

function BeststsSwallowRender:OnFlush()
    if not self.data then return end

	if IsEmptyTable(self.data) then
        if self.node_list.show_root then
            self.node_list.show_root:SetActive(false)
            self.node_list.add_btn:SetActive(true)
        end
		return
    else
        if self.node_list.show_root then
            self.node_list.show_root:SetActive(true)
            self.node_list.add_btn:SetActive(false)
        end
	end

    local beast_data = self.data.beast_data

    if beast_data == nil then   -- 不为空的是选择回来的
        beast_data = self.data
    end

    if beast_data and beast_data.server_data then
        local server_data = beast_data.server_data
        self.beast_item:SetData({item_id = beast_data.server_data.beast_id, is_beast = not self.data.is_egg})---这里需要其他的东西在加，看策划需求
    end
end

