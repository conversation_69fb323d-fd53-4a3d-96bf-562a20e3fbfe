--boss_ctrl 太多行，仙界boss相关分出来
require("game/worldserver/xianjie_boss_wg_data")
require("game/worldserver/xianjie_enter_consume_view")
require("game/worldserver/xianjie_boss_tired_view")
require("game/worldserver/xianjie_boss_revenge_view")
require("game/worldserver/xianjie_boss_getitem_show")
require("game/worldserver/xianjie_boss_equip_show")

BossWGCtrl = BossWGCtrl or BaseClass(BaseWGCtrl)
function BossWGCtrl:InitXianjieWGCtrl()
    self.xianjie_data = XianJieBossWGData.New()
    self.xianjie_enter_consume_view = XianJieEnterConsumeView.New()
    self.xianjie_boss_tired_view = XianJieBossTiredView.New()
    self.xianjie_boss_revenge_view = XianJieBossRevengeView.New()
    self.xianjie_boss_getitem_show = XianJieBossGetItemShow.New()
    self.xianjie_boss_equip_show = XianJieBossEquipShow.New()

    self:RegisterXianJieProtocols()
end

function BossWGCtrl:RegisterXianJieProtocols()
    self:RegisterProtocol(CSCrossFairylandBossOperate)
	self:RegisterProtocol(SCCrossFairylandBossInfo, "OnSCCrossFairylandBossInfo")
    self:RegisterProtocol(SCCrossFairylandBossInfoUpdate, "OnSCCrossFairylandBossInfoUpdate")
    self:RegisterProtocol(SCCrossFairylandBossDropHistory, "OnSCCrossFairylandBossDropHistory")
    self:RegisterProtocol(SCCrossFairylandBossDropHistoryAdd, "OnSCCrossFairylandBossDropHistoryAdd")
    self:RegisterProtocol(SCCrossFairylandBossForenotice, "OnSCCrossFairylandBossForenotice")
    self:RegisterProtocol(SCCrossFairylandBossSceneInfo, "OnSCCrossFairylandBossSceneInfo")
    self:RegisterProtocol(SCCrossFairylandBaseInfo, "OnSCCrossFairylandBaseInfo")
    self:RegisterProtocol(SCCrossFairylandBossPickFallingItem, "OnSCCrossFairylandBossPickFallingItem")
    
    self:RegisterProtocol(SCCrossFairylandBossBeKillRecordInfo, "OnSCCrossFairylandBossBeKillRecordInfo")
    self:RegisterProtocol(SCCrossFairylandBossBeKillRecordAdd, "OnSCCrossFairylandBossBeKillRecordAdd")
    self:RegisterProtocol(SCCrossFairylandBossBeKillRecordRemove, "OnSCCrossFairylandBossBeKillRecordRemove")
end

function BossWGCtrl:DeleteXianjieWGCtrl()
    if self.xianjie_data then
        self.xianjie_data:DeleteMe()
        self.xianjie_data = nil
    end
    if self.xianjie_enter_consume_view then
        self.xianjie_enter_consume_view:DeleteMe()
        self.xianjie_enter_consume_view = nil
    end

    if self.xianjie_boss_tired_view then
        self.xianjie_boss_tired_view:DeleteMe()
        self.xianjie_boss_tired_view = nil
    end

    if self.xianjie_boss_revenge_view then
        self.xianjie_boss_revenge_view:DeleteMe()
        self.xianjie_boss_revenge_view = nil
    end

    if self.xianjie_boss_getitem_show then
        self.xianjie_boss_getitem_show:DeleteMe()
        self.xianjie_boss_getitem_show = nil
    end
    
    if self.xianjie_boss_equip_show then
        self.xianjie_boss_equip_show:DeleteMe()
        self.xianjie_boss_equip_show = nil
    end

    if nil ~= self.remind_alert then
		self.remind_alert:DeleteMe()
		self.remind_alert = nil
    end
    
    if nil ~= self.remind_enter_alert then
		self.remind_enter_alert:DeleteMe()
		self.remind_enter_alert = nil
	end
end

function BossWGCtrl:GoToKillXianjie(boss_id, layer)
    local cfg = XianJieBossWGData.Instance:GetBossCfgById(boss_id)
    local slot_limit, slot_page_limit = cfg.slot_limit, cfg.slot_page_limit
    local cur_slot, cur_page = XianJieBossWGData.Instance:GetCurSlotAndPage()

    local is_high = false
    if cur_slot > slot_limit then
        is_high = true
    elseif cur_slot == slot_limit and cur_page > slot_page_limit then
        is_high = true
    end
    local high_str = is_high and "\n"..Language.XianJieBoss.CurStageTooHigh or ""
    --您已集齐当前层全部碎片，可激活并前往更高的层，是否依然前往
    local can_active = XianJieBossWGData.Instance:GetCurPageCanActive()
    local active_str = can_active and "\n"..Language.XianJieBoss.CurStageCanActive or ""
    local str = ""
    if can_active then
        str = active_str
    elseif is_high then
        str = high_str
    end

    local ok_func = function()
        BossWGData.Instance:SetCurSelectBossID(0 , 0, boss_id)
        BossWGCtrl.Instance:SendXianJieBossReq(CROSS_FAIRYLAND_BOSS_OPERATE_TYPE.CROSS_FAIRYLAND_BOSS_OPERATE_TYPE_ENTER_SCENE, layer)
    end
    local remain_time, total_time = XianJieBossWGData.Instance:GetXianjieEnterInfo()
    if remain_time <= 0 then --免费次数
        self.xianjie_enter_consume_view:SetEnterBossComsunData(boss_id, ok_func)
    else
        if not is_high and not can_active then
            ok_func()
        else
            if nil == self.remind_alert then
                self.remind_alert = Alert.New()
            end
            self.remind_alert:SetOkFunc(ok_func)
            self.remind_alert:SetLableString(ToColorStr(Language.XianJieBoss.EnterXianjieTip, COLOR3B.RED) ..str)  
            self.remind_alert:SetLableRectWidth(590)
            self.remind_alert:Open()
        end
    end
end

function BossWGCtrl:SendXianJieBossReq(type1, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossFairylandBossOperate)
	protocol.operate_type = type1
	protocol.param1 = param1 or 0
    protocol.param2 = param2 or 0
    protocol.param3 = param3 or 0
    --print_log("SendXianJieBossReq",protocol.operate_type, protocol.param1, protocol.param2, protocol.param3 )
	protocol:EncodeAndSend()
end

function BossWGCtrl:OnSCCrossFairylandBossInfo(protocol)
    self.xianjie_data:SaveBossInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.xianjie_boss)
end

function BossWGCtrl:OnSCCrossFairylandBossInfoUpdate(protocol)
    self.xianjie_data:SaveBossInfoUpdate(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.xianjie_boss)
end

function BossWGCtrl:OnSCCrossFairylandBossDropHistory(protocol)
    self.xianjie_data:SaveBossDropHistory(protocol)
end

function BossWGCtrl:OnSCCrossFairylandBossDropHistoryAdd(protocol)
    self.xianjie_data:AddBossDropHistory(protocol)
end

function BossWGCtrl:OnSCCrossFairylandBossForenotice(protocol)
    --print_log("OnSCCrossFairylandBossForenotice",protocol)
    local data = {}
    data.boss_id = protocol.boss_id
    data.boss_type = COMMON_CONSTS.XianJieBossIdx
    --data.level = protocol.level
    data.scene_type = SceneType.XianJie_Boss
    self.boss_forenotice_view:SetShowData(data)
    self.boss_forenotice_view:Open()
end


function BossWGCtrl:OnSCCrossFairylandBossSceneInfo(protocol)
    --print_log("OnSCCrossFairylandBossSceneInfo",protocol)
    self.xianjie_data:SetXianJieTiredValue(protocol)
    if Scene.Instance:GetSceneType() == SceneType.XianJie_Boss then
        MainuiWGCtrl.Instance:FlushView(0, "flush_xianjie_nuqi")
        local mainrole = Scene.Instance:GetMainRole()
        if mainrole and mainrole:GetVo() then
            local area = mainrole:GetVo().area_index
            if area ~= protocol.area_camp then
                AddDelayCall(self, function()
                    local mainrole = Scene.Instance:GetMainRole()
                    if mainrole then
                        mainrole:SetAttr("area_index", protocol.area_camp)
                        self:ReloadAllAreaIndexEquipImg()
                    end
                end, 0.1) --延迟防止断线重连数据没设置进去
            end
        end
    end
end

function BossWGCtrl:OpenXianJieTiredView()
    self.xianjie_boss_tired_view:Open()
end

function BossWGCtrl:CloseXianJieTiredView()
    self.xianjie_boss_tired_view:Close()
end

function BossWGCtrl:GetXianJieTiredView()
    return self.xianjie_boss_tired_view
end

function BossWGCtrl:FlushXianJieTiredView()
    if self.xianjie_boss_tired_view:IsOpen() then
        self.xianjie_boss_tired_view:Flush()
    end
end

function BossWGCtrl:GetXianJieEquipView()
    return self.xianjie_boss_equip_show
end

--进入次数
function BossWGCtrl:SetXianJieEnterTimes(times)
   self.xianjie_data:SetXianJieEnter(times)
   ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.xianjie_boss)
   RemindManager.Instance:Fire(RemindName.WorldServer_Xianjie)
end

function BossWGCtrl:RequestXianJieBossInfo()
    local cur_max_layer = XianJieBossWGData.Instance:GetMaxLayer()
    for i = 1, cur_max_layer do
        self:SendXianJieBossReq(CROSS_FAIRYLAND_BOSS_OPERATE_TYPE.CROSS_FAIRYLAND_BOSS_OPERATE_TYPE_BOSS_INFO, i)
    end
end

function BossWGCtrl:RequestXianJieBaseInfo()
    self:SendXianJieBossReq(CROSS_FAIRYLAND_BOSS_OPERATE_TYPE.CROSS_FAIRYLAND_BOSS_OPERATE_TYPE_BASE_INFO)
end

function BossWGCtrl:OnSCCrossFairylandBaseInfo(protocol)
    self.xianjie_data:SaveBaseInfo(protocol)
end

function BossWGCtrl:OpenXianJieRevengeView()
    self.xianjie_boss_revenge_view:Open()
end

function BossWGCtrl:CloseXianJieRevengeView()
    self.xianjie_boss_revenge_view:Close()
end


--  装备掉落相关  ----
function BossWGCtrl:ShowGetXianJieEquipItem(item_data)
	if self.xianjie_boss_getitem_show then
		self.xianjie_boss_getitem_show:Show(item_data)
	end
end

--从地上捡起物品 
local pick_falling_count = 1
function BossWGCtrl:OnSCCrossFairylandBossPickFallingItem(protocol)
	local show_func	= function ()
        local data = FairyLandEquipmentWGData.Instance:GetGodBookChipCfgByItemId(protocol.item_id)
		if data and next(data) then
			local item_data = {}
            item_data.part = data.part
            item_data.item_id = protocol.item_id
			item_data.change_num = 1
			item_data.anim_complete_fun = BindTool.Bind(self.PickXianjieItemAnimComplete,self)
			pick_falling_count = pick_falling_count + 1
			if pick_falling_count > 50 then
				pick_falling_count = 1
			end
            self:ShowGetXianJieEquipItem(item_data)
            local item_cfg = ItemWGData.Instance:GetItemConfig(protocol.item_id)
            local color = item_cfg.color or 1
			local bundle, asset = ResPath.GetFallItemEffect(color)
			local async_loader = AllocAsyncLoader(self, "XianJieBossPickFalling" .. pick_falling_count)
			local mainrole = Scene.Instance:GetMainRole()
			async_loader:SetIsUseObjPool(true)
			async_loader:SetParent(mainrole.draw_obj.root_transform)
			async_loader:Load(bundle, asset, function(obj)
				if IsNil(obj) then
					return
				end
				obj:SetActive(true)
				if mainrole and mainrole.draw_obj then
					local x, y = GameMapHelper.LogicToWorld(protocol.pos_x, protocol.pos_y)
					local role_pos = mainrole.draw_obj.root_transform.position
					obj.transform.position = Vector3(x, role_pos.y, y)
					obj.transform:DOLocalMove(Vector3(0, 2, 0), 1)
				end
				GlobalTimerQuest:AddDelayTimer(function()
					async_loader:Destroy()
				end, 1)
			end)
		end
	end

    if self.xianjie_boss_equip_show then
        self.xianjie_boss_equip_show:FlushData(show_func)
	end
end

--捡起物品动画播放完了，回调事件
function BossWGCtrl:PickXianjieItemAnimComplete()
	if self.xianjie_boss_equip_show and self.xianjie_boss_equip_show:IsOpen() then
		self.xianjie_boss_equip_show:Flush()
		self.xianjie_boss_equip_show:SetAutoHide()
	end
end


function BossWGCtrl:GetXianjieEquipBtnNode(item_id)
	if self.xianjie_boss_equip_show and self.xianjie_boss_equip_show:IsOpen() then
		local node = self.xianjie_boss_equip_show:GetEquipBtnNode(item_id)
		return node
	end
end

function BossWGCtrl:OpenXianJieBossEquipShow()
	if self.xianjie_boss_equip_show and not self.xianjie_boss_equip_show:IsOpen() then
		self.xianjie_boss_equip_show:Open()
	end
end

function BossWGCtrl:FlushXianjieBossInfo()
    ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.xianjie_boss, "clear_xianjie_select")
end

function BossWGCtrl:CloseXianJieBossEquipShow()
	if self.xianjie_boss_equip_show then
		self.xianjie_boss_equip_show:Close()
	end
end

function BossWGCtrl:GetXianJieBossEquipShowState()
	if self.xianjie_boss_equip_show then
		return self.xianjie_boss_equip_show:IsOpen() and self.xianjie_boss_equip_show:GetState()
	end
end

function BossWGCtrl:MoveXianJieRevengeHorizontal(is_left)
	if self.xianjie_boss_revenge_view and self.xianjie_boss_revenge_view:IsOpen() then
		self.xianjie_boss_revenge_view:MoveHorizontal(is_left)
	end
end

function BossWGCtrl:OnReceiveRolePosInfo(protocol)
    if protocol.obj_type == SceneObjType.Role then
        local enemy_info = self.xianjie_data:GetCurEnemyInfo()
        if enemy_info then
            local uuid = enemy_info.uuid
            if uuid and uuid == protocol.param then
                GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
                GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
                    self:XianJieFindAndFightEnemy(enemy_info)
                end)
                GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), protocol.pos_x, protocol.pos_y, 0, nil,nil,nil,nil,nil,nil,true)
            end
        end
    else --查询不到，认为离开了场景
        BossWGCtrl.Instance:CancelSelectXianJieEnemy()
    end
end


function BossWGCtrl:OnSCCrossFairylandBossBeKillRecordInfo(protocol)
    self.xianjie_data:SaveXianjieEnemyInfo(protocol)
    self.xianjie_boss_revenge_view:Flush()
end

function BossWGCtrl:OnSCCrossFairylandBossBeKillRecordAdd(protocol)
	self.xianjie_data:AddXianjieEnemyInfo(protocol)
    self.xianjie_boss_revenge_view:Flush()
end

function BossWGCtrl:OnSCCrossFairylandBossBeKillRecordRemove(protocol)
	self.xianjie_data:RemoveXianjieEnemyInfo(protocol)
    self.xianjie_boss_revenge_view:Flush()
end

function BossWGCtrl:EnterXianJieSceneCallBack()
    BossWGCtrl.Instance:OpenXianJieTiredView()
    BossWGCtrl.Instance:OpenXianJieBossEquipShow()
    BossWGCtrl.Instance:OpenXianJieRevengeView()
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        if nil == self.remind_enter_alert then
            self.remind_enter_alert = Alert.New()
        end
        self.remind_enter_alert:SetLableString(Language.XianJieBoss.AlertTips)
        self.remind_enter_alert:SetCheckBoxDefaultSelect(false)
        self.remind_enter_alert:SetShowCheckBox(true, "Xianjie_enter_tip")
        self.remind_enter_alert:SetCheckBoxText(Language.Chat.DontTipToday)
        self.remind_enter_alert:SetOkFunc(function()
            if Scene.Instance:GetIsOpenCrossViewByScene() then
                ViewManager.Instance:Open(GuideModuleName.CrossTeamView)
            end
        end)
        self.remind_enter_alert:SetCancelFunc(function()  --因为取消时候如果勾选不再提示，通用提示框还是会打开
            if Scene.Instance:GetIsOpenCrossViewByScene() then
                if self.remind_enter_alert:GetClickCheckBoxIsShow() then --所以在取消的时候加个存储
                    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
                    local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
                    PlayerPrefsUtil.SetInt("Xianjie_enter_tip" .. main_role_id, open_day)
                end
            end
        end)
        local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
        local has_today_cancel_flag = PlayerPrefsUtil.GetInt("Xianjie_enter_tip" .. main_role_id) == open_day
        if not has_today_cancel_flag then
            self.remind_enter_alert:Open()
        end
    end
end

function BossWGCtrl:OutXianJieSceneCallBack()
    TipWGCtrl.Instance:CloseAlertTips()
    self:CloseXianJieRevengeView()
    self:CloseXianJieBossEquipShow()
    self.xianjie_data:ClearCurEnemyInfo()
    self.xianjie_data:ClearXianJieEnemyInfo()
    if self.xianjie_data:GetXianjieRed() == 1 then --有次数或者有门票，打开界面
        ViewManager.Instance:Open(GuideModuleName.WorldServer, FunName.XianJieBoss)
    end
    if self.remind_enter_alert and self.remind_enter_alert:IsOpen() then
        self.remind_enter_alert:Close()
    end
end

function BossWGCtrl:CancelSelectXianJieEnemy()
    if Scene.Instance:GetSceneType() == SceneType.XianJie_Boss then
        self.xianjie_data:ClearCurEnemyInfo()
        self.xianjie_boss_revenge_view:CancelSelectEnemy()
    end
end

function BossWGCtrl:XianJieFindAndFightEnemy(enemy_info)
    local uuid = enemy_info.uuid
    local obj = Scene.Instance:GetRoleByUUID(uuid)
    if obj then
        self:DealFindEndEnmey(obj)
    else
        local plat_type, server_id = enemy_info.usid.temp_high, enemy_info.usid.temp_low
        self.xianjie_data:SaveCurEnemyInfo(enemy_info)
        local ll = TwoUIntToLL(uuid.temp_high, uuid.temp_low)
        Scene.Instance:SendGetOneMovePosNew(SceneObjType.Role, plat_type, server_id, ll)
    end
end

function BossWGCtrl:SelectEnemyCallBack(enemy_info)
    local main_role = Scene.Instance:GetMainRole()
    if main_role == nil or main_role:IsDeleted() or main_role:IsRealDead() or main_role:IsGhost() then
        self:CancelSelectXianJieEnemy()
		return
    end
    local my_vo = main_role:GetVo()
    local target_info = {}
    target_info.merge_server_id = enemy_info.usid.temp_low
    target_info.plat_type = enemy_info.usid.temp_high
    target_info.area_index = enemy_info.area_index
    target_info.guild_id = enemy_info.guild_id
    local is_can_attack, need_change_mode = self:GetXianJieSceneIsCanAttack(target_info, my_vo)

    local ok_fun = function()
        if need_change_mode then
            MainuiWGCtrl.Instance:SendSetAttackMode(need_change_mode, 1)
        end
        self:XianJieFindAndFightEnemy(enemy_info)
    end

    if is_can_attack then
        ok_fun()
    else
        local cancel_fun = function() 
            self:CancelSelectXianJieEnemy()
        end
        local str = string.format(Language.XianJieBoss.ChangeModeFight, Language.Common.AttackMode[need_change_mode])
        TipWGCtrl.Instance:OpenAlertTips(str, ok_fun, cancel_fun, nil, cancel_fun)
    end
end

--返回是否是敌人(不考虑安全区，是否死亡等因素，只考虑当前模式) ,想要攻击时需要切换的模式
--target_info //area_index, merge_server_id, guild_id, plat_type,
function BossWGCtrl:GetXianJieSceneIsCanAttack(target_info, my_vo)
    if IsEmptyTable(target_info) then
        return false, ATTACK_MODE.ALL
    end
    local main_role = Scene.Instance:GetMainRole()
    local main_role_vo = main_role and main_role:GetVo()
    my_vo = my_vo or main_role_vo
    local my_attack_mode = my_vo.attack_mode
    local my_area_index = my_vo.area_index
    local target_area_index = target_info.area_index
    local my_server_id = my_vo.merge_server_id
    local is_same_guild = target_info.guild_id ~= 0 and my_vo.guild_id ~= 0 and target_info.guild_id == my_vo.guild_id

    local is_can_attack = true
    local need_change_mode
    local is_same_server = my_server_id == target_info.merge_server_id and my_vo.plat_type == target_info.plat_type
    if my_attack_mode == ATTACK_MODE.PEACE then --和平，无法攻击需要切换模式
        is_can_attack = false
        if my_area_index ~= target_area_index then
            need_change_mode = ATTACK_MODE.AREA
        elseif not is_same_server then
            need_change_mode = ATTACK_MODE.NAMECOLOR
        elseif not is_same_guild then
            need_change_mode = ATTACK_MODE.GUILD
        else
            need_change_mode = ATTACK_MODE.ALL
        end
    elseif my_attack_mode == ATTACK_MODE.AREA then --战区模式
        if my_area_index ~= target_area_index then
            is_can_attack = true
        else
            is_can_attack = false
            if not is_same_server then
                need_change_mode = ATTACK_MODE.NAMECOLOR
            elseif not is_same_guild then
                need_change_mode = ATTACK_MODE.GUILD
            else
                need_change_mode = ATTACK_MODE.ALL
            end
        end
    elseif my_attack_mode == ATTACK_MODE.NAMECOLOR then --国家模式
        if my_server_id == target_info.merge_server_id and my_vo.plat_type == target_info.plat_type then
            is_can_attack = false
            if not is_same_guild then
                need_change_mode = ATTACK_MODE.GUILD
            else
                need_change_mode = ATTACK_MODE.ALL
            end
        else
            is_can_attack = true
        end
    elseif my_attack_mode == ATTACK_MODE.GUILD then --仙盟模式
        if not is_same_guild then
            is_can_attack = true
        else
            need_change_mode = ATTACK_MODE.ALL
            is_can_attack = false
        end
    elseif my_attack_mode == ATTACK_MODE.ALL then
        is_can_attack = true
    end

    return is_can_attack, need_change_mode
end

function BossWGCtrl:DealFindEndEnmey(obj)
    GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
    MoveCache.SetEndType(MoveEndType.AttackTarget)
    GuajiCache.target_obj = obj
    GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj, SceneTargetSelectType.SELECT)
    GuajiWGCtrl.Instance:MoveToObj(obj, COMMON_CONSTS.GUAJI_MAX_RANGE, nil, nil, true)
    GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
end

function BossWGCtrl:OnXianJieEquipInfoReceive()
    self:ReloadOtherFollowXianJieEquipImg()
end

function BossWGCtrl:ReloadOtherFollowXianJieEquipImg()
    local role_list = Scene.Instance:GetRoleList()
    if not IsEmptyTable(role_list) then
        for k, v in pairs(role_list) do
            if v and v.ReloadFollowXianJieEquipImg then
                v:ReloadFollowXianJieEquipImg()
            end
        end
    end
end

function BossWGCtrl:ReloadAllAreaIndexEquipImg()
    local main_role = Scene.Instance:GetMainRole()
    if main_role then
        main_role:ReloadAreaIndexEquipImg()
    end
    local role_list = Scene.Instance:GetRoleList()
    if not IsEmptyTable(role_list) then
        for k, v in pairs(role_list) do
            if v and v.ReloadAreaIndexEquipImg then
                v:ReloadAreaIndexEquipImg()
            end
        end
    end
end

function BossWGCtrl:JudgeNeedPickObj(obj)
    if obj and obj.IsRole and obj:IsRole() then
        GuajiWGCtrl.Instance:SetBossDeadTime() --停留捡装备
    end
end

function BossWGCtrl:PrintAllRoleState()
    --BossWGCtrl.Instance:PrintAllRoleState()
    local main_vo = GameVoManager.Instance:GetMainRoleVo()
    local str = ""
    if main_vo then
        str = str .. "拿到的主角hp数据：" .. main_vo.hp .. "/" .. main_vo.max_hp
    end
    local role_list = Scene.Instance:GetRoleList()
    if not IsEmptyTable(role_list) then
        for k, v in pairs(role_list) do
            if v and v.vo then
                str = str .."  其他角色hp数据：" .. v.vo.name.." " .. v.vo.hp .. "/" .. v.vo.max_hp
            end
        end
    end
    print_error(str)
end