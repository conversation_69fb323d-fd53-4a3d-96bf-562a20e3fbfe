﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_UI3DDisplayWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(Nirvana.UI3DDisplay), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>RegFunction("SetDragSpeed", SetDragSpeed);
		<PERSON><PERSON>RegFunction("DisplayPerspective", DisplayPerspective);
		<PERSON><PERSON>RegFunction("DisplayPerspectiveSimple", DisplayPerspectiveSimple);
		<PERSON><PERSON>RegFunction("DisplayPerspectiveWithOffset", DisplayPerspectiveWithOffset);
		<PERSON><PERSON>RegFunction("DisplayOrthographic", DisplayOrthographic);
		<PERSON><PERSON>RegFunction("DisplayOrthographicSimple", DisplayOrthographicSimple);
		L.RegFunction("Display", Display);
		<PERSON>.RegFunction("ClearDisplay", ClearDisplay);
		<PERSON><PERSON>RegFunction("ResetRotation", ResetRotation);
		<PERSON><PERSON>unction("SetRotation", SetRotation);
		<PERSON><PERSON>RegFunction("SetScale", SetScale);
		<PERSON><PERSON>RegFunction("OnDrag", OnDrag);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("FitScaleRoot", get_FitScaleRoot, null);
		L.RegVar("DisplayCamera", get_DisplayCamera, set_DisplayCamera);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetDragSpeed(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.UI3DDisplay obj = (Nirvana.UI3DDisplay)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SetDragSpeed(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DisplayPerspective(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 7);
			Nirvana.UI3DDisplay obj = (Nirvana.UI3DDisplay)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay));
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
			UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
			UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 4);
			float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
			float arg4 = (float)LuaDLL.luaL_checknumber(L, 6);
			float arg5 = (float)LuaDLL.luaL_checknumber(L, 7);
			obj.DisplayPerspective(arg0, arg1, arg2, arg3, arg4, arg5);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DisplayPerspectiveSimple(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			Nirvana.UI3DDisplay obj = (Nirvana.UI3DDisplay)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay));
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
			UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
			UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 4);
			obj.DisplayPerspectiveSimple(arg0, arg1, arg2);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DisplayPerspectiveWithOffset(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 5);
			Nirvana.UI3DDisplay obj = (Nirvana.UI3DDisplay)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay));
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
			UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
			UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 4);
			UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 5);
			obj.DisplayPerspectiveWithOffset(arg0, arg1, arg2, arg3);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DisplayOrthographic(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 7);
			Nirvana.UI3DDisplay obj = (Nirvana.UI3DDisplay)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay));
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
			UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
			UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 4);
			float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
			float arg4 = (float)LuaDLL.luaL_checknumber(L, 6);
			float arg5 = (float)LuaDLL.luaL_checknumber(L, 7);
			obj.DisplayOrthographic(arg0, arg1, arg2, arg3, arg4, arg5);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DisplayOrthographicSimple(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			Nirvana.UI3DDisplay obj = (Nirvana.UI3DDisplay)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay));
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
			UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
			UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 4);
			obj.DisplayOrthographicSimple(arg0, arg1, arg2);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Display(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			Nirvana.UI3DDisplay obj = (Nirvana.UI3DDisplay)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay));
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
			UnityEngine.Camera arg1 = (UnityEngine.Camera)ToLua.CheckObject(L, 3, typeof(UnityEngine.Camera));
			obj.Display(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearDisplay(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.UI3DDisplay obj = (Nirvana.UI3DDisplay)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay));
			obj.ClearDisplay();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResetRotation(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.UI3DDisplay obj = (Nirvana.UI3DDisplay)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay));
			obj.ResetRotation();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetRotation(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.UI3DDisplay obj = (Nirvana.UI3DDisplay)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay));
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.SetRotation(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetScale(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.UI3DDisplay obj = (Nirvana.UI3DDisplay)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay));
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.SetScale(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnDrag(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.UI3DDisplay obj = (Nirvana.UI3DDisplay)ToLua.CheckObject(L, 1, typeof(Nirvana.UI3DDisplay));
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnDrag(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_FitScaleRoot(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UI3DDisplay obj = (Nirvana.UI3DDisplay)o;
			UnityEngine.Transform ret = obj.FitScaleRoot;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index FitScaleRoot on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_DisplayCamera(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UI3DDisplay obj = (Nirvana.UI3DDisplay)o;
			UnityEngine.Camera ret = obj.DisplayCamera;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index DisplayCamera on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_DisplayCamera(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UI3DDisplay obj = (Nirvana.UI3DDisplay)o;
			UnityEngine.Camera arg0 = (UnityEngine.Camera)ToLua.CheckObject(L, 2, typeof(UnityEngine.Camera));
			obj.DisplayCamera = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index DisplayCamera on a nil value");
		}
	}
}

