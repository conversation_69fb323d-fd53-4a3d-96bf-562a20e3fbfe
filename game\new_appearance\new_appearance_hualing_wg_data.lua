function NewAppearanceWGData:InitHuaLingCfg()
    local cfg = ConfigManager.Instance:GetAutoConfig("hualing_cfg_auto")
    self.hualing_cfg = ListToMap(cfg.hualing, "type", "id")
    self.open_cfg = ListToMap(cfg.open, "type", "image_id")
    self.hualing_stuff_cfg = ListToMap(cfg.hualing, "cost_item_id")
    
    self.hualing_info = {}
    self:GetHuaLingStuffRemindList()
    RemindManager.Instance:Register(RemindName.NewAppearance_LingChong_HuaLing, BindTool.Bind(self.GetHuaLingRemind, self, TabIndex.new_appearance_lingchong_hualing))
    RemindManager.Instance:Register(RemindName.NewAppearance_Mount_HuaLing, BindTool.Bind(self.GetHuaLingRemind, self, TabIndex.new_appearance_mount_hualing))
    RemindManager.Instance:Register(RemindName.NewAppearance_Kun_HuaLing, BindTool.Bind(self.GetHuaLingRemind, self, TabIndex.new_appearance_kun_hualing))
end

function NewAppearanceWGData:DeleteHuaLingData()
    RemindManager.Instance:UnRegister(RemindName.NewAppearance_LingChong_HuaLing)
    RemindManager.Instance:UnRegister(RemindName.NewAppearance_Mount_HuaLing)
    RemindManager.Instance:UnRegister(RemindName.NewAppearance_Kun_HuaLing)
end

function NewAppearanceWGData:SetHuaLingInfo(protocol)
    self.hualing_info[protocol.type] = protocol.hualing_item
end

function NewAppearanceWGData:UpdateHuaLingInfo(protocol)
    self.hualing_info[protocol.type][protocol.seq] = protocol.level
end

function NewAppearanceWGData:GetHuaLingCfg(type, seq)
    return (self.hualing_cfg[type] or {})[seq]
end

function NewAppearanceWGData:GetHuaLingAttrCfg(type, seq, level)
    local data_list = {}
    local cfg = self:GetHuaLingCfg(type, seq)
    if IsEmptyTable(cfg) then
        return data_list
    end

    local is_max_level, limit_level = self:IsHuaLingMaxLevel(type, seq, level)
    if level > limit_level then
        return data_list
    end

    local attr_id, attr_value = 0, 0
    for i = 1, 5 do
        attr_id = cfg["attr_id" .. i]
		attr_value = cfg["attr_value" .. i]
        if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
			data_list[attr_str] = attr_value * level
		end
    end

    return data_list
end

function NewAppearanceWGData:GetHuaLingOpenCfg(type, image_id)
    return (self.open_cfg[type] or {})[image_id]
end

function NewAppearanceWGData:GetHuaLingLevel(type, seq)
    return (self.hualing_info[type] or {})[seq] or 0
end

function NewAppearanceWGData:IsHuaLingMaxLevel(type, seq, level)
    local cfg = self:GetHuaLingCfg(type, seq)
    local target_level = level or self:GetHuaLingLevel(type, cfg.seq)
    return target_level >= cfg.level_limit, cfg.level_limit
end

-- 化灵材料
function NewAppearanceWGData:GetHuaLingStuffRemindList()
    self.hualing_stuff_remind_list = {}

    for k,v in pairs(self.hualing_stuff_cfg) do   --type   list[seq]
        self.hualing_stuff_remind_list[k] =  self.hualing_stuff_remind_list[k] or {}
        local remind = v.type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT and RemindName.NewAppearance_Mount_HuaLing
         or MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG and RemindName.NewAppearance_LingChong_HuaLing
         or RemindName.NewAppearance_Kun_HuaLing
        self.hualing_stuff_remind_list[k][remind] = true
    end
end

function NewAppearanceWGData:CheckHuaLingStuffRemind(item_id)
    local flush_view = false
    local fire_remind_list = function (remind_list)
        if remind_list then
            for k,v in pairs(remind_list) do
                RemindManager.Instance:Fire(k)
            end
    
            flush_view = true
        end
    end

    fire_remind_list(self.hualing_stuff_remind_list[item_id])
    return flush_view
end

function NewAppearanceWGData:GetHuaLingRemind(tab_index)
    local tab_data = NEW_APPEARANCE_TAB_DATA[tab_index]
    if not tab_data then
        return 0
    end

    local tip_type = tab_data.strengthen_type
    local qc_type = tab_data.qc_type
    local show_list = self:GetQiChongSpecialShowList(qc_type)
    for k,v in ipairs(show_list) do
        if self:GetSingleHuaLingRemind(qc_type, v.image_id) then
            MainuiWGCtrl.Instance:InvateTip(tip_type, 1, function ()
                FunOpen.Instance:OpenViewByName(GuideModuleName.NewAppearanceWGView, tab_index)
                return true
            end)

            return 1
        end
    end

    MainuiWGCtrl.Instance:InvateTip(tip_type, 0)
    
    return 0
end

function NewAppearanceWGData:GetSingleHuaLingRemind(type, seq)
    local hualing_cfg = self:GetHuaLingCfg(type, seq)
    if IsEmptyTable(hualing_cfg) then
        return false
    end

    local hualing_open_cfg = self:GetHuaLingOpenCfg(type, hualing_cfg.image_id)
    if IsEmptyTable(hualing_open_cfg) then
        return false
    end

    local grade = self:GetSpecialQiChongGrade(type, seq)
    local is_active = grade > 0 and grade >= hualing_open_cfg.limit_grade
    local is_max_grade = self:IsHuaLingMaxLevel(type, seq)

    if is_active and not is_max_grade then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(hualing_cfg.cost_item_id)
        return item_num >= hualing_cfg.cost_item_num
    end

    return false
end