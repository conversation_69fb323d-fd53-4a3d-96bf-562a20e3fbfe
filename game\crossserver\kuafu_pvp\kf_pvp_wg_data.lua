KuafuPVPWGData = KuafuPVPWGData or BaseClass()

KuafuPVPWGData.KF3V3_SIDE =
{
	SIDE_0 = 0,										--对战方0
	SIDE_1 = 1,										--对战方1
	SIDE_MAX = 2
}

function KuafuPVPWGData:__init()
	if KuafuPVPWGData.Instance then
		ErrorLog("[KuafuPVPWGData] attempt to create singleton twice!")
		return
	end
	KuafuPVPWGData.Instance =self
	self.stronghold_info = {
		side_score_list = {},
		stronghold_list = {},
		flag_info = {}
	}

	self.matesList = {}

	self.role_info = {
		self_side = 0,
		kills = 0,
		assist = 0,
		dead = 0,
	}

	self.prepare_info = {
		match_state = 0,
		win_side = 0,
		next_state_time = 0,
		user_info_list = {},
	}

	self.acticity_info = {
		challenge_mvp_count = 0,
		challenge_score = 0,
		challenge_total_match_count = 0,
		challenge_win_match_count = 0,
		win_percent = 0,
		today_match_count = 0,
		matching_state = 0,
	}

	self.match_state_info = {
		matching_state = -1,
		user_list = {},
	}
	self.challenge_score = 0
	self.join_time_reward_flag = {}
	self.gongxun_reward_fetch_list = {}
	self.kuafu_tvt_cfg = ConfigManager.Instance:GetAutoConfig("kuafu_tvt_auto")
end

function KuafuPVPWGData:__delete()
	KuafuPVPWGData.Instance = nil
	self.kuafu_tvt_cfg = nil
end
--获取购买花费
function KuafuPVPWGData.GetBuyTimeCost()
	local cfg =  ConfigManager.Instance:GetAutoConfig("kuafu_tvt_auto").other[1]
	return cfg.max_buy_times,cfg.buy_time_cost,cfg.join_limit_daycount
end

--获取战队改名卡物品列表
function KuafuPVPWGData.GetModifyNameItem()
	local cfg =  ConfigManager.Instance:GetAutoConfig("kuafu_tvt_auto").other[1]
	return cfg.name_item
end

--获取战队改名卡消耗货币类型， 消耗货币数量
function KuafuPVPWGData.GetModifyNameComsume()
	local cfg =  ConfigManager.Instance:GetAutoConfig("kuafu_tvt_auto").other[1]
	return cfg.name_money_type, cfg.name_money
end

function KuafuPVPWGData:SetMatesInfo(list)
	self.matesList = list
end

function KuafuPVPWGData:AddTeamMate(info)
	if #self.matesList >= 3 then return end
	for k,v in pairs(self.matesList) do
		if v.uid == info.role_id then
			return
		end
	end
	local vo = {}
	vo.plat_type = info.plat_type
	vo.server_id = info.server_id
	vo.role_id = info.role_id
	vo.uid = info.role_cross_id--
	vo.user_name = info.role_name
	vo.sex = info.sex
	vo.head_icon = info.head_icon
	vo.prof = info.prof
	vo.camp = info.camp
	vo.level = info.level
	vo.challenge_score = info.challenge_score
	vo.win_percent = info.win_percent
	vo.mvp_count = info.mvp_count
	vo.capability = info.capability
	vo.appearance = info.appearance
	self.matesList[#self.matesList +1] = vo
end

function KuafuPVPWGData:GetMatesInfo()
	return self.matesList
end

function KuafuPVPWGData:SetRoleInfo(info)
	self.role_info.self_side = info.self_side
	self.role_info.kills = info.kills
	self.role_info.assist = info.assist
	self.role_info.dead = info.dead
end

function KuafuPVPWGData:GetRoleInfo()
	return self.role_info
end

function KuafuPVPWGData:SetStrongHoldInfo(info)
	self.stronghold_info.side_score_list = info.side_score_list
	self.stronghold_info.stronghold_list = info.stronghold_list
	self.stronghold_info.flag_info = info.flag_info
end

function KuafuPVPWGData:GetStrongHoldInfo()
	return self.stronghold_info
end
function KuafuPVPWGData:SetKF3v3DayCount(info)
	 self.reward_daycount = info
end

function KuafuPVPWGData:GetKF3v3DayCount()
		return self.reward_daycount
end

function KuafuPVPWGData:SetMatchStateInfo(info)
	self.match_state_info.matching_state = info.matching_state
	self.match_state_info.user_list = info.user_list
end

function KuafuPVPWGData:GetMatchStateInfo()
	return self.match_state_info
end

function KuafuPVPWGData:GetKaFuTvTCfg()
	return self.kuafu_tvt_cfg
end

function KuafuPVPWGData:SetActivityInfo(info)
	self.acticity_info = info
	self.acticity_info.cur_season  = info.season_count
	self.join_time_reward_flag = bit:d2b(info.join_reward_fetch_flag)
	self.cross_3v3_season_reward_use = info.cross_3v3_season_reward_use
end

function KuafuPVPWGData:GetActivityInfo()
	return self.acticity_info
end

function KuafuPVPWGData:GetWZCardSelectCfg(index)
	if self.cross_3v3_season_reward_use  == nil then return  end
		for k,v in ipairs(self.cross_3v3_season_reward_use) do
			if v == index  then
				return true
			end
		end
	return false
end

function KuafuPVPWGData:GetPvPJionTimesRewardIsGet(index)
	return self.join_time_reward_flag[33- index]
end

function KuafuPVPWGData:SetPrepareInfo(info)
	self.prepare_info.match_state = info.match_state
	self.prepare_info.win_side = info.win_side
	self.prepare_info.next_state_time = info.next_state_time
	self.prepare_info.user_info_list = info.user_info_list
end

function KuafuPVPWGData:GetResultList()
	local data = {}
	local data_before = {}
	local data_after = {}
	for k,v in pairs(self.prepare_info.user_info_list) do
		local list = __TableCopy(v)
		list.is_win = 1
		if self.prepare_info.win_side == 0 or self.prepare_info.win_side == -1  and v.index <= 3 then
			list.is_win = 0
		elseif self.prepare_info.win_side == 1 or self.prepare_info.win_side == -1 and v.index > 3 then
			list.is_win = 0
		end
		if k <= 3 then
			table.insert(data_before,list)
		else
			table.insert(data_after,list)
		end
		--table.insert(data,list)
	end

	table.sort(data_before,SortTools.KeyUpperSorter("origin_score"))
	table.sort(data_after,SortTools.KeyUpperSorter("origin_score"))
	if self.prepare_info.win_side == 0 or self.prepare_info.win_side == -1 then
		for k,v in pairs(data_after) do
			table.insert(data_before,v)
		end
		for k,v in pairs(data_before) do
			v.index = k
		end
		data = data_before
	elseif self.prepare_info.win_side == 1 then
		for k,v in pairs(data_before) do
			table.insert(data_after,v)
		end
		for k,v in pairs(data_after) do
			v.index = k
		end
		data = data_after
	end




	return data
end

function KuafuPVPWGData:GetPrepareInfo()
	return self.prepare_info
end

function KuafuPVPWGData:GetGatherNameByObjid(objid)
	local name,color = "", COLOR3B.WHITE
	local col_t = {[0] = COLOR3B.WHITE, COLOR3B.RED, COLOR3B.BLUE}
	for k,v in pairs(self.stronghold_info.stronghold_list) do
		if objid == v.obj_id then
			name = Language.KuafuPVP.GatherName[v.owner_side + 1] or ""
			color = col_t[v.owner_side + 1] or COLOR3B.WHITE
		end
	end
	return name,color
end

function KuafuPVPWGData:GetGatherResByObjid(objid)
	local res = 21
	local res_t = {[0] = 21, 22, 15}
	for k,v in pairs(self.stronghold_info.stronghold_list) do
		if objid == v.obj_id then
			res = res_t[v.owner_side + 1] or 21
		end
	end
	return res
end


function KuafuPVPWGData:GetJoinTimesReward(join_times)
	local cfg =  self.kuafu_tvt_cfg.join_times_reward
	local num = #cfg
	for i = 1, #cfg do
		if join_times >= cfg[i].jion_times and cfg[i].seq + 1 == self.reward_daycount then
			num = i
			break
		end
		if join_times < cfg[i].jion_times then
			num = i
			break
		end
	end
	return cfg[num]
end
function KuafuPVPWGData:GetRewardIntegralCfg(protocol)
	self.challenge_score = protocol.info.challenge_score
	self.gongxun_value = protocol.info.gongxun_value
	self.gongxun_reward_fetch_list = bit:d2b(protocol.info.gongxun_reward_fetch_flag)
end

function KuafuPVPWGData:GetRewardIntegral()
	return self.challenge_score
end
function KuafuPVPWGData:GetRewardGongxun()
	return self.gongxun_value
end

function KuafuPVPWGData:GetPvPGongXunRewardIsGet(index)
	return self.gongxun_reward_fetch_list[32- index]
end

function KuafuPVPWGData:GetRewardCfg()
	local reward_cfg = self.kuafu_tvt_cfg.join_times_reward
	return reward_cfg
end

function KuafuPVPWGData:GetRewardDanCfg()
	local reward_cfg = self.kuafu_tvt_cfg.grade_score
	return reward_cfg
end


function KuafuPVPWGData:GetJiFenRewardCfg()
	local reward_cfg = self.kuafu_tvt_cfg.score_reward
	local cfg = {}
	for i = 1,#reward_cfg do
		cfg[i] = {}
		cfg[i].reward_item = reward_cfg[i].reward_item
		cfg[i].seq = reward_cfg[i].seq
		cfg[i].need_score = reward_cfg[i].need_score
		cfg[i].pvp = true
	end
	return cfg
end


function KuafuPVPWGData:GetdwRankReward(season)
	local cfg = self.kuafu_tvt_cfg.gxshow_cfg
	 -- return cfg
	local data = {}
	local should_select_season = season
	if season > 12 then
		should_select_season = 12 - season % 2
	end
	for k,v in pairs(cfg) do
		if v.season == should_select_season then
			data[v.grade] = {}
			data[v.grade].seq = v.seq
			data[v.grade].grade = v.grade
			data[v.grade].score = v.score
			data[v.grade].name = v.name
			data[v.grade].reward_item = v.reward_item
		end
	end
	return data
end

function KuafuPVPWGData:GetRewardBaseCell(jifen)
	local item_cfg = self.kuafu_tvt_cfg.grade_score

	for k = #item_cfg,1, -1 do
		if item_cfg[k] == nil then
			--print_error( "k 为空")
		end

		if item_cfg[k] and item_cfg[k].score <= jifen then
			return item_cfg[k]
		end
	end
	return item_cfg[1]
end

function KuafuPVPWGData:GetGridNum()
	if nil == self.kuafu_tvt_cfg or nil == self.kuafu_tvt_cfg.grade_score then
		return 0
	end

	return #self.kuafu_tvt_cfg.grade_score
end

function KuafuPVPWGData:SetProgLevel(grade)
	local item_cfg = self.kuafu_tvt_cfg.grade_score
	if grade < #item_cfg - 1 then
		for k = #item_cfg,1, -1 do
			if item_cfg[k].grade == grade + 1 then
				return item_cfg[k]
			end
		end
	end
	return item_cfg[#item_cfg]
end

function KuafuPVPWGData:GetWZLingPaiCfg()
	if nil == self.kuafu_tvt_cfg or nil == self.kuafu_tvt_cfg.xndex then
		return nil
	end

	self.cfg_list = self.kuafu_tvt_cfg.xndex
	local data_lits  = self:GetActivityInfo()
	self.data_cfg  = {}

	if nil == data_lits or not data_lits.cur_season then
		return nil
	end
	if data_lits.cur_season <= 1 then
		table.insert(self.data_cfg, {})
	end

	local cur_season = data_lits.cur_season
	if cur_season <= 12 then
		for k,v in ipairs(self.cfg_list) do
			if v.seq <= data_lits.cur_season + 1 then
				local data = {}
				local data_flag  = data_lits.cross_3v3_season_reward[k]
				data.index = v.seq
				data.flag = data_flag
				data.cur_season = data_lits.season_count
				data.pic  = v.img_pic
				data.type = "kuafu_tvt_auto"
				table.insert(self.data_cfg, data)
			end
		end
	else
		for i = 1, cur_season do
			if i < 12 then
				--if cur_season + 1 <= i then
					local data = {}
					local data_flag  = data_lits.cross_3v3_season_reward[i]
					data.index = self.cfg_list[i].seq
					data.flag = data_flag
					data.cur_season = data_lits.cur_season
					data.pic  = self.cfg_list[i].img_pic
					data.type = "kuafu_tvt_auto"
					table.insert(self.data_cfg, data)
				--end
			else
				local should_select_season = 12 - i % 2
				--if self.cfg_list[i].seq <= i + 1 then
					local data = {}
					local data_flag  = data_lits.cross_3v3_season_reward[i]
					data.index = i
					data.flag = data_flag
					data.cur_season = data_lits.cur_season
					data.pic  = self.cfg_list[should_select_season].img_pic
					data.type = "kuafu_tvt_auto"
					table.insert(self.data_cfg, data)
				--end
			end
		end
	end
	return self.data_cfg
end

function KuafuPVPWGData:GetWZCardAttributeCfg(index,grade)
	local ring_att = self.kuafu_tvt_cfg.season_card
	for k,v in ipairs(ring_att) do
		if v.season == index and v.grade == grade then
			return v
		end
	end
	return {}
end

function KuafuPVPWGData:GetPvPRankData()
	local ranking_data = RankWGData.Instance:GetRankData(RankKind.Cross, CROSS_PERSON_RANK_TYPE.CROSS_PERSON_RANK_TYPE_3V3_SCORE)
	local kfpvp_other_cfg = self.kuafu_tvt_cfg.other[1]
	self.pvp_rank_list = {}
	for k,v in pairs(ranking_data) do
		if v.rank_value >= kfpvp_other_cfg.rank_score_limit then
			table.insert(self.pvp_rank_list, v)
		end
	end
	return self.pvp_rank_list
end

function KuafuPVPWGData:RemindKFPvP()
	local cfg = self:GetJiFenRewardCfg()
	local gongxun_value = self:GetRewardGongxun()
	local flag = 0
	for k,v in ipairs(cfg) do
		local remind = self:GetPvPGongXunRewardIsGet(v.seq)
		if gongxun_value >= v.need_score and remind == 0  then
			flag = flag + 1
		end
	end
	return  flag
end

function KuafuPVPWGData:IsChckEnemyOccupyKFPvP(target_obj)
	if Scene.Instance:GetSceneType() ~= SceneType.Kf_PVP then return false end
	if target_obj.obj_type == SceneObjType.Monster and self.stronghold_info.flag_info.flag_side then
		if self.role_info.self_side == self.stronghold_info.flag_info.flag_side then
		return true
		-- local info_list = self.prepare_info.user_info_list
		-- local main_role_vo = GameVoManager.Instance:GetMainRoleVo() --role_id
		-- print_error(main_role_vo,info_list)
		-- for k,v in pairs(info_list) do
		-- 	if v.role_id == main_role_vo.role_id then
		-- 		if k > 3 then
		-- 			if self.stronghold_info.flag_info.flag_side == 1 then
		-- 			return true
		-- 			end
		-- 		else
		-- 			if self.stronghold_info.flag_info.flag_side == 0 then
		-- 			return true
		-- 			end
		-- 		end
		-- 	end
		end
	end
	return false
end

function KuafuPVPWGData:IsMineOccupy()
	if self.stronghold_info.flag_info.flag_side then
		if self.role_info.self_side == self.stronghold_info.flag_info.flag_side then
		return true
		end
	end
	return false
end

function KuafuPVPWGData:GetEnemyList()
	local enemy_list = {}
	if self.match_state_info then
		for k,v in pairs(self.match_state_info.user_list) do
			local flag = true
			for m,n in pairs(self.matesList) do
				if v.role_id == n.uid then
					flag = false
				end
			end
			if flag then
				table.insert(enemy_list,v)
			end
		end
	end
	return enemy_list
end

function KuafuPVPWGData:GetBigRankScore()
	local cfg = self.kuafu_tvt_cfg.grade_score
	local big_rank = {}
	for i=1,#cfg,5 do
		local list = {}
		list.low_score = cfg[i].score
		list.up_score = cfg[i + 5] and cfg[i + 5].score - 1
		list.name = string.gsub(cfg[i].name,"V","")
		list.pk_type = "pvp"
		table.insert(big_rank, 1, list)
	end
	return big_rank
end

function KuafuPVPWGData:GetOtherCfg()
	return ConfigManager.Instance:GetAutoConfig("kuafu_tvt_auto").other[1]
end