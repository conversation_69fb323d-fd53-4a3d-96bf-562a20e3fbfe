﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Nirvana;
using UnityEngine.EventSystems;

public class PassDragEvent : PassEvent, IBeginDragHandler, IEndDragHandler, IDragHandler, IInitializePotentialDragHandler, IEventSystemHandler
{
    public void OnBeginDrag(PointerEventData eventData)
    {
        this.PassEventDown<IBeginDragHandler>(eventData, ExecuteEvents.beginDragHandler);
    }

    public void OnDrag(PointerEventData eventData)
    {
        this.PassEventDown<IDragHandler>(eventData, ExecuteEvents.dragHandler);
    }

    public void OnEndDrag(PointerEventData eventData)
    {
        this.PassEventDown<IEndDragHandler>(eventData, ExecuteEvents.endDragHandler);
    }

    public void OnInitializePotentialDrag(PointerEventData eventData)
    {
        this.PassEventDown<IInitializePotentialDragHandler>(eventData, ExecuteEvents.initializePotentialDrag);
    }
}

