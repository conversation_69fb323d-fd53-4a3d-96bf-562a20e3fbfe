﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace HUDProgramme
{
    public class HUDAnim : MonoBehaviour
    {
        [Header("透明度")]
        public AnimationCurve AlphaCurve;
        [Header("大小")]
        public AnimationCurve ScaleCurve;
        [Header("上下位移")]
        public AnimationCurve MoveCurve;
        [Header("左右位移")]
        public AnimationCurve MoveXCurve;
        [Header("飘字前缀")]
        public string HeadName;
        [Header("飘字字体")]
        public string NumberString;

        public float OffsetX;
        public float RandomOffsetX;
        public float OffsetY;
        public float RandomOffsetY;
        public float GapTime;
        public int SpriteGap; // 图片间隔
        public HUDAlignType AlignType;
        public bool ScreenAlign;                // 是不是按屏幕对齐
        public HUDAlignType ScreenAlignType;    // 屏幕对齐类型
    }

    public enum HUDAlignType
    {
        align_left,   // 左对齐
        align_center, // 右对齐
        align_right,  // 居中
    };
}

