----------------------------------------------------
--开服活动 -- 爱的表白
----------------------------------------------------

function ServerActivityTabView:ProfessLoveLoadCallBack()
    if not self.profess_love_list then
        self.profess_love_list = AsyncListView.New(ProfessLoveRender, self.node_list.propose_mid_list)
    end
   
    self.node_list.profess_btn_goto_marry.button:AddClickListener(BindTool.Bind(self.Qing<PERSON>G<PERSON>o<PERSON>ar<PERSON>, self))
    self.node_list.profess_love_plus_btn.button:AddClickListener(BindTool.Bind(self.QingyuanGoToMarry, self))
    self.node_list.btn_propose_wall.button:AddClickListener(BindTool.Bind(self.OpenProfess<PERSON><PERSON>, self))
    self.node_list.btn_goto_propose.button:AddClickListener(BindTool.Bind(self.OpenProposeView, self))
    self.node_list.goto_rank_view.button:AddClickListener(BindTool.Bind(self.OpenProposeRankView, self))
    self.node_list.btn_profess_love_rule.button:AddClickListener(BindTool.Bind(self.OpenProposeRuleView, self))
    self.node_list.my_lover_head_pos.button:AddClickListener(BindTool.Bind(self.OpenProfessLoveMenu, self))
    
    self.my_head_cell = BaseHeadCell.New(self.node_list.my_head_pos)
    self.my_lover_cell = BaseHeadCell.New(self.node_list.my_lover_head_pos)
    self.my_lover_head = RoleHeadCell.New(false)
    self.first_head_cell1 = BaseHeadCell.New(self.node_list.first_head_pos)
    self.first_head_cell2 = BaseHeadCell.New(self.node_list.first_lover_head_pos)
end

function ServerActivityTabView:ShowPerofessIndexCallBack()
    ServerActivityWGCtrl.Instance:SendActLoverReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_BIAOBAI, 0)

    self:DoADBBAnim()
end

function ServerActivityTabView:ProfessLoveReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("act_profess_love_count_down") then
		CountDownManager.Instance:RemoveCountDown("act_profess_love_count_down")
	end
    if self.profess_love_list then
        self.profess_love_list:DeleteMe()
        self.profess_love_list = nil
    end
    if self.my_lover_head then
        self.my_lover_head:DeleteMe()
        self.my_lover_head = nil
    end
    if self.my_head_cell then
		self.my_head_cell:DeleteMe()
		self.my_head_cell = nil
    end
    if self.my_lover_cell then
		self.my_lover_cell:DeleteMe()
		self.my_lover_cell = nil
    end
    if self.first_head_cell1 then
		self.first_head_cell1:DeleteMe()
		self.first_head_cell1 = nil
    end
    if self.first_head_cell2 then
		self.first_head_cell2:DeleteMe()
		self.first_head_cell2 = nil
    end

    -- if self.profess_love_model then
	-- 	self.profess_love_model:DeleteMe()
	-- 	self.profess_love_model = nil
    -- end

    self.ache_profess_love_res = nil
end

function ServerActivityTabView:UpdateProfessLoveTimeShow()
	local activity_id = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_BIAOBAI
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(activity_id)
	if nil == activity_info then
		return
    end
    if activity_info.next_time > TimeWGCtrl.Instance:GetServerTime() then
	    self:UpdateProfessLoveNextTime(TimeWGCtrl.Instance:GetServerTime(), activity_info.next_time)
	    CountDownManager.Instance:AddCountDown("act_profess_love_count_down", BindTool.Bind1(self.UpdateProfessLoveNextTime, self), BindTool.Bind1(self.CompleteProfessLoveNextTime, self), activity_info.next_time, nil, 1)
    else
        self:CompleteProfessLoveNextTime()
    end
end

function ServerActivityTabView:CompleteProfessLoveNextTime()
    self:SetTabVisible()
end

function ServerActivityTabView:UpdateProfessLoveNextTime(elapse_time, total_time)
	local have_total_time = total_time - elapse_time
	local str = ""
	local cfg_info = ActivePerfertQingrenWGData.Instance:GetBiaoBaiEndTimeCfg()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_BIAOBAI)
	if activity_info == nil then
		return
	end

	local end_hour_time_1 = cfg_info.end_time / 100
	local end_hour_time_2 = cfg_info.end_time % 100
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local reward_start_time = 24 * 60 * 60 * (cfg_info.end_day - 1) + end_hour_time_1*60*60 + end_hour_time_2 * 60 - (TimeWGCtrl.Instance:GetServerTime() - activity_info.start_time)
	local reward_start_time1 = reward_start_time + cfg_info.during_time_s
	local str
	if reward_start_time > 0 then
		local time_tab = TimeUtil.Format2TableDHM(reward_start_time)
		if time_tab.day > 0 then
			str = string.format(Language.Common.TimeStr, time_tab.day, time_tab.hour, time_tab.min)
		else
			if time_tab.hour > 0 then
				str = string.format(Language.Common.TimeStr1, time_tab.hour, time_tab.min)
			else
				str = string.format(Language.Common.TimeStr6, time_tab.min)
			end
        end
        str = ToColorStr(str, COLOR3B.GREEN)
		self.node_list.profess_love_count_down.text.text = string.format(Language.Activity.ActivityEndTimeDesc_1,str) --"距离排名结算："..str
	else
		if reward_start_time1 > 0 then
			local time_tab = TimeUtil.Format2TableDHM(reward_start_time1)
            str = string.format(Language.Common.TimeStr6, time_tab.min)
            str = ToColorStr(str, COLOR3B.GREEN)
			self.node_list.profess_love_count_down.text.text = string.format(Language.Activity.ActivityEndTimeDesc_2,str)--"结算中，距离奖励发放："..str
		else
			local time_tab = TimeUtil.Format2TableDHM(have_total_time)
			if time_tab.day > 0 then
				str = string.format(Language.Common.TimeStr, time_tab.day, time_tab.hour, time_tab.min)
			else
				if time_tab.hour > 0 then
					str = string.format(Language.Common.TimeStr1, time_tab.hour, time_tab.min)
				else
					str = string.format(Language.Common.TimeStr6, time_tab.min)
				end
			end
            str = ToColorStr(str, COLOR3B.GREEN)
			self.node_list.profess_love_count_down.text.text = string.format(Language.Activity.ActivityEndTimeDesc_3,str)--"已发奖，距离活动结束："..str
		end
	end
end


function ServerActivityTabView:OpenProposeRuleView() --规则
    RuleTip.Instance:SetContent(Language.OpenServer.ActProfessLoveDes, Language.OpenServer.ActProfessLoveTitle)
end

function ServerActivityTabView:OpenProposeRankView() --查看榜单
    ServerActivityWGCtrl.Instance:SendActLoverReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_BIAOBAI, 0) --请求一下信息，防止名次显示不一致
    ServerActivityWGCtrl.Instance:SendActLoverReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_BIAOBAI, 1) --请求排行信息
    ServerActivityWGCtrl.Instance:OpenProfessRankView()
end

function ServerActivityTabView:OpenProfessWall()
    ViewManager.Instance:Open(GuideModuleName.ProfessWallView)
end

function ServerActivityTabView:OpenProposeView()
    --ViewManager.Instance:Open(GuideModuleName.ChooseProfessView)
end

function ServerActivityTabView:QingyuanGoToMarry()
    MarryWGCtrl.Instance:OpenTiQinView()
end

function ServerActivityTabView:FlushProfessLove()
    local data_list = ActivePerfertQingrenWGData.Instance:GetBiaoBaiDataList()
    self.profess_love_list:SetDataList(data_list)

    local other_cfg = ActivePerfertQingrenWGData.Instance:GetQuanChengReLianCfgOther()
    local data = other_cfg.perfect_lover_item_id
    --local item_cfg = ItemWGData.Instance:GetItemConfig(data)
    --local num = item_cfg.capability_show or 0
    --self.node_list.propose_cap_value.text.text = num

    self:FlushProfessLoveRightPanel()
    --self:FlushProfessLoveModel()
    self:UpdateProfessLoveTimeShow()
end

function ServerActivityTabView:FlushProfessLoveModel()
    if not self.profess_love_model then
        self.profess_love_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["propose_model_pos"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = true,
        }
        
        self.profess_love_model:SetRenderTexUI3DModel(display_data)
        -- self.profess_love_model:SetUI3DModel(self.node_list["propose_model_pos"].transform, self.node_list["propose_model_pos"].event_trigger_listener,
        --  1, false, MODEL_CAMERA_TYPE.BASE)
    end
    local cfg = ActivePerfertQingrenWGData.Instance:GetQuanChengReLianCfgOther()
    if cfg.love_profess_resource_id and self.ache_profess_love_res ~= cfg.love_profess_resource_id then
        local res_id = cfg.love_profess_resource_id
        self.ache_profess_love_res = res_id
        local bundle, asset = ResPath.GetHaiZiModel(res_id)
        self.profess_love_model:SetMainAsset(bundle, asset,function()
            self.profess_love_model:PlaySoulAction()
        end)
    end
end

function ServerActivityTabView:FlushProfessLoveRightPanel()
    local role_vo = RoleWGData.Instance.role_vo
    local is_marry = role_vo.lover_uid > 0
    self.node_list.profess_love_my_name.text.text = role_vo.name
    self.node_list.not_marry_container:SetActive(not is_marry)
    self.node_list.marry_container:SetActive(is_marry)
    self.node_list.profess_love_plus_btn:SetActive(not is_marry)
    if is_marry then
        self.node_list.profess_love_lover_name.text.text = role_vo.lover_name
        BrowseWGCtrl.Instance:BrowRoelInfo(role_vo.lover_uid, BindTool.Bind1(self.GetProfessLoveLoverInfoRes, self))
    else
        self.my_lover_cell:SetImgBg(false)
        self.node_list.profess_love_lover_name.text.text = Language.OpenServer.Zanwu
    end
    local data = {fashion_photoframe = 0}
	data.role_id = role_vo.role_id
	data.prof = role_vo.prof
    data.sex = role_vo.sex
    self:SetProfessLoveRoleHeadData(data, self.my_head_cell)

    local data_info = ActivePerfertQingrenWGData.Instance:GetProfessForLoveInfo()
    if not IsEmptyTable(data_info) then
        if data_info.first_rank_list and data_info.first_rank_list[1].role_id > 0 then
            self:SetProfessLoveRoleHeadData(data_info.first_rank_list[1], self.first_head_cell1)
            self:SetProfessLoveRoleHeadData(data_info.first_rank_list[2], self.first_head_cell2)
            self.node_list.first_container:SetActive(true)
            self.node_list.no_first_rank:SetActive(false)
            self.node_list.first_lover_name1.text.text = data_info.first_rank_list[1].name
            self.node_list.first_lover_name2.text.text = data_info.first_rank_list[2].name
        else
            self.first_head_cell1:SetVisible(false)
            self.first_head_cell2:SetVisible(false)
            self.node_list.first_container:SetActive(false)
            self.node_list.no_first_rank:SetActive(true)
        end
        local rank_str = data_info.owner_rank_index == -1 and Language.OpenServer.RankNotExist or 
        string.format(Language.OpenServer.RankDes, data_info.owner_rank_index) 
        local score_str = string.format(Language.OpenServer.ScoreStr, data_info.owner_total_score)
        self.node_list.profess_my_cur_rank.text.text = rank_str
        self.node_list.profess_my_cur_score.text.text = score_str
        self.node_list.first_rank_score.text.text = string.format(Language.OpenServer.ScoreStr, data_info.total_score or 0)
    else
        self.first_head_cell1:SetVisible(false)
        self.first_head_cell2:SetVisible(false)
        self.node_list.first_container:SetActive(false)
        self.node_list.no_first_rank:SetActive(true)
        self.node_list.profess_my_cur_rank.text.text = Language.OpenServer.RankNotExist
        self.node_list.profess_my_cur_score.text.text = string.format(Language.OpenServer.ScoreStr, 0)
    end

end

function ServerActivityTabView:SetProfessLoveRoleHeadData(data, head)
	if not head or not data then
		return
    end
    head:SetVisible(true)
    local data1 = {fashion_photoframe = 0}
	data1.role_id = data.role_id
	data1.prof = data.prof
    data1.sex = data.sex
    head:SetImgBg(false)
	head:SetData(data1)
end

function ServerActivityTabView:GetProfessLoveLoverInfoRes(protocol)
	if not protocol then
		return
    end
    local role_info = {
        role_id = protocol.role_id,
        role_name = protocol.role_name,
        prof = protocol.prof,
        sex = protocol.sex,
        is_online = protocol.is_online,
        team_index = protocol.team_index,
        plat_type = protocol.plat_type,
        plat_name = protocol.plat_name,
        server_id = protocol.server_id,
    }
    self.my_lover_head:SetRoleInfo(role_info)
    self:SetProfessLoveRoleHeadData(protocol, self.my_lover_cell)
end

function ServerActivityTabView:OpenProfessLoveMenu()
    local role_vo = RoleWGData.Instance.role_vo
    local is_marry = role_vo.lover_uid > 0
    if not is_marry then
        MarryWGCtrl.Instance:OpenTiQinView()
    else
        self.my_lover_head:OpenMenu(nil, Vector2(303, 93))
    end
end

function ServerActivityTabView:DoADBBAnim()
    local tween_info = UITween_CONSTS.ServerActivityTab
    UITween.FakeHideShow(self.node_list["lover_propose_root"])
    UITween.AlphaShow(GuideModuleName.ServerActivityTabView, self.node_list["lover_propose_root"], 0, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)

    self:DoADBBCellsAnim()
end

function ServerActivityTabView:DoADBBCellsAnim()
    local tween_info = UITween_CONSTS.ServerActivityTab.ListCellRender
    self.node_list["propose_mid_list"]:SetActive(false)
    ReDelayCall(self, function()
        self.node_list["propose_mid_list"]:SetActive(true)
        local list =  self.profess_love_list:GetAllItems()
        local sort_list = ServerActivityWGData.Instance:GetSortListView(list)
        local count = 0
        for k,v in ipairs(sort_list) do
            if 0 ~= v.index then
                count = count + 1
            end
            v.item:PalyADBBItemAnim(count)
        end
    end, tween_info.DelayDoTime, "ADBB_Cell_Tween")
end

--========================================================================================
ProfessLoveRender = ProfessLoveRender or BaseClass(BaseRender)
function ProfessLoveRender:LoadCallBack()
	self.cell_list = {}
	for i = 1, 8 do
		self.cell_list[i] = ItemCell.New(self.node_list["cell_list"])
	end
	--self.node_list.btn_get.button:AddClickListener(BindTool.Bind(self.GetBtnClick, self))
end

function ProfessLoveRender:__delete()
    if self.cell_list then
		for k,v in pairs(self.cell_list) do
			v:DeleteMe()
		end
		self.cell_list = nil
	end
end

function ProfessLoveRender:OnFlush()
    if not self.data then
        return
    end
    if self.data.is_rank then
        self.node_list.des_text.text.text = string.format(Language.OpenServer.ProposeDestxt1, self.data.rank_max_client,
        self.data.rank_min_client,self.data.rank_val_min_client)
        --self.node_list.btn_get:SetActive(false)
        self.node_list.had_get:SetActive(false)
    else
        local my_score = ActivePerfertQingrenWGData.Instance:GetPersonTotalScoreByIndex(self.data.index)
        local color = my_score >= self.data.score_value and COLOR3B.GREEN or COLOR3B.RED
        local score_str = ToColorStr(self.data.score_value, color)
        self.node_list.des_text.text.text = string.format(Language.OpenServer.ProposeDestxt2, score_str)
        local had_get = ActivePerfertQingrenWGData.Instance:GetPersonRewardFlagByIndex(self.data.index - 1) == 1 --从0开始
        --local str = my_score >= self.data.score_value and Language.OpenServer.Get or Language.OpenServer.GoTo
        --self.node_list.btn_get:SetActive(not had_get)
        self.node_list.had_get:SetActive(had_get)
    end
    --self.cell_list:SetDataList(self.data.reward_item)

    local cell_count = 0
    for i = 1, 8 do
        if self.data.reward_item[i] then
            self.cell_list[i]:SetData(self.data.reward_item[i])
            self.cell_list[i]:SetVisible(true)
            cell_count = cell_count + 1
        else
            self.cell_list[i]:SetVisible(false)
        end
    end
    self.node_list.scroll_rect.scroll_rect.enabled = cell_count > 2
    local width = self.node_list.scroll_rect.rect.sizeDelta.x
    self.node_list.cell_list.rect.anchoredPosition = Vector2(-width/2, 0)

end

function ProfessLoveRender:PalyADBBItemAnim(item_index)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.ServerActivityTab.ListCellRender

    UITween.FakeHideShow(self.node_list["tween_root"])
    ReDelayCall(self, function()
        if self.node_list and self.node_list["tween_root"] then
            UITween.RotateAlphaShow(GuideModuleName.ServerActivityTabView, self.node_list["tween_root"], tween_info)
        end

    end, tween_info.NextDoDelay * wait_index, "adbb_item_" .. wait_index)
end
