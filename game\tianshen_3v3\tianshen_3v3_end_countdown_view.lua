-- 天神3v3结束前倒计时面板
TianShen3v3EndCountdownView = TianShen3v3EndCountdownView or BaseClass(SafeBaseView)
function TianShen3v3EndCountdownView:__init()
    self.active_close = false
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
    self:SetMaskBg(false, false)
	self:AddViewResource(0, "uis/view/tianshen_3v3_ui_prefab", "layout_tianshen_3v3_end_countdown")
end

function TianShen3v3EndCountdownView:__delete()
end

function TianShen3v3EndCountdownView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("TianShen3v3EndCountdownView") then
		CountDownManager.Instance:RemoveCountDown("TianShen3v3EndCountdownView")
    end
    self:KillTweener()
end

function TianShen3v3EndCountdownView:LoadCallBack()
	if self.fill_tween then
        return
    end

    self.total_time = self:GetEndTimeStamp() - TimeWGCtrl.Instance:GetServerTime()
    if self.total_time <= 0 then
        self:Close()
        return
    end

    self:KillTweener()
    self.node_list.yuanquan.image.fillAmount = 1
    self.node_list.tuowei_img.image.fillAmount = 1
    self.node_list.tuowei_img.rect.localRotation = Quaternion.Euler(0, 0, 225)
    self.fill_tween = self.node_list.yuanquan.image:DOFillAmount(0, self.total_time):OnUpdate(function()
        local value = self.node_list.yuanquan.image.fillAmount
        local rotate = - 360 * value + 225
        if rotate > 135 then
            self.node_list.tuowei_img.image.fillAmount = value * 4
        end
        self.node_list.tuowei_img.rect.localRotation = Quaternion.Euler(0, 0, - 360 * value + 225)
    end):OnComplete(function()
        self.fill_tween = nil
        self:Close()
    end)
    
	self.node_list["daojishi_text"].text.text = math.floor(self.total_time)
	CountDownManager.Instance:RemoveCountDown("TianShen3v3EndCountdownView")
	CountDownManager.Instance:AddCountDown("TianShen3v3EndCountdownView", BindTool.Bind1(self.UpdateCountDownTime, self), BindTool.Bind(self.Close, self), nil, self.total_time, 0.5)
end

function TianShen3v3EndCountdownView:UpdateCountDownTime(elapse_time, total_time)
	if not self.node_list["daojishi_text"] then
        self:Close()
		return
    end
    local last_time = math.floor(total_time - elapse_time)  
	self.node_list["daojishi_text"].text.text = last_time
end

function TianShen3v3EndCountdownView:OnFlush()

end

function TianShen3v3EndCountdownView:GetEndTimeStamp()
    local scene_info = TianShen3v3WGData.Instance:GetSceneInfo()
    if scene_info then 
        return scene_info.pk_end_timestamp
    end
    return 0
end

function TianShen3v3EndCountdownView:KillTweener()
    if self.fill_tween then
        self.fill_tween:Kill()
        self.fill_tween = nil
    end
end
