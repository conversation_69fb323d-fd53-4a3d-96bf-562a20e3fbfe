SocietyBeKilledView = SocietyBeKilledView or BaseClass(SafeBaseView)

function SocietyBeKilledView:__init()
	if SocietyBeKilledView.Instance then
		ErrorLog("[SocietyBeKilledView] attempt to create singleton twice!")
		return
	end
	SocietyBeKilledView.Instance = self
	-- --self.view_name = GuideModuleName.CallBoss
	-- self.is_modal = true

	self.desc_data = {}
	self:AddViewResource(0, "uis/view/society_ui_prefab", "layout_bekilled")
end

function SocietyBeKilledView:__delete()
	SocietyBeKilledView.Instance = nil
end

function SocietyBeKilledView:ReleaseCallBack()
	if nil ~= self.killer_list then
		self.killer_list:DeleteMe()
		self.killer_list  = nil
	end

	self.list_killerlist = nil
end

function SocietyBeKilledView:LoadCallBack(index, loaded_times)
	self.list_killerlist = self.node_list["list_bekilllist"]

	XUI.AddClickEventListener(self.node_list["btn_cancel_data"], BindTool.Bind1(self.OnClickBtnCancel, self))
	XUI.AddClickEventListener(self.node_list["btn_close"], BindTool.Bind1(self.OnClickBtnClose, self))
	XUI.AddClickEventListener(self.node_list["btn_close_window1"], BindTool.Bind1(self.OnClickBtnClose, self))
	XUI.AddClickEventListener(self.node_list["btn_close_window"], BindTool.Bind1(self.OnClickBtnClose, self))

	self.killer_list = AsyncListView.New(KillerItemRender, self.list_killerlist)
	self.killer_list:SetSelectCallBack(BindTool.Bind1(self.SelecItemCallBack, self))
end

function SocietyBeKilledView:OnClickBtnClose()
	self:Close()
end

function SocietyBeKilledView:ShowIndexCallBack()
	--print_error("赋值")
	if self.killer_list then
	--print_error("赋值",self.desc_data)

		self.killer_list:SetDataList(self.desc_data,0)
	end
	self:Flush()
end

function SocietyBeKilledView:OnFlush(param_t, index)
	-- XUI.SetButtonEnabled(self.node_t_list.btn_cancel_data.node, self.select_index ~= -1)
end

function SocietyBeKilledView:SelecItemCallBack(item, index)
	self.select_index = index
	self:Flush()
end

function SocietyBeKilledView:CloseCallBack()
	self.select_index = -1
end

function SocietyBeKilledView:SetKillerData(data)
	if not data then return end

	local tab = {}
	tab.killer_name = data.killer_name
	tab.now_timetemp = data.now_timetemp
	tab.role_id = data.role_id
	tab.m_prof = data.m_prof
	tab.index = #self.desc_data + 1
	if #self.desc_data >= 20 then
		table.remove(self.desc_data, 1)
		table.insert(self.desc_data, tab)
	else
		table.insert(self.desc_data, tab)
	end
end

function SocietyBeKilledView:OpenView()
	self:Open()
end

function SocietyBeKilledView:OnClickBtnCancel()
	if -1 == self.select_index then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.SelectTip)
		return
	end
	table.remove(self.desc_data, self.select_index)
	self.killer_list:SetDataList(self.desc_data)
	self.killer_list:CancelSelect()
	self.select_index = -1
	self:Flush()
end

function SocietyBeKilledView:RemoveItemByIndex(index)

end


-----------------------------KillerItemRender
------------------------------------------------------------------------
KillerItemRender = KillerItemRender or BaseClass(BaseRender)
function KillerItemRender:__init()

end

function KillerItemRender:__delete()

end

-- function KillerItemRender:CreateChild()
-- 	BaseRender.CreateChild(self)

-- end

function KillerItemRender:OnFlush()
--print_error(self.data)
	self.node_list["lbl_killer_time"].text.text = os.date("%Y-%m-%d %X", self.data.now_timetemp)
	self.node_list["lbl_killer_desc"].text.text = string.format(Language.Society.KillerIndex,self.index)
	self.node_list["lbl_killer_name"].text.text = self.data.killer_name
	local bundle,asset = ResPath.GetRoleHeadIconSociety(self.data.m_prof)
	self.node_list["icon"].image:LoadSprite(bundle,asset)
--print_error(self.data)
	--self.node_list["img_head"].transform.localScale = Vector3(0.24, 0.24, 0.24)

	--local prof = self.data.m_prof > 10 and math.floor(self.data.m_prof % 10) or self.data.m_prof
	--AvatarManager.Instance:UpdateAvatarImg(self.node_tree.img_head.node, self.data.role_id, self.data.m_prof, true)
end
function KillerItemRender:OnSelectChange(is_select)
	self.node_list.Is_select:SetActive(is_select)
end