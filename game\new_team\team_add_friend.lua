TeamAddFriend = TeamAddFriend or BaseClass(SafeBaseView)
local this = TeamAddFriend
local TeamAddFriend_Count = 0
function this:__init()
	self.view_cache_time = 0
	self:SetMaskBg()
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(740, 426)})
	self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_team_addfriend")
end

function this:__delete()
end

function this:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.BiZuoBaoXiang.AlertCancelBtnText
	XUI.AddClickEventListener(self.node_list.btn_add, BindTool.Bind(self.AddFriend, self))
	self.member_info_list = {}
	
	for i = 1, 4 do
		if self.add_frient_list[i] then
			self.member_info_list[i] = TeamFriendAddInfo.New(self.node_list["info"..i])
			self.member_info_list[i]:SetData(self.add_frient_list[i])
			self.member_info_list[i]:SetCallBack(function ()
				self:Close()
			end)
		else
			self.node_list["info" .. i]:SetActive(false)
		end
	end

end

function this:SetData(add_frient_list, title)
	self.add_frient_list = add_frient_list
	TeamAddFriend_Count = #self.add_frient_list
	self.title = title
end

function this:ReleaseCallBack()
	if self.member_info_list then
		for k, v in pairs(self.member_info_list) do
			self.member_info_list[k]:DeleteMe()
		end
		self.member_info_list = nil
	end
end

function this:OpenCallBack()

end

function this:CloseCallBack()
	
end

function this:AddFriend()
	for k, v in pairs(self.add_frient_list) do
		SocietyWGCtrl.Instance:AddFriend(v.orgin_role_id, 0)
	end

	-- SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.)
	TeamAddFriend_Count = 0
	self:Close()
end


-------------------------------------------------------------------------------------------
--- TeamFriendAddInfo
-------------------------------------------------------------------------------------------

TeamFriendAddInfo = TeamFriendAddInfo or BaseClass(BaseRender)
function TeamFriendAddInfo:__init()
	self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
	XUI.AddClickEventListener(self.node_list.btn_add_single, BindTool.Bind(self.AddFriend, self))
end
function TeamFriendAddInfo:__delete()
	if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end
end
function TeamFriendAddInfo:OnFlush()
	if not self.data then return end
	local is_empty_data = IsEmptyTable(self.data)

	--没有队员信息，隐藏相关信息
	if is_empty_data then
		self.view:SetActive(false)
		return
	end
	self.node_list.send:SetActive(false)
	self.node_list.btn_add_single:SetActive(true)
	self.node_list.no_data_hide:SetActive(true)

	if self.data.orgin_role_id > 0 then
		local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
		--人物名字
		self.node_list["role_name"].text.text = self.data.name

		--Vip等级
		-- local bundle, asset = ResPath.GetVipIcon("vip" .. self.data.vip_level)
		-- self.node_list.vip_level.image:LoadSprite(bundle, asset, function()
		-- 	self.node_list.vip_level.image:SetNativeSize()
		-- end)

		-- self.node_list.vip_level:SetActive(self.data.vip_level > 0)
		-- self.node_list.vip_level.text.text = string.format(Language.NewTeam.VipLevel, self.data.vip_level)
		local str = ""
		local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
		if is_vis then
			str = string.format(Language.Role.FeiXianDesc3, role_level)
		else
			str = string.format(Language.NewTeam.TimeInviteRoleName, role_level)
		end
		--人物等级
		self.node_list["role_level"].text.text = str--string.format(Language.NewTeam.TimeInviteRoleName, self.data.level)
		----头像
		local data = {}
		data.role_id = self.data.orgin_role_id
		data.prof = self.data.prof
		data.sex = self.data.sex
		data.fashion_photoframe = self.data.shizhuang_photoframe
		self.head_cell:SetData(data)
	end
end

function TeamFriendAddInfo:AddFriend()
	SocietyWGCtrl.Instance:AddFriend(self.data.orgin_role_id,0)
	self.node_list.send:SetActive(true)
	self.node_list.btn_add_single:SetActive(false)
	TeamAddFriend_Count = TeamAddFriend_Count - 1
	if TeamAddFriend_Count == 0 then
		self.out_call_back()
	end
end

function TeamFriendAddInfo:SetCallBack(callback)
	self.out_call_back = callback
end