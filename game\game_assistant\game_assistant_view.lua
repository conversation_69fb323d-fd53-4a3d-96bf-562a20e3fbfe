-- 霸服指引（小助手）
GameAssistantView = GameAssistantView or BaseClass(SafeBaseView)
local MAX_BIG_TYPE = 4
local MAX_SUB_TYPE = 3

function GameAssistantView:__init()
	self:SetMaskBg(false)
	self.view_style = ViewStyle.Half
	self.is_safe_area_adapter = true
	self.default_index = TabIndex.assistant_tongpiao

    local common_bundle = "uis/view/common_panel_prefab"
    local bundle_name = "uis/view/game_assistant_prefab"
	self:AddViewResource(0, bundle_name, "layout_game_assistant_bg_panel")
    self:AddViewResource(TabIndex.assistant_tongpiao, bundle_name, "layout_game_assistant_tongpiao_view")
	self:AddViewResource(TabIndex.assistant_today_act, bundle_name, "layout_game_assistant_today_act_view")
	self:AddViewResource({TabIndex.assistant_today_must, TabIndex.assistant_want_lingyu}, bundle_name, "layout_game_assistant_xianyu_view")
	self:AddViewResource({TabIndex.assistant_hot_buy}, bundle_name, "layout_game_assistant_fashion_view")
	self:AddViewResource({TabIndex.assistant_want_exp, TabIndex.assistant_want_equip}, bundle_name, "layout_game_assistant_want_view")
	self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar_Activity")
	self:AddViewResource(0, common_bundle, "layout_a3_common_top_panel")
end

function GameAssistantView:OpenCallBack()
	self:OpenAssistantWant()
end

function GameAssistantView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.GameAssistant.TitleName

	self:InitTabbar()
end

-- 关闭前调用
function GameAssistantView:CloseCallBack()
	-- self:CloseExchangeCallBack()
	self:CloseAssistantTodayActivity()
	self:CloseAssistantXianYu()
	self:CloseAssistantFashion()
	self:CloseAssistantWant()
end

function GameAssistantView:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	self:ReleaseAssistantTodayActivity()
	self:ReleaseAssistantXianYu()
	self:ReleaseAssistantFashion()
	self:ReleaseAssistantWant()
end

function GameAssistantView:InitTabbar()
	if self.tabbar then
		return
	end

	local tb = GameAssistantWGData.Instance:getAllType()
	self.tab_name_list = {}
	self.remind_name_list = {}

	for k, v in ipairs(tb) do
		self.tab_name_list[k] = v.assistant_name
	end


	self.tabbar = Tabbar.New(self.node_list, "VerticalTabbar_Activity")
	self.tabbar:SetVerTabbarCellName("VerticalTabbarCell_Activity")
	self.tabbar:Init(self.tab_name_list, nil, ResPath.CommonBundleName, nil, self.remind_name_list)
	self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	self:SetTabSate()
end

function GameAssistantView:SetTabSate()
	self.tabbar:SetToggleVisible(TabIndex.assistant_tongpiao,
		GameAssistantWGData.Instance:getTypeState(TabIndex.assistant_tongpiao))

	self.tabbar:SetToggleVisible(TabIndex.assistant_today_act,
		GameAssistantWGData.Instance:getTypeState(TabIndex.assistant_today_act))
	
	self.tabbar:SetToggleVisible(TabIndex.assistant_today_must,
		GameAssistantWGData.Instance:getTypeState(TabIndex.assistant_today_must))

		
	self.tabbar:SetToggleVisible(TabIndex.assistant_hot_buy,
	GameAssistantWGData.Instance:getTypeState(TabIndex.assistant_hot_buy))

	-- self.tabbar:SetToggleVisible(TabIndex.assistant_fashion,
	-- 	GameAssistantWGData.Instance:getTypeState(TabIndex.assistant_fashion))

	self.tabbar:SetToggleVisible(TabIndex.assistant_want_lingyu,
		GameAssistantWGData.Instance:getTypeState(TabIndex.assistant_want_lingyu))

	self.tabbar:SetToggleVisible(TabIndex.assistant_want_exp,
		GameAssistantWGData.Instance:getTypeState(TabIndex.assistant_want_exp))
	
	self.tabbar:SetToggleVisible(TabIndex.assistant_want_equip,
		GameAssistantWGData.Instance:getTypeState(TabIndex.assistant_want_equip))
end

function GameAssistantView:LoadIndexCallBack(index)
	if index == TabIndex.assistant_today_act then
		self:LoadAssistantTodayActivity()
	elseif index == TabIndex.assistant_today_must or index == TabIndex.assistant_want_lingyu then
		self:LoadAssistantXianYu()
	elseif index == TabIndex.assistant_hot_buy or index == TabIndex.assistant_fashion then
		self:LoadAssistantFashion()
	elseif index == TabIndex.assistant_want_exp or index == TabIndex.assistant_want_equip then
		self:LoadAssistantWant()
	end
end

function GameAssistantView:ShowIndexCallBack(index)
	if index == TabIndex.assistant_today_act then
		self:ShowAssistantTodayActivity()
	elseif index == TabIndex.assistant_today_must or index == TabIndex.assistant_want_lingyu then
		self:ShowAssistantXianYu()
	elseif index == TabIndex.assistant_hot_buy or index == TabIndex.assistant_fashion then
		self:ShowAssistantFashion()
	elseif index == TabIndex.assistant_want_exp or index == TabIndex.assistant_want_equip then
		self:ShowAssistantWant()
	end
end

function GameAssistantView:OnFlush(param_t, index)
	if index == TabIndex.assistant_today_act then
		self:FlushAssistantTodayActivity(param_t)
	elseif index == TabIndex.assistant_today_must or index == TabIndex.assistant_want_lingyu then
		self:FlushAssistantXianYu(param_t)
	elseif index == TabIndex.assistant_hot_buy or index == TabIndex.assistant_fashion then
		self:FlushAssistantFashion(param_t)
	elseif index == TabIndex.assistant_want_exp or index == TabIndex.assistant_want_equip then
		self:FlushAssistantWant(param_t)
	end
end

