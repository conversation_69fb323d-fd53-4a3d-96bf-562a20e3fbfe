FlopDrawView = FlopDrawView or BaseClass(SafeBaseView)

function FlopDrawView:__init()
    self.view_style = ViewStyle.Half
	self.is_safe_area_adapter = true
	self:SetMaskBg()

	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
	self:AddViewResource(0, "uis/view/flop_draw_ui_prefab", "layout_flop_darw")
	self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar_Activity")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function FlopDrawView:__delete()
end

function FlopDrawView:ReleaseCallBack()
    if self.show_cell_list then
        self.show_cell_list:DeleteMe()
        self.show_cell_list = nil
    end

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

    if self.task_list then
        self.task_list:DeleteMe()
        self.task_list = nil
    end

    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

    if self.step_reward_list then
        for _, v in pairs(self.step_reward_list) do
            v:DeleteMe()
        end
        self.step_reward_list = nil
    end

    if self.alert ~= nil then
        self.alert:DeleteMe()
        self.alert = nil
    end

    self.alert = nil
    self.old_step_flag = nil
    self.is_play_step_tween = false

    if CountDownManager.Instance:HasCountDown("flop_draw_time") then
        CountDownManager.Instance:RemoveCountDown("flop_draw_time")
    end
end

function FlopDrawView:OpenCallBack()
    FlopDrawWGCtrl.Instance:ReqFlopDrawInfo(OA_FLOP_DRAW_OPERTE_TYPE.INFO)
end

function FlopDrawView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.FlopDraw.TitleName
	local bundle, asset = ResPath.GetRawImagesPNG("a3_whfc_bg_1")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

    if not self.show_cell_list then
        self.show_cell_list = AsyncListView.New(ItemCell, self.node_list.show_cell_list)
    end

    if not self.task_list then
        self.task_list = AsyncListView.New(FlopDrawTaskCell, self.node_list.task_list)
    end

    XUI.AddClickEventListener(self.node_list["flop_btn"], BindTool.Bind(self.OnClickStepBtn, self))
    XUI.AddClickEventListener(self.node_list["once_flop_btn"], BindTool.Bind(self.OnClickOnceStepBtn, self)) --一键
    XUI.AddClickEventListener(self.node_list["mask_lock"], BindTool.Bind(self.ClickMask, self))
    self.node_list["cost_icon"].button:AddClickListener(BindTool.Bind(self.ShowCostItemTips, self))
    self.node_list["need_cost_icon"].button:AddClickListener(BindTool.Bind(self.ShowCostItemTips, self))
	XUI.AddClickEventListener(self.node_list.probability_show_btn, BindTool.Bind(self.OpenGaiLvView, self))

    self.step_reward_list = {}
	self:InitTabbar()
    self:InitMoneyBar()
    self:InitStepPanel()
end

function FlopDrawView:ShowIndexCallBack()
    self:FlushTimeCount()
    ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.FlopDrawView, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CAT_VENTURE2)
end

function FlopDrawView:OnFlush(param_t, index)
    for k,v in pairs(param_t) do
        if k == "all" then
            self:FlushAllView()
            self.is_play_step_tween = false
        elseif k == "flush_task" then
            self:FlushRightView()
        elseif k == "flush_flop_step" then
            self.is_play_step_tween = true
            self:FlushCostView()
            self:FlushStepView()
        elseif k == "flush_cost" then
            self:FlushCostView()
        end
    end
end

function FlopDrawView:InitTabbar()
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list, "VerticalTabbar_Activity")
		self.tabbar:SetVerTabbarCellName("VerticalTabbarCell_Activity")
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		self.tabbar:Init({ Language.FlopDraw.TitleName }, nil, ResPath.CommonBundleName, nil, nil)
	end
end

function FlopDrawView:InitMoneyBar()
    self.money_bar = MoneyBar.New()
    local bundle, asset = ResPath.GetWidgets("MoneyBar")
    local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
    self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
end

function FlopDrawView:InitStepPanel()
    local res_async_loader = AllocResAsyncLoader(self, "step_reward_cell")
    res_async_loader:Load("uis/view/flop_draw_ui_prefab", "step_reward_cell", nil,
        function(new_obj)
            if not new_obj then
                return
            end

            local item_root = self.node_list.reward_group
            local item_list = {}
            for i = 1, FlopDrawWGData.MAX_STEP_REWARD_COUNT do
                local obj_root = item_root:FindObj("reward_item_" .. i)
                if obj_root then
                    local obj = ResMgr:Instantiate(new_obj)
                    obj.transform:SetParent(obj_root.transform, false)
                    item_list[i] = FlopDrawStepRewardCell.New(obj)
                    item_list[i]:SetIndex(i)
                end
            end
            self.step_reward_list = item_list
            self:FlushMidView()
        end)
end

function FlopDrawView:FlushAllView()
    self:FlushRightView()
    self:FlushCostView()
    self:FlushMidView()
    self:FlushStepView()
end

function FlopDrawView:FlushMidView()
    local reward_cfg = FlopDrawWGData.Instance:GetAllStepRewardCfg()
    for i, v in ipairs(self.step_reward_list) do
        if reward_cfg and reward_cfg[i] then
            v:SetData(reward_cfg[i])
        end
    end
end

function FlopDrawView:FlushRightView()
    local item_show_data = FlopDrawWGData.Instance:GetShowRewardCfg()
    local show_data = SortDataByItemColor(item_show_data)
    if self.show_cell_list then
        self.show_cell_list:SetDataList(show_data)
    end

    local all_task_data = FlopDrawWGData.Instance:GetAllTaskList()
    self.task_list:SetDataList(all_task_data)
end

function FlopDrawView:FlushCostView()
    local cur_step = FlopDrawWGData.Instance:GetCurStep()
    local other_cfg = FlopDrawWGData.Instance:GetOtherCfg()
    local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.cost_item_id)
    if IsEmptyTable(item_cfg) then
        return
    end

    local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.cost_item_id)
    local cost_num = FlopDrawWGData.Instance:GetStepCostNum(cur_step + 1)
    
    self.node_list["cost_icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
    self.node_list["need_cost_icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
    self.node_list.cost_item_num.text.text = item_num
    local str = item_num .. "/" .. cost_num
   -- local color = item_num >= cost_num and COLOR3B.D_GREEN or COLOR3B.D_RED
    self.node_list.need_cost_item_num.text.text = str
    local is_remind = FlopDrawWGData.Instance:ShowStepBtnRemind()
    self.node_list.step_remind:SetActive(is_remind)
end

function FlopDrawView:FlushStepView()
    self.node_list.mask_lock:SetActive(self.is_play_step_tween)
    local all_step_flag = FlopDrawWGData.Instance:GetAllStepState()
    if self.is_play_step_tween then
        GlobalTimerQuest:AddDelayTimer(function()
            self.node_list.mask_lock:SetActive(false)
            self.is_play_step_tween = false
        end, 0.5)

        for k, v in pairs(all_step_flag) do
            if v == 1 and self.old_step_flag and self.old_step_flag[k] == 0 then
                if self.step_reward_list[k] then
                    self.step_reward_list[k]:PlayTween()
                end
            end
        end
    end

    self.old_step_flag = all_step_flag
end

function FlopDrawView:FlushTimeCount()
    local time, next_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CAT_VENTURE2)
    if time > 0 then
        if CountDownManager.Instance:HasCountDown("flop_draw_time") then
            CountDownManager.Instance:RemoveCountDown("flop_draw_time")
        end

        CountDownManager.Instance:AddCountDown("flop_draw_time", 
            BindTool.Bind(self.FinalUpdateTimeCallBack, self), 
            BindTool.Bind(self.OnComplete, self), 
            nil, time, 1)
    else
        self:OnComplete()
    end
end

function FlopDrawView:FinalUpdateTimeCallBack(now_time, total_time)
    local time = math.ceil(total_time - now_time)
    local time_str = TimeUtil.FormatSecondDHM8(time)
    self.node_list["act_time"].text.text = string.format(Language.FlopDraw.ActivityTime, time_str) 
end

function FlopDrawView:OnComplete()
    self.node_list.act_time.text.text = ""
end

function FlopDrawView:OnClickStepBtn()
    local cur_step = FlopDrawWGData.Instance:GetCurStep()
    if cur_step >= FlopDrawWGData.MAX_STEP_REWARD_COUNT  then
        TipWGCtrl.Instance:ShowSystemMsg(Language.FlopDraw.AllBuy)
        return
    end

    local other_cfg = FlopDrawWGData.Instance:GetOtherCfg()
    local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.cost_item_id)
    if not item_cfg then
        print_error("=====物品id不存在===", other_cfg.cost_item_id)
        return
    end

    local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.cost_item_id)
    local cost_num = FlopDrawWGData.Instance:GetStepCostNum(cur_step + 1)
    if item_num >= cost_num then
        FlopDrawWGCtrl.Instance:ReqFlopDrawInfo(OA_FLOP_DRAW_OPERTE_TYPE.STEP, 0)
    else
        if not self.alert then
            self.alert = Alert.New()
        end

        self.alert:ClearCheckHook()
        self.alert:SetShowCheckBox(true, "flop_draw")
        self.alert:SetCheckBoxDefaultSelect(false)
        local name = ""
        if item_cfg ~= nil then
            name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
        end

        local cost = other_cfg.cost_gold * (cost_num - item_num)
        local str = string.format(Language.FlopDraw.CostStr, name, cost)
        self.alert:SetLableString(str)
        local ok_func = function ()
            local have_enough = RoleWGData.Instance:GetIsEnoughUseGold(cost)
            if have_enough then
                FlopDrawWGCtrl.Instance:ReqFlopDrawInfo(OA_FLOP_DRAW_OPERTE_TYPE.STEP, 0)
            else
                UiInstanceMgr.Instance:ShowChongZhiView()
            end
        end

        self.alert:SetOkFunc(ok_func)
        self.alert:Open()
    end 
end

function FlopDrawView:OnClickOnceStepBtn()
    local cur_step = FlopDrawWGData.Instance:GetCurStep()
    if cur_step >= FlopDrawWGData.MAX_STEP_REWARD_COUNT  then
        TipWGCtrl.Instance:ShowSystemMsg(Language.FlopDraw.AllBuy)
        return
    end

    local other_cfg = FlopDrawWGData.Instance:GetOtherCfg()
    local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.cost_item_id)
    if not item_cfg then
        print_error("=====物品id不存在===", other_cfg.cost_item_id)
        return
    end

    local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.cost_item_id)
    local cost_num = 0
    for i = cur_step + 1, FlopDrawWGData.MAX_STEP_REWARD_COUNT do
        cost_num = cost_num + FlopDrawWGData.Instance:GetStepCostNum(i)
    end

    if item_num >= cost_num then
        FlopDrawWGCtrl.Instance:ReqFlopDrawInfo(OA_FLOP_DRAW_OPERTE_TYPE.STEP, 1)
        GlobalTimerQuest:AddDelayTimer(function()
            FlopDrawWGCtrl.Instance:ReqFlopDrawInfo(OA_FLOP_DRAW_OPERTE_TYPE.STEP_ALL_REWARD)
            end, 1)
    else
        if not self.alert then
            self.alert = Alert.New()
        end

        self.alert:ClearCheckHook()
        self.alert:SetShowCheckBox(true, "once_flop_draw")
        self.alert:SetCheckBoxDefaultSelect(false)
        local name = ""
        if item_cfg ~= nil then
            name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
        end

        local cost = other_cfg.cost_gold * (cost_num - item_num)
        local str = string.format(Language.FlopDraw.CostStr, name, cost)
        self.alert:SetLableString(str)
        local ok_func = function ()
            local have_enough = RoleWGData.Instance:GetIsEnoughUseGold(cost)
            if have_enough then
                FlopDrawWGCtrl.Instance:ReqFlopDrawInfo(OA_FLOP_DRAW_OPERTE_TYPE.STEP, 1)
                GlobalTimerQuest:AddDelayTimer(function()
                    FlopDrawWGCtrl.Instance:ReqFlopDrawInfo(OA_FLOP_DRAW_OPERTE_TYPE.STEP_ALL_REWARD)
                    end, 1)
            else
                UiInstanceMgr.Instance:ShowChongZhiView()
            end
        end

        self.alert:SetOkFunc(ok_func)
        self.alert:Open()
    end 
end

function FlopDrawView:ClickMask()
    TipWGCtrl.Instance:ShowSystemMsg(Language.FlopDraw.IsPlayingTrun)
end

function FlopDrawView:ShowCostItemTips()
    local other_cfg = FlopDrawWGData.Instance:GetOtherCfg()
    local item_id = other_cfg.cost_item_id
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id}) 
end

function FlopDrawView:OpenGaiLvView()
    local info = FlopDrawWGData.Instance:GetGaiLvInfo()
    TipWGCtrl.Instance:OpenTipsRewardProView(info)
end

--------------------------------任务格子----------------
FlopDrawTaskCell = FlopDrawTaskCell or BaseClass(BaseRender)

function FlopDrawTaskCell:__init()
    self.node_list["go_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGo,self))
    self.node_list["get_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGetTaskReward,self))
end

function FlopDrawTaskCell:__delete()
end

function FlopDrawTaskCell:OnFlush()
    if not self.data then
        return
    end

    self.node_list.go_btn:SetActive(self.data.status == REWARD_STATE_TYPE.UNDONE)
    self.node_list.get_btn:SetActive(self.data.status == REWARD_STATE_TYPE.CAN_FETCH)
    self.node_list.have_get:SetActive(self.data.status == REWARD_STATE_TYPE.FINISH)

    self.node_list.target_text:SetActive(self.data.task_type ~= 2 and self.data.task_type ~= 3)
    local str = self.data.des 
    local num = self.data.item_list[0].num 
    self.node_list.desc_text.text.text = string.format(Language.FlopDraw.Desc, str, num)
    local count_str = self.data.progress_num_flag .. "/" .. self.data.target
    self.node_list.target_text.text.text = string.format(Language.FlopDraw.TaskProgress, count_str)

    local item_id = self.data.item_list[0].item_id
    local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
    if item_cfg then
        self.node_list["icon_img"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
    end
end

function FlopDrawTaskCell:OnClickGo()
    if self.data.open_panel ~= "" then
        FunOpen.Instance:OpenViewNameByCfg(self.data.open_panel)
    end
end

function FlopDrawTaskCell:OnClickGetTaskReward()
    FlopDrawWGCtrl.Instance:ReqFlopDrawInfo(OA_FLOP_DRAW_OPERTE_TYPE.TASK_REWARD, self.data.task_id)
end

-------------------步数奖励格子-------------------
 FlopDrawStepRewardCell = FlopDrawStepRewardCell or BaseClass(BaseRender)

function FlopDrawStepRewardCell:__init()
    self.reward_item = ItemCell.New(self.node_list["reward_item_pos"])
    self.node_list["get_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGetStepReward, self))
    self.node_list["lock_img"].button:AddClickListener(BindTool.Bind(self.OnClickLock, self))
end

function FlopDrawStepRewardCell:__delete()
    if self.reward_item then
        self.reward_item:DeleteMe()
        self.reward_item = nil
    end
end

function FlopDrawStepRewardCell:OnFlush()
    if not self.data then
        return
    end

    local is_flop = FlopDrawWGData.Instance:GetStepState(self.data.step)
    if is_flop then
        local item_id = self.data.item.item_id
        self.reward_item:SetData({item_id = item_id})       
        self.reward_item:SetRightBottomColorText(self.data.item.num)
        self.reward_item:SetRightBottomTextVisible(true)
    else
        self.reward_item:SetData(nil)  
    end

    local is_get = FlopDrawWGData.Instance:GetRewardStateByStep(self.data.step)
    self.node_list.canget_img:SetActive(not is_get and is_flop)
    self.node_list.get_btn:SetActive(not is_get)
    self.node_list.already_get:SetActive(is_get)
    self:ReInitPosition()
end

function FlopDrawStepRewardCell:OnClickGetStepReward()
    if not self.data then
        return
    end

    local is_get = FlopDrawWGData.Instance:GetRewardStateByStep(self.data.step)  
    if is_get then
        TipWGCtrl.Instance:ShowSystemMsg(Language.FlopDraw.IsGetReward)
        return
    end

    local is_flop = FlopDrawWGData.Instance:GetStepState(self.data.step)
    if is_flop then
        FlopDrawWGCtrl.Instance:ReqFlopDrawInfo(OA_FLOP_DRAW_OPERTE_TYPE.STEP_REWARD, self.data.step)
    else
        TipWGCtrl.Instance:ShowSystemMsg(Language.FlopDraw.NotStep)
    end
end

function FlopDrawStepRewardCell:OnClickLock()
    TipWGCtrl.Instance:ShowSystemMsg(Language.FlopDraw.LockTips)
end

function FlopDrawStepRewardCell:ReInitPosition()
    if not self.data then
        return
    end

    if self.playing_truning  then
        return
    end

    self.node_list.tween_root.transform.rotation = Quaternion.Euler(0, 0, 0)
    local is_flop = FlopDrawWGData.Instance:GetStepState(self.data.step)
    self.node_list.lock_img:SetActive(not is_flop)
end

function FlopDrawStepRewardCell:PlayTween()
    if self.playing_truning  then
        return
    end

    self.playing_truning = true
    self.node_list["tween_root"].transform.rotation = Quaternion.Euler(0, 180,0)
    self.node_list["lock_img"]:SetActive(true)

    local base_time = 0
    local time1 = 0.2
    local time2 = 0.2

    self.turning_anim1 =  GlobalTimerQuest:AddDelayTimer(function ()
        self.node_list["tween_root"].rect:DORotate(Vector3(0, 90, 0), time1, DG.Tweening.RotateMode.FastBeyond360)
    end, base_time)

    self.turning_anim2 = GlobalTimerQuest:AddDelayTimer(function ()
        self.node_list["tween_root"].rect:DORotate(Vector3(0, 0, 0), time1, DG.Tweening.RotateMode.FastBeyond360)
        self.node_list["lock_img"]:SetActive(false)
        self:Flush()
    end, base_time+time1)

    self.turning_anim3 = GlobalTimerQuest:AddDelayTimer(function ()
        self.playing_truning = false
        self:ReInitPosition()
    end, base_time + time1 + time2)
end