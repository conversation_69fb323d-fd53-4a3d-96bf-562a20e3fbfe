local COIN_DATA = {is_bind = 0, item_id = 65535} 		--铜币

function EquipmentView:InitStrengthView()
	if not self.sth_equip_body_list then
		self.sth_equip_body_list = AsyncListView.New(STHEquipBodyList<PERSON>ellRender, self.node_list.sth_equip_body_list)
		self.sth_equip_body_list:SetStartZeroIndex(false)
		self.sth_equip_body_list:SetSelectCallBack(BindTool.Bind(self.OnSelectSthEquipBodyHandler, self))
		self.sth_equip_body_list:SetEndScrolledCallBack(BindTool.Bind(self.SthEquipBodyListSetEndScrollCallBack, self))
	end

	if not self.equip_qh_item_cell then
		self.equip_qh_item_cell = ItemCell.New(self.node_list["strength_eq_item"])
		self.equip_qh_item_cell:SetItemTipFrom(ItemTip.FROM_EQUIPMENT)
	end

	if not self.equip_qh_list then
	    self.equip_qh_list = {}
	    for part = 0, GameEnum.EQUIP_INDEX_XIANZHUO do
	        self.equip_qh_list[part] = EquipStrengthItemRender.New(self.node_list.strength_equip_list:FindObj("item_" .. part))
	        self.equip_qh_list[part]:SetIndex(part)
	        self.equip_qh_list[part]:SetClickCallBack(BindTool.Bind(self.OnClickStrengthListCallBack, self))
	    end
    end

	if nil == self.strength_attr_list then
		self.strength_attr_list = {}
		local attr_num = self.node_list.strength_attr_list.transform.childCount
		for i = 1, attr_num do
		    local cell = CommonAddAttrRender.New(self.node_list["sth_attr_" .. i])
		    cell:SetIndex(i)
			cell:SetAttrNameNeedSpace(true)
		    self.strength_attr_list[i] = cell
		end
	end

	self.strength_select_data = nil
	-- self.last_level = -1
	-- self.last_index = -1
	self.equip_up_struff = {} --强化石缓存
	self.qh_old_progress_value = nil
	self.need_strength_coin = 0

	self.sth_jump_equip_body_seq = -1
	self.sth_jump_equip_body_equip_data = {}
	self.sth_need_equip_body_tween = true
	self.strength_select_index = -1

	self.is_strength_auto_up = false

	XUI.AddClickEventListener(self.node_list["btn_equip_qh"], BindTool.Bind(self.OnBtnEquipStrengthHandeler, self, 0)) --强化
	XUI.AddClickEventListener(self.node_list["layout_btn_strength_tips"], BindTool.Bind(self.OnBtnEquipStrengthTips, self))
	XUI.AddClickEventListener(self.node_list["btn_equip_qh_auto"], BindTool.Bind(self.AutoEquipStrength, self)) --自动强化
	XUI.AddClickEventListener(self.node_list["strength_stuff_material_btn"], BindTool.Bind(self.ShowStrengthItemTips, self, false))
	XUI.AddClickEventListener(self.node_list["strength_stuff_coin_btn"], BindTool.Bind(self.ShowStrengthItemTips, self, true))
	XUI.AddClickEventListener(self.node_list["btn_sth_equip_body"], BindTool.Bind(self.OnClickSthEquipBodyBtn, self))
end

function EquipmentView:StrengthDeleteMe()
	if self.equip_qh_item_cell then
		self.equip_qh_item_cell:DeleteMe()
		self.equip_qh_item_cell = nil
	end

	if self.equip_qh_list then
		for k,v in pairs(self.equip_qh_list) do
			v:DeleteMe()
		end
		self.equip_qh_list = nil
	end

	if self.strength_attr_list then
        for k,v in pairs(self.strength_attr_list) do
            v:DeleteMe()
        end
        self.strength_attr_list = nil
    end

	-- if self.strength_coin_stuff_cell then
	-- 	self.strength_coin_stuff_cell:DeleteMe()
	-- 	self.strength_coin_stuff_cell = nil
	-- end

	if self.attri_item then
		for k,v in pairs(self.attri_item) do
			v:DeleteMe()
		end
		self.attri_item = nil
	end

	self.equip_qh_list_flag = nil
	-- self.last_level = -1
	-- self.last_index = -1
	self.qh_old_progress_value = nil
	self.equip_up_struff = nil

    if self.strength_model_tween then
		self.strength_model_tween:Kill()
		self.strength_model_tween = nil
	end

	if self.sth_equip_body_list then
		self.sth_equip_body_list:DeleteMe()
		self.sth_equip_body_list = nil
	end

	self.sth_jump_equip_body_seq = nil
	self.sth_jump_equip_body_equip_data = nil
	self.sth_need_equip_body_tween = nil

	self.is_strength_auto_up = nil
end

------------------------------------------Guide_Start-----------------------------------------
function EquipmentView:GetEquipmentIntensifyListCallBack()
	local cur_data = self:GetCurSelectStrengthData()
	local equip_list, default_index = EquipmentWGData.Instance:GetRoleEquipStrengthRemind(true, cur_data)
	if not equip_list then
		return
	end

	if self.equip_qh_list and self.equip_qh_list[default_index] then
		return self.equip_qh_list[default_index]:GetView(), nil
	else
		return NextGuideStepFlag
	end
end

function EquipmentView:GetBtnEquipQhNode()
	if self.node_list["btn_equip_qh"] then
		return self.node_list["btn_equip_qh"], BindTool.Bind(self.OnBtnEquipStrengthHandeler, self, 0)
	end
end

function EquipmentView:GetBtnEquipAutoQhNode()
	return self.node_list["btn_equip_qh_auto"], BindTool.Bind(self.AutoEquipStrength, self)
end
-------------------------------------------Guide_End------------------------------------------

function EquipmentView:ShowFlushEquipStrengthIndexCallBack()
	self.strength_select_index = -1
	self.sth_select_equip_body_seq = nil
	self.is_strength_auto_up = false
end

-- 刷新淬火界面
function EquipmentView:FlushEquipStrength(jump_data)
	local total_equip_body_data_list = EquipBodyWGData.Instance:GetDZEquipBodyDataList()
	self.sth_equip_body_list:SetDataList(total_equip_body_data_list)

	if jump_data and jump_data.item_index then
		local equip_body_seq = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(jump_data.item_index)
		local selct_part_data = EquipWGData.Instance:GetGridData(jump_data.item_index)

		self:StrengthChangeToTargetEquipBody({equip_body_seq = equip_body_seq, selct_part_data = selct_part_data})
		return
	end

	local body_seq = jump_data and jump_data.open_param
	local jump_to_body_index = nil
	if body_seq then
		body_seq = tonumber(body_seq)
		-- 任务跳转，如果已解锁并已穿戴，直接跳到指定肉身
		local can_duanzao, is_unlock, is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyCanDuanZao(body_seq)
		if is_unlock and is_wear_equip then
			for k, v in pairs(total_equip_body_data_list) do
				if v.seq == body_seq then
					jump_to_body_index = k
					break
				end
			end
		end
	end

	if not jump_to_body_index then
		jump_to_body_index = self:GetSthSelectEquipBodySeq(total_equip_body_data_list)
	end
	self.sth_equip_body_list:JumpToIndex(jump_to_body_index)
	-- self:FlushEquipStrengthSlider()
end

function EquipmentView:StrengthChangeToTargetEquipBody(data)
	if IsEmptyTable(data) then
		return
	end

	local total_equip_body_data_list = EquipBodyWGData.Instance:GetDZEquipBodyDataList()
	if IsEmptyTable(total_equip_body_data_list) then
		return
	end

	self.sth_jump_equip_body_seq = data.equip_body_seq
	self.sth_jump_equip_body_equip_data = data.selct_part_data

	for k, v in pairs(total_equip_body_data_list) do
		if v.seq == self.sth_jump_equip_body_seq then
			if self.sth_equip_body_list then
				self.sth_equip_body_list:JumpToIndex(k)
				self.sth_jump_equip_body_seq = -1
			end

			break
		end
	end
end

-- 第一次进来选中 红点 > 穿戴装备高阶 > 穿戴装备   
function EquipmentView:GetSthSelectEquipBodySeq(total_equip_body_data_list)
	if self.sth_select_equip_body_seq then
		return self.sth_select_equip_body_index
	end
	
	local default_seq = -1
	local default_index = -1
	if not IsEmptyTable(total_equip_body_data_list) then
		for i = #total_equip_body_data_list, 1, -1 do
			local data = total_equip_body_data_list[i]

			if EquipmentWGData.Instance:GetSthEquipBodyRemind(data.seq) > 0 then
				return i
			end

			if default_seq < 0 or data.seq > default_seq  then
				local can_duanzao, is_unlock, is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyCanDuanZao(data.seq)
				
				if is_unlock and is_wear_equip then
					default_seq = data.seq
					default_index = i
				end
			end
		end
	end

	return default_index
end

-- function EquipmentView:GetSthSelectEquipBodySeq(total_equip_body_data_list)
-- 	if self.sth_select_equip_body_seq then
-- 		if EquipmentWGData.Instance:GetSthEquipBodyRemind(self.sth_select_equip_body_seq) then
-- 			return self.sth_select_equip_body_index
-- 		end
-- 	end

-- 	local default_seq = -1
-- 	local default_index = -1
-- 	if not IsEmptyTable(total_equip_body_data_list) then
-- 		for k, v in pairs(total_equip_body_data_list) do
-- 			if EquipmentWGData.Instance:GetSthEquipBodyRemind(v.seq) > 0 then
-- 				return k
-- 			end

-- 			if default_seq < 0 or v.seq > default_seq  then
-- 				local can_duanzao, is_unlock, is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyCanDuanZao(v.seq)
				
-- 				if is_unlock and is_wear_equip then
-- 					default_seq = v.seq
-- 					default_index = k
-- 				end
-- 			end
-- 		end
-- 	end

-- 	return self.sth_select_equip_body_index or default_index
-- end

-- 选中肉身
function EquipmentView:OnSelectSthEquipBodyHandler(item, cell_index, is_default, is_click)
	if nil == item or IsEmptyTable(item.data) then
		return
	end

	local data = item.data
	local sth_seq_change = self.sth_select_equip_body_seq ~= data.seq
	self.sth_select_equip_body_seq = data.seq
	self.sth_select_equip_body_index = item.index
	self.sth_select_equip_body_data = data

	-- 刷新淬火装备
	self:FlushEquipStrengthListDataSource(sth_seq_change)

	-- local unlock = EquipBodyWGData.Instance:IsEquipBodyUnLock(data.seq)
	-- if not unlock and is_click then
	-- 	EquipBodyWGCtrl.Instance:SetDataAndOpen(data)
	-- end

	if sth_seq_change then
		self:StopAutoEquipStrength()
	end
end

-- 刷列表数据源
function EquipmentView:FlushEquipStrengthListDataSource(sth_seq_change)
	if nil == self.equip_qh_list or nil == self.sth_select_equip_body_seq then
		return
	end

	local equip_data_list = EquipmentWGData.Instance:GetRoleEquipStrengthListData(self.sth_select_equip_body_seq)
	for k,v in pairs(self.equip_qh_list) do
		v:SetData(equip_data_list[k])
	end

	if not IsEmptyTable(self.sth_jump_equip_body_equip_data) then
		local equip_part = EquipmentWGData.GetEquipPartByEquipBodyIndex(self.sth_jump_equip_body_equip_data.index)
		if not IsEmptyTable(equip_data_list[equip_part]) then
			if self.equip_qh_list[equip_part] then
				self.equip_qh_list[equip_part]:OnClick()
				return
			end
		end
	end

	-- 选中
	local default_index = self:GetSthEquipSelect(equip_data_list, sth_seq_change)
	if self.equip_qh_list[default_index] then
		self.equip_qh_list[default_index]:OnClick()
	end
end

-- 策划要求 不跳转红点
function EquipmentView:GetSthEquipSelect(data_list, sth_seq_change)
	if not sth_seq_change and self.strength_select_index >= 0 then
		return self.strength_select_index
		-- local can_strength = EquipmentWGData.Instance:IsCanStrengthEquip(self.sth_select_equip_body_seq, self.strength_select_data.index)

		-- if can_strength then
		-- 	return self.strength_select_index
		-- end
	end

	local default_index = -1
	for k, v in pairs(data_list) do
		local can_strength = EquipmentWGData.Instance:IsCanStrengthEquip(self.sth_select_equip_body_seq, v.index)

		if default_index < 0 then
			default_index = k
		end

		if can_strength then
			return k
		end
	end

	if self.strength_select_index >= 0 and not IsEmptyTable(data_list[self.strength_select_index]) then
		return self.strength_select_index
	else
		return default_index
	end
end

-- 选择装备回调
function EquipmentView:OnClickStrengthListCallBack(cell)
	if nil == cell or IsEmptyTable(cell.data) then
		return 
	end

	local strength_item_data = cell.data
	-- local strength_item_data = cell and cell.data
	-- if not strength_item_data or self.strength_select_index == strength_item_data.index then
	-- 	return
	-- end

	if self.strength_select_index ~= cell.index then
		self:StopAutoEquipStrength()
	end

	-- self.last_level = -1
	-- self.last_index = -1
	self.qh_old_progress_value = nil
	self.strength_select_data = strength_item_data
	self.strength_select_index = cell.index

	self.node_list["TextItem"].text.text = ItemWGData.Instance:GetItemNameDarkColor(strength_item_data.item_id)
	self.equip_qh_item_cell:SetData(strength_item_data)

	self:FlushEquipStrengthSlider()

	for k, v in pairs(self.equip_qh_list) do
		v:SetSelectIndex(strength_item_data.index % 15)
	end
end

function EquipmentView:GetCurSelectStrengthData()
	return self.strength_select_data
end

--刷新进度条
function EquipmentView:FlushEquipStrengthSlider()
	local strength_item_data = self:GetCurSelectStrengthData()

	if nil == strength_item_data then
		self.node_list.qh_jiacheng_slider.slider.value = 0
		self.node_list.qh_progess_desc.text.text = ""
		return
	end

	self:FlushStrengthAllAttr()

	local item_cfg = ItemWGData.Instance:GetItemConfig(strength_item_data.item_id)

	if item_cfg then
		local equip_index = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
		local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(equip_index)
		local cur_progress, slider_desc = EquipmentWGData.Instance:GetTotalQHLevelAndNextNeedByType(equip_type, self.sth_select_equip_body_seq)
		if self.qh_old_progress_value == nil then
			self.node_list.qh_jiacheng_slider.slider.value = cur_progress
		else
			self.node_list.qh_jiacheng_slider.slider:DOValue(cur_progress, 0.5)
		end

		self.qh_old_progress_value = cur_progress
		self.node_list.qh_progess_desc.text.text = slider_desc
	end
end

function EquipmentView:FlushStrengthAllAttr(level)
	local strength_item_data = self:GetCurSelectStrengthData()
	if nil == strength_item_data then
		return
	end

	local equip_boduy_index = strength_item_data.index
	local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_boduy_index)
	local now_level = level or EquipmentWGData.Instance:GetStrengthLevelByIndex(equip_boduy_index)

	local equip_cfg = EquipmentWGData.Instance:GetNewStrengthCfgByLevel(equip_part, now_level)
	if nil == equip_cfg then
		return
	end

	local next_equip_cfg = EquipmentWGData.Instance:GetNewStrengthCfgByLevel(equip_part, now_level + 1)
	local is_max_level = EquipmentWGData.Instance:IsEquipStrengthMaxLevel(self.sth_select_equip_body_seq, strength_item_data.index, now_level)
	local color_str
	self.equip_up_struff = {need_num = 0}
	local is_have_stuff = false
	local coin_enough = false
	local bundle, asset
	local material_color_str

	if not is_max_level then
		local role_coin = RoleWGData.Instance.role_info.coin or 0

		-- 升星拿的下一级别的消耗   如1级升2级，拿的2及的配置消耗
		local need_coin = next_equip_cfg.coin
		self.need_strength_coin = need_coin
		coin_enough = need_coin <= role_coin

		local coin_str_color = need_coin <= role_coin and COLOR3B.GREEN or COLOR3B.D_PINK
		local cur_coin_str = ToColorStr(CommonDataManager.ConverExpByThousand(role_coin), coin_str_color)
		color_str = cur_coin_str .. "/" .. CommonDataManager.ConverExpByThousand(need_coin)

		if next_equip_cfg.stuff_num > 0 then
			self.equip_up_struff = {item_id = next_equip_cfg.stuff_id, need_num = next_equip_cfg.stuff_num}
			local num = ItemWGData.Instance:GetItemNumInBagById(next_equip_cfg.stuff_id)
			is_have_stuff = num >= next_equip_cfg.stuff_num
			material_color_str = ToColorStr(string.format("%s/%s", num, next_equip_cfg.stuff_num), is_have_stuff and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH)
			bundle, asset = ResPath.GetItem(next_equip_cfg.stuff_id)
		else
			bundle, asset = ResPath.GetCommonImages("a3_ty_suo")
		end
	else
		color_str = Language.Equip.NotStuff
		material_color_str = Language.Equip.NotStuff
		bundle, asset = ResPath.GetItem(equip_cfg.stuff_id)

	end

	self.node_list.qh_coin_desc.text.text = color_str
	self.node_list.qh_material_stuff_desc.text.text = material_color_str
	self.node_list.strength_stuff_pos.image:LoadSprite(bundle, asset, function ()
		self.node_list.strength_stuff_pos.image:SetNativeSize()
	end)

	self.node_list["strength_qh_expend_panel"]:SetActive(not is_max_level)

	if not is_max_level then
		UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["strength_qh_expend_panel"].rect)
	end

	self:FlushStrengthMaxInfo(is_max_level)
	self:FlushEquipStrengthAttr(now_level)
	self:SetStrengthRemind(not is_max_level and is_have_stuff and coin_enough)
	self:StartAutoEquipStrength(not is_max_level and is_have_stuff and coin_enough)

	local active_remind = EquipmentWGData.Instance:GetEquipBodyLevelRemind(self.sth_select_equip_body_seq) or 0
    self.node_list["strength_active_remind"]:SetActive(active_remind > 0)
	self.node_list["btn_strength_tips_effect"]:SetActive(active_remind > 0)
	-- self.last_level = now_level
	-- self.last_index = strength_item_data.index
	-- self.last_stuff_num = equip_cfg.stuff_num
	------------------------------------------------------------------------------

	-- local now_level = level or EquipmentWGData.Instance:GetStrengthLevelByIndex(strength_item_data.index)
	-- local equip_cfg_index = strength_item_data.index % 15
	
	-- local equip_cfg = EquipmentWGData.Instance:GetNewStrengthCfgByLevel(equip_cfg_index, now_level)
	-- if nil == equip_cfg then
	-- 	return
	-- end

	-- local next_equip_cfg = EquipmentWGData.Instance:GetNewStrengthCfgByLevel(equip_cfg_index, now_level + 1)
	-- local role_coin = RoleWGData.Instance.role_info.coin or 0
	-- local is_max_level = EquipmentWGData.Instance:IsEquipStrengthMaxLevel(self.sth_select_equip_body_seq, strength_item_data.index, now_level)
	-- local need_coin = equip_cfg.coin
	-- self.need_strength_coin = need_coin

	-- local color_str
	-- if is_max_level then
	-- 	color_str = Language.Equip.NotStuff 
	-- else
	-- 	local coin_str_color = need_coin <= role_coin and COLOR3B.GREEN or COLOR3B.D_PINK
	-- 	local cur_coin_str = ToColorStr(CommonDataManager.ConverExpByThousand(role_coin), coin_str_color)
	-- 	color_str = cur_coin_str .. "/" .. CommonDataManager.ConverExpByThousand(need_coin)
	-- end
	-- self.node_list.qh_coin_desc.text.text = color_str

	-- self.equip_up_struff = {need_num = 0}
	-- local is_have_stuff = true
	-- local bundle, asset

	-- local material_color_str
	-- -- 材料消耗
	-- if is_max_level then
	-- 	material_color_str = Language.Equip.NotStuff
	-- 	bundle, asset = ResPath.GetItem(equip_cfg.stuff_id)
	-- else
	-- 	if next_equip_cfg.stuff_num > 0 then
	-- 		self.equip_up_struff = {item_id = next_equip_cfg.stuff_id, need_num = next_equip_cfg.stuff_num}
	-- 		local num = ItemWGData.Instance:GetItemNumInBagById(next_equip_cfg.stuff_id)
	-- 		is_have_stuff = num >= next_equip_cfg.stuff_num
	-- 		material_color_str = ToColorStr(string.format("%s/%s", num, next_equip_cfg.stuff_num), is_have_stuff and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH)
	
	-- 		bundle, asset = ResPath.GetItem(next_equip_cfg.stuff_id)
	-- 	else
	-- 		bundle, asset = ResPath.GetCommonImages("a3_ty_suo")
	-- 	end
	-- end

	-- self.node_list.qh_material_stuff_desc.text.text = material_color_str

	-- self.node_list.strength_stuff_pos.image:LoadSprite(bundle, asset, function ()
	-- 	self.node_list.strength_stuff_pos.image:SetNativeSize()
	-- end)
	
	-- local max_equip_level = EquipBodyWGData.Instance:GetForgeLevelLimit(self.sth_select_equip_body_seq)
	-- self.node_list["strength_qh_expend_panel"]:SetActive(now_level < max_equip_level)

	-- self:FlushStrengthMaxInfo(is_max_level)
	-- self:FlushEquipStrengthAttr(now_level)

	-- self:SetStrengthRemind(is_have_stuff and need_coin <= role_coin and not is_max_level)

	-- self:StartAutoEquipStrength(is_have_stuff and need_coin <= role_coin and not is_max_level)

    -- local active_remind = EquipmentWGData.Instance:GetEquipBodyLevelRemind(self.sth_select_equip_body_seq) or 0
    -- self.node_list["strength_active_remind"]:SetActive(active_remind > 0)
	-- self.node_list["btn_strength_tips_effect"]:SetActive(active_remind > 0)
	-- self.last_level = now_level
	-- self.last_index = strength_item_data.index
	-- self.last_stuff_num = equip_cfg.stuff_num
end

function EquipmentView:FlushStrengthMaxInfo(is_max_level)
	self:SetAllEquipStrengthButtonEnabled(not is_max_level)
	self.node_list["strengtn_maxlevel_content"]:SetActive(not is_max_level)
	-- self.node_list["strength_max_state"]:SetActive(is_max_level)
end

--刷新强化面板属性显示
function EquipmentView:FlushEquipStrengthAttr(level)
	local strength_item_data = self:GetCurSelectStrengthData()
	if IsEmptyTable(strength_item_data) then
		return
	end

	local now_level = level or EquipmentWGData.Instance:GetStrengthLevelByIndex(strength_item_data.index)
	local equip_body_index = strength_item_data.index % 15

	local equip_cfg = EquipmentWGData.Instance:GetNewStrengthCfgByLevel(equip_body_index, now_level)
	if IsEmptyTable(equip_cfg) then
		return
	end

	local is_max_level = EquipmentWGData.Instance:IsEquipStrengthMaxLevel(self.sth_select_equip_body_seq, strength_item_data.index, now_level)
	local next_equip_cfg = {}

	if not is_max_level then
		next_equip_cfg = EquipmentWGData.Instance:GetNewStrengthCfgByLevel(equip_body_index, now_level + 1)
	end

	local attr_data = EquipWGData.GetSortAttrListHaveNextByCfg(equip_cfg, next_equip_cfg)

	local equip_type = 0
	if equip_body_index == GameEnum.EQUIP_INDEX_XIANLIAN
		or equip_body_index == GameEnum.EQUIP_INDEX_XIANZHUI
		or equip_body_index == GameEnum.EQUIP_INDEX_XIANJIE
		or equip_body_index == GameEnum.EQUIP_INDEX_XIANZHUO then
		equip_type = 1 --一键按钮饰品类型.
	else
		equip_type = 0 --一键按钮普通装备类型.
	end

	attr_data = EquipmentWGData.Instance:CalcEquipStrengthAttrAddPer(attr_data, equip_type, self.sth_select_equip_body_seq)
	local need_show_up_effect = false

	if nil ~= self.equip_strength_index_cache and nil ~= self.equip_strength_level_cache then
		if (self.equip_strength_index_cache == strength_item_data.index) and (now_level > self.equip_strength_level_cache) then
			need_show_up_effect = true
		end
	end

	for k, v in ipairs(self.strength_attr_list) do
		v:SetData(attr_data[k])

		if need_show_up_effect then
            v:PlayAttrValueUpEffect()
        end
	end

	self.equip_strength_index_cache = strength_item_data.index
	self.equip_strength_level_cache = now_level

	local max_equip_level = EquipBodyWGData.Instance:GetForgeLevelLimit(self.sth_select_equip_body_seq)
	local is_max_level = now_level >= max_equip_level

	local cur_str = (now_level > 0 and (is_max_level and "" or string.format(Language.Equip.StrengthLv2, now_level)) ) or Language.Equip.StrengthNoLv
	self.node_list.strength_eq_cur_lv.text.text = cur_str
	self.node_list.strength_eq_next_lv.text.text = is_max_level and Language.Equip.StrengthMaxLv or Language.Equip.StrengthNextLv

	self.node_list.sth_eq_arrow:CustomSetActive(not is_max_level)
	self.node_list.sth_attr_arrow:CustomSetActive(not is_max_level)

	self.node_list.strength_eq_next_lv.rect.anchoredPosition = Vector2(is_max_level and 0 or 212, -3)
	local width = is_max_level and 300 or 760
	self.node_list.sth_attr_1.rect.sizeDelta = Vector2(width, 34)
	self.node_list.sth_attr_2.rect.sizeDelta = Vector2(width, 34)
end

function EquipmentView:PlayEquipStrengthEffect()
	TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_qianghua, is_success = true, pos = Vector2(0, 0)})
end

function EquipmentView:ShowStuffTips()
	self:PlayEquipStrengthEffect()

	local strength_item_data = self:GetCurSelectStrengthData()
	if nil == strength_item_data then
		return
	end

	local now_level = EquipmentWGData.Instance:GetStrengthLevelByIndex(strength_item_data.index) - 1

	local auto_up_need_num = 0
	local equip_cfg = EquipmentWGData.Instance:GetNewStrengthCfgByLevel(strength_item_data.index % 15, now_level)

	if equip_cfg and (self.cache_strength_cfg == nil or self.cache_strength_cfg.item_id ~= equip_cfg.stuff_id) then
		self.cache_strength_cfg = ItemWGData.Instance:GetItemConfig(equip_cfg.stuff_id)
	end
	if nil == equip_cfg then
		return
	end
	if self.cache_strength_cfg ~= nil then
		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.Equip.Strength_Stuff,
			ToColorStr(self.cache_strength_cfg.name, ITEM_COLOR[self.cache_strength_cfg.color]), equip_cfg.stuff_num))
	end
end

function EquipmentView:OpenQHShowEquipTips()
	local strength_item_data = self:GetCurSelectStrengthData()
	TipWGCtrl.Instance:OpenItem(strength_item_data, ItemTip.FROM_EQUIPMENT)
end

function EquipmentView:ShowStrengthItemTips(is_cion)
	if is_cion then
		TipWGCtrl.Instance:OpenItemTipGetWay(COIN_DATA)
	else
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.equip_up_struff.item_id})
	end
end

--判断是否有足够的金钱强化装备 --@true 金钱足够 --@false 金钱不够
function EquipmentView:IsHaveMoneyToUplevel()
	local role_coin = RoleWGData.Instance.role_info.coin or 0
	if self.need_strength_coin <= role_coin then
		return true
	end

	return false
end

--判断是否有材料强化
function EquipmentView:IsHaveStuffToStrengthUpLevel( )
	if self.equip_up_struff.need_num and self.equip_up_struff.need_num > 0 then --需要材料
		local num = ItemWGData.Instance:GetItemNumInBagById(self.equip_up_struff.item_id)
		if num >= self.equip_up_struff.need_num then
			return true
		end
	else
		return true
	end

	return false
end

function EquipmentView:SetAllEquipStrengthButtonEnabled( enabled )
	self:SetBtnStrengthEnable(enabled)
	self:SetAutoStrengthEnable(enabled)
end

function EquipmentView:SetAutoStrengthEnable( enabled )
	XUI.SetButtonEnabled(self.node_list["btn_equip_qh_auto"], enabled)
end

function EquipmentView:SetBtnStrengthEnable(enabled)
	XUI.SetButtonEnabled(self.node_list["btn_equip_qh"], enabled)
end

function EquipmentView:SetStrengthRemind(enable)
	self.node_list.strength_remind:SetActive(enable)
end

function EquipmentView:OnBtnEquipStrengthTips()
	RoleWGCtrl.Instance:SetEquipTextData(ROLE_EQUIP_ATTR_TIPS.STREHGTH_TIP, self.sth_select_equip_body_seq)
	RoleWGCtrl.Instance:OpenEquipAttr()
end

-- 自动进阶请求处理  
function EquipmentView:AutoEquipStrength()
	if self.is_strength_auto_up then
		self:StopAutoEquipStrength()
	else
		local strength_item_data = self:GetCurSelectStrengthData()
		if strength_item_data ~= nil then
			local is_max_level = EquipmentWGData.Instance:IsEquipStrengthMaxLevel(self.sth_select_equip_body_seq, strength_item_data.index)
	
			if is_max_level then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Equipment.Max)
				self:StopAutoEquipStrength()
				return
			end
	
			self:OnBtnEquipStrengthHandeler(1)
		end
	end
end

-- 时间间隔请求  
function EquipmentView:StartAutoEquipStrength(can_cuihuo)
	if self.is_strength_auto_up then
		if can_cuihuo then
			TryDelayCall(self, function ()
				self:OnBtnEquipStrengthHandeler(1)
			end, 0.3, "StartAutoEquipStrength")
		else
			self:StopAutoEquipStrength()
		end
	end
end

-- 切换装备 退出 切换肉身退出 切换功能退出  关闭退出
function EquipmentView:StopAutoEquipStrength()
	self.is_strength_auto_up = false

	if self.node_list.desc_btn_equip_qh_auto then
		self.node_list.desc_btn_equip_qh_auto.text.text = Language.Equip.StrengthCuiHuoBtn[0]
	end
end

-- 强化装备请求
function EquipmentView:OnBtnEquipStrengthHandeler(one_key)
	local strength_item_data = self:GetCurSelectStrengthData()
	if nil == strength_item_data then
		return
	end

	local is_max_level = EquipmentWGData.Instance:IsEquipStrengthMaxLevel(self.sth_select_equip_body_seq, strength_item_data.index)
	if is_max_level then
		return
	end

	self.is_strength_auto_up = true
	self.node_list.desc_btn_equip_qh_auto.text.text = Language.Equip.StrengthCuiHuoBtn[1]

	if not self:IsHaveStuffToStrengthUpLevel() then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.equip_up_struff.item_id})
		self:StopAutoEquipStrength()
		return
	end

	if self:IsHaveMoneyToUplevel() then
		local param_1 = strength_item_data.index
		if one_key == 1 then
			local equip_part = EquipmentWGData.GetEquipPartByEquipBodyIndex(strength_item_data.index)

			if equip_part == GameEnum.EQUIP_INDEX_XIANLIAN
				or equip_part == GameEnum.EQUIP_INDEX_XIANZHUI
				or equip_part == GameEnum.EQUIP_INDEX_XIANJIE
				or equip_part == GameEnum.EQUIP_INDEX_XIANZHUO then
				param_1 = 0			--一键按钮饰品类型.
			else
				param_1 = 1			--一键按钮普通装备类型.
			end
		end

		EquipmentWGCtrl.Instance:SendEquipStrengthGrade(param_1, self.sth_select_equip_body_seq, one_key, 1)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NoCoin)
		TipWGCtrl.Instance:OpenItemTipGetWay(COIN_DATA)
		self:StopAutoEquipStrength()
	end
end

function EquipmentView:OnClickSthEquipBodyBtn()
	EquipmentWGCtrl.Instance:OpenStrengthOverviewView()
end

function EquipmentView:SthEquipBodyListSetEndScrollCallBack()
	if self.sth_need_equip_body_tween then
		self.sth_need_equip_body_tween = false

		local tween_info = UITween_CONSTS.EquipBody

		local cell_list = self.sth_equip_body_list:GetAllItems()
		for i = 1, #cell_list do
			cell_list[i]:PlayItemTween()
		end
	end
end

--------------------------------STHEquipBodyListCellRender-----------------------------------
STHEquipBodyListCellRender = STHEquipBodyListCellRender or BaseClass(BaseRender)

function STHEquipBodyListCellRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_tip"], BindTool.Bind(self.OnClickTipBtn, self))
end

function STHEquipBodyListCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local icon_bundle, icon_asset = ResPath.GetEquipBodyIcon(self.data.show_icon)
	self.node_list.icon.image:LoadSprite(icon_bundle, icon_asset, function ()
		self.node_list.icon.image:SetNativeSize()
	end)

	local hl_icon_bundle, hl_icon_asset = ResPath.GetEquipBodyIcon(self.data.show_hl_icon)
	self.node_list.icon_hl.image:LoadSprite(hl_icon_bundle, hl_icon_asset, function ()
		self.node_list.icon_hl.image:SetNativeSize()
	end)

	self.node_list.name.text.text = self.data.name
	self.node_list.name_hl.text.text = self.data.name
	self.node_list.specall_name.text.text = self.data.name

	local can_duanzao, is_unlocak, is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyCanDuanZao(self.data.seq)
	self.node_list.flag_lock:CustomSetActive(not is_unlocak and not is_wear_equip)
	self.node_list.no_equip_tip:CustomSetActive(is_unlocak and not is_wear_equip)

	self.node_list.btn_tip:CustomSetActive(not can_duanzao)

	local remind = EquipmentWGData.Instance:GetSthEquipBodyRemind(self.data.seq) > 0
	self.node_list.remind:CustomSetActive(remind)
end

function STHEquipBodyListCellRender:OnSelectChange(is_select)
	local is_special = self.data.type == 1
	self.node_list.icon:CustomSetActive(is_special or (not is_special and not is_select))
	self.node_list.icon_hl:CustomSetActive(not is_special and is_select)
	self.node_list.special_select:CustomSetActive(is_special and is_select)
	self.node_list.name:CustomSetActive(not is_special and not is_select)
	self.node_list.name_hl:CustomSetActive(not is_special and is_select)
	self.node_list.specall_name:CustomSetActive(is_special)
end

function STHEquipBodyListCellRender:OnClickTipBtn()
	local can_duanzao, is_unlocak, is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyCanDuanZao(self.data.seq)

	if not can_duanzao then
		if not is_unlocak then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleEquipBody.EquipBodyLockCanDuanZao)
		elseif not is_wear_equip then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleEquipBody.EquipBodyNotWearEquipCanDuanZao)
		end
	end
end

function STHEquipBodyListCellRender:PlayItemTween()
	UITween.FakeHideShow(self.node_list.root)
	local index = self:GetIndex()
	local tween_info = UITween_CONSTS.EquipBody.ListCellTween
	self.cell_delay_key = "STHEquipBodyItemCellRender" .. index
	ReDelayCall(self, function()
		if self.node_list and self.node_list.root then
			UITween.FakeToShow(self.node_list.root)
		end
	end, tween_info.NextDoDelay * (index - 1), self.cell_delay_key)
end