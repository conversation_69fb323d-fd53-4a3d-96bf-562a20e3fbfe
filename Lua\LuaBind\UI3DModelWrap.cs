﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UI3DModelWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UI3DModel), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>RegFunction("GetTarget", GetTarget);
		<PERSON><PERSON>unction("SetIsGrey", SetIsGrey);
		<PERSON><PERSON>Function("SetIsSupportClip", SetIsSupportClip);
		<PERSON><PERSON>RegFunction("OnAddGameobject", OnAddGameobject);
		<PERSON><PERSON>RegFunction("OnRemoveGameObject", OnRemoveGameObject);
		<PERSON><PERSON>Function("SetOverrideOrder", SetOverrideOrder);
		<PERSON><PERSON>RegFunction("ResetAllRenders", ResetAllRenders);
		<PERSON><PERSON>RegFunction("SetClipSoftness", SetClipSoftness);
		L.RegFunction("__eq", op_Equality);
		<PERSON><PERSON>RegFunction("__tostring", ToLua.op_ToString);
		<PERSON>.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetTarget(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UI3DModel obj = (UI3DModel)ToLua.CheckObject<UI3DModel>(L, 1);
			UnityEngine.GameObject o = obj.GetTarget();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIsGrey(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UI3DModel obj = (UI3DModel)ToLua.CheckObject<UI3DModel>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetIsGrey(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIsSupportClip(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UI3DModel obj = (UI3DModel)ToLua.CheckObject<UI3DModel>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetIsSupportClip(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnAddGameobject(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UI3DModel obj = (UI3DModel)ToLua.CheckObject<UI3DModel>(L, 1);
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
			obj.OnAddGameobject(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnRemoveGameObject(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UI3DModel obj = (UI3DModel)ToLua.CheckObject<UI3DModel>(L, 1);
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
			obj.OnRemoveGameObject(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetOverrideOrder(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 5);
			UI3DModel obj = (UI3DModel)ToLua.CheckObject<UI3DModel>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
			int arg3;
			obj.SetOverrideOrder(arg0, arg1, arg2, out arg3);
			LuaDLL.lua_pushinteger(L, arg3);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResetAllRenders(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UI3DModel obj = (UI3DModel)ToLua.CheckObject<UI3DModel>(L, 1);
			obj.ResetAllRenders();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetClipSoftness(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UI3DModel obj = (UI3DModel)ToLua.CheckObject<UI3DModel>(L, 1);
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.SetClipSoftness(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

