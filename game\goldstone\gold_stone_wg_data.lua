GoldStoneWGData = GoldStoneWGData or BaseClass()

function GoldStoneWGData:__init()
	if GoldStoneWGData.Instance then
		error("[GoldStoneWGData] Attempt to create singleton twice!")
		return
	end

	GoldStoneWGData.Instance = self
	self:InitConfig()
	self.rmb_shop_id = 0
	self.free_shop_id = 0
	RemindManager.Instance:Register(RemindName.GoldStoneRemind, BindTool.Bind(self.GetGoldStoneRed, self))
end

function GoldStoneWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.GoldStoneRemind)
    GoldStoneWGData.Instance = nil
end

function GoldStoneWGData:InitConfig()
	self.golden_talk_auto = ConfigManager.Instance:GetAutoConfig("golden_talk_auto")
	self.open_day_cfg = self.golden_talk_auto.open_day
	self.rmb_shop_cfg = self.golden_talk_auto.rmb_shop
	self.free_shop_cfg = self.golden_talk_auto.free_shop
end

function GoldStoneWGData:GetFreeShop()
	return self.free_shop_cfg[self.free_shop_id] or {}
end

function GoldStoneWGData:SetShopIsBuyFlag(protocol)
	self.is_buy_rmb = protocol.is_buy_rmb == 1
	self.is_buy_free = protocol.is_buy_free == 1
end

function GoldStoneWGData:GetShopIsBuyFlag()
	return self.is_buy_rmb, self.is_buy_free
end

function GoldStoneWGData:ChangeShopID()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or 0
	local cfg = self.open_day_cfg
	for k, v in ipairs(cfg) do
		if open_day >= v.start_day and open_day <= v.end_day then
			self.rmb_shop_id = v.rmb_shop_id
			self.free_shop_id = v.free_shop_id
			return
		end
	end
end

function GoldStoneWGData:GetCurFreeShopID()
	return self.free_shop_id
end

function GoldStoneWGData:GetCurShopCfg()
	return self.rmb_shop_cfg[self.rmb_shop_id]
end

function GoldStoneWGData:GetGoldStoneRed()
	local _, is_buy_free = self:GetShopIsBuyFlag()
	if not is_buy_free then
		return 1
	end

    return 0
end