FlowerRankView = FlowerRankView or BaseClass(SafeBaseView)

function FlowerRankView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/flower_rank_ui_prefab", "layout_flower_rank")
end

function FlowerRankView:__delete()

end

function FlowerRankView:ReleaseCallBack()
	if nil ~= self.list_item then
		self.list_item:DeleteMe()
		self.list_item = nil
	end

	if nil ~= self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end
end

function FlowerRankView:LoadCallBack()

	self.list_item = AsyncBaseGrid.New()
	self.list_item:CreateCells({cell_count= 9, col=3, row=3, itemRender = FlowerRankItemRender,assetBundle = 'uis/view/flower_rank_ui_prefab',
		assetName = 'ph_paiming_item', list_view = self.node_list.ph_list_item})
	XUI.AddClickEventListener(self.node_list.btn_nanshen, BindTool.Bind1(self.OnClickNanShen, self))
	XUI.AddClickEventListener(self.node_list.btn_nvshen, BindTool.Bind1(self.OnClickNvshen, self))
	XUI.AddClickEventListener(self.node_list.btn_act_award, BindTool.Bind1(self.OnClickActAward, self))
	XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind1(self.Close, self))


	self.rank_list = AsyncListView.New(ButtomRankListItemRender,self.node_list.ph_rank_list)

	XUI.AddClickEventListener(self.node_list.btn_songhua_one, BindTool.Bind1(self.OnClickBtnSongHua, self))

end

function FlowerRankView:ShowIndexCallBack(index)
	--根据玩家性别请求排行榜数据
	local role_vo =  GameVoManager.Instance:GetMainRoleVo()
	if 1 == role_vo.sex then
		RankWGCtrl.Instance:SendCrossGetRankListReq(CROSS_PERSON_RANK_TYPE.CROSS_PERSON_RANK_TYPE_FLOWER_HANDSOME)
	else
		RankWGCtrl.Instance:SendCrossGetRankListReq(CROSS_PERSON_RANK_TYPE.CROSS_PERSON_RANK_TYPE_FLOWER_BEAUTY)
	end

	if CountDownManager.Instance:HasCountDown("flower_rank") then
		CountDownManager.Instance:RemoveCountDown("flower_rank")
	end
	local act_cornucopia_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.CROSS_RAND_ACTIVITY_TYPE_FLOWER_RANK) or {}
	if act_cornucopia_info.status == ACTIVITY_STATUS.OPEN then
		local end_time = act_cornucopia_info.end_time or 0
		self:UpdataRollerTime(0,end_time - TimeWGCtrl.Instance:GetServerTime())
		CountDownManager.Instance:AddCountDown("flower_rank", BindTool.Bind1(self.UpdataRollerTime, self), BindTool.Bind1(self.CompleteRollerTime, self), end_time, nil, 1)
	else
		self:CompleteRollerTime()
	end

end

function FlowerRankView:OpenCallBack()

end

function FlowerRankView:OnFlush()
	self:FlushRankOne()

	--刷新鲜花榜前十名
	local list = FlowerRankWGData.Instance:GetFlowerRankItemList()
	self.list_item:SetDataList(list,3)

    --刷新底部排行榜
	local list = FlowerRankWGData.Instance:GetButtomRankList()
	self.rank_list:SetDataList(list,3)
end
--刷新活动倒计时
function FlowerRankView:UpdataRollerTime(elapse_time, next_time)
	local time = next_time - elapse_time
	if self.node_list.lbl_time ~= nil then
		if time > 0 then
			local format_time = TimeUtil.FormatSecond(time,3)
			self.node_list.lbl_time.text.text = (format_time)
		end
	end
end
--倒计时时间结束
function FlowerRankView:CompleteRollerTime()
	if self.lbl_time ~= nil then
		self.lbl_time.text.text = ("00:00:00")
	end
end
--刷新第一名
function FlowerRankView:FlushRankOne()

	self.rank_one = FlowerRankWGData.Instance:GetRankOne()
	if next(self.rank_one)~= nil then
		self.node_list.lbl_name_one.text.text = (self.rank_one.user_name)
		local server_name =  FlowerRankWGData.Instance:GetServerName(self.rank_one)
		self.node_list.lbl_server_one.text.text = (server_name)
		self.node_list.lbl_meili_one.text.text = (self.rank_one.rank_value)
		XUI.UpdateRoleHead(self.node_list.default_head_icon, self.node_list.custom_head_icon, self.rank_one.user_id, self.rank_one.sex,self.rank_one.prof, nil)

	else
		self.node_list.default_head_icon.image.enabled = false
		self.node_list.custom_head_icon.raw_image.enabled = false
		self.node_list.lbl_name_one.text.text = (Language.KuafuGuildBattle.KfNoOccupy)
		self.node_list.lbl_server_one.text.text = ("")
		self.node_list.lbl_meili_one.text.text = ("")
		-- self.img_head:loadTexture(ResPath.GetMarryResPath("img_head_bg"))
		-- self.img_head:setScale(1)
	end
end


--点击送花
function FlowerRankView:OnClickBtnSongHua()
	if next(self.rank_one) ~= nil  then
		FlowerWGCtrl.Instance:OpenSendFlowerView(self.rank_one.user_id, self.rank_one.user_name,self.rank_one.sex,self.rank_one.prof)
	end
end

--点击男神榜
function FlowerRankView:OnClickNanShen()
	RankWGCtrl.Instance:SendCrossGetRankListReq(CROSS_PERSON_RANK_TYPE.CROSS_PERSON_RANK_TYPE_FLOWER_HANDSOME )
end

--点击女神榜
function FlowerRankView:OnClickNvshen()
	RankWGCtrl.Instance:SendCrossGetRankListReq(CROSS_PERSON_RANK_TYPE.CROSS_PERSON_RANK_TYPE_FLOWER_BEAUTY )
end

--点击活动奖励
function FlowerRankView:OnClickActAward()
	FlowerRankWGCtrl.Instance:OpenFlowerRankAward()
end


-------------------------------------------------------
			--中部鲜花榜item
-------------------------------------------------------
FlowerRankItemRender = FlowerRankItemRender or BaseClass(BaseRender)

function FlowerRankItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_songhua, BindTool.Bind1(self.OnClickBtnSongHua, self))
end
function FlowerRankItemRender:__delete()
end

function FlowerRankItemRender:CreateChild()
	BaseRender.CreateChild(self)
	self.img_role_head = self.node_list.img_role_head

	local size =self.img_role_head:getContentSize()
	local x,y =self.img_role_head:getPosition()
end

function FlowerRankItemRender:OnFlush()

	self.btn = nil
	if self.data == nil or next(self.data) == nil then
		self.node_list.lbl_name.text.text = (Language.KuafuGuildBattle.KfNoOccupy)
		self.node_list.lbl_meili.text.text = ("")
		self.node_list.lbl_server_name.text.text = string.format(Language.Flower.RankIndex,self.index + 1)
		self.node_list.default_head_icon.image.enabled = false
		self.node_list.custom_head_icon.raw_image.enabled = false
		self.node_list.lbl_server.text.text = ""
	else
		self.node_list.default_head_icon.image.enabled = true
		self.node_list.custom_head_icon.raw_image.enabled = true
		self.node_list.lbl_server_name.text.text = (string.format(Language.Flower.RankIndex,self.data.rank_index))
		self.node_list.lbl_name.text.text = (self.data.user_name)
		self.node_list.lbl_meili.text.text = (self.data.rank_value)
		local server_name =  FlowerRankWGData.Instance:GetServerName(self.data)
		self.node_list.lbl_server.text.text = (server_name)
		XUI.UpdateRoleHead(self.node_list.default_head_icon, self.node_list.custom_head_icon, self.data.user_id, self.data.sex,self.data.prof, nil)
		-- self.img_head:setScale(0.3)
		self.btn = self.data
	end

end
function FlowerRankItemRender:OnClickBtnSongHua()
	if self.btn ~= nil then
		FlowerWGCtrl.Instance:OpenSendFlowerView(self.btn.user_id, self.btn.user_name,self.btn.sex,self.btn.prof)
	end
end

-------------------------------------------------------------
			--底部排行榜
-------------------------------------------------------------
ButtomRankListItemRender = ButtomRankListItemRender or BaseClass(BaseRender)

function ButtomRankListItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_songhua, BindTool.Bind1(self.OnClickBtnSongHua, self))
end
function ButtomRankListItemRender:__delete()
end

function ButtomRankListItemRender:OnFlush()
	if self.data == nil or next(self.data) == nil  then return  end
	self.node_list.lbl_list_index.text.text = (self.data.rank_index)
	self.node_list.lbl_list_name.text.text = (self.data.user_name)
	local server_name =  FlowerRankWGData.Instance:GetServerName(self.data)
	self.node_list.lbl_list_server_name.text.text = (server_name)
	self.node_list.lbl_list_meili.text.text = (self.data.rank_value)
end

function ButtomRankListItemRender:OnClickBtnSongHua()
	FlowerWGCtrl.Instance:OpenSendFlowerView( self.data.user_id,  self.data.user_name,self.data.sex, self.data.prof)
end

function ButtomRankListItemRender:CreateSelectEffect()
end
