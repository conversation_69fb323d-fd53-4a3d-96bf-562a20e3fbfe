-- 天神-组合技能面板

TianShenUnionSkillView = TianShenUnionSkillView or BaseClass(SafeBaseView)

local content_list_height = 610
local min_cell_list_height = 90
local max_cell_list_height = 180

local max_page_num = 3

function TianShenUnionSkillView:__init()
	self.view_name = "TianShenUnionSkillView"
	self:SetMaskBg(true, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

	local bundle_name = "uis/view/tianshen_prefab"
	self:AddViewResource(0, bundle_name, "layout_ts_guanghuan")
end

function TianShenUnionSkillView:ReleaseCallBack()
	if self.page_tab_list then
		for k, v in pairs(self.page_tab_list) do
			v:DeleteMe()
		end
		self.page_tab_list = nil
	end
end

function TianShenUnionSkillView:LoadCallBack()
	self.select_page_index = 0
	self.page_tab_list = {}

	local show_data = TianShenWGData.Instance:GetUnionSkillPageShowCfg()
	for i = 1, max_page_num do
		self.page_tab_list[i] = TSUnionSkillPageRender.New(self.node_list["item_page_" .. i])
		self.page_tab_list[i]:SetIndex(i)
		self.page_tab_list[i]:AddClickEventListener(BindTool.Bind(self.OnClickPageTab, self))
		if show_data[i] then
			self.page_tab_list[i]:SetData(show_data[i])
		end
	end
end

function TianShenUnionSkillView:ShowIndexCallBack()
	self:OnClickPageTab(self.page_tab_list[1])

	if self.page_tab_list then
		for k, v in pairs(self.page_tab_list) do
			v:Flush()
		end
	end
end

function TianShenUnionSkillView:OnFlush()
	if self.page_tab_list and self.page_tab_list[self.select_page_index] then
		self.page_tab_list[self.select_page_index]:Flush()
	end
end

function TianShenUnionSkillView:OnClickPageTab(page_tab)
	if self.select_page_index == page_tab:GetIndex() then
		return
	end
	self.select_page_index = page_tab:GetIndex()
	self:FlushPageTabState()
end

function TianShenUnionSkillView:FlushPageTabState()
	local move_x = 0
	local page_list = self.page_tab_list
	local select_index = self.select_page_index

	for i=1,#page_list do
		page_list[i]:ShowInfoPanel(i == select_index)
		page_list[i]:DoMove(move_x)
		move_x = move_x + page_list[i]:GetWidth()
	end

	local rect = self.node_list["content_root"].rect
	RectTransform.SetSizeDeltaXY(rect, move_x, content_list_height)
end

--大页签---------------------------TSUnionSkillPageRender start
TSUnionSkillPageRender = TSUnionSkillPageRender or BaseClass(BaseRender)
function TSUnionSkillPageRender:__init()
	self.m_width = 102--页签宽度大小(固定)
	self.m_ex_width = 365
	self.m_ison = false

	if not self.page_cell_list then
		self.page_cell_list = AsyncListView.New(TianShenUnionSkillRender, self.node_list["ts_union_skill_list"])
	end
end

function TSUnionSkillPageRender:__delete()
	if self.page_cell_list then
		self.page_cell_list:DeleteMe()
		self.page_cell_list = nil
	end
	self.is_set_width = nil
end

function TSUnionSkillPageRender:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data)then
		return
	end

	--内容宽度大小(根据展示内容)
	if not self.is_set_width then
		self.is_set_width = true
		self.m_ex_width = #data >= 2 and 730 or 365
		self.node_list["ts_union_skill_list"].scroll_rect.enabled = #data > 2
		RectTransform.SetSizeDeltaXY(self.node_list["mask_root"].rect, self.m_ex_width, content_list_height)
		RectTransform.SetSizeDeltaXY(self.node_list["info_root"].rect, self.m_ex_width, content_list_height)
		self.node_list["info_root"].transform.localPosition = Vector3(-self.m_ex_width, 0, 0)
	end

	self.page_cell_list:SetDataList(data)
end

function TSUnionSkillPageRender:GetWidth()
	if self.m_ison then
		return self.m_width + self.m_ex_width
	else
		return self.m_width
	end
end

function TSUnionSkillPageRender:GetIsOn()
	return self.m_ison
end

function TSUnionSkillPageRender:ShowInfoPanel(is_show)
	self.m_ison = is_show and not self.m_ison or false
	local move_x = self.m_ison and 0 or -self.m_ex_width
	if self.node_list.info_root then
		self.node_list.info_root.rect:DOAnchorPosX(move_x, 0.5)
	end
end

function TSUnionSkillPageRender:DoMove(pos_x)
	local root = self:GetView()
	root.rect:DOAnchorPosX(pos_x, 0.5)
end

--技能子类---------------------------TianShenUnionSkillRender start
TianShenUnionSkillRender = TianShenUnionSkillRender or BaseClass(BaseRender)
function TianShenUnionSkillRender:__init()
	if not self.sub_list then
		self.sub_list = {}
	end
	for i = 1, 2 do
		self.sub_list[i] = TianShenUnionSkillSubRender.New(self.node_list["cell_root_" .. i])
	end
end

function TianShenUnionSkillRender:__delete()
	if self.sub_list then
		for k, v in pairs(self.sub_list) do
			v:DeleteMe()
		end
		self.sub_list = nil
	end
end

function TianShenUnionSkillRender:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data)then
		return
	end
	for i, v in ipairs(self.sub_list) do
		if data[i] then
			v:SetData(data[i])
		end
		self.node_list["cell_root_" .. i]:SetActive(not IsEmptyTable(data[i]))
	end

	--设置故事描述--策划要求每个Cell都要显示
	--[[
	local index = self:GetIndex()
	local story_desc = ""
	if data[1] then
		story_desc = Language.TianShen.TSUnionStorys[data[1].page_group] and Language.TianShen.TSUnionStorys[data[1].page_group][index]
	end
	self.node_list["down_desc"].text.text = story_desc
	--]]
end

--技能子子类---------------------------TianShenUnionSkillSubRender start
TianShenUnionSkillSubRender = TianShenUnionSkillSubRender or BaseClass(BaseRender)
function TianShenUnionSkillSubRender:__init()
	XUI.AddClickEventListener(self.node_list["one_key_fight_btn"], BindTool.Bind(self.OnClickOneKeyBtn, self))
	if not self.union_skill_head_list then
		self.union_skill_head_list = AsyncListView.New(TianShenUnionHeadRender, self.node_list["cell_union_skill_list"])
	end
end

function TianShenUnionSkillSubRender:__delete()
	if self.union_skill_head_list then
		self.union_skill_head_list:DeleteMe()
		self.union_skill_head_list = nil
	end
end

function TianShenUnionSkillSubRender:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data)then
		return
	end

	--技能标题,描述
	self.node_list["union_skill_name"].text.text = data.skill_name or ""
	local desc_list = Split(data.skill_describe or "", "\n")
	for i = 1, 2 do
		if desc_list[i] then
			self.node_list["union_skill_desc_" .. i].text.text = desc_list[i]
		end
		self.node_list["union_skill_desc_" .. i]:SetActive(desc_list[i] ~= nil)
	end

	--组合效果描述
	self.node_list["down_desc"].text.text = data.union_desc or ""

	--天神头像列表
	local active_num = 0
	local ts_idx_list = {}
	local temp_data = Split(data.tianshen_list or "", "|")
	if not IsEmptyTable(temp_data) then
		for i, v in ipairs(temp_data) do
			ts_idx_list[i] = {}
			ts_idx_list[i].ts_index = tonumber(v)

			if TianShenWGData.Instance:IsActivation(tonumber(v)) then
				active_num = active_num + 1
			end
		end
		self.union_skill_head_list:SetDataList(ts_idx_list)
	end

	--一键上阵按钮
	self.is_union_all_active = #temp_data > 0 and active_num >= #temp_data
	local is_skill_active = TianShenWGData.Instance:CheckTSUnionSkillActiveById(data.skill_id)
	self.node_list["img_has_active"]:SetActive(is_skill_active)
	self.node_list["one_key_fight_btn"]:SetActive(not is_skill_active)
	if not is_skill_active then
		XUI.SetGraphicGrey(self.node_list["one_key_fight_btn"], not self.is_union_all_active)
		self.node_list["txt_one_key_fight"].text.text = Language.TianShen.TSUnionOneKeyBtnName_1
	end
end

function TianShenUnionSkillSubRender:OnClickOneKeyBtn()
	local data = self:GetData()
	if IsEmptyTable(data) then
		TianShenWGCtrl.Instance:CloseTianShenUnionSkillView()--关闭
		return
	end

	--已激活技能
	if TianShenWGData.Instance:CheckTSUnionSkillActiveById(data.skill_id) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.TSUnionSkillHasActive)
		return
	end

	--组合是否全部激活
	if not self.is_union_all_active then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.TSUnionNotAllActive)
		return
	end

	local ts_idx_list = Split(data.tianshen_list or "", "|")
	if IsEmptyTable(ts_idx_list) then
		print_error("天神组合技能(tianshen_list字段)配置有误,技能Id:", data.skill_id)
		TianShenWGCtrl.Instance:CloseTianShenUnionSkillView()--关闭
		return
	end

	--槽位解锁判断
	if TianShenWGData.Instance:GetActiveZhanSitNum() < #ts_idx_list then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.TSUnionNotLockFightPos)
		return
	end

	--一键上阵判断
	local all_fight_pos_data = TianShenWGData.Instance:GetAllFightPosData()--全部位置信息
	local can_fight_list = {}
	local temp_pos_data, is_same_ts, t_fight_data

	-- print_error("all_fight_pos_data", #all_fight_pos_data, all_fight_pos_data)
	for pos = 0, #all_fight_pos_data do
		temp_pos_data = all_fight_pos_data[pos]
		if temp_pos_data.is_pos_act then--位置是否解锁
			t_fight_data = {}
			if temp_pos_data.image_index < 0 then--位置为空槽,位置可替换上阵
				t_fight_data.pos = pos
				t_fight_data.sort_key = temp_pos_data.image_index
				table.insert(can_fight_list, t_fight_data)
			else
				--比对需要一键上阵的天神
				is_same_ts = false
				for i, ts_idx in ipairs(ts_idx_list) do
					if temp_pos_data.image_index == tonumber(ts_idx) then
						is_same_ts = true
						table.remove(ts_idx_list, i)
						break
					end
				end

				--1.该位置已上阵的和待上阵中一致,位置不用替换上阵
				--2.该位置处于cd状态,位置不可替换上阵
				-- print_error("is_same_ts, temp_pos_data.is_in_cd", is_same_ts, temp_pos_data.is_in_cd)
				if not is_same_ts and not temp_pos_data.is_in_cd then
					t_fight_data.pos = pos
					t_fight_data.sort_key = temp_pos_data.image_index
					table.insert(can_fight_list, t_fight_data)
				end
			end
		end
	end

	--可上阵槽位判断
	-- print_error("can_fight_list, ts_idx_list", can_fight_list, ts_idx_list)
	if #can_fight_list < #ts_idx_list then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.TSUnionNotCDFightPos)
		return
	end

	--排序一下,优先空槽
	SortTools.SortAsc(can_fight_list, "sort_key")

	-- 请求一键上阵
	local t_fight_data
	for i, ts_idx in ipairs(ts_idx_list) do
		t_fight_data = can_fight_list[i]
		if t_fight_data then
			-- print_error(string.format("请求一键上阵 天神:%s, 位置:%s", ts_idx, t_fight_data.pos))
			TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type3, tonumber(ts_idx), t_fight_data.pos)
		end
	end
	TianShenWGCtrl.Instance:CloseTianShenUnionSkillView()--关闭
end
-----------------------------TianShenUnionSkillSubRender end

