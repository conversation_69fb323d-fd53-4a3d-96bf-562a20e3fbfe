ActorTrigger = ActorTrigger or BaseClass()

function ActorTrigger:__init(obj_type, param_list)
	self.obj_type = obj_type
	self.is_use_low_quality = obj_type == SceneObjType.Role
	self.param_list = param_list
	self.effects = nil
	self.sounds = nil
	self.camera_shakes = nil
	self.radial_blurs = nil

	self.animator = nil
	self.enable_effect = true
	self.enable_camera_shake = true
	self.enable_radial_blur = true
	self.is_modeify_mask = false
	self.has_created_effect_trigger = false
	self.play_no_actor_index = 1

	if obj_type == SceneObjType.MainRole or obj_type == SceneObjType.Role then
		local shield_type_skill_effect = ShieldObjType.RoleSkillEffect
		local shield_type_camera_shake = ShieldObjType.RoleCameraShake

		if obj_type == SceneObjType.MainRole then
			shield_type_skill_effect = ShieldObjType.MainRoleSkillEffect
			shield_type_camera_shake = ShieldObjType.MainRoleCameraShake
		end

		self.effects_handle = ReuseableHandleManager.Instance:GetShieldHandle(ActorTriggerEffectHandle, self, shield_type_skill_effect)
		self.camera_shake_handle = ReuseableHandleManager.Instance:GetShieldHandle(ActorTriggerCameraShakeHandle, self, shield_type_camera_shake)

		self.effects_handle:CreateShieldHandle()
		self.camera_shake_handle:CreateShieldHandle()
	end

	if obj_type == SceneObjType.MainRole then
		self.radial_blur_handle = ReuseableHandleManager.Instance:GetShieldHandle(ActorTriggerRadialBlurHandle, self, ShieldObjType.MainRoleRadialBlur)
		self.radial_blur_handle:CreateShieldHandle()
	end
end

function ActorTrigger:__delete()
	if self.effects then
		for _, effect in pairs(self.effects) do
			ActorTriggerEffect.Release(effect)
		end
		self.effects = nil
	end

	if self.sounds then
		for _, sound in pairs(self.sounds) do
			sound:DeleteMe()
		end
		self.sounds = nil
	end

	if self.camera_shakes then
		for _, camera_shake in pairs(self.camera_shakes) do
			camera_shake:DeleteMe()
		end
		self.camera_shakes = nil
	end

	if self.radial_blurs then
		for _, radial_blur in pairs(self.radial_blurs) do
			radial_blur:DeleteMe()
		end
		self.radial_blurs = nil
	end

	self:DeleteEffectHandle()
	self:DeleteCameraShakeHandle()
	self:DeleteRadialBlurHandle()

	self.param_list = nil
	self.ui3d_model = nil
	self.effect_custom_scale = nil
	self.effect_custom_rotation = nil

	if self.no_actor_effects ~= nil then
		for k,v in pairs(self.no_actor_effects) do
			ActorTriggerEffect.Release(v)
		end

		self.no_actor_effects = nil
	end

	self.no_actor_data = nil
end

function ActorTrigger:GetMainRole()
	return self.obj_type == SceneObjType.MainRole
end

function ActorTrigger:GetPrefabData()
	return self.prefab_data
end

function ActorTrigger:Reset()
	if self.effects then
		for _, effect in pairs(self.effects) do
			effect:Reset()
		end
	end

	if self.sounds then
		for _, sound in pairs(self.sounds) do
			sound:Reset()
		end
	end

	if self.camera_shakes then
		for _, camera_shake in pairs(self.camera_shakes) do
			camera_shake:Reset()
		end
	end

	if self.radial_blurs then
		for _, radial_blur in pairs(self.radial_blurs) do
			radial_blur:Reset()
		end
	end

	self.effect_custom_scale = nil
	self.effect_custom_rotation = nil

	if self.no_actor_effects then
		for _, effect in pairs(self.no_actor_effects) do
			effect:Reset()
		end
	end

	self.no_actor_data = nil
end

function ActorTrigger:SetPrefabData(data, is_modeify_mask, dynamic_data)
	if self.prefab_data == data then
		return
	end

	self:Reset()
	self.prefab_data = data
	self.is_modeify_mask = is_modeify_mask
	self.has_created_effect_trigger = false

	if self.prefab_data and self.prefab_data.actorTriggers then
		local actor_triggers = self.prefab_data.actorTriggers

		-- 特效
		-- 根据实际情况初始化effects
		self:CheckActorEffectTrigger()

		-- 音效
		-- 因为其它玩家写死了不播声音，所以主角才初始化sounds
		if self:GetMainRole() or self.ui3d_model ~= nil
			or self.obj_type == SceneObjType.Role
			or (self.param_list and self.param_list.is_force_audio)
			or self.obj_type == SceneObjType.SkillShower
			or self.obj_type == ShieldObjType.Boss then

			local sounds = actor_triggers.sounds or {}
			if #sounds > 0 then
				self.sounds = self.sounds or {}
			end

			for k, sound in pairs(sounds) do
				if self.sounds[k] then
					self.sounds[k]:InitData(sound.soundEventName, self.obj_type)
				else
					self.sounds[k] = ActorTriggerSound.New(sound.soundEventName, self.obj_type)
				end
				
				self.sounds[k]:Init(sound)
			end
		end

		-- 震屏
		-- 因为其它玩家写死了不震屏，所以主角才初始化camera_shake
		if self:GetMainRole() then
			local index = 1
			local use_camera_shakes = {}
			local camera_shakes = actor_triggers.cameraShakes or {}
			self.camera_shakes = self.camera_shakes or {}
			-- 一些特殊的技能是没有动作的，但是策划要震屏效果
			if #camera_shakes > 0 then
				for k,v in pairs(camera_shakes) do
					use_camera_shakes[index] = v
					index = index + 1
				end
			end

			if dynamic_data ~= nil and dynamic_data.camera_shakes ~= nil and camera_shakes ~= nil then
				for k,v in pairs(dynamic_data.camera_shakes) do
					use_camera_shakes[index] = v
					index = index + 1					
				end
			end			

			for k, camera_shake in pairs(use_camera_shakes) do
				if self.camera_shakes[k] then
					self.camera_shakes[k]:InitData(camera_shake.eventName)
				else
					self.camera_shakes[k] = ActorTriggerCameraShake.New(camera_shake.eventName)
				end
				self.camera_shakes[k]:Init(camera_shake)
				self.camera_shakes[k]:Enalbed(self.enable_camera_shake)
			end
		end

		-- 径向模糊
		if self:GetMainRole() then
			local radial_blurs = actor_triggers.radialBlurs or {}
			if #radial_blurs > 0 then
				self.radial_blurs = self.radial_blurs or {}
			end

			for k, radial_blur in pairs(radial_blurs) do
				if self.radial_blurs[k] then
					self.radial_blurs[k]:InitData()
				else
					self.radial_blurs[k] = ActorTriggerRadialBlur.New()
				end
				
				self.radial_blurs[k]:Init(radial_blur)
			end
		end
	end
end

-- 只有当enable_effect为true时才实例化ActorTriggerEffect
function ActorTrigger:CheckActorEffectTrigger()
	if self.has_created_effect_trigger or not self.enable_effect or nil == self.prefab_data or nil == self.prefab_data.actorTriggers then
		return
	end
	self.has_created_effect_trigger = true

	local effects = self.prefab_data.actorTriggers.effects or {}
	if #effects > 0 then
		self.effects = self.effects or {}
	end

	for k, effect in pairs(effects) do
		if self.effects[k] then
			self.effects[k]:Reset()
			self.effects[k]:InitData(effect.triggerEventName, self.is_modeify_mask, self.is_use_low_quality)
		else
			self.effects[k] = ActorTriggerEffect.GetActorTriggerEffect()
			self.effects[k]:InitData(effect.triggerEventName, self.is_modeify_mask, self.is_use_low_quality)
		end

		self.effects[k]:Enalbed(self.enable_effect)
		self.effects[k]:Init(effect)
		self.effects[k]:SetUI3dModel(self.ui3d_model)
		if self.effect_custom_scale ~= nil then
			self.effects[k]:SetEffectCustomScale(self.effect_custom_scale)
		end
		if self.target_effect_custom_scale ~= nil then
			self.effects[k]:SetTargetEffectCustomScale(self.target_effect_custom_scale)
		end
		if self.effect_custom_rotation ~= nil then
			self.effects[k]:SetEffectCustomRotation(self.effect_custom_rotation)
		end
	end
end

--[[
		默认值
	  	data = {
            triggerEventName = "",
            triggerDelay = 0,
            triggerFreeDelay = 0,
            effectGoName = "",
            effectAsset = {
                BundleName = bundle,
                AssetName = asset,
            },
            playerAtTarget = false,
            referenceNodeHierarchyPath = "",
            isAttach = false,
            isRotation = true,
            triggerStopEvent = "",
            effectBtnName = "",
            playerAtPos = false,
        }
]]
function ActorTrigger:TryPlayNoActorEffect(bundle, asset, param, state_info, source, target, extra_effect_data)
	if not self.enable_effect or nil == self.prefab_data or nil == self.prefab_data.actorTriggers then
		return
	end

	self.play_no_actor_index = self.play_no_actor_index + 1
	if self.play_no_actor_index > 4 then
		self.play_no_actor_index = 1
	end

	if self.no_actor_effects == nil then
		self.no_actor_effects = {}
	end

	if self.no_actor_data == nil then
		self.no_actor_data = {}
	end

	local index = self.play_no_actor_index
	local data = self.no_actor_data[index]
	if data == nil or extra_effect_data ~= nil then
		extra_effect_data = extra_effect_data or {}
  		data = {
            triggerEventName = "",
            triggerDelay = extra_effect_data.triggerDelay or 0,
            triggerFreeDelay = extra_effect_data.triggerFreeDelay or 0,
            effectGoName = "",
            effectAsset = {
                BundleName = bundle,
                AssetName = asset,
            },
            playerAtTarget = extra_effect_data.playerAtTarget or false,
            referenceNodeHierarchyPath = extra_effect_data.referenceNodeHierarchyPath or "",
            isAttach = extra_effect_data.isAttach or false,
            isRotation = extra_effect_data.isRotation == nil and true or extra_effect_data.isRotation,
            triggerStopEvent = "",
            effectBtnName = "",
            playerAtPos = extra_effect_data.playerAtPos or false,
        }

        self.no_actor_data[index] = data
    else
    	self.no_actor_data[index].effectAsset.BundleName = bundle
    	self.no_actor_data[index].effectAsset.AssetName = asset
	end

	if self.no_actor_effects[index] then
		self.no_actor_effects[index]:Reset()
		self.no_actor_effects[index]:InitData(data.triggerEventName, self.is_modeify_mask, self.is_use_low_quality)
	else
		self.no_actor_effects[index] = ActorTriggerEffect.GetActorTriggerEffect()
		self.no_actor_effects[index]:InitData(data.triggerEventName, self.is_modeify_mask, self.is_use_low_quality)
	end

	self.no_actor_effects[index]:Enalbed(self.enable_effect)
	self.no_actor_effects[index]:Init(data)
	self.no_actor_effects[index]:SetUI3dModel(self.ui3d_model)
	if self.effect_custom_scale ~= nil then
		self.no_actor_effects[index]:SetEffectCustomScale(self.effect_custom_scale)
	end

	if self.target_effect_custom_scale ~= nil then
		self.no_actor_effects[index]:SetTargetEffectCustomScale(self.target_effect_custom_scale)
	end

	if self.effect_custom_rotation ~= nil then
		self.no_actor_effects[index]:SetEffectCustomRotation(self.effect_custom_rotation)
	end

	self.no_actor_effects[index]:OnAnimatorEvent(param, state_info, source, target)
end

function ActorTrigger:SetEffectTriggerCustomScale(scale)
	self.effect_custom_scale = scale
	if self.effect_custom_scale ~= nil and self.effects then
		for _, effect in pairs(self.effects) do
			if effect then
				effect:SetEffectCustomScale(self.effect_custom_scale)
			end
		end
	end

	if self.effect_custom_scale ~= nil and self.no_actor_effects then
		for _, effect in pairs(self.no_actor_effects) do
			if effect then
				effect:SetEffectCustomScale(self.effect_custom_scale)
			end
		end
	end
end

function ActorTrigger:SetTargetEffectTriggerCustomScale(scale)
	self.target_effect_custom_scale = scale
	if self.target_effect_custom_scale ~= nil and self.effects then
		for _, effect in pairs(self.effects) do
			if effect then
				effect:SetTargetEffectCustomScale(self.target_effect_custom_scale)
			end
		end
	end

	if self.target_effect_custom_scale ~= nil and self.no_actor_effects then
		for _, effect in pairs(self.no_actor_effects) do
			if effect then
				effect:SetTargetEffectCustomScale(self.target_effect_custom_scale)
			end
		end
	end
end

function ActorTrigger:SetEffectTriggerCustomRotation(rotation)
	self.effect_custom_rotation = rotation
	if self.effect_custom_rotation ~= nil and self.effects then
		for _, effect in pairs(self.effects) do
			if effect then
				effect:SetEffectCustomRotation(self.effect_custom_rotation)
			end
		end
	end

	if self.effect_custom_rotation ~= nil and self.no_actor_effects then
		for _, effect in pairs(self.no_actor_effects) do
			if effect then
				effect:SetEffectCustomRotation(self.effect_custom_rotation)
			end
		end
	end
end


-- get/set
function ActorTrigger:EnableEffect(value)
	if value == nil then
		return self.enable_effect
	end

	if self.enable_effect ~= value then
		self.enable_effect = value
		self:CheckActorEffectTrigger()

		if self.effects then
			for _, effect in pairs(self.effects) do
				effect:Enalbed(value)
			end
		end

		if self.no_actor_effects then
			for _, effect in pairs(self.no_actor_effects) do
				effect:Enalbed(value)
			end
		end
	end
end

-- get/set
function ActorTrigger:EnableCameraShake(value)
	if value == nil then
		return self.enable_camera_shake
	end

	if self.enable_camera_shake ~= value then
		self.enable_camera_shake = value
		if self.camera_shakes then
			for _, camera_shake in pairs(self.camera_shakes) do
				camera_shake:Enalbed(value)
			end
		end
	end
end

function ActorTrigger:EnableRadialBlur(value)
	if value == nil then
		return self.enable_radial_blur
	end

	if self.enable_radial_blur ~= value then
		self.enable_radial_blur = value
		if self.radial_blurs then
			for _, radial_blur in pairs(self.radial_blurs) do
				radial_blur:Enalbed(value)
			end
		end
	end
end

function ActorTrigger:StopEffects()
	if self.effects then
		for _, effect in pairs(self.effects) do
			effect:StopEffects()
		end
	end

	if self.no_actor_effects then
		for _, effect in pairs(self.no_actor_effects) do
			effect:StopEffects()
		end
	end
end

function ActorTrigger:OnAnimatorEvent(param, state_info, source, target, anim_name)
	if self.effects then
		for _, effect in pairs(self.effects) do
			if effect.anima_name == anim_name then
				effect:OnAnimatorEvent(param, state_info, source, target, anim_name)
			end
		end
	end

	if self.sounds then
		for _, sound in pairs(self.sounds) do
			if sound.anima_name == anim_name then
				sound:OnAnimatorEvent(param, state_info, source, target, anim_name)
			end
		end
	end

	if self:GetMainRole() and self.camera_shakes then
		for _, camera_shake in pairs(self.camera_shakes) do
			if camera_shake.anima_name == anim_name then
				camera_shake:OnAnimatorEvent(param, state_info, source, target, anim_name)
			end
		end
	end

	if self:GetMainRole() and self.radial_blurs then
		for _, radial_blur in pairs(self.radial_blurs) do
			if radial_blur.anima_name == anim_name then
				radial_blur:OnAnimatorEvent(param, state_info, source, target, anim_name)
			end
		end
	end
end

function ActorTrigger:SetUI3dModel(ui3d_model)
	self.ui3d_model = ui3d_model
	if self.effects then
		for _, effect in pairs(self.effects) do
			effect:SetUI3dModel(ui3d_model)
		end
	end
end

function ActorTrigger:RemoveEffectsAndSounds()
	if self.effects then
		for _, effect in pairs(self.effects) do
			effect:RemoveEffects()
		end
	end

	if self.sounds then
		for _, sound in pairs(self.sounds) do
			sound:StopSounds()
		end
	end
end

function ActorTrigger:DeleteEffectHandle()
	if self.effects_handle then
		ReuseableHandleManager.Instance:ReleaseShieldHandle(self.effects_handle)
		self.effects_handle = nil
	end
end

function ActorTrigger:DeleteCameraShakeHandle()
	if self.camera_shake_handle then
		ReuseableHandleManager.Instance:ReleaseShieldHandle(self.camera_shake_handle)
		self.camera_shake_handle = nil
	end
end

function ActorTrigger:DeleteRadialBlurHandle()
	if self.radial_blur_handle then
		ReuseableHandleManager.Instance:ReleaseShieldHandle(self.radial_blur_handle)
		self.radial_blur_handle = nil
	end
end

function ActorTrigger:StopAllEffectPlay()
	if self.effects then
		for _, effect in pairs(self.effects) do
			effect:StopPlay()
		end
	end

	if self.no_actor_effects then
		for _, effect in pairs(self.no_actor_effects) do
			effect:StopPlay()
		end
	end
end


------------------------------ActorTriggerEffectHandle-----------------------------------------------

ActorTriggerEffectHandle = ActorTriggerEffectHandle or BaseClass(ReuseableShieldHandle)
function ActorTriggerEffectHandle:__init(actor_trigger, shield_obj_type)
	self:Init(actor_trigger, shield_obj_type)
end

function ActorTriggerEffectHandle:__delete()
	self:Clear()
end

function ActorTriggerEffectHandle:Init(actor_trigger, shield_obj_type)
	self.actor_trigger = actor_trigger
	self.shield_obj_type = shield_obj_type
end

function ActorTriggerEffectHandle:Clear()
	self.actor_trigger = nil
end

function ActorTriggerEffectHandle:VisibleChanged(visible)
	if self.actor_trigger then
		self.actor_trigger:EnableEffect(visible)
	end
end

---------------------------------------ActorTriggerCameraShakeHandle-----------------------------------

ActorTriggerCameraShakeHandle = ActorTriggerCameraShakeHandle or BaseClass(ReuseableShieldHandle)
function ActorTriggerCameraShakeHandle:__init(actor_trigger, shield_obj_type)
	self:Init(actor_trigger, shield_obj_type)
end

function ActorTriggerCameraShakeHandle:__delete()
	self:Clear()
end

function ActorTriggerCameraShakeHandle:Init(actor_trigger, shield_obj_type)
	self.actor_trigger = actor_trigger
	self.shield_obj_type = shield_obj_type
end

function ActorTriggerCameraShakeHandle:Clear()
	self.actor_trigger = nil
end

function ActorTriggerCameraShakeHandle:VisibleChanged(visible)
	if self.actor_trigger then
		self.actor_trigger:EnableCameraShake(visible)
	end
end


---------------------------------------ActorTriggerRadialBlurHandle-----------------------------------
ActorTriggerRadialBlurHandle = ActorTriggerRadialBlurHandle or BaseClass(ReuseableShieldHandle)
function ActorTriggerRadialBlurHandle:__init(actor_trigger, shield_obj_type)
	self:Init(actor_trigger, shield_obj_type)
end

function ActorTriggerRadialBlurHandle:__delete()
	self:Clear()
end

function ActorTriggerRadialBlurHandle:Init(actor_trigger, shield_obj_type)
	self.actor_trigger = actor_trigger
	self.shield_obj_type = shield_obj_type
end

function ActorTriggerRadialBlurHandle:Clear()
	self.actor_trigger = nil
end

function ActorTriggerRadialBlurHandle:VisibleChanged(visible)
	if self.actor_trigger then
		self.actor_trigger:EnableRadialBlur(visible)
	end
end





