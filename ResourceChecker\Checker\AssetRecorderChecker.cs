﻿#if UNITY_EDITOR
using System;
using System.IO;
using UnityEngine;
using UnityEditor;

public class AssetRecorderChecker : StaticCheck
{
    private static readonly string rootPath = Application.dataPath.Replace("/Assets", "");

    protected override string[] GetAssets()
    {
        return new string[] { "Assets/Resource Recordings.csv" };
    }

    protected override void Check(string assetPath, Action<CheckObject[]> callback)
    {
        string filePath = Path.Combine(rootPath, assetPath).Replace("\\", "/");
        if (File.Exists(filePath))
        {
            DateTime now = DateTime.Now;
            FileInfo info = new FileInfo(filePath);
            var timeSpan = now - info.LastWriteTime;
            if (timeSpan > new TimeSpan(7 * 24, 0, 0))
            {
                CheckObject checkObj = new CheckObject(null, string.Format("新手资源已经超过{0}天没有更新了，请及时处理", timeSpan.Days), WarningLevel.High);
                callback(new CheckObject[] { checkObj });
            }
        }
        else
        {
            CheckObject checkObj = new CheckObject(null, "新手资源丢失，请及时处理", WarningLevel.High);
            callback(new CheckObject[] { checkObj });
        }
    }

    protected override BaseWarningWindow GetWindow()
    {
        return UnityEditor.EditorWindow.GetWindowWithRect<RecorderWarningWindow>(new Rect(Screen.width / 2, 600, 800, 600));
    }

    protected override void ShowWindow()
    {
        base.ShowWindow();
        var window = GetWindow();
        window.onClose = null;
        window.onLostFocus = null;
    }
}
#endif