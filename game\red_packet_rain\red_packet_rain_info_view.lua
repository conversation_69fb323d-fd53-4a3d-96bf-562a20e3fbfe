RedPacketRainInfoView = RedPacketRainInfoView or BaseClass(SafeBaseView)

function RedPacketRainInfoView:__init()
    -- self.is_big_view = true
	self.is_safe_area_adapter = true
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
    self:SetMaskBg(false, false)
	self.view_layer = UiLayer.MainUILow

	self.view_cache_time = 0
	self:AddViewResource(0, "uis/view/red_packet_rain_ui_prefab", "layout_red_packet_rain_info")
end

function RedPacketRainInfoView:LoadCallBack()
    MainuiWGCtrl.Instance:AddInitCallBack(nil,function()
        if self:IsOpen() then
            local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
            self.obj = self.node_list["layout_view"].gameObject
            self.obj.transform:SetParent(parent.gameObject.transform, false)
        end
    end)
end

function RedPacketRainInfoView:ReleaseCallBack()

    -- CountDownManager.Instance:RemoveCountDown("mwjl_fb_count_down")
end

function RedPacketRainInfoView:OnFlush(param_t)
    self:UpdateInfo()
    self:UpdateReward()
	-- for k,v in pairs(param_t) do
	-- 	if k == "all" then
            
	-- 	end
	-- end
end


function RedPacketRainInfoView:OpenCallBack()
    -- local info = RedPacketRainWGData.Instance:GetAcitivityInfo()
    -- self.node_list.cap_time.text.text = TimeUtil.FormatSecondDHM9(invalid_time - TimeWGCtrl.Instance:GetServerTime())
    -- self.timer_1 = CountDown.Instance:AddCountDown(total_time, interval,
    --     -- 回调方法
    --     function(elapse_time, total_time)
    --         self.node_list.text_activity_time.text.text = ""
    --     end,
    --     -- 倒计时完成回调方法
    --     function()
    --     end
    -- )

    
end

function  RedPacketRainInfoView:ShowIndexCallBack()
    self:UpdateInfo()
    self:UpdateReward()
end

function RedPacketRainInfoView:CloseCallBack()
    self:CleanTimer1()
    self:CleanNextTimer()

end

-- 清除倒计时器1
function RedPacketRainInfoView:CleanTimer1()
    if self.timer_1 and CountDown.Instance:HasCountDown(self.timer_1) then
        CountDown.Instance:RemoveCountDown(self.timer_1)
        self.timer_1 = nil
    end
end

function RedPacketRainInfoView:CleanNextTimer()
    if self.next_timer and CountDown.Instance:HasCountDown(self.next_timer) then
        CountDown.Instance:RemoveCountDown(self.next_timer)
        self.next_timer = nil
    end

    if self.end_timer and CountDown.Instance:HasCountDown(self.end_timer) then
        CountDown.Instance:RemoveCountDown(self.end_timer)
        self.end_timer = nil
    end
end

function RedPacketRainInfoView:UpdateInfo()

    self:CleanNextTimer()

    local end_time, next_time = ActivityWGData.Instance:GetActivityResidueTime(RedPacketRainWGData.Instance:GetActivityId())

    local info = RedPacketRainWGData.Instance:GetAcitivityInfo()
    if info then
        if info.round_state == 1 then
            self.node_list.text_state.text.text = Language.RedPacketRain.WiatStr
        elseif info.round_state == 2 then
            self.node_list.text_state.text.text = Language.RedPacketRain.InRainStr
        elseif info.round_state == 3 then
            self.node_list.text_state.text.text = Language.RedPacketRain.NextStarStr
        elseif info.round_state == 0 then
            self.node_list.text_state.text.text = Language.RedPacketRain.EndStr
            self.node_list.text_next_time.text.text = ""
        end
        
        if info.role_info.role_id == 0 then
            self.node_list.text_desc.text.text = Language.RedPacketRain.SendSystem
        else
            self.node_list.text_desc.text.text = string.format(Language.RedPacketRain.SendRole, info.role_info.role_name)
        end
        
        if info.round_state ~= 0 then
            local next_total_time = info.next_state_time - TimeWGCtrl.Instance:GetServerTime()
            self.node_list.text_next_time.text.text = TimeUtil.FormatSecondDHM9(next_total_time)
            if next_total_time > 0 then
                self.next_timer = CountDown.Instance:AddCountDown(next_total_time, 1,
                -- 回调方法
                function(elapse_time, total_time)
                    self.node_list.text_next_time.text.text = TimeUtil.FormatSecondDHM9(info.next_state_time - TimeWGCtrl.Instance:GetServerTime())
                end,
                -- 倒计时完成回调方法
                function()
                end
            )
            end

        end

        self.node_list.text_activity_time.text.text = TimeUtil.FormatSecondDHM9(end_time)
        self.end_timer = CountDown.Instance:AddCountDown(end_time, 1,
            -- 回调方法
            function(elapse_time, total_time)
                self.node_list.text_activity_time.text.text = TimeUtil.FormatSecondDHM9(total_time - elapse_time)
            end,
            -- 倒计时完成回调方法
            function()
                self.node_list.text_activity_time.text.text = ""
            end
        )
    end
    



end

function RedPacketRainInfoView:UpdateReward()
    local role_info = RedPacketRainWGData.Instance:GetRoleInfo()
    if role_info then
        self.node_list.text_num_1.text.text = role_info["gold_sum"] or 0
        self.node_list.text_num_2.text.text = role_info["yuanbao_sum"] or 0
        self.node_list.text_num_3.text.text = role_info["coin_sum"] or 0
        self.node_list.text_num_4.text.text = role_info["score"] or 0
    end

end


