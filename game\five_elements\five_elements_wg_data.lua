FiveElementsWGData = FiveElementsWGData or BaseClass()

function FiveElementsWGData:__init()
	if FiveElementsWGData.Instance ~= nil then
		print_error("[FiveElementsWGData] attempt to create singleton twice!")
		return
	end
	FiveElementsWGData.Instance = self

	self.five_elements_cfg = ConfigManager.Instance:GetAutoConfig("five_elements_cfg_auto")
	self.part_cfg = self.five_elements_cfg.part
	self.skill_cfg = self.five_elements_cfg.skill
	self.stone_cfg = self.five_elements_cfg.stone
	self.store_item_cfg = ListToMap(self.five_elements_cfg.stone, "item_id")
	self.store_hole_cfg = ListToMapList(self.five_elements_cfg.stone, "hole")
	self.hole_level_cfg = ListToMap(self.five_elements_cfg.hole_level, "part", "hole", "level")
	self.compose_show_cfg = ListToMapList(self.five_elements_cfg.compose_show, "big_type")
	self.stone_compos_cfg = ListToMap(self.five_elements_cfg.stone_compos, "big_type", "small_type")
	self.skill_awaken_cfg = ListToMap(self.five_elements_cfg.skill_awaken, "level")
	self.part_level_cfg = ListToMap(self.five_elements_cfg.part_level, "part", "level")
	self.hole_show_cfg = ListToMapList(self.five_elements_cfg.hole_show, "big_type")
	self.hole_suit_cfg = ListToMapList(self.five_elements_cfg.hole_suit, "big_type", "small_type")
	self.talent_seq_cfg = ListToMap(self.five_elements_cfg.talent, "seq")
	self.talent_cost_cfg = ListToMap(self.five_elements_cfg.talent, "cost_item_id")
	self.hole_suit_seq_cfg = ListToMap(self.five_elements_cfg.hole_suit, "big_type", "small_type", "seq")

	self:InitCangMingData()  --初始化五行沧溟
	self:InitTreasuryData() --初始化宝库data

	self.talent_flag = {}
	self.stone_hole_cfg = {}
	self:CalculationStoreColorCfg()

	self.skill_awaken_level = 1
	self.compose_bag_stuff_cache = {}
	self.compose_wear_stuff_cache = {}
	self.bag_grid_list = {}
	self.bag_hole_color_info = {}
	self.part_item_list = {}
	self.active_skill_info = {}
	self.compose_remind_list = {}
	self.overview_remind_list = {}
	self.knapsack_remind_list = {}
	self.suit_active_flag_list = {}

	self.suit_info = {}
	self.suit_active_list = {}
	self.knapsack_tisheng_remind_cache = {}

	-- 总览红点
	self.knapscak_cur_select_part = 0

	RemindManager.Instance:Register(RemindName.FiveElement_Overview, BindTool.Bind(self.GetOverViewRemind, self))
	RemindManager.Instance:Register(RemindName.FiveElement_Knapsack, BindTool.Bind(self.GetKnapsackRemind, self))
	RemindManager.Instance:Register(RemindName.FiveElement_Talent, BindTool.Bind(self.GetTalentRemind, self))
	RemindManager.Instance:Register(RemindName.FiveElement_Treasury, BindTool.Bind(self.GetTreasuryRed, self))
	RemindManager.Instance:Register(RemindName.FiveElement_CangMing, BindTool.Bind(self.GetCangMingRemind, self))
end

function FiveElementsWGData:__delete()
	FiveElementsWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.FiveElement_Overview)
	RemindManager.Instance:UnRegister(RemindName.FiveElement_Knapsack)
	RemindManager.Instance:UnRegister(RemindName.FiveElement_Talent)
	RemindManager.Instance:UnRegister(RemindName.FiveElement_Treasury)
	RemindManager.Instance:UnRegister(RemindName.FiveElement_CangMing)
end

--------------------------------------------Remind_Start---------------------------------------------
function FiveElementsWGData:GetOverViewRemind()
	if not FunOpen.Instance:GetFunIsOpened(FunName.FiveElementsView) then
		return 0
	end

	if not IsEmptyTable(self:GetOverViewActiveSkillInfo()) then
		local current_level = self:GetSkillAwakenLevel()
		local up_need_data = self:GetSkillAwakenCfgByLevel(current_level)

		if not IsEmptyTable(up_need_data) then
			local is_max_level = current_level == self:GetSkillAwakenCfgLen()

			if not is_max_level then
				local has_num = ItemWGData.Instance:GetItemNumInBagById(up_need_data.cost_item_id)
	
				if  has_num >= up_need_data.cost_item_num then
					return 1
				end
			end
		end
	end

	return 0
end

function FiveElementsWGData:GetKnapsackRemind()
	if not FunOpen.Instance:GetFunIsOpened(FunName.FiveElementsView) then
		return 0
	end

	if self.knapsack_remind_list and self.knapsack_remind_list.knapsack_remind then
		return 1
	end

	if self.compose_remind_list and self.compose_remind_list.compose_remind then
		return 1
	end

	if self.suit_info and self.suit_info.remind then
		return 1
	end

	return self.knapsack_tisheng_remind_cache.remind and 1 or 0

end

function FiveElementsWGData:GetKnapsackTogUpFlag(part_id)
	return (self.knapsack_remind_list[part_id] or {}).part_remind or false
end

function FiveElementsWGData:GetKnapsackShengHuaFlag(part_id)
	return (self.knapsack_remind_list[part_id] or {}).shenghua_remind or false
end

function FiveElementsWGData:GetKnapsackXiangQianFlag(part_id, part_cell_id)
	return ((self.knapsack_remind_list[part_id] or {})[part_cell_id] or {}).xiangqian or false
end

function FiveElementsWGData:GetKnapsackQiangHuaFlag(part_id, part_cell_id)
	return ((self.knapsack_remind_list[part_id] or {})[part_cell_id] or {}).qianghua or false
end

function FiveElementsWGData:GetOverviewPartRemind(part_id)
	return self.knapsack_tisheng_remind_cache[part_id] or false
end

function FiveElementsWGData:GetFiveElementsHoleLevelCfg(part, hole, level)
	return ((self.hole_level_cfg[part] or {})[hole] or {})[level] or {}
end

function FiveElementsWGData:GetTalentRemind()
	if not FunOpen.Instance:GetFunIsOpened(FunName.FiveElementsView) then
		return 0
	end

	for k,v in pairs(self.talent_seq_cfg) do
		local is_remind = self:GetSingleTalentRemind(v.seq)

		if is_remind then
			return 1
		end
	end
	
	return 0
end

function FiveElementsWGData:GetFiveElementsPartCellRemind(part_id, part_cell_id)
	local xiangqian = self:GetKnapsackXiangQianFlag(part_id, part_cell_id)
	local qianghua = self:GetKnapsackQiangHuaFlag(part_id, part_cell_id)
	return xiangqian or qianghua
end

function FiveElementsWGData:GetSuitMainRemind()
	return self.suit_info and self.suit_info.remind or false
end

function FiveElementsWGData:GetKnapsackBagRemind(part_id)
	if IsEmptyTable(self.bag_grid_list) then
		return false
	end

	for i = 0, 7 do
		local remind = self:GetKnapsackXiangQianFlag(part_id, i)
		if remind then
			return true
		end
	end

	return false
end

--------------------------------------------Remind_ENd-----------------------------------------------

-----------------------------------------------选中Start------------------------------------------

function FiveElementsWGData:SetPartDefaultSelectId(part_id)
	self.knapscak_cur_select_part = part_id
end

function FiveElementsWGData:GetPartDefaultSelectId()
	return self.knapscak_cur_select_part
end

function FiveElementsWGData:GetSuitDefauitSelectBigType(big_type)
	local cur_select_big_type = big_type or 0
	cur_select_big_type = cur_select_big_type >= 0 and cur_select_big_type or 0

	local data_list = self:GetSuitInfo()
	if not IsEmptyTable(data_list) then
		local cur_remind = (data_list[cur_select_big_type] or {}).remind or false
		if cur_remind then
			return cur_select_big_type
		end
	
		for i = 0, 4 do
			local remind = (data_list[i] or {}).remind or false
			if remind then
				return i
			end
		end
	end

	return cur_select_big_type
end

function FiveElementsWGData:GetSuitDefauitSelectSmallType(big_type, small_type)
	local cur_select_big_type = big_type or 0
	local cur_small_type = small_type or 0
	cur_select_big_type = cur_select_big_type >= 0 and cur_select_big_type or 0
	cur_small_type = cur_small_type >= 0 and cur_small_type or 0

	local data_list = self:GetSuitInfo()
	if not IsEmptyTable(data_list) then
		local cur_remind = ((data_list[cur_select_big_type] or {})[cur_small_type + 1] or {}).remind or false
		if cur_remind then
			return cur_small_type
		end

		for j = 0, 6 do
			local remind = ((data_list[cur_select_big_type] or {})[j + 1] or {}).remind or false

			if remind then
				return j
			end
		end
	end

	return cur_small_type
end

function FiveElementsWGData:GetQiangHuaAttrData(up_data, set_data)
	local attr_data = {}
	for i = 1, 5 do
		if up_data["attr_id"..i] ~= 0 then
			attr_data[i] = {}
			attr_data[i].attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(up_data["attr_id"..i]))
			attr_data[i].attr_value = up_data["attr_value"..i]
			if set_data["attr_value"..i] then
				attr_data[i].add_value = set_data["attr_value"..i] - up_data["attr_value"..i]
			else
				attr_data[i].add_value = 0
			end
		end
	end
	return attr_data
end

function FiveElementsWGData:GetKnapsackDefaultSelectTogIndex(cur_select_index)
	local select_index = cur_select_index or 0

	if self:GetKnapsackTogUpFlag(select_index) then
		return select_index
	end

	for i = 0, 4 do
		if self:GetKnapsackTogUpFlag(i) then
			return i
		end
	end

	return select_index
end

function FiveElementsWGData:GetKnapsackDefaultSelectPartCellId(part_id, part_cell_id)
	local cur_select_part_cell_index = part_cell_id or -1

	if cur_select_part_cell_index >= 0 then
		local remind = self:GetKnapsackQiangHuaFlag(part_id, cur_select_part_cell_index)

		if remind then
			return cur_select_part_cell_index
		end

		for i = 0, 7 do
			local remind = self:GetKnapsackQiangHuaFlag(part_id, i)
			if remind then
				return i
			end
		end
	else
		for i = 0, 7 do
			local data = self:GetPartItemCellData(part_id, i)

			if not IsEmptyTable(data) then
				if data.color > GameEnum.ITEM_COLOR_WHITE then
					local remind = self:GetKnapsackQiangHuaFlag(part_id, i)

					if remind then
						return i
					end

					if cur_select_part_cell_index < 0 then
						cur_select_part_cell_index = i
					end
				end
			end
		end
	end
	
	return cur_select_part_cell_index
end

---------------------------------------------选中End------------------------------------------------

----------------------------------------------SetInfo_Start------------------------------------------
function FiveElementsWGData:SetFiveElementsInfo(protocol)
	self.skill_awaken_level = protocol.skill_awaken_level
	self.part_item_list = protocol.part_item_list
	self.main_pool_seq = protocol.reward_pool_seq
	self.talent_flag = protocol.talent_flag

	for k, v in pairs(protocol.part_item_list) do
		self.suit_active_flag_list[v.part_id] = bit:d2b_l2h(v.suit_active_flag, nil, true)
	end

	self:CalculationPreviewActiveSkillInfo()
	self:CalculationSuitCfg()
end

function FiveElementsWGData:SetFiveElementsBagInfo(protocol)
	local grid_list = protocol.grid_list
	self.bag_grid_list = grid_list
	self:CalculationComposeData()
end

function FiveElementsWGData:SetOverviewSkillData(protocol)
	self.skill_awaken_level = protocol.skill_awaken_level
end
----------------------------------------------SetInfo_End--------------------------------------------

----------------------------------------------UpdateInfo_Start---------------------------------------
function FiveElementsWGData:UpdateFiveElementsBagInfo(protocol)
	local new_data = protocol.grid_info
	local old_data = self.bag_grid_list[new_data.index]
	local no_old_data = IsEmptyTable(old_data)
	local is_add = false
	local change_num = 0

	if (no_old_data and new_data.item_id > 0 and new_data.num > 0) or (not no_old_data and old_data.item_id <= 0 and new_data.item_id > 0 and new_data.num > 0) then
		self.bag_grid_list[new_data.index] = new_data
		is_add = true
		change_num = new_data.num
	elseif not no_old_data and old_data.item_id > 0 and old_data.num > 0 and new_data.item_id > 0 and new_data.num > 0  then
		self.bag_grid_list[new_data.index] = new_data
		is_add = new_data.num > old_data.num
		change_num = new_data.num - old_data.num
	else
		self.bag_grid_list[new_data.index] = nil
	end

	self:CalculationComposeData()
	return is_add, change_num
end

function FiveElementsWGData:UpdatePartCellInfo(protocol)
	local part_item = protocol.part_item
	self.suit_active_flag_list[part_item.part_id] = bit:d2b_l2h(part_item.suit_active_flag, nil, true)
	self.part_item_list[protocol.part] = part_item

	self:CalculationComposeData()
	self:CalculationPreviewActiveSkillInfo()
	self:CalculationSuitCfg()
end
----------------------------------------------UpdateInfo_End-----------------------------------------

--------------------------------------------Calculation_Start----------------------------------------
-- 计算物品改变是否属于总览
function FiveElementsWGData:CheckIsOverviewItem(change_item_id, change_reason)
	if not IsEmptyTable(self:GetOverViewActiveSkillInfo()) then
		local current_level = self:GetSkillAwakenLevel()
		local up_need_data = self:GetSkillAwakenCfgByLevel(current_level)

		if not IsEmptyTable(up_need_data) and change_item_id == up_need_data.cost_item_id then
			return true
		end
	end

	return false
end

-- 计算物品改变是否属于背包
function FiveElementsWGData:CheckIsKnapsackItem(change_item_id, change_reason)
	-- 提升
	for i = 0, 4 do
		for j = 0, 7 do
			local data = self:GetPartItemCellData(i, j)
			if not IsEmptyTable(data) then
				if data.color > GameEnum.ITEM_COLOR_WHITE then
					local up_data = self:GetFiveElementsHoleLevelCfg(i ,j, data.level)

					if not IsEmptyTable(up_data) and change_item_id == up_data.cost_item_id then
						return true
					end
				end
			end
		end
	end

	-- 升华
	for i = 0, 4 do
		local data = self:GetPartItemData(i)
		if not IsEmptyTable(data) and data.color > GameEnum.ITEM_COLOR_WHITE then
			local up_need_data = self:GetFiveElementsPartLevelCfg(i, data.level)
			if not IsEmptyTable(up_need_data) and change_item_id == up_need_data.cost_item_id then
				return true
			end
		end
	end

	return false
end

-- 镶嵌替换
function FiveElementsWGData:GetStoreUpFlag(item_id, color, part_type)
	local hole = self:GetStoreInfoByItemId(item_id).hole
	local old_color = self:GetPartItemCellData(part_type, hole).color or GameEnum.ITEM_COLOR_WHITE
	return (old_color == GameEnum.ITEM_COLOR_WHITE or color > old_color)
end

function FiveElementsWGData:IsStoreCanWare(hole, item_color)
	local part = self:GetPartDefaultSelectId()
	local color_cfg = self:GetPartItemCellData(part, hole)
	local color = not IsEmptyTable(color_cfg) and color_cfg.color or GameEnum.NAME_COLOR_WHITE
	return color == GameEnum.NAME_COLOR_WHITE or item_color > color
end
--------------------------------------------Calculation_End------------------------------------------

--------------------------------------------Common_Get_Start------------------------------------------
function FiveElementsWGData:GetHoleSuitSeqCfg(big_type, small_type, seq)
	return ((self.hole_suit_seq_cfg[big_type] or {})[small_type] or {})[seq] or {}
end

function FiveElementsWGData:GetSkillAwakenCfgByLevel(level)
	return self.skill_awaken_cfg[level] or {}
end

function FiveElementsWGData:GetSkillAwakenCfgLen()
	return #self.skill_awaken_cfg
end

function FiveElementsWGData:GetPartCfg()
	return self.part_cfg
end

function FiveElementsWGData:GetPartItemData(part_type)
	return self.part_item_list[part_type] or {}
end

function FiveElementsWGData:GetPartItemDataColorList(part_type)
	return (self.part_item_list[part_type] or {}).color_list or {}
end

function FiveElementsWGData:GetPartItemCellData(part_type, cell_index)
	return ((self.part_item_list[part_type] or {}).hole_item_list or {})[cell_index] or {}
end

function FiveElementsWGData:GetPartItemHoleDataList(part_type)
	return (self.part_item_list[part_type] or {}).hole_item_list or {}
end

function FiveElementsWGData:GetFiveElementsSkillCap(skill_info, skill_level)
	local skill_cap = 0
	local skill_info = skill_info or self:GetOverViewActiveSkillInfo()
	local skill_level = skill_level or self:GetSkillAwakenLevel()

	if IsEmptyTable(skill_info) then
		return skill_cap
	end

	local skill_cfg = SkillWGData.Instance:GetWuXingSkillById(skill_info.skill_id, skill_level)
	
	if IsEmptyTable(skill_cfg) then
		return skill_cap
	end

	skill_cap = AttributeMgr.GetCapability(nil, skill_cfg)
	return skill_cap
end

function FiveElementsWGData:GetFiveElementsSkillCapValue()
	local capability = 0
	local skill_cap = self:GetFiveElementsSkillCap()
	local base_attribute = AttributePool.AllocAttribute()

	-- 五部位
	for i = 0, 4 do
		local color = self:GetPartItemData(i).color or GameEnum.NAME_COLOR_WHITE
		if color > GameEnum.NAME_COLOR_WHITE then
			local data_cfg = self:GetOverViewTiShengData(i)

			if not IsEmptyTable(data_cfg) then
				local cfg = self:GetAttrInfo(data_cfg)
				base_attribute = base_attribute + cfg
			end
		end

		-- 五部位8孔
		for j = 0, 7 do
			local cell_data = self:GetPartItemCellData(i, j)
			if not IsEmptyTable(cell_data) and cell_data.color > GameEnum.NAME_COLOR_WHITE then
				local store_cfg = self:GetStoreInfoByItemId(cell_data.item_id)

				if not IsEmptyTable(store_cfg) then
					base_attribute = base_attribute + self:GetAttrInfo(store_cfg)
				end

				local data = self:GetPartCellUpLevelInfo(i, j, cell_data.level)

				if not IsEmptyTable(data) then
					local cfg = self:GetAttrInfo(data)
					base_attribute = base_attribute + cfg
				end
			end
		end
	end

	-- 激活套装   重新计算  改为手动激活了
	if not IsEmptyTable(self.suit_active_list) then
		local hole_suit_cfg = self.hole_suit_cfg

		for k, v in pairs(self.suit_active_list) do
			local data = self:GetHoleSuitSeqCfg(v.big_type, v.small_type, v.seq)

			if not IsEmptyTable(data) then
				local cfg = self:GetAttrInfo(data)
				base_attribute = base_attribute + cfg
			end
		end
	end

	-- 天赋
	for k, v in pairs(self.talent_seq_cfg) do
		if self:GetTalentFlagBySeq(v.seq) then
			local cfg = self:GetAttrInfo(v)
			base_attribute = base_attribute + cfg
		end
	end

	capability = AttributeMgr.GetCapability(base_attribute)
	return capability + skill_cap
end

function FiveElementsWGData:GetOverViewJueXingData()
	local level = self:GetSkillAwakenLevel()
	local current_data = self:GetSkillAwakenCfgByLevel(level)
	local next_data = self:GetSkillAwakenCfgByLevel(level + 1)
	return current_data, next_data
end

function FiveElementsWGData:GetOverViewTiShengData(part_id)
	local part_id = part_id
	local part_data = {}
	local current_data = {}
	local next_data = {}

	if part_id < 0 then
		return part_data, current_data
	end

	part_data = self:GetPartItemData(part_id)

	if not IsEmptyTable(part_data) and part_data.level then
		current_data = self:GetFiveElementsPartLevelCfg(part_id, part_data.level)
		next_data = self:GetFiveElementsPartLevelCfg(part_id, part_data.level + 1)
	end

	return current_data, next_data
end

-- 背包
function FiveElementsWGData:GetFiveElementsBagListInfo()
	local data_list = SortDataByItemColor(self.bag_grid_list)
	return data_list
end

function FiveElementsWGData:GetStoreInfoByItemId(item_id)
	return self.store_item_cfg and self.store_item_cfg[item_id] or {}
end

function FiveElementsWGData:GetStoreCfg()
	return self.stone_cfg
end

function FiveElementsWGData:GetPartCellUpLevelInfo(part_id, part_cell_id, level)
	local current_data = self:GetFiveElementsHoleLevelCfg(part_id, part_cell_id, level)
	local next_data = self:GetFiveElementsHoleLevelCfg(part_id, part_cell_id, level + 1) or {}
	return current_data, next_data
end

function FiveElementsWGData:GetAttrInfo(data, data_len)
	local attr_list = {}
	local attr_id, attr_value = 0, 0
	local data_len = data_len or 5

	for i = 1, data_len do
		attr_id = data["attr_id" .. i]
		attr_value = data["attr_value" .. i]
		if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
			attr_list[attr_str] = attr_value
		end
	end

	return attr_list
end

function FiveElementsWGData:GetSuitBigItemListInfo()
	return self.hole_show_cfg
end

function FiveElementsWGData:GetSuitBigItemListInfoByIndex(index)
	return self.hole_show_cfg[index]
end

function FiveElementsWGData:GetComposeRemind()
	return self.compose_remind_list
end

function FiveElementsWGData:GetComposeDefaultSelect(select_big_type, select_smalltype)
	local compose_remind_list = self:GetComposeRemind()

	if IsEmptyTable(compose_remind_list) then
		return select_big_type, select_smalltype
	end 

	local big_type_remind = (compose_remind_list[select_big_type] or {}).remind or false
	if big_type_remind then
		local remind_list = compose_remind_list[select_big_type]

		if not IsEmptyTable(remind_list) then
			if remind_list[select_smalltype].remind then
				return 	select_big_type, select_smalltype
			else
				for k, v in pairs(remind_list) do
					if type(v) == "table" then
						if v.remind then
							return select_big_type, k
						end
					end
				end
			end
		end
	else
		for k, v in pairs(compose_remind_list) do
			if type(v) == "table" then
				if v.remind then
					for i, u in pairs(v) do
						if type(u) == "table" then
							if u.remind then
								return k, i
							end
						end
					end
				end
			end
		end
	end

	return select_big_type, select_smalltype
end

function FiveElementsWGData:GetJueXingSkillInfo(get_next_data, skill_id, skill_level)
	local skill_id = skill_id or self:GetOverViewActiveSkillId()
	local skill_level = skill_level or (get_next_data and (self:GetSkillAwakenLevel() + 1) or self:GetSkillAwakenLevel())
	local skill_cfg = SkillWGData.Instance:GetWuXingSkillById(skill_id, skill_level)
	local max_level = self:GetSkillAwakenCfgLen()

	if IsEmptyTable(skill_cfg) then
		return {}
	end

	local skill_client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, skill_level)
	if IsEmptyTable(skill_client_cfg) then
		return {}
	end

	local cur_skill_desc = skill_client_cfg.description
	local next_skill_desc = ""
	local limit_text = ""
	local real_limit_text = ""
	local capability = self:GetFiveElementsSkillCap()
	local is_max = skill_level >= max_level
	if not is_max then
		local skill_client_next_level_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, skill_level + 1)
		next_skill_desc = skill_client_next_level_cfg.description
		real_limit_text = Language.FiveElements.SkillNextNeedDesc
	end

	local skill_type_tab = SkillWGData.Instance:GetSkillAttackType(skill_id)
	local show_data = {
		icon = skill_client_cfg.icon_resource,
		top_text = skill_cfg.skill_name,	-- 技能名
		skill_level = skill_level,
		body_text = cur_skill_desc,							-- 当前等级技能描述
		limit_text = limit_text ~= "" and limit_text or real_limit_text,
		capability = capability,
		x = 0,
		y = 0,
		set_pos2 = true,
	}

	return show_data
end

function FiveElementsWGData:GetSuitActiveFlag(part_id, seq)
	return self.suit_active_flag_list[part_id][seq] == 1
end

function FiveElementsWGData:GetDEfaultShowShill()
	return self.skill_cfg[1]
end

 function FiveElementsWGData:GetSlillTipsCfg(index)
	return self.skill_cfg[index].next_txt
 end

function FiveElementsWGData:GetFiveElementsPartLevelCfg(part, level)
	return (self.part_level_cfg[part] or {})[level] or {}
end

--------------------------------------------Common_Get_End--------------------------------------------

-------------------------------------suit_start--------------------------------------
function FiveElementsWGData:CalculationStoreColorCfg()
	local stone_hole_cfg = {}

	for k, v in pairs(self.store_hole_cfg) do
		for i, u in pairs(v) do
			local _, color = ItemWGData.Instance:GetItemColor(u.item_id)
			stone_hole_cfg[k] = stone_hole_cfg[k] or {}
			stone_hole_cfg[k][color] = u.item_id
		end
	end

	self.stone_hole_cfg = stone_hole_cfg
end

function FiveElementsWGData:GetStoreHoleCfg()
	return self.stone_hole_cfg or {}
end
--------------------------------------suit_end---------------------------------------

---------------------------------------技能觉醒等级start------------------------------
function FiveElementsWGData:GetSkillAwakenLevel()
	return self.skill_awaken_level
end
----------------------------------------技能觉醒等级end-------------------------------

---------------------------------------计算合成信息start------------------------------
function FiveElementsWGData:CalculationComposeData()
	local compose_bag_stuff_cache = {}
	local compose_wear_stuff_cache = {}
	local bag_hole_color_info = {}

	--镶嵌的数据
	for k, v in pairs(self.part_item_list) do
		if not IsEmptyTable(v.inlaid_items) then
			for i, u in pairs(v.inlaid_items) do
				local data = {item_id = u.item_id, color = u.color, part_id = u.part_id, hole = u.hole, num = 1}
				compose_wear_stuff_cache[u.item_id] = compose_wear_stuff_cache[u.item_id] or {}
				table.insert(compose_wear_stuff_cache[u.item_id], data)
			end
		end
	end

	--背包数据
	for m, n in pairs(self.bag_grid_list) do
		compose_bag_stuff_cache[n.item_id] = compose_bag_stuff_cache[n.item_id] or {}
		local data = {item_id = n.item_id, color = n.color, part_id = -1, hole = -1, num = n.num}
		table.insert(compose_bag_stuff_cache[n.item_id], data)

		local store_cfg = FiveElementsWGData.Instance:GetStoreInfoByItemId(n.item_id)
		if not IsEmptyTable(store_cfg) then
			bag_hole_color_info[store_cfg.hole] = bag_hole_color_info[store_cfg.hole] or {}
			table.insert(bag_hole_color_info[store_cfg.hole], n)
		end		
	end

	self.compose_bag_stuff_cache = compose_bag_stuff_cache
	self.compose_wear_stuff_cache = compose_wear_stuff_cache
	self.bag_hole_color_info = bag_hole_color_info
	self:CalculationComposeRemind()
end

-- 获取 孔位得装备数据 已排序，最高品质在第一位
function FiveElementsWGData:GetHoleEquipDataListCache(hole, need_sort)
	local target_data_list = self.bag_hole_color_info[hole] or {}

	if need_sort and not IsEmptyTable(target_data_list) then
		table.sort(target_data_list, SortTools.KeyUpperSorter("color"))
	end

	return target_data_list
end

function FiveElementsWGData:GetComposeToggleListInfo(index)
	local target_data_list = {}
	local data_list = self:GetComposeBigItemListInfoByType(index)

	for k, v in pairs(data_list) do
		local compose_data = self:GetComposeData(v.big_type, v.small_type)

		if not IsEmptyTable(compose_data) then
			local data = {
				name = v.name,
				big_type = v.big_type,
				small_type = v.small_type,
				item_id = compose_data.item_id,
				stuff_id = compose_data.stuff_id,
				stuff_num = compose_data.stuff_num,
			}

			table.insert(target_data_list, data)
		end
	end

	return target_data_list
end

function FiveElementsWGData:GetComposeBigItemListInfo()
	return self.compose_show_cfg
end

function FiveElementsWGData:GetComposeBigItemListInfoByType(type)
	return self.compose_show_cfg[type]
end

-- 合成只允许有一个已经镶嵌的当材料
function FiveElementsWGData:GetCanComposeNum(stuff_id)
	local has_num = 0
	local compose_bag_stuff_cache_num = self:GetComposeBagCacheNumByItemId(stuff_id)
	local compose_wear_stuff_cache = self:GetComposeWearCacheByItemId(stuff_id)
	local bag_compose_data = (compose_wear_stuff_cache or {})[1] or {}

	if not IsEmptyTable(bag_compose_data) then
		has_num = has_num + 1
	end

	if compose_bag_stuff_cache_num > 0 then
		has_num = has_num + compose_bag_stuff_cache_num
	end

	return has_num, bag_compose_data
end

function FiveElementsWGData:GetComposeBagCacheByItemId(item_id)
	return self.compose_bag_stuff_cache[item_id] or {}
end

function FiveElementsWGData:GetComposeBagCacheNumByItemId(item_id)
	local data_list = self:GetComposeBagCacheByItemId(item_id)
	local num = 0

	for k, v in pairs(data_list) do
		if v.num and v.num > 0 then
			num = num +v.num
		end
	end

	return num
end

function FiveElementsWGData:GetComposeWearCacheByItemId(item_id)
	return self.compose_wear_stuff_cache[item_id] or {}
end

function FiveElementsWGData:GetComposeData(big_type, small_type)
	return (self.stone_compos_cfg[big_type] or {})[small_type] or {}
end
---------------------------------------计算合成信息end----------------------------

---------------------------------------纵览面板技能start---------------------------------
-- 计算总览面板激活的技能
function FiveElementsWGData:CalculationPreviewActiveSkillInfo()
	local skill_cfg = {}
	local color_list = {}
	local active_part_num = 0

	for i = 0, 4 do
		local cfg = self:GetPartItemData(i)

		if not IsEmptyTable(cfg) then
			local color = cfg.color or GameEnum.ITEM_COLOR_WHITE

			if color > GameEnum.ITEM_COLOR_WHITE then
				active_part_num = active_part_num + 1
	
				for j = color, 0, -1 do
					color_list[j] = color_list[j] or {}
					color_list[j].num = color_list[j].num and color_list[j].num + 1 or 1
				end
			end
		end
	end

	for k, v in pairs(self.skill_cfg) do
		if not IsEmptyTable(color_list[v.need_color]) and v.need_active_part <= color_list[v.need_color].num and v.need_active_part <= active_part_num then
			skill_cfg = v
		end
	end

	self.active_skill_info = skill_cfg
end

function FiveElementsWGData:GetOverViewActiveSkillInfo()
	return self.active_skill_info
end

function FiveElementsWGData:GetOverViewActiveSkillId()
	return self.active_skill_info and self.active_skill_info.skill_id or -1
end

---------------------------------------纵览面板技能end----------------------------------------

---------------------------------------红点计算start------------------------------------------
--合成标题红点及跳转计算
function FiveElementsWGData:CalculationComposeRemind()
	local compose_remind = false
	local big_type = #self:GetComposeBigItemListInfo()
	local remind_list = {}

	for i = 1, big_type do
		local small_type_list = self:GetComposeToggleListInfo(i)
		local big_type_remind = false
		remind_list[i] = remind_list[i] or {}

		for k, v in pairs(small_type_list) do
			remind_list[v.big_type] = remind_list[v.big_type] or {}
			remind_list[v.big_type][v.small_type] = remind_list[v.big_type][v.small_type] or {}
			remind_list[v.big_type][v.small_type].remind = false

			if self:GetCanComposeNum(v.stuff_id) >= v.stuff_num then
				remind_list[v.big_type][v.small_type].remind = true
				big_type_remind = true
				compose_remind = true
			end
		end

		remind_list[i].remind = big_type_remind
	end

	remind_list.compose_remind = compose_remind
	self.compose_remind_list = remind_list
end

-- 背包提升面板红点缓存
function FiveElementsWGData:CalculationOverViewRemind()
	local knapsack_tisheng_remind_cache = {}
	knapsack_tisheng_remind_cache.remind = false
	for i = 0, 4 do
		knapsack_tisheng_remind_cache[i] = false
		local data = self:GetPartItemData(i)

		if not IsEmptyTable(data) and data.color > GameEnum.ITEM_COLOR_WHITE then
			local up_need_data = self:GetFiveElementsPartLevelCfg(i, data.level)
			local is_max_level = data.level == #self.part_level_cfg[i]

			if not IsEmptyTable(up_need_data) then
				local has_num = ItemWGData.Instance:GetItemNumInBagById(up_need_data.cost_item_id)

				if not is_max_level and has_num >= up_need_data.cost_item_num then
					knapsack_tisheng_remind_cache[i] = true
					knapsack_tisheng_remind_cache.remind = true
				end
			end
		end
	end

	self.knapsack_tisheng_remind_cache = knapsack_tisheng_remind_cache
end

-- 背包面板红点
function FiveElementsWGData:CalculationKnapsackRemind()
	local knapsack_remind_list = {}
	knapsack_remind_list.knapsack_remind = false

	for i = 0, 4 do
		knapsack_remind_list[i] = knapsack_remind_list[i] or {}
		knapsack_remind_list[i].part_remind = false
		knapsack_remind_list[i].shenghua_remind = false

		local part_data = self:GetPartItemData(i)
		if not IsEmptyTable(part_data) and part_data.color > GameEnum.ITEM_COLOR_WHITE then
			local up_need_data = self:GetFiveElementsPartLevelCfg(i, part_data.level)
			local is_max_level = part_data.level == #self.part_level_cfg[i]

			if not IsEmptyTable(up_need_data) then
				local has_num = ItemWGData.Instance:GetItemNumInBagById(up_need_data.cost_item_id)

				if not is_max_level and has_num >= up_need_data.cost_item_num then
					knapsack_remind_list.knapsack_remind = true
					knapsack_remind_list[i].part_remind = true
					knapsack_remind_list[i].shenghua_remind = true
				end
			end
		end

		for j = 0, 7 do
			knapsack_remind_list[i][j] = knapsack_remind_list[i][j] or {}
			knapsack_remind_list[i][j].xiangqian = false
			
			local data = self:GetPartItemCellData(i, j)
			local old_color = data.color or GameEnum.ITEM_COLOR_WHITE
			-- local hole_info = self.bag_hole_color_info[j]
			local hole_equip_list_cache = self:GetHoleEquipDataListCache(j, true)

			if not IsEmptyTable(hole_equip_list_cache) then
				local best_color = (hole_equip_list_cache[1] or {}).color or GameEnum.ITEM_COLOR_WHITE

				if best_color > old_color then
					knapsack_remind_list[i][j].xiangqian = true
					knapsack_remind_list[i].part_remind = true
	        		knapsack_remind_list.knapsack_remind = true
				end
			end

			knapsack_remind_list[i][j].qianghua = false

			if not IsEmptyTable(data) then
				if data.color > GameEnum.ITEM_COLOR_WHITE then
					local level = data.level
					local up_data = self:GetFiveElementsHoleLevelCfg(i, j, level)
					local is_max_level = IsEmptyTable(self:GetFiveElementsHoleLevelCfg(i, j, level + 1))

					if not IsEmptyTable(up_data) then
						local need_item, need_num = up_data.cost_item_id, up_data.cost_item_num
						local has_num = ItemWGData.Instance:GetItemNumInBagById(need_item)

						if not is_max_level and has_num >= need_num then
							knapsack_remind_list[i][j].qianghua = true
							knapsack_remind_list[i].part_remind = true
							knapsack_remind_list.knapsack_remind = true
						end
					end
				end
			end
		end
	end

	self.knapsack_remind_list = knapsack_remind_list
end
---------------------------------------红点计算end------------------------------------------

---------------------------------------套装start------------------------------------------
function FiveElementsWGData:CalculationSuitCfg()
	local suit_info = {remind = false}
	local active_suit_list = {}

	for i = 0, 4 do
		local data_list = self:GetSuitBigItemListInfoByIndex(i)
		local big_type_cell = {remind = false}

		for k, v in pairs(data_list) do              --小标题
			local suit_data_list = self:GetHoleSuitCfgByType(v.big_type, v.small_type)
			
			if not IsEmptyTable(suit_data_list) then
				local small_type_data = {remind = false}
				local end_data = suit_data_list[#suit_data_list]

				if not IsEmptyTable(end_data) then
					local end_color_list = self:GetPartItemDataColorList(end_data.part)

					local end_color_data = (end_color_list or {})[end_data.color] or {}
					small_type_data.name = string.format(Language.FiveElements.SuitSmallTypeName, v.name, end_color_data.compnum or 0, end_data.need_num)
					small_type_data.num = end_color_data.num or 0
					small_type_data.part_cell_id_list = end_color_data.part_cell_id_list or {}
					small_type_data.comppart_cell_id_list = end_color_data.comppart_cell_id_list or {}
				end

				for a, b in pairs(suit_data_list) do
					local color_list = self:GetPartItemDataColorList(b.part)
					local compnum = ((color_list or {})[b.color] or {}).compnum or 0
					local can_active = compnum >= b.need_num
					local is_active = self:GetSuitActiveFlag(b.part, b.seq)
					local remind = not is_active and can_active
					small_type_data.color = b.color

					if remind then
						small_type_data.remind = true
					end

					if is_active then
						table.insert(active_suit_list, {big_type = v.big_type, small_type = v.small_type, seq = b.seq})
					end

					local suit_attr_data = {
						name = v.name,
						big_type = v.big_type,
						small_type = v.small_type,
						part = b.part,
						color = b.color,
						need_num = b.need_num,
						seq = b.seq,
						attr_data = b, -- ItemShowWGData.Instance:OutStrAttrListAndCapalityByAttrId(b, "attr_id", "attr_value"),
						can_active = can_active,
						is_active = is_active,
						remind = remind,
					}

					table.insert(small_type_data, suit_attr_data)
				end

				big_type_cell[v.small_type] = small_type_data
				
				if small_type_data.remind then
					big_type_cell.remind = true
				end
			else
				big_type_cell.name = v.name
			end
		end
		
		suit_info[i] = big_type_cell

		if big_type_cell.remind then
			suit_info.remind = true
		end
	end

	self.suit_info = suit_info
	self.suit_active_list = active_suit_list
end

function FiveElementsWGData:GetSuitInfo()
	return self.suit_info
end

function FiveElementsWGData:GetHoleSuitCfgByType(big_type, small_type)
	return (self.hole_suit_cfg[big_type] or {})[small_type] or {}
end
---------------------------------------套装end------------------------------------------

------------------------------------------五行天赋数据-----------------
-- 天赋激活改变
function FiveElementsWGData:SetSingleTalentInfo(protocol)
	self.talent_flag = protocol.talent_flag
end

function FiveElementsWGData:GetAllTalentCfg()
	return self.talent_seq_cfg
end

function FiveElementsWGData:GetTalentCfgBySeq(seq)
    return self.talent_seq_cfg[seq] or {}
end

function FiveElementsWGData:GetTalentFlagBySeq(seq)
    return self.talent_flag[seq] and self.talent_flag[seq] == 1
end

function FiveElementsWGData:GetCurTalentAttrList(seq)
	local cfg = self:GetTalentCfgBySeq(seq)
    local attr_list = {}

    if IsEmptyTable(cfg) then
        return attr_list
    end

    local is_active = self:GetTalentFlagBySeq(seq)
    local em_data = EquipmentWGData.Instance
    local attr_id, attr_value = 0, 0
    local max_attr_num = 5

    for i = 1, max_attr_num do
        attr_id = cfg["attr_id" .. i]
        attr_value = cfg["attr_value" .. i]
        if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
            local attr_str = em_data:GetAttrStrByAttrId(attr_id)
            local data = {
                attr_str = attr_str,
                attr_value = is_active and attr_value or 0,
                add_value = is_active and 0 or attr_value,
                attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str),
            }

            table.insert(attr_list, data)
        end
    end

    if not IsEmptyTable(attr_list) then
        table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
    end

    return attr_list
end

function FiveElementsWGData:GetSingleTalentRemind(seq)
	local is_remind = false
	local cfg = self:GetTalentCfgBySeq(seq)

	if not IsEmptyTable(cfg) then
		local is_active = FiveElementsWGData.Instance:GetTalentFlagBySeq(cfg.seq)
		local pre_is_active = true

		if cfg.per_seq >= 0 then
			pre_is_active = FiveElementsWGData.Instance:GetTalentFlagBySeq(cfg.per_seq)
		end

    	local has_num = ItemWGData.Instance:GetItemNumInBagById(cfg.cost_item_id)

    	if pre_is_active and has_num >= cfg.cost_item_num and not is_active then
    		is_remind = true
    	end
	end

	return is_remind
end

function FiveElementsWGData:GetIsTalentStuff(item_id)
    return self.talent_cost_cfg[item_id] ~= nil
end
------------------------------------------五行天赋数据End-----------------