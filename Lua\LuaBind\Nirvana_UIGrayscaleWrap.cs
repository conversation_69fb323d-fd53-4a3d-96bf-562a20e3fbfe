﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_UIGrayscaleWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(Nirvana.UIGrayscale), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("__eq", op_Equality);
		L<PERSON>RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("GrayScale", get_GrayScale, set_GrayScale);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_GrayScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UIGrayscale obj = (Nirvana.UIGrayscale)o;
			int ret = obj.GrayScale;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index GrayScale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_GrayScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UIGrayscale obj = (Nirvana.UIGrayscale)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.GrayScale = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index GrayScale on a nil value");
		}
	}
}

