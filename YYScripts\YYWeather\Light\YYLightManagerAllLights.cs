﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public partial class YYLightManager
{

    private readonly List<LightState> lights = new List<LightState>();

    public class LightState
    {
        public Light Light { get; internal set; }

        internal float Update(Camera camera)
        {
            return 0.0f;
        }
    }

    private void UpdateAllLights()
    {
        Light[] allLights = GameObject.FindObjectsOfType<Light>();
        lights.Clear();
        foreach (Light light in allLights)
        {
            if (light.type == LightType.Directional)  //自己加的
            {
                if (light != null && light.enabled)// && light.intensity > 0.0f && light.color.a > 0.0f && (IgnoreLights == null || !IgnoreLights.Contains(light)))
                {
                    lights.Add(GetOrCreateLightState(light));
                }
            }
        }
    }

    private LightState GetOrCreateLightState(Light light)
    {
        return new LightState { Light = light };
    }

    public static YYCelestialObject SunForCamera(Camera camera)
    {
        if (Instance == null)
        {
            return null;
        }

        return Instance.sunPerspective;

        //return (camera == null || !camera.orthographic ? Instance.SunPerspective : Instance.SunOrthographic);
    }

    public static Color EvaluateGradient(Gradient gradient, float lookup)
    {
        if (gradient == null)
        {
            return Color.white;
        }
        Color color = gradient.Evaluate(lookup);
        float a = color.a;
        color *= color.a;
        color.a = a;
        return color;
    }

}
