PremiumGiftView = PremiumGiftView or BaseClass(SafeBaseView)

local ROOT_POSITION = {
    [1] = Vector2(0, 54),
    [2] = Vector2(-12, 70),
    [3] = Vector2(-12, 63),
    [4] = Vector2(-31, 58)
}

local ROOT_POSITION2 = {
    [1] = Vector2(0, -70),
    [2] = Vector2(-20, -70),
    [3] = Vector2(-39, -70),
    [4] = Vector2(-24, -70),
}

function PremiumGiftView:__init()
    self:SetMaskBg()

    self.is_safe_area_adapter = true
    self.view_style = ViewStyle.Window
    self.cur_select_sub_index = -1

    self:AddViewResource(0, "uis/view/rebate_gift_ui_prefab", "premium_gift_view")
end

function PremiumGiftView:LoadCallBack()
    self.index = -1

    for i = 0, 4 do
        self.node_list["toggle_" .. i].toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self, i))
    end

    self.big_act_list = {}
    for i = 1, 10 do
        self.big_act_list[i] = PremiumGiftSubTaskItem.New(self.node_list.big_content:FindObj("premium_gift_small_item" .. i))
    end

    -- if not self.small_act_list then
    --     self.small_act_list = AsyncListView.New(PremiumGiftSubTaskItem, self.node_list["small_act_list"])
    -- end

    if not self.model_display then
        self.model_display = OperationActRender.New(self.node_list["display_root"])
    end

    if not self.sub_display then
        self.sub_display = OperationActRender.New(self.node_list["sub_display_root"])
    end

    if not self.fz_display then
        self.fz_display = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["fzdisplay"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = true,
        }
        
        self.fz_display:SetRenderTexUI3DModel(display_data)
        -- self.fz_display:SetUI3DModel(self.node_list["fzdisplay"].transform,
        --     self.node_list.fzdisplay.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.fz_display)
    end

    -- if not self.gift_reward_list then
    --     self.gift_reward_list = AsyncListView.New(ItemCell, self.node_list["gift_reward_list"])
    -- end

    self:InitTitleAttr()
    self:LoginTimeCountDown()
    self.node_list.sub_title_top_txt.text.text = Language.PremiumGift.SubTitleTopTxt
    self.node_list.sub_title_bom_txt.text.text = Language.PremiumGift.SubTitleBomTxt
    --self.node_list.main_title_top_txt.text.text = Language.PremiumGift.MainTitleTopTxt
    --self.node_list.main_title_bom_txt.text.text = Language.PremiumGift.MainTitleBomTxt

    XUI.AddClickEventListener(self.node_list["btn_main_lq"], BindTool.Bind1(self.OnClickMainGet, self))
    XUI.AddClickEventListener(self.node_list["btn_sub_lq"], BindTool.Bind1(self.OnClickSubGet, self))
    --XUI.AddClickEventListener(self.node_list["skill_item"], BindTool.Bind1(self.OnClickSkillTips, self))
    --XUI.AddClickEventListener(self.node_list["btn_rule_tips"], BindTool.Bind(self.OnClickTipsBtn, self))
    --XUI.AddClickEventListener(self.node_list["fz_skill_root"], BindTool.Bind(self.OnFZBtnSkillIcon, self))
end


function PremiumGiftView:ShowIndexCallBack()
    self:DoCellsAnim()
    ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.PremiumGiftView, ACTIVITY_TYPE.PREMIUM_GIFT)
end

function PremiumGiftView:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("primium_gift_down") then
        CountDownManager.Instance:RemoveCountDown("primium_gift_down")
    end

    if self.big_act_list then
		for k, v in pairs(self.big_act_list) do
            v:DeleteMe()
		end

		self.big_act_list = nil
	end

    -- if self.small_act_list then
    --     self.small_act_list:DeleteMe()
    --     self.small_act_list = nil
    -- end

    if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end

    if self.sub_display then
        self.sub_display:DeleteMe()
        self.sub_display = nil
    end

    if self.fz_display then
        self.fz_display:DeleteMe()
        self.fz_display = nil
    end

    -- if self.gift_reward_list then
    --     self.gift_reward_list:DeleteMe()
    --     self.gift_reward_list = nil
    -- end

    if CountDownManager.Instance:HasCountDown("primium_today_time") then
        CountDownManager.Instance:RemoveCountDown("primium_today_time")
    end

    self.cur_select_sub_index = nil
    self.skill_data = nil

    self.attr_title_list = {}
    self.attr_name_list = {}
    self.attr_value_list = {}
end

function PremiumGiftView:OpenCallBack()
    PremiumGiftWGCtrl.Instance:SendRechargeInfo(ACTIVITY_TYPE.PREMIUM_GIFT, PREMIUM_GIFT_OPERATE_TYPE.INFO)
end

function PremiumGiftView:OnFlush()
    self:FlushShowView()
    self:FlushModelShow()
    self:TodayTimeCountDown()
    self:FlushAttr()
end

function PremiumGiftView:OnClickTipsBtn()
    RuleTip.Instance:SetTitle(Language.PremiumGift.GiftRuleTitle)
    RuleTip.Instance:SetContent(Language.PremiumGift.GiftRuleDesc)
end

function PremiumGiftView:OnFZBtnSkillIcon()
    local open_day_cfg = PremiumGiftWGData.Instance:GetopenDayGradeCfg()
    local data = SupremeFieldsWGData.Instance:SkillShowCfgList(open_day_cfg.footlight_type, 1)
    CommonSkillShowCtrl.Instance:SetViewDataAndOpen(data)
end

function PremiumGiftView:InitTitleAttr()
    self.attr_title_list = {}
    self.attr_name_list = {}
    self.attr_value_list = {}

    local attr_num = self.node_list.attr_list.transform.childCount
    for i = 1, attr_num do
        self.attr_title_list[i] = self.node_list.attr_list:FindObj("attr_" .. i)
        self.attr_name_list[i] = self.attr_title_list[i]:FindObj("attr_name")
        self.attr_value_list[i] = self.attr_title_list[i]:FindObj("attr_value")
    end
end

function PremiumGiftView:FlushAttr()
    local model_item_id, img_type = PremiumGiftWGData.Instance:GetBigRewardCfg()
    local item_cfg = ItemWGData.Instance:GetItemConfig(model_item_id)
    if not item_cfg then
        return
    end

    local info = TitleWGData.Instance:GetConfig(item_cfg.param1)
    if not info then
        return
    end
    local cur_attr_list = TitleWGData.Instance:GetTitleAttrInfo(info)

    -- 属性显示
    local index = 1
    local is_per = true
    local sort_list = AttributeMgr.SortAttribute()

    for k, v in ipairs(sort_list) do
        local is_show = PremiumGiftWGData.Instance:CheckIsSelectTitleAttr(v)

        if is_show and cur_attr_list[v] ~= 0 then
            is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v)
            self.attr_name_list[index].text.text = EquipmentWGData.Instance:GetAttrNameByAttrStr(v, true, false)
            self.attr_value_list[index].text.text = is_per and cur_attr_list[v] / 100 .. "%" or cur_attr_list[v]

            self.attr_title_list[index]:SetActive(true)
            index = index + 1
        end
    end

    local attr_num = self.node_list.attr_list.transform.childCount
    for i = index, attr_num do
        self.attr_title_list[i]:SetActive(false)
    end
end

function PremiumGiftView:FlushModelShow()
    local model_item_id, img_type = PremiumGiftWGData.Instance:GetBigRewardCfg()
    local open_day_cfg = PremiumGiftWGData.Instance:GetopenDayGradeCfg()
    local no_sub_task = PremiumGiftWGData.Instance:NotHasSubTask()
    --self.node_list.skill_root:CustomSetActive(not no_sub_task)
    if model_item_id > 0 then
        local data = {}
        local type = open_day_cfg.type or 1
        data.item_id = model_item_id
        data.render_type = type
        --data.should_ani = type == 1
        data.rotation = Vector3(0, 0, 0)
        local position = Split(open_day_cfg.mount_pos, "|")
        data.position = Vector3.New(position[1], position[2], position[3])
        local scale_t = Split(open_day_cfg.mount_scale, "|")
        data.scale = Vector3.New(scale_t[1], scale_t[2], scale_t[3])

        data.model_click_func = function()
            TipWGCtrl.Instance:OpenItem({ item_id = model_item_id })
        end
        self.model_display:SetData(data)

        if not no_sub_task then
            local mode_data = { item_id = data.item_id }
            self.skill_data = PremiumGiftWGData.Instance:GetBigRewardSkillCfg(mode_data)
            local bundle, asset
            if self.skill_data.skill_icon then
                bundle, asset = ResPath.GetSkillIconById(self.skill_data.skill_icon)
            end

            -- self.node_list["img_skill_icon"].image:LoadSpriteAsync(bundle, asset, function()
            --     self.node_list["img_skill_icon"].image:SetNativeSize()
            -- end)

            --local bundle2, asset2 = ResPath.GetNoPackPNG("a1_jbfl_zs" .. img_type)
            -- self.node_list["title_bg"].image:LoadSprite(bundle2, asset2, function()
            --     self.node_list["title_bg"].image:SetNativeSize()
            -- end)
        end
    end

    local capability = 0
    if not IsEmptyTable(open_day_cfg) then
        for k, v in pairs(open_day_cfg.maintask_item_list) do
            local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)

            if item_cfg then
                capability = capability + ItemShowWGData.CalculateCapability(v.item_id)
            end
        end
    end

    self.node_list["model_cap_value"].text.text = capability
end

function PremiumGiftView:FlushShowView()
    local jump_index = PremiumGiftWGData.Instance:GetJumpRedTab()
    self:OnClickSwitch(jump_index)

    local big_data_list = PremiumGiftWGData.Instance:GetMainTaskList()

    if big_data_list then
        for i = 1, 10 do
            if i <= #big_data_list then
                self.node_list.big_content:FindObj("premium_gift_small_item" .. i):SetActive(true)
                self.big_act_list[i]:SetData(big_data_list[i])
                self.big_act_list[i]:SetIndex(i)
                -- self.big_act_list[i]:SetChainActive(i < #big_data_list)
            else
                self.node_list.big_content:FindObj("premium_gift_small_item" .. i):SetActive(false)
            end
        end
    end

    local main_task_red = PremiumGiftWGData.Instance:GetMainTaskRed()
    self.node_list["btn_main_yl"]:SetActive(PremiumGiftWGData.Instance:IsMainLongGet())
    self.node_list["btn_main_lq"]:SetActive(main_task_red > 0)
    self.node_list["common_capability"]:SetActive(main_task_red <= 0 and not PremiumGiftWGData.Instance:IsMainLongGet())
    self.node_list["toggle_red_main"]:SetActive(main_task_red > 0)

    local show_tab_list = PremiumGiftWGData.Instance:GetSubTaskActDayTabShow()
    if show_tab_list then
        for k, v in pairs(show_tab_list) do
            self.node_list["toggle_" .. k]:SetActive(v == 1)
        end
    end

    local day_list = PremiumGiftWGData.Instance:GetSubTaskActDayList()

    if day_list then
        for k, v in pairs(day_list) do
            local day = CommonDataManager.GetAncientNumber(v)
            local str = string.format(Language.RebateGiftAct.GiftDayTxt, day)
            if k == 1 then
                self.node_list["normal_text_" .. k].text.text = Language.RebateGiftAct.GiftToDayTitle
                self.node_list["hl_text_" .. k].text.text = Language.RebateGiftAct.GiftToDayTitle
            else
                self.node_list["normal_text_" .. k].text.text = str
                self.node_list["hl_text_" .. k].text.text = str
            end

            local cur_day = PremiumGiftWGData.Instance:GetSubTaskActTabDay(k)
            local sub_red = PremiumGiftWGData.Instance:GetSubTaskRed(cur_day)
            self.node_list["toggle_red_sub_" .. k]:SetActive(sub_red > 0)
        end
    end

    self:FlushFZView()
end

function PremiumGiftView:FlushFZView()
    local open_day_cfg = PremiumGiftWGData.Instance:GetopenDayGradeCfg()
    local footlight_type = (open_day_cfg or {}).footlight_type or -1
    local active = footlight_type >= 0

    self.node_list.fzdisplay:SetActive(active)
    --self.node_list.gift_reward_list:SetActive(active)
    --self.node_list.fz_skill_root:SetActive(active)

    if active then
        self.fz_display:RemoveAllModel()
        local bundle, asset = ResPath.GetSkillFaZhenModel(0)
        self.fz_display:SetMainAsset(bundle, asset)

        local display_reward = {}
        for i = 0, #open_day_cfg.maintask_item_list do
            table.insert(display_reward, open_day_cfg.maintask_item_list[i])
        end
        --self.gift_reward_list:SetDataList(display_reward)

        local fz_skill_id = SupremeFieldsWGData.Instance:GetSkillIDList(footlight_type, 1)[1] or 0
        local fz_skill_cfg = SupremeFieldsWGData.Instance:GetFootLightSkillCfg(fz_skill_id)
        --local icon_bundle, icon_asset = ResPath.GetSkillIconById(fz_skill_cfg.icon)
        -- self.node_list.fz_skill_icon.image:LoadSpriteAsync(icon_bundle, icon_asset, function()
        --     self.node_list.fz_skill_icon.image:SetNativeSize()
        -- end)

        --local cfg = SupremeFieldsWGData.Instance:GetFootLightCfg(footlight_type)
        --self.node_list.fz_skill_text.text.text = cfg.skill_txt
    end
end

function PremiumGiftView:FlushActList(index)
    local cur_day_data = PremiumGiftWGData.Instance:GetSubTaskActTabDay(index)

    if cur_day_data > 0 then
        -- local small_data_list = PremiumGiftWGData.Instance:GetSubTaskList(cur_day_data)

        -- if small_data_list then
        --     self.small_act_list:SetDataList(small_data_list)
        -- end

        local sub_model_item = PremiumGiftWGData.Instance:GetSubTaskRewardCfg(cur_day_data)
        if not IsEmptyTable(sub_model_item) then
            local item_id = (((sub_model_item[1] or {}).subtask_item_list or {})[0] or {}).item_id or 0

            if item_id > 0 then
                local data = {}
                data.item_id = item_id
                data.render_type = 1 --配置没有给类型  全是称号奖励
                data.position = Vector3(0, 0, 0)
                data.rotation = Vector3(0, 0, 0)
                data.scale = Vector3(2.2, 2.2, 2.2)
                data.model_click_func = function()
                    TipWGCtrl.Instance:OpenItem({ item_id = data.item_id })
                end

                self.sub_display:SetData(data)
                local capability = ItemShowWGData.CalculateCapability(data.item_id, true)
                self.node_list["sub_cap_value"].text.text = capability
            end
        end

        local sub_task_red = PremiumGiftWGData.Instance:GetSubTaskRed(cur_day_data)
        local sub_get_state = PremiumGiftWGData.Instance:GetSubTaskRewardState(cur_day_data)

        self.node_list["btn_sub_lq"]:SetActive(sub_task_red > 0)
        self.node_list["btn_sub_yl"]:SetActive(sub_get_state > 0)
    end
end

function PremiumGiftView:DoCellsAnim()
    self.node_list["big_act_list"]:SetActive(false)
    local tween_info = UITween_CONSTS.PremiumGiftView
    ReDelayCall(self, function()
        UITween.CleanAlphaShow(GuideModuleName.PremiumGiftView)
        self.node_list["big_act_list"]:SetActive(true)
       -- local list =  self.big_act_list:GetAllItems()
        local count = 0
        -- for k,v in ipairs(list) do
        --     if 0 ~= v.index then
        --         count = count + 1
        --     end
        --     v:PlayItemAnim(count)
        -- end
        for i = 1, 10 do
            if 0 ~= self.big_act_list[i].index then
                count = count + 1
            end
            self.big_act_list[i]:PlayItemAnim(count)
        end
    end, tween_info.DelayDoTime, "pg_big_act_list")
end

function PremiumGiftView:OnClickSwitch(index)
    if self.index == index then
        return
    end

    self.index = index
    self.node_list["toggle_" .. index].toggle.isOn = true
    self.node_list["main_task_show"]:SetActive(index == 0)
    self.node_list["sub_task_show"]:SetActive(index ~= 0)
    self.node_list.activity_time:SetActive(index == 0)
    self.node_list.totay_time:SetActive(index ~= 0)
    self.cur_select_sub_index = index
    self:FlushActList(index)
end

function PremiumGiftView:OnClickMainGet()
    if not PremiumGiftWGData.Instance:IsMainLongGet() then
        PremiumGiftWGCtrl.Instance:SendRechargeInfo(
            ACTIVITY_TYPE.PREMIUM_GIFT,
            PREMIUM_GIFT_OPERATE_TYPE.GET_MAIN_TASK_REWARD)
    end
end

function PremiumGiftView:OnClickSubGet()
    if self.cur_select_sub_index < 0 then
        return
    end

    local cur_day_data = PremiumGiftWGData.Instance:GetSubTaskActTabDay(self.cur_select_sub_index)
    PremiumGiftWGCtrl.Instance:SendRechargeInfo(
        ACTIVITY_TYPE.PREMIUM_GIFT,
        PREMIUM_GIFT_OPERATE_TYPE.GET_SUB_TASK_REWARD,
        cur_day_data)
end

function PremiumGiftView:OnClickSkillTips()
    local data = self.skill_data
    if data == nil then
        return
    end

    local skill_describe = data.skill_describe or data.skill_des
    local limit_text = ""
    if not data.is_open_skill then
        limit_text = string.format(Language.NewAppearance.SkillGradeActTips, data.active_grade)
    end

    local capability = NewAppearanceWGData.Instance:GetSingleSkillCap(data)
    local show_data = {
        icon = data.skill_icon,
        top_text = data.skill_name,
        body_text = skill_describe,
        limit_text = limit_text,
        x = 0,
        y = 0,
        set_pos2 = true,
        capability = capability,
    }

    NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

------------------------------------活动时间倒计时----------------------------------------
function PremiumGiftView:LoginTimeCountDown()
    local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.PREMIUM_GIFT)
    if not IsEmptyTable(activity_data) then
        local invalid_time = activity_data.end_time

        if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
            self.node_list["time_down"].text.text = TimeUtil.FormatSecondDHM8(invalid_time -
            TimeWGCtrl.Instance:GetServerTime())
            CountDownManager.Instance:AddCountDown("primium_gift_down", BindTool.Bind1(self.UpdateCountDown, self),
                BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
        end
    end
end

function PremiumGiftView:UpdateCountDown(elapse_time, total_time)
    local valid_time = total_time - elapse_time

    if valid_time > 0 then
        self.login_act_date = TimeUtil.FormatUnixTime2Date()

        if self.node_list["time_down"] then
            self.node_list["time_down"].text.text = TimeUtil.FormatSecondDHM8(valid_time)
        end
    end
end

function PremiumGiftView:OnComplete()
    if self.node_list["time_down"] then
        self.node_list["time_down"].text.text = ""
    end

    self:Close()
end

function PremiumGiftView:TodayTimeCountDown()
    local time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())

    if time > 0 then
        if CountDownManager.Instance:HasCountDown("primium_today_time") then
            CountDownManager.Instance:RemoveCountDown("primium_today_time")
        end

        CountDownManager.Instance:AddCountDown("primium_today_time",
            BindTool.Bind(self.UpdateTodayCountDown, self),
            BindTool.Bind(self.OnTodayComplete, self),
            nil, time, 1)
    else
        self:OnTodayComplete()
    end
end

function PremiumGiftView:UpdateTodayCountDown(elapse_time, total_time)
    local valid_time = total_time - elapse_time

    if valid_time > 0 then
        self.login_act_date = TimeUtil.FormatUnixTime2Date()

        if self.node_list["today_time_down"] then
            self.node_list["today_time_down"].text.text = TimeUtil.FormatSecondDHM8(valid_time)
        end
    end
end

function PremiumGiftView:OnTodayComplete()
    if self.node_list["today_time_down"] then
        self.node_list["today_time_down"].text.text = ""
    end
end

---------------------------------------主活动---------------------------------
PremiumGiftMainTaskItem = PremiumGiftMainTaskItem or BaseClass(BaseRender)

function PremiumGiftMainTaskItem:__delete()
    if self.main_item_reward then
        self.main_item_reward:DeleteMe()
        self.main_item_reward = nil
    end
end

function PremiumGiftMainTaskItem:LoadCallBack()
    if not self.main_item_reward then
        self.main_item_reward = ItemCell.New(self.node_list["item_pos"])
    end

    XUI.AddClickEventListener(self.node_list["btn_get"], BindTool.Bind1(self.OnClickJump, self))
end

function PremiumGiftMainTaskItem:OnFlush()
    if self.data == nil then
        return
    end

    local data_item_id = 0
    local item_name = ""
    if self.data.type == 6 then --称号
        local title_cfg = TitleWGData.GetTitleConfig(self.data.param1)
        data_item_id = title_cfg.item_id
        item_name = title_cfg and title_cfg.name or ""
    else --时装以及其他
        data_item_id = WardrobeWGData.Instance:GetActItemId(self.data)
        local item_cfg = ItemWGData.Instance:GetItemConfig(data_item_id)
        item_name = item_cfg and item_cfg.name or ""
    end

    self.main_item_reward:SetData({ item_id = data_item_id })
    if self.node_list["item_name"] then
        self.node_list["item_name"].text.text = item_name
    end
    local asset, bundle = ResPath.GetCommon("a2_ty_wpk_bk_" .. self.data.show_icon_ground)
    self.node_list["item_kuang"].image:LoadSprite(asset, bundle, function()
        self.node_list["item_kuang"].image:SetNativeSize()
    end)

    local is_get = self.data.is_get > 0
    self.node_list["btn_get"]:SetActive(not is_get)
    self.node_list["is_get"]:SetActive(is_get)

    -- local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.PREMIUM_GIFT)
    -- local cur_day = CommonDataManager.GetAncientNumber(self.data.seq + 1)
    -- local name_str = (self.data.seq + 1) == act_day and Language.RebateGiftAct.GiftToDayTitle or string.format(Language.RebateGiftAct.GiftDayTxt, cur_day)
    -- self.node_list["btn_name"].text.text = name_str

    self.node_list["btn_name"].text.text = self.data.show_open_decs
end

function PremiumGiftMainTaskItem:OnClickJump()
    if self.data == nil then
        return
    end

    if PremiumGiftWGData.Instance:NotHasSubTask() then
        if self.data.act_type ~= "" then
            local act_active = ActivityWGData.Instance:GetActivityIsOpen(self.data.act_type)
            if act_active then
                FunOpen.Instance:OpenViewNameByCfg(self.data.open_panel)
            else
                TipWGCtrl.Instance:ShowSystemMsg(Language.Common.ActivateNoOpen)
            end
        else
            FunOpen.Instance:OpenViewNameByCfg(self.data.open_panel)
        end
    else
        local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.PREMIUM_GIFT)

        if self.data.seq + 1 > act_day then
            TipWGCtrl.Instance:ShowSystemMsg(Language.RebateGiftAct.GiftTabError)
            return
        end

        local jump_index = PremiumGiftWGData.Instance:GetSubTaskDayByIndex(self.data.seq + 1)
        PremiumGiftWGCtrl.Instance:OnClickExtinctGiftTab(jump_index)
    end
end

------------------------------------------子活动--------------------------------------------------
PremiumGiftSubTaskItem = PremiumGiftSubTaskItem or BaseClass(BaseRender)

function PremiumGiftSubTaskItem:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function PremiumGiftSubTaskItem:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["btn_jump_bg"], BindTool.Bind1(self.OnClickSubJump, self))

    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["item_cell"])
    end
end

function PremiumGiftSubTaskItem:OnFlush()
    if self.data == nil then
        return
    end

    local data_item_id = self.data.show_icon
    local item_cfg = ItemWGData.Instance:GetItemConfig(data_item_id)
    local item_name = item_cfg and item_cfg.name or ""
    local is_get = self.data.is_get > 0

    self.item_cell:SetData({item_id = data_item_id})

    --self.sub_item_reward:SetData({ item_id = data_item_id })
    self.node_list["sub_lock_item_name"].text.text = item_name
    self.node_list["sub_active_item_name"].text.text = item_name
    self.node_list["sub_btn_name"].text.text = self.data.show_open_decs

    --self.node_list["btn_jump_bg"]:SetActive(not is_get)
    --self.node_list["sub_is_get"]:SetActive(is_get)

    self.node_list["lock_content"]:SetActive(not is_get) --未激活
    self.node_list["active_content"]:SetActive(is_get)       --已激活
    self.node_list["item_active_flag"]:SetActive(is_get)       --已激活
end

function PremiumGiftSubTaskItem:SetChainActive(bool)
    self.node_list["chain_img"]:SetActive(bool)
end

function PremiumGiftSubTaskItem:PlayItemAnim(item_index)
    if not self.node_list["root"] then return end

    local tween_info = UITween_CONSTS.PremiumGiftView.ListCellRender
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index

    self.node_list.root.canvas_group.alpha = 0
    ReDelayCall(self, function()
        UITween.AlphaShow(GuideModuleName.PremiumGiftView, self.node_list["root"], tween_info.FromAlpha, tween_info.ToAlpha, tween_info.AlphaTweenTime)
    end, tween_info.NextDoDelay * wait_index, "premium_gift_small_item" .. wait_index)
end

function PremiumGiftSubTaskItem:OnClickSubJump()
    if self.data == nil then
        return
    end

    --已激活不跳转.
    local is_get = self.data.is_get > 0
    if is_get then
        return
    end

    local open_panel = self.data.open_panel or ""
    local act_type = self.data.act_type or ""

    if open_panel ~= "" then
        if act_type == "" then
            FunOpen.Instance:OpenViewNameByCfg(open_panel)
        else
            local is_act_open = ActivityWGData.Instance:GetActivityIsOpen(act_type)

            if is_act_open then
                FunOpen.Instance:OpenViewNameByCfg(open_panel)
            else
                TipWGCtrl.Instance:ShowSystemMsg(Language.Common.ActivateNoOpen)
            end
        end
    end
end

----------------------------
PremiumGiftFlowerItem = PremiumGiftFlowerItem or BaseClass(BaseRender)
function PremiumGiftFlowerItem:__init()

end

function PremiumGiftFlowerItem:__delete()

end

function PremiumGiftFlowerItem:LoadCallBack()

end

function PremiumGiftFlowerItem:OnFlush()
    local is_get = self.data.is_get > 0
    self.node_list.normal_img:SetActive(not is_get)
    self.node_list.active_img:SetActive(is_get)
end