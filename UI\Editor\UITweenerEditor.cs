//-------------------------------------------------
//            NGUI: Next-Gen UI kit
// Copyright © 2011-2017 Tasharen Entertainment Inc
//-------------------------------------------------

using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(UGUITweener), true)]
public class UITweenerEditor : Editor
{
	public override void OnInspectorGUI ()
	{
		GUILayout.Space(6f);
		UGUITweenEditorTools.SetLabelWidth(110f);
		base.OnInspectorGUI();
		DrawCommonProperties();
	}

	protected void DrawCommonProperties ()
	{
		UGUITweener tw = target as UGUITweener;

		if (UGUITweenEditorTools.DrawHeader("Tweener"))
		{
			UGUITweenEditorTools.BeginContents();
			UGUITweenEditorTools.SetLabelWidth(110f);

			GUI.changed = false;

			TweenMethod method = (TweenMethod)EditorGUILayout.EnumPopup("Play Method", tw.method);
			UGUITweener.Style style = (UGUITweener.Style)EditorGUILayout.EnumPopup("Play Style", tw.style);
			AnimationCurve curve = EditorGUILayout.CurveField("Animation Curve", tw.animationCurve, GUILayout.Width(170f), GUILayout.Height(62f));


			GUILayout.BeginHorizontal();
			float dur = EditorGUILayout.FloatField("Duration", tw.duration, GUILayout.Width(170f));
			GUILayout.Label("seconds");
			GUILayout.EndHorizontal();

			GUILayout.BeginHorizontal();
			float del = EditorGUILayout.FloatField("Start Delay", tw.delay, GUILayout.Width(170f));
			GUILayout.Label("seconds");
			GUILayout.EndHorizontal();

			int tg = EditorGUILayout.IntField("Tween Group", tw.tweenGroup, GUILayout.Width(170f));
			bool ts = EditorGUILayout.Toggle("Ignore TimeScale", tw.ignoreTimeScale);
			bool fx = EditorGUILayout.Toggle("Use Fixed Update", tw.useFixedUpdate);
			bool enabled_play = EditorGUILayout.Toggle("enable play", tw.enabled_play);
			bool disable_finish = EditorGUILayout.Toggle("disable finished", tw.disable_finish);


			if (GUI.changed)
			{
				UGUITweenEditorTools.RegisterUndo("Tween Change", tw);
				tw.animationCurve = curve;
				tw.method = method;
				tw.style = style;
				tw.ignoreTimeScale = ts;
				tw.tweenGroup = tg;
				tw.duration = dur;
				tw.delay = del;
				tw.useFixedUpdate = fx;
				tw.enabled_play = enabled_play;
				tw.disable_finish = disable_finish;
				UGUITweenEditorTools.SetDirty(tw);
			}
			UGUITweenEditorTools.EndContents();
		}

		UGUITweenEditorTools.SetLabelWidth(80f);
		UGUITweenEditorTools.DrawEvents("On Finished", tw, tw.onFinished);
	}
}
