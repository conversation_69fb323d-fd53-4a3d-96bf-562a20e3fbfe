ContinuousXiaoFeiWGData = ContinuousXiaoFeiWGData or BaseClass()
function ContinuousXiaoFeiWGData:__init()
	if ContinuousXiaoFeiWGData.Instance then
		error("[ContinuousXiaoFeiWGData] Attempt to create singleton twice!")
		return
	end
	ContinuousXiaoFeiWGData.Instance = self

	self.consumescore_total_score = 0
	self.score_record_list = {}

	self.today_chongzhi = 0
	self.can_fetch_reward_flag = 0
	self.has_fetch_reward_flag = 0
	self.continue_chongzhi_days = 0
	self.is_activity_over = 0
end

function ContinuousXiaoFeiWGData:__delete()
	
	ContinuousXiaoFeiWGData.Instance = nil
end

function ContinuousXiaoFeiWGData:SetContinueXiaoFeiInfo(protocol)
	self.total_consume = protocol.total_consume
	self.reward_flag = protocol.reward_flag
end

function ContinuousXiaoFeiWGData:GetCurIndexIsFetched(index)
	local flag = bit:d2b(self.reward_flag)
	return flag[32 - index] == 1
end

function ContinuousXiaoFeiWGData:GetTotalConsume()
	return self.total_consume
end

function ContinuousXiaoFeiWGData:GetFetchRewardFlag()
	return self.reward_flag
end

function ContinuousXiaoFeiWGData:GetRemind()
	if not self.total_consume or not self.reward_flag then return false end
	local data_list = self:GetRewardData()
	local flag = bit:d2b(self.reward_flag or 0)
	for i = 0, 15 do
		if flag[32 - i] ~= 1 then
			if not data_list[i + 1] then
				return false
			end
			if self.total_consume >= data_list[i + 1].total_consume then
				return true
			end
		end
	end
	return false
end

--function ContinuousXiaoFeiWGData:GetContinueChongzhiFetchDays()
--	local act_cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
--	if nil == act_cfg or nil == act_cfg.other then return end
--	local rand_t = act_cfg.other
--	local other_cfg = ServerActivityWGData.Instance:GetRandActivityConfig(rand_t, ACTIVITY_TYPE.RAND_CONTINUE_CHONGZHI)
--	return other_cfg[1].continue_chongzhi_fetch_extra_reward_need_days or 0
--end

---- 额外奖励
--function ContinuousXiaoFeiWGData:GetExtraReward()
--	local act_cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
--	if nil == act_cfg or nil == act_cfg.continue_chonghzi then return end
--	local rand_t = act_cfg.continue_chonghzi
--	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_CONTINUE_CHONGZHI)
--	local openday_time = TimeWGCtrl.Instance:GetServerRealStartTime()
--	local day  = math.ceil((activity_status.start_time  - openday_time)/ (3600*24))
--	local continuous_recharge_cfg = ServerActivityWGData.Instance:GetRandHappyActivity(rand_t, day)
--	return continuous_recharge_cfg[1].extra_reward_item or {}
--end

function ContinuousXiaoFeiWGData:GetIsCanGetExtraReward()
	local is_can_fetch = bit:_and(1, self.reward_flag)
	if 1 == is_can_fetch then
		return true
	end

	return false
end

--function ContinuousXiaoFeiWGData:GetIsHasGetExtraReward()
--	local is_has_fetch = bit:_and(1, self.has_fetch_reward_flag)
--	if 1 == is_has_fetch then
--		return true
--	end
--
--	return false
--end
--
function ContinuousXiaoFeiWGData:GetRewardData()
	local cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	if nil == cfg or nil == cfg.consume_reward then
		return {}
	end
	local data_list = {}

	local rand_t = cfg.consume_reward
	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.TotalConsume)
	local openday_time = TimeWGCtrl.Instance:GetServerRealStartTime()
	if activity_status == nil then
		return {}
	end

	local world_level = RankWGData.Instance:GetWordLevel()
	local record_level = 0
	for i, v in pairs(rand_t) do
		record_level = v.world_level
		if v.world_level > world_level then
			break
		end
	end

	local index = 1
	for k, v in pairs(rand_t) do
		if v.world_level == record_level then
			local cell_datalist = {}
			for i = 1, 4 do
				local data = {}
			    local item_data = v.consume_reward[i - 1]
				data["item_id"] = item_data and item_data["item_id"] or 0
				data["num"] = item_data and item_data["num"] or 0
				data["is_bind_"] = item_data and item_data["is_bind"] or 0
				cell_datalist[i] = data
			end

			data_list[index] = cell_datalist
			data_list[index].can_fetch_reward_flag = self.reward_flag
			data_list[index].cur_consume = self.total_consume
			data_list[index].total_consume = v.consume_target
			local is_fetch = self:GetCurIndexIsFetched(index - 1)
			data_list[index].is_fetch = is_fetch
			data_list[index].seq = index - 1
			-- 可领取 > 去消费 > 已领取
			local is_can = self.total_consume >= v.consume_target
			if is_can and not is_fetch then --可领取
				data_list[index].can_fetch_sort_flag =  1000
			elseif not is_fetch and not is_can then --去消费
				data_list[index].can_fetch_sort_flag =  10000
			elseif is_fetch then --已领取
				data_list[index].can_fetch_sort_flag =  100000
			end
			index = index + 1
		end
	end

	--table.sort(data_list, function (a,b)
	--	return a.can_fetch_sort_flag > b.can_fetch_sort_flag
	--end)

	table.sort(data_list, SortTools.KeyLowerSorters("can_fetch_sort_flag", "total_consume"))

	return data_list
end