WuHunZhiLiView = WuHunZhiLiView or BaseClass(SafeBaseView)

function WuHunZhiLiView:__init()
	self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
    self.view_name = "WuHunZhiLiView"
	local bundle_name = "uis/view/wuhunzhenshen_prefab"
	local common_bundle_name = "uis/view/common_panel_prefab"
	self:AddViewResource(0, common_bundle_name, "layout_a3_common_panel")
    self:AddViewResource(0, bundle_name, "layout_tianshen_wuhunzhili")
    self:AddViewResource(0, common_bundle_name, "layout_a3_common_top_panel")


	self.wuhun_li_list_view = nil
end

function WuHunZhiLiView:__delete()

end

function WuHunZhiLiView:OpenCallBack()

end

function WuHunZhiLiView:CloseCallBack()

end

function WuHunZhiLiView:LoadCallBack()
	if not self.wuhun_li_list_view then
        self.wuhun_li_list_view = AsyncListView.New(TianShenWuHunLiShowRender, self.node_list["list_view"])
    end

	local title = Language.WuHunZhenShen.WuHunZhiLi
	self.node_list.title_view_name.text.text = title

	local bundle, assert = ResPath.GetF2RawImagesPNG("a3_ftxd_bjt")
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, assert, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)
end

function WuHunZhiLiView:ReleaseCallBack()
    if self.wuhun_li_list_view then
        self.wuhun_li_list_view:DeleteMe()
        self.wuhun_li_list_view = nil
    end
end

function WuHunZhiLiView:OnFlush(param_t, index)
	local wuhun_list = WuHunWGData.Instance:GetAllWuhunZhiLilist()
	if wuhun_list then
		self.wuhun_li_list_view:SetDataList(wuhun_list)
	end
end

--=================================================================================
TianShenWuHunLiShowRender = TianShenWuHunLiShowRender or BaseClass(BaseRender)
function TianShenWuHunLiShowRender:OnFlush()
	if self.data then

		local wuhun_li_cfg = WuHunWGData.Instance:GetWuHunLiCfg(self.data.wh_type)
		self.node_list.wuhun_li_value.text.text = self.data.wuhun_li

		if not wuhun_li_cfg then
			return
		end

		local bg_bundle, bg_asset = ResPath.GetRawImagesPNG(wuhun_li_cfg.wh_bg)
		self.node_list["bg"].raw_image:LoadSprite(bg_bundle, bg_asset, function()
			self.node_list["bg"].raw_image:SetNativeSize()
		end)

		self.node_list.wuhun_li_name.text.text = wuhun_li_cfg.wh_name
		self.node_list.wuhun_li_desc.text.text = wuhun_li_cfg.wh_txt
	end
end
