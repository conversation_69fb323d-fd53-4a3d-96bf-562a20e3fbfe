OperationActMecha = OperationActMecha or BaseClass(OperationActRenderBase)

--[[配置数据：
    ----[[机甲模型
        render_type = OARenderType.Mecha,
        model_rt_type = ModelRTSCaleType.S,
        mecha_seq = 0, 机甲唯一seq
        mecha_part_list = {}  机甲部位list
        
        model_click_func
        hide_model_block
        -- 额外调整模型根节点的transform
        model_root_local_position (table、Vector3)
        model_root_local_rotation (table、Quaternion、Vector3)
        model_root_local_scale (number)
    ----]]
--]]

function OperationActMecha:__delete()
    if nil ~= self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end
end

function OperationActMecha:ReleaseCallBack()
end

function OperationActMecha:Reset()
    self.node_list["display_root"]:SetActive(false)
    if self.model_display then
        self.model_display:RemoveAllModel()
    end
end

function OperationActMecha:Show()
    if not self.data then
        return
    end

    self.node_list["display_root"]:SetActive(true)
    if nil == self.model_display then
		self.model_display = RoleModel.New()
		local model_type = self.model_type or MODEL_CAMERA_TYPE.BASE
        local display_data = {
            parent_node = self.node_list["display_root"],
            camera_type = model_type,
            offset_type = self.offset_type,
            rt_scale_type = self.data.model_rt_type or ModelRTSCaleType.S,
            can_drag = true,
        }
        
        self.model_display:SetRenderTexUI3DModel(display_data)
        -- local event_trigger_listener_node = self.data.event_trigger_listener_node or self.node_list["display_root"]
        -- self.model_display:SetUI3DModel(self.node_list["display_root"].transform, event_trigger_listener_node.event_trigger_listener, 1, true, model_type, self.offset_type)
        self.default_display_y = self.node_list["display_root"].rect.anchoredPosition.y
        self.model_block = self.node_list["display_root"]:GetComponent("UIBlock")
    end

    if self.data.model_click_func then
        self.node_list["display_root"].event_trigger_listener:AddPointerClickListener(BindTool.Bind(self.OnClickModel, self))
    end

    if self.model_block then
        self.model_block.enabled = not self.data.hide_model_block
    end

    self:TryFlushMechaModelInfo()
end


function OperationActMecha:TryFlushMechaModelInfo()
    local mecha_seq = self.data.mecha_seq

    if mecha_seq and mecha_seq >= 0 then
        local part_list = {}
		local base_part = MechaWGData.Instance:GetMechaBasePartListByMechaSeq(mecha_seq)
		for k, v in pairs(base_part) do
			local part_cfg = MechaWGData.Instance:GetPartCfgBySeq(v)
			part_list[part_cfg.part] = part_cfg.res_id
		end

        local target_part_list = self.data.mecha_part_list
        if not IsEmptyTable(target_part_list) then
            for k, v in pairs(target_part_list) do
                local part_cfg = MechaWGData.Instance:GetPartCfgBySeq(v)
                part_list[part_cfg.part] = part_cfg.res_id
            end
        end

        local part_info = {
            gundam_seq = mecha_seq,
            gundam_body_res = part_list[MECHA_PART_TYPE.BODY] or 0,
            gundam_weapon_res = part_list[MECHA_PART_TYPE.WEAPON] or 0,
            gundam_left_arm_res = part_list[MECHA_PART_TYPE.LEFT_HAND] or 0,
            gundam_right_arm_res = part_list[MECHA_PART_TYPE.RIGHT_HAND] or 0,
            gundam_left_leg_res = part_list[MECHA_PART_TYPE.LEFT_FOOT] or 0,
            gundam_right_leg_res = part_list[MECHA_PART_TYPE.RIGHT_FOOT] or 0,
            gundam_left_wing_res = part_list[MECHA_PART_TYPE.LEFT_WING] or 0,
            gundam_right_wing_res = part_list[MECHA_PART_TYPE.RIGHT_WING] or 0,
        }

        self.model_display:SetGundamModel(part_info)
    end
end