require("game/serveractivity/must_buy/must_buy_act_btn")
require("game/serveractivity/must_buy/must_buy_tip")
require("game/serveractivity/must_buy/must_buy_view")
require("game/serveractivity/must_buy/must_buy_wg_data")


MustBuyWGCtrl = MustBuyWGCtrl or BaseClass(BaseWGCtrl)
function MustBuyWGCtrl:__init()
	if MustBuyWGCtrl.Instance then
		ErrorLog("[MustBuyWGCtrl] attempt to create singleton twice!")
		return
	end
	MustBuyWGCtrl.Instance = self
	self.view = MustBuyView.New(GuideModuleName.MustBuy)
	self.tip_view = MustBuyTip.New(GuideModuleName.MustBuyTip)
	self.data = MustBuyWGData.New()
	self:RegisterAllProtocols()
	self:BindGlobalEvent(MainUIEventType.CHANGE_MAINUI_BUTTON, BindTool.Bind(self.MainUIButtonCreate, self))
	self:BindGlobalEvent(OtherEventType.VIEW_CLOSE, BindTool.Bind(self.CheckOtherView, self))
	self.role_attr_change = BindTool.Bind1(self.OnRoleAttrValueChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_attr_change, {"level"})
end

function MustBuyWGCtrl:__delete()
	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.tip_view then
		self.tip_view:DeleteMe()
		self.tip_view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.gold_cost_alert then
		self.gold_cost_alert:DeleteMe()
		self.gold_cost_alert = nil
	end

	RoleWGData.Instance:UnNotifyAttrChange(self.role_attr_change)
	self.role_attr_change = nil
	MustBuyWGCtrl.Instance = nil
end

function MustBuyWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCRAWorthBuyInfo, "OnSCRAWorthBuyInfo")
end

function MustBuyWGCtrl:OnSCRAWorthBuyInfo(protocol)
	-- print_error("OnSCRAWorthBuyInfo", protocol.item_count, protocol)
	local act_cfg = DailyWGData.Instance:GetActivityConfig(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_MUST_BUY)
	if act_cfg and act_cfg.level > RoleWGData.Instance:GetRoleLevel() then
		return
	end
	protocol = self.data:CheckProtocolConfig(protocol)
	self.data:SetActInfo(protocol)
	self:ChangeMustBuyButton(protocol.item_count > 0)

	if protocol.popup_seq >= 0 then
		if not self.view:IsOpen() then
			local data = self.data:GetItemCfgBySeq(protocol.popup_seq)
			self.tip_view:SetData(data)
			self.open_tip = true
			self:CheckMutexView()
		end
	end

	self:ShowButtonNew()
	self:ShowButtonNextTime()
	ViewManager.Instance:FlushView(GuideModuleName.MustBuy)
	RemindManager.Instance:Fire(RemindName.MustBuyRemind)
end

function MustBuyWGCtrl:CheckMutexView()
	if not self.open_tip then
		return
	end
	local mutex_view = self.data:GetMutexView()
	for k,v in pairs(mutex_view) do
		if ViewManager.Instance:IsOpen(GuideModuleName[v]) then
			return
		end
	end
	if OfflineRestWGCtrl.Instance:IsViewOpen() then
		return
	end
	if FunctionGuide.Instance:IsSomeViewOpen() then
		return
	end
	if TipWGCtrl.Instance:IsShowSomeTip() then
		return
	end
	if VipTyWGCtrl.Instance:IsShouCiTipOpen() then
		return
	end
	if CgManager.Instance:IsCgIng() then
		return
	end
	if Scene.Instance:IsSceneLoading() then
		return
	end
	local main_role = Scene.Instance:GetMainRole()
	if main_role:IsQingGong() then
		return
	end

	if main_role:IsMitsurugi() then
		return
	end

	if IS_AUDIT_VERSION then
		return
	end

	self.open_tip = false
	ViewManager.Instance:Open(GuideModuleName.MustBuyTip)
end

function MustBuyWGCtrl:SendQueryInfo()
	local t = {}
	t.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_MUST_BUY
	t.opera_type = RA_WORTH_BUY_OPERA_TYPE.RA_QUERY_INFO
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(t)
end

function MustBuyWGCtrl:SendBuyItem(seq)
	local t = {}
	t.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_MUST_BUY
	t.opera_type = RA_WORTH_BUY_OPERA_TYPE.BUY
	t.param_1 = seq
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(t)
end

function MustBuyWGCtrl:SendHadCheck()
	local t = {}
	t.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_MUST_BUY
	t.opera_type = RA_WORTH_BUY_OPERA_TYPE.HAD_CHECK
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(t)
end

function MustBuyWGCtrl:SendOffSale()
	local t = {}
	t.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_MUST_BUY
	t.opera_type = RA_WORTH_BUY_OPERA_TYPE.OFF_SALE
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(t)
end

function MustBuyWGCtrl:MainUIButtonCreate(act_type, status)
	if act_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_MUST_BUY and status then
		self:ChangeMustBuyButton()
		self.button_create = true
		if self.cache_list and #self.cache_list > 0 then
			for i,v in ipairs(self.cache_list) do
				v()
			end
		end

		self.cache_list = {}
	end
end

function MustBuyWGCtrl:ChangeMustBuyButton(status)
	self.btn_status = (status == nil) and self.btn_status or status
	GlobalEventSystem:Fire(MUSTBUY_EVENT.BUTTON_CHANGE, MUSTBUY_ACT_BTN.CHANGE_STATUS, self.btn_status)
end

function MustBuyWGCtrl:ShowButtonNew()
	local callback = function()
		GlobalEventSystem:Fire(MUSTBUY_EVENT.BUTTON_CHANGE, MUSTBUY_ACT_BTN.SHOW_NEW, self.data:GetNewFlag())
	end
	if self.button_create then
		callback()
	else
		self:EventCache(callback)
	end
end

function MustBuyWGCtrl:ShowButtonNextTime()
	local time = self.data:ShowButtonNextTime()
	local callback = function()
		GlobalEventSystem:Fire(MUSTBUY_EVENT.BUTTON_CHANGE, MUSTBUY_ACT_BTN.SHOW_COUNT, time)
	end
	if self.button_create then
		callback()
	else
		self:EventCache(callback)
	end
end

function MustBuyWGCtrl:EventCache(callback)
	if not self.cache_list then
		self.cache_list = {}
	end
	table.insert(self.cache_list, callback)
end

function MustBuyWGCtrl:CheckOtherView(view)
	self:CheckMutexView()
end

function MustBuyWGCtrl:OnRoleAttrValueChange(key, new_value, old_value)
	if key == "level" then
		local act_cfg = DailyWGData.Instance:GetActivityConfig(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_MUST_BUY)
		if act_cfg == nil then
			return
		end
		if new_value >= act_cfg.level then
			self:SendQueryInfo()
			RoleWGData.Instance:UnNotifyAttrChange(self.role_attr_change)
		end
	end
end

function MustBuyWGCtrl:OpenAlert(num, callback)
	if not self.gold_cost_alert then
		self.gold_cost_alert = Alert.New()
	end
	self.gold_cost_alert:SetOkFunc(callback)
	self.gold_cost_alert:SetLableString(string.format(Language.MustBuy.NoEnoughGold, num))
	self.gold_cost_alert:Open()
end

function MustBuyWGCtrl:SetScrollInteract()
  	self.view:SetScrollInteract()
end
