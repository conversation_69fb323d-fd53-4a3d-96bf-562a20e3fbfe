EquipTargetWGData = EquipTargetWGData or BaseClass()
-- 【职业改动修改】
local equip_target_type = {
    [0] = {
        [1] = 4,
        [2] = 8,
        [3] = 5,
        [4] = 7,
    },

    [1] = {
        [1] = 1,
        [2] = 2,
        [3] = 3,
        [4] = 6,
    }
}

TargetTypeToName = {
    [1] = "equip_1_1_id",
    [2] = "equip_1_2_id",
    [3] = "equip_1_3_id",
    [4] = "equip_0_1_id",
    [5] = "equip_0_3_id",
    [6] = "equip_1_4_id",
    [7] = "equip_0_4_id",
    [8] = "equip_0_2_id",
}


EquipTargetWGData.SuitPartNecklace = 4 --仙链索引
EquipTargetWGData.SuitPartDrop = 7  --仙坠索引

local VIP_BOSS_SCENE_ID_0 = 1360
local VIP_BOSS_SCENE_ID_1 = 1361

function EquipTargetWGData:__init()
    if EquipTargetWGData.Instance then
        error("[EquipTargetWGData] Attempt to create singleton twice!")
        return
    end

    EquipTargetWGData.Instance = self
    self:InitConfig()
    self.suit_index = 0
    self.m_all_param = {}
    self.eqsuit_index = 0
    self.old_equip_list = {}
    self.new_equip_list = {}
    self.old_equip_state = {}
    self.old_equip_star = {}
    self.is_charge = false
    self.charge_item_id = 0
    self.trait_index = 0
    self.system_open_flag = 0
    self.active_equip_data = {}
    RemindManager.Instance:Register(RemindName.EquipTarget, BindTool.Bind(self.GetEquipTargetAllRemind, self))

    self:InitEIData()
end

function EquipTargetWGData:__delete()
    self.suit_index = 0
    self.old_equip_state = {}
    self.old_equip_list = {}
    self.new_equip_list = {}
    self.old_equip_star = {}
    self.m_all_param = {}
    self.active_equip_data = {}
    self.eqsuit_index = 0
    self.is_charge = false
    self.charge_item_id = 0
    self.trait_index = 0
    self.system_open_flag = nil
    RemindManager.Instance:UnRegister(RemindName.EquipTarget)
    self:DeleteEIData()

    EquipTargetWGData.Instance = nil
end

function EquipTargetWGData:InitConfig()
    self.equip_auto = ConfigManager.Instance:GetAutoConfig("equiptarget_auto")
    self.equip_trait_index = ListToMap(self.equip_auto.thunder_att, "trait_index")
    self.equip_suit_active = self.equip_auto.equip_suit_active
    self.suit_skill = ListToMap(self.equip_auto.suit_skill, "suit_skill_id")
    self.suit_id_cfg = ListToMap(self.equip_auto.suit_basic_info, "suit_index")
    self.suit_basic_info = self.equip_auto.suit_basic_info
    self.suit_attr_info = ListToMap(self.equip_auto.ativate_att, "suit_index", "equip_num")
    self.equip_getaway = ListToMap(self.equip_auto.acquisition_path, "equip_id", "get_type")
    self.compose_desc_cfg = ListToMap(self.equip_auto.compose_desc, "index")
    self.equip_cfg_list = ItemWGData.Instance:GetEquipmentCfg()

    self.equip_list = {}
    self.msword_equip = {}
    self.mmusic_equip = {}
    self.wsword_equip = {}
    self.wmusic_equip = {}
    local equip_star = {}
    self.frist_weapon = {}
    self.need_equip_list = {}
    self.equip_star_list = {}
    self.frist_weapon_list = {}

    for k,v in pairs(self.suit_basic_info) do
        table.insert(equip_star, v.equip_star)
        if v.frist_charge_weapon ~= 0 then
            table.insert(self.frist_weapon, v.frist_charge_weapon)
        end
    end

    for i = 1, GameEnum.MAX_EQUIP_TARGET_SUIT_NUM do
        self.equip_star_list[i] = Split(equip_star[i], "|")
    end

    -- 【职业改动修改】
    for sex = GameEnum.FEMALE, GameEnum.MALE do
        self.need_equip_list[sex] = {}
		for prof = GameEnum.ROLE_PROF_1, GameEnum.ROLE_PROF_4 do
			local type = equip_target_type[sex] and equip_target_type[sex][prof]
            local key = TargetTypeToName[type]
            self.need_equip_list[sex][prof] = {}
			if key then
				for k,v in pairs(self.suit_basic_info) do
                    self.need_equip_list[sex][prof][v.suit_index] = Split(v[key], "|")
                end
			end
		end
	end

    self.frist_weapon_list = Split(self.frist_weapon[1], "|")

end

function EquipTargetWGData:GetRoleTargetType()
    local sex = RoleWGData.Instance:GetRoleSex()
    local prof = RoleWGData.Instance:GetRoleProf()
    return equip_target_type[sex] and equip_target_type[sex][prof] or 1
end

function EquipTargetWGData:GetEquipinfo(suit_index)
    if self.suit_id_cfg and self.suit_id_cfg[suit_index] then
        return self.suit_id_cfg[suit_index]
    end
end

-- 获取套装有效的装备数量
function EquipTargetWGData:GetEquipListCount(suit_index)
    local list = self:GetEquipList(suit_index)
    local count = 0
    for k, v in pairs(list) do
        if tonumber(v) > 0 then
            count = count + 1
        end
    end
    return count
end

--根据套装ID取到装备数据
function EquipTargetWGData:GetEquipList(suit_index)
    if IsEmptyTable(self.equip_list[suit_index]) then
        local sex = RoleWGData.Instance:GetRoleSex()
        local prof = RoleWGData.Instance:GetRoleProf()
        self.equip_list[suit_index] = ((self.need_equip_list[sex] or {})[prof] or {})[suit_index]
    end

    if self.is_charge then
        self.equip_list[suit_index][6] = self.charge_item_id
        self.is_charge = false
    end

    local equip_list = (self.m_all_param[suit_index + 1] or {}).equip_list or {}

    if not IsEmptyTable(equip_list) then
        for k, v in pairs(equip_list) do
            if v > 0 then
                self.equip_list[suit_index][k] = v
            end 
        end
    end

    return self.equip_list[suit_index]
end

--获取途径
function EquipTargetWGData:GetEquipGetawayById(item_id)
    if self.equip_getaway[item_id] then
        return self.equip_getaway[item_id]
    end
end

--根据套装ID取到奖励数据
function EquipTargetWGData:GetEquipReward(suit_index)
    if self.suit_reward[suit_index] then
        return self.suit_reward[suit_index]
    end
    return {}
end

--检测是否获得配置中的装备
function EquipTargetWGData:CheckEquipChange(suit_index, id)
    self.equip_solt = 0
    if self.new_equip_list[suit_index] ~= nil then
        for k, v in pairs(self.new_equip_list[suit_index]) do --k 部位
            if v == id then
                self.suit_index = suit_index - 1
                self.equip_solt = k
                self.equip_id = v
                return true, self.equip_id
            end
        end
    end

    return false, 0
end

function EquipTargetWGData:SetSystemOpenFlag(system_open_flag)
    self.system_open_flag = system_open_flag
end

function EquipTargetWGData:EqTargetIsShow1()
    return self.system_open_flag == 1
end

--判断主界面按钮显示状态  
function EquipTargetWGData:EqTargetIsShow()
    local all_suit_state_num = 0
    local is_mshow = false
    local is_show = false

    if not IsEmptyTable(self.m_all_param) then
        for i = 1, GameEnum.MAX_EQUIP_TARGET_SUIT_NUM do
            if self.m_all_param[i].suit_active_reward > 0 then
                all_suit_state_num = all_suit_state_num + 1
            end
        end
        is_mshow = all_suit_state_num < GameEnum.MAX_EQUIP_TARGET_SUIT_NUM  --特殊处理一下
        is_show = all_suit_state_num >= GameEnum.MAX_EQUIP_TARGET_SUIT_NUM
    end

    return is_mshow, is_show
end 

--主界面显示进度
function EquipTargetWGData:GetEqTargetSlider()
    local suit_index = 0
    local suit_name = ""
    local display_type = 0
    local eq_num = 0
    local role_lv = RoleWGData.Instance:GetRoleLevel()
    if not IsEmptyTable(self.m_all_param) then
        for k, v in pairs(self.m_all_param) do
            if v.suit_active_reward == 0 then
                if self.suit_id_cfg[k - 1] and self.suit_id_cfg[k - 1].open_level <= role_lv then
                    suit_index = k - 1
                end
                break
            else  
                suit_index = k - 1
            end
        end
    end
    display_type = self:GetEquipinfo(suit_index).display_type
    suit_name = self:GetEquipinfo(suit_index).suit_name
    eq_num = self:GetEquipStateNumByIndex(suit_index)
    return eq_num, suit_name, display_type, suit_index
end

--主界面根据获得物品取得套装数据
function EquipTargetWGData:GetEquipListById()
    return self:GetEquipList(self.suit_index), self.equip_solt, self.suit_index
end 

--主界面套装索引取得套装数据
function EquipTargetWGData:GetEquipListByIndex(suit_index)
    return self:GetEquipList(suit_index), self.equip_solt, suit_index
end 

--标签页
function EquipTargetWGData:GetEquipBigType()
    local data_list = {}
    local role_lv = RoleWGData.Instance:GetRoleLevel()
    for k, v in pairs(self.suit_basic_info) do
        if role_lv >= v.open_level then
             table.insert(data_list, v)
        end
    end

    return data_list
end

function EquipTargetWGData:GetEquipStateNumBySuitIndex(suit_index)
    local list = EquipTargetWGData.Instance:GetEquipList(suit_index)
    local vaule_sum = 0
    local max_count = 0
    local not_active_count = 0
    for k_2, v_2 in ipairs(list) do
        if v_2 and tonumber(v_2) > 0 then
            local state = EquipTargetWGData.Instance:GetEquipSortStateByIndex(suit_index, k_2, true)
            if state then
                not_active_count = not_active_count + 1
            end
            vaule_sum = vaule_sum + 1
        end
    end

    if not_active_count > 0 then
        max_count = vaule_sum
        return max_count-not_active_count, max_count
    end

    if max_count == 0 then
        max_count = #list
    end
    return max_count-not_active_count, max_count
end

-- 套装是否全部激活

function EquipTargetWGData:IsAllActive(suit_index)
    local cur_cfg = self.suit_attr_info[suit_index]
    if not cur_cfg then
        return true
    end
    local gaer_list = self:GetEquipJinDu(suit_index)
    local index = 0
    for k, v in pairs(cur_cfg) do
        local is_open = gaer_list[index]
        if is_open == 0 then
            return false
        end
        index = index + 1
    end
    return true
end

--套装属性展示
function EquipTargetWGData:GetEquipmenSuitAttr(suit_index)
    local cur_cfg = self.suit_attr_info[suit_index]
    local gaer_list = self:GetEquipJinDu(suit_index)
    if not cur_cfg then
        return {}
    end

    local data_list = {}
    for k, v in pairs(cur_cfg) do
        local seq = #data_list + 1
        data_list[seq] = {}
        data_list[seq].equip_num = v.equip_num
        data_list[seq].is_open = gaer_list[seq - 1]
        data_list[seq].attr_list = {}
        local tem_index = 1
        local attr_list = AttributeMgr.GetAttributteValueByClass(v)
        for name, value in pairs(attr_list) do
            data_list[seq].attr_list[tem_index] = {attr_type = name, value = value, sort_index = AttributeMgr.GetSortAttributeIndex(name)}
            tem_index = tem_index + 1
        end
        if not IsEmptyTable(data_list[seq].attr_list) then
            table.sort(data_list[seq].attr_list, SortTools.KeyLowerSorter("sort_index"))
        end
    end

    if not IsEmptyTable(data_list) then
        table.sort(data_list, SortTools.KeyLowerSorter("equip_num"))
    end

    return data_list
end

--技能属性转换战力
function EquipTargetWGData:GetSkillAttrCap(skill_index)
    local cap = 0
    local data = self.suit_skill[skill_index]
    if IsEmptyTable(data) then
        return cap
    end
    cap = data.capability_inc
   --[[ local attribute = AttributeMgr.GetAttributteByClass(data)
    cap = AttributeMgr.GetCapability(attribute)--]]
    return cap
end

--套装属性转换战力
function EquipTargetWGData:GetEquipAttrCap(suit_index)
    local cap = 0
    local data = self.suit_attr_info[suit_index]

    if IsEmptyTable(data) then
        return cap
    end

    for i = 1, GameEnum.EQUIP_TARGET_GAER do
        local attribute = AttributeMgr.GetAttributteByClass(data[i*2])
        cap = cap + AttributeMgr.GetCapability(attribute)
    end

    return cap
end

function EquipTargetWGData:GetSkillDes(skill_id)
    local des = ""
    local dark_des = ""
    local skill_icon = 0
   
    if self.trait_index <= 1 and self.suit_skill[skill_id] then
        skill_icon = self.suit_skill[skill_id].skill_pic
        des = self.suit_skill[skill_id].des_skill
        dark_des = self.suit_skill[skill_id].des_skill_light
    elseif self.equip_trait_index[self.trait_index] then
        skill_icon = self.equip_trait_index[self.trait_index].icon_show
        des = self.equip_trait_index[self.trait_index].des1
        dark_des = self.equip_trait_index[self.trait_index].des2
    end
   
    return des, skill_icon,dark_des
end

--技能说明
function EquipTargetWGData:GetSuitSkillDes(skill_id, display_type, suit_index)
    local des = ""
    local skill_icon = 0
    local name = ""
    if not IsEmptyTable(self.suit_id_cfg[suit_index]) then
        if display_type == TARGET_TYPE.TIANSHEN then
            des = self.suit_id_cfg[suit_index].des_tianshen
        elseif display_type == TARGET_TYPE.ROLE then
            des = self.suit_id_cfg[suit_index].des_tianshen
        elseif self.suit_skill[skill_id] then
            if suit_index == 4 and self.trait_index > 1 then
                skill_icon = self.equip_trait_index[self.trait_index].icon_show
                des = self.equip_trait_index[self.trait_index].des1
            else
                skill_icon = self.suit_skill[skill_id].skill_pic
                des = self.suit_skill[skill_id].des_skill
            end
            name = self.suit_skill[skill_id].suit_skill_name
        else
            des = self.suit_id_cfg[suit_index].des_tianshen
        end
    end

    return des, skill_icon, name
end

--判断是否存在技能配置
function EquipTargetWGData:GetSuitSkillActive(skill_id)
    return not IsEmptyTable(self.suit_skill[skill_id])
end

function EquipTargetWGData:SetEquipKillBossInfo(trait_index)
    self.trait_index = trait_index
end

--设置总数据
function EquipTargetWGData:SetAllEquipInfo(m_all_param)
    local role_lv = RoleWGData.Instance:GetRoleLevel()
    local data_list = {}
    for k, v in pairs(m_all_param) do
        if not IsEmptyTable(self.suit_id_cfg[v.suit_index]) then
            if self.suit_id_cfg[v.suit_index].open_level <= role_lv then
                table.insert(data_list,v)
            end
        end
    end

    self.active_equip_data = data_list
    self.m_all_param = m_all_param

    for k, v in pairs(self.m_all_param) do
        self.new_equip_list[k] = v.equip_list
    end
end

--设置页签数据
function EquipTargetWGData:SetEquipInfoChange(protocol)
    self.cur_gaer = protocol.cur_gaer
    self.suit_active_reward = protocol.suit_active_reward
    self.eqsuit_index = protocol.suit_index
end

function EquipTargetWGData:GetEquipSkillAct(index)
    if not IsEmptyTable(self.m_all_param) and not IsEmptyTable(self.m_all_param[index + 1]) then
        if self.m_all_param[index + 1].suit_active_reward == 1 then
            return true
        end
    end
    return false
end

--获取对应页签的套装index以及激活状态
function EquipTargetWGData:GetEquipTypeChangeInfo()
    return self.cur_gaer, self.eqsuit_index, self.suit_active_reward
end

function EquipTargetWGData:GetSuitInfo(suit_index)
    return self.m_all_param[suit_index + 1] or {}
end

function EquipTargetWGData:GetEquipStateNumByIndex(suit_index)
    local equip_num = 0
    if not IsEmptyTable(self.m_all_param) and not IsEmptyTable(self.m_all_param[suit_index + 1])then
        for k, v in pairs(self.m_all_param[suit_index + 1].equip_state) do
            if v > 0 then
                equip_num = equip_num + 1
            end
        end
    end
    return equip_num
end

function EquipTargetWGData:GetEquipShowStarLevel(suit_index, index)
    local star_level = 3
    if not IsEmptyTable(self.equip_star_list[suit_index + 1]) then
        for k, v in pairs(self.equip_star_list[suit_index + 1]) do
            if k == index then
                star_level = tonumber(v)
            end
        end
    end

    return star_level
end

--判断主界面套装装备的对应位置是否激活
function EquipTargetWGData:GetEquipSortStateByIndex(suit_index, index, is_eqview)

    local is_show = true
    local star_level = 3

    if not IsEmptyTable(self.equip_star_list[suit_index + 1]) then
        for k, v in pairs(self.equip_star_list[suit_index + 1]) do
            if k == index then
                star_level = tonumber(v)
            end
        end
    end

    if not IsEmptyTable(self.old_equip_state) and not IsEmptyTable(self.old_equip_state[suit_index + 1]) and not is_eqview then
        for k, v in pairs(self.old_equip_state[suit_index + 1]) do
            if k == index - 1 and v > 0 then
                is_show = false
            end
        end
    end

    if is_eqview then
        if not IsEmptyTable(self.m_all_param) and not IsEmptyTable(self.m_all_param[suit_index + 1]) then
            for k, v in pairs(self.m_all_param[suit_index + 1].equip_state) do
                if k == index - 1 and v > 0 then
                    is_show = false
                end
            end
        end
    end

    if not IsEmptyTable(self.old_equip_star) and not IsEmptyTable(self.old_equip_star[suit_index + 1]) then
        for k, v in pairs(self.old_equip_star[suit_index + 1]) do
            if k == index and v >= 3 then
                star_level = v
            end
        end
    end

    return is_show, star_level
end

function EquipTargetWGData:SetOldEquipListInfo()
    if not IsEmptyTable(self.m_all_param) then
        for k, v in pairs(self.m_all_param) do
            self.old_equip_state[k] = v.equip_state
            -- self.old_equip_list[k] = v.equip_list
            self.old_equip_star[k] = v.star_level
        end
    end
end

function EquipTargetWGData:GetOldDataFetchedList()
    local list = {}
    if not IsEmptyTable(self.m_all_param) and not IsEmptyTable(self.m_all_param[self.eqsuit_index + 1]) then
        for k, v in pairs(self.m_all_param[self.eqsuit_index + 1]) do
            list[k] = v
        end
    end

    return list
end

--获取套装激活档次
function EquipTargetWGData:GetEquipJinDu(suit_index)
    return ((self.m_all_param or {})[suit_index + 1] or {})["gaer_state"] or {}, ((self.m_all_param or {})[suit_index + 1] or {})["cur_gaer"] or 0
end

-- function EquipTargetWGData:GetEquipTargetRemind(suit_index)
--     local role_lv = RoleWGData.Instance:GetRoleLevel()
--     local gaer_list, cur_gaer = self:GetEquipJinDu(suit_index)
--     local info = self.suit_id_cfg[suit_index]
--     local open_level = info and info.open_level or RoleWGData.GetRoleMaxLevel()
--     if role_lv < open_level then
--         return false
--     end
--     for i = 1, cur_gaer do
--         if gaer_list[i-1] and gaer_list[i-1] <= 0 then
--             return true
--         end
--     end
--     return false
-- end

function EquipTargetWGData:JumpToRemind()
    local role_lv = RoleWGData.Instance:GetRoleLevel()
    local index = 0

    for k, v in pairs(self.active_equip_data) do
        if v.suit_active_reward <= 0 then
            if self.suit_id_cfg[v. suit_index] and self.suit_id_cfg[v.suit_index].open_level <= role_lv then
                index = k
            end
            return index
        else
            index = index + 1
        end
    end

    return 0
end

function EquipTargetWGData:GetEquipTargetRemind(suit_index, is_contian_compose)
    local index = 0
    local role_lv = RoleWGData.Instance:GetRoleLevel()
    local base_info = self.suit_id_cfg
    if not IsEmptyTable(self.active_equip_data) and role_lv >= self.equip_suit_active[1].limit_level then
        for k, v in pairs(self.active_equip_data) do
            if v.suit_index == suit_index then
                local info = base_info[v.suit_index]
                local open_level = RoleWGData.GetRoleMaxLevel()
                if not IsEmptyTable(info) then
                    open_level = info.open_level
                end
    
                if role_lv >= open_level then
                    for i = 1, v.cur_gaer do
                        if v.gaer_state[i - 1] <= 0 and i <= 4 and v.cur_gaer > 0  then
                            index = k
                            -- EquipTargetWGCtrl.Instance:CreatMainUiActbtn(MAINUI_TIP_TYPE.EQUIP_TARGET, 1)
                            return true, index
                        end
                    end
                end
            end
        end
    end

    if is_contian_compose then
        local info = base_info[suit_index]
        local is_limit = false
        if info.compose_red_limit_index ~= -1 and not self:IsAllActive(info.compose_red_limit_index) then
            is_limit = true
        end
        if not is_limit then
            local cur_num, max_num = EquipTargetWGData.Instance:GetEquipStateNumBySuitIndex(suit_index)
            if cur_num < max_num then
                local list = EquipTargetWGData.Instance:GetEquipList(suit_index)
                for k_1, v_1 in pairs(list) do
                    local item_id = tonumber(v_1)
                    if item_id > 0 then
                        local is_show, star_level = EquipTargetWGData.Instance:GetEquipSortStateByIndex(suit_index, k_1, true)
                        if is_show and EquipmentWGData.Instance:GetCESingleRemindByData(item_id, star_level) then
                            index = k_1
                            return true, index
                        end
                    end
                end
            end
        end
    end

    return false, index
end

function EquipTargetWGData:GetEquipTargetAllRemind()
    local index = 0
    local role_lv = RoleWGData.Instance:GetRoleLevel()
    local base_info = self.suit_id_cfg
    if not IsEmptyTable(self.active_equip_data) and role_lv >= self.equip_suit_active[1].limit_level then
        for k, v in pairs(self.active_equip_data) do
            local info = base_info[v.suit_index]
            local open_level = RoleWGData.GetRoleMaxLevel()
            if not IsEmptyTable(info) then
                open_level = info.open_level
            end

            if role_lv >= open_level then
                for i = 1, v.cur_gaer do
                    if v.gaer_state[i - 1] <= 0 and i <= 4 and v.cur_gaer > 0  then
                        -- index为可升级的档位
                        index = k
                        EquipTargetWGCtrl.Instance:CreatMainUiActbtn(MAINUI_TIP_TYPE.EQUIP_TARGET, 1)
                        return 1, index
                    end
                end
            end
        end
    end

    -- 没收集完 但是可以合成
    for suit_index, value in pairs(base_info) do

        local is_limit = false
        if value.compose_red_limit_index ~= -1 and not self:IsAllActive(value.compose_red_limit_index) then
            is_limit = true
        end
        if not is_limit then
            local cur_num, max_num = EquipTargetWGData.Instance:GetEquipStateNumBySuitIndex(suit_index)
            if cur_num < max_num then
                local list = EquipTargetWGData.Instance:GetEquipList(suit_index)
                for k_1, v_1 in pairs(list) do
                    local item_id = tonumber(v_1)
                    if item_id > 0 then
                        local is_show, star_level = EquipTargetWGData.Instance:GetEquipSortStateByIndex(suit_index, k_1, true)
                        if is_show and EquipmentWGData.Instance:GetCESingleRemindByData(item_id, star_level) then
                            index = k_1
                            return 1, index
                        end
                    end
                end
            end
        end
    end   
    

    EquipTargetWGCtrl.Instance:CreatMainUiActbtn(MAINUI_TIP_TYPE.EQUIP_TARGET, 0)
    return 0, index
end


function EquipTargetWGData:GetEquipSuitActiveCfg()
    return self.equip_suit_active[1]
end

-- VIPBoss 一二层
function EquipTargetWGData:IsVIPBossScene()
    local cur_scene_id = Scene.Instance:GetSceneId() or 1-- 当前场景id
    if cur_scene_id == VIP_BOSS_SCENE_ID_0 then
        if self:IsAllActive(0) then
            return true, 1
        end
        return true, 0
    end
    if cur_scene_id == VIP_BOSS_SCENE_ID_1 then
        if self:IsAllActive(2) then
            return true, 3
        end
        return true, 2
    end
    return false
end

--------------------合成说明--------------------
-- 获取合成说明cfg
function  EquipTargetWGData:GetComposeDescCfg()
    return self.compose_desc_cfg
end

-- 获取合成说明cfg长度
function  EquipTargetWGData:GetComposeDescCount()
    return #self.compose_desc_cfg
end

----------------------入口显示--------------------------
function EquipTargetWGData:GetEquipTargetFuncOpen()
    local is_equip_collect_open = FunOpen.Instance:GetFunIsOpened(FunName.MainEquipTargetView)

    local list_data = self:GetEquipBigType()
    local active_count = false
    for k_1, v_1 in ipairs(list_data) do
        local list = self:GetEquipList(v_1.suit_index)
        for k_2, v_2 in ipairs(list) do
            if v_2 and tonumber(v_2) > 0 then

                local state = self:GetEquipSortStateByIndex(v_1.suit_index, k_2, true)
                if not state then
                    active_count = true
                    break
                end
            end
        end

        if active_count then
            break
        end
    end

    return is_equip_collect_open and active_count
end