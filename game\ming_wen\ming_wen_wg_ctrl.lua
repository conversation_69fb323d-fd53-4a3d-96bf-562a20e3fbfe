require("game/ming_wen/ming_wen_wg_data")
require("game/ming_wen/ming_wen_view")
require("game/ming_wen/ming_wen_bag_view")
require("game/ming_wen/ming_wen_library_view")
require("game/ming_wen/ming_wen_convert_tips")
require("game/ming_wen/ming_wen_item_render")
require("game/ming_wen/ming_wen_fen_jie")
require("game/ming_wen/ming_wen_all_attr_view")

MingWenWGCtrl = MingWenWGCtrl or BaseClass(BaseWGCtrl)
function MingWenWGCtrl:__init()
	if MingWenWGCtrl.Instance ~= nil then
		ErrorLog("[MingWenWGCtrl] Attemp to create a singleton twice !")
	end
	MingWenWGCtrl.Instance = self

	self.view = MingWenView.New(GuideModuleName.MingWenView)
	self.data = MingWenWGData.New()
	self.bag_view = MingWenBagView.New()
	self.library_view = MingWenLibraryView.New()
	self.mingwen_convert_tips = MingWenConvertTips.New()
	self.mingwen_all_attr_view = MingWenAllAttrView.New()

	self:RegisterAllProtocols()
    self.delay_notice_list = {}
    Runner.Instance:AddRunObj(self, 8)

end

function MingWenWGCtrl:__delete()
    Runner.Instance:RemoveRunObj(self)
	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.library_view then
		self.library_view:DeleteMe()
		self.library_view = nil
	end
	if self.bag_view then
		self.bag_view:DeleteMe()
		self.bag_view = nil
	end

	if self.mingwen_convert_tips then
		self.mingwen_convert_tips:DeleteMe()
	end

	if self.mingwen_all_attr_view then
		self.mingwen_all_attr_view:DeleteMe()
	end
	MingWenWGCtrl.Instance = nil
end


function MingWenWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSPosyOperator)
	self:RegisterProtocol(CSPosyDeCompose)
	self:RegisterProtocol(CSPosyCompose)
	self:RegisterProtocol(SCPosyBagInfo,"OnSCPosyBagInfo")
	self:RegisterProtocol(SCPosyGridInfo,"OnSCPosyGridInfo")
	self:RegisterProtocol(SCPosyBaseInfo,"OnSCPosyBaseInfo")

    self:RegisterProtocol(SCOtherItemAddNotice, "OnSCOtherItemAddNotice") --获得铭纹的提示
end
			-- POSY_OPERATOR_TYPE_BASE_INFO = 0,	// 基本信息
			-- POSY_OPERATOR_TYPE_UP_LEVEL = 1,	// 升级 p1(slot) 特殊槽不能升级
			-- POSY_OPERATOR_TYPE_COMPOSE = 2,		// 合成 p1(item_id)
			-- POSY_OPERATOR_TYPE_BAG_INFO = 3,	// 背包信息
			-- POSY_OPERATOR_TYPE_PUT_SLOT = 4,	// 装备 p1(slot) p2(背包位置) p3(is_speical)
			-- POSY_OPERATOR_TYPE_DOWN_SLOT = 5,	// 卸下 p1(slot)
			-- POSY_OPERATOR_TYPE_DE_COMPOSE_INDEX = 6,	// 分解p1(背包位置)
			-- POSY_OPERATOR_TYPE_DE_COMPOSE_QUAILITY = 7,	// 分解p1(quality)

function MingWenWGCtrl:SendMingWenOperaReq(opera_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSPosyOperator)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()

end

function MingWenWGCtrl:OnSCOtherItemAddNotice(protocol)
    local put_reason = protocol.reason_type
    if put_reason == PUT_REASON_TYPE.PUT_REASON_FISH or
    put_reason == PUT_REASON_TYPE.PUT_REASON_CSA_YANHUA_SHENGDIAN_REWARD
    or put_reason == PUT_REASON_TYPE.PUT_REASON_RA_YANHUA_SHENGDIAN_2_REWARD then
        local temp_tb = {}
        temp_tb.change_item_id = protocol.item_id
        temp_tb.put_reason = put_reason
        temp_tb.add_num = protocol.num
        if put_reason == PUT_REASON_TYPE.PUT_REASON_FISH then--运营活动幸运锦鲤，捕鱼
            local is_do_tween = OperationActivityWGCtrl.Instance and OperationActivityWGCtrl.Instance:GetIsDoTween()
			if is_do_tween then --有播动画
				temp_tb.notice_time_stamp = Status.NowTime + OAFishWGData.Instance:GetNoticeDelayTime()
			else
				temp_tb.notice_time_stamp = Status.NowTime
            end
        elseif put_reason == PUT_REASON_TYPE.PUT_REASON_CSA_YANHUA_SHENGDIAN_REWARD then
            temp_tb.notice_time_stamp = Status.NowTime + MergeFireworksWGData.Instance:GetDelayTime()
        elseif put_reason == PUT_REASON_TYPE.PUT_REASON_RA_YANHUA_SHENGDIAN_2_REWARD then--节日活动烟花盛典扭蛋机
            temp_tb.notice_time_stamp = Status.NowTime + NiuDanWGData.Instance:GetDelayTime()
        else
            temp_tb.notice_time_stamp = Status.NowTime
        end
        table.insert(self.delay_notice_list, temp_tb)
    else
        local item_cfg = ItemWGData.Instance:GetItemConfig(protocol.item_id)
        if item_cfg then
            local str = string.format(Language.TreasureHunt.MingWenGetItem, ITEM_COLOR[item_cfg.color], item_cfg.name, protocol.num)
            SysMsgWGCtrl.Instance:ErrorRemind(str)
        end
    end
end

function MingWenWGCtrl:Update(now_time, elapse_time)
	if #self.delay_notice_list > 0 then
		for i = #self.delay_notice_list, 1, -1 do
			if now_time > self.delay_notice_list[i].notice_time_stamp then
                local t = table.remove(self.delay_notice_list, i)
                local item_cfg = ItemWGData.Instance:GetItemConfig(t.change_item_id)
                if item_cfg then
                    local str_1 = t.not_mingwen and Language.TreasureHunt.GetItem or  Language.TreasureHunt.MingWenGetItem
                    local str = string.format(str_1, ITEM_COLOR[item_cfg.color], item_cfg.name, t.add_num)
                    SysMsgWGCtrl.Instance:ErrorRemind(str)
                end
			end
		end
	end
end

function MingWenWGCtrl:SendMingWenDeCompose(count, list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSPosyDeCompose)
	protocol.count = count or 0
	protocol.index_list = list or {}
	protocol:EncodeAndSend()
end

function MingWenWGCtrl:SendMingWenCompose(item_id,count, index_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSPosyCompose)
	protocol.item_id = item_id or 0
	protocol.count = count or 0
	protocol.index_list = index_list or {}
	protocol:EncodeAndSend()
end


-----背包信息
function MingWenWGCtrl:OnSCPosyBagInfo(protocol)
	self.data:OnSCPosyBagInfo(protocol)
	if self.view:IsOpen() then
		self.view:FLushCurIndex()
	end
	RemindManager.Instance:Fire(RemindName.MingWen_HeCheng)
	RemindManager.Instance:Fire(RemindName.MingWen_FenJie)
	RemindManager.Instance:Fire(RemindName.MingWen_XiangQian)
	RemindManager.Instance:Fire(RemindName.MingWen_HeCheng_In_View)
	
end

----单个改变
function MingWenWGCtrl:OnSCPosyGridInfo(protocol)
	self.data:OnSCPosyGridInfo(protocol)
	if self.view:IsOpen() then
		self.view:FLushCurIndex()
	end
	RemindManager.Instance:Fire(RemindName.MingWen_HeCheng)
	RemindManager.Instance:Fire(RemindName.MingWen_FenJie)
	RemindManager.Instance:Fire(RemindName.MingWen_XiangQian)
	RemindManager.Instance:Fire(RemindName.MingWen_HeCheng_In_View)
end

----基本信息
function MingWenWGCtrl:OnSCPosyBaseInfo(protocol)
	local old_data_list = self.data:GetEquipPosyData()
    self.data:OnSCPosyBaseInfo(protocol)
    self:ShowMingWenMoneyChange(protocol)

	if self.view:IsOpen() then
		local slot_index = self.view.cur_select_slot_index
		if slot_index and slot_index >= 0 then
			local old_data = old_data_list[slot_index].posy
			local new_data = protocol.base_slot[slot_index].posy
			if old_data.item_id > 0 and new_data.item_id > 0 and old_data.item_id ~= new_data.item_id then
				TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.MingWenView.TipDesc3, new_data.level))
			end
		end

		self.view:FLushCurIndex()
	end

	RemindManager.Instance:Fire(RemindName.MingWen_HeCheng)
	RemindManager.Instance:Fire(RemindName.MingWen_FenJie)
	RemindManager.Instance:Fire(RemindName.MingWen_XiangQian)
	RemindManager.Instance:Fire(RemindName.MingWen_HeCheng_In_View)
end

--铭晶
function MingWenWGCtrl:ShowMingWenMoneyChange(protocol)
    if self.mingwen_xinjing and self.mingwen_xinjing < protocol.mingwen_xinjing then
        local add_num = protocol.mingwen_xinjing - self.mingwen_xinjing  
        local put_reason = protocol.reason_type
        if put_reason == PUT_REASON_TYPE.PUT_REASON_TU_NV_LANG 
        or put_reason == PUT_REASON_TYPE.PUT_REASON_CSA_YANHUA_SHENGDIAN_REWARD
        or put_reason == PUT_REASON_TYPE.PUT_REASON_RA_YANHUA_SHENGDIAN_2_REWARD  then
            local temp_tb = {}
            temp_tb.change_item_id = COMMON_CONSTS.VIRTUAL_ITEM_XINJING
            temp_tb.put_reason = put_reason
            temp_tb.add_num = add_num
            temp_tb.not_mingwen = true -- 不是铭纹
            if put_reason == PUT_REASON_TYPE.PUT_REASON_TU_NV_LANG then--运营活动伏羲演卦
                if OATurnTableWGData.Instance and OATurnTableWGData.Instance:GetIsTurningTable() then
                    temp_tb.notice_time_stamp = Status.NowTime + 6
                else
                    temp_tb.notice_time_stamp = Status.NowTime
                end
            elseif put_reason == PUT_REASON_TYPE.PUT_REASON_CSA_YANHUA_SHENGDIAN_REWARD then
                temp_tb.notice_time_stamp = Status.NowTime + MergeFireworksWGData.Instance:GetDelayTime()
            elseif put_reason == PUT_REASON_TYPE.PUT_REASON_RA_YANHUA_SHENGDIAN_2_REWARD then--节日活动烟花盛典扭蛋机
                temp_tb.notice_time_stamp = Status.NowTime + FestivalFireworksWGData.Instance:GetDelayTime()
            else
                temp_tb.notice_time_stamp = Status.NowTime
            end
            table.insert(self.delay_notice_list, temp_tb)
        else
            local cfg = ItemWGData.Instance:GetItemConfig(COMMON_CONSTS.VIRTUAL_ITEM_XINJING)
		    local str = string.format(Language.TreasureHunt.GetItem, ITEM_COLOR[cfg.color],cfg.name, add_num)
            SysMsgWGCtrl.Instance:ErrorRemind(str)
        end

    end
    self.mingwen_xinjing = protocol.mingwen_xinjing
end

function MingWenWGCtrl:OpenTipsAttrView(tips_data)
	self.mingwen_all_attr_view:SetData(tips_data)
end

--slot_index 为-1 展示所有
function MingWenWGCtrl:OpenMingWenBag(slot_index, is_from_xunbao)
	if not self.bag_view:IsOpen() then
		self.bag_view:Open()
	end
	self.data:SetSelectPosyIndex(slot_index)
    self.bag_view:Flush(nil,nil,{["is_from_xunbao"] = is_from_xunbao})
end

--打开铭文总览页面
function MingWenWGCtrl:OpenPosyLibrary()
	if not self.library_view:IsOpen() then
		self.library_view:Open()
	end
end

function MingWenWGCtrl:ComposeResult(result)
	if result > 0 then
		if self.view:IsOpen() then
			self.view:Flush(TabIndex.ming_wen_he_cheng,"compose_success")
		end
	end
end

function MingWenWGCtrl:UpGradeResult(result)
	if result > 0 then
		if self.view:IsOpen() then
			self.view:Flush(TabIndex.ming_wen_xiang_qian, "upgrade_success")
		end
	end
end

function MingWenWGCtrl:ActiveResult(result)
	if result > 0 then
		if self.view:IsOpen() then
			self.view:Flush(TabIndex.ming_wen_xiang_qian, "active_success")
		end
	end
end


function MingWenWGCtrl:OpenMingWenConvertTipsView(data)
	self.mingwen_convert_tips:SetDataAndOpen(data)
end
