MarketWGData = MarketWGData or  BaseClass()

MarketWGData.MaxPriceCoin = 99999999					-- 价格上限-铜币
MarketWGData.MaxPriceGold = 99999						-- 价格上限-元宝

MarketWGData.PriceTypeCoin = 1						-- 价格类型-铜币
MarketWGData.PriceTypeGold = 2						-- 价格类型-元宝

MarketWGData.SaleItemTypeCoin = 1						-- 出售物品类型-铜币
MarketWGData.SaleItemTypeItem = 2						-- 出售物品类型-物品

MarketWGData.MyWantBigId = -100 						-- 我的求购大类型
MarketWGData.AllWantBigId = -200 						-- 全部求购大类型

--拍卖类型
AUCTION_TYPE = {
	Country = 0,			--国家拍卖
	Guild = 1,				--仙盟拍卖
	System = 2,				--系统拍卖
	Guild_Battle = 4,		--仙盟战跨服拍卖
}

--拍卖信息大分类类型(用于顶部大页签)
AUCTION_INFO_BIG_TYPE = {
	Country = 1,		--国家拍卖
	Guild = 2,			--仙盟拍卖
	CrossServer = 3,	--跨国拍卖
}

--拍卖日志类型
AUCTION_LOG_STATE = {
	My = 1,				--我的记录
	Country_Succ = 2,	--国家卖出
	Country_Unsucc = 3,	--国家流拍
	Guild_Succ = 4,		--仙盟卖出
	Guild_Unsucc = 5,	--仙盟流拍
	CrossServer_Succ = 6,	--跨国卖出
	CrossServer_Unsucc = 7,	--跨国流拍
}

--跨服拍卖进攻状态
AUCTION_CS_ATTACK_STATE = {
	None = 0,				--无状态
	Attack = 1,				--进攻
	Defend = 2,				--防守
}

--拍卖红点类型
local Auction_Remind_Type = {
	Map = 1,			--版图
	Guild = 2,			--仙盟
	Guild_LiuPai = 3,	--仙盟流拍
	CrossServer = 4,	--跨国
}

function MarketWGData:__init()
	if MarketWGData.Instance ~= nil then
		ErrorLog("[MarketWGData] attempt to create singleton twice!")
		return
	end
	MarketWGData.Instance = self

	self:InitProtocolInfo()
	self:InitCfg()

	--红点绑定
	RemindManager.Instance:Register(RemindName.Market_Cross_Server, BindTool.Bind(self.CheckAuctionRemind, self, AUCTION_INFO_BIG_TYPE.CrossServer))--市场跨服拍卖
	RemindManager.Instance:Register(RemindName.Market_Country_Auction, BindTool.Bind(self.CheckAuctionRemind, self, AUCTION_INFO_BIG_TYPE.Country))--市场国家拍卖
	RemindManager.Instance:Register(RemindName.Market_Guild_Auction, BindTool.Bind(self.CheckAuctionRemind, self, AUCTION_INFO_BIG_TYPE.Guild))--市场仙盟拍卖
end

function MarketWGData:__delete()
	self.all_auction_info_list_by_index = nil
	self.all_cs_auction_info_list_by_index = nil
	self.auction_info_list_big_type = nil
	self.auction_info_list_sub_type = nil
	self.my_auction_index_list = nil
	self.all_auction_log_list = nil

	RemindManager.Instance:UnRegister(RemindName.Market_Cross_Server)
	RemindManager.Instance:UnRegister(RemindName.Market_Country_Auction)
	RemindManager.Instance:UnRegister(RemindName.Market_Guild_Auction)

	MarketWGData.Instance = nil
end

------------------------------------ 协议数据存取 -----------------------------------
-- 初始化协议数据
function MarketWGData:InitProtocolInfo()
	self.market_goods_list = {}						-- 所有商品列表
	self.market_index_list = {} 					-- 存储商品在列表中index值的列表，该列表的key值为auction_index（商品唯一索引）

	self.market_goods_amount_list = {} 				-- 商品数目
	self.goods_aount_list_init = false 				-- 商品数目是否已经初始化完成

	self.my_market_goods_list = {} 					-- 自己上架的商品列表
	self.my_market_index_list = {} 					-- 存储商品在列表中index值的列表，该列表的key值为auction_index（商品唯一索引）
	self.my_market_goods_list_is_init = false 		-- 数据是否初始化完毕

	self.record_info_list = {} 						-- 市场买卖记录

	self.market_want_list = {} 						-- 所有求购信息列表
	self.market_want_index_list = {} 				-- 存储求购信息在列表中index值的列表，该列表的key值为wanted_index（求购信息唯一索引）
	self.market_want_list_is_init = false

	self.my_market_want_list = {} 					-- 我发布的求购信息
	self.my_market_want_index_list = {} 			-- 存储求购信息在列表中index值的列表，该列表的key值为wanted_index（求购信息唯一索引）

	self.all_auction_info_list_by_index = {}		--全部本服拍卖信息 key:index
	self.all_cs_auction_info_list_by_index = {}		--全部跨服拍卖信息 key:index
	self.auction_info_list_big_type = {}			--拍卖信息--大分类存储
	self.auction_info_list_sub_type = {}			--拍卖信息-子类型分类存储
	self.my_auction_index_list = {}					--我的竞拍信息--只存索引

	self.all_auction_log_list = {}					--竞拍全部日志信息
	self.auction_be_outdone_info_list = {}			--当前参与竞拍的商品价格被超越信息
	self.auction_up_qipao_active = true 			--每次上线有拍卖红点,都显示气泡

	self.week_active = 0
	self.week_shelves_count = 0
end

-- 辅助：根据 auction_index 查找目标在列表中的位置
function MarketWGData:FindIndexInListByAuctionIndex(goods_list, auction_index)
    if not goods_list or not auction_index then
        return nil
    end

    for idx, item in ipairs(goods_list) do
        if item and item.auction_index == auction_index then
            return idx
        end
    end

    return nil
end

-- 辅助：安全移除并维护索引表（swap-remove）
-- index_map 为 auction_index -> list_index 的映射表
function MarketWGData:SafeSwapRemove(goods_list, remove_index, index_map)
    if not goods_list or not index_map then return end
    local last_index = #goods_list
    if not remove_index or last_index <= 0 then return end

    local last_goods = goods_list[last_index]
    if remove_index ~= last_index and last_goods then
        index_map[last_goods.auction_index] = remove_index
        goods_list[remove_index] = last_goods
    end

    goods_list[last_index] = nil
end

-- 设置子类商品数据列表(如果数据量过大(>1000)，服务端会分批下发)
function MarketWGData:AddMarketGoodsListInfo(protocol)
	if not self.market_goods_list[protocol.big_id] then
		self.market_goods_list[protocol.big_id] = {}
	end

	if not self.market_goods_list[protocol.big_id][protocol.small_id] then
		self.market_goods_list[protocol.big_id][protocol.small_id] = {}
	end

	for i,v in ipairs(protocol.goods_list_info) do
		table.insert(self.market_goods_list[protocol.big_id][protocol.small_id], v)
		self.market_index_list[v.auction_index] = #self.market_goods_list[protocol.big_id][protocol.small_id]
	end
end

-- 设置自身上架的子类商品数据列表
function MarketWGData:SetMyMarketGoodsListInfo(protocol)
	self.my_market_goods_list = {}
	for i,v in ipairs(protocol.my_goods_list_info) do
		table.insert(self.my_market_goods_list, v)
		self.my_market_index_list[v.auction_index] = #self.my_market_goods_list
	end

	self.my_market_goods_list_is_init = true
end

-- 设置商品数量
function MarketWGData:SetGoodsAmountList(protocol)
	self.goods_aount_list_init = true
	for i,v in ipairs(protocol.amount_info_list) do
		self:AddGoodsAmount(v.big_id, v.small_id, v.amount)
	end
end

-- 增加商品数量
function MarketWGData:AddGoodsAmount(big_id, small_id, amount)
	if not self.goods_aount_list_init then
		return
	end

	amount = amount or 1
	if not self.market_goods_amount_list[big_id] then
		self.market_goods_amount_list[big_id] = {}
	end

	if not self.market_goods_amount_list[big_id][small_id] then
		self.market_goods_amount_list[big_id][small_id] = 0
	end

	self.market_goods_amount_list[big_id][small_id] = self.market_goods_amount_list[big_id][small_id] + amount
end

-- 减少商品数量
function MarketWGData:SubtractGoodsAmount(big_id, small_id, amount)
	if not self.goods_aount_list_init then
		return
	end

	amount = amount or 1
	if not self.market_goods_amount_list[big_id] then
		self.market_goods_amount_list[big_id] = {}
	end

	if not self.market_goods_amount_list[big_id][small_id] then
		self.market_goods_amount_list[big_id][small_id] = 0
		return
	end

	self.market_goods_amount_list[big_id][small_id] = self.market_goods_amount_list[big_id][small_id] - amount
end

-- 获取对应子类的商品数量
function MarketWGData:GetGoodsAmount(big_id, small_id)
	-- 判断获取的是不是一个大类型下的全部子类型商品
	if self:IsAllSubtype(small_id) then
		local sub_all_goods_amount = 0
		local sub_type_cfg = self:GetSubTypeCfg(big_id)
		for i,v in ipairs(sub_type_cfg) do
			local single_sub_type_goods_amount = CheckList(self.market_goods_amount_list, big_id, v.small_id) or 0
			sub_all_goods_amount = sub_all_goods_amount + single_sub_type_goods_amount
		end
		return sub_all_goods_amount
	end

	return CheckList(self.market_goods_amount_list, big_id, small_id) or 0
end

-- 单个商品更变
function MarketWGData:UpdateMarketGoodsInfo(protocol)
    local cfg = self:GetAuctionCfgByItemId(protocol.change_item_info.item_data.item_id)
    if not cfg then
        return
    end

	-- 更新所有商品列表（已下发完整数据的列表才进行更新）
	if self.market_goods_list[cfg.big_id] then
		local goods_t = self.market_goods_list[cfg.big_id][cfg.small_id]
		if goods_t then
			-- 增加商品
			if protocol.op_type == AUCTIONITEM_OP_TYPE.AUCTIONITEM_OP_TYPE_ADD then
				table.insert(goods_t, protocol.change_item_info)
				self.market_index_list[protocol.change_item_info.auction_index] = #goods_t
			-- 商品更变
			elseif protocol.op_type == AUCTIONITEM_OP_TYPE.AUCTIONITEM_OP_TYPE_YAOHE then
                local need_change_index = self.market_index_list[protocol.change_item_info.auction_index]
                if not need_change_index then
                    need_change_index = self:FindIndexInListByAuctionIndex(goods_t, protocol.change_item_info.auction_index)
                    if need_change_index then
                        self.market_index_list[protocol.change_item_info.auction_index] = need_change_index
                    end
                end

                if need_change_index then
                    goods_t[need_change_index] = protocol.change_item_info
                else
                    -- 若未找到，追加并建立索引，避免空索引
                    table.insert(goods_t, protocol.change_item_info)
                    self.market_index_list[protocol.change_item_info.auction_index] = #goods_t
                end
			-- 移除商品
			elseif protocol.op_type == AUCTIONITEM_OP_TYPE.AUCTIONITEM_OP_TYPE_REMOVE
				or protocol.op_type == AUCTIONITEM_OP_TYPE.AUCTIONITEM_OP_TYPE_DIRECTYBUG then

                local need_remove_index = self.market_index_list[protocol.change_item_info.auction_index]
                if not need_remove_index then
                    need_remove_index = self:FindIndexInListByAuctionIndex(goods_t, protocol.change_item_info.auction_index)
                end
                -- 清理被移除项的索引
                self.market_index_list[protocol.change_item_info.auction_index] = nil
                if need_remove_index then
                    local last_goods = goods_t[#goods_t]
                    if last_goods and last_goods.auction_index and need_remove_index ~= #goods_t then
                        self.market_index_list[last_goods.auction_index] = need_remove_index
                    end
                    self:SafeSwapRemove(goods_t, need_remove_index, self.market_index_list)
                end
			end
		end
	end

	-- 更新自己上架的商品列表
	local role_id = RoleWGData.Instance:GetOriginUid()
	local my_goods = role_id == protocol.change_item_info.seller_id 		-- 判断是否是自己上架的商品
	if my_goods then
		local goods_t = self.my_market_goods_list
		if self.my_market_goods_list_is_init then
			-- 增加商品
			if protocol.op_type == AUCTIONITEM_OP_TYPE.AUCTIONITEM_OP_TYPE_ADD then
				table.insert(goods_t, protocol.change_item_info)
				self.my_market_index_list[protocol.change_item_info.auction_index] = #goods_t
			-- 商品更变
			elseif protocol.op_type == AUCTIONITEM_OP_TYPE.AUCTIONITEM_OP_TYPE_YAOHE then
                local need_change_index = self.my_market_index_list[protocol.change_item_info.auction_index]
                if not need_change_index then
                    need_change_index = self:FindIndexInListByAuctionIndex(goods_t, protocol.change_item_info.auction_index)
                    if need_change_index then
                        self.my_market_index_list[protocol.change_item_info.auction_index] = need_change_index
                    end
                end
                if need_change_index then
                    goods_t[need_change_index] = protocol.change_item_info
                else
                    -- 若本地未记录该商品则追加，避免空索引
                    table.insert(goods_t, protocol.change_item_info)
                    self.my_market_index_list[protocol.change_item_info.auction_index] = #goods_t
                end
			-- 移除商品
			elseif protocol.op_type == AUCTIONITEM_OP_TYPE.AUCTIONITEM_OP_TYPE_REMOVE
				or protocol.op_type == AUCTIONITEM_OP_TYPE.AUCTIONITEM_OP_TYPE_DIRECTYBUG then
                local need_remove_index = self.my_market_index_list[protocol.change_item_info.auction_index]
                if not need_remove_index then
                    need_remove_index = self:FindIndexInListByAuctionIndex(goods_t, protocol.change_item_info.auction_index)
                end
                -- 清理被移除项的索引
                self.my_market_index_list[protocol.change_item_info.auction_index] = nil
                if need_remove_index then
                    local last_goods = goods_t[#goods_t]
                    if last_goods and last_goods.auction_index and need_remove_index ~= #goods_t then
                        self.my_market_index_list[last_goods.auction_index] = need_remove_index
                    end
                    self:SafeSwapRemove(goods_t, need_remove_index, self.my_market_index_list)
                end
			end
		end
	end

	-- 更新商品数量
	if protocol.op_type == AUCTIONITEM_OP_TYPE.AUCTIONITEM_OP_TYPE_ADD then
		self:AddGoodsAmount(cfg.big_id, cfg.small_id)
	elseif protocol.op_type == AUCTIONITEM_OP_TYPE.AUCTIONITEM_OP_TYPE_REMOVE
		or protocol.op_type == AUCTIONITEM_OP_TYPE.AUCTIONITEM_OP_TYPE_DIRECTYBUG then
		self:SubtractGoodsAmount(cfg.big_id, cfg.small_id)
	end

end

-- 获取对应类型的商品列表
function MarketWGData:GetGoodsListInfo(big_type, sub_type)
	-- 判断获取的是不是一个大类型下的全部子类型商品
	if self:IsAllSubtype(sub_type) then
		local sub_all_goods = {}
		local sub_type_cfg = self:GetSubTypeCfg(big_type)
		for i,v in ipairs(sub_type_cfg) do
			local single_sub_type_goods = CheckList(self.market_goods_list, big_type, v.small_id) or {}
			for i,v in ipairs(single_sub_type_goods) do
				table.insert(sub_all_goods, v)
			end
		end
		return sub_all_goods
	end

	return CheckList(self.market_goods_list, big_type, sub_type) or {}
end

-- 获得在市场上售出的同种物品的商品列表
function MarketWGData:GetGoodsInfoByItemid(item_id)
	local auction_cfg = self:GetAuctionCfgByItemId(item_id)
	if not auction_cfg then
		return {}
	end

	local result = {}
	local goods_list_info = self:GetGoodsListInfo(auction_cfg.big_id, auction_cfg.small_id)
	for i,v in ipairs(goods_list_info) do
		if v.item_data.item_id == item_id then
			table.insert(result, v)
		end
	end
	table.sort(result, SortTools.KeyLowerSorter("total_price"))
	return result
end

-- 对应类型商品数据是否已经下发完整数据
function MarketWGData:GoodsListInfoIsInit(big_type, sub_type)
	return CheckList(self.market_goods_list, big_type, sub_type) ~= nil
end

-- 获取自己上架的商品列表
function MarketWGData:GetMyGoodsListInfo()
	return self.my_market_goods_list
end

-- 获取自己上架的商品数目
function MarketWGData:GetMyGoodsListAmount()
	return #self:GetMyGoodsListInfo()
end

-- 设置购买记录
function MarketWGData:SetRecordInfoList(protocol)
	self.record_info_list = protocol.record_list
end

-- 获得购买记录
function MarketWGData:GetRecordInfoList()
	return self.record_info_list
end

-- 根据大类型和子类型获取求购列表信息
function MarketWGData:GetWantListInfo(big_type, sub_type)
	return CheckList(self.market_want_list, big_type, sub_type) or {}
end

-- 获取所有求购信息
function MarketWGData:GetAllWantListInfo()
	local result = {}
	for big_type, v in pairs(self.market_want_list) do
		for sub_type, v2 in pairs(v) do
			for _, v3 in ipairs(v2) do
				table.insert(result, v3)
			end
		end
	end
	return result
end

-- 获得自己发布的求购信息
function MarketWGData:GetMyWantListInfo()
	return self.my_market_want_list
end

-- 设置子类求购数据列表(如果数据量过大，服务端会分批下发)
function MarketWGData:AddMarketWantListInfo(protocol)
	for i,v in ipairs(protocol.want_list_info) do
		local auction_cfg = self:GetAuctionCfgByItemId(v.item_id)
		local big_id = auction_cfg.big_id
		local small_id = auction_cfg.small_id
		if not self.market_want_list[big_id] then
			self.market_want_list[big_id] = {}
		end

		if not self.market_want_list[big_id][small_id] then
			self.market_want_list[big_id][small_id] = {}
		end

		table.insert(self.market_want_list[big_id][small_id], v)
		self.market_want_index_list[v.wanted_index] = #self.market_want_list[big_id][small_id]

		-- 更新自己的求购列表
		local role_id = RoleWGData.Instance:GetOriginUid()
		local my_want = role_id == v.wanted_uid 							-- 判断是否是自己发出的求购信息
		if my_want then
			table.insert(self.my_market_want_list, v)
			self.my_market_want_index_list[v.wanted_index] = #self.my_market_want_list
		end
	end
	self.market_want_list_is_init = true
end

-- 求购列表是否初始化
function MarketWGData:GetMarketWantListIsInit()
	return self.market_want_list_is_init
end

-- 单个求购信息更变
function MarketWGData:SetMarketWantInfo(protocol)
	-- 更新所有求购信息列表（已下发完整数据的列表才进行更新）
	if not self:GetMarketWantListIsInit() then
		return
	end
	local cfg = self:GetAuctionCfgByItemId(protocol.change_item.item_id)

	if not self.market_want_list[cfg.big_id] then
		self.market_want_list[cfg.big_id] = {}
	end

	if not self.market_want_list[cfg.big_id][cfg.small_id] then
		self.market_want_list[cfg.big_id][cfg.small_id] = {}
	end

	local want_t = self.market_want_list[cfg.big_id][cfg.small_id]
	-- 增加求购信息
	if protocol.op_type == MARKET_WANT_UPDATE_TYPE.ADD then
		table.insert(want_t, protocol.change_item)
		self.market_want_index_list[protocol.change_item.wanted_index] = #want_t
	-- 移除求购信息
	elseif protocol.op_type == MARKET_WANT_UPDATE_TYPE.REMOVE then
		local need_remove_index = self.market_want_index_list[protocol.change_item.wanted_index]
		local last_want = want_t[#want_t]

		if need_remove_index == nil then
			print_error("删除一个没有的数据,请检查！", want_t, #want_t, cfg.big_id, cfg.small_id, protocol.change_item)
			return
		end

		self.market_want_index_list[last_want.wanted_index] = need_remove_index
		self.market_want_index_list[protocol.change_item.wanted_index] = nil

		want_t[need_remove_index] = last_want
		want_t[#want_t] = nil
	end

	-- 更新自己的求购列表数据
	local role_id = RoleWGData.Instance:GetOriginUid()
	local my_want = role_id == protocol.change_item.wanted_uid 							-- 判断是否是自己发出的求购信息
	if my_want then
		-- 增加个人求购信息
		if protocol.op_type == MARKET_WANT_UPDATE_TYPE.ADD then
			table.insert(self.my_market_want_list, protocol.change_item)
			self.my_market_want_index_list[protocol.change_item.wanted_index] = #self.my_market_want_list
		-- 移除个人求购信息
		elseif protocol.op_type == MARKET_WANT_UPDATE_TYPE.REMOVE then
			local need_remove_index = self.my_market_want_index_list[protocol.change_item.wanted_index]
			if need_remove_index == nil then
				print_error("删除一个没有的数据,请检查！", protocol.change_item.wanted_index, self.my_market_want_index_list, protocol.change_item, self.my_market_want_list)
				return
			end
			local last_want = self.my_market_want_list[#self.my_market_want_list]

			self.my_market_want_index_list[last_want.wanted_index] = need_remove_index
			self.my_market_want_index_list[protocol.change_item.wanted_index] = nil

			self.my_market_want_list[need_remove_index] = last_want
			self.my_market_want_list[#self.my_market_want_list] = nil
		end
	end
end
----------------------------------- 协议数据存取End ---------------------------------

------------------------------------ 配置数据存取 -----------------------------------
-- 初始化配置表
function MarketWGData:InitCfg()
	-- 大类型配置
	self.big_type_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("auctionconfig_auto").big_type, "big_id")

	-- 子类型配置
	self.sub_type_cfg = ListToMapList(ConfigManager.Instance:GetAutoConfig("auctionconfig_auto").small_type, "big_id")
	self.sub_type_cfg_2 = ListToMap(ConfigManager.Instance:GetAutoConfig("auctionconfig_auto").small_type, "small_id")

	-- 装备星级上架限制
	self.equip_limit_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("auctionconfig_auto").equip_limit, "equip_color")

	-- 商品配置
	local all_market_item_cfg = {}
	for i,v in pairs(ItemWGData.Instance:GetEquipmentCfg()) do
		if v.can_auction == 1 then
			table.insert(all_market_item_cfg, self:MarketItemStruct(v))
		end
	end

	for i,v in pairs(ItemWGData.Instance:GetExpenseCfg()) do
		if v.can_auction == 1 then
			table.insert(all_market_item_cfg, self:MarketItemStruct(v))
		end
	end

	for i,v in pairs(ItemWGData.Instance:GetGiftCfg()) do
		if v.can_auction == 1 then
			table.insert(all_market_item_cfg, self:MarketItemStruct(v))
		end
	end

	for i,v in pairs(ItemWGData.Instance:GetOtherCfg()) do
		if v.can_auction == 1 then
			table.insert(all_market_item_cfg, self:MarketItemStruct(v))
		end
	end

	for i,v in pairs(ItemWGData.Instance:GetVirtualCfg()) do
		if v.can_auction == 1 then
			table.insert(all_market_item_cfg, self:MarketItemStruct(v))
		end
	end

	self.auction_cfg_1 = ListToMapList(all_market_item_cfg, "big_id", "small_id")
	self.auction_cfg_2 = ListToMap(all_market_item_cfg, "item_id")
	self.auction_cfg_3 = ListToMapList(all_market_item_cfg, "big_id")
	self.all_market_item_cfg = all_market_item_cfg

	--拍卖配置
	self.new_auction_cfg = ConfigManager.Instance:GetAutoConfig("new_auction_auto")
	self.new_auction_item_cfg = ListToMap(self.new_auction_cfg.auction_item, "type", "item_id")
	self.new_auction_type_cfg = ListToMap(self.new_auction_cfg.auction_type, "type")
	--self.new_auction_one_price_toggle = self.new_auction_cfg.other[1].guild_to_auction_price_button
end

-- --仙盟拍卖一口价显示开关
-- function MarketWGData:GetGuildOnePriceIsShow()
-- 	return self.new_auction_one_price_toggle == 1
-- end

function MarketWGData:MarketItemStruct(item_cfg)
	local big_id = self:GetBigIdBySmallId(item_cfg.search_type)

	if big_id then
		return {
			item_id = item_cfg.id,
			big_id = big_id,
			small_id = item_cfg.search_type,
			auction_price_type = item_cfg.auction_price_type,   -- 货币类型
			auction_price = item_cfg.auction_price, 			-- 推荐价格
			item_cfg = item_cfg,
		}
	end
	return nil
end

-- 转化推荐价格
function MarketWGData:ConvertAuctionPrice(auction_cfg, star_num)
	if not auction_cfg then
		return 0
	end

	star_num = star_num or 0
	local price_list = Split(auction_cfg.auction_price, ",")
	if price_list[star_num + 1] then
		return tonumber(price_list[star_num + 1])
	end

	print_error("没有找到物品id为：" .. auction_cfg.item_id .. "的" .. star_num .. "星物品的推荐价格，请检查物品表的auction_price字段")
	return price_list[0] or self:GetMinPrice()
end

-- 获得商品配置
function MarketWGData:GetAuctionCfgByType(...)
	return CheckList(self.auction_cfg_1, ...)
end

-- 获得对应id的商品配置
function MarketWGData:GetAuctionCfgByItemId(item_id)
	item_id = tonumber(item_id)
	return CheckList(self.auction_cfg_2, item_id)
end

-- 获得一个大类型下的全部子类型商品配置
function MarketWGData:GetAuctionCfgByBigType(big_type)
	return self.auction_cfg_3[big_type]
end

-- 获得所有商品配置
function MarketWGData:GetAuctionCfg()
	return self.all_market_item_cfg
end

-- 获得大类型配置
function MarketWGData:GetBigTypeCfg(...)
	return CheckList(self.big_type_cfg, ...)
end

-- 获取大类型配置，加上全部求购、我的求购
function MarketWGData:GetBigTypeCfgAndMyWant()
	local result = {}

	local all_want_cfg = {
		big_id = MarketWGData.AllWantBigId,
		big_name = Language.Market.AllWantStr
	}

	local my_want_cfg = {
		big_id = MarketWGData.MyWantBigId,
		big_name = Language.Market.MyWantStr
	}

	table.insert(result, all_want_cfg)
	table.insert(result, my_want_cfg)
	for i,v in ipairs(MarketWGData.Instance:GetBigTypeCfg()) do
		table.insert(result, v)
	end
	return result
end

function MarketWGData:GetSubTypeCfg(...)
	return CheckList(self.sub_type_cfg, ...) or {}
end

function MarketWGData:GetSubTypeCfgBySubType(sub_type)
	return self.sub_type_cfg_2[sub_type]
end

function MarketWGData:GetSubTypeCfgForBuyView(big_type)
	local cfg = self:GetSubTypeCfg(big_type)
	local result_cfg = {}
	result_cfg[1] = {} 		-- 插入一个“全部”子类型
	for k, v in pairs(cfg[1]) do
		result_cfg[1][k] = v
	end

	result_cfg[1].small_id = self:ConvertToAllSubtype(result_cfg[1].small_id)
	result_cfg[1].small_name = Language.Market.All
	local big_type_cfg = self:GetBigTypeCfg(big_type)
	result_cfg[1].item_id = big_type_cfg.item_id

	for i, v in ipairs(cfg) do
		table.insert(result_cfg, v)
	end

	return result_cfg
end

function MarketWGData:GetBigIdBySmallId(small_id)
	if self.sub_type_cfg_2[small_id] then
		return self.sub_type_cfg_2[small_id].big_id
	else
		print_error("拍卖配置里没有找到小类型为：" .. small_id .. "的配置，请检查物品表的search_type是否有对应拍卖小类型")
	end
end

function MarketWGData:GetOriginalSubTypeCfg()
	return ConfigManager.Instance:GetAutoConfig("auctionconfig_auto").small_type
end

-- 获得市场其他配置
function MarketWGData:GetOtherCfg()
	return ConfigManager.Instance:GetAutoConfig("auctionconfig_auto").other[1]
end

-- 获得上架最低价格
function MarketWGData:GetMinPrice()
	return self:GetOtherCfg().shelves_min_price
end

-- 获取一种子类商品的阶数列表
function MarketWGData:GetOrderList(big_type, sub_type)
	local result = {}
	local temp = {}
	local cfg = nil

	if big_type == MarketWGData.AllWantBigId then
		cfg = self:GetAuctionCfg()
	else
		-- 判断是不是"全部"子类型
		if self:IsAllSubtype(sub_type) then
			cfg = self:GetAuctionCfgByBigType(big_type) 				-- 获取大类型下全部子类型商品
		else
			cfg = self:GetAuctionCfgByType(big_type, sub_type)			-- 获取大类型下一个子类型商品
		end
	end

	if not cfg then
		return result
	end

	for k, v in ipairs(cfg) do
		local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		if item_cfg and item_cfg.order and not temp[item_cfg.order] then
			table.insert(result, item_cfg.order)
			temp[item_cfg.order] = true
		end
	end

	table.sort(result)

	return result
end

-- 获取一种子类商品的品质列表
function MarketWGData:GetColorList(big_type, sub_type)
	local result = {}
	local temp = {}
	local cfg = nil

	if big_type == MarketWGData.AllWantBigId then
		cfg = self:GetAuctionCfg()
	else
		-- 判断是不是"全部"子类型
		if self:IsAllSubtype(sub_type) then
			cfg = self:GetAuctionCfgByBigType(big_type) 				-- 获取大类型下全部子类型商品
		else
			cfg = self:GetAuctionCfgByType(big_type, sub_type)			-- 获取大类型下一个子类型商品
		end
	end

	if not cfg then
		return result
	end

	for k, v in ipairs(cfg) do
		local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		if item_cfg and item_cfg.color and not temp[item_cfg.color] then
			table.insert(result, item_cfg.color)
			temp[item_cfg.color] = true
		end
	end

	table.sort(result)

	return result
end

-- 获得一个商品所有星级
function MarketWGData:GetStarList(item_id)
	local result = {}
	if item_id == nil then
		return result
	end
	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(item_id)
	if item_type ~= GameEnum.ITEM_BIGTYPE_EQUIPMENT then
		return result
	end

	local rand_attr_cfg = EquipmentWGData.Instance:GetEquipRandAttrCfg(item_cfg.color)
	for equip_star = 1, rand_attr_cfg.rand_attr_num do
		if rand_attr_cfg["star_" .. equip_star .. "_weight"]
			and rand_attr_cfg["star_" .. equip_star .. "_weight"] > 0
			and equip_star >= self:GetEquipStarLimit(item_cfg.color) then 						-- 星级限制
			table.insert(result, equip_star)
		end
	end
	return result
end

-- 获取装备上架星级限制
function MarketWGData:GetEquipStarLimit(color)
	local star_limit_cfg = self.equip_limit_cfg[color]
	if star_limit_cfg then
		return star_limit_cfg.star_level_limit
	end
	return 0
end
----------------------------------- 配置数据存取End ---------------------------------

-- 市场上架面板背包数据
function MarketWGData:GetMarketSellBagInfoList()
	local result = {}
	local index = 0
	local bag_item_list = ItemWGData.Instance:GetBagItemDataList() 		-- 普通背包
	for k,v in pairs(bag_item_list) do
		if self:CheckIsCanMarket(v) then
			result[index] = v
			index = index + 1
		end
	end

	local stuff_data_list = ItemWGData.Instance:GetStuffStorgeItemData()
	for k,v in pairs(stuff_data_list) do
		if self:CheckIsCanMarket(v) then
			result[index] = v
			index = index + 1
		end
	end

	-- local shenshou_list = ShenShouWGData.Instance:GetShenshouBackpackInfo() -- 神兽
	local shenshou_list = ShenShouWGData.Instance:GetShenShouBagDataList()
	if shenshou_list then
	 	for k,v in pairs(shenshou_list) do
			if self:CheckIsCanMarket(v) then
				result[index] = v
				index = index + 1
			end
	 	end
	end

	return result -- 从0开始
end

-- 在所有背包查找是否拥有对应物品
function MarketWGData:SearchItemInAllBag(item_id, item_num, equip_star)
	local all_item = self:GetMarketSellBagInfoList()
	if IsEmptyTable(all_item) then
		return nil
	end

	for i = 0, #all_item do
		if all_item[i].item_id == item_id and all_item[i].num >= item_num then
			if all_item[i].param == nil or (all_item[i].param and all_item[i].param.star_level == equip_star) then
				return all_item[i]
			end
		end
	end
	return nil
end

-- 人物等级是否足够
function MarketWGData:GetMarketItemNeedLevel(item_id)
	return true
	-- local auction_cfg = self:GetAutoConfigByItemId(item_id)
	-- if not auction_cfg then
	-- 	return false
	-- end
	-- return Scene.Instance:GetMainRole().level >= auction_cfg.open_level
end

-- 是否能上架
function MarketWGData:CheckIsCanMarket(data)
	if not data or not data.item_id then
		return false
	end

	-- 装备需要判断星级限制
	if data.param and data.param.star_level then
		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(data.item_id)
		if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
			local star_limit = self:GetEquipStarLimit(item_cfg.color)
			if data.param.star_level < star_limit then
				return false
			end
		end
	end

	return self:GetAuctionCfgByItemId(data.item_id) ~= nil and data.is_bind == 0
end

-- 获取交易税率
function MarketWGData:GetTax()
	return tonumber(VipWGData.Instance:GetVipSpecPermissionsValue(VIP_LEVEL_AUTH_TYPE.MARKET_TAX)) / 10000
end

-- 判断当前玩家能否设置上架密码
function MarketWGData:CanSetPassword()
	return tonumber(VipWGData.Instance:GetVipSpecPermissionsValue(VIP_LEVEL_AUTH_TYPE.MARKET_PASSWORD)) == 1
end

-- 获得玩家可上架数目
function MarketWGData:CanSellAmount()
	return tonumber(VipWGData.Instance:GetVipSpecPermissionsValue(VIP_LEVEL_AUTH_TYPE.MARKET_SELL_AMOUNT))
end

-- 判断该子类型是否是“全部”子类型
function MarketWGData:IsAllSubtype(sub_type)
	if not sub_type then
		return false
	end
	return sub_type % 100 == 0
end

-- 转换成“全部”子类型
function MarketWGData:ConvertToAllSubtype(sub_type)
	return math.floor(sub_type / 100) * 100
end

---------拍卖----start----------------------------------------------------------------------------
--所有拍卖商品信息
function MarketWGData:SetAllAuctionInfo(protocol)
	local is_cross_info = protocol.is_cross == 1
	self:ResetAuctionInfo(is_cross_info)
	-- self.all_auction_info_list_by_index = {}
	local temp_all_list = {}
	for k, v in pairs(protocol.all_auction_list) do
		--全部信息
		temp_all_list[v.index] = v
	end
	--根据跨服区别设置全部信息
	if is_cross_info then
		self.all_cs_auction_info_list_by_index = temp_all_list
	else
		self.all_auction_info_list_by_index = temp_all_list
	end

	self:UpdateAuctionCacheInfo(temp_all_list)
end

function MarketWGData:ResetAuctionInfo(is_cross_info)
	if is_cross_info then
		self.all_cs_auction_info_list_by_index = {}
		self.auction_info_list_big_type[AUCTION_INFO_BIG_TYPE.CrossServer] = {}
	else
		self.all_auction_info_list_by_index = {}
		self.auction_info_list_big_type[AUCTION_INFO_BIG_TYPE.Country] = {}
		self.auction_info_list_big_type[AUCTION_INFO_BIG_TYPE.Guild] = {}
	end
end

--刷新市场竞价缓存信息
-- fix_type : 用于区分信息按最新类型存放
function MarketWGData:UpdateAuctionCacheInfo(all_info_list)
	-- self.auction_info_list_big_type[AUCTION_INFO_BIG_TYPE.CrossServer] = {}
	-- self.auction_info_list_big_type[AUCTION_INFO_BIG_TYPE.Country] = {}
	-- self.auction_info_list_big_type[AUCTION_INFO_BIG_TYPE.Guild] = {}
	-- self.auction_info_list_sub_type = {}

	if IsEmptyTable(all_info_list) then
		self:FireAuctionAllReminds()
		return
	end

	for k, v in pairs(all_info_list) do
		-- 国家/仙盟分类信息
		if v.fix_type == AUCTION_TYPE.Country or v.fix_type == AUCTION_TYPE.System then-- 国家拍卖/系统拍卖 同一面板显示
			table.insert(self.auction_info_list_big_type[AUCTION_INFO_BIG_TYPE.Country], v)
		elseif v.fix_type == AUCTION_TYPE.Guild then				-- 仙盟拍卖
			table.insert(self.auction_info_list_big_type[AUCTION_INFO_BIG_TYPE.Guild], v)
		elseif v.fix_type == AUCTION_TYPE.Guild_Battle then			-- 跨国拍卖
			table.insert(self.auction_info_list_big_type[AUCTION_INFO_BIG_TYPE.CrossServer], v)
		end

		-- 国家/仙盟 子分类信息
		local cfg = self:GetNewAuctionCfgByType(v.fix_type, v.item_id)
		if cfg then
			if not self.auction_info_list_sub_type[v.fix_type] then
				self.auction_info_list_sub_type[v.fix_type] = {}
			end

			if not self.auction_info_list_sub_type[v.fix_type][cfg.sub_type] then
				self.auction_info_list_sub_type[v.fix_type][cfg.sub_type] = {}
			end

			table.insert(self.auction_info_list_sub_type[v.fix_type][cfg.sub_type], v)
		end
	end

	self:FireAuctionAllReminds()
end

function MarketWGData:OnAuctionItemInfoUpdate(protocol)
	local change_info = protocol.auction_data
	local is_cross = protocol.is_cross == 1
	--是否已存在这个信息
	local is_exist_info = false
	if is_cross then--全部跨服 拍卖信息列表更新
		is_exist_info = self.all_cs_auction_info_list_by_index[change_info.index] ~= nil
		self.all_cs_auction_info_list_by_index[change_info.index] = change_info
	else--全部本服 拍卖信息列表更新
		is_exist_info = self.all_auction_info_list_by_index[change_info.index] ~= nil
		self.all_auction_info_list_by_index[change_info.index] = change_info
	end
	
	-- print_error("FFF=== 是否已存在这个信息", is_exist_info, change_info.index)

	if change_info.type ~= change_info.fix_type then--类型发生变更,检测一下清除旧的数据
		self:RemoveAuctionInfoByFixType(change_info.index, change_info)
	end

	local cfg = self:GetNewAuctionCfgByType(change_info.fix_type, change_info.item_id)
	if not cfg then
		print_error(string.format("拍卖配置有问题: type:%s, item_id:%s", change_info.fix_type, change_info.item_id))
		return
	end

	if is_exist_info and not IsEmptyTable(self.auction_info_list_sub_type[change_info.fix_type])
		and not IsEmptyTable(self.auction_info_list_sub_type[change_info.fix_type][cfg.sub_type]) then
		-- (国家拍卖/系统拍卖)
		local info_type = AUCTION_INFO_BIG_TYPE.Country
		if change_info.fix_type == AUCTION_TYPE.Guild then				--仙盟拍卖
			info_type = AUCTION_INFO_BIG_TYPE.Guild
		elseif change_info.fix_type == AUCTION_TYPE.Guild_Battle then	--跨国拍卖
			info_type = AUCTION_INFO_BIG_TYPE.CrossServer
		end

		local is_info_in_big_tb = false
		for k, v in pairs(self.auction_info_list_big_type[info_type]) do
			if v.index == change_info.index then
				self.auction_info_list_big_type[info_type][k] = change_info
				is_info_in_big_tb = true
				break
			end
		end

		if not is_info_in_big_tb then
			-- print_error("仙盟流拍--新增111bigbig", change_info.index)
			table.insert(self.auction_info_list_big_type[info_type], change_info)
		end

		local is_info_in_sma_tb = false
		for k, v in pairs(self.auction_info_list_sub_type[change_info.fix_type][cfg.sub_type]) do
			if v.index == change_info.index then
				self.auction_info_list_sub_type[change_info.fix_type][cfg.sub_type][k] = change_info
				is_info_in_sma_tb = true
				break
			end
		end

		if not is_info_in_sma_tb then
			-- print_error("仙盟流拍--新增222smasma", change_info.index)
			table.insert(self.auction_info_list_sub_type[change_info.fix_type][cfg.sub_type], change_info)
		end

	else--不存在这个信息,则新增
		if change_info.fix_type == AUCTION_TYPE.Country or change_info.fix_type == AUCTION_TYPE.System then-- 国家拍卖/系统拍卖
			if not self.auction_info_list_big_type[AUCTION_INFO_BIG_TYPE.Country] then
				self.auction_info_list_big_type[AUCTION_INFO_BIG_TYPE.Country] = {}
			end
			table.insert(self.auction_info_list_big_type[AUCTION_INFO_BIG_TYPE.Country], change_info)
		elseif change_info.fix_type == AUCTION_TYPE.Guild then-- 仙盟拍卖
			if not self.auction_info_list_big_type[AUCTION_INFO_BIG_TYPE.Guild] then
				self.auction_info_list_big_type[AUCTION_INFO_BIG_TYPE.Guild] = {}
			end
			table.insert(self.auction_info_list_big_type[AUCTION_INFO_BIG_TYPE.Guild], change_info)
		elseif change_info.fix_type == AUCTION_TYPE.Guild_Battle then-- 跨国拍卖
			if not self.auction_info_list_big_type[AUCTION_INFO_BIG_TYPE.CrossServer] then
				self.auction_info_list_big_type[AUCTION_INFO_BIG_TYPE.CrossServer] = {}
			end
			table.insert(self.auction_info_list_big_type[AUCTION_INFO_BIG_TYPE.CrossServer], change_info)
		end

		if not self.auction_info_list_sub_type[change_info.fix_type] then
			self.auction_info_list_sub_type[change_info.fix_type] = {}
		end

		if not self.auction_info_list_sub_type[change_info.fix_type][cfg.sub_type] then
			self.auction_info_list_sub_type[change_info.fix_type][cfg.sub_type] = {}
		end

		-- print_error("FFF=== --不存在这个信息,则新增")
		table.insert(self.auction_info_list_sub_type[change_info.fix_type][cfg.sub_type], change_info)--拍卖信息-分类列表
	end

	self:FireAuctionAllReminds()
end

--拍卖的type和fix_type对应不上,检测清除一下旧的表(仙盟类型转国家类型)
function MarketWGData:RemoveAuctionInfoByFixType(check_index, check_info)
	-- (国家拍卖/系统拍卖)
	local info_type = AUCTION_INFO_BIG_TYPE.Country
	if check_info.type == AUCTION_TYPE.Guild then				--仙盟拍卖
		info_type = AUCTION_INFO_BIG_TYPE.Guild
	end

	if not IsEmptyTable(self.auction_info_list_big_type[info_type]) then
		for k, v in pairs(self.auction_info_list_big_type[info_type]) do
			if v.index == check_index then
				table.remove(self.auction_info_list_big_type[info_type], k)
				break
			end
		end
	end

	local cfg = self:GetNewAuctionCfgByType(check_info.type, check_info.item_id)
	if cfg then
		if not self.auction_info_list_sub_type[check_info.type]
			or not self.auction_info_list_sub_type[check_info.type][cfg.sub_type] then
			return
		end

		for k, v in pairs(self.auction_info_list_sub_type[check_info.type][cfg.sub_type]) do
			if v.index == check_index then
				table.remove(self.auction_info_list_sub_type[check_info.type][cfg.sub_type], k)
				break
			end
		end
	end
end

--信息单条移除刷新
function MarketWGData:OnAuctionItemInfoRemove(protocol)
	local need_remove_index = protocol.index
	local is_cross = protocol.is_cross == 1
	local need_remove_info = nil
	if is_cross then
		need_remove_info = self.all_cs_auction_info_list_by_index[need_remove_index]
	else
		need_remove_info = self.all_auction_info_list_by_index[need_remove_index]
	end
	
	--我的竞价列表
	self.my_auction_index_list[need_remove_index] = nil
	-- print_error("FFF=== 000000信息单条移除刷新", need_remove_info)
	if not need_remove_info then
		return
	end

	if is_cross then
		self.all_cs_auction_info_list_by_index[need_remove_index] = nil
	else--全部本服拍卖信息列表
		self.all_auction_info_list_by_index[need_remove_index] = nil
	end

	-- (国家拍卖/系统拍卖)
	local info_type = AUCTION_INFO_BIG_TYPE.Country
	if need_remove_info.fix_type == AUCTION_TYPE.Guild then				--仙盟拍卖
		info_type = AUCTION_INFO_BIG_TYPE.Guild
	elseif need_remove_info.fix_type == AUCTION_TYPE.Guild_Battle then	--跨国拍卖
		info_type = AUCTION_INFO_BIG_TYPE.CrossServer
	end

	if not IsEmptyTable(self.auction_info_list_big_type[info_type]) then
		for k, v in pairs(self.auction_info_list_big_type[info_type]) do
			if v.index == need_remove_index then
				table.remove(self.auction_info_list_big_type[info_type], k)
				break
			end
		end
	end

	local cfg = self:GetNewAuctionCfgByType(need_remove_info.fix_type, need_remove_info.item_id)
	if cfg then
		if not self.auction_info_list_sub_type[need_remove_info.fix_type]
			or not self.auction_info_list_sub_type[need_remove_info.fix_type][cfg.sub_type] then
			return
		end

		for k, v in pairs(self.auction_info_list_sub_type[need_remove_info.fix_type][cfg.sub_type]) do
			if v.index == need_remove_index then
				-- print_error("子分类列表  单条移除成功")
				table.remove(self.auction_info_list_sub_type[need_remove_info.fix_type][cfg.sub_type], k)
				break
			end
		end
	end

	self:FireAuctionAllReminds()
end

--竞拍记录列表信息
--竞拍记录只读取最原始的类型
function MarketWGData:SetAuctionLogInfo(protocol)
	local my_role_id = RoleWGData.Instance:GetUUid()
	local is_cross = protocol.is_cross == 1
	-- self.all_auction_log_list = {}
	self:ResetAuctionLogInfo(is_cross)
	for k, v in pairs(protocol.auction_log_info_list) do
		if not IsEmptyTable(v) and v.item_id and v.item_id > 0 then
			--个人
			if my_role_id == v.auction_uid then
				local temp_key = AUCTION_LOG_STATE.My
				self.all_auction_log_list[temp_key] = self.all_auction_log_list[temp_key] or {}
				table.insert(self.all_auction_log_list[temp_key], v)
			end

			local auction_key
			local role_id = v.auction_uid.temp_low
			--国家    0 / 2
			if v.type == AUCTION_TYPE.Country or v.type == AUCTION_TYPE.System then
				if role_id > 0 then--卖出
					auction_key = AUCTION_LOG_STATE.Country_Succ
				else
					auction_key = AUCTION_LOG_STATE.Country_Unsucc
				end
			-- 仙盟
			elseif v.type == AUCTION_TYPE.Guild then
				--仙盟战 流拍到跨国记录里
				if v.fix_type == AUCTION_TYPE.Guild_Battle then
					if role_id > 0 then--卖出
						auction_key = AUCTION_LOG_STATE.CrossServer_Succ
					else
						auction_key = AUCTION_LOG_STATE.CrossServer_Unsucc
					end
				else
					if role_id > 0 then--卖出
						auction_key = AUCTION_LOG_STATE.Guild_Succ
					else
						auction_key = AUCTION_LOG_STATE.Guild_Unsucc
					end
				end
			end

			if auction_key then
				self.all_auction_log_list[auction_key] = self.all_auction_log_list[auction_key] or {}
				table.insert(self.all_auction_log_list[auction_key], v)
			end
		end
	end
	self:SortAuctionLogInfo()
end

function MarketWGData:ResetAuctionLogInfo(is_cross)
	for log_key, log_list in pairs(self.all_auction_log_list) do
		if is_cross then
			if log_key == AUCTION_LOG_STATE.CrossServer_Succ or log_key == AUCTION_LOG_STATE.CrossServer_Succ then
				log_list = {}
			end
		else
			if log_key ~= AUCTION_LOG_STATE.CrossServer_Succ and log_key ~= AUCTION_LOG_STATE.CrossServer_Succ then
				log_list = {}
			end
		end
	end
end

--单条刷新 竞拍记录列表信息
function MarketWGData:OnAuctionLogInfoUpdate(protocol)
	local data = protocol.auction_log_info
	if IsEmptyTable(data) or not data.item_id or data.item_id <= 0 then
		return
	end

	--个人
	local my_role_id = RoleWGData.Instance:GetUUid()
	if my_role_id == data.auction_uid then
		local temp_key = AUCTION_LOG_STATE.My
		self.all_auction_log_list[temp_key] = self.all_auction_log_list[temp_key] or {}
		table.insert(self.all_auction_log_list[temp_key], data)

		local cfg = MarketWGData.Instance:GetNewAuctionCfgByType(data.type, data.item_id)
		if cfg then
			local is_one_price = data.auction_price >= cfg.one_price
			if is_one_price then
				MarketWGCtrl.Instance:OpenAuctionOnePriceGetView(
					{
						{ item_id = data.item_id, item_num = data.item_num, is_bind = data.is_bind }
					}
				)
			end
		end
	end

	local key
	local role_id = data.auction_uid.temp_low

	--国家
	if data.type == AUCTION_TYPE.Country or data.type == AUCTION_TYPE.System then
		if role_id > 0 then--卖出
			key = AUCTION_LOG_STATE.Country_Succ
		else
			key = AUCTION_LOG_STATE.Country_Unsucc
		end
	-- 仙盟
	elseif data.type == AUCTION_TYPE.Guild then
		if data.fix_type == AUCTION_TYPE.Guild_Battle then
			if role_id > 0 then--卖出
				key = AUCTION_LOG_STATE.CrossServer_Succ
			else
				key = AUCTION_LOG_STATE.CrossServer_Unsucc
			end
		else
			if role_id > 0 then--卖出
				key = AUCTION_LOG_STATE.Guild_Succ
			else
				key = AUCTION_LOG_STATE.Guild_Unsucc
			end
		end
	end

	if key then
		self.all_auction_log_list[key] = self.all_auction_log_list[key] or {}
		table.insert(self.all_auction_log_list[key], data)
	end

	self:SortAuctionLogInfo()
end

--竞拍记录列表信息 排序key: log_time
function MarketWGData:SortAuctionLogInfo()
	local max_count = 50--限制长度
	for k, tb in pairs(self.all_auction_log_list) do
		SortTools.SortDesc(tb, "log_time")
		if #tb > max_count then
			for i = #tb, max_count, -1 do
				table.remove(tb, i)
			end
		end
	end
end

--我的竞价列表信息
function MarketWGData:SetMyAuctionInfo(protocol)
	local info = nil
	for _, index in pairs(protocol.my_auction_info_list) do
		info = self:GetAuctionInfoByIndex(index)
		if info then
			self.my_auction_index_list[index] = index
		end
	end
end

--我的竞价列表信息新增
function MarketWGData:OnMyAuctionInfoAdd(protocol)
	local info = self:GetAuctionInfoByIndex(protocol.index)
	if info then
		self.my_auction_index_list[protocol.index] = protocol.index
	end
end

--获取竞拍页签分类名称 show_index: 1:国家 2:仙盟
function MarketWGData:GetAuctionSortNameList(show_index)
	local name_list = {}
	local sort_key = 1
	for k, v in pairs(self.new_auction_cfg.show_type) do
		if show_index == AUCTION_INFO_BIG_TYPE.Country and (v.type == AUCTION_TYPE.Country or v.type == AUCTION_TYPE.System) then-- 国家拍卖/系统拍卖 同一面板显示
			if v.type == AUCTION_TYPE.Country then
				sort_key = 1
			else
				sort_key = 2
			end
			if not name_list[sort_key] then
				name_list[sort_key] = {}
				name_list[sort_key].f_name = v.name
				name_list[sort_key].c_names = {}
			end
			table.insert(name_list[sort_key].c_names, v.sub_type, v.sub_name)
		elseif show_index == AUCTION_INFO_BIG_TYPE.Guild and v.type == AUCTION_TYPE.Guild then			-- 仙盟拍卖
			if not name_list[sort_key] then
				name_list[sort_key] = {}
				name_list[sort_key].f_name = v.name
				name_list[sort_key].c_names = {}
			end
			table.insert(name_list[sort_key].c_names, v.sub_type, v.sub_name)
		elseif show_index == AUCTION_INFO_BIG_TYPE.CrossServer and (v.type == AUCTION_TYPE.Guild_Battle) then		-- 跨国拍卖
			sort_key = 2

			if not name_list[sort_key] then
				name_list[sort_key] = {}
				name_list[sort_key].f_name = v.name
				name_list[sort_key].c_names = {}
			end
			table.insert(name_list[sort_key].c_names, v.sub_type, v.sub_name)
		end
	end

	return name_list
end

--获取竞拍页签数据 show_index: 1:国家 2:仙盟
function MarketWGData:GetAuctionTabList(show_index)
	local list = {}
	for k, v in pairs(self.new_auction_cfg.show_type) do
		if show_index == AUCTION_INFO_BIG_TYPE.Country and (v.type == AUCTION_TYPE.Country or v.type == AUCTION_TYPE.System) then-- 国家拍卖/系统拍卖 同一面板显示
			table.insert(list, v)
		elseif show_index == AUCTION_INFO_BIG_TYPE.Guild and v.type == AUCTION_TYPE.Guild then			-- 仙盟拍卖
			table.insert(list, v)
		elseif show_index == AUCTION_INFO_BIG_TYPE.CrossServer and (v.type == AUCTION_TYPE.Guild_Battle) then		-- 跨国拍卖
			table.insert(list, v)
		end
	end

	return list
end

--获取各大分类的全部商品信息
function MarketWGData:GetAllAuctionByType(big_type)
	if big_type == AUCTION_TYPE.Country or big_type == AUCTION_TYPE.System then-- 国家拍卖/系统拍卖 同一面板显示
		return self.auction_info_list_big_type[AUCTION_INFO_BIG_TYPE.Country]
	elseif big_type == AUCTION_TYPE.Guild then-- 仙盟拍卖
		return self.auction_info_list_big_type[AUCTION_INFO_BIG_TYPE.Guild]
	elseif big_type == AUCTION_TYPE.Guild_Battle then-- 跨国拍卖
		return self.auction_info_list_big_type[AUCTION_INFO_BIG_TYPE.CrossServer]
	end

	return nil
end

--根据商品索引index取得 商品信息(跨服与本服索引唯一)
function MarketWGData:GetAuctionInfoByIndex(index)
	local temp_info = nil
	temp_info = self.all_auction_info_list_by_index[index]
	if IsEmptyTable(temp_info) then--本服数据取不到,到跨服数据检测下
		temp_info = self.all_cs_auction_info_list_by_index[index]
	end 
	return temp_info
end

function MarketWGData:GetNewAuctionItemInfoByType(big_type, sub_type)
	if self.auction_info_list_sub_type[big_type] then
		return self.auction_info_list_sub_type[big_type][sub_type]
	end
	return nil
end

function MarketWGData:GetNewAuctionCfgByType(type, item_id)
	if self.new_auction_item_cfg and self.new_auction_item_cfg[type] then
		return self.new_auction_item_cfg[type][item_id]
	end
	return nil
end

function MarketWGData:GetMyAuctionIndexList()
	return self.my_auction_index_list
end

--根据商品索引index 判断当前玩家参与过此商品竞拍
function MarketWGData:GetIsMyAuctionByIndex(index)
	if not index then return false end
	return self.my_auction_index_list[index]
end

function MarketWGData:GetAuctionLogInfoByType(type)
	return self.all_auction_log_list[type]
end

--当前参与竞拍的商品价格被超越信息更新
function MarketWGData:OnAuctionBeOutdoneInfoUpdate(new_info)
	table.insert(self.auction_be_outdone_info_list, new_info)
end

function MarketWGData:GetAuctionBeOutdoneInfo()
	return self.auction_be_outdone_info_list
end

function MarketWGData:ClearAuctionBeOutdoneInfo()
	self.auction_be_outdone_info_list = {}
end

--新数据根据旧的位置排好(仅限拍卖特殊用处)
function MarketWGData:SortAuctionInfoListByOld(new_info, old_info)
	if IsEmptyTable(new_info) or IsEmptyTable(old_info) then
		return nil
	end
	local temp_list = {}
	local max_list_length = 0
	local max_length_data = {}--新旧数据比对,取长度最大者用于判断商品是否存在
	if #new_info > #old_info then
		max_list_length = #new_info
		max_length_data = new_info
	else
		max_list_length = #old_info
		max_length_data = old_info
	end

	for i = 1, max_list_length do
		if max_length_data[i] and self:GetAuctionInfoByIndex(max_length_data[i].index) then--看下这个商品是否存在(是否卖掉/流拍)
			if old_info[i] then --旧表中是否存在
				if not new_info[i] or old_info[i].index ~= new_info[i].index then
					for k, v in pairs(new_info) do
						if v.index == old_info[i].index then
							table.insert(temp_list, v)
							break
						end
					end
				else--刷新数据
					table.insert(temp_list, new_info[i])
				end
			else--数据新增了
				table.insert(temp_list, new_info[i])
			end
		end
	end
	return temp_list
end

function MarketWGData:GetPublickTime(type)
	local type_cfg = self.new_auction_type_cfg[type]
	if type_cfg and type_cfg.public_time then
		local public_time = type_cfg.public_time * 60
		return public_time
	else
		return 0
	end
end

--筛选去掉世界拍卖不处于拍卖中的物品,同时返回下个商品的时间用来刷新界面
function MarketWGData:FiterAuction(list)
	local temp_list = {}
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local public_time = self:GetPublickTime(AUCTION_TYPE.Country)
	local next_time = 0
	for k, v in pairs(list) do
		if server_time >= v.sell_time - public_time and server_time <= v.end_time then
			table.insert(temp_list, v)
		
		elseif server_time < v.sell_time - public_time then
			if next_time == 0 then
				next_time = v.sell_time - public_time
			else
				next_time = math.min(next_time, v.sell_time - public_time)
			end
		end

	end
	return temp_list, next_time
end

--检测拍卖的红点
function MarketWGData:CheckAllAuctionRemind()
	if self:CheckAuctionRemind(AUCTION_INFO_BIG_TYPE.CrossServer) > 0 then
		return Auction_Remind_Type.CrossServer
	elseif self:CheckAuctionRemind(AUCTION_INFO_BIG_TYPE.Guild) > 0 then
		return Auction_Remind_Type.Guild
	end

	local num_flag = self:CheckAuctionRemind(AUCTION_INFO_BIG_TYPE.Country)
	if num_flag > 0 then
		return num_flag
	end

	return 0
end

function MarketWGData:CheckAuctionRemind(param)
	local fun_name = FunName.market_country_auction
	if param == AUCTION_INFO_BIG_TYPE.Guild then
		fun_name = FunName.market_guild_auction
	elseif param == AUCTION_INFO_BIG_TYPE.CrossServer then
		fun_name = FunName.market_cross_server
	end

	local is_open = FunOpen.Instance:GetFunIsOpened(fun_name)
	if not is_open then
		return 0
	end

	if param == AUCTION_INFO_BIG_TYPE.Country then
		if not IsEmptyTable(self.auction_info_list_sub_type[AUCTION_TYPE.Country]) then
			for k, sub_tb in pairs(self.auction_info_list_sub_type[AUCTION_TYPE.Country]) do
				if not IsEmptyTable(sub_tb) then--版图类型不为空
					return Auction_Remind_Type.Map
				end
			end
		end

		if not IsEmptyTable(self.auction_info_list_sub_type[AUCTION_TYPE.System]) then
			for _, sub_tb in pairs(self.auction_info_list_sub_type[AUCTION_TYPE.System]) do
				if not IsEmptyTable(sub_tb) then
					for _, auc_info in pairs(sub_tb) do
						if auc_info.type ~= AUCTION_TYPE.System then--国家拍卖含有仙盟流拍转过来的
							return Auction_Remind_Type.Guild_LiuPai
						end
					end
				end
			end
		end
	elseif param == AUCTION_INFO_BIG_TYPE.Guild
			and not IsEmptyTable(self.auction_info_list_big_type[AUCTION_INFO_BIG_TYPE.Guild])
			and RoleWGData.Instance.role_vo.guild_id ~= 0 then
		return Auction_Remind_Type.Guild

	elseif param == AUCTION_INFO_BIG_TYPE.CrossServer then
		if not IsEmptyTable(self.auction_info_list_sub_type[AUCTION_TYPE.Guild_Battle]) then
			for _, sub_tb in pairs(self.auction_info_list_sub_type[AUCTION_TYPE.Guild_Battle]) do
				if not IsEmptyTable(sub_tb) then
					for _, auc_info in pairs(sub_tb) do
						if auc_info.type == AUCTION_TYPE.Guild_Battle then
							return Auction_Remind_Type.Guild_LiuPai
						end
					end
				end
			end
		end

		return 0
	end

	return 0
end

function MarketWGData:FireAuctionAllReminds()
	RemindManager.Instance:Fire(RemindName.Market_Cross_Server)
	RemindManager.Instance:Fire(RemindName.Market_Country_Auction)
	RemindManager.Instance:Fire(RemindName.Market_Guild_Auction)

	if not self:GetAuctionFunIsOpen() then
		return
	end

	local num_flag = self:CheckAllAuctionRemind()
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.Market_Auction, num_flag, function ()
		if num_flag == Auction_Remind_Type.Guild then--优先判断仙盟拍卖
			--跳转仙盟拍卖
			-- MarketWGCtrl.Instance:OpenAuctionInfoAddByType(AUCTION_TYPE.Guild)
			--策划需求改成直接跳转
			FunOpen.Instance:OpenViewByName(GuideModuleName.Market, TabIndex.market_guild_auction)
		-- 跨国拍卖
		elseif num_flag == Auction_Remind_Type.CrossServer then
			FunOpen.Instance:OpenViewByName(GuideModuleName.Market, TabIndex.market_cross_server)
		elseif num_flag == Auction_Remind_Type.Map then
			--跳转版图拍卖
			-- MarketWGCtrl.Instance:OpenAuctionInfoAddByType(AUCTION_TYPE.Country)
			FunOpen.Instance:OpenViewByName(GuideModuleName.Market, TabIndex.market_country_auction)
		else
			--跳转国家拍卖
			-- MarketWGCtrl.Instance:OpenAuctionInfoAddByType(AUCTION_TYPE.System)
			FunOpen.Instance:OpenViewByName(GuideModuleName.Market, TabIndex.market_country_auction)
		end

		return true--气泡框跟随市场拍卖红点--常驻
	end)
end

function MarketWGData:GetAuctionFunIsOpen()
	local flag_1 = FunOpen.Instance:GetFunIsOpened(FunName.market_country_auction)
	local flag_2 = FunOpen.Instance:GetFunIsOpened(FunName.market_guild_auction)
	local flag_3 = FunOpen.Instance:GetFunIsOpened(FunName.market_cross_server)

	return flag_1 or flag_2 or flag_3
end

function MarketWGData:AuctionDataListSort()
	return function(a, b)
		local order_a = 100000
		local order_b = 100000
		--剩余时间比对 小到大
		if a.end_time > b.end_time then
			order_a = order_a + 10000
		elseif a.end_time < b.end_time then
			order_b = order_b + 10000
		end
		--品质比对 大到小!
		local c_str1, color_a = ItemWGData.Instance:GetItemColor(a.item_id)
        local c_str2, color_b = ItemWGData.Instance:GetItemColor(b.item_id)
        if color_a > color_b then
        	order_a = order_a - 1000
        elseif color_a < color_b then
        	order_b = order_b - 1000
        end
        --index对比 小到大
        if a.index > b.index then
			order_a = order_a + 100
		elseif a.index < b.index then
			order_b = order_b + 100
		end

        return order_a < order_b
	end
end

--设置 聊天框拍卖图标 的上方文字气泡显示
function MarketWGData:SetAuctionUpQiPaoActive(is_show)
	self.auction_up_qipao_active = is_show
end

function MarketWGData:GetAuctionUpQiPaoActive()
	return self.auction_up_qipao_active == true
end

--检测当前我国的进攻状态: 0:无状态 1:进攻 2:防守 
function MarketWGData:CheckMyCountryAttackState()
	local a_info = self:GetAllAuctionByType(AUCTION_TYPE.Guild_Battle)
	local state = AUCTION_CS_ATTACK_STATE.None
	if not IsEmptyTable(a_info) and not IsEmptyTable(a_info[1]) then
		local temp_param = a_info[1].param or 0
		local flag = bit:d2b1n(temp_param)
		if flag >= 3 then
			state = AUCTION_CS_ATTACK_STATE.Attack
		elseif flag >= 1 then
			state = AUCTION_CS_ATTACK_STATE.Defend
		else
			state = AUCTION_CS_ATTACK_STATE.None
		end
	end
	return state
end
---------拍卖----end----------------------------------------------------------------------------

function MarketWGData:SetAuctionInfo(protocol)
	self.week_active = protocol.week_active
	self.week_shelves_count = protocol.week_shelves_count
	self.jump_flag = protocol.jump_flag
end

function MarketWGData:GetAuctionInfo()
	return self.week_active, self.week_shelves_count, self.jump_flag
end