ControlBeastsElementActiveView = ControlBeastsElementActiveView or BaseClass(SafeBaseView)

local GM_ATTR_COUNT = 8
local BASE_ATTR_ADD_PER_ID = 100000
local NORMAL_SKILL_HURT_PER_ID = 200000

function ControlBeastsElementActiveView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_beasts_element_active_view")
end

function ControlBeastsElementActiveView:LoadCallBack()
	if not self.attr_list then
		self.attr_list = {}

		for i = 1, GM_ATTR_COUNT do
			local desc_obj = self.node_list.attr_list:FindObj(string.format("attr_%d", i))
			local cell = CommonAttrRender.New(desc_obj)
			cell:SetAttrNameNeedSpace(true)
			cell:SetAttrNeedMaoHao(true)
			self.attr_list[i] = cell
		end
	end
end

function ControlBeastsElementActiveView:ReleaseCallBack()
	if self.attr_list and #self.attr_list > 0 then
		for _, render_cell in ipairs(self.attr_list) do
			render_cell:DeleteMe()
			render_cell = nil
		end

		self.attr_list = nil
	end

	self.now_active_gm_type = nil
	self.now_active_gm_count = nil
end

function ControlBeastsElementActiveView:SetNowActiveGmType(now_active_gm_type, gm_count)
	self.now_active_gm_type = now_active_gm_type
	self.now_active_gm_count = gm_count
end

function ControlBeastsElementActiveView:OnFlush()
	if (not self.now_active_gm_type) or (not self.now_active_gm_count) then
		return
	end

	local temp_fetter_data = ControlBeastsWGData.Instance:GetFetterCfgByTypeNumber(self.now_active_gm_type, self.now_active_gm_count)
	
	if not temp_fetter_data then 
		return 
	end

	self.node_list.title_name_txt.text.text = temp_fetter_data.fetters_name
	local attr_list = {}
	for i = 1, 6 do
		local key = temp_fetter_data["attr_id" .. i]
		if key then
			if not attr_list[key] then
				attr_list[key] = {}
				attr_list[key].attr_str = key
			end

			attr_list[key].attr_value = temp_fetter_data["attr_value" .. i]
		end
	end

	local return_table = {}
	for _, attr_data in pairs(attr_list) do
		if attr_data and attr_data.attr_str and attr_data.attr_str ~= 0 then
			table.insert(return_table, attr_data)
		end
	end

	table.sort(return_table, function (a, b)
		return a.attr_str < b.attr_str
	end)

	-- 插入特殊普攻增伤
	local normal_skill_hurt_per = temp_fetter_data.normal_skill_hurt_per or 0
	local normal_skill_hurt_per_value = math.floor(normal_skill_hurt_per / BEAST_DEFINE.BEASTS_BOUNS_PER2)
	if normal_skill_hurt_per_value > 0 then
		table.insert(return_table, 1, {attr_str = NORMAL_SKILL_HURT_PER_ID, attr_value = normal_skill_hurt_per_value})
	end

	-- 插入特殊基础加成
	local base_attr_add_per = temp_fetter_data.base_attr_add_per or 0
	local base_attr_add_per_value = math.floor(base_attr_add_per / BEAST_DEFINE.BEASTS_BOUNS_PER2)
	if base_attr_add_per_value > 0 then
		table.insert(return_table, 1, {attr_str = BASE_ATTR_ADD_PER_ID, attr_value = base_attr_add_per_value})
	end

	for i, render_cell in ipairs(self.attr_list) do
		render_cell:SetVisible(return_table[i] ~= nil)

		if return_table[i] ~= nil then
			render_cell:SetData(return_table[i])

			if return_table[i].attr_str == NORMAL_SKILL_HURT_PER_ID or return_table[i].attr_str == BASE_ATTR_ADD_PER_ID then
				local name_str = return_table[i].attr_str == NORMAL_SKILL_HURT_PER_ID and Language.ContralBeasts.ElementAttrType1 or Language.ContralBeasts.ElementAttrType2
				render_cell:ResetName(name_str)
				render_cell:ResetAttrVlaue(string.format("%s%%", return_table[i].attr_value))
			end
		end
	end
end