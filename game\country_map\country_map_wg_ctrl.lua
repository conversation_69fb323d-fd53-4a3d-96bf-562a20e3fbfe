require("game/country_map/country_map_wg_data")
require("game/country_map/country_map_map_view")
require("game/country_map/country_map_act_view")
require("game/country_map/country_map_actshow_view")
require("game/country_map/country_map_actshow_wg_data")
require("game/country_map/ultimate_battlefield/ultimate_battlefield_view")

CountryMapWGCtrl = CountryMapWGCtrl or BaseClass(BaseWGCtrl)

function CountryMapWGCtrl:__init()
	if nil ~= CountryMapWGCtrl.Instance then
		ErrorLog("[CountryMapWGCtrl]:Attempt to create singleton twice!")
	end
	CountryMapWGCtrl.Instance = self

	self.data = CountryMapWGData.New()
	-- self.actshow_data = CountryMapActShowWgData.New()
	self.map_view = CountryMapMapView.New(GuideModuleName.CountryMapMapView)
	self.act_view = CountryMapActView.New(GuideModuleName.CountryMapActView)

	self:RegisterAllProtocols()
    self:BindGlobalEvent(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind(self.OnSceneLoadingQuite, self))
end

function CountryMapWGCtrl:__delete()
	if self.map_view ~= nil then
		self.map_view:DeleteMe()
		self.map_view = nil
	end

	if self.actshow_data then
		self.actshow_data:DeleteMe()
		self.actshow_data = nil
	end

	if self.act_view ~= nil then
		self.act_view:DeleteMe()
		self.act_view = nil
	end



	if self.data ~= nil then
		self.data:DeleteMe()
		self.data = nil
	end

	CountryMapWGCtrl.Instance = nil
end

-- 协议注册
function CountryMapWGCtrl:RegisterAllProtocols()
	-- 进入其他服成功
	self:RegisterProtocol(SCCrossSpyEnterOtherGSSuc, "OnSCCrossSpyEnterOtherGSSuc")
	-- 进入别的服
	self:RegisterProtocol(CSCrossEnterOtherGSReq)
	-- 返回原服
	self:RegisterProtocol(CSCrossLeaveOtherGSReq)
end

function CountryMapWGCtrl:OpenBoxRewardView(index)
	if index ~= nil then
		self.box_reward_view:SetData(index)
	end
end

function CountryMapWGCtrl:OnSceneLoadingQuite()
	self.scene_change_end = true
	self:CheckEnterOtherGsCallback()
end

function CountryMapWGCtrl:OnSCCrossSpyEnterOtherGSSuc(protocol)
	self.enter_other_gs_key = protocol.key
	self.send_enter_gs_suc = true
	self:CheckEnterOtherGsCallback()
end

function CountryMapWGCtrl:CheckEnterOtherGsCallback()
	if self.send_enter_gs_suc and self.scene_change_end then
		GlobalEventSystem:Fire(SceneEventType.ENTER_OTHER_SERVER_COMPLETE, self.enter_other_gs_key)
		local function end_jump()
			local call_back = self.enter_other_gs_call_back
			self:ClearEnterOtherGsCache()
			if call_back then
				call_back()
			end
		end

		if not TaskWGCtrl.Instance:IsFly() then
			end_jump()
		else
			TaskWGCtrl.Instance:AddFlyUpList(end_jump)
		end
	end
end

function CountryMapWGCtrl:ClearEnterOtherGsCache()
	self.send_enter_gs_suc = nil
	self.scene_change_end = nil
	self.enter_other_gs_call_back = nil
	self.enter_other_gs_key = nil
end

-- 请求进入其他国家服务器, key对应枚举CROSS_ENTER_OTHER_GS_KEY
function CountryMapWGCtrl:SendCrossEnterOtherGs(plat_type, server_id, key, call_back)
	self:ClearEnterOtherGsCache()
	self.enter_other_gs_call_back = call_back
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossEnterOtherGSReq)
	protocol.plat_type = plat_type
	protocol.server_id = server_id
	protocol.key = key
	protocol:EncodeAndSend()
end

-- 请求返回原服, key对应枚举CROSS_ENTER_OTHER_GS_KEY
function CountryMapWGCtrl:SendReturnToOriginalServer(key, call_back)
	if RoleWGData.Instance:InOtherServer() then
		self:ClearEnterOtherGsCache()
		self.send_enter_gs_suc = true 		-- 如果在其他跨服请求，默认该退出一定会成功
		self.enter_other_gs_key = key
		self.enter_other_gs_call_back = call_back

		local protocol = ProtocolPool.Instance:GetProtocol(CSCrossLeaveOtherGSReq)
		protocol:EncodeAndSend()
	end
end