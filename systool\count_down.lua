-- 倒计时

CountDown = CountDown or BaseClass()

function CountDown:__init()
	if nil ~= CountDown.Instance then
		print_error("[CountDown]:Attempt to create singleton twice!")
	end
	CountDown.Instance = self

	self.countdown_list = {}
	self.check_callback_map = {}
	Runner.Instance:AddRunObj(self, 4)
end

function CountDown:__delete()
	CountDown.Instance = nil
	self.countdown_list = {}
	Runner.Instance:RemoveRunObj(self)
end

-- total_time：总时间
-- interval：回调间隔
-- timer_func：执行方法(参数:elapse_time, total_time)
-- return key
function CountDown:AddCountDown(total_time, interval, timer_func, end_call_back)
	if nil == timer_func then
		return
	end

	local cd_info = {
		total_time = total_time,
		interval = interval,
		elapse_time = 0,
		timer_func = timer_func,
		end_call_back = end_call_back,
		last_callback_time = Status.NowTime,
	}

	self.countdown_list[cd_info] = cd_info
	if nil ~= timer_func then
		self.check_callback_map[timer_func] = timer_func
	end
	if nil ~= end_call_back then
		self.check_callback_map[end_call_back] = end_call_back
	end

	local num = GetTableLen(self.countdown_list)
	if num >= 30 then
		print_error(string.format("[CountDown]计时器多达%d条，请检查！", num))
	end

	return cd_info
end

local update_list = {}
local end_call_back_list = {}
function CountDown:Update(now_time, elapse_time)
	local update_list_index = 0
	local end_call_back_list_index = 0

	for k, v in pairs(self.countdown_list) do
		v.elapse_time = v.elapse_time + elapse_time
		if v.elapse_time >= v.total_time then
			v.elapse_time = v.total_time
			self:DelCheckCountInfo(v)
			self.countdown_list[k] = nil

			update_list_index = update_list_index + 1
			update_list[update_list_index] = v

			end_call_back_list_index = end_call_back_list_index + 1
			end_call_back_list[end_call_back_list_index] = v
		elseif now_time - v.last_callback_time >= v.interval then
			v.last_callback_time = now_time
			update_list_index = update_list_index + 1
			update_list[update_list_index] = v
		end
	end

	for i = 1, update_list_index do
		local v = update_list[i]
		v.timer_func(v.elapse_time, v.total_time)
	end

	for i = 1, end_call_back_list_index do
		local v = end_call_back_list[i]
		if nil ~= v.end_call_back then
			Trycall(v.end_call_back, v.elapse_time, v.total_time) 	-- 逻辑中尽是报错，如果一个出现报错，将影响其他地方出现BUG
		end
	end
end

function CountDown:RemoveCountDown(key)
	if key == nil then return end
	self:DelCheckCountInfo(self.countdown_list[key])
	self.countdown_list[key] = nil
end

function CountDown:SetElapseTime(key, elapse_time)
	if nil ~= self.countdown_list[key] then
		self.countdown_list[key].elapse_time = elapse_time
	end
end

function CountDown:GetRemainTime(key)
	if nil ~= self.countdown_list[key] then
		return self.countdown_list[key].total_time - self.countdown_list[key].elapse_time
	end
	return 0
end

function CountDown:HasCountDown(key)
	return nil ~= self.countdown_list[key]
end

function CountDown:DelCheckCountInfo(info)
	if nil == info then return end

	if nil ~= info.timer_func then
		self.check_callback_map[info.timer_func] = nil
	end
	if nil ~= info.end_call_back then
		self.check_callback_map[info.end_call_back] = nil
	end
end

function CountDown:IsExistsListen(callback)
	return nil ~= self.check_callback_map[callback]
end