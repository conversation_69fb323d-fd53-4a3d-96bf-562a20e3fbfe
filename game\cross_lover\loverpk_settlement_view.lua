LoverPkSettlementView = LoverPkSettlementView or BaseClass(SafeBaseView)
function LoverPkSettlementView:__init()
    self.view_style = ViewStyle.Half
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_jiesuan_panel")
    self:AddViewResource(0, "uis/view/cross_lover_prefab", "layout_loverpk_settlement")
    self:SetMaskBg(true)
end

function LoverPkSettlementView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_close"], BindTool.Bind1(self.OnClickClose, self))

    self:UpdateTime(TimeWGCtrl.Instance:GetServerTime(), TimeWGCtrl.Instance:GetServerTime() + 5)
	if CountDownManager.Instance:HasCountDown("LoverPkSettlementView") then
		CountDownManager.Instance:RemoveCountDown("LoverPkSettlementView")
	end
	CountDownManager.Instance:AddCountDown("LoverPkSettlementView", 
		BindTool.Bind1(self.UpdateTime, self), 
		function ()
			local scene_type = Scene.Instance:GetSceneType()

			if scene_type == SceneType.CROSS_PK_LOVER then
				LoverPkWGCtrl.Instance:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.RETURN_STANDBY)
			end

			self:Close()
		end,
	TimeWGCtrl.Instance:GetServerTime() + 5 , nil, 1)
end

function LoverPkSettlementView:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("LoverPkSettlementView") then
		CountDownManager.Instance:RemoveCountDown("LoverPkSettlementView")
	end
end

function LoverPkSettlementView:SetDataInfoAndOpen(data)
	self.is_win = tonumber(data.is_win)
	self.fight_second = tonumber(data.fight_second)
    self.round = tonumber(data.round)
    self.side_list = data.side_list

	if not self:IsOpen() then
        self:Open()
    else
        self:Flush()
    end
end

function LoverPkSettlementView:OnFlush()
	if nil == self.is_win or nil == self.fight_second or nil == self.round or IsEmptyTable(self.side_list) then
		return
	end

    local win_flag = self.is_win == 1
    local lose_flag = self.is_win ~= 1
    self.node_list.victory:CustomSetActive(win_flag)
	self.node_list.lose:CustomSetActive(lose_flag)
    self.node_list.win_bg:CustomSetActive(win_flag)
	self.node_list.lose_bg:CustomSetActive(lose_flag)

    for k, v in pairs(self.side_list) do

        for i, u in pairs(v) do
            local role_id = ((u or {}).uuid or {}).temp_low or 0
            local is_robot = u.is_robot == 1 or false
            if is_robot then
                XUI.UpdateRoleHead(self.node_list["head_default_" .. k .. "_" .. i], self.node_list["head_" .. k .. "_" .. i], role_id, u.sex, u.prof, false, false, false)
            else
                BrowseWGCtrl.Instance:BrowRoelInfo(role_id, function (protocol)
                    XUI.UpdateRoleHead(self.node_list["head_default_" .. k .. "_" .. i], self.node_list["head_" .. k .. "_" .. i], role_id,  protocol.sex,  protocol.prof, false, false, false)
                end)
            end

            self.node_list["name_" .. k .. "_" .. i].text.text = u.name
            self.node_list["be_kill_" .. k .. "_" .. i]:CustomSetActive(u.dead_times ~= 0)
        end
    end

    local target_round = self.round >= 0 and self.round or 0
    local win_str = ""
    local rank_str = ""

    if lose_flag then
        win_str = Language.LoverPK.KnockoutResult[2]
        rank_str = Language.LoverPK.KnockoutRankResult[target_round]
        self.node_list.lose_rank.text.text = win_str .. rank_str
    elseif win_flag then
        if target_round >= 3 then
            win_str = Language.LoverPK.KnockoutResult[3]
        else
            win_str = Language.LoverPK.KnockoutResult[1]
            rank_str = Language.LoverPK.KnockoutRankResult[target_round + 1] or ""
        end

        self.node_list.win_rank.text.text = win_str .. rank_str
    end
end

function LoverPkSettlementView:UpdateTime(elapse_time, total_time)
	if self.node_list.lbl_close then
		local temp_seconds = GameMath.Round(total_time - elapse_time)
		self.node_list.lbl_close.text.text = string.format(Language.LoverPK.EndTimeCountDown, temp_seconds)
	end
end

function LoverPkSettlementView:OnClickClose()
    local scene_type = Scene.Instance:GetSceneType()
	
	if scene_type == SceneType.CROSS_PK_LOVER then
		LoverPkWGCtrl.Instance:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.RETURN_STANDBY)
	end

	self:Close()
end