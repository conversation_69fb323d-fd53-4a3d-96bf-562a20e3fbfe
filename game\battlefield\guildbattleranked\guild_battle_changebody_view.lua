GuildBattleChageView = GuildBattleChageView or BaseClass(SafeBaseView)

function GuildBattleChageView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/guild_battle_ranked_ui_prefab", "layout_guild_changebody")
end

function GuildBattleChageView:LoadCallBack()
	self:SetSecondView(nil, self.node_list["size"])
	self.node_list.title_view_name.text.text = Language.Task.ViewNameChangeBody
	self.node_list.text_1.text.text = Language.Task.GuildBattleChangeBodyDesc
	self.node_list.btn_close.button:AddClickListener(BindTool.Bind(self.OnClickClose, self))
	self.node_list.btn_sure.button:AddClickListener(BindTool.Bind(self.OnClickGoNpc, self, 1))
end

function GuildBattleChageView:ShowIndexCallBack()

end

function GuildBattleChageView:OnClickClose()
	self:Close()
end

function GuildBattleChageView:OnClickGoNpc(machine_type)
	GuildBattleRankedWGCtrl.Instance.view:OnClickUseSkill(machine_type)
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	self:Close()
end