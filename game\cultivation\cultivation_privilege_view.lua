CultivationPrivilegeView = CultivationPrivilegeView or BaseClass(SafeBaseView)

-- local TOKEN_INIT_POS = Vector3(-257, 34, 0)
-- local tween_up_height = 30

function CultivationPrivilegeView:__init()

    self.view_style = ViewStyle.Half
    self:SetMaskBg()
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:AddViewResource(0, "uis/view/cultivation_ui_prefab", "layout_cultivation_privilege_view")
end

function CultivationPrivilegeView:LoadCallBack()
    -- self.node_list.desc_privilege_tip.text.text = Language.Cultivation.PrivilegeViewTips

    if not self.privilege_reward_list then
        self.privilege_reward_list = AsyncListView.New(ItemCell, self.node_list.privilege_reward_list)
        self.privilege_reward_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list.btn_get_reward, BindTool.Bind(self.OnClickGetReward, self))
    XUI.AddClickEventListener(self.node_list.btn_tip, BindTool.Bind(self.OnClickTipBtn, self))
end

function CultivationPrivilegeView:ShowIndexCallBack()
    -- self:PlayTween()
end

function CultivationPrivilegeView:ReleaseCallBack()
    if self.privilege_reward_list then
        self.privilege_reward_list:DeleteMe()
        self.privilege_reward_list = nil
    end

    if CountDownManager.Instance:HasCountDown("cultivation_privilege_time") then
        CountDownManager.Instance:RemoveCountDown("cultivation_privilege_time")
    end

    -- self:CancelTween()
end

function CultivationPrivilegeView:OnFlush()
    local privilege_cfg = CultivationWGData.Instance:GetCultivationPrivilegeCfg()

    -- self.node_list.desc_title_name.text.text = privilege_cfg.title

    -- local cap = CultivationWGData.Instance:GetCultivationCap()
    -- self.node_list.cap_value.text.text = cap

    local is_get_privilege =  CultivationWGData.Instance:IsGetPrivilege()
    local btn_desc = ""
    local is_get_today_reward = CultivationWGData.Instance:IsGetTodayReward()
    if is_get_privilege then
        btn_desc = is_get_today_reward and Language.Cultivation.ToDayRewardBtnState[1] or Language.Cultivation.ToDayRewardBtnState[0]
    else
        btn_desc = RoleWGData.GetPayMoneyStr(privilege_cfg.price, privilege_cfg.rmb_type, privilege_cfg.rmb_seq)
    end
    self.node_list.desc_btn_get_reward.text.text = btn_desc
    self.node_list.btn_get_reward_remind:CustomSetActive(is_get_privilege and not is_get_today_reward)
    self.node_list.btn_get_reward:CustomSetActive((not is_get_privilege) or (is_get_privilege and not is_get_today_reward))
    -- XUI.SetButtonEnabled(self.node_list.btn_get_reward, not is_get_privilege or (is_get_privilege and not is_get_today_reward))

    self.privilege_reward_list:SetDataList(privilege_cfg.daily_reward_item)
    self.privilege_reward_list:SetRefreshCallback(function(item_cell, cell_index)
        if item_cell then
            item_cell:SetLingQuVisible(is_get_today_reward)
        end
    end)
    self.node_list.img_receive:CustomSetActive(is_get_today_reward)

    if is_get_privilege then
        local server_time = TimeWGCtrl.Instance:GetServerTime()
        local privilege_time = CultivationWGData.Instance:GetPrivitegeEndTime()
        self.node_list.timer_panel:CustomSetActive(false)
        if privilege_time > server_time then
            self.node_list.act_timer_txt.text.text = ToColorStr(TimeUtil.FormatSecondDHM8(privilege_time - server_time), COLOR3B.GREEN)
            self.node_list.text_active_timer.text.text = TimeUtil.FormatTimeLanguage3(privilege_time - server_time)
            -- self.node_list.timer_panel:CustomSetActive(true)
            CountDownManager.Instance:AddCountDown("cultivation_privilege_time",
		    function (now_time, total_time)
			    if self.node_list.act_timer_txt then
				    local time = math.ceil(total_time - now_time)
				    self.node_list.act_timer_txt.text.text = ToColorStr(TimeUtil.FormatSecondDHM8(time), COLOR3B.GREEN)
                    self.node_list.text_active_timer.text.text = TimeUtil.FormatTimeLanguage3(time)

			    end
		    end,
		    function ()
			    if self.node_list.act_timer_txt then
				    self.node_list.act_timer_txt.text.text = ""
                    self.node_list.text_active_timer.text.text = Language.Cultivation.ActiveDay
					self.node_list.timer_panel:CustomSetActive(false)
			    end
		    end,
            privilege_time, nil, 1)
        end
    else
        if CountDownManager.Instance:HasCountDown("cultivation_privilege_time") then
            CountDownManager.Instance:RemoveCountDown("cultivation_privilege_time")
        end

        self.node_list.timer_panel:CustomSetActive(false)
    end

    self.node_list.text_acrive.text.text = is_get_privilege and Language.Cultivation.PrivilegeActiveStr[2] or Language.Cultivation.PrivilegeActiveStr[1]
end

function CultivationPrivilegeView:OnClickGetReward()
    local is_get_privilege =  CultivationWGData.Instance:IsGetPrivilege()
    local privilege_cfg = CultivationWGData.Instance:GetCultivationPrivilegeCfg()

    if is_get_privilege then
        local is_get_today_reward = CultivationWGData.Instance:IsGetTodayReward()

        if not is_get_today_reward then
            CultivationWGCtrl.Instance:ReceivePrivilegeReward()
        end
    else
        RechargeWGCtrl.Instance:Recharge(privilege_cfg.price, privilege_cfg.rmb_type, privilege_cfg.rmb_seq)
    end
end

function CultivationPrivilegeView:OnClickTipBtn()
    RuleTip.Instance:SetContent(Language.Cultivation.PrivilegeTipContent, Language.Cultivation.PrivilegeTipTitle)
end

-- function CultivationPrivilegeView:PlayTween()
-- 	if not self.tween then
-- 		local tween_root = self.node_list["mid_icon"].rect
--         tween_root.anchoredPosition = TOKEN_INIT_POS
-- 		self.tween = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + tween_up_height, 1)
-- 		self.tween:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
-- 	else
-- 		self.tween:Restart()
-- 	end
-- end

-- function CultivationPrivilegeView:CancelTween()
-- 	if self.tween then
--         self.tween:Kill()
--         self.tween = nil
--         local tween_root = self.node_list["mid_icon"]

--         if tween_root then
--             tween_root.rect.anchoredPosition = TOKEN_INIT_POS
--         end
-- 	end
-- end