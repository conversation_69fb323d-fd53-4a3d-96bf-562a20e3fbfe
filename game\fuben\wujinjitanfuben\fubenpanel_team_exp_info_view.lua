TeamExpFbInfoView = TeamExpFbInfoView or BaseClass(SafeBaseView)
-- 多人经验本信息面板

function TeamExpFbInfoView:__init()
	self:AddViewResource(0, "uis/view/main_ui_prefab", "layout_inspire")
	-- self:AddViewResource(0, "uis/view/team_exp_fb_prefab", "layout_exp_xiaolv")
	
	self.active_close = false
	self.view_layer = UiLayer.MainUI
	self.curr_wave_index = 0
	self.is_click_guwu = false
	self.is_click_medicine = false
	self.item_data_event = BindTool.Bind1(self.ItemDataChangeCallback, self)
	self.buy_double_linghun_state = 0
	self.btn_tween_list = {}
	self.view_cache_time = 2
	self.star_cacular = false
end

function TeamExpFbInfoView:__delete()
	self.start_timestamp = 0
	self.star_cacular = false
end

function TeamExpFbInfoView:LoadCallBack()
	-- self.node_list.layout_exp_xiaolv_root.rect.anchoredPosition3D = Vector3(0, 255, 0)
	if MainuiWGCtrl.Instance:IsLoadMainUiView() then
	 	self:InitCallBack()
	else
		self.mainui_open_complete_handle = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.InitCallBack, self))
	end
	self.start_timestamp = TimeWGCtrl.Instance:GetServerTime()

	self.star_cacular = false

	XUI.AddClickEventListener(self.node_list.additon_btn, BindTool.Bind(self.OnClickAdditonBtn,self))


end

function TeamExpFbInfoView:OnGuWuChange()
	self:Flush()
end

function TeamExpFbInfoView:InitCallBack()
	-- MainuiWGCtrl.Instance:SetTaskPanel(false,nil,nil)
	-- MainuiWGCtrl.Instance:SetTaskActive(false)
	self.task_panel_change = true
	-- MainuiWGCtrl.Instance:SetTaskPanel(false,nil,nil)

	self.end_time_bar = self.node_list.down_time
	-- self.layout_tower_end_time = self.node_list.layout_tower_end_time_node



	XUI.AddClickEventListener(self.node_list.btn_efficiency, BindTool.Bind1(self.OnClickOpenEfficiencyView, self))
	XUI.AddClickEventListener(self.node_list.btn_vip, BindTool.Bind1(self.OnClickVip, self))

	local fb_info = WuJinJiTanWGData.Instance:GetWuJinJiTanAllInfo()

	if fb_info.fuben_type == FUBEN_TYPE.LINGHUNGUANGCHANG then
		self.double_linghun_alert = Alert.New()
	end
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
	--加载信息面板挂在task面板上
	ResMgr:LoadGameobjSync("uis/view/team_exp_fb_prefab","layout_team_exp_info",
        function (obj)
			local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
            obj.transform:SetParent(parent.transform,false)
 
			obj = U3DObject(obj)
			self.info = TeamFuBenInfo.New(obj)
			self.next_boss_refresh_text = self.info:GetTextShowNumText()
			--self.info:AnchorPosition(151,-121.9)
			self.info:AnchorPosition(0, 0)
			self.info:SetLingHunZhiZhuTipsActive(true)
			self.info:SetRewardShow()
			self.load_info = true
			if self.need_flush then
				self.need_flush = false
				self:Flush()
			end
			
			local mainui_ctrl = MainuiWGCtrl.Instance
			mainui_ctrl:AddInitCallBack(nil, function()
				if Scene.Instance:GetSceneType() == SceneType.Wujinjitan or Scene.Instance:GetSceneType() == SceneType.LingHunGuangChang then
					mainui_ctrl:SetAutoGuaJi(true)
					-- mainui_ctrl:SetTaskAndTeamCallBack(nil, nil)
				end

				local mainui_ctrl = MainuiWGCtrl.Instance
				local parent = mainui_ctrl:GetPlayerInfoWidgetRoot()

				if self.node_list["layout_exp_xiaolv_root"] then
					self.obj = self.node_list["layout_exp_xiaolv_root"].gameObject
					self.obj.transform:SetParent(parent.gameObject.transform)
					self.obj.transform.localPosition = Vector3(0, 0, 0)
					self.obj.transform.localScale = Vector3.one
					self.obj.transform:SetAsFirstSibling()
				end
			
				if self.is_out_fb then
					self.obj:SetActive(false)
				end
			
				self.is_out_fb = nil

			end)
		end)
	if self.need_flush then
		self.need_flush = false
		self:Flush()
	end

	self.is_init = true
	--GlobalEventSystem:Fire(MainUIEventType.MAIN_TOP_ARROW_CLICK,ison)
	self.main_top_click = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind1(self.TopPanelAni, self))
	--self:StartBtnTween() --2019/9/27 屏蔽摇动动画

	self.effect_change = GlobalEventSystem:Bind(ObjectEventType.FIGHT_EFFECT_CHANGE, BindTool.Bind(self.OnEffectChange, self))
	self.fb_guwu_change = GlobalEventSystem:Bind(OtherEventType.FB_GUWU_CHANGE, BindTool.Bind(self.OnGuWuChange, self))
	--Vip信息改变
    self.vip_change_event = GlobalEventSystem:Bind(OtherEventType.VIP_INFO_CHANGE, BindTool.Bind(self.OnVipInfoChange, self))
	-- MainuiWGCtrl.Instance:AddBtnToGuajiLine(self.node_list.btn_efficiency)
	MainuiWGCtrl.Instance:AddBtnToFbIconGroup2Line(self.node_list.btn_vip)
	self:FlushVipIcon()
end

function TeamExpFbInfoView:OnEffectChange()
	self:Flush()
end

function TeamExpFbInfoView:OnVipInfoChange()
	if not self:IsLoadedIndex(0) then
		return
	end
    self:FlushVipIcon()
end

function TeamExpFbInfoView:OnClickAdditonBtn()
	ViewManager.Instance:Open(GuideModuleName.ExpAdditionView)
end

function TeamExpFbInfoView:TopPanelAni( ison )
	--print_error(ison)
	--local move_dis = ison and -1 or 1
	local max_move ,min_move = 0 , -173

	--self.node_list.root_layout_inspire:SetActive(not is_on)
	local move_vaule = ison == true and max_move or min_move
	if nil == self.node_list.root_layout_inspire then return end
	local tween = self.node_list.root_layout_inspire.rect:DOAnchorPosY(move_vaule, 1)
	tween:SetEase(DG.Tweening.Ease.OutBack)
	--tween:SetDelay(delay_time)
	tween:OnUpdate(function()
		self.node_list.root_layout_inspire.canvas_group.alpha = ((max_move - min_move) - (self.node_list.root_layout_inspire.rect.anchoredPosition.y - min_move)) / (max_move - min_move)
	end)
end

function TeamExpFbInfoView:OnClickAutoInspire()
	local other_cfg = WuJinJiTanWGData.GetOtherCfg()
	local per_gold_consume = other_cfg.gold_guwu_cost
	local per_coin_consume = other_cfg.coin_guwu_cost
	local main_role_vo = RoleWGData.Instance:GetRoleInfo()
	local all_gold = main_role_vo.gold + main_role_vo.bind_gold
	local all_coin = main_role_vo.coin
	local can_buy_gold_count = math.floor(all_gold/per_gold_consume)
	local can_buy_coin_count = math.floor(all_coin / per_coin_consume)
	can_buy_gold_count = math.floor(all_gold/per_gold_consume) <= 5 and math.floor(all_gold/per_gold_consume) or 5
	can_buy_coin_count = math.floor(all_coin / per_coin_consume) <= 5 and math.floor(all_coin / per_coin_consume) or 5
	if (can_buy_gold_count + can_buy_coin_count) <= 0 then return end
	FuBenWGCtrl.Instance:SendFbGuwu(-1,can_buy_gold_count,can_buy_coin_count)
end

-- --当前进度
-- function TeamExpFbInfoView:SetMonsterWave( str )
-- 	self.node_list.lbl_monster_wave.text.text = str
-- end

-- --个人杀怪
-- function TeamExpFbInfoView:SetKillNum( str )
-- 	self.node_list.lbl_kill_num.text.text = str
-- end

-- --当前获得经验
-- function TeamExpFbInfoView:GetExp( str )
-- 	self.node_list.lbl_get_exp.text.text = str
-- end

-- --鼓舞伤害
-- function TeamExpFbInfoView:AddHurt( str )
-- 	self.node_list.lbl_add_hurt.text.text = str
-- end

-- --经验加成
-- function TeamExpFbInfoView:AddExp( str )
-- 	self.node_list.lbl_add_exp.text.text = str
-- end

-- --团队经验加成
-- function TeamExpFbInfoView:TeamAddExp( str )
-- 	 self.node_list.lbl_team_add_exp.text.text = str
-- end

-- --每分钟的经验效率
-- function TeamExpFbInfoView:SetExpEta( str )
-- 	 self.node_list.lbl_add_exp_eta.text.text = str
-- end

function TeamExpFbInfoView:OnClickOpenGuWuView()
	-- local cfg = WuJinJiTanWGData.Instance:GetWuJinJiCfg().other[1]
	-- local role_info = WuJinJiTanWGData.Instance:GetWuJinJiTanRoleInfo()
	-- local add_hurt = cfg.guwu_add_per * (role_info.gold_guwu_count + role_info.coin_guwu_count)
	-- if add_hurt >= 100 then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.TeamExpFbAddHurt)
	-- else
		FuBenWGCtrl.Instance:OpenTeamExpCheerView()
	-- end
	self.is_click_guwu = true
end

function TeamExpFbInfoView:SetAddHurtBubbleVisit(is_visible)
	-- print_error("TeamExpFbInfoView:SetAddHurtBubbleVisit",is_visible)
end

function TeamExpFbInfoView:OnChangeLayoutInspire(state)
	-- print_error("TeamExpFbInfoView:OnChangeLayoutInspire",state)
end

function TeamExpFbInfoView:ForceBottomVisible()
	-- print_error("TeamExpFbInfoView:ForceBottomVisible")
end

function TeamExpFbInfoView:OnClickOpenEfficiencyView()
	FuBenWGCtrl.Instance:OpenTeamExpMdeicineView()
	self.is_click_medicine = true
end

function TeamExpFbInfoView:OnClickVip()
	local max = VipWGData.Instance:GetVipExpMaxAdd()
	local cur = VipWGData.Instance:GetCurExpAdd()
	local can_skip = WuJinJiTanWGData.Instance:GetIsShowArrowVip()
	if can_skip then
		if cur >= max then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.ExpAddition.VipMaxAddTip)
			return
		end

		--local tab_index = RechargeWGData.Instance:GetVIPDefultIndex()
		ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_vip)
		return
	end

end

function TeamExpFbInfoView:ReleaseCallBack()

	if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end

	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end

	if CountDownManager.Instance:HasCountDown("prepare_time") then
		CountDownManager.Instance:RemoveCountDown("prepare_time")
	end

	if self.times_timequest ~= nil then
		GlobalTimerQuest:CancelQuest(self.times_timequest)
		self.times_timequest = nil
	end
	if self.end_time_bar then
		self.end_time_bar = nil
	end
	self.next_linghun_time_stamp = nil

	-- if self.layout_tower_end_time then
	-- 	self.layout_tower_end_time = nil
	-- end
	if self.main_top_click then
		GlobalEventSystem:UnBind(self.main_top_click)
		self.main_top_click = nil
	end
	if self.effect_change then
		GlobalEventSystem:UnBind(self.effect_change)
		self.effect_change = nil
	end
	if self.fb_guwu_change then
		GlobalEventSystem:UnBind(self.fb_guwu_change)
		self.fb_guwu_change = nil
	end
	if self.vip_change_event then
		GlobalEventSystem:UnBind(self.vip_change_event)
		self.vip_change_event = nil
	end
	if self.mainui_open_complete_handle then
	 	GlobalEventSystem:UnBind(self.mainui_open_complete_handle)
	 end

	if self.info then
		local obj = self.info.node_list.layout_team_exp_info.gameObject
		self.info:DeleteMe()
		ResMgr:Destroy(obj)
		self.info = nil
	end
	if self.double_linghun_alert then
		self.double_linghun_alert:DeleteMe()
		self.double_linghun_alert = nil
	end
	if self.btn_tween_list then
		for k,v in pairs(self.btn_tween_list) do
			v:Kill()
			self.btn_tween_list[k] = nil
		end
		self.btn_tween_list = {}
	end
	self.next_boss_refresh_text = nil


	self.need_flush = false
	self.is_init = false
	self.load_info = false
	self.pre_exp_eta = nil
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
end

function TeamExpFbInfoView:ItemDataChangeCallback()
	--print_error("物品改变")
	self.is_item_change =true
	self:Flush()
end

function TeamExpFbInfoView:CloseCallBack(index)
	self.is_out_fb = true
    if self.obj then
        self.obj:SetActive(false)
    end

	if CountDownManager.Instance:HasCountDown("prepare_time") then
		CountDownManager.Instance:RemoveCountDown("prepare_time")
	end
	if self.double_linghun_alert then
		self.double_linghun_alert:Close()
	end

	MainuiWGCtrl.Instance:ResetTaskPanel()
	self.curr_wave_index = 0
	self.is_click_guwu = false
	self.is_click_medicine = false
	self.buy_double_linghun_state = 0
	if CountDownManager.Instance:HasCountDown("prepare_time_exp_fuben") then
		CountDownManager.Instance:RemoveCountDown("prepare_time_exp_fuben")
	end

	if self.info then
		local obj = self.info.node_list.layout_team_exp_info.gameObject
		self.info:DeleteMe()
		ResMgr:Destroy(obj)
		self.info = nil
	end
    if self.node_list and self.node_list.btn_efficiency then
        self.node_list.btn_efficiency.transform:SetParent(self.root_node_transform,false)
        self.node_list.btn_vip.transform:SetParent(self.root_node_transform,false)
    end
end

function TeamExpFbInfoView:ShowIndexCallBack(index)
	if self.obj then
        self.obj:SetActive(true)
    end

	self:Flush()
end

function TeamExpFbInfoView:OnFlush(param_list, index)
	if self:IsOpen() == false then
		return
	end
	if not self.is_init or not self.load_info then
		self.need_flush = true
		return
	end
	if not self.info then return end

	local cfg = WuJinJiTanWGData.Instance:GetWuJinJiCfg().other[1]
	local fb_info = WuJinJiTanWGData.Instance:GetWuJinJiTanAllInfo()
	local role_info = WuJinJiTanWGData.Instance:GetWuJinJiTanRoleInfo()
	if not role_info or IsEmptyTable(role_info) then return end
	if not fb_info or IsEmptyTable(fb_info) then return end
	--显示鼓舞text
	local gold_guwu_count = role_info.gold_guwu_count
	local coin_guwu_count = role_info.coin_guwu_count
	local count1,count2 = role_info.coin_guwu_count,role_info.gold_guwu_count
	local add_per1 = WuJinJiTanWGData.Instance:GetGuWuAddPer(count1)
	local add_per2 = WuJinJiTanWGData.Instance:GetGuWuAddPer(count2)
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.Guild_Invite then
		add_per1 = GuildInviteWGData.Instance:GetGuWuAddPer(count1)
		add_per2 = GuildInviteWGData.Instance:GetGuWuAddPer(count2)
	end

	self.buy_double_linghun_state = role_info.double_reward_count
	self:FlushLingHunInfo(fb_info,role_info)

	-- 新手第一次经验本波数会减少，优先读取服务器下发的数据
	local max_wave = fb_info.max_wave_count == 0 and cfg.wave or fb_info.max_wave_count 	
	self.info:SetMonsterWave(string.format(Language.FuBen.TeamExpWave, fb_info.curr_wave_index, max_wave))
	self.info:SetKillNum(fb_info.kill_monster_count)

	-- 改成显示铜币
	-- self.info:GetExp(role_info.fetch_exp)
	self.info:GetExp(role_info.fetch_coin)

	local add_hurt = cfg.guwu_add_per/100 * (role_info.gold_guwu_count + role_info.coin_guwu_count)

	self:FlushVipIcon()

	self.info:AddHurt(add_hurt .. "%")
	local now_level = GameVoManager.Instance:GetMainRoleVo().level
	self.info:LevelChange(role_info.first_enter_level, now_level)

	local role_num = fb_info.scene_role_num --因为机器人 可能没算在里面， 不用这个了
	local add_num = NewTeamWGData.Instance:GetExpAdd() * 10
	local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.LingHunGuangChang  --两个组队副本特殊处理满队伍加成
        or scene_type == SceneType.HIGH_TEAM_EQUIP_FB then
        add_num = 20
    end

	self.info:TeamAddExp(add_num  .. "%")

	local is_vip = VipWGData.Instance:IsVip()
	local guizu_exp = is_vip and VipWGData.Instance:GetCurExpAdd() or 0
	self.info:GuiZuAddExp(VipWGData.Instance:GetCurExpAdd() / 100 .. "%")

	local add_exp = 0
	local exp_buff = FightWGData.Instance:GetExpBuff()
	if exp_buff then
		add_exp = exp_buff.param_list[3] / 100
	end

	local is_zhuzhan = WuJinJiTanWGData.Instance:GetIsZhuZhan()

	-- 经验药水屏蔽
	-- if not self.is_click_medicine and not is_zhuzhan then 	--助战状态不弹经验药水使用
	-- 	local list = WuJinJiTanWGData.Instance:GetMedicineItemList()
	-- 	for i, v in ipairs(list) do
	-- 		-- local num = ItemWGData.Instance:GetItemNumInBagById(v)
	-- 		-- if num > 0 then
	-- 		-- 	self:OnClickOpenEfficiencyView()
	-- 		-- 	break
	-- 		-- end
	-- 		self:OnClickOpenEfficiencyView()
	-- 	end
	-- end

	self.info:AddExp(add_exp .. "%")
	if add_exp > 0 then
		self.node_list.jingYan_bg.gameObject:SetActive(true)
		self.node_list["btn_efficiency_icon"].animator:SetBool("shake",false)
		self.node_list["xiaolv_text"].text.text = string.format(Language.ExpAddition.VipAdd, add_exp)
		self.node_list.can_up_img:SetActive(false)
		self.is_click_medicine = true
	else
		self.node_list.jingYan_bg.gameObject:SetActive(false)
		self.node_list.can_up_img:SetActive(true)
		self.node_list["btn_efficiency_icon"].animator:SetBool("shake",true)
	end

	if 0 == fb_info.curr_wave_index then
		self:FlushPrepareTime()
	else
		self:FlushAni()
	end

	--print_error("是否助战 : ", is_zhuzhan)
	self.info:SetIsZhuZhan(is_zhuzhan, add_num, add_exp)
	if is_zhuzhan then
		local shengwang_value = WuJinJiTanWGData.Instance:GetZhuZhanShengWang()
		self.info:SetZhuZhanValue(shengwang_value)
	end

    -- self.node_list.btn_efficiency:SetActive(not is_zhuzhan)
	-- self.node_list.btn_vip:SetActive(not is_zhuzhan and not IS_AUDIT_VERSION)
	self.node_list.btn_vip:SetActive(false)


	--self.node_list.btn_efficiency:SetActive(not is_zhuzhan)
	-- if not self.first_load_complete and self.node_list["lbl_name_content"] then
	-- 	self.first_load_complete = true
	-- 	self.node_list["lbl_name_content"].rect.localPosition = Vector2(self.node_list["lbl_name_content"].rect.localPosition.x, 0)
	-- end

	if not self.star_cacular then
		local show_exp = fb_info.min_exp
		self.node_list["jingyanxiaolv"].text.text = CommonDataManager.ConverExp(math.ceil(show_exp))
	end

end

function TeamExpFbInfoView:FlushVipIcon()
	local vip_level = GameVoManager.Instance:GetMainRoleVo().vip_level
	-- XUI.SetGraphicGrey(self.node_list.vip_icon, vip_level == 0)
	local vip_add = VipWGData.Instance:GetCurExpAdd()
	local is_vip = VipWGData.Instance:IsVip()
	if is_vip then
		self.node_list.vip_text.text.text = string.format(Language.ExpAddition.VipAdd, vip_add / 100)
	end

	if self.info then
		-- self.info:UpdataVipExpAddInfo()
	end

	local max_add = VipWGData.Instance:GetVipExpMaxAdd()
	self.node_list.vip_bg:SetActive(is_vip and vip_add > 0)
	local is_show_arrow = WuJinJiTanWGData.Instance:GetIsShowArrowVip()
	self.node_list.arrow_vip:SetActive(is_show_arrow)
end

function TeamExpFbInfoView:FlushAni()
	local effect_flag = false
	local role_info = WuJinJiTanWGData.Instance:GetWuJinJiTanRoleInfo()
	local effect_list = FightWGData.Instance:GetMainRoleEffectList()--GetMainRoleShowEffect()
	for i,v in ipairs(effect_list) do
		if EFFECT_CLIENT_TYPE.ECT_ITEM_EXP1 == v.client_effect_type or EFFECT_CLIENT_TYPE.ECT_ITEM_EXP2 == v.client_effect_type
			or EFFECT_CLIENT_TYPE.ECT_ITEM_EXP3 == v.client_effect_type then -- 经验药水类buff
			effect_flag = true
		end
	end

	local remain_guwu_times = 10 - role_info.gold_guwu_count - role_info.coin_guwu_count
	local flag2 = remain_guwu_times == 0
	local flag3 = self.buy_double_linghun_state == 1


	self:FlushAction(flag2, effect_flag,flag3)
end

function TeamExpFbInfoView:FlushLingHunInfo(fb_info,role_info)
	if nil == self.next_boss_refresh_text.text then return end
	if nil ==  self.next_linghun_time_stamp or (self.next_linghun_time_stamp and self.next_linghun_time_stamp ~= fb_info.next_boss_refresh_timestamp)  then
		if CountDownManager.Instance:HasCountDown("prepare_time_exp_fuben") then
			CountDownManager.Instance:RemoveCountDown("prepare_time_exp_fuben")
		end
		self.next_linghun_time_stamp = fb_info.next_boss_refresh_timestamp
		self:RefreshBossUpdateCallBack(TimeWGCtrl.Instance:GetServerTime(), fb_info.next_boss_refresh_timestamp)

		if fb_info.next_boss_refresh_timestamp > TimeWGCtrl.Instance:GetServerTime() then
			CountDownManager.Instance:AddCountDown("prepare_time_exp_fuben", BindTool.Bind1(self.RefreshBossUpdateCallBack, self),
				BindTool.Bind1(self.RefreshBossCompleteCallBack, self),fb_info.next_boss_refresh_timestamp, nil, 0.5)
		end
	end
end
function TeamExpFbInfoView:RefreshBossUpdateCallBack(elapse_time, total_time)
	--self.end_time_bar:SetNumber(math.ceil(total_time - elapse_time))
	if nil == self.next_boss_refresh_text.text then return end
	local num = math.floor(total_time - elapse_time)
	if num > 0 then
		local format_time = TimeUtil.MSTime(num)
		local end_desc = string.format(Language.FuBenPanel.BuyDoubleLingHunTips1,format_time)
		self.next_boss_refresh_text.text = end_desc
	else
		self.next_boss_refresh_text.text = Language.FuBenPanel.BuyDoubleLingHunTips2
	end

end
function TeamExpFbInfoView:RefreshBossCompleteCallBack()
	if nil == self.next_boss_refresh_text.text then return end
	self.next_boss_refresh_text.text = Language.FuBenPanel.BuyDoubleLingHunTips2
end

function TeamExpFbInfoView:FlushAction(is_stop1, is_stop2, is_stop3)

end

function TeamExpFbInfoView:StartBtnTween()
	local btn_tween = function (trans)
		local sequence = DG.Tweening.DOTween.Sequence()
		sequence:Append(trans:DOLocalRotate(Vector3(0, 0, -60),0.2):SetEase(DG.Tweening.Ease.InOutQuad))
		sequence:Append(trans:DOLocalRotate(Vector3(0, 0, 60),0.4):SetEase(DG.Tweening.Ease.InOutQuad))
		sequence:Append(trans:DOLocalRotate(Vector3(0,0,0),0.2):SetEase(DG.Tweening.Ease.InOutQuad))
		sequence:AppendInterval(1)
		return sequence
	end

	self.btn_tween_list["btn_efficiency"] = btn_tween(self.node_list.btn_efficiency.transform):SetLoops(1000)
end

-- 准备倒计时
function TeamExpFbInfoView:FlushPrepareTime()
	local fb_info = WuJinJiTanWGData.Instance:GetWuJinJiTanAllInfo()
	-- UiInstanceMgr.Instance:ShowFBStartDown2(fb_info.prepare_end_timestamp,function()
	-- 	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	-- end)
	UiInstanceMgr.Instance:DoFBStartDown(fb_info.prepare_end_timestamp + 1, function()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		self:FlushAni()
	end)
	-- self:PreUpdateCallBack(TimeWGCtrl.Instance:GetServerTime(), fb_info.prepare_end_timestamp)
	-- CountDownManager.Instance:AddCountDown("prepare_time", BindTool.Bind1(self.PreUpdateCallBack, self), BindTool.Bind1(self.PreCompleteCallBack, self), fb_info.prepare_end_timestamp, nil, 1)
end

function TeamExpFbInfoView:PreUpdateCallBack(elapse_time, total_time)
	--self.end_time_bar:SetNumber(math.ceil(total_time - elapse_time))
	self.end_time_bar.text.text = math.ceil(total_time - elapse_time)
end


-- function TeamExpFbInfoView:IsLingHunGuangChang()
-- 	local linghunguangchang_level_limit = WuJinJiTanWGData.Instance:GetLingHunGuangChangLevelLimit()
-- 	local role_level = GameVoManager.Instance:GetMainRoleVo().level
-- 	return role_level >= linghunguangchang_level_limit
-- end







TeamFuBenInfo = TeamFuBenInfo or BaseClass(BaseRender)

function TeamFuBenInfo:LoadCallBack()
	self.node_list.lbl_get_exp.text.text = 0
end

function TeamFuBenInfo:__delete()
	self.old_exp_num = nil
end

--当前进度
function TeamFuBenInfo:SetMonsterWave( str )
	self.node_list.lbl_monster_wave.text.text = str
end

--组队杀怪
function TeamFuBenInfo:SetKillNum( str )
	self.node_list.lbl_kill_num.text.text = str
end

--当前获得经验
function TeamFuBenInfo:GetExp(num)
	if nil == self.old_exp_num then
		self.old_exp_num = 0
	end
	self.save_exp_num = num
	self:PlayExpAni()
end

function TeamFuBenInfo:PlayExpAni()
	if self.is_doing_ani then
		return
	end
	if self.old_exp_num >= self.save_exp_num then return end

	self.is_doing_ani = true

	local next_exp_num = self.save_exp_num
	local text_obj = self.node_list.lbl_get_exp:GetComponent(typeof(TMPro.TextMeshProUGUI))
	local complete_fun = function()
		self.old_exp_num = next_exp_num
		self.is_doing_ani = false
		self:PlayExpAni()
	end

	local update_fun = function(num)
		local value, postfix_name = CommonDataManager.ConverExpFBNum(num)
		if postfix_name == "" then
			text_obj.text = (string.format("%.0f", value)) .. postfix_name
		else
			text_obj.text = (value) .. postfix_name
		end
	end
	UITween.DONumberTo(text_obj, self.old_exp_num, next_exp_num, 0.5, update_fun, complete_fun)
	self.node_list.lbl_get_exp.transform:DOPunchScale(Vector3(0.1, 0.1, 0.1), 0.5)
end

--鼓舞伤害
function TeamFuBenInfo:LevelChange(num1, num2)
	local is_show_next = num2 > num1
	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(num1)
	local new_is_vis,new_role_level = RoleWGData.Instance:GetDianFengLevel(num2)
	self.node_list["dianfeng1"]:SetActive(is_vis)

	self.node_list.old_role_level.text.text = role_level
	self.node_list.new_role_level.text.text = new_role_level

	self.node_list.new_role_level:SetActive(is_show_next)
	self.node_list["next_level"]:SetActive(is_show_next)
	self.node_list["dianfeng2"]:SetActive(new_is_vis and is_show_next)
end

--等级改变
function TeamFuBenInfo:AddHurt( str )
	self.node_list.lbl_add_hurt.text.text = str
end



--经验加成
function TeamFuBenInfo:AddExp( str )
	self.node_list.lbl_add_exp.text.text = str
end

--团队经验加成
function TeamFuBenInfo:TeamAddExp( str )
	 self.node_list.lbl_team_add_exp.text.text = str
end

--贵族经验加成
function TeamFuBenInfo:GuiZuAddExp( str )
	 self.node_list.lbl_guizu_add_exp.text.text = str
end

--每分钟的经验效率
function TeamFuBenInfo:SetExpEta( str )
	 self.node_list.lbl_add_exp_eta.text.text = str
end

function TeamFuBenInfo:AnchorPosition( x,y )
	self.view.rect.anchoredPosition = Vector2(x,y)
end

function TeamFuBenInfo:SetLingHunZhiZhuTipsActive(active)
	self.node_list.linghunzhizhutips.gameObject:SetActive(active)
end
function TeamFuBenInfo:GetTextShowNumText()
	return self.node_list.linghunzhizhutips.text
end

function TeamFuBenInfo:SetRewardShow()
	-- 奖励显示
	local data_list = WuJinJiTanWGData.Instance:GetLingHunGuangChangCfg()
	local reward_list = data_list.other[1].reward_item
	local list = {}
	for i = 0, 10 do
		if reward_list[i] then
			table.insert(list, reward_list[i])
		else
			break
		end
	end
	self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_scroll)
	self.reward_list:SetDataList(list, 3)
end

function TeamFuBenInfo:SetIsZhuZhan(is_zhuzhan, exp_value, buff_value)
	-- self.node_list.jingyanjiacheng:SetActive(not is_zhuzhan)		--策划需求:列表不显示药水加成
	-- self.node_list.zuduijingyanjiacheng:SetActive(exp_value > 0)	--策划需求:助战状态也能显示组队加成,加成为0也显示
	-- self.node_list.jingyan:SetActive(not is_zhuzhan)             -- 策划需求:掉落铜币，不显示等级和经验
	self.node_list.zhuzhan_jiangli_shengwang:SetActive(is_zhuzhan)
	--WuJinJiTanWGData:GetZhuZhanShengWang()
end

function TeamFuBenInfo:SetZhuZhanValue(value)
	self.node_list.lbl_zhuzhan_shengwang.text.text = value
end

function TeamFuBenInfo:UpdataVipExpAddInfo()
	if self.node_list and self.node_list.vip_exp_add then
		local vip_level = GameVoManager.Instance:GetMainRoleVo().vip_level
		local vip_add = VipWGData.Instance:GetCurExpAdd()
		local is_vip = VipWGData.Instance:IsVip()

		if is_vip then
			self.node_list.vip_exp_add.text.text = string.format(Language.ExpAddition.VipExpAddStr, vip_add / 100)
			self.node_list.vip_exp_add:CustomSetActive(true)
		else
			self.node_list.vip_exp_add:CustomSetActive(false)
		end
	end
end