RoleBagWGData = RoleBagWGData or BaseClass()

function RoleBagWGData:__init()
	if RoleBagWGData.Instance then
		error("[RoleBagWGData] Attempt to create singleton twice!")
		return
	end
	RoleBagWGData.Instance = self

	self.shenlu_info = {
		shenlu_level = 0,
		shenlu_exp = 0,
	}
	self.select_seq_id  = -1
	self.flog_tab = {0,0,0,0,0,0,0}
	self.all_cell = {}

	self.menu_list = {}
	self.seq_list ={}
	self.longhun_info = {}
	self.melt_info = {melt_level = 0,melt_exp = 0}

	self.equip_melt_auto = ConfigManager.Instance:GetAutoConfig("equip_melt_auto")

	self.gurad_cfg = ConfigManager.Instance:GetAutoConfig("impguard_auto").imp
	local other_config_auto = ConfigManager.Instance:GetAutoConfig("other_config_auto")
	self.knapsack_other_cfg = other_config_auto.knapsack[1]
	self.bag_virtual_cfg = ListToMap(other_config_auto.equip_tips, "position_bag")

	self.chaijie_index = -1

	self.storge_bag_client_data_list = {}
	for i=0,COMMON_CONSTS.MAX_SRORGE_COUNT do
		self.storge_bag_client_data_list[i] = {}
	end

	RemindManager.Instance:Register(RemindName.BagBag, BindTool.Bind(self.GetBagBagRemindNum, self))				-- 背包标签红点
	RemindManager.Instance:Register(RemindName.BagSSS, BindTool.Bind(self.GetBagStarStrongStoneRemindNum, self))	-- 背包星级强化宝石红点
	RemindManager.Instance:Register(RemindName.BagStuff, BindTool.Bind(self.GetStuffRemind, self))					-- 背包材料红点
	self.rolebag_clean_flag = true

	self.change_callback = BindTool.Bind(self.OnRoleAttrValueChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.change_callback, {"level","prof", "gold", "bind_gold"})

	self:RegisterBagBagRemindInBag(RemindName.BagBag)
end

function RoleBagWGData:__delete()
	RoleBagWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.BagBag)
	RoleWGData.Instance:UnNotifyAttrChange(self.change_callback)
end

function RoleBagWGData:RegisterBagBagRemindInBag(remind_name)
    local map = {}

	-- 银票
    local yinpiao_cfg = self:GetYinPiaoExchangeCfg()
	for _, v in ipairs(yinpiao_cfg) do
		map[v.item_id] = true
	end

    local item_id_list = {}
    for k,v in pairs(map) do
        table.insert(item_id_list, k)
    end

    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function RoleBagWGData:GetKnapsackOtherCfg()
	return self.knapsack_other_cfg
end

function RoleBagWGData:OnRoleAttrValueChange( key, new_value, old_value)
	if key == "level" or key == "gold" or key == "bind_gold" then
		RemindManager.Instance:Fire(RemindName.BagBag)
	end
	if (key == 'level' or key == 'prof') and new_value ~= old_value then
		local data_list = ItemWGData.Instance:GetBagItemDataList()
		local equip_cfg_list = ItemWGData.Instance:GetEquipmentCfg()
		local update_list = {}
		for k, v in pairs(data_list) do
			if equip_cfg_list[v.item_id] then
				update_list[#update_list + 1] = v
			end
		end
		RoleBagWGCtrl.Instance:OpenDisposeListUpdate(update_list)
	end
end



function RoleBagWGData:SetRoleBagCleanFlag(status)
	self.rolebag_clean_flag = status
end

function RoleBagWGData:GetBagCleanFlag()
	return self.rolebag_clean_flag
end

function RoleBagWGData:SetShenluInfo(info)
	self.shenlu_info.shenlu_level = info.shenlu_level
	self.shenlu_info.shenlu_exp = info.shenlu_exp
end

function RoleBagWGData:GetShenluInfo()
	return self.shenlu_info
end

-- 获取银票兑换配置
function RoleBagWGData:GetYinPiaoExchangeCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto").yinpiao_exchange
	return cfg
end

function RoleBagWGData:GetShenluCfgByLevel(shenlu_level)
	local shenlu_level_cfg = ConfigManager.Instance:GetAutoConfig("shenluconfig_auto").shenlu_level_cfg
	for k,v in pairs(shenlu_level_cfg) do
		if shenlu_level == v.level then
			return v
		end
	end

	return nil
end

function RoleBagWGData:GetEquipExp(equip_level)
	local equip_exp_cfg = ConfigManager.Instance:GetAutoConfig("shenluconfig_auto").equip_exp_cfg
	for k,v in pairs(equip_exp_cfg) do
		if equip_level == v.equip_level then
			return v.add_exp
		end
	end
	return 0
end

-- 智能筛选可熔炼装备(最多8件)
function RoleBagWGData:GetCanForgeEquipList(is_recommend)
	local auto_forge_list = {}
	local index = 0

	local bag_item_list = ItemWGData.Instance:GetBagItemDataList()
	for k,v in pairs(bag_item_list) do
		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(v.item_id)
		if item_cfg and big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT and not EquipWGData.IsTreasureType(item_cfg.sub_type) and not EquipWGData.IsJLType(item_cfg.sub_type) then
			local is_good = is_recommend and EquipWGData.Instance:GetIsBetterEquip(v)
			if not is_good then
				local data = {index = v.index, item_id = v.item_id, is_bind = v.is_bind, num = v.num, limit_level = item_cfg.limit_level, sub_type = item_cfg.sub_type}
				auto_forge_list[index] = data
				index = index + 1

				if index >= 8 then
					break
				end
			end
		end
	end

	return auto_forge_list
end

-- 是否有可熔炼装备
function RoleBagWGData:IsCanForgeEquipList()
	local index = 0

	local bag_item_list = ItemWGData.Instance:GetBagItemDataList()
	for k,v in pairs(bag_item_list) do
		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(v.item_id)
		if item_cfg and big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT and not EquipWGData.IsTreasureType(item_cfg.sub_type) and not EquipWGData.IsJLType(item_cfg.sub_type) then
			local is_good = EquipWGData.Instance:GetIsBetterEquip(v)
			if not is_good then
				return true
			end
		end
	end

	return false
end

-- 判断是否是智能筛选装备
function RoleBagWGData:GetItemCanForge(item_id)
	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(item_id)
	if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT and not EquipWGData.IsTreasureType(item_cfg.sub_type) then
		local is_good = EquipWGData.Instance:GetIsBetterEquip(item_cfg)
		if not is_good then return true end
	end
	return false
end

------------------------------------------------熔炼

-- 获取熔炼阶数列表
function RoleBagWGData:GetBagEquipMeltShowGradeList()
	local open_cfg = self.equip_melt_auto.open_level
	local name_list = {}
	local role_level = RoleWGData.Instance:GetAttr("level")
	local max_length = #Language.Bag.NameList1
	local show_index
	for i, v in ipairs(Language.Bag.NameList1) do
		if open_cfg[max_length - i] and role_level >= open_cfg[max_length - i].role_level then
			table.insert(name_list, {name = v, index = i})
			if not show_index then
				show_index = i
			end
		end
	end

	show_index = show_index or max_length
	table.insert(name_list,{name = Language.Bag.NameList1[max_length], index = max_length})

	return name_list, show_index
end

--获取背包中所有紫色及以上的可回收装备
function RoleBagWGData:GetBagEquipMeltList()
	local bag_item_list = ItemWGData.Instance:GetBagItemDataList()
	local bag_list = {}
	local index = 0
	local function compose_data(data, cfg)
		local temp = {}
		for k, v in pairs(data) do
			temp[k] = v
		end
		local num = data.num or 1
		temp.color = cfg.color
		temp.order = cfg.order or 0
		temp.recyclget = cfg.recyclget * num
		temp.recycltype = cfg.recycltype
		temp.star_level = data.param and data.param.star_level or 0
		temp.sub_type = cfg.sub_type
		return temp
	end

	for k, v in pairs(bag_item_list) do
		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(v.item_id)
		if item_cfg and big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
			if item_cfg.color >= 1 and item_cfg.color <= 5 and item_cfg.recycltype == 2 and v.param and v.param.impression == 0 then
				if not EquipWGData.Instance:GetIsBetterEquip(v) then
					index = index + 1
					bag_list[index] = compose_data(v, item_cfg)
				end
			end
		elseif item_cfg and item_cfg.recycltype == 2 then
			index = index + 1
			bag_list[index] = compose_data(v, item_cfg)
		end
	end

	--排序：品质→星级→阶数→部位
	table.sort(bag_list, SortTools.KeyUpperSorters("color", "star_level", "order", "sub_type", "item_id"))
	return bag_list, index
end

--自动熔炼-获取背包种满足自动熔炼满足装备
function RoleBagWGData:GetBagEquipAutoMeltList()
	local bag_item_list = ItemWGData.Instance:GetBagItemDataList()
	local limit_color = GameEnum.EQUIP_COLOR_ORANGE
	local bag_list = {}
	local index = 0
	local function compose_data(data, cfg)
		local temp = {}
		for k,v in pairs(data) do
			temp[k] = v
		end
		local num = data.num or 1
		temp.color = cfg.color
		temp.order = cfg.order or 0
		temp.recyclget = cfg.recyclget * num
		temp.recycltype = cfg.recycltype
		temp.star_level = data.param and data.param.star_level or 0
		return temp
	end
	for k,v in pairs(bag_item_list) do
		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(v.item_id)
		if item_cfg and big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
			if (item_cfg.color >= 1 and item_cfg.color < limit_color or (item_cfg.color == limit_color and (v.param and v.param.star_level or 0) <= 1 )) 
				and item_cfg.recycltype == 2 and v.param and v.param.impression == 0 then
					if not EquipWGData.Instance:GetIsBetterEquip(v) then
						index = index + 1
						bag_list[index] = compose_data(v, item_cfg)
					end
			end
		end
	end
	return bag_list
end

--保存熔炼信息
function RoleBagWGData:SetEquipMeltInfo(protocol)
	self.melt_info.melt_level = protocol.melt_level
	self.melt_info.melt_exp = protocol.melt_exp
end

function RoleBagWGData:GetEquipMeltInfo()
	return self.melt_info
end

function RoleBagWGData:GetEquipMeltCfgByLevel(level)
	return self.equip_melt_auto.melt_level[level] or nil
end

function RoleBagWGData:GetEquipMelAttrListByLevel(level)
	local attr_list = {}
	local cfg = self:GetEquipMeltCfgByLevel(level)
	if IsEmptyTable(cfg) then
		return attr_list
	end

	-- 全属性加成
	local total_attr_per = cfg.attr_per / 10000
	local total_attr_data = {}
	total_attr_data.attr_str = Language.Bag.TunShiTotalAttrNameStr
	total_attr_data.attr_value = cfg.attr_per / 100 .. "%"
	total_attr_data.attr_sort = 999
	total_attr_data.is_special_attr = true
	
	if total_attr_per > 0 or level <= 0 then
		table.insert(attr_list, total_attr_data)
	end

	for i = 1, 6 do
		local data = {}
		data.attr_str = cfg["attr_".. i .."_type"] or ""
		data.attr_value = cfg["attr_".. i .."_val"] or 0
		data.attr_value = math.floor(data.attr_value * (1 + total_attr_per))
		data.attr_sort = AttributeMgr.GetSortAttributeIndex(data.attr_str)
		data.is_special_attr = false

		if data.attr_value > 0 or (i <= 4 and level <= 0) then
			table.insert(attr_list, data)
		end
	end

	if not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

	return attr_list
end

function RoleBagWGData:GetEquipMeltMaxLevel()
	local cfg_list = self.equip_melt_auto.melt_level
	return cfg_list[#cfg_list].melt_level
end

function RoleBagWGData:EquipMeltCfg()
	return self.equip_melt_auto
end

function RoleBagWGData:GetCellOpenNeedCount(index)
	if self.open_cell_cost == nil then   --开格子配置直接写在这
		self.open_cell_cost = {
			{min_extend_index = 0, need_item_count = 1},
			{min_extend_index = 35, need_item_count = 1},
			{min_extend_index = 45, need_item_count = 2},
			{min_extend_index = 50, need_item_count = 3},
			{min_extend_index = 55, need_item_count = 4},
			{min_extend_index = 60, need_item_count = 6},
			{min_extend_index = 65, need_item_count = 8},
			{min_extend_index = 70, need_item_count = 10},
			{min_extend_index = 75, need_item_count = 15},
			-- {min_extend_index = 80, need_item_count = 25},
		}
	end

	local len = #self.open_cell_cost
	for i=len, 1, -1 do
		if index >= self.open_cell_cost[i].min_extend_index then
			return self.open_cell_cost[i].need_item_count
		end
	end
	return 0
end


--------------------------------------------------------------
--一键出售界面
function RoleBagWGData:GetAutoSellGrid()
	local auto_sell_list = {}
	local key = 0
	local num = 0
	local bag_list = ItemWGData.Instance:GetBagItemDataList()
	for k,v in pairs(bag_list) do
		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(v.item_id)
		if item_cfg and big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT and item_cfg.color < 3 and v.item_id <= 8000 and not EquipWGData.IsSPType(big_type) then
			local index = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
			-- local equip_data = EquipWGData.Instance:GetGridData(index)
			-- local now_capability = EquipmentWGData.Instance:GetEquipPingfen(equip_data)
			-- local item_capability = EquipmentWGData.Instance:GetEquipPingfen(v)
			--if item_cfg.limit_prof ~= role_prof then
			if index ~= GameEnum.EQUIP_INDEX_XIANJIE and index ~= GameEnum.EQUIP_INDEX_XIANZHUO then
				auto_sell_list[key] = v
				key = key + 1
				num = num + 1
			end
			--end
		end
	end
	if key < 40 then
		for i=1,39 - #auto_sell_list do
			auto_sell_list[key] = {}
			key = key + 1
		end
	end
	-- key = key - 1
	if key % 8 ~= 0 then
		for i=1,8 - key % 8 do
			auto_sell_list[key] = {}
			key = key + 1
		end
	end
	return auto_sell_list,num
end


function RoleBagWGData.IsCanDecompose(item_id)
	for k,v in ipairs(ConfigManager.Instance:GetAutoConfig("decompose_auto").decompose) do
		if v.item_id == item_id then
			return true, v
		end
	end
	return false
end

function RoleBagWGData:GetCanDecompose(item_id)
	for k,v in ipairs(ConfigManager.Instance:GetAutoConfig("decompose_auto").decompose) do
		if v.item_id == item_id then
			return v
		end
	end
	return nil
end



----------------------------红点--------------------------------------
--功能提示
function RoleBagWGData:ShowBagMainuiIcon(module_tabindex,mainui_tip_type,flag)
	if flag then
		MainuiWGCtrl.Instance:InvateTip(mainui_tip_type,1,function ()
			FunOpen.Instance:OpenViewByName(GuideModuleName.Bag,module_tabindex)
			return true
		end)
	else
		MainuiWGCtrl.Instance:InvateTip(mainui_tip_type,0)
	end
end

function RoleBagWGData:GetGuradCfg()
	local role_level = RoleWGData.Instance.role_vo.level
	local cfg = {}
	for k,v in pairs(self.gurad_cfg) do
		if role_level >= v.level_limit then
			table.insert(cfg,v)
		end
	end
	table.sort(cfg, function(a, b) return a.imp_type > b.imp_type end )
	return cfg
end


function RoleBagWGData:GetBagStarStrongStoneRemindNum()
	local equip_star_remind = EquipWGData.Instance:GetStaActiveLevelRemind() or 0
    if equip_star_remind > 0 then
        return 1
    end

    return 0
end

function RoleBagWGData:GetBagBagRemindNum()
    if ItemWGData.Instance:GetIsHasQuickUseItem() then
    	return 1
    end

    -- if LongZhuWGData.Instance:IsShowLongZhuRedPoint() then
	-- 	return 1
	-- end
	
    if ItemWGData.Instance:GetHasShowGiftItemRed() then
		return 1
    end

    -- if HiddenWeaponWGData and HiddenWeaponWGData.Instance then
    --     local shenji_equip_remind = HiddenWeaponWGData.Instance.remind_manager:GetRemindCounts() or 0
    --     if shenji_equip_remind >0 then
    --         return 1
    --     end
    -- end

	return 0 -- EquipWGData.Instance:GetImpguardRemindEnable() and 1 or 0 	--屏蔽背包小鬼红点
end

-- 银票兑换红点
function RoleBagWGData:GetYinPiaoExchangeRemind()
	local open_flag = FunOpen.Instance:GetFunIsOpened(FunName.YinPiaoExchangeView)
	if not open_flag then
		return false
	end

	local cfg_list = self:GetYinPiaoExchangeCfg()
	local exchange_total = 0
	-- 银票总数
	for i,v in ipairs(cfg_list) do
		local num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
		exchange_total = exchange_total + num*v.exchange_num
	end

	return exchange_total >= 200
end

-- 背包材料红点
function RoleBagWGData:GetStuffRemind()
	
	return 0
end

function RoleBagWGData:GetBagOrInventoryIsExistById(item_id)
	for k,v in pairs(ItemWGData.Instance:GetBagItemDataList()) do 		--背包中的信息
		if v.item_id > 0 then
			if v.item_id == item_id then
				return true
			end
		end
	end

	local guard_info = EquipWGData.Instance:GetmpGuardInfo()		--装备格子上的信息

	for i=1,2 do
		local data = guard_info.item_wrapper[i]
		if data and data.item_id and data.item_id > 0 then
			if data.item_id == item_id then
				return true
			end
		end
	end
	return false
end

-- 获取背包虚拟道具显示
function RoleBagWGData:GetBagVirtualItem(pos)
	local item_data
	local cfg = self.bag_virtual_cfg and self.bag_virtual_cfg[pos]
	if not cfg then
		return item_data
	end

	local xianpin_type_list = Split(cfg.xianpin_type_list,'|')
	for i,v in ipairs(xianpin_type_list) do
		xianpin_type_list[i] = tonumber(v)
	end

	item_data = {item_id = cfg.item_ID, param = {star_level = cfg.star_level, xianpin_type_list = xianpin_type_list},
		star_level = cfg.star_level}
	return item_data
end


--获取物品可以开启多少背包格子
function RoleBagWGData:GetCanOpenHowManySlot(storage_type, num)
	local now_index = 0
	local max_gird_num = 0

	if storage_type == GameEnum.STORAGER_TYPE_BAG then
		now_index = ItemWGData.Instance.max_knapsack_valid_num
		max_gird_num = GameEnum.ROLE_BAG_SLOT_NUM
	elseif storage_type == GameEnum.STORAGER_TYPE_STORAGER then
		now_index =  ItemWGData.Instance.max_storage_valid_num
		max_gird_num = GameEnum.STORAGER_SLOT_NUM
	end

	if now_index == max_gird_num then
		return -1, 0, 0
	end

	local need_number = 0
	local can_open_num = 0
	local old_need_num = 0
	local consume_num = RoleBagWGData.Instance:GetKnapsackOtherCfg().consume_num or 0
	for try_index = now_index, max_gird_num - 1 do
		old_need_num = need_number
		need_number = need_number + consume_num -- open_one_need
		can_open_num = can_open_num + 1

		if need_number > num then
			return can_open_num - 1, need_number, old_need_num
		end
	end

	return can_open_num, need_number, need_number
end

function RoleBagWGData:GetSpecialBagItemResloveCfg(item_id)
	return ConfigManager.Instance:GetAutoConfig("compose_auto").item_decompose[item_id]
end

