--骑宠装备背包
MountLingChongEquipView = MountLingChongEquipView or BaseClass(SafeBaseView)


function MountLingChongEquipView:InitBagView()
    if not self.bag_grid then
        self.bag_grid = AsyncBaseGrid.New()
        self.bag_grid:SetStartZeroIndex(false)
		self.bag_grid:SetIsShowTips(false)                       --  ,change_cells_num = 2
		self.bag_grid:CreateCells({col = 4, cell_count = 200, list_view = self.node_list["ph_bag_grid"], itemRender = EquipBagRender})
		self.bag_grid:SetSelectCallBack(BindTool.Bind1(self.SelectBagCellCallBack, self))
    end
    self.node_list["btn_bag_sort"].button:AddClickListener(BindTool.Bind(self.OnClickSortBag, self))
    self.node_list["onekey_wearing"].button:AddClickListener(BindTool.Bind(self.OnClickOnekeyWearing, self))
end

function MountLingChongEquipView:OnClickOnekeyWearing()
    MountLingchongEquipWGCtrl.Instance:SendMountPetEquipReq(self.cur_show_type,
    MOUNT_PET_EQUIP_OPERA_TYPE.MOUNT_PET_EQUIP_OPER_TYPE_ONE_KEY_PUTON) --请求整理
end

function MountLingChongEquipView:OnClickSortBag()
    if self.arrange_time then return end

    CountDownManager.Instance:AddCountDown('MountLingChongEquipView_BagCleanup',function (time,total_time)
        time = math.modf(time)
        self.node_list.cleanup_txt.text.text = total_time - time
    end,
    function ()
        self.node_list.cleanup_txt.text.text = Language.Bag.CleanUp_1
        self.arrange_time = false
        XUI.SetButtonEnabled(self.node_list.btn_bag_sort,true)
        CountDownManager.Instance:RemoveCountDown('MountLingChongEquipView_BagCleanup')
    end, nil, 3, 1)

    self.node_list.cleanup_txt.text.text = 3
    XUI.SetButtonEnabled(self.node_list.btn_bag_sort,false)
    self.arrange_time = true
    MountLingchongEquipWGCtrl.Instance:SendMountPetEquipReq(self.cur_show_type, 
    MOUNT_PET_EQUIP_OPERA_TYPE.MOUNT_PET_EQUIP_OPER_TYPE_REORDER_BAG) --请求整理
    self.bag_grid:JumpToIndex(1)
end

function MountLingChongEquipView:SelectBagCellCallBack(cell)
	if nil == cell then
		return
	end

	local cell_data = cell:GetData()
	if nil == cell_data or not next(cell_data) then
		return
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(cell_data.item_id)
    local btn_callback_event = {}
    btn_callback_event[1] = {}
    btn_callback_event[1].btn_text = Language.MountPetEquip.Wear
    btn_callback_event[1].callback = function ()
        MountLingchongEquipWGCtrl.Instance:SendMountPetEquipReq(self.cur_show_type, 
        MOUNT_PET_EQUIP_OPERA_TYPE.MOUNT_PET_EQUIP_OPER_TYPE_PUT_ON, cell_data.grid_index) --请求穿戴
    end
	TipWGCtrl.Instance:OpenItem(cell_data, ItemTip.FROM_BAG, nil, nil, btn_callback_event)				--打开tip,提示使用

end

function MountLingChongEquipView:DeleteBagView()
    if nil ~= self.bag_grid then
		self.bag_grid:DeleteMe()
		self.bag_grid = nil
    end

    self.arrange_time = nil
    if CountDownManager.Instance:HasCountDown('MountLingChongEquipView_BagCleanup') then
		CountDownManager.Instance:RemoveCountDown('MountLingChongEquipView_BagCleanup')
	end
end

function MountLingChongEquipView:FlushBagView()
    self.bag_grid:CancleAllSelectCell()
    local data_list = MountLingChongEquipWGData.Instance:GetBagItemDataList(self.cur_show_type)
    self.bag_grid:SetDataList(data_list)
    
    local remind = MountLingChongEquipWGData.Instance:EquipBagRemind(self.cur_show_type) == 1
    self.node_list.wear_remind:SetActive(remind)
end

EquipBagRender = EquipBagRender or BaseClass(ItemCell)
-- 是否忽略点击
function EquipBagRender:IsIgnoreCKStorgeClick()
	return IsEmptyTable(self.data) or self.data.item_id == nil or self.data.item_id == 0
end

function EquipBagRender:__init()
	self.need_default_eff = true
end

function EquipBagRender:OnFlush()
    ItemCell.OnFlush(self)
    local is_up = MountLingChongEquipWGData.Instance:IsBetterThanCurWearing(self.data.show_type, self.data.item_id)
    self:SetUpFlagIconVisible(is_up)
end

function EquipBagRender:OnClick()
    if self:IsIgnoreCKStorgeClick() then
		return
	end
	if self.data and self.is_showtip then
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		if nil == item_cfg then return end
        TipWGData.Instance:SetDefShowBuyCount(self.show_buy_num or 1)
		TipWGCtrl.Instance:OpenItem(self.data, ItemTip.FROM_MOUNTEQUIP_BAG, nil)
    end
    
    
	BaseRender.OnClick(self)
end