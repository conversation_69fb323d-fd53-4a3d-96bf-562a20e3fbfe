require("game/vip/viptip_view")
require("game/vip/vip_wg_data")
require("game/vip/vip_power")
require("game/recharge/recharge_vip_tiyan_view")
require("game/recharge/recharge_baozhu_active_view")
require("game/recharge/recharge_vip_up_view")
require("game/recharge/vip_free_tips")
require("game/recharge/vip_zero_buy_render")
require("game/vip/viptip_xufei_view")
require("game/vip/vip_renew_view")
require("game/vip/vip_desc_info_view")
require("game/recharge/vip_daily_gift_show_view")
require("game/recharge/recharge_vip_Initial_view")
require("game/recharge/recharge_vip_show_initial_view")

-- VIP
VipWGCtrl = VipWGCtrl or BaseClass(BaseWGCtrl)

function VipWGCtrl:__init()
	if VipWGCtrl.Instance ~= nil then
		ErrorLog("[VipWGCtrl] Attemp to create a singleton twice !")
	end
	VipWGCtrl.Instance = self

	self.vip_data = VipWGData.New()
	self.vip_power = VipPower.New()
	self.viptip_view = VipTipView.New(GuideModuleName.VipTip)
	self.vip_tiyan_view = VipTiYanView.New(GuideModuleName.VipTiYanView)
	self.vip_active_view = VipActiveView.New(GuideModuleName.VipActiveView)
	self.vip_up_view = VipUpView.New(GuideModuleName.VipUpView)
	self.vip_initial_view = VipInitialView.New(GuideModuleName.VipInitialView)
	self.vip_show_initial_view = VipShowInitialView.New()
	self.vip_renew_view = VipRenewView.New()
	self.vip_desc_info_view = VipDescInfoView.New()
	self.vip_daily_gift_show_view = VipDailyGiftShowView.New()
	self.viptip_xufei_view = VipTipXuFeiView.New(GuideModuleName.VipTipXuFei)

	self:RegisterAllProtocols()
end

function VipWGCtrl:__delete()
	self.vip_data:DeleteMe()
	self.vip_data = nil

	self.vip_power:DeleteMe()
	self.vip_power = nil

	self.viptip_view:DeleteMe()
	self.viptip_view = nil
	
	self.vip_tiyan_view:DeleteMe()
	self.vip_tiyan_view = nil
	
	self.vip_active_view:DeleteMe()
	self.vip_active_view = nil

	self.vip_up_view:DeleteMe()
	self.vip_up_view = nil

	self.vip_initial_view:DeleteMe()
	self.vip_initial_view = nil

	self.vip_show_initial_view:DeleteMe()
	self.vip_show_initial_view = nil

	self.vip_renew_view:DeleteMe()
	self.vip_renew_view = nil

	self.vip_desc_info_view:DeleteMe()
	self.vip_desc_info_view = nil

	self.viptip_xufei_view:DeleteMe()
	self.viptip_xufei_view = nil

	self.vip_daily_gift_show_view:DeleteMe()
	self.vip_daily_gift_show_view = nil

	VipWGCtrl.Instance = nil
	self:CancelShowCountDown()
end

function VipWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCVipInfo, "OnVipInfo")
	self:RegisterProtocol(SCRoleVipExpChange,"OnSCRoleVipExpChange")
	self:RegisterProtocol(SCVipCardInfo, "OnSCVipCardInfo")
	self:RegisterProtocol(SCVipFetchGiftInfo, "OnSCVipFetchGiftInfo")

	self:RegisterProtocol(CSFetchVipLevelReward)
	self:RegisterProtocol(CSOnKeyFetchVipWeekReward)
	self:RegisterProtocol(CSBuyVipTimeCard)
	self:RegisterProtocol(CSGetVipWelfare)--周礼包2765
	self:RegisterProtocol(CSBuyVIPUp)
	self:RegisterProtocol(CSTiYanVip)
	self:RegisterProtocol(CSBuyVipGiftBag)
end

--打开体验VIP
function VipWGCtrl:OpenVipTrialPanel()
	if not self.vip_tiyan_view:IsOpen() then
		self.vip_tiyan_view:Open()
	end
end

-- 打开vip激活界面
function VipWGCtrl:OpenVipActiveView()
	-- self.vip_active_view:Open()
	self:OpenVipUpView()
end

-- 打开vip升级界面
function VipWGCtrl:OpenVipUpView()
	if not self.vip_up_view:IsOpen() then
		self.vip_up_view:Open()
	end
end

function VipWGCtrl:OpenVipInitialView()
	if not self.vip_initial_view:IsOpen() then
		self.vip_initial_view:Open()
	end
end

-- 打开vip初始化升级界面
function VipWGCtrl:OpenVipShowInitialView()
	local old_vip_level, vip_level = self.vip_data:GetVipInitialInfo()
	local result_initial_flag = self.vip_data:GetVipInitialFlag()
	local seq = old_vip_level + 1
	if result_initial_flag and old_vip_level < vip_level then
		self:CancelShowCountDown()
	    self.show_initial_quest = GlobalTimerQuest:AddRunQuest(function ()
	        if seq <= vip_level then
	            self.vip_show_initial_view:SetDataAndOpen(seq)
	            seq = seq + 1
	        	if seq > vip_level then
	        		self.vip_data:SetVipInitialFlag(false)
	        	end
	        end
		end, 0.2)
	end
end

function VipWGCtrl:CancelShowCountDown()
	if self.show_initial_quest ~= nil then
		GlobalTimerQuest:CancelQuest(self.show_initial_quest)
		self.show_initial_quest = nil
	end
end

-- 仙玉不足
function VipWGCtrl:OpenTipNoGold()
	self:OpenVipTipsView("no_gold", {is_xufei = false})

	--弹窗检测是否需要增加礼包直购
	LimitTimeGiftWGCtrl.Instance:CheckNoGoldPopupGift()
	LimitTimeGiftWGCtrl.Instance:CheckNoGoldPopupGift2()
	DiscountPurchaseWGCtrl.Instance:CheckNoGoldPopupGift()
end

-- 烟雨灵石不足
function VipWGCtrl:OpenTipNoScore()
	self:OpenVipTipsView("no_score", {is_xufei = false})

	--弹窗检测是否需要增加礼包直购
	LimitTimeGiftWGCtrl.Instance:CheckNoGoldPopupGift()
	LimitTimeGiftWGCtrl.Instance:CheckNoGoldPopupGift2()
	DiscountPurchaseWGCtrl.Instance:CheckNoGoldPopupGift()
end

-- VIP过期续费
function VipWGCtrl:OpenInvalidView()
	self:OpenVipTipsView("is_xufei", {is_xufei = true})
end

function VipWGCtrl:OpenVipTipsView(key, param)
	self.viptip_view:Open()
	if key and not IsEmptyTable(param) then
		self.viptip_view:Flush(0, key, param)
	end
end

-- vip续费
function VipWGCtrl:OpenvipTipXuFeiView(param)
	ViewManager.Instance:Open(GuideModuleName.VipTipXuFei, nil, "xufei", param )
end

-- VIP续费(可以买三种卡的界面)
function VipWGCtrl:OpenVipRenewView()
	self.vip_renew_view:Open()
end
function VipWGCtrl:OpenVipDescInfoView(level)
	self.vip_desc_info_view:SetVipLevel(level)
	if self.vip_desc_info_view:IsOpen() then
        self.vip_desc_info_view:Flush()
    else
    	self.vip_desc_info_view:Open()
    end
end

function VipWGCtrl:CloseVipRenewView()
	self.vip_renew_view:Close()
end

--请求体验VIP
function VipWGCtrl:CSTiYanVip()
	local protocol = ProtocolPool.Instance:GetProtocol(CSTiYanVip)
	protocol:EncodeAndSend()
end

-- 购买VIP专属礼包
function VipWGCtrl:CSBuyVipGiftBag(vip_index)
	local protocol = ProtocolPool.Instance:GetProtocol(CSBuyVipGiftBag)
	protocol.vip_index = vip_index 
	protocol:EncodeAndSend()
end

--发送领取奖励申请
function VipWGCtrl:SendFetchVipLevelRewardReq(seq)
	local protocol = ProtocolPool.Instance:GetProtocol(CSFetchVipLevelReward)
	protocol.seq = seq - 1					--C++从0开始
	protocol:EncodeAndSend()
end
--发送周礼包领取奖励申请（之前的暂时先不管） 2788
function VipWGCtrl:SendFetchVipWeekReward(seq)
	local protocol = ProtocolPool.Instance:GetProtocol(CSOnKeyFetchVipWeekReward)
	protocol:EncodeAndSend()
end
--每周礼包申请2765
function VipWGCtrl:SendVipWeekReward(type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetVipWelfare)
	protocol.type = type
	protocol:EncodeAndSend()
end

--发送购买月卡申请
function VipWGCtrl:SendBuyVipTimeCard(opera_type, card_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSBuyVipTimeCard)
	protocol.opera_type = opera_type or 0
	protocol.card_type = card_type or 0
	protocol:EncodeAndSend()
end

--直升vip4
function VipWGCtrl:SendUpVIPFour()
	local protocol = ProtocolPool.Instance:GetProtocol(CSBuyVIPUp)
	protocol:EncodeAndSend()
end

function VipWGCtrl:OnSCRoleVipExpChange(protocol)
	self.vip_data:SetRechargeExp(protocol.vip_exp,protocol.vip_level,false,protocol.vip_exp_change_type,protocol.vip_exp_add_value)
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function ()
		MainuiWGCtrl.Instance.view:Flush(0, "recharge_vip")
	end)
	GlobalEventSystem:Fire(OtherEventType.VIP_INFO_CHANGE)
end

-- Vip卡信息
function VipWGCtrl:OnSCVipCardInfo(protocol)
	self.vip_data:SetVipCardInfo(protocol)
	RechargeWGCtrl.Instance:Flush(TabIndex.recharge_vip)
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function ()
		MainuiWGCtrl.Instance.view:Flush(0, "recharge_vip")
	end)
	RemindManager.Instance:Fire(RemindName.Vip)
	RemindManager.Instance:Fire(RemindName.Vip_Col)
	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		main_role:SetAttr("vip_free_card")
	end
end

-- Vip每日礼包领取信息
function VipWGCtrl:OnSCVipFetchGiftInfo(protocol)
	self.vip_data:SetVipDailyGiftInfo(protocol)
	RechargeWGCtrl.Instance:Flush(TabIndex.recharge_vip, "daily_gift_info")
	RemindManager.Instance:Fire(RemindName.Vip_Col)--VIP横向按钮
end

--vip等级信息变化
function VipWGCtrl:OnVipInfo(protocol)
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()	
	if main_role_vo.obj_id == protocol.obj_id then
		RoleWGData.Instance:SetAttr("vip_level", protocol.vip_level)
		
		--领取信息
		self.vip_data.fetch_flag = bit:d2b(protocol.fetch_level_reward_flag)
		self.vip_data:SetRechargeExp(protocol.vip_exp,protocol.vip_level,true,-1,-1) --第一次赋值
		self.vip_data.fetch_week_flag = bit:d2b(protocol.fetch_week_reward_flag)
		self.vip_data.fetch_daily_flag = bit:d2b(protocol.fetch_daily_reward_flag)
		self.vip_data:SetVipEndTime(protocol.vip_end_timestamp, protocol.vip_time_card_type)
		self.vip_data:SetRoleVipPastLevel(protocol.vip_level_on_card_timeout)
		self.vip_data:SetRoleVipEndTime(protocol.vip_end_timestamp)
		self.vip_data:SetVipBuyFlag(protocol.vip_giftbag_flag)
		self.vip_data:SetIsFetchInitialVip(protocol.is_fetch_initial_vip)

		self.vip_data:CheckVipUpLevel()

		QiFuWGCtrl.Instance:OnVipInfo(protocol)
		FunOpen.Instance:CheckVipConditionOnAcceptData()
		-- RemindManager.Instance:Fire(RemindName.Week_Buy)
		GlobalEventSystem:Fire(OtherEventType.VIP_INFO_CHANGE)
	end

	local obj = Scene.Instance:GetObjectByObjId(protocol.obj_id)
	if obj ~= nil and obj:IsRole() then
		obj:SetAttr("vip_level", protocol.vip_level)
	end
	RechargeWGCtrl.Instance:Flush(TabIndex.recharge_vip)

	self.exp_qifu_level = protocol.exp_qifu_level
	RemindManager.Instance:Fire(RemindName.Vip)--VIP主界面按钮

	if WelfareWGCtrl.Instance.view:IsOpen() then
		WelfareWGCtrl.Instance:Flush(WELFARE_TYPE.QIFU)
	end

	MainuiWGCtrl.Instance:FlushView(0,"recharge_vip")
end

function VipWGCtrl:GetQiFuLevel()
	return self.exp_qifu_level
end

function VipWGCtrl:BuyAndUseVipCard(seq)
	local card_cfg = self.vip_data:GetVipCardCfg(seq)
	if IsEmptyTable(card_cfg) then
		return
	end
	local shop_cfg = ShopWGData.Instance:GetShopCfgItemId(card_cfg.item_id)
	if shop_cfg then
		local gold = RoleWGData.Instance:GetAttr('gold')
	    if gold < shop_cfg.price then
			self:OpenTipNoGold()
	    	return
	    end
	    if shop_cfg.price >= COMMON_CONSTS.AlertConst then
	    	TipWGCtrl.Instance:OpenAlertTips(string.format(Language.Common.CommonAlertFormat3, shop_cfg.price, card_cfg.name), function ()
				ShopWGCtrl.Instance:SendShopBuy(card_cfg.item_id, 1, 1, 0, shop_cfg.seq)
				self.vip_renew_view:Close()
			end)
	    else
	    	ShopWGCtrl.Instance:SendShopBuy(card_cfg.item_id, 1, 1, 0, shop_cfg.seq)
	    	self.vip_renew_view:Close()
	    end
	end
end

function VipWGCtrl:ShowVipDailyGift(data_list, is_gray, call_back)
	self.vip_daily_gift_show_view:Flush(0, "gift_info", {data_list = data_list, is_gray = is_gray, call_back = call_back})
	self.vip_daily_gift_show_view:Open()
end

-- vip直升初始化操作
function VipWGCtrl:OnInintialVipResult(result, old_vip_level, vip_level)
	if result > 0 then
		self.vip_data:SetVipInitialFlag(result > 0)
		self.vip_data:SetVipInitialInfo(old_vip_level, vip_level)
		self:OpenVipShowInitialView()
	end
end
