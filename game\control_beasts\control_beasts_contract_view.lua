function ControlBeastsWGView:ReleaseBeastsContractCallBack()
    if self.contract_reward_item then
		self.contract_reward_item:DeleteMe()
        self.contract_reward_item = nil
	end

	if self.contract_display_list and #self.contract_display_list > 0 then
		for _, display_cell in ipairs(self.contract_display_list) do
			display_cell:DeleteMe()
			display_cell = nil
		end

		self.contract_display_list = nil
	end

	self.contract_model_name_list = nil
	self.curr_contract_show = nil
	self.contract_star_list = nil
end 

function ControlBeastsWGView:LoadContractViewCallBack()
    if not self.contract_display_list then
		self.contract_display_list = {}
		self.contract_model_name_list = {}

		for i = 1, 3 do
			self.contract_display_list[i] = RoleModel.New()
			local str = string.format("contract_model_display%d", i)

			local display_data = {
				parent_node = self.node_list[str],
				camera_type = MODEL_CAMERA_TYPE.BASE,
				-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
				rt_scale_type = ModelRTSCaleType.S,
				can_drag = true,
			}
	
			self.contract_display_list[i]:SetRenderTexUI3DModel(display_data)
			-- self.contract_display_list[i]:SetUI3DModel(self.node_list[str].transform, self.node_list[str].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
			self:AddUiRoleModel(self.contract_display_list[i])

			self.contract_model_name_list[i] = {}
			self.contract_model_name_list[i].contract_active_jingshen_img = self.node_list[string.format("contract_active_jingshen_img_%d", i)]
			self.contract_model_name_list[i].contract_beasts_name = self.node_list[string.format("contract_beasts_name_%d", i)]
			self.contract_model_name_list[i].contract_star_list = {}

			for j = 1, 5 do
				self.contract_model_name_list[i].contract_star_list[j] = self.node_list[string.format("contract_star%d_%d", j, i)]
			end
		end
	end

    if not self.contract_reward_item then
        self.contract_reward_item = ItemCell.New(self.node_list.contract_reward_item)
    end

	if not self.contract_star_list then
		self.contract_star_list = {}
		for i = 1,5 do
			self.contract_star_list[i] = self.node_list["contract_star" .. i]
		end
	end

    XUI.AddClickEventListener(self.node_list.contract_hand_book_btn, BindTool.Bind(self.LookHandBook, self))                  	-- 图鉴
    XUI.AddClickEventListener(self.node_list.contract_preview_btn, BindTool.Bind(self.LookPreView, self))                  		-- 预览
    XUI.AddClickEventListener(self.node_list.operate_one_btn, BindTool.Bind2(self.OperateContractBtn, self, 1))                 -- 结缘1次
    XUI.AddClickEventListener(self.node_list.operate_ten_btn, BindTool.Bind2(self.OperateContractBtn, self, 2))                 -- 结缘10次
	XUI.AddClickEventListener(self.node_list.operate_one_const_root, BindTool.Bind(self.ShowOperateItemBtn, self))          -- 结缘物品展示
	XUI.AddClickEventListener(self.node_list.operate_ten_const_root, BindTool.Bind(self.ShowOperateItemBtn, self))          -- 结缘物品展示
end

function ControlBeastsWGView:OpenContractViewCallBack()

end

function ControlBeastsWGView:CloseContractViewCallBack()

end

function ControlBeastsWGView:ShowContractViewCallBack()

end

function ControlBeastsWGView:FlushContractViewCallBack(param_t)
	if not self.contract_level then
		self.contract_level = ControlBeastsContractWGData.Instance:GetBeastDrawDeedLevel()
	end

	if not self.contract_server_level then
		self.contract_server_level = ControlBeastsContractWGData.Instance:GetBeastDrawDeedLevel()
	else
		if self.contract_server_level ~= ControlBeastsContractWGData.Instance:GetBeastDrawDeedLevel()
		and self.contract_server_level < ControlBeastsContractWGData.Instance:GetBeastDrawDeedLevel() then
			ControlBeastsContractWGCtrl.Instance:OpenBeastsContractLvUpView()
		end

		self.contract_server_level = ControlBeastsContractWGData.Instance:GetBeastDrawDeedLevel()
	end

    self.contract_list = nil
	self:FlushContractRewardList()
	self:FlushContractRewardOperateMode()
	self:FlushContractCenterMessage()
	-- self:FlushContractSelectHl()
	-- self:FlushRewardModel()
end

-- 刷新操作奖励列表
function ControlBeastsWGView:FlushContractRewardList()
	self.node_list.contract_level_text.text.text = string.format(Language.ContralBeasts.ContrastCondition5, self.contract_server_level)
	-- 刷新结缘数字
	local cur_reward_times = ControlBeastsContractWGData.Instance:GetBeastDrawDeedTimes()
	local base_cfg = ControlBeastsContractWGData.Instance:GetBaseCfg()
	if base_cfg then
		self.node_list.contract_operate_num.text.text = string.format("%d/%d", cur_reward_times, base_cfg.reward_times)
        self.node_list.contract_reward_slider.slider.value = cur_reward_times / base_cfg.reward_times
    end

    local list = ControlBeastsContractWGData.Instance:GetCurLevelExtraRewards()
    if not IsEmptyTable(list) then
        local data = list[1] and list[1].item
        if data then
            self.contract_reward_item:SetVisible(true) 
            self.contract_reward_item:SetData(data)
        else
            self.contract_reward_item:SetVisible(false) 
        end
    end
end

-- 刷新操作模型
function ControlBeastsWGView:FlushContractRewardOperateMode()
	local base_cfg = ControlBeastsContractWGData.Instance:GetBaseCfg()
	if base_cfg then
		local item_icon = ItemWGData.Instance:GetItemIconByItemId(base_cfg.cost_item_id) --拥有的数量
		local item_num = ItemWGData.Instance:GetItemNumInBagById(base_cfg.cost_item_id)
		if item_icon then
			local bundle, asset = ResPath.GetItem(item_icon)
			self.node_list.operate_one_const_icon.image:LoadSpriteAsync(bundle, asset)
			self.node_list.operate_ten_const_icon.image:LoadSpriteAsync(bundle, asset)
		end

		local mode_cfg = ControlBeastsContractWGData.Instance:GetCurBeastDrawMode(1)
		if mode_cfg then
			self.node_list.operate_one_const_text.text.text = string.format("%s/%s", item_num, mode_cfg.cost_item_num) 
			local is_one_red = ControlBeastsContractWGData.Instance:GetCurBeastDrawModeRedByMode(1)
			self.node_list.operate_one_red:CustomSetActive(is_one_red)
			UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.operate_one_const_root.rect)
		end

		local mode_cfg = ControlBeastsContractWGData.Instance:GetCurBeastDrawMode(2)
		if mode_cfg then
			self.node_list.operate_ten_const_text.text.text = string.format("%s/%s", item_num, mode_cfg.cost_item_num) 
			local is_ten_red = ControlBeastsContractWGData.Instance:GetCurBeastDrawModeRedByMode(2)
			self.node_list.operate_ten_red:CustomSetActive(is_ten_red)
			UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.operate_ten_const_root.rect)
		end
	end
end

-- 展示中间的信息
function ControlBeastsWGView:FlushContractCenterMessage()
	if self.curr_contract_show then
		return
	end

	local cfg = ControlBeastsContractWGData.Instance:GetBaseCfg()
	if (not cfg) or (not cfg.show_item) then
		return
	end

	local index = 1
	for k, show_item in pairs(cfg.show_item) do
		if show_item and show_item.item_id and self.contract_display_list[index] then
			local res_id = ControlBeastsWGData.Instance:GetBeastModelResId(show_item.item_id)
			local bundle, asset = ResPath.GetBeastsModel(res_id)
			self.contract_display_list[index]:SetMainAsset(bundle, asset)
			-- self.contract_display_list[index]:PlayRoleAction(SceneObjAnimator.Rest)
			
			local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(show_item.item_id)
			local show_name_node = self.contract_model_name_list[index]

			if beast_cfg and show_name_node then
				local bundle, asset = ResPath.GetCommonImages(string.format("a3_hs_pz%d", beast_cfg.beast_color))
				show_name_node.contract_active_jingshen_img.image:LoadSprite(bundle, asset, function()
					show_name_node.contract_active_jingshen_img.image:SetNativeSize()
				end)
		
				local star_res_list = GetStarImgResByStar(beast_cfg.beast_star)
				for k,v in pairs(show_name_node.contract_star_list) do
					v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
				end
		
				show_name_node.contract_beasts_name.text.text = beast_cfg.beast_name
			end

			index = index + 1
		end
	end
end

-- 展示奖励
function ControlBeastsWGView:RewardContractListShow(protocol)
	if self.operate_mode_id == nil then
		return
	end

	local again_func = function ()
		self:OperateContractBtn(self.operate_mode_id)
	end

	local other_info = {}
	local type_index = self.operate_mode_id + 1

	if type_index > 3 then
		type_index = 3
	end

	other_info.again_text = Language.TreasureHunt.BtnText[type_index]

	local base_cfg = ControlBeastsContractWGData.Instance:GetBaseCfg()
	if base_cfg then
		other_info.stuff_id = base_cfg.cost_item_id
		local mode_cfg = ControlBeastsContractWGData.Instance:GetCurBeastDrawMode(self.operate_mode_id)
		other_info.times = mode_cfg and mode_cfg.cost_item_num or 0
		other_info.spend = base_cfg.cost_gold
	end

	other_info.total_price_desc1 = Language.ContralBeasts.TotalPriceDesc

	local item_list = {}      -- 去除大奖得物品列表
	local best_data = {} 
	local best_item_list = {}  -- 大奖列表   

	for k, v in pairs(protocol.item_list) do
		if ControlBeastsContractWGData.Instance:IsExtraRewardItem(v.item_id) then
			table.insert(best_item_list, v)
		else
			table.insert(item_list, v)
		end
	end
	-- item_id          item_ids

	if not IsEmptyTable(best_item_list) then
		if #best_item_list == 1 then
			best_data.item_id = best_item_list[1].item_id
		else
			local item_ids = {}

			for k, v in pairs(best_item_list) do
				item_ids[k] = v.item_id
			end

			best_data.item_ids = item_ids
		end

		other_info.best_data = best_data
	end

	TipWGCtrl.Instance:ShowGetValueReward(item_list, again_func, other_info, false)
end


-----------------------------------------------------------------------------
-- 结缘
function ControlBeastsWGView:OperateContractBtn(mode_id)
	local base_cfg = ControlBeastsContractWGData.Instance:GetBaseCfg()
	if base_cfg then
		local has_num = ItemWGData.Instance:GetItemNumInBagById(base_cfg.cost_item_id) --拥有的数量
		local mode_cfg = ControlBeastsContractWGData.Instance:GetCurBeastDrawMode(mode_id)
		local need_num = mode_cfg and mode_cfg.cost_item_num or 0

		local tips_data = {}
		tips_data.item_id = base_cfg.cost_item_id
		tips_data.price = base_cfg.cost_gold
		tips_data.draw_count = need_num
		tips_data.has_checkbox = true
		tips_data.checkbox_str = string.format("beasts_contract_draw%d", mode_id)
		LimitTimeGiftWGCtrl.Instance:CheckNeedDrawRewardPopupGift(LIMIT_TIME_GIFT_POPUP_DRAW_TYPE.POPUP_DRAW_BEASTS)
		TipWGCtrl.Instance:OpenTipsDrawRewardView(tips_data, BindTool.Bind2(self.SendOperateContractReq, self, mode_id), nil)
	end
end

function ControlBeastsWGView:SendOperateContractReq(mode_id)
	ControlBeastsWGCtrl.Instance:SendOperateTypeBeastDraw(mode_id)
	self.operate_mode_id = mode_id
end

-- 图鉴
function ControlBeastsWGView:LookHandBook()
	ControlBeastsContractWGCtrl.Instance:OpenBeastsContractHandBookView()
end

-- 预览
function ControlBeastsWGView:LookPreView()
	ControlBeastsContractWGCtrl.Instance:OpenBeastsContractPreviewView()
end

-- 展示消耗道具
function ControlBeastsWGView:ShowOperateItemBtn()
	local base_cfg = ControlBeastsContractWGData.Instance:GetBaseCfg()
	if base_cfg then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = base_cfg.cost_item_id})
	end
end
--------------------------------背包幻兽类型item-----------------------
BeastsContractTypeItemRender = BeastsContractTypeItemRender or BaseClass(BaseRender)

function BeastsContractTypeItemRender:OnFlush()
	if not self.data then
		return
	end
	local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.data.item_id)

    if beast_cfg then
        local bundle, asset = ResPath.GetControlBeastsImg(string.format("a3_hs_bq_%d", beast_cfg.beast_element))
        self.node_list.normal.image:LoadSprite(bundle, asset, function()
            self.node_list.normal.image:SetNativeSize()
        end)
    end
end

-- 刷新选中状态
function BeastsContractTypeItemRender:FlushSelectHl(is_select)
    self.node_list.select:CustomSetActive(is_select)
end