TipsEneterCommonSceneView = TipsEneterCommonSceneView or BaseClass(SafeBaseView)

function TipsEneterCommonSceneView:__init()
	self.view_layer = UiLayer.MainUIHigh
	self.active_close = false
	self:AddViewResource(0, "uis/view/entercommonscenetip_prefab", "EnterCommonSceneView")
	self.cg_event = GlobalEventSystem:Bind(ObjectEventType.CG_EVENT_END, BindTool.Bind(self.Play, self))
end

function TipsEneterCommonSceneView:Play(bundle_name, asset_name)

end

function TipsEneterCommonSceneView:__delete()
	if self.cg_event then
		GlobalEventSystem:UnBind(self.cg_event)
		self.cg_event = nil
	end
end

function TipsEneterCommonSceneView:ReleaseCallBack()
	self:RemoveDelay()
end

function TipsEneterCommonSceneView:LoadCallBack()
end

function TipsEneterCommonSceneView:CloseCallBack()
end


function TipsEneterCommonSceneView:SetSceneId(scene_id)
	self.scene_id = scene_id or 0
	self:Open()
end

function TipsEneterCommonSceneView:OnFlush()
	if 0 == self.scene_id then
		return
	end

	local scene_type = Scene.Instance:GetSceneType()
	local scenc_name = Scene.Instance:GetSceneName()
	--self.node_list.common:SetActive(scene_type == SceneType.Common)
	self.node_list.other:SetActive(scene_type ~= SceneType.Common)
	self.node_list.com_name.text.text = scenc_name
	self.node_list.other_name.text.text = scenc_name
	local node = scene_type ~= SceneType.Common and self.node_list.other or self.node_list.common
	UITween.DoViewDuangOpenTween(node)
	self:RemoveDelay()
	self.timer_quest = GlobalTimerQuest:AddDelayTimer(function ()
		self:Close()
	end, 5)
end

function TipsEneterCommonSceneView:RemoveDelay()
	if nil ~= self.timer_quest then
		GlobalTimerQuest:CancelQuest(self.timer_quest)
		self.timer_quest = nil
	end
end