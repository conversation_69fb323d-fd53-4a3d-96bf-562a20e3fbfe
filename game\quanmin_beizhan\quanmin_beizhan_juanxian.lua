
function QuanMinBeiZhanView:InitJuanXianView()
	XUI.AddClickEventListener(self.node_list.jx_btn_jx_one, BindTool.Bind(self.OnJXOneBtn<PERSON><PERSON><PERSON><PERSON><PERSON>,self))
	XUI.AddClickEventListener(self.node_list.jx_btn_jx_ten, BindTool.Bind(self.OnJXTenBtnClickHandler,self))
	XUI.AddClickEventListener(self.node_list.jx_btn_tip, BindTool.Bind(self.OnJXBtnTipClickHnadler,self))
	XUI.AddClickEventListener(self.node_list.jx_one_icon, BindTool.Bind(self.OnJXBtnItemClickHnadler,self))
	XUI.AddClickEventListener(self.node_list.jx_ten_icon, BindTool.Bind(self.OnJXBtnItem<PERSON><PERSON><PERSON>nadler,self))
	XUI.AddClickEventListener(self.node_list.jx_item_icon, BindTool.Bind(self.OnJXBtnItemClickHnadler,self))

	local theme_cfg = QuanMinBeiZhanWGData.Instance:GetActivityThemeCfg(TabIndex.quanmin_beizhan_juanxian)
	self.other_cfg = QuanMinBeiZhanWGData.Instance:GetActivityThemeOtherCfg()
	if self.other_cfg ~= nil and self.other_cfg[1] ~= nil then
		-- self.node_list.jx_biaoyu_text.text.text = theme_cfg.text1
		self.node_list.jx_tip_label.text.text = theme_cfg.rule_tip
		self.node_list.jx_xuanchuantu.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(theme_cfg.xuanchuantu))
		self.node_list.jx_biaoyu.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(theme_cfg.xuanchuanbiaoyu))

		local item_cfg = ItemWGData.Instance:GetItemConfig(self.other_cfg[1].juanxian_stuff_item_id)
		if item_cfg then
			self.node_list.jx_one_icon.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
			self.node_list.jx_ten_icon.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
			self.node_list.jx_item_icon.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
		end
	end

	self.alert_window = Alert.New(nil, nil, nil, nil, true)
	self:InitRewardNode()
	self:FlushJuanXianView()
	self:JuanXianTimeCountDown()

	QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_QUANFUJUANXIAN, QUANFU_JUANXIAN_OP_TYPE.TYPE_INFO)

	if self.jx_item_data_change_callback == nil then
		self.jx_item_data_change_callback = BindTool.Bind1(self.JXOnItemDataChange, self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.jx_item_data_change_callback, false)
	end
end

function QuanMinBeiZhanView:ReleaseJuanXianView()
	if self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end

	if self.qf_reward_cell_list ~= nil then
		for k,v in pairs(self.qf_reward_cell_list) do
			v:DeleteMe()
		end
		self.qf_reward_cell_list = nil
	end

	if self.jx_role_reward_list ~= nil then
		self.jx_role_reward_list:DeleteMe()
		self.jx_role_reward_list = nil
	end

	if self.qf_reward_list ~= nil then
		self.qf_reward_list:DeleteMe()
		self.qf_reward_list = nil
	end

	if self.jx_count_down and CountDownManager.Instance:HasCountDown(self.jx_count_down) then
		CountDownManager.Instance:RemoveCountDown(self.jx_count_down)
	end

	if self.jx_item_data_change_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.jx_item_data_change_callback)
		self.jx_item_data_change_callback = nil
	end

	self.other_cfg = nil
end

--初始化奖励节点
function QuanMinBeiZhanView:InitRewardNode()
	local cfg = QuanMinBeiZhanWGData.Instance:GetQFJXDayRewardCfg()
	local len = #cfg
	
	local list = {}
	for i,v in ipairs(cfg) do
		table.insert(list, v)
	end

	local data = {}
	data.index = 1000
	table.insert(list, data)	--增加多一个多适配，不显示

	if not self.qf_reward_list then
		self.qf_reward_list = AsyncListView.New(QFJXRewardItemRender, self.node_list.jx_slide_list)
	end
	self.qf_reward_list:SetDataList(list)
end

--物品变化
function QuanMinBeiZhanView:JXOnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if self.other_cfg and self.other_cfg[1].juanxian_stuff_item_id == change_item_id then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(self.other_cfg[1].juanxian_stuff_item_id)
		self.node_list.jx_my_own.text.text = item_num
		self.node_list.jx_one_redpoint:SetActive(item_num > 0)
		self.node_list.jx_ten_redpoint:SetActive(item_num >= 10)
	end
	RemindManager.Instance:Fire(RemindName.QuanMinBeiZhan_JuanXian)
	RemindManager.Instance:Fire(RemindName.QuanMinBeiZhan)
end

function QuanMinBeiZhanView:FlushJXRewardState()
	if self.qf_reward_cell_list ~= nil then
		for i,v in ipairs(self.qf_reward_cell_list) do
			v:OnFlush()
		end
	else
		self.qf_reward_list:RefreshActiveCellViews()
	end
end

--跳转到最新的进度
function QuanMinBeiZhanView:JXGotoLastProgress()
	if self.qf_reward_list then
		local active_num, total_num = QuanMinBeiZhanWGData.Instance:GetActiveNum()
		self.qf_reward_list:ReloadData(active_num / total_num)
	end
end

--刷新界面状态
function QuanMinBeiZhanView:FlushJuanXianView()
	local qf_cfg = QuanMinBeiZhanWGData.Instance:GetQFJXDayRewardCfg()
	if qf_cfg ~= nil then
		local max_jx_count = qf_cfg[#qf_cfg].server_day_juanxian_num
		local server_count = QuanMinBeiZhanWGData.Instance:GetServerJuanXianCount()
		local rate = server_count * 1.0 / max_jx_count
		-- self.node_list.jx_slide.slider.value = rate
		self.node_list.jx_slide_value.text.text = string.format("%d%%", math.floor(rate * 100))
		self.node_list.jx_biaoyu_text.text.text = string.format(Language.QuanMinBeiZhan.JuanXianStr8, server_count)
	end

	local role_reward_cfg = QuanMinBeiZhanWGData.Instance:GetQFRoleRewardCfg()
	if role_reward_cfg ~= nil then
		if not self.jx_role_reward_list then
			self.jx_role_reward_list = AsyncListView.New(QMJXRoleRewardItemRender, self.node_list.jx_role_reward_list)
		end
		local list = {}
		local state
		for i,v in ipairs(role_reward_cfg) do
			local item = {}
			item.cfg = v
			item.index = v.index
			state = QuanMinBeiZhanWGData.Instance:GetRoleRewardState(v.index)
			if state == ActivityRewardState.KLQ then
				item.sort = 0
			elseif state == ActivityRewardState.BKL then
				item.sort = 1
			else
				item.sort = 2
			end
			table.insert(list, item)
		end
		table.sort(list, SortTools.KeyLowerSorter("sort","index"))

		self.jx_role_reward_list:SetDataList(list)
	end

	if self.other_cfg then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(self.other_cfg[1].juanxian_stuff_item_id)
		self.node_list.jx_my_own.text.text = item_num
		self.node_list.jx_one_redpoint:SetActive(item_num > 0)
		self.node_list.jx_ten_redpoint:SetActive(item_num >= 10)
	end
end

function QuanMinBeiZhanView:OnJXBtnTipClickHnadler()
	local role_tip = RuleTip.Instance
	if role_tip then
		local title,desc = QuanMinBeiZhanWGData.Instance:GetActivityTip(TabIndex.quanmin_beizhan_juanxian)
		if title ~= nil and desc ~= nil then
			role_tip:SetTitle(title)
			role_tip:SetContent(desc)
		end
	end
end

function QuanMinBeiZhanView:OnJXBtnItemClickHnadler()
	if self.other_cfg and self.other_cfg[1] then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(self.other_cfg[1].juanxian_stuff_item_id)
		TipWGCtrl.Instance:OpenItem({item_id = self.other_cfg[1].juanxian_stuff_item_id, num = item_num, is_bind = 1}, ItemTip.FROM_NORMAL, nil)
	end
end

function QuanMinBeiZhanView:OnJXOneBtnClickHandler()
	if self.other_cfg ~= nil then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(self.other_cfg[1].juanxian_stuff_item_id)
		if item_num > 0 then
			QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_QUANFUJUANXIAN, QUANFU_JUANXIAN_OP_TYPE.TYPE_JUANXIAN, 1, JUANXINA_STUFF_TYPE.TYPE_ITEM)
		else
			self.alert_window:SetLableString(string.format(Language.QuanMinBeiZhan.JuanXianStr1, self.other_cfg[1].juanxian_need_xianyu, 1))
			self.alert_window:SetOkFunc(function ()
				QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_QUANFUJUANXIAN, QUANFU_JUANXIAN_OP_TYPE.TYPE_JUANXIAN, 1, JUANXINA_STUFF_TYPE.TYPE_XIANAYU)
			end)
			self.alert_window:Open()
			return
		end
	end
end

function QuanMinBeiZhanView:OnJXTenBtnClickHandler()
	if self.other_cfg ~= nil then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(self.other_cfg[1].juanxian_stuff_item_id)
		if item_num >= 10 then
			QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_QUANFUJUANXIAN, QUANFU_JUANXIAN_OP_TYPE.TYPE_JUANXIAN, 10, JUANXINA_STUFF_TYPE.TYPE_ITEM)
		else
			self.alert_window:SetLableString(string.format(Language.QuanMinBeiZhan.JuanXianStr1, self.other_cfg[1].juanxian_need_xianyu * 10, 10))
			self.alert_window:SetOkFunc(function ()
				QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_QUANFUJUANXIAN, QUANFU_JUANXIAN_OP_TYPE.TYPE_JUANXIAN, 10, JUANXINA_STUFF_TYPE.TYPE_XIANAYU)
			end)
			self.alert_window:Open()
			return
		end
	end
end

--有效时间倒计时
function QuanMinBeiZhanView:JuanXianTimeCountDown()
	self.jx_count_down = "jx_count_down"
	local invalid_time = QuanMinBeiZhanWGData.Instance:GetActivityInValidTime(TabIndex.quanmin_beizhan_juanxian)
	if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
		self.node_list.jx_time_label.text.text = TimeUtil.FormatSecondDHM2(invalid_time - TimeWGCtrl.Instance:GetServerTime())
		CountDownManager.Instance:AddCountDown(self.jx_count_down, BindTool.Bind1(self.UpdateJXCountDown, self), BindTool.Bind1(self.OnJXComplete, self), invalid_time, nil, 1)
	end
end

function QuanMinBeiZhanView:UpdateJXCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.login_act_date = TimeUtil.FormatUnixTime2Date()
		self.node_list.jx_time_label.text.text = TimeUtil.FormatSecondDHM2(valid_time)
	end
end

function QuanMinBeiZhanView:OnJXComplete()
	self.node_list.jx_time_label.text.text = Language.Activity.TianshenRoadLoginTime
end

--------------------------------------奖励类型tips------------------------------------------
QUJXTipView = QUJXTipView or BaseClass(SafeBaseView)

function QUJXTipView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/quanmin_beizhan_ui_prefab", "layout_jxreward_tip_view")
end

function QUJXTipView:__delete()

end

function QUJXTipView:ReleaseCallBack()
	self.has_load_callback = nil
	self.need_flush = nil
	self.is_bh = nil
end

function QUJXTipView:LoadCallBack()
	self.has_load_callback = true
	if self.need_flush then
		self:OnFlush()
	end
end

function QUJXTipView:SetData(data)
	self.data = data
	self:OnFlush()
end

function QUJXTipView:OnFlush()
	if not self.has_load_callback then
		self.need_flush = true
		return
	end

	if self.data == nil then
		return
	end

	-- local cfg = QuanMinBeiZhanWGData.Instance:GetQFRewardTypeCfgForIndex(self.data.reward_type)

	self.node_list.item:SetActive(true)
	self.node_list.cell_bg:SetActive(false)
	self.node_list.star:SetActive(false)

	self.node_list.name.text.text = self.data.name
	self.node_list.desc.text.text = self.data.desc
	if self.data.reward_type == 1 then
		self.node_list.item.image:LoadSprite(ResPath.GetQuanMinBeiZhanImagePath(self.data.icon))
	elseif self.data.reward_type == 2 then
		self.node_list.item:SetActive(false)
		self.node_list.cell_bg:SetActive(true)
		self.node_list.star:SetActive(true)

		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.param1)
		if item_cfg then
			self.node_list.cell.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
			if item_cfg.color >= GameEnum.ITEM_COLOR_GREEN then
				local bundle, asset = ResPath.GetCommonBackGround(string.format("bg_cell_circle2_%d", item_cfg.color))
				self.node_list.cell_bg.image:LoadSprite(bundle, asset)
			end
		end

		if self.data.param2 > 1 then
			self.node_list.item_num_bg:SetActive(true)
			self.node_list.item_num.text.text = self.data.param2
		else
			self.node_list.item_num_bg:SetActive(false)
		end

		for i=1,3 do
			self.node_list["star"..i]:SetActive(i <= self.data.star)
		end

	elseif self.data.reward_type == 3 or self.data.reward_type == 6 then
		self.node_list.item.image:LoadSprite(ResPath.GetSkillIconById(self.data.icon))
	elseif self.data.reward_type == 4 then
		local boss_name = Language.QuanMinBeiZhan.JuanXianBossList[tonumber(self.data.param1)]
		self.node_list.item.image:LoadSprite(ResPath.GetQuanMinBeiZhanImagePath(self.data.icon))
	elseif self.data.reward_type == 5 then
		self.node_list.desc.text.text = ""
	end

	if self.data.reward_type == 2 then
		self.node_list.state_text.text.text = string.format(Language.QuanMinBeiZhan.JuanXianStr2, self.data.server_day_juanxian_num)
		local state = QuanMinBeiZhanWGData.Instance:GetGiftState(self.data.index)
		if state == ActivityRewardState.YLQ then
			self.node_list.time_text.text.text = Language.QuanMinBeiZhan.JuanXianStr5
		elseif state == ActivityRewardState.KLQ then
			self.node_list.time_text.text.text = Language.QuanMinBeiZhan.JuanXianStr6
		else
			self.node_list.time_text.text.text = Language.QuanMinBeiZhan.JuanXianStr7
		end
	else
		local time_sec = QuanMinBeiZhanWGData.Instance:GetRewardTime(self.data.index)
		self.node_list.state_text.text.text = string.format(Language.QuanMinBeiZhan.JuanXianStr3, self.data.server_day_juanxian_num)
		if time_sec > 0 then
			self.node_list.time_text.text.text = string.format(Language.QuanMinBeiZhan.JuanXianStr4, TimeUtil.FormatSecond2MYHM1(time_sec))
		else
			self.node_list.time_text.text.text = Language.QuanMinBeiZhan.JuanXianStr7
		end
	end
end

---------------------------------- 全服每日捐献奖励---------------------------
QFJXRewardItemRender = QFJXRewardItemRender or BaseClass(BaseRender)
function QFJXRewardItemRender:__init(instance)
	
end

function QFJXRewardItemRender:LoadCallBack()
	if self.data ~= nil then
		self:OnFlush()
	end
end

function QFJXRewardItemRender:__delete()

end

function QFJXRewardItemRender:SetItemData(data)
	self.data = data
	self:OnFlush()
end

function QFJXRewardItemRender:OnFlush()
	if not self.data then
		return
	end

	--最后一个做适配的，非有效数据
	if self.data.index == 1000 then
		self.node_list.slide_bg:SetActive(false)
		self.node_list.slide:SetActive(false)
		self.node_list.up:SetActive(false)
		self.node_list.down:SetActive(false)
		return
	end

	local max_count = QuanMinBeiZhanWGData.Instance:GetQFJXDayRewardCfgMaxCount()
	local rate = QuanMinBeiZhanWGData.Instance:IsActiveServerJXReward(self.data.index)

	self.node_list.up:SetActive(false)
	self.node_list.down:SetActive(false)
	self.node_list.down_yilingqu:SetActive(false)
	self.node_list.down_bx_redpoint:SetActive(false)
	self.node_list.up_yilingqu:SetActive(false)
	self.node_list.up_bx_redpoint:SetActive(false)
	self.node_list.up_star:SetActive(false)
	self.node_list.down_star:SetActive(false)
	self.node_list.slide:SetActive(true)
	self.node_list.slide_bg:SetActive(true)
	self.node_list.left_line:SetActive(self.data.index == 1)
	self.node_list.right_line:SetActive(self.data.index == max_count)

	self.node_list.slide.slider.value = rate

	if self.data.index % 2 == 0 then
		for i=1,5 do
			self.node_list["down_type_"..i]:SetActive(false)
		end
		self.node_list.down:SetActive(true)
		
		-- XUI.SetGraphicGrey(self.node_list["down_type_"..self.data.reward_type], rate ~= 1)
		self.node_list["down_type_"..self.data.reward_type]:SetActive(true)
		self.node_list["down_type_"..self.data.reward_type].button:AddClickListener(BindTool.Bind(self.OnBtnClick, self))
		if self.data.reward_type == 2 then
			local state = QuanMinBeiZhanWGData.Instance:GetGiftState(self.data.index)
			self.node_list.down_bx_redpoint:SetActive(state == ActivityRewardState.KLQ)
			self.node_list.down_yilingqu:SetActive(state == ActivityRewardState.YLQ)

			local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.param1)
			if item_cfg then
				self.node_list.down_bx.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
				if item_cfg.color >= GameEnum.ITEM_COLOR_GREEN then
					local bundle, asset = ResPath.GetCommonBackGround(string.format("bg_cell_circle2_%d", item_cfg.color))
					self.node_list.down_quality_bg.image:LoadSprite(bundle, asset)
				end
			end

			if self.data.param2 > 1 then
				self.node_list.down_num_bg:SetActive(true)
				self.node_list.down_num.text.text = self.data.param2
			else
				self.node_list.down_num_bg:SetActive(false)
			end

			self.node_list.down_star:SetActive(true)
			for i=1,3 do
				self.node_list["down_star"..i]:SetActive(i <= self.data.star)
			end
		elseif self.data.reward_type == 3 or self.data.reward_type == 6 then
			self.node_list["down_type_3"].image:LoadSprite(ResPath.GetSkillIconById(self.data.icon))
		elseif self.data.reward_type == 4 then
			self.node_list["down_boss_type"].text.text = self.data.desc
		end
	else
		for i=1,5 do
			self.node_list["up_type_"..i]:SetActive(false)
		end
		self.node_list.up:SetActive(true)

		-- XUI.SetGraphicGrey(self.node_list["up_type_"..self.data.reward_type], rate ~= 1)
		self.node_list["up_type_"..self.data.reward_type]:SetActive(true)
		self.node_list["up_type_"..self.data.reward_type].button:AddClickListener(BindTool.Bind(self.OnBtnClick, self))
		if self.data.reward_type == 2 then
			local state = QuanMinBeiZhanWGData.Instance:GetGiftState(self.data.index)
			self.node_list.up_bx_redpoint:SetActive(state == ActivityRewardState.KLQ)
			self.node_list.up_yilingqu:SetActive(state == ActivityRewardState.YLQ)
			
			local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.param1)
			if item_cfg then
				self.node_list.up_bx.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
				if item_cfg.color >= GameEnum.ITEM_COLOR_GREEN then
					-- local bundle, asset = ResPath.GetF2BackgroundImages(string.format("bg_cell_circle2_%d", item_cfg.color))
					local bundle, asset = "uis/images/background_atlas", string.format("bg_cell_circle2_%d", item_cfg.color)
					self.node_list.up_quality_bg.image:LoadSprite(bundle, asset)
				end
			end

			if self.data.param2 > 1 then
				self.node_list.up_num_bg:SetActive(true)
				self.node_list.up_num.text.text = self.data.param2
			else
				self.node_list.up_num_bg:SetActive(false)
			end

			self.node_list.up_star:SetActive(true)
			for i=1,3 do
				self.node_list["up_star"..i]:SetActive(i <= self.data.star)
			end
		elseif self.data.reward_type == 3 or self.data.reward_type == 6 then
			self.node_list["up_type_3"].image:LoadSprite(ResPath.GetSkillIconById(self.data.icon))
		elseif self.data.reward_type == 4 then
			self.node_list["up_boss_type"].text.text = self.data.desc
		end
	end

end

function QFJXRewardItemRender:OnBtnClick()
	if self.data.reward_type == 2 then
		local state = QuanMinBeiZhanWGData.Instance:GetGiftState(self.data.index)
		if state == ActivityRewardState.KLQ then
			QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_QUANFUJUANXIAN, QUANFU_JUANXIAN_OP_TYPE.TYPE_SERVER_REWARD, self.data.index)
		else
			-- TipWGCtrl.Instance:OpenItem({item_id = self.data.param1, num = self.data.param2, is_bind = self.data.param3}, ItemTip.FROM_NORMAL, nil)
			QuanMinBeiZhanWGCtrl.Instance:OpenJXRewardTip(self.data)
		end
	else
		--打开礼包界面
		QuanMinBeiZhanWGCtrl.Instance:OpenJXRewardTip(self.data)
	end
end

-------------------------------------------个人捐献奖励------------------------------------
QMJXRoleRewardItemRender = QMJXRoleRewardItemRender or BaseClass(BaseRender)
function QMJXRoleRewardItemRender:__init()

end

function QMJXRoleRewardItemRender:LoadCallBack()
	self.node_list.btn_mask.button:AddClickListener(BindTool.Bind(self.OnBtnMaskClick, self))
end

function QMJXRoleRewardItemRender:__delete()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end

function QMJXRoleRewardItemRender:OnFlush()
	if not self.data then
		return
	end

	if self.cell == nil then
		self.cell = ItemCell.New(self.node_list["cell"])
	end
	self.cell:SetData(self.data.cfg.reward_item[0])

	local show_num = 0
	local my_count = QuanMinBeiZhanWGData.Instance:GetRoleJuanXianCount()
	local color = "<color=#432912>"
	if my_count < self.data.cfg.juanxian_num then
		color = "<color=#ff0000>"
		show_num = my_count
	else
		show_num = self.data.cfg.juanxian_num
	end
	self.node_list.txt.text.text = string.format("%s%d/%d</color>",color, show_num, self.data.cfg.juanxian_num)

	local state = QuanMinBeiZhanWGData.Instance:GetRoleRewardState(self.data.cfg.index)
	self.node_list.btn_mask:SetActive(state == ActivityRewardState.KLQ)
	self.cell:SetLingQuVisible(state == ActivityRewardState.YLQ)
	self.cell:SetCanOperateIconVisible(state == ActivityRewardState.KLQ)

end

function QMJXRoleRewardItemRender:OnBtnMaskClick()
	QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_QUANFUJUANXIAN, QUANFU_JUANXIAN_OP_TYPE.TYPE_ROLE_REWARD, self.data.cfg.index)
end