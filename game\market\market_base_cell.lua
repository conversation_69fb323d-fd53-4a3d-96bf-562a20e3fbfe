MarketBaseCell = MarketBaseCell or BaseClass(ItemCell)

function MarketBaseCell:OnClick()
	if self.data and self.is_showtip then
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		if nil == item_cfg then return end
		if item_cfg.sub_type and item_cfg.sub_type >= GameEnum.E_TYPE_SHENSHOU_PART_0 and item_cfg.sub_type <= GameEnum.E_TYPE_SHENSHOU_PART_4 then
			local data = self:ShenShouEquipChangeValue(self.data)
			ShenShouWGCtrl.Instance:OpenShenShouEquipTip(data, ShenShouEquipTip.FROM_MARKET_SHANGJIA_CLICK)
		else
			TipWGCtrl.Instance:OpenItem(self.data, ItemTip.FROM_MARKET_SHANGJIA_CLICK, nil)
		end 
	end
end

function MarketBaseCell:ShenShouEquipChangeValue(data)
	local temp_data = {}
	temp_data.attr_list = {}
	if data.param then
		temp_data.attr_list[1] = {attr_type = data.param.shen_level, attr_value = data.param.random_arrt_val}
		temp_data.attr_list[2] = {attr_type = data.param.fuling_level, attr_value = data.param.has_lucky}
		temp_data.attr_list[3] = {attr_type = data.param.random_attr_type, attr_value = data.param.fumo_id}

	end
	temp_data.item_id = data.item_id
	temp_data.star_count = data.star_level
	temp_data.is_bind = data.is_bind
	temp_data.grid_name = GRID_TYPE_SHENSHOU_BAG
	temp_data.auction_index = data.auction_index
	return temp_data
end