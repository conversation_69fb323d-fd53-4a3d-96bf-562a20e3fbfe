------------------------------------------------------------
--物品tip
------------------------------------------------------------
ItemTip = ItemTip or BaseClass(SafeBaseView)

ItemTip.FROM_NORMAL = 0							--无
ItemTip.FROM_BAG = 1 							--在背包界面中（没有打开仓库和出售）          --装备只显示基础和仙品
ItemTip.FROM_BAG_ON_BAG_STORGE = 2				--打开仓库界面时，来自背包
ItemTip.FROM_STORGE_ON_BAG_STORGE = 3			--打开仓库界面时，来自仓库
ItemTip.FROM_BAG_ON_BAG_SALE = 4				--打开售卖界面时，来自背包
ItemTip.FROM_BAG_EQUIP = 5						--在装备界面时，来自装备                      --装备所有信息
ItemTip.FROM_BAOXIANG = 6 						--打开宝箱界面时，来自宝箱
ItemTip.FROM_MARKET_JISHOU = 7					--打开市场界面时，来自寄售
ItemTip.FROME_MARKET_GOUMAI = 8					--打开市场界面时，来自购买
ItemTip.FROME_BROWSE_ROLE = 9					--查看角色界面时，来自查看
ItemTip.FROM_SJ_JC_ON = 10						--来自装备升级继承放入
ItemTip.FROM_EQUIPMENT = 11 					--来自装备锻造
ItemTip.FROM_SJ_JC_OFF = 12 					--来自装备升级继承卸下
ItemTip.FROM_BAG_ON_BAG_SALE_SHITU = 13 		--师徒装备售卖界面时，来自背包
ItemTip.FROM_ACT_NEED_CONTRAST = 14				--活动界面 需要装备对比
ItemTip.FROM_BAG_ON_GUILD_STORGE = 15			--打开仙盟仓库界面时，来自背包
ItemTip.FROM_STORGE_ON_GUILD_STORGE = 16		--打开仙盟仓库界面时，来自仓库
ItemTip.FROM_CAMP_EQUIP = 17					--打开阵营装备界面时，来自阵营装备
ItemTip.FROM_PANIC_ITEM = 18					--打开特惠秒杀
ItemTip.FROM_WELFARE_ITEM = 19					--打开福利
ItemTip.FROM_LUCKY_BUY = 20						--来自幸运云购
ItemTip.FROM_COOL_BUY = 21						--来自许愿池
ItemTip.FROM_JINGLING_BAOXIANG = 22				--打开宝箱界面时，来自精灵寻宝界面
ItemTip.FROM_BAG_XIAOGUI = 23					-- 在装备界面时，来自小鬼
ItemTip.FROM_NO_GUOQI = 24 						--在背包界面中（小鬼没过期）
ItemTip.FROM_GUOQI = 25 						--在背包界面中（小鬼过期）
ItemTip.FROM_EQUIPBROWSE = 26                   --奖励物品装备预览                              --装备基础和幸运仙品信息
ItemTip.FROM_WORLD = 27                         --世界传闻预览                                  --装备只显示基础和仙品不显示按钮
ItemTip.EQUIPMENT_CONTRAST = 28                 --装备对比
ItemTip.FROM_EQUIMENT_HECHENG = 29              --装备合成
ItemTip.FROM_EQUIP_FUMO = 30  					--在附魔界面
ItemTip.FROM_SHENGYIN = 31  					--在圣印界面
ItemTip.FROM_SHENGYIN_NOT_USE = 32 				--圣印属性界面 不加使用按钮
ItemTip.FROM_TEHUI_SHOP = 33					--打开随机活动-特惠秒杀
ItemTip.FROM_QiChong = 34						--骑宠背包
ItemTip.FROM_SHENGYIN_NOT_USEHERE = 35			--圣印已经装备的物品
ItemTip.FROM_MARKET_QIUGOU = 36					--市场求购界面
ItemTip.FROM_TRANSFER = 38						--转职
ItemTip.FROM_BAOSHI = 39                        --宝石
ItemTip.FROM_BOSS = 40							--BOSS面板
ItemTip.FROM_FULILOGIN = 41						--福利七天登录
ItemTip.FROM_FIRSTRECHARGE = 42 				--首充
ItemTip.FROM_MARKET_SHANGJIA = 43             	 --上架
ItemTip.FROM_CELL_CHATEXT = 44					--聊天
ItemTip.FROM_PUTON_XIAOGUI = 45					--已装备的小鬼
ItemTip.FROM_BAG_MELTING = 46 					--背包一键熔炼
ItemTip.SHANGGU_JINGLING_EQUIP = 47 			--上古精灵分解
ItemTip.FROM_SHOP_BIND_GOLD = 48 				--绑定元宝商城(小鬼)
ItemTip.FROM_SHOP_GOLD = 49 				 	--元宝商城(小鬼)
ItemTip.SHOP_BUY = 50 							--商城购买
ItemTip.FROM_CHATEXT = 53				        --聊天的物品
ItemTip.FROM_SHOP_EQUIP = 54					--商城装备
ItemTip.FROM_GUILDBOSS = 55				        --仙盟boss展示物品
ItemTip.FROM_TIANSHEN_ROAD = 56
ItemTip.KAIFU = 57								--开服
ItemTip.FROM_TIANSHEN_JICHENG = 59				--天神继承
ItemTip.FROM_TIANSHEN_SHENSHI_EQUIP = 60		--天神神饰 装备
ItemTip.FROM_TIANSHEN_SHENSHI_BAG = 61			--天神神饰 背包
ItemTip.FROM_TIANSHEN_FENJIE = 62 				--天神神饰 分解
ItemTip.FROM_TIANSHEN_QIANGHUA = 63 			--天神神饰 强化
ItemTip.FROM_TIANSHEN_SHENQI_FENJIE = 65 		--天神神器 分解
ItemTip.FORM_ROBOT_SHOW_INFO = 66				--机器人盟主展示信息 和查看玩家信息一样，但是是客户端取配置的
ItemTip.FROM_MARKET_SHANGJIA_CLICK = 100         --市场点击显示
ItemTip.FROM_GET_REWARD = 101                    --恭喜获得点击显示
ItemTip.FROM_ETERNAL_NIGHT = 102                 --永夜之巅
ItemTip.FROM_LONGHUN_BAG = 103					--龙魂背包
ItemTip.FROM_LONGHUN_EQUIP = 104				--龙魂装备
ItemTip.FROM_MINGWEN_BAG = 105					--铭文装备
ItemTip.FROM_TIANSHEN_BAGUA_BAG = 106			--天神八卦背包
ItemTip.FROM_TIANSHEN_BAGUA_EQUIP = 107			--天神八卦装备
ItemTip.FROM_TIANSHEN_BAGUA_BAG = 108			--天神八卦商城
ItemTip.FROM_MOUNTEQUIP_EQUIP = 109			    --骑宠装备穿戴 --
ItemTip.FROM_MOUNTEQUIP_BAG = 110			    --骑宠装备背包
ItemTip.FROM_FIGHT_SOUL_BONE = 111			    --战魂魂骨
ItemTip.FROM_FIGHT_SOUL_BONE_CONTRAST = 112		--战魂魂骨-对比
ItemTip.FROM_FIGHT_SOUL_BONE_WEAR = 113			--战魂魂骨-穿戴列表
ItemTip.FROM_ROLE_BAG_TRANSSEX = 114			--背包-转化
ItemTip.FROM_NEW_YINJI = 115                   	 -- 装备印记
ItemTip.FROM_NEW_YINJI_CHUANWEN = 116            -- 装备印记传闻
ItemTip.FROM_HOLY_EQUIP_BAG = 117				-- 圣装背包
ItemTip.FROM_HOLY_EQUIP_WEAR = 118				-- 圣装穿戴
ItemTip.FROM_HOLY_EQUIP_CONTRAST = 119			-- 圣装对比
ItemTip.FROM_HOLY_EQUIP_EVOLVE = 120			-- 圣装来着进化界面
ItemTip.FROM_SWORN_EQUIP = 121					-- 结义装备界面
ItemTip.FROM_RELIC_EQUIP = 122					-- 圣器装备界面
ItemTip.FROM_RELIC_EQUIP_WEAR = 123			    -- 圣器穿戴
ItemTip.FROM_SHITIAN_EQUIP = 124			    -- 弑天装备界面
ItemTip.FROM_ZHUHUN_EQUIP = 125			    	-- 铸魂装备界面
ItemTip.FROM_ZHUHUN_EQUIP_NEXT = 126			-- 铸魂装备下一级
ItemTip.FROM_LONGSHEN_EQUIP = 127			    -- 龙神装备界面
ItemTip.FROM_DAOHANG_EQUIP = 128                -- 道行装备界面
ItemTip.FROM_MENGLING_EQUIP = 129               -- 梦灵镶嵌的装备
ItemTip.FROM_MAEKET_POP_USE_EQUIP = 130         -- 市场快捷装备
ItemTip.FROM_KEY_USE_EQUIP = 131                -- 自动弹出快捷使用
ItemTip.FROM_HUNT_THUNDER_STORGE = 132          -- 寻宝 雷法仓库
ItemTip.FROM_PREVIEW_SHOW_ITEM = 133          	-- 特殊道具预览增加使用按钮
ItemTip.FROM_CUSTOM_BTN = 10000 				--大于 10000 自己传过来的按钮

ItemTip.HANDLE_EQUIP = 1						--装备
ItemTip.HANDLE_USE = 2							--使用
ItemTip.HANDLE_COMPOSE = 3						--合成
ItemTip.HANDLE_STORGE = 4						--存放
ItemTip.HANDLE_SALE = 5							--售卖
ItemTip.HANDLE_BACK_BAG = 6						--取出 从仓库取回到背包
ItemTip.HANDLE_TAKEOFF = 7 						--取下 装备
ItemTip.BAOXIANG_QUCHU = 8						--从宝箱中取出物品放进背包
ItemTip.SHICHANG_CHEHUI = 9 					--撤回寄售的物品
ItemTip.SHICHANG_GOUMAI = 10 					--从市场中购买
ItemTip.RONGHE = 11 							--融合
ItemTip.HANDLE_EXCHANGE = 12					--兑换
ItemTip.HANDLE_FORGE = 13						--锻造
ItemTip.HANDLE_RECOVER = 14						--出售（回收）
ItemTip.HANDLE_TAKEON = 15						--放入
ItemTip.HANDLE_QINGYUSN = 16					--情缘
ItemTip.HANDLE_RONGLIAN = 17					--熔炼
ItemTip.HANDLE_ACTIVE = 21						--激活
ItemTip.HANDLE_UP_LEVEL = 22					--升级
ItemTip.JINGLING_BAOXIANG_QUCHU = 23			--取出 从精灵宝箱中取出物品放进背包
ItemTip.HANDLE_GONGXIAN = 24					--贡献 从帮派背包中贡献给帮派
ItemTip.HANDLE_DECOMPOSE = 25					--分解 背包
ItemTip.HANDLE_XUFEI_PUTON = 26					--续费穿在身上的小鬼
ItemTip.HANDLE_XUFEI_INBAG = 27					--续费背包的小鬼
ItemTip.HANDLE_BAOSHI_INLAY = 28				--宝石镶嵌
ItemTip.HANDLE_EQUIP_DECOMPOSE = 31				--拆解
ItemTip.HANDLE_PRESENT_SEND = 32				--赠送
ItemTip.HANDLE_REPACE = 33						--替换
ItemTip.HANDLE_FIGHT_SOUL_RESTORE = 34			--四象还原
ItemTip.HANDLE_HECHENG_PUTON = 53				--已穿戴小鬼合成
ItemTip.HANDLE_HUANHUA = 58                     --守护幻化
ItemTip.SHICHANG_WORLD_SELL = 64                --世界上架
ItemTip.TXS_ACTIVE = 66		                    --同心锁激活
ItemTip.BOSS_REFRESH = 67		                --Boss刷新，召唤卡
ItemTip.HANDLE_BABY_DECOMPOSE = 68		        --仙娃分解
ItemTip.HANDLE_DRESS = 70		        		--穿戴
ItemTip.HANDLE_LINGYU_INLAY = 71				--灵玉镶嵌
ItemTip.HANDLE_ITEM_RESOVLE = 72				--物品分解
ItemTip.HANDLE_FIVEELEMENTS_INLAY = 80          --五行背包镶嵌
ItemTip.HANDLE_CHARM_INLAY = 90                 --五行背包镶嵌
ItemTip.COLLECT_CARD = 91                 		--卡牌收集
ItemTip.BEAST_ALCHEMY_EQUIP_BAG = 91           	--幻兽内丹
ItemTip.HUNT_THUNDER_BAOXIANG_QUCHU = 92        --寻宝雷法



ItemTip.QUICK_ADDITEM = 5000					--快速获得物品



local ATTR_BASE = 1 					-- 基础属性
local ATTR_PINK_SUIT = 2				-- 粉装属性
local ATTR_GOD_SUIT = 3					-- 神装属性
local ATTR_MORE_COLOR = 4				-- 幻彩属性
local ATTR_LEGEND = 5					-- 传奇属性 仙品属性
local ATTR_SHENGPIN = 6 				-- 升品属性
local ATTR_STONE = 7					-- 宝石
local ATTR_STONE_JL = 8 				-- 宝石精炼
local ATTR_SUIT = 9						-- 装备套装
local ATTR_LINGYUE = 10					-- 灵玉
local ATTP_YULING = 11                  -- 御灵
local ATTP_ZHUSHEN = 12					-- 铸神
local ATTP_XILIAN = 13					-- 装备洗炼

local ATTR_XIANMENG_CANGKU = 20			-- 仙盟仓库属性
local REMIND_HOW_COMPOSE = 21			-- 装备合成公式
local ATTR_EQUIP_SKILL = 22				-- 装备技能

local ATTR_STRENGTHEN = 30				-- 装备强化属性 天神专属
local ATTR_JIPING = 31					-- 装备极品属性 天神专属

local ATTR_SHENJI_STAR = 32				-- 神机暗器 星级属性
local ATTR_SHENJI_SKILL = 33			-- 神机暗器 专属技能
local ATTR_ITEM_SKILL = 34				-- 物品技能(1主动技能 2专属技能 3被动技能)

local ATTR_NEWYINJI = 41				--新印记属性
local ATTR_KEYINFU = 42					--刻印符属性

function ItemTip:__init()
	self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Pop
	self.label_t = Language.Tip.ButtonLabel
	self.data = CommonStruct.ItemDataWrapper()
	self.market_sell_num = 1
	self.shenping_base_pingfen_per = 0 	-- 升品基础总属性加成
	self.need_hide_buy_btn = false
	self.get_way_view_is_on = false
	self:InitLoadUIConfig()
end

function ItemTip:InitLoadUIConfig()
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_background_common_panel")
	self:AddViewResource(0, "uis/view/itemtip_ui_prefab", "layout_itemtip")
end

function ItemTip:__delete()

end

function ItemTip:LoadCallBack()
	self:InitTipsPanel()
end

function ItemTip:ReleaseCallBack()
	self:ReleaseTipsPanel()
end

function ItemTip:CloseCallBack()
    TipWGData.Instance:SetDefShowBuyCount(nil)
	if self.base_tips then
		self.base_tips:CancleShowCCEffect()
	end

	TipWGCtrl.Instance:TryOpenWaitItemTips()
end

function ItemTip:ShowIndexCallBack()
	self:FlushView()
end

function ItemTip:InitTipsPanel()
	self.base_tips = BaseTip.New(self.node_list["base_tip_root"])
	self.item_cell = self.base_tips:GetItemCell()
	self.item_cell:SetIsShowTips(false)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.MainUIView, self.get_guide_ui_event)
end

function ItemTip:ShowMoneyBar(is_show, price_type)
	if price_type == nil then
		is_show = false
	end

	local is_show_honor = false
	local is_show_chivalrous = false
	if is_show then
		for k,v in pairs(price_type) do
			if v == Shop_Money_Type.Type4 then
				is_show_honor = true
			elseif v == Shop_Money_Type.Type7 then
				is_show_chivalrous = true
			end
		end
	end

	local show_params = {
		show_gold = true, show_bind_gold = true, show_coin = true,
		show_silver_ticket = true, show_shengwang_xinjing = is_show_honor,
		show_chivalrous = is_show_chivalrous,
	}
	
	if self.money_bar ~= nil then
		if is_show then
			self.money_bar:SetMoneyShowInfo(0, 0, show_params)
			self.money_bar:AddAllListen()
			self.node_list["money_tabar_pos"]:SetActive(true)
			self.money_bar:Flush()
		else
			self.money_bar:UnAllListen()
			self.node_list["money_tabar_pos"]:SetActive(false)
		end
	elseif is_show then
		self.money_bar = MoneyBar.New()
		self.money_bar:SetIconClickCallBack(function() self:Close() end)
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end

function ItemTip:ReleaseTipsPanel()
	self.shenping_base_pingfen_per = 0
	self.market_sell_num = 1
	self.item_cell = nil
	self.need_hide_buy_btn = false
	if self.base_tips then
		self.base_tips:DeleteMe()
		self.base_tips = nil
	end
	if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.MainUIView, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	self:DestroyBackgroundCellLoader()
end

function ItemTip:FlushView()
	self.node_list.background_root:SetActive(false)
	self.market_sell_num = 1
	self.base_tips:Reset()
	self:ShowTipContent()
	self:ShowOperationState()
end

--data = {item_id=100....} 如果背包有的话最好把背包的物品传过来
function ItemTip:SetData(data, fromView, param_t, item_pos_x, btn_callback_event)
	if not data then
		return
	end

	if type(data) == "string" then
		self.data = CommonStruct.ItemDataWrapper()
		self.data.item_id = data
	else
		self.data = __TableCopy(data)
		if self.data.param == nil then
			self.data.param = CommonStruct.ItemParamData()
		end
	end
	self.from_view = fromView or ItemTip.FROM_NORMAL
	self.handle_param_t = param_t or {}
	self.btn_callback_event = btn_callback_event
	self:Open()
end

function ItemTip:OperationClickHandler(tag)
	if self.data == nil then
		return
	end

	self.handle_type = tag
	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if item_cfg == nil then
		return
	end

	if self.handle_type == ItemTip.QUICK_ADDITEM then
		local cur_data_conf = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		local gm_additem_num = 10
		if cur_data_conf and cur_data_conf.pile_limit and cur_data_conf.pile_limit == 999 then
			gm_additem_num = 999
		end
		SysMsgWGCtrl.SendGmCommand("additem", self.data.item_id .." ".. gm_additem_num .. " " .. 0)
	elseif self.handle_type == ItemTip.HANDLE_EQUIP then		--装备
		if ItemWGData.GetIsXiaogGui(self.data.item_id) then
			local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(self.data.item_id)
			local equip_index = xiaogui_cfg.impguard_type - 1
			BagWGCtrl.Instance:SendUseItem(self.data.index, 1, equip_index)
		elseif ItemWGData.GetIsFabao(self.data.item_id) then
			EquipmentWGCtrl.Instance:SendEquipCrossEquipOpera(1, self.data.index)
		else
			local equip_part = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
			if equip_part ~= -1 then
			 	BagWGCtrl.Instance:SendUseItem(self.data.index, 1, equip_part, item_cfg.need_gold)
			end
		end
	elseif self.handle_type == ItemTip.HANDLE_COMPOSE then		--合成
		if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
			local open_panel = item_cfg.open_panel

			-- 小鬼合成跳转
			if ItemWGData.GetIsXiaogGui(item_cfg.id) then
				FunOpen.Instance:OpenViewNameByCfg(open_panel)
			else
				local equip_part = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
				local can_compose, tab_str, compose_cfg = EquipmentWGData.Instance:GetEquipIsCanCompose(self.data)

				-- -- 身上仙器 跳 合成
				-- if self.from_view == ItemTip.FROM_BAG_EQUIP and (equip_part == GameEnum.EQUIP_INDEX_XIANJIE or equip_part == GameEnum.EQUIP_INDEX_XIANZHUO) then
				-- 	local equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(item_cfg)
				-- 	local is_can_compose, jump_tab_str = EquipmentWGData.Instance:GetWearEquipCanXQCompose(equip_body_index)

				-- 	if is_can_compose then
				-- 		FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, jump_tab_str, {jump_item_id = self.data.item_id})
				-- 	end
				-- else
				-- 	local is_xqsc, product_data = ComposeWGData.Instance:GetIsXQSCStuff(self.data)
				-- 	-- 背包仙器合成仙器材料跳转

				-- 	if is_xqsc then
				-- 		tab_str = "other_xianqi_stuff"
				-- 		FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, tab_str,
				-- 						{to_ui_name = 0, open_param = product_data.sub_type, to_ui_param = product_data.child_type})
				-- 	else
				-- 		-- 其他装备 跳合成
				-- 		-- if equip_part <= GameEnum.EQUIP_INDEX_XIANFU then
				-- 		-- 	can_compose, tab_str, compose_cfg = EquipmentWGData.Instance:GetEquipIsCanCompose(self.data)
				-- 		-- end
				-- 		can_compose, tab_str, compose_cfg = EquipmentWGData.Instance:GetEquipIsCanCompose(self.data)
				-- 	end
				-- end

				if can_compose then
					local open_param, to_ui_param
					local acc_list = EquipmentWGData.Instance:GetEquinHeChengAccordionDataList(compose_cfg.title_index)
					if not IsEmptyTable(acc_list) then
						for acc_k, acc_v in ipairs(acc_list) do
							if acc_v.name_type == compose_cfg.type and acc_v.star_level == compose_cfg.compose_equip_best_attr_num then
								open_param = acc_k
								for child_k, child_v in ipairs(acc_v.child) do
									if child_v.order == compose_cfg.order then
										to_ui_param = child_k
										break
									end
								end
							end
						end

						if equip_part == GameEnum.EQUIP_INDEX_XIANJIE or equip_part == GameEnum.EQUIP_INDEX_XIANZHUO then
							FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, tab_str, {to_ui_name = 0, open_param = open_param, to_ui_param = to_ui_param, jump_to_equip_data = self.data})
						else
							FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, tab_str, {to_ui_name = 0, open_param = open_param, to_ui_param = to_ui_param})
						end
					end
				end
			end
		else
			local config = ComposeWGData.Instance:GetComposeCfgByStuffId2(self.data.item_id)
			if config then
				local role_level = RoleWGData.Instance.role_vo.level
				if role_level < config.level then
					SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Map.OpenLevel, RoleWGData.GetLevelString(config.level)))
					return
				end

				local index = config.big_type * 10 + config.type
				local remind_tab_list = {big_type = config.big_type, type = config.type, sub_type = config.sub_type, index = config.child_type}
				local jump_index = ComposeWGData.Instance:GetRealJumpIndexByType(remind_tab_list)
				FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, index, {open_param = config.sub_type, sub_view_name = jump_index})
			end
		end
	elseif self.handle_type == ItemTip.HANDLE_EXCHANGE then		--兑换
		if self.from_view == ItemTip.FROM_STORGE_ON_GUILD_STORGE then --新增关于帮派仓库兑换的逻辑判断
			local star_level = self.data.star_level or 0
			local need_price = GuildCangKuWGData.Instance:GetStorageAccessScore(item_cfg.order,item_cfg.color,star_level)
			local has_score = GuildCangKuWGData.Instance:GetStorgeScore()
			if need_price > has_score then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.CangKuScoreNot)
				return
			end
			GuildWGCtrl.Instance:SendStorgetOutItem(self.data.index, 1, self.data.item_id)
		end
    elseif self.handle_type == ItemTip.TXS_ACTIVE then			--同心锁激活
        ViewManager.Instance:Open(GuideModuleName.Marry, TabIndex.marry_hunjie)
    elseif self.handle_type == ItemTip.HANDLE_BABY_DECOMPOSE then  --仙娃分解
    	local ok_func = function ()
			MarryWGCtrl.Instance:SendBabyOperaReq(BABY_REQ_TYPE.BABY_REQ_TYPE_DISPOSE_CARD, self.data.item_id)
		end
		
    	TipWGCtrl.Instance:OpenAlertTips(Language.Marry.AlerTips, BindTool.Bind(ok_func, self))
    elseif self.handle_type == ItemTip.BOSS_REFRESH then --boss刷新，召唤卡
        BossWGCtrl.Instance:DoOperaBossRefresh(self.data.item_id)
    elseif self.handle_type == ItemTip.HANDLE_SALE then
		self:OnOpenPopNum()

	elseif self.handle_type == ItemTip.HANDLE_STORGE then
		RoleBagWGCtrl.Instance.view:HandleItemTipCallBack(self.data, self.handle_type, self.handle_param_t)

	elseif self.handle_type == ItemTip.HANDLE_BACK_BAG or self.handle_type == ItemTip.HANDLE_TAKEOFF then
		if self.from_view == ItemTip.FROM_SJ_JC_OFF then
			EquipmentWGCtrl.Instance:TakeOffSjJcCell(self.handle_param_t)
		elseif ItemWGData.Instance:GetIsQingyuanEquip(self.data.item_id) and self.handle_type ~= ItemTip.HANDLE_BACK_BAG then
			MarryWGCtrl.Instance:SendTakeOffEquip()
		else
			if self.handle_type == ItemTip.HANDLE_BACK_BAG then
				if self.from_view == ItemTip.FROM_STORGE_ON_BAG_STORGE then
		   			RoleBagWGCtrl.Instance.view:HandleItemTipCallBack(self.data, self.handle_type, self.handle_param_t)
		   		end
			elseif self.handle_type == ItemTip.HANDLE_TAKEOFF then
				if ItemWGData.GetIsFabao(self.data.item_id) then
					EquipmentWGCtrl.Instance:SendEquipCrossEquipOpera(0, 0)
				elseif self.from_view == ItemTip.FROM_BAG_EQUIP then
					local have_inlay_forge = EquipmentWGData.Instance:GetWearEquipHaveInlayFogre(self.data.index)
					if have_inlay_forge then
						EquipmentWGCtrl.Instance:OpenTakeOffAlertTips(Language.Equip.TakeOffEquipTip, function()
							RoleWGCtrl.Instance:CSTakeOffEquip(self.data.index)
						end)
					else
						RoleWGCtrl.Instance:CSTakeOffEquip(self.data.index)
					end
				elseif ItemWGData.GetIsXiaogGui(self.data.item_id) then
					RoleWGCtrl.Instance:SendImpGuardOperaReq(IMP_GUARD_REQ_TYPE.IMP_GUARD_REQ_TYPE_TAKEOFF,self.data.pos)
				-- 四象魂骨卸下
				elseif item_cfg.sub_type == GameEnum.E_TYPE_SIXIANG_BONE then
					local fight_soul_type, bone_part = FightSoulWGData.GetBoneParamByItemId(self.data.item_id)
					local is_wear, slot = FightSoulWGData.Instance:GetTypeIsWear(fight_soul_type)
					if not is_wear then
						SysMsgWGCtrl.Instance:ErrorRemind(Language.FightSoul.UpLevelLimitDesc5)
					else
						FightSoulWGCtrl.Instance:ReqFightSoulOp(FIGHT_SOUL_OP_TYPE.BONE_PUT_DOWN, slot, bone_part, self.data.bag_index)
					end
			   	end
		   	end
		end
	elseif self.handle_type == ItemTip.HANDLE_HECHENG_PUTON then --已穿戴小鬼合成
		if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT and item_cfg.open_panel ~= nil and item_cfg.open_panel ~= "" and item_cfg.open_panel ~= 0 then
			local open_panel = item_cfg.open_panel
			FunOpen.Instance:OpenViewNameByCfg(open_panel)
		end
	elseif self.handle_type == ItemTip.HANDLE_HUANHUA then       --已穿戴小鬼幻化
		if ItemWGData.GetIsXiaogGui(self.data.item_id) then
			RoleWGCtrl.Instance:SendImpGuardOperaReq(IMP_GUARD_REQ_TYPE.IMP_GUARD_REQ_TYPE_HUANHUA, self.data.pos)
		end
 	elseif self.handle_type == ItemTip.HANDLE_USE or self.handle_type == ItemTip.HANDLE_DRESS then			--使用/穿戴
 		local fun_open_name = item_cfg.funopen_limit or ""
		local show_fun_limit = false
		if fun_open_name ~= "" then
			local is_open = FunOpen.Instance:GetFunIsOpened(fun_open_name)
			show_fun_limit = not is_open
		end

		if show_fun_limit then
			local fun_cfg = FunOpen.Instance:GetFunByName(fun_open_name) or {}
			local fun_name = fun_cfg.show_name or ""
			local str = string.format(Language.Tip.FunOpenLimit, TIPS_COLOR.SOCRE, fun_name)
			SysMsgWGCtrl.Instance:ErrorRemind(str)
			return
		end

 		local prof = RoleWGData.Instance:GetRoleProf() or 0
 		if item_cfg.limit_prof and item_cfg.limit_prof ~= 5 and item_cfg.limit_prof ~= prof then
 			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ProfDif)
 			return
 		end

 		if RoleWGData.Instance.role_vo.level >= item_cfg.limit_level then
 			-- 称号直接跳转到称号界面
 			if item_cfg.use_type == 3 then
 				self:Close()
				 local title_id = item_cfg.param1
				 local diy_cfg = TitleWGData.Instance:GetDiyTitleCfg(title_id)
				 if diy_cfg then
					 TitleWGCtrl.Instance:OpenDiyTitleNameView(title_id)
				 else
					 RoleWGCtrl.Instance:OpenRoleTitleView(title_id)
				 end

				return
 			end

 			if item_cfg.item_type ~= nil and item_cfg.item_type ~= "" and item_cfg.item_type == CLIENT_ITEM_TYPE.AWAKE_SKILL then
 				self:Close()
 				local awake_data = SkillWGData.Instance:GetAwakeForItem()
 				if awake_data == nil or awake_data[item_cfg.id] == nil then
 					SysMsgWGCtrl.Instance:ErrorRemind(Language.Skill.IsNotCanAwake)
 					return
 				end

 				self:OpenPanelByName(item_cfg.open_panel)
 				return
 			end

	 		if item_cfg.click_use == 1 then
	 			-- 藏宝图道具特殊处理
	 			if item_cfg.id == 27861 or item_cfg.id == 27860 or item_cfg.id == 27859 then
	 				local bootybay_scene_id = BootyBayWGData.Instance:GetBootyBaySceneId()
	 				local scene_id = Scene.Instance:GetSceneId()
	 				local scene_type = Scene.Instance:GetSceneType()
	 				local baotu_cfg = BootyBayWGData.Instance:GetBaoTuCfgById(item_cfg.id)
	 				local item_data = {item_id = baotu_cfg.baotu_item , quality = baotu_cfg.color}
	 				--当前已经在副本内
	 				if scene_type == SceneType.BOOTYBAY_FB or scene_type == SceneType.TEAM_BOOTYBAY_FB then
	 					TipWGCtrl.Instance:ShowSystemMsg(Language.BootyBay.BaoTuDesc1)
						self:Close()
						return
	 				end
	 				--当前不在普通场景内
	 				if scene_type ~= SceneType.Common then
	 					TipWGCtrl.Instance:ShowSystemMsg(Language.BootyBay.BaoTuDesc2)
						self:Close()
						return
	 				end

					local bootybay_other_cfg = BootyBayWGData.Instance:GetOtherConfig()
					local role_level = RoleWGData.Instance.role_vo.level

					if bootybay_other_cfg.open_level > role_level then
						TipWGCtrl.Instance:ShowSystemMsg(Language.Boss.CaveBossTips)
						self:Close()
						return
					end

	 				if scene_id ~= bootybay_scene_id then
	 					if baotu_cfg ~= nil and baotu_cfg.color > 3 then
	 						BootyBayWGData.Instance:SetNowWabaoInfo(item_data)
	 						ViewManager.Instance:Open(GuideModuleName.BootyBayPreView)
	 					else
		 					local scene_logic = Scene.Instance:GetSceneLogic()
		 					if scene_logic ~= nil then
						   		local x, y = scene_logic:GetTargetScenePos(bootybay_scene_id)
						   		TaskWGCtrl.Instance:SendFlyByShoe(bootybay_scene_id, x or 0, y or 0, -1, false)
								BootyBayWGData.Instance:SetUseBaoTuInfo(item_data)
                                BootyBayWGData.Instance:SetWaBaoType(WABAO_TYPE.WABAO_TYPE_BAOTU)
                                BootyBayWGCtrl.Instance:FlushBootybaySceneFollowView()
		 					end
	 					end
					else
						BootyBayWGCtrl.Instance:SendBaoTuWaBao(item_data, item_data.quality == BootyBaySceneFollowView.MaxColor)
					end
					self:Close()
					ViewManager.Instance:Close(GuideModuleName.Bag)
					return
	 			end

				-- 龙珠道具
	 			if LongZhuWGData.Instance:IsLongZhuStuff(item_cfg.id) then
	 				self:Close()
					ViewManager.Instance:Open(GuideModuleName.Bag, TabIndex.rolebag_longzhu, "select_long_zhu", {item_id = item_cfg.id})
					return
				end

				-- 是否是增加红包雨轮次的道具
				if RedPacketRainWGData.Instance:IsConsumeId(item_cfg.id) then
					if ActivityWGData.Instance:GetActivityIsOpen(RedPacketRainWGData.Instance:GetActivityId()) then
						local activity_info = RedPacketRainWGData.Instance:GetAcitivityInfo() 
						if activity_info and activity_info.round_state ~= 0 then
							RedPacketRainWGCtrl.Instance:OpenSendView()
							self:Close()
							return
						else
							SysMsgWGCtrl.Instance:ErrorRemind(Language.RedPacketRain.EndStr)
							self:Close()
							return
						end

					else
						SysMsgWGCtrl.Instance:ErrorRemind(Language.RedPacketRain.ActivityNotOpen)
						self:Close()
						return
					end
				end

				-- 是否是开启红包雨的道具
				if RedPacketRainWGData.Instance:IsAddActivityConsumeId(item_cfg.id) then
					local activity_info = RedPacketRainWGData.Instance:GetAcitivityInfo()
					if activity_info then
						if activity_info.round_state ~= 0 then
							SysMsgWGCtrl.Instance:ErrorRemind(Language.RedPacketRain.NotAddActivityTips)
						else
							RedPacketRainWGCtrl.Instance:OpenSendAddActivityView(item_cfg.id)
						end
						self:Close()
						return
					else
						RedPacketRainWGCtrl.Instance:OpenSendAddActivityView(item_cfg.id)
						self:Close()
						return
					end
				end

				-- 捉鬼BOSS
				local zhuogui_boss_scene_cfg = FuBenPanelWGData.Instance:GetZhuoGuiBossSceneCfgByItemId(item_cfg.id)
				if zhuogui_boss_scene_cfg then
					local scene_type = Scene.Instance:GetSceneType()
					if scene_type ~= SceneType.GHOST_FB_GLOBAL then
						TipWGCtrl.Instance:ShowSystemMsg(Language.ZhuoGuiFuBen.OtherSceneUseItem)
						self:Close()
						return
					end

					FuBenPanelWGCtrl.Instance:SendZhuoGuiOperate(GHOST_FB_OPERATE_TYPE.ENTER_BAOTU_FB, zhuogui_boss_scene_cfg.seq)
					self:Close()
					return
				end

	 			--背包,仓库,扩展格子，特殊处理
	 			if item_cfg.id == COMMON_CONSTS.OpenBagItemId or item_cfg.id == COMMON_CONSTS.OpenBagItemId then
	 				TipWGCtrl.Instance:OpenAlertIconTips()
					self:Close()
					return
	 			end

				local is_dec_boss_item = MainuiWGData.Instance:IsDecTiredItem(item_cfg.id)
				if is_dec_boss_item ~= nil then
					FuBenWGCtrl.Instance:SendDecBossTired(is_dec_boss_item)
					self:Close()
					return
				end

	 			if item_cfg.max_open_num and item_cfg.max_open_num > 0 then
	 				local select_gift_data = ItemWGData.Instance:GetItemListInGift(self.data.item_id)
	 				local gift_item_cfg, big_type = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	 				if gift_item_cfg and gift_item_cfg.equip_star > 0 then
	 					select_gift_data = ItemWGData.Instance:GetResetGiftConfig(self.data.item_id)
	 				end
	 				FunctionChooseWGCtrl.Instance:SetSelectGifData(self.data.index, item_cfg.max_open_num, select_gift_data, self.data.item_id)
	 			else
 					BagWGCtrl.Instance:SendUseItem(self.data.index, self.handle_param_t.num, self.data.sub_type, item_cfg.need_gold)
	 			end

	 			if item_cfg.open_panel ~= "" then
	 				--测试需求，不带装备不给打开装备面板
	 				local temp_list = Split(item_cfg.open_panel, "#")
	 				if temp_list[1] == GuideModuleName.Equipment then
	 					local data_list = EquipWGData.Instance:GetDataList()
						if not data_list or IsEmptyTable(data_list) then
							SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NeedWearEquip)
							return
						end
	 				end

	 				self:OpenPanelByName(item_cfg.open_panel)
	 			end

	 		elseif item_cfg.click_use == 2 and (item_cfg.ignore_num == nil or item_cfg.ignore_num == 0)then					--批量使用

	 			if ItemWGData.Instance:GetItemNumInBagByIndex(self.data.index) == 1 then
	 				--使用一张挂机卡时，如果时间溢出，弹出溢出框
					if OfflineRestWGData.Instance:IsOverstep(self.data.item_id, 1) then
						OfflineRestWGCtrl.Instance:OpenOfflineOverstepView(self.data.item_id, 1)
						self:Close()
					else
						-- 特推宝箱，暂时屏蔽
						-- if ItemWGData.Instance:IsNeedGoldGift(self.data.item_id) then
						-- 	ViewManager.Instance:Open(GuideModuleName.NeedGoldGiftView, nil, "all", {select_item_id = self.data.item_id})
						-- else
	 						BagWGCtrl.Instance:SendUseItem(self.data.index, self.handle_param_t.num, self.data.sub_type, item_cfg.need_gold)
	 					-- end

	 				end
	 			else

					if ItemWGData.Instance:GetIsCanUseItem(self.data.item_id, item_cfg.use_daytimes) then
	 					OfflineRestWGCtrl.Instance:OpenUserOfflineView(self.data.item_id)
	 				else
						BagWGCtrl.Instance:SendUseItem(self.data.index, GameMath.Round(1), 0, item_cfg.need_gold)
	 				end
	 			end
	 		elseif item_cfg.click_use == 2 and item_cfg.ignore_num == 1 and self.from_view ~= ItemTip.FROM_PREVIEW_SHOW_ITEM then
	 			BagWGCtrl.Instance:SendUseItem(self.data.index, self.data.num)
	 		elseif (item_cfg.click_use == 0 and item_cfg.open_panel ~= "") or self.from_view == ItemTip.FROM_PREVIEW_SHOW_ITEM then

 				--测试需求，不带装备不给打开装备面板
 				local temp_list = Split(item_cfg.open_panel, "#")
 				if temp_list[1] == GuideModuleName.Equipment then
 					local data_list = EquipWGData.Instance:GetDataList()
					if not data_list or IsEmptyTable(data_list) then
						SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NeedWearEquip)
						return
					end
 				end

				--打开的如果是某个活动需要判断活动是否开启
				if item_cfg.open_panel_act_id and item_cfg.open_panel_act_id ~= "" then
					local is_open = ActivityWGData.Instance:GetActivityIsOpen(item_cfg.open_panel_act_id)
					if not is_open then
						SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
						self:Close()
						return
					end
				end

				-- 是否为图鉴
				local is_tujian = ShanHaiJingWGData.Instance:GetTJResolveShowCfg(self.data.item_id) ~= nil
				if is_tujian then
					ShanHaiJingWGData.Instance:SetTJTips(false)
					ShanHaiJingWGData.Instance:SetJumpTuJianInfo(self.data.item_id)
					FunOpen.Instance:OpenViewByName(GuideModuleName.ShanHaiJingView, nil, { flush_jump_tujian = true })
					self:Close()
					return
				end

				-- 是否是开启红包雨的道具
				if RedPacketRainWGData.Instance:IsAddActivityConsumeId(item_cfg.id) then
					local activity_info = RedPacketRainWGData.Instance:GetAcitivityInfo()
					if activity_info then
						if activity_info.round_state ~= 0 then
							SysMsgWGCtrl.Instance:ErrorRemind(Language.RedPacketRain.NotAddActivityTips)
						else
							RedPacketRainWGCtrl.Instance:OpenSendAddActivityView(item_cfg.id)
						end
						self:Close()
						return
					else
						RedPacketRainWGCtrl.Instance:OpenSendAddActivityView(item_cfg.id)
						self:Close()
						return
					end
				end

	 			self:OpenPanelByName(item_cfg.open_panel)
	 		end
	 	else
			local level_str = RoleWGData.GetLevelString(item_cfg.limit_level)
	 		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.UseLevelLimitUse, level_str))
	 	end

 	elseif self.handle_type == ItemTip.BAOXIANG_QUCHU then				--从寻宝仓库取出
        if nil ~= self.data and self.handle_param_t ~= nil then
            TreasureHuntWGCtrl.Instance:GetOutOneItem(self.handle_param_t.fromIndex - 1)
        end
 	elseif self.handle_type == ItemTip.JINGLING_BAOXIANG_QUCHU then		--从精灵寻宝仓库取出

	elseif self.handle_type == ItemTip.HUNT_THUNDER_BAOXIANG_QUCHU then		
        if nil ~= self.data and self.handle_param_t ~= nil then
			TreasureHuntThunderWGCtrl.Instance:SendCSThunderDrawRequest(THUNDER_DRAW_OPERATE_TYPE.GET_ONE_STORGE, self.handle_param_t.fromIndex - 1)
        end
	elseif self.handle_type == ItemTip.SHICHANG_CHEHUI then
		local ok_func = function ()
			-- 撤回物品
			MarketWGCtrl.Instance:SendRemoveGoods(self.data.auction_index)
		end
		if self.data.last_auction_id > 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Market.CanNotXiaJia)
			self:Close()
			return
		end
		TipWGCtrl.Instance:OpenAlertTips(Language.Market.AlerTips, BindTool.Bind(ok_func, self))
	elseif self.handle_type == ItemTip.SHICHANG_GOUMAI then
		if self.from_view == ItemTip.FROM_PANIC_ITEM then
			ServerActivityWGCtrl.Instance:SendRAPanicBuyOperaReq(RA_PANICBUY_OPERA_TYPE.RA_PANICBUY_OPERA_BUY, self.handle_param_t.seq)
			return
		end
		MarketWGCtrl.Instance:OpenBuyMarketGoodsAlert(self.data.auction_index, self.data.has_password, self.data.total_price, self.data.item_id)

	elseif self.handle_type == ItemTip.RONGHE then
		if ItemWGData.Instance:GetIsQingyuanEquip(self.data.item_id) then
			local qingyuan_equip = MarryWGData.Instance:GetEquipCfgById(self.data.item_id) or {}
			if nil ~= qingyuan_equip then
				MarryWGCtrl.Instance:SendQingyuanUpLevel(self.data.item_id, qingyuan_equip.slot_idx)
			end
		end

	elseif self.handle_type == ItemTip.HANDLE_FORGE then
		local param_t = nil
		if self.from_view == ItemTip.FROM_BAG_EQUIP then
			param_t = {item_index = self.data.index}
			FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, "equipment_strength", param_t)
		end

	elseif self.handle_type == ItemTip.HANDLE_RECOVER then
		if(self.from_view == ItemTip.FROM_BAG_ON_BAG_SALE and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_JINGLING) then
			self:OnOpenPopNum()
		elseif item_cfg.sub_type == GameEnum.EQUIP_TYPE_JINGLING then
			BagWGCtrl.Instance:SendDiscardItem(self.data.index, self.data.num, self.data.item_id, self.data.num, 1)
		elseif ItemWGData.Instance:GetIsQingyuanEquip(self.data.item_id) then
			BagWGCtrl.Instance:SendDiscardItem(self.data.index, self.data.num, self.data.item_id, self.data.num, 1)
		elseif item_cfg.recycltype == 2 and big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
			BagWGCtrl.Instance:SendDiscardItem(self.data.index, self.data.num, self.data.item_id, self.data.num, 1)
		else
			BagWGCtrl.Instance:SendDiscardItem(self.data.index, self.data.num, self.data.item_id, self.data.num, 1)
		end
	elseif self.handle_type == ItemTip.HANDLE_RONGLIAN then    -- 熔炼
		-- RoleBagWGCtrl.Instance:OpenMeltView()
		ViewManager.Instance:Open(GuideModuleName.RoleBagViewMeltingView)
	elseif self.handle_type == ItemTip.HANDLE_GONGXIAN then--帮派仓库界面 从背包中贡献
		if self.from_view == ItemTip.FROM_BAG_ON_GUILD_STORGE then
			self:OnOpenPopNum()
		else
			FunOpen.Instance:OpenViewByName(GuideModuleName.GuildView, "guild_cangku")
		end
	elseif self.handle_type == ItemTip.HANDLE_TAKEON then
		if self.from_view == ItemTip.FROM_SJ_JC_ON then
			EquipmentWGCtrl.Instance:TakeOnSjJcCell(self.data, self.handle_param_t)
		end
	elseif self.handle_type == ItemTip.HANDLE_QINGYUSN then
		FunOpen.Instance:OpenViewByName(GuideModuleName.Marry, "marry_jiehun")
	elseif self.handle_type == ItemTip.HANDLE_EQUIP_DECOMPOSE then
		if ItemWGData.Instance:GetIsQingyuanEquip(self.data.item_id) then
			BagWGCtrl.Instance:SendDiscardItem(self.data.index, self.data.num, self.data.item_id, self.data.num, 1)
		else
			self:OnOpenPopNum()
		end
	elseif self.handle_type == ItemTip.HANDLE_XUFEI_PUTON then
		local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(self.data.item_id)
		local is_use_bind_gold = 1
		local bind_gold = RoleWGData.Instance.role_info.bind_gold
		if (ItemWGData.GetIsUseBindGold(self.data.item_id) == 1 and bind_gold < xiaogui_cfg.bind_gold_price) or ItemWGData.GetIsUseBindGold(self.data.item_id) == 0 then
			is_use_bind_gold = 0
		end
		local lable_string = string.format(Language.Equip.XiaoGuiXuFei2, xiaogui_cfg.bind_gold_price)
		if ItemWGData.GetIsUseBindGold(self.data.item_id) == 1 then
			if bind_gold < xiaogui_cfg.bind_gold_price then
				lable_string = string.format(Language.Equip.XiaoGuiXuFei3, xiaogui_cfg.gold_price)
				is_use_bind_gold = 0
				TipWGCtrl.Instance:OpenAlertTips(lable_string, function ()
					RoleWGCtrl.Instance:SendImpGuardOperaReq(IMP_GUARD_REQ_TYPE.IMP_GUARD_REQ_TYPE_RENEW_PUTON,self.data.pos, is_use_bind_gold)
					self:Close()
				end, function () self:Close() end)
				return
			end
			RoleWGCtrl.Instance:SendImpGuardOperaReq(IMP_GUARD_REQ_TYPE.IMP_GUARD_REQ_TYPE_RENEW_PUTON,self.data.pos, is_use_bind_gold)
		else
			lable_string = string.format(Language.Equip.XiaoGuiXuFei1, xiaogui_cfg.gold_price)
			is_use_bind_gold = 0
			TipWGCtrl.Instance:OpenAlertTips(lable_string, function ()
				RoleWGCtrl.Instance:SendImpGuardOperaReq(IMP_GUARD_REQ_TYPE.IMP_GUARD_REQ_TYPE_RENEW_PUTON,self.data.pos, is_use_bind_gold)
				self:Close()
			end, function () self:Close() end)
		end
	elseif self.handle_type == ItemTip.HANDLE_XUFEI_INBAG then
		MainuiWGCtrl.Instance:OpenGuradInvalidTimeView()
		self:Close()

	elseif self.handle_type == ItemTip.HANDLE_BAOSHI_INLAY then
		FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, "equipment_baoshi")
	elseif self.handle_type == ItemTip.HANDLE_LINGYU_INLAY then
		FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, "equipment_lingyu")
	elseif self.handle_type >= ItemTip.FROM_CUSTOM_BTN then
		local btn_key = self.handle_type - ItemTip.FROM_CUSTOM_BTN
		if self.btn_callback_event[btn_key] ~= nil and self.btn_callback_event[btn_key].callback ~= nil then
			self:Close()
			self.btn_callback_event[btn_key].callback()
			return
		end
	elseif self.handle_type == ItemTip.HANDLE_PRESENT_SEND then
		if ProfessWallWGData.Instance:IsProfessGiftByItemId(self.data.item_id) then
			ViewManager.Instance:Open(GuideModuleName.ChooseProfessView)
		else
			local friend_list = SocietyWGData.Instance:GetFriendList()
			if #friend_list > 0 then
				SocietyWGCtrl.Instance:SendGoodsOpen(ItemTip.HANDLE_PRESENT_SEND,self.data)
			else
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.NoFriends)
			end
		end
	elseif self.handle_type == ItemTip.SHOP_BUY then --元宝商城、绑定元宝商城购买
		local item_buy_num = self.base_tips:GetBuyCount()
		if item_buy_num <= 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ChongXinShuRu)
			return
		end
		local shop_cfg = ShopWGData.Instance:GetShopCfgItemId(self.data.item_id)
		if not shop_cfg then
			return
		end

		--不能购买
		local is_can_cell, str = ShopWGData.Instance:IsCanCell(shop_cfg.seq, true)
		if not is_can_cell then
			if str then
				SysMsgWGCtrl.Instance:ErrorRemind(str)
			end
			return
		end

		--检测货币是否满足
		if not ShopWGData.Instance:CheckMoneyToBuy(shop_cfg.seq, item_buy_num, false, false) then
			local shop_cfg = ShopWGData.Instance:GetItemCfgBySeq(shop_cfg.seq)
			if shop_cfg then
				local itemid = COIN_ITEM_ID[shop_cfg.price_type]
				TipWGCtrl.Instance:OpenItemTipGetWay({item_id = itemid})
			end
			self:Close()
			return
		end

		if item_buy_num > ShopWGData.Instance:GetMaxBuyNum(shop_cfg.seq) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Shop.IsCanCell13)
			return
		end

		local function ok_func()
			--你策划要求仙玉>=100就弹多一个二次确认框
			local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
			if not item_cfg then
				return
			end
            local buy_count = item_buy_num
			local confirm_func = function ()
				ShopWGCtrl.Instance:SendShopBuy(item_cfg.id, buy_count, 0, 0, shop_cfg.seq)
			end

			if item_buy_num * shop_cfg.price >= 100 and shop_cfg.price_type == Shop_Money_Type.Type1 then
				local name_str = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
				TipWGCtrl.Instance:OpenAlertTips(string.format(Language.Common.CommonAlertFormat1, shop_cfg.price * item_buy_num, name_str, item_buy_num), confirm_func)
			else
				ShopWGCtrl.Instance:SendShopBuy(item_cfg.id, item_buy_num, 0, 0, shop_cfg.seq)
			end
		end

		if not ShopWGData.Instance:CheckBangYuToBuy(shop_cfg.seq, item_buy_num) then
			-- TipWGCtrl.Instance:OpenAlertTips(Language.Guild.BindGoldNo,ok_func)
			TipWGCtrl.Instance:OpenAlertByNotBindYu(ok_func)
			return
		end
		ok_func()
	elseif self.handle_type == ItemTip.SHICHANG_WORLD_SELL then
		-- 上架拍卖
		MarketWGCtrl.Instance:OpenMarketTipItemView(self.data)
	elseif self.handle_type == ItemTip.HANDLE_REPACE then
		if item_cfg.sub_type == GameEnum.E_TYPE_SIXIANG_BONE then
			local fs_data = FightSoulWGData.Instance
			local fight_soul_type, bone_part, suit_type = FightSoulWGData.GetBoneParamByItemId(self.data.item_id)
			local meet_list = fs_data:GetBonePartCanWearList(fight_soul_type, bone_part)
			local is_wear, slot_index = fs_data:GetTypeIsWear(fight_soul_type)
			if is_wear then
				FightSoulWGCtrl.Instance:OpenBoneSelectView(meet_list, slot_index, bone_part)
			end
		end
	elseif self.handle_type == ItemTip.HANDLE_ACTIVE then
		if LongZhuWGData.Instance:IsLongZhuStuff(self.data.item_id) then
			ViewManager.Instance:Open(GuideModuleName.Bag, TabIndex.rolebag_longzhu, "select_long_zhu", {item_id = self.data.item_id})
		end
	elseif self.handle_type == ItemTip.HANDLE_UP_LEVEL then
		if LongZhuWGData.Instance:IsLongZhuStuff(self.data.item_id) then
			ViewManager.Instance:Open(GuideModuleName.Bag, TabIndex.rolebag_longzhu, "select_long_zhu", {item_id = self.data.item_id})
		end
	--物品分解
	elseif self.handle_type == ItemTip.HANDLE_ITEM_RESOVLE then
		if TipWGData.Instance:GetHasItemResolve(self.data.item_id) then
			local bag_index = self.data.index
			local item_id = self.data.item_id
			BagWGCtrl.Instance:SnedCSItemResolveReq(bag_index,item_id,1)
		end
	elseif self.handle_type == ItemTip.HANDLE_FIGHT_SOUL_RESTORE then
		local exp_num, grade_stuff_id, grade_stuff_num = FightSoulWGData.Instance:GetReturnExpAndStuff(self.data)
		local exp_str, grade_stuff_str = "", ""
		if exp_num > 0 then
			local item_id = FightSoulWGData.Instance:GetFightSoulExpItemId()
			local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
			local item_name = Language.FightSoul.FightSoulExpName
			if item_cfg then
				exp_str = string.format("\n<color=%s>%s：</color>%s", ITEM_COLOR[item_cfg.color],
							item_name, CommonDataManager.ConverNumber(exp_num))
			end
		end

		if grade_stuff_id > 0 and grade_stuff_num > 0 then
			local item_cfg = ItemWGData.Instance:GetItemConfig(grade_stuff_id)
			if item_cfg then
				local temp_str = exp_num > 0 and "      " or "\n"
				grade_stuff_str = string.format("%s<color=%s>%s：</color>%s", temp_str, ITEM_COLOR[item_cfg.color],
												item_cfg.name, grade_stuff_num)
			end
		end

		local desc = string.format(Language.FightSoul.RestoreItemDesc, exp_str, grade_stuff_str)
		local ok_func = function()
			FightSoulWGCtrl.Instance:ReqFightSoulOp(FIGHT_SOUL_OP_TYPE.FIGHT_SOUL_RESTORE, self.data.index)
		end

		TipWGCtrl.Instance:OpenAlertTips(desc, ok_func)
	end
	self:Close()
end

function ItemTip:ShowOperationState()
	local handle_types = TipWGData.Instance:GetOperationLabelByType(self.data, self.from_view)
	local btn_info_list = {}

	if not IsEmptyTable(handle_types) then
		for i, v in ipairs(handle_types) do
			local temp = {}
			temp.btn_name = self.label_t[v]
			temp.btn_click = BindTool.Bind2(self.OperationClickHandler, self, v)

			if v == ItemTip.HANDLE_SALE or v == ItemTip.HANDLE_EQUIP_DECOMPOSE then
				btn_info_list[#btn_info_list + 1] = temp
			else
				table.insert(btn_info_list, 1, temp)
			end
		end
	end

	if self.btn_callback_event and (type(self.btn_callback_event) == "table") then
		for i,v in ipairs(self.btn_callback_event) do
			local temp = {}
			temp.btn_name = v.btn_text
			temp.spend_item = v.spend_item

			if v.btn_click == nil then
				temp.btn_click = BindTool.Bind2(self.OperationClickHandler, self, ItemTip.FROM_CUSTOM_BTN + i)	
			else
				temp.btn_click = v.btn_click
			end
	
			if v.show_red then
				temp.btn_red = v.show_red
			end
			btn_info_list[#btn_info_list + 1] = temp
		end
	end

	if #btn_info_list > 0 then
		self.base_tips:SetBtnsClick(btn_info_list)
	end
end

function ItemTip:ShowTipContent()
	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if item_cfg == nil then
		return
	end

	if self.from_view == ItemTip.FROM_MARKET_SHANGJIA or  self.from_view == ItemTip.FROM_MARKET_JISHOU or self.from_view == ItemTip.FROM_MARKET_SHANGJIA_CLICK then
		self.item_cell:SetItemTipFrom(ItemTip.FROM_MARKET_SHANGJIA)
	elseif self.from_view == ItemTip.FROM_CHATEXT then
		self.item_cell:SetItemTipFrom(ItemTip.FROM_CELL_CHATEXT)
	elseif item_cfg.sub_type == GameEnum.E_TYPE_XIANJIE_EQUI
		and (self.from_view == ItemTip.FROM_HOLY_EQUIP_BAG or self.from_view == ItemTip.FROM_BAG) then
		self.item_cell:SetItemTipFrom(ItemTip.FROM_HOLY_EQUIP_BAG)
	end

	-- 出场特效
	local tip_show_effect_id = tonumber(item_cfg.tip_show_effect)
	if tip_show_effect_id and tip_show_effect_id > 0 then
		self.base_tips:SetShowCCEffect(tip_show_effect_id)
	end

	self.item_cell:SetData(self.data)
	local force_show_top_bg, show_long_eff = false, false
	if ItemWGData.GetIsXiaogGui(self.data.item_id) then 			-- 小鬼
		self:ParseXiaogui(item_cfg)
		self:FlushXiaoGuiDisplayModel(item_cfg)
	elseif big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then		-- 装备
		if item_cfg.sub_type == GameEnum.E_TYPE_SIXIANG then	-- 四象
			self:ParseFightSoul(item_cfg, self.data)
		elseif item_cfg.sub_type == GameEnum.E_TYPE_SIXIANG_BONE then	-- 四象魂骨
			self:ParseFightSoulBone(item_cfg, self.data)
		elseif item_cfg.sub_type == GameEnum.E_TYPE_XIANJIE_EQUI then	-- 圣装
			self:ParseHolyEquip(item_cfg, self.data)
			
			local he_item_cfg = FairyLandEquipmentWGData.Instance:GetHolyEquipItemCfg(self.data.item_id)
			--仙界装备的特殊部位:显示顶部盘龙特效
			if he_item_cfg and FairyLandEquipmentWGData.Instance:GetIsSpecialType(he_item_cfg.part) then
				force_show_top_bg = true
				show_long_eff = true
			end
		else
			self:ParseEquip(item_cfg, self.data)
		end
	elseif ControlBeastsCultivateWGData.Instance:IsBeastAlchemyEquipItem(self.data.item_id) then
		self:ParseBeastAlchemyEquipItem(item_cfg, self.data)
	else
		self:ParseProp(item_cfg, big_type)
	end

	local display_type = item_cfg.is_display_role
	if display_type and display_type == DisplayItemTip.Display_type.TIANSHEN then
		local tianshen_cfg = TianShenWGData.Instance:GetTianShenActItemCfgByActId(self.data.item_id)
		if tianshen_cfg and tianshen_cfg.index then
			self.base_tips:SetWuXingInfo(TianShenWGData.Instance:GetTianShenWuXingByIndex(tianshen_cfg.index))
		end
	else
		self.base_tips:SetWuXingInfo(0)
	end

	self:ShowItemName(item_cfg, big_type, self.data)
	self:SetEquipColorBg(item_cfg.color or 0, force_show_top_bg)
	self.base_tips:SetOrnamentImage(item_cfg.color or 0, item_cfg.special_border or 0)
	if show_long_eff then--盘龙特效
		self:SetEquipTopLongEffectShow(item_cfg.color or 0, force_show_top_bg)
	end

	-- tip面板特效
	local tips_effect_name = item_cfg.tips_effect_name
	local tips_buttom_effect_name = item_cfg.tips_buttom_effect_name
	
	if tips_effect_name and tips_effect_name ~= "" then
		local bundle_name, asset_name = ResPath.GetEffectUi(tips_effect_name)
		self:SetEquipTipsPanelEffectShow(bundle_name, asset_name)
	elseif item_cfg.color >= GameEnum.ITEM_COLOR_SHINING_GOLD then
		local bundle_name, asset_name = ResPath.GetWuPinKuangEffectUi(TIPS_KUANG_QUALITY_EFFECT[item_cfg.color])
		self:SetEquipTipsPanelEffectShow(bundle_name, asset_name)
	end

	if tips_buttom_effect_name and tips_buttom_effect_name ~= "" then
		local bundle_name, asset_name = ResPath.GetEffectUi(tips_buttom_effect_name)
		self:SetEquipTipsPanelButtomEffectShow(bundle_name, asset_name)
	end
	
	self.base_tips:SetMarketPanel({price = self.data.total_price, item_id = self.data.item_id}) 				-- 设置市场售价
end

function ItemTip:ShowItemName(item_cfg, big_type, data)
	if item_cfg == nil or data == nil then
		return
	end

	-- 查看玩家
	local is_get_browse_data = self.from_view == ItemTip.FROME_BROWSE_ROLE or self.from_view == ItemTip.FORM_ROBOT_SHOW_INFO
	local str = item_cfg.name
	if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
		-- 四象
		if item_cfg.sub_type == GameEnum.E_TYPE_SIXIANG then
			if data.level and data.level > 0 then
				str = str .. " +" .. data.level
			end
		-- 四象魂骨
		elseif item_cfg.sub_type == GameEnum.E_TYPE_SIXIANG_BONE then
			local is_wear, fs_slot, bone_part = FightSoulWGData.Instance:GetBoneBagIndexIsWear(data.bag_index)
			if is_wear then
				local part_data = FightSoulWGData.Instance:GetBonePartDataBySlotPart(fs_slot, bone_part)
				local strength_level = part_data and part_data.slot_level or 0
				if strength_level > 0 then
					str = str .. " +" .. strength_level
				end
			end
		elseif item_cfg.sub_type == GameEnum.E_TYPE_XIANJIE_EQUI then
			if data.is_wear then
				local fle_data = FairyLandEquipmentWGData.Instance
				local item_id = data.item_id
				local he_data = fle_data:HolyEquipItemData(item_id)
				local strength_level = is_get_browse_data and (data.strength_level and data.strength_level or 0) or fle_data:GetPartStrengthenLevel(he_data.slot, he_data.part)
				if strength_level > 0 then
					str = str .. " +" .. strength_level
				end
			end
		else
			-- 加上升品的名字
			if self.data.index and not is_get_browse_data and self.data.frombody then
				local star_count = EquipmentWGData.Instance:GetNewEquipShengPinStarInfoByPart(self.data.index) or 0
				if star_count > 0 then
					local shengpin_cfg = EquipmentWGData.Instance:GetNewEquipShengPinCfg(star_count, self.data.index)
					if shengpin_cfg and shengpin_cfg.quality_grade and shengpin_cfg.quality_grade > 0 then
						str = str .. Language.Equip.shengPin_itemTip[shengpin_cfg.quality_grade + 1]
					end
				end
			end
			-- 真炼名字：真·name
			if 1 == self.data.param.is_refine then
				str = Language.Compose.ZhenLianName .. str
			end

			-- local param_suit_index = self.data.param.suit_index or -1
			-- if param_suit_index >= 0 and (self.from_view == ItemTip.FROM_BAG_EQUIP or self.from_view == ItemTip.FROM_EQUIPMENT) then
			-- 	str = "[" .. Language.Equip.TabSub3[param_suit_index + 1] .. "] " .. str
			-- elseif is_get_browse_data and param_suit_index >= 0 then
			-- 	str = "[" .. Language.Equip.TabSub3[param_suit_index + 1] .. "] " .. str
			-- end

			local strength_level = 0
			if self.data.frombody and not is_get_browse_data then
				local equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(item_cfg)
				strength_level = EquipmentWGData.Instance:GetStrengthLevelByIndex(equip_body_index)
			elseif self.data.param ~= nil and self.data.param.strengthen_level ~= nil then
				strength_level = self.data.param.strengthen_level
			end

			if strength_level > 0 and (self.data.frombody or is_get_browse_data) then
				str = str .. " +" .. strength_level
			end
		end
	end

	self.base_tips:SetItemName(ToColorStr(str, ITEM_COLOR[item_cfg.color]))
end

--解析装备tips
function ItemTip:ParseEquip(item_cfg, item_data)
	self.common_pingfen_num = 0 --基础评分
	self.comp_pingfen_num = 0 	--综合评分
	if item_data == nil or item_cfg == nil then
		return
	end

	local attribute = {}
	local equip_part = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
	local equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(item_cfg)
	self:ParseAttribute(attribute, item_cfg)
	self:InitDataParam(item_data, item_cfg, equip_part)
	local show_attr = true  
	if equip_part >= GameEnum.EQUIP_INDEX_HUNJIE then --装备类型超过同心锁（婚戒）不显示某些属性
		show_attr = false
	end

	local data_param = self.data.param
	local is_get_browse_data = self.from_view == ItemTip.FROME_BROWSE_ROLE or self.from_view == ItemTip.FORM_ROBOT_SHOW_INFO
	for i, v in pairs(attribute) do
		local list = attribute[i]
		if ATTR_STONE == i and show_attr then
			self:SetAttrStone()
		elseif ATTR_SHENGPIN == i and not is_get_browse_data then    			--升品属性
			self:SetAttrShengPin(list, item_cfg, equip_part)
		elseif ATTR_STONE_JL == i and data_param.stone_baptize_level and data_param.stone_baptize_level > 0 and self:CanShowEquipStoneJLAttr() then
			self:ShowAttrStoneJl(equip_body_index)
		elseif ATTR_LEGEND == i then 											-- 仙品属性
			self:SetXianPinAttrInfo(item_cfg)
		elseif ATTR_SUIT == i and data_param.self_suit_open and data_param.suit_open_num and data_param.suit_open_num > 0 then
			self:SetAttrSuit(item_cfg, equip_body_index)
		elseif ATTR_BASE == i then
			self:SetBaseAttrInfo(list)
		elseif ATTR_STRENGTHEN == i then
			self:SetAttrStrengthen(item_cfg)
		elseif ATTR_JIPING == i then --极品
			self:SetAttrJiPing(item_cfg)
		elseif ATTR_PINK_SUIT == i then
			self:SetEquipSpecialAttr(item_cfg, EQUIP_SPECIAL_ATTR_TYPE.PINK)
		elseif ATTR_GOD_SUIT == i then
			self:SetEquipSpecialAttr(item_cfg, EQUIP_SPECIAL_ATTR_TYPE.GOLD)
		elseif ATTR_MORE_COLOR == i then
			self:SetEquipSpecialAttr(item_cfg, EQUIP_SPECIAL_ATTR_TYPE.COLOR)
		elseif ATTR_XIANMENG_CANGKU == i then
			self:SetXianMengCangKuAttr(self.data)
		elseif REMIND_HOW_COMPOSE == i then
			self:ShowEquipTipsCompose()
		elseif ATTR_EQUIP_SKILL == i then
			self:SetEquipSkillShow()
		elseif ATTR_LINGYUE == i and show_attr then
			self:SetAttrLingYu()
		elseif ATTR_SHENJI_STAR == i then
			self:SetAttrShenJiStar()
		elseif ATTR_SHENJI_SKILL == i then
			self:SetAttrShenJiSkill(item_cfg)
		elseif ATTR_ITEM_SKILL == i then
			self:SetAttrItemSkill(item_cfg)
		elseif ATTR_NEWYINJI == i then			--新印记属性
			self:ShowAttrNewYinJi(item_cfg, item_data, equip_part)
		elseif ATTR_KEYINFU == i then			--刻印符属性
			self:ShowAttrKeYinFu(item_cfg, item_data, equip_part)
		elseif ATTP_YULING == i and show_attr then
			self:SetAttrYuLing(equip_part) 
		elseif ATTP_ZHUSHEN == i then		--铸神
			self:SetAttrZhuShen(equip_part) 
		elseif ATTP_XILIAN == i then
			self:SetAttrXilian(item_cfg, equip_body_index)
		end
	end

	self:SetEquipNormalInfo()
	self:SetEquipTitleNormalInfo()
end

function ItemTip:SetEquipTitleNormalInfo()
	local data = self.data
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	if not item_cfg then
		return
	end

	local equip_part = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
	local sex = RoleWGData.Instance:GetRoleSex()
	local prof = RoleWGData.Instance:GetRoleProf()

	local rich_part_text = ""

	if TianShenWGData.Instance:IsTianShenEquip(item_cfg.sub_type) then
		if data.grade_level then
			rich_part_text = string.format(Language.Tip.Order, data.grade_level) .. Language.TianShen.EquipNameList[TianShenWGData.Equip_Pos[item_cfg.sub_type]]
		else
			rich_part_text = Language.TianShen.TianShenShenShi
		end
	elseif HiddenWeaponWGData.IsHiddenWeapon(item_cfg.sub_type) then
		rich_part_text = HiddenWeaponWGData.Instance:GetEquipPartStr(item_cfg.id)
	else
		local sex_str = Language.Common.SexName[item_cfg.limit_sex] or ""
		local order = item_cfg.order or ""
		local Stone = Language.Stone[equip_part] or ""
		local prefix_text = ""
		if EquipBodyWGData.Instance:GetEquipIsSpecailOrder(item_cfg.order) then
			prefix_text = EquipBodyWGData.Instance:GetEquipSpecailOrderSignStr(item_cfg.order)
		else
			prefix_text = string.format(Language.Tip.Order, item_cfg.order)
		end

		rich_part_text = prefix_text .. sex_str .. Stone
	end

	-- if not TianShenWGData.Instance:IsTianShenEquip(item_cfg.sub_type) then

	-- elseif data.grade_level then
	-- 	rich_part_text = string.format(Language.Tip.Order, data.grade_level) .. Language.TianShen.EquipNameList[TianShenWGData.Equip_Pos[item_cfg.sub_type]]
	-- else
	-- 	rich_part_text = Language.TianShen.TianShenShenShi
	-- end

	local mix_limit_prof = EquipWGData.GetEquipProfLimit(equip_part, item_cfg.order)
	local limit_sex = item_cfg.limit_sex or 0
	local rich_type_text = Language.Common.ProfName[limit_sex][item_cfg.limit_prof]
	local is_limit_zhuanzhi_prof = item_cfg.is_limit_zhuanzhi_prof and tonumber(item_cfg.is_limit_zhuanzhi_prof) or 0
	local zhuan_str = ""
	if item_cfg.is_zhizun == 1 then
		rich_type_text = Language.Common.ProfName[limit_sex][item_cfg.limit_prof]
	elseif mix_limit_prof > 0 and item_cfg.limit_prof ~= 5 and is_limit_zhuanzhi_prof == 1 then
		--2025.6.9 策划要求屏蔽转数.
		-- zhuan_str = string.format(Language.F2Tip.Zhuan, CommonDataManager.GetDaXie(mix_limit_prof))
		rich_type_text = Language.Common.ProfName[limit_sex][mix_limit_prof * 10 + item_cfg.limit_prof] .. zhuan_str
	elseif mix_limit_prof > 0 and item_cfg.limit_prof == 5 and is_limit_zhuanzhi_prof == 1 then
		-- zhuan_str = string.format(Language.F2Tip.Zhuan, CommonDataManager.GetDaXie(mix_limit_prof))
		rich_type_text = rich_type_text .. zhuan_str
	end

	local sex_color = TIPS_COLOR.SOCRE
	local prof_color = TIPS_COLOR.SOCRE
	if self.from_view ~= ItemTip.FROME_BROWSE_ROLE and self.from_view ~= ItemTip.FORM_ROBOT_SHOW_INFO then
		local zhuanzhi_num = RoleWGData.Instance:GetZhuanZhiNumber()
		local sex_limit = item_cfg.limit_sex ~= sex and item_cfg.limit_sex ~= GameEnum.SEX_NOLIMIT
		local prof_limit = item_cfg.limit_prof ~= prof and item_cfg.limit_prof ~= GameEnum.ROLE_PROF_NOLIMIT
		local prof_level_limit = zhuanzhi_num < mix_limit_prof
		sex_color = sex_limit and COLOR3B.D_RED or sex_color
		prof_color = (prof_limit or (prof_level_limit and is_limit_zhuanzhi_prof == 1)) and COLOR3B.D_RED or prof_color
	end

	self.base_tips:SetEquipSocre(string.format(Language.Tip.ZhuangBeiLeiXing_1, sex_color, rich_part_text))
	self.base_tips:SetSyntheticalSocre(string.format(Language.Tip.ZhuangBeiProf, prof_color, rich_type_text))
end

function ItemTip:SetEquipNormalInfo()
	local data = self.data
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)

	local normal_attr_info = {}
	local str_limit_level = RoleWGData.GetLevelString(item_cfg.limit_level)
	local str_cultivation_limit = CultivationWGData.Instance:GetEquipLevelLimitStr()
	normal_attr_info[1] = {name = Language.F2Tip.LvName, label = str_limit_level..str_cultivation_limit}
	normal_attr_info[2] = {name = Language.F2Tip.PingFen, label = math.ceil(self.common_pingfen_num)}
	normal_attr_info[3] = {name = Language.F2Tip.CompPingFen, label = math.ceil(self.comp_pingfen_num)}
	self.base_tips:SetNormalAttribute(normal_attr_info)
end

function ItemTip:OpenPanelByName(panel_name)
	if self.data and self.data.item_id == COMMON_CONSTS.GuildTanheItemId and RoleWGData.Instance.role_vo.guild_id == 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.PleaseJoinGuild)
		return
	end

	local item_id = nil
	if self.data then
		item_id = self.data.item_id
	end
	FunOpen.Instance:OpenViewNameByCfg(panel_name, item_id)
end

-- 打开数字键盘
function ItemTip:OnOpenPopNum()
	if self.data == nil then
		return
	end
	local pop_num_view = TipWGCtrl.Instance:GetPopNumView()
	if pop_num_view then
		local maxnum = 1
		if self.data.index then
			maxnum = ItemWGData.Instance:GetItemNumInBagByIndex(self.data.index)
		end
		if maxnum == 1 then  --数量为1时不弹
			self:OnOKCallBack(maxnum)
		else
			pop_num_view:Open()
			pop_num_view:SetText(maxnum)
			pop_num_view:SetMaxValue(maxnum)
		end
		pop_num_view:SetOkCallBack(BindTool.Bind1(self.OnOKCallBack, self))
	end
end

-- 数字键盘确定按钮回调
function ItemTip:OnOKCallBack(num)
	if self.data == nil then
		return
	end
	self.item_num = tonumber(num)
	local maxnum = ItemWGData.Instance:GetItemNumInBagByIndex(self.data.index)
	if self.item_num > maxnum then
		self.item_num = maxnum
	elseif self.item_num == 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ChongXinShuRu)
		return
	end
	self.handle_param_t.num = self.item_num

	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(self.data.item_id)

	if self.handle_type == ItemTip.HANDLE_USE then
		if OfflineRestWGData.Instance:IsOverstep(self.data.item_id, self.handle_param_t.num) then
			OfflineRestWGCtrl.Instance:OpenOfflineOverstepView(self.data.item_id, self.handle_param_t.num)
		else
			BagWGCtrl.Instance:SendUseItem(self.data.index, self.handle_param_t.num, self.data.sub_type, item_cfg.need_gold)
		end

	elseif self.handle_type == ItemTip.HANDLE_SALE then
		if item_cfg and item_cfg.is_rare and item_cfg.is_rare == 1 then
			local ok_func = function()
				local item_num = ItemWGData.Instance:GetItemNumInBagByIndex(self.data.index, self.data.item_id)
				BagWGCtrl.Instance:SendDiscardItem(self.data.index, num, self.data.item_id, item_num, 0)
			end
			TipWGCtrl.Instance:OpenAlertTips(Language.Tip.IsRare, ok_func)
			return
		end
		local item_num = ItemWGData.Instance:GetItemNumInBagByIndex(self.data.index, self.data.item_id)
		BagWGCtrl.Instance:SendDiscardItem(self.data.index, num, self.data.item_id, item_num, 0)
	elseif self.from_view == ItemTip.FROM_BAG then
		if self.handle_type == ItemTip.HANDLE_EQUIP_DECOMPOSE then
			if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
				local legend_num = self.data.param and self.data.param.star_level or 0
				local decompose_cfg = EquipmentWGData.Instance:GetEquipDecomposeByID(item_cfg.id, legend_num)
				if not decompose_cfg then
					return
				end

				local desc = EquipmentWGData.Instance:GetEquipDeComposeDescByItemid(item_cfg.id, legend_num)

				local index_list = {}
				index_list[1] = self.data.is_bind
				local index_list = {}
				index_list[1] =self.data.index
				local ok_func = function()
					EquipmentWGCtrl.Instance:SendEquipComposeOperaReq(COMPOSE_EQUIP_OPERA_TYPE.EQUIP_OPERA_TYPE_DECOMPOSE, self.data.item_id, decompose_cfg.decompose_equip_best_attr_num, index_list)
				end

				TipWGCtrl.Instance:OpenAlertTips(desc, ok_func)
				return
			end
			local tab_index = ItemWGData.ComposeGetTypeById(self.data.item_id)
			FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, tab_index * 1000, {is_stuff = true, item_id = self.data.item_id})
		end
 	elseif self.from_view == ItemTip.FROM_BAG_ON_BAG_SALE_SHITU then
 		MasterWGCtrl.Instance:HandleItemTipCallBack(self.data, self.handle_type, self.handle_param_t)
	end
end

-- 解析四象tips
function ItemTip:ParseFightSoul(item_cfg, data)
	if IsEmptyTable(item_cfg) or IsEmptyTable(data) then
		return
	end

	local rich_type = string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE,
									Language.F2Tip.DisplayType[item_cfg.is_display_role])
	self.base_tips:SetEquipSocre(rich_type)

	local rich_level = string.format(Language.Tip.ShiYongDengJiNew, TIPS_COLOR.SOCRE,
								RoleWGData.GetLevelString(item_cfg.limit_level))
	self.base_tips:SetSyntheticalSocre(rich_level)

	-- 物品描述
	local desc_info = {}
	local description = item_cfg.description
	if description and description ~= "" then
		desc_info[#desc_info + 1] = {title = Language.F2Tip.WuPinMiaoShu, desc = description}
	end
	self.base_tips:SetItemDesc(desc_info)

	local is_per, per_value, per_str
	local function get_attr_list(attr_list)
		local temp_list = {}
		if IsEmptyTable(attr_list) then
			return temp_list
		end

		for k, v in pairs(attr_list) do
			if v.attr_value > 0 then
				local temp = {}
				temp.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(v.attr_str, true)
				is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v.attr_str)
				per_value = is_per and v.attr_value / 100 or v.attr_value
				per_str = is_per and "%" or ""
				temp.attr_value = per_value .. per_str
				table.insert(temp_list, temp)
			end
		end
		return temp_list
	end

	-- 升级属性
	if data.level and data.level > 0 then
		local level_cfg = FightSoulWGData.Instance:GetLevelCfgByLevel(data.level)
		if not IsEmptyTable(level_cfg) then
			local attr_list = EquipWGData.GetSortAttrListByCfg(level_cfg)

			local attr_info = {}
			attr_info.title_name = Language.F2Tip.SiXiangTitle1
			attr_info.attr_list = get_attr_list(attr_list)
			self.base_tips:SetPinkAttribute(attr_info)
		end
	end

	-- 突破属性
	if data.grade and data.grade > 0 then
		local break_cfg = FightSoulWGData.Instance:GetBreakCfgByGrade(data.grade)
		if not IsEmptyTable(break_cfg) then
			local attr_list = EquipWGData.GetSortAttrListByCfg(break_cfg)

			local attr_info = {}
			attr_info.title_name = Language.F2Tip.SiXiangTitle2
			attr_info.attr_list = get_attr_list(attr_list)
			self.base_tips:SetGodAttribute(attr_info)
		end
	end

	-- 融合属性
	local fs_item_cfg = FightSoulWGData.Instance:GetFightSoulItemCfg(data.item_id)
	if not IsEmptyTable(fs_item_cfg) then
		local attr_list = EquipWGData.GetSortAttrListByCfg(fs_item_cfg)

		local attr_info = {}
		attr_info.title_name = Language.F2Tip.SiXiangTitle3
		attr_info.attr_list = get_attr_list(attr_list)
		self.base_tips:SetMoreColorAttribute(attr_info)


		-- 技能
		local skill_id = fs_item_cfg.active_skill
		local skill_level = fs_item_cfg.skill_level
		local skill_cfg = SkillWGData.Instance:GetSiXiangSkillById(skill_id, skill_level)
		local skill_client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, skill_level)
		if not IsEmptyTable(skill_cfg) and not IsEmptyTable(skill_client_cfg) then
			local cur_skill_desc = SkillWGData.Instance:GetSiXiangSkillDes(skill_id, skill_level, true)
			local skill_info = {
								title_label = Language.F2Tip.SiXiangPower,
								skill_name = skill_cfg.skill_name, --ToColorStr(, ITEM_COLOR[item_cfg.color]),
								skill_icon_id = skill_client_cfg.icon_resource,
								cur_skill_desc = cur_skill_desc,
								have_next = false,
								show_special_kuang = false,
								next_desc_tilte = "",
								next_skill_desc = ""}

			self.base_tips:SetEquipSkill(skill_info)
		end
	end

	-- 合成配置
	local show_tips, process_list = ItemShowWGData.Instance:GetFightSoulComposeProcessData(data)
	if show_tips then
		local title_str = string.format(Language.F2Tip.ComposeTitleDesc, item_cfg.name)
		local sixiang_cap = FightSoulWGData.Instance:GetNoEquipCapability(data)
		local compose_info = {title_desc = title_str, equip_list = process_list,
							bottom_desc = Language.F2Tip.ComposeBottomDesc2,
							capability = sixiang_cap, show_add = false,}
		self.base_tips:SetEquipTipsComposePanel(compose_info)
	end

	-- 获取途径
	self:ShowItemGetDesc(item_cfg)
end

-- 解析四象魂骨tips
function ItemTip:ParseFightSoulBone(item_cfg, data)
	if IsEmptyTable(item_cfg) or IsEmptyTable(data) then
		return
	end
	self.common_pingfen_num = 0
	self.comp_pingfen_num = 0

	--标题
	local rich_type = string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE,
									Language.F2Tip.TypeSiXiangBone)
	self.base_tips:SetEquipSocre(rich_type)
	local limit_sex = item_cfg.limit_sex or 0
	local rich_prof = string.format(Language.Tip.ZhuangBeiProf, TIPS_COLOR.SOCRE,
								Language.Common.ProfName[limit_sex][item_cfg.limit_prof])
	self.base_tips:SetSyntheticalSocre(rich_prof)

	local item_id = data.item_id
	local star = data.star
	local fight_soul_type, bone_part, suit_type = FightSoulWGData.GetBoneParamByItemId(item_id)

	-- 基础属性
	local attr_list
	local attr_cfg = FightSoulWGData.Instance:GetBoneAttrCfg(bone_part, item_cfg.color, star)
	local is_wear, fs_slot, bone_part = FightSoulWGData.Instance:GetBoneBagIndexIsWear(data.bag_index)
	if is_wear then
		local part_data = FightSoulWGData.Instance:GetBonePartDataBySlotPart(fs_slot, bone_part)
		local strength_level = part_data and part_data.slot_level or 0
		if strength_level > 0 then
			local strength_cfg = FightSoulWGData.Instance:GetBoneStrengthCfg(bone_part, strength_level)
			attr_list = EquipWGData.GetSortAttrListHaveNextByCfg(attr_cfg, strength_cfg)
		else
			attr_list = EquipWGData.GetSortAttrListByCfg(attr_cfg)
		end
	else
		attr_list = EquipWGData.GetSortAttrListByCfg(attr_cfg)
	end

	local temp_list = {}
	local base_num = 0
	local uplevel_str = ""
	local is_per, per_value, per_str
	for k, v in pairs(attr_list) do
		if v.attr_value > 0 then
			local temp = {}
			temp.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(v.attr_str)
			is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v.attr_str)
			per_value = is_per and v.attr_value / 100 or v.attr_value
			per_str = is_per and "%" or ""
			temp.attr_value = per_value .. per_str
			uplevel_str = ""
			if v.attr_next_value and v.attr_next_value > 0 then
				uplevel_str = string.format("（%s+%s）", Language.Tip.StrengthText, v.attr_next_value)

				base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_str) or 0
				self.comp_pingfen_num = self.comp_pingfen_num + v.attr_next_value * base_num
			end
			temp.add_str = uplevel_str

			base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_str) or 0
			self.common_pingfen_num = self.common_pingfen_num + v.attr_value * base_num

			table.insert(temp_list, temp)
		end
	end

	if not IsEmptyTable(temp_list) then
		self.base_tips:SetBaseAttribute(temp_list)
	end

	-- 套装
	local suit_data = FightSoulWGData.Instance:GetBoneSuitShowData(fight_soul_type, suit_type, item_cfg.color)
	if not IsEmptyTable(suit_data) then
		if self.from_view == ItemTip.FROM_NORMAL then
			for i=1,#suit_data.info_list do
				suit_data.info_list[i].is_act = true
			end
		end
		self.base_tips:SetFightSoulSuitAttribute({fs_type = fight_soul_type, suit_type = suit_type, color = item_cfg.color, suit_data = suit_data})
		self.comp_pingfen_num = self.comp_pingfen_num + suit_data.suit_score
	end

	self.comp_pingfen_num = self.comp_pingfen_num + self.common_pingfen_num
	-- 装备评分
	local normal_attr_info = {}
	local str_limit_level = RoleWGData.GetLevelString(item_cfg.limit_level)
	normal_attr_info[1] = {name = Language.F2Tip.LvName, label = str_limit_level}
	normal_attr_info[2] = {name = Language.F2Tip.PingFen, label = math.ceil(self.common_pingfen_num)}
	normal_attr_info[3] = {name = Language.F2Tip.CompPingFen, label = math.ceil(self.comp_pingfen_num)}
	self.base_tips:SetNormalAttribute(normal_attr_info)

	-- 合成配置
	local show_tips, process_list = ItemShowWGData.Instance:GetFightSoulBoneComposeProcessData(data)
	if show_tips then
		local title_str = string.format(Language.F2Tip.ComposeTitleDesc, item_cfg.name)
		local sixiang_cap = 0--AttributeMgr.GetCapability(AttributeMgr.GetAttributteByClass(attr_cfg))
		local compose_info = {title_desc = title_str, equip_list = process_list,
							bottom_desc = Language.F2Tip.ComposeBottomDesc3,
							capability = sixiang_cap, show_add = false,}
		self.base_tips:SetEquipTipsComposePanel(compose_info)
	end

	-- 获取途径
	-- self:ShowItemGetDesc(item_cfg)
	self:FlushGetWayView()
end

-- 解析圣装
function ItemTip:ParseHolyEquip(item_cfg, data)
	if IsEmptyTable(item_cfg) or IsEmptyTable(data) then
		return
	end

	-- 查看玩家
	local is_browse = self.from_view == ItemTip.FROME_BROWSE_ROLE or self.from_view == ItemTip.FORM_ROBOT_SHOW_INFO
	local fle_data = FairyLandEquipmentWGData.Instance
	local item_id = data.item_id
	local he_data = fle_data:HolyEquipItemData(item_id)
	local slot = he_data.slot
	local part = he_data.part
	local is_wear = data.is_wear

	--标题
	local rich_type = string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE,
									Language.F2Tip.TypeHolyEquip)
	self.base_tips:SetEquipSocre(rich_type)
	local slot_cfg = FairyLandEquipmentWGData.Instance:GetGodBodyCfg(slot)
	local fle_str = Language.FairyLandEquipment
	local part_str = string.format(fle_str.EquipTipsDesc, slot_cfg.short_name, fle_str.PartName[part])
	local rich_part = string.format(Language.Tip.ZhuangBeiPart, TIPS_COLOR.SOCRE, part_str)
	self.base_tips:SetSyntheticalSocre(rich_part)


	self.common_pingfen_num = 0		-- 装备评分：基础属性 + 特殊属性
	self.comp_pingfen_num = 0		-- 综合评分：基础属性 + 特殊属性 + 强化属性 +仙品属性 + 进化属性

	-- 基础属性
	local attr_list
	local attr_cfg = fle_data:GetHolyEquipItemCfg(item_id)
	if is_wear then
		--强化属性
		local strength_level = is_browse and (data.strength_level and data.strength_level or 0) or fle_data:GetPartStrengthenLevel(slot, part)
		if strength_level > 0 then
			local strength_cfg = fle_data:GetFLEStrengthenCfgBySlotPartLv(slot, part, strength_level)
			attr_list = EquipWGData.GetSortAttrListHaveNextByCfg(attr_cfg, strength_cfg)
		else
			attr_list = EquipWGData.GetSortAttrListByCfg(attr_cfg)
		end
	else
		attr_list = EquipWGData.GetSortAttrListByCfg(attr_cfg)
	end

	local temp_list = {}
	local base_num = 0
	local uplevel_str = ""
	local is_per, per_value, per_str
	for k, v in pairs(attr_list) do
		if v.attr_value > 0 then
			local temp = {}
			temp.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(v.attr_str)
			is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v.attr_str)
			per_value = is_per and v.attr_value / 100 or v.attr_value
			per_str = is_per and "%" or ""
			temp.attr_value = per_value .. per_str
			uplevel_str = ""
			if v.attr_next_value and v.attr_next_value > 0 then
				uplevel_str = string.format("（%s+%s）", Language.Tip.StrengthText, v.attr_next_value)

				base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_str) or 0
				self.comp_pingfen_num = self.comp_pingfen_num + v.attr_next_value * base_num
			end
			temp.add_str = uplevel_str

			base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_str) or 0
			self.common_pingfen_num = self.common_pingfen_num + v.attr_value * base_num
			self.comp_pingfen_num = self.comp_pingfen_num + v.attr_value * base_num

			table.insert(temp_list, temp)
		end
	end

	if not IsEmptyTable(temp_list) then
		self.base_tips:SetBaseAttribute(temp_list)
	end
	-- 特殊属性

	-- 星级属性
	local rand_attr_list, rand_attr_score
	if is_wear then
		if is_browse then
			rand_attr_list, rand_attr_score = fle_data:GetBrowseEquipRandAttrAndScore(data)
		else
			rand_attr_list, rand_attr_score = fle_data:GetPartRandAttrAndScore(slot, part)
		end
	end

	if not IsEmptyTable(rand_attr_list) then
		local rand_info_list = {}
		rand_info_list.attr_list = rand_attr_list
		local grade = is_browse and (data.grade and data.grade or 0) or fle_data:GetPartGrade(slot, part)
		local grade_str = string.format(Language.FairyLandEquipment.EvolveGradeTipDesc, grade)
		rand_info_list.title_name = Language.Tip.StarAttr .. " " .. grade_str
		self.base_tips:SetFLEFEvolveAttribute(rand_info_list)
		self.comp_pingfen_num = self.comp_pingfen_num + rand_attr_score
	end

	-- 装备评分
	local normal_attr_info = {}
	local str_limit_level = RoleWGData.GetLevelString(item_cfg.limit_level)
	normal_attr_info[1] = {name = Language.F2Tip.LvName, label = str_limit_level}
	normal_attr_info[2] = {name = Language.F2Tip.PingFen, label = math.ceil(self.common_pingfen_num)}
	normal_attr_info[3] = {name = Language.F2Tip.CompPingFen, label = math.ceil(self.comp_pingfen_num)}
	self.base_tips:SetNormalAttribute(normal_attr_info)

	-- 合成配置
	local show_tips, process_list = ItemShowWGData.Instance:GetHolyEquipColorProcessData(he_data)
	if show_tips then
		local part_name = Language.FairyLandEquipment.PartName[part]
		local title_str = string.format(Language.FairyLandEquipment.EquipColorTitleDesc, slot_cfg.short_name, part_name)
		local compose_info = {title_desc = title_str, equip_list = process_list, show_add = false,}
		self.base_tips:SetEquipTipsComposePanel(compose_info)
	end

	-- 获取途径
	-- self:ShowItemGetDesc(item_cfg)
end

--解析道具tips
function ItemTip:ParseProp(item_cfg, big_type)
	local fun_open_name = item_cfg.funopen_limit or ""
	local show_fun_limit = false
	if fun_open_name ~= "" then
		local is_open = FunOpen.Instance:GetFunIsOpened(fun_open_name)
		show_fun_limit = not is_open
	end

	local rich_level = ""
	if not show_fun_limit then
		rich_level = string.format(Language.Tip.ShiYongDengJiNew, TIPS_COLOR.SOCRE, RoleWGData.GetLevelString(item_cfg.limit_level))
		if item_cfg.is_display_role ~= 0  then
			rich_level = string.format(Language.Tip.ShiYongDengJiNew, TIPS_COLOR.SOCRE, RoleWGData.GetLevelString(item_cfg.limit_level))
		end
	end
	self.base_tips:SetSyntheticalSocre(rich_level)

	local rich_type = ""
	local tujian_data = ShanHaiJingWGData.Instance:GetTJResolveShowCfg(item_cfg.id)
	if tujian_data ~= nil then
		local is_act = ShanHaiJingWGData.Instance:GetTJIsActBySeq(tujian_data.seq)
		local key = is_act and 1 or 2
		rich_type = Language.Tip.FashionState[key] or ""
	elseif item_cfg.goods_type then
		rich_type = string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE, Language.ShowItemType[item_cfg.goods_type])
	end
	self.base_tips:SetEquipSocre(rich_type)

	self:ShowItemDescription(item_cfg)
	self:ShowFlairObj()
	self:ShowItemGetDesc(item_cfg)
	self:ShowItemExpDesc(item_cfg)
	self:ShowBaGuaPai()
end

function ItemTip:ParseAttribute(attribute, item_cfg)
	local base_attr_cfg = item_cfg

	local base_attri_butte = AttributeMgr.GetAttributteByClass(base_attr_cfg) --根据职业获取属性, true, item_cfg.limit_prof
	attribute[ATTR_BASE] = base_attri_butte

	if TianShenWGData.Instance:IsTianShenEquip(item_cfg.sub_type) then
		attribute[ATTR_STRENGTHEN] = base_attri_butte
		attribute[ATTR_JIPING] = base_attri_butte
	elseif HiddenWeaponWGData.IsHiddenWeapon(item_cfg.sub_type) then
		local data = self.data
		local cfg = HiddenWeaponWGData.Instance:GetOnlyEquipCfgById(item_cfg.id)
		local equip = {
	        big_type = cfg.big_type,
			node_type = cfg.node_type,
			base_star = data.equip and data.equip.base_star or cfg.base_star,
			base_color = data.equip and data.equip.base_color or cfg.base_color,
			special_flag = data.equip and data.equip.special_flag or cfg.special_flag
		}
		local base_cfg = HiddenWeaponWGData.Instance:GetEquipBaseAttr(equip)
		base_attri_butte = AttributeMgr.GetAttributteByClass(base_cfg)
		attribute[ATTR_BASE] = base_attri_butte
		attribute[ATTR_SHENJI_STAR] = base_attri_butte
		attribute[ATTR_SHENJI_SKILL] = base_attri_butte
		attribute[ATTR_ITEM_SKILL] = base_attri_butte
		attribute[REMIND_HOW_COMPOSE] = base_attri_butte
	else
		attribute[ATTR_STONE] = base_attri_butte
		attribute[ATTR_STONE_JL] = base_attri_butte
		attribute[ATTR_LEGEND] = base_attri_butte
		attribute[ATTR_SUIT] = base_attri_butte
		attribute[ATTR_SHENGPIN] = base_attri_butte
		attribute[ATTR_PINK_SUIT] = base_attri_butte
		attribute[ATTR_GOD_SUIT] = base_attri_butte
		attribute[ATTR_MORE_COLOR] = base_attri_butte
		attribute[REMIND_HOW_COMPOSE] = base_attri_butte
		attribute[ATTR_EQUIP_SKILL] = base_attri_butte
		attribute[ATTR_LINGYUE] = base_attri_butte
		attribute[ATTR_NEWYINJI] = base_attri_butte
		attribute[ATTR_KEYINFU] = base_attri_butte
		attribute[ATTP_YULING] = base_attri_butte
		attribute[ATTP_ZHUSHEN] = base_attri_butte
		attribute[ATTP_XILIAN] = base_attri_butte
	end

	if self.from_view == ItemTip.FROM_STORGE_ON_GUILD_STORGE then
		attribute[ATTR_XIANMENG_CANGKU] = base_attri_butte
	end
end

function ItemTip:ParseXiaogui(item_cfg)
	local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(self.data.item_id)
	if xiaogui_cfg == nil then
		return
	end

	local attr_list = {}
	local specail_list = {}
	local pingfen = 0
	for key,value in pairs(xiaogui_cfg) do
		local temp = {}
		temp.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(key)
		if temp.attr_name and temp.attr_name ~= "" and temp.attr_name ~= "：" then
			if value > 0 then
				if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(key) then -- 经验加成
					temp.attr_desc = string.format("%d%%", value / 100)
					specail_list[#specail_list + 1] = temp
				else
					temp.attr_value = value
					attr_list[#attr_list + 1] = temp
					local count = TipWGData.Instance:GetCommonPingFenCfgByIndex(key)
					if count > 0 then
						pingfen = pingfen + count * value
					end
				end
			end
		end
	end
	self.base_tips:SetBaseAttribute(attr_list)
	self.base_tips:SetSpecialAttribute(specail_list)
	self.base_tips:SetEquipSocre(string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE, Language.Equip.XiaoGuiType))

	local rich_level = ""
	if not show_fun_limit then
		rich_level = string.format(Language.Tip.ShiYongDengJiNew, TIPS_COLOR.SOCRE, RoleWGData.GetLevelString(item_cfg.limit_level))
		if item_cfg.is_display_role ~= 0  then
			rich_level = string.format(Language.Tip.ShiYongDengJiNew, TIPS_COLOR.SOCRE, RoleWGData.GetLevelString(item_cfg.limit_level))
		end
	end
	self.base_tips:SetSyntheticalSocre(rich_level)

	local _, capability = ItemShowWGData.Instance:GetShouHuAttrByData(self.data.item_id)
	self.base_tips:SetCapabilityPanel({capability = capability})

	local normal_attr_info = {}
	normal_attr_info[1] = {name = Language.F2Tip.LvName, label = RoleWGData.GetLevelString(item_cfg.limit_level)}
	normal_attr_info[2] = {name = Language.F2Tip.CompPingFen, label = pingfen}
	self.base_tips:SetNormalAttribute(normal_attr_info)

	if self.data.invalid_time and self.data.invalid_time > 0 then
		local time_str = ""
		local time = self.data.invalid_time - TimeWGCtrl.Instance:GetServerTime()
		local time_tab = TimeUtil.Format2TableDHMS(time)
		if time > 0 then
			if time_tab.day >= 1 then
				time_str = string.format(Language.Common.NextTimeText, time_tab.day, Language.ExpPool.TimeList.d)
			elseif time_tab.hour >= 1 then
				time_str = string.format(Language.Common.NextTimeText, time_tab.hour, Language.ExpPool.TimeList.h)
			elseif time_tab.min >= 1 then
				time_str = string.format(Language.Common.NextTimeText, time_tab.min, Language.ExpPool.TimeList.min)
			else
				time_str = string.format(Language.Common.NextTimeText, time_tab.s, Language.ExpPool.TimeList.s)
			end
		else
			time_str = Language.Equip.XiaoGuiGuoQi
		end
		self.base_tips:SetBottomLabel(time_str)
	end

	self:ShowItemDescription(item_cfg)
end

function ItemTip:ParseAttrStoreSXDProp(item_cfg, big_type)
	local fun_open_name = item_cfg.funopen_limit or ""
	local show_fun_limit = false
	local capability = 0
	local attribute = AttributePool.AllocAttribute()
	if fun_open_name ~= "" then
		local is_open = FunOpen.Instance:GetFunIsOpened(fun_open_name)
		show_fun_limit = not is_open
	end

	local rich_level = ""
	if not show_fun_limit then
		rich_level = string.format(Language.Tip.ShiYongDengJiNew, TIPS_COLOR.SOCRE, RoleWGData.GetLevelString(item_cfg.limit_level))
		if item_cfg.is_display_role ~= 0 then
			rich_level = string.format(Language.Tip.ShiYongDengJiNew, TIPS_COLOR.SOCRE, RoleWGData.GetLevelString(item_cfg.limit_level))
		end
	end
	self.base_tips:SetSyntheticalSocre(rich_level)

	local rich_type = ""
	local tujian_data = ShanHaiJingWGData.Instance:GetTJResolveShowCfg(item_cfg.id)
	if tujian_data ~= nil then
		local is_act = ShanHaiJingWGData.Instance:GetTJIsActBySeq(tujian_data.seq)
		local key = is_act and 1 or 2
		rich_type = Language.Tip.FashionState[key] or ""
	elseif item_cfg.goods_type and item_cfg.goods_type ~= 0 then
		rich_type = string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE, Language.ShowItemType[item_cfg.goods_type])
	end
	self.base_tips:SetEquipSocre(rich_type)

	local sxd_store_cfg = NewAppearanceWGData.Instance:GetAttrStoreSXDCfg(self.data.item_id)
	if not IsEmptyTable(sxd_store_cfg) then
		local temp_list = {}

		for i = 1, 5 do
			local attr_id = sxd_store_cfg["attr_id" .. i]
			local attr_value = sxd_store_cfg["attr_value" .. i]

			if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
				local data = {}
				local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_id))
				data["attr_name"] = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str, true)
				data["attr_value"] = AttributeMgr.PerAttrValue(attr_str, attr_value)
				data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
				attribute[attr_str] = attribute[attr_str] + attr_value
				table.insert(temp_list, data)
			end
		end

		if not IsEmptyTable(temp_list) then
			capability = AttributeMgr.GetCapability(attribute)
			table.sort(temp_list, SortTools.KeyLowerSorter("attr_sort"))
			self.base_tips:SetBaseAttribute(temp_list)
		end
	end

	self.base_tips:SetCapabilityPanel({capability = capability})
	self:ShowItemDescription(item_cfg)
	self:ShowItemGetDesc(item_cfg)
end

-- 解析五行
function ItemTip:ParseFiveElements(item_cfg, data)
	if IsEmptyTable(item_cfg) or IsEmptyTable(data) then
		return
	end

	-- 类型
	local rich_type = string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE,
	Language.F2Tip.DisplayType[item_cfg.goods_type])
	self.base_tips:SetEquipSocre(rich_type)

	-- 使用等级
	local rich_level = string.format(Language.Tip.ShiYongDengJiNew, TIPS_COLOR.SOCRE,
	RoleWGData.GetLevelString(item_cfg.limit_level))
	self.base_tips:SetSyntheticalSocre(rich_level)

	-- 物品描述  
	local desc_info = {}
	local description = item_cfg.description
	if description and description ~= "" then
		desc_info[#desc_info + 1] = {title = Language.F2Tip.WuPinMiaoShu, desc = description}
	end
	self.base_tips:SetItemDesc(desc_info)

	local function change_attr_list(attr_list)
		local temp_list = {}
		if IsEmptyTable(attr_list) then
			return temp_list
		end

		for k, v in pairs(attr_list) do
			local temp = {}
			temp.attr_name = v.attr_name
			temp.attr_value = v.value_str
			table.insert(temp_list, temp)
		end

		return temp_list
	end
	
	-- 战力
	local attr_list_info, capability = ItemShowWGData.Instance:GetFiveElementsAttrByData(self.data.item_id)
	local attr_info = {}
	attr_info.title_name = Language.FiveElements.StoreAttrTitle
	attr_info.attr_list = change_attr_list(attr_list_info)
	self.base_tips:SetPinkAttribute(attr_info)
	self.base_tips:SetCapabilityPanel({capability = capability})

	-- 获取途径
	self:ShowItemGetDesc(item_cfg)
end

-- 符咒装备
function ItemTip:ParseCharmEquip(item_cfg, data)
	if IsEmptyTable(item_cfg) or IsEmptyTable(data) then
		return
	end

	-- 阶数
	local equip_item_cfg = CultivationWGData.Instance:GetCharmEquipByItemId(data.item_id)
	if not IsEmptyTable(equip_item_cfg) then
		local order_str = string.format(Language.Charm.CharmOrderLevel, equip_item_cfg.order)
		self.item_cell:SetRightTopImageText(order_str)
	end

	-- 类型
	local rich_type = string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE,
	Language.F2Tip.DisplayType[item_cfg.goods_type])
	self.base_tips:SetEquipSocre(rich_type)

	-- 使用等级(仙修境界等级)
	local eqaip_data = CultivationWGData.Instance:GetCharmEquipByItemId(data.item_id)
	local stage_cfg = CultivationWGData.Instance:GetXiuWeiStageCfgByState(eqaip_data.need_xiuwei_stage)
	local rich_level = string.format(Language.Tip.ShiYongStage, TIPS_COLOR.SOCRE,stage_cfg. stage_title)
	self.base_tips:SetSyntheticalSocre(rich_level)

	-- 物品描述  
	local desc_info = {}
	local description = item_cfg.description
	if description and description ~= "" then
		desc_info[#desc_info + 1] = {title = Language.F2Tip.WuPinMiaoShu, desc = description}
	end
	self.base_tips:SetItemDesc(desc_info)	

	local function change_attr_list(attr_list)
		local temp_list = {}
		if IsEmptyTable(attr_list) then
			return temp_list
		end

		for k, v in pairs(attr_list) do
			local temp = {}
			temp.attr_name = v.attr_name
			temp.attr_value = v.value_str
			table.insert(temp_list, temp)
		end

		return temp_list
	end
	
	-- 战力
	local attr_list_info, capability = ItemShowWGData.Instance:GetCharmAttrByData(self.data.item_id)
	local attr_info = {}
	attr_info.title_name = Language.Charm.StoreAttrTitle
	attr_info.attr_list = change_attr_list(attr_list_info)
	-- self.base_tips:SetPinkAttribute(attr_info)
	self.base_tips:SetCharmAttribute(attr_info)
	self.base_tips:SetCapabilityPanel({capability = capability})

	-- 获取途径
	self:ShowItemGetDesc(item_cfg)
end

-- 道行装备
function ItemTip:ParseDaoHangEquip(item_cfg, data)
	if IsEmptyTable(item_cfg) or IsEmptyTable(data) then
		return
	end

	local rich_type = string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE,
	Language.F2Tip.DisplayType[item_cfg.goods_type])
	self.base_tips:SetEquipSocre(rich_type)

	local rich_level = string.format(Language.Tip.ShiYongDengJiNew, TIPS_COLOR.SOCRE,
	RoleWGData.GetLevelString(item_cfg.limit_level))
	self.base_tips:SetSyntheticalSocre(rich_level)

	local equip_item_cfg = MultiFunctionWGData.Instance:GetDaoHangEquipByItemId(data.item_id)
	local strength_cfg = {}
	local name_str = item_cfg.name or ""
	
	if self.from_view == ItemTip.FROM_DAOHANG_EQUIP and data.level and data.level > 0 then
		name_str = name_str .. string.format(Language.Charm.DaoHangEquipAddLevel, data.level)
		strength_cfg = MultiFunctionWGData.Instance:GetDaoHangSlotEnhanceCfgBySlotAndLevel(data.slot, data.level)
	end

	self.base_tips:SetItemName(name_str)
	local attr_id_list = {}
	local attr_num = 0
	local temp_list = {}

	--策划可能配置的装备跟强化的属性配置匹配不上，不可直接按照index判断
	local function cal_attr_data(attr_id, attr_value, is_add)
		local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_id))
		local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str, true)
			
		local value_str = is_add and AttributeMgr.PerAttrValue(attr_str, 0) or AttributeMgr.PerAttrValue(attr_str, attr_value)
		local add_str = ""
		local add_value = is_add and attr_value or 0
		if add_value > 0 then
			add_str = string.format(Language.Charm.DaoHangAttrAdd, AttributeMgr.PerAttrValue(attr_str, add_value))
		end

		local num_attr_value = is_add and 0 or attr_value
		local num_add_value = is_add and attr_value or 0
		local attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)

		if not attr_id_list[attr_id] then
			local temp = {}
			temp.attr_name = attr_name
			temp.attr_value = value_str
			temp.add_str = add_str
			temp.num_attr_value = num_attr_value
			temp.num_add_value = num_add_value
			temp.attr_sort = attr_sort
			table.insert(temp_list, temp)
			attr_num = attr_num + 1
			attr_id_list[attr_id] = attr_num
		else
			if is_add then
				temp_list[attr_id_list[attr_id]].num_add_value = temp_list[attr_id_list[attr_id]].num_add_value + attr_value
				local add_str = ""

				if temp_list[attr_id_list[attr_id]].num_add_value > 0 then
					add_str = string.format(Language.Charm.DaoHangAttrAdd, AttributeMgr.PerAttrValue(attr_str, temp_list[attr_id_list[attr_id]].num_add_value))
				end
				
				temp_list[attr_id_list[attr_id]].add_str = add_str
			else
				temp_list[attr_id_list[attr_id]].num_attr_value = temp_list[attr_id_list[attr_id]].num_attr_value + attr_value
				temp_list[attr_id_list[attr_id]].attr_value = AttributeMgr.PerAttrValue(attr_str, temp_list[attr_id_list[attr_id]].num_attr_value)
			end	
		end
	end

	for i = 1, 5 do
		local attr_id = equip_item_cfg["attr_id" .. i] or 0
		local attr_value = equip_item_cfg["attr_value" .. i] or 0

		local add_attr_id = strength_cfg["attr_id" .. i] or 0
		local add_attr_value = strength_cfg["attr_value" .. i] or 0

		if (attr_id > 0 and attr_value > 0) or (add_attr_id > 0 and add_attr_value > 0) then
			if attr_id > 0 and attr_value > 0 then
				cal_attr_data(attr_id, attr_value, false)
			end
	
			if add_attr_id > 0 and add_attr_value > 0 then
				cal_attr_data(add_attr_id, add_attr_value, true)
			end
		end
	end

	if not IsEmptyTable(temp_list) then
		-- table.sort(temp_list, SortTools.KeyLowerSorter("num_add_value", "num_attr_value"))
		table.sort(temp_list, SortTools.KeyLowerSorter("attr_sort"))
		self.base_tips:SetBaseAttribute(temp_list)
	end

	if self.from_view == ItemTip.FROM_DAOHANG_EQUIP then
		if data.grade and data.grade > 0 then
			local grade_cfg = MultiFunctionWGData.Instance:GetDaoHangSlotAdvanceCfgBySlotAndLevel(data.slot, data.grade)

			if not IsEmptyTable(grade_cfg) then
				local info_list = {}
				local advance_attr_list = {}
				
				for i = 1, 5 do
					local attr_id = grade_cfg["attr_id" .. i] or 0
					local attr_value = grade_cfg["attr_value" .. i] or 0

					if attr_id > 0 and attr_value > 0 then
						local temp = {}
						local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_id))
						local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str, true)
						local value_str = AttributeMgr.PerAttrValue(attr_str, attr_value)
						temp.attr_name = attr_name
						temp.attr_value = value_str
						table.insert(advance_attr_list, temp)
					end
				end

				if not IsEmptyTable(advance_attr_list) then
					info_list.title_name = Language.Charm.DaoHangAdvanceTipTitle
					info_list.attr_list = advance_attr_list
					self.base_tips:SetGodAttribute(info_list)
				end
			end
		end

	--开光属性   是否开光
		if data.has_kaiguang_value then
			local kaiguang_attr_list = {}
			local kaiguang_quality_cfg = MultiFunctionWGData.Instance:GetDaoHangkaiGuangAttrLimitCfg(data.color)

			if not IsEmptyTable(kaiguang_quality_cfg) then
				for i = 0, 4 do
					local kaiguang_cell_attr_cfg = MultiFunctionWGData.Instance:GetDaoHangKaiGuangAttrCfg(data.slot, i)
					local attr_id = kaiguang_cell_attr_cfg["attr_id"] or 0
					local attr_value = kaiguang_cell_attr_cfg["attr_val"] or 0
					local cur_per_value = (data.per_kaiguang_attr_value_list or {})[i] or 0

					if cur_per_value > 0 then
						local temp = {}
						local per_value = cur_per_value / kaiguang_quality_cfg.limit
						local target_value = per_value * attr_value
						local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_id))
						local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str, true)
						local value_str = AttributeMgr.PerAttrValue(attr_str, target_value)
						temp.attr_name = attr_name
						temp.attr_value = value_str
						table.insert(kaiguang_attr_list, temp)
					end
				end
			end

			if not IsEmptyTable(kaiguang_attr_list) then
				local info_list = {}
				info_list.title_name = Language.Charm.DaoHangKaiGuangTipTitle
				info_list.attr_list = kaiguang_attr_list
				self.base_tips:SetPinkAttribute(info_list)
			end
		end

		local keyin_item_data = {}
		if not IsEmptyTable(data.carve_item_list) then
			for i = 1, 3 do
				local keyin_cell_data = {}
				keyin_cell_data.title_name = Language.Charm.DaoHangKeYinTitleName[i]
				local level = (data.carve_item_list[i] or {}).level or 0

				if level > 0 then
					local data_cfg = MultiFunctionWGData.Instance:GetDaoHangSlotCarveCfg(data.slot, i, level)
					if not IsEmptyTable(data_cfg) then
						keyin_cell_data.attr_list = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(data_cfg, "attr_id", "attr_value")
						table.insert(keyin_item_data, keyin_cell_data)
					end
				end
			end
		end

		if not IsEmptyTable(keyin_item_data) then
			local keyin_attr_list = {}
			keyin_attr_list.title_name = Language.Charm.DaoHangkeYinTipTitle
			keyin_attr_list.attr_list = keyin_item_data
			self.base_tips:SetDaoHangKeYinAttribute(keyin_attr_list)
		end
	end

	self:ShowItemGetDesc(item_cfg)
end

-- 梦灵装备
function ItemTip:ParseMengLingEquip(item_cfg, data)
	if IsEmptyTable(item_cfg) or IsEmptyTable(data) then
		return
	end

	local rich_type = string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE,
	Language.F2Tip.DisplayType[item_cfg.goods_type])
	self.base_tips:SetEquipSocre(rich_type)

	local rich_level = string.format(Language.Tip.ShiYongDengJiNew, TIPS_COLOR.SOCRE,
	RoleWGData.GetLevelString(item_cfg.limit_level))
	self.base_tips:SetSyntheticalSocre(rich_level)

	local desc_info = {}
	local base_num = 0
	local description = item_cfg.description
	if description and description ~= "" then
		desc_info[#desc_info + 1] = {title = Language.F2Tip.WuPinMiaoShu, desc = description}
	end

	self.base_tips:SetItemDesc(desc_info)

	local item_id = data.item_id
	local equip_cfg = MengLingWGData.Instance:GetMengLingEquipCfgByItemId(item_id)

	if not IsEmptyTable(equip_cfg) then
		local temp_list = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(equip_cfg, "attr_id", "attr_value", "attr_name", "attr_value")

		if not IsEmptyTable(temp_list) then
			self.base_tips:SetBaseAttribute(temp_list)
		end
	end

	self:ShowItemGetDesc(item_cfg)
end

--解析结义装备
function ItemTip:ParseSwornEquip(item_cfg, data)
	if IsEmptyTable(item_cfg) or IsEmptyTable(data) then
		return
	end

	self.common_pingfen_num = 0
	--self.comp_pingfen_num = 0

	--标题
	local rich_type = string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE,
									Language.ShowItemType[1])
	self.base_tips:SetEquipSocre(rich_type)

	-- 使用等级
	local rich_level = string.format(Language.Tip.ShiYongDengJiNew, TIPS_COLOR.SOCRE,
	RoleWGData.GetLevelString(item_cfg.limit_level))
	self.base_tips:SetSyntheticalSocre(rich_level)

	-- 物品描述  
	local desc_info = {}
	local base_num = 0
	local description = item_cfg.description
	if description and description ~= "" then
		desc_info[#desc_info + 1] = {title = Language.F2Tip.WuPinMiaoShu, desc = description}
	end

	self.base_tips:SetItemDesc(desc_info)
	local suit, hole = SwornWGData.Instance:GetJinLanEquipHole(data.item_id)
	local star_level = 1
	local level = 0
	if self.from_view == ItemTip.FROM_SWORN_EQUIP then
		star_level = SwornWGData.Instance:GetHoleStarLevel(suit, hole)
		level = SwornWGData.Instance:GetHoleLevel(suit, hole)
	end

	local attribute = AttributePool.AllocAttribute()
	--星级属性
	local star_attr_list = SwornWGData.Instance:GetJinLanEquipStarAttrList(data.item_id, star_level)
	if not IsEmptyTable(star_attr_list) then
		local strong_list = {}
		for k,v in pairs(star_attr_list) do
			local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(v.attr_str, true)
				local temp = {attr_name = attr_name,
							attr_value = v.attr_value}
				strong_list[#strong_list + 1] = temp

			base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_str) or 0
			self.common_pingfen_num = self.common_pingfen_num + v.attr_value * base_num
			--self.comp_pingfen_num = self.comp_pingfen_num + v.attr_value * base_num

			attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
		end

		local star_info_list = {}
		star_info_list.attr_list = strong_list
		star_info_list.title_name = Language.Sworn.SwornAttrTitle
		self.base_tips:SetPinkAttribute(star_info_list)
	end

	-- 等级属性
	local level_attr_list = SwornWGData.Instance:GetJinLanEquipLevelAttrList(data.item_id, level)
	if not IsEmptyTable(level_attr_list) then 
		local strong_list = {}
		for k,v in pairs(level_attr_list) do
			local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(v.attr_str, true)
			if attr_name then
				local temp = {}
				temp.attr_name = attr_name
				temp.attr_value = v.attr_value
				strong_list[#strong_list + 1] = temp
			end

			base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_str) or 0
			self.common_pingfen_num = self.common_pingfen_num + v.attr_value * base_num
			--self.comp_pingfen_num = self.comp_pingfen_num + v.attr_value * base_num

			attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
		end

		local rand_info_list = {}
		rand_info_list.attr_list = strong_list
		rand_info_list.title_name = Language.Sworn.NextAttr
		self.base_tips:SetStrongAttribute(rand_info_list)
	end

	--名字
	local star_str = CommonDataManager.GetAncientNumber(star_level)
	local item_name = string.format(Language.Sworn.EquipStarName, star_str, item_cfg.name)
	if level > 0 then
		item_name = item_name .. string.format(Language.Sworn.EquipLevel, level)
	end
	self.base_tips:SetItemName(item_name)
	

	-- 装备评分
	local normal_attr_info = {}
	local str_limit_level = RoleWGData.GetLevelString(item_cfg.limit_level)
	normal_attr_info[1] = {name = Language.F2Tip.LvName, label = str_limit_level}
	normal_attr_info[2] = {name = Language.F2Tip.PingFen, label = math.ceil(self.common_pingfen_num)}
	--normal_attr_info[3] = {name = Language.F2Tip.CompPingFen, label = math.ceil(self.comp_pingfen_num)}
	self.base_tips:SetNormalAttribute(normal_attr_info)

	-- 战力
	local capability = AttributeMgr.GetCapability(attribute)
	self.base_tips:SetCapabilityPanel({capability = capability})
end

--解析圣器装备
function ItemTip:ParseRelicEquip(item_cfg, data)
	if IsEmptyTable(item_cfg) or IsEmptyTable(data) then
		return
	end

	self.common_pingfen_num = 0
	--self.comp_pingfen_num = 0
	--标题
	local rich_type = string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE,
									Language.ShowItemType[1])
	self.base_tips:SetEquipSocre(rich_type)

	-- 使用等级
	local rich_level = string.format(Language.Tip.ShiYongDengJiNew, TIPS_COLOR.SOCRE,
	RoleWGData.GetLevelString(item_cfg.limit_level))
	self.base_tips:SetSyntheticalSocre(rich_level)

	-- 物品描述  
	local desc_info = {}
	local description = item_cfg.description
	if description and description ~= "" then
		desc_info[#desc_info + 1] = {title = Language.F2Tip.WuPinMiaoShu, desc = description}
	end

	--self.base_tips:SetItemDesc(desc_info)

	local relic_seq = HolyDarkWeaponWGCtrl.Instance:GetHolyDarkViewSelectWeaponSeq()
	local equip_cfg = HolyDarkWeaponWGData.Instance:GetRelicEquipItemData(data.item_id)
	if not equip_cfg then
		print_error("====装备配置不存在id=", data.item_id)
		return 
	end
	local strength_level = 0          --强化等级
	local strength_attr_per = 0       ---槽位强化等级加成
	local star_level = 0              -- 星级
	local grade = 0                   --阶数
	local attribute = AttributePool.AllocAttribute()


	if self.from_view == ItemTip.FROM_RELIC_EQUIP then
		strength_level = data.level
		local cur_level_cfg = HolyDarkWeaponWGData.Instance:GetRelicSlotLevelCfg(relic_seq, data.slot_index, data.level)
		if not IsEmptyTable(cur_level_cfg) then
			strength_attr_per = cur_level_cfg.attr_per
		end

		star_level = data.star_level
		grade = data.grade
	end

	-- 基础属性 强化属性
	local base_attr_list = HolyDarkWeaponWGData.Instance:GetRelicEquipBaseAttrList(data.item_id)

	local temp_list = {}
	local base_num = 0
	local uplevel_str = ""
	local is_per, per_value, per_str
	for k, v in pairs(base_attr_list) do
		if v.attr_value > 0 then
			local temp = {}
			is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v.attr_str)
			if not is_per then
				temp.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(v.attr_str)
				per_value = is_per and v.attr_value / 100 or v.attr_value
				per_str = is_per and "%" or ""
				temp.attr_value = per_value .. per_str
				uplevel_str = ""
				if strength_attr_per > 0 then
					uplevel_str = string.format("（%s+%s）", Language.Tip.StrengthText, math.ceil(v.attr_value * strength_attr_per * 0.0001))
					base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_str) or 0
					self.common_pingfen_num = self.common_pingfen_num + v.attr_value * base_num * strength_attr_per * 0.0001
					attribute[v.attr_str] = attribute[v.attr_str] + (v.attr_value * strength_attr_per * 0.0001)
				end

				base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_str) or 0
				self.common_pingfen_num = self.common_pingfen_num + v.attr_value * base_num
				attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value

				temp.add_str = uplevel_str

				table.insert(temp_list, temp)
			end
		end
	end

	if strength_level > 0 then
		temp_list.title_name = string.format(Language.HolyDarkWeapon.BaseAttrTitle, strength_level) 
	end


	if not IsEmptyTable(temp_list) then
		self.base_tips:SetBaseAttribute(temp_list)
	end

	--特殊处理将百分比属性拆出来
	local spe_attr_list = {}
	for k, v in pairs(base_attr_list) do
		if v.attr_value > 0 then
			local spe_temp = {}
			is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v.attr_str)
			if is_per then
				spe_temp.attr_name = ToColorStr(EquipmentWGData.Instance:GetAttrNameByAttrStr(v.attr_str), ITEM_COLOR[item_cfg.color]) 
				per_value = is_per and v.attr_value / 100 or v.attr_value
				per_str = is_per and "%" or ""
				spe_temp.attr_value =  ToColorStr(per_value .. per_str, ITEM_COLOR[item_cfg.color]) 
				base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_str) or 0
				self.common_pingfen_num = self.common_pingfen_num + v.attr_value * base_num
				attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
				table.insert(spe_attr_list, spe_temp)
			end
		end
	end

	if not IsEmptyTable(spe_attr_list) then
		local spe_info_list = {}
		spe_info_list.title_name = Language.HolyDarkWeapon.SpecialAttrName[item_cfg.color]
		spe_info_list.attr_list = spe_attr_list
		self.base_tips:SetStrongAttribute(spe_info_list)
	end

	--星级属性
	local star_attr_list = HolyDarkWeaponWGData.Instance:GetRelicSlotStarAttrList(relic_seq, equip_cfg.slot, star_level)
	if not IsEmptyTable(star_attr_list) then
		local star_list = {}
		for k,v in pairs(star_attr_list) do
			local temp = {}
			local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(v.attr_str, true)
			temp.attr_name = attr_name
			is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v.attr_str)
			per_value = is_per and v.attr_value / 100 or v.attr_value
			per_str = is_per and "%" or ""
			temp.attr_value = per_value .. per_str
			star_list[#star_list + 1] = temp

			base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_str) or 0
			self.common_pingfen_num = self.common_pingfen_num + v.attr_value * base_num
			--self.comp_pingfen_num = self.comp_pingfen_num + v.attr_value * base_num
			attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
		end

		local star_info_list = {}
		star_info_list.attr_list = star_list
		star_info_list.title_name = Language.HolyDarkWeapon.StarAttrTitle
		self.base_tips:SetPinkAttribute(star_info_list)
	end

	--阶数属性
	local grade_attr_list = HolyDarkWeaponWGData.Instance:GetRelicSlotGradeAttrList(relic_seq, equip_cfg.slot, grade)
	if not IsEmptyTable(grade_attr_list) then
		local grade_list = {}
		for k,v in pairs(grade_attr_list) do
			local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(v.attr_str, true)
				local temp = {attr_name = attr_name,
							attr_value = v.attr_value}
				grade_list[#grade_list + 1] = temp

			base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_str) or 0
			self.common_pingfen_num = self.common_pingfen_num + v.attr_value * base_num
			--self.comp_pingfen_num = self.comp_pingfen_num + v.attr_value * base_num
			attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
		end

		local grade_info_list = {}
		grade_info_list.attr_list = grade_list
		grade_info_list.title_name = Language.HolyDarkWeapon.GradeAttrTitle
		--self.base_tips:SetStrongAttribute(grade_info_list)
		self.base_tips:SetShengPinAttribute(grade_info_list)
	end

	--名字
	local item_name = item_cfg.name
	if star_level > 0 then
		local star_str = CommonDataManager.GetAncientNumber(star_level)
		item_name = string.format(Language.HolyDarkWeapon.EquipStarName, star_str, item_name)
	end

	if grade > 0 then
		local grade_str = CommonDataManager.GetAncientNumber(grade)
		item_name = string.format(Language.HolyDarkWeapon.EquipGradeName, item_name, grade_str)
	end

	self.base_tips:SetItemName(item_name)
	self.item_cell:SetLeftTopImg(star_level or 0)

	-- 装备评分
	local normal_attr_info = {}
	local str_limit_level = RoleWGData.GetLevelString(item_cfg.limit_level)
	normal_attr_info[1] = {name = Language.F2Tip.LvName, label = str_limit_level}
	normal_attr_info[2] = {name = Language.F2Tip.PingFen, label = math.ceil(self.common_pingfen_num)}
	--normal_attr_info[3] = {name = Language.F2Tip.CompPingFen, label = math.ceil(self.comp_pingfen_num)}
	self.base_tips:SetNormalAttribute(normal_attr_info)

		-- 战力
	local capability = AttributeMgr.GetCapability(attribute)
	self.base_tips:SetCapabilityPanel({capability = capability})
end

--解析弑天装备
function ItemTip:ParseShiTianEquip(item_cfg, data)
	if IsEmptyTable(item_cfg) or IsEmptyTable(data) then
		return
	end

	self.common_pingfen_num = 0
	--标题
	local rich_type = string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE,
									Language.ShowItemType[1])
	self.base_tips:SetEquipSocre(rich_type)

	-- 使用等级
	local rich_level = string.format(Language.Tip.ShiYongDengJiNew, TIPS_COLOR.SOCRE,
	RoleWGData.GetLevelString(item_cfg.limit_level))
	self.base_tips:SetSyntheticalSocre(rich_level)

	-- 物品描述  
	local desc_info = {}
	local base_num = 0
	local description = item_cfg.description
	if description and description ~= "" then
		desc_info[#desc_info + 1] = {title = Language.F2Tip.WuPinMiaoShu, desc = description}
	end
	self.base_tips:SetItemDesc(desc_info)

	local hole_cfg = ShiTianSuitWGData.Instance:GetShiTIanEquipHole(data.item_id)
	local star_level = 0
	local strengthen_level = 0
	local from_shitian_view = self.from_view == ItemTip.FROM_SHITIAN_EQUIP
	local strengthen_attr_list, stone_attr_list, infuse_soul_attr_list
	if from_shitian_view then
		star_level = ShiTianSuitWGData.Instance:GetHoleStarLevel(hole_cfg.suit_seq, hole_cfg.part)
		strengthen_level = ShiTianSuitStrengthenWGData.Instance:GetEpStrengthenCurLevel(hole_cfg.suit_seq, hole_cfg.part)
		strengthen_attr_list, stone_attr_list, infuse_soul_attr_list = ShiTianSuitStrengthenWGData.Instance:GetShiTianEquipAttrList(hole_cfg.suit_seq, hole_cfg.part)
	end

	local attribute = AttributePool.AllocAttribute()
	local is_per, per_value, per_str
	local xp_name_color = ITEM_COLOR[item_cfg.color] or "#A8A5A4FF"
	--基础属性
	local nor_attr_list, xp_attr_list = ShiTianSuitWGData.Instance:GetShiTianEquipStarAttrList(data.item_id, star_level)
	if not IsEmptyTable(nor_attr_list) then
		local strong_list = {}
		for k,v in pairs(nor_attr_list) do
			local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(v.attr_str, true)
				local temp = {attr_name = attr_name,
							attr_value = v.attr_value}
				strong_list[#strong_list + 1] = temp

			base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_str) or 0
			self.common_pingfen_num = self.common_pingfen_num + v.attr_value * base_num
			attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
		end

		local star_info_list = {}
		star_info_list.attr_list = strong_list
		star_info_list.title_name = Language.ShiTianSuit.TipsTitleName
		self.base_tips:SetPinkAttribute(star_info_list)
	end

	--仙品属性
	if not IsEmptyTable(xp_attr_list) then
		local strong_list = {}
		for k,v in pairs(xp_attr_list) do
			is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v.attr_str)
			if not is_per then
				local attr_name = ToColorStr(EquipmentWGData.Instance:GetAttrNameByAttrStr(v.attr_str, true), xp_name_color)
				if attr_name then
					local temp = {}
					temp.attr_name = attr_name
					temp.attr_value = ToColorStr(v.attr_value, xp_name_color)
					strong_list[#strong_list + 1] = temp
				end

				base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_str) or 0
				self.common_pingfen_num = self.common_pingfen_num + v.attr_value * base_num
				attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
			else --百分比属性
				if v.attr_value > 0 then
					local spe_temp = {}
					spe_temp.attr_name = ToColorStr(EquipmentWGData.Instance:GetAttrNameByAttrStr(v.attr_str), xp_name_color)
					per_value = is_per and v.attr_value / 100 or v.attr_value
					per_str = is_per and "%" or ""
					spe_temp.attr_value = ToColorStr(per_value .. per_str, xp_name_color)
					base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_str) or 0
					self.common_pingfen_num = self.common_pingfen_num + v.attr_value * base_num
					attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
					table.insert(strong_list, spe_temp)
				end
			end
		end

		local rand_info_list = {}
		rand_info_list.attr_list = strong_list
		rand_info_list.title_name = Language.ShiTianSuit.TipsTitleName2
		self.base_tips:SetStrongAttribute(rand_info_list)
	end

	if not IsEmptyTable(strengthen_attr_list) then
		for k, v in pairs(strengthen_attr_list) do
			attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
		end
	end

	if not IsEmptyTable(stone_attr_list) then
		for attr_str, attr_value in pairs(stone_attr_list) do
			attribute[attr_str] = attribute[attr_str] + attr_value
		end
	end

	if not IsEmptyTable(infuse_soul_attr_list) then
		for k, v in pairs(infuse_soul_attr_list) do
			attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
		end
	end

	--名字
	local star_str = CommonDataManager.GetAncientNumber(star_level)
	local item_name = item_cfg.name

	if star_level > 0 then
		item_name = string.format(Language.ShiTianSuit.EquipStarName, star_str, item_cfg.name)
	end

	if strengthen_level > 0 then
		item_name = string.format(Language.ShiTianSuit.TipsStrengthenLevel, item_name, strengthen_level)
	end

	self.base_tips:SetItemName(item_name)
	self.item_cell:SetLeftTopImg(star_level or 0)

	local tips_info = ShiTianSuitWGData.Instance:GetSuitRewradCfg(hole_cfg.suit_seq)
	self.base_tips:SetShiTianSuitAttribute(tips_info, hole_cfg.suit_seq)

	-- 装备评分
	local normal_attr_info = {}
	local str_limit_level = RoleWGData.GetLevelString(item_cfg.limit_level)
	normal_attr_info[1] = {name = Language.F2Tip.LvName, label = str_limit_level}
	normal_attr_info[2] = {name = Language.F2Tip.PingFen, label = math.ceil(self.common_pingfen_num)}
	--normal_attr_info[3] = {name = Language.F2Tip.CompPingFen, label = math.ceil(self.comp_pingfen_num)}
	self.base_tips:SetNormalAttribute(normal_attr_info)

	-- 战力
	local capability = AttributeMgr.GetCapability(attribute)
	self.base_tips:SetCapabilityPanel({capability = capability})

	local suit_strengthen_info = ShiTianSuitStrengthenWGData.Instance:GetTipsInfo(hole_cfg.suit_seq, hole_cfg.part)
	self.base_tips:SetShiTianSuitStrengthenInfo(suit_strengthen_info, hole_cfg.suit_seq, hole_cfg.part, from_shitian_view)
end

--武魂系统魂石
function ItemTip:ParseWuHunFrontGemTip(item_cfg, data)
	if IsEmptyTable(item_cfg) or IsEmptyTable(data) then
		return
	end

	self.common_pingfen_num = 0
	--标题
	local rich_type = string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE,
									Language.ShowItemType[1])
	self.base_tips:SetEquipSocre(rich_type)

	-- 使用等级
	local rich_level = string.format(Language.Tip.ShiYongDengJiNew, TIPS_COLOR.SOCRE,
	RoleWGData.GetLevelString(item_cfg.limit_level))
	self.base_tips:SetSyntheticalSocre(rich_level)

	-- 物品描述
	local desc_info = {}
	local base_num = 0
	local description = item_cfg.description
	if description and description ~= "" then
		desc_info[#desc_info + 1] = {title = Language.F2Tip.WuPinMiaoShu, desc = description}
	end
	self.base_tips:SetItemDesc(desc_info)

	local active_cfg = WuHunFrontWGData.Instance:GetIsFrontGemByItemId(data.item_id)
	local wuhun_id, front_index, gem_index = active_cfg.wuhun_id, active_cfg.soul_front_seq, active_cfg.soul_stone_seq
	local gem_data = WuHunFrontWGData.Instance:GetWuHunFrontGemData(wuhun_id, front_index, gem_index) or {}
	local gem_star = gem_data.gem_star == COMMON_GAME_ENUM.FUYI and 0 or gem_data.gem_star
	local gem_level = gem_data.gem_level or 0
	local star_cfg = WuHunFrontWGData.Instance:GetFrontGemStarCfg(wuhun_id, front_index, gem_index, gem_star)
	local level_cfg = WuHunFrontWGData.Instance:GetFrontGemLevelCfgByLevel(gem_level)

	local attribute = AttributePool.AllocAttribute()
	local gem_attr_data = {}
	gem_attr_data.star_attrs = {}    --升星的属性
	gem_attr_data.level_attrs = {}   --升级的属性

	--基础属性
	if star_cfg then
		local strong_list = {}
		for i = 1, 5 do
			local attr_key = star_cfg["attr_id" .. i]
			local attr_value = star_cfg["attr_value" .. i]

			if attr_key and attr_key > 0 and attr_value >= 0 then
				local attr_name1 = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_key)
				local value_str, name_str = WuHunFrontWGData.Instance:GetAttrChangeValue(attr_key, attr_value, true)
				strong_list[#strong_list + 1] = {attr_name = name_str, attr_value = value_str}

				base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(attr_name1) or 0
				self.common_pingfen_num = self.common_pingfen_num + attr_value * base_num
				
				attribute[attr_name1] = attr_value
			end
		end
		gem_attr_data.star_attrs = strong_list
		local star_info_list = {}
		star_info_list.attr_list = strong_list
		star_info_list.title_name = Language.ShiTianSuit.TipsTitleName
		self.base_tips:SetPinkAttribute(star_info_list)
	end

	--升级属性
	if level_cfg then
		local strenge_list = {}
		local xp_name_color = ITEM_COLOR[item_cfg.color] or "#A8A5A4FF"
		for	i = 1, 5 do
			local attr_key = level_cfg["attr_id" .. i]
			local attr_value = level_cfg["attr_value" .. i]
			if attr_key and attr_key > 0 and attr_value >= 0 then
				local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_key)
				local attr_name = ToColorStr(EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str, true), xp_name_color)
				local temp = {}
				temp.attr_name = attr_name
				temp.attr_value = ToColorStr(attr_value, xp_name_color)
				strenge_list[#strenge_list + 1] = temp

				base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(attr_str) or 0
				self.common_pingfen_num = self.common_pingfen_num + attr_value * base_num
			
				attribute[attr_str] = attr_value
			end
		end

		gem_attr_data.level_attrs = strenge_list
		local rand_info_list = {}
		rand_info_list.attr_list = strenge_list
		rand_info_list.title_name = Language.WuHunZhenShen.WuhunFrontBaoshiTxt1
		self.base_tips:SetStrongAttribute(rand_info_list)
	end

	--名字
	self.base_tips:SetItemName(item_cfg.name)

	-- 装备评分
	local normal_attr_info = {}
	local str_limit_level = RoleWGData.GetLevelString(item_cfg.limit_level)
	normal_attr_info[1] = {name = Language.F2Tip.LvName, label = str_limit_level}
	normal_attr_info[2] = {name = Language.F2Tip.PingFen, label = math.ceil(self.common_pingfen_num)}
	self.base_tips:SetNormalAttribute(normal_attr_info)

	-- 战力
	local capability = AttributeMgr.GetCapability(attribute)
	self.base_tips:SetCapabilityPanel({capability = capability})

	self.base_tips:SetWuHunFrontGemInfo(gem_attr_data, active_cfg, gem_data)
end

function ItemTip:FlushShiTianZuoQiDisplayModel(item_cfg, data)
	if IsEmptyTable(item_cfg) or IsEmptyTable(data) then
		return
	end

	local hole_cfg = ShiTianSuitWGData.Instance:GetShiTIanEquipHole(data.item_id)
	local suit_description_cfg = ShiTianSuitWGData.Instance:GetSuitTypeCfgBySeq(hole_cfg.suit_seq)
	local res_id = suit_description_cfg and suit_description_cfg.res_id or 0
	if tonumber(res_id) > 0 then
		local display_model = self.base_tips:GetModle(true, MODEL_CAMERA_TYPE.BASE)
		if display_model then
			local bundle, asset = ResPath.GetMountModel(tonumber(res_id))
			display_model:SetMainAsset(bundle, asset)
		end
	end
end

--显示描述description
function ItemTip:ShowItemDescription(item_cfg)
	if item_cfg == nil then
		return
	end
	local info_list = {}

	local description = ItemWGData.Instance:GetItemConstDesc(item_cfg, self.data.num)
	if item_cfg.use_type == Item_Use_Type.ROLE_EXP_DAN then               -- 使用类型为84的具体描述读配置
		local exp_num = self:GetLeveReward(item_cfg.param1)
		local exp_per = RoleWGData.Instance:GetRoleExpCorrection()
		exp_num = exp_num * exp_per
		description = string.format(description, CommonDataManager.ConverExp(exp_num))
	end

	description = CommonDataManager.ParseGameName(description)
	if description and description ~= "" then
		-- 碎片的物品描述内加上已拥有数量
	    if item_cfg.is_chip == 1 and not FairyLandEquipmentWGData.Instance:GetIsGodBookChip(item_cfg.id) then
	    	local has_num = ToColorStr(ItemWGData.Instance:GetItemNumInBagById(item_cfg.id), COLOR3B.C8)
	    	local has_num_str = ToColorStr(string.format(Language.F2Tip.HasItemNumStr, has_num), COLOR3B.C4)
	    	info_list[#info_list + 1] = {title = Language.F2Tip.WuPinMiaoShu, desc = string.format("%s\n%s", description, has_num_str)}
	    else
	    	info_list[#info_list + 1] = {title = Language.F2Tip.WuPinMiaoShu, desc = description}
	    end
	end

	-- 限时道具 描述显示获取时间%s年%月%日%s时 （有效期%s小时）
	if self.data.invalid_time and self.data.invalid_time > 0 then
		if not ItemWGData.GetIsXiaogGui(self.data.item_id) and item_cfg.time_length ~= "" and item_cfg.time_length > 0 then
			local remaining_time = self.data.invalid_time - TimeWGCtrl.Instance:GetServerTime()
			if remaining_time > 0 and info_list[1] then
				local get_time = self.data.invalid_time - item_cfg.time_length
				local get_time_tab = os.date("*t", get_time)
				local str = string.format(Language.F2Tip.TimeLimitItemTip, get_time_tab.year, get_time_tab.month, get_time_tab.day, get_time_tab.hour, item_cfg.time_length / 3600)
				info_list[1].desc = info_list[1].desc .. str
			end
		end
	end

	local desc = nil
	for i = 2, 6 do
		desc = item_cfg["description" .. i]
		if desc and desc ~= "" then
			info_list[#info_list + 1] = {title = item_cfg["biaoti" .. i], desc = desc}
		-- elseif i == 2 and item_cfg.search_type == 1500 then
		-- 	desc = ItemWGData.Instance:AutoGetGiftDropDesc(item_cfg.id)
		-- 	info_list[#info_list + 1] = {title = item_cfg["biaoti" .. i], desc = desc}
		end
	end

	local capability = 0
	local need_get_sys, sys_type, replace_idx, show_cap_type = false, 0, 2, TIPS_CAP_SHOW.SHOW_CFG

	local function set_need_sys_attr(show_data, sys_attr_cap_location, ignore_desc)
		local attr_desc = ""
		need_get_sys, sys_type, replace_idx, show_cap_type = ItemShowWGData.Instance:GetIsNeedSysAttr(show_data.item_id, sys_attr_cap_location)
		if need_get_sys then
			attr_desc, capability = ItemShowWGData.Instance:GetItemAttrDescAndCap(show_data, sys_type, self:IsShowMaxCap(sys_type))
			if attr_desc ~= "" and not ignore_desc then
				local temp_title = Language.F2Tip.SysAttrTitle[sys_type] or Language.F2Tip.SysAttrTitle[0]
				if self.from_view == ItemTip.FROM_MINGWEN_BAG or self.from_view == ItemTip.FROM_LONGHUN_BAG or self.from_view == ItemTip.FROM_LONGHUN_EQUIP then
					temp_title = Language.F2Tip.SysCurAttrTitle[1]
				end

				local temp_data = {title = temp_title, desc = attr_desc}
				table.insert(info_list, replace_idx, temp_data)
			end
		end
    end

	local sys_attr_cap_location = item_cfg.sys_attr_cap_location
    local revert_item_id = ItemWGData.Instance:GetComposeProductid(self.data.item_id)
	local data = self.data
	if revert_item_id ~= self.data.item_id then
		data = {}
	    local item_cfg1 = ItemWGData.Instance:GetItemConfig(revert_item_id)
	    for k,v in pairs(self.data) do
	    	data[k] = v
	    end
	    data.item_id = revert_item_id

		sys_attr_cap_location = item_cfg1.sys_attr_cap_location
	end

    --如果是材料，需要显示合成的目标装备的属性，配置转成目标装备配置
    set_need_sys_attr(data, sys_attr_cap_location)
	local agent_tip_desc = ItemWGData.Instance:GetAgentTipsDesc(item_cfg.id)
	local rep_index = 2
	if agent_tip_desc then
		table.insert(info_list, rep_index, agent_tip_desc)
	end
	self.base_tips:SetItemDesc(info_list)

	if item_cfg.search_type == 1500 and not DrawGiftWGData.Instance:IsDrawGift(item_cfg.id) then
		local gift_reward_list =  ItemWGData.Instance:GetGiftDropList(item_cfg.id, 1)
		self.base_tips:SetGiftItemListShowInfo(gift_reward_list)
	end

	if capability == nil then
		print_error("----Why capability will get nil----", item_cfg.id)
		capability = 0
	end

	local have_cfg_cap = item_cfg.capability_show and item_cfg.capability_show > 0
	if need_get_sys and show_cap_type == TIPS_CAP_SHOW.SHOW_CALC and capability and capability > 0 then
		if self:IsShowMaxCap(sys_type) then
			self.base_tips:SetMaxCapabilityPanel({capability = capability})
		else
			self.base_tips:SetCapabilityPanel({capability = capability})
		end
	elseif (need_get_sys and show_cap_type == TIPS_CAP_SHOW.SHOW_CALC) or (not need_get_sys or not show_cap_type) then
		if have_cfg_cap then
			self.base_tips:SetCapabilityPanel({capability = item_cfg.capability_show})
		end
	end
end

-- 展示资质
function ItemTip:ShowFlairObj()
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if item_cfg == nil then
		return
	end

	local display_type = item_cfg.is_display_role
	if display_type == DisplayItemTip.Display_type.BEASTS then
		local info_list = {}
		info_list.title = Language.F2Tip.BeastFlair
		if ControlBeastsWGData.Instance:CheckIsHolyBeastUnlockItemId(self.data.item_id) then
			info_list.beast_id = item_cfg.param2 or 0
		else
			info_list.beast_id = self.data.item_id
		end
		info_list.is_beast = self.data.is_beast
		info_list.bag_id = self.data.bag_id
		info_list.skill_title = Language.F2Tip.BeastSkill
		info_list.nor_skill_fun = function()
			ControlBeastsWGData.Instance:ShowBeastSkill(info_list.beast_id, true)
		end
		info_list.skill_fun = function()
			ControlBeastsWGData.Instance:ShowBeastSkill(info_list.beast_id, false)
		end

		info_list.sp_skill_fun = function()
			ControlBeastsWGData.Instance:ShowSpBeastSkill(info_list.beast_id, false)
		end
		
		self.base_tips:SetBeastFlairShowInfo(info_list)
	end
end

-- Tips是否显示满级战力
function ItemTip:IsShowMaxCap(sys_type)
	return false

	-- if sys_type == ITEMTIPS_SYSTEM.MOUNT or
	-- 	sys_type == ITEMTIPS_SYSTEM.LING_CHONG or
	-- 	sys_type == ITEMTIPS_SYSTEM.TS_HUANHUA or
	-- 	sys_type == ITEMTIPS_SYSTEM.HUA_KUN or
	-- 	sys_type == ITEMTIPS_SYSTEM.XIAN_WA
	-- 	then
	-- 	return true
	-- else
	-- 	return false
	-- end
end

--显示丹药经验次数
function ItemTip:ShowItemExpDesc(item_cfg)
	local info_list = {}
	local use_num = 1
	if item_cfg.use_type == Item_Use_Type.ROLE_EXP_DAN or 1 == item_cfg.use_type then
		local use_dan_list = ItemWGData.Instance:SetLimitNumByUseTimeList()
		local is_first = true
		for k,v in pairs(use_dan_list) do
			if item_cfg.id == v.item_id then
				use_num = v.use_num
				is_first = false
				break
			end
		end

		local cfg = ItemWGData.Instance:GetLimitNumByUseTime(use_num)
		if IsEmptyTable(cfg) then
			return
		end

		local msg = ToColorStr(cfg.limit_num / 100 .. "%", COLOR3B.DEFAULT_NUM)
		use_num = is_first and 0 or use_num
		info_list.desc = string.format(Language.F2Tip.ExpDesc1, use_num)
		info_list.xiushici = string.format(Language.F2Tip.ExpDesc2, msg)
	end

	if not IsEmptyTable(info_list) then
		info_list.title = Language.F2Tip.ExpDesc1
		self.base_tips:SetExpDesc(info_list)
	end
end

--显示获得途径
function ItemTip:ShowItemGetDesc(item_cfg)
	local info_list = {}
	if (item_cfg.get_msg and 0 < string.len(item_cfg.get_msg)) or not item_cfg.get_way or 0 == string.len(item_cfg.get_way) then
		local get_msg = item_cfg.get_msg
		if not get_msg or 0 == string.len(get_msg) then
			return
		end
		if item_cfg.is_display_role ~= 0 then
			info_list.desc = ToColorStr(string.format(Language.Common.TipsBrackets, get_msg), TIPS_COLOR.GETWAY_VALUE)
		else
			info_list.desc = ToColorStr(string.format(Language.Common.TipsBrackets, get_msg), TIPS_COLOR.GETWAY_VALUE)
		end
	elseif (item_cfg.get_way_des and 0 < string.len(item_cfg.get_way_des)) then
		local get_msg = item_cfg.get_way_des
		if not get_msg or 0 == string.len(get_msg) then
			return
		end
		info_list.desc = ToColorStr(string.format(Language.Common.TipsBrackets, get_msg), TIPS_COLOR.GETWAY_VALUE)
	end
	if item_cfg.xiushici and item_cfg.xiushici ~= "" then
		info_list.xiushici = item_cfg.xiushici
	end
	if not IsEmptyTable(info_list) then
		info_list.title = Language.F2Tip.GetWayStr
		self.base_tips:SetGetWayDesc(info_list)
	end
end

function ItemTip:OnClickGoToGet(open_panel)
	if open_panel == GuideModuleName.Compose then
		FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, nil, {is_stuff = false, item_id = self.data.item_id})
	elseif open_panel == "guaji" then  						--挂机按钮点击去挂机
		TaskWGData.Instance:GuaJiGetMonster()
	else
		FunOpen.Instance:OpenViewNameByCfg(open_panel)
	end
	self:Close()
end

function ItemTip:GetLeveReward(param)
	local role_reward_cfg = ConfigManager.Instance:GetAutoConfig("role_level_reward_auto").level_reward_list
	local role_level = RoleWGData.Instance.role_vo.level
	if role_reward_cfg and role_reward_cfg[role_level] then
		return role_reward_cfg[role_level]["exp_dan_exp_" .. param + 1]
	end
end

function ItemTip:SetEquipColorBg(color, show_condition)
	self.base_tips:SetTopColorBg(color, show_condition)
end

function ItemTip:SetEquipTopLongEffectShow(color, show_condition)
	self.base_tips:SetTopLongEffectShow(color, show_condition)
end

function ItemTip:SetEquipTipsPanelEffectShow(tips_effect_bundle, tips_effect_name)
	self.base_tips:SetTipsPanelEffectShow(tips_effect_bundle, tips_effect_name)
end

function ItemTip:SetEquipTipsPanelButtomEffectShow(tips_effect_bundle, tips_effect_name)
	self.base_tips:SetTipsPanelBottomEffectShow(tips_effect_bundle, tips_effect_name)
end

--是否显示精练属性
function ItemTip:CanShowEquipStoneJLAttr()
	return self.from_view == ItemTip.FROM_BAG_EQUIP
			or self.from_view == ItemTip.FROM_EQUIPMENT
            or self.from_view == ItemTip.FROME_BROWSE_ROLE
            or self.from_view == ItemTip.FORM_ROBOT_SHOW_INFO
end

--	刷新小鬼模型
function ItemTip:FlushXiaoGuiDisplayModel(item_cfg)
	local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(item_cfg.id)
	if xiaogui_cfg then
		local display_model = self.base_tips:GetModle(true, MODEL_CAMERA_TYPE.BASE)
		if display_model then
			local bundle, asset = ResPath.GetGuardModel(xiaogui_cfg.appe_image_id)
			display_model:SetMainAsset(bundle, asset)
		end
	end
end

function ItemTip:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.ItemTipBtn then
		local index = tonumber(ui_param) or 1
		if self.base_tips.tips_node_list["btns"] then
			for k, v in pairs(self.base_tips.tips_node_list["btns"]) do
				if v:GetActive() then
					if "tip_btn_"..index == k then
						return v
					end
				end
			end
		end

	end
end

function ItemTip:SetAttrStone()
	local data = self.data
	local data_param = data.param
	local baoshi_t = data_param.baoshi_t or {}

	local function get_attr_list(stone_cfg, baoshi_color)
		local attr_list = {}
		for j = 1, GameEnum.EQUIP_BAOSHI_ATTR_NUM do
			local type = stone_cfg["attr_type" .. j]
			local value = stone_cfg["attr_val" .. j]
			if type and value and type > 0 and value > 0 then
				local temp = {}
				local name_str = EquipmentWGData.Instance:GetAttrNameByAttrId(type, true)
				temp.attr_str = ToColorStr(name_str, baoshi_color)
				local is_per = EquipmentWGData.Instance:GetAttrIsPer(type)
				local value_str = ""
				if is_per then
					value_str = string.format("+%.2f%%", value / 100)
				else
					value_str = "+" .. value
				end
				temp.attr_num = ToColorStr(value_str, baoshi_color)
				attr_list[j] = temp
				-- 宝石属性评分
				local num = TipWGData.Instance:GetXianPingSpecialAttrByOrder(type, value)
				self.comp_pingfen_num = self.comp_pingfen_num + num
			else
				break
			end
		end
		return attr_list
	end

	local stone_info_list = {}
	local baoshi_item_id = 0
	for i = 1, GameEnum.MAX_STONE_COUNT do
		local temp = {}
		local slot_data = baoshi_t[i - 1]
		baoshi_item_id = slot_data and slot_data.item_id or 0
		if baoshi_item_id > 0 then
			local baoshi_cfg = ItemWGData.Instance:GetItemConfig(baoshi_item_id)
			local stone_cfg = EquipmentWGData.Instance:GetBaoShiCfgByItemId(baoshi_item_id)
			if baoshi_cfg and stone_cfg then
				local baoshi_color = ITEM_COLOR[baoshi_cfg.color]
				temp.name = ToColorStr(baoshi_cfg.name, baoshi_color)
				temp.icon = "a3_bsi_" .. stone_cfg.stone_type
				temp.attr_list = get_attr_list(stone_cfg, baoshi_color)
				temp.sort = i
			end
		else
			local is_open = slot_data and slot_data.is_open == 1
			temp.is_lock = not is_open
			temp.sort = is_open and (i + 100) or (i + 1000)
		end
		stone_info_list[i] = temp
	end

	table.sort(stone_info_list, SortTools.KeyLowerSorter("sort"))
	self.base_tips:SetStoneAttribute(stone_info_list)
end

function ItemTip:SetAttrYuLing(equip_index)
	local data = self.data
	local yuling_data = data.yuling or {}
	local yuling_info_list = {}

	for i = GameEnum.EQUIP_INDEX_TOUKUI, GameEnum.EQUIP_INDEX_XIANZHUO do
		local temp = {}
		local equip_yuling_item_data = yuling_data[i] or {}
		local is_unlock = equip_yuling_item_data and equip_yuling_item_data.is_unlock or 0
		local is_open = is_unlock == 1
		if is_open then
			temp.is_lock = not is_open
			temp.icon = "a3_fl_bs_" .. equip_yuling_item_data.show_icon
			temp.sort = i
		else
			temp.is_lock = not is_open
			temp.sort = is_open and (i + 100) or (i + 1000)
		end

		yuling_info_list[i] = temp
	end

	if not IsEmptyTable(yuling_data) then
		yuling_info_list.add_per = yuling_data.equip_part_add_per or 0
		local equip_attr_data = EquipmentWGData.Instance:GetEquipCapAndAttrList(equip_index)
		yuling_info_list.attr_list = not IsEmptyTable(equip_attr_data) and equip_attr_data.attr_list or {}
	else
		yuling_info_list.attr_list = {}
		yuling_info_list.add_per = 0
	end
	
	table.sort(yuling_info_list, SortTools.KeyLowerSorter("sort"))
	--暂时先去掉御灵
	--self.base_tips:SetYuLingAttribute(yuling_info_list)
end

--铸神
function ItemTip:SetAttrZhuShen(equip_index)
	local equip_level = 0
	if self.from_view == ItemTip.FROM_ZHUHUN_EQUIP or self.from_view == ItemTip.FROM_BAG_EQUIP then
		equip_level = EquipmentWGData.Instance:GetZhuShenEquipPartInfo(equip_index)
	elseif self.from_view == ItemTip.FROM_ZHUHUN_EQUIP_NEXT then
		equip_level = EquipmentWGData.Instance:GetZhuShenEquipPartInfo(equip_index)
		equip_level = equip_level + 1
	end

	local equip_attr_cfg = EquipmentWGData.Instance:GetZhuShenEquipAttrList(equip_index, equip_level)
	local base_num = 0
	local attribute = AttributePool.AllocAttribute()
	
	if not IsEmptyTable(equip_attr_cfg) then
		local strong_list = {}
		for k,v in pairs(equip_attr_cfg) do
			local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(v.attr_str, true)
				local temp = {
					attr_name = attr_name,
					attr_value = v.attr_value
				}
				strong_list[#strong_list + 1] = temp

			base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_str) or 0
			self.common_pingfen_num = self.common_pingfen_num + v.attr_value * base_num

			attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
		end

		local star_info_list = {}
		star_info_list.attr_list = strong_list
		star_info_list.title_name = Language.EquipmentZhuShen.ZhuHunTipsAttr
		self.base_tips:SetZhuShenAttribute(star_info_list)
	end
end

--灵玉属性
function ItemTip:SetAttrLingYu()
	local data = self.data
	local data_param = data.param
	local lingyu_t = data_param.lingyu_t or {}

	local function get_attr_list(lingyu_cfg, lingyu_color)
		local attr_list = {}
		for j = 1, GameEnum.EQUIP_LINGYU_ATTR_NUM do
			local type = lingyu_cfg["attr_type" .. j]
			local value = lingyu_cfg["attr_val" .. j]
			if type and value and type > 0 and value > 0 then
				local temp = {}
				local name_str = EquipmentWGData.Instance:GetAttrNameByAttrId(type, true)
				temp.attr_str = ToColorStr(name_str, lingyu_color)
				local is_per = EquipmentWGData.Instance:GetAttrIsPer(type)
				local value_str = ""
				if is_per then
					value_str = string.format("+%.2f%%", value / 100)
				else
					value_str = "+" .. value
				end
				temp.attr_num = ToColorStr(value_str, lingyu_color)
				attr_list[j] = temp
				-- 灵玉属性评分
				local num = TipWGData.Instance:GetXianPingSpecialAttrByOrder(type, value)
				self.comp_pingfen_num = self.comp_pingfen_num + num
			else
				break
			end
		end
		return attr_list
	end

	local lingyu_info_list = {}
	local lingyu_item_id = 0
	local can_show_lingyu = false

	for i = 1, GameEnum.MAX_LINGYU_COUNT do
		local temp = {}
		local slot_data = lingyu_t[i - 1]
		lingyu_item_id = slot_data and slot_data.item_id or 0

		if lingyu_item_id > 0 then
			local item_cfg = ItemWGData.Instance:GetItemConfig(lingyu_item_id)
			local lingyu_cfg = EquipmentLingYuWGData.Instance:GetLingYuCfgByItemId(lingyu_item_id)

			if item_cfg and lingyu_cfg then
				local lingyu_color = ITEM_COLOR[item_cfg.color]
				temp.name = ToColorStr(item_cfg.name, lingyu_color)
				temp.icon = LINGYU_TYPE_ICON[lingyu_cfg.lingyu_type]
				temp.attr_list = get_attr_list(lingyu_cfg, lingyu_color)
				temp.sort = i
				can_show_lingyu = true
			end
		else
			local is_open = slot_data and slot_data.is_open == 1
			temp.is_lock = not is_open
			temp.sort = is_open and (i + 100) or (i + 1000)
		end
		lingyu_info_list[i] = temp
	end

	if can_show_lingyu and not IsEmptyTable(lingyu_info_list) then
		table.sort(lingyu_info_list, SortTools.KeyLowerSorter("sort"))
		self.base_tips:SetLingYuAttribute(lingyu_info_list)
	end
end

function ItemTip:SetAttrShengPin(list, item_cfg, equip_part)
	local info_list = {}
	local star_count = EquipmentWGData.Instance:GetNewEquipShengPinStarInfoByPart(equip_part) or 0
	if star_count > 0 and self.data.frombody then
		local attr_list = {}
		local shengpin_cfg = EquipmentWGData.Instance:GetNewEquipShengPinCfg(star_count, equip_part)
		local text_color = ITEM_COLOR[shengpin_cfg.star_color or 0]
		local quality_text = Language.Equip.ShengPin[shengpin_cfg.quality_grade + 1]
		for i,v in ipairs(ShengPinSortAttrList) do
			if shengpin_cfg[v] and shengpin_cfg[v] > 0 then
				-- local score_cfg = EquipmentWGData.Instance:GetBaptizeAttrScore(v)
				-- if score_cfg and not IsEmptyTable(score_cfg) then
				-- 	local pingfen = TipWGData.Instance:GetCommonPingFenCfgByIndex(v)
				-- 	self.comp_pingfen_num = self.comp_pingfen_num + pingfen * shengpin_cfg[v]
				-- end
				local temp = {}
				temp.attr_name = Language.Common.AttrNameList2[v]
				temp.attr_value = ""
				if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v) then
					temp.attr_value = string.format("%s%%", shengpin_cfg[v] / 100)
				else
					temp.attr_value = shengpin_cfg[v]
				end
				temp.attr_value = temp.attr_value .. string.format(Language.Equip.ShengPinLimitText6, quality_text, star_count % 10)
				temp.attr_name = ToColorStr(temp.attr_name, text_color)
				temp.attr_value = ToColorStr(temp.attr_value, text_color)
				attr_list[#attr_list + 1] = temp
			end
		end

		if shengpin_cfg.quality_grade > 0 then
			local _, attr_per, attr_type = EquipmentWGData.Instance:GetNewShengPinAttrCfgByQuality(equip_part, shengpin_cfg.quality_grade)
			if attr_per > 0 then
				local temp = {}
				local attr_type_text = attr_type == "base_attr_per" and Language.Tip.CommonAttr or Language.Common.TipsAttrNameList[attr_type] or ""
				temp.attr_name = string.format("%s%s", Language.Stone[equip_part], attr_type_text)
				temp.attr_value = string.format("%s%%（%s）", attr_per * 0.01, quality_text)
				temp.attr_name = ToColorStr(temp.attr_name, text_color)
				temp.attr_value = ToColorStr(temp.attr_value, text_color)
				attr_list[#attr_list + 1] = temp
			end
		end

		info_list.attr_list = attr_list
		info_list.title_name = Language.F2Tip.ShenPinAttrTitle
	end

	self.comp_pingfen_num = self.comp_pingfen_num + self.shenping_base_pingfen_per
	if not IsEmptyTable(info_list) then
		self.base_tips:SetShengPinAttribute(info_list)
	end
end

function ItemTip:SetXianPinAttrInfo(item_cfg)
	local legend_attr_list = {}
	local legend_num = 0
	local is_exhibition = false 				-- 是否活动界面展示属性
    if self.data.param.xianpin_type_list then
        if self.from_view == ItemTip.FORM_ROBOT_SHOW_INFO then
            legend_num = self.data.param.star_level or 0
            legend_attr_list = EquipWGData.GetPreviewLegendAttr(self.data, legend_num)
        else
            local show_baptize_add = self:CanShowEquipStoneJLAttr()
            legend_attr_list = EquipWGData.GetLegendAttr(self.data, show_baptize_add)
            legend_num = #legend_attr_list
        end
	else
		if self.from_view == ItemTip.FROM_FULILOGIN then
            legend_num = self.data.star_level or 0
		else
			legend_num = self.data.param.star_level or 0
		end

		legend_attr_list = EquipWGData.GetPreviewLegendAttr(self.data, legend_num)
		is_exhibition = true
	end

	if legend_num < 1 then
		return
	end

	local info_list = {}
	local attr_list = {}
	for i,v in ipairs(legend_attr_list) do
		local temp = {}
		local per_value = 1
		local per_str = ""
		if v.is_per and v.is_per ~= "" and v.is_per == 1 then
			per_value = 0.01
			per_str = "%"
		end

        if is_exhibition then
            if self.from_view == ItemTip.FORM_ROBOT_SHOW_INFO then
                local orgin_pingfen_num = TipWGData.Instance:GetSpecialPingFenCfgByOrder(item_cfg.order, v.attr_type, v.orgin_value, v.is_star_attr)
                local add_pingfen_num = TipWGData.Instance:GetSpecialPingFenCfgByOrder(item_cfg.order, v.attr_type, v.value, v.is_star_attr)
                self.common_pingfen_num = self.common_pingfen_num + orgin_pingfen_num
                self.comp_pingfen_num = self.comp_pingfen_num + add_pingfen_num
                local color = ITEM_COLOR[v.baptize_quality] or ITEM_COLOR[0]
                if v.value and v.value > 0 then
                    local to_ceil = math.ceil(v.value)
                    to_ceil = to_ceil * per_value
                    local to_show_num = math.floor(to_ceil * 100)
                    to_show_num = to_show_num * 0.01

                    temp.label = ToColorStr(string.format("%s  %s%s", v.desc, to_show_num, per_str), color)
                else
                    temp.label = ToColorStr(v.desc, color)
                end

			info_list.title_name = Language.Tip.XianShuXing
            else
                local desc1 = v.must_get and Language.Role.EquipDesc3 or Language.Role.EquipDesc1
                local desc1_color = v.must_get and "#fef07c" or COLOR3B.DEFAULT
                local desc2 = string.format("<color=%s>%s</color>   <color=%s>%s</color>", TIPS_COLOR.ATTR_NAME, v.desc, TIPS_COLOR.ATTR_VALUE, v.value * per_value)
                temp.label = ToColorStr(desc1, desc1_color) .. "    " .. desc2
                info_list.title_name = string.format(Language.Tip.RandXianShuXing, legend_num)
            end
		else
			local orgin_pingfen_num = TipWGData.Instance:GetSpecialPingFenCfgByOrder(item_cfg.order, v.attr_type, v.orgin_value, v.is_star_attr)
			local add_pingfen_num = TipWGData.Instance:GetSpecialPingFenCfgByOrder(item_cfg.order, v.attr_type, v.value, v.is_star_attr)
			self.common_pingfen_num = self.common_pingfen_num + orgin_pingfen_num
			self.comp_pingfen_num = self.comp_pingfen_num + add_pingfen_num
			local color = ITEM_COLOR[v.baptize_quality] or ITEM_COLOR[0]
			if v.value and v.value > 0 then
				local to_ceil = math.ceil(v.value)
				to_ceil = to_ceil * per_value
				local to_show_num = math.floor(to_ceil * 100)
				to_show_num = to_show_num * 0.01

				temp.label = ToColorStr(string.format("%s   %s%s", v.desc, to_show_num, per_str), color)
			else
				temp.label = ToColorStr(v.desc, color)
			end

			info_list.title_name = Language.Tip.XianShuXing
		end

		attr_list[i] = temp
	end

	if #attr_list > 0 then
		info_list.attr_list = attr_list
		self.base_tips:SetXianpinAttribute(info_list)
	end
end

function ItemTip:SetAttrSuit(item_cfg, equip_body_index)
	local info_list = {}
	local data_param = self.data.param

	if data_param.self_suit_open and data_param.suit_open_num > 0 then
		local is_get_browse_data = self.from_view == ItemTip.FROME_BROWSE_ROLE or self.from_view == ItemTip.FORM_ROBOT_SHOW_INFO
		local list_data = EquipmentWGData.Instance:GetEquipmenSuitStoneAttr(item_cfg, equip_body_index, is_get_browse_data)
		
		if not IsEmptyTable(list_data) then
			local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
			local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(equip_part)
			local complete_suit_num = EquipmentWGData.Instance:GetEquipmenSuitAllActiveNum(equip_type)
			local suit_title = EquipmentWGData.Instance:GetEquipmenSuitTitleName(equip_body_seq, equip_type)
			info_list.suit_title = string.format("%s [ %d/%d ]", suit_title, data_param.suit_open_num, complete_suit_num)

			local suit_attr_list = {}
			for i, data in ipairs(list_data) do
				-- 计算评分
				local temp_list = {}
				local attr_color = data.is_open and COLOR3B.D_GREEN or COLOR3B.GRAY
				for k, v in ipairs(data.attr_list) do
					local temp = {}
					temp.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(v.attr_type, true)
					temp.attr_value = ""

					if not EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v.attr_type) then
						temp.attr_value = v.value
						if data.is_open then
							local base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_type)
							self.comp_pingfen_num = self.comp_pingfen_num + base_num * v.value
						end
					else
						temp.attr_value = string.format("%.2f%%", v.value / 100)
						if data.is_open then
							self.comp_pingfen_num = self.comp_pingfen_num + TipWGData.Instance:GetXianPingSpecialAttrByOrder(v.attr_type, v.value)
						end
					end

					temp.attr_name = ToColorStr(temp.attr_name, attr_color)
					temp.attr_value = ToColorStr(temp.attr_value, attr_color)
					temp_list[k] = temp
				end

				local suit_name = string.format("%d%s", data.same_order_num, Language.Equip.SuitNumCompany)
				suit_attr_list[i] = {suit_name = suit_name, attr_list = temp_list, is_open = data.is_open}
			end

			info_list.suit_attr_list = suit_attr_list
		end
	end

	if not IsEmptyTable(info_list) then
		self.base_tips:SetXianqSuitAttribute(info_list)
	end

	-- local info_list = {}
	-- local data_param = self.data.param

	-- if data_param.suit_open_num > 0 then
	-- 	local is_get_browse_data = self.from_view == ItemTip.FROME_BROWSE_ROLE or self.from_view == ItemTip.FORM_ROBOT_SHOW_INFO
	-- 	local list_data = EquipmentWGData.Instance:GetEquipmenSuitStoneAttr(data_param.suit_index, item_cfg.order, equip_index, is_get_browse_data)

	-- 	if not IsEmptyTable(list_data) then
	-- 		local suit_index_name = Language.Equip.TabSub3[data_param.suit_index + 1]
	-- 		local complete_suit_num = EquipmentWGData.Instance:GetEquipmenSuitALLNum(equip_index)
	-- 		local suit_title = ""
	-- 		if EquipmentWGData.GetEquipSuitTypeByPartType(equip_index) == GameEnum.EQUIP_BIG_TYPE_XIANQI then
	-- 			suit_title = Language.Equip.XianQiSuit
	-- 		else
	-- 			suit_title = EquipmentWGData.Instance:GetEquipmenSuitName(item_cfg.order)
	-- 		end

	-- 		info_list.suit_title = string.format("%s [ %s %d/%d ]", suit_index_name, suit_title, data_param.suit_open_num, complete_suit_num)
	-- 		local suit_attr_list = {}
	-- 		for i, data in ipairs(list_data) do
	-- 			-- 计算评分
	-- 			local temp_list = {}
	-- 			local attr_color = data.is_open and COLOR3B.D_GREEN or COLOR3B.GRAY
	-- 			for k, v in ipairs(data.attr_list) do
	-- 				local temp = {}
	-- 				temp.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(v.attr_type, true)
	-- 				temp.attr_value = ""
	-- 				if not EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v.attr_type) then
	-- 					temp.attr_value = v.value
	-- 					if data.is_open then
	-- 						local base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_type)
	-- 						self.comp_pingfen_num = self.comp_pingfen_num + base_num * v.value
	-- 					end
	-- 				else
	-- 					temp.attr_value = string.format("%.2f%%", v.value / 100)
	-- 					if data.is_open then
	-- 						self.comp_pingfen_num = self.comp_pingfen_num + TipWGData.Instance:GetXianPingSpecialAttrByOrder(v.attr_type, v.value)
	-- 					end
	-- 				end

	-- 				temp.attr_name = ToColorStr(temp.attr_name, attr_color)
	-- 				temp.attr_value = ToColorStr(temp.attr_value, attr_color)
	-- 				temp_list[k] = temp
	-- 			end

	-- 			local suit_name = string.format("%d%s", data.same_order_num, Language.Equip.SuitNumCompany)
	-- 			suit_attr_list[i] = {suit_name = suit_name, attr_list = temp_list, is_open = data.is_open}
	-- 		end
	-- 		info_list.suit_attr_list = suit_attr_list
	-- 	end
	-- end

	-- if not IsEmptyTable(info_list) then
	-- 	self.base_tips:SetXianqSuitAttribute(info_list)
	-- end
end

function ItemTip:SetBaseAttrInfo(list)
	local data = self.data
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	local data_param = data.param
	local strength_cfg = {}
	local scale_per = 0 -- 基础属性放大参数

	if data_param.strengthen_level and data_param.strengthen_level > 0 and self.data.frombody then
		local equip_part = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
		strength_cfg = EquipmentWGData.Instance:GetNewStrengthCfgByLevel(equip_part, data_param.strengthen_level)
		strength_cfg = AttributeMgr.GetAttributteByClass(strength_cfg)
	elseif TianShenWGData.Equip_Pos[item_cfg.sub_type] and data.grade_level and 1 <= data.grade_level then
		strength_cfg = TianShenWGData.Instance:GetEquipUpgradeAttr(item_cfg.sub_type, item_cfg.color, data.star_level, data.grade_level)
		strength_cfg = AttributeMgr.GetAttributteByClass(strength_cfg)
	elseif HiddenWeaponWGData.IsHiddenWeapon(item_cfg.sub_type) then
		strength_cfg = HiddenWeaponWGData.Instance:GetZLAddAttr(data)
		if data.color_attr then
			local base_attr_per = data.color_attr.base_attr_per or 0
			scale_per = base_attr_per / 10000
		else
			local star_sttr = HiddenWeaponWGData.Instance:GetShowEquipStarAttr(data)
			scale_per = star_sttr.base_attr_per / 10000
		end
	end

	local attr_tab = {}
	for k, v in pairs(list) do
		if type(v) == "number" and v > 0 then
			local value_str = ""
			if k == "ming_zhong" or k == "shan_bi" then
				value_str = v / 100 .. "%"
			elseif k == "per_pofang" or k == "per_mianshang" or k == "per_baoji" then
				value_str = v * 100 .. "%"
			else
				value_str = math.floor(v)
			end
			local strength_str = ""
			if strength_cfg[k] and strength_cfg[k] > 0 then
				if HiddenWeaponWGData.IsHiddenWeapon(item_cfg.sub_type) then
					strength_str = string.format("（%s+%d）", Language.Tip.ZhuLingText, strength_cfg[k])
				else
					strength_str = string.format("（%s+%d）", Language.Tip.StrengthText, strength_cfg[k])
				end
			end

			local temp = {}
			temp.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(k)
			temp.attr_value = value_str
			temp.add_str = strength_str
			attr_tab[k] = temp

			local base_num = 0
			if strength_cfg[k] and strength_cfg[k] > 0 then
				base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(k) or 0
				self.comp_pingfen_num = self.comp_pingfen_num + base_num * strength_cfg[k]
			end

			base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(k) or 0
			local p_num = v * base_num * (1 + scale_per)
			self.common_pingfen_num = self.common_pingfen_num + p_num
			self.comp_pingfen_num = self.comp_pingfen_num + p_num
		end
	end

	local attr_list = {}
	local attr_index = AttributeMgr.GetAttrList()
	for i,v in ipairs(attr_index) do
		if attr_tab[v] then
			-- attr_list[i] = attr_tab[v]
			table.insert(attr_list,attr_tab[v])
		end
	end
	if #attr_list > 0 then
		self.base_tips:SetBaseAttribute(attr_list)
	end
end

function ItemTip:SetAttrJiPing(item_cfg)
	local star_level = self.data.star_level
	local grade_level = self.data.grade_level
	local xianpin_list = self.data.xianpin_list
	local jiping_cfg = TianShenWGData.Instance:GetEquipUpgradeAcuraAttr(item_cfg.sub_type, item_cfg.color, star_level, grade_level)
	if not jiping_cfg then
		return
	end
	local is_exhibition = false -- 展示随机属性
	if IsEmptyTable(xianpin_list) and TianShenWGData.Equip_Pos[item_cfg.sub_type] then
		is_exhibition = true
		star_level = item_cfg.color > 3 and TianShenWGData.Instance:GetTianShenAttrNum(item_cfg.color) or 0
		xianpin_list = TianShenWGData.Instance:GetEquipRecommendAttr(item_cfg.sub_type, star_level, item_cfg.color, 3)
	end
	local attr_type = nil
	local attr_list = {}
	for i,v in ipairs(xianpin_list) do
		if v.attr_id > 0 then
			local temp = {}
			attr_type = EquipmentWGData.Instance:GetAttrStrByAttrId(v.attr_id)
			temp.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrId(v.attr_id)
			temp.attr_name = ToColorStr(temp.attr_name, COLOR3B.D_PURPLE)
			if is_exhibition then
				temp.attr_name = Language.Role.EquipDesc1 .. temp.attr_name
			end
			local is_pre = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(attr_type)
			if attr_type and is_pre then
				temp.attr_value = string.format(" +%.2f%%", v.attr_value / 100)
			else
				temp.attr_value = " +" .. v.attr_value
			end

			temp.attr_value = ToColorStr(temp.attr_value, COLOR3B.D_PURPLE)
			if grade_level > 1 then
				local add_value = jiping_cfg[attr_type]
				if is_pre then
					add_value = add_value / 100 .. "%"
				end
				temp.attr_value = string.format("%s<color=%s>（%d%s+%s）</color>", temp.attr_value, COLOR3B.D_PURPLE, grade_level, Language.Common.Jie, add_value)
			end
			attr_list[i] = temp

			-- 评分
			local num = TipWGData.Instance:GetXianPingSpecialAttrByOrder(v.attr_id, v.attr_value)
			self.comp_pingfen_num = self.comp_pingfen_num + num
			local num2 = TipWGData.Instance:GetCommonPingFenCfgByIndex(attr_type)
			if num2 and jiping_cfg[attr_type] then
				self.comp_pingfen_num = self.comp_pingfen_num + num2 * jiping_cfg[attr_type]
			end
		end
	end

	if #attr_list > 0 then
		local info_list = {}
		if is_exhibition then
			info_list.title_name = string.format(Language.Tip.RandJiPinShuXing, TianShenWGData.Instance:GetTianShenAttrNum(item_cfg.color))
		else
			info_list.title_name = Language.F2Tip.JiPinShuXing
		end
		info_list.attr_list = attr_list
		self.base_tips:SetShengPinAttribute(info_list)
	end
end

function ItemTip:ShowAttrStoneJl(equip_body_index)
	local refine_level = EquipmentWGData.Instance:GetStoneRefineLevelByPart(equip_body_index)
	local refine_cfg = EquipmentWGData.Instance:GetStoneRefineByLevel(refine_level)

	if refine_cfg then
		local info = {}
		info.title = Language.F2Tip.BSJLTitle
		info.desc = string.format(Language.Equipment.TipsRefineUpDesc, refine_cfg.add_attribute_pct / 100)
		self.comp_pingfen_num = self.comp_pingfen_num + EquipmentWGData.Instance:GetBSJLScoreByPart(equip_body_index)
		self.base_tips:SetBSJLAttribute(info)
	end
end

function ItemTip:SetAttrStrengthen(item_cfg)
	local strange_attr = TianShenWGData.Instance:GetEquipStrangeAttr(item_cfg.sub_type, self.data.stren_level)
	if IsEmptyTable(strange_attr) then
		return
	end
	local attr_list = {}
	for k,v in pairs(strange_attr) do
		local attr_name = Language.Common.TipsAttrNameList[k]
		if attr_name and v > 0 then
			local temp = {}
			temp.attr_name = attr_name
			temp.attr_value = v
			attr_list[#attr_list + 1] = temp
		end
	end
	local strange_attr_butte = AttributeMgr.GetAttributteByClass(strange_attr)
	for k,v in pairs(strange_attr_butte) do
		if v > 0 then
			local base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(k) or 0
			self.comp_pingfen_num = self.comp_pingfen_num + v * base_num
		end
	end
	if #attr_list > 0 then
		local info_list = {}
		info_list.title_name = string.format(Language.TianShen.TianShenTips1, strange_attr.strength_level or 0)
		info_list.attr_list = attr_list
		self.base_tips:SetStrongAttribute(info_list)
	end
end

function ItemTip:SetEquipSpecialAttr(item_cfg, attr_type)
	local equip_id = self.data.item_id
	local n_cfg = EquipmentWGData.Instance:GetEquipSpecialAttrCfgByIdAndType(equip_id, attr_type)
	if not n_cfg or not item_cfg then
		return
	end
	local color_list = {COLOR3B.D_PINK, COLOR3B.D_GLOD, COLOR3B.D_DAZZLING}
	local attr_list = {}
	for i = 1, 10 do
		local attr_id = n_cfg["special_type" .. i]
		local attr_value = n_cfg["special_val" .. i]
		if attr_id and attr_value and attr_value > 0 then
			local temp = {}
			local pingfen = TipWGData.Instance:GetXianPingSpecialAttrByOrder(attr_id, attr_value)
			self.common_pingfen_num = self.common_pingfen_num + pingfen
			self.comp_pingfen_num = self.comp_pingfen_num + pingfen

			local color = color_list[attr_type] or color_list[1]-- ITEM_COLOR[item_cfg.color]
			local name = EquipmentWGData.Instance:GetAttrNameByAttrId(attr_id, true)
			temp.attr_name = ToColorStr(name, color)
			local is_per = EquipmentWGData.Instance:GetAttrIsPer(attr_id)
			attr_value = is_per and (attr_value * 0.01 .. "%") or attr_value
			temp.attr_value = ToColorStr(attr_value, color)
			attr_list[#attr_list + 1] = temp
		end
	end

	if #attr_list > 0 then
		local info_list = {}
		if attr_type == EQUIP_SPECIAL_ATTR_TYPE.PINK then
			info_list.title_name = Language.Tip.PinkSuitAttr
			info_list.attr_list = attr_list
			self.base_tips:SetPinkAttribute(info_list)

		elseif attr_type == EQUIP_SPECIAL_ATTR_TYPE.GOLD then
			info_list.title_name = Language.Tip.GodSuitAttr
			info_list.attr_list = attr_list
			self.base_tips:SetGodAttribute(info_list)

		elseif attr_type == EQUIP_SPECIAL_ATTR_TYPE.COLOR then
			info_list.title_name = Language.Tip.MoreColorAttr
			info_list.attr_list = attr_list
			self.base_tips:SetMoreColorAttribute(info_list)
		end
	end
end

function ItemTip:SetXianMengCangKuAttr(data)
	self.base_tips:SetXianMengCangKuPanel(data)
end

function ItemTip:ShowEquipTipsCompose()
	local data = self.data
	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(data.item_id)
	if item_cfg == nil or big_type ~= GameEnum.ITEM_BIGTYPE_EQUIPMENT then
		return
	end

	local show_tips, process_list, show_add , str, bottom_str

	if HiddenWeaponWGData.IsHiddenWeapon(item_cfg.sub_type) then
		local cfg = HiddenWeaponWGData.Instance:GetOnlyEquipCfgById(item_cfg.id)
        local equip = data.equip or {}
		local special_flag = equip.special_flag or cfg.special_flag
		local color = equip.base_color or cfg.base_color
		local star = equip.base_star or cfg.base_star
		process_list = HiddenWeaponWGData.Instance:GetTipMakeWay(cfg.item_id, special_flag, color, star)
		if IsEmptyTable(process_list) then return end
		show_add = false
		str = Language.HiddenWeapon.TypeName[cfg.big_type] .. Language.Tip.HiddenWay
		bottom_str = Language.F2Tip.ComposeBottomDesc4
	else
		show_tips, process_list, show_add = ItemShowWGData.Instance:GetEquipComposeProcessData(data)
		if not show_tips then
			return
		end
		local equip_part = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
		local order_str = item_cfg.order--CommonDataManager.GetDaXie(item_cfg.order)
		local part_str = Language.Stone[equip_part] or ""
		str = string.format(Language.F2Tip.TipsComposeTitle, order_str, part_str)
		bottom_str = Language.F2Tip.ComposeBottomDesc1
	end

	local info = {title_desc = str, equip_list = process_list,
				bottom_desc = bottom_str,
				show_add = show_add,}
	self.base_tips:SetEquipTipsComposePanel(info)
end

function ItemTip:SetEquipSkillShow()
	local skill_cfg = EquipmentWGData.Instance:GetEquipKillCfgByItemId(self.data.item_id)
	if IsEmptyTable(skill_cfg) then
		return
	end

	local next_need_order = 0
	local next_skill_desc = ""
	local next_desc_tilte = ""
	local cur_desc_title = Language.F2Tip.SkillEffectTitle
	local cur_skill_desc = skill_cfg.desc

	-- 2024/12/30 策划HLZ修改
	-- 1、只有已穿戴最高阶显示当前效果、下级效果
	-- 2、其他装备不管穿没穿，只显示技能效果
  	-- 3、在技能描述后面，加上（多件装备只生效最高阶的效果）
	if self.data.frombody then
		local max_order_equi_id = EquipWGData.Instance:GetMaxOrderPartEquip(self.data.item_id)

		if max_order_equi_id == self.data.item_id then
			cur_desc_title = Language.F2Tip.CurSkillEffectTitle
			cur_skill_desc = ToColorStr(cur_skill_desc, COLOR3B.GREEN)
			local next_skill_cfg = EquipmentWGData.Instance:GetEquipKillCfgByItemId(skill_cfg.next_equip_id)
	
			if next_skill_cfg then
				next_skill_desc = next_skill_cfg.desc
				local next_item_cfg = ItemWGData.Instance:GetItemConfig(skill_cfg.next_equip_id)
				if next_item_cfg then
					next_need_order = next_item_cfg.order
					next_desc_tilte = string.format(Language.F2Tip.EquipSkillTitleDesc, next_need_order, Language.Common.ColorName4[next_item_cfg.color])
				end
			end
		end
	end

	local info = {
		skill_name = skill_cfg.name,
		skill_icon_id = skill_cfg.icon,
		cur_desc_title = cur_desc_title,
		cur_skill_desc = cur_skill_desc,
		have_next = next_need_order > 0,
		next_desc_tilte = next_desc_tilte,
		next_skill_desc = next_skill_desc,
		show_special_kuang = false
	}

	self.base_tips:SetEquipSkill(info)
end

function ItemTip:InitDataParam(data, item_cfg, equip_part)
	if data.frombody and equip_part >= 0 then
		local param = data.param
		local equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(item_cfg)

		if self.from_view == ItemTip.FROME_BROWSE_ROLE or self.from_view == ItemTip.FORM_ROBOT_SHOW_INFO then
			param.suit_open_num = BrowseWGData.Instance:GetEquipmenSuitOpenNum(data)
			param.self_suit_open = BrowseWGData.Instance:GetEquipmenSuitOpenFlag(equip_part) > 0
		else
			param.strengthen_level = EquipmentWGData.Instance:GetStrengthLevelByIndex(equip_body_index) --强度等级
			-- param.suit_index = EquipmentWGData.Instance:GetSuitInfoByPartType(equip_index) --套装部位
			-- local suit_data = {
			-- 	item_cfg = item_cfg,
			-- 	suit_index = param.suit_index,
			-- }
			local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
			local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(equip_part)
			param.suit_open_num = EquipmentWGData.Instance:GetSuitActiveSameEquipNum(equip_body_seq, equip_type)     --获取套装开启数量
			param.self_suit_open = EquipmentWGData.Instance:GetEquipSuitOrderFlag(equip_body_seq, equip_part) > 0

			param.baoshi_t = EquipmentWGData.Instance:GetStoneInfoListByIndex(equip_body_index) or {} --根据装备部位获取宝石数据
			param.lingyu_t = EquipmentLingYuWGData.Instance:GetLingYuInfoListByIndex(equip_body_index) or {} --根据装备部位获取灵玉数据
			-- param.baptize_list = EquipmentWGData.Instance:GetEquipBaptizeAttrAdditionList(equip_index) --获取精炼属性

			local refine_level = EquipmentWGData.Instance:GetStoneRefineLevelByPart(equip_body_index) --宝石完善程度
			param.stone_baptize_level = refine_level
		end
	end
end

function ItemTip:FlushQuickBuyPanel()
	if self.from_view == ItemTip.FROME_MARKET_GOUMAI then
		return
	end

	-- 市场购买or上架的Tips不显示商城的购买按钮
	if self.from_view == ItemTip.FROM_MARKET_SHANGJIA or self.from_view == ItemTip.SHOP_BUY then
		self.base_tips:SetBuyPanelHide()
		self.base_tips:RemoveBtnsClick({btn_name = Language.F2Tip.Buy})
		self:ShowMoneyBar(false)
		return
	end

	local buy_list = __TableCopy(TipWGData.Instance:GetShopBuyList(self.data.item_id))
	if IsEmptyTable(buy_list) then
		if not self.data.special_buy_info then
			self.base_tips:SetBuyPanelHide()
			self.base_tips:RemoveBtnsClick({btn_name = Language.F2Tip.Buy})
			self:ShowMoneyBar(false)
		end
		return
	end

	if self.need_hide_buy_btn then
		self.base_tips:SetBuyPanelHide()
		self.base_tips:RemoveBtnsClick({btn_name = Language.F2Tip.Buy})
		self:ShowMoneyBar(false)
		return
	end
	
	if self.data.special_buy_info and self.data.special_buy_info.price_type then
		for k,v in pairs(buy_list) do
			if v.price_type == self.data.special_buy_info.price_type then
				v.price = self.data.special_buy_info.price
				if self.data.special_buy_info.discount_str and self.data.special_buy_info.discount_str ~= "" then
					v.discount_str = self.data.special_buy_info.discount_str
					v.old_price = self.data.special_buy_info.old_price
				end
			end
		end
	end

	local toggle_name_list = {}
	local price_type_list = {}
	for i,data in ipairs(buy_list) do
		toggle_name_list[i] = Language.Shop.MoneyTypeName[data.price_type]
		price_type_list[i] = data.price_type
	end

	if self.data.special_buy_info and self.data.special_buy_info.not_toggle_name then
		toggle_name_list = {}
	end

	local buy_count = TipWGData.Instance:GetDefShowBuyCount()
	local buy_info = {buy_count = buy_count or 1, price = 0, price_type = 0, max_buy_count = 0}
	buy_info.shop_cfg_one = buy_list[1]

	local change_price_type_func = function (index)
		if buy_info.toggle_select == index then
			return
		end
		local max_buy_count = 999
		if self.data.special_buy_info and self.data.special_buy_info.max_buy_count then
			max_buy_count = self.data.special_buy_info.max_buy_count
		else
			max_buy_count = ShopWGData.Instance:GetMaxBuyNum(buy_list[index].seq)
		end
		buy_info.toggle_select = index
		buy_info.data = buy_list[index]
		buy_info.price = buy_list[index].price
		buy_info.price_type = buy_list[index].price_type
		buy_info.buy_count = math.min(buy_info.buy_count, max_buy_count)
		buy_info.max_buy_count = max_buy_count
		buy_info.item_id = self.data.item_id

		self.base_tips:SetBuyPanel(buy_info)

		-- 限购显示(策划需求去掉物品框上的限购显示)
		-- local limit_str_list = ShopWGData.Instance:ExplainComposeStr(buy_list[index].seq)
		-- if self.data.special_buy_info and self.data.special_buy_info.show_num  then
		-- 	if self.data.num and self.data.num > 1 then
		-- 		self.item_cell:SetRightBottomTextVisible(true)
		-- 	else
		-- 		self.item_cell:SetRightBottomTextVisible(false)
		-- 	end
		-- elseif limit_str_list[1] then
		-- 	local str = limit_str_list[1].str
		-- 	str = string.gsub(str, "<.->", "")
		-- 	str = string.gsub(str, Language.Shop.LimitBuyStr, "")
		-- 	self.item_cell:SetRightBottomText(str)
		-- 	self.item_cell:SetRightBottomTextVisible(true)
		-- else
		-- 	self.item_cell:SetRightBottomTextVisible(false)
		-- end
	end

	-- if #buy_list > 1 then
		buy_info.toggle_select = -1
		buy_info.toggle_name = toggle_name_list
		buy_info.toggle_click = change_price_type_func
		buy_info.shop_cfg_two = buy_list[2]
	-- end

	change_price_type_func(1)

	local btn_info = {btn_name = Language.F2Tip.Buy, btn_click = BindTool.Bind(self.OnClickBuy, self)}
	self.base_tips:AddBtnsClick(btn_info)

	self:ShowMoneyBar(true, price_type_list)
end

function ItemTip:FlushGetWayView()
	self.get_way_view_is_on = false
	self.base_tips:SetItemId(self.data.item_id)
	-- 市场购买or上架的Tips不显示商城的购买按钮
	if self.from_view == ItemTip.FROME_MARKET_GOUMAI or self.from_view == ItemTip.FROM_MARKET_SHANGJIA or self.from_view == ItemTip.SHOP_BUY then
		return
	end

	local data_list = TipWGData.Instance:GetGetWayList(self.data.item_id)
	self.data_list = data_list

	local path_list = {}
	for i,data in ipairs(data_list) do
		if data.ShowType == ItemTipGoods.ShowType.Type2 then
            local temp = {label = data.cfg.open_name, icon = data.cfg.icon, btn_click = BindTool.Bind(self.OnClickGoToOpenPanel, self, i), special_bg = data.special_bg}
			path_list[#path_list + 1] = temp
		end
	end

	if #path_list > 0 then
		local is_on = true--self.from_view ~= ItemTip.FROM_BAG and self.from_view ~= ItemTip.FROM_LONGHUN_BAG and self.from_view ~= ItemTip.FROM_LONGHUN_EQUIP
		self.base_tips:SetGetWayPanel({path_list = path_list, is_on = is_on})
		self.get_way_view_is_on = is_on
	end
end

--神机暗器 星级属性
function ItemTip:SetAttrShenJiStar()
	local data = self.data
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	local info_list = {}
	local attr_list = {}

	local cfg = HiddenWeaponWGData.Instance:GetOnlyEquipCfgById(item_cfg.id)
    local equip = data.equip or {}
	local star = equip.base_star or cfg.base_star
	local color = equip.base_color or cfg.base_color
	local special_flag = equip.special_flag or cfg.special_flag
	cfg = HiddenWeaponWGData.Instance:GetEquipStarAttr(cfg.big_type, color, star, special_flag)
	local star_attr_list = HiddenWeaponWGData.Instance:GetColorAttrIndexByTip(cfg.big_type)
	local rand_attr_list = data.protocol_equip_item and data.protocol_equip_item.rand_attr_list or {}

	local count = 0
	local str, add_value, per, val, base_score, comp_score, is_wan, score_cfg, add_str, last_per_str
	for i, v in ipairs(star_attr_list) do
		local temp = {}
		if cfg[v.attr] > 0 then
			count = count + 1
			str = EquipmentWGData.Instance:GetAttrNameByAttrStr(v.attr)
			is_wan = AttributeMgr.AttrIsWan(v.attr) or v.attr == "base_attr_per"
			val = is_wan and cfg[v.attr] / 100 .. "%" or cfg[v.attr]
			str = string.format(Language.Tip.HiddenAttr1, str, val)
			per = rand_attr_list[count] or 0
			if per > 0 then
				if is_wan then
					add_value = string.format("%.1f", (per * cfg[v.attr] / 1000000))  .. "%"
				else
					add_value = math.ceil(cfg[v.attr] * per / 10000)
				end
				add_str = string.format(Language.Tip.HiddenStarAttr3, add_value)
				local last_per = ""
				if per % 100 == 0 then
					last_per = string.format("%.0f", (per / 100))
				else
					last_per = string.format("%.1f", (per / 100))
				end
				last_per_str = string.format(Language.Tip.HiddenStarAttr4, last_per)
			else
				add_str = ""
				last_per_str = ""
			end

			temp.attr_text = str
			temp.state_text = add_str or ""
			temp.last_text = last_per_str or ""
			local bundle,asset = ResPath.GetCommonImages("a2_ty_xx_x")
			temp.icon_bundle = bundle
			temp.icon_asset = asset
			table.insert(attr_list,temp)
			score_cfg = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr)
			base_score = (base_score or 0) + score_cfg * cfg[v.attr]
			comp_score = (comp_score or 0) + score_cfg * (cfg[v.attr] + per)
		end
	end
	self.common_pingfen_num = self.common_pingfen_num + base_score
	self.comp_pingfen_num = self.comp_pingfen_num + comp_score
	if rand_attr_list[6] then
		local base_attr = HiddenWeaponWGData.Instance:GetShowEquipBaseAttr(data)
		local pro = (rand_attr_list[6] * cfg["base_attr_per"]) / 100000000
		local base_attr_score = TipWGData.Instance:GetCommonPingFenByAttrList(base_attr)
		self.comp_pingfen_num = self.comp_pingfen_num + base_attr_score * pro
	end
	if data.is_virtual then
		info_list.title = Language.Tip.StarAttr .. Language.Tip.HiddenVirtual1
	else
		info_list.title = Language.Tip.StarAttr
	end
	info_list.attr_list = attr_list
	self.base_tips:SetHiddenStarAttribute(info_list)
end

--神机暗器 专属技能
function ItemTip:SetAttrShenJiSkill(item_cfg)
	local data = self.data
	local cfg = HiddenWeaponWGData.Instance:GetOnlyEquipCfgById(item_cfg.id)
	local awake_cfg = HiddenWeaponWGData.Instance:GetAwakenCfgData(cfg.big_type)
	local special_effect_level = data.protocol_equip_item and data.protocol_equip_item.special_effect_level or 0
	local desc_awake_cfg = awake_cfg[1]
	awake_cfg = awake_cfg[special_effect_level + 1]
	self.comp_pingfen_num = self.comp_pingfen_num + TipWGData.Instance:GetCommonPingFenByAttrList(awake_cfg)

	local next_need_order = 0
	local next_skill_desc = ""
	local next_desc_tilte = ""

	local skill_name = ""
	local cur_skill_desc = ""
	if awake_cfg then
		local awake = special_effect_level == 0 and "\n" ..  Language.Tip.NoAwake or ""
		skill_name = awake_cfg.effect_name .. awake
		cur_skill_desc = desc_awake_cfg.skill_des
	end
	
	local title = Language.F2Tip.ShenJiZhuanShu[cfg.big_type]
	local info = {skill_name = skill_name,
					title_label = title,
					skill_icon_id = awake_cfg.skill_icon,
					cur_skill_desc = cur_skill_desc,
					have_next = false,
					next_desc_tilte = "",
					next_skill_desc = "",
					show_special_kuang = false}

	self.base_tips:SetEquipSkill(info)
end

--物品技能(1主动技能 2专属技能 3被动技能)
function ItemTip:SetAttrItemSkill(item_cfg)
	local info_list = {}
	if HiddenWeaponWGData.IsHiddenWeapon(item_cfg.sub_type) then
		info_list = HiddenWeaponWGData.Instance:GetShenJiSkillData(self.data)
	end

	if not IsEmptyTable(info_list) then
		for i = 1, 3 do
			if not IsEmptyTable(info_list["skill_list_" .. i]) then
				self.base_tips:SetItemSkill(info_list)
				break
			end
		end
	end
end

function ItemTip:ShowAttrNewYinJi(item_cfg, equip_data, equip_index)

	if not (self.from_view == ItemTip.FROM_BAG_EQUIP) and
	not (self.from_view == ItemTip.FROM_NEW_YINJI) and
	not (self.from_view == ItemTip.FROME_BROWSE_ROLE) and
	not (self.from_view == ItemTip.FROM_EQUIPMENT) and
	not (self.from_view == ItemTip.FROM_NEW_YINJI_CHUANWEN) then
		return
	end

	local yinji_info = nil
	if self.from_view == ItemTip.FROME_BROWSE_ROLE or self.from_view == ItemTip.FROM_NEW_YINJI_CHUANWEN then
		yinji_info = self.data.zbyj_info
	else
		yinji_info = NewYinJiJiChengWGData.Instance:GetYinJiInfoByEquipPart(equip_index)
	end

	if not yinji_info or yinji_info.has_act_yinji_type <= 0 then
		return
	end

	local yinji_attrs_list = NewYinJiJiChengWGData.Instance:GetActivedYinJiAttrByData(equip_data, yinji_info.has_act_yinji_type)
	if IsEmptyTable(yinji_attrs_list) then
		return
	end

	local pingfen_num = AttributeMgr.GetCapability(yinji_attrs_list)
	self.common_pingfen_num = self.common_pingfen_num + pingfen_num
	self.comp_pingfen_num = self.comp_pingfen_num + pingfen_num

	local attr_name_list = Language.Common.TipsAttrNameList
	local sort_list = AttributeMgr.SortAttribute()

	local title_str = Language.NewEquipYinJi.YinJiQuality[yinji_info.has_act_yinji_type] or ""
	title_str = string.format(Language.NewEquipYinJi.YinJiTipsDesc, item_cfg.order, title_str)

	local add_per_str = ""
	local add_per_list = EquipmentWGData.Instance:GetEquipShenPingAttrFromCfgByEquipIdTwo(item_cfg.id)
	if add_per_list and add_per_list["yinji_attr_per"] > 0 then
		local add_per_title = string.format(Language.NewEquipYinJi.YinJiTipsDesc3, ITEM_COLOR[yinji_info.has_act_yinji_type + 2])
		title_str = title_str .. add_per_title
		add_per_str = string.format(Language.NewEquipYinJi.YinJiTipsDesc4, (add_per_list["yinji_attr_per"] / 100) .. "%")
	end

    local info_list = {}
    for i,v in ipairs(sort_list) do
        if yinji_attrs_list[v] > 0 then
			local value = yinji_attrs_list[v]
			if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v) then
				value = (value / 100) .. "%"
			end
			value = value .. add_per_str

			local info = {attr_name = attr_name_list[v], attr_value = value}
			info.attr_value = info.attr_value --ToColorStr(info.attr_value, ITEM_COLOR[yinji_info.has_act_yinji_type + 2])
			info_list[#info_list + 1] = info
        end
	end

	self.base_tips:SetYinJiAttribute({title = title_str, info_list = info_list})
end

function ItemTip:ShowAttrKeYinFu(item_cfg, equip_data, equip_index)
	if not (self.from_view == ItemTip.FROM_BAG_EQUIP) and
	not (self.from_view == ItemTip.FROM_NEW_YINJI) and
	not (self.from_view == ItemTip.FROME_BROWSE_ROLE) and
	not (self.from_view == ItemTip.FROM_EQUIPMENT) and
	not (self.from_view == ItemTip.FROM_NEW_YINJI_CHUANWEN) then
		return
	end

	local yinji_info = nil
	if self.from_view == ItemTip.FROME_BROWSE_ROLE or self.from_view == ItemTip.FROM_NEW_YINJI_CHUANWEN then
		yinji_info = self.data.zbyj_info
	else
		yinji_info = NewYinJiJiChengWGData.Instance:GetYinJiInfoByEquipPart(equip_index)
	end

	if not yinji_info then
		return
	end

	local keyinfu_attrs_list = NewYinJiJiChengWGData.Instance:GetEquipKeYinFuTotalAttrList(yinji_info.kong_wei_info_sc)
	local pingfen_num = AttributeMgr.GetCapability(keyinfu_attrs_list)
	self.common_pingfen_num = self.common_pingfen_num + pingfen_num
	self.comp_pingfen_num = self.comp_pingfen_num + pingfen_num

	local add_per_str = ""
	local title_str = Language.NewEquipYinJi.YinJiTipsDesc2
	local add_per_list = EquipmentWGData.Instance:GetEquipShenPingAttrFromCfgByEquipIdTwo(item_cfg.id)
	if add_per_list and add_per_list["keyinfu_attr_per"] > 0 then
		local add_per_title = string.format(Language.NewEquipYinJi.YinJiTipsDesc3, "#FFFFFF")
		title_str = title_str .. add_per_title
		add_per_str = string.format(Language.NewEquipYinJi.YinJiTipsDesc4, (add_per_list["keyinfu_attr_per"] / 100) .. "%")
	end

	local attr_name_list = Language.Common.TipsAttrNameList
    local sort_list = AttributeMgr.SortAttribute()
    local info_list = {}
	for i,v in ipairs(sort_list) do
        if keyinfu_attrs_list[v] > 0 then
			local value = keyinfu_attrs_list[v]
			if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v) then
				value = value / 100 .. "%"
			end
			value = value .. add_per_str
			local info = {attr_name = attr_name_list[v], attr_value = value}
			info_list[#info_list + 1] = info
        end
    end

    if not IsEmptyTable(info_list) then
    	self.base_tips:SetYinFuAttribute({title = title_str, info_list = info_list})
    end
end

function ItemTip:OnClickBuy(index)
	local buy_info = self.base_tips:GetBuyInfo()
	local data = buy_info and buy_info.data
	if not data then
		return
	end

	local is_can_cell, str = ShopWGData.Instance:IsCanCell(data.seq, true)
	if not is_can_cell then
		if str then
			SysMsgWGCtrl.Instance:ErrorRemind(str)
		end
		return
	end

	local buy_count = self.base_tips:GetBuyCount()
	local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(data.seq)
	local price = buy_info and buy_info.price or shop_cfg.price
	local all_price = buy_count * price

	--你策划要求仙玉>=100就弹多一个二次确认框
	if all_price >= 100 and shop_cfg.price_type == Shop_Money_Type.Type1 then
		local item_config = ItemWGData.Instance:GetItemConfig(data.itemid)
		if item_config then
			local name_str = ToColorStr(item_config.name, ITEM_COLOR[item_config.color])
			local num = buy_count
			if self.data.special_buy_info and self.data.special_buy_info.show_num and self.data.num then
				num = buy_count * self.data.num
			end
			TipWGCtrl.Instance:OpenAlertTips(string.format(Language.Common.CommonAlertFormat1, all_price, name_str, num), BindTool.Bind(self.RealSendBuy, self, data, buy_count))
		end
	else
		if shop_cfg.price_type == Shop_Money_Type.Type2 then
			local ok_func = function ()
				self:RealSendBuy(data, buy_count)
			end
			if not ShopWGData.Instance:CheckBangYuToBuy(data.seq, buy_count) then
				local check_string = "shop_bind_gold"
				local chect_text_str = Language.Common.DontTip2
				TipWGCtrl.Instance:OpenCheckAlertTips(Language.Guild.BindGoldNo2, ok_func,check_string,chect_text_str)
			else
				ok_func()
			end
		else
			self:RealSendBuy(data, buy_count)
		end
	end

	self:Close()
end

function ItemTip:RealSendBuy(data, buy_count)
	local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(data.seq)
	if self.data and self.data.special_buy_info and self.data.special_buy_info.price_type == shop_cfg.price_type and self.data.special_buy_call_back  then
		self.data.special_buy_call_back(buy_count)
	else
		ShopWGCtrl.Instance:SendShopBuy(data.itemid, buy_count, 0, 0, data.seq)
	end
	if shop_cfg then
		ShopWGCtrl.Instance:SendReqShopItemInfo(shop_cfg.shop_type, shop_cfg.page_type)
	end
end

function ItemTip:OnClickGoToOpenPanel(index)
	local data = self.data_list[index]
	if not data or data.ShowType ~= ItemTipGoods.ShowType.Type2 or not data.cfg or self.data == nil then
		return
	end

	local open_panel = data.cfg.open_panel
	local open_panel_table = Split(data.cfg.open_panel,"#")
	if open_panel == GuideModuleName.Compose then
		local view_index = 0
		local view_param = {open_param = 1, sub_view_name = 1}
		local compose_cfg = ComposeWGData.Instance:GetComposeCfgByProductId(self.data.item_id)
		if compose_cfg then
			view_index = compose_cfg.big_type * 10 + compose_cfg.type
			view_param = {open_param = compose_cfg.sub_type, sub_view_name = compose_cfg.child_type}
		end
		FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, view_index, view_param)
	elseif open_panel == "guaji" then  						--挂机按钮点击去挂机
		TaskWGData.Instance:GuaJiGetMonster()
	elseif open_panel == "GuildJuanXian" then
		local isnot_join_guild = RoleWGData.Instance.role_vo.guild_id == 0
		if isnot_join_guild then
			FunOpen.Instance:OpenViewByName(GuideModuleName.Guild, TabIndex.guild_guildlist)
			TipWGCtrl.Instance:ShowSystemMsg(Language.Common.CanNotEnterNoGuild)
		else
			GuildWGCtrl.Instance:OpenGuildJuanXinaView()
		end
	elseif open_panel_table[1] == "shop" then
		local tab_index = 0
		if open_panel_table[2] then
			tab_index = ShopWGData.ShowTypeToIndex[open_panel_table[2]]
		else
			tab_index = ShopWGData.Instance:GetItemSellTabIndex(data.item_id)
		end
		ShopWGCtrl.Instance:ShopJumpToItemByIDAndTabIndex(data.item_id,tab_index)
	elseif open_panel == "boss_xiezhu" then
		BossAssistWGCtrl.Instance:Open()
	else
		FunOpen.Instance:OpenViewNameByCfg(open_panel)
	end

	local close_event = TipWGCtrl.Instance:GetCloseOtherView()
	if nil ~= close_event then
		close_event()
		TipWGCtrl.Instance:CloseOtherView(nil)
	end
	self:Close()
end

function ItemTip:ShowBaGuaPai() --921165525

end

--设置隐藏掉购买按钮
function ItemTip:SetRemoveBuyPanelHide() 
	self.need_hide_buy_btn = true
end

function ItemTip:SetAttrXilian(item_cfg, equip_body_index)
	local attr_list = {}

	if self.data.frombody then
		local slot_info = EquipmentWGData.Instance:GetBaptizeSlotInfoByEquipBodyIndex(equip_body_index)

		if not IsEmptyTable(slot_info) then
			for k, v in pairs(slot_info) do
				if v.is_buy  == 1 then
					local attr_name = EquipmentWGData.Instance:GetAttrName(v.word_type)
					local is_per = EquipmentWGData.Instance:GetAttrIsPer(v.word_type)
					local per_desc = is_per and "%" or ""
					local attr_value = is_per and v.attr_value * 0.01 .. "%" or v.attr_value
					-- local min_value = is_per and v.min_value * 0.01 .. "%" or v.min_value
					-- local max_value = is_per and v.max_value * 0.01 .. "%" or v.max_value

					table.insert(attr_list, {attr_name = attr_name, attr_value = attr_value})
				end
			end
		end
	end

	if not IsEmptyTable(attr_list) then
		self.base_tips:SetXilianAttribute(attr_list)
	end
end

--解析特权积分.
function ItemTip:ParseTeQuanSuit(item_cfg, data)
	if IsEmptyTable(item_cfg) or IsEmptyTable(data) then
		return
	end

	-- 类型
	local rich_type = string.format(Language.Tip.ZhuangBeiLeiXing_1, F2_TIPS_COLOR.SOCRE,
	Language.ShowItemType[item_cfg.goods_type])
	self.base_tips:SetEquipSocre(rich_type)

	-- 使用等级
	local rich_level = string.format(Language.Tip.ShiYongDengJiNew, F2_TIPS_COLOR.SOCRE,
	RoleWGData.GetLevelString(item_cfg.limit_level))
	self.base_tips:SetSyntheticalSocre(rich_level)

	-- 物品描述  
	local desc_info = {}
	local description = item_cfg.description
	if description and description ~= "" then
		desc_info[#desc_info + 1] = {title = Language.F2Tip.WuPinMiaoShu, desc = description}
	end

	for i = 2, 6 do
		local desc = item_cfg["description" .. i]
		if desc and desc ~= "" then
			desc_info[#desc_info + 1] = {title = item_cfg["biaoti" .. i], desc = desc}
		end
	end

	self.base_tips:SetItemDesc(desc_info)

	-- 获取途径
	self:ShowItemGetDesc(item_cfg)
	local cangjin_other_cfg = CangJinShopWGData.Instance:GetOtherCfg()
	local item_list_info = {title = Language.CangJinShopView.CangJinExTipsText, data = cangjin_other_cfg}
	self.base_tips:SetShowItemListInfo(item_list_info)
end

--解析名望.
function ItemTip:ParseHornor(item_cfg, data)
	if IsEmptyTable(item_cfg) or IsEmptyTable(data) then
		return
	end

	-- 类型
	local rich_type = string.format(Language.Tip.ZhuangBeiLeiXing_1, F2_TIPS_COLOR.SOCRE,
	Language.ShowItemType[item_cfg.goods_type])
	self.base_tips:SetEquipSocre(rich_type)

	-- 使用等级
	local rich_level = string.format(Language.Tip.ShiYongDengJiNew, F2_TIPS_COLOR.SOCRE,
	RoleWGData.GetLevelString(item_cfg.limit_level))
	self.base_tips:SetSyntheticalSocre(rich_level)

	-- 物品描述  
	local desc_info = {}
	local description = item_cfg.description
	if description and description ~= "" then
		desc_info[#desc_info + 1] = {title = Language.F2Tip.WuPinMiaoShu, desc = description}
	end

	for i = 2, 6 do
		local desc = item_cfg["description" .. i]
		if desc and desc ~= "" then
			desc_info[#desc_info + 1] = {title = item_cfg["biaoti" .. i], desc = desc}
		end
	end

	self.base_tips:SetItemDesc(desc_info)

	-- 获取途径
	self:ShowItemGetDesc(item_cfg)
	local other_cfg = ShopWGData.Instance:GetOtherCfg()
	local item_list_info = {title = Language.Shop.HornorExTipsText, data = other_cfg}
	self.base_tips:SetShowItemListInfo(item_list_info)
end

-- 幻兽内丹装备
function ItemTip:ParseBeastAlchemyEquipItem(item_cfg, data)
	if IsEmptyTable(item_cfg) or (not data) then
		return
	end

	local equip_cfg = ControlBeastsCultivateWGData.Instance:GetEquipCfg(data.item_id)
	-- 类型
	local rich_type = string.format(Language.Tip.ZhuangBeiLeiXing_1, F2_TIPS_COLOR.SOCRE, equip_cfg.hole_name or "")
	self.base_tips:SetEquipSocre(rich_type)

	-- 使用等级
	local rich_level = string.format(Language.Tip.ShiYongDengJiNew, TIPS_COLOR.SOCRE, RoleWGData.GetLevelString(item_cfg.limit_level))
	self.base_tips:SetSyntheticalSocre(rich_level)

	-- 设置评分标签
	local equip_data = data.equip_info
	local desc_info = {}

	if equip_data ~= nil then
		self.base_tips:SetBeastAlchemyScoreInfo(data)
	else
		local description = item_cfg.description
		if description and description ~= "" then
			desc_info[#desc_info + 1] = {title = Language.F2Tip.WuPinMiaoShu, desc = description}
		end	
	end

	local attr_list = ItemShowWGData.Instance:OutStrAttrListAndCapalityByAttrId(equip_cfg, "attr_id", "attr_value", 1, 3)
	if not data.is_bag_equip then -- 不是背包的要计算槽位加成
		attr_list = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipAttrList(equip_data, data.fight_slot, data.equip_slot_lv)
	end

	local attr_list_str = ItemShowWGData.Instance:OutAttrListToStr(attr_list)
	if attr_list_str ~= nil and attr_list_str ~= "" then
		desc_info[#desc_info + 1] = {title = Language.F2Tip.SysAttrTitle[0], desc = attr_list_str}
	end

	self.base_tips:SetItemDesc(desc_info)

	if equip_data ~= nil then
		self.base_tips:SetBeastAlchemyAdditionalInfo(data)
	end

	-- local rich_type = ""
	-- local tujian_data = ShanHaiJingWGData.Instance:GetTJResolveShowCfg(item_cfg.id)
	-- if tujian_data ~= nil then
	-- 	local is_act = ShanHaiJingWGData.Instance:GetTJIsActBySeq(tujian_data.seq)
	-- 	local key = is_act and 1 or 2
	-- 	rich_type = Language.Tip.FashionState[key] or ""
	-- elseif item_cfg.goods_type then
	-- 	rich_type = string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE, Language.ShowItemType[item_cfg.goods_type])
	-- end
	-- self.base_tips:SetEquipSocre(rich_type)

	-- 
	-- self:ShowFlairObj()
	-- self:ShowItemGetDesc(item_cfg)
	-- self:ShowItemExpDesc(item_cfg)
	-- self:ShowBaGuaPai()
end

-- 展示天幕
function ItemTip:SetBackgroundCellLoader(item_id)
	self.node_list.background_root:SetActive(true)
	self.base_tips:SetLeftShowModelDiVisible(false)
	local asset, bundle = ResPath.BackgroundShow(item_id)

	if not self.background_loader then
		local background_loader = AllocAsyncLoader(self, "item_tip_back_cell_loader")
		background_loader:SetIsUseObjPool(true)
		background_loader:SetParent(self.node_list["background_root"].transform)
		self.background_loader = background_loader
	end
	self.background_loader:Load(asset, bundle)
end

function ItemTip:DestroyBackgroundCellLoader()
	if self.background_loader then
        self.background_loader:Destroy()
		self.background_loader = nil
	end
end

--解析抽奖礼包
function ItemTip:ParseDrawGift(item_cfg, data)
	if IsEmptyTable(item_cfg) or IsEmptyTable(data) then
		return
	end

	-- 类型
	local rich_type = string.format(Language.Tip.ZhuangBeiLeiXing_1, F2_TIPS_COLOR.SOCRE,
	Language.F2Tip.DisplayType[item_cfg.goods_type])
	self.base_tips:SetEquipSocre(rich_type)

	-- 使用等级
	local rich_level = string.format(Language.Tip.ShiYongDengJiNew, F2_TIPS_COLOR.SOCRE,
	RoleWGData.GetLevelString(item_cfg.limit_level))
	self.base_tips:SetSyntheticalSocre(rich_level)

	-- 物品描述  
	local desc_info = {}
	local description = item_cfg.description
	if description and description ~= "" then
		desc_info[#desc_info + 1] = {title = Language.F2Tip.WuPinMiaoShu, desc = description}
	end

	for i = 2, 6 do
		local desc = item_cfg["description" .. i]
		if desc and desc ~= "" then
			desc_info[#desc_info + 1] = {title = item_cfg["biaoti" .. i], desc = desc}
		end
	end

	self.base_tips:SetItemDesc(desc_info)

	-- 获取途径
	self:ShowItemGetDesc(item_cfg)
	self.base_tips:SetDeawGiftInfo(item_cfg)
end

function ItemTip:FlushDrawGiftDisplayModel(item_cfg, data)
	if IsEmptyTable(item_cfg) or IsEmptyTable(data) then
		return
	end

	local cfg = DrawGiftWGData.Instance:GetDrawGiftCfgById(item_cfg.id)
	if not cfg then
		return
	end

	local model_display, node_root = self.base_tips:GetSpeModle()
	if not model_display or not node_root then
		return
	end

	local display_data = {}
	display_data.should_ani = true
	if cfg.model_show_itemid ~= 0 and cfg.model_show_itemid ~= "" then
		local split_list = string.split(cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = cfg.model_show_itemid
		end
	end

	display_data.bundle_name = cfg["model_bundle_name"]
    display_data.asset_name = cfg["model_asset_name"]
    local model_show_type = tonumber(cfg["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1
    model_display:SetData(display_data)

    local scale = cfg["display_scale"]
    Transform.SetLocalScaleXYZ(node_root.transform, scale, scale, scale)

	local pos_x, pos_y = 0, 0
	if cfg.display_pos and cfg.display_pos ~= "" then
		local pos_list = string.split(cfg.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	RectTransform.SetAnchoredPositionXY(node_root.rect, pos_x, pos_y)

	if cfg.rotation and cfg.rotation ~= "" then
		local rotation_tab = string.split(cfg.rotation,"|")
		node_root.transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	end
end


--解析万图谱
function ItemTip:ParseShanhaijingZuhe(item_cfg, data)
	if IsEmptyTable(item_cfg) or IsEmptyTable(data) then
		return
	end

	local tujian_cfg = ShanHaiJingWGData.Instance:GetTJResolveShowCfg(self.data.item_id)

	-- 状态
	local is_act = ShanHaiJingWGData.Instance:GetTJIsActBySeq(tujian_cfg.seq)
	local key = is_act and 1 or 2
	local rich_type = Language.Tip.FashionState[key] or ""
	self.base_tips:SetEquipSocre(rich_type)

	-- 使用等级
	local rich_level = string.format(Language.Tip.ShiYongDengJiNew, F2_TIPS_COLOR.SOCRE,
	RoleWGData.GetLevelString(item_cfg.limit_level))
	self.base_tips:SetSyntheticalSocre(rich_level)

	-- 物品描述  
	local desc_info = {}
	local description = item_cfg.description
	if description and description ~= "" then
		desc_info[#desc_info + 1] = {title = Language.F2Tip.WuPinMiaoShu, desc = description}
	end

	self:ShowItemDescription(item_cfg)

	

	local zuhe_seq_list = ShanHaiJingWGData.Instance:GetZuHeSeqListBySeq(tujian_cfg.seq)
	if zuhe_seq_list then
		local zuhe_info = {}
		local name = ""
		local active_count = 0
		for k, v in pairs(zuhe_seq_list) do
			local temp_tujian_cfg = ShanHaiJingWGData.Instance:GetTJCfgBySeq(tonumber(v))
			local temp_item_cfg = ItemWGData.Instance:GetItemConfig(temp_tujian_cfg.active_item_id)
			local temp_name = "["..temp_item_cfg.name.."]"
			local temp_is_act = ShanHaiJingWGData.Instance:GetTJIsActBySeq(temp_tujian_cfg.seq)
			if temp_is_act then
				name = name .. ToColorStyleByQuality(temp_name,temp_item_cfg.color).."\n"
				active_count = active_count + 1
			else
				name = name .. ToColorStr(temp_name,COLOR3B.C12).."\n"
			end
		end
		zuhe_info.item_name = name
		zuhe_info.active_count = active_count
		zuhe_info.max_count = #zuhe_seq_list
		self.base_tips:SetShanHaiJingZuHe(zuhe_info)
	end


	-- 获取途径
	self:ShowItemGetDesc(item_cfg)

end

--------------- TipShowGiftItem tip展示礼包具体的itemcell
TipShowGiftItem = TipShowGiftItem or BaseClass(BaseRender)
function TipShowGiftItem:LoadCallBack()
	if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.cell_pos)
        self.item_cell:SetTipClickCallBack(BindTool.Bind(self.OnClickCallBack, self))
	end
end

function TipShowGiftItem:ReleaseCallBack()
	if self.item_cell then
        self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function TipShowGiftItem:OnFlush()
	local is_hide = IsEmptyTable(self.data)
	self.node_list.hide_root:CustomSetActive(not is_hide)
	if is_hide then
		return
	end

	self.item_cell:SetData(self.data)
end

function TipShowGiftItem:OnClickCallBack()
    if IsEmptyTable(self.data) then
        return true
    end

    TipWGCtrl.Instance:ItemtipsOpenOtherItemTips(self.data)
    return true
end
