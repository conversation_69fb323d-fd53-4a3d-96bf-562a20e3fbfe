CustomizedSuitSkillView = CustomizedSuitSkillView or BaseClass(SafeBaseView)

function CustomizedSuitSkillView:__init()
	self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/customized_suit_ui_prefab", "layout_customized_suit_skill")
	self:SetMaskBgAlpha(250/255)
end

function CustomizedSuitSkillView:__delete()
end

function CustomizedSuitSkillView:OpenCallBack()
end

function CustomizedSuitSkillView:CloseCallBack()
	self.suit = nil							---当前的套装id
	self.curr_select_index = nil			---当前选择的技能下标 
	self.curr_select_skill_id = nil			---当前选择的技能id
end

function CustomizedSuitSkillView:LoadCallBack()
	-- if not self.cur_choose_skill_list then
	-- 	self.cur_choose_skill_list = AsyncListView.New(CustomizedSkillRender, self.node_list.item_list_root)
	-- 	self.cur_choose_skill_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectDreamSkillHandler, self))
	-- end

	self.cur_choose_skill_list = {}
	for i = 1, 3 do
		self.cur_choose_skill_list[i] = CustomizedSkillRender.New(self.node_list["part_skill_cell" .. i])
		self.cur_choose_skill_list[i]:SetClickCallBack(BindTool.Bind(self.OnSelectDreamSkillHandler, self))
		self.cur_choose_skill_list[i]:SetIndex(i)
	end

	XUI.AddClickEventListener(self.node_list["one_key_btn"], BindTool.Bind(self.OperateSkillBtn, self))
end

function CustomizedSuitSkillView:ReleaseCallBack()
	-- if self.cur_choose_skill_list then
	-- 	self.cur_choose_skill_list:DeleteMe()
	-- 	self.cur_choose_skill_list = nil
	-- end

	if self.cur_choose_skill_list then
		for k, v in pairs(self.cur_choose_skill_list) do
			v:DeleteMe()
		end
	 end
	self.cur_choose_skill_list = {}
end

function CustomizedSuitSkillView:SetSuit(suit)
	self.suit = suit
end

function CustomizedSuitSkillView:OnSelectDreamSkillHandler(item)
	if nil == item or nil == item.data then
		return
	end

	if self.curr_select_index == item.index then
		return
	end

	for i, v in ipairs(self.cur_choose_skill_list) do
		v:OnSelectChange(item.index == v.index)
	end

	self.curr_select_index = item.index
	self.curr_select_skill_id = item.data.skill_id
	self.node_list.skill_desc.text.text = item.data.skill_desc
	self:RefreshChooseBtnStatus()
end

function CustomizedSuitSkillView:OnFlush(param_t, index)
	if self.suit == nil then
		return
	end

	local cur_data = CustomizedSuitWGData.Instance:GetThemeSkillBySuit(self.suit)

	if not cur_data then
		return
	end

	self:RefreshChooseRoot(cur_data)
	self.node_list.one_key_btn_red:CustomSetActive(CustomizedSuitWGData.Instance:GetOneThemeSelectSkillRed(self.suit))
end

---刷新选择技能相关界面
function CustomizedSuitSkillView:RefreshChooseRoot(cur_data)
	local select_skill_data, curr_select_index = CustomizedSuitWGData.Instance:GetSelectSkillListBySuit(cur_data, self.suit)
	if select_skill_data and self.cur_choose_skill_list then
		for i = 1, 3 do
			self.cur_choose_skill_list[i]:SetData(select_skill_data[i])
		end

		if self.curr_select_index == nil and self.cur_choose_skill_list[curr_select_index] then
			self.cur_choose_skill_list[curr_select_index]:OnClick()
			--self.cur_choose_skill_list:JumpToIndex(curr_select_index, 4)
		end
	end

	--刷新一下文字
	local act, mum, max_num = CustomizedSuitWGData.Instance:IsCustomizedSkillAct(self.suit)
	local color = mum >= max_num and COLOR3B.D_GREEN or COLOR3B.D_RED
	local color_str = ToColorStr(string.format("%d/%d", mum, max_num), color)
	self.node_list.choose_tips.text.text = string.format(Language.Customized.SkillTips, color_str)
end

-- 刷新按钮状态
function CustomizedSuitSkillView:RefreshChooseBtnStatus()
	local cur_data = CustomizedSuitWGData.Instance:GetThemeSkillBySuit(self.suit)

	if not cur_data then
		return
	end

	local is_select_ed = self.curr_select_skill_id == cur_data.skill_id
	local key_btn_str = is_select_ed and Language.Customized.selected_skill_lable or Language.Customized.select_skill_lable
	self.node_list.one_key_btn_text.text.text = key_btn_str
	XUI.SetGraphicGrey(self.node_list["one_key_btn"], is_select_ed)
end

---点击
function CustomizedSuitSkillView:OperateSkillBtn()
	local act, mum, max_num = CustomizedSuitWGData.Instance:IsCustomizedSkillAct(self.suit)
	if mum < max_num then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Customized.NotAtive)
		return
	end

	local cur_data = CustomizedSuitWGData.Instance:GetThemeSkillBySuit(self.suit)
	if not cur_data then
		return
	end

	if self.curr_select_skill_id == cur_data.skill_id then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Customized.CanNotSelect)
		return
	end

	-- 当当前技能不为0的时候先发一个重置
	if cur_data.skill_id ~= 0 then
		CustomizedSuitWGCtrl.Instance:SendWardrobeTypeReset(self.suit, 0, 0)
	end

	CustomizedSuitWGCtrl.Instance:SendWardrobeTypeChoose(self.suit, self.curr_select_skill_id, 0)
	self:Close()
end

-------------------------------------技能列表item---------------------------
CustomizedSkillRender = CustomizedSkillRender or BaseClass(BaseRender)
function CustomizedSkillRender:__init()
end

function CustomizedSkillRender:OnFlush()
	if self.data then
		self.node_list.name.text.text = self.data.skill_name
		self.node_list.desc.text.text = self.data.skill_desc2

		-- local bundle, asset = ResPath.GetCustomizedImg("a3_ystz_jnzb" .. self.data.suit)
		--self.node_list.cell_bg.image:LoadSprite(bundle, asset)

		if self.node_list.wear_flag then
			self.node_list.wear_flag:CustomSetActive(self.data.skill_id == self.data.cur_select_skill_id)
		end

		if self.data.skill_icon then
			self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(self.data.skill_icon))
		end


		local suit_data = CustomizedSuitWGData.Instance:GetCustomizedSuitInfoBySuit(self.data.suit)
		if IsEmptyTable(suit_data) then
			return
		end
		local str = string.format("a3_suit_skill_icon_%s_%s", suit_data.theme_res_id, self.index)
		local bundle, asset = ResPath.GetRawImagesPNG(str)
		self.node_list.tz_img.raw_image:LoadSprite(bundle, asset, function()
			self.node_list.tz_img.raw_image:SetNativeSize()
		end)
	end
end

function CustomizedSkillRender:OnSelectChange(is_select)
	--self.node_list.normal_bg:SetActive(not is_select)
	self.node_list.select_bg:SetActive(is_select)
end