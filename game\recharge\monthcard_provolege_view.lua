MonthCardProvolegeView = MonthCardProvolegeView or BaseClass(SafeBaseView)

local ProvolegeType = {
	MonthCard = 1, 
	Vip = 2,
}
local BtnState = {
	CanBuy = 1,		--可购买
	CanReward = 2,	--可领取
	Rewarded = 3,	--已全部领取
	TomorrowReward = 4, --明日可领取
}

function MonthCardProvolegeView:__init()
	self.view_style = ViewStyle.Full
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(true,true)
	self.mc_btn_state = BtnState.CanBuy
	self.vip_btn_state = BtnState.CanBuy
	self.mc_cur_day = 0
	self.vip_cur_day = 0

	self:AddViewResource(0, "uis/view/recharge_ui_prefab", "layout_month_card")
end

function MonthCardProvolegeView:__delete()

end

function MonthCardProvolegeView:LoadCallBack()
	self.node_list.mc_free_mop_up.button:AddClickListener(BindTool.Bind(self.OnClickFreeMopUp, self, ProvolegeType.MonthCard))
	self.node_list.vip_free_mop_up.button:AddClickListener(BindTool.Bind(self.OnClickFreeMopUp, self, ProvolegeType.Vip))
	self.node_list.mc_free_merge.button:AddClickListener(BindTool.Bind(self.OnClickFreeMerge, self, ProvolegeType.MonthCard))
	self.node_list.vip_free_merge.button:AddClickListener(BindTool.Bind(self.OnClickFreeMerge, self, ProvolegeType.Vip))

	self.node_list.mc_tips_btn.button:AddClickListener(BindTool.Bind2(self.OnClickTipBtn, self, TabIndex.recharge_monthcard))
	self.node_list.vip_tips_btn.button:AddClickListener(BindTool.Bind2(self.OnClickTipBtn, self, TabIndex.recharge_storehouse))

	self.node_list.mc_buy_btn.button:AddClickListener(BindTool.Bind(self.OnClickBuyBtn, self, ProvolegeType.MonthCard))
	self.node_list.vip_buy_btn.button:AddClickListener(BindTool.Bind(self.OnClickBuyBtn, self, ProvolegeType.Vip))

	self.node_list.close_btn.button:AddClickListener(BindTool.Bind(self.Close, self))

	local touzijihua_cfg = ConfigManager.Instance:GetAutoConfig("touzijihua_auto")
	self.node_list.mc_ewai_lbl.text.text = touzijihua_cfg and touzijihua_cfg.other[1].extra_gold_show or ""

	local month_card_cfg = RechargeWGData.Instance:GetTZCardCfg(INVEST_CARD_TYPE.MonthCard)
	self.node_list.mc_fanli_lbl.text.text = month_card_cfg.return_show
	self.node_list.mc_lifan_lbl.text.text = month_card_cfg.return_gold
	self.node_list.mc_buy_cost_label.text.text =  string.format(Language.Recharge.MonthCardBuy, month_card_cfg.need_rmb)

	local storehouse_cfg = RechargeWGData.Instance:GetTZCardCfg(INVEST_CARD_TYPE.StorehouseCard)
	self.node_list.vip_fanli_lbl.text.text = storehouse_cfg.return_show
	self.node_list.vip_lifan_lbl.text.text = storehouse_cfg.return_gold

	self.node_list.vip_tip_lbl.text.text = Language.Vip.VipTouZiTip

	self.mc_rmb_type = month_card_cfg.rmb_type
	self.vip_rmb_type = storehouse_cfg.rmb_type
	self.invest_need_vip_level = storehouse_cfg.invest_need_vip_level
	if storehouse_cfg.invest_need_vip_level > 0 then
		self.node_list.vip_buy_cost_label.text.fontSize = 22
		self.node_list.vip_buy_cost_label.text.text = string.format(Language.Vip.MonthCardBuyBtnLbl, storehouse_cfg.invest_need_vip_level, storehouse_cfg.need_rmb)
	else
		self.node_list.vip_buy_cost_label.text.fontSize = 30
		self.node_list.vip_buy_cost_label.text.text =  string.format(Language.Recharge.MonthCardBuy, storehouse_cfg.need_rmb)
	end

	self.mc_rmb = month_card_cfg.need_rmb
	self.vip_rmb = storehouse_cfg.need_rmb

	self.vip_reward_list = AsyncListView.New(ItemCell, self.node_list.vip_reward_list)
	self.vip_reward_list:SetStartZeroIndex(true)
end

function MonthCardProvolegeView:ReleaseCallBack()
	if self.vip_reward_list then
		self.vip_reward_list:DeleteMe()
		self.vip_reward_list = nil
	end
end

function MonthCardProvolegeView:OnFlush()
	local mc_can_active = RechargeWGData.Instance:CanActiveTZCard(INVEST_CARD_TYPE.MonthCard)
	-- print_error("mc_can_active", mc_can_active)
	self.node_list.mc_buy_cost_label:SetActive(mc_can_active)
	-- XUI.SetGraphicGrey(self.node_list.mc_buy_btn, not mc_can_active)
	self.node_list.mc_reward_label:SetActive(not mc_can_active)

	local is_show_mc_eff = false
	local mc_max_day = RechargeWGData.Instance:GetTZCardMaxDay(INVEST_CARD_TYPE.MonthCard)
	-- print_error("mc_max_day =", mc_max_day)
	local mc_card_cfg_list = RechargeWGData.Instance:GetTZCardRewardCfg(INVEST_CARD_TYPE.MonthCard)
	if not mc_can_active then
		local card_info = RechargeWGData.Instance:GetInvestCardInfo(INVEST_CARD_TYPE.MonthCard)
		local cur_day = card_info.cur_day + 1
		cur_day = cur_day <= mc_max_day and cur_day or mc_max_day
		self.mc_cur_day = cur_day

		local cur_day_cfg = mc_card_cfg_list[cur_day]
		-- print_error("card_max_day1, cur_day1 =", mc_max_day, cur_day, card_info, cur_day_cfg, mc_card_cfg_list[cur_day + 1].reward_gold_bind)
		local reward_flag = card_info.fetch_reward_flag[cur_day - 1]
		XUI.SetGraphicGrey(self.node_list.mc_buy_btn, reward_flag ~= 0)
		self.node_list.mc_buy_redmind:SetActive(reward_flag == 0)
		local has_reward, _ = RechargeWGData.Instance:CanGetInvestCardReward(INVEST_CARD_TYPE.MonthCard)
		self.node_list.mc_tips_redmind:SetActive(has_reward)
		self.node_list.mc_tip_lbl1.text.text = string.format(Language.Vip.MonthCardTip2_1, mc_card_cfg_list[self.mc_cur_day].reward_gold_bind)
		self.node_list.mc_tip_lbl2.text.text = string.format(Language.Vip.MonthCardTip2_2, cur_day, mc_max_day)
		is_show_mc_eff = reward_flag == 0
		self.node_list.mc_reward_label.text.text = reward_flag == 0 and "可领取" or ((cur_day + 1) <= mc_max_day and Language.Vip.TomorrowRewardTip or "已领取")
		self.mc_btn_state = reward_flag == 0 and BtnState.CanReward or ((cur_day + 1) <= mc_max_day and BtnState.TomorrowReward or BtnState.Rewarded)
	else
		is_show_mc_eff = true
		self.mc_cur_day = 1
		self.mc_btn_state = BtnState.CanBuy
		XUI.SetGraphicGrey(self.node_list.mc_buy_btn, false)
		self.node_list.mc_reward_label.text.text = string.format(Language.Recharge.MonthCardBuy, self.mc_rmb)
		self.node_list.mc_buy_redmind:SetActive(false)
		self.node_list.mc_tips_redmind:SetActive(false)
		self.node_list.mc_tip_lbl1.text.text = string.format(Language.Vip.MonthCardTip1_1, mc_card_cfg_list[self.mc_cur_day].reward_gold_bind)
		self.node_list.mc_tip_lbl2.text.text = string.format(Language.Vip.MonthCardTip1_2, mc_max_day)
	end
	self.node_list.mc_buy_effect:SetActive(is_show_mc_eff)


	local vip_can_active = RechargeWGData.Instance:CanActiveTZCard(INVEST_CARD_TYPE.StorehouseCard)
	-- print_error("vip_can_active", vip_can_active)
	self.node_list.vip_buy_cost_label:SetActive(vip_can_active)
	-- XUI.SetGraphicGrey(self.node_list.vip_buy_btn, not vip_can_active)
	self.node_list.vip_reward_label:SetActive(not vip_can_active)
	local is_show_vip_eff = false
	local vip_max_day = RechargeWGData.Instance:GetTZCardMaxDay(INVEST_CARD_TYPE.StorehouseCard)
	local vip_card_cfg_list = RechargeWGData.Instance:GetTZCardRewardCfg(INVEST_CARD_TYPE.StorehouseCard)
	if not vip_can_active then
		local card_info = RechargeWGData.Instance:GetInvestCardInfo(INVEST_CARD_TYPE.StorehouseCard)
		local cur_day = card_info.cur_day + 1
		local cur_day_cfg = vip_card_cfg_list[cur_day]
		cur_day = cur_day <= vip_max_day and cur_day or vip_max_day
		self.vip_cur_day = cur_day

		local reward_flag = card_info.fetch_reward_flag[cur_day - 1]
		XUI.SetGraphicGrey(self.node_list.vip_buy_btn, reward_flag ~= 0)
		self.node_list.vip_buy_redmind:SetActive(reward_flag == 0)

		local has_reward, _ = RechargeWGData.Instance:CanGetInvestCardReward(INVEST_CARD_TYPE.StorehouseCard)
		self.node_list.vip_tips_redmind:SetActive(has_reward)
		self.node_list.vip_time_lbl.text.text = string.format(Language.Vip.VipTouZiDay2, self.vip_cur_day, vip_max_day)
		is_show_vip_eff = reward_flag == 0

		local reward_list_cfg = vip_card_cfg_list[self.vip_cur_day].reward_item
		self.node_list.vip_tip_lbl:SetActive(false)
		self.node_list.vip_tip_lbl1:SetActive(true)
		local vip_invest_card_bindgold = RechargeWGData.Instance:GetTZJHOtherCfg("vip_invest_card_bindgold")
		self.node_list.vip_tip_lbl1.text.text = string.format(Language.Vip.VipTouZiTip2_1, vip_invest_card_bindgold or 0)

		self.node_list.vip_reward_label.text.text = reward_flag == 0 and "可领取" or (((cur_day + 1) <= vip_max_day) and Language.Vip.TomorrowRewardTip or "已领取")
		self.vip_btn_state = reward_flag == 0 and BtnState.CanReward or (((cur_day + 1) <= vip_max_day) and BtnState.TomorrowReward or BtnState.Rewarded)
	else
		is_show_vip_eff = true
		self.vip_cur_day = 1
		self.vip_btn_state = BtnState.CanBuy
		XUI.SetGraphicGrey(self.node_list.vip_buy_btn, false)
		self.node_list.vip_reward_label.text.text = string.format(Language.Recharge.MonthCardBuy, self.vip_rmb)
		self.node_list.vip_buy_redmind:SetActive(false)
		self.node_list.vip_tips_redmind:SetActive(false)
		self.node_list.vip_time_lbl.text.text = string.format(Language.Vip.VipTouZiDay1, vip_max_day)
		self.node_list.vip_tip_lbl:SetActive(true)
		self.node_list.vip_tip_lbl1:SetActive(false)
	end
	self.node_list.vip_buy_effect:SetActive(is_show_vip_eff)
	
	local reward_list_cfg = vip_card_cfg_list[self.vip_cur_day].reward_item
	self.vip_reward_list:SetDataList(reward_list_cfg)
end

function MonthCardProvolegeView:OnClickTipBtn(tab_index)
	RechargeWGCtrl.Instance:Open(tab_index)
end

function MonthCardProvolegeView:OnClickFreeMopUp(provolege_type)
	local title = provolege_type == ProvolegeType.MonthCard and Language.Recharge.MonthCardProvolegeMcMopUpTitle or Language.Recharge.MonthCardProvolegeVipMopUpTitle
	local content = provolege_type == ProvolegeType.MonthCard and Language.Recharge.MonthCardProvolegeMcMopUpContent or Language.Recharge.MonthCardProvolegeVipMopUpContent
	RuleTip.Instance:SetContent(content, title)
end

function MonthCardProvolegeView:OnClickFreeMerge(provolege_type)
	local title = provolege_type == ProvolegeType.MonthCard and Language.Recharge.MonthCardProvolegeMcMergeTitle or Language.Recharge.MonthCardProvolegeVipMergeTitle
	local content = provolege_type == ProvolegeType.MonthCard and Language.Recharge.MonthCardProvolegeMcMergeContent or Language.Recharge.MonthCardProvolegeVipMergeContent
	RuleTip.Instance:SetContent(content, title)
end

function MonthCardProvolegeView:OnClickBuyBtn(provolege_type)
	local btn_state = provolege_type == ProvolegeType.MonthCard and self.mc_btn_state or self.vip_btn_state
	-- print_error("btn_state, provolege_type =", btn_state, provolege_type)
	if btn_state == BtnState.Rewarded then
		TipsSystemManager.Instance:ShowSystemTips(Language.Vip.TodayRewarded)
	elseif btn_state == BtnState.TomorrowReward then
		TipsSystemManager.Instance:ShowSystemTips(Language.Vip.TomorrowRewardTip)
	elseif btn_state == BtnState.CanBuy then
		if provolege_type == ProvolegeType.MonthCard then
			RechargeWGCtrl.Instance:Recharge(self.mc_rmb, self.mc_rmb_type, INVEST_CARD_TYPE.MonthCard)
		elseif RoleWGData.Instance:GetRoleVo().vip_level >= self.invest_need_vip_level then
			RechargeWGCtrl.Instance:Recharge(self.vip_rmb, self.vip_rmb_type, INVEST_CARD_TYPE.StorehouseCard)
		end
	else
		local card_type = provolege_type == ProvolegeType.MonthCard and INVEST_CARD_TYPE.MonthCard or INVEST_CARD_TYPE.StorehouseCard
		local cur_day = provolege_type == ProvolegeType.MonthCard and self.mc_cur_day or self.vip_cur_day
		-- print_error("card_type, cur_day =", card_type, cur_day)
		if cur_day > 0 then
			RechargeWGCtrl.Instance:SendFetchInvestCardReward(card_type, cur_day - 1)
		end
	end
end