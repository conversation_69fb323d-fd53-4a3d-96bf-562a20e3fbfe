require("game/sixiang_call/sixiang_call_enum")
require("game/sixiang_call/sixiang_call_wg_data")
require("game/sixiang_call/sixiang_summon_item")
require("game/sixiang_call/sixiang_call_view")
require("game/sixiang_call/sixiang_call_record")
require("game/sixiang_call/sixiang_summon_view")
-- require("game/sixiang_call/sixiang_activate_view")
require("game/sixiang_call/sixiang_exchange_view")
require("game/sixiang_call/sixiang_reward_view")
require("game/sixiang_call/sixiang_reward_pond_view")
require("game/sixiang_call/xianqi_zhenlian_view")
require("game/sixiang_call/xianqi_zhenlian_item")
require("game/sixiang_call/xianqi_reward_view")
require("game/sixiang_call/shenji_tianci_record")

SiXiangCallWGCtrl = SiXiangCallWGCtrl or BaseClass(BaseWGCtrl)

function SiXiangCallWGCtrl:__init()
	if SiXiangCallWGCtrl.Instance then
        error("[SiXiangCallWGCtrl]:Attempt to create singleton twice!")
	end
	SiXiangCallWGCtrl.Instance = self

    self.data = SiXiangCallWGData.New()
    self.view = SiXiangCallView.New(GuideModuleName.SiXiangCallView)
	self.xianqi_zhenlian_view = ShenJiTianCiView.New(GuideModuleName.ShenJiTianCiView)
    -- self.sixiang_activate_view = SiXiangActivateView.New(GuideModuleName.SiXiangYuGaoView)
    self.sixiang_record_view = SiXiangCallRecordView.New(GuideModuleName.SiXiangCallRecord)
    self.sixiang_exchange_view = SiXiangExchangeView.New(GuideModuleName.SiXiangExchange)
    self.sixiang_reward_view = SiXiangRewardView.New(GuideModuleName.SiXiangReward)
    self.xianqi_reward_view = XianQiRewardView.New(GuideModuleName.XianQiReward)
    self.sixiang_reward_pond_view = SiXiangRewardPondView.New(GuideModuleName.SiXiangRewardPond)
    self.xianqi_record_view = ShenJiTianCiRecordView.New()

    self:RegisterAllProtocals()
    self:BindGlobalEvent(OtherEventType.PASS_DAY2, BindTool.Bind1(self.DayChange, self))
end

function SiXiangCallWGCtrl:__delete()
    SiXiangCallWGCtrl.Instance = nil

    self.data:DeleteMe()
    self.data = nil

    self.view:DeleteMe()
    self.view = nil

	self.xianqi_zhenlian_view:DeleteMe()
	self.xianqi_zhenlian_view = nil

    self.sixiang_record_view:DeleteMe()
    self.sixiang_record_view = nil

	self.xianqi_record_view:DeleteMe()
	self.xianqi_record_view = nil

	if self.shake_delay then
		GlobalTimerQuest:CancelQuest(self.shake_delay)
		self.shake_delay = nil
	end
end

function SiXiangCallWGCtrl:RegisterAllProtocals()
	-- self:RegisterProtocol(SCSiXiangYuGao, "OnSCSiXiangYuGao")
    self:RegisterProtocol(SCYuanShenZhaoHuanInfo, "OnSCSiXiangSummonInfo")
    self:RegisterProtocol(SCYuanShenZhaoHuanDrawReward, "OnSiXiangSummonReward")
    self:RegisterProtocol(SCYuanShenZhaoHuanNewDrawLog, "OnSiXiangSummonNewDrawLog")
    self:RegisterProtocol(SCYuanShenZhaoHuanConvertShopMoneyChange, "OnSiXiangSummonShopMoneyChange")

    --仙器真炼(F1 神机百炼)
    self:RegisterProtocol(CSOpShenJiBaiLian)
	self:RegisterProtocol(SCShenJiBaiLianInfo, "OnSCShenJiBaiLianInfo")
	self:RegisterProtocol(SCShenJiBaiBianDrawReward, "OnSCShenJiBaiBianDrawReward")
	self:RegisterProtocol(SCShenJiBaiLianNewDrawLog, "OnSCShenJiBaiLianNewDrawLog")
	self:RegisterProtocol(SCShenJiBaiLianConvertShopMoneyChange, "OnSCShenJiBaiLianConvertShopMoneyChange")
end

function SiXiangCallWGCtrl:DayChange()
	if self.view:IsOpen() then
		self.view:Flush(nil, "day_change")
	end
	self.data:RegisterSummonRemindInBag()
end

function SiXiangCallWGCtrl:GetCurShowIndex()
	local show_index = -1
	if self.view:IsOpen() then
		show_index = self.view:GetShowIndex()
	elseif self.xianqi_zhenlian_view:IsOpen() then
		show_index = self.xianqi_zhenlian_view:GetShowIndex()
	end
	return show_index
end


----------------------------------四象召唤-------------------------------------

function SiXiangCallWGCtrl:SendSiXiangRequest(opera, param1, param2, param3, param4)
	local protocol = ProtocolPool.Instance:GetProtocol(CSOpYuanShenZhaoHuan)
 	protocol.op_type = opera or 0
 	protocol.param1 = param1 or 0
 	protocol.param2 = param2 or 0
 	protocol.param3 = param3 or 0
 	protocol.param4 = param4 or 0
 	protocol:EncodeAndSend()
end

-- 四象召唤信息
function SiXiangCallWGCtrl:OnSCSiXiangSummonInfo(protocol)
	self.data:SetSiXiangSummonInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.sixiang_call_sx)
	end

	ViewManager.Instance:FlushView(GuideModuleName.SiXiangExchange)

	if self.sixiang_record_view:IsOpen() then
		self.sixiang_record_view:Flush(0, SiXiangRecordType.SELF)
	end
end

-- 四象召唤抽奖信息
function SiXiangCallWGCtrl:OnSiXiangSummonReward(protocol)
	self.data:SetSiXiangSummonReward(protocol)
	self.sixiang_reward_view:Open()
	self.sixiang_reward_view:Flush(0, "summon_reward")
	RemindManager.Instance:Fire(RemindName.SiXiangCall_SX)
	FightSoulWGCtrl.Instance:FlushBone()
end

-- 四象召唤全服日志新信息
function SiXiangCallWGCtrl:OnSiXiangSummonNewDrawLog(protocol)
	self.data:AddSiXiangServerLog(protocol)
	if self.sixiang_record_view:IsOpen() then
		self.sixiang_record_view:Flush(0, SiXiangRecordType.ALL)
	end
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.sixiang_call_sx, "sever_log")
	end
end

--四象召唤兑换商店货币变化
function SiXiangCallWGCtrl:OnSiXiangSummonShopMoneyChange(protocol)
	self.data:SetSiXiangShopMoneyNum(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.SiXiangExchange)
end

function SiXiangCallWGCtrl:PlayNextRewardTween()
	self.sixiang_reward_view:NextGetRewardTween()
end

-- 抽奖
function SiXiangCallWGCtrl:SiXiangSummon(item_id, need_num, draw_type)
	self.data:SetCacheSummonInfo({item_id = item_id, need_num = need_num, draw_type = draw_type})
    local has_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
    if has_num >= need_num then
        self:SendSummon(draw_type, 0)
    elseif draw_type == OP_YSZH_DRAW_TYPE.MULTI or draw_type == OP_YSZH_DRAW_TYPE.SINGLE then
        local complement_num = SiXiangCallWGData.Instance:GetSiXiangSummonOtherCfg("complement_num")
        if complement_num <= 0 then -- 不能用仙玉补足
            TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
        else
            local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
            if not item_cfg then
                return
            end

            local name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
            local price = complement_num * (need_num - has_num)
            local str = string.format(Language.Common.CommonAlertFormat1, price, name, need_num - has_num)

            TipWGCtrl.Instance:OpenCheckAlertTips(str,
            	BindTool.Bind(self.SendSummon, self, draw_type, 1),
            	"sixiang_summon_alert" .. draw_type)
        end
    end
end

function SiXiangCallWGCtrl:SendSummon(draw_type, cost_gold)
	local summon_type = self.data:GetSiXiangSummonType()
    self:SendSiXiangRequest(OP_YUAN_SHEN_ZHAO_HUAN_TYPE.DRAW, draw_type, cost_gold, summon_type)
end

function SiXiangCallWGCtrl:IsSkipSummonAnim()
	return self.view:GetSummonSkipToggleState() or true
end

function SiXiangCallWGCtrl:IsSkipZhenLianAnim()
	return self.xianqi_zhenlian_view:GetZhenLianSkipToggleState()
end

function SiXiangCallWGCtrl:Test()
	if not self.xianqi_zhenlian_view:IsOpen() then
		self.xianqi_zhenlian_view:Open()
	end
end

----------------------------------四象召唤 end-------------------------------------

--[[ 四象预告
function SiXiangCallWGCtrl:OnSCSiXiangYuGao(protocol)
	self.data:SetSiXiangYuGaoInfo(protocol)
	self.sixiang_activate_view:Flush()
	self:CheckSiXiangYuGaoOpen()
	self:CheckSiXiangCall()
	RemindManager.Instance:Fire(RemindName.SiXiangCall_YG)
end

function SiXiangCallWGCtrl:CheckSiXiangCall()
	local is_open = self.data:GetSiXiangIsActivate()
	MainuiWGCtrl.Instance:SetMainTopBtnObjEnable("BtnSiXiangCallView", is_open)
end

function SiXiangCallWGCtrl:CheckSiXiangYuGaoOpen()
	local yugao_info = self.data and self.data:GetSiXiangYuGaoInfo()
	if not yugao_info then
		return
	end

	MainuiWGCtrl.Instance:AddInitCallBack(ACTIVITY_TYPE.SIXIANG_YUGAO, function ()
		if self.data:IsGetAllYuGaoReward() then
			MainuiWGCtrl.Instance:ActivitySetButtonVisible(ACTIVITY_TYPE.SIXIANG_YUGAO, false)
			return
		end

		local role_level = RoleWGData.Instance:GetAttr("level")
		local show_level = -1
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local show_day = -1

		local cfg_list = self.data:GetNoticeDataList()
		if cfg_list and cfg_list[1] then
			show_level = cfg_list[1].level_limit
			show_day = cfg_list[1].open_server_day_limit
		end
		
		if show_level < 0 or role_level < show_level then
			return
		end

		if show_day < 0 or open_day < show_day then
			return
		end

		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.SIXIANG_YUGAO)
		if not act_cfg then
			return
		end

		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.SIXIANG_YUGAO, ACTIVITY_STATUS.OPEN, 0, 0, 0, 0)
	end)
end
--]]


---------仙器真炼(F1 神机百炼)  start------------------------------------------------------------------------------
function SiXiangCallWGCtrl:DrawMachine(draw_type, cost_gold)
	self:SendMachineOp(MACHINE_OP_TYPE.DRAW, draw_type, cost_gold)--1表示使用仙玉补足
end

function SiXiangCallWGCtrl:SendMachineOp(opera, param1, param2, param3)
	-- print_error("FFFF===== 仙器真炼(F1 神机百炼) 请求操作", opera, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSOpShenJiBaiLian)
 	protocol.op_type = opera or 0
 	protocol.param1 = param1 or 0
 	protocol.param2 = param2 or 0
 	protocol.param3 = param3 or 0
	--print_error(opera, param1, param2)
 	protocol:EncodeAndSend()
end

function SiXiangCallWGCtrl:OnSCShenJiBaiLianInfo(protocol)
	-- print_error("1111111 info", protocol.person_draw_log, protocol.server_draw_log, protocol)
	self.data:SetMachineInfo(protocol)

	--刷新
	if self.xianqi_zhenlian_view:IsOpen() then
		self.xianqi_zhenlian_view:Flush()
	end

	ViewManager.Instance:FlushView(GuideModuleName.SiXiangExchange)

	if self.xianqi_record_view:IsOpen() then
		self.xianqi_record_view:Flush(0, SiXiangRecordType.SELF)
	end
end

--抽奖信息返还
function SiXiangCallWGCtrl:OnSCShenJiBaiBianDrawReward(protocol)
	-- print_error("22222222 抽奖信息返还reward", protocol)
	self.data:SetMachineResult(protocol)
	self.xianqi_reward_view:Open()
	self.xianqi_reward_view:Flush(0, "xianqi_reward")
	RemindManager.Instance:Fire(RemindName.SiXiangCall_XQZL)
end

function SiXiangCallWGCtrl:OnSCShenJiBaiLianNewDrawLog(protocol)
	-- print_error("3333333 add_log", protocol)
	self.data:AddMachineLog(protocol)
	if self.xianqi_record_view:IsOpen() then
		self.xianqi_record_view:Flush(0, SiXiangRecordType.ALL)
	end
	if self.xianqi_zhenlian_view:IsOpen() then
		self.xianqi_zhenlian_view:Flush()
	end
end

--打开仙器真炼抽奖信息
function SiXiangCallWGCtrl:OpenXianQiRecordView()
	if not self.xianqi_record_view:IsOpen() then
		self.xianqi_record_view:Open()
	end
end

function SiXiangCallWGCtrl:OnSCShenJiBaiLianConvertShopMoneyChange(protocol)
	self.data:SetXianQiShopMoneyChange(protocol.convert_shop_money_num)
	ViewManager.Instance:FlushView(GuideModuleName.SiXiangExchange)
end

--NewFun start

-- 仙器真炼--请求抽奖
function SiXiangCallWGCtrl:TryToXianQiZhenLian(item_id, need_num, draw_type)
	self.data:SetCacheXianQiInfo({item_id = item_id, need_num = need_num, draw_type = draw_type})
    local has_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
    if has_num >= need_num then
        self:DrawMachine(draw_type, 0)
    else
        local complement_num = SiXiangCallWGData.Instance:GetXQZLOtherCfgByKey("complement_num")
        if complement_num <= 0 then -- 不能用仙玉补足
            TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
        else
            local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
            if not item_cfg then
                return
            end

            local name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
            local price = complement_num * (need_num - has_num)
            -- local str = string.format(Language.Common.CommonAlertFormat3, price, name)
            local str = SiXiangCallWGData.Instance:GetMaAlertStr(item_id, need_num, has_num)

            TipWGCtrl.Instance:OpenCheckAlertTips(str,
            	BindTool.Bind(self.DrawMachine, self, draw_type, 1),
            	"xianqi_zhenlian_alert" .. draw_type)
        end
    end
end

function SiXiangCallWGCtrl:PlayXQZLNextRewardTween()
	self.xianqi_reward_view:NextGetRewardTween()
end

function SiXiangCallWGCtrl:FlushXQZLView()
	if self.xianqi_zhenlian_view and self.xianqi_zhenlian_view:IsOpen() then
		self.xianqi_zhenlian_view:Flush()
	end
end

--NewFun end
----------仙器真炼(F1 神机百炼)  end-----------------------------------------------------------------------------