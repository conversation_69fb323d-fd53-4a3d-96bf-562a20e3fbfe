RechargeRewardWGData = RechargeRewardWGData or BaseClass()

RechargeItemType = {NORMAL = 1, CHAOZHI = 4, REWARD = 2, CARD = 3}
function RechargeRewardWGData:__init()
	if RechargeRewardWGData.Instance then
		ErrorLog("[RechargeRewardWGData] Attemp to create a singleton twice !")
	end
	RechargeRewardWGData.Instance = self

	self.diff_weekday_chongzhi_stage_fetch_flag = 0
	-- RemindManager.Instance:Register(RemindName.DailyRecharge,BindTool.Bind(self.IsShowDailyRed,self))
end

function RechargeRewardWGData:__delete()
	-- RemindManager.Instance:UnRegister(RemindName.DailyRecharge)
	RechargeRewardWGData.Instance = nil
end

function RechargeRewardWGData:IsShowDailyRed()
	if self:GetDailyTotalRechargeRemind() then
		return 1
	end
	return 0
end

-- 根据首充天数获取物品列表
function RechargeRewardWGData:GetItemListByDay(day)
	local chongzhireward_auto = ConfigManager.Instance:GetAutoConfig("chongzhireward_auto")

	local chongzhi_reward = chongzhireward_auto.special_chongzhi_reward[day - 1]
	local show_list = {}
	if nil ~= chongzhi_reward then
		local item_list_in_gift = ItemWGData.Instance:GetItemListInGift(chongzhi_reward.reward_item.item_id)
		for _,item_in_gift in pairs(item_list_in_gift) do
			table.insert(show_list, item_in_gift)
		end
	end

	return show_list
end

-- 获取在8天循环中的第几天
function RechargeRewardWGData:GetDailyRechargeRewardDay()
	-- 从开服第一天开始，8天一循环
	local day = 0
	local open_days = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if open_days > 0 then
		day = open_days % 8
		day = day ~= 0 and day or 8
	end

	return day
end

-- 获取每日累充奖励rechargereward_daily
function RechargeRewardWGData:GetDailyTotalRechargeReward(gold_type)
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local cfg = ConfigManager.Instance:GetAutoConfig("daily_total_recharge_auto").daily_total_recharge_reward
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	for i = #cfg, 1, -1 do                 -- 先循环找一下当前档位等级
		if cfg[i].seq == gold_type - 1 and role_level >= cfg[i].role_level and open_day >= cfg[i].opengame_day then
			return cfg[i]
		end
	end
end

-- 获取每日累充右边列表rechargereward_daily
function RechargeRewardWGData:GetDailyTotalRechargeRightReward()
	local role_level =  RoleWGData.Instance:GetRoleLevel()
	local cfg = ConfigManager.Instance:GetAutoConfig("daily_total_recharge_auto").total_recharge_day_reward
	local fetch_flag = self:GetTotalRechargeTotalDayFetchRewardFlag()
	local days = self:GetTotalRechargeTotalDay()
	local data_list = {}
	local level = 0
	for i = 1, #cfg do                 -- 先循环找一下当前档位等级
		local j = i + 1
		if j > #cfg then return end
		if cfg[j].role_level > role_level then
			level = cfg[i].role_level
			break
		end
	end
	local j = 0
	for i = 1, #cfg do
		if cfg[i].role_level > level then break end
		if cfg[i].role_level == level then
			local flag = -1
			local has_fetch = fetch_flag[32 - j] == 1 and 1 or 0
			j = j + 1
			if cfg[i].total_recharge_day <= days then 						-- 到达时间
				flag = has_fetch > 0 and 1 or 0			-- 是否已领取
			else
				flag = -1							-- 时间未到
			end
			table.insert(data_list, {fetch_flag = flag, reward_item = cfg[i].reward_item, total_recharge_day = cfg[i].total_recharge_day})
		end
	end
	return data_list
end

-- 每日累充主界面图标红点提醒
function RechargeRewardWGData:GetDailyTotalRechargeRemind()
	local chongzhi_data = ServerActivityWGData.Instance:GetTotalChongZhiData()
	if nil == chongzhi_data then
		return
	end
	local remind_num = 0
	for i = 1, 3 do
		local cfg =self:GetDailyTotalRechargeReward(i)
		local fetch_flag = self:GetTotalRechargeFetchRewardFlag(i - 1)
		local is_can_get = false
		if cfg then
			is_can_get = chongzhi_data.today_recharge >= cfg.need_recharge_gold and fetch_flag ~= 1
		end
		if is_can_get then
			remind_num = remind_num + 1
		end
	end

	local data_list = self:GetDailyTotalRechargeRightReward()
	for i = 1, #data_list do
		if data_list[i].fetch_flag == 0 then
			remind_num = remind_num + 1
		end
	end

	return remind_num > 0 and true or false
end

-- 获取当天每日首充奖励
function RechargeRewardWGData:GetCurDayDailyRechargeReward(type)
	local day = self:GetDailyRechargeRewardDay()
	local cfg = ConfigManager.Instance:GetAutoConfig("firstchongzhirewardcfg_auto").daily_chongzhi_reward
	for i = 1, #cfg do
		if cfg[i].opengame_day == day and cfg[i].daily_first_chongzhi_type == type then
			return cfg[i]
		end
	end
end

-- 获取每日首充额外奖励
function RechargeRewardWGData:GetDailyRechargeExtraReward()
	local chongzhireward_auto = ConfigManager.Instance:GetAutoConfig("chongzhireward_auto")
	local other_cfg = chongzhireward_auto.other
	return other_cfg[1].daily_first_recharge_extra_reward_item
end

-- 获取再充额外奖励
function RechargeRewardWGData:GetZaiRechargeExtraReward()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("chongzhireward_auto").other[1]
	local reward_item = other_cfg.zai_chongzhi_reward_item
	local show_list = {}
	if nil ~= reward_item then
		local item_list_in_gift = ItemWGData.Instance:GetItemListInGift(reward_item.item_id)
		for _,item_in_gift in pairs(item_list_in_gift) do
			table.insert(show_list, item_in_gift)
		end
	end

	return show_list
end

-- 获三充额外奖励
function RechargeRewardWGData:GetSanRechargeExtraReward()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("chongzhireward_auto").other[1]
	local reward_item = other_cfg.third_chongzhi_reward_item
	local show_list = {}
	if nil ~= reward_item then
		local item_list_in_gift = ItemWGData.Instance:GetItemListInGift(reward_item.item_id)
		for _,item_in_gift in pairs(item_list_in_gift) do
			table.insert(show_list, item_in_gift)
		end
	end

	return show_list
end


-- 每日累充根据物品seq取物品列表
function RechargeRewardWGData:GetItemListBySeq(seq)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local updata_time = math.max(server_time - 6 * 3600, 0) 	-- 6点刷新时间
	local w_day = tonumber(os.date("%w", updata_time))
	local chongzhi_reward = ConfigManager.Instance:GetAutoConfig("chongzhireward_auto").weekday_total_chongzhi
	for k,v in pairs(chongzhi_reward) do
		if seq == v.seq and w_day == v.day_of_week then
			local item_list_in_gift = ItemWGData.Instance:GetItemListInGift(v.reward_item.item_id)
			return item_list_in_gift
		end
	end
	return nil
end

-- 每日累充根据物品seq获取需充值元宝
function RechargeRewardWGData:GetChongzhiBySeq(seq)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local updata_time = math.max(server_time - 6 * 3600, 0) 	-- 6点刷新时间
	local w_day = tonumber(os.date("%w", updata_time))

	local chongzhi_reward = ConfigManager.Instance:GetAutoConfig("chongzhireward_auto").weekday_total_chongzhi
	for k,v in pairs(chongzhi_reward) do
		if seq == v.seq and w_day == v.day_of_week then
			return v.need_total_chongzhi
		end
	end
	return nil
end

-- 每日累充额度
function RechargeRewardWGData:GetTotalRecharge()
	local chongzhi_data = ServerActivityWGData.Instance:GetTotalChongZhiData()
	if nil == chongzhi_data then
		return
	end
	local total_charge_value = chongzhi_data.diff_wd_chongzhi_value

	return total_charge_value
end

function RechargeRewardWGData:SetRechargeFetchInfo(protocol)
	self.total_chongzhi_day_count = protocol.total_chongzhi_day_count
	self.diff_weekday_chongzhi_stage_fetch_flag = protocol.daily_total_chongzhi_fetch_reward_flag
	self.daily_reward_fetch_flag = bit:d2b(protocol.daily_total_chongzhi_fetch_reward_flag)
	self.daily_day_fetch_flag = bit:d2b(protocol.daily_day_chongzhi_fetch_reward_flag)
	self.daily_total_recharge_fetch_reward_flag = bit:d2b(protocol.daily_total_recharge_fetch_reward_flag)
	self.daily_total_recharge_total_day_fetch_reward_flag = bit:d2b(protocol.daily_total_recharge_total_day_fetch_reward_flag)
	self.daily_total_recharge_total_day = protocol.daily_total_recharge_total_day
end

-- 每日累充奖励领取标记rechargereward_daily
function RechargeRewardWGData:GetTotalRechargeFetchRewardFlag(index)
	return self.daily_total_recharge_fetch_reward_flag[32 - index]
end

-- 每日累充充值天数奖励标记rechargereward_daily
function RechargeRewardWGData:GetTotalRechargeTotalDayFetchRewardFlag()
	return self.daily_total_recharge_total_day_fetch_reward_flag
end

-- 每日累充充值天数rechargereward_daily
function RechargeRewardWGData:GetTotalRechargeTotalDay()
	return self.daily_total_recharge_total_day
end

-- 获取今日首充累计天数
function RechargeRewardWGData:GetDailyRechargeTotalDays()
	return self.total_chongzhi_day_count or 0
end

-- 每日累充奖励领取标记
function RechargeRewardWGData:GetRechargeFetchFlag()
	return self.diff_weekday_chongzhi_stage_fetch_flag or 0
end

-- 每日累充奖励领取标记
function RechargeRewardWGData:GetDailyRechargeFetchFlag(index)
	self.daily_reward_fetch_flag = self.daily_reward_fetch_flag or {}
	return self.daily_reward_fetch_flag[32 - index]
end

-- 每日累充奖励按天领取标记
function RechargeRewardWGData:GetDailyRechargeDayRewardFlag()
	return self.daily_day_fetch_flag or {}
end

function RechargeRewardWGData.CanGetFirstRechargeReward()
	local chongzhi_data = ServerActivityWGData.Instance:GetTotalChongZhiData()
	local history_recharge = chongzhi_data.history_recharge or 0
	local fetch_flag = chongzhi_data.special_first_chongzhi_fetch_reward_flag or {}

	local has_reward = false
	-- for i = 1, 3 do
	-- 	local is_geted = fetch_flag[32 - i] == 1
	-- 	local need_gold = RechargeRewardWGData.Instance:GetFirstRechargeNeedGold(i)
	-- 	print_error(history_recharge,need_gold)
	-- 	if history_recharge >= need_gold and not is_geted then
	-- 		has_reward = true
	-- 		break
	-- 	end
	-- end

	--改为一个档次
	local is_geted = fetch_flag[32 - 1] == 1
	local need_gold = RechargeRewardWGData.Instance:GetFirstRechargeNeedGold(1)
	-- print_error(history_recharge,need_gold)
	if history_recharge >= need_gold and not is_geted then
		has_reward = true
	end

	return has_reward
end

function RechargeRewardWGData.CanGetRechargeAgainReward()
	local chongzhi_data = ServerActivityWGData.Instance:GetTotalChongZhiData()
	if  nil == chongzhi_data.history_recharge then
		return false
	end
	return chongzhi_data.zai_chongzhi_fetch_reward_flag == 1
end

function RechargeRewardWGData.CanGetRechargeThirdReward()
	local chongzhi_data = ServerActivityWGData.Instance:GetTotalChongZhiData()
	if  nil == chongzhi_data.history_recharge then
		return false
	end
	return chongzhi_data.third_chongzhi_reward_flag == 1
end

function RechargeRewardWGData:GetIsHasRechargeDailyReward()
	local today_recharge = RechargeWGData.Instance:GetTodayRecharge()
	local has_reward = false
	for i = 1, 3 do
		local cfg = self:GetCurDayDailyRechargeReward(i)
		local fetch_flag = self:GetDailyRechargeFetchFlag(i)
		if cfg then
			has_reward = today_recharge >= cfg.need_total_chongzhi and fetch_flag ~= 1
		end

		if has_reward == true then
			return true
		end

	end

	local fetch_flag = self:GetDailyRechargeDayRewardFlag()
	local cfg = ConfigManager.Instance:GetAutoConfig("firstchongzhirewardcfg_auto").total_chongzhi_reward
	local days = self:GetDailyRechargeTotalDays()				--累计充值次数
	for i = 1, #cfg do
		if i <= days then
			has_reward = fetch_flag[32 - i] ~= 1
			if has_reward == true then
				return true
			end
		else
			break
		end
	end

	return has_reward
end

function RechargeRewardWGData.CanGetRechargeTotalDailyReward()
	local chongzhi_data = ServerActivityWGData.Instance:GetTotalChongZhiData()
	if  nil == chongzhi_data.history_recharge then
		return false
	end
	-- for i = 0, TotalRechargeRewardView.MAX_PAGE_COUNT - 1 do
	-- 	local need_chongzhi = RechargeRewardWGData.Instance:GetChongzhiBySeq(i)
	-- 	local fetch_flag = RechargeRewardWGData.Instance:GetRechargeFetchFlag()
	-- 	local fetch_flag_list = bit:d2b(fetch_flag)
	-- 	if chongzhi_data.diff_wd_chongzhi_value >= need_chongzhi and fetch_flag_list[32 - i] == 0 then
	-- 		return true
	-- 	end
	-- end
	return false
end

-- 根据档次获取首充所需元宝
function RechargeRewardWGData:GetFirstRechargeNeedGold(type)
	local cfg = ConfigManager.Instance:GetAutoConfig("firstchongzhirewardcfg_auto").first_chongzhi_reward
	return cfg[type] and cfg[type].need_total_chongzhi
end

-- 获取首充最高档次所需元宝
function RechargeRewardWGData:GetFirstRechargeMaxNeedGold()
	local cfg = ConfigManager.Instance:GetAutoConfig("firstchongzhirewardcfg_auto").first_chongzhi_reward
	return cfg[#cfg].need_total_chongzhi
end

-- 获得首充每档显示资源id
function RechargeRewardWGData:GetFirstRechargeShowId(type)
	local cfg = ConfigManager.Instance:GetAutoConfig("firstchongzhirewardcfg_auto").first_chongzhi_reward
	return cfg[type].show_left, cfg[type].show_right
end

-- 根据档次获取首充奖励数据
function RechargeRewardWGData:GetFirstRechargeRewardData(type)
	local cfg = ConfigManager.Instance:GetAutoConfig("firstchongzhirewardcfg_auto").first_chongzhi_reward
	local reward_list = {}
	for k, v in pairs(cfg) do
		if v.first_chongzhi_type == type then
			reward_list = v
			break
		end
	end
	if next(reward_list) ==  nil then return {} end
	local prof = RoleWGData.Instance:GetRoleProf()
	local reward_item = __TableCopy(reward_list["reward_item_prof_"..prof])

	if type == 1 then
		local reward = reward_item[1]
		local param_t = {}
		param_t.star_level = 2
		reward.param = param_t
	end
	return reward_item
end
