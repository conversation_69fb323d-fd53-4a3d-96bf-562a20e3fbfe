OneDayRechargeView = OneDayRechargeView or BaseClass(SafeBaseView)
function OneDayRechargeView:__init()
    self.cur_select_seq = -1
    self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/oneday_recharge_ui_prefab", "layout_oneday_recharge")
end

function OneDayRechargeView:LoadCallBack()
    self.node_list["btn_close"].button:AddClickListener(BindTool.Bind(self.Close,self))
    self.node_list["buy_btn"].button:AddClickListener(BindTool.Bind(self.OnClickBuy,self))

    self.toggle_list = AsyncListView.New(OneDayRechargeToggleItem, self.node_list["recharge_grade_list"])
    self.toggle_list:SetSelectCallBack(BindTool.Bind1(self.ToggleBtnSelectCallBack,self))
    self.toggle_list:SetStartZeroIndex(true)

    if self.grade_item_list == nil then
        self.grade_item_list = {}
        for i = 0, 5 do
            self.grade_item_list[i] = ItemCell.New(self.node_list["reward_item_" .. i])
        end
    end

    GlobalTimerQuest:AddDelayTimer(function()
		self:LoadModel()
	end, 0.5)
    
end

function OneDayRechargeView:ReleaseCallBack()
    self.cur_select_seq = -1

    if self.toggle_list then
        self.toggle_list:DeleteMe()
        self.toggle_list = nil
    end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end

    if self.grade_item_list then
        for k, v in pairs(self.grade_item_list) do
            v:DeleteMe()
        end
        self.grade_item_list = nil
    end

    if CountDownManager.Instance:HasCountDown("oneday_recharge_down") then
		CountDownManager.Instance:RemoveCountDown("oneday_recharge_down")
	end
end

function OneDayRechargeView:OpenCallBack()
    OneDayRechargeWGCtrl.Instance:SendOneDayRechargeReq(OA_DAILY_RECHARGE_OPERATE_TYPE.INFO)
end

function OneDayRechargeView:OnClickBuy()
    if self.cur_select_seq ~= -1 then
        local btn_red = OneDayRechargeWGData.Instance:GetRewardSeqRed(self.cur_select_seq)
        if not btn_red then --充点小钱
            ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz)
        else --领取
            OneDayRechargeWGCtrl.Instance:SendOneDayRechargeReq(OA_DAILY_RECHARGE_OPERATE_TYPE.FETCH_REWARD, self.cur_select_seq)
        end
    end

end

function OneDayRechargeView:OnFlush(param_t)
    for k, v in pairs(param_t) do
		if "all" == k then
			self:LoginTimeCountDown()
            self:FlushView()
        elseif k == "flush_model" then
            GlobalTimerQuest:AddDelayTimer(function()
                self:LoadModel()
            end, 0.5)
		end
	end
end

function OneDayRechargeView:ToggleBtnSelectCallBack(cell)
    if cell == nil or cell.data == nil then
        return
    end
    
    self.cur_select_seq = cell.data.seq

    local cur_seq_reward = OneDayRechargeWGData.Instance:GetRewardSeqCfg(cell.data.seq)
    if cur_seq_reward and cur_seq_reward.reward_item then
        for k, v in pairs(self.grade_item_list) do
            v:SetData(cur_seq_reward.reward_item[k])
        end
    end

    self:FlushRewardView()
end

function OneDayRechargeView:FlushRewardView()
    local btn_red = OneDayRechargeWGData.Instance:GetRewardSeqRed(self.cur_select_seq)
    local is_get = OneDayRechargeWGData.Instance:GetRewardIsGet(self.cur_select_seq)
    self.node_list["btn_redmind"]:SetActive(btn_red)
    self.node_list["buy_btn"]:SetActive(not is_get)
    self.node_list["buy_is_get"]:SetActive(is_get)

    self.node_list["btn_text"].text.text = (not btn_red) and Language.OneDayRecharge.BtnTxtDesc2 or Language.OneDayRecharge.BtnTxtDesc1
end

function OneDayRechargeView:FlushView()
    local chongzhi_gold = OneDayRechargeWGData.Instance:GetChongzhiGold()
    self.node_list["my_charge_value"].text.text = string.format(Language.OneDayRecharge.ChongZhiTotalGold, chongzhi_gold)

    local reward_grade_list = OneDayRechargeWGData.Instance:GetGradeTabList()
    if reward_grade_list then
        self.toggle_list:SetDataList(reward_grade_list)
        
        local jump_hole = OneDayRechargeWGData.Instance:GetJumpRewardSeq()
        if jump_hole ~= nil and jump_hole ~= self.cur_select_seq then
            self.toggle_list:JumpToIndex(jump_hole)
            self.cur_select_seq = jump_hole
        end
    end

    self:FlushRewardView()
end

function OneDayRechargeView:LoadModel()
    if self.model_display == nil then
		self.model_display = OperationActRender.New(self.node_list["model_root"])

        local show_model_id = OneDayRechargeWGData.Instance:GetShowModelCfg()
        if show_model_id > 0 then
            local data = {}
            data.item_id = show_model_id
            data.render_type = 0
            data.position = Vector3(0, 0, 0)
            data.rotation = Vector3(0, 0, 0)
            data.scale = Vector3(1, 1, 1)
            self.model_display:SetData(data)

            local capability = ItemShowWGData.CalculateCapability(show_model_id, true)
            self.node_list["model_cap_value"].text.text = capability
        end
	end
end

------------------------------------活动时间倒计时
function OneDayRechargeView:LoginTimeCountDown()
    local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DAILY_RECHARGE)
	if activity_data ~= nil then
		local invalid_time = activity_data.end_time
        if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
            self.node_list["act_time"].text.text = TimeUtil.FormatSecondDHM8(invalid_time - TimeWGCtrl.Instance:GetServerTime())
            CountDownManager.Instance:AddCountDown("oneday_recharge_down", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
        end
	end
end

function OneDayRechargeView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.login_act_date = TimeUtil.FormatUnixTime2Date()
		self.node_list["act_time"].text.text = TimeUtil.FormatSecondDHM8(valid_time)
	end
end

function OneDayRechargeView:OnComplete()
    self.node_list["act_time"].text.text = ""
	self:Close()
end


OneDayRechargeToggleItem = OneDayRechargeToggleItem or BaseClass(BaseRender)
function OneDayRechargeToggleItem:OnFlush()
    if not self.data then
		return
	end
    
    self.node_list["nor_recharge_txt"].text.text = string.format(Language.OneDayRecharge.GradeTabGold, self.data.need_chongzhi)
    self.node_list["hl_recharge_txt"].text.text = string.format(Language.OneDayRecharge.GradeTabGold, self.data.need_chongzhi)
    
    local is_red = OneDayRechargeWGData.Instance:GetRewardSeqRed(self.data.seq)
    self.node_list["remind"]:SetActive(is_red)

    local is_get = OneDayRechargeWGData.Instance:GetRewardIsGet(self.data.seq)
    XUI.SetGraphicGrey(self.node_list["nor_img"], is_get)
    XUI.SetGraphicGrey(self.node_list["hl_img"], is_get)
end

function OneDayRechargeToggleItem:OnSelectChange(is_select)
	self.node_list["hl_img"]:SetActive(is_select)
	self.node_list["nor_img"]:SetActive(not is_select)
end