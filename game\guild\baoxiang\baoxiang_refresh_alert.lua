BaoXiangRefreshAlert = BaoXiangRefreshAlert or BaseClass(SafeBaseView)

function BaoXiangRefreshAlert:__init()
	self:SetMaskBg(false)
	self:LoadConfig()
end

-- 加载配置
function BaoXiangRefreshAlert:LoadConfig()
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "baoxiang_refresh_alert")
end

function BaoXiangRefreshAlert:__delete()

end

function BaoXiangRefreshAlert:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("baoxiang_add_firend_cd_time") then
        CountDownManager.Instance:RemoveCountDown("baoxiang_add_firend_cd_time")
    end
	if self.reward_list then
		for k,v in pairs(self.reward_list) do
			v:DeleteMe()
		end
		self.reward_list = nil
	end
end

function BaoXiangRefreshAlert:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_OK"], BindTool.Bind1(self.OnClickOK, self))
	XUI.AddClickEventListener(self.node_list["btn_cancel"], BindTool.Bind1(self.OnClickCancel, self))
	XUI.AddClickEventListener(self.node_list["close_btn"], BindTool.Bind1(self.OnClickClose, self))
	if not self.reward_list then
		self.reward_list = {}
	end
end

function BaoXiangRefreshAlert:OnFlush()
	if IsEmptyTable(self.data) then return end
	local item = self.data
	local new_quality = item.new_quality
	local old_quality = item.old_quality
	local treasure_cfg = GuildBaoXiangWGData.Instance:GetTreasureCfgByQuality(new_quality)
	local color = treasure_cfg and treasure_cfg.common_color or 1
	local bx_name = ToColorStr(treasure_cfg and treasure_cfg.name or "",ITEM_COLOR[color])
	local alert_str = string.format(Language.BiZuoBaoXiang.SuccRefreshAlertTips,item.name,bx_name)
	self.node_list["rich_dialog"].text.text = alert_str
	local uid = item.uid
	local is_friend = SocietyWGData.Instance:CheckIsFriend(uid)
	self.node_list["btn_cancel"]:SetActive(not is_friend)
	if CountDownManager.Instance:HasCountDown("baoxiang_add_firend_cd_time") then
        CountDownManager.Instance:RemoveCountDown("baoxiang_add_firend_cd_time")
    end
	XUI.SetButtonEnabled(self.node_list["btn_cancel"], true)
	self.node_list["btn_cancel_text"].text.text = Language.BiZuoBaoXiang.AlertCancelBtnText

	local item_data = {}
	for i = 0, #treasure_cfg.reward_item do
		table.insert(item_data,treasure_cfg.reward_item[i])
	end
	local tab = #item_data
	local index = 0
	for i = 1, tab do
		if self.reward_list[i] == nil then
			self.reward_list[i] = ItemCell.New(self.node_list.reward_list)
		end
		self.reward_list[i]:SetActive(true)
		self.reward_list[i]:SetData(item_data[i])
		index = index + 1
	end

	for i = index + 1, #self.reward_list do
		if self.reward_list[i] then
			self.reward_list[i]:SetActive(false)
		end
	end
end

function BaoXiangRefreshAlert:SetData(data)
	self.data = data
end

function BaoXiangRefreshAlert:OnClickOK()
	if IsEmptyTable(self.data) then return end
	local uid = self.data.uid
	GuildWGCtrl.Instance:SendCSDailyTreasureOperate(DAILY_TREASURE_OPERATE_TYPE.DAILY_TREASURE_OPERATE_TYPE_HELP_REWARD_REQ,uid)
	self:OnClickClose()
end

function BaoXiangRefreshAlert:OnClickCancel()
	if IsEmptyTable(self.data) then return end
	local uid = self.data.uid
	SocietyWGCtrl.Instance:AddFriend(uid,0)

	if CountDownManager.Instance:HasCountDown("baoxiang_add_firend_cd_time") then
        CountDownManager.Instance:RemoveCountDown("baoxiang_add_firend_cd_time")
    end
    self:UpdateInviteTime(0,30)
    CountDownManager.Instance:AddCountDown("baoxiang_add_firend_cd_time", BindTool.Bind(self.UpdateInviteTime,self), 
    BindTool.Bind(self.CompleteInviteTime, self), 30 + TimeWGCtrl.Instance:GetServerTime())
    XUI.SetButtonEnabled(self.node_list["btn_cancel"], false)
	-- self:Close()
end

function BaoXiangRefreshAlert:UpdateInviteTime(elapse_time, total_time)
	local time = math.ceil(total_time - elapse_time)
	if time >= 0 then
		self.node_list["btn_cancel_text"].text.text = time .. "s"
	end
end


function BaoXiangRefreshAlert:CompleteInviteTime()
	XUI.SetButtonEnabled(self.node_list["btn_cancel"], true)
	self.node_list["btn_cancel_text"].text.text = Language.BiZuoBaoXiang.AlertCancelBtnText
end

function BaoXiangRefreshAlert:OnClickClose()
	local item = GuildBaoXiangWGData.Instance:GetBaoXiangRefreshData()
	if not IsEmptyTable(item) then 
		 self:SetData(item)
		 self:Flush()
		 return
	end
	self:Close()
end