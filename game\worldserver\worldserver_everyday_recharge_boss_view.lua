function WorldServerView:InitEveryDayRechargeBossView()
    self.erb_select_boss_data = {}
    self.erb_select_boss_type_data = {}
    self.erb_select_scene_index_cache = -1
    self.erb_model_res_cache = -1

    if not self.erb_cell_list_1 then
        self.erb_cell_list_1 = AsyncBaseGrid.New()
        local t = {}
        t.col = 4
        t.change_cells_num = 1
        t.itemRender = BossRewardCell
        t.list_view = self.node_list["erb_cell_list_1"]
        self.erb_cell_list_1:CreateCells(t)
        self.erb_cell_list_1:SetStartZeroIndex(false)
    end

    if not self.erb_cell_list_2 then
        self.erb_cell_list_2 = AsyncBaseGrid.New()
        local t = {}
        t.col = 4
        t.change_cells_num = 1
        t.itemRender = BossRewardCell
        t.list_view = self.node_list["erb_cell_list_2"]
        self.erb_cell_list_2:CreateCells(t)
        self.erb_cell_list_2:SetStartZeroIndex(false)
    end

    if not self.erb_recharge_type_list then
        self.erb_recharge_type_list = AsyncListView.New(ERBRechargeTypeItemRender, self.node_list.erb_recharge_type_list)
        self.erb_recharge_type_list:SetSelectCallBack(BindTool.Bind(self.OnSelectERBRechargeTypeHandler, self))
        self.erb_recharge_type_list:SetStartZeroIndex(true)
    end

    if not self.erb_boss_list then
        self.erb_boss_list = AsyncListView.New(ERBBossListItemRender, self.node_list.erb_boss_list)
        self.erb_boss_list:SetSelectCallBack(BindTool.Bind(self.OnSelectERBBossItemHandler, self))
        self.erb_boss_list:SetStartZeroIndex(false)
    end

    self.AddClickEventListener(self.node_list["btn_erb_enter"], BindTool.Bind(self.OnClickErbEnter, self))
    self.AddClickEventListener(self.node_list["btn_erb_record"], BindTool.Bind(self.OnClickErbRecord, self))
end

function WorldServerView:ReleaseEveryDayRechargeBossView()
    if self.erb_cell_list_1 then
        self.erb_cell_list_1:DeleteMe()
        self.erb_cell_list_1 = nil
    end

    if self.erb_cell_list_2 then
        self.erb_cell_list_2:DeleteMe()
        self.erb_cell_list_2 = nil
    end

    if self.erb_recharge_type_list then
        self.erb_recharge_type_list:DeleteMe()
        self.erb_recharge_type_list = nil
    end

    if self.erb_boss_list then
        self.erb_boss_list:DeleteMe()
        self.erb_boss_list = nil
    end
end

function WorldServerView:ShowIndexEveryDayRechargeBossView()
    BossWGCtrl.Instance:SendCrossRealChargeBossOperate(CROSS_REAL_CHARGE_BOSS_OPERATE_TYPE.INFO)
    self.node_list.erb_model_display:CustomSetActive(true)
    self.node_list.erb_ui_root:CustomSetActive(false)
    self.node_list.erb_black_mask:CustomSetActive(true)
    self.node_list.erb_ui_root.canvas_group.alpha = 0

    ReDelayCall(self, function ()
        self.node_list.erb_black_mask:CustomSetActive(false)
        self.node_list.erb_ui_root:CustomSetActive(true)
        self.node_list.erb_ui_root.canvas_group:DoAlpha(0, 1, 3):SetEase(DG.Tweening.Ease.Linear):OnComplete(function ()
            -- self.node_list.erb_model_display:CustomSetActive(true)
    
        end)
    end, 0.5, "ShowIndexEveryDayRechargeBossView")

    self.node_list.erb_screen_effect:CustomSetActive(false)
    self.node_list.erb_screen_effect:CustomSetActive(true)
end

function WorldServerView:OnFlushEveryDayRechargeBossView()
    local erb_type_data_list = BossWGData.Instance:GetCurERBTypeList()
    self.erb_recharge_type_list:SetDataList(erb_type_data_list)

    if not IsEmptyTable(erb_type_data_list) then
        self.erb_recharge_type_list:JumpToIndex(self:GetERBRechargeTypeSelect(erb_type_data_list))
    end
end

function WorldServerView:FlushErbRightPanel()
    if not IsEmptyTable(self.erb_select_boss_data) then
        self.node_list.erb_model_name.text.text = self.erb_select_boss_data.boss_name
        self.node_list.erbz_model_lv.text.text = self.erb_select_boss_data.boss_level

        self.erb_cell_list_1:SetDataList(SortDataByItemColor(self.erb_select_boss_data.drop_item_list))
        self.erb_cell_list_2:SetDataList(SortDataByItemColor(self.erb_select_boss_data.special_drop_item_list))

        local is_special_boss = self.erb_select_boss_data.is_special_boss == 1

        if is_special_boss then
            local boss_info = BossWGData.Instance:GetERBBossInfoByBossId(self.erb_select_boss_data.boss_id)
            local next_refresh_time = boss_info.next_refresh_time or 0
            local alive = next_refresh_time == 0
            self.node_list.erb_special_boss_rebirth_pro:CustomSetActive(not alive)

            if not alive then
                local need_kill_boss_num = self.erb_select_boss_data.kill_boss_num
                local kill_boss_num = BossWGData.Instance:GetERBSceneKillBossNumBySceneId(self.erb_select_boss_data.scene_id)
                local target_show_num = kill_boss_num % need_kill_boss_num
    
                self.node_list.erb_special_boss_rebirth_pro.text.text = string.format(Language.Boss.ErbBossRebirthPro, target_show_num, need_kill_boss_num)
            end
        else
            self.node_list.erb_special_boss_rebirth_pro:CustomSetActive(false)
        end
    end

    if not IsEmptyTable(self.erb_select_boss_type_data) then
        local today_recharge_num = RechargeWGData.Instance:GetToDayRealChongZhiRmb()
        local need_recharge_num = self.erb_select_boss_type_data.need_consume_rmb_today
        local recharge_enough = need_recharge_num <= today_recharge_num
        local target_show_recharge_num = recharge_enough and need_recharge_num or today_recharge_num
        local color = recharge_enough and COLOR3B.D_GREEN or COLOR3B.D_RED
        self.node_list.erb_enter_desc.text.text = string.format(Language.Boss.ErbEnterRechargeDesc, color, target_show_recharge_num, need_recharge_num)
    end
end

function WorldServerView:FlushErbBomInfo()
    local cur_layer_relation_cfg, next_layer_relation_cfg = BossWGData.Instance:GetCurErbLayerCfg()
    self.node_list.erb_area_desc.text.text = cur_layer_relation_cfg.group_name or ""

    local role_level = RoleWGData.Instance:GetRoleLevel()
    local is_dianfeng, show_level = RoleWGData.Instance:GetDianFengLevel(role_level)
    self.node_list.erb_level_dianfeng:CustomSetActive(is_dianfeng)
    self.node_list.erb_level_desc.text.text = string.format(Language.Common.LevelNormal, show_level)

    local is_max_group = IsEmptyTable(next_layer_relation_cfg)
    self.node_list.erb_change_area_desc:CustomSetActive(not is_max_group)

    if not is_max_group then
        local level_str = RoleWGData.Instance:TransToDianFengLevelStr(next_layer_relation_cfg.need_role_level_min)
        self.node_list.erb_change_area_desc.text.text = string.format(Language.Boss.ErbChangeAreaDesc, level_str, next_layer_relation_cfg.group_name)
    end
end

---------------------------------click_call_back----------------------------
function WorldServerView:OnSelectERBRechargeTypeHandler(item)
    if nil == item or IsEmptyTable(item.data) then
        return
    end
    local data = item.data
    local index = item.index

    if self.erb_select_scene_index_cache ~= index then
        BossWGCtrl.Instance:SendCrossRealChargeBossOperate(CROSS_REAL_CHARGE_BOSS_OPERATE_TYPE.BOSS, data.scene_index)
        self.erb_select_scene_index_cache = index
    end

    self.erb_select_boss_type_data = data
    local boss_list = BossWGData.Instance:GetERBBossListBySceneIndex(data.scene_index)
    self.erb_boss_list:SetDataList(boss_list)
    if not IsEmptyTable(boss_list) then
        self.erb_boss_list:JumpToIndex(self:GetERBBossItemSelect(boss_list))
    end
end

function WorldServerView:OnSelectERBBossItemHandler(item)
    if nil == item or IsEmptyTable(item.data) then
        return
    end

    local data = item.data
    self.erb_select_boss_data = data
    self:OnFlushBossAnim()
    self:FlushErbRightPanel()
    self:FlushErbBomInfo()
end

---------------------------------get_select----------------------------
function WorldServerView:GetERBRechargeTypeSelect(erb_type_data_list)
    return self.erb_select_scene_index_cache >= 0 and self.erb_select_scene_index_cache or 0
end

function WorldServerView:GetERBBossItemSelect(erb_type_data_list)
    return 1
end

---------------------------------btn_click----------------------------
function WorldServerView:OnClickErbEnter()
    if IsEmptyTable(self.erb_select_boss_type_data) then
        return
    end

    local today_recharge_num = RechargeWGData.Instance:GetToDayRealChongZhiRmb()
    local need_recharge_num = self.erb_select_boss_type_data.need_consume_rmb_today
    local recharge_diff = need_recharge_num - today_recharge_num

    if need_recharge_num > today_recharge_num then
        local content = string.format(Language.Boss.ErbRechargeNotEnoughDesc, recharge_diff)
        TipWGCtrl.Instance:OpenAlertTips(content, function ()
            ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz)
        end)
    else
        BossWGData.Instance:SetEveryDayRechargeRedPoint(self.erb_select_boss_type_data.boss_group, self.erb_select_boss_type_data.boss_group_index)
        RemindManager.Instance:Fire(RemindName.WorldServer_EveryDay_Recharge_Boss)
        self:Flush()
        BossWGData.Instance:SetCurSelectBossID(TabIndex.worserv_everyday_recharge_boss, self.erb_select_boss_type_data.scene_index, self.erb_select_boss_data.boss_id)
        CrossServerWGCtrl.Instance.SendCrossStartReq(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_REAL_CHARGE_BOSS, self.erb_select_boss_type_data.scene_id)
    end
end

function WorldServerView:OnClickErbRecord()
    BossWGCtrl.Instance:SetRecordView(BOSS_RECORD_TYPE.EVERYDAY_RECHARGE_BOSS)
    ViewManager.Instance:Open(GuideModuleName.BossDropRecord)
end

---------------------------------ERBRechargeTypeItemRender----------------------------
ERBRechargeTypeItemRender = ERBRechargeTypeItemRender or BaseClass(BaseRender)

function ERBRechargeTypeItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local bg_nor_bundle, bg_nor_asset = ResPath.GetBossUI("a2_boss_yuan" .. self.data.boss_group_index)
    self.node_list.bg_nor.image:LoadSprite(bg_nor_bundle, bg_nor_asset, function ()
        self.node_list.bg_nor.image:SetNativeSize()
    end)

    local nor_bundle, nor_asset = ResPath.GetBossUI("a2_boss_gwtx" .. self.data.boss_group_index)
    self.node_list.icon_nor.image:LoadSprite(nor_bundle, nor_asset, function ()
        self.node_list.icon_nor.image:SetNativeSize()
    end)

    local hl_bundle, hl_asset = ResPath.GetBossUI("a2_boss_gwtxhl" .. self.data.boss_group_index)
    self.node_list.icon_select.image:LoadSprite(hl_bundle, hl_asset, function ()
        self.node_list.icon_select.image:SetNativeSize()
    end)

    self.node_list.desc_recharge.text.text = self.data.scene_name
    -- self.node_list.desc_recharge.text.text = string.format(Language.Boss.ErbRechargeRmb, self.data.need_consume_rmb_today)

    local remind = BossWGData.Instance:IsShowEveryDayRechargeRedPoint(self.data.boss_group, self.data.boss_group_index)
    self.node_list.remind:CustomSetActive(remind)
end

function ERBRechargeTypeItemRender:OnSelectChange(is_select)
    self.node_list.icon_nor:CustomSetActive(not is_select)
    self.node_list.icon_select:CustomSetActive(is_select)
end

--------------------------------ERBBossListItemRender---------------------------------
ERBBossListItemRender = ERBBossListItemRender or BaseClass(BaseRender)

function ERBBossListItemRender:__delete()
    if self.refresh_event then
        GlobalTimerQuest:CancelQuest(self.refresh_event)
        self.refresh_event = nil
    end
end

function ERBBossListItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local bundle, asset = ResPath.GetBossIcon("wrod_boss_" .. self.data.big_icon)
    self.node_list["img_boss"].image:LoadSprite(bundle, asset, function()
        self.node_list.img_boss.image:SetNativeSize()
    end)

    local is_special_boss = self.data.is_special_boss == 1
    if is_special_boss and self.data.special_show_name ~= "" then
        self.node_list["text_boss_level"].text.text = self.data.special_show_name
    else
        local monster_info = BossWGData.Instance:GetMonsterInfo(self.data.boss_id)
        if monster_info and monster_info.boss_jieshu > 0 then
            self.node_list["text_boss_level"].text.text = string.format(Language.Boss.JieShu,
                NumberToChinaNumber(monster_info.boss_jieshu))
        end
    end

    self.node_list["text_boss_name"].text.text = self.data.boss_name

    local is_drop = self.data.is_drop_jewelry == 1 and self.data.jewelry_txt ~= ""
    self.node_list.drop_jewelry:CustomSetActive(is_drop)
    self.node_list.desc_drop_jewelry.text.text = is_drop and self.data.jewelry_txt or ""

    self:RefreshRemainTime()
    if not is_special_boss and not self.refresh_event then
        self.refresh_event = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.RefreshRemainTime, self), 1)
    end
end

function ERBBossListItemRender:RefreshRemainTime()
    local boss_server_info = BossWGData.Instance:GetERBBossInfoByBossId(self.data.boss_id)
    local is_special_boss = self.data.is_special_boss == 1
    local next_refresh_time = boss_server_info.next_refresh_time or 0
    local alive = next_refresh_time == 0
    
    self.node_list.gray_mask:CustomSetActive(not alive)

    if is_special_boss then
        self.node_list.be_kill_flag:CustomSetActive(false)
        self.node_list.special_boss_kill_flag:CustomSetActive(not alive)
        self.node_list["lbl_time"].text.text = ""

        if not alive then
            local need_kill_boss_num = self.data.kill_boss_num
            local kill_boss_num = BossWGData.Instance:GetERBSceneKillBossNumBySceneId(self.data.scene_id)
            local target_show_num = kill_boss_num % need_kill_boss_num

            self.node_list.special_boss_rebirth_time.text.text = target_show_num .. "/" .. need_kill_boss_num
        end
        
        if self.refresh_event then
            GlobalTimerQuest:CancelQuest(self.refresh_event)
            self.refresh_event = nil
        end
    else
        self.node_list.special_boss_kill_flag:CustomSetActive(false)
        self.node_list.be_kill_flag:CustomSetActive(not alive)

        local flush_time = next_refresh_time - TimeWGCtrl.Instance:GetServerTime()

        if not alive and flush_time > 0 then
            self.node_list["lbl_time"].text.text = TimeUtil.FormatSecond(flush_time)
        else
            self.node_list["lbl_time"].text.text = ""
            if self.refresh_event then
                GlobalTimerQuest:CancelQuest(self.refresh_event)
                self.refresh_event = nil
            end
        end
    end
end

function ERBBossListItemRender:OnSelectChange(is_select)
    self.node_list.select_image:SetActive(is_select)
end