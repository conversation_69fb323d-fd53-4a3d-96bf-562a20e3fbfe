WardrobeSuitRender = WardrobeSuitRender or BaseClass(BaseRender)
function WardrobeSuitRender:LoadCallBack()
    -- if not self.item_cell then
    --     self.item_cell = ItemCell.New(self.node_list["item_pos"])
    --     self.item_cell:SetIsShowTips(false)
    -- end
end

function WardrobeSuitRender:__delete()
    -- if self.item_cell then
    --     self.item_cell:DeleteMe()
    --     self.item_cell = nil
    -- end
end


function WardrobeSuitRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    -- local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.icon)
    -- if item_cfg then
    --     self.item_cell:SetData({item_id = self.data.icon})
    -- end

    self.node_list.normal_bg.raw_image:LoadSprite(ResPath.GetRawImagesPNG(string.format("a3_sz_bg_tz0%d", self.data.suit_color)))
    local sex = RoleWGData.Instance:GetRoleSex()
    local sex_dif_id = sex == GameEnum.MALE and 1 or 3
    local bundle, asset = ResPath.GetNoPackPNG(string.format("a3_wardrobe_suit_%d_%d", sex_dif_id, self.data.suit))
    self.node_list.wardrobe_suit_icon.raw_image:LoadSprite(bundle, asset)
    local state_color = self.data.real_act_num >= self.data.total_part_num and COLOR3B.GREEN or COLOR3B.WHITE
    local state_str = string.format("%d/%d", self.data.real_act_num, self.data.total_part_num)
    state_str = ToColorStr(state_str, state_color)
    local suit_title = string.format("%s(%s)", self.data.suit_name, state_str)
    self.node_list.suit_name.tmp.text = suit_title

    local is_remind = WardrobeWGData.Instance:GetWardRobeRemindBySuit(self.data.part_list)
    self.node_list.remind:SetActive(is_remind)
    local bundle, asset = ResPath.GetWardrobeImg(string.format("a3_sz_bg_0%d", self.data.suit_color))
    self.node_list.flag_image.image:LoadSprite(bundle, asset, function()
        self.node_list.flag_image.image:SetNativeSize()
    end)
    self.node_list.flag_image_txt.text.text = self.data.suit_cell_title
end

function WardrobeSuitRender:OnSelectChange(is_select)
	self.node_list["select_bg"]:SetActive(is_select)
end

-------------------------------WardrobePartRender------------------------------
WardrobePartRender = WardrobePartRender or BaseClass(BaseRender)
function WardrobePartRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    if self.data.type ~= nil then
        local fashion_cfg = nil
        local name_str = ""
        self.node_list.part_item_color_flag:SetActive(false)
        local is_remind, is_act = WardrobeWGData.Instance:GetWardRobeRemindByPartData(self.data)

		if self.data.type == WARDROBE_PART_TYPE.FASHION then -- 时装大类
			fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(self.data.param1, self.data.param2)
            name_str = fashion_cfg and fashion_cfg.name
        elseif self.data.type == WARDROBE_PART_TYPE.MOUNT or self.data.type == WARDROBE_PART_TYPE.HUA_KUN then
            local qichong_select_type = nil
            if self.data.type == WARDROBE_PART_TYPE.MOUNT then
                fashion_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(self.data.param1)
                name_str = fashion_cfg and fashion_cfg.image_name
            else
                fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(self.data.param1)
                name_str = fashion_cfg and fashion_cfg.name
            end
		end

        if fashion_cfg ~= nil then
            self.node_list.get_way_root:CustomSetActive(fashion_cfg.get_msg ~= nil and fashion_cfg.get_msg ~= 0)

            if fashion_cfg.get_msg ~= nil and fashion_cfg.get_msg ~= 0 then
                self.node_list.get_way_name:CustomSetActive(fashion_cfg.get_msg == 2 or fashion_cfg.get_msg == 3)
                self.node_list.get_way_buy_root:CustomSetActive(fashion_cfg.get_msg == 1)
                local get_str = fashion_cfg.get_msg == 2 and fashion_cfg.get_desc or fashion_cfg.get_param1

                self.node_list.get_way_name.tmp.text = get_str
                self.node_list.get_way_buy_num.tmp.text = fashion_cfg.get_param1
            end

            local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.show_item_id)
            local color = item_cfg.color <= 10 and item_cfg.color or 10

            if item_cfg then
                self.node_list.normal_bg.image:LoadSprite(ResPath.GetWardrobeImg(string.format("a3_sz_bg_color_0%d", color)))
                self.node_list.part_item_name.tmp.text = name_str
            end

            local sex = RoleWGData.Instance:GetRoleSex()
            local prof = RoleWGData.Instance:GetRoleProf()
            local func = ResPath.GetCommonWardrobeIcon
            if self.data.part_type == SHIZHUANG_TYPE.BODY then
                if sex == 0 then
                    func = ResPath.GetWomanRoleWardrobeIcon
                else
                    func = ResPath.GetManRoleWardrobeIcon
                end
            elseif self.data.part_type == SHIZHUANG_TYPE.SHENBING then
                func = ResPath.GetCommonWardrobeWeaponIcon
            end

            local bundle, asset = func(self.data.show_item_id, prof)
            self.node_list.part_item_icon.image:LoadSprite(bundle, asset, function()
                self.node_list.part_item_icon.image:SetNativeSize()
            end)
        end

        self.node_list.lock_status:CustomSetActive(not is_act)
        self.node_list.remind:CustomSetActive(is_remind)
    end
end

function WardrobePartRender:OnSelectChange(is_select)
    if self.node_list and self.node_list.select_bg then
        self.node_list.select_bg:SetActive(is_select)
    end
end

-------------------------------WardrobePartShowRender------------------------------
WardrobePartShowRender = WardrobePartShowRender or BaseClass(BaseRender)
function WardrobePartShowRender:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["item_pos"])
    end
end

function WardrobePartShowRender:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function WardrobePartShowRender:OnFlush()
    if IsEmptyTable(self.data) then
        self.view:SetActive(false)
        return
    end

    self.view:SetActive(true)
    self.node_list.item_type_icon:CustomSetActive(self.data.show_item_id == 0)
    self.node_list.item_pos:CustomSetActive(self.data.show_item_id ~= 0)
    self.node_list.item_bg_icon:CustomSetActive(self.data.show_item_id == 0)
    self.node_list.lock_status:CustomSetActive(self.data.state == REWARD_STATE_TYPE.UNDONE)

    if self.data.show_item_id ~= 0 then
        self.item_cell:SetData({item_id = self.data.show_item_id})
    else
        local final_part_type = self.data.part_type or 1
        self.node_list.item_type_icon.image:LoadSprite(ResPath.GetWardrobeImg(string.format("a3_sz_icon_dz%d", final_part_type)))
    end
end

-------------------------------WardrobePartRender------------------------------
WardrobeFashionPartRender = WardrobeFashionPartRender or BaseClass(BaseRender)
function WardrobeFashionPartRender:OnFlush()
    if IsEmptyTable(self.data) then
        self.view:SetActive(false)
        return
    end

    local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(self.data.part_type, self.data.index)
    local is_act = false

    if fashion_cfg then
        self.node_list.part_item_color_flag:SetActive(fashion_cfg.is_open_dyeing == 1)
        self.node_list.get_way_root:CustomSetActive(fashion_cfg.get_msg ~= nil and fashion_cfg.get_msg ~= 0)

        if fashion_cfg.get_msg ~= nil and fashion_cfg.get_msg ~= 0 then
            self.node_list.get_way_name:CustomSetActive(fashion_cfg.get_msg == 2 or fashion_cfg.get_msg == 3)
            self.node_list.get_way_buy_root:CustomSetActive(fashion_cfg.get_msg == 1)
            local get_str = fashion_cfg.get_msg == 2 and fashion_cfg.get_desc or fashion_cfg.get_param1

            self.node_list.get_way_name.tmp.text = get_str
            self.node_list.get_way_buy_num.tmp.text = fashion_cfg.get_param1
        end

        is_act = NewAppearanceWGData.Instance:GetFashionIsAct(self.data.part_type, self.data.index)
        local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.active_stuff_id)

        if item_cfg then
            local color = item_cfg.color <= 10 and item_cfg.color or 10
            self.node_list.normal_bg.image:LoadSprite(ResPath.GetWardrobeImg(string.format("a3_sz_bg_color_0%d", color)))
            self.node_list.part_item_name.tmp.text = fashion_cfg.name

            local sex = RoleWGData.Instance:GetRoleSex()
            local func = ResPath.GetCommonWardrobeIcon
            local prof = RoleWGData.Instance:GetRoleProf()
            if self.data.part_type == SHIZHUANG_TYPE.BODY then
                if sex == 0 then
                    func = ResPath.GetWomanRoleWardrobeIcon
                else
                    func = ResPath.GetManRoleWardrobeIcon
                end
            elseif self.data.part_type == SHIZHUANG_TYPE.SHENBING then
                func = ResPath.GetCommonWardrobeWeaponIcon
            end

            local bundle, asset = func(self.data.active_stuff_id, prof)
            self.node_list.part_item_icon.image:LoadSprite(bundle, asset, function()
                self.node_list.part_item_icon.image:SetNativeSize()
            end)
        end
    end

    self.node_list.lock_status:CustomSetActive(not is_act)
    local collect_red = WardrobeWGData.Instance:GetWardrobeFashionCollectRed(self.data.part_type, self.data.index)
    self.node_list.remind:CustomSetActive(self.data.is_remind or collect_red)
end

function WardrobeFashionPartRender:OnSelectChange(is_select)
    self.node_list.select_bg:SetActive(is_select)
end

-------------------------------WardrobeAttrRender------------------------------
WardrobeAttrRender = WardrobeAttrRender or BaseClass(BaseRender)
function WardrobeAttrRender:__init()
    self.attr_list = {}
    local attr_num = self.node_list.attr_list.transform.childCount
    for i = 1, attr_num do
        self.attr_list[i] = self.node_list.attr_list:FindObj("attr_" .. i)
        XUI.AddClickEventListener(self.attr_list[i], BindTool.Bind(self.OnClickAttrShowMessage, self, i))           --点击事件
    end
end

function WardrobeAttrRender:__delete()
    self.attr_list = nil
end

function WardrobeAttrRender:SetFromItemTip(bool)
    self.is_from_itemtip = bool
end

function WardrobeAttrRender:OnClickAttrShowMessage(attr_index)
    local data = self:GetData()
    if IsEmptyTable(data) then
        return
    end

    local list = data.attr_list
    if (not list) or(not list[attr_index]) then
        return
    end

    if list[attr_index].attr_type == "attr_skill" then
        local cfg = WardrobeWGData.Instance:GetExtraSkillCfgBySkillId(list[attr_index].value)
        if cfg then
            local show_data = {
                icon = cfg.skill_icon,
                top_text = cfg.skill_name,
                body_text = cfg.skill_desc,
                x = 0,
                y = -120,
                set_pos = true,
                hide_next = true,
                is_active_skill = true,
            }
            NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
        end
    end
end

function WardrobeAttrRender:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
        self.view:SetActive(false)
        return
    else
        self.view:SetActive(true)
	end

    local attri_color = "#ffffff"
    if data.is_act then
        attri_color = COLOR3B.DEFAULT
    end

    if self.node_list.lock_image then
        self.node_list.lock_image:CustomSetActive(not data.is_act)
        self.node_list.unlock_image:CustomSetActive(data.is_act)
    end

    local need_str
    if self.is_from_itemtip then
        if self.node_list.suit_icon then
            XUI.SetGraphicGrey(self.node_list.suit_icon, not data.is_act)
        end
        need_str = data.all_act_attr and Language.Wardrobe.AllActDesc1 or string.format(Language.Wardrobe.SuitNumCompany1, data.need_num)
    else
        need_str = data.all_act_attr and Language.Wardrobe.AllActDesc or string.format(Language.Wardrobe.SuitNumCompany, data.need_num)
    end

    self.node_list.need_num.tmp.text = ToColorStr(need_str, attri_color)
    local list = data.attr_list

    for k, v in ipairs(self.attr_list) do
        if list[k] then
            local is_per = not EquipmentWGData.Instance:GetAttrIsPerByAttrStr(list[k].attr_type)
            local name = ""
            local attr_str = ""
            local value = ""
            if list[k].attr_type == "add_per" then
                name = Language.Wardrobe.SpecialAttr
                value = is_per and list[k].value or list[k].value / 100 .. "%"
                local format_str = self.is_from_itemtip and "%s   %s" or "%s %s"
                attr_str = string.format(format_str, name, value)
            elseif list[k].attr_type == "attr_skill" then
                local cfg = WardrobeWGData.Instance:GetExtraSkillCfgBySkillId(list[k].value)
                if cfg then
                    if self.is_from_itemtip then
                        attr_str = string.format("%s   <u>%s</u>", Language.Wardrobe.ExtraSkillName, cfg.skill_name)
                    else
                        attr_str = string.format("%s     <u>%s</u>", Language.Wardrobe.ExtraSkillName, cfg.skill_name)
                    end
                end
            else
                name = EquipmentWGData.Instance:GetAttrName(list[k].attr_type, false)
                value = is_per and list[k].value or list[k].value / 100 .. "%"
                if self.is_from_itemtip then
                    attr_str = string.format("%s   %s", name, value)
                elseif is_per then
                    attr_str = string.format("%s         %s", name, value)
                else
                    attr_str = string.format("%s     %s", name, value)
                end
            end

            v.tmp.text = ToColorStr(attr_str, attri_color)
            v:SetActive(true)
        else
            v:SetActive(false)
        end
    end
end
-------------------------------WardrobeFashionTypeRender------------------------------
WardrobeFashionTypeRender = WardrobeFashionTypeRender or BaseClass(BaseRender)
function WardrobeFashionTypeRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list.normal_txt.tmp.text = self.data.name
    self.node_list.select_txt.tmp.text = self.data.name
    self.node_list.remind:CustomSetActive(self.data.is_red)
end

function WardrobeFashionTypeRender:OnSelectChange(is_select)
    self.node_list.select:SetActive(is_select)
end

-------------------------------WardrobeFashionFilterRender------------------------------
WardrobeFashionFilterRender = WardrobeFashionFilterRender or BaseClass(BaseRender)
function WardrobeFashionFilterRender:OnFlush()
    self.node_list.name_txt.tmp.text = self.data
end

function WardrobeFashionFilterRender:OnSelectChange(is_select)
    self.node_list.select:SetActive(is_select)
end

