require("game/operation_activity/tiancai_chushen/tiancai_chushen_wg_data")
require("game/operation_activity/tiancai_chushen/tiancai_chushen_view")
require("game/operation_activity/tiancai_chushen/tiancai_chushen_second_view")
require("game/operation_activity/tiancai_chushen/tiancai_chushen_result_view")

ChuShenWGCtrl = ChuShenWGCtrl or BaseClass(BaseWGCtrl)

function ChuShenWGCtrl:__init()
	if ChuShenWGCtrl.Instance then
		ErrorLog("[ChuShenWGCtrl] Attemp to create a singleton twice !")
	end
	ChuShenWGCtrl.Instance = self

	self.chushen_data = ChuShenWGData.New()
	self.chushen_rank_view = ChuShenSecondRankPanel.New()
	self.chushen_result_view = ChuShenCookResultPanel.New()
	self.chushen_unlock_view = ChuShenRewardShowPanel.New()

	self:RegisterAllProtocols()
	-- self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.ChuShenInfoReq, self))
	OperationActivityWGCtrl.Instance:ListenHotUpdate("config/auto_new/oa_tiancaichushen_auto", BindTool.Bind(self.OnHotUpdate, self))
end

function ChuShenWGCtrl:__delete()
	ChuShenWGCtrl.Instance = nil

	if self.chushen_data then
		self.chushen_data:DeleteMe()
		self.chushen_data = nil
	end

	if self.chushen_rank_view  then
		self.chushen_rank_view:DeleteMe()
		self.chushen_rank_view = nil
	end

	if self.chushen_result_view  then
		self.chushen_result_view:DeleteMe()
		self.chushen_result_view = nil
	end

	if self.chushen_unlock_view then
		self.chushen_unlock_view:DeleteMe()
		self.chushen_unlock_view = nil
	end
end

function ChuShenWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOATianCaiChuShenLog,'OnSCOATianCaiChuShenLog')
	self:RegisterProtocol(SCOATianCaiChuShenInfo,'OnSCOATianCaiChuShenInfo')
	self:RegisterProtocol(SCOATianCaiChuShenFoodMenu,'OnSCOATianCaiChuShenFoodMenu')
	self:RegisterProtocol(SCOATianCaiChuShenCook,'OnSCOATianCaiChuShenCook')

	self:RegisterProtocol(CSOATianCaiChuShenOpera)

end

function ChuShenWGCtrl:ChuShenInfoReq()
	self:CSOATianCaiChuShenOpera(TIANCAICHUSHEN_OPERA_TYPE.TIANCAICHUSHEN_OPERA_TYPE_1)
	self:CSOATianCaiChuShenOpera(TIANCAICHUSHEN_OPERA_TYPE.TIANCAICHUSHEN_OPERA_TYPE_2)
	self:CSOATianCaiChuShenOpera(TIANCAICHUSHEN_OPERA_TYPE.TIANCAICHUSHEN_OPERA_TYPE_3)
end

function ChuShenWGCtrl:OpenChuShenSecondPanel()
	self.chushen_rank_view:Open()
end

--天才厨神信息请求
function ChuShenWGCtrl:CSOATianCaiChuShenOpera(operate_typr, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSOATianCaiChuShenOpera)
	protocol.operate_type = operate_typr
	protocol.param_1 = param1 or 0
	protocol.param_2 = param2 or 0
	protocol.param_3 = param3 or 0
	protocol:EncodeAndSend()
end

--厨神排行榜
function ChuShenWGCtrl:OnSCOATianCaiChuShenLog(protocol)
	ChuShenWGData.Instance:ChuShenRankData(protocol)
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_chushen)
	self.chushen_rank_view:Flush()
end

--厨神活动信息
function ChuShenWGCtrl:OnSCOATianCaiChuShenInfo(protocol)
	ChuShenWGData.Instance:ChuShenActInfoData(protocol)
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_chushen)
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_chushen, "ChuShenAllInfo")
	self.chushen_unlock_view:Flush()
	RemindManager.Instance:Fire(RemindName.OperationChuShen)
end

function ChuShenWGCtrl:OnSCOATianCaiChuShenFoodMenu(protocol)
	ChuShenWGData.Instance:ChuShenFoodInfoData(protocol)
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_chushen)
	RemindManager.Instance:Fire(RemindName.OperationChuShen)
end

--烹饪结果返回
function ChuShenWGCtrl:OnSCOATianCaiChuShenCook(protocol)
	ChuShenWGData.Instance:ResultData(protocol)
	--OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_chushen, "ChuShenResult")
	if protocol.cook_type and protocol.cook_type ~= TIANCAICHUSHEN_COOK_TYPE.TIANCAICHUSHEN_COOK_TYPE_5 then
	    ChuShenWGCtrl.Instance:OpenResultPanel(protocol)
	end
    OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_chushen, "ChuShenResult")
	RemindManager.Instance:Fire(RemindName.OperationChuShen)
end

function ChuShenWGCtrl:OpenResultPanel(protocol)
	self.chushen_result_view:SetDataOpen(protocol)
end

function ChuShenWGCtrl:OpenRewardPanel()
	self.chushen_unlock_view:Open()
end

function ChuShenWGCtrl:OnHotUpdate()
	self.chushen_data:LoadConfig()
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_chushen, "ChuShenHotUpdata")
end