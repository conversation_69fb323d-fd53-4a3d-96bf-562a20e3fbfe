LordEveryDayShopView = LordEveryDayShopView or BaseClass(SafeBaseView)
function LordEveryDayShopView:__init()
    self.view_layer = UiLayer.Normal
    self:SetMaskBg(false, true)

    self:AddViewResource(0, "uis/view/lord_every_day_shop_prefab", "layout_lord_shop")
end

function LordEveryDayShopView:OpenCallBack()
    if LordEveryDayShopWGData.Instance:GetRechargeRedPoint() then
        LordEveryDayShopWGData.Instance:SetRechargeRedPoint()
        RemindManager.Instance:Fire(RemindName.LoadEveryDayShop)
    end
end

function LordEveryDayShopView:LoadCallBack()
	self.money_bar = MoneyBar.New()
	local bundle, asset = ResPath.GetWidgets("MoneyBar")
	local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }

    self.money_bar:SetMoneyShowInfo(0, 0, show_params)
	self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)

    if not self.shop_list then
        self.shop_list = {}
        for i = 1, 9 do
            self.shop_list[i] = LordEveryDayShopRender.New(self.node_list["shop_list"]:FindObj("lord_shop_item" .. i))
            self.shop_list[i]:SetIndex(i)
        end
    end

    XUI.AddClickEventListener(self.node_list["refresh_shop_btn"], BindTool.Bind(self.OnClickFlushShop, self))
    self.node_list["open_panel_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGo,self))
    self.node_list["item_icon"].button:AddClickListener(BindTool.Bind(self.ShowItemTips, self))
end

function LordEveryDayShopView:ReleaseCallBack()
    if self.is_playing_time then
		GlobalTimerQuest:CancelQuest(self.is_playing_time)
		self.is_playing_time = nil
	end

    if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

    if self.shop_list then
		for k, v in pairs(self.shop_list) do
            v:DeleteMe()
        end
        self.shop_list = nil
	end

    self.is_playing_turning = false
end

function LordEveryDayShopView:DelayClickAble()
	self.is_playing_turning = true
	self.is_playing_time = GlobalTimerQuest:AddDelayTimer(function ()
		self.is_playing_turning = false
	end, 1)
end

function LordEveryDayShopView:OnFlush(prarm_t)
	for k,v in pairs(prarm_t) do
        if k == "all" then
            self:FlushShopList()
            self:FlushExchangeView()
        elseif k == "flush_cell_anim" then
			self:DelayClickAble()
            self:PlayFlushCellTween()
            self:FlushShopList()
            self:FlushExchangeView()
        elseif k == "flush_exchange" then
            self:FlushExchangeView()
		end
	end
end

function LordEveryDayShopView:FlushShopList()
    local data = LordEveryDayShopWGData.Instance:GetShowShopInfo()
    for k, v in ipairs(self.shop_list) do
        v:SetData(data[k])
    end
end

function LordEveryDayShopView:FlushExchangeView()
    local other_cfg = LordEveryDayShopWGData.Instance:GetOtherCfg()
    local cost_item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.exchange_item_id)
	self.node_list["item_icon"].image:LoadSprite(ResPath.GetItem(cost_item_cfg.icon_id))
    local item_count = ItemWGData.Instance:GetItemNumInBagById(other_cfg.exchange_item_id)
    self.node_list.item_num.text.text = item_count
end

function LordEveryDayShopView:PlayFlushCellTween()
    if self.shop_list then
        for i = 5, 9 do
            self.shop_list[i]:PlayItemTween()
        end
    end
end

function LordEveryDayShopView:OnClickFlushShop()
	if self.is_playing_turning or self.just_send_and_wait then
		TipWGCtrl.Instance:ShowSystemMsg(Language.LordEveryDayShop.IsPlayingTrun)
		return
	end

    local other_cfg = LordEveryDayShopWGData.Instance:GetOtherCfg()
    local flush_money = other_cfg.refresh_consume or 0
    local have_enough = true
    local str = ""
    if other_cfg.refresh_type == 1 then
        str = string.format(Language.LordEveryDayShop.LordEveryDayShopFlushConfirm, flush_money)
        have_enough = RoleWGData.Instance:GetIsEnoughUseGold(flush_money)
    elseif other_cfg.refresh_type == 2 then
        str = string.format(Language.LordEveryDayShop.LordEveryDayShopFlushConfirm2, flush_money)
        have_enough = RoleWGData.Instance:GetIsEnoughBindGold(flush_money)
    end
    local ok_func = function ()
        if have_enough then
            self.just_send_and_wait = true
            LordEveryDayShopWGCtrl.Instance:SendCSLordShopOperateRequest(LORD_EVERYDAY_SHOP_TYPE.SHOP_FLUSH)
            GlobalTimerQuest:AddDelayTimer(function ()
                self.just_send_and_wait = false
            end, 1)
        else
            if other_cfg.refresh_type == 1 then
                TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.LordEveryDayShop.NeedEnoughToFlush,flush_money))
                UiInstanceMgr.Instance:ShowChongZhiView()
            elseif other_cfg.refresh_type == 2 then
                TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.LordEveryDayShop.NeedEnoughToFlush2,flush_money))
            end
        end
    end
    TipWGCtrl.Instance:OpenCheckAlertTips(str, ok_func, "lord_everyday_shop", nil)
end

function LordEveryDayShopView:OnClickGo()
    local other_cfg = LordEveryDayShopWGData.Instance:GetOtherCfg()
    if other_cfg.open_panel and other_cfg.open_panel ~= "" then
        FunOpen.Instance:OpenViewNameByCfg(other_cfg.open_panel)
    end
end

function LordEveryDayShopView:ShowItemTips()
    local other_cfg = LordEveryDayShopWGData.Instance:GetOtherCfg()
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = other_cfg.exchange_item_id})
end

-----------LordEveryDayShopRender----------------
LordEveryDayShopRender = LordEveryDayShopRender or BaseClass(BaseRender)

function LordEveryDayShopRender:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["item_pos"])
    end

    -- self.node_list["consume_icon"].button:AddClickListener(BindTool.Bind(self.ShowItemTips, self))
    self.node_list["item_click"].button:AddClickListener(BindTool.Bind(self.ClickExchange, self))
end

function LordEveryDayShopRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
    
    self:StopAnimDelay()
    self.playing_truning = false
end


function LordEveryDayShopRender:OnFlush()
    if nil == self.data then
		return
	end

    local item_data = self.data.cfg.reward_item[0]
    if item_data then
        local item_cfg = ItemWGData.Instance:GetItemConfig(item_data.item_id)
        if item_cfg then
            self.item_cell:SetData({item_id = item_data.item_id, is_bind = item_data.is_bind, num = item_data.num})
            self.node_list.item_name.text.text = item_cfg.name --ToColorStr(item_cfg.name, ITEM_COLOR_LIGHT[item_cfg.color])
        else
            print_error("策划检测配置魔王仙藏奖励配置 reward_item item_id"..tostring(item_data.item_id))
        end

    end

    self.node_list.limit_buy_time.text.text = string.format(Language.LordEveryDayShop.LimitBuy, self.data.buy_time, self.data.cfg.buy_limit)
    self.node_list.consume_num.text.text = self.data.cfg.consume_num

    local cost_item_cfg = ItemWGData.Instance:GetItemConfig(self.data.cfg.consume_item_id or 0)
    if cost_item_cfg then
	    self.node_list["consume_icon"].image:LoadSprite(ResPath.GetItem(cost_item_cfg.icon_id))
    end

    self.node_list.item_click:SetActive(self.data.buy_time < self.data.cfg.buy_limit)
    self.node_list.img_empty:SetActive(self.data.buy_time >= self.data.cfg.buy_limit)
end

function LordEveryDayShopRender:ShowItemTips()
    if nil == self.data then
		return
	end

    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.data.cfg.consume_item_id})
end

function LordEveryDayShopRender:ClickExchange()
	if self.playing_truning or not self.data then
		return
	end

    if self.data.buy_time >= self.data.cfg.buy_limit then
        TipWGCtrl.Instance:ShowSystemMsg(Language.LordEveryDayShop.MaxExchangeNum)
        return
    end

    local data = {}
    data.convery_seq = self.data.cfg.seq
    data.buy_time = self.data.buy_time
    LordEveryDayShopWGCtrl.Instance:OpenShopConvertTipsView(data)
    --LordEveryDayShopWGCtrl.Instance:SendCSLordShopOperateRequest(LORD_EVERYDAY_SHOP_TYPE.SHOP_BUY, self.data.cfg.seq, 1)
end

function LordEveryDayShopRender:PlayItemTween()
	if self.playing_truning then
		return
	end

    self:StopAnimDelay()
    self.playing_truning = true

    self.node_list["nor_group"].canvas_group.alpha = 0
    self.node_list["nor_group"].transform.rotation = Quaternion.Euler(0, 180, 0)
    self.node_list["hl_group"].transform.rotation = Quaternion.Euler(0, 0, 0)
    self.node_list["hl_group"].transform.localScale = Vector3(1, 1, 1)
    self.node_list["hl_group"]:SetActive(false)

    local base_time = 0
    local time1 = 0.25
	local time2 = 0.25

    self.turning_anim1 =  GlobalTimerQuest:AddDelayTimer(function ()
		for i = 1, 2 do
			self.node_list["hl_group"]:SetActive(true)
			self.node_list["hl_group"].canvas_group.alpha = 1
            self.node_list["hl_group"].rect:DORotate(Vector3(0, -90, 0), time1, DG.Tweening.RotateMode.Fast)
            self.node_list["hl_group"].rect:DOScale(Vector3(1.1, 1.1, 1.1), time1)
            self.node_list["hl_group"].canvas_group.alpha = 1

            self.node_list["nor_group"].canvas_group.alpha = 0
            self.node_list["nor_group"].rect:DORotate(Vector3(0, 90, 0), time1, DG.Tweening.RotateMode.Fast)
		end
	end, base_time)

    self.turning_anim2 = GlobalTimerQuest:AddDelayTimer(function ()
        self.node_list["hl_group"]:SetActive(false)
        self.node_list["hl_group"].rect:DORotate(Vector3(0, 0, 0), time1, DG.Tweening.RotateMode.Fast)
        self.node_list["hl_group"].canvas_group:DoAlpha(1, 0, 0.2)
        
        self.node_list["nor_group"].canvas_group.alpha = 1
        self.node_list["nor_group"].rect:DORotate(Vector3(0, 0, 0), time2, DG.Tweening.RotateMode.Fast)
        self.node_list["nor_group"].rect:DOScale(Vector3(1, 1, 1), time2)
	end, base_time + time1)

    self.turning_anim3 = GlobalTimerQuest:AddDelayTimer(function ()
		self.playing_truning = false
		self:ReInitPosition()
	end, base_time + time1 + time2)
end

function LordEveryDayShopRender:ReInitPosition()
    self.node_list["nor_group"].canvas_group.alpha = 1
    self.node_list["nor_group"].transform.rotation = Quaternion.Euler(0, 0, 0)
    self.node_list["nor_group"].transform.localScale = Vector3(1, 1, 1)
    self.node_list["hl_group"]:SetActive(false)
 end

 function LordEveryDayShopRender:StopAnimDelay()
	if self.turning_anim1 then
		GlobalTimerQuest:CancelQuest(self.turning_anim1)
		self.turning_anim1 = nil
	end

	if self.turning_anim2 then
		GlobalTimerQuest:CancelQuest(self.turning_anim2)
		self.turning_anim2 = nil
	end

	if self.turning_anim3 then
		GlobalTimerQuest:CancelQuest(self.turning_anim3)
		self.turning_anim3 = nil
	end
end
