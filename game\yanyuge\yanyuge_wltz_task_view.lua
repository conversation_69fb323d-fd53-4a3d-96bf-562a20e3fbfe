YanYuGeWLTZTaskView = YanYuGeWLTZTaskView or BaseClass(SafeBaseView)

function YanYuGeWLTZTaskView:__init()
    self.view_layer = UiLayer.Normal
    self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel_2", {vector2 = Vector2(33, 0), sizeDelta = Vector2(628, 404)})
    self:AddViewResource(0, "uis/view/yanyuge_ui_prefab", "layout_yanyuge_wltz_task_view")
end

function YanYuGeWLTZTaskView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.YanYuGe.WLTZTaskPanelTitle
    XUI.AddClickEventListener(self.node_list.btn_panel1, BindTool.Bind(self.OnClickPanelBtn, self, 1))
    XUI.AddClickEventListener(self.node_list.btn_panel2, BindTool.Bind(self.OnClickPanelBtn, self, 2))

    if not self.wltz_task_list then
        self.wltz_task_list = AsyncListView.New(WLTZTaskListCellRender, self.node_list.wltz_task_list)
        self.wltz_task_list:SetStartZeroIndex(false)
    end

    self.select_panel_index = 1
end

function YanYuGeWLTZTaskView:ReleaseCallBack()
    if self.wltz_task_list then
        self.wltz_task_list:DeleteMe()
        self.wltz_task_list = nil
    end

    self.select_panel_index = nil
end

function YanYuGeWLTZTaskView:OnFlush(param_t, index)
    for k,v in pairs(param_t) do
        if k == "jump_panel" then
            self.select_panel_index = tonumber(v.panel_index)
        end
    end

    for i = 1, 2 do
        self.node_list["btn_normal_panel" .. i]:CustomSetActive(i == self.select_panel_index)
        self.node_list["btn_hl_panel" .. i]:CustomSetActive(i ~= self.select_panel_index)
    end

    local data_list = YanYuGeWGData.Instance:GetCurExtraScoreCfg()
    local target_data_list = {}

    for k, v in pairs(data_list) do
        if v.get_type == self.select_panel_index then
            table.insert(target_data_list, v)
        end
    end

    self.wltz_task_list:SetDataList(target_data_list)
end

function YanYuGeWLTZTaskView:OnClickPanelBtn(index)
    self.select_panel_index = index
    self:Flush()
end

WLTZTaskListCellRender = WLTZTaskListCellRender or BaseClass(BaseRender)

function WLTZTaskListCellRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_jump, BindTool.Bind(self.OnClickJumpBtn, self))
end

function WLTZTaskListCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end
    
    self.node_list.desc_task.text.text = self.data.desc_up2
    local is_complete = YanYuGeWGData.Instance:IsExtraScoreTaskComplete(self.data.seq)
    self.node_list.btn_jump:CustomSetActive(not is_complete)
    self.node_list.flag_complete:CustomSetActive(is_complete)
end

function WLTZTaskListCellRender:OnClickJumpBtn()
    if IsEmptyTable(self.data) then
        return
    end

    if self.data.open_panel and "" ~= self.data.open_panel then
        FunOpen.Instance:OpenViewNameByCfg(self.data.open_panel)
        ViewManager.Instance:Close(GuideModuleName.YanYuGeWLTZTaskView)
    end
end