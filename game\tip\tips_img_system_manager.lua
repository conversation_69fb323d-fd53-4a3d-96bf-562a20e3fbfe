--带图片和文字的文本的提示
TipsImgSystemManager = TipsImgSystemManager or BaseClass()

function TipsImgSystemManager:__init()
	if TipsImgSystemManager.Instance ~= nil then
		error("[TipsImgSystemManager] attempt to create singleton twice!")
		return
	end
	TipsImgSystemManager.Instance = self

	self.next_time = 0.0
	self.list = {}
	self.tips_list = {}
	self.index = 1
	Runner.Instance:AddRunObj(self, 3)
end

function TipsImgSystemManager:__delete()
	self.list = {}
	for k,v in pairs(self.tips_list) do
		v:DeleteMe()
	end

	self.tips_list= {}
	self.next_time = nil

	TipsImgSystemManager.Instance = nil
	Runner.Instance:RemoveRunObj(self)
end

local tip_t = {}
local add_time = 0
local normal_time = 0.6
local time_int = normal_time
local normal_speed = 1.5
local tips_speed = normal_speed
local show_count = 0
local max_speed = 18
local min_time = 0.05
--文字图片 最多5个
--msg  = {
	-- text_list = {[1] = "",[2] = "",[3] = ""},img_list = {[1] = {bundle = "",asset = ""},
	-- need_color = {[1] = {out_line = "#264294",gradient1 = "#6debdb",gradient2 = "#ffffff"}},}
--}
function TipsImgSystemManager:ShowSystemTips(msg, speed)
	if #tip_t > 20 then --最多飘20条
		table.remove(tip_t, 1)
	end
	table.insert(tip_t, {msg, speed})

	show_count = 0
	for k,v in pairs(self.tips_list) do
		if v:SystemTipsVis() then
			show_count = show_count + 1
		end
	end

	show_count = show_count + #tip_t
	time_int = show_count > 3 and normal_time / (show_count - 3 + 1) or normal_time
	time_int = math.max(time_int, min_time)
end

function TipsImgSystemManager:RealShowSystemTips(msg, speed)
	speed = speed or 1
	if type(speed) ~= "number" then
		speed = 1
	end

	local tips_cell = self.tips_list[self.index]
	if tips_cell then
		tips_cell:CloseTips()
		tips_cell:Show(msg, speed, 0)
	else
		local canvas_transform = TipsImgSystemManager.GetTipsNumberCanvas().transform
		local obj = ResPoolMgr:TryGetGameObject("uis/view/miscpre_load_prefab", "SystemImgTipsView", canvas_transform)
		tips_cell = TipsImgSystemView.New(obj)
		self.tips_list[self.index] = tips_cell
		tips_cell:Show(msg, speed, 0)
	end

	for k,v in pairs(self.tips_list) do
		if k ~= self.index then
			v:AddIndex()
		end
	end

	self.index = self.index + 1
	if self.index > 3 then
		self.index = 1
	end
end

function TipsImgSystemManager:Update(now_time, elapse_time)
	if tip_t[1] and (now_time - add_time > time_int) then
		add_time = now_time
		show_count = 0
		for k,v in pairs(self.tips_list) do
			if v:SystemTipsVis() then
				show_count = show_count + 1
			end
		end

		show_count = show_count + #tip_t
		tips_speed = show_count > 3 and normal_speed * (show_count - 3 + 1) or normal_speed
		tips_speed = math.min(tips_speed, max_speed)
		time_int = show_count > 3 and normal_time / (show_count - 3 + 1) or normal_time
		time_int = math.max(time_int, min_time)
		local msg = table.remove(tip_t, 1)
		self:RealShowSystemTips(msg[1], tips_speed)
		for k,v in pairs(self.tips_list) do
			v:ChangeSpeed(tips_speed)
		end
	end
end

function TipsImgSystemManager.GetTipsNumberCanvas()
	if nil == TipsImgSystemManager.floating_canvas then
		local obj = ResMgr:Instantiate(SafeBaseView.GetBaseViewParentTemplate())
		obj.name = "TipsImgSystemCanvas"
		obj:SetActive(true)

		local canvas = obj:GetComponent(typeof(UnityEngine.Canvas))
		canvas.overrideSorting = true
		canvas.sortingOrder = UiLayer.PopTop * 1000 + 900
		canvas.worldCamera = UICamera

		local canvas_transform = canvas.transform
		obj:GetComponent(typeof(UnityEngine.UI.GraphicRaycaster)).enabled = false

		canvas_transform:SetParent(UILayer.transform, false)
		canvas_transform:SetLocalScale(1, 1, 1)

		local rect = canvas_transform:GetComponent(typeof(UnityEngine.RectTransform))
		rect.anchorMax = Vector2(1, 1)
		rect.anchorMin = Vector2(0, 0)
		rect.anchoredPosition3D = Vector3(0, 0, 0)
		rect.sizeDelta = Vector2(0, 0)

		TipsImgSystemManager.floating_canvas = canvas
	end

	return TipsImgSystemManager.floating_canvas
end
