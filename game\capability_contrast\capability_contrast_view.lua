CapabilityContrastView = CapabilityContrastView or BaseClass(SafeBaseView)

function CapabilityContrastView:__init()
	self:SetMaskBg(true, true)

    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/capability_contrast_ui_prefab", "layout_capability_contrast")
end

function CapabilityContrastView:__delete()

end

function CapabilityContrastView:OpenCallBack()

end

function CapabilityContrastView:CloseCallBack()
end

function CapabilityContrastView:LoadCallBack()
	self:SetSecondView(nil, self.node_list["size"])
	self.node_list.title_view_name.text.text = Language.CapabilityContrast.Title

	self.cap_total_list = {}
	self.my_head = BaseHeadCell.New(self.node_list.my_head_node)
    self.other_head = BaseHeadCell.New(self.node_list.other_head_node)

	RectTransform.SetSizeDeltaXY(self.my_head.node_list.root_cell.rect, 83, 83)
	RectTransform.SetSizeDeltaXY(self.my_head.node_list.bg.rect, 83, 83)
	RectTransform.SetSizeDeltaXY(self.other_head.node_list.bg.rect, 83, 83)
	RectTransform.SetSizeDeltaXY(self.my_head.node_list.default_head_icon.rect, 83, 83)
end

function CapabilityContrastView:ReleaseCallBack()
	if self.my_head then
		self.my_head:DeleteMe()
		self.my_head = nil
	end

    if self.other_head then
		self.other_head:DeleteMe()
		self.other_head = nil
    end

	if self.cap_total_list then
		for k, v in pairs(self.cap_total_list) do
			v:DeleteMe()
		end
		self.cap_total_list = nil
	end

end

function CapabilityContrastView:OnFlush()
	self:SetMyHeadData()
	self:SetOtherHeadData()
	self:FlushCapList()
end

function CapabilityContrastView:SetMyHeadData()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local appearance = role_vo and role_vo.appearance
	local data = {fashion_photoframe = appearance.fashion_photoframe}
	data.role_id = RoleWGData.Instance:InCrossGetOriginUid()
	data.prof = role_vo.prof
	data.sex = role_vo.sex
	data.is_show_main = true

	--self.my_head:SetImgBg(appearance and appearance.fashion_photoframe > 0)
	self.my_head:SetImgBg(false)
	self.my_head:SetData(data)
	RectTransform.SetSizeDeltaXY(self.my_head.node_list.default_head_icon.rect, 83, 83)
	RectTransform.SetSizeDeltaXY(self.my_head.node_list.custom_head_icon.rect, 76, 76)

	local is_vis, level = RoleWGData.Instance:GetDianFengLevel(role_vo.level)
	self.node_list.my_name.text.text = role_vo.name
	self.node_list.my_manji_img:SetActive(is_vis)
	self.node_list.my_level_txt.text.text = level
	
	self.node_list.my_cap.text.text = role_vo.capability
	self.node_list.my_vip_num.text.text = string.format("%s", role_vo.vip_level)
end

function CapabilityContrastView:SetOtherHeadData()
	local role_info = CapabilityContrastWGData.Instance:GetLookRoleInfo()
	if IsEmptyTable(role_info) then
		return
	end

	local data = {fashion_photoframe = role_info.fashion_photoframe}
	data.role_id = role_info.uuid.temp_low
	data.prof = role_info.prof
	data.sex = role_info.sex
	--self.other_head:SetImgBg(appearance and appearance.fashion_photoframe > 0)
	self.other_head:SetImgBg(false)
	self.other_head:SetData(data)
	RectTransform.SetSizeDeltaXY(self.other_head.node_list.default_head_icon.rect, 83, 83)
	RectTransform.SetSizeDeltaXY(self.other_head.node_list.custom_head_icon.rect, 76, 76)

	self.node_list.other_name.text.text = role_info.role_name
	local is_vis, level = RoleWGData.Instance:GetDianFengLevel(role_info.level)
	self.node_list.other_manji_img:SetActive(is_vis)
	self.node_list.other_level_txt.text.text = level
	local my_total_cap = CapabilityContrastWGData.Instance:GetMyTotalCap()
	local other_total_cap = role_info.capability

	self.node_list.other_cap.text.text = other_total_cap
	self.node_list.other_vip_num.text.text = string.format("%s", role_info.vip_level)

	self.node_list.other_win_image:SetActive(my_total_cap < other_total_cap)
	self.node_list.my_win_image:SetActive(my_total_cap > other_total_cap)
end

function CapabilityContrastView:FlushCapList()
	local list_data = CapabilityContrastWGData.Instance:GetCapShowList()
	for i, v in ipairs(list_data) do
		if self.cap_total_list[i] then
			self.cap_total_list[i]:SetData(v)
		else
			local async_loader = AllocAsyncLoader(self, "cap_com_total" .. i)
			async_loader:SetParent(self.node_list["cap_list"].transform)
			async_loader:Load("uis/view/capability_contrast_ui_prefab", "contrast_total_cell", function (obj)
				local cell = CapContrastTotalRender.New(obj)
				cell:SetData(v)
				self.cap_total_list[i] = cell
			end)
		end
	end
end

--------------------------------------------------------------------------------
CapContrastTotalRender = CapContrastTotalRender or BaseClass(BaseRender)
function CapContrastTotalRender:__init()
	self.cap_sub_list = {}

	XUI.AddClickEventListener(self.node_list.btn, BindTool.Bind(self.ClickBtn, self))
end

function CapContrastTotalRender:__delete()
	if self.cap_sub_list then
		for k, v in pairs(self.cap_sub_list) do
			v:DeleteMe()
		end
		self.cap_sub_list = nil
	end
end

function CapContrastTotalRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local jump_view = self.data.jump_view
	local is_open, limit_desc = FunOpen.Instance:GetCfgPathIsOpen(jump_view)
	self.node_list.sys_name.text.text = self.data.sys_name

	self.node_list.no_limit:SetActive(is_open)
	self.node_list.limit_desc:SetActive(not is_open)

	if not is_open then
		self.node_list.limit_desc.text.text = limit_desc
	else
		local my_cap = self.data.my_cap
		local other_cap = self.data.other_cap
		local total_cap = my_cap + other_cap
		local progress = total_cap > 0 and my_cap / total_cap or 0.5

		self.node_list.slider.slider.value = progress
		self.node_list.my_cap.text.text = my_cap
		self.node_list.other_cap.text.text = other_cap
		self.node_list.btn_text.text.text = self.data.btn_text
	end

	self.node_list.sub_list:SetActive(false)
end

function CapContrastTotalRender:ClickBtn()
	if IsEmptyTable(self.data) then
		return
	end

	local had_sub = #self.data.sub_list > 0
	if not had_sub then
		FunOpen.Instance:OpenViewNameByCfg(self.data.jump_view)
		return
	end

	if not self.node_list.sub_list:GetActive() then
		self.node_list.btn_text.text.text = Language.CapabilityContrast.BtnText
		local list_data = self.data.sub_list
		for i, v in ipairs(list_data) do
			if self.cap_sub_list[i] then
				self.cap_sub_list[i]:SetData(v)
				if i == #list_data then
					self.node_list.sub_list:SetActive(true)
				end
			else
				local async_loader = AllocAsyncLoader(self, "cap_com_sub" .. i)
				async_loader:SetParent(self.node_list["sub_list"].transform)
				async_loader:Load("uis/view/capability_contrast_ui_prefab", "contrast_sub_cell", function (obj)
					local cell = CapContrastSubRender.New(obj)
					cell:SetData(v)
					self.cap_sub_list[i] = cell
					if i == #list_data then
						self.node_list.sub_list:SetActive(true)
					end
				end)
			end
		end
	else
		self.node_list.btn_text.text.text = self.data.btn_text
		self.node_list.sub_list:SetActive(false)
	end
end

--------------------------------------------------------------------------------
CapContrastSubRender = CapContrastSubRender or BaseClass(BaseRender)
function CapContrastSubRender:__init()
	XUI.AddClickEventListener(self.node_list.btn, BindTool.Bind(self.ClickBtn, self))
end

function CapContrastSubRender:__delete()

end

function CapContrastSubRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local jump_view = self.data.jump_view
	local is_open, limit_desc = FunOpen.Instance:GetCfgPathIsOpen(jump_view)
	self.node_list.sys_name.text.text = self.data.sys_name

	self.node_list.no_limit:SetActive(is_open)
	self.node_list.limit_desc:SetActive(not is_open)

	if not is_open then
		self.node_list.limit_desc.text.text = limit_desc
	else
		local my_cap = self.data.my_cap
		local other_cap = self.data.other_cap
		local total_cap = my_cap + other_cap
		local progress = total_cap > 0 and my_cap / total_cap or 0.5

		self.node_list.slider.slider.value = progress
		self.node_list.my_cap.text.text = my_cap
		self.node_list.other_cap.text.text = other_cap
		self.node_list.btn_text.text.text = self.data.btn_text
	end
end

function CapContrastSubRender:ClickBtn()
	if IsEmptyTable(self.data) then
		return
	end

	-- 适配策划说的未穿戴装备时 弹出tips 2023/11/23
	if self.data.main_index == 2 and (self.data.child_index == 2 or self.data.child_index == 3 or self.data.child_index == 4) then
		local data_list = EquipWGData.Instance:GetDataList()
		if IsEmptyTable(data_list) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NeedWearEquip)
			return
		end
	end

	FunOpen.Instance:OpenViewNameByCfg(self.data.jump_view)
end
