
BossAssistListView = BossAssistListView or BaseClass(SafeBaseView)
function BossAssistListView:__init()
	self.default_index = 1
	self.active_close = false

	self:AddViewResource(0, "uis/view/boss_assist_ui_prefab", "layout_assist_list")
	self.view_layer = UiLayer.MainUILow
	self.open_tween = nil
	self.close_tween = nil
end

function BossAssistListView:__delete()
end

function BossAssistListView:ReleaseCallBack()
	if self.role_list then
		self.role_list:DeleteMe()
		self.role_list = nil
	end

	if self.move_event then
		GlobalEventSystem:UnBind(self.move_event)
		self.move_event = nil
	end
end

function BossAssistListView:LoadCallBack(index, loaded_times)
	if loaded_times <= 1 then
		self.role_list = AsyncListView.New(AssistRoleListRender, self.node_list.role_list)
		self.role_list:JumpToTop()

		XUI.AddClickEventListener(self.node_list.btn_reward, BindTool.Bind(self.RewardClick, self))
		XUI.AddClickEventListener(self.node_list.btn_assist, BindTool.Bind(self.OnClickAssist, self))
		XUI.AddClickEventListener(self.node_list.btn_Cancel, BindTool.Bind(self.CancelClick, self))
		self.move_event = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind1(self.ShowPanel, self))
	end
end

function BossAssistListView:RewardClick()
	ViewManager.Instance:Open(GuideModuleName.BossAssistHelp)
end

function BossAssistListView:CancelClick()
	self:Close()
	if BossAssistWGData.Instance:IsAssist() then
		BossAssistWGCtrl.SendGuildAssistCancelHelp()
	end
end

function BossAssistListView:ShowPanel(is_on)
	self.node_list.panel:SetActive(not is_on)
end

function BossAssistListView:ShowIndexCallBack(index)
	self:Flush(index)
end

function BossAssistListView:OpenCallBack()

end

function BossAssistListView:CloseCallBack()
	
end

function BossAssistListView:OnClickAssist()
	local assist_info = BossAssistWGData.Instance:GetAssistInfo()
	GuajiWGCtrl.Instance:StopGuaji()
	MoveCache.SetEndType(MoveEndType.FightByMonsterId)
	GuajiCache.monster_id = assist_info.target_boss_id
	MoveCache.param1 = assist_info.target_boss_id
	local range = BossWGData.Instance:GetMonsterRangeByid(assist_info.target_boss_id)

	GuajiWGCtrl.Instance:MoveToPos(assist_info.target_boss_scene_id, assist_info.target_boss_pos_x, assist_info.target_boss_pos_y, range)
end

function BossAssistListView:OnFlush(param_t, index)
	local assist_info = BossAssistWGData.Instance:GetAssistInfo()
	local is_assist = assist_info.assist_role_id > 0
	self.node_list.list_view:SetActive(not is_assist)
	self.node_list.state_view:SetActive(is_assist)
	if is_assist then
		--self.node_list.name.text.text = assist_info.assist_role_name
		local cur_num = RoleWGData.Instance.role_vo.day_assist_shengwang
		self.node_list["txt_num"].text.text = ToColorStr(cur_num, COLOR3B.D_GREEN) .. "/" .. BossAssistView.MAX_DAY_ASSIST_SHENGWANG
			
		local t = {}
		t.role_id = assist_info.assist_role_id
		t.prof = assist_info.assist_role_prof
		t.fashion_photoframe = assist_info.fashion_photoframe
	else
		self.role_list:SetDataList(BossAssistWGData.Instance:GetGuildAssistRoleAssistDamageList())
	end
end


----------------------------------------------------------------------------
-- AssistRoleListRender
----------------------------------------------------------------------------
AssistRoleListRender = AssistRoleListRender or BaseClass(BaseRender)
function AssistRoleListRender:__init()

end

function AssistRoleListRender:__delete()

end

function AssistRoleListRender:LoadCallBack()

end

function AssistRoleListRender:OnFlush()
	self.node_list.name.text.text = self.data.role_name
	self.node_list.damage.text.text = CommonDataManager.ConverNumber(self.data.damage_value)
	if self.index > 3 then
		self.node_list.num.text.text = self.index
		self.node_list.num_icon:SetActive(false)
	else
		self.node_list.num.text.text = ""
		self.node_list.num_icon:SetActive(true)
		self.node_list.num_icon.image:LoadSprite(ResPath.GetCommonImages("icon_paiming" .. self.index))
	end
	--self.node_list.per_bg.image.fillAmount = self.data.damage_value / BossAssistWGData.Instance:GetGuildAssistRoleAssistDamageMax()
end

function AssistRoleListRender:AddFriend()

end