function WorldTreasureView:InitMWView()
	self.mowang_grade_change = true
	self.drop_reward_item_list = {}

	XUI.AddClickEventListener(self.node_list.btn_goto_boss, BindTool.Bind(self.OnClickBtnGotoBoss,self))
	XUI.AddClickEventListener(self.node_list.btn_goto_treasure, BindTool.Bind(self.OnClickBtnGotoTreasure,self))

	local desc_cfg = WorldTreasureWGData.Instance:GetClientDesc(TabIndex.tcdb_mowang)
	if  not IsEmptyTable(desc_cfg) then
		self.node_list.mw_tip_label.text.text = desc_cfg.rule_desc
	end

	if nil == self.mw_display_model then
		local display_node = self.node_list.mw_display
		self.mw_display_model = RoleModel.New()
        local display_data = {
			parent_node = display_node,
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			-- can_drag = true,
		}

		self.mw_display_model:SetRenderTexUI3DModel(display_data)
		self:AddUiRoleModel(self.mw_display_model)
	end

	-- self:MWTimeCountDown()
	self:FlushMWView()
end

function WorldTreasureView:LoadMWVImg()
	local bundle, asset = ResPath.GetWorldTreasureRawImages("xtyg_bt_3")
	self.node_list.mw_img_title.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.mw_img_title.raw_image:SetNativeSize()
	end)

    bundle, asset = ResPath.GetWorldTreasureRawImages("xtyg_btd_1")
	self.node_list.mw_desc_bg.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.mw_desc_bg.raw_image:SetNativeSize()
	end)

	bundle, asset = ResPath.GetWorldTreasureRawImages("jsrh_bg_2")
	self.node_list.mw_bg.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.mw_bg.raw_image:SetNativeSize()
	end)

	local cur_grade = WorldTreasureWGData.Instance:GetTreasureCurGrade()
    self.node_list.goto_boss_text.text.color = StrToColor(Language.WorldTreasure.GradeColorList1[cur_grade])
	self.node_list.goto_treasure_text.text.color = StrToColor(Language.WorldTreasure.GradeColorList1[cur_grade])
	self.node_list.mw_line1.image.color = StrToColor(Language.WorldTreasure.GradeColorList1[cur_grade])
	self.node_list.mw_line2.image.color = StrToColor(Language.WorldTreasure.GradeColorList1[cur_grade])
end

function WorldTreasureView:ReleaseMWView()
	if self.mw_grid then
		self.mw_grid:DeleteMe()
		self.mw_grid = nil
	end

	-- 销毁模型
	if self.mw_display_model then
		self.mw_display_model:DeleteMe()
		self.mw_display_model = nil
	end

	if self.drop_reward_item_list then
		for _,v in pairs(self.drop_reward_item_list) do
			v:DeleteMe()
		end
		self.drop_reward_item_list = nil
	end

	CountDownManager.Instance:RemoveCountDown("tianshenroad_mowang_count_down")
end

function WorldTreasureView:ShowMWView()
	WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_SPECIAL_DROP_INFO)
	self:FlushMWModel()
	-- self:DoTRMWAnim()
end

function  WorldTreasureView:FlushMWModel()
	local cfg = WorldTreasureWGData.Instance:GetExtraDropModelCfg()
	if not IsEmptyTable(cfg) then
		self.mw_display_model:SetMainAsset(cfg.model_bundle_name, cfg.model_asset_name)

		if cfg.whole_display_pos and cfg.whole_display_pos ~= "" then
			local pos = Split(cfg.whole_display_pos, "|")
			RectTransform.SetAnchoredPosition3DXYZ(self.node_list.mw_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end

		if cfg.model_rot and cfg.model_rot ~= "" then
			local rot = Split(cfg.model_rot, "|")
			self.mw_display_model:SetRTAdjustmentRootLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
		end
		
		if cfg.model_scale and cfg.model_scale ~= "" then
			self.mw_display_model:SetRTAdjustmentRootLocalScale(cfg.model_scale)
		end
	end
end

function WorldTreasureView:FlushMWView(param_t, index)
	if self.mowang_grade_change then
        self.mowang_grade_change = false
        self:LoadMWVImg()
    end

	local desc_cfg = WorldTreasureWGData.Instance:GetClientDesc(TabIndex.tcdb_mowang)
	if  not IsEmptyTable(desc_cfg) then
		self.node_list.mw_tip_label.text.text = desc_cfg.rule_desc
	end
	self:FlushMWDropRewardList()
end

function WorldTreasureView:FlushMWTaskList()
	local task_list = TianshenRoadWGData.Instance:GetMWTaskList()
	if task_list ~= nil then
		if self.mw_grid == nil then
			local bundle = "uis/view/tianshenroad_ui_prefab"
			local asset = "mw_item"
			self.mw_grid = AsyncBaseGrid.New()
			self.mw_grid:SetStartZeroIndex(false)
			self.mw_grid:CreateCells({col = 2, cell_count = #task_list, list_view = self.node_list["mw_list"],
						assetBundle = bundle, assetName = asset, itemRender = MWTaskItemRender})
			self.mw_grid:SetDataList(task_list, 3)
		else
			self.mw_grid:SetDataList(task_list, 2)
		end
	end
end

function WorldTreasureView:FlushMWDropRewardList()

	local data_list = WorldTreasureWGData.Instance:GetExtraDropCountList()
	local item_list = self.drop_reward_item_list

	if #item_list < #data_list then
		local item_root = self.node_list.mw_special_reward
		for i = #item_list + 1, #data_list do
			item_list[i] = MWDropRewardItem.New(item_root)
		end
		self.drop_reward_item_list = item_list
	end

	for i=1,#item_list do
		item_list[i]:SetData(data_list[i])
	end
end

function WorldTreasureView:OnClickBtnGotoBoss()
	ViewManager.Instance:Open(GuideModuleName.Boss)
end

function WorldTreasureView:OnClickBtnGotoTreasure()
	-- ViewManager.Instance:Open(GuideModuleName.TreasureHunt,TabIndex.treasurehunt_equip)
	local grade_cfg = WorldTreasureWGData.Instance:GetCurGradeCfg()
	FunOpen.Instance:OpenViewNameByCfg(grade_cfg.drop_open_panel_name)
end

--有效时间倒计时
-- function WorldTreasureView:MWTimeCountDown()
-- 	CountDownManager.Instance:RemoveCountDown("tianshenroad_mowang_count_down")
-- 	local invalid_time = TianshenRoadWGData.Instance:GetActivityInValidTime(TabIndex.tianshenroad_mowang)
-- 	local server_time = TimeWGCtrl.Instance:GetServerTime()
-- 	if invalid_time > server_time then
-- 		self.node_list.mw_time_label.text.text = TimeUtil.FormatSecondDHM2(invalid_time - server_time)
-- 		CountDownManager.Instance:AddCountDown("tianshenroad_mowang_count_down", BindTool.Bind1(self.UpdateMWCountDown, self), BindTool.Bind1(self.MWTimeCountDown, self), invalid_time, nil, 1)
-- 	else
-- 		self.node_list.mw_time_label.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
-- 		self.node_list.mw_time_label.text.color = Str2C3b(COLOR3B.RED)
-- 	end
-- end

-- function WorldTreasureView:UpdateMWCountDown(elapse_time, total_time)
-- 	local valid_time = total_time - elapse_time
-- 	if valid_time > 0 then
-- 		self.node_list.mw_time_label.text.text = TimeUtil.FormatSecondDHM2(valid_time)
-- 	end
-- end

function WorldTreasureView:DoTRMWAnim()
	local tween_info = UITween_CONSTS.TianshenRoadView
    UITween.FakeHideShow(self.node_list["ts_mowan_root"])

    UITween.AlphaShow(GuideModuleName.WorldTreasureView, self.node_list["ts_mowan_root"], 0, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)

end