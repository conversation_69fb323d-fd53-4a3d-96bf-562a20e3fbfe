
--------------------------------------------------
function RechargeView:InitRechargeWeekBuyView()
	RechargeWGCtrl.Instance:CSPeriodOperaReq(PERIOD_OP_TYPE.PERIOD_OP_TYPE_ALL_INFO, 0)
	RechargeWGData.Instance:SetWeekBuyRedPointMark()
	
	if self.recharge_gridscroll_week_buy == nil then
		local cell_counts = 8
		self.recharge_gridscroll_week_buy = AsyncBaseGrid.New()
		local bundle = "uis/view/recharge_ui_prefab"
		local asset = "ph_recharge_week_buy_item"
		self.recharge_gridscroll_week_buy:CreateCells({col = 4, cell_count = cell_counts, list_view = self.node_list.ph_recharge_gridscrl_week_buy,
				assetBundle = bundle, assetName = asset, itemRender = RechargeWeekBuyItem})
		self.recharge_gridscroll_week_buy:SetStartZeroIndex(false)
		self.recharge_gridscroll_week_buy:SetSelectCallBack(BindTool.Bind(self.ClickRechargeWeekBuyItemHandler, self))
	end
	XUI.AddClickEventListener(self.node_list.week_buy_btn, BindTool.Bind1(self.OnClickWeekBuy, self))
end

function RechargeView:DeleteRechargeWeekBuyView()
	if self.node_list and self.node_list.week_buy_btn then
		XUI.SetButtonEnabled(self.node_list.week_buy_btn, true)
	end

	if self.recharge_gridscroll_week_buy then
		self.recharge_gridscroll_week_buy:DeleteMe()
		self.recharge_gridscroll_week_buy = nil
	end

	if self.alert_view then
		self.alert_view:DeleteMe()
		self.alert_view = nil
	end
end
function RechargeView:OnClickWeekBuy()
	RechargeWGCtrl.Instance:CSPeriodOperaReq(PERIOD_OP_TYPE.PERIOD_OP_TYPE_BUY,0)
end
function RechargeView:OnFlushRechargeWeekBuyView()
	local data_list = RechargeWGData.Instance:GetBaseInfoWeekBuy()
	if nil == data_list or IsEmptyTable(data_list) then 
		return 
	end

	local flag_info = RechargeWGData.Instance:GetWeekBuyInfo()
	if IsEmptyTable(flag_info) or (flag_info.cur_day and flag_info.cur_day <= 0)then
		self.node_list.btn_text_num:SetActive(true)
		self.node_list.btn_text_num_1:SetActive(false)
		XUI.SetButtonEnabled(self.node_list.week_buy_btn, true)
        self.node_list.week_buy_effect:SetActive(true)
	else
		self.node_list.btn_text_num:SetActive(false)
		self.node_list.btn_text_num_1:SetActive(true)
		XUI.SetGraphicGrey(self.node_list.btn_text_num_1, true)
		XUI.SetButtonEnabled(self.node_list.week_buy_btn, false)    
        self.node_list.week_buy_effect:SetActive(false)
	end

	--self.node_list.btn_text_num.text.text = data_list[1].buy_gold ..Language.Recharge.WeekBuyBuy
	-- local num = 0
	-- for k,v in pairs(data_list) do
	-- 	if k > 1 then
	-- 		num = num + v.reward_bind_gold
	-- 	end
	-- end
	--self.node_list.fanhuan_num_1.text.text = num
	if nil ~= self.recharge_gridscroll_week_buy then
		self.recharge_gridscroll_week_buy:SetDataList(data_list, 0)
	end
end


function RechargeView:ClickRechargeWeekBuyItemHandler(item)
	local flag_info = RechargeWGData.Instance:GetWeekBuyInfo()
	if IsEmptyTable(flag_info) then
		return
	end

	if item.index == 1 then
		RechargeWGCtrl.Instance:CSPeriodOperaReq(PERIOD_OP_TYPE.PERIOD_OP_TYPE_FETCH_EXTRA_REWARD,0)
	else
		RechargeWGCtrl.Instance:CSPeriodOperaReq(PERIOD_OP_TYPE.PERIOD_OP_TYPE_FETCH_REWARD,item.index - 1)
	end
	
end

--------------------------------------------------
--RechargeWeekBuyItem
--------------------------------------------------
RechargeWeekBuyItem = RechargeWeekBuyItem or BaseClass(BaseGridRender)
function RechargeWeekBuyItem:__init()

end

function RechargeWeekBuyItem:__delete()
	if self.node_list and self.node_list.ph_recharge_week_buy_item then
		XUI.SetGraphicGrey(self.node_list.ph_recharge_week_buy_item, false)
	end
end

function RechargeWeekBuyItem:ReleaseCallBack()

end

function RechargeWeekBuyItem:OnFlush()
	if nil == self.data then 
		return 
	end

	local is_ani = false
	XUI.SetButtonEnabled(self.node_list.item_group, true)
	self.node_list.lbl_money.text.text = self.data.reward_bind_gold..Language.Recharge.BangDingXianYu
	self.node_list.buy_get_cash:SetActive(self.index == 1 )
	self.node_list.buy_get_cashtext:SetActive(self.index ~= 1 )
	self.node_list.img_autoname_bg:SetActive(self.index == 1 )
	self.node_list.img_autoname_bg1:SetActive(self.index ~= 1 )
	self.node_list.img_gold_icon_week_buy:SetActive(self.index == 1 )
	self.node_list.img_gold_icon_week_buy1:SetActive(self.index ~= 1 )


	self.node_list.buy_get_cashtext.text.text = string.format(Language.Recharge.DayLingQu,Language.Common.UpNum[self.index - 1]) 
	
	local flag_info = RechargeWGData.Instance:GetWeekBuyInfo()
	if IsEmptyTable(flag_info) or (flag_info.cur_day and flag_info.cur_day <= 0)then
		--self:SetAni(false)
		self.node_list.buy_get_is_over.text.text = Language.Recharge.NoReach
		return
	end

	local flag = bit:d2b(flag_info.fetch_flag)
	if flag_info.cur_day + 1 < self.index then
		self.node_list.buy_get_is_over.text.text = Language.Recharge.NoReach
		is_ani = false
	else
		if flag[33 - self.index] == 0 then
			self.node_list.buy_get_is_over.text.text = Language.Recharge.CanLingQu1
			is_ani = true
		else
			self.node_list.buy_get_is_over.text.text = Language.Recharge.NoCanLingQu
			XUI.SetButtonEnabled(self.node_list.item_group, false)
			is_ani = false
		end
	end
	--self:SetAni(is_ani)
end

function RechargeWeekBuyItem:SetAni(vas)
	GlobalTimerQuest:AddDelayTimer(function ()
		self.node_list.img_gold_icon_week_buy.animator:SetBool("is_shake", vas)
	end, 0.2)
end

function RechargeWeekBuyItem:OnClickLingQu()
end

-- 重写CreateSelectEffect，为了去掉那选中的边框
function RechargeWeekBuyItem:CreateSelectEffect()
end

