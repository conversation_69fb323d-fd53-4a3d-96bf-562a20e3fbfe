
ServerBanBenActivityView = ServerBanBenActivityView or BaseClass(SafeBaseView)

ServerBanBenActivityView.ActGobalXunbaoView_Flag = false --特殊处理

function ServerBanBenActivityView:__init()
	self.view_style = ViewStyle.Full
	self:SetMaskBg(false,false)
	--self.order = 1
	self:AddViewResource(0, "uis/view/act_operate_ui_prefab", "layout_open_activity_panel2")
	self.btns_change_flag = true
	self.sub_view_list = {}
	self.select_index = 1
	self.is_had_click_btn = {}
	--self.order = 1
	self.roledata_change_callback = BindTool.Bind1(self.RoleDataChangeCallBack, self)
end

function ServerBanBenActivityView:__delete()

end

function ServerBanBenActivityView:ReleaseCallBack()
	if nil ~= self.btn_list then
		self.btn_list:DeleteMe()
		self.btn_list = nil
	end

	if nil ~= self.sub_view_list then
		for k, v in pairs(self.sub_view_list) do
			v:DeleteMe()
		end
	end
	self.sub_view_list = {}
	self.cur_subview = nil
	self.btns_change_flag = true
	self.select_index = 1

	CountDownManager.Instance:RemoveCountDown("version_act_status")

	self.has_load_callback = nil
	self.is_should_change_to_view = nil
	self.change_to_view_data = nil
end

function ServerBanBenActivityView:LoadCallBack()
	self.node_list.title.image:LoadSprite(ResPath.GetOpenserverActiveIcon('time_activity'))
	self.btn_list = AsyncListView.New(VersionBtnItemBanBen,self.node_list.ph_btn_select_list)
	self.btn_list:SetSelectCallBack(BindTool.Bind1(self.OnOpenViewType, self))
	XUI.AddClickEventListener(self.node_list.btn_close_window,BindTool.Bind(self.Close,self))
	self.has_load_callback = true

	if self.is_should_change_to_view == true then
		self.is_should_change_to_view = nil
		self:SetChangeToView(self.change_to_view_data.open_type , self.change_to_view_data.return_call_back)
		self.change_to_view_data = nil
	end
end

function ServerBanBenActivityView:ShowIndexCallBack(index)
	-- if self.cur_subview then
	-- 	self.cur_subview:PlayTween()
	-- 	self.cur_subview:Open()
	-- end
	if self.btns_change_flag then
		self:UpdateBtnList()
	end
	RoleWGData.Instance:NotifyAttrChange(self.roledata_change_callback, {"vip_level", "level"})
	self:BtnListOpenHandler()
end

function ServerBanBenActivityView:OpenCallBack()

	ServerActivityWGCtrl.Instance:SendWeekendBossReq()
	if self.cur_subview then
		self.cur_subview:PlayTween()
		self.cur_subview:Open()
	end
end

-- 按钮列表打开面板处理
function ServerBanBenActivityView:BtnListOpenHandler()
	local open_act_cfg = nil
	local open_act_list = ServerActivityWGData.Instance:GetShowOpenActList(true)
	local read_act_cfg = {}
	read_act_cfg = self:GetBanBenOpenList(open_act_list)
	if nil == read_act_cfg[self.cur_btn_index] then
		return
	else
		open_act_cfg = read_act_cfg[self.cur_btn_index]
	end
	local act_id = open_act_cfg.id

	if act_id == ServerActClientId.DISCOUNT_ONSELL1 then
		-- if not ActDiscountData.Instance:GetDishasOpen() then
		-- 	ActDiscountData.Instance:SetDishasOpen(true)
		-- end
	end
end

function ServerBanBenActivityView:RoleDataChangeCallBack(key, value)
	if key == "level" then
		self:RefreshView()
	end
	if key == "vip_level" then
		self:RefreshView()
	end
end

function ServerBanBenActivityView:CloseCallBack()
	RoleWGData.Instance:UnNotifyAttrChange(self.roledata_change_callback)

	ServerActivityWGData.Instance:CleanUpFinishAct()
	ServerActivityWGData.Instance:CleanActCloseType()

	if self.cur_subview then
		self.cur_subview:Close()
	end
	self.has_open_callback = false
end

function ServerBanBenActivityView:UpdateBtnList()
	if not self:IsOpen() then
		self.btns_change_flag = true
		return
	else
		self.btns_change_flag = false
	end

	local is_close = self:CreateBtnList()
	if is_close then
		return
	end
end

function ServerBanBenActivityView:CreateBtnList()
	if self.node_list.ph_btn_select_list == nil then return end
	-- local ph_btn = self.node_list.ph_btn_select_list
	local read_act_cfg = self:GetOpenActList()
	if next(read_act_cfg) then
		self.btn_list:SetDataList(read_act_cfg,3)
		self.btn_list:SelectIndex(self.select_index)
		return false
	else
		self:Close()
		return true
	end
end

function ServerBanBenActivityView:SetChangeToView(open_type,return_call_back)
	if not self.has_load_callback then
		self.is_should_change_to_view = true
		self.change_to_view_data = {open_type = open_type, return_call_back = return_call_back}
		return
	end
	self:ChangeToView(open_type,return_call_back)
end

function ServerBanBenActivityView:ChangeToView(open_type,return_call_back)
	local read_act_cfg = self:GetOpenActList()
	local index = -1
	for k,v in pairs(read_act_cfg) do
		if v.open_type == open_type then
			index = k
			break
		end
	end
	if index == -1 then
		-- print_error("活动还未开始")
		if return_call_back ~= nil and type(return_call_back)== "function" then
			return_call_back()
		end
		return
	end
	local len = index / #read_act_cfg--( 1 / (#read_act_cfg -7))
	self.btn_list:ReloadData(len)
	self:SelectIndexTab(index)
end
function ServerBanBenActivityView:SelectIndexTab(default_index, count)
	count = count or 5
	if count < 0 then
		return
	end
	if self.btn_list:GetItemAt(default_index) == nil then
	self.close_timer_quest12 = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.SelectIndexTab, self,default_index, count - 1), 0.05)
	end
	self.btn_list:SelectIndex(default_index)
end

function ServerBanBenActivityView:GetOpenActList()
	local read_act_cfg = {}
	local open_act_cfg = ServerActivityWGData.Instance:GetShowOpenActList(true, false)
	read_act_cfg = self:GetBanBenOpenList(open_act_cfg)
	return read_act_cfg
end

function ServerBanBenActivityView:SelectHandler(index)
	if self.btn_list then
		self.btn_list:SelectIndex(index)
	else
		self.select_index = index
	end
end

function ServerBanBenActivityView:GetBanBenOpenList(list)
	local read_act_cfg = {}

	for k,v in pairs(list) do
		local act = ActivityWGData.Instance:GetActivityStatuByType(v.open_type)
		if act ~= nil then
			if nil ~= act.open_type and act.open_type == RAND_ACTIVITY_OPEN_TYPE.RAND_ACTIVITY_OPEN_TYPE_VERSION and
				act.status ~= ACTIVITY_STATUS.CLOSE then
				table.insert(read_act_cfg,v)
			end
		end
	end

	return read_act_cfg
end

function ServerBanBenActivityView:OnOpenViewType(data)
	if self.node_list.ph_btn_select_list == nil then return end
	self.cur_btn_index = data.index
	local item = self.btn_list:GetItemAt( self.btn_list:GetSelectIndex())
	if item == nil then return end
	local data = item:GetData()
	local act_id = data.id
	local view_type = data.view_type
	-- print_error(act_id)
	self.is_had_click_btn[act_id] = true

	if self.cur_subview ~= self.sub_view_list[act_id] then
		self.cur_subview:Close()
	end


	self.sub_view_list[act_id] = self.sub_view_list[act_id] or ActivitySubViewCfg[view_type].New(act_id)

	local need_play_tween = false
	if self.cur_subview == nil then
		need_play_tween = true
	end

	self.cur_subview = self.sub_view_list[act_id]
	if need_play_tween then
		self.cur_subview:PlayTween()
	end

	self.cur_subview:Open()

	if nil ~= self.cur_subview.FlushFlagInfo then
		self.cur_subview:FlushFlagInfo()
	end

	-- self:RefreshSmallEle(act_id)
	-- --随机活动重新拉数据
	if "number" == type(data.get_type) and data.get_type > 2048 then
		ServerActivityWGCtrl.Instance:SendRandActivityOperaReq({rand_activity_type = data.get_type, opera_type = RendActOperaType.QUERY_INFO})
	end

	self:RefreshButtonRemind()
end

function ServerBanBenActivityView:GetCurView()
	return self.cur_subview
end

function ServerBanBenActivityView:RefreshView(param_t, index)
	if nil == self.cur_subview then return end
	if type(param_t) ~= "table" then
		if nil ~= self.cur_subview then
			self.cur_subview:RefreshView(param_t)
		end
		return
	end
	for k, v in pairs(param_t) do
		if "act_info" == k then
			if self.cur_subview == self.sub_view_list[v.act_id] then
				self.cur_subview:RefreshView(param_t)
				-- self:RefreshSmallEle(v.act_id)
			end
		end
	end
end

function ServerBanBenActivityView:RefreshButtonRemind()
	if nil == self.btn_list then
		return
	end
	local read_act_cfg = self:GetOpenActList()
	if read_act_cfg == nil then return end
	self.node_list.ph_btn_select_list.scroller:RefreshActiveCellViews()
end

function ServerBanBenActivityView:GetActCfgByActType(act_type)
	local client_act_cfg = ServerActivityWGData.Instance:GetClientActList()
	for k, v in pairs(client_act_cfg) do
		if v.open_type == act_type then
			return v
		end
	end
	return nil
end

-----------------------------------------------------------------------
VersionBtnItem = VersionBtnItem or BaseClass(BaseRender)
function VersionBtnItem:__init()
	self.activity_name = ""
	self.node_list.img_red.image.enabled = false
end

function VersionBtnItem:__delete()
	self.activity_name = nil
end

function VersionBtnItem:ChangeOpenFlag()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local is_open = ServerActivityWGData.Instance:IsOpenningSprintActTwo(self.data.id)
	self.node_list["open_flag"]:SetActive(true)
	local bundle,asset
	if is_open then
		local str = os.date("*t", TimeWGCtrl.Instance:GetServerTime())
		if str.hour >= 23 and self.index == open_day then
			bundle,asset = ResPath.GetOpenserverActiveIcon("has_open")
		else
			bundle,asset = ResPath.GetOpenserverActiveIcon("openning")
		end
		self.activity_name = (self.data.act_name)
	else
		local str = self.data.order < open_day and "has_open" or "no_open"
		open_day = open_day == 2 and 1 or open_day

		if self.data.order - open_day > 1 then
			-- self.activity_name = Language.Common.WillOpen
			self.activity_name = (self.data.act_name)
			XUI.SetButtonEnabled(self.view, true)
		else
			XUI.SetButtonEnabled(self.view, true)
			bundle,asset = ResPath.GetOpenserverActiveIcon(str)
			self.activity_name = (self.data.act_name)
		end
	end
	if bundle then
		self.node_list["open_flag"]:SetActive(true)
		self.node_list["open_flag"].image:LoadSprite(bundle,asset)
	else
		self.node_list["open_flag"]:SetActive(false)
	end
end

function VersionBtnItem:OnFlush()
	if nil == self.data then return end
	-- if self.data.reason ~= nil then
	-- 	self:SetReason(self.data.reason)
	-- 	self.activity_name = (self.data.reason)
	-- 	self:OnSelectChange(false)
	-- 	return
	-- end

	self:ChangeOpenFlag()
	self:OnSelectChange(self:IsSelectIndex())
end

function VersionBtnItem:OnSelectChange(is_select)
	if is_select then
		self.node_list.img_hl:SetActive(true)
	else
		self.node_list.img_hl:SetActive(false)
	end
	self.node_list.lbl_activity_name.text.text = self.activity_name
end

function VersionBtnItem:SetReason(reason)
	self.reason = reason
end

function VersionBtnItem:GetReason()
	return self.reason
end

VersionBtnOpenItem = VersionBtnOpenItem or BaseClass(VersionBtnItem)

function VersionBtnOpenItem:OnFlush()
	if nil == self.data then return end

	self:ChangeOpenFlag()
	self.activity_name = (self.data.act_name)
	self:OnSelectChange(self:IsSelectIndex())
end


function VersionBtnOpenItem:OnSelectChange(is_select)
	self.node_list.img_red.image.enabled = ServerActivityWGData.Instance:GetOpenServerRedPoint(self.data.id,is_select)
	if is_select then
		self.node_list.img_hl:SetActive(true)
	else
		self.node_list.img_hl:SetActive(false)
	end
	self.node_list.lbl_activity_name.text.text = self.activity_name
end

function VersionBtnOpenItem:ChangeOpenFlag()
	self.node_list["open_flag"]:SetActive(false)
end

-----------------------------------------------------------------------
VersionBtnItemBanBen = VersionBtnItemBanBen or BaseClass(BaseRender)
function VersionBtnItemBanBen:__init()
	self.activity_name = ""
end

function VersionBtnItemBanBen:__delete()
	self.activity_name = nil
end

function VersionBtnItemBanBen:OnFlush()
	if nil == self.data then return end

	self.node_list.open_flag:SetActive(false)
	if self.data.reason ~= nil then
		self:SetReason(self.data.reason)
		self.activity_name = (self.data.reason)
		self:OnSelectChange(false)
		return
	end

	self:OnSelectChange(self:IsSelect())
	self.activity_name = (self.data.act_name)
end

function VersionBtnItemBanBen:OnSelectChange(is_select)
	if self.data and self.data.remind_id then
		self.node_list.img_red:SetActive(ServerActivityWGData.Instance:GetActBenActRemind(self.data.remind_id) == 1)
	end
	if is_select then
		self.node_list.img_hl:SetActive(true)
	else
		self.node_list.img_hl:SetActive(false)
	end
	self.node_list.lbl_activity_name.text.text = self.activity_name
end

function VersionBtnItemBanBen:CreateSelectEffect()

end

function VersionBtnItemBanBen:SetRemindNum(num)

end

function VersionBtnItemBanBen:SetReason(reason)
	self.reason = reason
end

function VersionBtnItemBanBen:GetReason()
	return self.reason
end