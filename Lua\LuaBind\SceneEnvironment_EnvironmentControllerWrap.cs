﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class SceneEnvironment_EnvironmentControllerWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(SceneEnvironment.EnvironmentController), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("Execute", Execute);
		<PERSON><PERSON>unction("__eq", op_Equality);
		<PERSON><PERSON>Function("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>ar("layer", get_layer, set_layer);
		<PERSON><PERSON>ar("priority", get_priority, set_priority);
		<PERSON><PERSON>("sharedEnvironmentData", get_sharedEnvironmentData, set_sharedEnvironmentData);
		<PERSON><PERSON>("environmentData", get_environmentData, set_environmentData);
		<PERSON><PERSON>("sharedCharacterData", get_sharedCharacterData, set_sharedCharacterData);
		<PERSON><PERSON>("characterData", get_characterData, set_characterData);
		<PERSON><PERSON>();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Execute(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			SceneEnvironment.EnvironmentController obj = (SceneEnvironment.EnvironmentController)ToLua.CheckObject<SceneEnvironment.EnvironmentController>(L, 1);
			UnityEngine.Rendering.ScriptableRenderContext arg0 = StackTraits<UnityEngine.Rendering.ScriptableRenderContext>.Check(L, 2);
			bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
			bool arg2 = LuaDLL.luaL_checkboolean(L, 4);
			obj.Execute(arg0, ref arg1, ref arg2);
			LuaDLL.lua_pushboolean(L, arg1);
			LuaDLL.lua_pushboolean(L, arg2);
			return 2;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_layer(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentController obj = (SceneEnvironment.EnvironmentController)o;
			SceneEnvironment.EnvironmentLayer ret = obj.layer;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index layer on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_priority(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentController obj = (SceneEnvironment.EnvironmentController)o;
			int ret = obj.priority;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index priority on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_sharedEnvironmentData(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentController obj = (SceneEnvironment.EnvironmentController)o;
			SceneEnvironment.EnvironmentData ret = obj.sharedEnvironmentData;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index sharedEnvironmentData on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_environmentData(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentController obj = (SceneEnvironment.EnvironmentController)o;
			SceneEnvironment.EnvironmentData ret = obj.environmentData;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index environmentData on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_sharedCharacterData(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentController obj = (SceneEnvironment.EnvironmentController)o;
			SceneEnvironment.CharacterData ret = obj.sharedCharacterData;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index sharedCharacterData on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_characterData(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentController obj = (SceneEnvironment.EnvironmentController)o;
			SceneEnvironment.CharacterData ret = obj.characterData;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index characterData on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_layer(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentController obj = (SceneEnvironment.EnvironmentController)o;
			SceneEnvironment.EnvironmentLayer arg0 = (SceneEnvironment.EnvironmentLayer)ToLua.CheckObject(L, 2, typeof(SceneEnvironment.EnvironmentLayer));
			obj.layer = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index layer on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_priority(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentController obj = (SceneEnvironment.EnvironmentController)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.priority = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index priority on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_sharedEnvironmentData(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentController obj = (SceneEnvironment.EnvironmentController)o;
			SceneEnvironment.EnvironmentData arg0 = (SceneEnvironment.EnvironmentData)ToLua.CheckObject<SceneEnvironment.EnvironmentData>(L, 2);
			obj.sharedEnvironmentData = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index sharedEnvironmentData on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_environmentData(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentController obj = (SceneEnvironment.EnvironmentController)o;
			SceneEnvironment.EnvironmentData arg0 = (SceneEnvironment.EnvironmentData)ToLua.CheckObject<SceneEnvironment.EnvironmentData>(L, 2);
			obj.environmentData = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index environmentData on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_sharedCharacterData(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentController obj = (SceneEnvironment.EnvironmentController)o;
			SceneEnvironment.CharacterData arg0 = (SceneEnvironment.CharacterData)ToLua.CheckObject<SceneEnvironment.CharacterData>(L, 2);
			obj.sharedCharacterData = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index sharedCharacterData on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_characterData(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentController obj = (SceneEnvironment.EnvironmentController)o;
			SceneEnvironment.CharacterData arg0 = (SceneEnvironment.CharacterData)ToLua.CheckObject<SceneEnvironment.CharacterData>(L, 2);
			obj.characterData = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index characterData on a nil value");
		}
	}
}

