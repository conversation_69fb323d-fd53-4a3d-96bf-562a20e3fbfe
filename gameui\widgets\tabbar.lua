
----------------------------------------------------
-- Tabbar
----------------------------------------------------
Tabbar = Tabbar or BaseClass(BaseRender)

local HorLoadKey = "__tabbar_hot_load_key"
local VerLoadKey = "__tabbar_ver_load_key"

-- local PathName = "uis/view/miscpre_load_prefab"
local VerTabbarCellName = "VerticalTabbarCell"
local HorTabbarCellName = "HorizontalTabbarCell"

function Tabbar:__init(parent, ver_tab_prefab_name, hor_tab_prefab_name)
	self.ver_path =  "uis/view/common_panel_prefab"
	self.hor_path =  "uis/view/common_panel_prefab"
	self.ver_list = nil
	self.hor_list = nil
	self.scroll = nil
	self.scroll_bg = nil
	self.defulte_index = nil
	self.ver_icon_path = nil
	self.horiz_icon_path = nil
	self.horiz_icon = nil

	self.ver_cell_list = {}
	self.hor_cell_list = {}
	self.hor_cell_load_list = {}

	self.select_ver_index = 1
	self.select_hor_index = {}

	self.ver_remind_list = {}
	self.remind_to_index = {}
	self.remind_tab = {}
	self.remind_val_v_t = {}
	self.remind_val_h_t = {}
	self.length = {}
	self.hor_tab = {}
	self.ver_tab = {}
	self.other_callback_list = {}
	self.ver_name = nil
	self.ver_cell_loaders = {}
	self.hor_cell_loaders = {}
	self.indie_ver_remind_list = {}

	self.ver_obj_list = {}
	self.ver_res_async_loader = nil
	self.is_init = false

	self.tabbar_call_back = nil
	self.create_hor_call_back = nil  --用于创建横向按钮后的操作
	self.create_ver_call_back = nil  --用于创建纵向按钮后的操作

	local vertical_tabbar = ver_tab_prefab_name ~= nil and parent[ver_tab_prefab_name] or parent.VerticalTabbar
	if vertical_tabbar then
		self.ver_list = vertical_tabbar.VerticalTabbar
		self.ver_list_content = vertical_tabbar.VerticalTabbarContent
		self:SetInstance(self.ver_list)
		if vertical_tabbar.scroll and vertical_tabbar.bg then
			self.scroll = vertical_tabbar.scroll
			self.scroll_bg = vertical_tabbar.bg
		end
	end

	local horizontal_tabbar = hor_tab_prefab_name ~= nil and parent[hor_tab_prefab_name] or parent.HorizontalTabbar
	if horizontal_tabbar then
		self.hor_list = horizontal_tabbar.HorizontalTabbar
		self.hor_list_content = horizontal_tabbar.HorizontalTabbarContent
		self:SetInstance(self.hor_list)
	end
	self.remind_change = BindTool.Bind(self.RemindChangeCallBack, self)
end

function Tabbar:__delete()
	for i, v in ipairs(self.ver_obj_list) do
		ResMgr:Destroy(v)
	end
	self.ver_obj_list = {}
	self.ver_res_async_loader = nil

	if next(self.ver_cell_list) ~= nil then
		for _,v in pairs(self.ver_cell_list) do
			v:DeleteMe()
		end
	end
	self.ver_cell_list = {}

	if next(self.hor_cell_list) ~= nil then
		for _,v in pairs(self.hor_cell_list) do
			v:DeleteMe()
		end
	end
	self.hor_cell_list = {}
	self:UnAllListen()

	self.button_list = nil
	self.defulte_index = nil
	self.create_ver_call_back = nil  --用于创建纵向按钮后的操作
	self.create_hor_call_back = nil  --用于创建横向按钮后的操作
	self.other_callback_list = nil
	self.ver_cell_loaders = nil
	self.hor_cell_loaders = nil

	self.hor_tab_fly_pos = nil
	self.hor_tab_anim_time = nil
	self.hor_tab_anim_distance = nil
	self.old_select_ver_cell = nil
end

function Tabbar:SetSelectCallback(callback)
	if callback then
		self.tabbar_call_back = callback
	end
end

function Tabbar:SetCreateHorCallBack(callback)
	-- body
	self.create_hor_call_back = callback
end

--完成纵向按钮后回调
function Tabbar:SetCreateVerCallBack( callback )
	self.create_ver_call_back = callback
end

--特殊需求，界面内显示红点，vercell显示红点，但horcell不显示红点
function Tabbar:SetNotShowHorRemindIndex( tab_index )
	self.not_show_hor_remind = {}
	for k,v in pairs(tab_index) do
		self.not_show_hor_remind[v] = true
	end
end

-- 设置独立的纵向标签红点 不受横向标签红点的影响
-- ver：第几个标签
function Tabbar:SetIndieVerBarRemind(ver, remind_name)
	self.remind_to_index[remind_name] = ver * 1000
	self.indie_ver_remind_list[ver] = remind_name
	RemindManager.Instance:Bind(self.remind_change, remind_name)
	local num = RemindManager.Instance:GetRemind(remind_name)
	self:RemindChangeCallBack(remind_name, num)
end


function Tabbar:RemindChangeCallBack(remind_name, num)
	local index = self.remind_to_index[remind_name]
	if nil ~= index and nil ~= num then
		local ver = math.floor(index / 1000)
		local hor = index % 1000
		local old_num = self.remind_val_h_t[index] or 0
		self.remind_val_v_t[ver] = self.remind_val_v_t[ver] or 0

		local ver_change_remind = false
		-- 独立的纵向标签红点 不受横向标签红点的影响
		if self.indie_ver_remind_list[ver] then
			ver_change_remind = self.indie_ver_remind_list[ver] == remind_name
		else
			ver_change_remind = true
		end

		if ver_change_remind then
			self.remind_val_v_t[ver] = self.remind_val_v_t[ver] + num - old_num
		end

		self.remind_val_h_t[index] = num

		if self.ver_cell_list[ver] and self.remind_val_v_t[ver] then
			self.ver_cell_list[ver]:ShowRemind(self.remind_val_v_t[ver] > 0)
		end
		if self.hor_cell_list[hor] and self.not_show_hor_remind and self.not_show_hor_remind[ver * 10 + hor] then
			self.hor_cell_list[hor]:ShowRemind(false)
		elseif self.hor_cell_list[hor] and self.remind_val_h_t[index] and ver == self.select_ver_index then
			self.hor_cell_list[hor]:ShowRemind(self.remind_val_h_t[index] > 0)
		end
    end
end

--是否展示新字
function Tabbar:SetNewTextEnableByTabIndex(tab_index, enable)
	if not tab_index then
		return
	end
	local ver = math.floor(tab_index / 10)
	if self.ver_cell_list[ver] then
		self.ver_cell_list[ver]:SetNewTextEnable(enable)
	end
end

-- GM调试用
function Tabbar:GetRemindInfo()
	if self.remind_val_v_t ~= nil then
		for k,v in pairs(self.remind_val_v_t) do
			print_error("remind_val_v_t 红点信息：", k, v)
		end
	end

	if self.remind_val_h_t ~= nil then
		for k,v in pairs(self.remind_val_h_t) do
			print_error("remind_val_h_t 红点信息：", k, v)
		end
	end

	if self.remind_to_index ~= nil then
		for k,v in pairs(self.remind_to_index) do
			print_error("remind_to_index 红点信息：", k, v)
		end
	end
end

function Tabbar:SetVerTabbarCellName(name)
	self.ver_name = name
end

function Tabbar:SetVerTabbarIconStr(str)
	self.ver_icon = str
end

function Tabbar:SetVerTabbarIconPath(path)
	self.ver_icon_path = path
end

function Tabbar:SetHorizTabbarIconStr(str)
	self.horiz_icon = str
end

function Tabbar:SetHorizTabbarIconPath(path)
	self.horiz_icon_path = path
end

function Tabbar:SetVerTabbarStr(str_list)
	if self.ver_cell_list then
		for k,v in pairs(self.ver_cell_list) do
			if str_list[k] then
				v:SetTextStr(str_list[k])
			end
		end
	end
end


--force_play 需要加载完成
function Tabbar:SetHorTabFlyInAnim(star_pos_y,time,distance,force_play)
	self.hor_tab_fly_pos = star_pos_y
	self.hor_tab_anim_time = time
	self.hor_tab_anim_distance = distance
	if force_play then
		self:PlayHorTabFlyInAnim(0,force_play)
	end
end

function Tabbar:PlayHorTabFlyInAnim(index,force_play)
	if not self.hor_tab_fly_pos or not self.hor_tab_anim_time then 
		return 
	end

	if (self.have_ver and (self.hor_tab[self.select_ver_index] and self.hor_tab[self.select_ver_index][index])) or force_play then
		if self.hor_cell_list[index] then
			self.hor_cell_list[index]:OnPreparePlayFlyAnim()
		end

		if force_play then
			for k,v in pairs(self.hor_cell_list) do
				v:OnPreparePlayFlyAnim()
			end
		end

		if index >= self:GetHorListViewNumbers() or force_play then
			local spacing = self.hor_tab_anim_distance or 30
			local speed = self.hor_tab_fly_pos / self.hor_tab_anim_time
			local active_count = 0
			for k,v in pairs(self.hor_cell_list) do
				active_count = active_count + 1
				local new_posy = self.hor_tab_fly_pos + (active_count-1)* spacing
				v:OnPlayFlyAnim(new_posy,new_posy/speed)
			end
			self.hor_tab_fly_pos = nil
			self.hor_tab_anim_time = nil
			self.hor_tab_anim_distance = nil
		end
	end
end

-- 设置 ver tabber 图片名字
function Tabbar:SetVerTabbarNameImgRes(path, res_name)
	self.ver_name_img_path = path
	self.ver_name_img_res_name = res_name
end

function Tabbar:Init(VerTab, HorTab, ver_path, hor_path, remind_tab, tab_type)
	self.is_init = true
	self.tab_fun_list = {}
	self.ver_fun_list = {}
	if type(ver_path) == "string" then
		self.ver_path = ver_path
	end

	if type(hor_path) == "string" then
		self.hor_path = hor_path
	end

	if type(remind_tab) == "table" then
		for k,v in pairs(remind_tab) do
			for k2,v2 in pairs(v) do
				self.remind_to_index[v2] = k * 1000 + k2
				local num = RemindManager.Instance:GetRemind(v2)
				self:RemindChangeCallBack(v2, num)
			end
		end
		self.remind_tab = remind_tab
		self:AddAllListen()
	end

	self.ver_tab = VerTab or {}
	if self.ver_list then
		self.have_ver = true
		if self.hor_list then
			self.hor_tab = HorTab or {}
			self:InitSelectHorIndex()
			self.length = {}
			for i , v in pairs(self.hor_tab) do
				if v then
					self.length[i] = type(v) == "table" and #v or 1
				end
			end
		end

		if self:GetVerListViewNumbers() > 0 then
			self:LoadVerCell(tab_type)
		end
	elseif self.hor_list then
		self.hor_tab = HorTab or {}
		self:InitSelectHorIndex()
		self.length = {}
		--当没有纵向列表的时候仅仅等于1的时候不能满足需求，所以添加一个变量用来控制（vip）
		self.length[0] = #self.hor_tab
		if self:GetHorListViewNumbers() > 0 then
			self:LoadHorCell()
		end
	end
end

function Tabbar:AddAllListen()
	for k,v in pairs(self.remind_tab) do
		for k2,v2 in pairs(v) do
			RemindManager.Instance:Bind(self.remind_change, v2)
		end
	end
end

function Tabbar:UnAllListen()
	RemindManager.Instance:UnBind(self.remind_change)
end

function Tabbar:InitSelectHorIndex()
	for k,v in pairs(self.ver_tab) do
		if not self.select_hor_index[k] then
			self.select_hor_index[k] = self.hor_tab[k] and 1 or 0
		end
	end
end

-- 根据index取按钮
function Tabbar:GetToggleByIndex(index)
	local view, callback
	local ver = math.floor(index / 10)
	if self.ver_cell_list[ver] and not self.ver_cell_list[ver]:IsOn() then
		view = self.ver_cell_list[ver].view
		callback = function() self:ChangeToIndex(index) end
	elseif self.hor_cell_list[index % 10] then
		view = self.hor_cell_list[index % 10].view
		callback = function() self:ChangeToIndex(index) end
	end

	if view and view.rect and view.rect.rect.width > 0 and view.rect.rect.height > 0 then
		return view, callback
	end
end

function Tabbar:SetToggleVisible(k, visible, reason_string, not_init)
	local ver = math.floor(k / 10)
	local hor = k % 10

	if nil == self.tab_fun_list[ver] then
		self.tab_fun_list[ver] = {}
	end

	self.tab_fun_list[ver][hor] = visible
	local v_vis = visible
	if self.length[ver] then
		for i=1, (self.length[ver]) do
			if self.tab_fun_list[ver][i] ~= false then
				v_vis = true
				break
			end
		end
	end

	if self.ver_cell_list[ver] then
		self.ver_cell_list[ver]:SetActive(v_vis)
	end

	self.ver_fun_list[ver] = v_vis
	if self.hor_cell_list[hor] and ver == self.select_ver_index then
		self.hor_cell_list[hor]:SetActive(visible)
	end
	-- 强关虽然关了页签 但是没有切换界面。TODO 当所有页签关闭时 应该要关闭界面
	local need_change = true
	if not visible and not_init then
		for k, v in pairs(self.ver_fun_list) do
			if v and k == self.select_ver_index then
				local hor_index = self.select_hor_index[k] or 0
				self:ChangeToIndex(k * 10 + hor_index)
				need_change = false
				break
			end
		end
		if need_change then
			for k, v in pairs(self.ver_fun_list) do
				if v then
					local hor_index = self.select_hor_index[k] or 0
					self:ChangeToIndex(k * 10 + hor_index)
					need_change = false
					break
				end
			end
		end
	end
end

function Tabbar:SetVerToggleVisble(k,visible)
	if not self.ver_list then
	   return
	end

	local ver = math.floor(k / 10)
	if self.ver_cell_list[ver] ~= nil then
		self.ver_cell_list[ver]:SetActive(visible)
	end
end

function Tabbar:SetVerBGVisble(visible)
	if not self.scroll and not self.scroll_bg then
	   return
	end
	
	self.scroll_bg:SetActive(visible)
	self.scroll:SetActive(visible)
end

function Tabbar:SetToggleEnable(k, visible)
	if not self.ver_list then return end
	local ver = math.floor(k / 10)
	local hor = k % 10
	if 0 == hor then
		-- self.ver_state[ver] = visible
		self.ver_cell_list[ver]:SetActive(visible)
	elseif self.hor_list and ver == self.select_ver_index then
		-- self.hor_state[hor] = visible
		if 1 == hor and ver > 0 then
			self.ver_cell_list[ver]:SetActive(visible)
		end
		if self.hor_cell_list[hor] then
			--self.hor_cell_list[hor]:SetActive(visible)
			self.hor_cell_list[hor].view.toggle.interactable = visible
			XUI.SetGraphicGrey(self.hor_cell_list[hor].view,not visible)--置灰
		end
	end
end

function Tabbar:LoadVerCell(tab_type)
	--for _,v in pairs(self.ver_cell_loaders) do
	--	v:Destroy()
	--end
	-- self.ver_cell_loaders = {}

	local item_render = tab_type or VerItemRender
	for i, v in ipairs(self.ver_obj_list) do
		ResMgr:Destroy(v)
	end
	self.ver_obj_list = {}
	if self.ver_res_async_loader then
		self.ver_res_async_loader:Destroy()
	end
	
	for _,v in pairs(self.ver_cell_list) do
		v:DeleteMe()
	end
	self.ver_cell_list = {}

	local ver_num = self:GetVerListViewNumbers()

	self.ver_res_async_loader = self.ver_res_async_loader or AllocResAsyncLoader(self, VerLoadKey)
	self.ver_res_async_loader:Load(self.ver_path, self.ver_name or VerTabbarCellName, nil, function(new_obj)
		for i=1,ver_num do
			local obj = ResMgr:Instantiate(new_obj)
			if nil == obj then return end
			obj.transform:SetParent(self.ver_list_content.transform, false)
			self.ver_obj_list[i] = obj
			local ver_item_cell = item_render.New(obj)
			local index = #self.ver_cell_list + 1
			if self.remind_val_v_t[index] then
				ver_item_cell:ShowRemind(self.remind_val_v_t[index] > 0)
			end
			ver_item_cell:SetToggleGroup(self.ver_list.toggle_group)
			obj:GetComponent(typeof(UnityEngine.UI.Toggle)):AddClickListener(
			BindTool.Bind(self.VerListEventCallback, self, ver_item_cell))
			local ver_item_data = self.ver_tab[i]
			local ver_icon_str = self.ver_icon and (self.ver_icon .. i) or "role_bag1"
			ver_item_cell:SetIndex(i)
			ver_item_cell:SetIconStr(ver_icon_str, self.ver_icon_path)
			ver_item_cell:SetNameImgRes(self.ver_name_img_path, self.ver_name_img_res_name)
			ver_item_cell:SetData(ver_item_data)
			self.ver_cell_list[index] = ver_item_cell
			if not  ver_item_cell:IsOn() and self.select_hor_index[index] and self.select_ver_index == index then
				ver_item_cell.no_call_back = true
				ver_item_cell:ChangeHL(true)
			end
			if ver_num == index and self.create_ver_call_back then
				self.create_ver_call_back()
			end
			if nil ~= self.ver_fun_list[index] then
				ver_item_cell:SetActive(self.ver_fun_list[index])
			end

			local delete_index
			for k,v in pairs(self.other_callback_list) do
				if math.floor(k / 10) == index and v.is_ver then
					ver_item_cell:SetOtherBtn(v.isOn,v.callback)
					delete_index = k
					break
				end
			end
			if delete_index then
				self.other_callback_list[delete_index] = nil
			end
		end
		self:SetVerScroll()
	end)
end

function Tabbar:SetVerScroll()
	if not self.ver_list then
		return 
	end
	if self.ver_list.scroll_rect then
		local high = self.ver_list.rect.sizeDelta.y
		local item = self:GetToggleByIndex(10)
		-- self.ver_cell_list[ver]:SetActive(visible)
		local count = 0
		for i=1,#self.ver_cell_list do
			if self.ver_cell_list[i].view.gameObject.activeSelf then
				count = count + 1
			end
		end

		local ex_high = 0
		local ver_layout_group = self.ver_list_content:GetComponent(typeof(UnityEngine.UI.VerticalLayoutGroup))
		if ver_layout_group then
			ex_high = ver_layout_group.spacing * (count - 1)
			ex_high = ex_high + ver_layout_group.padding.top
		end

		if item then
			self.ver_list.scroll_rect.verticalNormalizedPosition = 0
			self.ver_list.scroll_rect.enabled = count * item.rect.sizeDelta.y + ex_high >= high
		end
	end
end

function Tabbar:SetHorScroll()
	-- body
	if not self.hor_list then return end
	if self.hor_list.scroll_rect then
		local width = self.hor_list.rect.sizeDelta.x
		local item = self:GetToggleByIndex(11)
		local count = 0
		for i=1,#self.hor_cell_list do
			if self.hor_cell_list[i].view.gameObject.activeSelf then
				count = count + 1
			end
		end
		if item then
			self.hor_list.scroll_rect.horizontalNormalizedPosition = 0
			self.hor_list.scroll_rect.enabled = count * item.rect.sizeDelta.x >= width
		end
	end
end

function Tabbar:LoadHorCell()
	local load_end = function (hor_item_cell, index)
		local hor_num = self:GetHorListViewNumbers()
		if index > hor_num then
			hor_item_cell:SetActive(false)
			return
		end
		local hor_item_data = self.have_ver and (self.hor_tab[self.select_ver_index] and self.hor_tab[self.select_ver_index][index] or nil)
				or (self.hor_tab[index])

		hor_item_cell:SetData(hor_item_data)
		if self.horiz_icon then
			hor_item_cell:SetIconStr(self.horiz_icon_path, self.horiz_icon .. (self.select_ver_index * 10 + index))
		end
		if self.remind_val_h_t[index + self.select_ver_index * 1000] then
			hor_item_cell:ShowRemind(self.remind_val_h_t[index + self.select_ver_index * 1000]> 0)
		end

		if self.tab_fun_list[self.select_ver_index] and nil ~= self.tab_fun_list[self.select_ver_index][index] then
			hor_item_cell:SetActive(self.tab_fun_list[self.select_ver_index][index])
		end

		if index == self.select_hor_index[self.select_ver_index] then
			if not hor_item_cell:IsOn() then
				hor_item_cell.no_call_back = true
				hor_item_cell:ChangeHL(true)
			end
		end

		if hor_num == index then
			for i,v in ipairs(self.hor_cell_list) do
				if i ~= self.select_hor_index[self.select_ver_index] then
					v:ChangeHL(false)
				end
			end
			if self.create_hor_call_back then
				local callback_index = self.select_hor_index[self.select_ver_index] and (self.select_ver_index * 10 + self.select_hor_index[self.select_ver_index]) or self.select_ver_index * 10
				self.create_hor_call_back(callback_index)
			end
		end

		local delete_index
		for k,v in pairs(self.other_callback_list) do
			if k % 10 == index and v.is_hor then
				hor_item_cell:SetOtherBtn(v.isOn,v.callback)
				delete_index = k
				break
			end
		end

		if delete_index then
			self.other_callback_list[delete_index] = nil
		end

		if self.hor_tab_fly_pos then
			--GlobalTimerQuest:AddDelayTimer(function()
				self:PlayHorTabFlyInAnim(index)
			--end,0)
		end

		self:SetHorScroll()
	end

	local hor_num = self:GetHorListViewNumbers()
	for i = 1, hor_num do
		if self.hor_cell_list[i] == nil and not self.hor_cell_load_list[i] then
			self.hor_cell_load_list[i] = true
			local async_loader = AllocAsyncLoader(self, HorLoadKey .. i)
			self.hor_cell_loaders[i] = async_loader
			local hor_parent = self.hor_list_content
			local hor_path = self.hor_path
			local hor_cell = HorTabbarCellName

			async_loader:SetParent(hor_parent.transform)
			async_loader:Load(hor_path, hor_cell, function (obj)
				self.hor_cell_load_list[#self.hor_cell_list + 1] = nil
				if nil == obj or IsNil(obj) then
					print_error("IsNil")
					return
				end

				local hor_item_cell = HorItemRender.New(obj)
				table.insert(self.hor_cell_list, hor_item_cell)
				local index = #self.hor_cell_list
				self.hor_cell_list[index] = hor_item_cell
				hor_item_cell:SetToggleGroup(self.hor_list.toggle_group)
				hor_item_cell:SetIndex(index)
				obj:GetComponent(typeof(UnityEngine.UI.Toggle)):AddClickListener(
					BindTool.Bind(self.HorListEventCallback, self, hor_item_cell))
				load_end(hor_item_cell, index)
			end)

		elseif self.hor_cell_list[i] then
			local hor_item_cell = self.hor_cell_list[i]
			load_end(hor_item_cell, i)
		end
	end
end

function Tabbar:ChangeToIndex(index, is_no_callback)
	local v_index = math.floor(index / 10)
	local h_index = index % 10
	if self.select_ver_index == v_index and self.select_hor_index[self.select_ver_index] == h_index then
		return
	end

	self.select_ver_index = math.floor(index / 10)
	self.select_hor_index[self.select_ver_index] = index % 10

	if not self.is_init then
		return
	end

	if next(self.ver_cell_list) and self.have_ver then
		for _,v in pairs(self.ver_cell_list) do
			if v:GetIndex() == self.select_ver_index then
				 -- 理论上这个方法只是给按钮切换状态，不需要回调，但我不确定逻辑层是否真的所有都是如此, 所以增加is_no_callback参数
				 -- 建议后面的新项目主程大胆把该类重构，或者重写一个
				if is_no_callback then 
					local old_no_callback = v.no_call_back
					v.no_call_back = true
					v:ChangeHL(true)
					v.no_call_back = old_no_callback
				else
					v:ChangeHL(true)
				end
	
				break
			end
		end

		for _,v in pairs(self.ver_cell_list) do
			if v:GetIndex() ~= self.select_ver_index and v:IsOn() then
				v:ChangeHL(false)
			end
		end
	end

	if  next(self.hor_cell_list) then
		for _,v in pairs(self.hor_cell_list) do
			if v:GetIndex() == index % 10 then
				if is_no_callback then
					local old_no_callback = v.no_call_back
					v.no_call_back = true
					v:ChangeHL(true)
					v.no_call_back = old_no_callback
				else
					v:ChangeHL(true)
				end
				break
			end
		end

		for _,v in pairs(self.hor_cell_list) do
			if v:GetIndex() ~= index % 10 and v:IsOn() then
				v:ChangeHL(false)
			end
		end
	end
end

function Tabbar:GetVerListViewNumbers()
	return #self.ver_tab
end

function Tabbar:VerListEventCallback(cell, isOn)
	if not isOn then
		return
	end
	local cur_index = cell:GetIndex()
	local target_index = self.select_hor_index[cur_index] and (cur_index * 10 + self.select_hor_index[cur_index]) or cur_index * 10
	if self:IsVerLimitSelected(target_index) then
		ReDelayCall(self, function()
			if self.old_select_ver_cell then
				self.old_select_ver_cell.view.toggle.isOn = true
			end
			cell.view.toggle.isOn = false
		end, 0, "tabbar_toggle_delay_change")
		return
	end

	self.select_ver_index = cur_index
	self:CheckHorCellSelect()
	local hor_num = self:GetHorListViewNumbers()
	local callback_index = self.select_hor_index[self.select_ver_index] and (self.select_ver_index * 10 + self.select_hor_index[self.select_ver_index]) or self.select_ver_index * 10
	for i,v in ipairs(self.hor_cell_list) do
		v:ShowRemind(false)
		v:SetActive(i <= hor_num)
	end

	if self.hor_list and hor_num > 0 then
		self:LoadHorCell()
		if self.tabbar_call_back and not cell.no_call_back then
			self.tabbar_call_back(callback_index)
		end
	elseif hor_num == 0 then
		if self.tabbar_call_back and not cell.no_call_back then
			self.tabbar_call_back(callback_index)
		end
	end

	cell.no_call_back = false
	self.old_select_ver_cell = cell
end

function Tabbar:IsVerLimitSelected(index)
	return false
end

function Tabbar:CheckHorCellSelect()
	local hor_show_index = self.select_hor_index[self.select_ver_index]
	if hor_show_index and self.tab_fun_list[self.select_ver_index] and self.tab_fun_list[self.select_ver_index][hor_show_index] == false
	and self.length[self.select_ver_index] then
		for i=1,self.length[self.select_ver_index] do
			if self.tab_fun_list[self.select_ver_index][i] ~= false then
				self.select_hor_index[self.select_ver_index] = i
				break
			end
		end
	end
end

function Tabbar:GetHorListViewNumbers()
	return self.length[self.select_ver_index] or self.length[0] or 0
end

function Tabbar:HorListEventCallback(cell, isOn)
	if isOn then
		self.select_hor_index[self.select_ver_index] = cell:GetIndex()
		if self.tabbar_call_back and not cell.no_call_back then
			self.tabbar_call_back(self.select_ver_index * 10 + self.select_hor_index[self.select_ver_index])
		end
	end
	cell.no_call_back = false
end

function Tabbar:SetOtherBtn(index,isOn,callback)
	local ver = math.floor(index / 10)
	if self.ver_cell_list and self.ver_cell_list[ver] then
		self.ver_cell_list[ver]:SetOtherBtn(isOn,callback)
	else
		self.other_callback_list[index] = {is_ver = true,isOn = isOn ,callback = callback}
	end
end

function Tabbar:SetOtherBtnImgEnable(index, enable)
	local ver = math.floor(index / 10)
	if self.ver_cell_list and self.ver_cell_list[ver] then
		self.ver_cell_list[ver]:SetOtherBtnImgEnable(enable)
	end
end

function Tabbar:SetHorOtherBtn(index,isOn,callback)
	local hor = index % 10
	if self.hor_cell_list and self.hor_cell_list[hor] then
		self.hor_cell_list[hor]:SetOtherBtn(isOn,callback)
	else
		self.other_callback_list[index] = {is_hor = true,isOn = isOn ,callback = callback}
	end
end

--设置cell的alpha和interactable来显隐cell
function Tabbar:SetVerToggleCanvas(k, enable)
	if not self.ver_list then
		return
	end
	local ver = math.floor(k/10)
	if self.ver_cell_list[ver] ~= nil then
		self.ver_cell_list[ver]:SetCanvasAlpha(enable)
	end
end

--设置横排按钮打开时选中按钮
function Tabbar:SetHorToggleSelect(index)
	local hor = index % 10
	local ver = math.floor(index / 10)
	self.select_hor_index[ver] = hor
end

function Tabbar:GetVerListNum()
	local num = 0
	for i, v in pairs(self.ver_fun_list) do
		num = v and num + 1 or num
	end
	return num
end

--获取生成的纵向列表部分
function Tabbar:GetVerCellList()
	return self.ver_cell_list
end

function Tabbar:GetVerCell(tab_index)
	if not self.ver_list then
		return
	end

	local ver = math.floor(tab_index / 10)
	return self.ver_cell_list[ver]
end

function Tabbar:GetHorCellList()
	return self.hor_cell_list
end

function Tabbar:GetHorCell(tab_index)
	if not self.ver_list then
		return
	end

	local hor = tab_index % 10
	return self.hor_cell_list[hor]
end

--tabbar置灰，部分功能使用，小心复用
function Tabbar:SetVerToggleGrey(k, visible)
	if not self.ver_list then
	   return
	end

	local ver = math.floor(k / 10)
	if self.ver_cell_list[ver] ~= nil then
		XUI.SetGraphicGrey(self.ver_cell_list[ver].view, visible)
	end
end

--动态控制tabbar的页签图片和文字颜色，bingqie刷新过程中保持当前的选中状态（动态运营活动需求）
function Tabbar:SetVerImageTxtColor(bundle, asset1, asset2, color1, color2, red_pos, imgpos)
	if nil == self.ver_cell_list then
		return
	end

	for k,v in pairs(self.ver_cell_list) do
		v:SetImgAndTxtColor(bundle, asset1, asset2, color1, color2, red_pos, imgpos)
	end
end

function Tabbar:FlushVerImage()
	if nil == self.ver_cell_list then
		return
	end

	local img_path, res_name = self.ver_name_img_path, self.ver_name_img_res_name
	for k,v in pairs(self.ver_cell_list) do
		v:SetNameImgRes(img_path, res_name)
	end
end

--重新给hortab 内容赋值
function Tabbar:SetHorTabData(hor_tab)
	self.hor_tab = hor_tab
	self:LoadHorCell()
end

function Tabbar:JumpToVerPrecent(percent)
	self.ver_list.scroll_rect.verticalNormalizedPosition = percent
end

----------------------------------------
-------VerItemRender


VerItemRender = VerItemRender or BaseClass(BaseRender)
function VerItemRender:__init()
	self.no_call_back = false
	if self.node_list.ver_img_name then
		self.node_list.ver_img_name:SetActive(false)
	end

	if self.node_list.ver_img_name_hl then
		self.node_list.ver_img_name_hl:SetActive(false)
	end
end

function VerItemRender:__delete()

end

function VerItemRender:SetIconStr(icon_str, icon_path)
	if self.node_list.ver_icon then
		icon_path = icon_path or ResPath.GetFunctionIcon
		local asset_name, bundle_name = icon_path(icon_str)
		self.node_list.ver_icon.image:LoadSprite(asset_name, bundle_name, function ()
			self.node_list.ver_icon.image:SetNativeSize()
			self.node_list.ver_icon:SetActive(true)
		end)
	end

	-- 高亮图标
	if self.node_list.ver_icon_hl then
		icon_path = icon_path or ResPath.GetFunctionIcon
		local asset_name, bundle_name = icon_path(icon_str .. "_hl")
		self.node_list.ver_icon_hl.image:LoadSprite(asset_name, bundle_name, function ()
			self.node_list.ver_icon_hl.image:SetNativeSize()
			self.node_list.ver_icon_hl:SetActive(true)
		end)
	end
end

function VerItemRender:SetTextStr(str)
	if self.node_list.Text then
		self.node_list.Text.text.text = str
	end
	if self.node_list.TextHL then
		self.node_list.TextHL.text.text = str
	end
end

-- 标签的 图片名
function VerItemRender:SetNameImgRes(path, res_name)
	if not path or not res_name then
		return
	end

	res_name = res_name .. self.index
	if self.node_list.ver_img_name then
		self.node_list.ver_img_name.image:LoadSprite(path, res_name, function ()
			self.node_list.ver_img_name.image:SetNativeSize()
			self.node_list.ver_img_name:SetActive(true)
		end)
	end

	-- 高亮图片名
	if self.node_list.ver_img_name_hl then
		self.node_list.ver_img_name_hl.image:LoadSprite(path, res_name .. "_hl", function ()
			self.node_list.ver_img_name_hl.image:SetNativeSize()
			self.node_list.ver_img_name_hl:SetActive(true)
		end)
	end
end

function VerItemRender:OnFlush()
	local str = string.gsub(self.data, "\n", "")
	if string.find(self.data, "<need_enter>") then
		str = string.gsub(self.data, "<need_enter>", "")
	end
	self.node_list.Text.text.text = str
	if self.node_list.TextHL then
		self.node_list.TextHL.text.text = str
	end
end

function VerItemRender:SetToggleGroup(group)
	self.view.toggle.group = group
end

function VerItemRender:ChangeHL(is_HL)
	if self.view.toggle.isOn ~= is_HL then
		self.view.toggle.isOn = is_HL or false
	end
end

function VerItemRender:IsOn()
	return self.view.toggle.isOn
end

function VerItemRender:SetActive(visible)
	self.view:SetActive(visible)
end

function VerItemRender:ShowRemind(visible)
    if self.node_list and self.node_list["RedPoint"] then
        self.node_list["RedPoint"]:SetActive(visible)
    end
end

function VerItemRender:SetOtherBtn(IsOn,callback)
	self.node_list["btn_other"]:SetActive(IsOn)
	if callback then
		self.node_list["btn_other"].button:AddClickListener(callback)
	end
end

function VerItemRender:SetOtherBtnImgEnable(enable)
	if self.node_list["btn_other_img"] then
		self.node_list["btn_other_img"]:SetActive(enable)
	end
end

function VerItemRender:SetCanvasAlpha(enable)
	local component = self.view:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
	component.alpha = enable and 1 or 0
	component.interactable = enable
end

--是否展示新字（通用预制体里面没有这个节点，需要的话自己搞个预制体）
function VerItemRender:SetNewTextEnable(enable)
	if self.node_list["txt_new"] then
		self.node_list["txt_new"]:SetActive(enable)
	end
end

--鸿蒙感悟特殊部分处理
function VerItemRender:SetNoShowElement(is_show)
	if self.node_list["img_adorm"] then
		self.node_list["img_adorm"]:SetActive(is_show)
	end
end

--鸿蒙感悟特殊部分处理
function VerItemRender:SetHandOrTailShow(is_show, is_show1, is_show2)
	if self.node_list["hand"] and self.node_list["hand1"] then
		self.node_list["hand"]:SetActive(is_show)
		self.node_list["hand1"]:SetActive(is_show1)
	end
	
	if self.node_list["tail"] then
		self.node_list["tail"]:SetActive(is_show2)
	end
end

--鸿蒙感悟字体大小
function VerItemRender:SetVerTextSize(size)
	if self.node_list.Text then
		self.node_list.Text.text.fontSize = size
	end
end

--设置页签的图片以及文字颜色
--加载途径，非高亮，高亮，非高亮颜色，高亮颜色(颜色要传表，因为包含渐变之类的) coolor key 0.文字本身颜色 1 上渐变 2.下渐变 3.描边 4.阴影
function VerItemRender:SetImgAndTxtColor(bundle, asset1, asset2, color1, color2, red_pos, imgpos)
	if nil == bundle then
		return
	end

	if self.node_list["Image"] and asset1 then
		self.node_list["Image"].image:LoadSprite(bundle, asset1,function ()
			self.node_list["Image"].image:SetNativeSize()
		end)
	end

	if self.node_list["HLImage"] and asset2 then
		self.node_list["HLImage"].image:LoadSprite(bundle, asset2,function ()
			self.node_list["HLImage"].image:SetNativeSize()
		end)
	end

	if self.node_list["Image"] and imgpos and imgpos[1] then
		self.node_list["Image"].rect.anchoredPosition = imgpos[1]
	end

	if self.node_list["HLImage"] and imgpos and imgpos[2] then
		self.node_list["HLImage"].rect.anchoredPosition = imgpos[2]
	end

	local text = self.node_list["Text"]
	if text then
		if color1[0] then
			text.text.color = color1[0]
		end

		local gradient = text:GetOrAddComponent(typeof(UIGradient))
		if gradient then
			gradient.enabled = color1[1] ~= nil
			if color1[1] ~= nil then
				gradient.Color1 = color1[1]
				gradient.Color2 = color1[2]
			end
		end

		-- TODO 需要改为新版处理方式
		-- local shadow = text:GetOrAddComponent(typeof(UnityEngine.UI.Shadow))
		-- if shadow then
		-- 	-- TODO 需要改为新版处理方式
		-- 	-- shadow.enabled = color1[4] ~= nil
		-- 	if color1[4] ~= nil then
		-- 		shadow.effectColor = color1[4]
		-- 	end
		-- end

		-- local out_line = text:GetOrAddComponent(typeof(UnityEngine.UI.Outline))
		-- if out_line then
		-- 	out_line.enabled = color1[3] ~= nil
		-- 	if color1[3] ~= nil then
		-- 		out_line.effectColor = color1[3]
		-- 	end
		-- end
	end

	--高亮文字
	local hl_text = self.node_list["TextHL"]
	if hl_text then
		if color2[0] then
			hl_text.text.color = color2[0]
		end

		local gradient = hl_text:GetOrAddComponent(typeof(UIGradient))
		if gradient then
			gradient.enabled = color2[1] ~= nil
			if color2[1] ~= nil then
				gradient.Color1 = color2[1]
				gradient.Color2 = color2[2]
			end
		end

		-- TODO 需要改为新版处理方式
		-- local shadow = hl_text:GetOrAddComponent(typeof(UnityEngine.UI.Shadow))
		-- if shadow then
		-- 	-- TODO 需要改为新版处理方式
		-- 	-- shadow.enabled = color2[4] ~= nil
		-- 	if color2[4] ~= nil then
		-- 		shadow.effectColor = color2[4]
		-- 		shadow.effectDistance = Vector2(0.5, -0.5)
		-- 	end
		-- end

		-- local out_line = hl_text:GetOrAddComponent(typeof(UnityEngine.UI.Outline))
		-- if out_line then
		-- 	out_line.enabled = color2[3] ~= nil
		-- 	if color2[3] ~= nil then
		-- 		out_line.effectColor = color2[3]
		-- 		out_line.effectDistance = Vector2(-1, -1)
		-- 	end
		-- end
	end

	if red_pos then
		self.node_list["RedPoint"].transform.localPosition = red_pos
	end
end

function VerItemRender:ShowActTag(is_show)
	if self.node_list["act_tag"] then
		self.node_list["act_tag"]:SetActive(is_show)
	end
end
----------------------------------------
-------HorItemRender

HorItemRender = HorItemRender or BaseClass(BaseRender)
function HorItemRender:__init()
    self.no_call_back = false
    self.name_show_type = false
    self.show_small_icon = false
end

function HorItemRender:__delete()
    self.name_show_type = nil
    self.small_icon_str = nil
	self.small_icon_path = nil
end

function HorItemRender:OnFlush()
	if self.data and type(self.data) ~= "table" then
		if self.name_show_type then
			self.node_list.Text.text.text = ""
			if self.node_list.Text_hl then
				self.node_list.Text_hl.text.text = ""
			end

			if self.node_list["HLImagetxt"] then
				self.node_list["HLImagetxt"].text.text = ""
			end

			if self.node_list["name_img"] then
				self.node_list["name_img"]:SetActive(true)
				local asset_name, bundle_name = ResPath.GetFunctionIcon(self.data)
				self.node_list["name_img"].image:LoadSprite(asset_name, bundle_name, function ()
				self.node_list["name_img"].image:SetNativeSize()
				end)
			end

			if self.node_list["name_img_hl"] then
				self.node_list["name_img_hl"]:SetActive(true)
				local asset_name, bundle_name = ResPath.GetFunctionIcon(self.data.."_hl")
				self.node_list["name_img_hl"].image:LoadSprite(asset_name, bundle_name, function ()
				self.node_list["name_img_hl"].image:SetNativeSize()
				end)
			end

		else
			self.node_list.Text.text.text = self.data
			if self.node_list.Text_hl then
				self.node_list.Text_hl.text.text = self.data
			end

			if self.node_list["HLImagetxt"] then
				self.node_list["HLImagetxt"].text.text = self.data
			end
			
			if self.node_list["name_img"] then
				self.node_list["name_img"]:SetActive(false)
			end

			if self.node_list["name_img_hl"] then
				self.node_list["name_img_hl"]:SetActive(false)
			end

		end

		if self.show_small_icon then
			if self.node_list["icon_img"] and self.small_icon_str then
				local icon_path = self.small_icon_path or ResPath.GetFunctionIcon
				local asset_name, bundle_name = icon_path(self.small_icon_str)
					self.node_list["icon_img"].image:LoadSprite(asset_name, bundle_name, function ()
					self.node_list["icon_img"].image:SetNativeSize()
				end)
			end

			if self.node_list["icon_img_hl"] and self.small_icon_str then
				local icon_path = self.small_icon_path or ResPath.GetFunctionIcon
				local asset_name, bundle_name = icon_path(self.small_icon_str.."_hl")
					self.node_list["icon_img_hl"].image:LoadSprite(asset_name, bundle_name, function ()
					self.node_list["icon_img_hl"].image:SetNativeSize()
				end)
			end
		end
	end
end

function HorItemRender:SetToggleGroup(group)
	self.view.toggle.group = group
end

function HorItemRender:ChangeHL(is_HL)
	if self.view.toggle.isOn ~= is_HL then
		self.view.toggle.isOn = is_HL or false
	end
end

function HorItemRender:IsOn()
	return self.view.toggle.isOn
end

function HorItemRender:SetActive(visible)
	self.view:SetActive(visible)
end

function HorItemRender:ShowRemind(visible)
	if self.node_list ~= nil and self.node_list["RedPoint"] ~= nil then
		self.node_list["RedPoint"]:SetActive(visible)
	end
end

function HorItemRender:SetIconStr(icon_path, icon_str)
	if self.node_list.hor_icon then
		self.node_list.hor_icon.image:LoadSprite(icon_path, icon_str, function ()
			self.node_list.hor_icon.image:SetNativeSize()
			self.node_list.hor_icon:SetActive(true)
		end)
	end

	-- 高亮图标
	if self.node_list.hor_icon_hl then
		icon_path = icon_path or ResPath.GetBSFunctionIcon
		icon_str = icon_str .. "_hl"
		self.node_list.hor_icon_hl.image:LoadSprite(icon_path, icon_str, function ()
			self.node_list.hor_icon_hl.image:SetNativeSize()
			self.node_list.hor_icon_hl:SetActive(true)
		end)
	end
end

function HorItemRender:SetOtherBtn(IsOn,callback)
	self.node_list["btn_other"]:SetActive(IsOn)
	if callback then
		self.node_list["btn_other"].button:AddClickListener(callback)
	end
end

function HorItemRender:SetNameImage(value)
	self.name_show_type = value
end

function HorItemRender:OnPlayFlyAnim(fly_star_pos_y,fly_time)
	if self.node_list["anim_root"] and fly_star_pos_y and fly_time then
		self.node_list["anim_root"].transform.localPosition = Vector3(0,fly_star_pos_y,0)
		if self.node_list["anim_root"].canvas_group then
			self.node_list["anim_root"].canvas_group.alpha = 1
		end
		self.node_list["anim_root"].rect:DOAnchorPosY(0, fly_time)
	end
end

function HorItemRender:OnPreparePlayFlyAnim()
	if self.node_list["anim_root"] and  self.node_list["anim_root"].canvas_group then
		self.node_list["anim_root"].canvas_group.alpha = 0
	end
end

function HorItemRender:ShowActTag(is_show)
	if self.node_list["act_tag"] then
		self.node_list["act_tag"]:SetActive(is_show)
	end
end