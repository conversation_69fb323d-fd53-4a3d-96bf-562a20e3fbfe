AlchemyComposeView = AlchemyComposeView or BaseClass(SafeBaseView)
local MAX_TYPE = 7
function AlchemyComposeView:__init()
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(true)

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(2, -4), sizeDelta = Vector2(1102, 590)})
	self:AddViewResource(0, "uis/view/country_map_ui/alchemy_prefab", "layout_alchemy_compose_view")
end

function AlchemyComposeView:__delete()

end

function AlchemyComposeView:LoadCallBack()
    self.node_list["title_view_name"].text.text = Language.CountryAlchemy.TitleName1

	for i = 1, MAX_TYPE do
		XUI.AddClickEventListener(self.node_list["toggle_" .. i], BindTool.Bind(self.OnClickToggle, self, i))
	end

	self.compose_list = AsyncListView.New(AlchemyComposeCell, self.node_list["compose_list"])

	self.select_compose_type = 1
	self.show_item = ItemCell.New(self.node_list["compose_item_pos"])
end

function AlchemyComposeView:CloseCallBack()

end

function AlchemyComposeView:ReleaseCallBack()
	if self.compose_list then
		self.compose_list:DeleteMe()
		self.compose_list = nil
	end

	if self.show_item then
		self.show_item:DeleteMe()
		self.show_item = nil
	end
	
end

function AlchemyComposeView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			-- 丹灶序号
			if v.field_seq then
				AlchemyWGData.Instance:SetCurDanzaoSeq(v.field_seq)
			end
		end
	end

	self:FlushToggle()
	self:FlushcomposeList()
	self:FlushOther()
end

function AlchemyComposeView:OnClickToggle(index)
	self.select_compose_type = index
	self:FlushToggle()
	self:FlushcomposeList()
	self:FlushOther()
end

function AlchemyComposeView:FlushToggle()
	local show_compose_list = AlchemyWGData.Instance:GetAllShowComposeCfg()
	for i = 1, MAX_TYPE do
		if i <= #show_compose_list then
			self.node_list["toggle_" .. i]:SetActive(true)
			self.node_list["toggle_" .. i .. "_hl"]:SetActive(i == self.select_compose_type)
			self.node_list["name_" .. i].text.text = show_compose_list[i].name
			self.node_list["name_" .. i .. "_hl"].text.text = show_compose_list[i].name
		else
			self.node_list["toggle_" .. i]:SetActive(false)
		end
	end
end

function AlchemyComposeView:FlushcomposeList()
	local compose_cfg_list = AlchemyWGData.Instance:GetComposeCfgByType(self.select_compose_type)
	self.compose_list:SetDataList(compose_cfg_list)
end

function AlchemyComposeView:FlushOther()
	local show_compose_cfg = AlchemyWGData.Instance:GetShowComposeCfgByType(self.select_compose_type)
	self.show_item:SetData({item_id = show_compose_cfg.zhenxi_id})
	local item_cfg = ItemWGData.Instance:GetItemConfig(show_compose_cfg.zhenxi_id)
	self.node_list.compose_item_name.text.text = item_cfg.name
	local reduce_time = AlchemyWGData.Instance:GetReduceTime()
	self.node_list.reduce_time_str.text.text = string.format(Language.CountryAlchemy.ReduceTime, reduce_time * 100)
end

-- 单个配方
----------------------------------------------------
AlchemyComposeCell = AlchemyComposeCell or BaseClass(BaseRender)

function AlchemyComposeCell:__init()
	self.stuff_item_list = {}
	for i = 1, 3 do
		self.stuff_item_list[i] = ItemCell.New(self.node_list["stuff_item_" .. i])
	end

	self.product_item = ItemCell.New(self.node_list["product_item"])
	XUI.AddClickEventListener(self.node_list["use_btn"], BindTool.Bind(self.OnClickUseBtn, self))
end

function AlchemyComposeCell:__delete()
	for i,v in ipairs(self.stuff_item_list) do
		v:DeleteMe()
	end
	self.stuff_item_list = nil

	self.product_item:DeleteMe()
	self.product_item = nil
end

function AlchemyComposeCell:OnFlush()
	if not self.data then
		return 
	end

	local product_item_id = self.data.product_item.item_id
	local item_cfg = ItemWGData.Instance:GetItemConfig(product_item_id)
	self.node_list.name.text.text = item_cfg.name
	local remain_time = TimeUtil.FormatSecondDHM6(self.data.need_time)
	self.node_list.time.text.text = string.format(Language.CountryAlchemy.NeedTimeStr, remain_time)
	self.product_item:SetData({item_id = product_item_id})

	for i = 1, 3 do
		if self.node_list["add_" .. i] then
			self.node_list["add_" .. i]:SetActive(self.data["stuff_id_" .. i] > 0)
		end

		self.node_list["stuff_item_" .. i]:SetActive(self.data["stuff_id_" .. i] > 0)
		if self.data["stuff_id_" .. i] > 0 then
			local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data["stuff_id_" .. i])
			local stuff_count = self.data["stuff_num_" .. i] 
			self.stuff_item_list[i]:SetData({item_id = self.data["stuff_id_" .. i]})
			local color = item_num >= stuff_count and COLOR3B.D_GREEN or COLOR3B.D_RED
			self.stuff_item_list[i]:SetRightBottomTextVisible(true)
			self.stuff_item_list[i]:SetRightBottomText(ToColorStr(item_num .. '/' .. stuff_count, color))
		else
			self.stuff_item_list[i]:SetData(nil)
			self.stuff_item_list[i]:SetRightBottomTextVisible(false)
		end
	end
end

function AlchemyComposeCell:OnClickUseBtn()
	local cur_danzao_seq = AlchemyWGData.Instance:GetCurDanzaoSeq()
	if not self.data  and (not cur_danzao_seq)then
		return 
	end
	
	local stuff_cfg = {}
	for i = 1, 3 do
		if self.data["stuff_id_" .. i] > 0 then
			table.insert(stuff_cfg, {stuff_id = self.data["stuff_id_" .. i], stuff_count = self.data["stuff_num_" .. i]})
		end
	end

	local can_use = true
	for i, v in ipairs(stuff_cfg) do
		local num = ItemWGData.Instance:GetItemNumInBagById(v.stuff_id)
		if num < v.stuff_count then
			can_use = false
			break
		end
	end

	if can_use then
		AlchemyWGCtrl.Instance:SenSAlchemyReq(ALCHEMY_OPERATE_TYPE.COMPOS, cur_danzao_seq, self.data.seq)
		ViewManager.Instance:Close(GuideModuleName.AlchemyComposeView)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.CountryAlchemy.StuffNotEnough)
	end
end