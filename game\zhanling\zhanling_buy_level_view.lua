ZhanLingBuyLevelView = ZhanLingBuyLevelView or BaseClass(SafeBaseView)

local buy_level_type = 4
function ZhanLingBuyLevelView:__init()
	self:SetMaskBg(true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:AddViewResource(0, "uis/view/zhanling_ui_prefab", "layout_zhanling_buy_Level")
end

function ZhanLingBuyLevelView:LoadCallBack()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
	self.cur_level = ZhanLingWGData.Instance:GetZhanLingInfo().level
	self.buy_level_list = AsyncListView.New(ZhanLingBuyLevelRender, self.node_list["buy_level_part"])
	self.node_list.text_close_tip.text.text = Language.ZhanLing.BuyLevelCloseTip
end

function ZhanLingBuyLevelView:ReleaseCallBack()
	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.buy_level_list then
		self.buy_level_list:DeleteMe()
		self.buy_level_list = nil
	end

	ZhanLingWGCtrl.Instance:ClearBuyLevelAlert()
end

function ZhanLingBuyLevelView:OnFlush()
	local level_list = ZhanLingWGData.Instance:GetLevelBuyTypeList()
	self.buy_level_list:SetDataList(level_list)

	local zhanling_lnfo = ZhanLingWGData.Instance:GetZhanLingInfo()
	local new_level = zhanling_lnfo and zhanling_lnfo .level or 0
	if new_level ~= self.cur_level then
		self:PlayLevelUpTween()
	end
end

function ZhanLingBuyLevelView:PlayLevelUpTween()

	TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.ty_shengji, is_success = true, pos = Vector2(0, 0),
	parent_node = self.node_list["eff_show_node"]})

	-- if	self.tween_sequence then
	-- 	self.tween_sequence:Kill()
	-- 	self.tween_sequence = nil
	-- end

	-- local image = self.node_list.img_level_up
	-- image.gameObject:SetActive(true)
	-- image.rect.anchoredPosition = Vector2(0, 280)

	-- self.tween_sequence = DG.Tweening.DOTween.Sequence()
	-- local tween_domove = image.rect:DOAnchorPosY(300, 0.5);
	-- local tween_dofade_1 = image:DoFade(1, 0.5)
	-- local tween_dofade_2 = image:DoFade(0, 0.5)
	-- self.tween_sequence:Append(tween_dofade_1)
	-- self.tween_sequence:AppendInterval(0.5)
	-- self.tween_sequence:Append(tween_dofade_2)
	-- self.tween_sequence:Insert(0, tween_domove)
end