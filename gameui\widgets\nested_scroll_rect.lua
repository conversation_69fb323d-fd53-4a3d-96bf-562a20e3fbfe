local SNestedScrollDir = {
    Horizontal = 0,
    Vertical = 1,
    NoDir = 2,
}

--解决重叠的ScrollRect滑动问题
SNestedScrollRect = SNestedScrollRect or BaseClass()

function SNestedScrollRect:__init(node)
    if not node then
        print_error("[SNestedScrollRect] node is nil")
    end
    if IsNil(node.event_trigger_listener) then
        print_error("[SNestedScrollRect] event_trigger_listener is nil")
    end
    if IsNil(node.scroll_rect) then
        print_error("[SNestedScrollRect] scroll_rect is nil")
    end

    self.event_listener = node.event_trigger_listener

    --当前scroll
    self.scroll_rect = node.scroll_rect
    --scroll滑动方向
    -- self.scroll_dir = node.scroll_rect.horizontal and SNestedScrollDir.Horizontal or SNestedScrollDir.Vertical
    self.scroll_dir = node.scroll_rect.horizontal and SNestedScrollDir.Horizontal or node.scroll_rect.vertical and SNestedScrollDir.Vertical or SNestedScrollDir.NoDir
    self:InitListener()
end

function SNestedScrollRect:__delete()
    self.event_listener = nil
    self.scroll_rect = nil
    self.parent_scroll_rect = nil
    self.scroll_dir = nil
    self.begin_dir = nil
end

function SNestedScrollRect:InitListener()
    self.on_begin_drag = BindTool.Bind1(self.OnBeginDrag, self)
    self.on_drag = BindTool.Bind1(self.OnDrag, self)
    self.on_end_drag = BindTool.Bind1(self.OnEndDrag, self)
    if self.event_listener ~= nil then
        self.event_listener:AddBeginDragListener(self.on_begin_drag)
        self.event_listener:AddDragListener(self.on_drag)		--按下
        self.event_listener:AddEndDragListener(self.on_end_drag)
    end
end

function SNestedScrollRect:OnBeginDrag(event_data)
    if not self.parent_scroll_rect then
        return
    end

    self.begin_dir = math.abs(event_data.delta.x) > math.abs(event_data.delta.y)
                        and SNestedScrollDir.Horizontal or SNestedScrollDir.Vertical
    if self.begin_dir ~= self.scroll_dir then
        self.parent_scroll_rect:OnBeginDrag(event_data)
    end
end

function SNestedScrollRect:OnDrag(event_data)
    if not self.parent_scroll_rect or not self.begin_dir then
        return
    end

    if self.begin_dir ~= self.scroll_dir then
        self.parent_scroll_rect.enabled = true
        self.parent_scroll_rect:OnDrag(event_data)
        self.scroll_rect.enabled = false
    else
        self.parent_scroll_rect.enabled = false
        self.scroll_rect.enabled = true
    end
end

function SNestedScrollRect:OnEndDrag(event_data)
    if not self.parent_scroll_rect or not self.begin_dir then
        return
    end

    if self.begin_dir ~= self.scroll_dir then
        self.parent_scroll_rect:OnEndDrag(event_data)
    end
    self.parent_scroll_rect.enabled = true
    self.scroll_rect.enabled = true
end

function SNestedScrollRect:SetParentScrollRect(parent_scroll_rect)
    if IsNil(parent_scroll_rect) then
        print_error("[SNestedScrollRect] parent_scroll_rect is nil")
        return
    end

    local parent_scroll_dir = parent_scroll_rect.horizontal and SNestedScrollDir.Horizontal or SNestedScrollDir.Vertical
    if self.scroll_dir == parent_scroll_dir then
        print_error("[SNestedScrollRect] 两个方向一样就不需要用这个了")
        return
    end

    self.parent_scroll_rect = parent_scroll_rect
end