﻿using UnityEngine;
using UnityEngine.UI;

[AddComponentMenu("UGUI/Tween/UGUI Tween Height")]
[RequireComponent(typeof(RectTransform))]
public class UGUITweenHeight : UGUITweener 
{
	public float from = 0f;
	public float to = 0f;

	bool mCached = false;
	RectTransform mTrans;

	void Cache ()
	{
		mCached = true;
		mTrans = GetComponent<RectTransform>();
	}

	public float value
	{
		get
		{
			if (!mCached) Cache();
			if (mTrans != null) return mTrans.sizeDelta.y;
			return 0;
		}
		set
		{
			if (!mCached) Cache();
			if (mTrans != null) mTrans.sizeDelta = new Vector2(mTrans.sizeDelta.x, value);
		}
	}

	protected override void OnUpdate (float factor, bool isFinished) { value = Mathf.Lerp(from, to, factor); }

	static public UGUITweenHeight Begin (GameObject go, float duration, float height)
	{
		#if UNITY_EDITOR
		if (!Application.isPlaying) return null;
		#endif
		UGUITweenHeight comp = UGUITweener.Begin<UGUITweenHeight>(go, duration);
		comp.from = comp.value;
		comp.to = height;

		if (duration <= 0f)
		{
			comp.Sample(1f, true);
			comp.enabled = false;
		}
		return comp;
	}

	[ContextMenu("Set 'From' to current value")]
	public override void SetStartToCurrentValue () { from = value; }

	[ContextMenu("Set 'To' to current value")]
	public override void SetEndToCurrentValue () { to = value; }

	[ContextMenu("Assume value of 'From'")]
	void SetCurrentValueToStart () { value = from; }

	[ContextMenu("Assume value of 'To'")]
	void SetCurrentValueToEnd () { value = to; }
}
