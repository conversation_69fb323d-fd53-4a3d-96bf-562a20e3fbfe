EverythingUnderTheSunWGData = EverythingUnderTheSunWGData or BaseClass()

function EverythingUnderTheSunWGData:__init()
	if EverythingUnderTheSunWGData.Instance then
		error("[EverythingUnderTheSunWGData] Attempt to create singleton twice!")
		return
	end

    EverythingUnderTheSunWGData.Instance = self

    self:InitParam()
    self:InitConfig()
end

function EverythingUnderTheSunWGData:__delete()
    EverythingUnderTheSunWGData.Instance = nil
end

function EverythingUnderTheSunWGData:InitParam()
    self.accordion_data_list = {}
    self.big_type_data_list = {}
    self.save_model_list = {}
    self.open_record_list = {}
end

function EverythingUnderTheSunWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("everything_under_the_sun_cfg_auto")
    self.main_cfg = cfg.main
    self.big_type_info = cfg.big_type_info
    self.show_cfg = ListToMapList(cfg.show, "small_type")

    self:CreateAccordionList()
end

-- 创建每天显示活动和活动内显示内容的列表
function EverythingUnderTheSunWGData:CreateAccordionList()
    if not IsEmptyTable(self.accordion_data_list) and not IsEmptyTable(self.big_type_data_list) then
        return
    end

    local accordion_data_list = {}
    for k, v in pairs(self.main_cfg) do
        accordion_data_list[v.open_day] = {}
        local list = Split(v.act_type_list, "|")
        for _, act_type in pairs(list) do
            local act_type_num = tonumber(act_type)
            local cfg = self:GetBigTypeCfgByType(act_type_num)
            if not IsEmptyTable(cfg) then
                table.insert(accordion_data_list[v.open_day], {act_type = act_type_num, info = cfg})
            end
        end
    end

    local big_type_data_list = {}
    for act_type, v in pairs(self.big_type_info) do
        big_type_data_list[act_type] = {}
        local list = Split(v.small_type_list, "|")
        for k, small_type in pairs(list) do
            local small_type_num = tonumber(small_type)
            table.insert(big_type_data_list[act_type], {small_type = small_type_num})
        end
    end

    self.accordion_data_list = accordion_data_list
    self.big_type_data_list = big_type_data_list
end

-- 最大开启天数
function EverythingUnderTheSunWGData:GetMaxShowDay()
    return self.main_cfg[#self.main_cfg].open_day
end

function EverythingUnderTheSunWGData:GetAccordionList(day)
    return self.accordion_data_list[day] or {}
end

-- 初始化做筛选开服天数为-1，故在界面获取时才筛选
function EverythingUnderTheSunWGData:GetBigTypeList(big_type, is_next)
    local big_type_list = {}
    if self.big_type_data_list[big_type] then
        for k, v in pairs(self.big_type_data_list[big_type]) do
            local data = self:GetSmallTypeCfgByType(v.small_type, is_next)
            if not IsEmptyTable(data) then
                table.insert(big_type_list, data)
            end
        end
    end

    return big_type_list
end

-- 通过活动号获得外层按钮展示配置表
function EverythingUnderTheSunWGData:GetBigTypeCfgByType(act_type)
    return self.big_type_info[act_type] or {}
end

-- 通过具体显示内容index获得内层按钮展示配置表
function EverythingUnderTheSunWGData:GetSmallTypeCfgByType(small_type, is_next)
    if self.show_cfg[small_type] then
        local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
        local day = is_next and cur_day + 1 or cur_day
        for k, v in pairs(self.show_cfg[small_type]) do
            if day >= v.start_day and day <= v.off_day then
                return v
            end
        end

    end

    return {}
end

-- 获得当天外层要显示列表
function EverythingUnderTheSunWGData:GetShowAccordionList()
    local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local cur_day_data = self:GetAccordionList(cur_day)         -- 获得要显示的活动列表
    local next_day_data = self:GetAccordionList(cur_day + 1)
    local show_list = {}

    -- 遍历，判断活动是否开启再插入
    for k, data in pairs(cur_day_data) do
        local is_open = ActivityWGData.Instance:GetActivityIsOpen(data.act_type)
        if is_open then
            data.is_next = false
            table.insert(show_list, data)
            if not self.open_record_list[data.act_type] then    -- 只记录当天开启的活动，不记录明日预告
                self.open_record_list[data.act_type] = 1
            end
        end
    end

    for k, data in pairs(next_day_data) do
        if not self.open_record_list[data.act_type] and data.info.is_pre_hide == 0 then
            data.is_next = true
            table.insert(show_list, data)
        end
    end

    -- 优先当天排前，其次根据表字段sort大小
    table.sort(show_list, function(a, b)
            local a_sort = a.is_next and a.info.sort + 1000 or a.info.sort
            local b_sort = b.is_next and b.info.sort + 1000 or b.info.sort

            return a_sort < b_sort
		end)

    return show_list
end

function EverythingUnderTheSunWGData:ResetRecordList()
    self.open_record_list = {}
end

function EverythingUnderTheSunWGData:GetIsShowActivity(act_type)
    local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local cur_day_data = self:GetAccordionList(cur_day)
    for k, v in pairs(cur_day_data) do
        if v.act_type == act_type then
            return true
        end
    end

    return false
end

function EverythingUnderTheSunWGData:GetIsNextDayActivity(act_type)
    if self.open_record_list[act_type] then
        return false
    end

    local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local cur_day_data = self:GetAccordionList(cur_day + 1)
    for k, v in pairs(cur_day_data) do
        if v.act_type == act_type then
            return true
        end
    end

    return false
end

function EverythingUnderTheSunWGData:GetModelDataBySmallType(small_type, is_next)
    if self.save_model_list[small_type] then
        return self.save_model_list[small_type]
    end

    self.save_model_list[small_type] = self:CreateModelData(small_type, is_next)
    return self.save_model_list[small_type]
end

function EverythingUnderTheSunWGData:CreateModelData(small_type, is_next)
    local show_data = self:GetSmallTypeCfgByType(small_type, is_next)
    local new_mode_data = {}
    if IsEmptyTable(show_data) then
        return new_mode_data
    end

    for i = 1, 3 do
        local display_data = {}
        local model_show_type = show_data["model_show_type" .. i]

        if model_show_type == 0 then
            break
        end

        local model_show_itemid = show_data["model_show_itemid" .. i]
        local model_bundle_name = show_data["model_bundle_name" .. i]
        local model_asset_name = show_data["model_asset_name" .. i]

        display_data.should_ani = true
        if model_show_itemid ~= 0 and model_show_itemid ~= "" then
            local split_list = Split(model_show_itemid, "|")
            if #split_list > 1 then
                local list = {}
                for k, v in pairs(split_list) do
                    list[tonumber(v)] = true
                end
                display_data.model_item_id_list = list
            else
                display_data.item_id = model_show_itemid
            end
        end

        display_data.hide_model_block = false
        display_data.bundle_name = model_bundle_name
        display_data.asset_name = model_asset_name
        display_data.render_type = model_show_type - 1
        display_data.need_wp_tween = true

        if model_show_type == 1 and model_bundle_name and model_bundle_name ~= "" then
            display_data.need_wp_tween = false
        end

        local transform_info = {}
        local display_pos = show_data["display_pos" .. i]
        local display_rotation = show_data["display_rotation" .. i]
        local display_scale = show_data["display_scale" .. i]

        local pos_x, pos_y = 0, 0
	    if display_pos and display_pos ~= "" then
	    	local pos_list = Split(display_pos, "|")
	    	pos_x = tonumber(pos_list[1]) or pos_x
	    	pos_y = tonumber(pos_list[2]) or pos_y
	    end

        transform_info.pos_x = pos_x
	    transform_info.pos_y = pos_y

        local rot_x, rot_y, rot_z = 0, 0, 0
	    if display_rotation and display_rotation ~= "" then
	    	local rot_list = Split(display_rotation, "|")
	    	rot_x = tonumber(rot_list[1]) or rot_x
	    	rot_y = tonumber(rot_list[2]) or rot_y
	    	rot_z = tonumber(rot_list[3]) or rot_z
	    end

        transform_info.rot_x = rot_x
	    transform_info.rot_y = rot_y
	    transform_info.rot_z = rot_z

        local scale = display_scale
        transform_info.scale = (scale and scale ~= "" and scale > 0) and scale or 1

        new_mode_data[i] = {}
        new_mode_data[i].display_data = display_data
        new_mode_data[i].transform_info = transform_info
    end

    return new_mode_data
end