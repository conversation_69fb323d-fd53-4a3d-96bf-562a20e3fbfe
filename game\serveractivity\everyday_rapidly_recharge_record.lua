EveryDayRechargeViewRecordTip = EveryDayRechargeViewRecordTip or BaseClass(SafeBaseView)

function EveryDayRechargeViewRecordTip:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)

	self:AddViewResource(0, "uis/view/rechargereward_ui_prefab", "everyday_rapidly_recharge_record")
end

function EveryDayRechargeViewRecordTip:ReleaseCallBack()
	if self.record_list then
		self.record_list:DeleteMe()
		self.record_list = nil
	end
end

function EveryDayRechargeViewRecordTip:LoadCallBack()
	self.toggle_index = 1

	self.node_list.title_view_name.text.text = Language.EverydayRecharge.RapidlyRecordTitle

	self.node_list.btn_buy.toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self, 1))
	self.node_list.btn_draw.toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self, 2))
	self.record_list = AsyncListView.New(EveryDayRechargeViewRecordItem, self.node_list["role_list"])

	self.node_list.btn_buy.toggle.isOn = true
end

function EveryDayRechargeViewRecordTip:OnClickSwitch(state, is_on)
	if is_on and state then
		self.toggle_index = state
		self:FlushRecordList()
	end
end

function EveryDayRechargeViewRecordTip:OnFlush(param_t)
	self:FlushRecordList()
end

function EveryDayRechargeViewRecordTip:FlushRecordList()
	local data_list = ServerActivityWGData.Instance:GetEveryDayRapidlyGiftRecordInfo(self.toggle_index)
	local is_show_list = not IsEmptyTable(data_list)
	if is_show_list then
		self.record_list:SetDataList(data_list)
	end
	self.node_list["role_list"]:SetActive(is_show_list)
	self.node_list["no_invite"]:SetActive(not is_show_list)

	self.node_list.txt_no_info.text.text = Language.EverydayRecharge.TxtRecordEmptyType[self.toggle_index]
end

EveryDayRechargeViewRecordItem = EveryDayRechargeViewRecordItem or BaseClass(BaseRender)

function EveryDayRechargeViewRecordItem:OnFlush()
	local index = self:GetIndex()
	local mark = (index % 2) == 1
	self.node_list["root_bg"]:SetActive(mark)

	if not self.data then
		return
	end

	local rapidly_cfg = ServerActivityWGData.Instance:GetRapidlyGiftCfg()
	if not rapidly_cfg then
		return
	end

	local item_name = ""
	--随机礼包ID.
	if rapidly_cfg[self.data.item_id] and rapidly_cfg[self.data.item_id].id == self.data.item_id then
			item_name = string.format(Language.EverydayRecharge.TxtRecord1_2, "#3c8652", rapidly_cfg[self.data.item_id].item_name)
	else
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		if item_cfg then
			local color = ITEM_COLOR_LIGHT[item_cfg.color]
			item_name = string.format(Language.EverydayRecharge.TxtRecord1_2, color, item_cfg.name)
		end
	end

	local role_name = self.data.role_name or RoleWGData.Instance:GetAttr("name")
	local str1 = string.format(Language.EverydayRecharge.TxtRecord1, role_name,
		Language.EverydayRecharge.TxtRecordType[self.data.type_index])
	self.node_list["desc"].text.text = str1
	self.node_list["txt_btn"].text.text = item_name
end
