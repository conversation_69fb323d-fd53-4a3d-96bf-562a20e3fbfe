TSShenLingDianStarAttrView = TSShenLingDianStarAttrView or BaseClass(SafeBaseView)

function TSShenLingDianStarAttrView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/tianshen_prefab", "layout_tianshen_shendian_star_attr")
end

function TSShenLingDianStarAttrView:ReleaseCallBack()
	if self.now_attr then
		self.now_attr:DeleteMe()
		self.now_attr = nil
	end

	if self.next_attr then
		self.next_attr:DeleteMe()
		self.next_attr = nil
	end

	self.now_shendian_seq = nil
end

function TSShenLingDianStarAttrView:SetData(shendian_seq)
	self.now_shendian_seq = shendian_seq
	self:Open()
end

function TSShenLingDianStarAttrView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.TianShen.TSShenDianStarAttrTitle

	if not self.now_attr then
		self.now_attr = ShenLingDaianStarAttrRender.New(self.node_list.now_attr)
	end

	if not self.next_attr then
		self.next_attr = ShenLingDaianStarAttrRender.New(self.node_list.next_attr)
	end
end

function TSShenLingDianStarAttrView:OnFlush(param_t)
	if not self.now_shendian_seq then
		return
	end

	-- 获取列表当前的和下一个
	local star_num = TSShenLingDianData.Instance:GetHallAllStarNumBySeq(self.now_shendian_seq)
	local list = TSShenLingDianData.Instance:GetHallAttrListBySeqStar(self.now_shendian_seq, star_num)
	self.now_attr:SetShenLingDaianStarNum(star_num)
	self.now_attr:SetData(list[1])
	self.node_list.next_attr:CustomSetActive(list[2] ~= nil)
	self.node_list.next_full_root:CustomSetActive(list[2] == nil)
	
	if list[2] ~= nil then
		self.next_attr:SetShenLingDaianStarNum(star_num)
		self.next_attr:SetData(list[2])
	end
end

-------------------------------------------------------------------------------------------------------------------
ShenLingDaianStarAttrRender = ShenLingDaianStarAttrRender  or BaseClass(BaseRender)
function ShenLingDaianStarAttrRender:LoadCallBack()
    -- 升阶属性
    if self.attr_list == nil then
        self.attr_list = {}
        for i = 1, 8 do
            local attr_obj = self.node_list.attr_list:FindObj(string.format("attr_%d", i))
            if attr_obj then
                local cell = CommonAddAttrRender.New(attr_obj)
                cell:SetIndex(i)
                cell:SetAttrNameNeedSpace(true)
                self.attr_list[i] = cell
            end
        end
    end
end

function ShenLingDaianStarAttrRender:ReleaseCallBack()
    if self.attr_list and #self.attr_list > 0 then
		for _, cell in ipairs(self.attr_list) do
			cell:DeleteMe()
			cell = nil
		end

		self.attr_list = nil
	end

	self.shendian_star = nil
end

function ShenLingDaianStarAttrRender:SetShenLingDaianStarNum(shendian_star)
	self.shendian_star = shendian_star
end

function ShenLingDaianStarAttrRender:OnFlush()
	if not self.data then 
		return 
	end

	local star_num = self.shendian_star or 0
	local desc_color = star_num >= self.data.star_num and COLOR3B.GREEN or COLOR3B.RED
	local color = star_num >= self.data.star_num and COLOR3B.GREEN or COLOR3B.WHITE
	local str = ToColorStr(string.format("(%d/%d)", star_num, self.data.star_num), desc_color)
	self.node_list.attr_desc.text.text = ToColorStr(string.format(Language.TianShen.TSShenDianStarAttrDesc, self.data.star_num, str), color)
    local list, _ = TSShenLingDianData.Instance:OutStrAttrListAndCapalityByAttrId(self.data, nil, "attr_id", "attr_value", 1, 5)
	
    for i, cell in ipairs(self.attr_list) do
        local data = list[i]
        cell:SetVisible(data ~= nil)

        if data ~= nil then
			cell:SetAttrSpecialColor(color)
			cell:SetData(data)
        end
    end

end