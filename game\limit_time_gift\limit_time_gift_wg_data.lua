LimitTimeGiftWGData = LimitTimeGiftWGData or BaseClass()

LIMIT_TIME_GIFT_POPUP_TYPE = 
{
	NO_GOLD_POPUP = 1,				-- 货币不足(少于定义的数量)
	OPEN_VIEW_POPUP = 3,			-- 打开窗口
	CULTIVATION_POPUP = 6,			-- 修为丹不足
	DRAW_COUNT_POPUP= 12,			-- 抽奖次数不足（1.幻兽 2.神格 3.寻宝）
	NO_FISHION_POPUP = 14,			-- 只要外观货币不足就弹
	NO_GOLD_POPUP2 = 15,			-- 只要货币不足就弹
	AFTER_DRAW = 16,				-- 合服活动-陨落星辰-首次抽奖会弹.
	ZHUANZHI_FB = 17,               -- 转职副本失败
	EQUIP_SUIT_HARMONY = 19,        -- 装备合鸣
}

LIMIT_TIME_GIFT_POPUP_DRAW_TYPE = {
	POPUP_DRAW_BEASTS = 1,				--	幻兽
	POPUP_DRAW_TS_EQUIP = 2,			--	神格
	POPUP_DRAW_FIND_WARD = 3,			-- 	寻宝
}

function LimitTimeGiftWGData:__init()
	if LimitTimeGiftWGData.Instance then
		error("[LimitTimeGiftWGData]:Attempt to create singleton twice!")
		return
	end
	LimitTimeGiftWGData.Instance = self

	local cfg_auto = ConfigManager.Instance:GetAutoConfig("limittimegift_auto")
	-- self.gift_info_cfg_list = ListToMapList(cfg_auto.gift_info, "gift_id")
	self.gift_info_cfg_list = cfg_auto.gift_info -- 应该全是连号的
	self.gift_info_cfg_list_2 = ListToMap(cfg_auto.gift_info2, "gift_id") -- 新增重复上架礼包,id从1000开始
	
	self.pass_tip_time = 300 -- 5分钟内过期弹提示
	self.gift_info_list = {}
	self.act_status = 0

	self:InitPurchaseCfg()
	self:InitGradeData()
	self:RegisterRemind()
end

function LimitTimeGiftWGData:__delete()
	self.gift_info_list = nil
	self:UnRegisterRemind()
	LimitTimeGiftWGData.Instance = nil
end

function LimitTimeGiftWGData:RegisterRemind()
	RemindManager.Instance:Register(RemindName.LimitTimeGift, BindTool.Bind(self.IsShowRedPoint, self))	-- 活动主界面红点
	RemindManager.Instance:Register(RemindName.LimitTimeGiftPurchase, BindTool.Bind(self.IsShowPurchaseRedPoint, self))	-- 活动主界面红点
end

function LimitTimeGiftWGData:UnRegisterRemind()
	RemindManager.Instance:UnRegister(RemindName.LimitTimeGift)
	RemindManager.Instance:UnRegister(RemindName.LimitTimeGiftPurchase)
end

---[[ 协议数据
-- 8300 登录的时候下发一次总的后端所有开启过的礼包信息
function LimitTimeGiftWGData:SetGiftInfoList(protocol)
	local info_list = protocol.gift_info_list
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local temp_list = {}
	local cfg_data = nil
	for _,v in pairs(info_list) do
		cfg_data = self:GetGiftCfgByGiftID(v.gift_id)
		if cfg_data and v.gift_timestamp > server_time then
			v.can_buy_num = cfg_data.num - v.gift_num
			v.is_eject = cfg_data.is_eject
			temp_list[v.gift_id] = v
		end
	end
	self.gift_info_list = temp_list
end

function LimitTimeGiftWGData:SetChangeGiftInfoList(protocol)
	local info_list = protocol.gift_info_list
	local cfg_data = nil
	local temp_gift_id = nil
	for _,v in pairs(info_list) do
		cfg_data = self:GetGiftCfgByGiftID(v.gift_id)
		if cfg_data then
			v.can_buy_num = cfg_data.num - v.gift_num
			v.is_eject = cfg_data.is_eject
			temp_gift_id = self:GetGiftTrueId(v.gift_id, v.index)
			self.gift_info_list[temp_gift_id] = v
		end
	end
end

--礼包id相同,用index区分
function LimitTimeGiftWGData:GetGiftTrueId(gift_id, index)
	if not gift_id then
		return 0
	end
	if not index or index < 0 then
		return gift_id
	else
		return gift_id + index
	end
end

function LimitTimeGiftWGData:CleanNotBuyGiftInfo()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if self.gift_info_list then
		local temp_list = {}
		for k,v in pairs(self.gift_info_list) do
			if v.gift_timestamp > server_time and v.can_buy_num > 0 then
				temp_list[k] = v
			end
		end
		self.gift_info_list = temp_list
	end
end

function LimitTimeGiftWGData:GetGiftInfoList()
	return self.gift_info_list
end


function LimitTimeGiftWGData:GetGiftInfoByGiftId(gift_id)
	return self.gift_info_list[gift_id]
end

--]]

---[[ 表格数据
function LimitTimeGiftWGData:GetGiftInfoCfgList()
	return self.gift_info_cfg_list
end

function LimitTimeGiftWGData:GetGiftCfgByGiftID(gift_id)
	gift_id = gift_id or 0
	if gift_id >= 1000 then
		if self.gift_info_cfg_list_2 then
			return self.gift_info_cfg_list_2[gift_id]
		end
	else
		if self.gift_info_cfg_list then
			return self.gift_info_cfg_list[gift_id]
		end
	end
end
--]]

function LimitTimeGiftWGData:GetPassTipTime()
	return self.pass_tip_time or 0
end

function LimitTimeGiftWGData:SetActStatus(status)
	self.act_status = status
end

function LimitTimeGiftWGData:GetActStatus()
	return self.act_status
end

-- 有新的礼包提示红点
function LimitTimeGiftWGData:IsShowRedPoint()
	local gift_info_list = self:GetGiftInfoList()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local temp_gift_id = nil
	if gift_info_list then
		for _,v in pairs(gift_info_list) do
			if v.gift_timestamp > server_time and v.can_buy_num > 0 then
				temp_gift_id = self:GetGiftTrueId(v.gift_id, v.index)
				if self:GetGiftRemindByID(temp_gift_id) then
					return 1
				end
			end
		end
	end
	return 0
end

-- 有没有能买的限时礼包
function LimitTimeGiftWGData:HasCanBuyGift()
	local gift_info_list = self:GetGiftInfoList()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if gift_info_list then
		for _,v in pairs(gift_info_list) do
			if v.can_buy_num > 0 and v.gift_timestamp > server_time then
				return true
			end
		end
	end
end

function LimitTimeGiftWGData:GetGiftRemindByID(gift_id, is_pass)
	local uuid = RoleWGData.Instance:GetUUid()
	local mark = is_pass and "pass_gift" or "gift_red_point"
	local key = string.format("%s%s%s%s", uuid.temp_low, uuid.temp_high, mark, gift_id)
	local save_day = PlayerPrefsUtil.GetInt(key)
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	return not save_day or cur_day ~= save_day
end

function LimitTimeGiftWGData:SetGiftRemindByID(gift_id, is_pass)
	local uuid = RoleWGData.Instance:GetUUid()
	local mark = is_pass and "pass_gift" or "gift_red_point"
	local key = string.format("%s%s%s%s", uuid.temp_low, uuid.temp_high, mark, gift_id)
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	PlayerPrefsUtil.SetInt(key, cur_day)
end

function LimitTimeGiftWGData:GetActIsOpen()
	local is_open = false
	local end_time = 0
	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.LIMIT_TIME_GIFT)
	local role_level = RoleWGData.Instance:GetAttr("level")
	if not act_cfg or role_level < act_cfg.level or role_level > act_cfg.level_max then
		return is_open, end_time
	end

	local gift_info_list = self:GetGiftInfoList()
	if gift_info_list then
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		for _,v in pairs(gift_info_list) do
			if v.gift_timestamp > server_time and v.can_buy_num and v.can_buy_num > 0 then
				if v.gift_timestamp < end_time or end_time == 0 then -- 取最短的计时
					is_open = true
					end_time = v.gift_timestamp
					break
				end
			end
		end
	end

	return is_open, end_time
end


-------------------------------------直购礼包------------------------------------------------------------
-- 初始化直购礼包配置
function LimitTimeGiftWGData:InitPurchaseCfg()
	local cfg_auto = ConfigManager.Instance:GetAutoConfig("popup_gift_cfg_auto")
	if cfg_auto then
		self.gift_cfg = ListToMap(cfg_auto.gift, "gift_grade", "gift_id")
		self.condition_cfg = cfg_auto.condition
	end

	self.popupgift_list = {}
	self.open_day_grade_flag = {}
end

-- 初始化当前数据
function LimitTimeGiftWGData:InitGradeData()
	self.gift_data_list = {}
end

function LimitTimeGiftWGData:GetConditionCfgByGrade(gift_grade)
	return self.condition_cfg[gift_grade]
end
-- 创建展示数据
function LimitTimeGiftWGData:CreateShowData()
	local info = {}
	info.grade = 0
	info.end_time = 0
	info.gift = nil
	info.gift_data = nil
	info.title_name = ""
	return info
end

-- 刷新弹出数据(服务器)
function LimitTimeGiftWGData:RefreshPopupGiftAllData(protocol)
	self.popupgift_list = protocol.popupgift_list
	self.open_day_grade_flag = protocol.open_day_grade_flag
	self:RefreshCurrGradeData()
end

-- 刷新弹出数据(服务器)
function LimitTimeGiftWGData:RefreshPopupGiftOneData(protocol)
	self.popupgift_list[protocol.index + 1] = protocol.popupgift
	self.open_day_grade_flag = protocol.open_day_grade_flag
	self:RefreshCurrGradeData()
end

-- 刷新礼包数据
function LimitTimeGiftWGData:RefreshCurrGradeData()
	if not self.popupgift_list or not self.gift_cfg then
		return
	end

	self.gift_data_list = {}
	for k, v in pairs(self.popupgift_list) do
		if v.grade ~= 0 then
			local condition_cfg = self.condition_cfg[v.grade]
			local gift_cfg = self.gift_cfg[v.grade]
			if not condition_cfg or not gift_cfg then
				print_error("检查限时礼包配置是否有问题！grade: " .. v.grade)
				return
			end

			local show_data = self:CreateShowData()
			show_data.grade = v.grade
			show_data.end_time = v.end_time
			show_data.gift = v.gift
			show_data.title_name = condition_cfg.gift_type
			show_data.remind_time = condition_cfg.remind_time
			show_data.popup_type = condition_cfg.popup_type
		
			local gift_data = {}
			local has_not_buy = false
			for _, cfg_data in pairs(gift_cfg) do
				local temp_data = {}
				temp_data.title_name = cfg_data.gift_name
				temp_data.title_img_res = cfg_data.title_img_res
				temp_data.discount = cfg_data.gift_dis
				local price = RoleWGData.GetPayMoneyStr(cfg_data.RMB, cfg_data.rmb_type, cfg_data.rmb_seq)
				temp_data.price = price
				temp_data.RMB = cfg_data.RMB
				temp_data.rmb_type = cfg_data.rmb_type
				temp_data.rmb_seq = cfg_data.rmb_seq
		
				local buy_num = show_data.gift and show_data.gift[cfg_data.gift_id + 1] or 0
				local is_limit = buy_num >= cfg_data.num
				temp_data.is_buy = is_limit
				if not is_limit then
					has_not_buy = true
				end
		
				local item_list = {}
				if cfg_data.reward_item then
					for key, item_data in pairs(cfg_data.reward_item) do
						table.insert(item_list, item_data)
					end
				end
		
				temp_data.item_list = item_list
				table.insert(gift_data, temp_data)
			end
			show_data.gift_data = gift_data
			if has_not_buy then
				table.insert(self.gift_data_list, show_data)
			end
		end
	end
end

-- 获取当前的是否还存在可以购买的东西
function LimitTimeGiftWGData:CkeckCurrGradeCanBuy()
	if IsEmptyTable(self.gift_data_list) then return false end
	for _, v in pairs(self.gift_data_list) do
		for _, data in ipairs(v.gift_data) do
			if not data.is_buy then
				return true
			end
		end
	end
	return false
end

-- 获取最短剩余时间
function LimitTimeGiftWGData:GetPopupGiftEndTime()
	local min_end_time = -1
	for k, v in pairs(self.gift_data_list) do
		if min_end_time == -1 then
			min_end_time = v.end_time
		else
			min_end_time = math.min(min_end_time, v.end_time)
		end
	end

	return min_end_time
end

-- 获取当前的礼包列表
function LimitTimeGiftWGData:GetCurrShowDataList()
	return self.gift_data_list
end

--今日弹窗标记

function LimitTimeGiftWGData:GetCurOpenDayGradeFlag()
	return self.open_day_grade_flag
end
--- 展示直购礼包红点
function LimitTimeGiftWGData:IsShowPurchaseRedPoint()
	return 0	
end

--- 检测直购礼包是否满足灵玉不足的条件
function LimitTimeGiftWGData:CheckNeedNoGoldPopupGift()
	if not self.condition_cfg then
		return false, 0
	end

	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or 0   -- 开服天数
	local role_lv = RoleWGData.Instance:GetRoleLevel() or 0
	for _, condition_data in pairs(self.condition_cfg) do
		if condition_data and condition_data.popup_type == LIMIT_TIME_GIFT_POPUP_TYPE.NO_GOLD_POPUP and open_day == condition_data.open_day and role_lv >= condition_data.role_level then
			return true, condition_data.gift_grade
		end
	end

	return false, 0
end

--- 检测直购礼包是否满足灵玉不足的条件(不足就弹)
function LimitTimeGiftWGData:CheckNeedNoGoldPopupGift2()
	if not self.condition_cfg then
		return false, 0
	end

	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or 0   -- 开服天数
	local role_lv = RoleWGData.Instance:GetRoleLevel() or 0
	for _, condition_data in pairs(self.condition_cfg) do
		if condition_data and condition_data.popup_type == LIMIT_TIME_GIFT_POPUP_TYPE.NO_GOLD_POPUP2 and open_day == condition_data.open_day and role_lv >= condition_data.role_level then
			return true, condition_data.gift_grade
		end
	end

	return false, 0
end

--- 检测直购礼包是否满足活动中道具不足的条件
function LimitTimeGiftWGData:CheckNeedOpenViewPopupGift(act_id)
	if not self.condition_cfg then
		return false, 0
	end

	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or 0   -- 开服天数
	local role_lv = RoleWGData.Instance:GetRoleLevel() or 0

	for _, condition_data in pairs(self.condition_cfg) do
		
		if condition_data and condition_data.popup_type == LIMIT_TIME_GIFT_POPUP_TYPE.OPEN_VIEW_POPUP and open_day == condition_data.open_day and 
		role_lv >= condition_data.role_level and condition_data.popup_condition == act_id then
			local item_num = ItemWGData.Instance:GetItemNumInBagById(condition_data.popup_condition2)
			if item_num <= 0 then
				return true, condition_data.gift_grade
			end
		end
	end
	
	return false, 0
end

--- 检测直购礼包是否满足修为丹不足的条件
function LimitTimeGiftWGData:CheckNeedCultivationPopupGift()
	if not self.condition_cfg then
		return false, 0
	end

	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or 0   -- 开服天数
	local role_lv = RoleWGData.Instance:GetRoleLevel() or 0
	for _, condition_data in pairs(self.condition_cfg) do
		if condition_data and condition_data.popup_type == LIMIT_TIME_GIFT_POPUP_TYPE.CULTIVATION_POPUP and open_day == condition_data.open_day and 
		role_lv >= condition_data.role_level then
			-- 检测当前修为丹是否不足
			local cur_stage_cfg = CultivationWGData.Instance:GetCurXiuWeiStageCfg()
			if cur_stage_cfg then
				local item_id = cur_stage_cfg.cost_stage_item and cur_stage_cfg.cost_stage_item.item_id or 0
				local cost_num = cur_stage_cfg.cost_stage_item and cur_stage_cfg.cost_stage_item.num or 0
				local item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
				if item_num < cost_num then
					return true, condition_data.gift_grade
				end
			end
		end
	end
	
	return false, 0
end

--- 检测直购礼包是否满足抽奖道具不足的条件
function LimitTimeGiftWGData:CheckNeedDrawRewardPopupGift(popup_draw_type)
	if not self.condition_cfg then
		return false, 0
	end

	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or 0   -- 开服天数
	local role_lv = RoleWGData.Instance:GetRoleLevel() or 0
	for _, condition_data in pairs(self.condition_cfg) do
		if condition_data and condition_data.popup_type == LIMIT_TIME_GIFT_POPUP_TYPE.DRAW_COUNT_POPUP and open_day == condition_data.open_day and 
		role_lv >= condition_data.role_level and condition_data.popup_condition == popup_draw_type then
			local item_num = ItemWGData.Instance:GetItemNumInBagById(condition_data.popup_condition2)
			if item_num <= 0 then
				return true, condition_data.gift_grade
			end
		end
	end
	
	return false, 0
end

--- 检测直购礼包是否外观货币不足的条件
function LimitTimeGiftWGData:CheckNeedFishionGoldPopupGift()
	if not self.condition_cfg then
		return false, 0
	end

	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or 0   -- 开服天数
	local role_lv = RoleWGData.Instance:GetRoleLevel() or 0
	for _, condition_data in pairs(self.condition_cfg) do
		if condition_data and condition_data.popup_type == LIMIT_TIME_GIFT_POPUP_TYPE.NO_FISHION_POPUP and open_day == condition_data.open_day and 
		role_lv >= condition_data.role_level then
			return true, condition_data.gift_grade
		end
	end
	
	return false, 0
end

--- 检测直购礼包是否首次抽奖的条件
function LimitTimeGiftWGData:CheckAfterDrawPopupGift()
	if not self.condition_cfg then
		return false, 0
	end

	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or 0   -- 开服天数
	local role_lv = RoleWGData.Instance:GetRoleLevel() or 0
	for _, condition_data in pairs(self.condition_cfg) do
		if condition_data and condition_data.popup_type == LIMIT_TIME_GIFT_POPUP_TYPE.AFTER_DRAW and open_day == condition_data.open_day and 
		role_lv >= condition_data.role_level then
			return true, condition_data.gift_grade
		end
	end
	
	return false, 0
end

function LimitTimeGiftWGData:CheckNeedEquipSuitHarmonyPopupGift(gift_grade)
	local target_cfg = (self.condition_cfg or {})[gift_grade]

	if IsEmptyTable(target_cfg) then
		return false, 0
	end

	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or 0   -- 开服天数
	local role_lv = RoleWGData.Instance:GetRoleLevel() or 0
	if target_cfg.popup_type == LIMIT_TIME_GIFT_POPUP_TYPE.EQUIP_SUIT_HARMONY  
		and open_day >= target_cfg.open_day and open_day <= target_cfg.end_day 
		and role_lv >= target_cfg.role_level then
		return true, target_cfg.gift_grade
	end

	return false, 0
end

--- 检测转职失败的条件
function LimitTimeGiftWGData:CheckZhuanZhiPopupGift()
	if not self.condition_cfg then
		return false, 0
	end

	local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or 0   -- 开服天数
	local role_lv = RoleWGData.Instance:GetRoleLevel() or 0
	for _, condition_data in pairs(self.condition_cfg) do
		if condition_data and condition_data.popup_type == LIMIT_TIME_GIFT_POPUP_TYPE.ZHUANZHI_FB and 
		(open_day == condition_data.open_day or condition_data.open_day <= 0) and 
		role_lv >= condition_data.role_level and 
		condition_data.popup_condition == prof_level + 1 then
			return true, condition_data.gift_grade
		end
	end
	
	return false, 0
end