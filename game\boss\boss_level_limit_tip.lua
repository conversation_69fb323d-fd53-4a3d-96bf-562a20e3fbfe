BossLevelLimitTip = BossLevelLimitTip or BaseClass(SafeBaseView)
BossLevelLimitTip.Delay = 3
function BossLevelLimitTip:__init()
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "boss_level_limit_tip")
	self.view_layer = UiLayer.MainUI
    self.close_tween_time = 0
end

function BossLevelLimitTip:ReleaseCallBack()
    self.close_tween_time = 0
    self.tween_anim = nil
end

function BossLevelLimitTip:LoadCallBack()

end

function BossLevelLimitTip:ShowIndexCallBack()
    if self.tween_anim then
        self.tween_anim:Kill()
    end

    self.close_tween_time = 0
    if self.tween_time_daly then
        GlobalTimerQuest:CancelQuest(self.tween_time_daly)
    end
    self.tween_time_daly = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.UpdateTime, self), 0.4)
end

function BossLevelLimitTip:OnFlush(param)
    for i, v in pairs(param) do
        if i == "time" then
            self.close_tween_time = v.time + BossLevelLimitTip.Delay
        end
    end
end

function BossLevelLimitTip:UpdateTime()
    if self.close_tween_time < Status.NowTime then
        self:DoEndAnim()
    end
end

function BossLevelLimitTip:DoEndAnim()
    if self.tween_time_daly then
        GlobalTimerQuest:CancelQuest(self.tween_time_daly)
        self.tween_time_daly = nil
    end

    self.tween_anim = self.node_list["tip"].canvas_group:DoAlpha(1, 0, 0.5)
    self.tween_anim:OnComplete(function()
        self:Close()
    end)
end