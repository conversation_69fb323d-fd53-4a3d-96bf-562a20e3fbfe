QifuYunShiResultView = QifuYunShiResultView or BaseClass(SafeBaseView)

function QifuYunShiResultView:__init()
    self:SetMaskBg(true,true)
    self:AddViewResource(0, "uis/view/welfare_ui_prefab", "layout_yunshi_result_view")
end

function QifuYunShiResultView:__delete()
end

function QifuYunShiResultView:ReleaseCallBack()
	if self.yunshi_list_view then
		self.yunshi_list_view:DeleteMe()
		self.yunshi_list_view = nil
	end
	self.ske_graphic_chouqian = nil

	if self.anim_tween then
		self.anim_tween:Kill()
		self.anim_tween = nil
	end
end

function QifuYunShiResultView:CloseCallBack()
	if self.anim_tween then
		self.anim_tween:Kill()
		self.anim_tween = nil
	end

	if self.chouqian_sequence then
		self.chouqian_sequence:Kill()
		self.chouqian_sequence = nil
	end

	if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end

	self.paly_anim_end = false
	QiFuWGCtrl.Instance:FlushQiFuView(TabIndex.qifu_yunshi)
	local qifu_view = QiFuWGCtrl.Instance.view
	if qifu_view and qifu_view:IsLoadedIndex(TabIndex.qifu_yunshi) then
		qifu_view:SetYunShiActive(false)
	end
end

function QifuYunShiResultView:LoadCallBack()
	self.yunshi_list_view = AsyncListView.New(YunShiListItem,self.node_list["yunshi_list_view"])
	self.paly_anim_end = false
	if not self.ske_graphic_chouqian then
        self.ske_graphic_chouqian = self.node_list["chouqian_img_anim"].gameObject:GetComponent("SkeletonGraphic")
    end

    if self.ske_graphic_chouqian and self.ske_graphic_chouqian.AnimationState then
    	self.ske_graphic_chouqian.AnimationState:SetAnimation(0, "stand", false)
    end
end

function QifuYunShiResultView:ShowIndexCallBack()
	local ignoire_anim_flag = QifuYunShiWGData.Instance:GetIgnoireAnimFlag()
	if ignoire_anim_flag then
		self.paly_anim_end = true
		return
	end
	self:PlayAnim()
end

local YunShiColor = {
	[1] = "#0098d1",
	[2] = "#8000bc",
	[3] = "#bc5400",
	[4] = "#f10c14",
}
function QifuYunShiResultView:OnFlush()
	local fortune_info = QifuYunShiWGData.Instance:GetFortuneInfo()
	local fortune_type = fortune_info.fortune_type
	local add_list = QifuYunShiWGData.Instance:GetCurMyFortuneAdditionCfg()
	self.yunshi_list_view:SetDataList(add_list)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local time_str = TimeUtil.FormarDaXieTimeYMD(server_time)
	self.node_list["yunshi_timer"].text.text = time_str
	local fortune_cfg = QifuYunShiWGData.Instance:GetFortuneTypeCfg(fortune_type)
	if not IsEmptyTable(fortune_cfg) then
		self.node_list["yunshi_name"].image:LoadSprite(ResPath.GetF2WelfareImages("yunshi_img"..fortune_type))
		self.node_list["yunshi_name"].image:SetNativeSize()
		self.node_list["qian_yunshi_name"].text.text = ToColorStr(fortune_cfg.name,YunShiColor[fortune_type])
		self.node_list["best_img"]:SetActive(fortune_cfg.is_best == 1 and self.paly_anim_end)
	end
end

function QifuYunShiResultView:ChouQianAnim()
    self.node_list["chouqian_img_anim"]:SetActive(true)
	self.node_list["yunshi_content"]:SetActive(false)

	local tween_scale = self.node_list["chouqian_img_anim"].transform:DOScale(1.3, 0.5)

	if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end

	self.sequence = DG.Tweening.DOTween.Sequence()
    self.sequence:SetEase(DG.Tweening.Ease.Linear)
    self.sequence:Append(tween_scale)
	self.sequence:OnComplete(function ()
		if self.ske_graphic_chouqian and self.ske_graphic_chouqian.AnimationState then
	    	self.ske_graphic_chouqian.AnimationState:SetAnimation(0, "action", true)
	    end
		TryDelayCall(self, function ()
			self:YunShiAnim()
		end, 1, "chouqian_img_anim_delay")
	end)

end

function QifuYunShiResultView:YunShiAnim()
	-- self.node_list["chouqian_img"]:SetActive(false)
	self.node_list["chouqian_img_anim"]:SetActive(false)
	self.node_list["yunshi_content"]:SetActive(true)
	if self.ske_graphic_chouqian and self.ske_graphic_chouqian.AnimationState then
    	self.ske_graphic_chouqian.AnimationState:SetAnimation(0, "stand", false)
    end

	local qian_obj = self.node_list["qian_img"].gameObject
	local qian_obj_x, qian_obj_y = qian_obj.transform.anchoredPosition.x, qian_obj.transform.anchoredPosition.y
	local qian_obj_start_rotation = u3dpool.vec3(0, 0, 10)
	qian_obj.transform.localRotation = u3dpool.vec3(0, 0, 0)
	qian_obj.transform.localScale = u3dpool.vec3(0, 0, 0)
	qian_obj.transform.anchoredPosition = u3dpool.vec3(0, -12, 0)

	local yunshi_anim_obj = self.node_list["yunshi_anim_root"].gameObject
	local yunshi_anim_obj_x, yunshi_anim_obj_y = yunshi_anim_obj.transform.anchoredPosition.x, yunshi_anim_obj.transform.anchoredPosition.y
	yunshi_anim_obj.transform.anchoredPosition = u3dpool.vec3(0, 740, 0)

	local best_img_obj = self.node_list["best_img"].gameObject
	best_img_obj:SetActive(false)	
	local yunshi_name_obj = self.node_list["yunshi_name"].gameObject
	yunshi_name_obj:SetActive(false)	

	local qian_tween1 = qian_obj.transform:DOScale(Vector3(1, 1, 1), 0.25)
	local qian_tween2 = qian_obj.transform:DOAnchorPos(u3dpool.vec3(qian_obj_x, qian_obj_y, 0), 0.25)
	local qian_tween3 = qian_obj.transform:DOLocalRotate(u3dpool.vec3(0, 0, 10), 0.25)
	local yunshi_anim_tween1 = yunshi_anim_obj.transform:DOAnchorPos(u3dpool.vec3(yunshi_anim_obj_x, yunshi_anim_obj_y, 0), 0.25)

	if self.anim_tween then
		self.anim_tween:Kill()
		self.anim_tween = nil
	end

	self.anim_tween = DG.Tweening.DOTween.Sequence()
	self.anim_tween:Append(qian_tween1)
	self.anim_tween:Append(qian_tween2)
	self.anim_tween:Append(qian_tween3)
	self.anim_tween:Append(yunshi_anim_tween1)
	self.anim_tween:SetEase(DG.Tweening.Ease.Linear)
	self.anim_tween:OnComplete(function ()
		local fortune_info = QifuYunShiWGData.Instance:GetFortuneInfo()
		local fortune_type = fortune_info.fortune_type
		local fortune_cfg = QifuYunShiWGData.Instance:GetFortuneTypeCfg(fortune_type)
		yunshi_name_obj:SetActive(true)
		UITween.ScaleShowPanel(yunshi_name_obj,u3dpool.vec3(5, 5, 5))
		if not IsEmptyTable(fortune_cfg) and fortune_cfg.is_best == 1 then
			best_img_obj:SetActive(true)
			UITween.ScaleShowPanel(best_img_obj,u3dpool.vec3(5, 5, 5))
			self.paly_anim_end = true
		end
		self.node_list["close_block"]:SetActive(false)
		local bundle, asset = ResPath.GetEffectUi("UI_yunshi_sg_001")
		EffectManager.Instance:PlayAtTransform(bundle, asset, self.node_list["yunshi_anim_effect"].transform,1)
	end)
end

function QifuYunShiResultView:PlayAnim()
	local qifu_view = QiFuWGCtrl.Instance.view
	if qifu_view and qifu_view:IsLoadedIndex(TabIndex.qifu_yunshi) then
		qifu_view:SetYunShiActive(false)
	end
	self.node_list["close_block"]:SetActive(true)
	local first_send_chouqian_falg = QifuYunShiWGData.Instance:GetFirstSendChouQianFlag()
	if first_send_chouqian_falg then
		self:ChouQianAnim()
		QifuYunShiWGData.Instance:SetFirstSendChouQianFlag(false)
	else
		self:YunShiAnim()
	end
	
end