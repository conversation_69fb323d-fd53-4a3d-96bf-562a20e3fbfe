--首冲提示
FirstRechargeTips = FirstRechargeTips or BaseClass(SafeBaseView)

function FirstRechargeTips:__init()
	-- self:SetMaskBg(true, true, false)
	self.view_layer = UiLayer.MainUIHigh
	self:AddViewResource(0, "uis/view/rechargereward_ui_prefab", "First_recharge_tips")
	
	--self.active_close = false
end

function FirstRechargeTips:ReleaseCallBack()
	if self.role_arena_display_1 then
		self.role_arena_display_1:DeleteMe()
		self.role_arena_display_1 = nil
	end
end

function FirstRechargeTips:Open()
	if IS_AUDIT_VERSION then
		return
	end
	SafeBaseView.Open(self)
end

function FirstRechargeTips:OpenCallBack()
	-- local main_ui_act_btn = MainuiWGCtrl.Instance:GetActivityButtonByActivityType(ACTIVITY_TYPE.FIRST_CHONGZHI)
	-- if main_ui_act_btn then
	-- 	main_ui_act_btn:SetCanvasGroupVal(0)
	-- end
end

function FirstRechargeTips:CloseCallBack()
	-- self:PlayFlyTween()
end

function FirstRechargeTips:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["add_rexcharge_btn"], BindTool.Bind1(self.OpenRechargeFun, self))
	XUI.AddClickEventListener(self.node_list["btn_model"], BindTool.Bind1(self.OpenFirstRecharge, self))
	-- XUI.AddClickEventListener(self.node_list.clocs_windows, BindTool.Bind1(self.Close, self))
	if self.role_arena_display_1 == nil then
		self.role_arena_display_1 = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["SiutDisplay1"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}

		self.role_arena_display_1:SetRenderTexUI3DModel(display_data)
	end

	local left, right = ServerActivityWGData.Instance:GetFirstRechargeDisplay(1)
	-- local bundle, asset = ResPath.GetRoleModel(left)
	-- self.role_arena_display_1:SetMainAsset(bundle,asset)
	self.role_arena_display_1:SetRoleResid(left)

	GlobalTimerQuest:AddDelayTimer(function ()
		if self.role_arena_display_1 then
			self.role_arena_display_1:SetWeaponModel(ResPath.GetWeaponModelRes(right))
		end
	end,0.1)

	local id_left = ServerActivityWGData.Instance:GetFirstRechargeTipsCapability()
	self:CreateCapabilityAtlasNumLeft(id_left)
end

function FirstRechargeTips:CreateCapabilityAtlasNumLeft(id)
	local shouchong_anim_cfg = ServerActivityWGData.Instance:GetSCAnimCfg(1, 1)
	if shouchong_anim_cfg then
		self.node_list["zhanli_text_left"].text.text = shouchong_anim_cfg.capability
	else
		self.node_list["zhanli_text_left"].text.text = 0
	end
end

function FirstRechargeTips:OpenRechargeFun()
	self:Close()
	ServerActivityWGCtrl.Instance:OpenFirstRechargeView()
	ViewManager.Instance:FlushView(GuideModuleName.FirstRechargeView,nil,nil,{change_to_sc_view = true})
end

function FirstRechargeTips:OpenFirstRecharge()
	self:Close()
	ServerActivityWGCtrl.Instance:OpenFirstRechargeView()
	ViewManager.Instance:FlushView(GuideModuleName.FirstRechargeView,nil,nil,{change_to_sc_view = true})
end

function FirstRechargeTips:PlayFlyTween()
	TipWGCtrl.Instance:OpenEasyFlyView()
	TipWGCtrl.Instance:FlushEasyFlyView(0, "act_icon", {act_type = ACTIVITY_TYPE.FIRST_CHONGZHI})
end