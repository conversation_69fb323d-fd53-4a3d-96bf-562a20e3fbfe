FengShenBangTitleView = FengShenBangTitleView or BaseClass(SafeBaseView)

function FengShenBangTitleView:__init(view_name)
	self.view_name = "FengShenBangTitleView"
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/fengshenbang_prefab", "layout_title_show_panel")
end

function FengShenBangTitleView:LoadCallBack()
	self:InitPanel()
	self:InitItemList()
end

function FengShenBangTitleView:InitPanel()
	self.node_list.layout_commmon_second_root.rect.sizeDelta = Vector2(954,538)
	self.node_list.title_view_name.text.text = Language.FengShenBang.RankViewTitle
end

function FengShenBangTitleView:InitItemList()
	local res_async_loader = AllocResAsyncLoader(self, "fengshenbang_title_item")
	res_async_loader:Load("uis/view/fengshenbang_prefab", "title_item", nil,
		function(new_obj)
			local title_parent = self.node_list.title_list.transform
			local title_list = {}
			for i=1,5 do
				local obj = ResMgr:Instantiate(new_obj)
				obj.transform:SetParent(title_parent, false)
				title_list[i] = FengShenBangTitleItem.New(obj)
				title_list[i]:SetIndex(i)
			end
			self.title_list = title_list
			self:RefreshView()
		end)
end

function FengShenBangTitleView:ReleaseCallBack()
	if self.title_list then
		for k,v in pairs(self.title_list) do
			v:DeleteMe()
		end
		self.title_list = nil
	end
end

function FengShenBangTitleView:OpenCallBack()

end

function FengShenBangTitleView:OnFlush(param_t)
	self:RefreshView()
end

function FengShenBangTitleView:RefreshView()
	local title_cfg_list = FengShenBangWGData.Instance:GetTitleList()
	if not title_cfg_list or not self.title_list then
		return
	end
	local title_list = self.title_list
	for i=1,#title_list do
		if title_cfg_list[i] then
			title_list[i]:SetData(title_cfg_list[i])
			title_list[i]:SetActive(true)
		else
			title_list[i]:SetActive(false)
		end
	end
end

------------------------------------------------------------------------------------

FengShenBangTitleItem = FengShenBangTitleItem or BaseClass(BaseRender)

function FengShenBangTitleItem:__delete()
	self.attr_obj_list:DeleteMe()
	self.attr_obj_list = nil
end

function FengShenBangTitleItem:LoadCallBack()
	self.attr_obj_list = AsyncListView.New(FengShenBangAttrItem, self.node_list.attr_list)
end

function FengShenBangTitleItem:OnFlush()
	local data = self:GetData()
	local bundle,asset = ResPath.GetTitleModel(data.title_id)
	self.node_list.title_display:ChangeAsset(bundle,asset)

	local role_info = FengShenBangWGData.Instance:GetRankRoleInfoByIndex(self:GetIndex())
	if role_info then
		self.node_list.role_name.text.text = role_info.role_name or role_info.name
	else
		self.node_list.role_name.text.text = Language.FengShenBang.ZanWu
	end
	self:FlushAttrList()
end

function FengShenBangTitleItem:FlushAttrList()
	local data = self:GetData()
	local titel_attr_list,titel_cap_value = FengShenBangWGData.Instance:GetTitleAttrList(data.title_id, true)
	self.attr_obj_list:SetDataList(titel_attr_list or {})
	self.node_list.cap_value.text.text = titel_cap_value or 0

	local title_cfg = TitleWGData.Instance:GetConfig(data.title_id)
	self.node_list.reward_label.text.text = string.format(Language.FengShenBang.AddExp, (title_cfg and title_cfg.add_exp_per / 100) or 0)
end

------------------------------------------------------------------------------------

FengShenBangAttrItem = FengShenBangAttrItem or BaseClass(BaseRender)

function FengShenBangAttrItem:OnFlush()
	local data = self:GetData()
	self.node_list.attr_name.text.text = Language.Common.TipsAttrNameList[data.attr_key]
	self.node_list.attr_value.text.text = AttributeMgr.PerAttrValue(data.attr_key, data.attr_value)
end