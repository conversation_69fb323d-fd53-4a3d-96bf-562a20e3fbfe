
TimeWGCtrl = TimeWGCtrl or BaseClass(BaseWGCtrl)

function TimeWGCtrl:__init()
	if TimeWGCtrl.Instance ~= nil then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[TimeWGCtrl] attempt to create singleton twice!")
		return
	end
	TimeWGCtrl.Instance = self

	self.server_time = 0
	self.server_real_start_time = 0
	self.last_req_time = 0
	self.timer_quest = nil
	self.speed_up_t_quest = nil

	self.login_time_point = 0

	self:registerAllProtocols()
	self:RegisterAllEvents()

	self.cur_day = -1

	self.send_time = 0 			--记录发送获取服务器时间协议的当前时间
	self.send_count = 0			--已发送获取服务器时间未返回次数
	self.cheak_delay_count = 0

	self.pi_time = 0
	self.online_time = 0
	self.online_save_time = 0

	self.cross_activity_open_time = 0	--服务器跨服开服时间.
end

function TimeWGCtrl:__delete()
	self:CancelTimer()

	self.send_count = 0
	self.cheak_delay_count = 0
	TimeWGCtrl.Instance = nil
end

function TimeWGCtrl:registerAllProtocols()
	self:RegisterProtocol(SCTimeAck, "OnTimeAck")
	self:RegisterProtocol(SCCrossActivityCalcOpenTime, "CrossActivityCalcOpenTime")

	self:RegisterProtocol(CSTimeReq)
end

-- 设置网络延迟
function TimeWGCtrl:SetDelayTime()
	-- if 0 == self.send_count then
	-- 	GameNet.Instance:SetDelayTime(Status.NowTime - self.send_time)
	-- elseif self.send_count > 0 and self.cheak_delay_count >= 30 then  --5分钟清一次
	--  	self.send_count = 0
	--  	self.cheak_delay_count = 0
	-- end
end

function TimeWGCtrl:RegisterAllEvents()
	self:Bind(LoginEventType.ENTER_GAME_SERVER_SUCC, BindTool.Bind1(self.OnEnterGameServerSucc, self))
	self:Bind(LoginEventType.GAME_SERVER_DISCONNECTED, BindTool.Bind1(self.OnGameServerDisConnect, self))
	self:Bind(LoginEventType.RECV_MAIN_ROLE_INFO, BindTool.Bind1(self.OnRecvMainRoleInfo, self))
end

function TimeWGCtrl:GetServerTime()
	return self.server_time + (Status.NowTime - self.last_req_time)
end

function TimeWGCtrl:GetCurPiTime()
	local offset_time = Status.NowTime - self.login_time_point
	return self.pi_time + offset_time * 1000
end

function TimeWGCtrl:SetServerTime(server_time, pi_time)
	self.server_time = server_time
	self.pi_time = pi_time or 0
	self.last_req_time = Status.NowTime
	self.login_time_point = Status.NowTime
end

function TimeWGCtrl:GetServerRealStartTime()
	return self.server_real_start_time
end

function TimeWGCtrl:GetServerRealCombineTime()
	return self.server_real_combine_time or 0
end

-- 合服天数
function TimeWGCtrl:GetServerRealCombineDay()
	local time_diff = self:GetServerTime() - self:GetServerRealCombineTime()
	local combine_day = 0 
	local day_time = 24 * 60 * 60 

	if time_diff > 0 then
		if time_diff > day_time then
			combine_day = math.ceil(time_diff / day_time)
		else
			combine_day = 1
		end
	end

	return combine_day
end

--当前本服开服天数
function TimeWGCtrl:GetCurOpenServerDay()
	return self.cur_day
end

--当前跨服开服天数
function TimeWGCtrl:GetCrossActivityOpenDay()
	--如果没开跨服，则返回本服开服天数.
	if self.cross_activity_open_time <= 0 then
		return self.cur_day
	end

	local temp_time = self:GetServerTime() - self.cross_activity_open_time
	local day = math.ceil(temp_time / (60 * 60 * 24))
	return day
end

function TimeWGCtrl:GetCrossActivityOpenTime()
	return self.cross_activity_open_time
end

-- 计算所给时间戳所在的开服天数
function TimeWGCtrl:GetOpenServerDayByTimestamp(timestamp)
	local cur_open_server_day = self:GetCurOpenServerDay()
	local today_start_timestamp = self:GetTodayBeginningTimestamp()
	local delta_timestamp = timestamp - today_start_timestamp
	local delta_day = math.floor(delta_timestamp / (60 * 60 * 24))
	local result = cur_open_server_day + delta_day
	return result
end

-- 今日零点的时间戳
function TimeWGCtrl:GetTodayBeginningTimestamp()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	return TimeUtil.GetEarlyTime(server_time)
end

function TimeWGCtrl:GetServerTimeFormat()
	local second = self:GetServerTime()
	return os.date("*t", second)
end

function TimeWGCtrl:SendTimeReq()
	self.send_time = Status.NowTime
	self.send_count = self.send_count + 1
	self.cheak_delay_count = self.cheak_delay_count + 1

	local protocol = ProtocolPool.Instance:GetProtocol(CSTimeReq)
	protocol:EncodeAndSend()
end

function TimeWGCtrl:OnTimeAck(protocol)
	-- 时区变更
	if protocol.server_time_zone ~= GlobalServerTimeZone then
		GlobalServerTimeZone = protocol.server_time_zone
		TimeUtil.GetLocalTimeZoneAndDiff()
	end

	self.send_count = math.max(self.send_count - 1, 0)
	-- self:SetDelayTime()
	self.server_time = protocol.server_time
	self.server_real_start_time = protocol.server_real_start_time
	self.server_real_combine_time = protocol.server_real_combine_time
	self.last_req_time = Status.NowTime
	if self.cur_day ~= protocol.open_days then
		local is_new_day = self.cur_day ~= -1
		self.cur_day = protocol.open_days

		RoleWGData.Instance:CacheAttr("cur_day", self.cur_day) -- 缓存开服天数

		if is_new_day then
			GlobalEventSystem:Fire(OtherEventType.PASS_DAY, self.cur_day)
		end
		GlobalEventSystem:Fire(OtherEventType.PASS_DAY2, self.cur_day)
	end

	self.has_server_time_flag = true
	if LoginWGCtrl.Instance then
		LoginWGCtrl.Instance:TrySendLoginFanLi2PHP()
	end
end

function TimeWGCtrl:CrossActivityCalcOpenTime(protocol)
	self.cross_activity_open_time = protocol.time
end

function TimeWGCtrl:OnEnterGameServerSucc()
	if not self.timer_quest then
		self:SendTimeReq()
		self.timer_quest = GlobalTimerQuest:AddRunQuest(function() self:SendTimeReq() end, 10)
	end
	-- if not self.speed_up_t_quest then
	-- 	self.speed_up_t_quest = GlobalTimerQuest:AddRunQuest(function() LoginWGCtrl.Instance:SendSpeedUpHello() end, 1.5)
	-- end
end


--收到主角信息 请求一次服务时间
--上面的OnEnterGameServerSucc可能会导致第一次请求 没返回
function TimeWGCtrl:OnRecvMainRoleInfo()
	if self.timer_quest then
		GlobalTimerQuest:CancelQuest(self.timer_quest)
		self.timer_quest = nil
	end

	self:SendTimeReq()
	self.timer_quest = GlobalTimerQuest:AddRunQuest(function() self:SendTimeReq() end, 10)
end

function TimeWGCtrl:OnGameServerDisConnect()
	self:CancelTimer()
end

function TimeWGCtrl:CancelTimer()
	if self.timer_quest then
		GlobalTimerQuest:CancelQuest(self.timer_quest)
		self.timer_quest = nil
	end

	if self.speed_up_t_quest then
		GlobalTimerQuest:CancelQuest(self.speed_up_t_quest)
		self.speed_up_t_quest = nil
	end
end

function TimeWGCtrl:GetDayIndex(begin_time, now_time)
	local tmp_time = 0
	local is_past = now_time < begin_time
	if is_past then
		tmp_time = begin_time
		begin_time = now_time
		now_time = tmp_time
	end

	local format_time = os.date("*t", begin_time)
	local begin_zero_time = os.time{year=format_time.year, month=format_time.month, day=format_time.day, hour=0, min = 0, sec=0}

	local day_index = (now_time - begin_zero_time) / (24 * 3600);

	if is_past then
		return -day_index
	else
		return day_index
	end
end

--获取当天的开始时间戳
function TimeWGCtrl:NowDayTimeStart(now_time)
	local tab = os.date("*t", now_time)
	tab.hour = 0
	tab.min = 0
	tab.sec = 0
	return os.time(tab)
end

--获取当天的结束时间戳
function TimeWGCtrl:NowDayTimeEnd(now_time)
	local tab = os.date("*t", now_time)
	tab.hour = 0
	tab.min = 0
	tab.sec = 0
	return tonumber(os.time(tab) + 86400)
end

function TimeWGCtrl:SetOnlineTime(time)
	self.online_time = time
	self.online_save_time = self:GetServerTime()
end

function TimeWGCtrl:GetOnlineTimes()
	if self.online_save_time > 0 then
		return self.online_time + self:GetServerTime() - self.online_save_time
	end
	return 0
end

function TimeWGCtrl:GetHasServerTimeFlag()
	return self.has_server_time_flag
end