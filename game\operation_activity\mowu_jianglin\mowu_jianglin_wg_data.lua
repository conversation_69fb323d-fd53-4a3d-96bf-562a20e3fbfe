MoWuJiangLinWGData = MoWuJiangLinWGData or BaseClass()
function MoWuJiangLinWGData:__init()
	if MoWuJiangLinWGData.Instance then
		ErrorLog("[MoWuJiangLinWGData] Attemp to create a singleton twice !")
	end
	MoWuJiangLinWGData.Instance = self
	self.jianglin_data = {}

	OperationActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.OPERA_ACT_MOWU_JIANGLIN, {[1] = OPERATION_EVENT_TYPE.LEVEL, [2] = OPERATION_EVENT_TYPE.DAY},
		BindTool.Bind(self.GetActCanOpen, self), BindTool.Bind(self.IsShowMoWuJiangLin, self))
		
	self:UpdataConfig()
	RemindManager.Instance:Register(RemindName.OperationMoWuJiangLin, BindTool.Bind(self.IsShowMoWuJiang<PERSON><PERSON>, self))

	self.is_boss_dead = false
end

function MoWuJiangLinWGData:__delete()
	MoWuJiangLinWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.OperationMoWuJiangLin)
end


-------------------------------------- 魔物降临 --------------------------------------
function MoWuJiangLinWGData:GetJiangLinRewardCfg()
	local interface = self:GetInterfaceByServerDay()
	local reawrd_cfg = {}

	for k,v in pairs(self.mowu_jianglin_reward_cfg) do
		if interface == v.grade then
			table.insert(reawrd_cfg, v)
		end
	end

	return reawrd_cfg
end

function MoWuJiangLinWGData:GetJiangLinBossDropCfg()
	local interface = self:GetInterfaceByServerDay()
	local cfg_list = self.operation_mowu_jianglin_cfg.boss_drop_cfg
	if cfg_list then
		for i=1,#cfg_list do
			if cfg_list[i].grade == interface then
				return cfg_list[i]
			end
		end
	end
end

function MoWuJiangLinWGData:GetJiangLinFlushTimeCfg()
	local interface = self:GetInterfaceByServerDay()
	local refresh_time = {}

	for k,v in pairs(self.mowu_jianglin_refresh_time_cfg) do
		if interface == v.grade then
			table.insert(refresh_time, v)
		end
	end

	return refresh_time
end

function MoWuJiangLinWGData:GetJiangLinBossCfg()
	local boss_cfg = self.operation_mowu_jianglin_cfg.boss_cfg
	local interface = self:GetInterfaceByServerDay()
	local get_boss_cfg = {}

	for k,v in pairs(boss_cfg) do
		if interface == v.grade then
			table.insert(get_boss_cfg, v)
		end
	end

	return get_boss_cfg
end

function MoWuJiangLinWGData:GetOtherCfg(key)
	local interface = self:GetInterfaceByServerDay()
	local cfg_list = self.operation_mowu_jianglin_cfg.other
	if cfg_list then
		for i=1,#cfg_list do
			if cfg_list[i].grade == interface then
				if key then
					return cfg_list[i][key]
				else
					return cfg_list[i]
				end
			end
		end
	end
end

function MoWuJiangLinWGData:SetMoWuJiangLInfo(protocol)
	self.jianglin_data.world_level = protocol.world_lv
	self.jianglin_data.pass_times = protocol.pass_times
end

function MoWuJiangLinWGData:MoWuJianLinActivityState(state, next_time)
	self.jianglin_data.next_time = next_time
	self.jianglin_data.state = state

	if self:IsInMoWuJiangLinActivity() then
		ActivityWGCtrl.Instance:OpenActivityNoticeView(ACTIVITY_TYPE.CLIENT_MOWUJINGLIN)
	end

	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_mowu_jianglin)
	RemindManager.Instance:Fire(RemindName.OperationMoWuJiangLin)
end

function MoWuJiangLinWGData:GetMoWuJianLinInfo()
	return self.jianglin_data
end

--判断是否在活动开启时间内
function MoWuJiangLinWGData:IsInMoWuJiangLinActivity()
	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.CLIENT_MOWUJINGLIN)
	if not act_cfg then
		return false
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	if role_level < act_cfg.level or role_level > act_cfg.level_max then
		return false
	end

	if self.jianglin_data.state ~= nil then
		return self.jianglin_data.state == ACTIVITY_STATUS.OPEN
	end
	return false
end

--获取活动状态
function MoWuJiangLinWGData:GetMoWuJiangLinActivityStatus()
	if self.jianglin_data.state ~= nil then
		return self.jianglin_data.state
	end
	return ACTIVITY_STATUS.CLOSE
end

function MoWuJiangLinWGData:GetJiangLinReward()
	local world_level = RankWGData.Instance:GetWordLevel() or 0
	local reward_cfg = self:GetJiangLinRewardCfg()
	for _,v in pairs(reward_cfg) do
		if world_level >= v.min_lv and world_level <= v.max_lv then
			return v
		end
	end
end

--获得当前bossid
function MoWuJiangLinWGData:GetBossId()
	local cfg_list = self:GetJiangLinBossCfg()
	if cfg_list then
		local world_level = RankWGData.Instance:GetWordLevel() or 0
		local boss_id = 0
		local boss_cfg = nil
		for i=1,#cfg_list do
			if world_level >= cfg_list[i].limit_lv then
				boss_id = cfg_list[i].boss_id
				boss_cfg = cfg_list[i]
			end
		end
		return boss_id, boss_cfg
	end
end

function MoWuJiangLinWGData:GetTSJLOpenTime()
	if self.time_pramstr == nil then
		self.time_pramstr = {}
		local cfg = self:GetJiangLinFlushTimeCfg()
		if cfg ~= nil then
			for k,v in pairs(cfg) do
				local date = {}
				local arr = {}
				for i=1,4 do
					arr[i] = tonumber(string.sub(v.refresh_time,i,i))
				end
				date.hour = arr[1] * 10 + arr[2]
				date.min = arr[3] * 10 + arr[4]
				table.insert(self.time_pramstr, date)
			end
		end
	end

	return self.time_pramstr
end

--是否开启
function MoWuJiangLinWGData:GetActCanOpen()
	local role_level = RoleWGData.Instance:GetAttr("level")
	local _, open_level = self:GetInterfaceByServerDay()
	if role_level < open_level then
		return false
	end

	local activity_open_day = OperationActivityWGData.Instance:GetActOpenDay(ACTIVITY_TYPE.OPERA_ACT_MOWU_JIANGLIN)
	local open_day_list = self:GetActivityCanOpenDay()
	if not activity_open_day or not open_day_list then
		return false
	end

	for _,v in pairs(open_day_list) do
		if tonumber(v) == activity_open_day then
			return true
		end
	end
	
	return false
end

function MoWuJiangLinWGData:GetMowuJiangLinViewShowTime()
	local open_day_list = self:GetActivityCanOpenDay()
	if nil == open_day_list then
		return 0
	end

	local time = 0
	local activity_open_day = OperationActivityWGData.Instance:GetActOpenDay(ACTIVITY_TYPE.OPERA_ACT_MOWU_JIANGLIN)
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPERA_ACT_MOWU_JIANGLIN)
	local check_day = 0
	if activity_open_day ~= nil and act_info ~= nil then
		for k, v in pairs(open_day_list) do
			if tonumber(v) == activity_open_day then
				activity_open_day = activity_open_day + 1
				check_day = check_day + 1
			else
				if tonumber(v) > activity_open_day then
					break
				end
			end
		end

		local has_time = act_info.end_time - act_info.start_time - activity_open_day * 86400
		if has_time <= 0 then
			time = act_info.end_time
		else
			local server_time = TimeWGCtrl.Instance:GetServerTime()
			local time_tab = os.date("*t", server_time)
			if time_tab ~= nil then
				time = server_time - time_tab.hour * 3600 - time_tab.min * 60 - time_tab.sec + 86400 * check_day
			end
		end
	end

	return time
end

function MoWuJiangLinWGData:GetActivityCanOpenDay()
	local open_day_list = nil
    local open_day = OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.OPERA_ACT_MOWU_JIANGLIN)
    local open_time = OperationActivityWGData.Instance:GetOpenTimeByAct(ACTIVITY_TYPE.OPERA_ACT_MOWU_JIANGLIN)
    local week = TimeUtil.FormatSecond3MYHM1(open_time)--获取当前是周几
	for k,v in pairs(self.mowu_jianglin_param_cfg) do
		if (v.start_server_day <= open_day and open_day < v.end_server_day and v.open_act_day and week == v.week_index) then
			open_day_list = Split(v.open_act_day, "|")
			break
		end
	end

	return open_day_list
end

--根据服务器开服天数获得界面配置
function MoWuJiangLinWGData:GetInterfaceByServerDay()
	local open_day = OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.OPERA_ACT_MOWU_JIANGLIN)
	for k,v in pairs(self.mowu_jianglin_param_cfg) do
		if v.start_server_day <= open_day and open_day <= v.end_server_day then
			return v.grade, v.open_level
		end
	end
	
	return 0, 9999
end

--根据界面配置获取界面信息
function MoWuJiangLinWGData:GetShouChongViewCfgByInterface()
	local interface = self:GetInterfaceByServerDay()
	return self.mowu_jianglin_interface_cfg[interface]
end

function MoWuJiangLinWGData:IsShowMoWuJiangLin()
	local is_open = OperationActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.OPERA_ACT_MOWU_JIANGLIN)
	if not is_open then
		return 0
	end

	if self:IsInMoWuJiangLinActivity() then
		return 1
	end
	return 0
end

function MoWuJiangLinWGData:UpdataConfig()
	self.operation_mowu_jianglin_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_mowu_jianglin_auto")
	self.mowu_jianglin_reward_cfg = self.operation_mowu_jianglin_cfg.reward_config
	self.mowu_jianglin_interface_cfg = ListToMap(self.operation_mowu_jianglin_cfg.interface, "interface")
	self.mowu_jianglin_param_cfg = self.operation_mowu_jianglin_cfg.config_param
	self.mowu_jianglin_refresh_time_cfg = self.operation_mowu_jianglin_cfg.refresh_time
	local safe_area_cfg = self.operation_mowu_jianglin_cfg.safe_area[1]
	self.safe_area_points = {}
	for i=1,4 do
		local t = Split(safe_area_cfg["posi" .. i], ",")
		self.safe_area_points[i] = {x = t[1], y = t[2]}
	end
	self.area_effect_cfg = self.operation_mowu_jianglin_cfg.area_effect
end

function MoWuJiangLinWGData:GetSafeAreaPoints()
	return self.safe_area_points
end

function MoWuJiangLinWGData:GetSafeAreaEffCfg()
	return self.area_effect_cfg
end

function MoWuJiangLinWGData:SetMWJLFinishInfo(protocol)
	local finish_info = {}
	local reward_list = {}
	local partipate_list = protocol.partipate_list
	for i=1,#partipate_list do
		if partipate_list[i].item_id > 0 then
			reward_list[#reward_list + 1] = partipate_list[i]
		end
	end

	finish_info.partipate_list = reward_list
	finish_info.fall_item_list = protocol.fall_item_list

	self.mwjl_finish_info = finish_info
end

function MoWuJiangLinWGData:GetMWJLFinishInfo()
	return self.mwjl_finish_info
end

function MoWuJiangLinWGData:SetShiLianBossState(is_boss_dead)
	self.is_boss_dead = is_boss_dead == 1
end

function MoWuJiangLinWGData:GetShiLianBossState()
	return self.is_boss_dead
end

--有效时间倒计时
function MoWuJiangLinWGData:IsInShiLianActivity()
	local server_time = TimeWGCtrl.Instance:GetServerTime()

	local refresh_time_list = self:GetShiLianRefreshTime()
	for i = 1, #refresh_time_list do
		if server_time >= refresh_time_list[i].start_timestemp and server_time < refresh_time_list[i].end_timestemp then
			return true, refresh_time_list[i]
		end
	end

	for i = 1, #refresh_time_list do
		if server_time < refresh_time_list[i].start_timestemp then
			return false, refresh_time_list[i]
		end
	end
	-- return true
	return false, nil
end

-- 获取试炼副本boss刷新时间范围
function MoWuJiangLinWGData:GetShiLianRefreshTime()
	if nil == self.refresh_time_list then
		self:SetShiLianRefreshTime()
	end
	return self.refresh_time_list
end

-- 设置试炼副本boss刷新时间范围
function MoWuJiangLinWGData:SetShiLianRefreshTime()
	self.refresh_time_list = {}
	local boss_time = self:GetOtherCfg("boss_time")
	local cfg = self:GetJiangLinFlushTimeCfg()
	for _,v in pairs(cfg) do
		local refresh_time = {}
		
		refresh_time.start_timestemp = TimeUtil.FormatCfgTimestamp(v.refresh_time)
		refresh_time.end_timestemp = refresh_time.start_timestemp + boss_time
		table.insert(self.refresh_time_list, refresh_time)
	end
	table.sort(self.refresh_time_list, function (a,b)
		return a.start_timestemp < b.start_timestemp
	end)
end
