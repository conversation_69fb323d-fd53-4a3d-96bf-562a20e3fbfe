-----------------------------------
--双修 升级
-----------------------------------

function ArtifactView:UpStarLoadIndexCallBack()
	-- 星级技能
	if self.star_skill_item_list == nil then
		self.star_skill_item_list = {}
		for i = 1, 2 do
			self.star_skill_item_list[i] = ArtifactStarSkillItem.New(self.node_list["star_skill_item_" .. i])
		end
	end
	-- 觉醒技能
	if self.awake_skill_item_list == nil then
		self.awake_skill_item_list = {}
		for i = 1, 2 do
			self.awake_skill_item_list[i] = ArtifactAwakeSkillItem.New(self.node_list["awake_skill_item_" .. i])
			self.awake_skill_item_list[i]:SetSkillInfoClickCallback(BindTool.Bind(self.OnAwakeSkillItemClick, self))
		end
	end

	if not self.cur_awake_skill_item then
		self.cur_awake_skill_item = ArtifactAwakeSkillItem.New(self.node_list["cur_awake_skill_item"])
	end

	XUI.AddClickEventListener(self.node_list["up_star_btn"], BindTool.Bind(self.OnBtnUpStar, self))				-- 升星
	XUI.AddClickEventListener(self.node_list["btn_awake"], BindTool.Bind(self.OnBtnAwake, self))				-- 觉醒
	XUI.AddClickEventListener(self.node_list["btn_back"], BindTool.Bind(self.OnClickBackBtn, self))				-- 返回升星
	XUI.AddClickEventListener(self.node_list["up_star_attr_btn"], BindTool.Bind(self.OnClickAttrBtn, self, 2))
	XUI.AddClickEventListener(self.node_list["awake_attr_btn"], BindTool.Bind(self.OnClickAttrBtn, self, 3))

	self.cur_awake_skill_level = 1
end

function ArtifactView:UpStarReleaseCallBack()
	if self.star_skill_item_list then
		for k, v in pairs(self.star_skill_item_list) do
			v:DeleteMe()
		end
		self.star_skill_item_list = nil
	end

	if self.awake_skill_item_list then
		for k, v in pairs(self.awake_skill_item_list) do
			v:DeleteMe()
		end
		self.awake_skill_item_list = nil
	end

	if self.cur_awake_skill_item then
		self.cur_awake_skill_item:DeleteMe()
		self.cur_awake_skill_item = nil
	end

	self.cur_awake_skill_level = nil
end

function ArtifactView:UpStarShowIndexCallBack()
	if self:IsAutoUpLevel() then
		self:StopLevelOperator()
	end
	self:PanelSwitch(1, true)
	-- self:PlayMainAni()
	-- self:PlayLeftListAni()
end

function ArtifactView:UpStarCloseCallBack()

end

function ArtifactView:UpStarOnFlush(param_t)
	self:FlushUpStarPart()
	self:FlushSkillPart()
	self:FlushAwakePart()
end

function ArtifactView:UpStarOnClickArtifactCell(cell)
	self:FlushUpStarPart()
	self:FlushSkillPart()
	self:FlushAwakePart()

	--切换女神时回到显示星级
	local show_back_anim = self.cur_show_panel ~= 1
	self:PanelSwitch(1, show_back_anim)
	if show_back_anim then
		self:PlayMainAni()
	end
end

--刷新升星部分信息
function ArtifactView:FlushUpStarPart()
	if not self:CheckFlushCondition() then
		return
	end

	local artifact_cfg = ArtifactWGData.Instance:GetArtifactCfgBySeq(self.select_artifact_seq)
	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.select_artifact_seq)

	-- 升星相关
	local cur_star_cfg = ArtifactWGData.Instance:GetArtifactStarCfg(self.select_artifact_seq, cur_artifact_data.star_level)
	local next_star_cfg = ArtifactWGData.Instance:GetArtifactStarCfg(self.select_artifact_seq, cur_artifact_data.star_level + 1)
	local max_star = next_star_cfg == nil
	self.node_list["star_max"]:SetActive(max_star)
	self.node_list["up_star_btn"]:SetActive(not max_star)

	if cur_star_cfg and not max_star then
		local item_cfg = ItemWGData.Instance:GetItemConfig(cur_star_cfg.cost_item_id)
		if item_cfg then
			self.node_list["cost_item_icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id)) --道具图标
		end

		local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_star_cfg.cost_item_id)
		local color = item_num >= cur_star_cfg.cost_item_num and COLOR3B.GREEN or COLOR3B.RED
		local up_star_str = item_num .. "/" .. cur_star_cfg.cost_item_num
		up_star_str = ToColorStr(up_star_str, color)
		self.node_list["txt_star_cost_num"].text.text = up_star_str

		-- 替代升星物品
		local replace_item_cfg = ItemWGData.Instance:GetItemConfig(cur_star_cfg.replace_item_id)
		if replace_item_cfg then
			self.node_list["replace_item_icon"].image:LoadSprite(ResPath.GetItem(replace_item_cfg.icon_id)) --道具图标
		end
		local replace_item_num = ItemWGData.Instance:GetItemNumInBagById(cur_star_cfg.replace_item_id)
		local color = replace_item_num >= cur_star_cfg.replace_item_cost and COLOR3B.GREEN or COLOR3B.RED
		local replace_cost_str = replace_item_num .. "/" .. cur_star_cfg.replace_item_cost
		replace_cost_str = ToColorStr(replace_cost_str, color)
		self.node_list["txt_replace_cost_num"].text.text = replace_cost_str
	end

	local star_res_list = GetStarImgResByStar(cur_artifact_data and cur_artifact_data.star_level or 0)
	for i = 1, GameEnum.ITEM_MAX_STAR do
		self.node_list["star_" .. i].image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
	end

	--红点
	local up_star_remind = ArtifactWGData.Instance:GetArtifactUpStarRemind(self.select_artifact_seq)
	self.node_list["upstar_btn_remind"]:SetActive(up_star_remind)
end

function ArtifactView:FlushSkillPart()
	if not self:CheckFlushCondition() then
		return
	end

	-- local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.select_artifact_seq)
	-- local skill_cfg = ArtifactWGData.Instance:GetArtifactSkillCfg(self.select_artifact_seq, cur_artifact_data.star_level)
	-- if skill_cfg then
	-- 	local skill_bundle, skill_asset = ResPath.GetSkillIconById(skill_cfg.skill_id)
	-- 	self.node_list.skill_icon.image:LoadSprite(skill_bundle, skill_asset)
	-- 	self.node_list.skill_name.text.text = skill_cfg.skill_name
	-- 	self.node_list["skill_lock"]:SetActive(cur_artifact_data.level == 0)
	-- end

	local cur_skill_cfg_list = ArtifactWGData.Instance:GetArtifactSkillCfgList(self.select_artifact_seq)
	for i, v in ipairs(self.star_skill_item_list) do
		v:SetData(cur_skill_cfg_list[i])
	end

	local cur_awake_skill_cfg_list = ArtifactWGData.Instance:GetAwakeSkillCfgList(self.select_artifact_seq)
	for i, v in ipairs(self.awake_skill_item_list) do
		v:SetData(cur_awake_skill_cfg_list[i])
	end
end

--刷新觉醒部分信息
function ArtifactView:FlushAwakePart()
	if not self:CheckFlushCondition() then
		return
	end

	local awake_skill_cfg = ArtifactWGData.Instance:GetAwakeSkillCfgByLevel(self.select_artifact_seq, self.cur_awake_skill_level)
	self.cur_awake_skill_item:SetData(awake_skill_cfg)
	self.node_list["txt_skill_desc"].text.text = awake_skill_cfg.skill_desc

	local artifact_cfg = ArtifactWGData.Instance:GetArtifactCfgBySeq(self.select_artifact_seq)
	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.select_artifact_seq)
	local awake_cfg = ArtifactWGData.Instance:GetArtifactAwakeCfg(self.select_artifact_seq, awake_skill_cfg.awake_level)

	if artifact_cfg and cur_artifact_data and awake_cfg then
		local is_awake = cur_artifact_data.awake_level >= awake_skill_cfg.awake_level
		self.node_list["btn_awake"]:SetActive(not is_awake)
		self.node_list["awake_max_flag"]:SetActive(is_awake)

		-- 前置觉醒条件
		local str1 = ""
		local is_enouth_awake_level = true
		if awake_skill_cfg.awake_level > 1 then
			str1 = string.format(Language.Artifact.AwakeTips1, artifact_cfg.name, awake_skill_cfg.awake_level - 1)
			is_enouth_awake_level = cur_artifact_data.awake_level + 1 >= awake_skill_cfg.awake_level
			str1 = ToColorStr(str1, is_enouth_awake_level and COLOR3B.GREEN or COLOR3B.RED)
			str1 = str1 .. "\n"
		end
		-- 前置星级条件
		local str2 = cur_artifact_data.star_level .. "/" .. awake_cfg.need_star
		str2 = string.format(Language.Artifact.AwakeTips, artifact_cfg.name, str2)
		local is_enouth_star_level = cur_artifact_data.star_level >= awake_cfg.need_star
		str2 = ToColorStr(str2, is_enouth_star_level and COLOR3B.GREEN or COLOR3B.RED)
		self.node_list["txt_awake_desc"].text.text = str1 .. str2

		local can_awake = is_enouth_awake_level and is_enouth_star_level
		self.node_list["img_awake_remind"]:SetActive(can_awake)
	end
end

function ArtifactView:OnBtnUpStar()
	if not self:CheckFlushCondition() then
		return
	end

	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.select_artifact_seq)
	local cur_star_cfg = ArtifactWGData.Instance:GetArtifactStarCfg(self.select_artifact_seq, cur_artifact_data.star_level)
	local next_star_cfg = ArtifactWGData.Instance:GetArtifactStarCfg(self.select_artifact_seq, cur_artifact_data.star_level + 1)
	if next_star_cfg and cur_star_cfg then
		local cost_num = ItemWGData.Instance:GetItemNumInBagById(cur_star_cfg.cost_item_id)
		local replace_item_num = ItemWGData.Instance:GetItemNumInBagById(cur_star_cfg.replace_item_id)
		if cost_num >= cur_star_cfg.cost_item_num or replace_item_num >= cur_star_cfg.replace_item_cost then
			ArtifactWGCtrl.Instance:SendCSArtifactOperateRequest(ARTIFACT_OPERATE_TYPE.STAR_UPLEVEL, self.select_artifact_seq)
		else
			TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = cur_star_cfg.cost_item_id })
		end
	end
end

function ArtifactView:OnBtnAwake()
	if not self:CheckFlushCondition() then
		return
	end

	local awake_skill_cfg = ArtifactWGData.Instance:GetAwakeSkillCfgByLevel(self.select_artifact_seq, self.cur_awake_skill_level)
	local awake_cfg = ArtifactWGData.Instance:GetArtifactAwakeCfg(self.select_artifact_seq, awake_skill_cfg.awake_level)
	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.select_artifact_seq)

	local is_enouth_awake_level = cur_artifact_data.awake_level + 1 == awake_skill_cfg.awake_level
	local is_enouth_star_level = cur_artifact_data.star_level >= awake_cfg.need_star

	if is_enouth_awake_level and is_enouth_star_level then
		ArtifactWGCtrl.Instance:SendCSArtifactOperateRequest(ARTIFACT_OPERATE_TYPE.UP_AWAKE_LEVEL, self.select_artifact_seq)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.Artifact.CanNotAwake)
	end
end

function ArtifactView:OnClickBackBtn()
	self:PanelSwitch(1, true)
	self:PlayMainAni()
	self:PlayLeftListAni()
end

function ArtifactView:OnAwakeSkillItemClick(skill_item)
	self.cur_awake_skill_level = skill_item:GetData().skill_level
	self:FlushAwakePart()
	self:PanelSwitch(2, true)
end

-- 1:切换到升星，2:切换到觉醒
function ArtifactView:PanelSwitch(target_panel_type, need_anim)
	target_panel_type = target_panel_type or 1
	self.cur_show_panel = target_panel_type
	self.node_list["up_star_panel"]:SetActive(target_panel_type == 1)
	self.node_list["awake_panel"]:SetActive(target_panel_type == 2)
	if need_anim then
		if target_panel_type == 1 then
			self.node_list["anim_root"].animation_player:Play("ShowStarPanel")
		elseif target_panel_type == 2 then
			self.node_list["anim_root"].animation_player:Play("ShowAwakePanel")
		end
	end
end

function ArtifactView:OnClickAttrBtn(attr_show_type)
	if not self:CheckFlushCondition() then
		return
	end
	local show_data = {
		attr_show_type = attr_show_type,
		artifact_seq = self.select_artifact_seq
	}
	ArtifactWGCtrl.Instance:OpenAwakeView(show_data)
end

---------------------------------------
-- ArtifactStarSkillItem
---------------------------------------
ArtifactStarSkillItem = ArtifactStarSkillItem or BaseClass(BaseRender)

function ArtifactStarSkillItem:__init()

end

function ArtifactStarSkillItem:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["skill_icon"], BindTool.Bind(self.OnBtnSkillClick, self))
	XUI.AddClickEventListener(self.node_list["btn_skill_info_click"], BindTool.Bind(self.OnBtnSkillInfoClickCallback, self))
end

function ArtifactStarSkillItem:ReleaseCallBack()
	self.skill_info_click_callback = nil
end

function ArtifactStarSkillItem:OnFlush()
	if not self.data then
		return
	end

	local cfg = self.data
	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.data.seq)

	local skill_bundle, skill_asset = ResPath.GetSkillIconById(cfg.skill_id)
	self.node_list.skill_icon.image:LoadSprite(skill_bundle, skill_asset)
	self.node_list.skill_name.text.text = cfg.skill_name
	self.node_list["skill_lock"]:SetActive(cur_artifact_data.star_level < cfg.star_level)
end

function ArtifactStarSkillItem:OnBtnSkillClick()
	if not self.data then
		return
	end
	local cfg = self.data

	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.data.seq)
	local is_locked = cur_artifact_data.star_level < cfg.star_level

	local show_data = {
		skill_box_type = SKILL_BOX_TYPE.ARTIFACT_SKILL,
		skill_type = 1, -- 升星技能
		x = 0,
		y = 0,
		set_pos2 = true,
		seq = self.data.seq,
		icon = cfg.skill_id,
		top_text = cfg.skill_name,
		skill_level = cfg.skill_level,
		hide_next = true,
		body_text = cfg.skill_desc,
		limit_text = is_locked and string.format(Language.Artifact.SkillTipsShow, cfg.star_level) or nil
	}
	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

function ArtifactStarSkillItem:SetSkillInfoClickCallback(callback)
	self.skill_info_click_callback = callback
end

function ArtifactStarSkillItem:OnBtnSkillInfoClickCallback()
	if self.skill_info_click_callback then
		self.skill_info_click_callback()
	end
end

---------------------------------------
-- ArtifactAwakeSkillItem
---------------------------------------
ArtifactAwakeSkillItem = ArtifactAwakeSkillItem or BaseClass(BaseRender)

function ArtifactAwakeSkillItem:__init()

end

function ArtifactAwakeSkillItem:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["skill_icon"], BindTool.Bind(self.OnBtnSkillClick, self))
	XUI.AddClickEventListener(self.node_list["btn_skill_info_click"], BindTool.Bind(self.OnBtnSkillInfoClickCallback, self))
end

function ArtifactAwakeSkillItem:ReleaseCallBack()
	self.skill_info_click_callback = nil
end

function ArtifactAwakeSkillItem:OnFlush()
	if not self.data then
		return
	end
	local cfg = self.data
	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.data.seq)
	local skill_bundle, skill_asset = ResPath.GetSkillIconById(cfg.skill_id)
	self.node_list.skill_icon.image:LoadSprite(skill_bundle, skill_asset)
	self.node_list.skill_name.text.text = cfg.skill_name
	self.node_list["skill_lock"]:SetActive(cur_artifact_data.awake_level < cfg.awake_level)

	local awake_cfg = ArtifactWGData.Instance:GetArtifactAwakeCfg(self.data.seq, cfg.awake_level) --觉醒配置
	local is_enouth_awake_level = cur_artifact_data.awake_level + 1 == cfg.awake_level
	local is_enouth_star_level = cur_artifact_data.star_level >= awake_cfg.need_star
	if self.node_list["img_remind"] then
		self.node_list["img_remind"]:SetActive(is_enouth_awake_level and is_enouth_star_level)
	end
end

function ArtifactAwakeSkillItem:OnBtnSkillClick()
	if not self.data then
		return
	end
	local cfg = self.data
	local awake_cfg = ArtifactWGData.Instance:GetArtifactAwakeCfg(self.data.seq, cfg.awake_level) --觉醒配置
	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.data.seq)

	local is_locked = cur_artifact_data.awake_level < cfg.awake_level
	local show_data = {
		skill_box_type = SKILL_BOX_TYPE.ARTIFACT_SKILL,
		skill_type = 2, --觉醒技能
		x = 0,
		y = 0,
		set_pos2 = true,
		seq = self.data.seq,
		icon = cfg.skill_id,
		top_text = cfg.skill_name,
		skill_level = cfg.skill_level,
		hide_next = true,
		body_text = cfg.skill_desc,
		limit_text = is_locked and string.format(Language.Artifact.SkillTipsShow, awake_cfg.need_star) or nil
	}
	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

function ArtifactAwakeSkillItem:SetSkillInfoClickCallback(callback)
	self.skill_info_click_callback = callback
end

function ArtifactAwakeSkillItem:OnBtnSkillInfoClickCallback()
	if self.skill_info_click_callback then
		self.skill_info_click_callback(self)
	end
end