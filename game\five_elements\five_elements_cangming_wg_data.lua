----五行沧溟
function FiveElementsWGData:InitCangMingData()
	self.waistlight_cfg_auto = ConfigManager.Instance:GetAutoConfig("waistlight_cfg_auto")
	self.waist_light_type_cfg = self.waistlight_cfg_auto.waist_light
	self.waist_stone_cfg = ListToMap(self.waistlight_cfg_auto.waist_stone, "type", "slot")
	self.waist_cost_cfg = ListToMap(self.waistlight_cfg_auto.waist_stone, "item_id")
    self.waist_skill_cfg = ListToMap(self.waistlight_cfg_auto.waist_light, "skill_id")

    self.use_waist_id = 0
	self.use_waist_skill_id = 0
	self.all_waist_lights_list = {}
	self.halo_skill_attack_num = 0

	self:InitDaoHunData()--五行沧溟拓展-道魂
	self:InitQianTianLuData()----五行沧溟拓展-乾天录
end

--协议数据
function FiveElementsWGData:SetWaistLightAllInfo(protocol)
	self.use_waist_id = protocol.use_waist_id
	self.use_waist_skill_id = protocol.skill_id
	self.all_waist_lights_list = protocol.all_waist_lights_list
end

function FiveElementsWGData:WaistLightItemUpdateInfo(protocol)
	if self.all_waist_lights_list[protocol.waist_id] then
		self.all_waist_lights_list[protocol.waist_id].slots = protocol.waist_light
	end
end

function FiveElementsWGData:SetCurUseWaistLight(protocol)
    self.use_waist_id = protocol.use_waist_id
	for k,v in pairs(self.all_waist_lights_list) do
		v.is_use = k == protocol.use_waist_id
	end
end

function FiveElementsWGData:SetCurUseWaistLightSkill(protocol)
    self.use_waist_skill_id = protocol.skill_id
end

function FiveElementsWGData:GetWaistLightSkillInfo(skill_id)
    local cfg = self.waist_skill_cfg[skill_id]
    local info = {}
    if cfg then
        local level = self:GetWaistLightOpen(cfg.type) --技能等级和光环一样
        local is_use = skill_id == self.use_waist_skill_id
        info.level = level == 0 and 1 or level  --技能默认为1级
        info.is_open = level > 0
        info.is_use = is_use
		info.skill_id = skill_id
		info.skill_halo_id = cfg.type
    end

    return info
end

function FiveElementsWGData:SkillShowCfgList(type_index, level)
	local data_list = {}
	local lv, is_open = self:TestFootLightOpen(type_index)

	local index = level and level or (is_open and lv or 1)
	local skill_id = (self:GetSkillIDList(type_index, index))[2] or 0
	data_list.skill_id = skill_id
	data_list.fazhen_id = type_index
	return data_list
end

--检测沧溟是否开启并返回对应的等级
function FiveElementsWGData:GetWaistLightOpen(type)
	local info = self.all_waist_lights_list[type]
	if not info then
		return 0, false
	end

	local open_num = 0
	local level = nil
	for k, v in pairs(info.slots) do
		if v.itemId > 0 then
			open_num = open_num + 1
		end

		if not level then
			level = v.level
		else
			level = math.min(level, v.level)
		end
	end

	return level, open_num == WAIST_LIGHT_NUMTYPE.COUNT_WAIST_LIGHT_STONE
end

--检测沧溟是否幻化
function FiveElementsWGData:GetWaistLightUse(type)
	local info = self.all_waist_lights_list[type]
	return info and info.is_use
end

--获取单个五行孔位数据
function FiveElementsWGData:GetWaistLightSlotInfo(type, slot)
	local info = ((self.all_waist_lights_list[type] or {})["slots"] or {})[slot]
	return info
end

-- 其他
function FiveElementsWGData:GetWaistLightOtherCfg()
	return self.waistlight_cfg_auto.other[1]
end

function FiveElementsWGData:GetHaloSkillAttackNeedNum()
	local attack_num = self:GetWaistLightOtherCfg().attack_num
	local cangming_cfg = self:GetWaistLightBaseCfgBySkillId(self.use_waist_skill_id or 0)
	local daohun_cfg = self:GetDaohunSkillCfg(cangming_cfg and cangming_cfg.type)
	if not IsEmptyTable(daohun_cfg) then--道魂拓展-减沧溟技能CD
		attack_num = daohun_cfg.count or 1
	end
	return attack_num or 1
end

function FiveElementsWGData:SetCurHaloSkillAttackNum(num)
	self.halo_skill_attack_num = num
end

function FiveElementsWGData:GetCurHaloSkillAttackNum()
	return self.halo_skill_attack_num
end

function FiveElementsWGData:GetIsCanDoHaloSkill()
	return self.halo_skill_attack_num >= self:GetHaloSkillAttackNeedNum()
end

function FiveElementsWGData:GetWaistLightBaseCfgBySkillId(skill_id)
	return self.waist_skill_cfg[skill_id]
end

--标签页
function FiveElementsWGData:GetWaistLightList()
	local data_list = {}
	for i = 1, #self.waist_light_type_cfg do
		local item = self.waist_light_type_cfg[i]
		if self:GetWaistItemIsCanShow(item.type) then
			table.insert(data_list, item)
		end
	end

	return data_list
end

-- 显示限制
function FiveElementsWGData:GetWaistItemIsCanShow(type)
	local cfg = self:GetWaistLightCfgByType(type)
	if not cfg then return false end

	local data_list = self:GetWaistLightSlotCfg(type)
    if not data_list then return false end

	for k, v in pairs(data_list) do
		local info = self:GetWaistLightSlotInfo(type, v.slot)
		if info and info.itemId > 0 then
			return true
		else
			local slot_cfg = (self.waist_stone_cfg[type] or {})[v.slot]
			if slot_cfg then
				local num = ItemWGData.Instance:GetItemNumInBagById(slot_cfg.item_id)
				if num > 0 then
					return true
				end
			end
		end
	end

    if cfg.is_default_show == 1 then
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local role_level = RoleWGData.Instance:GetRoleLevel()
        return open_day >= cfg.skynumber_show and role_level >= cfg.level_show
    end

    return false
end

function FiveElementsWGData:GetWaistLightCfgByType(type)
	return self.waist_light_type_cfg[type]
end

function FiveElementsWGData:GetWaistLightSlotCfg(type)
	return self.waist_stone_cfg[type]
end

function FiveElementsWGData:GetIsWaistLightStuff(item_id)
    return self.waist_cost_cfg[item_id] ~= nil
end

function FiveElementsWGData:GetWaistStoneByItemID(item_id)
	return self.waist_cost_cfg[item_id]
end

--根据孔位获取对应的孔位属性
function FiveElementsWGData:GetWaistLightSlotAttrList(type, slot, no_sort)
	local attr_list = {}
	local cfg = (self.waist_stone_cfg[type] or {})[slot]
    local info = self:GetWaistLightSlotInfo(type, slot)
	if IsEmptyTable(cfg) or info == nil then
        return attr_list
    end
	
	local slot_level= info.level
	local max_level = cfg.max_lv
	local em_data = EquipmentWGData.Instance
	local attr_id, attr_value = 0, 0
	local max_attr_num = 5
	for i = 1, max_attr_num do
        attr_id = cfg["attr_id" .. i]
        attr_value = cfg["attr_value" .. i]
        if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
            local attr_str = em_data:GetAttrStrByAttrId(attr_id)
            local data = {}
			data.attr_id = attr_id
			data.attr_value = attr_value * slot_level
			data.attr_str = attr_str
			if slot_level < max_level then
				data.add_value = attr_value
			else
				data.add_value = 0
			end

			if not no_sort then
				data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
			end

            table.insert(attr_list, data)
        end
    end

	if not no_sort and not IsEmptyTable(attr_list) then
        table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
    end

	return attr_list
end

--获得界面总属性
function FiveElementsWGData:WaistLightGetNewAttrList(type)
	local stone_list = self:GetWaistLightSlotCfg(type)
	if not stone_list then
		return
	end

	local temp = {}
	for k = 1, WAIST_LIGHT_NUMTYPE.COUNT_WAIST_LIGHT_STONE do--5个孔
		for i = 1, 5 do--每个孔属性数
			if stone_list[k] then
				local attr_id = stone_list[k]["attr_id" .. i]
				local attr_val = stone_list[k]["attr_value" .. i]
				if attr_id > 0 and attr_val > 0 then
					local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
					if not temp[attr_str] then 
						temp[attr_str] = attr_val
					else
						temp[attr_str] = temp[attr_str] + attr_val
					end
				end
			end
		end
	end

	return temp
end

----总战力
function FiveElementsWGData:GetWaistLightCap(type)
	local attribute = AttributePool.AllocAttribute()
	local stone_list = self:GetWaistLightSlotCfg(type)
	local cap = 0
	if not IsEmptyTable(stone_list) then
		for i, v in pairs(stone_list) do
            local data = self:GetWaistLightSlotInfo(v.type, v.slot)
			if data and data.level >= 1 then
				local attr_list = self:GetWaistLightSlotAttrList(v.type, v.slot)
				for key, value in pairs(attr_list) do
					attribute[value.attr_str] = attribute[value.attr_str] + value.attr_value
				end
			end
		end
	end

	cap = AttributeMgr.GetCapability(attribute)
	return cap
end

--五行沧溟红点
function FiveElementsWGData:GetCangMingRemind()
	if not FunOpen.Instance:GetFunIsOpened(FunName.FiveElementsView) then
		return 0
	end
	
    if self.waist_light_type_cfg then
        for i, v in pairs(self.waist_light_type_cfg) do
            local is_red = self:GetCangMingTypeRemind(v.type)
            if is_red then
                return 1
            end
        end
    end

	return 0
end

--单个光环红点
function FiveElementsWGData:GetCangMingTypeRemind(type)
    local data_list = self:GetWaistLightSlotCfg(type)
    local slot_red
    if data_list then
        for k, v in pairs(data_list) do
            slot_red = self:GetCangMingSlotRemind(v.type,v.slot)
            if slot_red then
                return true
            end
        end
    end

    return false
end

--单个孔红点
function FiveElementsWGData:GetCangMingSlotRemind(type, slot)
	local slot_cfg = (self.waist_stone_cfg[type] or {})[slot]
    local info = self:GetWaistLightSlotInfo(type, slot)
    if slot_cfg and info then
        local num = ItemWGData.Instance:GetItemNumInBagById(slot_cfg.item_id)
        if info.level < slot_cfg.max_lv and num >= slot_cfg.cost_item_num then 
            return true
        end
    end

    return false
end