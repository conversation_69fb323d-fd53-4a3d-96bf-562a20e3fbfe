-- H-好友虎摸.xls
local item_table={
[1]={item_id=22007,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
touch_reward={
{},
{low_level_limit=21,high_level_limit=40,},
{low_level_limit=41,high_level_limit=60,},
{low_level_limit=61,high_level_limit=80,},
{low_level_limit=81,high_level_limit=100,},
{low_level_limit=101,high_level_limit=120,},
{low_level_limit=121,high_level_limit=140,},
{low_level_limit=141,high_level_limit=160,},
{low_level_limit=161,high_level_limit=180,},
{low_level_limit=181,high_level_limit=200,},
{low_level_limit=201,high_level_limit=220,},
{low_level_limit=221,high_level_limit=240,},
{low_level_limit=241,high_level_limit=260,},
{low_level_limit=261,high_level_limit=280,},
{low_level_limit=281,high_level_limit=300,},
{low_level_limit=301,high_level_limit=320,},
{low_level_limit=321,high_level_limit=340,},
{low_level_limit=341,high_level_limit=360,},
{low_level_limit=361,high_level_limit=380,},
{low_level_limit=381,high_level_limit=400,},
{low_level_limit=401,high_level_limit=420,},
{low_level_limit=421,high_level_limit=440,},
{low_level_limit=441,high_level_limit=460,},
{low_level_limit=461,high_level_limit=480,},
{low_level_limit=481,high_level_limit=500,},
{low_level_limit=501,high_level_limit=520,},
{low_level_limit=521,high_level_limit=540,},
{low_level_limit=541,high_level_limit=560,},
{low_level_limit=561,high_level_limit=580,},
{low_level_limit=581,high_level_limit=600,},
{low_level_limit=601,high_level_limit=620,},
{low_level_limit=621,high_level_limit=640,},
{low_level_limit=641,high_level_limit=660,},
{low_level_limit=661,high_level_limit=680,},
{low_level_limit=681,high_level_limit=700,},
{low_level_limit=701,high_level_limit=720,},
{low_level_limit=721,high_level_limit=740,},
{low_level_limit=741,high_level_limit=760,},
{low_level_limit=761,high_level_limit=780,},
{low_level_limit=781,high_level_limit=800,},
{low_level_limit=801,high_level_limit=820,},
{low_level_limit=821,high_level_limit=840,},
{low_level_limit=841,high_level_limit=860,},
{low_level_limit=861,high_level_limit=880,},
{low_level_limit=881,high_level_limit=900,},
{low_level_limit=901,high_level_limit=920,},
{low_level_limit=921,high_level_limit=940,},
{low_level_limit=941,high_level_limit=960,},
{low_level_limit=961,high_level_limit=980,},
{low_level_limit=981,high_level_limit=1000,},
{low_level_limit=1001,high_level_limit=2000,}
},

touch_reward_meta_table_map={
},
other_default_table={open_level=1,daily_liveness_limit=30,reward_intimacy=5,random_friend_max_level_intreval=50,need_daily_work_exp=30,},

touch_reward_default_table={low_level_limit=1,high_level_limit=20,touch_reward_item={[0]=item_table[1]},touched_reward_item={[0]=item_table[1]},touch_silver_reward=0,touched_silver_reward=0,}

}

