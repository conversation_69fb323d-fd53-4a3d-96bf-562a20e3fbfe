MarketPopUseView = MarketPopUseView or BaseClass(SafeBaseView)

function MarketPopUseView:__init()
	self.call_back = nil
	self.item_list = {}

	self.active_close = false
	self.view_name = "MarketPopUseView"

	self.view_layer = UiLayer.PopTop
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_third2_panel")
	self:AddViewResource(0, "uis/view/guide_ui_prefab", "layout_market_pop_view")

	self.show_item_info = {}
end

function MarketPopUseView:__delete()
	self.show_item_info = nil
end

function MarketPopUseView:CloseCallBack()
	self.show_item_info = nil

	FunctionGuide.Instance:SetMarketPopUseTime()
end

function MarketPopUseView:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.item)
		self.item_cell:SetItemTipFrom(ItemTip.FROM_MAEKET_POP_USE_EQUIP)
	end

	self.node_list.btn_clear_close_window:SetActive(false)

	XUI.AddClickEventListener(self.node_list.market_pop_btn, BindTool.Bind2(self.ClickHandler, self, true))

	self.wait_time = 8
end

function MarketPopUseView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("market_pop_use_countdown") then
		CountDownManager.Instance:RemoveCountDown("market_pop_use_countdown")
	end

	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

	if self.slider_bg_tween then
		self.slider_bg_tween:Kill()
		self.slider_bg_tween = nil
	end
end

function MarketPopUseView:AddItemShow(item_id, bag_index)
	--只需要展示一个
	if not IsEmptyTable(self.show_item_info) then
		return
	end

	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(item_id)
	if not item_cfg then
		return
	end

	local data = {}
	data.item_id = item_id
	data.bag_index = bag_index
	self.show_item_info = data

	if not self:IsOpen() then
		self:Open()
	else
		self:OnFlush()
	end
end

function MarketPopUseView:OnFlush()
	if IsEmptyTable(self.show_item_info) then
		return
	end

	self:FlushInfoPanel()
	self:FlushTime()
end

function MarketPopUseView:FlushInfoPanel()
	local item_data = ItemWGData.Instance:GetGridData(self.show_item_info.bag_index)
	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(self.show_item_info.item_id)
	self.item_cell:SetData(item_data)
	self.node_list.item_name_txt.text.text = item_cfg.name
end

function MarketPopUseView:FlushTime()
	self:UpdateFunTime(0, wait_time)
	CountDownManager.Instance:AddCountDown("market_pop_use_countdown", 
		BindTool.Bind(self.UpdateFunTime, self), 
		BindTool.Bind(self.CompleteFunTime, self), 
		nil, self.wait_time, 1)
	self:DoBgSliderTween(self.wait_time)
end

function MarketPopUseView:DoBgSliderTween(time)
	if self.slider_bg_tween then
		self.slider_bg_tween:Kill()
		self.slider_bg_tween = nil
	end

	if self.node_list.slider_bg then
		self.node_list.slider_bg.image.fillAmount = 0
		self.slider_bg_tween = self.node_list.slider_bg.image:DOFillAmount(1, time):SetEase(DG.Tweening.Ease.Linear)
	end
end


function MarketPopUseView:UpdateFunTime(elapse_time, total_time)
end

function MarketPopUseView:CompleteFunTime()
	if CountDownManager.Instance:HasCountDown("market_pop_use_countdown") then
		CountDownManager.Instance:RemoveCountDown("market_pop_use_countdown")
	end

	self:Close()
end

function MarketPopUseView:ClickHandler()
	if IsEmptyTable(self.show_item_info) then
		return
	end

	local item_data = ItemWGData.Instance:GetGridData(self.show_item_info.bag_index)
	MarketWGCtrl.Instance:OpenMarketTipItemView(item_data, true)
	self:Close()
end