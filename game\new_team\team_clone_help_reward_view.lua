TeamCloneHelpRewardView = TeamCloneHelpRewardView or BaseClass(SafeBaseView)

function TeamCloneHelpRewardView:__init()
    self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {vector2 = Vector2(0, -2), sizeDelta = Vector2(814, 578)})
	self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_clone_help_reward")
end

function TeamCloneHelpRewardView:LoadCallBack()
    if not self.reward_list then
		self.reward_list = AsyncListView.New(TeamCloneHelpRewardItemCellRender, self.node_list.reward_list)
		self.reward_list:SetStartZeroIndex(false)
	end
end

function TeamCloneHelpRewardView:ReleaseCallBack()
    if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function TeamCloneHelpRewardView:OnFlush()
    local data_list = NewTeamWGData.Instance:GetTeamTargetCfg()
	self.reward_list:SetDataList(data_list)
end

TeamCloneHelpRewardItemCellRender = TeamCloneHelpRewardItemCellRender or BaseClass(BaseRender)

function TeamCloneHelpRewardItemCellRender:LoadCallBack()
	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
		self.reward_list:SetStartZeroIndex(true)
	end
end

function TeamCloneHelpRewardItemCellRender:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function TeamCloneHelpRewardItemCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.node_list.fb_name.text.text = self.data.team_type_name
	self.reward_list:SetDataList(self.data.fenshen_mail_reward)
	local get_reward_count = NewTeamWGData.Instance:GetFenShenMailCountBySeq(self.data.fenshen_mail_seq)
	local total_count = self.data.max_fenshen_mail_count
	local remaining_time = total_count - get_reward_count
	local str = ToColorStr(string.format("%s/%s", remaining_time, total_count), remaining_time > 0 and COLOR3B.DEFAULT or COLOR3B.D_RED)
	self.node_list.score.text.text = str
end