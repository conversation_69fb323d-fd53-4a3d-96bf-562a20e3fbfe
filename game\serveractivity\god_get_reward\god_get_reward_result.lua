GodGetRewardResult = GodGetRewardResult or BaseClass(SafeBaseView)

function GodGetRewardResult:__init()
    self.view_layer = UiLayer.PopWhite
    self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/ts_activity_ui_prefab", "layout_ts_duobao_result")
end

function GodGetRewardResult:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
    if self.tween_sequence then
        self.tween_sequence:Kill()
        self.tween_sequence = nil
    end
end

function GodGetRewardResult:LoadCallBack()
    self.item_cell = ItemCell.New(self.node_list.cell_root)
    XUI.AddClickEventListener(self.node_list.btn_sure, BindTool.Bind(self.Close, self))
    XUI.AddClickEventListener(self.node_list.btn_again, BindTool.Bind(self.OnClickAgainBtn, self))
end

function GodGetRewardResult:CloseCallBack()
    if self.close_call_back then
        self.close_call_back()
    end
end

function GodGetRewardResult:SetCloseCallBack(call_back)
    self.close_call_back = call_back
end

function GodGetRewardResult:SetAgainCallBack(call_back)
    self.again_call_back = call_back
end

function GodGetRewardResult:OnFlush(param_t)
   self:RefreshView()
   self:PlayGetRewardTween()
   -- self:PlayGetRewardAudio()
end

function GodGetRewardResult:RefreshView()
    local result = GodGetRewardWGData.Instance:GetResultInfo()
    if not result then
        self:Close()
        return
    end

    if result.get_exchange_score and result.get_exchange_score > 0 then
        self.node_list.score_lbl.text.text = result.get_exchange_score
        self.node_list.score_group:SetActive(true)
    else
        self.node_list.score_group:SetActive(false)
    end

    local reward_cfg = GodGetRewardWGData.Instance:GetRewardInfoByRewardID(result.reward_id)
    if not IsEmptyTable(reward_cfg) then
        self.item_cell:SetData(reward_cfg.reward_item)
        local item_cfg = ItemWGData.Instance:GetItemConfig(reward_cfg.reward_item.item_id)
        if item_cfg then
            self.node_list.item_name.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
        end
    end

    local cur_layer = GodGetRewardWGData.Instance:GetSelectLayer()
    local cur_draw_info = GodGetRewardWGData.Instance:GetCurDrawInfoByLayer(cur_layer)
    if not cur_draw_info then
        self.node_list.btn_again:SetActive(false)
        return
    end
    self.node_list.btn_again:SetActive(true)

    local const_cfg = ItemWGData.Instance:GetItemConfig(cur_draw_info.draw_consume_item_id)
    if const_cfg then
        local bundle,asset = ResPath.GetItem(const_cfg.icon_id)
        self.node_list.const_img.image:LoadSprite(bundle,asset)
    end
    local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_info.draw_consume_item_id)
    local can_buy = item_num >= cur_draw_info.draw_consume_item_count
    local color = can_buy and COLOR3B.D_GREEN or COLOR3B.D_RED
    self.node_list.const_lbl.text.text = ToColorStr(item_num .. "/" .. cur_draw_info.draw_consume_item_count, color)
end

function GodGetRewardResult:OnClickAgainBtn()
    if self.again_call_back then
        self.again_call_back()
    end
    self:Close()
end

function GodGetRewardResult:PlayGetRewardTween()
    local cell_transform = self.node_list.cell_root.transform
    local cell_canvas_group = self.node_list.cell_root.canvas_group

    cell_canvas_group.alpha = 1
    Transform.SetLocalScaleXYZ(cell_transform, 1.5, 1.5, 1.5)

    if self.tween_sequence then
        self.tween_sequence:Kill()
        self.tween_sequence = nil
    end
    
    local tween_sequence = DG.Tweening.DOTween.Sequence()
    local tween_1 = cell_transform:DOScale(Vector3.one, 0.5)
    local tween_2 = cell_canvas_group:DoAlpha(0, 1, 0.5)
    tween_sequence:Append(tween_1)
    tween_sequence:Join(tween_2)
    tween_sequence:OnComplete(function ()
        self.tween_sequence = nil
    end)

    self.tween_sequence = tween_sequence
end

function GodGetRewardResult:PlayGetRewardAudio()
    local bundle, asset = ResPath.UiseRes("effect_getreward")
    AudioManager.PlayAndForget(bundle, asset)
end