SocietyEnemyRecordView = SocietyEnemyRecordView or BaseClass(SafeBaseView)

function SocietyEnemyRecordView:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(true, true)

    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {sizeDelta = Vector2(862, 580)})
    self:AddViewResource(0, "uis/view/society_ui_prefab", "layout_enemy_hurt_record")
end

function SocietyEnemyRecordView:ReleaseCallBack()
    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end
end

function SocietyEnemyRecordView:LoadCallBack()
    self.record_list = AsyncListView.New(RenderSocietyRecord, self.node_list.list_enemy)
    XUI.AddClickEventListener(self.node_list.btn_clear, BindTool.Bind(self.OnClickClear, self))
    self.node_list.text_tip.text.text = Language.Society.EnemyRecordTip
    self.node_list.title_view_name.text.text = Language.Society.EnemyRecordTitle
end

function SocietyEnemyRecordView:CloseCallBack()
    SocietyWGData.Instance:SynEnemyRecordRealNum()
    SocietyWGCtrl.Instance:Flush("friend_list")

    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ENEMY_RECORD, 0, function() 
        ViewManager.Instance:Open(GuideModuleName.SocietyEnemyRecordView)
    end)
end

function SocietyEnemyRecordView:OnClickClear()
    SocietyWGCtrl.Instance:ClearEnemyRecordInfo()
end

function SocietyEnemyRecordView:OnFlush(param_t)
    local num, data = SocietyWGData.Instance:GetEnemyRecordInfo()
    if num == nil or data == nil then
        return
    end

    if num <= 0 then
        self.node_list.list_enemy:SetActive(false)
        self.node_list.root_no_invite:SetActive(true)
    else
        self.node_list.list_enemy:SetActive(true)
        self.node_list.root_no_invite:SetActive(false)    
        self.record_list:SetDataList(data)
    end
end


RenderSocietyRecord = RenderSocietyRecord or BaseClass(BaseRender)
function RenderSocietyRecord:__init()
    XUI.AddClickEventListener(self.node_list.btn_ignore, BindTool.Bind(self.OnClickIgnore, self))
    XUI.AddClickEventListener(self.node_list.btn_enemy, BindTool.Bind(self.OnClickEnemy, self))
end

function RenderSocietyRecord:__delete()
end

function RenderSocietyRecord:OnClickIgnore()
    if self.data == nil then
        return
    end

    SocietyWGCtrl.Instance:IgnoreOneEnemyRecord(self.data.user_id, self.data.plat_type, self.data.index)
end

function RenderSocietyRecord:OnClickEnemy()
    if self.data == nil then
        return
    end

    local is_enemy_ed = SocietyWGData.Instance:CheckisRoleEnemy(self.data.plat_type, self.data.user_id)
	if is_enemy_ed then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.AddEnemyError)
		return
	end
    
    SocietyWGCtrl.Instance:SendAddEnemy(self.data.user_id, self.data.plat_type)
end

function RenderSocietyRecord:OnFlush()
    if self.data == nil or next(self.data) == nil then
        return
    end

    self.node_list.text_name.text.text = self.data.gamename
    local s = os.date("%Y-%m-%d %H:%M", self.data.timestamp)
    local scene_name = ""
    if self.data.scene_id ~= nil and self.data.scene_id ~= 0 then
        local scene_cfg = ConfigManager.Instance:GetSceneConfig(self.data.scene_id)
        if scene_cfg ~= nil then
            scene_name = scene_cfg.name
        end
    end


    self.node_list.text_record.text.text = string.format(Language.Society.EnemyRecordStr, s, scene_name)

    local is_enemy = SocietyWGData.Instance:CheckisRoleEnemy(self.data.plat_type, self.data.user_id)
    self.node_list.btn_ignore:SetActive(not is_enemy)
    self.node_list.btn_enemy:SetActive(not is_enemy)
    self.node_list.text_is_enemy:SetActive(is_enemy)
end