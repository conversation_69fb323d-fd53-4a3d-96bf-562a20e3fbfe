GodPurchaseView = GodPurchaseView or BaseClass(SafeBaseView)

function GodPurchaseView:__init()
    self.view_layer = UiLayer.Normal
    self.view_style = ViewStyle.Half
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/god_purchase_ui_prefab", "layout_god_purchase")
end

function GodPurchaseView:__delete()
end

function GodPurchaseView:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
	end

	if CountDownManager.Instance:HasCountDown("god_purchase_time") then
        CountDownManager.Instance:RemoveCountDown("god_purchase_time")
    end

	if CountDownManager.Instance:HasCountDown("god_purchase_zhe_time") then
        CountDownManager.Instance:RemoveCountDown("god_purchase_zhe_time")
    end
end

function GodPurchaseView:OpenCallBack()
	GodPurchaseWGCtrl.Instance:ReqGodPurchaseInfo(OA_GOD_RMB_BUY_OPERATE_TYPE.INFO)
end

function GodPurchaseView:LoadCallBack()
 	if self.reward_list == nil then
		self.reward_list = AsyncListView.New(TianShenPurchaseCell, self.node_list.reward_list)
    end

    if not self.model_display then
        self.model_display = OperationActRender.New(self.node_list["display_model"])
        self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	XUI.AddClickEventListener(self.node_list["buy_btn"], BindTool.Bind(self.OnClickBuy, self))
	XUI.AddClickEventListener(self.node_list["zhe_buy_btn"], BindTool.Bind(self.OnClickZheBuy, self))

	local shop_info = GodPurchaseWGData.Instance:GetCurShopCfg()
	if shop_info and shop_info[1] then
		for i = 1, 4 do
			local bundle, asset = ResPath.GetGodPurchaseImg(shop_info[1].title_name .. i)
			self.node_list["title_img" .. i].image:LoadSprite(bundle, asset, function()
				self.node_list["title_img" .. i].image:SetNativeSize()
			end)
		end
	end
end

function GodPurchaseView:ShowIndexCallBack()
    self:FlushTimeCount()
	for i = 1, 4 do
		self.node_list["title_img" .. i]:SetActive(false)
		ReDelayCall(self, function()
			self:DoTweenScaleContent(self.node_list["title_img" .. i])
		end, 0.12 * i, "title_img" .. i)
	end
end

function GodPurchaseView:OnFlush(param_t, index)
	local shop_info = GodPurchaseWGData.Instance:GetCurShopCfg()
	local other_cfg = GodPurchaseWGData.Instance:GetOtherCfg()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
	local info_1 = shop_info and shop_info[1]
	local info_2 = shop_info and shop_info[2]
	if not IsEmptyTable(shop_info) then
		self:FlushModel(info_1)
		local reward_data = {}
		if info_1 and info_1.reward_item then
			for i, v in pairs(info_1.reward_item) do
				table.insert(reward_data, v)
			end
		end

		self.reward_list:SetDataList(reward_data)

	 	local is_open_gift = other_cfg.switch == 1
	 	local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
		local is_all_buy = GodPurchaseWGData.Instance:GetCurShopIsAllBuy()

		self.node_list.zhe_buy_btn:SetActive(not IsEmptyTable(info_2) and act_day == 1 and is_open_gift and not is_all_buy)
	    self.node_list.zhe_time_str:SetActive(not IsEmptyTable(info_2) and act_day == 1 and is_open_gift and not is_all_buy)
	    self.node_list.xz_img:SetActive(not IsEmptyTable(info_2) and act_day == 1 and is_open_gift and not is_all_buy)
	    --self.node_list.desc_img:SetActive(not IsEmptyTable(info_2) and act_day == 1 and is_open_gift and not is_all_buy)
	    --self.node_list.desc_img_1:SetActive(not (act_day == 1 and is_open_gift))

        if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN and info_1 then
			local price = RoleWGData.GetPayMoneyStr(info_1.price, info_1.rmb_type, info_1.rmb_seq)
			self.node_list.buy_price.text.text = price --购买价格
			--self.node_list.all_count_bg:SetActive(true)
		else
			self.node_list.buy_price.text.text = Language.GodPurchase.LookGodView
			--self.node_list.all_count_bg:SetActive(false)
		end

		if info_1 then
			local bug_flag = GodPurchaseWGData.Instance:GetShopIsBuyFlag(info_1.seq)
			XUI.SetButtonEnabled(self.node_list["buy_btn"], not bug_flag)
			if bug_flag then
				self.node_list.buy_price.text.text = Language.GodPurchase.AllBuy
			end
			self.node_list.desc.text.text = info_1.describe_txt or ""
			self:FlushCapStr(info_1)
			self.node_list.all_count.text.text = info_1.all_discount or ""
		end

		if info_2 and act_day == 1 and is_open_gift then
			local bug_flag = GodPurchaseWGData.Instance:GetShopIsBuyFlag(info_2.seq)
			XUI.SetButtonEnabled(self.node_list["zhe_buy_btn"], not bug_flag)
			local price = RoleWGData.GetPayMoneyStr(info_2.price, info_2.rmb_type, info_2.rmb_seq)
			self.node_list.buy_price.text.text = bug_flag and Language.GodPurchase.AllBuy or price
			--self.node_list.zhe_buy_price.text.text = price --打折购买价格
			self.node_list.zhe_count.text.text = info_2.all_discount
			self:FlushZheTimeCount()
		end
	end
end

function GodPurchaseView:DoTweenScaleContent(node)
	local scale = Vector3(1, 1, 1)
	if node ~= nil then
		node:SetActive(true)
		node.rect.localScale = Vector3(2, 2, 2)
		node.rect:DOScale(scale, 0.3)
	end
end

function GodPurchaseView:FlushModel(shop_info)
	if IsEmptyTable(shop_info) then
		return
	end
	
	local display_data = {}
	display_data.should_ani = true
	if shop_info.model_show_itemid ~= 0 and shop_info.model_show_itemid ~= "" then
		local split_list = string.split(shop_info.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = shop_info.model_show_itemid
		end
	end
	
	display_data.bundle_name = shop_info["model_bundle_name"]
    display_data.asset_name = shop_info["model_asset_name"]
    local model_show_type = tonumber(shop_info["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1
    -- display_data.event_trigger_listener_node = self.node_list["EventTriggerListener"]
	if shop_info.model_pos and shop_info.model_pos ~= "" then
		local pos_list = string.split(shop_info.model_pos, "|")
		local pos_x = tonumber(pos_list[1]) or 0
		local pos_y = tonumber(pos_list[2]) or 0
		local pos_z = tonumber(pos_list[3]) or 0

		display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
	end

	if shop_info.model_rot and shop_info.model_rot ~= "" then
		local rot_list = string.split(shop_info.model_rot, "|")
		local rot_x = tonumber(rot_list[1]) or 0
		local rot_y = tonumber(rot_list[2]) or 0
		local rot_z = tonumber(rot_list[3]) or 0

		--display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
		display_data.role_rotation = Vector3(rot_x, rot_y, rot_z)
	end

	if shop_info.model_scale and shop_info.model_scale ~= "" then
		display_data.model_adjust_root_local_scale = shop_info.model_scale
	end

	display_data.model_rt_type = ModelRTSCaleType.L
    self.model_display:SetData(display_data)

    local pos_x, pos_y = 0, 0
	if shop_info.whole_display_pos and shop_info.whole_display_pos ~= "" then
		local pos_list = string.split(shop_info.whole_display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end
	RectTransform.SetAnchoredPositionXY(self.node_list.display_model.rect, pos_x, pos_y)

	local rot_x, rot_y, rot_z = 0, 0, 0
	if shop_info.whole_display_rot and shop_info.whole_display_rot ~= "" then
		local rot_list = string.split(shop_info.whole_display_rot, "|")
		rot_x = tonumber(rot_list[1]) or rot_x
		rot_y = tonumber(rot_list[2]) or rot_y
		rot_z = tonumber(rot_list[3]) or rot_z

		self.node_list.display_model.transform.localRotation = Quaternion.Euler(rot_x, rot_y, rot_z)
	end

	local scale = shop_info["whole_display_scale"]
	if scale and scale ~= "" then
		Transform.SetLocalScaleXYZ(self.node_list["display_model"].transform, scale, scale, scale)
	end
end

function GodPurchaseView:FlushCapStr(shop_info)
	local capability = 0
	local item_data = SortDataByItemColor(shop_info.reward_item)
	for k, v in pairs(item_data) do
		local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		if item_cfg then
			capability = capability + ItemShowWGData.CalculateCapability(v.item_id)
		end
	end

	self.node_list.cap_value.text.text = capability
	--self.node_list.add_cap.text.text = string.format(Language.GodPurchase.AddCapTxt, capability)
end

function GodPurchaseView:OnClickBuy()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		local is_all_buy = GodPurchaseWGData.Instance:GetCurShopIsAllBuy()
		if is_all_buy then
			TipWGCtrl.Instance:ShowSystemMsg(Language.GodPurchase.AllShopBuy)
			return
		end

		local shop_info = GodPurchaseWGData.Instance:GetCurShopCfg()
		local info_1 = shop_info and shop_info[1]
		if info_1 then
			RechargeWGCtrl.Instance:Recharge(info_1.price, info_1.rmb_type, info_1.rmb_seq)
		end
	else
		ViewManager.Instance:Open(GuideModuleName.GuiXuDreamView)
		return
	end
end

function GodPurchaseView:OnClickZheBuy()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
	if activity_info.status ~= ACTIVITY_STATUS.OPEN then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GodPurchase.CurActHadEnd)
		return
	end

	local is_all_buy = GodPurchaseWGData.Instance:GetCurShopIsAllBuy()
	if is_all_buy then
		TipWGCtrl.Instance:ShowSystemMsg(Language.GodPurchase.AllShopBuy)
		return
	end

	local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
	local other_cfg = GodPurchaseWGData.Instance:GetOtherCfg()
	if act_day == 1 and other_cfg.switch == 1 then
		local shop_info = GodPurchaseWGData.Instance:GetCurShopCfg()
		local info_2 = shop_info and shop_info[2]
		if info_2 then
			RechargeWGCtrl.Instance:Recharge(info_2.price, info_2.rmb_type, info_2.rmb_seq)
		end
	end
end

function GodPurchaseView:FlushTimeCount()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
	local time
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
	else
		time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
	end 

    if time > 0 then
        if CountDownManager.Instance:HasCountDown("god_purchase_time") then
            CountDownManager.Instance:RemoveCountDown("god_purchase_time")
        end

        CountDownManager.Instance:AddCountDown("god_purchase_time", 
            BindTool.Bind(self.FinalUpdateTimeCallBack, self), 
            BindTool.Bind(self.OnComplete, self), 
            nil, time, 1)
    else
        self:OnComplete()
    end
end

function GodPurchaseView:FinalUpdateTimeCallBack(now_time, total_time)
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
    local time = math.ceil(total_time - now_time)
    local time_str = TimeUtil.FormatSecondDHM8(time)
    if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
    	self.node_list["time_str"].text.text = string.format(Language.GodPurchase.ActivityTime, time_str) 
    else
    	self.node_list["time_str"].text.text = string.format(Language.GodPurchase.OpenActivityTime, time_str) 
    end
end

function GodPurchaseView:OnComplete()
    self.node_list.time_str.text.text = ""
end

function GodPurchaseView:FlushZheTimeCount()
    local time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
    if time > 0 then
        if CountDownManager.Instance:HasCountDown("god_purchase_zhe_time") then
            CountDownManager.Instance:RemoveCountDown("god_purchase_zhe_time")
        end

        CountDownManager.Instance:AddCountDown("god_purchase_zhe_time", 
            BindTool.Bind(self.FinalUpdateZheTimeCallBack, self), 
            BindTool.Bind(self.OnZheComplete, self), 
            nil, time, 1)
    else
        self:OnZheComplete()
    end
end

function GodPurchaseView:FinalUpdateZheTimeCallBack(now_time, total_time)
    local time = math.ceil(total_time - now_time)
    local zhe_time_str = TimeUtil.FormatSecondDHM8(time)
    self.node_list["zhe_time_str"].text.text = string.format(Language.GodPurchase.ZheTime, zhe_time_str) 
end

function GodPurchaseView:OnZheComplete()
    self.node_list.zhe_time_str.text.text = ""
end

-----------------------GodPurchaseCell-------------
GodPurchaseCell = GodPurchaseCell or BaseClass(BaseRender)

function GodPurchaseCell:__init()
	self.item = ItemCell.New(self.node_list["cell_pos"])
end

function GodPurchaseCell:__delete()
	if self.item then
		self.item:DeleteMe()
		self.item = nil
	end
end

function GodPurchaseCell:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.item:SetData(self.data)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	self.node_list.name.text.text = item_cfg.name
end