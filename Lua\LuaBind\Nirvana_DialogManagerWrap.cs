﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_DialogManagerWrap
{
	public static void Register(LuaState L)
	{
		L.BeginStaticLibs("DialogManager");
		<PERSON><PERSON>RegFunction("ShowMessage", ShowMessage);
		<PERSON><PERSON>RegFunction("ShowConfirm", ShowConfirm);
		L.EndStaticLibs();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ShowMessage(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			string arg0 = ToLua.CheckString(L, 1);
			string arg1 = ToLua.CheckString(L, 2);
			string arg2 = ToLua.CheckString(L, 3);
			System.Action arg3 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 4);
			Nirvana.DialogManager.ShowMessage(arg0, arg1, arg2, arg3);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ShowConfirm(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 6);
			string arg0 = ToLua.CheckString(L, 1);
			string arg1 = ToLua.CheckString(L, 2);
			string arg2 = ToLua.CheckString(L, 3);
			System.Action arg3 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 4);
			string arg4 = ToLua.CheckString(L, 5);
			System.Action arg5 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 6);
			Nirvana.DialogManager.ShowConfirm(arg0, arg1, arg2, arg3, arg4, arg5);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

