PrivilegedGuidanceView = PrivilegedGuidanceView or BaseClass(SafeBaseView)
function PrivilegedGuidanceView:__init()
    self.view_name = "PrivilegedGuidanceView"
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/privileged_guidance_ui_prefab", "layout_privileged_view")
end

function PrivilegedGuidanceView:ReleaseCallBack()
    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end
end

function PrivilegedGuidanceView:LoadCallBack()
    if not self.item_list then
        self.item_list = AsyncListView.New(PrivilegedItemRender, self.node_list["item_list"])
        --self.item_list:SetSelectCallBack(BindTool.Bind(self.OnSelectItemCallBack, self))
    end

    for i = 1,2 do
        self.node_list["select_btn_name"..i].text.text = Language.PrivilegedGuidance.TypeBtnName[i]
        self.node_list["select_btn_name_hl"..i].text.text = Language.PrivilegedGuidance.TypeBtnName[i]
        self.node_list["select_btn_red"..i]:SetActive(false)
        XUI.AddClickEventListener(self.node_list["select_btn"..i],BindTool.Bind(self.ClickSelectBtn,self,i))
    end

    self.node_list["select_btn1"].toggle.isOn = true
    self.select_btn_type = 1
end

function PrivilegedGuidanceView:OpenCallBack()
    LongXiWGCtrl.Instance:SendLongXiRequest(LONGXI_OPERATE_TYPE.LONGXI_OPERATE_TYPE_ITEM_INFO)
end

function PrivilegedGuidanceView:CloseCallBack()
    LongXiWGData.Instance:SetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN, false)
    LongXiWGData.Instance:SetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL, false)
end

function PrivilegedGuidanceView:OnFlush(param_t)
    for k,v in pairs(param_t) do
        if k == "flush_effect" then
            self:PlayUpLvEffect(v.seq)
        end
    end

    for i = 1, 2 do
        local is_remind = PrivilegedGuidanceWGData.Instance:GuidanceRemindBySubType(i)
        self.node_list["select_btn_red"..i]:SetActive(is_remind)
    end

    self:FlushItemList()
end

function PrivilegedGuidanceView:PlayUpLvEffect(seq)
    if not LongXiWGData.Instance:GetAutoUpLevelStateByType(seq) then
        return
    end

    TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji,
    is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["effect_root"]})
end

function PrivilegedGuidanceView:ClickSelectBtn(tab_type)
    if self.select_btn_type == tab_type then
        return 
    end

    self.select_btn_type = tab_type
    self:FlushItemList()
end

function PrivilegedGuidanceView:FlushItemList()
    local item_data = PrivilegedGuidanceWGData.Instance:GetItemListsBySub(self.select_btn_type)
    self.item_list:SetDataList(item_data)
end


----------------------------------------------------------------------------------------
local AdvertisementColor = {
    [0] = "#79564e",
    [1] = "#29522f",
    [2] = "#693f22",
    [3] = "#ffdf5f",
    [4] = "#fffbb7",
    [5] = "#ffdf5f",
    [6] = "#79564e",
    [7] = "#ffdf5f",
    [8] = "#fffbb7",
    [9] = "#79564e",
}

local AdvertisementOutLineColor = {
    [3] = "#754385",
    [4] = "#7b101f",
    [5] = "#463b7d",
    [7] = "#374372",
    [8] = "#7b101f",
}

local EffectList = {
    [0] = "UI_zztq3_jin",
    [1] = "UI_zztq3_lv",
    [2] = "UI_zztq3_jin",
    [3] = "UI_zztq3_zi",
    [4] = "UI_zztq3_hong",
    [5] = "UI_zztq3_zi",
    [6] = "UI_zztq3_jin",
    [7] = "UI_zztq3_lan",
    [8] = "UI_zztq3_hong",
    [9] = "UI_zztq3_jin",
}

PrivilegedItemRender = PrivilegedItemRender or BaseClass(BaseRender)
function PrivilegedItemRender:__delete()
    if nil ~= self.desc_list then
        for k,v in pairs(self.desc_list) do
            v:DeleteMe()
        end
        self.desc_list = nil
    end

    if nil ~= self.reward_list then
        for k,v in pairs(self.reward_list) do
            v:DeleteMe()
        end
        self.reward_list = nil
    end
end

function PrivilegedItemRender:LoadCallBack()
    if not self.reward_list then
        self.reward_list = {}
        local num = self.node_list.pos_content.transform.childCount
        for i = 1, num do
            self.reward_list[i] = ItemCell.New(self.node_list["pos_" .. i])
        end
    end

    if not self.desc_list then
        self.desc_list = {}
        local slot_num = self.node_list.desc_list.transform.childCount
        for i = 1, slot_num do
            self.desc_list[i] = AdvertisementItemRender.New(self.node_list.desc_list:FindObj("advertisement_" .. i))
        end
    end

    self.node_list["btn_buy"].button:AddClickListener(BindTool.Bind(self.OnClickBtnBuy, self))
    self.node_list["btn_reward"].button:AddClickListener(BindTool.Bind(self.OnClickBtnReward, self))
    self.node_list["special_btn_buy"].button:AddClickListener(BindTool.Bind(self.OnClickBtnBuy, self))
    self.node_list["special_btn_reward"].button:AddClickListener(BindTool.Bind(self.OnClickBtnReward, self))
    self.node_list["btn_boss_activate"].button:AddClickListener(BindTool.Bind(self.OnClickBossEffect, self))
    self.node_list["btn_go"].button:AddClickListener(BindTool.Bind(self.OnClickGoPanel, self))
    XUI.AddClickEventListener(self.node_list.skill_btn, BindTool.Bind(self.OnClickSkillBtn, self))
end

function PrivilegedItemRender:OnFlush()
    if not self.data.cfg then
        return
    end

    local width = self.data.cfg.if_skill == 1 and 172 or 262
    self.node_list["reward_list"].rect.sizeDelta = Vector2(width, COMMON_CONSTS.ItemCellSize)
    self.node_list["reward_list"].scroll_rect.horizontalNormalizedPosition = 0
    self.node_list.btn_root:SetActive(self.data.cfg.type ~= PrivilegedGuidanceType.Guidance_Type_1)
    self.node_list.special_btn_root:SetActive(self.data.cfg.type == PrivilegedGuidanceType.Guidance_Type_1)
    local cost_num, is_rmb = PrivilegedGuidanceWGData.Instance:GetNeedGold(self.data.cfg.type)
    local buy_btn_show, reward_btn_show, is_grey, btn_str = PrivilegedGuidanceWGData.Instance:GetRenderCanBuy(self.data.cfg.type)
    self.node_list.btn_buy:SetActive(buy_btn_show)
    self.node_list.special_btn_buy:SetActive(buy_btn_show)
    self.node_list.special_btn_reward:SetActive(reward_btn_show)
    self.node_list.icon_gold:SetActive(not is_rmb)
    self.node_list.btn_reward:SetActive(reward_btn_show)
    self.node_list.btn_reward_text.text.text = btn_str
    self.node_list.need_price.text.text = ToColorStr(self.data.cfg.need_gold, AdvertisementColor[self.data.cfg.type])
    self.node_list.btn_reward_red:SetActive(not is_grey and reward_btn_show)
    self.node_list.special_btn_reward_red:SetActive(not is_grey and reward_btn_show)
    XUI.SetGraphicGrey(self.node_list["btn_reward"], is_grey)
    XUI.SetGraphicGrey(self.node_list["special_btn_reward"], is_grey)
    self.node_list["btn_boss_activate"]:SetActive(self.data.cfg.type == PrivilegedGuidanceType.Guidance_Type_9)
    self.node_list.gold_price.text.text = cost_num
    self.node_list.special_gold_price.text.text = cost_num
    local bundle, asset = ResPath.GetRawImagesPNG("a2_tqyd_" .. self.data.cfg.type)
    self.node_list["bg"].raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["bg"].raw_image:SetNativeSize()
    end)

    -- if self.data.cfg.type == PrivilegedGuidanceType.Guidance_Type_1 then
    --     local has_week_card = RechargeWGData.Instance:IsHasWeekCard()
    --     width = has_week_card and 120 or 208
    -- end

    self.node_list.max_level:SetActive(self.data.cfg.type == PrivilegedGuidanceType.Guidance_Type_9 and is_grey)
    -- self.node_list.special_btn_buy.rect.sizeDelta = Vector2(width, 63)
    self.node_list.tap_sign_txt.text.text = Language.PrivilegedGuidance.TapSignTxt[self.data.cfg.tap_sign]
    self.node_list.skill_btn:SetActive(self.data.cfg.if_skill == 1)
    if self.data.cfg.if_skill == 1 then
        self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(self.data.cfg.skill_icon))
    end
    -- TODO 需要改为新版处理方式
    -- local out_line = self.node_list.need_price:GetOrAddComponent(typeof(UnityEngine.UI.Outline))
    -- out_line.effectDistance = Vector2(0.5, -0.5)
    -- if AdvertisementOutLineColor[self.data.cfg.type] then
    --     out_line.effectColor = Str2C3b(AdvertisementOutLineColor[self.data.cfg.type])
    --     out_line.enabled = true
    -- else
    --      out_line.enabled = false
    -- end
    
    for k,v in pairs(self.reward_list) do
        if self.data.cfg.reward_item[k - 1] then
            self.node_list["pos_" .. k]:SetActive(true)
            v:SetData(self.data.cfg.reward_item[k - 1])
        else
            self.node_list["pos_" .. k]:SetActive(false)
        end
    end

    local data_list = PrivilegedGuidanceWGData.Instance:GetAdvertisementList(self.data.cfg.type)
    for i,v in ipairs(self.desc_list) do
        if data_list[i] then
            v:SetActive(true)
            v:SetData(data_list[i])
        else
            v:SetActive(false)
        end
    end

    local bundle, asset = ResPath.GetA2Effect(EffectList[self.data.cfg.type])
    self.node_list.effect:ChangeAsset(bundle, asset)
end

function PrivilegedItemRender:OnClickSkillBtn()
    if not self.data.cfg then
        return
    end

    local show_data = {
        icon = self.data.cfg.skill_icon,
        top_text = self.data.cfg.skill_name,
        body_text = self.data.cfg.skill_des,

        x = -49,
        y = 0,
        set_pos = true,
    }

    NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

function PrivilegedItemRender:OnClickBtnBuy()
    if not self.data.cfg then
        return
    end

    local can_buy = PrivilegedGuidanceWGData.Instance:GetRenderCanBuy(self.data.cfg.type)
    if not can_buy then
        return
    end

    local type = self.data.cfg.type
    if type == PrivilegedGuidanceType.Guidance_Type_1 then
        local cur_wc_grade = RechargeWGData.Instance:GetWeekCardGrade()
        cur_wc_grade = cur_wc_grade < 0 and 1 or cur_wc_grade + 1
        local cfg = RechargeWGData.Instance:GetWeekCardOtherCfg(cur_wc_grade)
        if cfg then
            local cost_num = cfg.week_card_price
            local rmb_seq = cfg.rmb_seq
            RechargeWGCtrl.Instance:Recharge(cost_num, cfg.rmb_type, rmb_seq)
        end
    elseif type == PrivilegedGuidanceType.Guidance_Type_2 then
        local month_card_cfg = RechargeWGData.Instance:GetTZCardCfg(INVEST_CARD_TYPE.MonthCard)
        local month_card_cost = month_card_cfg and month_card_cfg.need_rmb or 0
        local month_card_cost_type = month_card_cfg and month_card_cfg.rmb_type or 0
        if month_card_cost > 0 then
            RechargeWGCtrl.Instance:Recharge(month_card_cost, month_card_cost_type, INVEST_CARD_TYPE.MonthCard)
        end
    elseif type == PrivilegedGuidanceType.Guidance_Type_3 then
        local storehouse_cfg = RechargeWGData.Instance:GetTZCardCfg(INVEST_CARD_TYPE.StorehouseCard)
        local storehouse_cost = storehouse_cfg and storehouse_cfg.need_rmb or 0
        local storehouse_cost_type = storehouse_cfg and storehouse_cfg.rmb_type or 0
        local invest_need_vip_level = storehouse_cfg.invest_need_vip_level
        if storehouse_cost > 0 and RoleWGData.Instance:GetRoleVo().vip_level >= invest_need_vip_level then
            RechargeWGCtrl.Instance:Recharge(storehouse_cost, storehouse_cost_type, INVEST_CARD_TYPE.StorehouseCard)
        end
    elseif type == PrivilegedGuidanceType.Guidance_Type_4 then
        local base_data = LongXiWGData.Instance:GetLongXiBaseInfoByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)
        RechargeWGCtrl.Instance:Recharge(base_data.price, base_data.rmb_type, base_data.rmb_seq)
    elseif type == PrivilegedGuidanceType.Guidance_Type_5 then
        local base_data = LongXiWGData.Instance:GetLongXiBaseInfoByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
        RechargeWGCtrl.Instance:Recharge(base_data.price, base_data.rmb_type, base_data.rmb_seq)
    elseif type == PrivilegedGuidanceType.Guidance_Type_6 then
        local longshen_level = DragonTempleWGData.Instance:GetLongShenLevel()
        local cur_level_cfg = DragonTempleWGData.Instance:GetLongShenLevelCfg(longshen_level)
        local next_level_cfg = DragonTempleWGData.Instance:GetLongShenLevelCfg(longshen_level + 1)
        if IsEmptyTable(next_level_cfg) or IsEmptyTable(cur_level_cfg) then
            return
        end

        RechargeWGCtrl.Instance:Recharge(cur_level_cfg.price, cur_level_cfg.rmb_type, cur_level_cfg.rmb_seq)
    elseif type == PrivilegedGuidanceType.Guidance_Type_7 then
        local no_buy = PierreDirectPurchaseWGData.Instance:GetBuyTypeAllNotBuy(1)
        local data = PierreDirectPurchaseWGData.Instance:GetDirectPurchaseCfgByType(1)
        if no_buy and not IsEmptyTable(data) then
            RechargeWGCtrl.Instance:Recharge(data.buy_all_price, data.rmb_type, data.buy_type)
        end
    elseif type == PrivilegedGuidanceType.Guidance_Type_8 then
        local cfg = DragonTempleWGData.Instance:GetOtherCfg()
        if not IsEmptyTable(cfg) then
            local has_privilege = DragonTempleWGData.Instance:IsHatchHasPrivilege()
            if not has_privilege then
                RechargeWGCtrl.Instance:Recharge(cfg.hatch_price, cfg.hatch_rmb_type, cfg.hatch_rmb_seq)
            end 
        end
    elseif type == PrivilegedGuidanceType.Guidance_Type_9 then
        local is_active = BossPrivilegeWGData.Instance:GetBossPrivilegeIsActive() --是否激活过
        if not is_active then --激活
            local times_list = BossPrivilegeWGData.Instance:GetPrivilegeTimesCfg(1)
            local enough = RoleWGData.Instance:GetIsEnoughUseGold(times_list.condition_value)
            if enough then
                BossPrivilegeWGCtrl.Instance:SendBossPrivilegeReq(BOSS_DROP_PRIVILEGE_OPERATE_TYPE.LEVEL_UP) --升级特权请求(0为激活)
                --首次激活自动开启特权
                BossPrivilegeWGCtrl.Instance:SendBossPrivilegeReq(BOSS_DROP_PRIVILEGE_OPERATE_TYPE.SET_STATUS, BOSS_DROP_PRIVILEGE_OPEN_TYPE.OPEN)
            else
                VipWGCtrl.Instance:OpenTipNoGold()
            end
        else
            local cur_level ,max_level = BossPrivilegeWGData.Instance:GetCurPrivilegeLevel()
            local next_level = cur_level >= max_level and max_level or cur_level + 1
            local next_times_list = BossPrivilegeWGData.Instance:GetPrivilegeTimesCfg(next_level)
            if cur_level >= max_level then
                return
            end

            if next_times_list.condition_type == 1 then
                local is_enough_money = RoleWGData.Instance:GetIsEnoughUseGold(next_times_list.condition_value)
                if is_enough_money then
                    BossPrivilegeWGCtrl.Instance:SendBossPrivilegeReq(BOSS_DROP_PRIVILEGE_OPERATE_TYPE.LEVEL_UP)
                else
                    VipWGCtrl.Instance:OpenTipNoGold()
                end
            elseif next_times_list.condition_type == 2 then
                RechargeWGCtrl.Instance:Recharge(next_times_list.condition_value, next_times_list.rmb_type, next_times_list.rmb_seq)
            end
        end
    elseif type == PrivilegedGuidanceType.Guidance_Type_10 then
        local need_level = RechargeWGData.Instance:GetZFCurBuyCfg()
        if IsEmptyTable(need_level) then
            return
        end

        RechargeWGCtrl.Instance:Recharge(need_level.rmb, need_level.rmb_type, need_level.rmb_seq)
    end
end

function PrivilegedItemRender:OnClickBtnReward()
    if not self.data.cfg then
        return
    end

    local type = self.data.cfg.type
    if type == PrivilegedGuidanceType.Guidance_Type_1 then
        local cur_wc_grade = RechargeWGData.Instance:GetWeekCardGrade()
        cur_wc_grade = cur_wc_grade < 0 and 1 or cur_wc_grade + 1
        local other_cfg = RechargeWGData.Instance:GetWeekCardOtherCfg(cur_wc_grade)
        local day_num = other_cfg and other_cfg.week_card_day or 0
        local cur_day = RechargeWGData.Instance:GetWeekCardCurrentDay()
        local has_week_card = RechargeWGData.Instance:IsHasWeekCard()
        local get_flag = RechargeWGData.Instance:GetWeekCardRewardFlag(cur_day)
        local can_get_reward = has_week_card and cur_day <= day_num and not get_flag
        local has_get_reward = has_week_card and cur_day <= day_num and get_flag
        if not has_week_card then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Recharge.WeekCardTips[1])
        elseif has_get_reward then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Recharge.WeekCardTips[0])
        elseif can_get_reward then
            RechargeWGCtrl.Instance:SendWeekCardOperate(WEEKCARD_OPERATE_TYPE.WEEKCARD_OPERATE_TYPE_FETCH_DAILY_REWARD,cur_day)
        end
    elseif type == PrivilegedGuidanceType.Guidance_Type_2 then
        local card_info = RechargeWGData.Instance:GetInvestCardInfo(INVEST_CARD_TYPE.MonthCard)
        local can_get_reward = RechargeWGData.Instance:CanGetInvestCardReward(INVEST_CARD_TYPE.MonthCard, card_info.cur_day)
        RechargeWGCtrl.Instance:SendFetchInvestCardReward(INVEST_CARD_TYPE.MonthCard, card_info.cur_day)
    elseif type == PrivilegedGuidanceType.Guidance_Type_3 then
        local card_info = RechargeWGData.Instance:GetInvestCardInfo(INVEST_CARD_TYPE.StorehouseCard)
        RechargeWGCtrl.Instance:SendFetchInvestCardReward(INVEST_CARD_TYPE.StorehouseCard, card_info.cur_day)
    elseif type == PrivilegedGuidanceType.Guidance_Type_4 then
        local data = LongXiWGData.Instance:GetLongXiDataByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)
        if data.is_active > 0 then
            return
        end

        if PrivilegedGuidanceWGData.Instance:IsCanUp(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL) then
            if LongXiWGData.Instance:GetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL) then
                LongXiWGData.Instance:SetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL, false)
            else
                LongXiWGData.Instance:SetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL, true)
                PrivilegedGuidanceWGCtrl.Instance:UpLevel(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)
            end
        else
            TipWGCtrl.Instance:ShowSystemMsg(Language.LongXi.UpGoodsNotEnough)
        end
    elseif type == PrivilegedGuidanceType.Guidance_Type_5 then
        local data = LongXiWGData.Instance:GetLongXiDataByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
        if data.is_active > 0 then
            return
        end

        if PrivilegedGuidanceWGData.Instance:IsCanUp(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN) then
            if LongXiWGData.Instance:GetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN) then
                LongXiWGData.Instance:SetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN, false)
            else
                LongXiWGData.Instance:SetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN, true)
                PrivilegedGuidanceWGCtrl.Instance:UpLevel(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
            end
        else
            TipWGCtrl.Instance:ShowSystemMsg(Language.LongXi.UpGoodsNotEnough)
        end
    elseif type == PrivilegedGuidanceType.Guidance_Type_8 then
        local has_privilege = DragonTempleWGData.Instance:IsHatchHasPrivilege()
        if has_privilege then
            local can_get_daily_reward = DragonTempleWGData.Instance:CanGetHatchDailyReward()
            if can_get_daily_reward then
                DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.FETCH_HATCH_DAILY_REWARD)
                return 
            end
        end
    end
end

function PrivilegedItemRender:OnClickBossEffect()
    BossPrivilegeWGCtrl.Instance:OpenBossPrivilegeUpgradeView()
end

function PrivilegedItemRender:OnClickGoPanel()
    if not self.data.cfg then
        return
    end

    ViewManager.Instance:Close(GuideModuleName.PrivilegedGuidanceView)
    FunOpen.Instance:OpenViewNameByCfg(self.data.cfg.open_panel)
end


------------------------------------------------------------------------
AdvertisementItemRender = AdvertisementItemRender or BaseClass(BaseRender)
function AdvertisementItemRender:LoadCallBack()
    
end

function AdvertisementItemRender:OnFlush()
    if not self.data then
        return
    end

    local pic = self.data.type % 2 == 0 and "a2_tqyd_zs" or "a2_tqyd_zs2"
    local bundle, asset = ResPath.GetCommon(pic)
    self.node_list.icon.image:LoadSpriteAsync(bundle, asset, function ()
        self.node_list.icon.image:SetNativeSize()
    end)
    -- TODO 需要改为新版处理方式
    -- local out_line = self.node_list.desc:GetOrAddComponent(typeof(UnityEngine.UI.Outline))
    -- out_line.effectDistance = Vector2(0.5, -0.5)
    -- if AdvertisementOutLineColor[self.data.type] then
    --     out_line.effectColor = Str2C3b(AdvertisementOutLineColor[self.data.type])
    --     out_line.enabled = true
    -- else
    --      out_line.enabled = false
    -- end
    
    self.node_list.desc.text.text = ToColorStr(self.data.advertisement, AdvertisementColor[self.data.type])
end
