SupremeFieldsSkillShowView = SupremeFieldsSkillShowView or BaseClass(SafeBaseView)

function SupremeFieldsSkillShowView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:SetMaskBg(false)
	self:SetMaskBgAlpha(0)

	-- self:AddViewResource(0, "uis/view/skill_show_ui_prefab", "skill_show_bg_scene")
	self:AddViewResource(0, "uis/view/skill_show_ui_prefab", "layout_field_skill_show_view")
	self:AddViewResource(0, "uis/view/skill_show_ui_prefab", "layout_skill_desc_view")
end

function SupremeFieldsSkillShowView:LoadCallBack()
	self.cur_select_field_index = -1
	if not self.field_show_list then
		self.field_show_list = AsyncListView.New(FieldRender, self.node_list["field_list_view"])
		self.field_show_list:SetSelectCallBack(BindTool.Bind(self.OnSelectCustomizedCallBack, self))
		self.field_show_list:SetLimitSelectFunc(BindTool.Bind(self.IsLimitClick, self))
	end

	if not self.skill_pre_view then
		self.skill_pre_view = SkillPreview.New(self.node_list.skill_pre_root, self.node_list.skill_pre_rawimage, true)
		self.skill_pre_view:SetPreviewPlayEndCb(BindTool.Bind1(self.PreviewPlayEnd, self))
	end

	self.skill_play_timestemp = 0
    self.normal_attack_play_timestemp = 0
end

function SupremeFieldsSkillShowView:ReleaseCallBack()
	self.skill_data = nil
	self.cur_select_field_index = -1

	if self.field_show_list then
		self.field_show_list:DeleteMe()
		self.field_show_list = nil
	end

	if self.skill_pre_view then
		self.skill_pre_view:DeleteMe()
		self.skill_pre_view = nil
	end

	self.skill_play_timestemp = nil
    self.normal_attack_play_timestemp = nil
	self:RemovePreSkillDelayTimer()
end

function SupremeFieldsSkillShowView:SetShowDataAndOpen(data)
	self.show_data = data
	self:Open()
end

function SupremeFieldsSkillShowView:ShowIndexCallBack()

end

-- 列表选择返回
function SupremeFieldsSkillShowView:OnSelectCustomizedCallBack(item)
	if self:IsLimitClick() then
		return
	end

	if nil == item or nil == item.data then
		return
	end

	self.skill_data = item.data
	local data = item.data
	if self.cur_select_field_index == data.type then
		return
	end

	self.cur_select_field_index = data.type

	if self.skill_pre_view:GetPreviewIsLoaded() then
		self.skill_pre_view:PlaySkill(self.skill_data.skill_preview_id, true)
	else
		self.skill_pre_view:SetPreviewLoadCb(function()
			self.skill_pre_view:PlaySkill(self.skill_data.skill_preview_id, true)
		end)
	end

	local skill_id = data.skill_id
	local skill_cfg = SupremeFieldsWGData.Instance:GetFootLightSkillCfg(skill_id)
	self.skill_play_timestemp = Status.NowTime + 4
	local skill_name, skill_icon, skill_desc

	if skill_cfg then
		skill_name = skill_cfg.skill_name
		skill_icon = skill_cfg.icon
		skill_desc = skill_cfg.skill_txt

		self:FlushSkillInfo(skill_id, 1, skill_name, skill_desc)

		local bundle, asset = ResPath.GetSkillIconById(skill_icon)
		self.node_list["skill_icon"].image:LoadSpriteAsync(bundle, asset, function()
			self.node_list["skill_icon"].image:SetNativeSize()
		end)
	end
end

--刷新技能描述.
function SupremeFieldsSkillShowView:FlushSkillInfo(skill_id, skill_level, skill_name, skill_desc)
	local client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, skill_level)
	if client_cfg then
		self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(client_cfg.icon_resource))
	end

	self.node_list.skill_nane.text.text = skill_name
	self.node_list.skill_desc.text.text = skill_desc
end

function SupremeFieldsSkillShowView:IsLimitClick(no_tips)
	local is_playing_skill = Status.NowTime < self.skill_play_timestemp
	if not no_tips and is_playing_skill then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Skill.ShowSkillLimitClickStr)
	end

	return is_playing_skill
end

function SupremeFieldsSkillShowView:OnFlush()
	if not self.show_data then
		return
	end

	local show_list = SupremeFieldsWGData.Instance:GetFootLightSkillList()
	if IsEmptyTable(show_list) then
		return
	end

	if self.field_show_list then
		self.field_show_list:SetDataList(show_list)
	end

	local show_type = self.show_data.fazhen_id
	if self.cur_select_field_index ~= show_type then
		local jump_index = 1
		for k, v in pairs(show_list) do
			if show_type == v.type then
				jump_index = k
				break
			end
		end

		self.field_show_list:JumpToIndex(jump_index)
	end
end

--移除回调
function SupremeFieldsSkillShowView:RemovePreSkillDelayTimer()
    if self.show_pre_skill_delay_timer then
        GlobalTimerQuest:CancelQuest(self.show_pre_skill_delay_timer)
        self.show_pre_skill_delay_timer = nil
    end
end

-- 技能预览结束延迟4秒继续播放
function SupremeFieldsSkillShowView:PreviewPlayEnd()
	self:RemovePreSkillDelayTimer()

	self.show_pre_skill_delay_timer = GlobalTimerQuest:AddDelayTimer(function ()
		if not self.skill_data then
			return
		end

		if self.skill_pre_view and self.skill_pre_view:GetPreviewIsLoaded() then
			self.skill_pre_view:PlaySkill(self.skill_data.skill_preview_id)
		end
	end, 4)
end

--===================================================================
FieldRender = FieldRender or BaseClass(BaseRender)

function FieldRender:OnFlush()
	if self.data == nil then
		return
	end

	local skill_cfg = SupremeFieldsWGData.Instance:GetFootLightSkillCfg(self.data.skill_id)
	local bundle, asset = ResPath.GetSkillIconById(skill_cfg.icon)
	self.node_list.icon.image:LoadSprite(bundle, asset, function()
		self.node_list.icon.image:SetNativeSize()
	end)

	self.node_list.name.text.text = self.data.skill_name
end

function FieldRender:OnSelectChange(is_select)
	self.node_list.select_hl:SetActive(is_select)
end
