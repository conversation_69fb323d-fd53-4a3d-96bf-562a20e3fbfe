--排行榜
ExperienceFBBuffView = ExperienceFBBuffView or BaseClass(SafeBaseView)

function ExperienceFBBuffView:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(560, 500)})
	self:AddViewResource(0, "uis/view/offlinerest_ui_prefab", "layout_experience_fb_buff")
end

function ExperienceFBBuffView:OpenCallBack()

end

function ExperienceFBBuffView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.OfflineRest.ExperienceFbBuffTitle

    if not self.layout_experience_buff_list then
	    self.layout_experience_buff_list = AsyncListView.New(ExperienceBuffItem, self.node_list.layout_experience_buff_list)
    end
end

function ExperienceFBBuffView:CloseCallBack()

end

function ExperienceFBBuffView:ReleaseCallBack()
    if self.layout_experience_buff_list then
        self.layout_experience_buff_list:DeleteMe()
        self.layout_experience_buff_list = nil
    end

	self.show_level = nil
end

function ExperienceFBBuffView:SetCurrlevel(curr_level)
	self.show_level = curr_level
end


function ExperienceFBBuffView:OnFlush(param_t)
	self:FlushRankList()
end

function ExperienceFBBuffView:FlushRankList()
	if not self.show_level then
		return
	end

	local level_cfg = ExperienceFbWGData.Instance:GetLevelCfgByLevel(self.show_level)
	local rank_list = ExperienceFbWGData.Instance:GetCardListByIndex(level_cfg and level_cfg.card_pool or 0)
	if #rank_list <= 0 then
		return
	end

	local aim_list = {}
	for _, data_list in pairs(rank_list) do
		for level, data in ipairs(data_list) do
			if data.seq ~= nil and data.level ~= nil then
				table.insert(aim_list, data)
			end
		end
	end


	self.layout_experience_buff_list:SetDataList(aim_list)
end

-------------------------------------PWSceneHurtItemCellRender---------------------------------
ExperienceBuffItem = ExperienceBuffItem or BaseClass(BaseRender)
local WAVE_BUFF = {
	ADD_WAVE_TIME = 0,
	ADD_BUFF = 1,
	ADD_SKILL_HURT = 2,
	ADD_SKILL_POWER = 3,
}


function ExperienceBuffItem:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

	-- local str = string.format("%s\n", self.data.name)
	-- local desc = ""
	-- if self.data.type == WAVE_BUFF.ADD_WAVE_TIME then
	-- 	desc = string.format(self.data.desc, self.data.param1)
	-- elseif self.data.type == WAVE_BUFF.ADD_BUFF then
	-- 	desc = self.data.desc
	-- elseif self.data.type == WAVE_BUFF.ADD_SKILL_HURT then
	-- 	desc = string.format(self.data.desc, math.floor(self.data.param1 / 100))
	-- elseif self.data.type == WAVE_BUFF.ADD_SKILL_POWER then
	-- 	desc = string.format(self.data.desc, self.data.param1)
	-- end

	self.node_list.rank_text.text.text = self.data.seq + 1
	self.node_list.buff_name.text.text = ToColorStr(self.data.name, ITEM_COLOR[self.data.color]) 
	self.node_list.buff_desc.text.text = ToColorStr(self.data.desc, ITEM_COLOR[self.data.color])
end