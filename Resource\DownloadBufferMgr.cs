﻿using LuaInterface;
using System.Collections.Generic;
using UnityEngine;
using System;

public static class DownloadBufferMgr
{
    private static List<DownloadBuffer> activeBufferList = new List<DownloadBuffer>();
    private static Dictionary<int, Queue<DownloadBuffer>> bufferDic = new Dictionary<int, Queue<DownloadBuffer>>();
    private static List<int> bufferSizeList = new List<int>();
    private static int minWriteSize = 1 * 1024;
    private static float writeCD = 0f;

    //debug
    private static bool isDebug = false;
    private static List<DebugLog> debugLogs = new List<DebugLog>();
    private static float lastFlushTimeStamp = 0;
    private static int totalIOSpeed = 0;
    private static int totalWritedMemSpeed = 0;
    private static int totalWritedDiskSpeed = 0;
    private static int totalDownloadSpeed = 0;
    private static int totalBufferSize = 0;
    private static int totalBufferUsedSize = 0;
    private static int totalWritedSize = 0;
    private static int totalFinishedCount = 0;
    private static int totalFalidCount = 0;
    private static bool showDetails = false;

    // 创建一个buffer(由逻辑层创建)
    public static void CreateBuffer(int size)
    {
        var buffer = new DownloadBuffer(size);
        Queue<DownloadBuffer> list;
        if (!bufferDic.TryGetValue(size, out list))
        {
            list = new Queue<DownloadBuffer>();
            bufferDic.Add(size, list);
            bufferSizeList.Add(size);
            bufferSizeList.Sort();
        }

        buffer.IsDebug = isDebug;
        list.Enqueue(buffer);
        activeBufferList.Add(buffer);
    }

    public static void OnGameStop()
    {
        DestoryAllBuffer(true);
        if (isDebug)
        {
            totalWritedSize = 0;
            totalFinishedCount = 0;
            totalFalidCount = 0;
        }
    }

    // 删除所有buffer
    // buffer如果正在使用中，删除掉会导致下载任务失败
    public static void DestoryAllBuffer(bool isGameStop = false)
    {
        bufferDic.Clear();
        bufferSizeList.Clear();
        List<DownloadBuffer> deleteList = new List<DownloadBuffer>();
        for (int i = 0; i < activeBufferList.Count; ++i)
        {
            var buffer = activeBufferList[i];
            if (null != buffer)
            {
                deleteList.Add(buffer);
            }
        }
        activeBufferList.Clear();

        for (int i = 0; i < deleteList.Count; ++i)
        {
            try
            {
                deleteList[i].OnDestory(isGameStop);
            }
            catch(Exception)
            {

            }
        }
    }

    public static void DestoryBuffer(int size, int count = 0)
    {
        List<DownloadBuffer> deleteList = new List<DownloadBuffer>();
        Queue<DownloadBuffer> list;
        if (bufferDic.TryGetValue(size, out list))
        {
            int deletedCount = 0;
            if (count > 0)
            {
                // 优先删除空闲的buffer
                while (list.Count > 0)
                {
                    if (deletedCount >= count)
                    {
                        break;
                    }

                    var buffer = list.Dequeue();
                    deleteList.Add(buffer);
                    ++deletedCount;
                }

                for (int i = 0; i < activeBufferList.Count; ++i)
                {
                    if (deletedCount >= count)
                    {
                        break;
                    }

                    var buffer = activeBufferList[i];
                    if (buffer.BufferLength == size)
                    {
                        if (!deleteList.Contains(buffer))
                        {
                            deleteList.Add(buffer);
                            ++deletedCount;
                        }
                    }
                }
            }
            else 
            {
                // 把这个属于这个size的全部删除
                list.Clear();

                for (int i = 0; i < activeBufferList.Count; ++i)
                {
                    var buffer = activeBufferList[i];
                    if (buffer.BufferLength == size)
                    {
                        deleteList.Add(buffer);
                        ++deletedCount;
                    }
                }
            }

            for (int i = 0; i < deleteList.Count; ++i)
            {
                var buffer = deleteList[i];
                activeBufferList.Remove(buffer);
            }

            // 这里注意OnDestory会触发回调，所以放到最后
            for (int i = 0; i < deleteList.Count; ++i)
            {
                var buffer = deleteList[i];
                try
                {
                    buffer.OnDestory(false);
                }
                catch(Exception)
                {

                }
            }
        }
    }

    public static void Update()
    {
        float nowTime = Time.realtimeSinceStartup;
        for (int i = 0; i < activeBufferList.Count; ++i)
        {
            // buffer.Update()会调用Lua的回调，加个判断防止数组越界
            if (i >= activeBufferList.Count)
                break;

            var buffer = activeBufferList[i];
            buffer.Update(nowTime);
        }

        if (isDebug)
        {
            UpdateDebugInfo();
        }
    }

    // 满足最小的Size时才会写入
    public static int MinWriteSize
    {
        get { return minWriteSize; }
        set { minWriteSize = value; }
    }

    // 每一次写入的时间间隔
    public static float WriteCD
    {
        get { return writeCD; }
        set { writeCD = value; }
    }

    // 从池中获取一个buffer
    [NoToLua]
    public static DownloadBuffer GetBuffer(int size)
    {
        int validSize = -1;
        for (int i = 0; i < bufferSizeList.Count; ++i)
        {
            int bufferSize = bufferSizeList[i];
            int count = bufferDic[bufferSize].Count;
            if (count > 0)
            {
                validSize = bufferSize;
            }

            if (bufferSize >= size && count > 0)
            {
                break;
            }
        }

        if (validSize >= 0)
        {
            var buffer = bufferDic[validSize].Dequeue();
            return buffer;
        }
        else
        {
            // 如果buffer不够用，则自动创建一个大小为0的buffer，也就意味着没有缓冲的效果了
            Debug.Log("自动创建一个大小为0的buffer");
            CreateBuffer(0);
            var buffer = bufferDic[0].Dequeue();
            return buffer;
        }
    }

    // 把一个buffer回池
    [NoToLua]
    public static void ReleaseBuffer(DownloadBuffer buffer)
    {
        buffer.Clear();
        int size = buffer.BufferLength;
        Queue<DownloadBuffer> buffers;
        if (bufferDic.TryGetValue(size, out buffers))
        {
            buffers.Enqueue(buffer);
        }
    }

    /// <summary>
    /// /////////////////////////////////////////DEBUG相关////////////////////////////////////////////
    /// </summary>

    public static bool IsDebug
    {
        get { return isDebug; }
        set
        {
            isDebug = value;
            for (int i = 0; i < activeBufferList.Count; ++i)
            {
                var buffer = activeBufferList[i];
                buffer.IsDebug = isDebug;
            }
        }
    }

    [NoToLua]
    private static class MessageStyle
    {
        public static GUIStyle ContentStyle { get; private set; }
        public static GUIStyle LogContentStyle { get; private set; }
        public static GUIStyle ButtonTextStyle { get; private set; }

        static MessageStyle()
        {
            ContentStyle = new GUIStyle(GUI.skin.label)
            {
                fontSize = 20,
                alignment = TextAnchor.UpperLeft,
                padding = new RectOffset(0, 0, 0, 0),
                fontStyle = FontStyle.Bold,
            };

            LogContentStyle = new GUIStyle(GUI.skin.label)
            {
                fontSize = 20,
                alignment = TextAnchor.MiddleLeft,
                padding = new RectOffset(0, 0, 0, 0),
                fontStyle = FontStyle.Bold,
            };

            ButtonTextStyle = new GUIStyle(GUI.skin.button)
            {
                fontSize = 30,
                alignment = TextAnchor.MiddleCenter,
                padding = new RectOffset(0, 0, 0, 0),
                fontStyle = FontStyle.Bold,
            };
        }
    }

    [NoToLua]
    public static void OnGUI()
    {
        GUI.Box(new Rect(10, 0, 400, 180), "");
        GUI.TextArea(new Rect(10, 0, 400, 30), string.Format("IO: {0}次/s", totalIOSpeed), MessageStyle.ContentStyle);
        GUI.TextArea(new Rect(10, 30, 400, 30), string.Format("下载速度: {0}MB/s", ByteToMByte(totalDownloadSpeed).ToString("0.00")), MessageStyle.ContentStyle);
        GUI.TextArea(new Rect(10, 60, 400, 30), string.Format("内存写入速度: {0}MB/s", ByteToMByte(totalWritedMemSpeed).ToString("0.00")), MessageStyle.ContentStyle);
        GUI.TextArea(new Rect(10, 90, 400, 30), string.Format("硬盘写入速度: {0}MB/s", ByteToMByte(totalWritedDiskSpeed).ToString("0.00")), MessageStyle.ContentStyle);
        GUI.TextArea(new Rect(10, 120, 400, 30), string.Format("缓存使用率: {0}MB/{1}MB {2}%", ByteToMByte(totalBufferUsedSize).ToString("0.00"), ByteToMByte(totalBufferSize).ToString("0.0"), ((float)totalBufferUsedSize / totalBufferSize * 100).ToString("0.0")), MessageStyle.ContentStyle);
        GUI.TextArea(new Rect(10, 150, 500, 30), string.Format("完成次数:{0} 失败次数:{1} 写入:{2}MB", totalFinishedCount, totalFalidCount, ByteToMByte(totalWritedSize).ToString("0.0")), MessageStyle.ContentStyle);
        if (GUI.Button(new Rect(510, 0, 100, 40), new GUIContent("展开"), MessageStyle.ButtonTextStyle))
        {
            showDetails = !showDetails;
        }

        if (showDetails)
        {
            int logWidth = 400;
            int logHeight = 70;
            int space = 5;
            int logStartOffsetX = 10;
            int logStartOffsetY = 180;
            int contentSize = 20;
            int pageCount = 8;
            int logStartX = 0;
            int logStartY = 0;

            for (int i = 0; i < debugLogs.Count; ++i)
            {
                var log = debugLogs[i];
                logStartY = i % pageCount == 0 ? 0 : logStartY;
                logStartX = logWidth * (i / pageCount);

                GUI.contentColor = log.isFree ? Color.white : log.usedpercent < 50f ? Color.green : log.usedpercent >= 80f ? Color.red : Color.yellow;
                GUI.Box(new Rect(logStartOffsetX + logStartX, logStartOffsetY + logStartY, logWidth, logHeight - space), "");

                int offset = 0;
                GUI.Label(new Rect(logStartOffsetX + logStartX, logStartOffsetY + logStartY, logWidth, contentSize), string.Format("缓冲器{0} 容量:{1}MB 已使用:{2}%", i, log.totalBufferSize, log.usedpercent.ToString("0")), MessageStyle.ContentStyle);
                offset += 20;
                if (log.isFree)
                {
                    GUI.Label(new Rect(logStartOffsetX + logStartX, logStartOffsetY + logStartY + offset, logWidth, contentSize), "空闲", MessageStyle.ContentStyle);
                    offset += 20;
                }
                else
                {
                    GUI.Label(new Rect(logStartOffsetX + logStartX, logStartOffsetY + logStartY + offset, logWidth, contentSize), string.Format("文件大小:{0}MB/{1}MB", log.receivedSize, log.fileSize), MessageStyle.ContentStyle);
                    offset += 20;
                    GUI.Label(new Rect(logStartOffsetX + logStartX, logStartOffsetY + logStartY + offset, logWidth, contentSize), string.Format("下载速度:{0}MB/s 硬盘写入速度{1}MB/s", log.downloadSpeed, log.writedDiskSpeed), MessageStyle.ContentStyle);
                    offset += 20;
                }

                logStartY += logHeight;

                GUI.contentColor = Color.white;
            }
        }
    }

    private static void UpdateDebugInfo()
    {
        if (lastFlushTimeStamp + 1 > Time.realtimeSinceStartup)
        {
            for (int i = 0; i < debugLogs.Count; ++i)
            {
                var log = debugLogs[i];
                var buffer = log.buffer;
                log.receivedSize = ByteToMByte(buffer.ReceivedSize).ToString("0.00");
                if (buffer.BufferLength > 0)
                {
                    log.usedpercent = (float)buffer.UsedCache / buffer.BufferLength * 100f;
                }
                else
                {
                    log.usedpercent = 100f;
                }

                if (buffer.FileSize > 0)
                {
                    log.fileSize = ByteToMByte(buffer.FileSize).ToString("0.00");
                    log.receivedSize = ByteToMByte(buffer.ReceivedSize).ToString("0.00");
                    log.isFree = false;
                }
                else
                {
                    log.isFree = true;
                }
            }

            return;
        }

        float deltaTime = Time.realtimeSinceStartup - lastFlushTimeStamp;
        lastFlushTimeStamp = Time.realtimeSinceStartup;

        totalIOSpeed = 0;
        totalWritedMemSpeed = 0;
        totalWritedDiskSpeed = 0;
        totalDownloadSpeed = 0;
        totalBufferSize = 0;
        totalBufferUsedSize = 0;
        totalFinishedCount = 0;
        totalFalidCount = 0;
        debugLogs.Clear();

        for (int i = 0; i < activeBufferList.Count; ++i)
        {
            var buffer = activeBufferList[i];
            int ioCount, writedMemSize, writedDiskSize, downloadSize;
            buffer.GetDebugLogs(out ioCount, out writedMemSize, out writedDiskSize, out downloadSize);
            totalIOSpeed += ioCount;
            totalWritedMemSpeed += writedMemSize;
            totalWritedDiskSpeed += writedDiskSize;
            totalDownloadSpeed += downloadSize;
            totalBufferSize += buffer.BufferLength;
            totalBufferUsedSize += buffer.UsedCache;
            totalFinishedCount += buffer.TotalFinishedCount;
            totalFalidCount += buffer.TotalFaildCount;

            DebugLog log = new DebugLog();
            log.buffer = buffer;
            log.ioCount = ioCount;
            log.writedMemSpeed = ByteToMByte(writedMemSize / deltaTime).ToString("0.0"); ;
            log.writedDiskSpeed = ByteToMByte(writedDiskSize / deltaTime).ToString("0.0"); 
            log.downloadSpeed = ByteToMByte(downloadSize / deltaTime).ToString("0.0"); ;
            log.totalBufferSize = ByteToMByte(buffer.BufferLength).ToString("0.0");
            log.usedBufferSize = ByteToMByte(buffer.UsedCache).ToString("0.0");
            if (buffer.BufferLength > 0)
            {
                log.usedpercent = (float)buffer.UsedCache / buffer.BufferLength * 100f;
            }
            else
            {
                log.usedpercent = 100f;
            }

            if (buffer.FileSize > 0)
            {
                log.fileSize = ByteToMByte(buffer.FileSize).ToString("0.00");
                log.receivedSize = ByteToMByte(buffer.ReceivedSize).ToString("0.00");
                log.isFree = false;
            }
            else
            {
                log.isFree = true;
            }

            debugLogs.Add(log);
            buffer.ClearDebugLogs();
        }

        totalIOSpeed = (int)(totalIOSpeed / deltaTime);
        totalWritedSize += totalWritedDiskSpeed;

        debugLogs.Sort(CompareDebugLog);
    }

    private static float ByteToMByte(int num)
    {
        return (float)num / 1024 / 1024;
    }

    private static float ByteToMByte(float num)
    {
        return num / 1024 / 1024;
    }

    private struct DebugLog
    {
        public DownloadBuffer buffer;
        public string totalBufferSize;
        public string usedBufferSize;
        public float usedpercent;
        public int ioCount;
        public string writedDiskSpeed;
        public string writedMemSpeed;
        public string downloadSpeed;
        public string fileSize;
        public string receivedSize;
        public bool isFree;
    }

    private static int CompareDebugLog(DebugLog a, DebugLog b)
    {
        return b.buffer.BufferLength.CompareTo(a.buffer.BufferLength);
    }
}
