FieldChangellengeRecordPanel = FieldChangellengeRecordPanel or BaseClass(SafeBaseView)

function FieldChangellengeRecordPanel:__init()
	self:SetMaskBg(true)

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(810, 572)})
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_changlleng_record")
end

function FieldChangellengeRecordPanel:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Field1v1.Arena_Record
	self.rank_list = AsyncListView.New(FieldChangellengeRecordRender, self.node_list.ph_ranking_list)
	self:Flush()
end

function FieldChangellengeRecordPanel:ReleaseCallBack()
	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end
end

function FieldChangellengeRecordPanel:OnFlush()
	local data_list = Field1v1WGData.Instance:GetReportinfo()
	self.node_list.layout_blank_tip:SetActive(#data_list <= 0)
	self.rank_list:SetDataList(data_list)
end


FieldChangellengeRecordRender = FieldChangellengeRecordRender or BaseClass(BaseRender)

function FieldChangellengeRecordRender:__init()
	
end

function FieldChangellengeRecordRender:LoadCallBack()
	
end

function FieldChangellengeRecordRender:OnFlush()
	local  server_time = TimeWGCtrl.Instance:GetServerTime()
	local time_table_server = os.date("*t", server_time)
	local time_table_local = os.date("*t", self.data.challenge_time)
	if time_table_server.day == time_table_local.day then
		self.node_list.time.text.text = string.format("%02d:%02d", time_table_local.hour, time_table_local.min)
	else
		self.node_list.time.text.text = string.format(Language.Common.XXMXXD, time_table_local.month, time_table_local.day)
	end

	if self.data.is_sponsor == 1 then --我挑战
		if self.data.is_win == 1 then
			if self.data.new_rankpos == self.data.old_rankpos then
				self.node_list.desc.text.text = string.format(Language.Field1v1.FieldRecordResult_1, self.data.target_name)
			else
				self.node_list.desc.text.text = string.format(Language.Field1v1.FieldRecordResult_2, self.data.target_name, self.data.new_rankpos + 1)
			end
		else
			self.node_list.desc.text.text = string.format(Language.Field1v1.FieldRecordResult_3, self.data.target_name)
		end
	else
		if self.data.is_win == 1 then
			self.node_list.desc.text.text = string.format(Language.Field1v1.FieldRecordResult_4, self.data.target_name)
		else
			if self.data.new_rankpos == self.data.old_rankpos then
				self.node_list.desc.text.text = string.format(Language.Field1v1.FieldRecordResult_5, self.data.target_name)
			else
				self.node_list.desc.text.text = string.format(Language.Field1v1.FieldRecordResult_6, self.data.target_name, self.data.new_rankpos + 1)
			end
		end
	end

end

function FieldChangellengeRecordRender:__delete()
	
end