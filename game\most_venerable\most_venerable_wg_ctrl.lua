require("game/most_venerable/most_venerable_wg_data")
require("game/most_venerable/most_venerable_view")

MostVenerableWGCtrl = MostVenerableWGCtrl or BaseClass(BaseWGCtrl)

function MostVenerableWGCtrl:__init()
	if MostVenerableWGCtrl.Instance then
		ErrorLog("[MostVenerableWGCtrl] attempt to create singleton twice!")
		return
	end

	MostVenerableWGCtrl.Instance = self
	self.data = MostVenerableWGData.New()
    self.view = MostVenerableView.New(GuideModuleName.MostVenerableView)
  
    self:RegisterAllProtocols()
    self:RegisterAllEvents()

    self.act_status = nil
end

function MostVenerableWGCtrl:__delete()
    self:UnRegisterAllEvents()

	self.view:DeleteMe()
	self.view = nil

    self.data:DeleteMe()
	self.data = nil

    self.act_status = nil

    MostVenerableWGCtrl.Instance = nil
end

function MostVenerableWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCZuiQiangXianZunInfo,"OnSCZuiQiangXianZunInfo")
end

function MostVenerableWGCtrl:RegisterAllEvents()
    -- 角色属性改变
    self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, { "level" })

    -- 天数改变
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.CheckActicityOpen, self))
end

function MostVenerableWGCtrl:UnRegisterAllEvents()
    if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end
end

function MostVenerableWGCtrl:RequestRankInfo()
    Field1v1WGCtrl.Instance:SendChallengeField(CHALLENGE_RANK_LIST_TYPE.CHALLENGE_RANK_LIST_TYPE_ZUI_QIANG_XIAN_ZUN)
end

function MostVenerableWGCtrl:OnSCZuiQiangXianZunInfo(protocol)
	--print_error("===========数据============", protocol)
	self.data:SetRankListInfo(protocol)
    self:CheckActicityOpen()
    ViewManager.Instance:FlushView(GuideModuleName.MostVenerableView, TabIndex.most_venerable)
end

function MostVenerableWGCtrl:OnRoleAttrChange(attr_name, value, old_value)
	if attr_name == "level" then
        self:CheckActicityOpen()
	end
end

-- 检查是否开启
function MostVenerableWGCtrl:CheckActicityOpen()
    local role_level = RoleWGData.Instance:GetAttr("level")
    local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.MOST_VENERABLE)

    local status = ACTIVITY_STATUS.CLOSE
    if not IsEmptyTable(act_cfg) then
        if role_level < act_cfg.level or role_level > act_cfg.limit_level then
            status = ACTIVITY_STATUS.CLOSE
        elseif server_day < act_cfg.act_serveropen or server_day > act_cfg.act_serverover_day then
            status = ACTIVITY_STATUS.CLOSE
        else
            status = ACTIVITY_STATUS.OPEN
        end
    end

    if self.act_status ~= status then
        ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.MOST_VENERABLE, status)
        self.act_status = status
    end
    return status
end