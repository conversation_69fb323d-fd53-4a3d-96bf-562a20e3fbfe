LimitTimeGiftView = LimitTimeGiftView or BaseClass(SafeBaseView)

local Show_Cell_Max_Count = 4

function LimitTimeGiftView:__init(view_name)
	self.view_style = ViewStyle.Half
	self:AddViewResource(0, "uis/view/limit_time_gift_prefab", "layout_limit_time_gift_view")
	self:SetMaskBg(true, true)
end

function LimitTimeGiftView:LoadCallBack()
	self.gift_toggle_list = {}
	self:InitPanel()
end

function LimitTimeGiftView:ReleaseCallBack()
	if self.gift_toggle_list then
		for k,v in pairs(self.gift_toggle_list) do
			v:DeleteMe()
		end
		self.gift_toggle_list = nil
	end

	if self.gift_item_list then
		self.gift_item_list:DeleteMe()
		self.gift_item_list = nil
	end

	if self.close_alert then
		self.close_alert:DeleteMe()
		self.close_alert = nil
	end
	
	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	CountDownManager.Instance:RemoveCountDown("limit_time_gift_count_down")
end

function LimitTimeGiftView:InitPanel()
	self.select_gift_type = nil
	self.gift_item_list = AsyncListView.New(LimitTimeGiftItem, self.node_list.gift_item_list)

	self.close_alert = Alert.New()

	self.money_bar = MoneyBar.New()
	local bundle, asset = ResPath.GetWidgets("MoneyBar")
	local show_params = { show_gold = true }
    self.money_bar:SetMoneyShowInfo(0, 0, show_params)
	self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)

	XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind(self.OnClickCloseBtn, self))

	self.node_list.text_desc.text.text = Language.LimitTimeGift.PassGiftTip6
	--list的箭头显示.
	--self.node_list.gift_item_list.scroller.scrollerEndScrolled = BindTool.Bind(self.TouZiScrollerEndScrolled, self)
	-- self.node_list.gift_item_list.scroll_rect.onValueChanged:AddListener(BindTool.Bind(self.TouZiScrollerEndScrolled, self)) --列表滑动监听
end

--水平list的箭头显示.
-- function LimitTimeGiftView:TouZiScrollerEndScrolled()
-- 	local val = self.node_list.gift_item_list.scroll_rect.horizontalNormalizedPosition
-- 	local item_nums = self.gift_item_list:GetListViewNumbers()
-- 	local val_age = 0
-- 	if item_nums ~= 0 then val_age = 1 / item_nums end
-- 	local show_arrow = item_nums > Show_Cell_Max_Count
-- 	self.node_list.left_jiantou:SetActive(show_arrow and val > val_age and val_age ~= 0)
-- 	self.node_list.right_jiantou:SetActive(show_arrow and val < 1-val_age and val_age ~= 0)
-- end

function LimitTimeGiftView:Open()
	LimitTimeGiftWGData.Instance:CleanNotBuyGiftInfo()
	if LimitTimeGiftWGData.Instance:HasCanBuyGift() then
		SafeBaseView.Open(self)
	else
		LimitTimeGiftWGCtrl.Instance:CheckActIsOpen()
	end
end

function LimitTimeGiftView:OpenCallBack()
	
end

function LimitTimeGiftView:CloseCallBack()
	RemindManager.Instance:Fire(RemindName.LimitTimeGift)
end

function LimitTimeGiftView:ShowIndexCallBack()
    self:DoShowTweenStart()
	self:SetRemindTag()
    self:FlushGiftItemList()
end

function LimitTimeGiftView:DoShowTweenStart()
    UITween.CleanAllTween(GuideModuleName.LimitTimeGift)
    UITween.FakeHideShow(self.node_list.gift_item_list)
    local tween_info = UITween_CONSTS.LimitTimeBuy
    --UITween.FakeHideShow(self.node_list.title_bg)

    self.node_list.left_title_img:SetActive(false)
    local title_info = UITween_CONSTS.LimitTimeBuy.TitleScale
    self.node_list.left_title_img:SetActive(true)
    UITween.DoScaleMoveShow(GuideModuleName.LimitTimeGift,self.node_list.left_title_img, title_info)
    --local bg_info = UITween_CONSTS.LimitTimeBuy.BgMove
    --UITween.MoveToShowPanel(GuideModuleName.LimitTimeGift,self.node_list.list_bg, bg_info.BgStartPos, bg_info.BgEndPos, bg_info.MoveTime, bg_info.MoveTweenType)
    self.do_list_tween = true
    --self.node_list.center_panel:SetActive(false)
	--UITween.AlphaShow(GuideModuleName.LimitTimeGift, self.node_list.title_bg, tween_info.FromAlpha, 1, tween_info.AlphaTime, tween_info.AlphaTweenType)
    self:DoListTween()
    -- ReDelayCall(self, function()
    --     self.node_list.center_panel:SetActive(true)
    --     UITween.AlphaShow(GuideModuleName.LimitTimeGift, self.node_list.title_bg, tween_info.FromAlpha, 1, tween_info.AlphaTime, tween_info.AlphaTweenType)
    --     self:DoListTween()
    -- end,bg_info.MoveTime, "LimitTimeGiftView")
    
end

function LimitTimeGiftView:DoListTween()
    if not self.do_list_tween then
        return
    end

    local tween_info = UITween_CONSTS.LimitTimeBuy
    ReDelayCall(self, function()
        local list = self.gift_item_list:GetAllItems()
        local sort_list = {}

        for i, v in pairs(list) do
            local data = {}
            data.index = v:GetIndex()
            data.item = v
            sort_list[#sort_list + 1] = data
        end

        table.sort(sort_list, SortTools.KeyLowerSorter("index"))

        local count = 0
        for k,v in ipairs(sort_list) do
            if 0 ~= v.index then
                count = count + 1
            end
            v.item:PalyItemAnimator(count)
        end

        UITween.FakeToShow(self.node_list.gift_item_list)
    end, tween_info.DelayAlphaShowTimeQuick, "limit_time_list_tween")

    self.do_list_tween = false
end

function LimitTimeGiftView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "gift_info" then
			self:FlushGiftItemList()
		end
	end
end

function LimitTimeGiftView:SetRemindTag()
	local gift_info_list = LimitTimeGiftWGData.Instance:GetGiftInfoList()
	local temp_gift_id = nil
	if gift_info_list then
		for k,v in pairs(gift_info_list) do
			temp_gift_id = LimitTimeGiftWGData.Instance:GetGiftTrueId(v.gift_id, v.index)
			LimitTimeGiftWGData.Instance:SetGiftRemindByID(temp_gift_id)
		end
	end
end

function LimitTimeGiftView:FlushGiftItemList()
	local gift_info_list = LimitTimeGiftWGData.Instance:GetGiftInfoList()
	if not gift_info_list then
		return
	end
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local info_list = SortTableKey(gift_info_list)

	table.sort( info_list, function (a, b)
		local order_a = 100000
		local order_b = 100000

		if a.can_buy_num and b.can_buy_num then
			if a.can_buy_num > b.can_buy_num then
				order_a = order_a + 10000
			elseif a.can_buy_num < b.can_buy_num then
				order_b = order_b + 10000
			end
		end

		if a.is_eject and b.is_eject then
			if a.is_eject > b.is_eject then
				order_a = order_a + 1000
			elseif a.is_eject < b.is_eject then
				order_b = order_b + 1000
			end
		end

		if a.gift_timestamp and b.gift_timestamp then
			if a.gift_timestamp < b.gift_timestamp then
				order_a = order_a + 100
			elseif a.gift_timestamp > b.gift_timestamp then
				order_b = order_b + 100
			end
		end

		return order_a > order_b
		-- if a.can_buy_num ~= b.can_buy_num then
		-- 	return a.can_buy_num > b.can_buy_num
		-- else
		-- 	return a.gift_timestamp < b.gift_timestamp
		-- end
	end )
	local itemcount = 0
    for k,v in ipairs(info_list) do
        if nil ~= v then
            itemcount = itemcount + 1
        end
    end
    self.gift_item_list:SetDataList(info_list)
end

function LimitTimeGiftView:OnClickCloseBtn()
	if self.close_alert and LimitTimeGiftWGData.Instance:HasCanBuyGift() then
		self.close_alert:SetShowCheckBox(true, "LimitTimeGiftClose")
		self.close_alert:SetLableString(Language.LimitTimeGift.CloseStr)
		self.close_alert:SetOkFunc(BindTool.Bind(self.Close, self))
		self.close_alert:SetOkString(Language.LimitTimeGift.CloseBtnSure)
		self.close_alert:SetCancelString(Language.LimitTimeGift.CloseBtnCancel)
		self.close_alert:Open()
		return
	end
	self:Close()
end

-------------------------------------------------------------------------------------

LimitTimeGiftItem = LimitTimeGiftItem or BaseClass(BaseRender)

function LimitTimeGiftItem:__init()
	self.count_down_key = ""
	self.cfg_data = nil
	self.color_text_change = 300 -- 最后5分钟颜色改变
	self.change_color_flag = true

	self.item_list = {}

	for i = 1, 4 do
		self.item_list[i] = ItemCell.New(self.node_list["item_" .. i])
	end
end

function LimitTimeGiftItem:__delete()
	if self.item_list then
		for k, v in pairs(self.item_list) do
			v:DeleteMe()
		end
		self.item_list = nil
	end

	self.cfg_data = nil
	self.color_text_change = nil
	CountDownManager.Instance:RemoveCountDown(self.count_down_key)
end

function LimitTimeGiftItem:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_buy, BindTool.Bind(self.OnClickBuyBtn, self))
end

function LimitTimeGiftItem:OnFlush()
	--[[if self.index then
		local pos_need_up = self.index % 2 == 0
		local pos_y = pos_need_up and 21 or -7
		RectTransform.SetAnchoredPositionXY(self.node_list.root.rect, 0, pos_y)
	end]]

	local gift_info = self:GetData()
	local cfg_data = LimitTimeGiftWGData.Instance:GetGiftCfgByGiftID(gift_info.gift_id)
	if not cfg_data then
		return
	end

	self.cfg_data = cfg_data
	self.node_list.gift_name.text.text = cfg_data.gift_name
	self.node_list.price_lbl.text.text = cfg_data.price
	local cn_str = Language.LimitTimeGift.Num2DiscountStr[cfg_data.gift_discount] or ""
	self.node_list.zhekou_lbl.text.text = cn_str .. Language.MustBuy.Discount
	-- self.node_list.gift_img.image:LoadSprite(ResPath.GetLimitTimeGiftIcon(cfg_data.gift_pic))
	-- self.node_list.gift_img.image:SetNativeSize()
	local item_num = cfg_data.reward_item
	self.node_list.item_group1:SetActive(true)
	self.node_list.item_group2:SetActive(#item_num + 1 > 2)

	if #item_num + 1 > 2 then 
		self.node_list.reward_list.rect.localScale = Vector3(0.8,0.8,0.8)
	else
		self.node_list.reward_list.rect.localScale = Vector3(1,1,1)
	end

	for i = 1, 4 do
		self.node_list["item_" .. i]:SetActive(#item_num + 1 >= i)
		if #item_num + 1 >= i then
			self.item_list[i]:SetData(item_num[i - 1])
		end
	end

	self:FlushBuyStatus()
end

function LimitTimeGiftItem:FlushBuyStatus()
	local gift_info = self:GetData()

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	server_time = math.ceil(server_time)
	local count_down_time = gift_info.gift_timestamp - server_time

	self.node_list.btn_buy:SetActive(gift_info.can_buy_num > 0 and count_down_time > 0)
	-- self.node_list.all_sell_img:SetActive(gift_info.can_buy_num <= 0)
	-- self.node_list.pass_time_img:SetActive(gift_info.can_buy_num > 0 and count_down_time <= 0)
	self.node_list.sell_out:SetActive(gift_info.can_buy_num <= 0 or (gift_info.can_buy_num > 0 and count_down_time <= 0))
	if gift_info.can_buy_num <= 0 then
		CountDownManager.Instance:RemoveCountDown(self.count_down_key)
		self.node_list.text_time.text.text = ""
		self.node_list.text_sell.text.text = Language.Common.SellOut2
	elseif gift_info.can_buy_num > 0 and count_down_time <= 0 then
		self.node_list.text_sell.text.text = Language.Common.NoTime
		self:FlushGiftItemCountDown(count_down_time)
	else
		self:FlushGiftItemCountDown(count_down_time)
	end

end

function LimitTimeGiftItem:FlushGiftItemCountDown(count_down_time)
	CountDownManager.Instance:RemoveCountDown(self.count_down_key)
	self.count_down_key = "limit_time_gift_item_count_down" .. self:GetIndex()
	if count_down_time > 0 then
		self.node_list.text_time.text.text = TimeUtil.FormatSecond2HMS(count_down_time)
		if count_down_time > self.color_text_change then
			local color = "#1D7A39"
			self.node_list.text_time.text.color = Str2C3b(color)
			self.change_color_flag = true
		else
			self.node_list.text_time.text.color = Str2C3b(COLOR3B.C1)
		end
		CountDownManager.Instance:AddCountDown(
			self.count_down_key,
			BindTool.Bind(self.UpdateCountDown, self),
			BindTool.Bind(self.FlushBuyStatus, self),
			nil,
			count_down_time,
			1
		)
	else
		self.node_list.text_time.text.text = Language.LimitTimeGift.PassTimeStr
		self.node_list.text_time.text.color = Str2C3b(COLOR3B.C1)
	end
end

function LimitTimeGiftItem:UpdateCountDown(elapse_time, total_time)
	self.node_list.text_time.text.text = TimeUtil.FormatSecond2HMS(total_time - elapse_time)
	if self.change_color_flag and total_time - elapse_time < self.color_text_change then
		self.node_list.text_time.text.color = Str2C3b(COLOR3B.C1)
		self.change_color_flag = false
	end
end


function LimitTimeGiftItem:OnClickBuyBtn()
	local gift_info = self:GetData()
	local cfg_data = self.cfg_data
	if not cfg_data or not gift_info then
		return
	end
	
	local pass_time = gift_info.gift_timestamp
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if server_time > pass_time then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.LimitTimeGift.PassGiftTip3)
		return
	end

	if gift_info.can_buy_num > 0 then
		TipWGCtrl.Instance:OpenAlertTips(string.format(Language.Common.CommonAlertFormat3, cfg_data.price, cfg_data.gift_name), function ()
				local is_new_type = gift_info.index and gift_info.index >= 0
				if is_new_type then
					LimitTimeGiftWGCtrl.Instance:SendBuyGift(1, gift_info.index)
				else
					LimitTimeGiftWGCtrl.Instance:SendBuyGift(0, cfg_data.gift_id)
				end
			end)
	end
end

function LimitTimeGiftItem:PalyItemAnimator(item_index)
	local wait_index = item_index - 1
	wait_index = wait_index < 0 and 0 or wait_index
	local tween_info = UITween_CONSTS.LimitTimeBuy.ListCellTween
    -- self.node_list["zhekou_bg"]:SetActive(false)
	UITween.FakeHideShow(self.node_list["root"])
    -- local call_back = function()
    --     self.node_list["zhekou_bg"]:SetActive(true)
    --     UITween.DoScaleMoveShow(GuideModuleName.LimitTimeGift,self.node_list["zhekou_bg"], tween_info)
    -- end
	ReDelayCall(self, function()
		if self.node_list and self.node_list["root"] then
			UITween.RotateAlphaShow(GuideModuleName.LimitTimeGift,self.node_list["root"], tween_info)
		end
	end, tween_info.NextDoDelay * wait_index, "LimitTimeGiftItem_" .. wait_index)
end