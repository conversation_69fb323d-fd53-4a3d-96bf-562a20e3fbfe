AchievementView = AchievementView or BaseClass(SafeBaseView)

local TOGGLE_MAX = 8
local level_achievement_id = 18 --等級成就最大id
function AchievementView:InitAchievementPanel()
	self.big_toggle_list = nil
	self.small_toggle_list = nil
	self.cj_cell_list = {}
	self.node_list.get_once_btn.button:AddClickListener(BindTool.Bind1(self.OnClickOneReceive, self)) --一键领取
	self:CreateAccordion()
	self:CreateAchievementList()
	self.btn_within = 0
	self.btn_outer = 1
	self.is_can_flush = true --是否可以刷新列表，因为在场景内战斗时会影响协议问题所以
	self.is_zidong = false --手否自动点击外层按钮


	-- 多脚本共用变量
	self.choose_type_sort = nil
	self.choose_client_sort = nil
	self.page = 1 --页码（只有两页，两张算作一页）
	self.is_need_flush = false
	self.main_view_type = 1
	self.shenhua_level = nil
	self.can_do_achievement_anim = true
end

--切换其他页签的时候
function AchievementView:DelAchieventBtnData()
	self.btn_within = 0
	self.btn_outer = 1
	self.is_can_flush = true
	self.is_zidong = false
end

function AchievementView:XuanZeBtn()
	if AchievementWGData.Instance:IsShowAchievementParentRedPoint() == 1 then
		self:SkipView()
	else
		self:XuanZeOneBtn()
	end
end

--默认选中第一个下拉列表
function AchievementView:XuanZeOneBtn()
	if not self.cj_cell_list[TOGGLE_MAX] then return end
	self.btn_within = 1
	self.btn_outer = 1
	local accordion_tab = AchievementWGData.Instance:GetAccordionTable()
	self.big_toggle_list[self.btn_within].accordion_element.isOn = true
	self:OnClickExpandHandler(self.btn_within, accordion_tab[self.btn_within].type_sort, false, true)
end

function AchievementView:DeleteAchievementPanel()
	if nil ~= self.recen_achieve_list then
		self.recen_achieve_list:DeleteMe()
		self.recen_achieve_list = nil
	end
	if nil ~= self.achievement_list then
		self.achievement_list:DeleteMe()
		self.achievement_list = nil
	end

	if nil ~= self.cj_cell_list then
		for k, v in pairs(self.cj_cell_list) do
			for k1, v1 in pairs(v) do
				v1:DeleteMe()
				v1 = nil
			end
			v = nil
		end
		self.cj_cell_list = nil
	end
	self.btn_within = nil
	self.btn_outer = nil
	self.is_can_flush = nil
	self.is_zidong = nil
	AchievementWGData.Instance.achievement_is_skip = true

	if self.big_toggle_list then
		for key, value in pairs(self.big_toggle_list) do
			value.list_item_cell:DeleteMe()
			self.big_toggle_list[key] = nil
		end
		self.big_toggle_list = nil
	end

	if self.small_toggle_list then
		for key, value in pairs(self.small_toggle_list) do
			for k1, v1 in pairs(value) do
				v1 = nil
			end
			self.small_toggle_list[key] = nil
		end
		self.small_toggle_list = nil
	end
end

function AchievementView:InitMainPanelText()
	self:CreateRecenAchieveList()
	local name_tab = AchievementWGData.Instance:GetAchievementName()
	if nil == name_tab then return end
	for i = 1, TOGGLE_MAX do
		if self.node_list["lbl_achievement_name_" .. i] then
			if nil == name_tab[i] or nil == name_tab[i].name then
				self.node_list["lbl_achievement_name_" .. i].text.text = "Error"
			else
				self.node_list["lbl_achievement_name_" .. i].text.text = name_tab[i].name --成就名字赋值
				-- print_error("===================================",name_tab)
			end
		end
	end
end

function AchievementView:FlushShieldCJ()
	local accordion_tab = AchievementWGData.Instance:GetAccordionTable()
	for i = 1, TOGGLE_MAX do
		-- 如果里面的子成就都没开启  则隐藏大标签
		local show_flag
		local show_list = {}
		local accor_data = accordion_tab[i].child

		for ii, vv in ipairs(accor_data) do
			show_list = AchievementWGData.Instance:GetAchievementTable(false, i, ii)
			if #show_list > 0 then
				show_flag = true
				break
			end
		end
		if not show_flag then
			self.node_list["SelectBtn" .. i]:SetActive(false)
			self.node_list["List" .. i]:SetActive(false)
		end
	end
end

function AchievementView:FlushAchievementPanel(param_t, index)
	self:SetAchievementData()

	if AchievementWGData.Instance.achievement_is_skip then
		self:IsBtnRedShow()
	end

	if AchievementWGData.Instance:GetIsFlushAccordion() then
		self:SetAccordionData(false)
		AchievementWGData.Instance:SetIsFlushAccordion()
	end

	if AchievementWGData.Instance:GetIsTipViewToOpenViewType() then
		local type_sort, client_sort = AchievementWGData.Instance:GetTipViewToOpenViewType()
		AchievementWGData.Instance:SetTipViewToOpenViewType(0, 0, false)
	end

	self:SetProgressPos(2)
	self:FlushAchievementProgress()

	local red_is_show = false
	local accordion_tab = AchievementWGData.Instance:GetAccordionTable()
	for k_1, v_1 in pairs(accordion_tab) do
		for k_2, v_2 in pairs(v_1.child) do
			if v_2.remind_tip > 0 then
				red_is_show = true
			end
		end
	end
	--XUI.SetButtonEnabled(self.node_list["get_once_btn"], red_is_show)

	self:DoAchievementCellAnim()
end

--idx: 位置索引.
function AchievementView:SetProgressPos(idx)
	self.node_list.totalnum_slider.rect.sizeDelta = self.node_list["slider_pos" .. idx].rect.sizeDelta
	self.node_list.totalnum_slider.rect.position = self.node_list["slider_pos" .. idx].rect.position
	self.node_list.total.rect.position = self.node_list["total_pos" .. idx].rect.position
	self.node_list.totalnum_slider_bg:CustomSetActive(idx == 1)
end

function AchievementView:FlushAchievementTotal()
	self:SetTatolAchieveData()
	self:FlushMainPanelText()
	self:SetProgressPos(1)
	self:FlushAchievementProgress()
end

function AchievementView:GetAchievementList()
	--获取 大成就列表
	local type_tab = AchievementWGData.Instance:GetAchievementTable(true)
	local yes_list = {} --已完成的
	local no_list = {} --未完成的
	for k, v in pairs(type_tab) do
		if (v.has_fatch == 1) then
			table.insert(yes_list, v)
		else
			table.insert(no_list, v)
		end
	end

	return yes_list, no_list
end

function AchievementView:FlushAchievementProgress()
	local yes_list, no_list = self:GetAchievementList()

	local name_tab = AchievementWGData.Instance:GetAchievementName()
	local total_achievement = 0     --所有成就的 总成就点
	local total_active_achievement = 0 --当前成就的 总成就点
	for i = 1, #name_tab do
		total_achievement = total_achievement + name_tab[i].achievement
		total_active_achievement = total_active_achievement + name_tab[i].active_achievement
	end

	--当前目标成就的 成就点显示                    所有成就的总点数     当前目标成就的点数
	local cur_achievement = IsEmptyTable(no_list) and total_achievement or no_list[1].param1
	--玩家当前的成就进度
	self.node_list.total_num.text.text = total_active_achievement .. '/' .. cur_achievement
	self.node_list.totalnum_slider.slider.value = total_active_achievement / cur_achievement
end

function AchievementView:FlushMainPanelText()
	local name_tab = AchievementWGData.Instance:GetAchievementName()
	for i = 1, TOGGLE_MAX do
		self.node_list["text_progress" .. i].text.text =
			string.format("%s/%s", name_tab[i].active_achievement, name_tab[i].achievement)
		self.node_list["ProgressBG" .. i].slider.value = name_tab[i].active_achievement / name_tab[i].achievement
	end

	--获取 大成就列表
	local yes_list, no_list = self:GetAchievementList()
	local yes_str, index


	--玩家当前的成就类型名字
	if IsEmptyTable(yes_list) then
		--如果没有完成的成就  就显示未完成的第一个成就名字
		yes_str = no_list[1]
		index = 1
	else
		yes_str, index = self:GetListMaxStr(yes_list)
	end

	--获取 最大的成就
	if index > 12 then
		index = 12
	end

	--显示已完成的成就中 点数最大的成就名字index
	-- self.node_list.cj_ty_name.text.text = yes_str.sub_type_str
	self.node_list.cj_ty_name.image:LoadSprite(ResPath.GetAchievementImg(string.format("a3_sub_type_icon_0%d", index)))
end

--获取已完成的成就表中 成就点最大的那个
function AchievementView:GetListMaxStr(tablelist)
	local returnMaxNumber = nil
	local returnMaxIndex = nil
	if (type(tablelist) ~= "table") then
		print_error("类型错误!")
		return nil
	end

	for k, v in pairs(tablelist) do
		if (returnMaxNumber == nil) then
			returnMaxNumber = v
			returnMaxIndex = k
		end

		if (v.param1 > returnMaxNumber.param1) then
			returnMaxNumber = v
			returnMaxIndex = k
		end
	end
	return returnMaxNumber, returnMaxIndex
end

-- 创建扩展列表
function AchievementView:CreateAccordion()
	if self.big_toggle_list then return end
	if nil == self.cj_cell_list then return end
	--成就目录
	local accordion_tab = AchievementWGData.Instance:GetAccordionTable()
	-- print_error("获取成就目录",accordion_tab)

	self.big_toggle_list = {}
	self.small_toggle_list = {}
	for i = 1, TOGGLE_MAX do
		local content_node = self.node_list["achievement_content"]
		self.small_toggle_list[i] = content_node:FindObj("List" .. i)
		self.small_toggle_list[i].list_item_cell = {}

		self.big_toggle_list[i] = content_node:FindObj("SelectBtn" .. i)
		self.big_toggle_list[i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickExpandHandler, self, i,
			accordion_tab[i].type_sort, true))
		local big_btn_cell = AchievementBigTypeToggleRender.New(content_node:FindObj("SelectBtn" .. i))
		big_btn_cell:SetIndex(i)
		self.big_toggle_list[i].list_item_cell = big_btn_cell

		if nil ~= accordion_tab[i] and nil ~= accordion_tab[i].child then
			self.big_toggle_list[i].list_item_cell:SetData(accordion_tab[i])
			self:LoadCJCell(i, accordion_tab[i].child)
		end
	end
end

function AchievementView:GetNodeList(parent, str)
	local obj = parent.transform:Find(str).gameObject
	local item = U3DObject(obj, obj.transform, self)
	return item
end

function AchievementView:LoadCJCell(index, accor_data)
	if nil == accor_data then
		return
	end

	local res_async_loader = AllocResAsyncLoader(self, "cj_accordion_item" .. index)
	res_async_loader:Load("uis/view/achievement_ui_prefab", "cj_accordion_item", nil,
		function(new_obj)
			local item_vo = {}
			for i = 1, #accor_data do
				local obj = ResMgr:Instantiate(new_obj)
				local obj_transform = obj.transform
				obj_transform:SetParent(self.small_toggle_list[index].transform, false)
				obj:GetComponent("Toggle").group = self.small_toggle_list[index].toggle_group
				self.small_toggle_list[index].list_item_cell[i] = obj
				local item_render = AchievementListItemRender.New(obj)
				item_render:SetData(accor_data[i]) --里层按钮赋值信息
				item_render.parent_view = self
				item_vo[i] = item_render
			end
			self.cj_cell_list[index] = item_vo
			if index == TOGGLE_MAX then
				self:IsBtnRedShow()
			end
		end)
end

--刷新手风琴数据
function AchievementView:SetAccordionData(is_click)
	local accordion_tab = AchievementWGData.Instance:GetAccordionTable()
	if nil == accordion_tab then return end
	if nil == self.cj_cell_list then return end
	for k, v in pairs(self.cj_cell_list) do
		local child_data = accordion_tab[k].child
		if nil ~= accordion_tab[k] or nil ~= child_data then
			for k1, v1 in pairs(v) do
				v1:SetData(child_data[k1])
			end
		end
	end
end

--外层按钮
function AchievementView:OnClickExpandHandler(index, sort, istrue, is_on)
	if not self.cj_cell_list[index] then
		return
	end
	if self.is_zidong then
		local show_list = {}
		for i, v in ipairs(self.cj_cell_list[index]) do
			show_list = AchievementWGData.Instance:GetAchievementTable(false, index, i)
			v:SetVisible(#show_list > 0)
		end
		return
	end

	if nil == sort or nil == is_on or false == is_on or self.is_zidong then return end
	if istrue and (#self.cj_cell_list[index] < self.btn_outer or self.btn_within ~= index or (self.btn_within == index and self.up_btn_outer == self.btn_outer)) then
		self.btn_outer = 1
	end
	self.btn_within = index
	self:SetChooseTypeSort(sort)
	local obj = self.small_toggle_list[index].list_item_cell[self.btn_outer]
	obj:GetComponent("Toggle").isOn = true

	-- 内层按钮显隐
	local show_list = {}
	local show_index
	for i, v in ipairs(self.cj_cell_list[index]) do
		show_list = AchievementWGData.Instance:GetAchievementTable(false, index, i)
		v:SetVisible(#show_list > 0)
		if #show_list > 0 and not show_index then
			show_index = i
		end
	end

	if self.big_toggle_list[index].accordion_element.isOn then
		if not self.is_zidong then
			self.cj_cell_list[index][show_index]:OnClickItem(true)
		end
	end
end

--外层按钮(为了区分手动和自动点击)
function AchievementView:CaseOnClickExpandHandler(index, sort, istrue, is_on)
	if nil == sort or nil == is_on or false == is_on then return end
	if not self.cj_cell_list[index] then
		return
	end
	if istrue and (#self.cj_cell_list[index] < self.btn_outer or self.btn_within ~= index or (self.btn_within == index and self.up_btn_outer == self.btn_outer)) then
		self.btn_outer = 1
	end
	self.btn_within = index
	self:SetChooseTypeSort(sort)
	local obj = self.small_toggle_list[index].list_item_cell[self.btn_outer]
	obj:GetComponent("Toggle").isOn = true
	if self.big_toggle_list[index].accordion_element.isOn then
		if self.is_zidong then
			self.cj_cell_list[index][self.btn_outer]:OnClickItem(true)
			self.is_zidong = false
		end
	end
end

--内层按钮
function AchievementView:OnClickProductHandler(cell_data)
	self.btn_outer = cell_data.client_sort
	self.is_can_flush = true
	if self.up_btn_outer == nil or self.up_btn_outer ~= self.btn_outer then
		self.up_btn_outer = self.btn_outer
	end

	self:SetChooseClientSort(cell_data.client_sort)

	-- 刷新右侧列表
	self:SetAchievementData()
	self:DoAchievementCellAnim()
end

--成就总览界面任务展示条
function AchievementView:CreateRecenAchieveList()
	self.recen_achieve_list = AsyncListView.New(RecenAchievementItemRender, self.node_list.ph_recen_achievement_list)
	self:SetTatolAchieveData()
end

--总览任务条显示
function AchievementView:SetTatolAchieveData()
	local data = AchievementWGData.Instance:GetAchievementTable(true)
	if not data then return end
	--之前排序暂时用不到,注销掉
	local function scortfun(a, b)
		if a.has_active == b.has_active and a.has_fatch == b.has_fatch then --未完成
			return a.id < b.id
		elseif a.has_fatch ~= b.has_fatch then                        --未领取
			return a.has_fatch < b.has_fatch
		else
			return a.has_active > b.has_active
		end
	end
	table.sort(data, scortfun)
	local is_enter = self.is_can_flush or AchievementWGData.Instance.achievement_is_skip
	-- print_error(is_enter,self.recen_achieve_list ~= nil,data)
	if self.recen_achieve_list and is_enter then
		self.recen_achieve_list:SetDataList(data)
		self.recen_achieve_list:JumpToTop()
		self.is_can_flush = false
	elseif self.recen_achieve_list then
		self.recen_achieve_list:SetDataList(data)
	end
end

--成就界面各个部分的任务条
function AchievementView:CreateAchievementList()
	if self.achievement_list then return end
	self.achievement_list = AsyncListView.New(AchievementItemRender, self.node_list.ph_achievement_list)
	self.achievement_list:SetRefreshCallback(BindTool.Bind(self.DoAchievementCellAnim, self))
end

--刷新任务是列表
function AchievementView:SetAchievementData()
	local type_sort = self:GetChooseTypeSort()
	local client_sort = self:GetChooseClientSort()
	if not type_sort or not client_sort then return end
	local data = AchievementWGData.Instance:GetAchievementTable(false, type_sort, client_sort)
	if not data then return end
	local function scortfun(a, b)
		if a.has_active == b.has_active and a.has_fatch == b.has_fatch then --未完成
			return a.id < b.id
		elseif a.has_fatch ~= b.has_fatch then                        --未领取
			return a.has_fatch < b.has_fatch
		else
			return a.has_active > b.has_active
		end
	end

	table.sort(data, scortfun)
	local is_enter = self.is_can_flush or AchievementWGData.Instance.achievement_is_skip
	if self.achievement_list and is_enter then
		self.achievement_list:SetDataList(data)
		self.achievement_list:JumpToTop()
		self.is_can_flush = false
	elseif self.achievement_list then
		self.achievement_list:SetDataList(data)
	end
end

--判断按钮红点是否显示
function AchievementView:IsBtnRedShow()
	local red_is_show = false
	local accordion_tab = AchievementWGData.Instance:GetAccordionTable()
	for i = 1, #self.big_toggle_list do
		local accor_data = nil
		if nil ~= accordion_tab[i] and nil ~= accordion_tab[i].child then
			self.big_toggle_list[i].list_item_cell:SetData(accordion_tab[i])
			accor_data = accordion_tab[i].child
			for k, v in pairs(accor_data) do
				if v.remind_tip > 0 then
					red_is_show = true
				end
			end
		end
	end
	if self.cj_cell_list[TOGGLE_MAX] then
		if not red_is_show then
			self:XuanZeBtn()
		else
			if accordion_tab ~= nil and not IsEmptyTable(accordion_tab) and self.btn_outer ~= nil and self.btn_within ~= nil then
				if accordion_tab[self.btn_within] ~= nil and accordion_tab[self.btn_within].child ~= nil and accordion_tab[self.btn_within].child[self.btn_outer] ~= nil then
					local child_data = accordion_tab[self.btn_within].child[self.btn_outer]
					if child_data ~= nil and child_data.remind_tip > 0 then
						return
					end
				end
			end

			self:SkipView()
		end
	end
end

--自动跳转界面判断
function AchievementView:SkipView()
	if not self.cj_cell_list[TOGGLE_MAX] then return end
	local accordion_tab = AchievementWGData.Instance:GetAccordionTable()
	for i = 1, #self.big_toggle_list do
		local accor_data = nil
		if nil ~= accordion_tab[i] and nil ~= accordion_tab[i].child then
			accor_data = accordion_tab[i].child
			for k, v in pairs(accor_data) do
				if v.remind_tip > 0 then
					if i ~= self.btn_within then
						self.btn_within = i
						self.btn_outer = k
						self.is_zidong = true
						self.big_toggle_list[i].accordion_element.isOn = true
						self:CaseOnClickExpandHandler(i, accordion_tab[i].type_sort, false, true)
					elseif i == self.btn_within and self.btn_outer ~= k then
						self.btn_within = i
						self.btn_outer = k
						self.is_zidong = true
						self.big_toggle_list[i].accordion_element.isOn = true
						self:CaseOnClickExpandHandler(i, accordion_tab[i].type_sort, false, true)
					end
					return
				end
			end
		end
	end
	self.btn_outer = 1
end

-- 跨脚本共用变量
function AchievementView:SetPageValue(value)
	self.page = value
end

function AchievementView:GetPageValue()
	return self.page
end

function AchievementView:LeftPage()
	local is_min = false
	self.page = self.page - 1
	if self.page < 1 then
		self.page = 1
		is_min = true
	end

	return is_min
end

function AchievementView:RightPage()
	local is_max = false
	self.page = self.page + 1
	if self.page > 2 then
		self.page = 2
		is_max = true
	end

	return is_max
end

function AchievementView:SetChooseTypeSort(value)
	self.choose_type_sort = value
end

function AchievementView:GetChooseTypeSort()
	return self.choose_type_sort
end

function AchievementView:SetChooseClientSort(value)
	self.choose_client_sort = value
end

function AchievementView:GetChooseClientSort()
	return self.choose_client_sort
end

function AchievementView:SetIsNeedFlush(value)
	self.is_need_flush = value
end

function AchievementView:GetIsNeedFlush()
	return self.is_need_flush
end

function AchievementView:GetHeLuoViewType()
	return self.main_view_type
end

function AchievementView:SetShenHuaLevel(value)
	self.shenhua_level = value
end

function AchievementView:GetShenHuaLevel()
	return self.shenhua_level
end

--一键领取按钮
function AchievementView:OnClickOneReceive()
	local red_is_show = false
	local accordion_tab = AchievementWGData.Instance:GetAccordionTable()
	for k_1, v_1 in pairs(accordion_tab) do
		for k_2, v_2 in pairs(v_1.child) do
			if v_2.remind_tip > 0 then
				red_is_show = true
			end
		end
	end

	if red_is_show then
		local type_sort = self:GetChooseTypeSort()
		AchievementWGCtrl.Instance:SendCSAchievementFetchReward(type_sort, 0)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Achievement.NoRewardTip)
	end
end

function AchievementView:DoAchievementCellAnim()
	if not self.can_do_achievement_anim or not self.achievement_list then
		return
	end

	local list = self.achievement_list:GetAllItems()
	if #list <= 0 then
		return
	end

	self.can_do_achievement_anim = false

	UITween.CleanAllTween(GuideModuleName.Achievement)

	local tween_info = UITween_CONSTS.AchievementSys
	self.node_list["ph_achievement_list"]:SetActive(false)

	ReDelayCall(self, function()
		self.node_list["ph_achievement_list"]:SetActive(true)

		local count = 0
		local list = self.achievement_list:GetAllItems()
		local sort_list = GetSortListView(list)

		for k, v in ipairs(sort_list) do
			if 0 ~= v.index then
				count = count + 1
			end

			v.item:PalyAchievementItemRenderAnim(count)
		end
	end, tween_info.AlphaTime, "Achievement_Cell_Tween")
end

function AchievementView:SetCanDoAchievementAnim()
	self.can_do_achievement_anim = true
end

----------------------------------------------------------------------------
-- 大类型toggle
--AchievementBigTypeToggleRender
----------------------------------------------------------------------------
AchievementBigTypeToggleRender = AchievementBigTypeToggleRender or BaseClass(BaseRender)
function AchievementBigTypeToggleRender:__init()

end

function AchievementBigTypeToggleRender:__delete()
end

function AchievementBigTypeToggleRender:OnFlush()
	self.node_list.text_btn.text.text = self.data.type_str
	self.node_list.text_high_btn.text.text = self.data.type_str
	local red_is_show = false
	for k, v in pairs(self.data.child) do
		--可领取的任务个数>0
		if v.remind_tip > 0 then
			red_is_show = true
		end
	end
	self.node_list.img_read:SetActive(red_is_show)
end

----------------------------------------------------------------------------
-- 小类型toggle
-- AchievementListItemRender
----------------------------------------------------------------------------
AchievementListItemRender = AchievementListItemRender or BaseClass(BaseRender)
function AchievementListItemRender:__init()
end

function AchievementListItemRender:__delete()
	self.parent_view = nil
end

function AchievementListItemRender:LoadCallBack()
	self.view.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickItem, self))
end

function AchievementListItemRender:OnClickItem(is_on)
	if nil == is_on then return end
	if true == is_on then
		for k, v in pairs(self.parent_view.cj_cell_list) do
			for k1, v1 in pairs(v) do
				v1:OnSelectChange(false)
			end
		end
		self.parent_view:OnClickProductHandler(self.data) --最里层按钮点击
		self:OnSelectChange(true)
	else
		self:OnSelectChange(false)
	end
end

function AchievementListItemRender:OnFlush()
	self.node_list.img_remind:SetActive(self.data.remind_tip >= 1)
	local per = "（" .. self.data.param3 .. "/" .. self.data.param4 .. "）"
	self.node_list.text_name.text.text = self.data.client_type_str .. per --江湖等级
	self.node_list.text_name_hl.text.text = self.data.client_type_str .. per
end

function AchievementListItemRender:CreateSelectEffect()

end

function AchievementListItemRender:OnSelectChange(is_select)
	if self.node_list["highlight"] and self.node_list["text_name"] then
		local name = self.node_list.text_name.text.text
		self.node_list.text_name.text.text = name
	end
end

----------------------------------------------------------------------------
-- RecenAchievementItemRender
----------------------------------------------------------------------------
RecenAchievementItemRender = RecenAchievementItemRender or BaseClass(BaseRender)
function RecenAchievementItemRender:__init()

end

function RecenAchievementItemRender:__delete()
	if self.item_cell_1 then
		self.item_cell_1:DeleteMe()
		self.item_cell_1 = nil
	end
	if self.item_cell_2 then
		self.item_cell_2:DeleteMe()
		self.item_cell_2 = nil
	end
	if self.total_list then
		self.total_list:DeleteMe()
		self.total_list = nil
	end
end

function RecenAchievementItemRender:LoadCallBack()
	if not self.item_cell_1 then
		self.item_cell_1 = ItemCell.New(self.node_list.ph_item_cell_1)
		self.item_cell_2 = ItemCell.New(self.node_list.ph_item_cell_2)
		XUI.AddClickEventListener(self.node_list.btn_item_get, BindTool.Bind1(self.OnClickToGetReward, self))
	end
end

function RecenAchievementItemRender:OnFlush()
	--客户端做的假的
	local progress_all_value = AchievementWGData.Instance:GetAllAchievement()
	self.node_list.lbl_item_name.text.text = self.data.sub_type_str

	self.node_list.img_yes_reward:SetActive(self.data.has_active ~= 0 and self.data.has_fatch ~= 0)
	self.node_list.img_no_reward:SetActive(self.data.has_active == 0 or
		(self.data.has_active ~= 0 and self.data.has_fatch == 0 and progress_all_value < self.data.param1))

	self.node_list.btn_item_get:SetActive(self.data.has_active ~= 0 and self.data.has_fatch == 0 and
		progress_all_value >= self.data.param1)

	local desc = ''
	desc = self.data.client_show
	local progress_value = self.data.has_fatch ~= 0 and self.data.param1 or progress_all_value
	local per = progress_value .. "/" .. self.data.param1
	per = ToColorStr(per, progress_value >= self.data.param1 and COLOR3B.C8 or COLOR3B.C10)

	self.node_list.rich_item_desc.text.text = desc
	self.node_list.rich_item_desc2.text.text = string.format("（%s）", per)

	local sex = RoleWGData.Instance:GetRoleSex()
	local show_cfg = sex == GameEnum.MALE and self.data.man_reward_item or self.data.woman_reward_item
	local show_list = {}
	for i, v in pairs(show_cfg) do
		table.insert(show_list, v)
	end
	-- 基础货币
	local money_id
	local num
	if self.data.bind_gold > 0 then
		money_id = 65533
		num = self.data.bind_gold
	elseif self.data.gold and self.data.gold > 0 then
		money_id = 65534
		num = self.data.gold
	elseif self.data.sliver_ticket and self.data.sliver_ticket > 0 then
		money_id = 65531
		num = self.data.sliver_ticket
	elseif self.data.coin > 0 then
		money_id = 65535
		num = self.data.coin
	elseif self.data.prestige > 0 then
		money_id = 90086
		num = self.data.prestige
	end

	if money_id then
		table.insert(show_list, { item_id = money_id, num = num })
	end

	for i = 1, 2 do
		if show_list[i] then
			self['item_cell_' .. i]:SetData(show_list[i])
		end
		self['item_cell_' .. i]:SetVisible(show_list[i] ~= nil)
	end
end

function RecenAchievementItemRender:OnClickToGetReward()
	AchievementWGCtrl.Instance:SendCSAchievementFetchReward(self.data.id, 1)
	AudioService.Instance:PlayRewardAudio()
end

----------------------------------------------------------------------------
-- AchievementItemRender
----------------------------------------------------------------------------
AchievementItemRender = AchievementItemRender or BaseClass(BaseRender)
function AchievementItemRender:__init()

end

function AchievementItemRender:__delete()
	if self.item_cell_1 then
		self.item_cell_1:DeleteMe()
		self.item_cell_1 = nil
	end
	if self.item_cell_2 then
		self.item_cell_2:DeleteMe()
		self.item_cell_2 = nil
	end
end

function AchievementItemRender:LoadCallBack()
	if not self.item_cell_1 then
		self.item_cell_1 = ItemCell.New(self.node_list.ph_item_cell_1)
		self.item_cell_1.hide_numtxt_less_num = 0
		self.item_cell_2 = ItemCell.New(self.node_list.ph_item_cell_2)
		XUI.AddClickEventListener(self.node_list.btn_item_get, BindTool.Bind1(self.OnClickToGetReward, self)) --领取请求7231
		XUI.AddClickEventListener(self.node_list.item_btn, BindTool.Bind1(self.OnClickItemBtn, self))
	end
end

function AchievementItemRender:OnFlush()
	--active ~=0完成  fatch ~=0领取
	self.node_list.lbl_item_name.text.text = self.data.sub_type_str
	self.node_list.lbl_item_score.text.text = self.data.chengjiu

	self.node_list.img_yes_reward:SetActive(self.data.has_active ~= 0 and self.data.has_fatch ~= 0)
	self.node_list.img_no_reward:SetActive(self.data.has_active == 0)

	self.node_list.btn_item_get:SetActive(self.data.has_active ~= 0 and self.data.has_fatch == 0)

	local desc = ''
	-- local str = " "
	desc = self.data.client_desc
	local spe_value = AchievementWGData.Instance:GetSpeProgressValueById(self.data.id)
	if self.data.progress_show >= 0 and self.data.progress_value then
		local param1 = self.data.progress_show == 26 and self.data.param1 * 10000 or self.data.param1
		local progress_value = (self.data.has_fatch ~= 0 or self.data.has_active ~= 0) and self.data.param1 or
			self.data.progress_value
		local per
		if self.data.id <= level_achievement_id then -- 等级成就（等级巅峰转化）
			local is_vis = {}
			local level = {}

			is_vis[1], level[1] = RoleWGData.Instance:GetDianFengLevel(progress_value)
			is_vis[2], level[2] = RoleWGData.Instance:GetDianFengLevel(param1)
			for i = 1, 2 do
				if is_vis[i] then
					level[i] = string.format(Language.Achievement.DianFeng, level[i])
				end
			end
			per = level[1] .. "/" .. level[2]
		else
			per = progress_value .. "/" .. param1
		end
		per = ToColorStr(per, progress_value >= param1 and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED)
		desc = XmlUtil.RelaceTagContent(desc, "per", per)
	elseif self.data.progress_show >= 999 and spe_value then
		local param1 = self.data.progress_show == 26 and self.data.param1 * 10000 or self.data.param1
		local progress_value = (self.data.has_fatch ~= 0 or self.data.has_active ~= 0) and self.data.param1 or spe_value

		local per = progress_value .. "/" .. param1
		per = ToColorStr(per, progress_value >= param1 and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED)
		desc = XmlUtil.RelaceTagContent(desc, "per", per)
	end
	--
	self.node_list.rich_item_desc.text.text = desc

	local sex = RoleWGData.Instance:GetRoleSex()
	local show_cfg = sex == GameEnum.MALE and self.data.man_reward_item or self.data.woman_reward_item
	local show_list = {}
	for i, v in pairs(show_cfg) do
		table.insert(show_list, v)
	end
	-- 基础货币
	local money_id
	local num
	local money_tab = {}

	if self.data.bind_gold > 0 then
		money_id = COMMON_CONSTS.VIRTUAL_ITEM_BIND_GOLD
		num = self.data.bind_gold
		table.insert(money_tab, { item_id = money_id, num = num })
	end
	if self.data.gold and self.data.gold > 0 then
		money_id = COMMON_CONSTS.VIRTUAL_ITEM_GOLD
		num = self.data.gold
		table.insert(money_tab, { item_id = money_id, num = num })
	end
	if self.data.sliver_ticket and self.data.sliver_ticket > 0 then
		money_id = COMMON_CONSTS.VIRTUAL_ITEM_YUANBAO
		num = self.data.sliver_ticket
		table.insert(money_tab, { item_id = money_id, num = num })
	end
	if self.data.coin > 0 then
		money_id = COMMON_CONSTS.VIRTUAL_ITEM_BIND_COIN
		num = self.data.coin
		table.insert(money_tab, { item_id = money_id, num = num })
	end
	if self.data.prestige > 0 then
		money_id = COMMON_CONSTS.VIRTUAL_ITEM_SHENGWANG
		num = self.data.prestige
		table.insert(money_tab, { item_id = money_id, num = num })
	end
	for k, v in pairs(money_tab) do
		table.insert(show_list, v)
	end
	for i = 1, 2 do
		if show_list[i] then
			self['item_cell_' .. i]:SetData(show_list[i])
		end
		self['item_cell_' .. i]:SetVisible(show_list[i] ~= nil)
	end
end

function AchievementItemRender:ReturnNeedShow(data)
	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(data.item_id)
	if big_type == GameEnum.ITEM_BIGTYPE_EXPENSE then
		--绑定铜钱和绑定元宝
		if 2 == item_cfg.use_type or 8 == item_cfg.use_type then
			if data.num > 0 then
				return CommonDataManager.ConverExp(item_cfg.param1 * data.num, true, true)
			end
		end
	elseif data.num > 1 then
		return CommonDataManager.ConverExp(data.num, true, true)
	end
end

function AchievementItemRender:OnClickToGetReward()
	AchievementWGCtrl.Instance:SendCSAchievementFetchReward(self.data.id, 1)
	AudioService.Instance:PlayRewardAudio()
end

function AchievementItemRender:OnClickItemBtn()
	TipWGCtrl.Instance:OpenItem({ item_id = 90614 })
end

function AchievementItemRender:PalyAchievementItemRenderAnim(item_index)
	if not self.node_list["tween_root"] then return end
	local wait_index = item_index - 1
	wait_index = wait_index < 0 and 0 or wait_index

	local tween_info = UITween_CONSTS.AchievementSys.ListCellTween
	UITween.FakeHideShow(self.node_list["tween_root"])
	ReDelayCall(self, function()
		if self.node_list and self.node_list["tween_root"] then
			UITween.DoScaleAlaphaShow(GuideModuleName.Achievement, self.node_list["tween_root"], tween_info)
		end
	end, tween_info.NextDoDelay * wait_index, "Achievement_item_" .. wait_index)
end
