require("game/daily/daily_wabao_wg_ctrl")
-- require("game/daily/daily_view_lingyu")
-- require("game/daily/daily_juqingfb_view")
-- require("game/daily/daily_team_view")
-- require("game/daily/daily_view")
require("game/daily/daily_wg_data")
-- require("game/daily/daily_ph_reward_view")
-- require("game/daily/welkin_preview_view")
-- require("game/daily/daliy_peteq_reward_view")

-- 日常
DailyWGCtrl = DailyWGCtrl or BaseClass(BaseWGCtrl)
EquipFb = {
	MAX_LEVEL = 40,
	}
WeaponFb = {
	MAX_CHAPTER = 20,
	MAX_LEVEL = 10,
	MAX_REWARD = 3,
	}
WelfareFb = {
	MAX_COUNT = 8
	}

function DailyWGCtrl:__init()
	if DailyWGCtrl.Instance then
		error("[DailyWGCtrl]:Attempt to create singleton twice!")
	end
	DailyWGCtrl.Instance = self

	self.data = DailyWGData.New()
	-- self.dailyfb_view = DailyDailyFbView.New()
	-- self.lingyufb_view = DailyLingyuView.New()
	-- self.juqingfb_view = DailyJuQingFbView.New()
	-- self.dailyteam_view =  DailyTeamView.New()
	-- self.ph_reward_view =  DailyPhRewardView.New()
	-- self.welkin_pre_view = FBWelkinPreView.New()
	-- self.daliy_peteq_reward_view = PeteqRewardView.New()

	self:RegisterAllProtocals()
	self:RegisterAllRemind()
	self:WabaoInit()
end

function DailyWGCtrl:__delete()

	-- self.dailyteam_view:DeleteMe()
	-- self.dailyteam_view = nil

	-- self.juqingfb_view:DeleteMe()
	-- self.juqingfb_view = nil

	-- self.dailyfb_view:DeleteMe()
	-- self.dailyfb_view = nil

	-- self.lingyufb_view:DeleteMe()
	-- self.lingyufb_view = nil

	-- self.ph_reward_view:DeleteMe()
	-- self.ph_reward_view = nil

	-- self.welkin_pre_view:DeleteMe()
	-- self.welkin_pre_view = nil

	-- self.daliy_peteq_reward_view:DeleteMe()
	-- self.daliy_peteq_reward_view = nil


	-- self.view:DeleteMe()
	-- self.view = nil

	self.data:DeleteMe()
	self.data = nil
	self:DeleteWaBao()
	DailyWGCtrl.Instance = nil
end

function DailyWGCtrl:RegisterAllProtocals()
	--扫荡
	self:RegisterProtocol(SCAutoFbResult, "OnAutoFbResult")
	--装备本
	self:RegisterProtocol(SCEquipFbInfo, "OnEquipFbInfo")
	self:RegisterProtocol(CSEquipFbOperate)
	--福利本
	self:RegisterProtocol(SCWelfareFbInfo, "OnWelfareFbInfo")
	self:RegisterProtocol(CSWelfareFbOperate)

	--多人副本房间
	self:RegisterProtocol(SCTeamFbRoomInfo, "OnTeamFbRoomInfo")
	self:RegisterProtocol(CSTeamFbRoomOperate)

	--宠物品质挑战本
	self:RegisterProtocol(CSChallengeFbOperate)
	self:RegisterProtocol(SCChallengeFbInfo, "OnChallengeFbInfo")
	self:RegisterProtocol(SCChallengeSceneInfo, "OnChallengeSceneInfo")

	--宠物装备本
	self:RegisterProtocol(CSWeaponFbOperate)
	self:RegisterProtocol(SCWeaponFbInfo, "OnWeaponFbInfo")

	--天仙阁
	-- self:RegisterProtocol(CSTianxiangeFbOperate)
	-- self:RegisterProtocol(SCTianxiangeInfo, "OnTianxiangeInfo")
	-- self:RegisterProtocol(SCTianxiangeSceneInfo, "OnTianxiangeSceneInfo")

	--高战本
	self:RegisterProtocol(CSGaozhanFbOperate)
	self:RegisterProtocol(SCGaozhanFbInfo, "OnGaoZhanInfo")
	self:RegisterProtocol(SCGaozhanSceneInfo, "OnGaozhanSceneInfo")

	--无双副本
	self:RegisterProtocol(CSWushuangFbOperate)
	self:RegisterProtocol(SCWushuangFbInfo, "OnWushuangInfo")
	self:RegisterProtocol(SCWushuangSceneInfo, "OnWushaungSceneInfo")

end

function DailyWGCtrl:RegisterAllRemind()
	self:BindGlobalEvent(OtherEventType.DAY_COUNT_CHANGE, BindTool.Bind(self.DaycountChange, self))
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.MainuiOpenCreate, self))
	self:BindGlobalEvent(SceneEventType.SCENE_CHANGE_COMPLETE, BindTool.Bind1(self.OnSceneChangeComplete, self))
end

function DailyWGCtrl:MainuiOpenCreate()
	ActivityWGCtrl.Instance:SendGetTuMoTaskInfo()
	-- ActivityWGCtrl.Instance:SendGetPaoHuanTaskInfo()
	self:SendStoryFBGetRoleInfo()
	self:SendDailyFbGetRoleInfo()
	-- PataWGCtrl.Instance:SendPataFbGetAllInfo()
	self:SendChallengeFbInfo()
	self:SendWeaponFbInfo()
	-- GongChengWGCtrl.Instance:SendGongchengzhanOperate(SIEGE_OPERA_TYPE.REQ_INFO)
	-- self:SendPataFbInfo()
	self:SendGaoZhanFbOperate()
	self:SendWushuangInfo()
end


function DailyWGCtrl:OnSceneChangeComplete()

end

--活跃度信息
function DailyWGCtrl:OnActiveDegreeInfo(protocol)
	-- self.data:SetRewardFlags(protocol.reward_flags)
	-- self.data:SetDegreeList(protocol.degree_list)
	-- self.data:UpdateRewardStatus()
end

function DailyWGCtrl:DaycountChange(day_counter_id)

end

--------------------------------------
--界面
--------------------------------------
function DailyWGCtrl:Open(index, param_t)

	-- self.view:Open(index, param_t)


	if param_t and "select_daily_richang" == param_t.to_ui_name and nil ~= param_t.to_ui_param then
		-- self.view:Flush(index, "select_daily_richang", param_t)
	end
end

function DailyWGCtrl:Close()
	-- self.view:Close()
	-- self.dailyfb_view:Close()
	-- self.lingyufb_view:Close()
	-- self.juqingfb_view:Close()
end

function DailyWGCtrl:OpenDailyFbView(daily_fb_data)
	-- self.dailyfb_view:SetDailyFbData(daily_fb_data)
	-- self.dailyfb_view:Open()
end

function DailyWGCtrl:OpenLingyuFbView(daily_fb_data)
	-- self.lingyufb_view:SetDailyFbData(daily_fb_data)
	-- self.lingyufb_view:Open()
end

function DailyWGCtrl:OpenJuQingFbView(chapter_data)
	-- self.juqingfb_view:SetChapterData(chapter_data)
	-- self.juqingfb_view:Open()
end

function DailyWGCtrl:OpenDailyTeamView(fuben_data)
	-- self.dailyteam_view:SetCreateTeamData(fuben_data)
	-- self.dailyteam_view:Open()
end

function DailyWGCtrl:OpenPhRewardView()
	-- self.ph_reward_view:Open()
end

--扫荡结果
function DailyWGCtrl:OnAutoFbResult(protocol)
	local info = {}
	info.level = protocol.level
	info.is_pass = protocol.is_pass 					-- 0失败 1通关 2副本中更新
	info.fight_layer = protocol.fight_layer
	info.is_active_leave_fb = protocol.is_active_leave_fb
end


-------------------------------------------------
---装备本
-------------------------------------------------
function DailyWGCtrl:OnEquipFbInfo(protocol)
	self.data:OnEquipFbInfo(protocol)
	-- self.view:Flush(DailyViewIndex.Equip)
end
--装备本请求信息（可在打开面板第一次请求，之后的变化服务端会主动通知）
function DailyWGCtrl:SendStoryFBGetRoleInfo()
	self:SendEquipFbOperate(EQUIP_FB_OPERA_TYPE.EQUIPFB_REQ_INFO)
end
function DailyWGCtrl:SendEquipFbOperate(operate, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEquipFbOperate)
	protocol.operate = operate
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

--------------------------
--日常本
--------------------------
function DailyWGCtrl:OnWelfareFbInfo(protocol)
	self.data:OnDailyFbFBRoleInfo(protocol)
	-- self.view:Flush(DailyViewIndex.RiChang)
	BiZuoWGCtrl.Instance:Flush()
end

--日常副本请求信息（可在打开面板第一次请求，之后的变化服务端会主动通知）
function DailyWGCtrl:SendDailyFbGetRoleInfo()
	self:SendWelfareFbOperate(FULI_FB_OPERA_TYPE.WELFAREFB_REQ_INFO)
end

function DailyWGCtrl:SendDailyFbSaodang(index, auto_buy, double)
	self:SendWelfareFbOperate(FULI_FB_OPERA_TYPE.WELFAREFB_AUTO, index, auto_buy, double)
end
--  福利本请求购买次数
function DailyWGCtrl:SendDailyExtraBuy(index)
	self:SendWelfareFbOperate(FULI_FB_OPERA_TYPE.WELFAREFB_REQ_BUY_TIMES,index)
end

--福利副本操作
function DailyWGCtrl:SendWelfareFbOperate(operate, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSWelfareFbOperate)
	protocol.operate = operate
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

---------------------------------------------------
--多人副本
---------------------------------------------------
function DailyWGCtrl:OnTeamFbRoomInfo(protocol)
	self.data:OnTeamFbRoomInfo(protocol)
	-- self.view:Flush(DailyViewIndex.TeamFb)
	--临时测试所为  以后优化
	-- require("game/manytower/manytower_wg_ctrl")
	-- if ManyTowerWGCtrl.Instance.view then
	-- 	ManyTowerWGCtrl.Instance.view:Flush()
	-- end
end

function DailyWGCtrl:SendTeamFbRoomOperate(operate_type, param1, param2, param3, param4)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTeamFbRoomOperate)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol.param4 = param4 or 0
	protocol:EncodeAndSend()
end
-- 请求房间列表
function DailyWGCtrl:SendTeamFbReqRoomList(team_type)
	Log("-------------DailyWGCtrl:SendTeamFbReqRoomList", team_type)
	self:SendTeamFbRoomOperate(TEAM_FB_OPERAT_TYPE.REQ_ROOM_LIST, team_type)
end
-- 创建房间
function DailyWGCtrl:SendTeamFbCreateRoom(team_type, mode, cap_limit, is_auto_start)
	Log("-------------DailyWGCtrl:SendTeamFbCreateRoom", team_type, mode, cap_limit, is_auto_start)
	self:SendTeamFbRoomOperate(TEAM_FB_OPERAT_TYPE.CREATE_ROOM, team_type, cap_limit, is_auto_start)
end
-- 加入指定房间
function DailyWGCtrl:SendTeamFbJoinRoom(team_type, team_index)
	Log("-------------DailyWGCtrl:SendTeamFbJoinRoom", team_type, team_index)
	self:SendTeamFbRoomOperate(TEAM_FB_OPERAT_TYPE.JOIN_ROOM, team_type, team_index)
end
-- 开始
function DailyWGCtrl:SendTeamFbStarFb(is_ready)
	Log("-------------DailyWGCtrl:SendTeamFbStarFb", is_ready)
	self:SendTeamFbRoomOperate(TEAM_FB_OPERAT_TYPE.START_ROOM, is_ready)
end
-- 退出房间
function DailyWGCtrl:SendTeamFbExitRoom()
	Log("-------------DailyWGCtrl:SendTeamFbExitRoom")
	self:SendTeamFbRoomOperate(TEAM_FB_OPERAT_TYPE.EXIT_ROOM)
end
-- 改变模式
function DailyWGCtrl:SendTeamFbChangeMode(mode)
	Log("-------------DailyWGCtrl:SendTeamFbChangeMode", mode)
	-- self:SendTeamFbRoomOperate(TEAM_FB_OPERAT_TYPE.CHANGE_MODE, mode)
end

--------宠物品质挑战本----

-- 挑战副本信息
function DailyWGCtrl:OnChallengeFbInfo(protocol)
	self.data:SetLingyuFbInfo(protocol)
	-- self.view:Flush(DailyViewIndex.PetQuality)
end

-- 挑战副本（灵玉副本）场景信息
function DailyWGCtrl:OnChallengeSceneInfo(protocol)
	self.data:SetLingyuSceneInfo(protocol)
	FuBenWGCtrl.Instance:UpdataTaskFollow()
	if 1 == protocol.is_finish and 1 == protocol.is_pass then
		Scene.Instance:InitDoorList()
		 GlobalTimerQuest:AddDelayTimer(function ()
		 	if Scene.Instance:GetSceneLogic().SetGuaiJiDoors then
		 		Scene.Instance:GetSceneLogic():SetGuaiJiDoors()
		 	end
		 end, 1)
	end
	if 1 == protocol.is_pass and protocol.layer == ChallengeFb.MAX_LAYER - 1 then
		local finishvo = FuBenWGData.CreateCommonPassVo()
		finishvo.scene_type = SceneType.LingyuFb
		finishvo.is_pass = protocol.is_pass
		finishvo.reward_list = FuBenWGData.Instance:GetPickRewardList(SceneType.LingyuFb) or {}
		finishvo.tip1 = string.format(Language.FuBen.FubenJindu, protocol.layer + 1, ChallengeFb.MAX_LAYER)
		finishvo.tip2 = Language.FuBen.TongGuanReward
		FuBenWGCtrl.Instance:OpenFuBenTxtEndView(finishvo)
	end
end

-- 灵玉挑战副本请求
function DailyWGCtrl:SendChallengeFbOperate(operate, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSChallengeFbOperate)
	protocol.operate = operate
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

-- 挑战副本请求信息
function DailyWGCtrl:SendChallengeFbInfo()
	self:SendChallengeFbOperate(LINGYU_FB_OPERA_TYPE.REQINFO)
end

-- 挑战副本购买次数
function DailyWGCtrl:SendChallengeFbBuy()
	self:SendChallengeFbOperate(LINGYU_FB_OPERA_TYPE.BUYJOINTIMES)
end

-- 挑战副本扫荡
function DailyWGCtrl:SendChallengeFbSao(level, is_auto_buy)
	self:SendChallengeFbOperate(LINGYU_FB_OPERA_TYPE.AUTO, level, is_auto_buy)
end

-- 挑战副本重置关卡
function DailyWGCtrl:SendChallengeFbReset(level)
	self:SendChallengeFbOperate(LINGYU_FB_OPERA_TYPE.RESETLEVEL, level)
end

-------宠物装备副本---------------------

-- 请求武器副本信息
function DailyWGCtrl:SendWeaponFbInfo()
	self:SendWeaponFbOperate(WEAPONFB_OPERA_TYPE.WEAPONFB_REQ_INFO)
end

-- 请求武器副本领取奖励
function DailyWGCtrl:SendWeaponFbReward(chapter, reward_index)
	self:SendWeaponFbOperate(WEAPONFB_OPERA_TYPE.WEAPONFB_FETCH_REWARD, chapter, reward_index)
end

-- 请求武器副本扫荡
function DailyWGCtrl:SendWeaponFbSaodang(chapter, level, is_autobuy)
	self:SendWeaponFbOperate(WEAPONFB_OPERA_TYPE.WEAPONFB_AUTO, chapter, level, is_autobuy)
end

-- 请求武器副本增加次数
function DailyWGCtrl:SendWeaponFbAddCount()
	self:SendWeaponFbOperate(WEAPONFB_OPERA_TYPE.WEAPONFB_ADD_TIMES)
end

-- 武器副本操作
function DailyWGCtrl:SendWeaponFbOperate(operate, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSWeaponFbOperate)
	protocol.operate = operate
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

-- 武器副本信息
function DailyWGCtrl:OnWeaponFbInfo(protocol)
	self.data:SetPeteqFbInfo(protocol)
	-- self.view:Flush(DailyViewIndex.PetEquip)
end

-- 武器副本场景信息
function DailyWGCtrl:SetCurPeteqfbLevel(level)
	self.data:SetCurPeteqfbLevel(level)
end

-- 武器副本场景信息
function DailyWGCtrl:OpenPeteqRewardView(chapter, index)
	-- self.daliy_peteq_reward_view:OpenReward(chapter, index)
end

-- 宠物装备副本刷新面板
function DailyWGCtrl:FlushPeteqView()
	-- self.view:Flush(DailyViewIndex.PetEquip)
end

-------高战副本---------------------

function DailyWGCtrl:SendGaoZhanFbOperate(operate, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGaozhanFbOperate)
	protocol.operate = operate or 0
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

function DailyWGCtrl:OnGaoZhanInfo(protocol)
	local pass_level = protocol.level
	local auto_cur_level = protocol.auto_cur_level
	local can_auto_max_level = protocol.can_auto_max_level

	self.data:SetGaoZhanInfo(pass_level, auto_cur_level, can_auto_max_level)
	-- self.view:Flush(DailyViewIndex.HighFb)
end

function DailyWGCtrl:OnGaozhanSceneInfo(protocol)
	if 1 == protocol.is_finish then
		local pass_vo = FuBenWGData.CreateCommonPassVo()
		pass_vo.scene_type = FUBEN_TYPE.GAOZHANFB
		pass_vo.is_pass = protocol.is_pass
		pass_vo.reward_list = {}
		pass_vo.tip1 = Language.FuBen.TongGuanTime .. HtmlTool.GetHtml(TimeUtil.FormatSecondDHM8(protocol.pass_time_s), COLOR3B.GREEN , 24)
		pass_vo.tip3 = Language.FuBen.NextLevel
		if 0 == protocol.is_pass then
			pass_vo.reward_list = {}
			pass_vo.tip2 = Language.FuBen.TongGuanReward .. HtmlTool.GetHtml(Language.Common.No, COLOR3B.GREEN , 24)
		end
		FuBenWGCtrl.Instance:OpenFuBenNextView(pass_vo)
	end
end

-------无双副本---------------------

function DailyWGCtrl:SendWushuangInfo()
	self:SendWushuangFbOperate(WUSHUANG_OPERA_TYPE.WUSHUANGFB_REQ_INFO)
end

function DailyWGCtrl:SendWushuangRewrad()
	self:SendWushuangFbOperate(WUSHUANG_OPERA_TYPE.WUSHUANGFB_FETCH_REWARD)
end

function DailyWGCtrl:SendWushuangFbOperate(operate, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSWushuangFbOperate)
	protocol.operate = operate or 0
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

function DailyWGCtrl:OnWushuangInfo(protocol)
	local pass_level = protocol.level

	self.data:SetWushuangLevelInfo(pass_level)
	-- self.view:Flush(DailyViewIndex.WuShuang)
end

function DailyWGCtrl:OnWushaungSceneInfo(protocol)
	local config = DailyWGData.Instance:GetWushuangFbConfig()
	if not config[protocol.level + 1] then
		return
	end
	if config[protocol.level + 1].need_role_level > RoleWGData.Instance.role_vo.level then return end
	if 1 == protocol.is_finish then
		local pass_vo = FuBenWGData.CreateCommonPassVo()
		pass_vo.scene_type = FUBEN_TYPE.WUSHUANGFB
		pass_vo.is_pass = protocol.is_pass
		-- pass_vo.reward_list = {}
		pass_vo.tip1 = Language.FuBen.TongGuanTime .. HtmlTool.GetHtml(TimeUtil.FormatSecondDHM8(protocol.pass_time_s), COLOR3B.GREEN , 24)
		-- pass_vo.tip3 = Language.FuBen.NextLevel
		-- if 0 == protocol.is_pass then
		-- 	pass_vo.reward_list = {}
		-- 	pass_vo.tip2 = Language.FuBen.TongGuanReward .. HtmlTool.GetHtml(Language.Common.No, COLOR3B.GREEN , 24)
		-- end
		FuBenWGCtrl.Instance:OpenFuBenNextView(pass_vo)
	end
end