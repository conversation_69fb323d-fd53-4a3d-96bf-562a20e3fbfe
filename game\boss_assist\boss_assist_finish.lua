
BossAssistFinishView = BossAssistFinishView or BaseClass(SafeBaseView)
function BossAssistFinishView:__init()
	self.is_use_mask = true
	self.default_index = 1
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/boss_assist_ui_prefab", "layout_boss_kill_reward")
	self.boss_reward_t = {}
	self.boss_first_reward_t = {}
	self.view_cache_time = 0
end

function BossAssistFinishView:__delete()
end

function BossAssistFinishView:ReleaseCallBack()
	for k,v in pairs(self.boss_reward_t) do
		v:DeleteMe()
	end
	self.boss_reward_t = {}
	for k,v in pairs(self.boss_first_reward_t) do
		v:DeleteMe()
	end
	self.boss_first_reward_t = {}
	if self.close_timer then
		GlobalTimerQuest:CancelQuest(self.close_timer)
		self.close_timer = nil
	end
end

function BossAssistFinishView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_enter, BindTool.Bind(self.Close, self))
	self.node_list.jiesuan_bg.rect.sizeDelta = Vector2(0,332)
end

function BossAssistFinishView:ShowIndexCallBack(index)
	self:Flush(index)
	self.close_time = TimeWGCtrl.Instance:GetServerTime() + 10
	if nil == self.close_timer then
		self:UpdateCloseTime()
		self.close_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.UpdateCloseTime, self), 1)
	end
end

function BossAssistFinishView:UpdateCloseTime()
	local time = self.close_time - TimeWGCtrl.Instance:GetServerTime()
	if time > 0 then
		self.node_list.close_time.text.text = string.format(Language.BossAssist.AutoClose, time)
	else
		self:Close()
		GlobalTimerQuest:CancelQuest(self.close_timer)
		self.close_timer = nil
	end
end

function BossAssistFinishView:OpenCallBack()

end

function BossAssistFinishView:CloseCallBack()
	FuBenWGCtrl.Instance:SendLeaveFB()
end

function BossAssistFinishView:OnFlush(param_t, index)
	--旧掉落协议
	--local reward_info = BossAssistWGData.Instance:GetGuildAssisBossRewardInfo()
	local reward_info = BossAssistWGData.Instance:GetWorldBossDropInfo()
	self.node_list.PanelBottom:SetActive(reward_info.has_first_reward == 1)
	local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[reward_info.target_boss_id]
	if monster_cfg then
		self.node_list.normal_text.text.text = string.format(Language.BossAssist.KillText, monster_cfg.name .. "lv." .. monster_cfg.level)
		if SocietyWGData.Instance:GetIsInTeam() == 1 then
			self.node_list.first_text.text.text = Language.BossAssist.TeamKillFirstText
		else
			self.node_list.first_text.text.text = Language.BossAssist.KillFirstText
		end
	end
	for k,v in pairs(reward_info.first_reward_item_list) do
       if self.boss_first_reward_t[k] == nil then
            self.boss_first_reward_t[k] = ItemCell.New(self.node_list.first_reward)
       end
       self.boss_first_reward_t[k]:SetData(v)
    end
	for k,v in pairs(reward_info.reward_item_list) do
       if self.boss_reward_t[k] == nil then
            self.boss_reward_t[k] = ItemCell.New(self.node_list.normal_reward)
       end
       self.boss_reward_t[k]:SetData(v)
    end
end
