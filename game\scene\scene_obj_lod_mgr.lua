-- 细节控制器
-- 根据Obj离MainCamera的距离，选择不同的细节等级
SceneObjLODManager = SceneObjLODManager or BaseClass()

local MaxCheckCount = 5 		-- 每一次最多检查的数量
local HighDistance = 10 * 10		-- 高细节的距离
local MiddleDistance = 25 * 25	-- 中细节的距离

function SceneObjLODManager:__init()
	if SceneObjLODManager.Instance then
		print_error("[SceneObjLODManager] Attemp to create a singleton twice !")
	end
	SceneObjLODManager.Instance = self

	self.obj_list1 = {}
	self.obj_list2 = {}
	self.insert_list = nil
	self.remove_list = nil
	self.checked_list = {}

	self.next_check_timer = 0
	self.check_cd = 0.2
	self.quality_level = 0
end

function SceneObjLODManager:__delete()
	self.obj_list1 = {}
	self.obj_list2 = {}
	self.insert_list = nil
	self.remove_list = nil
	self.checked_list = {}
	SceneObjLODManager.Instance = nil
end

function SceneObjLODManager:Update(now_time, elapse_time)
	if now_time < self.next_check_timer then
		return
	end
	self.next_check_timer = now_time + self.check_cd

	if self.insert_list then
		for k,v in pairs(self.insert_list) do
			self.obj_list1[k] = v
		end
		self.insert_list = nil
	end

	if self.remove_list then
		for k,v in pairs(self.remove_list) do
			self.obj_list1[k] = nil
			self.obj_list2[k] = nil
		end
		self.remove_list = nil
	end

	if IsNil(MainCamera) then
		return
	end
	local camera_transform = MainCamera.transform

	local index = 1
	local need_exchange = true
	for k,v in pairs(self.obj_list1) do
		if index > MaxCheckCount then
			need_exchange = false
			break
		end
		index = index + 1

		self.checked_list[k] = v
		local obj_position = v:GetLuaPosition()
		local level = self:CaculateDetailLevel(camera_transform, obj_position)

		if v:ChangeDetailLevel(level) then
			need_exchange = false
			break
		end
	end

	for k,v in pairs(self.checked_list) do
		self.obj_list1[k] = nil
		self.obj_list2[k] = v
	end
	self.checked_list = {}

	if need_exchange then
		self.obj_list1 = self.obj_list2
		self.obj_list2 = {}
	end
end

function SceneObjLODManager:Insert(obj)
	self.insert_list = self.insert_list or {}
	self.insert_list[obj] = obj
end

function SceneObjLODManager:Remove(obj)
	self.remove_list = self.remove_list or {}
	self.remove_list[obj] = true
end

local main_camera_pos = u3d.vec3(0, 0, 0)
local camera_forward = u3d.vec3(0, 0, 0)
function SceneObjLODManager:CaculateDetailLevel(camera_transform, obj_pos)
	-- 最高品质时不做屏蔽
	if self.quality_level == 0 then
		return SceneObjDetailLevel.High
	end

	local level = SceneObjDetailLevel.Low

	self.camera_position = Transform.GetPosition(camera_transform, self.camera_position)
	local topos = u3dpool.v3Sub(obj_pos, self.camera_position)

	self.camera_forward = Transform.GetForward(camera_transform, self.camera_forward)

	-- obj与camera的夹角不超过90度时
	local cos = u3dpool.v3Dot(topos, self.camera_forward)
	if cos > 0 then
		local distance = u3dpool.v3Length(topos, false)
		if distance <= HighDistance then
			level = SceneObjDetailLevel.High
		elseif distance <= MiddleDistance then
			level = SceneObjDetailLevel.Middle
		end
	end

	return level
end

function SceneObjLODManager:SetQualityLevel(level)
	self.quality_level = level
	self.check_cd = level == 0 and 2 or 0.2
end