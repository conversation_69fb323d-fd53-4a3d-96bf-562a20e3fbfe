-- 1小城池 2中城池 3大城池 4基地   seq 0开始 18及后面表示基地

HolyHeavenlyDomainWGData = HolyHeavenlyDomainWGData or BaseClass()

function HolyHeavenlyDomainWGData:__init()
	if HolyHeavenlyDomainWGData.Instance ~= nil then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[HolyHeavenlyDomainWGData] attempt to create singleton twice!")
		return
	end

	HolyHeavenlyDomainWGData.Instance = self

	local cfg = ConfigManager.Instance:GetAutoConfig("cross_divine_domain_auto")
	self.other_cfg = cfg.other[1]
	self.camp_cfg = cfg.camp
	self.city_cfg = cfg.city
	self.monster_cfg = ListToMap(cfg.monster, "city_seq", "seq")
	self.person_score_rank_reward_cfg = cfg.person_score_rank_reward
	self.server_score_rank_reward = cfg.server_score_rank_reward
	self.convert_cfg = cfg.convert
	self.score_reward_cfg = cfg.score_reward
	self.tired_hurt_reduce_cfg = cfg.tired_hurt_reduce
	self.hof_server_rank_reward_cfg = cfg.hof_server_rank_reward
	self.hof_person_rank_reward_cfg = cfg.hof_person_rank_reward
	self.worship_pos_cfg = cfg.worship_pos

	self:InitCache()
	self.convert_remind = 0
	self.convert_remind_cache = {}
	self.score_reward_remind = 0

	self.rmb_buy_time = 0
	self.score = 0
	self.power = 0
	self.last_recover_pwoer_time = 0
	self.convert_list = {}
	self.city_info = {}
	self.server_to_countryid = {}
	self.my_camp_id = -1
	self.score_reward_flag = {}
	self.capture_reward_flag = {}
	self.worship_flag = {}
	self.be_worship_times = 0
	self.city_concern_flag_list = {}

	-- 分配的房间ID，如果小于等于0，就是没有进行匹配  不能进入活动 是这个服没有进行匹配  无法进入活动
	self.room_id = -1
	self.assemble_city_seq = -1
	self.last_assemble_time = 0
	self.server_camp_list = {}
	self.city_owner_list = {}
	self.cur_score_rank_list = {} -- 全服个人积分排行榜 名人堂
	self.today_add_score_rank_list = {} -- 今日增长积分榜
	self.last_score_rank_list = {} -- 前一天结算积分榜
	self.monster_info = {}
	self.server_socre_list = {}        -- 当前积分榜   --名人堂
	self.server_today_socre_list = {}   -- 今日积分榜
	self.convene_city_seq = -1
	self.last_convene_time = 0
	self.camp_rank_player_list = {}
	self.battlefield_remind = 0
	self.battlefield_city_remind_cache = {}
	self.today_score = 0

	self.enter_fb_select_boss_index = 1
	self.enter_fb_select_boss_data = {}
	self.enter_fb_select_city_seq = 0

	RemindManager.Instance:Register(RemindName.HolyHeavenlyDomainRank, BindTool.Bind(self.GetRankRemind, self))
	RemindManager.Instance:Register(RemindName.HolyHeavenlyDomainBattlefield, BindTool.Bind(self.GetBattlefieldRemind, self))
	RemindManager.Instance:Register(RemindName.HolyHeavenlyDomainShop, BindTool.Bind(self.GetShopRemind, self))
	RemindManager.Instance:Register(RemindName.HolyHeavenlyDomainScoreReward, BindTool.Bind(self.GetScoreRewardRemind, self))
end

function HolyHeavenlyDomainWGData:InitCache()
	self.convert_item_cache = {}
	local convert_cfg = self:GetAllConvertCfg()

	for k, v in pairs(convert_cfg) do
		local cost_item = v.stuff_id_1
		if cost_item and not self.convert_item_cache[cost_item] then
			self.convert_item_cache[cost_item] = cost_item
		end
	end

	local city_near_cache = {}
	for k, v in pairs(self:GetMapCitiDataList()) do
		city_near_cache[v.seq] = Split(v.connect_city, "|")
	end

	self.city_near_cache = city_near_cache
end

function HolyHeavenlyDomainWGData:__delete()
	HolyHeavenlyDomainWGData.Instance = nil

	RemindManager.Instance:UnRegister(RemindName.HolyHeavenlyDomainDetails)
	RemindManager.Instance:UnRegister(RemindName.HolyHeavenlyDomainRank)
	RemindManager.Instance:UnRegister(RemindName.HolyHeavenlyDomainShop)
	RemindManager.Instance:UnRegister(RemindName.HolyHeavenlyDomainScoreReward)

	self.enter_fb_city_seq = nil
	self.enter_fb_select_boss_seq = nil
	self.enter_fb_select_boss_data = nil
end

----------------------------------remind_start--------------------------------------
function HolyHeavenlyDomainWGData:GetRankRemind()
	return 0
end

function HolyHeavenlyDomainWGData:GetBattlefieldRemind()
	return self.battlefield_remind or 0
end

function HolyHeavenlyDomainWGData:GetShopRemind()
	return self.convert_remind
end

function HolyHeavenlyDomainWGData:GetScoreRewardRemind()
	return self.score_reward_remind
end

function HolyHeavenlyDomainWGData:CalBattlefieldRemindCache()
	local battlefield_remind = 0
	local battlefield_city_remind_cache = {}
	local my_server = RoleWGData.Instance:GetOriginalUSIDStr()

	for k, v in pairs(self:GetMapCitiDataList()) do
		if v.type ~= 4 then
			local city_info = self:GetCityInfoBySeq(v.seq)
			local city_flag = 0

			if not IsEmptyTable(city_info) then
				if city_info.owner_usid == my_server then
					if self:GetCaptureRewardFlagBySeq(city_info.city_seq) == 0 then
						battlefield_remind = 1
						city_flag = 1
					end
				end
			end

			battlefield_city_remind_cache[v.seq] = city_flag
		end
	end

	self.battlefield_remind = battlefield_remind
	self.battlefield_city_remind_cache = battlefield_city_remind_cache
end

function HolyHeavenlyDomainWGData:GetDetailsRemind()
	-- 商城
	if self:GetShopRemind() == 1 then
		return 1
	end

	-- 战功
	if self:GetScoreRewardRemind() == 1 then
		return 1
	end

	-- 战场
	if self:GetBattlefieldRemind() == 1 then
		return 1
	end
	
	return 0
end

----------------------------------remind_end--------------------------------------

-- 赛季状态 休赛期显示休赛期 + 结束时间   战斗阶段显示 战斗 + 时间段
function HolyHeavenlyDomainWGData:GetDetailsSeasonAndTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local now_weekday = TimeUtil.FormatSecond3MYHM1(server_time)  --今日周几
	local other_cfg = self:GetOtherCfg()
	local status = ""
	local day_str = ""
	local day_diff = 0
	local one_day_time = 24 * 60 * 60

	local zero_time = TimeWGCtrl.Instance:NowDayTimeStart(server_time)
	local summary_day = other_cfg.summary_day

  	if now_weekday == summary_day then
		status = Language.HolyHeavenlyDomain.EndOfSeasonTime
		local target_time = zero_time + day_diff * one_day_time
		day_str = TimeUtil.FormatSecond2MY(target_time)
	else
		status = Language.HolyHeavenlyDomain.SeasonPreparationTime
		local end_day = summary_day == 0 and 7 or summary_day

		if now_weekday <= end_day then
			day_diff = end_day - now_weekday - 1
			day_diff = day_diff >= 0 and day_diff or 0
		else
			day_diff = end_day + 6 - now_weekday
		end

		local end_time =  zero_time + day_diff * one_day_time
		local end_str = TimeUtil.FormatSecond2MY(end_time)
		local start_str = 0

		local start_week = end_day + 1
		start_week = start_week % 8

		if now_weekday >= start_week then
			start_str = server_time - (now_weekday - start_week) * one_day_time
		else
			start_str = server_time - (now_weekday + 7 - start_week) * one_day_time
		end

		day_str = TimeUtil.FormatSecond2MY(start_str) .. "-" .. end_str
	end

	return status, day_str
end

-- 是否处于休赛期
function HolyHeavenlyDomainWGData:IsInTheOffSeason()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local now_weekday = TimeUtil.FormatSecond3MYHM1(server_time)
	local other_cfg = self:GetOtherCfg()
	local match_day = other_cfg.summary_day
	local end_day = match_day == 0 and 7 or match_day
	return now_weekday == end_day
end

function HolyHeavenlyDomainWGData:GetDetailsSeasonOpenTime()
	local other_cfg = self:GetOtherCfg()
	local open_time = other_cfg.open_time
	local close_time = other_cfg.close_time
	local end_open_time = math.floor(open_time % 100) == 0 and "00" or math.floor(open_time % 100)
	local end_close_time = math.floor(close_time % 100) == 0 and "00" or math.floor(close_time % 100)

	local today_start_time = TimeWGCtrl.Instance:NowDayTimeStart(TimeWGCtrl.Instance:GetServerTime())
	local open_time_value = today_start_time + math.floor(open_time /100) * 60 * 60 + open_time % 100 * 60
	local close_time_value = today_start_time + math.floor(close_time /100) * 60 * 60 + close_time % 100 * 60
	local open_time_str = math.floor(open_time /100) .. ":" .. end_open_time
	local close_time_str = math.floor(close_time /100) .. ":" .. end_close_time

	return open_time_value, close_time_value, open_time_str, close_time_str
end

function HolyHeavenlyDomainWGData:IsInActDuration()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local today_start_time = TimeWGCtrl.Instance:NowDayTimeStart(server_time)
	local other_cfg = self:GetOtherCfg()
	local open_time = other_cfg.open_time
	local close_time = other_cfg.close_time
	local open_time_value = today_start_time + math.floor(open_time /100) * 60 * 60 + open_time % 100 * 60
	local close_time_value = today_start_time + math.floor(close_time /100) * 60 * 60 + close_time % 100 * 60

	local state = server_time > open_time_value and server_time < close_time_value
	local open_time_str = TimeUtil.FormatHM(open_time_value)
	local slose_time_str = TimeUtil.FormatHM(close_time_value)
	return state, string.format(Language.HolyHeavenlyDomain.ActOpenTime, open_time_str, slose_time_str)
end

function HolyHeavenlyDomainWGData:GetHofEndTime()
	return TimeWGCtrl.Instance:GetServerTime() + 1000
end

-- 名人堂
function HolyHeavenlyDomainWGData:GetHofRankDataList()
	local my_server_data, total_my_data = {}, {}

	local uuid_str = RoleWGData.Instance:GetUUIDStr()

	-- 圣域排行榜
	local my_camp = self:GetMyCampSeq()
	local server_today_socre_list= self:GetNowServerScoreList()
	if not IsEmptyTable(server_today_socre_list) then
		for k, v in pairs(server_today_socre_list) do
			if v.camp_seq == my_camp then
				my_server_data = v
				break
			end
		end
	end

	-- 全服个人积分排行榜
	local total_player_score_rank_list = self:GetPlayerScoreRankInfoByType(CROSS_DIVINE_DOMAIN_PERSON_RANK_TYPE.SCORE_RANK)
	if not IsEmptyTable(total_player_score_rank_list) then
		for k, v in pairs(total_player_score_rank_list) do
			if v.uuid_str == uuid_str then
				total_my_data = v
				break
			end
		end
	end

	return server_today_socre_list, total_player_score_rank_list, my_server_data, total_my_data
end

-- 16个阵营的服Id
function HolyHeavenlyDomainWGData:GetCampCountryDataList()
	return self.server_camp_list
end

function HolyHeavenlyDomainWGData:GetCampServerIdByCampSeq(seq)
	return self.server_camp_list[seq]
end

-- 战况城池列表 1 占领  2丢失  3未攻下
function HolyHeavenlyDomainWGData:GetWarSituationCountryDataList()
	local country_data_list = {}
	local fatigue_value = 0
	-- local other_cfg = self:GetOtherCfg()
	--local my_sid = RoleWGData.Instance:GetOriginalUSIDStr()
	local my_server = RoleWGData.Instance:GetOriginalUSIDStr()
	local my_camp_id = self:GetMyCampSeq()

	for k, v in pairs(self:GetMapCitiDataList()) do
		if v.type ~= 4 then
			local city_info = self:GetCityInfoBySeq(v.seq)

			if not IsEmptyTable(city_info) then
				if city_info.owner_usid == my_server then
					fatigue_value = fatigue_value + v.capture_tired
					table.insert(country_data_list, {cfg = v, status = 1, camp = my_camp_id})
				elseif city_info.pre_owner_usid == my_server and city_info.owner_usid ~= my_server then
					local country_id = self:GetCountryIdByServerUsid(city_info.owner_usid)
					table.insert(country_data_list, {cfg = v, status = 2, camp = country_id})
				elseif city_info.pre_owner_usid ~= my_server and city_info.owner_usid ~= my_server then
					local pre_siege_info_list = city_info.pre_server_siege_list

					if not IsEmptyTable(pre_siege_info_list) then
						local siege = pre_siege_info_list[my_camp_id] or 0

						if siege > 0 then
							local country_id = self:GetCountryIdByServerUsid(city_info.owner_usid)
							table.insert(country_data_list, {cfg = v, status = 3, camp = country_id})
						end
					end
				end
			end
		else
			if my_camp_id >= 0 then
				if v.relegation_camp == my_camp_id then
					fatigue_value = fatigue_value + v.capture_tired
					table.insert(country_data_list, {cfg = v, status = 1, camp = my_camp_id})
				end
			end
		end
	end

	table.sort(country_data_list, SortTools.KeyLowerSorter("status", "camp"))
	local _, efficiency_str = self:GetSiegeEfficiencyByTired(fatigue_value)

	return country_data_list, fatigue_value, efficiency_str
end

function HolyHeavenlyDomainWGData:GetMapCitiDataList()
	return self.city_cfg
end

function HolyHeavenlyDomainWGData:GetMapXianLiValue()
	local other_cfg = self:GetOtherCfg()
	local privilege_time = self:GetPrivilegeBuyInfo()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
    local is_active_privilege = privilege_time > server_time
	local max_power = is_active_privilege and other_cfg.rmb_buy_max_power or other_cfg.base_max_power

	return self.power, max_power
end

function HolyHeavenlyDomainWGData:GetCityCfgByCitySeq(seq)
	return self.city_cfg[seq]
end

-- 特权时间 大于现在时间表示激活状态
function HolyHeavenlyDomainWGData:GetPrivilegeBuyInfo()
	return self.rmb_buy_time
end

function HolyHeavenlyDomainWGData:GetRankDataList()
	local today_person_rank_data = self:GetPlayerScoreRankInfoByType(CROSS_DIVINE_DOMAIN_PERSON_RANK_TYPE.TODAY_SCORE_RANK)
	local uuid_str = RoleWGData.Instance:GetUUIDStr()
	local my_person_rank_data = {}

	if not IsEmptyTable(today_person_rank_data) then
		for k, v in pairs(today_person_rank_data) do
			if v.uuid_str == uuid_str then
				my_person_rank_data = v
			end
		end
	end

	local server_score_list = self:GetServerTodayScoreList()
	local my_camp_rank_data = {}
	local my_camp_seq = self:GetMyCampSeq()

	if not IsEmptyTable(server_score_list) then
		for k, v in pairs(server_score_list) do
			if my_camp_seq == v.camp_seq then
				my_camp_rank_data = v
			end
		end
	end

	return today_person_rank_data, server_score_list, my_person_rank_data, my_camp_rank_data
end

function HolyHeavenlyDomainWGData:GetScore()
	return 	self.score
end

function HolyHeavenlyDomainWGData:GetCountryTipBossDataList(city_seq)
	local boss_data_list = {}

	for k, v in pairs(self.monster_cfg[city_seq]) do
		table.insert(boss_data_list, v)
	end
	
	table.sort(boss_data_list, SortTools.KeyUpperSorters("bg_type"))
	return boss_data_list
end

function HolyHeavenlyDomainWGData:GetCountryBossCfg(city_seq, boss_seq)
	return (self.monster_cfg[city_seq] or {})[boss_seq] or {}
end

function HolyHeavenlyDomainWGData:GetCountryTipRewardDataList(city_seq)
	return (self.city_cfg[city_seq] or {}).boss_reward_item or {}
end

function HolyHeavenlyDomainWGData:GetCountryTipAttentionFlag(city_seq, boss_seq)
	return self:GetCityConcernFlagBySeq(city_seq, boss_seq) == 1
end

function HolyHeavenlyDomainWGData:GetCountryMoBaiRoleDataList()
	local role_data_list = {}
	for i = 1, 3 do
		table.insert(role_data_list, {user_id = i<=2 and RoleWGData.Instance:InCrossGetOriginUid() or 0})
	end

	return role_data_list
end

function HolyHeavenlyDomainWGData:GetCampCfgByCamp(camp)
	return self.camp_cfg[camp]
end

function HolyHeavenlyDomainWGData:GetAllCampCfg()
	return self.camp_cfg
end

function HolyHeavenlyDomainWGData:GetOtherCfg()
	return self.other_cfg
end

function HolyHeavenlyDomainWGData:GetPersonScoreRankCfgByRank(rank)
	local rank_cfg = {}
	for k, v in pairs(self.person_score_rank_reward_cfg) do
		if rank >= v.min_rank and rank <= v.max_rank then
			return v
		end
	end

	return rank_cfg
end

function HolyHeavenlyDomainWGData:GetServerScoreRankCfgByRank(rank)
	local rank_cfg = {}
	for k, v in pairs(self.server_score_rank_reward) do
		if rank >= v.min_rank and rank <= v.max_rank then
			return v
		end
	end

	return rank_cfg
end

function HolyHeavenlyDomainWGData:GetHofServerScoreRankCfgByRank(rank)
	local rank_cfg = {}
	for k, v in pairs(self.hof_server_rank_reward_cfg) do
		if rank >= v.min_rank and rank <= v.max_rank then
			return v
		end
	end

	return rank_cfg
end

function HolyHeavenlyDomainWGData:GetHofPersonScoreRankCfgByRank(rank)
	local rank_cfg = {}
	for k, v in pairs(self.hof_person_rank_reward_cfg) do
		if rank >= v.min_rank and rank <= v.max_rank then
			return v
		end
	end

	return rank_cfg
end

function HolyHeavenlyDomainWGData:GetAllConvertCfg()
	return self.convert_cfg
end

-- 当前赛季结束时间戳
function HolyHeavenlyDomainWGData:GetActTimeEnd()
	if self:IsInTheOffSeason() then
		return 0
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local now_weekday = TimeUtil.FormatSecond3MYHM1(server_time)  --今日周几
	local other_cfg = self:GetOtherCfg()
	local zero_time = TimeWGCtrl.Instance:NowDayTimeStart(server_time) -- 今日0点的时间
	local summary_day = other_cfg.summary_day
	local end_day = summary_day == 0 and 7 or summary_day
	local day_diff = 0
	local one_day_time = 24 * 60 *60

	if now_weekday <= end_day then
		day_diff = end_day - now_weekday - 1
		day_diff = day_diff >= 0 and day_diff or 0
	else
		day_diff = end_day + 6 - now_weekday
	end

	return zero_time + day_diff * one_day_time
end

function HolyHeavenlyDomainWGData:IsConvertItem(change_item_id)
	return nil ~= self.convert_item_cache[change_item_id]
end

function HolyHeavenlyDomainWGData:CalConvertRemind()
	local convert_cfg = self:GetAllConvertCfg()
	local convert_remind = 0
	local convert_remind_cache = {}

	for k, v in pairs(convert_cfg) do
		-- 能否兑换
		local time = self:GetConvertTimeBySeq(v.seq)
		local is_times_limit = v.times_limit ~= 0
		local item_count = ItemWGData.Instance:GetItemNumInBagById(v.stuff_id_1)
		local has_enough_stuff = item_count >= v.stuff_num_1
		local time_enough = not is_times_limit or (is_times_limit and (time < v.times_limit))
		local can_exchange = has_enough_stuff and time_enough

		if can_exchange then
			convert_remind = 1
		end

		convert_remind_cache[v.seq] = can_exchange and 1 or 0
	end

	self.convert_remind = convert_remind
	self.convert_remind_cache = convert_remind_cache
end

function HolyHeavenlyDomainWGData:CalScoreRewardRemind()
	local score_reward_remind = 0
	local score = self:GetScore()
	local score_reward_cfg = self:GetScoreRewardCfg()
	local is_get = false

	for k, v in pairs(score_reward_cfg) do
		is_get = self:GetScoreRewardFlagBySeq(v.seq)
		
		if not is_get then
			if score >= v.need_score then
				score_reward_remind = 1
				break
			end
		end
	end

	self.score_reward_remind = score_reward_remind
end

function HolyHeavenlyDomainWGData:GetScoreRewardCfg()
	return self.score_reward_cfg
end

function HolyHeavenlyDomainWGData:GetScoreRewardFlagBySeq(seq)
	return self.score_reward_flag[seq] == 1
end

function HolyHeavenlyDomainWGData:GetConvertShowItemId()
	return self.other_cfg.convert_itemid
end

-- 已经兑换次数
function HolyHeavenlyDomainWGData:GetConvertTimeBySeq(seq)
	return self.convert_list[seq] or 0
end

function HolyHeavenlyDomainWGData:GetMapTopInfo()
	local big_citi_num, mid_city_num, small_city_num = 0, 0, 0
	local tired_value = 0
	-- local my_usid = RoleWGData.Instance:GetOriginalUSIDStr()
	local my_server = RoleWGData.Instance:GetOriginalUSIDStr()

	for k, v in pairs(self:GetMapCitiDataList()) do
		local info = self:GetCityInfoBySeq(v.seq)
		if not IsEmptyTable(info) then
			if info.owner_usid == my_server then
				if v.type == 1 then
					small_city_num = small_city_num + 1
				elseif v.type == 2 then
					mid_city_num = mid_city_num + 1
				elseif v.type == 3 then
					big_citi_num = big_citi_num + 1
				end
	
				tired_value = tired_value + v.capture_tired
			end
		end
	end

	local siege_efficiency, siege_efficiency_str, tired_decay_value, tired_decay_value_str = self:GetSiegeEfficiencyByTired(tired_value)
	return big_citi_num, mid_city_num, small_city_num, siege_efficiency, siege_efficiency_str, tired_decay_value, tired_decay_value_str
end

function HolyHeavenlyDomainWGData:GetSiegeEfficiencyByTired(tired)
	local efficiency = 0
	local tired_decay_value = 0
	local tired_decay_value_str = ""

	for k, v in pairs(self.tired_hurt_reduce_cfg) do
		if tired >= v.min_tired and tired <= v.max_tired then
			efficiency = 10000 - v.reduce_scale
			tired_decay_value = v.reduce_scale / 10000
			tired_decay_value_str = tired_decay_value * 100 .."%"
			break
		end
	end

	local efficiency_value = efficiency == 0 and 0 or efficiency / 10000
	local efficiency_str = (efficiency_value * 100) .. "%"

	return efficiency_value, efficiency_str, tired_decay_value, tired_decay_value_str
end

function HolyHeavenlyDomainWGData:GetCityInfoBySeq(city_seq)
	return self.city_info[city_seq] or {}
end

function HolyHeavenlyDomainWGData:GetMyCampSeq()
	return self.my_camp_id or -1
end

function HolyHeavenlyDomainWGData:GetCountryIdByServerUsid(usid)
	return self.server_to_countryid[usid]
end

function HolyHeavenlyDomainWGData:GetCaptureRewardFlagBySeq(city_seq)
	return self.capture_reward_flag[city_seq]
end

-- 城池能否设置目标
function HolyHeavenlyDomainWGData:IsCityCanSetTarget(city_seq, ignore_time)
	local status = false

	local other_cfg = self:GetOtherCfg()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local next_convene_time = self.last_convene_time + other_cfg.convene_cd

	if not ignore_time then
		if next_convene_time >= server_time then
			local time_str = TimeUtil.FormatTimeLanguage2(next_convene_time - server_time)
			return false, string.format(Language.HolyHeavenlyDomain.ConveneCityCd, time_str)
		end
	end

	local city_cfg = self:GetCityCfgByCitySeq(city_seq)

	if not IsEmptyTable(city_cfg) then
		local city_info = self:GetCityInfoBySeq(city_seq)

		if not IsEmptyTable(city_info) then
			local my_server = RoleWGData.Instance:GetOriginalUSIDStr()

			--基地不可设置成目标
			if city_cfg.type == 4 then
				return false, Language.HolyHeavenlyDomain.CanNotSetMainCityIsTarget
			end

			-- 已占领的
			if city_info.owner_usid == my_server then
				status = true
			else
				-- 已连线的
				-- local s = city_cfg.connect_city
                -- local t = Split(s, "|")
				local t = self:GetCityConnectCityCache(city_cfg.seq)

				for i = 1, #t do
                    local near_index = tonumber(t[i]) or 0
					local near_city_cfg = self:GetCityCfgByCitySeq(near_index)

					if not IsEmptyTable(near_city_cfg) then
						local is_main_city = near_city_cfg.type == 4

						if is_main_city then
							local camp = near_city_cfg.relegation_camp
							local server = self:GetCampServerIdByCampSeq(camp)
							if server == my_server then
								status = true
								break
							end
						else
							local near_city_info = self:GetCityInfoBySeq(near_index)

							if not IsEmptyTable(near_city_info) then
								if near_city_info.owner_usid == my_server then
									status = true
									break
								end
							end
						end
					end
                end
			end
		end
	end

	return status, Language.HolyHeavenlyDomain.SetTargetError
end

function HolyHeavenlyDomainWGData:GetPrivilegeAddValue()
	local add_str = 0 .. "%"
	local add_per = 0

    local server_time = TimeWGCtrl.Instance:GetServerTime()
	local peivilege_time = self:GetPrivilegeBuyInfo()

	if peivilege_time > server_time then
		local other_cfg = self:GetOtherCfg()
		add_per = other_cfg.rmb_buy_siege_value_addition / 10000
		add_str = add_per * 100 .. "%"
	end

	return add_per, add_str
end

-- 是否关注
function HolyHeavenlyDomainWGData:GetCityConcernFlagBySeq(city_seq, seq)
	return (self.city_concern_flag_list[city_seq] or {})[seq] or 0
end

function HolyHeavenlyDomainWGData:GetAssembleCityFlagBySeq(city_seq)
	return self.assemble_city_seq == city_seq
end

function HolyHeavenlyDomainWGData:IsAssembleCityCD()
	local other_cfg = self:GetOtherCfg()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local next_cd_time = self.last_assemble_time + other_cfg.assemble_cd
	local is_cd_time = next_cd_time >= server_time

	return is_cd_time, next_cd_time, next_cd_time - server_time
end

function HolyHeavenlyDomainWGData:GetCityOwerUsidByCitySeq(seq)
	return self.city_owner_list[seq] or -1
end

function HolyHeavenlyDomainWGData:GetCampCfgByServerId(usid)
	local camp_id = self:GetCountryIdByServerUsid(usid)
	return self:GetCampCfgByCamp(camp_id)
end

function HolyHeavenlyDomainWGData:GetCampRankPlayerListBySeq(seq)
	return self.camp_rank_player_list[seq] or {}
end

function HolyHeavenlyDomainWGData:IsConveneCity(city_seq)
	return self.convene_city_seq == city_seq
end

function HolyHeavenlyDomainWGData:IsSetConveneCity()
	return self.convene_city_seq >= 0
end

-- 是否匹配到了房间
function HolyHeavenlyDomainWGData:IsMateRoot()
	return self.room_id > 0
end

-- -- 活动开启条件
-- function HolyHeavenlyDomainWGData:IsActOpen()
-- 	local other_cfg = self:GetOtherCfg()
-- 	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

-- 	if other_cfg.open_day < open_day then
-- 		return false
-- 	end

-- 	local kf_world_level = BiZuoWGData.Instance and CrossServerWGData.Instance:GetServerMeanWorldLevel()

-- 	if other_cfg.open_world_level < kf_world_level then
-- 		return false
-- 	end

-- 	local role_level = RoleWGData.Instance:GetRoleLevel()
-- 	if role_level > other_cfg.open_level then
-- 		return false
-- 	end

-- 	return true
-- end

function HolyHeavenlyDomainWGData:GetMonsterCfgByCitySeq(city_seq)
	return self.monster_info[city_seq]
end

function HolyHeavenlyDomainWGData:GetMonsterCfgByCitySeqAndBossSeq(city_seq, boss_seq)
	return ((self.monster_info[city_seq] or {}).monster_info_list or {})[boss_seq] or {}
end

-- 今日积分排行
function HolyHeavenlyDomainWGData:GetServerTodayScoreList()
	return self.server_today_socre_list
end

function HolyHeavenlyDomainWGData:CanEnterMoBaiScene()
	-- 休赛期可以进入  考虑是否有人上榜
	local is_in_the_offseason = self:IsInTheOffSeason()
	local total_palyer_rank_list = self:GetPlayerScoreRankInfoByType(CROSS_DIVINE_DOMAIN_PERSON_RANK_TYPE.SCORE_RANK)

	return is_in_the_offseason and not IsEmptyTable(total_palyer_rank_list)
end

function HolyHeavenlyDomainWGData:GetNowServerScoreList()
	return self.server_socre_list
end

-- 获取膜拜对象雕像信息
function HolyHeavenlyDomainWGData:GetWorshipVoByIndex(index)
	local cur_score_rank_list = self:GetPlayerScoreRankInfoByType(CROSS_DIVINE_DOMAIN_PERSON_RANK_TYPE.SCORE_RANK)
	return (cur_score_rank_list or {})[index] or {}
end

function HolyHeavenlyDomainWGData:SetEnterFbCache(boss_index, city_seq, boss_data)
	self.enter_fb_select_boss_index = boss_index
	self.enter_fb_select_city_seq = city_seq
	self.enter_fb_select_boss_data = boss_data
end

function HolyHeavenlyDomainWGData:GetEnterFbCache()
	return self.enter_fb_select_boss_index
end

function HolyHeavenlyDomainWGData:GetEnterFbBossDataCache()
	return self.enter_fb_select_boss_data
end

function HolyHeavenlyDomainWGData:GetEnterFbCitySeq()
	return self.enter_fb_select_city_seq
end

function HolyHeavenlyDomainWGData:GetFbBossList()
	local boss_data_list = {}
	local city_seq = self:GetEnterFbCitySeq()
	local boss_info = self:GetMonsterCfgByCitySeq(city_seq)

	if not IsEmptyTable(boss_info) then
		local boss_info_list = boss_info.monster_info_list or {}

		if not IsEmptyTable(boss_info_list) then
			for k, v in pairs(boss_info_list) do
				local boss_cfg = self:GetCountryBossCfg(city_seq, v.seq)
				
				if not IsEmptyTable(boss_cfg) then
					table.insert(boss_data_list, {boss_cfg = boss_cfg, info = v})
				end
			end
		end
	end

	return boss_data_list
end

function HolyHeavenlyDomainWGData:GetMoBaiFbPlayerRankList()
	local data_list = {}

	for i = 1, 5 do
		local data = self:GetWorshipVoByIndex(i)
		if not IsEmptyTable(data) then
			table.insert(data_list, data)
		end
	end

	return data_list
end

function HolyHeavenlyDomainWGData:GetWorshipFlagByIndex(index)
	return self.worship_flag[index] == 1
end

function HolyHeavenlyDomainWGData:GetWorshipProgress()
	local task_pro, total_task_pro = 0, 0

	local data_list = self:GetMoBaiFbPlayerRankList()
	total_task_pro = #data_list

	for k, v in pairs(data_list) do
		if self:GetWorshipFlagByIndex(k) then
			task_pro = task_pro + 1
		end
	end

	return task_pro, total_task_pro
end

function HolyHeavenlyDomainWGData:GetWorShipCfgByIndex(index)
	return self.worship_pos_cfg[index]
end

function HolyHeavenlyDomainWGData:GetCurWorShipCfg()
	local worship_data = {}
	local data_list = self:GetMoBaiFbPlayerRankList()

	if IsEmptyTable(data_list) then
		return worship_data
	end

	for k, v in pairs(data_list) do
		-- 没有膜拜过
		local is_worship = self:GetWorshipFlagByIndex(k)

		if not is_worship then
			local data = {}
			data.cfg = self:GetWorShipCfgByIndex(k)
			data.info = v
			worship_data = data
			break
		end
	end

	return worship_data
end

-- 已经连线的 可以设置目标的
function HolyHeavenlyDomainWGData:GetSetTargetDataList()
	local data_list = {}
	local my_server = RoleWGData.Instance:GetOriginalUSIDStr()
	local seq_cache = {}

	for k, v in pairs(self:GetMapCitiDataList()) do
		local is_main_city = v.type == 4
		local need_check_near = false

		if is_main_city then
			local owner_usid = self:GetCampServerIdByCampSeq(v.relegation_camp)
			local is_my_city = my_server == owner_usid

			if is_my_city then
				need_check_near = true
			end
		else
			local info = self:GetCityInfoBySeq(v.seq)
			if info.owner_usid == my_server then
				if nil == seq_cache[v.seq] then
					table.insert(data_list, v)
					seq_cache[v.seq] = v.seq
				end

				need_check_near = true
			end
		end

		if need_check_near then
			-- local s = v.connect_city
			-- local t = Split(s, "|")
			local t = self:GetCityConnectCityCache(v.seq)

			--连线也可以设置为目标
			for i = 1, #t do
				local near_index = tonumber(t[i]) or 0
				local near_city_cfg = self:GetCityCfgByCitySeq(near_index)

				if near_city_cfg.type ~= 4 then
					if nil == seq_cache[near_city_cfg.seq] then
						seq_cache[near_city_cfg.seq] = near_city_cfg.seq
						table.insert(data_list, near_city_cfg)
					end
				end
			end
		end
	end

	-- 策划要求 城池类型3>2>1，城池同类型相同则归属非本服的优先
	local my_server = RoleWGData.Instance:GetOriginalUSIDStr()
	table.sort(data_list, function (a, b)
		if a.type == b.type then
			local a_city_info = self:GetCityInfoBySeq(a.seq)
			local b_city_info = self:GetCityInfoBySeq(b.seq)
			local owner_usid_a = a_city_info and a_city_info.owner_usid or -1
			local owner_usid_b = b_city_info and b_city_info.owner_usid or -1
			return owner_usid_b ~= my_server and owner_usid_a == my_server
		else
			return a.type > b.type
		end
	end)


	return data_list
end

function HolyHeavenlyDomainWGData:GetBossAngryAndScore(boss_vo)
	local angry = 0
	local score = 0
	local seq = -1
	local city_seq = -1

	if boss_vo.special_param > 0 then
		seq = boss_vo.special_param % 1000
		city_seq = math.floor(boss_vo.special_param / 1000) - 1
		local boss_cfg = self:GetCountryBossCfg(city_seq, seq)
		angry = boss_cfg and boss_cfg.need_power or 0
		score = boss_cfg and boss_cfg.kill_score or 0
	end

	return angry, score
end

function HolyHeavenlyDomainWGData:GetTodayScore()
	return self.today_score
end

function HolyHeavenlyDomainWGData:GetDetailShowItemList()
	return (self:GetOtherCfg() or {}).show_reward_item or {}
end

function HolyHeavenlyDomainWGData:GetWarSituationDescByfatigueValue(fatigue_value)
	local desc = ""

	for k, v in pairs(self.tired_hurt_reduce_cfg) do
		if fatigue_value >= v.min_tired and fatigue_value <= v.max_tired then
			return v.text
		end

		desc = v.text
	end

	return desc
end

function HolyHeavenlyDomainWGData:GetCityConnectCityCache(seq)
	return self.city_near_cache[seq]
end

----------------------------------protocol----------------------------------
function HolyHeavenlyDomainWGData:SetBaseInfo(protocol)
	self.rmb_buy_time = protocol.rmb_buy_time
	self.power = protocol.power
	self.last_recover_pwoer_time = protocol.last_recover_pwoer_time
	self.convert_list = protocol.convert_list                       -- 兑换次数信息 序号跟配置表匹配seq
	self.city_concern_flag_list = protocol.city_concern_flag_list   -- 关注标记 序号跟seq匹配
end

function HolyHeavenlyDomainWGData:SetRoomInfo(protocol)
	self.room_id = protocol.room_id
	self.assemble_city_seq = protocol.assemble_city_seq       --集结的城市Seq
	self.last_assemble_time = protocol.last_assemble_time      -- 上次集结时间
	self.server_camp_list = protocol.server_camp_list        -- 16个阵营 房间阵营 对应seq 的服Id
	self.city_owner_list = protocol.city_owner_list           -- 18个城市拥有者 服务器id

	local my_server = RoleWGData.Instance:GetOriginalUSIDStr()
	local server_to_countryid = {}
	for k, v in pairs(self.server_camp_list) do
		if v == my_server then
			self.my_camp_id = k
		end

		local plat, server = LLStrToInt(v)
		if plat ~= 0 or server ~= 0 then
			server_to_countryid[v] = k
		end
	end

	self.server_to_countryid = server_to_countryid
end

-- 18个城池信息  主城池无信息  可单独请求，不可直接赋值
function HolyHeavenlyDomainWGData:SetCityInfo(protocol)
	local city_info = {}
	for k, v in pairs(protocol.city_info_list) do
		city_info[v.city_seq] = v
	end

	self.city_info = city_info
end

-- 1,分别是全域个人积分排行（以前是当前积分榜 ）  2,今日增长积分榜 3,前一天的结算的榜 没用到
function HolyHeavenlyDomainWGData:SetPlayerScoreRankInfo(protocol)
	local type = protocol.type
	local rank_item_list = protocol.rank_item_list

	if type == CROSS_DIVINE_DOMAIN_PERSON_RANK_TYPE.SCORE_RANK then
		self.cur_score_rank_list = rank_item_list
	elseif type == CROSS_DIVINE_DOMAIN_PERSON_RANK_TYPE.TODAY_SCORE_RANK then
		self.today_add_score_rank_list = rank_item_list
	else
		self.last_score_rank_list = rank_item_list
	end
end

function HolyHeavenlyDomainWGData:GetPlayerScoreRankInfoByType(type)
	if type == CROSS_DIVINE_DOMAIN_PERSON_RANK_TYPE.SCORE_RANK then
		return self.cur_score_rank_list
	elseif type == CROSS_DIVINE_DOMAIN_PERSON_RANK_TYPE.TODAY_SCORE_RANK then
		return self.today_add_score_rank_list
	else
		return self.last_score_rank_list
	end
end

-- 怪物总信息 场景内主动推送
function HolyHeavenlyDomainWGData:SetMonsterInfo(protocol)
	local data = {}
	local city_seq = protocol.city_seq
	data.city_seq = city_seq
	data.player_num = protocol.player_num

	local monster_info_list = {}
	for k, v in pairs(protocol.monster_info_list) do
		monster_info_list[v.seq] = v
	end

	data.monster_info_list = monster_info_list
	self.monster_info[city_seq] = data
end

-- 更新怪物信息-- 场景外需要我主动请求
function HolyHeavenlyDomainWGData:UpdateMonsterInfo(protocol)
	local city_seq = protocol.city_seq
	local monster_info = protocol.monster_info
	
	local monster_info_list = (self.monster_info[city_seq] or {}).monster_info_list or {}
	if not IsEmptyTable(monster_info_list) then
		for k, v in pairs(monster_info_list) do
			if v.seq == monster_info.seq then
				self.monster_info[city_seq].monster_info_list[v.seq] = monster_info
				break
			end
		end
	end
end

-- 需要自己排名 16 个阵营积分排行
function HolyHeavenlyDomainWGData:SetServerScoreRank(protocol)
	local server_socre_list = {}
	local server_today_socre_list = {}

	local socre_list = protocol.server_socre_list    --名人堂
	if not IsEmptyTable(socre_list) then
		for k, v in pairs(socre_list) do
			if v > 0 then
				table.insert(server_socre_list, {camp_seq = k, score = v})
			end
		end

		table.sort(server_socre_list, SortTools.KeyUpperSorter("score"))-- 当前积分榜
		if not IsEmptyTable(server_socre_list) then
			for k, v in pairs(server_socre_list) do
				v.rank = k
			end
		end
	end

	self.server_socre_list = server_socre_list

	local today_socre_list = protocol.server_today_socre_list
	if not IsEmptyTable(today_socre_list) then
		for k, v in pairs(today_socre_list) do
			if v > 0 then
				table.insert(server_today_socre_list, {camp_seq = k, score = v})
			end
		end
	
		table.sort(server_today_socre_list, SortTools.KeyUpperSorter("score"))
		if not IsEmptyTable(server_today_socre_list) then
			for k, v in pairs(server_today_socre_list) do
				v.rank = k
			end
		end
	end

	self.server_today_socre_list = server_today_socre_list-- 今日积分榜
end

function HolyHeavenlyDomainWGData:SetBaseOtherInfo(protocol)
	self.score = protocol.score
	self.score_reward_flag = bit:d2b_l2h(protocol.score_reward_flag, nil, true)
	self.capture_reward_flag = bit:d2b_l2h(protocol.capture_reward_flag, nil, true)   --对应城池表 占城奖励领取标记
	self.worship_flag = bit:d2b_l2h(protocol.worship_flag, nil, true)    -- 膜拜标记   对应 0、1、2、3、4
	self.be_worship_times = protocol.be_worship_times
	self.convene_city_seq = protocol.convene_city_seq   --目标cityid
	self.last_convene_time = protocol.last_convene_time  -- 上次设置目标时间
	self.today_score = protocol.today_score
end

-- 阵营前三名
function HolyHeavenlyDomainWGData:SetCampInfo(protocol)
	local camp = protocol.camp
	local rank_item_list = protocol.rank_item_list
	local player_data_list = {}

	for k, v in pairs(rank_item_list) do
		if v.uuid.temp_low > 0 then
			table.insert(player_data_list, v)
		end
	end

	if not IsEmptyTable(player_data_list) then
		self.camp_rank_player_list[camp] = player_data_list
	end
end
