--武魂的最大魂阵索引8
--武魂的最大魂阵魂石索引9
--武魂的最大魂阵魂石刻印索引6

WuHunFrontWGData = WuHunFrontWGData or BaseClass()

function WuHunFrontWGData:__init()
	if WuHunFrontWGData.Instance ~= nil then
		error("[WuHunFrontWGData] attempt to create singleton twice!")
		return
	end

	WuHunFrontWGData.Instance = self

	self.front_gem_item_cfg = nil			--魂石物品表
    self.soul_stone_active_cfg = nil        --魂石激活物品表
	self.soul_stone_active_attr_cfg = nil        --魂石激活属性表
	self.soul_stone_star_attr_cfg = nil        --魂石星级属性表
	self.soul_stone_star_cfg = nil           --魂石升星配置表
	self.soul_stone_level_cfg = nil          --魂石升级配置表
	self.engrave_level_attr_cfg = nil        --刻印等级属性表
	self.engrave_attr_cfg = nil              --刻印属性
	self.soul_stone_level_attr_cfg = nil     --魂石等级属性表
	self.engrave_resonance_cfg = nil	     --刻印共鸣
	self.soul_stone_grade_cfg = nil          --魂石升阶配置
	self.soul_stone_grade_attr_cfg = nil     --魂石品阶属性
	self.engrave_level_cfg = nil             --刻印升级表
	self.new_engrave_level_up_cfg = nil
	self.soul_stone_grade_attr_min_cfg = nil     --魂石品阶属性加成
	self.gem_active_attr_list = nil           --魂石激活属性表 去除掉激活数量为索引

	self.baoshi_table = {}                   --宝石的换算表
	self.wuhun_front_gem_list = {}              --魂阵相关数据
	self.wuhun_bag_list = {}                  --武魂背包数据
	self.hunzhen_xingxiang_tab = {}           --魂阵形象

    self:InitFrontConfig()

	RemindManager.Instance:Register(RemindName.WuHunFront, BindTool.Bind(self.GetFrontWuHunRed,self))
end

function WuHunFrontWGData:__delete()
	WuHunFrontWGData.Instance = nil

	self.front_gem_item_cfg = nil
    self.soul_stone_active_cfg = nil
	self.soul_stone_active_attr_cfg = nil
	self.soul_stone_star_attr_cfg = nil
	self.soul_stone_star_cfg = nil
	self.soul_stone_level_cfg = nil
	self.engrave_level_attr_cfg = nil
	self.engrave_attr_cfg = nil
	self.soul_stone_level_attr_cfg = nil
	self.engrave_resonance_cfg = nil
	self.soul_stone_grade_cfg = nil
	self.soul_stone_grade_attr_cfg = nil
	self.engrave_level_cfg = nil
	self.new_engrave_level_up_cfg = nil
	self.soul_stone_grade_attr_min_cfg = nil
	self.soul_formation_appimage_cfg = nil

	self.baoshi_table = nil
	self.wuhun_front_gem_list = nil
	self.gem_active_attr_list = nil
	self.wuhun_bag_list = nil
	self.hunzhen_xingxiang_tab = nil

	RemindManager.Instance:UnRegister(RemindName.WuHunFront)
end

function WuHunFrontWGData:InitFrontConfig()
    --魂阵相关配置表
	local soul_cfg = ConfigManager.Instance:GetAutoConfig("hun_zhen_cfg_auto")
	if soul_cfg then
		self.front_gem_item_cfg = ListToMap(soul_cfg.soul_stone_active_item, "item_id")
		self.soul_stone_active_cfg = ListToMap(soul_cfg.soul_stone_active_item, "wuhun_id", "soul_front_seq", "soul_stone_seq")
		self.soul_stone_active_attr_cfg = ListToMap(soul_cfg.soul_stone_active_attr, "wuhun_id", "soul_front_seq", "active_num")
		self.soul_stone_star_attr_cfg = ListToMap(soul_cfg.soul_stone_star_attr, "wuhun_id", "soul_front_seq", "min_star")
		self.soul_stone_star_cfg = ListToMap(soul_cfg.soul_stone_star_cfg, "wuhun_id", "soul_front_seq", "soul_stone_seq", "star")
		self.soul_stone_level_cfg = ListToMap(soul_cfg.soul_stone_level_cfg, "level")
		self.engrave_level_attr_cfg = ListToMap(soul_cfg.engrave_level_attr, "min_level")
		self.engrave_attr_cfg = ListToMap(soul_cfg.engrave_attr, "item_id")
		self.soul_stone_level_attr_cfg = ListToMap(soul_cfg.soul_stone_level_attr, "wuhun_id", "soul_front_seq", "seq")
		self.engrave_resonance_cfg = ListToMap(soul_cfg.engrave_resonance, "wuhun_id", "soul_front_seq", "seq")
		self.soul_stone_grade_cfg = ListToMap(soul_cfg.soul_stone_grade_cfg, "wuhun_id", "soul_front_seq", "soul_stone_seq", "grade")
		self.soul_stone_grade_attr_cfg = ListToMap(soul_cfg.soul_stone_grade_attr, "wuhun_id", "soul_front_seq", "seq")
		self.engrave_level_cfg = ListToMap(soul_cfg.engrave_level, "old_item_id")
		self.new_engrave_level_up_cfg = ListToMap(soul_cfg.engrave_level, "new_item_id")
		self.soul_stone_grade_attr_min_cfg = ListToMap(soul_cfg.soul_stone_grade_attr_min, "min_level")
		self.soul_formation_appimage_cfg = ListToMap(soul_cfg.soul_formation_appimage, "wuhun_id", "soul_front_seq")

		for k_1, v_1 in pairs(soul_cfg.soul_formation_appimage) do
			local data = {}
			data.pos_t = Split(v_1.display_pos, "|")
			data.roa_t = Split(v_1.display_rotation, "|")
			data.display_scale = v_1.display_scale
			self.hunzhen_xingxiang_tab[v_1.wuhun_id] = self.hunzhen_xingxiang_tab[v_1.wuhun_id] or {}
			self.hunzhen_xingxiang_tab[v_1.wuhun_id][v_1.soul_front_seq]	= data
		end

		self.gem_active_attr_list = {}
		for k_i, k_v in ipairs(soul_cfg.soul_stone_active_attr) do
			self.gem_active_attr_list[k_v.wuhun_id] = self.gem_active_attr_list[k_v.wuhun_id] or {}
			self.gem_active_attr_list[k_v.wuhun_id][k_v.soul_front_seq] = self.gem_active_attr_list[k_v.wuhun_id][k_v.soul_front_seq] or {}
			table.insert(self.gem_active_attr_list[k_v.wuhun_id][k_v.soul_front_seq], k_v)
		end

		self.first_lev_stone_price = soul_cfg.engrave_level[1].price
		self:CtreBaoShiConversionTable(soul_cfg.engrave_level)
	end
end

--cfg当前配置  next_cfg下一级配置 add_prop加成百分比 策划说只会当前配置加成
function WuHunFrontWGData:GetAttrFunc(cfg, next_cfg, add_prop)
	local attr_data = {}

	for j = 1, 5 do
		local attr_key = cfg["attr_id" .. j]
		local attr_value = cfg["attr_value" .. j]

		if attr_key and attr_key > 0 and attr_value >= 0 then
			attr_data[attr_key] = attr_data[attr_key] or {}
			attr_data[attr_key].attr_str = attr_key
			attr_value = add_prop and (1 + add_prop / 100) * attr_value or attr_value
			attr_data[attr_key].attr_value = (attr_data[attr_key].attr_value or 0) +  math.floor(attr_value)

			if next_cfg then
				local next_attr_value = next_cfg["attr_value" .. j]
				next_attr_value = add_prop and (1 + add_prop / 100) * next_attr_value or next_attr_value
				attr_data[attr_key].add_value = math.ceil(next_attr_value) - attr_data[attr_key].attr_value
			end
		end
	end

	local attr_list = {}
	for k_1, v_1 in pairs(attr_data) do
		table.insert(attr_list, v_1)
	end
	table.sort(attr_list, SortTools.KeyLowerSorter("attr_str"))

	return attr_list
end

--得到属性是否万分比换算后的值
function WuHunFrontWGData:GetAttrChangeValue(attr_str, attr_value, space_name)
	local is_per = EquipmentWGData.Instance:GetAttrIsPer(attr_str)
	local attr_name = EquipmentWGData.Instance:GetAttrName(attr_str, space_name)
	if is_per then
		return (attr_value / 100) .. "%", attr_name
	else
		return attr_value, attr_name
	end
end

--通过武魂id获取相关数据
function WuHunFrontWGData:GetWuHunFrontDataByWuHunId(wuhun_id)
	return self.wuhun_front_gem_list[wuhun_id] or {}
end

--这里初始化把没用的数据去掉了
function WuHunFrontWGData:SetWuHunFrontData(protocol)
	local list = protocol.front_gem_list
	self.wuhun_front_gem_list = {}
	for wuhun_id, value in pairs(list) do
		local max_front_count = self:GetMaxFrontByWuhunId(wuhun_id)
		self.wuhun_front_gem_list[wuhun_id] = value

		for i = 0, COMMON_GAME_ENUM.EIGHT - 1 do
			local front_list = value.front_list[i]
			if max_front_count < i + 1 then
				self.wuhun_front_gem_list[wuhun_id].front_list[i] = nil
			else
				local max_gem_count = self:GetMaxFrontByWuhunIdFront(wuhun_id, front_list.wuhun_front_index)
				for j = max_gem_count, COMMON_GAME_ENUM.NINE - 1 do
					self.wuhun_front_gem_list[wuhun_id].front_list[i].gem_list[j] = nil
				end
			end
		end
	end
end

--刷新魂阵的属性信息
function WuHunFrontWGData:UpdateFrontPropertyData(protocol)
	for wuhun_id, value in pairs(self.wuhun_front_gem_list) do
		if value.wuhun_id == protocol.wuhun_id and value.front_list[protocol.wuhun_front_index] then
			local front_data = value.front_list[protocol.wuhun_front_index]
			front_data.gem_level_index = protocol.gem_level_index
			front_data.engrave_level_index = protocol.engrave_level_index
			front_data.gem_grade_index = protocol.gem_grade_index
			break
		end
	end
end

--刷新魂阵里面的魂石信息
function WuHunFrontWGData:UpdateFrontGemData(protocol)
	for k_1, v_1 in pairs(self.wuhun_front_gem_list) do
		local front_list = v_1.front_list[protocol.wuhun_front_index]
		if v_1.wuhun_id == protocol.wuhun_id and front_list then

			local gem_list = front_list.gem_list[protocol.front_gem_index]
			if gem_list then
				gem_list.front_gem_index = protocol.front_gem_index
				gem_list.gem_star = protocol.gem_star
				gem_list.gem_level = protocol.gem_level
				gem_list.gem_grade = protocol.gem_grade
				break
			end
		end
	end
end

--刷新魂阵的铭刻物品
function WuHunFrontWGData:UpdateFrontGemEngraveItem(protocol)
	for k_1, v_1 in pairs(self.wuhun_front_gem_list) do
		local front_list = v_1.front_list[protocol.wuhun_front_index]
		if v_1.wuhun_id == protocol.wuhun_id and front_list then

			local gem_list = front_list.gem_list[protocol.front_gem_index]
			if gem_list then
				gem_list.engrave_list = protocol.engrave_list
			end
		end
	end
end

--刷新魂阵的幻化信息
function WuHunFrontWGData:UpdateHuanHuaFrontIndex(protocol)
	if self.wuhun_front_gem_list[protocol.wuhun_id] then
		self.wuhun_front_gem_list[protocol.wuhun_id].front_use_index = protocol.front_use_index
	end
end

--通过武魂id 获取对应的幻化的魂阵
function WuHunFrontWGData:GetWuHunFrontHuanHuaIndex(wuhun_id)
	local wuhun_data = self:GetWuHunFrontDataByWuHunId(wuhun_id)
	if not IsEmptyTable(wuhun_data) then
		return wuhun_data.front_use_index
	end

	return COMMON_GAME_ENUM.FUYI
end

--得到一个魂阵的信息
function WuHunFrontWGData:GetFrontDataByFrontIndex(wuhun_id, front_index)
	for k_1, v_1 in pairs(self.wuhun_front_gem_list) do
		local front_list = v_1.front_list[front_index]
		if v_1.wuhun_id == wuhun_id and front_list then
			return front_list
		end
	end

	return {}
end

--得到一个魂阵下面的一个魂石信息
function WuHunFrontWGData:GetWuHunFrontGemData(wuhun_id, front_index, gem_index)
	for k_1, v_1 in pairs(self.wuhun_front_gem_list) do
		local front_list = v_1.front_list[front_index]
		if v_1.wuhun_id == wuhun_id and front_list then
			return front_list.gem_list[gem_index] or {}
		end
	end

	return {}
end

--得到一个魂石的全部刻印
function WuHunFrontWGData:GetGemAllEngraveData(wuhun_id, front_index, gem_index)
	local list = {}
	for k_1, v_1 in pairs(self.wuhun_front_gem_list) do
		local front_list = v_1.front_list[front_index]
		if v_1.wuhun_id == wuhun_id and front_list then

			local gem_list = front_list.gem_list[gem_index]
			if gem_list and gem_list.engrave_list then
				return gem_list.engrave_list
			end
		end
	end

	return list
end

--获得魂石的是否激活
function WuHunFrontWGData:GetFrontGemIsActiveState(wuhun_id, front_index, gem_index)
	local gem_data = self:GetWuHunFrontGemData(wuhun_id, front_index, gem_index)
	if not gem_data.gem_star or gem_data.gem_star == COMMON_GAME_ENUM.FUYI then
		return false
	end

	return true
end
-----------------------------------------------------------------配置表的获取     开始-----------------------------------------------------------


--通过武魂ID得到魂阵集合
function WuHunFrontWGData:GetFrontTabByWuHunId(wuhun_id)
	return self.soul_stone_active_cfg[wuhun_id] or {}
end

--得到武魂石的列表 通过武魂ID和魂阵索引
function WuHunFrontWGData:GetFrontGemTab(wuhun_id, front_index, gem_index)
	local cfg = (self.soul_stone_active_cfg[wuhun_id] or {})[front_index] or {}
	if gem_index then
		return cfg[gem_index] or {}
	end
	return cfg
end

--得到刻印的物品相关数据
function WuHunFrontWGData:GetEngraveCfgByItemId(item_id)
	return self.engrave_attr_cfg[item_id] or {}
end

--得到刻印升级的配置
function WuHunFrontWGData:GetEngraveLevelCfg(item_id)
	return self.engrave_level_cfg[item_id] or {}
end

--获得魂石的星级配置
function WuHunFrontWGData:GetFrontGemStarCfg(wuhun_id, front_index, gem_index, star)
	local gem_cfg = ((self.soul_stone_star_cfg[wuhun_id] or {})[front_index] or {})[gem_index]

    if star then
        return (gem_cfg or {})[star]
    end

    return gem_cfg
end

--获得魂石星级属性表数据 武魂ID 魂阵索引 最低星级
function WuHunFrontWGData:GetFrontGemStarAttrCfg(wuhun_id, front_index, min_star)
	return ((self.soul_stone_star_attr_cfg[wuhun_id] or {})[front_index] or {})[min_star]
end

--获得魂石激活属性表 武魂ID 魂阵索引 激活魂石数量
function WuHunFrontWGData:GetFrontGemActiveAttrCfg(wuhun_id, front_index, active_num)
	return ((self.soul_stone_active_attr_cfg[wuhun_id] or {})[front_index] or {})[active_num]
end

--获得魂石升级配置表的配置
function WuHunFrontWGData:GetFrontGemLevelCfgByLevel(gem_level)
	return self.soul_stone_level_cfg[gem_level]
end

--获得魂石的品阶表的配置
function WuHunFrontWGData:GetFrontGemGradeCfg(wuhun_id, front_index, gem_index, grade)
	return (((self.soul_stone_grade_cfg[wuhun_id] or {})[front_index] or {})[gem_index] or {})[grade]
end

--获得刻印等级属性表的配置
function WuHunFrontWGData:GetEngraveLevelAttrCfg(level)
	return self.engrave_level_attr_cfg[level]
end

--获得刻印共鸣的表
function WuHunFrontWGData:GetEngraveResonanceCfg(wuhun_id, front_index, level)
	local cfg = (self.engrave_resonance_cfg[wuhun_id] or {})[front_index]
	if level then
		return (cfg or {})[level]
	end

	return cfg
end

--获得魂石属性品阶的表
function WuHunFrontWGData:GetGemGradeAttrCfg(wuhun_id, front_index, level)
	local cfg = (self.soul_stone_grade_attr_cfg[wuhun_id] or {})[front_index]
	if level then
		return (cfg or {})[level]
	end

	return cfg
end

--获得魂石属性品阶加成表
function WuHunFrontWGData:GetGemGradeAttrAddCfg(level)
	return self.soul_stone_grade_attr_min_cfg[level]
end

--得到魂石等级属性表的相关数据
function WuHunFrontWGData:GetFrontGemLevelAttr(wuhun_id, front_index, index)
	local cfg = (self.soul_stone_level_attr_cfg[wuhun_id] or {})[front_index]
	if not index then
		return cfg
	end

	return (cfg or {})[index]
end

--通过一个物品ID判断是否是魂石
function WuHunFrontWGData:GetIsFrontGemByItemId(item_id)
	return self.front_gem_item_cfg[item_id]
end

-- 获取魂阵形象配置
function WuHunFrontWGData:GetSoulFormationAppimageCfg(wuhun_id, front_index)
	local emtry = {}
	return ((self.soul_formation_appimage_cfg or emtry)[wuhun_id] or emtry)[front_index] or nil
end

--获取魂阵配置的一些格式化配置
function WuHunFrontWGData:GetHunZhenXingXiangSpilt(wuhun_id, front_index)
	return (self.hunzhen_xingxiang_tab[wuhun_id] or {})[front_index]
end
-----------------------------------------------------------------配置表的获取     结束-----------------------------------------------------------

--获取魂阵中魂石的最小品阶
function WuHunFrontWGData:GetFrontMinGemGrade(wuhun_id, front_index)
	local front_data = self:GetFrontDataByFrontIndex(wuhun_id, front_index)
	local min_grade = COMMON_GAME_ENUM.MAX
	for k_i, k_v in pairs(front_data.gem_list or {}) do
		if k_v.gem_grade < min_grade then
			min_grade = k_v.gem_grade
		end
	end

	return min_grade
end

--获取魂阵中魂石的最小星级
function WuHunFrontWGData:GetFrontMinGemStar(wuhun_id, front_index)
	local front_data = self:GetFrontDataByFrontIndex(wuhun_id, front_index)
	local min_star = COMMON_GAME_ENUM.MAX
	for k_i, k_v in pairs(front_data.gem_list or {}) do
		if k_v.gem_star < min_star then
			min_star = k_v.gem_star
		end
	end

	min_star = min_star == COMMON_GAME_ENUM.MAX and COMMON_GAME_ENUM.FUYI or min_star
	return min_star
end

--得到魂阵中魂石的总等级
function WuHunFrontWGData:GetFrontGemLevelSum(wuhun_id, front_index)
	local front_list = self:GetFrontDataByFrontIndex(wuhun_id, front_index)
	local level_sum = 0
	if not IsEmptyTable(front_list) and front_list.gem_list then
		for k_i, k_v in pairs(front_list.gem_list) do
			level_sum = k_v.gem_level + level_sum
		end
	end

	return level_sum
end

--得到一个武魂下面魂阵的数量
function WuHunFrontWGData:GetMaxFrontByWuhunId(wuhun_id)
	local wuhun_data = self:GetFrontTabByWuHunId(wuhun_id)
	return GetTableLen(wuhun_data)
end

--得到对应类型的1级宝石数据
function WuHunFrontWGData:GetFristBaoshiCfgByType(engrave_type, level)
	local level = level or 1
	for k_1, v_1 in pairs(self.engrave_attr_cfg) do
		if engrave_type == v_1.type and level == v_1.level then
			return v_1
		end
	end
end

--得到配置一个武魂下面魂石的数量
function WuHunFrontWGData:GetMaxFrontByWuhunIdFront(wuhun_id, front_index)
	local front_data = self:GetFrontGemTab(wuhun_id, front_index)
	return GetTableLen(front_data)
end

function WuHunFrontWGData:GetMaxFrontActiveAttrNum(wuhun_id, front_index, active_num)
    local max_value = 0
    local max_has_active_cfg = {} --已经激活的孔位里面的最大配置
    local next_cfg = {}
	local has_max_count = 0

    if not self.gem_active_attr_list[wuhun_id] or not self.gem_active_attr_list[wuhun_id][front_index] then
        return max_value, max_has_active_cfg, next_cfg
    end

    local cfgs = self.gem_active_attr_list[wuhun_id][front_index]
    for k_1, v_1 in ipairs(cfgs) do
        if v_1.active_num > max_value then
            max_value = v_1.active_num
        end

        if active_num >= v_1.active_num and has_max_count <= v_1.active_num then
			has_max_count = v_1.active_num
            max_has_active_cfg = v_1
            if cfgs[k_1 + 1] then
                next_cfg = cfgs[k_1 + 1]
            end
        end
    end

	return max_value, max_has_active_cfg, next_cfg
end

--得到魂阵页面的激活升星属性 返回值属性列表  状态（1读取激活表 2读取星级属性） 数量（根据2状态来）
function WuHunFrontWGData:GetFrontGemActiveStarProperty(wuhun_id, front_index)
    local front_list = self:GetFrontDataByFrontIndex(wuhun_id, front_index)
    local gem_num = self:GetMaxFrontByWuhunIdFront(wuhun_id, front_index)
    local active_num = 0
	local min_star = self:GetFrontMinGemStar(wuhun_id, front_index)
	local star_num = 0 --超过最低星级数量
    for i = 0, gem_num - 1 do
		local gem_star = front_list.gem_list and front_list.gem_list[i] and front_list.gem_list[i].gem_star or COMMON_GAME_ENUM.FUYI
		active_num = gem_star == COMMON_GAME_ENUM.FUYI and active_num or active_num + 1

        star_num = gem_star > min_star and star_num + 1 or star_num
	end

    local attr_list = {}
    local max_active_num, max_active_cfg, next_cfg = self:GetMaxFrontActiveAttrNum(wuhun_id, front_index, active_num)
	local num_values = {}
    if max_active_num > active_num then --没激活够  读取激活表
		attr_list = self:GetAttrFunc(max_active_cfg, next_cfg)

		num_values[1] = active_num
		num_values[2] = next_cfg.active_num
    else
        min_star = min_star < 0 and 0 or min_star
        local cfg = self:GetFrontGemStarAttrCfg(wuhun_id, front_index, min_star)
        local next_cfg = self:GetFrontGemStarAttrCfg(wuhun_id, front_index, min_star + 1)
		attr_list = self:GetAttrFunc(cfg, next_cfg)

		local star_cfgs = self.soul_stone_star_attr_cfg[wuhun_id] and self.soul_stone_star_attr_cfg[wuhun_id][front_index] or {}
		local max_star = star_cfgs[GetTableLen(star_cfgs) - 1].min_star or 0
		num_values[1] = min_star
		num_values[2] = max_star
		num_values[3] = star_num
		num_values[4] = gem_num
    end

	return attr_list, max_active_num > active_num, num_values
end

--得到一个魂石里面的刻印镶嵌的信息 没有镶嵌是0
function WuHunFrontWGData:GetFrontGemEngraveItemId(wuhun_id, front_index, gem_index, engrave_index)
	for k_1, v_1 in pairs(self.wuhun_front_gem_list) do
		local front_list = v_1.front_list[front_index]
		if v_1.wuhun_id == wuhun_id and front_list then

			local gem_list = front_list.gem_list[gem_index]
			if gem_list and gem_list.engrave_list[engrave_index] then
				return gem_list.engrave_list[engrave_index]
			end
		end
	end

	return 0
end

--创建一个宝石的缓存数据
function WuHunFrontWGData:CtreBaoShiConversionTable(cfg)
	for i, k_v in ipairs(cfg) do
		local old_cfg = self.engrave_attr_cfg[k_v.old_item_id]
		if not old_cfg then
			ErrorLog("武魂刻印属性表中不存在值" .. k_v.old_item_id)
			return
		end

		self.baoshi_table[k_v.old_item_id] = {}
		--如果1级 直接在这里存
		if old_cfg.level <= 1 then
			self.baoshi_table[k_v.old_item_id].num = 0
			self.baoshi_table[k_v.old_item_id].price = 0
		else
			self.baoshi_table[k_v.old_item_id].num = k_v.price / self.first_lev_stone_price
			self.baoshi_table[k_v.old_item_id].price = k_v.price
		end

		if not self.engrave_level_cfg[k_v.new_item_id] then
			self.baoshi_table[k_v.new_item_id] = {}
			self.baoshi_table[k_v.new_item_id].num = k_v.price * k_v.need_num / self.first_lev_stone_price
			self.baoshi_table[k_v.new_item_id].price = k_v.price * k_v.need_num
		end
	end
end

--得到每级宝石需要的价值
function WuHunFrontWGData:GetBaoShiPriceByItemId(item_id)
	return self.baoshi_table[item_id] or {}
end

-- 在背包内的宝石转换成一级的总数 和 总价值
function WuHunFrontWGData:GetBaoShiConversionTableInBag(stone_type)
	local conversion_list = {}
	local need_num = 0
	local price = self.first_lev_stone_price

	local bag_list = self:GetAllTypeBaoShiByType(stone_type)
	for k, v in pairs(bag_list) do
		local baoshi_cfg = self:GetEngraveCfgByItemId(v.item_id)
        local cfg = self:GetBaoShiPriceByItemId(v.item_id)
        need_num = need_num + (cfg.num * v.num)

        if baoshi_cfg.level <= 1 then
            need_num = (need_num + 1 * v.num)
        end
	end

	conversion_list.num = need_num
	conversion_list.price = price * need_num
	return conversion_list
end

-- 获取升级宝石需要低级宝石描述
function WuHunFrontWGData:CalcUpgradeNeedStoneStr(old_item_id)
    if not old_item_id then
		return nil
	end

	local cfg = self:GetEngraveLevelCfg(old_item_id)
    if IsEmptyTable(cfg) then
        return nil
    end

    local new_stone_item_price = (cfg.price * cfg.need_num) - cfg.price		--新宝石价格
    local expend_tab = {}
    while cfg ~= nil do
        local have_count = WuHunFrontWGData.Instance:GetItemNumInBagById(old_item_id)
        if have_count > 0 then
            local need_count = new_stone_item_price / cfg.price
            local offest_count = have_count - need_count
            local add_count = offest_count > 0 and need_count or have_count
            if add_count > 0 then
                table.insert(expend_tab, {item_id = old_item_id, count = add_count})
            end

            if offest_count > 0 then
                break
            end
            new_stone_item_price = new_stone_item_price - cfg.price * have_count
        end

        local temp_cfg = self.new_engrave_level_up_cfg[old_item_id]
        if temp_cfg == nil then
            break
        end

        old_item_id = temp_cfg.old_item_id
        cfg = self:GetEngraveLevelCfg(old_item_id)
    end

    if IsEmptyTable(expend_tab) then
        return nil
    else
        local str = ""
        for i, v in ipairs(expend_tab) do
            local name = ItemWGData.Instance:GetItemName(v.item_id, nil, true)
            if i < #expend_tab then
                str = str .. name .. "*" .. v.count .. "，"
            else
                str = str .. name .. "*" .. v.count
            end
        end

        return str
    end
end

--根据宝石ID获取宝石属性
function WuHunFrontWGData:GetBaoShiNatrue(item_id)
	local stone_cfg = self:GetEngraveCfgByItemId(item_id)
	if nil == stone_cfg then
		return "", ""
	end

	local attr_num = GameEnum.EQUIP_BAOSHI_ATTR_NUM
	local name_str = ItemWGData.Instance:GetItemName(item_id, nil, true)
	local attr_str = ""

	for i = 1, attr_num do
		local type = stone_cfg["attr_id" .. i]
		local value = stone_cfg["attr_value" .. i]

		if value and value > 0 then
			local name = EquipmentWGData.Instance:GetAttrNameByAttrId(type, true)
			name = DeleteStrSpace(name)
			local is_per = EquipmentWGData.Instance:GetAttrIsPer(type)
			value = is_per and (value * 0.01 .. "%") or value
			local attr_desc = name .. " " .. ToColorStr(value, COLOR3B.GREEN)
			local huan_hang = 1 < i and "\n" or ""
			attr_str = attr_str .. huan_hang .. attr_desc
		end
	end

	return name_str, attr_str
end

--计算升级宝石花费的价格
function WuHunFrontWGData:ComputBaoShiUpgradePrice(old_item_id)
	if not old_item_id or old_item_id <= 0 then
		return COMMON_GAME_ENUM.MAX
	end

	local cfg = self:GetEngraveLevelCfg(old_item_id)

	--已经满级
	if cfg == nil then
		return COMMON_GAME_ENUM.MAX
	end

	--目标需要的换算数量
	local new_item_id = cfg.new_item_id 			          --新宝石id
	local new_conversion_list = self.baoshi_table[new_item_id] or {}
	if IsEmptyTable(new_conversion_list) then
		return COMMON_GAME_ENUM.MAX
	end

	--当前宝石孔的换算数量
	local cur_conversion_list = self.baoshi_table[old_item_id] or {}
	local cur_stone_cfg = self:GetEngraveCfgByItemId(old_item_id)
	local cur_num, cur_price = 0, 0

	cur_num = cur_conversion_list.num
	cur_price = cur_conversion_list.price
	
	--背包所有宝石的换算数量
	local bag_conversion_list = self:GetBaoShiConversionTableInBag(cur_stone_cfg.type)
	local new_stone_item_price = 0
	local have_price = cur_price + bag_conversion_list.price
	if new_conversion_list.price > have_price then
		new_stone_item_price = new_conversion_list.price - have_price
	end

	return new_stone_item_price
end

--获得魂石所有可替换的宝石 engrave_index 是1到6的孔位
function WuHunFrontWGData:GetAllFrontGemEngrave(wuhun_id, front_index, gem_index, engrave_index)
	if not wuhun_id or not front_index or not gem_index or not engrave_index then
		return {}
	end

	local list_data = {}
	local engrave_item = self:GetFrontGemEngraveItemId(wuhun_id, front_index, gem_index, engrave_index)

	local type_value = math.ceil(engrave_index / 2)
	for k_1, v_1 in pairs(self.wuhun_bag_list) do
		if v_1.engrave_type == type_value then
			local has_max_level = false
			if engrave_item > 0 then
				local cfg = self:GetEngraveCfgByItemId(engrave_item)
				if cfg and v_1.level > cfg.level then
					has_max_level = true
				end
			else
				has_max_level = true
			end

			if has_max_level then
				local data = {}
				data.level = v_1.level
				data.item_id = v_1.item_id
				data.stone_type = type_value
				data.index = v_1.index
				data.is_show_remind = true

				table.insert(list_data, data)
			end
		end
	end

	table.sort(list_data, function (a, b)
		if a.level == b.level then
			return a.item_id > b.item_id
		end
		return a.level > b.level
	end)

	return list_data
end

--获得武魂的列表
function WuHunFrontWGData:GetFrontWuHunListData(show_type, wuhun_id)
	local wuhun_list = WuHunWGData.Instance:GetAllWuhunlist()
	local list_data = {}
	local list_index = COMMON_GAME_ENUM.FUYI
	for k_i, k_v in ipairs(wuhun_list) do
		local data = {}
		data.wuhun_id = k_v.wuhun_id
		data.lock = k_v.lock
		data.wuhun_icon = k_v.wuhun_icon
		data.wh_name = k_v.wh_name
		data.show_red = self:GetFrontWuHunRedByWuHunId(k_v.wuhun_id, show_type)
		if k_v.wuhun_id == wuhun_id then
			list_index = k_i
		end
		table.insert(list_data, data)
	end

	return list_data, list_index
end

--通过武魂魂阵的列表
function WuHunFrontWGData:GetFrontListByWuHunId(wuhun_id, show_type)
	local tab_cfg = self:GetFrontTabByWuHunId(wuhun_id)
	if IsEmptyTable(tab_cfg) then
		return {}
	end

	local cfg_data = ListIndexFromZeroToOne(tab_cfg)
	local list_data = {}
	for k_1, v_1 in ipairs(cfg_data) do
		local data = {}
		local _, value = next(v_1)
		data.wuhun_id = value.wuhun_id
		data.front_index = value.soul_front_seq
		data.front_icon = value.front_icon
		data.show_red = self:GetWuHunFrontRed(wuhun_id, data.front_index, show_type)
		table.insert(list_data, data)
	end

	return list_data
end

--通过武魂ID和魂阵索引获得列表数据
function WuHunFrontWGData:GetFrontGemList(wuhun_id, front_index, show_type)
	local tab_cfg = self:GetFrontGemTab(wuhun_id, front_index)
	if IsEmptyTable(tab_cfg) then
		return {}
	end

	local datas = ListIndexFromZeroToOne(tab_cfg)
	local list_data = {}
	for k_i, k_v in ipairs(datas) do
		local gem_data = self:GetWuHunFrontGemData(wuhun_id, front_index, k_v.soul_stone_seq)
		local data = {}
		data.wuhun_id = k_v.wuhun_id
		data.front_index = k_v.soul_front_seq
		data.gem_index = k_v.soul_stone_seq
		data.item_id = k_v.item_id
		data.item_num = k_v.item_num
		data.front_icon = k_v.front_icon
		data.gem_icon = k_v.gem_icon
		data.gem_star = gem_data.gem_star or COMMON_GAME_ENUM.FUYI
		data.gem_level = gem_data.gem_level or 0
		data.gem_grade = gem_data.gem_grade or 0
		data.show_red = self:GetWuHunFrontGemRed(data.wuhun_id, data.front_index, data.gem_index, show_type)
		table.insert(list_data, data)
	end

	table.sort(list_data, SortTools.KeyLowerSorter("gem_index"))
	return list_data
end

--得到魂阵下面的刻印总等级
function WuHunFrontWGData:GetFrontEngraveSumLevel(wuhun_id, front_index)
	local front_data = WuHunFrontWGData.Instance:GetFrontDataByFrontIndex(wuhun_id, front_index)
 	local level_sum = 0
	for k_1, v_1 in pairs(front_data.gem_list or {}) do
		for k_2, v_2 in pairs(v_1.engrave_list) do
			local e_cfg = WuHunFrontWGData.Instance:GetEngraveCfgByItemId(v_2)
			if not IsEmptyTable(e_cfg) then
				level_sum = level_sum + e_cfg.level
			end
		end
	end

	return level_sum
end

--魂石一键镶嵌
function WuHunFrontWGData:GetGemOneKeyXiangQian(wuhun_id, front_index, gem_index)
	local gem_data = self:GetWuHunFrontGemData(wuhun_id, front_index, gem_index)
	local list = {}
	local engrave_list = gem_data.engrave_list or {}

	local get_level_func = function(item_id)
		local cfg = self:GetEngraveCfgByItemId(item_id)
		if not IsEmptyTable(cfg) then
			return cfg.level
		end
		return 0
	end
	local has_change = false
	for i = 1, 3 do
		local engrave_data = self:GetAllTypeBaoShiByType(i)
		local index1 = 2 * i - 1
		local index2 = 2 * i

		local equip_id1 = engrave_list[index1]
		local equip_id2 = engrave_list[index2]
		local max_id1 = engrave_data[1] and engrave_data[1].item_id or 0
		local max_id2 = engrave_data[2] and engrave_data[2].item_id or 0
		if engrave_data[1] and engrave_data[1].num > 1 then --数量大于1个 可以两个一样的
			max_id2 = engrave_data[1].item_id
		end

		local level_a = get_level_func(equip_id1)
		local level_b = get_level_func(equip_id2)
		local level_c = get_level_func(max_id1)
		local level_d = get_level_func(max_id2)

		local t_levels = {{level_a, equip_id1}, {level_b, equip_id2}, {level_c, max_id1}, {level_d, max_id2}}

		table.sort(t_levels, function (a, b)
			return a[1] > b[1]
		end)

		list[index1] = t_levels[1][2]
		list[index2] = t_levels[2][2]

		if not has_change then
			has_change = true
			if (list[index1] == equip_id1 and list[index2] == equip_id2) or (list[index1] == equip_id2 and list[index2] == equip_id1) then
				has_change = false
			end
		end
	end

	return list, has_change
end

--获得武魂魂阵的战力
function WuHunFrontWGData:GetWuHunFrontFight(wuhun_id, front_index)
	local attr_list = self:GetWuHunFrontAttrList(wuhun_id, front_index)
	local attribute = AttributePool.AllocAttribute()
	for id_num, v_1 in pairs(attr_list) do
		if id_num > 0 and v_1.attr_value > 0 then
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(id_num)
			if attribute[attr_str] then
				attribute[attr_str] = attribute[attr_str] + v_1.attr_value
			end
		end
	end
	return AttributeMgr.GetCapability(attribute)
end

--通过一个最低等级  获得最大的一组数据
function WuHunFrontWGData:GetMaxCanGradeAttrCfg(min_level)
	local now_level = COMMON_GAME_ENUM.FUYI
	local tab_cfg = {}
	local max_level = 0
	local next_level = COMMON_GAME_ENUM.MAX
	local max_cfg = {}
	local next_cfg = {}

	for k_1, v_1 in pairs(self.soul_stone_grade_attr_min_cfg) do
		if min_level >= v_1.min_level and now_level < v_1.min_level then
			now_level = v_1.min_level
			tab_cfg = v_1
		end

		if max_level <= v_1.min_level then
			max_level = v_1.min_level
			max_cfg = v_1
		end

		if v_1.min_level > min_level and next_level > v_1.min_level then
			next_level = v_1.min_level
			next_cfg = v_1
		end
	end

	return tab_cfg, max_cfg, next_cfg
end

--魂阵的战力计算
function WuHunFrontWGData:GetWuHunFrontAttrList(wuhun_id, front_index)
	local attr_list = {}
	if not self.soul_stone_active_attr_cfg[wuhun_id] or not self.soul_stone_active_attr_cfg[wuhun_id][front_index] or
		not self.soul_stone_star_cfg[wuhun_id] or not self.soul_stone_star_cfg[wuhun_id][front_index] or
		not self.soul_stone_level_attr_cfg[wuhun_id] or not self.soul_stone_level_attr_cfg[wuhun_id][front_index] or
		not self.engrave_resonance_cfg[wuhun_id] or not self.engrave_resonance_cfg[wuhun_id][front_index] or
		not self.soul_stone_grade_cfg[wuhun_id] or not self.soul_stone_grade_cfg[wuhun_id][front_index] or
		not self.soul_stone_grade_attr_cfg[wuhun_id] or not self.soul_stone_grade_attr_cfg[wuhun_id][front_index]
		then
		return attr_list
	end

	local wuhun_data = self:GetWuHunFrontDataByWuHunId(wuhun_id) --一个武魂下的数据
	if IsEmptyTable(wuhun_data) then
		return attr_list
	end

	local active_attr_cfg = self.soul_stone_active_attr_cfg[wuhun_id][front_index]
	local tab_v = {max_num = 0, active_num = 0, add_index = 0, min_active_star = COMMON_GAME_ENUM.MAX, min_gem_grade = COMMON_GAME_ENUM.MAX}
	local front_list = wuhun_data.front_list[front_index]
	local gem_num = self:GetMaxFrontByWuhunIdFront(wuhun_id, front_index)

	--首先判断取魂石激活属性  还是魂石星级属性  大于最大的孔位解锁  就是魂石星级属性
	for i = 0, gem_num - 1 do
		local gem_star = front_list.gem_list[i].gem_star
		tab_v.active_num = gem_star == COMMON_GAME_ENUM.FUYI and tab_v.active_num or tab_v.active_num + 1

		--这里直接遍历出魂石中最小的星级
		if tab_v.min_active_star > gem_star then
			tab_v.min_active_star = gem_star
		end

		if tab_v.min_gem_grade > front_list.gem_list[i].gem_grade then
			tab_v.min_gem_grade = front_list.gem_list[i].gem_grade
		end
	end

	local function add_func(cfg, add_pro)
		for j = 1, 5 do
			local attr_key = cfg["attr_id" .. j]
			local attr_value = cfg["attr_value" .. j]

			if attr_key and attr_value > 0 then
				attr_list[attr_key] = attr_list[attr_key] or {}

				--可能有百分比加成
				local attr_value = add_pro and attr_value * (1 + add_pro) or attr_value
				attr_list[attr_key].attr_value = (attr_list[attr_key].attr_value or 0) +  attr_value
			end
		end
	end

	local min_level_cfg = self:GetMaxCanGradeAttrCfg(tab_v.min_gem_grade)

	--魂石等级属性表 soul_stone_level_attr
	local gem_level_attr_cfg = self.soul_stone_level_attr_cfg[wuhun_id][front_index][front_list.gem_level_index]
	if gem_level_attr_cfg then
		add_func(gem_level_attr_cfg)
	end

	--刻印共鸣 engrave_resonance
	local resonance_cfg = self.engrave_resonance_cfg[wuhun_id][front_index][front_list.engrave_level_index]
	if resonance_cfg then
		add_func(resonance_cfg)
	end

	local keyin_add = min_level_cfg and min_level_cfg.add or 0--这里有个刻印属性百分比加成

	--魂石品阶属性 soul_stone_grade_attr
	local gem_grade_attr_cfg = self.soul_stone_grade_attr_cfg[wuhun_id][front_index][front_list.gem_grade_index]
	if gem_grade_attr_cfg then
		add_func(gem_grade_attr_cfg)
	end

	for i = 0, gem_num - 1 do
		local gem_star = front_list.gem_list[i].gem_star

		local a_count = i + 1
		if active_attr_cfg[a_count] then --这里是数量 不是索引
			tab_v.max_num = a_count
		    if tab_v.active_num >= a_count then
				tab_v.add_index = a_count
			end
		end

		--计算魂石下面的刻印的最小等级
		local min_engrave = COMMON_GAME_ENUM.MAX
		for m = 1, COMMON_GAME_ENUM.SIX do
			local engrave_list = front_list.gem_list[i].engrave_list or {}

			if engrave_list[m] then
				local engrave_at_cfg = self.engrave_attr_cfg[engrave_list[m]]
				if engrave_at_cfg then
					if min_engrave > engrave_list[m] then
						min_engrave = engrave_at_cfg.level
					end

					--刻印属性 战力加成 engrave_attr
					add_func(engrave_at_cfg, keyin_add)
				end
			end
		end

		--计算魂石升星表配置的战力加成  soul_stone_star_cfg
		local star_cfg = self:GetFrontGemStarCfg(wuhun_id, front_index, i, gem_star)
		if gem_star > COMMON_GAME_ENUM.FUYI and star_cfg then
			add_func(star_cfg)
		end

		--魂石升级表  soul_stone_level_cfg
		local gem_level = front_list.gem_list[i].gem_level
		local level_cfg = self.soul_stone_level_cfg[gem_level]
		local engrave_cfg = self.engrave_level_attr_cfg[min_engrave]
		if level_cfg and engrave_cfg then
			add_func(level_cfg, engrave_cfg.add)
		end

		--魂石升阶表 soul_stone_grade_cfg
		local gem_grade = front_list.gem_list[i].gem_grade
		if self.soul_stone_grade_cfg[wuhun_id][front_index][i] and self.soul_stone_grade_cfg[wuhun_id][front_index][i][gem_grade] then
			local gem_grade_cfg = self.soul_stone_grade_cfg[wuhun_id][front_index][i][gem_grade]
			if gem_grade_cfg then
				add_func(gem_grade_cfg)
			end
		end
	end

	--激活魂石数量达到最大的激活  读取魂石星级属性表对应属性 soul_stone_star_attr
	if tab_v.active_num >= tab_v.max_num then
		tab_v.min_active_star = tab_v.min_active_star == COMMON_GAME_ENUM.FUYI and 0 or tab_v.min_active_star
		local star_attr_cfg = self:GetFrontGemStarAttrCfg(wuhun_id, front_index, tab_v.min_active_star)
		if star_attr_cfg then
			add_func(star_attr_cfg)
		end
	else --未达到解锁的最大孔位  读取对应孔位数据  soul_stone_active_attr
		local attr_cfg = self:GetFrontGemActiveAttrCfg(wuhun_id, front_index, tab_v.add_index)
		if attr_cfg then
			add_func(attr_cfg)
		end
	end

	return attr_list
end


--武魂背包界面
function WuHunFrontWGData:SetAllWuHunBagData(protocol)
	self.wuhun_bag_list = protocol.grid_list

	for k_1, v_1 in pairs(self.wuhun_bag_list) do
		WuHunWGCtrl.Instance:WuHunKeyUseView(v_1.item_id, v_1.index)
	end
end

--刷新武魂的背包
function WuHunFrontWGData:UpdateWuHunBagData(protocol)
	local new_data = protocol.grid_info
	local index = new_data.index
	local item_id = new_data.item_id
	local old_data = self.wuhun_bag_list[index]
	local old_num = old_data and old_data.num or 0
	local new_num = new_data.num

	local change_reason = GameEnum.DATALIST_CHANGE_REASON_UPDATE
	if (old_data == nil or old_data.item_id == nil or old_data.item_id <= 0) and new_num > old_num then
		change_reason = GameEnum.DATALIST_CHANGE_REASON_ADD
	elseif old_data and old_data.item_id > 0 and new_num < old_num then
		change_reason = GameEnum.DATALIST_CHANGE_REASON_REMOVE
		if new_data.item_id <= 0 then
			item_id = old_data.item_id
			new_data = nil
		end
	end

	self.wuhun_bag_list[index] = new_data
	WuHunWGCtrl.Instance:OnWuHunItemDataChange(item_id, index, change_reason, old_num, new_num)
end

--得到同类型的所有宝石数据
function WuHunFrontWGData:GetAllTypeBaoShiByType(engrave_type)
	local data = {}
	for k_1, v_1 in pairs(self.wuhun_bag_list) do
		if v_1.engrave_type == engrave_type and not IsEmptyTable(self:GetEngraveCfgByItemId(v_1.item_id)) then
			table.insert(data, v_1)
		end
	end

	if not IsEmptyTable(data) then
        table.sort(data, function (a, b)
			if a.level == b.level then
				return a.item_id > b.item_id
			end

			return a.level > b.level
		end)
    end

	return data
end

--通过物品ID获取对应的数量
function WuHunFrontWGData:GetItemNumInBagById(item_id)
	for key, value in pairs(self.wuhun_bag_list) do
		if value.item_id == item_id then
			return value.num
		end
	end

	return 0
end

--获得一个物品的数据
function WuHunFrontWGData:GetItemDataByItemId(item_id)
	for key, value in pairs(self.wuhun_bag_list) do
		if value.item_id == item_id then
			return value
		end
	end
end

--获得一个宝石的等级
function WuHunFrontWGData:GetBaoShiLevelByItemId(item_id)
 	local data = self:GetItemDataByItemId(item_id)
	if data then
		return data.level
	end
	return 0
end

-- 格子信息
function WuHunFrontWGData:GetWuHunBagItem(index)
	return self.wuhun_bag_list[index]
end




------------------------------------------红点 开始 -----------------------------------------
--给的武魂页签下是否有红点 所有的武魂
function WuHunFrontWGData:GetFrontWuHunRed()
	local wuhun_list = self:GetFrontWuHunListData()
	for k_i, k_v in ipairs(wuhun_list) do
		if self:GetFrontWuHunRedByWuHunId(k_v.wuhun_id) then
			return 1
		end
	end

	return 0
end

--获得一个武魂的红点信息
function WuHunFrontWGData:GetFrontWuHunRedByWuHunId(wuhun_id, show_type)
	if not WuHunWGData.Instance:GetWuHunIsActive(wuhun_id) then --武魂未激活
		return false
	end

	local front_list = self:GetFrontListByWuHunId(wuhun_id)
	for k_i, k_v in ipairs(front_list) do
		local red_show = self:GetWuHunFrontRed(wuhun_id, k_v.front_index, show_type)
		if red_show then
			return true
		end
	end

	return false
end

--判断一个魂阵是否显示红点
function WuHunFrontWGData:GetWuHunFrontRed(wuhun_id, front_index, show_type)
	if not WuHunWGData.Instance:GetWuHunIsActive(wuhun_id) then --武魂未激活
		return false
	end

	local front_hunshi_list = self:GetFrontGemTab(wuhun_id, front_index)
	for k_i, v_1 in pairs(front_hunshi_list) do
		local red_show = self:GetWuHunFrontGemRed(wuhun_id, front_index, v_1.soul_stone_seq, show_type)
		if red_show then
			return true
		end
	end

	local mingke_red = self:GetWuHunFrontMingKeRed(wuhun_id, front_index)
	if mingke_red then
		return true
	end

	return false
end

--判断一个魂石是否显示红点 show_type 为1表示主界面  2位刻印和叠阵界面
function WuHunFrontWGData:GetWuHunFrontGemRed(wuhun_id, front_index, gem_index, show_type)
	if not WuHunWGData.Instance:GetWuHunIsActive(wuhun_id) then
		return false
	end

	if not show_type or show_type == 1 then
		--升星红点
		if self:GetFrontGemStarUpRed(wuhun_id, front_index, gem_index) then
			return true
		end

		--升级按钮红点
		if self:GetFrontGemLevelUpRed(wuhun_id, front_index, gem_index) then
			return true
		end
	end

	if not show_type or show_type == 2 then
		--刻印红点
		if self:GetWuHunFrontGemKeYinRed(wuhun_id, front_index, gem_index) then
			return true
		end

		--叠阵红点
		if self:GetWuHunFrontGemDieZhenRed(wuhun_id, front_index, gem_index) then
			return true
		end
	end

	return false
end

--铭刻按钮的红点
function WuHunFrontWGData:GetWuHunFrontMingKeRed(wuhun_id, front_index)
	if not WuHunWGData.Instance:GetWuHunIsActive(wuhun_id) then
		return false
	end

	local front_hunshi_list = self:GetFrontGemTab(wuhun_id, front_index)
	for k_i, v_1 in pairs(front_hunshi_list) do
		local gem_index = v_1.soul_stone_seq
		if self:GetFrontGemIsActiveState(wuhun_id, front_index, gem_index) then --已经激活的魂石
			local keyin_red = self:GetWuHunFrontGemKeYinRed(wuhun_id, front_index, gem_index)
			if keyin_red then
				return true
			end

			local diezhen_red = self:GetWuHunFrontGemDieZhenRed(wuhun_id, front_index, gem_index)
			if diezhen_red then
				return true
			end
		end
	end

	return false
end

--判断一个魂石的刻印红点
function WuHunFrontWGData:GetWuHunFrontGemKeYinRed(wuhun_id, front_index, gem_index)
	if not WuHunWGData.Instance:GetWuHunIsActive(wuhun_id) or not self:GetFrontGemIsActiveState(wuhun_id, front_index, gem_index) then
		return false
	end

	--宝石有红点
	if self:GetFrontGemKeYinOneKeyRed(wuhun_id, front_index, gem_index) then
		return true
	end

	--刻印共鸣有红点
	if self:GetFrontGemGongMingRed(wuhun_id, front_index, gem_index) then
		return true
	end

	return false
end

--判断一个魂石的叠阵红点
function WuHunFrontWGData:GetWuHunFrontGemDieZhenRed(wuhun_id, front_index, gem_index)
	if self:GetWuHunFrontGemDieZhenBtnCostRed(wuhun_id, front_index, gem_index) then
		return true
	end

	if self:GetFrontGemGradeAddPropRed(wuhun_id, front_index, gem_index) then
		return true
	end

	return false
end

--魂石的升星红点
function WuHunFrontWGData:GetFrontGemStarUpRed(wuhun_id, front_index, gem_index)
	if not WuHunWGData.Instance:GetWuHunIsActive(wuhun_id) then
		return false
	end

	local gem_data = self:GetWuHunFrontGemData(wuhun_id, front_index, gem_index)
	local gem_star = (not gem_data.gem_star or gem_data.gem_star == COMMON_GAME_ENUM.FUYI) and 0 or gem_data.gem_star ---1星级没有配置表

	--升星红点
	local next_cfg = self:GetFrontGemStarCfg(wuhun_id, front_index, gem_index, gem_star + 1)
	local star_cfg = self:GetFrontGemStarCfg(wuhun_id, front_index, gem_index, gem_star)

	if next_cfg and star_cfg then
		local num = self:GetItemNumInBagById(star_cfg.item_id)
		if num >= star_cfg.item_num then
			return true
		end
	end

	return false
end

--魂石的升级红点
function WuHunFrontWGData:GetFrontGemLevelUpRed(wuhun_id, front_index, gem_index)
	if self:GetFrontGemLevelUpCostRed(wuhun_id, front_index, gem_index) then
		return true
	end

	if self:GetFrontGemLevelUpAddPropRed(wuhun_id, front_index, gem_index) then
		return true
	end

	return false
end

--魂石的升级消耗材料红点
function WuHunFrontWGData:GetFrontGemLevelUpCostRed(wuhun_id, front_index, gem_index)
	if not WuHunWGData.Instance:GetWuHunIsActive(wuhun_id) or not self:GetFrontGemIsActiveState(wuhun_id, front_index, gem_index) then
		return false
	end

	local gem_data = self:GetWuHunFrontGemData(wuhun_id, front_index, gem_index)
	local gem_level = gem_data.gem_level or 0
	local level_cfg = self:GetFrontGemLevelCfgByLevel(gem_level)
	local next_level_cfg = self:GetFrontGemLevelCfgByLevel(gem_level + 1)

	if level_cfg and next_level_cfg then
		local role_coin = RoleWGData.Instance.role_info.coin or 0
		local num = self:GetItemNumInBagById(level_cfg.item_id)
		if role_coin >= level_cfg.coin_num and num >= level_cfg.item_num then --材料不足
			return true
		end
	end

	return false
end

--魂石升级里面升级加成红点
function WuHunFrontWGData:GetFrontGemLevelUpAddPropRed(wuhun_id, front_index, gem_index)
	if not WuHunWGData.Instance:GetWuHunIsActive(wuhun_id) or not self:GetFrontGemIsActiveState(wuhun_id, front_index, gem_index) then
		return false
	end

	local front_data = self:GetFrontDataByFrontIndex(wuhun_id, front_index)
	local gem_level_index = front_data.gem_level_index
	local next_cfg = self:GetFrontGemLevelAttr(wuhun_id, front_index, gem_level_index + 1)
	if next_cfg then
		local level_sum = self:GetFrontGemLevelSum(wuhun_id, front_index)
		if level_sum >= next_cfg.level_total then
			return true
		end
	end

	return false
end

--魂石里面品阶按钮消耗材料的红点
function WuHunFrontWGData:GetWuHunFrontGemDieZhenBtnCostRed(wuhun_id, front_index, gem_index)
	if not WuHunWGData.Instance:GetWuHunIsActive(wuhun_id) or not self:GetFrontGemIsActiveState(wuhun_id, front_index, gem_index) then
		return false
	end

	local gem_data = WuHunFrontWGData.Instance:GetWuHunFrontGemData(wuhun_id, front_index, gem_index)
    local grade_cfg = WuHunFrontWGData.Instance:GetFrontGemGradeCfg(wuhun_id, front_index, gem_index, gem_data.gem_grade)
	local next_grade_cfg = WuHunFrontWGData.Instance:GetFrontGemGradeCfg(wuhun_id, front_index, gem_index, gem_data.gem_grade + 1)

	if next_grade_cfg and grade_cfg then
		local num = WuHunFrontWGData.Instance:GetItemNumInBagById(grade_cfg.item_id)
		if num >= grade_cfg.item_num then
			return true
		end
	end

	return false
end

--魂石里面品阶加成的红点
function WuHunFrontWGData:GetFrontGemGradeAddPropRed(wuhun_id, front_index, gem_index)
	if not WuHunWGData.Instance:GetWuHunIsActive(wuhun_id) or not self:GetFrontGemIsActiveState(wuhun_id, front_index, gem_index) then
		return false
	end

	local front_data = WuHunFrontWGData.Instance:GetFrontDataByFrontIndex(wuhun_id, front_index)
	local gem_grade_index = front_data.gem_grade_index
	local next_cfg = WuHunFrontWGData.Instance:GetGemGradeAttrCfg(wuhun_id, front_index, gem_grade_index + 1)

	if next_cfg then
		local can_show = true
		for k_i, k_v in pairs(front_data.gem_list) do
			if k_v.gem_grade < next_cfg.min_grade then
				can_show = false
				break
			end
		end

		if can_show then
			return true
		end
	end

	return false
end

--魂石刻印里面的一键镶刻
function WuHunFrontWGData:GetFrontGemKeYinOneKeyRed(wuhun_id, front_index, gem_index)
	if not WuHunWGData.Instance:GetWuHunIsActive(wuhun_id) or not self:GetFrontGemIsActiveState(wuhun_id, front_index, gem_index) then
		return false
	end

	local gem_data = self:GetWuHunFrontGemData(wuhun_id, front_index, gem_index)
	for i = 1, 6 do
		local list = self:GetAllFrontGemEngrave(wuhun_id, front_index, gem_index, i)
		if not IsEmptyTable(list) then
			return true
		end

		local engrave_item = gem_data.engrave_list and gem_data.engrave_list[i] or 0
		local upgrade_cost = WuHunFrontWGData.Instance:ComputBaoShiUpgradePrice(engrave_item)
		if upgrade_cost <= 0 then
			return true
		end
	end

	return false
end

--魂石刻印里面的宝石 共鸣加成
function WuHunFrontWGData:GetFrontGemGongMingRed(wuhun_id, front_index)
	if not WuHunWGData.Instance:GetWuHunIsActive(wuhun_id) then
		return false
	end

	local front_data = WuHunFrontWGData.Instance:GetFrontDataByFrontIndex(wuhun_id, front_index)
	local engrave_level_index = front_data.engrave_level_index
	local next_cfg = WuHunFrontWGData.Instance:GetEngraveResonanceCfg(wuhun_id, front_index, engrave_level_index + 1)

	if next_cfg then
		local level_sum = self:GetFrontEngraveSumLevel(wuhun_id, front_index)

		if level_sum >= next_cfg.level_total then
			return true
		end
	end

	return false
end
------------------------------------------红点 结束 -----------------------------------------