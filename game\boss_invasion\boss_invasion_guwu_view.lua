-- 鼓舞界面
BOSSInvasionGuWuView = BOSSInvasionGuWuView or BaseClass(SafeBaseView)

function BOSSInvasionGuWuView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(812, 574)})
	self:AddViewResource(0, "uis/view/boss_invasion_ui_prefab", "layout_boss_invasion_guwu_view")
end

function BOSSInvasionGuWuView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.BOSSInvasion.GuWuViewTitleName

	XUI.AddClickEventListener(self.node_list.btn_guwu_coin, BindTool.Bind(self.OnClickGuWu, self, 0))
	XUI.AddClickEventListener(self.node_list.btn_guwu_gold, BindTool.Bind(self.OnClickGuWu, self, 1))
	XUI.AddClickEventListener(self.node_list.btn_hook1, BindTool.Bind(self.OnClickHook1, self))
	XUI.AddClickEventListener(self.node_list.btn_hook2, BindTool.Bind(self.OnClickHook2, self))

	self.is_tongbi = BOSSInvasionWGData.Instance:GetAutoGuwuFlag(CROSS_BOSS_STRIKE_GUWU_TYPE.GUWU_TYPE_COIN)
	self.is_gold = BOSSInvasionWGData.Instance:GetAutoGuwuFlag(CROSS_BOSS_STRIKE_GUWU_TYPE.GUWU_TYPE_GOLD)
	self.node_list.hook1:SetActive(self.is_tongbi)
	self.node_list.hook2:SetActive(self.is_gold)
end

function BOSSInvasionGuWuView:OnClickHook1()
	BOSSInvasionWGCtrl.Instance:SendBOSSInvasionReq(CROSS_BOSS_STRIKE_OPERATE_TYPE.SET_GUWU_FLAG, CROSS_BOSS_STRIKE_GUWU_TYPE.GUWU_TYPE_COIN)
	self.is_tongbi = not self.is_tongbi
	self.node_list.hook1:SetActive(self.is_tongbi)
end

function BOSSInvasionGuWuView:OnClickHook2()
	BOSSInvasionWGCtrl.Instance:SendBOSSInvasionReq(CROSS_BOSS_STRIKE_OPERATE_TYPE.SET_GUWU_FLAG, CROSS_BOSS_STRIKE_GUWU_TYPE.GUWU_TYPE_GOLD)
	self.is_gold = not self.is_gold
	self.node_list.hook2:SetActive(self.is_gold)
end

function BOSSInvasionGuWuView:OnClickGuWu(guwu_type)
	local cur_guwu_cfg = BOSSInvasionWGData.Instance:GetCurBossCfg()

	if guwu_type == CROSS_BOSS_STRIKE_GUWU_TYPE.GUWU_TYPE_COIN then
		local role_coin = RoleWGData.Instance.role_info.coin or 0

		if role_coin > cur_guwu_cfg.coin_guwu_cost then
			BOSSInvasionWGCtrl.Instance:SendBOSSInvasionReq(CROSS_BOSS_STRIKE_OPERATE_TYPE.GUWU, CROSS_BOSS_STRIKE_GUWU_TYPE.GUWU_TYPE_COIN)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NoCoin)
			TipWGCtrl.Instance:OpenItemTipGetWay({is_bind = 0, item_id = 65535})
		end
	else
		local bind_gold_num = RoleWGData.Instance:GetRoleInfo().bind_gold
		local gold_num = RoleWGData.Instance:GetRoleInfo().gold

		if cur_guwu_cfg.gold_guwu_cost > bind_gold_num and cur_guwu_cfg.gold_guwu_cost > gold_num then
			VipWGCtrl.Instance:OpenTipNoGold()
		else
			BOSSInvasionWGCtrl.Instance:SendBOSSInvasionReq(CROSS_BOSS_STRIKE_OPERATE_TYPE.GUWU, CROSS_BOSS_STRIKE_GUWU_TYPE.GUWU_TYPE_GOLD)
		end
	end
end

function BOSSInvasionGuWuView:OnFlush(param, index)
	local cur_guwu_cfg = BOSSInvasionWGData.Instance:GetCurBossCfg()
	local coin_guwu_time, gold_guwu_time = BOSSInvasionWGData.Instance:GetCurGuwuTime()
	self.node_list.times1.text.text = string.format(Language.BOSSInvasion.GuWuTimeStr, coin_guwu_time, cur_guwu_cfg.coin_guwu_max_times)
	self.node_list["times2"].text.text = string.format(Language.BOSSInvasion.GuWuTimeStr, gold_guwu_time, cur_guwu_cfg.gold_guwu_max_times)

	local attr_name = EquipmentWGData.GetFightAttrNameByAttrId(GameEnum.FIGHT_CHARINTATTR_TYPE_SHANGHAI_JC_PER)
	local add_per1 = BOSSInvasionWGData.Instance:GetCoinAddPer(coin_guwu_time)
	local add_per2 = BOSSInvasionWGData.Instance:GetGoldAddPer(gold_guwu_time)
	self.node_list["xiaoguo_desc1"].text.text = string.format( attr_name .. "+%d%%",add_per1)
	self.node_list["xiaoguo_desc2"].text.text = string.format( attr_name .. "+%d%%",add_per2)

	self.node_list.lbl_coin_cost.text.text = cur_guwu_cfg.coin_guwu_cost
	self.node_list.lbl_gold_cost.text.text = cur_guwu_cfg.gold_guwu_cost

	self.node_list.btn_guwu_gold:SetActive(gold_guwu_time < cur_guwu_cfg.gold_guwu_max_times)
	self.node_list.btn_guwu_coin:SetActive(coin_guwu_time < cur_guwu_cfg.coin_guwu_max_times)

	self.node_list.coin_finish:SetActive(coin_guwu_time >= cur_guwu_cfg.coin_guwu_max_times)
	self.node_list.gold_finish:SetActive(gold_guwu_time >= cur_guwu_cfg.gold_guwu_max_times)
end