require("game/triple_recharge/triple_recharge_wg_data")
require("game/triple_recharge/triple_recharge_view")

TripleRechargeWGCtrl = TripleRechargeWGCtrl or BaseClass(BaseWGCtrl)

function TripleRechargeWGCtrl:__init()
	if TripleRechargeWGCtrl.Instance then
		error("[TripleRechargeWGCtrl]:Attempt to create singleton twice!")
	end

    TripleRechargeWGCtrl.Instance = self

    self.data = TripleRechargeWGData.New()
    self.view = TripleRechargeView.New(GuideModuleName.TripleRechargeView)

    self:RegisterAllProtocols()

    self.activity_change_callback = BindTool.Bind(self.OnActChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)
end

function TripleRechargeWGCtrl:__delete()
    self.data:DeleteMe()
	self.data = nil

    self.view:DeleteMe()
	self.view = nil

    TripleRechargeWGCtrl.Instance = nil

    if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
		self.activity_change_callback = nil
	end

    if self.level_change then
        RoleWGData.Instance:UnNotifyAttrChange(self.level_change)
        self.level_change = nil
    end
end

function TripleRechargeWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOATripleRechargeInfo, "OnSCOATripleRechargeInfo")
end

function TripleRechargeWGCtrl:OnSCOATripleRechargeInfo(protocol)
    self.data:SetAllInfo(protocol)
    self:CheckNeedCloseAct()
end

function TripleRechargeWGCtrl:CheckNeedCloseAct()
    local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TRIPLE_RECHARGE)
	if activity_info and activity_info.status == ACTIVITY_STATUS.CLOSE then
		return
	end

	local need_close = self.data:GetCurGradeIsNeedClose()
	if need_close then
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TRIPLE_RECHARGE, ACTIVITY_STATUS.CLOSE)
		if self.view:IsOpen() then
			self.view:Close()
		end
	else
		ViewManager.Instance:FlushView(GuideModuleName.TripleRechargeView)
	end
end

function TripleRechargeWGCtrl:OnActChange(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TRIPLE_RECHARGE then
        local activity_cfg = ActivityWGData.Instance:GetDailyActivityDailyCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TRIPLE_RECHARGE)
        local role_level = GameVoManager.Instance:GetMainRoleVo().level
        local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
        local is_can_open = PlayerPrefsUtil.GetInt("TripleRechargeWGCtrl" .. main_role_id) ~= 1
        if status == ACTIVITY_STATUS.OPEN and role_level >= tonumber(activity_cfg.level) and is_can_open then
            if self.view then
                self.view:Open()
                PlayerPrefsUtil.SetInt("TripleRechargeWGCtrl" .. main_role_id, 1)
            end
        elseif status == ACTIVITY_STATUS.OPEN and role_level < tonumber(activity_cfg.level) then
            if not self.level_change then
                self.level_change = BindTool.Bind(self.RoleDataChangeCallBack, self)
                RoleWGData.Instance:NotifyAttrChange(self.level_change, {"level"})
            end
        elseif status == ACTIVITY_STATUS.CLOSE then
            PlayerPrefsUtil.SetInt("TripleRechargeWGCtrl" .. main_role_id, 0)
        end
	end
end

function TripleRechargeWGCtrl:RoleDataChangeCallBack(key, value, old_value)
	if key == "level" then
        local activity_cfg = ActivityWGData.Instance:GetDailyActivityDailyCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TRIPLE_RECHARGE)
        local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TRIPLE_RECHARGE)
        if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN and value >= tonumber(activity_cfg.level) then
            local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
            local is_can_open = PlayerPrefsUtil.GetInt("TripleRechargeWGCtrl" .. main_role_id) ~= 1
            if self.view and is_can_open then
                self.view:Open()
                PlayerPrefsUtil.SetInt("TripleRechargeWGCtrl" .. main_role_id, 1)
            end

            if self.level_change then
                RoleWGData.Instance:UnNotifyAttrChange(self.level_change)
                self.level_change = nil
            end
        end
	end
end