CopperBuyView = CopperBuyView or BaseClass(DayCountChangeView)

function CopperBuyView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Normal
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(612, 460)})
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_copper_msg")
end

function CopperBuyView:__delete()

end

function CopperBuyView:ReleaseCallBack()
	DayCountChangeView.ReleaseCallBack(self)
	if self.copper_vip_num1 then
		self.copper_vip_num1:DeleteMe()
		self.copper_vip_num1 = nil
	end

	if self.copper_vip_num2 then
		self.copper_vip_num2:DeleteMe()
		self.copper_vip_num2 = nil
	end
end

function CopperBuyView:LoadCallBack()
	DayCountChangeView.LoadCallBack(self)
	self.node_list["title_view_name"].text.text = Language.ViewName.CiShuZengJia
	local other_cfg = FuBenPanelWGData.Instance:GetTongBiBenOtherCfg()
	local str = string.format(Language.FuBenPanel.CopperBuyNum, other_cfg.buy_times_need_gold)
	self.node_list["rich_buy_desc"].text.text = str
	
	self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind(self.OnClinkCancelHandler, self))
	self.node_list["btn_buy_count"].button:AddClickListener(BindTool.Bind(self.OnClinkBuyCount, self))
end

function CopperBuyView:ShowIndexCallBack()
end

function CopperBuyView:OnFlush()
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	local vip_buy_cfg = FuBenPanelWGData.Instance:GetVipBuyCountCfg()
	local copper_info = CopperFbWGData.Instance:GetTongBiInfo()
	local temp_str = vip_buy_cfg["param_" .. role_vip] - copper_info.day_buy_times 
	local des
	if temp_str > 0 then
    	des = string.format(Language.FuBenPanel.CopperBuyTips, vip_buy_cfg["param_" .. role_vip] - copper_info.day_buy_times, vip_buy_cfg["param_" .. role_vip])
    else
    	des = string.format(Language.FuBenPanel.CopperBuyTipsNotnough, vip_buy_cfg["param_" .. role_vip] - copper_info.day_buy_times, vip_buy_cfg["param_" .. role_vip])
    end
	
	self.node_list["rich_buy_des_1"].text.text = des
	local next_vip = FuBenPanelWGData.Instance:GetVipNextBuyCount(role_vip)

	local max_vip = VipWGData.Instance:GetMaxVIPLevel()
	if role_vip < next_vip and vip_buy_cfg["param_" .. next_vip] then
		if vip_buy_cfg["param_" .. next_vip] > 0 then
			des = string.format(Language.FuBenPanel.CopperBuyTips2, vip_buy_cfg["param_" .. next_vip])
		end
	else
		des = string.format(Language.FuBenPanel.CopperBuyTips2, vip_buy_cfg["param_" .. role_vip])
	end
	
	if role_vip >= next_vip then
		des = Language.FuBenPanel.CopperBuyTips3
	end

	self.node_list["rich_buy_des_2"].text.text = des
	if self.node_list["img_vip_curlevel"] and self.node_list["img_vip_nexlevel"] then
		self.node_list["img_vip_curlevel"].text.text ="V".. role_vip
		if role_vip < next_vip then
			self.node_list["img_vip_nexlevel"].text.text = "V"..next_vip
			self.node_list["layout_next_vip"]:SetActive(true)	
		else
			self.node_list["img_vip_nexlevel"].text.text = "V"..role_vip
		end
	end
end

function CopperBuyView:OnClinkCancelHandler()
	self:Close()
end

function CopperBuyView:OnClinkBuyCount()
	FuBenPanelWGCtrl.Instance:SendTongBiFbOperate(COIN_FB_OPERA_TYPE.COIN_FB_OPERA_TYPE_BUY_FB_TIMES)

	local role_vip = VipWGData.Instance:GetRoleVipLevel() or 0
	local vip_buy_cfg = FuBenPanelWGData.Instance:GetVipBuyCountCfg()
	local copper_info = CopperFbWGData.Instance:GetTongBiInfo()

	if vip_buy_cfg == nil or copper_info == nil or copper_info.day_buy_times == nil or vip_buy_cfg["param_" .. role_vip] - copper_info.day_buy_times <= 1 then
		--self:Close()
	end
end

