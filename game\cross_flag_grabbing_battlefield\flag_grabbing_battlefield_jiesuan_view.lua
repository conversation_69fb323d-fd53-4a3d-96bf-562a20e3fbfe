FlagGrabbingBattleFieldJieSuanView = FlagGrabbingBattleFieldJieSuanView or BaseClass(SafeBaseView)

function FlagGrabbingBattleFieldJieSuanView:__init()
	self:SetMaskBg()
    self.view_style = ViewStyle.Half
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_jiesuan_panel")
    self:AddViewResource(0, "uis/view/country_map_ui/flag_grabing_battlefield_ui_prefab", "layout_fgb_jiesuan_view2")
end

function FlagGrabbingBattleFieldJieSuanView:LoadCallBack()
    if not self.fgb_item_list then
        self.fgb_item_list = AsyncListView.New(ItemCell, self.node_list.fgb_item_list)
        self.fgb_item_list:SetStartZeroIndex(true)
    end

    if not self.fgb_rank_list then
        self.fgb_rank_list = AsyncListView.New(FGBJieSuanRankListItemRender, self.node_list.fgb_rank_list)
        self.fgb_rank_list:SetStartZeroIndex(true)
    end
end

function FlagGrabbingBattleFieldJieSuanView:ReleaseCallBack()
    if self.fgb_item_list then
        self.fgb_item_list:DeleteMe()
        self.fgb_item_list = nil
    end

    if self.fgb_rank_list then
        self.fgb_rank_list:DeleteMe()
        self.fgb_rank_list = nil
    end

    -- if self.btn_text_countdown then
	-- 	GlobalTimerQuest:CancelQuest(self.btn_text_countdown)
	-- 	self.btn_text_countdown = nil
	-- end
end

function FlagGrabbingBattleFieldJieSuanView:SetData(data_info)
    self.data_info = data_info
end

function FlagGrabbingBattleFieldJieSuanView:OnFlush()
    if IsEmptyTable(self.data_info) then
        return
    end

    local fgb_data = CrossFlagGrabbingBattleFieldWGData.Instance
    local data = self.data_info
    local my_camp = fgb_data:GetFGBMyCamp()
    local other_camp = my_camp == 0 and 1 or 0
    local my_score = (data.camp_score_list or {})[my_camp] or 0
    local other_score = (data.camp_score_list or {})[other_camp] or 0
    local is_suc = my_score >= other_score
    self.node_list.victory:SetActive(is_suc)
    self.node_list.lose:SetActive(not is_suc)

    for i = 0, 1 do
        local tram_cfg = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBCampCfgByCampId(i)
		local team_name = tram_cfg and tram_cfg.camp_name or ""

        self.node_list["fgb_team_name" .. i].text.text = team_name
        self.node_list["fgb_team_score" .. i].text.text = i == my_camp and my_score or other_score

        local win_flag = false
        if my_score ~= other_score then
            local suc_id = is_suc and my_camp or other_camp
            win_flag = i == suc_id
        end

        self.node_list["fgb_team_win" .. i]:CustomSetActive(win_flag)
        self.node_list["fgb_team_fail" .. i]:CustomSetActive(not win_flag)
    end

    local reward_data = is_suc and fgb_data:GetFGBWinRewardList() or fgb_data:GetFGBFailRewardList()
    self.fgb_item_list:SetDataList(reward_data)
    self.fgb_rank_list:SetDataList(data.rank_item_list)
end

-- function FlagGrabbingBattleFieldJieSuanView:AddCountDown()
--     if self.btn_text_countdown then
-- 		GlobalTimerQuest:CancelQuest(self.btn_text_countdown)
-- 		self.btn_text_countdown = nil
-- 	end

--     local total_time = 8
--     local index = 0
--     self.node_list.close_time_text.text.text = string.format(Language.Common.AutoCloseTimerTxt, total_time)
--     self.btn_text_countdown = GlobalTimerQuest:AddTimesTimer(function ()
--         index = index + 1
--         if self.node_list.close_time_text then
--             self.node_list.close_time_text.text.text = string.format(Language.Common.AutoCloseTimerTxt, total_time - index)
--         end
--     end, 1, total_time)
-- end

------------------------------FGBJieSuanRankListItemRender----------------------------
FGBJieSuanRankListItemRender = FGBJieSuanRankListItemRender or BaseClass(BaseRender)

function FGBJieSuanRankListItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.role_name.text.text = self.data.name
    local is_top_3 = self.data.rank <= 3
    self.node_list.rank_num_icon:CustomSetActive(is_top_3)

    -- local bundle, asset = "", ""
    local rank_text = ""
	if not is_top_3 then
        -- local image_id = self.data.rank % 2 == 0 and "a1_ty_pmd4" or "a1_ty_pmd5"
        -- bundle, asset = ResPath.GetCommonImages(image_id)
        rank_text = self.data.rank
        self.node_list.bg:SetActive(false)
        self.node_list.bg1:SetActive(true)
	else
		-- bundle, asset = ResPath.GetCommonImages("a1_ty_pmd" .. self.data.rank)
        self.node_list.rank_num_icon.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. self.data.rank))
        self.node_list.bg.image:LoadSprite(ResPath.GetCommonImages("a3_ty_list_" .. self.data.rank))
        self.node_list.bg1:SetActive(false)
        self.node_list.bg:SetActive(true)
	end

    self.node_list.rank_text.text.text = rank_text
    self.node_list.role_score.text.text = self.data.score
    self.node_list.role_kill_num.text.text = self.data.kill_num
end