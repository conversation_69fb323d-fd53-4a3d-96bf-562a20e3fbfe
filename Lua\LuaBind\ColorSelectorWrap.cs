﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class ColorSelectorWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(ColorSelector), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>RegFunction("FulshNowPosColor", FulshNowPosColor);
		<PERSON><PERSON>Function("SetSelectorWheel", SetSelectorWheel);
		<PERSON><PERSON>RegFunction("UpdateHexColorSelectorWheel", UpdateHexColorSelectorWheel);
		<PERSON><PERSON>RegFunction("UpdateColorSelectorWheel", UpdateColorSelectorWheel);
		<PERSON><PERSON>Function("SetIsSoft", SetIsSoft);
		<PERSON><PERSON>Function("qingrou", qingrou);
		L.RegFunction("quanshu", quanshu);
		L.RegFunction("OnPointerDown", OnPointerDown);
		<PERSON><PERSON>RegFunction("OnDrag", OnDrag);
		<PERSON><PERSON>RegFunction("<PERSON>Move", CircleMove);
		<PERSON><PERSON>RegFunction("GetCanvasFinalPos", GetCanvasFinalPos);
		<PERSON><PERSON>unction("CalculateWheelPos", CalculateWheelPos);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("m_SubColorSelector", get_m_SubColorSelector, set_m_SubColorSelector);
		L.RegVar("m_ColorWheel", get_m_ColorWheel, set_m_ColorWheel);
		L.RegVar("m_GetColorWheel", get_m_GetColorWheel, set_m_GetColorWheel);
		L.RegVar("m_ColorSaturation", get_m_ColorSaturation, set_m_ColorSaturation);
		L.RegVar("m_SelectorWheel", get_m_SelectorWheel, set_m_SelectorWheel);
		L.RegVar("m_radius_offset", get_m_radius_offset, set_m_radius_offset);
		L.RegVar("m_canvas_offset_pos", get_m_canvas_offset_pos, set_m_canvas_offset_pos);
		L.RegVar("angle", get_angle, set_angle);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FulshNowPosColor(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ColorSelector obj = (ColorSelector)ToLua.CheckObject<ColorSelector>(L, 1);
			obj.FulshNowPosColor();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetSelectorWheel(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ColorSelector obj = (ColorSelector)ToLua.CheckObject<ColorSelector>(L, 1);
			UnityEngine.UI.Image arg0 = (UnityEngine.UI.Image)ToLua.CheckObject<UnityEngine.UI.Image>(L, 2);
			obj.SetSelectorWheel(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UpdateHexColorSelectorWheel(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ColorSelector obj = (ColorSelector)ToLua.CheckObject<ColorSelector>(L, 1);
			string arg0 = ToLua.CheckString(L, 2);
			obj.UpdateHexColorSelectorWheel(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UpdateColorSelectorWheel(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ColorSelector obj = (ColorSelector)ToLua.CheckObject<ColorSelector>(L, 1);
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.UpdateColorSelectorWheel(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIsSoft(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			ColorSelector obj = (ColorSelector)ToLua.CheckObject<ColorSelector>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			obj.SetIsSoft(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int qingrou(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ColorSelector obj = (ColorSelector)ToLua.CheckObject<ColorSelector>(L, 1);
			obj.qingrou();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int quanshu(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ColorSelector obj = (ColorSelector)ToLua.CheckObject<ColorSelector>(L, 1);
			obj.quanshu();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnPointerDown(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ColorSelector obj = (ColorSelector)ToLua.CheckObject<ColorSelector>(L, 1);
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnPointerDown(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnDrag(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ColorSelector obj = (ColorSelector)ToLua.CheckObject<ColorSelector>(L, 1);
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnDrag(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CircleMove(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ColorSelector obj = (ColorSelector)ToLua.CheckObject<ColorSelector>(L, 1);
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.CircleMove(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetCanvasFinalPos(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ColorSelector obj = (ColorSelector)ToLua.CheckObject<ColorSelector>(L, 1);
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			UnityEngine.Vector3 o = obj.GetCanvasFinalPos(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CalculateWheelPos(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ColorSelector obj = (ColorSelector)ToLua.CheckObject<ColorSelector>(L, 1);
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.CalculateWheelPos(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_SubColorSelector(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ColorSelector obj = (ColorSelector)o;
			SubColorSelector ret = obj.m_SubColorSelector;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_SubColorSelector on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_ColorWheel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ColorSelector obj = (ColorSelector)o;
			UnityEngine.RectTransform ret = obj.m_ColorWheel;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_ColorWheel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_GetColorWheel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ColorSelector obj = (ColorSelector)o;
			UnityEngine.UI.Image ret = obj.m_GetColorWheel;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_GetColorWheel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_ColorSaturation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ColorSelector obj = (ColorSelector)o;
			UnityEngine.UI.RawImage ret = obj.m_ColorSaturation;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_ColorSaturation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_SelectorWheel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ColorSelector obj = (ColorSelector)o;
			UnityEngine.RectTransform ret = obj.m_SelectorWheel;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_SelectorWheel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_radius_offset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ColorSelector obj = (ColorSelector)o;
			float ret = obj.m_radius_offset;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_radius_offset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_canvas_offset_pos(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ColorSelector obj = (ColorSelector)o;
			UnityEngine.Vector3 ret = obj.m_canvas_offset_pos;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_canvas_offset_pos on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_angle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ColorSelector obj = (ColorSelector)o;
			float ret = obj.angle;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index angle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_SubColorSelector(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ColorSelector obj = (ColorSelector)o;
			SubColorSelector arg0 = (SubColorSelector)ToLua.CheckObject<SubColorSelector>(L, 2);
			obj.m_SubColorSelector = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_SubColorSelector on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_ColorWheel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ColorSelector obj = (ColorSelector)o;
			UnityEngine.RectTransform arg0 = (UnityEngine.RectTransform)ToLua.CheckObject(L, 2, typeof(UnityEngine.RectTransform));
			obj.m_ColorWheel = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_ColorWheel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_GetColorWheel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ColorSelector obj = (ColorSelector)o;
			UnityEngine.UI.Image arg0 = (UnityEngine.UI.Image)ToLua.CheckObject<UnityEngine.UI.Image>(L, 2);
			obj.m_GetColorWheel = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_GetColorWheel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_ColorSaturation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ColorSelector obj = (ColorSelector)o;
			UnityEngine.UI.RawImage arg0 = (UnityEngine.UI.RawImage)ToLua.CheckObject<UnityEngine.UI.RawImage>(L, 2);
			obj.m_ColorSaturation = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_ColorSaturation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_SelectorWheel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ColorSelector obj = (ColorSelector)o;
			UnityEngine.RectTransform arg0 = (UnityEngine.RectTransform)ToLua.CheckObject(L, 2, typeof(UnityEngine.RectTransform));
			obj.m_SelectorWheel = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_SelectorWheel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_radius_offset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ColorSelector obj = (ColorSelector)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.m_radius_offset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_radius_offset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_canvas_offset_pos(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ColorSelector obj = (ColorSelector)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.m_canvas_offset_pos = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_canvas_offset_pos on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_angle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ColorSelector obj = (ColorSelector)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.angle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index angle on a nil value");
		}
	}
}

