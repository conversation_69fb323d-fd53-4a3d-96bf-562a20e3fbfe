SupremeFieldsWGData = SupremeFieldsWGData or BaseClass()

function SupremeFieldsWGData:__init()
	if SupremeFieldsWGData.Instance then
		error("[SupremeFieldsWGData] Attempt to create singleton twice!")
		return
	end

	SupremeFieldsWGData.Instance = self

	self.footlight_auto = ConfigManager.Instance:GetAutoConfig("footlight_cfg_auto")
	self.footlight_act_auto = ConfigManager.Instance:GetAutoConfig("operation_activity_footlight_rmb_buy_auto")
	self.footlight_skill_cfg = self.footlight_auto.footlight_skill
	--self.footlight_type_cfg = ListToMap(self.footlight_auto.foot_light, "type")
	self.footlight_type_cfg = self.footlight_auto.foot_light
	self.stone_all_cfg = self.footlight_auto.foot_stone
	self.stone_type_cfg = ListToMapList(self.footlight_auto.foot_stone, "type")
	self.stone_cfg = ListToMap(self.footlight_auto.foot_stone, "type", "slot")
	self.stone_item_cfg = ListToMap(self.footlight_auto.foot_stone, "item_id")
	self.skill_hole_cfg = self.footlight_auto.skill_hole--ListToMap(self.footlight_auto.skill_hole, "slot")
	self.wuxing_star_attr_cfg = ListToMap(self.footlight_auto.star_attr, "id", "star")
	--self.wuxing_total_attr_cfg = self.footlight_auto.total_star_attr --ListToMap(self.footlight_auto.total_star_attr, "total_star")
	self.wuxing_total_attr_cfg = ListToMap(self.footlight_auto.total_star_attr, "index")
	self.footlight_tab_list_cfg = self.footlight_auto.tab_type_show
	self.footlight_tab_type_cfg = self.footlight_auto.tab_type

	self.all_foot_light_list = {}
	self.all_foot_light_skill_list = {}
	self.foot_stone = {}
	self.is_buy_rmb = false
	self.is_buy_free = false
	self.skill_list = {}
	self.skill_type_list = {}
	self:InitFootLightTypeSkillID()
	self:InitFootStone()
	self:GetStuffRemindList()
	RemindManager.Instance:Register(RemindName.SupremeFields, BindTool.Bind(self.MainUIRemind, self))
	RemindManager.Instance:Register(RemindName.SupremeFieldsAct, BindTool.Bind(self.GetSupremeFieldsActRed, self))

	self.wuxing_star_list = {}
	self.wuxing_id = 0
	self.wuxing_field_star = 0
	self.wuxing_total_star = 0

	self.field_toggle_list = {}
end

function SupremeFieldsWGData:__delete()
  	RemindManager.Instance:UnRegister(RemindName.SupremeFields)
	  RemindManager.Instance:UnRegister(RemindName.SupremeFieldsAct)
	SupremeFieldsWGData.Instance = nil
end

function SupremeFieldsWGData:MainUIRemind()
	if not FunOpen.Instance:GetFunIsOpened("SupremeFieldsWGView") then
		return 0
	end

	if self:TestFootLightMainUiRedPoint() then
		return 1
	end

	if self:TestSkillSlotHasSurplus() and self:TestSkillCanBattle() then
		return 1
	end

	if self:GetWuXingTotalAttrIsCanActivate() then
		return 1
	end

	if self:GetWuXingLateAttrIsCanActivateRemind() then
		return 1
	end

	return 0
end

--主界面活动红点
function SupremeFieldsWGData:GetSupremeFieldsActRed()
	local _, is_buy_free = self:GetShopIsBuyFlag()
	if not is_buy_free then
		return 1
	end

    return 0
end

function SupremeFieldsWGData:InitFootStone()
	for k,v in pairs(self.footlight_auto.foot_stone) do
		self.foot_stone[v.item_id] = v
	end
end

function SupremeFieldsWGData:GetFootStoneByItemID(item_id)
	return self.foot_stone[item_id]
end

function SupremeFieldsWGData:GetFootActInfo()
	return self.footlight_act_auto
end

function SupremeFieldsWGData:SetShopIsBuyFlag(seq)
	self.buy_tmb_count = seq.buy_tmb_count or 0
	self.is_buy_free = seq.is_buy_free == 1
	self.reserve = seq.reserve
end

function SupremeFieldsWGData:GetShopIsBuyFlag()
	return self.buy_tmb_count, self.is_buy_free
end

function SupremeFieldsWGData:GetCurShopCfg()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_ZHI_ZUN)
	local waist_type = -1
	local rmb_shop_id, free_shop_id = 0, 0
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		local start_day = ActivityWGData.Instance:GetActivityOpenInServerDay(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_ZHI_ZUN)
		local cfg = self.footlight_act_auto.open_day
		for k,v in ipairs(cfg) do
			if start_day >= v.start_day and start_day <= v.end_day then
				waist_type = v.waist_type
				rmb_shop_id = v.rmb_shop_id
				free_shop_id = v.rmb_shop_id
				break
			end
		end
	else
		--grade = 1--测试
	end

	local cur_grade_cfg = self.footlight_act_auto.rmb_shop[rmb_shop_id] or {}
	local cur_grade_free_cfg = self.footlight_act_auto.free_shop[free_shop_id] or {}
	return waist_type, cur_grade_cfg, cur_grade_free_cfg
end

--标签页
function SupremeFieldsWGData:GetFootLightList()
	local data_list = {}
	local temp_day = 0
	local temp_level = 0
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or 0
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	local count = #self.footlight_type_cfg
	for i = 0, count do
		local item = self.footlight_type_cfg[i]
		local is_show = self:IsShowFootLight(item.type)
		temp_day = item.skynumber_show or 0
		temp_level = item.level_show or 0
		if ((open_day >= temp_day and role_level >= temp_level) and item.is_default_show == 1) or is_show then
			table.insert(data_list, item)
		end
	end

	return data_list
end

-- 设置Tab标签列表.
function SupremeFieldsWGData:SetFieldToggleList()
	local field_toggle_list = {}
	local cfg = self.footlight_tab_list_cfg
	local big_type, small_type = 0, 0
	for k, v in ipairs(cfg) do
		big_type = v.tab_type
		small_type = v.sub_type

		if not field_toggle_list[big_type] then
			field_toggle_list[big_type] = {
				name = v.name,
				type = big_type,
				child_list = {},
				is_show = false,
				is_remind = false
			}
		end

		local cfg_data = self:GetFootLightCfg(small_type)
		if cfg_data then
			field_toggle_list[big_type].is_show = true
			local is_red = self:TestFootLightRedPoint(small_type)
			if not is_red then
				is_red = self:GetWuXingLateAttrIsCanActivate(small_type)
			end

			if not field_toggle_list[big_type].is_remind then
				if is_red then
					field_toggle_list[big_type].is_remind = true
				else
					--判断直购活动开启.
					local grade, cur_shop_cfg, _ = self:GetCurShopCfg()
					local buy_tmb_count = self:GetShopIsBuyFlag()
					is_red = grade == small_type and buy_tmb_count <= cur_shop_cfg.buy_count_limit
					field_toggle_list[big_type].is_remind = is_red
				end
			end

			local lv = 0
			local wuxing_star_list = self:GetAllStarAttrInfo()
			if not IsEmptyTable(wuxing_star_list) or wuxing_star_list[small_type] then
				lv = wuxing_star_list[small_type]
			end
		
			local data = { name = cfg_data.name, type = small_type, lv = lv, is_remind = is_red , cfg = cfg_data}
			table.insert(field_toggle_list[big_type].child_list, data)
		end
	end

	for i = #field_toggle_list, 1, -1 do
		if field_toggle_list[i] and not field_toggle_list[i].is_show then
			field_toggle_list[i] = nil
		end
	end

	self.field_toggle_list = field_toggle_list
end

function SupremeFieldsWGData:GetFieldToggleList()
	return self.field_toggle_list
end

-- 获取合成配置 by 类型
function SupremeFieldsWGData:GetItemComposeCfgByType(big_type, small_type)
    return (self.compose_toggle_map_cfg[big_type] or {})[small_type]
end

function SupremeFieldsWGData:IsShowFootLight(type)
	local cfgs = self.stone_type_cfg[type] or {}
	for k,v in pairs(cfgs) do
		local _, is_open = self:TestFootLightSlotOpen(type, v.slot)
		local has_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
		if has_num > 0 or is_open then
			return true
		end
	end

	return false
end

function SupremeFieldsWGData:AllSFootLightSkillInfo(info)
	for k,v in pairs(info.all_skill_slots) do
		self.all_foot_light_skill_list[k] = v
	end
end

function SupremeFieldsWGData:AllSFootLightInfo(info)
	self:AllSFootLightSkillInfo(info)
	for k,v in pairs(info.all_foot_light_list) do
		if k <= #self.footlight_type_cfg then
			self.all_foot_light_list[k] = v
		end
	end

	RemindManager.Instance:Fire(RemindName.SupremeFields)
end

function SupremeFieldsWGData:ChangeSFootLightInfo(value)
	if self.all_foot_light_list[value.foot_id] then
		self.all_foot_light_list[value.foot_id].slots = value.foot_light_list
	end
end

function SupremeFieldsWGData:GetAllSFootLightById(type_index)
    return self.all_foot_light_list[type_index] or {}
end

function SupremeFieldsWGData:SetCurUseFootLight(type_index)
	for k,v in pairs(self.all_foot_light_list) do
		v.is_use = type_index == k
	end
end

function SupremeFieldsWGData:GetFootLightSkillList()
	local data_list = {}
	local count = #self.footlight_type_cfg
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or 0
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	for i = 0, count, 1 do
		local item = self.footlight_type_cfg[i]
		local is_show = self:IsShowFootLight(item.type)
		local temp_day = item.skynumber_show or 0
		local temp_level = item.show_level or 0
		if ((open_day >= temp_day and role_level >= temp_level) and item.is_default_show == 1) or is_show then
			local list = {}
			local lv, is_open = self:TestFootLightOpen(item.type)
			local index = is_open and lv or 1
			local skill_id = tonumber(self.skill_type_list[item.type][index][1])
			local skill_name = self:GetFootLightSkillCfg(skill_id).skill_name
			list.type = item.type
			list.name = item.name
			list.skill_name = skill_name
			list.is_open = is_open
			list.is_put_on = self:TestBattleSkillPutOn(skill_id)
			list.skill_id = skill_id
			list.skill_preview_id = item.skill_preview_id
			list.lv = lv
			list.sequence = item.skill_sequence--品质
			table.insert(data_list, list)
		end
	end

	table.sort(data_list, SortTools.KeyUpperSorters("lv", "sequence", "skill_id"))
	return data_list
end

function SupremeFieldsWGData:GetFootLightSkillSlotList()
	local data_list = {}
	for k,v in pairs(self.all_foot_light_skill_list) do
		local list = {}
		list.is_open = v.is_open
		list.slot_index = k
		list.skill_ids = v.skill_ids
		table.insert(data_list, list)
	end

	return data_list
end

--自动上阵技能数据
function SupremeFieldsWGData:GetAutoBattleSkillList()
	local skill_data = self:GetFootLightSkillList()
	local data_list = {}
	local battle_skill_list = {}

	for k,v in pairs(skill_data) do
		if v.is_open and not v.is_put_on then
			local temp = {}
			temp.hole_id = -1
			temp.foot_id = v.type
			temp.level = v.lv
			table.insert(battle_skill_list, temp)
		end
	end

	for k,v in pairs(self.all_foot_light_skill_list) do
		local list = {}
		if v.is_open and v.skill_ids[1] == 0 and not IsEmptyTable(battle_skill_list) then
			list.hole_id = k
			list.foot_id = battle_skill_list[1].foot_id
			list.level = battle_skill_list[1].level
			table.remove(battle_skill_list, 1)
		else
			list.hole_id = -1
			list.foot_id = -1
			list.level = 0
		end

		table.insert(data_list, list)
	end

	return #data_list, data_list
end

function SupremeFieldsWGData:GetFootLightSkillCfg(skill_id)
	return self.footlight_skill_cfg[skill_id]
end

function SupremeFieldsWGData:GetSkillHoleCfg(slot_index)
	return self.skill_hole_cfg[slot_index]
end

function SupremeFieldsWGData:GetFootLightCfg(type_index)
	return self.footlight_type_cfg[type_index]
end

function SupremeFieldsWGData:GetFootLightSlotCfg(type_index)
	return self.stone_cfg[type_index]
end

function SupremeFieldsWGData:GetFootLightSlotCfgByIndex(type_index, slot_index)
	return (self.stone_cfg[type_index] or {})[slot_index]
end

--根据领域和孔位获取对应的孔位属性
function SupremeFieldsWGData:GetFootLightSlotAttrList(type_index, slot_index)
	local cfg = (self.stone_cfg[type_index] or {})[slot_index]
	local attr_list = {}
	local attr_list_1 = {}

	if not cfg then
		return attr_list, attr_list_1
	end

	local slot_level, is_open = self:TestFootLightSlotOpen(type_index, slot_index)
	local max_level = cfg.max_lv
	for i = 1, 5 do
		local attr_val = cfg["attr_value" .. i]
		if attr_val > 0 then
			local temp = {}
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(cfg["attr_id" .. i])
			temp.attr_str = attr_str
			temp.attr_value = attr_val * slot_level
			if is_open and slot_level < max_level then
				temp.add_value = attr_val --* (slot_level + 1)
			else
				temp.add_value = 0
			end

			attr_list[i] = temp
		end
	end

	if self.footlight_auto.attr_name[cfg.attr_sys_id] and cfg.attr_sys_value > 0 then
		local temp = {}
		temp.attr_str = self.footlight_auto.attr_name[cfg.attr_sys_id].sys_name
		temp.attr_value = cfg.attr_sys_value / 100 * slot_level
		if is_open and slot_level < max_level then
			temp.add_value = cfg.attr_sys_value / 100 --* (slot_level + 1)
		else
			temp.add_value = 0
		end

		table.insert(attr_list_1, temp)
	end

	return attr_list, attr_list_1
end

--只计算镶嵌过的孔位战力、五行通天的战力.
function SupremeFieldsWGData:GetAllSlotCap(type_index)
	local capability = 0
	local attribute = AttributePool.AllocAttribute()
	local slot_cfg = self:GetFootLightSlotCfg(type_index)
	if not slot_cfg then return capability end

	for k,v in pairs(slot_cfg) do
		for i = 1, 5 do
			local slot_level, is_open = self:TestFootLightSlotOpen(type_index, k)
			local value = v["attr_value" .. i]
			local attr_id = v["attr_id" .. i]
			if is_open and value > 0 then
				local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
				if attribute[attr_str] then
					attribute[attr_str] = attribute[attr_str] + (value * slot_level)
				end
			end
		end
	end

	--计算五行通天战力.
	local wuxing_star_list = SupremeFieldsWGData.Instance:GetAllStarAttrInfo()
	if IsEmptyTable(wuxing_star_list) or not wuxing_star_list[type_index] then
		return
	end

	local wuxing_lv = wuxing_star_list[type_index]
	local attr_list = (self.wuxing_star_attr_cfg[type_index] or {})[wuxing_lv]
	if not IsEmptyTable(attr_list) then
		for i = 1, 5 do
			local value = attr_list["attr_value" .. i]
			local attr_id = attr_list["attr_id" .. i]
			if value > 0 then
				local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
				if attribute[attr_str] then
					attribute[attr_str] = attribute[attr_str] + value
				end
			end
		end
	end

	capability = AttributeMgr.GetCapability(attribute)
	return capability
end

--领域获得界面总属性
function SupremeFieldsWGData:FootLightGetNewAttrList(type_index)
	local stone_list = self:GetFootLightSlotCfg(type_index)
	if not stone_list then
		return
	end

	local temp = {}
	for k = 1, SUPREME_FIELDS_NUMTYPE.COUNT_FOOT_LIGHT_STONE do--5个孔
		for i = 1, 5 do--每个孔属性数
			if stone_list[k] then
				local attr_id = stone_list[k]["attr_id" .. i]
				local attr_val = stone_list[k]["attr_value" .. i]
				if attr_id > 0 and attr_val > 0 then
					local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
					if not temp[attr_str] then 
						temp[attr_str] = attr_val
					else
						temp[attr_str] = temp[attr_str] + attr_val
					end
				end
			end
		end
	end

	return temp
end

--领域获得界面战力
function SupremeFieldsWGData:FootLightGetNewCap(type_index)
	local capability = 0
	local attribute = AttributePool.AllocAttribute()
	local slot_cfg = self:GetFootLightSlotCfg(type_index)
	if not slot_cfg then
		return capability
	end

	for k = 1, SUPREME_FIELDS_NUMTYPE.COUNT_FOOT_LIGHT_STONE do--5个孔
		for i = 1, 5 do--每个孔属性数
			if slot_cfg[k] then
				local attr_id = slot_cfg[k]["attr_id" .. i]
				local attr_val = slot_cfg[k]["attr_value" .. i]
				if attr_id > 0 and attr_val > 0 then
					local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
					if attribute[attr_str] then
						attribute[attr_str] = attribute[attr_str] + attr_val
					end
				end
			end
		end
	end

	capability = AttributeMgr.GetCapability(attribute)
	return capability
end

--检测五行孔位是否开启
function SupremeFieldsWGData:TestFootLightSlotOpen(type_index, slot_index)
	local info = self:GetAllSFootLightById(type_index)
	local empty_table = {}
	local level = (((info or empty_table).slots or empty_table)[slot_index] or empty_table).level or 0
	return  level > 0 and level or 1,  level > 0
end

--获取所有五行石下一级的条件数量.
function SupremeFieldsWGData:GetSlotNextNeedSum(type_index, lv)
	local sum = 0
	local is_max = false
	local info = self:GetAllSFootLightById(type_index)
	if IsEmptyTable(info) then
		return sum, is_max
	end

	for index, value in ipairs(info.slots) do
		if value.level >= lv then
			sum = sum + 1
		end
	end

	if sum >= 5 then
		is_max = true
	end

	return sum, is_max
end

--获取所有五行石的等级总和.
function SupremeFieldsWGData:GetTotalSlotLevel()
	local lv = 0
	local data_list = SupremeFieldsWGData.Instance:GetFootLightList()
	for key, value in pairs(data_list) do
		local info = self:GetAllSFootLightById(value.type)
		if IsEmptyTable(info) then
			return lv
		end

		for index, value in ipairs(info.slots) do
			lv = lv + value.level
		end
	end

	return lv
end

--检测领域是否开启并返回对应的等级
function SupremeFieldsWGData:TestFootLightOpen(type_index)
	local info = self:GetAllSFootLightById(type_index)
	if IsEmptyTable(info) then
		return 0, false
	end

	local open_num = 0
	local level = nil
	for k,v in pairs(info.slots) do
		if v.itemId > 0 then
			open_num = open_num + 1
		end

		if not level then
			level = v.level
		end

		if level > v.level then
			level = v.level
		end
	end

	return level or 1, open_num == SUPREME_FIELDS_NUMTYPE.COUNT_FOOT_LIGHT_STONE
end

--检测领域是否有红点
function SupremeFieldsWGData:TestFootLightRedPoint(type_index)
	local info = self:GetAllSFootLightById(type_index)
	if IsEmptyTable(info) then
		return false
	end

	for k,v in pairs(info.slots) do
		local slot_cfg = self:GetFootLightSlotCfgByIndex(type_index, k)
		if slot_cfg then
			local num = ItemWGData.Instance:GetItemNumInBagById(slot_cfg.item_id)
			if v.level < slot_cfg.max_lv and num >= slot_cfg.cost_item_num then
				return true
			end
		end
	end

	return false
end

--检测五行孔位是否有红点
function SupremeFieldsWGData:TestFootLightSlotRedPoint(type_index, slot_index)
	local info = self:GetAllSFootLightById(type_index)
	local slot_cfg = self:GetFootLightSlotCfgByIndex(type_index, slot_index)
	if IsEmptyTable(info) or not info.slots[slot_index] or not slot_cfg then
		return false
	end

	local num = ItemWGData.Instance:GetItemNumInBagById(slot_cfg.item_id)
	if info.slots[slot_index].level < slot_cfg.max_lv and num >= slot_cfg.cost_item_num then 
		return true
	end

	return false
end

--检测领域技能是否上阵
function SupremeFieldsWGData:TestBattleSkillPutOn(skill_id)
	local is_put_on = false
	local skill_list = self:GetFootLightSkillSlotList()
	if not skill_list then
		return false
	end

	for k,v in pairs(skill_list) do
		for i=1, #v.skill_ids do
			if skill_id == v.skill_ids[i] then
				is_put_on = true
				break
			end
		end
	end

	return is_put_on
end

--检测领域是否幻化
function SupremeFieldsWGData:TestFootLightUse(type_index)
	local info = self:GetAllSFootLightById(type_index)
	return info and info.is_use
end

--检测所有技能孔位是否还有空位
function SupremeFieldsWGData:TestSkillSlotHasSurplus()
	local slot_data = self:GetFootLightSkillSlotList()
	local num = 0
	for k,v in pairs(slot_data) do
		if v.skill_ids[1] == 0 and v.is_open then
			num = num + 1
		end
	end

	return num > 0
end

--检测是否还有技能可以上阵
function SupremeFieldsWGData:TestSkillCanBattle()
	local skill_data = self:GetFootLightSkillList()
	local num = 0
	for k,v in pairs(skill_data) do
		if v.is_open and not v.is_put_on then
			num = num + 1
		end
	end

	return num > 0
end

--检测所有红点
function SupremeFieldsWGData:TestFootLightMainUiRedPoint()
	for k,v in pairs(self.all_foot_light_list) do
		for i = 1, SUPREME_FIELDS_NUMTYPE.COUNT_FOOT_LIGHT_STONE do
			local slot_cfg = self:GetFootLightSlotCfgByIndex(k, i)
			if slot_cfg and v.slots[i].level < slot_cfg.max_lv then
				local num = ItemWGData.Instance:GetItemNumInBagById(slot_cfg.item_id)
				if num >= slot_cfg.cost_item_num then
					return true
				end
			end
		end
	end

	return false
end

function SupremeFieldsWGData:SkillShowCfgList(type_index, level)
	local data_list = {}
	local lv, is_open = self:TestFootLightOpen(type_index)

	local index = level and level or (is_open and lv or 1)
	local skill_id = (self:GetSkillIDList(type_index, index))[2] or 0
	data_list.skill_id = skill_id
	data_list.fazhen_id = type_index
	return data_list
end

function SupremeFieldsWGData:InitFootLightTypeSkillID()
	for k, v in pairs(self.footlight_type_cfg) do
	local skill_id_team = Split(v.skill_id, "|")
		if not self.skill_type_list[k] then
			self.skill_type_list[k] = {}
		end

		for k1,v1 in pairs(skill_id_team) do
			if not self.skill_type_list[k][k1] then
				self.skill_type_list[k][k1] = {}
			end

			local id_list = Split(v1, ",")
			for k2,v2 in pairs(id_list) do
				local skill_id = tonumber(v2) or 0
				self.skill_list[skill_id] = k
				table.insert(self.skill_type_list[k][k1], skill_id)
			end
	  	end
	end
end

function SupremeFieldsWGData:GetFootLightTypeBySkillID(skill_id)
	return self.skill_list[skill_id]
end

function SupremeFieldsWGData:GetSkillIDList(type_index, index)
	return (self.skill_type_list[type_index] or {})[index] or {}
end

function SupremeFieldsWGData:GetStuffRemindList()
	self.stuff_remind_list = {}
	local item_id
	for k,v in pairs(self.stone_all_cfg) do
		item_id = v.item_id
		self.stuff_remind_list[item_id] = true
	end

	for k,v in pairs(self.skill_hole_cfg) do
		local item_list = Split(v.condition_value, "|")
		item_id = tonumber(item_list[1])
		self.stuff_remind_list[item_id] = true
	end
end

function SupremeFieldsWGData:CheckStuffRemind(item_id)
    local remind_list = self.stuff_remind_list[item_id]
    if not remind_list then
        return false
    end

    return true
end

---------------------领域直购每日一弹
function SupremeFieldsWGData:SetEveryDayOpenView()
	local key = "supreme_fields_everyday_tips_key"
    local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    PlayerPrefsUtil.SetInt(key .. main_role_id, open_day)
end

function SupremeFieldsWGData:GetNeedEveryDayOpenView()
	local need_open_view = false
	local key = "supreme_fields_everyday_tips_key"
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	need_open_view = PlayerPrefsUtil.GetInt(key .. main_role_id) ~= open_day
	return need_open_view
end

--------------------------五行通天---------------------------
function SupremeFieldsWGData:SetAllStarAttrInfo(protocol)
	self.wuxing_star_list = protocol.info
end

function SupremeFieldsWGData:SetSingleStarAttrInfo(protocol)
	self.wuxing_id = protocol.foot_light_id
	self.wuxing_field_star = protocol.foot_light_star
end

function SupremeFieldsWGData:SetSingleTotalStarAttrInfo(protocol)
	self.wuxing_total_star = protocol.foot_light_total_star
end

function SupremeFieldsWGData:GetAllStarAttrInfo()
	return self.wuxing_star_list
end

-- 领域ID
function SupremeFieldsWGData:GetStarAttrInfoByType(type)
	return self.wuxing_star_list[type]
end

function SupremeFieldsWGData:GetSingleStarAttrInfo()
	return self.wuxing_id, self.wuxing_field_star
end

function SupremeFieldsWGData:GetSingleTotalStarAttrInfo()
	return self.wuxing_total_star
end

--获取领域的五行通天的属性加成.
function SupremeFieldsWGData:GetWuXingStarAttrList(id, lv)
	local cfg = (self.wuxing_star_attr_cfg[id] or {})[lv]
	local attr_list = {}

	if IsEmptyTable(cfg) then
		return
	end

	for i = 1, 5 do
		local attr_val = cfg["attr_value" .. i]
		if attr_val > 0 then
			local temp = {}
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(cfg["attr_id" .. i])
			temp.attr_str = attr_str
			temp.attr_value = attr_val

			attr_list[i] = temp
		end
	end

	return attr_list
end

--获取五行通天的总属性当前加成和下一阶加成.
function SupremeFieldsWGData:GetWuXingTotalAttrList(lv)
	--当前总星级索引.
	local cur_idx = 0
	--下一阶总星级索引.
	local late_idx = -1

	for index, value in pairs(self.wuxing_total_attr_cfg) do
		if value.total_star <= lv then
			cur_idx = index
		else
			late_idx = index
			break
		end
	end

	if late_idx == -1 then
		late_idx = cur_idx
	end

	local function GetAttrData(idx)
		local cfg = self.wuxing_total_attr_cfg[idx] or {}
		local attr_list = {}

		if IsEmptyTable(cfg) then
			return
		end

		for i = 1, 5 do
			local attr_val = cfg["attr_value" .. i]
			if idx == 1 or attr_val > 0 then
				local temp = {}
				local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(cfg["attr_id" .. i])
				temp.attr_str = attr_str
				temp.attr_value = attr_val

				attr_list[i] = temp
			end
		end

		return cfg.total_star, attr_list
	end

	local cur_lv, cur_attr_list = GetAttrData(cur_idx)
	local late_lv, late_attr_list = GetAttrData(late_idx)

	return cur_lv, late_lv, cur_attr_list, late_attr_list
end

--获取五行通天的当前可激活的总属性等级.
function SupremeFieldsWGData:GetWuXingCurCanActivateStar(lv)
	local cur_lv = 0
	for index, value in pairs(self.wuxing_total_attr_cfg) do
		if value.total_star <= lv then
			cur_lv = value.total_star
		else
			break
		end
	end

	return cur_lv
end

--获取五行通天的总属性是否可激活.
function SupremeFieldsWGData:GetWuXingTotalAttrIsCanActivate()
	local lv = self:GetTotalSlotLevel()
	local can_act_lv = self:GetWuXingCurCanActivateStar(lv)
	local cur_lv = self:GetSingleTotalStarAttrInfo()
	return can_act_lv > cur_lv
end

--获取领域的五行通天的下一阶属性加成是否可激活.
function SupremeFieldsWGData:GetWuXingLateAttrIsCanActivate(idx)
	local sum = 0
	local is_max = false
	if IsEmptyTable(self.wuxing_star_list) or not self.wuxing_star_list[idx] then
		return is_max
	end

	local lv = self.wuxing_star_list[idx]

	if lv < 10 then
		sum, is_max = self:GetSlotNextNeedSum(idx, lv + 1)
	end

	return is_max
end

--获取领域的五行通天是否有红点.
function SupremeFieldsWGData:GetWuXingLateAttrIsCanActivateRemind()
	local sum = 0
	local is_red = false
	for key, lv in pairs(self.wuxing_star_list) do
		if lv < 10 then
			sum, is_red = self:GetSlotNextNeedSum(key, lv + 1)
			if is_red then
				break
			end
		end
	end

	return is_red
end
----------------------------End------------------------------

function SupremeFieldsWGData:GetFootLightTypeByItemId(item_id)
	return (self.stone_item_cfg[item_id] or {}).type
end

function SupremeFieldsWGData:GetFootLightTabTypeCfg(type)
	return self.footlight_tab_type_cfg[type]
end