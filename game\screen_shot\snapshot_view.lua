local SystemFile = System.IO.File

SnapShotView = SnapShotView or BaseClass(SafeBaseView)

local NameBoxIndex = 1
local ServerBoxIndex = 2
SnapShotFrameConfig = {
    {item_icon = "frame_item_icon1", big_img = "snap_shot_frame1", is_have_frame = false},
    {item_icon = "frame_item_icon2", big_img = "snap_shot_frame2", is_have_frame = true},
    -- {item_icon = "frame_item_icon2", big_img = "snap_shot_frame2"},
}

function SnapShotView:__init()
    self:SetMaskBg()

    self.active_close = false
    self:AddViewResource(0, "uis/view/screen_shot_ui_prefab", "snapshot_panel")
    self.view_layer = UiLayer.Guide
	self.dcim_path = UnityEngine.Application.persistentDataPath .. "/Screenshot/Screenshot_"
    self.is_saving = false

    self.select_frame = nil
    self.select_data = nil
    self.select_index = nil
end

function SnapShotView:ReleaseCallBack()
    if self.frame_item_list then
        self.frame_item_list:DeleteMe()
        self.frame_item_list = nil
    end

    self.select_frame = nil

    self.frame_texture_list = nil
    self.select_data = nil
    self.select_index = nil
end

function SnapShotView:CloseCallBack()
    self.texture = nil
    self.is_saving = false
    GlobalTimerQuest:CancelQuest(self.delay_close_view)
end

function SnapShotView:ShowIndexCallBack()
    Scene.Instance:UpdateSceneQuality(SCENE_QUALITY_TYPE.DEPTH, false)
end

function SnapShotView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_save, BindTool.Bind(self.OnClickSave, self))

    self.mask_bg.image.color = Color.New(0, 0, 0, SafeBaseView.MASK_A)
    -- self.frame_item_list = AsyncListView.New(SnapShotFrameItem, self.node_list.frame_list)
    -- self.frame_item_list:SetSelectCallBack(BindTool.Bind(self.OnSelectFrameItem, self))

    -- local default_index = 1
    -- for i, v in ipairs(SnapShotFrameConfig) do
    --     if v.is_have_frame then
    --         default_index = i
    --         break
    --     end
    -- end

    -- self:CreateFrameTextureList()
    -- self.frame_item_list:SetDataList(SnapShotFrameConfig)
    -- self.frame_item_list:JumpToIndex(default_index)
end

function SnapShotView:OpenIndexCallBack()
end

function SnapShotView:SetTexture(texture)
    self.texture = texture
end

function SnapShotView:OnFlush()
    if self.texture ~= nil then
        self.node_list.photo:SetActive(true)
        self.node_list.photo.raw_image.texture = self.texture
    end
end

function SnapShotView:CreateFrameTextureList()
    self.frame_texture_list = {}
    for i, v in ipairs(SnapShotFrameConfig) do
        if v.is_have_frame then
            local frame_loader = AllocResAsyncLoader(self, "Frame" .. i)
            local bundle_name, asset_name = ResPath.GetF2RawImagesPNG(v.big_img)
            frame_loader:Load(bundle_name, asset_name, typeof(UnityEngine.Texture),
                function(texture)
                    self.frame_texture_list[i] = texture
                    if i == #SnapShotFrameConfig then
                        self:FlushSelectFrame()
                    end
                end)
        end
    end
end

function SnapShotView:FlushSelectFrame()
    if not self:IsOpen() then
        return
    end

    if self.select_data ~= nil and self.select_index ~= nil then
        if self.select_data.is_have_frame then
            if self.frame_texture_list ~= nil then
                self.select_frame = self.frame_texture_list[self.select_index]
            else
                self.select_frame = nil
            end
        else
            self.select_frame = nil
        end        
    end
end

function SnapShotView:OnSelectFrameItem(item, index)
    local data = item:GetData()
    if not data then
        return
    end
    --[[
    if data.big_img ~= "" then
        local b, a = ResPath.GetF2RawImagesPNG(data.big_img)
        self.node_list.photo_frame.raw_image:LoadSprite(b, a, function()
            self.node_list.photo_frame:SetActive(true)
        end)
    else
        self.node_list.photo_frame:SetActive(false)
    end
    --]]

    self.select_index = index
    self.select_data = data
    if data.is_have_frame then
        if self.frame_texture_list ~= nil then
            self.select_frame = self.frame_texture_list[index]
        else
            self.select_frame = nil
        end
    else
        self.select_frame = nil
    end
end

function SnapShotView:OnClickSave()
    if self.is_saving then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ScreenShot.CaoZuoPinFan)
        return
    end

    if self.texture then
        if not self:CheckHasAuth() then
            self:RequestPhotoPurviewAuth(function (is_succ, msg)
                if not is_succ then
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.ScreenShot.NoAuth)
                end
            end)

            return
        end

        GlobalTimerQuest:CancelQuest(self.delay_close_view)
        local path = self.dcim_path .. os.date("%Y%m%d_%H%M%S", os.time()) .. ".jpg"
        -- 先把照片保存到下载缓存目录
        local succ = UtilU3d.SaveScreenshot(self.texture, path, self.select_frame)
        if succ then
            self.delay_close_view = GlobalTimerQuest:AddDelayTimer(function ()
                self:Close()
            end,10)

            self.is_saving = true
            -- 再通过SDK接口，把照片从缓存目录拷贝到相册
            self:AddToPhotoAlbum(path, function (is_succ, msg)
                GlobalTimerQuest:CancelQuest(self.delay_close_view)
                if is_succ then
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.ScreenShot.SaveSucc)
                    self:Close()
                else
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.ScreenShot.SaveFail)
                end

                -- 删除缓存目录下的照片
                if not UNITY_EDITOR then
                    self:RemoveFile(path)
                end

                self.is_saving = false
            end)
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.ScreenShot.SaveFail)
            self.is_saving = false
        end
    end
end

-- 是否拥有访问相册的权限
function SnapShotView:CheckHasAuth()
	return ImageTool.IsPhotoPurview()
end

-- 申请相册的权限
function SnapShotView:RequestPhotoPurviewAuth(call_back)
	ImageTool.RequestPhotoPurview(call_back)
end

-- 保存到相册
function SnapShotView:AddToPhotoAlbum(photo_path, call_back)
    Scheduler.Delay(function ()
        ImageTool.AddToPhoto(photo_path, call_back)
    end)
end

function SnapShotView:RemoveFile(file_path)
	-- 延迟1帧，防止文件被其它地方(SDK)访问而导致操作失败
	Scheduler.Delay(function ()
		if SystemFile.Exists(file_path) then
			os.remove(file_path)
		end
	end)
end

SnapShotFrameItem = SnapShotFrameItem or BaseClass(BaseRender)
function SnapShotFrameItem:__init()
end

function SnapShotFrameItem:OnFlush()
    local b, a = ResPath.GetSnapshotFrameImg(self.data.item_icon)
    self.node_list.icon.image:LoadSprite(b, a, function()
        XUI.ImageSetNativeSize(self.node_list.icon)
    end)
end

function SnapShotFrameItem:OnSelectChange(is_select)
    self.node_list.light:SetActive(is_select)
end