function BossNewPrivilegeView:BossKillEveryLoadCallBack()
    XUI.AddClickEventListener(self.node_list.open_miaosha_btn, BindTool.Bind(self.OnClickOpenMiaoShaBtn, self))
    XUI.AddClickEventListener(self.node_list.auto_kill_flag_btn, BindTool.Bind(self.OnClickOpenAutoKillBtn, self))
end

function BossNewPrivilegeView:BossKillEveryOnFlush()
    self:MidPanelInfo()
    self:TaskPanelInfo()
    self:FlushBtnTxt()
end

function BossNewPrivilegeView:FlushBtnTxt()
    local flag = BossKillEveryWGData.Instance:GetMiaoShaOpenFlag()
    self.node_list.miaosha_state_txt.text.text = flag > 0 and Language.BossKillEvery.ClosenMiaoSha or
        Language.BossKillEvery.OpenMiaoSha
    local icon_str = flag > 0 and "a3_boss_btn_zt" or "a3_boss_btn_bf1"
    local bundle, asset = ResPath.GetBossUI(icon_str)
    self.node_list["open_miaosha_btn_icon"].image:LoadSprite(bundle, asset, function()
        self.node_list["open_miaosha_btn_icon"].image:SetNativeSize()
    end)

    local auto_kill_flag = BossKillEveryWGData.Instance:GetAutoKillBossFlag()
    self.node_list.auto_kill_flag_img:SetActive(auto_kill_flag > 0)
end

function BossNewPrivilegeView:MidPanelInfo()
    local cur_level = BossKillEveryWGData.Instance:GetCurLevel()
    local cur_level_cfg = BossKillEveryWGData.Instance:GetCurLevelCfg(cur_level)
    local next_level_cfg = BossKillEveryWGData.Instance:GetCurLevelCfg(cur_level + 1)
    local ext_count = BossKillEveryWGData.Instance:GetDayExtCount()
    local ext_emp_count = BossKillEveryWGData.Instance:GetDayExtEmpCount()

    local show_count = ext_count

    if IsEmptyTable(cur_level_cfg) then
        return
    end

    local day_kill_num = BossKillEveryWGData.Instance:GetCurDayKillCount()
    local remain_count = cur_level_cfg.everyday_max_kill_count + show_count + ext_emp_count - day_kill_num
    self.node_list.boss_kill_every_times.text.text = string.format(Language.BossKillEvery.remainCount, remain_count)

    local desc_str, cur_max_kill_str, next_max_kill_str, ext_count_str, ext_emp_count_str
    cur_max_kill_str = ToColorStr(cur_level_cfg.everyday_max_kill_count,
        cur_level_cfg.everyday_max_kill_count > 0 and COLOR3B.C8 or COLOR3B.C10)
    ext_count_str = ToColorStr(ext_count, ext_count > 0 and COLOR3B.C8 or COLOR3B.C10)
    ext_emp_count_str = ToColorStr(ext_emp_count, ext_emp_count > 0 and COLOR3B.C8 or COLOR3B.C10)

    if IsEmptyTable(next_level_cfg) then
        desc_str = string.format(Language.BossKillEvery.LevelDesc, cur_max_kill_str, ext_count_str, ext_emp_count_str)
    else
        next_max_kill_str = ToColorStr(next_level_cfg.everyday_max_kill_count,
            next_level_cfg.everyday_max_kill_count > 0 and COLOR3B.C8 or COLOR3B.C10)

        desc_str = string.format(Language.BossKillEvery.LevelDesc2,
            cur_max_kill_str, next_max_kill_str, ext_count_str, ext_count_str, ext_emp_count_str, ext_emp_count_str)
    end

    --傻逼谢子鹏要将所有的等级信息全部屏蔽，不展示给任何人知道当前多少级.
    -- self.node_list.cur_level.text.text = string.format(Language.BossKillEvery.CurLvel, cur_level)
    self.node_list.cur_level_desc.text.text = desc_str

    self.node_list.next_level:SetActive(not IsEmptyTable(next_level_cfg))
    -- self.node_list.next_level.text.text = string.format(Language.BossKillEvery.NextLvel, cur_level + 1)

    self.node_list.miaosha_tips_txt.text.text = Language.BossKillEvery.ClearTimeHint
    self.node_list.title_text.text.text = Language.BossKillEvery.TitleText
end

function BossNewPrivilegeView:TaskPanelInfo()
    local cur_level = BossKillEveryWGData.Instance:GetCurLevel()
    local next_level_cfg = BossKillEveryWGData.Instance:GetCurLevelCfg(cur_level + 1)

    self.node_list.not_task_desc:SetActive(IsEmptyTable(next_level_cfg))

    if IsEmptyTable(next_level_cfg) then
        self.node_list.next_level_task_desc.text.text = ""
        self.node_list.not_task_desc.text.text = Language.BossKillEvery.AllCompleteTask
        self.node_list.next_level_task.text.text = Language.BossKillEvery.NextLevelTask1
    else
        -- self.node_list.next_level_task.text.text = string.format(Language.BossKillEvery.NextLevelTask, cur_level + 1)
        self.node_list.next_level_task.text.text = Language.BossKillEvery.NextLevelTask2
        local role_level = RoleWGData.Instance:GetRoleLevel()
        local condition_num = BossKillEveryWGData.Instance:GetCurConditionNum()
        local condition_cfg = string.split(next_level_cfg.condition, "|")
        local need_level = condition_cfg[1] and tonumber(condition_cfg[1]) or 0
        local need_kill_boss = condition_cfg[2] and tonumber(condition_cfg[2]) or 0

        local level_color = role_level >= need_level and COLOR3B.C8 or COLOR3B.C10
        local boss_color = condition_num >= need_kill_boss and COLOR3B.C8 or COLOR3B.C10
        self.node_list.next_level_task_desc.text.text = string.format(Language.BossKillEvery.TaskDesc,
            ToColorStr(role_level, level_color),
            need_level, ToColorStr(condition_num, boss_color), need_kill_boss)
    end
end

function BossNewPrivilegeView:OnClickOpenMiaoShaBtn()
    local cur_level = BossKillEveryWGData.Instance:GetCurLevel()
    if BossKillEveryWGData.Instance:GetMiaoShaOpenFlag() == 0 and cur_level <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.BossKillEvery.NotAct)
        return
    end

    local is_open = BossKillEveryWGData.Instance:GetMiaoShaOpenFlag() == 0 and 1 or 0
    BossPrivilegeWGCtrl.Instance:SendChangeEveryDayKillBossFlagSeq(is_open)
end

function BossNewPrivilegeView:OnClickOpenAutoKillBtn()
    local cur_level = BossKillEveryWGData.Instance:GetCurLevel()
    if BossKillEveryWGData.Instance:GetMiaoShaOpenFlag() == 0 and cur_level <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.BossKillEvery.NotAct)
        return
    end

    local auto_kill_flag = BossKillEveryWGData.Instance:GetAutoKillBossFlag() == 0 and 1 or 0
    BossPrivilegeWGCtrl.Instance:SendChangeAutoKillBossFlagSeq(auto_kill_flag)
end