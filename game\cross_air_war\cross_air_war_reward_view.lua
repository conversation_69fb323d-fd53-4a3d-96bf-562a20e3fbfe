CrossAirWarRewardView = CrossAirWarRewardView or BaseClass(SafeBaseView)

local SelectState = {
    Score = 1,
    Rank = 2,
	Time_Score = 3,
	Stage = 4,
}

function CrossAirWarRewardView:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(830, 560)})
	self:AddViewResource(0, "uis/view/cross_air_war_ui_prefab", "layout_kf_air_war_reward")
	self:SetMaskBg(true)
end

function CrossAirWarRewardView:__delete()
end

function CrossAirWarRewardView:SetOpenData(is_show_person_score)
	self.is_show_person_score = is_show_person_score
end

function CrossAirWarRewardView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.ViewName.act_jjc_yulan_reward
	if not self.person_list then
		self.person_list = AsyncListView.New(CrossAirWarPersonRewardItem, self.node_list["person_list"])
		self.person_list:SetStartZeroIndex(true)
	end

	if not self.rank_list then
		self.rank_list = AsyncListView.New(CrossAirWarRankRewardItem, self.node_list["rank_list"])
	end

	if not self.stage_list then
		self.stage_list = AsyncListView.New(CrossAirWarStageRewardItem, self.node_list["stage_list"])
	end

	if not self.time_score_list then
		self.time_score_list = AsyncListView.New(CrossAirWarTimeScoreRewardItem, self.node_list["time_score_list"])
	end

	self.node_list["btn_person"].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickSwitch, self, SelectState.Score))
	self.node_list["btn_rank_moster"].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickSwitch, self, SelectState.Rank))
	self.node_list["btn_rank_time_score"].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickSwitch, self, SelectState.Time_Score))
	self.node_list["btn_rank_stage"].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickSwitch, self, SelectState.Stage))
	self:OnClickSwitch(SelectState.Score)
end

function CrossAirWarRewardView:ReleaseCallBack()
	if self.person_list then
		self.person_list:DeleteMe()
		self.person_list = nil
	end
	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end

	if self.stage_list then
		self.stage_list:DeleteMe()
		self.stage_list = nil
	end

	if self.time_score_list then
		self.time_score_list:DeleteMe()
		self.time_score_list = nil
	end
end

function CrossAirWarRewardView:ShowIndexCallBack()
	self.node_list["btn_person"].toggle.isOn = true
end

function CrossAirWarRewardView:OnFlush()
	self.node_list.person_score:CustomSetActive(self.is_show_person_score)

	if self.is_show_person_score then
		local score = CrossAirWarWGData.Instance:GetPlayerWarScore()
		self.node_list.person_score.text.text = string.format("%s%s", Language.ChessboardField.AccumulateDesc, score)
	end
end

function CrossAirWarRewardView:OnClickSwitch(state)
    if state == SelectState.Score then
        local data_list = CrossAirWarWGData.Instance:GetScoreRewardCfg()
        self.person_list:SetDataList(data_list)
    elseif state == SelectState.Rank then
        local data_list = CrossAirWarWGData.Instance:GetAllRankRewardList()
        self.rank_list:SetDataList(data_list)
	elseif state == SelectState.Stage then
		local data_list = CrossAirWarWGData.Instance:GetBossDropShowCfg()
        self.stage_list:SetDataList(data_list)	
	elseif state == SelectState.Time_Score then
		local data_list = CrossAirWarWGData.Instance:GetScoringListRewardCfg()
        self.time_score_list:SetDataList(data_list)	
    end
end

-----------------------CrossAirWarPersonRewardItem-----------------
CrossAirWarPersonRewardItem = CrossAirWarPersonRewardItem or BaseClass(BaseRender)

function CrossAirWarPersonRewardItem:__init()
	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell,self.node_list["reward_list"])
		self.reward_list:SetStartZeroIndex(true)
	end
end

function CrossAirWarPersonRewardItem:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function CrossAirWarPersonRewardItem:OnFlush()
	if not self.data then return end 
	if self.data.reward_item then
		self.reward_list:SetDataList(self.data.reward_item)
	end

	self.node_list["text"].text.text = string.format(Language.CrossAirWar.ScoreStr1, self.data.need_score)
end

-----------------------CrossAirWarRankRewardItem-----------------
CrossAirWarRankRewardItem = CrossAirWarRankRewardItem or BaseClass(BaseRender)

function CrossAirWarRankRewardItem:__init()
	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell,self.node_list["reward_list"])
		self.reward_list:SetStartZeroIndex(true)
	end
end

function CrossAirWarRankRewardItem:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function CrossAirWarRankRewardItem:OnFlush()
	if not self.data then return end

	local cfg = self.data.cfg
	local reward_item = cfg.reward_item
	self.reward_list:SetDataList(reward_item)

	local moster_cfg = CrossAirWarWGData.Instance:GetMonsterCfgBySeq(cfg.monster_seq)
	if moster_cfg ~= nil then
		self.node_list.stage_title.text.text = moster_cfg.stage_name
	end

	local rank_str = ""
	if cfg.min_rank == cfg.max_rank then
		rank_str = cfg.min_rank
	else
		rank_str = cfg.min_rank .. "-" .. cfg.max_rank
	end

	self.node_list.stage_desc.text.text = string.format(Language.CrossAirWar.RankStr, rank_str)
end

-----------------------CrossAirWarStageRewardItem-----------------
CrossAirWarStageRewardItem = CrossAirWarStageRewardItem or BaseClass(BaseRender)

function CrossAirWarStageRewardItem:__init()
	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell,self.node_list["reward_list"])
		self.reward_list:SetStartZeroIndex(true)
	end
end

function CrossAirWarStageRewardItem:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function CrossAirWarStageRewardItem:OnFlush()
	if not self.data then return end 
	if self.data.reward_item then
		self.reward_list:SetDataList(self.data.reward_item)
	end

	local stage = self.data.show_stage
	local phase_show_num = NumberToChinaNumber(stage)
	local stage_str = string.format(Language.CrossAirWar.PhaseStr1, phase_show_num)
	local str = string.format("%s·%s", stage_str, self.data.stage_show_title)
	self.node_list.stage_title.text.text = str
	self.node_list.stage_desc.text.text = self.data.stage_show_txt
end

-----------------------CrossAirWarTimeScoreRewardItem-----------------
CrossAirWarTimeScoreRewardItem = CrossAirWarTimeScoreRewardItem or BaseClass(BaseRender)
function CrossAirWarTimeScoreRewardItem:__init()
	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell,self.node_list["reward_list"])
		self.reward_list:SetStartZeroIndex(true)
	end
end

function CrossAirWarTimeScoreRewardItem:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function CrossAirWarTimeScoreRewardItem:OnFlush()
	if not self.data then return end

	local reward_item = self.data.reward_item
	self.reward_list:SetDataList(reward_item)

	local moster_cfg = CrossAirWarWGData.Instance:GetMonsterCfgBySeq(self.data.monster_seq)
	local rect_str = ""

	if self.data.min_time ~= nil and self.data.min_time == 0 then
		rect_str = string.format("(<%ds)", self.data.time)
	else
		rect_str = string.format("(%ds~%ds)", self.data.min_time, self.data.time)
	end

	rect_str = ToColorStr(rect_str, COLOR3B.WHITE)
	if moster_cfg ~= nil then
		self.node_list.stage_desc.text.text = string.format("%s%s", moster_cfg.stage_name, rect_str)
	end

	self.node_list.stage_title.image:LoadSprite(ResPath.GetCommon(string.format("a3_fb_ysz_%d", self.data.scoring_index)))
end

