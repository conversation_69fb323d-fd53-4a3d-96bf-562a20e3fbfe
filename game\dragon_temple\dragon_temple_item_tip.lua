DragonTempleItemTip = DragonTempleItemTip or BaseClass(SafeBaseView)

function DragonTempleItemTip:__init()
	self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Pop
	self.label_t = Language.Tip.ButtonLabel
	self.data = CommonStruct.ItemDataWrapper()
	self:InitLoadUIConfig()
end

function DragonTempleItemTip:InitLoadUIConfig()
	self:AddViewResource(0, "uis/view/dragon_temple_ui_prefab", "layout_dragon_temple_itemtip")
end

function DragonTempleItemTip:__delete()

end

function DragonTempleItemTip:SetDataAndOpen(data)
	self.data = data
    if not self:IsOpen() then
        self:Open()
    else
        self:Flush()
    end
end

function DragonTempleItemTip:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list["cell_pos"])
		self.item_cell:SetIsShowTips(false)
	end

	if not self.level_item_cell then
		self.level_item_cell = ItemCell.New(self.node_list["level_cost_pos"])
		self.level_item_cell:SetIsShowTips(false)
	end

	if not self.grade_item_cell then
		self.grade_item_cell = ItemCell.New(self.node_list["jinjie_cost_pos"])
		self.grade_item_cell:SetIsShowTips(false)
	end

	if not self.wear_equip_list then
		self.wear_equip_list = {}
		for i = 1, 5 do
			self.wear_equip_list[i] = DragonTipWearEquipCell.New(self.node_list["wear_equip_" .. i])
			self.wear_equip_list[i]:SetIndex(i)
		end
	end

	if not self.suit_attr_list then
		self.suit_attr_list = {}
		for i = 1, 5 do
			self.suit_attr_list[i] = DragonTipSuitAttrRender.New(self.node_list["suit_act_cell_" .. i], self)
		end
	end

	XUI.AddClickEventListener(self.node_list["level_up_btn"], BindTool.Bind(self.OnClickUpLevel, self))
	XUI.AddClickEventListener(self.node_list["jinjie_up_btn"], BindTool.Bind(self.OnClickUpGrade, self))
end

function DragonTempleItemTip:ReleaseCallBack()
	self.data = nil

	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

	if self.level_item_cell then
		self.level_item_cell:DeleteMe()
		self.level_item_cell = nil
	end

	if self.grade_item_cell then
		self.grade_item_cell:DeleteMe()
		self.grade_item_cell = nil
	end

	if self.wear_equip_list then
		for k, v in pairs(self.wear_equip_list) do
			v:DeleteMe()
		end
		self.wear_equip_list = nil
	end

	if self.suit_attr_list then
		for k, v in pairs(self.suit_attr_list) do
			v:DeleteMe()
		end
		self.suit_attr_list = nil
	end
end

function DragonTempleItemTip:CloseCallBack()
	self.data = nil
end

function DragonTempleItemTip:ShowIndexCallBack()

end

function DragonTempleItemTip:OnFlush(param_t, index)
	if not self.data then
		return
	end

	self:InitPanelState()
	self:ShowTipTopContent()
	self:ShowTipMidContent()
	self:ShowTipUpPanel()
	self:ShowRightPanel()
end
	
function DragonTempleItemTip:InitPanelState()
	self.node_list.add_attribut:SetActive(false)
	self.node_list.up_panel:SetActive(false)
	self.node_list.level_panel:SetActive(false)
	self.node_list.jinjie_panel:SetActive(false)
	self.node_list.suit_attribute:SetActive(false)
	self.node_list.suit_skill:SetActive(false)
end

function DragonTempleItemTip:ShowTipTopContent()
	self.item_cell:SetData({item_id = self.data.item_id})

	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if item_cfg then
		self.node_list.item_name.text.text = item_cfg.name
		local color = item_cfg.color or 1
		self.node_list["top_color_bg"].raw_image:LoadSprite(ResPath.GetRawImagesPNG("a3_ty_tips_top_" .. color))
		self.node_list["ornament_panel"]:SetActive(color >= GameEnum.ITEM_COLOR_PINK)
	end

	local level = 1
	if self.data.from_view ==ItemTip.FROM_LONGSHEN_EQUIP then
		--激活显示槽位等级,没激活显示1级(没激活是0级)
		local info = DragonTempleWGData.Instance:GetSoltEquipInfoBySolt(self.data.solt)
		if info and info.level > 0 then
			level = info.level
		end
	end

	self.node_list.item_synthetical_socre.text.text = string.format(Language.DragonTemple.TipLevel, level)
	self.node_list.item_equip_socre.text.text = string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE, Language.DragonTemple.EquipTypeName[self.data.type])
end

function DragonTempleItemTip:ShowTipMidContent()
	local solt_info = DragonTempleWGData.Instance:GetSoltEquipInfoBySolt(self.data.solt)
	if IsEmptyTable(solt_info) then
		return
	end

	--默认拿数据等级1级，进阶0阶
	local level = 1
	local grade = 0
	if self.data.from_view == ItemTip.FROM_LONGSHEN_EQUIP then
		local cur_attr_list, attr_cap = DragonTempleWGData.Instance:GetEquipAllAttrAndCap(solt_info.solt, solt_info.level, solt_info.grade)
		self.node_list.cap_value.text.text = attr_cap
		self:ShowBaseAttrInfo(cur_attr_list)
		self:ShowNextAttrInfo()
	else
		local cur_attr_list, attr_cap = DragonTempleWGData.Instance:GetEquipAllAttrAndCap(self.data.solt, level, grade)
		self.node_list.cap_value.text.text = attr_cap
		self:ShowBaseAttrInfo(cur_attr_list)
	end

	local state_str = solt_info.level > 0 and Language.DragonTemple.IsActive or Language.DragonTemple.NotActive
	self.node_list.base_title_label.text.text = string.format("%s%s", Language.DragonTemple.TipBaseTitle, state_str)
end

function DragonTempleItemTip:ShowBaseAttrInfo(cur_attr_list)
	local sort_list = AttributeMgr.SortAttribute()
	-- 属性显示
	local index = 1
	local is_per

	for k,v in ipairs(sort_list) do
		if cur_attr_list[v] ~= 0 then
			is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v)
			self.node_list["base_label_" .. index].text.text = EquipmentWGData.Instance:GetAttrNameByAttrStr(v)
			self.node_list["base_num_" .. index].text.text = is_per and cur_attr_list[v] / 100 .. "%" or cur_attr_list[v]
			index = index + 1
		end
	end

	for i = 1, 5 do
		self.node_list["base_label_" .. i]:SetActive(index > i)
	end
end

function DragonTempleItemTip:ShowNextAttrInfo()
	local solt_info = DragonTempleWGData.Instance:GetSoltEquipInfoBySolt(self.data.solt)
	if IsEmptyTable(solt_info) then
		return
	end

	local next_attr_list = {}
	local is_can_upgrade = DragonTempleWGData.Instance:IsCanUpGrade(self.data.solt)
	if is_can_upgrade then -- 走进阶
		local next_grade_cfg = DragonTempleWGData.Instance:GetEquipGradeCfg(self.data.solt, solt_info.grade + 1)
		if next_grade_cfg then
			self.node_list.add_attribut:SetActive(true)
			self.node_list.up_panel:SetActive(true)
			self.node_list.jinjie_panel:SetActive(true)
			self.node_list.add_title_label.text.text = Language.DragonTemple.TipGradeTitle
			next_attr_list = DragonTempleWGData.Instance:GetEquipAllAttrAndCap(self.data.solt, solt_info.level, solt_info.grade + 1)
		end
	else
		local next_level_cfg = DragonTempleWGData.Instance:GetEquipLevelCfg(self.data.solt, solt_info.level + 1)
		if next_level_cfg then
			self.node_list.add_attribut:SetActive(true)
			self.node_list.up_panel:SetActive(true)
			self.node_list.level_panel:SetActive(true)
			self.node_list.add_title_label.text.text = Language.DragonTemple.TipJiTitle
			next_attr_list = DragonTempleWGData.Instance:GetEquipAllAttrAndCap(self.data.solt, solt_info.level + 1, solt_info.grade)
		end
	end

	if not IsEmptyTable(next_attr_list) then
		local sort_list = AttributeMgr.SortAttribute()
		-- 属性显示
		local index = 1
		local is_per

		for k,v in ipairs(sort_list) do
			if next_attr_list[v] ~= 0 then
				is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v)
				self.node_list["add_label_" .. index].text.text = EquipmentWGData.Instance:GetAttrNameByAttrStr(v)
				self.node_list["add_num_" .. index].text.text = is_per and next_attr_list[v] / 100 .. "%" or next_attr_list[v]
				index = index + 1
			end
		end

		for i = 1, 5 do
			self.node_list["add_label_" .. i]:SetActive(index > i)
		end
	end
end

function DragonTempleItemTip:ShowTipUpPanel()
	local solt_info = DragonTempleWGData.Instance:GetSoltEquipInfoBySolt(self.data.solt)
	if IsEmptyTable(solt_info) then
		return
	end

	self.node_list.level_up_red:SetActive(false)
	self.node_list.jinjie_up_red:SetActive(false)

	if self.data.from_view == ItemTip.FROM_LONGSHEN_EQUIP then
		local cur_level_cfg = DragonTempleWGData.Instance:GetEquipLevelCfg(self.data.solt, solt_info.level)
		local next_level_cfg = DragonTempleWGData.Instance:GetEquipLevelCfg(self.data.solt, solt_info.level + 1)
		if next_level_cfg and cur_level_cfg then
			self.level_item_cell:SetData({item_id = cur_level_cfg.cost_item_id})
			local cost_num = DragonTempleWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
			local color = cost_num >= cur_level_cfg.cost_item_num and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED
			local up_level_str = cost_num .. "/" .. cur_level_cfg.cost_item_num
			self.node_list.level_cost_num.text.text = ToColorStr(up_level_str, color)
			self.node_list.level_up_red:SetActive(cost_num >= cur_level_cfg.cost_item_num)
		end

		local cur_grade_cfg = DragonTempleWGData.Instance:GetEquipGradeCfg(self.data.solt, solt_info.grade)
		local next_grade_cfg = DragonTempleWGData.Instance:GetEquipGradeCfg(self.data.solt, solt_info.grade + 1)
		if next_grade_cfg and cur_grade_cfg then
			self.grade_item_cell:SetData({item_id = cur_grade_cfg.cost_item_id})
			local cost_num = DragonTempleWGData.Instance:GetItemNumInBagById(cur_grade_cfg.cost_item_id)
			local color = cost_num >= cur_grade_cfg.cost_item_num and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED
			local up_grade_str = cost_num .. "/" .. cur_grade_cfg.cost_item_num
			self.node_list.jinjie_cost_num.text.text = ToColorStr(up_grade_str, color)
			self.node_list.jinjie_up_red:SetActive(cost_num >= cur_grade_cfg.cost_item_num and solt_info.level >= cur_grade_cfg.need_level)
		end
	end
end

function DragonTempleItemTip:OnClickUpLevel()
	if not self.data then
		return
	end
	
	local solt_info = DragonTempleWGData.Instance:GetSoltEquipInfoBySolt(self.data.solt)
	if IsEmptyTable(solt_info) then
		return
	end

	local cur_level_cfg = DragonTempleWGData.Instance:GetEquipLevelCfg(self.data.solt, solt_info.level)
	local next_level_cfg = DragonTempleWGData.Instance:GetEquipLevelCfg(self.data.solt, solt_info.level + 1)
	if next_level_cfg and cur_level_cfg then
		local cost_num = DragonTempleWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
		if cost_num >= cur_level_cfg.cost_item_num then
			DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.SOLT_LEVEL_UP, self.data.solt)
    	else
    		TipWGCtrl.Instance:ShowSystemMsg(Language.DragonTemple.CostNotEnough)
    	end
	end
end

function DragonTempleItemTip:OnClickUpGrade()
	if not self.data then
		return
	end

	local solt_info = DragonTempleWGData.Instance:GetSoltEquipInfoBySolt(self.data.solt)
	if IsEmptyTable(solt_info) then
		return
	end

	local cur_grade_cfg = DragonTempleWGData.Instance:GetEquipGradeCfg(self.data.solt, solt_info.grade)
	local next_grade_cfg = DragonTempleWGData.Instance:GetEquipGradeCfg(self.data.solt, solt_info.grade + 1)
	if next_grade_cfg and cur_grade_cfg then
		local cost_num = DragonTempleWGData.Instance:GetItemNumInBagById(cur_grade_cfg.cost_item_id)
		if cost_num >= cur_grade_cfg.cost_item_num and solt_info.level >= cur_grade_cfg.need_level then
			DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.SOLT_GRADE_UP, self.data.solt)
    	elseif solt_info.level < cur_grade_cfg.need_level then
    		TipWGCtrl.Instance:ShowSystemMsg(Language.DragonTemple.LevelNotEnough)
    	else
    		TipWGCtrl.Instance:ShowSystemMsg(Language.DragonTemple.CostNotEnough)
    	end
	end
end

function DragonTempleItemTip:ShowRightPanel()
	self:ShowTipWearEquipInfo()
	self:ShowSuitInfo()
	self:ShowSuitSkill()
end

function DragonTempleItemTip:ShowTipWearEquipInfo()
	local solt_info = DragonTempleWGData.Instance:GetSoltEquipInfo()
	local cur_wear_info = {}
    for i, v in pairs(solt_info) do
    	if v.type == self.data.type then
    		table.insert(cur_wear_info, v)
    	end
    end

    if not IsEmptyTable(cur_wear_info) then
    	local list_data = {}
    	local act_num = 0
		local num = 0
		local a, b = 0, 0
		for k, v in pairs (cur_wear_info) do
			num = num + 1
			a = math.ceil(num / 2)
			b = num % 2
			b = b == 0 and 2 or b
			if not list_data[a] then
				list_data[a] = {}
			end

			list_data[a][b] = v
			if v.level > 0 then
				act_num = act_num + 1
			end
		end

		local act_num_str = "(" .. act_num .. "/" .. #cur_wear_info .. ")"
		self.node_list.wear_equip_name.text.text = string.format("%s%s",Language.DragonTemple.EquipTypeName[self.data.type], act_num_str)

		for k, v in ipairs(self.wear_equip_list) do
			v:SetData(list_data[k] or {})
			v:SetActive(not IsEmptyTable(list_data[k]))
		end
    end
end

function DragonTempleItemTip:ShowSuitInfo()
	local suit_info = DragonTempleWGData.Instance:GetEquipSuitCfgByType(self.data.type)
	if not IsEmptyTable(suit_info) then
		self.node_list.suit_attribute:SetActive(true)
		for k, v in ipairs(self.suit_attr_list) do
			local data = suit_info[k]
			if data then
				self.node_list["suit_act_cell_" .. k]:SetActive(true)
				v:SetData(data)
			else
				self.node_list["suit_act_cell_" .. k]:SetActive(false)
			end
		end

		local act_num, cur_wear_info = DragonTempleWGData.Instance:GetEquipActNumBy(self.data.type)
		local act_num_str = "(" .. act_num .. "/" .. #cur_wear_info .. ")"
		self.node_list.suit_title_label.text.text = string.format("%s%s",Language.DragonTemple.EquipTypeName[self.data.type], act_num_str)
	end
end

function DragonTempleItemTip:ShowSuitSkill()
	local skill_cfg = DragonTempleWGData.Instance:GetEquipSkillByType(self.data.type)
	if skill_cfg then
		self.node_list.suit_skill:SetActive(true)
		local act_num = DragonTempleWGData.Instance:GetEquipActNumBy(self.data.type)
		local state_str = act_num >= skill_cfg.active_need_num and Language.DragonTemple.IsActive or Language.DragonTemple.NotActive
		local name_str = string.format("%s%s", skill_cfg.skill_name, state_str)
		self.node_list.suit_skill_name.text.text = name_str
		self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(skill_cfg.skill_id))
		self.node_list.skill_desc.text.text = skill_cfg.skill_desc
	end
end

function DragonTempleItemTip:GetViewForm()
	return self.data.from_view
end

-- 使用特效
function DragonTempleItemTip:PlayEffect(effect_name)
	TipWGCtrl.Instance:ShowEffect({effect_type = effect_name, is_success = true, pos = Vector2(0, 0),
						parent_node = self.node_list["effect_pos"]})
end
------------------------------DragonTipWearEquipCell---------------------
DragonTipWearEquipCell = DragonTipWearEquipCell or BaseClass(BaseRender)

function DragonTipWearEquipCell:__init()

end

function DragonTipWearEquipCell:__delete()

end

function DragonTipWearEquipCell:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	for i = 1, 2 do
		local info = self.data[i]
		if not IsEmptyTable(info) then
			self.node_list["wear_equip_name_" .. i]:SetActive(true)
			local color = info.level ~= 0 and COLOR3B.D_GREEN or COLOR3B.GRAY
			local item_cfg = ItemWGData.Instance:GetItemConfig(info.cost_item_id)
    		local name_str = item_cfg and item_cfg.name or ""
    		self.node_list["wear_equip_name_" .. i].text.text = ToColorStr(name_str, color)
		else
			self.node_list["wear_equip_name_" .. i]:SetActive(false)
		end
	end
end

-------------------------------DragonTipSuitAttrRender------------------------------
DragonTipSuitAttrRender = DragonTipSuitAttrRender or BaseClass(BaseRender)
function DragonTipSuitAttrRender:__init(instance, parent)
    self.attr_list = {}
    local attr_num = self.node_list.attr_list.transform.childCount
    for i = 1, attr_num do
        self.attr_list[i] = self.node_list.attr_list:FindObj("attr_" .. i)
    end

    XUI.AddClickEventListener(self.node_list["act_suit_btn"], BindTool.Bind(self.OnClickActSuit, self))
    self.parent = parent
end

function DragonTipSuitAttrRender:__delete()
    self.attr_list = nil
    self.parent = nil
end

function DragonTipSuitAttrRender:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
        self.view:SetActive(false)
        return
    else
        self.view:SetActive(true)
	end

	local cur_suit_level = DragonTempleWGData.Instance:GetSuitLevelByType(data.solt_type)
	local act_num = DragonTempleWGData.Instance:GetEquipActNumBy(data.solt_type)
	local is_act = cur_suit_level >= data.level
	if self.node_list.suit_icon then
        XUI.SetGraphicGrey(self.node_list.suit_icon, not is_act)
    end

    local from_view = self.parent:GetViewForm()
    local show_act_btn = not is_act and (act_num >= data.need_num) and (cur_suit_level + 1 == data.level) and (from_view == ItemTip.FROM_LONGSHEN_EQUIP)

    self.node_list.act_suit_btn:SetActive(show_act_btn)
    local attri_color = is_act and COLOR3B.DEFAULT_NUM or COLOR3B.GRAY
    local need_str = string.format(Language.DragonTemple.SuitNumCompany, data.need_num)
    self.node_list.need_num.text.text = need_str--ToColorStr(need_str, attri_color)
    local list = DragonTempleWGData.Instance:GetEquipSuitAttrList(data.solt_type, data.level)
    for k, v in ipairs(self.attr_list) do
        if list[k] then
            local is_per = not EquipmentWGData.Instance:GetAttrIsPerByAttrStr(list[k].attr_str)
            local name = EquipmentWGData.Instance:GetAttrName(list[k].attr_str, true)
            local value = is_per and list[k].attr_value or list[k].attr_value / 100 .. "%"
            local attr_str = string.format("%s   %s", name, value)
            v.text.text = ToColorStr(attr_str, attri_color)
            v:SetActive(true)
        else
            v:SetActive(false)
        end
    end
end

function DragonTipSuitAttrRender:OnClickActSuit()
	if IsEmptyTable(self.data) then
		return
	end

	local cur_suit_level = DragonTempleWGData.Instance:GetSuitLevelByType(self.data.solt_type)
	local from_view = self.parent:GetViewForm()
	local act_num = DragonTempleWGData.Instance:GetEquipActNumBy(self.data.solt_type)
	local is_act = cur_suit_level >= self.data.level
	local show_act_btn = not is_act and (act_num >= self.data.need_num) and (cur_suit_level + 1 == self.data.level) and (from_view == ItemTip.FROM_LONGSHEN_EQUIP)
	if show_act_btn then
		DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.SUIT_LEVEL_UP, self.data.solt_type)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.DragonTemple.NotCondition)
	end
end