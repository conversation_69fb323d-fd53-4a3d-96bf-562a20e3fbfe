-- 
BreakSkillShowView = BreakSkillShowView or BaseClass(SafeBaseView)

function BreakSkillShowView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self.skill_play_timestemp = 0
	self:SetMaskBg(false)
	self:SetMaskBgAlpha(0)

	self:AddViewResource(0, "uis/view/skill_show_ui_prefab", "layout_field_skill_show_view")
	self:AddViewResource(0, "uis/view/skill_show_ui_prefab", "layout_skill_desc_view")
end

function BreakSkillShowView:LoadCallBack()
	self:InitSkillInfo()
	self.cur_select_index = -1
	if not self.break_skill_show_list then
		self.break_skill_show_list = AsyncListView.New(SkillBreakRender, self.node_list["field_list_view"])
		self.break_skill_show_list:SetSelectCallBack(BindTool.Bind(self.OnSelectCustomized<PERSON>allBack, self))
		self.break_skill_show_list:SetLimitSelectFunc(BindTool.Bind(self.IsLimitClick, self))
	end

	if not self.skill_pre_view then
		self.skill_pre_view = SkillPreview.New(self.node_list.skill_pre_root, self.node_list.skill_pre_rawimage)
		self.skill_pre_view:SetPreviewPlayEndCb(BindTool.Bind1(self.PreviewPlayEnd, self))
	end
end

function BreakSkillShowView:InitSkillInfo()
	self.message_root_tween = self.node_list.skill_desc_root:GetComponent(typeof(UGUITweenPosition))
	XUI.AddClickEventListener(self.node_list.bag_forward_button, BindTool.Bind2(self.PlayBeastsMessagePositionTween, self, false))
	XUI.AddClickEventListener(self.node_list.bag_reverse_button, BindTool.Bind2(self.PlayBeastsMessagePositionTween, self, true))
end

-- 技能描述展示按钮.
function BreakSkillShowView:PlayBeastsMessagePositionTween(is_forward)
	self.node_list.bag_forward_button:CustomSetActive(is_forward)
	self.node_list.bag_reverse_button:CustomSetActive(not is_forward)

	if not IsNil(self.message_root_tween) then
		if is_forward then
			self.message_root_tween:PlayForward()
		else
			self.message_root_tween:PlayReverse()
		end
	end
end

-- 刷新技能描述.
function BreakSkillShowView:FlushSkillInfo(skill_id, skill_level, skill_name, skill_desc)
	local client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, skill_level)
	if client_cfg then
		self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(client_cfg.icon_resource))
		self.node_list.skill_nane.text.text = skill_name
		self.node_list.skill_desc.text.text = skill_desc
	end
end

-- 释放技能描述动画.
function BreakSkillShowView:ClearSkillInfo()
	if not IsNil(self.message_root_tween) then
		self.message_root_tween = nil
	end
end

function BreakSkillShowView:ReleaseCallBack()
	self.skill_data = nil
	self.cur_select_index = -1

	if self.break_skill_show_list then
		self.break_skill_show_list:DeleteMe()
		self.break_skill_show_list = nil
	end

	if self.skill_pre_view then
		self.skill_pre_view:DeleteMe()
		self.skill_pre_view = nil
	end
	self.skill_play_timestemp = 0

	self:ClearSkillInfo()
	self:RemovePreSkillDelayTimer()
end

function BreakSkillShowView:SetShowDataAndOpen(data)
	self.show_data = data
	self:Open()
end

function BreakSkillShowView:ShowIndexCallBack()

end

-- 列表选择返回
function BreakSkillShowView:OnSelectCustomizedCallBack(item)
	if self:IsLimitClick() then
		return
	end

	if nil == item or nil == item.data then
		return
	end

	if self.cur_select_index == item:GetIndex() then
		return
	end

	self.cur_select_index = item:GetIndex()
	self.skill_data = item.data
	local skill_data = item.data

	-- 技能按钮CD
	local skill_show_time = 4
	self.skill_play_timestemp = Status.NowTime + skill_show_time
	if self.skill_pre_view:GetPreviewIsLoaded() then
		self.skill_pre_view:PlaySkill(skill_data.skill_id)
	else
		self.skill_pre_view:SetPreviewLoadCb(function()
			self.skill_pre_view:PlaySkill(skill_data.skill_id)
		end)
	end
	self:PreviewPlayEnd()

	local desc = SkillWGData.Instance:GetSkillBreakDesc(skill_data.skill_id, skill_data.desc, false)
	self:FlushSkillInfo(skill_data.skill_id, 1, skill_data.skill_name, desc)

	local bundle, asset = ResPath.GetSkillIconById(skill_data.skill_icon_id)
	self.node_list["skill_icon"].image:LoadSpriteAsync(bundle, asset, function()
		self.node_list["skill_icon"].image:SetNativeSize()
	end)
end

function BreakSkillShowView:IsLimitClick(no_tips)
	local is_playing_skill = Status.NowTime < self.skill_play_timestemp
	if not no_tips and is_playing_skill then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Skill.ShowSkillLimitClickStr)
	end

	return is_playing_skill
end

function BreakSkillShowView:OnFlush()
	if not self.show_data then
		return
	end

	if self.break_skill_show_list then
		self.break_skill_show_list:SetDataList(self.show_data)
		self.break_skill_show_list:JumpToIndex(1)
	end
end

-- 刷新技能描述.
function BreakSkillShowView:FlushSkillInfo(skill_id, skill_level, skill_name, skill_desc)
	local client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, skill_level)
	if client_cfg then
		self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(client_cfg.icon_resource))
		self.node_list.skill_nane.text.text = skill_name
		self.node_list.skill_desc.text.text = skill_desc
	end
end

-- 移除回调
function BreakSkillShowView:RemovePreSkillDelayTimer()
	if self.show_pre_skill_delay_timer then
		GlobalTimerQuest:CancelQuest(self.show_pre_skill_delay_timer)
		self.show_pre_skill_delay_timer = nil
	end
end

-- 技能预览结束延迟4秒继续播放
function BreakSkillShowView:PreviewPlayEnd()
	self:RemovePreSkillDelayTimer()

	self.show_pre_skill_delay_timer = GlobalTimerQuest:AddDelayTimer(function()
		if not self.skill_data then
			return
		end

		if self.skill_pre_view and self.skill_pre_view:GetPreviewIsLoaded() then
			self.skill_pre_view:PlaySkill(self.skill_data.skill_id)
		end
	end, 4)
end

-- ===================================================================
SkillBreakRender = SkillBreakRender or BaseClass(BaseRender)

function SkillBreakRender:OnFlush()
	if self.data == nil then
		return
	end

	local bundle, asset = ResPath.GetSkillIconById(self.data.skill_icon_id)
	self.node_list.icon.image:LoadSprite(bundle, asset)
	self.node_list.name.text.text = self.data.skill_name
end

function SkillBreakRender:OnSelectChange(is_select)
	self.node_list.select_hl:SetActive(is_select)
end
