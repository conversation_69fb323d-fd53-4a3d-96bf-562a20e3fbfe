----------------------------------------------------
--周末boss
----------------------------------------------------
WeekendBossView = WeekendBossView or BaseClass(ActBaseViewTwo)

function WeekendBossView:__init(act_id)
	self.ui_config = {"uis/view/act_subview_ui_prefab","WeekendBossView"}
	self.config_tab = {
		{"layout_week_boss_view", {0}},
	}
	self.open_tween = nil
	self.close_tween = nil
	self.act_id = act_id or ServerActClientId.RAND_WEEKEND_BOSS
end

function WeekendBossView:__delete()
	if self.open_server_weekendboss_list ~= nil then 
		self.open_server_weekendboss_list:DeleteMe()
		self.open_server_weekendboss_list = nil
	end


	if CountDownManager.Instance:HasCountDown("weekendboss_next_status") then
		CountDownManager.Instance:RemoveCountDown("weekendboss_next_status")
	end

	if CountDownManager.Instance:HasCountDown("weekendboss_open_status") then
		CountDownManager.Instance:RemoveCountDown("weekendboss_open_status")
	end
end

function WeekendBossView:ReleaseCallBack()
	if self.reward_list ~= nil then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
	self.list_is_jum_top = true
	self.load_complete = nil
end

function WeekendBossView:LoadCallBack()
	self.reward_list = AsyncListView.New(WeekendBossItemRender,self.node_list["ph_login_gift_list"])
	self.load_complete = true
	self:RefreshView()
end

function WeekendBossView:RefreshView(param_list)
	if not self.load_complete then
		return
	end
	self:RefreshTopDesc()

	self.act_status = ServerActivityWGData.Instance:GetVersionActivityNowStatus(ACTIVITY_TYPE.RAND_WEEKEND_BOSS)
	if self.act_status == nil then return end
	self.node_list.ph_login_gift_list:SetActive(self.act_status.status == ACTIVITY_STATUS.OPEN)
	self.node_list.Img9_left_bot:SetActive(self.act_status.status ~= ACTIVITY_STATUS.OPEN)
	if self.act_status.status == ACTIVITY_STATUS.OPEN then
		local data_list = ServerActivityWGData.Instance:GetWeekendBossDataList()
		
		self.reward_list:SetDataList(data_list, 3)
	elseif self.act_status.status ~= ACTIVITY_STATUS.OPEN then
		if self.act_status.status ~= ACTIVITY_STATUS.STANDY then
			self.node_list.img_activity_time.text.text = "下轮活动倒计时"
		else
			self.node_list.img_activity_time.text.text = "活动开启倒计时"
		end
		self.node_list.lbl_end_time.text.text = TimeUtil.FormatSecondDHM8(math.floor(self.act_status.next_status_time - TimeWGCtrl.Instance:GetServerTime()))
		if CountDownManager.Instance:HasCountDown("weekendboss_next_status") then
			CountDownManager.Instance:RemoveCountDown("weekendboss_next_status")
		end
		CountDownManager.Instance:AddCountDown("weekendboss_next_status",
											   BindTool.Bind1(self.UpdateCountDownTime, self),
											   BindTool.Bind(self.CompleteCountDownTime, self, true),
											   self.act_status.next_status_time, nil, 0.5)
	end
end

-- 倒计时每次循环执行的函数
function WeekendBossView:UpdateCountDownTime(elapse_time, total_time)
	if self.node_list.lbl_end_time then
		self.node_list.lbl_end_time.text.text = TimeUtil.FormatSecondDHM8(math.floor(total_time - elapse_time))
		self:RefreshView()
	end
end
function WeekendBossView:CompleteCountDownTime(is_auto_fuhuo)
	self:RefreshView()
end

function WeekendBossView:RefreshTopDesc()
	local open_act_cfg = ServerActivityWGData.Instance:GetClientActCfg(ServerActClientId.RAND_WEEKEND_BOSS)
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(open_act_cfg.open_type)
	if act_info == nil then return end
	local star_str = TimeUtil.FormatSecond2MYHM(act_info.start_time)
	local end_str = TimeUtil.FormatSecond2MYHM(act_info.next_time)
	self.node_list.version_act_time.text.text = (star_str .. "----" .. end_str)
	--self.node_list.version_act_time.node:setColor(Str2C3b("007f18"))
	self.node_list.version_act_des.text.text = Language.OpenServer.ActShuoMing .. (open_act_cfg.top_desc)
	if CountDownManager.Instance:HasCountDown("weekendboss_open_status") then
		CountDownManager.Instance:RemoveCountDown("weekendboss_open_status")
	end

end


WeekendBossItemRender = WeekendBossItemRender or BaseClass(BaseRender)
function WeekendBossItemRender:__init()
end

function WeekendBossItemRender:__delete()
end

function WeekendBossItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_goto, BindTool.Bind1(self.OnClickGoto, self))
end

function WeekendBossItemRender:OnFlush()
	self.node_list.lbl_boss_count.text.text =(string.format(Language.Boss.LeftWeekendBoss, self.data.boss_num))
	local scene_cfg = ConfigManager.Instance:GetSceneConfig(self.data.scene_id)
	if scene_cfg == nil then return end
	self.node_list.lbl_map.text.text = (scene_cfg.name.."("..self.data.scene_level..")")

end

function WeekendBossItemRender:OnClickGoto()
	if self.data.scene_id ~= Scene.Instance:GetSceneId() then
		local scene_cfg = ConfigManager.Instance:GetSceneConfig(self.data.scene_id)
		if scene_cfg == nil then return end
		TaskWGCtrl.Instance:JumpFly(self.data.scene_id, scene_cfg.scenex, scene_cfg.sceney, true)
		ServerActivityWGCtrl.Instance:CloseActBanbenServerView()
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.HasInTargetScene)
	end
end
