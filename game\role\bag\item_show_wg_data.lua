--------------------------------------------------------
-- 游戏中的物品属性 装备合成进程 展示的数据管理
--------------------------------------------------------
ItemShowWGData = ItemShowWGData or BaseClass()

-- 策划要求这些个属性整个描述需要展示特殊颜色
local SPECIAL_COLOR_ATTR_ID = {
	[1] = 171, -- boss真伤
	[2] = 172, -- boss麻痹
	[3] = 173, -- boss秒杀
}

function ItemShowWGData:__init()
	if ItemShowWGData.Instance then
		ErrorLog("[ItemShowWGData] Attemp to create a singleton twice !")
	end

	self.cache_is_need_item_sys = {}
	ItemShowWGData.Instance = self
end

function ItemShowWGData:__delete()
    ItemShowWGData.Instance = nil
	self.sepceil_attr_namecolor_cache = nil
end

--------------------------------------------------------
-- 物品属性
--------------------------------------------------------
function ItemShowWGData:GetIsNeedSysAttr(item_id, str)
	if not str then
		return false, 0, 2, TIPS_CAP_SHOW.SHOW_CFG
	elseif type(str) == "string" and #str > 0 then
		local cache_data = self.cache_is_need_item_sys[item_id]
		if not cache_data then
			local str_list = Split(str, ",")
			cache_data = {
				sys_type = tonumber(str_list[1]),
				replace_idx = tonumber(str_list[2]),
				show_cap_type = tonumber(str_list[3]),
			}
			self.cache_is_need_item_sys[item_id] = cache_data
		end

		return true, cache_data.sys_type, cache_data.replace_idx, cache_data.show_cap_type
	elseif type(str) == "number" then
		return true, str, 2, TIPS_CAP_SHOW.SHOW_CFG
	end

	return false, 0, 2, TIPS_CAP_SHOW.SHOW_CFG
end


-- 【只获取属性描述】
-- prefix 属性与值的 间隔区分str
function ItemShowWGData:OnlyGetAttrDesc(item_id, name_color, value_color, prefix)
	self.only_get_attr_desc = true
	self.only_name_color = name_color
	self.only_value_color = value_color
	self.only_prefix = prefix

	local attr_desc = ""
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg == nil then
		self.only_get_attr_desc = nil
		self.only_name_color = nil
		self.only_value_color = nil
		self.only_prefix = nil
		return attr_desc
	end

	local show_data = {item_id = item_id}
	local need_get_sys, sys_type = ItemShowWGData.Instance:GetIsNeedSysAttr(item_id, item_cfg.sys_attr_cap_location)
    if need_get_sys then
        attr_desc = ItemShowWGData.Instance:GetItemAttrDescAndCap(show_data, sys_type)
    end

	self.only_get_attr_desc = nil
	self.only_name_color = nil
	self.only_value_color = nil
	self.only_prefix = nil
	return attr_desc
end

-- 【只获取属性列表】
function ItemShowWGData:OnlyGetAttrList(item_id)
	self.only_get_attr_list = true
	local _, __, attr_list = nil, nil, {}
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg == nil then
		self.only_get_attr_list = nil
		return attr_list
	end

	local show_data = {item_id = item_id}
	local need_get_sys, sys_type = ItemShowWGData.Instance:GetIsNeedSysAttr(item_id, item_cfg.sys_attr_cap_location)
    if need_get_sys then
		_, __, attr_list = ItemShowWGData.Instance:GetItemAttrDescAndCap(show_data, sys_type)
    end

	self.only_get_attr_list = nil
	return attr_list
end

-- 【只获取战力】
function ItemShowWGData:OnlyGetCapability(item_id)
	self.only_get_capability = true
	local _, capability = nil, 0
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg == nil then
		self.only_get_capability = nil
		return capability
	end

	local show_data = {item_id = item_id}
	local need_get_sys, sys_type = ItemShowWGData.Instance:GetIsNeedSysAttr(item_id, item_cfg.sys_attr_cap_location)
    if need_get_sys then
		_, capability = ItemShowWGData.Instance:GetItemAttrDescAndCap(show_data, sys_type)
    end

	self.only_get_capability = nil
	return capability
end


--需要客户端根据属性计算战力的物品
function ItemShowWGData.CalculateCapability(item_id, show_max_cap)
    local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg == nil then
		return 0, "", {}
	end

    local show_max_cap = show_max_cap or false
    local show_data = {item_id = item_id}
    local capability, attr_desc, attr_list = 0, "", {}
    local need_get_sys, sys_type = ItemShowWGData.Instance:GetIsNeedSysAttr(item_id, item_cfg.sys_attr_cap_location)
    if need_get_sys then
        attr_desc, capability, attr_list = ItemShowWGData.Instance:GetItemAttrDescAndCap(show_data, sys_type, show_max_cap)
	end

    return capability, attr_desc, attr_list
end

function ItemShowWGData:GetItemAttrDescAndCap(data, type, show_max_cap)
	local attr_desc = ""
	local capability = 0
	local max_length = 0
	local attr_list = {}

	if IsEmptyTable(data) or not type then
		return attr_desc, capability, attr_list
	end

	local item_id = data.item_id
	if type == ITEMTIPS_SYSTEM.SHJ_TUJIAN then
		attr_list, capability = self:GetSHJTuJianAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.FASHION then
		attr_list, capability = self:GetFashionAttrByData(item_id, show_max_cap)
	elseif type == ITEMTIPS_SYSTEM.TITLE then
		attr_list, capability = self:GetTitleAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.MOUNT then
		if show_max_cap then
			attr_list, capability = self:GetMountMaxAttrByData(item_id)
		else
			attr_list, capability = self:GetMountAttrByData(item_id)
		end
	elseif type == ITEMTIPS_SYSTEM.LING_CHONG then
		if show_max_cap then
			attr_list, capability = self:GetLingChongMaxAttrByData(item_id)
		else
			attr_list, capability = self:GetLingChongAttrByData(item_id)
		end
	elseif type == ITEMTIPS_SYSTEM.HUA_KUN then
		if show_max_cap then
			attr_list, capability = self:GetHuaKunMaxAttrByData(item_id)
		else
			attr_list, capability = self:GetHuaKunAttrByData(item_id)
		end
	elseif type == ITEMTIPS_SYSTEM.TS_HUANHUA then
		if show_max_cap then
			attr_list, capability, max_length = self:GetTianShenMaxAttrByData(item_id)
		else
			attr_list, capability, max_length = self:GetTianShenAttrByData(item_id)
		end
	elseif type == ITEMTIPS_SYSTEM.TS_SHENTONG then
		attr_list, capability = self:GetTSShenTongAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.TS_SHENQI then
		attr_list, capability = self:GetTSShenQiAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.BAO_SHI then
		attr_list, capability = self:GetBaoShiAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.SHOU_HU then
		attr_list, capability = self:GetShouHuAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.XIAN_WA then
		if show_max_cap then
			attr_list, capability = self:GetXianWaMaxAttrByData(item_id)
		else
			attr_list, capability = self:GetXianWaAttrByData(item_id)
		end
	elseif type == ITEMTIPS_SYSTEM.ZQ_SHUXINGDAN then
		attr_list, capability = self:GetZQShuXingDanAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.LC_SHUXINGDAN then
		attr_list, capability = self:GetLCShuXingDanAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.WG_SHUXINGDAN then
		attr_list, capability = self:GetWGShuXingDanAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.MING_WEN then
		attr_list, capability = self:GetMingWenAttrByData(data)
	elseif type == ITEMTIPS_SYSTEM.LONG_HUN then
		data.level = data.level or 1
		attr_list, capability = self:GetLongHunAttrByData(data)
	elseif type == ITEMTIPS_SYSTEM.SHEN_SHI then
		attr_list, capability = self:GetShenShiAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.BA_GUA then
		attr_list, capability = self:GetBaGuaAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.TOU_XIANG then
		attr_list, capability = self:GetTouXiangAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.LING_YU then
		attr_list, capability = self:GetLingYuAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.GOD_BOOK_CHIP then
		attr_list, capability = self:GetGodBookChipAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.LING_HE then
		attr_list, capability = self:GetTianShenLingHeAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.DUANLING_RUTI then
		attr_list, capability = self:GetDuanLingRuTiAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.ROLE_REFINING then
		attr_list, capability = self:GetRefiningAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.FIVE_ELEMENTS then
		attr_list, capability = self:GetFiveElementsAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.ARTIFACT then
		attr_list, capability = self:GetArtifactAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.CHARM then
		attr_list, capability = self:GetCharmAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.BACKGROUND then
		attr_list, capability = self:GetBackgroundAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.ZHIZUN then
		attr_list, capability = self:GetZhiZunAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.TS_HUAMO then
		attr_list, capability = self:GetTianShenHuaMoAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.CANGMING then
		attr_list, capability = self:GetCangMingAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.WUHUNZHENSHEN then
		attr_list, capability = self:GetWuHunAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.NEWFIGHTMOUNT then
		attr_list, capability = self:GetNewFightMountAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.DRAGON_TEMPLE_PELLET then
		attr_list, capability = self:GetDragonTemplePelletAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.ESOTERICA then
		attr_list, capability = self:GetEsotericaAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.BEAST then
		-- 圣兽碎片显示幻兽战力特殊处理
		local beast_id = item_id
		if ControlBeastsWGData.Instance:CheckIsHolyBeastUnlockItemId(item_id) then
			local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
			beast_id = (item_cfg or {}).param2
		end
		if beast_id then
			attr_list, capability = self:GetBeastsAttrByData(beast_id, data)
		end
	elseif type == ITEMTIPS_SYSTEM.Rumor then
		attr_list, capability = self:GetRumorAttrByData(item_id, data)
	elseif type == ITEMTIPS_SYSTEM.Mecha then
		attr_list, capability = self:GetMechaAttrByData(item_id, data)
	elseif type == ITEMTIPS_SYSTEM.SHJ_QQJYT then
		attr_list, capability = self:GetSHJQQJYTAttrByData(item_id, data)
	elseif type == ITEMTIPS_SYSTEM.TIANSHENSHUANGSHENG then
		attr_list, capability = self:GetShuangShengTianShenAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.CUSTOM_ACTION then
		attr_list, capability = self:GetCustomActionAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.WUHUN_SHUXINGDAN then
		attr_list, capability = self:GetWuHunShuXingDanAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.QIWEN_YIWU then
		attr_list, capability = self:GetQiWuAttrByData(item_id)
	elseif type == ITEMTIPS_SYSTEM.LONGSHENDIAN_YULONG then
		attr_list, capability = self:GetLongShenDianYuLongAttrByItemId(item_id)
	elseif type == ITEMTIPS_SYSTEM.MENGLING_SXS then
		attr_list, capability = self:GetMengLingSXSAttrByItemId(item_id)
	elseif type == ITEMTIPS_SYSTEM.CASTING_STONE then
		attr_list, capability = self:GetShiZhuangStoneAttrByItemId(item_id)
	elseif type == ITEMTIPS_SYSTEM.THUNDER_MANA then
		attr_list, capability = self:GetThunderManaAttrByItemId(item_id)
	elseif type == ITEMTIPS_SYSTEM.BEAST_SXD then
		attr_list, capability = self:GetBeastSXDAttrByItemId(item_id)
	elseif type == ITEMTIPS_SYSTEM.BEAST_SKIN then
		attr_list, capability = self:GetBeastSkinAttrByItemId(item_id)
	elseif type == ITEMTIPS_SYSTEM.SPIRIT then
		attr_list, capability = self:GetSpiritAttrByItemId(item_id)
	elseif type == ITEMTIPS_SYSTEM.MULTI_MOUNT then
		attr_list, capability = self:GetMultiMountAttrByItemId(item_id)
	end

	if self.only_get_attr_list or self.only_get_capability then
		return attr_desc, capability, attr_list
	end

	if type == ITEMTIPS_SYSTEM.MING_WEN or type == ITEMTIPS_SYSTEM.LONG_HUN then
		attr_desc = self:OutMingWenAndLongHunAttrListToStr(attr_list)
	elseif type == ITEMTIPS_SYSTEM.TS_HUANHUA then
		attr_desc = self:OutAttrListToStrByClass(attr_list, max_length)
	else
		attr_desc = self:OutAttrListToStr(attr_list)
	end

	return attr_desc, capability, attr_list
end

-- 属性信息整理输出
function ItemShowWGData.OutSortAttrList(temp_list)
    local attr_list = {}
    if IsEmptyTable(temp_list) then
        return attr_list
    end

    local sort_attribute = AttributeMgr.SortAttribute()
    for k,v in ipairs(sort_attribute) do
        if temp_list[v] and temp_list[v] > 0 then
            local data = {}
            data.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(v, true)
            data.value_str = AttributeMgr.PerAttrValue(v, temp_list[v])
            table.insert(attr_list, data)
        end
    end

    return attr_list
end

-- 配置为字符串属性 输出列表和战力
function ItemShowWGData:OutStrAttrListAndCapality(cfg)
    local attr_list = {}
    local capability = 0
    if IsEmptyTable(cfg) then
        return attr_list, capability
    end

    local temp_list = AttributeMgr.GetAttributteByClass(cfg)
    attr_list = ItemShowWGData.OutSortAttrList(temp_list)
	if not self.only_get_attr_desc and not self.only_get_attr_list then
    	capability = AttributeMgr.GetCapability(temp_list)
	end

    return attr_list, capability
end

-- 配置为属性id 输出列表和战力(属性id字符串， 属性value字符串, 属性数量读取开始索引， 属性数量读取结束索引)
function ItemShowWGData:OutStrAttrListAndCapalityByAttrId(cfg, type_key_str, value_key_str, read_start, read_end)
	local attr_list = {}
    local capability = 0
    if IsEmptyTable(cfg) or type_key_str == nil or value_key_str == nil then
        return attr_list, capability
    end

	read_start = read_start or 1
	read_end = read_end or 5

	local attr_list = {}
	local attribute = AttributePool.AllocAttribute()
	for i = read_start, read_end do
		local attr_id = cfg[type_key_str .. i]
		local attr_value = cfg[value_key_str .. i]
		if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
			local data = {}
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_id))
			data.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str, true)
            data.value_str = AttributeMgr.PerAttrValue(attr_str, attr_value)
			data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
			table.insert(attr_list, data)

			if attribute[attr_str] then
				attribute[attr_str] = attribute[attr_str] + attr_value
			end
		end
	end

	if not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

	if not self.only_get_attr_desc and not self.only_get_attr_list then
    	capability = AttributeMgr.GetCapability(attribute)
	end

	return attr_list, capability
end

function ItemShowWGData:OutStrSwornAttrListAndCapalityByAttrId(cfg, type_key_str, value_key_str, read_start, read_end)
	local attr_list = {}
    local capability = 0
    if IsEmptyTable(cfg) or type_key_str == nil or value_key_str == nil then
        return attr_list, capability
    end

	read_start = read_start or 1
	read_end = #cfg

	local attribute = AttributePool.AllocAttribute()
	for i = read_start, read_end do
		local attr_id = cfg[i][type_key_str]
		local attr_value = cfg[i][value_key_str]
		if attr_id and attr_value then
			local data = {}
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_id))
			data.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str, true)
            data.value_str = AttributeMgr.PerAttrValue(attr_str, attr_value)
			data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
			table.insert(attr_list, data)

			if attribute[attr_str] then
				attribute[attr_str] = attribute[attr_str] + attr_value
			end
		end
	end

	if not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

	if not self.only_get_attr_desc and not self.only_get_attr_list then
    	capability = AttributeMgr.GetCapability(attribute)
	end

	return attr_list, capability
end

-- 两份配置为字符串属性相加 输出列表和战力(两份配置都不能为空)
function ItemShowWGData:OutStrAttrListAndCapalityTwoMix(cfg1, cfg2)
    local attr_list = {}
    local capability = 0
    if IsEmptyTable(cfg1) or IsEmptyTable(cfg2) then
        return attr_list, capability
    end

    local temp_list_1 = AttributeMgr.GetAttributteByClass(cfg1)
    local temp_list_2 = AttributeMgr.GetAttributteByClass(cfg2)
    local mix_list = AttributeMgr.AddAttributeAttr(temp_list_1, temp_list_2)
    attr_list = ItemShowWGData.OutSortAttrList(mix_list)
	if not self.only_get_attr_desc and not self.only_get_attr_list then
    	capability = AttributeMgr.GetCapability(mix_list)
	end

    return attr_list, capability
end

-- 属性列表 输出成拼接字符
function ItemShowWGData:OutAttrListToStr(attr_list)
	local attr_desc = ""
	if IsEmptyTable(attr_list) then
		return attr_desc
	end

	local name_color = self.only_name_color or '#A8A5A4FF'
	local value_color = self.only_value_color or '#DCDCDCFF'
	local prefix = self.only_prefix or '   '
	local str_format = "<color=%s>%s</color>%s<color=%s>%s</color>%s"
	local length = #attr_list

	for k, v in ipairs(attr_list) do
		local huan_hang = k == length and "" or "\n"
		local attr_name = v.attr_name or EquipmentWGData.Instance:GetAttrName(v.attr_str, true, false)
		local special_color = self:SpecialAttrNameToColor(attr_name) or self:NotBaseAttrNameToColor(attr_name)	

		local value_str = v.value_str
		if value_str == nil then
			local is_per = v.is_per or EquipmentWGData.Instance:GetAttrIsPer(v.attr_str)
			local per_desc = is_per and "%" or ""
			value_str = (is_per and v.attr_value / 100 or v.attr_value) or 0
		end

		local need_special_color = nil ~= special_color
		local target_name_color = need_special_color and special_color or name_color
		local target_value_color = need_special_color and special_color or value_color

		attr_desc = attr_desc .. string.format(str_format, target_name_color, attr_name, prefix, target_value_color, value_str, huan_hang)
	end

	return attr_desc
end

-- 属性列表 输出成拼接字符
function ItemShowWGData:OutAttrListToStrByClass(attr_list)
	local attr_desc = ""
	if IsEmptyTable(attr_list) then
		return attr_desc
	end

	local new_attr_list = EquipWGData.GetSortAttrListHaveAddByCfg(attr_list)
	local length = #new_attr_list
	local name_color = self.only_name_color or '#A8A5A4FF'
	local value_color = self.only_value_color or '#DCDCDCFF'
	local prefix = self.only_prefix or '   '
	local str_format = "<color=%s>%s</color>%s<color=%s>%s</color>%s"

	for k, v in ipairs(new_attr_list) do
		local huan_hang = k == length and "" or "\n"
		local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v.attr_str)
		local special_color = SPECIAL_ATTR_COLOR[v.attr_str]
		local need_special_color = nil ~= special_color
		local target_name_color = need_special_color and special_color or name_color
		local target_value_color = need_special_color and special_color or value_color

		local value_str = ""
		if is_per and v.attr_value > 0 then
			value_str = (math.floor(v.attr_value) / 100) .. "%"
		else
			value_str = math.floor(v.attr_value)
		end
		attr_desc = attr_desc .. string.format(str_format, target_name_color, Language.Common.AttrNameList[v.attr_str], prefix, target_name_color, value_str, huan_hang)
	end

	return attr_desc
end

function ItemShowWGData:SpecialAttrNameToColor(attr_name)
	if not self.sepceil_attr_namecolor_cache then
		self.sepceil_attr_namecolor_cache = {}

		for k, v in pairs(SPECIAL_COLOR_ATTR_ID) do
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(v))
			local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str, true)

			self.sepceil_attr_namecolor_cache[attr_name] = SPECIAL_ATTR_COLOR[v]
		end
	end

	return self.sepceil_attr_namecolor_cache[attr_name]
end

function ItemShowWGData:NotBaseAttrNameToColor(attr_name)
	local is_base = EquipmentWGData.Instance:GetAttrIsBaseByAttrName(attr_name)
	return not is_base and '#ffe58c' or nil
end

-- 铭文、龙魂属性列表 输出成拼接字符
function ItemShowWGData:OutMingWenAndLongHunAttrListToStr(attr_list)
	local attr_desc = ""
	if IsEmptyTable(attr_list) then
		return attr_desc
	end

	-- 转换
	local attr_name_tree_r = AttributeMgr.GetAttrNameTreeR()
	local conversion_list = {}
	for k,v in pairs(attr_list) do
		local str_key = attr_name_tree_r[k] or k
		conversion_list[str_key] = v
	end

	-- 排序
	local sort_list = {}
	local sort_attribute = AttributeMgr.SortAttribute()
	for k,v in ipairs(sort_attribute) do
		if conversion_list[v] then
			local data = {}
			data.value = conversion_list[v].value
			data.maxlv_value = conversion_list[v].max_level_value
			data.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(v, true)
			data.value_str = AttributeMgr.PerAttrValue(v, conversion_list[v].value)
			data.maxlv_value_str = AttributeMgr.PerAttrValue(v, conversion_list[v].max_level_value)
			table.insert(sort_list, data)
		end
	end

	local str_format = "<color=#A8A5A4FF>%s</color>   <color=#DCDCDCFF>%s</color>%s"
	local after_maxlv_format = "%s\n\n<color=#F8D999FF>满级后</color>\n%s"
	local before_attr_str = ""
	local after_attr_str = ""

	local length = #sort_list
	for k, v in ipairs(sort_list) do
		local huan_hang = k == length and "" or "\n"
		if v.value > 0 then
			before_attr_str = before_attr_str .. string.format(str_format, v.attr_name, v.value_str, huan_hang)
		end

		if v.maxlv_value > 0 then
			after_attr_str = after_attr_str .. string.format(str_format, v.attr_name, v.maxlv_value_str, huan_hang)
		end
	end

	if #after_attr_str > 0 then
		attr_desc = string.format(after_maxlv_format, before_attr_str, after_attr_str)
	else
		attr_desc = before_attr_str
	end

	return attr_desc
end


--[[
	获取物品的属性列表
]]






-- 山海经-图鉴
function ItemShowWGData:GetSHJTuJianAttrByData(item_id)
	local cfg = ShanHaiJingWGData.Instance:GetTJResolveShowCfg(item_id)
	local star_cfg = ShanHaiJingWGData.Instance:GetTJUpStarCfg(cfg.seq, 1)
    local attr_list, capability = self:OutStrAttrListAndCapality(cfg)
    return attr_list, capability
end

-- 时装
function ItemShowWGData:GetFashionAttrByData(item_id, show_level_max_cap)
	local attr_list = {}
	local capability = 0
	local cfg = NewAppearanceWGData.Instance:GetFashionCfgByItemId(item_id)
	if IsEmptyTable(cfg) then
		return attr_list, capability
	end

	local level = 1
	
	if show_level_max_cap then
		local level_up_cfg = NewAppearanceWGData.Instance:GetFashionUpLevelCfg(cfg.part_type, cfg.index)
		level = level_up_cfg.max_up_level
	end

	local level_cfg = NewAppearanceWGData.Instance:GetFashionUpLevelAttrCfg(cfg.part_type, cfg.index, level)
	attr_list, capability = self:OutStrAttrListAndCapality(level_cfg)

	local insert_key = -1
	for key, value in pairs(attr_list) do
		if EquipmentWGData.Instance:GetAttrIsPer(value.attr_str) then
			if insert_key < 0 then
				insert_key = key
			end
		end
	end

	local tb_attr_add_perf = NewAppearanceWGData.Instance:GetFashionPerAddAttr(cfg.part_type, cfg.index, level)
    local attr_add_per = tb_attr_add_perf and tb_attr_add_perf.attr_value or 0 --cfg and cfg.attr_add_perf
    if attr_add_per and attr_add_per > 0 then
        local temp = {}
		local attr_title_str = string.format(Language.NewAppearance.AttrAddPer, Language.NewAppearance.FashionPerAttrGroup[cfg.part_type])
        temp.attr_name = ToColorStr(attr_title_str, COLOR3B.GLOD)
        temp.value_str = attr_add_per / 100 .. "%"

        local cap_val = NewAppearanceWGData.Instance:CalcFashionAttrCapWithAddPer(attr_list, attr_add_per / 10000)
        capability = capability + cap_val
		table.insert(attr_list, insert_key > 0 and insert_key or #attr_list + 1, temp)
    end

	-- 技能战力
	local skill_level = 1
	local skill_cfg = NewAppearanceWGData.Instance:GetFashionSkillCfg(cfg.part_type, cfg.index, skill_level)
	if skill_cfg then
		local skill_cap = NewAppearanceWGData.Instance:GetSingleSkillCap(skill_cfg)
		capability = capability + skill_cap
	end

	return attr_list, capability
end

-- 称号
function ItemShowWGData:GetTitleAttrByData(item_id)
	local cfg = TitleWGData.Instance:GetTitleConfigByItemId(item_id)
	local attr_list, capability = self:OutStrAttrListAndCapality(cfg)
	return attr_list, capability
end

-- 坐骑
function ItemShowWGData:GetMountAttrByData(item_id)
	local cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByItemId(MOUNT_LINGCHONG_APPE_TYPE.MOUNT, item_id)
	-- 技能战力
	local image_id = cfg and cfg.image_id or -1
    local map_attr_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongUpStarAttrCfg(MOUNT_LINGCHONG_APPE_TYPE.MOUNT, image_id, 1)
	local attr_list, capability = self:OutStrAttrListAndCapality(map_attr_cfg)

	local skill_id = 0
	local skill_cfg = NewAppearanceWGData.Instance:GetSpecialMountSkillCfg(image_id, skill_id)
	if skill_cfg then
		local skill_cap = NewAppearanceWGData.Instance:GetSingleSkillCap(skill_cfg)
		capability = capability + skill_cap
	end
	local t_attr_add_perf = NewAppearanceWGData.Instance:GetSpecialQiChongAttrAddPer(MOUNT_LINGCHONG_APPE_TYPE.MOUNT, image_id, 1)
    local attr_add_per = t_attr_add_perf or 0 --cfg and cfg.attr_add_perf

	local insert_key = -1
	for key, value in pairs(attr_list) do
		if EquipmentWGData.Instance:GetAttrIsPer(value.attr_str) then
			if insert_key < 0 then
				insert_key = key
			end
		end
	end

    if attr_add_per and attr_add_per > 0 then
        local temp = {}
		local name_str = Language.Tip.MountLingChongAttrName[MOUNT_LINGCHONG_APPE_TYPE.MOUNT]
        temp.attr_name = ToColorStr(name_str, COLOR3B.GLOD)
        temp.value_str = attr_add_per / 100 .. "%"
        local add_capa = MountLingChongEquipWGData.Instance:MountPetEquipAttr(MOUNT_LINGCHONG_APPE_TYPE.MOUNT, attr_add_per/10000)
        capability = capability + add_capa
		table.insert(attr_list, insert_key > 0 and insert_key or #attr_list + 1, temp)
    end
	return attr_list, capability
end

-- 坐骑最高阶时候的战力
function ItemShowWGData:GetMountMaxAttrByData(item_id)
	local active_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByItemId(MOUNT_LINGCHONG_APPE_TYPE.MOUNT, item_id)
	-- 技能战力
	local image_id = active_cfg and active_cfg.image_id or -1
	
	local star_map_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongUpStarCfg(MOUNT_LINGCHONG_APPE_TYPE.MOUNT, image_id)
	local max_star_lv = star_map_cfg.max_up_level or 1
	
	local map_attr_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongUpStarAttrCfg(MOUNT_LINGCHONG_APPE_TYPE.MOUNT, image_id, max_star_lv)
	local lv_one_map_attr_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongUpStarAttrCfg(MOUNT_LINGCHONG_APPE_TYPE.MOUNT, image_id, 1)
	local lv_one_attr_list = self:OutStrAttrListAndCapality(lv_one_map_attr_cfg)
	
	local attr_list, capability = self:OutStrAttrListAndCapality(map_attr_cfg)
	local skill_id = 0
	local skill_cfg = NewAppearanceWGData.Instance:GetSpecialMountSkillList(image_id)
	if skill_cfg then

		local skill_cap = 0
		for k,v in pairs(skill_cfg) do
			skill_cap = NewAppearanceWGData.Instance:GetSingleSkillCap(v)
			capability = capability + skill_cap
		end
	end

	local max_grade = NewAppearanceWGData.Instance:GetSpeicalMountMaxGrade()
	local max_grade_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongUpGradeCfg(MOUNT_LINGCHONG_APPE_TYPE.MOUNT, image_id, max_grade)

	if max_grade_cfg then
		local _, grade_capability = self:OutStrAttrListAndCapality(max_grade_cfg)
		capability = capability + grade_capability
	end

	local t_attr_add_perf = 0 
    for i = 1, max_star_lv do
        t_attr_add_perf = t_attr_add_perf + (NewAppearanceWGData.Instance:GetSpecialQiChongAttrAddPer(MOUNT_LINGCHONG_APPE_TYPE.MOUNT, image_id, i) or 0)
    end

    local attr_add_per = t_attr_add_perf or 0 --active_cfg and active_cfg.attr_add_perf
    if attr_add_per and attr_add_per > 0 then
        local add_capa = MountLingChongEquipWGData.Instance:MountPetEquipAttr(MOUNT_LINGCHONG_APPE_TYPE.MOUNT, attr_add_per / 10000)
        capability = capability + add_capa
    end

	local insert_key = -1
	for key, value in pairs(attr_list) do
		if EquipmentWGData.Instance:GetAttrIsPer(value.attr_str) then
			if insert_key < 0 then
				insert_key = key
			end
		end
	end

	local lv_one_attr_add_perf = NewAppearanceWGData.Instance:GetSpecialQiChongAttrAddPer(MOUNT_LINGCHONG_APPE_TYPE.MOUNT, image_id, 1)
    local lv_one_attr_add_per = lv_one_attr_add_perf or 0 --active_cfg and active_cfg.attr_add_perf
	if lv_one_attr_add_per and lv_one_attr_add_per > 0 then
		local temp = {}
        temp.attr_name = Language.Tip.MountLingChongAttrName[MOUNT_LINGCHONG_APPE_TYPE.MOUNT]
        temp.value_str = lv_one_attr_add_per / 100 .. "%"
		table.insert(lv_one_attr_list, insert_key > 0 and insert_key or #attr_list + 1, temp)
	end

	return lv_one_attr_list, capability
end

-- 灵宠
function ItemShowWGData:GetLingChongAttrByData(item_id)
	local cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByItemId(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG, item_id)
	-- 技能战力
	local image_id = cfg and cfg.image_id or -1
	local map_attr_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongUpStarAttrCfg(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG, image_id, 1)
	local attr_list, capability = self:OutStrAttrListAndCapality(map_attr_cfg)

	local skill_id = 0
	local skill_cfg = NewAppearanceWGData.Instance:GetSpecialLingChongSkillCfg(image_id, skill_id)
	if skill_cfg then
		local skill_cap = NewAppearanceWGData.Instance:GetSingleSkillCap(skill_cfg)
		capability = capability + skill_cap
    end

	local insert_key = -1
	for key, value in pairs(attr_list) do
		if EquipmentWGData.Instance:GetAttrIsPer(value.attr_str) then
			if insert_key < 0 then
				insert_key = key
			end
		end
	end

	local t_attr_add_perf = NewAppearanceWGData.Instance:GetSpecialQiChongAttrAddPer(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG, image_id, 1)
    -- local attr_add_per = cfg and cfg.attr_add_per
	local attr_add_per = t_attr_add_perf or 0
    if attr_add_per and attr_add_per > 0 then
        local temp = {}
		local name_str = Language.Tip.MountLingChongAttrName[MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG]
        temp.attr_name = ToColorStr(name_str, COLOR3B.GLOD)
        temp.value_str = attr_add_per / 100 .. "%"
        local add_capa = MountLingChongEquipWGData.Instance:MountPetEquipAttr(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG, attr_add_per/10000)
        capability = capability + add_capa
		table.insert(attr_list, insert_key > 0 and insert_key or #attr_list + 1, temp)
    end
	return attr_list, capability
end

-- 灵宠最高阶战力
function ItemShowWGData:GetLingChongMaxAttrByData(item_id)
	local cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByItemId(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG, item_id)	
	-- 技能战力
	local image_id = cfg and cfg.image_id or -1
	local star_map_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongUpStarCfg(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG, image_id)
	local max_star_lv = star_map_cfg.max_up_level or 1
	
	local map_attr_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongUpStarAttrCfg(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG, image_id, max_star_lv)
	local lv_one_map_attr_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongUpStarAttrCfg(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG, image_id, 1)
	local lv_one_attr_list = self:OutStrAttrListAndCapality(lv_one_map_attr_cfg)
	
	local attr_list, capability = self:OutStrAttrListAndCapality(map_attr_cfg)

	local skill_id = 0
	local skill_cfg = NewAppearanceWGData.Instance:GetSpecialLingChongSkillList(image_id)
	if skill_cfg then
		local skill_cap = 0
		for k,v in pairs(skill_cfg) do
			skill_cap = NewAppearanceWGData.Instance:GetSingleSkillCap(v)
			capability = capability + skill_cap
		end
    end

    local max_grade = NewAppearanceWGData.Instance:GetSpeicalLingChongMaxGrade()
	local max_grade_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongUpGradeCfg(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG, image_id, max_grade)

	if max_grade_cfg then
		local _, grade_capability = self:OutStrAttrListAndCapality(max_grade_cfg)
		capability = capability + grade_capability
	end

	local t_attr_add_perf = 0 
    for i = 1, max_star_lv do
        t_attr_add_perf = t_attr_add_perf + (NewAppearanceWGData.Instance:GetSpecialQiChongAttrAddPer(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG, image_id, i) or 0)
    end
	local attr_add_per = t_attr_add_perf or 0 --active_cfg and active_cfg.attr_add_perf
    if attr_add_per and attr_add_per > 0 then
        local add_capa = MountLingChongEquipWGData.Instance:MountPetEquipAttr(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG, attr_add_per / 10000)
        capability = capability + add_capa
    end

	local insert_key = -1
	for key, value in pairs(lv_one_attr_list) do
		if EquipmentWGData.Instance:GetAttrIsPer(value.attr_str) then
			if insert_key < 0 then
				insert_key = key
			end
		end
	end

	local lv_one_attr_add_perf = NewAppearanceWGData.Instance:GetSpecialQiChongAttrAddPer(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG, image_id, 1)
    local lv_one_attr_add_per = lv_one_attr_add_perf or 0 --active_cfg and active_cfg.attr_add_perf
	if lv_one_attr_add_per and lv_one_attr_add_per > 0 then
		local temp = {}
        temp.attr_name = Language.Tip.MountLingChongAttrName[MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG]
        temp.value_str = lv_one_attr_add_per / 100 .. "%"
		table.insert(lv_one_attr_list, insert_key > 0 and insert_key or #attr_list + 1, temp)
	end
	return lv_one_attr_list, capability
end

-- 化鲲
function ItemShowWGData:GetHuaKunAttrByData(item_id)
	local attr_list = {}
	local capability = 0
	local cfg = NewAppearanceWGData.Instance:GetKunActCfgByItemId(item_id)
	if IsEmptyTable(cfg) then
		return attr_list, capability
	end

	local level = 0
	local star = 1
	local level_cfg = NewAppearanceWGData.Instance:GetKunUpGradeCfg(cfg.id, level)
	local star_cfg = NewAppearanceWGData.Instance:GetKunUpStarAttrCfg(cfg.id, star)
	attr_list, capability = self:OutStrAttrListAndCapalityTwoMix(level_cfg, star_cfg)

	local t_show_attr_add_perf = NewAppearanceWGData.Instance:GetSpecialQiChongAttrAddPer(MOUNT_LINGCHONG_APPE_TYPE.KUN, cfg.id, 1)
    local show_attr_add_per = t_show_attr_add_perf or 0 --cfg and cfg.attr_add_perf
	local insert_key = -1
	for key, value in pairs(attr_list) do
		if EquipmentWGData.Instance:GetAttrIsPer(value.attr_str) then
			if insert_key < 0 then
				insert_key = key
			end
		end
	end

    if show_attr_add_per and show_attr_add_per > 0 then
        local temp = {}
		local name_str = Language.Tip.MountLingChongAttrName[MOUNT_LINGCHONG_APPE_TYPE.KUN]
        temp.attr_name = ToColorStr(name_str, COLOR3B.GLOD)
        temp.value_str = show_attr_add_per / 100 .. "%"
		table.insert(attr_list, insert_key > 0 and insert_key or #attr_list + 1, temp)
    end
	return attr_list, capability
end

-- 化鲲最大阶战力
function ItemShowWGData:GetHuaKunMaxAttrByData(item_id)
	local attr_list = {}
	local capability = 0
	local cfg = NewAppearanceWGData.Instance:GetKunActCfgByItemId(item_id)
	if IsEmptyTable(cfg) then
		return attr_list, capability
	end
	local star_map_cfg = NewAppearanceWGData.Instance:GetKunUpStarCfg(cfg.id)
	local max_star_lv = star_map_cfg.max_up_level or 1
	--满级属性
	local level = NewAppearanceWGData.Instance:GetKunMaxGrade()
	local level_cfg = NewAppearanceWGData.Instance:GetKunUpGradeCfg(cfg.id, level)
	attr_list, capability = self:OutStrAttrListAndCapality(level_cfg)

	--基础属性(策划需求:属性列表显示激活的)
	local base_level = 0
	local base_star = 1
	local base_level_cfg = NewAppearanceWGData.Instance:GetKunUpGradeCfg(cfg.id, base_level)
	local base_star_cfg = NewAppearanceWGData.Instance:GetKunUpStarAttrCfg(cfg.id, base_star)
	local max_star_cfg = NewAppearanceWGData.Instance:GetKunUpStarAttrCfg(cfg.id, max_star_lv)

	local _, max_star_cap = self:OutStrAttrListAndCapality(max_star_cfg)
	local base_attr, base_cap = self:OutStrAttrListAndCapalityTwoMix(base_level_cfg, base_star_cfg)
	capability = max_star_cap + capability

	--技能
	local skill_cfg = NewAppearanceWGData.Instance:GetKunSkillList(cfg.id)
	local skill_cap = 0
	for k,v in pairs(skill_cfg) do
		skill_cap = NewAppearanceWGData.Instance:GetKunSkillAttrCap(v)
		capability = capability + skill_cap
	end

	local star_map_cfg = NewAppearanceWGData.Instance:GetKunUpStarCfg(cfg.id)
	local max_star_lv = star_map_cfg.max_up_level or 1

	local t_attr_add_perf = 0 
    for i = 1, max_star_lv do
        t_attr_add_perf = t_attr_add_perf + (NewAppearanceWGData.Instance:GetSpecialQiChongAttrAddPer(MOUNT_LINGCHONG_APPE_TYPE.KUN, cfg.id, i) or 0)
    end
	
	local attr_add_per = t_attr_add_perf or 0 --active_cfg and active_cfg.attr_add_perf
	local extra_cap = 0
    if attr_add_per and attr_add_per > 0 then
		local attr = AttributeMgr.MulAttribute(max_star_cfg, attr_add_per / 10000)
		extra_cap = AttributeMgr.GetCapability(attr)
		capability = capability + extra_cap
    end

	local t_show_attr_add_perf = NewAppearanceWGData.Instance:GetSpecialQiChongAttrAddPer(MOUNT_LINGCHONG_APPE_TYPE.KUN, cfg.id, 1)
    local show_attr_add_per = t_show_attr_add_perf or 0 --cfg and cfg.attr_add_perf
	local insert_key = -1
	for key, value in pairs(base_attr) do
		if EquipmentWGData.Instance:GetAttrIsPer(value.attr_str) then
			if insert_key < 0 then
				insert_key = key
			end
		end
	end

    if show_attr_add_per and show_attr_add_per > 0 then
        local temp = {}
        temp.attr_name = Language.Tip.MountLingChongAttrName[MOUNT_LINGCHONG_APPE_TYPE.KUN]
        temp.value_str = show_attr_add_per / 100 .. "%"
		table.insert(base_attr, insert_key > 0 and insert_key or #base_attr + 1, temp)
    end
	return base_attr, capability
end

-- 天神-幻化
function ItemShowWGData:GetTianShenAttrByData(item_id)
	local attr_list = {}
	local capability = 0
	local cfg = TianShenWGData.Instance:GetTianShenActItemCfgByActId(item_id)
	if IsEmptyTable(cfg) then
		return attr_list, capability
	end

	local star = 0
	local level_cfg = TianShenWGData.Instance:GetTianShenStar1(cfg.index, star)
	attr_list, capability = self:OutStrAttrListAndCapality(level_cfg)
	return attr_list, capability
end

-- 天神-幻化
function ItemShowWGData:GetTianShenMaxAttrByData(item_id)
	local attr_list = {}
	local capability = 0
	local cfg = TianShenWGData.Instance:GetTianShenActItemCfgByActId(item_id)
	if IsEmptyTable(cfg) then
		return attr_list, capability
	end

	local star = 10
	local level_cfg = TianShenWGData.Instance:GetTianShenStar1(cfg.index, star)
	local attr_star_list = AttributeMgr.GetAttributteByClass(level_cfg)

	local level =  TianShenWGData.Instance:GetTianShenMaxGrade()
	local new_level_cfg = TianShenWGData.Instance:GetTianShenUpgrade(cfg.index, level)
	local attr_upgrade_list = AttributeMgr.GetAttributteByClass(new_level_cfg)

	local pasv_attr_list = TianShenWGData.Instance:GetPasvAttr(cfg.index, true,true)
	for k,v in pairs(pasv_attr_list) do
		if attr_star_list[k] then
			attr_star_list[k] = attr_star_list[k] * v
		end

		if attr_upgrade_list[k] then
			attr_upgrade_list[k] = attr_upgrade_list[k] * v
		end
	end
	capability = AttributeMgr.GetCapability(attr_star_list) + AttributeMgr.GetCapability(attr_upgrade_list)

	for k, v in pairs(attr_star_list) do
		if v ~= 0 then
			if attr_list[k] then
				attr_list[k] = attr_list[k] + v
			else
				attr_list[k] = v
			end
		end
	end

	for k, v in pairs(attr_upgrade_list) do
		if v ~= 0 then
			if attr_list[k] then
				attr_list[k] = attr_list[k] + v
			else
				attr_list[k] = v
			end
		end
	end
	-- local bei_cfg = TianShenWGData.Instance:GetSpecialImagePasvSkillCfgByItemId(item_id)
	-- if bei_cfg then
	-- 	local bei_capability = 0
	-- 	for k,v in pairs(bei_cfg) do
	-- 		bei_capability = TianShenWGData.Instance:GetTianShenSpecialSkillCap(v)
	-- 		capability = capability + bei_capability
	-- 	end
	-- end


	-- local zhu_cfg = TianShenWGData.Instance:GetTianShenZhuSkillByItemid(item_id)
	-- local zhu_capability = 0
	-- for i=1,#zhu_cfg do
	-- 	local skill_id = tonumber(zhu_cfg[i])
	-- 	local ts_skill_cfg = SkillWGData.Instance:GetTianShenSkillCfg(skill_id, 1)
	-- 	zhu_capability = zhu_capability + ts_skill_cfg.capability_inc
	-- end
	-- capability = capability + zhu_capability

	local eat_attr = TianShenWGData.Instance:GetColorLimitAttrTab(7)
	local eat_class_attr = AttributeMgr.GetAttributteByClass(eat_attr)
	for k, v in pairs(eat_class_attr) do
		if v ~= 0 then
			if attr_list[k] then
				attr_list[k] = attr_list[k] + v
			else
				attr_list[k] = v
			end
		end
	end

	local _, eat_capability = self:OutStrAttrListAndCapality(eat_attr)
	local item_cap = 0
	local item_cfg_list = TianShenWGData.Instance:GetMaxShenShiLimiteList()
	for k,v in pairs(item_cfg_list) do
		local temp_item_cap, _ = TianShenWGData.Instance:GetShenShiZhanLi(v.shenshi_id)
		local attr_cfg = TianShenWGData.Instance:GetTianShenAttributeTab(v.shenshi_id)
		local temp_item_list = AttributeMgr.GetAttributteByClass(attr_cfg)

		for m, n in pairs(temp_item_list) do
			if n ~= 0 then
				if attr_list[m] then
					attr_list[m] = attr_list[m] + n
				else
					attr_list[m] = n
				end
			end
		end
		item_cap = item_cap + temp_item_cap
	end
	-- local next_attr = TianShenWGData.Instance:GetSingleLimitAttrTab(8)
	-- local _, next_attr_capability = self:OutStrAttrListAndCapality(next_attr)
	capability = capability + eat_capability + item_cap

	local max_length = 0
	for k, v in pairs(attr_list) do
		max_length = max_length + 1
	end

	return attr_list, capability, max_length
end


-- 天神-神通
function ItemShowWGData:GetTSShenTongAttrByData(item_id)
	local attr_list = {}
	local capability = 0
	local cfg = TianShenWGData.Instance:GetSTCfgByActId(item_id)
	if IsEmptyTable(cfg) then
		return attr_list, capability
	end

	local level = 1
	local level_cfg = TianShenWGData.Instance:GetSTLevelcfg(cfg.skill_id, level)
	attr_list, capability = self:OutStrAttrListAndCapality(level_cfg)
	return attr_list, capability
end

-- 天神-神器
function ItemShowWGData:GetTSShenQiAttrByData(item_id)
	local attr_list = {}
	local capability = 0
	local cfg = TianShenWGData.Instance:GetShenQiCfgByActItemId(item_id)
	if IsEmptyTable(cfg) then
		return attr_list, capability
	end

	local star = 0
	local star_cfg = TianShenWGData.Instance:GetShenQiStarCfg(cfg.index, star)
	attr_list, capability = self:OutStrAttrListAndCapality(star_cfg)

	--local shenqi_info = TianShenWGData.Instance:GetTianShenShenQiInfo(cfg.index)
	--local now_shenqi_cfg = TianShenWGData.Instance:GetShenQiStarCfg(cfg.index, 0) --henqi_info and shenqi_info.star or 0)
	local shenqi_zhu_skill = TianShenWGData.Instance:GetShenQiSkillInfo(cfg.index,0)--,shenqi_info and shenqi_info.star or 0)
    local skill_cfg = TianShenWGData.Instance:GetShenQiSkillCfg(shenqi_zhu_skill[1].skill,star_cfg.skill_level)
    local skill_cap = skill_cfg.capability_inc or 0
    capability = capability + skill_cap
	return attr_list, capability
end

-- 宝石
function ItemShowWGData:GetBaoShiAttrByData(item_id)
	local cfg = EquipmentWGData.Instance:GetSingleBaoShiAttrList(item_id)
	local attr_list, capability = self:OutStrAttrListAndCapality(cfg)
	return attr_list, capability
end

-- 灵玉
function ItemShowWGData:GetLingYuAttrByData(item_id)
	local cfg = EquipmentLingYuWGData.Instance:GetSingleLingYuAttrList(item_id)
	local attr_list, capability = self:OutStrAttrListAndCapality(cfg)
	return attr_list, capability
end

-- 守护
function ItemShowWGData:GetShouHuAttrByData(item_id)
	local cfg = EquipmentWGData.GetXiaoGuiCfg(item_id)
	local attr_list, capability = self:OutStrAttrListAndCapality(cfg)
	return attr_list, capability
end

-- 仙娃
function ItemShowWGData:GetXianWaAttrByData(item_id)
	local attr_list = {}
	local capability = 0
	local baby_grade = 0
	local baby_type, baby_id = MarryWGData.Instance:GetBabyTypeItemId(item_id)

	local level_cfg = MarryWGData.Instance:GetBabyAttrCfg(baby_type, baby_id, baby_grade)
	attr_list, capability = self:OutStrAttrListAndCapality(level_cfg)
	return attr_list, capability
end

-- 仙娃最大等级
function ItemShowWGData:GetXianWaMaxAttrByData(item_id)
	local attr_list = {}
	local capability = 0
	local baby_grade = MarryWGData.Instance:GetBabyMaxGrade()
	local baby_type, baby_id = MarryWGData.Instance:GetBabyTypeItemId(item_id)

	local level_cfg = MarryWGData.Instance:GetBabyAttrCfg(baby_type, baby_id, baby_grade)
	attr_list, capability = self:OutStrAttrListAndCapality(level_cfg)

	if self.only_get_attr_desc or self.only_get_attr_list then
		return attr_list, capability
	end

	local baby_skill_cfg = MarryWGData.Instance:GetBabySkillCfg(baby_type, baby_id)
	if baby_skill_cfg then
		local skill_capability = 0
		for k,v in pairs(baby_skill_cfg) do
			local skill_attribute = MarryWGData.Instance:GetBabySkillAddAttr(v.param1, v.param2)
			if skill_attribute ~= nil then
				skill_capability = AttributeMgr.GetCapability(skill_attribute)
				capability = capability + skill_capability
			end
		end
	end
	return attr_list, capability
end

-- 坐骑属性丹
function ItemShowWGData:GetZQShuXingDanAttrByData(item_id)
	local cfg = NewAppearanceWGData.Instance:GetMountSXDCfgByItemId(item_id)
	local attr_list, capability = self:OutStrAttrListAndCapality(cfg)
	return attr_list, capability
end

-- 灵宠属性丹
function ItemShowWGData:GetLCShuXingDanAttrByData(item_id)
	local cfg = NewAppearanceWGData.Instance:GetLingChongSXDCfgByItemId(item_id)
	local attr_list, capability = self:OutStrAttrListAndCapality(cfg)
	return attr_list, capability
end

-- 外观属性丹
function ItemShowWGData:GetWGShuXingDanAttrByData(item_id)
	local cfg = NewAppearanceWGData.Instance:GetAdvancedSXDCfgByItemId(item_id)
	local attr_list, capability = self:OutStrAttrListAndCapality(cfg)
	return attr_list, capability
end

-- 铭文
function ItemShowWGData:GetMingWenAttrByData(data)
	local attr_list, capability = MingWenWGData.Instance:GetEquipPosyAttrListByItemId(data)
	return attr_list, capability
end

-- 龙魂
function ItemShowWGData:GetLongHunAttrByData(data)
	local attr_list ,capability = LongHunWGData.Instance:GetEquipLongHunAttrListByItemId(data)
	return attr_list, capability
end

--天神神饰
function ItemShowWGData:GetShenShiAttrByData(item_id, attr_per)
	local cfg = TianShenWGData.Instance:GetTianShenAttributeTab(item_id, attr_per)
	local attr_list, capability = self:OutStrAttrListAndCapality(cfg)
	return attr_list, capability
end

--天神八卦牌
function ItemShowWGData:GetBaGuaAttrByData(item_id)
	local attr_name_and_value,is_pre = TianShenBaGuaWGData.Instance:GetBaGuaEquipAttrAndZhanLi(item_id,true)
	local attr_list, capability = self:OutStrAttrListAndCapality(attr_name_and_value)
	return attr_list, 0
end

--头像
function ItemShowWGData:GetTouXiangAttrByData(item_id)
	local cfg = RoleWGData.Instance:GetHeadCfgByItemId(item_id)
	local attr_list, capability = self:OutStrAttrListAndCapality(cfg)
	return attr_list, capability
end

-- 天书碎片
function ItemShowWGData:GetGodBookChipAttrByData(item_id)
	local cfg = FairyLandEquipmentWGData.Instance:GetGodBookChipCfgByItemId(item_id)
	local attr_list, capability = self:OutStrAttrListAndCapality(cfg)
	return attr_list, capability
end

-- 天神灵核
function ItemShowWGData:GetTianShenLingHeAttrByData(item_id)
	local cfg = TianShenLingHeWGData.Instance:GetLingGHeCfgByItemId(item_id)
	local attr_list, capability = self:OutStrAttrListAndCapality(cfg)
	return attr_list, capability
end

-- 锻灵入体
function ItemShowWGData:GetDuanLingRuTiAttrByData(item_id)
	local cfg = EquipTargetWGData.Instance:GetEIStuffDataByItemId(item_id, 0)
	local attr_list, capability = self:OutStrAttrListAndCapality(cfg)

	return attr_list, capability
end

--丹药
function ItemShowWGData:GetRefiningAttrByData(item_id)
	local cfg = AlchemyWGData.Instance:GetPelletInfo(item_id)
	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value")
	return attr_list, capability
end

--五行
function ItemShowWGData:GetFiveElementsAttrByData(item_id)
	local cfg = FiveElementsWGData.Instance:GetStoreInfoByItemId(item_id)
	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value")
	return attr_list, capability
end

--双修(仙魔神器)
function ItemShowWGData:GetArtifactAttrByData(item_id)
	local cfg = ArtifactWGData.Instance:GetArtifactCfgByItemId(item_id)
	local cur_level_cfg = ArtifactWGData.Instance:GetArtifactLevelCfg(cfg.seq, 1)
	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cur_level_cfg, "attr_id", "attr_value", 1, 7)
	return attr_list, capability
end

-- 符咒
function ItemShowWGData:GetCharmAttrByData(item_id)
	local cfg = CultivationWGData.Instance:GetCharmEquipByItemId(item_id)
	local attr_list, capability = CultivationWGData.Instance:GetCharmItemData(cfg)
	return attr_list, capability
end

-- 奇境
function ItemShowWGData:GetBackgroundAttrByData(item_id)
	local cfg = BackgroundWGData.Instance:GetBackgroundCfgByItemId(item_id)
	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value", 1, 6)
	return attr_list, capability
end

-- 道行
function ItemShowWGData:GetDaoHangAttrByData(item_id)
	local cfg = MultiFunctionWGData.Instance:GetDaoHangEquipByItemId(item_id)
	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value", 1, 5)
	return attr_list, capability
end

-- 至尊领域
function ItemShowWGData:GetZhiZunAttrByData(item_id)
	local cfg = SupremeFieldsWGData.Instance:GetFootStoneByItemID(item_id)
	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value", 1, 5)
	return attr_list, capability
end

-- 天神化魔
function ItemShowWGData:GetTianShenHuaMoAttrByData(item_id)
	local attr_list = {}
	local capability = 0
	local cfg = TianShenHuamoWGData.Instance:GetHuaMoAttributeCfgByItem(item_id)
	if IsEmptyTable(cfg) then
		return attr_list, capability
	end

	attr_list, capability = self:OutStrAttrListAndCapality(cfg)
	return attr_list, capability
end

-- 五行沧溟
function ItemShowWGData:GetCangMingAttrByData(item_id)
	local cfg = FiveElementsWGData.Instance:GetWaistStoneByItemID(item_id)
	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value", 1, 5)
	return attr_list, capability
end

-- 武魂真身
function ItemShowWGData:GetWuHunAttrByData(item_id)
	local cfg = WuHunWGData.Instance:GetWuHunAttrCfgByItemID(item_id)
	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value", 1, 5)
	return attr_list, capability
end

--武魂属性丹
function ItemShowWGData:GetWuHunShuXingDanAttrByData(item_id)
	local cfg = WuHunWGData.Instance:GetWuHunShuXingDanAttrCfgByItemID(item_id)
	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value", 1, 5)
	return attr_list, capability
end

-- 战斗坐骑
function ItemShowWGData:GetNewFightMountAttrByData(item_id)
	local cfg = NewFightMountWGData.Instance:GetUpgradeCostCfg(item_id)
	local mount_seq = cfg and cfg.mount_seq or 0
	local mount_cfg = NewFightMountWGData.Instance:GetUpgradeLevelCfg(mount_seq, 1)
	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(mount_cfg, "attr_id", "attr_value", 1, 5)
	return attr_list, capability
end

-- 龙神殿丹药
function ItemShowWGData:GetDragonTemplePelletAttrByData(item_id)
	local cfg = DragonTempleWGData.Instance:GetHatchPelletCostCfg(item_id)
	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value", 1, 5)
	return attr_list, capability
end

-- 秘笈
function ItemShowWGData:GetEsotericaAttrByData(item_id)
	local cfg
	local part_item_cfg = CultivationWGData.Instance:GetEsotericaPartItemCfg(item_id)
	if part_item_cfg then
		cfg = CultivationWGData.Instance:GetEsotericaPartLevelCfg(part_item_cfg.seq, part_item_cfg.part, part_item_cfg.part_level)
	end

	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value", 1, 5)
	return attr_list, capability
end

-- 奇物
function ItemShowWGData:GetQiWuAttrByData(item_id)
	local cfg
	local item_cfg = StrangeCatalogWGData.Instance:GetQiWuItemCfg(item_id)
	if item_cfg then
		local base_attr_seq = item_cfg.base_attr_seq
		cfg = StrangeCatalogWGData.Instance:GetBaseAttrCfgBySeq(base_attr_seq)
	end

	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value", 1, 5)
	return attr_list, capability
end

-- 灵兽基本属性
function ItemShowWGData:GetBeastsAttrByData(item_id, data)
	local level_cfg = ControlBeastsWGData.Instance:GetBeastLevelData(data)
	local cfg = ControlBeastsWGData.Instance:GetBeastLevelAndBaseData(item_id, level_cfg)
	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value", 1, 6)
	return attr_list, capability
end

-- 灵兽基本属性(臻品属性)
function ItemShowWGData:GetBeastsAttrBestByData(data)
	local cfg = ControlBeastsWGData.Instance:GetBeastBestLevelData(data)
	local attr_desc = ""

	if cfg == nil then
		--attr_desc = Language.F2Tip.BeastNotHaveAttrBest
		attr_desc = ""
	else
		local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value", 1, 2)
		local new_attr_list = self:AddBeastsSpecialStr(data, attr_list)
		attr_desc = self:OutAttrListToStr(new_attr_list)
	end

	return attr_desc
end

-- 灵兽基本属性(臻品属性)
function ItemShowWGData:AddBeastsSpecialStr(data, attr_list)
	if attr_list then
		for index, attr_data in ipairs(attr_list) do
			local str = ControlBeastsWGData.Instance:GetBeastBestUnlockStar(data, index)
			attr_data.value_str = attr_data.value_str .. str
		end
	end

	return attr_list
end

-- 传闻表基本属性
function ItemShowWGData:GetRumorAttrByData(item_id, data)
	local cfg = CustomizedRumorsWGData.Instance:GetRumorCfgByItemId(item_id)
	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value", 1, 5)
	return attr_list, capability
end

-- 机甲基础属性
function ItemShowWGData:GetMechaAttrByData(item_id, data)
	-- local cfg = MechaWGData.Instance:GetMechaCfgByItemId(item_id)
	-- local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value", 1, 8)
	-- return attr_list, capability
	return MechaWGData.Instance:GetMechaCfgByItemId(item_id)
end

-- 千秋绝艳图基础属性
function ItemShowWGData:GetSHJQQJYTAttrByData(item_id, data)
	local cfg = ShanHaiJingLSCWGData.Instance:GetLuoShenCeItemCfgByItemId(item_id)
	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value", 1, 5)
	return attr_list, capability
end

-- 双生天神基础属性
function ItemShowWGData:GetShuangShengTianShenAttrByData(item_id)
	local cfg = TianShenShuangShengWGData.Instance:GetShuangShengTianShenAttrByItemId(item_id)
	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value", 1, 7)
	return attr_list, capability
end

-- 自定义动作基础属性
function ItemShowWGData:GetCustomActionAttrByData(item_id)
	local action_cfg = CustomActionData.Instance:GetActionCfgByItemID(item_id)
	if not action_cfg then
		return {}, 0
	end

	local cfg = CustomActionData.Instance:GetActionLevelCfg(action_cfg.action_id, 0)
	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value", 1, 5)
	return attr_list, capability
end

--龙神殿 御龙定乾坤
function ItemShowWGData:GetLongShenDianYuLongAttrByItemId(item_id)
	local cfg = NewFightMountWGData.Instance:GetSkillLevelCostCfg(item_id, 1) --这里固定显示1级的属性
	if not cfg then
		return {}, 0
	end

	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value", 1, 5)
	return attr_list, capability
end

--灵阵 属性石
function ItemShowWGData:GetMengLingSXSAttrByItemId(item_id)
	local cfg = MengLingWGData.Instance:GetStoneCfgById(item_id)
	if not cfg then
		return {}, 0
	end

	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value", 1, 7)
	return attr_list, capability
end

--时装 缘晶石
function ItemShowWGData:GetShiZhuangStoneAttrByItemId(item_id)
	local cfg = WardrobeWGData.Instance:GetStoneCfgByItemId(item_id)
	if not cfg then
		return {}, 0
	end

	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value", 1, 5)
	return attr_list, capability
end

--雷法装备
function ItemShowWGData:GetThunderManaAttrByItemId(item_id)
	local cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(item_id)
	if not cfg then
		return {}, 0
	end

	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value", 1, 5)
	return attr_list, capability
end

-- 幻兽属性丹
function ItemShowWGData:GetBeastSXDAttrByItemId(item_id)
	local cfg = ControlBeastsWGData.Instance:IsBeastsPelletCfgByItem(item_id)
	if not cfg then
		return {}, 0
	end

	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value", 0, 3)
	return attr_list, capability
end

-- 幻兽皮肤属性
function ItemShowWGData:GetBeastSkinAttrByItemId(item_id)
	local attr_list, capability = {}, 0
	local cfg = ControlBeastsWGData.Instance:GetBeastsSkinCfgByItemId(item_id)
	if not cfg then
		return attr_list, capability
	end
	local attr_cfg = ControlBeastsWGData.Instance:GetSkinLevelCfg(cfg.skin_seq, 1)
	if not attr_cfg then
		return attr_list, capability
	end
	attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(attr_cfg, "attr_id", "attr_value", 1, 7)
	return attr_list, capability
end

-- 元神变身属性
function ItemShowWGData:GetSpiritAttrByItemId(item_id)
	local attr_list, capability = {}, 0
	local cfg = CultivationWGData.Instance:GetSpiritAttrByItemId(item_id)
	if not cfg then
		return attr_list, capability
	end

	attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value", 1, 4)
	return attr_list, capability
end

-- 双人坐骑属性及战力，策划让拿激活的1级属性战力
function ItemShowWGData:GetMultiMountAttrByItemId(item_id)
	local attr_list, capability = {}, 0

	local cfg = MultiMountWGData.Instance:GetItemShowCfgByItemId(item_id)
	if not cfg then
		return attr_list, capability
	end

	attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value", 1, 5)

	return attr_list, capability
end
--------------------------------------------------------
-- 装备合成进程
--------------------------------------------------------
function ItemShowWGData:GetEquipComposeProcessData(data)
	local process_list = {}
	if IsEmptyTable(data) then
		return false, process_list, false
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	if IsEmptyTable(item_cfg) then
		return false, process_list, false
	end

	-- 仙器（仙戒、仙镯）的不显示
	if item_cfg.sub_type == GameEnum.EQUIP_TYPE_XIANJIE or item_cfg.sub_type == GameEnum.EQUIP_TYPE_XIANZHUO then
		return false, process_list, false
	end

	local equip_order = item_cfg.order
	local min_order, max_order = EquipmentWGData.Instance:GetEquipComposeOrderRange()
	if equip_order < min_order or equip_order > max_order then
		return false, process_list, false
	end

	local equip_color = item_cfg.color
	local equip_star = data.param and data.param.star_level or 0
	local tips_show_data = EquipmentWGData.Instance:GetEquipTipsShowComposeData(equip_order, equip_color, equip_star)
	if IsEmptyTable(tips_show_data) or IsEmptyTable(tips_show_data.equip_list) then
		return false, process_list, false
	end

	local equip_list = tips_show_data.equip_list
	for k, v in ipairs(equip_list) do
		local equip_data = EquipmentWGData.Instance:GetAllWearEquipSplitDataByData(item_cfg.sub_type, equip_order, v.color, item_cfg.limit_sex, item_cfg.limit_prof)
		local show_data = {}
		show_data.item_id = equip_data.id
		show_data.param = {star_level = v.star}
		show_data.is_cur = (data.item_id == show_data.item_id) and (v.star == equip_star)
		process_list[#process_list + 1] = show_data
	end

	if IsEmptyTable(process_list) or #process_list <= 1 then
		return false, process_list, false
	end

	return true, process_list, tips_show_data.show_add
end

--------------------------------------------------------
-- 四象合成进程
--------------------------------------------------------
function ItemShowWGData:GetFightSoulComposeProcessData(data)
	local process_list = {}
	local show_tips = false
	if IsEmptyTable(data) then
		return show_tips, process_list
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	if IsEmptyTable(item_cfg) then
		return show_tips, process_list
	end

	local sixiang_cfg = FightSoulWGData.Instance:GetFightSoulItemCfg(data.item_id)
	local sixiang_color = item_cfg.color
	local sixiang_star = data.star or sixiang_cfg.base_star
	local sixiang_type = sixiang_cfg.sixiang_type
	local tips_show_data = FightSoulWGData.Instance:GetFightSoulTipsShowComposeData(sixiang_color, sixiang_star)
	if IsEmptyTable(tips_show_data) then
		return show_tips, process_list
	end

	for k, v in ipairs(tips_show_data) do
		local sixiang_id = FightSoulWGData.Instance:GetItemIdByParam(v.color, v.star, sixiang_type)
		local show_data = {}
		show_data.item_id = sixiang_id
		show_data.star = FightSoulWGData.Instance:GetFightSoulItemStar(sixiang_id)
		show_data.is_cur = sixiang_id == data.item_id
		process_list[#process_list + 1] = show_data
	end

	if IsEmptyTable(process_list) or #process_list <= 1 then
		return show_tips, process_list
	end

	show_tips = true
	return show_tips, process_list
end


--------------------------------------------------------
-- 四象魂骨合成进程
--------------------------------------------------------
function ItemShowWGData:GetFightSoulBoneComposeProcessData(data)
	local process_list = {}
	local show_tips = false
	if IsEmptyTable(data) then
		return show_tips, process_list
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	if IsEmptyTable(item_cfg) then
		return show_tips, process_list
	end

	local tips_show_data = FightSoulWGData.Instance:GetFightSoulTipsShowBoneComposeData(item_cfg.color, data.star)
	if IsEmptyTable(tips_show_data) then
		return show_tips, process_list
	end

	local fight_soul_type, bone_part, suit_type = FightSoulWGData.GetBoneParamByItemId(data.item_id)
	for k, v in ipairs(tips_show_data) do
		local item_id = FightSoulWGData.GetBoneItemIdByParam(fight_soul_type, bone_part, suit_type, v.color)
		local show_data = {}
		show_data.item_id = item_id
		show_data.star = v.star
		show_data.is_cur = (item_id == data.item_id) and (data.star == show_data.star)
		process_list[#process_list + 1] = show_data
	end

	if IsEmptyTable(process_list) or #process_list <= 1 then
		return show_tips, process_list
	end

	show_tips = true
	return show_tips, process_list
end

--------------------------------------------------------
-- 圣装合成进程
--------------------------------------------------------
function ItemShowWGData:GetHolyEquipColorProcessData(data)
	local process_list = {}
	local show_tips = false
	if IsEmptyTable(data) then
		return show_tips, process_list
	end

	local slot = data.slot
	local part = data.part
	local color = data.color
	process_list = FairyLandEquipmentWGData.Instance:GetHolyEquipTipsShowColorData(slot,part,color)

	if IsEmptyTable(process_list) or #process_list <= 1 then
		return show_tips, process_list
	end

	show_tips = true
	return show_tips, process_list
end
