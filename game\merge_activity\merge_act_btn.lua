MergeActBtn = MergeActBtn or BaseClass(MainActivityBtn)

function MergeActBtn:__init(instance)
	self:BindGlobalEvent(ACTIVITY_BTN_EVENT.MERGE_ACT_BTN_CHANGE, BindTool.Bind(self.Button<PERSON>hange, self))
end

function MergeActBtn:LoadCallBack()
	self:ButtonChange()
	self:SetBgActive(false)
end

function MergeActBtn:CreateBtnAsset(parent)
	self:LoadAsset("uis/view/merge_activity_ui_prefab", "merge_act_btn", parent.transform, function(obj)
		self.node_list.SpecialEff:SetActive(true)
	end)
end

function MergeActBtn:ButtonChange()
	if self:IsNil() then
		return
	end

	local show_new_flag = MergeActivityWGData.Instance:GetIsShowMainBtnNewTxt()
	self:SetTxtNewStatus(show_new_flag == 1)
end

function MergeActBtn:SetTxtNewStatus(status)
	if not self.node_list then return end
	self.node_list["txt_new"]:SetActive(status)
end