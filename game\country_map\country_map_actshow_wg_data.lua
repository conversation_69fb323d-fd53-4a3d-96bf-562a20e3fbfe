CountryMapActShowWgData = CountryMapActShowWgData or BaseClass()

function CountryMapActShowWgData:__init()
	if nil ~= CountryMapActShowWgData.Instance then
		ErrorLog("[CountryMapActShowWgData]:Attempt to create singleton twice!")
	end

    CountryMapActShowWgData.Instance = self

    local country_auto = ConfigManager.Instance:GetAutoConfig("country_auto")
    self.act_cfg = ListToMap(country_auto.act_cfg, "index")
end

function CountryMapActShowWgData:__delete()
    CountryMapActShowWgData.Instance = nil
end

function CountryMapActShowWgData:GetActShowActCfg()
    return self.act_cfg
end

function CountryMapActShowWgData:GetAcfCfgByIndex(index)
    return self.act_cfg[index] or {}
end

-- 先去跨服通用表找  然后去日常活动弹出提示找
function CountryMapActShowWgData:GetActOpenTimeByActType(act_id, index)
    local week_desc, time_desc = "", ""

    if act_id > 0 then
        local zhou_str, start_time, end_time, is_every_day = BiZuoWGData.Instance:GetActOpenTimeStr(act_id)

        if zhou_str ~= "" and start_time ~= end_time then
            week_desc = is_every_day and zhou_str or string.format(Language.CountryMap.WeekStr, zhou_str)
            time_desc = start_time .. "-" .. end_time
        else
            local act_data = ActivityWGData.Instance:GetActivityCfgByType(act_id)

            if not IsEmptyTable(act_data) then
                local open_day = act_data.open_day
                local split_list = Split(open_day,":")

                if #split_list >= 7 then
                    week_desc = Language.BiZuo.Everyday
                else
                    for k,v in pairs(split_list) do
                        local day = tonumber(v) == 0 and 7 or tonumber(v)
                        local day_str = Language.Common.WeekDay[day]
                        if k == #split_list then
                            week_desc = week_desc .. day_str
                        else
                            week_desc = week_desc .. day_str .. "、"
                        end
                    end
                end
            end
        end
    end

    if week_desc == "" then
        local cfg = self:GetAcfCfgByIndex(index)
        week_desc = cfg and cfg.open_week_day or ""
    end

    if time_desc == "" then
        local cfg = self:GetAcfCfgByIndex(index)
        time_desc = cfg and cfg.open_time or ""
    end

    return week_desc, time_desc
end

