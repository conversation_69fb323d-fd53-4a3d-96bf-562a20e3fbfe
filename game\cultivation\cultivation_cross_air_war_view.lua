function CultivationView:LoadCallBack_CrossAirWar()
    if not self.kfkz_reward_list then
    	self.kfkz_reward_list = AsyncListView.New(ItemCell, self.node_list["kfkz_reward_list"])
        self.kfkz_reward_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list.btn_kfkz_guide, BindTool.Bind(self.OnClickKFKZGuide, self))
    XUI.AddClickEventListener(self.node_list.btn_kfkz_reward_show, BindTool.Bind(self.OnClickKFKZRewardPreview, self))
    XUI.AddClickEventListener(self.node_list.go_kfkz_btn, BindTool.Bind(self.OnClickGoToKFKZ, self))
    self.refresh_time = nil
end

function CultivationView:ReleaseCallBack_CrossAirWar()
    if self.kfkz_reward_list then
        self.kfkz_reward_list:DeleteMe()
        self.kfkz_reward_list = nil
    end
end

function CultivationView:OnFlush_CrossAirWar(param_t)
    self.node_list["txt_kfkz_title"].text.text = CrossAirWarWGData.Instance:GetEnteranceTitle()
    self.node_list["txt_kfkz_desc"].text.text = CrossAirWarWGData.Instance:GetGameplayDesc()
    self.node_list["txt_kfkz_open_time"].text.text = CrossAirWarWGData.Instance:GetOpenTimeDesc()

    local reward_list = CrossAirWarWGData.Instance:GetShowFieldRewardData()
    if self.kfkz_reward_list and reward_list then
        self.kfkz_reward_list:SetDataList(reward_list)
    end
end

function CultivationView:ShowIndexCallBack_CrossAirWar()
    if not self.refresh_time then
        self.refresh_time = TimeWGCtrl.Instance:GetServerTime()
        CrossAirWarWGCtrl.Instance:SendCSCrossAirWarOperate(CROSS_AIR_WAR_OPERATE_TYPE.BASE_INFO)
    else
        if TimeWGCtrl.Instance:GetServerTime() - self.refresh_time >= 5 then
            CrossAirWarWGCtrl.Instance:SendCSCrossAirWarOperate(CROSS_AIR_WAR_OPERATE_TYPE.BASE_INFO)
        end
    end 
end

function CultivationView:OnClickKFKZGuide()
    -- CrossAirWarWGCtrl.Instance:OpenBossVoteView()
    local role_tip = RuleTip.Instance
    if role_tip then
        local title, desc = Language.CrossAirWar.AirWarTitle, Language.CrossAirWar.AirWarDesc
        if title ~= nil and desc ~= nil then
            role_tip:SetTitle(title)
            role_tip:SetContent(desc)
        end
    end
end

function CultivationView:OnClickKFKZRewardPreview()
    CrossAirWarWGCtrl.Instance:OpenRewardPreview()
end

function CultivationView:OnClickGoToKFKZ()
    local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_AIR_WAR)
    local act_status = CrossAirWarWGData.Instance:GetAirWarActivityStatus()

    if act_info and act_info.status == ACTIVITY_STATUS.OPEN and act_status ~= CROSS_AIR_WAR_AUCTION_STATUS.STATUS_END then-- 
        CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_AIR_WAR)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
    end
end