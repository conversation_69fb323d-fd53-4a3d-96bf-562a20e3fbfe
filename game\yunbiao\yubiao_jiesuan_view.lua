YuBiaoJieSuan = YuBiaoJieSuan or BaseClass(SafeBaseView)

function YuBiaoJieSuan:__init()
	self.view_style = ViewStyle.Half
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_result_panel")
	self:AddViewResource(0, "uis/view/yunbiao_ui_prefab", "layout_jiesuan")

	self.exp = 0
	self.gold = 0

	self.data_list = {}
end

function YuBiaoJieSuan:__delete()

end

function YuBiaoJieSuan:ReleaseCallBack()

	self.index = 0

	self.exp = 0
	self.gold = 0

	self.data_list = nil

	if self.gold_cell then
		self.gold_cell:DeleteMe()
		self.gold_cell = nil
	end
	if self.exp_cell then
		self.exp_cell:DeleteMe()
		self.exp_cell = nil
	end
	if self.other_cell then
		self.other_cell:DeleteMe()
		self.other_cell = nil
	end

	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.YuBiaoJieSuan, self.get_guide_ui_event)
	self.get_guide_ui_event = nil
end

function YuBiaoJieSuan:LoadCallBack()
	-- local item_num = 0
	-- for k,v in pairs(self.data_list) do
	-- 	item_num = item_num + 1
	-- end

	local free_husong_num = YunbiaoWGData.Instance:GetFreeHusongNum()--免费护送次数
	local canyunbiao_num =YunbiaoWGData.Instance:GetMaxGoumaiNum() --可购买次数

	if free_husong_num == 0  then
		self.index = 1
	end
	self.node_list.btn_quxiao.button:AddClickListener(BindTool.Bind(self.OnClinkCloseHandler, self))
	self.node_list.btn_guanbi.button:AddClickListener(BindTool.Bind(self.OnClinkCloseHandler, self))
	self.node_list.btn_queren.button:AddClickListener(BindTool.Bind2(self.OnClinkAgainHandler, self,self.index))
	-- self.node_list.img_twoicon:SetActive(false)
	-- self.node_list.img_twoexp:SetActive(false)
	self.node_list.rich_jiesuan:SetActive(true)
	self.gold_cell = ItemCell.New(self.node_list["cell1_1"])
	self.exp_cell = ItemCell.New(self.node_list["cell2_2"])

	self.reward_item_list = {}
	-- 奖励列表
	for i = 1, 5 do
		self.reward_item_list[i] = ItemCell.New(self.node_list["reward_" .. i])
	end

	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.YuBiaoJieSuan, self.get_guide_ui_event)

	-- self.cd_time = GlobalTimerQuest:AddDelayTimer(BindTool.Bind1(self.CompleteCountDownTime,self), 5)
end

-- 引导升级
function YuBiaoJieSuan:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == "btn_guanbi" then
		if self.task_is_free then
			return self.node_list.btn_guanbi, BindTool.Bind(self.Close, self)
		else
			return self.node_list.btn_quxiao, BindTool.Bind(self.Close, self)
		end
		
	end

	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

function YuBiaoJieSuan:ShowIndexCallBack()
	MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.NAMECOLOR)
	self:Flush()
end

function YuBiaoJieSuan:CloseCallBack()
	-- if self.cd_time then
	-- 	GlobalTimerQuest:CancelQuest(self.cd_time)
	-- 	self.cd_time = nil
	-- end
	local cur_zhu_task_cfg = TaskWGData.Instance:GetTaskTypeZhuCfg()
	if cur_zhu_task_cfg and TaskWGData.Instance:GetTaskIsCanCommint(cur_zhu_task_cfg.task_id) then
	    if YunbiaoWGData.Instance:IsZhuXianHuSong(cur_zhu_task_cfg.task_id) then
	        TaskWGCtrl.Instance:SendTaskCommit(cur_zhu_task_cfg.task_id)
	    end
	end
end

function YuBiaoJieSuan:OnFlush()
	self.gold_cell:SetData({item_id = 65536})
	self.gold_cell:SetRightBottomTextVisible(true)
	self.exp_cell:SetData({item_id = 90050})
	self.exp_cell:SetRightBottomTextVisible(true)
	for i = 1, 2 do
		self.node_list["cell"..i.."_double"]:SetActive(self.is_double)
	end

	-- 奖励列表
	for i = 1, 5 do
		if self.data_list[i] then
			self.node_list["reward_" .. i]:SetActive(true)
			self.reward_item_list[i]:SetData(self.data_list[i])
		else
			self.node_list["reward_" .. i]:SetActive(false)
		end
	end

	local color = YunbiaoWGData.Instance:GetCurHusongColor()
	local task_cfg = YunbiaoWGData.Instance:GetTaskCfgByColor(color)
	self.node_list.text_name.text.text = string.format(Language.YunBiao.JiesuanName,task_cfg.task_name) 
	
 	local count = YunbiaoWGData.Instance:GetHusongRemainTimes()
 	if YunbiaoWGData.Instance:IsWeiHusong() then
 		self.node_list["rich_jiesuan"].text.text = ""
		self.node_list.btn_quxiao:SetActive(false)
		self.node_list.btn_queren:SetActive(false)
 	elseif count > 0 then
 		self.node_list["rich_jiesuan"].text.text = string.format(Language.YunBiao.JieSuanTitle1,count)
 		self.node_list["queren_text"].text.text = Language.YunBiao.Btn_Name_1
		self.node_list.btn_quxiao:SetActive(true)
		self.node_list.btn_queren:SetActive(true)
 	else
 		self.node_list["rich_jiesuan"].text.text = string.format(Language.YunBiao.JieSuanTitle4,count)
 		self.node_list["queren_text"].text.text = Language.YunBiao.Btn_Name_2
		self.node_list.btn_quxiao:SetActive(false)
		self.node_list.btn_queren:SetActive(true)
 	end

 	self.node_list["rich_jiesuan"]:SetActive(not self.task_is_free)
 	self.node_list["btn_guanbi"]:SetActive(self.task_is_free)
 	self.node_list["btn_queren"]:SetActive(not self.task_is_free)
 	self.node_list["btn_quxiao"]:SetActive(not self.task_is_free)

	local str = ""
	local str1 = ""
	local exp_wan = 0
	local icon_wan = 0

	str = CommonDataManager.ConverExpExtend(self.exp, false)
	-- self.node_list.lbl_exp.text.text = str
	self.exp_cell:SetRightBottomColorText(str)

	str1 = CommonDataManager.ConverExpExtend(self.gold, false)
	-- self.node_list.lbl_icon.text.text = str1
	self.gold_cell:SetRightBottomColorText(str1)

	-- self.node_list.img_twoexp:SetActive(self.is_double)
	MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.NAMECOLOR)
end


function YuBiaoJieSuan:CompleteCountDownTime()
	self:Close()
end

function YuBiaoJieSuan:SetJieSuanData(data_list, exp, gold, is_double, insurance, task_is_free)
	self.data_list = data_list
	self.exp = exp
	local exp_per = RoleWGData.Instance:GetRoleExpCorrection()
	self.exp = self.exp * exp_per
	
	self.gold = gold

	self.is_double = is_double
	self.insurance = insurance
	self.task_is_free = task_is_free 		-- 是否是引导的第一次护送（免费护送）
	if not self:IsOpen() then
		self:Open()
	else
		self:Flush()
	end
end




function YuBiaoJieSuan:OnClinkCloseHandler()
	self:Close()
end

function YuBiaoJieSuan:OnClinkAgainHandler(index)
	local count = YunbiaoWGData.Instance:GetHusongRemainTimes()
 	if count > 0 and not YunbiaoWGData.Instance:IsWeiHusong() then
 		local task_id = ConfigManager.Instance:GetAutoConfig("husongcfg_auto").other[1].task_id
        if task_id then
            local role = Scene.Instance:GetMainRole()
            local role_x,role_y = role:GetLogicPos()
            local data = TaskWGData.Instance:GetTaskConfig(task_id).accept_npc
            if role_x == data.x and role_y == data.y then
                YunbiaoWGCtrl.Instance:OpenWindow()
            else
				TaskWGCtrl.Instance:JumpFly(data.scene,data.x,data.y,true)--SendFlyByShoe(102, 51, 63)
				YunbiaoWGCtrl.Instance:SetIsClickHuSong(true)
            end
        end
        self:Close()
 	else
 		self:Close()
 	end
end
