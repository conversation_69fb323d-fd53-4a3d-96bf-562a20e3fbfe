-- J-节日活动-限时累充.xls
local item_table={
[1]={item_id=26191,num=1,is_bind=1},
[2]={item_id=22753,num=1,is_bind=1},
[3]={item_id=44085,num=1,is_bind=1},
[4]={item_id=26174,num=5,is_bind=1},
[5]={item_id=50301,num=4,is_bind=1},
[6]={item_id=26193,num=1,is_bind=1},
[7]={item_id=44050,num=1,is_bind=1},
[8]={item_id=48089,num=1,is_bind=1},
[9]={item_id=26174,num=10,is_bind=1},
[10]={item_id=48071,num=5,is_bind=1},
[11]={item_id=48183,num=1,is_bind=1},
[12]={item_id=27613,num=1,is_bind=1},
[13]={item_id=26174,num=20,is_bind=1},
[14]={item_id=48117,num=1,is_bind=1},
[15]={item_id=48187,num=1,is_bind=1},
[16]={item_id=26174,num=45,is_bind=1},
[17]={item_id=48440,num=1,is_bind=1},
[18]={item_id=48187,num=2,is_bind=1},
[19]={item_id=48408,num=1,is_bind=1},
[20]={item_id=50301,num=140,is_bind=1},
[21]={item_id=48443,num=1,is_bind=1},
[22]={item_id=45017,num=1,is_bind=1},
[23]={item_id=50301,num=280,is_bind=1},
[24]={item_id=57359,num=1,is_bind=1},
[25]={item_id=26450,num=1,is_bind=1},
[26]={item_id=26174,num=90,is_bind=1},
[27]={item_id=48441,num=2,is_bind=1},
[28]={item_id=50301,num=480,is_bind=1},
[29]={item_id=46401,num=1,is_bind=1},
[30]={item_id=26455,num=1,is_bind=1},
[31]={item_id=26174,num=135,is_bind=1},
[32]={item_id=48441,num=4,is_bind=1},
[33]={item_id=50301,num=1400,is_bind=1},
[34]={item_id=57360,num=1,is_bind=1},
[35]={item_id=26459,num=1,is_bind=1},
[36]={item_id=26174,num=180,is_bind=1},
[37]={item_id=48441,num=6,is_bind=1},
[38]={item_id=50301,num=2400,is_bind=1},
[39]={item_id=23351,num=1,is_bind=1},
[40]={item_id=26463,num=1,is_bind=1},
[41]={item_id=26174,num=225,is_bind=1},
[42]={item_id=48441,num=10,is_bind=1},
[43]={item_id=50301,num=4000,is_bind=1},
[44]={item_id=57368,num=1,is_bind=1},
[45]={item_id=57369,num=1,is_bind=1},
[46]={item_id=50301,num=3,is_bind=1},
[47]={item_id=46048,num=2,is_bind=1},
[48]={item_id=27611,num=5,is_bind=1},
}

return {
config_param={
{},
{start_server_day=16,end_server_day=61,grade=1,},
{start_server_day=61,end_server_day=91,grade=2,},
{start_server_day=91,end_server_day=9999,grade=3,}
},

config_param_meta_table_map={
},
reward={
{},
{ID=2,stage_value=4000,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},},
{ID=3,stage_value=8000,reward_item={[0]=item_table[6],[1]=item_table[7],[2]=item_table[8],[3]=item_table[9],[4]=item_table[10]},},
{ID=4,stage_value=16000,reward_item={[0]=item_table[11],[1]=item_table[7],[2]=item_table[12],[3]=item_table[13],[4]=item_table[8]},},
{ID=5,stage_value=28000,reward_item={[0]=item_table[14],[1]=item_table[15],[2]=item_table[12],[3]=item_table[16],[4]=item_table[8]},},
{ID=6,stage_value=48000,reward_item={[0]=item_table[17],[1]=item_table[18],[2]=item_table[19],[3]=item_table[6],[4]=item_table[20]},},
{ID=7,stage_value=80000,reward_item={[0]=item_table[21],[1]=item_table[6],[2]=item_table[16],[3]=item_table[22],[4]=item_table[23]},},
{ID=8,stage_value=140000,reward_item={[0]=item_table[24],[1]=item_table[25],[2]=item_table[26],[3]=item_table[27],[4]=item_table[28]},},
{ID=9,stage_value=400000,reward_item={[0]=item_table[29],[1]=item_table[30],[2]=item_table[31],[3]=item_table[32],[4]=item_table[33]},},
{ID=10,stage_value=700000,reward_item={[0]=item_table[34],[1]=item_table[35],[2]=item_table[36],[3]=item_table[37],[4]=item_table[38]},},
{ID=11,stage_value=1200000,reward_item={[0]=item_table[39],[1]=item_table[40],[2]=item_table[41],[3]=item_table[42],[4]=item_table[43]},},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,reward_item={[0]=item_table[44],[1]=item_table[25],[2]=item_table[26],[3]=item_table[27],[4]=item_table[28]},},
{grade=3,},
{grade=3,reward_item={[0]=item_table[45],[1]=item_table[35],[2]=item_table[36],[3]=item_table[37],[4]=item_table[38]},},
{grade=3,}
},

reward_meta_table_map={
[31]=9,	-- depth:1
[32]=10,	-- depth:1
[33]=11,	-- depth:1
[35]=2,	-- depth:1
[41]=8,	-- depth:1
[37]=4,	-- depth:1
[38]=5,	-- depth:1
[39]=6,	-- depth:1
[40]=7,	-- depth:1
[30]=8,	-- depth:1
[42]=31,	-- depth:2
[36]=3,	-- depth:1
[29]=40,	-- depth:2
[22]=33,	-- depth:2
[27]=38,	-- depth:2
[26]=37,	-- depth:2
[25]=36,	-- depth:2
[24]=35,	-- depth:2
[43]=10,	-- depth:1
[21]=43,	-- depth:2
[20]=42,	-- depth:3
[19]=41,	-- depth:2
[18]=29,	-- depth:3
[17]=39,	-- depth:2
[16]=27,	-- depth:3
[15]=26,	-- depth:3
[14]=25,	-- depth:3
[13]=24,	-- depth:3
[28]=17,	-- depth:3
[44]=22,	-- depth:3
},
interface={
{},
{grade=1,},
{grade=2,},
{grade=3,}
},

interface_meta_table_map={
},
config_param_default_table={start_server_day=1,end_server_day=16,grade=0,open_role_level=100,},

reward_default_table={grade=0,ID=1,stage_value=2000,reward_item={[0]=item_table[1],[1]=item_table[4],[2]=item_table[46],[3]=item_table[47],[4]=item_table[48]},special_frame=0,special_content=0,},

interface_default_table={grade=0,text_color="#984b26",activity_des="1.活动期间<color=#6FBB6F>充值到指定额度</color>可免费领取豪华大礼，时不我待，不容错过！\n2.充值按照<color=#6FBB6F>1元=10灵玉</color>计入\n3.活动结束后未及时领取的奖励通过<color=#6FBB6F>邮件</color>发放",}

}

