require("game/new_festival_activity/new_festival_consume_rebate/new_festival_consume_rebate_view")
require("game/new_festival_activity/new_festival_consume_rebate/new_festival_consume_rebate_wg_data")

NewFestivalConsumeRebateWGCtrl = NewFestivalConsumeRebateWGCtrl or BaseClass(BaseWGCtrl)
function NewFestivalConsumeRebateWGCtrl:__init()
	if NewFestivalConsumeRebateWGCtrl.Instance then
		ErrorLog("[NewFestivalConsumeRebateWGCtrl] Attemp to create a singleton twice !")
	end
	NewFestivalConsumeRebateWGCtrl.Instance = self

	self.data = NewFestivalConsumeRebateWGData.New()
	self:RegisterAllProtocols()
end

function NewFestivalConsumeRebateWGCtrl:__delete()
	NewFestivalConsumeRebateWGCtrl.Instance = nil

    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end
end

function NewFestivalConsumeRebateWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCOACounsumeRebateInfo, "OnSCOACounsumeRebateInfo")
end

function NewFestivalConsumeRebateWGCtrl:SendConsumeRebateReq(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.NEW_FESTIVAL_ACT_CONSUME_REBATE
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function NewFestivalConsumeRebateWGCtrl:OnSCOACounsumeRebateInfo(protocol)
    self.data:SetConsumeRebateInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.NewFestivalActivityView, TabIndex.new_festival_activity_2348)
end
