require("game/guild_answer/guild_answer_wg_data")
require("game/guild_answer/guild_answer_rank")
require("game/guild_answer/guild_answer_timer")
require("game/guild_answer/guild_pass_view")
require("game/guild_answer/guild_pass_progress_view")
GuildAnswerWGCtrl = GuildAnswerWGCtrl or BaseClass(BaseWGCtrl)

function GuildAnswerWGCtrl:__init()
	if nil ~= GuildAnswerWGCtrl.Instance then
		print("[GuildAnswerWGCtrl] attempt to create singleton twice!")
		return
	end
	GuildAnswerWGCtrl.Instance = self
	self.data = GuildAnswerWGData.New()
	self.rank_view = GuildAnswerRank.New()
	self.answer_time_view = GuildAnswerStartTimer.New(GuideModuleName.GuildAnswerStartTimer)
	self.pass_view = GuildPassView.New(GuideModuleName.GuildPass)
	self.pass_progree_view = GuildPassProgressView.New()
	self:RegisterAllProtocols()

	self.act_call_back = BindTool.Bind(self.ActivityChangeCallBack,self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.act_call_back)
end

function GuildAnswerWGCtrl:__delete()
	if nil ~= self.data then
		self.data:DeleteMe()
	    self.data = nil
    end

    if nil ~= self.rank_view then
        self.rank_view:DeleteMe()
        self.rank_view = nil
    end

	if nil ~= self.answer_time_view then
		self.answer_time_view:DeleteMe()
		self.answer_time_view = nil
	end

    if nil ~= self.pass_view then
		self.pass_view:DeleteMe()
		self.pass_view = nil
	end

    if nil ~= self.pass_progree_view then
		self.pass_progree_view:DeleteMe()
		self.pass_progree_view = nil
	end

	if self.act_call_back then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_call_back)
		self.act_call_back = nil
	end

	GuildAnswerWGCtrl.Instance = nil
end

function GuildAnswerWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCGuildQuestionPlayerInfo, "OnGuildQuestionPlayerInfo")
	self:RegisterProtocol(SCGuildQuestionQuestionInfo, "OnGuildQuestionQuestionInfo")
	self:RegisterProtocol(SCGuildQuestionGuildRankInfo, "OnGuildQuestionGuildRankInfo")
	self:RegisterProtocol(SCGuildQuestionRoundResult, "OnSCGuildQuestionRoundResult")
	--self:RegisterProtocol(SCGuildQuestionStateInfo, "OnGuildQuestionStateInfo")

    self:RegisterProtocol(SCGuildChuanGongInviteInfo, "OnSCGuildChuanGongInviteInfo")--邀请信息
    self:RegisterProtocol(SCStartGuildChuanGong, "OnSCStartGuildChuanGong")--传功广播
    self:RegisterProtocol(SCGuildChuanGongInviteMeInfo, "OnSCGuildChuanGongInviteMeInfo")--邀请我的信息


	self:RegisterProtocol(CSGuildQuestionEnterReq)
	self:RegisterProtocol(CSGuildChuanGongInviteReq) --邀请传功
	self:RegisterProtocol(CSGuildChuanGongInviteAck) --回应邀请
	self:RegisterProtocol(CSGuildQuestionQueryRank) --请求答题排行
end

-- 玩家信息
function GuildAnswerWGCtrl:OnGuildQuestionPlayerInfo(protocol)
	self.data:SetQuestionPlayerInfo(protocol)
	local msg_info = ChatWGData.CreateMsgInfo()
	local time = TimeWGCtrl.Instance:GetServerTime()
	msg_info.send_time_str = TimeUtil.FormatTable2HMS(os.date("*t", time))
	-- msg_info.channel_window_bubble_type = -1
	if protocol.true_uid > 0 then
		msg_info.channel_type = CHANNEL_TYPE.GUILD
		msg_info.msg_reason = CHAT_MSG_RESSON.GUILD_TIPS
		local num1 = GuildAnswerWGData.Instance:GetAnswerNumByScore(self.data:GetGuildQuestionOtherCfg().guild_score)
		local num2 = GuildAnswerWGData.Instance:GetAnswerNumByScore(protocol.guild_score)
		msg_info.content = string.format(Language.GuildAnswer.AnswerRight, protocol.true_name, num1, num2)
		--GlobalTimerQuest:AddDelayTimer(function ()
			ChatWGCtrl.Instance.data:AddChannelMsg(msg_info)
		--end, 1.5)
		if protocol.true_uid == protocol.uid then
			GlobalEventSystem:Fire(OtherEventType.GUILD_ANSWER_TRUE)
		end
	end
	--

	ChatWGCtrl.Instance:RefreshChannel(CHANNEL_TYPE.GUILD, true)

	if protocol.is_gather == 1 and not self.is_open_chat and Scene.Instance:GetSceneType() == SceneType.GUILD_ANSWER_FB  then
		ChatWGCtrl.Instance:OpenChatWindow(ChatTabIndex[CHANNEL_TYPE.GUILD])
		MainuiWGCtrl.Instance:HideOrShowChatView(true)
		self.is_open_chat = true
	end

	if self.rank_view:IsLoaded() then
		self.rank_view:Flush()
	end

	if self.answer_time_view and self.answer_time_view:IsLoaded() then
    	self.answer_time_view:FlushDinnerPanel()
    end
end

function GuildAnswerWGCtrl:ResetOpenChatFlag()
	self.is_open_chat = nil
	self.answer_time_view:ClearEndTimeFlag()
end

-- 帮派题目
function GuildAnswerWGCtrl:OnGuildQuestionQuestionInfo(protocol)
	self.data:SetQuestionInfo(protocol)
	if Scene.Instance:GetSceneType() == SceneType.GUILD_ANSWER_FB then
		self:OpenTimeView()
	end

	if self.answer_time_view and self.answer_time_view:IsLoaded() then
		if protocol.question_index == self.data:GetMaxAnswerNum() and protocol.question_end_timestamp == 0 then
			self.answer_time_view:FlushAnswerEndTime()
		else
			self.answer_time_view:Flush()
		end
	end
	ChatWGCtrl.Instance:RefreshChannel(CHANNEL_TYPE.GUILD, true)
	end

function GuildAnswerWGCtrl:OnGuildQuestionGuildRankInfo(protocol)
	 --print_error("rank", protocol)
	self.data:SetGuildRankInfo(protocol)
	if self.rank_view:IsLoaded() then
		self.rank_view:Flush()
	end
	if self.answer_time_view and self.answer_time_view:IsLoaded() then
    	self.answer_time_view:FlushRankPanel()
    end
    if ViewManager.Instance:IsOpen(GuideModuleName.GuildActivityDaTiRank) then
		ViewManager.Instance:FlushView(GuideModuleName.GuildActivityDaTiRank)
	end
end

function GuildAnswerWGCtrl:OnSCGuildQuestionRoundResult(protocol)
	--print_error("round", protocol)
	self.data:SetGroundInfo(protocol)

	local ture_answer = ChatWGData.CreateMsgInfo()
	ture_answer.send_time_str = TimeUtil.FormatTable2HMS(os.date("*t", TimeWGCtrl.Instance:GetServerTime()))
	ture_answer.channel_type = CHANNEL_TYPE.GUILD
	ture_answer.msg_reason = CHAT_MSG_RESSON.GUILD_TIPS
	ture_answer.content = string.format(Language.GuildAnswer.RightKey, protocol.question_index, protocol.answer or "")
	ChatWGCtrl.Instance.data:AddChannelMsg(ture_answer)

	ChatWGCtrl.Instance:RefreshChannel(CHANNEL_TYPE.GUILD, true)
end

-- function GuildAnswerWGCtrl:OnGuildQuestionStateInfo(protocol)
-- 	self.data:SetGuildQuestionStateInfo(protocol)
-- end

function GuildAnswerWGCtrl:SendGuildQuestionEnterReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSGuildQuestionEnterReq)
	protocol:EncodeAndSend()
end

function GuildAnswerWGCtrl:OpenTimeView()
	if not self.answer_time_view:IsOpen() then
		self.answer_time_view:Open()
	end
end

function GuildAnswerWGCtrl:CloseTimeView()
	if self.answer_time_view then
		self.answer_time_view:Close()
	end
end

function GuildAnswerWGCtrl:FlushPassBtnState()
	self.answer_time_view:Flush(0, "FlushPassBtnState")
end

function GuildAnswerWGCtrl:OpenRankView()
	if not self.rank_view:GetOpenState() and not self.rank_view:IsOpen() then
		local question_info = GuildAnswerWGData.Instance:GetQuestionInfo()
		if question_info.question_state ~= 1 or question_info.question_end_timestamp ~= 0 then
			self.rank_view:Open()
		end
	end

	if self.answer_time_view:IsOpen() then
		self.answer_time_view:Flush(0, "FlushGoDaTiBtn")
	end
end

function GuildAnswerWGCtrl:ShowOrHideAnswerRankList(is_show)
	if self.answer_time_view and self.answer_time_view:IsOpen() then
		self.answer_time_view:Flush(0, "ShowOrHideAnswerRankList", {state = is_show})
	end
end

function GuildAnswerWGCtrl:FlushRankView()
	self.rank_view:Flush()
end

function GuildAnswerWGCtrl:IsRankViewOpen()
	return self.rank_view:IsOpen()
end

function GuildAnswerWGCtrl:CloseRankView()
	if self.rank_view:IsOpen() and self.rank_view:GetOpenState() then
		self.rank_view:Close()
	end
end

function GuildAnswerWGCtrl:ShowOrHideGoDaTiBtn()
	if self.answer_time_view:IsOpen() then
		self.answer_time_view:ShowGoDaTiBtn()
	end
end

function GuildAnswerWGCtrl:DOPlayForward()
	self.rank_view:DOPlayForward()
end

function GuildAnswerWGCtrl:DOPlayBackwards()
	self.rank_view:DOPlayBackwards()
end

function GuildAnswerWGCtrl:SendInviteOperate(target_uid)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGuildChuanGongInviteReq)
	protocol.target_uid = target_uid
	protocol:EncodeAndSend()
end

function GuildAnswerWGCtrl:SendCSGuildQuestionQueryRank()
	local protocol = ProtocolPool.Instance:GetProtocol(CSGuildQuestionQueryRank)
	protocol:EncodeAndSend()
end

function GuildAnswerWGCtrl:SendAcceptOperate(target_uid, is_agree)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGuildChuanGongInviteAck)
	protocol.req_uid = target_uid
	protocol.agree = is_agree
	protocol:EncodeAndSend()
end

function GuildAnswerWGCtrl:OnSCGuildChuanGongInviteInfo(protocol)
    self.data:GetSetInviteInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.GuildPass, nil, GuildPassView.STATE.SEND)
end

function GuildAnswerWGCtrl:OnSCGuildChuanGongInviteMeInfo(protocol)
    self.data:GetSetInviteMeInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.GuildPass, nil, GuildPassView.STATE.ACCEPT)
    self.answer_time_view:Flush(0, "FlushBtnPassRed")
end

function GuildAnswerWGCtrl:OnSCStartGuildChuanGong(protocol)
    self.data:SetSCStartGuildChuanGong(protocol)
	local main_obj = Scene.Instance:GetObjectByObjId(protocol.inviter_objid)
	local passive_obj = Scene.Instance:GetObjectByObjId(protocol.invited_objid)
	if main_obj ~= nil then
		main_obj:PlayChuanGongAnim(SceneObjAnimator.Pass)
	end
	if passive_obj ~= nil then
		passive_obj:PlayChuanGongAnim(SceneObjAnimator.Pass)--SceneObjAnimator.Pass2策划要求都用抬手动作
	end
	if main_obj and passive_obj then
		main_obj:RotaToTarget(passive_obj:GetRoot().transform.position, 0.2)
		passive_obj:RotaToTarget(main_obj:GetRoot().transform.position, 0.2)
	end
	self:CheckSelfChuanGong(protocol.inviter_objid, protocol.invited_objid)
	if self.data:GetUidIsInvitemeList(protocol.inviter_objid,protocol.invited_objid) then
		ViewManager.Instance:FlushView(GuideModuleName.GuildPass, nil, GuildPassView.STATE.ACCEPT)
	end
end

function GuildAnswerWGCtrl:FlushLeftPanel()
	self.answer_time_view:Flush(0, "FlushPassTimes")
end

function GuildAnswerWGCtrl:FlushTimerView()
	self.answer_time_view:Flush()
end

function GuildAnswerWGCtrl:CheckSelfChuanGong(obj_id1, obj_id2)
	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		local vo = main_role:GetVo()
		if vo and vo.obj_id == obj_id1 or vo.obj_id == obj_id2 then
			self:OpenPassProgress()
		end
	end
end

function GuildAnswerWGCtrl:OpenPassProgress()
	--print_error("打开进度")
	if GatherBar.Instance:IsOpen() then
		Scene.Instance:SendStopGather()
	end
	self.pass_progree_view:Open()
end

function GuildAnswerWGCtrl:ClosePassProgreeView()
	if self.pass_progree_view:IsOpen() then
		self.pass_progree_view:Close()
	end
end

function GuildAnswerWGCtrl:HidePassBtnAnim(flag)
    GuildAnswerWGData.Instance:SetShowInviteAnimFlag(false)
	if self.answer_time_view and self.answer_time_view:IsLoaded() then
    	self.answer_time_view:ShowPassBtnAnim(flag)
    end
end

function GuildAnswerWGCtrl:ActivityChangeCallBack(activity_type, status, end_time)
	if ACTIVITY_TYPE.GUILD_ANSWER == activity_type then
		local chuangong_act = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GUILD_CHUAN_GONG)
		if status == ACTIVITY_STATUS.OPEN then
			self:SendCSGuildQuestionQueryRank()
			if not IsEmptyTable(chuangong_act) and (chuangong_act.status == ACTIVITY_STATUS.STANDY or chuangong_act.status == ACTIVITY_STATUS.OPEN) then
				MainuiWGCtrl.Instance:ActivitySetButtonVisible(ACTIVITY_TYPE.GUILD_CHUAN_GONG, false)
			end
		elseif status == ACTIVITY_STATUS.STANDY then
			if not IsEmptyTable(chuangong_act) and (chuangong_act.status == ACTIVITY_STATUS.STANDY or chuangong_act.status == ACTIVITY_STATUS.OPEN) then
				MainuiWGCtrl.Instance:ActivitySetButtonVisible(ACTIVITY_TYPE.GUILD_CHUAN_GONG, false)
			end
		elseif status == ACTIVITY_STATUS.CLOSE then
			if not IsEmptyTable(chuangong_act) and (chuangong_act.status == ACTIVITY_STATUS.STANDY or chuangong_act.status == ACTIVITY_STATUS.OPEN) then
				local callback = function ()
					local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GUILD_CHUAN_GONG)
					if not IsEmptyTable(act_info) then
						MainuiWGCtrl.Instance.view:ActivityChangeCallBack(ACTIVITY_TYPE.GUILD_CHUAN_GONG, act_info.status, act_info.next_time, act_info.open_type)
					end
				end
				MainuiWGCtrl.Instance:AddInitCallBack(ACTIVITY_TYPE.GUILD_CHUAN_GONG, callback)
			end
			--仙盟答题在活动结束时，自动将玩家踢出，不要停留在场景中 60s倒计时踢出
			local scene_type = Scene.Instance:GetSceneType()
			if scene_type == SceneType.GUILD_ANSWER_FB then
				GuildAnswerWGCtrl.Instance:StartLevelSceneTimer()
			end
		end
	end
end

function GuildAnswerWGCtrl:ShowOrHideGuildAnswer(enable)
	if self.answer_time_view and self.answer_time_view:IsLoaded() then
		self.answer_time_view:ShowOrHideRoot(enable)
	end
end

function GuildAnswerWGCtrl:StartLevelSceneTimer()
	if self.answer_time_view and self.answer_time_view:IsLoaded() then
		self.answer_time_view:StartLevelSceneTimer()
	end
end

function GuildAnswerWGCtrl:GetAnswereTaskView()
	return self.answer_time_view
end