KfOneVoneFisish = KfOneVoneFisish or BaseClass(SafeBaseView)

function KfOneVoneFisish:__init()
	self.view_style = ViewStyle.Half
	self.default_index = 0
	self.view_layer = UiLayer.Pop
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_jiesuan_panel")
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_kf_1v1_end")
end

function KfOneVoneFisish:__delete()

end

function KfOneVoneFisish:ReleaseCallBack()

end

function KfOneVoneFisish:LoadCallBack()
	self.node_list["btn_out_end"].button:AddClickListener(BindTool.Bind1(self.OnClickBuyOut, self))

	self.node_list.layout_txt_end.button:AddClickListener(BindTool.Bind1(self.Close<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,self))
	local out_fb_time = TimeWGCtrl.Instance:GetServerTime() + 10
	local result_cfg = KuafuOnevoneWGData.GetFinishGetItem(self.end_result)
	local kf_jf = KuafuOnevoneWGData.Instance:Get1V1InfoJiFen()
	local kf_cfg = KuafuOnevoneWGData.Instance:GetRewardBaseCell(kf_jf)
	local rewared_id_1 = KuafuOnevoneWGData.Instance:GetRewardBaseCell(self.old_duanwei_jifen)
	local rewared_id_2 = KuafuOnevoneWGData.Instance:GetRewardBaseCell(self.awrd_score + self.old_duanwei_jifen )
	for i=1,4 do
		self["reward_baecell"..i] = ItemCell.New(self.node_list["reward_"..i])
	end

	self.node_list.same_duanwei:SetActive(false)
	self.node_list.same_duanwei_1:SetActive(false)
	self.node_list.duanwei_xianshi_1:SetActive(false)
	self.node_list.duanwei_xianshi:SetActive(false)
	if rewared_id_1.name ~= rewared_id_2.name then
		if self.awrd_score > 0 then
			self.node_list.old_duanwei.text.text = rewared_id_1.name
			self.node_list.new_duanwei.text.text = rewared_id_2.name
			self.node_list.old_duanwei_1.text.text = rewared_id_1.name
			self.node_list.new_duanwei_1.text.text = rewared_id_2.name
		else
			self.node_list.old_duanwei.text.text = rewared_id_1.name
			self.node_list.new_duanwei.text.text = rewared_id_2.name
			self.node_list.old_duanwei_1.text.text = rewared_id_1.name
			self.node_list.new_duanwei_1.text.text = rewared_id_2.name
		end
		self.node_list.duanwei_jinji:SetActive(self.awrd_score > 0)
		self.node_list.duanwei_jiangji:SetActive(self.awrd_score <= 0)
	else
		self.node_list.same_duanwei.text.text = string.format(Language.Kuafu1V1.NowDuanWei,rewared_id_1.name)
		self.node_list.same_duanwei_1.text.text = string.format(Language.Kuafu1V1.NowDuanWei,rewared_id_1.name)
		self.node_list.duanwei_jinji:SetActive(false)
		self.node_list.duanwei_jiangji:SetActive(false)
	end

	if 0 == self.end_result then
		self.node_list.duanwei_xianshi_1:SetActive(rewared_id_1.name ~= rewared_id_2.name)
		self.node_list.same_duanwei_1:SetActive(rewared_id_1.name == rewared_id_2.name)
		self.node_list["lose"]:SetActive(true)
		self.node_list["victory"]:SetActive(false)
		-- self.node_list.failure_arrow:SetActive(true)
		-- self.node_list.win_arrow:SetActive(false)
		self.node_list.rich_jifen.text.text = string.format(Language.Kuafu1V1.ResultTip4,self.awrd_score + self.old_duanwei_jifen, self.awrd_score)--ToColorStr(string.format(Language.Kuafu1V1.ResultTip4, self.awrd_score), COLOR3B.WHITE)
	else
		self.node_list.duanwei_xianshi:SetActive(rewared_id_1.name ~= rewared_id_2.name)
		self.node_list.same_duanwei:SetActive(rewared_id_1.name == rewared_id_2.name)
		self.node_list["lose"]:SetActive(false)
		self.node_list["victory"]:SetActive(true)
		-- self.node_list.failure_arrow:SetActive(false)
		-- self.node_list.win_arrow:SetActive(true)
		self.node_list.rich_jifen.text.text = string.format(Language.Kuafu1V1.ResultTip1, self.awrd_score + self.old_duanwei_jifen,self.awrd_score)--ToColorStr(string.format(Language.Kuafu1V1.ResultTip1, self.awrd_score), COLOR3B.WHITE)
	end

	local reward_list = KuafuOnevoneWGData.Instance:GetKFOneVOneOtherCfg()
	local data_list = self.end_result == 0 and reward_list.loser_item or reward_list.winner_item
	local kf1v1_info = KuafuOnevoneWGData.Instance:Get1V1Info()
	local max_count = KuafuOnevoneWGData.GetMaxJionTimes()

	if not IsEmptyTable(kf1v1_info) and max_count > 0 then
		local surplus_times = max_count + kf1v1_info.today_buy_times - kf1v1_info.today_reward_count
		if surplus_times <= 0 then
			data_list = {}
		end
	end

	for i=1,4 do
		--self.node_list["reward_"..i]:SetActive(i <= 2)
		if data_list and data_list[i - 1] then
			self.node_list["reward_"..i]:SetActive(true)
			self["reward_baecell"..i]:SetData(data_list[i - 1] )
		else
			self.node_list["reward_"..i]:SetActive(false)
		end
	end
	--local score_reward = KuafuOnevoneWGData.Instance:GetGongXunReward(self.end_result)
	--self.node_list.rich_get_exp.text.text = string.format(Language.Kuafu1V1.ResultTip2, self.add_gongxun)--ToColorStr(string.format(Language.Kuafu1V1.ResultTip2, score_reward), COLOR3B.WHITE)
	self.node_list.rich_get_exp:SetActive(false)
	--CountDownManager.Instance:AddCountDown("onevone_finish_confirm", nil, BindTool.Bind1(self.CloseViewHandler, self),nil, 5, 1)
	if CountDownManager.Instance:HasCountDown("onevone_finish_confirm") then
		CountDownManager.Instance:RemoveCountDown("onevone_finish_confirm")
	end
	self:UpdateTime(TimeWGCtrl.Instance:GetServerTime(), TimeWGCtrl.Instance:GetServerTime() + 5)
	CountDownManager.Instance:AddCountDown("onevone_finish_confirm", BindTool.Bind1(self.UpdateTime, self), BindTool.Bind1(self.CloseViewHandler, self),  TimeWGCtrl.Instance:GetServerTime() + 5 , nil, 1)
end

function KfOneVoneFisish:OnClickBuyOut()
	Field1v1WGCtrl.Instance:Send1V1ReturnReadyScene()
end

function KfOneVoneFisish:UpdateTime(elapse_time, total_time)
	local temp_seconds = GameMath.Round(total_time - elapse_time)
	self.node_list.lbl_close.text.text = string.format(Language.Field1v1.EndTimeCountDown,temp_seconds)
end

function KfOneVoneFisish:ShowIndexCallBack(index)
	if nil == self.end_result then
		return
	end
end

function KfOneVoneFisish:OpenCallBack()
end

function KfOneVoneFisish:CloseCallBack()
	self.end_result = nil
	self.old_duanwei_jifen = nil
	 self.add_gongxun = nil
	-- AudioManager.Instance:PlayOpenCloseUiEffect()

	if CountDownManager.Instance:HasCountDown("onevone_finish_confirm") then
		CountDownManager.Instance:RemoveCountDown("onevone_finish_confirm")
	end
	Field1v1WGCtrl.Instance:LeaveZhanChangScene()
	for i=1,4 do
		if self["reward_baecell"..i] then
			self["reward_baecell"..i]:DeleteMe()
			self["reward_baecell"..i] = nil
		end
	end

	--CrossServerWGCtrl.ConnectBack()
	--FuBenWGCtrl.Instance:SendLeaveFB()
	-- FuBenWGCtrl.Instance:SendLeaveFB()
end

function KfOneVoneFisish:SetEndData(data)
	self.end_result = data.result
	self.awrd_score = data.award_score
	self.old_duanwei_jifen = data.old_award_score or 0
	self.add_gongxun = data.add_gongxun
end

function KfOneVoneFisish:CloseViewHandler()
	self:Close()
end
