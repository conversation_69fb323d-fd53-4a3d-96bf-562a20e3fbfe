require("game/conquest_war/conquest_war_view")
require("game/conquest_war/conquest_war_fgb_view")
require("game/conquest_war/conquest_war_tjmc_view")
require("game/conquest_war/conquest_war_yyhd_view")
require("game/conquest_war/conquest_war_zxzc_view")
require("game/conquest_war/conquest_war_zllx_view")
require("game/conquest_war/conquest_war_kfkz_view")
require("game/conquest_war/conquest_war_wg_data")

ConquestWarWGCtrl = ConquestWarWGCtrl or BaseClass(BaseWGCtrl)

function ConquestWarWGCtrl:__init()
	if ConquestWarWGCtrl.Instance then
		print_error("[ConquestWarWGCtrl]:Attempt to create singleton twice!")
	end
	ConquestWarWGCtrl.Instance = self

	if not self.view then
		self.view = ConquestWarView.New(GuideModuleName.ConquestWarView)
	end

	if not self.data then
		self.data = ConquestWarWGData.New()
	end
end

function ConquestWarWGCtrl:__delete()
	ConquestWarWGCtrl.Instance = nil

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end
end
