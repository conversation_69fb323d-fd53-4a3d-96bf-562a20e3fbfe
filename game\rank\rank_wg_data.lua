RankKind = {
	Person = 1,										-- 个人排行
	Guild = 2,										-- 仙盟排行
	Cross = 3,										-- 跨服排名
	Peakathletics = 4,								-- 巅峰竞技
	MultiPlayer = 5,								-- 团队竞技
}

PersonRankType = {
	ZhanLi = 0,										-- 综合战力榜
	Level = 1,										-- 等级榜
	Equip = 2,										-- 装备战力榜
	MeiLi = 3,										-- 魅力总榜
	CAPABILITY_CAMP_1 = 4,							-- 阵营1综合战力榜
	CAPABILITY_CAMP_2 = 5,							-- 阵营2综合战力榜
	CAPABILITY_CAMP_3 = 6,							-- 阵营3综合战力榜
	Mount = 7,										-- 坐骑战力榜
	Mentality = 8,									-- 修炼战力榜
	Wing = 9,										-- 羽翼战力榜
	Charm = 10,										-- 魅力榜

	CharmMale = 11,									-- 男性魅力榜
	CharmFemale = 12,								-- 女性魅力榜
	KillWorldBoss = 13,								-- 世界boss伤害
	RaChongzhi = 14,								-- 充值排行榜
	RkConsumeGold = 15,								-- 本服消费排行榜
	RedPaperConsumeGold = 16,						-- 红包土豪榜
	RedPaperLuckyValue = 17,						-- 红包幸运榜
	RaDayChongzhiNum = 19,							-- 随机活动每日充值
	RaDayConsumeNum = 20,							-- 随机活动每日消费

	PERSON_RANK_TYPE_BOSS_HUNTER = 21,				-- 猎魔达人
	PlantingTreeWatering = 22,						-- 植树活动浇水次数排行
	SpeedRecharge = 23,								-- 至尊充值排行榜
	MountGrade = 26,								-- 坐骑进阶榜
	FabaoGrade = 27,								-- 法宝进阶榜
	LingChongGrade = 28,							-- 灵宠进阶榜
	WingGrade = 29,									-- 羽翼进阶榜
	ShenWuGrade = 30,								-- 神武进阶榜
	ZhanqiGrade = 45, 								-- 战骑进阶榜
	LingqiGrade = 42,								-- 灵骑进阶榜
	BianShenGrade = 57,								-- 变身进阶榜
	JianZhenGrade = 50,                             -- 剑阵进阶榜
	TianShenGrade = 62,                             -- 天神战力榜
	XianWaGrade = 49,                               -- 仙娃榜
	CrossCompetition = 90,              			-- 跨服竞技榜
	DuJieJingjie = 96,								-- 渡劫境界榜
	BeastCapability = 97,							-- 排行榜里面的幻兽战力榜

	OGA_Consume = 31,								-- 开服活动-首日消费榜
	OGA_LEVEL = 32,									-- 开服活动-角色等级榜
	OGA_MOUNT_GRADE = 33,							-- 开服活动-坐骑进阶榜
	OGA_LINGCHONG_GRADE = 34,						-- 开服活动-灵宠进阶榜
	OGA_DAY_CHONGZHI_RANK = 35,						-- 开服活动-今日充值榜
	OGA_STONE_LEVEL = 36,							-- 开服活动-宝石镶嵌等级
    OGA_CAPABILITY_ALL = 37,						-- 开服活动-角色战力榜
    OGA_EQUIP_SCORE_RANK = 42,						-- 开服活动-装备评分榜
	OGA_LINGQI_GRADE = 44,						    -- 开服活动-灵骑进阶榜
	OGA_LINGYI_GRADE = 46,						    -- 开服活动-灵翼进阶榜
	LingYiGrade = 46,								-- 灵翼进阶榜
	TianXianGeRank = 48, 							-- 天仙阁排行榜

	PERSON_RANK_TYPE_CSA_DAY_CONSUME_RANK = 38,		-- 合服活动-每日消费排行
	GUAJI_EFFICIENCY = 39,			-- 挂机效率
	PERSON_RANK_TYPE_CSA_RECHARGE_TOTAL = 40,		-- 合服活动-充值排行

	Competition = 53,              					-- 竞技榜
	BianShen = 54,									-- 小目标 变身
	-- LuoShu = 55,									-- 小目标 洛书
	XianQiGrade = 70,								-- --仙器榜
	XiLian = 56,									-- 小目标 洗练

	PERSON_RANK_TYPE_PROFESS_SCORE = 58,            --表白积分榜
	PERSON_RANK_TYPE_BE_PROFESS_SCORE  = 59,		--被表白积分榜
	JinJie = 60,									--境界榜

	PERSON_RANK_TYPE_SEND_FLOWER_MALE = 80,         --魅力排行男榜
	PERSON_RANK_TYPE_SEND_FLOWER_FEMALE = 81,       --魅力排行女榜

	BEAST = 84,									--幻兽战力榜
	SHENBING = 85,								--神兵战力榜
	CULTIVATION = 86,							--修为等级榜
	TIANSHEN = 87,								--天神战力榜
	GOLD = 88,									--灵玉消费榜
	SHENSHOU = 89,								--异兽战力榜

	CampZhanLi = 10000,	--自己阵营战力排行
}

GuildRankType = {
	Level = 0,										-- 军团等级榜
	XianmengZhan = 1,								-- 仙盟战排行
	Kill_World_Boss = 2,							-- 击杀世界boss数量
	XianMengBipinKillWorldBoss = 3,					-- 仙盟比拼期间击杀世界boss数量
	Day_Increase_Capability = 4,					-- 仙盟每日增加战力
    Capability = 5,									-- 仙盟战力榜

    GUILD_RANK_TYPE_CSA_BOSS_HUNTER = 8,			-- 仙盟猎魔榜
}

MultiRankType = {
	TeamFight = 1,
	MVP = 2,
}

CrossRankType = {
	CROSS_ZHANLI = 0,
	CROSS_MountGrade = 1,
	CROSS_FabaoGrade = 2,
	CROSS_WingGrade = 3,
	CROSS_ShenWuGrade = 4,
	CROSS_TianXianGeRank = 5,
	CROSS_XianWaGrade = 6,
	CROSS_JianZhenGrade = 7,
	CROSS_JinJie = 8,
	CROSS_TianShenGrade = 9,
	CROSS_Level = 10,
	CROSS_Equip = 11,
	CROSS_LingChongGrade = 13,
	CROSS_XianQiGrade = 16,						    -- 跨服仙器榜
}

----------------------------------------------------
----------------------------------------------------
RankClientType = {
	ZHANLI 				= 1,						-- 战力
	DENGJI 				= 2,						-- 等级
	ZHUANGBEI			= 3,						-- 装备
	XIANMENG_ZHANLI		= 4,						-- 仙盟
	MEILI 				= 5,						-- 魅力
	ZUOQI 				= 6,						-- 坐骑
	YUYI 				= 7,						-- 羽翼
	XIANMENGZHAN 		= 8,						-- 仙盟战
	SHOULIE 			= 9,						-- BOSS狩猎
	RICHMAN 			= 10,						-- 土豪榜
	LUCKMAN				= 11,						-- 运气榜
	-- 12 废弃
	CROSS_ZHANLI 		= 13,						-- 跨服排名
	PEAK_FIGHT			= 14,						-- 巅峰竞技
	CROSS_MEILI			= 15,						-- 跨服魅力
	TEAM_FIGHT			= 16,						-- 团队竞技
	MVP					= 17,						-- MVP次数排行
	MountGrade 			= 26,						-- 坐骑进阶榜
	FabaoGrade 			= 27,						-- 法宝进阶榜
	LingChongGrade		= 28,						-- 灵宠进阶榜
	WingGrade 			= 29,						-- 羽翼进阶榜
	ShenWuGrade 		= 30,						-- 神武进阶榜
	ZhanqiGrade 		= 44,						-- 战骑进阶榜
	LingqiGrade         = 42,						-- 灵骑进阶榜
	LingYiGrade 		= 46,						-- 灵翼进阶榜
	JianZhenGrade       = 50,                       -- 剑阵进阶榜
	TianShenGrade       = 62,                       -- 天神战力榜
	XianWaGrade         = 59,                       -- 仙娃榜
	CrossCompetition 	= 90,						-- 跨服竞技榜
	DuJieJingjie 		= 96,						-- 渡劫境界榜
	BeastCapability 	= 97,						-- 排行榜里面的幻兽战力榜

	OGA_Consume = 31,								-- 开服活动-首日消费
	OGA_LEVEL = 32,									-- 开服活动-角色等级榜
	OGA_MOUNT_GRADE = 33,							-- 开服活动-坐骑进阶榜
	OGA_LINGCHONG_GRADE = 34,						-- 开服活动-灵宠进阶榜
	OGA_DAY_CHONGZHI_RANK = 35,						-- 开服活动-今日充值榜
	OGA_STONE_LEVEL = 36,							-- 开服活动-宝石镶嵌等级
    OGA_CAPABILITY_ALL = 37,						-- 开服活动-角色战力榜
    OGA_EQUIP_SCORE_RANK = 42,						-- 开服活动-装备评分榜
	OGA_LINGQI_GRADE = 43,						    -- 开服活动-灵骑进阶榜
	OGA_LINGYI_GRADE = 45,						    -- 开服活动-灵翼进阶榜
	TianXianGeRank = 48,	--符文塔排行榜

	XIANMENG  			= 100,
	XIANMENGKILLBOSS 	= 101,						-- 仙盟狩猎
	CHONGZHI 	 		= 102,						-- 充值
	XIAOFEI 	 		= 103,						-- 消费
	XIANMENG_UPZHANLI 	= 104,						-- 仙盟战力提升排行
	XIANMENG_UPKILLBOSS = 105,
	RAND_CHONGZHI 		= 106,						-- 每日充值排行
	RAND_CONSUME		= 107,						-- 每日消费排行
	RAND_CHONGZHI_2		= 108,						-- 至尊充值排行2
	RAND_MULTI_CHALLENGE	= 109,					-- 勋章排行榜
	KF_1VN 				= 110, 						-- 跨服1VN
	GET_MARRIED = 112,								-- 咱们结婚吧
	PERSON_RANK_TYPE_CSA_DAY_CONSUME_RANK = 113,	-- 合服活动-每日消费排行
	GUAJI_EFFICIENCY = 114,			-- 挂机效率
	PERSON_RANK_TYPE_CSA_RECHARGE_TOTAL = 115,		-- 合服活动-充值排行
	ROLE_CHARM_MAN_RANK = 80,                      -- 魅力男榜
	ROLE_CHARM_FEMAN_RANK = 81,                    --魅力女榜

	BEAST = 84,									--幻兽战力榜
	SHENBING = 85,								--神兵战力榜
	CULTIVATION = 86,							--修为等级榜
	TIANSHEN = 87,								--天神战力榜
	GOLD = 88,									--灵玉消费榜
	SHENSHOU = 89,								--异兽战力榜

	PERSON_RANK_TYPE_OA_RECHARGE_RANK = 94,		-- 运营活动 - 充值榜
	PERSON_RANK_TYPE_OA_CONSUME_RANK = 95,		-- 运营活动 - 消费榜
}

RankServerType = {
	[RankKind.Person] = {
		[PersonRankType.ZhanLi] = RankClientType.ZHANLI,						-- 战力
		[PersonRankType.Level] = RankClientType.DENGJI,							-- 等级
		[PersonRankType.Equip] = RankClientType.ZHUANGBEI,						-- 装备
		[PersonRankType.MeiLi] = RankClientType.MEILI,							-- 魅力
		[PersonRankType.Mount] = RankClientType.ZUOQI,							-- 坐骑
		[PersonRankType.Wing] = RankClientType.YUYI,							-- 羽翼
		[PersonRankType.KillWorldBoss] = RankClientType.SHOULIE,				-- 击杀世界boss排行
		[PersonRankType.RaChongzhi] = RankClientType.CHONGZHI,					-- 充值排行
		[PersonRankType.RkConsumeGold] = RankClientType.XIAOFEI,				-- 本服消费排行
		[PersonRankType.RedPaperConsumeGold] = RankClientType.RICHMAN,			-- 红包土豪排行
		[PersonRankType.RedPaperLuckyValue] = RankClientType.LUCKMAN,			-- 红包幸运排行
		[PersonRankType.TianXianGeRank] = RankClientType.TianXianGeRank,				-- 符文塔排行

		[PersonRankType.MountGrade] = RankClientType.MountGrade,						-- 坐骑进阶榜
		[PersonRankType.FabaoGrade] = RankClientType.FabaoGrade,						-- 法宝进阶榜
		[PersonRankType.LingChongGrade] = RankClientType.LingChongGrade,					-- 灵宠进阶榜
		[PersonRankType.WingGrade] = RankClientType.WingGrade,						-- 羽翼进阶榜
		[PersonRankType.ShenWuGrade] = RankClientType.ShenWuGrade,				-- 神武进阶榜
		[PersonRankType.ZhanqiGrade] = RankClientType.ZhanqiGrade,				-- 战骑进阶榜
		[PersonRankType.LingqiGrade] = RankClientType.LingqiGrade,				-- 灵骑进阶榜
		[PersonRankType.JianZhenGrade] = RankClientType.JianZhenGrade,          -- 剑阵进阶榜
		[PersonRankType.TianShenGrade] = RankClientType.TianShenGrade,          -- 天神进阶榜
		[PersonRankType.XianWaGrade] = RankClientType.XianWaGrade,          -- 天神进阶榜
		[PersonRankType.CrossCompetition] = RankClientType.CrossCompetition,    -- 跨服竞技榜
		[PersonRankType.DuJieJingjie] = RankClientType.DuJieJingjie,          -- 渡劫境界榜
		[PersonRankType.BeastCapability] = RankClientType.BeastCapability,          -- 排行榜里面的幻兽战力榜

		[PersonRankType.RaDayChongzhiNum] = RankClientType.RAND_CHONGZHI,		-- 每日充值排行
		[PersonRankType.RaDayConsumeNum] = RankClientType.RAND_CONSUME,			-- 每日消费排行
		[PersonRankType.SpeedRecharge] = RankClientType.RAND_CHONGZHI_2,			-- 至尊充值排行2
		[PersonRankType.PERSON_RANK_TYPE_CSA_DAY_CONSUME_RANK] = RankClientType.PERSON_RANK_TYPE_CSA_DAY_CONSUME_RANK,
		[PersonRankType.GUAJI_EFFICIENCY] = RankClientType.GUAJI_EFFICIENCY,			-- 挂机效率
		[PersonRankType.PERSON_RANK_TYPE_SEND_FLOWER_MALE] = RankClientType.ROLE_CHARM_MAN_RANK,    --魅力男榜
		[PersonRankType.PERSON_RANK_TYPE_SEND_FLOWER_FEMALE] = RankClientType.ROLE_CHARM_FEMAN_RANK,   --魅力女榜

		[PersonRankType.BEAST] = RankClientType.BEAST,   						--幻兽战力榜				
		[PersonRankType.SHENBING] = RankClientType.SHENBING,   					--神兵战力榜
		[PersonRankType.CULTIVATION] = RankClientType.CULTIVATION,   			--修为等级榜
		[PersonRankType.TIANSHEN] = RankClientType.TIANSHEN,  					--天神战力榜
		[PersonRankType.GOLD] = RankClientType.GOLD,  							--灵玉消费榜
		[PersonRankType.SHENSHOU] = RankClientType.SHENSHOU,   					--异兽战力榜

		[PersonRankType.OGA_LEVEL] = RankClientType.OGA_LEVEL,
		[PersonRankType.OGA_MOUNT_GRADE] = RankClientType.OGA_MOUNT_GRADE,
		[PersonRankType.OGA_LINGCHONG_GRADE] = RankClientType.OGA_LINGCHONG_GRADE,
		[PersonRankType.OGA_DAY_CHONGZHI_RANK] = RankClientType.OGA_DAY_CHONGZHI_RANK,
		[PersonRankType.OGA_STONE_LEVEL] = RankClientType.OGA_STONE_LEVEL,
        [PersonRankType.OGA_CAPABILITY_ALL] = RankClientType.OGA_CAPABILITY_ALL,
        [PersonRankType.OGA_EQUIP_SCORE_RANK] = RankClientType.OGA_EQUIP_SCORE_RANK,
		[PersonRankType.OGA_LINGQI_GRADE] = RankClientType.OGA_LINGQI_GRADE,
		[PersonRankType.OGA_LINGYI_GRADE] = RankClientType.OGA_LINGYI_GRADE,
		[PersonRankType.LingYiGrade] = RankClientType.LingYiGrade,
		[PersonRankType.PERSON_RANK_TYPE_CSA_RECHARGE_TOTAL] = RankClientType.PERSON_RANK_TYPE_CSA_RECHARGE_TOTAL,

	},
	[RankKind.Guild] = {
		[0] = RankClientType.XIANMENG,				-- 仙盟
		[1] = RankClientType.XIANMENGZHAN,			-- 仙盟战
		[2] = RankClientType.XIANMENGKILLBOSS,		-- 仙盟狩猎
		[3] = RankClientType.XIANMENG_UPKILLBOSS,	-- 仙盟比拼期间击杀boss数量排行榜
		[4] = RankClientType.XIANMENG_UPZHANLI,		-- 仙盟战力提升
		[5] = RankClientType.XIANMENG_ZHANLI,		-- 仙盟总战力
	},

	[RankKind.Cross] = {
		[0] = RankClientType.CROSS_ZHANLI,			-- 跨服排名
		[1] = RankClientType.CROSS_MEILI,			-- 跨服魅力
		[4] = RankClientType.GET_MARRIED, 			-- 咱们结婚吧
	},
	[RankKind.Peakathletics] = {
		[0] = RankClientType.PEAK_FIGHT,			-- 巅峰竞技
	},
	[RankKind.MultiPlayer] = {
		[1] = MultiRankType.TeamFight,				-- 团队竞技
		[2] = MultiRankType.MVP,					-- MVP次数排行
	},
}
----------------------------------------------------
-- 待删除end
----------------------------------------------------
MAX_RANK_NUM = 100 --前几名上排行榜

RankWGData = RankWGData or BaseClass()

function RankWGData:__init()
	if RankWGData.Instance ~= nil then
		ErrorLog("[RankWGData] Attemp to create a singleton twice !")
	end
	RankWGData.Instance = self

	self.rank_list = {}
	self.self_value_list = {}
	self.my_rank = {}
	self.my_rank_data = {}
	self.word_level = 0								-- 世界等级
	self.top_user_level = 0							-- 服务器最高世界等级
	self.target_uid = 0
	self.admire_num = 0                             -- 被赞美次数
	self.can_admire_num = 0                         -- 今日可赞美次数
	self.contempt_num = 0
	self.admire_uid = {}                            -- 被赞美过的用户列表
	self.dalao_list = {} 							-- 排行前三的数据
	self.act_my_rank = {}
	self.is_mobai = 0								-- 膜拜按钮数据次数
	self.zan_uid = 0                                -- 点赞id
	self.remain_zan_count = 0                       -- 点赞数
	self.zan_array = {}                             -- 点赞人id表
	self.dianzan_red = true                         -- 点赞红点显示
	self.kf_world_level = 0                     -- 跨服等级


	local replacemonsterconfig_auto = ConfigManager.Instance:GetAutoConfig("replacemonsterconfig_auto")
	self.re_world_monster_cfg = ListToMapList(replacemonsterconfig_auto.re_world_monster, "monster_id")
	RemindManager.Instance:Register(RemindName.Rank, BindTool.Bind(self.IsShowRankRedPoint, self))			          -- 排行榜、名人堂
	--RemindManager.Instance:Register(RemindName.ZhuZaiShenDian, BindTool.Bind(self.IsShowShenDianRedPoint, self))	  -- 主宰神殿

end

function RankWGData:__delete()
	RankWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.Rank)
	RemindManager.Instance:UnRegister(RemindName.ZhuZaiShenDian)
end


--排行榜  名人堂红点提示
function RankWGData:IsShowRankRedPoint()
	if not FunOpen.Instance:GetFunIsOpened("rank")  then
		return 0
	end
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowDianZanRedPoint() then
		return 1
	end
	return 0
end
function RankWGData:IsShowDianZanRedPoint()
	if self:GetRemaindLikesCount() > 0 and self.dianzan_red then
		self.dianzan_red = false
		return 1
	else
		return 0
	end
end
function RankWGData:IsShowShenDianRedPoint()
	local uid = ZhuZaiShenDianWGData.Instance:GetMengZhuUid()
	if ZhuZaiShenDianWGData.Instance:GetRewardNum() == 1 and uid > 0 then
		return 1
	end
	return 0
end
function RankWGData.GetRankKey(kind, rank_type)
	if not kind or not rank_type then
		return
	end
	return kind * 1000 + rank_type
end

--类型转换客户端-->>服务器端
function RankWGData:GetRankServerType(client_type)
	local server_type = {}
	for k, v in pairs(RankServerType) do
		for key, value in pairs(v) do
			if client_type == value then
				return k, key
			end
		end
	end
	ErrorLog("no such Server rank type !!")
end

-- 获取当前自己排行数据
function RankWGData:GetSelfValueData(kind, rank_type)
	local rank_key = RankWGData.GetRankKey(kind, rank_type)
	return self.self_value_list[rank_key] or {}
end

-- 设置个人排行数据
function RankWGData:SetPersonRankData(rank_type, protocol)
	--print_error("----设置个人排行数据----", rank_type, list)
	local rank_key = RankWGData.GetRankKey(RankKind.Person, rank_type)
	local list = protocol.rank_list
	local self_rank = protocol.self_rank
	local self_value = protocol.self_value
	local data = {}
	data.self_rank = self_rank
	data.self_value = self_value
	self.self_value_list[rank_key] = data

	self.rank_list[rank_key] = list
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	for i, v in ipairs(list) do
		v.rank_index = i
		AvatarManager.Instance:SetAvatarKey(v.user_id, v.avatar_key_big, v.avatar_key_small)

		if v.user_id == main_role_vo.role_id then
			self.my_rank[rank_key] = i
			self.my_rank_data[rank_key] = v
		end
	end
	if rank_type == PersonRankType.ZhanLi then
		rank_key = RankWGData.GetRankKey(RankKind.Person, PersonRankType.CampZhanLi)
		self.rank_list[rank_key] = self:GetCampZhanliList(list)
		for i, v in ipairs(self.rank_list[rank_key]) do
			v.rank_index = i
			AvatarManager.Instance:SetAvatarKey(v.user_id, v.avatar_key_big, v.avatar_key_small)

			if v.user_id == main_role_vo.role_id then
				self.my_rank[rank_key] = i
				self.my_rank_data[rank_key] = v
			end
		end
	elseif rank_type == PROFESS_RANK_TYPE.PERSON_RANK_TYPE_RA_PROFESS_MALE then                  --表白墙男榜
		for i,v in ipairs(list) do
			AvatarManager.Instance:SetAvatarKey(v.user_id, v.avatar_key_big, v.avatar_key_small)
		end
		ProfessWallWGData.Instance:SetProfessRankMaleInfo(list)
		ProfessWallWGCtrl.Instance:FlushView(TabIndex.profess_wall_rank_male)
	elseif rank_type == PROFESS_RANK_TYPE.PERSON_RANK_TYPE_RA_PROFESS_FEMALE then                --表白墙女榜
		for i,v in ipairs(list) do
			AvatarManager.Instance:SetAvatarKey(v.user_id, v.avatar_key_big, v.avatar_key_small)
		end
		ProfessWallWGData.Instance:SetProfessRankFemaleInfo(list)
		ProfessWallWGCtrl.Instance:FlushView(TabIndex.profess_wall_rank_female)
	elseif rank_type == RankClientType.ROLE_CHARM_MAN_RANK then                  --魅力男榜
		for i,v in ipairs(list) do
			AvatarManager.Instance:SetAvatarKey(v.user_id, v.avatar_key_big, v.avatar_key_small)
		end
		
		RoleCharmNoticeWGData.Instance:SetCharmRankManInfo(list)
		RoleCharmNoticWGCtrl.Instance:RuningRequesFlushCharmRankInfo()
	elseif rank_type == RankClientType.ROLE_CHARM_FEMAN_RANK then                --魅力女榜
		for i,v in ipairs(list) do
			AvatarManager.Instance:SetAvatarKey(v.user_id, v.avatar_key_big, v.avatar_key_small)
		end

		RoleCharmNoticeWGData.Instance:SetCharmRankFeManInfo(list)
		RoleCharmNoticWGCtrl.Instance:RuningRequesFlushCharmRankInfo()
	elseif rank_type == RankClientType.PERSON_RANK_TYPE_OA_RECHARGE_RANK then		--充值榜
		for i,v in ipairs(list) do
			AvatarManager.Instance:SetAvatarKey(v.user_id, v.avatar_key_big, v.avatar_key_small)
		end

		RechargeRankWGData.Instance:SetRechargeRankInfo(list)
		ViewManager.Instance:FlushView(GuideModuleName.RechargeRankView)
		RechargeRankWGCtrl.Instance:RuningRequesFlushRechargeRankInfo()
	elseif rank_type == RankClientType.PERSON_RANK_TYPE_OA_CONSUME_RANK then		-- 消费榜
		for i,v in ipairs(list) do
			AvatarManager.Instance:SetAvatarKey(v.user_id, v.avatar_key_big, v.avatar_key_small)
		end

		ConsumeRankWGData.Instance:SetConsumeRankInfo(list)
		ViewManager.Instance:FlushView(GuideModuleName.ConsumeRankView)
		ConsumeRankWGCtrl.Instance:RuningRequesFlushConsumeRankInfo()
	end
end

function RankWGData:GetCampZhanliList(list)
	local camp_list = {}
	for i,v in ipairs(list) do
		if v.camp == GameVoManager.Instance:GetMainRoleVo().camp then
			table.insert(camp_list, __TableCopy(v))
		end
	end
	return camp_list
end

-- 设置仙盟排行数据
function RankWGData:SetGuildRankData(rank_type, protocol)
	local rank_key = RankWGData.GetRankKey(RankKind.Guild, rank_type)

	local list = protocol.rank_list
	local self_rank = protocol.self_rank
	local self_value = protocol.self_value
	local data = {}
	data.self_rank = self_rank
	data.self_value = self_value
	self.self_value_list[rank_key] = data
	self.rank_list[rank_key] = list

	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	for i, v in ipairs(list) do
		v.rank_index = i

		if v.guild_id == main_role_vo.guild_id then
			self.my_rank[rank_key] = i
			self.my_rank_data[rank_key] = v
		end
	end
end

function RankWGData:GetRankList()
	if self.rank_list then
		return self.rank_list
	end
	return nil
end

-- 设置跨服数据排行
function RankWGData:SetCrossRankData(rank_type, protocol)
	-- WeGetMarriedWGCtrl.Instance:SetRankListData(list)--报错临时屏蔽
	local rank_key = RankWGData.GetRankKey(RankKind.Cross, rank_type)
	local list = protocol.rank_list
	local self_rank = protocol.self_rank
	local self_value = protocol.self_value
	local data = {}
	data.self_rank = self_rank
	data.self_value = self_value
	self.self_value_list[rank_key] = data
	self.rank_list[rank_key] = list

	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	for i, v in ipairs(list) do
		v.rank_index = i

		if v.user_id == main_role_vo.role_id or main_role_vo.origin_uid == v.user_id then
			self.my_rank[rank_key] = i
			self.my_rank_data[rank_key] = v
		end
	end
end

--设置1V1跨服排行
function RankWGData:SetCross1v1WeekRecord(rank_type, list)
	local rank_key = RankWGData.GetRankKey(RankKind.Peakathletics, rank_type)

	self.rank_list[rank_key] = list

	if PersonRankType.PEAK_FIGHT == rank_type then
		table.sort(self.rank_list[rank_key], SortTools.KeyUpperSorters("score", "level"))
	end

	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	if nil == main_role_vo then
		return
	end
	--设置自己的跨服排名
	for i, v in ipairs(list) do
		v.rank_index = i

		if v.role_id == main_role_vo.role_id then
			self.my_rank[rank_key] = i
			self.my_rank_data[rank_key] = v
		end
	end
end

-- 设置团队竞技数据排行
function RankWGData:SetMultiRankData(rank_type, list)
	local rank_key = RankWGData.GetRankKey(RankKind.MultiPlayer, rank_type)
	if rank_type == MultiRankType.TeamFight or
		rank_type == MultiRankType.MVP then
		table.sort(list, SortTools.KeyUpperSorter("rank_value"))
	end
	self.rank_list[rank_key] = list

	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	for i, v in ipairs(list) do
		v.rank_index = i

		if v.role_id == main_role_vo.role_id then
			self.my_rank[rank_key] = i
			self.my_rank_data[rank_key] = v
		end
	end
end

function RankWGData:GetRankData(kind, rank_type)
	local rank_key = RankWGData.GetRankKey(kind, rank_type)
	if nil == self.rank_list[rank_key] then
		return {}
	end

	return self.rank_list[rank_key]
end

--获取一页的数据
function RankWGData:GetRankPageData(kind, rank_type, is_all)
	local rank_key = RankWGData.GetRankKey(kind, rank_type)
	if nil == self.rank_list[rank_key] then
		return {}
	end
	--print_error(self.rank_list)
	local page_data = {}
	self.dalao_list = {}
	for i = 1, #self.rank_list[rank_key] do
		if i > MAX_RANK_NUM then break end
		if i <= 3 then
			self.dalao_list[i] = self.rank_list[rank_key][i] 		-- 截取前三名
		end
		local diff = is_all and 0 or 3
		if self.rank_list[rank_key][i + diff] then
			self.rank_list[rank_key][i + diff].kind = kind
			self.rank_list[rank_key][i + diff].rank_type = rank_type
		end
		table.insert(page_data, self.rank_list[rank_key][i + diff])
	end
	return page_data
end

function RankWGData:GetDaLaoData()   -- 获取前三名信息
	return self.dalao_list
end

--获取自己的排名
function RankWGData:GetMyRank(kind, rank_type)
	local rank_key = RankWGData.GetRankKey(kind, rank_type)
	if self.my_rank then
		return self.my_rank[rank_key]
	end
end

--获取自己的排名数据
function RankWGData:GetMyRankData(kind, rank_type)
	local rank_key = RankWGData.GetRankKey(kind, rank_type)
	if self.my_rank_data then
		return self.my_rank_data[rank_key]
	end
end

-- 设置世界等级
function RankWGData:SetWordLevel(word_level)
	if self.word_level ~= word_level then
		self.word_level = word_level
		GlobalEventSystem:Fire(OtherEventType.WORLD_LEVEL_CHANGE, word_level)
		RoleWGData.Instance:CacheAttr("word_level", word_level) -- 缓存世界等级
	end
end

-- 获取世界等级
function RankWGData:GetWordLevel()
	return self.word_level
end

-- 设置服务器最高世界等级
function RankWGData:SetTopWordLevel(top_word_level)
	self.top_user_level = top_word_level
end

-- 获取服务器最高世界等级
function RankWGData:GetTopWordLevel()
	return self.top_user_level
end

-- 设置跨服等级
function RankWGData:SetKFWordLevel(hidden_world_level)
	self.kf_world_level = hidden_world_level
end

-- 获取跨服等级--现统一调用每日必做的接口 CrossServerWGData.Instance:GetServerMeanWorldLevel()
function RankWGData:GetKFWordLevel()
	return self.kf_world_level
end

----------------------------评价功能 begin-------------------------------
-- 获取评估信息
function RankWGData:SetRoleEvaluateInfo(target_uid, admire_num, contempt_num, can_admire_num)
	self.target_uid = target_uid
	self.admire_num = admire_num
	self.contempt_num = contempt_num
	self.can_admire_num = can_admire_num
end

function RankWGData:SetCanAdMireNum(num)
	self.can_admire_num = num
end
-- 获取角色uid
function RankWGData:GetRoleUid()
	return self.target_uid
end

-- 获取角色被赞美次数
function RankWGData:GetRoleAdmireNum()
	return self.admire_num
end

-- 获取角色被鄙视次数
function RankWGData:GetRoleContemptNum()
	return self.contempt_num
end

-- 获得今天可赞次数
function RankWGData:GetCanAdmireNum()
	return self.can_admire_num
end

-- 点赞过的用户ID列表
function RankWGData:SetAdmireUID(uid_list)
	-- 先清空之前的点赞过的ID
	for i = 1, #self.admire_uid do
		table.remove(self.admire_uid, i)
	end

	for i = 1, #uid_list do
		if 0 ~= uid_list[i] and uid_list then
			self.admire_uid[i] = uid_list[i]
		end
	end
end

-- 判断某元素是否存在表里
function RankWGData:ElementIsInTable(element, table)
	for _, v in pairs(table) do
		if element == v then
			return true
		end
	end
	return false
end
----------------------------评价功能 end-------------------------------

function RankWGData:GetNameByTypeAndLevel(type, level)
	local  image_type = 0
	if PersonRankType.FabaoGrade == type then
		image_type = 1
	elseif PersonRankType.LingChongGrade == type then
		image_type = 2
	elseif PersonRankType.WingGrade == type then
		image_type = 0
	elseif PersonRankType.ShenWuGrade == type then
		image_type = 3
	end

	-- if 0 == level then
	-- 	level = 1
	-- end
	if PersonRankType.MountGrade == type then
		local mount_cfg = ConfigManager.Instance:GetAutoConfig("mount_auto").upstar_level
		for i,v in ipairs(mount_cfg) do
			if v.star_level == level then
				return v.mount_name
			end
		end
	elseif PersonRankType.LingqiGrade == type then
		local lingqi_cfg = ConfigManager.Instance:GetAutoConfig("lingqi_mount_cfg_auto").upstar_level
		for i,v in ipairs(lingqi_cfg) do
			if v.star_level == level then
				return v.mount_name
			end
		end
	elseif PersonRankType.LingChongGrade == type then
		local lingchong_cfg = ConfigManager.Instance:GetAutoConfig("lingchong_auto").upstar_level
		for i,v in ipairs(lingchong_cfg) do
			if v.star_level == level then
				return v.image_name
			end
		end
	elseif PersonRankType.LingYiGrade == type then
		local lingyi_cfg = ConfigManager.Instance:GetAutoConfig("lingyi_cfg_auto").upstar_level
		for i,v in ipairs(lingyi_cfg) do
			if v.star_level == level then
				return v.mount_name
			end
		end
	end

	return "error"
end

function RankWGData:GetProfNameByProf(prof, sex)
	local role_sex = sex or RoleWGData.Instance.role_vo.sex
	local prof_name_list = ConfigManager.Instance:GetAutoConfig('zhuanzhicfg_auto').prof_name
	local zhiye = prof % 10
	local zhuanshu = math.floor(prof/ 10)

	for k,v in pairs(prof_name_list) do
		if v.prof == zhiye and v.zhuan_num == zhuanshu and v.sex == role_sex then
			return v.prof_name
		end
	end
end

function RankWGData:SetActMyRank(rank_type,my_rank,rank_value)
	if not self.act_my_rank[rank_type] then
		self.act_my_rank[rank_type] = {}
	end
	self.act_my_rank[rank_type].my_rank = my_rank
	self.act_my_rank[rank_type].rank_value = rank_value
	GlobalEventSystem:Fire(CHONGBANG_TIP_EVENT.ACT_CHECK,ChongBangTipWGData.ACT_TIP_TYPE.KAIFU,rank_type)
end

function RankWGData:GetActMyRank(rank_type)
	return self.act_my_rank and self.act_my_rank[rank_type]
end

function RankWGData:SetIsMobai(ismobai_table)
	self.is_mobai = ismobai_table.is_mobai
end

function RankWGData:GetIsmobai()
	return self.is_mobai
end

--获取替换怪物id
function RankWGData:GetReplaceMonster(monster_id)
	local cfg_list = self.re_world_monster_cfg[monster_id]
	for i, v in pairs(cfg_list) do
		if self.word_level >= v.world_min_level and self.word_level <= v.world_max_level then
			return v.remonster_id
		end
	end
	return monster_id
end

function RankWGData:GetRankMaxLikesCount()
	local count = 0
	local other_config = ConfigManager.Instance:GetAutoConfig("other_config_auto")
	if other_config and other_config.rank_reward[1] then
		count = other_config.rank_reward[1].count or 0
	end
	return count
end

function RankWGData:SetRankZanArray(protocol)
	self.zan_array = protocol.zan_array
end

function RankWGData:GetRankZanArray()
	return self.zan_array
end
--剩余点赞数量
function RankWGData:GetRemaindLikesCount()
	local count = self:GetRankMaxLikesCount()
	self.remain_zan_count = count
	for i=1,count do
		if self.zan_array[i] ~= 0 then
			self.remain_zan_count = self.remain_zan_count - 1
		end
	end
	return self.remain_zan_count
end

--打印点赞信息
function RankWGData:PrintRankZanArray()
	print_error(">>>>>>>>>>self.zan_array",self.zan_array)
end

----------------------------获取排行榜功力信息-------------------------------
--传入Item数据  获取职业描述  及 功力描述
function RankWGData:GetRankItemStrengthInfomation(data_info)
	local zhiye_text = ""
	local zhanli_text = ""

	if not IsEmptyTable(data_info) then
	--宗门排名
	if data_info.kind == RankKind.Guild then
		zhiye_text = Language.Rank.MZ..data_info.tuan_zhang_name
		-- zhanli_text = Language.Rank.XMZL..CommonDataManager.ConverExpByThousand(data_info.level)
		zhanli_text = CommonDataManager.ConverExpByThousand(data_info.level)
	end

	--个人排名
	if data_info.kind == RankKind.Person then
		zhiye_text = self:GetProfNameByProf(data_info.prof, data_info.sex)

		--战力排名
		if data_info.rank_type == PersonRankType.ZhanLi then
			-- zhanli_text = Language.Rank.ZL.. CommonDataManager.ConverExpByThousand(data_info.rank_value)
			zhanli_text =  CommonDataManager.ConverExpByThousand(data_info.rank_value)
		-- 装备评分榜
		elseif data_info.rank_type == PersonRankType.Equip then
			-- zhanli_text = Language.Rank.PF.. CommonDataManager.ConverExpByThousand(data_info.rank_value)
			zhanli_text =  CommonDataManager.ConverExpByThousand(data_info.rank_value)
		-- 境界榜
		elseif data_info.rank_type == PersonRankType.JinJie then
			local info = JingJieWGData.Instance:GetJingJieCfgBylevel(data_info.rank_value)
			local name = ""
			if info then
				name = info.name
			end
			-- zhanli_text =  Language.Rank.JZ..name
			zhanli_text = name
		--修真排名
		elseif data_info.rank_type == PersonRankType.TianXianGeRank then
			-- zhanli_text = Language.Rank.XZCS..FuBenPanelWGData.Instance:GetRankStr(data_info.rank_value)
			zhanli_text = FuBenPanelWGData.Instance:GetRankStr(data_info.rank_value)
		-- 等级排名
		elseif data_info.rank_type == PersonRankType.Level then
			zhanli_text = RoleWGData.Instance:TransToDianFengLevelStr(data_info.rank_value) .. Language.Rank.Ji
		--灵宠
		elseif data_info.rank_type == PersonRankType.LingChongGrade then
			local upstar_cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(1, data_info.rank_value)
			local image_grade_num = math.max(1, upstar_cfg.grade_num)
			local star_num = data_info.rank_value - 10 * (image_grade_num - 1 )
			-- zhanli_text = Language.Rank.LCPJ..image_grade_num..Language.Common.Jie..star_num..Language.Common.Star
			zhanli_text = image_grade_num..Language.Common.Jie..star_num..Language.Common.Star
		-- 坐骑排名
		elseif data_info.rank_type == PersonRankType.MountGrade then
			local upstar_cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(0, data_info.flexible_int)
			local image_grade_num = math.max(1, upstar_cfg.grade_num)
			local star_num = data_info.flexible_int - 10*(image_grade_num - 1 )
			-- zhanli_text = Language.Rank.ZJPJ..image_grade_num..Language.Common.Jie..star_num..Language.Common.Star
			zhanli_text = image_grade_num..Language.Common.Jie..star_num..Language.Common.Star
		-- 羽翼排名
		elseif data_info.rank_type == PersonRankType.WingGrade then
			-- zhanli_text = Language.Rank.YYDJ..data_info.rank_value..Language.Common.Ji
			zhanli_text = data_info.rank_value..Language.Common.Ji
		-- 法宝排名
		elseif data_info.rank_type ==  PersonRankType.FabaoGrade or data_info.rank_type == CrossRankType.CROSS_FabaoGrade then
			-- zhanli_text = Language.Rank.FBDJ..data_info.rank_value..Language.Common.Ji
			zhanli_text = data_info.rank_value..Language.Common.Ji
		-- 神兵排名
		elseif data_info.rank_type ==  PersonRankType.ShenWuGrade then
			-- zhanli_text = Language.Rank.SBDJ..data_info.rank_value..Language.Common.Ji
			zhanli_text = data_info.rank_value..Language.Common.Ji
		-- 剑阵排名
		elseif data_info.rank_type == PersonRankType.JianZhenGrade then
			-- zhanli_text = Language.Rank.JLDJ..data_info.rank_value..Language.Common.Ji
			zhanli_text = data_info.rank_value..Language.Common.Ji
		-- 天神排名
		elseif data_info.rank_type == PersonRankType.TianShenGrade then
			-- zhanli_text = Language.Rank.TSDJ..CommonDataManager.ConverExpByThousand(data_info.rank_value)
			zhanli_text = CommonDataManager.ConverExpByThousand(data_info.rank_value)
		-- 仙娃榜
		elseif data_info.rank_type == PersonRankType.XianWaGrade then
			-- zhanli_text = Language.Rank.XWZZL..CommonDataManager.ConverExpByThousand(data_info.rank_value)
			zhanli_text = CommonDataManager.ConverExpByThousand(data_info.rank_value)
		-- 仙器榜
		elseif data_info.rank_type == PersonRankType.XianQiGrade then
			-- zhanli_text = Language.Rank.XQZZL..CommonDataManager.ConverExpByThousand(data_info.rank_value)
			zhanli_text = CommonDataManager.ConverExpByThousand(data_info.rank_value)
		-- 竞技榜
		elseif data_info.rank_type == PersonRankType.CrossCompetition then
			-- zhanli_text = string.format(Language.Rank.CompetitionRank, CommonDataManager.ConverExpByThousand(data_info.rank_value))
			zhanli_text = string.format(CommonDataManager.ConverExpByThousand(data_info.rank_value))
		-- 渡劫榜
		elseif data_info.rank_type == PersonRankType.DuJieJingjie then
			local level = data_info.rank_value
			local level_cfg = DujieWGData.Instance:GetLevelCfg(level)
			local type_max = DujieWGData.Instance:GetTypeMax(level_cfg.type, level_cfg.type_seq) 
			local type_str = DujieWGData.Instance:GetTypeStr(level_cfg.type)
			local type_seq_str = NumberToChinaNumber(level_cfg.type_seq == 0 and 1 or level_cfg.type_seq)
			local index = DujieWGData.Instance:GetLevelIndex(level)
			zhanli_text = string.format(Language.Dujie.SkyLevleStr2, type_str, type_seq_str, index, type_max)
			--zhanli_text = string.format(CommonDataManager.ConverExpByThousand(data_info.rank_value))
		-- 幻兽榜
		elseif data_info.rank_type == PersonRankType.BeastCapability then
			zhanli_text = string.format(CommonDataManager.ConverExpByThousand(data_info.rank_value))
		end
	elseif data_info.kind == RankKind.Cross then
		zhiye_text = self:GetProfNameByProf(data_info.prof)

		--战力排名
		if data_info.rank_type == CrossRankType.CROSS_ZHANLI then
			-- zhanli_text = Language.Rank.ZL.. CommonDataManager.ConverExpByThousand(data_info.rank_value)
			zhanli_text = CommonDataManager.ConverExpByThousand(data_info.rank_value)
		-- 装备评分榜
		elseif data_info.rank_type == CrossRankType.CROSS_Equip then
			-- zhanli_text = Language.Rank.PF.. CommonDataManager.ConverExpByThousand(data_info.rank_value)
			zhanli_text = CommonDataManager.ConverExpByThousand(data_info.rank_value)
		-- 境界榜
		elseif data_info.rank_type == CrossRankType.CROSS_JinJie then
			local info = JingJieWGData.Instance:GetJingJieCfgBylevel(data_info.rank_value)
			local name = ""
			if info then
				name = info.name
			end
			-- zhanli_text =  Language.Rank.JZ..name
		--修真排名
		elseif data_info.rank_type == CrossRankType.CROSS_TianXianGeRank then
			-- zhanli_text = Language.Rank.XZCS..FuBenPanelWGData.Instance:GetRankStr(data_info.rank_value)
			zhanli_text = FuBenPanelWGData.Instance:GetRankStr(data_info.rank_value)
		-- 等级排名
		elseif data_info.rank_type == CrossRankType.CROSS_Level then
			zhanli_text = RoleWGData.Instance:TransToDianFengLevelStr(data_info.rank_value) .. Language.Rank.Ji
		--灵宠
		elseif data_info.rank_type == CrossRankType.CROSS_LingChongGrade then
			local upstar_cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(1, data_info.rank_value)
			local image_grade_num = math.max(1, upstar_cfg.grade_num)
			local star_num = data_info.rank_value - 10 * (image_grade_num - 1 )
			-- zhanli_text = Language.Rank.LCPJ..image_grade_num..Language.Common.Jie..star_num..Language.Common.Star
			zhanli_text = image_grade_num..Language.Common.Jie..star_num..Language.Common.Star
		-- 坐骑排名
		elseif data_info.rank_type == CrossRankType.CROSS_MountGrade then
			local upstar_cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(0, data_info.flexible_int)
			local image_grade_num = math.max(1, upstar_cfg.grade_num)
			local star_num = data_info.flexible_int - 10*(image_grade_num - 1 )
			-- zhanli_text = Language.Rank.ZJPJ..image_grade_num..Language.Common.Jie..star_num..Language.Common.Star
			zhanli_text = image_grade_num..Language.Common.Jie..star_num..Language.Common.Star
		-- 羽翼排名
		elseif data_info.rank_type == CrossRankType.CROSS_WingGrade then
			-- zhanli_text = Language.Rank.YYDJ..data_info.rank_value..Language.Common.Ji
			zhanli_text = data_info.rank_value..Language.Common.Ji
		-- 法宝排名
		elseif data_info.rank_type == CrossRankType.CROSS_FabaoGrade then
			-- zhanli_text = Language.Rank.FBDJ..data_info.rank_value..Language.Common.Ji
			zhanli_text = data_info.rank_value..Language.Common.Ji
		-- 神兵排名
		elseif data_info.rank_type == CrossRankType.CROSS_ShenWuGrade then
			-- zhanli_text = Language.Rank.SBDJ..data_info.rank_value..Language.Common.Ji
			zhanli_text = data_info.rank_value..Language.Common.Ji
		-- 剑阵排名
		elseif data_info.rank_type == CrossRankType.CROSS_JianZhenGrade then
			-- zhanli_text = Language.Rank.JLDJ..data_info.rank_value..Language.Common.Ji
			zhanli_text = data_info.rank_value..Language.Common.Ji
		-- 天神排名
		elseif data_info.rank_type == CrossRankType.CROSS_TianShenGrade then
			-- zhanli_text = Language.Rank.TSDJ..CommonDataManager.ConverExpByThousand(data_info.rank_value)
			zhanli_text = CommonDataManager.ConverExpByThousand(data_info.rank_value)
		-- 仙娃榜
		elseif data_info.rank_type == CrossRankType.CROSS_XianWaGrade then
			-- zhanli_text = Language.Rank.XWZZL..CommonDataManager.ConverExpByThousand(data_info.rank_value)
			zhanli_text = CommonDataManager.ConverExpByThousand(data_info.rank_value)
		-- 仙器榜
		elseif data_info.rank_type == CrossRankType.CROSS_XianQiGrade then
			-- zhanli_text = Language.Rank.XQZZL..CommonDataManager.ConverExpByThousand(data_info.rank_value)
			zhanli_text = CommonDataManager.ConverExpByThousand(data_info.rank_value)
		end
	end
end

	return zhiye_text, zhanli_text
end

function RankWGData:GetRankListAndMyStrengthInfomation(kind, rank_type, is_all)
	local data_list = self:GetRankPageData(kind, rank_type, is_all)
	local cur_rankdata = self:GetSelfValueData(kind, rank_type)
	local MyProf, MyStrengthInfomation = "", ""
	local my_rank = cur_rankdata.self_rank
	if my_rank ~= -1 and cur_rankdata.rank_list and cur_rankdata.rank_list[my_rank] then
		MyProf, MyStrengthInfomation = self:GetRankItemStrengthInfomation(cur_rankdata.rank_list[my_rank])
	end
	return data_list, MyStrengthInfomation
end