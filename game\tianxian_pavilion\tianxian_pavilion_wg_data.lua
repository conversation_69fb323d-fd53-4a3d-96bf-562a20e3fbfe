TianxianPavilionWGData = TianxianPavilionWGData or BaseClass()

function TianxianPavilionWGData:__init()
    if TianxianPavilionWGData.Instance then
        error("[TianxianPavilionWGData] Attempt to create singleton twice!")
        return
    end

    TianxianPavilionWGData.Instance = self

    self:InitParam()
    self:InitConfig()
end

function TianxianPavilionWGData:__delete()
    TianxianPavilionWGData.Instance = nil
end

function TianxianPavilionWGData:InitParam()
    self.round = 0
	self.convert = {}
    self.consume_item_info = {}
    self.props_num_list = {}
end

function TianxianPavilionWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("tianxian_baoge_cfg_auto")
    self.convert_cfg = ListToMapList(cfg.convert, "round", "show")
    self.interface_cfg = ListToMap(cfg.interface, "round", "show_index")
    self.consume_cfg = ListToMap(cfg.convert, "round", "seq")
    self.task_cfg = ListToMapList(cfg.activityjump, "round")
    self.heraid_cfg = ListToMap(cfg.heraid, "round")
end

-- 协议数据
function TianxianPavilionWGData:SetAllInfo(protocol)
    self.round = protocol.round														-- 轮次
	self.end_time = protocol.end_time								                -- 结束时间
	self.convert = protocol.convert							                    	-- 根据索引获取每个奖励已经兑换了的次数
end

-- 配置相关
function TianxianPavilionWGData:GetConvertCfg()
    return self.convert_cfg[self.round] or {}
end

function TianxianPavilionWGData:GetRoundInterfaceCfg()
    return self.interface_cfg[self.round] or {}
end

function TianxianPavilionWGData:GetInterfaceCfg(index)
    local round_cfg = self:GetRoundInterfaceCfg()
    if not IsEmptyTable(round_cfg) then
        return round_cfg[index] or {}
    end
end


function TianxianPavilionWGData:GetConvertShowCfg(index)
    return (self.convert_cfg[self.round] or {})[index] or {}
end

function TianxianPavilionWGData:GetTaskCfg()
    return self.task_cfg[self.round] or {}
end

function TianxianPavilionWGData:GetHeraidCfg()
    return self.heraid_cfg[self.round] or {}
end



function TianxianPavilionWGData:GetEndTime()
    return self.end_time or 0
end

function TianxianPavilionWGData:GetExchangeNum(seq)
    return self.convert[seq] or 1
end



function TianxianPavilionWGData:GetPropsNum(round, seq)
    if not self.props_num_list[round] then
        self.props_num_list[round] = {}
    end

    if self.props_num_list[round][seq] then
        return self.props_num_list[round][seq]
    end

    local list = {}
    if self.consume_cfg[round] and self.consume_cfg[round][seq] then
        local consume_cfg = self.consume_cfg[round][seq].consume
        if consume_cfg ~= 0 and consume_cfg ~= "" then
            local exchange_props = string.split(consume_cfg, "|")
            for index, value in ipairs(exchange_props) do
                local exchange_num = string.split(value, ",")
                local item_id = tonumber(exchange_num[1])
                local need_num = tonumber(exchange_num[2])
                table.insert(list, {item_id = item_id, need_num = need_num})
            end
        end
    end

    self.props_num_list[round][seq] = list
    return list
end



function TianxianPavilionWGData:GetCanExchange(round, seq)
    local consume_cfg = self:GetPropsNum(round, seq)
    if IsEmptyTable(consume_cfg) then
        return false
    end

    for k, v in pairs(consume_cfg) do
        local bag_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
        if bag_num < v.need_num then
            return false
        end
    end

    return true
end

function TianxianPavilionWGData:IsTianxianPavilionItem(item_id)
    local cfg = self:GetRoundInterfaceCfg()
    if IsEmptyTable(cfg) then
        return false
    end

    for k, v in pairs(cfg) do
        for i = 1, 2 do
            local prop_item = v["prop_" .. i]
            if prop_item ~= "" and prop_item == item_id then
                return true
            end
        end
    end

    return false
end