﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class ClickableObjectWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(ClickableObject), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>unction("SetClickListener", SetClickListener);
		<PERSON><PERSON>unction("SetClickable", SetClickable);
		<PERSON><PERSON>RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetClickListener(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ClickableObject obj = (ClickableObject)ToLua.CheckObject(L, 1, typeof(ClickableObject));
			System.Action arg0 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 2);
			obj.SetClickListener(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetClickable(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ClickableObject obj = (ClickableObject)ToLua.CheckObject(L, 1, typeof(ClickableObject));
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetClickable(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

