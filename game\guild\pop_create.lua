GuildPopCreate = GuildPopCreate or BaseClass(SafeBaseView)

function GuildPopCreate:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_guild_create")
end

function GuildPopCreate:__delete()
end

function GuildPopCreate:ReleaseCallBack()
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.itemdata_change_callback)
	self.itemdata_change_callback = nil
	if self.pop_alert then
		self.pop_alert:DeleteMe()
		self.pop_alert = nil
	end
	if self.radiobutton then
		self.radiobutton:DeleteMe()
		self.radiobutton = nil
	end
	if self.creat_window_input then
		self.creat_window_input=nil
	end
	if self.select_toggle then
		self.select_toggle = nil
	end
end

function GuildPopCreate:LoadCallBack()
	if nil == self.itemdata_change_callback then
		self.itemdata_change_callback = BindTool.Bind1(self.FlushPanel, self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.itemdata_change_callback)
	end

	self:RegisterAllEvent()
	self.create_model = {
		coin = 1,
		jianmengling = 2,
	}
	local guild_cfg = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").other_config[1]
	
	self.max_level = {
		--max_1 = 90,
		max_1 = guild_cfg.creat_guild_level,
		max_2 = guild_cfg.creat_guild_level,
	}
	self.current_create_model = 2--1                 -- 默认toggle选择第一个

	-- self.node_list["layout_commmon_second_root"].rect.anchoredPosition = Vector2(-6.26,-9.43)
	-- self.node_list["layout_commmon_second_root"].rect.sizeDelta = Vector2(603.77,423.2)
	self:SetSecondView(Vector2(484,370))
	self.node_list.title_view_name.text.text = Language.Guild.GuildCreateName
	self:FlushPanel()

end

function GuildPopCreate:ShowIndexCallBack()
	self:FlushPanel()
end

function GuildPopCreate:FlushPanel()
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local level = tonumber(main_role_vo.level)
	local gold = tonumber(main_role_vo.gold)
	local bind_gold = tonumber(main_role_vo.bind_gold)
	local guild_cfg = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").other_config[1]
	local LEVEL1 = guild_cfg.creat_guild_level
	local BINGGOLD = guild_cfg.create_gold_bind
	local LEVEL2 = guild_cfg.creat_guild_level
	local GOLD = guild_cfg.create_gold  
	local text
	
	-- for i = 1, 3 do
	-- 	self.select_toggle = self.node_list["select"..i].toggle
	-- 	self.node_list["text_des"..i].text.text = Language.Guild["CreateGuildText" .. i]
	-- 	self.select_toggle:AddValueChangedListener(BindTool.Bind2(self.OnSelectCreateModelHandler, self, i))
	-- end
	local create_item_id = guild_cfg.create_item_id
	local index = ItemWGData.Instance:GetItemIndex(create_item_id)

	if main_role_vo.vip_level >= guild_cfg.creat_guild_vip then
		text = ToColorStr(string.format(Language.Guild.CreateGuildVipLimit, guild_cfg.creat_guild_vip), COLOR3B.GREEN)  
	else
		text = ToColorStr(string.format(Language.Guild.CreateGuildVipLimit, guild_cfg.creat_guild_vip), COLOR3B.RED)  
	end

	self.node_list.text_des_vip_limit.text.text = text

	for i = 1, 2 do
		if i == 1 then
			if LEVEL1 > level then
				LEVEL1 = HtmlTool.GetHtml(LEVEL1, COLOR3B.RED)
			else
				LEVEL1 = HtmlTool.GetHtml(LEVEL1, COLOR3B.GREEN)
			end
			if 0 > index then
				BINGGOLD = HtmlTool.GetHtml("1个", COLOR3B.RED)
			else
				BINGGOLD = HtmlTool.GetHtml("1个", COLOR3B.GREEN)
			end
			--self.node_list["text_des"..i].text.text = string.format(Language.Guild["CreateGuildText" .. i],LEVEL1)
			self.node_list["text_des"..i].text.text = string.format(Language.Guild["CreateGuildText" .. i],LEVEL1,BINGGOLD)
		elseif  i == 2 then
			if LEVEL2 > level then
				LEVEL2 = HtmlTool.GetHtml(LEVEL2, COLOR3B.RED)
			else
				LEVEL2 = HtmlTool.GetHtml(LEVEL2, COLOR3B.GREEN)
			end
			if 0 > index then
				GOLD = HtmlTool.GetHtml("1个", COLOR3B.RED)
			else
				GOLD = HtmlTool.GetHtml("1个", COLOR3B.GREEN)
			end
			--self.node_list["text_des"..i].text.text = string.format(Language.Guild["CreateGuildText" .. i],LEVEL2)
			self.node_list["text_des"..i].text.text = string.format(Language.Guild["CreateGuildText" .. i],LEVEL2,GOLD)
		end
		self.select_toggle = self.node_list["select"..i].toggle
		
		self.select_toggle:AddValueChangedListener(BindTool.Bind2(self.OnSelectCreateModelHandler, self, i))
	end

	self.creat_window_input = self.node_list["guild_name"]
end

function GuildPopCreate:SelectCostIndex(index)
	-- if self.radiobutton then
	-- 	self.radiobutton:SelectIndex(index, true)
	-- end
	-- self.current_create_model = index
end

-- 注册事件
function GuildPopCreate:RegisterAllEvent()
	self.node_list.btn_create_guild.button:AddClickListener(BindTool.Bind(self.OnCreateGuildHandler, self))
	self.node_list.btn_cancel.button:AddClickListener(BindTool.Bind1(self.OnCancelHandler, self))
	self.node_list.jianmengling_btn.button:AddClickListener(BindTool.Bind1(self.ShowJianMengLingTips, self))
end

function GuildPopCreate:ShowJianMengLingTips()
	local create_item_id = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").other_config[1].create_item_id
	TipWGCtrl.Instance:OpenItemTipGetWay({item_id = create_item_id})
end

-- 选择创建方式事件
function GuildPopCreate:OnSelectCreateModelHandler(index)
	self.current_create_model = index
end

-- 创建仙盟事件
function GuildPopCreate:OnCreateGuildHandler()
	local name = ""
	if(self.creat_window_input.input_field.text == "") then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.ShuRuXianMengMingZi)
		return
	else
		name = self.creat_window_input.input_field.text
	end

	-- if string.len(name) < 6 then
	-- 	TipWGCtrl.Instance:ShowSystemMsg(Language.Common.limitContent)		
	-- 	return
	-- end

	-- if string.len(name) > 15 then
	-- 	TipWGCtrl.Instance:ShowSystemMsg(Language.Common.limitContent)
	-- 	return
	-- end
	
	local index = -1
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local level = main_role_vo.level
	if self.current_create_model == self.create_model.jianmengling then
		if self.max_level.max_2 > level then
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Guild.LevelBuZu,self.max_level.max_2))
			return
		end
	elseif self.current_create_model == self.create_model.coin then
		if self.max_level.max_1 > level then
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Guild.LevelBuZu,self.max_level.max_1))
			return
		end
	end
	-- if self.current_create_model == self.create_model.jianmengling then
	 	local create_item_id = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").other_config[1].create_item_id
		index = ItemWGData.Instance:GetItemIndex(create_item_id)
	-- end

	if ChatFilter.Instance:IsIllegal(name, true) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.IllegalContent)
		return
	end

	if #name > 21 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.limitContent)
		return
	end

	local name1 = {}
	for i=1,#name do
		table.insert(name1,string.sub(name,i,i))
	end
	-- print_error(#name1,name1)
	-- for k,v in pairs(name1) do
	-- 	if (v ~= nil and tonumber(v)<0) or v == nil then
	-- 		print_error("true")
	-- 	else
	-- 		print_error("false")
	-- 	end

	-- end
	local c = ""
	local b = ""
	local i = 1
	local d = 0
	while true do
        c = string.sub(name,i,i)
        b = string.byte(c)
        if b > 128 then
                i = i + 3
                d = d + 1
        else
                i = i + 1
                d = d + 1
        end

        if i > #name then
                break
        end
  	end
	if d > 6 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.limitContent)
		return
	end
	-- if self.current_create_model == 1 then --绑元创建
	-- 	local bind_gold = tonumber(main_role_vo.bind_gold)
	-- 	local BINGGOLD = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").other_config[1].create_gold_bind
	-- 	if BINGGOLD > bind_gold then
	-- 		if self.pop_alert == nil then
	-- 			self.pop_alert = Alert.New()
	-- 		end
	-- 		self.pop_alert:SetOkFunc(function ()
	-- 			GuildWGCtrl.Instance:SendCreateGuildReq(name, GUILD_CREATE_TYPE.GUILD_CREATE_TYPE_GUILD_BIND_NO_ENOUGH, index, Language.Guild.EmptyNotice)
	-- 		end)
	-- 		self.pop_alert:SetLableString(Language.Guild.BindGoldNo)
	-- 		self.pop_alert:Open()
	-- 		return
	-- 	end
	-- end
	if index < 0 then
		local data = ShopWGData.Instance:GetShopCfgItemId(create_item_id)
		GlobalEventSystem:Fire(KnapsackEventType.KNAPSACK_LECK_ITEM, create_item_id, 1, data.seq)
		return
	end
	--print_error(self.max_level.max_2 , main_role_vo.level,index)
	GuildWGCtrl.Instance:SendCreateGuildReq(name,GUILD_CREATE_TYPE.GUILD_CREATE_TYPE_ITEM, index, Language.Guild.EmptyNotice)
	-- GuildWGCtrl.Instance:SendCreateGuildReq(name, self.current_create_model, index, Language.Guild.EmptyNotice)
end


-- 取消事件
function GuildPopCreate:OnCancelHandler()
	self:Close()
end