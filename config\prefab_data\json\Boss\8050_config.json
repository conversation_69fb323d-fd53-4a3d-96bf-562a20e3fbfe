{"actorController": {"projectiles": [], "hurts": [], "beHurtEffecct": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "hurtEffectName": "", "beHurtNodeName": "", "beHurtAttach": false}, "actorTriggers": {"effects": [{"triggerEventName": "attack1/begin", "triggerDelay": 1.0, "triggerFreeDelay": 0.0, "effectGoName": "8050_attack", "effectAsset": {"BundleName": "effects/prefab/model/boss/8050/8050_attack_prefab", "AssetName": "8050_attack", "AssetGUID": "203cb29f69f07dd4d8bd0a4d38e4de00", "IsEmpty": false}, "playerAtTarget": true, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": false, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1", "playerAtPos": true, "ignoreParentScale": true}, {"triggerEventName": "attack2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "8050_skill", "effectAsset": {"BundleName": "effects/prefab/model/boss/8050/8050_skill_prefab", "AssetName": "8050_skill", "AssetGUID": "1e78ecbbafe3f234f859a5a365d4f7b0", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2", "playerAtPos": false, "ignoreParentScale": true}, {"triggerEventName": "attack3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "8050_skill2", "effectAsset": {"BundleName": "effects/prefab/model/boss/8050/8050_skill2_prefab", "AssetName": "8050_skill2", "AssetGUID": "1633883eba759634ca4517168eacc303", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack3", "playerAtPos": false, "ignoreParentScale": true}], "sounds": [], "cameraShakes": [], "radialBlurs": []}}