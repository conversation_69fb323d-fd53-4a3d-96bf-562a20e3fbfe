BrowseXianQiItem = BrowseXianQiItem or BaseClass(BaseRender)

local XianQiType = {
	Weapon = 1,
	Clothes = 2,
}

local UI_COLOR_FX = {
	[7] = "UI_effect_anqi_saoguang_cai_GJ",
	[6] = "UI_effect_anqi_saoguang_fen_GJ",
	[5] = "UI_effect_anqi_saoguang_hong_GJ",
	[4] = "UI_effect_anqi_saoguang_cheng"
}

function BrowseXianQiItem:__init()
	self.equip_model = nil
	self.all_star_list = {}
	self.cache_equip_vo = nil
end

function BrowseXianQiItem:__delete()
	self.equip_model:DeleteMe()
	self.equip_model = nil

	if self.model_tween then
		self.model_tween:Kill()
		self.model_tween = nil
	end
end

function BrowseXianQiItem:DoLoad(parent)
	self:LoadAsset("uis/view/browse_ui_prefab", "hw_equip_cell", parent.transform)
end

function BrowseXianQiItem:LoadCallBack()
	self.equip_model = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["model_root"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.M,
		can_drag = false,
	}

	self.equip_model:SetRenderTexUI3DModel(display_data)
	-- self.equip_model:SetUI3DModel(self.node_list.model_root.transform, nil, 0, false, MODEL_CAMERA_TYPE.BASE)

	XUI.AddClickEventListener(self.node_list.click_img, BindTool.Bind(self.OnClickItem, self))
	self:InitStarList()
end

function BrowseXianQiItem:InitStarList()
	local all_star_list = {}
	for i = 1, 6 do
		all_star_list[i] = self.node_list["jin_star_" .. i]
		all_star_list[6 + i] = self.node_list["red_star_" .. i]
	end
	self.all_star_list = all_star_list
end

function BrowseXianQiItem:OnClickItem()
	local data = self:GetData()
	if IsEmptyTable(data) or data.item_id <= 0 then
		return
	end

	if not self.cache_equip_vo then
		self.cache_equip_vo = HiddenWeaponWGData.Instance:GetCommonEquipVo(data)
	end

	TipWGCtrl.Instance:OpenItem(self.cache_equip_vo, ItemTip.FROM_NORMAL)
end

function BrowseXianQiItem:OnFlush()
	self.cache_equip_vo = nil
	self:FlushItemBgIcon()
	self:FlushItemBgColor()
	self:FlushItemAwakenBg()
	self:FlushItemStarNum()
	self:FlushItemEffect()
	self:FlushEquipModel()
end

function BrowseXianQiItem:FlushItemBgIcon()
	local index = self:GetIndex()
	local res_name = index == XianQiType.Weapon and "anqi_jianyin" or "linjia_jianyin"
	local asset, bundle = ResPath.GetF2CommonIcon(res_name)
	self.node_list.eq_icon.image:LoadSprite(asset, bundle, function()
		self.node_list.eq_icon.image:SetNativeSize()
	end)
end

function BrowseXianQiItem:FlushItemBgColor()
	local data = self:GetData()
	local color = data and data.color or 0

	if color > 0 then
		local asset, bundle = ResPath.GetShenJiGridColor(color)
		self.node_list.color_bg.image:LoadSprite(asset, bundle, function()
			self.node_list.color_bg.image:SetNativeSize()
		end)
		self.node_list.color_bg:SetActive(true)
	else
		self.node_list.color_bg:SetActive(false)
	end
end

function BrowseXianQiItem:FlushItemAwakenBg()
	local data = self:GetData()
	local level = data and data.special_effect_level or 0

	if level > 0 then
		local asset, bundle = ResPath.GetShenJiAwakenBg(level)
		self.node_list.awaken_bg.image:LoadSprite(asset, bundle, function()
			self.node_list.awaken_bg.image:SetNativeSize()
		end)
		self.node_list.awaken_bg:SetActive(true)
	else
		self.node_list.awaken_bg:SetActive(false)
	end
end

function BrowseXianQiItem:FlushItemStarNum()
	local data = self:GetData()
	local star = data and data.star or 0

	if star > 0 then
		local star_list = self.all_star_list
		for i = 1, #star_list do
			star_list[i]:SetActive(i <= star)
		end
		self.node_list.star_area:SetActive(true)
	else
		self.node_list.star_area:SetActive(false)
	end
end

function BrowseXianQiItem:FlushItemEffect()
	local data = self:GetData()
	local color = data and data.color or 0

	if color > 0 then
		local asset, bundle = ResPath.GetEffectUi(UI_COLOR_FX[color])
		self.node_list.effect_color_node:ChangeAsset(asset, bundle)
		self.node_list.effect_color_node:SetActive(true)
	else
		self.node_list.effect_color_node:SetActive(false)
	end
end

function BrowseXianQiItem:FlushEquipModel()
	if not self.equip_model then
		return
	end

	local data = self:GetData()
	local res_name = data and HiddenWeaponWGData.Instance:GetEquipModelAsset(data.item_id, data.color)

	if not res_name then
		self.node_list.model_root:SetActive(false)
		self:StopModelTween()
	else
		self.equip_model:ClearModel()

		local bundle, asset = ResPath.GetShenJiModel(res_name)
		self.equip_model:SetMainAsset(bundle, asset)

		self.node_list.model_root:SetActive(true)
		self:PlayModelTween()
	end
end

function BrowseXianQiItem:PlayModelTween()
	if self.model_tween then
		self.model_tween:Restart()
		return
	end

	local tween_root = self.node_list.model_root.transform
	tween_root.localRotation = Quaternion.Euler(0, 15, 0)

	local tween_rotation = tween_root:DOLocalRotate(Vector3(0, -15, 0), 4)
	tween_rotation:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	self.model_tween = tween_rotation
end

function BrowseXianQiItem:StopModelTween()
	if self.model_tween then
		self.model_tween:Pause()
	end
end
