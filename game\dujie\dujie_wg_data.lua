DujieWGData = DujieWGData or BaseClass()

function DujieWGData:__init()
	if DujieWGData.Instance ~= nil then
		<PERSON>rro<PERSON><PERSON><PERSON>("[DujieWGData] attempt to create singleton twice!")
		return
	end

	DujieWGData.Instance = self

	self.is_in_dujie = false
	self.new_time = 0

	self:InitCfg()
	self:InitBodyCfg()
	self:InitDujieArea()
	self:InitDujiePos()
    self.draw_gift_count_list = {}

	RemindManager.Instance:Register(RemindName.Dujieing, BindTool.Bind(self.GetDujieRemind, self))

end

function DujieWGData:__delete()
	self:ClearInviteTimer()
	self:DeleteBody()
	RemindManager.Instance:UnRegister(RemindName.Dujieing)
	DujieWGData.Instance = nil
end

function DujieWGData:InitCfg()
    self.ordeal_cfg = ConfigManager.Instance:GetAutoConfig("ordeal_cfg_auto")
    self.level_cfg = ListToMap(self.ordeal_cfg.level, "level")
    self.level_type_cfg = ListToMapList(self.ordeal_cfg.level, "type","type_seq")
    self.ordeal_sort_cfg = ListToMap(self.ordeal_cfg.ordeal, "level", "seq")
    self.thunder_cfg = ListToMap(self.ordeal_cfg.thunder, "seq")
    self.skill_cfg = ListToMap(self.ordeal_cfg.skill, "level", "seq")
    self.vitality_cfg = ListToMap(self.ordeal_cfg.vitality, "zhuanzhi_level")
    self.strength_cfg = ListToMap(self.ordeal_cfg.strength, "jiengjie_level")
    self.type_cfg = ListToMap(self.ordeal_cfg.type, "type")
	self.thunder_name_cfg = ListToMap(self.ordeal_cfg.thunder_name,"type")
	self.write_name_cfg = ListToMap(self.ordeal_cfg.write_name,"type","seq")
	self.thanks_msg_cfg = ListToMap(self.ordeal_cfg.thank_text, "id")
	
	self.open_func_list = {}
	for i, v in ipairs(self.level_cfg) do
		if v.open_func_desc ~= "" then
			table.insert(self.open_func_list,v)
		end
	end
end

function DujieWGData:InitDujieArea()
	self.dujie_scene_id = self:GetOtherCfg("ordeal_scene_id")
	local pos = self:GetOtherCfg("ordeal_pos")
	pos = Split(pos,",")
	self.dujie_pos_x = tonumber(pos[1])
	self.dujie_pos_y = tonumber(pos[2])
	self.dujie_range = self:GetOtherCfg("ordeal_range")
end

function DujieWGData:GetDescByTypeAndSeq(type ,seq)
	if self.write_name_cfg[type] and self.write_name_cfg[type][seq] then
		return self.write_name_cfg[type][seq].desc
	end

	return ""
end

function DujieWGData:GetOtherCfg(key)
	if self.ordeal_cfg and key then
		return self.ordeal_cfg.other[1][key]
	end
end

function DujieWGData:GetLevelCfg(level)
	if level > #self.level_cfg then
		level = #self.level_cfg
	end
	return self.level_cfg[level]
end

function DujieWGData:GetDujieAttrList(level,is_need_next)
	local level_cfg = self:GetLevelCfg(level)
	local next_level_cfg = self:GetLevelCfg(level + 1)
	local attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(level_cfg, is_need_next and next_level_cfg or nil, nil, nil, 1, 5)
	return attr_list
end

function DujieWGData:GetDujieCapality(level)
	local level_cfg = self:GetLevelCfg(level)
	if level_cfg then
		local attr_list, capability = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(level_cfg, "attr_id", "attr_value","attr_name","attr_value",1,5)
		return capability
	end
	return 0
end

function DujieWGData:GetNextOpenFuncDesc(level,is_show_cur)
	for k, v in ipairs(self.open_func_list) do
		if v.level == level and is_show_cur then
			return v.open_func_desc
		end

		if v.level > level then
			local level_cfg = self:GetLevelCfg(v.level)
			return string.format(Language.Dujie.OpenFuncStr,self:GetDescByTypeAndSeq(DUJIE_DESC_TYPE.Stage,level_cfg.type),v.open_func_desc) 
		end
	end
	return ""
end

function DujieWGData :GetSkillCfg(level,seq)
	if self.skill_cfg[level] and self.skill_cfg[level][seq] then
		return self.skill_cfg[level][seq]
	end
	return nil
end

function DujieWGData:GetTypeStr(type)
	local name = self:GetDescByTypeAndSeq(DUJIE_DESC_TYPE.Stage,type)
	if name ~= nil and name ~= "" then
		return name
	end
	print_error("找不到渡劫阶段名字，需要策划检查配置，类型:",type)
	return ""
end

function DujieWGData:GetThunderName(type)
	if self.thunder_name_cfg[type] then
		return self.thunder_name_cfg[type].thunder_txt
	end
	return ""
end

-- 获取当前阶段有几重天
function DujieWGData:GetTypeMax(type, type_seq)
	if type == 1 and type_seq == 0 then
		return #self.level_type_cfg[type][type_seq+1]
	end
	return #self.level_type_cfg[type][type_seq]
end

-- 获取当前等级处于几重天
function DujieWGData:GetLevelIndex(level)
	local level_cfg = self:GetLevelCfg(level)
	local count = 0
	if level == 0 then
		return 0
	end
	for i, v in ipairs(self.level_type_cfg[level_cfg.type][level_cfg.type_seq]) do
		count = count + 1
		if v.level == level then
			return count
		end
		
	end
	return 0
end

-- 获取当前等级处于几重天文本
function DujieWGData:GetLevelIndexStr(level)
	local number = self:GetLevelIndex(level)
	return number..Language.Dujie.LevelIndex
end

function DujieWGData:GetVitalityCfg(zhuanzhi_levle)
	return self.vitality_cfg[zhuanzhi_levle]
end

function DujieWGData:GetStrengthCfg(jingjie_levle)
	return self.strength_cfg[jingjie_levle]
end

function DujieWGData:GetNextDujieCfg()
	local base_info = self:GetOrdealBaseInfo()
    -- 等级
    local level = base_info and base_info.level or 0
    local level_cfg = self:GetLevelCfg(level + 1)
	return level_cfg
end

function DujieWGData:GetThunderCfgBySeq(seq)
	return self.thunder_cfg[seq]
end

function DujieWGData:SetOrdealBaseInfo(protocol)
    self.ordeal_base_info = protocol
end

function DujieWGData:GetOrdealBaseInfo()
	return self.ordeal_base_info
end

-- 获取渡劫等级
function DujieWGData:GetDujieLevel()
	return self.ordeal_base_info and self.ordeal_base_info.level or 0
end

function DujieWGData:SetOrdealInfo(protocol)
	self:SetOrderRangeCount(protocol.ordeal_show_role_num, true)
	
	self.thunder_list = {}
	local count = 0;
	-- local server_time = TimeWGCtrl.Instance:GetServerTime()
	local offset_time = protocol.now_time - protocol.ordeal_start_time
	for i = 1, 30 do
		local thunder_item = protocol.thunder_item_list[i]
		local next_time = (thunder_item.thunder_offset_ms/1000) - offset_time
		if thunder_item.thunder_seq~= 0 and thunder_item.thunder_seq ~= -1 and next_time > 0.1 then --0有误差，会导致劈雷了，但是次数没变
			count = count + 1
			local thunder_info = {}
			thunder_info.seq = thunder_item.thunder_seq
			thunder_info.time = next_time
			self.thunder_list[count] = thunder_info
			-- print_error(string.format("i:%s 雷电索引：%s  渡劫倒计时:%s",i, thunder_item.thunder_seq, next_time))
		end
	end
    self.ordeal_info = protocol

	self.old_ordeal_info = {}
	self.old_ordeal_info.ordeal_start_time = protocol.ordeal_start_time
	self.old_ordeal_info.vitality = protocol.vitality
	self.old_ordeal_info.strength = protocol.strength
	self.old_ordeal_info.next_thunder_index = protocol.next_thunder_index
end


function DujieWGData:GetOrdealInfo()
	return self.ordeal_info
end

function DujieWGData:GetSkillCD(index)
	if self.ordeal_info.skill_cd_time_list and self.ordeal_info.skill_cd_time_list[index] then
		return self.ordeal_info.skill_cd_time_list[index]
	end
	return 0
end

function DujieWGData:GetOldOrdealInfo()
	if nil == self.old_ordeal_info then
		self.old_ordeal_info = {}
		self.old_ordeal_info.ordeal_start_time =0
		self.old_ordeal_info.vitality = 0
		self.old_ordeal_info.strength = 0
	end
	return self.old_ordeal_info
end

function DujieWGData:GetThunderList()
	return self.thunder_list or {}
end

function DujieWGData:GetThunderCount()
	if self.thunder_list then
		return #self.thunder_list
	end
	return 0
end

function DujieWGData:GetFirstThunderInfo()
	if self.thunder_list and self.thunder_list[1] then
		return self.thunder_list[1]
	end
	return nil
end

function DujieWGData:GetDujieRemind()
	local is_open = FunOpen.Instance:GetFunIsOpened(FunName.DujieView)
	if not is_open then
		return 0
	end
	if self:IsCanDujie() then
		return 1
	end
	return 0
end

-- 是否满足渡劫条件
function DujieWGData:IsCanDujie()

	local base_info = self:GetOrdealBaseInfo()
    -- 等级
    local level = base_info and base_info.level or 0
    local next_level_cfg = self:GetLevelCfg(level + 1)
    if next_level_cfg then
        -- 转职等级限制
        if next_level_cfg.zhuanzhi_limit == 0 then
			if TransFerWGData.Instance:GetGodAndDemonsType() == -1 then
				MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.DUJIE_TIPS, 0)
				return false, DUJIE_NO_CAN_TYPE.TRANSFER
			end
		else
			local zhuanzhi_level = RoleWGData.Instance:GetZhuanZhiNumber()
			local is_finish,cur_num, max_num = TransFerWGData.Instance:IsAllFinish()
			if (zhuanzhi_level + 1  < next_level_cfg.zhuanzhi_limit) or  (zhuanzhi_level + 1  == next_level_cfg.zhuanzhi_limit and cur_num + 1 < max_num ) then
				MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.DUJIE_TIPS, 0)
				return false, DUJIE_NO_CAN_TYPE.TRANSFER
			end
		end
		-- 修为等级限制
		local cultivation_level = CultivationWGData.Instance:GetXiuWeiState()
		if cultivation_level < next_level_cfg.jingjie_limit then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.DUJIE_TIPS, 0)
			return false, DUJIE_NO_CAN_TYPE.CULTIVATION
		end

		-- 受损状态
        if base_info then
            local cur_time = TimeWGCtrl.Instance:GetServerTime()
            if  base_info.ordeal_damage_time - cur_time > 0 then
                MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.DUJIE_TIPS, 0)
				return false, DUJIE_NO_CAN_TYPE.FAIL
            end
        end
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.DUJIE_TIPS, 1,function()
			DujieWGCtrl.Instance:OpenDujieView()
		end)
		return true
    end
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.DUJIE_TIPS, 0)
	return false, DUJIE_NO_CAN_TYPE.LEVEL_MAX
end

-- 是否在渡劫范围
function DujieWGData:IsInDujieArea()
	local scene_id = Scene.Instance:GetSceneId()
	if scene_id == self.dujie_scene_id then
		local role_x, role_y = Scene.Instance:GetMainRole():GetLogicPos()
		local dis = GameMath.GetDistance(self.dujie_pos_x, self.dujie_pos_y, role_x, role_y, false)
		-- 做个+1的误差
		if dis <= (self.dujie_range) * (self.dujie_range) then
			return true
		end
	end
	return false
end

function DujieWGData:GetDujieAreaInfo()
	return self.dujie_scene_id, self.dujie_pos_x, self.dujie_pos_y

end

-- 是否在渡劫中
function DujieWGData:IsInDujie()
	return self.is_in_dujie
end

function DujieWGData:SetIsInDujie(is_in)
	self.is_in_dujie = is_in
end

-- 初始化渡劫的几个安全点
function DujieWGData:InitDujiePos()
	local random_pos_temp = self:GetOtherCfg("random_pos")
	local random_pos_list_temp = Split(random_pos_temp,"|")
	self.dujie_pos_list = {}
	for i, v in ipairs(random_pos_list_temp) do
		local pos = Split(v,",")
		pos.x = tonumber(pos[1])
		pos.y = tonumber(pos[2])
		table.insert(self.dujie_pos_list, pos)
	end
end

-- 随机获得一个渡劫坐标点
function DujieWGData:GetRandomDujiePos()
	if self.dujie_pos_list then
		local index = math.random(1,#self.dujie_pos_list)
		return self.dujie_pos_list[index]
	end
	return nil
end

function DujieWGData:SetIsNeedOpenDujie(is_need)
	self.is_need_open_dujie = is_need
end

function DujieWGData:GetIsNeedOpenDujie()
	return self.is_need_open_dujie
end


function DujieWGData:SetSceneThunderAudio(time)
	if ViewManager.Instance:IsOpen(GuideModuleName.DujieView) then
		return false
	end
	if time - self.new_time >= 1 then
		self.new_time = time
		return true
	end
	return false
end

function DujieWGData:GetAddedStrength(order_range_count)
	local single_add_strength, vitality_limit = self:GetSinglePlayerAddStrength()
	local add_strength = single_add_strength * order_range_count

	return math.min(add_strength, vitality_limit)
end

function DujieWGData:GetSinglePlayerAddStrength()
	local level = self:GetDujieLevel()
	local level_cfg = self:GetLevelCfg(level + 1)
	if IsEmptyTable(level_cfg) then
		return 0
	end
	return level_cfg.vitality, level_cfg.vitality_limit
end

-- 随机获取一个感谢文本
function DujieWGData:GetThanksMsg()
	local index = math.random(1, #self.thanks_msg_cfg)
	return self.thanks_msg_cfg[index]
end

-------------------------场景特效----------------------
-- 是否显示场景特效
function DujieWGData:IsShowSceneEffect()
	if ViewManager.Instance:IsOpen(GuideModuleName.DujieOperateView) or ViewManager.Instance:IsOpen(GuideModuleName.DujieView) then
		return
	end
	local scene_id = Scene.Instance:GetSceneId()
	return self.dujie_scene_id == scene_id and self.order_count and self.order_count > 0 and not self:IsInDujie()
end

-- 设置渡劫人数
function DujieWGData:SetOrderCount(count)
	self.order_count = count
end

-- 设置渡劫人数
function DujieWGData:GetOrderCount(count)
	return self.order_count
end

-- 设置渡劫范围人数
function DujieWGData:SetOrderRangeCount(count, break_limit)
	-- 渡劫开始后人数不刷新
	if not break_limit then
		if self:IsInDujie() then
			return
		end
	end
	if count < 0 then
		count = 0
	end
	self.order_range_count = count
end

-- 获取渡劫范围人数
function DujieWGData:GetOrderRangeCount(count)
	return self.order_range_count or 0
end

-- 设置渡劫邀请cd
function DujieWGData:SetLastInviteTime(time)
	self.last_invite_time = time
end

-- 获取渡劫邀请cd
function DujieWGData:GetLastInviteTime()
	return self.last_invite_time or 0
end

-- 添加渡劫邀请信息到列表
function DujieWGData:AddDujieInvite(invite_uid, invite_name)

	self:ClearInviteTimer()

	self.invite_data = {}
	self.invite_data.invite_uid = invite_uid
	self.invite_data.invite_name = invite_name

	--30秒后清除
	self.dujie_invite_timer = GlobalTimerQuest:AddDelayTimer(
		function() 
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.DUJIE_INVITE_TIPS, 0)
			self.invite_data  = nil
		end, 30)

	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.DUJIE_INVITE_TIPS, 1,function()
		local function CheckInviteState()
			DujieWGCtrl.Instance:GotoDujieArea()
		end
		local invite_data = self:GetDujieInviteData()
		if not IsEmptyTable(invite_data) then
			DujieWGCtrl.Instance:OpenDujieReceivreView(invite_data)
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.DUJIE_INVITE_TIPS, 0)
			self.invite_data  = nil
		end

	end)
end

-- 获取渡劫信息列表 顶部信息
function DujieWGData:GetDujieInviteData()
	if not self.invite_data then
		return 
	end
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.DUJIE_INVITE_TIPS, 0)
	self:ClearInviteTimer()
	local invite_data = {}
	invite_data.invite_uid = self.invite_data.invite_uid
	invite_data.invite_name = self.invite_data.invite_name
	return invite_data
end

function DujieWGData:ClearInviteTimer()
	if self.dujie_invite_timer then
		GlobalTimerQuest:CancelQuest(self.dujie_invite_timer)
		self.dujie_invite_timer = nil
	end
end