FRIEND_TOGGLE_MAX = 4
FRIEND_MAX_NUM = 75

SOCIETY_LIXIREN = 1
SOCIETY_FRIEND = 2
SOCIETY_ENEMY = 3
SOCIETY_BLACK = 4

local HIDE_VOICE_WIDTH = 490
local SHOW_VOICE_WIDTH = 442
local LianXiRenNum = 30
local ITEM_HEIGHT = 88

function SocietyView:InitFriendView()
	self.layout_friend_base = self.node_list["layout_friend"]
	self.view_cache_time = 0
	self.cur_btn_list_num = -1
	self.click_voice_btn = false
	self.private_role_id = 0
	self.private_role_name = ""
	self.cur_cell_data = {}
	self.cur_button_index = -1
	self.cur_active = false
	--self.node_list["layout_blank_tip2"].gameObject:SetActive(false)

	XUI.AddClickEventListener(self.node_list["btn_addfriend"], BindTool.Bind1(self.OnOpenAddFriendView, self))  --中间的添加
	--XUI.AddClickEventListener(self.node_list["btn_batchremove"], BindTool.Bind1(self.OnOpenAddRemoveView, self))
	XUI.AddClickEventListener(self.node_list["btn_friend_apply"], BindTool.Bind1(self.OpenFriendApplyReqView, self))--好友申请界面
	XUI.AddClickEventListener(self.node_list["SendButton"], BindTool.Bind1(self.OnSendMessage, self))--发送
	XUI.AddClickEventListener(self.node_list["TranslateBtnPos"], BindTool.Bind1(self.GetMainRolePos, self))--位置
	XUI.AddClickEventListener(self.node_list["TranslateBtn"], BindTool.Bind1(self.OperateActive, self))--切换界面
	XUI.AddClickEventListener(self.node_list["Block1"], BindTool.Bind1(self.CloseMore, self))--关闭装备界面
	--XUI.AddClickEventListener(self.node_list["btn_humo_state"], BindTool.Bind1(self.OnclickHuMoState, self))--虎摸说明
	XUI.AddClickEventListener(self.node_list["btn_huomo_rember"], BindTool.Bind1(self.OnclickOpenHuMoRmberView, self))--虎摸记录
	XUI.AddClickEventListener(self.node_list["anti_fraud_cartoon_btn"], BindTool.Bind1(self.OnClickAntiFraudCartoonBtn, self))	-- 防骗漫画
	XUI.AddClickEventListener(self.node_list["btn_enemy_record"], BindTool.Bind1(self.OnClickEnemyRecord, self))	-- 击杀记录
	XUI.AddClickEventListener(self.node_list["btn_new_msg"], BindTool.Bind1(self.OnClickNewMsg, self))
	XUI.AddClickEventListener(self.node_list["IgnoreButton"], BindTool.Bind1(self.OnLYLIgnoreButton, self))
	XUI.AddClickEventListener(self.node_list["LiaoButton"], BindTool.Bind1(self.OnLYLLiaoButton, self))
	XUI.AddClickEventListener(self.node_list["btn_qinmi"], BindTool.Bind1(self.OnClickOpenQinMi, self)) -- 亲密度
	XUI.AddClickEventListener(self.node_list["btn_qipao"], BindTool.Bind1(self.ToQiPao, self)) -- 气泡
	self.node_list.PYP_InputField.input_field.onSelect:AddListener(BindTool.Bind(self.OnInputFieldClickOrEndEdit, self, true)) -- 拍一拍自定义输入框
	self.node_list.PYP_InputField.input_field.onEndEdit:AddListener(BindTool.Bind(self.OnInputFieldClickOrEndEdit, self, false))

   	self.role_data_list = {}
   	self.role_data_list[1] = AsyncListView.New(SocietyFriendRender, self.node_list.left_info_render_1)
   	self.role_data_list[1]:SetSelectCallBack(BindTool.Bind(self.OnClickProductHandler, self))
   	self.role_data_list[2] = AsyncListView.New(SocietyFriendRender, self.node_list.left_info_render_2)
   	self.role_data_list[2]:SetSelectCallBack(BindTool.Bind(self.OnClickProductHandler, self))
   	self.role_data_list[2]:SetEndScrolledCallBack(BindTool.Bind(self.FirendListEndScrolledCallBack, self))
   	self.role_data_list[3] = AsyncListView.New(SocietyEnemyRender, self.node_list.left_info_render_3)
   	self.role_data_list[3]:SetSelectCallBack(BindTool.Bind(self.OnClickProductHandler, self))
   	self.role_data_list[4] = AsyncListView.New(BlackViewlistRender, self.node_list.left_info_render_4)
   	self.role_data_list[4]:SetSelectCallBack(BindTool.Bind(self.OnClickProductHandler, self))
   	self.last_select_role_index = 0

   	-- for i=1, 4 do
   	-- 	self.node_list["left_info_render_"..i]:SetActive(false)
   	-- end

	self:CreateFriendList()
	for i = 1, FRIEND_TOGGLE_MAX do
		self.node_list["SelectBtn" .. i].accordion_element:AddValueChangedListener(BindTool.Bind(self.OnClickExpandHandler, self, true, i, false))
	end

	for i=1,8 do
		self.node_list["small_top_list_"..i].toggle:AddClickListener(BindTool.Bind(self.SelectToggleCallback, self, i))
	end
	self.node_list["small_top_list_6"]:SetActive(not ChatWGData.Instance:GetIsShieldPYP())

	-------第一个-------------------
	local bundle = "uis/view/society_ui_prefab"
	local asset = "NorItemRender"
	if self.grid_normal_face == nil then
		self.grid_normal_face  = AsyncBaseGrid.New()
		self.table_data = ChatWGData.Instance:GetNormalFaceCfg()
		self.grid_normal_face:CreateCells({col = 8, cell_count = #self.table_data,
		assetName = asset,
		assetBundle = bundle,
		itemRender = EmojiIconCell,
		list_view = self.node_list["normal_face_list"]
		})
		self.grid_normal_face:SetSelectCallBack(BindTool.Bind(self.ClickCallBack, self))
		self.grid_normal_face:SetStartZeroIndex(false)
	end
-------------第三个  文字表情---------------------------------
	-- self.grid_font_face = AsyncListView.New(ChatWordFaceRender, self.node_list.font_face_list)
	if nil == self.wordface_cfg then
		self.wordface_cfg = ChatWGData.Instance:GetWordFaceCfg()
	end

	local bundle1 = "uis/view/society_ui_prefab"
	local asset1 = "Font_ItemRender"
	self.grid_font_face  = AsyncBaseGrid.New()
	self.grid_font_face:CreateCells({col = 3, cell_count = #self.wordface_cfg,
		assetName = asset1,
		assetBundle = bundle1,
		itemRender = ChatWordFaceRender,
		list_view = self.node_list["font_face_list"] })
	self.grid_font_face:SetSelectCallBack(BindTool.Bind(self.ClickFontFace, self))
	self.grid_font_face:SetStartZeroIndex(false)

-----------------------第二个  大表情   -----------------------------------------
	if self.grid_word_face == nil then
		self.big_table_data = ChatWGData.Instance:GetGifFaceCfg()
		local bundle2 = "uis/view/society_ui_prefab"
		local asset2 = "Big_ItemRender"
		self.grid_word_face = AsyncBaseGrid.New()
		self.grid_word_face:CreateCells({col = 8, cell_count = #self.big_table_data,
			assetName = asset2,
			assetBundle = bundle2,
		 	itemRender = BigFaceIconCell,
		  	list_view = self.node_list["big_face_list"] })
		self.grid_word_face:SetStartZeroIndex(false)
		self.grid_word_face:SetSelectCallBack(BindTool.Bind(self.ClickCallBack, self))
	end

	self.cur_chat_data = {}
	if nil == self.accordion_list then
		self.accordion_list = {}
		self.accordion_cell_list = {}
		for i=1, 4 do
			self.accordion_list[i] = {}
			self.accordion_list[i].text_name = self.node_list["text_btn"..i]
			self.accordion_list[i].text_name_hl = self.node_list["text_btn_hl"..i]
			self.accordion_list[i].list = self.node_list["List"..i ]
		end
		self:LoadBossListCell(1)
	end
	self.event_listener = self.node_list["VoiceButton"].event_trigger_listener

	self.on_point_down = BindTool.Bind1(self.OnPointDown, self)
	self.on_point_up = BindTool.Bind1(self.OnPointUp, self)
	self.on_drag = BindTool.Bind1(self.OnDrag, self)
	if self.event_listener ~= nil then
		self.event_listener:AddPointerDownListener(self.on_point_down)		--按下
		self.event_listener:AddPointerUpListener(self.on_point_up)
		self.event_listener:AddDragListener(self.on_drag)
	end
	local input_sendName = self.node_list["ChatInput"].input_field
   	input_sendName.onEndEdit:AddListener(BindTool.Bind1(self.SetInputText, self))

	self:ChangeInputFieldWidth()
	-- self.chat_pb_change_event = GlobalEventSystem:Bind(OtherEventType.CHAT_PB_CHANGE, BindTool.Bind1(self.ChangeInputFieldWidth, self))

	self.node_list["chat_input_def"].text.text = Language.Chat.MsgDefInputDesc

	--self.node_list["btn_new_msg"].transform:DOLocalMoveY(self.node_list["btn_new_msg"].transform.localPosition.y + 10, 0.5):SetLoops(-1, DG.Tweening.LoopType.Yoyo)
end

function SocietyView:OnInputFieldClickOrEndEdit(is_need_clear)
	if is_need_clear then
		self.node_list.Placeholder.text.text = ""
	else
		self.node_list.Placeholder.text.text = Language.Chat.PlaceholderText
	end
end

function SocietyView:OnLYLIgnoreButton()
	if self.private_role_id and self.private_role_id > 0 then
		local role_info = SocietyWGData.Instance:FindAddReq(self.private_role_id)
		--print_error("self.private_role_id, role_info =", self.private_role_id, role_info)
		if role_info then
			SocietyWGCtrl.Instance:SendAddFriendRet(role_info, 0)
			SocietyWGData.Instance:RemoveLYLInfo(self.private_role_id)
			self:OnClickExpandHandler(false, 1, false)
			self:SetLiaoYiLiaoPanelState(false)
		end
	end
end

function SocietyView:OnClickChange(index, is_on)
	if is_on and self.cur_button_index ~= index then
		self.node_list["SelectBtn" .. index].accordion_element.isOn = true
		self:OnClickExpandHandler(true, index, false)
		self.cur_button_index = index
	end
end

function SocietyView:OnLYLLiaoButton()
	if self.private_role_id and self.private_role_id > 0 then
		local lyl_role_info = SocietyWGData.Instance:GetLYLRoleInfo(self.private_role_id)
		if lyl_role_info and lyl_role_info.is_online == 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.OutOnLine)
			return
		end
		local role_info = SocietyWGData.Instance:FindAddReq(self.private_role_id)
		if role_info then
			SocietyWGCtrl.Instance:SendAddFriendRet(role_info, 1, true)
			-- SocietyWGData.Instance:RemoveLYLInfo(self.private_role_id)
			local private_role_id = self.private_role_id
			local private_role_name = self.private_role_name

			self:OnClickExpandHandler(false, 1, true)

			self:SetPrivateRoleId(private_role_id)
			self.private_role_name = private_role_name

			-- self:OnClickExpandHandler(false, 2, true)
		end
	end
end

function SocietyView:OnClickOpenQinMi()
	MarryWGCtrl.Instance:SetIntimacyData(self.cur_cell_data.intimacy, self.cur_cell_data.gamename)
end

-- 语音入口屏蔽
function SocietyView:ChangeInputFieldWidth()
	local is_shield_voice = GLOBAL_CONFIG.param_list.shield_chat_voice == 1 -- ChatWGData.IS_PB_AUDIO_CHAT
	local width = is_shield_voice and HIDE_VOICE_WIDTH or SHOW_VOICE_WIDTH
	self.node_list.ChatInput.rect.sizeDelta = Vector2(width, self.node_list.ChatInput.rect.sizeDelta.y)
	self.node_list["VoiceButton"]:SetActive(not is_shield_voice)
end


function SocietyView:SetInputText()
	-- 正常组件都没了事件也不会触发，但是报错了 做个容错
	if self.node_list["ChatInput"] == nil then
		return
	end
	local str = self.node_list["ChatInput"].input_field.text
	if nil == str or "" == str then
		return
	end
	SocietyWGData.Instance:SetInputShow(str)
end

function SocietyView:InitOneBtn()
	self:OnClickChange(1,true)
	-- self.node_list["firend_btn_1"].toggle.isOn = true
	self.node_list["SelectBtn1"].accordion_element.isOn = true
end

function SocietyView:ClickCallBack(cell)
	local data = cell:GetData()
	if nil == data then
		return
	end

	if data.vip_limit and data.vip_limit > VipWGData.Instance:GetVipLevel() then
		--VIP等级不足
		-- SysMsgWGCtrl.Instance:ErrorRemind(Language.Vip.VipTips_4)
		ChatWGCtrl.Instance:OpenChatVipLimitTipView()
		return
	end

	local id = data.id
	if nil == self.node_list["ChatInput"].input_field.text then
		self.node_list["ChatInput"].input_field.text = ""
	end

	if ChatWGData.ExamineEditText(self.node_list["ChatInput"].input_field.text, 2) then
		self.node_list["ChatInput"].input_field.text = self.node_list["ChatInput"].input_field.text .."/".. id
	end

end

function SocietyView:ClickFontFace(cell, cell_index, is_default)

	if nil == cell then
		return
	end

	local data = cell:GetData()
	if nil == data then
		return
	end

	self.wordface_cfg = ChatWGData.Instance:GetWordFaceCfg()
	self:SendText(data.content)
	self:CloseMore()
end

function SocietyView:CloseMore()
	self.cur_active = not self.cur_active
	self:FlushHideOrActive(false)
end

function SocietyView:OpenFriendApplyReqView()
	MainuiWGCtrl.Instance:RemoveTipIconByIconObj(MAINUI_TIP_TYPE.FRIEND)
	SocietyWGCtrl.Instance:OpenReqView()
	-- body
end

--是否显示背包定位等
function SocietyView:OperateActive(index)
	--local is_acive = self.node_list["more_container"]:GetActive()
	self.cur_active = not self.cur_active
	self:FlushHideOrActive(self.cur_active)

	if self.cur_active then
		local jump_to_index = index or 1
		if not self.node_list["small_top_list_" .. jump_to_index] then
			jump_to_index = 1
		end
		self:SelectToggleCallback(jump_to_index)
		self.node_list["small_top_list_" .. jump_to_index].toggle.isOn = true
	end
end

--显示背包
function SocietyView:ToBag(type)
	ChatWGData.Instance:SetIsChatBag(true)
	self:ChangeToPanel(self.node_list["bag_root"])
	self:SelectBagToggleCallback(type)
end

function SocietyView:SelectBagToggleCallback(type)
	if type == SOCOETY_BTN_TYPE.BAG then--背包
		self:ToNorBag()
	elseif type == SOCOETY_BTN_TYPE.BAG2 then--材料背包
		self:ToStuffBag()
	end
	self.node_list["bag_list"]:SetActive(type == SOCOETY_BTN_TYPE.BAG)
	self.node_list["stuff_bag_list"]:SetActive(type == SOCOETY_BTN_TYPE.BAG2)
end

function SocietyView:ToNorBag()
	local show_data_list = ChatWGData.Instance:GetShowItemDataList()
	if #show_data_list <= 0 then
		return
	end
	if not self.bag then
		self.bag = AsyncBaseGrid.New()
		-- local count = math.ceil( #show_data_list / 5 ) * 5
		self.bag:CreateCells({col = 8, itemRender = FriendChatBagItemRender,
								assetBundle = "uis/view/society_ui_prefab", assetName = "item_image",
								cell_count = #show_data_list, list_view = self.node_list["bag_list"]})
		self.bag:SetSelectCallBack(BindTool.Bind(self.HandleBagOnClick, self))
		self.bag:SetStartZeroIndex(false)
	end
	self.bag:SetDataList(show_data_list, 0)
end

function SocietyView:ToStuffBag()
	local show_data_list = ChatWGData.Instance:GetShowStuffItemDataList()
	if #show_data_list <= 0 then
		return
	end
	if not self.stuff_bag then
		self.stuff_bag = AsyncBaseGrid.New()
		-- local count = math.ceil( #show_data_list / 5 ) * 5
		self.stuff_bag:CreateCells({col = 8, itemRender = FriendChatBagItemRender,
										assetBundle = "uis/view/society_ui_prefab", assetName = "item_image",
										cell_count = #show_data_list, list_view = self.node_list["stuff_bag_list"]})
		self.stuff_bag:SetSelectCallBack(BindTool.Bind(self.HandleBagOnClick, self))
		self.stuff_bag:SetStartZeroIndex(false)
	end
	self.stuff_bag:SetDataList(show_data_list, 0)
end

--发送位置
function SocietyView:ToPos()
	self:GetMainRolePos()
	self:FlushHideOrActive(false)
end

--显示表情
function SocietyView:ToFace(type)
	--普通表情_默认
	--特殊表情
	--文字表情
	self:ChangeToPanel(self.node_list["face_list"])

	self:SelectFaceToggleCallback(type)
end

--发送骰子
function SocietyView:ToTouZi()
	self:SendDice()
	self.cur_active = not self.cur_active
	self:FlushHideOrActive(false)
end

function SocietyView:ToQiPao()
	ViewManager.Instance:Open(GuideModuleName.NewAppearanceWGView, TabIndex.new_appearance_zhuangban_bubble)
	self:FlushHideOrActive(false)
end

function SocietyView:SelectToggleCallback(i)
	if i == SOCOETY_BTN_TYPE.BAG then
		self:ToBag(SOCOETY_BTN_TYPE.BAG)
	elseif i == SOCOETY_BTN_TYPE.BAG2 then
		self:ToBag(SOCOETY_BTN_TYPE.BAG2)
	elseif i == SOCOETY_BTN_TYPE.QIPAO then
	 	self:ToQiPao()
	elseif i == SOCOETY_BTN_TYPE.NORNAL_FACE then
		self:ToFace(SOCOETY_BTN_TYPE.NORNAL_FACE)
	elseif i == SOCOETY_BTN_TYPE.TOUZI then
		self:ToTouZi()
	-- elseif i == CHAT_MORE.HONGBAO then
	-- 	self:ToHongBao()
	-- elseif i == CHAT_MORE.ZHAOJI then
	-- 	self:ToZhaoJi()
	elseif i == SOCOETY_BTN_TYPE.POS then
		self:SetPos()
	elseif i == SOCOETY_BTN_TYPE.ZHAOHU then
		self:ToZhaoHu()
    	self:PYPShowIndexCallBack()
    elseif i == SOCOETY_BTN_TYPE.SPEAK then
		 self:ToFontFace()
	elseif i == SOCOETY_BTN_TYPE.BIG_FACE then
		self:ToFace(SOCOETY_BTN_TYPE.BIG_FACE)
	end
	self.more_index = i
end

function SocietyView:ToZhaoHu()
	self:ChangeToPanel(self.node_list["zhaohu_root"])
	self:PYPLoadCallBack()
end

function SocietyView:SetPos()
	if Scene.Instance:GetSceneType() == SceneType.GHOST_FB_GLOBAL then
        TipWGCtrl.Instance:ShowSystemMsg(Language.ZhuoGuiFuBen.LimitPosSend)
		return
	end

	local main_role = Scene.Instance.main_role
	if nil ~= main_role then
		local x, y = main_role:GetLogicPos()
		local scene_key = 1 or 0
		local open_line = 1 or 0

		-- 如果此场景不能分线
		if open_line <= 0 then
			scene_key = -1
		end

		local main_role_vo = main_role.vo
		local pos_msg = string.format(Language.Chat.PosFormat, Scene.Instance:GetSceneName(), x, y,main_role_vo.cur_plat_name,main_role_vo.current_server_id)
		-- local pos_msg = string.format(Language.Society.Pos_Show, Scene.Instance:GetSceneName(), x, y)
		local edit_text = self.node_list["ChatInput"].input_field.text

		if ChatWGData.ExamineEditText(edit_text, 1) then
			self.node_list["ChatInput"].input_field.text = edit_text .. pos_msg
			-- self.chat_input.input_field.text = edit_text .. pos_msg
			ChatWGData.Instance:InsertPointTab()
			self:OnSendMessage()
		end

	end
	self:FlushHideOrActive(false)
end

function SocietyView:SelectFaceToggleCallback(i)
	-- self:SelectToggleCallback(1)
	if i == SOCOETY_BTN_TYPE.NORNAL_FACE then
		self:ToNormalFace()
	elseif i == SOCOETY_BTN_TYPE.BIG_FACE then
		self:ToBigFace()
	-- elseif i ==3 then
	-- 	self:ToFontFace()
	end
end

function SocietyView:ToNormalFace()
	self.node_list["normal_face_list"]:SetActive(true)
	self.node_list["big_face_list"]:SetActive(false)

	self.grid_normal_face:SetDataList(self.table_data)
end

function SocietyView:ToBigFace()
	self.node_list["normal_face_list"]:SetActive(false)
	self.node_list["big_face_list"]:SetActive(true)
	self.grid_word_face:SetDataList(self.big_table_data, 0)
end

function SocietyView:ToFontFace()
	self:ChangeToPanel(self.node_list["speak_root"])
	self.grid_font_face:SetDataList(self.wordface_cfg, 0)
end

--摇色子
function SocietyView:SendDice()
	--判断好友是否离线
	local info  = SocietyWGData.Instance:FindFriend(self.private_role_id)
	if nil == info or info.is_online == 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.FriendIsNotOnline)
		return
	end

	local rand_num = math.random(1, 100)
	local text = string.format(Language.Chat.DiceWord2, rand_num)
	ChatWGCtrl.Instance:SendPrivateChatMsg(self.private_role_id, text, CHAT_CONTENT_TYPE.TEXT, nil, false)
	self:SetAchievementData()
end
function SocietyView:HandleBagOnClick(cell)
	if nil == cell then
		return
	end

	local item_data = cell:GetData()
	if nil == item_data then
		return
	end

	local cfg = ItemWGData.Instance:GetItemConfig(item_data.item_id)
	local edit_text = self.node_list["ChatInput"].input_field.text or ""
	-- --转换物品名字到输入框
	if ChatWGData.ExamineEditText(edit_text, 2) then
		edit_text = "[" .. cfg.name .. "]"
		ChatWGData.Instance:InsertItemTab(item_data, true)
		self:AddEditText(edit_text)
	end
end

function SocietyView:AddEditText(text)
	self.node_list["ChatInput"].input_field.text = self.node_list["ChatInput"].input_field.text .. text
end

function SocietyView:ChangeToPanel(node_panel)
	if self.cur_show_panel ~= nil then
		self.cur_show_panel:SetActive(false)
	end

	self.cur_show_panel = node_panel
	self.cur_show_panel:SetActive(true)
end

function SocietyView:FlushHideOrActive(is_active)
	--self.node_list["more_container"]:SetActive(is_active)
	local y_value = 0
	if is_active then
		y_value = 0
		self.node_list.Block1:SetActive(true)
	else
		self.node_list.Block1:SetActive(false)
		y_value = -500
	end
	local Hide_animator = self.node_list["more_container"].rect:DOAnchorPosY(y_value, 0.2)
	Hide_animator:SetEase(DG.Tweening.Ease.Linear)
end

function SocietyView:GetPrivateRoleId()
	return self.private_role_id
end


--发送信息
function SocietyView:OnSendMessage()
	if 0 == self.private_role_id then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.CorrentObj)
		return
	end

	local tmp_time = self:GetCdTime()
	if tmp_time > 0 and self.cd_timer then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.CanNotChat, math.ceil(tmp_time)))
		return
	end

	local text = self.node_list["ChatInput"].input_field.text
	local len = string.len(text)

	--判断是否是是gm命令
	if len >= 6 and string.sub(text, 1, 6) == "/jy_gm" then
		local blank_begin, blank_end = string.find(text, " ")
		local colon_begin, colon_end = string.find(text, ":")
		if blank_begin and blank_end and colon_begin and colon_end then
			local type = string.sub(text, blank_end + 1, colon_begin - 1)
			local command = string.sub(text, colon_end + 1, -1)
			SysMsgWGCtrl.SendGmCommand(type, command)
		end
		self:CleanInput()
		return
	elseif len >= 7 and string.sub(text, 1, 7) == "/jy_cmd" then
		local blank_begin, blank_end = string.find(text, " ")
		if blank_begin and blank_end then
			ClientCmdWGCtrl.Instance:Cmd(string.sub(text, blank_end + 1, len))
		end
		self:CleanInput()
		return
	end

	if len <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NilContent)
		return
	end

	if len >= COMMON_CONSTS.MAX_CHAT_MSG_LEN then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.ContentToLong)
		return
	end

	if ChatWGData.ExamineEditText(text, 0) == false then
		return
	end

	-- 聊天内容检测
	local message = ChatWGData.Instance:FormattingMsg(text, CHAT_CONTENT_TYPE.TEXT)
	if "" == message then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NilContent)
		return
	end

	--检测玩家输入的文本中是否包含GIF特殊表情
	if not ChatWGData.Instance:CheckCanShowStrGifFace(text) then
		ChatWGCtrl.Instance:OpenChatVipLimitTipView()
		return
	end

	--判断好友是否离线
	ChatWGCtrl.Instance:SendPrivateChatMsg(self.private_role_id, message, CHAT_CONTENT_TYPE.TEXT, nil, false)
	self:UpdateMsg()
	SocietyWGData.Instance:SetInputShow()
	self:CleanInput()
	self:SetCdTime()
end

-- 聊天CD
function SocietyView:GetCdTime()
	--local end_time = ChatWGData.Instance:GetChannelCdEndTime(CHANNEL_TYPE.WORLD) or Status.NowTime
	--return math.ceil(end_time - Status.NowTime)

	local end_time, is_clear_time = SocietyWGData.Instance:GetChannelCdEndTime()
	if (end_time - Status.NowTime) > 0 then
		return math.ceil(end_time - Status.NowTime)
	else
		return math.floor(end_time - Status.NowTime)
	end

end

function SocietyView:SetCdTime()
	--ChatWGData.Instance:SetChannelCdEndTime(CHANNEL_TYPE.WORLD)

	SocietyWGData.Instance:SetChannelCdEndTime()
	local end_time, is_clear_time = SocietyWGData.Instance:GetChannelCdEndTime()
	if end_time <= Status.NowTime then
		return
	end

	self:ShowBtnCd()
end

function SocietyView:ShowBtnCd()
	if nil == self.node_list["left_time"] then
		return
	end

	self:ClearCdTimer()
	local cd_time = self:GetCdTime()
	self:UpdateBtnCd()

	if cd_time > 0 then
		self.cd_timer = GlobalTimerQuest:AddTimesTimer(BindTool.Bind1(self.UpdateBtnCd, self), 1, cd_time)
	end
end

function SocietyView:ClearCdTimer()
	if nil ~= self.cd_timer then
		GlobalTimerQuest:CancelQuest(self.cd_timer)
		self.cd_timer = nil
	end
end

function SocietyView:UpdateMsg()
	self:SetAchievementData()
	SocietyWGData.Instance:SetPlayerprefsinfo(self.private_role_id)
	self:SetSocietyFriendNum()

	if self.cur_btn_list_num == 1 then
		self:SetAccordionData()
	end
end

function SocietyView:UpdateBtnCd()
	local cd_time = self:GetCdTime()
	if cd_time < 0 then
		if nil ~= self.node_list["left_time"] then
			self.node_list["left_time"].text.text = Language.Chat.Send
		end
		self:ClearCdTimer()
	else
		if nil ~= self.node_list["left_time"] then
			self.node_list["left_time"].text.text = Language.Chat.Wait .. "(" .. cd_time .. ")"
		end
	end
end

function SocietyView:ClearChatCd()
	local end_time, is_clear_time = SocietyWGData.Instance:GetChannelCdEndTime()
	if is_clear_time then
		self:ClearCdTimer()
		if nil ~= self.node_list["left_time"] then
			self.node_list["left_time"].text.text = Language.Chat.Send
		end
	end
end

function SocietyView:SendText(text)
	-- 聊天内容检测
	local message = ChatWGData.Instance:FormattingMsg(text, CHAT_CONTENT_TYPE.TEXT)
	if "" == message then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NilContent)
		return
	end

	--判断好友是否离线
	local info  = SocietyWGData.Instance:FindFriend(self.private_role_id)
	if nil == info or info.is_online == 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.FriendIsNotOnline)
		return
	end

	ChatWGCtrl.Instance:SendPrivateChatMsg(self.private_role_id, message, CHAT_CONTENT_TYPE.TEXT, nil, false)
	self:SetAchievementData()
end

function SocietyView:CleanInput()
	self.node_list["ChatInput"].input_field.text = ""
	ChatWGData.Instance:ClearInput()
end

function SocietyView:LoadBossListCell(index,is_auto,is_not_default)
	local data = {}
	if index == SOCIETY_LIXIREN then
		data = self:GetPlayerprefsinfo()
	elseif index == SOCIETY_FRIEND then
		data = SocietyWGData.Instance:GetFriendList2()
	elseif index == SOCIETY_ENEMY then
		data = SocietyWGData.Instance:GetEnemyList()
	elseif index == SOCIETY_BLACK then
		data = ChatWGData.Instance:GetBlacklist()
	end

	local num = #data >3 and 4 or #data
	for i=1, 4 do
		local space = self.node_list["space"..i]
		if i <= num then
			space.transform:SetParent(self.accordion_list[index].list.transform, false)
		else
			space.transform:SetParent(self.node_list["space_gropu"].transform,false)
		end
	end
end

--外层
function SocietyView:OnClickExpandHandler(is_click, index, is_auto, is_on) --is_auto 用来判断是否来自主界面icon触发的
	if self.cur_btn_list_num and self.cur_btn_list_num > 0 and not is_auto then
		self.role_data_list[self.cur_btn_list_num]:CancelSelect()
	end

	self.cur_btn_list_num = index
	if self.cur_btn_list_num ~= SOCIETY_LIXIREN then
		self.is_open_from_lyl = false
	end

	if nil == self.accordion_list then
		self.accordion_list = {}
	end

	if nil == self.accordion_list[index] then
		self.accordion_list[index] = {}
		self.accordion_list[index].text_name = self.node_list["text_btn"..index]
		self.accordion_list[index].text_name_hl = self.node_list["text_btn_hl"..index]
		self.accordion_list[index].list = self.node_list["List"..index ]
	end
	self:LoadBossListCell(index,is_auto,true)
	ChatWGCtrl.Instance:SendBlackGetList()

	self:SetNoFriendChatBg(true)
	self:FlushHideOrActive(false)
	--self.node_list.more_container:SetActive(false)
	self:CleanInput()
	-- print_error("SocietyView.OnClickExpandHandler", index, self.private_role_id)
	if not is_auto then
		self:SetPrivateRoleId(0)
		self.private_role_name = ""
	else
		if SOCIETY_FRIEND == index then--好友列表跳转
			if self.private_role_id and self.private_role_id ~= 0 then
				self:JumpToFriendByRoleId(self.private_role_id, index)
			end
		end
	end
	self:SetAccordionData(not is_click)

	self.node_list.btn_enemy_record:SetActive(self.cur_btn_list_num == 3)
	self.node_list.root_normal_btn:SetActive(self.cur_btn_list_num ~= 3)
	self.node_list.img_enemy_record_red:SetActive(SocietyWGData.Instance:GetIsHasNewEnemyRecord())
end

--取消选中高亮
function SocietyView:SetAllItemFalse()
	for k,v in pairs(self.accordion_cell_list) do
		for k1,v1 in pairs(v) do
			v1:OnSelectChange(false)
		end
	end
end

function SocietyView:SelectItemByIcon(index)
	self.accordion_cell_list[self.cur_btn_list_num][index]:OnClickItem(true)
end

--内层
function SocietyView:OnClickProductHandler(cell,cell_index,is_default,is_click)
	local bool = self.cur_btn_list_num and self.cur_btn_list_num > 2
	SocietyWGCtrl.Instance.is_friend_chat_panel = not bool
	if nil == cell.data then
		self:SetNoFriendChatBg(true)
		return
	end


	self.friend_select_chat_data = cell.data
	-- if self.private_role_id and self.private_role_id ~= cell.data.user_id then
		--self:ClearChatCd()
	-- end

	if self.from_lyl_role_id == nil then
		self.is_open_from_lyl = false
	else
		if self.from_lyl_role_id ~= cell.data.user_id then
			self.is_open_from_lyl = false
		end
	end

	cell:SetKuaFuState()
	self:SetPrivateRoleId(cell.data.user_id)
	self.private_role_name = cell.data.gamename

	local is_friend = SocietyWGData.Instance:GetIsMyFriend(self.private_role_id)
	local is_lyl_msg = false
	-- print_error("is_friend", self.private_role_id, is_friend)
	if self.cur_btn_list_num == SOCIETY_LIXIREN and cell.data.is_lyl and not is_friend then
		self.node_list.InputPanel:SetActive(false)
		self.node_list.cannot_input_promot:SetActive(false)
		self.node_list.top_info:SetActive(false)
		is_lyl_msg = true
		self:SetAchievementData(true, is_lyl_msg)
		self.node_list["layout_blank_tip2"]:SetActive(false)
		self:SetChatListActive(true)
		SocietyWGData.Instance:RemLYLUnreadMsg(cell.data.user_id)
		return
	end

	self:SetLiaoYiLiaoPanelState(false)
	self:SetNoFriendChatBg(bool)
	if self.cur_btn_list_num and self.cur_btn_list_num <= 2 then --好友跟联系人才处理信息是都读取
		ChatWGCtrl.Instance:AddPrivateRequset(cell.data.gamename, nil, cell.data.user_id, cell.data)
		ChatWGData.Instance:ReadPrivateCache(cell.data.user_id)
		ChatWGData.Instance:ReadMessage(cell.data.user_id)
		SocietyWGData.Instance:RemLYLUnreadMsg(cell.data.user_id)
		ChatWGData.Instance:RemPrivateUnreadMsg(cell.data.user_id)
		MainuiWGCtrl.Instance:SetChatPrivateUnreadMsgHead()
		self:ShowIntimacyInfo(cell.data.intimacy)
		self:ShowFriendInfo(cell.data.gamename, 1 == cell.data.is_online)
		cell:FlushUnChat()
	end

	if 1 == cell.data.is_online then
		ChatWGData.Instance:RemoveCache(cell.data.user_id)
	end

	SocietyWGData.Instance:SetSelectFriendSendId(cell.data.user_id)
	self.cur_cell_data = cell.data
	self:SetAchievementData(true, is_lyl_msg)
end

function SocietyView:ShowIntimacyInfo(intimacy_data)
	local intimacy_value = intimacy_data
	local intimacy_level = 0
	local cur_buff_cfg = {}
	local intimacy_buff_cfg = SocietyWGData.Instance:GetintimacyBuffCfg()
	for k,v in pairs(intimacy_buff_cfg) do
	 	if intimacy_value >= v.intimacy then
	 		intimacy_level = v.buff_level
	 		cur_buff_cfg = v
	 	else
	 		break
	 	end
	end
	self.node_list.qinmidu_text.text.text = string.format(Language.Society.QinMiDu, intimacy_level, cur_buff_cfg.buff_name)

	local res_str = intimacy_level == 0 and "a3_sj_xx3" or ( intimacy_level <= 5 and "a3_sj_xx1" or "a3_sj_xx2")
	local asset, bundle = ResPath.GetF2Society(res_str)
	self.node_list.image_heart.image:LoadSprite(asset, bundle, function()
		self.node_list.image_heart.image:SetNativeSize()
	end)
end

function SocietyView:ShowFriendInfo(name,is_online)
	self.node_list.text_friend_name.text.text = name
	self.node_list.text_online_state.text.text = is_online and Language.Society.OnlineState or Language.Society.OfflineState
	local asset, bundle = ResPath.GetF2Society(is_online and "a3_sj_zx" or "a3_sj_zx2")
	self.node_list.img_online_state.image:LoadSprite(asset, bundle)
end

function SocietyView:SetAchievementData(come_form_click, is_lyl_msg)
	if self.friend_chat_list == nil then
		return
	end

	if is_lyl_msg == nil and self.private_role_id > 0 then
		is_lyl_msg = not SocietyWGData.Instance:GetIsMyFriend(self.private_role_id)
	end

	local chat_list
	if is_lyl_msg then
		self:GetLYLContentListMsg()
		return
	else
		local private_obj1 = ChatWGData.Instance:GetPrivateObjByRoleId(self.private_role_id)
		if nil == private_obj1 then
			private_obj1 = {}
			self:SetFriendChatList(private_obj1)
			return
	    end

	    chat_list = private_obj1.msg_list 
	    if not IsEmptyTable(chat_list) then
	        for k, v in pairs(chat_list) do
	            v.is_from_private = 1
	        end
	    end

	    if come_form_click then
	    	self.node_list.new_msg:SetActive(false)
	    end
	end
		
    if chat_list then
    	self:SetFriendChatList(chat_list, come_form_click and 1 or nil)
    end
end

function SocietyView:GetLYLContentListMsg()
	local lyl_content = {}
	local is_show_opera_btn = false
	local lyl_interaction_info = MarryWGData.Instance:GetLiaoYiLiaoInteraction(self.private_role_id)
	if lyl_interaction_info and not IsEmptyTable(lyl_interaction_info.content_ids) then
		local main_role_id = RoleWGData.Instance:InCrossGetOriginUid()
		BrowseWGCtrl.Instance:SendQueryRoleInfoReq(self.private_role_id, function (role_info)
			for k,v in pairs(lyl_interaction_info.content_ids) do
				local msg_info = ChatWGData.CreateMsgInfo()
				local msg_role_info = v.sender_role_id == main_role_id 
									and RoleWGData.Instance:GetRoleVo()
									or role_info
				msg_info.from_uid = v.sender_role_id
				msg_info.msg_id = ChatWGData.Instance:GetMsgId()
				msg_info.channel_type = 12
				msg_info.content = SocietyWGData.Instance:GetLYLContentCfg(v.content_id)[1].content
				msg_info.sex = msg_role_info.sex
				msg_info.prof = msg_role_info.prof
				msg_info.username = msg_role_info.role_name
				msg_info.vip_level = msg_role_info.vip_level
				msg_info.shield_vip_flag = msg_role_info.shield_vip_flag
				msg_info.level = msg_role_info.level
				msg_info.send_time_str = v.msg_timestamp
				table.insert(lyl_content, msg_info)

				--print_error("msg_info =", v.msg_timestamp, msg_info, v)
				if v.sender_role_id ~= main_role_id then
					is_show_opera_btn = true
				end

				--print_error("设置撩一撩信息缓存", v.is_lyl_harass, v.sender_role_id, main_role_id)
				if v.is_lyl_harass == 1 and v.sender_role_id ~= main_role_id then
					local msg_info = ChatWGData.CreateMsgInfo()
					msg_info.from_uid = self.private_role_id
					msg_info.msg_id = ChatWGData.Instance:GetMsgId()
					msg_info.channel_type = 12
					msg_info.content = ""
					msg_info.is_lyl_harass = 1
					table.insert(lyl_content, msg_info)
				end
			end

			if is_show_opera_btn then
				local role_info = SocietyWGData.Instance:FindAddReq(self.private_role_id)
				if nil == role_info then
					is_show_opera_btn = false
				end
			end

			if self.node_list then
				self:SetFriendChatList(lyl_content, 1)
				self:SetLiaoYiLiaoPanelState(is_show_opera_btn)
				self.node_list.new_msg:SetActive(false)
			end
		end)
	end
end

function SocietyView:SetFriendChatList(data, refresh)
	-- print_error("刷新聊天", data)
	if self.friend_chat_list then
		self.friend_chat_list:SetDataList(data, refresh)
	end
end

function SocietyView:SetLiaoYiLiaoPanelState(state)
	self.node_list.LiaoYiLiaoPanel:SetActive(state)
end

function SocietyView:SetAccordionData(is_force_rebuild_layout)
	if nil == self.node_list.text_btn1 then
		return
	end
	self:SetSocietyFriendNum()
	self:SetEnemyNum()
	self:SetBlackNum()
	if self.cur_btn_list_num <= 0 then
		return
	end
	local data = {}
	local i = self.cur_btn_list_num
	--print_error("SetAccordionData", i)
	if nil == self.node_list["SelectBtn" .. i] or  self.node_list["SelectBtn" .. i].accordion_element.isOn == false then return end

	if i == SOCIETY_LIXIREN then
		data = self:GetPlayerprefsinfo()
	end

	if i == SOCIETY_FRIEND then
		data = SocietyWGData.Instance:GetFriendList2()
	end

	if i == SOCIETY_ENEMY then
		data = SocietyWGData.Instance:GetEnemyList()
	end

	if i == SOCIETY_BLACK then
		data = ChatWGData.Instance:GetBlacklist()
	end

	local spe_length_count = #data > 3 and 3.5 or #data
	--重新计算self.node_list["List"..i]的layout_element.preferredHeight，并且刷新显示
	if is_force_rebuild_layout and (self.node_list["List"..i].layout_element.preferredHeight < ITEM_HEIGHT * spe_length_count - 50 or
		self.node_list["List"..i].layout_element.preferredHeight > ITEM_HEIGHT * spe_length_count + 50) then
		local Height_value = (ITEM_HEIGHT * spe_length_count + 10) >= 308 and 308 or (ITEM_HEIGHT * spe_length_count + 10)
		self.node_list["List"..i].layout_element.preferredHeight = Height_value
		UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["List"..i].rect)
	end
	local y_value = (ITEM_HEIGHT * spe_length_count) >= 308 and 308 or (ITEM_HEIGHT * spe_length_count)
   	self.node_list["left_info_render_"..i].rect.sizeDelta = Vector2(326, y_value)



   	self.need_refresh_select_idnex = 0
   	self.need_refresh_society_idnex = 0
   	for k,v in ipairs(data) do
   		v.render_type = i
   		if self.private_role_id == v.user_id then
   			self.need_refresh_select_idnex = k
   			self.need_refresh_society_idnex = i
   		end
   	end
   --	self.node_list["left_info_render_"..i].rect.sizeDelta = Vector2(318, 91 * length_count)
   	-- if i ~= SOCIETY_LIXIREN or need_flush then
   		local need_refresh_active = SocietyWGData.Instance:GetListNeedRefreshActive()
   		-- print_error("~~~~~~~~~", need_refresh_active, data)
   		if need_refresh_active then
   			self.role_data_list[i]:RefreshActiveCellViews()
   			SocietyWGData.Instance:SetListNeedRefreshActive(false)
   		else
   			if self.need_refresh_select_idnex > 0 then
	   			self.role_data_list[i]:SetDefaultSelectIndex(self.need_refresh_select_idnex)
	   		end
   			self.role_data_list[i]:SetDataList(data)
   		end

   		-- self:FlushSelectFriendCell(i,select_idnex)

	-- end
end

function SocietyView:GetMainRolePos()
	if Scene.Instance:GetSceneType() == SceneType.GHOST_FB_GLOBAL then
        TipWGCtrl.Instance:ShowSystemMsg(Language.ZhuoGuiFuBen.LimitPosSend)
		return
	end

	local main_role = Scene.Instance.main_role
	if nil ~= main_role then
		local x, y = main_role:GetLogicPos()
		local pos_msg = string.format(Language.Chat.PosFormat, Scene.Instance:GetSceneName(), x, y)
		local edit_text = self.node_list["ChatInput"].input_field.text
		if ChatWGData.ExamineEditText(edit_text, 1) then
			self.node_list["ChatInput"].input_field.text = edit_text .. pos_msg
			ChatWGData.Instance:InsertPointTab()
			self:OnSendMessage()
		end
	end
end

function SocietyView:DeleteFriendView()
	if self.chat_pb_change_event then
		GlobalEventSystem:UnBind(self.chat_pb_change_event)
		self.chat_pb_change_event = nil
	end

	if self.event_listener ~= nil then
		self.event_listener = nil
	end

	self.private_role_id = nil
	self.click_voice_btn = false

	if self.bag then
		self.bag:DeleteMe()
		self.bag = nil
	end

	if self.stuff_bag then
		self.stuff_bag:DeleteMe()
		self.stuff_bag = nil
	end

	if self.cur_show_panel ~= nil then
		self.cur_show_panel=nil
	end

	if self.grid_normal_face ~= nil then
		self.grid_normal_face:DeleteMe()
		self.grid_normal_face = nil
	end

	if self.grid_word_face ~= nil then
		self.grid_word_face:DeleteMe()
		self.grid_word_face = nil
	end

	if self.grid_font_face ~= nil then
		self.grid_font_face:DeleteMe()
		self.grid_font_face = nil
	end

	if self.friend_chat_list then
		self.friend_chat_list:DeleteMe()
		self.friend_chat_list = nil
	end

	self.cur_btn_list_num = nil
	self.cur_cell_data = {}

	if self.accordion_cell_list then
		self.accordion_cell_list = nil
	end

	if self.accordion_list then
		self.accordion_list = nil
	end

	if nil ~= self.cd_timer then
		GlobalTimerQuest:CancelQuest(self.cd_timer)
		self.cd_timer = nil
	end

	if self.role_data_list then
		for k, v in pairs(self.role_data_list) do
			v:DeleteMe()
		end
		self.role_data_list = nil
	end
	
	self.layout_friend_base = nil
	self.last_select_role_index = 0
	self.firend_list_loaded_falg = false
	self.cur_button_index = nil
	self.friend_select_chat_data = nil

	self:PYPReleaseCallBack()
end

function SocietyView:CreateFriendList()
	self.friend_chat_list = ChatListView.New()
	self.friend_chat_list:SetChannelType(CHANNEL_TYPE.PRIVATE)
	self.friend_chat_list:Create(ChatCell, self.node_list.ChatList)
	self.friend_chat_list:SetScrollerEndHandle(BindTool.Bind(self.ScrollerEndHandle, self))
	self.friend_chat_list:SetNewMsgRefreshCallBack(BindTool.Bind(self.NewMsgRefresh, self))
end

function SocietyView:NewMsgRefresh()
	self.node_list.new_msg:SetActive(true)
end

function SocietyView:OnClickNewMsg()
	self.friend_chat_list:FlushCompreView()
	self.node_list.new_msg:SetActive(false)
end

function SocietyView:ScrollerEndHandle()
	self.node_list.new_msg:SetActive(false)
end

function SocietyView:OnClickOnekeyBlessing()
	SocietyWGCtrl.Instance:SendChangeBlessInfoReq(OPERA_TYPE.FRIEND_BLESS_OPERA_TYPE_BLESS, 0, 1)
end

function SocietyView:OnShowAddView()
	--local friend_open_cfg = FunOpen.Instance:GetFunByName("society_friend")
	-- local open_level = 0
	-- if friend_open_cfg and friend_open_cfg.trigger_param then
	-- 	open_level = friend_open_cfg.trigger_param
	-- end
	-- if GameVoManager.Instance:GetMainRoleVo().level < open_level then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Society.AddFriendLevelLimit,open_level))
	-- 	return
	-- end
	SocietyWGCtrl.Instance:OpenAddFrinedView()
end

-- 打开批量删除面板
function SocietyView:OnOpenAddRemoveView()
	SocietyWGCtrl.Instance:OpenRemovePanel()
end

--一键添加
function SocietyView:OnOpenAddFriendView()
	--local friend_open_cfg = FunOpen.Instance:GetFunByName("society_friend")
	-- local open_level = 0
	-- if friend_open_cfg and friend_open_cfg.trigger_param then
	-- 	open_level = friend_open_cfg.trigger_param
	-- end
	-- if GameVoManager.Instance:GetMainRoleVo().level < open_level then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Society.AddFriendLevelLimit,open_level))
	-- 	return
	-- end
	SocietyWGCtrl.Instance:SendGetRandomRoleList()
end

--删除好友
function SocietyView:OnDeleteFriend(user_id)
	SocietyWGCtrl.Instance:DeleteFriend(user_id)
end

-- 一键踩空间
-- function SocietyView:OnBatchCaiView()
-- 	RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_KEY_ADD_FRIEND)
-- end

--最近联系人  好友
function SocietyView:SetSocietyFriendNum()
	local list_1 =  self:GetPlayerprefsinfo()
	local temp_count1 = 0
	for k,v in pairs(list_1) do
		if v and v.is_online == 1 then
			temp_count1 = temp_count1 + 1
		end
	end

	self.node_list.text_btn1.text.text = string.format(Language.Society.LianXiRen,temp_count1,#list_1)
	self.node_list.text_btn_hl1.text.text = string.format(Language.Society.LianXiRen,temp_count1,#list_1)
	local list_2 =  SocietyWGData.Instance:GetFriendList2()
	local temp_count2 = 0

	for k,v in pairs(list_2) do
		if v and v.is_online == 1 then
			temp_count2 = temp_count2 + 1
		end
	end
	self.node_list.text_btn2.text.text = string.format(Language.Society.FriendNum,temp_count2,#list_2)
	self.node_list.text_btn_hl2.text.text = string.format(Language.Society.FriendNum,temp_count2,#list_2)
end

--仇人
function SocietyView:SetEnemyNum()
	local list_1 =  SocietyWGData.Instance:GetEnemyList()
	local temp_count1 = 0
	for k,v in pairs(list_1) do
		if v and (v.is_online == ONLINE_TYPE.ONLINE_TYPE_ONLINE or v.is_online == ONLINE_TYPE.ONLINE_TYPE_CROSS) then
			temp_count1 = temp_count1 + 1
		end
	end
	self.node_list.text_btn3.text.text = string.format(Language.Society.EnemyNum,temp_count1,#list_1)
	self.node_list.text_btn_hl3.text.text = string.format(Language.Society.EnemyNum,temp_count1,#list_1)
end

--黑名单
function SocietyView:SetBlackNum()
	local list_1 =  ChatWGData.Instance:GetBlacklist()
	self.node_list.text_btn4.text.text = string.format(Language.Society.BlackNum,#list_1)
	self.node_list.text_btn_hl4.text.text = string.format(Language.Society.BlackNum,#list_1)
end

function SocietyView:FriendDeleteHandle(uid)
	if self.private_role_id and self.private_role_id == uid then
		self:SetPrivateRoleId(0)
		if self.node_list and self.node_list["layout_blank_tip2"] then
			self:SetNoFriendChatBg(true)
		end
	end
end



function SocietyView:SetNoFriendChatBg( bool )
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local vip_level = VipWGData.Instance:GetRoleVipLevel()
	local real_chongzhi_rmb = RechargeWGData.Instance:GetRealChongZhiRmb()
	local limint_level, limint_vip_level, limint_real_chongzhi = ChatWGData.Instance:GetChatLevelLimint(CHANNEL_TYPE.PRIVATE)

	local is_can_chat = false

	if role_level >= limint_level then
		if real_chongzhi_rmb >= limint_real_chongzhi then
			if limint_vip_level > 0 then
				is_can_chat = vip_level >= limint_vip_level
				--[[
				-- 自己V6以下 对方V6以下（不充钱），不限制
				-- 自己V6以下 对方V6以上（充钱的），限制（显示那个条件）
				-- 自己V6以上 不限制
				local chat_target_vip = self.friend_select_chat_data and self.friend_select_chat_data.vip_level or 0
				if vip_level >= limint_vip_level then
					is_can_chat = true
				elseif vip_level < limint_vip_level and chat_target_vip < limint_vip_level then
					is_can_chat = true
				end
				]]
	
			else
				is_can_chat = true
			end
		end

	end

	self.node_list.layout_blank_tip2:SetActive(bool)
	self.node_list.InputPanel:SetActive(not bool and is_can_chat)
	self.node_list.cannot_input_promot:SetActive(not bool and not is_can_chat)
	if not bool and not is_can_chat then
		--策划要求 当Vip=0 只显示等级开启
		if limint_vip_level == 0 and limint_real_chongzhi == 0 then
			self.node_list.cannot_input_desc.text.text = string.format(Language.Chat.ChatLimitByLevel, limint_level)
		elseif limint_vip_level == 0 then
			self.node_list.cannot_input_desc.text.text = string.format(Language.Chat.ChatRealRecharge, limint_level, limint_real_chongzhi)
		else
			self.node_list.cannot_input_desc.text.text = string.format(Language.Chat.ChatLimitByLevelAndVIP, limint_level, limint_vip_level)
		end
	end

	self.node_list.top_info:SetActive(not bool and is_can_chat)
	if bool then
		self:SetLiaoYiLiaoPanelState(false)
		self.node_list.new_msg:SetActive(false)
	end

	if not bool then
		local str = SocietyWGData.Instance:GetInputShow()
		if nil ~= str and "" ~= str then
			self.node_list["ChatInput"].input_field.text = str
		end
	end
	self:SetChatListActive(not bool)
end

function SocietyView:SetChatListActive(state)
	self.node_list.ChatList:SetActive(state)
end

function SocietyView:FlushFriendNumView()


end

function SocietyView:GetGuideOpenKeyAddFriend()
	-- if  self.node_list["btn_autoaddfriend"] then
	-- 	return self.node_list["btn_autoaddfriend"], BindTool.Bind1(self.OnOpenAddFriendView, self)
	-- end
end

function SocietyView:RemindChange(remind_name, num)
	-- if remind_name == RemindName.AntiFraudCartoonRemind and self.node_list["cartoon_remind"] and self.node_list["loudspeaker"] then
	-- 	self.node_list["cartoon_remind"]:SetActive(num > 0)
	-- 	self.node_list["loudspeaker"].animator:SetBool("is_shake", num > 0)
	-- end

	-- if remind_id == RemindId.master_reward then
	-- 	self.tabbar:SetRemindNumByIndex(math.floor(tonumber(TabIndex.master_info) / 1000), num)
	-- elseif remind_id == RemindId.master_gongfeng then
	-- 	self.tabbar:SetRemindNumByIndex(math.floor(tonumber(TabIndex.master_worship) / 1000), num)
	-- elseif remind_id == RemindId.Fish then
	-- 	self.tabbar:SetRemindNumByIndex(math.floor(tonumber(TabIndex.society_buyu)/ 1000), num)
	-- end
end

function SocietyView:FirendListEndScrolledCallBack(scroll_obj, start_idx, end_idx)
	--防止其他跳转指定私聊时,刷新不及时
	--self.firend_list_loaded_falg 此变量只有好友跳转才会重设,此函数会在滑动列表调用,谨慎使用
	if self.firend_list_loaded_falg then
		self.firend_list_loaded_falg = false
		self:PersonChangeSort()
	end
end

function SocietyView:JumpToFriendByRoleId(role_id, index)
	-- print_error("JumpToFriendByRoleId", role_id, index)
	if index == SOCIETY_FRIEND then
		if not self.role_data_list[SOCIETY_FRIEND] or not role_id then
			return
		end
		
		local jump_cell_idx = self:GetFriendIndexByRoleId(role_id)
		self.role_data_list[SOCIETY_FRIEND]:JumpToIndex(jump_cell_idx)
		local cell = self.role_data_list[SOCIETY_FRIEND]:GetItemAt(jump_cell_idx)
		if cell then
			self:OnClickProductHandler(cell)
		end
	-- elseif index == SOCIETY_LIXIREN then
	-- 	local list_view = self.role_data_list[SOCIETY_LIXIREN]
	-- 	local data_list = list_view and list_view:GetDataList()
	-- 	-- print_error("data_list =", data_list)
	-- 	if data_list ~= nil then
	-- 		for i=1, #data_list do
	-- 			local cell_data = data_list[i]
	-- 			local cell = list_view:GetItemAt(i)
	-- 			-- print_error("cell_data =", i, cell_data and cell_data.user_id == role_id, cell_data)
	-- 			-- print_error("cell =", cell and cell:GetIndex() or "nil")
	-- 			if cell_data and cell_data.user_id == role_id then
	-- 				list_view:JumpToIndex(i)
	-- 				break
	-- 			end
	-- 		end
	-- 	end
	end
end

function SocietyView:GetFriendIndexByRoleId(role_id)
	local data = SocietyWGData.Instance:GetFriendList2() or {}
	local def_idx = 1
	for k, v in pairs(data) do
		if v.user_id == role_id then
			def_idx = k
			break
		end
	end
	return def_idx
end

function SocietyView:PersonChangeSort()
	if self.private_role_id then
		self:SetPrivateRoleId(self.private_role_id)
	end

	if nil == self.private_role_id or self.private_role_id <= 0 or nil == self.cur_btn_list_num
		or self.cur_btn_list_num <= 0 then return end
	if nil == self.node_list["SelectBtn" .. self.cur_btn_list_num] or  self.node_list["SelectBtn" .. self.cur_btn_list_num].accordion_element.isOn == false then return end

	local data = {}
	local index = 0
	if self.cur_btn_list_num == 1 then
		data = self:GetPlayerprefsinfo()
	elseif self.cur_btn_list_num == 2 then
		data = SocietyWGData.Instance:GetFriendList2()
	-- elseif self.cur_btn_list_num == 3 then
	-- 	data = SocietyWGData.Instance:GetEnemyList()
	elseif self.cur_btn_list_num == 4 then
		data = ChatWGData.Instance:GetBlacklist()
	end

	for k,v in pairs(data) do
		if v.user_id == self.private_role_id then
			index = k
			break
		end
	end

	if index <= 0 then
		self:SetNoFriendChatBg(true)
	else
		if self.last_select_role_index ~= index then
			self.last_select_role_index = index
		end
	end

	local rect_transform = self.node_list["left_info_render_"..self.cur_btn_list_num].scroll_rect.content:GetComponent(typeof(UnityEngine.RectTransform))
	local hight = rect_transform.sizeDelta.y
	local cell_hight = 86
	local space = 2
	local move_y = 0

	if #data > 3 then
		move_y = (index - 2) * (cell_hight + space)
		move_y = move_y < (hight - 3 * cell_hight - 2 * space) and move_y or  (hight - 3 * cell_hight - 2 * space)
		move_y = move_y > 0 and move_y or 0
		move_y = move_y
		rect_transform:DOAnchorPos(Vector2(0, move_y), 0.3)
	end
end

function SocietyView:OpenViewFormRoleId(role_id)
	if role_id then
		self.firend_list_loaded_falg = true
		self:SetPrivateRoleId(role_id)
		-- self.node_list["firend_btn_2"].toggle.isOn = true
		self.node_list["SelectBtn" .. 2].accordion_element.isOn = true
		self:OnClickExpandHandler(false, 2, true)
		-- SocietyWGCtrl.Instance.open_friend_from_main_icon = false
	end
end

function SocietyView:SetPrivateRoleId(role_id)
	--print_error("设置PrivateRoleId", role_id)
	self.private_role_id = role_id
end

function SocietyView:OpenViewFormAddFriend(role_id)
	-- print_error("OpenViewFormAddFriend", role_id, self.private_role_id, self.private_role_id == role_id, SocietyWGData.Instance:GetLYLRoleInfo(role_id) ~= nil)
	if role_id and self.private_role_id == role_id and SocietyWGData.Instance:GetLYLRoleInfo(role_id) ~= nil then
		-- print_error("OpenViewFormAddFriend2")
		self.firend_list_loaded_falg = true
		-- self:SetPrivateRoleId(role_id)
		self:SetNoFriendChatBg(false)
		-- self.node_list["SelectBtn" .. 2].accordion_element.isOn = true
		-- self:OnClickExpandHandler(false, 2,true)
		-- SocietyWGCtrl.Instance.open_friend_from_main_icon = false
		-- self:JumpToFriendByRoleId(role_id, SOCIETY_FRIEND)
	-- else
		-- SocietyWGData.Instance:RemoveLYLRoleInfo(role_id)
	end
end

function SocietyView:OpenViewFromLiaoYiLiao(role_id, is_force_jump)
	is_force_jump = is_force_jump ~= nil and is_force_jump == 1 or false
	-- print_error("SocietyView.OpenViewFromLiaoYiLiao", role_id, self.private_role_id)
	if role_id and is_force_jump then
		self.firend_list_loaded_falg = false
		self:SetPrivateRoleId(role_id)
		-- self.node_list["firend_btn_2"].toggle.isOn = true
		self.node_list["SelectBtn" .. 2].accordion_element.isOn = true
		self:OnClickExpandHandler(false, 2, true)
	end

	self:UpdateMsg()
end

function SocietyView:OpenViewFromMainIcon(is_init)
	--if SocietyWGCtrl.Instance.open_friend_from_main_icon == false then 
		--self:SetAccordionData(true)
	--	return 
	--end
	--优先判断帮派私聊，因为帮派私聊标识打开界面就会被清除，不会影响下边
	local guild_send_id = GuildWGData.Instance:GetSendID()
	if guild_send_id ~= nil then
		self:SetPrivateRoleId(guild_send_id)
		-- self.node_list["firend_btn_2"].toggle.isOn = true
		self.node_list["SelectBtn" .. 2].accordion_element.isOn = true
		self:OnClickExpandHandler(false, 2, true)
		GuildWGData.Instance:SetSendID(nil)
		SocietyWGCtrl.Instance.open_friend_from_main_icon = false
		return
	end

	if is_init then
		self.is_open_from_lyl = self.ignore_def_select_friend
	end

	if not self.ignore_def_select_friend then
		local unchat_list = ChatWGData.Instance:GetPrivateUnreadList()
		local yly_unchat_list = SocietyWGData.Instance:GetLYLUnreadList()
		local temp_info = unchat_list[#unchat_list]
		local lyl_temp_info = yly_unchat_list[#yly_unchat_list]
		if (temp_info and (self.private_role_id == 0 or temp_info.from_uid == self.private_role_id)) then
			self:SetPrivateRoleId(temp_info.from_uid)
			-- self.node_list["firend_btn_1"].toggle.isOn = true
			self.node_list["SelectBtn" .. 1].accordion_element.isOn = true
			self:OnClickExpandHandler(false, 1, true)
		elseif (lyl_temp_info and (self.private_role_id == 0 or lyl_temp_info.role_id == self.private_role_id)) then
			self:SetPrivateRoleId(lyl_temp_info.role_id)
			-- self.node_list["firend_btn_1"].toggle.isOn = true
			self.node_list["SelectBtn" .. 1].accordion_element.isOn = true
			self:OnClickExpandHandler(false, 1, true)
		else
			local friend_list = SocietyWGData.Instance:GetFriendList2()
			local have_jump = false
			for k,v in pairs(friend_list) do
				if v.touch_me_total_count > 0  then
					self:SetPrivateRoleId(v.user_id)
					have_jump = true
					-- self.node_list["firend_btn_1"].toggle.isOn = true
					self.node_list["SelectBtn" .. 1].accordion_element.isOn = true
					self:OnClickExpandHandler(false, 1,true)
					break
				end
			end

			if not have_jump then
				-- self.node_list["firend_btn_2"].toggle.isOn = true
				self.node_list["SelectBtn" .. 2].accordion_element.isOn = true
				--self:OnClickExpandHandler(false, 2,false)
			end

		end
	end

	SocietyWGCtrl.Instance.open_friend_from_main_icon = false
end

function SocietyView:FlushFriendView()
	MainuiWGCtrl.Instance:RemoveTipIconByIconObj(MAINUI_TIP_TYPE.FRIEND_CHAT)

	local data, cur_is_black = self:GetPlayerprefsinfo()
	if SocietyWGCtrl.Instance.open_friend_from_main_icon then
		self:OpenViewFromMainIcon(false)
	else
		if cur_is_black and SOCIETY_LIXIREN == self.cur_btn_list_num then
			self:OnClickExpandHandler(false, 1, false)
		else
			self:SetAccordionData(true)
		end
	end

	self:SetHuMoInfo()
	local is_remind = SocietyWGData.Instance:GetFirendRedShowList()
	self.node_list["friend_red"]:SetActive(1 == is_remind)

	local is_remind2 = SocietyWGData.Instance:GetRecentlyFirendRedShow(data)
	-- self.node_list["recently_friend_red"]:SetActive(1 == is_remind2)
	self.node_list["new_friend_red"]:SetActive(SocietyWGData.Instance:HadFriendReqRed() == 1)
	-- self.node_list["cartoon_remind"]:SetActive(RemindManager.Instance:GetRemind(RemindName.AntiFraudCartoonRemind) > 0)
	-- self.node_list["loudspeaker"].animator:SetBool("is_shake", RemindManager.Instance:GetRemind(RemindName.AntiFraudCartoonRemind) > 0)
	self.node_list.btn_enemy_record:SetActive(self.cur_btn_list_num == 3)
	self.node_list.root_normal_btn:SetActive(self.cur_btn_list_num ~= 3)
	self.node_list.img_enemy_record_red:SetActive(SocietyWGData.Instance:GetIsHasNewEnemyRecord())

	local is_friend = SocietyWGData.Instance:FindFriend(self.private_role_id)
	if is_friend  then
		self:SetLiaoYiLiaoPanelState(false)
	end

end

function SocietyView:ClickVoiceBtn()
	self.click_voice_btn = not self.click_voice_btn
	self:HideMsgInput(self.click_voice_btn)
end

function SocietyView:HideMsgInput(is_valid)
	--self.node_list["recording_btn"]:SetActive(is_valid)
	--self.node_list["msg_send_content"]:SetActive(not is_valid)
end

function SocietyView:OnPointDown(event_data)
	--self.is_down = true
	AutoVoiceWGCtrl.Instance:SetIsMainChatVoice(false,true)
	self:HandleVoiceStart()
	self.record_point_down_y = event_data.position.y
end

function SocietyView:OnDrag(event_data)
	local delta = math.abs(self.record_point_down_y - event_data.position.y)
	local is_in_range = delta < 46
	AutoVoiceWGCtrl.Instance:SetState(is_in_range and VoiceViewDragState.InRange or VoiceViewDragState.OutRange)
end

function SocietyView:OnPointUp(event_data)
	local delta = math.abs(self.record_point_down_y - event_data.position.y)
	local is_cancel_voice = delta > 46
	self:HandleVoiceStop(is_cancel_voice)
end

function SocietyView:HandleVoiceStop(is_cancel_voice)
	-- self.show_listen_trigger:SetValue(false)
	if AutoVoiceWGCtrl.Instance.view:IsOpen() then
		AutoVoiceWGCtrl.Instance:SetIsCancelVoice(is_cancel_voice)
		AutoVoiceWGCtrl.Instance.view:Close()
	end
end

function SocietyView:HandleVoiceStart()
	local private_role_id = self.private_role_id
	if private_role_id == 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.CorrentObj)
		return
	end
	AutoVoiceWGCtrl.Instance:ShowVoiceView( CHANNEL_TYPE.PRIVATE)
end

--获取之前保存的最近联系人
function SocietyView:GetPlayerprefsinfo()
	local temp_get_uid_list  = {}
	local old_user_id_list = {}
	local have_uid = {}
	local cur_is_black = false
	for i = 1,LianXiRenNum do
		if PlayerPrefsUtil.HasKey(SocietyWGData.GetPrivaPrefsKey("friend_last_uid", i)) then
			local temp_friend = PlayerPrefsUtil.GetInt(SocietyWGData.GetPrivaPrefsKey("friend_last_uid", i))
			local temp_time  = PlayerPrefsUtil.GetInt(SocietyWGData.GetPrivaPrefsKey("friend_last_time", i))
			local send_time  = PlayerPrefsUtil.GetInt(SocietyWGData.GetPrivaPrefsKey("friend_send_time", i))
			local is_form_lyl = PlayerPrefsUtil.GetInt(SocietyWGData.GetPrivaPrefsKey("friend_isfromlyl", i))
			local is_friend = SocietyWGData.Instance:FindFriend(temp_friend)
			local time_num = TimeWGCtrl.Instance:GetServerTime() - temp_time
			local time_level_three_day = 3 * 24 * 60 * 60 - time_num
			local is_black = ChatWGData.Instance:InBlacklist(temp_friend)
			if is_black then -- and self.private_role_id == temp_friend then
				if self.private_role_id ~= 0 then
					cur_is_black = self.private_role_id == temp_friend
				else
					cur_is_black = true
				end
			end
			if not is_black and not old_user_id_list[temp_friend] and (is_friend or is_form_lyl and is_form_lyl == 1) and time_level_three_day > 0 then
				local info = (is_form_lyl and is_form_lyl == 1) and SocietyWGData.Instance:GetLYLRoleInfo(temp_friend)
							 or SocietyWGData.Instance:GetAfterSortFriendList(temp_friend)
				if info then
					have_uid[temp_friend] = true
					info.is_recently_friend = true
					info.friend_last_time = temp_time
					info.friend_send_time = send_time
					info.is_form_lyl = is_form_lyl
					table.insert(temp_get_uid_list, info)
					old_user_id_list[temp_friend] = true
				end
			end
		end
	end

	local info_2 = {}
	local friend_list = SocietyWGData.Instance:GetFriendList2()
	for k,v in pairs(friend_list) do
		if v.touch_me_total_count and v.touch_me_total_count > 0 and not have_uid[v.user_id] then
			info_2 = SocietyWGData.Instance:GetAfterSortFriendList(v.user_id)
			info_2.friend_send_time = not self.is_open_from_lyl and TimeWGCtrl.Instance:GetServerTime() or 0
			table.insert(temp_get_uid_list, info_2)
		end
	end
	--根据好友最新一次发送信息的时间来做排序
    table.sort(temp_get_uid_list, SortTools.KeyUpperSorters("friend_send_time","is_online"))
	return temp_get_uid_list, cur_is_black
end

--删除最近联系人  并非删除好友 待定
function SocietyView:DeletePlayerprefsinfo()
end

--虎摸说明
function SocietyView:OnclickHuMoState()
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetTitle(Language.Society.HuMoTips)
		role_tip:SetContent(Language.Society.HuMoStateTips)
	end
end

function SocietyView:OnclickOpenHuMoRmberView()
	SocietyWGCtrl.Instance:OpenHuMoRmberView()
end

function SocietyView:OnClickAntiFraudCartoonBtn()
	ViewManager.Instance:Open(GuideModuleName.AntiFraudCartoonView)
end

function SocietyView:OnClickEnemyRecord()
	ViewManager.Instance:Open(GuideModuleName.SocietyEnemyRecordView)
end

function SocietyView:SetHuMoInfo()
	local shengyu_num = SocietyWGData.Instance:GetDayTouchShengYuNum()

	local humo_info = SocietyWGData.Instance:GetFriendTouchBaseInfo()
	local be_humo_num = 0

	if nil ~= humo_info then
		be_humo_num = humo_info.today_be_touch_count
	end
	--self.node_list["be_humo_num"].text.text = string.format(Language.Society.BeHuMoNum,be_humo_num)
end

AddFriendTipsPanel = AddFriendTipsPanel or BaseClass(SafeBaseView)

function AddFriendTipsPanel:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/society_ui_prefab", "layout_addfriend_tips_panel")
end

function AddFriendTipsPanel:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.FanRenXiuZhen.ChallageTipTitle
	self:SetSecondView(Vector2(753, 422.5))
	XUI.AddClickEventListener(self.node_list["ok_btn"], BindTool.Bind(self.OKFun, self)) --一键添加
	XUI.AddClickEventListener(self.node_list["cancle_btn"], BindTool.Bind(self.Close, self)) --一键添加
	self:Flush()
end

function AddFriendTipsPanel:SetDataInfo(info)
	self.data_info = info
	self:Open()
	self:Flush()
end

function AddFriendTipsPanel:OnFlush()
	if self.data_info == nil then
		return
	end

	XUI.UpdateRoleHead(self.node_list["ph_role_head"], self.node_list["custom_role_head"],
	self.data_info.role_id, self.data_info.sex,self.data_info.prof, false, nil, true)
	self.node_list.player_name.text.text = self.data_info.role_name
	self.node_list.desc.text.text = string.format(Language.Society.AddFriendTipsDesc,self.data_info.role_name)
end

function AddFriendTipsPanel:ReleaseCallBack()
	self.data_info = nil
end

function AddFriendTipsPanel:OKFun()
	SocietyWGCtrl.Instance:AddFriend(self.data_info.role_id, 0)
	self:Close()
end
