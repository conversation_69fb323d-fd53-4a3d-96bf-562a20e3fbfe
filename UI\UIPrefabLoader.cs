﻿//------------------------------------------------------------------------------
// This file is part of MistLand project in Nirvana.
// Copyright © 2016-2016 Nirvana Technology Co., Ltd.
// All Right Reserved.
//------------------------------------------------------------------------------

using System;
using UnityEngine;

public abstract class UIPrefabLoader : MonoBehaviour
{
    /// <summary>
    /// Wait the instance.
    /// </summary>
    public abstract void Wait(Action<GameObject> wait);
}
