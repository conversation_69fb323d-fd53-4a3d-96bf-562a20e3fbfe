MainUiMultiSkillSelectView = MainUiMultiSkillSelectView or BaseClass(SafeBaseView)

function MainUiMultiSkillSelectView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self.is_maskbg_button_click = true
	self:SetMaskBg(true, true)
	self:LoadConfig()

	self.pos_list = {}
	self.tween_list = {}
	self.wuxing_list = {}
end

function MainUiMultiSkillSelectView:__delete()
end

function MainUiMultiSkillSelectView:LoadConfig()
	self:AddViewResource(0, "uis/view/main_ui_prefab", "layout_multi_skill_select")
end

function MainUiMultiSkillSelectView:ReleaseCallBack()
end

function MainUiMultiSkillSelectView:LoadCallBack()
	for i = 1, 4 do
		if self.node_list["btn_skill_" .. i] ~= nil then
			XUI.AddClickEventListener(self.node_list["btn_skill_" .. i], BindTool.Bind(self.ClickSkill, self, i))
		end
	end

	local root_pos = self.node_list.root_pos.transform.localPosition
	local r = 120
	local start_rotate = 15
	for i = 1, 4 do
		local show_rotate = (start_rotate + 50 * i + 360) % 360
		local calc_x = root_pos.x
		local calc_y = root_pos.y
		local x = calc_x + math.cos(math.rad(show_rotate)) * r
		local y = calc_y + math.sin(math.rad(show_rotate)) * r
		-- if self.node_list["btn_skill_" .. i] ~= nil then
		-- 	self.node_list["btn_skill_" .. i].transform.localPosition = Vector3(x, y, root_pos.z)
		-- end
		self.pos_list[i] = Vector3(x, y, root_pos.z)
	end
end

function MainUiMultiSkillSelectView:ShowIndexCallBack()
	self.wuxing_list = {}
	local use_index = TianShenWGData.Instance:GetMultiSkillUseIndex()
	local index = 0
	for i = 1, 5 do
		if use_index ~= i then
			table.insert(self.wuxing_list, i)
			index = index + 1
			if self.node_list["btn_skill_" .. index] ~= nil then
				self.node_list["btn_skill_" .. index].image:LoadSprite(ResPath.GetMainUIIcon("wuxing_" .. i))
			end			
		end
	end

	self.is_show_ani = false
	self:ShowTween(true)
end

function MainUiMultiSkillSelectView:CloseCallBack()
	self:KillAllTween()
end

function MainUiMultiSkillSelectView:ClickSkill(index)
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local is_system_bianshen = SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM == main_role_vo.special_appearance
	if not is_system_bianshen then
		self:Close()
		return
	end

	if self.wuxing_list[index] == nil then
		self:Close()
		return
	end

	local send_index = self.wuxing_list[index]
	local remove_list, add_list = TianShenWGData.Instance:GetChangeMultiSendList(send_index)
	if remove_list == nil or add_list == nil then
		self:Close()
		return
	end

	self:Close()
	-- 羁绊技能不切
	local send_remove_list = {}
	local send_add_list = {}
	local use_index = 1
	for i = 1, #remove_list do
		if i ~= 4 then
			send_remove_list[use_index] = remove_list[i]
			send_add_list[use_index] = add_list[i]
			use_index = use_index + 1
		end
	end

	TianShenWGCtrl.Instance:SendPerformChangeTianShenSkill(send_index, send_remove_list, send_add_list)
end

function MainUiMultiSkillSelectView:OnFlush()
end

function MainUiMultiSkillSelectView:KillAllTween()
    for i = 1, 4 do
        if self.tween_list[i] ~= nil then
            self.tween_list[i]:Kill()
        end
    end

    self.tween_list = {}
end

function MainUiMultiSkillSelectView:ShowTween(is_show)
    if self.is_show_ani == is_show then
        return
    end

    if #self.pos_list == 0 then
    	return
    end
    
    self.is_show_ani = is_show
    self:KillAllTween()

    for i = 1, 4 do
        if self.is_show_ani then
        	if self.node_list["btn_skill_" .. i] ~= nil then
        		self.tween_list[i] = UITween.ShowFadeUpToObj(self.node_list["btn_skill_" .. i].gameObject, self.pos_list[i])
        	end
        else
        	if self.node_list["btn_skill_" .. i] ~= nil then
        		local root_pos = self.node_list.root_pos.transform.localPosition
	            local tween, call_back = UITween.HideFadeUpToObj(self.node_list["btn_skill_" .. i].gameObject, root_pos)
	            self.tween_list[i] = tween
	            if tween ~= nil then
	                tween:OnComplete(function()
	                    if call_back ~= nil then
	                        call_back()
	                    end
	                end)
	            end
        	end
        end
    end
end