require("game/draw_gift/draw_gift_wg_data")
require("game/draw_gift/draw_gift_items")

DrawGiftWGCtrl = DrawGiftWGCtrl or BaseClass(BaseWGCtrl)

function DrawGiftWGCtrl:__init()
	if DrawGiftWGCtrl.Instance then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[DrawGiftWGCtrl] attempt to create singleton twice!")
		return
	end

	DrawGiftWGCtrl.Instance = self
	self.data = DrawGiftWGData.New()
  
    self:RegisterAllProtocols()
end

function DrawGiftWGCtrl:__delete()
	DrawGiftWGCtrl.Instance = nil

    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end
end

function DrawGiftWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCDrawGiftAllInfo, "OnSCDrawGiftAllInfo")
	self:RegisterProtocol(SCDrawGiftUpdate, "OnSCDrawGiftUpdate")
end

function DrawGiftWGCtrl:OnSCDrawGiftAllInfo(protocol)
    self.data:SetAllDrawGiftInfo(protocol)
end

function DrawGiftWGCtrl:OnSCDrawGiftUpdate(protocol)
    self.data:SetSingleDrawGiftInfo(protocol)
end
