 ------------------------------------------------------------
--宠物相关主View
------------------------------------------------------------
HWDetailSkillTip = HWDetailSkillTip or BaseClass(SafeBaseView)

function HWDetailSkillTip:__init()
    self:SetMaskBg(true,true)
    self.view_layer = UiLayer.Pop
    self:AddViewResource(0, "uis/view/shenji_anqiruanjia_ui_prefab", "layout_juexin_skill_tip")
end

function HWDetailSkillTip:ReleaseCallBack()

end

function HWDetailSkillTip:SetData(data)
    if not data then return end
    self.data = data
    self:Open()
end

function HWDetailSkillTip:LoadCallBack(index, loaded_times)

end

function HWDetailSkillTip:CloseCallBack()
end

function HWDetailSkillTip:ShowIndexCallBack(index)
    self:Flush()

end

function HWDetailSkillTip:OnFlush(param_t, index)
    if not self.data then return end
    for k,v in pairs(param_t) do
        if k == "all" then
        end
    end

    -- 技能配置
    local data = self.data
    local special_effect_level = data.special_effect_level
    local big_type = data.big_type
    local is_active = special_effect_level > 0
    local awaken_cfg = HiddenWeaponWGData.Instance:GetAwakenCfgData(big_type)
    if not awaken_cfg then
        print_error('读取配置错误  data:', data)
        return
    end
    
    -- Top
    local def_cfg = awaken_cfg[1]
    local cur_cfg = awaken_cfg[special_effect_level]
    if awaken_cfg ~= nil then
        for k,v in pairs(awaken_cfg) do
            if v.effect_level == special_effect_level then
                cur_cfg = v
                break
            end
        end
    end

    local attr_data = AttributeMgr.GetAttributteByClass(cur_cfg)
    local cap = AttributeMgr.GetCapability(attr_data, cur_cfg)
    self.node_list["cap_value"].text.text = cap

    self.node_list.no_active:SetActive(not is_active)
    self.node_list.skill_name.text.text = def_cfg.effect_name
    self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(def_cfg.skill_icon))
    -- self.node_list.img_skill_icon_normal.image:LoadSprite(ResPath.GetCommonBackGround("bg_cell_circle3_" .. data.skill_cfg.color))
    for i=1,5 do
        self.node_list['star_' .. i]:SetActive(special_effect_level >= i)
    end

    -- Middle
    self.node_list.zs_info.text.text = def_cfg.skill_des
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.zs_info.rect)

    -- Bottom
    local cfg
    local previous_cfg
    local sort_list = AttributeMgr.SortAttribute()
    local attr_name = Language.Common.TipsAttrNameList
    for i=1,5 do
        cfg = awaken_cfg[i + 1]     
        previous_cfg = awaken_cfg[i]     
        if cfg then
            self.node_list['jx_info_' .. i]:SetActive(true)
            self.node_list['jx_attr_' .. i]:SetActive(true)
            self.node_list['jx_info_' .. i].text.text = cfg.skill_des
            UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list['jx_info_' .. i].rect)
            XUI.SetGraphicGrey(self.node_list['jx_info_' .. i], special_effect_level < i)

            local attr_str = ""
            local attr_list = AttributeMgr.GetAttributteByClass(cfg)
            attr_list = AttributeMgr.LerpAttributeAttr(AttributeMgr.GetAttributteByClass(previous_cfg), attr_list)
            local index = 1
            local is_per = false
            for i,v in ipairs(sort_list) do
                if attr_list[v] and attr_list[v] > 0 then
                    is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v)
                    attr_str = attr_str .. attr_name[v] .. " " .. ToColorStr(is_per and (attr_list[v]/100 .. "%") or attr_list[v], "#ffffff")
                    attr_str = attr_str .. "\n"
                    index = index + 1
                end
            end
            attr_str = string.sub(attr_str, 1, -2)

            self.node_list['jx_attr_' .. i].text.text = attr_str
            UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list['jx_attr_' .. i].rect)
            XUI.SetGraphicGrey(self.node_list['jx_attr_' .. i], special_effect_level < i)
        else
            self.node_list['jx_info_' .. i]:SetActive(false)
            self.node_list['jx_attr_' .. i]:SetActive(false)
        end
    end
    
end

