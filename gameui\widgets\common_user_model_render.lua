-- data = {
-- 	body_res_id = 0,		-- 身体	
-- 	mask_id = 0,			-- 脸饰
-- 	belt_id = 0,			-- 腰饰
-- 	tail_id = 0,			-- 尾巴
-- 	shou_huan_id = 0,		-- 手环
-- 	halo_id = 0,			-- 光环
-- 	wing_id = 0,			-- 羽翼
-- 	fabao_id = 0,			-- 法宝
-- 	jianzhen_id = 0,		-- 剑阵
-- 	weapon_id = 0,			-- 武器
-- 	foot_effect_id = 0,		-- 足迹
-- 	mount_res_id = 0,		-- 坐骑
-- 	mount_action = "",		-- 坐骑动作
-- }

CommonUserModelRender = CommonUserModelRender or BaseClass(BaseRender)
function CommonUserModelRender:__init()
	self.parent_view = nil
end

function CommonUserModelRender:LoadCallBack()
end

function CommonUserModelRender:__delete()
	self.parent_view = nil

	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	self.data_cache = nil
end

function CommonUserModelRender:AddUiRoleModel(parent)
	self.parent_view = parent
end

function CommonUserModelRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end

	if nil == self.role_model then
		self.role_model = RoleModel.New()

		if self.data.is_ui_scene then
			self.role_model:SetUISceneModel(self.node_list.display.event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
			self.parent_view:AddUiRoleModel(self.role_model, self.data.show_index)
		else
			local display_data = {
				parent_node = self.node_list["display"],
				camera_type = MODEL_CAMERA_TYPE.BASE,
				rt_scale_type = self.data.model_rt_type or ModelRTSCaleType.M,
				can_drag = false,
			}
			
			self.role_model:SetRenderTexUI3DModel(display_data)
	
			if self.parent_view then
				self.parent_view:AddUiRoleModel(self.role_model)
			end
		end
	end

	if self.data_cache == nil then
		self.data_cache = {}
	end

	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local special_status_table = {}
	local prof = role_vo.prof
	local sex = role_vo.sex
	local weapon_res_id = 0
	local action_name = self.data.cur_anim or SceneObjAnimator.Walk
	
	if self.data.weapon_id and self.data.weapon_id > 0 then		-- 武器
		weapon_res_id = RoleWGData.GetFashionWeaponId(sex, prof, self.data.weapon_id)
	else
		weapon_res_id = RoleWGData.GetJobWeaponId(sex, prof)
	end

	if self.data.foot_effect_id and self.data.foot_effect_id > 0 then		-- 足迹
		action_name = SceneObjAnimator.Walk
	end

	local appe_data = role_vo.appearance or {}
	local role_diy_appearance_data = role_vo.role_diy_appearance or {}
	local extra_role_model_data = {
		prof = prof,
		sex = sex,
        d_face_res = appe_data.default_face_res_id,
        d_hair_res = appe_data.default_hair_res_id,
		d_body_res = appe_data.default_body_res_id,
		weapon_res_id = weapon_res_id,
		animation_name = action_name,
		no_need_do_anim = false,

		eye_size = role_diy_appearance_data.eye_size,
		eye_position = role_diy_appearance_data.eye_position,
		eye_shadow_color = role_diy_appearance_data.eye_shadow_color,

		left_pupil_type = role_diy_appearance_data.left_pupil_type,
		left_pupil_size = role_diy_appearance_data.left_pupil_size,
		left_pupil_color = role_diy_appearance_data.left_pupil_color,

		right_pupil_type = role_diy_appearance_data.right_pupil_type,
		right_pupil_size = role_diy_appearance_data.right_pupil_size,
		right_pupil_color = role_diy_appearance_data.right_pupil_color,

		mouth_size = role_diy_appearance_data.mouth_size,
		mouth_position = role_diy_appearance_data.mouth_position,
		mouth_color = role_diy_appearance_data.mouth_color,

		face_decal_id = role_diy_appearance_data.face_decal_id,
		hair_color = role_diy_appearance_data.hair_color,
		preset_seq = role_diy_appearance_data.preset_seq,
    }

	self.role_model:SetRoleResid(self.data.body_res_id, nil, extra_role_model_data, self.data.is_not_reset_pos)

	if self.data.mask_id and self.data.mask_id > 0 then		-- 脸饰
		if self.data_cache.mask_id == nil or self.data_cache.mask_id ~= self.data.mask_id then
			self.data_cache.mask_id = self.data.mask_id
			self.role_model:SetMaskResid(self.data.mask_id)
		end
	else
		self.data_cache.mask_id = nil
		self.role_model:RemoveMask()
	end

	if self.data.belt_id and self.data.belt_id > 0 then		-- 腰饰
		if self.data_cache.belt_id == nil or self.data_cache.belt_id ~= self.data.belt_id then
			self.data_cache.belt_id = self.data.belt_id
			self.role_model:SetWaistResid(self.data.belt_id)
		end
	else
		self.data_cache.belt_id = nil
		self.role_model:RemoveWaist()
	end

	if self.data.tail_id and self.data.tail_id > 0 then		-- 尾巴
		if self.data_cache.tail_id == nil or self.data_cache.tail_id ~= self.data.tail_id then
			self.data_cache.tail_id = self.data.tail_id
			self.role_model:SetTailResid(self.data.tail_id)
		end
	else
		self.data_cache.tail_id = nil
		self.role_model:RemoveTail()
	end

	if self.data.shou_huan_id and self.data.shou_huan_id > 0 then		-- 手环
		if self.data_cache.shou_huan_id == nil or self.data_cache.shou_huan_id ~= self.data.shou_huan_id then
			self.data_cache.shou_huan_id = self.data.shou_huan_id
			self.role_model:SetShouHuanResid(self.data.shou_huan_id)
		end
	else		
		self.data_cache.shou_huan_id = nil
		self.role_model:RemoveShouHuan()
	end

	if self.data.halo_id and self.data.halo_id > 0 then		-- 光环
		if self.data_cache.halo_id == nil or self.data_cache.halo_id ~= self.data.halo_id then
			self.data_cache.halo_id = self.data.halo_id
			self.role_model:SetHaloResid(self.data.halo_id)
		end
	else
		self.data_cache.halo_id = nil
		self.role_model:RemoveHalo()
	end

	if self.data.wing_id and self.data.wing_id > 0 then		-- 羽翼
		if self.data_cache.wing_id == nil or self.data_cache.wing_id ~= self.data.wing_id then
			self.data_cache.wing_id = self.data.wing_id
			self.role_model:SetWingResid(self.data.wing_id, true)
		end
	else
		self.data_cache.wing_id = nil
		self.role_model:RemoveWing()
	end

	if self.data.fabao_id and self.data.fabao_id > 0 then		-- 法宝
		if self.data_cache.fabao_id == nil or self.data_cache.fabao_id ~= self.data.fabao_id then
			self.data_cache.fabao_id = self.data.fabao_id
			self.role_model:SetBaoJuResid(self.data.fabao_id)
		end
	else
		self.data_cache.fabao_id = nil
		self.role_model:RemoveBaoJu()
	end

	if self.data.jianzhen_id and self.data.jianzhen_id > 0 then		-- 剑阵
		if self.data_cache.jianzhen_id == nil or self.data_cache.jianzhen_id ~= self.data.jianzhen_id then
			self.data_cache.jianzhen_id = self.data.jianzhen_id
			self.role_model:SetJianZhenResid(self.data.jianzhen_id)
		end
	else
		self.data_cache.jianzhen_id = nil
		self.role_model:RemoveJianZhen()
	end

	-- if self.data.weapon_id and self.data.weapon_id > 0 then		-- 武器
	-- 	self.data_cache.weapon_id = self.data.weapon_id
	-- 	local res_id = AppearanceWGData.GetFashionWeaponIdByResViewId(self.data.weapon_id)
	-- 	self.role_model:SetWeaponResid(res_id)
	-- end

	if self.data.foot_effect_id and self.data.foot_effect_id > 0 then		-- 足迹
		if self.data_cache.foot_effect_id == nil or self.data_cache.foot_effect_id ~= self.data.foot_effect_id then
			self.data_cache.foot_effect_id = self.data.foot_effect_id
			self.role_model:SetFootTrailModel(self.data.foot_effect_id)
		end
		self.role_model:SetRotation(MODEL_ROTATION_TYPE.FOOT)
	else
		self.data_cache.foot_effect_id = nil
		self.role_model:RemoveFootTrail(true)
	end

	if self.data.mount_res_id and self.data.mount_res_id > 0 then		-- 坐骑
		if self.data_cache.mount_res_id == nil or self.data_cache.mount_res_id ~= self.data.mount_res_id then
			self.data_cache.mount_res_id = self.data.mount_res_id
			self.role_model:SetMountResid(self.data.mount_res_id)
		end

		self.role_model:PlayStartAction(self.data.mount_action)
	else
		self.data_cache.mount_res_id = nil
		self.role_model:RemoveMount()
	end

	
	if self.data.model_adjust_root_local_position then
		self.role_model:SetRTAdjustmentRootLocalPosition(self.data.model_adjust_root_local_position.x, self.data.model_adjust_root_local_position.y, self.data.model_adjust_root_local_position.z)
	end

	if self.data.model_adjust_root_local_scale then
		self.role_model:SetRTAdjustmentRootLocalScale(self.data.model_adjust_root_local_scale)
	end
	if self.data.model_rot then
		self.role_model:SetRTAdjustmentRootLocalRotation(self.data.model_rot.x, self.data.model_rot.y, self.data.model_rot.z)
	end
end

function CommonUserModelRender:SetRotation(vec3)
	if self.role_model then
		self.role_model:SetRotation(vec3)
	end
end

function CommonUserModelRender:SetUSAdjustmentNodeLocalScale(scale)
	if self.role_model then
		self.role_model:SetUSAdjustmentNodeLocalScale(scale)
	end
end

function CommonUserModelRender:SetUSAdjustmentNodeLocalPosition(pos)
	if self.role_model then
		self.role_model:SetUSAdjustmentNodeLocalPosition(pos.x, pos.y, pos.z)
	end
end

function CommonUserModelRender:SetUSAdjustmentNodeLocalRotationTween(pos)
	if self.role_model then
		self.role_model:SetUSAdjustmentNodeLocalRotationTween(pos.x, pos.y, pos.z)
	end
end

function CommonUserModelRender:SetUSAdjustmentNodeLocalRotation(rot)
	if self.role_model then
		self.role_model:SetUSAdjustmentNodeLocalRotation(rot.x, rot.y, rot.z)
	end
end

function CommonUserModelRender:ChangeRoleAction(animation)
	if self.role_model then
		self.role_model:PlayRoleAction(animation)
	end
end

function CommonUserModelRender:SetWeaponModel()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local special_status_table = {ignore_halo = true}
	local prof = role_vo.prof
	local sex = role_vo.sex
	local weapon_res_id = 0
	if self.data.weapon_id and self.data.weapon_id > 0 then		-- 武器
		weapon_res_id = RoleWGData.GetFashionWeaponId(sex, prof, self.data.weapon_id)
	else
		weapon_res_id = RoleWGData.GetJobWeaponId(sex, prof)
	end

	if weapon_res_id > 0 and self.role_model then
		local weapon_bundle, weapon_asset = ResPath.GetWeaponModelRes(weapon_res_id)
		self.role_model:SetWeaponModel(weapon_bundle, weapon_asset)
	end
end


function CommonUserModelRender:SetRemoveWeaponModel()
	if self.role_model then
		self.role_model:RemoveWeapon()
	end
end


function CommonUserModelRender:SetModelFocus(to_pos, from_pos, to_scale, from_scale, all_move_time)
	if self.role_model then
		self.role_model:SetModelFocus(to_pos, from_pos, to_scale, from_scale, all_move_time)
	end
end

-- 设置模型聚焦状态
-- is_not_pinch 是否不能聚焦
function CommonUserModelRender:ModelFocusStatus(is_not_pinch)
	if self.role_model then
		self.role_model:ModelFocusStatus(is_not_pinch)
	end
end

-- 设置聚焦移动
function CommonUserModelRender:ModelFocusMove(is_forward)
	if self.role_model then
		self.role_model:ModelFocusMove(is_forward)
	end
end

--去掉头发颜色
function CommonUserModelRender:ResetPartHairDyeColor()
	if self.role_model then
		self.role_model:ResetPartHairDyeColor()
	end
end