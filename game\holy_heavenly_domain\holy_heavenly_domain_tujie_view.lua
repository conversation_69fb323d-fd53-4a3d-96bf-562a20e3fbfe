HolyHeavenlyDomainTuJieView = HolyHeavenlyDomainTuJieView or BaseClass(SafeBaseView)

function HolyHeavenlyDomainTuJieView:__init()
    self:SetMaskBg(false, true)
    self.view_layer = UiLayer.Pop
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {vector2 = Vector2(0, 25), sizeDelta = Vector2(880, 590)})
    self:AddViewResource(0, "uis/view/holy_heavenly_domain_ui_prefab", "layout_holy_heavenly_domain_diagram")
end

function HolyHeavenlyDomainTuJieView:LoadCallBack()
    self.select_tujie_index = 1

    if not self.page_toggle_list then
		self.page_toggle_list = {}
		local node_num = self.node_list.adnt_page_point_root.transform.childCount
		for i = 1, node_num do
			self.page_toggle_list[i] = self.node_list.adnt_page_point_root:FindObj("page_point_" .. i)
		end

		self.page_toggle_list[self.select_tujie_index].toggle.isOn = true
	end

    for k, v in pairs(self.page_toggle_list) do
		v:CustomSetActive(k <= 3)
	end

    self.node_list.title_view_name.text.text = Language.HolyHeavenlyDomain.TuJieTitle
    XUI.AddClickEventListener(self.node_list.btn_right, BindTool.Bind(self.OnClickToChangeTuJie, self, 1))
    XUI.AddClickEventListener(self.node_list.btn_left, BindTool.Bind(self.OnClickToChangeTuJie, self, -1))
end

function HolyHeavenlyDomainTuJieView:ReleaseCallBack()
    self.page_toggle_list = nil
end

function HolyHeavenlyDomainTuJieView:OnFlush()
    self.node_list.btn_right:CustomSetActive(self.select_tujie_index < 3)
    self.node_list.btn_left:CustomSetActive(self.select_tujie_index > 1)

    for i = 1, 3 do
        self.node_list["diagram" .. i]:CustomSetActive(self.select_tujie_index == i)  
    end
end

function HolyHeavenlyDomainTuJieView:OnClickToChangeTuJie(index)
    local next_select_tujie = self.select_tujie_index + index
    self.select_tujie_index = next_select_tujie > 0 and next_select_tujie <= 3 and next_select_tujie or 3 or 0
    self.page_toggle_list[self.select_tujie_index].toggle.isOn = true
    self:Flush()
end