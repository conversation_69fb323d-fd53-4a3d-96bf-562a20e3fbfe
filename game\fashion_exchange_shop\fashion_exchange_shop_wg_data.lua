FashionExchangeShopWGData = FashionExchangeShopWGData or BaseClass()

Fashion_Exchange_Shop_Money_Type = {
	Type1 = 1, -- 璇玑印
	Type2 = 2, -- 五彩印
}

function FashionExchangeShopWGData:__init()
	if FashionExchangeShopWGData.Instance then
		error("[FashionExchangeShopWGData] Attempt to create singleton twice!")
		return
	end
	FashionExchangeShopWGData.Instance = self

	self:InitParam()
	self:InitConfig()
	self:ExplainCfg()
end

function FashionExchangeShopWGData:__delete()
	FashionExchangeShopWGData.Instance = nil
end

function FashionExchangeShopWGData:InitParam()
	self.limit_list = {}
	self.all_shop_info = {}
	self.select_cell_info =
	{
		seq = 1,
		cell_idx = 1,
	}
end

function FashionExchangeShopWGData:InitConfig()
	local cfg = ConfigManager.Instance:GetAutoConfig("shape_shop_auto")
	self.item_seq_cfg = ListToMap(cfg.item, "seq")
	self.item_seq_cfg_1 = ListToMap(cfg.item, "item_id", "price_type")
	self.type_item_cfg = ListToMap(cfg.item, "shop_type", "seq")
	self.shop_type_cfg = ListToMap(cfg.shop_type, "shop_type")
	self.condition_cfg = ListToMap(cfg.condition, "condition_id")
	self.money_cfg = cfg.money
	self.money_item_cfg = ListToMap(cfg.money, "item_id")


	self.other_cfg = cfg.other[1]
end

function FashionExchangeShopWGData:GetOtherConfig()
	return self.other_cfg
end

function FashionExchangeShopWGData:SetSelectCellInfo(data)
	self.select_cell_info = data
end

function FashionExchangeShopWGData:GetSelectCellInfo()
	return self.select_cell_info
end

function FashionExchangeShopWGData:GetMoneyCfg()
	return self.money_cfg
end

function FashionExchangeShopWGData:GetItemDataList(index)
	local shop_type = index

	local list = {}
	local item_list = self.type_item_cfg[shop_type]

	if not item_list then
		return list
	end

	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	for k, v in pairs(item_list) do
		if self:CheckItemCondition(v.seq, 2) and (5 == v.zhiye_show_limit or v.zhiye_show_limit == main_role_vo.prof) then
			table.insert(list, v)
		end
	end

	if not IsEmptyTable(list) then
		local function check_limit(data)
			local is_vip_limit, vip_limit_str, is_can_buy = FashionExchangeShopWGData.Instance:IsVipLimit(data.seq)
			local is_can_cell, sell_str = FashionExchangeShopWGData.Instance:IsCanCell(data.seq)
			-- local is_limit = FashionExchangeShopWGData.Instance:IsLimit(data.seq)

			local limit = 0
			--不可购买排序到最后.
			if not is_can_buy or not is_can_cell then
				limit = 1
			end
			return limit
		end

		table.sort(list, function (a, b)
			local order_a = 100000
			local order_b = 100000

			local limit_a = check_limit(a)
			local limit_b = check_limit(b)
			if limit_a > limit_b then
				order_a = order_a + 10000
			elseif limit_a < limit_b then
				order_b = order_b + 10000
			end

			if a.sort_id > b.sort_id then
				order_a = order_a + 1000
			elseif a.sort_id < b.sort_id then
				order_b = order_b + 1000
			end
			
			if a.seq > b.seq then
				order_a = order_a + 100
			elseif a.seq < b.seq then
				order_b = order_b + 100
			end

			return order_a < order_b  -- 升序排序
		end)
	end

	return list
end

function FashionExchangeShopWGData:AllShopInfo(protocol)
	for k, v in pairs(protocol.shop_item_list) do
		self.all_shop_info[v.seq] = v
	end
end

function FashionExchangeShopWGData:ChangeShopInfo(protocol)
	self.all_shop_info[protocol.shop_item.seq] = protocol.shop_item
end

function FashionExchangeShopWGData:GetAllShopInfoBySeq(seq)
	return self.all_shop_info[seq]
end

function FashionExchangeShopWGData:ExplainCfg()
	local list = nil
	for k, v in pairs(self.item_seq_cfg) do
		self.limit_list[v.seq] = {}
		self.limit_list[v.seq].day_limit = v.day_limit or 0
		self.limit_list[v.seq].whole_life_limit = v.whole_life_limit or 0
		self.limit_list[v.seq].weeks_limit = v.weeks_limit or 0
		self.limit_list[v.seq].month_limit = v.month_limit or 0
	end
end

-- 基于CheckItemCondition 不满足时
-- 检测是否只有指定条件不满足
-- cond_type指定条件
function FashionExchangeShopWGData:CheckConditionState(seq, check_type, cond_type)
	-- body
	check_type = check_type or 1
	local shop_cfg = self.item_seq_cfg[seq]
	if not shop_cfg or not cond_type then
		return false
	end

	local condition_id = nil
	if 1 == check_type then
		condition_id = shop_cfg.buy_condition
	elseif 2 == check_type then
		condition_id = shop_cfg.show_condition
	end
	local condition_cfg, condition_type = self:GetCondition(condition_id)
	if not condition_cfg or IsEmptyTable(condition_cfg) then
		return false
	end

	local flag_list = nil
	local flag
	if 0 == #condition_cfg then
		flag = ConditionManager.Instance:CheckSingle(condition_cfg)
		flag_list = {}
		flag_list[condition_cfg.condition_type] = flag
	else
		flag_list = ConditionManager.Instance:CheckConditionList(condition_cfg)
	end
	-- print_error("flag_list:::", cond_type, flag_list)
	for k, v in pairs(flag_list) do
		if not v and k ~= cond_type then
			return false
		end
	end

	return true
end

-- 检测条件
-- check_type 1：购买检测， 2：显示检测
-- back_reason Language里的枚举
function FashionExchangeShopWGData:CheckItemCondition(seq, check_type, back_reason)
	check_type = check_type or 1
	local shop_cfg = self.item_seq_cfg[seq]
	if not shop_cfg then return false end
	local condition_id = nil
	if 1 == check_type then
		condition_id = shop_cfg.buy_condition
	elseif 2 == check_type then
		condition_id = shop_cfg.show_condition
	end
	local condition_cfg, condition_type = self:GetCondition(condition_id)
	if not condition_cfg or IsEmptyTable(condition_cfg) then return false end

	local flag, str = nil, nil
	if 0 == #condition_cfg then
		flag, str = ConditionManager.Instance:CheckSingle(condition_cfg, back_reason)
	else
		flag, str = ConditionManager.Instance:CheckList(condition_cfg, condition_type, back_reason)
	end

	return flag, str
end

function FashionExchangeShopWGData:GetCondition(condition_id)
	local condition_cfg = self.condition_cfg[condition_id]
	if not condition_cfg then return {} end

	if condition_cfg.condition_type == ConditionManager.Check_Type.Type1 or
		condition_cfg.condition_type == ConditionManager.Check_Type.Type2 then
		local list = {}
		-- 只能嵌套一层
		for i = 1, 3 do
			condition_id = condition_cfg["param" .. i]
			if 0 ~= condition_id then
				list[i] = self.condition_cfg[condition_id]
			end
		end
		SortTools.SortAsc(list, "sort")
		return list, condition_cfg.condition_type
	else
		return condition_cfg
	end
end

--是否VIP等级限制
function FashionExchangeShopWGData:IsVipLimit(seq)
	local shop_cfg = self.item_seq_cfg[seq]
	local condition_id = shop_cfg.buy_condition
	local condition_cfg, condition_type = self:GetCondition(condition_id)
	local check_cfg = nil
	local is_show = false
	local is_vip = false
	local str = nil
	local is_can_buy = self:CheckItemCondition(seq)
	if 0 == #condition_cfg then
		is_vip = condition_cfg.condition_type == ConditionManager.Check_Type.Type103
		is_show = is_vip
		check_cfg = condition_cfg
		return is_show, check_cfg.param1, is_can_buy, check_cfg.param1
	else
		for k, v in pairs(condition_cfg) do
			if not is_vip then
				is_vip = v.condition_type == ConditionManager.Check_Type.Type103
				check_cfg = v
			end

			if condition_type == ConditionManager.Check_Type.Type1 and is_vip then
				is_show = true
				return is_show, check_cfg.param1, is_can_buy, check_cfg.param1
			elseif condition_type == ConditionManager.Check_Type.Type2 then
				if not is_can_buy then
					is_show = true
				end

				if is_show and is_vip then
					break
				end
			end
		end
	end

	if is_show and not is_can_buy then
		str = check_cfg.param1
	end

	return is_show, str, is_can_buy, check_cfg.param1
end

function FashionExchangeShopWGData:CheckMoneyToBuy(seq, num, need_tips, show_vip, buy_price)
	local shop_cfg = self.item_seq_cfg[seq]
	if not shop_cfg then
		return false
	end

	local price_num_1 = ItemWGData.Instance:GetItemNumInBagById(self.other_cfg.price_item_id1)
	local price_num_2 = ItemWGData.Instance:GetItemNumInBagById(self.other_cfg.price_item_id2)

	local flag = true
	local str = nil
	local price = buy_price or shop_cfg.price
	if shop_cfg.price_type == Fashion_Exchange_Shop_Money_Type.Type1 then
		if price_num_1 < price * num then
			flag = false
			if show_vip then
				UiInstanceMgr.Instance:ShowChongZhiView()
			elseif need_tips then
				str = Language.FashionExchangeShop.MoneyDes1
			end
		end
	elseif shop_cfg.price_type == Fashion_Exchange_Shop_Money_Type.Type2 then
		if price_num_2 < price * num then
			flag = false
			if show_vip then
				UiInstanceMgr.Instance:ShowChongZhiView()
			elseif need_tips then
				str = Language.FashionExchangeShop.MoneyDes2
			end
		end
	end

	if not flag and str then
		SysMsgWGCtrl.Instance:ErrorRemind(str)
	end

	return flag
end

function FashionExchangeShopWGData:GetShopCfgSeq(seq)
	return self.item_seq_cfg[seq]
end

function FashionExchangeShopWGData:GetShopList(item_id, buy_get_way)
	local item_list = {}
	for k, v in pairs(self.item_seq_cfg) do
		if item_id == v.itemid then
			table.insert(item_list, v)
		end
	end

	SortTools.SortAsc(item_list, "shop_type")

	return item_list
end

function FashionExchangeShopWGData:ContrastNum(limit_type, total_num, num, str_type_2)
	local can_buy_flag = total_num > num
	local color = ""
	if can_buy_flag then
		color = COLOR3B.C8
	else
		color = COLOR3B.C3
	end

	local str = ""
	if str_type_2 then
		str = string.format(Language.FashionExchangeShop["CanBuyDes" .. limit_type], total_num - num)
	else
		str = string.format(Language.FashionExchangeShop.BuyDes5, color, total_num - num)
		str = Language.FashionExchangeShop["BuyDes" .. limit_type] .. str
	end
	return can_buy_flag, str
end

-- 返回商品购买提示
function FashionExchangeShopWGData:ExplainComposeStr(seq, str_type_2)
	local limit_cfg = self:GetLimitCfg(seq)
	local shop_info = self.all_shop_info[seq]

	if not limit_cfg or not shop_info then
		return
	end

	local limit_num = 0

	local t_type = nil
	local list = {}
	local can_buy_flag, str = nil, nil

	-- 个人每日
	limit_num = limit_cfg.day_limit
	if 0 < limit_num then
		can_buy_flag, str = self:ContrastNum(1, limit_num, shop_info.day_buy_count, str_type_2)
		t_type = 1
		list[t_type] = {}
		list[t_type].flag = can_buy_flag
		list[t_type].str = str
		list[t_type].t_type = t_type
	end

	-- 个人终身
	limit_num = limit_cfg.whole_life_limit
	if 0 < limit_num then
		can_buy_flag, str = self:ContrastNum(2, limit_num, shop_info.life_buy_count, str_type_2)
		t_type = 2
		list[t_type] = {}
		list[t_type].flag = can_buy_flag
		list[t_type].str = str
		list[t_type].t_type = t_type
	end

	-- 每周限购
	limit_num = limit_cfg.weeks_limit
	if 0 < limit_num then
		can_buy_flag, str = self:ContrastNum(3, limit_num, shop_info.week_buy_count, str_type_2)
		t_type = 3
		list[t_type] = {}
		list[t_type].flag = can_buy_flag
		list[t_type].str = str
		list[t_type].t_type = t_type
	end
	
	-- 每月限购
	limit_num = limit_cfg.month_limit
	if 0 < limit_num then
		can_buy_flag, str = self:ContrastNum(4, limit_num, shop_info.month_buy_count, str_type_2)
		t_type = 4
		list[t_type] = {}
		list[t_type].flag = can_buy_flag
		list[t_type].str = str
		list[t_type].t_type = t_type
	end

	if IsEmptyTable(list) then
		return list
	end

	local tab = {}
	for index, value in pairs(list) do
		if value.flag then
			tab = value
		end
		break
	end

	return tab
end

-- 己售馨标识
function FashionExchangeShopWGData:ItemEmpty(seq)
	local limit_cfg = self:GetLimitCfg(seq)
	local shop_info = self.all_shop_info[seq]
	if not limit_cfg or not shop_info then return false end
	local limit_num = 0

	-- 个人终身
	limit_num = limit_cfg.whole_life_limit
	if 0 < limit_num and limit_num <= shop_info.life_buy_count then
		return true, string.format(Language.FashionExchangeShop.CanBuyDes2, 0)
	end

	-- 个人每日
	limit_num = limit_cfg.day_limit
	if 0 < limit_num and limit_num <= shop_info.day_buy_count then
		return true, string.format(Language.FashionExchangeShop.CanBuyDes1, 0)
	end

	--每周限购
	limit_num = limit_cfg.weeks_limit
	if 0 < limit_num and limit_num <= shop_info.week_buy_count then
		return true, string.format(Language.FashionExchangeShop.CanBuyDes3, 0)
	end

	--每月限购
	limit_num = limit_cfg.month_limit
	if 0 < limit_num and limit_num <= shop_info.month_buy_count then
		return true, string.format(Language.FashionExchangeShop.CanBuyDes4, 0)
	end

	return false
end

-- 是否可购买
function FashionExchangeShopWGData:IsCanCell(seq)
	local limit_cfg = self:GetLimitCfg(seq)
	local shop_info = self.all_shop_info[seq]
	local is_can_cell = true
	local str = nil
	if not limit_cfg or not shop_info then
		return is_can_cell, str
	end

	local limit_num = 0

	-- 个人终身
	limit_num = limit_cfg.whole_life_limit
	if 0 < limit_num and is_can_cell then
		is_can_cell = limit_num > shop_info.life_buy_count
		if not is_can_cell then
			str = Language.FashionExchangeShop.EmptyDes
		end
	end

	-- 个人每日
	limit_num = limit_cfg.day_limit
	if 0 < limit_num and is_can_cell then
		is_can_cell = limit_num > shop_info.day_buy_count
		if not is_can_cell then
			str = Language.FashionExchangeShop.IsCanCell
		end
	end

	-- 个人每周
	limit_num = limit_cfg.weeks_limit
	if 0 < limit_num and is_can_cell then
		is_can_cell = limit_num > shop_info.week_buy_count
		if not is_can_cell then
			str = Language.FashionExchangeShop.WeekSellOut
		end
	end

	-- 个人每月
	limit_num = limit_cfg.month_limit
	if 0 < limit_num and is_can_cell then
		is_can_cell = limit_num > shop_info.month_buy_count
		if not is_can_cell then
			str = Language.FashionExchangeShop.MonthSellOut
		end
	end

	return is_can_cell, str
end

function FashionExchangeShopWGData:GetMaxBuyNum(seq)
	--不限购.
	local limit_str_list = self:ExplainComposeStr(seq)
	if IsEmptyTable(limit_str_list) then
		return 999
	end

	local limit_cfg = self:GetLimitCfg(seq)
	local shop_cfg = self.item_seq_cfg[seq]
	local shop_info = self.all_shop_info[seq]
	if not limit_cfg or not shop_cfg then return 1 end
	if not shop_info then return 999 end

	local limit_num = 0
	local max = 0

	-- 个人终身
	limit_num = limit_cfg.whole_life_limit
	max = limit_num - shop_info.life_buy_count

	-- 每月限购
	local month_limit = limit_cfg.month_limit
	if limit_num <= 0 or (month_limit > 0 and limit_num > month_limit) then
		limit_num = month_limit
		-- 个人每日
		max = month_limit - shop_info.month_buy_count
	end

	-- 每周限购
	local weeks_limit = limit_cfg.weeks_limit
	if limit_num <= 0 or (weeks_limit > 0 and limit_num > weeks_limit) then
		limit_num = weeks_limit
		-- 个人每日
		max = weeks_limit - shop_info.week_buy_count
	end

	--无终身限购再判断每日限购. ex-终身限购大于每日限购也判断每日限购
	local day_limit = limit_cfg.day_limit
	if limit_num <= 0 or (day_limit > 0 and limit_num > day_limit) then
		limit_num = day_limit
		-- 个人每日
		max = day_limit - shop_info.day_buy_count
	end

	max = math.max(0, max)
	max = math.min(max, 999)

	return max
end

function FashionExchangeShopWGData:GetLimitCfg(seq)
	-- body
	return self.limit_list[seq]
end

function FashionExchangeShopWGData:IsLimit(seq)
	-- body
	local limit_cfg = self:GetLimitCfg(seq)
	if not limit_cfg then 
		return false 
	end

	if limit_cfg.day_limit > 0 or limit_cfg.whole_life_limit > 0 or limit_cfg.month_limit > 0 or limit_cfg.weeks_limit > 0 then
		return true
	end

	return false
end

function FashionExchangeShopWGData:CheckShowShopTab()
	local show_tab_list = {}

	for index, value in pairs(self.shop_type_cfg) do
		local flag = false
		local condition_cfg, condition_type = self:GetCondition(value.open_condition)
		if not IsEmptyTable(condition_cfg) then
			if 0 == #condition_cfg then
				flag = ConditionManager.Instance:CheckSingle(condition_cfg)
			else
				flag = ConditionManager.Instance:CheckList(condition_cfg, condition_type)
			end
		end
		show_tab_list[value.shop_type] = flag
	end

	return show_tab_list
end

-- 是否是璇玑云楼的货币道具
function FashionExchangeShopWGData:CheckIsMoneyExchangeItem(item_id)
	local data = (self.money_item_cfg or {})[item_id]

	return data ~= nil
end

-- 获取对应货币能够兑换的奖励展示
function FashionExchangeShopWGData:GetMoneyExchangeShowList(item_id)
	local final_list = nil
	local data = (self.money_item_cfg or {})[item_id]

	if data and data.exchange_item_show ~= nil and (not IsEmptyTable(data.exchange_item_show)) and data.exchange_title_tips ~= nil and 
	data.exchange_title_tips ~= "" then
		local final_list = {}
		final_list.suit_item_list_1 = {}
		final_list.suit_item_list_2 = {}
		local final_indx_1 = COMMON_CONSTS.NUMBER_ZERO
		local final_indx_2 = COMMON_CONSTS.NUMBER_ZERO

		for k, v in pairs(data.exchange_item_show) do
			if v and v.item_id then
				if final_indx_1 <= 3 then
					final_list.suit_item_list_1[final_indx_1] = v
					final_indx_1 = final_indx_1 + 1
				else
					final_list.suit_item_list_2[final_indx_2] = v
					final_indx_2 = final_indx_2 + 1
				end
			end
		end

		return final_list, data.exchange_title_tips
	end

	return final_list, ""
end










