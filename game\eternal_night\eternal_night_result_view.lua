EternalNightResultView = EternalNightResultView or BaseClass(SafeBaseView)
function EternalNightResultView:__init()
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_jiesuan_panel")
	self:AddViewResource(0, "uis/view/eternal_night_ui_prefab", "layout_eternal_night_result")
	self:SetMaskBg(false)
	self.active_close = false
	self.view_layer = UiLayer.Pop
end

function EternalNightResultView:__delete()
end

function EternalNightResultView:LoadCallBack()
	self.list_view = AsyncListView.New(EternalNightResultItem,self.node_list["list_view"])
	XUI.AddClickEventListener(self.node_list["close_btn"],BindTool.Bind(self.ClickCloseBtn,self))
	self.item_list = AsyncListView.New(ItemCell,self.node_list["item_list"])
	self.node_list.victory:SetActive(true)
end

function EternalNightResultView:ReleaseCallBack()
	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view = nil
	end
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end

	if CountDownManager.Instance:HasCountDown("eternal_night_result_close") then
		CountDownManager.Instance:RemoveCountDown("eternal_night_result_close")
	end
end

function EternalNightResultView:CloseCallBack()
	FuBenWGCtrl.Instance:SendLeaveFB()
end

function EternalNightResultView:ShowIndexCallBack()
	
	local scene_info = EternalNightWGData.Instance:GetSceneInfo()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local kick_out_role_time = scene_info.kick_out_role_time or 0
	local time = kick_out_role_time - server_time
	if time > 0 then
		if CountDownManager.Instance:HasCountDown("eternal_night_result_close") then
			CountDownManager.Instance:RemoveCountDown("eternal_night_result_close")
		end
		CountDownManager.Instance:AddCountDown("eternal_night_result_close", BindTool.Bind(self.UpdataTime, self), BindTool.Bind(self.TimeCompleteCallBack, self), nil, time, 1)
	end
end

function EternalNightResultView:OnFlush()
	local rank_data = EternalNightWGData.Instance:GetRankList()
	self.list_view:SetDataList(rank_data)
	local my_data = EternalNightWGData.Instance:GetPlayerInfoBySelfId()
	if my_data then
		local rank = my_data and my_data.rank or 0
		local score = my_data and my_data.score or 0
		local combo_kill_num = my_data and my_data.combo_kill_num or 0
		local live_time = my_data and my_data.live_time or 0
		local name = my_data and my_data.name or ""
		if rank <= 3 then
			self.node_list["rank_img"]:SetActive(true)
			self.node_list["rank_num"]:SetActive(false)
			self.node_list["rank_img"].image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp"..rank))
			self.node_list["rank_img"].image:SetNativeSize()
		else
			self.node_list["rank_img"]:SetActive(false)
			self.node_list["rank_num"]:SetActive(true)
		end
		self.node_list["rank_num"].text.text = rank
		self.node_list["score"].text.text = score
		self.node_list["kill_num"].text.text = combo_kill_num
		local str = TimeUtil.MSTime(live_time)
		self.node_list["live_time"].text.text = str
		self.node_list["name"].text.text = name
		if rank > 1 then
			self.node_list.title_img.image:LoadSprite(ResPath.GetCommonPanel("a3_gxhd_lbl_pm"))
		else
			self.node_list.title_img.image:LoadSprite(ResPath.GetCommonPanel("a3_gxhd_lbl_gj2"))
		end
		local other_cfg = EternalNightWGData.Instance:GetOtherCfg()
		local finals_max_relive_times = other_cfg.finals_max_relive_times
		local relive_times = my_data.relive_times or 0
		relive_times = finals_max_relive_times - relive_times
		self.node_list["live_num"].text.text = relive_times
	end
	local item_data = {}
	local self_info = EternalNightWGData.Instance:GetSelfInfo()
	if not IsEmptyTable(self_info) then
		local rank = my_data and my_data.rank or 0
		local rank_reward_cfg = EternalNightWGData.Instance:GetRankRewardCfgByRank(rank)
		if rank_reward_cfg then
			for i=0,#rank_reward_cfg.reward_item do
				table.insert(item_data,rank_reward_cfg.reward_item[i])
			end
		end
		self.node_list["item_list"]:SetActive(not IsEmptyTable(item_data))
		self.node_list["not_item_list"]:SetActive(IsEmptyTable(item_data))
		if not IsEmptyTable(item_data) then
			self.item_list:SetDataList(item_data)
		end
	end
end

function EternalNightResultView:UpdataTime(elapse_time, total_time)
	local time = math.floor(total_time - elapse_time)
	self.node_list["close_text"].text.text = string.format(Language.EternalNight.ResultOutTimer,time)
end

function EternalNightResultView:TimeCompleteCallBack()
	self:Close()
end

function EternalNightResultView:ClickCloseBtn()
	self:Close()
end

-----------------ItemRender-------------
EternalNightResultItem = EternalNightResultItem or BaseClass(BaseRender)

function EternalNightResultItem:__init()
	self.item_list = AsyncListView.New(ItemCell,self.node_list["item_list"])
end

function EternalNightResultItem:__delete()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end

function EternalNightResultItem:OnFlush()
	if not self.data then return end
	if self.data.rank <= 3 then
		self.node_list["rank_img"]:SetActive(true)
		self.node_list["bg"]:SetActive(true)
		self.node_list["rank_num"]:SetActive(false)
		self.node_list["line"]:SetActive(false)
		self.node_list["rank_img"].image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp"..self.data.rank))
		self.node_list["rank_img"].image:SetNativeSize()
		self.node_list["bg"].image:LoadSprite(ResPath.GetCommonImages("a3_ty_list_"..self.data.rank))
	else
		self.node_list["rank_img"]:SetActive(false)
		self.node_list["bg"]:SetActive(false)
		self.node_list["line"]:SetActive(true)
		self.node_list["rank_num"]:SetActive(true)
		self.node_list["rank_num"].text.text = self.data.rank
	end

	local role_uuid = RoleWGData.Instance:GetUUid()
	local name = self.data.name
	if role_uuid == self.data.uuid then
		name = ToColorStr(name, COLOR3B.D_GREEN)
	end
	self.node_list["name"].text.text = name
	self.node_list["kill_num"].text.text = self.data.combo_kill_num
	self.node_list["score"].text.text = self.data.score
	local str = TimeUtil.MSTime(self.data.live_time)
	self.node_list["live_time"].text.text = str
	local other_cfg = EternalNightWGData.Instance:GetOtherCfg()
	local finals_max_relive_times = other_cfg.finals_max_relive_times
	local relive_times = self.data.relive_times or 0
	relive_times = finals_max_relive_times - relive_times
	self.node_list["live_num"].text.text = relive_times

	local item_data = {}
	local rank_reward_cfg = EternalNightWGData.Instance:GetRankRewardCfgByRank(self.data.rank)
	if rank_reward_cfg then
		for i=0,#rank_reward_cfg.reward_item do
			table.insert(item_data,rank_reward_cfg.reward_item[i])
		end
	end
	
	self.node_list["item_list"]:SetActive(not IsEmptyTable(item_data))
	self.node_list["not_item_list"]:SetActive(IsEmptyTable(item_data))
	if not IsEmptyTable(item_data) then
		self.item_list:SetDataList(item_data)
	end 
end


