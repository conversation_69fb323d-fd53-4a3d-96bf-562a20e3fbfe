{"actorController": {"projectiles": [], "hurts": [], "beHurtEffecct": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "hurtEffectName": "", "beHurtNodeName": "", "beHurtAttach": false, "hurtEffectFreeDelay": 0.0, "QualityCtrlList": []}, "actorTriggers": {"effects": [{"triggerEventName": "attack1/begin", "triggerDelay": 0.4, "triggerFreeDelay": 0.0, "effectGoName": "eff_4016yushou_attack1", "effectAsset": {"BundleName": "effects/prefab/model/yushou/4016/eff_4016yushou_attack1_prefab", "AssetName": "eff_4016yushou_attack1", "AssetGUID": "220a503a52c69a24f93fa76d103d7dbe", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "rest/begin", "triggerDelay": 0.5, "triggerFreeDelay": 0.0, "effectGoName": "eff_4017yushou_rest", "effectAsset": {"BundleName": "effects/prefab/model/yushou/4017/eff_4017yushou_rest_prefab", "AssetName": "eff_4017yushou_rest", "AssetGUID": "2e1c0bd3c4b138943910c657964b998e", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "root", "isAttach": true, "isRotation": false, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "rest", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack2/begin", "triggerDelay": 0.6, "triggerFreeDelay": 0.0, "effectGoName": "yushou_skill_3", "effectAsset": {"BundleName": "effects/prefab/model/yushou/common/yushou_skill_3_prefab", "AssetName": "yushou_skill_3", "AssetGUID": "6b60b57d59716634aa70a7bfe0da9b2e", "IsEmpty": false}, "playerAtTarget": true, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": false, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2", "playerAtPos": false, "ignoreParentScale": false}], "halts": [], "sounds": [], "cameraShakes": [], "cameraFOVs": [], "sceneFades": [], "footsteps": []}, "actorBlinker": {"blinkFadeIn": 0.0, "blinkFadeHold": 0.0, "blinkFadeOut": 0.0}, "TimeLineList": []}