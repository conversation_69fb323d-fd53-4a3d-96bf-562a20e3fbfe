OperationActivityView = OperationActivityView or BaseClass(SafeBaseView)

local MONNEY_TYPE = {
	[1] = "i_xiaohao_xianyu",
	[2] = "i_xiaohao_bangyu",
	[3] = "",
}

local LIST_VIEW_HIGHT = {
	[1] = Vector2(1050, 322),			--3*3
	[2] = Vector2(1050, 505),			--3*4
}

--限时秒杀进度条档位
local XSMS_SLIDER_STEP = {
	[1] = 0.213,
	[2] = 0.409,
	[3] = 0.604,
	[4] = 0.799,
	[5] = 1,
}

function OperationActivityView:InitXianShiMiaoSha()

end

function OperationActivityView:LoadIndexCallBackXianShiMiaoSha()
	self.select_type = 1
	self.page_index = 1
	self.is_need_slider = true
	XianshiMiaoshaWGData.Instance:SetCurSelectType(self.select_type)

	if nil == self.xianshi_miaosha_list_view then
		local bundle, asset = "uis/view/operation_xianshi_miaosha_prefab", "Xianshi_Miaosha_Cell"
		self.xianshi_miaosha_list_view = AsyncBaseGrid.New()
		self.xianshi_miaosha_list_view:CreateCells({col = 5, itemRender = XianShiMiaoShaCell,
			list_view = self.node_list.miaosha_list_view, assetBundle = bundle, assetName = asset, change_cells_num = 1})
		self.xianshi_miaosha_list_view:SetStartZeroIndex(false)
	end

	if not self.top_btn_list then
		self.top_btn_list = {}

		for i=1,2 do
			self.top_btn_list[i] = self.node_list["miaosha_top_btn_" .. i]
			self.top_btn_list[i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickTopToggle, self, i))
		end

		self.top_btn_list[self.select_type].toggle.isOn = true
	end

	if not self.gift_list then
		self.gift_list = {}

		for i=1,5 do
			self.gift_list[i] = ItemCell.New(self.node_list["miaosha_item_cell_" .. i])
			self.gift_list[i]:SetClickCallBack(BindTool.Bind(self.OnclickGiftItem, self, i))
			self.gift_list[i]:SetIsShowTips(false)
			-- self.gift_list[i]:SetIsShowBigBottomText(true)
		end
	end

	self.node_list.miaosha_tips_toggle.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickTipsToggle, self))
	-- self.node_list.miaosha_tips_btn.button:AddClickListener(BindTool.Bind1(self.OnClickXianShiMiaoShaTipsBtn, self))

	local role_id = RoleWGData.Instance:GetRoleVo().role_id
	local value = PlayerPrefsUtil.GetInt("miaosha_tips_toggle_" .. role_id)
	self.node_list.miaosha_tips_toggle.toggle.isOn = value == 0
	self.xianshi_miaosha_interface_cfg = XianshiMiaoshaWGData.Instance:GetInterfaceCfg()

	self:FlushXianShiMiaoShaPictrue()
end

function OperationActivityView:DeleteXianShiMiaoSha()
	if self.xianshi_miaosha_list_view then
		self.xianshi_miaosha_list_view:DeleteMe()
		self.xianshi_miaosha_list_view = nil
	end

	if self.gift_list then
		for k,v in pairs(self.gift_list) do
			v:DeleteMe()
		end
		self.gift_list = nil
	end

	self.top_btn_list = nil
	self.select_type = nil
	self.page_index = nil
	self.is_need_slider = nil

	self:ClearXianshiMiaoShaCountDown()
end

function OperationActivityView:ShowIndexCallXianShiMiaoSha()
	self:SetXSMSRuleTipsInfo()
end

function OperationActivityView:FlushXianShiMiaoSha()
	self.xianshi_miaosha_data = XianshiMiaoshaWGData.Instance:GetXianShiMiaoShaInfoByType(self.select_type)
	-- print_error("FFFFF========== 界面取到的信息", self.xianshi_miaosha_data)
	self.xianshi_miaosha_interface_cfg = XianshiMiaoshaWGData.Instance:GetInterfaceCfg()

	-- self:FlushXianShiMiaoShaPictrue()
	self:FlushBottomSlider()
	self:RefreshListView()
	self:FlushTopBtn()
end

function OperationActivityView:SetXSMSRuleTipsInfo()
	local str_root = Language.OpertionAcitvity.XianShiMiaoSha
	self:SetRuleInfo(str_root.Rule_Des, str_root.Rule_Title)
    self:SetOutsideRuleTips(str_root.Bottom_Des)
end

--刷新ui
function OperationActivityView:FlushXianShiMiaoShaPictrue()

	if not self.xianshi_miaosha_interface_cfg then
		return
	end
	self.node_list.miaosha_mask_bg.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(self.xianshi_miaosha_interface_cfg.pic_12))
	self.node_list.miaosha_mask_bg.raw_image:SetNativeSize()

	for i=1,3 do
		self.node_list["miaosha_top_btn_" .. i].image:LoadSprite(ResPath.GetXianShiMiaoShaoPictrue(self.xianshi_miaosha_interface_cfg.pic_1))
		self.node_list["miaosha_top_btn_" .. i].image:SetNativeSize()

		self.node_list["miaosha_toggle_hl_" .. i].image:LoadSprite(ResPath.GetXianShiMiaoShaoPictrue(self.xianshi_miaosha_interface_cfg.pic_2))
		self.node_list["miaosha_toggle_hl_" .. i].image:SetNativeSize()
	end

	self.node_list.miaosha_img_1.image:LoadSprite(ResPath.GetXianShiMiaoShaoPictrue(self.xianshi_miaosha_interface_cfg.pic_4))
	self.node_list.miaosha_img_1.image:SetNativeSize()

	self.node_list.miaosha_img_2.image:LoadSprite(ResPath.GetXianShiMiaoShaoPictrue(self.xianshi_miaosha_interface_cfg.pic_4))
	self.node_list.miaosha_img_2.image:SetNativeSize()

	self.node_list.miaosha_img_3.image:LoadSprite(ResPath.GetXianShiMiaoShaoPictrue(self.xianshi_miaosha_interface_cfg.pic_3))
	self.node_list.miaosha_img_3.image:SetNativeSize()

	self.node_list.miaosha_slider_bg.image:LoadSprite(ResPath.GetXianShiMiaoShaoPictrue(self.xianshi_miaosha_interface_cfg.pic_9))
	self.node_list.miaosha_slider_bg.image:SetNativeSize()

	self.node_list["miaosha_slider_bg_1"].image:LoadSprite(ResPath.GetXianShiMiaoShaoPictrue(self.xianshi_miaosha_interface_cfg.pic_11))
	self.node_list["miaosha_slider_fill_1"].image:LoadSprite(ResPath.GetXianShiMiaoShaoPictrue(self.xianshi_miaosha_interface_cfg.pic_10))

	self.node_list.miaosha_text_img_1.image:LoadSprite(ResPath.GetXianShiMiaoShaoPictrue(self.xianshi_miaosha_interface_cfg.pic_16))
	self.node_list.miaosha_text_img_1.image:SetNativeSize()

	self.node_list.miaosha_other_desc.image:LoadSprite(ResPath.GetXianShiMiaoShaoPictrue(self.xianshi_miaosha_interface_cfg.pic_17))
	self.node_list.miaosha_other_desc.image:SetNativeSize()

	self.node_list.miaosha_raw_bg_2.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(self.xianshi_miaosha_interface_cfg.pic_20))
	self.node_list.miaosha_raw_bg_2.raw_image:SetNativeSize()

	for i=1,5 do
		self.node_list["miaosha_zhaungshi_" .. i].image:LoadSprite(ResPath.GetXianShiMiaoShaoPictrue(self.xianshi_miaosha_interface_cfg.pic_22))
		self.node_list["miaosha_zhaungshi_" .. i].image:SetNativeSize()
	end
	self.node_list.miaosha_tips_toggle_text.text.text = self.xianshi_miaosha_interface_cfg.desc_11

end

--刷新listview
function OperationActivityView:RefreshListView()
	self.node_list.miaosha_mask_bg:SetActive(false)
	self.node_list.miaosha_flush_time:SetActive(true)
	local data_list = XianshiMiaoshaWGData.Instance:GetXianShiMiaoShaListData(self.select_type)
	-- print_error("FFF===== 刷新listview", data_list)
	-- 备货期间不展示进度条、展示虚拟物品、展示遮罩
	if IsEmptyTable(data_list) then
		local fictitious_data_list = XianshiMiaoshaWGData.Instance:GetFictitiousDataList()

		self.node_list.miaosha_mask_bg:SetActive(true)
		self.node_list.miaosha_bottom_slider:SetActive(false)
		self.node_list.miaosha_flush_time:SetActive(false)
		return
	end

	if self.is_need_slider then
		self.node_list.miaosha_list_view.rect.sizeDelta = LIST_VIEW_HIGHT[1]
	else
		self.node_list.miaosha_list_view.rect.sizeDelta = LIST_VIEW_HIGHT[2]
	end

	self.xianshi_miaosha_list_view:SetDataList(data_list)
end

--刷新进度条
function OperationActivityView:FlushBottomSlider()
	if not self.xianshi_miaosha_data then
		return
	end

	local shop_lib_cfg = XianshiMiaoshaWGData.Instance:GetShopLibConfigByType(self.select_type, self.xianshi_miaosha_data.shop_id)
	if not shop_lib_cfg then
		self.node_list.miaosha_bottom_slider:SetActive(false)
		return
	end

	-- 赠送额度为0不展示进度条
	if IsEmptyTable(shop_lib_cfg.gift_item) then
		self.node_list.miaosha_bottom_slider:SetActive(false)
		self.is_need_slider = false
		return
	end
	local gift_quota_list = Split(shop_lib_cfg.gift_quota, ",")
	self.is_need_slider = true
	self.node_list.miaosha_bottom_slider:SetActive(true)

	local cur_finish_step = 0
	for i=1,5 do
		-- 物品
		if self.gift_list[i] and shop_lib_cfg.gift_item[i - 1] then
			self.gift_list[i]:SetData(shop_lib_cfg.gift_item[i - 1])
		end
		-- 额度
		if gift_quota_list[i] then
			local gift_quota = tonumber(gift_quota_list[i])
			self.node_list["miaosha_value_" .. i].text.text = gift_quota

			if self.xianshi_miaosha_data.total_quota >= gift_quota and self.xianshi_miaosha_data.quota_reward_tag[i] == 0 then
				--可领取未领取
				self.node_list["miaosha_effect_" .. i]:SetActive(true)
				self.gift_list[i]:SetLingQuVisible(false)
				cur_finish_step = i
			elseif self.xianshi_miaosha_data.total_quota >= gift_quota and self.xianshi_miaosha_data.quota_reward_tag[i] == 1 then
				--已领取
				self.node_list["miaosha_effect_" .. i]:SetActive(false)
				self.gift_list[i]:SetLingQuVisible(true)
				cur_finish_step = i
			else
				self.node_list["miaosha_effect_" .. i]:SetActive(false)
				self.gift_list[i]:SetLingQuVisible(false)
			end
		end
	end

	--进度条特殊设置
	local slider_show_value = 0
	if cur_finish_step > 0 and gift_quota_list[cur_finish_step] then
		if tonumber(gift_quota_list[cur_finish_step]) == self.xianshi_miaosha_data.total_quota then
			slider_show_value = XSMS_SLIDER_STEP[cur_finish_step]
		else
			local next_index = cur_finish_step + 1
			local is_max = next_index > #gift_quota_list
			if is_max then
				slider_show_value = 1
			else
				local temp_total = tonumber(gift_quota_list[next_index]) - tonumber(gift_quota_list[cur_finish_step])
				local temp_cur = self.xianshi_miaosha_data.total_quota - tonumber(gift_quota_list[cur_finish_step])
				local temp_slider_value = XSMS_SLIDER_STEP[next_index] - XSMS_SLIDER_STEP[cur_finish_step]
				local temp_rate = temp_cur / temp_total
				slider_show_value = XSMS_SLIDER_STEP[cur_finish_step] + (temp_rate * temp_slider_value)
			end
		end
	else
		slider_show_value = (self.xianshi_miaosha_data.total_quota / tonumber(gift_quota_list[1])) * XSMS_SLIDER_STEP[1]
	end
	self.node_list["miaosha_slider_1"].slider.value = slider_show_value

	self.node_list.miaosha_raw_bg_2:SetActive(self.is_need_slider)
	self.node_list.miaosha_total_value.text.text = self.xianshi_miaosha_data.total_quota
	-- self.node_list.miaosha_tips_desc_1.text.text = shop_lib_cfg.time
	local other_cfg = XianshiMiaoshaWGData.Instance:GetOtherCfg()

	if other_cfg then
		self.node_list.miaosha_money_count.text.text = other_cfg.price
	end

	self:SetOutsideRuleTips(shop_lib_cfg.rule)

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPERA_ACT_XIANSHI_MIAOSHA)

	if not activity_info or activity_info.end_time == -1 then
		return
	end

	self:SetActRemainTime(TabIndex.operation_act_xianshi_miaosha, activity_info.end_time)

end

--专属类型切换
function OperationActivityView:OnClickTopToggle(index, is_on)
	if is_on then
		if self.select_type == index then
			return
		end

		self.select_type = index
		XianshiMiaoshaWGData.Instance:SetCurSelectType(self.select_type)
		RemindManager.Instance:Fire(RemindName.OperationXianshiMiaosha)
		self.page_index = 1
		self:FlushXianShiMiaoSha()
	end
end

--刷新顶部按钮
function OperationActivityView:FlushTopBtn()
	self:ClearXianshiMiaoShaCountDown()

	if not self.xianshi_miaosha_data then
		return
	end

	local role_id = RoleWGData.Instance:GetRoleVo().role_id
	PlayerPrefsUtil.SetInt("refresh_remind_flag" .. self.select_type .. role_id, 0)
	for i=1,2 do
		local data = XianshiMiaoshaWGData.Instance:GetXianShiMiaoShaInfoByType(i)

		if data.shop_id ~= 0 then
			local shop_lib_cfg = XianshiMiaoshaWGData.Instance:GetShopLibConfigByType(i, data.shop_id)

			if shop_lib_cfg then
				self.node_list["miaosha_top_btn_" .. i]:SetActive(true)

				if i == self.select_type then
					self.node_list["miaosha_top_btn_text_" .. i].text.text = ToColorStr(shop_lib_cfg.type_name, COLOR3B.RED)
				else
					self.node_list["miaosha_top_btn_text_" .. i].text.text = ToColorStr(shop_lib_cfg.type_name, COLOR3B.WHITE)
				end
			end
		else
			self.node_list["miaosha_top_btn_" .. i]:SetActive(false)
		end
	end

	local time = self.xianshi_miaosha_data.refresh_time - TimeWGCtrl.Instance:GetServerTime()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPERA_ACT_XIANSHI_MIAOSHA)

	if not activity_info or activity_info.end_time == -1 then
		self.node_list.miaosha_flush_time.text.text = ""
		return
	end

	local end_time = activity_info.end_time - TimeWGCtrl.Instance:GetServerTime()
	if time > 0 and time <= end_time then
		CountDownManager.Instance:AddCountDown("xianshi_miaosha_countdown", BindTool.Bind1(self.XianshiMiaoShaChangeTime, self), BindTool.Bind1(self.XianshiMiaoShaCompleteTime, self), nil, time, 1)
		self:XianshiMiaoShaChangeTime(0, time)
	else
		self.node_list.miaosha_flush_time.text.text = ""
	end
end

--计时器
function OperationActivityView:XianshiMiaoShaChangeTime(elapse_time, total_time)
	local time = total_time - elapse_time

	if time > 0 then
		if self.node_list.miaosha_flush_time and self.xianshi_miaosha_interface_cfg then
			
			self.node_list.miaosha_flush_time.text.text = string.format(self.xianshi_miaosha_interface_cfg.desc_1, TimeUtil.FormatSecond(time, 3))
		end

		local time_list = TimeUtil.Format2TableDHM2(time)

		if self.node_list.miaosha_flush_time_2 then
			self.node_list.miaosha_flush_time_2.text.text = string.format("%02d:%02d:%02d", time_list.hour, time_list.min, time_list.sec)
		end
	else
		self.node_list.miaosha_flush_time.text.text = ""
	end
end

--计时完成
function OperationActivityView:XianshiMiaoShaCompleteTime()
	self:ClearXianshiMiaoShaCountDown()
	self:XianshiMiaoShaChangeTime(0, 0)
end

--清除计时器
function OperationActivityView:ClearXianshiMiaoShaCountDown()
	if CountDownManager.Instance:HasCountDown("xianshi_miaosha_countdown") then
		CountDownManager.Instance:RemoveCountDown("xianshi_miaosha_countdown")
	end
end

-- 秒杀提醒勾选
function OperationActivityView:OnClickTipsToggle(is_on)
	local role_id = RoleWGData.Instance:GetRoleVo().role_id
	local value = is_on == true and 0 or 1
	PlayerPrefsUtil.SetInt("miaosha_tips_toggle_" .. role_id, value)
end

-- 累计奖励点击事件
function OperationActivityView:OnclickGiftItem(index)
	if not self.xianshi_miaosha_data then
		return
	end

	if not self.xianshi_miaosha_data.quota_reward_tag[index] then
		return
	end

	local shop_lib_cfg = XianshiMiaoshaWGData.Instance:GetShopLibConfigByType(self.select_type, self.xianshi_miaosha_data.shop_id)

	if not shop_lib_cfg then
		return
	end

	if shop_lib_cfg.gift_item == 0 or shop_lib_cfg.gift_quota == 0 then
		return
	end

	local gift_quota_list = Split(shop_lib_cfg.gift_quota, ",")

	if not gift_quota_list[index] then
		return
	end

	if self.xianshi_miaosha_data.total_quota >= tonumber(gift_quota_list[index]) and self.xianshi_miaosha_data.quota_reward_tag[index] == 0 then
		--请求
		ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.OPERA_ACT_XIANSHI_MIAOSHA, OA_TIMED_SPIKE_OPERA_TYPE.OA_TIMED_SPIKE_OPERA_TYPE_RECEIVE_GIFT, self.select_type, index - 1)
		
		local shop_lib_cfg = XianshiMiaoshaWGData.Instance:GetShopLibConfigByType(self.select_type, self.xianshi_miaosha_data.shop_id)

		if shop_lib_cfg and shop_lib_cfg.gift_item[index - 1] then
			local reward_list = {[1] = shop_lib_cfg.gift_item[index - 1]}
			TipWGCtrl.Instance:ShowGetReward(nil, reward_list, false)
		end
	else
		--提示
		TipWGCtrl.Instance:OpenItem(shop_lib_cfg.gift_item[index - 1])
	end

end
-----------------------------------------------------------XianShiMiaoShaCell-------------------------------------------------------------------------------

XianShiMiaoShaCell = XianShiMiaoShaCell or BaseClass(BaseGridRender)

function XianShiMiaoShaCell:LoadCallBack()
	self.node_list.buy_btn.button:AddClickListener(BindTool.Bind(self.OnClickBuyBtn, self))

	self.item_cell = ItemCell.New(self.node_list.item_cell)
	self:FlushPictrue()
end

function XianShiMiaoShaCell:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function XianShiMiaoShaCell:FlushPictrue()
	local interface_cfg = XianshiMiaoshaWGData.Instance:GetInterfaceCfg()

	if not interface_cfg then
		return
	end

	self.node_list.Bg.image:LoadSprite(ResPath.GetXianShiMiaoShaoPictrue(interface_cfg.pic_8))
	self.node_list.Bg.image:SetNativeSize()

	self.node_list.line.image:LoadSprite(ResPath.GetXianShiMiaoShaoPictrue(interface_cfg.pic_5))	

	self.node_list.original_price.text.text = interface_cfg.desc_6
	self.node_list.cur_price.text.text = interface_cfg.desc_5
end

function XianShiMiaoShaCell:OnFlush()
	self:FlushPictrue()
	if not self.data then
		return
	end
	local select_type = XianshiMiaoshaWGData.Instance:GetCurSelectType()

	--虚拟物品
	if self.data.is_fictitious then
		self.item_cell:SetData({item_id = self.data.item_id})
		self.node_list.original_price_value.text.text = "？？？"
		self.node_list.original_money_icon.image:LoadSprite(ResPath.GetF2CommonIcon(MONNEY_TYPE[select_type]))
		self.node_list.cur_money_icon.image:LoadSprite(ResPath.GetF2CommonIcon(MONNEY_TYPE[select_type]))
		self.node_list.cur_price_value.text.text = "？？？"
		self.node_list.discount_desc.text.text = string.format(Language.OpertionAcitvity.XianShiMiaoSha.DescountDesc, "？")
		self.node_list.surplus_desc.text.text = ""
		self.node_list.limit_desc:SetActive(false)
		self.node_list.sellout_flag:SetActive(false)
		self.node_list.buy_btn:SetActive(true)
		self.node_list.limit_count_desc:SetActive(false)
		return
	end

	local item_lib_cfg = XianshiMiaoshaWGData.Instance:GetItemLibConfigById(self.data.id)

	if not item_lib_cfg then
		return
	end

	self.node_list.original_price_value.text.text = item_lib_cfg.original_price
	self.node_list.original_money_icon.image:LoadSprite(ResPath.GetF2CommonIcon(MONNEY_TYPE[select_type]))
	self.node_list.original_money_icon.image:SetNativeSize()

	self.node_list.cur_money_icon.image:LoadSprite(ResPath.GetF2CommonIcon(MONNEY_TYPE[select_type]))
	self.node_list.cur_money_icon.image:SetNativeSize()

	self.node_list.cur_price_value.text.text = item_lib_cfg.current_price
	self.item_cell:SetData({item_id = item_lib_cfg.item_id, num = 1, is_bind = item_lib_cfg.is_bind})
	
	local vip_level = VipWGData.Instance:IsVip() and RoleWGData.Instance.role_vo.vip_level or 0
	local role_level = RoleWGData.Instance:GetRoleLevel()

	if vip_level < item_lib_cfg.vip_level_limit then
		self.node_list.sellout_flag:SetActive(false)
		self.node_list.buy_btn:SetActive(false)
		self.node_list.limit_desc:SetActive(true)
		self.node_list.limit_desc.text.text = string.format(Language.OpertionAcitvity.XianShiMiaoSha.VipLevelLimitDesc, item_lib_cfg.vip_level_limit)
	elseif role_level < item_lib_cfg.level_limit then
		self.node_list.sellout_flag:SetActive(false)
		self.node_list.buy_btn:SetActive(false)
		self.node_list.limit_desc:SetActive(true)
		self.node_list.limit_desc.text.text = string.format(Language.OpertionAcitvity.XianShiMiaoSha.LevelLimitDesc, item_lib_cfg.level_limit)
	else
		local can_buy = self.data.last_num > 0 and self.data.bought_times < item_lib_cfg.buy_limit_value
		self.node_list.limit_desc:SetActive(false)
		self.node_list.buy_btn:SetActive(can_buy)
		-- XUI.SetButtonEnabled(self.node_list.buy_btn, can_buy)
		self.node_list.sellout_flag:SetActive(not can_buy)

		local interface_cfg = XianshiMiaoshaWGData.Instance:GetInterfaceCfg()
		if interface_cfg then
			self.node_list.buy_btn_text.text.text = can_buy and interface_cfg.desc_7 or interface_cfg.desc_10
		end
	end

	local interface_cfg = XianshiMiaoshaWGData.Instance:GetInterfaceCfg()

	if not interface_cfg then
		return
	end

	
	self.node_list.surplus_desc.text.text = string.format(interface_cfg.desc_3, self.data.last_num)
	self.node_list.discount_desc.text.text = string.format(interface_cfg.desc_2, item_lib_cfg.discount)
	self.node_list.surplus_desc:SetActive(item_lib_cfg.is_show == 1)
	self.node_list.limit_count_desc:SetActive(item_lib_cfg.buy_limit_type == 1 or item_lib_cfg.buy_limit_type == 5)

	local color = self.data.bought_times >= item_lib_cfg.buy_limit_value and COLOR3B.RED or COLOR3B.DEFAULT
	local str = string.format(interface_cfg.desc_4, self.data.bought_times, item_lib_cfg.buy_limit_value)

	self.node_list.limit_count_desc.text.text = ToColorStr(str, color)
	self.item_cell:SetBindIconVisible(false)
	-- self.item_cell:SetRightDownCellBgVisible(item_lib_cfg.buy_limit_type == 1 or item_lib_cfg.buy_limit_type == 5)

	-- local top_bg_pic = item_lib_cfg.is_show == 1 and interface_cfg.pic_7 or interface_cfg.pic_6
	-- self.node_list.top_bg.image:LoadSprite(ResPath.GetXianShiMiaoShaoPictrue(top_bg_pic))
	-- self.node_list.top_bg.image:SetNativeSize()

	self.node_list.top_bg:SetActive(item_lib_cfg.discount > 0)
end

function XianShiMiaoShaCell:OnClickBuyBtn()
	local select_type = XianshiMiaoshaWGData.Instance:GetCurSelectType()
	local xianshi_miaosha_data = XianshiMiaoshaWGData.Instance:GetXianShiMiaoShaInfoByType(select_type)

	if not xianshi_miaosha_data then
		return
	end

	local state = OperationActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.OPERA_ACT_XIANSHI_MIAOSHA)

	if not state then
		local interface_cfg = XianshiMiaoshaWGData.Instance:GetInterfaceCfg()

		if interface_cfg then
			TipWGCtrl.Instance:ShowSystemMsg(interface_cfg.desc_15)
		end

		return
	end

	local shop_lib_cfg = XianshiMiaoshaWGData.Instance:GetShopLibConfigByType(select_type, xianshi_miaosha_data.shop_id)

	if not shop_lib_cfg then
		return
	end
	ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.OPERA_ACT_XIANSHI_MIAOSHA, OA_TIMED_SPIKE_OPERA_TYPE.OA_TIMED_SPIKE_OPERA_TYPE_BUY, shop_lib_cfg.id, self.data.id)
end