require("game/lifetime_of_love/lifetime_of_love_view")
require("game/lifetime_of_love/lifetime_of_love_wg_data")

LifeTimeOfLoveWGCtrl = LifeTimeOfLoveWGCtrl or BaseClass(BaseWGCtrl)

function LifeTimeOfLoveWGCtrl:__init()
	if LifeTimeOfLoveWGCtrl.Instance then
		ErrorLog("[LifeTimeOfLoveWGCtrl] Attemp to create a singleton twice !")
	end

	LifeTimeOfLoveWGCtrl.Instance = self

    self.data = LifeTimeOfLoveWGData.New()
    self.view = LifeTimeOfLoveView.New(GuideModuleName.LifeTimeOfLoveView)

    self:RegisterAllProtocols()
end

function LifeTimeOfLoveWGCtrl:__delete()
    self.data:DeleteMe()
	self.data = nil

    self.view:DeleteMe()
	self.view = nil

    LifeTimeOfLoveWGCtrl.Instance = nil
end

function LifeTimeOfLoveWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSOAALifeLongLoveOperate)
	self:RegisterProtocol(SCAALifeLongLoveInfo, "OnSCAALifeLongLoveInfo")
end

--领取奖励请求
function LifeTimeOfLoveWGCtrl:SendGetRewardReq(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSOAALifeLongLoveOperate)
    protocol.operate_type = operate_type or 0
    protocol.param1 = param1 or 0
    protocol.param2 = param2 or 0
    protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function LifeTimeOfLoveWGCtrl:OnSCAALifeLongLoveInfo(protocol)
    self.data:SetAllInfo(protocol)
    if self.view:IsOpen() then
        self.view:Flush(nil, "update_task_info")
    end

	RemindManager.Instance:Fire(RemindName.LifeTimeOfLove)
end