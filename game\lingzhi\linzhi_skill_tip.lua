LingZhiSkillTips = LingZhiSkillTips or BaseClass(SafeBaseView)

LingZhiSkillTips.ViewType = {
    Normal = 1,
    BaseSkill = 2,
    MaxLevel = 3,
}

function LingZhiSkillTips:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/lingzhi_prefab", "layout_lz_skill_tip_view")
end

function LingZhiSkillTips:LoadCallBack()
    self.node_list.line_1:SetActive(false)
    self.node_list.line_2:SetActive(false)


end

function LingZhiSkillTips:ReleaseCallBack()

end

function LingZhiSkillTips:SetData(data)
    self.data = data
    self:Open()
    self:Flush()
end

function LingZhiSkillTips:OnFlush()
    if not self.data then
        return
    end

    local data = self.data
    if data.view_type == LingZhiSkillTips.ViewType.Normal then
        self:OnFlushNormal(data)
    elseif data.view_type == LingZhiSkillTips.ViewType.BaseSkill then
        self:OnFlushBaseSkill(data)
    elseif data.view_type == LingZhiSkillTips.ViewType.MaxLevel then
        self:OnFlushMaxLevel(data)
    else
        self:OnFlushNormal(data)
    end
end

function LingZhiSkillTips:OnFlushMaxLevel(data)
    local lingzhi_type = data.lingzhi_type
    local skill_level = data.max_level
    local cfg = data.max_cfg
    if IsEmptyTable(cfg) then return end
    local skill_index = cfg.skill_index
    local level_str = string.format(Language.LingZhi.LevelStr,cfg.skill_level)
    self.node_list.skill_name.text.text = cfg.skill_name or ""--ToColorStr(cfg.skill_name or "", ITEM_COLOR[cfg.skill_color]) .. "\n" .. level_str
    -- self.node_list.skill_name.text.text = ToColorStr(cfg.skill_name or "", ITEM_COLOR[cfg.skill_color]) .. "         " .. ToColorStr("Lv." .. cfg.skill_level, COLOR3B.GREEN)
    self.node_list.skill_level.text.text = level_str
    self.node_list.limit_text.text.text = LingZhiSkillWGData.Instance:GetSJSkillDes(cfg, "effect_dec_1",true)
    self.node_list["skill_dsc_cur"].text.text = Language.LingZhi.MaxLevelDescTitle
    self.node_list.limit_next_level:SetActive(false)
    self.node_list.limit_next_dsc:SetActive(false)

    local skill_icon_id = cfg.skill_icon_id or 1
    local name = 'a1_qh_skill_icon_' .. skill_icon_id
    self.node_list.skill_icon:SetActive(true)
    self.node_list.base_skill_icon:SetActive(false)
    self.node_list.skill_icon.image:LoadSprite(ResPath.GetLingZhiImg(name))

end

function LingZhiSkillTips:OnFlushBaseSkill(data)
    self.node_list.base_skill_icon:SetActive(true)
    self.node_list.skill_icon:SetActive(false)

    local lingzhi_type = data.lingzhi_type
    local base_skill_cfg = LingZhiSkillWGData.Instance:GetBaseSkillCfg(lingzhi_type)
    
    self.node_list.skill_name.text.text = base_skill_cfg.skill_name or ''
    self.node_list.limit_text.text.text = LingZhiSkillWGData.Instance:GetSJBaseSkillDes(base_skill_cfg,true)
    self.node_list["skill_dsc_cur"].text.text = Language.LingZhi.CurLevelDescTitle
    self.node_list.limit_next_level:SetActive(false)
    self.node_list.limit_next_dsc:SetActive(false)

    local server_info = LingZhiSkillWGData.Instance:GetSingleServerInfo(lingzhi_type)
    local color = LingZhiSkillWGData.Instance:GetSJBaseSkillColor(server_info)
    self.node_list.sj_base_skill_icon.image:LoadSprite(ResPath.GetSkillIconById(base_skill_cfg.skill_icon))
    -- self.node_list.sj_base_skill_bg.image:LoadSprite(ResPath.GetLingZhiImg("lz_skill_ground_" .. color))

end

function LingZhiSkillTips:OnFlushNormal(data)
    local cfg = data.cfg
    if IsEmptyTable(cfg) then return end
    local lingzhi_type = data.lingzhi_type
    local skill_index = data.skill_index
    local skill_level = cfg.skill_level
    local level_str = string.format(Language.LingZhi.LevelStr,cfg.skill_level)
    self.node_list.skill_name.text.text = cfg.skill_name or "" --ToColorStr(cfg.skill_name or "", ITEM_COLOR[cfg.skill_color]) .. "\n" .. level_str
    self.node_list["skill_dsc_cur"].text.text = Language.LingZhi.CurLevelDescTitle
    self.node_list.skill_level.text.text = level_str
    
    local nex_cfg = LingZhiSkillWGData.Instance:GetFwSkillUpLevelCfg(lingzhi_type, skill_index, skill_level)
    self.node_list.limit_next_level:SetActive(nex_cfg ~= nil)
    self.node_list.limit_next_dsc:SetActive(nex_cfg ~= nil)

    self.node_list.limit_text.text.text = LingZhiSkillWGData.Instance:GetSJSkillDes(cfg, "effect_dec_1",true)
    if nex_cfg then
        self.node_list.limit_next_dsc.text.text = LingZhiSkillWGData.Instance:GetSJSkillDes(nex_cfg, "effect_dec_1",true)
    end

    self.node_list.skill_icon:SetActive(true)
    self.node_list.base_skill_icon:SetActive(false)

    local skill_icon_id = cfg.skill_icon_id or 1
    local name = 'a1_lz_skill_icon_high_'..skill_icon_id
    self.node_list.skill_icon.image:LoadSprite(ResPath.GetLingZhiImg(name))
end