ControlBeastsContractLvUpView = ControlBeastsContractLvUpView or BaseClass(SafeBaseView)

function ControlBeastsContractLvUpView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/control_beasts_contract_ui_prefab", "control_beasts_contract_lv_up")
end

function ControlBeastsContractLvUpView:__delete()

end

function ControlBeastsContractLvUpView:ReleaseCallBack()
	if self.left_cell_list then
		for k,v in ipairs(self.left_cell_list) do
			v:DeleteMe()
		end
		self.left_cell_list = nil
    end

	if self.right_cell_list then
		for k,v in ipairs(self.right_cell_list) do
			v:DeleteMe()
		end
		self.right_cell_list = nil
    end
end

function ControlBeastsContractLvUpView:LoadCallBack()
	self.left_cell_list = {}
	for i = 1, 4 do
		self.left_cell_list[i] = ItemCell.New(self.node_list["left_contract_content"])
	end

	self.right_cell_list = {}
	for i = 1, 4 do
		self.right_cell_list[i] = ItemCell.New(self.node_list["right_contract_content"])
	end
end


function ControlBeastsContractLvUpView:OnFlush(param_t)
	local contract_level = ControlBeastsContractWGData.Instance:GetBeastDrawDeedLevel()
	self.node_list.left_contract_level_text.text.text = string.format(Language.ContralBeasts.ContrastCondition5, contract_level - 1)
	local beast_list, _ = ControlBeastsContractWGData.Instance:GetBeastRewardCfgByType(contract_level - 1)
	-- 刷新灵兽奖励
	if beast_list and self.left_cell_list then
		for i = 1, 4 do
			if self.left_cell_list[i] and beast_list[i] then
				self.left_cell_list[i]:SetData(beast_list[i])
			end
		end
	end

	self.node_list.right_contract_level_text.text.text = string.format(Language.ContralBeasts.ContrastCondition5, contract_level)
	local beast_list, _ = ControlBeastsContractWGData.Instance:GetBeastRewardCfgByType(contract_level)
	-- 刷新灵兽奖励
	if beast_list and self.right_cell_list then
		for i = 1, 4 do
			if self.right_cell_list[i] and beast_list[i] then
				self.right_cell_list[i]:SetData(beast_list[i])
			end
		end
	end
end