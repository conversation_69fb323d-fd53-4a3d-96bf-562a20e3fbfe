-- H-合服活动-烟花盛典.xls
local item_table={
[1]={item_id=50002,num=10,is_bind=1},
[2]={item_id=50002,num=45,is_bind=1},
[3]={item_id=46393,num=1,is_bind=1},
[4]={item_id=48121,num=1,is_bind=1},
[5]={item_id=48123,num=1,is_bind=1},
[6]={item_id=48118,num=1,is_bind=1},
[7]={item_id=26193,num=1,is_bind=1},
[8]={item_id=26191,num=1,is_bind=1},
[9]={item_id=27908,num=1,is_bind=1},
[10]={item_id=39105,num=1,is_bind=1},
[11]={item_id=28533,num=1,is_bind=1},
[12]={item_id=28545,num=1,is_bind=1},
[13]={item_id=50006,num=1,is_bind=1},
[14]={item_id=50005,num=1,is_bind=1},
[15]={item_id=28530,num=1,is_bind=1},
[16]={item_id=28531,num=1,is_bind=1},
[17]={item_id=28532,num=1,is_bind=1},
[18]={item_id=22009,num=1,is_bind=1},
[19]={item_id=45017,num=1,is_bind=1},
[20]={item_id=48106,num=1,is_bind=1},
[21]={item_id=30424,num=1,is_bind=1},
[22]={item_id=28510,num=1,is_bind=1},
[23]={item_id=28490,num=1,is_bind=1},
[24]={item_id=28470,num=1,is_bind=1},
[25]={item_id=47106,num=1,is_bind=1},
[26]={item_id=47098,num=1,is_bind=1},
[27]={item_id=57660,num=1,is_bind=1},
[28]={item_id=57661,num=1,is_bind=1},
[29]={item_id=57662,num=1,is_bind=1},
[30]={item_id=57600,num=1,is_bind=1},
[31]={item_id=57620,num=1,is_bind=1},
[32]={item_id=57640,num=1,is_bind=1},
[33]={item_id=48122,num=1,is_bind=1},
[34]={item_id=48124,num=1,is_bind=1},
[35]={item_id=57663,num=1,is_bind=1},
[36]={item_id=57675,num=1,is_bind=1},
[37]={item_id=48413,num=1,is_bind=1},
[38]={item_id=27860,num=1,is_bind=1},
[39]={item_id=27861,num=1,is_bind=1},
[40]={item_id=26504,num=1,is_bind=1},
[41]={item_id=26519,num=1,is_bind=1},
[42]={item_id=48088,num=1,is_bind=1},
[43]={item_id=48096,num=1,is_bind=1},
[44]={item_id=39105,num=80,is_bind=1},
[45]={item_id=26520,num=1,is_bind=1},
[46]={item_id=26194,num=1,is_bind=1},
[47]={item_id=26505,num=1,is_bind=1},
[48]={item_id=39105,num=100,is_bind=1},
[49]={item_id=26685,num=1,is_bind=1},
[50]={item_id=26670,num=1,is_bind=1},
[51]={item_id=26669,num=1,is_bind=1},
[52]={item_id=26684,num=1,is_bind=1},
[53]={item_id=50002,num=1,is_bind=1},
[54]={item_id=27805,num=1,is_bind=1},
}

return {
config_param={
{},
{start_server_day=21,end_server_day=40,grade=1,},
{start_server_day=41,end_server_day=60,grade=2,},
{start_server_day=61,end_server_day=999,grade=3,}
},

config_param_meta_table_map={
},
grade={
{},
{grade=1,consume=2,reward_bind=2,reward_unbind=2,rebate=2,model_item=37715,},
{grade=2,consume=3,reward_bind=3,reward_unbind=3,rebate=3,model_item=37716,},
{grade=3,consume=4,reward_bind=4,reward_unbind=4,rebate=4,model_item=37717,}
},

grade_meta_table_map={
},
consume={
{},
{onekey_lotto_num=10,consume_count=600,yanhua_item=item_table[1],yanhua_exit_time=6,},
{onekey_lotto_num=50,consume_count=2700,yanhua_item=item_table[2],yanhua_exit_time=8,discount_text=9,},
{consume=2,},
{consume=2,},
{consume=2,},
{consume=3,},
{consume=3,},
{consume=3,},
{consume=4,},
{consume=4,},
{consume=4,}
},

consume_meta_table_map={
[5]=2,	-- depth:1
[8]=5,	-- depth:2
[11]=8,	-- depth:3
[6]=3,	-- depth:1
[9]=6,	-- depth:2
[12]=9,	-- depth:3
},
reward={
{reward_type=1,player_guarantee=6100,broadcast=1,reward_show=1,rewrad_rare_show=0.001,},
{reward_id=2,reward_item=item_table[3],player_guarantee=4050,},
{reward_id=3,reward_item=item_table[4],},
{reward_id=4,reward_item=item_table[5],player_guarantee=1000,rewrad_rare_show=0.002,},
{reward_id=5,reward_item=item_table[6],broadcast=1,},
{reward_id=6,reward_item=item_table[7],rewrad_rare_show=0.005,},
{reward_id=7,reward_item=item_table[8],reward_type=2,reward_show=2,},
{reward_id=8,reward_item=item_table[9],rewrad_rare_show=0.09,},
{reward_id=9,reward_item=item_table[10],rewrad_rare_show=0.15,},
{reward_id=10,reward_item=item_table[11],rewrad_rare_show=0.08,},
{reward_id=11,reward_item=item_table[12],},
{reward_id=12,reward_item=item_table[13],rewrad_rare_show=0.1,},
{reward_id=13,reward_item=item_table[14],},
{reward_id=14,reward_item=item_table[15],rewrad_rare_show=0.02,},
{reward_id=15,reward_item=item_table[16],},
{reward_id=16,reward_item=item_table[17],rewrad_rare_show=0.03,},
{reward_id=17,reward_item=item_table[18],rewrad_rare_show=0.05,},
{reward_id=18,reward_item=item_table[19],broadcast=1,rewrad_rare_show=0.005,},
{reward_id=19,reward_item=item_table[20],},
{reward_id=20,reward_item=item_table[21],rewrad_rare_show=0.85,},
{reward_id=21,reward_item=item_table[22],broadcast=1,},
{reward_id=22,reward_item=item_table[23],reward_show=0,rewrad_rare_show=0.04,},
{reward_id=23,reward_item=item_table[24],reward_show=0,rewrad_rare_show=0.1,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,reward_item=item_table[25],},
{reward_id=11,reward_item=item_table[26],},
{reward=2,},
{reward=2,},
{reward=2,reward_item=item_table[27],},
{reward_id=15,reward_item=item_table[28],},
{reward=2,reward_item=item_table[29],},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,reward_item=item_table[30],},
{reward=2,reward_item=item_table[31],},
{reward=2,reward_item=item_table[32],},
{reward=3,},
{reward=3,},
{reward=3,reward_item=item_table[33],},
{reward=3,reward_item=item_table[34],},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,reward_item=item_table[35],},
{reward=4,reward_item=item_table[36],},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,reward_item=item_table[37],},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,}
},

reward_meta_table_map={
[15]=16,	-- depth:1
[13]=17,	-- depth:1
[11]=10,	-- depth:1
[77]=8,	-- depth:1
[78]=9,	-- depth:1
[40]=17,	-- depth:1
[39]=16,	-- depth:1
[38]=39,	-- depth:2
[37]=14,	-- depth:1
[43]=20,	-- depth:1
[44]=21,	-- depth:1
[67]=21,	-- depth:1
[31]=77,	-- depth:2
[63]=40,	-- depth:2
[62]=16,	-- depth:1
[36]=13,	-- depth:2
[54]=8,	-- depth:1
[55]=9,	-- depth:1
[56]=10,	-- depth:1
[57]=11,	-- depth:2
[58]=12,	-- depth:1
[59]=36,	-- depth:3
[66]=43,	-- depth:2
[35]=58,	-- depth:2
[61]=15,	-- depth:2
[33]=10,	-- depth:1
[90]=44,	-- depth:2
[89]=66,	-- depth:3
[86]=63,	-- depth:3
[19]=18,	-- depth:1
[34]=33,	-- depth:2
[81]=35,	-- depth:3
[32]=78,	-- depth:2
[60]=14,	-- depth:1
[84]=38,	-- depth:3
[83]=37,	-- depth:2
[82]=59,	-- depth:4
[85]=39,	-- depth:2
[80]=34,	-- depth:3
[79]=33,	-- depth:2
[88]=19,	-- depth:2
[87]=18,	-- depth:1
[76]=7,	-- depth:1
[65]=19,	-- depth:2
[68]=22,	-- depth:1
[69]=23,	-- depth:1
[64]=87,	-- depth:2
[46]=23,	-- depth:1
[42]=88,	-- depth:3
[5]=7,	-- depth:1
[30]=76,	-- depth:2
[41]=64,	-- depth:3
[53]=30,	-- depth:3
[45]=22,	-- depth:1
[92]=46,	-- depth:2
[91]=45,	-- depth:2
[70]=1,	-- depth:1
[51]=5,	-- depth:2
[6]=5,	-- depth:2
[24]=70,	-- depth:2
[47]=24,	-- depth:3
[28]=51,	-- depth:3
[74]=28,	-- depth:4
[29]=6,	-- depth:3
[4]=1,	-- depth:1
[3]=4,	-- depth:2
[2]=1,	-- depth:1
[75]=29,	-- depth:4
[52]=75,	-- depth:5
[48]=2,	-- depth:2
[49]=3,	-- depth:3
[50]=4,	-- depth:2
[27]=50,	-- depth:3
[26]=49,	-- depth:4
[25]=48,	-- depth:3
[73]=27,	-- depth:4
[72]=26,	-- depth:5
[71]=25,	-- depth:4
},
rebate={
{},
{index=2,lotto_num=30,},
{index=3,lotto_num=50,reward_item={[0]=item_table[38]},},
{index=4,lotto_num=70,},
{index=5,lotto_num=90,reward_item={[0]=item_table[39]},},
{index=6,lotto_num=110,},
{index=7,lotto_num=140,reward_item={[0]=item_table[40]},},
{index=8,lotto_num=180,},
{index=9,lotto_num=220,reward_item={[0]=item_table[41]},},
{index=10,lotto_num=260,},
{index=11,lotto_num=300,reward_item={[0]=item_table[42]},},
{index=12,lotto_num=340,},
{index=13,lotto_num=380,reward_item={[0]=item_table[43]},},
{index=14,lotto_num=420,reward_item={[0]=item_table[44]},},
{index=15,lotto_num=460,reward_item={[0]=item_table[45]},},
{index=16,lotto_num=500,reward_item={[0]=item_table[46]},},
{index=17,lotto_num=550,reward_item={[0]=item_table[47]},},
{index=18,lotto_num=600,reward_item={[0]=item_table[48]},},
{index=19,lotto_num=650,reward_item={[0]=item_table[7]},},
{index=20,lotto_num=700,},
{index=21,lotto_num=750,reward_item={[0]=item_table[19]},},
{index=22,lotto_num=800,},
{index=23,lotto_num=850,},
{index=24,lotto_num=900,},
{index=25,lotto_num=950,},
{index=26,lotto_num=1000,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=3,},
{rebate=3,},
{rebate=3,},
{rebate=3,},
{rebate=3,},
{rebate=3,},
{rebate=3,},
{rebate=3,},
{rebate=3,},
{rebate=3,},
{rebate=3,},
{rebate=3,},
{rebate=3,},
{rebate=3,},
{rebate=3,},
{rebate=3,},
{rebate=3,},
{rebate=3,},
{rebate=3,},
{rebate=3,},
{rebate=3,},
{rebate=3,},
{rebate=3,},
{rebate=3,reward_item={[0]=item_table[49]},},
{rebate=3,},
{rebate=3,reward_item={[0]=item_table[50]},},
{rebate=4,},
{rebate=4,},
{rebate=4,},
{rebate=4,},
{rebate=4,},
{rebate=4,},
{rebate=4,reward_item={[0]=item_table[51]},},
{rebate=4,},
{rebate=4,reward_item={[0]=item_table[52]},},
{rebate=4,},
{rebate=4,},
{rebate=4,},
{rebate=4,},
{rebate=4,},
{rebate=4,reward_item={[0]=item_table[49]},},
{rebate=4,},
{rebate=4,reward_item={[0]=item_table[50]},},
{rebate=4,},
{rebate=4,},
{rebate=4,},
{rebate=4,},
{rebate=4,},
{rebate=4,},
{rebate=4,},
{rebate=4,},
{rebate=4,}
},

rebate_meta_table_map={
[74]=22,	-- depth:1
[38]=12,	-- depth:1
[46]=20,	-- depth:1
[90]=38,	-- depth:2
[48]=74,	-- depth:2
[88]=10,	-- depth:1
[98]=46,	-- depth:2
[56]=4,	-- depth:1
[54]=2,	-- depth:1
[34]=8,	-- depth:1
[84]=6,	-- depth:1
[58]=84,	-- depth:2
[60]=34,	-- depth:2
[82]=56,	-- depth:2
[62]=88,	-- depth:2
[64]=90,	-- depth:3
[80]=54,	-- depth:2
[86]=60,	-- depth:3
[100]=48,	-- depth:3
[36]=62,	-- depth:3
[32]=58,	-- depth:3
[23]=19,	-- depth:1
[72]=98,	-- depth:3
[25]=21,	-- depth:1
[30]=82,	-- depth:3
[28]=80,	-- depth:3
[24]=15,	-- depth:1
[26]=17,	-- depth:1
[89]=11,	-- depth:1
[75]=23,	-- depth:2
[76]=24,	-- depth:2
[77]=25,	-- depth:2
[78]=26,	-- depth:2
[99]=21,	-- depth:1
[102]=76,	-- depth:3
[81]=3,	-- depth:1
[97]=19,	-- depth:1
[96]=18,	-- depth:1
[95]=17,	-- depth:1
[94]=16,	-- depth:1
[85]=7,	-- depth:1
[93]=15,	-- depth:1
[92]=14,	-- depth:1
[91]=13,	-- depth:1
[87]=9,	-- depth:1
[83]=5,	-- depth:1
[101]=75,	-- depth:3
[52]=78,	-- depth:3
[71]=97,	-- depth:2
[29]=81,	-- depth:2
[31]=83,	-- depth:2
[33]=85,	-- depth:2
[35]=87,	-- depth:2
[37]=89,	-- depth:2
[39]=91,	-- depth:2
[40]=92,	-- depth:2
[41]=93,	-- depth:2
[42]=94,	-- depth:2
[43]=95,	-- depth:2
[44]=96,	-- depth:2
[45]=71,	-- depth:3
[47]=99,	-- depth:2
[73]=47,	-- depth:3
[49]=101,	-- depth:4
[51]=77,	-- depth:3
[103]=51,	-- depth:4
[55]=29,	-- depth:3
[57]=31,	-- depth:3
[59]=33,	-- depth:3
[61]=35,	-- depth:3
[63]=37,	-- depth:3
[65]=39,	-- depth:3
[66]=40,	-- depth:3
[67]=41,	-- depth:3
[68]=42,	-- depth:3
[69]=43,	-- depth:3
[70]=44,	-- depth:3
[50]=102,	-- depth:4
[104]=52,	-- depth:4
},
role_sp_guarantee={
{},
{sp_player_guarantee_id=2,reward_id=2,sp_guarantee_weight="1,2,0|3,3,2|4,4,80|5,5,20",},
{sp_player_guarantee_id=3,reward_id=3,sp_guarantee_weight="1,1,0|2,2,80|3,3,20|4,4,20|5,5,20",},
{sp_player_guarantee_id=4,reward_id=4,sp_guarantee_weight="1,1,0|2,2,20|3,3,80|4,5,20",},
{sp_player_guarantee_id=5,reward_id=5,sp_guarantee_weight="1,1,100|2,5,20",},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,}
},

role_sp_guarantee_meta_table_map={
[18]=3,	-- depth:1
[17]=2,	-- depth:1
[15]=5,	-- depth:1
[14]=4,	-- depth:1
[10]=15,	-- depth:2
[12]=17,	-- depth:2
[19]=14,	-- depth:2
[9]=19,	-- depth:3
[8]=18,	-- depth:2
[7]=12,	-- depth:3
[13]=8,	-- depth:3
[20]=10,	-- depth:3
},
yanhua_show={
{gather_id_l=124,},
{grade=1,},
{grade=2,},
{grade=3,}
},

yanhua_show_meta_table_map={
},
config_param_default_table={start_server_day=1,end_server_day=20,grade=0,open_level=100,},

grade_default_table={grade=0,consume=1,reward_bind=1,reward_unbind=1,sp_guarantee_x=250,sp_guarantee_n=0,sp_guarantee_finish=0,rebate=1,model_item=37714,tip_desc="扭蛋中的奖励概率：",tip_title="狂欢扭蛋",player_guarantee=200,},

consume_default_table={consume=1,onekey_lotto_num=1,consume_count=60,yanhua_item=item_table[53],yanhua_exit_time=4,discount_text="",},

reward_default_table={reward=1,reward_id=1,reward_item=item_table[54],reward_type=3,player_guarantee=0,broadcast=0,reward_show=3,rewrad_rare_show=0.01,},

rebate_default_table={rebate=1,index=1,lotto_num=10,reward_item={[0]=item_table[1]},rebate_icon=1,},

role_sp_guarantee_default_table={reward=1,sp_player_guarantee_id=1,reward_id=1,sp_guarantee_weight="1,3,0|4,4,2|5,5,80",show_icon=1,},

yanhua_show_default_table={grade=0,yanhua_show_time="20:15|21:15|22:15|23:15",yanhua_show_duration=600,scene_id=1003,yanhua_show_pos="382,273",yanhua_show_range=60,yanhua_pos_s=382273,yanhua_s_eff=10001,yanhua_interval_s=15,gather_pos_s=382273,gather_id_s=124,gather_drop_id_s=10370,gather_time_s=2,gather_flush_num_s=13,gather_flush_num_max_s=20,gather_day_reward_num_s=10,yanhua_pos_l=382273,yanhua_l_eff=10002,yanhua_interval_l=30,gather_pos_l=382273,gather_id_l=339,gather_drop_id_l=10370,gather_time_l=10,gather_flush_num_l=4,gather_flush_num_max_l=10,gather_day_reward_num_l=3,findway_pos="360,283",gather_level_limit=100,}

}

