JinyintaWGData = JinyintaWGData or BaseClass()

function JinyintaWGData:__init()
	if JinyintaWGData.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[JinyintaWGData] attempt to create singleton twice!")
		return
	end
	JinyintaWGData.Instance = self
	self.lottery_cur_level = 0
	self.history_list = {}
	self.draw_count = 1
	self.list = {}
end

function JinyintaWGData:__delete()
	JinyintaWGData.Instance = nil
end

function JinyintaWGData:GetShowItems()
	local reward_cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig().level_lottery_reward or {}
	local rand_t = ServerActivityWGData.Instance:GetRandActivityConfig(reward_cfg, ACTIVITY_TYPE.RAND_JINYINTA)
	local list = {}
	for k,v in ipairs(rand_t) do
		local layer = v.level + 1
		list[layer] = list[layer] or {}
		table.insert(list[layer], v.reward_item)
	end
	return list
end

function JinyintaWGData:GetLotterykeyID()
	local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local rand_t = rand_config.other
	local other_cfg = ServerActivityWGData.Instance:GetRandActivityConfig(rand_t, ACTIVITY_TYPE.RAND_JINYINTA)
	return other_cfg[1].level_lottery_key_item_id
end

function JinyintaWGData:GetOneShowItem(index)
	local reward_cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig().level_lottery_reward or {}
	local rand_t = ServerActivityWGData.Instance:GetRandActivityConfig(reward_cfg, ACTIVITY_TYPE.RAND_JINYINTA)
	for k,v in ipairs(rand_t) do
		if v.reward_index == index then
			return v.reward_item
		end
	end
	return nil
end

function JinyintaWGData:GetKeyByLevel(level)
	local consume_cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig().level_lottery_consume or {}
	for k,v in pairs(consume_cfg) do
		if v.level == level then
			return v.lottery_consume_key
		end
	end
	return 0
end

function JinyintaWGData:GetOneDrawCost(level)
	local consume_cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig().level_lottery_consume or {}
	for k,v in pairs(consume_cfg) do
		if v.level == level then
			return v.lottery_consume_gold
		end
	end
	return 0
end

function JinyintaWGData:SetCurLayer(lottery_cur_level)
	self.lottery_cur_level = lottery_cur_level
end

function JinyintaWGData:GetCurLayer()
	return self.lottery_cur_level
end

function JinyintaWGData:SetHistoryList(history_list)
	self.history_list = history_list
end

function JinyintaWGData:GetHistoryList()
	return self.history_list
end

function JinyintaWGData:SetDrawCount(count)
	self.draw_count = count
end

function JinyintaWGData:GetDrawCount()
	return self.draw_count
end

function JinyintaWGData:GetOnlineTime()
	return self.online_time or 0
end

function JinyintaWGData:SetFreeTimes(freetimes)
	self.freetimes = freetimes or 0
end	

function JinyintaWGData:GetFreeTimes()
	return self.freetimes or 0
end	

function JinyintaWGData:SetNextFreeRemainTime(next_free_remain_time)
	self.next_free_remain_time = next_free_remain_time or 0
end	

function JinyintaWGData:GetNextFreeRemainTime()
	return self.next_free_remain_time or 0
end	

function JinyintaWGData:SetXunBaoScore(xunbao_score)
	self.xunbao_score = xunbao_score or 0
end	

function JinyintaWGData:GetXunBaoScore()
	return self.xunbao_score or 0
end	

function JinyintaWGData:SetXunBaoScoreCfg()
	local i = 0
	for k , v in pairs(ConfigManager.Instance:GetAutoConfig("xunbaoscore_auto").score_exchange) do
		if v.randact_type == ACTIVITY_TYPE.RAND_JINYINTA then
			i = i +1
			self.list[i] = v
		end	
	end
end

function JinyintaWGData:GetXunBaoScoreCfg()
	return self.list
end

function JinyintaWGData:GetJinYinTaRemind()
	local score_exchange = ConfigManager.Instance:GetAutoConfig("xunbaoscore_auto").score_exchange
	if score_exchange[1] ~= nil then
		return self:GetXunBaoScore() >= score_exchange[1].consume_score and 1 or 0
	end
	return 0
end