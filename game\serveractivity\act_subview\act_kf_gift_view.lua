---------------
-- 开服礼包
---------------
function ServerActivityTabView:KFGiftReleaseCallBack()

	if self.kf_gift_item_list then
		self.kf_gift_item_list:DeleteMe()
		self.kf_gift_item_list = nil
	end

	CountDownManager.Instance:RemoveCountDown("act_kf_gift_count_down")
end

function ServerActivityTabView:KFGiftLoadCallBack()
	ServerActivityWGData.Instance:SetActKfGiftRemind(0)
	self.kf_gift_select_index = 0
	self.kf_gift_default_index = 0

	self.kf_gift_item_list = AsyncListView.New(KFGiftItem, self.node_list.kf_gift_item_list)
	self.kf_gift_item_list:SetSelectCallBack(BindTool.Bind(self.KFGiftOnClickItem, self))

	self.node_list.kf_gift_item_list.scroll_rect.onValueChanged:AddListener(BindTool.Bind(self.OnValueChanged, self))

	XUI.AddClickEventListener(self.node_list.btn_left, BindTool.Bind(self.Jump, self, true))
	XUI.AddClickEventListener(self.node_list.btn_right, BindTool.Bind(self.Jump, self, false))

	
	self:KFGiftFlushItemList()
end

function ServerActivityTabView:Jump(is_left)
	local pos_x = self.node_list.kf_gift_item_list.scroll_rect.horizontalNormalizedPosition

	local all_count = self.kf_gift_item_list:GetListViewNumbers()

	if all_count > 2 then
		local width = (1/(all_count - 3))
		if is_left then
			if pos_x - width*3 <= 0 then
				pos_x = 0
			else
				pos_x = pos_x - width*3
			end
			self.node_list.kf_gift_item_list.scroll_rect.horizontalNormalizedPosition = pos_x
		else
			if pos_x + width*3 >= 1 then
				pos_x = 1
			else
				pos_x = pos_x + width*3
			end
			self.node_list.kf_gift_item_list.scroll_rect.horizontalNormalizedPosition = pos_x
		end
	end
	
end

function ServerActivityTabView:OnValueChanged(pos)
	-- pos范围0-1
	-- local all_count = self.kf_gift_item_list:GetListViewNumbers()
	-- if all_count == 0 then
	-- 	return
	-- end
	-- all_count = all_count - 2

	-- local index = math.ceil(all_count * pos.x)
	
	-- self.kf_gift_item_list:SelectIndex(index)
	local all_count = self.kf_gift_item_list:GetListViewNumbers()
	local width = (1/(all_count - 2))
	local val = self.node_list.kf_gift_item_list.scroll_rect.horizontalNormalizedPosition
	self.node_list.btn_left:SetActive(val ~= 0 and val >= width)
	self.node_list.btn_right:SetActive(val <= (1-width))

end

function ServerActivityTabView:GiftShowIndexCallBack()
	self.kf_gift_item_list:RefreshActiveCellViews()
	self.node_list.kf_gift_item_list.scroll_rect.horizontalNormalizedPosition = 0
end

function ServerActivityTabView:KFGiftOnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "kf_gift" then
			self:KFGiftFlushItemList()
		elseif k == "all" then
			if v.libao_index then
				self.kf_gift_default_index = v.libao_index
				self:CounterPosX()
				-- self:KFGiftFlushSelectIndex()
			end
		end
	end
end


function ServerActivityTabView:KFGiftFlushItemList()
	

	if ServerActivityWGData.Instance.is_test_model then -- 测试用给策划看所有的礼包模型
		local data_list = ServerActivityWGData.Instance:GetTestActKfGiftCfg()
		self.kf_gift_item_list:SetDataList(data_list)
	else
		local temp_list = self:CounterPosX()
		self.kf_gift_item_list:SetDataList(temp_list)
	end


	-- self:KFGiftFlushSelectIndex()
end

-- 礼包排序
function ServerActivityTabView:CounterPosX()
	local data_list = ServerActivityWGData.Instance:GetActKfGiftInfoList()
	local now_time = TimeWGCtrl.Instance:GetServerTime()

	---[[ 今日推荐排前面(24小时内上架的新礼包)
	local sort_list = {}
	for k,v in pairs(data_list) do
		if v.put_libao_timestamp + 24 * 3600 < now_time then
			sort_list[200 + k] = v
		else
			sort_list[k] = v
		end
	end
	sort_list = SortTableKey(sort_list)
	--]]

	---[[ 过期的 卖完的 排后面
	local temp_list = {}
	for k,v in pairs(sort_list) do
		if v.pass_time <= now_time then
			temp_list[200 + k] = v
		elseif v.can_buy_num <= 0 then
			temp_list[300 + k] = v
		else
			temp_list[k] = v
		end
	end
	temp_list = SortTableKey(temp_list)
	
	--]]
	if self.kf_gift_default_index ~= 0 then
		local jump_index = 0 
		self.jump_index = 0
		for key, value in pairs(temp_list) do
			self.all_count = jump_index
			jump_index = jump_index + 1
			if self.jump_index == 0 and value.libao_index == self.kf_gift_default_index then
				self.jump_index = jump_index
			end
		end
	end

	return temp_list
end

-- function ServerActivityTabView:KFGiftFlushSelectIndex()
-- 	if self.jump_index and self.jump_index ~= 0 and self.all_count~=nil then
-- 		if self.all_count > 0 then
-- 			local pos_x = (self.jump_index-1) / self.all_count
-- 			self.kf_gift_item_list:JumptToPrecent(pos_x)
-- 			self.jump_index = 0
-- 		end
-- 	end
	
-- end

function ServerActivityTabView:KFGiftOnClickItem(item, cell_index, is_default, is_click)
	local data = item:GetData()
	if IsEmptyTable(data) then
		return
	end

	if is_click then
		self.kf_gift_default_index = 0
	end

	if self.kf_gift_select_index == data.libao_index and is_click then
		return
	end
	self.kf_gift_select_index = data.libao_index

end



------------------------------------------------------------------------------------------

KFGiftItem = KFGiftItem or BaseClass(BaseRender)

function KFGiftItem:__init()
	self.kf_gift_reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)

	if self.kf_gift_reward_list2 == nil then
		local bundle, asset = "uis/view/open_server_activity_ui_prefab", "reward_item"
		self.kf_gift_reward_list2 = AsyncBaseGrid.New()
		self.kf_gift_reward_list2:CreateCells({
			col = 3,
			change_cells_num = 1,
			list_view = self.node_list.reward_list_2,
			assetBundle = bundle,
			assetName = asset,
			itemRender = ActGiftItemCellRender
		})
		self.kf_gift_reward_list2:SetStartZeroIndex(false)
	end

	-- self.kf_gift_reward_list2 = AsyncListView.New(ItemCell, self.node_list.reward_list_2)

	XUI.AddClickEventListener(self.node_list.buy_btn, BindTool.Bind(self.KFGiftOnClickBuyBtn, self))
end

function KFGiftItem:__delete()
	self:CleanTimer1()

	if self.kf_gift_reward_list then
		self.kf_gift_reward_list:DeleteMe()
		self.kf_gift_reward_list = nil
	end

	if self.kf_gift_reward_list2 then
		self.kf_gift_reward_list2:DeleteMe()
		self.kf_gift_reward_list2 = nil
	end
end

function KFGiftItem:OnFlush()
	local data = self:GetData()

	if nil == data then
		self.node_list.item_root:SetActive(false)
		return
	end
	self.node_list.item_root:SetActive(true)
	local gift_cfg = ServerActivityWGData.Instance:GetActKfGiftCfg(data.libao_index)
	if not gift_cfg then
		return
	end

	-- 价格
	self.node_list.buy_btn_lbl.text.text = gift_cfg.price or ""
	local show_text_num, show_txt = CommonDataManager.ConverMoneyBar(gift_cfg.old_price)
	self.node_list.old_price_label.text.text = show_text_num .. show_txt
	-- 折扣
	self.node_list.text_sale.text.text = gift_cfg.discount

	self.node_list.text_title.text.text = gift_cfg.gift_name
	self.node_list.text_title_2.text.text = gift_cfg.gift_name

	if data.buy_num >= gift_cfg.num then
	else
		self:FlushGiftTimer()
	end

	---[[ 按钮状态
	local pass_time = gift_cfg.dur_times * 60 + data.put_libao_timestamp
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	local count_down_time = pass_time - now_time

	self.node_list.img_bg_1:SetActive(gift_cfg.num > data.buy_num and count_down_time > 0)
	self.node_list.img_bg_2:SetActive(not (gift_cfg.num > data.buy_num and count_down_time > 0))



	self.node_list.flag_group:SetActive(gift_cfg.num <= data.buy_num or count_down_time <= 0)
	self.node_list.common_flag_dark_red:SetActive(gift_cfg.num <= data.buy_num)
	self.node_list.common_flag_dark_red2:SetActive(count_down_time <= 0)
	-- self.node_list.kf_gift_all_sell_img:SetActive(gift_cfg.num <= data.buy_num)
	self.node_list.buy_btn:SetActive(gift_cfg.num > data.buy_num and count_down_time > 0)
	self.node_list.old_group:SetActive(gift_cfg.num > data.buy_num and count_down_time > 0)
	-- 礼包剩余时间
	self.node_list.text_time:SetActive(gift_cfg.num > data.buy_num)
	--]]

	self:KFGiftFlushRewardList(gift_cfg)
	--]]

end


function KFGiftItem:FlushGiftTimer()
	self:CleanTimer1()

	local data = self:GetData()
	local now_time = TimeWGCtrl.Instance:GetServerTime()

	if data.pass_time > now_time then

		self.timer_1 = CountDown.Instance:AddCountDown(data.pass_time - now_time, 1,
        	-- 回调方法
        	function(elapse_time, total_time)
				self.node_list.text_time.text.text = string.format(Language.OpenServer.ActRemainTime3,TimeUtil.FormatSecondDHM8(total_time - elapse_time)) 
        	end,
        	-- 倒计时完成回调方法
        	function()
				self:Flush()
        	end
    	)

		
    else

	end
end

-- 清除倒计时器1
function KFGiftItem:CleanTimer1()
    if self.timer_1 and CountDown.Instance:HasCountDown(self.timer_1) then
        CountDown.Instance:RemoveCountDown(self.timer_1)
        self.timer_1 = nil
    end
end

function KFGiftItem:OnSelectChange(is_select)

end


function KFGiftItem:KFGiftFlushRewardList(gift_cfg)
	local reward = self:KFGiftGetRewardList(gift_cfg)
	self.node_list.reward_list_big:SetActive(#reward > 2)
	self.node_list.reward_list_small:SetActive(#reward <= 2)
	if #reward > 2 then
		self.kf_gift_reward_list2:SetDataList(reward)
	else
		self.kf_gift_reward_list:SetDataList(reward)
	end

end


function KFGiftItem:KFGiftGetRewardList(gift_cfg)
	local reward = SortTableKey(gift_cfg.reward)
	if gift_cfg.is_split == 1 then
		local data_list = {}
		local item_cfg,item_type = nil, nil
		for i=1,#reward do
			item_cfg,item_type = ItemWGData.Instance:GetItemConfig(reward[i].item_id)
			if item_type == GameEnum.ITEM_BIGTYPE_GIF then
				local item_list = ItemWGData.Instance:GetGiftDropList(reward[i].item_id, 2)
				for j=1,#item_list do
					data_list[#data_list + 1] = item_list[j]
				end
			else
				data_list[#data_list + 1] = reward[i]
			end
		end
		return data_list
	else
		return reward
	end
end

function KFGiftItem:KFGiftOnClickBuyBtn()
	local gift_cfg = ServerActivityWGData.Instance:GetActKfGiftCfg(self.data.libao_index)
	if not gift_cfg then
		return
	end
	local text_desc = string.format(Language.OpenServer.DiscountBuyGift, gift_cfg.price, gift_cfg.gift_name)
	TipWGCtrl.Instance:OpenAlertTips(text_desc, function()
		ServerActivityWGCtrl.Instance:SendCSOgaTehuilibaoOpera(OGA_TEHUI_LIBAO_OPERA.OGA_TEHUI_LIBAO_OPERA_BUY, self.data.libao_index)
	end)
	
end


ActGiftItemCellRender = ActGiftItemCellRender or BaseClass(BaseRender)

function ActGiftItemCellRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.item_pos)
end


function ActGiftItemCellRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function ActGiftItemCellRender:OnFlush()
    if not self.data then
        return
    end
	self.item_cell:SetData(self.data)
end