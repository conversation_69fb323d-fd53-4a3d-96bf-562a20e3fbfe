--------------------------------------------------
-- 累充
--------------------------------------------------
DailyTotalRechargeView = DailyTotalRechargeView or BaseClass(SafeBaseView)

DailyTotalRechargeView.BtnState = {
	btn_name_ylq = 1,
	btn_name_lqjl = 2,
	btn_name_cdxq = 3
}

function DailyTotalRechargeView:__init()
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/rechargereward_ui_prefab", "layout_daily_total_recharge")
	self.cur_page = 1
end

function DailyTotalRechargeView:__delete()
	
end

function DailyTotalRechargeView:ReleaseCallBack()
	if self.item_cell_list then
		for k,v in pairs(self.item_cell_list) do
			v:DeleteMe()
		end
		self.item_cell_list = {}
	end

	if self.center_cell then
		self.center_cell:DeleteMe()
		self.center_cell = nil
	end

	if self.list_reward then
		self.list_reward:DeleteMe()
		self.list_reward = nil
	end

	self.toggle_remind = {}
	self.num_list = {}
	self.flag_hl = {}

	if self.global_day_change then
		GlobalEventSystem:UnBind(self.global_day_change)
		self.global_day_change = nil
	end
end

function DailyTotalRechargeView:LoadCallBack(index, loaded_times)
	self.item_cell_list = {}
	for i = 1, 6 do
		self.item_cell_list[i] = ItemCell.New(self.node_list["ph_item_" .. i])
	end

	self.center_cell = ItemCell.New(self.node_list.ph_cell_top)

	XUI.AddClickEventListener(self.node_list.layout_recharge_some, BindTool.Bind(self.OnClickRecharge, self))
	self.toggle_remind = {}
	self.flag_hl = {}
	self.num_list = {}
	for i=1,3 do
		self.node_list["flag" .. i].button:AddClickListener(BindTool.Bind(self.OnClickFlag, self,i))
		self.toggle_remind[i] = self.node_list["flag_red_" .. i]
		self.flag_hl[i] = self.node_list["flag_hl_" .. i]
		self.num_list[i] = self.node_list["num_" .. i]
	end

	self.list_reward = AchievementView.New(RechargeRewardDailyRender, self.node_list.ph_list_reward)

	self.global_day_change = GlobalEventSystem:Bind(OtherEventType.PASS_DAY, BindTool.Bind1(self.DayChange, self))
end

function DailyTotalRechargeView:OnClickFlag(i)
	self.cur_page = i
	self:FlushContent()
	self:FlushFlagHl()
end

function DailyTotalRechargeView:FlushFlagHl()
	for i=1,3 do
		self.flag_hl[i]:SetActive(i == self.cur_page)
		self.num_list[i]:SetActive(i == self.cur_page)
	end
end

function DailyTotalRechargeView:ShowIndexCallBack()
	self:Flush()
end

function DailyTotalRechargeView:FlushContent()
	self:RefreshItemSlot()
	self:RefreshBtnFetch()
end

function DailyTotalRechargeView:OnFlush()
	local chongzhi_data = ServerActivityWGData.Instance:GetTotalChongZhiData()
	if nil == chongzhi_data then return end
	local auto_index = nil
	for i = 1, 3 do
		local fetch_flag = RechargeRewardWGData.Instance:GetTotalRechargeFetchRewardFlag(i - 1)
		local need_gold = RechargeRewardWGData.Instance:GetDailyTotalRechargeReward(i).need_recharge_gold
		local remind_num = (chongzhi_data.today_recharge >= need_gold and fetch_flag ~= 1) and 1 or 0
		if nil == auto_index and remind_num == 1 then
			auto_index = i
		end
	end
	self:OnClickFlag(self.cur_page)
	self:RefreshRadioBtnTipsVisible()
	self:RefreshItemSlot()
	self:RefreshBtnFetch()
	self:RefreshListDayReward()
end

function DailyTotalRechargeView:SetBtnTitleImg(ptr_str)
	local bundle,asset = ResPath.GetRechargeReward(ptr_str)
	self.node_list["img_btn_name"].image:LoadSprite(bundle,asset,function()
		self.node_list["img_btn_name"].image:SetNativeSize()
	end)
end

function DailyTotalRechargeView:SetBtnEnabled(is_enabled)
	XUI.SetButtonEnabled(self.node_list["layout_recharge_some"], is_enabled)
end

-- -- 创建非卖标记
-- function DailyTotalRechargeView:CreateNotSellSign(item_cell_view)
-- 	if nil == item_cell_view.not_sell_sign then
-- 		local sign = XUI.CreateImageView(0, 0, ResPath.GetCommon("notsell"), true)
-- 		item_cell_view:addChild(sign, 99)
-- 		local cell_size = item_cell_view:getContentSize()
-- 		local sign_size = sign:getContentSize()

-- 		sign:setPosition(cell_size.height - sign_size.height/2, cell_size.width - sign_size.height/2)

-- 		item_cell_view.not_sell_sign = sign
-- 	end
-- end

function DailyTotalRechargeView:DayChange()
	if self:IsOpen() then
		self:RefreshRadioBtnTipsVisible()
		self:RefreshItemSlot()
		self:RefreshBtnFetch()
		self:RefreshListDayReward()

	end
end

-- 刷新可领取提示
function DailyTotalRechargeView:RefreshRadioBtnTipsVisible()
	local chongzhi_data = ServerActivityWGData.Instance:GetTotalChongZhiData()
	if nil == chongzhi_data then
		return
	end
	
	-- RechargeWGCtrl.Instance:UpdateDailyRechargeIcon()  --刷新红点

	for i = 1, 3 do
		local cfg = RechargeRewardWGData.Instance:GetDailyTotalRechargeReward(i)
		local fetch_flag = RechargeRewardWGData.Instance:GetTotalRechargeFetchRewardFlag(i - 1)
		local is_can_get = false
		if cfg then
			is_can_get = chongzhi_data.today_recharge >= cfg.need_recharge_gold and fetch_flag ~= 1
		end
		local remind_num = is_can_get and 1 or 0
		self.toggle_remind[i]:SetActive(remind_num > 0)
	end
end

--刷新物品格子
function DailyTotalRechargeView:RefreshItemSlot()
	local item_list = RechargeRewardWGData.Instance:GetDailyTotalRechargeReward(self.cur_page)
	if item_list == nil then return end
 
 	self.center_cell:SetData(item_list.reward_item[0])
	for i = 1, #self.item_cell_list do
		self.item_cell_list[i]:SetData(item_list.reward_item[i - 1])
	end
end

-- 刷新今日累计充值数
function DailyTotalRechargeView:RefreshLblTodayRecharge()
	local today_recharge = RechargeWGData.Instance:GetTodayRecharge()
end

-- 刷新领取奖励按钮
function DailyTotalRechargeView:RefreshBtnFetch()
	local chongzhi_data = ServerActivityWGData.Instance:GetTotalChongZhiData()
	if nil == chongzhi_data then
		return
	end

	local cfg = RechargeRewardWGData.Instance:GetDailyTotalRechargeReward(self.cur_page)
	local fetch_flag = RechargeRewardWGData.Instance:GetTotalRechargeFetchRewardFlag(self.cur_page - 1)

	local need_recharge = cfg and cfg.need_recharge_gold or 0
	if chongzhi_data.today_recharge >= need_recharge then
		self:SetBtnTitleImg(DailyTotalRechargeView.BtnState.btn_name_lqjl) -- 领取奖励
		if fetch_flag ~= 1 then
			self:SetBtnEnabled(true)
		else
			self:SetBtnTitleImg(DailyTotalRechargeView.BtnState.btn_name_ylq)
			self:SetBtnEnabled(false)
			return
		end
	else
		self:SetBtnEnabled(true)
		self:SetBtnTitleImg(DailyTotalRechargeView.BtnState.btn_name_cdxq) -- 充点小钱
	end
end

-- 刷新按天奖励列表
function DailyTotalRechargeView:RefreshListDayReward()
	local data_list = RechargeRewardWGData.Instance:GetDailyTotalRechargeRightReward()
	self.list_reward:SetDataList(data_list)
end

function DailyTotalRechargeView:OnClickRecharge()
	local today_recharge = RechargeWGData.Instance:GetTodayRecharge()
	local cfg = RechargeRewardWGData.Instance:GetDailyTotalRechargeReward(self.cur_page)
	local need_recharge = cfg and cfg.need_recharge_gold or 0

	if today_recharge >= need_recharge then
		ServerActivityWGCtrl.Instance:SendChongzhiFetchReward(RECHARGEREWARD_REQ_TYPE.DAILY_TOTAL_RECHARGE, self.cur_page - 1, 0)
	else
		FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge)
	end
end

-- function DailyTotalRechargeView:FlushCellShiftData()
-- 	local item_list = RechargeRewardWGData.Instance:GetCurDayDailyRechargeReward(2)
-- 	if item_list == nil then return end

-- 	local item_data = {}
-- 	if self.cell_shift_type == 1 then
-- 		item_data = item_list.reward_item1
-- 	elseif self.cell_shift_type == 2 then
-- 		item_data = item_list.reward_item2
-- 	end
-- 	self.cell_shift:SetData(item_data)
-- end


-- ItemRender --------------------------------------------------------------------------------------
RechargeRewardDailyRender = RechargeRewardDailyRender or BaseClass(BaseRender)
function RechargeRewardDailyRender:__init()

end

function RechargeRewardDailyRender:__delete()
	if self.cell_reward then
		self.cell_reward:DeleteMe()
		self.cell_reward = nil
	end

end

function RechargeRewardDailyRender:LoadCallBack()
	self.cell_reward = ItemCell.New(self.node_list.ph_cell_reward)
	XUI.AddClickEventListener(self.node_list.btn_fetch_daily, BindTool.Bind(self.OnClickBtnFetch, self))
end

function RechargeRewardDailyRender:OnFlush()
	if not self.data then return end
	local recharge_days = RechargeRewardWGData.Instance:GetTotalRechargeTotalDay()
	local days = recharge_days > 6 and 6 or recharge_days
	if self.data.reward_item then
		self.cell_reward:SetData(self.data.reward_item)
		self.node_list.image_daily__reward_title.text.text = string.format(Language.DailyRecharge.RechargeMoney,self.data.total_recharge_day,days,self.data.total_recharge_day,300)
		self.has_inited = true
	end
	local fetch_flag = self.data.fetch_flag
	if fetch_flag ~= nil then
		self.node_list.btn_text.text.text = fetch_flag < 1 and Language.Common.LingQu or Language.Common.YiLingQu
		XUI.SetButtonEnabled(self.node_list.btn_fetch_daily,fetch_flag == 0)
	end
end

function RechargeRewardDailyRender:OnClickBtnFetch()
	ServerActivityWGCtrl.Instance:SendChongzhiFetchReward(RECHARGEREWARD_REQ_TYPE.TOTAL_RECHARGE_DAY, self.index - 1)
end