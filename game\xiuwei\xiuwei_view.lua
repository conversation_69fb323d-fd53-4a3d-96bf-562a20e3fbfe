XiuWeiView = XiuWeiView or BaseClass(SafeBaseView)

local MAX_SMALL_STATTE_CELL_NUM = 5
local MAX_LEVEL_CELL_NUM = 5
local SmallStateCellPos = {
    [3] = {[1] = {x = -70, y = 12}, [2] = {x = 0, y = -15}, [3] = {x = 70, y = 12}},
    [4] = {[1] = {x = -70, y = 12}, [2] = {x = -26, y = -13}, [3] = {x = 26, y = -13}, [4] = {x = 70, y = 12}},
    [5] = {[1] = {x = -70, y = 12}, [2] = {x = -38, y = -9}, [3] = {x = 0, y = -15}, [4] = {x = 38, y = -9}, [5] = {x = 70, y = 12}},
}

function XiuWeiView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_top_panel")
	self:AddViewResource(0, "uis/view/xiuwei_ui_prefab", "layout_xiuwei_view")
	self:SetTabShowUIScene(0, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.XIUWEI})
end

function XiuWeiView:LoadCallBack()
    self.top_part_x, self.top_part_y = RectTransform.GetAnchoredPositionXY(self.node_list.top_part.rect)
    self.bottom_part_x, self.bottom_part_y = RectTransform.GetAnchoredPositionXY(self.node_list.bottom_part.rect)
    self.left_part_x, self.left_part_y = RectTransform.GetAnchoredPositionXY(self.node_list.left_part.rect)
    self.right_part_x, self.right_part_y = RectTransform.GetAnchoredPositionXY(self.node_list.right_part.rect)
    self.cur_show_num = 0
    self.node_list.title_view_name.text.text = Language.XiuWei.ViewName

    if not self.small_state_list then
        self.small_state_list = {}
        for i = 1, MAX_SMALL_STATTE_CELL_NUM do
            self.small_state_list[i] = XiuWeiSmallStatelCell.New(self.node_list["small_state_cell" .. i])
            self.small_state_list[i]:SetIndex(i)
        end
    end

    if not self.level_cell_list then
        self.level_cell_list = {}
        for i = 1, MAX_LEVEL_CELL_NUM do
            self.level_cell_list[i] = XiuWeiLevelCell.New(self.node_list.level_cell_list:FindObj("level_cell" .. i))
            self.level_cell_list[i]:SetIndex(i)
            self.level_cell_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickLevelCellCallBack, self))
        end
    end

    if not self.limit_list then
        self.limit_list = AsyncListView.New(XiuWeiLimitCell, self.node_list.limit_list)
        self.limit_list:SetStartZeroIndex(true)
    end

    if not self.attr_list then
        self.attr_list = AsyncListView.New(XiuWeiAttrRender, self.node_list.attr_list)
    end

    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(true)
    end

    if not self.luck_item then
        self.luck_item = XiuWeiLuckItem.New(self.node_list.luck_item)
        self.luck_item:SetClickCallBack(BindTool.Bind(self.OnClickLuckItemCallBack, self))
    end

    XUI.AddClickEventListener(self.node_list.goto_xianfa_btn, BindTool.Bind(self.OnClickJumpBtn, self, GuideModuleName.EsotericaView))
    XUI.AddClickEventListener(self.node_list.goto_xiuxianshilian_btn, BindTool.Bind(self.OnClickJumpBtn, self, GuideModuleName.FuBenPanel, TabIndex.fubenpanel_welkin))
    XUI.AddClickEventListener(self.node_list.open_danyao_panel_btn, BindTool.Bind(self.OnClickOpenDanYaoPanelBtn, self))
    XUI.AddClickEventListener(self.node_list.jump_xs_btn, BindTool.Bind(self.OnClickJumpBtn, self, GuideModuleName.BiZuo))
    XUI.AddClickEventListener(self.node_list.jump_bz_btn, BindTool.Bind(self.OnClickJumpBtn, self, GuideModuleName.BiZuo))
    XUI.AddClickEventListener(self.node_list.jump_rc_btn, BindTool.Bind(self.OnClickJumpBtn, self, GuideModuleName.BiZuo))
    XUI.AddClickEventListener(self.node_list.jump_xx_btn, BindTool.Bind(self.OnClickJumpBtn, self, GuideModuleName.BiZuo, TabIndex.bizuo_act_hall))
    XUI.AddClickEventListener(self.node_list.preview_btn, BindTool.Bind(self.OnClickPreviewBtn, self))
    XUI.AddClickEventListener(self.node_list.to_break_btn, BindTool.Bind(self.OnClickTuPoBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind(self.OnClickCloseWindow, self))

    local other_remind_list = {RemindName.BiZuo}
    self.remind_callback = BindTool.Bind(self.OtherRemindCallBack, self)
    for k,v in pairs(other_remind_list) do
        RemindManager.Instance:Bind(self.remind_callback, v)
    end

    self:FlushModel()

    self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.XiuWeiView, self.get_guide_ui_event)
    -- local ui_scene_config_index = CultivationWGData.Instance:GetXiuWeiOtherCfg().xiuwei_ui_scene_config_index
    -- Scene.Instance:ChangeUISceneController(UI_SCENE_TYPE.DEFAULT, UI_SCENE_CONFIG_INDEX.XIUWEI, ui_scene_config_index)

    self.is_show_preview = false
    self:PreviewLoadCallBack()
    self:InitTweenRoot()
end

function XiuWeiView:CloseCallBack()
    MainuiWGCtrl.Instance:DelayShowCachePower(0)
end

function XiuWeiView:ReleaseCallBack()
    self:ClearAniTween()
    RemindManager.Instance:UnBind(self.remind_callback)
    if self.get_guide_ui_event then
        FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.XiuWeiView, self.get_guide_ui_event)
        self.get_guide_ui_event = nil
    end

    if self.show_model then
        self.show_model:DeleteMe()
        self.show_model = nil
    end

    if self.small_state_list then
        for k, v in pairs(self.small_state_list) do
            v:DeleteMe()
        end

        self.small_state_list = nil
    end

    if self.level_cell_list then
        for k, v in pairs(self.level_cell_list) do
            v:DeleteMe()
        end

        self.level_cell_list = nil
    end

    if self.limit_list then
        self.limit_list:DeleteMe()
        self.limit_list = nil
    end

    if self.attr_list then
        self.attr_list:DeleteMe()
        self.attr_list = nil
    end

    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end

    if self.luck_item then
        self.luck_item:DeleteMe()
        self.luck_item = nil
    end

    if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end

    self:PreviewReleaseCallBack()
    self.old_is_complete_flag = nil
end

function XiuWeiView:ShowIndexCallBack()
    self:ClearAniTween()
    self:ResetNodePos()

    if self.show_model then
        local is_complete = XiuWeiWGData.Instance:IsCompleteAllLevelCellFlag()
        local anim_name = is_complete and SceneObjAnimator.ChousJiangIdle or SceneObjAnimator.Sit_Idle
		-- self.show_model:PlaySitAction()
        self.show_model:PlayRoleAction(anim_name)
	end

    self:PreviewShowIndexCallBack()
    self.is_show_preview = nil
end

function XiuWeiView:OnFlush(param_t)
	for k, v in pairs(param_t) do
		if k == "tupo_back" then
            self:PlayTuPoAni(v)
            self:OnPreviewFlush()
			return
        elseif k == "show_level_guide" then
            self:ShowLevelNormalAreaGuide(v.root_id)
            self:ReSetModelAnim()
		elseif k == "flush_model" then
            self:ReSetModelAnim()
        end
	end

    self:FlushMidPart()
    self:FlushTopPart()
    self:FlushBottomPart()
    self:FlushRightPart()
    self:OnPreviewFlush()
    self:FlushTotalAdd()
end

function XiuWeiView:ReSetModelAnim()
    if self.show_model then
        local is_complete = XiuWeiWGData.Instance:IsCompleteAllLevelCellFlag()
        local anim_name = is_complete and SceneObjAnimator.ChousJiangIdle or SceneObjAnimator.Sit_Idle
        self.show_model:PlayRoleAction(anim_name)
    end
end

-- 刷新相关
function XiuWeiView:FlushModel()
    if not self.show_model then
		self.show_model = RoleModel.New()
        self.show_model:ClearCustomDisplayTranfromData()
		self.show_model:SetUISceneModel(self.node_list["model_pos"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.show_model, 0)
	end

    local role_vo = GameVoManager.Instance:GetMainRoleVo()
    local special_status_table = {ignore_halo = true, ignore_weapon = true, ignore_wing = true}
    local is_complete = XiuWeiWGData.Instance:IsCompleteAllLevelCellFlag()
    local anim_name = is_complete and SceneObjAnimator.ChousJiangIdle or SceneObjAnimator.Sit_Idle
    self.show_model:SetModelResInfo(role_vo, special_status_table, nil, anim_name)

    local other_cfg = CultivationWGData.Instance:GetXiuWeiOtherCfg()
    if other_cfg.xiuwei_role_model_pos and other_cfg.xiuwei_role_model_pos ~= "" then
		local pos = Split(other_cfg.xiuwei_role_model_pos, "|")
		self.show_model:SetUSAdjustmentNodeLocalPosition(tonumber(pos[1]) or 0, tonumber(pos[2]) or 0, tonumber(pos[3]) or 0)
	end

	local sex, prof = RoleWGData.Instance:GetRoleSexProf()
    local xiuwei_role_model_rot = other_cfg["xiuwei_role_model_rot_"..sex.."_"..prof] or other_cfg.xiuwei_role_model_rot


	if xiuwei_role_model_rot and xiuwei_role_model_rot ~= "" then
		local pos = Split(xiuwei_role_model_rot, "|")
		self.show_model:SetUSAdjustmentNodeLocalRotation(tonumber(pos[1]) or 0, tonumber(pos[2]) or 0, tonumber(pos[3]) or 0)
	end

	if other_cfg.xiuwei_role_model_scale and other_cfg.xiuwei_role_model_scale ~= "" then
		self.show_model:SetUSAdjustmentNodeLocalScale(tonumber(other_cfg.xiuwei_role_model_scale) or 0)
	end

    self.show_model:FixToOrthographic(self.root_node_transform)
	-- self.show_model:SetRTAdjustmentRootTransform({model_pos = other_cfg.xiuwei_role_model_pos, model_rot = other_cfg.xiuwei_role_model_rot, model_scale = other_cfg.xiuwei_role_model_scale})
end

function XiuWeiView:FlushMidPart()
	local can_break, is_max_level, is_max_stage = CultivationWGData.Instance:IsCanBreak()
    local is_ready_tupo = not is_max_stage and can_break
    self.node_list.to_break_btn:CustomSetActive(is_ready_tupo)
    self.node_list.level_cell_list:CustomSetActive(not is_ready_tupo)

    if not is_ready_tupo then
        local data_list = XiuWeiWGData.Instance:GetLevelStageShowList()
        for k, v in pairs(self.level_cell_list) do
            v:SetData(data_list[k])
        end
    end

    local is_complete = XiuWeiWGData.Instance:IsCompleteAllLevelCellFlag()

    if nil~= self.old_is_complete_flag and not self.old_is_complete_flag and is_complete then
        self.show_model:PlayRoleAction(SceneObjAnimator.ChouJiang_Click)
    end
    self.old_is_complete_flag = is_complete
end

function XiuWeiView:FlushTopPart()
    local cur_state_cfg = CultivationWGData.Instance:GetCurXiuWeiStageCfg()
    if IsEmptyTable(cur_state_cfg) then
        return
    end

    self.node_list.preview_btn_remind:CustomSetActive(CultivationWGData.Instance:GetPreviewAwradRemin())
    local state_img_bundle, state_img_asset = ResPath.GetXiuWeiImg(cur_state_cfg.stage_title_icon)
    self.node_list.big_state_img.image:LoadSprite(state_img_bundle, state_img_asset)

    local small_state_show_list = XiuWeiWGData.Instance:GetShowSmallStageList(cur_state_cfg.client_stage)
    local show_num = #small_state_show_list
    local is_change_num = false
    if self.cur_show_num ~= show_num then
        is_change_num = true
        self.cur_show_num = show_num
    end

    for k, v in pairs(self.small_state_list) do
        v:SetData(small_state_show_list[k])
        v:ChangeShowPos(is_change_num, show_num)
    end
end

function XiuWeiView:FlushBottomPart()
    local get_cultivation_remin = CultivationWGData.Instance:GetCultivationItemRemind()
	self.node_list.open_danyao_panel_btn_remind:CustomSetActive(get_cultivation_remin)

	local esoterica_remind = CultivationWGData.Instance:GetEsotericaAllRemind()
	self.node_list.goto_xianfa_btn_remind:CustomSetActive(esoterica_remind == 1)

    local cur_state_cfg = CultivationWGData.Instance:GetCurXiuWeiStageCfg()
    if IsEmptyTable(cur_state_cfg) then
        return
    end

	local can_break, is_max_level, is_max_stage = CultivationWGData.Instance:IsCanBreak()
    local is_ready_tupo = not is_max_stage and can_break
	self.node_list.normal_panel:CustomSetActive(not is_ready_tupo)
	self.node_list.tupo_panel:CustomSetActive(is_ready_tupo)
	-- self.node_list.luck_item:CustomSetActive(is_ready_tupo)
    self.node_list.luck_item:CustomSetActive(false)
	self.node_list.open_danyao_panel_btn:CustomSetActive(not is_ready_tupo)
    if is_ready_tupo then
        local other_cfg = CultivationWGData.Instance:GetXiuWeiOtherCfg()
        local is_use = CultivationWGData.Instance:GetIsUseGuaranteeItem()
        local stage_item = other_cfg.stage_item_id
        local stage_item_num = ItemWGData.Instance:GetItemNumInBagById(stage_item)

        local suc_rate = cur_state_cfg.success_rate
        local add_rate = is_use and cur_state_cfg.item_success_rate * stage_item_num or 0

        local is_get_privilege = CultivationWGData.Instance:IsGetPrivilege()
        local xiuwei_privilege_cfg = CultivationWGData.Instance:GetCultivationPrivilegeCfg()
        if is_get_privilege then
            add_rate = add_rate + xiuwei_privilege_cfg.add_success_rate
        end

        local target_rate = suc_rate + add_rate
        target_rate = target_rate > 10000 and 10000 or target_rate
        local need_add_rate = 10000 - suc_rate
        local target_add_rate = suc_rate > 10000 and 0 or add_rate > need_add_rate and need_add_rate or add_rate
        local need_lucky_item_num = 0
        if need_add_rate ~= 0 then
            if is_get_privilege then
                local privilege_add_rate = xiuwei_privilege_cfg.add_success_rate
                if need_add_rate >= privilege_add_rate then
                    need_lucky_item_num = math.ceil((need_add_rate - privilege_add_rate) / cur_state_cfg.item_success_rate)
                end
            else
                need_lucky_item_num = math.ceil(need_add_rate / cur_state_cfg.item_success_rate)
            end
        end

        self.node_list.tupo_gailv.text.text = string.format(Language.XiuWei.TuPoSucRate, suc_rate / 100, target_add_rate / 100)

        local next_state_cfg = CultivationWGData.Instance:GetNextXiuWeiStageCfg()
        local next_stage_title = next_state_cfg.stage_title or ""
        self.node_list.next_preview_tip.text.text = string.format(Language.XiuWei.TuPoTip, cur_state_cfg.stage_title, next_stage_title)
        self.luck_item:SetData({stage_item = stage_item, stage_item_num = stage_item_num, need_lucky_item_num = need_lucky_item_num})
    else
        local cur_xiuwei_level_cfg = CultivationWGData.Instance:GetCurXiuWeiLevelCfg()
        local next_level_need_exp = cur_xiuwei_level_cfg.need_exp
        local cur_exp = CultivationWGData.Instance:GetXiuWeiExp()
        self.node_list.xiuwei_num.text.text = string.format(Language.Cultivation.XiuWeiValue, cur_exp, next_level_need_exp)
        self.node_list.xiuwei_num_slider.slider.value = cur_exp / next_level_need_exp
    end
end

function XiuWeiView:FlushRightPart()
	local can_break, is_max_level, is_max_stage = CultivationWGData.Instance:IsCanBreak()
    local is_show_fun_open_panel = false
    self.node_list.add_cap_num.text.text = CultivationWGData.Instance:GetCultivationCap()
    local cur_stage_cfg = CultivationWGData.Instance:GetCurXiuWeiStageCfg()
	local next_stage_cfg = CultivationWGData.Instance:GetNextXiuWeiStageCfg()
    if IsEmptyTable(cur_stage_cfg) then
        return
    end

    local cur_level = CultivationWGData.Instance:GetXiuWeiLevel()
    local cur_level_cfg = XiuWeiWGData.Instance:GetLevelStageCfg(cur_stage_cfg.stage, cur_level)
    local next_xiuwei_cfg = XiuWeiWGData.Instance:GetLevelStageCfg(cur_stage_cfg.stage, cur_level + 1)

    local xiuwei_data_list = nil
    local jinjie_attr_list = nil
    if not is_max_stage and is_max_level then
        jinjie_attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_stage_cfg, next_stage_cfg, nil, nil, 1, 7)
        xiuwei_data_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_level_cfg, nil, nil, nil, 1, 7)
    else
        jinjie_attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_stage_cfg, nil, nil, nil, 1, 7)
        xiuwei_data_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_level_cfg, next_xiuwei_cfg, nil, nil, 1, 7)
    end

    -- 现在要求全部整合了
    local total_data_list = EquipWGData.MergeAttrList(xiuwei_data_list, jinjie_attr_list)

    -- 加上天道相关数据
    -- local td_data = {}
    -- td_data.is_td = true
    -- td_data.cur_count = CultivationWGData.Instance:GetSlotCountByStage(cur_stage_cfg.stage)
    -- td_data.can_break = can_break
    -- table.insert(total_data_list, td_data)
    self.attr_list:SetDataList(total_data_list)

    if not is_max_stage then
        self.node_list.limit_title_txt.text.text = string.format(Language.XiuWei.LimitPanelTitle, (next_stage_cfg.stage_title or cur_stage_cfg.stage_title or ""))
        local task_cfg = XiuWeiWGData.Instance:GetTaskCfg()
        if task_cfg then
            self.limit_list:SetDataList(task_cfg)
        end

        self.node_list.fun_open_title_tip.text.text = cur_stage_cfg.stage_title
        local btn_name_list = {}
        if cur_stage_cfg.open_fun_img_list ~= "" then
            is_show_fun_open_panel = true
            btn_name_list = Split(cur_stage_cfg.open_fun_img_list, "|")
        end

        for i = 1, 3 do
            self.node_list["open_fun_cell" .. i]:CustomSetActive(btn_name_list[i] ~= nil)
            if btn_name_list[i] then
                local bundle, asset = ResPath.GetMainUIIcon(btn_name_list[i])
                self.node_list["open_fun_icon" .. i].image:LoadSprite(bundle, asset, function ()
                    self.node_list["open_fun_icon" .. i].image:SetNativeSize()
                end)
            end
        end

        local reward_item = cur_stage_cfg.reward_item
        if not IsEmptyTable(reward_item) then
            self.reward_list:SetDataList(reward_item)
            self.node_list.reward_panel:CustomSetActive(not is_max_stage)
        else
            self.node_list.reward_panel:CustomSetActive(false)
        end

        -- local stage_preview_cfg = CultivationWGData.Instance:GetCultivationPreviewCfgByStage(cur_stage_cfg.client_stage)
        -- if not IsEmptyTable(stage_preview_cfg) then
        --     self.reward_list:SetDataList(stage_preview_cfg.reward_item)
        --     self.node_list.reward_panel:CustomSetActive("" ~= stage_preview_cfg.reward_item)
        -- end
    end

    self.node_list.tupo_limit_panel:CustomSetActive(not is_max_stage)
	-- self.node_list.reward_panel:CustomSetActive(not is_max_stage)
	self.node_list.fun_open_panel:CustomSetActive(not is_max_stage and is_show_fun_open_panel)
    -- self.node_list.flag_task_not_complete:CustomSetActive(not is_max_stage and XiuWeiWGData.Instance:IsCanShowTuPoTip())

end

-- 点击相关
function XiuWeiView:OnClickJumpBtn(view_name, tab_index)
    FunOpen.Instance:OpenViewByName(view_name, tab_index)
end

function XiuWeiView:OnClickPreviewBtn()
    -- ViewManager.Instance:Open(GuideModuleName.XiuWeiPreviewView)
    self:TweenInfoToPreview()
end

function XiuWeiView:OnClickCloseWindow()
    if self.is_show_preview then
        self:TweenPreviewToInfo()
        return
    end

    self:Close()
end

function XiuWeiView:OnClickOpenDanYaoPanelBtn()
	CultivationWGCtrl.Instance:OpenGetView()
end

function XiuWeiView:OnClickTuPoBtn()
    local can_dujie, is_max_level, is_max_stage = CultivationWGData.Instance:IsCanBreak()
	if is_max_stage then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Cultivation.MaxStage)
		return
	end

	local cur_stage_cfg = CultivationWGData.Instance:GetCurXiuWeiStageCfg()
	if not can_dujie then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Cultivation.OpenDuJieLevel, cur_stage_cfg.need_level))
		return
	end

	local other_cfg = CultivationWGData.Instance:GetXiuWeiOtherCfg()
	local is_use = CultivationWGData.Instance:GetIsUseGuaranteeItem()
	local stage_item = other_cfg.stage_item_id
	local stage_item_num = ItemWGData.Instance:GetItemNumInBagById(stage_item)

	local dujie_func = function ()
        MainuiWGData.Instance:CreateCacheTable()
		CultivationWGCtrl.Instance:SetRoleCapCache()
		local use_lucky_item = stage_item_num > 0 and is_use and 1 or 0
		CultivationWGCtrl.Instance:UpgradeStage(use_lucky_item)
	end

	local suc_rate = cur_stage_cfg.success_rate
	local add_rate = is_use and cur_stage_cfg.item_success_rate * stage_item_num or 0
	local is_get_privilege = CultivationWGData.Instance:IsGetPrivilege()
	if is_get_privilege then
		local xiuwei_privilege_cfg = CultivationWGData.Instance:GetCultivationPrivilegeCfg()
		add_rate = add_rate + xiuwei_privilege_cfg.add_success_rate
	end
	local target_rate = suc_rate + add_rate
	target_rate = target_rate > 10000 and 10000 or target_rate

	if target_rate >= 10000 then
		dujie_func()
	else
		if not self.alert then
			self.alert = Alert.New()
		end

		self.alert:SetShowCheckBox(true, "xiuwei_dujie_alert")
		self.alert:SetOkFunc(function ()
			dujie_func()
		end)

		local content = string.format(Language.Cultivation.XiuWeiDuJieDesc, target_rate / 100)
		self.alert:SetLableString(content)
		self.alert:SetCheckBoxDefaultSelect(false)
		self.alert:SetCheckBoxText(Language.Cultivation.XiuWeiDuJieCheckBox)
		self.alert:Open()
	end
end

function XiuWeiView:OnClickLevelCellCallBack(cell)
    if not cell then
        return
    end

    local cell_data = cell:GetData()
    if not cell_data then
        return
    end

    local is_can_up, tip = XiuWeiWGData.Instance:GetSingleLevelIsCanUp(cell_data.stage, cell_data.level)
    if not is_can_up then
        if tip then
            SysMsgWGCtrl.Instance:ErrorRemind(tip)
        end
        return
    end

    CultivationWGCtrl.Instance:UpgradeLevel()
end

function XiuWeiView:OnClickLuckItemCallBack()
    self:FlushBottomPart()
end

-- 动画相关
function XiuWeiView:PlayTuPoAni(is_suc)
    self:ClearAniTween()
    self.node_list.mid_part:CustomSetActive(false)
    self.tupo_ani_tweener = DG.Tweening.DOTween.Sequence()
    self.tupo_ani_tweener:Join(self.node_list.top_part.rect:DOAnchorPosY(self.top_part_x + 2000, 1))
    self.tupo_ani_tweener:Join(self.node_list.bottom_part.rect:DOAnchorPosY(self.top_part_x - 2000, 1))
    self.tupo_ani_tweener:Join(self.node_list.left_part.rect:DOAnchorPosX(self.top_part_x - 2000, 1))
    self.tupo_ani_tweener:Join(self.node_list.right_part.rect:DOAnchorPosX(self.top_part_x + 2000, 1))
    self.tupo_ani_tweener:SetEase(DG.Tweening.Ease.Linear)
    self.tupo_ani_tweener:OnComplete(function ()
        self.show_model:PlayRoleAction(SceneObjAnimator.Prizedraw)
        self.show_model:CrossFadeToOtherAnim(SceneObjAnimator.Prizedraw, SceneObjAnimator.Sit_Idle, function ()
            self:ResetNodePos()
            self:FlushModel()
            self:FlushMidPart()
            self:FlushTopPart()
            self:FlushBottomPart()
            self:FlushRightPart()
            MainuiWGCtrl.Instance:DelayShowCachePower(0)

            if is_suc then
                local new_cap = RoleWGData.Instance:GetMainRoleCap()
                if new_cap > CultivationWGCtrl.Instance:GetRoleCapCache() then
                    MainuiWGCtrl.Instance.view:ShowPowerChange(new_cap, self.role_cap_cache)
                end

                CultivationWGCtrl.Instance:OpenCultivationBreakSuccessView()
            else
                CultivationWGCtrl.Instance:OpenCultivationBreakFailView()
            end
        end)
    end)
end

function XiuWeiView:ResetNodePos()
    RectTransform.SetAnchoredPositionXY(self.node_list.top_part.rect, self.top_part_x, self.top_part_y)
    RectTransform.SetAnchoredPositionXY(self.node_list.bottom_part.rect, self.bottom_part_x, self.bottom_part_y)
    RectTransform.SetAnchoredPositionXY(self.node_list.left_part.rect, self.left_part_x, self.left_part_y)
    RectTransform.SetAnchoredPositionXY(self.node_list.right_part.rect, self.right_part_x, self.right_part_y)
    self.node_list.mid_part:CustomSetActive(true)
end

function XiuWeiView:ClearAniTween()
    if self.tupo_ani_tweener then
        self.tupo_ani_tweener:Kill()
        self.tupo_ani_tweener = nil
    end
end

-- 其他
function XiuWeiView:OtherRemindCallBack(remind_name, num)
    if remind_name == RemindName.BiZuo then
        self.node_list.jump_bz_btn_remind:CustomSetActive(num > 0)
    end
end

function XiuWeiView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == "btn_cultivation_up" then
        local cur_level = CultivationWGData.Instance:GetXiuWeiLevel()
	    return self.node_list.level_cell_list:FindObj(ui_name), BindTool.Bind(self.OnClickLevelCellCallBack, self, self.level_cell_list[cur_level + 1])
    elseif ui_name == "limit_guide_task_02" or ui_name == "limit_guide_task_03" then
        local jump_fun = function()
            FunOpen.Instance:OpenViewNameByCfg("fubenpanel#fubenpanel_welkin")
        end

        if ui_name == "limit_guide_task_03" then
            local task_cfg = XiuWeiWGData.Instance:GetTaskCfg()
            local zero_data = task_cfg and task_cfg[0]
            if zero_data then
                local task_data = XiuWeiWGData.Instance:GetTaskDataBySeq(zero_data.seq)
                local is_complete = task_data.status == XiuWeiWGData.TaskState.Complete

                if is_complete then
                    return self.node_list.limit_guide_task_01
                end
            end
        end
        
        return self.node_list[ui_name], jump_fun
	end

	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

function XiuWeiView:GetUISceneEffectsTransform()
	local ui_scene_cfg = Scene.Instance:GetUISceneCfg(UI_SCENE_TYPE.DEFAULT)
	if not IsEmptyTable(ui_scene_cfg) then
		return Scene.Instance:GetUIEffectsTransform(ui_scene_cfg.asset_name)
	end

	return nil
end

function XiuWeiView:ShowLevelNormalAreaGuide(root_id)
    if root_id and self.node_list["level_cell" .. root_id] then
        FunctionGuide.Instance:ShowNormalAreaGuide(GuideModuleName.XiuWeiView, self.node_list["level_cell" .. root_id])
    end
end

function XiuWeiView:FlushTotalAdd()
	local number,rate = CultivationWGData.Instance:GetTotalExpAdd()
	self.node_list.text_number.text.text = number
	self.node_list.text_rate.text.text = string.format(Language.Cultivation.ExpAddTotalStr3, rate)
end

------------------------------------------tween_start---------------------------------------------
function XiuWeiView:InitTweenRoot()
    
end

-- preview_left_root  -354 -20
-- preview_right_root 382  25
-- preview_top_root -1       186
-- preview_bom_root   0 -286

-- top_part        -138  -124
-- bottom_part     -216   80
-- mid_part        -140    -22
-- left_part       66    108
-- right_part       -210  0
function XiuWeiView:TweenInfoToPreview()
    self.is_show_preview = true
    local time = 0.5
    local tween_type = DG.Tweening.Ease.Linear

    UITween.CleanAllMoveToShowPanel(GuideModuleName.XiuWeiView)
    UITween.MoveToShowPanel(GuideModuleName.XiuWeiView, self.node_list["preview_left_root"], Vector2(-661, -20), Vector2(-461, -20), time, tween_type)
	UITween.MoveToShowPanel(GuideModuleName.XiuWeiView, self.node_list["preview_right_root"], Vector2(522, 0), Vector2(322, 0), time, tween_type)
	UITween.MoveToShowPanel(GuideModuleName.XiuWeiView, self.node_list["preview_top_root"], Vector2(-149, 386), Vector2(-149, 186), time, tween_type)
    UITween.MoveToShowPanel(GuideModuleName.XiuWeiView, self.node_list["preview_bom_root"], Vector2(-149, -486), Vector2(-149, -286), time, tween_type)

    UITween.MoveToShowPanel(GuideModuleName.XiuWeiView, self.node_list["top_part"], Vector2(-138, -124), Vector2(-138, 60), time, tween_type)
	UITween.MoveToShowPanel(GuideModuleName.XiuWeiView, self.node_list["bottom_part"],Vector2(-216, 80), Vector2(-216, -404), time, tween_type)
	UITween.MoveToShowPanel(GuideModuleName.XiuWeiView, self.node_list["left_part"], Vector2(66, 108), Vector2(-134, 108), time, tween_type)
    UITween.MoveToShowPanel(GuideModuleName.XiuWeiView, self.node_list["right_part"], Vector2(-210, 0), Vector2(10, 0), time, tween_type)

    UITween.ReleaseAlpahTabByObj(self.node_list["preview_root"])
    UITween.AlpahShowPanel(self.node_list["preview_root"], true, time, tween_type, function ()
        self.node_list["preview_root"].canvas_group.interactable = true
        self.node_list["preview_root"].canvas_group.blocksRaycasts = true
    end)

    UITween.ReleaseAlpahTabByObj(self.node_list["xiuwei_root"])
    UITween.AlpahShowPanel(self.node_list["xiuwei_root"], false, time, tween_type, function ()
        self.node_list["xiuwei_root"].canvas_group.interactable = false
        self.node_list["xiuwei_root"].canvas_group.blocksRaycasts = false
    end)
end

function XiuWeiView:TweenPreviewToInfo()
    self.is_show_preview = false
    local time = 0.5
    local tween_type = DG.Tweening.Ease.Linear

    UITween.CleanAllMoveToShowPanel(GuideModuleName.XiuWeiView)
    UITween.MoveToShowPanel(GuideModuleName.XiuWeiView, self.node_list["preview_left_root"], Vector2(-461, -20), Vector2(-661, -20), time, tween_type)
	UITween.MoveToShowPanel(GuideModuleName.XiuWeiView, self.node_list["preview_right_root"],Vector2(322, 0), Vector2(522, 0), time, tween_type)
	UITween.MoveToShowPanel(GuideModuleName.XiuWeiView, self.node_list["preview_top_root"], Vector2(-149, 186), Vector2(-149, 386), time, tween_type)
    UITween.MoveToShowPanel(GuideModuleName.XiuWeiView, self.node_list["preview_bom_root"], Vector2(-149, -286), Vector2(-149, -486), time, tween_type)

    UITween.MoveToShowPanel(GuideModuleName.XiuWeiView, self.node_list["top_part"], Vector2(-138, 60), Vector2(-138, -124), time, tween_type)
	UITween.MoveToShowPanel(GuideModuleName.XiuWeiView, self.node_list["bottom_part"],Vector2(-216, -404),Vector2(-216, 80), time, tween_type)
	UITween.MoveToShowPanel(GuideModuleName.XiuWeiView, self.node_list["left_part"],  Vector2(-134, 108),Vector2(66, 108), time, tween_type)
    UITween.MoveToShowPanel(GuideModuleName.XiuWeiView, self.node_list["right_part"], Vector2(10, 0),Vector2(-210, 0),  time, tween_type)

    UITween.ReleaseAlpahTabByObj(self.node_list["preview_root"])
    UITween.AlpahShowPanel(self.node_list["preview_root"], false, time, tween_type, function ()
        self.node_list["preview_root"].canvas_group.interactable = false
        self.node_list["preview_root"].canvas_group.blocksRaycasts = false
    end)
    
    UITween.ReleaseAlpahTabByObj(self.node_list["xiuwei_root"])
    UITween.AlpahShowPanel(self.node_list["xiuwei_root"], true, time, tween_type, function ()
        self.node_list["xiuwei_root"].canvas_group.interactable = true
        self.node_list["xiuwei_root"].canvas_group.blocksRaycasts = true
    end)
end
-------------------------------------------tween_end----------------------------------------------

-------------------------- XiuWeiSmallStatelCell 小境界展示render
XiuWeiSmallStatelCell = XiuWeiSmallStatelCell or BaseClass(BaseRender)
function XiuWeiSmallStatelCell:OnFlush()
    self:SetVisible(self.data ~= nil)
    if not self.data then
        return
    end

    local cn_num = NumberToChinaNumber(self.index)
    self.node_list.small_state.text.text = string.format(Language.XiuWei.Level, cn_num)

    local cur_stage = CultivationWGData.Instance:GetXiuWeiState()
    self.node_list.hl:CustomSetActive(cur_stage >= self.data.stage)
    XUI.SetGraphicGrey(self.view, cur_stage < self.data.stage)
end

function XiuWeiSmallStatelCell:ChangeShowPos(need_change_pos, total_num)
    if not need_change_pos then
        return
    end

    local pos_info = (SmallStateCellPos[total_num] or {})[self.index]
    if not pos_info then
        return
    end

    self:SetAnchoredPosition(pos_info.x, pos_info.y)
end

-------------------------- XiuWeiLevelCell 小境界等级展示render
XiuWeiLevelCell = XiuWeiLevelCell or BaseClass(BaseRender)
function XiuWeiLevelCell:OnFlush()
    self:SetVisible(self.data ~= nil)
    if not self.data then
        return
    end

    local cur_level = CultivationWGData.Instance:GetXiuWeiLevel()
    local is_active = cur_level >= self.data.level
    self.node_list.normal:CustomSetActive(not is_active)
    self.node_list.active_flag:CustomSetActive(is_active)
    -- self.node_list.can_active:CustomSetActive(XiuWeiWGData.Instance:GetSingleLevelIsCanUp(self.data.stage, self.data.level))
    self.node_list.remind:CustomSetActive(XiuWeiWGData.Instance:GetSingleLevelIsCanUp(self.data.stage, self.data.level))
end

-------------------------- XiuWeiLimitCell 突破条件展示render
XiuWeiLimitCell = XiuWeiLimitCell or BaseClass(BaseRender)
function XiuWeiLimitCell:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.go_to_btn, BindTool.Bind(self.OnClickGotoBtn, self))
end

function XiuWeiLimitCell:OnFlush()
    if not self.data then
        return
    end

    local task_data = XiuWeiWGData.Instance:GetTaskDataBySeq(self.data.seq)
    if not task_data then
        return
    end

    local is_complete = task_data.status == XiuWeiWGData.TaskState.Complete
    self.node_list.enough_flag:CustomSetActive(is_complete)
    self.node_list.not_enough_flag:CustomSetActive(not is_complete)
	self.node_list.go_to_btn:CustomSetActive(not is_complete)
    local color = is_complete and COLOR3B.C8 or COLOR3B.C6
    -- local str = self:GetLimitStr()
    self.node_list.limit_txt.text.text = ToColorStr(self.data.task_des, color)

    local complete_all_level_flag = XiuWeiWGData.Instance:IsCompleteAllLevelCellFlag()
    local next_stage_cfg = CultivationWGData.Instance:GetNextXiuWeiStageCfg()
	local is_max_stage = IsEmptyTable(next_stage_cfg)

    -- self.node_list.flag_task_not_complete:CustomSetActive(not is_complete and not is_max_stage and complete_all_level_flag)
    self.node_list.flag_task_not_complete:CustomSetActive(not is_complete and not is_max_stage)
end

-- function XiuWeiLimitCell:GetLimitStr()
--     local str = ""
--     if not self.data then
--         return str
--     end

--     if self.data.task_type == XiuWeiWGData.TaskType.XiuWeiLevel then
--         str = Language.XiuWei.LimitStr[1]
--     elseif self.data.task_type == XiuWeiWGData.TaskType.TowerLayer then
--         str = string.format(Language.XiuWei.LimitStr[2], self.data.progress)
--     elseif self.data.task_type == XiuWeiWGData.TaskType.ActiveFasion then
--         local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(self.data.param1, self.data.param2)
--         local fashion_name = fashion_cfg and fashion_cfg.name or ""
--         str = string.format(Language.XiuWei.LimitStr[3], fashion_name)
--     elseif self.data.task_type == XiuWeiWGData.TaskType.RoleLevel then
--         str = string.format(Language.XiuWei.LimitStr[4], self.data.progress)
--     elseif self.data.task_type == XiuWeiWGData.TaskType.RoleCapability then
--         str = string.format(Language.XiuWei.LimitStr[5], self.data.progress)
--     end

--     return str
-- end

function XiuWeiLimitCell:OnClickGotoBtn()
    if not self.data then
        return
    end

    if self.data.opera_type == 1 then
        local has_can_active_root = XiuWeiWGData.Instance:GetCanUpLevelRootId()

        if has_can_active_root and has_can_active_root >= 0 then
            XiuWeiWGCtrl.Instance:ShowLevelNormalGuideView(has_can_active_root)
            return
        end
    elseif self.data.opera_type == 3 then
        TaskWGCtrl.Instance:OpenTaskGainExpTipView()
        return
    elseif self.data.opera_type == 4 then
        ViewManager.Instance:Open(GuideModuleName.MainUIStrongMenuView)
        return
    end

    FunOpen.Instance:OpenViewNameByCfg(self.data.open_pane1)
end

-------------------------- XiuWeiLuckItem 突破幸运道具
XiuWeiLuckItem = XiuWeiLuckItem or BaseClass(BaseRender)
function XiuWeiLuckItem:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.luck_item_icon)
        self.item_cell:SetShowCualityBg(false)
    end

    XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClickSelf, self))
end

function XiuWeiLuckItem:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function XiuWeiLuckItem:OnFlush()
    if not self.data then
        return
    end

    local is_use = CultivationWGData.Instance:GetIsUseGuaranteeItem()
	self.node_list.not_select_flag:CustomSetActive(not is_use)
	self.node_list.luck_item_icon:CustomSetActive(is_use)
	self.node_list.img_cost_bg:CustomSetActive(is_use)
    if is_use then
        local num_str = string.format("%s/%s", self.data.stage_item_num, self.data.need_lucky_item_num)
        self.item_cell:SetData({ item_id = self.data.stage_item })
		self.item_cell:SetFlushCallBack(function ()
			self.item_cell:SetQualityIconVisible(false)
			self.item_cell:SetEffectRootEnable(false)
			self.item_cell:SetCellBgEnabled(false)
			self.item_cell:SetRightBottomTextVisible(false)

			local right_text = ToColorStr(self.data.stage_item_num .. "/" .. self.data.need_lucky_item_num,
			(self.data.need_lucky_item_num == 0 or (self.data.stage_item_num > self.data.need_lucky_item_num)) and COLOR3B.C8 or COLOR3B.C10)
			self.item_cell:SetRightBottomColorText(right_text)
			self.item_cell:SetRightBottomTextVisible(true)
			self.item_cell:SetUseButton(false)
		end)

		self.node_list.img_cost.text.text = ToColorStr(num_str, self.data.stage_item_num >= self.data.need_lucky_item_num and COLOR3B.C8 or COLOR3B.C10)
    end
end

function XiuWeiLuckItem:SetClickCallBack(click_callback)
	self.click_callback = click_callback
end

function XiuWeiLuckItem:OnClickSelf()
    local is_use = CultivationWGData.Instance:GetIsUseGuaranteeItem()
	if not is_use then
		CultivationWGData.Instance:SetIsUseGuaranteeItem(true)
        self:Flush()
	else
		local other_cfg = CultivationWGData.Instance:GetXiuWeiOtherCfg()
		local stage_item = other_cfg.stage_item_id
		local btn_callback_event
		if not IsEmptyTable(other_cfg) then
			btn_callback_event = {}
			btn_callback_event[1] = {btn_text = Language.Equipment.UnUseGuarantee, callback = function()
				CultivationWGData.Instance:SetIsUseGuaranteeItem(false)
                self:Flush()
			end}
		end

        TipWGCtrl.Instance:OpenItem({item_id = stage_item}, nil, nil, nil, btn_callback_event)
    end

    if self.click_callback then
        self.click_callback()
    end
end

-------------------------- XiuWeiAttrRender 修为属性render
XiuWeiAttrRender = XiuWeiAttrRender or BaseClass(BaseRender)
function XiuWeiAttrRender:__init()
	self.item_render = CommonAddAttrRender.New(self.node_list["root"])
end

function XiuWeiAttrRender:__delete()
	if self.item_render then
		self.item_render:DeleteMe()
		self.item_render = nil
	end
end

function XiuWeiAttrRender:OnFlush()
	if not self.data then return end
	if self.data.is_td then
		self.node_list.attr_name.text.text = Language.Cultivation.StageAttrTDName
		local cfg = CultivationWGData.Instance:GetCharmEquipShowStageCfg()

		if cfg then
			local cur_value = 1
			local next_value = 1
			local cur_stage_cfg = CultivationWGData.Instance:GetCurXiuWeiStageCfg()
			for i, v in ipairs(cfg) do
				if cur_stage_cfg.stage >= v.need_xiuwei_stage then
					cur_value = v.order
				end
				if next_value == 1 and cur_stage_cfg.stage < v.need_xiuwei_stage then
					next_value = v.order
				end
			end

			self.node_list.attr_value.text.text = cur_value
			local next_str = (self.data.can_break and next_value > cur_value) and next_value or ""
			self.node_list.add_value.text.text = next_str
			self.node_list.arrow:CustomSetActive(tostring(next_str) ~= "")
			self.node_list.add_value:CustomSetActive(tostring(next_str) ~= "")
		end

		self.node_list.attr_name.text.text = ToColorStr(self.node_list.attr_name.text.text, COLOR3B.PINK)
		self.node_list.attr_value.text.text = ToColorStr(self.node_list.attr_value.text.text, COLOR3B.PINK)
		self.node_list.add_value.text.text = ToColorStr(self.node_list.add_value.text.text, COLOR3B.PINK)
	else
		self.item_render:SetData(self.data)
	end
end