﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using System.IO;
using System.Linq;
using Nirvana;
using ProceduralLOD;
using UnityEditor.SceneManagement;

class SceneObjOptimizeTool : EditorWindow
{
    [MenuItem("GameObject/地编/浮窗", priority = 2000)]
    private static void OpenWindow()
    {
        var window = GetWindow<SceneObjOptimizeTool>();
        window.titleContent = new GUIContent("地编");
        window.Show();
    }

    private void OnGUI()
    {

        GUILayout.Label("选中:", EditorStyles.boldLabel);
        
        EditorGUILayout.Space();
        
        if (GUILayout.Button("整合相同预制体"))
        {
            CollectByPrefab();
        }
        
        if (GUILayout.Button("添加 LOD Generate Helper"))
        {
            AddLODGenerateHelper();
        }
        
        EditorGUILayout.Space();
        
        GUILayout.Label("全场景:", EditorStyles.boldLabel);
        
        EditorGUILayout.Space();
        
        if (GUILayout.Button("规范检测场景节点结构及组件"))
        {
            FixedDefaultInspector();
        }
        
        if (GUILayout.Button("检查非法预制体实例"))
        {
            CheckInvalidPrefab();
        }
        
        if (GUILayout.Button("清除合并网格"))
        {
            ClearCombineMeshes();
        }

        if (GUILayout.Button("重置LOD距离"))
        {
            LODGenerateHelper.ResetSwitchDistanceAll();
        }
        
        if (GUILayout.Button("刷新LOD材质"))
        {
            LODGenerateHelper.RefreshMaterialAll();
        }

        if (GUILayout.Button("重新生成所有LOD"))
        {
            if (EditorUtility.DisplayDialog("重新生成所有LOD", "重新生成当前场景所有LOD（LODGenerateHelper）, 需要不少的时间，是否继续？", "确认", "取消"))
            {
                LODGenerateHelper.BuildAll();
            }
        }
        
        if (Application.isPlaying)
        {
            if (GUILayout.Button("显示为LOD等级"))
            {
                DisplayLOD();
            }
        }
    }
    
    [MenuItem("GameObject/地编/整合相同预制体")]
    public static void CollectByPrefab()
    {
        GameObject prefab = PrefabUtility.GetNearestPrefabInstanceRoot(Selection.activeGameObject);
        if (prefab == null)
            return;

        string path = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(prefab);
        string name = Path.GetFileNameWithoutExtension(path);

        GameObject root = new GameObject(name);
        root.transform.SetParent(prefab.transform.parent);

        foreach (var t in Transform.FindObjectsByType<Transform>(FindObjectsInactive.Exclude, FindObjectsSortMode.None))
        {
            var root2 = PrefabUtility.GetNearestPrefabInstanceRoot(t.gameObject);
            if (root2)
            {
                var path2 = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(root2);
                if (path2 == path)
                {
                    root2.transform.SetParent(root.transform);
                }
            }
        }

        Selection.activeGameObject = root;
    }
    
    public static void CheckInvalidPrefab()
    {
        bool collectInvalidToSceneRoot = false;
        
        Dictionary<string, List<GameObject>> dict = new Dictionary<string, List<GameObject>>();
        foreach (var obj in FindObjectsByType<GameObject>(FindObjectsSortMode.None))
        {
            if (PrefabUtility.IsPartOfAnyPrefab(obj))
            {
                var root = PrefabUtility.GetNearestPrefabInstanceRoot(obj);
                var path = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(root);
                if (!dict.TryGetValue(path, out List<GameObject> list))
                {
                    list = new List<GameObject>();
                    dict.Add(path, list);
                }

                if (!list.Contains(root))
                {
                    list.Add(root);
                }
            }
        }

        foreach (var kv in dict)
        {
            var list = kv.Value;
            var count = list.Count;
            foreach (var obj in list)
            {
                int errorCode = -1;
                
                var addedGameObjects = PrefabUtility.GetAddedGameObjects(obj);
                foreach (var ovr in addedGameObjects)
                {
                    if (count == 1)
                    {
                        if (PrefabUtility.IsAnyPrefabInstanceRoot(ovr.instanceGameObject))
                            errorCode = 1;
                        else
                            ovr.Apply();
                    }
                    else
                    {
                        errorCode = 2;
                    }
                }

                var addedComponents = PrefabUtility.GetAddedComponents(obj);
                foreach (var ovr in addedComponents)
                {
                    if (count == 1)
                    {
                        ovr.Apply();
                    }
                    else
                    {
                        errorCode = 3;
                    }
                }

                var removedComponents = PrefabUtility.GetRemovedComponents(obj);
                foreach (var ovr in removedComponents)
                {
                    if (count == 1)
                    {
                        ovr.Apply();
                    }
                    else
                    {
                        errorCode = 4;
                    }
                }

                var objectOverrides = PrefabUtility.GetObjectOverrides(obj, false);
                foreach (var ovr in objectOverrides)
                {
                    if (ovr.instanceObject is LODGenerateHelper)
                    {
                        if (count == 1)
                        {
                            ovr.Apply();
                        }
                        else
                        {
                            errorCode = 5;
                        }
                    }
                    else if (ovr.instanceObject is GameObject instance && ovr.GetAssetObject() is GameObject source)
                    {
                        if (instance.activeSelf != source.activeSelf)
                        {
                            if (count == 1)
                            {
                                ovr.Apply();
                            }
                            else
                            {
                                errorCode = 6;
                            }
                        }
                    }
                }
                
                if (Array.Exists(obj.GetComponentsInParent<Transform>(), p => p.name == "Models"))
                {
                    if (obj.GetComponent<LODGenerateHelper>() == null)
                    {
                        errorCode = 7;
                    }
                }

                if (errorCode > 0)
                {
                    Debug.LogError("非法预制体实例：" + obj.name + ", " + errorCode switch
                    {
                        1 => "AddedGameObject中存在其他预制体",
                        2 => "存在AddedGameObject",
                        3 => "存在AddedComponent",
                        4 => "存在RemovedComponent",
                        5 => "LODGenerateHelper存在差异",
                        6 => "子节点激活状态存在差异",
                        7 => "缺少LODGenerateHelper",
                    }, obj);

                    if (collectInvalidToSceneRoot)
                    {
                        string poolName = "非法预制体实例";
                        GameObject pool = GameObject.Find(poolName);
                        if (pool == null)
                            pool = new GameObject(poolName);
                        obj.transform.SetParent(pool.transform);
                    }
                }
            }
        }
    }
    
    public static void DisplayLOD()
    {
        GameObject.Find("GPUInstances")?.SetActive(false);
        
        var shader = Shader.Find("Universal Render Pipeline/Unlit");
        var mat0 = new Material(shader);
        mat0.SetColor("_BaseColor", Color.green);
        var mat1 = new Material(shader);
        mat1.SetColor("_BaseColor", Color.blue);
        var mat2 = new Material(shader);
        mat2.SetColor("_BaseColor", Color.red);
        
        foreach (var g in FindObjectsOfType<LODGroup>())
        {
            var lods = g.GetLODs();
            for (int i = 0; i < lods.Length; i++)
            {
                int lv = i;

                Material mat = lv switch
                {
                    0 => mat0,
                    1 => mat1,
                    2 => mat2,
                };
                foreach (var renderer in lods[i].renderers)
                { 
                    if (renderer != null)
                    {
                        var mats = new Material[renderer.materials.Length];
                        for (int j = 0; j < mats.Length; j++)
                        {
                            mats[j] = mat;
                        }
                        renderer.materials = mats;
                    }
                }
            }
        }
    }
    
    private static GameObject GetNeastPrefab(GameObject target)
    {
        if (PrefabUtility.IsPartOfAnyPrefab(target))
        {
            return PrefabUtility.GetNearestPrefabInstanceRoot(target);
        }

        return null;
    }

    static GameObject GetOrAddGameObject(string objName, bool isMainDirChild = true)
    {
        GameObject gameObject = GameObject.Find("Main/" + objName);
        if (null == gameObject)
        {
            gameObject = GameObject.Find(objName);
        }

        if (null == gameObject)
        {
            gameObject = new GameObject();
            gameObject.name = objName;
        }

        if (isMainDirChild)
        {
            gameObject.transform.parent = GameObject.Find("Main").transform;
        }

        return gameObject;
    }

    static GameObject GetOrAddModelsGameObject(string objName)
    {
        GameObject gameObject = GameObject.Find("Main/Models/" + objName);
        if (null == gameObject)
        {
            gameObject = new GameObject(objName);
            gameObject.transform.parent = GameObject.Find("Models").transform;
        }

        return gameObject;
    }
    
    static void ClearCombineMeshes()
    {
        foreach (var filter in FindObjectsByType<MeshFilter>(FindObjectsSortMode.None))
        {
            if (filter != null && !filter.transform.parent.name.StartsWith("Auto Generate LOD") && AssetDatabase.GetAssetPath(filter.sharedMesh).Contains("AutoLODMeshes"))
            {
                var lodHelper = filter.GetComponent<LODGenerateHelper>();
                if (lodHelper != null)
                {
                    lodHelper.ClearAutoLOD();
                }
                
                for (int i = 0; i < filter.transform.childCount; i++)
                {
                    var child = filter.transform.GetChild(i);
                    child.gameObject.SetActive(true);
                }
                DestroyImmediate(filter.GetComponent<MeshRenderer>());
                DestroyImmediate(filter);
                DestroyImmediate(lodHelper);
            }
        }
        
        EditorSceneManager.MarkAllScenesDirty();
    }
    
    public static void FixedDefaultInspector()
    {
        GameObject MainObj = GameObject.Find("/Main");
        if (MainObj == null)
        {
            return;
        }
        
        SceneOptimize sceneOptimize = MainObj.GetOrAddComponent<SceneOptimize>();

        GameObject SceneDriver = GetOrAddGameObject("Scene Driver");
        SceneDriver.GetOrAddComponent<SceneDriver>();

        GameObject HeroLight = GameObject.Find("Main/Hero light");
        if (null != HeroLight)
        {
            Light light = HeroLight.GetOrAddComponent<Light>();
            light.shadows = LightShadows.None;
        }
        else
        {
            Debug.LogError("Error, 没有找到Hero light！！！");
        }

        GameObject Waters = GetOrAddGameObject("Waters");
        QualityControlActive waterQualityControlActive = Waters.GetOrAddComponent<QualityControlActive>();
        waterQualityControlActive.AutoFetch(true);

        GameObject Effects = GetOrAddGameObject("Effects");
        QualityControlActive effectsQualityControlActive = Effects.GetOrAddComponent<QualityControlActive>();
        effectsQualityControlActive.AutoFetch(true);

        GameObject colliders = GameObject.Find("Colliders");
        MeshRenderer meshRenderer = colliders.GetComponentInChildren<MeshRenderer>();
        if (null != meshRenderer)
        {
            meshRenderer.enabled = true;
        }

        GetOrAddGameObject("JumpWalk");
        GetOrAddGameObject("Animations");
        GetOrAddGameObject("Lights");
        GetOrAddGameObject("Models");

        //GetOrAddModelsGameObject("Others");
        //GetOrAddModelsGameObject("Stone");
        //GetOrAddModelsGameObject("Plant");
        //GetOrAddModelsGameObject("Terrain");
        //GetOrAddModelsGameObject("TempObjs");

        GameObject MainCamera = GameObject.Find("Main Camera");
        if (MainCamera != null)
        {
            GameObject.DestroyImmediate(MainCamera);
        }

        sceneOptimize.QuickOptimize();

        EditorUtility.ClearProgressBar();
        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
    }

    static void AddLODGenerateHelper()
    {
        foreach (var obj in Selection.gameObjects)
        {
            if (obj.name.ToLower().Contains("collider"))
            {
                continue;
            }
            
            if (obj.GetComponent<LODGenerateHelper>() == null)
            {
                obj.AddComponent<LODGenerateHelper>();
            }
        }
    }
}