﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class TMPro_TMP_TextWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(TMPro.TMP_Text), typeof(UnityEngine.UI.MaskableGraphic));
		<PERSON><PERSON>RegFunction("ForceMeshUpdate", ForceMeshUpdate);
		<PERSON><PERSON>Function("UpdateGeometry", UpdateGeometry);
		<PERSON><PERSON>RegFunction("UpdateVertexData", UpdateVertexData);
		L.RegFunction("SetVertices", SetVertices);
		<PERSON><PERSON>RegFunction("UpdateMeshPadding", UpdateMeshPadding);
		<PERSON><PERSON>Function("CrossFadeColor", CrossFadeColor);
		<PERSON><PERSON>unction("CrossFadeAlpha", CrossFadeAlpha);
		L.RegFunction("SetText", SetText);
		<PERSON>.RegFunction("SetCharArray", SetCharArray);
		<PERSON><PERSON>Function("GetPreferredValues", GetPreferredValues);
		<PERSON><PERSON>unction("GetRenderedValues", GetRenderedValues);
		<PERSON><PERSON>unction("GetTextInfo", GetTextInfo);
		<PERSON><PERSON>unction("ComputeMarginSize", ComputeMarginSize);
		L.RegFunction("ClearMesh", ClearMesh);
		L.RegFunction("GetParsedText", GetParsedText);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("text", get_text, set_text);
		L.RegVar("textPreprocessor", get_textPreprocessor, set_textPreprocessor);
		L.RegVar("isRightToLeftText", get_isRightToLeftText, set_isRightToLeftText);
		L.RegVar("font", get_font, set_font);
		L.RegVar("fontSharedMaterial", get_fontSharedMaterial, set_fontSharedMaterial);
		L.RegVar("fontSharedMaterials", get_fontSharedMaterials, set_fontSharedMaterials);
		L.RegVar("fontMaterial", get_fontMaterial, set_fontMaterial);
		L.RegVar("fontMaterials", get_fontMaterials, set_fontMaterials);
		L.RegVar("color", get_color, set_color);
		L.RegVar("alpha", get_alpha, set_alpha);
		L.RegVar("enableVertexGradient", get_enableVertexGradient, set_enableVertexGradient);
		L.RegVar("colorGradient", get_colorGradient, set_colorGradient);
		L.RegVar("colorGradientPreset", get_colorGradientPreset, set_colorGradientPreset);
		L.RegVar("spriteAsset", get_spriteAsset, set_spriteAsset);
		L.RegVar("tintAllSprites", get_tintAllSprites, set_tintAllSprites);
		L.RegVar("styleSheet", get_styleSheet, set_styleSheet);
		L.RegVar("textStyle", get_textStyle, set_textStyle);
		L.RegVar("overrideColorTags", get_overrideColorTags, set_overrideColorTags);
		L.RegVar("faceColor", get_faceColor, set_faceColor);
		L.RegVar("outlineColor", get_outlineColor, set_outlineColor);
		L.RegVar("outlineWidth", get_outlineWidth, set_outlineWidth);
		L.RegVar("fontSize", get_fontSize, set_fontSize);
		L.RegVar("fontWeight", get_fontWeight, set_fontWeight);
		L.RegVar("pixelsPerUnit", get_pixelsPerUnit, null);
		L.RegVar("enableAutoSizing", get_enableAutoSizing, set_enableAutoSizing);
		L.RegVar("fontSizeMin", get_fontSizeMin, set_fontSizeMin);
		L.RegVar("fontSizeMax", get_fontSizeMax, set_fontSizeMax);
		L.RegVar("fontStyle", get_fontStyle, set_fontStyle);
		L.RegVar("isUsingBold", get_isUsingBold, null);
		L.RegVar("horizontalAlignment", get_horizontalAlignment, set_horizontalAlignment);
		L.RegVar("verticalAlignment", get_verticalAlignment, set_verticalAlignment);
		L.RegVar("alignment", get_alignment, set_alignment);
		L.RegVar("characterSpacing", get_characterSpacing, set_characterSpacing);
		L.RegVar("wordSpacing", get_wordSpacing, set_wordSpacing);
		L.RegVar("lineSpacing", get_lineSpacing, set_lineSpacing);
		L.RegVar("lineSpacingAdjustment", get_lineSpacingAdjustment, set_lineSpacingAdjustment);
		L.RegVar("paragraphSpacing", get_paragraphSpacing, set_paragraphSpacing);
		L.RegVar("characterWidthAdjustment", get_characterWidthAdjustment, set_characterWidthAdjustment);
		L.RegVar("enableWordWrapping", get_enableWordWrapping, set_enableWordWrapping);
		L.RegVar("wordWrappingRatios", get_wordWrappingRatios, set_wordWrappingRatios);
		L.RegVar("overflowMode", get_overflowMode, set_overflowMode);
		L.RegVar("isTextOverflowing", get_isTextOverflowing, null);
		L.RegVar("firstOverflowCharacterIndex", get_firstOverflowCharacterIndex, null);
		L.RegVar("linkedTextComponent", get_linkedTextComponent, set_linkedTextComponent);
		L.RegVar("isTextTruncated", get_isTextTruncated, null);
		L.RegVar("enableKerning", get_enableKerning, set_enableKerning);
		L.RegVar("extraPadding", get_extraPadding, set_extraPadding);
		L.RegVar("richText", get_richText, set_richText);
		L.RegVar("parseCtrlCharacters", get_parseCtrlCharacters, set_parseCtrlCharacters);
		L.RegVar("isOverlay", get_isOverlay, set_isOverlay);
		L.RegVar("isOrthographic", get_isOrthographic, set_isOrthographic);
		L.RegVar("enableCulling", get_enableCulling, set_enableCulling);
		L.RegVar("ignoreVisibility", get_ignoreVisibility, set_ignoreVisibility);
		L.RegVar("horizontalMapping", get_horizontalMapping, set_horizontalMapping);
		L.RegVar("verticalMapping", get_verticalMapping, set_verticalMapping);
		L.RegVar("mappingUvLineOffset", get_mappingUvLineOffset, set_mappingUvLineOffset);
		L.RegVar("renderMode", get_renderMode, set_renderMode);
		L.RegVar("geometrySortingOrder", get_geometrySortingOrder, set_geometrySortingOrder);
		L.RegVar("isTextObjectScaleStatic", get_isTextObjectScaleStatic, set_isTextObjectScaleStatic);
		L.RegVar("vertexBufferAutoSizeReduction", get_vertexBufferAutoSizeReduction, set_vertexBufferAutoSizeReduction);
		L.RegVar("firstVisibleCharacter", get_firstVisibleCharacter, set_firstVisibleCharacter);
		L.RegVar("maxVisibleCharacters", get_maxVisibleCharacters, set_maxVisibleCharacters);
		L.RegVar("maxVisibleWords", get_maxVisibleWords, set_maxVisibleWords);
		L.RegVar("maxVisibleLines", get_maxVisibleLines, set_maxVisibleLines);
		L.RegVar("useMaxVisibleDescender", get_useMaxVisibleDescender, set_useMaxVisibleDescender);
		L.RegVar("pageToDisplay", get_pageToDisplay, set_pageToDisplay);
		L.RegVar("margin", get_margin, set_margin);
		L.RegVar("textInfo", get_textInfo, null);
		L.RegVar("havePropertiesChanged", get_havePropertiesChanged, set_havePropertiesChanged);
		L.RegVar("isUsingLegacyAnimationComponent", get_isUsingLegacyAnimationComponent, set_isUsingLegacyAnimationComponent);
		L.RegVar("transform", get_transform, null);
		L.RegVar("rectTransform", get_rectTransform, null);
		L.RegVar("autoSizeTextContainer", get_autoSizeTextContainer, set_autoSizeTextContainer);
		L.RegVar("mesh", get_mesh, null);
		L.RegVar("isVolumetricText", get_isVolumetricText, set_isVolumetricText);
		L.RegVar("bounds", get_bounds, null);
		L.RegVar("textBounds", get_textBounds, null);
		L.RegVar("flexibleHeight", get_flexibleHeight, null);
		L.RegVar("flexibleWidth", get_flexibleWidth, null);
		L.RegVar("minWidth", get_minWidth, null);
		L.RegVar("minHeight", get_minHeight, null);
		L.RegVar("maxWidth", get_maxWidth, null);
		L.RegVar("maxHeight", get_maxHeight, null);
		L.RegVar("preferredWidth", get_preferredWidth, null);
		L.RegVar("preferredHeight", get_preferredHeight, null);
		L.RegVar("renderedWidth", get_renderedWidth, null);
		L.RegVar("renderedHeight", get_renderedHeight, null);
		L.RegVar("layoutPriority", get_layoutPriority, null);
		L.RegVar("OnFontAssetRequest", get_OnFontAssetRequest, set_OnFontAssetRequest);
		L.RegVar("OnSpriteAssetRequest", get_OnSpriteAssetRequest, set_OnSpriteAssetRequest);
		L.RegVar("OnPreRenderText", get_OnPreRenderText, set_OnPreRenderText);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ForceMeshUpdate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				obj.ForceMeshUpdate();
				return 0;
			}
			else if (count == 2)
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
				obj.ForceMeshUpdate(arg0);
				return 0;
			}
			else if (count == 3)
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
				bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
				obj.ForceMeshUpdate(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: TMPro.TMP_Text.ForceMeshUpdate");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UpdateGeometry(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
			UnityEngine.Mesh arg0 = (UnityEngine.Mesh)ToLua.CheckObject(L, 2, typeof(UnityEngine.Mesh));
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			obj.UpdateGeometry(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UpdateVertexData(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				obj.UpdateVertexData();
				return 0;
			}
			else if (count == 2)
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				TMPro.TMP_VertexDataUpdateFlags arg0 = (TMPro.TMP_VertexDataUpdateFlags)ToLua.CheckObject(L, 2, typeof(TMPro.TMP_VertexDataUpdateFlags));
				obj.UpdateVertexData(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: TMPro.TMP_Text.UpdateVertexData");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetVertices(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
			UnityEngine.Vector3[] arg0 = ToLua.CheckStructArray<UnityEngine.Vector3>(L, 2);
			obj.SetVertices(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UpdateMeshPadding(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
			obj.UpdateMeshPadding();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CrossFadeColor(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 5)
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				bool arg2 = LuaDLL.luaL_checkboolean(L, 4);
				bool arg3 = LuaDLL.luaL_checkboolean(L, 5);
				obj.CrossFadeColor(arg0, arg1, arg2, arg3);
				return 0;
			}
			else if (count == 6)
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				bool arg2 = LuaDLL.luaL_checkboolean(L, 4);
				bool arg3 = LuaDLL.luaL_checkboolean(L, 5);
				bool arg4 = LuaDLL.luaL_checkboolean(L, 6);
				obj.CrossFadeColor(arg0, arg1, arg2, arg3, arg4);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: TMPro.TMP_Text.CrossFadeColor");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CrossFadeAlpha(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			bool arg2 = LuaDLL.luaL_checkboolean(L, 4);
			obj.CrossFadeAlpha(arg0, arg1, arg2);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetText(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2 && TypeChecker.CheckTypes<string>(L, 2))
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				string arg0 = ToLua.ToString(L, 2);
				obj.SetText(arg0);
				return 0;
			}
			else if (count == 2 && TypeChecker.CheckTypes<System.Text.StringBuilder>(L, 2))
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				System.Text.StringBuilder arg0 = (System.Text.StringBuilder)ToLua.ToObject(L, 2);
				obj.SetText(arg0);
				return 0;
			}
			else if (count == 2 && TypeChecker.CheckTypes<char[]>(L, 2))
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				char[] arg0 = ToLua.CheckCharBuffer(L, 2);
				obj.SetText(arg0);
				return 0;
			}
			else if (count == 3 && TypeChecker.CheckTypes<bool>(L, 3))
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				string arg0 = ToLua.CheckString(L, 2);
				bool arg1 = LuaDLL.lua_toboolean(L, 3);
				obj.SetText(arg0, arg1);
				return 0;
			}
			else if (count == 3 && TypeChecker.CheckTypes<float>(L, 3))
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				string arg0 = ToLua.CheckString(L, 2);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 3);
				obj.SetText(arg0, arg1);
				return 0;
			}
			else if (count == 4 && TypeChecker.CheckTypes<string, float, float>(L, 2))
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				string arg0 = ToLua.ToString(L, 2);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 3);
				float arg2 = (float)LuaDLL.lua_tonumber(L, 4);
				obj.SetText(arg0, arg1, arg2);
				return 0;
			}
			else if (count == 4 && TypeChecker.CheckTypes<char[], int, int>(L, 2))
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				char[] arg0 = ToLua.CheckCharBuffer(L, 2);
				int arg1 = (int)LuaDLL.lua_tonumber(L, 3);
				int arg2 = (int)LuaDLL.lua_tonumber(L, 4);
				obj.SetText(arg0, arg1, arg2);
				return 0;
			}
			else if (count == 5)
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				string arg0 = ToLua.CheckString(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				obj.SetText(arg0, arg1, arg2, arg3);
				return 0;
			}
			else if (count == 6)
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				string arg0 = ToLua.CheckString(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 6);
				obj.SetText(arg0, arg1, arg2, arg3, arg4);
				return 0;
			}
			else if (count == 7)
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				string arg0 = ToLua.CheckString(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 6);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 7);
				obj.SetText(arg0, arg1, arg2, arg3, arg4, arg5);
				return 0;
			}
			else if (count == 8)
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				string arg0 = ToLua.CheckString(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 6);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 7);
				float arg6 = (float)LuaDLL.luaL_checknumber(L, 8);
				obj.SetText(arg0, arg1, arg2, arg3, arg4, arg5, arg6);
				return 0;
			}
			else if (count == 9)
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				string arg0 = ToLua.CheckString(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 6);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 7);
				float arg6 = (float)LuaDLL.luaL_checknumber(L, 8);
				float arg7 = (float)LuaDLL.luaL_checknumber(L, 9);
				obj.SetText(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7);
				return 0;
			}
			else if (count == 10)
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				string arg0 = ToLua.CheckString(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 6);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 7);
				float arg6 = (float)LuaDLL.luaL_checknumber(L, 8);
				float arg7 = (float)LuaDLL.luaL_checknumber(L, 9);
				float arg8 = (float)LuaDLL.luaL_checknumber(L, 10);
				obj.SetText(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: TMPro.TMP_Text.SetText");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetCharArray(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				char[] arg0 = ToLua.CheckCharBuffer(L, 2);
				obj.SetCharArray(arg0);
				return 0;
			}
			else if (count == 4)
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				char[] arg0 = ToLua.CheckCharBuffer(L, 2);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
				int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
				obj.SetCharArray(arg0, arg1, arg2);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: TMPro.TMP_Text.SetCharArray");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetPreferredValues(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				UnityEngine.Vector2 o = obj.GetPreferredValues();
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 2)
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				string arg0 = ToLua.CheckString(L, 2);
				UnityEngine.Vector2 o = obj.GetPreferredValues(arg0);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 3)
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector2 o = obj.GetPreferredValues(arg0, arg1);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 4)
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				string arg0 = ToLua.CheckString(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				UnityEngine.Vector2 o = obj.GetPreferredValues(arg0, arg1, arg2);
				ToLua.Push(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: TMPro.TMP_Text.GetPreferredValues");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetRenderedValues(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				UnityEngine.Vector2 o = obj.GetRenderedValues();
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 2)
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
				UnityEngine.Vector2 o = obj.GetRenderedValues(arg0);
				ToLua.Push(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: TMPro.TMP_Text.GetRenderedValues");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetTextInfo(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
			string arg0 = ToLua.CheckString(L, 2);
			TMPro.TMP_TextInfo o = obj.GetTextInfo(arg0);
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ComputeMarginSize(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
			obj.ComputeMarginSize();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearMesh(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				obj.ClearMesh();
				return 0;
			}
			else if (count == 2)
			{
				TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
				bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
				obj.ClearMesh(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: TMPro.TMP_Text.ClearMesh");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetParsedText(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 1);
			string o = obj.GetParsedText();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_text(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			string ret = obj.text;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index text on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_textPreprocessor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.ITextPreprocessor ret = obj.textPreprocessor;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index textPreprocessor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isRightToLeftText(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.isRightToLeftText;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isRightToLeftText on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_font(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.TMP_FontAsset ret = obj.font;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index font on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_fontSharedMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			UnityEngine.Material ret = obj.fontSharedMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fontSharedMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_fontSharedMaterials(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			UnityEngine.Material[] ret = obj.fontSharedMaterials;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fontSharedMaterials on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_fontMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			UnityEngine.Material ret = obj.fontMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fontMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_fontMaterials(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			UnityEngine.Material[] ret = obj.fontMaterials;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fontMaterials on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_color(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			UnityEngine.Color ret = obj.color;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index color on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_alpha(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.alpha;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index alpha on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableVertexGradient(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.enableVertexGradient;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableVertexGradient on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_colorGradient(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.VertexGradient ret = obj.colorGradient;
			ToLua.PushValue(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index colorGradient on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_colorGradientPreset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.TMP_ColorGradient ret = obj.colorGradientPreset;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index colorGradientPreset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_spriteAsset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.TMP_SpriteAsset ret = obj.spriteAsset;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index spriteAsset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_tintAllSprites(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.tintAllSprites;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index tintAllSprites on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_styleSheet(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.TMP_StyleSheet ret = obj.styleSheet;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index styleSheet on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_textStyle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.TMP_Style ret = obj.textStyle;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index textStyle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_overrideColorTags(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.overrideColorTags;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index overrideColorTags on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_faceColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			UnityEngine.Color32 ret = obj.faceColor;
			ToLua.PushValue(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index faceColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_outlineColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			UnityEngine.Color32 ret = obj.outlineColor;
			ToLua.PushValue(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index outlineColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_outlineWidth(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.outlineWidth;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index outlineWidth on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_fontSize(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.fontSize;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fontSize on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_fontWeight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.FontWeight ret = obj.fontWeight;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fontWeight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_pixelsPerUnit(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.pixelsPerUnit;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pixelsPerUnit on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableAutoSizing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.enableAutoSizing;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableAutoSizing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_fontSizeMin(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.fontSizeMin;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fontSizeMin on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_fontSizeMax(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.fontSizeMax;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fontSizeMax on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_fontStyle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.FontStyles ret = obj.fontStyle;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fontStyle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isUsingBold(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.isUsingBold;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isUsingBold on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_horizontalAlignment(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.HorizontalAlignmentOptions ret = obj.horizontalAlignment;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index horizontalAlignment on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_verticalAlignment(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.VerticalAlignmentOptions ret = obj.verticalAlignment;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index verticalAlignment on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_alignment(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.TextAlignmentOptions ret = obj.alignment;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index alignment on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_characterSpacing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.characterSpacing;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index characterSpacing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_wordSpacing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.wordSpacing;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index wordSpacing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_lineSpacing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.lineSpacing;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index lineSpacing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_lineSpacingAdjustment(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.lineSpacingAdjustment;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index lineSpacingAdjustment on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_paragraphSpacing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.paragraphSpacing;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index paragraphSpacing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_characterWidthAdjustment(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.characterWidthAdjustment;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index characterWidthAdjustment on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableWordWrapping(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.enableWordWrapping;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableWordWrapping on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_wordWrappingRatios(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.wordWrappingRatios;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index wordWrappingRatios on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_overflowMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.TextOverflowModes ret = obj.overflowMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index overflowMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isTextOverflowing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.isTextOverflowing;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isTextOverflowing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_firstOverflowCharacterIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			int ret = obj.firstOverflowCharacterIndex;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index firstOverflowCharacterIndex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_linkedTextComponent(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.TMP_Text ret = obj.linkedTextComponent;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index linkedTextComponent on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isTextTruncated(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.isTextTruncated;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isTextTruncated on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableKerning(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.enableKerning;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableKerning on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_extraPadding(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.extraPadding;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index extraPadding on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_richText(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.richText;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index richText on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_parseCtrlCharacters(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.parseCtrlCharacters;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index parseCtrlCharacters on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isOverlay(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.isOverlay;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isOverlay on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isOrthographic(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.isOrthographic;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isOrthographic on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableCulling(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.enableCulling;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableCulling on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ignoreVisibility(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.ignoreVisibility;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ignoreVisibility on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_horizontalMapping(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.TextureMappingOptions ret = obj.horizontalMapping;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index horizontalMapping on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_verticalMapping(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.TextureMappingOptions ret = obj.verticalMapping;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index verticalMapping on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_mappingUvLineOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.mappingUvLineOffset;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index mappingUvLineOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_renderMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.TextRenderFlags ret = obj.renderMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index renderMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_geometrySortingOrder(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.VertexSortingOrder ret = obj.geometrySortingOrder;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index geometrySortingOrder on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isTextObjectScaleStatic(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.isTextObjectScaleStatic;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isTextObjectScaleStatic on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_vertexBufferAutoSizeReduction(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.vertexBufferAutoSizeReduction;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index vertexBufferAutoSizeReduction on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_firstVisibleCharacter(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			int ret = obj.firstVisibleCharacter;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index firstVisibleCharacter on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_maxVisibleCharacters(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			int ret = obj.maxVisibleCharacters;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index maxVisibleCharacters on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_maxVisibleWords(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			int ret = obj.maxVisibleWords;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index maxVisibleWords on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_maxVisibleLines(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			int ret = obj.maxVisibleLines;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index maxVisibleLines on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_useMaxVisibleDescender(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.useMaxVisibleDescender;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index useMaxVisibleDescender on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_pageToDisplay(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			int ret = obj.pageToDisplay;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pageToDisplay on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_margin(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			UnityEngine.Vector4 ret = obj.margin;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index margin on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_textInfo(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.TMP_TextInfo ret = obj.textInfo;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index textInfo on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_havePropertiesChanged(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.havePropertiesChanged;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index havePropertiesChanged on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isUsingLegacyAnimationComponent(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.isUsingLegacyAnimationComponent;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isUsingLegacyAnimationComponent on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_transform(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			UnityEngine.Transform ret = obj.transform;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index transform on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_rectTransform(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			UnityEngine.RectTransform ret = obj.rectTransform;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rectTransform on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_autoSizeTextContainer(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.autoSizeTextContainer;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoSizeTextContainer on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_mesh(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			UnityEngine.Mesh ret = obj.mesh;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index mesh on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isVolumetricText(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool ret = obj.isVolumetricText;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isVolumetricText on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_bounds(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			UnityEngine.Bounds ret = obj.bounds;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index bounds on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_textBounds(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			UnityEngine.Bounds ret = obj.textBounds;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index textBounds on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_flexibleHeight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.flexibleHeight;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index flexibleHeight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_flexibleWidth(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.flexibleWidth;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index flexibleWidth on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_minWidth(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.minWidth;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index minWidth on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_minHeight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.minHeight;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index minHeight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_maxWidth(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.maxWidth;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index maxWidth on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_maxHeight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.maxHeight;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index maxHeight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_preferredWidth(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.preferredWidth;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index preferredWidth on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_preferredHeight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.preferredHeight;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index preferredHeight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_renderedWidth(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.renderedWidth;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index renderedWidth on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_renderedHeight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float ret = obj.renderedHeight;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index renderedHeight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_layoutPriority(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			int ret = obj.layoutPriority;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index layoutPriority on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OnFontAssetRequest(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(System.Func<int,string,TMPro.TMP_FontAsset>)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OnSpriteAssetRequest(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(System.Func<int,string,TMPro.TMP_SpriteAsset>)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OnPreRenderText(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(System.Action<TMPro.TMP_TextInfo>)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_text(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			string arg0 = ToLua.CheckString(L, 2);
			obj.text = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index text on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_textPreprocessor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.ITextPreprocessor arg0 = (TMPro.ITextPreprocessor)ToLua.CheckObject<TMPro.ITextPreprocessor>(L, 2);
			obj.textPreprocessor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index textPreprocessor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isRightToLeftText(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isRightToLeftText = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isRightToLeftText on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_font(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.TMP_FontAsset arg0 = (TMPro.TMP_FontAsset)ToLua.CheckObject<TMPro.TMP_FontAsset>(L, 2);
			obj.font = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index font on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_fontSharedMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			UnityEngine.Material arg0 = (UnityEngine.Material)ToLua.CheckObject<UnityEngine.Material>(L, 2);
			obj.fontSharedMaterial = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fontSharedMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_fontSharedMaterials(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			UnityEngine.Material[] arg0 = ToLua.CheckObjectArray<UnityEngine.Material>(L, 2);
			obj.fontSharedMaterials = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fontSharedMaterials on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_fontMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			UnityEngine.Material arg0 = (UnityEngine.Material)ToLua.CheckObject<UnityEngine.Material>(L, 2);
			obj.fontMaterial = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fontMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_fontMaterials(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			UnityEngine.Material[] arg0 = ToLua.CheckObjectArray<UnityEngine.Material>(L, 2);
			obj.fontMaterials = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fontMaterials on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_color(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.color = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index color on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_alpha(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.alpha = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index alpha on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enableVertexGradient(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.enableVertexGradient = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableVertexGradient on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_colorGradient(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.VertexGradient arg0 = StackTraits<TMPro.VertexGradient>.Check(L, 2);
			obj.colorGradient = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index colorGradient on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_colorGradientPreset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.TMP_ColorGradient arg0 = (TMPro.TMP_ColorGradient)ToLua.CheckObject<TMPro.TMP_ColorGradient>(L, 2);
			obj.colorGradientPreset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index colorGradientPreset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_spriteAsset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.TMP_SpriteAsset arg0 = (TMPro.TMP_SpriteAsset)ToLua.CheckObject<TMPro.TMP_SpriteAsset>(L, 2);
			obj.spriteAsset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index spriteAsset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_tintAllSprites(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.tintAllSprites = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index tintAllSprites on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_styleSheet(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.TMP_StyleSheet arg0 = (TMPro.TMP_StyleSheet)ToLua.CheckObject<TMPro.TMP_StyleSheet>(L, 2);
			obj.styleSheet = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index styleSheet on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_textStyle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.TMP_Style arg0 = (TMPro.TMP_Style)ToLua.CheckObject<TMPro.TMP_Style>(L, 2);
			obj.textStyle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index textStyle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_overrideColorTags(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.overrideColorTags = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index overrideColorTags on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_faceColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			UnityEngine.Color32 arg0 = StackTraits<UnityEngine.Color32>.Check(L, 2);
			obj.faceColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index faceColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_outlineColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			UnityEngine.Color32 arg0 = StackTraits<UnityEngine.Color32>.Check(L, 2);
			obj.outlineColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index outlineColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_outlineWidth(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.outlineWidth = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index outlineWidth on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_fontSize(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.fontSize = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fontSize on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_fontWeight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.FontWeight arg0 = (TMPro.FontWeight)ToLua.CheckObject(L, 2, typeof(TMPro.FontWeight));
			obj.fontWeight = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fontWeight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enableAutoSizing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.enableAutoSizing = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableAutoSizing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_fontSizeMin(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.fontSizeMin = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fontSizeMin on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_fontSizeMax(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.fontSizeMax = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fontSizeMax on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_fontStyle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.FontStyles arg0 = (TMPro.FontStyles)ToLua.CheckObject(L, 2, typeof(TMPro.FontStyles));
			obj.fontStyle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fontStyle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_horizontalAlignment(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.HorizontalAlignmentOptions arg0 = (TMPro.HorizontalAlignmentOptions)ToLua.CheckObject(L, 2, typeof(TMPro.HorizontalAlignmentOptions));
			obj.horizontalAlignment = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index horizontalAlignment on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_verticalAlignment(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.VerticalAlignmentOptions arg0 = (TMPro.VerticalAlignmentOptions)ToLua.CheckObject(L, 2, typeof(TMPro.VerticalAlignmentOptions));
			obj.verticalAlignment = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index verticalAlignment on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_alignment(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.TextAlignmentOptions arg0 = (TMPro.TextAlignmentOptions)ToLua.CheckObject(L, 2, typeof(TMPro.TextAlignmentOptions));
			obj.alignment = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index alignment on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_characterSpacing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.characterSpacing = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index characterSpacing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_wordSpacing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.wordSpacing = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index wordSpacing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_lineSpacing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.lineSpacing = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index lineSpacing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_lineSpacingAdjustment(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.lineSpacingAdjustment = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index lineSpacingAdjustment on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_paragraphSpacing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.paragraphSpacing = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index paragraphSpacing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_characterWidthAdjustment(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.characterWidthAdjustment = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index characterWidthAdjustment on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enableWordWrapping(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.enableWordWrapping = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableWordWrapping on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_wordWrappingRatios(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.wordWrappingRatios = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index wordWrappingRatios on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_overflowMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.TextOverflowModes arg0 = (TMPro.TextOverflowModes)ToLua.CheckObject(L, 2, typeof(TMPro.TextOverflowModes));
			obj.overflowMode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index overflowMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_linkedTextComponent(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.TMP_Text arg0 = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 2);
			obj.linkedTextComponent = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index linkedTextComponent on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enableKerning(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.enableKerning = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableKerning on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_extraPadding(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.extraPadding = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index extraPadding on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_richText(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.richText = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index richText on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_parseCtrlCharacters(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.parseCtrlCharacters = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index parseCtrlCharacters on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isOverlay(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isOverlay = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isOverlay on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isOrthographic(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isOrthographic = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isOrthographic on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enableCulling(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.enableCulling = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableCulling on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ignoreVisibility(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.ignoreVisibility = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ignoreVisibility on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_horizontalMapping(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.TextureMappingOptions arg0 = (TMPro.TextureMappingOptions)ToLua.CheckObject(L, 2, typeof(TMPro.TextureMappingOptions));
			obj.horizontalMapping = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index horizontalMapping on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_verticalMapping(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.TextureMappingOptions arg0 = (TMPro.TextureMappingOptions)ToLua.CheckObject(L, 2, typeof(TMPro.TextureMappingOptions));
			obj.verticalMapping = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index verticalMapping on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_mappingUvLineOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.mappingUvLineOffset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index mappingUvLineOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_renderMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.TextRenderFlags arg0 = (TMPro.TextRenderFlags)ToLua.CheckObject(L, 2, typeof(TMPro.TextRenderFlags));
			obj.renderMode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index renderMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_geometrySortingOrder(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			TMPro.VertexSortingOrder arg0 = (TMPro.VertexSortingOrder)ToLua.CheckObject(L, 2, typeof(TMPro.VertexSortingOrder));
			obj.geometrySortingOrder = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index geometrySortingOrder on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isTextObjectScaleStatic(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isTextObjectScaleStatic = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isTextObjectScaleStatic on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_vertexBufferAutoSizeReduction(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.vertexBufferAutoSizeReduction = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index vertexBufferAutoSizeReduction on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_firstVisibleCharacter(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.firstVisibleCharacter = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index firstVisibleCharacter on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_maxVisibleCharacters(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.maxVisibleCharacters = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index maxVisibleCharacters on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_maxVisibleWords(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.maxVisibleWords = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index maxVisibleWords on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_maxVisibleLines(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.maxVisibleLines = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index maxVisibleLines on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_useMaxVisibleDescender(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.useMaxVisibleDescender = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index useMaxVisibleDescender on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_pageToDisplay(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.pageToDisplay = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pageToDisplay on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_margin(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			UnityEngine.Vector4 arg0 = ToLua.ToVector4(L, 2);
			obj.margin = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index margin on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_havePropertiesChanged(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.havePropertiesChanged = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index havePropertiesChanged on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isUsingLegacyAnimationComponent(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isUsingLegacyAnimationComponent = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isUsingLegacyAnimationComponent on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_autoSizeTextContainer(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.autoSizeTextContainer = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoSizeTextContainer on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isVolumetricText(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_Text obj = (TMPro.TMP_Text)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isVolumetricText = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isVolumetricText on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_OnFontAssetRequest(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'TMPro.TMP_Text.OnFontAssetRequest' can only appear on the left hand side of += or -= when used outside of the type 'TMPro.TMP_Text'");
			}

			if (arg0.op == EventOp.Add)
			{
				System.Func<int,string,TMPro.TMP_FontAsset> ev = (System.Func<int,string,TMPro.TMP_FontAsset>)arg0.func;
				TMPro.TMP_Text.OnFontAssetRequest += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				System.Func<int,string,TMPro.TMP_FontAsset> ev = (System.Func<int,string,TMPro.TMP_FontAsset>)arg0.func;
				TMPro.TMP_Text.OnFontAssetRequest -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_OnSpriteAssetRequest(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'TMPro.TMP_Text.OnSpriteAssetRequest' can only appear on the left hand side of += or -= when used outside of the type 'TMPro.TMP_Text'");
			}

			if (arg0.op == EventOp.Add)
			{
				System.Func<int,string,TMPro.TMP_SpriteAsset> ev = (System.Func<int,string,TMPro.TMP_SpriteAsset>)arg0.func;
				TMPro.TMP_Text.OnSpriteAssetRequest += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				System.Func<int,string,TMPro.TMP_SpriteAsset> ev = (System.Func<int,string,TMPro.TMP_SpriteAsset>)arg0.func;
				TMPro.TMP_Text.OnSpriteAssetRequest -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_OnPreRenderText(IntPtr L)
	{
		try
		{
			TMPro.TMP_Text obj = (TMPro.TMP_Text)ToLua.CheckObject(L, 1, typeof(TMPro.TMP_Text));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'TMPro.TMP_Text.OnPreRenderText' can only appear on the left hand side of += or -= when used outside of the type 'TMPro.TMP_Text'");
			}

			if (arg0.op == EventOp.Add)
			{
				System.Action<TMPro.TMP_TextInfo> ev = (System.Action<TMPro.TMP_TextInfo>)arg0.func;
				obj.OnPreRenderText += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				System.Action<TMPro.TMP_TextInfo> ev = (System.Action<TMPro.TMP_TextInfo>)arg0.func;
				obj.OnPreRenderText -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

