ZhanLingActView = ZhanLingActView or BaseClass(SafeBaseView)
function ZhanLingActView:__init()
	self:SetMaskBg(true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:AddViewResource(0, "uis/view/zhanling_ui_prefab", "layout_zhanling_act")
end

function ZhanLingActView:LoadCallBack()
    self.nomax_reward_list = AsyncBaseGrid.New()
    self.nomax_reward_list:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list["nomax_reward_grid"]})
    self.nomax_reward_list:SetStartZeroIndex(false)

	self.max_reward_list = AsyncBaseGrid.New()
	self.max_reward_list:CreateCells({col = 3, change_cells_num = 1, list_view = self.node_list["max_reward_list"]})
	self.max_reward_list:SetStartZeroIndex(false)

    -- local item_count = self.node_list["max_reward_list"].transform.childCount
    -- self.max_reward_list = {}
    -- for i = 1, item_count do
    --     self.max_reward_list[i] = {}
    --     self.max_reward_list[i].obj = self.node_list["max_reward_list"]:FindObj("ItemCell_" .. i)
    --     self.max_reward_list[i].cell = ItemCell.New(self.max_reward_list[i].obj)
    -- end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	-- 模型显示
	-- if not self.act_model_display then
	-- 	local item_bundle = "uis/view/zhanling_ui_prefab"
	-- 	local item_asset = "zhanling_display_item"
	-- 	self.act_model_display = OperationActRender.New(self.node_list.act_model_root, item_bundle, item_asset)
	-- 	local offset_setting = MODEL_OFFSET_TYPE.NORMALIZE
	-- 	self.act_model_display:SetModelType(MODEL_CAMERA_TYPE.TIPS, offset_setting)
	-- end

    XUI.AddClickEventListener(self.node_list["btn_buy_zhanling"], BindTool.Bind(self.OnClickBuyZhanlingBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_show_tip"], BindTool.Bind(self.OnClickDescBtn, self))
	XUI.AddClickEventListener(self.node_list["bottom_desc_mask"], BindTool.Bind(self.OnClickCloseBtn, self))
end

function ZhanLingActView:ReleaseCallBack()
	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

    if self.nomax_reward_list then
		self.nomax_reward_list:DeleteMe()
		self.nomax_reward_list = nil
	end

    if self.max_reward_list then
		self.max_reward_list:DeleteMe()
        self.max_reward_list = nil
    end

	if self.buy_remind_alert ~= nil then
		self.buy_remind_alert:DeleteMe()
		self.buy_remind_alert = nil
	end

	if self.act_model_display then
		self.act_model_display:DeleteMe()
		self.act_model_display = nil
	end

end

function ZhanLingActView:OnFlush()
	-- 是否激活
	local is_act = ZhanLingWGData.Instance:GetIsActHighZhanLing()
	if is_act then
		self:Close()
		return
	end

	--所有战令奖励
    --local all_nomax_reward = ZhanLingWGData.Instance:GetAllRewardList()
	--local silver_list = ZhanLingWGData.Instance:GetBuyPriceCfg(1)
	--local gold_list = ZhanLingWGData.Instance:GetBuyPriceCfg(2)

	local silver_list, gold_list = ZhanLingWGData.Instance:GetBuyShowItemLst()
    self.nomax_reward_list:SetDataList(silver_list)
	self.max_reward_list:SetDataList(gold_list)

	--最高级固定奖励
    -- local after_max_reward = ZhanLingWGData.Instance:GetAfterMaxLevelRewardList()
    -- for k, v in pairs(self.max_reward_list) do
    --     if after_max_reward[k] then
    --         v.obj:SetActive(true)
    --         v.cell:SetData(after_max_reward[k])
    --     else
    --         v.obj:SetActive(false)
    --     end
    -- end

    local other_cfg = ZhanLingWGData.Instance:GetZhanLingOtherCfg()
	self.node_list.lingyu_image:SetActive(other_cfg.unlock_type == 2)
	--local x_value = other_cfg.unlock_type == 2 and 16 or 0
	local btn_x_value = other_cfg.unlock_type == 2 and 16 or 0
	--self.node_list.price_value.rect.anchoredPosition = Vector2(x_value, -16)
	self.node_list.btn_buy_text.rect.anchoredPosition = Vector2(btn_x_value, 0)
    if other_cfg.unlock_type == 1 then -- 直购
		local price = RoleWGData.GetPayMoneyStr(other_cfg.unlock_cost, other_cfg.rmb_type, other_cfg.rmb_seq)
		local price_show = string.format(Language.ZhanLing.BuyButton, price) 
	   	self.node_list.btn_buy_text.text.text = price_show --购买价格
		--self.node_list.price_value.text.text = other_cfg.show_value --总价值
	elseif other_cfg.unlock_type == 2 then -- 灵玉
		self.node_list.btn_buy_text.text.text =  string.format(Language.ZhanLing.BuyButton, other_cfg.unlock_cost)--购买价格
		--self.node_list.price_value.text.text =  other_cfg.show_value--总价值
	end

	local show_model_cfg = ZhanLingWGData.Instance:GetActViewShowModelCfg()
	if self.act_model_display and show_model_cfg then
		local data = {}
		data.should_ani = true
		data.render_type = show_model_cfg.guding_mark - 1
		data.item_id = show_model_cfg.buy_model_item_id
		data.model_click_func = function ()
            TipWGCtrl.Instance:OpenItem({item_id = show_model_cfg.buy_model_item_id})
        end

		self.act_model_display:SetData(data)
	end

	self.node_list.final_reward_name.text.text = show_model_cfg and show_model_cfg.art_fonts or ""

	self.node_list.desc1.text.text = Language.ZhanLing.BuyDesc1
	self.node_list.desc2.text.text = Language.ZhanLing.BuyDesc2
	self.node_list.desc3.text.text = Language.ZhanLing.BuyDesc3

	local zhanling_info = ZhanLingWGData.Instance:GetZhanLingInfo()
	XUI.SetButtonEnabled(self.node_list.btn_buy_zhanling, not zhanling_info.act_high)
end

function ZhanLingActView:OnClickBuyZhanlingBtn()
	local other_cfg = ZhanLingWGData.Instance:GetZhanLingOtherCfg()
	local role_gold = GameVoManager.Instance:GetMainRoleVo().gold

	if other_cfg.unlock_type == 1 then
		RechargeWGCtrl.Instance:Recharge(other_cfg.unlock_cost, other_cfg.rmb_type, other_cfg.rmb_seq)
	elseif other_cfg.unlock_type == 2 then
		if nil == self.buy_remind_alert then
			self.buy_remind_alert = Alert.New(nil, nil, nil, nil, true)
			self.buy_remind_alert:SetCheckBoxDefaultSelect(false)
		end

		local cost_value = other_cfg and other_cfg.unlock_cost or 0
		self.buy_remind_alert:SetLableString(string.format(Language.ZhanLing.BuyHighZhanLingTips1, cost_value))
		self.buy_remind_alert:SetOkFunc(function()
			if role_gold < cost_value then
				VipWGCtrl.Instance:OpenTipNoGold()
			else
				ZhanLingWGCtrl.Instance:SendZhanLingRequest(ZhanLingOperType.BuyHighZhanLing)
				self:Close()
			end
		end)

		self.buy_remind_alert:Open()
	end
end

function ZhanLingActView:OnClickDescBtn()
	self.node_list.desc_content:SetActive(true)
	self.node_list.bottom_desc_mask:SetActive(true)
end

function ZhanLingActView:OnClickCloseBtn()
	self.node_list.desc_content:SetActive(false)
	self.node_list.bottom_desc_mask:SetActive(false)
end