AssignmentView = AssignmentView or BaseClass(SafeBaseView)

function AssignmentView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/offlinerest_ui_prefab", "layout_assignment")

    self.select_assign_tab = AssignmentWGData.ASSIGN_TAB_TYPE.MY
    self.select_assign_toggle = 0
    self.select_assign_huanshou_type = 0
end

function AssignmentView:OpenCallBack()
    AssignmentWGCtrl.Instance:SendTaskReq(ASSIGN_TASK_OPERATE_TASK.INFO)
    AssignmentWGCtrl.Instance:SendRentReq(ASSIGN_RENT_OPERATE_TASK.INFO)
    AssignmentWGCtrl.Instance:SendRentReq(ASSIGN_RENT_OPERATE_TASK.SELF_RENT)
end

function AssignmentView:ReleaseCallBack()
    if self.task_list then
        self.task_list:DeleteMe()
        self.task_list = nil
    end

    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end

    if self.assign_grid then
        self.assign_grid:DeleteMe()
        self.assign_grid = nil
    end

    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
    
    if self.fashion_cell_list then
        for k, v in pairs(self.fashion_cell_list) do
            v:DeleteMe()
        end
        self.fashion_cell_list = nil
    end

    if self.flush_item_cell then
        self.flush_item_cell:DeleteMe()
        self.flush_item_cell = nil
    end

    -- if self.common_reward_close_event then
	-- 	GlobalEventSystem:UnBind(self.common_reward_close_event)
	-- 	self.common_reward_close_event = nil
	-- end

    self:CleanTimer1()
    self:CleanGuideTimer()


    self.fashion_cell_data_list = nil
    self.select_assign_tab = AssignmentWGData.ASSIGN_TAB_TYPE.MY
    self.select_assign_toggle = 0
    self.select_assign_huanshou_type = 0 
end

function AssignmentView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.flush_btn, BindTool.Bind(self.OnRefreshBtnClick, self))
    XUI.AddClickEventListener(self.node_list.one_key_btn, BindTool.Bind(self.OnOneKeyBtnClick, self))
    XUI.AddClickEventListener(self.node_list.start_btn, BindTool.Bind(self.OnStartBtnClick, self))
    XUI.AddClickEventListener(self.node_list.compilt_btn, BindTool.Bind(self.OnFinishBtnClick, self))
    XUI.AddClickEventListener(self.node_list.rule_btn, BindTool.Bind(self.OnRuleBtnClick, self))
    XUI.AddClickEventListener(self.node_list.auto_buy_toggle, BindTool.Bind(self.OnAutoBuyToggleClick, self))
    XUI.AddClickEventListener(self.node_list.bg, BindTool.Bind(self.OnClickBg, self))
    XUI.AddClickEventListener(self.node_list.go_assign_btn, BindTool.Bind(self.OnGoAssignBtnClick, self))
    XUI.AddClickEventListener(self.node_list.btn_assign_close, BindTool.Bind(self.OnClickBtnAssignClose, self))
    
    -- for i = 0, AssignmentWGData.FASHION_TYPE_MAX do
    --     XUI.AddClickEventListener(self.node_list["assign_toggle" .. i], BindTool.Bind(self.OnClickAssignToggle, self, i))
    -- end

    -- for i = 1, AssignmentWGData.ASSIGN_TAB_TYPE_MAX do
    --     XUI.AddClickEventListener(self.node_list["assign_tab" .. i], BindTool.Bind(self.OnClickAssigTab, self, i))
    -- end

    for i = 1, AssignmentWGData.MAX_TASK_CONDITION do
        XUI.AddClickEventListener(self.node_list["assign_need_img" .. i], BindTool.Bind(self.OnAssignNeedClick, self, i))
    end

    for i = 0, 5 do
        XUI.AddClickEventListener(self.node_list["compose_bag_type_" .. i], BindTool.Bind(self.OnClickAssignHuanShouTypeToggle, self, i))
    end

    if not self.task_list then
        self.task_list = AsyncListView.New(AssignTaskRender, self.node_list["task_list"])
        self.task_list:SetSelectCallBack(BindTool.Bind1(self.OnTaskSelect, self))
    end

    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
        self.reward_list:SetStartZeroIndex(true)
    end

    if self.assign_grid == nil then
        local bundle = "uis/view/offlinerest_ui_prefab"
        local asset = "assign_cell"
		self.assign_grid = AssignmentGrid.New()
		self.assign_grid:CreateCells({
			col = 5, 
			change_cells_num = 1, 
			list_view = self.node_list["assign_grid"],
			assetBundle = bundle, 
			assetName = asset, 
			itemRender = AssignGridItemRender})
		self.assign_grid:SetStartZeroIndex(false)
        self.assign_grid:SetIsMultiSelect(true)
        self.assign_grid:SetSelectCallBack(BindTool.Bind1(self.OnClickAssign, self))
	end

    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
	end
    
    self.fashion_cell_list = {}
    for i = 1, AssignmentWGData.MAX_TASK_CONDITION do
        local fashion_cell = self.node_list.assign_group2:FindObj("fashion_cell" .. i)
        if fashion_cell then
            local cell = AssignFashionRender.New(fashion_cell)
            cell:SetClickCallBack(BindTool.Bind1(self.OnClickFashionCell, self))
            cell:SetIndex(i)
            self.fashion_cell_list[i] = cell
        end
    end

    if self.flush_item_cell == nil then
        self.flush_item_cell = ItemCell.New(self.node_list.flush_item_cell)
    end
end

function AssignmentView:ShowIndexCallBack()
    self:InitTweenState()
end

function AssignmentView:OnFlush(param_t)
    for k, v in pairs(param_t) do
		if "all" == k then
			self:FlushTaskView()
            self:FlushExp()
            self:AssignmentViewAnimation()
        elseif "AllRent" == k then
            self:FlushAssignRoot()
        elseif "FlushExp" == k then
            self:FlushExp()
        elseif "AllTask" == k then
            self:FlushTaskView()
            self:FlushRightView()
        elseif "RefershItem" == k then
            self:FlushFlushBtn(self.node_list.auto_buy_toggle.toggle.isOn)
            self:FlushRefreshNum()
        elseif "HuanShouDataChange" == k then
            self:FlushAssignHuanShouDataListInfo()
		end
        
	end
end

-- function AssignmentView:OnCommonRewardClose()
-- 	FunctionGuide.Instance:CheckConfigGuide()
-- end

function AssignmentView:OnAutoBuyToggleClick(is_on)
    self.node_list.auto_toggle_img:CustomSetActive(is_on)
    self:FlushFlushBtn(is_on)
end

function AssignmentView:InitTweenState()
	self.do_assignment_tween = true
end

--刷新按钮的文本状态刷新
function AssignmentView:FlushFlushBtn(is_on)
    local item_id = AssignmentWGData.Instance:GetRefreshItemId()
    local fresh_need_num = AssignmentWGData.Instance:GetRefreshItemNum()
    local has_num = ItemWGData.Instance:GetItemNumInBagById(item_id)

    local has_times = AssignmentWGData.Instance:GetHasFreeRefreshTimes()
    local text_state = not has_times and is_on and has_num < fresh_need_num
    self.node_list.xianyu_descript:CustomSetActive(text_state)
    self.node_list.flush_btn_descirpt:CustomSetActive(not text_state)
    if has_times then
        self.node_list.flush_btn_descirpt.text.text = Language.Assignment.FreshBtnStr2
    elseif is_on and fresh_need_num > has_num then
        self.node_list.flush_need_text.text.text = AssignmentWGData.Instance:GetRefreshNeedXianyu()
    else
        self.node_list.flush_btn_descirpt.text.text = Language.Assignment.FreshBtnStr
    end
end

------------------------------------------------right_root  start--------------------------------------------------
function AssignmentView:OnFinishBtnClick()--领取奖励
    local task_data = AssignmentWGData.Instance:GetTaskData(self.select_task_id or -1).cfg
    if IsEmptyTable(task_data) then
        return
    end

    local state = AssignmentWGData.Instance:GetTaskState(task_data.id)
    if not (state == AssignmentWGData.TASK_STATUS.ASSIGN) then
        return
    end

    local has_special_task = AssignmentWGData.Instance:GetHasSpecialTask()
    local total_time = AssignmentWGData.Instance:GetTaskRestTime(task_data.id)
    if total_time > 0 and not has_special_task then
        local enough = RoleWGData.Instance:GetIsEnoughUseGold(task_data.quick_finish_cost_money)
        if enough then
            AssignmentWGCtrl.Instance:SendTaskReq(ASSIGN_TASK_OPERATE_TASK.QUICK_FINISH, task_data.id)
        else
            VipWGCtrl.Instance:OpenTipNoGold()
        end
    elseif total_time <= 0 then
        AssignmentWGCtrl.Instance:SendTaskReq(ASSIGN_TASK_OPERATE_TASK.TASK_REWARD, task_data.id)
    end
end

function AssignmentView:OnStartBtnClick()
    -- self.node_list.assign_root:CustomSetActive(false)
    -- self.node_list.btn_assign_close:CustomSetActive(false)

    -- local has_nil = false
    -- local has_my_assign = false
    -- for k, v in pairs(self.fashion_cell_data_list) do
    --     if v.data == nil then
    --         has_nil = true
    --     end

    --     if v.data and v.data.status == -1 then
    --         has_my_assign = true
    --     end
    -- end

    -- if not has_my_assign then
    --     SysMsgWGCtrl.Instance:ErrorRemind(Language.Assignment.NoMyAssign)
    --     return
    -- end
    
    -- if not has_nil and not IsEmptyTable(self.fashion_cell_data_list) then
    --     local task_cfg = AssignmentWGData.Instance:GetTaskData(self.select_task_id or -1).cfg

    --     local exp = AssignmentWGData.Instance:GetTaskExp()
    --     local need_exp = task_cfg.cost_exp or 0
    --     if exp < need_exp then
    --         SysMsgWGCtrl.Instance:ErrorRemind(Language.Assignment.NoExp)
    --         return
    --     end

    --     local param_list1 = {}
    --     local param_list2 = {}
    --     for k, v in pairs(self.fashion_cell_data_list) do
    --         local id = v.data.is_my_fashion == 0 and v.data.guild_index or v.data.cfg.seq
    --         local type = v.data.is_my_fashion == 0 and AssignmentWGData.FASHION_TYPE.RENT or v.data.cfg.type--雇佣的外形type固定为4
    --         table.insert(param_list1, type)
    --         table.insert(param_list2, id)
    --     end
    --     AssignmentWGCtrl.Instance:SendTaskReq(ASSIGN_TASK_OPERATE_TASK.ASSIN, task_cfg.id, param_list1, param_list2)
    --     self:ClearFashionDataList()
    -- else
    --     SysMsgWGCtrl.Instance:ErrorRemind(Language.Assignment.AssignNoEnough)
    -- end

    self.node_list.assign_root:CustomSetActive(false)
    self.node_list.btn_assign_close:CustomSetActive(false)

    local has_nil = false
    local has_my_assign = false
    for k, v in pairs(self.fashion_cell_data_list) do
        if v.data == nil then
            has_nil = true
        end

        if v.data and v.data.status == -1 then
            has_my_assign = true
        end
    end

    if not has_my_assign then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Assignment.NoMyAssign)
        return
    end
    
    if not has_nil and not IsEmptyTable(self.fashion_cell_data_list) then
        local task_cfg = AssignmentWGData.Instance:GetTaskData(self.select_task_id or -1).cfg

        local exp = AssignmentWGData.Instance:GetTaskExp()
        local need_exp = task_cfg.cost_exp or 0
        if exp < need_exp then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Assignment.NoExp)
            return
        end

        local param_list1 = {}
        local param_list2 = {}
        for k, v in pairs(self.fashion_cell_data_list) do
            -- local id = v.data.is_my_fashion == 0 and v.data.guild_index or v.data.cfg.seq
            -- local type = v.data.is_my_fashion == 0 and AssignmentWGData.FASHION_TYPE.RENT or v.data.cfg.type--雇佣的外形type固定为4

            local id = v.data.bag_index
            local type = AssignmentWGData.FASHION_TYPE.HUANSHOU
            table.insert(param_list1, type)
            table.insert(param_list2, id)  -- 幻兽背包列表的那个序号
        end

        AssignmentWGCtrl.Instance:SendTaskReq(ASSIGN_TASK_OPERATE_TASK.ASSIN, task_cfg.id, param_list1, param_list2)
        self:ClearFashionDataList()
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Assignment.AssignNoEnough)
    end
end

function AssignmentView:OnOneKeyBtnClick()
    --print_error("OnOneKeyBtnClick")
    -- self.node_list.assign_root:CustomSetActive(false)
    -- self.node_list.btn_assign_close:CustomSetActive(false)
    -- for k, v in pairs(self.fashion_cell_data_list) do
    --     v.data = nil
    -- end

    -- local task_data = AssignmentWGData.Instance:GetTaskData(self.select_task_id or -1)
    -- local data_list = AssignmentWGData.Instance:GetOneKeyDataList(task_data.task_id) or {}
    -- for i = 1, #data_list do
    --     self:InsertFashionToCellDataList(data_list[i])
    -- end 

    -- self:FlushFashionCell()

    -- if #data_list < #self.fashion_cell_data_list then
    --     SysMsgWGCtrl.Instance:ErrorRemind(Language.Assignment.ConditionNotEnough)
    -- end

    self.node_list.assign_root:CustomSetActive(false)
    self.node_list.btn_assign_close:CustomSetActive(false)
    for k, v in pairs(self.fashion_cell_data_list) do
        v.data = nil
    end

    local task_data = AssignmentWGData.Instance:GetTaskData(self.select_task_id or -1)
    local data_list = AssignmentWGData.Instance:GetBeastOneKeyDataList(task_data.task_id) or {}

    for i = 1, #data_list do
        self:InsertFashionToCellDataList(data_list[i])
    end 

    self:FlushFashionCell()

    if #data_list < #self.fashion_cell_data_list then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Assignment.ConditionNotEnough)

        TipWGCtrl.Instance:OpenAlertTips(Language.Assignment.ConditionNotEnough2, function()
			ViewManager.Instance:Open(GuideModuleName.ControlBeastsPrizeDrawWGView)
		end,nil,nil,nil,nil,nil,Language.Common.GoTo)
    end
end

function AssignmentView:FlushFashionCell()
    local cell_list = self.fashion_cell_list
    -- print_error(self.fashion_cell_data_list)
    for i = 1, #cell_list do
        cell_list[i]:SetData(self.fashion_cell_data_list[i])
    end
end

function AssignmentView:OnClickFashionCell(item, isOn)
    local data_list = self.task_list:GetDataList()
    if IsEmptyTable(data_list) then
        return
    end

    self.node_list.condition_tips:CustomSetActive(false)
    --外形选择界面打开的情况点击则关闭外形选择界面
    if self.node_list.assign_root:GetActive() then
        self.node_list.assign_root:CustomSetActive(false)
        self.node_list.btn_assign_close:CustomSetActive(false)
        return
    end
    
    --派遣中不能进行外形选择
    local state = AssignmentWGData.Instance:GetTaskState(self.select_task_id or -1)
    if state ~= AssignmentWGData.TASK_STATUS.NORMAL then
        return
    end

    if nil ~= item and item.data and item.index and nil ~= self.fashion_cell_data_list[item.index].data then
        self.fashion_cell_data_list[item.index].data = nil
        self:FlushFashionCell()
        return
    end

    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
    if main_role_vo.guild_id <= 0 then--没加入工会直接打开选择外形弹窗
        self:FlushAssignRoot()
    end

    self.fashion_is_click = true
    AssignmentWGCtrl.Instance:SendRentReq(ASSIGN_RENT_OPERATE_TASK.INFO)
    AssignmentWGCtrl.Instance:SendRentReq(ASSIGN_RENT_OPERATE_TASK.SELF_RENT)
end

--刷新右边面板
function AssignmentView:FlushRightRoot(data)
    self:CleanTimer1()
    self.fashion_cell_data_list = {}

    if IsEmptyTable(data) then
        self.reward_list:SetDataList({})
        self.node_list.right_title.text.text = ""
        for i = 0, AssignmentWGData.MAX_TASK_CONDITION do
            self.node_list["assign_need_img" .. i]:CustomSetActive(false)
        end
        self.node_list.right_normal_root:CustomSetActive(false)
        return
    else
        self.node_list.right_normal_root:CustomSetActive(true)
    end

    --达成奖励
    self.reward_list:SetDataList(data.reward)
    
    --达成奖励
    local bundle, asset = ResPath.GetRawImagesPNG("a3_wtrw_rect_" .. data.color)
    self.node_list.reward_list_bg.raw_image:LoadSprite(bundle, asset)

    --右边顶头描述
    self.node_list.right_title.text.text = data.descirpt

    --刷新委托条件
    local all_condition_list = Split(data.conditions, "|")
    local max_star = -1

    for i = 1, AssignmentWGData.MAX_TASK_CONDITION do
        local condition = all_condition_list[i]
        self.node_list["assign_need_img" .. i]:CustomSetActive(condition ~= nil)
        self.fashion_cell_list[i]:SetActive(condition ~= nil)

        if condition ~= nil then
            local condition_list = Split(condition, ",")
            local need_type = tonumber(condition_list[1])
            local need_color = tonumber(condition_list[2])
            local need_beast_element = tonumber(condition_list[3])
            local need_beast_star = tonumber(condition_list[4])
            -- local bundle, asset = ResPath.GetOfflinerestImages("a3_wt_tj_" .. need_type .. "_" .. (need_color))

            if max_star < need_beast_star then
                max_star = need_beast_star
            end

            local bundle, asset = ResPath.GetOfflinerestImages("a3_hs_bq_" .. need_beast_element)
            self.node_list["assign_need_img" .. i].image:LoadSprite(bundle, asset)

            table.insert(self.fashion_cell_data_list, {need_type = need_type, need_color = need_color, need_beast_element = need_beast_element, need_beast_star = need_beast_star})
        end
    end

    -- 星级
    self.node_list["assign_need_img0"]:CustomSetActive(max_star >= 0)
    self.node_list["desc_assign_need0"].text.text = max_star

    --刷新派遣名单以下界面
    local state = AssignmentWGData.Instance:GetTaskState(data.id)
    self.node_list.right_start_root:CustomSetActive(state == AssignmentWGData.TASK_STATUS.NORMAL)
    self.node_list.right_assign_root:CustomSetActive(state == AssignmentWGData.TASK_STATUS.ASSIGN)
    
    
    --刷新任务时间显示
    self.node_list.img_need_time_bg:CustomSetActive(state == AssignmentWGData.TASK_STATUS.NORMAL)
    self.node_list.text_need_time:CustomSetActive(state == AssignmentWGData.TASK_STATUS.NORMAL)
    self.node_list.img_time_bg:CustomSetActive(state == AssignmentWGData.TASK_STATUS.ASSIGN)
    self.node_list.text_time:CustomSetActive(state == AssignmentWGData.TASK_STATUS.ASSIGN)

    if state == AssignmentWGData.TASK_STATUS.NORMAL then
        local time_str = TimeUtil.FormatSecond(data.time)
        self.node_list.text_need_time.text.text = time_str
        self.node_list.need_lilian_text.text.text = data.cost_exp
    elseif state == AssignmentWGData.TASK_STATUS.ASSIGN then
        --派遣结束倒计时
        self.node_list.text_time.text.text = Language.Assignment.Complit
        local total_time = AssignmentWGData.Instance:GetTaskRestTime(data.id)
        if total_time > 0 then
            local rest_time = AssignmentWGData.Instance:GetTaskRestTime(data.id)
            local start_time_str = string.format(Language.Assignment.Assigning, TimeUtil.FormatSecond(rest_time))
            
            self.node_list.text_time.text.text = start_time_str
            self.timer_1 = CountDown.Instance:AddCountDown(total_time, 1,
                -- 回调方法
                function(elapse_time, total_time)
                    local rest_time = AssignmentWGData.Instance:GetTaskRestTime(data.id)
                    local time_str = string.format(Language.Assignment.Assigning, TimeUtil.FormatSecond(rest_time)) 
                    self.node_list.text_time.text.text = time_str
                end,
                -- 倒计时完成回调方法
                function()
                    self.node_list.text_time.text.text = Language.Assignment.Complit
                    -- AssignmentWGCtrl.Instance:SendTaskReq(ASSIGN_TASK_OPERATE_TASK.INFO)
                end
            )  
        end

        --直接完成需要仙玉
        local has_special_task = AssignmentWGData.Instance:GetHasSpecialTask()
        self.node_list.complit_need_text.text.text = data.quick_finish_cost_money   
        self.node_list.complit_need:CustomSetActive(total_time > 0 and not has_special_task)

        --直接完成|领取 按钮文本
        local str = Language.Assignment.Lingqu
        if total_time > 0 and not has_special_task then
            str =Language.Assignment.ComplitImd
        end
        self.node_list.assign_btn_text.text.text = str

        local is_assign = state == AssignmentWGData.TASK_STATUS.ASSIGN
        --派遣完成红点
        self.node_list.complit_remind:CustomSetActive(is_assign and total_time <= 0)

        local normal_status = (not has_special_task and is_assign)
        local special_status = (has_special_task and total_time <= 0)
        --派遣完成按钮
        XUI.SetButtonEnabled(self.node_list.compilt_btn, normal_status or special_status)

        --刷新派遣名单数据
        local fashion_list = AssignmentWGData.Instance:GetTaskFashionList(data.id)

        for k, fashion in pairs(fashion_list) do
            self:InsertFashionToCellDataList(fashion)
        end
    end

    --刷新派遣名单格子
    self:FlushFashionCell()
end

function AssignmentView:ClearFashionDataList()
    for i = 1, #self.fashion_cell_data_list do
        self.fashion_cell_data_list[i].data = nil
    end
    self:FlushFashionCell()
end

function AssignmentView:OnAssignNeedClick(index)
    if self.fashion_cell_data_list and self.fashion_cell_data_list[index] then
        local cell_data = self.fashion_cell_data_list[index]
        local color = cell_data.need_color
        local color_name = Language.Common.ColorName[color]
        local fashion_name = Language.Assignment.FashionTypeName[cell_data.need_type]
        -- local color_str = ITEM_COLOR_DARK[color]
        -- local str = string.format(Language.Assignment.NeedUseTips, color_str, color_name, fashion_name)

        local str = string.format(Language.Assignment.DescNeedUseTips, fashion_name, Language.ContralBeasts.GMTypeList[cell_data.need_beast_element], cell_data.need_beast_star , color_name)
        self.node_list.condition_tips_text.text.text = str
        self.node_list.condition_tips:CustomSetActive(true)
    else
        self.node_list.condition_tips:CustomSetActive(false)
    end
end

------------------------------------------------right_root end--------------------------------------------------

------------------------------------------------assign_root start--------------------------------------------------
-- function AssignmentView:OnClickAssigTab(index)
--     if self.select_assign_tab == index then
--         return
--     end

--     if AssignmentWGData.Instance:GetHasSpecialTask() then
--         SysMsgWGCtrl.Instance:ErrorRemind(Language.Assignment.NoRent)
--         return
--     end
    
--     local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
--     if index == AssignmentWGData.ASSIGN_TAB_TYPE.OTHER and main_role_vo.guild_id <= 0 then
--         SysMsgWGCtrl.Instance:ErrorRemind(Language.Assignment.NoGuidAssign)
--         return
--     end

--     self.select_assign_tab = index
--     self:FlushAssignRootCondition()
-- end


-- function AssignmentView:OnClickAssignToggle(index)
--     if self.select_assign_toggle == index then
--         return
--     end
    
--     self.select_assign_toggle = index
--     self:FlushAssignRootCondition()
-- end

--选择派遣外形
function AssignmentView:OnClickAssign(cell)
    local data = cell and cell:GetData() or {}
    local cfg = data.cfg
    if IsEmptyTable(cfg) or IsEmptyTable(data) or IsEmptyTable(self.fashion_cell_data_list) then
        return
    end
    
    local index = -1
    local data_num = 0
    local borrow_num = 0
    for i = 1, #self.fashion_cell_data_list do
        local cell_data = self.fashion_cell_data_list[i].data
        if cell_data and AssignmentWGData.Instance:GetIsSameHuanShouData(cell_data, data) then
            index = i
        end
        
        if cell_data then
            data_num = data_num + 1
        end

        if cell_data and cell_data.is_my_fashion == 0 then
            borrow_num = borrow_num + 1
        end
    end
    
    local select_status = true
    local rent_times = AssignmentWGData.Instance:GetCanBorrowTimes()

    if index > 0 then--取消选择外形刷新派遣名单数据
        self.fashion_cell_data_list[index].data = nil
        select_status = false
        self.assign_grid:SetCancelSelectCellIndex(cell.index)
    elseif data_num >= #self.fashion_cell_data_list then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Assignment.AssignEnough)
        select_status = false
        self.assign_grid:SetCancelSelectCellIndex(cell.index)
    elseif data.is_my_fashion == 0 and rent_times <= borrow_num then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Assignment.NoBorrowTimes)
        select_status = false
        self.assign_grid:SetCancelSelectCellIndex(cell.index)
    elseif index <= 0 then
        local insert_success = self:InsertFashionToCellDataList(data)
        if not insert_success then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Assignment.AssignNoCondition)
            select_status = false
            self.assign_grid:SetCancelSelectCellIndex(cell.index)
        end
    end
    
    cell:OnSelectChange(select_status)
    self:FlushFashionCell()
end

function AssignmentView:SerachFashionBestIndex(fashion, fashion_type)
    local best_color = -1
    local best_index = -1

    for i = 1, #self.fashion_cell_data_list do
        local cell_data = self.fashion_cell_data_list[i]
        local need_color = cell_data.need_color
        local need_type = cell_data.need_type
        local need_beast_element = cell_data.need_beast_element
        local need_beast_star = cell_data.need_beast_star

        local cfg = fashion.cfg

        if fashion_type == need_type and cell_data.data == nil and cfg.color >= need_color and need_color > best_color 
        and (need_beast_element == 0 or (cfg.beast_cfg.beast_element == need_beast_element)) 
        and cfg.beast_cfg.beast_star >= need_beast_star then
            best_index = i
            best_color = need_color
        end
    end

    return best_index
end

function AssignmentView:InsertFashionToCellDataList(fashion)
    local best_index = self:SerachFashionBestIndex(fashion, fashion.cfg.type)

    if best_index <= 0 then
        best_index = self:SerachFashionBestIndex(fashion, 0)
    end

    if best_index > 0 then
        self.fashion_cell_data_list[best_index].data = fashion
    end

    return best_index > 0
end

--刷新并打开外形选择弹窗
function AssignmentView:FlushAssignRoot()
    if self.fashion_is_click then
        self.node_list.assign_root:CustomSetActive(true)
        self.node_list.btn_assign_close:CustomSetActive(true)
        self.fashion_is_click = false
    end

    if not self.select_assign_tab then
        self.select_assign_tab = AssignmentWGData.ASSIGN_TAB_TYPE.MY
    end
    -- self:FlushAssignRootCondition()
    self:FlushAssignHuanShouRootCondition()
end

--刷新选择外形弹窗
function AssignmentView:FlushAssignRootCondition()
    if IsEmptyTable(self.fashion_cell_data_list) then
        return
    end

    --刷新下面的筛选条件toggle状态
    for i = 0, 4 do
        XUI.SetGraphicGrey(self.node_list["assign_toggle" .. i], i ~= self.select_assign_toggle)
    end

    --刷新顶端tab状态
    for i = 1, AssignmentWGData.ASSIGN_TAB_TYPE_MAX do
        self.node_list["assign_select" .. i]:CustomSetActive(i == self.select_assign_tab)
    end

    self.assign_grid:CancleAllSelectCell()

    local data_list = {}
    if self.select_assign_tab == AssignmentWGData.ASSIGN_TAB_TYPE.MY then
        data_list = AssignmentWGData.Instance:GetMyAssignFashionDataList(self.select_assign_toggle)
    elseif self.select_assign_tab == AssignmentWGData.ASSIGN_TAB_TYPE.OTHER then
        data_list = AssignmentWGData.Instance:GetRentAssignFashionDataList(self.select_assign_toggle)
    end
    self.node_list.no_data:CustomSetActive(IsEmptyTable(data_list))
    self.assign_grid:SetDataList(data_list)
    
    --在新筛选条件中选中保存的数据
    for i = 1, #self.fashion_cell_data_list do
        local data = self.fashion_cell_data_list[i].data
        if data then
            local select_index = -1
            for k, v in pairs(data_list) do
                if AssignmentWGData.Instance:GetIsSameFashionData(v, data) then
                    select_index = k
                    break
                end
            end

            if select_index > 0 then
                self.assign_grid:SetSelectCellIndexByNoCancel(select_index)
            end
        end
    end

    local rent_times = AssignmentWGData.Instance:GetCanBorrowTimes()
    local color = rent_times > 0 and COLOR3B.GREEN or COLOR3B.RED
    self.node_list.assign_time_text.text.text = string.format(Language.Assignment.AssignTips, color, rent_times)
end
------------------------------------------------assign_root end--------------------------------------------------

function AssignmentView:OnRuleBtnClick()
    RuleTip.Instance:SetContent(Language.Assignment.AssignTipsStr, Language.Assignment.AssignTipsTitle)
end

function AssignmentView:OnClickBg()
    self.node_list.assign_root:CustomSetActive(false)
    self.node_list.btn_assign_close:CustomSetActive(false)
    self.node_list.condition_tips:CustomSetActive(false)
end

function AssignmentView:OnGoAssignBtnClick()
    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
    if main_role_vo.guild_id <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Assignment.GoAssignTips)
    else
        ViewManager.Instance:Open(GuideModuleName.GuildView, TabIndex.guild_assign)
    end
end

function AssignmentView:OnClickBtnAssignClose()
    self.node_list.assign_root:CustomSetActive(false)
    self.node_list.btn_assign_close:CustomSetActive(false)
end

--选中委托任务列表任务
function AssignmentView:OnTaskSelect(item)
    local data = item and item:GetData()
    if IsEmptyTable(data) then 
        return 
    end

    if self.select_task_id ~= data.id then
        self.select_task_id = data.id
    end

    self.node_list.assign_root:CustomSetActive(false)
    self.node_list.condition_tips:CustomSetActive(false)
    self.node_list.btn_assign_close:CustomSetActive(false)

    self:FlushRightRoot(data)
end

function AssignmentView:CleanGuideTimer()
    if self.guide_timer and CountDown.Instance:HasCountDown(self.guide_timer) then
        CountDown.Instance:RemoveCountDown(self.guide_timer)
        self.guide_timer = nil
    end
end

function AssignmentView:CleanTimer1()
    if self.timer_1 and CountDown.Instance:HasCountDown(self.timer_1) then
        CountDown.Instance:RemoveCountDown(self.timer_1)
        self.timer_1 = nil
    end
end



function AssignmentView:FlushRightView()
    local data_list = self.task_list:GetDataList() or {}
    local index = 1
    for i, v in ipairs(data_list) do
        local state = AssignmentWGData.Instance:GetTaskState(v.id)
        if state == AssignmentWGData.TASK_STATUS.NORMAL then
            local fashion_cell_data_list = {}
            local one_key_data_list = AssignmentWGData.Instance:GetBeastOneKeyDataList(v.id) or {}

            local all_condition_list = Split(v.conditions, "|")
            local need_beast_num = all_condition_list and #all_condition_list or 99999
            local exp = AssignmentWGData.Instance:GetTaskExp()
            local remind = #one_key_data_list >= need_beast_num and exp >= v.cost_exp
            if remind then
                index = i
                break
            end
        elseif state == AssignmentWGData.TASK_STATUS.ASSIGN then
            local rest_time = AssignmentWGData.Instance:GetTaskRestTime(v.id)
            if rest_time <= 0 then
                index = i
                break
            end
        end
    end

    local data = data_list[index] or {}--默认选中第一个
    self.select_task_id = data.id
    self.task_list:SelectIndex(index)

    self:FlushRightRoot(data)
end

function AssignmentView:OnRefreshBtnClick()

    local num = 0
    local data_list = AssignmentWGData.Instance:GetTaskDataList()
    for k, v in pairs(data_list) do
        local state = AssignmentWGData.Instance:GetTaskState(v.task_id)
        if state ~= AssignmentWGData.TASK_STATUS.NORMAL then
            num = num + 1
        end
    end
    if num >= AssignmentWGData.MAX_TASK_NUM then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Assignment.AssignTaskMax)
        return
    end


    local has_times = AssignmentWGData.Instance:GetHasFreeRefreshTimes()
    if has_times then
        AssignmentWGCtrl.Instance:SendTaskReq(ASSIGN_TASK_OPERATE_TASK.TASK_REFRESH, 0)
        return
    end

    local item_id = AssignmentWGData.Instance:GetRefreshItemId()
    local fresh_need_num = AssignmentWGData.Instance:GetRefreshItemNum()
    local has_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
    if has_num >= fresh_need_num then
        AssignmentWGCtrl.Instance:SendTaskReq(ASSIGN_TASK_OPERATE_TASK.TASK_REFRESH, 1)
        return
    end

    local is_auto_buy = self.node_list.auto_buy_toggle.toggle.isOn
    local consume = AssignmentWGData.Instance:GetRefreshNeedXianyu()
    local enough_xianyu = RoleWGData.Instance:GetIsEnoughUseGold(consume)
    if enough_xianyu and is_auto_buy then
        AssignmentWGCtrl.Instance:SendTaskReq(ASSIGN_TASK_OPERATE_TASK.TASK_REFRESH, 2)
        return
    end

    
    if is_auto_buy then
        VipWGCtrl.Instance:OpenTipNoGold()
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Assignment.RefreshNoItem)
    end
end

function AssignmentView:FlushExp()
    local exp = AssignmentWGData.Instance:GetTaskExp()
    local max_exp = AssignmentWGData.Instance:GetMaxTaskExp()

    self.node_list.lilian_slider.slider.value = exp / max_exp
    self.node_list.lilian_slider_text.text.text = string.format(Language.Assignment.LilianSpliderStr, exp, max_exp)
end

--刷新左边任务列表
function AssignmentView:FlushTaskView()
    self.node_list.assign_root:CustomSetActive(false)
    self.node_list.btn_assign_close:CustomSetActive(false)
    self.node_list.condition_tips:CustomSetActive(false)

    local data_list = nil
    local has_special_task = AssignmentWGData.Instance:GetHasSpecialTask()

    self.node_list.left_special_root:CustomSetActive(has_special_task)
    self.node_list.left_normal_root:CustomSetActive(not has_special_task)

    if has_special_task then
        --刷新特殊任务进度界面
        local task_id = AssignmentWGData.Instance:GetCurrentSpecialTaskId()
        for i = 1, AssignmentWGData.MAX_SPECIAL_TASK do
            self.node_list["is_specical_finish" .. i]:CustomSetActive(task_id > (i - 1))
        end

        self.node_list.unlock_slider.slider.value = task_id / (AssignmentWGData.MAX_SPECIAL_TASK - 1)
    else
        local item_id = AssignmentWGData.Instance:GetRefreshItemId()
        --刷新按钮
        self.flush_item_cell:SetData({item_id = item_id})
        self:FlushFlushBtn(self.node_list.auto_buy_toggle.toggle.isOn)
        self:FlushRefreshNum()
    end

    data_list = AssignmentWGData.Instance:GetTaskCfgList() or {}
    self.task_list:SetDataList(data_list)
end

function AssignmentView:FlushRefreshNum()
    local has_special_task = AssignmentWGData.Instance:GetHasSpecialTask()
    if has_special_task then return end

    local item_id = AssignmentWGData.Instance:GetRefreshItemId()
    local fresh_need_num = AssignmentWGData.Instance:GetRefreshItemNum()
    local has_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
    local color = has_num >= fresh_need_num and COLOR3B.GREEN or COLOR3B.RED
    local str = string.format(Language.Assignment.RefreshItemNum, color, has_num, fresh_need_num)
    self.node_list.flush_item_num.text.text = str
end

function AssignmentView:AssignmentViewAnimation()
	if not self.do_assignment_tween then
		return
	end
	local tween_info = UITween_CONSTS.Assignment_Road
	UITween.CleanAllTween(GuideModuleName.AssignmentView)

	UITween.FakeHideShow(self.node_list.task_list)

	ReDelayCall(self, function()
		local list = self.task_list:GetAllItems()
		local sort_list = {}

		for i, v in pairs(list) do
			local data = {}
			data.index = v:GetIndex()
			data.item = v
			sort_list[#sort_list + 1] = data
		end
		table.sort(sort_list, SortTools.KeyLowerSorter("index"))

		local count = 0
		local cur_index = 0
		for k, v in ipairs(sort_list) do
			if 0 ~= v.index then
				count = count + 1
			end
			v.item:PalyItemAnimator(count)
		end

		UITween.FakeToShow(self.node_list.task_list)
	end, tween_info.DelayDoTime, "assignment")

	self.do_assignment_tween = false
end

---------------------------------------------------------------------------------
---------------------------------幻兽修改——START----------------------------------
---------------------------------------------------------------------------------
function AssignmentView:OnClickAssignHuanShouTypeToggle(index)
    if self.select_assign_huanshou_type == index then
        return
    end
    
    self.select_assign_huanshou_type = index
    self:FlushAssignHuanShouRootCondition()
end

function AssignmentView:FlushAssignHuanShouRootCondition()
    if IsEmptyTable(self.fashion_cell_data_list) then
        return
    end

    for i = 0, 5 do
        self.node_list["compose_bag_type_select_" .. i]:CustomSetActive(i == self.select_assign_huanshou_type)
    end

    self.assign_grid:CancleAllSelectCell()

    local data_list = AssignmentWGData.Instance:GetMyAssignHuanShouDataList(self.select_assign_huanshou_type)
    self.node_list.no_data:CustomSetActive(IsEmptyTable(data_list))

    self.assign_grid:SetDataList(data_list)

    -- if IsEmptyTable(self.fashion_cell_data_list) then
    --     return
    -- end

    -- --刷新下面的筛选条件toggle状态
    -- for i = 0, 4 do
    --     XUI.SetGraphicGrey(self.node_list["assign_toggle" .. i], i ~= self.select_assign_toggle)
    -- end

    -- --刷新顶端tab状态
    -- for i = 1, AssignmentWGData.ASSIGN_TAB_TYPE_MAX do
    --     self.node_list["assign_select" .. i]:CustomSetActive(i == self.select_assign_tab)
    -- end

    -- self.assign_grid:CancleAllSelectCell()

    -- local data_list = {}
    -- if self.select_assign_tab == AssignmentWGData.ASSIGN_TAB_TYPE.MY then
    --     data_list = AssignmentWGData.Instance:GetMyAssignFashionDataList(self.select_assign_toggle)
    -- elseif self.select_assign_tab == AssignmentWGData.ASSIGN_TAB_TYPE.OTHER then
    --     data_list = AssignmentWGData.Instance:GetRentAssignFashionDataList(self.select_assign_toggle)
    -- end
    -- self.node_list.no_data:CustomSetActive(IsEmptyTable(data_list))
    -- self.assign_grid:SetDataList(data_list)
    
    -- --在新筛选条件中选中保存的数据
    -- for i = 1, #self.fashion_cell_data_list do
    --     local data = self.fashion_cell_data_list[i].data
    --     if data then
    --         local select_index = -1
    --         for k, v in pairs(data_list) do
    --             if AssignmentWGData.Instance:GetIsSameFashionData(v, data) then
    --                 select_index = k
    --                 break
    --             end
    --         end

    --         if select_index > 0 then
    --             self.assign_grid:SetSelectCellIndexByNoCancel(select_index)
    --         end
    --     end
    -- end

    local rent_times = AssignmentWGData.Instance:GetCanBorrowTimes()
    local color = rent_times > 0 and COLOR3B.GREEN or COLOR3B.RED
    self.node_list.assign_time_text.text.text = string.format(Language.Assignment.AssignTips, color, rent_times)
end

function AssignmentView:FlushAssignHuanShouDataListInfo()
    if self.assign_grid then
        local data_list = AssignmentWGData.Instance:GetMyAssignHuanShouDataList(self.select_assign_huanshou_type)
        self.node_list.no_data:CustomSetActive(IsEmptyTable(data_list))
        self.assign_grid:SetDataList(data_list)
    end
end

---------------------------------------------------------------------------------
---------------------------------幻兽修改——END----------------------------------
---------------------------------------------------------------------------------

-----------------------------------------AssignTaskRender--------------------------
AssignTaskRender = AssignTaskRender or BaseClass(BaseRender)

function AssignTaskRender:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
	end

    self:CleanTimer1()
end

function AssignTaskRender:LoadCallBack()
	if self.item_cell == nil then
		self.item_cell = ItemCell.New(self.node_list.item_cell)
		self.item_cell:SetNeedItemGetWay(true)
	end
end

function AssignTaskRender:OnSelectChange(is_select)
	self.node_list.select_img:CustomSetActive(is_select)
end

local task_star_num = 5
function AssignTaskRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

    local bundle, asset = ResPath.GetRawImagesPNG("a3_wtrw_" .. self.data.color)
    self.node_list.bg_img.raw_image:LoadSprite(bundle, asset)

	self.node_list.task_name.text.text = self.data.name
	
    for i = 1, task_star_num do
        self.node_list["star" .. i]:CustomSetActive(self.data.color >= i)
    end

    self.item_cell:SetData(self.data.reward[0])

    local state = AssignmentWGData.Instance:GetTaskState(self.data.id)
    self.node_list.normal_root:CustomSetActive(state == AssignmentWGData.TASK_STATUS.NORMAL)
    self.node_list.assignment_root:CustomSetActive(state == AssignmentWGData.TASK_STATUS.ASSIGN)

    if state == AssignmentWGData.TASK_STATUS.NORMAL then
        local time_str = TimeUtil.FormatSecond(self.data.time)
        self.node_list.need_time_text.text.text = time_str
        self.node_list.lilian_text.text.text = self.data.cost_exp

        local fashion_cell_data_list = {}
        local data_list = AssignmentWGData.Instance:GetBeastOneKeyDataList(self.data.id) or {}

        --刷新委托条件
        local all_condition_list = Split(self.data.conditions, "|")
        local max_star = -1

        for i = 1, AssignmentWGData.MAX_TASK_CONDITION do
            local condition = all_condition_list[i]
            if condition ~= nil then
                local condition_list = Split(condition, ",")
                local need_type = tonumber(condition_list[1])
                local need_color = tonumber(condition_list[2])
                local need_beast_element = tonumber(condition_list[3])
                local need_beast_star = tonumber(condition_list[4])

                if max_star < need_beast_star then
                    max_star = need_beast_star
                end

                table.insert(fashion_cell_data_list, {need_type = need_type, need_color = need_color, need_beast_element = need_beast_element, need_beast_star = need_beast_star})
            end
        end
        local exp = AssignmentWGData.Instance:GetTaskExp()
        local remind = #data_list >= #fashion_cell_data_list and  exp >= self.data.cost_exp
        self.node_list.remind:SetActive(remind)
    elseif state == AssignmentWGData.TASK_STATUS.ASSIGN then
        local total_time = AssignmentWGData.Instance:GetTaskRestTime(self.data.id)
        self:CleanTimer1()
        if total_time > 0 then
            local rest_time = AssignmentWGData.Instance:GetTaskRestTime(self.data.id)
            local time_str = TimeUtil.FormatSecond(rest_time)
            self.node_list.time_text.text.text = time_str
            self.timer_1 = CountDown.Instance:AddCountDown(total_time, 1,
                -- 回调方法
                function(elapse_time, total_time)
                    rest_time = AssignmentWGData.Instance:GetTaskRestTime(self.data.id)
                    time_str = TimeUtil.FormatSecond(rest_time)
                    self.node_list.time_text.text.text = time_str

                    self.node_list.assign_slider.slider.value = ((self.data.time - rest_time) / self.data.time)
                end,
                -- 倒计时完成回调方法
                function()
                    AssignmentWGCtrl.Instance:SendTaskReq(ASSIGN_TASK_OPERATE_TASK.INFO)
                end
            )
        else
            self.node_list.time_text.text.text = Language.Assignment.AssignComplit
        end

        self.node_list.yiwancheng:CustomSetActive(total_time <= 0)
        self.node_list.jinxingzhong:CustomSetActive(total_time > 0)
        
        self.node_list.assignment_time:CustomSetActive(total_time > 0)
        
        -- self.node_list.assign_slider:SetLocalPosition(0, (total_time > 0 and -24 or -5))

        local rest_time = total_time > 0 and total_time or 0
        self.node_list.assign_slider.slider.value = ((self.data.time - rest_time) / self.data.time)

       

        local remind = total_time <= 0
        self.node_list.remind:SetActive(remind)
    end
end

function AssignTaskRender:CleanTimer1()
    if self.timer_1 and CountDown.Instance:HasCountDown(self.timer_1) then
        CountDown.Instance:RemoveCountDown(self.timer_1)
        self.timer_1 = nil
    end
end

function AssignTaskRender:GetNode(name)
    return self.node_list[name]
end

function AssignTaskRender:PalyItemAnimator(item_index)
	local wait_index = item_index - 1
	wait_index = wait_index < 0 and 0 or wait_index
	local tween_info = UITween_CONSTS.ExpAditionCellTween
	UITween.FakeHideShow(self.node_list.tween_root)

	ReDelayCall(self, function()
		if self.node_list and self.node_list.tween_root then
			UITween.RotateAlphaShow(GuideModuleName.AssignmentView, self.node_list.tween_root, tween_info)
		end
	end, tween_info.NextDoDelay * wait_index, "AssignTaskCell_" .. wait_index)
end

-----------------------------------------AssignTaskRender--------------------------
AssignFashionRender = AssignFashionRender or BaseClass(BaseRender)

function AssignFashionRender:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
	end
end

function AssignFashionRender:LoadCallBack()
	if self.item_cell == nil then
		self.item_cell = ItemCell.New(self.node_list.item_pos)
        self.item_cell:SetNeedItemGetWay(false)
        self.item_cell:IsCanDJ(false)
        self.item_cell:SetUseButton(false)
	end
end

function AssignFashionRender:OnFlush()
	if IsEmptyTable(self.data) then
        return
	end

    local data = self.data.data
    self.node_list.bg:CustomSetActive(data == nil)
    self.node_list.add_btn:CustomSetActive(data == nil)
    self.node_list.item_pos:CustomSetActive(data ~= nil)
    self.node_list.rent_img:CustomSetActive(data ~= nil)

    if data == nil then
        local bundle, asset = ResPath.GetCommonImages("a3_ty_wpk_" .. self.data.need_color)
        self.node_list.bg.image:LoadSprite(bundle, asset)
    else
        local not_my_fashion = data.is_my_fashion == 0
        self.node_list.rent_img:CustomSetActive(not_my_fashion)
        self.item_cell:SetData({item_id = data.cfg.item_id})
    end
end


---------------------------------------------AssignGridItemRender----------------------------------------------

AssignGridItemRender = AssignGridItemRender or BaseClass(BaseGridRender)

function AssignGridItemRender:OnSelectChange(is_select)
	self.node_list.is_select:CustomSetActive(is_select)
end

function AssignGridItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
        self.item_cell = nil
	end
end

function AssignGridItemRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.item_cell)
    self.item_cell:SetNeedItemGetWay(false)
    self.item_cell:IsCanDJ(false)
    self.item_cell:SetUseButton(false)
end

--数据模型FashionDataModel
function AssignGridItemRender:OnFlush()
	if IsEmptyTable(self.data) or IsEmptyTable(self.data.cfg) then
		return
	end

	self.item_cell:SetData({item_id = self.data.cfg.item_id})
    
    self.node_list.type_img:CustomSetActive(false)
    -- 现在只有幻兽类型，不需要图标区分类型，而且还会挡住幻兽星级
    -- local color_img_str = (self.data.cfg.color)
    -- self.node_list.type_img:CustomSetActive(color_img_str > 0)
    -- if color_img_str > 0 then
    --     local bundle, asset = ResPath.GetOfflinerestImages("a3_wt_tj_" .. self.data.cfg.type .. "_" .. color_img_str)
    --     self.node_list.type_img.image:LoadSprite(bundle, asset)
    -- end
end

---------------------------------------------AssignmentGrid----------------------------------------------

AssignmentGrid = AssignmentGrid or BaseClass(AsyncBaseGrid)

function AssignmentGrid:SetSelectCellIndexByNoCancel(cell_index)
	if self.select_tab[1] == nil then 
        self.select_tab[1] = {}
    end

	self.select_tab[1][cell_index] = true
end

function AssignmentGrid:SetCancelSelectCellIndex(cell_index)
	if self.select_tab[1] == nil then 
        self.select_tab[1] = {}
    end

	self.select_tab[1][cell_index] = nil
end