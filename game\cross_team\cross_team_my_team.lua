local MAX_TEAM_MEMBER_NUMS = 3
local CustomPos = {
	Vector2(401,-107),
	Vector2(721,-107),
	Vector2(1042,-107)
}
function CrossTeamView:InitMyTeam()
    --self.team_my_list = AsyncListView.New(CrossAllTeamListRender, self.node_list["pt_ph_quick_list"])
	XUI.AddClickEventListener(self.node_list.btn_invite2, BindTool.Bind1(self.OnClickInvite, self))
	XUI.AddClickEventListener(self.node_list.btn_invite3, BindTool.Bind1(self.OnClickInvite, self))
	XUI.AddClickEventListener(self.node_list.btn_rule, BindTool.Bind1(self.OnClickRule, self))
    XUI.AddClickEventListener(self.node_list.my_level_limit_bg, BindTool.Bind(self.TeamOnClickChangeGoal, self, false))
    XUI.AddClickEventListener(self.node_list["btn_block"], BindTool.Bind1(self.OnClickBlock, self))
    if not self.team_cell_flush then
        self.team_cell_flush = GlobalEventSystem:Bind(TeamInfoQuery.TEAM_INFO_BACK, BindTool.Bind(self.TeamCellFlush,self))
    end
    self.team_list = {}
	for i = 1, MAX_TEAM_MEMBER_NUMS do
    local async_loader = AllocAsyncLoader(self, "TeamMemberInfo" .. i)
    async_loader:SetParent(self.node_list["ph_team_list" .. i].transform)
    async_loader:Load("uis/view/new_team_ui_prefab", "ph_cross_myteam_render",
        function (obj)
            obj = U3DObject(obj)
            self.team_list[i] = CrossTeamMyListItem.New(obj)
            self.team_list[i]:SetIndex(i)
        end)
    end
    
    for i = 1, 5 do
		self.node_list["btn_choose_" .. i].button:AddClickListener(BindTool.Bind(self.OnClickIndexChoose,self,i))
    end

    self:TeamCellFlush()
end

function CrossTeamView:ReleaseMyTeam()
    if self.team_list then
		for k,v in pairs(self.team_list) do
			if v then
				v:DeleteMe()
			end
		end
		self.team_list = {}
    end
    if self.team_cell_flush then
		GlobalEventSystem:UnBind(self.team_cell_flush)
		self.team_cell_flush = nil
	end

    self.custom_menu_state = false
	self.team_info = nil
end

function CrossTeamView:OnClickIndexChoose(i)
	if self.team_info == nil or i == nil then return end
	if i == 1 then
		BrowseWGCtrl.Instance:OpenWithUid(self.team_info.role_id, nil, self.team_info.is_cross)
	elseif i == 2 then
		SocietyWGCtrl.Instance:IAddFriend(self.team_info.role_id)
	elseif i == 3 then
		CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_CHANGE_LEADER, self.team_info.member_index)
	elseif i == 4 then
		CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_KICK_OUT, self.team_info.member_index)
	elseif i == 5 then
		CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_REQ_LEADER)
	end
	self:OnClickBlock()
end

function CrossTeamView:OnClickBlock()
	self.custom_menu_state = false
	self.team_info = nil
	self.node_list.choose_team_view:SetActive(false)
end

function CrossTeamView:FlushMyTeam()
    -- local team_type, teamfb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	-- local goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, teamfb_mode)
	-- if not goal_info then
	-- 	return
    -- end
    
    local is_in_team = 1 == CrossTeamWGData.Instance:GetIsInTeam()
    self.node_list["pt_btn_auto_req"]:SetActive(is_in_team)
    self.node_list["no_team_root"]:SetActive(is_in_team)
	-- local member_list = CrossTeamWGData.Instance:GetTeamMemberList()
	--self.node_list["lbl_fb_name"].text.text = CrossTeamWGData.Instance:GetSceneName()
    self.node_list.my_team_layout_blank_tip2:SetActive(not is_in_team)
	self.node_list["pt_btn_apply"]:SetActive(is_in_team and 1 == CrossTeamWGData.Instance:GetIsTeamLeader())
	local is_show_talk = is_in_team and CrossTeamWGData.Instance:GetTeamMemberCount() < 3
	self.node_list["pt_btn_speak"]:SetActive(is_show_talk)

	local count = CrossTeamWGData.Instance:GetTeamMemberCount() - 1
	local exp_add = count * 10
	self.node_list["exp_text"].text.text = exp_add .. "%"
	-- self:OnFlushBtn()

	-- local is_qingyuan = team_type == GoalTeamType.QingYuanFb
	-- self.node_list["img_people3"]:SetActive(not is_qingyuan)

    local online_member_count = CrossTeamWGData.Instance:GetTeamMemberCount()
    -- for k,v in pairs(member_list) do
    --     if v.is_online == 1 then
    --         online_member_count = online_member_count + 1
    --     end
    -- end
	for i = 1, 3 do
		if i <= online_member_count then
			self.node_list["img_people" .. i].image:LoadSprite(ResPath.GetNewTeamImg("a3_zjm_dw11"))
		else
			self.node_list["img_people" .. i].image:LoadSprite(ResPath.GetNewTeamImg("a3_zjm_dw21"))
		end
	end

end

function CrossTeamView:OnClickCrateTeam()
	CrossTeamWGCtrl.Instance:SendCreateTeamReq()
end

function CrossTeamView:OnClickRule()
	local team_type, team_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
    local now_goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, team_fb_mode)
    local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.NewTeam.EXP_rule_title)
	rule_tip:SetContent(now_goal_info.team_rule or Language.NewTeam.EXP_desc_rule)
end

function CrossTeamView:TeamCellFlush()
    -- local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
    -- local is_zhedie, zhedie_btn_count = NewTeamWGData.Instance:GetIsZheDieBtn(team_type)
	if self.team_list == nil or self.team_list[3] == nil then
		return
	end

	local change_index, member_list = CrossTeamWGData.Instance:GetTeamMemberListView()
	local member_state,btn_invite_state
	local is_in_team = 1 == CrossTeamWGData.Instance:GetIsInTeam()

    for k,index in ipairs(change_index) do
        local v = member_list[k]
		if v and v.uuid and v.uuid.temp_low > 0 then
			self.team_list[k]:SetData(v)
			member_state = v and v.name or v  --人名,"",nil
			btn_invite_state = member_state == "" or not member_state
			self.node_list["btn_invite" .. k]:SetActive(btn_invite_state)
            self.team_list[k]:SetActive(true)
        else
            self.team_list[k]:SetActive(false)
            self.node_list["btn_invite" .. k]:SetActive(is_in_team)
		end
    end
    
	local min_level, max_level = CrossTeamWGData.Instance:GetTeamLimitLevel()
    local str = string.format(Language.NewTeam.PTLevelLimitTop, min_level, max_level)
	EmojiTextUtil.ParseRichText(self.node_list["level_limit_text"].emoji_text, str, 24, COLOR3B.BLUE_TITLE)
end

function CrossTeamView:OnClickInvite()
	NewTeamWGCtrl.Instance:OpenInviteView()
end

function CrossTeamView:OpenCustomMenu(buff, info ,pos)
	self.custom_menu_state = not self.custom_menu_state
	self.team_info = self.custom_menu_state and info or nil
	self.node_list.choose_team_view:SetActive(self.custom_menu_state)
	for k,v in pairs(buff) do
		self.node_list["btn_choose_" .. k]:SetActive(v)
	end
	if self.custom_menu_state then
		self.node_list.layout_choose_team.rect.anchoredPosition = pos
	end
end

------------------itemRender-----------------
CrossTeamMyListItem = CrossTeamMyListItem or BaseClass(BaseRender)

function CrossTeamMyListItem:__init()
	XUI.AddClickEventListener(self.node_list.RoleDisplay, BindTool.Bind(self.OnClickChoose, self))
	self.role_model = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["RoleDisplay"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.S,
		can_drag = true,
	}

	self.role_model:SetRenderTexUI3DModel(display_data)
	-- self.role_model:SetUI3DModel(self.node_list["RoleDisplay"].transform, self.node_list.ModelEvent.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
	self.cache = nil
	self.state = nil
	self.is_self = false
	self.cur_model = nil
	self.count = 3
	self.old_fabao_id = -1
	self.old_role_id = -1
end

function CrossTeamMyListItem:__delete()
	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	self.cache = nil
	self.state = nil
	self.is_self = nil
	self.old_fabao_id = nil
	self.old_role_id = nil
end

function CrossTeamMyListItem:ReleaseTeam()
	self.cache = nil
	self.state = nil
	self.is_self = nil
end

function CrossTeamMyListItem:OnClickChoose()
	if self.data == nil then return end
	if self.data.uuid == RoleWGData.Instance:GetUUid()  then
		return
	end
    local buff = self:OnClickView()
    local team_info = {}
    team_info.role_id = self.data.uuid.temp_low
    team_info.is_cross = not self:IsSameServer()
    team_info.uuid = self.data.uuid
    team_info.member_index = self.data.index
	CrossTeamWGCtrl.Instance:OpenCustomMenu(buff, team_info, CustomPos[self.index])
end

function CrossTeamMyListItem:IsSameServer()
	if self.data == nil then return false end
	local main_role_server_id = RoleWGData.Instance:GetMergeServerId()
    local main_role_plat_type = RoleWGData.Instance:GetPlatType()
    local plat, server
    if self.data.usid then
        plat, server = self.data.usid.temp_high, self.data.usid.temp_low
    end
	local server_id = server or main_role_server_id
    local plat_type = plat or main_role_plat_type
    self.plat_type = plat_type
	return server_id == main_role_server_id and plat_type == main_role_plat_type
end

function CrossTeamMyListItem:OnClickView()
	if self.data == nil then return end
	local items = {true,true,true,true,true} --查看资料，添加好友，移交队长，踢出队伍，请求队长
	if 0 == CrossTeamWGData.Instance:GetIsTeamLeader() then--去掉 移交队长
		items[3] = false
		items[4] = false
	else
		items[5] = false--去掉 申请队长
	end
	if not self:IsSameServer() or nil ~= SocietyWGData.Instance:FindFriend(self.data.uuid.temp_low) then--去掉 添加好友
		items[2] = false
	end
	if 1 ~= self.data.is_leader then
		items[5] = false
	end
	return items
end

function CrossTeamMyListItem:OnFlush()
    if not self.data then return end
    self.data.is_match = false
	if self.state ~= self.data.is_match or not self.data.is_match then
		self.state  = self.data.is_match
		self.node_list["lbl_match_desc"]:SetActive(false)
		self.node_list["lbl_role_name"]:SetActive(not self.state)
		self.node_list["level_limit_text"]:SetActive(not self.state)
	end
	-- self:SetVipNum(self.data.vip_level)
	self.node_list["img_leader"]:SetActive(1 == self.data.is_leader)
	local is_me = RoleWGData.Instance:GetUUid() == self.data.uuid
    self.node_list["lbl_role_name"].text.text = is_me and self.data.name or self.data.name .. " " .. Language.NewTeam.MemberState_Near

	--人物等级
	local is_vis,level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
	self.node_list["dianfeng_img"]:SetActive(is_vis)
	self.node_list["level_limit_text"].text.text = string.format(Language.Common.LevelNormal,level)

	if self.data.uuid == RoleWGData.Instance:GetUUid() then
		self.node_list.bg_fightpower:SetActive(false)
		self.node_list.fightpower_num.text.text = RoleWGData.Instance.role_vo.capability
	else
		self.node_list.bg_fightpower:SetActive(false)--self.data.capability ~= nil)
		self.node_list.fightpower_num.text.text = self.data.capability or 0
	end

	self:SetModel()
	--self:QiPaoState()
	self.node_list["role_prof"].image:LoadSprite(ResPath.GetCommonImages(RoleWGData.GetProfIcon(self.data.prof, self.data.sex)))
end

-- function CrossTeamMyListItem:QiPaoState()
-- 	self.node_list.role_scene_bg:SetActive(false)
-- end

function CrossTeamMyListItem:SetMatchPoint()
	self.node_list["lbl_match_desc"].text.text = Language.NewTeam["Matching" .. self.count]
	self.count = self.count + 1 < 4 and self.count + 1 or 1
end

--全模型加载，记得剔除不需要的( SetModelResInfo() )
function CrossTeamMyListItem:SetModel()
	if self.data == nil then return end

    local role_id = 0
    if self.data and self.data.uuid and self.data.uuid.temp_low then
        role_id = self.data.uuid.temp_low
    end
    local uuid = self.data.uuid
	if role_id == 0 or not self.data.uuid then
		self.role_model:ClearModel()
		self:ReleaseTeam()
		self.old_role_id = -1
		return
	end

	local vo = GameVoManager.Instance:GetMainRoleVo()
	local server_id = self.data.uuid.temp_high
	local self_server_id = vo.merge_server_id
	local special_status_table = {ignore_wing = true, ignore_fazhen = true, ignore_jianzhen = true}
	if uuid == RoleWGData.Instance:GetUUid() then
		--if vo.role_id ~= self.old_role_id then
			self.role_model:SetModelResInfo(vo, special_status_table)
		--end

		self.old_role_id = vo.role_id
	else
		local function callback( protocol )
			if self.data and uuid ~= RoleWGData.Instance:GetUUid() then
				NewTeamWGData.Instance:SetRoleInfo(protocol)
				local role_appearance = NewTeamWGData.Instance:GetRoleInfo()
				if self.role_model then
					-- if role_appearance.role_id ~= self.old_role_id then
					self.role_model:SetModelResInfo(role_appearance, special_status_table)
					-- end
					self.old_role_id = role_appearance.role_id
				end
			end
		end

		--跨服玩家组队的
		if server_id ~= self_server_id then
			BrowseWGCtrl.Instance:SendCrossQueryRoleInfo(server_id, role_id, callback)
		else
			BrowseWGCtrl.Instance:BrowRoelInfo(self.data.orgin_role_id, callback)
		end
	end

	self:ResetRoleAction()
	self.cache = role_id
end

function CrossTeamMyListItem:ResetRoleAction()
	if self.role_model then
		self.role_model:PlayLastAction()
	end
end

function CrossTeamMyListItem:SetVipNum(num)
	if num == 0 or num == nil then
		self.node_list.vip_image:SetActive(false)
	else
		local bundle, asset = ResPath.GetCommonIcon("a2_vip".. num)
		self.node_list.vip_image.image:LoadSpriteAsync(bundle, asset, function ()  		
			self.node_list.vip_image.image:SetNativeSize()
		end)
	end
end


function CrossTeamMyListItem:OnClickMenuButton(index, sender, param)
	if self.data == nil then return end
	
	if 0 == self.data.is_online then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NotOnline)
		return
	end
	local menu_text = param[index]
	if menu_text == Language.NewTeam.OpenMenu[1] then
		BrowseWGCtrl.Instance:OpenWithUid()
	elseif menu_text == Language.NewTeam.OpenMenu[2] then
		SocietyWGCtrl.Instance:IAddFriend()
	elseif menu_text == Language.NewTeam.OpenMenu[3] then
		SocietyWGCtrl.Instance:SendChangeTeamLeader()
	end
end
