-- D-打地鼠.xls
local item_table={
[1]={item_id=48570,num=1,is_bind=1},
[2]={item_id=26368,num=5,is_bind=1},
[3]={item_id=26148,num=30,is_bind=1},
[4]={item_id=26344,num=20,is_bind=1},
[5]={item_id=26203,num=15,is_bind=1},
[6]={item_id=36416,num=800000,is_bind=1},
[7]={item_id=26368,num=4,is_bind=1},
[8]={item_id=26148,num=27,is_bind=1},
[9]={item_id=26344,num=18,is_bind=1},
[10]={item_id=36416,num=720000,is_bind=1},
[11]={item_id=26368,num=3,is_bind=1},
[12]={item_id=26148,num=24,is_bind=1},
[13]={item_id=26344,num=16,is_bind=1},
[14]={item_id=36416,num=640000,is_bind=1},
[15]={item_id=48571,num=1,is_bind=1},
[16]={item_id=26367,num=8,is_bind=1},
[17]={item_id=26148,num=21,is_bind=1},
[18]={item_id=26344,num=14,is_bind=1},
[19]={item_id=26203,num=12,is_bind=1},
[20]={item_id=36416,num=560000,is_bind=1},
[21]={item_id=26367,num=6,is_bind=1},
[22]={item_id=26148,num=18,is_bind=1},
[23]={item_id=26344,num=12,is_bind=1},
[24]={item_id=26203,num=10,is_bind=1},
[25]={item_id=36416,num=480000,is_bind=1},
[26]={item_id=30447,num=5,is_bind=1},
[27]={item_id=36420,num=100,is_bind=1},
[28]={item_id=26149,num=10,is_bind=1},
[29]={item_id=26200,num=5,is_bind=1},
[30]={item_id=26344,num=3,is_bind=1},
[31]={item_id=36416,num=500000,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
mole={
[1]={ID=1,max_stop_ms=2500,score=10,hit_times=1,item_id=1,item_type=1,item_move_time=1500,},
[2]={ID=2,name="高分",min_stop_ms=1800,touch_times=2,score=20,item_id=2,},
[3]={ID=3,name="炸弹",score=-50,},
[4]={ID=4,name="冰冻",control_ms=1500,hit_num_pos_x=0,hit_num_pos_y=0,action_hit_num_time=0,},
[5]={ID=5,name="惊吓",item_id=5,hit_num_pos_x=0,hit_num_pos_y=0,action_hit_num_time=0,},
[6]={ID=6,name="宝箱",random_gold="100,200",hit_times=0,hit_num_pos_x=0,hit_num_pos_y=0,action_hit_num_time=0,}
},

mole_meta_table_map={
[2]=1,	-- depth:1
},
random_pool={
{},
{min_score=111,max_score=560,interval_ms=800,},
{min_score=561,max_score=9999,interval_ms=700,}
},

random_pool_meta_table_map={
},
hit={
{},
{min_times=20,max_times=999,score_added=20000,}
},

hit_meta_table_map={
},
score_rank_reward={
{reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{min_rank=2,max_rank=2,reward_item={[0]=item_table[1],[1]=item_table[7],[2]=item_table[8],[3]=item_table[9],[4]=item_table[5],[5]=item_table[10]},},
{min_rank=3,max_rank=3,reward_item={[0]=item_table[1],[1]=item_table[11],[2]=item_table[12],[3]=item_table[13],[4]=item_table[5],[5]=item_table[14]},},
{min_rank=4,max_rank=5,reward_item={[0]=item_table[15],[1]=item_table[16],[2]=item_table[17],[3]=item_table[18],[4]=item_table[19],[5]=item_table[20]},},
{min_rank=6,max_rank=8,},
{min_rank=9,max_rank=10,},
{min_rank=11,max_rank=15,},
{min_rank=16,max_rank=20,},
{min_rank=21,max_rank=25,},
{min_rank=26,max_rank=32,}
},

score_rank_reward_meta_table_map={
[5]=4,	-- depth:1
[6]=4,	-- depth:1
},
experience_mole={
[1]={id=1,occur_ms=1000,bubble_str="灰狼出来了\n快敲击它!",},
[2]={id=2,hole=8,occur_ms=2000,stop_ms=2000,},
[3]={id=3,mole_id=2,occur_ms=4500,bubble_str="野猪皮较厚\n需要敲击2次",},
[4]={id=4,mole_id=2,hole=4,occur_ms=6000,},
[5]={id=5,mole_id=3,occur_ms=8500,bubble_str="危险!\n不要碰炸弹!",},
[6]={id=6,mole_id=4,hole=9,occur_ms=12000,bubble_str="小心!\n冰兔会冻住你!",},
[7]={id=7,hole=2,occur_ms=13000,stop_ms=1500,},
[8]={id=8,mole_id=2,hole=7,occur_ms=14000,},
[9]={id=9,mole_id=5,hole=6,bubble_str="别打扰熊猫\n会吓跑其它动物",},
[10]={id=10,hole=1,},
[11]={id=11,hole=3,},
[12]={id=12,mole_id=6,occur_ms=19000,bubble_str="哇!稀有宝箱\n千万别错过！",}
},

experience_mole_meta_table_map={
[3]=9,	-- depth:1
[4]=2,	-- depth:1
[8]=7,	-- depth:1
[5]=2,	-- depth:1
},
join_reward={
[1]={times=1,},
[2]={times=2,},
[3]={times=3,}
},

join_reward_meta_table_map={
},
other_default_table={hole_num=9,round_time=60,experience_time=23,},

mole_default_table={ID=1,name="普通",min_stop_ms=1400,max_stop_ms=2000,touch_times=1,score=0,random_gold="0,0",delay_ms=100,control_ms=0,hit_times=-1,action_ms=200,item_id=0,item_type=0,hit_num_pos_x=90,hit_num_pos_y=60,action_hit_num_time=2500,item_move_time=0,},

random_pool_default_table={min_score=0,max_score=110,interval_ms=1000,},

hit_default_table={min_times=10,max_times=19,score_added=15000,},

score_rank_reward_default_table={min_rank=1,max_rank=1,reward_item={[0]=item_table[15],[1]=item_table[21],[2]=item_table[22],[3]=item_table[23],[4]=item_table[24],[5]=item_table[25]},},

experience_mole_default_table={id=1,mole_id=1,hole=5,occur_ms=16000,stop_ms=3000,bubble_type="",bubble_str="",},

join_reward_default_table={times=1,reward_item={[0]=item_table[26],[1]=item_table[27],[2]=item_table[28],[3]=item_table[29],[4]=item_table[30],[5]=item_table[31]},}

}

