---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON>ua)
--- Created by jf.
--- DateTime: 2019/10/31 15:25
---

DayCountChangeView = DayCountChangeView or BaseClass(SafeBaseView)

function DayCountChangeView:ReleaseCallBack()
    if self.day_count_change_event then
		GlobalEventSystem:UnBind(self.day_count_change_event)
		self.day_count_change_event = nil
	end
end
function DayCountChangeView:LoadCallBack()
    self.day_count_change_event = GlobalEventSystem:Bind(OtherEventType.DAY_COUNT_CHANGE, BindTool.Bind(self.OnDayCountChange, self))
end

function DayCountChangeView:OnDayCountChange(day_count_id)
	self:Flush()
end