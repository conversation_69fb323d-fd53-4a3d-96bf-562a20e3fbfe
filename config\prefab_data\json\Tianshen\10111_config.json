{"actorController": {"projectiles": [], "hurts": []}, "actorTriggers": {"effects": [{"triggerEventName": "rest/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10111_skill1", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10111/10111_skill1_prefab", "AssetName": "10111_skill1", "AssetGUID": "017291d045776cc4aabdf76e74b6d05d", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "hurt_root", "isAttach": true, "isRotation": false, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": -15.0, "triggerStopEvent": "[none]", "effectBtnName": "rest", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10111_skill1", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10111/10111_skill1_prefab", "AssetName": "10111_skill1", "AssetGUID": "017291d045776cc4aabdf76e74b6d05d", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "buff_down", "isAttach": true, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 1.5, "offsetPosY": 0.0, "offsetPosZ": -8.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10111_skill2", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10111/10111_skill2_prefab", "AssetName": "10111_skill2", "AssetGUID": "d98cd48c7cedd094a956724e25deeee0", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "buff_down", "isAttach": true, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 1.5, "offsetPosY": 0.0, "offsetPosZ": -8.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10111_skill3", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10111/10111_skill3_prefab", "AssetName": "10111_skill3", "AssetGUID": "61ed7005965f5cd49a69fdfdd0da8f91", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "buff_down", "isAttach": true, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 1.5, "offsetPosY": 0.0, "offsetPosZ": -8.0, "triggerStopEvent": "[none]", "effectBtnName": "attack3", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "combo1_1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10111_attack1", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10111/10111_attack1_prefab", "AssetName": "10111_attack1", "AssetGUID": "490effc135dbc714590d6d8f22201e76", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1", "playerAtPos": false, "ignoreParentScale": false}], "sounds": [{"soundEventName": "combo1_1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10111", "AssetName": "MingJiangcombo_1_1", "AssetGUID": "b748e82b2d0e9b3438e4c51db19081c4", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_1", "soundBtnName": "combo1", "soundIsMainRole": false}, {"soundEventName": "combo1_2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10111", "AssetName": "MingJiangcombo_1_2", "AssetGUID": "ba1d4054c7ce1f24d9f8dd84c7f406ca", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_2", "soundBtnName": "combo2", "soundIsMainRole": false}, {"soundEventName": "combo1_3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10111", "AssetName": "MingJiangcombo_1_3", "AssetGUID": "e584b90ac8fcffa49bdad29cb6cfe240", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_3", "soundBtnName": "combo3", "soundIsMainRole": false}, {"soundEventName": "attack1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10111", "AssetName": "MingJiangattack1", "AssetGUID": "774ca7ff58bbe5e469bd4cf804a2f588", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack1", "soundBtnName": "attack1", "soundIsMainRole": false}, {"soundEventName": "attack2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10111", "AssetName": "MingJiangattack2", "AssetGUID": "1b328f277f940744e907fd922eb238d9", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack2", "soundBtnName": "attack2", "soundIsMainRole": false}, {"soundEventName": "attack3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10111", "AssetName": "MingJiangattack3", "AssetGUID": "dbbf92fd7a3d4104d9d94a11672c50f5", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack3", "soundBtnName": "attack3", "soundIsMainRole": false}, {"soundEventName": "combo1_3/begin", "soundDelay": 0.5, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10111", "AssetName": "MingJiangcombo_1_31", "AssetGUID": "865603bc03c739a4d9ad1ce4904aa996", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_31", "soundBtnName": "combo3_1", "soundIsMainRole": false}], "cameraShakes": [], "radialBlurs": []}}