--准备截屏界面
ScreenShotView = ScreenShotView or BaseClass(SafeBaseView)

local MaxSliderValue = 20
local MinSliderValue = 2

function ScreenShotView:__init()
    self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
    self.is_safe_area_adapter = true
    self.active_close = false
    self:AddViewResource(0, "uis/view/screen_shot_ui_prefab", "screenshoting_panel")
    self:AddViewResource(0, "uis/view/main_ui_prefab", "BottomJoystick")
    self.view_layer = UiLayer.Guide
    self.shield_obj_list = {}
    self.qufu_index = 0
    self.screen_texture = nil
end

function ScreenShotView:ReleaseCallBack()
    if self.apply_view_close_event then
        GlobalEventSystem:UnBind(self.apply_view_close_event)
        self.apply_view_close_event = nil
    end

    if self.camera_handle_event then
        GlobalEventSystem:UnBind(self.camera_handle_event)
        self.camera_handle_event = nil
    end

    self.slider = nil
    self.setting_bg_state = false
    self.show_state = true
    self.shield_async_loader = nil
    for k,v in pairs(self.shield_obj_list) do
        v:DeleteMe()
    end
    self.shield_obj_list = {}
    self.qufu_index = 0

    if self.screen_texture then
        UnityEngine.GameObject.Destroy(self.screen_texture)
        self.screen_texture = nil
    end

    if self.alert_window then
        self.alert_window:DeleteMe()
        self.alert_window = nil
    end
end

function ScreenShotView:OpenCallBack()
    SceneOptimizeMgr.SetCullEnable(false, true)
    -- 断线时关闭拍照
    self.disconnect_event = GlobalEventSystem:Bind(LoginEventType.GAME_SERVER_DISCONNECTED, BindTool.Bind(self.Resume, self))
    -- CG时关闭拍照
    self.cg_start_event = GlobalEventSystem:Bind(ObjectEventType.CG_EVENT_START, BindTool.Bind(self.Resume, self))
    -- 切场景时关闭拍照
    self.change_scene_event = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_ENTER, BindTool.Bind(self.Resume, self))
    -- 受到攻击时关闭拍照
    self.be_atk_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_BE_HIT, BindTool.Bind(self.MainRoleBeAtk, self))
    -- 功能引导时关闭拍照
    self.guid_event = GlobalEventSystem:Bind(OtherEventType.GuideChange, BindTool.Bind(self.Resume, self))

    -- 停止挂机
    GuajiWGCtrl.Instance:StopGuaji()
    local main_role = Scene.Instance:GetMainRole()
    if main_role then
    	main_role:StopMove()
    end

    local list = ScreenShotWGData.Instance:GetList()
    for i,v in ipairs(list) do
        local is_shield = 0 == ScreenShotWGData.Instance:GetIsSelect(i)
        ScreenShotWGCtrl.Instance:RegisterShieldRule(v.shield_type, is_shield)
    end

    -- 屏蔽所有shadow
    ScreenShotWGCtrl.Instance:RegisterShieldRule(ShieldObjType.Shadow, true)

    local floating_canvas = TipsNumberShowManager.GetTipsNumberCanvas()
    if floating_canvas then
    	floating_canvas.enabled = false
    end

    local fight_canvas = FightText.Instance:GetCanvas()
    if fight_canvas then
    	fight_canvas.enabled = false
    end
end

function ScreenShotView:CloseCallBack()
    MainCameraFollow.targetOffset = Vector3(0, 0, 0)
    SceneOptimizeMgr.SetCullEnable(true)
    local floating_canvas = TipsNumberShowManager.GetTipsNumberCanvas()
    if floating_canvas then
    	floating_canvas.enabled = true
    end

    local fight_canvas = FightText.Instance:GetCanvas()
    if fight_canvas then
    	fight_canvas.enabled = true
    end

    if self.disconnect_event then
        GlobalEventSystem:UnBind(self.disconnect_event)
        self.disconnect_event = nil
    end

    if self.cg_start_event then
        GlobalEventSystem:UnBind(self.cg_start_event)
        self.cg_start_event = nil
    end

    if self.change_scene_event then
        GlobalEventSystem:UnBind(self.change_scene_event)
        self.change_scene_event = nil
    end

    if self.be_atk_event then
        GlobalEventSystem:UnBind(self.be_atk_event)
        self.be_atk_event = nil
    end

    if self.guid_event then
        GlobalEventSystem:UnBind(self.guid_event)
        self.guid_event = nil
    end

    if self.tween_quest then
        GlobalTimerQuest:CancelQuest(self.tween_quest)
        self.tween_quest = nil
    end

    ScreenShotWGCtrl.Instance:ClearAllShieldRule()

    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
    if main_role_vo.level <= 65 then
    	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
    end
end

function ScreenShotView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["btn_snapshot"],BindTool.Bind(self.OnClickSnapShot, self))
    XUI.AddClickEventListener(self.node_list["btn_setting"],BindTool.Bind(self.OnClickSetting, self))
    XUI.AddClickEventListener(self.node_list["btn_add_distane"],BindTool.Bind(self.OnClickAdd, self))
    XUI.AddClickEventListener(self.node_list["btn_reduce_distance"],BindTool.Bind(self.OnClickReduce, self))
    XUI.AddClickEventListener(self.node_list["btn_exit"],BindTool.Bind(self.OnClickExit, self))
    XUI.AddClickEventListener(self.node_list["btn_rest"],BindTool.Bind(self.OnClickRest, self))
    XUI.AddClickEventListener(self.node_list["btn_show"],BindTool.Bind(self.OnClickShow, self))
    XUI.AddClickEventListener(self.node_list["btn_close_bg"],BindTool.Bind(self.OnClickCloseBG, self))
    XUI.AddClickEventListener(self.node_list["close_bg_btn"],BindTool.Bind(self.OnClickCloseBG, self))
    XUI.AddClickEventListener(self.node_list["btn_custom_action"],BindTool.Bind(self.OnClickOpenCustomAcrion, self))
    
    self.node_list["btn_custom_action"]:SetActive(FunOpen.Instance:GetFunIsOpened(GuideModuleName.CustomActionView))
    
    if self.shield_async_loader then
        self.shield_async_loader:Destroy()
    end

    local data_list = ScreenShotWGData.Instance:GetList()
    -- local line_coutn = math.ceil(#data_list / 3)
    -- for i = 1, line_coutn do
    --     local obj = self.node_list.setting_btn_line:FindObj("line"..i)
    --     if nil ~= obj then
    --         obj:SetActive(true)
    --     end
    -- end

    self.shield_async_loader = self.shield_async_loader or AllocResAsyncLoader(self, "shield_async_loader")
    self.shield_async_loader:Load("uis/view/screen_shot_ui_prefab", "setting_cell", nil, function(new_obj)
        for i=1,#data_list do
            local obj = ResMgr:Instantiate(new_obj)
            obj.transform:SetParent(self.node_list.setting_bg.transform, false)
            self.shield_obj_list[i] = ScreenSnapShotItem.New(obj)
            self.shield_obj_list[i]:SetIndex(i)
            self.shield_obj_list[i]:SetData(data_list[i])
            if data_list[i].shield_type == SnapShotShieldType.QuFu then
                self.qufu_index = i
                self:FlushQuFu()
            end
        end
    end)

    self.slider = self.node_list.slider_distance.slider
    self.slider:AddValueChangedListener(BindTool.Bind(self.OnSliderValueChanged, self))
    self.apply_view_close_event = GlobalEventSystem:Bind(OtherEventType.VIEW_CLOSE, BindTool.Bind(self.OnViewCloseHandler, self))
    self.camera_handle_event = GlobalEventSystem:Bind(MainUIEventType.CAMERA_HANDLE_CHANGE, BindTool.Bind(self.OnCameraChange, self))
end

function ScreenShotView:OpenIndexCallBack()

end

function ScreenShotView:ShowIndexCallBack()
    self.setting_bg_state = false
    self.show_state = true
    self.node_list.setting_bg:SetActive(true)
    self.node_list.btn_close_bg:SetActive(false)
    self.node_list.setting_bg.rect.anchoredPosition = Vector2(500, 0)
    self:FlushQuFu()
    self:SyncFormCameraFollow()
    self:PlayCameraTween()
    self:FlushRootShow()
end

function ScreenShotView:SyncFormCameraFollow()
    if MainCameraFollow then
        if self.slider then
            self.slider.value = MainCameraFollow.Distance
        end
    end
end

---[[
function ScreenShotView:PlayCameraTween()
    if IsNil(MainCameraFollow) then
        return
    end

    if MarryWGData.Instance:GetOwnIsXunyou() then -- 结婚巡游
        local tween_total_angle_x = 40
        local tween_total_angle_y = MainCameraFollow.Target.eulerAngles.y - 150
        local tween_total_dis = 12
        self.slider.value = tween_total_dis
        Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.SCENE_POS, tween_total_angle_x, tween_total_angle_y, tween_total_dis)
        return
    end

    local main_role = Scene.Instance:GetMainRole()
    if not main_role or main_role:IsDeleted() then
        return
    end

    if not main_role.draw_obj then
        return
    end

    local role_root = main_role.draw_obj:GetRoot()
    if not role_root then
        return
    end

    local main_role_angles = role_root.transform.localEulerAngles

    local tween_total_angle_x = 15
    local tween_total_angle_y = main_role_angles.y - 180
    local tween_total_dis = 2

    local mount_appeid = main_role:GetCurRidingResId()
    if mount_appeid > 0 then
        tween_total_angle_y = main_role_angles.y + 240
        tween_total_dis = 5
    elseif main_role:GetIsInSit() then
        tween_total_angle_x = 0
        tween_total_angle_y = main_role_angles.y + 240
        tween_total_dis = 6
    end

    self.slider.value = tween_total_dis
    MainCameraFollow.targetOffset = Vector3(0, -0.3, 0)
    Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.SCENE_POS, tween_total_angle_x, tween_total_angle_y, tween_total_dis)
end
--]]

function ScreenShotView:OnFlush(param_t)
    for k,v in pairs(param_t) do
        if k == "qufu" then
            self:FlushQuFu()
        end
    end
end

-- 主角被攻击
function ScreenShotView:FlushQuFu()
    if self.qufu_index > 0 then
        local flag = ScreenShotWGData.Instance:GetIsSelect(self.qufu_index)
        self.node_list.qufu_panel:SetActive(flag == 1)
        self.node_list.role_name.text.text = RoleWGData.Instance:GetAttr("name")

        local temp_name = string.format(Language.WorldServer.ServerDefName, RoleWGData.Instance:GetAttr("server_id"))
        self.node_list.server_name.text.text = temp_name
    end
end

-- 主角被攻击
function ScreenShotView:MainRoleBeAtk(deliverer)
    local vo = GameVoManager.Instance:GetMainRoleVo()
    if vo and vo.max_hp and vo.max_hp > 0 and vo.hp then
        if vo.hp / vo.max_hp < 0.5 then
            self:Resume()
            SysMsgWGCtrl.Instance:ErrorRemind(Language.ScreenShot.MainRoleBeAtk)
        end
    end
end

function ScreenShotView:Resume()
    -- 重启游戏时触发断线会报错
    if ScreenShotWGCtrl and ScreenShotWGCtrl.Instance then
        ScreenShotWGCtrl.Instance:Resume()
    end
end

function ScreenShotView:SettingBGTween(is_show)
    if is_show then
        self.node_list.setting_bg.rect:DOAnchorPos(Vector2(0, 0), 0.5)
    else
        self.node_list.setting_bg.rect:DOAnchorPos(Vector2(500, 0), 0.5)
    end
end

function ScreenShotView:OnClickSnapShot()
    --隐藏剩余界面
    self.node_list.screenshoting_root:SetActive(false)
    self.node_list.game_name:SetActive(true)
    self.setting_bg_state = false
    --self.node_list.setting_bg:SetActive(false)
    self.node_list.Joystick:SetActive(false)
    ViewManager.Instance:Close(GuideModuleName.CustomActionWindowView)

    local click_effect_layer = GameObject.Find("GameRoot/UILayer/ClickEffectCanvas").transform
    click_effect_layer.gameObject:SetActive(false)

    if self.screen_texture then
        UnityEngine.GameObject.Destroy(self.screen_texture)
        self.screen_texture = nil
    end

    UtilU3d.ScreenshotByPng(function (texture)
        ScreenShotWGCtrl.Instance:OpenSnapShotView(texture)
        click_effect_layer.gameObject:SetActive(true)
        self.node_list.qufu_panel:SetActive(false)
        self.screen_texture = texture
    end)
end

function ScreenShotView:OnClickSetting()
    self.setting_bg_state = not self.setting_bg_state
    --self.node_list.setting_bg:SetActive(self.setting_bg_state)
    self.node_list.Joystick:SetActive(not self.setting_bg_state)
    self.node_list.btn_close_bg:SetActive(self.setting_bg_state)
    self:SettingBGTween(self.setting_bg_state)
    if self.setting_bg_state then
        self:FlushSettingItemList()
    end
end

function ScreenShotView:OnClickAdd()
    local value = self.slider.value
    value = value - 1
    if value < MinSliderValue then
        value = MinSliderValue
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ScreenShot.MinValue)
    end
    self.slider.value = value
end

function ScreenShotView:OnClickReduce()
    local value = self.slider.value
    value = value + 1
    if value > MaxSliderValue then
        value = MaxSliderValue
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ScreenShot.MaxValue)
    end
    self.slider.value = value
end

function ScreenShotView:OnClickCloseBG()
    self.setting_bg_state = not self.setting_bg_state
    --self.node_list.setting_bg:SetActive(self.setting_bg_state)
    self.node_list.Joystick:SetActive(not self.setting_bg_state)
    self.node_list.btn_close_bg:SetActive(self.setting_bg_state)
    self:SettingBGTween(self.setting_bg_state)
end

function ScreenShotView:OnClickExit()
    self:Resume()
end

function ScreenShotView:OnClickRest()
    if nil == self.alert_window then
        self.alert_window = Alert.New(nil, nil, nil, nil, nil, nil, nil, nil, true, UiLayer.Guide)
    end

    self.alert_window:SetLableString(Language.ScreenShot.RestCamera)
    self.alert_window:SetOkFunc(function ()
        self:SyncFormCameraFollow()
        self:PlayCameraTween()
    end)

    self.alert_window:Open()
end

function ScreenShotView:OnClickShow()
    self.show_state = not self.show_state
    self.node_list.Joystick:SetActive(self.show_state)
    self:FlushRootShow()
    if not self.show_state then
        ViewManager.Instance:Close(GuideModuleName.CustomActionWindowView)
    end
end

function ScreenShotView:FlushRootShow()
    local image = self.show_state and "a3_pz_an" or "a3_pz_an1"
    local bundle, asset = ResPath.GetSnapshotFrameImg(image)
    self.node_list.btn_show.image:LoadSpriteAsync(bundle, asset, function ()
        self.node_list.btn_show.image:SetNativeSize()
    end)

    self.node_list.root:SetActive(self.show_state)
end

function ScreenShotView:FlushSettingItemList()
    for k,v in pairs(self.shield_obj_list) do
        v:Flush()
    end
end

function ScreenShotView:OnSliderValueChanged(value)
    Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.SCENE_POS, nil, nil, value)
end

function ScreenShotView:OnViewCloseHandler(view)
    if view.view_name == GuideModuleName.SnapShotView then
        --恢复默认状态界面
        self.node_list.screenshoting_root:SetActive(true)
        self.node_list.game_name:SetActive(false)
        local is_select_qufu = ScreenShotWGData.Instance:GetIsSelect(self.qufu_index) == 1
        self.node_list.qufu_panel:SetActive(is_select_qufu)
        self.setting_bg_state = false
        self.node_list.setting_bg.rect.anchoredPosition = Vector2(500, 0)
        --self.node_list.setting_bg:SetActive(false)
        self.node_list.Joystick:SetActive(true)
    end
end

function ScreenShotView:OnCameraChange()
    self:SyncFormCameraFollow()
end

function ScreenShotView:OnClickOpenCustomAcrion()
    if YunbiaoWGData.Instance:GetIsHuShong() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.YunBiao.IsHuSong)
        return
    end

	local scene_cfg = Scene.Instance:GetCurFbSceneCfg()
	if scene_cfg and scene_cfg.pb_custom_action == 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoThis)
		return
	end
    
    CustomActionCtrl.Instance:OpenCustomActionWindowView(Vector2(0, 312))
end

--------------------------------------------------------------------------------------------------

ScreenSnapShotItem = ScreenSnapShotItem or BaseClass(BaseRender)
function ScreenSnapShotItem:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_select, BindTool.Bind(self.OnClickSelect, self))
end

function ScreenSnapShotItem:OnFlush()
    self.node_list.setting_text.text.text = self.data.name

    local flag = ScreenShotWGData.Instance:GetIsSelect(self.index)
    self.node_list.hook:SetActive(flag == 1)
end

function ScreenSnapShotItem:OnClickSelect()
    if self.index == 1 then
		local main_role = Scene.Instance:GetMainRole()
        if main_role and main_role:IsInteractive() then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoThis)
            return
        end
    end

    local active = self.node_list.hook:GetActive()
    self.node_list.hook:SetActive(not active)
    ScreenShotWGData.Instance:ChangeSelect(self.index, not active)
    ScreenShotWGCtrl.Instance:RegisterShieldRule(self.data.shield_type, active)
end