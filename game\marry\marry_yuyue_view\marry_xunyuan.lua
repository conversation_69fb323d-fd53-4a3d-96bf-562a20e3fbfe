--寻缘
--ProfessView = ProfessView or BaseClass(SafeBaseView)
local MAX_SEX = 2
local LEFT = 1
local RIGHT = 2

-- function ProfessView:__init()
-- 	self:SetMaskBg(true)
--     --self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_marry_xunyuan_panel")
--     self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_marry_xunyuan")
--     self.cur_page = 1
--     self.all_page = 1
--     self.select_sex = 2
--     self.xy_all_list = nil
-- end

function ProfessView:ReleaseXunYuanCallBack()
    if self.xy_all_list then
        self.xy_all_list:DeleteMe()
        self.xy_all_list = nil
    end

    self.cur_page = 1
    self.select_sex = 2
    MarryWGData.Instance:SetXunYuanSelectSex(self.select_sex)
end

function ProfessView:LoadXunYuanCallBack()
    self.all_page = 1
    self.select_sex = 2
    self.xy_all_list = nil
    XUI.AddClickEventListener(self.node_list.close_sex, BindTool.Bind(self.OnClickCloseSex, self))
    XUI.AddClickEventListener(self.node_list.btn_fabu, BindTool.Bind(self.OnClickFaBu, self))
    XUI.AddClickEventListener(self.node_list.left_arrow_btn, BindTool.Bind(self.OnClickChangePage, self, LEFT))
    XUI.AddClickEventListener(self.node_list.right_arrow_btn, BindTool.Bind(self.OnClickChangePage, self, RIGHT))
    XUI.AddClickEventListener(self.node_list.page_btn, BindTool.Bind(self.OnClickPage, self))
    XUI.AddClickEventListener(self.node_list.xunyuan_tip, BindTool.Bind(self.OnClickTipsBtn, self))
    for i = 0, MAX_SEX do
        XUI.AddClickEventListener(self.node_list["sex_btn_" .. i], BindTool.Bind(self.OnSelectSex, self, i))
    end

    if not self.xy_all_list then
        self.xy_all_list = AsyncBaseGrid.New()
        local bundle = "uis/view/marry_ui_prefab"
		local asset = "marry_xunyuan_item"
        self.xy_all_list:CreateCells({col = 3, change_cells_num = 1, list_view = self.node_list["xy_all_list"],
            assetBundle = bundle, assetName = asset, itemRender = MarryXunYuanItemRender})
        self.xy_all_list:SetStartZeroIndex(false)
    end

    self:OnSelectSex(self.select_sex)
end

function ProfessView:ShowXunYuanIndexCallBack()
    MarryWGCtrl.Instance:SendXunYuanOperateReq(XUNYUAN_OPERATE_TYPE.INFO, self.select_sex, self.cur_page)
end

function ProfessView:SetPageIndex(page)
    self.cur_page = page
end

function ProfessView:OnFlushXunYuan(page)
    if page and page > 0 then
        self.cur_page = page
        MarryWGCtrl.Instance:SendXunYuanOperateReq(XUNYUAN_OPERATE_TYPE.INFO, self.select_sex, self.cur_page)
        return
    end

    local data = MarryWGData.Instance:GetAllXunYuanInfo()
    local xunyuan_page_count = MarryWGData.Instance:GetXunYuanPageCount()
    local xunyuan_info_flag = MarryWGData.Instance:GetXunYuanInfoFlag()

    self.node_list.btn_fabu:CustomSetActive(xunyuan_info_flag == 0 and not MarryWGData.Instance:IsMarry())

    if IsEmptyTable(data) then
        if self.cur_page == 1 then
            self.node_list.xy_have_data:CustomSetActive(false)
            self.node_list.xy_all_no_data:CustomSetActive(true)
        else
            self.cur_page = self.cur_page - 1
            MarryWGCtrl.Instance:SendXunYuanOperateReq(XUNYUAN_OPERATE_TYPE.INFO, self.select_sex, self.cur_page)
        end
        return
    end

    self.node_list.xy_have_data:CustomSetActive(true)
    self.node_list.xy_all_no_data:CustomSetActive(false)
    self.all_page = xunyuan_page_count
    self.node_list.cur_page_text.text.text = self.cur_page .. "/"
    self.node_list.all_page_text.text.text = self.all_page
    self.node_list.left_arrow_btn:CustomSetActive(self.cur_page > 1)
    self.node_list.right_arrow_btn:CustomSetActive(self.cur_page < self.all_page)

    self.xy_all_list:SetDataList(data)
end

function ProfessView:OnSelectSex(sex)
    for i = 0, MAX_SEX do
        if i == sex then
            self.node_list["sex_btn_" .. i]:FindObj("Img"):SetActive(false)
            self.node_list["sex_btn_" .. i]:FindObj("Img_hl"):SetActive(true)
        else
            self.node_list["sex_btn_" .. i]:FindObj("Img"):CustomSetActive(true)
            self.node_list["sex_btn_" .. i]:FindObj("Img_hl"):CustomSetActive(false)
        end
    end

	self.select_sex = sex
    MarryWGData.Instance:SetXunYuanSelectSex(self.select_sex)
	self.node_list.cur_sex_text.text.text = Language.Marry.ChooseSexText[sex + 1]
	self.node_list.btn_select_sex.toggle.isOn = false
    MarryWGCtrl.Instance:SendXunYuanOperateReq(XUNYUAN_OPERATE_TYPE.INFO, self.select_sex, self.cur_page)
end

function ProfessView:OnClickCloseSex()
	self.node_list.btn_select_sex.toggle.isOn = false
end

function ProfessView:OnClickFaBu()
	MarryWGCtrl.Instance:SetXunYuanFaBuInfo()
    MarryWGCtrl.Instance:OpenXunYuanFaBuView()
end

function ProfessView:OnClickChangePage(index)
    if index == LEFT then
        if self.cur_page > 1 then
            self.cur_page = self.cur_page - 1
        else
            return
        end
    else
        if self.cur_page < self.all_page then
            self.cur_page = self.cur_page + 1
        else
            return
        end
    end

	MarryWGCtrl.Instance:SendXunYuanOperateReq(XUNYUAN_OPERATE_TYPE.INFO, self.select_sex, self.cur_page)
end

function ProfessView:OnClickTipsBtn()
    local role_tip = RuleTip.Instance
    role_tip:SetTitle(Language.Marry.XunYuanTipTitle)
    role_tip:SetContent(Language.Marry.XunYuanTipContent)
end

-- 点击数目，弹出小键盘
function ProfessView:OnClickPage()
	local function callback(input_num)
		self.cur_page = input_num
        MarryWGCtrl.Instance:SendXunYuanOperateReq(XUNYUAN_OPERATE_TYPE.INFO, self.select_sex, self.cur_page)
	end

	local num_keypad = TipWGCtrl.Instance:GetPopNumView()
	num_keypad:Open()
	num_keypad:SetNum(self.cur_page)
	num_keypad:SetMaxValue(self.all_page)
	num_keypad:SetMinValue(1)
	num_keypad:SetOkCallBack(callback)
end

------------------------MarryXunYuanItemRender---------------------------
MarryXunYuanItemRender = MarryXunYuanItemRender or BaseClass(BaseRender)

function MarryXunYuanItemRender:init()

end

function MarryXunYuanItemRender:LoadCallBack()
    if not self.head_cell then
        self.head_cell = BaseHeadCell.New(self.node_list["head_cell"])
    end

    XUI.AddClickEventListener(self.node_list.tiqin_btn, BindTool.Bind(self.OnClickTiQin, self))
    XUI.AddClickEventListener(self.node_list.add_friend_btn, BindTool.Bind(self.OnClickAddFriend, self))
    XUI.AddClickEventListener(self.node_list.delete_btn, BindTool.Bind(self.OnClickDelete, self))
    XUI.AddClickEventListener(self.node_list.modify_btn, BindTool.Bind(self.OnClickModify, self))
end

function MarryXunYuanItemRender:__delete()
    if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end

function MarryXunYuanItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local is_me = self.data.role_uid == RoleWGData.Instance:InCrossGetOriginUid()
    self.node_list.other_role_content:CustomSetActive(not is_me)
    self.node_list.cur_role_content:CustomSetActive(is_me)
    local color = is_me and "#471c5b" or "#a72676"
    local color2 = is_me and "#6b4170" or "#804b6c"

    if self.data.is_online == 1 then
        self.node_list.online_state.text.text = Language.Marry.OnlineState
        self.node_list.tiqin_btn:CustomSetActive(not MarryWGData.Instance:IsMarry())
        self.node_list.add_friend_btn:CustomSetActive(true)
    else
        self.node_list.online_state.text.text = Language.Marry.OfflineState
        self.node_list.tiqin_btn:CustomSetActive(false)
        self.node_list.add_friend_btn:CustomSetActive(false)
    end

    if nil ~= SocietyWGData.Instance:FindFriend(self.data.role_uid) then
        self.node_list.add_friend_btn:CustomSetActive(false)
    end

    self.node_list.role_name.text.text = ToColorStr(self.data.role_name, color)

    local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.data.role_level)

    if is_vis then
        self.node_list["TextLevel"].text.text = ToColorStr(role_level, color)
    else
        self.node_list["TextLevel"].text.text = ToColorStr("Lv." .. role_level, color)
    end

    self.node_list["level_icon"]:SetActive(is_vis)
    self.node_list["interval"]:SetActive(not is_vis)
	self.node_list["ContentText"].text.text = ToColorStr(self.data.zhenghun_info, color2)  --表白内容

    self:SetHeadCell()
end

function MarryXunYuanItemRender:SetHeadCell()
    local data = {role_id = self.data.role_uid, prof = self.data.prof, sex = self.data.role_sex, fashion_photoframe = 0}
    self.head_cell:SetData(data)

    local bundle = "uis/view/marry_ui/images_atlas"
    local asset = "a3_qy_tq_yd"
    self.head_cell:ChangeBg(bundle, asset, true)
end

function MarryXunYuanItemRender:OnClickTiQin()
    MarryWGCtrl.Instance:OpenTiQinView(self.data.role_uid, self.data.role_name, self.data.prof, self.data.role_sex)
end

function MarryXunYuanItemRender:OnClickAddFriend()
    if nil ~= SocietyWGData.Instance:FindFriend(self.data.role_uid) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.IsFriend)
        return
    end

    SocietyWGCtrl.Instance:IAddFriend(self.data.role_uid)
end

function MarryXunYuanItemRender:OnClickDelete()
    MarryWGCtrl.Instance:SendXunYuanOperateReq(XUNYUAN_OPERATE_TYPE.DELETE, MarryWGData.Instance:GetXunYuanSelectSex())
end

function MarryXunYuanItemRender:OnClickModify()
    MarryWGCtrl.Instance:SetXunYuanModifyInfo(self.data.zhenghun_info)
    MarryWGCtrl.Instance:OpenXunYuanFaBuView()
end