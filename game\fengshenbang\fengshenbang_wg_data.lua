FengShenBangWGData = FengShenBangWGData or BaseClass()

function FengShenBangWGData:__init()
	if FengShenBangWGData.Instance then
		error("[FengShenBangWGData]:Attempt to create singleton twice!")
		return
	end
	FengShenBangWGData.Instance = self
	self:InitData()
	self:RegisterRemind()
end

function FengShenBangWGData:__delete()
	FengShenBangWGData.Instance = nil
	self:UnRegisterRemind()
end

function FengShenBangWGData:InitData()
	self.fengshenbang_cfg = ConfigManager.Instance:GetAutoConfig("oga_godsrank_auto")
	self.act_rank_info = nil
	self.act_show_info = nil
	self.title_list = nil
	self.select_index = 0
	self.scene_id = 0
	self.rank_role_info_list = {}
	self.role_person_info = nil
	self.scene_info = nil
	self.xianwei_rob_info = nil
	self.first_onlie_remind = true
end

function FengShenBangWGData:RegisterRemind()
	RemindManager.Instance:Register(RemindName.FengShenBang, BindTool.Bind(self.IsShowFSBRedPoint, self))	-- 活动主界面红点
end

function FengShenBangWGData:UnRegisterRemind()
	RemindManager.Instance:UnRegister(RemindName.FengShenBang)
end

function FengShenBangWGData:SetSelectIndex(value)
	self.select_index = value
end

function FengShenBangWGData:GetSelectIndex()
	return self.select_index
end

function FengShenBangWGData:SetFSBSceneId(scene_id)
	self.scene_id = scene_id
end

function FengShenBangWGData:GetFSBSceneId(scene_id)
	return self.scene_id
end

-- 五大仙位排行数据
function FengShenBangWGData:SetActRankInfo(protocol)
	local act_rank_info = {}
	act_rank_info.delay_count = protocol.delay_count
	act_rank_info.act_is_close = protocol.oga_godsrank_activity_close == 1
	act_rank_info.rank_role_uid_list = protocol.oga_godsrank_roleuid_list
	act_rank_info.kill_cost_time_list = protocol.oga_godsrank_boss_kill_cost_times
	act_rank_info.kill_boss_normal_count_list = protocol.oga_godsrank_boss_kill
	act_rank_info.kill_boss_speical_count_list = protocol.oga_godsrank_boss_special_kill
	self.act_rank_info = act_rank_info
end

function FengShenBangWGData:GetActRankInfo()
	return self.act_rank_info
end

-- 活动结束后展示五个仙位排行数据
function FengShenBangWGData:SetActRankInfoByClose(protocol)
	local act_show_info = {}
	act_show_info.rank_role_uid_list = protocol.oga_godsrank_roleuid_list
	self.act_show_info = act_show_info
end

function FengShenBangWGData:GetActRankInfoByClose()
	return self.act_show_info
end

function FengShenBangWGData:GetRankRoleUidList()
	local rank_info = self:GetActRankInfo() or self:GetActRankInfoByClose()
	local role_uid_list = rank_info and rank_info.rank_role_uid_list
	return role_uid_list
end

-- 仙位排行上的人物数据
function FengShenBangWGData:SetRankRoleInfo(role_uid,info)
	-- self.rank_role_info_list[role_uid] = DeepCopy(info)
	local info_list = {}
	for k,v in pairs(info) do
		info_list[k] = v
	end
	self.rank_role_info_list[role_uid] = info_list
end

function FengShenBangWGData:GetRankRoleInfo(role_uid)
	return self.rank_role_info_list[role_uid]
end

function FengShenBangWGData:GetRankRoleInfoByIndex(index)
	local rank_info = self:GetActRankInfo()
	local role_uid_list = rank_info and rank_info.rank_role_uid_list or {}
	local gurad_info = self:GetRankRoleInfo(role_uid_list[index])
	return gurad_info
end

-- 玩家个人数据
function FengShenBangWGData:SetRolePersonInfo(protocol)
	local role_person_info = {}
	role_person_info.next_challenge_timestamp = protocol.next_challenge_timestamp
	role_person_info.day_join_reward_num = protocol.day_join_reward_num
	self.role_person_info = role_person_info
end

function FengShenBangWGData:GetRolePersonInfo()
	return self.role_person_info
end

function FengShenBangWGData:GetDayTotalJoinTime()
	return self.role_person_info and self.role_person_info.day_join_reward_num
end

-- 副本内数据
function FengShenBangWGData:SetSceneInfo(protocol)
	local scene_info = {}
	scene_info.finish = protocol.finish == 1
	scene_info.pass = protocol.pass == 1
	scene_info.enter_timestamp = protocol.enter_timestamp
	scene_info.end_times_tamp = protocol.end_times_tamp
	scene_info.reward_coin_flag = protocol.reward_coin_flag
	self.scene_info = scene_info
end

function FengShenBangWGData:GetSceneInfo()
	return self.scene_info
end

function FengShenBangWGData:GetJoinRewardShowState()
	--print_error("FFFF====== self.scene_info.reward_coin_flag", self.scene_info.reward_coin_flag)
	return self.scene_info and self.scene_info.reward_coin_flag == 1
end

-- 仙位被抢数据
function FengShenBangWGData:SetXianWeiRobInfo(protocol)
	local xianwei_rob_info = {}
	xianwei_rob_info.rank_index = protocol.rank_index
	xianwei_rob_info.role_uid = protocol.role_uid
	xianwei_rob_info.role_name = protocol.role_name
	xianwei_rob_info.is_remind = true
	self.xianwei_rob_info = xianwei_rob_info
end

function FengShenBangWGData:GetXianWeiRobInfo()
	return self.xianwei_rob_info
end

function FengShenBangWGData:GetBossKillCount(index)
	local noraml_count = 0
	local special_count = 0
	local act_rank_info = self:GetActRankInfo()
	if act_rank_info then
		noraml_count = act_rank_info.kill_boss_normal_count_list[index]
		special_count = act_rank_info.kill_boss_speical_count_list[index]
	end
	return noraml_count,special_count
end

-- boss信息
function FengShenBangWGData:GetBossInfo(index,scene_id)
	local scene_cfg = nil
	if index then
		scene_cfg = self:GetSceneCfgData(index)
	elseif scene_id then
		scene_cfg = self:GetSceneCfgDataBySceneID(scene_id)
		index = scene_cfg.layer + 1
	end
	local monster_cfg = scene_cfg and BossWGData.Instance:GetMonsterInfo(scene_cfg.boss_id)
	local grow_up_cfg = scene_cfg and self:GetBossGrowUpCfg(scene_cfg.boss_id)
	if not monster_cfg or not grow_up_cfg then
		return
	end

	local noraml_count,special_count = self:GetBossKillCount(index)
	local grow_up_value = noraml_count + special_count * grow_up_cfg.up_special
	local boss_info = {}
	boss_info.level = monster_cfg.level
	boss_info.name = monster_cfg.name
	boss_info.id = monster_cfg.id

	if grow_up_value > 0 then
		local add_capability = monster_cfg.capability * 0.0001 * grow_up_cfg.capability * grow_up_value
		add_capability = math.floor(add_capability)
		boss_info.capability = monster_cfg.capability + add_capability
	else
		boss_info.capability = monster_cfg.capability
	end

	return boss_info
end

-- 称号属性
function FengShenBangWGData:GetTitleAttrList(title_id, need_cap)
	local title_cfg = TitleWGData.Instance:GetConfig(title_id)
	if not title_cfg then
		return
	end
	local attr_info = AttributeMgr.GetAttributteByClass(title_cfg)
	local attr_sort = AttributeMgr.GetAttrList()
	local attr_list = {}
	for i,v in ipairs(attr_sort) do
		if attr_info[v] and attr_info[v] > 0 then
			attr_list[#attr_list + 1] = {attr_key = v, attr_value = attr_info[v]}
		end
	end
	return attr_list, need_cap and AttributeMgr.GetCapability(attr_info)
end

-- 活动是否开启
function FengShenBangWGData:GetActIsOpen()
	local other_info = FengShenBangWGData.Instance:GetActCfgList("other")
	if other_info then
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		if open_day >= other_info.open_server_day then
			local now_time = TimeWGCtrl.Instance:GetServerTime()
			local start_time, clear_time, close_time = self:GetActTime()
			return close_time > now_time
		end
	end
	return false
end

-- 活动是否结算了
function FengShenBangWGData:GetActIsClear()
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	local start_time, clear_time, close_time = self:GetActTime()
	local delay_time = FengShenBangWGData.Instance:GetActDelayTime()
	return now_time > clear_time + delay_time
end

-- 活动开始时间戳 活动结算时间戳 活动结束时间戳 活动预告时间戳
function FengShenBangWGData:GetActTime()
	local other_info = FengShenBangWGData.Instance:GetActCfgList("other")
	local open_day_time = TimeWGCtrl.Instance:GetServerRealStartTime() --开服时间戳

	if open_day_time <= 0 then
		return 0, 0, 0, 0
	end

	local open_day = other_info.open_server_day
	open_day = open_day > 0 and open_day - 1 or 0
	local format_time = os.date("*t", open_day_time)
	local start_time = os.time({
		year = format_time.year,
		month = format_time.month,
		day = format_time.day + open_day,
		hour = 0,
		min = 0,
		sec = 0
	})

	if not start_time then
		return 0, 0, 0, 0
	end

	local close_tab = Split(other_info.dur_activity_time, "|")
	local clear_tab = Split(other_info.settle_activity_time, "|")
	local close_day = tonumber(close_tab[1]) or 0
	local close_hour = tonumber(close_tab[2]) or 0
	local clear_day = tonumber(clear_tab[1]) or 0
	local clear_hour = tonumber(clear_tab[2]) or 0
	local close_time = start_time + (close_day - other_info.open_server_day) * 24 * 3600 + close_hour * 3600
	local clear_time = start_time + (clear_day - other_info.open_server_day) * 24 * 3600 + clear_hour * 3600
	local yugao_time = start_time - (24 - other_info.yugao_time) * 3600

	return start_time, clear_time, close_time, yugao_time
end

-- 活动延时结算时间(s)
function FengShenBangWGData:GetActDelayTime()
	local rank_info = self:GetActRankInfo()
	local other_info = FengShenBangWGData.Instance:GetActCfgList("other")
	if rank_info and other_info and other_info.delay_challenge_cd then
		return other_info.delay_challenge_cd * rank_info.delay_count
	end
	return 0
end

function FengShenBangWGData:GetActStatus()
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	local start_time, clear_time, close_time, yugao_time = self:GetActTime()
	local act_status = 0
	if now_time > close_time then
		act_status = ACTIVITY_STATUS.CLOSE
	elseif now_time >= start_time then
		act_status = ACTIVITY_STATUS.OPEN
	elseif now_time >= yugao_time then
		act_status = ACTIVITY_STATUS.STANDY
	end
	return act_status
end

---[[ 表格数据
function FengShenBangWGData:GetActCfgList(key)
	if key == "other" and self.fengshenbang_cfg[key] then
		return self.fengshenbang_cfg[key][1]
	else
		return self.fengshenbang_cfg[key]
	end
end

-- 获取称号数据
function FengShenBangWGData:GetTitleList(index)
	if not self.title_list then
		local cfg_list = self:GetActCfgList("scene")
		local title_list = {}
		if cfg_list then
			for i=0,#cfg_list do
				title_list[i + 1] = {title_id = cfg_list[i].title_id, stuff_id = cfg_list[i].reward_title_id}
			end
		end
		self.title_list = title_list
	end
	if index then
		return self.title_list[index]
	else
		return self.title_list
	end
end

-- 获取场景数据
function FengShenBangWGData:GetSceneCfgData(index)
	local cfg_list = self:GetActCfgList("scene")
	if index then
		return cfg_list[index - 1]
	end
end

function FengShenBangWGData:GetSceneCfgDataBySceneID(scene_id)
	local cfg_list = self:GetActCfgList("scene")
	for i=0,#cfg_list do
		if cfg_list[i].scene_id == scene_id then
			return cfg_list[i]
		end
	end
end

function FengShenBangWGData:GetSceneCfgDataByTitleID(title_id)
	local cfg_list = self:GetActCfgList("scene")
	for i=0,#cfg_list do
		if cfg_list[i].title_id == title_id then
			return cfg_list[i]
		end
	end
end

-- boss成长属性
function FengShenBangWGData:GetBossGrowUpCfg(boss_id)
	local cfg_list = self:GetActCfgList("boss")
	for i=1,#cfg_list do
		if cfg_list[i].bossid == boss_id then
			return cfg_list[i]
		end
	end
end
--]]

function FengShenBangWGData:IsFSBTitle(title_id)
	local title_list = self:GetTitleList()
	for i=1,#title_list do
		if title_list[i].title_id == title_id then
			return true
		end
	end
end

function FengShenBangWGData:IsShowFSBRedPoint()
	if self:GetActIsClear() then
		return 0
	end
	if self:IsXianWeiRob() then
		return 1
	elseif self.first_onlie_remind then
		self.first_onlie_remind = false
		return 1
	end
	return 0
end

-- 仙位被抢红点提示
function FengShenBangWGData:IsXianWeiRob()
	local xianwei_rob_info = self:GetXianWeiRobInfo()
	return xianwei_rob_info and xianwei_rob_info.is_remind
end
