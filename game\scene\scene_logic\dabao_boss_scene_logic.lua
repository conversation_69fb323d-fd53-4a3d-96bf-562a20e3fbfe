local BOSS_REBIRTH_REMIND_INTERVAL = 30  -- s

<PERSON><PERSON>BossSceneLogic = DabaoBossSceneLogic or BaseClass(CommonFbLogic)

function DabaoBossSceneLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
end

function DabaoBossSceneLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
end

function DabaoBossSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	local _, cur_layer, _ = BossWGData.Instance:GetCurSelectBossID()
	MainuiWGCtrl.Instance:ResetLightBoss()
	BossWGCtrl.Instance:SendDongKuBossReq(BossView.ReqType.SINGLELAYERINFO, cur_layer)

	self.main_role_move_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_POS_CHANGE, BindTool.Bind(self.OnMainRoleMove, self))
	self.refresh_boss_list = GlobalEventSystem:Bind(OtherEventType.BOSS_CHANGE, BindTool.Bind(self.CheckUseBossReflushCard, self))

	-- MainuiWGCtrl.Instance:SetTaskButtonTrue()
	GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,true)
	BaseFbLogic.SetLeaveFbTip(true)
	BossWGCtrl.Instance:EnterSceneCallback()
	BossWGCtrl.Instance:Close()
	-- local select_obj_list = Scene.Instance:GetRoleList()
	-- local obj_select = nil
	-- for _,v in pairs(select_obj_list) do
	-- 	obj_select = v
	-- 	break
	-- end
	-- if obj_select then
	-- 	GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj_select, SceneTargetSelectType.SELECT)
	-- end

	FuhuoWGCtrl.Instance:SetFuhuoMustCallback(BindTool.Bind(self.FuHuoCallBack, self))
	
	BossPrivilegeWGCtrl.Instance:EnterBossPrivilegeSceneCallBack()

	self.boss_rebirth_boss_cfg = {}
	self.last_remind_time = 0
end

function DabaoBossSceneLogic:FuHuoCallBack(use_type)
	local tarck_type, track_role_uuid = self:GetTrackRoleInfo()
	if tarck_type == OBJ_FOLLOW_TYPE.TEAM then
		return
	end
	
	use_type = use_type or FuHuoType.Common
	if use_type == FuHuoType.Common then
		self:CommonMoveCallBack()
	else
		self:HereFuHuoCallBack()
	end
end

function DabaoBossSceneLogic:CommonMoveCallBack()
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
end

function DabaoBossSceneLogic:HereFuHuoCallBack()
	local select_obj = nil
	if GuajiCache.target_obj then
		select_obj = GuajiCache.target_obj
	end

	if select_obj and not select_obj:IsDeleted() and Scene.Instance:IsEnemy(select_obj) then
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
		GuajiCache.target_obj = select_obj
		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, select_obj, SceneTargetSelectType.SELECT)
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	else
		self:CommonMoveCallBack()
	end

end

-- 获取挂机打怪的位置
function DabaoBossSceneLogic:GetGuiJiMonsterPos()
	CommonFbLogic.GetGuiJiMonsterPos(self)
	local target_distance = 20 * 20
	local target_x = 0
    local target_y = 0
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()

	local obj_move_info_list = Scene.Instance:GetObjMoveInfoList()

	for k, v in pairs(obj_move_info_list) do
		local vo = v:GetVo()
		if vo.obj_type == SceneObjType.Monster and BaseSceneLogic.IsAttackMonster(vo.type_special_id, vo) then
			local distance = GameMath.GetDistance(x, y, vo.pos_x, vo.pos_y, false)
			if distance < target_distance then
				target_x = vo.pos_x
                target_y = vo.pos_y
				target_distance = distance
			end
		end
	end

	return target_x, target_y
end

function DabaoBossSceneLogic:OnObjCreate(obj)
	--if obj and not SceneObj.select_obj and self:IsEnemy(obj) then
	--	GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj, SceneTargetSelectType.SELECT)
	--end
end

function DabaoBossSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)
	BossWGCtrl.Instance:OutSceneCallback(true)
	BossWGCtrl.Instance:SendDongKuBossReq(BossView.ReqType.ALLINFO)
	BossWGData.Instance:SetDabaoBossAngryValue({angry_value = 0})
	BossWGCtrl.Instance:CloseDriveView()
	GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,false)
	FuhuoWGCtrl.Instance:ClearFuhuoMustCallback()

	if BossWGData.Instance:GetIsEnterInScene() then
		BossWGCtrl.Instance:OpenBossViewByScene(old_scene_type, new_scene_type)
	end

	if self.create_obj_event then
		GlobalEventSystem:UnBind(self.create_obj_event)
		self.create_obj_event = nil
	end

	if self.main_role_move_event then
		GlobalEventSystem:UnBind(self.main_role_move_event)
		self.main_role_move_event = nil
   end

   	if self.refresh_boss_list then
		GlobalEventSystem:UnBind(self.refresh_boss_list)
		self.refresh_boss_list = nil
	end

	BossPrivilegeWGCtrl.Instance:OutBossPrivilegeSceneCallBack()
	self.boss_rebirth_boss_cfg = nil
	self.last_remind_time = nil

	if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end
end

function DabaoBossSceneLogic:OpenFbSceneCd()

end

-- 获取挂机打怪的敌人(优先级： 优先打角色，如果点击前往击杀则优先打BOSS)
function DabaoBossSceneLogic:GetGuajiCharacter()
	local is_need_stop = false

	local target_obj = self:GetMonster()
	if target_obj ~= nil then
		is_need_stop = true
		return target_obj, nil, is_need_stop
	end

	if target_obj == nil then
		target_obj, is_need_stop = self:GetNormalRole()
		return target_obj, nil, is_need_stop
	end
end

function DabaoBossSceneLogic:GetNormalRole()
	local role_list = Scene.Instance:GetRoleList()
	local info = self:GetGuaJiInfo()
	local is_stop = info ~= nil

	for k,v in pairs(role_list) do
		if self:IsEnemy(v) then
			if info ~= nil then
				if not v:IsDeleted() then
					local pos_x, pos_y = v:GetLogicPos()
					local dis = GameMath.GetDistance(info.x, info.y, pos_x, pos_y, false)
					if dis <= info.aoi_range * info.aoi_range then
						GuajiCache.target_obj = v
						return v, is_stop
					end
				end
			else
				GuajiCache.target_obj = v
				return v, is_stop
			end
		end
	end

	return nil, is_stop
end

function DabaoBossSceneLogic:GetMonster()
	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local main_role = Scene.Instance:GetMainRole()
	local obj = nil
	if main_role ~= nil then
		local x, y = main_role:GetLogicPos()

		local info = self:GetGuaJiInfo()
		if info ~= nil then
			distance_limit = info.aoi_range * info.aoi_range
			x = info.x
			y = info.y
		end	
		
		obj = Scene.Instance:SelectObjHelper(SceneObjType.Monster, x, y, distance_limit, SelectType.Enemy)
	end

	return obj
end

function DabaoBossSceneLogic:GetFbSceneMonsterListCfg(monsters_list_cfg)
	local monsters_list = MapWGData.Instance:GetSceneMonsterSort(monsters_list_cfg)
	return BossWGData.Instance:GetCurSceneAllMonster(monsters_list), true
end

function DabaoBossSceneLogic:GetFbSceneMonsterBossCfg()
	return BossWGData.Instance:GetCurSceneAllBoss()
end

-- 此场景优先保证单位数量
function DabaoBossSceneLogic:GetSceneQualityPolicy()
	return QualityPolicy.UnitCount
end

-- 此场景优先显示敌人(false则优先显示队员)
function DabaoBossSceneLogic:IsEnemyVisiblePriortiy()
	return true
end

function DabaoBossSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
	self:CheckGuaJiPosMove()
end

-- 主角移动事件
function DabaoBossSceneLogic:OnMainRoleMove()
	-- self:CheckUseBossReflushCard()
end

function DabaoBossSceneLogic:CheckUseBossReflushCard()
	-- 是否今日不再提醒
	-- if self:TodayIgnoreBossReflushRemind() then
	-- 	return
	-- end

	local main_role = Scene.Instance:GetMainRole()
	if not main_role then
		return
	end

	local boss_list = self:GetFbSceneMonsterBossCfg()
	if IsEmptyTable(boss_list) then
		return
	end

	local pos_x, pos_y = main_role:GetLogicPos()
	local target_distance = 1000 * 1000
	local boss_rebirth_cfg = MainuiWGData.Instance:GetBossRebirthCfg()
	local rebirth_range = boss_rebirth_cfg.rebirth_range
	local rebirth_dis = rebirth_range * rebirth_range
	local boss_cfg

	for k, v in pairs(boss_list) do
		local dis = GameMath.GetDistance(v.x, v.y, pos_x, pos_y, false)
		if dis < rebirth_dis and dis < target_distance then
			boss_cfg = v
			target_distance = dis
		end
	end

	if boss_cfg then
		local boss_state = BossWGData.Instance:GetBossStatusByBossId(boss_cfg.boss_id)
		if boss_state == 0 then
			local server_time = TimeWGCtrl.Instance:GetServerTime()

			local need_remind = false
			if IsEmptyTable(self.boss_rebirth_boss_cfg) then
				need_remind = true
			else
				local cache_boss_cfg = self.boss_rebirth_boss_cfg
				local is_same_boss_cfg = cache_boss_cfg.boss_id == boss_cfg.boss_id and cache_boss_cfg.x == boss_cfg.x and cache_boss_cfg.y == boss_cfg.y
				if not is_same_boss_cfg or (is_same_boss_cfg and (self.last_remind_time + BOSS_REBIRTH_REMIND_INTERVAL) < server_time) then
					need_remind = true
				end
			end

			if need_remind then
				self.boss_rebirth_boss_cfg = boss_cfg
				local rebirth_item, has_num = MainuiWGData.Instance:GetBossRefreshItemIdStuff()
				if rebirth_item then
					if self:TodayIgnoreBossReflushRemind() then
						BossWGCtrl.Instance:DoOperaBossRefresh(rebirth_item)
					else
						if not self:TodayNoTips() and BossWGData.Instance:IsBossQuickRebirthNotCountDown() then
							BossWGCtrl.Instance:OpenBossQuickRebirthShow()
						end
					end
				end
			end
		else
			self.boss_rebirth_boss_cfg = {}
		end
	end
end

function DabaoBossSceneLogic:TodayIgnoreBossReflushRemind()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local rebirth_reflush_status = PlayerPrefsUtil.GetInt("boss_quick_rebirth_reflush" .. main_role_id)
	return rebirth_reflush_status == 1
end

function DabaoBossSceneLogic:TodayNoTips()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local has_today_flag = PlayerPrefsUtil.GetInt("boss_quick_rebirth_tips" .. main_role_id) == open_day
	return has_today_flag
end