TreasurePalaceData = TreasurePalaceData or BaseClass()

TreasurePalace_Model_Type = {
    LifeTime = 1,
    EveryDay = 2,
}

function TreasurePalaceData:__init()
	if TreasurePalaceData.Instance then
		error("[TreasurePalaceData] Attempt to create singleton twice!")
		return
	end

    TreasurePalaceData.Instance = self

    RemindManager.Instance:Register(RemindName.Treasurepalace_Lifetime_Recharge, BindTool.Bind(self.GetLifeTimeRechargeRemind, self))
    RemindManager.Instance:Register(RemindName.Treasurepalace_Everyday_Recharge, BindTool.Bind(self.GetEverydayRechargeRemind, self))
    RemindManager.Instance:Register(RemindName.Treasurepalace_Enter_Reward, BindTool.Bind(self.GetEnterRewardRemind, self))
    RemindManager.Instance:Register(RemindName.Treasurepalace_Rebate, BindTool.Bind(self.GetRebateRemind, self))
    RemindManager.Instance:Register(RemindName.Treasurepalace_Gift, BindTool.Bind(self.GetGiftRemind, self))

    self:InitParam()
    self:InitConfig()
end

function TreasurePalaceData:__delete()
    RemindManager.Instance:UnRegister(RemindName.TreasurePalace)
    RemindManager.Instance:UnRegister(RemindName.Treasurepalace_Lifetime_Recharge)
    RemindManager.Instance:UnRegister(RemindName.Treasurepalace_Everyday_Recharge)
    RemindManager.Instance:UnRegister(RemindName.Treasurepalace_Enter_Reward)
    RemindManager.Instance:UnRegister(RemindName.Treasurepalace_Rebate)
    RemindManager.Instance:UnRegister(RemindName.Treasurepalace_Gift)

    TreasurePalaceData.Instance = nil
end

function TreasurePalaceData:InitParam()
    self.save_model_list = {}
    self.history_toggle_list = {}

    self.history_rmb_num = 0
    self.today_rmb_num = 0
    self.history_reward_flag = {}
    self.today_reward_flag = {}
    self.today_info = {}
end

function TreasurePalaceData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("zhenbaodian_cfg_auto")
    self.history_reward_cfg = cfg.history_reward
    self.today_reward_cfg = cfg.today_reward
    self.other_cfg = cfg.other[1]
    self.vip_gift_cfg = cfg.vip_gift
    self.reward_show_cfg = ListToMap(cfg.reward_show, "seq", "reward_seq")
end

-------------- 红点相关strat --------------
function TreasurePalaceData:GetLifeTimeRechargeRemind()
    for k, v in pairs(self.history_reward_cfg) do
        if self.history_rmb_num >= v.need_num and self:GetHistoryRewardBySeq(v.seq) == 0 then
            return 1
        end
    end

    return 0
end

function TreasurePalaceData:GetEverydayRechargeRemind()
    for k, v in pairs(self.today_info) do
        if self.today_rmb_num >= v.cfg.need_num and v.lq_state == 0 then
            return 1
        end
    end

    return 0
end
-------------- 红点相关end --------------

-------------- 协议相关strat --------------
function TreasurePalaceData:SetTreasurePalaceAllInfo(protocol)
    self.history_rmb_num = protocol.history_rmb_num		        --活动历史真冲数
	self.history_reward_flag = protocol.history_reward_flag     --奖励标记
	self.today_rmb_num = protocol.today_rmb_num			        --活动每日真冲数
	self.today_reward_flag = protocol.today_reward_flag	        --每日奖励标识

    self.enter_game_reward = protocol.enter_game_reward -- 进游奖励标识
    self.vip_gift = protocol.vip_gift                   -- vip礼包获取次数
    self.server_gift_count = protocol.server_gift_count -- 全服礼包获取次数

    self:UpdataTodayInfo()
end

function TreasurePalaceData:SetTreasurePalaceRewardInfo(protocol)
    self.history_reward_flag = protocol.history_reward_flag
    self.today_reward_flag = protocol.today_reward_flag

    self:UpdataTodayInfo()
end

function TreasurePalaceData:SetTreasurePalaceRecharge(protocol)
    self.history_rmb_num = protocol.history_rmb_num
    self.today_rmb_num = protocol.today_rmb_num
end
-------------- 协议相关end --------------

-------------- 获取数据相关strat --------------
function TreasurePalaceData:GetOneRemindTabIndex()
    if self:GetLifeTimeRechargeRemind() > 0 then
        return TabIndex.treasure_palace_lifetime_recharge
    elseif self:GetEverydayRechargeRemind() > 0 then
        return TabIndex.treasure_palace_everyday_recharge
    else
        return TabIndex.treasure_palace_lifetime_recharge
    end
end

function TreasurePalaceData:GetHistoryRMBNum()
    return self.history_rmb_num
end

function TreasurePalaceData:GetTodayRMBNum()
    return self.today_rmb_num
end

function TreasurePalaceData:GetHistoryToggleDataList()
    if not IsEmptyTable(self.history_toggle_list) then
        return self.history_toggle_list
    end

    for k, v in pairs(self.history_reward_cfg) do
        self.history_toggle_list[k] = {need_num = v.need_num, seq = v.seq}
    end

    return self.history_reward_cfg
end

function TreasurePalaceData:GetHistoryCfgBySeq(seq)
    return self.history_reward_cfg[seq]
end

function TreasurePalaceData:UpdataTodayInfo()
    local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local today_info = {}

    for k, v in pairs(self.today_reward_cfg) do
        if v.min_open_day <= server_day and server_day <= v.max_open_day then
            local data = {}
            data.seq = v.seq
            data.cfg = v
            data.lq_state = self:GetTodayRewardBySeq(data.seq)
            table.insert(today_info, data)
        end
    end

    if not IsEmptyTable(today_info) then
        table.sort(today_info, SortTools.KeyLowerSorters("lq_state", "seq"))
    end

    self.today_info = today_info
end

function TreasurePalaceData:GetTodayInfo()
    return self.today_info
end

function TreasurePalaceData:GetHistoryRewardBySeq(seq)
    return self.history_reward_flag[seq] or 0
end

function TreasurePalaceData:GetTodayRewardBySeq(seq)
    return self.today_reward_flag[seq] or 0
end

function TreasurePalaceData:GetModelDataByTypeAndSeq(model_type, seq)
    if self.save_model_list[model_type] then
        if self.save_model_list[model_type][seq] then
            return self.save_model_list[model_type][seq]
        end
    else
        self.save_model_list[model_type] = {}
    end

    self.save_model_list[model_type][seq] = self:CreateModelData(model_type, seq)
    return self.save_model_list[model_type][seq]
end

function TreasurePalaceData:GetLifeTimeToggleListRemindIndex()
    for i = 0, #self.history_reward_cfg do
        local data = self.history_reward_cfg[i]
        if self.history_rmb_num >= data.need_num and self:GetHistoryRewardBySeq(data.seq) == 0 then
            return data.seq, true
        end
    end

    return 0, false
end

-------------- 获取数据相关end --------------

function TreasurePalaceData:CreateModelData(model_type, seq)
    local show_data
    local model_data_count = 1
    if model_type == TreasurePalace_Model_Type.LifeTime then
        show_data = self.history_reward_cfg[seq]
        model_data_count = 2
    elseif model_type == TreasurePalace_Model_Type.EveryDay then
        show_data = self.other_cfg
    end

    local new_mode_data = {}
    for i = 1, model_data_count do
        local display_data = {}
        local model_show_type = show_data["model_show_type" .. i]

        if model_show_type == 0 then
            break
        end

        local model_show_itemid = show_data["model_show_itemid" .. i]
        local model_bundle_name = show_data["model_bundle_name" .. i]
        local model_asset_name = show_data["model_asset_name" .. i]

        display_data.should_ani = true
        if model_show_itemid ~= 0 and model_show_itemid ~= "" then
            local split_list = string.split(model_show_itemid, "|")
            if #split_list > 1 then
                local list = {}
                for k, v in pairs(split_list) do
                    list[tonumber(v)] = true
                end
                display_data.model_item_id_list = list
            else
                display_data.item_id = model_show_itemid
            end
        end

        display_data.hide_model_block = false
        display_data.bundle_name = model_bundle_name
        display_data.asset_name = model_asset_name
        display_data.render_type = model_show_type - 1
        display_data.need_wp_tween = true

        if model_show_type == 1 and model_bundle_name and model_bundle_name ~= "" then
            display_data.need_wp_tween = false
        end

        local transform_info = {}
        local display_pos = show_data["display_pos" .. i]
        local display_rotation = show_data["display_rotation" .. i]
        local display_scale = show_data["display_scale" .. i]

        local pos_x, pos_y = 0, 0
	    if display_pos and display_pos ~= "" then
	    	local pos_list = string.split(display_pos, "|")
	    	pos_x = tonumber(pos_list[1]) or pos_x
	    	pos_y = tonumber(pos_list[2]) or pos_y
	    end

        transform_info.pos_x = pos_x
	    transform_info.pos_y = pos_y

        local rot_x, rot_y, rot_z = 0, 0, 0
	    if display_rotation and display_rotation ~= "" then
	    	local rot_list = string.split(display_rotation, "|")
	    	rot_x = tonumber(rot_list[1]) or rot_x
	    	rot_y = tonumber(rot_list[2]) or rot_y
	    	rot_z = tonumber(rot_list[3]) or rot_z
	    end

        transform_info.rot_x = rot_x
	    transform_info.rot_y = rot_y
	    transform_info.rot_z = rot_z

        local scale = display_scale
        transform_info.scale = (scale and scale ~= "" and scale > 0) and scale or 1

        new_mode_data[i] = {}
        new_mode_data[i].display_data = display_data
        new_mode_data[i].transform_info = transform_info
    end

    return new_mode_data
end


--------------------------------------修改------------------------------------------
function TreasurePalaceData:UpdateZhenBaoDianEnterGameRewardInfo(protocol)
    self.enter_game_reward = protocol.enter_game_reward
end

function TreasurePalaceData:UpdateZhenBaoDianVipGiftInfo(protocol)
    self.vip_gift = protocol.vip_gift                   -- vip礼包获取次数
    self.server_gift_count = protocol.server_gift_count -- 全服礼包获取次数
end

function TreasurePalaceData:GetEnterRewardRemind()
    if not self:IsGetEnterReward() then
        return 1
    end

    return 0
end

function TreasurePalaceData:GetRebateRemind()
    return 0
end

function TreasurePalaceData:GetGiftRemind()
    local target_data_list = self:GetVipGiftCfg()
    local level = RoleWGData.Instance:GetRoleLevel()
    local vip_level = VipWGData.Instance:GetRoleVipLevel()

    if not IsEmptyTable(target_data_list) then
        for k, v in pairs(target_data_list) do
            local is_get_flag = self:IsGetVipGiftFlag(v.seq)
            if not is_get_flag then
                local can_change = v.server_count == 0 or (v.server_count > 0 and v.server_count > self:GetVipGiftServerGetCount(v.seq))
                if can_change then
                    if level >= v.level_limit and vip_level >= v.vip_level_limit then
                        return 1
                    end
                end
            end
        end
    end

    return 0
end

function TreasurePalaceData:GetOtherCfg()
    return self.other_cfg
end

function TreasurePalaceData:GetVipGiftCfg()
    return self.vip_gift_cfg
end

function TreasurePalaceData:IsGetEnterReward()
    return self.enter_game_reward == 1
end

function TreasurePalaceData:GetVipGiftServerGetCount(seq)
    return self.server_gift_count[seq] or 0
end

function TreasurePalaceData:IsGetVipGiftFlag(seq)
    return self.vip_gift[seq] == 1
end

function TreasurePalaceData:GetGiftDataList()
    local data_list = {}

    local target_data_list = self:GetVipGiftCfg()
    local level = RoleWGData.Instance:GetRoleLevel()
    local vip_level = VipWGData.Instance:GetRoleVipLevel()

    if not IsEmptyTable(target_data_list) then
        for k, v in pairs(target_data_list) do
            local is_get_flag = self:IsGetVipGiftFlag(v.seq)
            local can_change = not is_get_flag and (v.server_count == 0 or (v.server_count > 0 and v.server_count > self:GetVipGiftServerGetCount(v.seq)))
            local can_get = can_change and level >= v.level_limit and vip_level >= v.vip_level_limit
            local server_get_count = self:GetVipGiftServerGetCount(v.seq)
            local sort_index = can_get and 0 or can_change and 1 or is_get_flag and 3 or 2
            local sell_out = not is_get_flag and v.server_count > 0 and v.server_count <= server_get_count

            local data = {}
            data.cfg = v
            data.seq = v.seq
            data.is_get_flag = is_get_flag
            data.can_change = can_change
            data.sell_out = sell_out
            data.can_get = can_get
            data.server_get_count = server_get_count
            data.sort_index = sort_index
            table.insert(data_list, data)
        end

        table.sort(data_list, SortTools.KeyLowerSorter("sort_index", "seq"))
    end

    return data_list
end

function TreasurePalaceData:GetLifeTimeRewardDataListBySeq(seq)
    return self.reward_show_cfg[seq] or {}
end