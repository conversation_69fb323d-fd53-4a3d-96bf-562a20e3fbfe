{"actorController": {"projectiles": [], "hurts": [], "beHurtEffecct": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "hurtEffectName": "", "beHurtNodeName": "", "beHurtAttach": false, "hurtEffectFreeDelay": 0.0, "QualityCtrlList": []}, "actorTriggers": {"effects": [{"triggerEventName": "attack1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10115_skill1", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10115/10115_skill1_prefab", "AssetName": "10115_skill1", "AssetGUID": "ea8aa26417542f440ad91e293d288d17", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10115_skill2", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10115/10115_skill2_prefab", "AssetName": "10115_skill2", "AssetGUID": "dc6f3ca9537356049a173b04bd985ff3", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10115_skill3", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10115/10115_skill3_prefab", "AssetName": "10115_skill3", "AssetGUID": "b737b1f87f9a4384bb21bde9a81aadfb", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack3", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "rest/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10115_skill2", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10115/10115_skill2_prefab", "AssetName": "10115_skill2", "AssetGUID": "dc6f3ca9537356049a173b04bd985ff3", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "root", "isAttach": true, "isRotation": false, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "rest", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "combo1_1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10115_acttack", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10115/10115_acttack_prefab", "AssetName": "10115_acttack", "AssetGUID": "2b55a0daf5f21bb429daa1b44dc8e82b", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo", "playerAtPos": false, "ignoreParentScale": false}], "halts": [], "sounds": [{"soundEventName": "combo1_1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10115", "AssetName": "MingJiangcombo_1_1", "AssetGUID": "5938aa339da6fc440ac2d7894ddc5730", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_1", "soundBtnName": "combo1", "soundIsMainRole": false}, {"soundEventName": "combo1_2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10115", "AssetName": "MingJiangcombo_1_2", "AssetGUID": "6c007530ce4063145a7cba982de4095c", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_2", "soundBtnName": "combo2", "soundIsMainRole": false}, {"soundEventName": "combo1_3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10115", "AssetName": "MingJiangcombo_1_3", "AssetGUID": "bea834bb38f35004ca51f055c12a4bfc", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_3", "soundBtnName": "combo3", "soundIsMainRole": false}, {"soundEventName": "attack1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10115", "AssetName": "MingJiangattack1", "AssetGUID": "c1a4d8e8a2809fd41b8dd3c0b4165476", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack1", "soundBtnName": "attack1", "soundIsMainRole": false}, {"soundEventName": "attack2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10115", "AssetName": "MingJiangattack2", "AssetGUID": "3c79646c3ca86654589b26cb78460a55", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack2", "soundBtnName": "attack2", "soundIsMainRole": false}, {"soundEventName": "attack3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10115", "AssetName": "MingJiangattack3", "AssetGUID": "5e139e2945e1c7a4ba2bb39560487841", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack3", "soundBtnName": "attack3", "soundIsMainRole": false}], "cameraShakes": [], "cameraFOVs": [], "sceneFades": [], "footsteps": []}, "actorBlinker": {"blinkFadeIn": 0.0, "blinkFadeHold": 0.0, "blinkFadeOut": 0.0}, "TimeLineList": []}