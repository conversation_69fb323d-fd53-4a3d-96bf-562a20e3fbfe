PerfectLoverWGData = PerfectLoverWGData or BaseClass()

function PerfectLoverWGData:__init()
	if PerfectLoverWGData.Instance ~= nil then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[PerfectLoverWGData] attempt to create singleton twice!")
		return
	end
	PerfectLoverWGData.Instance = self
end

function PerfectLoverWGData:__delete()
	PerfectLoverWGData.Instance = nil
end

function PerfectLoverWGData:SetPerfectLoverInfo(protocol)
	self.perfect_lover_type_record_flag = protocol.perfect_lover_type_record_flag
	self.ra_perfect_lover_name_list = protocol.ra_perfect_lover_name_list
end

function PerfectLoverWGData:GetPerfectLoverInfo()
	return self.perfect_lover_type_record_flag
end
function PerfectLoverWGData:GetPerfectLoverNameList()
	return self.ra_perfect_lover_name_list
end