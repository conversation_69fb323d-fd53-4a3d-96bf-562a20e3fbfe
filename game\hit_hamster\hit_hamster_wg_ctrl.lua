require("game/hit_hamster/hit_hamster_wg_data")
require("game/hit_hamster/hit_hamster_view")
require("game/hit_hamster/hit_hamster_rank_view")
require("game/hit_hamster/hit_hamster_result_view")

HitHamsterWGCtrl = HitHamsterWGCtrl or BaseClass(BaseWGCtrl)

function HitHamsterWGCtrl:__init()
	if HitHamsterWGCtrl.Instance then
		error("[HitHamsterWGCtrl]:Attempt to create singleton twice!")
		return
	end
	HitHamsterWGCtrl.Instance = self

	self.data = HitHamsterWGData.New()
	self.view = HitHamsterView.New(GuideModuleName.HitHamsterView)
	self.hit_hamster_result_view = HitHamsterResultView.New()
	self.hit_hamster_rank_view = HitHamsterRankView.New()
	self:RegisterAllProtocals()
end

function HitHamsterWGCtrl:__delete()
	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.hit_hamster_rank_view then
		self.hit_hamster_rank_view:DeleteMe()
		self.hit_hamster_rank_view = nil
	end

	if self.hit_hamster_result_view then
		self.hit_hamster_result_view:DeleteMe()
		self.hit_hamster_result_view = nil
	end
	
	HitHamsterWGCtrl.Instance = nil
end

function HitHamsterWGCtrl:RegisterAllProtocals()

end

function HitHamsterWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(CSWhackMoleOperate)

	self:RegisterProtocol(SCWhackMoleItemInfo, "OnSCWhackMoleItemInfo")
	self:RegisterProtocol(SCWhackMoleItemUpdate, "OnSCWhackMoleItemUpdate")
	self:RegisterProtocol(SCCWhackMoleRankInfo, "OnSCCWhackMoleRankInfo")
	self:RegisterProtocol(SCWhackMoleBaseInfo, "OnSCWhackMoleBaseInfo")
end


---------------------------------------打地鼠--------------------------------------------
-- 打地鼠操作按钮
function HitHamsterWGCtrl:SendCSWhackMoleOperate(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSWhackMoleOperate)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

-- 打地鼠操作（开始游戏）
function HitHamsterWGCtrl:SendWhackMoleGameStart()
	self:SendCSWhackMoleOperate(WHACK_MOLE_OPERATE_TYPE.WHACK_MOLE_OPERATE_TYPE_START)
end

-- 打地鼠操作（结束游戏）
function HitHamsterWGCtrl:SendWhackMoleGameEnd()
	self:SendCSWhackMoleOperate(WHACK_MOLE_OPERATE_TYPE.WHACK_MOLE_OPERATE_TYPE_END)
end

-- 打地鼠操作（打地鼠）
function HitHamsterWGCtrl:SendWhackMoleGameTouch(index)
	self:SendCSWhackMoleOperate(WHACK_MOLE_OPERATE_TYPE.WHACK_MOLE_OPERATE_TYPE_TOUCH, index)
end

-- 打地鼠操作（排行榜）
function HitHamsterWGCtrl:SendWhackMoleGameRank()
	self:SendCSWhackMoleOperate(WHACK_MOLE_OPERATE_TYPE.WHACK_MOLE_OPERATE_TYPE_RANK_INFO)
end

-- 打地鼠操作（基础信息）
function HitHamsterWGCtrl:SendWhackMoleBaseInfo()
	self:SendCSWhackMoleOperate(WHACK_MOLE_OPERATE_TYPE.WHACK_MOLE_OPERATE_TYPE_BASE_INFO)
end

-- 打地鼠操作（领取参与奖励）
function HitHamsterWGCtrl:SendWhackMoleJoinReward()
	self:SendCSWhackMoleOperate(WHACK_MOLE_OPERATE_TYPE.WHACK_MOLE_OPERATE_TYPE_FETCH_JOIN_REWARD)
end
---------------------------------------------------------------------------------------

-- 打地鼠信息
function HitHamsterWGCtrl:OnSCWhackMoleItemInfo(protocol)
	-- print_error("打地鼠信息", protocol)
	self.data:SetWhackMoleItemInfo(protocol)

	if protocol.end_time > 0 then
		self:FlushView(0, "game_start", {is_experience = false})
	else
		self:FlushView(0, "game_end")
	end
	-- 刷新红点
	RemindManager.Instance:Fire(RemindName.HitHamster)
	self:SendWhackMoleGameRank()
end

-- 打地鼠信息更新
function HitHamsterWGCtrl:OnSCWhackMoleItemUpdate(protocol)
	-- print_error("打地鼠信息更新", protocol)
	self.data:UpdateMoleItemInfo(protocol)
	self:FlushView(0, "score")
	-- 刷新红点
	RemindManager.Instance:Fire(RemindName.HitHamster)
end

-- 打地鼠排行榜
function HitHamsterWGCtrl:OnSCCWhackMoleRankInfo(protocol)
	-- print_error("打地鼠排行榜", protocol)
	self.data:SetWhackMoleRankInfo(protocol)
	self:FlushView(0, "rank")
	self:FlushWhackMoleRankView()
	self:FlushGameResultView()
end

-- 打地鼠参与奖励信息
function HitHamsterWGCtrl:OnSCWhackMoleBaseInfo(protocol)
	self.data:SetWhackMoleBaseInfo(protocol)
	self:FlushView(0, "times_reward")
	self:FlushGameResultView()
end

---------------------------------------------------------------------------------------
-- 刷新界面
function HitHamsterWGCtrl:FlushView(index, key, param_t)
	if self.view:IsOpen() then
		self.view:Flush(index, key, param_t)
	end
end

function HitHamsterWGCtrl:OpenWhackMoleRankView()
	if not self.hit_hamster_rank_view:IsOpen() then
		self.hit_hamster_rank_view:Open()
	else
		self.hit_hamster_rank_view:Flush()
	end
end

function HitHamsterWGCtrl:FlushWhackMoleRankView()
	if self.hit_hamster_rank_view:IsOpen() then
		self.hit_hamster_rank_view:Flush()
	end
end

function HitHamsterWGCtrl:OpenGameResultView(show_result_data)
	self.hit_hamster_result_view:SetShowData(show_result_data)
	if not self.hit_hamster_result_view:IsOpen() then
		self.hit_hamster_result_view:Open()
	else
		self.hit_hamster_result_view:Flush()
	end
end

function HitHamsterWGCtrl:FlushGameResultView()
	if self.hit_hamster_result_view:IsOpen() then
		self.hit_hamster_result_view:Flush()
	end
end

function HitHamsterWGCtrl:CloseGameResultView()
	if self.hit_hamster_result_view:IsOpen() then
		self.hit_hamster_result_view:Close()
	end
end