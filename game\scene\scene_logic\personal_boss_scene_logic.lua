PersonalBossSceneLogic = PersonalBossSceneLogic or BaseClass(CommonFbLogic)

function PersonalBossSceneLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
end

function PersonalBossSceneLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
end

function PersonalBossSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
    -- MainuiWGCtrl.Instance:SetTaskActive(false)
    -- MainuiWGCtrl.Instance:SetFubenTaskList()
    MainuiWGCtrl.Instance:SetTaskContents(false)
    MainuiWGCtrl.Instance:SetOtherContents(false)
	BossWGCtrl.Instance:Close()
	GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,true)
	-- local cfg = BossWGData.Instance:GetPersonBossCfg(Scene.Instance:GetSceneId())
	-- local other_cfg = ConfigManager.Instance:GetAutoConfig("personboss_auto").other[1]
	-- FuBenPanelWGCtrl.Instance:OpenStarAniView({time3 = cfg.star_time_3, time2 = cfg.star_time_2, time1 = cfg.star_time_1,
	-- 	per1 = other_cfg.star1, per2 = other_cfg.star2, per3 = other_cfg.star3, str = Language.Boss.StarAniStr,})

	MainuiWGCtrl.Instance:SetFBNameState(true, Scene.Instance:GetSceneName())
	MainuiWGCtrl.Instance:SetTeamBtnState(false)

	BaseFbLogic.SetLeaveFbTip(true)
	self.fight_state = GlobalEventSystem:Bind(ObjectEventType.ENTER_FIGHT, BindTool.Bind(self.OnEnterFight, self))
end

function PersonalBossSceneLogic:Out(old_scene_type, new_scene_type)
    CommonFbLogic.Out(self)
    MainuiWGCtrl.Instance:SetTaskContents(true)
    MainuiWGCtrl.Instance:SetOtherContents(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	-- UiInstanceMgr.Instance:CloseSceneCountDown()
	-- MainuiWGCtrl.Instance:SetTaskActive(true)
	GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,false)
	FuBenPanelWGCtrl.Instance:CloseStarAniView()

	if BossWGData.Instance:GetIsEnterInScene() and BossWGData.Instance:CheckPerOutSceneOpenViewLevel() then
		BossWGCtrl.Instance:OpenBossViewByScene(old_scene_type, new_scene_type)
	end
	if not BossWGData.Instance:GetKillBossSucc() and not FuBenWGData.Instance:GetFuBenInitiativeToLeave(SceneType.PERSON_BOSS) then
		FuBenWGCtrl.Instance:OpenFuBenLoseView()
	end
    
	if self.fight_state then
		GlobalEventSystem:UnBind(self.fight_state)
        self.fight_state = nil
	end
	self:CancelFightQuest()
end

function PersonalBossSceneLogic:OpenFbSceneCd()

end


function PersonalBossSceneLogic:GetGuajiPos()
	local cfg = BossWGData.Instance:GetPersonBossCfg(Scene.Instance:GetSceneId())
	if cfg and cfg.enter_pos_x and cfg.enter_pos_y then
		return cfg.enter_pos_x, cfg.enter_pos_y
	end
    return 163, 796
end

function PersonalBossSceneLogic:GetFbSceneMonsterListCfg(monsters_list_cfg)
	local monsters_list = MapWGData.Instance:GetSceneMonsterSort(monsters_list_cfg)
	return BossWGData.Instance:GetCurSceneAllMonster(monsters_list), true
end

function PersonalBossSceneLogic:GetFbSceneMonsterBossCfg()
	return BossWGData.Instance:GetCurSceneAllBoss()
end

function PersonalBossSceneLogic:OnEnterFight()
	-- local obj_id = GuajiCache.target_obj_id
	-- local obj = Scene.Instance:GetObj(obj_id)
	-- local vo = obj and obj:GetVo()

	-- if vo then
	-- 	local boss_cfg = BossWGData.Instance:GetPerBossCfgInAllInfo(vo.monster_id)
	-- 	if not boss_cfg then
	-- 		return
	-- 	end

	-- 	local monster_cfg = BossWGData.Instance:GetMonsterCfgByid(boss_cfg.boss_id)
	-- 	if monster_cfg == nil then
	-- 		return
	-- 	end

		-- local role_level = RoleWGData.Instance:GetRoleLevel()
		-- if role_level - boss_cfg.max_delta_level >= monster_cfg.level and not self.fight_check then
		-- 	self.fight_check = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.UpdateCheckFight, self), 1)
		-- end
	-- end
end

function PersonalBossSceneLogic:UpdateCheckFight()
	local main_role = Scene.Instance:GetMainRole()
	if main_role and main_role:IsAtkPlaying() then
		--BossWGCtrl.Instance:ShowLevelLimitTip(Status.NowTime)
	else
		self:CancelFightQuest()
	end
end

function PersonalBossSceneLogic:CancelFightQuest()
	if self.fight_check then
		GlobalTimerQuest:CancelQuest(self.fight_check)
        self.fight_check = nil
	end
end