EquipmentLingYuOverview = EquipmentLingYuOverview or BaseClass(SafeBaseView)

function EquipmentLingYuOverview:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 14),sizeDelta = Vector2(1080, 614)})
	self:AddViewResource(0, "uis/view/equipment_ui_prefab", "layout_equipment_lingyu_overview")
end

function EquipmentLingYuOverview:LoadCallBack()
    if not self.equipment_lingyu_equip_body_list then
        self.equipment_lingyu_equip_body_list = AsyncListView.New(LingYuEquipBodyOverviewItemCellRender, self.node_list.equipment_lingyu_equip_body_list)
    end

    self.node_list.title_view_name.text.text = Language.EquipLingYu.LingYuOverviewTitle
end

function EquipmentLingYuOverview:ReleaseCallBack()
    if self.equipment_lingyu_equip_body_list then
        self.equipment_lingyu_equip_body_list:DeleteMe()
        self.equipment_lingyu_equip_body_list = nil
    end
end

function EquipmentLingYuOverview:OnFlush()
    local data_list = EquipBodyWGData.Instance:GetDZXJXQEquipBodyDataList()
    self.equipment_lingyu_equip_body_list:SetDataList(data_list)
end

--------------------------------------LingYuEquipBodyOverviewItemCellRender---------------------------------------
LingYuEquipBodyOverviewItemCellRender = LingYuEquipBodyOverviewItemCellRender or BaseClass(BaseRender)

function LingYuEquipBodyOverviewItemCellRender:LoadCallBack()
    self.default_select_part = -1
    self.equip_body_seq_cache = -1
    self.default_select_part_data = {}

    if not self.lingyu_equip_list then
        self.lingyu_equip_list = AsyncListView.New(LingYuOverviewEquipItemCellRender, self.node_list.lingyu_equip_list)
        self.lingyu_equip_list:SetSelectCallBack(BindTool.Bind(self.OnSelectEquipHandler, self))
    end

    XUI.AddClickEventListener(self.node_list["btn_to_equip_suit"], BindTool.Bind(self.OnClickToEquipSuitBtn, self))
    XUI.AddClickEventListener(self.node_list["btn_bsly"], BindTool.Bind(self.OnClickBSLYBtn, self))
end

function LingYuEquipBodyOverviewItemCellRender:__delete()
    if self.lingyu_equip_list then
        self.lingyu_equip_list:DeleteMe()
        self.lingyu_equip_list = nil
    end
end

function LingYuEquipBodyOverviewItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    if self.equip_body_seq_cache ~= self.data.seq then
        self.default_select_part = -1
        self.default_select_part_data = {}
    end

    self.equip_body_seq_cache = self.data.seq

    local baoshi_total_level = EquipmentLingYuWGData.Instance:GetTotalLingYuLevel(self.data.seq)
    self.node_list.desc_btn_bsly.text.text = string.format(Language.EquipLingYu.LingYuOverviewLevel, baoshi_total_level)

    local unlock = EquipBodyWGData.Instance:IsEquipBodyUnLock(self.data.seq)
    local is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyWearEquip(self.data.seq)
    self.node_list.unlock:CustomSetActive(not unlock or not is_wear_equip)
    self.node_list.lingyu_equip_list:CustomSetActive(unlock and is_wear_equip)
    self.node_list.name.text.text = self.data.name
    self.node_list.desc_unlock.text.text = not unlock and Language.RoleEquipBody.EquipBodyLock or Language.RoleEquipBody.EquipBodyNotWearEquip
    self.node_list.btn_bsly:CustomSetActive(unlock and is_wear_equip)

    if unlock then
        local lingyu_equip_list = EquipmentLingYuWGData.Instance:GetEquipLingYuShowList(self.data.seq)
        self.lingyu_equip_list:SetDataList(lingyu_equip_list)
    end
end

function LingYuEquipBodyOverviewItemCellRender:OnSelectEquipHandler(cell)
    if nil == cell or IsEmptyTable(cell.data) then
        return
    end
    
    self.default_select_part_data = cell.data
end

function LingYuEquipBodyOverviewItemCellRender:OnClickToEquipSuitBtn()
    local unlock = EquipBodyWGData.Instance:IsEquipBodyUnLock(self.data.seq)
    local is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyWearEquip(self.data.seq)

    if not unlock or not is_wear_equip then
        local str = not unlock and Language.RoleEquipBody.EquipBodyLock or Language.RoleEquipBody.EquipBodyNotWearEquip
        SysMsgWGCtrl.Instance:ErrorRemind(str)
        return
    end

    if unlock and is_wear_equip then
        EquipmentWGCtrl.Instance:Flush(TabIndex.equipment_lingyu, "equip_lingyu_change_to_equip_body", {equip_body_seq = self.data.seq, selct_part_data = self.default_select_part_data})
    end

    EquipmentWGCtrl.Instance:CloseLingYuOverviewView()
end

function LingYuEquipBodyOverviewItemCellRender:OnClickBSLYBtn()
    RoleWGCtrl.Instance:SetEquipTextData(ROLE_EQUIP_ATTR_TIPS.LINGYU_TIP, self.data.seq)
	RoleWGCtrl.Instance:OpenEquipAttr()
end

------------------------------------LingYuOverviewEquipItemCellRender------------------------------------
LingYuOverviewEquipItemCellRender = LingYuOverviewEquipItemCellRender or BaseClass(BaseRender)

function LingYuOverviewEquipItemCellRender:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["ph_item"])
        self.item_cell:SetIsShowTips(false)
    end

    self.old_item_id = 0
end

function LingYuOverviewEquipItemCellRender:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end

    self.old_item_id = nil
end

function LingYuOverviewEquipItemCellRender:OnFlush()
	if nil == self.data or nil == self.data.param then
		return
	end

    if self.item_cell and self.data.item_id ~= self.old_item_id then
		self.item_cell:SetData(self.data)
        self.item_cell:SetRightTopImageTextActive(false)
	end

	self.old_item_id = self.data.item_id

    -- local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	-- if self.node_list["lbl_name"] and item_cfg then
	-- 	local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
	-- 	self.node_list["lbl_name"].text.text = item_name
	-- end

    local lingyu_info = EquipmentLingYuWGData.Instance:GetLingYuInfoListByIndex(self.data.index)
	if nil == lingyu_info then
		return
	end

	local show_lingyu_list = {}
	for i= 0, GameEnum.MAX_LINGYU_COUNT - 1 do
		local is_open = EquipmentLingYuWGData.Instance:LingYuSlotIsOpen(self.data.index, i)
		if is_open then
			table.insert(show_lingyu_list, lingyu_info[i])
		end
    end

	if not IsEmptyTable(show_lingyu_list) then
		table.sort(show_lingyu_list, SortTools.KeyUpperSorter("item_id"))
	end

    local total_level = 0

    for i = 0, GameEnum.MAX_LINGYU_COUNT - 1 do
		if show_lingyu_list[i + 1] then
			local data = show_lingyu_list[i + 1]
			local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
			local bundle, asset = "", ""
			if item_cfg then
				local lingyu_cfg = EquipmentLingYuWGData.Instance:GetLingYuCfgByItemId(data.item_id)
				local lingyu_type = lingyu_cfg.lingyu_type
				bundle, asset = ResPath.GetEquipmentIcon("a2_ls_" .. (lingyu_type or 1))
				self.node_list["icon_" .. i].image:LoadSprite(bundle, asset, function()
					self.node_list["icon_" .. i].image:SetNativeSize()
				end)
				self.node_list["icon_" .. i]:SetActive(true)

                total_level = total_level + lingyu_cfg.level
			else
				-- bundle, asset = ResPath.GetF2CommonIcon("i_gem_bg")
				-- self.node_list["icon_" .. i].image:LoadSprite(bundle, asset, function()
				-- 	self.node_list["icon_" .. i].image:SetNativeSize()
				-- end)
				self.node_list["icon_" .. i]:SetActive(false)
			end

			self.node_list["Img_" .. i]:SetActive(true)
			self.node_list["img_suo_" .. i]:SetActive(false)
		else
			self.node_list["Img_" .. i]:SetActive(false)
			self.node_list["img_suo_" .. i]:SetActive(true)
		end
	end

    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if self.node_list["lbl_name"] and item_cfg then
		local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
        local item_name_str = item_name .. (total_level > 0 and string.format(Language.EquipLingYu.LingYuOverviewCellName, total_level) or "")

		self.node_list["lbl_name"].text.text = item_name_str
        self.node_list["hl_lbl_name"].text.text = item_name_str
	end
end

function LingYuOverviewEquipItemCellRender:OnSelectChange(is_select)
    self.node_list["img9_item_bg_hl"]:SetActive(is_select)
    self.node_list["img9_item_bg"]:SetActive(not is_select)
end