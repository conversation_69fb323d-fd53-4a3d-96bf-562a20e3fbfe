﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_UIVariableWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(Nirvana.UIVariable), typeof(System.Object));
		<PERSON><PERSON>RegFunction("GetBoolean", GetBoolean);
		<PERSON><PERSON>unction("GetInteger", GetInteger);
		<PERSON><PERSON>Function("GetFloat", GetFloat);
		L.RegFunction("GetString", GetString);
		<PERSON><PERSON>RegFunction("GetAsset", GetAsset);
		<PERSON><PERSON>RegFunction("InitBoolean", InitBoolean);
		<PERSON><PERSON>RegFunction("InitInteger", InitInteger);
		<PERSON><PERSON>RegFunction("InitFloat", InitFloat);
		<PERSON><PERSON>unction("InitString", InitString);
		<PERSON><PERSON>RegFunction("SetBoolean", SetBoolean);
		<PERSON><PERSON>RegFunction("SetInteger", SetInteger);
		<PERSON><PERSON>unction("SetFloat", SetFloat);
		<PERSON><PERSON>unction("SetString", SetString);
		<PERSON><PERSON>unction("InitValue", InitValue);
		<PERSON><PERSON>unction("SetValue", SetValue);
		<PERSON>.RegFunction("SetAsset", SetAsset);
		L.RegFunction("ResetAsset", ResetAsset);
		L.RegFunction("New", _CreateNirvana_UIVariable);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("Name", get_Name, null);
		L.RegVar("Type", get_Type, null);
		L.RegVar("ValueObject", get_ValueObject, null);
		L.RegVar("OnValueChanged", get_OnValueChanged, set_OnValueChanged);
		L.RegVar("OnValueInitialized", get_OnValueInitialized, set_OnValueInitialized);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateNirvana_UIVariable(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				Nirvana.UIVariable obj = new Nirvana.UIVariable();
				ToLua.PushSealed(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: Nirvana.UIVariable.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetBoolean(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
			bool o = obj.GetBoolean();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetInteger(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
			int o = obj.GetInteger();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetFloat(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
			float o = obj.GetFloat();
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetString(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
			string o = obj.GetString();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetAsset(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
			Nirvana.AssetID o = obj.GetAsset();
			ToLua.PushValue(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int InitBoolean(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.InitBoolean(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int InitInteger(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
			long arg0 = LuaDLL.tolua_checkint64(L, 2);
			obj.InitInteger(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int InitFloat(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.InitFloat(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int InitString(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
			string arg0 = ToLua.CheckString(L, 2);
			obj.InitString(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetBoolean(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetBoolean(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetInteger(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
			long arg0 = LuaDLL.tolua_checkint64(L, 2);
			obj.SetInteger(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetFloat(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.SetFloat(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetString(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
			string arg0 = ToLua.CheckString(L, 2);
			obj.SetString(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int InitValue(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2 && TypeChecker.CheckTypes<bool>(L, 2))
			{
				Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
				bool arg0 = LuaDLL.lua_toboolean(L, 2);
				obj.InitValue(arg0);
				return 0;
			}
			else if (count == 2 && TypeChecker.CheckTypes<float>(L, 2))
			{
				Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
				float arg0 = (float)LuaDLL.lua_tonumber(L, 2);
				obj.InitValue(arg0);
				return 0;
			}
			else if (count == 2 && TypeChecker.CheckTypes<long>(L, 2))
			{
				Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
				long arg0 = LuaDLL.tolua_toint64(L, 2);
				obj.InitValue(arg0);
				return 0;
			}
			else if (count == 2 && TypeChecker.CheckTypes<string>(L, 2))
			{
				Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
				string arg0 = ToLua.ToString(L, 2);
				obj.InitValue(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Nirvana.UIVariable.InitValue");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetValue(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2 && TypeChecker.CheckTypes<bool>(L, 2))
			{
				Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
				bool arg0 = LuaDLL.lua_toboolean(L, 2);
				obj.SetValue(arg0);
				return 0;
			}
			else if (count == 2 && TypeChecker.CheckTypes<float>(L, 2))
			{
				Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
				float arg0 = (float)LuaDLL.lua_tonumber(L, 2);
				obj.SetValue(arg0);
				return 0;
			}
			else if (count == 2 && TypeChecker.CheckTypes<long>(L, 2))
			{
				Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
				long arg0 = LuaDLL.tolua_toint64(L, 2);
				obj.SetValue(arg0);
				return 0;
			}
			else if (count == 2 && TypeChecker.CheckTypes<string>(L, 2))
			{
				Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
				string arg0 = ToLua.ToString(L, 2);
				obj.SetValue(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Nirvana.UIVariable.SetValue");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetAsset(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
				Nirvana.AssetID arg0 = StackTraits<Nirvana.AssetID>.Check(L, 2);
				obj.SetAsset(arg0);
				return 0;
			}
			else if (count == 3)
			{
				Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
				string arg0 = ToLua.CheckString(L, 2);
				string arg1 = ToLua.CheckString(L, 3);
				obj.SetAsset(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Nirvana.UIVariable.SetAsset");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResetAsset(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
			obj.ResetAsset();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Name(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UIVariable obj = (Nirvana.UIVariable)o;
			string ret = obj.Name;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Name on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Type(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UIVariable obj = (Nirvana.UIVariable)o;
			Nirvana.UIVariableType ret = obj.Type;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Type on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ValueObject(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UIVariable obj = (Nirvana.UIVariable)o;
			object ret = obj.ValueObject;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ValueObject on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OnValueChanged(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(System.Action)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OnValueInitialized(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(System.Action)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_OnValueChanged(IntPtr L)
	{
		try
		{
			Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Nirvana.UIVariable.OnValueChanged' can only appear on the left hand side of += or -= when used outside of the type 'Nirvana.UIVariable'");
			}

			if (arg0.op == EventOp.Add)
			{
				System.Action ev = (System.Action)arg0.func;
				obj.OnValueChanged += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				System.Action ev = (System.Action)arg0.func;
				obj.OnValueChanged -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_OnValueInitialized(IntPtr L)
	{
		try
		{
			Nirvana.UIVariable obj = (Nirvana.UIVariable)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariable));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Nirvana.UIVariable.OnValueInitialized' can only appear on the left hand side of += or -= when used outside of the type 'Nirvana.UIVariable'");
			}

			if (arg0.op == EventOp.Add)
			{
				System.Action ev = (System.Action)arg0.func;
				obj.OnValueInitialized += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				System.Action ev = (System.Action)arg0.func;
				obj.OnValueInitialized -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

