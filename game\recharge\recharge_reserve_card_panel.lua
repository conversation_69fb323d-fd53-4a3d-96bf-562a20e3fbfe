local SLIDER_POS = {[0] = 0, [1] = 0.2, [2] = 0.4, [3] = 0.6, [4] = 0.8, [5] = 1}

-- 攒福特权
function RechargeView:DeleteReserveCardPanel()--ReleseCallBack
    if self.zf_ph_baoxiang_list then
		for k, v in pairs(self.zf_ph_baoxiang_list) do
            v:DeleteMe()
        end
        self.zf_ph_baoxiang_list = nil
	end

    if self.zf_online_list then
		for k, v in pairs(self.zf_online_list) do
            v:DeleteMe()
        end
        self.zf_online_list = nil
	end
end

function RechargeView:InitReserveCard()--LoadCallBack
    if self.zf_ph_baoxiang_list == nil then
        self.zf_ph_baoxiang_list = {}
        for i = 1, RechargeWGData.MAX_RESERVE_REWARD_NUM do
            self.zf_ph_baoxiang_list[i] = ReserveRebateCell.New(self.node_list.zf_rebate_list:FindObj("rebate_cell_" .. i))
            self.zf_ph_baoxiang_list[i]:SetIndex(i)
        end
    end

    if self.zf_online_list == nil then
        self.zf_online_list = {}
        for i = 1, RechargeWGData.MAX_RESERVE_ONLINE_NUM do
            self.zf_online_list[i] = ReserveOnlineCell.New(self.node_list.zf_online_reward_list:FindObj("zf_online_reward_" .. i))
            self.zf_online_list[i]:SetIndex(i)
        end
    end

    XUI.AddClickEventListener(self.node_list["zf_buy_btn"], BindTool.Bind(self.OnClickBuyReserve, self))
    XUI.AddClickEventListener(self.node_list["zf_get_today_linyu"], BindTool.Bind(self.OnClickGetLingYu, self))
    XUI.AddClickEventListener(self.node_list["go_zf_preview"], BindTool.Bind(self.OnClickPreview, self))

    self:FlushPreviewRemind()
end

function RechargeView:ShowReserveCardIndexCallBack()

end

function RechargeView:FlushReserveCardPanel()
    self:FlushPrivilegePanel()
    self:FlushOnlineInfo()
    self:FlushBtnInfo()
    self:FlushSlider()
    self:FlushRightInfo()
end

function RechargeView:FlushPrivilegePanel()
    local back_cfg = RechargeWGData.Instance:GetZFBackCfg(true)
    if IsEmptyTable(back_cfg) then
        return
    end

    local is_buy = RechargeWGData.Instance:IsBuyReserveCard()
    local is_buy_all = RechargeWGData.Instance:IsBuyAllReserveCard()
    self.node_list.zf_privilege_bg:SetActive(not is_buy_all)
    self.node_list.zf_get_today_linyu:SetActive(is_buy)
    self.node_list.zf_next_linyu:SetActive(is_buy)

    local cur_level = RechargeWGData.Instance:GetZFCurLevel()

    local bundle_1, asset_1 = ResPath.GetF2RawImagesPNG("a3_vip_zftq_jz" .. (is_buy and cur_level or 1))
    self.node_list.zf_type_bg.raw_image:LoadSprite(bundle_1, asset_1, function()
        self.node_list.zf_type_bg.raw_image:SetNativeSize()
    end)

    self.node_list.zf_type_img:SetActive(is_buy)

    if is_buy then
        self.node_list.zf_type_img.image:LoadSprite(ResPath.GetVipImage("a3_vip_zftq_bq" .. cur_level))

        local cur_cfg = RechargeWGData.Instance:GetZFBackCfg()
        if not IsEmptyTable(cur_cfg) then
            self.node_list.zf_type_text.text.text = cur_cfg.quality
        end
    end

    local all_back_cfg = RechargeWGData.Instance:GetZFAllBackCfg()
    local rate_str = string.format(Language.Recharge.RateText, 0)
    if not IsEmptyTable(all_back_cfg) then
        for k, v in ipairs(all_back_cfg)do
            if k == cur_level then
                rate_str = string.format(Language.Recharge.RateText, v.return_rate)
                break
            end
        end
    end
    self.node_list["zf_rate_num"].text.text = rate_str

    local cur_back_cfg = RechargeWGData.Instance:GetZFBackCfg()
    if IsEmptyTable(cur_back_cfg)  then
        cur_back_cfg = RechargeWGData.Instance:GetZFBackCfg(true)
    end

    local cur_back_multiple = back_cfg.back_multiple .. "%"
    local today_back_num = RechargeWGData.Instance:GetZFTodayBackNum()
    local cur_magnification = cur_back_cfg.back_multiple / 100
    local cur_level_num = math.ceil(today_back_num * cur_magnification)

    self.node_list.zf_cur_text.text.text = string.format(Language.Recharge.PrivilegeDescribe[is_buy and 2 or 1], cur_back_multiple, back_cfg.quality)

    self.node_list.zf_next_lingyu_text.text.text = string.format(Language.Recharge.ToDayPrivilegeDescribe, today_back_num)

    local mail_back_num = RechargeWGData.Instance:GetZFMailBackNum()
    if not IsEmptyTable(cur_back_cfg) then
        self.node_list.zf_get_lingyu_remind:SetActive(mail_back_num > 0)

        if mail_back_num > 0 then
            self.node_list.zf_today_lingyu.text.text = string.format(Language.Recharge.MailBackNum, math.ceil(mail_back_num * cur_magnification))
        else
            self.node_list.zf_today_lingyu.text.text = string.format(Language.Recharge.TodayBackNum, cur_level_num)
        end
    end
end

function RechargeView:FlushOnlineInfo()
    local online_cfg = RechargeWGData.Instance:GetZFOnlineRewardShowList()
    if IsEmptyTable(online_cfg) then
        return
    end

    for k, v in ipairs(self.zf_online_list) do
        v:SetData(online_cfg[k])
    end
end

function RechargeView:FlushBtnInfo()
    local is_buy_all = RechargeWGData.Instance:IsBuyAllReserveCard()
    self.node_list.zf_buy_btn_bg:SetActive(not is_buy_all)
    if not is_buy_all then
        local need_level = RechargeWGData.Instance:GetZFCurBuyCfg()
        if IsEmptyTable(need_level) then
            return
        end

        local price = RoleWGData.GetPayMoneyStr(need_level.rmb, need_level.rmb_type, need_level.rmb_seq)
        -- local original_price = RoleWGData.GetPayMoneyStr(need_level.original_price, need_level.rmb_type, need_level.rmb_seq)
        self.node_list.zf_buy_text.text.text = string.format(Language.Recharge.BuyPrice, price)
        -- self.node_list.zf_original_price.text.text = string.format(Language.Recharge.OriginalPrice, original_price)
        self.node_list.zf_original_price.text.text = string.format(Language.Recharge.OriginalPrice, string.format(Language.Common.MoneyTypes[3] , need_level.original_price))
    end
end

function RechargeView:FlushSlider()
    local progress = RechargeWGData.Instance:GetZFCurBXProgress()
    local slider_value = SLIDER_POS[progress]
    self.node_list.zf_rebate_slider.slider.value = slider_value
end

function RechargeView:FlushRightInfo()
    local final_list = RechargeWGData.Instance:GetZFFinalRewardShowList()
    if IsEmptyTable(final_list) then
        return
    end

    for k, v in ipairs(self.zf_ph_baoxiang_list) do
        v:SetData(final_list[k])
    end

    local all_lift_num = RechargeWGData.Instance:GetZFAllBackNum()
    self.node_list.zf_accumulate_num.text.text = all_lift_num
end

function RechargeView:OnClickBuyReserve()
	local need_level = RechargeWGData.Instance:GetZFCurBuyCfg()
	if IsEmptyTable(need_level) then
		return
	end

    RechargeWGCtrl.Instance:Recharge(need_level.rmb, need_level.rmb_type, need_level.rmb_seq)
end

function RechargeView:OnClickGetLingYu()
    local mail_back_num = RechargeWGData.Instance:GetZFMailBackNum()
    local today_back_num = RechargeWGData.Instance:GetZFTodayBackNum()

    if mail_back_num > 0 then
        RechargeWGCtrl.Instance:CSZanfutequanClientReq(ZANFUTEQUAN_OPERA_TYPE.BACK_REWARD)
    elseif today_back_num >= 0 and mail_back_num <= 0 then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Recharge.TomorrowCanGet)
    end
end

function RechargeView:OnClickPreview()
    RechargeWGData.Instance:SetPreviewRemind()
    RemindManager.Instance:Fire(RemindName.Reserve_Zftq)
	self:FlushPreviewRemind()
    RechargeWGCtrl.Instance:OpenPreviewView()
end

function RechargeView:FlushPreviewRemind()
    if nil == self.node_list["zf_preview_remind"] then
		return
	end

	self.node_list["zf_preview_remind"]:SetActive(RechargeWGData.Instance:IsShowPreviewRemind())
end


ReserveRebateCell = ReserveRebateCell or BaseClass(BaseRender)
function ReserveRebateCell:LoadCallBack()
    self.node_list["zf_btn_lingqu"].button:AddClickListener(BindTool.Bind(self.OnClickGetReward, self))
end

function ReserveRebateCell:OnFlush()
    if self.data == nil then
        return
    end

    local is_get = RechargeWGData.Instance:GetZFFinalRewardFlag(self.data.seq)
    local all_lift_num = RechargeWGData.Instance:GetZFAllBackNum()
    local need_num = self.data.back_num

    self.node_list.need_num.text.text = need_num
    self.node_list.rebate_remind:SetActive(false)
    if all_lift_num >= need_num then
        self.node_list.rebate_remind:SetActive(is_get == 0)
    end
end

function ReserveRebateCell:OnClickGetReward()
    if self.data == nil then
        return
    end

    local is_buy = RechargeWGData.Instance:IsBuyReserveCard()
    if not is_buy then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Recharge.NoBuy)
        local data_list =
		{
			view_type = RewardShowViewType.Normal,
			reward_item_list = self.data.reward_item
		}
		RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
        return
    end

    local is_get = RechargeWGData.Instance:GetZFFinalRewardFlag(self.data.seq)
    local all_lift_num = RechargeWGData.Instance:GetZFAllBackNum()
    local need_num = self.data.back_num

    if is_get == 1 then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Recharge.IsGetReward)
        return
    end

    if all_lift_num >= need_num then
        RechargeWGCtrl.Instance:CSZanfutequanClientReq(ZANFUTEQUAN_OPERA_TYPE.FINAL_REWARD, self.data.seq)
    else
        local data_list =
		{
			view_type = RewardShowViewType.Normal,
			reward_item_list = self.data.reward_item
		}
		RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
    end
end


ReserveOnlineCell = ReserveOnlineCell or BaseClass(BaseRender)
function ReserveOnlineCell:ReleaseCallBack()
    if self.zf_refresh_event and CountDown.Instance:HasCountDown(self.zf_refresh_event) then
		CountDown.Instance:RemoveCountDown(self.zf_refresh_event)
        self.zf_refresh_event = nil
	end

    if self.reward_item then
        self.reward_item:DeleteMe()
        self.reward_item = nil
    end
end

function ReserveOnlineCell:LoadCallBack()
    if not self.reward_item then
        self.reward_item = ItemCell.New(self.node_list["zf_item_pos"])
        self.reward_item:SetIsUseRoundQualityBg(true)
        self.reward_item:SetEffectRootEnable(false)
        self.reward_item:SetCellBgEnabled(false)
    end


    self.node_list["zf_get_online_reward"].button:AddClickListener(BindTool.Bind(self.OnClickGetReward, self))
end

function ReserveOnlineCell:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local reward_item = self.data.cfg.reward_item
    local item_cfg = ItemWGData.Instance:GetItemConfig(reward_item[0].item_id)
    if not IsEmptyTable(item_cfg) then
        self.reward_item:SetData({item_id = reward_item[0].item_id})
    end

    local is_buy = RechargeWGData.Instance:IsBuyReserveCard()

    if not is_buy then
        local online_bundle, online_asset = ResPath.GetVipImage("a3_vip_zftq_di5")
        self.node_list.zf_online_bg.image:LoadSprite(online_bundle, online_asset, function()
            self.node_list.zf_online_bg.image:SetNativeSize()
        end)
        self.node_list.zf_time_bg:SetActive(false)
        return
    end

    if self.zf_refresh_event and CountDown.Instance:HasCountDown(self.zf_refresh_event) then
		CountDown.Instance:RemoveCountDown(self.zf_refresh_event)
        self.zf_refresh_event = nil
	end

    self.node_list.zf_online_remind:SetActive(false)
    self.node_list.zf_time_bg:SetActive(self.data.status == REWARD_STATE_TYPE.READY)
    self.node_list.zf_mask:SetActive(self.data.status == REWARD_STATE_TYPE.FINISH)

    local type_bg_res = "a3_vip_zftq_di5"

    if self.data.status == REWARD_STATE_TYPE.UNDONE then
        self.node_list.zf_activity_time.text.text = Language.Recharge.Inpreparation
    elseif self.data.status == REWARD_STATE_TYPE.CAN_FETCH then
        self.node_list.zf_activity_time.text.text = Language.Recharge.CanGetReward
        self.node_list.zf_online_remind:SetActive(true)
        type_bg_res = "a3_vip_zftq_di4"
    elseif self.data.status == REWARD_STATE_TYPE.FINISH then
        self.node_list.zf_online_remind:SetActive(false)
        self.node_list.zf_activity_time.text.text = Language.Recharge.GetRewardOver
    elseif self.data.status == REWARD_STATE_TYPE.READY then
        self:FlushTimeCount()
    end

    local online_bundle, online_asset = ResPath.GetVipImage(type_bg_res)
    self.node_list.zf_online_bg.image:LoadSprite(online_bundle, online_asset, function()
        self.node_list.zf_online_bg.image:SetNativeSize()
    end)

    self.node_list.zf_online_bg_2.image:LoadSprite(ResPath.GetVipImage(type_bg_res .. "_2"))
end

function ReserveOnlineCell:FlushTimeCount()
    if self.zf_refresh_event and CountDown.Instance:HasCountDown(self.zf_refresh_event) then
		CountDown.Instance:RemoveCountDown(self.zf_refresh_event)
        self.zf_refresh_event = nil
	end

    self.zf_refresh_event = CountDown.Instance:AddCountDown(self.data.ready_next_time, 1,
    BindTool.Bind(self.ShowTimeText, self))

end

function ReserveOnlineCell:ShowTimeText(elapse_time, total_time)
    local time = math.floor(total_time - elapse_time)
    if time > 0 then
        local time_str = TimeUtil.FormatSecondDHM9(time)
        self.node_list["zf_activity_time"].text.text = string.format(Language.Recharge.TimeStr, time_str)
    else
        self.node_list.zf_activity_time.text.text = ""
    end
end

function ReserveOnlineCell:OnClickGetReward()
    if IsEmptyTable(self.data) then
        return
    end

    local is_buy = RechargeWGData.Instance:IsBuyReserveCard()
    if not is_buy then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Recharge.NoBuy)

		local data_list =
		{
			view_type = RewardShowViewType.Normal,
			reward_item_list = self.data.cfg.reward_item
		}
		RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
        return
    end

    if self.data.status == REWARD_STATE_TYPE.FINISH then
        self.node_list.zf_activity_time.text.text = Language.Recharge.GetRewardOver
        TipWGCtrl.Instance:ShowSystemMsg(Language.Recharge.IsGetReward)
        return
    end

    if self.data.status == REWARD_STATE_TYPE.CAN_FETCH then
        RechargeWGCtrl.Instance:CSZanfutequanClientReq(ZANFUTEQUAN_OPERA_TYPE.ONLINE_REWARD, self.data.cfg.seq)
    else
        local data_list =
		{
			view_type = RewardShowViewType.Normal,
			reward_item_list = self.data.cfg.reward_item
		}
		RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
    end
end