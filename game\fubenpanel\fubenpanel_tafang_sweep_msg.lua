TaFangSweepMsgView = TaFangSweepMsgView or BaseClass(SafeBaseView)

function TaFangSweepMsgView:__init()
	self.is_modal = true
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_sweep_msg")
	self.item_data_change_callback = BindTool.Bind1(self.ShowIndexCallBack, self)
end

function TaFangSweepMsgView:__delete()

end

function TaFangSweepMsgView:ReleaseCallBack()
	if self.saodang_cell then
		self.saodang_cell:DeleteMe()
		self.saodang_cell = nil
	end
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
end

function TaFangSweepMsgView:LoadCallBack()
	local ph = self.node_list["ph_stuff_cell"]
	self.saodang_cell = ItemCell.New(ph)

	FuBenPanelWGData.Instance:SetTaFanIsNologer(false)
	self.node_list["img_nohint_hook"]:SetActive(false)

	FuBenPanelWGData.Instance:SetTaFanSaoDanBossNum(10)
	self.node_list["label_boss_num"].text.text = 10
	local other_cfg = FuBenWGCtrl.Instance.defense_fb_data:GetDefenseTowerOtherCfg()
	local str = string.format(Language.DefenseFb.DefenseBossGold, other_cfg.extra_call_gold * 10)
	self.node_list["label_no_longer"].text.text = str


	self.node_list["btn_OK"].button:AddClickListener(BindTool.Bind(self.OnClinkOkHandler, self))
	self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind(self.OnClickCancel, self))

	self.node_list["btn_plus"].button:AddClickListener(BindTool.Bind2(self.OnClickBossPlusMinus, self, true))
	self.node_list["btn_minus"].button:AddClickListener(BindTool.Bind2(self.OnClickBossPlusMinus, self, false))


	self.node_list["btn_nohint_checkbox"].button:AddClickListener(BindTool.Bind1(self.OnClickCheckBox, self))
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
end

function TaFangSweepMsgView:OnClickBossPlusMinus(to_plus)
	local num = FuBenPanelWGData.Instance:GetTaFanSaoDanBossNum()
	if to_plus then
		num = math.min(num + 1, 10)
	else
		num = math.max(num - 1, 1)
	end
	self.node_list["label_boss_num"].text.text = num
	FuBenPanelWGData.Instance:SetTaFanSaoDanBossNum(num)

	local str = string.format(Language.DefenseFb.DefenseBossGold, 20 *num)
	self:SetCheckBoxText(str)
end

function TaFangSweepMsgView:SetCheckBoxText(str)
	if nil ~= str and "" ~= str then
		if nil ~= self.node_list["label_no_longer"] then
			self.node_list["label_no_longer"].text.text = str
		end
	end
end

function TaFangSweepMsgView:OnClickCheckBox()
	local is_visible = self.node_list["img_nohint_hook"]:GetActive()
	self.node_list["img_nohint_hook"]:SetActive(not is_visible)
	FuBenPanelWGData.Instance:SetTaFanIsNologer(not is_visible)
end

function TaFangSweepMsgView:ShowIndexCallBack()
	self:Flush()
end

function TaFangSweepMsgView:OnFlush()
	local other_cfg = FuBenWGCtrl.Instance.defense_fb_data:GetDefenseTowerOtherCfg()
	self.saodang_cell:SetData({item_id = other_cfg.sweep_item_id, is_bind = 1})

	local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.sweep_item_id)
	self.node_list["lbl_item_num"].text.text = item_num .. "/" .. other_cfg.sweep_item_num
	self.node_list["lbl_item_num"].text.text = ToColorStr(self.node_list["lbl_item_num"].text.text, item_num >= other_cfg.sweep_item_num and COLOR3B.WHITE or COLOR3B.D_RED)

	
end

function TaFangSweepMsgView:OnClinkOkHandler()
	local tf_cfg_other = FuBenWGCtrl.Instance.defense_fb_data:GetDefenseTowerOtherCfg()
	local tf_fb_buy_num = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_BUILD_TOWER_FB_BUY_TIMES) or 0
	local tf_fb_join_num = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_BUILD_TOWER_FB_ENTER_TIMES) or 0
	local left_times = tf_cfg_other.enter_free_times + tf_fb_buy_num - tf_fb_join_num

	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	local vip_buy_cfg = FuBenPanelWGData.Instance:GetVipBuyTfCountCfg()
	local day_buy_times = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_BUILD_TOWER_FB_BUY_TIMES) or 0
	local time = vip_buy_cfg["param_" .. role_vip] - day_buy_times

	local other_cfg = FuBenWGCtrl.Instance.defense_fb_data:GetDefenseTowerOtherCfg()

	local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.sweep_item_id)
	if item_num < other_cfg.sweep_item_num then
		GlobalEventSystem:Fire(KnapsackEventType.KNAPSACK_LECK_ITEM, other_cfg.sweep_item_id, other_cfg. sweep_item_num)
		return
	end
	local is_max_count = vip_buy_cfg["param_" .. 15] == day_buy_times
	if left_times == 0 and time > 0 then
		FuBenPanelWGCtrl.Instance:OpenTaFangBuy()
	elseif left_times == 0 and time <= 0 then
		if is_max_count then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.BuyMaxCount)
		else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.DefenseFb.LeftSweepTimes)
		end
	else
		local num = FuBenPanelWGData.Instance:GetTaFanSaoDanBossNum()
		local is_nolonger_tips = FuBenPanelWGData.Instance:GetTaFanIsNologer()
		local sweep_boss_num = is_nolonger_tips and num or 0
		FuBenPanelWGData.Instance:SetSaoDangMark(true)
		FuBenWGCtrl.Instance:SendSwipeFB(FUBEN_TYPE.FBCT_TAFANG, sweep_boss_num)
	end
end

function TaFangSweepMsgView:OnClickCancel()
	self:Close()
end