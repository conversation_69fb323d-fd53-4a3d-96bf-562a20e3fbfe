﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_Singleton_RuntimeGUIMgrWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(Nirvana.Singleton<RuntimeGUIMgr>), typeof(System.Object), "Singleton_RuntimeGUIMgr");
		<PERSON><PERSON>un<PERSON>("New", _CreateNirvana_Singleton_RuntimeGUIMgr);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.Reg<PERSON>ar("Instance", get_Instance, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateNirvana_Singleton_RuntimeGUIMgr(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				Nirvana.Singleton<RuntimeGUIMgr> obj = new Nirvana.Singleton<RuntimeGUIMgr>();
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: Nirvana.Singleton<RuntimeGUIMgr>.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Instance(IntPtr L)
	{
		try
		{
			ToLua.PushObject(L, Nirvana.Singleton<RuntimeGUIMgr>.Instance);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

