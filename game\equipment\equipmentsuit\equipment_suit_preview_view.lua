SuitPreviewView = SuitPreviewView or BaseClass(SafeBaseView)

SuitPreviewView.TabIndex = {
    ZB = TabIndex.equipment_suit_eq,                           --装备
    SP = TabIndex.equipment_suit_sp,                           --饰品
}
local SuitIndex = {0, 1, 2}

function SuitPreviewView:__init()
    self:SetMaskBg(true,true)
    self.view_layer = UiLayer.Normal
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
    self:AddViewResource(0, "uis/view/equipment_suit_ui_prefab", "layout_preview_view")
    --self:AddViewResource(SuitPreviewView.TabIndex.SP, "uis/view/equipment_suit_ui_prefab", "layout_preview_view")
end

function SuitPreviewView:ReleaseCallBack()
    if self.suit_order_tab then
        self.suit_order_tab:DeleteMe()
        self.suit_order_tab = nil
    end

    if self.ph_top_btn then
        self.ph_top_btn:DeleteMe()
        self.ph_top_btn = nil
    end

    if self.suit_item_group then
        for k,v in pairs(self.suit_item_group) do
            v:DeleteMe()
        end
        self.suit_item_group = nil
    end

    self.suit_order = nil
    self.suit_set = nil
end

function SuitPreviewView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.Equip.SuitPreView
    self:SetSecondView(Vector2(1174, 676))
    self.suit_order = -1
    self.suit_set = -1
    local tab_list = EquipmentWGData.Instance:GetSuitPreviewTab()
    local top_list = EquipmentWGData.Instance:GetSuitPreviewTopTab()

    self.ph_top_btn = AsyncListView.New(SuitTopRender, self.node_list.ph_top_list)
    self.ph_top_btn:SetDataList(top_list)
    self.ph_top_btn:SetSelectCallBack(BindTool.Bind1(self.TopBarChangeToIndex, self))
    self.ph_top_btn:SetDefaultSelectIndex(1)

    self.suit_order_tab = AsyncListView.New(SuitOrderTabRender, self.node_list.ph_btn_listview)
    self.suit_order_tab:SetDataList(tab_list)
    self.suit_order_tab:SetSelectCallBack(BindTool.Bind1(self.OrderChangeToIndex, self))
    self.suit_order_tab:SetDefaultSelectIndex(1)

    self.suit_item_group = {}
    for i = 1, #SuitIndex do
        self.suit_item_group[i] = SuitGroupRender.New()
        self.suit_item_group[i]:LoadAsset("uis/view/equipment_suit_ui_prefab", "layout_suit_group", self.node_list.suit_layout_group.transform)
    end
end

function SuitPreviewView:OnFlush()
    self:FlushEquip()
end

function SuitPreviewView:ShowIndexCallBack()
    self:JumpToTab()
end

function SuitPreviewView:JumpToTab()
    local btn_index, top_btn_index = EquipmentWGData.Instance:GetJumpSuitPreviewTab()
    self.suit_order_tab:JumpToIndex(btn_index)
    self.ph_top_btn:JumpToIndex(top_btn_index)
end

function SuitPreviewView:FlushEquip()
    local equip_list = EquipmentWGData.Instance:GetSuitPreviewList(self.suit_order, self.suit_set)

    for i = 1, #SuitIndex do
        local data = {}
        data.suit_index = SuitIndex[i]
        data.equip_data = equip_list
        self.suit_item_group[i]:SetData(data)
    end
end

function SuitPreviewView:TopBarChangeToIndex(cell)
    local top_cell = cell:GetData()
    if self.suit_set == top_cell then
        return
    end
    self.suit_set = top_cell
    self:FlushEquip()
end

function SuitPreviewView:OrderChangeToIndex(cell)
    local btn_cell = cell:GetData()
    if self.suit_order == btn_cell then
        return
    end
    self.suit_order = btn_cell
    self:FlushEquip()
end



----------------------------------SuitOrderTabRender---------------------------------------
SuitOrderTabRender = SuitOrderTabRender or BaseClass(BaseRender)
function SuitOrderTabRender:OnFlush()
    if self.data == nil then
        return
    end

    self.node_list.text_item.text.text = string.format(Language.Equip.SuitOrder1, CommonDataManager.GetDaXie(self.data))
    self.node_list.text_item2.text.text = string.format(Language.Equip.SuitOrder1, CommonDataManager.GetDaXie(self.data))
    self:OnSelectChange(self:IsSelectIndex())
end

function SuitOrderTabRender:OnSelectChange(is_select)
    local img_bundle, image_name
    if  self.node_list.bg_image then
        if is_select then
             img_bundle, image_name = ResPath.GetF2CommonButtonToggle("bt_list_btn_xz_1")      -- 切换高亮效果
             self.node_list.text_item2:SetActive(true)
             self.node_list.text_item:SetActive(false)
        else
             img_bundle, image_name = ResPath.GetF2CommonButtonToggle("bt_list_btn_1")
             self.node_list.text_item2:SetActive(false)
             self.node_list.text_item:SetActive(true)
        end

        self.node_list.bg_image.image:LoadSprite(img_bundle, image_name)
    end
end



-------------------------SuitTopRender---------------------------
SuitTopRender = SuitTopRender or BaseClass(BaseRender)
function SuitTopRender:OnFlush()
    if self.data == nil then
        return
    end

    self.node_list.Text.text.text = Language.Equip.SuitEquip[self.data]
    self.node_list.Text_hl.text.text = Language.Equip.SuitEquip[self.data]
    self:OnSelectChange(self:IsSelectIndex())
end

function SuitTopRender:OnClick()
    if self.click_callback then
        self.click_callback(self.data.index)
    end
    self:SetSelectIndex(self.data.index)
end

function SuitTopRender:OnSelectChange(is_select)
    if is_select then
        self.node_list.HLImage:SetActive(true)
        self.node_list.Image:SetActive(false)
    else
        self.node_list.HLImage:SetActive(false)
        self.node_list.Image:SetActive(true)
    end
end



-------------------------SuitGroupRender---------------------------
SuitGroupRender = SuitGroupRender or BaseClass(BaseRender)
function SuitGroupRender:__init()

end

function SuitGroupRender:__delete()
    if self.passive_item_list ~= nil then
        self.passive_item_list:DeleteMe()
        self.passive_item_list = nil
    end
    self.parent = nil
end

function SuitGroupRender:OnFlush()
    if nil == self.data or nil == self.data.equip_data or IsEmptyTable(self.data.equip_data) then
        return
    end

    self.node_list["title"].text.text = string.format(Language.Equip.SuitOrder, CommonDataManager.GetDaXie(self.data.equip_data[1].suit_order), Language.Equip.TabSub3[self.data.suit_index + 1])

    local num = 0
    local equip_data = {}

    for k,v in pairs(self.data.equip_data) do
        if not equip_data[num] then
            equip_data[num] = {}
        end
        equip_data[num].cfg = v
        equip_data[num].suit_index = self.data.suit_index
        num = num + 1
    end

    if nil == self.passive_item_list then
        self.passive_item_list = AsyncBaseGrid.New()
    end

    local bundle, asset = "uis/view/equipment_suit_ui_prefab", "equip_item"
    self.passive_item_list:CreateCells({col = 2, cell_count = num, itemRender = SuitEquipItemRender,
            list_view = self.node_list["ph_equip_list"], assetBundle = bundle, assetName = asset, is_no_data_hide = true})
    self.passive_item_list:SetDataList(equip_data, 0)
    self.node_list.item.layout_element.minHeight = 35 + (math.ceil(num / 2) * 100) + (math.ceil(num / 2) - 1) * 10

end



-------------------------------SuitEquipItemRender---------------------------------------------------------------------
SuitEquipItemRender = SuitEquipItemRender or BaseClass(BaseRender)
function SuitEquipItemRender:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.cell)
        self.item_cell:SetIsShowTips(false)
    end
    self.suit_index = 0
end

function SuitEquipItemRender:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end

    self.suit_index = nil
end

function SuitEquipItemRender:OnFlush()
    if nil == self.data or not self.data.cfg or not self.data.cfg.suit_order then
        return
    end

    local act_stuff_list = EquipmentWGData.Instance:GetCountSuitPartStuffNum()
    
    for i = 0, 2 do
        self.node_list["equip_desc"..i]:SetActive(false)
        self.node_list["equip_num"..i]:SetActive(false)
    end
    local equip_part_count_list = EquipmentWGData.Instance:GetPartEquipUseSuitCount(self.data.cfg.suit_part)
    local stuff_list = EquipmentWGData.Instance:GetStuffActList(self.data.cfg.suit_order, self.data.cfg.suit_part)
    local stuff_suit_list = stuff_list[self.data.suit_index + 1]
    self.item_cell:SetData({item_id = self.data.cfg.item})

    local new_stuff_list = {}
    local stuff_sort = {}
    for i = 0, self.data.suit_index do
        local stuff_suit_list2 = stuff_list[i + 1]
        for t = 0, self.data.suit_index do
            local key = t + 1
            local stuff_id = stuff_suit_list2["stuff_".. key .. "_id"]
            local need_num = stuff_suit_list2["stuff_".. key .."_num"]
            if stuff_id > 0 and need_num > 0 then
                if not new_stuff_list[stuff_id] then
                    new_stuff_list[stuff_id] = need_num
                    stuff_sort[key] = stuff_id
                else
                    new_stuff_list[stuff_id] = new_stuff_list[stuff_id] + need_num
                end
            end
        end
    end
    for i = 0, self.data.suit_index do
        local key = i + 1 --     1 - 3
        local stuff_id = stuff_sort[key] or 0 --stuff_suit_list["stuff_".. key .. "_id"]
        local need_num = new_stuff_list[stuff_id] or 0--stuff_suit_list["stuff_".. key .."_num"]
        if self.node_list["equip_desc" .. i] and stuff_id > 0 and need_num > 0 then
            local item_cfg = ItemWGData.Instance:GetItemConfig(stuff_id)
            if item_cfg then
                local count = ItemWGData.Instance:GetItemNumInBagById(stuff_id)
                local be_used_num = equip_part_count_list[stuff_id] or 0
                count = count + be_used_num
                local color = count >= need_num and COLOR3B.GREEN or COLOR3B.RED
                local str = ""
                if count >= need_num then
                    str = ToColorStr(Language.Equip.SuitPreViewNum2,color)
                else
                    str = ToColorStr(string.format(Language.Equip.SuitPreViewNum3, need_num - count),color)
                end
                self.node_list["equip_num"..i]:SetActive(true)
                self.node_list["equip_desc"..i]:SetActive(true)
                self.node_list["equip_desc"..i].text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
                self.node_list["equip_num"..i].text.text = str --string.format(Language.Equip.SuitPreViewNum, ToColorStr(count, color), need_num)
            end
        end
    end

end
