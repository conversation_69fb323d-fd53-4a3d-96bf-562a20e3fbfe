OperationActImage = OperationActImage or BaseClass(OperationActRenderBase)

function OperationActImage:__delete()
	self.node_list = nil
end

function OperationActImage:Reset()
    self.node_list.image_root:SetActive(false)
end

--[[配置数据：
    ----[[图片
        bundle_name = "",
        asset_name = "",

        render_type = OARenderType.Image,
        should_ani = false,

		move_time
		pos_up
		pos_down

		image_effect_bundle
		image_effect_asset
    ----]]
--]]
function OperationActImage:Show()
	if not self.data.bundle_name or self.data.bundle_name == "" or
			not self.data.asset_name or self.data.asset_name == "" then
		return
	end

	self.node_list.image_root.raw_image:LoadSprite(self.data.bundle_name, self.data.asset_name, function()
		self.node_list.image_root.raw_image:SetNativeSize()
		self.node_list.image_root:SetActive(true)
	end)

	if self.data.should_ani ~= nil and type(self.data.should_ani) == "boolean" then
		self.node_list["image_root"].animator.enabled = self.data.should_ani
	end

	if self.data.pos_up and self.data.pos_down then
		local time = self.data.move_time and self.data.move_time or 0.5
		UITween.MoveLoop(self.node_list["image_root"], self.data.pos_up, self.data.pos_down , time)
	end

	if self.node_list.image_effect then
		if self.data.image_effect_bundle and self.data.image_effect_asset then
			self.node_list.image_effect:SetActive(true)
			self.node_list.image_effect:ChangeAsset(self.data.image_effect_bundle , self.data.image_effect_asset)
		else
			self.node_list.image_effect:SetActive(false)
		end
	end
end
