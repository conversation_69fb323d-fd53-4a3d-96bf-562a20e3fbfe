-- 付费解锁奖励界面
PositionalWarfarePayUnlockView = PositionalWarfarePayUnlockView or BaseClass(SafeBaseView)

function PositionalWarfarePayUnlockView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(600, 424)})
    self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_positional_warfare_pay_unlock_view")
end

function PositionalWarfarePayUnlockView:LoadCallBack()
    local need_gold = PositionalWarfareWGData.Instance:GetOtherAttrValue("score_added_need_gold")
    self.node_list.desc_content.text.text = string.format(Language.PositionalWarfare.PayUnlockDescContent, need_gold)

    XUI.AddClickEventListener(self.node_list.btn_sure, BindTool.Bind(self.OnClickSureBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_canel, BindTool.Bind(self.Close, self))

    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
    end
end

function PositionalWarfarePayUnlockView:ReleaseCallBack()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function PositionalWarfarePayUnlockView:OnFlush()
    local data_list = PositionalWarfareWGData.Instance:GetPersonScoreRewardDataList()
    self.reward_list:SetDataList(data_list)
end

function PositionalWarfarePayUnlockView:OnClickSureBtn()
    PositionalWarfareWGCtrl.Instance:SendCrossLandWarOperate(CROSS_LAND_WAR_OPERATE_TYPE.ADDED_DEVOTE_REWARD)
    self:Close()
end