FengShenBangView = FengShenBangView or BaseClass(SafeBaseView)

local act_is_open = true
function FengShenBangView:__init(view_name)
	self.view_style = ViewStyle.Full
	self:SetMaskBg(true, true)

	self:AddViewResource(0, "uis/view/fengshenbang_prefab", "layout_fengshenbang")
end

function FengShenBangView:LoadCallBack()
	if self.root_node_transform then -- 退出副本后打开界面动画节点有问题(先这样临时解决)
		RectTransform.SetAnchoredPositionXY(self.root_node_transform.transform, 0, 0)
	end
	self:InitParam()
	self:InitListener()
	self:InitModel()
	self:RefreshActTime()
end

function FengShenBangView:ReleaseCallBack()
	if self.item_list then
		for k, v in pairs(self.item_list) do
			v:DeleteMe()
		end
		self.item_list = nil
	end
	GlobalTimerQuest:CancelQuest(self.close_timer)
	CountDownManager.Instance:RemoveCountDown("fengshenbang_challeng_cd")
end

function FengShenBangView:OpenCallBack()
	FengShenBangWGCtrl.Instance:RequestFengShenBang(CS_OGA_GODS_RANK_TYPE.CS_OGA_GODS_RANK_TYPE_ACTIVITY_INFO)
	FengShenBangWGCtrl.Instance:RequestFengShenBang(CS_OGA_GODS_RANK_TYPE.CS_OGA_GODS_RANK_TYPE_PERSON_INFO)
end

function FengShenBangView:CloseCallBack()
	RemindManager.Instance:Fire(RemindName.FengShenBang)
end

function FengShenBangView:OnFlush(param_t)
	for k, v in pairs(param_t) do
		if k == "ser_data" then
			self:RefreshView()
		elseif k == "role_info" then
			self:RefreshModelPanel()
		elseif k == "person_info" then
			self:RefreshCountDown()
		elseif k == "pass_day" then
			local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
			local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.FengShenBang)
			if act_cfg and open_day > act_cfg.act_serverover_day and not self.close_timer then
				self.close_timer = GlobalTimerQuest:AddDelayTimer(function()
					self.close_timer = nil
					self:Close()
				end, 15)
			end
		elseif k == "delay_count" then
			self:RefreshActTime()
		end
	end
	self:RefreshJoinTimeShow()
end

function FengShenBangView:InitParam()
	self.item_list = {}
end

function FengShenBangView:InitListener()
	self.node_list.tips_btn.button:AddClickListener(BindTool.Bind(self.OnClickTipsBtn, self))
	self.node_list.title_btn.button:AddClickListener(BindTool.Bind(self.OnClickTitleShow, self))
	self.node_list.close_btn.button:AddClickListener(BindTool.Bind(self.Close, self))
	self.node_list.clear_cd_btn.button:AddClickListener(BindTool.Bind(self.OnClickClearCDBtn, self))
end

function FengShenBangView:InitModel()
	local item_list = {}
	local parent = self.node_list["item_root"]
	for i = 1, 5 do
		local obj = parent.transform:Find("fengshenbang_item_" .. i)
		if not obj then
			break
		end
		item_list[i] = FengShenBangItem.New(obj.gameObject)
		item_list[i]:SetIndex(i)
	end
	self.item_list = item_list
end

function FengShenBangView:RefreshActTime()
	local start_time, clear_time, close_time = FengShenBangWGData.Instance:GetActTime()
	local delay_time = FengShenBangWGData.Instance:GetActDelayTime()

	if delay_time > 0 then
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		local sub_time = clear_time + delay_time - server_time
		if sub_time > 0 then
			local clear_time_str = TimeUtil.FormatSecond(sub_time)
			self.node_list.act_time_label.text.text = string.format(Language.FengShenBang.ActTimeStr2, clear_time_str)
			return
		end
	end

	local end_time_str = TimeUtil.FormatSecond2MYHM(clear_time)
	local start_time_str = TimeUtil.FormatSecond2MYHM(start_time)
	self.node_list.act_time_label.text.text = string.format(Language.FengShenBang.ActTimeStr, start_time_str,
		end_time_str)
end

function FengShenBangView:RefreshView()
	act_is_open = FengShenBangWGData.Instance:GetActIsOpen()

	local act_status = FengShenBangWGData.Instance:GetActStatus()
	if act_status == ACTIVITY_STATUS.CLOSE then
		self.node_list.act_end_desc.text.text = Language.FengShenBang.ActClose
	elseif act_status == ACTIVITY_STATUS.STANDY then
		self.node_list.act_end_desc.text.text = Language.FengShenBang.ActStandy
	else
		self.node_list.act_end_desc.text.text = ""
	end
	self.node_list.cd_time:SetActive(act_status == ACTIVITY_STATUS.OPEN)

	FengShenBangWGCtrl.Instance:CheckXianWeiRob()
end

function FengShenBangView:RefreshCountDown()
	CountDownManager.Instance:RemoveCountDown("fengshenbang_challeng_cd")
	local count_down_time = 0
	local role_person_info = FengShenBangWGData.Instance:GetRolePersonInfo()
	if role_person_info and act_is_open then
		local next_time = role_person_info.next_challenge_timestamp
		local now_time = TimeWGCtrl.Instance:GetServerTime()
		count_down_time = next_time - now_time
	end
	if count_down_time > 0 then
		self.node_list.cd_time_label.text.text = TimeUtil.FormatSecondDHM8(count_down_time)
		CountDownManager.Instance:AddCountDown(
			"fengshenbang_challeng_cd",
			BindTool.Bind(self.UpdateCountDown, self),
			BindTool.Bind(self.RefreshCountDown, self),
			nil,
			count_down_time,
			1
		)
	else
		self.node_list.cd_time_label.text.text = Language.FengShenBang.CanChallengeStr
	end
	self.node_list.clear_cd_btn:SetActive(count_down_time > 0)
end

function FengShenBangView:UpdateCountDown(elapse_time, total_time)
	self.node_list.cd_time_label.text.text = TimeUtil.FormatSecondDHM8(total_time - elapse_time)
end

function FengShenBangView:RefreshModelPanel()
	local role_uid_list = FengShenBangWGData.Instance:GetRankRoleUidList()
	if not self.item_list or not role_uid_list then
		return
	end
	for i, v in ipairs(self.item_list) do
		v:SetData(FengShenBangWGData.Instance:GetRankRoleInfo(role_uid_list[i]) or {})
	end
end

function FengShenBangView:OnClickTipsBtn()
	RuleTip.Instance:SetTitle(Language.FengShenBang.ViewTitle)
	RuleTip.Instance:SetContent(Language.FengShenBang.TipsDesc)
end

function FengShenBangView:OnClickTitleShow()
	FengShenBangWGCtrl.Instance:OpenTitleView()
end

function FengShenBangView:OnClickClearCDBtn()
	local role_person_info = FengShenBangWGData.Instance:GetRolePersonInfo()
	if not role_person_info then
		return
	end
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	local next_time = role_person_info.next_challenge_timestamp
	local cd_time = next_time - now_time
	if cd_time <= 0 then
		return
	end
	local fresh_cost = 0
	local fresh_cost_list = FengShenBangWGData.Instance:GetActCfgList("fresh_cost")
	if fresh_cost_list then
		for i = 1, #fresh_cost_list do
			if cd_time > fresh_cost_list[i].challenge_cd then
				fresh_cost = fresh_cost_list[i].fresh_cost
			else
				break
			end
		end
	end

	local gold = RoleWGData.Instance:GetAttr('gold')
	if gold < fresh_cost then
		VipWGCtrl.Instance:OpenTipNoGold()
		return
	end

	if fresh_cost > 0 then
		TipWGCtrl.Instance:OpenAlertTips(string.format(Language.FengShenBang.ClearCDStr, fresh_cost), function()
			FengShenBangWGCtrl.Instance:RequestFengShenBang(CS_OGA_GODS_RANK_TYPE.CS_OGA_GODS_RANK_TYPE_FRESH_CD)
		end)
	end
end

function FengShenBangView:RefreshJoinTimeShow()
	local info = FengShenBangWGData.Instance:GetRolePersonInfo()
	local other_cfg = FengShenBangWGData.Instance:GetActCfgList("other")
	local join_time_limit = other_cfg.join_num
	local can_join_time = 0 --可参加次数
	local time_color_str
	if not IsEmptyTable(info) then
		--已参加次数
		local is_join_time = math.min(info.day_join_reward_num or can_join_time, join_time_limit)
		can_join_time = join_time_limit - is_join_time
	end
	time_color_str = can_join_time <= 0 and ToColorStr(0, COLOR3B.D_RED) or can_join_time
	self.node_list["join_time_label"].text.text = string.format(Language.FengShenBang.JoinTimeDes, join_time_limit,
		time_color_str, join_time_limit)
end

-------------------------------------------------------------------------------------------------------

FengShenBangItem = FengShenBangItem or BaseClass(BaseRender)

function FengShenBangItem:__init()

end

function FengShenBangItem:__delete()
	self.role_model:DeleteMe()
	self.role_model = nil
	self.boss_model:DeleteMe()
	self.boss_model = nil
end

function FengShenBangItem:LoadCallBack()
	self.node_list.event_trigger.button:AddClickListener(BindTool.Bind(self.OnClickChallengeBtn, self))
	self.node_list.challenge_btn.button:AddClickListener(BindTool.Bind(self.OnClickChallengeBtn, self))
	self.role_model = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["role_model_root"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.S,
		can_drag = false,
	}
	
	self.role_model:SetRenderTexUI3DModel(display_data)
	-- self.role_model:SetUI3DModel(self.node_list.role_model_root.transform, nil, 1, nil, MODEL_CAMERA_TYPE.BASE)

	self.boss_model = RoleModel.New()
	local boss_display_data = {
		parent_node = self.node_list["boss_model_root"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.S,
		can_drag = false,
	}
	
	self.boss_model:SetRenderTexUI3DModel(boss_display_data)
	-- self.boss_model:SetUI3DModel(self.node_list.boss_model_root.transform, nil, 1, nil, MODEL_CAMERA_TYPE.BASE)
	self.node_list.name_label.text.text = ""
end

function FengShenBangItem:OnFlush()
	local is_empty = IsEmptyTable(self:GetData())
	self.node_list.role_model_root:SetActive(not is_empty)
	self.node_list.boss_model_root:SetActive(is_empty)
	self.node_list.challenge_btn:SetActive(act_is_open)
	self:FlushTitleImg()
	if is_empty then
		self:FlushBossModel()
	else
		self:FlushRoleModel()
	end
end

function FengShenBangItem:FlushTitleImg()
	local scene_cfg = FengShenBangWGData.Instance:GetSceneCfgData(self:GetIndex())
	if scene_cfg then
		local bundle, asset = ResPath.GetTitleModel(scene_cfg.title_id)
		self.node_list.title_img:ChangeAsset(bundle, asset)
	end

	local boss_info = FengShenBangWGData.Instance:GetBossInfo(self:GetIndex())
	if not boss_info then
		return
	end
	local my_capability = RoleWGData.Instance:GetRoleVo().capability or 0
	local boss_capability = boss_info.capability or 0
	local img_name = boss_capability > my_capability and "fsb_tiaozhan" or "fsb_tiaozhan_green"
	self.node_list.challenge_btn.image:LoadSprite(ResPath.GetFengShenBangIcon(img_name))
end

function FengShenBangItem:FlushRoleModel()
	local data = self:GetData()
	self.node_list.name_label.text.text = data.role_name or data.name
	self.role_model:SetModelResInfo(data)
	self.role_model:RemoveWing()
end

function FengShenBangItem:FlushBossModel()
	local scene_cfg = FengShenBangWGData.Instance:GetSceneCfgData(self:GetIndex())
	local monster_cfg = scene_cfg and BossWGData.Instance:GetMonsterInfo(scene_cfg.boss_id)
	if monster_cfg then
		local bundle, asset = ResPath.GetMonsterModel(monster_cfg.resid)
		self.boss_model:SetMainAsset(bundle, asset)
	end
	self.node_list.name_label.text.text = monster_cfg and monster_cfg.name or ""
end

function FengShenBangItem:OnClickChallengeBtn()
	FengShenBangWGData.Instance:SetSelectIndex(self:GetIndex())
	FengShenBangWGCtrl.Instance:OpenChallengeView()
end
