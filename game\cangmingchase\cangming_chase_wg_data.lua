CangMingChaseWGData = CangMingChaseWGData or BaseClass()

function CangMingChaseWGData:__init()
	if CangMingChaseWGData.Instance then
		error("[CangMingChaseWGData] Attempt to create singleton twice!")
		return
	end

	CangMingChaseWGData.Instance = self
	self:InitConfig()
	self.rmb_shop_id = 0
	self.free_shop_id = 0
	RemindManager.Instance:Register(RemindName.CangMingBuyAct, BindTool.Bind(self.GetCangMingBuyActRed, self))
end

function CangMingChaseWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.CangMingBuyAct)
    CangMingChaseWGData.Instance = nil
end

function CangMingChaseWGData:InitConfig()
	self.cangming_chase_auto = ConfigManager.Instance:GetAutoConfig("operation_activity_waistlight_rmb_buy_auto")
	self.open_day_cfg = self.cangming_chase_auto.open_day
	self.rmb_shop_cfg = self.cangming_chase_auto.rmb_shop
	self.free_shop_cfg = self.cangming_chase_auto.free_shop
end

function CangMingChaseWGData:SetShopIsBuyFlag(protocol)
	--print_error("protocol",protocol)
	self.is_buy_rmb = protocol.is_buy_rmb == 1
	self.is_buy_free = protocol.is_buy_free == 1
	self:ChangeShopID()
end

function CangMingChaseWGData:GetShopIsBuyFlag()
	return self.is_buy_rmb, self.is_buy_free
end

function CangMingChaseWGData:ChangeShopID()
	local start_day = ActivityWGData.Instance:GetActivityOpenInServerDay(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CANGMING_BUY)
	local cfg = self.open_day_cfg
	for k, v in ipairs(cfg) do
		if start_day >= v.start_day and start_day <= v.end_day then
			self.rmb_shop_id = v.rmb_shop_id
			-- self.free_shop_id = v.rmb_shop_id
			return
		end
	end
end

-- function CangMingChaseWGData:GetCurFreeShopID()
-- 	return self.free_shop_id
-- end

function CangMingChaseWGData:GetCurShopCfg()
	return self.rmb_shop_cfg[self.rmb_shop_id]
end

function CangMingChaseWGData:GetCangMingBuyActRed()
	-- 暂时屏蔽
	-- local _, is_buy_free = self:GetShopIsBuyFlag()
	-- if not is_buy_free then
	-- 	return 1
	-- end

    return 0
end