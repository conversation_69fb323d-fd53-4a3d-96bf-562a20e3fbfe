require("game/openning/openning_view")

-- 开启仙途
OpenningWGCtrl = OpenningWGCtrl or BaseClass(BaseWGCtrl)

function OpenningWGCtrl:__init()
	if OpenningWGCtrl.Instance ~= nil then
		ErrorLog("[OpenningWGCtrl] attempt to create singleton twice!")
		return
	end

	OpenningWGCtrl.Instance = self
	self.view = OpenningView.New(GuideModuleName.OpenningView)

	self:BindGlobalEvent(OtherEventType.TASK_COMPLETED_ID_LIST_INIT, BindTool.Bind(self.OnTaskCompletedIdListInit, self))
	self:BindGlobalEvent(SceneEventType.CLOSE_LOADING_VIEW, BindTool.Bind(self.OnCloseLoadingView, self))

	self.first_open = true
	self.first_vip_initial_open = true
	self.loading_end = false
	self.task_info_init = false
end

function OpenningWGCtrl:__delete()
	self.first_open = true
	self.first_vip_initial_open = true
	self.loading_end = false
	self.task_info_init = false
	self.view:DeleteMe()
	self.view = nil
	OpenningWGCtrl.Instance = nil
end

-- 已完成任务列表初始化
function OpenningWGCtrl:OnTaskCompletedIdListInit()
	self.task_info_init = true
	self:CheckOpenView()
end

function OpenningWGCtrl:OnCloseLoadingView()
	self.loading_end = true
	self:CheckOpenView()
end

function OpenningWGCtrl:CheckOpenView()
	if not self.task_info_init or not self.loading_end then
		return
	end
	local first_zhu_task_cfg = TaskWGData.Instance:GetFirstTaskCfgByType(GameEnum.TASK_TYPE_ZHU)
	if first_zhu_task_cfg then
		local task_id = first_zhu_task_cfg.task_id
		if self.first_open and not TaskWGData.Instance:GetTaskIsCompleted(task_id) then
			--ViewManager.Instance:Open(GuideModuleName.OpenningView)
			self.first_open = false
		end
	end

	local is_fetch_initial_vip = VipWGData.Instance:GetIsFetchInitialVip()
	if self.first_vip_initial_open and is_fetch_initial_vip then
		VipWGCtrl.Instance:OpenVipInitialView()
		self.first_vip_initial_open = false
	end
end
