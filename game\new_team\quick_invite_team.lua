QuickInviteTeam = QuickInviteTeam or BaseClass(SafeBaseView)

local DownCountTime = 20
function QuickInviteTeam:__init()
	self.view_name = "QuickInviteTeam"
	self.view_layer = UiLayer.Pop
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

	self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_quick_invite_team")

	self.running_timer = nil
	self.cur_times = 0
	self.cur_invite_team_info = nil
end

function QuickInviteTeam:__delete()
end

function QuickInviteTeam:ReleaseCallBack()
	self:ClearTimer()
end

function QuickInviteTeam:LoadCallBack()
	self.node_list["IgnoreBtn"].button:AddClickListener(BindTool.Bind(self.OnReceiveHandler, self, 1))
	self.node_list["JoinBtn"].button:AddClickListener(BindTool.Bind(self.OnReceiveHand<PERSON>, self, 0, 0))
	self.node_list["btn_fenshen_join"].button:AddClickListener(BindTool.Bind(self.OnReceiveHandler, self, 0, 1))
	self:FlushView()
end

function QuickInviteTeam:OnReceiveHandler(is_received, is_fenshen)
	local is_fenshen_open = NewTeamWGData.Instance:GetFenshenFuncIsOpen()
	if is_fenshen == 1 and not is_fenshen_open then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.FenShenNotOpen)
		return
	end

	self:SendInviteUserTransmitRet(is_received, is_fenshen)
	self:ClearTimer()
	self:FlushView()
end

function QuickInviteTeam:SendInviteUserTransmitRet(is_received, is_fenshen)
	is_received = is_received or 1
	if self.cur_invite_team_info == nil 
		or self.cur_invite_team_info.inviter == nil
		or self.cur_invite_team_info.inviter == 0 then
		return
    end
    self.is_record_refuse_flag = self.node_list.toggle.toggle.isOn and 1 or 0
	SocietyWGCtrl.Instance:SendInviteUserTransmitRet(self.cur_invite_team_info.inviter, is_received, self.is_record_refuse_flag, is_fenshen)
end

function QuickInviteTeam:OpenCallBack()
	
end

function QuickInviteTeam:OnFlush()
    self:FlushView()
end

function QuickInviteTeam:FlushView()
	if self.running_timer then
		return
	end
    local team_info = NewTeamWGData.Instance:GetQuickInviteTeamTopTeamInfo()
	if team_info == nil or IsEmptyTable(team_info) then
		self:ClearTimer()
		self:Close()
		return
	end
	self.cur_invite_team_info = team_info

	self.node_list["inviter_name"].text.text = string.format(Language.NewTeam.QuickInviter, team_info.inviter_name)
    local team_target = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_info.team_type, team_info.team_fb_mode)
    
    local map_name = Config_scenelist[Scene.Instance:GetSceneId()] and Config_scenelist[Scene.Instance:GetSceneId()].name or ""
    
    local is_same_scene = false
    if team_info.team_type == TeamDataConst.GoalType.None then --是无目标 且场景id相同 且是boss场景
        is_same_scene = Scene.Instance:GetSceneId() == team_info.leader_scene_id and BossWGData.IsBossScene(Scene.Instance:GetSceneType())
    end
    local target_str = is_same_scene and map_name or Language.NewTeam.NoTeamTypeTarget --显示地图名
    local normal_target_name = (not IsEmptyTable(team_target) and team_target.team_type_name and team_target.team_type_name ~= "")
    and team_target.team_type_name or Language.NewTeam.NoTeamTypeTarget

    local team_type_name = is_same_scene and target_str or normal_target_name

	self.node_list["target"].text.text = string.format(Language.NewTeam.QuickInviteTeamTypeName, team_type_name)
	self.node_list["IgnoreBtnText"].text.text = string.format(Language.NewTeam.IgnoreStr, DownCountTime)
    self.node_list.toggle.toggle.isOn = false
    self.node_list.country_icon:SetActive(false)

	if not self.running_timer then
		self.running_timer = GlobalTimerQuest:AddTimesTimer(function()
			self.cur_times = self.cur_times + 1

			if not self:IsLoaded() or not self:IsOpen() then
				self:ClearTimer()
				return
			end
			self.node_list["IgnoreBtnText"].text.text = string.format(Language.NewTeam.IgnoreStr, tostring(DownCountTime - self.cur_times))
            if self.cur_times >= DownCountTime then
				self:SendInviteUserTransmitRet(1)
                self:ClearTimer()
				self:FlushView()
			end
		end, 1, DownCountTime)
	end
end

function QuickInviteTeam:ClearTimer()
	self.cur_times = 0
	GlobalTimerQuest:CancelQuest(self.running_timer)
	self.running_timer = nil
end