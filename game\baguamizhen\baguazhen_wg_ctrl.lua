--[[
Author: error: git config user.name && git config user.email & please set dead value or install git
Date: 2022-09-28 12:47:44
LastEditors: error: git config user.name && git config user.email & please set dead value or install git
LastEditTime: 2022-10-08 14:42:37
FilePath: \Lua\game\baguamizhen\baguazhen_wg_ctrl.lua
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
--]]
require("game/baguamizhen/baguamizhen_wg_data")
require("game/baguamizhen/baguamizhen_task_view")
require("game/fubenpanel/fubenpanel_baguamizhen_buy")
require("game/fubenpanel/fubenpanel_bagua")
require("game/fubenpanel/fubenpanel_baguamizhen_saodang")


BaGuaMiZhenWGCtrl = BaGuaMiZhenWGCtrl or BaseClass(BaseWGCtrl)

function BaGuaMiZhenWGCtrl:__init()
	if BaGuaMiZhenWGCtrl.Instance then
		print_error("BaGuaMiZhenWGCtrl:Attempt to create singleton twice!")
	end
	BaGuaMiZhenWGCtrl.Instance = self
	self.data = BaGuaMiZhenWGData.New()
	self.buy_view = BaGuaMiZhenBuyView.New(GuideModuleName.FuBenPanelBaGuaMiZhenBuyView)
	self.saodang_view = BaGuaMiZhenSaoDangView.New(GuideModuleName.FuBenPanelBaGuaMiZhenSaoDangView)
	self.task_view = BaGuaMiZhenTaskView.New(GuideModuleName.BaGuaMiZhenTaskView)

	self:RegisterAllProtocals()
end

-- 注册协议
function BaGuaMiZhenWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(SCBaGuaMiZhenInfo, "OnSCBaGuaMiZhenInfo")

end

function BaGuaMiZhenWGCtrl:__delete()
	self.task_view:DeleteMe()
	self.saodang_view:DeleteMe()
	self.buy_view:DeleteMe()

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	BaGuaMiZhenWGCtrl.Instance = nil
end


function BaGuaMiZhenWGCtrl:OpenTaskView()
	self.task_view:Open()
end


function BaGuaMiZhenWGCtrl:CloseTaskView()
	self.task_view:ResetTaskPanel()
	self.task_view:Close()
end

function BaGuaMiZhenWGCtrl:FlushTaskView()
	self.task_view:Flush()
end

function BaGuaMiZhenWGCtrl:OnSCBaGuaMiZhenInfo(protocol)
	self.data:OnSCBaGuaMiZhenInfo(protocol)
end