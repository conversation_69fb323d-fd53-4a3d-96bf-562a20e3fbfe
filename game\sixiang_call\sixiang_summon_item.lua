-------------------------------------------
-- 四象物品
-------------------------------------------
SiXiangSummonItem = SiXiangSummonItem or BaseClass(FSBoneEquipBaseItem)
function SiXiangSummonItem:DoLoad(parent)
    self:LoadAsset("uis/view/sixiang_call_prefab", "sixiang_call_render", parent.transform)
end

function SiXiangSummonItem:OnFlush()
    if self.data == nil then
        return
    end

    FSBoneEquipBaseItem.OnFlush(self)
    local data = self.data.bone_data or self.data.item_data
    if data == nil then
        return
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
    if item_cfg and self.node_list.level then
        self.node_list.level.text.text = item_cfg.name
    end

    
end