local LEVEL_DELT_TIME = 0.5
StrangeCatalogView = StrangeCatalogView or BaseClass(SafeBaseView)

function StrangeCatalogView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/strange_catalog_ui_prefab", "layout_strange_catalog_new")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
end

function StrangeCatalogView:ReleaseCallBack()
	if self.artifact_list then
		for k, v in pairs(self.artifact_list) do
			v:DeleteMe()
		end
		self.artifact_list = nil
	end

	if self.qiwu_list then
		self.qiwu_list:DeleteMe()
		self.qiwu_list = nil
	end

	--[[
	if self.big_type_list then
		self.big_type_list:DeleteMe()
		self.big_type_list = nil
	end

	if self.sub_type_list then
		self.sub_type_list:DeleteMe()
		self.sub_type_list = nil
	end

	if self.item_grid then
		self.item_grid:DeleteMe()
		self.item_grid = nil
	end
	]]

	if self.qh_attr_list then
		for k, v in pairs(self.qh_attr_list) do
			v:DeleteMe()
		end
		self.qh_attr_list = nil
	end

	if self.qh_item_pos then
		self.qh_item_pos:DeleteMe()
		self.qh_item_pos = nil
	end

	if self.xl_item_pos then
		self.xl_item_pos:DeleteMe()
		self.xl_item_pos = nil
	end

	self.big_index = nil
	--self.sub_index = nil
	self.select_compose_type = nil
	self.select_seq = nil
	self.select_qiwu_index = nil
	self.jump_to_seq = nil
	self.is_auto_level = false

	self:CancelAutoLevelTimer()

	RemindManager.Instance:UnBind(self.remind_callback)
end

function StrangeCatalogView:LoadCallBack()
	if not self.artifact_list then
		self.artifact_list = {}
	    for i = 1, 6 do
	        self.artifact_list[i] = ArtifactQWCell.New(self.node_list.artifact_list:FindObj("artifact_cell_" .. i))
	        self.artifact_list[i]:SetIndex(i)
	        self.artifact_list[i]:SetClickCallBack(BindTool.Bind1(self.OnSelectBTypeCallBack, self))
	    end
	end

	if not self.qiwu_list then
		self.qiwu_list = AsyncFancyAnimView.New(SingleQiRender, self.node_list["qwyl_list_view"])
		self.qiwu_list:SetSelectCallBack(BindTool.Bind(self.OnSelectItem, self))
		self.qiwu_list:SetStartZeroIndex(false)
	end

	--[[
	if not self.big_type_list then
		self.big_type_list = AsyncListView.New(QwBigListRender, self.node_list["big_qiwu_list"])
		self.big_type_list:SetSelectCallBack(BindTool.Bind(self.SelectBTypeCallBack, self))
	end

	if not self.sub_type_list then
		self.sub_type_list = AsyncListView.New(SubTypeCell, self.node_list.small_qiwu_list)
		self.sub_type_list:SetSelectCallBack(BindTool.Bind(self.OnClickSubCell, self))
	end

	self.is_show_type = true
	self:OnClickSelectType()
	XUI.AddClickEventListener(self.node_list["btn_select_type"], BindTool.Bind(self.OnClickSelectType, self))
	XUI.AddClickEventListener(self.node_list["close_list_part"], BindTool.Bind(self.OnClickSelectType, self))

	if not self.item_grid then
		self.item_grid = QiWenYiWuGrid.New()
		self.item_grid:CreateCells({
			list_view = self.node_list["qwyl_grid_list"],
			itemRender = SingleQiRender,
			assetBundle = "uis/view/strange_catalog_ui_prefab",
			assetName = "cel_qwyl_item",
			columns = 4,
		})
		self.item_grid:SetSelectCallBack(BindTool.Bind(self.OnSelectItem, self))
	end
	]]

	local str
	for i = 1, 2 do
		str = string.format("toggle_%d", i)
		XUI.AddClickEventListener(self.node_list[str], BindTool.Bind(self.OnClickToggle, self, i))
		if i == 1 then
			self.node_list[str].toggle.isOn = true
			self:OnClickToggle(i, true)
		end
	end

	if self.qh_attr_list == nil then
		self.qh_attr_list = {}
		local node_num = self.node_list["qh_attr_list"].transform.childCount
		for i = 1, node_num do
			self.qh_attr_list[i] = CommonAddAttrRender.New(self.node_list["qh_attr_list"]:FindObj("attr_" .. i))
			self.qh_attr_list[i]:SetAttrNameNeedSpace(true)
		end
	end

	self.qh_item_pos = ItemCell.New(self.node_list.qh_item_pos)
	self.xl_item_pos = ItemCell.New(self.node_list.xl_item_pos)

	XUI.AddClickEventListener(self.node_list.go_trammels_btn, BindTool.Bind(self.OnClickTrammel, self))
	XUI.AddClickEventListener(self.node_list.go_reslove_btn, BindTool.Bind(self.OnClickReslove, self))
	XUI.AddClickEventListener(self.node_list.btn_qianghua, BindTool.Bind(self.OnClickQiangHua, self))
	XUI.AddClickEventListener(self.node_list.btn_xilian, BindTool.Bind(self.OnClickXiLian, self))
	XUI.AddClickEventListener(self.node_list.btn_yijianxilian, BindTool.Bind(self.OnClickAutoXL, self)) --自动升级
	XUI.AddClickEventListener(self.node_list.btn_hide_right, BindTool.Bind(self.OnClickHideRightBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_show_right, BindTool.Bind(self.OnClickShowRightBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_hide_right_mask, BindTool.Bind(self.OnClickHideRightBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_jump, BindTool.Bind(self.OnClickJumpBtn, self))
	self.select_seq = -1
	self.select_qiwu_index = nil
	self.jump_to_seq = 0
	self.select_compose_type = 1
	self.big_index = -1
	--self.sub_index = -1
	self.is_auto_level = false

    self.remind_callback = BindTool.Bind(self.OtherRemindCallBack, self)
    RemindManager.Instance:Bind(self.remind_callback, RemindName.StrangeCatalogJiBan)
end

function StrangeCatalogView:ShowIndexCallBack()
	self.node_list.title_view_name.text.text = Language.StrangeCatalog.ChangeRoleName

	local bundle, asset = ResPath.GetRawImagesJPG("a3_sxhg_zbj_bj")
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)
end

function StrangeCatalogView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			-- local all_cfg = StrangeCatalogWGData.Instance:UpdateShowList()
			-- if IsEmptyTable(all_cfg) then
			-- 	return
			-- end

			--self.big_type_list:SetDataList(all_cfg)
			--self.node_list["cur_type_name"].text.text = all_cfg[1].name
			if v.sel_seq then
				self.jump_sel_seq = v.sel_seq + 1
			end

			if self.big_index == -1 or self.jump_sel_seq then
				--local big_index = StrangeCatalogWGData.Instance:GetListRemindBigType()
				--self.big_type_list:SelectIndex(big_index)
				self:FlushCapValue()
				self:FlushBigTypeList()
			end

		elseif k == "update_bag" then
			self:UpdateBag()
		elseif k == "update_cap" then
			self:FlushCapValue()
		end
	end
end

function StrangeCatalogView:UpdateBag()
	self:FlushBigTypeList()
	self:FlushRightPart()
	self:FlushQHPart()
	self:FlushXLPart()
	--self:FlushSubType(self.big_index)
	--self:FlushQiWuCell(self.sub_index)
	self:FlushQiWuList()
end

function StrangeCatalogView:FlushBigTypeList()
	local big_type_list = StrangeCatalogWGData.Instance:GetBigTypeDataList()
	for i, v in ipairs(self.artifact_list) do
		v:SetData(big_type_list[i])
	end
	if self.big_index == -1 or self.jump_sel_seq then
		local jump_seq = self.jump_sel_seq or 1
		self.artifact_list[jump_seq]:OnClick(true)
		self.jump_sel_seq = nil
	end
end

function StrangeCatalogView:OnSelectBTypeCallBack(cell)
	local big_index = cell:GetIndex()
	if big_index == self.big_index then
		return
	end

	--local all_cfg = StrangeCatalogWGData.Instance:UpdateShowList()
	-- if IsEmptyTable(all_cfg) then
	-- 	return
	-- end

	self.big_index = big_index

	for k, v in pairs(self.artifact_list) do
		v:SetSelectIndex(self.big_index)
	end

	--self:FlushQiWuCell(self.big_index)
	self:FlushQiWuList(true)
end

--[[
-- big类型选择回调
function StrangeCatalogView:SelectBTypeCallBack(cell)
	local big_index = cell:GetIndex()
	if big_index == self.big_index then
		return
	end

	local all_cfg = StrangeCatalogWGData.Instance:UpdateShowList()
	if IsEmptyTable(all_cfg) then
		return
	end

	self.big_index = big_index
	self.node_list["cur_type_name"].text.text = all_cfg[self.big_index].name
	self:FlushSubType(self.big_index)
	local sub_index = StrangeCatalogWGData.Instance:GetListRemindSubType(self.big_index)
	self.sub_type_list:JumpToIndex(sub_index)
	self.sub_index = -1
end

function StrangeCatalogView:OnClickSelectType()
	self.is_show_type = not self.is_show_type
	self.node_list["qiwu_list_part"]:SetActive(self.is_show_type)
	self.node_list["type_arrow_down"]:SetActive(self.is_show_type)
	self.node_list["type_arrow_up"]:SetActive(not self.is_show_type)
end

function StrangeCatalogView:FlushSubType(sub_index)
	local all_cfg = StrangeCatalogWGData.Instance:UpdateShowList()
	if IsEmptyTable(all_cfg) then
		return
	end

	local sub_data_list = all_cfg[sub_index].sub_type_list
	if not IsEmptyTable(sub_data_list) then
		self.sub_type_list:SetDataList(sub_data_list)
	end
end

-- sub类型选择回调
function StrangeCatalogView:OnClickSubCell(cell)
	local sub_index = cell:GetIndex()
	if self.sub_index == sub_index then
		return
	end

	self.sub_index = sub_index
	self:FlushQiWuCell(self.sub_index)
	local qua_index, seq = StrangeCatalogWGData.Instance:GetListRemindQuaType(self.big_index, self.sub_index)
	self.item_grid:JumpToSeqAndSelect(seq)
end

function StrangeCatalogView:FlushQiWuCell(sub_index)
	local all_cfg = StrangeCatalogWGData.Instance:UpdateShowList()
	if IsEmptyTable(all_cfg) then
		return
	end

	if all_cfg[self.big_index] then
		self.item_grid:SetDataList(all_cfg[self.big_index].sub_type_list[sub_index].quality_type_list)
	end
end
]]

function StrangeCatalogView:FlushQiWuList(need_jump_remind)
	if not self.big_index then
		return
	end

	local data_list = StrangeCatalogWGData.Instance:GetQiWuDataList(self.big_index - 1)
	if IsEmptyTable(data_list) then
		return
	end

	self.qiwu_list:SetDataList(data_list)
	local jump_qiwu_index = self.select_qiwu_index or 1
	if need_jump_remind then
		for i, v in ipairs(data_list) do
			if StrangeCatalogWGData.Instance:SingleRemind(v.seq) then
				jump_qiwu_index = i
				break
			end
		end
	end
	self.qiwu_list:SelectCell(jump_qiwu_index)
end

function StrangeCatalogView:OnSelectItem(cell)
	local data = cell:GetData()
	if data == nil then
		return
	end

	local cell_seq = data.seq
	if self.select_seq ~= cell_seq then
		self:StopLevelOperator()
		self.select_seq = cell_seq
		self.select_qiwu_index = cell:GetIndex()
	end

	local qh_have_remind = StrangeCatalogWGData.Instance:QiangHuaRemind(cell_seq)
	local xl_have_remind = StrangeCatalogWGData.Instance:ZhuLingRemind(cell_seq)
	self.node_list["remind_qh"]:SetActive(qh_have_remind)
	self.node_list["remind_xl"]:SetActive(xl_have_remind)
	self.node_list["img_pannel_red"]:SetActive(qh_have_remind or xl_have_remind)

	local selcet_index = 1
	if not self.select_compose_type then
		if qh_have_remind then
			selcet_index = 1
		elseif xl_have_remind then
			selcet_index = 2
		end
	else
		selcet_index = self.select_compose_type
	end

	if self.node_list["toggle_" .. selcet_index].toggle.isOn then
		self:OnClickToggle(selcet_index)
	else
		self.node_list["toggle_" .. selcet_index].toggle.isOn = true
	end

	self:FlushTrainToggle()
end

function StrangeCatalogView:OnClickToggle(index, is_on)
	if not is_on then
		return
	end

	if self.select_compose_type ~= index then
		self:StopLevelOperator()
	end

	self.select_compose_type = index
	self:FlushTrainToggle()
end

function StrangeCatalogView:FlushTrainToggle()
	self:FlushRightPart()
	if self.select_compose_type == 1 then
		self:FlushQHPart()
	elseif self.select_compose_type == 2 then
		self:FlushXLPart()
	end
end

-- 拿到当前seq信息
function StrangeCatalogView:GetCurSeqInfo()
	return StrangeCatalogWGData.Instance:GetSingleInfo(self.select_seq)
end

-- 刷新战力
function StrangeCatalogView:FlushCapValue()
	local cao_value = StrangeCatalogWGData.Instance:TuJianRecordList()
	self.node_list.cap_value.text.text = cao_value
end

function StrangeCatalogView:FlushRightPart()
	local data = self:GetCurSeqInfo()
	local seq = (data or {}).seq or -1
	local cur_server_info = StrangeCatalogWGData.Instance:GetQWSingleServerInfo(seq)
	if IsEmptyTable(cur_server_info) then
		return
	end

	local bundle, asset = ResPath.GetNoPackPNG(data.pic_seq)
	self.node_list["img_qiwu_icon"].image:LoadSprite(bundle, asset, function ()
		self.node_list["img_qiwu_icon"].image:SetNativeSize()
	end)

	local qiwu_level = cur_server_info.level
	local item_id = data.active_item_id
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg then
		self.node_list["qiwu_name"].text.text = item_cfg.name
		self.node_list["qiwu_level"].text.text = string.format(Language.StrangeCatalog.level, qiwu_level)
	end

	local qiwu_xl_level = cur_server_info.zhuling_level
	local qua_name = Language.ShiTianSuit.InfuseSoulQuality[qiwu_xl_level]
	self.node_list["txt_qiwu_quality"].text.text = string.format(Language.StrangeCatalog.QuaLevel, qua_name)

	bundle, asset = ResPath.GetStrangeCatalogImg("a3_sxhg_zbj_pz_" .. cur_server_info.zhuling_level)
	self.node_list["img_qiwu_quality"].image:LoadSprite(bundle, asset)
end

-------------------------------------强化面板 部分-------------------------------------
function StrangeCatalogView:FlushQHPart()
	local data = self:GetCurSeqInfo()
	if IsEmptyTable(data) then
		self.node_list.qianghua_has_data:SetActive(false)
		self.node_list.qianghua_nodata:SetActive(true)
		return
	end

	local cur_server_info = StrangeCatalogWGData.Instance:GetQWSingleServerInfo(data.seq)
	if IsEmptyTable(cur_server_info) then
		self.node_list.qianghua_has_data:SetActive(false)
		self.node_list.qianghua_nodata:SetActive(true)
		return
	end

	self.node_list.qianghua_has_data:SetActive(true)
	self.node_list.qianghua_nodata:SetActive(false)

	local qiwu_level = cur_server_info.level
	-- local item_id = data.active_item_id
	-- local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	-- if item_cfg then
	-- 	self.node_list.qh_item_name.text.text = item_cfg.name
	-- 	self.node_list.qiwu_level.text.text = string.format(Language.StrangeCatalog.level, qiwu_level)
	-- end

	local add_value = StrangeCatalogWGData.Instance:GetLevelAddValue(qiwu_level)											-- 等级对应的属性万分比
	local qh_attr_list = StrangeCatalogWGData.Instance:GetCurBaseAttrList(data.base_attr_seq, qiwu_level, add_value)		-- 当前索引基础属性
	if not IsEmptyTable(qh_attr_list) then
		for k, v in pairs(self.qh_attr_list) do
			v:SetData(qh_attr_list[k])
		end
	end

	self.node_list.qh_special_attr:SetActive(false)
	local special_attr_seq = data.special_attr_seq
	local qiwu_xl_level = cur_server_info.zhuling_level
	local cur_spe_attr = StrangeCatalogWGData.Instance:GetCurSpecialAttr(special_attr_seq, qiwu_xl_level)
	if not IsEmptyTable(cur_spe_attr) then
		self.node_list.qh_special_attr:SetActive(true)

		local effective_max_Level = cur_spe_attr.effect_max_level
		local effective_txt_color = qiwu_level > effective_max_Level and COLOR3B.C10 or COLOR3B.C8
		local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrId(cur_spe_attr.attr_id1)
		self.node_list.qh_resume.text.text = string.format(Language.StrangeCatalog.SpecialText, cur_spe_attr.attr_value1 .. attr_name)
		self.node_list.qh_effective_level.text.text = string.format(Language.StrangeCatalog.EffectiveLevel, effective_txt_color, qiwu_level, effective_max_Level)

		local effective_Level = 0
		if qiwu_level > effective_max_Level then
			effective_Level = effective_max_Level
		else
			effective_Level = qiwu_level
		end
		local summation = cur_spe_attr.attr_value1 * math.floor(effective_Level / 10)
		self.node_list.qh_summation_text.text.text = string.format(Language.StrangeCatalog.SummationText, summation)
	end

	local level_attr_cfg = StrangeCatalogWGData.Instance:GetLevelAttrCfg()
	local max_level = level_attr_cfg[#level_attr_cfg].max_level
	self.node_list.not_full_level:SetActive(max_level > qiwu_level)
	self.node_list.qh_full_level:SetActive(qiwu_level >= max_level)
	if qiwu_level >= max_level then
		return
	end

	self.qh_item_pos:SetData({item_id = data.active_item_id})
	local qh_remind = StrangeCatalogWGData.Instance:QiangHuaRemind(data.seq)
	self.node_list.qh_remind:SetActive(qh_remind)

	local num = StrangeCatalogWGData.Instance:GetItemNum(data.active_item_id)
	local need_num = 1																										-- 后端默认为1
	local num_txt_color = num < need_num and COLOR3B.C10 or COLOR3B.C8
	local str = string.format(Language.StrangeCatalog.NeedNum, num_txt_color, num, need_num)
	self.qh_item_pos:SetRightBottomTextVisible(true)
	self.qh_item_pos:SetRightBottomText(str)
	--self.node_list.qh_item_text.text.text = string.format(Language.StrangeCatalog.EffectiveLevel, num_txt_color, need_num, num)

	local qh_btn_text = qiwu_level == 0 and Language.StrangeCatalog.ActivateNow or Language.StrangeCatalog.UpgradeNow
	self.node_list.qh_btn_text.text.text = qh_btn_text
end

------------------------------洗练面板  部分-------------------------------------
function StrangeCatalogView:FlushXLPart()
	local data = self:GetCurSeqInfo()
	if IsEmptyTable(data) then
		self.node_list.xilian_has_data:SetActive(false)
		self.node_list.xilian_nodata:SetActive(true)
		return
	end

	local cur_server_info = StrangeCatalogWGData.Instance:GetQWSingleServerInfo(data.seq)
	if IsEmptyTable(cur_server_info) then
		self.node_list.xilian_has_data:SetActive(false)
		self.node_list.xilian_nodata:SetActive(true)
		return
	end

	self.node_list.xilian_has_data:SetActive(true)
	self.node_list.xilian_nodata:SetActive(false)

	-- local item_id = data.active_item_id
	-- local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	-- self.node_list.xl_item_name.text.text = item_cfg.name

	local qiwu_xl_level = cur_server_info.zhuling_level
	local qua_name = Language.ShiTianSuit.InfuseSoulQuality[qiwu_xl_level]
	self.node_list["qiwu_quality"].text.text = string.format(Language.StrangeCatalog.QuaLevel, qua_name)

	local cur_exp = cur_server_info.zhuling_exp
	self.node_list.degree_text.text.text = string.format(Language.StrangeCatalog.DegreeText, cur_exp)
	self.node_list.qiwu_degree_slider.slider.value = cur_exp

	self.node_list.xl_special_attr:SetActive(false)
	local special_attr_seq = data.special_attr_seq
	local cur_spe_attr = StrangeCatalogWGData.Instance:GetCurSpecialAttr(special_attr_seq, qiwu_xl_level)
	if not IsEmptyTable(cur_spe_attr) then
		self.node_list.xl_special_attr:SetActive(true)

		local qiwu_level = cur_server_info.level
		local effective_max_Level = cur_spe_attr.effect_max_level
		local effective_txt_color = qiwu_level > effective_max_Level and COLOR3B.C10 or COLOR3B.C8
		local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrId(cur_spe_attr.attr_id1)
		self.node_list.xl_resume.text.text = string.format(Language.StrangeCatalog.SpecialText, cur_spe_attr.attr_value1 .. attr_name)
		self.node_list.xl_effective_level.text.text = string.format(Language.StrangeCatalog.EffectiveLevel, effective_txt_color, qiwu_level, effective_max_Level)

		local effective_Level = 0
		if qiwu_level > effective_max_Level then
			effective_Level = effective_max_Level
		else
			effective_Level = qiwu_level
		end
		local summation = cur_spe_attr.attr_value1 * math.floor(effective_Level / 10)
		self.node_list.xl_summation_text.text.text = string.format(Language.StrangeCatalog.SummationText, summation)
	end

	local spe_seq_cfg = StrangeCatalogWGData.Instance:GetSpecialBySeq(special_attr_seq)
	if not spe_seq_cfg then
		return
	end

	local max_zhuling_level = #spe_seq_cfg
	self.node_list.unlock_all:SetActive(qiwu_xl_level >= max_zhuling_level)
	--self.node_list.full_xl_level:SetActive(qiwu_xl_level >= max_zhuling_level)
	--self.node_list.degree:SetActive(qiwu_xl_level < max_zhuling_level)
	self.node_list.not_unlock:SetActive(qiwu_xl_level < max_zhuling_level)

	if qiwu_xl_level >= max_zhuling_level then
		self.node_list.degree_text.text.text = string.format(Language.StrangeCatalog.DegreeText, 100)
		self.node_list.qiwu_degree_slider.slider.value = 100
		return
	end

	local qiwu_qh_level = cur_server_info.level
	local next_zhuling_cfg = StrangeCatalogWGData.Instance:GetZhuLingSingleCfg(qiwu_xl_level + 1)
	if not next_zhuling_cfg then
		return
	end

	local need_level = next_zhuling_cfg.need_level or 0
	local need_color = qiwu_qh_level >= need_level and COLOR3B.C8 or COLOR3B.C10
	self.node_list.need_level.text.text = string.format(Language.StrangeCatalog.NeedLevel, need_color, qiwu_qh_level, need_level)

	self.xl_item_pos:SetData({item_id = next_zhuling_cfg.consume_item_id})

	local num = ItemWGData.Instance:GetItemNumInBagById(next_zhuling_cfg.consume_item_id)
	local need_num = next_zhuling_cfg.consume_item_num
	local num_txt_color = num >= need_num and COLOR3B.C8 or COLOR3B.C10
	local str = string.format(Language.StrangeCatalog.NeedNum, num_txt_color, num, need_num)
	self.xl_item_pos:SetRightBottomTextVisible(true)
	self.xl_item_pos:SetRightBottomText(str)
	--self.node_list.xl_item_text.text.text = string.format(Language.StrangeCatalog.EffectiveLevel, num_txt_color, num, need_num)

	local xl_remind = StrangeCatalogWGData.Instance:ZhuLingRemind(data.seq)
	self.node_list.xl_remind:SetActive(xl_remind)
end

-- 显示强化特效
function StrangeCatalogView:ShowQiangHuaEffect()
	local data = self:GetCurSeqInfo()
	if not data then
		return
	end

	local num = StrangeCatalogWGData.Instance:GetItemNum(data.active_item_id)
	local need_num = 1
	local has_enough_item = num >= need_num
	if not has_enough_item then
		return
	end

	local cur_sevrer_info = StrangeCatalogWGData.Instance:GetQWSingleServerInfo(data.seq)
	local level = cur_sevrer_info.level
	if level == 0 then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo, is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["ph_effect"]})
	elseif level > 0 then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji, is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["ph_effect"]})
	end
end

-- 羁绊
function StrangeCatalogView:OnClickTrammel()
	StrangeCatalogWGCtrl.Instance:OpenJiBanView()
end

-- 分解
function StrangeCatalogView:OnClickReslove()
	StrangeCatalogWGCtrl.Instance:OpenResloveView()
end

-- 强化
function StrangeCatalogView:OnClickQiangHua()
	local data = self:GetCurSeqInfo()
	if IsEmptyTable(data) then
		return
	end

	local artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(data.big_type)
	if not artifact_data or artifact_data.level <= 0 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.StrangeCatalog.ArtifactNotActive)
		return
	end

	local cur_sevrer_info = StrangeCatalogWGData.Instance:GetQWSingleServerInfo(data.seq)
	local level = cur_sevrer_info.level
	local level_attr_cfg = StrangeCatalogWGData.Instance:GetLevelAttrCfg()
	local max_level = level_attr_cfg[#level_attr_cfg].max_level

	if max_level <= level then
		return
	end

	local item_id = data.active_item_id
	local num = StrangeCatalogWGData.Instance:GetItemNum(item_id)

	if num > 0 then
		StrangeCatalogWGCtrl.Instance:OnHandBookOperate(HANDBOOK_OPERA_TYPE.UPGRADE_LEVEL, data.seq)
	else
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
	end
end

-- 洗练
function StrangeCatalogView:OnClickXiLian(one_key, is_auto)
	local data = self:GetCurSeqInfo()
	if IsEmptyTable(data) then
		return
	end

	local artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(data.big_type)
	if not artifact_data or artifact_data.level <= 0 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.StrangeCatalog.ArtifactNotActive)
		return
	end

	local cur_sevrer_info = StrangeCatalogWGData.Instance:GetQWSingleServerInfo(data.seq)
	local qiwu_xl_level = cur_sevrer_info.zhuling_level

	local special_attr_seq = data.special_attr_seq
	local spe_seq_cfg = StrangeCatalogWGData.Instance:GetSpecialBySeq(special_attr_seq)
	if not spe_seq_cfg then
		return
	end

	local max_zhuling_level = #spe_seq_cfg
	if max_zhuling_level <= qiwu_xl_level then
		self:StopLevelOperator()
		return
	end

	if self:IsAutoXiLian() and not is_auto then
		TipWGCtrl.Instance:ShowSystemMsg(Language.StrangeCatalog.LevelBtnTip)
		return
	end

	local next_zhuling_cfg = StrangeCatalogWGData.Instance:GetZhuLingSingleCfg(qiwu_xl_level + 1)
	if not next_zhuling_cfg then
		self:StopLevelOperator()
		return
	end

	local qiwu_qh_level = cur_sevrer_info.level
	if qiwu_qh_level < next_zhuling_cfg.need_level then
		self:StopLevelOperator()
		TipWGCtrl.Instance:ShowSystemMsg(Language.StrangeCatalog.Insufficient_level)
		return
	end

	local num = ItemWGData.Instance:GetItemNumInBagById(next_zhuling_cfg.consume_item_id)
	local need_num = next_zhuling_cfg.consume_item_num
	if num >= need_num then
		StrangeCatalogWGCtrl.Instance:OnHandBookOperate(HANDBOOK_OPERA_TYPE.ZHULING, data.seq)
		if is_auto then
			self:SetLevelButtonEnabled(true)
		end
	else
		self:StopLevelOperator()
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = next_zhuling_cfg.consume_item_id})
	end
end

-- 自动洗练
function StrangeCatalogView:OnClickAutoXL()
	if self:IsAutoXiLian() then --正在自动升级则取消强化
		self:StopLevelOperator()
		return
	end
	self:OnClickXiLian(1, true)
end

--取消自动升级倒计时
function StrangeCatalogView:CancelAutoLevelTimer()
	if nil ~= self.auto_level_timer_quest then
		GlobalTimerQuest:CancelQuest(self.auto_level_timer_quest)
		self.auto_level_timer_quest = nil
	end
end

-- 自动升级操作
function StrangeCatalogView:AutoUpLevelUpOnce()
	self:CancelAutoLevelTimer()
	local data = self:GetCurSeqInfo()
	if IsEmptyTable(data) then
		return
	end

	local cur_sevrer_info = StrangeCatalogWGData.Instance:GetQWSingleServerInfo(data.seq)
	if IsEmptyTable(cur_sevrer_info) then
		return
	end

	local qiwu_xl_level = cur_sevrer_info.zhuling_level
	local next_zhuling_cfg = StrangeCatalogWGData.Instance:GetZhuLingSingleCfg(qiwu_xl_level + 1)
	if not next_zhuling_cfg then
		return
	end

	if self:IsAutoXiLian() then
		self.auto_level_timer_quest = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.OnClickXiLian, self, 1, true), LEVEL_DELT_TIME)
	end
end

-- 使用特效
function StrangeCatalogView:PlayUseEffect(effect_name)
	TipWGCtrl.Instance:ShowEffect({effect_type = effect_name, is_success = true, pos = Vector2(0, 0),
						parent_node = self.node_list["ph_effect"]})
end

function StrangeCatalogView:IsAutoXiLian()
	return self.is_auto_level
end

function StrangeCatalogView:StopLevelOperator()
	self:SetLevelButtonEnabled(false)
end

-- 设置强化按钮是否可用
function StrangeCatalogView:SetLevelButtonEnabled(enabled)
	self.is_auto_level = enabled
	self:SetAutoLevelBtnNameStr(not enabled and Language.StrangeCatalog.AutoXiLian or Language.StrangeCatalog.StopAutoXiLian)
	if false == enabled then
		self:CancelAutoLevelTimer()
	end
end

function StrangeCatalogView:SetAutoLevelBtnNameStr(strength_btn_str)
	if self.node_list["xl_auto_text"] then
		self.node_list["xl_auto_text"].text.text = strength_btn_str
	end
end

function StrangeCatalogView:OnClickHideRightBtn()
	self.node_list["btn_hide_right_mask"]:SetActive(false)
	self.node_list["right_panel"]:SetActive(false)
end

function StrangeCatalogView:OnClickShowRightBtn()
	self.node_list["btn_hide_right_mask"]:SetActive(true)
	self.node_list["right_panel"]:SetActive(true)
end

function StrangeCatalogView:OnClickJumpBtn()
	ViewManager.Instance:Open(GuideModuleName.ArtifactTravelView, TabIndex.artifact_affection, nil, {to_ui_param = self.big_index})
end

-- 红点变化回调
function StrangeCatalogView:OtherRemindCallBack(remind_name, num)
    if remind_name == RemindName.StrangeCatalogJiBan then
        self.node_list["jiban_remind"]:SetActive(num > 0)
    end
end

-- 红点变化回调
function StrangeCatalogView:ShowRightPartView()
    self:OnClickShowRightBtn()
end

---------------------QwBigListRender---------------------
QwBigListRender = QwBigListRender or BaseClass(BaseRender)
function QwBigListRender:OnFlush()
	if not self.data then
		return
	end
	local mingzi = self.data.name
	self.node_list.shj_qiwu_name.text.text = mingzi
	self.node_list.shj_qiwu_text.text.text = mingzi
	self.node_list.select_qiwu_bg:SetActive(self.is_select)

	local is_remind = self.data.is_remind
	self.node_list.is_big_remind:SetActive(is_remind)
end

function QwBigListRender:OnSelectChange(is_select)
	if self.node_list.select_qiwu_bg then
		self.node_list.select_qiwu_bg:SetActive(is_select)
	end
end

-----------------SubTypeCell-----------------
SubTypeCell = SubTypeCell or BaseClass(BaseRender)
function SubTypeCell:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local name = self.data.name
	local is_remind = self.data.is_remind
	self.node_list.normal_lbl.text.text = name
	self.node_list.hight_lbl.text.text = name
	self.node_list.title_remind:SetActive(is_remind)
end

function SubTypeCell:OnSelectChange(is_select)
	self.node_list.hight_bg:SetActive(is_select)
end

---------------------------------------
-- 左侧列表Item
---------------------------------------
ArtifactQWCell = ArtifactQWCell or BaseClass(BaseRender)

function ArtifactQWCell:OnFlush()
	if not self.data then
		return
	end

	local bundle, asset = ResPath.GetStrangeCatalogImg("a3_sxhg_sx_tx_" .. self.data.big_type)
	self.node_list["img_icon"].image:LoadSprite(bundle, asset)

	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.data.big_type)
	local is_act = cur_artifact_data.level > 0
	self.node_list["img_locked"]:SetActive(not is_act)

	local artifact_cfg = ArtifactWGData.Instance:GetArtifactCfgBySeq(self.data.big_type)
	local artifact_name = artifact_cfg and artifact_cfg.name or ""
	self.node_list["txt_name_nor"].text.text = artifact_name
	self.node_list["txt_name_hl"].text.text = artifact_name

	local remind = StrangeCatalogWGData.Instance:GetBigTypeRemind(self.data.big_type)
	self.node_list["remind"]:SetActive(remind)
end

function ArtifactQWCell:OnSelectChange(is_select)
	self.node_list["normal"]:SetActive(not is_select)
	self.node_list["select_hl"]:SetActive(is_select)
	self.node_list["name_bg_nor"]:SetActive(not is_select)
	self.node_list["name_bg_hl"]:SetActive(is_select)
	local scale = is_select and 1 or 0.8
	self.view.transform:SetLocalScale(scale, scale, scale)
end


-----------------SingleQiRender-----------------
SingleQiRender = SingleQiRender or BaseClass(BaseRender)

function SingleQiRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_special"], BindTool.Bind(self.OnClickSpecialBtn, self))
end

function SingleQiRender:ReleaseCallBack()
	self.click_call_back = nil
end

function SingleQiRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self:FlushQWRemind()
	local item_id = self.data.active_item_id
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)

	if not IsEmptyTable(item_cfg) then
		--[[
		local bundle, asset = ResPath.GetStrangeCatalogImg("a2_yw_bg_" .. item_cfg.color)
		self.node_list.cell_bg.image:LoadSprite(bundle, asset, function()
			self.node_list.cell_bg.image:SetNativeSize()
		end)
		]]
	end

	self.node_list.curiosity_name.text.text = item_cfg.name

	local cur_server_info = StrangeCatalogWGData.Instance:GetQWSingleServerInfo(self.data.seq)
	local level = cur_server_info.level or 0
	local qiwu_xl_level = cur_server_info.zhuling_level or 0
	local num = StrangeCatalogWGData.Instance:GetItemNum(item_id)
	self.node_list["img_locked"]:SetActive(level <= 0)
	-- self.node_list.level_desc:SetActive(level > 0)
	-- self.node_list.activate_btn:SetActive(num > 0 and level <= 0)
	self.node_list.degree_bg:SetActive(qiwu_xl_level > 0)

	local image_name = level <= 0 and (self.data.pic_seq .. "_d") or self.data.pic_seq
	local bundle1, asset1 = ResPath.GetNoPackPNG(image_name)
	self.node_list["curiosity_item"].image:LoadSprite(bundle1, asset1, function()
		self.node_list["curiosity_item"].image:SetNativeSize()
	end)

	-- if level <= 0 then
	-- 	return
	-- end

	-- local effect_name = BaseCell_Ui_Circle_Effect[item_cfg.color]			-- 后续修改特效
	-- if effect_name then
	-- 	local bundle1, assert1 = ResPath.GetWuPinKuangEffectUi(effect_name)
	-- 	self.node_list["effect_root"]:ChangeAsset(bundle1, assert1)
	-- 	self.node_list["effect_root"]:SetActive(true)
	-- else
	-- 	self.node_list["effect_root"]:SetActive(false)
	-- end

	local level_str = level <= 0 and Language.StrangeCatalog.Locked or string.format(Language.StrangeCatalog.level2, level)
	self.node_list.level_desc.text.text = level_str

	local degree_desc = Language.StrangeCatalog.degree_text[qiwu_xl_level]
	self.node_list.degree_text.text.text = degree_desc
end

function SingleQiRender:FlushQWRemind()
	local is_remind = self.data.is_remind
	self.node_list.remind:SetActive(is_remind)
end

function SingleQiRender:OnSelectChange(is_select)
	-- self.node_list.bg:SetActive(not is_select)
	-- self.node_list.hight_light:SetActive(is_select)
	self.node_list["btn_special"]:SetActive(is_select)
end

function SingleQiRender:OnClickSpecialBtn()
	StrangeCatalogWGCtrl.Instance:ShowRightPartView()
end