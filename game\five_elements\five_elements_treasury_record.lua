FiveElementsTreasuryRecordView = FiveElementsTreasuryRecordView or BaseClass(SafeBaseView)

function FiveElementsTreasuryRecordView:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(true, true)

    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {sizeDelta = Vector2(932, 590)})
    self:AddViewResource(0, "uis/view/five_elements_ui_prefab", "five_elements_treasury_record")
end

function FiveElementsTreasuryRecordView:ReleaseCallBack()
    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end
end

function FiveElementsTreasuryRecordView:LoadCallBack()
    self.toggle_index = 1

    self.node_list.txt_tip2.text.text = Language.SiXiangCall.RecordTip

    self.node_list.btn_all.toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self))
    --self.node_list.btn_self.toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self))

    self.record_list = AsyncListView.New(FiveElementsTreasuryRecordRender, self.node_list["role_list"])
    self.node_list["btn_all"].toggle.isOn = true
end

function FiveElementsTreasuryRecordView:OpenCallBack()
    FiveElementsWGCtrl.Instance:SendSFiveElementsRequest(FIVE_ELEMENTS_OPERATE_TYPE.FIVE_ELEMENTS_OPERATE_TYPE_DRAW_RECORD_INFO)
end

function FiveElementsTreasuryRecordView:OnClickSwitch(state)
    self.toggle_index = state
    -- if state == SiXiangRecordType.SELF then
    --     --SiXiangCallWGCtrl.Instance:SendMachineOp(MACHINE_OP_TYPE.INFO)
    -- end
    self:FlushRecordList()
end

function FiveElementsTreasuryRecordView:OnFlush(param_t)
    self:FlushRecordList()
    self:FlushTitle()
end

function FiveElementsTreasuryRecordView:FlushRecordList()
    local data_list = FiveElementsWGData.Instance:GetServerloglist()
    local is_show_list = not IsEmptyTable(data_list)
    if is_show_list then
        self.record_list:SetDataList(data_list)
    end
    self.node_list["role_list"]:SetActive(is_show_list)
    self.node_list["no_invite"]:SetActive(not is_show_list)
end

function FiveElementsTreasuryRecordView:FlushTitle()
    self.node_list.title_view_name.text.text = Language.FiveElementsTreasury.RecordTitle
end

-------------------------------------------------------------------------------------------------------

FiveElementsTreasuryRecordRender = FiveElementsTreasuryRecordRender or BaseClass(BaseRender)

function FiveElementsTreasuryRecordRender:LoadCallBack()
    self.node_list["txt_btn"].button:AddClickListener(BindTool.Bind(self.OnclickItem, self))
end

function FiveElementsTreasuryRecordRender:OnFlush()
    local data = self:GetData()
    local index = self:GetIndex()

    local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
    if not item_cfg then
        return
    end

    -- local color = mark and ITEM_COLOR[item_cfg.color] or ITEM_COLOR[item_cfg.color]
    local color = ITEM_COLOR[item_cfg.color]
    local role_name = data.name or RoleWGData.Instance:GetAttr("name")
    local str1 = string.format(Language.FiveElementsTreasury.TxtRecord3, role_name)
    local name = string.format(Language.FiveElementsTreasury.TxtRecord1_2, color, item_cfg.name)
    local num = string.format(Language.FiveElementsTreasury.TxtRecord1_3, color, data.num)

    self.node_list["desc"].text.text = str1
    self.node_list["txt_btn"].text.text = name
    self.node_list["num"].text.text = num
    self.node_list["time"].text.text = os.date("%m-%d   %X", data.record_time)
end

function FiveElementsTreasuryRecordRender:OnclickItem()
    local data = self:GetData()
    if data == nil then
        return
    end

    TipWGCtrl.Instance:OpenItem({item_id = data.item_id})
end