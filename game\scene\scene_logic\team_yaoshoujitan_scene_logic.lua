TeamYaoShouJiTanSceneLogic = TeamYaoShouJiTanSceneLogic or BaseClass(CommonFbLogic)

function TeamYaoShouJiTanSceneLogic:__init()
	self.boss_enter_handler = GlobalEventSystem:Bind(ObjectEventType.VISIBLE_OBJ_ENTER_MONSTER, BindTool.Bind(self.OnBossEnterVisible, self))
end

function TeamYaoShouJiTanSceneLogic:__delete()
	GlobalEventSystem:UnBind(self.boss_enter_handler)
end

function TeamYaoShouJiTanSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	
	DailyWGCtrl.Instance:Close()

	FuBenWGCtrl.Instance:OpenTaskFollow()

	-- XuiBaseView.CloseAllView()
end

function TeamYaoShouJiTanSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
end

function TeamYaoShouJiTanSceneLogic:Out()
	CommonFbLogic.Out(self)
	FuBenWGCtrl.Instance:CloseTaskFollow()
end

function TeamYaoShouJiTanSceneLogic:OnBossEnterVisible(monster_vo)
	local scene_info = FuBenWGData.Instance:GetTeamFbSceneLogicInfo()
	if nil == scene_info then
		return
	end
	local monster_id = FuBenWGData.Instance:GetYsjtTeamFbMonsterId(scene_info.mode)
	if monster_vo.monster_id == monster_id then
		local monster_obj = Scene.Instance:GetObjectByObjId(monster_vo.obj_id)
		local effect_id = FuBenWGData.GetJiTanAttrEffectId(scene_info.boss_attr_type)
		local anim_path, anim_name = ResPath.GetEffectAnimPath(effect_id)
		monster_obj:GetModel():ChangeLayerResFrameAnim(
			GRQ_SCENE_OBJ, InnerLayerType.AttrEffect, anim_path, anim_name, false, FrameTime.FaZhen, false, COMMON_CONSTS.MAX_LOOPS, false, 
			0, 0)
	end
end
