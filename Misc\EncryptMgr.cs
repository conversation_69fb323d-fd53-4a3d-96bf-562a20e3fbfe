﻿using LuaInterface;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using UnityEngine;
using System.Collections;

public class EncryptMgr
{
    //读取缓存资源用加密key，游戏登录后由php query传递修改，决定当前的加密方式
    private static byte[] encryptKey;
    private static string base64Key = "";

    //读取整包资源用加密key，外部不可修改
    private static string streamingEncryptString = "Jgz~";
    private static byte[] streamingEncryptKey = new byte[] { 74, 103, 122, 126 };
    private static string streamingBase64Key = "";

    public static string Base64KeyCode
    {
        get { return "hUg7T8Ht458Mgh1Rvf0Ep"; }
    }

    //默认：ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=
    //益乐il1使用key1 MNOPQRSTUVWXYZABCDEFGHIJKLnopqrstuvwxyzabcdefghijklm5678901234+/=
    //益乐il1使用key2 PQRSHIJKLMNOTUVABlmnopqrsCDEFGWXvwxyz01234YZabcdefghijktu56789+/=
    //益乐il1使用key3 EFGHABCDIJKLMNOPQRSTUVWXYZfghijabcdeklmnopqrstuvwxyz4560123789+/=
    //越南ia1使用key1 LMNOPQRSTABCDEFGHIJKUVWXYZabclmnopqrstudefghijkv01234wxyz56789+/=
    //越南ia1使用key2 PQRSLMNOTABCDEFGHIJKUVWXYZabclmnopqrstudefghijkv56789wxyz01234+/=
    //A1BT-img使用Key1：ABOPQRSJKLMNXYZabcdefghijklTUVWCDEFGHImnopq2345rstuvwxyz016789+/=
    //A1BT-imh使用Key1：HIJKLMNOPQRSTUABCDEFGVWXfghijklmnopYZabcdeqrs12345tuvwxyz06789+/=
    static string DefaultBase64Code = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
    static string Base64Code = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
    static char[] Base64CodeChar;

    /// <summary>
    /// 整包资源是否使用base64混淆（注意：打包混淆资源时需要手动改为true）
    /// </summary>
    private static bool isBase64EncryptStreamingAsset = false;
    /// <summary>
    /// 缓存资源是否使用base64混淆，只有当isBase64EncryptAsset是true才可以为true（注意：打包混淆资源时需要手动改为true）
    /// </summary>
    private static bool isBase64EncryptCacheAsset = false;
    /// <summary>
    /// 是否使用偏移加密资源
    /// </summary>
    private static bool isEncryptAsset = false;


    public static void InitEncryptKey()
    {
#if UNITY_ANDROID || UNITY_IOS
        isEncryptAsset = true;
        encryptKey = GetEncryptKey();
#endif
    }

    public static byte[] GetEncryptKey()
    {
#if UNITY_ANDROID || UNITY_IOS
        if (null == encryptKey)
        {
#if UNITY_EDITOR
            InitABEncryptKey();
#else
            encryptKey = GetStreamingEncryptKey();
            ReadCacheEncryptKeyFile();
#endif
        }
#endif
        return encryptKey;
    }

    public static byte[] GetStreamingEncryptKey()
    {
        return streamingEncryptKey;
    }

    public static string GetStreamingEncryptyString()
    {
        return streamingEncryptString;
    }

    public static void SetEncryptKey(string encryptKeyStr)
    {
        if (!string.IsNullOrEmpty(encryptKeyStr))
        {
            encryptKey = CalcEncryptKey(encryptKeyStr);
        }
    }

    public static void SetBase64EncryptKey(string base64EncryptKeyStr)
    {
        if (string.IsNullOrEmpty(base64EncryptKeyStr))
        {
            Base64Code = DefaultBase64Code;
        }
        else
        {
            Base64Code = base64EncryptKeyStr;
        }
        Base64CodeChar = Base64Code.ToCharArray();
    }

    private static bool ReadCacheEncryptKeyFile()
    {
        string encrypyKeyCacheFilePath = string.Format("{0}/e.txt", Application.persistentDataPath);
        if (!File.Exists(encrypyKeyCacheFilePath))
        {
            //Debug.LogError("没有发现 CacheEncryptKeyFile，直接使用整包加密key, encrypyKeyCacheFilePath = " + encrypyKeyCacheFilePath);
            return false;
        }

        using (StreamReader streamReader = new StreamReader(encrypyKeyCacheFilePath))
        {
            string line = streamReader.ReadLine();
            if (string.IsNullOrEmpty(line))
            {
                //Debug.LogError("没有发现 CacheEncryptKeyFile，直接使用整包加密key, encrypyKeyCacheFilePath = " + encrypyKeyCacheFilePath);
                return false;
            }
            encryptKey = CalcEncryptKey(line);
            string line2 = streamReader.ReadLine();
            base64Key = string.IsNullOrEmpty(line2) ? "" : line2;
            //Debug.LogErrorFormat("ReadCacheEncryptKeyFile encryptKey:{0}, base64Key:{1}", encryptKey, base64Key);
        }
        return true;
    }

    public static void SaveCacheEncryptKeyFile()
    {
        string encrypyKeyCacheFilePath = string.Format("{0}/e.txt", Application.persistentDataPath);
        try
        {
            string e = "";
            for (int i = 0; i < encryptKey.Length; i++)
            {
                string s = Convert.ToString(encryptKey[i]);
                e = i == 0 ? s : string.Format("{0},{1}", e, s);
            }

            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.AppendLine(e);
            stringBuilder.AppendLine(base64Key);
            File.WriteAllText(encrypyKeyCacheFilePath, stringBuilder.ToString());
        }
        catch (Exception ex)
        {
            Debug.LogError(ex);
        }
        //Debug.LogErrorFormat("SaveCacheEncryptKeyFile encrypyKeyCacheFilePath:{0}, IsExist:{1}", encrypyKeyCacheFilePath, File.Exists(encrypyKeyCacheFilePath));
    }

    [NoToLua]
    public static void ClearEncryptKey()
    {
        encryptKey = null;
        base64Key = "";
    }

    [NoToLua]
    public static void RefreshEncryptKey()
    {
        ClearEncryptKey();
        InitABEncryptKey();
    }

    [NoToLua]
    public static void InitABEncryptKey()
    {
#if UNITY_EDITOR
        encryptKey = null;
        base64Key = "";
        string path = Path.Combine(Application.dataPath, "Game/Deploy/Install/ABEncrypt_Key.txt");
        string[] encryptKeyListLine = File.ReadAllLines(path);
        string curPackageName = "";
        string encryptKeyStr = "";
        byte[] curKeyList = { };
        if (encryptKeyListLine.Length > 2)
        {
            string[] defaultStrs = encryptKeyListLine[1].Split('|');
            curPackageName = defaultStrs[1];
            for (int i = 2; i < encryptKeyListLine.Length; i++)
            {
                string[] s = encryptKeyListLine[i].Split('|');

                if (curPackageName == s[0])
                {
                    string[] keys = s[1].Split(',');
                    encryptKeyStr = s[1];
                    encryptKey = new byte[keys.Length];
                    for (int j = 0; j < keys.Length; j++)
                    {
                        encryptKey[j] = (byte)int.Parse(keys[j]);
                    }
                    base64Key = s[2];
                    break;
                }
            }
        }
        //Debug.LogErrorFormat("偏移加密key:{0}, base64加密key:{1}", encryptKeyStr, base64Key);
#endif
    }

    public static int GetEncryptKeyLength()
    {
#if UNITY_ANDROID && UNITY_EDITOR || UNITY_IOS && UNITY_EDITOR
        return GetEncryptKey().Length;
#endif
        return (encryptKey != null && encryptKey.Length > 0) ? encryptKey.Length : 0;
    }

    public static int GetEncryptKeyLength(string assetbundleName)
    {
        string base64Code = EncryptMgr.XORAndBase64Encode(assetbundleName, EncryptMgr.Base64KeyCode);
        byte[] key = string.IsNullOrEmpty(assetbundleName) ? EncryptMgr.GetEncryptKey() : GetAscii(base64Code);
        return key.Length * 3;
    }

    public static byte[] GetAscii(string s)
    {
        byte[] ascii_s = Encoding.ASCII.GetBytes(s);
        byte[] base64_b = Encoding.ASCII.GetBytes(Convert.ToBase64String(ascii_s));
        return base64_b;
    }

    public static int GetStreamingEncryptKeyLength()
    {
        return GetStreamingEncryptKey().Length;
    }

    public static byte[] CalcEncryptKey(string key)
    {
        string[] keyAry = key.Split(',');
        var encryptKey = new byte[keyAry.Length];
        for (int i = 0; i < keyAry.Length; i++)
        {
            encryptKey[i] = Convert.ToByte(keyAry[i]);
        }

        return encryptKey;
    }

    /// <summary>
    /// 设置客户端AB缓存是否使用base64混淆方式读取，只有当整包资源也是base64混淆时才能生效
    /// </summary>
    /// <param name="_isBase64EncryptCacheAsset"></param>
    public static void IsBase64EncryptCacheAsset(bool _isBase64EncryptCacheAsset)
    {
        isBase64EncryptCacheAsset = _isBase64EncryptCacheAsset && isBase64EncryptStreamingAsset;
    }

    public static bool GetIsBase64EncryptCacheAsset()
    {
        return isBase64EncryptCacheAsset;
    }

    public static bool GetIsBase64EncryptStreamingAsset()
    {
        return isBase64EncryptStreamingAsset;
    }

    [NoToLua]
    public static void OpenBase64EncryptInEditorMode(bool _isOpen)
    {
        isBase64EncryptCacheAsset = _isOpen;
        isBase64EncryptStreamingAsset = _isOpen;
    }

    public static bool IsEncryptAsset()
    {
        return isEncryptAsset;
    }

    private static Dictionary<string, string> encrpytStreamingPathBase64MapO2N = new Dictionary<string, string>();
    private static Dictionary<string, string> encrpytStreamingPathBase64SetN2O = new Dictionary<string, string>();

    public static string GetStreamingEncryptPath(string path)
    {
        if (!isBase64EncryptStreamingAsset)
            return path;

#if UNITY_ANDROID || UNITY_IOS
        return GetStreamingEncryptPath2Base64(path);
#else
        return path;
#endif
    }

    [NoToLua]
    public static string GetStreamingEncryptPath2Base64Editor(string path)
    {
        return GetStreamingEncryptPath2Base64(path);
    }

    public static string GetStreamingEncryptPath2Base64(string path)
    {
        if (!isBase64EncryptStreamingAsset)
            return path;
#if !UNITY_EDITOR
        if (encrpytStreamingPathBase64MapO2N.ContainsKey(path))
        {
            return encrpytStreamingPathBase64MapO2N[path];
        }
#endif

        //过滤hash值，hash值不做混淆处理
        int hashIndexOf = path.IndexOf("-");
        bool isHasHash = path.Contains("-") && IsNumberStr(path.Substring(hashIndexOf + 1, path.Length - hashIndexOf - 1));
        string hashStr = isHasHash ? path.Substring(hashIndexOf, path.Length - hashIndexOf) : "";
        path = isHasHash ? path.Replace(hashStr, "") : path;

        //过滤文件名后缀
        bool isFile = path.Contains(".");
        int fileIndexOf = path.IndexOf(".");
        string fileTypeStr = isFile ? path.Substring(fileIndexOf, path.Length - fileIndexOf) : "";
        path = isFile ? path.Replace(fileTypeStr, "") : path;
        //Debug.LogError(path);
        string[] ary = path.Split('/');
        StringBuilder builder = new StringBuilder();
        for (int m = 0; m < ary.Length; m++)
        {
            string name = ary[m];
            if (string.IsNullOrEmpty(name))
            {
                continue;
            }

            if (m != 0)
            {
                builder.Append("/");
            }

            if (name == "BundleCache")
            {
                byte[] bytes = Encoding.Default.GetBytes(name);
                string newName = Convert.ToBase64String(bytes);
                builder.Append(name);
            }
            else
            {
                builder.Append(ConvertContentToBase64(name));
            }
        }

        string newPath = builder.ToString() + (isHasHash ? hashStr : "");
        newPath = newPath + (isFile ? fileTypeStr : "");

#if !UNITY_EDITOR
        if (!encrpytStreamingPathBase64MapO2N.ContainsKey(path))
        {
            encrpytStreamingPathBase64MapO2N.Add(path, newPath);
        }

        if (!encrpytStreamingPathBase64SetN2O.ContainsKey(newPath))
        {
            encrpytStreamingPathBase64SetN2O.Add(newPath, path);
        }
#endif
        return newPath;
    }

    [NoToLua]
    public static string GetStreamingDecryptPath2Base64(string encryptPath)
    {
        if (!isBase64EncryptStreamingAsset)
            return encryptPath;

        //以下代码不要删除
#if !UNITY_EDITOR
        if (encrpytStreamingPathBase64SetN2O.ContainsKey(encryptPath))
        {
            return encrpytStreamingPathBase64SetN2O[encryptPath];
        }
#endif
        string originPath = "";
        try
        {
            //过滤hash值，hash值不做混淆处理
            int hashIndexOf = encryptPath.IndexOf("-");
            bool isHasHash = encryptPath.Contains("-") && IsNumberStr(encryptPath.Substring(hashIndexOf + 1, encryptPath.Length - hashIndexOf - 1));
            string hashStr = isHasHash ? encryptPath.Substring(hashIndexOf, encryptPath.Length - hashIndexOf) : "";
            encryptPath = isHasHash ? encryptPath.Replace(hashStr, "") : encryptPath;

            //过滤文件名后缀
            bool isFile = encryptPath.Contains(".");
            int fileIndexOf = encryptPath.IndexOf(".");
            string fileTypeStr = isFile ? encryptPath.Substring(fileIndexOf, encryptPath.Length - fileIndexOf) : "";
            encryptPath = isFile ? encryptPath.Replace(fileTypeStr, "") : encryptPath;

            string[] ary = encryptPath.Split('/');
            int len = ary.Length - 1;
            for (int i = 0; i < ary.Length; i++)
            {
                string subStr = ary[i];
                bool isLastWorld = i == len;
                originPath += (ConvertContentToDeBase64(subStr) + (isLastWorld ? "" : "/"));
            }

            originPath = isHasHash ? (originPath + hashStr) : originPath;
            originPath = isFile ? (originPath + fileTypeStr) : originPath;
        }
        catch (Exception ex)
        {
            Debug.LogError(ex.ToString());
        }

#if !UNITY_EDITOR
        if (!encrpytStreamingPathBase64SetN2O.ContainsKey(encryptPath))
        {
            encrpytStreamingPathBase64SetN2O.Add(encryptPath, originPath);
        }

        if (!encrpytStreamingPathBase64MapO2N.ContainsKey(originPath))
        {
            encrpytStreamingPathBase64MapO2N.Add(originPath, encryptPath);
        }
#endif
        return originPath;
    }

    private static Dictionary<string, string> encrpytPathBase64MapO2N = new Dictionary<string, string>();
    private static Dictionary<string, string> encrpytPathBase64SetN2O = new Dictionary<string, string>();
    public static string GetEncryptPath(string path)
    {
        if (!isBase64EncryptCacheAsset)
            return path;

#if UNITY_ANDROID || UNITY_IOS
        return GetEncryptPath2Base64(path);
#else
        return path;
#endif
    }

    public static string GetEncryptPath2Base64Editor(string path)
    {
        return GetEncryptPath2Base64(path);
    }

    public static string GetEncryptPath2Base64(string path)
    {
        if (!isBase64EncryptCacheAsset)
            return path;

#if !UNITY_EDITOR
        if (encrpytPathBase64MapO2N.ContainsKey(path))
        {
            return encrpytPathBase64MapO2N[path];
        }
#endif

        //过滤hash值，hash值不做混淆处理
        int hashIndexOf = path.IndexOf("-");
        bool isHasHash = path.Contains("-") && IsNumberStr(path.Substring(hashIndexOf + 1, path.Length - hashIndexOf - 1));
        string hashStr = isHasHash ? path.Substring(hashIndexOf, path.Length - hashIndexOf) : "";
        path = isHasHash ? path.Replace(hashStr, "") : path;

        //过滤文件名后缀
        bool isFile = path.Contains(".");
        int fileIndexOf = path.IndexOf(".");
        string fileTypeStr = isFile ? path.Substring(fileIndexOf, path.Length - fileIndexOf) : "";
        path = isFile ? path.Replace(fileTypeStr, "") : path;

        string[] ary = path.Split('/');
        StringBuilder builder = new StringBuilder();
        for (int m = 0; m < ary.Length; m++)
        {
            string name = ary[m];
            if (string.IsNullOrEmpty(name))
            {
                continue;
            }

            if (m != 0)
            {
                builder.Append("/");
            }

            builder.Append(ConvertContentToBase64(name));
        }

        string newPath = builder.ToString() + (isHasHash ? hashStr : "");
        newPath = newPath + (isFile ? fileTypeStr : "");
#if !UNITY_EDITOR
        if (!encrpytPathBase64MapO2N.ContainsKey(path))
        {
            encrpytPathBase64MapO2N.Add(path, newPath);
        }

        if (!encrpytPathBase64SetN2O.ContainsKey(newPath))
        {
            encrpytPathBase64SetN2O.Add(newPath, path);
        }
#endif

        return newPath;
    }

    [NoToLua]
    public static string GetDecryptPath2Base64(string encryptPath)
    {
        if (!isBase64EncryptStreamingAsset)
            return encryptPath;
#if !UNITY_EDITOR
        if (encrpytPathBase64SetN2O.ContainsKey(encryptPath))
        {
            return encrpytPathBase64SetN2O[encryptPath];
        }
#endif
        string originPath = "";
        try
        {
            //过滤hash值，hash值不做混淆处理
            int hashIndexOf = encryptPath.IndexOf("-");
            bool isHasHash = encryptPath.Contains("-") && IsNumberStr(encryptPath.Substring(hashIndexOf + 1, encryptPath.Length - hashIndexOf - 1));
            string hashStr = isHasHash ? encryptPath.Substring(hashIndexOf, encryptPath.Length - hashIndexOf) : "";
            encryptPath = isHasHash ? encryptPath.Replace(hashStr, "") : encryptPath;

            //过滤文件名后缀
            bool isFile = encryptPath.Contains(".");
            int fileIndexOf = encryptPath.IndexOf(".");
            string fileTypeStr = isFile ? encryptPath.Substring(fileIndexOf, encryptPath.Length - fileIndexOf) : "";
            encryptPath = isFile ? encryptPath.Replace(fileTypeStr, "") : encryptPath;

            string[] ary = encryptPath.Split('/');
            int len = ary.Length - 1;
            for (int i = 0; i < ary.Length; i++)
            {
                string subStr = ary[i];
                bool isLastWorld = i == len;
                originPath += (ConvertContentToDeBase64(subStr) + (isLastWorld ? "" : "/"));
            }

            originPath = isHasHash ? (originPath + hashStr) : originPath;
            originPath = isFile ? (originPath + fileTypeStr) : originPath;
        }
        catch (Exception ex)
        {
            Debug.LogError(ex.ToString());
        }

#if !UNITY_EDITOR
        if (!encrpytPathBase64SetN2O.ContainsKey(encryptPath))
        {
            encrpytPathBase64SetN2O.Add(encryptPath, originPath);
        }

        if (!encrpytPathBase64MapO2N.ContainsKey(originPath))
        {
            encrpytPathBase64MapO2N.Add(originPath, encryptPath);
        }
#endif
        return originPath;
    }

    private static Regex regex = new Regex(@"^[0-9]+$");
    private static bool IsNumberStr(string NumStr)
    {
        return regex.IsMatch(NumStr);
    }

    public static string ConvertContentToBase64(string s)
    {
        if (Base64CodeChar == null)
        {
            Base64CodeChar = Base64Code.ToCharArray();
        }
        try
        {
            byte empty = (byte)0;
            ArrayList byteMessage = new ArrayList(Encoding.Default.GetBytes(s));
            StringBuilder outmessage;
            int messageLen = byteMessage.Count;
            int page = messageLen / 3;
            int use = 0;
            if ((use = messageLen % 3) > 0)
            {
                for (int i = 0; i < 3 - use; i++)
                    byteMessage.Add(empty);
                page++;
            }
            outmessage = new StringBuilder(page * 4);
            for (int i = 0; i < page; i++)
            {
                byte[] instr = new byte[3];
                instr[0] = (byte)byteMessage[i * 3];
                instr[1] = (byte)byteMessage[i * 3 + 1];
                instr[2] = (byte)byteMessage[i * 3 + 2];
                int[] outstr = new int[4];
                outstr[0] = instr[0] >> 2;
                outstr[1] = ((instr[0] & 0x03) << 4) ^ (instr[1] >> 4);
                if (!instr[1].Equals(empty))
                    outstr[2] = ((instr[1] & 0x0f) << 2) ^ (instr[2] >> 6);
                else
                    outstr[2] = 64;
                if (!instr[2].Equals(empty))
                    outstr[3] = (instr[2] & 0x3f);
                else
                    outstr[3] = 64;
                outmessage.Append(Base64CodeChar[outstr[0]]);
                outmessage.Append(Base64CodeChar[outstr[1]]);
                outmessage.Append(Base64CodeChar[outstr[2]]);
                outmessage.Append(Base64CodeChar[outstr[3]]);
            }
            return outmessage.ToString();
        }
        catch (Exception ex)
        {
            throw ex;
        }
    }

    public static string ConvertContentToDeBase64(string en_s)
    {
        char[] str2chars = en_s.ToCharArray();
        string binary = "";
        for (int i = 0; i < str2chars.Length; i++)
        {
            binary += Decimal2Binary(GetCharIndexInBase64Code(str2chars[i])).Substring(2, 6);
        }

        string decryptStrs = "";
        for (int i = 0; i < binary.Length / 8; i++)
        {
            string s = binary.Substring(i * 8, 8);
            int ouptutValue = Convert.ToInt32(s, 2);

            ASCIIEncoding asciiEncoding = new ASCIIEncoding();
            byte[] byteArray = new byte[] { (byte)ouptutValue };
            decryptStrs += asciiEncoding.GetString(byteArray);
        }

        return decryptStrs;
    }

    private static int GetCharIndexInBase64Code(char c)
    {
        if (Base64CodeChar == null)
        {
            Base64CodeChar = Base64Code.ToCharArray();
        }
        for (int i = 0; i < Base64CodeChar.Length; i++)
        {
            if (Base64CodeChar[i] == c)
            {
                return i;
            }
        }
        return 0;
    }

    private static string Decimal2Binary(int decima)
    {
        int a = decima;
        long target = 0; //转换成的结果
        int count = 0, radix = 2; //除进制的次数,进制
        int resider; //余数
        for (int i = a; i > 0; i /= 2) //i是每次计算后的商
        {
            resider = i % radix; //取余求的该位置的2进制数
            count++; //代表2进制时的第几个数
            int power = 1; //用来确定在计算上代表的位置
            for (int j = 0; j < count - 1; j++)
            {
                power *= 10;
            }
            target = target + (resider * power);
        }

        string number = target.ToString(); //转换成字符串
        int s = number.Length; //获得当前数字的长度
        for (int i = 1; i <= 8 - s; i++) //在数字前补0，直到总数为8位
        {
            number = number.Insert(0, "0");
        }

        return number;
    }

    public static byte[] ReadEncryptBytes(byte[] bytes)
    {
        byte[] buffer = new byte[bytes.Length];
        for (int i = 0; i < bytes.Length; i++)
        {
            buffer[i] ^= bytes[i % bytes.Length];
        }
        return buffer;
    }

    public static string ReadStreamingEncryptFile(string path)
    {
        return ReadEncryptFile(path, streamingEncryptKey);
    }

    public static string ReadEncryptFile(string path, byte[] _encryptKey = null)
    {
#if !UNITY_EDITOR
        if (!isEncryptAsset)
        {
            return string.Empty;
        }
#endif

        if (!File.Exists(path))
        {
            return string.Empty;
        }

        int size = Convert.ToInt32(new FileInfo(path).Length);
        EncryptStream fileStream = new EncryptStream(path, FileMode.Open, FileAccess.Read, FileShare.None, size, false);
#if UNITY_EDITOR
        fileStream.SetEncryptKey(_encryptKey == null ? GetEncryptKey() : _encryptKey);
#else
        fileStream.SetEncryptKey(_encryptKey == null ? encryptKey : _encryptKey);
#endif
        byte[] buffer = new byte[size];
        int length = fileStream.Read(buffer, 0, buffer.Length);

        fileStream.Close();
        fileStream.Dispose();
        return Encoding.UTF8.GetString(buffer, 0, length);
    }

    public static bool DecryptAssetBundle(string path, string targetPath)
    {
        if (!isEncryptAsset)
        {
            return false;
        }

#if UNITY_ANDROID || UNITY_IOS
        return false;
#else
        if (!File.Exists(path))
        {
            return false;
        }

        if (File.Exists(targetPath))
        {
            return true;
        }

        string dirName = Path.GetDirectoryName(targetPath);
        if (!Directory.Exists(dirName))
        {
            Directory.CreateDirectory(dirName);
        }

        var data = File.ReadAllBytes(path);
        var fileStream = new EncryptStream(targetPath, FileMode.OpenOrCreate, FileAccess.ReadWrite, FileShare.None, data.Length, false);
        fileStream.SetEncryptKey(encryptKey);
        fileStream.Write(data, 0, data.Length);
        fileStream.Close();
        fileStream.Dispose();
#endif

        return true;
    }

    public static string DecryptAgentAssets(string path)
    {
        if (!isEncryptAsset)
        {
            return string.Empty;
        }

        if (!File.Exists(path))
        {
            return string.Empty;
        }

        if (!path.StartsWith(Application.streamingAssetsPath))
        {
            return string.Empty;
        }

        string relativePath = Regex.Replace(path, Application.streamingAssetsPath, "");
        string targetPath = string.Format("{0}/AgentAssets/{1}", Application.persistentDataPath, relativePath);
        if (File.Exists(targetPath))
        {
            return targetPath;
        }

        string dirName = Path.GetDirectoryName(targetPath);
        if (!Directory.Exists(dirName))
        {
            Directory.CreateDirectory(dirName);
        }

        var data = File.ReadAllBytes(path);
        var fileStream = new EncryptStream(targetPath, FileMode.OpenOrCreate, FileAccess.ReadWrite, FileShare.None, data.Length, false);
        fileStream.SetEncryptKey(encryptKey);
        fileStream.Write(data, 0, data.Length);
        fileStream.Close();
        fileStream.Dispose();
        return targetPath;
    }

    public static string DecryptStr2Str(string str)
    {
        return DecryptByte2Str(Encoding.UTF8.GetBytes(str));
    }

    public static string DecryptByte2Str(byte[] buffer)
    {
        byte[] _encryptKey;
        if (null != encryptKey && encryptKey.Length != 0)
        {
            _encryptKey = encryptKey;
        }
        else
        {
            _encryptKey = GetEncryptKey();
        }
        for (int i = 0; i < buffer.Length; i++)
        {
            int index = i % _encryptKey.Length;
            buffer[i] = (byte)((char)buffer[i] ^ _encryptKey[index]);
        }
        return Encoding.UTF8.GetString(buffer);
    }

    public static bool WriteFileToThePath(byte[] bytes, string path)
    {
        try
        {
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }
            using (FileStream fileStream = new FileStream(path, FileMode.OpenOrCreate, FileAccess.Write, FileShare.None, bytes.Length, false))
            {
                fileStream.Write(bytes, 0, bytes.Length);
                fileStream.Close();
                fileStream.Dispose();
            }
        }
        catch (Exception ex)
        {
            Debug.LogErrorFormat("Exception Info:", ex.ToString());
            return false;
        }

        return true;
    }

    public static string XORAndBase64Encode(string value, string key)
    {
#if !UNITY_EDITOR
        if (encrpytPathBase64MapO2N.ContainsKey(value))
        {
            return encrpytPathBase64MapO2N[value];
        }
#endif
        if (string.IsNullOrEmpty(value))
        {
            Debug.LogError("Error, EncryptMgr.XORAndBase64Encode value is null");
        }
        key = string.IsNullOrEmpty(key) ? EncryptMgr.Base64KeyCode : key;
        StringBuilder stringBuilder = new StringBuilder();
        try
        {
            byte[] numArray = new byte[1];
            int startIndex1 = 0;
            for (int startIndex2 = 0; startIndex2 < value.Length; ++startIndex2)
            {
                string s1 = value.Substring(startIndex2, 1);
                string s2 = key.Substring(startIndex1, 1);
                byte bytes1 = Encoding.UTF8.GetBytes(s1)[0];
                byte bytes2 = Encoding.UTF8.GetBytes(s2)[0];
                numArray[0] = (byte)((uint)bytes1 ^ (uint)bytes2);
                stringBuilder.Append((char)numArray[0]);
                ++startIndex1;
                if (startIndex1 >= key.Length)
                {
                    startIndex1 = 0;
                }
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error, EncryptMgr.XORAndBase64Encode value:{value}");
            throw;
        }

        string result = Convert.ToBase64String(Encoding.ASCII.GetBytes(stringBuilder.ToString()));
#if !UNITY_EDITOR
        if (!encrpytPathBase64MapO2N.ContainsKey(value))
        {
            encrpytPathBase64MapO2N.Add(value, result);
        }

        if (!encrpytPathBase64SetN2O.ContainsKey(result))
        {
            encrpytPathBase64SetN2O.Add(result, value);
        }
#endif
        return result;
    }

    public static string XORAndBase64Decode(string value, string key)
    {
#if !UNITY_EDITOR
        if (encrpytPathBase64SetN2O.ContainsKey(value))
        {
            return encrpytPathBase64SetN2O[value];
        }
#endif

        string str = FromBase64String(value);
        int startIndex2 = 0;
        byte[] numArray = new byte[1];

        StringBuilder stringBuilder = new StringBuilder();
        for (int startIndex1 = 0; startIndex1 < str.Length; ++startIndex1)
        {
            string s1 = str.Substring(startIndex1, 1);
            byte bytes1 = Encoding.UTF8.GetBytes(s1)[0];
            string s2 = key.Substring(startIndex2, 1);
            byte bytes2 = Encoding.UTF8.GetBytes(s2)[0];
            numArray[0] = (byte)((uint)bytes1 ^ (uint)bytes2);
            stringBuilder.Append((char)numArray[0]);
            ++startIndex2;
            if (startIndex2 >= key.Length)
            {
                startIndex2 = 0;
            }
        }

        string result = stringBuilder.ToString();

#if !UNITY_EDITOR
        if (!encrpytPathBase64SetN2O.ContainsKey(value))
        {
            encrpytPathBase64SetN2O.Add(value, result);
        }

        if (!encrpytPathBase64MapO2N.ContainsKey(result))
        {
            encrpytPathBase64MapO2N.Add(result, value);
        }
#endif

        return result;
    }

    public static string FromBase64String(string value)
    {
        return Encoding.UTF8.GetString(Convert.FromBase64String(value));
    }

#if UNITY_EDITOR
    [NoToLua]
    public static void EncryptSteamFiles()
    {
        string path = Path.Combine(Application.streamingAssetsPath, "file_list.txt").Replace("\\", "/");
        byte[] encryptKey = GetEncryptKey();
        string[] lines = File.ReadAllLines(path);
        EncryptStream fileStream = null;

        foreach (var line in lines)
        {
            string abPath = Path.Combine(Application.streamingAssetsPath, line);
            var abData = File.ReadAllBytes(abPath);
            fileStream = new EncryptStream(abPath, FileMode.Create);
            fileStream.SetEncryptKey(encryptKey);
            fileStream.Write(abData, 0, abData.Length);
            fileStream.Close();
            fileStream.Dispose();
        }

        var data = File.ReadAllBytes(path);
        fileStream = new EncryptStream(path, FileMode.Create);
        fileStream.SetEncryptKey(encryptKey);
        fileStream.Write(data, 0, data.Length);
        fileStream.Dispose();
    }

    [NoToLua]
    public static void DecryptSteamFiles()
    {
        string path = Path.Combine(Application.streamingAssetsPath, "file_list.txt").Replace("\\", "/");
        byte[] encryptKey = GetEncryptKey();
        var data = File.ReadAllBytes(path);
        EncryptStream fileStream = new EncryptStream(path, FileMode.Create);
        fileStream.SetEncryptKey(encryptKey);
        fileStream.Write(data, 0, data.Length);
        fileStream.Dispose();

        string[] lines = File.ReadAllLines(path);
        foreach (var line in lines)
        {
            string abPath = Path.Combine(Application.streamingAssetsPath, line);
            var abData = File.ReadAllBytes(abPath);
            fileStream = new EncryptStream(abPath, FileMode.Create);
            fileStream.SetEncryptKey(encryptKey);
            fileStream.Write(abData, 0, abData.Length);
            fileStream.Close();
            fileStream.Dispose();
        }
    }
#endif
}
