require("game/serveractivity/gift_choose/choose_gift_wg_data")
require("game/serveractivity/gift_choose/choose_gift_view")
require("game/serveractivity/gift_choose/batch_use_choose_gift_view")
FunctionChooseWGCtrl = FunctionChooseWGCtrl or BaseClass(BaseWGCtrl)

function FunctionChooseWGCtrl:__init()
	FunctionChooseWGCtrl.Instance = self
	self.data = FunctionChooseWGData.New()
	self.view = FunctionChooseView.New(GuideModuleName.ChooseGiftView)
	self.batch_use_choose_gift_view = BatchUseChooseGiftView.New(GuideModuleName.BatchUseChooseGiftView)
end

function FunctionChooseWGCtrl:__delete()
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end
	if nil ~= self.view then 
		self.view:DeleteMe()
		self.view = nil
	end

	if nil ~= self.batch_use_choose_gift_view then 
		self.batch_use_choose_gift_view:DeleteMe()
		self.batch_use_choose_gift_view = nil
	end

	FunctionChooseWGCtrl.Instance = nil 
end

-- 打开主窗口
function FunctionChooseWGCtrl:Open()
	ViewManager.Instance:Open(GuideModuleName.ChooseGiftView)
end

function FunctionChooseWGCtrl:SetSelectGifData(index, num, data, item_id, is_hide_btn)
	if self.view then
		self.view:SetData(index, num, data, item_id, is_hide_btn)
	end
end

function FunctionChooseWGCtrl:Flush()
	if self.view then
		self.view:Flush()
	end
end

function FunctionChooseWGCtrl:OpenBatchUseChooseGift(gift_id, item_id, num, show_num, callback, bag_index)
	if self.batch_use_choose_gift_view then
		self.batch_use_choose_gift_view:SetConfirmCallBack(callback)
		self.batch_use_choose_gift_view:SetData(gift_id, item_id, num, show_num, bag_index)
	end
end