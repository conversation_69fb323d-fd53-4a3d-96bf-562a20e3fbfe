DujieResultFailView = DujieResultFailView or BaseClass(SafeBaseView)

--渡劫失败面板
function DujieResultFailView:__init()
	self.default_index = 0
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:SetMaskBg(false)
	self.view_layer = UiLayer.Pop

	self:AddViewResource(0, "uis/view/dujie_ui_prefab", "layout_jiesuan_lose_bg_panel")
	self:AddViewResource(0, "uis/view/dujie_ui_prefab", "layout_jiesuan_lose")

	self.end_time = 5
	self.load_complete = false

end

function DujieResultFailView:ReleaseCallBack()
	self.load_complete = nil
	self.comefrom_scene_type = nil

	if self.play_star_event then
		GlobalEventSystem:UnBind(self.play_star_event)
		self.play_star_event = nil
	end
end

function DujieResultFailView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind(self.Close, self))

	XUI.AddClickEventListener(self.node_list.btn_zhuanzhi, BindTool.Bind(self.OpenZhuanzhi, self))
    XUI.AddClickEventListener(self.node_list.btn_xiuwei, BindTool.Bind(self.OpenXiuwei, self))
end


function DujieResultFailView:ShowIndexCallBack(index)
	UITween.ShowCommonTiaoZhanJieSuanPanelTween(self, false, self.node_list.tween_info_root)

    if self.end_time > 0 then
		self:UpdateCloseCountDownTime(1, self.end_time)
		if CountDownManager.Instance:HasCountDown("dujie_result_fail_close_timer") then
			CountDownManager.Instance:RemoveCountDown("dujie_result_fail_close_timer")
		end
		CountDownManager.Instance:AddCountDown("dujie_result_fail_close_timer", BindTool.Bind1(self.UpdateCloseCountDownTime, self), BindTool.Bind1(self.Close, self), nil, self.end_time , 1)
	end
end

function DujieResultFailView:CloseCallBack()

    if CountDownManager.Instance:HasCountDown("dujie_result_fail_close_timer") then
		CountDownManager.Instance:RemoveCountDown("dujie_result_fail_close_timer")
	end
end


function DujieResultFailView:OnFlush()
    local base_info = DujieWGData.Instance:GetOrdealBaseInfo()

    -- local server_time = TimeWGCtrl.Instance:GetServerTime()
    self.node_list.text_time.text.text = TimeUtil.FormatSecondDHM9(base_info.fail_reduce_time - base_info.last_ordeal_end_time)
    self.node_list.text_per.text.text = (base_info.fail_reduce_per/100).."%"
end


function DujieResultFailView:UpdateCloseCountDownTime(elapse_time, total_time)
	if total_time - elapse_time > 0 then
		self.node_list.lbl_end_time.text.text = string.format(Language.Dujie.AutoClose, math.floor(total_time - elapse_time))
	end
end

-- 打开转生
function DujieResultFailView:OpenZhuanzhi()
    ViewManager.Instance:Open(GuideModuleName.ZhuanSheng)
	self:Close()
end
-- 打开修为
function DujieResultFailView:OpenXiuwei()
    ViewManager.Instance:Open(GuideModuleName.XiuWeiView)
	self:Close()
end
