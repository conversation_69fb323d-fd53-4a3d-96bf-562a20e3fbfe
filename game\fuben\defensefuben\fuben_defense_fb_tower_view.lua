--塔防副本下侧
DefenseFbTowerView = DefenseFbTowerView or BaseClass(SafeBaseView)

function DefenseFbTowerView:__init()
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_tower_build_view")
end

function DefenseFbTowerView:__delete()
end

function DefenseFbTowerView:ReleaseCallBack()
	self.pos_index = nil
	if CountDownManager.Instance:HasCountDown("fuben_tafang_start_down") then
		CountDownManager.Instance:RemoveCountDown("fuben_tafang_start_down")
	end
	self.is_load_complete = nil
	self.one_level_tower_cfg = nil
end

function DefenseFbTowerView:LoadCallBack(index, loaded_times)
	-- self.mask_bg.image.color = Color.New(0,0,0,0)
	local defense_tower_cfg = FuBenWGCtrl.Instance.defense_fb_data:GetDefenseTowerList()
	self.one_level_tower_cfg = {}
	for k,v in ipairs(defense_tower_cfg)do
		if v.tower_level == 1 then
			self.one_level_tower_cfg[v.tower_type] = v
		end
	end
	
	for i=0,3 do
		self.node_list["btn_tower_build_"..i].button:AddClickListener(BindTool.Bind(self.OnClickDefenseBtn, self, BUILD_TOWER_OPERA_TYPE.BUILD_TOWER_OPERA_TYPE_BUILD, i))
	end
	XUI.AddClickEventListener(self.node_list["btn_close_window1"], BindTool.Bind1(self.CloseWindow1, self)) 
	self.node_list.slider_bg:SetActive(false)
	self.node_list.btn_group:SetActive(true)
	self.is_load_complete = true

	if self.is_should_flush then
		self:Flush()
	end
end

function DefenseFbTowerView:CloseCallBack()
	-- if self.target_obj.HideAttackRangeRadius then
	-- 	self.target_obj:HideAttackRangeRadius()
	-- end

	self.pos_index = nil
end

function DefenseFbTowerView:ShowIndexCallBack(index)
	-- if self.target_obj.ShowAttackRangeRadius then
	-- 	self.target_obj:ShowAttackRangeRadius()
	-- end
	-- if not self.is_load_complete then
	-- 	self.is_should_flush = true
	-- 	return
	-- end
	self.node_list.slider_bg:SetActive(false)
	self.node_list.btn_group:SetActive(true)
	 self:Flush()
end

function DefenseFbTowerView:OnFlush()
	if not self.is_load_complete then
		self.is_should_flush = true
		return
	end
	local defense_data = FuBenWGCtrl.Instance.defense_fb_data:GetBuildTowerFBSceneLogicInfo()
	local defense_douhun = defense_data.douhun or 0
	for i=0,3 do
		self.node_list["lbl_tower_build_" .. i].text.text = ToColorStr(self.one_level_tower_cfg[i].need_douhun, defense_douhun >= self.one_level_tower_cfg[i].need_douhun and COLOR3B.WHITE or COLOR3B.RED) 
		self.node_list["lbl_tower_name_" .. i].text.text = self.one_level_tower_cfg[i].tower_name
		self.node_list["rich_tower_spec_" .. i].text.text = self.one_level_tower_cfg[i].tower_spec
		self.node_list["btn_tower_build_"..i].button:AddClickListener(BindTool.Bind(self.OnClickDefenseBtn, self, BUILD_TOWER_OPERA_TYPE.BUILD_TOWER_OPERA_TYPE_BUILD, i))
	end
end

function DefenseFbTowerView:SetTargetObjData(pos_index)
	self.pos_index = pos_index
end

-- function DefenseFbTowerView:OnFlush(param_list, index)
-- 	if self.pos_index == nil then return end

-- 	-- local defense_data = FuBenWGCtrl.Instance.defense_fb_data:GetBuildTowerFBSceneLogicInfo()
-- 	-- local defense_douhun = defense_data.douhun or 0
-- end

function DefenseFbTowerView:OnClickDefenseBtn(operate_type, param2)
	if self.pos_index == nil then
		return
	end
	self.tower_type = operate_type 
	self.tower_index = param2
	self:ShowSlider()
	--self:CompleteTime()
end

function DefenseFbTowerView:UpdateTime(elapse_time, total_time)
	--print_error("测试")
	self.node_list.slider_image.image.fillAmount = elapse_time / total_time
end

function DefenseFbTowerView:ShowSlider()
	self.node_list.slider_bg:SetActive(true)
	self.node_list.btn_group:SetActive(false)
	if CountDownManager.Instance:HasCountDown("fuben_tafang_start_down") then
		CountDownManager.Instance:RemoveCountDown("fuben_tafang_start_down")
	end
	local time_stamp = TimeWGCtrl.Instance:GetServerTime()
	self:UpdateTime(time_stamp, time_stamp + 1 )
	CountDownManager.Instance:AddCountDown("fuben_tafang_start_down", BindTool.Bind1(self.UpdateTime, self), BindTool.Bind1(self.CompleteTime, self),  time_stamp + 1 , nil, 0.02)
end

function DefenseFbTowerView:CompleteTime()
	self.node_list.slider_bg:SetActive(false)
	if nil == self.tower_type or nil == self.tower_index then return end
--	FuBenWGCtrl.Instance:SendBuildTowerReq(operate_type, self.pos_index, param2)
	FuBenWGCtrl.Instance:SendBuildTowerReq(self.tower_type , self.pos_index, self.tower_index)
	-- if self.pos_index ~= nil then
	-- 	FuBenWGCtrl.Instance:OpenDefenseTowerFuBenFeformView(self.pos_index)
	-- end
	self:Close()
end

function DefenseFbTowerView:CloseWindow1()
	if CountDownManager.Instance:HasCountDown("fuben_tafang_start_down") then
		CountDownManager.Instance:RemoveCountDown("fuben_tafang_start_down")
	end
	self.node_list.slider_bg:SetActive(false)
	self.node_list.slider_image.image.fillAmount = 0
	self.tower_type = nil
	self:Close()
end

function DefenseFbTowerView:Close()
	if self.node_list.slider_bg then 
		local vas = self.node_list.slider_bg:GetActive()
		if vas then
			return
		end
	end
	SafeBaseView.Close(self)
end
