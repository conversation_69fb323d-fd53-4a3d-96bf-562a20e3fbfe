﻿//------------------------------------------------------------------------------
// This file is part of MistLand project in Nirvana.
// Copyright © 2016-2016 Nirvana Technology Co., Ltd.
// All Right Reserved.
//------------------------------------------------------------------------------

using System;
using Nirvana;
using UnityEngine.UI;

/// <summary>
/// The extensions for <see cref="Image"/>.
/// </summary>
public static class ImageExtensions
{
    /// <summary>
    /// Load the sprite into this image.
    /// </summary>
    public static void LoadSprite(
        this Image image, string bundleName, string spriteName, Action complete = null)
    {
        var assetID = new AssetID(bundleName, spriteName);

#if UNITY_EDITOR
        if (!SpritePool.Instance.CheckCanLoadSprite(image.gameObject, assetID))
        {
            return;
        }
#endif

        SpritePool.Instance.Load(
	        assetID,
	        sprite =>
	        {
	            if (sprite != null)
	            {
	                if (image != null)
	                {
	                    SpritePool.Instance.SetImageSprite(image, sprite);
	                    if (complete != null)
	                    {
	                        complete();
	                    }
	                }

	                SpritePool.Instance.Free(sprite);
	            }
	        });
    }
}
