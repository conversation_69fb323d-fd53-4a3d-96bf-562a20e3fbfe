TianShenBaGuaWGData = TianShenBaGuaWGData or BaseClass()

function TianShenBaGuaWGData:__init()
	TianShenBaGuaWGData.Instance = self
	local tianshen_bagua_cfg = ConfigManager.Instance:GetAutoConfig("tianshen_bagua_auto")
	self.tianshen_bagua_slot = ListToMap(tianshen_bagua_cfg.slot,"slot")
	self.tianshen_bagua_base_cfg = ListToMap(tianshen_bagua_cfg.info,"index","star","part")
	self.tianshen_bagua_id_cfg = ListToMap(tianshen_bagua_cfg.info,"itemid")
	self.tianshen_bagua_compose_cfg = ListToMap(tianshen_bagua_cfg.info,"stuff_id")
	self.tianshen_bagua_shop_cfg = ListToMap(tianshen_bagua_cfg.shop_item,"itemid")
	self.tianshen_bagua_suit_skill = ListToMap(tianshen_bagua_cfg.suit_skill,"index","sort_index")
	self.tianshen_bagua_advise_show = ListToMap(tianshen_bagua_cfg.advise_show,"part")
	self.tianshen_bagua_attr_index_cfg = ListToMap(tianshen_bagua_cfg.attr_slot,"free_fresh_cd")
	self.tianshen_bagua_shop_flush_cfg = ListToMap(tianshen_bagua_cfg.fresh_price,"times")
	self.tianshen_bagua_decompose_cfg = ListToMap(tianshen_bagua_cfg.decompose,"itemid")
	self.tianshen_bagua_shop_red_cfg = ListToMap(tianshen_bagua_cfg.shop_red,"bagua_index")
	
	
	self.tianshen_bagua_other = tianshen_bagua_cfg.other
	self.have_bagua_bag_change = false
	RemindManager.Instance:Register(RemindName.TianShen_BaGua, BindTool.Bind(self.GetTianShenBaGuaRemind, self))
	self.event_handler = GlobalEventSystem:Bind(OtherEventType.ROLE_LEVEL_UP,BindTool.Bind(self.DealLevelUp, self))
	self.cur_select_bagua = 0
	self.set_been_click = false
	self.grid_list = {}
end

function TianShenBaGuaWGData:__delete()
	if self.delay_stronger then
		GlobalTimerQuest:CancelQuest(self.delay_stronger)
		self.delay_stronger = nil
	end

	TianShenBaGuaWGData.Instance = nil
	GlobalEventSystem:UnBind(self.event_handler)
	self.event_handler = nil
end

function TianShenBaGuaWGData:GetTianShenBaguaSlot()
	return self.tianshen_bagua_slot or {}
end

function TianShenBaGuaWGData:GetTianShenBaguaOpenSlot()
	local open_slot = {}
	local can_open = true
	if self.tianshen_bagua_slot then
		for k,v in pairs(self.tianshen_bagua_slot) do
			if v.index <=3 then
				table.insert(open_slot,v)
			else
				can_open = true
				if self.grid_list and self.grid_list[v.index] then
					for k,v in pairs(self.grid_list[v.index]) do
						if v.item_id <= 0 then
							can_open = false
							break
						end
					end
				end
				if can_open then
					table.insert(open_slot,v)
				end
			end
		end
	end
	return open_slot
end


function TianShenBaGuaWGData:GetBaGuaNameInfo(slot)
	return self.tianshen_bagua_slot[slot] or {}
end


function TianShenBaGuaWGData:OnSCTianshenBaguaOtherInfo(protocol)
	self.free_fresh_count = protocol.free_fresh_count
    self.start_free_fresh_timestamp = protocol.start_free_fresh_timestamp
    self.shop_item_list = protocol.item_list
    self.buy_num = protocol.buy_num
    self.buy_count = protocol.buy_count
    if not self.bagua_money then
    	self.bagua_money = protocol.bagua_money
	elseif self.bagua_money ~= protocol.bagua_money then
		local item_id = self:GetBaGuaBiCfg()
		local cfg = ItemWGData.Instance:GetItemConfig(item_id)
		if self.bagua_money > protocol.bagua_money then
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Exchange.ExpendName,ToColorStr(cfg.name, GET_TIP_ITEM_COLOR[cfg.color]))..(self.bagua_money - protocol.bagua_money))
		else
			local str = string.format(Language.SysRemind.AddItem,ToColorStr(cfg.name, GET_TIP_ITEM_COLOR[cfg.color]), protocol.bagua_money - self.bagua_money)
			SysMsgWGCtrl.Instance:ErrorRemind(str)
		end
		self.bagua_money = protocol.bagua_money
		GlobalEventSystem:Fire(OtherEventType.Ba_Gua_Change)
	end
	
end

function TianShenBaGuaWGData:GetBaGuaShopBuyCount()
	return self.buy_count or 0
end

function TianShenBaGuaWGData:DealLevelUp()
	local new_level = GameVoManager.Instance:GetMainRoleVo().level
	if self.tianshen_bagua_slot then
		for k,v in pairs(self.tianshen_bagua_slot) do
			if v.open_level == new_level then
				self:CacularBaGuaSlotOpenList()
				self:CacularAllBaGuaEquipRed()
				TianShenWGCtrl.Instance:DoLevelFlushBaGua()
				return
			end
		end
	end
end

--八卦装备信息
function TianShenBaGuaWGData:OnSCTianshenBaguaInfo(protocol)
	self.grid_list = protocol.grid_list
	self:CacularBaGuaSlotOpenList()
end


function TianShenBaGuaWGData:OnSCTianshenBaguaPartChange(protocol)
	local old_id = 0
	if self.grid_list and self.grid_list[protocol.part] then
		old_id = self.grid_list[protocol.part][protocol.index].item_id or 0
		self.grid_list[protocol.part][protocol.index] = protocol.grid_data
	end

	if old_id == protocol.grid_data.item_id then
		TianShenWGCtrl.Instance:ShowBaGuaInsertAnim(protocol)
	end

	-- if old_id > 0 then
	-- 	self:CacularBaGuaSlotOpenList(old_id)
	-- end

	if protocol.grid_data.item_id > 0 then
		self:CacularBaGuaSlotOpenList()
	end

	local base_cfg = self.tianshen_bagua_id_cfg[protocol.grid_data.item_id] or {}
	if base_cfg then
		for i =1,3 do
			if self.tianshen_bagua_base_cfg[base_cfg.index] and self.tianshen_bagua_base_cfg[base_cfg.index][i] then
				self:CacularAllBaGuaEquipRed({item_id = self.tianshen_bagua_base_cfg[base_cfg.index][i][base_cfg.part].itemid})
			end
		end
	end
end


function TianShenBaGuaWGData:GetBaGuaShopData()
	return self.shop_item_list or {}
end

function TianShenBaGuaWGData:GetBaGuaCoinNum()
	return self.bagua_money or 0
end

function TianShenBaGuaWGData:GetFlushTimesAndTime()
	return self.free_fresh_count,self.start_free_fresh_timestamp
end

--八卦背包信息
function TianShenBaGuaWGData:OnSCTianshenBaguaBagInfo(protocol)
	self.grid_count = protocol.grid_count
	self.bag_list = protocol.bag_list
	self.bag_index_list = protocol.bag_index_list
	self.item_id_list = protocol.item_id_list
	self.have_bagua_bag_change = true
	self:CacularAllBaGuaEquipRed()
end

--八卦背包信息改变
function TianShenBaGuaWGData:OnSCTianShenBagChangeInfo(protocol)
	self.have_bagua_bag_change = true
	local change_info = protocol.change_info
	local old_id = 0 
	if self.bag_index_list and self.bag_index_list[change_info.index] then	
		old_id = self.bag_list[self.bag_index_list[change_info.index]].item_id
		self.item_id_list[old_id][change_info.index] = -1
		if not self.item_id_list[change_info.item_id] then
			self.item_id_list[change_info.item_id] = {}
		end
		-- if old_id > 0 then
		-- 	self:CacularAllBaGuaEquipRed({item_id = old_id})
		-- end
		self.item_id_list[change_info.item_id][change_info.index] = change_info.index
		self.bag_list[self.bag_index_list[change_info.index]] = change_info
	elseif self.bag_index_list then
		self.grid_count = self.grid_count + 1
		self.bag_list[self.grid_count] = change_info
		self.bag_index_list[change_info.index] = self.grid_count

		if not self.item_id_list[change_info.item_id] then
			self.item_id_list[change_info.item_id] = {}
		end
		self.item_id_list[change_info.item_id][change_info.index] = change_info.index
	else
		self.bag_index_list = {}
		self.bag_list = {}
		self.grid_count = 1
		self.bag_list[self.grid_count] = change_info
		self.bag_index_list[change_info.index] = self.grid_count
		self.item_id_list = {}
		self.item_id_list[change_info.item_id] = {}
		self.item_id_list[change_info.item_id][change_info.index] = change_info.index
	end
	if change_info.item_id > 0 then
		local cfg = ItemWGData.Instance:GetItemConfig(change_info.item_id)
		local str = string.format(Language.SysRemind.AddItem,ToColorStr(cfg.name, GET_TIP_ITEM_COLOR[cfg.color]), 1)
		SysMsgWGCtrl.Instance:ErrorRemind(str)
	end

	self:CacularAllBaGuaEquipRed()

	-- local base_cfg = {} 
	-- local old_cfg = {} 
	-- if old_id > 0 then
	-- 	old_cfg = self.tianshen_bagua_id_cfg[old_id]
	-- end

	-- if change_info.item_id > 0 then
	-- 	base_cfg = self.tianshen_bagua_id_cfg[change_info.item_id]
	-- end

	-- local need_flush_red = false
	-- if old_cfg and old_cfg.index and old_cfg.index >= 4 then
	-- 	need_flush_red = true
	-- else
	-- 	self:CacularAllBaGuaEquipRed({item_id = old_id})
	-- end

	-- if base_cfg and base_cfg.index >= 4 then
	-- 	need_flush_red = true
	-- else
	-- 	self:CacularAllBaGuaEquipRed(change_info)
	-- end

	-- if need_flush_red then
	-- 	for i = 4,7 do
	-- 		for k = 1,3 do
	-- 			if self.tianshen_bagua_base_cfg and self.tianshen_bagua_base_cfg[i] then
	-- 				base_cfg = self.tianshen_bagua_base_cfg[i][k]
	-- 				for k,v in pairs(base_cfg) do
	-- 					if v.itemid and self:ChackHaveBagua(v.itemid) then
	-- 						self:CacularAllBaGuaEquipRed({item_id = v.itemid})
	-- 					end
	-- 				end
	-- 			end			
	-- 		end
	-- 	end
	-- end
end

function TianShenBaGuaWGData:GetTianShenBaGuaEquipInfo(index)
	if not index or not self.grid_list then return {} end
	index = index + 1
	return self.grid_list[index] or {}
end

function TianShenBaGuaWGData:CheckBaGuaIsOpen(index)
	if not index then return false end
	local name_cfg = self.tianshen_bagua_slot[index] or {}
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	if name_cfg and name_cfg.open_level and role_level >= name_cfg.open_level then

	else
		return false
	end
	index = index + 1
	if self.grid_list[index -1] then
		for k,v in pairs(self.grid_list[index -1]) do
			if v.item_id <= 0 then
				return false
			end
		end
		return true
	elseif self.grid_list[index] then
		return true
	end
	return false
end
--穿戴8件
function TianShenBaGuaWGData:CheckWearMax(index)
	if not index then return false end
	index = index + 1
	if self.grid_list[index] then
		for k,v in pairs(self.grid_list[index]) do
			if v.item_id <= 0 then
				return false
			end
		end
		return true
	else
		return false
	end
	return false
end



function TianShenBaGuaWGData:GetTianShenBaGuaBagList(index)
	index = index or 0

	self.bag_grade_list = {}
	for i = 1, 20 do
		self.bag_grade_list[i] = {}
	end
	local num = 0
	local a,b = 1, 1
	local red_info = {}
	local temp_data = {}

	for k,v in pairs(self.bag_list) do
		red_info = self:GetBaGuaItemRedInfo(v.item_id)
		v.sort_index = red_info.slot_index or 0
		if v.item_id > 0 and ((self.tianshen_bagua_id_cfg[v.item_id] and self.tianshen_bagua_id_cfg[v.item_id].index == index) or index == 8) then
			num = num +1
			temp_data[num] = v
		end
	end

	if IsEmptyTable(temp_data) then
		return self.bag_grade_list
	end

	table.sort(temp_data,SortTools.KeyUpperSorter("sort_index"))

	num = 0
	for t,q in pairs(temp_data) do
		num = num + 1
		a = math.ceil(num / 4)
		b = num % 4
		b = b == 0 and 4 or b
		if not self.bag_grade_list[a] then
			self.bag_grade_list[a] = {}
		end
		self.bag_grade_list[a][b] = q
	end

	return self.bag_grade_list
end

function TianShenBaGuaWGData:CheckBaGuaStronger()
	if self.delay_stronger then
		return
	end
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_BAGUA,0,nil)
	self.delay_stronger = GlobalTimerQuest:AddDelayTimer(function ()
		GlobalTimerQuest:CancelQuest(self.delay_stronger)
		self.delay_stronger = nil
		self:GetTianShenBaGuaRemind(1)
	end, 1)
end

function TianShenBaGuaWGData:GetTianShenBaGuaRemind(check_type)
	local red_info
	-- for k,v in pairs(self.tianshen_bagua_id_cfg) do
	-- 	red_info = self:GetBaGuaItemRedInfo(k)
	-- 	if not IsEmptyTable(red_info) then
	-- 		if red_info.equip_red == 1 then
	-- 			return 1
	-- 		end
	-- 	end
	-- end
	local base_cfg = {}
	local next_base_cfg = {}
	local wear_list = {}
	local red_flag = 0
	local now_star = 0
	local bagua_info = {}
	local equip_info = {}
	for k = 1 , 8 do --,v in pairs(self.grid_list) do
		if self:CheckBaGuaIsOpenNew(k-1) then
			self:SaveTempIndex(k)
			for i =1,8 do
   				red_flag ,wear_list= TianShenBaGuaWGData.Instance:GetWearableEquip(k-1,i-1)
   				bagua_info = TianShenBaGuaWGData.Instance:GetTianShenBaGuaEquipInfo(k-1)
				equip_info = bagua_info[i]

				if equip_info.item_id > 0 then
					base_cfg = TianShenBaGuaWGData.Instance:GetBaGuaBaseInfo(equip_info.item_id)
					now_star = base_cfg.star
				else
					now_star = 0
				end

				for q = 3,1 ,-1 do
					if red_flag == 1 and wear_list[1] and wear_list[1].star >= q then
						self:SaveTempIndex(k)
						MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_BAGUA,1,function ()
			 					ViewManager.Instance:Open(GuideModuleName.TianShenView, TabIndex.tianshen_bagua,"jump_bagua",{index = k-1})
			 				end)
						return 1
					else
						local base_cfg3 = TianShenBaGuaWGData.Instance:GetBaGuaCfgByIndexStarPart(k-1,q,i-1)
						if base_cfg3.star > now_star then
							local can_compose = TianShenBaGuaWGData.Instance:CheckCanComposeItem(base_cfg3.itemid)
							if can_compose then
								self:SaveTempIndex(k)
								MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_BAGUA,1,function ()
			 							ViewManager.Instance:Open(GuideModuleName.TianShenView, TabIndex.tianshen_bagua,"jump_bagua",{index = k-1})
			 						end)
								return 1
							end
						end
					end
				end
   				if red_flag == 1 then 
   					self:SaveTempIndex(k)
   					MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_BAGUA,1,function ()
			 				ViewManager.Instance:Open(GuideModuleName.TianShenView, TabIndex.tianshen_bagua,"jump_bagua",{index = k-1})
			 			end)
					return 1
   				end
   			end


			-- for t,q in pairs(v) do
			-- 	wear_type,wear_list = TianShenBaGuaWGData.Instance:GetWearableEquip(k-1,t-1)
			-- 	if wear_type == 1 then
			-- 		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_BAGUA,1,function ()
			-- 			ViewManager.Instance:Open(GuideModuleName.TianShenView, TabIndex.tianshen_bagua,"jump_bagua",{index = k-1})
			-- 		end)
			-- 		return 1
			-- 	end

			-- 	if q.item_id > 0 then
			-- 		base_cfg = self.tianshen_bagua_id_cfg[q.item_id]
			-- 		if base_cfg.star < 3 then
			-- 			next_base_cfg = self.tianshen_bagua_compose_cfg[q.item_id]
			-- 			if next_base_cfg and next_base_cfg.item_id then
			-- 				local can_compsoe = self:CheckCanComposeItem(next_base_cfg.item_id)
			-- 				if can_compsoe then
			-- 					MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_BAGUA,1,function ()
			-- 						ViewManager.Instance:Open(GuideModuleName.TianShenView, TabIndex.tianshen_bagua,"jump_bagua",{index = k-1})
			-- 					end)
			-- 					return 1
			-- 				end
			-- 			end
			-- 		end
			-- 	else
			-- 		if self.tianshen_bagua_base_cfg[k-1] and self.tianshen_bagua_base_cfg[k-1][i] then
			-- 			next_base_cfg = self.tianshen_bagua_base_cfg[k-1][1][t-1]
			-- 			local can_compsoe = self:CheckCanComposeItem(next_base_cfg.item_id)
			-- 			if can_compsoe then
			-- 				MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_BAGUA,1,function ()
			-- 					ViewManager.Instance:Open(GuideModuleName.TianShenView, TabIndex.tianshen_bagua,"jump_bagua",{index = k-1})
			-- 				end)
			-- 				return 1
			-- 			end
			-- 		end
			-- 	end
			-- end
		else
		end
	end
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_BAGUA,0,nil)
	if check_type == 1 then
		return 0
	end
	if 1 == self:GetBaGuaShopRed() then
		return 1
	end

	return 0
end

function TianShenBaGuaWGData:SetCurSelectBaGua(select_index)
	self.cur_select_bagua = select_index
end

function TianShenBaGuaWGData:GetCurSelectBaGua()
	return self.cur_select_bagua or 0
end

--消耗材料得到合成目标以及合成类型
-- 0不消耗身上 1消耗身上 3无合成目标 4数量不足 
function TianShenBaGuaWGData:GetTargetComposeItem(use_item_id)
	local target_item = 0
	local count = 0
	local count2 = 0
	if self.tianshen_bagua_compose_cfg[use_item_id] then
		target_item = self.tianshen_bagua_compose_cfg[use_item_id].itemid
	end
	if target_item == 0 then 
		return target_item, 3,count,0
	end

 	local can_compsoe,compose_way,compose_type = self:CheckCanComposeItem(target_item)
 	local use_equip = compose_type and 1 or 0
 	if can_compsoe then
 		return target_item,use_equip,compose_way,use_equip
 	else
 		return target_item,4,compose_way,0
 	end

	-- if self.item_id_list[use_item_id] then
	-- 	for k,v in pairs(self.item_id_list[use_item_id]) do
	-- 		if v >=0 then
	-- 			count = count + 1
	-- 		end
	-- 	end
	-- else
	-- 	count = 0
	-- end

	-- local base_cfg = self.tianshen_bagua_id_cfg[use_item_id]
	-- if self.grid_list[base_cfg.index + 1] then
	-- 	if self.grid_list[base_cfg.index + 1][base_cfg.part + 1] and self.grid_list[base_cfg.index + 1][base_cfg.part + 1].item_id == use_item_id then
	-- 		count2 = 1
	-- 	end
	-- end

	-- if count <=1 then
	-- 	return target_item, 4,count,count2
	-- end

	-- if count2 == 1 then
	-- 	return target_item,1,count,count2
	-- end

	-- if count >= 3 then
	-- 	return target_item,0,count,0
	-- end
	-- return target_item, 4,count,0
end

function TianShenBaGuaWGData:GetBaGuaShopItemCfg(item_id)
	if self.tianshen_bagua_shop_cfg and self.tianshen_bagua_shop_cfg[item_id] then
		return self.tianshen_bagua_shop_cfg[item_id]
	end
	return {}
end

--value :true 需要战力计算
function TianShenBaGuaWGData:GetBaGuaEquipAttrAndZhanLi(item_id,value)
	local base_cfg = self.tianshen_bagua_id_cfg[item_id]
	if not base_cfg then return 0 end
	local attr_type = Split(base_cfg.attr_type_list,"|")
	local attr_list = Split(base_cfg.attr_value_list,"|")
	local attr_name_and_value = {}
	local is_pre = {}
	local name_show = ""
	for k,v in pairs(attr_type) do
		name_show = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(v))
		is_pre[name_show] = {}
		is_pre[name_show].is_pre = EquipmentWGData.Instance:GetAttrIsPerByAttrId(tonumber(v))
		is_pre[name_show].attr_type = v
		if name_show and name_show ~= "" then
			attr_name_and_value[name_show] = tonumber(attr_list[k])
			name_show = ""
		end
	end

	if value then
		local _, capability = ItemShowWGData.Instance:OutStrAttrListAndCapality(attr_name_and_value)
		if capability == 1 then 
			capability = 0
		end
		return attr_name_and_value,is_pre,capability
	else
		return attr_name_and_value,is_pre
	end
end

function TianShenBaGuaWGData:GetBaGuaSlotZhanLi(index)
	local info = self:GetTianShenBaGuaEquipInfo(index)
	local zhanli = 0
	local attr_list = {}
	local is_pre = {}
	local all_attr_list,all_is_pre = self:GetBaGuaEmptyAttrTable(index)

	local num = 0
	for i =1,8 do
		if info[i] and info[i].item_id > 0 then
			attr_list,is_pre = self:GetBaGuaEquipAttrAndZhanLi(info[i].item_id)
			for k,v in pairs(attr_list) do
				if all_attr_list[k] then
					all_attr_list[k] = all_attr_list[k] + tonumber(v)
					all_is_pre[k] = is_pre[k]
				else
					all_attr_list[k] = tonumber(v)
					all_is_pre[k] = is_pre[k]
				end
			end
		end
	end
	attr_list, zhanli = ItemShowWGData.Instance:OutStrAttrListAndCapality(all_attr_list)
	if zhanli == 1 then
		zhanli = 0
	end
	local skill_zhanli = self:GetEquipSuitZhanLi(index)
	zhanli = zhanli + skill_zhanli
	return zhanli,all_attr_list,all_is_pre
end

function TianShenBaGuaWGData:GetEquipSuitZhanLi(index)
	local bagua_skill_info = self:GetTianShenSuitSkillInfo(index)
	local is_pre = {}
	local name_show = ""
	local attr_name_and_value = {}
	local skill_zhanli = 0
	if IsEmptyTable(bagua_skill_info) then
		return 0
	end
	for k,v in pairs(bagua_skill_info) do
		if v.is_active then
			for t,q in pairs(v.attr_type_list2) do
				name_show = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(q))
				is_pre[name_show] = {}
				is_pre[name_show].is_pre = EquipmentWGData.Instance:GetAttrIsPerByAttrId(tonumber(v))
				is_pre[name_show].attr_type = v
				if name_show and name_show ~= "" then
					attr_name_and_value[name_show] = tonumber(v.attr_value_list[t])
					name_show = ""
				end
			end
			skill_zhanli = skill_zhanli + v.capability_inc
		end
	end
	local _, capability = ItemShowWGData.Instance:OutStrAttrListAndCapality(attr_name_and_value)
	capability = capability + skill_zhanli
	return capability
end

function TianShenBaGuaWGData:GetTianShenSuitSkillInfo(index)
	local info = self:GetTianShenBaGuaEquipInfo(index)
	local name_info = self:GetBaGuaNameInfo(index)
	local active_count = 0
	local two_star_count = 0
	local three_star_count = 0
	local base_cfg = {}
	for i =1,8 do
		if info[i] and info[i].item_id > 0 then
			base_cfg = self.tianshen_bagua_id_cfg[info[i].item_id]
			active_count = active_count + 1
			if base_cfg.star >= 2 then
				two_star_count = two_star_count + 1
				if base_cfg.star >= 3 then
					three_star_count = three_star_count + 1
				end
			end
		end
	end
	if not self.tianshen_bagua_suit_skill[index] then return {} end

	if not self.bagua_skill_info then
		self.bagua_skill_info = {}
	end
	if not self.bagua_skill_info[index] then
		self.bagua_skill_info[index] = {}
		local skill_info_count = 0
		local skill_info = {}
		if self.tianshen_bagua_suit_skill[index] then
			for k,v in pairs(self.tianshen_bagua_suit_skill[index]) do
				skill_info_count = skill_info_count + 1
				skill_info[skill_info_count] = {}
				skill_info[skill_info_count].skill_id = v.skill_id
				skill_info[skill_info_count].skill_level = v.skill_level
				skill_info[skill_info_count].skill_type = v.skill_type
				skill_info[skill_info_count].param_0 = v.param_0
				skill_info[skill_info_count].count = v.count
				skill_info[skill_info_count].count_star = v.count_star
				skill_info[skill_info_count].skill_des = v.skill_des
				skill_info[skill_info_count].skill_name = v.skill_name
				skill_info[skill_info_count].skill_icon = v.skill_icon
				skill_info[skill_info_count].capability_inc = v.capability_inc
				skill_info[skill_info_count].name = name_info.name

				local attr_type_list = Split(v.attr_type_list,"|")
				local name_show = {}
				local is_pre = {}
				for k,v in pairs(attr_type_list) do
					name_show[k] = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(v))
					is_pre[k] = EquipmentWGData.Instance:GetAttrIsPerByAttrId(tonumber(v))
				end
				skill_info[skill_info_count].attr_type_list = name_show
				skill_info[skill_info_count].attr_type_list2 = attr_type_list
				skill_info[skill_info_count].attr_value_list = Split(v.attr_value_list,"|")
				skill_info[skill_info_count].is_pre = is_pre
			end
			self.bagua_skill_info[index] = skill_info
		else
			self.bagua_skill_info[index] = {}
		end
	end
	if not IsEmptyTable(self.bagua_skill_info[index]) then
		for k,v in pairs(self.bagua_skill_info[index]) do
			if v and v.count then
				v.is_active = v.count <= active_count
				if v.count_star == 2 then
					v.is_active = two_star_count >= 8
				elseif v.count_star == 3 then
					v.is_active = three_star_count >= 8
				end 
				v.two_star_count = two_star_count
				v.three_star_count = three_star_count
				v.active_count = active_count
			end
		end
	end
	return self.bagua_skill_info[index] or {}
end

function TianShenBaGuaWGData:GetWearableEquip(bagua_index,equip_index)

	local bagua_info = self:GetTianShenBaGuaEquipInfo(bagua_index)
	local equip_info = bagua_info[equip_index+1]
	local target_cfg = {}
	local equip_target_list = {}
	local equip_wearable_list = {}
	local equip_wearable_num = 0
	local star = 0

	if equip_info.item_id > 0 then
		local base_cfg = self.tianshen_bagua_id_cfg[equip_info.item_id]
		star = base_cfg.star
	end
	local have_batter = 0

	for i = 3,1,-1 do
		if self.tianshen_bagua_base_cfg[bagua_index][i] then
			target_cfg = self.tianshen_bagua_base_cfg[bagua_index][i][equip_index]
			if self.item_id_list and target_cfg and target_cfg.itemid and self.item_id_list[target_cfg.itemid] then
				for k,v in pairs(self.item_id_list[target_cfg.itemid]) do
					if v ~= -1 and i > star then
						equip_target_list[1] = {}
						equip_target_list[1].item_id = target_cfg.itemid
						equip_target_list[1].index = v
						equip_target_list[1].batter = true
						equip_target_list[1].same = false
						equip_target_list[1].star = i
						--return 1,equip_target_list
						have_batter = 1
					end
					if v ~= -1 then
						equip_wearable_num = equip_wearable_num + 1
						equip_wearable_list[equip_wearable_num] = {}
						equip_wearable_list[equip_wearable_num].item_id = target_cfg.itemid
						equip_wearable_list[equip_wearable_num].index = v
						equip_wearable_list[equip_wearable_num].batter = i > star --equip_info.item_id <= 0
						equip_wearable_list[equip_wearable_num].same = i == star
						equip_wearable_list[equip_wearable_num].star = i
					end
				end
			end
		end
	end
	if equip_info.item_id <= 0 and equip_wearable_num > 0 or have_batter == 1 then
		return 1,equip_wearable_list
	end
	return 0,equip_wearable_list
end

function TianShenBaGuaWGData:GetAdviseShowCfg(part)
	local data = {}
	if self.tianshen_bagua_advise_show[part] then
		data[1] = {}
		data[1].part = self.tianshen_bagua_advise_show[part].part
		data[1].name = self.tianshen_bagua_advise_show[part].name
		data[1].icon = self.tianshen_bagua_advise_show[part].icon

	end
	return data
end

function TianShenBaGuaWGData:GetBaGuaModelID(index)
	if self.tianshen_bagua_advise_show[index] then
		return self.tianshen_bagua_advise_show[index].model_id or 8001
	end
	return 8003
end


function TianShenBaGuaWGData:GetBaGuaBaseInfo(item_id)
	if self.tianshen_bagua_id_cfg then
		return self.tianshen_bagua_id_cfg[item_id] or {}
	end
	return {}
end

function TianShenBaGuaWGData:GetBaGuaBaseInfoByindex(index, star, part)
	local base_data = {}
	if self.tianshen_bagua_base_cfg[index][star] then
		for i,v in pairs(self.tianshen_bagua_base_cfg[index][star]) do
			if v.part == part then
				base_data = v
			break
			end
		end
	end
	return base_data
end

function TianShenBaGuaWGData:GetIsBaGuaItem(item_id)
	if self.tianshen_bagua_id_cfg and self.tianshen_bagua_id_cfg[item_id] then
		return true
	end
	return false
end

function TianShenBaGuaWGData:GetAllBaGuaEquipCfg()
	return self.tianshen_bagua_id_cfg or {}
end

function TianShenBaGuaWGData:GetComposeAbleTable(bagua_index,is_red)
	local bagua_info = self:GetTianShenBaGuaEquipInfo(bagua_index)
	local equip_info
	local bag_num = 0
	local equip_num = 0
	local compose_list = {}
	local have_compose = 0
	if self.tianshen_bagua_base_cfg[bagua_index] then
		for i =1,8 do
			equip_info = bagua_info[i]
			for k,v in pairs(self.tianshen_bagua_base_cfg[bagua_index]) do
				bag_num = 0
				equip_num = 0
				if v and v[i-1] and v[i-1].stuff_id ~= 0 then
					if self.item_id_list[v[i-1].stuff_id] then
						for t,q in pairs(self.item_id_list[v[i-1].stuff_id]) do
							if q >= 0 then
								bag_num = bag_num + 1
							end
						end
						if equip_info.item_id == tonumber(v[i-1].stuff_id) then
							equip_num = equip_num + 1
						end
					end
					if bag_num + equip_num >= v[i-1].stuff_count then
						compose_list[v[i-1].itemid] = {}
						compose_list[v[i-1].itemid].bag_num = bag_num
						compose_list[v[i-1].itemid].equip_num = equip_num
						compose_list[v[i-1].itemid].index = i
						have_compose = 1
						if is_red then
							return 1
						end
					end
				end
			end
		end
		return have_compose,compose_list
	else
		return 0,{}
	end
	return 0,{}
end

function TianShenBaGuaWGData:GetBaGuaCfgByIndexStarPart(index,star,part)
	if self.tianshen_bagua_base_cfg[index] then
		if self.tianshen_bagua_base_cfg[index][star] then
			return self.tianshen_bagua_base_cfg[index][star][part] or {}
		end
	end
	return {}
end

function TianShenBaGuaWGData:GetBaGuaImageByBaGua(cur_select_bagua)
	if cur_select_bagua then
		self.cur_select_bagua = cur_select_bagua
		return
	end

	return self.cur_select_bagua
end

function TianShenBaGuaWGData:GetBaGuaBiCfg()
	return self.tianshen_bagua_other[1].coin_id or 0
end

function TianShenBaGuaWGData:GetBaGuaFlushTime()
	return self.tianshen_bagua_other[1].free_fresh_cd or 0
end

function TianShenBaGuaWGData:GetBaGuaFlushMoney()
	return self.tianshen_bagua_other[1].fresh_cost or 20
end

function TianShenBaGuaWGData:GetFlushBaGuaMoney()
	return self.tianshen_bagua_other[1].fresh_coin or 500
end

function TianShenBaGuaWGData:GetShopRefreshCount()
	return self.tianshen_bagua_other[1].shop_refresh_count or 8
end

function TianShenBaGuaWGData:GetBaGuaEmptyAttrTable(index)
	--self.tianshen_bagua_base_cfg[index][1]

	if self.bagua_empty_attr and self.bagua_empty_attr[index] then
		for k,v in pairs(self.bagua_empty_attr[index]) do
			self.bagua_empty_attr[index][k] = 0
		end
		return self.bagua_empty_attr[index],self.bagua_attr_is_pre[index]
	end

	if not self.bagua_empty_attr then
		self.bagua_empty_attr = {}
		self.bagua_attr_is_pre = {}
	end

	if not self.bagua_empty_attr[index] then
		self.bagua_empty_attr[index] = {}
		self.bagua_attr_is_pre[index] = {}
	end
	if not self.tianshen_bagua_base_cfg[index] then return {} end
	local attr_type = {}
	local name_show
	for k,v in pairs(self.tianshen_bagua_base_cfg[index][1]) do
		attr_type = Split(v.attr_type_list,"|")
		for t,q in pairs(attr_type) do
			name_show = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(q))
			self.bagua_empty_attr[index][name_show] = 0
			self.bagua_attr_is_pre[index][name_show] = {}
			self.bagua_attr_is_pre[index][name_show].is_pre = EquipmentWGData.Instance:GetAttrIsPerByAttrId(tonumber(q))
			self.bagua_attr_is_pre[index][name_show].attr_type = tonumber(q)
		end
	end
	return self.bagua_empty_attr[index],self.bagua_attr_is_pre[index]
end


function TianShenBaGuaWGData:GetBaGuaAttrSortIndex(attr_type)
	if self.tianshen_bagua_attr_index_cfg and self.tianshen_bagua_attr_index_cfg[attr_type] then
		return self.tianshen_bagua_attr_index_cfg[attr_type].attr_sort
	end
	return 1000
end

function TianShenBaGuaWGData:SetInfoChange(value)
	self.info_change_flag = value
end

function TianShenBaGuaWGData:InfoHaveChange()
	return self.info_change_flag or false
end

function TianShenBaGuaWGData:CacularBaGuaSlotOpenList()
	if not self.bagua_slot_open_list then
		self.bagua_slot_open_list = {}
	end

	for i =1,8 do
		if not self.bagua_slot_open_list[i-1] then
			self.bagua_slot_open_list[i-1] = self:CheckBaGuaIsOpen(i-1)
		end
	end
end

function TianShenBaGuaWGData:CheckBaGuaIsOpenNew(index)
	if self.bagua_slot_open_list and self.bagua_slot_open_list[index] then
		return self.bagua_slot_open_list[index]
	end
	return false
end

function TianShenBaGuaWGData:CacularAllBaGuaEquipRed(change_info)
	if change_info then
		if not self.all_bagua_equip_red then
			self.all_bagua_equip_red = {}
		end
		self:__CacularOneEquipRed(tonumber(change_info.item_id))
	else
		self.all_bagua_equip_red = {}
		for k,v in pairs(self.item_id_list) do
			self:__CacularOneEquipRed(tonumber(k))
		end
	end
end

function TianShenBaGuaWGData:__CacularOneEquipRed(item_id)
	self.all_bagua_equip_red[item_id] = {}
	self.all_bagua_equip_red[item_id].equip_red = 0
	self.all_bagua_equip_red[item_id].cross_compose = 0
	self.all_bagua_equip_red[item_id].compose_red = 0
	self.all_bagua_equip_red[item_id].slot_index = 0
	self.all_bagua_equip_red[item_id].bagua_index = 0
	if not self.item_id_list[item_id] then return end

	local base_cfg
	local compse_cfg
	local target_equip_info
	local target_equip_cfg
	local num = 0
	local equip_num = 0
	base_cfg = self.tianshen_bagua_id_cfg[item_id]
	if not base_cfg or not base_cfg.index then return end
	if not self:CheckBaGuaIsOpenNew(base_cfg.index) then return end
	
	self.all_bagua_equip_red[item_id].bagua_index = base_cfg.index
	compse_cfg = self.tianshen_bagua_compose_cfg[item_id]
	target_equip_info = self:GetTianShenBaGuaEquipInfo(base_cfg.index)
	-- if compse_cfg and compse_cfg.stuff_count > 0  then
	-- 	for t,q in pairs(self.item_id_list[item_id]) do
	-- 		if q >=0 then
	-- 			num = num + 1
	-- 		end
	-- 	end
	-- 	if target_equip_info[base_cfg.part +1] and target_equip_info[base_cfg.part +1].item_id == item_id then
	-- 		equip_num = 1
	-- 	end

	-- 	if num >= compse_cfg.stuff_count then
	-- 		self.all_bagua_equip_red[item_id].compose_red = 1
	-- 		self.all_bagua_equip_red[item_id].slot_index = 1000
	-- 	elseif num + equip_num >= compse_cfg.stuff_count and compse_cfg.star ~= 1 then --跨阶合成不用装备的
	-- 		self.all_bagua_equip_red[item_id].compose_red = 1
	-- 		self.all_bagua_equip_red[item_id].slot_index = 1000
	-- 	end
	-- 	if compse_cfg.star == 1 then
	-- 		self.all_bagua_equip_red[item_id].cross_compose = 1
	-- 	end
	-- else
	-- 	self.all_bagua_equip_red[item_id].compose_red = 0
	-- end
	--local can_conpose,compose_way,compose_type = self:CheckCanComposeItem(compse_cfg.itemid) --物品是否能合成
	self.all_bagua_equip_red[item_id].cross_compose = 0-- compose_type and 1 or 0
	self.all_bagua_equip_red[item_id].compose_red = 0--can_conpose and 1 or 0

	for o,p in pairs(self.item_id_list[item_id]) do
		if p >=0 then
			if target_equip_info[base_cfg.part +1].item_id <= 0 then
				self.all_bagua_equip_red[item_id].equip_red = 1
				self.all_bagua_equip_red[item_id].slot_index = 1000
			else
				target_equip_cfg = self.tianshen_bagua_id_cfg[target_equip_info[base_cfg.part +1].item_id]
				if target_equip_cfg.star < base_cfg.star then
					self.all_bagua_equip_red[item_id].equip_red = 1
					self.all_bagua_equip_red[item_id].slot_index = 1000
				end
			end
			break
		end
	end
	self.all_bagua_equip_red[item_id].slot_index = self.all_bagua_equip_red[item_id].slot_index + base_cfg.index * 100 + base_cfg.star * 10 + base_cfg.part 
end

function TianShenBaGuaWGData:GetBaGuaItemRedInfo(item_id)
	if self.all_bagua_equip_red and self.all_bagua_equip_red[item_id] then
		return self.all_bagua_equip_red[item_id]
	end
	return {}
end


function TianShenBaGuaWGData:GetBaGuaWearRed(bagua_index,equip_index)
	local item_red_info
	for i = 1,3 do
		if self.tianshen_bagua_base_cfg[bagua_index] and self.tianshen_bagua_base_cfg[bagua_index][i] then
			if self.tianshen_bagua_base_cfg[bagua_index][i][equip_index].itemid then
				item_red_info = self:GetBaGuaItemRedInfo(self.tianshen_bagua_base_cfg[bagua_index][i][equip_index].itemid)
				if item_red_info.equip_red == 1 then
					return 1
				end
			end
		end
	end
	return 0
end

function TianShenBaGuaWGData:GetAllBaGuaRed()
	return self.all_bagua_equip_red or {}
end

function TianShenBaGuaWGData:SaveTempIndex(open_index)
	self.temp_open_index = open_index - 1
end

function TianShenBaGuaWGData:GetOpenBaGuaSelect()
	-- local temp_index = 0
	-- local bagua_equip_info
	-- for q = 1,8 do
	-- 	bagua_equip_info = TianShenBaGuaWGData.Instance:GetTianShenBaGuaEquipInfo(q-1)
		

	-- 	for i =1,8 do
	-- 		if self:CheckBaGuaIsOpenNew(q-1) then
	-- 			temp_index = q-1
	-- 			if 1 == TianShenBaGuaWGData.Instance:GetBaGuaWearRed(q-1,i-1) then
	-- 				return temp_index
	-- 			else
	-- 				if bagua_equip_info[i] and bagua_equip_info[i].item_id > 0 
	-- 					and TianShenBaGuaWGData.Instance:GetBaGuaItemRedInfo(bagua_equip_info[i].item_id).compose_red == 1 
	-- 					and TianShenBaGuaWGData.Instance:GetBaGuaItemRedInfo(bagua_equip_info[i].item_id).cross_compose ~= 1 then
	-- 					return temp_index
	-- 				end
	-- 			end
	-- 		end
	-- 	end
	-- end
	return self.temp_open_index or 0
end

function TianShenBaGuaWGData:ChackHaveBagua(item_id)
	local count = 0
	if self.item_id_list[item_id] then
		for k,v in pairs(self.item_id_list[item_id]) do
			if v >=0 then
				count = count + 1
			end
		end
	end

	local base_cfg = self.tianshen_bagua_id_cfg[item_id]
	if self.grid_list[base_cfg.index + 1] then
		if self.grid_list[base_cfg.index + 1][base_cfg.part + 1] and self.grid_list[base_cfg.index + 1][base_cfg.part + 1].item_id == item_id then
			count = count + 1
		end
	end

	return count > 0
end

function TianShenBaGuaWGData:ChackHaveBaguaNum(item_id,not_use_equip)
	local count = 0
	local use_equip = false
	if self.item_id_list[item_id] then
		for k,v in pairs(self.item_id_list[item_id]) do
			if v >=0 then
				count = count + 1
			end
		end
	end

	if not not_use_equip then
		local base_cfg = self.tianshen_bagua_id_cfg[item_id]
		if self.grid_list[base_cfg.index + 1] then
			if self.grid_list[base_cfg.index + 1][base_cfg.part + 1] and self.grid_list[base_cfg.index + 1][base_cfg.part + 1].item_id == item_id then
				count = count + 1
				use_equip = true
			end
		end
	end

	return count
end

function TianShenBaGuaWGData:ChackHaveBaguaNum2(item_id,not_use_equip)
	local count = 0
	local use_equip = false
	if self.item_id_list[item_id] then
		for k,v in pairs(self.item_id_list[item_id]) do
			if v >=0 then
				count = count + 1
			end
		end
	end

	if not not_use_equip then
		local base_cfg = self.tianshen_bagua_id_cfg[item_id]
		if self.grid_list[base_cfg.index + 1] then
			if self.grid_list[base_cfg.index + 1][base_cfg.part + 1] and self.grid_list[base_cfg.index + 1][base_cfg.part + 1].item_id == item_id then
				count = count + 1
				use_equip = true
			end
		end
	end

	return count,use_equip
end


--有可直接装备且足够币，没有直接装备且有刷新次数
function TianShenBaGuaWGData:GetBaGuaShopRed()
	-- if self.free_fresh_count and self.free_fresh_count > 0 then
		-- if self.bagua_money and self.bagua_money < self:GetFlushBaGuaMoney() then
		-- 	return 0
		-- end
	-- end
	if self.shop_item_list then
		for k,v in pairs(self.shop_item_list) do
			if v.buy_num == 0 then
				if 1 == self:GetShopItemRed(v.item_id) then
					return 1
				end
			end
		end
	end
	local shop_item_cfg = {}
	local base_cfg = {}
	local one_star_can_wear = false
	local have_need_one_star = false
	if self.shop_item_list then
		for k,v in pairs(self.shop_item_list) do
			if v.buy_num == 0 then
				shop_item_cfg = self:GetBaGuaShopItemCfg(v.item_id)
				base_cfg = self.tianshen_bagua_id_cfg[v.item_id]

				if IsEmptyTable(base_cfg) then
					return 0
				end

				one_star_can_wear = true
				if self.grid_list and self.grid_list[base_cfg.index + 1][base_cfg.part + 1] then
					if self.grid_list[base_cfg.index + 1][base_cfg.part + 1].item_id >0 then
						one_star_can_wear = false
					end
				else
					return 0
				end

				if one_star_can_wear then
					have_need_one_star = true
				end

				if one_star_can_wear and shop_item_cfg.price <= self.bagua_money then
					return 1
				end
			end
		end
	end

	if not have_need_one_star and self.free_fresh_count and self.free_fresh_count > 0 and self.bagua_money and self.bagua_money >= 500 then
		if self.grid_list then
			for i =1,8 do
				if self:CheckBaGuaIsOpenNew(i-1) and self.tianshen_bagua_shop_red_cfg[i] and self.bagua_money >= self.tianshen_bagua_shop_red_cfg[i].fresh_cost then
        			for j = 1,8 do
            			local item_id = self.grid_list[i][j].item_id
            			if item_id > 0 then
            				local base_cfg = self.tianshen_bagua_id_cfg[item_id]
            				if base_cfg and base_cfg.star < 3 then
            					return 1
            				end
            			else
            				return 1
            			end
            		end
            	end
            end
        end
    end
	return 0
end

function TianShenBaGuaWGData:GetShopItemRed(item_id)
	local base_cfg = self.tianshen_bagua_id_cfg[item_id]
	local shop_item_cfg = TianShenBaGuaWGData.Instance:GetBaGuaShopItemCfg(item_id)
	local bagua_money = self.bagua_money or 0
	local price = shop_item_cfg and shop_item_cfg.price or 0  

	if price > bagua_money then
		return 0
	end

	if not base_cfg or not base_cfg.index then 
		return 0
	end

	if base_cfg.index >= 4 then --红色默认提醒
		return 1
	end

	if not self.bagua_shop_rule then
		self:LoadShopSetRule()
	end

	if self.bagua_shop_rule and self.bagua_shop_rule[1] then
		for i =1,3 do
			if self.tianshen_bagua_base_cfg[base_cfg.index] 
				and self.tianshen_bagua_base_cfg[base_cfg.index][i] 
				and self.tianshen_bagua_base_cfg[base_cfg.index][i][base_cfg.part]then
				if self:ChackHaveBagua(self.tianshen_bagua_base_cfg[base_cfg.index][i][base_cfg.part].itemid) then
					return 0
				end
			end 
		end
		return 1
	elseif self.bagua_shop_rule and self.bagua_shop_rule[2] then
		local target_item = 0

		if self.tianshen_bagua_compose_cfg[item_id] then
			target_item = self.tianshen_bagua_compose_cfg[item_id].itemid
		end
		if target_item == 0 then 
			return 0
		end
		local base_cfg_three = self.tianshen_bagua_base_cfg[base_cfg.index][3][base_cfg.part]

		if self:ChackHaveBagua(base_cfg_three.itemid) then
			return 0
		end

		if self:ChackHaveBagua(target_item) then
			return 0
		end

		local target_base_cfg = self.tianshen_bagua_id_cfg[target_item]
		if self:ChackHaveBaguaNum(item_id) >=  target_base_cfg.stuff_count then
			return 0
		end
		return 1

	elseif self.bagua_shop_rule and self.bagua_shop_rule[3] then
		local base_cfg_three = {}

		if self.tianshen_bagua_base_cfg and self.tianshen_bagua_base_cfg[base_cfg.index] and self.tianshen_bagua_base_cfg[base_cfg.index][3] then
			base_cfg_three = self.tianshen_bagua_base_cfg[base_cfg.index][3][base_cfg.part]
		end

		if IsEmptyTable(base_cfg_three) then 
			return 0 
		end

		if self:ChackHaveBaguaNum(base_cfg_three.itemid) > 0 then
			return 0
		end

		local have_num1 = 0

		if base_cfg_three.stuff_id > 0 then
			have_num1 = self:ChackHaveBaguaNum(base_cfg_three.stuff_id)
		end

		if have_num1 >= base_cfg_three.stuff_count then
			return 0
		end

		local lack_num = base_cfg_three.stuff_count - have_num1
		local base_cfg_two = self.tianshen_bagua_base_cfg[base_cfg.index][2][base_cfg.part]

		if IsEmptyTable(base_cfg_two) then 
			return 0 
		end

		if base_cfg_three.stuff_id > 0 then
			have_num1 = self:ChackHaveBaguaNum(base_cfg_two.stuff_id)
		end

		lack_num = lack_num * base_cfg_two.stuff_count
		if base_cfg_two.stuff_count > 0 then
			if lack_num <= have_num1 then
				return 0
			else
				return 1
			end
		else
			return 0
		end
		return 0
	else
		return 0
	end
	-- if self:CheckBaGuaIsOpenNew(base_cfg.index) then
		
	-- end
	return 0
end

function TianShenBaGuaWGData:GetShopFlushPrice(times)
	times = times > 0 and times or 1
	times = times <= 5 and times or 5
	return self.tianshen_bagua_shop_flush_cfg[times].fresh_cost or 20
end

function TianShenBaGuaWGData:GetBaGuaDeCompseCfg(item_id)
	return self.tianshen_bagua_decompose_cfg and self.tianshen_bagua_decompose_cfg[item_id] or {}
end

function TianShenBaGuaWGData:SelectShopSetRule(index)
	if not self.bagua_shop_rule then
		self:LoadShopSetRule()
	end
	for i =1,3 do
		if i == index then
			self.bagua_shop_rule[i] = not self.bagua_shop_rule[i]
		else
			self.bagua_shop_rule[i] = false
		end
	end
	
	self:SaveShopSetRule()
end

function TianShenBaGuaWGData:GetShopSetRule()
	if not self.bagua_shop_rule then
		self:LoadShopSetRule()
	end

	return self.bagua_shop_rule
end

function TianShenBaGuaWGData:SaveShopSetRule()
	local role_id = RoleWGData.Instance:GetUUIDStr()
	for i =1,3 do
		if self.bagua_shop_rule and self.bagua_shop_rule[i] then
			PlayerPrefsUtil.SetInt(role_id.."bagua_shop_set"..i,1)
		else
			PlayerPrefsUtil.SetInt(role_id.."bagua_shop_set"..i,0)
		end
	end
	PlayerPrefsUtil.Save()
end

function TianShenBaGuaWGData:LoadShopSetRule()
	local role_id = RoleWGData.Instance:GetUUIDStr()
	self.bagua_shop_rule = {}
	for i =1, 3 do
		--if PlayerPrefsUtil.HasKey(role_id.."bagua_shop_set"..i) then
		--	self.bagua_shop_rule[i] = PlayerPrefsUtil.GetInt(role_id.."bagua_shop_set"..i) == 1
		--else
			self.bagua_shop_rule[i] = i == 3
			PlayerPrefsUtil.SetInt(role_id.."bagua_shop_set"..i,i == 1 and 1 or 0)
		--end
	end
end



function TianShenBaGuaWGData:CheckNeedPlayShopOpen()
	if not self.shop_view_anim_played then
		return true
	else
		return false
	end

end


function TianShenBaGuaWGData:SetShopOpenHadPlay()
	self.shop_view_anim_played = true
end

function TianShenBaGuaWGData:CheckCanComposeItem(item_id) --物品是否能合成
	local can_conpose, compose_way ,compose_type = self.GetBaGuaComposeWay(item_id,1,false)
	return can_conpose, compose_way,compose_type
end

function TianShenBaGuaWGData:GetBaGuaComposeCfg(item_id)
	return self.tianshen_bagua_compose_cfg[item_id] or {}
end

function TianShenBaGuaWGData.GetBaGuaComposeWay(item_id,num,compose_type)
	local compse_cfg = TianShenBaGuaWGData.Instance:GetBaGuaBaseInfo(item_id)
	if compse_cfg and compse_cfg.stuff_count and compse_cfg.stuff_count > 0 and compse_cfg.stuff_id then
		compose_type = compse_cfg.star == 1 or compose_type
		local have_num,use_equip = TianShenBaGuaWGData.Instance:ChackHaveBaguaNum2(compse_cfg.stuff_id,compose_type)
		local need_num = compse_cfg.stuff_count * num
		local compose_way = {}
		if have_num >= need_num then
			compose_way[1] = {}
			compose_way[1].item_id = compse_cfg.stuff_id
			compose_way[1].have_num = have_num
			compose_way[1].need_num = need_num
			compose_way[1].use_equip = use_equip
			return true,compose_way,compose_type
		else
			local remine_need = need_num - have_num
			local compse_cfg2 = TianShenBaGuaWGData.Instance:GetBaGuaComposeCfg(compse_cfg.stuff_id)
			if compse_cfg2 and compse_cfg.stuff_count and compse_cfg.stuff_id then
				local can_conpose, compose_way2 = TianShenBaGuaWGData.GetBaGuaComposeWay(compse_cfg.stuff_id,remine_need,compose_type)
				if can_conpose then
					local index = 0
					if have_num > 0 then
						index = index + 1
						compose_way[index] = {}
						compose_way[index].item_id = compse_cfg.stuff_id
						compose_way[index].have_num = have_num
						compose_way[index].need_num = have_num
						compose_way[index].use_equip = use_equip
					end

					for k,v in pairs(compose_way2) do
						index = index + 1
						compose_way[index] = {}
						compose_way[index].item_id = v.item_id
						compose_way[index].have_num = v.have_num
						compose_way[index].need_num = v.need_num	
						compose_way[index].use_equip = v.use_equip
					end
					return can_conpose,compose_way,compose_type
				else
					compose_way[1] = {}
					compose_way[1].item_id = compse_cfg.stuff_id
					compose_way[1].have_num = have_num
					compose_way[1].need_num = need_num
					compose_way[1].use_equip = use_equip
					return false, compose_way,compose_type
				end
			else
				compose_way[1] = {}
				compose_way[1].item_id = compse_cfg.stuff_id
				compose_way[1].have_num = have_num
				compose_way[1].need_num = need_num
				compose_way[1].use_equip = use_equip
				return false,compose_way,compose_type
			end
		end
	else
		return false,{},compose_type
	end
end

function TianShenBaGuaWGData:SetHadClickSet(value)
	self.set_been_click = value
end

function TianShenBaGuaWGData:GetHadClickSet()
	return self.set_been_click
end

function TianShenBaGuaWGData:GetIsEmptyPartEquip(bagua_index,part)
	if self.tianshen_bagua_base_cfg[bagua_index] then
		for i=1,3 do
			if self.tianshen_bagua_base_cfg[bagua_index][i] and self.tianshen_bagua_base_cfg[bagua_index][i][part] then
				local have_num = self:ChackHaveBaguaNum(self.tianshen_bagua_base_cfg[bagua_index][i][part].itemid)
				if have_num > 0 then
					return false
				end
			end
		end
	end
	return true
end
