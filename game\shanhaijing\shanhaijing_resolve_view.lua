SHJFenjieView = SHJFenjieView or BaseClass(SafeBaseView)
local MAX_SEND_BREAK_NUM = 100
function SHJFenjieView:__init()
	self:LoadConfig()
end

-- 加载配置
function SHJFenjieView:LoadConfig()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(950, 592)})
	self:AddViewResource(0, "uis/view/shj_ui_prefab", "layout_fenjie")
end

function SHJFenjieView:ReleaseCallBack()
	if self.juhun_fenjie then
		self.juhun_fenjie:DeleteMe()
		self.juhun_fenjie = nil
	end

end

function SHJFenjieView:LoadCallBack(index, loaded_times)
	self.node_list.title_view_name.text.text = Language.ShanHaiJing.Title_Name_1

	for i=1, 5 do
		self.node_list["btn_checkbox" .. i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickFjColorHandler, self, i))
	end
	XUI.AddClickEventListener(self.node_list["btn_fengjie"], BindTool.Bind1(self.OnClickFenJie, self))
	XUI.AddClickEventListener(self.node_list["btn_getway"], BindTool.Bind1(self.OnClickGetWay, self))
	self:CreateFenJieGird()
end

function SHJFenjieView:CreateFenJieGird()
	if not self.juhun_fenjie then
		self.juhun_fenjie = AsyncBaseGrid.New()
		self.juhun_fenjie:SetStartZeroIndex(false)
		self.juhun_fenjie:SetIsMultiSelect(true)
		self.juhun_fenjie:SetIsShowTips(false)
		self.juhun_fenjie:CreateCells({col = 9, cell_count = 108, itemRender = SHJBaseCell,
			list_view = self.node_list["ph_fenjie_item"]})
		self.juhun_fenjie:SetSelectCallBack(BindTool.Bind1(self.SelectJuHunFenJieBagCallBack, self))
	end
end

-- 获取本地勾选配置
function SHJFenjieView:FlushLocalFlag()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local is_frist = PlayerPrefsUtil.GetInt("shj_resolve_is_first" .. role_id)
	is_frist = is_frist == 0
	if is_frist then
		PlayerPrefsUtil.SetInt("shj_resolve_is_first" .. role_id,1)
		PlayerPrefsUtil.SetInt("shj_resolve_" .. role_id .. "_" .. 1,1)
	end

	for i=1,5 do
		self.node_list["btn_checkbox" .. i].toggle.isOn = PlayerPrefsUtil.GetInt("shj_resolve_" .. role_id.. "_" .. i) == 1
	end
end

function SHJFenjieView:CloseCallBack()
	if self.node_list then
		self.closeing = true
		for i=1,5 do
			if self.node_list["btn_checkbox" .. i] then
				self.node_list["btn_checkbox" .. i].toggle.isOn = false
			end
		end

		if self.node_list.effect_root then
			self.node_list.effect_root:SetActive(false)
		end
		self.closeing = false
	end
end

function SHJFenjieView:OnFlush()

	self:FlushLocalFlag()
	-- 刷新列表
	self:FlushFenJieList()
	-- 刷新分解获得
	self:FlushFenJieNum()

end

function SHJFenjieView:OnClickFj()
	for i=1,5 do
		self:OnClickFjColorHandler(i, true)
	end
end

function SHJFenjieView:OnClickFjColorHandler(index, isOn)
	if self.closeing then return end
	local tj_card_type = ShanHaiJingWGData.Instance:GetCardType()
	self.data_list = ShanHaiJingWGData.Instance:GetResolveInfoList(tj_card_type)
	local select_list = ShanHaiJingWGData.Instance:GetFenJieGridByColor(index + 1,self.data_list)
	for k,v in pairs(select_list) do
		self.juhun_fenjie.select_tab[1][v] = isOn and true or nil
	end

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	PlayerPrefsUtil.SetInt("shj_resolve_" .. role_id .. "_" .. index,isOn and 1 or 0)

	self:Flush()
end


function SHJFenjieView:ShowIndexCallBack(index)
	self:Flush()
	self:OnClickFj()
end

function SHJFenjieView:FlushFenJieList()
	local tj_card_type = ShanHaiJingWGData.Instance:GetCardType()
	self.data_list = ShanHaiJingWGData.Instance:GetResolveInfoList(tj_card_type)

	if self.juhun_fenjie then
		self.juhun_fenjie:SetDataList(self.data_list, 3)
	end
end

function SHJFenjieView:FlushFenJieNum()
	local select_list = self.juhun_fenjie:GetAllSelectCell()
	local consume_item_id = ShanHaiJingWGData.Instance:GetTJConsume()
	local has_num = ShanHaiJingWGData.Instance:GetItemNumInBagById(consume_item_id)
	local total_num = 0
	for k,v in pairs(select_list) do
		if v.tujiancfg then
			total_num = total_num + (v.tujiancfg.break_reward.num * v.num)
		end
	end
	self.node_list.lbl_fenjie_num.text.text = string.format(Language.ShanHaiJing.TuJianFenjieNum, total_num) 
end

-------  点击分解 ---------------------
function SHJFenjieView:OnClickFenJie()
	local select_list = self.juhun_fenjie:GetAllSelectCell()
	local tj_card_type = ShanHaiJingWGData.Instance:GetCardType()
	local data_list = ShanHaiJingWGData.Instance:GetResolveInfoList(tj_card_type)	
	if #data_list < 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ShanHaiJing.Btn_Tip_4)
		return
	end

	if #select_list == 0 then 
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ShanHaiJing.Btn_Tip_5)
		return
	end

	-- for k,v in pairs(select_list) do
	-- 	ShanHaiJingWGCtrl.Instance:CSTujianOperaReq(TUJIAN_OP_TYPE.TUJIAN_OP_TYPE_BREAK,v.item_id,v.num)
	-- end
	local all_count = #select_list
	if all_count <= MAX_SEND_BREAK_NUM then
		ShanHaiJingWGCtrl.Instance:SendBreakListOpera(select_list)
	else
		local count = math.ceil(all_count / 100)
		local star_index = 1
		for i = 1, count do
			local page_list = {}
			local show_index = 1
			local end_index = i * MAX_SEND_BREAK_NUM
			if end_index <= all_count then
				for k = star_index, MAX_SEND_BREAK_NUM do
					page_list[show_index] = all_count[star_index]
					show_index = show_index + 1
					star_index = star_index + 1
				end
			else
				for k = star_index, all_count do
					page_list[show_index] = all_count[star_index]
					show_index = show_index + 1
					star_index = star_index + 1
				end
			end
			
			ShanHaiJingWGCtrl.Instance:SendBreakListOpera(page_list)
		end
	end
	self.juhun_fenjie:CancleAllSelectCell()

	local bundle_name1, asset_name1 = ResPath.GetEffectUi("UI_fuwenfenjie_g_04")
	self.node_list.effect_root:SetActive(true)
	EffectManager.Instance:PlayAtTransform(bundle_name1, asset_name1, self.node_list["effect_root"].transform, 4, Vector3(0, 32, 0),nil,nil)
end

-- 格子点击回调
function SHJFenjieView:SelectJuHunFenJieBagCallBack(cell)
	if nil == cell then return end
	self:Flush()
end

function SHJFenjieView:OnClickGetWay()
	TipWGCtrl.Instance:OpenItemTipGetWay({item_id = 90633})
end

SHJBaseCell = SHJBaseCell or BaseClass(ItemCell)
function SHJBaseCell:__init()
	self:NeedDefaultEff(false)
	self:UseNewSelectEffect(true)
end

function SHJBaseCell:SetSelect(is_select, item_call_back)
	ItemCell.SetSelectEffect(self, is_select)
end
