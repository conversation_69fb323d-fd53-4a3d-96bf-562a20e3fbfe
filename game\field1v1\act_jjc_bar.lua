ActJjcBar = ActJjcBar or BaseClass(Tabbar)

local ActJjcBarCellName = "arena_bar_render"
local ANIM_POS = {
	[1] = 0,
	[2] = -219,
	[3] = -438,
	[4] = -657,
	[5] = -876,
}

local BarType = {
	--jjc = 1,
	tianti = 1,
	kf_1v1 = 2,
	worlds_no1 = 3,
	ts_zc = 4,
	kf_3v3 = 5,
	kf_guild = 6,
}

function ActJjcBar:__init(parent)
	self.ver_path = "uis/view/field1v1_ui_prefab"
	if parent.arena_list then
		self.ver_list = parent.arena_enter
		self.ver_list_content = parent.arena_list
		self:SetInstance(self.ver_list)
	end
	self.async_loader_list = {}
	self.ver_bar_enabled_list = {}
end

function ActJjcBar:__delete()
	self.need_anim = nil
	self.async_loader_list = {}
end

-- 根据index取按钮
function ActJjcBar:GetToggleByIndex(index)
	index = index - 10
	local view, callback
	local ver = math.floor(index / 10)
	if self.ver_cell_list[ver] and not self.ver_cell_list[ver]:IsOn() then
		view = self.ver_cell_list[ver].view
		callback = function() self:ChangeToIndex(index + 10) end
	elseif self.hor_cell_list[index % 10] then
		view = self.hor_cell_list[index % 10].view
		callback = function() self:ChangeToIndex(index + 10) end
	end
	if view and view.rect and view.rect.rect.width > 0 and view.rect.rect.height > 0 then
		return view, callback
	end
end

function ActJjcBar:LoadVerCell()
	if next(self.ver_cell_list) ~= nil then
		for _,v in pairs(self.ver_cell_list) do
			v:DeleteMe()
		end
	end
	self.ver_cell_list = {}

	for k,v in pairs(self.async_loader_list) do
		v:Destroy()
	end

	local ver_num = self:GetVerListViewNumbers()
	for i=1,ver_num do
		local async_loader = AllocAsyncLoader(self, "loader" .. i)
		self.async_loader_list[i] = async_loader
		async_loader:SetActive(false)
		async_loader:SetParent(self.node_list["arena_list"].transform)
		async_loader:Load(self.ver_path, ActJjcBarCellName, function (obj)
			if nil == obj then
				return
			end

			local ver_item_cell = ArenaBarRender.New(obj)

			local index = #self.ver_cell_list + 1
			if self.remind_val_v_t[index] then
				ver_item_cell:ShowRemind(self.remind_val_v_t[index] > 0)
			end

			ver_item_cell:AddClickEventListener(BindTool.Bind(self.VerListEventCallback, self))

			local ver_item_data = self.ver_tab[i]
			ver_item_cell:SetIndex(i)
			ver_item_cell:SetData(ver_item_data)
			self.ver_cell_list[index] = ver_item_cell

			if self.ver_bar_enabled_list[index] == nil then
				self.ver_bar_enabled_list[index] = true
			end

			if not ver_item_cell:IsOn() and self.select_hor_index[index] and self.select_ver_index == index then
				ver_item_cell:ChangeHL(true)
			end

			if ver_num == index and self.create_ver_call_back then
				self.create_ver_call_back()
			end

			if i == ver_num and self.need_anim then
				self:PlayAnim(self.need_anim)
				self.need_anim = nil
			end
		end)
	end
end

function ActJjcBar:SetGraphicGrey(gameObject,index)
	if index > #Language.Field1v1.TabGrop then
		XUI.SetGraphicGrey(gameObject,true)
	else
		local bool = FunOpen.Instance:GetFunIsOpened(TabbarTipType[index * 10])
		if index * 10 == TabIndex.arena_kf3v3 then
			local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
			local limit_open_day = KF3V3WGData.Instance:GetOpenDay()
			bool = bool and open_day >= limit_open_day
		end

		XUI.SetGraphicGrey(gameObject, not bool)
	end

	self:SetPrepareSceneOpen(gameObject, index)
end

function ActJjcBar:SetPrepareSceneOpen(gameObject,index)
	local scene_id = Scene.Instance:GetSceneType()
	if scene_id == SceneType.Kf_OneVOne_Prepare then
		if nil == gameObject then
			for k,v in pairs(self.ver_cell_list) do
				XUI.SetGraphicGrey(v.view, k ~= BarType.kf_1v1)
			end
		else
			if index ~= BarType.ts_zc then
				XUI.SetGraphicGrey(gameObject, true)
			end
		end
	elseif scene_id == SceneType.Kf_PvP_Prepare then
		if nil == gameObject then
			for k,v in pairs(self.ver_cell_list) do
				XUI.SetGraphicGrey(v.view, k ~= BarType.kf_3v3)
			end
		else
			if index ~= BarType.kf_3v3 then
				XUI.SetGraphicGrey(gameObject ,true)
			end
		end
	end
end

function ActJjcBar:GetCellLength()
	return #self.ver_cell_list
end

function ActJjcBar:SetAnimCallBack(callback)
	self.anim_callback = callback
end

function ActJjcBar:PlayAnim(times, callback)
	self.anim_callback = self.anim_callback or callback
	local ver_num = self:GetVerListViewNumbers()
	if ver_num == #self.ver_cell_list then
		if self.anim_callback then
			self.anim_callback()
			self.anim_callback = nil
		end

		local enabled_list = self.ver_bar_enabled_list
		local anim_index = 1
		for i,v in ipairs(self.ver_cell_list) do
			v:SetActive(enabled_list[i])
			if enabled_list[i] then
				--v:StartAnim(times, anim_index)
				anim_index = anim_index + 1
			end
		end
	else
		self.need_anim = times
	end
end

function ActJjcBar:ResetAnimPos()
	for i,v in ipairs(self.ver_cell_list) do
		v:ResetPos()
	end
end

function ActJjcBar:VerListEventCallback(cell)
	self.select_ver_index = cell:GetIndex() + 1
	self:CheckHorCellSelect()
	local hor_num = self:GetHorListViewNumbers()
	local callback_index = self.select_hor_index[self.select_ver_index] and (self.select_ver_index * 10 + self.select_hor_index[self.select_ver_index]) or self.select_ver_index * 10
	for i,v in ipairs(self.hor_cell_list) do
		if not v.tab_loading then
			v:ShowRemind(false)
			v:SetActive(i <= hor_num)
		end
	end

	if self.hor_list and hor_num > 0 then
		self:LoadHorCell()
	elseif hor_num == 0 then
		if self.tabbar_call_back then
			self.tabbar_call_back(callback_index)
		end
	end
end

function ActJjcBar:SetToggleVisible(k, visible)

end

function ActJjcBar:SetVerToggleVisble(index, visible)
	local ver = math.floor(index / 10) - 1
	self.ver_bar_enabled_list[ver] = visible

	if self.ver_cell_list[ver] ~= nil then
		self.ver_cell_list[ver]:SetActive(visible)
	end
end

----------------------------------------------------------------

ArenaBarRender = ArenaBarRender or BaseClass(BaseRender)

function ArenaBarRender:LoadCallBack()
	self.need_flush_second = true
	self:OnFlushBySecond()
end

function ArenaBarRender:__delete()
	self.data_index = nil
	self.need_flush_second = nil

	if self.anim_delay then
		GlobalTimerQuest:CancelQuest(self.anim_delay)
		self.anim_delay = nil
	end
	
	if self.second_flush_delay then
		GlobalTimerQuest:CancelQuest(self.second_flush_delay)
		self.second_flush_delay = nil
	end
end

function ArenaBarRender:OnFlushBySecond()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local time_table = os.date("*t", server_time)
	local weekday_num = tonumber(os.date("%w",server_time))
	if nil == self.data_index then
		self.second_flush_delay = GlobalTimerQuest:AddDelayTimer(function ()
		if self.need_flush_second then
			self:OnFlushBySecond()
		end
	end,1)
		return
	end

	local is_lock = not ActivityWGData.Instance:GetActivityIsOpenByLevel(ACTIVITY_TYPE.KF_ONEVONE)

    local zhanchang_info = Field1v1WGData.Instance:GetChallengeFieldOtherCfg()
    self.node_list.open_act_tiaojian.text.text = ""
    self.node_list.open_act_time.text.text = ""

	local play_text = Language.Field1v1.JJCNoOpen
	local is_playing = false
    if self.data_index == BarType.jjc then
    	play_text = Language.Field1v1.JJCJinXingZhong
		is_playing = true

    	self.node_list.open_act_tiaojian.text.text = Language.Field1v1.JJCAct
    	self.node_list.open_act_time.text.text = Language.Field1v1.JJCJieSuan

	elseif self.data_index == BarType.tianti then
		play_text = Language.Field1v1.JJCJinXingZhong
		is_playing = true

    	self.node_list.open_act_tiaojian.text.text = Language.Field1v1.TianTiTxt15
    	self.node_list.open_act_time.text.text = Language.Field1v1.TianTiTxt16
    elseif self.data_index == BarType.kf_1v1 then
    	local time_str_start_tab = Split(zhanchang_info.onevone_start, ":")
    	local time_str_tab = Split(zhanchang_info.onevone_end, ":")
    	local act_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_ONEVONE)
		XUI.SetGraphicGrey(self.node_list.enter_bg, is_lock)
		play_text = act_open and Language.Field1v1.JJCJinXingZhong or Language.Field1v1.JJCNoOpen
		is_playing = act_open
    	local zhou_str,start_time, end_time = BiZuoWGData.Instance:GetActOpenTimeStr(ACTIVITY_TYPE.KF_ONEVONE)
    	self.node_list.open_act_tiaojian.text.text = string.format(Language.Field1v1.ZhouStrFormat,zhou_str)
    	self.node_list.open_act_time.text.text = string.format(Language.Field1v1.TimeStrOpen,start_time)

    -- 天神3v3
    elseif self.data_index == BarType.ts_zc then
    	local zhou_str, start_time, end_time,is_every_day = BiZuoWGData.Instance:GetActOpenTimeStr(ACTIVITY_TYPE.TIANSHEN_3V3)
	    local time_str = start_time .. "-" .. end_time
	    if is_every_day then
	    	self.node_list.open_act_tiaojian.text.text = zhou_str
	    else
	    	self.node_list.open_act_tiaojian.text.text = string.format(Language.TianShen3v3.Week, zhou_str)
	    end
    	self.node_list.open_act_time.text.text = time_str
    	play_text = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.TIANSHEN_3V3) and Language.Field1v1.JJCJinXingZhong or Language.Field1v1.JJCNoOpen
		is_playing = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.TIANSHEN_3V3)

    elseif self.data_index == BarType.kf_3v3 then
    	-- 屏蔽跨服3v3
		local time_str_start_tab = Split(zhanchang_info.pvp_start,":")
		local time_str_tab = Split(zhanchang_info.pvp_end,":")
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local limit_open_day = KF3V3WGData.Instance:GetOpenDay()
		local act_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_PVP)
		play_text = act_open and Language.Field1v1.JJCJinXingZhong or Language.Field1v1.JJCNoOpen
		is_playing = act_open
		local zhou_str, start_time, end_time = BiZuoWGData.Instance:GetActOpenTimeStr(ACTIVITY_TYPE.KF_PVP)
		self.node_list.open_act_tiaojian.text.text = string.format(Language.Field1v1.ZhouStrFormat,zhou_str)
		self.node_list.open_act_time.text.text = string.format(Language.Field1v1.TimeStrOpen,start_time)

    elseif self.data_index == BarType.kf_guild then
    	self.node_list.open_act_tiaojian.text.text = Language.Field1v1.LJZBAct
    	local open_sever_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    	if open_sever_day >= zhanchang_info.ljzb_open_day then
    		local time_str_tab = Split(zhanchang_info.ljzb_start,":")
    		--local time_str_tab2 = Split(zhanchang_info.ljzb_end,":")
			local act_open = weekday_num == 6 and tonumber(time_table.hour) == tonumber(time_str_tab[1]) and tonumber(time_table.min) > tonumber(time_str_tab[2])
			play_text = act_open and Language.Field1v1.JJCJinXingZhong or Language.Field1v1.JJCNoOpen
			is_playing = act_open
			self.node_list.open_act_time.text.text = Language.Field1v1.LJZBActKaiQi
    	else
			play_text = Language.Field1v1.JJCNoOpen
			is_playing = false
    		self.node_list.open_act_time.text.text = Language.Field1v1.LJZBDayKaiQi
    	end

    elseif self.data_index == BarType.worlds_no1 then
    	local zhou_str, start_time, end_time,is_every_day = BiZuoWGData.Instance:GetActOpenTimeStr(ACTIVITY_TYPE.WORLDS_NO1)
	    local time_str = start_time .. "-" .. end_time
	    if is_every_day then
	    	self.node_list.open_act_tiaojian.text.text = zhou_str
	    else
	    	self.node_list.open_act_tiaojian.text.text = string.format(Language.WorldsNO1.Week, zhou_str)
	    end
    	self.node_list.open_act_time.text.text = time_str
		play_text = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.WORLDS_NO1) and Language.Field1v1.JJCJinXingZhong or Language.Field1v1.JJCNoOpen
		is_playing = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.WORLDS_NO1)
    else
    	self.node_list.open_act_tiaojian:SetActive(false)
    	self.node_list.open_act_time:SetActive(false)
    	is_playing = false
		play_text = Language.Field1v1.JJCNoOpen
    	self:ShowRemind(false)
    end

    self.second_flush_delay = GlobalTimerQuest:AddDelayTimer(function ()
		if self.need_flush_second then
			self:OnFlushBySecond()
		end
	end,1)

	self.node_list.is_playing:SetActive(not is_lock and is_playing)
	self.node_list.is_playing_2:SetActive(not is_lock and not is_playing)
	self.node_list.play_text.text.text = play_text
	self.node_list.play_text_2.text.text = play_text
end

function ArenaBarRender:OnFlush()
	if not self.data then
		return 
	end
	self:GetSelfDataIndex()

	if self.data_index < BarType.kf_guild then
		self.node_list.enter_bg.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("a3_jjc_act_bg_" .. self.data_index))
		self.node_list.enter_bg.raw_image:SetNativeSize()

		self.node_list.explain_text.text.text = Language.Field1v1.TabGrop[self.data_index]
	end

	self.node_list.down_info:SetActive(self.data_index < BarType.kf_guild)
	self.node_list.none_text:SetActive( not (self.data_index < BarType.kf_guild))

	self:OnFlushBySecond()
end

function ArenaBarRender:LoadImage( node, bundle, asset )
	if node.image and bundle and asset then
		node.image:LoadSprite(bundle, asset, function()
			node.image:SetNativeSize()
		end)
	end
end

function ArenaBarRender:GetSelfDataIndex()
	if not self.data_index then
		self.data_index = 0
		for k,v in pairs(Language.Field1v1.TabGrop) do
			if v == self.data then
				self.data_index = k
			end
		end
	end
end

function ArenaBarRender:ShowRemind(visible, temp)
	self.node_list["RedPoint"]:SetActive(visible)
end

function ArenaBarRender:SetToggleGroup(group)
	self.view.toggle.group = group
end

function ArenaBarRender:ChangeHL(is_HL)
	
end

function ArenaBarRender:IsOn()
	-- return self.view.toggle.isOn
end

function ArenaBarRender:SetActive(visible)
	self.view:SetActive(visible)
end

function ArenaBarRender:StartAnim(times, anim_index)
	self:GetSelfDataIndex()

	local anim_type = times and times % 6 or 2
	if anim_type == 0 then
		self:ResetPos(ANIM_POS[anim_index])
		self.node_list["content"].rect:DOAnchorPosX(0, 0.6)
	elseif anim_type == 1 then
		self:ResetPos(ANIM_POS[anim_index])
		self.node_list["content"].rect:DOAnchorPosX(0, 0.6)
	elseif anim_type == 2 then
		local pos = ANIM_POS[#ANIM_POS - anim_index + 1]
		self:ResetPos(0 - pos)
		self.node_list["content"].rect:DOAnchorPosX(0, 0.6)

	elseif anim_type == 3 then
		local pos = ANIM_POS[#ANIM_POS - anim_index + 1]
		self:ResetPos(0 - pos)
		self.node_list["content"].rect:DOAnchorPosX(0, 0.6)
	elseif anim_type == 4 then
		self.node_list["content"].rect:DOAnchorPosY(0, 0.6)

	elseif anim_type == 5 then
		self.node_list["content"].rect.anchoredPosition = Vector2(0, anim_index % 2 == 1 and 768 or -768)
		self.node_list["content"].rect:DOAnchorPosY(0, 0.6)
	end
end

function ArenaBarRender:ResetPos(value)
	RectTransform.SetAnchoredPositionXY(self.node_list["content"].rect, value or 0, value and 0 or 768)
end

function ArenaBarRender:SetGraphicGrey()
	local bool = FunOpen.Instance:GetFunIsOpened(TabbarTipType[(self.data_index + 1)*10])
	if self.data_index == BarType.ts_zc then
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		XUI.SetGraphicGrey(self.view, not bool or open_day < KF3V3WGData.Instance:GetOpenDay())
	else
		XUI.SetGraphicGrey(self.view,not bool)
	end
	self:SetPrepareSceneOpen()
end

function ArenaBarRender:SetPrepareSceneOpen()
	local scene_id = Scene.Instance:GetSceneType()
	if scene_id == SceneType.Kf_OneVOne_Prepare then
		if self.data_index ~= BarType.kf_1v1 then
			XUI.SetGraphicGrey(self.view,true)
		end
	elseif scene_id == SceneType.Kf_PvP_Prepare then
		if self.data_index ~= BarType.ts_zc then
			XUI.SetGraphicGrey(self.view,true)
		end
	end
end