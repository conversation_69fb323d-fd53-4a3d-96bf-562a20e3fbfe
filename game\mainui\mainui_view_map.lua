MainUIViewMap = MainUIViewMap or BaseClass(BaseRender)

-- 这里将
function MainUIViewMap:__init(country_icon, map_name, map_pos)
	-- 获取变量
	self.map_time = self.node_list["MapTime"]
	self.battery_slider = self.node_list["Batteryslider"]
	self.net_work_state = self.node_list["NetWorkState"]
	self.net_work_state:SetActive(false)

	-- 监听系统事件.
	self.main_role_pos_change_handle = GlobalEventSystem:Bind(
		ObjectEventType.MAIN_ROLE_POS_CHANGE,
		BindTool.Bind(self.OnMainRolePosChange, self))

	self.time_quest = GlobalTimerQuest:AddTimesTimer(
		BindTool.Bind(self.OnUpdateTime, self), 5, 999999999)

	self.net_work_state_change = GlobalEventSystem:Bind(
		NetWorkStateEvent.NET_WORK_STATE_CHANGE,
		BindTool.Bind(self.NetWorkStateChange, self))
end

-- A2将事件和网络状态换到下方了，不影响其他功能增加一个初始化
function MainUIViewMap:NewInit(map_name, map_pos)
	self.map_name = map_name
	self.map_pos = map_pos
	-- 初始化
	self:OnSceneLoaded()
	local state, msg = SubpackageWGData.Instance:GetNetWorkState()
	self:NetWorkStateChange(state, msg)

	local main_role = Scene.Instance:GetMainRole()
	self:OnMainRolePosChange(main_role:GetLogicPos())
end

function MainUIViewMap:ReleaseCallBack()
	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end

	self.map_name = nil
	self.map_pos = nil
	self.map_time = nil
	self.battery_slider = nil

	GlobalEventSystem:UnBind(self.main_role_pos_change_handle)
	GlobalEventSystem:UnBind(self.net_work_state_change)
	self.net_work_state = nil
end

function MainUIViewMap:OnSceneLoaded()
	local map_name = Scene.Instance:GetSceneName()
	self.timer = os.date("%H:%M", TimeWGCtrl.Instance:GetServerTime())
	self.map_name.text.text = map_name
	self:OnUpdateTime()
end

function MainUIViewMap:OnMainRolePosChange(x, y)
	self.map_pos.text.text = string.format("（%s,%s）", x, y)
end

function MainUIViewMap:OnUpdateTime()
	self.timer = os.date("%H:%M", TimeWGCtrl.Instance:GetServerTime())
	self.map_time.text.text = self.timer
	if UnityEngine.SystemInfo.batteryLevel >= 0 then
		self.battery_slider.slider.value = UnityEngine.SystemInfo.batteryLevel * 100
	end
end

function MainUIViewMap:NetWorkStateChange(state, msg)
	if state == 0 then
		self.net_work_state.text.text = "无网络"
	elseif state == 1 then
		self.net_work_state.text.text = "移动网络"
	elseif state == 2 then
		self.net_work_state.text.text = "WIFI"
	else
		self.net_work_state.text.text = "未知网络"
	end
end
