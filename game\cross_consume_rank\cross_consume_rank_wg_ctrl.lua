require("game/cross_consume_rank/cross_consume_rank_wg_data")
require("game/cross_consume_rank/cross_consume_rank_view")
require("game/cross_consume_rank/cross_consume_view")

CrossConsumeRankWGCtrl = CrossConsumeRankWGCtrl or BaseClass(BaseWGCtrl)

function CrossConsumeRankWGCtrl:__init()
    if CrossConsumeRankWGCtrl.Instance then
		error("[CrossConsumeRankWGCtrl]:Attempt to create singleton twice!")
	end
   -- print_error("跨服消费榜")
    CrossConsumeRankWGCtrl.Instance = self

    self.data = CrossConsumeRankWGData.New()
    self.view = CrossConsumeView.New(GuideModuleName.CrossConsumeView)
    self.view_consume_rank = CrossConsumeRankView.New()
    
    self:RegisterProtocol(SCCrossConsumeRankInfo, "OnSCCrossConsumeRankInfo")

end

function CrossConsumeRankWGCtrl:__delete()
    if self.view  then
        self.view:DeleteMe()
        self.view = nil
    end

    if self.data ~= nil then
        self.data:DeleteMe()
        self.data = nil
    end

    if self.view_consume_rank then
        self.view_consume_rank:DeleteMe()
        self.view_consume_rank = nil
    end
    
    CrossConsumeRankWGCtrl.Instance = nil
end

function CrossConsumeRankWGCtrl:SendCrossConsumeReq(opera_type,param_1,param_2,param_3)
	local param_t = {}
	param_t.activity_type = ACTIVITY_TYPE.CROSS_CHANNEL_ACTIVITY_TYPE_CONSUME_RANK
	param_t.opera_type = opera_type or 0
	param_t.param_1 = param_1 or 0
	param_t.param_2 = param_2 or 0
	param_t.param_3 = param_3 or 0
	ActivityWGCtrl.Instance:SendCSCrossChannelActivityRequest(param_t)
end

function CrossConsumeRankWGCtrl:OnSCCrossConsumeRankInfo(protocol)
   -- print_error("protocol", protocol)
    self.data:SetConRankInfo(protocol)
    if self.view:IsOpen() then
        self.view:Flush(0, "rank_list")
    end
end

--打开消费排行榜的方法
function CrossConsumeRankWGCtrl:OpenConsumeRankView()
    self.view_consume_rank:Open()
end
