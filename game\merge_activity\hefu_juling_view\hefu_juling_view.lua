MergeActivityView = MergeActivityView or BaseClass(SafeBaseView)

function MergeActivityView:LoadIndexCallBackHeFuJuLing()
	self.cur_select_toggle = 1
	self.is_show_friend_list = false

	self.node_list.friend_btn.button:AddClickListener(BindTool.Bind(self.OnClickShowInviteGroupBtn, self, true))
	self.node_list.jl_invite_btn_group_mask.button:AddClickListener(BindTool.Bind(self.OnClickShowInviteGroupBtn, self, false))
	self.node_list.jl_world_invite_btn.button:AddClickListener(BindTool.Bind(self.OnClickWorldInviteBtn, self))
	self.node_list.jl_guild_invite_btn.button:AddClickListener(BindTool.Bind(self.OnClickGuildInviteBtn, self))
	self.node_list.jl_friend_invite_btn.button:AddClickListener(BindTool.Bind(self.OnClickFriendBtn, self))

	self.node_list.back_selfpot_btn.button:AddClickListener(BindTool.Bind(self.OnClickBackSelfPotBtn, self))

	self.node_list.juling_reward_show_btn.button:AddClickListener(BindTool.Bind(self.OnClickJulingRewardShowBtn, self))

	for i = 1, 6 do
		self.node_list["zhuling_root_"..i].button:AddClickListener(BindTool.Bind(self.OnClickOtherHelpPlayer, self, i))
	end

	self.node_list.plant_btn.button:AddClickListener(BindTool.Bind(self.OnClickPlantBtn, self))
	self.node_list.zhuling_btn.button:AddClickListener(BindTool.Bind(self.OnClickZhuLingBtn, self))
	self.node_list.invite_zhuling_btn.button:AddClickListener(BindTool.Bind(self.OnClickInviteZhuLingBtn, self))

	--帮派id不为0得时候请求一下成员列表信息
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	if 0 ~= role_vo.guild_id then
		GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, role_vo.guild_id)
	end

	HeFuJuLingWGCtrl.Instance:SendAllFirendAndMemberLingBaoInfo()
	HeFuJuLingWGData.Instance:SetGardenFlag(0)

	self:FlushMidPortion()
	HeFuJuLingWGData.Instance:UpdateJuLingFriendList()

	self.node_list.plant_btn_text.text.text = Language.JuLingZhuZhen.Plant
	self.node_list.zhuling_btn_text.text.text = Language.JuLingZhuZhen.ZhuLing
	self.node_list.invite_zhuling_btn_text.text.text = Language.JuLingZhuZhen.InviteZhuLing
	self.node_list.juling_reward_show_btn_text.text.text = Language.JuLingZhuZhen.JuLingRewardShowBtnText
end

function MergeActivityView:OnClickShowInviteGroupBtn(is_show)
	self.node_list.jl_invite_btn_group:SetActive(is_show)
	self.node_list.jl_invite_btn_group_mask:SetActive(is_show)
end

-- 世界频道协助按钮.
function MergeActivityView:OnClickWorldInviteBtn()
	local tmp_time = 0
	local end_time = ChatWGData.Instance:GetChannelCdEndTime(CHANNEL_TYPE.WORLD) or Status.NowTime
	if (end_time - Status.NowTime) > 0 then
		tmp_time = math.ceil(end_time - Status.NowTime)
	else
		tmp_time = math.floor(end_time - Status.NowTime)
	end
	if tmp_time > 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.CanNotChat, math.ceil(tmp_time)))
		return
	end

	local cd_time = ChatWGData.Instance:GetChatCdLimint(CHANNEL_TYPE.WORLD)
	if cd_time > 0 then
		ChatWGData.Instance:SetChannelCdEndTime(CHANNEL_TYPE.WORLD)
	end

	HeFuJuLingWGCtrl.Instance:CSCSAJuLingZhuZhenInfo(MERGE_JULINGZHUZHEN_OPERA_TYPE.MERGE_JULINGZHUZHEN_OPERA_TYPE_INVITE_HELP_ME, 0, MERGE_JULINGZHUZHEN_OPERA_INVITE_TYPE.WORLD)
	SysMsgWGCtrl.Instance:ErrorRemind(Language.JuLingZhuZhen.SendWorldChatText)
end

-- 仙盟协助按钮.
function MergeActivityView:OnClickGuildInviteBtn()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	if 0 ~= role_vo.guild_id then
		HeFuJuLingWGCtrl.Instance:CSCSAJuLingZhuZhenInfo(MERGE_JULINGZHUZHEN_OPERA_TYPE.MERGE_JULINGZHUZHEN_OPERA_TYPE_INVITE_HELP_ME, 0, MERGE_JULINGZHUZHEN_OPERA_INVITE_TYPE.GUILD)
		SysMsgWGCtrl.Instance:ErrorRemind(Language.JuLingZhuZhen.SendGuildChatText)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.JuLingZhuZhen.NotSendGuildChatText)
	end
end

-- 右边好友协助按钮
function MergeActivityView:OnClickFriendBtn()
	-- --帮派id不为0得时候请求一下成员列表信息
	-- local role_vo = GameVoManager.Instance:GetMainRoleVo()
	-- if 0 ~= role_vo.guild_id then
	-- 	GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, role_vo.guild_id)
	-- end
	HeFuJuLingWGCtrl.Instance:SendAllFirendAndMemberLingBaoInfo()
    HeFuJuLingWGCtrl.Instance:OpenFriendView()
end

function MergeActivityView:DeleteHeFuJuLing()
	self.cur_select_toggle = nil

	if self.harvest_reward_list then
		for i, v in ipairs(self.harvest_reward_list) do
			v:DeleteMe()
			v = nil
		end
		self.harvest_reward_list = nil
	end

	if self.all_invite_delaytime then
		GlobalTimerQuest:CancelQuest(self.all_invite_delaytime)
		self.all_invite_delaytime = nil
	end

	if self.node_list.all_invite_btn then
		XUI.SetButtonEnabled(self.node_list.all_invite_btn, true)
	end

	self:ClearNextZhuLingTime()
end

function MergeActivityView:ShowIndexCallBackHeFuJuLing()
	HeFuJuLingWGData.Instance:SetGardenFlag(0)

	self.node_list.harvest_reward_panel.animation_player:Play("Play")

	self:SetRuleInfo(Language.JuLingZhuZhen.TipsContent, Language.JuLingZhuZhen.TipsTitle)
	self:SetOutsideRuleTips(Language.JuLingZhuZhen.TipsDesc)
end

function MergeActivityView:FlushHeFuJuLing()
	local garden_flag = HeFuJuLingWGData.Instance:GetGardenFlag()
	if garden_flag == 1 then
		--别人聚灵的信息
		self.juling_info = HeFuJuLingWGData.Instance:GetOtherJuLingInfo()
	else
		--自己聚灵的信息
		self.juling_info = HeFuJuLingWGData.Instance:GetJuLingInfo()
	end
	self:FlushTopPortion()
	self:FlushMidPortion()
	self:FlushBottomPortion()
	self:FlushRightPortion()
end

function MergeActivityView:JuLingShowIndexAndCloseCallBack()
	HeFuJuLingWGData.Instance:SetGardenFlag(0)
	HeFuJuLingWGCtrl.Instance:CSCSAJuLingZhuZhenInfo(MERGE_JULINGZHUZHEN_OPERA_TYPE.MERGE_JULINGZHUZHEN_OPERA_TYPE_INFO_REQ)
end

--刷新顶部名称
function MergeActivityView:FlushTopPortion()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()

	self.node_list.friend_name_bg:SetActive(self.juling_info.uid ~= role_id)
	self.node_list.back_selfpot_btn:SetActive(self.juling_info.uid ~= role_id)
	self.node_list.juling_reward_show_btn:SetActive(self.juling_info.uid == role_id)
	self.node_list.harvest_slider_panel:SetActive(self.juling_info.uid == role_id)
	self:OnClickShowInviteGroupBtn(false)

	self.node_list.friend_name.text.text = string.format(Language.JuLingZhuZhen.JuLingDesc6, self.juling_info.name)
	self.node_list.back_selfpot_name.text.text = RoleWGData.Instance:GetRoleVo().role_name
end

--刷新中间两个盆栽
function MergeActivityView:FlushMidPortion()
	if IsEmptyTable(self.juling_info) or not self.juling_info.lingbao_list or IsEmptyTable(self.juling_info.lingbao_list) then
		return
	end

	if not IsEmptyTable(self.juling_info.lingbao_list[1]) then
		self:FlushLingBao()
	end
	
	for i = 1, 6 do
		self.node_list["remind_"..i]:SetActive(self:IsCanGetOtherHelpPlayerRewward(i))
	end

	--刷新聚灵奖励是否可领取
	self.node_list.juling_reward_show_btn_remind:SetActive(self:IsCanGetJulingReward())

	local zhulingtai_list = HeFuJuLingWGData.Instance:GetJuLingZhuLingLog().record_list
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()

	if role_id ~= self.juling_info.uid then
		zhulingtai_list = HeFuJuLingWGData.Instance:GetJuLingotherZhuLingLog().record_list
	end

	for i=1,6 do
		if not IsEmptyTable(zhulingtai_list[i]) then
			local flag, data=  self:IsCanGetOtherHelpPlayerRewward(i)
			if flag and not IsEmptyTable(data) then
				--处理完成了炼丹 但是朋友帮助奖励未领取，服务端已屏蔽个人记录数据，导致卡红点情况
				self.node_list["name_"..i].text.text = data.name
				self.node_list["zhuling_root_"..i]:SetActive(true)
			-- elseif zhulingtai_list[i].uid > 0 and zhulingtai_list[i].uid ~= role_id and  zhulingtai_list[i].name then
			-- 	self.node_list["name_"..i].text.text = zhulingtai_list[i].name
			-- 	self.node_list["zhuling_root_"..i]:SetActive(true)
			else	
				self.node_list["zhuling_root_"..i]:SetActive(false)
			end
		else
			self.node_list["zhuling_root_"..i]:SetActive(false)
		end
	end
end


function MergeActivityView:SortJulingDataList(data_list)
    local list = {}
	if data_list and not IsEmptyTable(data_list) then
        for k,v in pairs(data_list) do
            
            if v.item_id and v.item_id > 0 and v.num > 0 then
                local v1 = {}
                v1.item_id = v.item_id
                v1.is_bind = v.is_bind
                v1.num = v.num
                list[#list + 1] = v1

                local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(v1.item_id)
                v1.is_equip = 0
	            if big_type and big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
                    v1.is_equip = 1
                    v1.sort_star = v.param and v.param.star_level or 0
                else
                    v1.sort_star = 0
                end
                v1.color = item_cfg and item_cfg.color or 1
                v1.order = item_cfg.order or 0
            end
        end
		table.sort(list, SortTools.KeyUpperSorters("color","is_equip","sort_star","order"))
    end
    return list
end

--刷新底部部分
function MergeActivityView:FlushBottomPortion()
	local other_cfg = HeFuJuLingWGData.Instance:GetOtherCfg()

	if not other_cfg then
		return
	end
    local data_list = self:SortJulingDataList(other_cfg.zhuling_reward_show)

    local data_list2 = self:SortJulingDataList(other_cfg.ripe_reward_show)

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local graden_uid = HeFuJuLingWGData.Instance:GetCurGardenUid()

	if not self.harvest_reward_list then
		self.harvest_reward_list = {}
		for i = 1, 5 do
			self.harvest_reward_list[i] = ItemCell.New(self.node_list["garvest_item_cell_" .. i]:FindObj("item_cell"))
			self.harvest_reward_list[i]:SetIsUseRoundQualityBg(true)
			self.harvest_reward_list[i]:SetEffectRootEnable(false)
			self.harvest_reward_list[i]:SetCellBgEnabled(false)
			if data_list2[i] then
				self.harvest_reward_list[i]:SetData(data_list2[i])
			end
		end
	end

	if self.juling_info.lingbao_list[1] then
		self.node_list.remain_zhuling_times:SetActive(self.juling_info.lingbao_list[1].status ~= LINGBAO_STATUS.LINGBAO_STATUS_FETCH and role_id == graden_uid)
		self.node_list.remain_xiezhu_times:SetActive(self.juling_info.lingbao_list[1].status ~= LINGBAO_STATUS.LINGBAO_STATUS_FETCH and role_id == graden_uid)
	end

	local remain_zhuling_times = HeFuJuLingWGData.Instance:GetTodayRemainZhuLingTimes()
	local help_other_times = HeFuJuLingWGData.Instance:GetTodayRemainHelpOtherTimes()
    local color1 = remain_zhuling_times > 0 and COLOR3B.C8 or COLOR3B.C10
    local color2 = help_other_times > 0 and COLOR3B.C8 or COLOR3B.C10
	self.node_list.remain_zhuling_times.text.text = string.format(Language.JuLingZhuZhen.JuLingDesc1, color1, remain_zhuling_times)
	self.node_list.remain_xiezhu_times.text.text = string.format(Language.JuLingZhuZhen.JuLingDesc2, color2, help_other_times)

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_JULINGZHUZHEN)

	if not activity_info or activity_info.end_time == -1 then
		return
	end

	self:SetActRemainTime(TabIndex.merge_activity_2127, activity_info.end_time)

end

-- 刷新右边部分
function MergeActivityView:FlushRightPortion()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	
    local is_be_invited_lsit = {}

	for i = 1, 2 do
		is_be_invited_lsit[i] = HeFuJuLingWGData.Instance:GetIsBeInveiteByType(i)
	end
	self.node_list.friend_btn_remind:SetActive(is_be_invited_lsit[1] == true or is_be_invited_lsit[2] == true)
	self.node_list.jl_friend_btn_remind:SetActive(is_be_invited_lsit[1] == true or is_be_invited_lsit[2] == true)
end

function MergeActivityView:OnClickBackSelfPotBtn()
	HeFuJuLingWGData.Instance:SetGardenFlag(0)
	HeFuJuLingWGCtrl.Instance:CSCSAJuLingZhuZhenInfo(MERGE_JULINGZHUZHEN_OPERA_TYPE.MERGE_JULINGZHUZHEN_OPERA_TYPE_INFO_REQ)
end

--策划要求将炼丹可领取也放在此按钮 不可领取时限时奖励  可领取时领取奖励
function MergeActivityView:OnClickJulingRewardShowBtn()
	if self:IsCanGetJulingReward() then
		self:GetJuLingReward()
	else
		local other_cfg = HeFuJuLingWGData.Instance:GetOtherCfg()
		if not other_cfg then
			return
		end

		local data_list =
		{
			view_type = RewardShowViewType.Normal,
			reward_item_list = self:SortJulingDataList(other_cfg.zhuling_reward_show)
		}
		RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
	end
end

function MergeActivityView:IsCanGetJulingReward()
	if not IsEmptyTable(self.juling_info.lingbao_list[1]) then
		local data = self.juling_info.lingbao_list[1]
		local role_id = RoleWGData.Instance:InCrossGetOriginUid()
		local graden_uid = HeFuJuLingWGData.Instance:GetCurGardenUid()
		--在自己界面 
		if graden_uid == role_id and data.status == LINGBAO_STATUS.LINGBAO_STATUS_RIPE then
			return true
		end
	end
	return false
end

--领取聚灵完成奖励
function MergeActivityView:GetJuLingReward()
	local data = self.juling_info.lingbao_list[1]
	if not data then
		return
	end

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local graden_uid = HeFuJuLingWGData.Instance:GetCurGardenUid()

	if graden_uid ~= role_id then
		return
	end
	if data.status ~= LINGBAO_STATUS.LINGBAO_STATUS_RIPE then
		return
	end
	HeFuJuLingWGCtrl.Instance:CSCSAJuLingZhuZhenInfo(MERGE_JULINGZHUZHEN_OPERA_TYPE.MERGE_JULINGZHUZHEN_OPERA_TYPE_FETCH, 0)
end

function MergeActivityView:OnClickOtherHelpPlayer(index)
	if IsEmptyTable(self.juling_info.lingbao_list[1]) then
		return
	end

	if not self:IsCanGetOtherHelpPlayerRewward(index) then
		return
	end

	local data_list = HeFuJuLingWGData.Instance:GetHlepMeDataList(1)
	if not data_list[index] then
		return
	end

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local graden_uid = HeFuJuLingWGData.Instance:GetCurGardenUid()
	if graden_uid ~= role_id then
		return
	end

	HeFuJuLingWGCtrl.Instance:CSCSAJuLingZhuZhenInfo(MERGE_JULINGZHUZHEN_OPERA_TYPE.MERGE_JULINGZHUZHEN_OPERA_TYPE_FETCH_HELP_ME_REWARD, 0, data_list[index].uid)
end

function MergeActivityView:IsCanGetOtherHelpPlayerRewward(index)
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local graden_uid = HeFuJuLingWGData.Instance:GetCurGardenUid()
	local data_list = HeFuJuLingWGData.Instance:GetHlepMeDataList(1)

	if graden_uid == role_id and data_list[index] and data_list[index].uid > 0 then
		return true , data_list[index]
	end
	return false , {}
end

function MergeActivityView:FlushLingBao()
	self:ClearNextZhuLingTime()

	local data = self.juling_info.lingbao_list[1]

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	local graden_uid = HeFuJuLingWGData.Instance:GetCurGardenUid()

	-- XUI.SetGraphicGrey(self.node_list.zhuling_btn, false)
	self.node_list.zhuling_btn:SetActive(true)
	self.node_list.zhuling_btn_effect:SetActive(true)
	self.node_list.zhuling_times:SetActive(true)
	self.node_list.invite_zhuling_btn:SetActive(true)
	self.node_list.invite_zhuling_btn_effect:SetActive(true)

	local lingbao_cfg = HeFuJuLingWGData.Instance:GetZhuLingCfgByType(0)
    local value = data.lingbao_current_lingli / lingbao_cfg.ripe_need_lingli
    self.node_list.slider_percent.text.text = math.ceil((value) * 100) .. "%"
	self.node_list.harvest_slider_img.image.fillAmount = value

	self.node_list.friend_btn:SetActive(self.juling_info.uid == role_id and data.status ~= LINGBAO_STATUS.LINGBAO_STATUS_NOTHING)

	--不在自己界面
	if graden_uid ~= role_id then
		local canzhuling = data.status == LINGBAO_STATUS.LINGBAO_STATUS_GROWING

		self.node_list.plant_btn:SetActive(false)
		self.node_list.invite_zhuling_btn:SetActive(false)
		self.node_list.zhuling_times:SetActive(canzhuling)
		-- XUI.SetGraphicGrey(self.node_list.zhuling_btn, data.help_target_role_times >= lingbao_cfg.help_other_times)
		self.node_list.zhuling_btn_effect:SetActive(data.help_target_role_times < lingbao_cfg.help_other_times)
		self.node_list.zhuling_times.text.text = string.format("%s/%s",lingbao_cfg.help_other_times - data.help_target_role_times, lingbao_cfg.help_other_times) 
		return
	end

	-- 自己浇水次数
	local remain_times, max_times = HeFuJuLingWGData.Instance:GetTodayRemainZhuLingTimesByType(1)
	-- 被浇水次数
	local be_zhuling_remain_times, be_zhuling_max_times = HeFuJuLingWGData.Instance:GetTodayRemainBeZhuLingTimesByType(1)
	self.node_list.zhuling_times.text.text = string.format("%s/%s", remain_times, max_times)
	self.node_list.invite_times.text.text = string.format("%s/%s", be_zhuling_remain_times, be_zhuling_max_times)

	if data.status == LINGBAO_STATUS.LINGBAO_STATUS_NOTHING then --无
		self.node_list.plant_btn:SetActive(true)
		self.node_list.zhuling_btn:SetActive(false)
		self.node_list.invite_zhuling_btn:SetActive(false)
	elseif data.status == LINGBAO_STATUS.LINGBAO_STATUS_GROWING then --成长期
		self.node_list.plant_btn:SetActive(false)
		self.node_list.zhuling_btn_effect:SetActive(remain_times > 0)
		self.node_list.invite_zhuling_btn_effect:SetActive(be_zhuling_remain_times > 0)
		-- XUI.SetGraphicGrey(self.node_list.zhuling_btn, remain_times <= 0)
		-- XUI.SetGraphicGrey(self.node_list.invite_zhuling_btn, be_zhuling_remain_times <= 0)
	elseif data.status == LINGBAO_STATUS.LINGBAO_STATUS_RIPE then --未领取
		self.node_list.plant_btn:SetActive(false)
		self.node_list.zhuling_btn_effect:SetActive(false)
		self.node_list.invite_zhuling_btn_effect:SetActive(false)
		-- XUI.SetGraphicGrey(self.node_list.zhuling_btn, true)
		-- XUI.SetGraphicGrey(self.node_list.invite_zhuling_btn, true)
	elseif data.status == LINGBAO_STATUS.LINGBAO_STATUS_FETCH then --已领取
		self.node_list.plant_btn:SetActive(false)
		self.node_list.zhuling_btn_effect:SetActive(false)
		self.node_list.invite_zhuling_btn_effect:SetActive(false)
		-- XUI.SetGraphicGrey(self.node_list.zhuling_btn, true)
		-- XUI.SetGraphicGrey(self.node_list.invite_zhuling_btn, true)
	end

	local next_zhuling_time = data.next_zhuling_timestamp - TimeWGCtrl.Instance:GetServerTime()

	if next_zhuling_time > 0 then
		CountDownManager.Instance:AddCountDown("next_zhuling_time_1", BindTool.Bind1(self.NextZhuLingTime, self), BindTool.Bind1(self.CompleteNextZhuLingTime, self), nil, next_zhuling_time, 1)
		self:NextZhuLingTime(0, next_zhuling_time)
	end
	
end

function MergeActivityView:ClearNextZhuLingTime()
	if CountDownManager.Instance:HasCountDown("next_zhuling_time_1") then
		CountDownManager.Instance:RemoveCountDown("next_zhuling_time_1")
	end
	if self.node_list.next_zhuling_time then
		self.node_list.next_zhuling_time.text.text = ""
		self.node_list.next_zhuling_time_bg:SetActive(false)
	end

	if self.node_list.zhuling_btn then
		XUI.SetButtonEnabled(self.node_list.zhuling_btn, true)
	end
end

-- 浇水冷却时间
function MergeActivityView:NextZhuLingTime(elapse_time, total_time)
	local time = math.floor(total_time - elapse_time)

	if time > 0 and self.node_list.next_zhuling_time and self.node_list.zhuling_btn then
		self.node_list.next_zhuling_time_bg:SetActive(true)
		self.node_list.next_zhuling_time.text.text = string.format(Language.JuLingZhuZhen.JuLingDesc5, TimeUtil.FormatSecond(time))
		XUI.SetButtonEnabled(self.node_list.zhuling_btn, false)
	end
end

function MergeActivityView:CompleteNextZhuLingTime()
	self:ClearNextZhuLingTime()
end

function MergeActivityView:OnClickPlantBtn()
	local data = self.juling_info.lingbao_list[1]
	if  IsEmptyTable(data) then
		return
	end

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local graden_uid = HeFuJuLingWGData.Instance:GetCurGardenUid()
	if role_id == graden_uid then
		--普通盆栽
		HeFuJuLingWGCtrl.Instance:CSCSAJuLingZhuZhenInfo(MERGE_JULINGZHUZHEN_OPERA_TYPE.MERGE_JULINGZHUZHEN_OPERA_TYPE_PLANT, 0)
		SysMsgWGCtrl.Instance:ErrorRemind(Language.JuLingZhuZhen.TipsDesc13)
	end
end

function MergeActivityView:OnClickZhuLingBtn()
	local data = self.juling_info.lingbao_list[1]
	if  IsEmptyTable(data) then
		return
	end

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local graden_uid = HeFuJuLingWGData.Instance:GetCurGardenUid()

	if role_id ~= graden_uid then
		local juling_info = HeFuJuLingWGData.Instance:GetOtherJuLingInfo()

		if not juling_info then
			return 
		end

		--没有种植
		if data.status == LINGBAO_STATUS.LINGBAO_STATUS_NOTHING then
			TipWGCtrl.Instance:ShowSystemMsg(Language.JuLingZhuZhen.TipsDesc2)
			return
		end
		
		--今天已经为这个人注灵过
		local help_target_role_times = data.help_target_role_times or 0
		if help_target_role_times > 0 then
			TipWGCtrl.Instance:ShowSystemMsg(Language.JuLingZhuZhen.TipsDesc15)
			return
		end

		--对方注灵台已满员
		local zhulingtai_info = HeFuJuLingWGData.Instance:GetJuLingotherZhuLingLog()
		local zhulingtai_count = HeFuJuLingWGData.Instance:GetZhuLingTaiCount(zhulingtai_info)
		if zhulingtai_count >= 6 then
			TipWGCtrl.Instance:ShowSystemMsg(Language.JuLingZhuZhen.TipsDesc14)
			return
		end

		-- 已领取
		if data.status == LINGBAO_STATUS.LINGBAO_STATUS_FETCH then
			TipWGCtrl.Instance:ShowSystemMsg(Language.JuLingZhuZhen.TipsDesc3)
			return
		end

	else
		-- 自己浇水次数
		local remain_times, max_times = HeFuJuLingWGData.Instance:GetTodayRemainZhuLingTimesByType(1)

		if remain_times <= 0 then
			TipWGCtrl.Instance:ShowSystemMsg(Language.JuLingZhuZhen.TipsDesc19)
			return
		end
	end

	if data.status == LINGBAO_STATUS.LINGBAO_STATUS_NOTHING or
		data.status == LINGBAO_STATUS.LINGBAO_STATUS_RIPE or
		data.status == LINGBAO_STATUS.LINGBAO_STATUS_FETCH then
		return
	end

	XUI.SetButtonEnabled(self.node_list.zhuling_btn, false)

	if role_id == graden_uid then
		HeFuJuLingWGCtrl.Instance:CSCSAJuLingZhuZhenInfo(MERGE_JULINGZHUZHEN_OPERA_TYPE.MERGE_JULINGZHUZHEN_OPERA_TYPE_ZHULING, 0)

	else
		HeFuJuLingWGCtrl.Instance:CSCSAJuLingZhuZhenInfo(MERGE_JULINGZHUZHEN_OPERA_TYPE.MERGE_JULINGZHUZHEN_OPERA_TYPE_ZHULING_OTHER, 0, graden_uid)
	end
end

--邀请浇水按钮
function MergeActivityView:OnClickInviteZhuLingBtn()
	self:OnClickFriendBtn()
end