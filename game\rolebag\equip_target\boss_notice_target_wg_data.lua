BossNoticeTargetTipsWGData = BossNoticeTargetTipsWGData or BaseClass()

BossNoticeEquipType = {
    [1] = GameEnum.EQUIP_INDEX_TOUKUI,      -- = 0,                         --头盔
    [2] = GameEnum.EQUIP_INDEX_YIFU,        -- = 1,                         --衣服
    [3] = GameEnum.EQUIP_INDEX_KUZI,        -- = 2,                         --裤子
    [4] = GameEnum.EQUIP_INDEX_XIEZI,       -- = 4,                         --鞋子
    [5] = GameEnum.EQUIP_INDEX_WUQI,        -- = 5,                         --武器 剑 镰 琴 伞
    [6] = GameEnum.EQUIP_INDEX_XIANFU,      -- = 7,                         -- 匕首
}


function BossNoticeTargetTipsWGData:__init()
    if BossNoticeTargetTipsWGData.Instance then
        error("[BossNoticeTargetTipsWGData] Attempt to create singleton twice!")
        return
    end
    BossNoticeTargetTipsWGData.Instance = self
    self.cur_suit_index = -1
    self.boss_notice_quiplist = {}
    self.have_equip_num = 0
    self.cur_notice_grade = -1
    self.client_suit_index_flag = {}
    self:InitConfig()
end
function BossNoticeTargetTipsWGData:__delete()
    if self.equip_data_change then
        EquipWGData.Instance:UnNotifyDataChangeCallBack(self.equip_data_change)
        self.equip_data_change = nil
    end
    if self.role_data_change then
        RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
        self.role_data_change = nil
    end
    BossNoticeTargetTipsWGData.Instance = nil
end

function BossNoticeTargetTipsWGData:InitConfig()
    self.notice_boss_auto = ConfigManager.Instance:GetAutoConfig("notic_boss_auto")
    self.equip_collect_active = ListToMap(self.notice_boss_auto.equip_collect_active,"suit_index")
    self.comple_condition = ListToMap(self.notice_boss_auto.comple_condition,"suit_index")
end

function BossNoticeTargetTipsWGData:GetCurGradeStatus()
    local role_level = RoleWGData.Instance:GetRoleLevel()
    local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
    for k,v in pairs(self.equip_collect_active) do
        if role_level >= v.min_level and prof_level == v.prof_level and role_level <= v.max_level and self:CheckBossNoticeSuitEquipFlag(v.suit_index) == 0 then
            local need_equip_num = v.c_param1
            local need_equip_grade = v.c_param2
            local need_equip_quality = v.c_param3
            local need_equip_strar = v.c_param4
            self.have_equip_num = 0
            for t,q in pairs(BossNoticeEquipType) do
                local equip_data = EquipWGData.Instance:GetGridData(q)
                if equip_data and equip_data.param then
                    local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(equip_data.item_id)
                    if item_cfg.color >= need_equip_quality and equip_data.param.star_level >= need_equip_strar and item_cfg.order >= need_equip_grade then
                        self.have_equip_num = self.have_equip_num + 1
                    end
                end
            end
            if self.have_equip_num < need_equip_num then
                self.cur_suit_index = v.suit_index
                self.cur_need_prof_level = v.prof_level
                return v.suit_index
            end
        end
    end
    return -1
end

function BossNoticeTargetTipsWGData:GetCurGradeTaskStr()
    if self.cur_suit_index == -1 then
        self:GetCurGradeStatus()
    end
    if self.cur_suit_index == -1 then
        return ""
    end
    return Language.Boss.BossNoticeTask[self.cur_suit_index]
end

function BossNoticeTargetTipsWGData:GetCurGradeTaskDesc()
    if self.cur_suit_index == -1 then
        self:GetCurGradeStatus()
    end
    if self.cur_suit_index == -1 then
        return ""
    end
    local cur_cfg = self.equip_collect_active[self.cur_suit_index]
    local need_equip_num = cur_cfg.c_param1
    local need_equip_grade = cur_cfg.c_param2
    local need_equip_quality = cur_cfg.c_param3
    local need_equip_strar = cur_cfg.c_param4
    local str = string.format(Language.Boss.BossNoticeTaskText[0],need_equip_num,need_equip_grade,need_equip_strar,self.have_equip_num,need_equip_num)
    if self.have_equip_num >= need_equip_num then
        str = ToColorStr(str,COLOR3B.D_GREEN)
    end
    return str
end

function BossNoticeTargetTipsWGData:GetCurHaveNumIsEnough()
    if self.cur_suit_index == -1 then
        self:GetCurGradeStatus()
    end
    if self.cur_suit_index == -1 then
        return false
    end
    local cur_cfg = self.equip_collect_active[self.cur_suit_index]
    local need_equip_num = cur_cfg.c_param1
    return self.have_equip_num >= need_equip_num
end

function BossNoticeTargetTipsWGData:GetCurHaveNumAndNeedNum()
    if self.cur_suit_index == -1 then
        self:GetCurGradeStatus()
    end
    if self.cur_suit_index == -1 then
        return false
    end
    local cur_cfg = self.equip_collect_active[self.cur_suit_index]
    local need_equip_num = cur_cfg.c_param1
    return self.have_equip_num,need_equip_num
end


-- function BossNoticeTargetTipsWGData:CheckData( )
--     for k,v in pairs(BossNoticeEquipType) do
--         local equip_data = EquipWGData.Instance:GetGridData(v)
--         print_error("equip_data",v,equip_data)
--         local item_cfg = ItemWGData.Instance:GetItemConfig(equip_data.item_id)
--         print_error("item_cfg",item_cfg)
--     end
-- end

function BossNoticeTargetTipsWGData:GetCurEquipNeedList()
    local suit_index = self:GetCurGradeStatus()
    local need_params = self.equip_collect_active[suit_index]
    if not self.boss_notice_quiplist[suit_index] then
        self.boss_notice_quiplist[suit_index] = {}
        
        for k,v in pairs(TargetTypeToName) do
            self.boss_notice_quiplist[suit_index][k] = {}
            if self.comple_condition[suit_index] and self.comple_condition[suit_index][v] then
                local item_list = Split(self.comple_condition[suit_index][v],"|")

                for t,q in pairs(item_list) do
                    local item_data = {}
                    item_data.item_id = tonumber(q)
                    item_data.param = {}
                    item_data.param.star_level = need_params.c_param4
                    self.boss_notice_quiplist[suit_index][k][t] = item_data
                end
            else
                print_error("name of table is nil ",v)
            end
        end
    end
    

    local my_type = EquipTargetWGData.Instance:GetRoleTargetType()
    return self.boss_notice_quiplist[suit_index][my_type] or {}
end

-- function BossNoticeTargetTipsWGData:GetShowEquipList()
--     local suit_index = self:GetCurGradeStatus()
--     self.equip_collect_active[suit_index]
-- end

function BossNoticeTargetTipsWGData:GetBossNoticeEquipIndexIsEnough(index)
    if self.cur_suit_index == -1 then
        self:GetCurEquipNeedList()
    end

    if self.cur_suit_index ~= -1 then
        local cur_cfg = self.equip_collect_active[self.cur_suit_index]
        local need_equip_num = cur_cfg.c_param1
        local need_equip_grade = cur_cfg.c_param2
        local need_equip_quality = cur_cfg.c_param3
        local need_equip_strar = cur_cfg.c_param4
        local equip_data = EquipWGData.Instance:GetGridData(BossNoticeEquipType[index])
        if equip_data and equip_data.param then
            local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(equip_data.item_id)
            if item_cfg.color >= need_equip_quality and equip_data.param.star_level >= need_equip_strar and item_cfg.order >= need_equip_grade then
                return true,equip_data.param.star_level
            end
        end

        return false, 0
    end

    return false, 0
end


function BossNoticeTargetTipsWGData:GetBossNoticeEquipData(index)
    local equip_data = EquipWGData.Instance:GetGridData(BossNoticeEquipType[index])
    if equip_data and equip_data.param then
        local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(equip_data.item_id)
        local data = {}
        data.item_id = equip_data.item_id
        data.param = {}
        data.param.star_level = equip_data.param.star_level
        return data,equip_data
    end
    return {},{}
end

function BossNoticeTargetTipsWGData:GetCurGradeStatus2()
    if self.cur_suit_index == -1 then
        self:GetCurGradeStatus()
    end
    return self.cur_suit_index,self.cur_need_prof_level
end

function BossNoticeTargetTipsWGData:OnSCRoleNoticeBossStatus(protocol)
    self.cur_notice_grade = protocol.cur_notice_grade
    BossNoticeTargetTipsWGData.Instance:GetCurGradeStatus()
    if not self.equip_data_change then
        self.equip_data_change = BindTool.Bind(self.OnEquipDataChange, self)
        EquipWGData.Instance:NotifyDataChangeCallBack(self.equip_data_change)
    end

    if not self.role_data_change then
        self.role_data_change = BindTool.Bind1(self.RoleLevelChange, self)
        RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
    end

    if self:GetIsCanCommit() then
        EquipTargetWGCtrl.Instance:TryCompleteBossNoticeTask(self.cur_notice_grade)
    end
    FunctionGuide.Instance:OnBossNoticeEventOpen(self.cur_notice_grade)
end

function BossNoticeTargetTipsWGData:OnEquipDataChange()
    self:GetCurGradeStatus()
    MainuiWGCtrl.Instance:FlushTaskView()
    if self:GetIsCanCommit() then
        EquipTargetWGCtrl.Instance:TryCompleteBossNoticeTask(self.cur_notice_grade)
    end
end

function BossNoticeTargetTipsWGData:RoleLevelChange()
    self:OnEquipDataChange()
end

function BossNoticeTargetTipsWGData:GetIsCanCommit()
    if self.cur_suit_index == -1 and self.cur_notice_grade ~= 0 then
        return true
    end

    if self:GetCurHaveNumIsEnough() then
        return true
    end

    return false
end

function BossNoticeTargetTipsWGData:OnSCRoleNoticBossSaveClientInfo(protocol)
    self.client_suit_index_flag = protocol.client_suit_index_flag
end

function BossNoticeTargetTipsWGData:CheckBossNoticeSuitEquipFlag(suit_index)
    if self.client_suit_index_flag and self.client_suit_index_flag[32-suit_index] then
        return self.client_suit_index_flag[32-suit_index]
    end
    return 1
end

function BossNoticeTargetTipsWGData:GetNeedEquipCfg()
    if self.cur_suit_index == -1 then
        return 0
    end
    return self.equip_collect_active[self.cur_suit_index] or {}
end