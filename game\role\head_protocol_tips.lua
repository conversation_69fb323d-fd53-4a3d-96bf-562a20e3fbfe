HeadProtocolTips = HeadProtocolTips or BaseClass(SafeBaseView)

function HeadProtocolTips:__init()
	self:AddViewResource(0, "uis/view/role_ui_prefab", "layout_head_protocol")
    self.view_name = "HeadProtocolTips"
	self:SetMaskBg(true)
end

function HeadProtocolTips:ReleaseCallBack()
    self.call_back = nil
end

function HeadProtocolTips:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_sure, BindTool.Bind(self.OnClickSure, self))
end

function HeadProtocolTips:SetDataAndOpen(call_back)
    self.call_back = call_back
	self:Open()
end

function HeadProtocolTips:OnFlush()
    local is_agree = RoleWGData.Instance:IsAgreeHeadProtocol()
    if is_agree then
        self:Close()

		-- 有个动画，没立即消失
		ReDelayCall(self, function()
			if self.call_back then
	            self.call_back()
	        end
		end, 0.4, "HeadProtocolTips")

        return
    end

    local other_cfg = RoleWGData.Instance:GetHeadOtherCfg()
    self.node_list.protocol_desc_1.text.text = other_cfg and other_cfg.protocol_desc_1 or ""
	self.node_list.protocol_desc_2.text.text = other_cfg and other_cfg.protocol_desc_2 or ""
end

function HeadProtocolTips:OnClickSure()
	RoleWGCtrl.Instance:SendHeadAgreeProtocolReq(0)
end
