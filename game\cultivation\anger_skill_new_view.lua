local Break_Skill_Type = {
    BREAK_SKILL = 1,
}

local anger_type_res = {
    [0] = "a3_jj_txt_long2",
    [1] = "a3_jj_txt_shi2",
    [2] = "a3_jj_txt_mie2",
    [10] = "a3_jj_txt_long1",
    [11] = "a3_jj_txt_shi1",
    [12] = "a3_jj_txt_mie1",
}

function CultivationView:LoadCallBack__AngerSkill()
	--类别列表
	if not self.anger_skill_list then
        self.anger_skill_list = {}
        for i = 1, 6 do
            local cell = AngerSkillRender.New(self.node_list.anger_skill_list:FindObj("anger_skill_item_" .. i))
            cell:SetIndex(i)
            cell:SetClickCallBack(BindTool.Bind1(self.OnSelectSkillCB, self))
            self.anger_skill_list[i] = cell
        end
	end

    -- 技能列表
    if not self.anger_skill_type_list then
		self.anger_skill_type_list = AsyncListView.New(AngerSkillTypeRender, self.node_list.anger_skill_type_list)
        self.anger_skill_type_list:SetStartZeroIndex(true)
		self.anger_skill_type_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectSkillTypeCB, self))
	end

    -- 怒气技能境界相关列表
    if not self.anger_sp_attr_list then
		self.anger_sp_attr_list = AsyncListView.New(AngerSPAttrRender, self.node_list.anger_sp_attr_list)
	end

    -- 怒气普通技能境界相关列表
    if not self.anger_skill_attr_list then
		self.anger_skill_attr_list = AsyncListView.New(AngerSkillAttrRender, self.node_list.anger_skill_attr_list)
	end

    -- 怒气等级属性相关列表
    if not self.anger_type_attr_list then
        self.anger_type_attr_list = AsyncListView.New(CommonAddAttrRender, self.node_list.anger_type_attr_list)
    end

    -- 怒气buff属性相关列表
    if not self.anger_buff_attr_list then
        self.anger_buff_attr_list = AsyncListView.New(CommonAddAttrRender, self.node_list.anger_buff_attr_list)
    end

    if not self.active_item then
        self.active_item = ItemCell.New(self.node_list.active_item)
    end

    if not self.buff_item then
        self.buff_item = ItemCell.New(self.node_list.buff_item)
    end

	XUI.AddClickEventListener(self.node_list["ph_level"],BindTool.Bind(self.OnClickBtnLevel, self))
    XUI.AddClickEventListener(self.node_list["ph_buff"],BindTool.Bind(self.OnClickBtnBuff, self))
    XUI.AddClickEventListener(self.node_list["ph_buff_not"],BindTool.Bind(self.OnClickBtnBuff, self))
    XUI.AddClickEventListener(self.node_list.btn_transform, BindTool.Bind(self.OnClickTransform, self))
    XUI.AddClickEventListener(self.node_list.btn_anger_fashion, BindTool.Bind(self.OnClickAngerFashion, self))
    XUI.AddClickEventListener(self.node_list.btn_active, BindTool.Bind(self.OnClickActive, self))
    XUI.AddClickEventListener(self.node_list.btn_buff_upgrade, BindTool.Bind(self.OnClickBuffUpgrade, self))
end

function CultivationView:OnClickBtnLevel(is_on)
    if is_on then
        self.node_list.up_layer:SetActive(true)
        self.node_list.buff_layer:SetActive(false)
    end
end

function CultivationView:OnClickBtnBuff(is_on)
    if is_on or is_on == nil then
        if not CultivationWGData.Instance:IsActiveAnger(self.cur_select_type_index) then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Cultivation.AngerActiveTips2)
            return
        end
        self.node_list.up_layer:SetActive(false)
        self.node_list.buff_layer:SetActive(true)
    end

end


function CultivationView:ShowIndexCallBack__AngerSkill()
    self:FlushAngerSkillModel()
    self.node_list["ph_level"].toggle.isOn = true
    self:OnClickBtnLevel()
end

function CultivationView:FlushAngerSkillModel()
	if not self:IsLoaded() then
		return
	end

	if nil == self.anger_role_model then
		self.anger_role_model = RoleModel.New()
		self.anger_role_model:SetUISceneModel(nil, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.anger_role_model, {TabIndex.anger_skill})

		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local special_status_table = {ignore_halo = true, ignore_weapon = true}
		self.anger_role_model:SetPositionAndRotation(nil,nil,Vector3(1.6, 1.6, 1.6))

		self.anger_role_model:SetModelResInfo(role_vo, special_status_table, nil) --, SceneObjAnimator.transformation, true

        -- local power_type = role_vo.appearance_param + 1
        -- self.anger_role_model:SetActorConfigPrefabData(ConfigManager.Instance:GetRolePowerAutoPrefabConfig(role_vo.sex, 1, power_type))

		self.anger_role_model:SetWingResid(0)
	end

	if self.anger_role_model then
		-- self.anger_role_model:PlayRoleAction(SceneObjAnimator.transformation)
        -- self.anger_role_model:ExecuteAransformationAction2()
        -- print_error("self.cur_select_type_index=",self.cur_select_type_index)

		local show_nuqi_lv = CultivationWGData.Instance:GetAngerLevel(self.cur_select_type_index)
		local cfg = CultivationWGData.Instance:GetNuqiUpgradeCfg(self.cur_select_type_index, show_nuqi_lv)
		if cfg ~= nil then
			self.anger_role_model:SetAngerImage(self.cur_select_type_index, true, cfg.default_body, cfg.default_face, cfg.default_hair)
		end
		-- self.anger_role_model:PlayRoleAction(SceneObjAnimator.transformation)

        -- self.anger_role_model:ExecuteAransformationAction2()

        -- self.anger_role_model:DoAttackForNoTarget(3201)
        -- self.anger_role_model:ExecuteAransformationAction(self.cur_select_type_index)
		self.anger_role_model:SetRotation({ x = 6, y = 180, z = 0 })
		self.anger_role_model:FixToOrthographicOnUIScene()

        local model_transform = self.anger_role_model:GetModelPosNode()
		model_transform:DOLocalMoveY(-5.75, 0)
	end
end

function CultivationView:ReleaseCallBack__AngerSkill()
    if self.anger_skill_list and #self.anger_skill_list > 0 then
		for _, anger_skill_cell in ipairs(self.anger_skill_list) do
			anger_skill_cell:DeleteMe()
			anger_skill_cell = nil
		end

		self.anger_skill_list = nil
	end

    if self.anger_role_model then
		self.anger_role_model:DeleteMe()
		self.anger_role_model = nil
	end

    if self.anger_skill_type_list then
		self.anger_skill_type_list:DeleteMe()
		self.anger_skill_type_list = nil
	end

    if self.anger_sp_attr_list then
		self.anger_sp_attr_list:DeleteMe()
		self.anger_sp_attr_list = nil
	end

    if self.anger_skill_attr_list then
		self.anger_skill_attr_list:DeleteMe()
		self.anger_skill_attr_list = nil
	end

    if self.anger_type_attr_list then
		self.anger_type_attr_list:DeleteMe()
		self.anger_type_attr_list = nil
	end
    if self.anger_buff_attr_list then
		self.anger_buff_attr_list:DeleteMe()
		self.anger_buff_attr_list = nil
	end
    
    
    if self.active_item then
		self.active_item:DeleteMe()
		self.active_item = nil
	end

    if self.buff_item then
		self.buff_item:DeleteMe()
		self.buff_item = nil
	end

    

    self.cur_select_type_index = nil
    self.cur_select_skill_index = nil
    self.cur_select_skill_data = nil
    self.cur_role_res_id = nil

end

-- 选择类型回调
function CultivationView:OnSelectSkillTypeCB(type_item, cell_index, is_default, is_click)
    if nil == type_item or nil == type_item.data then
		return
	end

	--拿到当前选中的格子下标
	local cell_index = type_item:GetIndex()
	if self.cur_select_type_index == cell_index then   ---这里不能return，左侧列表也要跟着协议刷新而刷新
        return
	end

	self.cur_select_type_index = cell_index
    self.cur_select_skill_index = nil
    self:FlushSkillList()
    -- self:FlushActiveStatus()
    self:FlushAngerSkillModel()
    self:FlushBuffLayer()

    self:FlushToggleRemind()
    self:FlushToggleShow()
end


function CultivationView:FlushBuffLayer()
    local name = Language.Cultivation.AngerSkillType[self.cur_select_type_index]
    local buff_level = CultivationWGData.Instance:GetAngerBuffLevel(self.cur_select_type_index)
    self.node_list.text_anger_buff_name.tmp.text = string.format("%s %s%s",name, buff_level,Language.Common.Ji)
    -- 获取怒气本体属性
    local anger_level = CultivationWGData.Instance:GetAngerLevel(self.cur_select_type_index)
    local cur_upgrade_cfg = CultivationWGData.Instance:GetNuqiUpgradeCfg(self.cur_select_type_index, anger_level)
    local next_upgrade_cfg = CultivationWGData.Instance:GetNuqiUpgradeCfg(self.cur_select_type_index, anger_level + 1)
    local upgrade_attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_upgrade_cfg, next_upgrade_cfg, nil, nil, 1, 4)
    self.anger_type_attr_list:SetDataList(upgrade_attr_list)

    -- 根据buff等级获取buff属性
    local buff_level = CultivationWGData.Instance:GetAngerBuffLevel(self.cur_select_type_index)
    local cur_buff_cfg = CultivationWGData.Instance:GetAngerBuffCfg(self.cur_select_type_index, buff_level)
    local next_buff_cfg = CultivationWGData.Instance:GetAngerBuffCfg(self.cur_select_type_index ,buff_level + 1)
    local buff_attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_buff_cfg, next_buff_cfg, nil, nil, 10, 12)
    self.anger_buff_attr_list:SetDataList(buff_attr_list)

    -- 未满级
    if next_buff_cfg then
        self.node_list.img_max_buff:SetActive(false)
        self.node_list.btn_buff_upgrade:SetActive(true)
        self.node_list.buff_item:SetActive(true)

        if cur_buff_cfg then
            self.buff_item:SetData({ item_id = cur_buff_cfg.item_id })
            local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_buff_cfg.item_id)
            local color = item_num >= cur_buff_cfg.item_num and COLOR3B.D_GREEN or COLOR3B.D_RED
            -- self.node_list.buff_upgrade_remind:CustomSetActive(item_num >= cur_buff_cfg.item_num)
            self.buff_item:SetRightBottomTextVisible(true)
            self.buff_item:SetRightBottomColorText(item_num .. '/' .. cur_buff_cfg.item_num, color)
        end

        -- buff可升级红点
        
        if CultivationWGData.Instance:IsActiveAnger(self.cur_select_type_index) then
            local is_rich, item_id = CultivationWGData.Instance:IsRichItemAngerBuff(self.cur_select_type_index)
            self.node_list.buff_upgrade_remind:SetActive(is_rich)
        else
            self.node_list.buff_upgrade_remind:SetActive(false)
        end
        
    -- 满级
    else
        self.node_list.img_max_buff:SetActive(true)
        self.node_list.btn_buff_upgrade:SetActive(false)
        self.node_list.buff_item:SetActive(false)
    end
end

-- 选择类型回调
function CultivationView:OnSelectSkillCB(skill_item)
    if nil == skill_item or nil == skill_item.data then
		return
	end

	--拿到当前选中的格子下标
	local cell_index = skill_item:GetIndex()

    -- 之前说本体没激活不让点技能 现在可以点 注释掉
    -- if cell_index ~= 1 and not CultivationWGData.Instance:IsActiveAnger(self.cur_select_type_index) then
    --     SysMsgWGCtrl.Instance:ErrorRemind(Language.Cultivation.AngerActiveTips2)
    --     return
    -- end
	if self.cur_select_skill_index == cell_index then   ---这里不能return，左侧列表也要跟着协议刷新而刷新
        return
	end

	self.cur_select_skill_index = cell_index
    self.cur_select_skill_data = skill_item.data

    self:FlushSkillSelect()
    self:FlushSkillMessage()
    -- self:FlushSkillXiuWeiList()

end


function CultivationView:OnFlush__AngerSkill()
    -- 刷新入口
    self:FlushSkillTypeList()
    self:FlushBuffLayer()
    self:FlushToggleShow()
    self:FlushToggleRemind()
end

function CultivationView:FlushToggleShow()
    if not self.cur_select_type_index or not CultivationWGData.Instance:IsActiveAnger(self.cur_select_type_index) then
        self.node_list.ph_level.toggle.isOn = true
        self.node_list.ph_buff_not:SetActive(true)
        self.node_list.ph_buff:SetActive(false)
    else
        self.node_list.ph_buff_not:SetActive(false)
        self.node_list.ph_buff:SetActive(true)
    end

end

-- 刷新类型
function CultivationView:FlushSkillTypeList()
    local select_index = 0
    local type_list = Language.Cultivation.AngerSkillType
    local need_jump = false     -- 需要跳转是为切换自动跳转
    
    if self.cur_select_type_index == nil then
        -- self.cur_select_type_index = select_index
        need_jump = true
    end

    self.anger_skill_type_list:SetDataList(type_list)

    if need_jump then
        self.anger_skill_type_list:JumpToIndex(select_index, 10)
    else
        self:FlushSkillList()
    end

    -- self:FlushActiveStatus()
end

function CultivationView:FlushActiveStatus()
 
    local act_level = CultivationWGData.Instance:GetAngerLevel(self.cur_select_type_index)
    local max_level = CultivationWGData.Instance:GetNuqiMaxLevel(self.cur_select_type_index)
    local nuqi_upgrade_cfg = CultivationWGData.Instance:GetNuqiUpgradeCfg(self.cur_select_type_index, act_level)
    self.node_list.active_layer:CustomSetActive(act_level < max_level)
    self.node_list.max_layer:CustomSetActive(act_level >= max_level)
    self.node_list.btn_transform:CustomSetActive(act_level ~= 0)
    -- self.node_list.btn_anger_fashion:CustomSetActive(act_level ~= 0)
    self.node_list.active_remind:CustomSetActive(false)
    
    if nuqi_upgrade_cfg then
        self.active_item:SetData({ item_id = nuqi_upgrade_cfg.item_id })
        local item_num = ItemWGData.Instance:GetItemNumInBagById(nuqi_upgrade_cfg.item_id)
        local color = item_num >= nuqi_upgrade_cfg.item_num and COLOR3B.D_GREEN or COLOR3B.D_RED
        self.node_list.active_remind:CustomSetActive(item_num >= nuqi_upgrade_cfg.item_num)
        self.active_item:SetRightBottomTextVisible(true)
        self.active_item:SetRightBottomColorText(item_num .. '/' .. nuqi_upgrade_cfg.item_num, color)
    end

    if act_level ~= 0 then
        -- 刷新幻化状态
        local curr_nuqi_type = CultivationWGData.Instance:GetRoleCurrNuqiType()
        local str = curr_nuqi_type == self.cur_select_type_index and Language.Common.YiHuanHua or Language.Common.HuanHua
        self.node_list.btn_transform_text.text.text = str
    end
end

function CultivationView:FlushSkillStatus(skill_level)

    -- 技能本身从2开始，1是本体，要减1
    -- 表里下标又是从0开始，要减1
    -- 所以去表里拿数据的时候 下标要减去2
    local max_level = CultivationWGData.Instance:GetNuqiSkillMaxLevel(self.cur_select_type_index, self.cur_select_skill_index - 2)
    -- 协议是从1开始的 所以下标减1拿就行
    local nuqi_skill_upgrade_cfg = CultivationWGData.Instance:GetNuqiSkillUpgradeCfg(self.cur_select_type_index,self.cur_select_skill_index - 2, skill_level)
    self.node_list.active_layer:CustomSetActive(skill_level < max_level)
    self.node_list.max_layer:CustomSetActive(skill_level >= max_level)
    -- self.node_list.btn_transform:CustomSetActive(act_level ~= 0)
    self.node_list.active_remind:CustomSetActive(false)
    
    if nuqi_skill_upgrade_cfg then
        self.active_item:SetData({ item_id = nuqi_skill_upgrade_cfg.item_id })
        local item_num = ItemWGData.Instance:GetItemNumInBagById(nuqi_skill_upgrade_cfg.item_id)
        local color = item_num >= nuqi_skill_upgrade_cfg.item_num and COLOR3B.D_GREEN or COLOR3B.D_RED
        local is_active = CultivationWGData.Instance:IsActiveAnger(self.cur_select_type_index)
        self.node_list.active_remind:CustomSetActive(is_active and item_num >= nuqi_skill_upgrade_cfg.item_num)
        self.active_item:SetRightBottomTextVisible(true)
        self.active_item:SetRightBottomColorText(item_num .. '/' .. nuqi_skill_upgrade_cfg.item_num, color)
    end

    if skill_level ~= 0 then
        -- 刷新幻化状态
        local curr_nuqi_type = CultivationWGData.Instance:GetRoleCurrNuqiType()
        local str = curr_nuqi_type == self.cur_select_type_index and Language.Common.YiHuanHua or Language.Common.HuanHua
        self.node_list.btn_transform_text.text.text = str
    end
end

-- 刷新技能列表
function CultivationView:FlushSkillList()
    local skill_list = CultivationWGData.Instance:GetActiveSkillListByType(self.cur_select_type_index) 
    local select_index = 1

    if self.cur_select_skill_index == nil then
        self.cur_select_skill_index = select_index
        local data = {}
        data.skill_id = skill_list[select_index]
        data.anger_type = self.cur_select_type_index
        self.cur_select_skill_data = data
    end

    for i, anger_skill_cell in ipairs(self.anger_skill_list) do
        if skill_list[i] then


            anger_skill_cell:SetVisible(true)
            local data = {}
            data.skill_id = skill_list[i]
            data.anger_type = self.cur_select_type_index
            anger_skill_cell:SetData(data)
        else
            anger_skill_cell:SetVisible(false)
        end
    end

    self:FlushSkillSelect()
    self:FlushSkillMessage()
    -- self:FlushSkillXiuWeiList()
end

-- 刷新技能选中
function CultivationView:FlushSkillSelect()
    -- print_error("刷新技能选中")
    if not self.cur_select_skill_index then
        return
    end
    for i, anger_skill_cell in ipairs(self.anger_skill_list) do
        anger_skill_cell:ChangeSelect(i == self.cur_select_skill_index)
    end

    local name = ""
    

    if self.cur_select_skill_index == Break_Skill_Type.BREAK_SKILL then
        local level_info = CultivationWGData.Instance:GetAngerLevelInfo(self.cur_select_type_index)
        local active_cfg = CultivationWGData.Instance:GetActiveCfgByType(self.cur_select_type_index)
        if active_cfg and active_cfg.changes_name then
            name = active_cfg.changes_name
        end
        if level_info.nuqi_upgrade_level == 0 then
            self.node_list.text_anger_skill_name.tmp.text = name
        else
            self.node_list.text_anger_skill_name.tmp.text = string.format("%s %s%s",name, level_info.nuqi_upgrade_level,Language.Common.Ji)
        end
        

        self:FlushActiveStatus()
    else
        local skill_id = self.cur_select_skill_data.skill_id
        local skill_level = 1
        local skill_cfg = SkillWGData.Instance:GetXiuXianSkillConfig(skill_id, skill_level)
        
        if skill_cfg and skill_cfg.skill_name then
            name = skill_cfg.skill_name
        end
        local skill_level = CultivationWGData.Instance:GetAngerSkillLevel(self.cur_select_type_index, self.cur_select_skill_index - 2)
        self.node_list.text_anger_skill_name.tmp.text = string.format("%s %s%s",name, skill_level,Language.Common.Ji)

        self:FlushSkillStatus(skill_level)
    end
end

-- 刷新Toggle红点
function  CultivationView:FlushToggleRemind()
    local is_can_anger_buff_upgrade = false
    local is_can_anger_skill_upgrade =false
    local is_active = CultivationWGData.Instance:IsActiveAnger(self.cur_select_type_index)
    if is_active then
        is_can_anger_buff_upgrade = CultivationWGData.Instance:IsCanAngerBuffUpgrade(self.cur_select_type_index)
    end

    local is_can_anger_upgrade = CultivationWGData.Instance:IsCanAngerUpgrade(self.cur_select_type_index)
    if not is_can_anger_upgrade and is_active then
        is_can_anger_skill_upgrade = CultivationWGData.Instance:IsCanAllAngerSkillUpgrade(self.cur_select_type_index)
    end
    self.node_list.remind_buff:SetActive(is_can_anger_buff_upgrade)
    self.node_list.remind_level:SetActive(is_can_anger_upgrade or is_can_anger_skill_upgrade)

end

-- 刷新技能详情
function CultivationView:FlushSkillMessage()
    if not self.cur_select_skill_data then
        return
    end

    local skill_id = self.cur_select_skill_data.skill_id
    local skill_level = 1

    if self.cur_select_skill_index == Break_Skill_Type.BREAK_SKILL then
        self.node_list.btn_active:SetActive(true)
        self.node_list.text_active_tips:SetActive(false)
        
        self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(skill_id))
        skill_level = CultivationWGData.Instance:GetAngerLevel(self.cur_select_type_index, self.cur_select_skill_index)

        self.node_list.skill_name.tmp.text = Language.Cultivation["AngerName"..self.cur_select_type_index]
        if CultivationWGData.Instance:IsActiveAnger(self.cur_select_type_index) then
            self.node_list.text_title_anger_active.tmp.text = Language.Cultivation.CostUpgradeDesc
            self.node_list.text_anger_active.tmp.text = Language.Common.Up
        else
            self.node_list.text_title_anger_active.tmp.text = Language.Cultivation.CostActiveDesc
            self.node_list.text_anger_active.tmp.text = Language.Common.Activate

        end

        local nuqi_upgrade_cfg = CultivationWGData.Instance:GetNuqiUpgradeCfg(self.cur_select_type_index, self.cur_select_skill_index)
        self.node_list.skill_desc.tmp.text = nuqi_upgrade_cfg.desc
    else
        skill_level = CultivationWGData.Instance:GetAngerSkillLevel(self.cur_select_type_index, self.cur_select_skill_index - 2)

        local skill_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, skill_level)
        if skill_cfg then
            self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(skill_cfg.icon_resource))
        end
        -- 技能名字 技能描述
        local xiuxian_cfg = SkillWGData.Instance:GetXiuXianSkillConfig(skill_id, skill_level == 0 and 1 or skill_level)
        if xiuxian_cfg then
            local skill_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, skill_level == 0 and 1 or skill_level)
            self.node_list.skill_name.tmp.text = xiuxian_cfg.skill_name
            self.node_list.skill_desc.tmp.text = skill_cfg.description
            -- self.node_list.skill_cd.tmp.text = string.format(Language.Skill.SkillCd, xiuxian_cfg.cd_s / 1000)
        else
            self.node_list.skill_name.tmp.text = Language.Cultivation["AngerName"..self.cur_select_type_index]
            self.node_list.skill_desc.tmp.text = Language.Cultivation.AngerDesc
            -- self.node_list.skill_cd.tmp.text = Language.Cultivation.AngerSkillCD
        end
        -- 未激活时 技能不可升级
        if CultivationWGData.Instance:IsActiveAnger(self.cur_select_type_index) then
            self.node_list.btn_active:SetActive(true)
            self.node_list.text_active_tips:SetActive(false)
        else
            self.node_list.btn_active:SetActive(false)
            self.node_list.text_active_tips:SetActive(true)
            self.node_list.text_active_tips.tmp.text = Language.Cultivation.AngerActiveTips
        end
    end
    self.node_list.skill_lv.tmp.text = string.format(Language.Rank.Level, ToColorStr(skill_level, COLOR3B.GREEN)) 



    
end

-- 刷新技能突破列表
function CultivationView:FlushSkillXiuWeiList()
    -- self.node_list.anger_sp_attr_list:CustomSetActive(self.cur_select_skill_index == Break_Skill_Type.BREAK_SKILL)
    -- self.node_list.anger_skill_attr_list:CustomSetActive(self.cur_select_skill_index ~= Break_Skill_Type.BREAK_SKILL)

    -- if self.cur_select_skill_index == Break_Skill_Type.BREAK_SKILL then
    --     local list = CultivationWGData.Instance:GetSkillXiuweiCache()
    --     self.anger_sp_attr_list:SetDataList(list)
    -- else
    --     local list = CultivationWGData.Instance:GetSkillImproveBySkillId(self.cur_select_skill_data)
    --     self.anger_skill_attr_list:SetDataList(list)
    -- end

    -- 用同一个
    -- self.node_list.anger_sp_attr_list:CustomSetActive(false)
    self.node_list.anger_skill_attr_list:CustomSetActive(true)
    local list = CultivationWGData.Instance:GetSkillImproveBySkillId(self.cur_select_skill_data)
    self.anger_skill_attr_list:SetDataList(list)
end

------------------------------------------------------------------------------
-- 幻化
function CultivationView:OnClickTransform()
    if not self.cur_select_type_index then
        return
    end

    local nuqi_type = CultivationWGData.Instance:GetRoleCurrNuqiType()
    if nuqi_type == self.cur_select_type_index then
        return
    end

    CultivationWGCtrl.Instance:ChooseAngerType(self.cur_select_type_index)
end


-- 怒气技能升级
function CultivationView:AngerSkillUpgrade()
    local skill_level = CultivationWGData.Instance:GetAngerSkillLevel(self.cur_select_type_index, self.cur_select_skill_index - 2)
    local max_level = CultivationWGData.Instance:GetNuqiSkillMaxLevel(self.cur_select_type_index, self.cur_select_skill_index - 2)
    
    if skill_level >= max_level then

    else
        local nuqi_skill_upgrade_cfg = CultivationWGData.Instance:GetNuqiSkillUpgradeCfg(self.cur_select_type_index,self.cur_select_skill_index - 2, skill_level)
        if nuqi_skill_upgrade_cfg then
            local item_num = ItemWGData.Instance:GetItemNumInBagById(nuqi_skill_upgrade_cfg.item_id)
            if item_num >= nuqi_skill_upgrade_cfg.item_num then
                CultivationWGCtrl.Instance:UpgradeAngerSkill(self.cur_select_type_index, self.cur_select_skill_index - 2)
            else
                TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = nuqi_skill_upgrade_cfg.item_id })
            end
        end
    end
end

-- 怒气升级
function CultivationView:AngerUpgrade()
    local nuqi_level = CultivationWGData.Instance:GetAngerLevel(self.cur_select_type_index)
    local max_level = CultivationWGData.Instance:GetNuqiMaxLevel(self.cur_select_type_index)

    if nuqi_level >= max_level then

    else
        local nuqi_upgrade_cfg = CultivationWGData.Instance:GetNuqiUpgradeCfg(self.cur_select_type_index, nuqi_level)
        if nuqi_upgrade_cfg then
            local item_num = ItemWGData.Instance:GetItemNumInBagById(nuqi_upgrade_cfg.item_id)
            if item_num >= nuqi_upgrade_cfg.item_num then
                CultivationWGCtrl.Instance:ActiveAngerType(self.cur_select_type_index)
            else
                TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = nuqi_upgrade_cfg.item_id })
            end
        end
    end
end

-- 怒气buff升级
function CultivationView:AngerBuffUpgrade()
    if CultivationWGData.Instance:IsMaxAngerBuff(self.cur_select_type_index) then

    else
        local is_rich, item_id = CultivationWGData.Instance:IsRichItemAngerBuff(self.cur_select_type_index)
        if is_rich then
            CultivationWGCtrl.Instance:UpgradeAngerBuff(self.cur_select_type_index)
        else
            if item_id ~= 0 then
                TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = item_id })
            end
        end
    end

end

-- 激活
function CultivationView:OnClickActive()
    if not self.cur_select_type_index then
        return
    end

    if self.cur_select_skill_index == Break_Skill_Type.BREAK_SKILL then
        self:AngerUpgrade()
    else
        if CultivationWGData.Instance:IsActiveAnger(self.cur_select_type_index) then
            self:AngerSkillUpgrade()
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Cultivation.AngerActiveTips)
        end
    end
end

-- 升级buff
function  CultivationView:OnClickBuffUpgrade()
    if not self.cur_select_type_index then
        return
    end
    if CultivationWGData.Instance:IsActiveAnger(self.cur_select_type_index) then
        self:AngerBuffUpgrade()
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Cultivation.AngerActiveTips)
    end
end

-- 打开变身幻化
function  CultivationView:OnClickAngerFashion()
    if not self.cur_select_type_index then
        return
    end
    -- CultivationWGCtrl.Instance:OpenAngerFashionView(self.cur_select_type_index)
end
------------------------------------------------------------------------------
----------------------------------页签列表-----------------------
-- 技能类别
AngerSkillTypeRender = AngerSkillTypeRender or BaseClass(BaseRender)
function AngerSkillTypeRender:OnFlush()
	if not self.data then
		return
	end

	-- self.node_list.normal_text.text.text = self.data
    -- self.node_list.select_text.text.text = self.data
    -- local is_red = CultivationWGData.Instance:GetRoleNuqiTypeRed(self.index)
    -- 本体可升级
    local is_red = CultivationWGData.Instance:IsCanAngerUpgrade(self.index)
    if CultivationWGData.Instance:IsActiveAnger(self.index) then
        -- 所有技能可升级
        is_red = is_red or CultivationWGData.Instance:IsCanAllAngerSkillUpgrade(self.index)
        -- buff可升级
        is_red = is_red or CultivationWGData.Instance:IsCanAngerBuffUpgrade(self.index)
    end


    self.node_list.remind:CustomSetActive(is_red)

    local bundle, asset = ResPath.GetCultivationImg(anger_type_res[self.index])
    self.node_list.img_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.img_icon.image:SetNativeSize()
    end)

    local bundle, asset = ResPath.GetCultivationImg(anger_type_res[self.index + 10])
    self.node_list.img_icon_select.image:LoadSprite(bundle, asset, function()
        self.node_list.img_icon_select.image:SetNativeSize()
    end)
end

function AngerSkillTypeRender:OnSelectChange(is_select)
    self.node_list.normal:CustomSetActive(not is_select)
    self.node_list.select:CustomSetActive(is_select)
end

-- 技能
AngerSkillRender = AngerSkillRender or BaseClass(BaseRender)

function AngerSkillRender:OnFlush()
	if not self.data then
		return
	end

    local is_red = false
    if self.index == Break_Skill_Type.BREAK_SKILL then

        local bundle, asset = ResPath.GetCultivationImg(anger_type_res[self.data.anger_type + 10])
        self.node_list.skill_icon.image:LoadSprite(bundle, asset, function()
            self.node_list.skill_icon.image:SetNativeSize()
        end)

        is_red = CultivationWGData.Instance:IsCanAngerUpgrade(self.data.anger_type)
        self.node_list.remind:CustomSetActive(is_red)
    else
        local skill_cfg = SkillWGData.Instance:GetSkillClientConfig(self.data.skill_id)
        if skill_cfg then
            self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(skill_cfg.icon_resource))
        end

        if CultivationWGData.Instance:IsActiveAnger(self.data.anger_type) then
            is_red = is_red or CultivationWGData.Instance:IsCanAngerSkillUpgrade(self.data.anger_type, self.index - 2)
        end
        self.node_list.remind:CustomSetActive(is_red)
    end


end

function AngerSkillRender:ChangeSelect(is_select)
    if self.index ~= Break_Skill_Type.BREAK_SKILL then
        self.node_list.normal:CustomSetActive(not is_select)
        
        self.node_list.select:CustomSetActive(is_select)
    end

end

-- 怒气技能相关
AngerSPAttrRender = AngerSPAttrRender or BaseClass(BaseRender)

function AngerSPAttrRender:OnFlush()
	if not self.data then
		return
	end

    local is_red = false
    self.node_list.attr_str.tmp.text = string.format(Language.Cultivation.AngerSkillStage, self.data.stage_title) 
    self.node_list.attr_name.tmp.text = Language.Cultivation.AngerSkillInit
    self.node_list.attr_value.tmp.text = ToColorStr(string.format("+%s", self.data.init_nuqi), COLOR3B.GREEN)
    self.node_list.attr_name2.tmp.text = Language.Cultivation.AngerSkillReduce
    self.node_list.attr_value2.tmp.text = ToColorStr(string.format("-%s", self.data.init_reduce), COLOR3B.GREEN)
end

-- 怒气技能相关 新
AngerSPAttrRender2 = AngerSPAttrRender2 or BaseClass(BaseRender)

function AngerSPAttrRender2:OnFlush()
	if not self.data then
		return
	end

    local is_red = false
    self.node_list.attr_str.tmp.text = string.format(Language.Cultivation.AngerSkillStage, self.data.stage_title) 
    self.node_list.attr_desc.tmp.text = Language.Cultivation.AngerSkillInit..ToColorStr(string.format("+%s", self.data.init_nuqi), COLOR3B.GREEN)
end

-- 其他技能相关
AngerSkillAttrRender = AngerSkillAttrRender or BaseClass(BaseRender)

function AngerSkillAttrRender:OnFlush()
	if not self.data then
		return
	end

    local stage_data = CultivationWGData.Instance:GetXiuWeiStageCfgByState(self.data.stage)
    if stage_data ~= nil then
        self.node_list.attr_str.tmp.text = string.format(Language.Cultivation.AngerSkillStage, stage_data.stage_title)
    end

    self.node_list.attr_value.tmp.text = self.data.skill_txt
end