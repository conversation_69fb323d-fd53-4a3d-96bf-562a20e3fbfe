-- 跨服红包天降
require("game/red_packet_rain/red_packet_rain_wg_data")
require("game/red_packet_rain/red_packet_rain_view")
require("game/red_packet_rain/red_packet_rain_send_view")
require("game/red_packet_rain/red_packet_rain_info_view")
require("game/red_packet_rain/red_packet_rain_reward_view")
require("game/red_packet_rain/red_packet_rain_send_add_activity_view")
require("game/red_packet_rain/red_packet_rain_tips_view")

RedPacketRainWGCtrl = RedPacketRainWGCtrl or BaseClass(BaseWGCtrl)

function RedPacketRainWGCtrl:__init()
	if RedPacketRainWGCtrl.Instance then
		error("[RedPacketRainWGCtrl]:Attempt to create singleton twice!")
	end
    -- 单例
	RedPacketRainWGCtrl.Instance = self

    self.scene_effect_loadr = nil

    -- 创建data
    self.data = RedPacketRainWGData.New()
    -- 创建view
    -- 一般上需要配置支持跳转的，才需添加对应的模块名GuideModuleName.XXXView
    -- 见 guide_config.lua 关注 GuideModuleName表 FunName表 TabIndex表
    self.view = RedPacketRainView.New(GuideModuleName.RedPacketRainView)
    self.send_view = RedPacketRainSendView.New(GuideModuleName.RedPacketRainSendView)
    self.send_add_activity_view = RedPacketRainSendAddActivityView.New(GuideModuleName.RedPacketRainSendAddActivityView)
    self.info_view = RedPacketRainInfoView.New(GuideModuleName.RedPacketRainInfoView)
    self.reward_view = RedPacketRainRewardView.New()
    self.tips_view = RedPacketRainTipsView.New()

    -- 注册协议
    self:RegisterAllProtocols()

    self:BindGlobalEvent(ObjectEventType.MAIN_ROLE_POS_CHANGE, BindTool.Bind(self.RolePosChange, self))
    self:BindGlobalEvent(SceneEventType.CLOSE_LOADING_VIEW, BindTool.Bind(self.UpdateSceneShow, self))
    -- self:BindGlobalEvent(SceneEventType.SCENE_LOADING_STATE_ENTER, BindTool.Bind(self.DeleteSceneEffect, self))
    self:BindGlobalEvent(ObjectEventType.FLY_TO_SCENE, BindTool.Bind(self.FlyToScene, self))
    
    
end

function RedPacketRainWGCtrl:__delete()
    self:CleanCgTimer()
    self:DeleteSceneEffect()

    -- 销毁data
    self.data:DeleteMe()
	self.data = nil
    -- 销毁view
	self.view:DeleteMe()
	self.view = nil

    self.send_view:DeleteMe()
	self.send_view = nil

    self.info_view:DeleteMe()
	self.info_view = nil

    self.reward_view:DeleteMe()
	self.reward_view = nil

    self.tips_view:DeleteMe()
	self.tips_view = nil

    if self.send_add_activity_view then
        self.send_add_activity_view:DeleteMe()
        self.send_add_activity_view = nil
    end

    RedPacketRainWGCtrl.Instance = nil
end

-- 注册协议
function RedPacketRainWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(CSCrossRedPaperFallingOperate)
    --接收协议 与对应方法名绑定
	self:RegisterProtocol(SCCrossRedPaperFallingRewardInfo, "OnSCCrossRedPaperFallingRewardInfo")
	self:RegisterProtocol(SCCrossRedPaperFallingActivityInfo, "OnSCCrossRedPaperFallingActivityInfo")
	self:RegisterProtocol(SCCrossRedPaperFallingRoundSettlement, "OnSCCrossRedPaperFallingRoundSettlement")
	self:RegisterProtocol(SCCrossRedPaperFallingRoleInfo, "OnSCCrossRedPaperFallingRoleInfo")
	self:RegisterProtocol(SCCrossRedPaperFallingGetRewardInfo, "OnSCCrossRedPaperFallingGetRewardInfo")
end


-- 奖励信息
function RedPacketRainWGCtrl:OnSCCrossRedPaperFallingRewardInfo(protocol)
	local info  = protocol.reward_info
    self.data:SetRewardInfo(protocol)
    local count = 0
    for k, v in pairs(info.current_round_reward_flag) do
        if v == 1 then
            count = count + 1
        end
    end
    if self.view and self.view:IsOpen() then
        self.view:Flush(0, "flush_count", {count = count})
    end
end

-- 活动信息
function RedPacketRainWGCtrl:OnSCCrossRedPaperFallingActivityInfo(protocol)
    -- print_error("OnSCCrossRedPaperFallingActivityInfo： ", protocol.activity_info.round_state, protocol.activity_info.enjoy_status, "位置：", self.data:IsInRedPacketArea())
    local old_data = self.data:GetAcitivityInfo()
    self.data:SetAcitivityInfo(protocol)


    if protocol.activity_info.round_state == 3 then
        -- if RedPacketRainWGCtrl.Instance:IsCanPlayCg() then
        --     RedPacketRainWGCtrl.Instance:PlayCg()
        -- end
	elseif protocol.activity_info.round_state == 1 then
        if not self.data:IsInRedPacketArea() then
            -- TipWGCtrl.Instance:OpenAlertTips(Language.RedPacketRain.ActivityOpen,function ()
            --     -- 停掉挂机
            --     GuajiWGCtrl.Instance:StopGuaji()
            --     TaskGuide.Instance:CanAutoAllTask(false)
            --     self:RedPacketOperate(CROSS_RED_PAPER_FALLING_OPERATE_TYPE.CROSS_RED_PAPER_FALLING_OPERATE_TYPE_ENTER)
            -- end,nil,nil,nil,5,nil,Language.RedPacketRain.Join)
            self:OpenTipsView()
        else
            if RedPacketRainWGCtrl.Instance:IsCanPlayCg() then
                RedPacketRainWGCtrl.Instance:PlayCg()
            end
        end
    elseif protocol.activity_info.round_state == 2 and protocol.activity_info.enjoy_status == 1 and self.data:IsInRedPacketArea() then
        TipWGCtrl.Instance:CloseShowGetReward()
        ViewManager.Instance:Open(GuideModuleName.RedPacketRainView)
    end

    if old_data == nil then
        self:UpdateRedPacketRainState(true)
    else
        self:UpdateRedPacketRainState()
    end

    -- print_error("round_state:",protocol.activity_info.round_state)
    if protocol.activity_info.round_state == 0 then
        -- self:CloseInfoView()
        -- MainuiWGCtrl.Instance:ActivitySetButtonVisible(self.data:GetActivityId(), false)
    else
        
    end
    self:ShowSceneEffect()
    if self.info_view and self.info_view:IsOpen() then
        self.info_view:Flush()
    end

    MainuiWGCtrl.Instance:FlushView(0, "set_red_packet_rain")
end

-- 结算奖励
function RedPacketRainWGCtrl:OnSCCrossRedPaperFallingRoundSettlement(protocol)
    local temp_item_list = {}
    for k, v in pairs(protocol.reward_list) do
        if temp_item_list[v.item_id] then
            temp_item_list[v.item_id].num = temp_item_list[v.item_id].num + v.num
        else
            temp_item_list[v.item_id] = v
        end
    end
    local item_list = {}
    for k, v in pairs(temp_item_list) do
        table.insert(item_list,v)
    end
    self:OpenRewardView(item_list)
	--TipWGCtrl.Instance:ShowGetReward(nil, item_list)
    self.view:Close()
end

-- 结算奖励
function RedPacketRainWGCtrl:OnSCCrossRedPaperFallingRoleInfo(protocol)
    self.data:SetRoleInfo(protocol)
    if self.info_view and self.info_view:IsOpen() then
        self.info_view:Flush()
    end
end

--红包弹幕.
function RedPacketRainWGCtrl:OnSCCrossRedPaperFallingGetRewardInfo(protocol)
    if self.view and self.view:IsOpen() then
        self.view:AddOneSpecialDanMu(protocol)
    end
end


-- 协议发送
function RedPacketRainWGCtrl:RedPacketOperate(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossRedPaperFallingOperate)
	protocol.operate_type = operate_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function RedPacketRainWGCtrl:OpenSendView()
    if self.send_view then
        self.send_view:Open()
    end
end

function RedPacketRainWGCtrl:OpenSendAddActivityView(item_id)
    if self.send_add_activity_view then
        if item_id and item_id > 0 then
            self.send_add_activity_view:SetAddActivityItemId(item_id)
            self.send_add_activity_view:Open()
        end
        
    end
end

function RedPacketRainWGCtrl:OpenInfoView()
    -- if self.info_view then
    --     self.info_view:Open()
    -- end
    ViewManager.Instance:Open(GuideModuleName.RedPacketRainInfoView)
end

function RedPacketRainWGCtrl:CloseInfoView()
    if self.info_view and self.info_view:IsOpen() then
        self.info_view:Close()
    end

    if self.reward_view and self.reward_view:IsOpen() then
        self.reward_view:Close()
    end
end
function RedPacketRainWGCtrl:OpenRewardView(data)
    
    if self.reward_view then
        self.reward_view:SetData(data)
        self.reward_view:Open()
    end
end

function RedPacketRainWGCtrl:OpenTipsView()
    if self.tips_view then
        self.tips_view:Open()
    end
end



function RedPacketRainWGCtrl:RolePosChange()
    self:UpdateRedPacketRainState()
end

function RedPacketRainWGCtrl:UpdateSceneShow()
    self:UpdateRedPacketRainState(true)
    self:ShowSceneEffect()
end

function RedPacketRainWGCtrl:IsCanJoin()
    local role_level =  RoleWGData.Instance:GetRoleLevel()
    local limit_level = RedPacketRainWGData.Instance:GetOtherCfg("level_limit")
    return role_level >= limit_level
end

-----------------------场景CG-------------------------
-- 播放出场CG
function RedPacketRainWGCtrl:PlayCg()
    if not self.data:IsInRedPacketScene() then
        return
    end
    -- 停掉挂机
    GuajiWGCtrl.Instance:StopGuaji()
    -- 
	local cg_bundle = "cg/a3_cg_zhucheng_prefab"
	local cg_asset = "A3_CG_ZhuCheng3"

	CgManager.Instance:Play(BaseCg.New(cg_bundle, cg_asset),
		function()
		end,
		function(cg_obj)
		end,
	nil)
end

function RedPacketRainWGCtrl:IsCanPlayCg()
    -- 护送时不受影响
	if YunbiaoWGData.Instance:GetIsHuShong() then
		return false
	end
    -- 渡劫有cg 不能影响
    if ViewManager.Instance:IsOpen(GuideModuleName.DujieView) or ViewManager.Instance:IsOpen(GuideModuleName.DujieOperateView) then
        return false
    end
    -- 免费轮次
    local free_open_round = self.data:GetOtherCfg("free_open_round")
    local activity_info = RedPacketRainWGData.Instance:GetAcitivityInfo()
    -- if activity_info.current_round  == free_open_round   then
        local next_time = activity_info.next_state_time - TimeWGCtrl.Instance:GetServerTime()
        if next_time > 25 then
            self:CleanCgTimer()
            self.cg_tiemer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.PlayCg,self), next_time - 25)
        else
            return true
        end
    -- end
    return false
end

function RedPacketRainWGCtrl:CleanCgTimer()
    if self.cg_tiemer then
        GlobalTimerQuest:CancelQuest(self.cg_tiemer)
        self.cg_tiemer = nil
    end
end
--------------------- 场景特效----------------------
function RedPacketRainWGCtrl:ShowSceneEffect()
    if (ActivityWGData.Instance:GetActivityIsOpen(RedPacketRainWGData.Instance:GetActivityId()) or RedPacketRainWGData.Instance:IsOpenItemAcitivity()) and self:IsCanJoin() then
        -- local activity_info = RedPacketRainWGData.Instance:GetAcitivityInfo() 
		if  self.data:IsInRedPacketScene() then --activity_info and activity_info.round_state ~= 0 and 
            self:SetSceneEffect(true)
        else
            self:DeleteSceneEffect()
        end

    else
        self:DeleteSceneEffect()
    end
end

function RedPacketRainWGCtrl:SetSceneEffect(is_show)
    if self.scene_effect_loadr == nil then
        local key = "sh_art_scene_effect_red_packet"
        self.scene_effect_loadr = AllocAsyncLoader(self, key)
    end
    local bundle = "effects/prefab/environment/common/eff_yuanbao_diaoluo_prefab"
    local asset = "eff_yuanbao_diaoluo"
    local is_change = false
    if is_show then
        if self.effect_bundle == nil and self.effect_asset == nil then
            self.effect_bundle = bundle
            self.effect_asset = asset
            is_change = true

        end
    end

    self.scene_effect_loadr:SetActive(is_show)
    if is_change then
        self.scene_effect_loadr:SetIsUseObjPool(true)
        self.scene_effect_loadr:SetParent(G_EffectLayer)
        self.scene_effect_loadr:Load(bundle, asset, function(obj)
            if obj == nil then
                return
            end

            local pos_x, pos_y = self.data:GetRedPakcetPos()
            local wx, wy = GameMapHelper.LogicToWorld(pos_x, pos_y)
            local move_obj = obj:GetOrAddComponent(typeof(MoveableObject))
            move_obj:SetPosition(Vector3(wx, 0, wy))
            move_obj.gameObject.name = asset
        end)
    end
end

function RedPacketRainWGCtrl:FlyToScene(scene_id)
    if scene_id ~= RedPacketRainWGData.Instance:IsInRedPacketScene() then
        self:DeleteSceneEffect()
    end
end

function RedPacketRainWGCtrl:DeleteSceneEffect()
    if self.scene_effect_loadr ~= nil then
        self.scene_effect_loadr:DeleteMe()
        self.scene_effect_loadr = nil
        self.effect_bundle = nil
        self.effect_asset = nil
    end
end

-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    跨服红包天降     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-- 跨服红包天降
function RedPacketRainWGCtrl:UpdateRedPacketRainState(is_must_refresh)
	-- 是否活动开启
	if (ActivityWGData.Instance:GetActivityIsOpen(RedPacketRainWGData.Instance:GetActivityId()) or RedPacketRainWGData.Instance:IsOpenItemAcitivity()) and self:IsCanJoin()  then
		local activity_info = RedPacketRainWGData.Instance:GetAcitivityInfo() 
        -- 策划说不能抢，不能加场次的时候 活动还是要显示(如果说不显示就把下面的取消注释)
		-- if  not activity_info or activity_info.round_state == 0 then
		-- 	if self.is_in_red_packet_rain_area then
		-- 		self:HideInfoView()
		-- 	end
		-- 	return 
		-- end

		local old_state = self.is_in_red_packet_rain_area

		self:CheckIsInRedPacketRainArea()

		if old_state ~= self.is_in_red_packet_rain_area or is_must_refresh then
			if self.is_in_red_packet_rain_area then
				-- 隐藏其他task_view 显示红包的
				MainuiWGCtrl.Instance:SetTaskContents(false)
				MainuiWGCtrl.Instance:SetOtherContents(true)
				self:OpenInfoView()
                if activity_info and activity_info.round_state ~= 0 and self.data:IsCanUseItem() then
                    local role_id = RoleWGData.Instance:InCrossGetOriginUid()
                    local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
                    local value = PlayerPrefsUtil.GetInt("red_packet_rain_send" .. role_id .. cur_day)
                    if value == 0 then
                        self:OpenSendView()
                    end
                end
			else
				self:HideInfoView()

                if activity_info and activity_info.round_state ~= 0 and old_state ~= nil then
                    TipWGCtrl.Instance:OpenAlertTips(Language.RedPacketRain.Leval2,function ()
                        -- 停掉挂机
                        GuajiWGCtrl.Instance:StopGuaji()
                        TaskGuide.Instance:CanAutoAllTask(false)
                        self:RedPacketOperate(CROSS_RED_PAPER_FALLING_OPERATE_TYPE.CROSS_RED_PAPER_FALLING_OPERATE_TYPE_ENTER)
                    end)
                end

			end
		end
	else
		if self.is_in_red_packet_rain_area then
            self:HideInfoView()
			self.is_in_red_packet_rain_area = false
		end
	end
end

-- 是否在跨服红包天降区域
function RedPacketRainWGCtrl:CheckIsInRedPacketRainArea()
	self.is_in_red_packet_rain_area = RedPacketRainWGData.Instance:IsInRedPacketArea()
end

function RedPacketRainWGCtrl:HideInfoView()

    MainuiWGCtrl.Instance:SetTaskContents(true)
    MainuiWGCtrl.Instance:SetOtherContents(false)
    -- RedPacketRainWGCtrl.Instance:CloseInfoView()

    self.is_in_red_packet_rain_area = false
end
