BullScreenBarrageView = BullScreenBarrageView or BaseClass(SafeBaseView)

local BULL_SCREEN_BARRAGE_DEFAULT_SHOW_TIME = 30

--[[
    rumor_type = 
    desc_content = 
    reward_data_list = 
    show_time = 30
]]

function BullScreenBarrageView:__init()
    self.view_style = ViewStyle.Window
    self:SetMaskBg(true, false)
    local bundle = "uis/view/customized_rumors_ui_prefab"
    self:AddViewResource(0, bundle, "layout_regional_barrage_view")
end

function BullScreenBarrageView:LoadCallBack()
    if not self.regional_barrage then
        self.regional_barrage = RegionalBarrage.New(self.node_list.regional_barrage_area)
        self.regional_barrage:SetGetDanMuInfoFunc(BindTool.Bind(function ()
            return CustomizedRumorsWGData.Instance:GetBullScreenBarrageMessage()
        end, self))
    end

    if not self.special_rumior_root then
        self.special_rumior_root = BaseSpecialRumorsCell.New(self.node_list.special_rumior_root)
    end

    if not self.reward_show_list then
        self.reward_show_list = AsyncListView.New(ItemCell, self.node_list.reward_show_list)
    end

    XUI.AddClickEventListener(self.node_list.btn_tranks, BindTool.Bind(self.OnClicThanks, self))
end

function BullScreenBarrageView:ReleaseCallBack()
    if self.regional_barrage then
        self.regional_barrage:DeleteMe()
        self.regional_barrage = nil 
    end

    if self.special_rumior_root then
        self.special_rumior_root:DeleteMe()
        self.special_rumior_root = nil 
    end

    if self.reward_show_list then
        self.reward_show_list:DeleteMe()
        self.reward_show_list = nil 
    end

    self:StorAniTween()
end

function BullScreenBarrageView:ShowIndexCallBack()
    if self.regional_barrage then
        self.regional_barrage:StartShowDanMu(0.3)
    end
end

function BullScreenBarrageView:CloseCallBack()
    -- if self.regional_barrage then
    --     self.regional_barrage:StopAndClearDanMu()
    -- end

    if self.delay_close_func then
        GlobalTimerQuest:CancelQuest(self.delay_close_func)
        self.delay_close_func = nil
    end
end

function BullScreenBarrageView:SetDataList(data)
	if self.show_data == nil then
		self.show_data = {}
	end

	table.insert(self.show_data, data)
end

function BullScreenBarrageView:OnFlush()
	if IsEmptyTable(self.show_data) then
		self:Close()
		return
	end

    local data = self.show_data[1]
    XUI.SetButtonEnabled(self.node_list.btn_tranks, true)

    local rumor_data = {
        rumor_type = data.rumor_type,
        desc_rumors_content = data.desc_content,   -- 对接数据后 如果定制了显示定制
        show_yoyo_tween = true,
    }

    self.special_rumior_root:SetData(rumor_data)
    self.reward_show_list:SetDataList(data.reward_data_list)

    self:StorAniTween()

    local show_time = data.show_time or BULL_SCREEN_BARRAGE_DEFAULT_SHOW_TIME

    self.node_list.count_down_time.text.text = string.format(Language.CustomizedRumors.AutoCloseTime, show_time)
    self.animator_timer = CountDownManager.Instance:AddCountDown("BullScreenBarrageView", function (elapse_time, total_time)
            local valid_time = math.ceil(total_time - elapse_time)

            if self.node_list.count_down_time then
                self.node_list.count_down_time.text.text = string.format(Language.CustomizedRumors.AutoCloseTime, valid_time)
            end
        end,
        function ()
            table.remove(self.show_data, 1)
		    self:ShowNextRumorsHint()
        end,
    nil, show_time, 1)
end

function BullScreenBarrageView:StorAniTween()
	if self.animator_timer then
        CountDownManager.Instance:RemoveCountDown("BullScreenBarrageView")
		self.animator_timer = nil
	end
end

function BullScreenBarrageView:ShowNextRumorsHint()
	if self.need_stop then
		self.show_data = {}
	end

    self:Flush()
end

function BullScreenBarrageView:OnClicThanks()
    -- self:Flush()
    CustomizedRumorsWGCtrl.Instance:OpenCustomizedSetBarrageView()
end

function BullScreenBarrageView:AddOneSpecialDanMu(desc_content)
    if self.regional_barrage then
        local bg_bundle, bg_asset = ResPath.GetCustomizedRumorsImg("a2_dzcw_di2_1")
        local danmu_info = {
            desc_content = desc_content,
            need_random_color = false,
            bg_bundle = bg_bundle,
            bg_asset = bg_asset,
            move_speed = 200,
            color_data_list = ITEM_COLOR_DARK}

        XUI.SetButtonEnabled(self.node_list.btn_tranks, false)
        self.regional_barrage:AddOneTemporaryDanMu(danmu_info)
    end
end