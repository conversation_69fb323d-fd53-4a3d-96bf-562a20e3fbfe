
ZhuoYiYunChengSelectView = ZhuoYiYunChengSelectView or BaseClass(SafeBaseView)
function ZhuoYiYunChengSelectView:__init()
	self:SetMaskBg(true,true)

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/zhouyi_yuncheng_prefab", "layout_select_rewared_view")
end

function ZhuoYiYunChengSelectView:__delete()

end

function ZhuoYiYunChengSelectView:ReleaseCallBack()

	if self.change_select_list then
		self.change_select_list:DeleteMe()
		self.change_select_list = nil
	end

end

function ZhuoYiYunChengSelectView:OpenCallBack()
end

function ZhuoYiYunChengSelectView:LoadCallBack()
	self:SetSecondView(nil, self.node_list["bg_size"])
	self.node_list.title_view_name.text.text = Language.ZhouYiYunCheng.SelectViewTitle
	self.change_select_list = AsyncListView.New(YunChengSelectBigCell, self.node_list.xianling_list)
	self.node_list["confirm"].button:AddClickListener(BindTool.Bind(self.OnClickConfirm,self))
end

function ZhuoYiYunChengSelectView:OnClickConfirm()
	local all_select_flag = ZhouYiYunChengWGData.Instance:GetTempSelectList()
	for i = 1, 4 do
		local have_num,need_num = ZhouYiYunChengWGData.Instance:GetHaoManyWeSelect(i)
		if have_num ~= need_num then
			TipWGCtrl.Instance:ShowSystemMsg(Language.ZhouYiYunCheng.PleaseSelect)
			return
		end
	end

	local default_select = {}
	for i = 1 , 4 do
		default_select[i] = {}
		for q = 1, 32 do
			default_select[i][33-q] = all_select_flag[i][q] and 1 or 0
		end
	end

	ZhouYiYunChengWGCtrl.Instance:SendYunChengRollReq(RA_HAPPY_MONDAY_OP.RA_HAPPY_MONDAY_OP_CHOOSE_REWARD,bit:b2d(default_select[1]),bit:b2d(default_select[2]),bit:b2d(default_select[3]),bit:b2d(default_select[4]))
	self:Close()
end

function ZhuoYiYunChengSelectView:OnFlush()
	local all_data = ZhouYiYunChengWGData.Instance:GetAllItemList()
	self.change_select_list:SetDataList(all_data)
end


YunChengSelectBigCell = YunChengSelectBigCell or BaseClass(BaseRender)

function YunChengSelectBigCell:__init()
	self.change_select_list = AsyncListView.New(YunChengSelectSmallCell, self.node_list.item_group)
end

function YunChengSelectBigCell:__delete()
	if self.change_select_list then
		self.change_select_list:DeleteMe()
		self.change_select_list = nil
	end
end

function YunChengSelectBigCell:OnFlush()
	if not IsEmptyTable(self.data) then
		self.level = self.data[1].index1
		for k,v in pairs(self.data) do
			v.click_call_back = function ()
				self:FlushSelectNum()
			end
		end
		self.change_select_list:SetDataList(self.data)
		local bundle, asset = ResPath.GetZhouYiYunChengImg("zyyc_t"..self.level)
 		self.node_list["img"].image:LoadSpriteAsync(bundle, asset,function ()
			self.node_list["img"].image:SetNativeSize()
		end)

		self:FlushSelectNum()
	else

	end
end


function YunChengSelectBigCell:FlushSelectNum()
	local have_num,limite_num = ZhouYiYunChengWGData.Instance:GetHaoManyWeSelect(self.level)
	local color = have_num == limite_num and COLOR3B.GREEN or COLOR3B.RED
	local str = "("..ToColorStr(have_num,color).."/"..limite_num..")"
	self.node_list["select_num"].text.text = str
end

YunChengSelectSmallCell = YunChengSelectSmallCell or BaseClass(BaseRender)

function YunChengSelectSmallCell:__init()
	self.item_cell = ItemCell.New(self.node_list["item_pos"])
	--self.item_cell:UseNewSelectEffect(true)
	self.node_list["click_area"].button:AddClickListener(BindTool.Bind(self.OnClickItem, self))
	self.node_list["select_area"].button:AddClickListener(BindTool.Bind(self.OnClickSelect, self))
end

function YunChengSelectSmallCell:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function YunChengSelectSmallCell:OnFlush()
	if not IsEmptyTable(self.data) then
		local cell_data = {}
		cell_data.item_id = tonumber(self.data.item_id)
		cell_data.num = tonumber(self.data.num)
		cell_data.is_bind = tonumber(self.data.is_bind)
		self.item_cell:SetData(cell_data)
	else

	end
	self:FlushSelect()
end

function YunChengSelectSmallCell:OnClickSelect()
	local index1 = self.data.index1
	local index2 = self.data.index2
	local value = ZhouYiYunChengWGData.Instance:TryToSelectOrUnSelectThis(index1, index2)
	if value then
		self:FlushSelect()
		if self.data.click_call_back then
			self.data.click_call_back()
		end
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.ZhouYiYunCheng.IsSelectEnough)
	end
end

function YunChengSelectSmallCell:FlushSelect()
	local index1 = self.data.index1
	local index2 = self.data.index2
	local is_select = ZhouYiYunChengWGData.Instance:GetItemIsBeenSelect(index1, index2)
	self.node_list["select_flag"]:SetActive(is_select)
	--self.item_cell:SetSelectEffect(is_select)
end

function YunChengSelectSmallCell:OnClickItem()
	local cell_data = {}
	cell_data.item_id = tonumber(self.data.item_id)
	cell_data.num = tonumber(self.data.num)
	cell_data.is_bind = tonumber(self.data.is_bind)
	TipWGCtrl.Instance:OpenItem(cell_data)
end