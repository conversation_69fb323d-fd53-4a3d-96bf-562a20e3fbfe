-- 养龙寺
function CountryMapActView:LoadIndexCallBackYangLongSi()
	self:FlushYangLongSiBossModel()

	if not self.yanglongsi_reward_list then
		self.yanglongsi_reward_list = AsyncBaseGrid.New()
		self.yanglongsi_reward_list:CreateCells({col = 4, change_cells_num = 1,
						list_view = self.node_list["yanglongsi_reward_list"],
						assetBundle = "uis/view/country_map_ui/yanglonsi_ui_prefab", assetName = "yanglongsi_reward_item",
						itemRender = YangLongSiRewardItem})
	end

	local reward_cfg = YangLongSiaWGData.Instance:GetOtherCfg()
	if not IsEmptyTable(reward_cfg) then
		self.yanglongsi_reward_list:SetDataList(reward_cfg.show_reward_item)
	end

	self.reward_list = {}
	for i = 1, 5 do
		if not self.reward_list[i] then
			self.reward_list[i] = YangLongSiRewardBoxItem.New(self.node_list["boss_reward_list" .. i])
		end
	end

	self.node_list.boss_flush_desc.text.text = Language.YangLongSi.BossFlushDesc
	self.node_list.yanglongsi_des.text.text = Language.YangLongSi.ShenMiHaiYuDes

	XUI.AddClickEventListener(self.node_list.go_yanglongsi_btn, BindTool.Bind1(self.OnClickGoToYangLongSi, self))
	XUI.AddClickEventListener(self.node_list.yanglongsi_reward_btn, BindTool.Bind1(self.OnClickYangLongSiRewardBtn, self))
	XUI.AddClickEventListener(self.node_list.yanglongsi_lsgw_btn, BindTool.Bind1(self.OnClickYangLongSiLSGWBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_boss_focus, BindTool.Bind1(self.OnClickBossFocusBtn, self))
end

function CountryMapActView:ReleaseYangLongSi()
	if self.yanglongsi_reward_list then
		self.yanglongsi_reward_list:DeleteMe()
		self.yanglongsi_reward_list = nil
	end

	if self.reward_list then
		for k, v in pairs(self.reward_list) do
			v:DeleteMe()
		end
	end

	if self.yanglongsi_display_model then
		self.yanglongsi_display_model:DeleteMe()
		self.yanglongsi_display_model = nil
	end
end

function CountryMapActView:OnFlushYangLongSi(param_t, index)
	local data_list = YangLongSiaWGData.Instance:GetRewardDataList()

	for i = 1, 5 do
		local active = not IsEmptyTable(data_list[i - 1])

		if active then
			self.reward_list[i]:SetData(data_list[i - 1])
		end

		self.node_list["boss_reward_list" .. i]:SetActive(active)
	end

	self.node_list.yanglongsi_des_scroll_view.scroll_rect.verticalNormalizedPosition = 1

	self:FlushLSGWRemind()

	local noss_notice_flag = YangLongSiaWGData.Instance:GetBossRefreshRemindFlag()
	self.node_list.btn_boss_focus_gou:SetActive(noss_notice_flag)
end

function CountryMapActView:FlushLSGWRemind()
	local yanglongsi_lsgw_remind = YangLongSiaWGData.Instance:GetLSGWRemind()
	self.node_list.yanglongsi_lsgw_remind:SetActive(yanglongsi_lsgw_remind)
end

function CountryMapActView:FlushYangLongSiBossModel()
	local boss_data = YangLongSiaWGData.Instance:GetShowBossData()

	if not IsEmptyTable(boss_data) then
		if  nil == self.yanglongsi_display_model then
			self.yanglongsi_display_model = RoleModel.New()
			local display_data = {
				parent_node = self.node_list["yanglongsi_boss_model"],
				camera_type = MODEL_CAMERA_TYPE.BASE,
				-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
				rt_scale_type = ModelRTSCaleType.M,
				can_drag = true,
			}
	
			self.yanglongsi_display_model:SetRenderTexUI3DModel(display_data)
			-- self.yanglongsi_display_model:SetUI3DModel(self.node_list["yanglongsi_boss_model"].transform, self.node_list["yanglongsi_boss_model"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
		end

		local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[boss_data.monster_id] --获取boss模型
		self.yanglongsi_display_model:SetMainAsset(ResPath.GetMonsterModel(monster_cfg.resid))

		if boss_data.display_scale and  boss_data.display_scale ~= "" then
			local scale = boss_data.display_scale
			Transform.SetLocalScaleXYZ(self.node_list["yanglongsi_boss_model"].transform, scale, scale, scale)
		end

		if boss_data.display_pos and boss_data.display_pos ~= "" then
			local pos_x, pos_y = 0, 0
			local pos_list = string.split(boss_data.display_pos, "|")
			pos_x = tonumber(pos_list[1]) or pos_x
			pos_y = tonumber(pos_list[2]) or pos_y
			RectTransform.SetAnchoredPositionXY(self.node_list["yanglongsi_boss_model"].rect, pos_x, pos_y)
		end
		
		if boss_data.display_rotation and boss_data.display_rotation ~= "" then
			local rotation_tab = string.split(boss_data.display_rotation,"|")
			self.node_list["yanglongsi_boss_model"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
		end
	end
end

function CountryMapActView:OnClickYangLongSiRewardBtn()
	YangLongSiWGCtrl.Instance:OpenRankRewardPanel()
end

function CountryMapActView:OnClickGoToYangLongSi()
	local open_level = YangLongSiaWGData.Instance:GetOtherCfgData("open_level") or 0
	local role_level = RoleWGData.Instance:GetRoleLevel()
	if role_level < open_level then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.YangLongSi.LevelLimitJoin, RoleWGData.GetLevelString2(open_level)))
		return
	end

	local can_enter, reason = YangLongSiaWGData.Instance:CanEnterFightYangLongSiBoss()

    if not can_enter then
        SysMsgWGCtrl.Instance:ErrorRemind(reason)
        return
    end

	-- local my_jieyi_state = SwornWGData.Instance:HadSworn()
	-- if my_jieyi_state then
		CrossServerWGCtrl.Instance.SendCrossStartReq(ACTIVITY_TYPE.KF_YANGLONGSI)
	-- else
		-- TipWGCtrl.Instance:ShowSystemMsg(Language.YangLongSi.NoActivity)
	-- end
end

function CountryMapActView:OnClickYangLongSiLSGWBtn()
	YangLongSiWGCtrl.Instance:OpenYangLongSiLSGWView()
end

function CountryMapActView:OnClickBossFocusBtn()
	local noss_notice_flag = YangLongSiaWGData.Instance:GetBossRefreshRemindFlag()
	local is_remind = noss_notice_flag and 0 or 1
	YangLongSiWGCtrl.Instance:SendCSCrossYangLongOperate(CROSS_YANGLONG_OPERATE_TYPE.CROSS_YANGLONGOPERATE_TYPE_SET_REFRESH_REMIND_FALG, is_remind)
end

------------------------------

YangLongSiRewardBoxItem = YangLongSiRewardBoxItem or BaseClass(BaseRender)
function YangLongSiRewardBoxItem:__init()
	self.reward_cell_list = {}
end

function YangLongSiRewardBoxItem:__delete()
	self.reward_cell_list = nil

	if self.reward_cell_list then
		for k, v in pairs(self.reward_cell_list) do
			v:DeleteMe()
		end
	end
end

function YangLongSiRewardBoxItem:OnFlush()
	if IsEmptyTable(self.data) then
		return	
	end

	for i = 1, 4 do
		if not self.reward_cell_list[i] then
			self.reward_cell_list[i] = YangLongSiRewardBoxItemCell.New(self.node_list["yanglongsi_reward_box" .. i])
		end

		local active = not IsEmptyTable(self.data[i])
		self.node_list["yanglongsi_reward_box" .. i]:SetActive(active)

		if active then
			self.reward_cell_list[i]:SetData(self.data[i])
		end
	end
end

YangLongSiRewardBoxItemCell = YangLongSiRewardBoxItemCell or BaseClass(BaseRender)
function YangLongSiRewardBoxItemCell:__init()
	XUI.AddClickEventListener(self.node_list.get_btn, BindTool.Bind1(self.OnClickGetBtn, self))
end

function YangLongSiRewardBoxItemCell:OnFlush()
	if IsEmptyTable(self.data) then
		return	
	end

	local rank = self.data.rank
	local show_box_id = rank <= 3 and rank or 3

	for i = 1, 3 do
		self.node_list["box" .. i]:SetActive(i == show_box_id)
	end
end

function YangLongSiRewardBoxItemCell:OnClickGetBtn()
	if not IsEmptyTable(self.data) then
		YangLongSiaWGData.Instance:SetGetRewardItemIndex(self.data.index)
		YangLongSiWGCtrl.Instance:SendCSCrossYangLongOperate(CROSS_YANGLONG_OPERATE_TYPE.CROSS_YANGLONGOPERATE_TYPE_FETCH_RANK_REWARD, self.data.index)
	end
end

-----------------------------------------养龙寺界面奖励item------------------
YangLongSiRewardItem = YangLongSiRewardItem or BaseClass(BaseRender)

function YangLongSiRewardItem:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.yanglongsi_reward_item)
end

function YangLongSiRewardItem:ReleaseCallBack()
	if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function YangLongSiRewardItem:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end
	self.item_cell:SetData(self.data)
	RectTransform.SetLocalScale(self.node_list.yanglongsi_reward_item.rect, 0.9)
end