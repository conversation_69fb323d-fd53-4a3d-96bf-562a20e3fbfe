--场景内view
ExperienceFBSceneView = ExperienceFBSceneView or BaseClass(SafeBaseView)

local WAVE_BUFF = {
	ADD_WAVE_TIME = 0,
	ADD_BUFF = 1,
	ADD_SKILL_HURT = 2,
	ADD_SKILL_POWER = 3,
}

function ExperienceFBSceneView:__init()
	self.is_safe_area_adapter = true
    self:AddViewResource(0, "uis/view/offlinerest_ui_prefab", "layout_experience_fb_scene_view")
end

function ExperienceFBSceneView:OpenCallBack()
	if self.obj and self:IsLoadedIndex(0) then
        self.obj:SetActive(true)
    end
end

function ExperienceFBSceneView:LoadCallBack()
    MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))

    if not self.item_list then
	    self.item_list = AsyncListView.New(ItemCell, self.node_list.reward_item_list)
		self.item_list:SetStartZeroIndex(true)
    end

	if not self.select_buff_list then
		self.select_buff_list = {}

		for i = 1, 3 do
			self.select_buff_list[i] = ExperienceFBSelectBuff.New(self.node_list[string.format("select_buff_render_%d", i)])
			self.select_buff_list[i]:SetIndex(i)
			self.select_buff_list[i]:SetClickCallBack(BindTool.Bind1(self.SelectBuffIndex, self))
		end
	end

	
	self.alpha_tween = self.node_list.exp_west_times_tips:GetComponent(typeof(UGUITweenAlpha))
	-- self.fight_start_alpha_tween = self.node_list.fight_start:GetComponent(typeof(UGUITweenAlpha))
	self.boss_start_alpha_tween = self.node_list.boss_start:GetComponent(typeof(UGUITweenAlpha))
	XUI.AddClickEventListener(self.node_list.exp_west_skill, BindTool.Bind(self.OnClickUsePoserSkill, self))
	XUI.AddClickEventListener(self.node_list.attr_message_btn, BindTool.Bind(self.OnClickAttrMessage, self))
end

function ExperienceFBSceneView:InitCallBack()
	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()
	if self.node_list.task_root then
		self.obj = self.node_list.task_root.gameObject
		self.obj.transform:SetParent(parent.gameObject.transform)
		self.obj.transform.localPosition = Vector3(0, 0, 0)
		self.obj.transform.localScale = Vector3.one

		MainuiWGCtrl.Instance:SetFBNameState(false)
	end

    if self.is_out_fb then
        self.obj:SetActive(false)
    end

    self.is_out_fb = nil
	self.alpha_tween = nil
	-- self.fight_start_alpha_tween = nil
	self.boss_start_alpha_tween = nil
end

function ExperienceFBSceneView:CloseCallBack()
	if self.node_list and self.node_list.select_buff_root then
		self.node_list.select_buff_root:CustomSetActive(false)
	end

	self.is_out_fb = true
    if self.obj then
        self.obj:SetActive(false)
    end
end

function ExperienceFBSceneView:ReleaseCallBack()
	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end

    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end

    if self.select_buff_list then
		for i, v in ipairs(self.select_buff_list) do
			if v then
				v:DeleteMe()
			end
		end

		self.select_buff_list = nil
    end

	self.card_list = nil
	self.wave_end_time = nil
	self:CleanTimeDown()
end

function ExperienceFBSceneView:ShowIndexCallBack()
	if self.obj then
        self.obj:SetActive(true)
    end
end

-- 选择buff
function ExperienceFBSceneView:SelectBuffIndex(render)
	self.node_list.select_buff_root:CustomSetActive(false)
	if render == nil or render.data == nil then
		return
	end

	local index = render:GetIndex()
	local server_index = index - 1
	ExperienceFbWgCtrl.Instance:RequestExpWestChooseCard(render.data.wave, server_index)
end

function ExperienceFBSceneView:OnFlush(param_t, index)
    for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushWaveMessage()
			self:FlushSkillMessage()
			self:FlushCardMessage()
		elseif k == "flush_skill" then
			self:FlushSkillMessage()
		elseif k == "flush_wave" then
			self:FlushWaveMessage()
			self:FlushCardMessage()
		elseif k == "flush_card" then
			self:FlushCardMessage()
        end
    end
end

function ExperienceFBSceneView:FlushWaveMessage()
	local scene_info = ExperienceFbWGData.Instance:GetExpWestSceneInfo()
	if not scene_info then
		return
	end

	local wave = scene_info.wave or 0
	local wave_end_time = scene_info.wave_end_time or 0
	local level = scene_info.level or 0
	local monster_count = scene_info.monster_count or 0


	local now_wave = wave
	local cfg = ExperienceFbWGData.Instance:GetLevelWaveCfgByLevelWave(level, now_wave)

	if not cfg then
		return
	end

	local wave_monster_num = cfg.monster_num or 0
	local is_boss = cfg.is_boss or -1
	self.item_list:SetDataList(cfg.pass_reward_item)
	local now_wave_str = string.format(Language.OfflineRest.ExperienceFbWaveTips, now_wave)
	local wave_list = ExperienceFbWGData.Instance:GetWaveListByLevel(level)
	self.node_list.wave_text.text.text = now_wave_str
	self.node_list.last_monster_text.text.text = string.format("%d/%d", monster_count, wave_monster_num)
	self.node_list.exp_west_times_wave.text.text = string.format(Language.OfflineRest.ExperienceFbWaveTips2, now_wave, #wave_list)

	if self.wave_end_time ~= nil and self.wave_end_time ~= wave_end_time then
		local add_time = wave_end_time - self.wave_end_time
		if add_time > 0 then
			local add_str = TimeUtil.FormatSecond2MS(add_time)
			self.node_list.exp_west_times_tips.canvas_group.alpha = 1
			self.node_list.exp_west_times_tips:CustomSetActive(true)
			self.node_list.exp_west_times_tips.text.text = string.format("+%s", add_str)

			if self.alpha_tween then
				self.alpha_tween:ResetToBeginning()
			end
		end
	end

	self:FlushTimeCountDown(wave_end_time)
	self.wave_end_time = wave_end_time

	if is_boss ~= -1 and wave ~= 1 then
		if is_boss == 1 then
			self.node_list.boss_start.canvas_group.alpha = 1
			self.node_list.boss_start:CustomSetActive(true)

			if self.boss_start_alpha_tween then
				self.boss_start_alpha_tween:ResetToBeginning()
			end
		else
			-- self.node_list.fight_start.canvas_group.alpha = 1
			-- self.node_list.fight_start:CustomSetActive(true)

			-- if self.fight_start_alpha_tween then
			-- 	self.fight_start_alpha_tween:ResetToBeginning()
			-- end
			TipWGCtrl.Instance:ShowCommonFightStarTip()
		end
	end
end

-- 刷新进度和能量
function ExperienceFBSceneView:FlushSkillMessage()
	local scene_info = ExperienceFbWGData.Instance:GetExpWestSceneInfo()
	if not scene_info then
		return
	end

	local level = scene_info.level or 0
	local lv_cfg = ExperienceFbWGData.Instance:GetLevelCfgByLevel(level)
	if not lv_cfg then
		return
	end

	local need_skill_power = lv_cfg.need_skill_power or 1
	local skill_power = ExperienceFbWGData.Instance:GetExpWestSkillPower()
	local power_slider_value = skill_power / need_skill_power
	self.node_list.exp_west_skill_slider.slider.value = power_slider_value
	self.node_list.skill_progress.text.text = string.format("%s/%s", skill_power, need_skill_power)
	local skill_id = lv_cfg.skill_id or 0
	local icon_id = SkillWGData.Instance:GetSkillIconId(skill_id)
	local bundle, name = ResPath.GetSkillIconById(icon_id)
	self.node_list.exp_west_skill.image:LoadSprite(bundle, name)
end

-- 刷新卡组
function ExperienceFBSceneView:FlushCardMessage()
	local scene_info = ExperienceFbWGData.Instance:GetExpWestSceneInfo()
	local card_list = ExperienceFbWGData.Instance:GetAllCardInfo()

	if (not scene_info) or (not card_list) then
		return
	end

	local wave = scene_info.wave or 0
	local level = scene_info.level or 0
	if wave <= 1 then	--第二波的选取的属于第一波通关的数据，第一关没有卡牌选取
		return
	end

	local lv_cfg = ExperienceFbWGData.Instance:GetLevelCfgByLevel(level)
	if not lv_cfg then
		return
	end

	local now_wave = wave - 1	-- 位上一波选取卡牌buff
	local card_data = nil
	local card_wave = 0
	for i = 1, now_wave do
		if card_list[i] and card_list[i].choose_seq == -1 then
			card_data = card_list[i]
			card_wave = i
			break
		end
	end

	if card_data ~= nil then	-- 组装三个数据
		self.node_list.select_buff_root:CustomSetActive(true)
		for i, v in ipairs(card_data.random_seq_list) do
			if self.select_buff_list[i] then
				local data = {}
				data.wave = card_wave
				data.card_pool = lv_cfg.card_pool or 0
				data.choose_seq = v.seq
				data.choose_level = v.level
				self.select_buff_list[i]:SetData(data)
			end
		end
	end

end

-----------------波次倒计时-------------------
function ExperienceFBSceneView:CleanTimeDown()
	if CountDownManager.Instance:HasCountDown("experience_fb_scene_down") then
		CountDownManager.Instance:RemoveCountDown("experience_fb_scene_down")
	end
end

function ExperienceFBSceneView:FlushTimeCountDown(end_time)
	local invalid_time = end_time
	if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
		self:CleanTimeDown()
		self:UpdateExpWestDesc(invalid_time - TimeWGCtrl.Instance:GetServerTime())
		CountDownManager.Instance:AddCountDown("experience_fb_scene_down", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
	end
end

function ExperienceFBSceneView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self:UpdateExpWestDesc(valid_time)
	end
end

function ExperienceFBSceneView:OnComplete()
	self:CleanTimeDown()
	--self:UpdateExpWestDesc(0)
end

function ExperienceFBSceneView:UpdateExpWestDesc(time)
	if self.node_list and self.node_list.exp_west_desc then
		local time_str = ""
		if time > 0 then
			time_str = TimeUtil.FormatSecondDHM4(time)
		end 

		self.node_list.exp_west_times.text.text = time_str
	end

	if self.node_list and self.node_list.all_use_time_text then
		self.all_use_time = self.all_use_time or 0
		self.all_use_time = self.all_use_time + 0.75	--(服务器和当前时间村子误差)
		local all_time_str = TimeUtil.FormatSecond2MS(self.all_use_time)
		self.node_list.all_use_time_text.text.text = all_time_str
	end
end

-------------------------------------------------------------------------------
-- 使用技能
function ExperienceFBSceneView:OnClickUsePoserSkill()
	local scene_info = ExperienceFbWGData.Instance:GetExpWestSceneInfo()
	if not scene_info then
		return
	end

	local level = scene_info.level or 0
	local lv_cfg = ExperienceFbWGData.Instance:GetLevelCfgByLevel(level)
	if not lv_cfg then
		return
	end

	local need_skill_power = lv_cfg.need_skill_power or 1
	local skill_power = ExperienceFbWGData.Instance:GetExpWestSkillPower()
	if skill_power >= need_skill_power then
		ExperienceFbWgCtrl.Instance:RequestExpWestUseSkill()
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OfflineRest.ExperienceFbUseSkillError)
	end
end

-- 所有加成属性
function ExperienceFBSceneView:OnClickAttrMessage()
	ExperienceFbWgCtrl.Instance:OpenExperienceFbCardTipsView()
end

-------------------------------------PWSceneHurtItemCellRender---------------------------------
ExperienceFBSelectBuff = ExperienceFBSelectBuff or BaseClass(BaseRender)

function ExperienceFBSelectBuff:OnFlush()
    if IsEmptyTable(self.data) then
		return
	end

	local cfg = ExperienceFbWGData.Instance:GetCardCfgByIndexSeq(self.data.card_pool, self.data.choose_seq, self.data.choose_level)

	if not cfg then
		return
	end	

	-- local str = string.format("%s\n", cfg.name)
	-- local desc = ""
	-- if cfg.type == WAVE_BUFF.ADD_WAVE_TIME then
	-- 	desc = string.format(cfg.desc, cfg.param1)
	-- elseif cfg.type == WAVE_BUFF.ADD_BUFF then
	-- 	desc = cfg.desc
	-- elseif cfg.type == WAVE_BUFF.ADD_SKILL_HURT then
	-- 	desc = string.format(cfg.desc, math.floor(cfg.param1 / 100))
	-- elseif cfg.type == WAVE_BUFF.ADD_SKILL_POWER then
	-- 	desc = string.format(cfg.desc, cfg.param1)
	-- end

	self.node_list.buff_name.text.text = cfg.name
	self.node_list.buff_desc.text.text = cfg.desc
	local sprite_str = string.format("a3_ll_card_icon_0%d", cfg.icon)
	self.node_list.buff_icon.image:LoadSprite(ResPath.GetOfflinerestImg(sprite_str))
	self.node_list.buff_rawimage.raw_image:LoadSprite(ResPath.GetRawImagesPNG(string.format("a3_zjm_buff_di_%d", cfg.color or 1)))
end