XunBaoBag = XunBaoBag or BaseClass(SafeBaseView)

function XunBaoBag:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
    self:AddViewResource(0, "uis/view/zhuangbeixunbao_ui_prefab", "layout_xunbao_bag")
	self:SetMaskBg()
end

function XunBaoBag:ReleaseCallBack()
    if self.bag_list then
        self.bag_list:DeleteMe()
        self.bag_list = nil
    end
end

function XunBaoBag:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.Xunbao.BagTitle
    self:SetSecondView(nil, self.node_list["size"])

    self.node_list["Button"].button:AddClickListener(BindTool.Bind(self.OnClickGet, self))

    self.bag_list = AsyncBaseGrid.New()
    local t = {}
    t.cell_count = 200
    t.col = 8
    t.list_view = self.node_list["bag_list"]
    self.bag_list:Create<PERSON><PERSON>s(t)
    self.bag_list:SetStartZeroIndex(false)
end

function XunBaoBag:OnFlush()
    local bag_list = NewXunbaoWGData.Instance:GetTBLongBagList()
    self.bag_list:SetDataList(bag_list, 2)
end

function XunBaoBag:ShowIndexCallBack()
    self:Flush()
end

function XunBaoBag:OnClickGet()
    NewXunbaoWGCtrl.Instance:SendTaoBaoOp(TAOBAO_OP_TYPE.PUTTOPACK, -1)
end

--function XunBaoBag:OnClickGetByIndex(index)
--    NewXunbaoWGCtrl.Instance:SendTaoBaoOp(TAOBAO_OP_TYPE.PUTTOPACK, index)
--end
