SunProbabilityView = SunProbabilityView or BaseClass(SafeBaseView)

function SunProbabilityView:__init()
    self.view_layer = UiLayer.Normal
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(600, 420)})
    self:AddViewResource(0, "uis/view/sun_rainbow_ui_prefab", "layout_grch_probability")
    self:SetMaskBg(true, true)
end

function SunProbabilityView:__delete()

end

function SunProbabilityView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.SunRainbow.ProbabilityTitle
     if not self.probability_list then
        self.probability_list = AsyncListView.New(SunProItemRender, self.node_list.ph_pro_list) 
    end
end

function SunProbabilityView:ReleaseCallBack()
    if self.probability_list then
        self.probability_list:DeleteMe()
        self.probability_list = nil
    end
end

function SunProbabilityView:OnFlush()
    local info = SunRainbowWgData.Instance:GetProbabilityInfo()
    if not IsEmptyTable(info) then
        self.probability_list:SetDataList(info)
    end
end

-----------------------SunProItemRender-----------------------
SunProItemRender = SunProItemRender or BaseClass(BaseRender)
function SunProItemRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list.index_text.text.text = self.data.number
    local item_name = ItemWGData.Instance:GetItemName(self.data.item_id)
    local color = ItemWGData.Instance:GetItemColor(self.data.item_id)
    self.node_list.name_text.text.text = ToColorStr(item_name, color) 
    self.node_list.probability_text.text.text = self.data.random_count .. "%"
end
