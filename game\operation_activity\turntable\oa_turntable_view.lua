local OA_TURNTABLE_LAYER_MAX = 3
local OA_TURNTABLE_RANK_MAX = 3
local OA_TURNTABLE_COUNTDOWN_STR = "oa_turntable"

local TurnTablePageViewCellWidth = 212
local TurnTablePageViewCellSpace = 0
local TurnTablePageViewHeight = 440

local show_reward_progress = {
	NoStart = 1,
	InHand = 2,
	End = 3,
}

function OperationActivityView:LoadTurnTableView()
	self.turntable_once_draw = {}

	self.page_item_list = {}
	self.active_item_cell = {}
	self.turntable_simulate_anim_callback = nil
	self.turntable_next_round = false
	self.turntable_next_pool = false
	self.is_luckdraw_now = false

	self.need_check_pageview_anim_state = false
	self.show_reward_progress = show_reward_progress.NoStart

	if not self.oa_turntable_reward_list then
		self.oa_turntable_reward_list = AsyncListView.New(OATurnTableReward<PERSON><PERSON><PERSON>, self.node_list.reward_list)
	end
	if not self.oa_turntable_card_list then
		self.oa_turntable_card_list = AsyncFancyAnimView.New(OATurnTablePageViewCell<PERSON>enderer, self.node_list.card_list2)
	end

    self.node_list["turntable_btn_draw"].button:AddClickListener(BindTool.Bind(self.OnClickTurnTableBtnDraw, self))
    self.node_list["turntable_anim_check"].button:AddClickListener(BindTool.Bind(self.OnClickTurnTableBtnCheck, self))
    self.node_list["turntable_btn_record"].button:AddClickListener(BindTool.Bind(self.OnClickTurnTableBtnRecoed, self))
    self.node_list["turntable_once_check"].button:AddClickListener(BindTool.Bind(self.OnClickTurnTableBtnOnceAll, self))
    -- self.node_list["turntable_btn_reset"].button:AddClickListener(BindTool.Bind(self.OnClickTurnTableBtnReset, self))  --重置功能
    self.node_list["turntable_cost_img"].button:AddClickListener(BindTool.Bind(self.OnClickTurnTableLayerItem, self))
    self.node_list["turntable_btn_tip"].button:AddClickListener(BindTool.Bind(self.OnClickTurntableBtnTip, self))
	self.node_list["turntable_btn_reward"].button:AddClickListener(BindTool.Bind(self.OnClickTurntableRewardBtn, self, true))
	self.node_list["reward_close_btn"].button:AddClickListener(BindTool.Bind(self.OnClickTurntableRewardBtn, self, false))
    OATurnTableWGData.Instance:ChangeTurnTableLayerBtnState(false)
    self:ResetTurnTableOnceState()

    self:FlushTurnTableAnimCheck()
	self:FlushTurnTableRecordNum()

	self.node_list.reward_list_title.text.text = Language.OATurnTable.Reward_Title
end

function OperationActivityView:OnClickTurntableBtnTip()
	if not self.cur_turntable_layer then
		return
	end
    local cfg = OATurnTableWGData.Instance:GetLayerCfgByLayerNum(self.cur_turntable_layer)
	if not cfg then
		return
    end
    local desc = cfg.tips
    local probility_str = OATurnTableWGData.Instance:GetItemsProbility(self.cur_turntable_layer)
    desc = desc .. probility_str
	RuleTip.Instance:SetContent(desc,cfg.name)
end

function OperationActivityView:CloseTurnTableView()
	self.need_check_pageview_anim_state = true
end

function OperationActivityView:ReleaseTurnTableView()
	self:CleanAnimTimer()
    if CountDownManager.Instance:HasCountDown(OA_TURNTABLE_COUNTDOWN_STR) then
        CountDownManager.Instance:RemoveCountDown(OA_TURNTABLE_COUNTDOWN_STR)
    end

	if self.turntable_click_draw_timequest then
		GlobalTimerQuest:CancelQuest(self.turntable_click_draw_timequest)
		self.turntable_click_draw_timequest = nil
	end

	if self.page_item_list then
		for k,v in pairs(self.page_item_list) do
			v:DeleteMe()
		end
		self.page_item_list = nil
	end

	if self.turntable_alert then
		self.turntable_alert:DeleteMe()
		self.turntable_alert = nil
	end

	if self.oa_turntable_reward_list then
		self.oa_turntable_reward_list:DeleteMe()
		self.oa_turntable_reward_list = nil
	end

	if self.oa_turntable_card_list then
		self.oa_turntable_card_list:DeleteMe()
		self.oa_turntable_card_list = nil
	end
    self.onclick_turntable_draw = nil
    self.cur_turntable_layer = nil
    self.turntable_is_animing = nil
	self.is_luckdraw_now = nil

	self.turntable_win_vector2 = nil
	self.turntable_cost_img_cache = nil
	self.cache_yaogan_list = nil
	self.cache_yaogan_nv_list = nil
	self.turntable_simulate_anim_callback = nil
	self.turntable_once_draw = nil
	self.need_check_pageview_anim_state = nil
	self.is_show_reward = nil
end

local shield_pos = Vector2(10000,10000)
function OperationActivityView:ShowIndexTurnTable(index)
	if index == TabIndex.operation_act_turntable then
		if self.need_check_pageview_anim_state then
			self:CheckPageViewTurnTableAnim()
		end
		self:SetRuleInfoActive(false)
        self:SetRemainTimePos(shield_pos)
		OATurnTableWGCtrl.Instance:SendOpera(RA_TUNVLANG_OPERA_TYPE.RECORD)
		self:OpenCallBackTurnTable()
		self:CheckNeedResetTurnTable()
	end
end

function OperationActivityView:CheckPageViewTurnTableAnim()
	self.need_check_pageview_anim_state = false
	if self.show_reward_progress == show_reward_progress.InHand then
		self:SetCardListScrolState(true)
		self.is_luckdraw_now = false
		self:ChangeTurnTableLayerBtnState(false)
	end
end

-- 是否需要请求重置
function OperationActivityView:CheckNeedResetTurnTable()
	local layer_list = OATurnTableWGData.Instance:GetCurActLayerList()
	for i = OA_TURNTABLE_LAYER_MAX, 1, -1 do
		if layer_list[i] and OATurnTableWGData.Instance:CheckCanResetByLayer(layer_list[i]) then
			OATurnTableWGCtrl.Instance:SendOpera(RA_TUNVLANG_OPERA_TYPE.RESET_LAYER, layer_list[i])
		end
	end
end

function OperationActivityView:ResetTurnTableOnceState()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPERA_ACT_TURNTABLE)
	local open_time = activity_info.start_time
	local cache_start_time = RoleWGData.GetRolePlayerPrefsInt("TurnTableEndTime")
	if open_time ~= cache_start_time then
		RoleWGData.SetRolePlayerPrefsInt("TurnTableEndTime",open_time)

		local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
		for i=1,OA_TURNTABLE_LAYER_MAX do
			PlayerPrefsUtil.SetInt(OA_TURNTABLE_COUNTDOWN_STR .. "_ONCE" .. role_id .. i, 0)
		end 
		
		PlayerPrefsUtil.SetInt(OA_TURNTABLE_COUNTDOWN_STR .. "_ANIM" .. role_id, 0)
	end
end

function OperationActivityView:OpenCallBackTurnTable()
	local layer_list = OATurnTableWGData.Instance:GetCurActLayerList()
    -- 設置默认选中
    local select_index = 1
	for i = OA_TURNTABLE_LAYER_MAX, 1, -1 do
		if layer_list[i] then
			local is_show_red = OATurnTableWGData.Instance:IsShowRedByLayer(layer_list[i])
			if is_show_red then
				select_index = i
				break
			end
		end
	end

	if layer_list[select_index] then
		self:OnClickTurnTableLayer(select_index, layer_list[select_index])
	end
end

--刷新动画显示状态
function OperationActivityView:FlushTurnTableAnimCheck()
	local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid
	local img_yes_state = PlayerPrefsUtil.GetInt(OA_TURNTABLE_COUNTDOWN_STR .. "_ANIM" .. role_id) == 1
	self.node_list["turntable_anim_yes"]:SetActive(img_yes_state)
	self.turntable_ignore_anim = img_yes_state
end

--刷新一键抽奖显示状态
function OperationActivityView:FlushTurnTableOnceCheck()
	if not self.cur_turntable_layer then
		return
	end

	local cur_layer_index = self:GetTurnTableLayerIndex()
    local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid
	local img_yes_state = PlayerPrefsUtil.GetInt(OA_TURNTABLE_COUNTDOWN_STR .. "_ONCE" .. role_id .. cur_layer_index) == 1
	self.node_list["turntable_once_yes"]:SetActive(img_yes_state)

	for i=1,OA_TURNTABLE_LAYER_MAX do
		self.turntable_once_draw[i] = PlayerPrefsUtil.GetInt(OA_TURNTABLE_COUNTDOWN_STR .. "_ONCE" .. role_id .. i) == 1
	end 
end

--抽奖
function OperationActivityView:OnClickTurnTableBtnDraw()
    if self.onclick_turntable_draw or self.turntable_is_animing then
		TipWGCtrl.Instance:ShowSystemMsg(Language.OATurnTable.IsWorkIngNowPleaseWait)
		return
	end

    local is_empty = OATurnTableWGData.Instance:GetTurnTableIsDrawEmptyByLayer(self.cur_turntable_layer)
    if is_empty then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OATurnTable.DrawOut)
    else
		self.is_luckdraw_now = true
    	OATurnTableWGData.Instance:ChangeTurnTableLayerBtnState(not self.turntable_ignore_anim)
		local cur_layer_index = self:GetTurnTableLayerIndex()
		if self.turntable_once_draw[cur_layer_index] then
			self:OnClickTurnTableBtnOnceDraw()
		else
			self:GetTurnTableDrawInfo()
		end
    end
end

-- 点击一键抽奖
function OperationActivityView:OnClickTurnTableBtnOnceDraw()
	local cur_draw_info = OATurnTableWGData.Instance:GetAllDrawInfo(self.cur_turntable_layer)
	if cur_draw_info == nil or cur_draw_info.draw_consume_item_count == nil then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OATurnTable.DrawOut)
		self.onclick_turntable_draw = false
		return
	end
	local num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_info.draw_consume_item_id)
	local dif_num = cur_draw_info.draw_consume_item_count - num
	if dif_num > 0 then
		local layer_cfg = OATurnTableWGData.Instance:GetLayerCfgByLayerNum(self.cur_turntable_layer)
		
		if layer_cfg.conmsum_xianyu > 0 then
			local item_cfg = ItemWGData.Instance:GetItemConfig(layer_cfg.draw_consume_item_id)
			local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			local str = string.format(Language.OATurnTable.ItemNotEnough, dif_num, item_name, layer_cfg.conmsum_xianyu*dif_num)
			self:GetTurnTableOneKeyAlert()
			self.turntable_alert:SetLableString(str)
			self.turntable_alert:Open()
		else
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cur_draw_info.draw_consume_item_id})
		end
	else
		self:SendTurnTableDrawOneKey()
	end
end

function OperationActivityView:GetTurnTableDrawInfo()
	local cur_draw_info = OATurnTableWGData.Instance:GetTurnTableCurDrawInfoByLayer(self.cur_turntable_layer)
	if cur_draw_info == nil or cur_draw_info.draw_consume_item_count == nil then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OATurnTable.DrawOut)
		self.onclick_turntable_draw = false
		return
	end
	local num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_info.draw_consume_item_id)
	local dif_num = cur_draw_info.draw_consume_item_count - num
	if dif_num > 0 then
		local layer_cfg = OATurnTableWGData.Instance:GetLayerCfgByLayerNum(self.cur_turntable_layer)
		if layer_cfg.conmsum_xianyu > 0 then
			local item_cfg = ItemWGData.Instance:GetItemConfig(layer_cfg.draw_consume_item_id)
			local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			local str = string.format(Language.OATurnTable.ItemNotEnough, dif_num, item_name, layer_cfg.conmsum_xianyu*dif_num)
			self:GetTurnTableAlert()
			self.turntable_alert:SetLableString(str)
			self.turntable_alert:Open()
		else
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cur_draw_info.draw_consume_item_id})
		end
	else
		self:SendTurnTableDraw()
	end
end

function OperationActivityView:SendTurnTableDraw()
	local cur_draw_info = OATurnTableWGData.Instance:GetTurnTableCurDrawInfoByLayer(self.cur_turntable_layer)
	if cur_draw_info == nil or cur_draw_info.draw_consume_item_count == nil then
		return
	end
	local num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_info.draw_consume_item_id)
	local dif_num = cur_draw_info.draw_consume_item_count - num
	local need_buy = dif_num > 0 and 1 or 0

	self.onclick_turntable_draw = true
	OATurnTableWGCtrl.Instance:SendOpera(RA_TUNVLANG_OPERA_TYPE.DRAW, self.cur_turntable_layer, need_buy)

	if self.turntable_click_draw_timequest then
		GlobalTimerQuest:CancelQuest(self.turntable_click_draw_timequest)
		self.turntable_click_draw_timequest = nil
	end

	self.turntable_click_draw_timequest = GlobalTimerQuest:AddDelayTimer(function()
		self.onclick_turntable_draw = false
	end, 15)
end

-- 发送一键抽奖请求
function OperationActivityView:SendTurnTableDrawOneKey()
	-- 忽略动画
	if self.turntable_ignore_anim then
		self:SendTurnTableDrawOneKeyLogic()
	else
		self.turntable_simulate_anim_callback = BindTool.Bind(self.SendTurnTableDrawOneKeyLogic, self)
		self:StartTurnTableAnim()
	end
end

function OperationActivityView:SendTurnTableDrawOneKeyLogic()
	local cur_draw_info = OATurnTableWGData.Instance:GetAllDrawInfo(self.cur_turntable_layer)
	if cur_draw_info == nil or cur_draw_info.draw_consume_item_count == nil then
		return
	end
	local num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_info.draw_consume_item_id)
	local dif_num = cur_draw_info.draw_consume_item_count - num
	local need_buy = dif_num > 0 and 1 or 0

	self.onclick_turntable_draw = true
	OATurnTableWGCtrl.Instance:SendOpera(RA_TUNVLANG_OPERA_TYPE.DRAW_ONEKEY, self.cur_turntable_layer, need_buy)

	if self.turntable_click_draw_timequest then
		GlobalTimerQuest:CancelQuest(self.turntable_click_draw_timequest)
		self.turntable_click_draw_timequest = nil
	end

	self.turntable_click_draw_timequest = GlobalTimerQuest:AddDelayTimer(function()
		self.onclick_turntable_draw = false
	end, 15)
end

function OperationActivityView:GetTurnTableAlert()
	if not self.turntable_alert then
		self.turntable_alert = Alert.New()
		self.turntable_alert:SetShowCheckBox(true, OA_TURNTABLE_COUNTDOWN_STR)
		self.turntable_alert:SetCheckBoxText(Language.OATurnTable.AlertTip)
	end
	self.turntable_alert:SetOkFunc(BindTool.Bind(self.SendTurnTableDraw, self))
end

function OperationActivityView:GetTurnTableOneKeyAlert()
	if not self.turntable_alert then
		self.turntable_alert = Alert.New()
		self.turntable_alert:SetShowCheckBox(true, OA_TURNTABLE_COUNTDOWN_STR .. "onekey")
		self.turntable_alert:SetCheckBoxText(Language.OATurnTable.AlertTip)
	end
	self.turntable_alert:SetOkFunc(BindTool.Bind(self.SendTurnTableDrawOneKey, self))
end

--忽略动画
function OperationActivityView:OnClickTurnTableBtnCheck()
    self.turntable_ignore_anim = not self.turntable_ignore_anim
	self.node_list["turntable_anim_yes"]:SetActive(self.turntable_ignore_anim)
	local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	PlayerPrefsUtil.SetInt(OA_TURNTABLE_COUNTDOWN_STR .. "_ANIM" .. role_id, self.turntable_ignore_anim and 1 or 0)
end

--记录
function OperationActivityView:OnClickTurnTableBtnRecoed()
	-- OATurnTableWGCtrl.Instance:SendOpera(RA_TUNVLANG_OPERA_TYPE.RECORD)
    -- local data_list = OATurnTableWGData.Instance:GetRecordInfo()
    -- TipWGCtrl.Instance:OpenTipsRewardRecordView(data_list)
    -- OATurnTableWGData.Instance:ClearRecordCount()

    OATurnTableWGCtrl.Instance:OpenRecordView()
	OATurnTableWGData.Instance:ClearRecordCount()
	self:FlushTurnTableRecordNum()
end

--重置
function OperationActivityView:OnClickTurnTableBtnReset()
    if self.onclick_turntable_draw or self.turntable_is_animing then
		return
	end

	if nil == self.cur_turntable_layer then
		return
	end

	OATurnTableWGCtrl.Instance:SendOpera(RA_TUNVLANG_OPERA_TYPE.RESET_LAYER, self.cur_turntable_layer)

end

function OperationActivityView:GetTurnTableLayerIndex()
	local layer_list = OATurnTableWGData.Instance:GetCurActLayerList()
	local cur_layer_index = 1
	for i=1,OA_TURNTABLE_LAYER_MAX do
		if layer_list[i] == self.cur_turntable_layer then
			cur_layer_index = i
		end
	end

	return cur_layer_index
end

--一键抽奖
function OperationActivityView:OnClickTurnTableBtnOnceAll()
	local cur_layer_index = self:GetTurnTableLayerIndex()

    self.turntable_once_draw[cur_layer_index] = not self.turntable_once_draw[cur_layer_index]
	self.node_list["turntable_once_yes"]:SetActive(self.turntable_once_draw[cur_layer_index])
	local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	PlayerPrefsUtil.SetInt(OA_TURNTABLE_COUNTDOWN_STR .. "_ONCE" .. role_id .. cur_layer_index, self.turntable_once_draw[cur_layer_index] and 1 or 0)
	self:ChangeTurnTableButtonShow()
	self:ChangeTurnTableCostInfo()
end

--抽奖动画开始
function OperationActivityView:FlushTurnTableView(info)
	for i, v in pairs(info) do
		if v == "normal" then
			self:FlushTurnTableNormalShow()
			if self.cur_turntable_layer then
				self:OnClickTurnTableLayer(nil, self.cur_turntable_layer)
			end
		elseif v == "hot_update" then
			--self:FlushTurnTableBtnLayerInfo()
			self:FlushTurnTableNormalShow()
			if self.cur_turntable_layer then
				self:OnClickTurnTableLayer(nil, self.cur_turntable_layer)
			end
		elseif v == "record" then
			self:FlushTurnTableRecordNum()
		elseif v == "result" then
			self.onclick_turntable_draw = false
			self:StartTurnTableAnim()
		elseif v == "draw_all" then
			if self.cur_turntable_layer then
				self:OnClickTurnTableLayer(nil, self.cur_turntable_layer)
			end
		elseif v == "layer" then
			if self.cur_turntable_layer == info.layer then
				self:OnClickTurnTableLayer(nil, self.cur_turntable_layer)
			end
		end
	end
end

function OperationActivityView:FlushTurnTableNormalShow()
	self:ChangeTurnTableResetBtn()
end


function OperationActivityView:StartTurnTableAnim()
	--剩一张卡直接给
	if self.turntable_ignore_anim or #self.active_item_cell <= 1 then
		local reward_info = OATurnTableWGData.Instance:GetTurnTableCurDrawResultByLayer(self.cur_turntable_layer)
		self:FlushTurnTableAnimEnd(reward_info,function ()
			self.is_luckdraw_now = false
			self:FlushPageView()
		end)
	else
		self:ChangeTurnTableLayerBtnState(true)
		self:PrepareAnimTime()
		self:SetCardListScrolState(false)
	end
	self:ChangeTurnTableResetBtn()
end

--动画结束刷新对应面板
function OperationActivityView:FlushTurnTableAnimEnd(reward_info, close_func)
	self.show_reward_progress = show_reward_progress.End
	local is_empty = OATurnTableWGData.Instance:GetTurnTableIsDrawEmptyByLayer(self.cur_turntable_layer)
	--打开奖励界面（通用奖励获取？）
	self:ShowTurnTableGetReward(reward_info, close_func)
	if is_empty then
		self:ChangeTurnTablePoolTime()
	end
	self:ChangeTurnTableCostInfo()
	self:ChangeTurnTableButtonShow()
end

--动画期间不能切换层数
function OperationActivityView:ChangeTurnTableLayerBtnState(status)
	self.turntable_is_animing = status
	OATurnTableWGData.Instance:ChangeTurnTableLayerBtnState(self.turntable_is_animing)
end

--动画准备和开始
function OperationActivityView:PrepareAnimTime()
	local reward_info = OATurnTableWGData.Instance:GetTurnTableCurDrawResultByLayer(self.cur_turntable_layer)
	if not reward_info then
		return
	end

	self.node_list.card_list2:SetActive(false)
	self.node_list.card_list:SetActive(true)

	self.show_reward_progress = show_reward_progress.NoStart
	local in_center = #self.active_item_cell % 2 ~= 0
	local init_content_tween

	if in_center then
		init_content_tween= self.node_list.card_list_content.transform:DOAnchorPos(Vector2(0,0),0.2)
	else
		init_content_tween = self.node_list.card_list_content.transform:DOAnchorPos(Vector2((TurnTablePageViewCellSpace + TurnTablePageViewCellWidth)/2,0),0.2)
	end

	local center_render = self.active_item_cell[1]
	ReDelayCall(self, function()
		local data = {}
		local page_info = OATurnTableWGData.Instance:GetOATurnTablePageCellInfo(center_render:GetIndex())
		data.reward_info = reward_info
		data.page_info = page_info
		center_render:SetData(data)
		center_render:Flush()
	end, 1, "oa_turntable_setdata")

	local tween_callback = function ()
		self.show_reward_progress = show_reward_progress.InHand
		self:CheckShowTurnTableEndAnim()
	end

	init_content_tween:OnComplete(function ()
		for k, v in pairs(self.active_item_cell) do
			v:CoverCard(center_render,tween_callback)
		end
	end)

end

--奖励滑落
function OperationActivityView:CheckShowTurnTableEndAnim()
	if self.turntable_simulate_anim_callback then
		local fun = self.turntable_simulate_anim_callback
		self.turntable_simulate_anim_callback = nil
		fun()
		self.node_list.card_list2:SetActive(true)
		self.node_list.card_list:SetActive(false)
		return
	end
	local reward_info = OATurnTableWGData.Instance:GetTurnTableCurDrawResultByLayer(self.cur_turntable_layer)
	local time1 = 2.0
	ReDelayCall(self, function()
		self:FlushTurnTableAnimEnd(reward_info, function ()
			self:SetCardListScrolState(true)
			self.is_luckdraw_now = false
			self:FlushPageView()
			self:ChangeTurnTableLayerBtnState(false)
		end)
	end, time1, "oa_turntable_tween_end")
end

function OperationActivityView:ShowTurnTableGetReward(reward_info, close_func)
	if reward_info then
		TipWGCtrl.Instance:ShowGetReward(nil, {reward_info.reward_item}, false , nil, nil, close_func)
		if close_func then
			close_func()
		end
	else
		if close_func then
			close_func()
		end
	end
end

--点击层数切换
function OperationActivityView:OnClickTurnTableLayer(layer, layer_num)
    self.cur_turntable_layer = layer_num
    local cfg = OATurnTableWGData.Instance:GetLayerCfgByLayerNum(layer_num)
    self:ChangeTurnTablePoolTime()
	self:ChangeTurnTableResetBtn()
	self:ChangeTurnTableOnceShow(cfg)
	self:ChangeTurnTableCostInfo()
	self:ChangeTurnTableButtonShow()
	self:FlushTurnTableOnceCheck()

	self:FlushPageView()
end

-- 刷新遥感显隐
function OperationActivityView:ChangeYaoGanState()
	local layer_list = OATurnTableWGData.Instance:GetCurActLayerList()
	local layer_cfg = OATurnTableWGData.Instance:GetLayerCfgByLayerNum(self.cur_turntable_layer)
	local yaogan = layer_cfg.yaogan
	local renwu = layer_cfg.renwu
	if not yaogan or not renwu then
		return
	end

	for i=1,3 do
		self.node_list['layer_gan_' .. i]:SetActive(('layer_gan_' .. i) == yaogan)
	end

	for i=1,1 do
		self.node_list['layer_renwu_' .. i]:SetActive(('layer_renwu_' .. i) == renwu)
	end
end

--刷新奖池展示时间
function OperationActivityView:ChangeTurnTablePoolTime()
    local next_time = OATurnTableWGData.Instance:GetTurnTablePoolTimeByLayer(self.cur_turntable_layer)
    local is_empty = OATurnTableWGData.Instance:GetTurnTableIsDrawEmptyByLayer(self.cur_turntable_layer)
    self:ChangeTurnTableButtonState(is_empty)

    self.turntable_next_round = OATurnTableWGData.Instance:CheckHasNextRound(self.cur_turntable_layer)
    self.turntable_next_pool = OATurnTableWGData.Instance:CheckHasNextPool(self.cur_turntable_layer)

    if is_empty then
		self.node_list.turntable_act_time1.text.text = Language.OATurnTable.DrawOut
	else
		self.node_list.turntable_act_time1.text.text = string.format(Language.OATurnTable.TimeCount1, "")
    end

    local CDI = CountDownManager.Instance
    if CDI:HasCountDown(OA_TURNTABLE_COUNTDOWN_STR) then
        CDI:RemoveCountDown(OA_TURNTABLE_COUNTDOWN_STR)
    end
    
    if next_time > 0 and not is_empty then
		CDI:AddCountDown(OA_TURNTABLE_COUNTDOWN_STR, BindTool.Bind(self.UpdateTurnTablePoolTime, self),
				BindTool.Bind(self.CompleteTurnTablePoolTime, self), nil, next_time, 1)
	end
end

--刷新按钮状态
function OperationActivityView:ChangeTurnTableButtonState(is_empty)
	XUI.SetGraphicGrey(self.node_list["turntable_btn_draw"], is_empty)
end

--刷新按钮状态
function OperationActivityView:UpdateTurnTablePoolTime(elapse_time, total_time)
    local str = ""
	local temp_seconds = GameMath.Round(total_time - elapse_time)
	local remain_time = TimeUtil.FormatSecondDHM8(temp_seconds)
	-- remain_time = ToColorStr(remain_time, COLOR3B.PINK)

	if self.turntable_next_round or self.turntable_next_pool then
		str = string.format(Language.OATurnTable.TimeCount2, remain_time)
	else
		str = string.format(Language.OATurnTable.TimeCount1, remain_time)
	end

	self.node_list["turntable_act_time1"].text.text = str
end

--刷新按钮状态
function OperationActivityView:CompleteTurnTablePoolTime()
    self:OnClickTurnTableLayer(nil, self.cur_turntable_layer or 1)
	RemindManager.Instance:Fire(RemindName.OATurnTable)
end

--刷新reset按钮状态
function OperationActivityView:ChangeTurnTableResetBtn()
	local can_reset = OATurnTableWGData.Instance:CheckCanResetByLayer(self.cur_turntable_layer)
	if can_reset then
		self:OnClickTurnTableBtnReset()
	end
	self:ChangeTurnTableButtonState(can_reset)
end

--刷新一键抽奖是否显示
function OperationActivityView:ChangeTurnTableOnceShow(cfg)
	if not cfg then return end
	-- 一键抽奖需要vip等级限制
	local vip_level = VipWGData.Instance:GetRoleVipLevel()
	local is_vip = VipWGData.Instance:IsVip()
	local vip_flag = vip_level >= cfg.is_open_one_key_vip and is_vip

	self.node_list["turntable_once_show"]:SetActive(cfg.is_open_one_key_lottery == 1 and vip_flag)
	local cur_layer_index = self:GetTurnTableLayerIndex()
	if cfg.is_show_onec == 0 and self.turntable_once_draw[cur_layer_index] then
		self:OnClickTurnTableBtnOnceAll()
	end
end

--刷新消耗信息
function OperationActivityView:ChangeTurnTableCostInfo()
	local cur_draw_info
	local cur_layer_index = self:GetTurnTableLayerIndex()
	if self.turntable_once_draw[cur_layer_index] then
		cur_draw_info = OATurnTableWGData.Instance:GetAllDrawInfo(self.cur_turntable_layer)
	else
		cur_draw_info = OATurnTableWGData.Instance:GetTurnTableCurDrawInfoByLayer(self.cur_turntable_layer)
	end

	if cur_draw_info == nil then
		return
	end

	if cur_draw_info.draw_consume_item_count then
		local num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_info.draw_consume_item_id)
		local color = num >= cur_draw_info.draw_consume_item_count and COLOR3B.GREEN or COLOR3B.PINK
		self.node_list["turntable_cost_txt"].text.text = ToColorStr(num.. "/" .. cur_draw_info.draw_consume_item_count, color)
		self.node_list["turntable_draw_red"]:SetActive((num >= cur_draw_info.draw_consume_item_count) and cur_draw_info.draw_consume_item_count > 0)
		--2020/04/25 22:16 策划需求去掉钱够显示红点
		--RoleWGData.Instance:GetRoleVo().gold >= cur_draw_info.conmsum_xianyu * cur_draw_info.draw_consume_item_count
		self:ChangeTurnTableButtonState(false)
	else
		self.node_list["turntable_cost_txt"].text.text = "-/-"
		self.node_list["turntable_draw_red"]:SetActive(false)

		self:ChangeTurnTableButtonState(true)
	end
	self:LoadTurnTableCostImg(cur_draw_info.draw_consume_item_id)
	self:FlushCardNum()
end

function OperationActivityView:FlushCardNum()
	local card_num = OATurnTableWGData.Instance:GetRemainDrawTimesByLayer(self.cur_turntable_layer)
	self.node_list.turntable_reward_title.text.text = Language.OATurnTable.CardNumTitle
	local color = card_num > 0 and COLOR3B.D_GREEN or COLOR3B.PINK
	local card_str = string.format(Language.OATurnTable.CardNum, color, card_num)
	self.node_list.turntable_reward_count.text.text = card_str
end

function OperationActivityView:OnClickTurnTableLayerItem()
	if not self.turntable_cost_img_cache then
		return
	end
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.turntable_cost_img_cache})
	
end

function OperationActivityView:LoadTurnTableCostImg(item_id)
	if self.turntable_cost_img_cache ~= item_id then
		self.turntable_cost_img_cache = item_id
		local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
		if not item_cfg then
			return
		end

		local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
		self.node_list["turntable_cost_img"].image:LoadSprite(bundle, asset, function()
			self.node_list["turntable_cost_img"].image:SetNativeSize()
		end)
	end
end

--刷新button的显示
function OperationActivityView:ChangeTurnTableButtonShow()
	local cur_layer_index = self:GetTurnTableLayerIndex()

	--计算剩余抽奖次数
	local remains = OATurnTableWGData.Instance:GetRemainDrawTimesByLayer(self.cur_turntable_layer)
	remains = remains < 0 and 0 or remains

	if self.turntable_once_draw[cur_layer_index] and self.node_list["turntable_btn_draw_txt"].text then
		self.node_list["turntable_btn_draw_txt"].text.text = string.format(Language.OATurnTable.OnceBtnShow, remains)
	elseif self.node_list["turntable_btn_draw_txt"].text then
		self.node_list["turntable_btn_draw_txt"].text.text = Language.OATurnTable.DrawBtnShow
	end

	self.node_list.turntable_plate_bg:SetActive(remains <= 0)
	-- if remains <= 0 then
	-- 	local can_reset = OATurnTableWGData.Instance:CheckCanResetByLayer(self.cur_turntable_layer)
	-- 	local index 
	-- 	if can_reset then
	-- 		index = 2 --请先重置转盘
	-- 	elseif OATurnTableWGData.Instance:CheckHasNextRound(self.cur_turntable_layer) then
	-- 		index = 1 --倒计时结束后自动重置转盘
	-- 	else
	-- 		index = 3 --所有奖励已抽完
	-- 	end
	-- end
end

function OperationActivityView:FlushTurnTableRecordNum()
	local num = OATurnTableWGData.Instance:GetNewRecordCount()
	--print_error("record", num)
	if num > 0 then
		--self.node_list["turntable_btn_record"]:SetActive(true)
		self.node_list["turntable_record_text"].text.text = num
	else
		--self.node_list["turntable_btn_record"]:SetActive(false)
	end
end

function OperationActivityView:UpdateTurnTableRecord()
	local count =  OATurnTableWGData.Instance:GetNewRecordCount()
	OATurnTableWGData.Instance:SetNewRecordCount(count + 1)
	if self:GetShowIndex() == TabIndex.operation_act_turntable then
		self:FlushTurnTableRecordNum()
	end
end

--打开奖励界面
function OperationActivityView:OnClickTurntableRewardBtn(state)
	local reward_id_list = OATurnTableWGData.Instance:GetTurnTableRewardListByLayer(self.cur_turntable_layer)
	local reward_list = {}
    for i, v in ipairs(reward_id_list) do
        local state = OATurnTableWGData.Instance:GetTurnTableRewardIsShowByLayerAndIndex(self.cur_turntable_layer, i)
		local data = OATurnTableWGData.Instance:GetTurnTableRewardInfoById(v)
		data.reward_item.is_ylq = not state
		table.insert(reward_list, data.reward_item)
    end
    local data_list =
    {
        view_type = RewardShowViewType.Normal,
        reward_item_list = reward_list
    }
    RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
	-- self.item_cell:SetData(data.reward_info.reward_item)
	-- self.node_list.img_mask:SetActive(not data.state)
	-- if self.node_list.reward_close_btn:GetActive() ~= state then
	-- 	self.node_list.reward_close_btn:SetActive(state)
	-- end
end

---------------------------------------轮播图Start-----------------------------------------------
function OperationActivityView:FlushPageView()
	--在抽奖时候静止刷新
	if true == self.is_luckdraw_now then
		return
	end

	local reward_id_list = OATurnTableWGData.Instance:GetTurnTableRewardListByLayer(self.cur_turntable_layer)
    local bundle, asset = "uis/view/operation_turntable_prefab", "turntable_pageview_cell"
	local active_cell_list = {}
	local reward_cfg = {}
	local reward_data_list = {}
    for i, v in ipairs(reward_id_list) do
        local state = OATurnTableWGData.Instance:GetTurnTableRewardIsShowByLayerAndIndex(self.cur_turntable_layer, i)
        local info = {}
		local data = OATurnTableWGData.Instance:GetTurnTableRewardInfoById(v)
		local page_info = OATurnTableWGData.Instance:GetOATurnTablePageCellInfo(i)
		info.reward_info = data
		info.page_info = page_info
		info.state = state
        if not self.page_item_list[i] then
        	self.page_item_list[i] = OATurnTablePageViewCellRenderer.New()
			self.page_item_list[i]:SetIndex(i)
        	self.page_item_list[i]:LoadAsset(bundle, asset, self.node_list["card_list_content"].rect)
        end
        if state then
            self.page_item_list[i]:SetData(info)
			-- self.page_item_list[i]:Flush()
			table.insert(active_cell_list,self.page_item_list[i])
			table.insert(reward_data_list, info)
        end
		self.page_item_list[i]:InitTweenState()
        self.page_item_list[i]:SetCellState(state)
		table.insert(reward_cfg, info)
    end
	self:SetTurnTablePageViewCellPos(active_cell_list)
	self.active_item_cell = active_cell_list
	self.oa_turntable_reward_list:SetDataList(reward_cfg)

    for i = #reward_id_list+1, #self.page_item_list do
		if self.page_item_list[i] then
			self.page_item_list[i]:SetCellState(false)
		end
	end

	
	

	self:CleanAnimTimer()
	-- print_error("#reward_data_list:",#reward_data_list)
	-- 大于4个才跑马灯
	if #reward_data_list > 4 then
		local pos = 0
		self.anim_timer = CountDown.Instance:AddCountDown(999999, 0.02,
		function(elapse_time, total_time)
			-- 判断是否拖拽中，拖拽时不滑动
			if not self.oa_turntable_card_list.list_view.fancy_scroller.Dragging then
				-- 获取当前的位置，在当前位置的基础上加上偏移
				pos = self.oa_turntable_card_list.list_view.fancy_scroller.Position + 0.005
				-- 重新设置位置
				self.oa_turntable_card_list.list_view.fancy_scroller:UpdatePosition(pos)
			end
		end)
		self.node_list.card_list:SetActive(false)

		self.node_list.card_list2:SetActive(true)
		self.oa_turntable_card_list:SetDataList(reward_data_list)
	else
		self.node_list.card_list2:SetActive(false)
		self.node_list.card_list:SetActive(true)
	end


	
end

-- 清除倒计时器1
function OperationActivityView:CleanAnimTimer()
    if self.anim_timer and CountDown.Instance:HasCountDown(self.anim_timer) then
        CountDown.Instance:RemoveCountDown(self.anim_timer)
        self.anim_timer = nil
    end
end

function OperationActivityView:SetTurnTablePageViewCellPos(active_cell_list)
	local num = #active_cell_list
	local length = 0
	if num > 0 then
		if 1 == num then
			length = TurnTablePageViewCellWidth
		else
			length = TurnTablePageViewCellWidth * num + (num - 1) * TurnTablePageViewCellSpace
		end
	else
		length = 0
		return
	end
	self.node_list.card_list_content.rect.sizeDelta = Vector2(length,TurnTablePageViewHeight)

	if num % 2 == 0 then
		self.node_list.card_list_content.rect.anchoredPosition = Vector2(-(TurnTablePageViewCellSpace + TurnTablePageViewCellWidth)/2 , 0)
		for i = 1, #active_cell_list do
			local sign = 1
			if i <= 2  then
				sign = 1
			else
				if i%2 ==0 then
					sign = math.ceil((i - 1) / 2)
				else
					sign = math.ceil(i / 2)
				end
			end
			local pos_x = (TurnTablePageViewCellSpace + TurnTablePageViewCellWidth)/2 + (sign - 1) * (TurnTablePageViewCellSpace + TurnTablePageViewCellWidth)
			
			local pos_y = 0
			if i % 2 == 0 then
				active_cell_list[i]:SetPos(pos_x, pos_y)
			else
				active_cell_list[i]:SetPos(-pos_x, pos_y)
			end
		end	
	else
		self.node_list.card_list_content.rect.anchoredPosition = Vector2(0 , 0)
		for i = 1, #active_cell_list do
			local pos_x = 0
			local pos_y = 0
			if 1 == i then
				pos_x = 0
				pos_y = 0
				active_cell_list[i]:SetPos(pos_x,pos_y)
			else
				pos_x = math.floor(i / 2) * (TurnTablePageViewCellWidth + TurnTablePageViewCellSpace) 
				if i % 2 == 0 then
					active_cell_list[i]:SetPos(pos_x,pos_y)
				else
					active_cell_list[i]:SetPos(-pos_x,pos_y)
				end
			end
		end
	end
end

function OperationActivityView:SetCardListScrolState(state)
	self.node_list.card_list.scroll_rect.enabled = state
end

OATurnTablePageViewCellRenderer = OATurnTablePageViewCellRenderer or BaseClass(BaseRender)
function OATurnTablePageViewCellRenderer:__init()
	self.tween_info = {}
	self.is_in_tween = false
	self.tween_callback = {}
end

function OATurnTablePageViewCellRenderer:__delete()
	self.tween_info = nil
	self.is_in_tween = nil
	self.tween_callback = nil
	if self.reward_item then
		self.reward_item:DeleteMe()
		self.reward_item = nil
	end

	self:CancelTween()
end

function OATurnTablePageViewCellRenderer:InitTweenState()
	self.node_list.icon:SetActive(true)
	self.node_list.reward_bg:SetActive(true)
	self.node_list.page_view_name:SetActive(true)
	self.node_list.bg:SetActive(false)
	self.node_list.hightlight_bg:SetActive(false)
	self.is_in_tween = false
end

function OATurnTablePageViewCellRenderer:LoadCallBack()
	if not self.reward_item then
		self.reward_item = ItemCell.New(self.node_list.reward_item_pos)
	end
	self.node_list.icon:SetActive(true)
	self.node_list.reward_bg:SetActive(true)
	self.node_list.page_view_name:SetActive(true)
	self.node_list.bg:SetActive(false)
	self.node_list.hightlight_bg:SetActive(false)
end

function OATurnTablePageViewCellRenderer:OnFlush()
	local data = self.data
	if not data then
		return
	end
	self.reward_item:SetData(data.reward_info.reward_item)
	self.node_list.page_view_name.text.text = data.reward_info.show_name or ""
	-- if data.page_info.icon then
	-- 	local bundel, asset = ResPath.GetRawImagesPNG(data.page_info.icon)
	-- 	self.node_list.icon.raw_image:LoadSprite(bundel, asset, function ()
	-- 		self.node_list.icon.raw_image:SetNativeSize()
	-- 	end)
	-- end
end


function OATurnTablePageViewCellRenderer:SetCellState(state)
	self.node_list.tween_root:SetActive(state)
end

function OATurnTablePageViewCellRenderer:SetPos(pos_x, pos_y)
	self.node_list.tween_root.rect.anchoredPosition = Vector2(pos_x, pos_y)
end

function OATurnTablePageViewCellRenderer:CoverCard(center_render,tween_callback)
	if true == self.is_in_tween then
		return
	end
	self.is_in_tween = true

	self.tween_callback = tween_callback
	local trans_info = {}
	trans_info.pos = self.node_list.tween_root.rect.anchoredPosition
	self.tween_info = trans_info
	
	local focus_pos = center_render:GetCenterPos()
	local is_reward = center_render == self
	
 	local start_tween = self.node_list.tween_root.transform:DOLocalRotate(Vector3(0, 90, 0), 0.4)
	 start_tween:SetEase(DG.Tweening.Ease.Linear)
	 start_tween:OnComplete(function ()
		 self.node_list.icon:SetActive(false)
		 self.node_list.reward_bg:SetActive(false)
		 self.node_list.page_view_name:SetActive(false)
		 self.node_list.bg:SetActive(true)
		 local end_tween = self.node_list.tween_root.transform:DOLocalRotate(Vector3(0, 0, 0), 0.4)
		 end_tween:SetEase(DG.Tweening.Ease.Linear)
		 end_tween:OnComplete(function ()
			self:Focus(focus_pos,is_reward)
		 end)
	 end)
end

function OATurnTablePageViewCellRenderer:Focus(focus_pos,is_reward)
	local focus_tween = self.node_list.tween_root.transform:DOAnchorPos(focus_pos, 0.4)
	focus_tween:SetEase(DG.Tweening.Ease.Linear)
	focus_tween:OnComplete(function ()
		self:CancelTween()
		local sequence = DG.Tweening.DOTween.Sequence()
		self:ShakeAnimi(self.node_list.tween_root.transform,sequence)
		sequence:OnComplete(function ()
			self:SpreadOut(is_reward)
		end)

		self.sequence_tween = sequence
	end)
end

function OATurnTablePageViewCellRenderer:CancelTween()
    if self.sequence_tween then
        self.sequence_tween:Kill()
        self.sequence_tween = nil
    end
end

function OATurnTablePageViewCellRenderer:ShakeAnimi(trans, sequence)
	sequence = sequence or DG.Tweening.DOTween.Sequence()
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 0), 0.2, DG.Tweening.RotateMode.Fast)) 		--恢复0
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, -30), 0.1, DG.Tweening.RotateMode.Fast)) 	--左50
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 25), 0.2, DG.Tweening.RotateMode.Fast))	--右40
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, -20), 0.2, DG.Tweening.RotateMode.Fast))	--左30
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 15), 0.2, DG.Tweening.RotateMode.Fast))	--右20
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, -10), 0.2, DG.Tweening.RotateMode.Fast))	--左10
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 5), 0.2, DG.Tweening.RotateMode.Fast))	--右5
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 0), 0.1, DG.Tweening.RotateMode.Fast))	--恢复 0
    sequence:SetEase(DG.Tweening.Ease.Linear)
    sequence:SetLoops(1)
end

function OATurnTablePageViewCellRenderer:SpreadOut(is_reward)
	local move_endtween = self.node_list.tween_root.transform:DOAnchorPos(self.tween_info.pos, 0.3)
	move_endtween:SetEase(DG.Tweening.Ease.Linear)
	move_endtween:SetDelay(0.3)
	move_endtween:OnComplete(function ()
		if is_reward then
			self:ShowReward()
		end
		self.is_in_tween = false
	end)
end

function OATurnTablePageViewCellRenderer:GetCenterPos()
	return self.node_list.tween_root.rect.anchoredPosition
end

function OATurnTablePageViewCellRenderer:ShowReward()
	local reward_tween = self.node_list.tween_root.transform:DOLocalRotate(Vector3(0, 90, 0), 0.5)
	reward_tween:SetEase(DG.Tweening.Ease.Linear)
	reward_tween:SetDelay(0.3)
	reward_tween:OnComplete(function ()
		self.node_list.hightlight_bg:SetActive(true)
		self.node_list.icon:SetActive(true)
		self.node_list.reward_bg:SetActive(true)
		self.node_list.page_view_name:SetActive(true)
		self.node_list.bg:SetActive(false)
		local end_tween = self.node_list.tween_root.transform:DOLocalRotate(Vector3(0, 0, 0), 0.5)
		end_tween:SetEase(DG.Tweening.Ease.Linear)
		end_tween:OnComplete(function ()
			if self.tween_callback then
				self.tween_callback()
			end
		end)
	end)
end
-----------------------------------------轮播图End--------------------------------------------------

OATurnTableRewardRenderer = OATurnTableRewardRenderer or BaseClass(BaseRender)

function OATurnTableRewardRenderer:__init()
	self.item_cell = ItemCell.New(self.node_list.item_cell_pos)
end

function OATurnTableRewardRenderer:__delete()
	self.item_cell:DeleteMe()
	self.item_cell = nil
end

function OATurnTableRewardRenderer:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end
	local data = self.data
	self.item_cell:SetData(data.reward_info.reward_item)
	self.node_list.img_mask:SetActive(not data.state)
end

