-------------------------新灵犀芒图 - 全服抽奖次数奖励信息 START----------------------------
SCNewLingXiMangTuServerDrawTimesInfo = SCNewLingXiMangTuServerDrawTimesInfo or BaseClass(BaseProtocolStruct)
function SCNewLingXiMangTuServerDrawTimesInfo:__init()
    self.msg_type = 16521
end

function SCNewLingXiMangTuServerDrawTimesInfo:Decode()
	self.draw_times = MsgAdapter.ReadUInt()  					-- 全服抽奖次数 
	self.server_draw_times_reward_flag = MsgAdapter.ReadUInt()  -- 角色全服抽奖次数领取标识 字节位对应配置表seq, 0:未领取 1:已领取
end

SCNewLingxiMangTuDailyRewardInfo = SCNewLingxiMangTuDailyRewardInfo or BaseClass(BaseProtocolStruct)
function SCNewLingxiMangTuDailyRewardInfo:__init()
    self.msg_type = 16527
end

function SCNewLingxiMangTuDailyRewardInfo:Decode()
    self.flag = MsgAdapter.ReadInt()                           -- 0:未领取 1:已领取
end

-------------------------新灵犀芒图 - 全服抽奖次数奖励信息 END----------------------------

-- 天神殿协议 start
CSTianshenHallOperate = CSTianshenHallOperate or BaseClass(BaseProtocolStruct)
function CSTianshenHallOperate:__init()
    self.msg_type = 16540
end

function CSTianshenHallOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

SCTianshenHallBaseInfo = SCTianshenHallBaseInfo or BaseClass(BaseProtocolStruct)
function SCTianshenHallBaseInfo:__init()
    self.msg_type = 16541
end

function SCTianshenHallBaseInfo:Decode()
    self.use_seq = MsgAdapter.ReadInt()
end

-- 所有的天神神殿信息
SCTianshenHallItemInfo = SCTianshenHallItemInfo or BaseClass(BaseProtocolStruct)
function SCTianshenHallItemInfo:__init()
    self.msg_type = 16542
end

function SCTianshenHallItemInfo:Decode()
	self.hall_item_list = {}
	for i = 1, TIANSHEN_HALL_ENUM.MAX_ITEM_COUNT do
		self.hall_item_list[i] = {}
		self.hall_item_list[i].order = MsgAdapter.ReadInt()

		self.hall_item_list[i].index_list = {}
		for j = 1, TIANSHEN_HALL_ENUM.MAX_INDEX_COUNT do
			self.hall_item_list[i].index_list[j] = MsgAdapter.ReadShort()
		end
	end
end

-- 单个天神殿信息更新
SCTianshenHallItemUpdate = SCTianshenHallItemUpdate or BaseClass(BaseProtocolStruct)
function SCTianshenHallItemUpdate:__init()
    self.msg_type = 16543
end

function SCTianshenHallItemUpdate:Decode()
    self.seq = MsgAdapter.ReadInt()
	self.hall_item = {}
	self.hall_item.order = MsgAdapter.ReadInt()

	self.hall_item.index_list = {}
	for j = 1, TIANSHEN_HALL_ENUM.MAX_INDEX_COUNT do
		self.hall_item.index_list[j] = MsgAdapter.ReadShort()
	end
end

-- 天神殿上阵位置(一键上阵)
CSTianshenHallGoHall = CSTianshenHallGoHall or BaseClass(BaseProtocolStruct)
function CSTianshenHallGoHall:__init()
    self.msg_type = 16544
end

function CSTianshenHallGoHall:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.seq)
	for j = 1, TIANSHEN_HALL_ENUM.MAX_INDEX_COUNT do
		local index = self.index_list[j] or -1
		MsgAdapter.WriteShort(index)
	end
end

-- 天神殿协议 end

----------------------------- 开服活动扩展 - 直购,抽奖,任务,累充,每日奖励 start -----------------------------
local MAX_OGA_RMB_BUY_SEQ = 16 -- 直购seq最大索引
local MAX_OGA_TASK_TYPE = 4 -- 任务类型最大类型
-- 开服活动扩展 - 总信息
SCOGAExtendTotalInfo = SCOGAExtendTotalInfo or BaseClass(BaseProtocolStruct)
function SCOGAExtendTotalInfo:__init()
	self.msg_type = 16575
end

function SCOGAExtendTotalInfo:Decode()
	self.daily_reward_flag = MsgAdapter.ReadChar();			-- 每日领取标识 0：未领取 1：已领取
	local reserve_chs 
	for i = 1, 3 do
		reserve_chs = MsgAdapter.ReadChar();				-- 预留
	end

	local rmb_buy_info = {}									
	for i = 0, MAX_OGA_RMB_BUY_SEQ - 1 do
		rmb_buy_info[i] = MsgAdapter.ReadUChar()			-- 直购信息 下标:索引 值:购买次数
	end
	self.rmb_buy_info = rmb_buy_info

	local t_high = MsgAdapter.ReadUInt()
	local t_low = MsgAdapter.ReadUInt()
	local tb_decode = bit:ll2b_two(t_high, t_low)				-- 任务领取字节标识
	self.task_fetch_flag_list = tb_decode

	local task_process_list = {}
	local task_process_val
	for i = 0, MAX_OGA_TASK_TYPE - 1 do
		task_process_val = MsgAdapter.ReadInt()
		task_process_list[i] = task_process_val
	end
	self.task_process_list = task_process_list				-- 下标:任务类型 值:任务进度

	local cumulate_recharge_fetch_flag = MsgAdapter.ReadInt()				-- 累充领取标识 1 true 0 false
	self.cumulate_recharge_fetch_flag_list = bit:d2b_l2h(cumulate_recharge_fetch_flag, nil, true)
	self.recharge_num = MsgAdapter.ReadInt();				-- 充值额度
end

-- 开服活动扩展 - 直购信息更新
SCOGAExtendRmbBuyInfoUpdate = SCOGAExtendRmbBuyInfoUpdate or BaseClass(BaseProtocolStruct)
function SCOGAExtendRmbBuyInfoUpdate:__init()
	self.msg_type = 16576
end

function SCOGAExtendRmbBuyInfoUpdate:Decode()
	local info = {}
	info.seq 		= MsgAdapter.ReadUChar()	-- 索引
	info.buy_times 	= MsgAdapter.ReadUChar()	-- 购买次数
	self.ret_info = info
end

-- 开服活动扩展 - 任务进度更新
SCOGAExtendTaskProcessUpdate = SCOGAExtendTaskProcessUpdate or BaseClass(BaseProtocolStruct)
function SCOGAExtendTaskProcessUpdate:__init()
	self.msg_type = 16577
end

function SCOGAExtendTaskProcessUpdate:Decode()
	local info = {}
	info.type = 		MsgAdapter.ReadUChar()		-- 索引
	local reserve_chs 
	for i = 1, 3 do
		reserve_chs = MsgAdapter.ReadChar();		-- 预留
	end			
	info.task_process = MsgAdapter.ReadUChar() 		-- 进度
	self.ret_info = info
end

-- 开服活动扩展 - 任务领取状态更新
SCOGAExtendTaskFetchFlagUpdate = SCOGAExtendTaskFetchFlagUpdate or BaseClass(BaseProtocolStruct)
function SCOGAExtendTaskFetchFlagUpdate:__init()
	self.msg_type = 16578
end

function SCOGAExtendTaskFetchFlagUpdate:Decode()
	local t_high = MsgAdapter.ReadUInt()
	local t_low = MsgAdapter.ReadUInt()
	local tb_decode = bit:ll2b_two(t_high, t_low)				-- 任务领取字节标识
	self.ret_info = tb_decode
end

-- 开服活动扩展 - 累充信息更新
SCOGAExtendCumulateRechargeInfoUpdate = SCOGAExtendCumulateRechargeInfoUpdate or BaseClass(BaseProtocolStruct)
function SCOGAExtendCumulateRechargeInfoUpdate:__init()
	self.msg_type = 16579
end

function SCOGAExtendCumulateRechargeInfoUpdate:Decode()
	local info = {}
	local cumulate_recharge_fetch_flag = MsgAdapter.ReadInt()				-- 任务领取字节标识
	info.cumulate_recharge_fetch_flag_list = bit:d2b_l2h(cumulate_recharge_fetch_flag, nil, true)
	info.recharge_num = MsgAdapter.ReadInt();				-- 充值额度
	self.ret_info = info
end


-- 开服活动扩展 - 操作
CSOGAExtendReqOperate = CSOGAExtendReqOperate or BaseClass(BaseProtocolStruct)
function CSOGAExtendReqOperate:__init()
	self.msg_type = 16580
end

function CSOGAExtendReqOperate:Encode()
    MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1 or 0)
	MsgAdapter.WriteInt(self.param2 or 0)
end

----------------------------- 开服活动扩展 - 直购,抽奖,任务,累充,每日奖励  end  -----------------------------

----------------------神格抽奖start----------------------

-- 神格抽奖
CSGodhoodDrawOperate = CSGodhoodDrawOperate or BaseClass(BaseProtocolStruct)
function CSGodhoodDrawOperate:__init()
	self.msg_type = 16545
end

function CSGodhoodDrawOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)

	-- print_error("16545", self.operate_type, self.param1, self.param2, self.param3)
end

function GetMsgGodhoodDrawItem()
	local draw_item = {}
	draw_item.grade = MsgAdapter.ReadInt()
	draw_item.draw_times = MsgAdapter.ReadLL()
	draw_item.lucky = MsgAdapter.ReadLL()
	draw_item.times_reward_seq = MsgAdapter.ReadInt()
	draw_item.score = MsgAdapter.ReadLL()
	draw_item.draw_score = MsgAdapter.ReadLL()
	draw_item.convert_times_list = {}
	for i = 1, 40 do
		draw_item.convert_times_list[i] = MsgAdapter.ReadUChar()
	end
	return draw_item
end

-- 神格抽奖道具信息
SCGodhoodDrawItemInfo = SCGodhoodDrawItemInfo or BaseClass(BaseProtocolStruct)
function SCGodhoodDrawItemInfo:__init()
    self.msg_type = 16546
end

function SCGodhoodDrawItemInfo:Decode()
	self.item = GetMsgGodhoodDrawItem()
end

function GetMsgResultItem()
	local result_item = {}
	result_item.item_id = MsgAdapter.ReadItemId()
	result_item.reversh = MsgAdapter.ReadChar()
	result_item.is_bind = MsgAdapter.ReadChar()
	result_item.num = MsgAdapter.ReadInt()
	return result_item
end

-- 神格抽奖结果
SCGodhoodDrawDrawResult = SCGodhoodDrawDrawResult or BaseClass(BaseProtocolStruct)
function SCGodhoodDrawDrawResult:__init()
    self.msg_type = 16547
end

function SCGodhoodDrawDrawResult:Decode()
	self.times = MsgAdapter.ReadInt()
	self.baodi_item = GetMsgResultItem()
	self.count = MsgAdapter.ReadInt()
	self.result_item_list = {}
	for i = 1, self.count do
		self.result_item_list[i] = GetMsgResultItem()
	end
end

function GetMsgGodhoodDrawRecordItem()
	local record_item = {}
	record_item.uid = MsgAdapter.ReadInt()
	record_item.name = MsgAdapter.ReadName()
	record_item.item_id = MsgAdapter.ReadItemId()
	record_item.num = MsgAdapter.ReadUShort()
	record_item.time = MsgAdapter.ReadUInt()
	return record_item
end

-- 神格抽奖记录
SCGodhoodDrawRecordInfo = SCGodhoodDrawRecordInfo or BaseClass(BaseProtocolStruct)
function SCGodhoodDrawRecordInfo:__init()
    self.msg_type = 16548
end

function SCGodhoodDrawRecordInfo:Decode()
	self.count = MsgAdapter.ReadInt()
	self.record_data_list = {}
	for i = 1, self.count do
		self.record_data_list[i] = GetMsgGodhoodDrawRecordItem()
	end
end

-- 神格抽奖记录
SCGodhoodDrawRecordAdd = SCGodhoodDrawRecordAdd or BaseClass(BaseProtocolStruct)
function SCGodhoodDrawRecordAdd:__init()
    self.msg_type = 16549
end

function SCGodhoodDrawRecordAdd:Decode()
	self.record_data = GetMsgGodhoodDrawRecordItem()
end


----------------------神格抽奖start----------------------


-- 护送答题
CSHuSongAnswer = CSHuSongAnswer or BaseClass(BaseProtocolStruct)
function CSHuSongAnswer:__init()
	self.msg_type = 16581
end

function CSHuSongAnswer:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.index)
	MsgAdapter.WriteInt(self.answer_seq)

	-- print_error("16581", self.operate_type, self.index, self.answer_seq)
end

----------------------天财地宝start----------------------
CSTreasureOperate = CSTreasureOperate or BaseClass(BaseProtocolStruct)
function CSTreasureOperate:__init()
    self.msg_type = 16582
end

function CSTreasureOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.param_1)
	MsgAdapter.WriteInt(self.param_2)
	MsgAdapter.WriteInt(self.param_3)
end


--天财地宝 登录有礼信息
SCTreasureNewLoginGiftInfo = SCTreasureNewLoginGiftInfo or BaseClass(BaseProtocolStruct)
function SCTreasureNewLoginGiftInfo:__init()
	self.msg_type = 16583
end

function SCTreasureNewLoginGiftInfo:Decode()
	self.login_day_num = MsgAdapter.ReadInt()				--登录天数
	self.login_reward_flag = bit:d2b_l2h(MsgAdapter.ReadInt(), nil, true)			-- 奖励标记
end


--天财地宝 首充奖励信息
SCTreasureShouChongInfo = SCTreasureShouChongInfo or BaseClass(BaseProtocolStruct)
function SCTreasureShouChongInfo:__init()
	self.msg_type = 16584
end

function SCTreasureShouChongInfo:Decode()
	self.shouchong_reward_flag = MsgAdapter.ReadChar()     -- 0: 不可领取 1: 可领取 2: 已领取
	MsgAdapter.ReadChar()
	MsgAdapter.ReadShort()
end


--天财地宝 累计充值信息
SCTreasureLeiChongInfo = SCTreasureLeiChongInfo or BaseClass(BaseProtocolStruct)
function SCTreasureLeiChongInfo:__init()
	self.msg_type = 16585
end

function SCTreasureLeiChongInfo:Decode()
	self.cur_xianyu = MsgAdapter.ReadLL()												-- 当前仙玉总量
	self.leichong_reward_flag = bit:d2b_l2h(MsgAdapter.ReadInt(), nil, true)			-- 奖励标记
end


--天财地宝 拼图领奖信息
SCWoYaoShenQiInfo = SCWoYaoShenQiInfo or BaseClass(BaseProtocolStruct)
function SCWoYaoShenQiInfo:__init()
	self.msg_type = 16586
end

function SCWoYaoShenQiInfo:Decode()
	self.buy_libao_cnt = MsgAdapter.ReadInt()							-- 当前购买礼包数量
	self.reward_flag = MsgAdapter.ReadChar()							-- 最终奖励标记
	MsgAdapter.ReadChar()
	MsgAdapter.ReadShort()

	self.task_list = {}
	for i = 1, 24, 1 do
		local data = {}
		data.gift_state = MsgAdapter.ReadInt()  --0: 不可领取 1: 可领取 2: 已领取
		data.param = MsgAdapter.ReadInt()
		self.task_list[i] = data
	end
end


--限时抢购全部信息	
SCHunzhenShopAllInfo = SCHunzhenShopAllInfo or BaseClass(BaseProtocolStruct)
function SCHunzhenShopAllInfo:__init()
	self.msg_type = 16587
end

function SCHunzhenShopAllInfo:Decode()
	self.rmb_shop_buy_flag = MsgAdapter.ReadChar()
	self.daily_reward_flag = MsgAdapter.ReadChar()
	MsgAdapter.ReadShort()
	self.hunzhen_shop_count = {}
	for i = 0, 47, 1 do
		self.hunzhen_shop_count[i] = MsgAdapter.ReadInt()
	end
end


--限时抢购单个更新
SCHunzhenShopUpdateInfo = SCHunzhenShopUpdateInfo or BaseClass(BaseProtocolStruct)
function SCHunzhenShopUpdateInfo:__init()
	self.msg_type = 16588
end

function SCHunzhenShopUpdateInfo:Decode()
	self.seq = MsgAdapter.ReadInt()
	self.count = MsgAdapter.ReadInt()
	self.rmb_shop_buy_flag = MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
	MsgAdapter.ReadShort()
end



--天财地宝 基础信息
SCTreasureBaseInfo = SCTreasureBaseInfo or BaseClass(BaseProtocolStruct)
function SCTreasureBaseInfo:__init()
	self.msg_type = 16589
end

function SCTreasureBaseInfo:Decode()
	self.grade = MsgAdapter.ReadInt()
	self.cur_grade_end_time = MsgAdapter.ReadUInt()
end

function GetTrialFbitemInfo()
	local item = {}
	item.item_id = MsgAdapter.ReadItemId()
	item.num = MsgAdapter.ReadShort()
	item.is_bind = MsgAdapter.ReadShort()
	item.star = MsgAdapter.ReadShort()
	return item
end

-- 试炼副本结算信息
SCTrialFbFinishInfo = SCTrialFbFinishInfo or BaseClass(BaseProtocolStruct)
function SCTrialFbFinishInfo:__init()
	self.msg_type = 16590
end

function SCTrialFbFinishInfo:Decode()
	local count = MsgAdapter.ReadInt()
	self.partipate_list = {}
	self.fall_item_list = {}
	for i = 1, 15 do
		self.partipate_list[i] = GetTrialFbitemInfo()
	end	
	for i = 1, count do
		self.fall_item_list[i] = GetTrialFbitemInfo()
	end
end

-- 特殊掉落信息
SCTreasureSpecialDropInfo = SCTreasureSpecialDropInfo or BaseClass(BaseProtocolStruct)
function SCTreasureSpecialDropInfo:__init()
	self.msg_type = 16592
end

function SCTreasureSpecialDropInfo:Decode()
	self.drop_count_list = {}
	for i = 1, 5 do
		self.drop_count_list[i] =  MsgAdapter.ReadInt()
	end	
	
end

--天财地宝 龙蛋信息
SCTreasureDragonEgg = SCTreasureDragonEgg or BaseClass(BaseProtocolStruct)
function SCTreasureDragonEgg:__init()
	self.msg_type = 16591
end

function SCTreasureDragonEgg:Decode()
	self.exp = MsgAdapter.ReadUInt()
	self.level = MsgAdapter.ReadUShort()
	self.reward_flag = MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
end

--天财地宝 试炼副本 boss击杀信息
SCTrialFBBossDeadInfo = SCTrialFBBossDeadInfo or BaseClass(BaseProtocolStruct)
function SCTrialFBBossDeadInfo:__init()
	self.msg_type = 16593
end

function SCTrialFBBossDeadInfo:Decode()
	self.killer_uid = MsgAdapter.ReadInt()
	self.is_boss_dead = MsgAdapter.ReadInt()
end
-----------------------天财地宝end-----------------------

-----------------------限时狂欢-----------------------
--限时狂欢 魔王入侵 boss击杀信息
SCMoWuJiangLinDeadInfo = SCMoWuJiangLinDeadInfo or BaseClass(BaseProtocolStruct)
function SCMoWuJiangLinDeadInfo:__init()
	self.msg_type = 16597
end

function SCMoWuJiangLinDeadInfo:Decode()
	self.killer_uid = MsgAdapter.ReadInt()
	self.is_boss_dead = MsgAdapter.ReadInt()
end

-----------------------限时狂欢end-----------------------

-----------------------------豪礼万丈-----------------------
SCOAHaoLiWanZhang = SCOAHaoLiWanZhang or BaseClass(BaseProtocolStruct)
function SCOAHaoLiWanZhang:__init()
	self.msg_type = 16551
end

function SCOAHaoLiWanZhang:Decode()
    self.score = MsgAdapter.ReadLL()				-- 积分
	self.grade = MsgAdapter.ReadInt()				-- 档次
	self.round_num = MsgAdapter.ReadInt()			-- 轮数
	self.reward_flag = {}							-- 最大轮数
	for i = 0, 4 do
		local flag = MsgAdapter.ReadInt()
		self.reward_flag[i] = bit:d2b_l2h(flag, nil, true)
	end
	self.every_day_flag = MsgAdapter.ReadUChar()		-- 每日奖励
	MsgAdapter.ReadChar()
	MsgAdapter.ReadShort()
end
-----------------------------豪礼万丈-----------------------

-----------------------------首充豪礼start-----------------------
SCOAHaoLiWanZhang2Info = SCOAHaoLiWanZhang2Info or BaseClass(BaseProtocolStruct)
function SCOAHaoLiWanZhang2Info:__init()
	self.msg_type = 16573
end

function SCOAHaoLiWanZhang2Info:Decode()
    self.score = MsgAdapter.ReadLL()				-- 积分
	self.grade = MsgAdapter.ReadInt()				-- 档次
	self.round_num = MsgAdapter.ReadInt()			-- 轮数
	self.reward_flag = {}							-- 最大轮数
	for i = 0, 4 do
		local flag = MsgAdapter.ReadInt()
		self.reward_flag[i] = bit:d2b_l2h(flag, nil, true)
	end
	self.every_day_flag = MsgAdapter.ReadUChar()		-- 每日奖励
	MsgAdapter.ReadChar()
	MsgAdapter.ReadShort()
end
-----------------------------首充豪礼end-----------------------

-----------------------------豪礼万丈3start-----------------------
SCOAHaoLiWanZhang3Info = SCOAHaoLiWanZhang3Info or BaseClass(BaseProtocolStruct)
function SCOAHaoLiWanZhang3Info:__init()
	self.msg_type = 16574
end

function SCOAHaoLiWanZhang3Info:Decode()
    self.score = MsgAdapter.ReadLL()				-- 积分/灵玉
	self.grade = MsgAdapter.ReadInt()				-- 档次
	self.round_num = MsgAdapter.ReadInt()			-- 轮数
	self.reward_flag = {}							-- 最大轮数
	for i = 0, 4 do
		local flag = MsgAdapter.ReadInt()
		self.reward_flag[i] = bit:d2b_l2h(flag, nil, true)
	end
	self.every_day_flag = MsgAdapter.ReadUChar()		-- 每日奖励
	MsgAdapter.ReadChar()
	MsgAdapter.ReadShort()
end
-----------------------------豪礼万丈3end-----------------------

-----------------------------驭兽抽奖记录start----------------------
SCBeastDrawRecordInfo = SCBeastDrawRecordInfo or BaseClass(BaseProtocolStruct)
function SCBeastDrawRecordInfo:__init()
	self.msg_type = 16594
end

function SCBeastDrawRecordInfo:Decode()
    self.type = MsgAdapter.ReadInt()				-- 抽奖类型
	self.count = MsgAdapter.ReadInt()				-- 抽奖记录个数

	self.record_data_list = {}
	for i = 1, self.count do
		self.record_data_list[i] = {}
		self.record_data_list[i].beast_id  = MsgAdapter.ReadInt()
		self.record_data_list[i].record_time  = MsgAdapter.ReadUInt()
	end
end

SCBeastDrawRecordAdd = SCBeastDrawRecordAdd or BaseClass(BaseProtocolStruct)
function SCBeastDrawRecordAdd:__init()
	self.msg_type = 16595
end

function SCBeastDrawRecordAdd:Decode()
    self.type = MsgAdapter.ReadInt()				-- 抽奖类型
	self.record_data = {}
	self.record_data.beast_id  = MsgAdapter.ReadInt()
	self.record_data.record_time  = MsgAdapter.ReadUInt()
end
-----------------------------驭兽抽奖start----------------------

-----------------------------历练副本通关时间start----------------------
SCExpWestPassTimeInfo = SCExpWestPassTimeInfo or BaseClass(BaseProtocolStruct)
function SCExpWestPassTimeInfo:__init()
	self.msg_type = 16596
end

function SCExpWestPassTimeInfo:Decode()
	self.fb_pass_time  = MsgAdapter.ReadUInt()
end

-----------------------------历练副本通关时间end----------------------

SCFightTianXuanBuffInfo = SCFightTianXuanBuffInfo or BaseClass(BaseProtocolStruct)
function SCFightTianXuanBuffInfo:__init()
	self.msg_type = 16550
end

function SCFightTianXuanBuffInfo:Decode()
	self.uid = MsgAdapter.ReadInt()
	self.get_buff_time = MsgAdapter.ReadUInt()
	self.buff_refresh_time = MsgAdapter.ReadUInt()

	self.sex = MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
	self.pos_x = MsgAdapter.ReadShort()
	self.pos_y = MsgAdapter.ReadShort()
	self.fashion_photoframe = MsgAdapter.ReadShort()
	self.prof = MsgAdapter.ReadInt()
end

-----------------------------幻兽图鉴start----------------------
SCBeastHoodbookInfo = SCBeastHoodbookInfo or BaseClass(BaseProtocolStruct)
function SCBeastHoodbookInfo:__init()
	self.msg_type = 16598
end

function SCBeastHoodbookInfo:Decode()
	self.item_list = {}

	for i = 0, BEAST_DEFINE.MAX_BOOK_ITEM_COUNT do
		self.item_list[i] = {}
		self.item_list[i].max_star  = MsgAdapter.ReadInt()
		self.item_list[i].reward_star  = MsgAdapter.ReadInt()
	end
end

SCBeastHoodbookUpdate = SCBeastHoodbookUpdate or BaseClass(BaseProtocolStruct)
function SCBeastHoodbookUpdate:__init()
	self.msg_type = 16599
end

function SCBeastHoodbookUpdate:Decode()
    self.beast_type = MsgAdapter.ReadInt()				-- 抽奖类型
	self.item_data = {}
	self.item_data.max_star  = MsgAdapter.ReadInt()
	self.item_data.reward_star  = MsgAdapter.ReadInt()
end
-----------------------------幻兽图鉴end----------------------

---------------------------终身特惠start----------------------------
SCLifeIndulgenceInfo = SCLifeIndulgenceInfo or BaseClass(BaseProtocolStruct)
function SCLifeIndulgenceInfo:__init()
    self.msg_type = 16514
end

function SCLifeIndulgenceInfo:Decode()
	self.life_indulgence_shop_buy = {}
	for i = 0, 63 do
		self.life_indulgence_shop_buy[i] = MsgAdapter.ReadUChar()
	end
end
---------------------------终身特惠end---------------------------