﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class ProjectileSingleWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(ProjectileSingle), typeof(Projectile));
		<PERSON><PERSON>Function("Play", Play);
		<PERSON><PERSON>Function("Update", Update);
		<PERSON><PERSON>unction("__eq", op_Equality);
		L.<PERSON>Function("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Play(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 6);
			ProjectileSingle obj = (ProjectileSingle)ToLua.CheckObject(L, 1, typeof(ProjectileSingle));
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			UnityEngine.Transform arg1 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 3);
			int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
			System.Action arg3 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 5);
			System.Action arg4 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 6);
			obj.Play(arg0, arg1, arg2, arg3, arg4);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Update(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ProjectileSingle obj = (ProjectileSingle)ToLua.CheckObject(L, 1, typeof(ProjectileSingle));
			obj.Update();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

