NewHuanHuaFetterView = NewHuanHuaFetterView or BaseClass(SafeBaseView)

function NewHuanHuaFetterView:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true

    self:AddViewResource(0, "uis/view/huanhua_fetter_ui_prefab", "layout_new_huanhua_fetter")
    -- self:AddViewResource(0, "uis/view/huanhua_fetter_ui_prefab", "VerticalTabbar")
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_top_panel")

    self:SetTabShowUIScene(0, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.FETTERS_COLLECTION})
end

function NewHuanHuaFetterView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.HuanHuaFetter.TitleViewName

    if not self.money_bar then
        self.money_bar = MoneyBar.New()
        local bundle, asset = ResPath.GetWidgets("MoneyBar")
        local show_params = {
            show_gold = true,
            show_bind_gold = true,
            show_coin = true,
            show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
        self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
    end

    self.load_equip_cell_complete = false

    self.cell_list = {}
        
    local type_data_list = NewHuanHuaFetterWGData.Instance:GetTotalToggleShowBigTypeDataList()
    
    for i = 1, 10, 1 do
        local data = type_data_list[i]
        local has_data = not IsEmptyTable(data)
        
        self.node_list["SelectBtn" .. i]:CustomSetActive(has_data)
        -- self.node_list["List" .. i]:CustomSetActive(has_data)

        if has_data then
            self.node_list["normal_text" .. i].text.text = Language.HuanHuaFetter.TabGrop[data[1].big_type] or ""
            self.node_list["hl_text" .. i].text.text = Language.HuanHuaFetter.TabGrop[data[1].big_type] or ""
            
            self:LoadHuanHuaListCell(i, #type_data_list, data)
            self.node_list["SelectBtn" .. i].accordion_element:AddValueChangedListener(BindTool.Bind(self.OnClickBigTypeHandler, self, data[1].big_type))
        end
    end

    if not self.huanhua_item_list then
        self.huanhua_item_list = {}

        for i = 1, 4 do
            self.huanhua_item_list[i] = HuanHuaFetterMidItemCellRender.New(self.node_list["huanhua_item_" .. i])
        end
    end

    self.model_show_cache = {}

    if not self.model_display then
        self.model_display = RoleModel.New()
        self.model_display:SetUISceneModel(self.node_list.model_display.event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
        self:AddUiRoleModel(self.model_display, 0)
    end

    XUI.AddClickEventListener(self.node_list.btn_jbjx, BindTool.Bind(self.OnClickJBJXBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_get_reward, BindTool.Bind(self.OnClickGetRewardBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_show_reward, BindTool.Bind(self.OnClickShowRewardBtn, self))
end

function NewHuanHuaFetterView:ReleaseCallBack()
    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end

    if self.huanhua_item_list then
        for k, v in pairs(self.huanhua_item_list) do
            v:DeleteMe()
        end

        self.huanhua_item_list =  nil
    end

    if self.cell_list then
        for k, v in pairs(self.cell_list) do
            if not IsEmptyTable(v) then
                for i, u in pairs(v) do
                    u:DeleteMe()
                end
            end
        end

        self.cell_list = nil
    end

    self.model_show_cache = nil
    self.load_equip_cell_complete = nil
end

function NewHuanHuaFetterView:LoadHuanHuaListCell(index, length, cell_data_list)
    local res_async_loader = AllocResAsyncLoader(self, "NewHuanHuaFetterView" .. index)
    res_async_loader:Load("uis/view/huanhua_fetter_ui_prefab", "new_fetter_toggle_cell", nil,
        function(new_obj)
            local tab_index = self:GetShowIndex()
            local item_vo = {}

            for i=1, #cell_data_list do
                local obj = ResMgr:Instantiate(new_obj)
                local obj_transform = obj.transform
                obj_transform:SetParent(self.node_list["List" .. index].transform, false)
                local item_render = HuanHuaFetterSmallTypeItemCellRender.New(obj)
                obj:GetComponent("Toggle").group = self.node_list["List" .. index].toggle_group
                obj:GetComponent("Toggle"):AddValueChangedListener(BindTool.Bind(self.OnClickSmallTypeHandle, self, item_render))
                item_render.parent_view = self
                item_render:SetData(cell_data_list[i])
                item_vo[i] = item_render
    
                if index == length and i == #cell_data_list then
                    self.load_equip_cell_complete = true
                end
                
                obj:SetActive(false)
            end
    
            self.cell_list[index] = item_vo
            if self.load_equip_cell_complete then
                self:Flush()
            end
        end 
    )  
end

function NewHuanHuaFetterView:OnFlush(param_t, index)
    if not self.load_equip_cell_complete then
		return
	end

    -- 设置大类型得激活状态红点信息
    local type_data_list = NewHuanHuaFetterWGData.Instance:GetTotalToggleShowBigTypeDataList()
        
    for i = 1, 10, 1 do
        local data = type_data_list[i]
        local has_data = not IsEmptyTable(data)
        local can_show = false

        if has_data then
            can_show = NewHuanHuaFetterWGData.Instance:IsCanShowHuanHuaFetterType(data[1].big_type)
        end

        self.node_list["SelectBtn" .. i]:CustomSetActive(can_show)

        if can_show then
            local remind = NewHuanHuaFetterWGData.Instance:GetFetterBigTypeRemind(data[1].big_type)
            self.node_list["tog_remind" .. i]:CustomSetActive(remind)
        end
    end

    local select_bigtype = self:GetDefaultSelectBigType()
    if self.node_list["SelectBtn" .. select_bigtype].accordion_element.isOn then
        self:OnClickBigTypeHandler(select_bigtype, true)
    else
        self.node_list["SelectBtn" .. select_bigtype].accordion_element.isOn = true
    end
end

function NewHuanHuaFetterView:GetDefaultSelectBigType()
	if self.big_type then
		if NewHuanHuaFetterWGData.Instance:GetFetterBigTypeRemind(self.big_type) then 
			return self.big_type
		end
	end

    local default_select_big_type= self.big_type or 1
    local type_data_list = NewHuanHuaFetterWGData.Instance:GetTotalToggleShowBigTypeDataList()
    if not IsEmptyTable(type_data_list) then
        for k, v in pairs(type_data_list) do
            if NewHuanHuaFetterWGData.Instance:GetFetterBigTypeRemind(k) then 
                default_select_big_type = k
                break
            end
        end
    end

	return default_select_big_type
end

function NewHuanHuaFetterView:OnClickBigTypeHandler(big_type, isOn)
    if nil == big_type or not isOn  then
		return
	end

    local big_type_change = self.big_type ~= big_type
	self.big_type = big_type

    -- 设置子类型数据
    if not IsEmptyTable(self.cell_list[big_type]) then
        local cell_data = NewHuanHuaFetterWGData.Instance:GetToggleShowBigTypeDataList(big_type)
        for k, v in pairs(self.cell_list[big_type]) do
            v:SetData(cell_data[k])
        end        
    end

    local small_type = self:GetDefaultSelectSmallType(big_type_change)

    -- 选中子类型
    if self.cell_list ~= nil and self.cell_list[big_type] ~= nil then
		if self.cell_list[big_type][small_type].view.toggle.isOn then
			self:OnClickSmallTypeHandle(self.cell_list[big_type][small_type], true)
		else
			self.cell_list[big_type][small_type].view.toggle.isOn = true
		end
	end
end

function NewHuanHuaFetterView:GetDefaultSelectSmallType(big_type_change)
	if not big_type_change and self.small_type then
		local small_remind = NewHuanHuaFetterWGData.Instance:GetFetterSmallTypeRemind(self.big_type, self.small_type)

		if small_remind then
			return self.small_type
		end
	end

    local default_select_small_type = 1

    local cell_data = NewHuanHuaFetterWGData.Instance:GetToggleShowBigTypeDataList(self.big_type)
    if not IsEmptyTable(cell_data) then
        for k, v in pairs(cell_data) do
            local small_remind = NewHuanHuaFetterWGData.Instance:GetFetterSmallTypeRemind(self.big_type, v.small_type)

            if small_remind then
                default_select_small_type = k
                break
            end
        end
    end

	return default_select_small_type
end

function NewHuanHuaFetterView:OnClickSmallTypeHandle(item, is_on)
    if nil == item or nil == item.data or not is_on then
		return 
	end

    local data = item.data
	self.small_type = data.small_type

    self:FlushMidInfo()
end

function NewHuanHuaFetterView:FlushMidInfo()
    -- 设置四个水晶数据
    local activation_data_list, big_reward_data = NewHuanHuaFetterWGData.Instance:GetActivationDataListCfgByType(self.big_type, self.small_type)
    for i = 1, 4 do
        self.huanhua_item_list[i]:SetData(activation_data_list[i])
    end

    self:FlushModel(big_reward_data)

    -- if not IsEmptyTable(big_reward_data) then
    --     local act_item_id = NewHuanHuaFetterWGData.Instance:GetActItemId(big_reward_data)
    --     local item_name = ItemWGData.Instance:GetItemName(act_item_id) or ""
    --     self.node_list.desc_big_reward_name.text.text = item_name
    -- end

    local theme_cfg = NewHuanHuaFetterWGData.Instance:GetThemeCfgByType(self.big_type, self.small_type)
    local is_get_reward = NewHuanHuaFetterWGData.Instance:IsGetRewardCfg(theme_cfg.reward_seq)
    local can_get_reward = NewHuanHuaFetterWGData.Instance:IsCanGetReward(theme_cfg.reward_seq)
    local active_num, complete_num = NewHuanHuaFetterWGData.Instance:GetHuanHuaFetterActiveNum(theme_cfg.seq)

    local need_active_data_list = NewHuanHuaFetterWGData.Instance:GetRewardCfgCacheBySeq(theme_cfg.reward_seq)

    local color = complete_num >= #need_active_data_list and COLOR3B.D_GREEN or COLOR3B.D_RED
    -- local progress_str = is_get_reward and "" or complete_num .. "/" .. #need_active_data_list
    self.node_list.desc_progress.text.text = is_get_reward and "" or string.format(Language.HuanHuaFetter.SuitActProgress, color, complete_num, COLOR3B.D_GREEN, #need_active_data_list)

    self.node_list.desc_progress_bg:CustomSetActive(not is_get_reward)
    self.node_list.btn_get_reward:CustomSetActive(not is_get_reward)
    self.node_list.btn_show_reward:CustomSetActive(is_get_reward)

    local suit_act_remind = NewHuanHuaFetterWGData.Instance:IsHasCanActiveSuitCfg(theme_cfg.seq)
    self.node_list.btn_get_reward_remind:CustomSetActive((not is_get_reward and can_get_reward) or suit_act_remind)
    self.node_list.btn_jbjx_remind:CustomSetActive(suit_act_remind)
end

function NewHuanHuaFetterView:FlushModel(model_data)
    if IsEmptyTable(model_data) then
        
        if  self.model_display then
            self.model_display:RemoveAllModel()
        end

        return
    end

    if not IsEmptyTable(self.model_show_cache) and self.model_show_cache.type == model_data.type and self.model_show_cache.param1 == model_data.param1 and self.model_show_cache.param2 == model_data.param2 then
        return 
    end

    -- 显示坐骑
    if model_data.type == WARDROBE_PART_TYPE.MOUNT then
        local act_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(model_data.param1)
        if act_cfg then
            local appe_id = act_cfg.appe_image_id or act_cfg.active_id
            local bundle, asset = ResPath.GetMountModel(appe_id)

            self.model_display:SetMainAsset(bundle, asset, function ()
                -- self.model_display:PlayMountAction()
            end)
        end
    elseif model_data.type == WARDROBE_PART_TYPE.FASHION then
        local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(model_data.param1, model_data.param2)

        if fashion_cfg then
            local res_id = fashion_cfg.resouce
            local bundle, asset
            -- 法宝
            if model_data.param1 == SHIZHUANG_TYPE.FABAO then
               bundle, asset = ResPath.GetFaBaoModel(res_id)
            elseif model_data.param1 == SHIZHUANG_TYPE.HALO then
                bundle, asset = ResPath.GetHaloModel(res_id)
            end
    
            self.model_display:SetMainAsset(bundle, asset)
        end
    end

    self.model_show_cache = model_data

    if model_data.model_root_pos and "" ~= model_data.model_root_pos then
        local pos = Split(model_data.model_root_pos, "|")
        RectTransform.SetAnchoredPosition3DXYZ(self.node_list.model_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
    end

    if model_data.model_pos and "" ~= model_data.model_pos then
        local pos = Split(model_data.model_pos, "|")
        self.model_display:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
    end

    if model_data.model_rot and "" ~= model_data.model_rot then
        local pos = Split(model_data.model_rot, "|")
        self.model_display:SetUSAdjustmentNodeLocalRotation(pos[1] or 0, pos[2] or 0, pos[3] or 0)
    end

    if model_data.model_scale and "" ~= model_data.model_scale then
        self.model_display:SetUSAdjustmentNodeLocalScale(model_data.model_scale)
    end
end

function NewHuanHuaFetterView:OnClickGetRewardBtn()
    local theme_cfg = NewHuanHuaFetterWGData.Instance:GetThemeCfgByType(self.big_type, self.small_type)
    local is_get_reward = NewHuanHuaFetterWGData.Instance:IsGetRewardCfg(theme_cfg.reward_seq)
    local can_get_reward = NewHuanHuaFetterWGData.Instance:IsCanGetReward(theme_cfg.reward_seq)

    if not is_get_reward and can_get_reward then
        local reward_cfg = NewHuanHuaFetterWGData.Instance:GetRewardCfgBySeq(theme_cfg.reward_seq)

        if reward_cfg and reward_cfg.seq then
            -- 播放动画
            local bundle, asset = ResPath.GetEffect("UI_huanse_levelup")
            EffectManager.Instance:PlayAtTransform(bundle, asset, self.node_list["effect_root"].transform, 2.5, nil,nil, nil, nil, function ()
                NewHuanHuaFetterWGCtrl.Instance:SendHuanHuaFetterRequest(HUANHUA_FETTER_OPERATE_TYPE.FETCH_REWARD, reward_cfg.seq)
            end)

            return
        end
    else
        self:OnClickJBJXBtn()
    end
end

function NewHuanHuaFetterView:OnClickJBJXBtn()
    local _, big_reward_data = NewHuanHuaFetterWGData.Instance:GetActivationDataListCfgByType(self.big_type, self.small_type)

    if not IsEmptyTable(big_reward_data) then
        NewHuanHuaFetterWGCtrl.Instance:OpenAttrActiveView(big_reward_data)
    end
end

function NewHuanHuaFetterView:OnClickShowRewardBtn()
    local _, big_reward_data = NewHuanHuaFetterWGData.Instance:GetActivationDataListCfgByType(self.big_type, self.small_type)
    if not IsEmptyTable(big_reward_data) then
        local act_item_id = NewHuanHuaFetterWGData.Instance:GetActItemId(big_reward_data)
    
        if act_item_id then
            TipWGCtrl.Instance:OpenItem({item_id = act_item_id})
        end
    end
end

--------------------------------------HuanHuaFetterMidItemCellRender---------------------------------------
HuanHuaFetterMidItemCellRender = HuanHuaFetterMidItemCellRender or BaseClass(BaseRender)

function HuanHuaFetterMidItemCellRender:LoadCallBack()
    XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClick, self))
end

function HuanHuaFetterMidItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    -- local act_item_id = NewHuanHuaFetterWGData.Instance:GetActItemId(self.data)
    -- local color = ItemWGData.Instance:GetItemColor(act_item_id)
    -- local item_name = ItemWGData.Instance:GetItemName(act_item_id) or ""
    -- self.node_list.name.text.text = item_name

    local state = NewHuanHuaFetterWGData.Instance:GetSuitPartStateByData(self.data.seq, self.data.part)
    local is_active = (state == REWARD_STATE_TYPE.FINISH or state == REWARD_STATE_TYPE.CAN_FETCH)
    self.node_list.effect:CustomSetActive(is_active)

    self.node_list.flag_lock:CustomSetActive(not is_active)
    -- self.node_list.active_icon:CustomSetActive(is_active)

    local bg_bundle, bg_asset = ResPath.GetRawImagesPNG(self.data.bg)
    self.node_list.bg.raw_image:LoadSprite(bg_bundle, bg_asset, function ()
        self.node_list.bg.raw_image:SetNativeSize()
    end)

    bg_bundle, bg_asset = ResPath.GetRawImagesPNG(self.data.icon)
    self.node_list.icon.raw_image:LoadSprite(bg_bundle, bg_asset, function ()
        self.node_list.icon.raw_image:SetNativeSize()
    end)

    bg_bundle, bg_asset = ResPath.GetRawImagesPNG(self.data.mask)
    self.node_list.mask.raw_image:LoadSprite(bg_bundle, bg_asset, function ()
        self.node_list.mask.raw_image:SetNativeSize()
    end)
end

function HuanHuaFetterMidItemCellRender:OnClick()
    local act_item_id = NewHuanHuaFetterWGData.Instance:GetActItemId(self.data)
    
    if act_item_id then
        TipWGCtrl.Instance:OpenItem({item_id = act_item_id})
    end
end

--------------------------------------HuanHuaFetterSmallTypeItemCellRender---------------------------------------
HuanHuaFetterSmallTypeItemCellRender = HuanHuaFetterSmallTypeItemCellRender or BaseClass(BaseRender)

function HuanHuaFetterSmallTypeItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        self:SetVisible(false)
        return
    end

    local is_can_show = NewHuanHuaFetterWGData.Instance:IsCanShowTheme(self.data.big_type, self.data.small_type)
    self:SetVisible(is_can_show)

    if not is_can_show then
        return
    end

    self.node_list.name.text.text = self.data.suit_name
    self.node_list.hl_name.text.text = self.data.suit_name

    local bundle, asset = ResPath.GetHuanHuaFetterImg(self.data.show_icon) 
    self.node_list.icon.image:LoadSprite(bundle, asset, function ()
        self.node_list.icon.image:SetNativeSize()
    end)

    local remind = NewHuanHuaFetterWGData.Instance:GetFetterSmallTypeRemind(self.data.big_type, self.data.small_type)
    self.node_list.remind:CustomSetActive(remind)

    -- local theme_cfg = NewHuanHuaFetterWGData.Instance:GetThemeCfgByType(self.data.big_type, self.data.small_type)
    -- local is_get_reward = NewHuanHuaFetterWGData.Instance:IsGetRewardCfg(theme_cfg.reward_seq)
    -- self.node_list.no_active:CustomSetActive(not is_get_reward)
end

-- function NewHuanHuaFetterView:__init()
--     self.default_index = TabIndex.huanhua_fetter_waiguan
--     self.view_style = ViewStyle.Full
--     self.is_safe_area_adapter = true

--     -- self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
--     self:AddViewResource(0, "uis/view/huanhua_fetter_ui_prefab", "layout_new_huanhua_fetter")
--     self:AddViewResource(0, "uis/view/huanhua_fetter_ui_prefab", "VerticalTabbar")
--     self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_light_common_top_panel")

--     self:SetTabShowUIScene(0, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.FETTERS_COLLECTION})

--     self.remind_tab = {
--         { RemindName.HuanHuaFetterZuoQi },
--         { RemindName.HuanHuaFetterLingQi },
--         { RemindName.HuanHuaFetterFaBao },
--     }

--     self.tab_index_list = {
--         [TabIndex.new_huanhua_fetter_zuoqi] = true,
--         [TabIndex.new_huanhua_fetter_lingqi] = true,
--         [TabIndex.new_huanhua_fetter_fabao] = true,
--     }

--     self.tab_sub = {}
-- end

-- function NewHuanHuaFetterView:LoadCallBack()
--     self.node_list.title_view_name.text.text = Language.HuanHuaFetter.TitleViewName
    
--     if not self.tabbar then
--         self.tabbar = Tabbar.New(self.node_list)
--         self.tabbar:SetVerTabbarIconStr("a3_jb_nav_")
--         self.tabbar:SetVerTabbarIconPath(ResPath.GetHuanHuaFetterImg)
--         self.tabbar:SetCreateVerCallBack(BindTool.Bind(self.CheckVerCellCanOpen, self))
--         self.tabbar:Init(Language.HuanHuaFetter.TabGrop, nil, "uis/view/huanhua_fetter_ui_prefab", nil, self.remind_tab)
--         self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
--         self.first_open = true
--     end

--     if not self.money_bar then
--         self.money_bar = MoneyBar.New()
--         local bundle, asset = ResPath.GetWidgets("MoneyBar")
--         local show_params = {
--             show_gold = true,
--             show_bind_gold = true,
--             show_coin = true,
--             show_silver_ticket = true,
--         }
--         self.money_bar:SetMoneyShowInfo(0, 0, show_params)
--         self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
--     end

--     if not self.fc_bom_list then
--         self.fc_bom_list = AsyncListView.New(HuanHuaFetterBigTypeItemCellRender, self.node_list.fc_bom_list)
--         self.fc_bom_list:SetSelectCallBack(BindTool.Bind(self.OnSelectFCBomHandler, self))
--     end

--     if not self.fc_item_list then
--         self.fc_item_list = {}

--         for i = 1, 5 do
--             self.fc_item_list[i] = HuanHuaFetterFCItemCellRender.New(self.node_list["fc_item_".. i])
--             self.fc_item_list[i]:SetIndex(i)
--         end
--     end

--     self.select_big_type_data = {}

--     -- if not self.model_list then
--     --     self.model_list = {}
--     --     for i = 1, 5 do
--     --         self.model_list[i] = RoleModel.New()

--     --         local display_data = {
--     --             parent_node = self.node_list["model" .. i],
--     --             camera_type = MODEL_CAMERA_TYPE.BASE,
--     --             -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
--     --             rt_scale_type = ModelRTSCaleType.L,
--     --             can_drag = false,
--     --         }
    
--     --         -- self.model_list[i]:SetUISceneModel(self.node_list["model" .. i].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
--     --         self.model_list[i]:SetRenderTexUI3DModel(display_data)
-- 	-- 		self:AddUiRoleModel(self.model_list[i])
--     --     end
--     -- end

--     if not self.model_list then
--         self.model_list = {}
--         for i = 1, 5 do
--             self.model_list[i] = RoleModel.New()

--             -- local display_data = {
--             --     parent_node = self.node_list["model" .. i],
--             --     camera_type = MODEL_CAMERA_TYPE.BASE,
--             --     -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
--             --     rt_scale_type = ModelRTSCaleType.L,
--             --     can_drag = false,
--             -- }
    
--             self.model_list[i]:SetUISceneModel(self.node_list["model" .. i].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
--             -- self.model_list[i]:SetRenderTexUI3DModel(display_data)
-- 			self:AddUiRoleModel(self.model_list[i])
--         end
--     end


--     -- local bundle, asset = ResPath.GetRawImagesPNG("a3_jb_bj")
-- 	-- if self.node_list.RawImage_tongyong then
-- 	-- 	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
-- 	-- 		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
-- 	-- 	end)
-- 	-- end

--     XUI.AddClickEventListener(self.node_list.btn_jbjx, BindTool.Bind(self.OnClickJBJXBtn, self))

--     self.model_show_cache = {}

--     if nil == self.accordion_list_equip then
-- 		self.accordion_list_equip = {}
-- 		self.cell_list = {}
        
--         local type_data_list = NewHuanHuaFetterWGData.Instance:GetShowTypeDataList()
        
--         for i = 1, 10, 1 do
--             local data = type_data_list[i]
--             local has_data = not IsEmptyTable(data)
            
--             self.node_list["SelectBtn" .. i]:CustomSetActive(has_data)
--             self.node_list["List" .. i]:CustomSetActive(has_data)

--             if has_data then
--                 self.node_list["normal_text" .. i].text.text = Language.HuanHuaFetter.TabGrop[data.big_type] or ""
--                 self.node_list["hl_text" .. i].text.text = Language.HuanHuaFetter.TabGrop[data.big_type] or ""
                
--                 self:LoadHuanHuaListCell(i, #type_data_list)
--                 self.node_list["SelectBtn" .. i].accordion_element:AddValueChangedListener(BindTool.Bind(self.OnClickBigTypeHandler, self, data.big_type))
--             end
--         end
--     end
-- end

-- function NewHuanHuaFetterView:ReleaseCallBack()
--     if self.tabbar then
--         self.tabbar:DeleteMe()
--         self.tabbar = nil
--     end

--     if self.money_bar then
--         self.money_bar:DeleteMe()
--         self.money_bar = nil
--     end

--     if self.fc_bom_list then
--         self.fc_bom_list:DeleteMe()
--         self.fc_bom_list = nil
--     end

--     if self.fc_item_list then
--         for k, v in pairs(self.fc_item_list) do
--             v:DeleteMe()
--         end
--         self.fc_item_list = nil
--     end

--     if self.model_list then
--         for k, v in pairs(self.model_list) do
--             v:DeleteMe()
--         end
--         self.model_list = nil
--     end

--     self.model_show_cache = nil
-- end

-- function NewHuanHuaFetterView:CheckVerCellCanOpen()
--     if self.tabbar then
--         for index, v in pairs(self.tab_index_list) do
--             local can_open = NewHuanHuaFetterWGData.Instance:IsCanShowVerTabIndex(index)
--             self.tabbar:SetVerToggleVisble(index, can_open)
--             self.tab_index_list[index] = can_open
--         end
--     end
-- end

-- function NewHuanHuaFetterView:CalcShowIndex()
--     local index = SafeBaseView.CalcShowIndex(self)
--     if self.tab_index_list[index] then
--         return index
--     end

--     for i = TabIndex.huanhua_fetter_zuoqi, TabIndex.huanhua_fetter_fabao, 10 do
--         if self.tab_index_list[i] then
--             return i
--         end
--     end
-- end

-- function NewHuanHuaFetterView:OnFlush(param_t, index)
--     for k, v in pairs(param_t) do
--         if self.tab_index_list[index] then
--             self:FlushBigTypeList()
--         end
--     end
-- end

-- function NewHuanHuaFetterView:OnSelectFCBomHandler(item)
--     if nil == item or IsEmptyTable(item.data) then
--         return
--     end

--     local data = item.data
--     self.select_big_type_data = data
--     self:FlushSmallTypeInfo()
-- end

-- function NewHuanHuaFetterView:FlushBigTypeList()
--     local target_data_list = NewHuanHuaFetterWGData.Instance:GetToggleShowBigTypeDataList(math.floor(self.show_index / 10))
--     self.fc_bom_list:SetDataList(target_data_list)
--     self.fc_bom_list:JumpToIndex(self:CalBigTypeSelect(target_data_list))
-- end

-- function NewHuanHuaFetterView:CalBigTypeSelect(target_data_list)
--     -- 跳转能够激活套装的标签
--     if not IsEmptyTable(target_data_list) then
--         for k, v in pairs(target_data_list) do
--            if NewHuanHuaFetterWGData.Instance:GetFetterSmallTypeRemind(v.big_type, v.small_type) then
--                 return k
--            end
--         end
--     end
--     return 1
-- end

-- function NewHuanHuaFetterView:FlushSmallTypeInfo()
--     local big_type = math.floor(self.show_index / 10)
--     local theme_cfg = NewHuanHuaFetterWGData.Instance:GetThemeCfgByType(big_type, self.select_big_type_data.small_type)
--     local suit_act_remind = NewHuanHuaFetterWGData.Instance:IsHasCanActiveSuitCfg(theme_cfg.seq)
--     self.node_list.btn_jbjx_remind:CustomSetActive(suit_act_remind)

--     -- 根据大小类型获得seq 转化
--     local small_type = self.select_big_type_data.small_type
--     local activation_data_list = NewHuanHuaFetterWGData.Instance:GetActivationDataListCfgByType(big_type, small_type)
--     for i = 1, 5 do
--         local target_data = activation_data_list[i - 1] or {}
--         self.fc_item_list[i]:SetData(target_data)
--         self:FlushMIdModel(i, target_data)

--         local reward_limit = false
--         if target_data.is_reward_item == 1 then
--             local theme_cfg = NewHuanHuaFetterWGData.Instance:GetThemeSeqCfgBySeq(target_data.seq)
--             local is_get_reward = false
    
--             if theme_cfg and theme_cfg.reward_seq then
--                 is_get_reward = NewHuanHuaFetterWGData.Instance:IsGetRewardCfg(theme_cfg.reward_seq)
--             end
    
--             reward_limit = not is_get_reward
--         end

--         self.node_list["fc_item_".. i]:CustomSetActive(not IsEmptyTable(target_data))
--         self.node_list["model" .. i]:CustomSetActive(not IsEmptyTable(target_data) and not reward_limit)
--     end
-- end

-- function NewHuanHuaFetterView:FlushMIdModel(index, data)
--     if IsEmptyTable(data) then
--         self.model_show_cache[index] = {}
        
--         if self.model_list[index] then
--             self.model_list[index]:RemoveAllModel()
--         end

--         return
--     end

--     local cache_data = self.model_show_cache[index]
--     if not IsEmptyTable(cache_data) and cache_data.type == data.type and cache_data.param1 == data.param1 and cache_data.param2 == data.param2 then
--         return
--     end

--     -- 显示坐骑
--     if data.type == WARDROBE_PART_TYPE.MOUNT then
--         local act_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(data.param1)
--         if act_cfg then
--             local appe_id = act_cfg.appe_image_id or act_cfg.active_id
--             local bundle, asset = ResPath.GetMountModel(appe_id)

--             self.model_list[index]:SetMainAsset(bundle, asset, function ()
--                 -- self.model_list[index]:PlayMountAction()
--             end)
--         end
--     elseif data.type == WARDROBE_PART_TYPE.FASHION then
--         local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)

--         if fashion_cfg then
--             local res_id = fashion_cfg.resouce
--             local bundle, asset
--             -- 法宝
--             if data.param1 == SHIZHUANG_TYPE.FABAO then
--                bundle, asset = ResPath.GetFaBaoModel(res_id)
--             elseif data.param1 == SHIZHUANG_TYPE.HALO then
--                 bundle, asset = ResPath.GetHaloModel(res_id)
--             end
    
--             self.model_list[index]:SetMainAsset(bundle, asset)
--         end
--     end

--     self.model_show_cache[index] = data

--     if data.model_root_pos and "" ~= data.model_root_pos then
--         local pos = Split(data.model_root_pos, "|")
--         RectTransform.SetAnchoredPosition3DXYZ(self.node_list["model".. index].rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
--     end

--     if data.model_pos and "" ~= data.model_pos then
--         local pos = Split(data.model_pos, "|")
--         -- self.model_list[index]:SetRTAdjustmentRootLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
--         self.model_list[index]:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
--     end

--     if data.model_rot and "" ~= data.model_rot then
--         local pos = Split(data.model_rot, "|")
--         -- self.model_list[index]:SetRTAdjustmentRootLocalRotation(pos[1] or 0, pos[2] or 0, pos[3] or 0)
--         self.model_list[index]:SetUSAdjustmentNodeLocalRotation(pos[1] or 0, pos[2] or 0, pos[3] or 0)
--     end

--     if data.model_scale and "" ~= data.model_scale then
--         -- self.model_list[index]:SetRTAdjustmentRootLocalScale(data.model_scale)
--         self.model_list[index]:SetUSAdjustmentNodeLocalScale(data.model_scale)
--     end
-- end

-- function NewHuanHuaFetterView:OnClickJBJXBtn()
--     if self.select_big_type_data then
--         local big_type = math.floor(self.show_index / 10)
--         local small_type = self.select_big_type_data.small_type
--         local activation_data_list = NewHuanHuaFetterWGData.Instance:GetActivationDataListCfgByType(big_type, small_type)
--         for i = 1, 5 do
--             local target_data = activation_data_list[i - 1] or {}
--             if target_data.is_reward_item == 1 then
--                 NewHuanHuaFetterWGCtrl.Instance:OpenAttrActiveView(target_data)
--                 return
--             end
--         end
--     end
-- end

-- -------------------------------------------HuanHuaFetterBigTypeItemCellRender------------------------------------------------
-- -- 大类型
-- HuanHuaFetterBigTypeItemCellRender = HuanHuaFetterBigTypeItemCellRender or BaseClass(BaseRender)

-- function HuanHuaFetterBigTypeItemCellRender:OnFlush()
--     if IsEmptyTable(self.data) then
--         return
--     end

--     local remind = NewHuanHuaFetterWGData.Instance:GetFetterSmallTypeRemind(self.data.big_type, self.data.small_type)
--     self.node_list.remind:CustomSetActive(remind)

--     local bundle, asset = ResPath.GetHuanHuaFetterImg(self.data.show_icon) 
--     self.node_list.icon.image:LoadSprite(bundle, asset)

--     self.node_list.name.text.text = self.data.suit_name
-- end

-- function HuanHuaFetterBigTypeItemCellRender:OnSelectChange(is_select)
--     self.node_list.act_bg:CustomSetActive(is_select)
--     self.node_list.lock_bg:CustomSetActive(not is_select)
-- end

-- -------------------------------------------HuanHuaFetterFCItemCellRender------------------------------------------------
-- -- 具体幻化物体
-- HuanHuaFetterFCItemCellRender = HuanHuaFetterFCItemCellRender or BaseClass(BaseRender)

-- function HuanHuaFetterFCItemCellRender:LoadCallBack()
--     XUI.AddClickEventListener(self.node_list.btn_huanhua, BindTool.Bind(self.OnClickHuanHuaBtn, self))
--     XUI.AddClickEventListener(self.node_list.btn_active, BindTool.Bind(self.OnClickActiveBtn, self))
--     XUI.AddClickEventListener(self.node_list.fc_item, BindTool.Bind(self.OnClickFCItemBtn, self))
--     XUI.AddClickEventListener(self.node_list.btn_act_item, BindTool.Bind(self.OnClickActItemBtn, self))
-- end

-- function HuanHuaFetterFCItemCellRender:OnFlush()
--     if IsEmptyTable(self.data) then
--         return
--     end

--     local act_item_id = NewHuanHuaFetterWGData.Instance:GetActItemId(self.data)
--     local color = ItemWGData.Instance:GetItemColor(act_item_id)
--     local item_name = ItemWGData.Instance:GetItemName(act_item_id) or ""
--     self.node_list.name.text.text = item_name -- ToColorStr(item_name, color)

--     local active = NewHuanHuaFetterWGData.Instance:GetSuitPartStateByData(self.data.seq, self.data.part)
--     local is_active = (active == REWARD_STATE_TYPE.FINISH or active == REWARD_STATE_TYPE.CAN_FETCH)
--     local is_huanhua = NewHuanHuaFetterWGData.Instance:IsActItemIsHuanHua(self.data)
--     local is_reward_item = self.data.is_reward_item == 1

--     if is_reward_item then
--         local theme_cfg = NewHuanHuaFetterWGData.Instance:GetThemeSeqCfgBySeq(self.data.seq)
--         local is_get_reward = false
--         local can_get_reward = false

--         if theme_cfg and theme_cfg.reward_seq then
--             is_get_reward = NewHuanHuaFetterWGData.Instance:IsGetRewardCfg(theme_cfg.reward_seq)
--             can_get_reward = NewHuanHuaFetterWGData.Instance:IsCanGetReward(theme_cfg.reward_seq)
--         end

--         self.node_list.btn_active_remind:CustomSetActive(not is_get_reward and can_get_reward)
--         if self.node_list.big_reward_bg then
--             -- self.node_list.big_reward_bg:CustomSetActive(not is_get_reward)
--             self.node_list.big_reward_bg:CustomSetActive(false)
--         end


--         -- 领取奖励按钮
--         self.node_list.btn_active:CustomSetActive(not is_get_reward)
--         -- self.node_list.btn_act_item:CustomSetActive(is_get_reward and not is_active)
--         -- self.node_list.btn_huanhua:CustomSetActive(is_active and not is_huanhua and is_get_reward)
--         -- self.node_list.flag_is_huanhua:CustomSetActive(is_active and is_huanhua and is_get_reward)
--         self.node_list.name_bg:CustomSetActive(is_get_reward)
--     else
--         -- self.node_list.btn_act_item:CustomSetActive(not is_active)
--         -- self.node_list.btn_huanhua:CustomSetActive(is_active and not is_huanhua)
--         -- self.node_list.flag_is_huanhua:CustomSetActive(is_active and is_huanhua)
--         self.node_list.name_bg:CustomSetActive(true)
--         self.node_list.btn_active:CustomSetActive(false)

--         if self.node_list.big_reward_bg then
--             self.node_list.big_reward_bg:CustomSetActive(false)
--         end
--     end
-- end

-- function HuanHuaFetterFCItemCellRender:OnClickHuanHuaBtn()
--     -- 坐骑幻化
--     if self.data.type == WARDROBE_PART_TYPE.MOUNT then
--         local cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(self.data.param1)
--         -- 使用
--         local act_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(self.data.param1)
--         if act_cfg then
--             local appe_id = act_cfg.appe_image_id or act_cfg.active_id
--             NewAppearanceWGCtrl.Instance:SendMountReq(MOUNT_OPERA_TYPE.USE_IMAGE, appe_id, act_cfg.image_id or 0)
--         end
--     elseif self.data.type == WARDROBE_PART_TYPE.FASHION then

--         NewAppearanceWGCtrl.Instance:OnUseFashion(self.data.param1, self.data.param2, 1)
--     end
-- end

-- function HuanHuaFetterFCItemCellRender:OnClickActiveBtn()
--     local is_reward_item = self.data.is_reward_item == 1

--     if is_reward_item then
--         local is_get_reward = false
--         local can_get_reward = false

--         local theme_cfg = NewHuanHuaFetterWGData.Instance:GetThemeSeqCfgBySeq(self.data.seq)
--         if theme_cfg and theme_cfg.reward_seq then
--             is_get_reward = NewHuanHuaFetterWGData.Instance:IsGetRewardCfg(theme_cfg.reward_seq)
--             can_get_reward = NewHuanHuaFetterWGData.Instance:IsCanGetReward(theme_cfg.reward_seq)
--         end

--         if not is_get_reward and can_get_reward then
--             local reward_cfg = NewHuanHuaFetterWGData.Instance:GetRewardCfgBySeq(theme_cfg.reward_seq)

--             if reward_cfg and reward_cfg.seq then
--                 NewHuanHuaFetterWGCtrl.Instance:SendHuanHuaFetterRequest(HUANHUA_FETTER_OPERATE_TYPE.FETCH_REWARD, reward_cfg.seq)
--                 return
--             end
--         end
--     end

--     self:OnClickFCItemBtn()
--     -- local act_item_id = NewHuanHuaFetterWGData.Instance:GetActItemId(self.data)
    
--     -- if act_item_id then
--     --     TipWGCtrl.Instance:OpenItem({item_id = act_item_id})
--     -- end
-- end

-- function HuanHuaFetterFCItemCellRender:OnClickFCItemBtn()
--     if self.data.is_reward_item == 1 then
--         NewHuanHuaFetterWGCtrl.Instance:OpenAttrActiveView(self.data)
--     else
--         local act_item_id = NewHuanHuaFetterWGData.Instance:GetActItemId(self.data)
    
--         if act_item_id then
--             TipWGCtrl.Instance:OpenItem({item_id = act_item_id})
--         end
--     end
-- end

-- function HuanHuaFetterFCItemCellRender:OnClickActItemBtn()
--    if self.data.act_jump_path and ""~= self.data.act_jump_path then
--         FunOpen.Instance:OpenViewNameByCfg(self.data.act_jump_path)
--    end
-- end


