-- 此界面作废
function MarryView:InitZhView()
    self.is_auto_up_grade = false

    if not self.attr_list then
        self.attr_list = {}
        for i = 1, 5 do
            local attr_render = CommonAddAttrRender.New(self.node_list["attr_" .. i])
            self.attr_list[i] = attr_render
            self.attr_list[i]:SetIndex(i)
        end
    end

    if not self.show_item_list then
        self.show_item_list = AsyncListView.New(SendFlowerUpGradeRender, self.node_list.show_item_list)
    end

    XUI.AddClickEventListener(self.node_list.meili_upgrade_btn, BindTool.Bind(self.OnUpGradeBtn, self))
    XUI.AddClickEventListener(self.node_list.goto_btn, BindTool.Bind(self.OnClickGotoBtn, self))
end

function MarryView:DeleteFlowersView()
    if self.attr_list then
        for i = 1, 5 do
            self.attr_list[i]:DeleteMe()
            self.attr_list[i] = nil
        end
        self.attr_list = nil
    end

    if self.show_item_list then
        self.show_item_list:DeleteMe()
        self.show_item_list = nil
    end
end

function MarryView:FlushFlowersView(flush_key)
    self.node_list.upgrade_effect:SetActive(false)

    self:FlushFlowerItemList()
    local meili_level = MarryWGData.Instance:GetFlowerScoreUpgradeLevel()
    local flower_num = MarryWGData.Instance:GetSendFlowerNum()
    local flower_cfg = MarryWGData.Instance:GetSendFlowersUpgradeByLevel(meili_level)

    self.node_list.des_text.text.text = Language.Marry.FlowersDes
    self.node_list.meili_level_text.text.text = string.format(Language.Common.Level, meili_level)

    if flower_cfg then
        local next_flower_cfg = MarryWGData.Instance:GetSendFlowersUpgradeByLevel(meili_level + 1)
        local attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(flower_cfg, next_flower_cfg, "attr_id", "attr_value", 1, 5)
        local need_num = next_flower_cfg and next_flower_cfg.need_num or flower_cfg.need_num
        
        local need_show_effect = false
        if nil ~= self.meili_level_cache and meili_level > 0 and (meili_level > self.meili_level_cache) then
            need_show_effect = true
        end

        self.meili_level_cache = meili_level
        
        for i = 1, #self.attr_list do
            self.attr_list[i]:SetData(attr_list[i])

            if need_show_effect then
                self.attr_list[i]:PlayAttrValueUpEffect()
            end
        end

        local slider_value = flower_num / need_num
        slider_value = slider_value > 1 and 1 or slider_value
        local is_max_level = meili_level == MarryWGData.Instance:GetSendFlowersUpgradeMaxLevel()

        self.node_list.meili_num.text.text = string.format(Language.Flower.Expend, flower_num, need_num)
        self.node_list.upgrade_flag:SetActive(flower_num >= need_num and not is_max_level)
        self.node_list.upgrade_effect:SetActive(flower_num >= need_num and not is_max_level)
        self.node_list.max_level_flag:SetActive(is_max_level)
        self.node_list.meili_upgrade_btn.button.interactable = not is_max_level

        if "send_flower_level_change" == flush_key then
            self.node_list.meili_slider.slider:DOValue(0, 0.5):OnComplete(function()
                if self.node_list.meili_slider then
                    self.node_list.meili_slider.slider:DOValue(slider_value, 0.8):OnComplete(function()
                        self:AutoUpGrade()
                    end)
                end
            end)
        elseif "send_flower_num_change" == flush_key then
            self.node_list.meili_slider.slider:DOValue(slider_value, 1)
        else
            self.node_list.meili_slider.slider.value = slider_value
        end
    end
end

function MarryView:FlushFlowerItemList()
    local show_item_cfg = MarryWGData.Instance:GetSendFlowersItemList()
    for i = 1, #show_item_cfg do
        local item_data = show_item_cfg[i]
        item_data.num = ItemWGData.Instance:GetItemNumInBagById(item_data.item_id)
    end

    self.show_item_list:SetDataList(show_item_cfg)
end

function MarryView:CloseAutoUpGrade()
    self.is_auto_up_grade = false
end

function MarryView:OnUpGradeBtn()
    self.is_auto_up_grade = not self.is_auto_up_grade
    self:AutoUpGrade()
end

function MarryView:AutoUpGrade()
    if self.is_auto_up_grade then
        local flower_num = MarryWGData.Instance:GetSendFlowerNum()
        local flower_cfg = MarryWGData.Instance:GetSendFlowersUpgradeByLevel(MarryWGData.Instance:GetFlowerScoreUpgradeLevel() + 1)

        if flower_cfg and flower_num >= flower_cfg.need_num then
            MarryWGCtrl.Instance:SendCSUpgradeFlowerScoreLevel()
        else
            self:CloseAutoUpGrade()
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.SendFlowerRemind)
        end
    end
end

function MarryView:OnClickGotoBtn()
    FlowerWGCtrl.Instance:Open()
end

----------------
SendFlowerUpGradeRender = SendFlowerUpGradeRender or BaseClass(BaseRender)

function SendFlowerUpGradeRender:LoadCallBack()
    self.show_item = ItemCell.New(self.node_list.item_pos)
    self.show_item:SetHideRightDownBgLessNum(-1)
end

function SendFlowerUpGradeRender:ReleaseCallBack()
    if self.show_item then
        self.show_item:DeleteMe()
        self.show_item = nil
    end
end

function SendFlowerUpGradeRender:OnFlush()
    if not self.data then
        return
    end

    self.show_item:SetData(self.data)
end