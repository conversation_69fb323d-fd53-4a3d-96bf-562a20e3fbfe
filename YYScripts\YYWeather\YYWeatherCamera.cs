﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class YYWeatherCamera : MonoBehaviour {

    private Camera camera;


    private void Awake()
    {
        camera = GetComponent<Camera>();
       //camera.depthTextureMode = DepthTextureMode.Depth
    }


    private void OnPreCull()
    {
        //Debug.Log("OnPreCull");
        //if (YYCommandBufferManager.Instance != null && YYCommandBufferManager.cameraEnable == true)
        if(YYCommandBufferManager.cameraEnable == true)
        {
            YYCommandBufferManager.Instance.CameraPreCull(camera);

            //Debug.Log(" OnPreCull");
        }
        else
        {
            //Debug.Log("not OnPreCull");
        }
    }

    private void OnPreRender()
    {
        //Debug.Log("OnPreRender");
        //if (YYCommandBufferManager.Instance != null && YYCommandBufferManager.Instance.cameraEnable == true)
        if (YYCommandBufferManager.cameraEnable == true)
            YYCommandBufferManager.Instance.CameraPreRender(camera);
    }

    private void OnPostRender()
    {
        //Debug.Log("OnPostRender");
        //if (YYCommandBufferManager.Instance != null && YYCommandBufferManager.Instance.cameraEnable == true)
        if (YYCommandBufferManager.cameraEnable == true)
            YYCommandBufferManager.Instance.CameraPostRender(camera);
    }



}
