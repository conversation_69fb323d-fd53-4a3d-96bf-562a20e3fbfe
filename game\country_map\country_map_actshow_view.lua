-- 入口对应红点
local REMIND_LIST = {
    [1] = "",
    [2] = "",
    [3] = "",
    [4] = "",
    [5] = RemindName.CrossLongMaiTotal,                     -- 龙脉
}

function CountryMapMapView:ActShowViewLoadIndexCallBack()
    if not self.map_actshow_list then
        self.map_actshow_list = {}
        self.map_actshow_actid_list = {}

        local act_cfg = CountryMapActShowWgData.Instance:GetActShowActCfg()
        for k, v in pairs(act_cfg) do
            self.map_actshow_list[k] = MapActShowItemRender.New(self.node_list["map_act" .. k])
            self.map_actshow_list[k]:SetData(v)
            self.map_actshow_actid_list[v.act_id] = k
        end
    end

    self.act_show_remind_cache = {}
    self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)

    self.act_show_remind_callback = BindTool.Bind(self.OnActShowRemindChange, self)
	for k,v in pairs(REMIND_LIST) do
        if v ~= "" then
            self.act_show_remind_cache[v] = k
            RemindManager.Instance:Bind(self.act_show_remind_callback, v)
        end
	end
end

function CountryMapMapView:ActShowViewReleaseCallBack()
    if self.map_actshow_list then
        for k, v in pairs(self.map_actshow_list) do
            v:DeleteMe()
        end

        self.map_actshow_list = nil 
    end

    if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
		self.activity_change_callback = nil
	end

    if self.act_show_remind_callback then
        RemindManager.Instance:UnBind(self.act_show_remind_callback)
    end

    self.act_show_remind_cache = nil
end

function CountryMapMapView:ActShowViewShowIndexCallBack()
end

function CountryMapMapView:ActShowViewOnFlush(param_t)
    if self.map_actshow_list then
        for k, v in pairs(self.map_actshow_list) do
            v:Flush()
        end
    end

    self:FlushActShowViewRemind()
end

function CountryMapMapView:FlushActShowViewRemind()
    for k, v in pairs(REMIND_LIST) do
        if v ~= "" then
            if self.map_actshow_list[k] then
                local remind = RemindManager.Instance:GetRemind(v)
                self.map_actshow_list[k]:FlushRemind(remind > 0)
            end
        end
    end
end

function CountryMapMapView:OnActShowRemindChange(remind_name, num)
    local remind_id = self.act_show_remind_cache[remind_name]

    if remind_id then
        if self.map_actshow_list[remind_id] then
            self.map_actshow_list[remind_id]:FlushRemind(num > 0)
        end
    end
end

function CountryMapMapView:ActivityChangeCallBack(activity_type, status, next_time, open_type)
    if activity_type > 0 then
        if self.map_actshow_actid_list and self.map_actshow_actid_list[activity_type] then
            local index = self.map_actshow_actid_list[activity_type]

            if self.map_actshow_list[index] then
                self.map_actshow_list[index]:Flush()
            end
        end
    end
end

MapActShowItemRender = MapActShowItemRender or BaseClass(BaseRender)

function MapActShowItemRender:LoadCallBack()
    XUI.AddClickEventListener(self.view, BindTool.Bind(self.ClickHandler, self))
end

function MapActShowItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local is_reside = self.data.is_reside == 1
    local fun_open, limit_reason = FunOpen.Instance:GetFunIsOpened(self.data.fun_name, true)

    local is_run_now = is_reside
    local act_id = self.data.act_id
    local nned_show_time_flag = fun_open and act_id > 0
    if act_id > 0 then
        local act_is_open = ActivityWGData.Instance:GetActivityIsOpen(act_id)
        is_run_now = act_is_open
    end

    self.node_list.flag_act_run:CustomSetActive(is_run_now and (self.data.is_lock == 0) and fun_open)
    self.node_list.desc_act_name.text.text = self.data.act_name
    self.node_list.flag_act_lock:CustomSetActive(not fun_open or self.data.is_lock == 1)


    self.node_list.desc_open_condition.text.text = self.data.is_lock == 1 and Language.Common.JingQingQiDai or limit_reason
    self.node_list.week_day_bg:CustomSetActive(nned_show_time_flag)
    
    if nned_show_time_flag then
        local week_desc, time_desc = CountryMapActShowWgData.Instance:GetActOpenTimeByActType(self.data.act_id, self.data.index)
        self.node_list.desc_act_open_weekday.text.text = week_desc
        self.node_list.desc_act_opentime.text.text = time_desc
    end
end

function MapActShowItemRender:ClickHandler()
    if IsEmptyTable(self.data) then
        return
    end

    if self.data.is_lock == 1 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.JingQingQiDai)
        return
    end

    local is_reside = self.data.is_reside == 1
    local fun_open, limit_reason = FunOpen.Instance:GetFunIsOpened(self.data.fun_name, true)

    if is_reside or fun_open then
        ViewManager.Instance:Open(self.data.open_view)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(limit_reason)
    end
end

function MapActShowItemRender:FlushRemind(remind)
    self.node_list.remind:CustomSetActive(remind)
end