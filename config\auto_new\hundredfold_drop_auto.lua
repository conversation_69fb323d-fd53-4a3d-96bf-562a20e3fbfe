-- B-百倍爆装.xls
local item_table={
[1]={item_id=48071,num=1,is_bind=1},
[2]={item_id=22099,num=300,is_bind=1},
[3]={item_id=22615,num=3,is_bind=1},
[4]={item_id=46041,num=3,is_bind=1},
[5]={item_id=39159,num=3,is_bind=1},
[6]={item_id=48071,num=3,is_bind=1},
[7]={item_id=22099,num=1280,is_bind=1},
[8]={item_id=22615,num=5,is_bind=1},
[9]={item_id=46041,num=5,is_bind=1},
[10]={item_id=39159,num=5,is_bind=1},
[11]={item_id=48071,num=5,is_bind=1},
[12]={item_id=22099,num=3280,is_bind=1},
[13]={item_id=22615,num=10,is_bind=1},
[14]={item_id=46041,num=10,is_bind=1},
[15]={item_id=39159,num=10,is_bind=1},
[16]={item_id=26565,num=1,is_bind=1},
[17]={item_id=22099,num=10000,is_bind=1},
[18]={item_id=22615,num=20,is_bind=1},
[19]={item_id=46041,num=20,is_bind=1},
[20]={item_id=39159,num=20,is_bind=1},
[21]={item_id=37438,num=1,is_bind=1},
[22]={item_id=37031,num=1,is_bind=1},
[23]={item_id=22099,num=20000,is_bind=1},
[24]={item_id=22615,num=50,is_bind=1},
[25]={item_id=46041,num=50,is_bind=1},
[26]={item_id=39159,num=50,is_bind=1},
[27]={item_id=22099,num=10,is_bind=1},
[28]={item_id=22014,num=200,is_bind=1},
[29]={item_id=23628,num=500,is_bind=1},
[30]={item_id=38820,num=10,is_bind=1},
[31]={item_id=22099,num=15,is_bind=1},
[32]={item_id=22014,num=400,is_bind=1},
[33]={item_id=57840,num=4,is_bind=1},
[34]={item_id=57834,num=1,is_bind=1},
[35]={item_id=26369,num=1,is_bind=1},
[36]={item_id=26367,num=4,is_bind=1},
[37]={item_id=57840,num=3,is_bind=1},
[38]={item_id=26367,num=3,is_bind=1},
[39]={item_id=57840,num=2,is_bind=1},
[40]={item_id=38938,num=1,is_bind=1},
[41]={item_id=26558,num=1,is_bind=1},
[42]={item_id=22099,num=6480,is_bind=1},
[43]={item_id=22615,num=15,is_bind=1},
[44]={item_id=46041,num=15,is_bind=1},
[45]={item_id=39159,num=15,is_bind=1},
[46]={item_id=29530,num=1,is_bind=1},
[47]={item_id=57840,num=1,is_bind=1},
[48]={item_id=37901,num=1,is_bind=1},
[49]={item_id=37904,num=1,is_bind=1},
[50]={item_id=37906,num=1,is_bind=1},
[51]={item_id=37804,num=1,is_bind=1},
[52]={item_id=37807,num=1,is_bind=1},
[53]={item_id=22099,num=1,is_bind=1},
[54]={item_id=40223,num=1,is_bind=1},
[55]={item_id=40224,num=1,is_bind=1},
[56]={item_id=40225,num=1,is_bind=1},
[57]={item_id=40226,num=1,is_bind=1},
[58]={item_id=40043,num=1,is_bind=1},
[59]={item_id=40044,num=1,is_bind=1},
[60]={item_id=40045,num=1,is_bind=1},
[61]={item_id=40039,num=1,is_bind=1},
[62]={item_id=40040,num=1,is_bind=1},
[63]={item_id=39784,num=1,is_bind=1},
[64]={item_id=39785,num=1,is_bind=1},
[65]={item_id=39934,num=1,is_bind=1},
[66]={item_id=40006,num=1,is_bind=1},
[67]={item_id=39786,num=1,is_bind=1},
[68]={item_id=26367,num=1,is_bind=1},
[69]={item_id=26368,num=1,is_bind=1},
[70]={item_id=26378,num=1,is_bind=1},
[71]={item_id=26379,num=1,is_bind=1},
[72]={item_id=26380,num=1,is_bind=1},
[73]={item_id=26355,num=1,is_bind=1},
[74]={item_id=26356,num=1,is_bind=1},
[75]={item_id=26357,num=1,is_bind=1},
[76]={item_id=22530,num=1,is_bind=1},
[77]={item_id=22531,num=1,is_bind=1},
[78]={item_id=22532,num=1,is_bind=1},
[79]={item_id=26200,num=1,is_bind=1},
[80]={item_id=26203,num=1,is_bind=1},
[81]={item_id=26415,num=1,is_bind=1},
[82]={item_id=22012,num=1,is_bind=1},
[83]={item_id=39478,num=1,is_bind=1},
[84]={item_id=26099,num=1,is_bind=1},
[85]={item_id=26415,num=5,is_bind=1},
[86]={item_id=22099,num=20,is_bind=1},
[87]={item_id=57840,num=5,is_bind=1},
[88]={item_id=26367,num=5,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
level={
{drop_times_list="77,100|93,100|75,100|94,100",drop_times_list_show_1="26195:1:0",},
{level=1,exp_per_times_limit=10,drop_times_list_show="77,200|93,200|75,200|94,200",drop_times_list_show_1="26195:1:0",},
{level=2,exp_per_times_limit=20,drop_times_list_show="77,300|93,300|75,300|94,300",drop_times_list_show_1="26195:1:0",},
{level=3,exp_per_times_limit=30,drop_times_list_show="77,400|93,400|75,400|94,400",map_type="灵妖奇脉",},
{level=4,exp_per_times_limit=50,drop_times_list_show="77,500|93,500|75,500|94,500",map_type="天峰夺宝",},
{level=5,exp_per_times_limit=80,drop_times_list="77,300|93,300|75,300|94,300",drop_times_list_show="77,600|93,600|75,600|94,600",added_drop_times_list_show="91142:1:300,91641:1:300,26195:1:0,37901:1:0",drop_times_list_show_1="26195:1:0,37901:1:0",},
{level=6,exp_per_times_limit=120,drop_times_list="77,300|93,300|75,300|94,300",drop_times_list_show="77,700|93,700|75,700|94,700",map_type="灵妖奇脉",drop_times_list_show_1="26195:1:0,37901:1:0",},
{level=7,exp_per_times_limit=170,drop_times_list_show="77,800|93,800|75,800|94,800",map_type="天峰夺宝",},
{level=8,exp_per_times_limit=230,drop_times_list="77,400|93,400|75,400|94,400",drop_times_list_show="77,900|93,900|75,900|94,900",map_type="伏魔战场",drop_times_list_show_1="26195:1:0,37901:1:0",},
{level=9,exp_per_times_limit=300,drop_times_list="77,500|93,500|75,500|94,500",drop_times_list_show="77,1000|93,1000|75,1000|94,1000",drop_times_list_show_1="26195:1:0,37901:1:0",},
{level=10,exp_per_times_limit=380,drop_times_list="77,500|93,500|75,500|94,500",drop_times_list_show="77,1100|93,1100|75,1100|94,1100",added_drop_times_list_show="91142:1:500,91641:1:500,26195:1:0,26219:1:0,37901:1:0",map_type="灵妖奇脉",drop_times_list_show_1="26195:1:0,26219:1:0,37901:1:0",},
{level=11,exp_per_times_limit=470,drop_times_list_show="77,1200|93,1200|75,1200|94,1200",map_type="天峰夺宝",},
{level=12,exp_per_times_limit=570,drop_times_list="77,600|93,600|75,600|94,600",drop_times_list_show="77,1300|93,1300|75,1300|94,1300",map_type="伏魔战场",drop_times_list_show_1="26195:1:0,26219:1:0,37901:1:0",},
{level=13,exp_per_times_limit=680,drop_times_list="77,700|93,700|75,700|94,700",drop_times_list_show="77,1400|93,1400|75,1400|94,1400",drop_times_list_show_1="26195:1:0,26219:1:0,37901:1:0",},
{level=14,exp_per_times_limit=800,drop_times_list_show="77,1500|93,1500|75,1500|94,1500",map_type="灵妖奇脉",},
{level=15,exp_per_times_limit=930,drop_times_list="77,800|93,800|75,800|94,800",drop_times_list_show="77,1600|93,1600|75,1600|94,1600",added_drop_times_list_show="91142:1:800,91641:1:800,26195:1:0,26219:1:0,37901:1:0,37904:1:0",map_type="天峰夺宝",drop_times_list_show_1="26195:1:0,26219:1:0,37901:1:0,37904:1:0",},
{level=16,exp_per_times_limit=1070,drop_times_list="77,800|93,800|75,800|94,800",drop_times_list_show="77,1700|93,1700|75,1700|94,1700",map_type="伏魔战场",drop_times_list_show_1="26195:1:0,26219:1:0,37901:1:0,37904:1:0",},
{level=17,exp_per_times_limit=1220,drop_times_list="77,900|93,900|75,900|94,900",drop_times_list_show="77,1800|93,1800|75,1800|94,1800",drop_times_list_show_1="26195:1:0,26219:1:0,37901:1:0,37904:1:0",},
{level=18,exp_per_times_limit=1380,drop_times_list_show="77,1900|93,1900|75,1900|94,1900",map_type="灵妖奇脉",},
{level=19,exp_per_times_limit=1550,drop_times_list="77,1000|93,1000|75,1000|94,1000",drop_times_list_show="77,2000|93,2000|75,2000|94,2000",map_type="天峰夺宝",drop_times_list_show_1="26195:1:0,26219:1:0,37901:1:0,37904:1:0",},
{level=20,exp_per_times_limit=1730,drop_times_list="77,1000|93,1000|75,1000|94,1000",drop_times_list_show="77,2100|93,2100|75,2100|94,2100",added_drop_times_list_show="91142:1:1000,91641:1:1000,26195:1:0,26219:1:0,26220:1:0,37901:1:0,37904:1:0",map_type="伏魔战场",drop_times_list_show_1="26195:1:0,26219:1:0,26220:1:0,37901:1:0,37904:1:0",},
{level=21,exp_per_times_limit=1920,drop_times_list="77,1100|93,1100|75,1100|94,1100",drop_times_list_show="77,2200|93,2200|75,2200|94,2200",drop_times_list_show_1="26195:1:0,26219:1:0,26220:1:0,37901:1:0,37904:1:0",},
{level=22,exp_per_times_limit=2120,drop_times_list_show="77,2300|93,2300|75,2300|94,2300",map_type="灵妖奇脉",},
{level=23,exp_per_times_limit=2330,drop_times_list_show="77,2400|93,2400|75,2400|94,2400",map_type="天峰夺宝",},
{level=24,exp_per_times_limit=2550,drop_times_list="77,1200|93,1200|75,1200|94,1200",drop_times_list_show="77,2500|93,2500|75,2500|94,2500",map_type="伏魔战场",drop_times_list_show_1="26195:1:0,26219:1:0,26220:1:0,37901:1:0,37904:1:0",},
{level=25,exp_per_times_limit=2790,drop_times_list="77,1300|93,1300|75,1300|94,1300",drop_times_list_show="77,2600|93,2600|75,2600|94,2600",added_drop_times_list_show="91142:1:1300,91641:1:1300,26195:1:0,26219:1:0,26220:1:0,37901:1:0,37904:1:0,37906:1:0",drop_times_list_show_1="26195:1:0,26219:1:0,26220:1:0,37901:1:0,37904:1:0,37906:1:0",},
{level=26,exp_per_times_limit=3050,drop_times_list="77,1300|93,1300|75,1300|94,1300",drop_times_list_show="77,2700|93,2700|75,2700|94,2700",map_type="灵妖奇脉",drop_times_list_show_1="26195:1:0,26219:1:0,26220:1:0,37901:1:0,37904:1:0,37906:1:0",},
{level=27,exp_per_times_limit=3330,drop_times_list="77,1400|93,1400|75,1400|94,1400",drop_times_list_show="77,2800|93,2800|75,2800|94,2800",map_type="天峰夺宝",drop_times_list_show_1="26195:1:0,26219:1:0,26220:1:0,37901:1:0,37904:1:0,37906:1:0",},
{level=28,exp_per_times_limit=3630,drop_times_list_show="77,2900|93,2900|75,2900|94,2900",map_type="伏魔战场",},
{level=29,exp_per_times_limit=3950,drop_times_list="77,1500|93,1500|75,1500|94,1500",drop_times_list_show="77,3000|93,3000|75,3000|94,3000",drop_times_list_show_1="26195:1:0,26219:1:0,26220:1:0,37901:1:0,37904:1:0,37906:1:0",},
{level=30,exp_per_times_limit=4290,drop_times_list="77,1500|93,1500|75,1500|94,1500",drop_times_list_show="77,3100|93,3100|75,3100|94,3100",added_drop_times_list_show="91142:1:1500,91641:1:1500,26195:1:0,26219:1:0,26220:1:0,37901:1:0,37904:1:0,37906:1:0,37804:1:0",map_type="灵妖奇脉",drop_times_list_show_1="26195:1:0,26219:1:0,26220:1:0,37901:1:0,37904:1:0,37906:1:0,37804:1:0",},
{level=31,exp_per_times_limit=4650,drop_times_list="77,1600|93,1600|75,1600|94,1600",drop_times_list_show="77,3200|93,3200|75,3200|94,3200",map_type="天峰夺宝",drop_times_list_show_1="26195:1:0,26219:1:0,26220:1:0,37901:1:0,37904:1:0,37906:1:0,37804:1:0",},
{level=32,exp_per_times_limit=5030,drop_times_list_show="77,3300|93,3300|75,3300|94,3300",map_type="伏魔战场",},
{level=33,exp_per_times_limit=5430,drop_times_list="77,1700|93,1700|75,1700|94,1700",drop_times_list_show="77,3400|93,3400|75,3400|94,3400",drop_times_list_show_1="26195:1:0,26219:1:0,26220:1:0,37901:1:0,37904:1:0,37906:1:0,37804:1:0",},
{level=34,exp_per_times_limit=5880,drop_times_list_show="77,3500|93,3500|75,3500|94,3500",map_type="灵妖奇脉",},
{level=35,exp_per_times_limit=6380,drop_times_list="77,1800|93,1800|75,1800|94,1800",drop_times_list_show="77,3600|93,3600|75,3600|94,3600",added_drop_times_list_show="91142:1:1800,91641:1:1800,26195:1:0,26219:1:0,26220:1:0,37901:1:0,37904:1:0,37906:1:0,37804:1:0,37807:1:0",map_type="天峰夺宝",},
{level=36,exp_per_times_limit=6930,drop_times_list="77,1800|93,1800|75,1800|94,1800",drop_times_list_show="77,3700|93,3700|75,3700|94,3700",},
{level=37,exp_per_times_limit=7530,drop_times_list="77,1900|93,1900|75,1900|94,1900",drop_times_list_show="77,3800|93,3800|75,3800|94,3800",},
{level=38,exp_per_times_limit=8180,drop_times_list_show="77,3900|93,3900|75,3900|94,3900",map_type="灵妖奇脉",},
{level=39,exp_per_times_limit=8880,drop_times_list="77,2000|93,2000|75,2000|94,2000",drop_times_list_show="77,4000|93,4000|75,4000|94,4000",map_type="天峰夺宝",},
{level=40,exp_per_times_limit=9660,drop_times_list="77,2000|93,2000|75,2000|94,2000",drop_times_list_show="77,4100|93,4100|75,4100|94,4100",added_drop_times_list_show="91142:1:2000,91641:1:2000,26195:1:0,26219:1:0,26220:1:0,37901:1:0,37904:1:0,37906:1:0,37804:1:0,37807:1:0",map_type="伏魔战场",},
{level=41,exp_per_times_limit=10520,drop_times_list="77,2100|93,2100|75,2100|94,2100",drop_times_list_show="77,4200|93,4200|75,4200|94,4200",},
{level=42,exp_per_times_limit=11460,drop_times_list_show="77,4300|93,4300|75,4300|94,4300",map_type="灵妖奇脉",},
{level=43,exp_per_times_limit=12480,drop_times_list="77,2200|93,2200|75,2200|94,2200",drop_times_list_show="77,4400|93,4400|75,4400|94,4400",},
{level=44,exp_per_times_limit=13600,drop_times_list="77,2200|93,2200|75,2200|94,2200",drop_times_list_show="77,4500|93,4500|75,4500|94,4500",},
{level=45,exp_per_times_limit=14820,drop_times_list="77,2300|93,2300|75,2300|94,2300",drop_times_list_show="77,4600|93,4600|75,4600|94,4600",added_drop_times_list_show="91142:1:2300,91641:1:2300,26195:1:0,26219:1:0,26220:1:0,37901:1:0,37904:1:0,37906:1:0,37804:1:0,37807:1:0",},
{level=46,exp_per_times_limit=16140,drop_times_list="77,2300|93,2300|75,2300|94,2300",drop_times_list_show="77,4700|93,4700|75,4700|94,4700",},
{level=47,exp_per_times_limit=17560,drop_times_list="77,2400|93,2400|75,2400|94,2400",drop_times_list_show="77,4800|93,4800|75,4800|94,4800",},
{level=48,exp_per_times_limit=19080,drop_times_list="77,2400|93,2400|75,2400|94,2400",drop_times_list_show="77,4900|93,4900|75,4900|94,4900",map_type="伏魔战场",},
{level=49,exp_per_times_limit=20700,drop_times_list="77,2500|93,2500|75,2500|94,2500",drop_times_list_show="77,5000|93,5000|75,5000|94,5000",}
},

level_meta_table_map={
[5]=2,	-- depth:1
[4]=2,	-- depth:1
[37]=49,	-- depth:1
[39]=38,	-- depth:1
[43]=42,	-- depth:1
[44]=40,	-- depth:1
[45]=49,	-- depth:1
[47]=43,	-- depth:2
[48]=40,	-- depth:1
[35]=34,	-- depth:1
[33]=32,	-- depth:1
[24]=25,	-- depth:1
[23]=22,	-- depth:1
[19]=18,	-- depth:1
[29]=28,	-- depth:1
[15]=14,	-- depth:1
[12]=13,	-- depth:1
[8]=9,	-- depth:1
},
drop_times_target={
{}
},

drop_times_target_meta_table_map={
},
rmb_buy={
{reward={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},attr_value1=372,attr_value2=1241,attr_value3=62,attr_value4=41,},
{level=2,rmb_seq=1,price=18,reward={[0]=item_table[6],[1]=item_table[7],[2]=item_table[8],[3]=item_table[9],[4]=item_table[10]},drop_time_add=20,attr_value1=1588,attr_value2=5296,attr_value3=264,attr_value4=176,drop_time_add_show=20,},
{level=3,rmb_seq=2,price=28,reward={[0]=item_table[11],[1]=item_table[12],[2]=item_table[13],[3]=item_table[14],[4]=item_table[15]},drop_time_add=30,attr_value1=4071,attr_value2=13572,attr_value3=678,attr_value4=452,drop_time_add_show=30,},
{level=4,rmb_seq=3,price=38,drop_time_add=40,drop_time_add_show=40,},
{level=5,rmb_seq=4,price=58,drop_time_add=50,drop_time_add_show=50,},
{level=6,rmb_seq=5,price=88,reward={[0]=item_table[16],[1]=item_table[17],[2]=item_table[18],[3]=item_table[19],[4]=item_table[20]},drop_time_add=60,attr_value1=12413,attr_value2=41379,attr_value3=2068,attr_value4=1379,drop_time_add_show=60,},
{level=7,rmb_seq=6,price=188,reward={[0]=item_table[21],[1]=item_table[17],[2]=item_table[18],[3]=item_table[19],[4]=item_table[20]},drop_time_add=70,drop_time_add_show=70,},
{level=8,rmb_seq=7,price=288,reward={[0]=item_table[22],[1]=item_table[23],[2]=item_table[24],[3]=item_table[25],[4]=item_table[26]},drop_time_add=100,attr_value1=24827,attr_value2=82758,attr_value3=4137,attr_value4=2758,drop_time_add_show=100,}
},

rmb_buy_meta_table_map={
[7]=6,	-- depth:1
},
enter={
{},
{min_player_level=201,max_player_level=279,layer=2,},
{min_player_level=280,max_player_level=370,layer=3,},
{min_player_level=371,max_player_level=619,layer=4,},
{min_player_level=620,max_player_level=819,layer=5,},
{min_player_level=820,max_player_level=1019,layer=6,},
{min_player_level=1020,max_player_level=1219,layer=7,},
{min_player_level=1220,max_player_level=1419,layer=8,},
{min_player_level=1420,max_player_level=1619,layer=9,},
{min_player_level=1620,max_player_level=2000,layer=10,}
},

enter_meta_table_map={
},
refresh={
{},
{monster_seq=1,pos="114,224",},
{monster_seq=2,pos="118,223",},
{monster_seq=3,pos="119,222",},
{monster_seq=4,pos="121,220",},
{monster_seq=5,pos="122,217",},
{monster_seq=6,pos="122,215",},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=2,monster_id=6901,},
{wave=2,monster_id=6901,},
{wave=2,monster_id=6901,},
{monster_seq=3,pos="119,222",},
{wave=2,monster_id=6901,},
{monster_seq=5,pos="122,217",},
{wave=2,monster_id=6901,},
{wave=2,monster_id=6901,},
{wave=2,monster_id=6901,},
{monster_seq=9,pos="112,207",},
{wave=2,monster_id=6901,},
{wave=2,monster_id=6901,},
{wave=2,monster_id=6901,},
{wave=2,monster_id=6901,},
{wave=2,monster_id=6901,},
{wave=3,monster_id=6902,},
{wave=3,monster_id=6902,},
{wave=3,monster_id=6902,},
{monster_seq=3,pos="119,222",},
{wave=3,monster_id=6902,},
{monster_seq=5,pos="122,217",},
{wave=3,monster_id=6902,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=4,monster_id=6903,},
{wave=4,monster_id=6903,},
{wave=4,monster_id=6903,},
{monster_seq=3,pos="119,222",},
{wave=4,monster_id=6903,},
{monster_seq=5,pos="122,217",},
{wave=4,monster_id=6903,},
{wave=4,monster_id=6903,},
{wave=4,monster_id=6903,},
{wave=4,monster_id=6903,},
{wave=4,monster_id=6903,},
{wave=4,monster_id=6903,},
{wave=4,monster_id=6903,},
{wave=4,monster_id=6903,},
{wave=4,monster_id=6903,},
{wave=5,monster_id=6904,},
{wave=5,monster_id=6904,},
{wave=5,monster_id=6904,},
{monster_seq=3,pos="119,222",},
{wave=5,monster_id=6904,},
{monster_seq=5,pos="122,217",},
{wave=5,monster_id=6904,},
{wave=5,monster_id=6904,},
{wave=5,monster_id=6904,},
{wave=5,monster_id=6904,},
{wave=5,monster_id=6904,},
{wave=5,monster_id=6904,},
{monster_seq=12,pos="107,207",},
{wave=5,monster_id=6904,},
{wave=5,monster_id=6904,},
{wave=6,monster_id=6905,},
{wave=6,monster_id=6905,},
{wave=6,monster_id=6905,},
{monster_seq=3,pos="119,222",},
{wave=6,monster_id=6905,},
{monster_seq=5,pos="122,217",},
{wave=6,monster_id=6905,},
{wave=6,monster_id=6905,},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=7,monster_id=6906,},
{wave=7,monster_id=6906,},
{wave=7,monster_id=6906,},
{monster_seq=3,pos="119,222",},
{wave=7,monster_id=6906,},
{monster_seq=5,pos="122,217",},
{wave=7,monster_id=6906,},
{wave=7,monster_id=6906,},
{wave=7,monster_id=6906,},
{wave=7,monster_id=6906,},
{wave=7,monster_id=6906,},
{wave=7,monster_id=6906,},
{wave=7,monster_id=6906,},
{wave=7,monster_id=6906,},
{wave=7,monster_id=6906,},
{wave=8,monster_id=6907,},
{wave=8,monster_id=6907,},
{wave=8,monster_id=6907,},
{monster_seq=3,pos="119,222",},
{wave=8,monster_id=6907,},
{monster_seq=5,pos="122,217",},
{wave=8,monster_id=6907,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=9,monster_id=6908,},
{wave=9,monster_id=6908,},
{wave=9,monster_id=6908,},
{monster_seq=3,pos="119,222",},
{wave=9,monster_id=6908,},
{monster_seq=5,pos="122,217",},
{wave=9,monster_id=6908,},
{wave=9,monster_id=6908,},
{wave=9,monster_id=6908,},
{wave=9,monster_id=6908,},
{wave=9,monster_id=6908,},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=10,monster_id=6909,},
{wave=10,monster_id=6909,},
{wave=10,monster_id=6909,},
{monster_seq=3,pos="119,222",},
{wave=10,monster_id=6909,},
{monster_seq=5,pos="122,217",},
{wave=10,monster_id=6909,},
{wave=10,monster_id=6909,},
{wave=10,monster_id=6909,},
{wave=10,monster_id=6909,},
{wave=10,monster_id=6909,},
{wave=10,monster_id=6909,},
{wave=10,monster_id=6909,},
{wave=10,monster_id=6909,},
{wave=10,monster_id=6909,},
{wave=11,monster_id=6910,},
{wave=11,monster_id=6910,},
{wave=11,monster_id=6910,},
{monster_seq=3,pos="119,222",},
{wave=11,monster_id=6910,},
{monster_seq=5,pos="122,217",},
{wave=11,monster_id=6910,},
{monster_seq=7,pos="121,212",},
{wave=11,monster_id=6910,},
{wave=11,monster_id=6910,},
{wave=11,monster_id=6910,},
{wave=11,monster_id=6910,},
{wave=11,monster_id=6910,},
{wave=11,monster_id=6910,},
{wave=11,monster_id=6910,},
{wave=12,monster_id=6911,},
{wave=12,monster_id=6911,},
{wave=12,monster_id=6911,},
{monster_seq=3,pos="119,222",},
{wave=12,monster_id=6911,},
{monster_seq=5,pos="122,217",},
{wave=12,monster_id=6911,},
{wave=12,monster_id=6911,},
{wave=12,monster_id=6911,},
{wave=12,monster_id=6911,},
{wave=12,monster_id=6911,},
{wave=12,monster_id=6911,},
{wave=12,monster_id=6911,},
{wave=12,monster_id=6911,},
{monster_seq=14,pos="103,218",},
{wave=13,monster_id=6912,},
{wave=13,monster_id=6912,},
{wave=13,monster_id=6912,},
{monster_seq=3,pos="119,222",},
{wave=13,monster_id=6912,},
{monster_seq=5,pos="122,217",},
{wave=13,monster_id=6912,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=14,monster_id=6913,},
{wave=14,monster_id=6913,},
{wave=14,monster_id=6913,},
{monster_seq=3,pos="119,222",},
{wave=14,monster_id=6913,},
{monster_seq=5,pos="122,217",},
{wave=14,monster_id=6913,},
{wave=14,monster_id=6913,},
{wave=14,monster_id=6913,},
{wave=14,monster_id=6913,},
{wave=14,monster_id=6913,},
{wave=14,monster_id=6913,},
{wave=14,monster_id=6913,},
{wave=14,monster_id=6913,},
{wave=14,monster_id=6913,},
{wave=15,monster_id=6914,},
{wave=15,monster_id=6914,},
{wave=15,monster_id=6914,},
{monster_seq=3,pos="119,222",},
{wave=15,monster_id=6914,},
{monster_seq=5,pos="122,217",},
{wave=15,monster_id=6914,},
{monster_seq=7,pos="121,212",},
{wave=15,monster_id=6914,},
{wave=15,monster_id=6914,},
{wave=15,monster_id=6914,},
{wave=15,monster_id=6914,},
{wave=15,monster_id=6914,},
{wave=15,monster_id=6914,},
{wave=15,monster_id=6914,},
{wave=16,monster_id=6915,},
{wave=16,monster_id=6915,},
{wave=16,monster_id=6915,},
{monster_seq=3,pos="119,222",},
{wave=16,monster_id=6915,},
{monster_seq=5,pos="122,217",},
{wave=16,monster_id=6915,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=17,monster_id=6916,},
{wave=17,monster_id=6916,},
{wave=17,monster_id=6916,},
{monster_seq=3,pos="119,222",},
{wave=17,monster_id=6916,},
{monster_seq=5,pos="122,217",},
{wave=17,monster_id=6916,},
{wave=17,monster_id=6916,},
{wave=17,monster_id=6916,},
{wave=17,monster_id=6916,},
{wave=17,monster_id=6916,},
{wave=17,monster_id=6916,},
{wave=17,monster_id=6916,},
{wave=17,monster_id=6916,},
{wave=17,monster_id=6916,},
{wave=18,monster_id=6917,},
{wave=18,monster_id=6917,},
{wave=18,monster_id=6917,},
{monster_seq=3,pos="119,222",},
{wave=18,monster_id=6917,},
{monster_seq=5,pos="122,217",},
{wave=18,monster_id=6917,},
{wave=18,monster_id=6917,},
{wave=18,monster_id=6917,},
{wave=18,monster_id=6917,},
{monster_seq=10,pos="114,209",},
{wave=18,monster_id=6917,},
{wave=18,monster_id=6917,},
{wave=18,monster_id=6917,},
{wave=18,monster_id=6917,},
{wave=19,monster_id=6918,},
{wave=19,monster_id=6918,},
{wave=19,monster_id=6918,},
{monster_seq=3,pos="119,222",},
{wave=19,monster_id=6918,},
{wave=19,monster_id=6918,},
{wave=19,monster_id=6918,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=20,monster_id=6919,},
{wave=20,monster_id=6919,},
{wave=20,monster_id=6919,},
{monster_seq=3,pos="119,222",},
{wave=20,monster_id=6919,},
{monster_seq=5,pos="122,217",},
{wave=20,monster_id=6919,},
{wave=20,monster_id=6919,},
{wave=20,monster_id=6919,},
{wave=20,monster_id=6919,},
{wave=20,monster_id=6919,},
{wave=20,monster_id=6919,},
{wave=20,monster_id=6919,},
{wave=20,monster_id=6919,},
{monster_seq=14,pos="103,218",},
{wave=21,monster_id=6920,},
{wave=21,monster_id=6920,},
{wave=21,monster_id=6920,},
{monster_seq=3,pos="119,222",},
{wave=21,monster_id=6920,},
{monster_seq=5,pos="122,217",},
{wave=21,monster_id=6920,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=22,monster_id=6921,},
{wave=22,monster_id=6921,},
{wave=22,monster_id=6921,},
{monster_seq=3,pos="119,222",},
{wave=22,monster_id=6921,},
{monster_seq=5,pos="122,217",},
{wave=22,monster_id=6921,},
{wave=22,monster_id=6921,},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=23,monster_id=6922,},
{wave=23,monster_id=6922,},
{wave=23,monster_id=6922,},
{monster_seq=3,pos="119,222",},
{wave=23,monster_id=6922,},
{monster_seq=5,pos="122,217",},
{wave=23,monster_id=6922,},
{wave=23,monster_id=6922,},
{wave=23,monster_id=6922,},
{wave=23,monster_id=6922,},
{wave=23,monster_id=6922,},
{wave=23,monster_id=6922,},
{wave=23,monster_id=6922,},
{wave=23,monster_id=6922,},
{wave=23,monster_id=6922,},
{wave=24,monster_id=6923,},
{wave=24,monster_id=6923,},
{wave=24,monster_id=6923,},
{monster_seq=3,pos="119,222",},
{wave=24,monster_id=6923,},
{monster_seq=5,pos="122,217",},
{wave=24,monster_id=6923,},
{wave=24,monster_id=6923,},
{wave=24,monster_id=6923,},
{wave=24,monster_id=6923,},
{wave=24,monster_id=6923,},
{wave=24,monster_id=6923,},
{wave=24,monster_id=6923,},
{wave=24,monster_id=6923,},
{wave=24,monster_id=6923,},
{wave=25,monster_id=6924,},
{wave=25,monster_id=6924,},
{wave=25,monster_id=6924,},
{monster_seq=3,pos="119,222",},
{wave=25,monster_id=6924,},
{monster_seq=5,pos="122,217",},
{wave=25,monster_id=6924,},
{wave=25,monster_id=6924,},
{wave=25,monster_id=6924,},
{wave=25,monster_id=6924,},
{wave=25,monster_id=6924,},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=26,monster_id=6925,},
{wave=26,monster_id=6925,},
{wave=26,monster_id=6925,},
{monster_seq=3,pos="119,222",},
{wave=26,monster_id=6925,},
{monster_seq=5,pos="122,217",},
{wave=26,monster_id=6925,},
{wave=26,monster_id=6925,},
{wave=26,monster_id=6925,},
{wave=26,monster_id=6925,},
{wave=26,monster_id=6925,},
{wave=26,monster_id=6925,},
{wave=26,monster_id=6925,},
{wave=26,monster_id=6925,},
{wave=26,monster_id=6925,},
{wave=27,monster_id=6926,},
{wave=27,monster_id=6926,},
{wave=27,monster_id=6926,},
{monster_seq=3,pos="119,222",},
{wave=27,monster_id=6926,},
{wave=27,monster_id=6926,},
{wave=27,monster_id=6926,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=28,monster_id=6927,},
{wave=28,monster_id=6927,},
{wave=28,monster_id=6927,},
{monster_seq=3,pos="119,222",},
{wave=28,monster_id=6927,},
{monster_seq=5,pos="122,217",},
{wave=28,monster_id=6927,},
{wave=28,monster_id=6927,},
{wave=28,monster_id=6927,},
{wave=28,monster_id=6927,},
{wave=28,monster_id=6927,},
{wave=28,monster_id=6927,},
{wave=28,monster_id=6927,},
{wave=28,monster_id=6927,},
{monster_seq=14,pos="103,218",},
{wave=29,monster_id=6928,},
{wave=29,monster_id=6928,},
{wave=29,monster_id=6928,},
{monster_seq=3,pos="119,222",},
{wave=29,monster_id=6928,},
{monster_seq=5,pos="122,217",},
{wave=29,monster_id=6928,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=30,monster_id=6929,},
{wave=30,monster_id=6929,},
{wave=30,monster_id=6929,},
{monster_seq=3,pos="119,222",},
{wave=30,monster_id=6929,},
{monster_seq=5,pos="122,217",},
{wave=30,monster_id=6929,},
{wave=30,monster_id=6929,},
{wave=30,monster_id=6929,},
{wave=30,monster_id=6929,},
{monster_seq=10,pos="114,209",},
{wave=30,monster_id=6929,},
{wave=30,monster_id=6929,},
{wave=30,monster_id=6929,},
{wave=30,monster_id=6929,},
{wave=31,monster_id=6930,},
{wave=31,monster_id=6930,},
{wave=31,monster_id=6930,},
{monster_seq=3,pos="119,222",},
{wave=31,monster_id=6930,},
{monster_seq=5,pos="122,217",},
{wave=31,monster_id=6930,},
{monster_seq=7,pos="121,212",},
{wave=31,monster_id=6930,},
{wave=31,monster_id=6930,},
{wave=31,monster_id=6930,},
{wave=31,monster_id=6930,},
{wave=31,monster_id=6930,},
{wave=31,monster_id=6930,},
{wave=31,monster_id=6930,},
{wave=32,monster_id=6931,},
{wave=32,monster_id=6931,},
{wave=32,monster_id=6931,},
{monster_seq=3,pos="119,222",},
{wave=32,monster_id=6931,},
{monster_seq=5,pos="122,217",},
{wave=32,monster_id=6931,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=33,monster_id=6932,},
{wave=33,monster_id=6932,},
{wave=33,monster_id=6932,},
{monster_seq=3,pos="119,222",},
{wave=33,monster_id=6932,},
{monster_seq=5,pos="122,217",},
{wave=33,monster_id=6932,},
{wave=33,monster_id=6932,},
{wave=33,monster_id=6932,},
{wave=33,monster_id=6932,},
{wave=33,monster_id=6932,},
{wave=33,monster_id=6932,},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=34,monster_id=6933,},
{wave=34,monster_id=6933,},
{wave=34,monster_id=6933,},
{monster_seq=3,pos="119,222",},
{wave=34,monster_id=6933,},
{monster_seq=5,pos="122,217",},
{wave=34,monster_id=6933,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=35,monster_id=6934,},
{wave=35,monster_id=6934,},
{wave=35,monster_id=6934,},
{monster_seq=3,pos="119,222",},
{wave=35,monster_id=6934,},
{wave=35,monster_id=6934,},
{wave=35,monster_id=6934,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=36,monster_id=6935,},
{wave=36,monster_id=6935,},
{wave=36,monster_id=6935,},
{monster_seq=3,pos="119,222",},
{wave=36,monster_id=6935,},
{monster_seq=5,pos="122,217",},
{wave=36,monster_id=6935,},
{wave=36,monster_id=6935,},
{wave=36,monster_id=6935,},
{wave=36,monster_id=6935,},
{wave=36,monster_id=6935,},
{wave=36,monster_id=6935,},
{wave=36,monster_id=6935,},
{wave=36,monster_id=6935,},
{wave=36,monster_id=6935,},
{wave=37,monster_id=6936,},
{wave=37,monster_id=6936,},
{wave=37,monster_id=6936,},
{monster_seq=3,pos="119,222",},
{wave=37,monster_id=6936,},
{monster_seq=5,pos="122,217",},
{wave=37,monster_id=6936,},
{wave=37,monster_id=6936,},
{wave=37,monster_id=6936,},
{wave=37,monster_id=6936,},
{wave=37,monster_id=6936,},
{wave=37,monster_id=6936,},
{wave=37,monster_id=6936,},
{monster_seq=13,pos="104,212",},
{wave=37,monster_id=6936,},
{wave=38,monster_id=6937,},
{wave=38,monster_id=6937,},
{wave=38,monster_id=6937,},
{monster_seq=3,pos="119,222",},
{wave=38,monster_id=6937,},
{monster_seq=5,pos="122,217",},
{wave=38,monster_id=6937,},
{wave=38,monster_id=6937,},
{wave=38,monster_id=6937,},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=39,monster_id=6938,},
{wave=39,monster_id=6938,},
{wave=39,monster_id=6938,},
{monster_seq=3,pos="119,222",},
{wave=39,monster_id=6938,},
{monster_seq=5,pos="122,217",},
{wave=39,monster_id=6938,},
{wave=39,monster_id=6938,},
{wave=39,monster_id=6938,},
{wave=39,monster_id=6938,},
{wave=39,monster_id=6938,},
{wave=39,monster_id=6938,},
{wave=39,monster_id=6938,},
{wave=39,monster_id=6938,},
{wave=39,monster_id=6938,},
{wave=40,monster_id=6939,},
{wave=40,monster_id=6939,},
{wave=40,monster_id=6939,},
{monster_seq=3,pos="119,222",},
{wave=40,monster_id=6939,},
{monster_seq=5,pos="122,217",},
{wave=40,monster_id=6939,},
{wave=40,monster_id=6939,},
{wave=40,monster_id=6939,},
{wave=40,monster_id=6939,},
{wave=40,monster_id=6939,},
{wave=40,monster_id=6939,},
{wave=40,monster_id=6939,},
{wave=40,monster_id=6939,},
{wave=40,monster_id=6939,},
{wave=41,monster_id=6940,},
{wave=41,monster_id=6940,},
{wave=41,monster_id=6940,},
{monster_seq=3,pos="119,222",},
{wave=41,monster_id=6940,},
{monster_seq=5,pos="122,217",},
{wave=41,monster_id=6940,},
{wave=41,monster_id=6940,},
{wave=41,monster_id=6940,},
{wave=41,monster_id=6940,},
{wave=41,monster_id=6940,},
{wave=41,monster_id=6940,},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=42,monster_id=6941,},
{wave=42,monster_id=6941,},
{wave=42,monster_id=6941,},
{monster_seq=3,pos="119,222",},
{wave=42,monster_id=6941,},
{monster_seq=5,pos="122,217",},
{wave=42,monster_id=6941,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=43,monster_id=6942,},
{wave=43,monster_id=6942,},
{wave=43,monster_id=6942,},
{monster_seq=3,pos="119,222",},
{wave=43,monster_id=6942,},
{monster_seq=5,pos="122,217",},
{wave=43,monster_id=6942,},
{wave=43,monster_id=6942,},
{wave=43,monster_id=6942,},
{wave=43,monster_id=6942,},
{wave=43,monster_id=6942,},
{wave=43,monster_id=6942,},
{wave=43,monster_id=6942,},
{wave=43,monster_id=6942,},
{wave=43,monster_id=6942,},
{wave=44,monster_id=6943,},
{wave=44,monster_id=6943,},
{wave=44,monster_id=6943,},
{monster_seq=3,pos="119,222",},
{wave=44,monster_id=6943,},
{monster_seq=5,pos="122,217",},
{wave=44,monster_id=6943,},
{wave=44,monster_id=6943,},
{wave=44,monster_id=6943,},
{wave=44,monster_id=6943,},
{wave=44,monster_id=6943,},
{wave=44,monster_id=6943,},
{wave=44,monster_id=6943,},
{wave=44,monster_id=6943,},
{wave=44,monster_id=6943,},
{wave=45,monster_id=6944,},
{wave=45,monster_id=6944,},
{wave=45,monster_id=6944,},
{monster_seq=3,pos="119,222",},
{wave=45,monster_id=6944,},
{monster_seq=5,pos="122,217",},
{wave=45,monster_id=6944,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=46,monster_id=6945,},
{wave=46,monster_id=6945,},
{wave=46,monster_id=6945,},
{monster_seq=3,pos="119,222",},
{wave=46,monster_id=6945,},
{monster_seq=5,pos="122,217",},
{wave=46,monster_id=6945,},
{wave=46,monster_id=6945,},
{wave=46,monster_id=6945,},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=47,monster_id=6946,},
{wave=47,monster_id=6946,},
{wave=47,monster_id=6946,},
{monster_seq=3,pos="119,222",},
{wave=47,monster_id=6946,},
{monster_seq=5,pos="122,217",},
{wave=47,monster_id=6946,},
{monster_seq=7,pos="121,212",},
{wave=47,monster_id=6946,},
{wave=47,monster_id=6946,},
{wave=47,monster_id=6946,},
{wave=47,monster_id=6946,},
{wave=47,monster_id=6946,},
{wave=47,monster_id=6946,},
{wave=47,monster_id=6946,},
{wave=48,monster_id=6947,},
{wave=48,monster_id=6947,},
{wave=48,monster_id=6947,},
{monster_seq=3,pos="119,222",},
{wave=48,monster_id=6947,},
{monster_seq=5,pos="122,217",},
{wave=48,monster_id=6947,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=49,monster_id=6948,},
{wave=49,monster_id=6948,},
{wave=49,monster_id=6948,},
{monster_seq=3,pos="119,222",},
{wave=49,monster_id=6948,},
{monster_seq=5,pos="122,217",},
{wave=49,monster_id=6948,},
{wave=49,monster_id=6948,},
{wave=49,monster_id=6948,},
{wave=49,monster_id=6948,},
{wave=49,monster_id=6948,},
{wave=49,monster_id=6948,},
{wave=49,monster_id=6948,},
{monster_seq=13,pos="104,212",},
{wave=49,monster_id=6948,},
{wave=50,monster_id=6949,},
{wave=50,monster_id=6949,},
{wave=50,monster_id=6949,},
{monster_seq=3,pos="119,222",},
{wave=50,monster_id=6949,},
{monster_seq=5,pos="122,217",},
{wave=50,monster_id=6949,},
{wave=50,monster_id=6949,},
{wave=50,monster_id=6949,},
{wave=50,monster_id=6949,},
{monster_seq=10,pos="114,209",},
{wave=50,monster_id=6949,},
{wave=50,monster_id=6949,},
{wave=50,monster_id=6949,},
{wave=50,monster_id=6949,},
{wave=51,monster_id=6950,},
{wave=51,monster_id=6950,},
{wave=51,monster_id=6950,},
{monster_seq=3,pos="119,222",},
{wave=51,monster_id=6950,},
{wave=51,monster_id=6950,},
{wave=51,monster_id=6950,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=52,monster_id=6951,},
{wave=52,monster_id=6951,},
{wave=52,monster_id=6951,},
{monster_seq=3,pos="119,222",},
{wave=52,monster_id=6951,},
{monster_seq=5,pos="122,217",},
{wave=52,monster_id=6951,},
{wave=52,monster_id=6951,},
{wave=52,monster_id=6951,},
{wave=52,monster_id=6951,},
{wave=52,monster_id=6951,},
{wave=52,monster_id=6951,},
{wave=52,monster_id=6951,},
{wave=52,monster_id=6951,},
{wave=52,monster_id=6951,},
{wave=53,monster_id=6952,},
{wave=53,monster_id=6952,},
{wave=53,monster_id=6952,},
{wave=53,monster_id=6952,},
{wave=53,monster_id=6952,},
{monster_seq=5,pos="122,217",},
{wave=53,monster_id=6952,},
{wave=53,monster_id=6952,},
{wave=53,monster_id=6952,},
{wave=53,monster_id=6952,},
{wave=53,monster_id=6952,},
{wave=53,monster_id=6952,},
{wave=53,monster_id=6952,},
{wave=53,monster_id=6952,},
{wave=53,monster_id=6952,},
{wave=54,monster_id=6953,},
{wave=54,monster_id=6953,},
{wave=54,monster_id=6953,},
{monster_seq=3,pos="119,222",},
{wave=54,monster_id=6953,},
{monster_seq=5,pos="122,217",},
{wave=54,monster_id=6953,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=55,monster_id=6954,},
{wave=55,monster_id=6954,},
{wave=55,monster_id=6954,},
{monster_seq=3,pos="119,222",},
{wave=55,monster_id=6954,},
{monster_seq=5,pos="122,217",},
{wave=55,monster_id=6954,},
{wave=55,monster_id=6954,},
{wave=55,monster_id=6954,},
{wave=55,monster_id=6954,},
{wave=55,monster_id=6954,},
{wave=55,monster_id=6954,},
{wave=55,monster_id=6954,},
{wave=55,monster_id=6954,},
{wave=55,monster_id=6954,},
{wave=56,monster_id=6955,},
{wave=56,monster_id=6955,},
{wave=56,monster_id=6955,},
{monster_seq=3,pos="119,222",},
{wave=56,monster_id=6955,},
{monster_seq=5,pos="122,217",},
{wave=56,monster_id=6955,},
{wave=56,monster_id=6955,},
{wave=56,monster_id=6955,},
{wave=56,monster_id=6955,},
{wave=56,monster_id=6955,},
{monster_seq=11,pos="103,208",},
{wave=56,monster_id=6955,},
{wave=56,monster_id=6955,},
{wave=56,monster_id=6955,},
{wave=57,monster_id=6956,},
{wave=57,monster_id=6956,},
{wave=57,monster_id=6956,},
{monster_seq=3,pos="119,222",},
{wave=57,monster_id=6956,},
{wave=57,monster_id=6956,},
{wave=57,monster_id=6956,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=58,monster_id=6957,},
{wave=58,monster_id=6957,},
{wave=58,monster_id=6957,},
{monster_seq=3,pos="119,222",},
{wave=58,monster_id=6957,},
{monster_seq=5,pos="122,217",},
{wave=58,monster_id=6957,},
{wave=58,monster_id=6957,},
{wave=58,monster_id=6957,},
{wave=58,monster_id=6957,},
{wave=58,monster_id=6957,},
{wave=58,monster_id=6957,},
{wave=58,monster_id=6957,},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=59,monster_id=6958,},
{wave=59,monster_id=6958,},
{wave=59,monster_id=6958,},
{monster_seq=3,pos="119,222",},
{wave=59,monster_id=6958,},
{monster_seq=5,pos="122,217",},
{wave=59,monster_id=6958,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=60,monster_id=6959,},
{wave=60,monster_id=6959,},
{wave=60,monster_id=6959,},
{monster_seq=3,pos="119,222",},
{wave=60,monster_id=6959,},
{wave=60,monster_id=6959,},
{wave=60,monster_id=6959,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=61,monster_id=6960,},
{wave=61,monster_id=6960,},
{wave=61,monster_id=6960,},
{monster_seq=3,pos="119,222",},
{wave=61,monster_id=6960,},
{monster_seq=5,pos="122,217",},
{wave=61,monster_id=6960,},
{wave=61,monster_id=6960,},
{wave=61,monster_id=6960,},
{wave=61,monster_id=6960,},
{wave=61,monster_id=6960,},
{wave=61,monster_id=6960,},
{wave=61,monster_id=6960,},
{wave=61,monster_id=6960,},
{wave=61,monster_id=6960,},
{wave=62,monster_id=6961,},
{wave=62,monster_id=6961,},
{wave=62,monster_id=6961,},
{monster_seq=3,pos="119,222",},
{wave=62,monster_id=6961,},
{monster_seq=5,pos="122,217",},
{wave=62,monster_id=6961,},
{monster_seq=7,pos="121,212",},
{wave=62,monster_id=6961,},
{wave=62,monster_id=6961,},
{wave=62,monster_id=6961,},
{wave=62,monster_id=6961,},
{wave=62,monster_id=6961,},
{wave=62,monster_id=6961,},
{wave=62,monster_id=6961,},
{wave=63,monster_id=6962,},
{wave=63,monster_id=6962,},
{wave=63,monster_id=6962,},
{monster_seq=3,pos="119,222",},
{wave=63,monster_id=6962,},
{monster_seq=5,pos="122,217",},
{wave=63,monster_id=6962,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{wave=63,monster_id=6962,},
{monster_seq=14,pos="103,218",},
{wave=64,monster_id=6963,},
{wave=64,monster_id=6963,},
{wave=64,monster_id=6963,},
{monster_seq=3,pos="119,222",},
{wave=64,monster_id=6963,},
{wave=64,monster_id=6963,},
{wave=64,monster_id=6963,},
{wave=64,monster_id=6963,},
{wave=64,monster_id=6963,},
{wave=64,monster_id=6963,},
{wave=64,monster_id=6963,},
{wave=64,monster_id=6963,},
{wave=64,monster_id=6963,},
{wave=64,monster_id=6963,},
{wave=64,monster_id=6963,},
{wave=65,monster_id=6964,},
{wave=65,monster_id=6964,},
{wave=65,monster_id=6964,},
{monster_seq=3,pos="119,222",},
{wave=65,monster_id=6964,},
{monster_seq=5,pos="122,217",},
{wave=65,monster_id=6964,},
{wave=65,monster_id=6964,},
{wave=65,monster_id=6964,},
{wave=65,monster_id=6964,},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=66,monster_id=6965,},
{wave=66,monster_id=6965,},
{wave=66,monster_id=6965,},
{monster_seq=3,pos="119,222",},
{wave=66,monster_id=6965,},
{wave=66,monster_id=6965,},
{wave=66,monster_id=6965,},
{wave=66,monster_id=6965,},
{wave=66,monster_id=6965,},
{wave=66,monster_id=6965,},
{wave=66,monster_id=6965,},
{wave=66,monster_id=6965,},
{wave=66,monster_id=6965,},
{wave=66,monster_id=6965,},
{wave=66,monster_id=6965,},
{wave=67,monster_id=6966,},
{wave=67,monster_id=6966,},
{wave=67,monster_id=6966,},
{monster_seq=3,pos="119,222",},
{wave=67,monster_id=6966,},
{monster_seq=5,pos="122,217",},
{wave=67,monster_id=6966,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=68,monster_id=6967,},
{wave=68,monster_id=6967,},
{monster_seq=2,pos="118,223",},
{monster_seq=3,pos="119,222",},
{wave=68,monster_id=6967,},
{monster_seq=5,pos="122,217",},
{monster_seq=6,pos="122,215",},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=69,monster_id=6968,},
{wave=69,monster_id=6968,},
{monster_seq=2,pos="118,223",},
{monster_seq=3,pos="119,222",},
{monster_seq=4,pos="121,220",},
{monster_seq=5,pos="122,217",},
{monster_seq=6,pos="122,215",},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=70,monster_id=6969,},
{wave=70,monster_id=6969,},
{wave=70,monster_id=6969,},
{monster_seq=3,pos="119,222",},
{wave=70,monster_id=6969,},
{monster_seq=5,pos="122,217",},
{wave=70,monster_id=6969,},
{wave=70,monster_id=6969,},
{wave=70,monster_id=6969,},
{monster_seq=9,pos="112,207",},
{wave=70,monster_id=6969,},
{wave=70,monster_id=6969,},
{wave=70,monster_id=6969,},
{wave=70,monster_id=6969,},
{wave=70,monster_id=6969,},
{wave=71,monster_id=6970,},
{wave=71,monster_id=6970,},
{wave=71,monster_id=6970,},
{monster_seq=3,pos="119,222",},
{wave=71,monster_id=6970,},
{monster_seq=5,pos="122,217",},
{wave=71,monster_id=6970,},
{wave=71,monster_id=6970,},
{wave=71,monster_id=6970,},
{wave=71,monster_id=6970,},
{wave=71,monster_id=6970,},
{wave=71,monster_id=6970,},
{wave=71,monster_id=6970,},
{wave=71,monster_id=6970,},
{wave=71,monster_id=6970,},
{wave=72,monster_id=6971,},
{wave=72,monster_id=6971,},
{wave=72,monster_id=6971,},
{monster_seq=3,pos="119,222",},
{wave=72,monster_id=6971,},
{monster_seq=5,pos="122,217",},
{wave=72,monster_id=6971,},
{wave=72,monster_id=6971,},
{wave=72,monster_id=6971,},
{wave=72,monster_id=6971,},
{wave=72,monster_id=6971,},
{wave=72,monster_id=6971,},
{wave=72,monster_id=6971,},
{wave=72,monster_id=6971,},
{wave=72,monster_id=6971,},
{wave=73,monster_id=6972,},
{wave=73,monster_id=6972,},
{wave=73,monster_id=6972,},
{monster_seq=3,pos="119,222",},
{wave=73,monster_id=6972,},
{monster_seq=5,pos="122,217",},
{wave=73,monster_id=6972,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=74,monster_id=6973,},
{wave=74,monster_id=6973,},
{wave=74,monster_id=6973,},
{monster_seq=3,pos="119,222",},
{wave=74,monster_id=6973,},
{wave=74,monster_id=6973,},
{wave=74,monster_id=6973,},
{monster_seq=7,pos="121,212",},
{wave=74,monster_id=6973,},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{wave=74,monster_id=6973,},
{wave=74,monster_id=6973,},
{wave=74,monster_id=6973,},
{wave=74,monster_id=6973,},
{wave=75,monster_id=6974,},
{wave=75,monster_id=6974,},
{wave=75,monster_id=6974,},
{monster_seq=3,pos="119,222",},
{wave=75,monster_id=6974,},
{monster_seq=5,pos="122,217",},
{wave=75,monster_id=6974,},
{wave=75,monster_id=6974,},
{wave=75,monster_id=6974,},
{wave=75,monster_id=6974,},
{wave=75,monster_id=6974,},
{wave=75,monster_id=6974,},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=76,monster_id=6975,},
{wave=76,monster_id=6975,},
{wave=76,monster_id=6975,},
{monster_seq=3,pos="119,222",},
{wave=76,monster_id=6975,},
{monster_seq=5,pos="122,217",},
{wave=76,monster_id=6975,},
{wave=76,monster_id=6975,},
{wave=76,monster_id=6975,},
{wave=76,monster_id=6975,},
{wave=76,monster_id=6975,},
{wave=76,monster_id=6975,},
{wave=76,monster_id=6975,},
{wave=76,monster_id=6975,},
{wave=76,monster_id=6975,},
{wave=77,monster_id=6976,},
{wave=77,monster_id=6976,},
{wave=77,monster_id=6976,},
{monster_seq=3,pos="119,222",},
{wave=77,monster_id=6976,},
{monster_seq=5,pos="122,217",},
{wave=77,monster_id=6976,},
{wave=77,monster_id=6976,},
{wave=77,monster_id=6976,},
{wave=77,monster_id=6976,},
{wave=77,monster_id=6976,},
{wave=77,monster_id=6976,},
{wave=77,monster_id=6976,},
{wave=77,monster_id=6976,},
{wave=77,monster_id=6976,},
{wave=78,monster_id=6977,},
{wave=78,monster_id=6977,},
{wave=78,monster_id=6977,},
{monster_seq=3,pos="119,222",},
{wave=78,monster_id=6977,},
{monster_seq=5,pos="122,217",},
{wave=78,monster_id=6977,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=79,monster_id=6978,},
{wave=79,monster_id=6978,},
{wave=79,monster_id=6978,},
{monster_seq=3,pos="119,222",},
{wave=79,monster_id=6978,},
{monster_seq=5,pos="122,217",},
{wave=79,monster_id=6978,},
{wave=79,monster_id=6978,},
{wave=79,monster_id=6978,},
{wave=79,monster_id=6978,},
{wave=79,monster_id=6978,},
{wave=79,monster_id=6978,},
{wave=79,monster_id=6978,},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=80,monster_id=6979,},
{wave=80,monster_id=6979,},
{wave=80,monster_id=6979,},
{monster_seq=3,pos="119,222",},
{wave=80,monster_id=6979,},
{monster_seq=5,pos="122,217",},
{wave=80,monster_id=6979,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=81,monster_id=6980,},
{wave=81,monster_id=6980,},
{wave=81,monster_id=6980,},
{monster_seq=3,pos="119,222",},
{wave=81,monster_id=6980,},
{monster_seq=5,pos="122,217",},
{wave=81,monster_id=6980,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=82,monster_id=6981,},
{wave=82,monster_id=6981,},
{wave=82,monster_id=6981,},
{monster_seq=3,pos="119,222",},
{wave=82,monster_id=6981,},
{monster_seq=5,pos="122,217",},
{wave=82,monster_id=6981,},
{wave=82,monster_id=6981,},
{wave=82,monster_id=6981,},
{wave=82,monster_id=6981,},
{monster_seq=10,pos="114,209",},
{wave=82,monster_id=6981,},
{wave=82,monster_id=6981,},
{wave=82,monster_id=6981,},
{wave=82,monster_id=6981,},
{wave=83,monster_id=6982,},
{wave=83,monster_id=6982,},
{wave=83,monster_id=6982,},
{monster_seq=3,pos="119,222",},
{wave=83,monster_id=6982,},
{monster_seq=5,pos="122,217",},
{wave=83,monster_id=6982,},
{wave=83,monster_id=6982,},
{wave=83,monster_id=6982,},
{wave=83,monster_id=6982,},
{wave=83,monster_id=6982,},
{wave=83,monster_id=6982,},
{wave=83,monster_id=6982,},
{wave=83,monster_id=6982,},
{wave=83,monster_id=6982,},
{wave=84,monster_id=6983,},
{wave=84,monster_id=6983,},
{wave=84,monster_id=6983,},
{monster_seq=3,pos="119,222",},
{wave=84,monster_id=6983,},
{monster_seq=5,pos="122,217",},
{wave=84,monster_id=6983,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=85,monster_id=6984,},
{wave=85,monster_id=6984,},
{wave=85,monster_id=6984,},
{monster_seq=3,pos="119,222",},
{wave=85,monster_id=6984,},
{monster_seq=5,pos="122,217",},
{wave=85,monster_id=6984,},
{wave=85,monster_id=6984,},
{wave=85,monster_id=6984,},
{wave=85,monster_id=6984,},
{wave=85,monster_id=6984,},
{wave=85,monster_id=6984,},
{wave=85,monster_id=6984,},
{wave=85,monster_id=6984,},
{wave=85,monster_id=6984,},
{wave=86,monster_id=6985,},
{wave=86,monster_id=6985,},
{wave=86,monster_id=6985,},
{monster_seq=3,pos="119,222",},
{wave=86,monster_id=6985,},
{monster_seq=5,pos="122,217",},
{wave=86,monster_id=6985,},
{wave=86,monster_id=6985,},
{wave=86,monster_id=6985,},
{wave=86,monster_id=6985,},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=87,monster_id=6986,},
{wave=87,monster_id=6986,},
{wave=87,monster_id=6986,},
{monster_seq=3,pos="119,222",},
{wave=87,monster_id=6986,},
{wave=87,monster_id=6986,},
{wave=87,monster_id=6986,},
{wave=87,monster_id=6986,},
{wave=87,monster_id=6986,},
{wave=87,monster_id=6986,},
{wave=87,monster_id=6986,},
{wave=87,monster_id=6986,},
{wave=87,monster_id=6986,},
{wave=87,monster_id=6986,},
{wave=87,monster_id=6986,},
{wave=88,monster_id=6987,},
{wave=88,monster_id=6987,},
{wave=88,monster_id=6987,},
{monster_seq=3,pos="119,222",},
{wave=88,monster_id=6987,},
{monster_seq=5,pos="122,217",},
{wave=88,monster_id=6987,},
{wave=88,monster_id=6987,},
{wave=88,monster_id=6987,},
{wave=88,monster_id=6987,},
{wave=88,monster_id=6987,},
{wave=88,monster_id=6987,},
{wave=88,monster_id=6987,},
{wave=88,monster_id=6987,},
{wave=88,monster_id=6987,},
{wave=89,monster_id=6988,},
{wave=89,monster_id=6988,},
{wave=89,monster_id=6988,},
{monster_seq=3,pos="119,222",},
{wave=89,monster_id=6988,},
{monster_seq=5,pos="122,217",},
{wave=89,monster_id=6988,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=90,monster_id=6989,},
{wave=90,monster_id=6989,},
{wave=90,monster_id=6989,},
{monster_seq=3,pos="119,222",},
{wave=90,monster_id=6989,},
{monster_seq=5,pos="122,217",},
{wave=90,monster_id=6989,},
{wave=90,monster_id=6989,},
{wave=90,monster_id=6989,},
{wave=90,monster_id=6989,},
{wave=90,monster_id=6989,},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=91,monster_id=6990,},
{wave=91,monster_id=6990,},
{wave=91,monster_id=6990,},
{monster_seq=3,pos="119,222",},
{wave=91,monster_id=6990,},
{monster_seq=5,pos="122,217",},
{wave=91,monster_id=6990,},
{wave=91,monster_id=6990,},
{wave=91,monster_id=6990,},
{wave=91,monster_id=6990,},
{wave=91,monster_id=6990,},
{wave=91,monster_id=6990,},
{wave=91,monster_id=6990,},
{wave=91,monster_id=6990,},
{wave=91,monster_id=6990,},
{wave=92,monster_id=6991,},
{wave=92,monster_id=6991,},
{wave=92,monster_id=6991,},
{monster_seq=3,pos="119,222",},
{wave=92,monster_id=6991,},
{monster_seq=5,pos="122,217",},
{wave=92,monster_id=6991,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{wave=92,monster_id=6991,},
{wave=92,monster_id=6991,},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=93,monster_id=6992,},
{wave=93,monster_id=6992,},
{wave=93,monster_id=6992,},
{monster_seq=3,pos="119,222",},
{wave=93,monster_id=6992,},
{monster_seq=5,pos="122,217",},
{wave=93,monster_id=6992,},
{monster_seq=7,pos="121,212",},
{wave=93,monster_id=6992,},
{wave=93,monster_id=6992,},
{wave=93,monster_id=6992,},
{wave=93,monster_id=6992,},
{wave=93,monster_id=6992,},
{wave=93,monster_id=6992,},
{wave=93,monster_id=6992,},
{wave=94,monster_id=6993,},
{wave=94,monster_id=6993,},
{wave=94,monster_id=6993,},
{monster_seq=3,pos="119,222",},
{wave=94,monster_id=6993,},
{monster_seq=5,pos="122,217",},
{wave=94,monster_id=6993,},
{wave=94,monster_id=6993,},
{wave=94,monster_id=6993,},
{wave=94,monster_id=6993,},
{wave=94,monster_id=6993,},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=95,monster_id=6994,},
{wave=95,monster_id=6994,},
{wave=95,monster_id=6994,},
{monster_seq=3,pos="119,222",},
{wave=95,monster_id=6994,},
{monster_seq=5,pos="122,217",},
{wave=95,monster_id=6994,},
{monster_seq=7,pos="121,212",},
{wave=95,monster_id=6994,},
{wave=95,monster_id=6994,},
{wave=95,monster_id=6994,},
{wave=95,monster_id=6994,},
{wave=95,monster_id=6994,},
{wave=95,monster_id=6994,},
{wave=95,monster_id=6994,},
{wave=96,monster_id=6995,},
{wave=96,monster_id=6995,},
{wave=96,monster_id=6995,},
{monster_seq=3,pos="119,222",},
{wave=96,monster_id=6995,},
{monster_seq=5,pos="122,217",},
{monster_seq=6,pos="122,215",},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=97,monster_id=6996,},
{wave=97,monster_id=6996,},
{wave=97,monster_id=6996,},
{monster_seq=3,pos="119,222",},
{wave=97,monster_id=6996,},
{monster_seq=5,pos="122,217",},
{wave=97,monster_id=6996,},
{monster_seq=7,pos="121,212",},
{monster_seq=8,pos="118,210",},
{monster_seq=9,pos="112,207",},
{wave=97,monster_id=6996,},
{wave=97,monster_id=6996,},
{wave=97,monster_id=6996,},
{wave=97,monster_id=6996,},
{wave=97,monster_id=6996,},
{wave=98,monster_id=6997,},
{wave=98,monster_id=6997,},
{wave=98,monster_id=6997,},
{wave=98,monster_id=6997,},
{wave=98,monster_id=6997,},
{monster_seq=5,pos="122,217",},
{wave=98,monster_id=6997,},
{monster_seq=7,pos="121,212",},
{wave=98,monster_id=6997,},
{wave=98,monster_id=6997,},
{wave=98,monster_id=6997,},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{monster_seq=13,pos="104,212",},
{monster_seq=14,pos="103,218",},
{wave=99,monster_id=6998,},
{wave=99,monster_id=6998,},
{wave=99,monster_id=6998,},
{wave=99,monster_id=6998,},
{wave=99,monster_id=6998,},
{monster_seq=5,pos="122,217",},
{wave=99,monster_id=6998,},
{wave=99,monster_id=6998,},
{wave=99,monster_id=6998,},
{monster_seq=9,pos="112,207",},
{monster_seq=10,pos="114,209",},
{wave=99,monster_id=6998,},
{wave=99,monster_id=6998,},
{wave=99,monster_id=6998,},
{wave=99,monster_id=6998,},
{wave=100,monster_id=6999,},
{wave=100,monster_id=6999,},
{wave=100,monster_id=6999,},
{wave=100,monster_id=6999,},
{wave=100,monster_id=6999,},
{wave=100,monster_id=6999,},
{wave=100,monster_id=6999,},
{monster_seq=7,pos="121,212",},
{wave=100,monster_id=6999,},
{wave=100,monster_id=6999,},
{wave=100,monster_id=6999,},
{monster_seq=11,pos="103,208",},
{monster_seq=12,pos="107,207",},
{wave=100,monster_id=6999,},
{wave=100,monster_id=6999,}
},

refresh_meta_table_map={
[997]=7,	-- depth:1
[1000]=997,	-- depth:2
[1001]=997,	-- depth:2
[999]=997,	-- depth:2
[1002]=997,	-- depth:2
[1003]=997,	-- depth:2
[1004]=997,	-- depth:2
[1005]=997,	-- depth:2
[998]=997,	-- depth:2
[996]=997,	-- depth:2
[1428]=3,	-- depth:1
[1010]=5,	-- depth:1
[1008]=1010,	-- depth:2
[1482]=1002,	-- depth:3
[1022]=2,	-- depth:1
[1483]=1003,	-- depth:3
[1020]=1010,	-- depth:2
[1019]=1010,	-- depth:2
[1018]=1010,	-- depth:2
[1017]=1010,	-- depth:2
[1007]=1022,	-- depth:2
[1016]=1010,	-- depth:2
[1014]=1010,	-- depth:2
[1013]=1010,	-- depth:2
[1012]=1010,	-- depth:2
[1011]=1010,	-- depth:2
[995]=1010,	-- depth:2
[1009]=1010,	-- depth:2
[1484]=1019,	-- depth:3
[1015]=1010,	-- depth:2
[1485]=1020,	-- depth:3
[990]=1020,	-- depth:3
[993]=1428,	-- depth:2
[970]=1015,	-- depth:3
[969]=1014,	-- depth:3
[968]=1013,	-- depth:3
[967]=997,	-- depth:2
[1489]=1009,	-- depth:3
[966]=967,	-- depth:3
[965]=1010,	-- depth:2
[964]=965,	-- depth:3
[971]=965,	-- depth:3
[963]=1428,	-- depth:2
[1490]=1010,	-- depth:2
[960]=1020,	-- depth:3
[959]=1019,	-- depth:3
[958]=1018,	-- depth:3
[957]=1017,	-- depth:3
[956]=1016,	-- depth:3
[955]=1015,	-- depth:3
[954]=1014,	-- depth:3
[962]=1022,	-- depth:2
[994]=997,	-- depth:2
[972]=962,	-- depth:3
[974]=962,	-- depth:3
[992]=1022,	-- depth:2
[1023]=1022,	-- depth:2
[989]=1019,	-- depth:3
[988]=1018,	-- depth:3
[987]=1017,	-- depth:3
[986]=1016,	-- depth:3
[985]=1015,	-- depth:3
[984]=1014,	-- depth:3
[973]=962,	-- depth:3
[983]=1013,	-- depth:3
[981]=1011,	-- depth:3
[1487]=1022,	-- depth:2
[980]=1010,	-- depth:2
[979]=980,	-- depth:3
[978]=1428,	-- depth:2
[977]=1022,	-- depth:2
[1488]=1428,	-- depth:2
[975]=962,	-- depth:3
[982]=997,	-- depth:2
[1024]=1022,	-- depth:2
[1030]=1022,	-- depth:2
[1026]=1022,	-- depth:2
[1474]=1024,	-- depth:3
[1080]=1020,	-- depth:3
[1079]=1019,	-- depth:3
[1475]=1010,	-- depth:2
[1078]=1018,	-- depth:3
[1077]=1017,	-- depth:3
[1076]=1016,	-- depth:3
[1075]=1030,	-- depth:3
[1074]=1014,	-- depth:3
[1073]=1013,	-- depth:3
[1072]=997,	-- depth:2
[1071]=1072,	-- depth:3
[1070]=1010,	-- depth:2
[1069]=1070,	-- depth:3
[1068]=1428,	-- depth:2
[1067]=1022,	-- depth:2
[1476]=1475,	-- depth:3
[1082]=1022,	-- depth:2
[1065]=1020,	-- depth:3
[1083]=1428,	-- depth:2
[1085]=1010,	-- depth:2
[1101]=1026,	-- depth:3
[1100]=1010,	-- depth:2
[1099]=1100,	-- depth:3
[1098]=1428,	-- depth:2
[1097]=1022,	-- depth:2
[1472]=1022,	-- depth:2
[1095]=1085,	-- depth:3
[1094]=1085,	-- depth:3
[1093]=1085,	-- depth:3
[1473]=1428,	-- depth:2
[1092]=1085,	-- depth:3
[1091]=1085,	-- depth:3
[1090]=1085,	-- depth:3
[1089]=1085,	-- depth:3
[1088]=1085,	-- depth:3
[1087]=997,	-- depth:2
[1086]=1087,	-- depth:3
[1084]=1087,	-- depth:3
[1025]=1022,	-- depth:2
[1477]=997,	-- depth:2
[1063]=1018,	-- depth:3
[1042]=997,	-- depth:2
[1041]=1042,	-- depth:3
[1040]=1010,	-- depth:2
[1039]=1040,	-- depth:3
[1038]=1428,	-- depth:2
[1037]=1022,	-- depth:2
[1480]=1477,	-- depth:3
[1481]=1477,	-- depth:3
[1035]=1022,	-- depth:2
[1034]=1022,	-- depth:2
[1033]=1022,	-- depth:2
[1032]=1022,	-- depth:2
[1031]=1022,	-- depth:2
[953]=1013,	-- depth:3
[1029]=1022,	-- depth:2
[1028]=1022,	-- depth:2
[1027]=1022,	-- depth:2
[1043]=1028,	-- depth:3
[1064]=1034,	-- depth:3
[1044]=1029,	-- depth:3
[1046]=1031,	-- depth:3
[1062]=1032,	-- depth:3
[1061]=1031,	-- depth:3
[1060]=1030,	-- depth:3
[1059]=1029,	-- depth:3
[1058]=1028,	-- depth:3
[1057]=997,	-- depth:2
[1056]=1057,	-- depth:3
[1055]=1010,	-- depth:2
[1054]=1055,	-- depth:3
[1053]=1428,	-- depth:2
[1052]=1022,	-- depth:2
[1478]=1028,	-- depth:3
[1479]=1029,	-- depth:3
[1050]=1035,	-- depth:3
[1049]=1034,	-- depth:3
[1048]=1033,	-- depth:3
[1047]=1032,	-- depth:3
[1045]=1037,	-- depth:3
[1491]=1026,	-- depth:3
[944]=1034,	-- depth:3
[951]=1026,	-- depth:3
[846]=1026,	-- depth:3
[845]=1010,	-- depth:2
[844]=845,	-- depth:3
[843]=1428,	-- depth:2
[842]=1022,	-- depth:2
[840]=1035,	-- depth:3
[839]=1034,	-- depth:3
[847]=997,	-- depth:2
[838]=1033,	-- depth:3
[836]=1031,	-- depth:3
[835]=1030,	-- depth:3
[834]=1029,	-- depth:3
[833]=1028,	-- depth:3
[832]=997,	-- depth:2
[831]=832,	-- depth:3
[830]=1010,	-- depth:2
[837]=830,	-- depth:3
[848]=847,	-- depth:3
[849]=847,	-- depth:3
[850]=847,	-- depth:3
[868]=1033,	-- depth:3
[867]=1032,	-- depth:3
[866]=1031,	-- depth:3
[865]=1030,	-- depth:3
[864]=1029,	-- depth:3
[863]=1028,	-- depth:3
[862]=997,	-- depth:2
[861]=862,	-- depth:3
[860]=1010,	-- depth:2
[859]=860,	-- depth:3
[858]=1428,	-- depth:2
[857]=1022,	-- depth:2
[855]=847,	-- depth:3
[854]=847,	-- depth:3
[853]=847,	-- depth:3
[852]=847,	-- depth:3
[851]=847,	-- depth:3
[829]=830,	-- depth:3
[828]=1428,	-- depth:2
[827]=1022,	-- depth:2
[825]=1035,	-- depth:3
[802]=997,	-- depth:2
[801]=802,	-- depth:3
[800]=1010,	-- depth:2
[799]=800,	-- depth:3
[798]=1428,	-- depth:2
[797]=1022,	-- depth:2
[795]=1035,	-- depth:3
[794]=1034,	-- depth:3
[793]=1033,	-- depth:3
[792]=1032,	-- depth:3
[791]=1031,	-- depth:3
[790]=1030,	-- depth:3
[789]=1029,	-- depth:3
[788]=1028,	-- depth:3
[787]=997,	-- depth:2
[786]=787,	-- depth:3
[785]=1010,	-- depth:2
[803]=797,	-- depth:3
[869]=857,	-- depth:3
[804]=797,	-- depth:3
[806]=797,	-- depth:3
[824]=1034,	-- depth:3
[823]=1033,	-- depth:3
[822]=1032,	-- depth:3
[821]=1031,	-- depth:3
[820]=1030,	-- depth:3
[819]=1029,	-- depth:3
[818]=1028,	-- depth:3
[817]=997,	-- depth:2
[816]=817,	-- depth:3
[815]=1010,	-- depth:2
[814]=815,	-- depth:3
[813]=1428,	-- depth:2
[812]=1022,	-- depth:2
[810]=797,	-- depth:3
[809]=797,	-- depth:3
[808]=797,	-- depth:3
[807]=797,	-- depth:3
[805]=797,	-- depth:3
[952]=997,	-- depth:2
[870]=857,	-- depth:3
[873]=1428,	-- depth:2
[930]=1035,	-- depth:3
[929]=1034,	-- depth:3
[928]=1033,	-- depth:3
[927]=1032,	-- depth:3
[926]=1031,	-- depth:3
[925]=1030,	-- depth:3
[1495]=1030,	-- depth:3
[1494]=1029,	-- depth:3
[924]=1029,	-- depth:3
[922]=997,	-- depth:2
[921]=922,	-- depth:3
[920]=1010,	-- depth:2
[919]=920,	-- depth:3
[918]=1428,	-- depth:2
[917]=1022,	-- depth:2
[1496]=1031,	-- depth:3
[923]=917,	-- depth:3
[932]=1022,	-- depth:2
[933]=1428,	-- depth:2
[934]=933,	-- depth:3
[950]=1010,	-- depth:2
[949]=950,	-- depth:3
[948]=1428,	-- depth:2
[947]=1022,	-- depth:2
[1492]=997,	-- depth:2
[945]=933,	-- depth:3
[1102]=997,	-- depth:2
[943]=933,	-- depth:3
[942]=933,	-- depth:3
[941]=933,	-- depth:3
[940]=933,	-- depth:3
[939]=933,	-- depth:3
[1493]=1492,	-- depth:3
[938]=933,	-- depth:3
[937]=997,	-- depth:2
[936]=937,	-- depth:3
[935]=1010,	-- depth:2
[915]=1035,	-- depth:3
[914]=1034,	-- depth:3
[913]=1033,	-- depth:3
[912]=1032,	-- depth:3
[891]=1026,	-- depth:3
[890]=1010,	-- depth:2
[889]=890,	-- depth:3
[888]=1428,	-- depth:2
[887]=1022,	-- depth:2
[885]=873,	-- depth:3
[884]=873,	-- depth:3
[883]=873,	-- depth:3
[882]=873,	-- depth:3
[881]=873,	-- depth:3
[880]=873,	-- depth:3
[879]=873,	-- depth:3
[878]=873,	-- depth:3
[877]=997,	-- depth:2
[876]=877,	-- depth:3
[875]=1010,	-- depth:2
[874]=875,	-- depth:3
[892]=997,	-- depth:2
[872]=1022,	-- depth:2
[893]=892,	-- depth:3
[895]=892,	-- depth:3
[911]=1031,	-- depth:3
[1497]=1492,	-- depth:3
[910]=1030,	-- depth:3
[909]=1029,	-- depth:3
[908]=1028,	-- depth:3
[907]=997,	-- depth:2
[906]=907,	-- depth:3
[905]=1010,	-- depth:2
[904]=905,	-- depth:3
[903]=1428,	-- depth:2
[902]=1022,	-- depth:2
[1498]=1492,	-- depth:3
[900]=892,	-- depth:3
[899]=892,	-- depth:3
[898]=892,	-- depth:3
[897]=892,	-- depth:3
[896]=892,	-- depth:3
[894]=892,	-- depth:3
[1103]=1102,	-- depth:3
[1106]=1102,	-- depth:3
[1105]=1102,	-- depth:3
[1325]=1010,	-- depth:2
[1324]=1325,	-- depth:3
[1323]=1428,	-- depth:2
[1322]=1022,	-- depth:2
[1443]=1428,	-- depth:2
[1320]=1035,	-- depth:3
[1319]=1034,	-- depth:3
[1318]=1033,	-- depth:3
[1317]=1032,	-- depth:3
[1444]=1443,	-- depth:3
[1316]=1031,	-- depth:3
[1315]=1030,	-- depth:3
[1314]=1029,	-- depth:3
[1313]=1028,	-- depth:3
[1312]=997,	-- depth:2
[1311]=1312,	-- depth:3
[1310]=1010,	-- depth:2
[1326]=1322,	-- depth:3
[1309]=1310,	-- depth:3
[1327]=997,	-- depth:2
[1329]=1327,	-- depth:3
[1346]=1031,	-- depth:3
[1345]=1030,	-- depth:3
[1344]=1029,	-- depth:3
[1343]=1028,	-- depth:3
[1342]=997,	-- depth:2
[1341]=1342,	-- depth:3
[1340]=1010,	-- depth:2
[1339]=1340,	-- depth:3
[1338]=1428,	-- depth:2
[1337]=1022,	-- depth:2
[1442]=1022,	-- depth:2
[1335]=1327,	-- depth:3
[1334]=1327,	-- depth:3
[1333]=1327,	-- depth:3
[1332]=1327,	-- depth:3
[1331]=1327,	-- depth:3
[1330]=1327,	-- depth:3
[1328]=1327,	-- depth:3
[1308]=1428,	-- depth:2
[1307]=1022,	-- depth:2
[1445]=1010,	-- depth:2
[1285]=1030,	-- depth:3
[1284]=1029,	-- depth:3
[1283]=1028,	-- depth:3
[1282]=997,	-- depth:2
[1281]=1282,	-- depth:3
[1280]=1010,	-- depth:2
[1279]=1280,	-- depth:3
[1278]=1428,	-- depth:2
[1277]=1022,	-- depth:2
[1449]=1445,	-- depth:3
[1275]=1035,	-- depth:3
[1450]=1445,	-- depth:3
[1274]=1034,	-- depth:3
[1273]=1033,	-- depth:3
[1272]=1032,	-- depth:3
[1271]=1031,	-- depth:3
[1270]=1030,	-- depth:3
[1286]=1277,	-- depth:3
[1287]=1277,	-- depth:3
[1288]=1277,	-- depth:3
[1448]=1445,	-- depth:3
[1305]=1035,	-- depth:3
[1304]=1034,	-- depth:3
[1303]=1033,	-- depth:3
[1446]=1445,	-- depth:3
[1302]=1032,	-- depth:3
[1301]=1031,	-- depth:3
[1300]=1030,	-- depth:3
[1299]=1029,	-- depth:3
[1347]=1337,	-- depth:3
[1298]=1028,	-- depth:3
[1296]=1026,	-- depth:3
[1295]=1010,	-- depth:2
[1294]=1295,	-- depth:3
[1293]=1428,	-- depth:2
[1292]=1022,	-- depth:2
[1447]=997,	-- depth:2
[1290]=1277,	-- depth:3
[1289]=1277,	-- depth:3
[1297]=997,	-- depth:2
[1348]=1337,	-- depth:3
[1349]=1337,	-- depth:3
[1350]=1337,	-- depth:3
[1406]=1031,	-- depth:3
[1405]=1030,	-- depth:3
[1404]=1029,	-- depth:3
[1403]=1028,	-- depth:3
[1402]=997,	-- depth:2
[1401]=1402,	-- depth:3
[1433]=1428,	-- depth:2
[1400]=1010,	-- depth:2
[1399]=1400,	-- depth:3
[1398]=1428,	-- depth:2
[1397]=1022,	-- depth:2
[1434]=1428,	-- depth:2
[1395]=1035,	-- depth:3
[1394]=1034,	-- depth:3
[1393]=1033,	-- depth:3
[1392]=1032,	-- depth:3
[1391]=1031,	-- depth:3
[1407]=1397,	-- depth:3
[1408]=1397,	-- depth:3
[1409]=1397,	-- depth:3
[1410]=1397,	-- depth:3
[1427]=1022,	-- depth:2
[1430]=1010,	-- depth:2
[1425]=1035,	-- depth:3
[1424]=1034,	-- depth:3
[1423]=1033,	-- depth:3
[1422]=1032,	-- depth:3
[1421]=1031,	-- depth:3
[1420]=1030,	-- depth:3
[1390]=1030,	-- depth:3
[1419]=1434,	-- depth:3
[1417]=997,	-- depth:2
[1416]=1417,	-- depth:3
[1415]=1010,	-- depth:2
[1431]=1428,	-- depth:2
[1414]=1415,	-- depth:3
[1413]=1428,	-- depth:2
[1412]=1022,	-- depth:2
[1432]=1428,	-- depth:2
[1418]=1412,	-- depth:3
[1269]=1434,	-- depth:3
[1389]=1434,	-- depth:3
[1387]=997,	-- depth:2
[1438]=1428,	-- depth:2
[1365]=1035,	-- depth:3
[1364]=1034,	-- depth:3
[1363]=1438,	-- depth:3
[1362]=1032,	-- depth:3
[1361]=1031,	-- depth:3
[1360]=1030,	-- depth:3
[1359]=1434,	-- depth:3
[1439]=1428,	-- depth:2
[1358]=1433,	-- depth:3
[1357]=997,	-- depth:2
[1356]=1357,	-- depth:3
[1355]=1010,	-- depth:2
[1354]=1355,	-- depth:3
[1353]=1428,	-- depth:2
[1352]=1022,	-- depth:2
[1440]=1428,	-- depth:2
[1367]=1022,	-- depth:2
[1368]=1428,	-- depth:2
[1369]=1368,	-- depth:3
[1370]=1010,	-- depth:2
[1435]=1428,	-- depth:2
[1386]=1387,	-- depth:3
[1385]=1010,	-- depth:2
[1384]=1385,	-- depth:3
[1383]=1428,	-- depth:2
[1382]=1022,	-- depth:2
[1436]=1428,	-- depth:2
[1380]=1370,	-- depth:3
[1388]=1382,	-- depth:3
[1379]=1370,	-- depth:3
[1377]=1370,	-- depth:3
[1376]=1436,	-- depth:3
[1375]=1435,	-- depth:3
[1374]=1370,	-- depth:3
[1373]=1370,	-- depth:3
[1437]=1428,	-- depth:2
[1372]=997,	-- depth:2
[1371]=1372,	-- depth:3
[1378]=1372,	-- depth:3
[1268]=1433,	-- depth:3
[1267]=997,	-- depth:2
[1266]=1267,	-- depth:3
[1464]=1434,	-- depth:3
[1162]=997,	-- depth:2
[1161]=1162,	-- depth:3
[1160]=1010,	-- depth:2
[1159]=1160,	-- depth:3
[1158]=1428,	-- depth:2
[1157]=1022,	-- depth:2
[1465]=1435,	-- depth:3
[1155]=1440,	-- depth:3
[1154]=1439,	-- depth:3
[1153]=1438,	-- depth:3
[1152]=1437,	-- depth:3
[1151]=1436,	-- depth:3
[1150]=1435,	-- depth:3
[1149]=1434,	-- depth:3
[1466]=1436,	-- depth:3
[1148]=1433,	-- depth:3
[1163]=1157,	-- depth:3
[1164]=1157,	-- depth:3
[1165]=1157,	-- depth:3
[1166]=1157,	-- depth:3
[1183]=1438,	-- depth:3
[1182]=1437,	-- depth:3
[1181]=1436,	-- depth:3
[1180]=1435,	-- depth:3
[1179]=1434,	-- depth:3
[1178]=1433,	-- depth:3
[1177]=997,	-- depth:2
[1462]=997,	-- depth:2
[1147]=997,	-- depth:2
[1176]=1177,	-- depth:3
[1174]=1177,	-- depth:3
[1173]=1428,	-- depth:2
[1172]=1022,	-- depth:2
[1463]=1462,	-- depth:3
[1170]=1157,	-- depth:3
[1169]=1157,	-- depth:3
[1168]=1157,	-- depth:3
[1167]=1157,	-- depth:3
[1175]=1010,	-- depth:2
[1184]=1175,	-- depth:3
[1146]=1147,	-- depth:3
[1144]=1147,	-- depth:3
[1122]=1437,	-- depth:3
[1121]=1436,	-- depth:3
[1470]=1462,	-- depth:3
[1120]=1435,	-- depth:3
[1119]=1434,	-- depth:3
[1118]=1433,	-- depth:3
[1117]=997,	-- depth:2
[1116]=1117,	-- depth:3
[1115]=1010,	-- depth:2
[1114]=1115,	-- depth:3
[1113]=1428,	-- depth:2
[1112]=1022,	-- depth:2
[1110]=1440,	-- depth:3
[1109]=1439,	-- depth:3
[1108]=1438,	-- depth:3
[1107]=1437,	-- depth:3
[1429]=1428,	-- depth:2
[1123]=1112,	-- depth:3
[1124]=1112,	-- depth:3
[1125]=1112,	-- depth:3
[1469]=1462,	-- depth:3
[1143]=1428,	-- depth:2
[1142]=1022,	-- depth:2
[1467]=1462,	-- depth:3
[1140]=1440,	-- depth:3
[1139]=1439,	-- depth:3
[1138]=1438,	-- depth:3
[1137]=1437,	-- depth:3
[1136]=1436,	-- depth:3
[1145]=1010,	-- depth:2
[1135]=1435,	-- depth:3
[1134]=1434,	-- depth:3
[1133]=1433,	-- depth:3
[1132]=997,	-- depth:2
[1131]=1132,	-- depth:3
[1130]=1010,	-- depth:2
[1129]=1130,	-- depth:3
[1128]=1428,	-- depth:2
[1127]=1022,	-- depth:2
[1468]=1462,	-- depth:3
[1104]=1434,	-- depth:3
[1185]=1175,	-- depth:3
[1187]=1022,	-- depth:2
[1245]=1440,	-- depth:3
[1244]=1439,	-- depth:3
[1243]=1438,	-- depth:3
[1242]=1437,	-- depth:3
[1241]=1436,	-- depth:3
[1240]=1435,	-- depth:3
[1239]=1434,	-- depth:3
[1238]=1433,	-- depth:3
[1237]=997,	-- depth:2
[1236]=1237,	-- depth:3
[1235]=1010,	-- depth:2
[1234]=1235,	-- depth:3
[1233]=1428,	-- depth:2
[1455]=1440,	-- depth:3
[1232]=1022,	-- depth:2
[1230]=1440,	-- depth:3
[1229]=1439,	-- depth:3
[1454]=1439,	-- depth:3
[1453]=1438,	-- depth:3
[1247]=1022,	-- depth:2
[1248]=1428,	-- depth:2
[1265]=1010,	-- depth:2
[1264]=1265,	-- depth:3
[1263]=1428,	-- depth:2
[1262]=1022,	-- depth:2
[1451]=1436,	-- depth:3
[1452]=1437,	-- depth:3
[1260]=1248,	-- depth:3
[1259]=1248,	-- depth:3
[1228]=1438,	-- depth:3
[1258]=1248,	-- depth:3
[1256]=1248,	-- depth:3
[1255]=1248,	-- depth:3
[1254]=1248,	-- depth:3
[1253]=1248,	-- depth:3
[1252]=997,	-- depth:2
[1251]=1252,	-- depth:3
[1250]=1010,	-- depth:2
[1249]=1250,	-- depth:3
[1257]=1250,	-- depth:3
[1461]=1462,	-- depth:3
[1227]=1437,	-- depth:3
[1225]=1435,	-- depth:3
[1203]=1428,	-- depth:2
[1202]=1022,	-- depth:2
[1459]=1429,	-- depth:3
[1200]=1187,	-- depth:3
[1199]=1187,	-- depth:3
[1198]=1187,	-- depth:3
[1197]=1187,	-- depth:3
[1196]=1187,	-- depth:3
[1195]=1187,	-- depth:3
[1194]=1187,	-- depth:3
[1193]=1187,	-- depth:3
[1192]=997,	-- depth:2
[1191]=1192,	-- depth:3
[1460]=1010,	-- depth:2
[1190]=1010,	-- depth:2
[1189]=1190,	-- depth:3
[1188]=1428,	-- depth:2
[1204]=1202,	-- depth:3
[1458]=1428,	-- depth:2
[1205]=1010,	-- depth:2
[1206]=1205,	-- depth:3
[1224]=1434,	-- depth:3
[1223]=1433,	-- depth:3
[1222]=997,	-- depth:2
[1221]=1222,	-- depth:3
[1220]=1010,	-- depth:2
[1219]=1220,	-- depth:3
[1218]=1428,	-- depth:2
[1217]=1022,	-- depth:2
[1226]=1217,	-- depth:3
[1457]=1022,	-- depth:2
[1214]=1205,	-- depth:3
[1213]=1205,	-- depth:3
[1212]=1205,	-- depth:3
[1211]=1205,	-- depth:3
[1210]=1205,	-- depth:3
[1209]=1205,	-- depth:3
[1208]=1205,	-- depth:3
[1207]=997,	-- depth:2
[1215]=1207,	-- depth:3
[784]=1429,	-- depth:3
[750]=1440,	-- depth:3
[782]=1022,	-- depth:2
[276]=1431,	-- depth:3
[275]=1010,	-- depth:2
[274]=275,	-- depth:3
[273]=1428,	-- depth:2
[272]=1022,	-- depth:2
[270]=1440,	-- depth:3
[269]=1439,	-- depth:3
[268]=1438,	-- depth:3
[277]=997,	-- depth:2
[267]=1437,	-- depth:3
[265]=1435,	-- depth:3
[264]=1434,	-- depth:3
[263]=1433,	-- depth:3
[262]=997,	-- depth:2
[261]=262,	-- depth:3
[260]=1010,	-- depth:2
[259]=260,	-- depth:3
[258]=1428,	-- depth:2
[266]=258,	-- depth:3
[257]=1022,	-- depth:2
[278]=277,	-- depth:3
[280]=277,	-- depth:3
[299]=1439,	-- depth:3
[298]=1438,	-- depth:3
[297]=1437,	-- depth:3
[296]=1436,	-- depth:3
[295]=1435,	-- depth:3
[294]=1434,	-- depth:3
[293]=1433,	-- depth:3
[292]=997,	-- depth:2
[279]=277,	-- depth:3
[291]=292,	-- depth:3
[289]=292,	-- depth:3
[288]=1428,	-- depth:2
[287]=1022,	-- depth:2
[285]=277,	-- depth:3
[284]=277,	-- depth:3
[283]=277,	-- depth:3
[282]=277,	-- depth:3
[281]=277,	-- depth:3
[290]=1010,	-- depth:2
[255]=1440,	-- depth:3
[254]=1439,	-- depth:3
[253]=1438,	-- depth:3
[228]=1428,	-- depth:2
[227]=1022,	-- depth:2
[225]=1440,	-- depth:3
[224]=1439,	-- depth:3
[223]=1438,	-- depth:3
[222]=1437,	-- depth:3
[221]=1436,	-- depth:3
[220]=1435,	-- depth:3
[229]=227,	-- depth:3
[219]=1434,	-- depth:3
[217]=997,	-- depth:2
[216]=217,	-- depth:3
[215]=1010,	-- depth:2
[214]=215,	-- depth:3
[213]=1428,	-- depth:2
[212]=1022,	-- depth:2
[210]=1440,	-- depth:3
[209]=1439,	-- depth:3
[218]=212,	-- depth:3
[230]=1010,	-- depth:2
[231]=230,	-- depth:3
[232]=997,	-- depth:2
[252]=1437,	-- depth:3
[251]=1436,	-- depth:3
[250]=1435,	-- depth:3
[249]=1434,	-- depth:3
[248]=1433,	-- depth:3
[247]=997,	-- depth:2
[246]=247,	-- depth:3
[245]=1010,	-- depth:2
[244]=245,	-- depth:3
[243]=1428,	-- depth:2
[242]=1022,	-- depth:2
[240]=232,	-- depth:3
[239]=232,	-- depth:3
[238]=232,	-- depth:3
[237]=232,	-- depth:3
[236]=232,	-- depth:3
[235]=232,	-- depth:3
[234]=232,	-- depth:3
[233]=232,	-- depth:3
[300]=290,	-- depth:3
[302]=1022,	-- depth:2
[303]=1428,	-- depth:2
[304]=303,	-- depth:3
[371]=1436,	-- depth:3
[370]=1435,	-- depth:3
[369]=1434,	-- depth:3
[368]=1433,	-- depth:3
[367]=997,	-- depth:2
[366]=367,	-- depth:3
[365]=1010,	-- depth:2
[364]=365,	-- depth:3
[372]=365,	-- depth:3
[363]=1428,	-- depth:2
[360]=1440,	-- depth:3
[359]=1439,	-- depth:3
[358]=1438,	-- depth:3
[357]=1437,	-- depth:3
[356]=1436,	-- depth:3
[355]=1435,	-- depth:3
[354]=1434,	-- depth:3
[353]=1433,	-- depth:3
[362]=1022,	-- depth:2
[373]=362,	-- depth:3
[374]=362,	-- depth:3
[375]=362,	-- depth:3
[396]=1431,	-- depth:3
[395]=1010,	-- depth:2
[394]=395,	-- depth:3
[393]=1428,	-- depth:2
[392]=1022,	-- depth:2
[390]=1440,	-- depth:3
[389]=1439,	-- depth:3
[388]=1438,	-- depth:3
[387]=1437,	-- depth:3
[386]=1436,	-- depth:3
[385]=1435,	-- depth:3
[384]=1434,	-- depth:3
[383]=1433,	-- depth:3
[382]=997,	-- depth:2
[381]=382,	-- depth:3
[380]=1010,	-- depth:2
[379]=380,	-- depth:3
[378]=1428,	-- depth:2
[377]=1022,	-- depth:2
[352]=997,	-- depth:2
[208]=1438,	-- depth:3
[351]=352,	-- depth:3
[349]=352,	-- depth:3
[323]=1433,	-- depth:3
[322]=997,	-- depth:2
[321]=322,	-- depth:3
[320]=1010,	-- depth:2
[319]=320,	-- depth:3
[318]=1428,	-- depth:2
[317]=1022,	-- depth:2
[315]=303,	-- depth:3
[324]=317,	-- depth:3
[314]=303,	-- depth:3
[312]=303,	-- depth:3
[311]=303,	-- depth:3
[310]=303,	-- depth:3
[309]=303,	-- depth:3
[308]=303,	-- depth:3
[307]=997,	-- depth:2
[306]=307,	-- depth:3
[305]=1010,	-- depth:2
[313]=305,	-- depth:3
[325]=317,	-- depth:3
[326]=317,	-- depth:3
[327]=317,	-- depth:3
[348]=1428,	-- depth:2
[347]=1022,	-- depth:2
[345]=1440,	-- depth:3
[344]=1439,	-- depth:3
[343]=1438,	-- depth:3
[342]=1437,	-- depth:3
[341]=1436,	-- depth:3
[340]=1435,	-- depth:3
[339]=1434,	-- depth:3
[338]=1433,	-- depth:3
[337]=997,	-- depth:2
[336]=337,	-- depth:3
[335]=1010,	-- depth:2
[334]=335,	-- depth:3
[333]=1428,	-- depth:2
[332]=1022,	-- depth:2
[330]=317,	-- depth:3
[329]=317,	-- depth:3
[328]=317,	-- depth:3
[350]=1010,	-- depth:2
[207]=1437,	-- depth:3
[206]=1436,	-- depth:3
[205]=1435,	-- depth:3
[83]=1433,	-- depth:3
[82]=997,	-- depth:2
[81]=82,	-- depth:3
[80]=1010,	-- depth:2
[79]=80,	-- depth:3
[78]=1428,	-- depth:2
[77]=1022,	-- depth:2
[75]=1440,	-- depth:3
[84]=77,	-- depth:3
[74]=1439,	-- depth:3
[72]=1437,	-- depth:3
[71]=1436,	-- depth:3
[70]=1435,	-- depth:3
[69]=1434,	-- depth:3
[68]=1433,	-- depth:3
[67]=997,	-- depth:2
[66]=67,	-- depth:3
[65]=1010,	-- depth:2
[73]=65,	-- depth:3
[85]=77,	-- depth:3
[86]=77,	-- depth:3
[87]=77,	-- depth:3
[108]=1428,	-- depth:2
[107]=1022,	-- depth:2
[105]=1440,	-- depth:3
[104]=1439,	-- depth:3
[103]=1438,	-- depth:3
[102]=1437,	-- depth:3
[101]=1436,	-- depth:3
[100]=1435,	-- depth:3
[99]=1434,	-- depth:3
[98]=1433,	-- depth:3
[97]=997,	-- depth:2
[96]=97,	-- depth:3
[95]=1010,	-- depth:2
[94]=95,	-- depth:3
[93]=1428,	-- depth:2
[92]=1022,	-- depth:2
[90]=77,	-- depth:3
[89]=77,	-- depth:3
[88]=77,	-- depth:3
[64]=65,	-- depth:3
[109]=107,	-- depth:3
[63]=1428,	-- depth:2
[60]=1440,	-- depth:3
[35]=1010,	-- depth:2
[34]=35,	-- depth:3
[33]=1428,	-- depth:2
[32]=1022,	-- depth:2
[30]=1440,	-- depth:3
[29]=1439,	-- depth:3
[28]=1438,	-- depth:3
[27]=1437,	-- depth:3
[36]=32,	-- depth:3
[26]=1436,	-- depth:3
[24]=1434,	-- depth:3
[23]=1433,	-- depth:3
[22]=997,	-- depth:2
[21]=22,	-- depth:3
[20]=1010,	-- depth:2
[19]=20,	-- depth:3
[18]=1428,	-- depth:2
[17]=1022,	-- depth:2
[25]=17,	-- depth:3
[37]=997,	-- depth:2
[38]=37,	-- depth:3
[39]=37,	-- depth:3
[59]=1439,	-- depth:3
[58]=1438,	-- depth:3
[57]=1437,	-- depth:3
[56]=1436,	-- depth:3
[55]=1435,	-- depth:3
[54]=1434,	-- depth:3
[53]=1433,	-- depth:3
[52]=997,	-- depth:2
[51]=52,	-- depth:3
[50]=1010,	-- depth:2
[49]=50,	-- depth:3
[48]=1428,	-- depth:2
[47]=1022,	-- depth:2
[45]=37,	-- depth:3
[44]=37,	-- depth:3
[43]=37,	-- depth:3
[42]=37,	-- depth:3
[41]=37,	-- depth:3
[40]=37,	-- depth:3
[62]=1022,	-- depth:2
[397]=997,	-- depth:2
[110]=1010,	-- depth:2
[112]=997,	-- depth:2
[179]=1439,	-- depth:3
[178]=1438,	-- depth:3
[177]=1437,	-- depth:3
[176]=1436,	-- depth:3
[175]=1435,	-- depth:3
[174]=1434,	-- depth:3
[173]=1433,	-- depth:3
[172]=997,	-- depth:2
[180]=172,	-- depth:3
[171]=172,	-- depth:3
[169]=172,	-- depth:3
[168]=1428,	-- depth:2
[167]=1022,	-- depth:2
[165]=1440,	-- depth:3
[164]=1439,	-- depth:3
[163]=1438,	-- depth:3
[162]=1437,	-- depth:3
[161]=1436,	-- depth:3
[170]=1010,	-- depth:2
[182]=1022,	-- depth:2
[183]=1428,	-- depth:2
[184]=183,	-- depth:3
[204]=1434,	-- depth:3
[203]=1433,	-- depth:3
[202]=997,	-- depth:2
[201]=202,	-- depth:3
[200]=1010,	-- depth:2
[199]=200,	-- depth:3
[198]=1428,	-- depth:2
[197]=1022,	-- depth:2
[195]=183,	-- depth:3
[194]=183,	-- depth:3
[193]=183,	-- depth:3
[192]=183,	-- depth:3
[191]=183,	-- depth:3
[190]=183,	-- depth:3
[189]=183,	-- depth:3
[188]=183,	-- depth:3
[187]=997,	-- depth:2
[186]=187,	-- depth:3
[185]=1010,	-- depth:2
[160]=1435,	-- depth:3
[111]=112,	-- depth:3
[159]=1434,	-- depth:3
[157]=997,	-- depth:2
[131]=1436,	-- depth:3
[130]=1435,	-- depth:3
[129]=1434,	-- depth:3
[128]=1433,	-- depth:3
[127]=997,	-- depth:2
[126]=127,	-- depth:3
[125]=1010,	-- depth:2
[124]=125,	-- depth:3
[132]=125,	-- depth:3
[123]=1428,	-- depth:2
[120]=112,	-- depth:3
[119]=112,	-- depth:3
[118]=112,	-- depth:3
[117]=112,	-- depth:3
[116]=112,	-- depth:3
[115]=112,	-- depth:3
[114]=112,	-- depth:3
[113]=112,	-- depth:3
[122]=1022,	-- depth:2
[133]=122,	-- depth:3
[134]=122,	-- depth:3
[135]=122,	-- depth:3
[156]=157,	-- depth:3
[155]=1010,	-- depth:2
[154]=155,	-- depth:3
[153]=1428,	-- depth:2
[152]=1022,	-- depth:2
[150]=1440,	-- depth:3
[149]=1439,	-- depth:3
[148]=1438,	-- depth:3
[147]=1437,	-- depth:3
[146]=1436,	-- depth:3
[145]=1435,	-- depth:3
[144]=1434,	-- depth:3
[143]=1433,	-- depth:3
[142]=997,	-- depth:2
[141]=142,	-- depth:3
[140]=1010,	-- depth:2
[139]=140,	-- depth:3
[138]=1428,	-- depth:2
[137]=1022,	-- depth:2
[158]=152,	-- depth:3
[783]=1428,	-- depth:2
[398]=397,	-- depth:3
[400]=397,	-- depth:3
[660]=1440,	-- depth:3
[659]=1439,	-- depth:3
[658]=1438,	-- depth:3
[657]=1437,	-- depth:3
[656]=1436,	-- depth:3
[655]=1435,	-- depth:3
[654]=1434,	-- depth:3
[653]=1433,	-- depth:3
[662]=1022,	-- depth:2
[652]=997,	-- depth:2
[650]=1010,	-- depth:2
[649]=650,	-- depth:3
[648]=1428,	-- depth:2
[647]=1022,	-- depth:2
[645]=1440,	-- depth:3
[644]=1439,	-- depth:3
[643]=1438,	-- depth:3
[642]=1437,	-- depth:3
[651]=647,	-- depth:3
[641]=1436,	-- depth:3
[663]=1428,	-- depth:2
[665]=1010,	-- depth:2
[684]=1434,	-- depth:3
[683]=1433,	-- depth:3
[682]=997,	-- depth:2
[681]=682,	-- depth:3
[680]=1010,	-- depth:2
[679]=680,	-- depth:3
[678]=1428,	-- depth:2
[677]=1022,	-- depth:2
[664]=665,	-- depth:3
[675]=665,	-- depth:3
[673]=665,	-- depth:3
[672]=665,	-- depth:3
[671]=665,	-- depth:3
[670]=665,	-- depth:3
[669]=665,	-- depth:3
[668]=665,	-- depth:3
[667]=997,	-- depth:2
[666]=667,	-- depth:3
[674]=667,	-- depth:3
[640]=1435,	-- depth:3
[639]=1434,	-- depth:3
[638]=1433,	-- depth:3
[612]=1437,	-- depth:3
[611]=1436,	-- depth:3
[610]=1435,	-- depth:3
[609]=1434,	-- depth:3
[608]=1433,	-- depth:3
[607]=997,	-- depth:2
[606]=607,	-- depth:3
[605]=1010,	-- depth:2
[613]=605,	-- depth:3
[604]=605,	-- depth:3
[602]=1022,	-- depth:2
[600]=1440,	-- depth:3
[599]=1439,	-- depth:3
[598]=1438,	-- depth:3
[597]=1437,	-- depth:3
[596]=1436,	-- depth:3
[595]=1435,	-- depth:3
[594]=1434,	-- depth:3
[603]=1428,	-- depth:2
[614]=603,	-- depth:3
[615]=603,	-- depth:3
[617]=1022,	-- depth:2
[637]=997,	-- depth:2
[636]=637,	-- depth:3
[635]=1010,	-- depth:2
[634]=635,	-- depth:3
[633]=1428,	-- depth:2
[632]=1022,	-- depth:2
[630]=617,	-- depth:3
[629]=617,	-- depth:3
[628]=617,	-- depth:3
[627]=617,	-- depth:3
[626]=617,	-- depth:3
[625]=617,	-- depth:3
[624]=617,	-- depth:3
[623]=617,	-- depth:3
[622]=997,	-- depth:2
[621]=622,	-- depth:3
[620]=1010,	-- depth:2
[619]=620,	-- depth:3
[618]=1428,	-- depth:2
[685]=677,	-- depth:3
[686]=677,	-- depth:3
[687]=677,	-- depth:3
[688]=677,	-- depth:3
[756]=1431,	-- depth:3
[755]=1010,	-- depth:2
[754]=755,	-- depth:3
[753]=1428,	-- depth:2
[752]=1022,	-- depth:2
[1499]=1439,	-- depth:3
[749]=1439,	-- depth:3
[748]=1438,	-- depth:3
[757]=997,	-- depth:2
[747]=1437,	-- depth:3
[745]=1435,	-- depth:3
[744]=1434,	-- depth:3
[743]=1433,	-- depth:3
[742]=997,	-- depth:2
[741]=742,	-- depth:3
[740]=1010,	-- depth:2
[739]=740,	-- depth:3
[738]=1428,	-- depth:2
[746]=738,	-- depth:3
[758]=757,	-- depth:3
[759]=757,	-- depth:3
[760]=757,	-- depth:3
[780]=1440,	-- depth:3
[779]=1439,	-- depth:3
[778]=1438,	-- depth:3
[777]=1437,	-- depth:3
[776]=1436,	-- depth:3
[775]=1435,	-- depth:3
[774]=1434,	-- depth:3
[773]=1433,	-- depth:3
[772]=997,	-- depth:2
[771]=772,	-- depth:3
[770]=1010,	-- depth:2
[769]=770,	-- depth:3
[768]=1428,	-- depth:2
[767]=1022,	-- depth:2
[765]=757,	-- depth:3
[764]=757,	-- depth:3
[763]=757,	-- depth:3
[762]=757,	-- depth:3
[761]=757,	-- depth:3
[737]=1022,	-- depth:2
[593]=1433,	-- depth:3
[735]=1440,	-- depth:3
[733]=1438,	-- depth:3
[708]=1428,	-- depth:2
[707]=1022,	-- depth:2
[705]=1440,	-- depth:3
[704]=1439,	-- depth:3
[703]=1438,	-- depth:3
[702]=1437,	-- depth:3
[701]=1436,	-- depth:3
[700]=1435,	-- depth:3
[709]=707,	-- depth:3
[699]=1434,	-- depth:3
[697]=997,	-- depth:2
[696]=697,	-- depth:3
[695]=1010,	-- depth:2
[694]=695,	-- depth:3
[693]=1428,	-- depth:2
[692]=1022,	-- depth:2
[690]=677,	-- depth:3
[689]=677,	-- depth:3
[698]=692,	-- depth:3
[710]=1010,	-- depth:2
[711]=710,	-- depth:3
[712]=997,	-- depth:2
[732]=1437,	-- depth:3
[731]=1436,	-- depth:3
[730]=1435,	-- depth:3
[729]=1434,	-- depth:3
[728]=1433,	-- depth:3
[727]=997,	-- depth:2
[726]=727,	-- depth:3
[725]=1010,	-- depth:2
[724]=725,	-- depth:3
[723]=1428,	-- depth:2
[722]=1022,	-- depth:2
[720]=712,	-- depth:3
[719]=712,	-- depth:3
[718]=712,	-- depth:3
[717]=712,	-- depth:3
[716]=712,	-- depth:3
[715]=712,	-- depth:3
[714]=712,	-- depth:3
[713]=712,	-- depth:3
[734]=722,	-- depth:3
[592]=997,	-- depth:2
[591]=592,	-- depth:3
[590]=1010,	-- depth:2
[468]=1428,	-- depth:2
[467]=1022,	-- depth:2
[465]=1440,	-- depth:3
[464]=1439,	-- depth:3
[463]=1438,	-- depth:3
[462]=1437,	-- depth:3
[461]=1436,	-- depth:3
[460]=1435,	-- depth:3
[469]=467,	-- depth:3
[459]=1434,	-- depth:3
[457]=997,	-- depth:2
[456]=457,	-- depth:3
[455]=1010,	-- depth:2
[454]=455,	-- depth:3
[453]=1428,	-- depth:2
[452]=1022,	-- depth:2
[450]=1440,	-- depth:3
[449]=1439,	-- depth:3
[458]=452,	-- depth:3
[470]=1010,	-- depth:2
[471]=470,	-- depth:3
[472]=997,	-- depth:2
[492]=1437,	-- depth:3
[491]=1436,	-- depth:3
[490]=1435,	-- depth:3
[489]=1434,	-- depth:3
[488]=1433,	-- depth:3
[487]=997,	-- depth:2
[486]=487,	-- depth:3
[485]=1010,	-- depth:2
[484]=485,	-- depth:3
[483]=1428,	-- depth:2
[482]=1022,	-- depth:2
[480]=472,	-- depth:3
[479]=472,	-- depth:3
[478]=472,	-- depth:3
[477]=472,	-- depth:3
[476]=472,	-- depth:3
[475]=472,	-- depth:3
[474]=472,	-- depth:3
[473]=472,	-- depth:3
[448]=1438,	-- depth:3
[493]=482,	-- depth:3
[447]=1437,	-- depth:3
[445]=1435,	-- depth:3
[419]=1439,	-- depth:3
[418]=1438,	-- depth:3
[417]=1437,	-- depth:3
[416]=1436,	-- depth:3
[415]=1435,	-- depth:3
[414]=1434,	-- depth:3
[413]=1433,	-- depth:3
[412]=997,	-- depth:2
[420]=412,	-- depth:3
[411]=412,	-- depth:3
[409]=412,	-- depth:3
[408]=1428,	-- depth:2
[407]=1022,	-- depth:2
[405]=397,	-- depth:3
[404]=397,	-- depth:3
[403]=397,	-- depth:3
[402]=397,	-- depth:3
[401]=397,	-- depth:3
[410]=1010,	-- depth:2
[422]=1022,	-- depth:2
[423]=1428,	-- depth:2
[424]=423,	-- depth:3
[444]=1434,	-- depth:3
[443]=1433,	-- depth:3
[442]=997,	-- depth:2
[441]=442,	-- depth:3
[440]=1010,	-- depth:2
[439]=440,	-- depth:3
[438]=1428,	-- depth:2
[437]=1022,	-- depth:2
[435]=423,	-- depth:3
[434]=423,	-- depth:3
[433]=423,	-- depth:3
[432]=423,	-- depth:3
[431]=423,	-- depth:3
[430]=423,	-- depth:3
[429]=423,	-- depth:3
[428]=423,	-- depth:3
[427]=997,	-- depth:2
[426]=427,	-- depth:3
[425]=1010,	-- depth:2
[446]=437,	-- depth:3
[399]=397,	-- depth:3
[494]=482,	-- depth:3
[497]=1022,	-- depth:2
[564]=1434,	-- depth:3
[563]=1433,	-- depth:3
[562]=997,	-- depth:2
[561]=562,	-- depth:3
[560]=1010,	-- depth:2
[559]=560,	-- depth:3
[558]=1428,	-- depth:2
[557]=1022,	-- depth:2
[565]=557,	-- depth:3
[555]=1440,	-- depth:3
[553]=1438,	-- depth:3
[552]=1437,	-- depth:3
[551]=1436,	-- depth:3
[550]=1435,	-- depth:3
[549]=1434,	-- depth:3
[548]=1433,	-- depth:3
[547]=997,	-- depth:2
[546]=547,	-- depth:3
[554]=547,	-- depth:3
[566]=557,	-- depth:3
[567]=557,	-- depth:3
[568]=557,	-- depth:3
[589]=590,	-- depth:3
[588]=1428,	-- depth:2
[587]=1022,	-- depth:2
[585]=1440,	-- depth:3
[584]=1439,	-- depth:3
[583]=1438,	-- depth:3
[582]=1437,	-- depth:3
[581]=1436,	-- depth:3
[580]=1435,	-- depth:3
[579]=1434,	-- depth:3
[578]=1433,	-- depth:3
[577]=997,	-- depth:2
[576]=577,	-- depth:3
[575]=1010,	-- depth:2
[574]=575,	-- depth:3
[573]=1428,	-- depth:2
[572]=1022,	-- depth:2
[570]=557,	-- depth:3
[569]=557,	-- depth:3
[545]=1010,	-- depth:2
[495]=482,	-- depth:3
[544]=545,	-- depth:3
[542]=1022,	-- depth:2
[516]=1431,	-- depth:3
[515]=1010,	-- depth:2
[514]=515,	-- depth:3
[513]=1428,	-- depth:2
[512]=1022,	-- depth:2
[510]=497,	-- depth:3
[509]=497,	-- depth:3
[508]=497,	-- depth:3
[517]=997,	-- depth:2
[507]=497,	-- depth:3
[505]=497,	-- depth:3
[504]=497,	-- depth:3
[503]=497,	-- depth:3
[502]=997,	-- depth:2
[501]=502,	-- depth:3
[500]=1010,	-- depth:2
[499]=500,	-- depth:3
[498]=1428,	-- depth:2
[506]=498,	-- depth:3
[518]=517,	-- depth:3
[519]=517,	-- depth:3
[520]=517,	-- depth:3
[540]=1440,	-- depth:3
[539]=1439,	-- depth:3
[538]=1438,	-- depth:3
[537]=1437,	-- depth:3
[536]=1436,	-- depth:3
[535]=1435,	-- depth:3
[534]=1434,	-- depth:3
[533]=1433,	-- depth:3
[532]=997,	-- depth:2
[531]=532,	-- depth:3
[530]=1010,	-- depth:2
[529]=530,	-- depth:3
[528]=1428,	-- depth:2
[527]=1022,	-- depth:2
[525]=517,	-- depth:3
[524]=517,	-- depth:3
[523]=517,	-- depth:3
[522]=517,	-- depth:3
[521]=517,	-- depth:3
[543]=1428,	-- depth:2
[1500]=1440,	-- depth:3
},
recharge_task={
{}
},

recharge_task_meta_table_map={
},
wave={
{monster_level=1,reward_item={[0]=item_table[27],[1]=item_table[28],[2]=item_table[29],[3]=item_table[30]},},
{wave=2,monster_level=2,},
{wave=3,monster_level=3,},
{wave=4,monster_level=4,},
{wave=5,monster_level=5,},
{wave=6,monster_level=6,},
{wave=7,monster_level=7,},
{wave=8,monster_level=8,},
{wave=9,monster_level=9,},
{wave=10,monster_level=10,},
{wave=11,monster_level=11,},
{wave=12,monster_level=12,},
{wave=13,monster_level=13,},
{wave=14,monster_level=14,},
{wave=15,monster_level=15,},
{wave=16,monster_level=16,},
{wave=17,monster_level=17,},
{wave=18,monster_level=18,},
{wave=19,monster_level=19,},
{wave=20,reward_item={[0]=item_table[31],[1]=item_table[32],[2]=item_table[29],[3]=item_table[30]},},
{wave=21,},
{wave=22,},
{wave=23,},
{wave=24,},
{wave=25,},
{wave=26,},
{wave=27,},
{wave=28,},
{wave=29,},
{wave=30,},
{wave=31,},
{wave=32,},
{wave=33,},
{wave=34,},
{wave=35,},
{wave=36,},
{wave=37,},
{wave=38,},
{wave=39,},
{wave=40,},
{wave=41,},
{wave=42,},
{wave=43,},
{wave=44,},
{wave=45,},
{wave=46,},
{wave=47,},
{wave=48,},
{wave=49,},
{wave=50,},
{wave=51,},
{wave=52,},
{wave=53,},
{wave=54,},
{wave=55,},
{wave=56,},
{wave=57,},
{wave=58,},
{wave=59,},
{wave=60,},
{wave=61,},
{wave=62,},
{wave=63,},
{wave=64,},
{wave=65,},
{wave=66,},
{wave=67,},
{wave=68,},
{wave=69,},
{wave=70,},
{wave=71,},
{wave=72,},
{wave=73,},
{wave=74,},
{wave=75,},
{wave=76,},
{wave=77,},
{wave=78,},
{wave=79,},
{wave=80,},
{wave=81,},
{wave=82,},
{wave=83,},
{wave=84,},
{wave=85,},
{wave=86,},
{wave=87,},
{wave=88,},
{wave=89,},
{wave=90,},
{wave=91,},
{wave=92,},
{wave=93,},
{wave=94,},
{wave=95,},
{wave=96,},
{wave=97,},
{wave=98,},
{wave=99,},
{wave=100,}
},

wave_meta_table_map={
[18]=20,	-- depth:1
[2]=1,	-- depth:1
[3]=1,	-- depth:1
[4]=1,	-- depth:1
[5]=1,	-- depth:1
[6]=1,	-- depth:1
[7]=1,	-- depth:1
[8]=1,	-- depth:1
[9]=1,	-- depth:1
[10]=20,	-- depth:1
[11]=20,	-- depth:1
[12]=20,	-- depth:1
[13]=20,	-- depth:1
[14]=20,	-- depth:1
[15]=20,	-- depth:1
[16]=20,	-- depth:1
[19]=20,	-- depth:1
[17]=20,	-- depth:1
},
rank_reward={
{},
{min_rank=2,max_rank=3,reward_item={[0]=item_table[33],[1]=item_table[34],[2]=item_table[35],[3]=item_table[36],[4]=item_table[36]},},
{min_rank=4,max_rank=7,reward_item={[0]=item_table[37],[1]=item_table[34],[2]=item_table[35],[3]=item_table[38],[4]=item_table[38]},},
{min_rank=8,max_rank=20,reward_item={[0]=item_table[39],[1]=item_table[34],[2]=item_table[35],[3]=item_table[38],[4]=item_table[38]},}
},

rank_reward_meta_table_map={
},
level_up_task={
{complete_max_times=15,exp_per_times=5,},
{type=13,name="天峰夺宝",open_level=150,open_panel="fubenpanel#fubenpanel_equip_high",sort=2,},
{type=5,name="伏魔战场",open_level=290,open_panel="boss#boss_world",sort=3,}
},

level_up_task_meta_table_map={
},
other_default_table={fb_time_s=180,hour=23,minutes=50,prepare_time=5,extra_reward_min_quailty=4,extra_reward_min_star=3,},

level_default_table={level=0,exp_per_times_limit=0,drop_times_list="77,200|93,200|75,200|94,200",drop_times_list_show="77,100|93,100|75,100|94,100",added_drop_times_list_show="",map_type="仙遗洞天",map_type_index="1,2,3,4",percent_bonus=100,drop_times_list_show_1="26195:1:0,26219:1:0,26220:1:0,37901:1:0,37904:1:0,37906:1:0,37804:1:0,37807:1:0",},

drop_times_target_default_table={seq=0,reward={[0]=item_table[40]},drop_times=3000,drop_times_show=10000,},

rmb_buy_default_table={level=1,rmb_type=230,rmb_seq=0,price=8,reward={[0]=item_table[41],[1]=item_table[42],[2]=item_table[43],[3]=item_table[44],[4]=item_table[45]},drop_time_add=10,attr_id1=102,attr_value1=8044,attr_id2=101,attr_value2=26813,attr_id3=103,attr_value3=1340,attr_id4=104,attr_value4=893,attr_id5=0,attr_value5=0,attr_id6=0,attr_value6=0,attr_id7=0,attr_value7=0,attr_id8=0,attr_value8=0,model_show_itemid=37031,display_pos="0|0|0",display_scale=1,display_rotation="0|0|0",drop_time_add_show=10,},

enter_default_table={type=1,min_player_level=1,max_player_level=200,scene_id=3030,reward=item_table[46],layer=1,pos="115,218",drop_item_list={[0]=item_table[47],[1]=item_table[48],[2]=item_table[49],[3]=item_table[50],[4]=item_table[51],[5]=item_table[52],[6]=item_table[53],[7]=item_table[54],[8]=item_table[55],[9]=item_table[56],[10]=item_table[57],[11]=item_table[58],[12]=item_table[59],[13]=item_table[60],[14]=item_table[61],[15]=item_table[62],[16]=item_table[63],[17]=item_table[64],[18]=item_table[65],[19]=item_table[66],[20]=item_table[67],[21]=item_table[68],[22]=item_table[69],[23]=item_table[35],[24]=item_table[70],[25]=item_table[71],[26]=item_table[72],[27]=item_table[73],[28]=item_table[74],[29]=item_table[75],[30]=item_table[76],[31]=item_table[77],[32]=item_table[78],[33]=item_table[79],[34]=item_table[80],[35]=item_table[81]},},

refresh_default_table={scene_id=3030,wave=1,monster_seq=0,pos="109,222",monster_id=6900,},

recharge_task_default_table={seq=0,recharge_num=999999,reward={[0]=item_table[46],[1]=item_table[82],[2]=item_table[83],[3]=item_table[84],[4]=item_table[85]},name="黄金道场",map_type=1,},

wave_default_table={scene_id=3030,wave=1,monster_level=20,wave_hp=10,wave_gongji=10,reward_item={[0]=item_table[86],[1]=item_table[32],[2]=item_table[29],[3]=item_table[30]},},

rank_reward_default_table={type=1,min_rank=1,max_rank=1,reward_item={[0]=item_table[87],[1]=item_table[34],[2]=item_table[35],[3]=item_table[88],[4]=item_table[88]},},

level_up_task_default_table={act_type=0,type=38,name="仙遗洞天",open_level=100,open_dayindex=0,complete_max_times=3,exp_per_times=10,open_panel="boss#boss_vip",is_close=0,sort=1,}

}

