﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UGUITweenerWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UGUITweener), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>RegFunction("ExecuteStartEvent", ExecuteStartEvent);
		<PERSON><PERSON>Function("SetOnFinished", SetOnFinished);
		<PERSON><PERSON>Function("AddOnFinished", AddOnFinished);
		L.RegFunction("SetOnStarted", SetOnStarted);
		<PERSON>.RegFunction("AddOnSatrted", AddOnSatrted);
		<PERSON><PERSON>RegFunction("AddOnStarted", AddOnStarted);
		<PERSON><PERSON>Function("RemoveOnFinished", RemoveOnFinished);
		L.RegFunction("Sample", Sample);
		<PERSON>.RegFunction("PlayForward", PlayForward);
		<PERSON><PERSON>Function("PlayReverse", PlayReverse);
		<PERSON><PERSON>unction("SetSelfStatusFalse", SetSelfStatusFalse);
		<PERSON><PERSON>RegFunction("Play", Play);
		<PERSON><PERSON>RegFunction("ResetToBeginning", ResetToBeginning);
		L.RegFunction("Toggle", Toggle);
		L.RegFunction("SetStartToCurrentValue", SetStartToCurrentValue);
		L.RegFunction("SetEndToCurrentValue", SetEndToCurrentValue);
		L.RegFunction("GetHierarchy", GetHierarchy);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("current", get_current, set_current);
		L.RegVar("method", get_method, set_method);
		L.RegVar("style", get_style, set_style);
		L.RegVar("animationCurve", get_animationCurve, set_animationCurve);
		L.RegVar("ignoreTimeScale", get_ignoreTimeScale, set_ignoreTimeScale);
		L.RegVar("delay", get_delay, set_delay);
		L.RegVar("duration", get_duration, set_duration);
		L.RegVar("enabled_play", get_enabled_play, set_enabled_play);
		L.RegVar("disable_finish", get_disable_finish, set_disable_finish);
		L.RegVar("steeperCurves", get_steeperCurves, set_steeperCurves);
		L.RegVar("tweenGroup", get_tweenGroup, set_tweenGroup);
		L.RegVar("useFixedUpdate", get_useFixedUpdate, set_useFixedUpdate);
		L.RegVar("onFinished", get_onFinished, set_onFinished);
		L.RegVar("onFinished2", get_onFinished2, set_onFinished2);
		L.RegVar("onStarted", get_onStarted, set_onStarted);
		L.RegVar("eventReceiver", get_eventReceiver, set_eventReceiver);
		L.RegVar("callWhenFinished", get_callWhenFinished, set_callWhenFinished);
		L.RegVar("amountPerDelta", get_amountPerDelta, null);
		L.RegVar("tweenFactor", get_tweenFactor, set_tweenFactor);
		L.RegVar("direction", get_direction, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ExecuteStartEvent(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UGUITweener obj = (UGUITweener)ToLua.CheckObject<UGUITweener>(L, 1);
			obj.ExecuteStartEvent();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetOnFinished(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2 && TypeChecker.CheckTypes<EventDelegate.Callback>(L, 2))
			{
				UGUITweener obj = (UGUITweener)ToLua.CheckObject<UGUITweener>(L, 1);
				EventDelegate.Callback arg0 = (EventDelegate.Callback)ToLua.ToObject(L, 2);
				obj.SetOnFinished(arg0);
				return 0;
			}
			else if (count == 2 && TypeChecker.CheckTypes<EventDelegate>(L, 2))
			{
				UGUITweener obj = (UGUITweener)ToLua.CheckObject<UGUITweener>(L, 1);
				EventDelegate arg0 = (EventDelegate)ToLua.ToObject(L, 2);
				obj.SetOnFinished(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UGUITweener.SetOnFinished");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddOnFinished(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2 && TypeChecker.CheckTypes<EventDelegate.Callback>(L, 2))
			{
				UGUITweener obj = (UGUITweener)ToLua.CheckObject<UGUITweener>(L, 1);
				EventDelegate.Callback arg0 = (EventDelegate.Callback)ToLua.ToObject(L, 2);
				obj.AddOnFinished(arg0);
				return 0;
			}
			else if (count == 2 && TypeChecker.CheckTypes<EventDelegate>(L, 2))
			{
				UGUITweener obj = (UGUITweener)ToLua.CheckObject<UGUITweener>(L, 1);
				EventDelegate arg0 = (EventDelegate)ToLua.ToObject(L, 2);
				obj.AddOnFinished(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UGUITweener.AddOnFinished");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetOnStarted(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2 && TypeChecker.CheckTypes<EventDelegate.Callback>(L, 2))
			{
				UGUITweener obj = (UGUITweener)ToLua.CheckObject<UGUITweener>(L, 1);
				EventDelegate.Callback arg0 = (EventDelegate.Callback)ToLua.ToObject(L, 2);
				obj.SetOnStarted(arg0);
				return 0;
			}
			else if (count == 2 && TypeChecker.CheckTypes<EventDelegate>(L, 2))
			{
				UGUITweener obj = (UGUITweener)ToLua.CheckObject<UGUITweener>(L, 1);
				EventDelegate arg0 = (EventDelegate)ToLua.ToObject(L, 2);
				obj.SetOnStarted(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UGUITweener.SetOnStarted");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddOnSatrted(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UGUITweener obj = (UGUITweener)ToLua.CheckObject<UGUITweener>(L, 1);
			EventDelegate.Callback arg0 = (EventDelegate.Callback)ToLua.CheckDelegate<EventDelegate.Callback>(L, 2);
			obj.AddOnSatrted(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddOnStarted(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UGUITweener obj = (UGUITweener)ToLua.CheckObject<UGUITweener>(L, 1);
			EventDelegate arg0 = (EventDelegate)ToLua.CheckObject<EventDelegate>(L, 2);
			obj.AddOnStarted(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RemoveOnFinished(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UGUITweener obj = (UGUITweener)ToLua.CheckObject<UGUITweener>(L, 1);
			EventDelegate arg0 = (EventDelegate)ToLua.CheckObject<EventDelegate>(L, 2);
			obj.RemoveOnFinished(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Sample(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UGUITweener obj = (UGUITweener)ToLua.CheckObject<UGUITweener>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
			obj.Sample(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PlayForward(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UGUITweener obj = (UGUITweener)ToLua.CheckObject<UGUITweener>(L, 1);
			obj.PlayForward();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PlayReverse(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UGUITweener obj = (UGUITweener)ToLua.CheckObject<UGUITweener>(L, 1);
			obj.PlayReverse();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetSelfStatusFalse(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UGUITweener obj = (UGUITweener)ToLua.CheckObject<UGUITweener>(L, 1);
			obj.SetSelfStatusFalse();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Play(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UGUITweener obj = (UGUITweener)ToLua.CheckObject<UGUITweener>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.Play(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResetToBeginning(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UGUITweener obj = (UGUITweener)ToLua.CheckObject<UGUITweener>(L, 1);
			obj.ResetToBeginning();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Toggle(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UGUITweener obj = (UGUITweener)ToLua.CheckObject<UGUITweener>(L, 1);
			obj.Toggle();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetStartToCurrentValue(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UGUITweener obj = (UGUITweener)ToLua.CheckObject<UGUITweener>(L, 1);
			obj.SetStartToCurrentValue();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetEndToCurrentValue(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UGUITweener obj = (UGUITweener)ToLua.CheckObject<UGUITweener>(L, 1);
			obj.SetEndToCurrentValue();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetHierarchy(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 1, typeof(UnityEngine.GameObject));
			string o = UGUITweener.GetHierarchy(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_current(IntPtr L)
	{
		try
		{
			ToLua.Push(L, UGUITweener.current);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_method(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			TweenMethod ret = obj.method;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index method on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_style(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			UGUITweener.Style ret = obj.style;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index style on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_animationCurve(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			UnityEngine.AnimationCurve ret = obj.animationCurve;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index animationCurve on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ignoreTimeScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			bool ret = obj.ignoreTimeScale;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ignoreTimeScale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_delay(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			float ret = obj.delay;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index delay on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_duration(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			float ret = obj.duration;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index duration on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enabled_play(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			bool ret = obj.enabled_play;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enabled_play on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_disable_finish(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			bool ret = obj.disable_finish;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index disable_finish on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_steeperCurves(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			bool ret = obj.steeperCurves;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index steeperCurves on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_tweenGroup(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			int ret = obj.tweenGroup;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index tweenGroup on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_useFixedUpdate(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			bool ret = obj.useFixedUpdate;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index useFixedUpdate on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_onFinished(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			System.Collections.Generic.List<EventDelegate> ret = obj.onFinished;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onFinished on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_onFinished2(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			UnityEngine.Events.UnityAction ret = obj.onFinished2;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onFinished2 on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_onStarted(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			System.Collections.Generic.List<EventDelegate> ret = obj.onStarted;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onStarted on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_eventReceiver(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			UnityEngine.GameObject ret = obj.eventReceiver;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index eventReceiver on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_callWhenFinished(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			string ret = obj.callWhenFinished;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index callWhenFinished on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_amountPerDelta(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			float ret = obj.amountPerDelta;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index amountPerDelta on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_tweenFactor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			float ret = obj.tweenFactor;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index tweenFactor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_direction(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			AnimationOrTween.Direction ret = obj.direction;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index direction on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_current(IntPtr L)
	{
		try
		{
			UGUITweener arg0 = (UGUITweener)ToLua.CheckObject<UGUITweener>(L, 2);
			UGUITweener.current = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_method(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			TweenMethod arg0 = (TweenMethod)ToLua.CheckObject(L, 2, typeof(TweenMethod));
			obj.method = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index method on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_style(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			UGUITweener.Style arg0 = (UGUITweener.Style)ToLua.CheckObject(L, 2, typeof(UGUITweener.Style));
			obj.style = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index style on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_animationCurve(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			UnityEngine.AnimationCurve arg0 = (UnityEngine.AnimationCurve)ToLua.CheckObject<UnityEngine.AnimationCurve>(L, 2);
			obj.animationCurve = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index animationCurve on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ignoreTimeScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.ignoreTimeScale = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ignoreTimeScale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_delay(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.delay = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index delay on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_duration(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.duration = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index duration on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enabled_play(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.enabled_play = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enabled_play on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_disable_finish(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.disable_finish = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index disable_finish on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_steeperCurves(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.steeperCurves = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index steeperCurves on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_tweenGroup(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.tweenGroup = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index tweenGroup on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_useFixedUpdate(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.useFixedUpdate = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index useFixedUpdate on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_onFinished(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			System.Collections.Generic.List<EventDelegate> arg0 = (System.Collections.Generic.List<EventDelegate>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<EventDelegate>));
			obj.onFinished = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onFinished on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_onFinished2(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			UnityEngine.Events.UnityAction arg0 = (UnityEngine.Events.UnityAction)ToLua.CheckDelegate<UnityEngine.Events.UnityAction>(L, 2);
			obj.onFinished2 = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onFinished2 on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_onStarted(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			System.Collections.Generic.List<EventDelegate> arg0 = (System.Collections.Generic.List<EventDelegate>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<EventDelegate>));
			obj.onStarted = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onStarted on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_eventReceiver(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
			obj.eventReceiver = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index eventReceiver on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_callWhenFinished(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			string arg0 = ToLua.CheckString(L, 2);
			obj.callWhenFinished = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index callWhenFinished on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_tweenFactor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UGUITweener obj = (UGUITweener)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.tweenFactor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index tweenFactor on a nil value");
		}
	}
}

