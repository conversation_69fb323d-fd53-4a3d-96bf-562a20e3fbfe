<PERSON><PERSON><PERSON>zadanWGData = HuanlezadanWGData or BaseClass()
function Hu<PERSON><PERSON>zadanWGData:__init()
	if Hu<PERSON><PERSON>zadanWGData.Instance then
		error("[Hu<PERSON><PERSON>zadanWGData] Attempt to create singleton twice!")
		return
	end
	Huan<PERSON>zadanWGData.Instance = self
	self.remindEgg = 0
	self.huanlezadan_info = {
		cur_total_times = 0,
		smash_egg_fetch_times_reward_flag = 0,
		history_count = 0,
		had_smashed_egg_list = {},
		history_list = {},
	}
	self.egg_rank_list = {}
end

function HuanlezadanWGData:__delete()
	HuanlezadanWGData.Instance = nil
end

function HuanlezadanWGData:SetSCCrossRASmashedEggInfo(protocol)
	self.huanlezadan_info.cur_total_times = protocol.cur_total_times
	self.huanlezadan_info.smash_egg_fetch_times_reward_flag = protocol.smash_egg_fetch_times_reward_flag
	self.huanlezadan_info.history_count = protocol.history_count
	self.huanlezadan_info.had_smashed_egg_list = protocol.had_smashed_egg_list
	self.huanlezadan_info.history_list = protocol.history_list
end

function HuanlezadanWGData:GetSCCrossRASmashedEggInfo()
	-- 获取协议信息
	return self.huanlezadan_info
end

-- 获取配置表要展示的物品表数据
function HuanlezadanWGData:GetShowItemTable(state)
	local cfg = ServerActivityWGData.Instance:GetCrossRandActivityConfig()
	if nil == cfg or nil == cfg.happy_smashed_egg_reward then return end
	local happy_smashed_egg_reward_cfg = ServerActivityWGData.Instance:GetRandActivityConfig(cfg.happy_smashed_egg_reward, ACTIVITY_TYPE.CROSS_PERSON_RANK_TYPE_SMASHED_EGG)
	if happy_smashed_egg_reward_cfg then
		local list = {}
		local lower_level_list = {}			
		local role_level = RoleWGData.Instance:GetRoleLevel()
		for i,v in ipairs(happy_smashed_egg_reward_cfg) do
			if v.role_level and v.level_max then
				if role_level >= v.role_level and role_level <= v.level_max then
					table.insert(list,v)
				end
				if state and role_level > v.level_max then
					table.insert(lower_level_list,v)
				end
			else
				return happy_smashed_egg_reward_cfg
			end
		end
		return list,lower_level_list
	else
		return {}
	end
end

-- 获取购买列表的物品表数据
function HuanlezadanWGData:GetBuyItemTable()
	local itemTable = {}
	self.remindEgg = 0
	local showItemTable,lower_level_list = HuanlezadanWGData.Instance:GetShowItemTable(true)
	for i = 1, #self.huanlezadan_info.had_smashed_egg_list do
		if self.huanlezadan_info.had_smashed_egg_list[i] ~= 0 then
			local can_see = false
			for j = 1, #showItemTable do
				if self.huanlezadan_info.had_smashed_egg_list[i] == showItemTable[j].reward_item.item_id then
					local item = __TableCopy(showItemTable[j].reward_item)
					item.isCanSee = true
					can_see = true
					table.insert(itemTable, item)
					break
				end
			end
			if not can_see then
				for j=#lower_level_list,1,-1 do
					if self.huanlezadan_info.had_smashed_egg_list[i] == lower_level_list[j].reward_item.item_id then
						local item = __TableCopy(lower_level_list[j].reward_item)
						item.isCanSee = true
						table.insert(itemTable, item)
						break
					end
				end
			end
		else
			local item = __TableCopy(showItemTable[i].reward_item)
			item.isCanSee = false
			self.remindEgg = self.remindEgg +1
			table.insert(itemTable, item)
		end
	end
	return itemTable
end

-- 砸蛋福利
function HuanlezadanWGData:GetEggRewardItemTable()
	local cfg = ServerActivityWGData.Instance:GetCrossRandActivityConfig()
	if nil == cfg or nil == cfg.smashed_egg_times_reward then return end
	local smashed_egg_times_reward_cfg = ServerActivityWGData.Instance:GetRandActivityConfig(cfg.smashed_egg_times_reward, ACTIVITY_TYPE.CROSS_PERSON_RANK_TYPE_SMASHED_EGG)
	if smashed_egg_times_reward_cfg then
		return smashed_egg_times_reward_cfg
	else
		return {}
	end
end

-- 获取排行奖励
function HuanlezadanWGData:GetRankRewardCfg(row_rank)
	local temp = {}
	local cfg = ServerActivityWGData.Instance:GetCrossRandActivityConfig()
	if nil == cfg or nil == cfg.smashed_egg_rank_reward then return end
	for k,v in ipairs(cfg.smashed_egg_rank_reward) do
		if v.row_rank == row_rank then
			temp = __TableCopy(v)
		end
	end
	return temp
end

-- 砸蛋记录
function HuanlezadanWGData:GetEggRecordItemTable()
	return self.huanlezadan_info.history_list
end

-- 在other表获取砸一次花费的元宝
function HuanlezadanWGData:GetOneGold()
	local cfg = ServerActivityWGData.Instance:GetCrossRandActivityConfig()
	if nil == cfg or nil == cfg.other then return end
	if cfg.other[1].happy_smashed_egg_need_gold_once then
		return cfg.other[1].happy_smashed_egg_need_gold_once
	else
		return 30
	end
end

function HuanlezadanWGData:GetOtherConfig()
	local other_cfg = ServerActivityWGData.Instance:GetCrossRandActivityConfig()
	return other_cfg.other[1]
end
-- 获取砸完全部剩下花费的元宝--remindegg
function HuanlezadanWGData:GetAllGold()
	return self.remindEgg
end

-- 根据ID判断物品是否已领取
function HuanlezadanWGData:CheckIsFetchedByID(id)
	if 0 ~= (bit:_and(self.huanlezadan_info.smash_egg_fetch_times_reward_flag, bit:_lshift(1, id - 1))) then
		return true
	else
		return false
	end
end

-- 跨服砸蛋榜
function HuanlezadanWGData:SetSmashEggRankInfo(protocol)
	self.egg_rank_list = protocol
end
-- 跨服砸蛋榜
function HuanlezadanWGData:GetSmashEggRankInfo(protocol)
	return self.egg_rank_list
end
--获取服务器名字
function HuanlezadanWGData:GetServerName(data)
	local server_list = GLOBAL_CONFIG.server_info.server_list	-- 区服		
	local cur_plat_type = RoleWGData.Instance.role_vo.plat_type 	
	local name = 0
	if cur_plat_type == data.plat_type then
		for k,v in pairs(server_list) do
			if v.id == data.server_id then
				name = v.name
				name = string.gsub(name, "%(.-%)", "")
				name = string.gsub(name, "（.-）", "")
			end
		end
	else
		name = Language.Common.WaiYu.."_s".. data.server_id
	end
	return name
end

-- 红点提示
function HuanlezadanWGData:RemindHuanlezadan()
	local num = 0
	local cfg = self:GetEggRewardItemTable()
	local times = self:GetSCCrossRASmashedEggInfo().cur_total_times	
	for i=1,#cfg do
		if 0 == (bit:_and(self.huanlezadan_info.smash_egg_fetch_times_reward_flag, bit:_lshift(1, i - 1))) and times >= cfg[i].times then
			num = num + 1
		end
	end
	return num
end

--获取礼盒num
function HuanlezadanWGData:GetEggNeedItemNum()
	local cfg = ServerActivityWGData.Instance:GetCrossRandActivityConfig()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg.other[1].happy_smashed_egg_need_item_id)
	return item_num
end
