require("game/tianxian_pavilion/tianxian_pavilion_wg_data")
require("game/tianxian_pavilion/tianxian_pavilion_view")
require("game/tianxian_pavilion/tianxian_task_view")
require("game/tianxian_pavilion/tianxian_heraid_view")

TianxianPavilionWGCtrl = TianxianPavilionWGCtrl or BaseClass(BaseWGCtrl)

function TianxianPavilionWGCtrl:__init()
    if TianxianPavilionWGCtrl.Instance then
		error("[TianxianPavilionWGCtrl]:Attempt to create singleton twice!")
	end
	TianxianPavilionWGCtrl.Instance = self

    self.data = TianxianPavilionWGData.New()
    self.view = TianxianPavilionView.New(GuideModuleName.TianxianPavilionView)

    self.tianxian_task_view = TianxianTaskView.New()                -- 道具获取
    self.tianxin_hearid_view = TianxianHeraidView.New()             -- 下期预告

    self:RegisterAllProtocols()

    self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)
end

function TianxianPavilionWGCtrl:__delete()
    self.data:DeleteMe()
	self.data = nil

    self.view:DeleteMe()
	self.view = nil

    self.tianxian_task_view:DeleteMe()
    self.tianxian_task_view = nil

    self.tianxin_hearid_view:DeleteMe()
    self.tianxin_hearid_view = nil

    if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end

    TianxianPavilionWGCtrl.Instance = nil
end

-- 注册协议
function TianxianPavilionWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(CSTianXianBaoGeClientReq)

    self:RegisterProtocol(SCTianXianBaoGeAllInfo, "OnSTianXianBaoGeAllInfo")
end

-- 天仙宝阁请求操作
function TianxianPavilionWGCtrl:SendTianXianBaoGeClientReq(opera_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianXianBaoGeClientReq)
	protocol.operate_type = opera_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
    protocol:EncodeAndSend()
end

function TianxianPavilionWGCtrl:OnSTianXianBaoGeAllInfo(protocol)
    self.data:SetAllInfo(protocol)

    if self.view:IsOpen() then
        self.view:Flush()
    end

    MainuiWGCtrl.Instance:FlushTaskTopPanel()
end

function TianxianPavilionWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if self.data:IsTianxianPavilionItem(change_item_id) and new_num > old_num then
        if self.view:IsOpen() then
            self.view:Flush()
        end
	end
end

function TianxianPavilionWGCtrl:OpenView()
    self.view:Open()
end

function TianxianPavilionWGCtrl:OpenTaskView()
    self.tianxian_task_view:Open()
end

function TianxianPavilionWGCtrl:OpenHeraidView()
    self.tianxin_hearid_view:Open()
end