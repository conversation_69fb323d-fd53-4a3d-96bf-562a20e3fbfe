require("game/map/map_view")
require("game/map/map_wg_data") 

MapWGCtrl = MapWGCtrl or BaseClass(BaseWGCtrl)

function MapWGCtrl:__init()
	if MapWGCtrl.Instance ~= nil then
		print_error("[MapWGCtrl] attempt to create singleton twice!")
		return
	end
	MapWGCtrl.Instance = self

	self:RegisterAllProtocols()

	self.view = MapView.New(GuideModuleName.Map)
	self.data = MapWGData.New()

	self:RegisterProtocol(SCSceneLineInfoAck, "OnSceneLineInfoAck")
	self:RegisterProtocol(CSSceneLineInfoReq)
	self:RegisterProtocol(CSSwitchSceneLineReq)
end

function MapWGCtrl:RegisterAllProtocols()

end

function MapWGCtrl:__delete()
	if self.view ~= nil then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data ~= nil then
		self.data:DeleteMe()
		self.data = nil
	end

	MapWGCtrl.Instance = nil
end

function MapWGCtrl:OpenView()
	local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
	if fb_scene_cfg ~= nil and fb_scene_cfg.is_pb_little_map == 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Map.UnOpenInFb)
		return
	end
	ViewManager.Instance:Open(GuideModuleName.Map, "local_map")
end

function MapWGCtrl:CloseView()
	self.view:Close()
end

function MapWGCtrl:FlushMapLocalViewDoors()
	if self.view.local_view then
		self.view.local_view:ShowDoors()
	end
end

-- 刷新local地图
function MapWGCtrl:FlushMapLocalView(key, value)
	if self.view.local_view then
		self.view.local_view:Flush(key, value, "ass")
	end
end

-- 刷新global地图
function MapWGCtrl:FlushMapGlobalView(key, value)
	if self.view.global_view then
		self.view.global_view:Flush(key, value)
	end
end

function MapWGCtrl:GetMonsterIconObj(id)
	return self.view.local_view:GetMonsterIconObj(id)
end

function MapWGCtrl:ShowMonsterInfo()
end

function MapWGCtrl:CloseMonsterTip()
	ViewManager.Instance:Close(GuideModuleName.MonsterTip)
end

------------------分线----------------------
function MapWGCtrl:OnSceneLineInfoAck(protocol)
	self.data:SetSceneLineInfo(protocol)
end

function MapWGCtrl:SendSceneLineInfoReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSSceneLineInfoReq)
	protocol:EncodeAndSend()
end

function MapWGCtrl:SendSwitchSceneLineReq(scene_line)
	local protocol = ProtocolPool.Instance:GetProtocol(CSSwitchSceneLineReq)
	protocol.scene_line = scene_line or 0
	protocol:EncodeAndSend()
end