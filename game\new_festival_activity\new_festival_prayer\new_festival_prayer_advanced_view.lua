NewFestivalPrayerAdvancedView = NewFestivalPrayerAdvancedView or BaseClass(SafeBaseView)
function NewFestivalPrayerAdvancedView:__init()
	
	self:SetMaskBg(true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
    self:AddViewResource(0, "uis/view/new_festival_activity_ui_prefab", "layout_prayer_gj_view")
end

function NewFestivalPrayerAdvancedView:LoadCallBack()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
	if not self.nomax_reward_list then
		self.nomax_reward_list = AsyncBaseGrid.New()
		self.nomax_reward_list:CreateCells({col = 3, change_cells_num = 1, list_view = self.node_list["nomax_reward_grid"]})
		self.nomax_reward_list:SetStartZeroIndex(false)
	end

	if not self.max_reward_list then
		self.max_reward_list = AsyncBaseGrid.New()
		self.max_reward_list:CreateCells({col = 4, change_cells_num = 1, list_view = self.node_list["max_reward_list"]})
		self.max_reward_list:SetStartZeroIndex(false)
	end

	if self.model_display == nil then
		self.model_display = OperationActRender.New(self.node_list["act_model_root"])
	end

	XUI.AddClickEventListener(self.node_list["buy_btn"], BindTool.Bind(self.OnClickBuyBtn, self))
end

function NewFestivalPrayerAdvancedView:ReleaseCallBack()
	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.nomax_reward_list then
		self.nomax_reward_list:DeleteMe()
		self.nomax_reward_list = nil
	end

	if self.max_reward_list then
		self.max_reward_list:DeleteMe()
        self.max_reward_list = nil
    end

	if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end

	if self.buy_alert ~= nil then
		self.buy_alert:DeleteMe()
		self.buy_alert = nil
	end
end

function NewFestivalPrayerAdvancedView:OnFlush()
	local normal_reward_list, advanced_reward_list = NewFestivalPrayerWGData.Instance:GetAllPrayerReward()
	self.nomax_reward_list:SetDataList(normal_reward_list)
	self.max_reward_list:SetDataList(advanced_reward_list)

	local open_day_cfg = NewFestivalPrayerWGData.Instance:GetGradeOpenDayCfg()
	if not open_day_cfg then
		return
	end

	local price = RoleWGData.GetPayMoneyStr(open_day_cfg.price, open_day_cfg.rmb_type, open_day_cfg.rmb_seq)
	self.node_list.btn_buy_text.text.text = price --购买价格
 	self.node_list.price_value.text.text = price --总价值

	for i = 1, 3 do
		self.node_list["desc" .. i].text.text = Language.NewFestivalActivity.PrayerAdvancedDesc[i]
	end

	self:FlushModelInfo()
end

function NewFestivalPrayerAdvancedView:FlushModelInfo()
	local show_cfg = NewFestivalPrayerWGData.Instance:GetModelInfo()
    if not show_cfg then
        return
    end

    local display_data = {}
	display_data.should_ani = true
	if show_cfg.model_show_itemid ~= 0 and show_cfg.model_show_itemid ~= "" then
		local split_list = string.split(show_cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = show_cfg.model_show_itemid
		end
	end	

	display_data.bundle_name = show_cfg["model_bundle_name"]
    display_data.asset_name = show_cfg["model_asset_name"]
    local model_show_type = tonumber(show_cfg["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1
    display_data.event_trigger_listener_node = self.node_list["EventTriggerListener"]

    local pos_x, pos_y, pos_z = 0, 0, 0
	if show_cfg.display_pos_1 and show_cfg.display_pos_1 ~= "" then
		local pos_list = string.split(show_cfg.display_pos_1, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
		pos_z = tonumber(pos_list[3]) or pos_z
	end
	RectTransform.SetAnchoredPosition3DXYZ(self.node_list["act_model_root"].rect, pos_x, pos_y, pos_z)

	if show_cfg.model_pos_1 and show_cfg.model_pos_1 ~= "" then
		local pos_list = string.split(show_cfg.model_pos_1, "|")
		local posx = tonumber(pos_list[1]) or 0
		local posy = tonumber(pos_list[2]) or 0
		local posz = tonumber(pos_list[3]) or 0
		display_data.model_adjust_root_local_position = Vector3(posx, posy, posz)

	end

	local rot_x, rot_y, rot_z = 0, 0, 0
	if show_cfg.rotation_1 and show_cfg.rotation_1 ~= "" then
		local rot_list = string.split(show_cfg.rotation_1, "|")
		rot_x = tonumber(rot_list[1]) or rot_x
		rot_y = tonumber(rot_list[2]) or rot_y
		rot_z = tonumber(rot_list[3]) or rot_z
	end

	local scale = show_cfg["display_scale_1"] or 1

	display_data.role_rotation = Vector3(rot_x, rot_y, rot_z)
	display_data.model_adjust_root_local_scale = scale
	display_data.model_rt_type = ModelRTSCaleType.L

	-- if show_cfg.rotation_1 and show_cfg.rotation_1 ~= "" then
	-- 	local rotation_tab = string.split(show_cfg.rotation_1,"|")
	-- 	self.node_list["act_model_root"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	-- end

    self.model_display:SetData(display_data)
end

function NewFestivalPrayerAdvancedView:OnClickBuyBtn()
	local open_day_cfg = NewFestivalPrayerWGData.Instance:GetGradeOpenDayCfg()
	if not open_day_cfg then
		return
	end

	local is_buy_advanced = NewFestivalPrayerWGData.Instance:GetIsBuyAdvancedFlag()
	if is_buy_advanced then
		return
	end
	
	if nil == self.buy_alert then
		self.buy_alert = Alert.New(nil, nil, nil, nil, true)
		self.buy_alert:SetCheckBoxDefaultSelect(false)
	end

	local price = RoleWGData.GetPayMoneyStr(open_day_cfg.price, open_day_cfg.rmb_type, open_day_cfg.rmb_seq)
	self.buy_alert:SetLableString(string.format(Language.NewFestivalActivity.PrayerBuyAdvancedTips, price))
	self.buy_alert:SetOkFunc(function()
		RechargeWGCtrl.Instance:Recharge(open_day_cfg.price, open_day_cfg.rmb_type, open_day_cfg.rmb_seq)
		self:Close()
	end)

	self.buy_alert:Open()
end