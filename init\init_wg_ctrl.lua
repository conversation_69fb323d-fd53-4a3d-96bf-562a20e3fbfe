InitWGCtrl = {
	_class_type = true,
	ctrl_state = CTRL_STATE.START,
	loading_view = nil,
	last_flush_time = -1,
	str_list = {},
	loading_data = nil,
	scene_state = false,
}

require("init/global_config")
require("init/init_device")

RECHARGE_BILI = 10 --充值比例，从GLOBAL_CONFIG.param_list.recharge_bili获取

local FlushTime = 3
local audit_change_callback_list = {}
function InitWGCtrl:Init()
	assert(not InitWGCtrl.Instance, "multi instance InitWGCtrl")
	if not InitWGCtrl.Instance then
		InitWGCtrl.Instance = self
	end

	self.init_request_times = 1
	self.init_url_index = 1
	self.client_time = 0
	self.is_delay_time = false
	self.is_delay_time2 = false
	self.init_max_request_times = math.max(3, #GLOBAL_CONFIG.local_package_info.config.init_urls)

	self.update_total_size = 0
	self.downloaded_size = 0
end

function InitWGCtrl:Start()
	print_log("init ctrl start")
	self:Init()

	self.loading_data = require("init/init_loading_wg_data")
	self.loading_view = require("init/init_loading_view")
	self.loading_view:Start()
	self:GetRandomAsset()
	self:GetRandomStr()
	self:SendRequest()
end

function InitWGCtrl:Update(now_time, elapse_time)
	self.client_time = now_time
	if self.last_flush_time == -1 then
		self.last_flush_time = now_time
	elseif self.last_flush_time + FlushTime < now_time then
		self.last_flush_time = now_time
		self:GetRandomStr()
	end

	if self.ctrl_state == CTRL_STATE.START then
		self.ctrl_state = CTRL_STATE.UPDATE
		self:Start()
	elseif self.ctrl_state == CTRL_STATE.STOP then
		self.ctrl_state = CTRL_STATE.NONE
		self:Stop()
		PopCtrl(self)
	end

	if self.is_complete then
		if self.splash_complete then
			self:OnComplete()
			self.is_complete = false
		end
	end

	if self.is_delay_time then
		self:UpdateDelayTime(now_time, elapse_time)
	end

	if self.is_delay_time2 then
		self:UpdateDelayTime2(now_time, elapse_time)
	end
end

function InitWGCtrl:GetRandomStr()
	if #self.str_list < 1 then
		local temp_list = {}
		for k,v in pairs(self.loading_data.Reminding) do
			table.insert(temp_list, v)
		end
		self.str_list = temp_list
	end
	local index = math.random(1, #self.str_list)
	local str = self.str_list[index]
	self.loading_view:SetNotice(str)
	table.remove(self.str_list, index)
end

function InitWGCtrl:GetRandomAsset()
	-- 检查SDK是否存在闪屏页
	local url_tbl = {}
	-- 是否是第一次进游戏
	local is_first_start = UtilU3d.GetCacheData("is_first_start")
	if is_first_start == nil then
		if ResMgr.ExistedInStreaming("sdk_res/splash_1.png") then
			local url = ResUtil.GetAgentAssetPath("sdk_res/splash_1.png")
			table.insert(url_tbl, url)
		end
		if ResMgr.ExistedInStreaming("sdk_res/splash_2.png") then
			local url = ResUtil.GetAgentAssetPath("sdk_res/splash_2.png")
			table.insert(url_tbl, url)
		end
	end

	self.loading_view:SetSplashUrl(url_tbl, function() self.splash_complete = true end)
	UtilU3d.CacheData("is_first_start", 1)

	-- 检查SDK是否存在特殊的背景页，如果存在则使用SDK的背景页.
	if ResMgr.ExistedInStreaming("sdk_res/loading_bg.png") then
		local url = ResUtil.GetAgentAssetPath("sdk_res/loading_bg.png")
		self.loading_view:SetBgURL(url)
		return
	end

	local bunle_name = UtilU3d.GetCacheData("loading_bg_bundle_name")
	local asset_name = UtilU3d.GetCacheData("loading_bg_asset_name")

	if nil ~= bunle_name and nil ~= asset_name then
		self.loading_view:SetBgAsset(bunle_name, asset_name)
		return
	end

	local temp_list = self.loading_data.SceneImages
	local index = math.random(1, #temp_list)
	local asset = temp_list[index]
	if asset then
		bunle_name = asset[1]
		asset_name = asset[2]
		UtilU3d.CacheData("loading_bg_bundle_name", bunle_name)
		UtilU3d.CacheData("loading_bg_asset_name", asset_name)
		self.loading_view:SetBgAsset(bunle_name, asset_name)
	end
end

function InitWGCtrl:Stop()
end



function InitWGCtrl:SendServerInfo(plat_name, my_server_id, callback)
	local get_server_name_url = "cls.192.168.0.135/api/c2s/get_server_name.php"
	if GLOBAL_CONFIG.param_list.get_server_name_url and GLOBAL_CONFIG.param_list.get_server_name_url ~= "" then
		get_server_name_url = GLOBAL_CONFIG.param_list.get_server_name_url
	end
	local url = string.format("%s?plat=%s&server=%s",
		get_server_name_url,
		plat_name,
		my_server_id
		)
	HttpClient:Request(url, function(p_url, is_succ, data)
		local callback_data = cjson.decode(data)
		callback(is_succ, callback_data)
	end)
end

function InitWGCtrl:SendRequest()
	local url = self:GetQueryUrl()
	print_log("SendRequest", url)
	if not UNITY_EDITOR and UNITY_IOS then
		if UnityEngine.Application.internetReachability == UnityEngine.NetworkReachability.NotReachable then
			--前无网络，延迟请求
			self.is_delay_time2 = true
		else
			HttpClient:Request(url, function(p_url, is_succ, data)
				InitWGCtrl:OnRequestCallback(p_url, is_succ, data)
			end)
		end
	else
		HttpClient:Request(url, function(p_url, is_succ, data)
			InitWGCtrl:OnRequestCallback(p_url, is_succ, data)
		end)
	end
end

function InitWGCtrl:GetQueryUrl()
	local init_url = GLOBAL_CONFIG.local_package_info.config.init_urls[self.init_url_index]

	local data = {
		plat_id = GLOBAL_CONFIG.local_package_info.config.agent_id,
		pkg = GLOBAL_CONFIG.local_package_info.version,
		device = DeviceTool.GetDeviceID()
	}

	local link_data_str = PhpHandle.GetParamsToStr(data)
	local sign_str = PhpHandle.GetParamsToSignStr(data)
	local md5_sign = MD52.GetMD5(sign_str)
	local req_url = string.format("%s?%s&sign=%s", init_url, link_data_str, md5_sign)
	return req_url
end

function InitWGCtrl:OnRequestCallback(url, is_succ, data)
	require("manager/report_manager")

	local reconnect_window_title = "网络错误"
	local reconnect_window_msg = "连接服务器失败"
	local reconnect_window_btn_lbl = "重试"
	if not is_succ then
		print_error("query 请求失败：", self:GetQueryUrl())
		
		if self.init_request_times < self.init_max_request_times then
			self.init_request_times = self.init_request_times + 1
			self.init_url_index = self.init_url_index + 1
			if self.init_url_index > #GLOBAL_CONFIG.local_package_info.config.init_urls then
				self.init_url_index = 1
			end

			self:SendRequest()
		else
			self.init_request_times = 1
			self.loading_view:ShowMessageBox(reconnect_window_title, reconnect_window_msg, reconnect_window_btn_lbl, function()
				print_log("重试连接服务器")
				self:SendRequest()
			end)
			-- self:ReportConnectFaild(Report.STEP_CONNECT_PHP_SERVER_FAILED, url, data, self.init_max_request_times)
		end
		return
	end

	for i = 1, 1 do
		if data == "login block" then
			self.loading_view:ShowMessageBox("封禁", "您的设备已被封禁,请联系客服", "重试", function()
				print_log("重试连接服务器")
				self:SendRequest()
			end)
			return
		end

		local init_info = cjson.decode(data)
		local php_data = init_info ~= nil and init_info.data or nil
		
		-- 外网包需要解密
		if type(init_info) == "table" and type(php_data) ~= "table" and Base64Decode ~= nil then
			local b64 = Base64Decode(init_info.data, GlobalPHPInfoDecodeKey)
			php_data = cjson.decode(b64)
		end

		if init_info == nil or php_data == nil then
			self.loading_view:ShowMessageBox(reconnect_window_title, reconnect_window_msg, reconnect_window_btn_lbl, function()
				print_log("重试连接服务器")
				self:SendRequest()
			end)

			-- self:ReportConnectFaild(Report.STEP_JSON_DECODE_FAILED, url, data, self.cjson_max_request_times)
			return
		end

		if nil == php_data.param_list then break end
		GLOBAL_CONFIG.param_list = php_data.param_list
		GLOBAL_CONFIG.param_list.shield_chat_voice = 1 -- 强制屏蔽语音聊天
		GLOBAL_CONFIG.param_list.switch_list = php_data.param_list.switch_list or {}
		GLOBAL_CONFIG.api_urls = php_data.api_urls
		GLOBAL_CONFIG.chat_reward_word_list = php_data.chat_reward_word_list

		if nil ~= GLOBAL_CONFIG.param_list.recharge_bili then
			RECHARGE_BILI = GLOBAL_CONFIG.param_list.recharge_bili
		end
		
		-- 传参覆盖，让改动最少
		GlobalUrl = php_data.param_list.global_url or DefaultGlobalUrl

		if GLOBAL_CONFIG.param_list.res_encrypt_type == 2 then
			EncryptMgr.SetEncryptKey(GLOBAL_CONFIG.param_list.res_encrypt_key)
			EncryptMgr.SetBase64EncryptKey(GLOBAL_CONFIG.param_list.res_base64_value)
			EncryptMgr.SaveCacheEncryptKeyFile()
		end

		if GLOBAL_CONFIG.param_list.switch_list.audit_version then
			IS_AUDIT_VERSION = true
			for k,v in pairs(audit_change_callback_list) do
				k()
			end
		end

		if UNITY_EDITOR and not IS_AUDIT_VERSION then
			IS_AUDIT_VERSION = PlayerPrefsUtil.GetInt("IS_AUDIT_VERSION") == 1 and true or false
			if IS_AUDIT_VERSION then
				print_error("编辑器下已进入审核服状态(可用Developer面板关闭)")
			end
			for k,v in pairs(audit_change_callback_list) do
				k()
			end
		end

		if GLOBAL_CONFIG.param_list.switch_list.open_gvoice then
			IS_FEES_VOICE = true
		end
		
		if nil == php_data.server_info then break end
		GLOBAL_CONFIG.server_info = php_data.server_info
		GLOBAL_CONFIG.client_time = self.client_time
		if nil == php_data.version_info then break end
		local version_info = php_data.version_info

		GLOBAL_CONFIG.version_info = {}
		if nil == version_info.package_info then break end
		GLOBAL_CONFIG.version_info.php_package_info = version_info.package_info

		if nil ~= version_info.assets_info then
			GLOBAL_CONFIG.version_info.php_assets_info = version_info.assets_info
			ResMgr:SetAssetVersion(version_info.assets_info.version)
			ResMgr:SetAssetLuaVersion(version_info.assets_info.version)
		end

		-- 上报服务器第一条协议：游戏启动
		ReportManager:ReportBuriedCounter(BURIED_COUNTER_TYPE.gameInit)

		if nil ~= version_info.update_data and nil ~= version_info.update_data then
			--如果服务器有传update再执行，否则执行本地的update.lua
			local update_data = mime.unb64(version_info.update_data)
			local update_func = loadstring(update_data)
			if nil ~= update_func and "function" == type(update_func) then
				PushCtrl(update_func())
				return
			end
		end
		PushCtrl(require("update"))
	end
end

-- view
function InitWGCtrl:ShowLoading()
	if not self.loading_view then
		return
	end
	self.loading_view:Show()
end

function InitWGCtrl:HideLoading()
	if not self.loading_view then
		return
	end
	self.loading_view:Hide()
end

function InitWGCtrl:SetSceneState(scene_state)
	self.scene_state = scene_state
end

function InitWGCtrl:SetText(text)
	self.loading_view:SetText(text)
end

function InitWGCtrl:SetPercent(percent, callback)
	self.loading_view:SetPercent(percent, callback)
end

function InitWGCtrl:ShowMessageBox(title, content, button_name, complete)
	self.loading_view:ShowMessageBox(title, content, button_name, complete)
end

function InitWGCtrl:SetDownloading(state)
	self.loading_view:SetDownloading(state)
end

-- 0:极致 1:高清 2:流畅 3:省电
local function SetQuality(level)
	QualityConfig.QualityLevel = level
	PlayerPrefsUtil.SetInt("a3_custom_quality_level", level)
	if GlobalEventSystem then
		GlobalEventSystem:Fire(ObjectEventType.QUALITY_CHANGE)
	end
	
	if UNITY_ANDROID or UNITY_IOS then
		local recommend_quality = SettingWGData.Instance:GetRecommendQuality()
		
		if DEVELOPMENT_BUILD then
			--开发者模式不限制
			recommend_quality = 0
		end

		if recommend_quality == 2 or recommend_quality == 3 then
			--LimitScreenResolution(720)
			--UnityEngine.Application.targetFrameRate = 30 --中低端机不管开不开高帧，直接用30帧
		else
			--LimitScreenResolution(1080)
			UnityEngine.Shader.globalMaximumLOD = 300
		end
	end
end

function InitWGCtrl:CheckDefaultSetting()
	-- 如果玩家设置了，就不再进入默认设置
	local quality_level = 0
	if PlayerPrefsUtil.HasKey("a3_custom_quality_level") then
		quality_level = PlayerPrefsUtil.GetInt("a3_custom_quality_level")
	else
		quality_level = SettingWGData.Instance:GetRecommendQuality()
	end
	SetQuality(quality_level)
end

function InitWGCtrl:OnCompleteRequire()
	if G_IsDeveloper or GLOBAL_DELETE_ME_CHECK_SWITCH then
		CheckMem:InitGlobal()
	end

	local play = require("play")
	play:SetComplete(function ()
		-- 进入游戏时检查手机配置
		InitWGCtrl:CheckDefaultSetting()
		-- 开始预加载
		PreloadManager.Instance:Start()
		PreloadManager.Instance:WaitComplete(function (percent)
			self:SetPercent(0.08 * percent + 0.82)
			if percent < 1 then
				return
			end

			-- 打开登录界面
			local complete_callback = function ()
			self:SetPercent(1, function ()
				local select_role_state = UtilU3d.GetCacheData("select_role_state")
				if select_role_state ~= 1 then
					self:HideLoading()
					self:DestroyLoadingView()
				end
				LuaGC:StartGC()
				self:OnComplete()
			end)
		end
		-- 打开登录界面
		LoginWGCtrl.Instance:StartLogin(complete_callback)
		end)
	end)

	PushCtrl(play)
end

function InitWGCtrl:OnComplete()
	-- 闪屏完成后
	if self.splash_complete then
		print_log("InitWGCtrl:OnComplete")
		self:Delete()
	else
		self.is_complete = true
	end

	-- 初始化预下载
	PreDownload.Instance:CreateDownloadList()
	-- 下载package_0(新手资源)
	if GAME_ASSETBUNDLE then
        SubPackage.Instance:StartDownloadPackage(0)
    end
end

function InitWGCtrl:Delete()
	self.ctrl_state = CTRL_STATE.STOP
end

function InitWGCtrl:DestroyLoadingView()
	self.loading_view:Destroy()
end

-- 上报连接失败
function InitWGCtrl:ReportConnectFaild(reason, url, data, retry_times)
	-- ReportManager:Step(reason, nil, nil, nil, nil,
	-- 	UnityEngine.SystemInfo.deviceName,
	-- 	UnityEngine.SystemInfo.deviceModel,
	-- 	UnityEngine.SystemInfo.deviceUniqueIdentifier, url, data, retry_times)
end

--------------------------------------------------------------------热更相关--------------------------------------------------------------
local function CalculateUpdateSize(update_bundles)
	local size = 0
	for i, v in ipairs(update_bundles) do
		local bundle_size = 0
		if IsLuaAssetBundle(v) then
			bundle_size = ResMgr:GetLuaBundleSize(v) or 0
		else
			bundle_size = ResMgr:GetBundleSize(v) or 0
		end
		size = size + bundle_size
	end

	return size
end

function InitWGCtrl:ShowUpdateBundles(update_bundles, need_restart, call_back)
	if #update_bundles <= 0 then
		ResMgr:OnHotUpdateLuaComplete()
		call_back()
		return
	end

	self:SetDownloading(true)

	local total_file_size = CalculateUpdateSize(update_bundles)
	local total_downloaded_size = 0
	local retry_count = 0

	local update_callback = function (downloaded_size, download_speed)
		local tip = string.format("新版本更新: %0.1fMB/%0.1fMB, 速度: %0.2fMB/s", (total_downloaded_size + downloaded_size) / 1024 / 1024,
			total_file_size / 1024 / 1024, download_speed / 1024 / 1024)

		self:SetPercent((total_downloaded_size + downloaded_size) / total_file_size)
		self:SetText(tip)
	end

	-- ReportManager:Step(Report.STEP_UPDATE_ASSET_BUNDLE)
	local complete_callback = nil
	complete_callback = function (error_bundles, downloaded_size)
		total_downloaded_size = total_downloaded_size + downloaded_size
		if #error_bundles <= 0 then
			-- 下载成功, 还原网络下载地址
			if retry_count > 0 then
				ResMgr:SetDownloadingURL(GLOBAL_CONFIG.param_list.cdn_url)
			end

			ResMgr:OnHotUpdateLuaComplete()
			self:SetDownloading(false)
			-- ReportManager:Step(Report.STEP_UPDATE_ASSET_BUNDLE_COMPLETE)
			if need_restart then
				GameRestart()
			else
				call_back()
			end
			return
		end

		if IS_LOCLA_WINDOWS_DEBUG_EXE then
			self.loading_view:ShowMessageBox("网络错误", "Windows包热更新失败，请关闭重启", "退出", function()
				UnityEngine.Application.Quit()
			end)
			return
		end

		retry_count = retry_count + 1
		-- 最多重试8次
		if retry_count > 8 then
			-- ReportManager:Step(Report.STEP_UPDATE_ASSET_BUNDLE_FAIL)
			self:SetText("网络异常，正在为您尝试重新连接。。。")
			self.is_delay_time = true
			return
		end

		-- 切换下载地址
		if GLOBAL_CONFIG.param_list.cdn_url2 ~= nil and "" ~= GLOBAL_CONFIG.param_list.cdn_url2 then
			if retry_count % 2 == 1 then
				ResMgr:SetDownloadingURL(GLOBAL_CONFIG.param_list.cdn_url2)
			else
				ResMgr:SetDownloadingURL(GLOBAL_CONFIG.param_list.cdn_url)
			end
		end
		self:StartDownLoad(error_bundles, 5, update_callback, complete_callback)
	end

	self:StartDownLoad(update_bundles, 5, update_callback, complete_callback)
end

function InitWGCtrl:StartDownLoad(bundles, group_count, _update_call_back, _complete_callback)
	local bundle_group_list = {}
	for i = 1, group_count do
		bundle_group_list[i] = {downloaded_size = 0, bundles = {}}
	end

	local complete_group_count = 0
	local cur_group_index = 1
	local error_bundles = {}

	if #bundles <= 0 then
		_complete_callback(error_bundles, 0)
		return
	end

	for i = 1, #bundles do
		local bundle = bundles[i]
		table.insert(bundle_group_list[cur_group_index].bundles, bundle)
		cur_group_index = cur_group_index + 1
		if cur_group_index > group_count then
			cur_group_index = 1
		end
	end

	local last_calulate_speed_time = GlobalUnityTime
	local last_total_downloaded_size = 0
	local download_speed = 0

	local update_callback = function (group_index, downloaded_size)
		bundle_group_list[group_index].downloaded_size = downloaded_size

		local total_downloaded_size = 0
		for i = 1, group_count do
			total_downloaded_size = total_downloaded_size + bundle_group_list[i].downloaded_size
		end

		local elapse_time = GlobalUnityTime - last_calulate_speed_time
		if elapse_time >= 1 then
			download_speed = (total_downloaded_size - last_total_downloaded_size) / elapse_time
			last_total_downloaded_size = total_downloaded_size
			last_calulate_speed_time = GlobalUnityTime
		end

		_update_call_back(total_downloaded_size, download_speed)
	end

	local complete_callback = function (group_index, downloaded_size)
		bundle_group_list[group_index].downloaded_size = downloaded_size
		complete_group_count = complete_group_count + 1

		if complete_group_count >= group_count then
			local total_downloaded_size = 0
			for i = 1, group_count do
				total_downloaded_size = total_downloaded_size + bundle_group_list[i].downloaded_size
			end
			_complete_callback(error_bundles, total_downloaded_size)
		end
	end

	for i = 1, group_count do
		self:DoDownLoad(i, bundle_group_list[i].bundles, 1, 0, error_bundles, update_callback, complete_callback)
	end
end

function InitWGCtrl:DoDownLoad(group_index, update_bundles, index, total_downloaded_size, error_bundles, update_callback, complete_callback)
	if index > #update_bundles then
		complete_callback(group_index, total_downloaded_size)
		return
	end

	local bundle = update_bundles[index]
	local file_size = 0
	if IsLuaAssetBundle(bundle) then
		file_size = ResMgr:GetLuaBundleSize(bundle) or 0
	else
		file_size = ResMgr:GetBundleSize(bundle) or 0
	end

	ResMgr:UpdateBundle(bundle,
		function(progress, download_speed)
			local downloaded_size = total_downloaded_size + file_size * progress
			update_callback(group_index, downloaded_size)
		end,
		function(error_msg)
			if error_msg ~= nil and error_msg ~= "" then
				table.insert(error_bundles, bundle)
			else
				total_downloaded_size = total_downloaded_size + file_size
			end
			self:DoDownLoad(group_index, update_bundles, index + 1, total_downloaded_size, error_bundles, update_callback, complete_callback)
		end)
end

function InitWGCtrl:UpdateDelayTime(now_time, elapse_time)
	local initctrl_delay_time = UtilU3d.GetCacheData("initctrl_delay_time")
	if nil == initctrl_delay_time then
		UtilU3d.CacheData("initctrl_delay_time", now_time + 2)
	end
	
	if initctrl_delay_time and now_time > initctrl_delay_time then
		self.is_delay_time = false
		UtilU3d.CacheData("initctrl_delay_time", nil)
		GameRestart()
	end
end

function InitWGCtrl:UpdateDelayTime2(now_time, elapse_time)
	local initctrl_call_delay_time = UtilU3d.GetCacheData("initctrl_call_delay_time")
	if nil == initctrl_call_delay_time then
		UtilU3d.CacheData("initctrl_call_delay_time", now_time + 1)
	end

	if initctrl_call_delay_time and now_time > initctrl_call_delay_time then
		self.is_delay_time2 = false
		UtilU3d.CacheData("initctrl_call_delay_time", nil)
		self:SendRequest()
	end
end

function InitWGCtrl.NoticeAuditChange(callback)
	audit_change_callback_list[callback] = true
end

function InitWGCtrl.UnNoticeAuditChange(callback)
	audit_change_callback_list[callback] = nil
end

return InitWGCtrl
