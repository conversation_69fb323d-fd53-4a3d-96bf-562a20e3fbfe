SHJ_SL_TYPE = {
	CW = 1,	
	TS = 2,	
}

function ShanHaiJingView:SLReleaseCallBack()
	if self.sl_item_group then
		for k1,v1 in pairs(self.sl_item_group) do
			v1:DeleteMe()
		end
		self.sl_item_group = nil
	end
end

function ShanHaiJingView:SLShowIndexCallBack(show_type)
	self.node_list.sl_title_txt.text.text = Language.ShanHaiJing['ViewTitle_' .. self.show_index%10] or ''  
	self.sl_show_body_type = show_type
	self.node_list.sl_card_list.scroll_rect.verticalNormalizedPosition = 1
   	-- BossWGCtrl.Instance:SendBossTuJianReq(BOSS_CARD_OPERA_TYPE.BOSS_CARD_OPERA_TYPE_ALL_INFO,self.sl_show_body_type)
   	self:SLFlush()
end

function ShanHaiJingView:SLLoadCallBack()
	self.AddClickEventListener(self.node_list.sl_btn_jiban,BindTool.Bind(self.ClickOpenZuheView,self))
end

function ShanHaiJingView:SLFlush()
	local card_cfg_list = self.base_data:GetSLCfgByType(self.sl_show_body_type)
	if not card_cfg_list then return end
	
	if not self.sl_item_group then
		self.sl_item_group = {}
	end

	-- 不够的创建
	for i,v in ipairs(card_cfg_list) do
		if not self.sl_item_group[i] then
			self.sl_item_group[i] = SLGroupRender.New()
			self.sl_item_group[i]:LoadAsset(self.ui_config[1],'layout_sl_group_render', self.node_list.sl_layout_group.transform)
		end
		self.sl_item_group[i]:SetVisible(true)
		self.sl_item_group[i]:SetCardType(self.sl_show_body_type)
		self.sl_item_group[i]:SetData(v)
	end

	-- 多余的隐藏掉
	for i=#card_cfg_list + 1,#self.sl_item_group do
		self.sl_item_group[i]:SetVisible(false)
	end

	self:SLFlushOther(card_cfg_list)
end

function ShanHaiJingView:SLFlushOther(card_cfg_list)
	local view_info = self.base_data:GetSLViewInfo(self.sl_show_body_type)
	if not view_info then return end

	self.node_list.sl_capacity_num.text.text = view_info.capability
	self.node_list.sl_get_num.text.text = view_info.active_num .. '/' .. view_info.total_num
	self.node_list.sl_progress.slider.value = view_info.active_num/view_info.total_num

	--刷新属性
	local sl_card_attr_list = self.base_data:GetSLAttrCfgByType(self.sl_show_body_type)
	sl_card_attr_list = AttributeMgr.GetAttributteByClass(sl_card_attr_list)
	local attr_name = Language.Common.AttrName
	local index = 0
	for k,v in pairs(sl_card_attr_list) do
		if v ~= 0 then
			self.node_list['sl_attr_' .. v].text.text = attr_name[k] .. view_info.attr_list[k]
			index = index + 1 
		end
	end
	for i=1,6 do
		self.node_list['sl_attr_' .. i]:SetActive(index >= i)
	end
end

-------------------------大列表-------------------------------------
SLGroupRender = SLGroupRender or BaseClass(BaseRender)
function SLGroupRender:__delete()
	if self.sl_item_group then
		for k1,v1 in pairs(self.sl_item_group) do
			v1:DeleteMe()
		end
		self.sl_item_group = nil
	end
end

function SLGroupRender:SetCardType(card_type)
	self.card_type = card_type
end

function SLGroupRender:OnFlush()
	local data = self.data 
	if not data then return end
	
	local card_server_list = ShanHaiJingWGData.Instance:GetSLServerData(self.card_type)
	local card_has_active_list = card_server_list.card_has_active_flag
	local active_num = 0

	if not self.sl_item_group then
		self.sl_item_group = {}
	end
	-- 不够的创建
	for i,v in ipairs(data) do
		if not self.sl_item_group[i] then
			self.sl_item_group[i] = SLItemRender.New()
			self.sl_item_group[i]:LoadAsset("uis/view/shj_ui_prefab", "sl_card_item", self.view.transform)
		end
		self.sl_item_group[i]:SetVisible(true)
		self.sl_item_group[i]:SetCardType(self.card_type)
		self.sl_item_group[i]:SetData(v)
		if card_has_active_list[v.card_seq+1] == 1 then
			active_num = active_num + 1
		end
	end

	-- 多余的隐藏掉
	for i=#data + 1,#self.sl_item_group do
		self.sl_item_group[i]:SetVisible(false)
	end
	self.node_list.title.text.text = data[1].series_name
	ChangeToQualityText(self.node_list.title,data[1].color)
	self.node_list.title_num.text.text = string.format(Language.ShanHaiJing.Title_1,active_num,#data)   
	ChangeToQualityText(self.node_list.title_num,GameEnum.EQUIP_COLOR_GREEN)
	-- 计算宽高 
	self.node_list.item.layout_element.minHeight = #data ~= 0 and (60 + math.ceil(#data/3)*(124 + 5)) or 189

end
---------------------------小item---------------------------------------
SLItemRender = SLItemRender or BaseClass(BaseRender) 
function SLItemRender:SetCardType(card_type)
	self.card_type = card_type
end

function SLItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.st_item,BindTool.Bind(self.ClickActive,self))
end

-- 点击激活
function SLItemRender:ClickActive()
   	BossWGCtrl.Instance:SendBossTuJianReq(BOSS_CARD_OPERA_TYPE.BOSS_CARD_OPERA_TYPE_ACTIVE,self.card_type,self.data.card_seq)
end

function SLItemRender:OnFlush()
	local data = self.data 
	if not data then return end
	local view_info = ShanHaiJingWGData.Instance:GetSLItemViewInfo(self.card_type,data.card_seq,data)
	self.node_list.name.text.text = data.name
	self.node_list.remind.image.enabled = view_info.remind
	XUI.SetGraphicGrey(self.node_list.layout_gray,not view_info.has_active)
	self.node_list.attr_text_1.text.text = view_info.attr_txt_1
	self.node_list.attr_text_2.text.text = view_info.attr_txt_2

	-- 底图头像刷新
	self.node_list.bg_1.image:LoadSprite(ResPath.GetSHJImgPath("sl_circle_" .. data.color))
	self.node_list.bg_2:SetActive(data.color > 1)
	if data.color > 1 then
		self.node_list.bg_2.image:LoadSprite(ResPath.GetSHJImgPath("sl_flower_" .. data.color))
	end
	self.node_list.icon.image:LoadSprite(ResPath.GetSHJImgPath("sl_head_" .. data.head_id))
	self.node_list.item_bg.image:LoadSprite(ResPath.GetSHJImgPath("sl_bg_" .. data.color))

end