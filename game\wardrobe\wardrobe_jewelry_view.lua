local NOW_JEWELRY_PART_TYPE = {
	[1] = SHIZHUANG_TYPE.MASK,
	[2] = SHIZHUANG_TYPE.BELT,
	[3] = SHIZHUANG_TYPE.WING,
	[4] = SHIZHUANG_TYPE.FABAO,
}

local NOW_JEWELRY_SHOW_PART_TYPE = {
	[SHIZHUANG_TYPE.MASK] = 1001,
	[SHIZHUANG_TYPE.BELT] = 1002,
	[SHIZHUANG_TYPE.WING] = 1003,
	[SHIZHUANG_TYPE.FABAO] = 1004,
}

-- 刷新所有特效信息
function WardrobeView:FlushWardrobeJewelry(param_t)
	for k, v in pairs(param_t) do
		if k == "all" then
			if v.item_id ~= nil and v.item_id ~= "" then
				self:SetFashionJumpItemId(tonumber(v.item_id))
			end

			if v.open_param ~= nil and v.open_param ~= "" then
				self:FlushWardrobeJewelryList(tonumber(v.open_param))
			else
				self:FlushWardrobeJewelryList()
			end
		end
	end
end

-- 刷新时装数据
function WardrobeView:FlushWardrobeJewelryList(jump_index)
	local type_data = {}
	for i, v in ipairs(Language.Wardrobe.NameSubTable2) do
		local data = {}
		data.name = v
		data.table_index = NOW_JEWELRY_TYPE[i]
		data.is_wing = false
		data.is_red = WardrobeWGData.Instance:GetWardrobeAllPartRemindByIndex(data.table_index, data.is_wing)

		if jump_index == nil and data.is_red then
			jump_index = i
		end
		table.insert(type_data, data)
	end

	self:FlushFashionShowTypeList(type_data, jump_index)
end

-- 选择类型回调
function WardrobeView:SelectWardrobeJewelryTypeCB(cell_index, filter_index, jump_item_id)
	local list = NewAppearanceWGData.Instance:GetSortShowListByTabIndex(NOW_JEWELRY_TYPE[cell_index], true)
	local cache_data = self:GetUseIndexByPartType(NOW_JEWELRY_PART_TYPE[cell_index])

	local new_list = {}
	-- 取小翅膀
	if NOW_JEWELRY_TYPE[cell_index] == NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_WING then
		for i, v in ipairs(list) do
			local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(v.part_type, v.index)
			if fashion_cfg and fashion_cfg.is_wing ~= 1 then
				table.insert(new_list, v)
			end
		end
	else
		new_list = list
	end

	local filter_list = {}
	if filter_index == 1 then
		filter_list = new_list
	else
		for i, v in ipairs(new_list) do
			local is_act = NewAppearanceWGData.Instance:GetFashionIsAct(v.part_type, v.index)

			if filter_index == 2 then
				if is_act then
					table.insert(filter_list, v)
				end
			elseif filter_index == 3 then
				if not is_act then
					table.insert(filter_list, v)
				end
			elseif filter_index == 4 then
				local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(v.part_type, v.index)
				if fashion_cfg and fashion_cfg.get_msg == 1 then
					table.insert(filter_list, v)
				end
			end
		end
	end

	local jump_index = nil
	if jump_item_id ~= nil then
		for i, v in ipairs(filter_list) do
			if v.active_stuff_id == jump_item_id then
				self.select_fashion_part_index = nil
				jump_index = i
				break
			end
		end
	end

	if jump_index == nil then
		for i, v in ipairs(filter_list) do
			if v.is_remind and self.select_fashion_part_index == nil then
				jump_index = i
				break
			end
		end
	end
	
	if jump_index == nil then
		for i, v in ipairs(filter_list) do
			if cache_data and cache_data.used_index ~= 0 and v.index == cache_data.used_index then
				jump_index = i
				break
			end
		end
	end

	self:FlushFashionPartList(
		filter_list, 
		jump_index, 
		false, 
		NOW_JEWELRY_PART_TYPE[cell_index],
		NOW_JEWELRY_PART_TYPE[cell_index] == SHIZHUANG_TYPE.WING,
		NOW_JEWELRY_PART_TYPE[cell_index] ~= SHIZHUANG_TYPE.WING
	)
end

-- 选择数据回调
function WardrobeView:SelectWardrobeJewelryCB(select_fashion_part_index, fashion_part_data, is_select)
	local fashion_data = nil

	if fashion_part_data then
		fashion_data = self:AssembleFashionData(fashion_part_data.part_type, fashion_part_data.index)
		fashion_data.show_item_id = fashion_part_data.active_stuff_id
		fashion_data.type = WARDROBE_PART_TYPE.FASHION
		local item_cfg = ItemWGData.Instance:GetItemConfig(fashion_part_data.active_stuff_id)
		
		if item_cfg then
			fashion_data.show_color = item_cfg.color
		end
	end

	self:FlushNowPartMessage(fashion_data, is_select)
	self:FlushWardrobeJewelryPartItemList()
end

function WardrobeView:FlushWardrobeJewelryPartItemList()
	local list = self:GetNowCacheUseList()

	if not list then
		return
	end

	local item_list = {}
	for i, v in ipairs(list) do
		if NOW_JEWELRY_SHOW_PART_TYPE[v.part_type] then
			local data = {}
			data.show_item_id = v.stuff_id or 0
			data.part_type = v.part_type
			data.used_index = v.used_index
			data.state = REWARD_STATE_TYPE.UNDONE
			data.sort_num = NOW_JEWELRY_SHOW_PART_TYPE[v.part_type]

			if v.used_index ~= 0  then
				local is_act = NewAppearanceWGData.Instance:GetFashionIsAct(v.part_type, v.used_index)
				data.state = is_act and REWARD_STATE_TYPE.FINISH or REWARD_STATE_TYPE.UNDONE
			end

			table.insert(item_list, data)
		end
	end

	table.sort(item_list, SortTools.KeyLowerSorter("sort_num"))
	self:FlushPrePartItemList(item_list)
end