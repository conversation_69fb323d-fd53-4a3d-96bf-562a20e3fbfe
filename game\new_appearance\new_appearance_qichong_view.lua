local HUALING_OPA_TYPE = {
    UP = 0,
    AUTO_UP = 1,
}

function NewAppearanceWGView:QiChongInitView()
    if self.is_load_qichong then
        return
    end
    
    if self.qc_star_list == nil then
        self.qc_star_list = {}
        for i = 1, 5 do
            self.qc_star_list[i] = self.node_list["qc_star_" .. i]
        end
    end

    if not self.qc_upstar_attr_list then
        self.qc_upstar_attr_list = {}
        local parent_node = self.node_list["qc_upstar_attr_list"]
        local attr_num = parent_node.transform.childCount
        for i = 1, attr_num do
            local cell = QiChongAddAttrRender.New(parent_node:FindObj("attr_" .. i))
            cell:SetIndex(i)
            self.qc_upstar_attr_list[i] = cell
        end
    end

    if not self.qc_uplevel_attr_list then
        self.qc_uplevel_attr_list = {}
        local parent_node = self.node_list["qc_uplevel_attr_list"]
        local attr_num = parent_node.transform.childCount
        for i = 1, attr_num do
            local cell = QiChongAddAttrRender.New(parent_node:FindObj("attr_" .. i))
            cell:SetIndex(i)
            self.qc_uplevel_attr_list[i] = cell
        end
    end

    if not self.qc_hualing_attr_list then
        self.qc_hualing_attr_list = {}
        local parent_node = self.node_list["qc_hualing_attr_list"]
        local attr_num = parent_node.transform.childCount
        for i = 1, attr_num do
            local cell = QiChongAddAttrRender.New(parent_node:FindObj("attr_" .. i))
            cell:SetIndex(i)
            self.qc_hualing_attr_list[i] = cell
        end
    end

    if not self.qc_hualing_item then
        self.qc_hualing_item = ItemCell.New(self.node_list.qc_hualing_item)
    end

    if self.qc_stuff_item == nil then
        self.qc_stuff_item = ItemCell.New(self.node_list["qc_stuff_item"])
    end

    if self.kun_upgrade_stuff_item == nil then
        self.kun_upgrade_stuff_item = ItemCell.New(self.node_list["kun_upgrade_stuff_item"])
    end

    -- 技能
    self.qichong_skill_item_list = {}
    self.kun_skill_item_list = {}
    for i = 1, 5 do
        self.qichong_skill_item_list[i] = SpecialQiChongSkillRender.New(self.node_list["qc_skill_" .. i])
        self.kun_skill_item_list[i] = SpecialQiChongSkillRender.New(self.node_list["kun_skill_" .. i])
        self.qichong_skill_item_list[i]:SetIndex(i)
        self.kun_skill_item_list[i]:SetIndex(i)
    end

    self.qc_uplevel_stuff_item_list = {}
    for i = 1, 2 do
        self.qc_uplevel_stuff_item_list[i] = ItemCell.New(self.node_list["qc_uplevel_stuff_icon" .. i])
    end

    XUI.AddClickEventListener(self.node_list["qc_btn_use"], BindTool.Bind(self.OnClickQiChongUsed, self, true))
    XUI.AddClickEventListener(self.node_list["qc_btn_reset"], BindTool.Bind(self.OnClickQiChongUsed, self, false))
    XUI.AddClickEventListener(self.node_list["qc_btn_upstar"], BindTool.Bind(self.OnClickQiChongUpStar, self))
    XUI.AddClickEventListener(self.node_list["qc_btn_uplevel"], BindTool.Bind(self.OnClickQiChongUpLevel, self))
    XUI.AddClickEventListener(self.node_list["qc_btn_upgrade"], BindTool.Bind(self.OnClickKunUpLevel, self))
    XUI.AddClickEventListener(self.node_list["kun_btn_reslove"], BindTool.Bind(self.OnClickKunReslove, self))
    XUI.AddClickEventListener(self.node_list["qc_btn_equip"], BindTool.Bind(self.OnClickOpenQiChongEquip, self))
    XUI.AddClickEventListener(self.node_list["btn_hualing_up"], BindTool.Bind(self.OnClickHuaLingUp, self, HUALING_OPA_TYPE.UP))
    XUI.AddClickEventListener(self.node_list["btn_hualing_auto_up"], BindTool.Bind(self.OnClickHuaLingUp, self, HUALING_OPA_TYPE.AUTO_UP))

    for i = 1, 2 do
        XUI.AddClickEventListener(self.node_list["qc_uplevel_stuff_icon" .. i], BindTool.Bind(self.OnClickQiChongStuff, self, i))
    end

    self.is_load_qichong = true

    if not self.qc_select_item_grid then
        self.qc_select_item_grid = {}
        self.qc_cell_list = {}

        for i = 1, GameEnum.ITEM_COLOR_XUAN_QING do
			self.qc_select_item_grid[i] = {}
			self.qc_select_item_grid[i].text_name = self.node_list["qc_btn_text" .. i]
            self.qc_select_item_grid[i].text_name_hl = self.node_list["qc_btn_text_hl" .. i]
			self.qc_select_item_grid[i].list = self.node_list["qc_list_".. i]
            self.node_list["qc_select_btn" .. i].accordion_element.isOn = false
            self.node_list["qc_select_btn" .. i]:SetActive(false)
		    self.node_list["qc_list_" .. i]:SetActive(false)
			self.node_list["qc_select_btn" .. i].accordion_element:AddValueChangedListener(BindTool.Bind(self.OnClickQCSelectTogHandler, self, i))
		end
    end
end

function NewAppearanceWGView:QiChongReleaseCallBack()
    self:QiChongKunClearCountDown()
    self:ClearQiChongSliderTween()
    self.is_load_qichong = nil
    self.qc_star_list = nil
    self.qichong_select_type = nil
    self.qichong_select_index = nil
    self.qichong_select_cell_index = nil
    self.qichong_func_type = nil
    self.qc_select_grid_type = nil
    self.qc_select_grid_type_cache = nil
    self.qc_select_cell_index = nil
    self.qc_image_id_cache = nil
    if self.qc_upstar_attr_list then
        for k,v in pairs(self.qc_upstar_attr_list) do
            v:DeleteMe()
        end
        self.qc_upstar_attr_list = nil
    end

    if self.qc_uplevel_attr_list then
        for k,v in pairs(self.qc_uplevel_attr_list) do
            v:DeleteMe()
        end
        self.qc_uplevel_attr_list = nil
    end

    if self.qc_hualing_attr_list then
        for k,v in pairs(self.qc_hualing_attr_list) do
            v:DeleteMe()
        end
        self.qc_hualing_attr_list = nil
    end

    if self.qc_hualing_item then
        self.qc_hualing_item:DeleteMe()
        self.qc_hualing_item = nil
    end

    if self.qichong_skill_item_list then
        for k,v in pairs(self.qichong_skill_item_list) do
            v:DeleteMe()
        end
        self.qichong_skill_item_list = nil
    end

    if self.kun_skill_item_list then
        for k,v in pairs(self.kun_skill_item_list) do
            v:DeleteMe()
        end
        self.kun_skill_item_list = nil
    end

    if self.qc_uplevel_stuff_item_list then
        for k, v in pairs(self.qc_uplevel_stuff_item_list) do
            v:DeleteMe()
        end

        self.qc_uplevel_stuff_item_list = nil
    end

    if self.qc_stuff_item then
        self.qc_stuff_item:DeleteMe()
        self.qc_stuff_item = nil
    end

    if self.kun_upgrade_stuff_item then
        self.kun_upgrade_stuff_item:DeleteMe()
        self.kun_upgrade_stuff_item = nil
    end

    if self.qc_select_item_grid then
		self.qc_select_item_grid = nil
	end

    if self.qc_cell_list then
		for k,v in pairs(self.qc_cell_list) do
			for k1,v1 in pairs(v) do
				v1:DeleteMe()
			end
			self.qc_cell_list[k] = nil
		end

		self.qc_cell_list = nil
	end

    self:RemoveQCEffectDelayTimer()
end

function NewAppearanceWGView:QiChongShowIndexCallBack()
    local qc_type
    if self.show_index == TabIndex.new_appearance_mount_upstar
    or self.show_index == TabIndex.new_appearance_mount_upgrade
    or self.show_index == TabIndex.new_appearance_mount_hualing then
        qc_type = MOUNT_LINGCHONG_APPE_TYPE.MOUNT
    elseif self.show_index == TabIndex.new_appearance_lingchong_upstar
    or self.show_index == TabIndex.new_appearance_lingchong_upgrade
    or self.show_index == TabIndex.new_appearance_lingchong_hualing then
        qc_type = MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG
    elseif self.show_index == TabIndex.new_appearance_kun_upstar
    or self.show_index == TabIndex.new_appearance_kun_upgrade
    or self.show_index == TabIndex.new_appearance_kun_hualing then
        qc_type = MOUNT_LINGCHONG_APPE_TYPE.KUN
    end

    if self.qichong_select_type ~= qc_type then
        self.qichong_select_index = nil
        self.qichong_select_cell_index = nil
    end

    self.qc_select_grid_type = nil
    self.qc_select_cell_index = nil

    self.qichong_select_type = qc_type
    self.qichong_func_type = self.show_index % 10

    local is_kun_type = qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN
    self.node_list["qichong_upgrade_part"]:CustomSetActive(not is_kun_type)
    self.node_list["kun_upgrade_part"]:CustomSetActive(is_kun_type)
    self.node_list["kun_btn_reslove"]:CustomSetActive(is_kun_type and self.qichong_func_type == QICHONG_FUNC_TYPE.UPLEVEL)

    local show_equip_btn = MountLingChongEquipWGData.Instance:GetCanShowEquipBtn(qc_type)
    self.node_list["qc_btn_equip"]:CustomSetActive(show_equip_btn and not is_kun_type)
    if show_equip_btn then
        self.node_list.qc_btn_equip_text.text.text = Language.NewAppearance.QCEquipBtnText[self.qichong_select_type]
    end

    self:FlushQiChongEquipRemind()

    local is_lingchong_type = qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG
    local btn_text = is_lingchong_type and Language.NewAppearance.HuanHua or Language.NewAppearance.ChengQi
    self.node_list["qc_btn_use_text"].text.text = btn_text
end

function NewAppearanceWGView:FlushQiChongEquipRemind()
    if not self.is_load_qichong then
        return
    end

    local remind_name
    if self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
        remind_name = RemindName.MountEquipTotal
    elseif self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        remind_name = RemindName.LingChongEquipTotal
    elseif self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        remind_name = RemindName.HuaKunEquipTotal
    end

    local remind_num = RemindManager.Instance:GetRemind(remind_name)
    self.node_list["qc_btn_equip_remind"]:CustomSetActive(remind_num >= 1)
end


function NewAppearanceWGView:QiChongOnItemSelectCB(cell)
    local data = cell and cell:GetData()
    if data == nil then
        return
    end

    local image_id = data.image_id
    local cell_index = cell:GetIndex()
    -- if self.qichong_select_index == image_id and self.qichong_select_cell_index == cell_index then
    --     return
    -- end

    self:ClearQiChongSliderTween()
    if self.qc_image_id_cache ~= image_id then
        self:StopQiChongAutoUpGrade()
        self.qc_image_id_cache = image_id
    end
    self.qichong_select_index = image_id
    self.qichong_select_cell_index = cell_index
    self:QiChongFlushSelectView()

    local act_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByImageId(self.qichong_select_type, image_id)
    if act_cfg then
        local appe_image_id = act_cfg.appe_image_id or act_cfg.active_id
        self:ShowQiChongModel(self.qichong_select_type, appe_image_id)
    end
end

function NewAppearanceWGView:QiChongFlushView()
    local grid_data = NewAppearanceWGData.Instance:GetQCSortSpecialShowList(self.qichong_select_type, self.qichong_func_type)

    local load_info = {}
    for i = 1, GameEnum.ITEM_COLOR_XUAN_QING do
		self.node_list["qc_select_btn" .. i]:SetActive(nil ~= grid_data[i])

        if nil == grid_data[i] then
            self.node_list["qc_list_" .. i]:CustomSetActive(false)
        end

        self.node_list["qc_btn_text" .. i].text.text = Language.NewAppearance.AppearanceColorTypeName[i]
        self.node_list["qc_btn_text_hl" .. i].text.text = Language.NewAppearance.AppearanceColorTypeName[i]

        local remind = false
        if grid_data[i] then
            for k, v in pairs(grid_data[i]) do
                if v.is_remind then
                    remind = true
                    break
                end
            end
        end

        self.node_list["qc_btn_remind" .. i]:CustomSetActive(remind)

        if self.qc_cell_list[i] ~= nil then
            if nil ~= grid_data[i] then
                local start_num = #self.qc_cell_list[i]
                local end_num = #grid_data[i]

                if start_num < end_num then
                    table.insert(load_info, {index = i, start_num = start_num, end_num = end_num, data_list = grid_data[i]})
                end
            end
        else
            if nil ~= grid_data[i] then
                table.insert(load_info, {index = i, start_num = 1, end_num = #grid_data[i], data_list = grid_data[i]})
            end
        end
	end

    local function load_complete_func()
        self:FlushQCGridListCell()
        self:SetQCGridItemSelect()
    end

    if IsEmptyTable(load_info) then
        load_complete_func()
    else
        self:LoadQCridListCell(load_info, function()
            load_complete_func()
        end)
    end
end

function NewAppearanceWGView:QiChongFlushSelectView()
    local show_upstar = self.qichong_func_type == QICHONG_FUNC_TYPE.UPSTAR
    local show_uplevel = self.qichong_func_type == QICHONG_FUNC_TYPE.UPLEVEL
    local show_hualing = self.qichong_func_type == QICHONG_FUNC_TYPE.HUALING

    self.node_list["qc_upstar_part"]:CustomSetActive(show_upstar)
    self.node_list["qc_uplevel_part"]:CustomSetActive(show_uplevel)
    self.node_list["qc_hualing_part"]:CustomSetActive(show_hualing)

    -- if not show_upstar then
    --     self.node_list["qc_name_stars_list"]:CustomSetActive(false)
    -- end 

    if show_upstar then
        self:QiChongFlushUpStarView()
    elseif show_uplevel then
        if self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
            self:KunFlushUpLevelView()
        else
            self:QiChongFlushUpLevelView()
        end
    elseif show_hualing then
        self:QiChongFlushHuaLingView()
    end

    self:QiChongCommonPart()
end

-- 骑宠升星
function NewAppearanceWGView:QiChongFlushUpStarView()
    local info = NewAppearanceWGData.Instance:GetSpecialQiChongInfo(self.qichong_select_type, self.qichong_select_index)
    if info == nil then
        return
    end

    local act_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByImageId(self.qichong_select_type, self.qichong_select_index)
    if act_cfg == nil then
        return
    end

    local star_level = info.star_level or info.star
    local max_level = NewAppearanceWGData.Instance:GetSpecialQiChongMaxStarLevel(self.qichong_select_type, self.qichong_select_index)
    local is_max = star_level >= max_level

    -- local next_upstar_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongUpStarCfg(self.qichong_select_type, self.qichong_select_index, star_level + 1)
    -- local is_max = next_upstar_cfg == nil
    local is_act = NewAppearanceWGData.Instance:GetSpecialQiChongIsAct(self.qichong_select_type, self.qichong_select_index)

    -- 属性
    local attr_list, capability = {}, 0
    if self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        attr_list, capability = NewAppearanceWGData.Instance:GetKunAttrListAndCap(self.qichong_func_type, self.qichong_select_index)
    else
        attr_list, capability = NewAppearanceWGData.Instance:GetSpecialQiChongAttrListAndCap(self.qichong_select_type, self.qichong_func_type, self.qichong_select_index)
    end

    local need_show_up_effect = false
    if nil ~= self.qichong_select_type_cache and nil ~= self.qichong_func_type_cache and nil ~= self.qichong_select_index_cache and nil ~= self.qichong_select_levle_cache then
        if (self.qichong_select_type_cache == self.qichong_select_type) and (self.qichong_func_type_cache == self.qichong_func_type) and 
        (self.qichong_select_index_cache == self.qichong_select_index) and (star_level - self.qichong_select_levle_cache == 1) then
            need_show_up_effect = true
        end
    end

    local attr_data = NewAppearanceWGData.Instance:GetSpecialQiChongPerAddAttr(self.qichong_select_type, self.qichong_select_index, star_level)
    local insert_key = -1
    for key, value in pairs(attr_list) do
        if EquipmentWGData.Instance:GetAttrIsPer(value.attr_str) then
            value.attr_name = ToColorStr(EquipmentWGData.Instance:GetAttrName(value.attr_str, false, false), COLOR3B.GLOD)
            if insert_key < 0 then
                insert_key = key
            end
        end
    end

    if not IsEmptyTable(attr_data) then
        if attr_data.cur_value > 0 or attr_data.attr_next_value > 0 then
            table.insert(attr_list, insert_key > 0 and insert_key or #attr_list + 1, attr_data)
        end
    end

    for k,v in pairs(self.qc_upstar_attr_list) do
        if attr_list[k] then
            v:SetData(attr_list[k])
        else
            v:SetData({})
        end
        if need_show_up_effect then
            v:PlayAttrValueUpEffect()
        end
    end

    -- for k,v in pairs(self.qc_upstar_attr_list) do
    --     v:SetData(attr_list[k])

    --     if need_show_up_effect then
    --         v:PlayAttrValueUpEffect()
    --     end
    -- end

    self.qichong_select_type_cache = self.qichong_select_type
    self.qichong_func_type_cache = self.qichong_func_type
    self.qichong_select_index_cache = self.qichong_select_index
    self.qichong_select_levle_cache = star_level

    self.node_list["qc_cap_value"].text.text = capability

    -- 限时
    self:QiChongKunClearCountDown()
    local end_time = info.invalid_time or 0
    end_time = end_time - TimeWGCtrl.Instance:GetServerTime()
    -- 限时道具激活的限时
    local is_limit_time_type = act_cfg.timed_type == 2
    self.node_list["qc_us_cost_title"].text.text = Language.NewAppearance.CostTitle[is_limit_time_type and 2 or 1]
    
    if is_limit_time_type and end_time > 0 then
        self.node_list["qc_limited_time_desc"]:CustomSetActive(true)
        self.node_list["qc_limited_time_desc"].text.text = string.format(Language.NewAppearance.LimitTimeDesc[1], TimeUtil.FormatSecondDHM8(end_time))
        self.qck_quest = CountDown.Instance:AddCountDown(end_time, 0.5,
                                BindTool.Bind(self.QiChongKunFlushLimitTime, self),
                                BindTool.Bind(self.Flush, self))
    else
        self.node_list["qc_limited_time_desc"]:CustomSetActive(false)
    end

    -- 升级
    self.node_list["qc_stars_list"]:CustomSetActive(is_act and not is_limit_time_type)
    -- self.node_list["qc_name_stars_list"]:CustomSetActive(is_act and not is_limit_time_type)
    if is_act and not is_limit_time_type then
        -- local star_res_list = GetStarImgResByStar(star_level)

        local star_res_list = GetTwenTyStarImgResByStar(star_level - 1)
        for k,v in pairs(self.qc_star_list) do
            v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
            self.node_list["qc_name_star_" .. k].image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
        end
    end

    -- 消耗
    local is_remind = false
    local need_item_id, need_num = 0, 1

    if is_limit_time_type or (not is_max and not is_act) then
        need_item_id = act_cfg.active_item_id or act_cfg.active_need_item_id
    elseif not is_max and is_act then
        local up_star_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongUpStarCfg(self.qichong_select_type, self.qichong_select_index)
        need_item_id = up_star_cfg.need_item or up_star_cfg.item_id
        need_num = NewAppearanceWGData.Instance:GetSpecialQiChongUpStarCostNum(self.qichong_select_type, self.qichong_select_index, star_level)
    end

    if need_item_id > 0 then
        self.qc_stuff_item:SetData({item_id = need_item_id})
        local num = ItemWGData.Instance:GetItemNumInBagById(need_item_id)
        is_remind = num >= need_num
        local str_color = is_remind and COLOR3B.D_GREEN or COLOR3B.RED
        self.node_list["qc_need_num"].text.text = string.format("%s/%s", ToColorStr(num, str_color), need_num)
        self.node_list["qc_btn_upstar_remind"]:CustomSetActive(is_remind)
    end

    local btn_str = ""
    if is_act and is_limit_time_type then
        btn_str = Language.NewAppearance.UpStarBtnDesc[3]
    elseif is_act then
        btn_str = Language.NewAppearance.UpStarBtnDesc[2]
    else
        btn_str = Language.NewAppearance.UpStarBtnDesc[1]
    end
    self.node_list["qc_btn_upstar_text"].text.text = btn_str

    self.node_list["qc_max_star_flag"]:CustomSetActive(is_act and is_max and not is_limit_time_type)
    self.node_list["qc_star_no_max_part"]:CustomSetActive(not is_max or not is_act or is_limit_time_type)
end

function NewAppearanceWGView:QiChongKunClearCountDown()
	if CountDown.Instance:HasCountDown(self.qck_quest) then
        CountDown.Instance:RemoveCountDown(self.qck_quest)
        self.qck_quest = nil
    end
end

function NewAppearanceWGView:QiChongKunFlushLimitTime(elapse_time, total_time)
	local time = total_time - elapse_time
	if time > 0 then
		self.node_list["qc_limited_time_desc"].text.text = string.format(Language.NewAppearance.LimitTimeDesc[1], TimeUtil.FormatSecondDHM8(time))
	else
		self.node_list["qc_limited_time_desc"].text.text = ""
	end
end

-- 骑宠升阶
function NewAppearanceWGView:QiChongFlushUpLevelView()
    local info = NewAppearanceWGData.Instance:GetSpecialQiChongInfo(self.qichong_select_type, self.qichong_select_index)
    if info == nil then
        return
    end
    
    local grade = info.grade
    local grade_exp_val = info.grade_exp_val
    local need_cfg_grade = grade > 1 and grade or 1
    local cur_grade_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongUpGradeCfg(self.qichong_select_type, self.qichong_select_index, need_cfg_grade)
    local next_grade_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongUpGradeCfg(self.qichong_select_type, self.qichong_select_index, need_cfg_grade + 1)
    local is_max = next_grade_cfg == nil or cur_grade_cfg == nil
    local is_act = grade > 0
    -- 属性
    local attr_list, capability = NewAppearanceWGData.Instance:GetSpecialQiChongAttrListAndCap(self.qichong_select_type, self.qichong_func_type, self.qichong_select_index)

    local need_show_up_effect = false
    if nil ~= self.grade_qichong_select_type_cache and nil ~= self.grade_qichong_func_type_cache and nil ~= self.grade_qichong_select_index_cache and nil ~= self.grade_qichong_type_level_cache then
        if (self.grade_qichong_select_type_cache == self.qichong_select_type) and (self.grade_qichong_func_type_cache == self.qichong_func_type) and
        (self.grade_qichong_select_index_cache == self.qichong_select_index) and (grade - self.grade_qichong_type_level_cache == 1) then
            need_show_up_effect = true
        end
    end

    for k,v in pairs(self.qc_uplevel_attr_list) do
        v:SetData(attr_list[k])

        if need_show_up_effect then
            v:PlayAttrValueUpEffect()
        end
    end

    self.grade_qichong_select_type_cache = self.qichong_select_type
    self.grade_qichong_func_type_cache = self.qichong_func_type
    self.grade_qichong_select_index_cache = self.qichong_select_index
    self.grade_qichong_type_level_cache = grade

    self.node_list["qc_cap_value"].text.text = capability

    -- 进度
    if not is_act and cur_grade_cfg then
        self.node_list["qc_progress_text"].text.text = "0/" .. cur_grade_cfg.upgrade_need_exp
        self.node_list["qc_progress"].slider.value = 0
        XUI.SetButtonEnabled(self.node_list["qc_btn_uplevel"], false)
        self.node_list["qc_btn_uplevel_text"].text.text = Language.NewAppearance.UpGradeBtnDesc[1]
    elseif is_max then
        if self.is_qc_auto_upgrade then
            self:PlayQiChongSliderTween(0, grade_exp_val, cur_grade_cfg.upgrade_need_exp)
        else
            self.node_list["qc_progress"].slider.value = 1
        end

        self.node_list["qc_progress_text"].text.text = "-/-"
    else
        local upgrade_need_exp = cur_grade_cfg.upgrade_need_exp
        if self.is_qc_auto_upgrade then
            self.node_list["qc_progress_text"].text.text = grade_exp_val .. "/" .. upgrade_need_exp
            local add_grade = grade - self.qc_old_grade
            self.qc_old_grade = add_grade
            self:PlayQiChongSliderTween(add_grade, grade_exp_val, upgrade_need_exp)
        else
            self.node_list["qc_progress_text"].text.text = grade_exp_val .. "/" .. upgrade_need_exp
            self.node_list["qc_progress"].slider.value = grade_exp_val / upgrade_need_exp
            self.node_list["qc_btn_uplevel_text"].text.text = Language.NewAppearance.UpGradeBtnDesc[2]
            MainuiWGCtrl.Instance:DelayShowCachePower(0)
        end

        XUI.SetButtonEnabled(self.node_list["qc_btn_uplevel"], true)
    end

    if not is_act or is_max then
        self.node_list["qc_btn_upleve_remind"]:CustomSetActive(false)
    else
        local is_remind = NewAppearanceWGData.Instance:GetSpecialQiChongSingleUpLevelRemind(self.qichong_select_type, self.qichong_select_index)
        self.node_list["qc_btn_upleve_remind"]:CustomSetActive(is_remind and not self.is_qc_auto_upgrade) 
    end

    -- 消耗
    if is_max then
        self.node_list["qc_uplevel_stuff_icon1"]:CustomSetActive(false)
        self.node_list["qc_uplevel_stuff_icon2"]:CustomSetActive(false)
    else
        local stuff_list = NewAppearanceWGData.Instance:GetSpecialQiChongUpGradeStuff(self.qichong_select_type)
        for i = 1, 2 do
            local data = stuff_list[i]
            if data then
                local item_num = ItemWGData.Instance:GetItemNumInBagById(data.item_id)
                local bundle, asset = ItemWGData.Instance:GetTipsItemIcon(data.item_id)
                -- self.node_list["qc_uplevel_stuff_cost" .. i].text.text = item_num
                self.node_list["qc_uplevel_stuff_icon" .. i]:CustomSetActive(true)
                -- self.node_list["qc_uplevel_stuff_icon" .. i].image:LoadSprite(bundle, asset)

                self.qc_uplevel_stuff_item_list[i]:SetFlushCallBack(function ()
                    local right_text = ToColorStr(item_num, item_num > 0 and COLOR3B.D_GREEN or COLOR3B.D_RED)
                    self.qc_uplevel_stuff_item_list[i]:SetRightBottomColorText(right_text)
                    self.qc_uplevel_stuff_item_list[i]:SetRightBottomTextVisible(true)
                end)
        
                self.qc_uplevel_stuff_item_list[i]:SetData({item_id = data.item_id})
            else
                self.node_list["qc_uplevel_stuff_icon" .. i]:CustomSetActive(false)
            end
        end

        local is_remind = NewAppearanceWGData.Instance:GetSpecialQiChongSingleUpLevelRemind(self.qichong_select_type, self.qichong_select_index)
        self.node_list["qc_btn_upleve_remind"]:CustomSetActive(is_act and is_remind and not self.is_qc_auto_upgrade)
    end

    self.node_list["qc_max_level_flag"]:CustomSetActive(is_max)
    self.node_list["qc_level_no_max_part"]:CustomSetActive(not is_max)
end

-- 鲲 - 进阶
function NewAppearanceWGView:KunFlushUpLevelView()
    local info = NewAppearanceWGData.Instance:GetSpecialQiChongInfo(self.qichong_select_type, self.qichong_select_index)
    if info == nil then
        return
    end

    local is_act = info.is_act
    local level = info.level
    local cur_cfg = NewAppearanceWGData.Instance:GetKunUpGradeCfg(self.qichong_select_index, level)
    local next_cfg = NewAppearanceWGData.Instance:GetKunUpGradeCfg(self.qichong_select_index, level + 1)
    local is_max = next_cfg == nil

    local attr_list, capability = NewAppearanceWGData.Instance:GetKunAttrListAndCap(self.qichong_func_type, self.qichong_select_index)
    local need_show_up_effect = false

    if nil ~= self.kun_func_type_cache and nil ~= self.kun_select_index_cache and nil ~= self.kun_type_level_cache then
        if (self.kun_func_type_cache == self.qichong_func_type) and (self.kun_select_index_cache == self.qichong_select_index) and (level - self.kun_type_level_cache == 1) then
            need_show_up_effect = true
        end
    end

    for k,v in pairs(self.qc_uplevel_attr_list) do
        v:SetData(attr_list[k])

        if need_show_up_effect then
            v:PlayAttrValueUpEffect()
        end
    end

    self.kun_func_type_cache = self.qichong_func_type
    self.kun_select_index_cache = self.qichong_select_index
    self.kun_type_level_cache = level

    self.node_list["qc_cap_value"].text.text = capability

    local is_remind = false
    if not is_max then
        local material_cfg = NewAppearanceWGData.Instance:GetKunMaterial()
        self.kun_upgrade_stuff_item:SetData({item_id = material_cfg[1].param1})
        local num = NewAppearanceWGData.Instance:GetKunUpGradeExp()
        is_remind = num >= cur_cfg.need_num
        local str_color = is_remind and COLOR3B.D_GREEN or COLOR3B.RED
        local num_str = CommonDataManager.ConverMoneyByThousand(num)
        self.node_list["kun_upgrade_need_num"].text.text = string.format("%s/%s", ToColorStr(num_str, str_color), cur_cfg.need_num)
        XUI.SetButtonEnabled(self.node_list["qc_btn_upgrade"], is_act)
    end

    self.node_list["qc_max_level_flag"]:CustomSetActive(is_max)
    self.node_list["qc_level_no_max_part"]:CustomSetActive(not is_max)
    self.node_list["qc_btn_upgrade_remind"]:CustomSetActive(is_act and is_remind)

    local reslove_remind = NewAppearanceWGData.Instance:GetKunResolveRemind()
    self.node_list["kun_btn_reslove_remind"]:CustomSetActive(reslove_remind)
end

-- 公共部分
function NewAppearanceWGView:QiChongCommonPart()
    -- 名字
    local act_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByImageId(self.qichong_select_type, self.qichong_select_index)
    if act_cfg then
        local item_name = ItemWGData.Instance:GetItemName(act_cfg.active_item_id or act_cfg.active_need_item_id)
        self.node_list["qc_select_name"].text.text = item_name
    end

    local is_act = NewAppearanceWGData.Instance:GetSpecialQiChongIsAct(self.qichong_select_type, self.qichong_select_index)
    local grade = NewAppearanceWGData.Instance:GetSpecialQiChongGrade(self.qichong_select_type, self.qichong_select_index)
    if is_act and grade > 0 then
        self.node_list["qc_grade"].text.text = string.format(Language.NewAppearance.GradeStr, grade)
    else
        self.node_list["qc_grade"].text.text = ""
    end

    local cur_is_kun = self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.KUN
    -- 使用按钮
    local is_used = false
    if cur_is_kun then
        local used_id = NewAppearanceWGData.Instance:GetKunUsedId()
        is_used = self.qichong_select_index == used_id
    else
        local all_info = NewAppearanceWGData.Instance:GetQiChongInfo(self.qichong_select_type)
        if all_info and act_cfg then
            is_used = act_cfg.appe_image_id == all_info.used_imageid
        end
    end

    self.node_list["qc_btn_use"]:CustomSetActive(is_act and not is_used)
    self.node_list["qc_btn_reset"]:CustomSetActive(is_act and is_used)
    -- 技能
    local skill_list = NewAppearanceWGData.Instance:GetSpecialQiChongSkillList(self.qichong_select_type, self.qichong_select_index)
    local no_empty_skill_list = not IsEmptyTable(skill_list)
    self.node_list["qc_skill_total_part"]:CustomSetActive(no_empty_skill_list)
    self.node_list["qc_skill_part"]:CustomSetActive(not cur_is_kun)
    self.node_list["kun_skill_part"]:CustomSetActive(cur_is_kun)

    if cur_is_kun then
        for k,v in pairs(self.kun_skill_item_list) do
            if skill_list and skill_list[k] then
                v:SetData({cfg = skill_list[k], qc_type = self.qichong_select_type})
            else
                v:SetData(nil)
            end
        end
    else
        for k,v in pairs(self.qichong_skill_item_list) do
            if skill_list and skill_list[k - 1] then
                v:SetData({cfg = skill_list[k - 1], qc_type = self.qichong_select_type})
            else
                v:SetData(nil)
            end
        end
    end
end

-- 使用 / 重置
function NewAppearanceWGView:OnClickQiChongUsed(use_this)
    local appe_id = 0
    -- 重置
    if not use_this then
        local qc_type = self.qichong_select_type
        if self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
            qc_type = MOUNT_LINGCHONG_APPE_TYPE.MOUNT
        end

        local grade = NewAppearanceWGData.Instance:GetBaseQiChongGrade(qc_type)
        local base_cfg = NewAppearanceWGData.Instance:GetQiChongBaseCfgByGrade(qc_type, grade)
        if base_cfg then
            appe_id = base_cfg.appe_image_id
        end

        if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
            NewAppearanceWGCtrl.Instance:SendMountReq(MOUNT_OPERA_TYPE.USE_IMAGE, appe_id, -1)
        elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
            NewAppearanceWGCtrl.Instance:SendLingChongReq(LINGCHONG_OPERA_TYPE.USE_IMAGE, appe_id)
        end
    else
        -- 使用
        local act_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByImageId(self.qichong_select_type, self.qichong_select_index)
        if act_cfg then
            appe_id = act_cfg.appe_image_id or act_cfg.active_id
        end

        if self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
            NewAppearanceWGCtrl.Instance:SendMountReq(MOUNT_OPERA_TYPE.USE_IMAGE, appe_id, self.qichong_select_index)
        elseif self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
            NewAppearanceWGCtrl.Instance:SendLingChongReq(LINGCHONG_OPERA_TYPE.USE_IMAGE, appe_id)
        elseif self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
            NewAppearanceWGCtrl.Instance:SendKunOperaReq(KUN_OPERA_TYPE.USE_KUN, self.qichong_select_index)
        end

        self:PlayUseEffect()
    end
end

-- 升星
function NewAppearanceWGView:OnClickQiChongUpStar()
    local info = NewAppearanceWGData.Instance:GetSpecialQiChongInfo(self.qichong_select_type, self.qichong_select_index)
    if info == nil then
        return
    end

    local act_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByImageId(self.qichong_select_type, self.qichong_select_index)
    if act_cfg == nil then
        return
    end

    local star_level = info.star_level or info.star
    local is_act = NewAppearanceWGData.Instance:GetSpecialQiChongIsAct(self.qichong_select_type, self.qichong_select_index)
    local max_level = NewAppearanceWGData.Instance:GetSpecialQiChongMaxStarLevel(self.qichong_select_type, self.qichong_select_index)
    local is_max = star_level >= max_level
    local need_item_id, need_num = 0, 1
    local is_limit_time = act_cfg.timed_type == 2

    if is_limit_time or (not is_max and not is_act) then
        need_item_id = act_cfg.active_item_id or act_cfg.active_need_item_id
    elseif not is_max and is_act then
        local up_star_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongUpStarCfg(self.qichong_select_type, self.qichong_select_index)
        need_item_id = up_star_cfg.need_item or up_star_cfg.item_id
        need_num = NewAppearanceWGData.Instance:GetSpecialQiChongUpStarCostNum(self.qichong_select_type, self.qichong_select_index, star_level)
    end

    if need_item_id == 0 then
        return
    end

    local num = ItemWGData.Instance:GetItemNumInBagById(need_item_id)
    if num >= need_num then
        if not is_act then
            local item_cfg = ItemWGData.Instance:GetItemConfig(need_item_id)
		    local bag_index = ItemWGData.Instance:GetItemIndex(need_item_id)
		    BagWGCtrl.Instance:SendUseItem(bag_index, 1, item_cfg.sub_type, item_cfg.need_gold)
            return
        end

        local ok_func = function ()
            if self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
                NewAppearanceWGCtrl.Instance:SendMountReq(MOUNT_OPERA_TYPE.SPECIAL_IMAGE_UP_STAR, self.qichong_select_index)
            elseif self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
                NewAppearanceWGCtrl.Instance:SendLingChongReq(LINGCHONG_OPERA_TYPE.SPECIAL_IMAGE_UP_STAR, self.qichong_select_index, need_item_id)
            elseif self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
                NewAppearanceWGCtrl.Instance:SendKunOperaReq(KUN_OPERA_TYPE.UP_STAR, self.qichong_select_index)
            end
        end
    
        if is_act and is_limit_time then
            self:OpenIncreaseTimeTips(need_item_id, ok_func)
        else
            ok_func()
        end

        if not is_limit_time then
            self:PlayUpStarEffect()
        end
    else
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = need_item_id})
    end
end

-- 升阶 - 骑宠
function NewAppearanceWGView:OnClickQiChongUpLevel()
    if not self.is_qc_auto_upgrade then
        self:StartQiChongAutoUpGrade()
    else
        self:StopQiChongAutoUpGrade()
    end
end

-- 自动升阶
function NewAppearanceWGView:StartQiChongAutoUpGrade(no_tips)
    -- if self.is_qc_auto_upgrade then
    --     return
    -- end

    local info = NewAppearanceWGData.Instance:GetSpecialQiChongInfo(self.qichong_select_type, self.qichong_select_index)
    if info == nil then
        self:StopQiChongAutoUpGrade()
        return
    end

    local grade = info.grade
    local next_grade_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongUpGradeCfg(self.qichong_select_type, self.qichong_select_index, grade + 1)
    if next_grade_cfg == nil then
        self:StopQiChongAutoUpGrade()
        return
    end

    -- 升阶材料
    local stuff_list = NewAppearanceWGData.Instance:GetSpecialQiChongUpGradeStuff(self.qichong_select_type)
    local stuff_id, had_stuff = 0, false
    for i, v in ipairs(stuff_list) do
        if stuff_id == 0 then
            stuff_id = v.item_id
        end

		local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
		if item_num > 0 then
            stuff_id = v.item_id
			had_stuff = true
            break
		end
	end

    if not had_stuff then
        if not no_tips then
            TipWGCtrl.Instance:OpenItemTipGetWay({item_id = stuff_id})
        end

        self:StopQiChongAutoUpGrade()
        return
    end

    self.qc_old_grade = grade
    self.is_qc_auto_upgrade = true
    self.node_list["qc_btn_uplevel_text"].text.text = Language.NewAppearance.UpGradeBtnDesc[3]

    MainuiWGCtrl.Instance:CreateCacheTable()
    if self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
		NewAppearanceWGCtrl.Instance:SendMountReq(MOUNT_OPERA_TYPE.UPGRADE_SPECIAL_IMG, self.qichong_select_index, stuff_id)
	elseif self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
		NewAppearanceWGCtrl.Instance:SendLingChongReq(LINGCHONG_OPERA_TYPE.UPGRADE_SPECIAL_IMG, self.qichong_select_index, stuff_id)
	end
end

-- 停止升阶
function NewAppearanceWGView:StopQiChongAutoUpGrade()
    self.is_qc_auto_upgrade = nil
    if self.qichong_select_type ~= MOUNT_LINGCHONG_APPE_TYPE.KUN then
        self.node_list["qc_btn_uplevel_text"].text.text = Language.NewAppearance.UpGradeBtnDesc[2]
    end
end

function NewAppearanceWGView:ClearQiChongSliderTween()
    if self.qc_slider_tween then
        self.qc_slider_tween:Kill()
        self.qc_slider_tween = nil
    end
end

function NewAppearanceWGView:PlayQiChongSliderTween(add_grade, grade_exp_val, upgrade_need_exp)
    if add_grade == 0 and grade_exp_val == upgrade_need_exp then
        self:ClearQiChongSliderTween()
        local slider = self.node_list["qc_progress"].slider
        local time = tonumber(string.format("%.2f", (1 - slider.value) * 0.5))
        self.qc_slider_tween = slider:DOValue(1, time)
        self.qc_slider_tween:OnComplete(function ()
			MainuiWGCtrl.Instance:DelayShowCachePower(0) --延时调用
            self:PlayUpGradeEffect()
		end)

        self:StopQiChongAutoUpGrade()
        return
    end 

    self.qc_slider_tween_func = function (progress)
        self:ClearQiChongSliderTween()
        if progress <= 0 then
            if self.is_qc_auto_upgrade then
                self:StartQiChongAutoUpGrade(true)
            end
            
            return
        end

        local slider = self.node_list["qc_progress"].slider
		if progress > 1 then
			local time = tonumber(string.format("%.2f", (1 - slider.value) * 0.5))
			self.qc_slider_tween = slider:DOValue(1, time)
		else
			local time = tonumber(string.format("%.2f", (progress - slider.value) * 0.5))
			self.qc_slider_tween = slider:DOValue(progress, time)
		end

        progress = progress - 1
        self.qc_slider_tween:OnComplete(function ()
			if progress >= 0 then
				slider.value = 0
				self:PlayUpGradeEffect()
			end
			
			if progress < 1 then
				MainuiWGCtrl.Instance:DelayShowCachePower(0)
			end

			self.qc_slider_tween_func(progress)
		end)
    end

    local total_progress = add_grade + grade_exp_val / upgrade_need_exp
    self.qc_slider_tween_func(total_progress)
end

function NewAppearanceWGView:OnClickQiChongStuff(index)
    local stuff_list = NewAppearanceWGData.Instance:GetSpecialQiChongUpGradeStuff(self.qichong_select_type)
    local data = stuff_list[index]
    if not data then
        return
    end

    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = data.item_id})
end

-- 升阶 - 鲲
function NewAppearanceWGView:OnClickKunUpLevel()
    local info = NewAppearanceWGData.Instance:GetSpecialQiChongInfo(self.qichong_select_type, self.qichong_select_index)
    if info == nil then
        return
    end

    local is_act = info.is_act
    local level = info.level
    local next_cfg = NewAppearanceWGData.Instance:GetKunUpGradeCfg(self.qichong_select_index, level + 1)
    if next_cfg == nil or not is_act then
        return
    end

    local cur_cfg = NewAppearanceWGData.Instance:GetKunUpGradeCfg(self.qichong_select_index, level)
    local num = NewAppearanceWGData.Instance:GetKunUpGradeExp()
    if num >= cur_cfg.need_num then
        NewAppearanceWGCtrl.Instance:SendKunOperaReq(KUN_OPERA_TYPE.UP_LV, self.qichong_select_index)
    else
        local material_cfg = NewAppearanceWGData.Instance:GetKunMaterial()
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = material_cfg[1].param1})
    end
end

-- 分解
function NewAppearanceWGView:OnClickKunReslove()
    NewAppearanceWGCtrl.Instance:OpenHuaKunBagView()
end

-- 骑宠装备
function NewAppearanceWGView:OnClickOpenQiChongEquip()
	if self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
		FunOpen.Instance:OpenViewByName(GuideModuleName.LingChongEquipView)
	elseif self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
		FunOpen.Instance:OpenViewByName(GuideModuleName.MountEquipView)
    elseif self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
		FunOpen.Instance:OpenViewByName(GuideModuleName.HuaKunEquipView)
	end
end

-----------------------------------------------------化灵-------------------------------------------------------------------
function NewAppearanceWGView:QiChongFlushHuaLingView()
    local info = NewAppearanceWGData.Instance:GetSpecialQiChongInfo(self.qichong_select_type, self.qichong_select_index)
    if info == nil then
        return
    end

    local act_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByImageId(self.qichong_select_type, self.qichong_select_index)
    local is_time_limit_item = (act_cfg and act_cfg.timed_type or 1) == 2

    local hualing_cfg = NewAppearanceWGData.Instance:GetHuaLingCfg(self.qichong_select_type, self.qichong_select_index)
    if IsEmptyTable(hualing_cfg) then
        if not is_time_limit_item then
            print_error("缺失化灵配置通知策划检查 type =", self.qichong_select_type, "id =", self.qichong_select_index)
        end

        self.node_list["qc_hualing_part"]:CustomSetActive(false)
        return
    end

    local hualing_open_cfg = NewAppearanceWGData.Instance:GetHuaLingOpenCfg(self.qichong_select_type, hualing_cfg.image_id)
    if IsEmptyTable(hualing_open_cfg) then
        if not is_time_limit_item then
            print_error("缺失化灵开启配置通知策划检查", self.qichong_select_type, "id = ", self.qichong_select_index)
        end

        self.node_list["qc_hualing_part"]:CustomSetActive(false)
        return
    end

    local grade = self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.KUN and info.level or info.grade
    local is_active = grade > 0 and grade >= hualing_open_cfg.limit_grade
    local is_max_grade = NewAppearanceWGData.Instance:IsHuaLingMaxLevel(self.qichong_select_type, self.qichong_select_index)

    if is_active then
        if not is_max_grade then
            local item_num = ItemWGData.Instance:GetItemNumInBagById(hualing_cfg.cost_item_id)
            local enough = item_num >= hualing_cfg.cost_item_num

            -- self.qc_hualing_item:SetFlushCallBack(function ()
            --     local right_text = ToColorStr(item_num .. "/" .. hualing_cfg.cost_item_num, enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
            --     self.qc_hualing_item:SetRightBottomColorText(right_text)
            --     self.qc_hualing_item:SetRightBottomTextVisible(true)
            -- end)

            local str_color = enough and COLOR3B.D_GREEN or COLOR3B.RED
            self.node_list.qc_hualing_need_num.text.text = string.format("%s/%s", ToColorStr(item_num, str_color), hualing_cfg.cost_item_num)
            self.qc_hualing_item:SetData({item_id = hualing_cfg.cost_item_id})
            self.node_list.btn_hualing_up_remind:CustomSetActive(enough)
            self.node_list.btn_hualing_auto_up_remind:CustomSetActive(enough)
        end

        local level = NewAppearanceWGData.Instance:GetHuaLingLevel(self.qichong_select_type, hualing_cfg.seq)
        self.node_list.qc_hualing_level_text.text.text = string.format(Language.NewAppearance.HuaLingLevel, level)
    else
        self.node_list.qc_hualing_limit_text.text.text = string.format(Language.NewAppearance.HuaLingActiveDesc, hualing_open_cfg.limit_grade)
    end

    self.node_list.qc_hualing_limit_desc:CustomSetActive(not is_active)
    self.node_list.qc_max_hualinglevel_flag:CustomSetActive(is_active and is_max_grade)
    self.node_list.qc_hualinglevel_no_max_part:CustomSetActive(is_active and not is_max_grade)
    self.node_list.qc_hualing_level_part:CustomSetActive(is_active)

    -- 属性
    local attr_list, capability = {}, 0
    if self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        attr_list, capability = NewAppearanceWGData.Instance:GetKunAttrListAndCap(self.qichong_func_type, self.qichong_select_index)
    else
        attr_list, capability = NewAppearanceWGData.Instance:GetSpecialQiChongAttrListAndCap(self.qichong_select_type, self.qichong_func_type, self.qichong_select_index)
    end
    
    for k,v in pairs(self.qc_hualing_attr_list) do
        v:SetData(attr_list[k])
    end

    self.node_list["qc_cap_value"].text.text = capability
end

function NewAppearanceWGView:OnClickHuaLingUp(opa_type)
    local cfg = NewAppearanceWGData.Instance:GetHuaLingCfg(self.qichong_select_type, self.qichong_select_index)

    if not IsEmptyTable(cfg) then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg.cost_item_id)
        local enough = item_num >= cfg.cost_item_num
        
        if enough then
            NewAppearanceWGCtrl.Instance:SendHuaLingReq(HUALING_OPERATE_TYPE.UPLEVEL, self.qichong_select_type, cfg.seq, opa_type)
        else
            TipWGCtrl.Instance:OpenItem({item_id = cfg.cost_item_id})
        end
    end
end

-------------------------------------------------------------------------
function NewAppearanceWGView:OnClickQCSelectTogHandler(index, isOn)
    local jump_cell_index = 1

    if self.qc_select_grid_type_cache == index and self.qc_select_cell_index and self.qc_select_cell_index > 0 then
        jump_cell_index = self.qc_select_cell_index
    end

    self.qc_select_grid_type = index
    self.qc_select_grid_type_cache = index

    local grid_data = NewAppearanceWGData.Instance:GetQCSortSpecialShowList(self.qichong_select_type, self.qichong_func_type)
    local cell_data_list = grid_data[index]

    if not IsEmptyTable(cell_data_list) then
        local cur_select_data = cell_data_list[jump_cell_index]

        if nil == cur_select_data or not cur_select_data.is_remind then
            for k,v in ipairs(cell_data_list) do
                if v.is_remind then
                    jump_cell_index = k
                    break
                end
            end
        end
    end

    local cell_item = ((self.qc_cell_list or {})[index] or {})[jump_cell_index]

    if nil ~= cell_item then
        local tog = (cell_item.view or {}).toggle

        if nil ~= tog then
            if tog.isOn then
                self:OnClickQCGridItem(self.qc_cell_list[index][jump_cell_index], jump_cell_index, true)
            else
                -- 这里延时0.1秒，父节点没激活情况下设置isOn不生效
                self:RemoveQCEffectDelayTimer()
                self.show_qc_delay_timer = GlobalTimerQuest:AddDelayTimer(function ()
                    tog.isOn = true
                end, 0.1)
            end
        end
    end
end

--移除回调
function NewAppearanceWGView:RemoveQCEffectDelayTimer()
    if self.show_qc_delay_timer then
        GlobalTimerQuest:CancelQuest(self.show_qc_delay_timer)
        self.show_qc_delay_timer = nil
    end
end

function NewAppearanceWGView:OnClickQCGridItem(item, cell_index, is_on)
    if not is_on or not item or IsEmptyTable(item.data) then
        return
    end

    self.qc_select_cell_index = cell_index
    self:QiChongOnItemSelectCB(item)
end

function NewAppearanceWGView:LoadQCridListCell(load_info, load_complete_func)
    local res_async_loader = AllocResAsyncLoader(self, "appearance_qichong_item")

    res_async_loader:Load("uis/view/new_appearance_ui_prefab", "new_appearance_fs_item", nil,
    function(new_obj)
        for k, v in pairs(load_info) do
            if nil == self.qc_cell_list[v.index] then
                self.qc_cell_list[v.index] = {}
            end
        
            for i = v.start_num, v.end_num do
                local obj = ResMgr:Instantiate(new_obj)
                local obj_transform = obj.transform
                obj_transform:SetParent(self.qc_select_item_grid[v.index].list.transform, false)
                obj:GetComponent("Toggle").group = self.qc_select_item_grid[v.index].list.toggle_group
                local item_render = NewAppearanceQiChongRender.New(obj)
                table.insert(self.qc_cell_list[v.index], item_render)
                obj:SetActive(false)
                obj:GetComponent("Toggle"):AddValueChangedListener(BindTool.Bind(self.OnClickQCGridItem, self, item_render, #self.qc_cell_list[v.index]))
    
                if k == #load_info and i == v.end_num then
                    if load_complete_func then
                        load_complete_func()
                    end
                end
            end
        end
    end)
end

function NewAppearanceWGView:FlushQCGridListCell()
    local grid_data = NewAppearanceWGData.Instance:GetQCSortSpecialShowList(self.qichong_select_type, self.qichong_func_type)

    for k, v in pairs(grid_data) do
        if self.qc_cell_list ~= nil and self.qc_cell_list[k] ~= nil then
            for i, u in ipairs(self.qc_cell_list[k]) do
                u:SetActive(nil ~= v[i])

                if nil ~= v[i] then
                    u:SetData(v[i])
                end
            end
        end
    end
end

function NewAppearanceWGView:SetQCGridItemSelect()
    local select_tog_index = self:GetQCGridListTogSelect()
    if self.qc_select_grid_type_cache and select_tog_index ~= self.qc_select_grid_type_cache then
        if self.node_list["qc_select_btn" .. self.qc_select_grid_type_cache] then
            self.node_list["qc_select_btn" .. self.qc_select_grid_type_cache].accordion_element.isOn = false
        end
    elseif self.qc_select_grid_type == nil and select_tog_index == self.qc_select_grid_type_cache then
        if self.node_list["qc_select_btn" .. select_tog_index] then
            self.node_list["qc_select_btn" .. select_tog_index].accordion_element:Refresh()
        end
    end

    if self.node_list["qc_select_btn" .. select_tog_index] and self.node_list["qc_select_btn" .. select_tog_index].accordion_element.isOn then
        self:OnClickQCSelectTogHandler(select_tog_index, true)
    else
        if self.node_list["qc_select_btn" .. select_tog_index] then
            self.node_list["qc_select_btn" .. select_tog_index].accordion_element.isOn = true
        end
    end
end

function NewAppearanceWGView:GetQCGridListTogSelect()
    local grid_data = NewAppearanceWGData.Instance:GetQCSortSpecialShowList(self.qichong_select_type, self.qichong_func_type)
    if self.qc_select_grid_type and grid_data[self.qc_select_grid_type] then
        for k, v in pairs(grid_data[self.qc_select_grid_type]) do
            if v.is_remind then
                return self.qc_select_grid_type
            end
        end
    end

    local cur_is_kun = self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.KUN
    local used_grid_type = -1
    local used_select_cell_index = -1
    local used_image_id = nil

    if cur_is_kun then
        used_image_id = NewAppearanceWGData.Instance:GetKunUsedId()
    else
        local all_info = NewAppearanceWGData.Instance:GetQiChongInfo(self.qichong_select_type)
        if all_info then
            used_image_id = all_info.used_imageid
        end
    end

    local select_grid_type = self.qc_select_grid_type or -1
    local select_select_cell_index = -1
    local default_select_grid_type = -1
    for k, v in pairs(grid_data) do
        for i, u in pairs(v) do
            if default_select_grid_type < 0 or u.color < default_select_grid_type then
                default_select_grid_type = u.color
            end

            if u.appe_image_id == used_image_id then
                used_grid_type = u.color
                used_select_cell_index = i
            end

            if u.is_remind and (((select_grid_type > 0) and (u.color < select_grid_type)) or select_grid_type < 0) then
                select_grid_type = u.color
                select_select_cell_index = i
            end
        end
    end

    if select_grid_type > 0 then
        self.qc_select_cell_index = select_select_cell_index
        return select_grid_type
    else
        if used_grid_type > 0 then
            self.qc_select_cell_index = used_select_cell_index
            return used_grid_type
        else
            return default_select_grid_type
        end
    end

    return default_select_grid_type
end