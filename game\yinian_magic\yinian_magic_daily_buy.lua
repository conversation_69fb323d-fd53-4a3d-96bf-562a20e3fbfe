YiNianMagicDailyBuyView = YiNianMagicDailyBuyView or BaseClass(SafeBaseView)
function YiNianMagicDailyBuyView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/yinianmagic_prefab", "laout_magic_gift_view")
end

function YiNianMagicDailyBuyView:LoadCallBack()
    if not self.item_list then
        self.item_list = AsyncListView.New(ItemCell,self.node_list["ph_reward_list"])
        self.item_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list["buy_btn"], BindTool.Bind(self.OnClickBuy, self))
end

function YiNianMagicDailyBuyView:ReleaseCallBack()
    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end

    self.type_data = nil
end

function YiNianMagicDailyBuyView:SetDataAndOpen(seal_data)
	self.type_data = seal_data
    self:Open()
end

function YiNianMagicDailyBuyView:OnFlush()
	if not self.type_data then
		return
	end

    local info = YinianMagicWGData.Instance:GetTypeCfgByType(self.type_data)
    if IsEmptyTable(info) then
        return
    end

    local state = YinianMagicWGData.Instance:GetCurDailyGiftState()
    XUI.SetButtonEnabled(self.node_list["buy_btn"], not state)
    self.item_list:SetDataList(info.rmb_reward_item)
    local price = RoleWGData.GetPayMoneyStr(info.rmb_price, info.rmb_type, info.rmb_seq)
    self.node_list.price_count.text.text = price --购买价格
end

function YiNianMagicDailyBuyView:OnClickBuy()
    if not self.type_data then
		return
	end

    local info = YinianMagicWGData.Instance:GetTypeCfgByType(self.type_data)
    if IsEmptyTable(info) then
        return
    end

    RechargeWGCtrl.Instance:Recharge(info.rmb_price, info.rmb_type, info.rmb_seq)
end