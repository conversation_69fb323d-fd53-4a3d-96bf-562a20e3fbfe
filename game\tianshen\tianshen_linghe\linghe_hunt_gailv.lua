--灵核概率展示面板
LingHeHuntProbabilityView = LingHeHuntProbabilityView or BaseClass(SafeBaseView)

function LingHeHuntProbabilityView:__init()
    self.view_layer = UiLayer.Normal
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(690, 474)})
    self:AddViewResource(0, "uis/view/tianshen_linghe_ui_prefab", "layout_linghe_probability")
    self:SetMaskBg(true, true)
end

function LingHeHuntProbabilityView:ReleaseCallBack()
    if self.probability_list then
        self.probability_list:DeleteMe()
        self.probability_list = nil
    end
end

function LingHeHuntProbabilityView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.TianShenLingHe.ProbabilityTitle
     if not self.probability_list then
        self.probability_list = AsyncListView.New(LingHeProItemRender, self.node_list.ph_pro_list) 
    end
end

function LingHeHuntProbabilityView:OnFlush()
    local info = TianShenLingHeWGData.Instance:GetProbabilityInfo()
    self.probability_list:SetDataList(info)
end

----------------------------------------------------------------------------------
LingHeProItemRender = LingHeProItemRender or BaseClass(BaseRender)
function LingHeProItemRender:__delete()
    
end

function LingHeProItemRender:LoadCallBack()
    
end

function LingHeProItemRender:OnFlush()
    if not self.data then
        return
    end
    self.node_list.index_text.text.text = self.data.number
    local color = ItemWGData.Instance:GetItemColor(self.data.item_id)
    local item_name = ItemWGData.Instance:GetItemName(self.data.item_id)
    self.node_list.name_text.text.text = ToColorStr(item_name, color) 
    self.node_list.probability_text.text.text = self.data.random_count .. "%"
end
