﻿require("game/crossserver/kuafu_yezhanwangcheng/kf_yezhanwangcheng_wg_data")
require("game/crossserver/kuafu_yezhanwangcheng/kf_yezhanwangcheng_follow")
-- require("game/crossserver/kuafu_yezhanwangcheng/kf_yezhanwangcheng_view")
require("game/crossserver/kuafu_yezhanwangcheng/zc_result_view")
require("game/crossserver/kuafu_yezhanwangcheng/zc_rank_view")
require("game/crossserver/kuafu_yezhanwangcheng/zc_stage_view")

KuafuYeZhanWangChengWGCtrl = KuafuYeZhanWangChengWGCtrl or BaseClass(BaseWGCtrl)
function KuafuYeZhanWangChengWGCtrl:__init()
	if KuafuYeZhanWangChengWGCtrl.Instance then
		error("[KuafuYeZhanWangChengWGCtrl]:Attempt to create singleton twice!")
	end
	KuafuYeZhanWangChengWGCtrl.Instance = self

	self.data = KuafuYeZhanWangChengWGData.New()
	--self.view = KuafuYeZhanWangChengView.New()
	self.follow_view = KuafuYeZhanWangChengFollow.New()
	self.gift_view = SeeYeZhanWangChengRewardView.New()
	--self.allReward_view = YeZhanWangChengRewardView.New()
	self.zc_result_view = ZCResultView.New(GuideModuleName.ZCResultView)
	self.zc_rank_view = ZCRankView.New(GuideModuleName.ZCRankView)
	self.zc_stage_view = ZCStageView.New(GuideModuleName.ZCStageView)
	self:RegisterAllProtocals()
	self.last_score = 0
	self.show_score_list = {}
end

function KuafuYeZhanWangChengWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.follow_view:DeleteMe()
	self.follow_view = nil

	-- self.view:DeleteMe()
	-- self.view = nil

	self.gift_view:DeleteMe()
	self.gift_view = nil

	self.zc_stage_view:DeleteMe()
	self.zc_stage_view = nil

	self.zc_result_view:DeleteMe()
	self.zc_result_view = nil

	self.zc_rank_view:DeleteMe()
	self.zc_rank_view = nil

	if self.allReward_view then
		self.allReward_view:DeleteMe()
		self.allReward_view = nil
	end

	if self.show_score_tips then
		GlobalTimerQuest:CancelQuest(self.show_score_tips)
		self.show_score_tips = nil
	end

	self.show_score_list = {}
	KuafuYeZhanWangChengWGCtrl.Instance = nil
end

function KuafuYeZhanWangChengWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(SCNightFightSceneInfo, "OnSCNightFightSceneInfo") 						-- 7515 夜战王城人物信息
	self:RegisterProtocol(SCNightFightRankInfo, "OnSCNightFightRankInfo") 						-- 7517
	self:RegisterProtocol(SCFightBroadcastInfo, "OnSCFightBroadcastInfo") 				-- 7519 击杀广播
	self:RegisterProtocol(SCNightFightRoleInfo, "OnSCNightFightRoleInfo") 						-- 7518 不需要显示
	self:RegisterProtocol(SCFightPickBuffNotice, "OnSCFightPickBuffNotice") 				-- 7520
	self:RegisterProtocol(CSCrossNightFightOperate)
	self:RegisterProtocol(SCCrossNightFightInfo, "OnSCCrossNightFightInfo")
	self:RegisterProtocol(SCFightTianXuanBuffInfo, "OnSCFightTianXuanBuffInfo")


	-- self:RegisterProtocol(CSNightFightEnterReq)
end

-- function KuafuYeZhanWangChengWGCtrl:OpenView()
-- 	self.view:Open()
-- end
-- function KuafuYeZhanWangChengWGCtrl:Colse()
-- 	self.view:Open()
-- end

-- function KuafuYeZhanWangChengWGCtrl:Close()
-- 	self.view:Close()
-- end

function KuafuYeZhanWangChengWGCtrl:ColseFollowView()
	if self.follow_view:IsOpen() then
		self.follow_view:Close()
	end
	-- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(true)
	MainuiWGCtrl.Instance:ShowMainuiMenu(true)
	UiInstanceMgr.Instance:ColseFBStartDown()
end

--场景信息
function KuafuYeZhanWangChengWGCtrl:OnSCNightFightSceneInfo(protocol)
	self.data:NightFightSceneInfo(protocol)
	if Scene.Instance:GetSceneType() == SceneType.YEZHANWANGCHENGFUBEN then
		local scene_logic = Scene.Instance:GetSceneLogic()
		if scene_logic then
			scene_logic:SetActStatus()
		end
	end
	self.follow_view:Flush()
end

--排名数据
function KuafuYeZhanWangChengWGCtrl:OnSCNightFightRankInfo(protocol)
	local old_turn = self.data:GetCurTurn()
	self.data:NightFightRankInfo(protocol)
	if old_turn ~= protocol.turn then
		if self.follow_view:IsOpen() then
			self.follow_view:ChangeTurnView(protocol.turn)
			self.follow_view:ChangeCountDown()
			self.follow_view:ShowWaterEffect()
		end
	end
	if protocol.notify_reason == KuafuYeZhanWangChengWGData.NotifyReason.Update then
		ViewManager.Instance:FlushView(GuideModuleName.ZCRankView, nil, ZCRankView.FromView.YZWC)
	else
		if self.follow_view:IsOpen() then
			self.follow_view:ShowResult(protocol.turn)
		end
		local main_role = Scene.Instance:GetMainRole()
		if main_role:IsDead() then
			ViewManager.Instance:Close(GuideModuleName.FuhuoEquipFbView)
			FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.BACK_HOME, 0, -1)
		end
	end
	self.follow_view:Flush()
end

function KuafuYeZhanWangChengWGCtrl:OnSCNightFightRoleInfo(protocol)
	self.data:SetNightFightAllRoleScoreInfo(protocol)

	if not self:CheckActiIsOpen() then
		return
	end

	local role = Scene.Instance:GetRoleByObjId(protocol.obj_id)
	local main_role = Scene.Instance:GetMainRole()
	if main_role.vo.obj_id == protocol.obj_id then
		local self_info = KuafuYeZhanWangChengWGData.Instance:GetSetSelfInfo()
		local info = {}
		info.side_type = protocol.side_type
		info.obj_id = protocol.obj_id
		info.user_key = protocol.user_key
		info.score = protocol.score
		if protocol.in_init == 0 then
			if not self.show_score_tips then
				self.show_score_tips = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.ShowYZWCScoreRunShow, self), 0.5)
			end
			local show_num = protocol.score - self.last_score
			local jifen = Language.YeZhanWangCheng.JiFenStr
			local show_str = show_num > 0 and (jifen .. "+" .. show_num) or (jifen .. show_num)
			table.insert(self.show_score_list, show_str)
		end
		self.last_score = protocol.score
		info.multi_kill_role_num = protocol.multi_kill_role_num
		self.data:GetSetSelfInfo(info)
		-- 换边
		if self_info.side_type ~= info.side_type then
			self:UpdateSceneRoleInfo(info.side_type)
		end

		main_role:SetAttr("yzwc_score", protocol.score)
	end

	if role then
		role:SetAttr("yzwc_ico", protocol.side_type + 1)

		local vo = role:GetVo()
		if vo then
			--[[
			--考虑到红蓝两方的人在视野内一起刷到一个队伍并且一直在视野内，
			--强行刷新special_param避免打队友的情况
			local old_is_red_side = vo.special_param > 9999 and 1 or 0
			if old_is_red_side ~= protocol.is_red_side then
				vo.special_param = protocol.is_red_side == 1 and 10000 or 1
			end
			]]

			local old_side_type = math.floor(vo.special_param / 10000)
			if old_side_type ~= protocol.side_type then
				if vo.is_shadow == 1 then
					vo.special_param = protocol.side_type
				else
					vo.special_param = protocol.side_type * 10000
				end
			end

			local follow_ui = role:GetFollowUi()
			if follow_ui then
				follow_ui:GetHpBar():UpdateProgress()
			end
		end
	end

	if role then
		-- 连杀称号
		local title_info = self.data:GetRoleTitle(protocol.multi_kill_role_num)
		local title_res = title_info.title_res
		role:SetAttr("used_title_list", { title_res })

		-- 天选称号
		if CountDownManager.Instance:HasCountDown("tianxuan_buff_endtime" .. protocol.obj_id) then
			CountDownManager.Instance:RemoveCountDown("tianxuan_buff_endtime" .. protocol.obj_id)
		end
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		if protocol.buff_end_time > server_time then
			local tianxuan_title_res = self.data:GetTianXuanTitle()
			role:SetAttr("used_title_list", { tianxuan_title_res })
			CountDownManager.Instance:AddCountDown("tianxuan_buff_endtime" .. protocol.obj_id, nil,
				function()
					local scene_type = Scene.Instance:GetSceneType()
					if scene_type ~= SceneType.YEZHANWANGCHENGFUBEN then
						return
					end
					if not role:IsDeleted() then
						role:SetAttr("used_title_list", {})
					end
				end,
				protocol.buff_end_time, nil, 1)
		end
	end
end

function KuafuYeZhanWangChengWGCtrl:OnSCFightPickBuffNotice(protocol)
	local buff_type = protocol.buff_type
	self:ShowYZWCBuffText(Language.YeZhanWangCheng.BuffText[buff_type])
end

function KuafuYeZhanWangChengWGCtrl:ShowYZWCScoreRunShow()
	if #self.show_score_list == 0 then
		GlobalTimerQuest:CancelQuest(self.show_score_tips)
		self.show_score_tips = nil
		return
	end
	if #self.show_score_list > 0 then
		local str = table.remove(self.show_score_list, 1)
		self:ShowYZWCScoreText(str)
	end
end

function KuafuYeZhanWangChengWGCtrl:ShowYZWCScoreText(str)
	local main_role_obj = Scene.Instance:GetMainRole().draw_obj
	if main_role_obj then
		local attach_point = main_role_obj:GetAttachPoint(AttachPoint.BuffBottom)
		local bundle_name = "uis/view/floatingtext_ui_prefab"
		local asset_name = "YZWCScoreText"
		FightText.Instance:ShowText(bundle_name, asset_name, str, attach_point)
	end
end

function KuafuYeZhanWangChengWGCtrl:ShowYZWCBuffText(str)
	local main_role_obj = Scene.Instance:GetMainRole().draw_obj
	if main_role_obj then
		local attach_point = main_role_obj:GetAttachPoint(AttachPoint.BuffBottom)
		local bundle_name = "uis/view/floatingtext_ui_prefab"
		local asset_name = "YZWCBuffText"
		FightText.Instance:ShowText(bundle_name, asset_name, str, attach_point)
	end
end

function KuafuYeZhanWangChengWGCtrl:UpdateSceneRoleInfo(self_side_type) --self_is_red
	local obj_list = Scene.Instance:GetObjList()
	local this_is_my_side = false
	local color
	local follow_ui
	local is_shadow = false
	local shadow_score = 0

	for i, v in pairs(obj_list) do
		if v:GetType() == SceneObjType.Role then
			if v.vo.is_shadow == 1 then
				this_is_my_side = v.vo.special_param == self_side_type
			else
				local side_type = math.floor(v.vo.special_param / 10000)
				this_is_my_side = side_type == self_side_type
			end

			color = this_is_my_side and COLOR3B.WHITE or COLOR3B.RED

			if string.find(v.vo.name, COLOR3B.RED) and color == COLOR3B.WHITE then
				v.vo.name = string.gsub(v.vo.name, COLOR3B.RED, COLOR3B.WHITE)
			elseif string.find(v.vo.name, COLOR3B.WHITE) and color == COLOR3B.RED then
				v.vo.name = string.gsub(v.vo.name, COLOR3B.WHITE, COLOR3B.RED)
			else
				v.vo.name = ToColorStr(v.vo.name, color)
			end

			is_shadow = v.vo.is_shadow == 1
			if is_shadow then
				local show_cfg = KuafuYeZhanWangChengWGData.Instance:GetTipsShowInfo()
				local min = show_cfg.shadow_min_score or 100
				local max = show_cfg.shadow_max_score or 120
				v:SetAttr("yzwc_ico", v.vo.special_param + 1)
				shadow_score = GameMath.Rand(min, max)
				v:SetAttr("yzwc_score")
			end

			follow_ui = v:GetFollowUi()
			if follow_ui then
				follow_ui:SetName(v.vo.name, v)
				follow_ui:GetHpBar():UpdateProgress()
				if is_shadow then
					follow_ui:SetRoleScore()
				end
			end
		end
	end
end

function KuafuYeZhanWangChengWGCtrl:OnSCFightBroadcastInfo(protocol)
	--0 击杀  1 终结
	AvatarManager.Instance:SetAvatarKey(protocol.killer_uid, protocol.killer_avatar_key_big, protocol.killer_avatar_key_small)
	AvatarManager.Instance:SetAvatarKey(protocol.target_uid, protocol.target_avatar_key_big, protocol.target_avatar_key_small)
	local t = {}
	t.broadcast_type = protocol.broadcast_type
	t.killer_uid = protocol.killer_uid
	--t.killer_uid = protocol.killer_uid
	t.killer_name = protocol.killer_name
	t.killer_sex = protocol.killer_sex
	t.killer_prof = protocol.killer_prof
	t.killer_side_type = protocol.killer_side_type
	t.target_uid = protocol.target_uid
	t.target_name = protocol.target_name
	t.target_sex = protocol.target_sex
	t.target_prof = protocol.target_prof
	t.target_side_type = protocol.target_side_type
	t.param = protocol.param
	TipWGCtrl.Instance:AddZCRuneMsgCenter(t)
end

-- 加入夜战王城
function KuafuYeZhanWangChengWGCtrl:SendNightFightEnterReq()
	local is_finish1 = ActivityWGData.Instance:GetActDoubleSideFBIsFinish(ACTIVITY_TYPE.KUAFUYEZHANWANGCHENG)
	local is_finish2 = ActivityWGData.Instance:GetActDoubleSideFBIsFinish(ACTIVITY_TYPE.YEZHANWANGCHENG)
	if is_finish1 or is_finish2 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.ActDoubleSideFBTips)
		return
	end

	if not FuBenWGCtrl.CanEnterFuben() then
		return
	end

	-- local send_protocol = ProtocolPool.Instance:GetProtocol(CSNightFightEnterReq)
	-- send_protocol:EncodeAndSend()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossStartReq)
	send_protocol.cross_activity_type = ACTIVITY_TYPE.KUAFUYEZHANWANGCHENG
	send_protocol:EncodeAndSend()
end

-- 请求次数奖励
function KuafuYeZhanWangChengWGCtrl:SendEnterCountReward(index)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossNightFightOperate)
	protocol.opera_type = CROSS_NIGHT_FIGHT_OPERATE_TYPE.CROSS_NIGHT_FIGHT_OPERATE_TYPE_COUNT_REWARD
	protocol.param1 = index or 0
	protocol.param2 = 0
	protocol.param3 = 0
	protocol.param4 = 0
	protocol:EncodeAndSend()
end

function KuafuYeZhanWangChengWGCtrl:OnSCCrossNightFightInfo(protocol)
	self.data:SetSCCrossNightFightInfo(protocol)
	RemindManager.Instance:Fire(RemindName.YeZhanWangCheng)
	if self.gift_view:IsOpen() then
		self.gift_view:Flush()
	end
end

function KuafuYeZhanWangChengWGCtrl:OnSCFightTianXuanBuffInfo(protocol)
	-- print_error("------------protocol-------------", protocol)
	self.data:SetTianXuanBuffInfo(protocol) 

	if self.follow_view and self.follow_view:IsOpen() then
		self.follow_view:Flush()
	end
end

--任务面板
function KuafuYeZhanWangChengWGCtrl:OpenKuafuYeZhanWangChengFollow()
	self.follow_view:Open()
end

function KuafuYeZhanWangChengWGCtrl:CloseKuafuYeZhanWangChengFollow()
end

function KuafuYeZhanWangChengWGCtrl:GetKuafuYeZhanWangChengFollow()
	return self.follow_view
end

function KuafuYeZhanWangChengWGCtrl:OpenSeeGiftView()
	self.gift_view:Open()
end

function KuafuYeZhanWangChengWGCtrl:CloseSeeGiftView()
	self.gift_view:Close()
end

function KuafuYeZhanWangChengWGCtrl:ShowTimePrepareView(time)
	self.follow_view:SetPrepareTime(time)
end

function KuafuYeZhanWangChengWGCtrl:CheckActiIsOpen()
	return self.data:CheckActIsStart()
end

function KuafuYeZhanWangChengWGCtrl:OnYZWCBuffChange()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type ~= SceneType.YEZHANWANGCHENGFUBEN then
		return
	end

	local role_list = Scene.Instance:GetRoleList()
	for k, v in pairs(role_list) do
		if v and v.vo then
			v:ReloadUIYZWCBuff()
		end
	end
	local main_role = Scene.Instance:GetMainRole()
	if main_role and main_role.vo then
		main_role:ReloadUIYZWCBuff(true)
	end
end