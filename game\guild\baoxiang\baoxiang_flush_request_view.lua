BaoXiangFlushRequestView = BaoXiangFlushRequestView or BaseClass(SafeBaseView)

function BaoXiangFlushRequestView:__init()
	self:SetMaskBg(true)
	self:LoadConfig()
end

-- 加载配置
function BaoXiangFlushRequestView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",{vector2 = Vector2(38, 45), sizeDelta = Vector2(917, 526)})
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "baoxiang_flush_request")
end

function BaoXiangFlushRequestView:__delete()

end

function BaoXiangFlushRequestView:ReleaseCallBack()
	if self.request_list then
		self.request_list:DeleteMe()
		self.request_list = nil
	end
end

function BaoXiangFlushRequestView:LoadCallBack()
	self.request_list = AsyncListView.New(BaoXiangRequestItem,self.node_list["request_list"])
end

function BaoXiangFlushRequestView:OnFlush()
	local request_info_list = GuildBaoXiangWGData.Instance:GetDailyAllItemInfoList()
	self.request_list:SetDataList(request_info_list)
end

--------------------BaoXiangRequestItem-----------
BaoXiangRequestItem = BaoXiangRequestItem or BaseClass(BaseRender)

function BaoXiangRequestItem:__init()
	XUI.AddClickEventListener(self.node_list["request_btn"], BindTool.Bind(self.ClickRequestBtn,self))
end

function BaoXiangRequestItem:__delete()
	
end

function BaoXiangRequestItem:OnFlush()
	if not self.data then return end
	self.node_list["name_text"].text.text = self.data.name
	self.node_list["request_btn"]:SetActive(self.data.help_tag == 0)
	self.node_list["yiqiuzhu"]:SetActive(self.data.help_tag == 1)
	self.node_list["bg"]:SetActive(self.index%2 == 1)
	local fortune_cfg = QifuYunShiWGData.Instance:GetFortuneTypeCfg(self.data.fortune_type)
	self.node_list["yunshi_text"].text.text = fortune_cfg and fortune_cfg.name or ""
	local other_cfg = GuildBaoXiangWGData.Instance:GetOtherCfg()
	local help_refresh_times = self.data.help_refresh_times
	local has_num = other_cfg.help_refresh_times - help_refresh_times
	self.node_list["num_text"].text.text = has_num .. "/" .. other_cfg.help_refresh_times
	local has_id = GuildBaoXiangWGData.Instance:GetReqHelpRefreshUidListId(self.data.uid)
	self.node_list["yiqiuzhu"]:SetActive(has_id ~= nil)
	self.node_list["request_btn"]:SetActive(has_id == nil)
end

function BaoXiangRequestItem:ClickRequestBtn()
	if not self.data then return end
	GuildWGCtrl.Instance:SendCSDailyTreasureOperate(DAILY_TREASURE_OPERATE_TYPE.DAILY_TREASURE_OPERATE_TYPE_HELP_REFRESH_REQ,self.data.uid)
end