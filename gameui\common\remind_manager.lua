RemindManager = RemindManager or BaseClass()
require("gameui/common/remind_wg_data")

local develop_mode = require("editor/develop_mode")
local is_develop = develop_mode:IsDeveloper()

function RemindManager:__init()
	if nil ~= RemindManager.Instance then
		print_error("[RemindManager]:Attempt to create singleton twice!")
	end
	RemindManager.Instance = self

	self.record_num_t = {}
	self.check_callback_t = {}
	self.check_time_t = {}
	self.execute_callback_t = {}
	self.check_callback_map = {}

	self.wait_check_t = {}
	self.wait_check_queue_count = 0
	self.wait_check_queue = {}

	self.wait_execute_t = {}
	self.wait_execute_queue = {}

	self.remind_own_group_t = {}
	self.remind_data = RemindWGData.New()

	for group_name, list in pairs(RemindGroud) do
		for _, remind_name in pairs(list) do
			if group_name ~= remind_name then
				self.remind_own_group_t[remind_name] = self.remind_own_group_t[remind_name] or {}
				table.insert(self.remind_own_group_t[remind_name], group_name)
			end
		end
	end

	Runner.Instance:AddRunObj(self, 8)
end

function RemindManager:__delete()
	RemindManager.Instance = nil
	Runner.Instance:RemoveRunObj(self)
	self.remind_data:DeleteMe()
	self.remind_data = nil
end

function RemindManager:ClearWaitRemindTable()
	self.wait_execute_t = {}
	self.wait_check_t = {}
	self.wait_check_queue = {}
	self.record_num_t = {}
	self.wait_check_queue_count = 0
end

function RemindManager:Update(now_time, elapse_time)
	self:StepCheckRemind(now_time)
	self:StepExecuteRemind()
end

function RemindManager:Fire(remind_name)
	-- if self.check_callback_t[remind_name] == nil then
	-- 	print_error("----Fire 了个寂寞-----", remind_name)
	-- end
	local is_enfoece_close = FunOpen.Instance:IsEnforceCloseByFunName(RemindFunName[remind_name])

	if RemindFunName[remind_name] and not is_enfoece_close and not FunOpen.Instance:GetFunIsOpened(RemindFunName[remind_name]) then
		return
	end

	if nil == self.wait_check_t[remind_name] or is_enfoece_close then
		self.wait_check_t[remind_name] = true
		self.wait_check_queue_count = self.wait_check_queue_count + 1
		self.wait_check_queue[self.wait_check_queue_count] = remind_name
	end
end

--异步检查
local wait_check_queue = {}
function RemindManager:StepCheckRemind(now_time)
	if self.wait_check_queue_count <= 0 then
		return
	end

	-- 过滤出真正需要计算的
	local count = 0
	for i = 1, self.wait_check_queue_count do
		local v = self.wait_check_queue[i]

		--功能已开启 并且有回调方法 and nil ~= self.check_callback_t[v]
		--强制关闭
		if ((nil == RemindFunName[v] or FunOpen.Instance:GetFunIsOpened(RemindFunName[v]) ) ) 
			 or FunOpen.Instance:IsEnforceCloseByFunName(RemindFunName[v]) then
			count = count + 1
			wait_check_queue[count] = v
		end
	end

	local temp = self.wait_check_queue
	self.wait_check_queue = wait_check_queue
	wait_check_queue = temp
	self.wait_check_queue_count = count

	if self.wait_check_queue_count <= 0 then
		return
	end

	-- 如果是当前有回调方法的优先执行（基本意味着玩家正在看着，防止因队列太长导触发慢）
	local remind_name = self.wait_check_queue[1]
	for i = 1, self.wait_check_queue_count do
		local v = self.wait_check_queue[i]
		local list = self.execute_callback_t[v]

		if nil ~= list and next(list) then
			remind_name = self.wait_check_queue[i]
			
			table.remove(self.wait_check_queue, i)
			self.wait_check_queue_count = self.wait_check_queue_count - 1
			break
		end
	end


	if self.wait_check_queue_count > 0 then
		if remind_name == self.wait_check_queue[1] then
			table.remove(self.wait_check_queue, 1)
			self.wait_check_queue_count = self.wait_check_queue_count - 1
		end
	end

	self.wait_check_t[remind_name] = nil
	local num = self.record_num_t[remind_name] or 0

	-- 后台强关的时候直接把红点数量改为0
	if FunOpen.Instance:IsEnforceCloseByFunName(RemindFunName[remind_name]) then
		num = 0
	else
		if nil ~= self.check_callback_t[remind_name] then
			num = self.check_callback_t[remind_name]()
		else 
			-- 没有注册回调的可能是父节点，所以去计算子节点的数量
			-- 再通过RecalcOwnGroup重新设置父节点的数量
			local remind_list = RemindGroud[remind_name]
			if remind_list then
				local remind_name2 =remind_list[1]
				self:RecalcOwnGroup(remind_name2)
			end
		end
	end


	if is_develop then
		if nil == self.check_callback_t[remind_name] then
			-- print_error("红点提醒没有注册计算方法:", remind_name)
		end
		if num == nil or type(num) ~= "number"  then
			print_error("红点计算返回值有误:", remind_name, num)
		end
	end

	if num ~= self.record_num_t[remind_name] then
		self.record_num_t[remind_name] = num
		self:AddToExecuteWaitQueue(remind_name)
		self:RecalcOwnGroup(remind_name)
	end
end

--异步通知
function RemindManager:StepExecuteRemind()
	local excute_len = #self.wait_execute_queue
	if excute_len <= 0 then
		return
	end

	local excute_count = math.min(excute_len, 2)
	while excute_count > 0 do
		excute_count = excute_count - 1
		local t = table.remove(self.wait_execute_queue, 1)
		self.wait_execute_t[t.execute] = nil
		t.execute(t.remind_name, self.record_num_t[t.remind_name] or 0)
	end

end

function RemindManager:RecalcOwnGroup(remind_name)
	local group_list = self.remind_own_group_t[remind_name]
	if nil == group_list then
		return
	end
	
	for _, group_name in ipairs(group_list) do
		if not RemindFunName[group_name] or 
		(RemindFunName[group_name] and (FunOpen.Instance:GetFunIsOpened(RemindFunName[group_name]) or FunOpen.Instance:IsEnforceClose(RemindFunName[group_name])))
		 then 	-- 父节点也要判断功能开启

			local total_num = 0
			local remind_list = RemindGroud[group_name] or {}
			for _, v in pairs(remind_list) do
				total_num = total_num + (self.record_num_t[v] or 0)
			end
			
			if self.record_num_t[group_name] ~= total_num then
				self.record_num_t[group_name] = total_num
				self:AddToExecuteWaitQueue(group_name)
			end

			if nil ~= self.remind_own_group_t[group_name] then
				self:RecalcOwnGroup(group_name) -- 继续往上层计算
			end
		end
	end
end

function RemindManager:AddToExecuteWaitQueue(remind_name)
	local list = self.execute_callback_t[remind_name] or {}
	for _, execute in pairs(list) do
		if nil == self.wait_execute_t[execute] then
			table.insert(self.wait_execute_queue, {remind_name = remind_name, execute = execute})
		end
	end
end

--注册一个提醒事件
function RemindManager:Register(remind_name, callback)
	self.check_callback_map[callback] = callback
	self.check_callback_t[remind_name] = callback
end

function RemindManager:UnRegister(remind_name)
	local callback = self.check_callback_t[remind_name]
	if nil ~= callback then
		self.check_callback_map[callback] = nil
		self.check_callback_t[remind_name] = nil
	end
end

--绑定一个某提醒事件的监听
function RemindManager:Bind(execute, remind_name,is_notexecute)
	if nil == execute then
		return
	end

	self.check_callback_map[execute] = execute
	self.execute_callback_t[remind_name] = self.execute_callback_t[remind_name] or {}
	self.execute_callback_t[remind_name][execute] = execute
	if not is_notexecute then
		self:AddToExecuteWaitQueue(remind_name)
	end
end

function RemindManager:UnBind(execute)
	if nil == execute then
		return
	end

	self.check_callback_map[execute] = nil
	for k, v in pairs(self.execute_callback_t) do
		v[execute] = nil
	end

	for i = #self.wait_execute_queue, 1, -1 do
		if self.wait_execute_queue[i].execute == execute then
			table.remove(self.wait_execute_queue, i)
		end
	end

	self.wait_execute_t[execute] = nil
end

function RemindManager:GetRemind(remind_name)
	return self.record_num_t[remind_name] or 0
end

function RemindManager:IsExistsListen(callback)
	return nil ~= self.check_callback_map[callback]
end

-- 开发者模式检测专用
function RemindManager:GetRegisterNum(t)
	for k, v in pairs(self.check_callback_t) do
		t["register_remind_name : " .. k] = 1
	end
end

-- 开发者模式检测专用
function RemindManager:GetBindNum(t)
	for k1, v1 in pairs(self.execute_callback_t) do
		local num = 0
		for k2, v2 in pairs(v1) do
			num = num + 1
		end

		t["remind_name : " .. k1] = num
	end
end

function RemindManager:GetCheckCallbackList()
	return self.check_callback_t
end

function RemindManager:CmdShowAllSystemRemind()
	if self.record_num_t then
		if IsEmptyTable(self.record_num_t) then
			print_error("Remind num table is empty")
		else
			for k ,v in pairs(self.record_num_t) do
				if v > 0 then
					local str = "Remind_Name:<color=#79fa82>"..k.."</color>  Num:<color=#79fa82>"..v.."</color>"
					print_error(str)
				end
			end
		end
	end
end

function RemindManager:CmdShowRemindInfo(remind_name)
	if remind_name == RemindName.Vip_Col then
		RechargeWGData.Instance:ShowVipColRedInfo()
	elseif remind_name == RemindName.DailyRecharge then
		ServerActivityWGData.Instance:ShowDailyRechargeRedInfo()
	elseif remind_name == RemindName.XiuZhenRoad then
		XiuZhenRoadWGData.Instance:ShowXiuZhenRoadRedInfo()
    -- elseif remind_name == RemindName.ActivityHall then
    -- 	BiZuoWGData.Instance:PrintActivityHallRedShow()
	end
end

function RemindManager:CmdWaitQueueInfo()
	local debug_list = {}

	for k,v in pairs(self.wait_check_queue) do
		if debug_list[v] == nil then
			debug_list[v] = 1
		else
			debug_list[v] = debug_list[v] + 1
		end
	end

	print_error("CmdWaitQueueInfo", #self.wait_check_queue)
	for k,v in pairs(debug_list) do
		print_error("红点等待的计算队列信息:   ", k, v)
	end
end

function RemindManager:CmdWaitExecuteInfo()
	local debug_list = {}

	print_error("######CmdWaitExecuteInfo########", self.wait_execute_queue)
	for k,v in pairs(self.wait_execute_queue) do
		if debug_list[v.remind_name] == nil then
			debug_list[v.remind_name] = 1
		else
			debug_list[v.remind_name] = debug_list[v.remind_name] + 1
		end
	end

	print_error("CmdWaitExecuteInfo", #self.wait_execute_queue)
	for k,v in pairs(debug_list) do
		print_error("红点等待的执行回调队列信息:   ", k, v)
	end
end