ActCollectCardTaskView = ActCollectCardTaskView or BaseClass(SafeBaseView)
function ActCollectCardTaskView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "layout_second_panel", {vector2 = Vector2(0, 24), sizeDelta = Vector2(760, 554)})
	self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "layout_open_server_collect_card_task")
end

function ActCollectCardTaskView:ReleaseCallBack()
	if self.collect_card_task_list then
		self.collect_card_task_list:DeleteMe()
		self.collect_card_task_list = nil
	end
end

function ActCollectCardTaskView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.OpenServer.SendCollectCandTaskTitle
	if not self.collect_card_task_list then
		self.collect_card_task_list = AsyncListView.New(CollectCardTaskRender, self.node_list.collect_card_task_list)
	end
end

function ActCollectCardTaskView:OnFlush(param_t)
	local list = ActivityCollectCardWGData.Instance:GetCurGradeCollectCardTaskList()
	self.collect_card_task_list:SetDataList(list)
end
-------------------------------------------------------------------------------------------------------------------------
CollectCardTaskRender = CollectCardTaskRender or BaseClass(BaseRender)
function CollectCardTaskRender:__init()
    if not self.reward_list then
	    self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
		self.reward_list:SetStartZeroIndex(true)
    end

	XUI.AddClickEventListener(self.node_list.btn_task_go_to, BindTool.Bind(self.OnClickGotoFunc, self))
	XUI.AddClickEventListener(self.node_list.btn_task_can_get, BindTool.Bind(self.OnClickGetTaskReward, self))
end

function CollectCardTaskRender:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

-- 打开界面
function CollectCardTaskRender:OnClickGotoFunc()
	if (not self.data) or (not self.data.cfg_data) then
		return
	end

	FunOpen.Instance:OpenViewNameByCfg(self.data.cfg_data.open_panel)
end

-- 领取奖励
function CollectCardTaskRender:OnClickGetTaskReward()
	if (not self.data) or (not self.data.cfg_data) then
		return
	end

	ServerActivityWGCtrl:RequestAOCollectCardTaskReward(self.data.cfg_data.task_id)
end

function CollectCardTaskRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.btn_task_go_to:CustomSetActive(not self.data.is_can_get)
	self.node_list.btn_task_can_get:CustomSetActive(self.data.is_can_get and (not self.data.is_get_flag))
	self.node_list.task_can_get_remind:CustomSetActive(self.data.is_can_get and (not self.data.is_get_flag))
	self.node_list.btn_task_geted_flag:CustomSetActive(self.data.is_get_flag)

	if self.data.cfg_data and self.data.cfg_data.reward_list then
		self.reward_list:SetDataList(self.data.cfg_data.reward_list)
		local color = self.data.progress >= self.data.cfg_data.param1 and COLOR3B.L1 or COLOR3B.L5
		local str = string.format("%s(%s/%d)", self.data.cfg_data.task_description, ToColorStr(self.data.progress, color), self.data.cfg_data.param1)
		self.node_list.description.text.text = str
	end
end
