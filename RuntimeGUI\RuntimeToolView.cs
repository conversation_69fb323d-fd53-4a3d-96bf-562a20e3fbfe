﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class RuntimeToolView : RuntimeBaseView
{
    public RuntimeToolView() : base(RuntimeViewName.TOOL)
    {

    }

    override protected void OnReapintWindow(int windowid)
    {
        GUILayout.Space(15);
        GUILayout.BeginHorizontal();

        if (GUILayout.Button("场景编辑"))
        {
            RuntimeViewMgr.Instance.OpenView(RuntimeViewName.SCENE_EDIT);
        }
        if (GUILayout.But<PERSON>("LuaProfiler"))
        {
            RuntimeViewMgr.Instance.OpenView(RuntimeViewName.LUA_PROFILER);
        }
        GUILayout.EndHorizontal();
    }
}
