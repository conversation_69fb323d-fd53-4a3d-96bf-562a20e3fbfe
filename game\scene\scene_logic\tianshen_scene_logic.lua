TianShenSceneLogic = TianShenSceneLogic or BaseClass(CommonFbLogic)

function TianShenSceneLogic:__init()
end

function TianShenSceneLogic:__delete()
end

function TianShenSceneLogic:Enter(old_scene_type, new_scene_type)
    CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
    if old_scene_type == new_scene_type then return end
	ViewManager.Instance:CloseAll()
    GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)

    local layer_cfg = ConfigManager.Instance:GetAutoConfig("tianshen_fb_auto").scene_layer_cfg
    local scene_id  = Scene.Instance:GetSceneId()
    local ctrl = MainuiWGCtrl.Instance
    ctrl:AddInitCallBack(nil, function()
        ctrl:SetTaskContents(false)
        ctrl:SetOtherContents(true)
        ctrl:SetFBNameState(true, Scene.Instance:GetSceneName())
        ctrl:SetTeamBtnState(false)
        FuBenPanelWGCtrl.Instance:OpenTianShenLogicView()
    end)

  --   local cfg = FuBenPanelWGData.Instance:GetTianShenSceneLogicCfg()
  --   local other_cfg = FuBenPanelWGData.Instance:GetTianShenOtherCfg()
  --   FuBenPanelWGCtrl.Instance:OpenStarAniView({time3 = cfg.star_time_3, time2 = cfg.star_time_2, time1 = cfg.star_time_1, time0 = cfg.star_time_0,
		-- per0 = other_cfg.star0, per1 = other_cfg.star1, per2 = other_cfg.star2, per3 = other_cfg.star3, str = Language.Boss.StarAniStr,})
	
    Scene.SendGetAllObjMoveInfoReq()

    -- if scene_id == layer_cfg[1].scene_id then
    --     CgManager.Instance:Play(BaseCg.New("cg/x1_bs_geren02_prefab", "X1_BS_GeRen02_cg01"), function()
    --         -- body
    --     end, nil, nil)
    -- end
end

function TianShenSceneLogic:Out(old_scene_type, new_scene_type)
    CommonFbLogic.Out(self, old_scene_type, new_scene_type)
    FuBenPanelWGCtrl.Instance:CloseTianShenLogicView()
    MainuiWGCtrl.Instance:SetTaskContents(true)
    -- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
    MainuiWGCtrl.Instance:SetFBNameState(false)
    MainuiWGCtrl.Instance:SetTeamBtnState(true)
    MainuiWGCtrl.Instance:SetOtherContents(false)
    FuBenPanelWGCtrl.Instance:CloseStarAniView()
    UiInstanceMgr.Instance:ColseFBStartDown()
	ViewManager.Instance:CloseAll()
    FuBenWGCtrl.Instance:CheckLeaveOpenView(old_scene_type, TabIndex.fubenpanel_tianshen)
end