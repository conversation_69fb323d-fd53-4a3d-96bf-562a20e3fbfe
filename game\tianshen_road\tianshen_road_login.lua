local POS_LIST = {[0] = {x = 0, y = 32}, {x = 0, y = -17}, {x = 13, y = -29}, {x = 13, y = 15}, {x = 5, y = -24}}
local ROTA_LIST = {[0] = -4, 9, -3.6, -3.6, -10}

function TianshenRoadView:InitLoginRewarView()
	self.is_jump_day_index = false
	self.select_day_index = 1

	self.login_item_list = AsyncListView.New(TianShenRoadLoginRewardItem, self.node_list.login_item_list)
	self.login_item_list:SetSelectCallBack(BindTool.Bind1(self.OnClickDayBtn, self))
	self.login_item_list.IsLimitSelectByIndex = function(list, cell_index)
		local cfg_list = TianshenRoadWGData.Instance:GetLoginRewardCfg()
		if cell_index > #cfg_list then
			SysMsgWGCtrl.Instance:Error<PERSON><PERSON>ind(Language.TianShenRoad.LoginTip)
			return true
		end
		return false
	end

	if not self.grid_scroller_fun then
		self.grid_scroller_fun = BindTool.Bind(self.ScrollerEndScrolled, self)
		self.node_list.login_item_list.scroller.scrollerEndScrolled = self.grid_scroller_fun
	end

	self.lr_reward_list_view = AsyncListView.New(ItemCell, self.node_list["lg_reward_list"])
	self.lr_reward_list_view:SetStartZeroIndex(true)

	self.lr_double_list_view = AsyncListView.New(TianShenRoadDuoBeiItemRender, self.node_list["lg_double_list"])
	--XUI.AddClickEventListener(self.node_list.login_btn_tip, BindTool.Bind(self.OnBtnTipClickHnadler, self))
	XUI.AddClickEventListener(self.node_list["lg_btn_receive"], BindTool.Bind(self.OnClickGetLoginReward, self))

	self.role_vip_level_change_event = BindTool.Bind1(self.FlushLoginView, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_vip_level_change_event, {"vip_level"})
	-- local theme_cfg = TianshenRoadWGData.Instance:GetThemeCfgByTabIndex(TabIndex.tianshenroad_login)
	-- if theme_cfg ~= nil then
	-- 	self.node_list.img_login_title.text.text = theme_cfg.rule_desc
	-- end
	self:LoginTimeCountDown()
end

function TianshenRoadView:ScrollerEndScrolled()
	local val = self.node_list.login_item_list.scroll_rect.horizontalNormalizedPosition
	local cell_row = self.login_item_list:GetListViewNumbers()
	self.node_list.login_right_arrow:SetActive(cell_row > 5 and val < 0.85)
    self.node_list.login_left_arrow:SetActive(cell_row > 5 and val > 0.1)
end

function TianshenRoadView:OnClickDayBtn(cell)
    if not cell then
		return
	end

	local select_index = cell.index

	if (self.select_day_index == select_index) then
		return
	end

	self.select_day_index = select_index
	self:FlushLoginReward()
end

function TianshenRoadView:OnClickGetLoginReward()
	local day_info = TianshenRoadWGData.Instance:GetLoginRewardInfo(self.select_day_index)
	if not day_info then
		return
	end

	if day_info.common_gift_state == TianShenRoadRewardState.KLQ then
		TianshenRoadWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.GOD_DENGLU_YOULI, TIANSHEN_THEME_LOGIN_GIFT_OP_TYPE.COMMON_REWARD, self.select_day_index)
	elseif day_info.common_gift_state == TianShenRoadRewardState.BKL then
		TipWGCtrl.Instance:ShowSystemMsg(Language.TianShenRoad.TipDesc1)
	end

	if day_info.special_gift_state == TianShenRoadRewardState.KLQ then
		TianshenRoadWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.GOD_DENGLU_YOULI, TIANSHEN_THEME_LOGIN_GIFT_OP_TYPE.SPECIAL_REWARD, self.select_day_index)
	end
end

function TianshenRoadView:ReleaseLoginView()
	if self.login_item_list then
		self.login_item_list:DeleteMe()
		self.login_item_list = nil
	end

	if self.lr_reward_list_view then
		self.lr_reward_list_view:DeleteMe()
		self.lr_reward_list_view = nil
	end

	if self.lr_double_list_view then
		self.lr_double_list_view:DeleteMe()
		self.lr_double_list_view = nil
	end

	self.grid_scroller_fun = nil

	if self.role_vip_level_change_event then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_vip_level_change_event)
		self.role_vip_level_change_event = nil
	end

	CountDownManager.Instance:RemoveCountDown("tianshenroad_login_count_down")
end

function TianshenRoadView:LoginShowIndexCallBack()
	self.is_jump_day_index = true
	self:FlushLoginView()
	self:DoTRLoginCellsAnim()
	--self:DoTRLoginAnim()
end

function TianshenRoadView:FlushLoginView()
	self:FlushDayList()
	self:FlushLoginReward()
	self:FlushLoginDouBle()
end

function TianshenRoadView:OnBtnTipClickHnadler()
	local role_tip = RuleTip.Instance
	if role_tip then
		local title,desc = TianshenRoadWGData.Instance:GetActivityTip(TabIndex.tianshenroad_login)
		if title ~= nil and desc ~= nil then
			role_tip:SetTitle(title)
			role_tip:SetContent(desc)
		end
	end
end

function TianshenRoadView:FlushDayList()
	TianshenRoadWGData.Instance:FlushLoginRewardInfo()
	if self.login_item_list then
		local cfg_list = TianshenRoadWGData.Instance:GetLoginRewardCfg()

		local data_list = {}
		for k,v in pairs(cfg_list) do
			data_list[v.day_index] = v
		end

		if #data_list < 5 then
			for index = 5 - #data_list, 1, -1 do
				table.insert(data_list, {})
			end
		end
		self.login_item_list:SetDataList(data_list)

		if self.is_jump_day_index then
			self.is_jump_day_index = false
			local jump_index = TianshenRoadWGData.Instance:GetLoginJumpIndex()
			self.login_item_list:JumpToIndex(jump_index)
		end
	end
end

function TianshenRoadView:FlushLoginReward()
	local day_info = TianshenRoadWGData.Instance:GetLoginRewardInfo(self.select_day_index)
	local cfg_list = TianshenRoadWGData.Instance:GetLoginRewardCfg()
	if not day_info then
		return
	end

	local is_receive = day_info.common_gift_state == TianShenRoadRewardState.YLQ
	local is_can_receive = day_info.common_gift_state == TianShenRoadRewardState.KLQ
	self.node_list["lg_btn_receive"]:SetActive(not is_receive)
	self.node_list["lg_btn_receive_remind"]:SetActive(is_can_receive)
	self.node_list["lg_btn_ylq"]:SetActive(is_receive)
	XUI.SetGraphicGrey(self.node_list.lg_btn_receive, not is_can_receive)

	local cfg = cfg_list[self.select_day_index]
	self.lr_reward_list_view:SetRefreshCallback(function(item_cell, cell_index)
		if item_cell then
			item_cell:SetLingQuVisible(is_receive)
			item_cell:SetRedPointEff(is_can_receive)
		end
	end)
	self.lr_reward_list_view:SetDataList(cfg.reward_item)
end

function TianshenRoadView:FlushLoginDouBle()
    local info_list = TianshenRoadWGData.Instance:GetDuoBeiInfo()
	if info_list ~= nil then
        self.lr_double_list_view:SetDataList(info_list)
	end
end

--有效时间倒计时
function TianshenRoadView:LoginTimeCountDown()
	CountDownManager.Instance:RemoveCountDown("tianshenroad_login_count_down")
	local invalid_time = TianshenRoadWGData.Instance:GetActivityInValidTime(TabIndex.tianshenroad_login)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if invalid_time > server_time then
		self.node_list.login_time_label.text.text = TimeUtil.FormatSecondDHM2(invalid_time - server_time)
		CountDownManager.Instance:AddCountDown("tianshenroad_login_count_down", BindTool.Bind1(self.LoginUpdateCountDown, self), BindTool.Bind1(self.LoginTimeCountDown, self), invalid_time, nil, 1)
	else
		self.node_list.login_time_label.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
		self.node_list.login_time_label.text.color = Str2C3b(COLOR3B.RED)
	end
end

function TianshenRoadView:LoginUpdateCountDown(elapse_time, total_time)
	self.node_list.login_time_label.text.text = TimeUtil.FormatSecondDHM2(total_time - elapse_time)
end

function TianshenRoadView:DoTRLoginAnim()
	local tween_info = UITween_CONSTS.TianshenRoadView
    UITween.FakeHideShow(self.node_list["login_root"])
    UITween.AlphaShow(GuideModuleName.TianShenRoadPanel, self.node_list["login_root"], 0, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)

    --UITween.DoUpDownCrashTween(self.node_list["img_login_title"])
	self:DoTRLoginCellsAnim()
end

function TianshenRoadView:DoTRLoginCellsAnim()
	local tween_info = UITween_CONSTS.TianshenRoadView
	self.node_list["login_item_list"]:SetActive(false)
    ReDelayCall(self, function()
        self.node_list["login_item_list"]:SetActive(true)
        local list =  self.login_item_list:GetAllItems()
        local sort_list = GetSortListView(list)
        local count = 0
        for k,v in ipairs(sort_list) do
            if 0 ~= v.index then
                count = count + 1
            end
            v.item:PlayItemAnim(count)
        end
    end, tween_info.DelayDoTime, "TRLogin_Cell_Tween")
end

----------------------------------------------------------------------------------------

TianShenRoadLoginRewardItem = TianShenRoadLoginRewardItem or BaseClass(BaseRender)

function TianShenRoadLoginRewardItem:__delete()
	self.normal_reward_list:DeleteMe()
	self.normal_reward_list = nil
	self.special_reward_list:DeleteMe()
	self.special_reward_list = nil
end

function TianShenRoadLoginRewardItem:LoadCallBack()
	self.save_day_index = nil
	self.normal_reward_list = AsyncListView.New(ItemCell, self.node_list.normal_reward_list)
	self.special_reward_list = AsyncListView.New(ItemCell, self.node_list.special_reward_list)
	XUI.AddClickEventListener(self.node_list.get_btn, BindTool.Bind(self.OnClickGetBtn, self))
end

function TianShenRoadLoginRewardItem:OnFlush()
	local index = (self.index - 1) % 5
	local pos = POS_LIST[index]
	local rotation = ROTA_LIST[index]
	RectTransform.SetAnchoredPositionXY(self.node_list.tween_root.rect, pos.x, pos.y)
	self.node_list["tween_root"].rect.rotation = Quaternion.Euler(0, 0, rotation)
	self:FlushRewardItem()
	self:FlushBtnStatus()
end

function TianShenRoadLoginRewardItem:FlushRewardItem()
	local data = self:GetData()
	if IsEmptyTable(data) or self.save_day_index == data.day_index then
		return
	end
	self.save_day_index = data.day_index

	if data.vip_lv > 0 then
		local reward_list = SortTableKey(data.special_reward_item)
		self.special_reward_list:SetDataList(reward_list)
	end

	local reward_list = SortTableKey(data.reward_item)
	self.normal_reward_list:SetDataList(reward_list)
	self.node_list.normal_tag:SetActive(data.vip_lv > 0)
	self.node_list.special_group:SetActive(data.vip_lv > 0)
	self.node_list.day_label.text.text = string.format(Language.TianShenRoad.LoginStr1, data.day_index)
	self.node_list.special_tag_label.text.text = string.format(Language.TianShenRoad.Vipstr3, data.vip_lv)

	local bundle, asset = ResPath.GetTianShenRoadImg("a3_zsqd_dlyl_dl" .. self.save_day_index)
	if bundle and asset then
		self.node_list.day_img.image:LoadSprite(bundle, asset, function()
			self.node_list.day_img.image:SetNativeSize()
		end)
	end
end

function TianShenRoadLoginRewardItem:FlushBtnStatus()
	local data = self:GetData()
	if IsEmptyTable(data) then
		return
	end
	
	local day_info = TianshenRoadWGData.Instance:GetLoginRewardInfo(data.day_index)
	if not day_info then
		return
	end

	self.node_list.btn_label.text.text = Language.TianShenRoad.BtnStr2
	local img_name = day_info.common_gift_state == TianShenRoadRewardState.YLQ and "a3_zsqd_dlyl_dl7" or "a3_zsqd_dlyl_dl6"
	local bundle, asset = ResPath.GetRawImagesPNG(img_name)
    self.node_list.bg.raw_image:LoadSprite(bundle, asset, function()
        self.node_list.bg.raw_image:SetNativeSize()
    end)

	if day_info.common_gift_state == TianShenRoadRewardState.YLQ then
		if day_info.special_gift_state == TianShenRoadRewardState.YLQ or data.vip_lv <= 0 then
			self.node_list.get_btn:SetActive(false)
			self.node_list.complete_flag:SetActive(true)
			return
		elseif day_info.special_gift_state == TianShenRoadRewardState.BKL then
			self.node_list.btn_label.text.text = string.format(Language.TianShenRoad.Vipstr2, data.vip_lv)
		end
	end
	--self.node_list.get_btn:SetActive(true)
	self.node_list.complete_flag:SetActive(false)

	local can_get = day_info.common_gift_state == TianShenRoadRewardState.KLQ or day_info.special_gift_state == TianShenRoadRewardState.KLQ
	self.node_list.red_point:SetActive(can_get)
	XUI.SetButtonEnabled(self.node_list.get_btn, can_get)
end

function TianShenRoadLoginRewardItem:OnClickGetBtn()
	local data = self:GetData()
	local day_info = TianshenRoadWGData.Instance:GetLoginRewardInfo(data.day_index)
	if not day_info then
		return
	end

	if day_info.common_gift_state == TianShenRoadRewardState.KLQ then
		TianshenRoadWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.GOD_DENGLU_YOULI, TIANSHEN_THEME_LOGIN_GIFT_OP_TYPE.COMMON_REWARD, data.day_index)
	end

	if day_info.special_gift_state == TianShenRoadRewardState.KLQ then
		TianshenRoadWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.GOD_DENGLU_YOULI, TIANSHEN_THEME_LOGIN_GIFT_OP_TYPE.SPECIAL_REWARD, data.day_index)
	end
end

function TianShenRoadLoginRewardItem:PlayItemAnim(item_index)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.TianshenRoadView.FirstRechargeListCellRender

    UITween.FakeHideShow(self.node_list["tween_root"])
    ReDelayCall(self, function()
        UITween.AlphaShow(GuideModuleName.TianShenRoadPanel, self.node_list["tween_root"], tween_info.FromAlpha, tween_info.ToAlpha, tween_info.AlphaTweenTime)
    end, tween_info.NextDoDelay * wait_index, "TianshenRoadFrRewardRender" .. wait_index)
end

function TianShenRoadLoginRewardItem:OnSelectChange(is_select)
    self.node_list["select_content"]:CustomSetActive(is_select)
end

TianShenRoadDuoBeiItemRender = TianShenRoadDuoBeiItemRender or BaseClass(BaseRender)

function TianShenRoadDuoBeiItemRender:LoadCallBack()
	self.node_list.btn.button:AddClickListener(BindTool.Bind(self.OnBtnClickDuoBei, self))
end

function TianShenRoadDuoBeiItemRender:__delete()

end

function TianShenRoadDuoBeiItemRender:OnFlush()
	local data = self:GetData()
	if not data then
		return
	end

	local bundle, asset = ResPath.GetCommonImages(data.cfg.icon)
	self.node_list.icon.image:LoadSpriteAsync(bundle, asset, function ()
		self.node_list.icon.image:SetNativeSize()
	end)
end

function TianShenRoadDuoBeiItemRender:OnBtnClickDuoBei()
	local data = self:GetData()
	if data and data.cfg then
		FunOpen.Instance:OpenViewNameByCfg(data.cfg.panel)
	end
end