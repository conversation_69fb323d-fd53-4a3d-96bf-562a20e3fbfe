TianShenShenShiSkillTips = TianShenShenShiSkillTips or BaseClass(SafeBaseView)

function TianShenShenShiSkillTips:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/tianshen_prefab", "layout_shenshi_skill_tip")
end

function TianShenShenShiSkillTips:__delete()
end

function TianShenShenShiSkillTips:ReleaseCallBack()
	if self.skill_cell_list then
		for k,v in pairs(self.skill_cell_list) do
			v:DeleteMe()
		end
		self.skill_cell_list = nil
	end
end

function TianShenShenShiSkillTips:LoadCallBack()
	self.need_num = 0
	self:SetSecondView(Vector2(666, 723))
	self:SetViewName(Language.TianShen.ShenShiSkillTitle)
	self.skill_cell_list = {}
	for i = 1, 8 do
		self.skill_cell_list[i] = ShenShiSkillCell.New(self.node_list["tianshen_skill_cell"..i])
	end

	self.node_list["close_btn"].button:AddClickListener(BindTool.Bind(self.Close, self))
end

function TianShenShenShiSkillTips:ShowIndexCallBack()

end

function TianShenShenShiSkillTips:OnFlush()
	local num = 0
	for k,v in pairs(self.all_skill_data) do
		num = num + 1
		self.skill_cell_list[num]:SetData(v)
	end

	for i= 1,8 do
		self.node_list["tianshen_skill_cell"..i]:SetActive(i<=num)
	end
	
	-- GlobalTimerQuest:AddDelayTimer(function ()
	-- 	GlobalTimerQuest:AddDelayTimer(function ()
	-- 		local pos_y = 0
	-- 		local y_size = self.node_list.content.rect.sizeDelta.y
	-- 		local y_size2 = self.node_list.cost_cell_list.rect.sizeDelta.y
	-- 		pos_y = (y_size2 - y_size)/2
	-- 		self.node_list.content.rect.localPosition = Vector3(0,pos_y,0)
	-- 	end,0)
	-- end,0.1)
end


function TianShenShenShiSkillTips:SetSkillData(index)
	local skill_cfg = TianShenWGData.Instance:GetShenShiJinShengData(index)
	self.all_skill_data = {}
	for k,v in pairs(skill_cfg) do
		self.all_skill_data[k] = v
	end
end



ShenShiSkillCell = ShenShiSkillCell or BaseClass(BaseRender)

function ShenShiSkillCell:__init()
end

function ShenShiSkillCell:ReleaseCallBack()
end

function ShenShiSkillCell:OnFlush()
	self.node_list.skill_open.text.text = string.format(Language.TianShen.OpenLimit25, Language.TianShen.TSDaoHenLevel[self.data.level + 1])
	self.node_list.skill_desc.text.text = string.format(Language.TianShen.OpenLimit26, (tonumber(self.data.attr_per) or 0) / 100 .. "%")
	self.node_list["skill_lev_img"].image:LoadSprite(ResPath.GetCommon("a3_ty_pz" .. self.data.level + 1))
end