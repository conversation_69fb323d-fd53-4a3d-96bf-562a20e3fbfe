-------------------------活动按钮特殊特效---------------------------------------
local ActBtnSpecialEffectConfig = {
    [ACTIVITY_TYPE.XIUZHEN_ROAD] = {asset = "BtnXiuZhenRoad"},	--修真之路
    [ACTIVITY_TYPE.SHENBINGAWAKEN_ACTIVITY] = {asset1 = "sbjx_jian",asset3 = "sbjx_qin" }	--修真之路
}


MainActivityBtn = MainActivityBtn or BaseClass(BaseRender)
MainActivityBtn.CountDownNum = 0

function MainActivityBtn:__init(instance)
	self.time_str_type = 1
	self.end_time_complete_func = nil
	self.red_point_active = false
	self.show_rank_info_root = false
    self.act_id = 0
    if self.node_list and self.node_list["Icon"] then
        self.node_list["Icon"].animator.enabled = false
    end
end

function MainActivityBtn:CreateBtnAsset(parent, activity_type)
	if nil == self.root_node then
		local bundle = "uis/view/main_ui_prefab"
		local asset = "ActivityButtonView"

		self.btn_obj = ResPoolMgr:TryGetGameObject(bundle,asset)

		if self.btn_obj then
			self.btn_obj.transform:SetParent(parent.transform, false)
			self.btn_obj.name = "id:" .. activity_type
			self.act_id = activity_type
			self:SetInstance(self.btn_obj)
			self:CheckIsNeedAddCloseTweenForMain()
		end
	end
end

function MainActivityBtn:__delete()
	self:RemoveCountDown()
	if self.btn_obj then
		RectTransform.SetAnchorAllign(self.node_list["ActivityButtonView"].rect, AnchorPresets.MiddleCenter)
		ResPoolMgr:Release(self.btn_obj)
		self.btn_obj = nil
	end

	if CountDownManager.Instance:HasCountDown("first_recharge_advantage_tip") then
		CountDownManager.Instance:RemoveCountDown("first_recharge_advantage_tip")
	end
end

function MainActivityBtn:LoadCallBack()
	if nil ~= self.icon_state then
		self:Flush("ShowAni", {self.icon_state})
		self.icon_state = nil
	end

	if nil ~= self.effect_state then
		self:ShowLiuGuang(self.effect_state)
		self.effect_state = nil
	end

	if self.click_callback then
		self:AddClickEventListener(self.click_callback)
		self.click_callback = nil
	end

	self:ResetLiuGuangEffect()

	if self.node_list.rank_info_root then
		self.node_list.rank_info_root:SetActive(false)
	end

	self.show_rank_info_root = false

	if not self.shrinkbuttons_change then
		self.shrinkbuttons_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind1(self.ShrinkButtonsValueChange, self))
	end

	if self.node_list.first_recharge_advantage_tip then
		self.node_list.first_recharge_advantage_tip:SetActive(false)
	end

	if self.node_list.fold_effect then
		self.node_list.fold_effect:SetActive(false)
	end

	if self.node_list.RedPoint then
		self.node_list.RedPoint:SetActive(false)
	end

	if self.node_list.time then
		self.node_list.time.text.text = ""
	end

	if self.node_list.jjks then
		self.node_list.jjks:SetActive(false)
	end

	if self.node_list.jxz then
		self.node_list.jxz:SetActive(false)
	end

	if self.node_list.xsz then
		self.node_list.xsz:SetActive(false)
	end

	if self.node_list.jrzb then
		self.node_list.jrzb:SetActive(false)
	end

	if self.act_id == ACTIVITY_TYPE.LIMIT_TIME_GIFT then
		-- self.node_list["ActivityButtonView"].transform.anchoredPosition = Vector2(-150, 130) --出现的位置
		-- local sequence = DG.Tweening.DOTween.Sequence()
		-- sequence:AppendInterval(2)
		-- sequence:AppendCallback(function()
		-- 	self.node_list["ActivityButtonView"].transform:DOLocalMove(Vector3(0, 0, 0), 1)
			self.node_list["Icon"].animator.enabled = true
			self.node_list["Icon"].animator:SetBool("is_shake", true)
		-- end)
	end

	if self.act_id == ACTIVITY_TYPE.CAP_UP_GIFT then
		-- self.node_list["ActivityButtonView"].transform.anchoredPosition = Vector2(160, 130) --出现的位置
		-- local sequence = DG.Tweening.DOTween.Sequence()
		-- sequence:AppendInterval(2)
		-- sequence:AppendCallback(function()
		-- 	self.node_list["ActivityButtonView"].transform:DOLocalMove(Vector3(-74, 0, 0), 1)
			self.node_list["Icon"].animator.enabled = true
			self.node_list["Icon"].animator:SetBool("is_shake", true)
		-- end)
	end

	self.relevance_parent = nil
	self.relevance_child = nil
end

function MainActivityBtn:ReleaseCallBack()
	--self.node_list["bg"]:SetActive(true)
	self:RemoveShowRankInfoTimer()

	if self.shrinkbuttons_change then
		GlobalEventSystem:UnBind(self.shrinkbuttons_change)
		self.shrinkbuttons_change = nil
	end
end

function MainActivityBtn:OnFlush(param_t)
	if self.relevance_parent then
		self.relevance_parent:Flush("all", param_t)
	end

	for k,v in pairs(param_t or {"all"}) do
		if k == "CheckSpecialEffectBtn" then
			self:CheckSpecialEffectBtn(v[1])
		elseif k == "SetSprite" then
			self:SetSprite(v[1], v[2], v[3])
		elseif k == "SetBtnEnable" then
			self:SetBtnEnable(v[1])
		elseif k == "SetTime" then
			self:SetTime(v[1])
		elseif k == "SetSiblingIndex" then
			self:RealSetSiblingIndex(tonumber(v[1]))
		elseif k == "SetAsLastSibling" then
			self:SetAsLastSibling()
		elseif k == "SetRedPoint" then
			self:SetRedPoint(v[1])
		elseif k == "SetCanvasGroupVal" then
			self:SetCanvasGroupVal(v[1])
		elseif k == "HideButton" then
			self:HideButton()
		elseif k == "ShowActiveButton" then
			self:ShowActiveButton()
		elseif k == "SetBottomContent" then
			self:SetBottomContent(v[1], v[2])
		elseif k == "SetEndTime" then
			self:SetEndTime(v[1], v[2], v[3], v[4], v[5])
		elseif k == "ShowEffect" then
			self:ShowEffect(v[1])
		elseif k == "ShowAni" then
			self:ShowAni(v[1])
		elseif k == "ShowFlyAni" then
			self:ShowFlyAni()
		elseif k == "ShowLiuGuang" then
			self:ShowLiuGuang(v[1])
		elseif k == "ResetLiuGuangEffect" then
			self:ResetLiuGuangEffect()
		elseif k == "UpdateBg" then
			self:UpdateBg(v[1])
		elseif k == "SetActivityStateFlag" then
			self:SetActivityStateFlag(v[1], v[2])
		elseif k == "SetRankInfo" then
			self:SetRankInfo(v)
		elseif k == "SetBubbleDesc" then
			self:SetBubbleInfo(v)
		elseif k == "SetFoldEffect" then
			self:SetFoldEffect(v[1])
		elseif k == "SetFirstRechargeTip" then
			self:SetFirstRechargeTip()
		elseif k == "YanYuGeNobleTip" then
			self:FlushYanYuGeNobleTip()
		else
			BaseRender.OnFlush(self, param_t)
		end
	end
end

function MainActivityBtn:GetActId()
	return self.act_id
end

function MainActivityBtn:ResetActId(new_act_id)
	self.act_cfg = ActivityWGData.Instance:GetDailyActivityDailyCfg(new_act_id)
	self.act_id = new_act_id
end

function MainActivityBtn:GetIconPos()
	return self.node_list["Icon"] and self.node_list["Icon"].transform.position or Vector3(0,0,0)
end

function MainActivityBtn:CheckSpecialEffectBtn(activity_type)
	if IsEmptyTable(self.node_list) or not self.node_list.SpecialEff then
		return 
	end

	local cfg = ActBtnSpecialEffectConfig[activity_type]
	if cfg then
		self.node_list.Icon.image.enabled = false
		local bundle_name, asset_name = ResPath.MainUIActivityButton(cfg.asset)
		self.node_list["SpecialEff"]:ChangeAsset(bundle_name, asset_name)
		self.node_list["SpecialEff"]:SetActive(true)
	else
		self.node_list["SpecialEff"]:SetActive(false)
	end
end

function MainActivityBtn:SetSprite(icon_res, name_res, bg_res)
    if IsEmptyTable(self.node_list) then
		return
	end

    if self.act_id == ACTIVITY_TYPE.FIRST_CHONGZHI then--首充入口特殊处理
		-- local icon_list = {"a2_zjm_icon_sc1", "a1_btn_zc", "a1_btn_hc"}
    	-- local sc_index = self:GetFirstRechargeBtnResIndex()
		-- icon_res = "a2_zjm_icon_sc1"--icon_list[sc_index]

	elseif self.act_id == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_EXTINCT_GIFT then
		-- local other_cfg = RebateGiftActivityWGData.Instance:GetGiftOtherCfg()
		-- if other_cfg then
		-- 	icon_res = other_cfg.res_name
		-- end
	elseif self.act_id == ACTIVITY_TYPE.WORLD_TREASURE then
		local icon_name = WorldTreasureWGData.Instance:GetCurShowActiveIcon()
		if icon_name then
			icon_res = icon_name
		end
	elseif self.act_id == ACTIVITY_TYPE.LIFE_INDULGENCE_ACTIVITY then
		local icon_name = LifeIndulgenceWGData.Instance:GetMainIconRes()
		if icon_name then
			icon_res = icon_name
		end
	end

	self.node_list["SpecialEff"]:SetActive(false)
	self.node_list.Icon.image.enabled = false
	--2021 07 26 策划说屏蔽神兵觉醒动画图标
    -- if self.act_id == ACTIVITY_TYPE.SHENBINGAWAKEN_ACTIVITY then		-- 神兵觉醒特殊处理
    --     local prof = RoleWGData.Instance:GetRoleProf()
    --     local cfg = ActBtnSpecialEffectConfig[self.act_id]
    --     if cfg then
    --         local bundle_name, asset_name = ResPath.MainUIActivityButton(cfg["asset" .. prof])
    --         self.node_list["SpecialEff"]:ChangeAsset(bundle_name, asset_name)
    --         self.node_list["SpecialEff"]:SetActive(true)
    --     end
	-- else
	if self.act_id == ACTIVITY_TYPE.XIUZHEN_ROAD then
        -- local cfg = ActBtnSpecialEffectConfig[self.act_id]
        -- if cfg then
        --     local bundle_name, asset_name = ResPath.MainUIActivityButton(cfg.asset)
        --     self.node_list["SpecialEff"]:ChangeAsset(bundle_name, asset_name)
        --     self.node_list["SpecialEff"]:SetActive(true)
        -- end
        --2021 07 28 策划说屏蔽大圣归来动画图标
        self.node_list["SpecialEff"]:SetActive(false)
        local bundle, asset = ResPath.GetF2MainUIImage(icon_res)
        self.node_list.Icon.image:LoadSprite(bundle, asset, function()
            self.node_list.Icon.image:SetNativeSize()
        end)
    else

        local bundle, asset = ResPath.GetF2MainUIImage(icon_res)
        self.node_list.Icon.image:LoadSprite(bundle, asset, function()
            self.node_list.Icon.image:SetNativeSize()
        end)
    end

	if self.node_list["act_name"] and self.act_id ~= ACTIVITY_TYPE.LIMIT_TIME_GIFT then
		if name_res and name_res ~= "" then
	 		local name_bundle, name_asset = ResPath.GetF2MainUIImage(name_res)
			self.node_list["act_name"].image:LoadSprite(name_bundle, name_asset, function()
		 		self.node_list.act_name.image:SetNativeSize()
		 	end)
	 		self.node_list["act_name"]:SetActive(true)
	 	else
	 		self.node_list["act_name"]:SetActive(false)
	 	end
	 end

	 if self.node_list["bg"] then
	 	if bg_res and bg_res ~= "" then
	 		local name_bundle, name_asset = ResPath.GetF2MainUIImage(bg_res)
			self.node_list["bg"].image:LoadSprite(name_bundle, name_asset, function()
		 		self.node_list.bg.image:SetNativeSize()
		 	end)
	 	end
	 end
	if self.node_list.yqc then
		self.node_list.yqc:SetActive(self.act_id == ACTIVITY_TYPE.XIUZHEN_ROAD)
	end
end

--添加按钮点击事件
function MainActivityBtn:AddClickEventListener(callback)
	if IsEmptyTable(self.node_list) then
		self.click_callback = callback
		return
	end
	XUI.AddClickEventListener(self.node_list.ActivityButtonView, function()
		callback()
		self:OnClick()
	end)
end

function MainActivityBtn:OnClick()
	self:ShowAni(false)
	MainuiWGData.Instance:SetActivityBtnIsClick(self.data)
	self:ResetLiuGuangEffect()
end

function MainActivityBtn:SetBtnEnable(enable)
	if IsEmptyTable(self.node_list) then
		return
	end

	self.node_list.ActivityButtonView.canvas_group.blocksRaycasts = enable
	self.node_list.ActivityButtonView.canvas_group.interactable = enable
	self.node_list.ActivityButtonView.button.interactable = enable
end

--设置事件
function MainActivityBtn:SetTime(time)
	if IsEmptyTable(self.node_list) then
		print_error("btn can not init！！！！！")
		self:RemoveCountDown()
		return
	end

	if self.relevance_parent then
		self.relevance_parent:SetTime(time)
	end

	self.node_list.time.text.text = time
end

function MainActivityBtn:RealSetSiblingIndex(index)
	if self.view then
		self.view.transform:SetSiblingIndex(index)
	end
end
function MainActivityBtn:SetAsLastSibling()
	if self.view then
		self.view.transform:SetAsLastSibling()
	end
end
--更新红点
function MainActivityBtn:SetRedPoint(enable)
	self.red_point_active = enable
	if self.node_list and self.node_list.RedPoint then
		self.node_list.RedPoint:SetActive(enable and self.act_id ~= ACTIVITY_TYPE.CAP_UP_GIFT)
	end
end

function MainActivityBtn:FlushYanYuGeNobleTip()
	if self.node_list and self.node_list.yyg_yyls_get_tip then 
		self.node_list.yyg_yyls_get_tip:CustomSetActive((YanYuGeWGData.Instance:GetNoblePrivilegeRedPoint() > 0) and (self.act_id == ACTIVITY_TYPE.YanYuGe))
	end
end

function MainActivityBtn:RedPointIsShow()
	return self.red_point_active
end

function MainActivityBtn:SetCanvasGroupVal(val)
	if IsEmptyTable(self.node_list) then return end
	val = self:GetIsShouQi() and 0 or val 											-- 如果是收起状态则不允许设置为不透明
	self.node_list.ActivityButtonView.canvas_group.alpha = val
end

local BTNHIGHT = 96 			-- 按钮高度
function MainActivityBtn:HideButton()
	if IsEmptyTable(self.node_list) then return end
	BTNHIGHT = 96
	self.node_list.ActivityButtonView.canvas_group.alpha = 0
	self.node_list.ActivityButtonView.canvas_group.blocksRaycasts = false
	self.node_list.ActivityButtonView.rect.sizeDelta = Vector2(0, BTNHIGHT)
end

function MainActivityBtn:ShowActiveButton()
	if IsEmptyTable(self.node_list) then return end
	BTNHIGHT = 96
	self.node_list.ActivityButtonView.canvas_group.alpha = 1
	self.node_list.ActivityButtonView.canvas_group.blocksRaycasts = true
	self.node_list.ActivityButtonView.canvas_group.interactable = true
	self.node_list.ActivityButtonView.button.interactable = true
	self.node_list.ActivityButtonView.rect.sizeDelta = Vector2(BTNHIGHT, BTNHIGHT)
end

function MainActivityBtn:RemoveCountDown()
	if nil ~= self.countdown_key and CountDownManager.Instance:HasCountDown(self.countdown_key) then
		print_log("RemoveCountDown 		", self.countdown_key, TimeWGCtrl.Instance:GetServerTime(), self.act_id)
		CountDownManager.Instance:RemoveCountDown(self.countdown_key)
		self.countdown_key = nil

		if self.act_id ~= nil and ActivityWGData ~= nil and ActivityWGData.Instnace ~= nil then
			local data = ActivityWGData.Instnace:GetActivityStatuByType(self.act_id)
			if data ~= nil and data.status ~= nil and data.status ~= ACTIVITY_STATUS.CLOSE then
				local cur_time = TimeWGCtrl.Instance:GetServerTime()
				if data.next_time ~= nil and data.next_time > cur_time then
					print_error("活动图标移除倒计时有问题: 		", self.act_id, data, debug.traceback())
				end
			end
		end
	end
end

function MainActivityBtn:SetBottomContent(content, color)
	local is_end = false
	if content == nil or content == "" then
		self:RemoveCountDown()
		self:SetTime("")

		if self.end_time_complete_func ~= nil then
			self.end_time_complete_func()
		end
		is_end = true
	end
	if not is_end then
		-- if color ~= nil then
		-- 	content = string.format("<color=%s>%s</color>", color, content)
		-- end
		self:SetTime(content)
	end
end

--设置剩余时间 秒
function MainActivityBtn:SetEndTime(end_time, countdown_update_callback, coundown_complete_callback, interval,color)
	local old_countdown_key = self.countdown_key
	if nil == self.countdown_key then
		local num = MainuiWGData.Instance:GetMainUiActBtnKeyNum()
		self.countdown_key = "icon_countdown" .. num
	end
	self.end_time_complete_func = nil
	CountDownManager.Instance:RemoveCountDown(self.countdown_key)
	local update_fun = function(elapse_time, total_time)
		if self:IsNil() then
			self:RemoveCountDown()
			return
		end
		
        local time = CountDownManager.Instance:GetRemainTime(self.countdown_key)
        if self.act_cfg and self.act_cfg.act_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FORTUNE_CAT then --招财猫特殊处理
            if time <= 3 * 60 * 60 then
                local time_str = MainuiWGData.Instance:ConvertMainTopBtnTime(time)
                color = color or COLOR3B.GREEN
                self:SetBottomContent(time_str, color)
            end
        elseif self.act_cfg and self.act_cfg.act_type == ACTIVITY_TYPE.SHENBINGAWAKEN_ACTIVITY then --神兵觉醒特殊处理
            --if time <= 3 * 60 * 60 then
                -- local has_day = math.floor(time / 3600) >= 24
                -- local str = has_day and Language.Mainui.DayCloseLater or ""
                local time_str = MainuiWGData.Instance:ConvertMainTopBtnTime(time)
                color = color or COLOR3B.GREEN
                self:SetBottomContent(time_str, color)
            --end
        else
            local time_str = MainuiWGData.Instance:ConvertMainTopBtnTime(time)
            color = color or COLOR3B.GREEN

            self:SetBottomContent(time_str, color)
        end

		if nil ~= countdown_update_callback then
			countdown_update_callback(elapse_time, total_time)
		end
	end

	local complete_fun = function()
		self:RemoveCountDown()
		self.countdown_key = nil
		if nil ~= coundown_complete_callback then
			coundown_complete_callback()
		end
	end

	interval = interval or 0.5
	local cur_time = TimeWGCtrl.Instance:GetServerTime()

	if end_time -  cur_time> 0 then
		update_fun(0, end_time)
		if self.countdown_key then
			self.end_time_complete_func = coundown_complete_callback
			CountDownManager.Instance:AddCountDown(self.countdown_key, update_fun, complete_fun, end_time, nil, interval)
		else
			print_error("SetEndTime Error", old_countdown_key, self.countdown_key, cur_time, end_time, self.act_id)
		end
	else
		self.countdown_key = nil
		self:SetTime("")
	end
end



function MainActivityBtn:ShowEffect(enable)
	self:ShowAni(enable)
	self:ShowLiuGuang(enable)
end

function MainActivityBtn:ShowAni(enable)
	if self.node_list and self.node_list["Icon"] then
		local is_click = MainuiWGData.Instance:GetActivityBtnIsClick(self.act_id)
        if not is_click then
            self.node_list["Icon"].animator.enabled = true
            self.node_list["Icon"].animator:SetBool("is_shake", enable)
        else
            self.node_list["Icon"].animator.enabled = false
		end
	else
		self.icon_state = enable
	end
end

function MainActivityBtn:SetHasFly(value)
	self.has_fly = value
end

function MainActivityBtn:ShowFlyAni()
	if self.act_cfg == nil or self.has_fly then
		return
	end
	self.has_fly = true
	local cfg = {name = "activity_" .. self.act_cfg.act_type, show_name = self.act_cfg.name, icon = self.act_cfg.res_name}
	local _, value = next(ConfigManager.Instance:GetAutoConfig("funopen_auto").funopen_list)
	for k,v in pairs(value) do
		cfg[k] = cfg[k] or 0
	end
	TipWGCtrl.Instance:ShowOpenFunFlyView(cfg, self:GetView())
end

--流光特效
function MainActivityBtn:ShowLiuGuang(enable)
	if self.node_list and self.node_list["effect"] then
		self.node_list["effect"]:SetActive(enable)
	else
		self.effect_state = enable
	end
end

function MainActivityBtn:SetFoldEffect(enable)
	if self.node_list and self.node_list["fold_effect"] then
		self.node_list["fold_effect"]:SetActive(enable)
	end
end

function MainActivityBtn:SetTimeStrType(time_str_type)
	self.time_str_type = time_str_type
end

function MainActivityBtn:SetActCfg(act_cfg)
	self.act_cfg = act_cfg
end

--判断玩家等级来关闭按钮
--@true 表示玩家已经超过了参与等级
function MainActivityBtn:CheckMainUIActIsLimit()
	return MainuiWGData.Instance:CheckMainUIActIsLimit(self.act_cfg)
end

function MainActivityBtn:ResetLiuGuangEffect()
	if self.act_cfg then
		if self.act_cfg.is_show_effect == MAIN_ACT_LIUGUANG_TYPE.ALWAYS then
			self:ShowLiuGuang(true)
		elseif self.act_cfg.is_show_effect == MAIN_ACT_LIUGUANG_TYPE.SOMETIME and not MainuiWGData.Instance:GetActivityBtnIsClick(self.data) then
			self:ShowLiuGuang(true)
		else
			self:ShowLiuGuang(false)
		end
	end
end

-- 设置活动状态标签
function MainActivityBtn:SetActivityStateFlag(act_type, status)
	self.node_list.jjks:SetActive(false)
	self.node_list.jxz:SetActive(false)
	self.node_list.xsz:SetActive(false)
	self.node_list.jrzb:SetActive(false)

	if self.act_cfg and self.act_cfg.is_show_state == 0 then
		return
	end

	if (status == ACTIVITY_STATUS.OPEN or status == ACTIVITY_STATUS.XUNYOU) then
		if act_type == ACTIVITY_TYPE.KF_XIANMENGZHAN and GuildWGData.Instance:GetIsPrepareRestTime() then
			self.node_list.xsz:SetActive(true)
			return
		end

		local show_jxz = act_type ~= ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_XIANLING_ZHEN and act_type ~= ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FORTUNE_CAT
		self.node_list.jxz:SetActive(show_jxz)
	elseif status ~= ACTIVITY_STATUS.CLOSE then
		if act_type == ACTIVITY_TYPE.XIANMENGZHAN and GuildBattleRankedWGData.Instance:GetPrepareRestTime() then
			self.node_list.xsz:SetActive(true)
			return
		end

		if act_type == ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_ULTIMATE_BATTLE and UltimateBattlefieldWGData.Instance:CheckActIsOpen() then
			self.node_list.jrzb:SetActive(true)
			return
		end

		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)
		if act_cfg then
			if act_cfg.ready_can_enter == 0 then
				self.node_list.jjks:SetActive(true)
			else
				self.node_list.jrzb:SetActive(true)
			end
		end
	end
end

function MainActivityBtn:SetRankInfo(param_t)
    if #param_t == 2 then
        for i = 1, 2 do
            local data_info = param_t and param_t[i]
            if data_info and data_info.is_show then
                self.node_list["rank_label" .. i]:SetActive(true)
                self.node_list["rank_label" .. i].text.text = data_info.rank_label or ""
                -- self.node_list.rank_info_root:SetActive(true)
				self.node_list.time:SetActive(false)
				self.show_rank_info_root = true
            end
        end
    else
        local data_info = param_t and param_t[1]
        self.node_list["rank_label2"]:SetActive(false)
        if data_info and data_info.is_show then
            self.node_list["rank_label1"]:SetActive(true)
            self.node_list.rank_label1.text.text = data_info.rank_label or ""
            -- self.node_list.rank_info_root:SetActive(true)
			self.node_list.time:SetActive(false)
			self.show_rank_info_root = true
        else
            -- self.node_list.rank_info_root:SetActive(false)
			self.node_list.time:SetActive(true)
			self.show_rank_info_root = false
        end
    end

	-- 策划HYX需求 这个排行信息不再常驻，需要显示几秒然后消失， 然后切换右上角收启按钮时候  重新走倒计时显示逻辑
	if self.show_rank_info_root then
		local is_on = MainuiWGCtrl.Instance.view:GetShrinkButtonIsOn()

		if is_on then
			self.node_list.rank_info_root:SetActive(true)

			self:RemoveShowRankInfoTimer()

			self.show_rank_info_timer = GlobalTimerQuest:AddDelayTimer(function()
				if self.node_list.rank_info_root then
					self.node_list.rank_info_root:CustomSetActive(false)
				end
			end, UITween_CONSTS.MainUIBtn.RankInfoRootShowTime)
		end
	else
		self.node_list.rank_info_root:CustomSetActive(false)
	end
end

function MainActivityBtn:RemoveShowRankInfoTimer()
	if self.show_rank_info_timer then
		GlobalTimerQuest:CancelQuest(self.show_rank_info_timer)
		self.show_rank_info_timer = nil
	end
end

function MainActivityBtn:ShrinkButtonsValueChange(isOn)
	self:RemoveShowRankInfoTimer()

	if isOn then
		if self.show_rank_info_root and self.node_list and self.node_list.rank_info_root then
			self.node_list.rank_info_root:SetActive(true)

			self.show_rank_info_timer = GlobalTimerQuest:AddDelayTimer(function()
				if self.node_list.rank_info_root then
					self.node_list.rank_info_root:CustomSetActive(false)
				end
			end, UITween_CONSTS.MainUIBtn.RankInfoRootShowTime)
		end
	end
end

function MainActivityBtn:SetFirstRechargeTipClose()
	if CountDownManager.Instance:HasCountDown("first_recharge_advantage_tip") then
		CountDownManager.Instance:RemoveCountDown("first_recharge_advantage_tip")
	end
	if self.node_list.first_recharge_advantage_tip then
		self.node_list.first_recharge_advantage_tip:SetActive(false)
	end
end

function MainActivityBtn:SetFirstRechargeTip()
	self.node_list.first_recharge_desc1.text.text = Language.Recharge.RechargeAdvantageTipDesc1
	self:FlushFirstRechargeTipCountDown()
end

function MainActivityBtn:FlushFirstRechargeTipCountDown()
	CountDownManager.Instance:RemoveCountDown("first_recharge_advantage_tip")
	local end_time = ServerActivityWGData.Instance:GetSCTipsEndTime()
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	local count_down_time = end_time - now_time

	if count_down_time > 0 then
		self.node_list.first_recharge_advantage_tip:SetActive(true)
		self:UpdateFirstRechargeTipCountDown(0, count_down_time)
		CountDownManager.Instance:AddCountDown(
			"new_server_recharge_tip_count_down",
			BindTool.Bind(self.UpdateFirstRechargeTipCountDown, self),
			function ()
				CountDownManager.Instance:RemoveCountDown("first_recharge_advantage_tip")
				if self.node_list.first_recharge_advantage_tip then
					self.node_list.first_recharge_advantage_tip:SetActive(false)
				end
			end,
			nil,
			count_down_time,
			0.02
		)
	else
		self.node_list.first_recharge_advantage_tip:SetActive(false)
	end
end

function MainActivityBtn:UpdateFirstRechargeTipCountDown(elapse_time, total_time)
	if self:IsNil() then
		CountDownManager.Instance:RemoveCountDown("first_recharge_advantage_tip")
		return
	end

	local floor = math.floor
	local modf = math.modf
	local time = total_time - elapse_time
	local min = floor((time / 60) % 60)
	local s = floor(time % 60)
	local ss,ms = modf(time)
	ms = floor(ms * 100)
	self.node_list.first_recharge_count_down_lbl.text.text = string.format(Language.Recharge.RechargeTipCountDown2, min, s)
end

function MainActivityBtn:HideBg()
	-- self.node_list["bg"]:SetActive(false)
end

function MainActivityBtn:SetShouQiStateFunc(func)
	self.is_shouqi_func = func
end

-- 判断当前按钮是否是收起的状态
function MainActivityBtn:GetIsShouQi()
	if self.is_shouqi_func then
		return self.is_shouqi_func(self.act_id)
	else
		return false
	end
end

function MainActivityBtn:GetFirstRechargeBtnResIndex()
	local max_stage = 3
	local cur_show_index = 1
	for i = max_stage, 1, -1 do
		if ServerActivityWGData.Instance:GetRCGearFlag(i - 1) then
			cur_show_index = i
			break
		end 
	end
	return cur_show_index
end

-- 气泡
function MainActivityBtn:SetBubbleInfo(info)
	if not info or not info.show_bubble or not info.desc or info.desc == "" then
		self.node_list["bubble_node"]:SetActive(false)
		return
	end

	self.node_list["bubble_node"]:SetActive(true)
	self.node_list["bubble_text"].text.text = info.desc
end

function MainActivityBtn:CheckIsNeedAddCloseTweenForMain()
	if not self.act_id then
		return
	end

	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(self.act_id)

	if (not act_cfg) or (not act_cfg.open_panel_name) or (act_cfg.is_close_to_main_act_btn == 0) then
		return
	end

	local t = Split(act_cfg.open_panel_name, "#")
	local view_name = t[1]

	if view_name == nil or view_name == "" then
		return
	end

	local view = ViewManager.Instance:GetView(view_name)
	if view then
		view:AddActToOpenSource(self.act_id)
	end
end

function MainActivityBtn:SetRelevanceParnet(act_btn)
	self.relevance_parent = act_btn
	local act_data = ActivityWGData.Instance:GetActivityStatuByType(self.act_id)
	local status = act_data and act_data.status or 0
	local end_time = act_data and act_data.end_time or 0

	if self.relevance_parent then
		self.relevance_parent:Flush("SetActivityStateFlag", {self.act_id, status})
		self.relevance_parent:Flush("SetEndTime", {end_time})
	end
end

function MainActivityBtn:SetRelevanceChild(act_btn)
	self.relevance_child = act_btn
end