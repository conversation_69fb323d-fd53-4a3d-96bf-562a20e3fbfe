GuideBossInfoView = GuideBossInfoView or BaseClass(SafeBaseView)

function GuideBossInfoView:__init()
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_guide_boss_info")
	
	self.active_close = false
	self.main_ui_is_init = false
end

function GuideBossInfoView:__delete()
	self.is_load_complete = nil
	if self.info then
        local obj = self.info.gameObject
        ResMgr:Destroy(obj)
        self.info = nil
    end
end

function GuideBossInfoView:ResetTaskPanel()
	if self.task_panel_change then
		MainuiWGCtrl.Instance:ResetTaskPanel()
		self.task_panel_change = false
	end
end

function GuideBossInfoView:ReleaseCallBack()
	self.is_load_complete = nil
	self:ResetTaskPanel()

	if self.info then
		self.info:DeleteMe()
        local obj = self.info.gameObject
        ResMgr:Destroy(obj)
        self.info = nil
    end

	if self.mainui_open_complete_handle then
		GlobalEventSystem:UnBind(self.mainui_open_complete_handle)
		self.mainui_open_complete_handle = nil
	end

	MainuiWGCtrl.Instance:SetAutoGuaJi(false)

	self.is_fisrt = nil
end

function GuideBossInfoView:LoadCallBack()
	FuBenPanelWGCtrl.Instance:GetFuBenPanemView():SetActive(false)
	self.info = GuideBossInfoPanel.New(self.node_list.root_fuben_info)
	if MainuiWGCtrl.Instance:IsLoadMainUiView() then
		self:InitCallBack()
	else
		self.mainui_open_complete_handle = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.InitCallBack, self))
	end
	MainuiWGCtrl.Instance:SetAutoGuaJi(true)
end

function GuideBossInfoView:OpenCallBack()
	if self.is_load_complete and self.info then
        self.info:SetActive(true)
    end
end

-- 机器人打机器人触发
function GuideBossInfoView:OnRobertAttackRobert(attacker_robert_id, target_robert_id)
	local boss_hurt_list = RobertManager.Instance:GetRobertHurtList()
	local show_assist = boss_hurt_list[Scene.Instance:GetMainRole():GetObjId()] ~= nil
	--self.node_list.list_view:SetActive(show_assist)--引导boss不显示协助
end

function GuideBossInfoView:InitCallBack()
	local function callback(show_node)
		self.info:SetInstanceParent(show_node)
		self.info:AnchorPosition(150, -107)
		--self.info:AnchorPosition(0, 6)
		self.info:Flush()
	end
	MainuiWGCtrl.Instance:GetTaskMaskRootNode(callback)

	self.main_ui_is_init = true
end

function GuideBossInfoView:CloseCallBack()
	self:ResetTaskPanel()
	if self.info then
        self.info:SetActive(false)
        --self.node_list.list_view:SetActive(false)--引导boss不显示协助
    end
    GlobalEventSystem:UnBind(self.attack_robert_event)
end

function GuideBossInfoView:ShowIndexCallBack()
	self.attack_robert_event = GlobalEventSystem:Bind(OtherEventType.ROBERT_ATTACK_ROBERT, BindTool.Bind(self.OnRobertAttackRobert, self))
	self.task_panel_change = true
	self:Flush()
end

function GuideBossInfoView:OnFlush()
	if self.info then
        self.info:Flush()
    end
end


GuideBossInfoPanel = GuideBossInfoPanel or BaseClass(BaseRender)
function GuideBossInfoPanel:__init()
	self.boss_reward_t = {}
end

function GuideBossInfoPanel:__delete()
	for k,v in pairs(self.boss_reward_t) do
		v:DeleteMe()
	end
	self.boss_reward_t ={}
	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end
	if self.next_call_timer then
		GlobalTimerQuest:CancelQuest(self.next_call_timer)
		self.next_call_timer = nil
	end
end

function GuideBossInfoPanel:AnchorPosition( x,y )
	self.view.rect.anchoredPosition = Vector2(x,y)
end

function GuideBossInfoPanel:LoadCallBack()

end

function GuideBossInfoPanel:OnFlush()
	local boss_hurt_list = RobertManager.Instance:GetRobertHurtList()
	local show_assist = boss_hurt_list[Scene.Instance:GetMainRole():GetObjId()] ~= nil
	local is_pass = not RobertManager.Instance:IsFighting()
	self.node_list.BossList:SetActive(not show_assist)
	self.node_list.task_root_view:SetActive(show_assist)
	if show_assist and not is_pass then
		local has_team = SocietyWGData.Instance:GetTeamMemberCount() > 0
		local hurt_list = {}
		local damage_value = 0
		local max_value = 0
		if has_team then
			for k,v in pairs(boss_hurt_list) do
				if v.is_mine or SocietyWGData.Instance:IsTeamMember(v.role_id) then
					damage_value = damage_value + v.damage_value
				else
					max_value = math.max(max_value, v.damage_value)
				end
				if v.is_mine or not SocietyWGData.Instance:IsTeamMember(v.role_id) then
					table.insert(hurt_list, v)
				end
			end
			max_value = math.max(damage_value, max_value)
		else
			for k,v in pairs(boss_hurt_list) do
				max_value = math.max(max_value, v.damage_value)
				if v.is_mine then
					damage_value = v.damage_value
				end
				table.insert(hurt_list, v)
			end
		end
		for k,v in pairs(hurt_list) do
			v.max_value = max_value
			if v.is_mine then
				v.damage_value = damage_value
			end
		end
		table.sort(hurt_list, SortTools.KeyUpperSorter("damage_value"))
		if not self.rank_list then
			self.rank_list = AsyncListView.New(BossGuideAssistInfoItem, self.node_list.TaskList1)
		end
		self.rank_list:SetDataList(hurt_list)
		--local assist_info = BossAssistWGData.Instance:GetAssistInfo()
		self.node_list.my_damate.text.text = CommonDataManager.ConverNumber(damage_value)
		self.node_list.per_bg.slider.value = damage_value / max_value
	else
	    local data = BossWGData.Instance:GetWorldBossList()[1]
	    if data == nil then
	    	return
	    end

	    local boss_id = data.boss_id
	    local drop_item_list = ConfigManager.Instance:GetAutoConfig("newer_cfg_auto").wsjmw_reward[1].reward_item
	    local first_item_list = ConfigManager.Instance:GetAutoConfig("newer_cfg_auto").wsjmw_reward[1].first_reward_item

	    if show_assist and is_pass and not self.has_open_reward then
	    	self.has_open_reward = true
	  --   	local protocol = SCGuildAssisBossRewardInfo.New()
			-- protocol.target_boss_id = boss_id
			-- protocol.has_first_reward = 1
			-- protocol.reward_item_list = {}
			-- for k,v in pairs(drop_item_list) do
			-- 	table.insert(protocol.reward_item_list, v)
			-- end
			-- protocol.first_reward_item_list = {}
			-- for k,v in pairs(first_item_list) do
			-- 	table.insert(protocol.first_reward_item_list, v)
			-- end
			-- BossAssistWGCtrl.Instance:OnGuildAssisBossRewardInfo(protocol)
			FuBenWGCtrl.SendWTSReward(2)
	    end
	    local boss_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[boss_id]
	    if boss_cfg then
	        local color = COLOR3B.D_GREEN
	        self.node_list.boss_condition.text.text = string.format(Language.Boss.GuideBossDec1, boss_cfg.name, color, is_pass and 1 or 0)
	        for k,v in pairs(drop_item_list or {}) do
	           if self.boss_reward_t[k] == nil then
	                self.boss_reward_t[k] = ItemCell.New(self.node_list.boss_reward)
	           end
	           self.boss_reward_t[k]:SetData(v)
	        end
	    end
	end
	if self.next_call_timer then
		GlobalTimerQuest:CancelQuest(self.next_call_timer)
		self.next_call_timer = nil
	end
	if nil == self.next_call_timer then
		self.next_call_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.UpdateNextCallTime, self), 1)
	end
end

function GuideBossInfoPanel:UpdateNextCallTime()
	self:Flush()
end



--------------------------------------------------------------
-- 引导 boss itemrender
--------------------------------------------------------------
GuideBossItemRender = GuideBossItemRender or BaseClass(BaseRender)

function GuideBossItemRender:__init(instance, parent)
	XUI.AddClickEventListener(self.node_list.BtnSelf,BindTool.Bind(self.OnClickBossRender, self))
	self.boss_text = nil
	self.refresh_event = nil
	self.node_list["reward_title"]:SetActive(false)
	self.parent = parent
end

function GuideBossItemRender:__delete()
	self.boss_text = nil
	if self.refresh_event then
		GlobalTimerQuest:CancelQuest(self.refresh_event)
		self.refresh_event = nil
	end
	self.parent = nil
end

function GuideBossItemRender:OnClickBossRender()
    if self.data then
		MoveCache.SetEndType(MoveEndType.Auto)
		GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), self.data.info.x, self.data.info.y, 4)
	end
end

function GuideBossItemRender:OnFlush()
	if next(self.data) == nil and not self.data then return end

	self:RefreshRemainTime()
	if self.refresh_event then
		GlobalTimerQuest:CancelQuest(self.refresh_event)
		self.refresh_event = nil
	end
	self.refresh_event = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.RefreshRemainTime,self),0.2)
end

function GuideBossItemRender:RefreshRemainTime()
	if self.data then
		local time_txt
		if not self.data.is_alive then
			time_txt =  ToColorStr(TimeUtil.FormatSecond(self.data.time - TimeWGCtrl.Instance:GetServerTime(), 3),COLOR3B.RED)
		else
			time_txt =  ToColorStr(Language.Boss.BossRefresh,'#7cffb7')
		end

		self.node_list.TextDesc.text.text = self.data.name
		self.node_list.TimeDesc.text.text = time_txt
	end
end

function GuideBossItemRender:FlushHl()
	if self.node_list["SelectLigth"] then
		self.node_list["SelectLigth"]:SetActive(false)
	end
end

function GuideBossItemRender:OnSelectChange(is_select)
	self.node_list["high"]:SetActive(is_select)
end

BossGuideAssistInfoItem = BossGuideAssistInfoItem or BaseClass(BaseRender)
function BossGuideAssistInfoItem:__init()

end
function BossGuideAssistInfoItem:__delete()

end
function BossGuideAssistInfoItem:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.button, BindTool.Bind(self.OnClickAttk, self))
end

function BossGuideAssistInfoItem:OnClickAttk()

end

function BossGuideAssistInfoItem:OnFlush()
	local name = self.data.role_name
	if SocietyWGData.Instance:GetTeamMemberCount() > 0 and self.data.is_mine then
		name = name .. Language.BossAssist.TeamFlag
	end
	self.node_list.name.text.text = name

	local damage = CommonDataManager.ConverNumber(self.data.damage_value)
	self.node_list.damage.text.text = self.data.is_mine and ToColorStr(damage, COLOR3B.ROLE_NAME_COLOR) or damage

	self.node_list.num.text.text = self.index
	self.node_list.per_bg.slider.value = self.data.damage_value / self.data.max_value
	self.node_list.meng:SetActive(false)
	self.node_list.attk:SetActive(not self.data.is_mine)

	if self.index < 4 then
		self.node_list["icon"]:SetActive(true)
		self.node_list["icon"].image:LoadSprite(ResPath.GetF2CommonIcon("icon_paiming_big_" .. self.index))
		self.node_list.num.text.text = ""
	else
		self.node_list["icon"]:SetActive(false)
		self.node_list.num.text.text = self.index
	end
end