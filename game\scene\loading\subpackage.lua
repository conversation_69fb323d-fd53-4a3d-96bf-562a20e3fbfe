-- 分包下载
SubPackage = SubPackage or BaseClass()

function SubPackage:__init()
	if SubPackage.Instance then
		print_error("[SubPackage] Attempt to create singleton twice!")
		return
	end

	SubPackage.Instance = self

	self.package_count = require("config/packages/package_count")
	self.packages_info = {}
	self.package_downloadermgr_list = {}
	self:InitPackages()
	self.is_changing_scene = false

	GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_ENTER, BindTool.Bind(self.OnChangeScene, self, true))
    GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind(self.OnChangeScene, self, false))
end

function SubPackage:__delete()
	SubPackage.Instance = nil
	self.packages_info = {}

	for k,v in pairs(self.package_downloadermgr_list) do
		v:DeleteMe()
	end
	self.package_downloadermgr_list = {}
end

function SubPackage:InitPackages()
	if not GAME_ASSETBUNDLE then
		return
	end

	local package_count = self:GetPackageCount()
	local dic = {}

	-- package_0是新手资源，一定存在
	local need_download_list = {}
	local last_need_download_list = {}
	local total_size = 0
	local downloaded_size = 0
	for i = 0, package_count do
		local file_name = "config/packages/package_" .. i
		local config = require(file_name)
		last_need_download_list = need_download_list
		need_download_list = {}

		if i ~= 1 then
			total_size = 0
			downloaded_size = 0
		end

		local func = function (bundle_name)
			if not dic[bundle_name] then
				dic[bundle_name] = true
				local bundle_hash = ResMgr:GetBundleHash(bundle_name)
				if nil ~= bundle_hash then
					local file_size = ResMgr:GetBundleSize(bundle_name) or 0
					total_size = total_size + file_size

					if ResUtil.IsFileExist(bundle_name, bundle_hash) then
						downloaded_size = downloaded_size + file_size
					else
						table.insert(need_download_list, bundle_name)
					end
				end
			end
		end

		for _, v in ipairs(config) do
			local bundle_name = v.bundle
			func(bundle_name)

			local deps = ResMgr:GetBundleDeps(bundle_name)
			if nil ~= deps then
				for _, dep in ipairs(deps) do
					func(dep)
				end
			end
		end

		-- 把package_0插入到package_1中
		-- 当下载第一个分包时，就会先下载新手资源了
		if i == 1 then
			for _, v in ipairs(last_need_download_list) do
				table.insert(need_download_list, v)
			end
		end

		self.packages_info[i] = {total_size = total_size, downloaded_size = downloaded_size, need_download_list = need_download_list, is_downloading = false}
		UnRequire(file_name)
	end
end

-- 获取分包的数量
function SubPackage:GetPackageCount()
	if GAME_ASSETBUNDLE then
		return self.package_count
	end

	return 0
end

-- 获取分包的状态
function SubPackage:GetPackageInfo(index)
	local package_info = self.packages_info[index]
	return package_info.total_size, package_info.downloaded_size, package_info.is_downloading
end

-- 开始下载指定的分包
function SubPackage:StartDownloadPackage(index, update_call_back, complete_callback)
	local downloader_mgr = self.package_downloadermgr_list[index]
	if nil == downloader_mgr then
		downloader_mgr = self:CreatePackage(index)
	end

	downloader_mgr:Start(update_call_back, complete_callback)
end

-- 暂停指定的分包
function SubPackage:PauseDownloadPackage(index)
	local downloader_mgr = self.package_downloadermgr_list[index]
	if nil == downloader_mgr then
		print_error("[SubPackage] Pause Download Package Error", index)
		return
	end

	downloader_mgr:Pause()
end

-- 是否对指定的分包限速
function SubPackage:LimitPackageSpeed(index, is_limit_speed)
	local downloader_mgr = self.package_downloadermgr_list[index]
	if nil == downloader_mgr then
		downloader_mgr = self:CreatePackage(index)
	end

	downloader_mgr:LimitDownLoadSpeed(is_limit_speed)
end

function SubPackage:CreatePackage(index)
	local package_info = self.packages_info[index]
	local downloader_mgr = SubPackageDownloaderMgr.New(package_info)
	downloader_mgr:OnChangeScene(self.is_changing_scene)
	self.package_downloadermgr_list[index] = downloader_mgr
	return downloader_mgr
end

function SubPackage:OnChangeScene(flag)
	self.is_changing_scene = flag
	for k,v in pairs(self.package_downloadermgr_list) do
		v:OnChangeScene(flag)
	end
end

----------------------------------------------------- SubPackageDownloaderMgr -----------------------------------------------------
SubPackageDownloaderMgr = SubPackageDownloaderMgr or BaseClass()

function SubPackageDownloaderMgr:__init(package_info)
	self.package_info = package_info
	self.error_bundles = {}
	self.download_stamp_list = {}
	self.downloading_count = 0
	self.is_changing_scene = false

	self.downloader_list = {}

	local download_delegate = BindTool.Bind1(self.DownloadCallback, self)
	local get_next_bundle_delegate = BindTool.Bind1(self.GetNextBundle, self)

	for i = 1, 5 do
		self.downloader_list[i] = SubPackageDownloader.New(download_delegate, get_next_bundle_delegate)
	end

	self.is_limit_speed = false
end

function SubPackageDownloaderMgr:__delete()
	self:Pause()
	for k,v in pairs(self.downloader_list) do
		v:DeleteMe()
	end
	self.downloader_list = nil
end

function SubPackageDownloaderMgr:Start(update_call_back, complete_callback)
	self.update_call_back = update_call_back
	self.complete_callback = complete_callback

	if self.package_info.is_downloading then
		return
	end
	self.package_info.is_downloading = true

	self.download_stamp_list = {}

	for k,v in ipairs(self.error_bundles) do
		table.insert(self.package_info.need_download_list, v)
	end
	self.error_bundles = {}

	if not self.is_changing_scene then
		self.downloader_list[1]:Start()
		for i = 2, 5 do
			if not self.is_limit_speed then
				self.downloader_list[i]:Start()
			end
		end
	end

	self:CancelTimeQuest()
	self.timer_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.Update, self), 1)

	self:CheckComplete()
end

function SubPackageDownloaderMgr:Pause()
	self.package_info.is_downloading = false
	self:CancelTimeQuest()
	for k,v in ipairs(self.downloader_list) do
		v:Pause()
	end
end

-- 是否限速
function SubPackageDownloaderMgr:LimitDownLoadSpeed(is_limit_speed)
	self.is_limit_speed = is_limit_speed
	if self.is_changing_scene then
		return
	end

	if self.package_info.is_downloading then
		for i = 2, 5 do
			if is_limit_speed then
				self.downloader_list[i]:Pause()
			else
				self.downloader_list[i]:Start()
			end
		end
	end
end

function SubPackageDownloaderMgr:Update()
	local now_time = Status.NowTime
	-- 统计最近5秒的下载量，求平均速度
	local total_size = 0
	for i = #self.download_stamp_list, 1, -1 do
		local info = self.download_stamp_list[i]
		if now_time - info.time_stamp > 5 then
			table.remove(self.download_stamp_list, i)
		else
			total_size = total_size + info.file_size
		end
	end

	local speed = math.floor(total_size / 5)
	if self.update_call_back then
		self.update_call_back(self.package_info.total_size, self.package_info.downloaded_size, speed)
	end
end

function SubPackageDownloaderMgr:CancelTimeQuest()
	if self.timer_quest then
		GlobalTimerQuest:CancelQuest(self.timer_quest)
		self.timer_quest = nil
	end
end

function SubPackageDownloaderMgr:DownloadCallback(is_succ, bundle_name)
	if is_succ then
		local file_size = ResMgr:GetBundleSize(bundle_name) or 0
		table.insert(self.download_stamp_list, 1, {time_stamp = Status.NowTime, file_size = file_size})
		self.package_info.downloaded_size = self.package_info.downloaded_size + file_size
	else
		table.insert(self.error_bundles, bundle_name)
	end

	self.downloading_count = self.downloading_count - 1
	self:CheckComplete()
end

function SubPackageDownloaderMgr:CheckComplete()
	if #self.package_info.need_download_list <= 0 and self.downloading_count <= 0 then
		if self.complete_callback then
			self.complete_callback(#self.error_bundles <= 0, self.package_info.total_size, self.package_info.downloaded_size)
		end

		self:Pause()
	end
end

function SubPackageDownloaderMgr:GetNextBundle()
	local bundle_name = table.remove(self.package_info.need_download_list)
	if nil ~= bundle_name then
		self.downloading_count = self.downloading_count + 1
	end

	return bundle_name
end

function SubPackageDownloaderMgr:OnChangeScene(flag)
	self.is_changing_scene = flag

	if flag then
		for i = 1, 5 do
			self.downloader_list[i]:Pause()
		end
	else
		if self.package_info.is_downloading then
			self.downloader_list[1]:Start()
			if not self.is_limit_speed then
				for i = 2, 5 do
					self.downloader_list[i]:Start()
				end
			end
		end
	end
end

----------------------------------------------------- SubPackageDownloader -----------------------------------------------------
SubPackageDownloader = SubPackageDownloader or BaseClass()

function SubPackageDownloader:__init(download_delegate, get_next_bundle_delegate)
	self.download_delegate = download_delegate
	self.get_next_bundle_delegate = get_next_bundle_delegate
	self.download_callback = BindTool.Bind(self.OnDownloaded, self)
	self.is_downloading = false
	self.is_wait = false
	self.download_bundle_name = nil
end

function SubPackageDownloader:__delete()
	self.download_delegate = nil
	self.get_next_bundle_delegate = nil
end

function SubPackageDownloader:Start()
	self.is_downloading = true
	self:DoDownLoad()
end

function SubPackageDownloader:Pause()
	self.is_downloading = false
end

function SubPackageDownloader:DoDownLoad()
	if self.is_downloading and not self.is_wait then
		self.is_wait = true
		if self.get_next_bundle_delegate then
			self.download_bundle_name = self.get_next_bundle_delegate()
			if nil ~= self.download_bundle_name then
				AssetBundleMgr:DownLoadBundles({self.download_bundle_name}, self.download_callback, ResLoadPriority.steal)
			end
		end
	end
end

function SubPackageDownloader:OnDownloaded(is_succ)
	if self.download_delegate then
		self.download_delegate(is_succ, self.download_bundle_name)
	end
	self.download_bundle_name = nil

	self.is_wait = false
	self:DoDownLoad()
end