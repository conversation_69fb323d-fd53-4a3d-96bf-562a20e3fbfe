FlowerEffectView = FlowerEffectView or BaseClass(SafeBaseView)

function FlowerEffectView:__init()
	self.view_layer = UiLayer.Guide
	self.open_tween = nil
	self.close_tween = nil
	self:AddViewResource(0, "uis/view/sendflower_ui_prefab", "flower_effect_view")
	self.effect_tab = {}
end

function FlowerEffectView:__delete()
	self:ClearDelay()
end

function FlowerEffectView:ClearDelay()
	if self.delay then
		GlobalTimerQuest:CancelQuest(self.delay)
		self.delay = nil
	end
end

function FlowerEffectView:ShowIndexCallBack()
	self:Flush()
end

function FlowerEffectView:OnFlush()
	local delay_time
	for i,v in ipairs(self.effect_tab) do
		local bundle, asset, time = v.bundle, v.asset, v.time
		delay_time = time
		EffectManager.Instance:PlayAtTransform(bundle, asset, self.root_node_transform, time)
	end
	self.effect_tab = {}
	if delay_time then
		self:ClearDelay()
		self.delay = GlobalTimerQuest:AddDelayTimer(function()
	    	ViewManager.Instance:Close(GuideModuleName.FlowerEffectView)
		end, delay_time)
	end
end

function FlowerEffectView:PlayEffect(_bundle, _asset, _time)
	if not MainuiWGCtrl.Instance:IsLoadMainUiView() then
		return
	end
	table.insert(self.effect_tab, {bundle = _bundle, asset = _asset, time = _time})
	if self:IsOpen() then
		self:Flush()
	else
		ViewManager.Instance:Open(GuideModuleName.FlowerEffectView)
	end
end

