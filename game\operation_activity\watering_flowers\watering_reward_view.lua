WateringRewardView = WateringRewardView or BaseClass(SafeBaseView)

function WateringRewardView:__init(view_name)
	self.view_name = "WateringRewardView"
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/operation_watering_flowers_prefab", "layout_reward_show")
	self:SetMaskBg(true, true)
end

function WateringRewardView:LoadCallBack()
	self:InitParam()
	self:InitPanel()
	self:InitListener()
end

function WateringRewardView:ReleaseCallBack()
	if self.item_cell_list then
		self.item_cell_list:DeleteMe()
		self.item_cell_list = nil
	end
	self.call_back = nil
end

function WateringRewardView:InitParam()
	self.data_list = {}
	self.call_back = nil
	self.status = nil
end

function WateringRewardView:InitPanel()
	self.item_cell_list = AsyncListView.New(ItemCell, self.node_list.item_root)
end

function WateringRewardView:InitListener()
	XUI.AddClickEventListener(self.node_list.get_btn, BindTool.Bind(self.OnClickGetBtn, self))
end

function WateringRewardView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "gift_info" then
			self.data_list = v.data_list
			self.call_back = v.call_back
			self.status = v.status
		end
	end
	self:RefreshView()
end

function WateringRewardView:RefreshView()
	self.item_cell_list:SetDataList(self.data_list)
	
	XUI.SetButtonEnabled(self.node_list.get_btn, self.status == FLOWER_STATUS.FLOWER_STATUS_RIPE)

	self.node_list.get_btn:SetActive(self.status ~= FLOWER_STATUS.FLOWER_STATUS_FETCH)
	self.node_list.get_reward_img:SetActive(self.status == FLOWER_STATUS.FLOWER_STATUS_FETCH)
end

function WateringRewardView:OnClickGetBtn()
	if self.call_back then
		self.call_back()
	end
	self:Close()
end