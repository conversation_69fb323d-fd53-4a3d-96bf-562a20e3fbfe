require("game/flower/flower_view")
require("game/flower/flower_send_view")
require("game/flower/flower_upgrade_view")
require("game/flower/flower_back_view")
require("game/flower/flower_effect_view")
require("game/flower/flower_attr_view")
FlowerWGCtrl = FlowerWGCtrl or BaseClass(BaseWGCtrl)

function FlowerWGCtrl:__init()
	if FlowerWGCtrl.Instance then
		ErrorLog("[FlowerWGCtrl] attempt to create singleton twice!")
		return
	end
	FlowerWGCtrl.Instance = self
	self.send_flower_view = FlowerView.New(GuideModuleName.Flower)
	self.flower_back_view = FlowerBackView.New()
	self.flower_effect_view = FlowerEffectView.New(GuideModuleName.FlowerEffectView)
	self.flower_attr_view = FlowerAttrView.New()

	self.data = {}

	self.is_play_flower_effect = false
	self.is_play_flower_timer = nil
	self:RegisterAllProtocols()

	self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"all_charm"})
end

function FlowerWGCtrl:__delete()
	if nil ~= self.send_flower_view then
		self.send_flower_view:DeleteMe()
		self.send_flower_view = nil
	end
	if nil ~= self.flower_back_view then
		self.flower_back_view:DeleteMe()
		self.flower_back_view = nil
	end

	if nil ~= self.flower_effect_view then
		self.flower_effect_view:DeleteMe()
		self.flower_effect_view = nil
	end

	if nil ~= self.flower_attr_view then
		self.flower_attr_view:DeleteMe()
		self.flower_attr_view = nil
	end

	self:RemoveFlowerTimer()

	if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end

	FlowerWGCtrl.Instance = nil
end


function FlowerWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSGiveFlower)
	self:RegisterProtocol(SCGiveFlower, "OnGiveFlowerHandler")

end

function FlowerWGCtrl:Open(param_t)
	if not self.send_flower_view:IsOpen() then
		self.send_flower_view:Open()
	end

	--self.send_flower_view:SetAccepter(data)

	if param_t ~= nil and param_t.item_id then
		self.send_flower_view:Flush(0, "select_by_id", {id = param_t.item_id})
	end
end

function FlowerWGCtrl:OpenSendFlowerView(role_id, role_name,role_sex,role_prof)
	if role_id > 0 then
		local data = self:GetMsgData(role_id, role_name,role_sex,role_prof)
		self.send_flower_view:SetAccepter(data)
	end
	
	if not self.send_flower_view:IsOpen() then
		self.send_flower_view:Open(TabIndex.flower_send)
	else
		self.send_flower_view:Flush()
	end
end

function FlowerWGCtrl:SendFlower()
	-- body
end

-- SettingWGData.Instance:GetSettingData(SETTING_TYPE.FLOWER_EFFECT) --是否屏蔽鲜花特效

function FlowerWGCtrl:OnGiveFlowerHandler(protocol)
	self.data.from_name = protocol.from_name
	self.data.from_uid = protocol.from_uid
	self.data.target_name = protocol.target_name
	self.data.target_id = protocol.target_uid
	self.data.item_id	= protocol.item_id
	self.data.flower_num = protocol.flower_num
    self.data.sex = protocol.sex
    self.data.prof = protocol.prof
	self.data.avatar_key_big = protocol.avatar_key_big
	self.data.avatar_key_small = protocol.avatar_key_small
	AvatarManager.Instance:SetAvatarKey(self.data.from_uid, self.data.avatar_key_big, self.data.avatar_key_small)
	if self.data.target_id == RoleWGData.Instance.role_vo.role_id then
		self.flower_back_view:Onopen()
		--请求信息刷新鲜花榜
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		CrossFlowerRankWGCtrl.Instance:SendCrossFlowerReq(CROSS_PERSON_RANK_TYPE.CROSS_PERSON_RANK_TYPE_WEEK_ADD_CHARM, role_vo.sex)
	end

	local is_all_flower_effect = false
	local duration = 7
	local asset_name = nil

	if self.data.item_id == COMMON_CONSTS.FLOWER_START_ID or self.data.item_id == COMMON_CONSTS.FLOWER_SP_START_ID then --1  目标 纯色
		asset_name = Ui_Effect.UI_songhuabian_homeigu

	elseif self.data.item_id == COMMON_CONSTS.FLOWER_START_ID + 1 or self.data.item_id == COMMON_CONSTS.FLOWER_SP_START_ID + 1 then --9  目标 纯色
		asset_name = Ui_Effect.UI_songhuabian_homeigu

	elseif self.data.item_id == COMMON_CONSTS.FLOWER_START_ID + 2 or self.data.item_id == COMMON_CONSTS.FLOWER_SP_START_ID + 2 then --99 全服 纯色
		asset_name = Ui_Effect.UI_songhuaxinxing_hong

	elseif self.data.item_id == COMMON_CONSTS.FLOWER_START_ID + 3 or self.data.item_id == COMMON_CONSTS.FLOWER_SP_START_ID + 3 then --999 全服 双色
		is_all_flower_effect = true
		asset_name = Ui_Effect.UI_songhua999
	end

	if self.is_play_flower_effect then
		return
	end

	self.is_play_flower_effect = true
	self:RemoveFlowerTimer()
	self.is_play_flower_timer = GlobalTimerQuest:AddDelayTimer(function ()
		self:RemoveFlowerTimer()
		self.is_play_flower_effect = false
	end, 6.1)

	if asset_name and not SettingWGData.Instance:GetSettingData(SETTING_TYPE.FLOWER_EFFECT) then
		local bundle, asset = ResPath.GetEffectUi(asset_name)
			self:PlayerEffect(bundle, asset, duration)
		-- if is_all_flower_effect then
		-- 	bundle, asset = ResPath.GetEffectUi(Ui_Effect.UI_songhuaxinxing_hong)
		-- 	self:PlayerEffect(bundle, asset, duration)
		-- 	bundle, asset = ResPath.GetEffectUi(Ui_Effect.UI_songhuabian_homeigu)
		-- 	self:PlayerEffect(bundle, asset, duration)
		-- end
	end
end

function FlowerWGCtrl:RemoveFlowerTimer()
	if self.is_play_flower_timer then
		GlobalTimerQuest:CancelQuest(self.is_play_flower_timer)
		self.is_play_flower_timer = nil
	end
end

function FlowerWGCtrl:GetFlowerData()
	return self.data
end

function FlowerWGCtrl:PlayerEffect(bundle, asset, time)
	if self.flower_effect_view then
		self.flower_effect_view:PlayEffect(bundle, asset, time)
	end
end

-- 送花
function FlowerWGCtrl:SendGiveFlower(grid_index, item_id, target_uid, flower_num, is_marry, shop_seq)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGiveFlower)
	protocol.grid_index = grid_index
	protocol.item_id = item_id
	protocol.target_uid = target_uid
	protocol.is_anonymity = 0
	protocol.is_marry = is_marry or 0 --0 == 正常送花    1 == 巡游送花
	protocol.flower_num = flower_num or 0
	protocol.shop_seq = shop_seq or 0
	protocol:EncodeAndSend()
end

function FlowerWGCtrl:GetMsgData(role_id, gamename,role_sex,role_prof)
	local user_msg = {}
	user_msg.role_id = role_id
	user_msg.gamename = gamename
    user_msg.role_sex = role_sex or 1
    user_msg.role_prof = role_prof
	return user_msg
end

-- 角色属性改变
function FlowerWGCtrl:OnRoleAttrChange(attr_name, value, old_value)
	if attr_name == "all_charm" then
		if self.send_flower_view:IsOpen() then
			self.send_flower_view:Flush()
		end
	end
end

function FlowerWGCtrl:OpenTipsAttrView(tips_data)
	self.flower_attr_view:SetData(tips_data)
end

--[[
function FlowerWGCtrl:GetMyFriendList()
	local friend_list = SocietyWGData.Instance:GetFriendList()
	local temp_list = {}
	for k,v in pairs(friend_list) do
		-- if v.is_online == 1 or v.is_online == 3 then
		if v.is_online == ONLINE_TYPE.ONLINE_TYPE_ONLINE or v.is_online == ONLINE_TYPE.ONLINE_TYPE_CROSS then
			if RoleWGData.Instance.role_vo.lover_uid == v.user_id then
				v.is_love_id = 1
			else
				v.is_love_id = 0
			end
			table.insert(temp_list,v)
		end
	end
	table.sort(temp_list, SortTools.KeyUpperSorters("is_love_id", "intimacy", "level"))
	return temp_list
end
]]