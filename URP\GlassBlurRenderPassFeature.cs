using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

public class GlassBlurRenderPassFeature : ScriptableRendererFeature
{
    #region singleton
    private static GlassBlurRenderPassFeature m_Instance;
    public static GlassBlurRenderPassFeature Instance
    {
        get
        {
            return m_Instance;
        }
    }

    private GlassBlurRenderPassFeature() { }
    #endregion

    public Camera shotCamera = null;
    public Shader shader = null;
    public RenderPassEvent renderEvent = RenderPassEvent.BeforeRenderingPostProcessing;

    private Material blurredMaterial;


    private static readonly string k_GlassBlurTextureName = "_UIGlassBlurPassTexture";
    private GlassBlurRenderPass m_GlassBlurRenderPass;
    private RenderTargetHandle m_GlassBlurPassTexture;

    public override void Create()
    {
        m_Instance = this;
        m_GlassBlurPassTexture.Init(k_GlassBlurTextureName);
        m_GlassBlurRenderPass = new GlassBlurRenderPass();
        m_GlassBlurRenderPass.renderPassEvent = renderEvent;
    }

    public override void AddRenderPasses(ScriptableRenderer renderer, ref RenderingData renderingData)
    {
        if (shader == null)
        {
            Debug.LogError("Shader not set in GlassBlurRenderPassFeature. Please assign a shader.");
            return;
        }

        if (shotCamera != null && renderingData.cameraData.camera == shotCamera)
        {
            if (blurredMaterial == null)
                blurredMaterial = CoreUtils.CreateEngineMaterial(shader);

            m_GlassBlurRenderPass.Setup(renderer.cameraColorTarget, m_GlassBlurPassTexture, blurredMaterial);
            renderer.EnqueuePass(m_GlassBlurRenderPass);
        }
    }
}







public class GlassBlurRenderPass : ScriptableRenderPass
{
    private const string m_ProfileTag = "UI Glass Blur";
    private RenderTargetIdentifier m_Source;
    private RenderTargetHandle m_Destination;
    private Material m_blurMat;
    private int m_UIGlassBlurPassCacheRT = Shader.PropertyToID("_UIGlassBlurPassCacheRT");
    private int m_UIGlassBlurPassHorizontalRT = Shader.PropertyToID("_UIGlassBlurPassHorizontalRT");
    private int m_UIGlassBlurPassVerticalRT = Shader.PropertyToID("_UIGlassBlurPassVerticalRT");

    public void Setup(RenderTargetIdentifier source, RenderTargetHandle destination, Material material)
    {
        m_Source = source;
        m_Destination = destination;
        m_blurMat = material;
    }

    public GlassBlurRenderPass()
    {
    }

    public override void Configure(CommandBuffer cmd, RenderTextureDescriptor cameraTextureDescriptor)
    {
        RenderTextureDescriptor descriptor = cameraTextureDescriptor;
        //descriptor.depthBufferBits = 0;

        cmd.GetTemporaryRT(m_UIGlassBlurPassCacheRT, descriptor, FilterMode.Bilinear);
        cmd.GetTemporaryRT(m_UIGlassBlurPassHorizontalRT, descriptor, FilterMode.Bilinear);
        cmd.GetTemporaryRT(m_UIGlassBlurPassVerticalRT, descriptor, FilterMode.Bilinear);
    }

    public override void Execute(ScriptableRenderContext context, ref RenderingData renderingData)
    {
        CommandBuffer cmd = CommandBufferPool.Get(m_ProfileTag);

        float widthOverHeight = (1.0f * renderingData.cameraData.camera.pixelWidth) / (1.0f * renderingData.cameraData.camera.pixelHeight);
        float oneOverBaseSize = 1.0f / 512.0f;

        cmd.Blit(m_Source, m_UIGlassBlurPassVerticalRT);
        Vector4 offset = new Vector4(0, 3f * oneOverBaseSize, 0, 0);
        cmd.SetGlobalVector("_Offsets", offset);
        cmd.Blit(m_UIGlassBlurPassVerticalRT, m_UIGlassBlurPassHorizontalRT, m_blurMat);
        offset = new Vector4((3f / widthOverHeight) * oneOverBaseSize, 0.0f, 0.0f, 0.0f);
        cmd.SetGlobalVector("_Offsets", offset);
        cmd.Blit(m_UIGlassBlurPassHorizontalRT, m_UIGlassBlurPassCacheRT, m_blurMat);

        context.ExecuteCommandBuffer(cmd);
        cmd.Clear();
        CommandBufferPool.Release(cmd);
    }

    public override void FrameCleanup(CommandBuffer cmd)
    {
        cmd.ReleaseTemporaryRT(m_UIGlassBlurPassCacheRT);
        cmd.ReleaseTemporaryRT(m_UIGlassBlurPassHorizontalRT);
        cmd.ReleaseTemporaryRT(m_UIGlassBlurPassVerticalRT);
    }
}