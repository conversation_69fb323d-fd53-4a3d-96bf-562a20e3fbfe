TianShenBaoXiaLevelUpView = TianShenBaoXiaLevelUpView or BaseClass(SafeBaseView)

function TianShenBaoXiaLevelUpView:__init()
	self:SetMaskBg(true, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

	local bundle_name = "uis/view/tianshen_prefab"
	self:AddViewResource(0, bundle_name, "layout_ts_baoxia_up_level")

	self.view_name = "TianShenBaoXiaLevelUpView"
end

function TianShenBaoXiaLevelUpView:__delete()

end

function TianShenBaoXiaLevelUpView:ReleaseCallBack()
	self:ClearModelTimer()

	if self.baoxia_model then
		self.baoxia_model:DeleteMe()
		self.baoxia_model = nil
	end

	self.tween_mask_bg = nil
	self.tween_go_root = nil
	self.tween_bg_yuan = nil
end

function TianShenBaoXiaLevelUpView:LoadCallBack()
	self.node_list["btn_goto_use"].button:AddClickListener(BindTool.Bind(self.Close, self))
end

function TianShenBaoXiaLevelUpView:ResetAllAnimParam()
	self.tween_mask_bg = UITween.CanvasGroup(self.node_list["tween_mask_bg"].gameObject)
	self.tween_mask_bg.alpha = 0

	self.tween_bg_yuan = UITween.CanvasGroup(self.node_list["tween_bg_yuan"].gameObject)
	self.tween_bg_yuan.alpha = 0

	self.tween_go_root = UITween.CanvasGroup(self.node_list["tween_go_root"].gameObject)
	self.tween_go_root.alpha = 0

	self.node_list["model_pos"].transform.localPosition = u3dpool.vec3(0, 0, 0) --endX -240
	self.node_list["model_pos"]:SetActive(false)
	self.node_list["tween_move_bg_fang"].transform.localPosition = u3dpool.vec3(-810, 39, 0) --endX -45
end

function TianShenBaoXiaLevelUpView:OnFlush(param_t)
	self:ResetAllAnimParam()

	local cur_cfg = TianShenWGData.Instance:GetGodHoodDrawGradeCfg()
	self.node_list["text_bx_name"].text.text = string.format(Language.TianShenBaoXia.Up_Level_Des, cur_cfg.name)
	self.node_list["text_box_title"].text.text = cur_cfg.name

	--遮罩出现
	if not self.tween_mask_bg then return end
	local tween = self.tween_mask_bg:DoAlpha(0, 1, 0.5)
	tween:SetEase(DG.Tweening.Ease.InOutSine)
	tween:OnComplete(function()
		self:LoadBoxModel(cur_cfg)
	end)
end

function TianShenBaoXiaLevelUpView:LoadBoxModel(cur_cfg)
	--模型加载
	if not self.baoxia_model then
		self.baoxia_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["model_pos"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.S,
			can_drag = false,
		}
		
		self.baoxia_model:SetRenderTexUI3DModel(display_data)
		-- self.baoxia_model:SetUI3DModel(self.node_list["model_pos"].transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE)
	end

	local model_res_name = "3_2_zi"--模型加载名字配置
	
	local show_reward_cfg = TianShenWGData.Instance:GetGodHoodDrawShowRewardCfgByGrade(cur_cfg.grade)
	if type(show_reward_cfg.show_box_modelid) == "string" then
		model_res_name = show_reward_cfg.show_box_modelid
	end

	local m_bundle, m_asset = ResPath.GetOtherUIModelByName(model_res_name)
	self.baoxia_model:SetMainAsset(m_bundle, m_asset)

	--模型移动动画
	self.node_list["model_pos"]:SetActive(true)
	self:ClearModelTimer()
	self.model_timer_quest = GlobalTimerQuest:AddDelayTimer(function()
		local tween = self.node_list["model_pos"].rect:DOAnchorPosX(-320, 1)
		tween:SetEase(DG.Tweening.Ease.InOutSine)
		tween:OnComplete(function()
			self:PlayGoRootShowTween()
		end)
	end, 1)
end

function TianShenBaoXiaLevelUpView:PlayGoRootShowTween()
	local tween = self.tween_bg_yuan:DoAlpha(0, 1, 0.5)
	tween:SetEase(DG.Tweening.Ease.InOutSine)
	tween:OnComplete(function()
		local tween = self.tween_go_root:DoAlpha(0, 1, 2)
		tween:SetEase(DG.Tweening.Ease.InOutSine)
		self.node_list["tween_move_bg_fang"].rect:DOAnchorPosX(-45, 0.5)
	end)
end

function TianShenBaoXiaLevelUpView:ClearModelTimer()
	if self.model_timer_quest then
		GlobalTimerQuest:CancelQuest(self.model_timer_quest)
		self.model_timer_quest = nil
	end
end

