--天仙阁
FuBenPanelView.ENEMY_COUNT = 3
function FuBenPanelView:Initwelkin()
	self.old_role_index  = -2
	self:CreateWelkinList()
	FuBenPanelWGData.Instance:SetIsShowTianXianGePoint(true)
	RemindManager.Instance:Fire(RemindName.TianXianFuBen)
end
function FuBenPanelView:Deletewelkin()
	self.is_has_onclick_enter = nil
	if self.pt_reward then
		self.pt_reward:DeleteMe()
		self.pt_reward = nil
	end
	if self.pt_reward_1 then
		self.pt_reward_1:DeleteMe()
		self.pt_reward_1 = nil
	end
	if self.pt_reward_2 then
		self.pt_reward_2:DeleteMe()
		self.pt_reward_2 = nil
	end

	if nil ~= self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end
	if nil ~= self.tower_list then
		self.tower_list:DeleteMe()
		self.tower_list = nil
	end
end

function FuBenPanelView:CreateWelkinList()


	self:CreateRank()



	self:ShowDisPlay()
	local reward_item_1 = self.node_list["ph_welkin_pos_1"]
	local reward_item_2 = self.node_list["ph_welkin_pos_2"]
	self.pt_reward_1 = ItemCell.New(reward_item_1)
	self.pt_reward_2 = ItemCell.New(reward_item_2)
	--符文cell
	local ph = self.node_list["ph_pt_reward1"]
	
	--local fuwen_cfg = FuBenPanelWGData.Instance:GetConfigByStep()
	local pass_level = FuBenPanelWGData.Instance:GetPassLevel()
	local max_level = #ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").reward_show
	--进入按钮
	self.node_list["btn_change"].button:AddClickListener(BindTool.Bind(self.OnClickTerraceCallBack, self, pass_level, true))
	-- if fuwen_cfg then
		
		
		
	-- else
	-- 	local pass_level = FuBenPanelWGData.Instance:GetPassLevel()
	-- 	self.node_list["lbl_Tips1"].text.text = string.format(Language.FuBenPanel.TXGTongGuan, pass_level)
	-- end


end

-- 创建右侧排行榜
function FuBenPanelView:CreateRank()
	local ph_qiyi = self.node_list["ph_rank_list"]
	self.rank_list = AsyncListView.New(FuWenRankItemRender, ph_qiyi)
	local tower_list = self.node_list["ph_tower_list"]
	self.tower_list = AsyncListView.New(FuWenTowerItemRender, tower_list)

end

function FuBenPanelView:CheckReachCapability()
	local pass_level = FuBenPanelWGData.Instance:GetPassLevel()
	local terrace_list = FuBenPanelWGData.Instance:GetTerraceListCfgByLevel(pass_level) or {}
	local capability = 0
	if pass_level == 100 then
		return false
	end
	for i,v in ipairs(terrace_list) do
		if v.level == pass_level + 1 then
			capability = v.capability
			break
		end
	end
	return RoleWGData.Instance.role_vo.capability >= capability
end

--点击符文详情
function FuBenPanelView:OnClick()
	local fuwen_cfg = FuBenPanelWGData.Instance:GetConfigByStep()
	TipWGCtrl.Instance:OpenItem({item_id = fuwen_cfg.rune_id})
end



function FuBenPanelView:OnTwoClick()
	local jiesuo_config = FuBenPanelWGData.Instance:GetConfigFuWen()
	TipWGCtrl.Instance:OpenItem({item_id = jiesuo_config[2].rune_id})
end


function FuBenPanelView:OnFlushWelkinPanel()
--print_error("刷新啦啦啦啦啦11111111111111")
	--if self.terrace_list  then
		self:OnFlushWelkin()
	--end
end

function FuBenPanelView:OnFlushWelkin()
	if nil == self.rank_list then return end
	local rank_list = FuBenPanelWGData.Instance:GetTianXianGeRankList()
	self.rank_list:SetDataList(rank_list)
	local role_vo =  GameVoManager.Instance:GetMainRoleVo()
	local is_max = FuBenPanelWGData.Instance:IsReachMaxLevel()

	for i,v in pairs(rank_list) do
		if v.user_id == role_vo.role_id then
			local str = string.format(Language.FuWen.MyRank_1,i)
			self.node_list["rich_my_rank"].text.text = str

			break
		else
			--local str = string.format(Language.FuWen.MyRank1_1,"未上榜")
			self.node_list["rich_my_rank"].text.text = Language.FuWen.MyRank1_1
		end
	end

	local pass_level = FuBenPanelWGData.Instance:GetPassLevel()
	local max_level = #ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").reward_show
	if pass_level == max_level then pass_level = max_level - 1 end
	if pass_level <= 0 then
		pass_level = 0
	end 
 	local reward_show_list =  FuBenPanelWGData.Instance:GetTerraceRewardByLevel(pass_level+1)
 	self.pt_reward_1:SetData({item_id = reward_show_list.actiion_reward_item[0].item_id})
	self.pt_reward_2:SetData({item_id = reward_show_list.actiion_reward_item[1].item_id,num = reward_show_list.actiion_reward_item[1].num })
	local tower_info =  FuBenPanelWGData.Instance:GetCfgLevelInfo()
	----------------------
	local temp_tower_level = pass_level + 3
	local tower_data_list = {}

	for i = temp_tower_level, 1,-1 do
		table.insert(tower_data_list,tower_info[i])
	end
	
	self.tower_list:SetDataList(tower_data_list,0)


end

function FuBenPanelView:ShowDisPlay()
	
end


--点击进入下一关
function FuBenPanelView:OnClickTerraceCallBack(index, is_click_btn)
	local max_level = #ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").reward_show
	if index >= max_level then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FbWushuang.MaxLevelTips) --layout_sweep_msg(Clone) layout_copper_saodang(Clone)
		return
	end
	if self.is_has_onclick_enter then return  end
	self.is_has_onclick_enter = true
	
	FuBenWGData.Instance:SetTXGEnter(true)
	FuBenWGCtrl.Instance:SendEnterWelkinFb()
	--self:Close()

	self.cur_role_index = index
		
end

--------------------------------------------------------------------
--排行榜
FuWenRankItemRender = FuWenRankItemRender or BaseClass(BaseRender)


function FuWenRankItemRender:__init()

end

function FuWenRankItemRender:__delete()
	if nil ~= self.lbl_vip then
		self.lbl_vip:DeleteMe()
		self.lbl_vip = nil
	end
end

function FuWenRankItemRender:OnFlush()
	local one,two,three = 1,2,3 --Ç°ÈýÃû

	if self.data == nil then return end
	local num = self.index % 2
	self.node_list.item_bg:SetActive(num == 0)
	self.node_list["lbl_name"].text.text = self.data.user_name
	self.node_list["lbl_name_1"].text.text = self.data.user_name
	-- self.node_list.normal_obj:SetActive(self.index > 3)
	self.node_list.normal_obj:SetActive(true)
	self.node_list.spcial_obh:SetActive(false)
	-- self.node_list.spcial_obh:SetActive(self.index <= 3)
	self.node_list["lbl_rank_value"].text.text = string.format(Language.FbWushuang.RankValue,self.data.rank_value)
	self.node_list["lbl_rank_value_1"].text.text = string.format(Language.FbWushuang.RankValue,self.data.rank_value)
	
	self.node_list["lbl_paiming"]:SetActive(self.data.vip_level > 0)
	self.node_list["lbl_paiming"].text.text = self.data.vip_level
	self.node_list["img_vip"]:SetActive(self.data.vip_level > 0)

	self.node_list["lbl_zhanli"].text.text = self.data.flexible_ll
	self.node_list["lbl_zhanli_1"].text.text = self.data.flexible_ll

	
	if self.index == one or self.index == two or self.index == three then
		

		self.node_list["img_paiming"].image:LoadSprite(ResPath.GetRankImage("rank_num_"..self.index))
		self.node_list["img_paiming"]:SetActive(true)
		
	else
		self.node_list["img_paiming"]:SetActive(false)
		
		self.node_list["rank_level_num"].text.text = self.index
	end

end

function FuWenRankItemRender:CreateSelectEffect()
end

-------------------------------------------
--爬塔
-------------------------------------------
FuWenTowerItemRender = FuWenTowerItemRender or BaseClass(BaseRender)

function FuWenTowerItemRender:__init()
 	self.data_fuwen = nil
	XUI.AddClickEventListener(self.node_list["reward_item_bg"], BindTool.Bind1(self.CliskBulletHandler, self))
end

function FuWenTowerItemRender:__delete()
	self.data_fuwen = nil
end
function FuWenTowerItemRender:CliskBulletHandler()
	--self.data_fuwen = 22220
	-- print_error("item_id",self.data_fuwen)
	TipWGCtrl.Instance:OpenItem({item_id = self.data_fuwen }, self.item_tip_from or ItemTip.FROM_NORMAL, nil)
end
function FuWenTowerItemRender:OnFlush()
--print_error(self.data)
	local pass_level = FuBenPanelWGData.Instance:GetPassLevel()
	--local pass_level = 3
	local fuwen_cfg_info = FuBenPanelWGData.Instance:GetCfgFuWenInfo()
	self.node_list.tower_level.text.text = string.format(Language.FbWushuang.RankValue,self.data.level)
	local fuwen_id = nil
	for k,v in pairs(fuwen_cfg_info) do
		--print_error(k,v,v.layer,v.rune_id )
		if self.data.level == v.layer then
			fuwen_id = v.rune_id
		end
	end
	if fuwen_id then
		local bundle,asset =  ResPath.GetItem(fuwen_id)
		self.node_list.fuwen_icon.image:LoadSprite(bundle,asset)
		self.data_fuwen = fuwen_id
		self.node_list.reward_item_bg:SetActive(true)
		local item_fuwen_cfg = ItemWGData.Instance:GetItemConfig(fuwen_id)
		--print_error("FuWen",mmmm )
		self.node_list.fuwen_des.text.text = string.format(Language.FuBenPanel.TowerFuWnDs, item_fuwen_cfg.name)
		--break
	else
		self.node_list.reward_item_bg:SetActive(false)
	end


	local is_kaqi_cao,info_fuwencao = FuBenPanelWGData.Instance:GetFuWenCao(self.data.level)
	--print_error(self.data.level,is_kaqi_cao,info_fuwencao)
	if is_kaqi_cao and info_fuwencao then
		-- local bundle,asset =  ResPath.GetItem(info_fuwencao)
		-- self.node_list.fuwen_icon1.image:LoadSprite(bundle,asset)
		self.node_list.reward_item_bg1:SetActive(true)
	else
		self.node_list.reward_item_bg1:SetActive(false)
	end



self.node_list.cur_change_image:SetActive(false)
	if self.data.level == (pass_level + 1) then
		self.node_list.tianxiange_lock:SetActive(false)
		self.node_list.soutong_image:SetActive(false)
		--self.node_list.cur_change_image:SetActive(true)
		self.node_list.item_select_bg:SetActive(true)
		self.node_list.yitongguan_tianxiange:SetActive(false)
	elseif self.data.level < (pass_level + 1) then
		self.node_list.yitongguan_tianxiange:SetActive(true)
		self.node_list.tianxiange_lock:SetActive(false)
		self.node_list.soutong_image:SetActive(false)
		--self.node_list.cur_change_image:SetActive(false)
		self.node_list.item_select_bg:SetActive(false)
	else
		self.node_list.tianxiange_lock:SetActive(true)
		self.node_list.soutong_image:SetActive(false)
		--self.node_list.cur_change_image:SetActive(false)
		self.node_list.item_select_bg:SetActive(false)
		self.node_list.yitongguan_tianxiange:SetActive(false)
	end
	self.node_list.UI_tower:SetActive(self.data.level == (pass_level + 1))
    --print_error(self.data)
end