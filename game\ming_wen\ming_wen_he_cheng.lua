MingWenView = MingWenView or BaseClass(SafeBaseView)

function MingWenView:InitHeCheng()
	self.jump_open = self.jump_open or 1
	self.cur_hecheng_id = -1

	if nil == self.target_mingwen_item then
		self.target_mingwen_item = MingWenItemRender.New(self.node_list.target_mingwen_item)
		--self.target_mingwen_item.view.button:AddClickListener(BindTool.Bind(self.MingWenClickTarget,self,3)) --合成的 目标铭文  即将合成的铭文	
	end

	if not self.hecheng_cost_item_list then
        self.hecheng_cost_item_list = {}
        for i = 1, 3 do
            self.hecheng_cost_item_list[i] = ItemCell.New(self.node_list["hecheng_item" .. i])
        end
    end

	self.node_list["hecheng_btn"].button:AddClickListener(BindTool.Bind(self.MingWenClickCompose,self))  --合成铭文

	local open_jump_type = 1
	self.open_jump_cell_index = -1
	local accordion_tab,father_num = MingWenWGData.Instance:GetComposeFatherList()
	for i = 1, father_num do
		local accor_data = MingWenWGData.Instance:GetComPoseCellList(i)
		local is_red = 0
		for t,q in pairs(accor_data) do
			is_red = MingWenWGData.Instance:GetMingWenItemCanHeCheng(q[1].get_id)
			if is_red == 1 then
				open_jump_type = i
				self.open_jump_cell_index = t
				break
			end
		end
	end

	self:CreateAccordion(open_jump_type)
	MingWenWGData.Instance:SetIsOpenHeCheng()
	RemindManager.Instance:Fire(RemindName.MingWen_HeCheng)

	self.node_list.hecheng_tips_text.text.text = Language.MingWenView.TipDesc2
	--self:InitLineEffect() --特效方法 以后要替换  先注释掉
end

function MingWenView:JumpComposeBigType(jump_type)
	self.jump_open = jump_type
end

function MingWenView:DeleteHeCheng()

end

function MingWenView:ReleaseHeCheng()
	if self.target_mingwen_item then
		self.target_mingwen_item:DeleteMe()
		self.target_mingwen_item = nil
	end

	if self.hecheng_cell_list then
		for k,v in pairs(self.hecheng_cell_list) do
			for t,q in pairs(v) do
				q:DeleteMe()
			end
		end
		self.hecheng_cell_list = nil
	end

	if self.hecheng_cost_item_list then
        for k,v in pairs(self.hecheng_cost_item_list) do
            v:DeleteMe()
        end
		self.hecheng_cost_item_list = nil
	end
	self.hecheng_accordion_list = nil
	self.line_obj = nil
end

--连线特效
function MingWenView:InitLineEffect()
	if not self.line_obj then
		self.line_obj = {}
		local effect_root = self.node_list.hecheng_line_effect
		local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_hechengmianban_3_F2)
		local async_loader = AllocAsyncLoader(self, "compose_daoju_effect")
		async_loader:SetParent(effect_root.transform)
		async_loader:SetIsUseObjPool(true)
		async_loader:Load(bundle_name, asset_name,
		function (obj)
			local line = obj.transform:Find("line_6")
			self.line_obj[1] = line and line.gameObject

			local line2 = obj.transform:Find("line_5")
			self.line_obj[2] = line2 and line2.gameObject

			local line3 = obj.transform:Find("line_2")
			self.line_obj[3] = line3 and line3.gameObject

			local line4 = obj.transform:Find("line_3")
			self.line_obj[4] = line4 and line4.gameObject
			self:FlushHeChengRight()
		end)
	end
end

function MingWenView:CreateAccordion(index)
	if self.hecheng_accordion_list then return end
	local accordion_tab,father_num = MingWenWGData.Instance:GetComposeFatherList()
	self.hecheng_accordion_list = {}

	for i=1,10 do
		self.node_list["SelectBtn"..i]:SetActive(i <= father_num)
	end

	for i=1, father_num do
		self.hecheng_accordion_list[i] = {}
		self.hecheng_accordion_list[i].text_name = self.node_list["text_btn_"..i]
		self.hecheng_accordion_list[i].text_high_name = self.node_list["text_btn_hl_"..i]
		self.hecheng_accordion_list[i].select_btn = self.node_list["SelectBtn"..i]
		self.hecheng_accordion_list[i].select_btn.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickExpandHandler,self,accordion_tab[i].compose_type))
		self.hecheng_accordion_list[i].list = self.node_list["List"..i]
		self.hecheng_accordion_list[i].red_dot = self.node_list["eq_remind"..i]
		self.hecheng_accordion_list[i].list_item_cell = {}
		--local accor_data = nil
		--if nil ~= accordion_tab[i] and nil ~= accordion_tab[i] then
		self.hecheng_accordion_list[i].text_name.text.text = accordion_tab[i].compose_name
		self.hecheng_accordion_list[i].text_high_name.text.text = accordion_tab[i].compose_name
		-- 	accor_data = accordion_tab[i].child
		--end
		--self:LoadHeChengCell(i)
	end
	self.togglegroup = self.node_list["hecheng_cell_toggle_group"]
	local open_jump_index = index and index or accordion_tab[1].compose_type
	self:OnClickExpandHandler(open_jump_index,true)
end

function MingWenView:OnClickExpandHandler(index,is_on)
	if not is_on then return end
	self.cur_btn_list_num = index
	if nil == self.hecheng_accordion_list then
	 	self:CreateAccordion(index)
	 	return
	end

	if nil == self.hecheng_cell_list or nil == self.hecheng_cell_list[index] then
		self:LoadHeChengCell(index)
	else
		if self.open_jump_cell_index and self.open_jump_cell_index ~= -1 then
			if self.hecheng_accordion_list[index] and self.hecheng_accordion_list[index].list_item_cell[self.open_jump_cell_index] then
				self.hecheng_accordion_list[index].list_item_cell[self.open_jump_cell_index]:GetComponent("Toggle").isOn = true
				self.open_jump_cell_index = -1
			end
		else
			if self.hecheng_accordion_list[index] and self.hecheng_accordion_list[index].list_item_cell then
				self.hecheng_accordion_list[index].list_item_cell[1]:GetComponent("Toggle").isOn = true
				self:OnClickComPoseCell(self.hecheng_cell_list[index][1].data)
			end
		end
	end
end


function MingWenView:LoadHeChengCell(index)
	local res_async_loader = AllocResAsyncLoader(self, "posy_accordion_item" .. index)
	res_async_loader:Load("uis/view/ming_wen_ui_prefab", "posy_accordion_item", nil,
		function(new_obj)
			local item_vo = {}
			local accor_data = MingWenWGData.Instance:GetComPoseCellList(index)
			local get_id = -1
			local num = 1
			for k,v in pairs(accor_data) do
				local obj = ResMgr:Instantiate(new_obj)
				local obj_transform = obj.transform
				obj_transform:SetParent(self.hecheng_accordion_list[index].list.transform, false)
				obj:GetComponent("Toggle").group = self.togglegroup.toggle_group
				self.hecheng_accordion_list[index].list_item_cell[num] = obj
				num = num + 1
				local item_render = HeChengListItemRender.New(obj)
				item_render:AddClickEventListener(BindTool.Bind(self.OnClickComPoseCell,self,accor_data[k]),true)
				item_render:SetData(accor_data[k]) --里层按钮赋值信息
				--item_render.parent_view = self
				item_vo[k] = item_render
				-- if get_id == -1 then
				-- 	get_id = k
				-- end
			end
			if nil == self.hecheng_cell_list then
				self.hecheng_cell_list = {}
			end
			self.hecheng_cell_list[index] = item_vo
			if self.hecheng_accordion_list[index].select_btn.accordion_element.isOn then
				self:OnClickExpandHandler(index,true)
			else
				self.hecheng_accordion_list[index].select_btn.accordion_element.isOn = true
			end
		end)


end

function MingWenView:OnClickComPoseCell(cell_data,is_On)
	if self.cur_hecheng_id == cell_data[1].get_id then
		return
	end

	local is_limite,condition = MingWenWGData.Instance:GetPosyIsLimite(cell_data[1].get_id)
	self.cur_hecheng_id = cell_data[1].get_id
	self.cur_hecheng_data = cell_data[1]
	if is_limite then
		local str = string.format(Language.MingWenView.DuiHuanConditionTips,condition)
		TipWGCtrl.Instance:ShowSystemMsg(str)
	else
		self:FlushHeChengRight()
	end
end

function MingWenView:FlushHeCheng()
	self:FlushHeChengRight()
	self:FlushHeChengRed()
end

function MingWenView:FlushHeChengRed()
	for k,v in pairs(self.hecheng_accordion_list) do
		local accor_data = MingWenWGData.Instance:GetComPoseCellList(k)
		local is_red = 0
		v.red_dot:SetActive(false)
		for t,q in pairs(accor_data) do
			is_red = MingWenWGData.Instance:GetMingWenItemCanHeCheng(q[1].get_id)
			if is_red == 1 then
				v.red_dot:SetActive(true)
				break
			end
		end
	end
	if nil == self.hecheng_cell_list then
		self.hecheng_cell_list = {}
	end
	for k,v in pairs(self.hecheng_cell_list) do
		for t,q in pairs(v) do
			q:FlushHeChengRed()
		end
	end
end

function MingWenView:ClearHeChengRightPanel()
	self.node_list["hecheng_target_name"].text.text = ""
	self.node_list["hecheng_target_value"].text.text = ""
	self.node_list["hecheng_target_attr"].text.text = ""
	self.node_list["hecheng_name1"].text.text = ""
	self.node_list["hecheng_name2"].text.text = ""
	for i =1,3 do
		self.node_list["hecheng_item"..i]:SetActive(false)
		-- if self.node_list["hecheng_name_bg"..i] then
		-- 	self.node_list["hecheng_name_bg"..i]:SetActive(false)
		-- end
	end
	self.node_list["hecheng_lock3"]:SetActive(true)
	self.node_list["target_mingwen_item"]:SetActive(false)
end

function MingWenView:FlushHeChengRight()
	if nil == self.cur_hecheng_id then
		return
	end

	local base_cfg = MingWenWGData.Instance:GetMingWenBaseCfgByID(self.cur_hecheng_id)
	if not base_cfg then
		self:ClearHeChengRightPanel()
	else
		self.node_list["target_mingwen_item"]:SetActive(true)

		self.target_mingwen_item:SetData({item_id = self.cur_hecheng_id})

		self.is_enough = true
		if self.cur_hecheng_data.stuff_id_vec ~= 0 or self.cur_hecheng_data.xin_jing > 0 then
			self.node_list["hecheng_item3"]:SetActive(true)
			local data = {}
			local str = ""
			local cose_id3 = self.cur_hecheng_data.stuff_id_vec ~= 0 and self.cur_hecheng_data.stuff_id_vec or COMMON_CONSTS.VIRTUAL_ITEM_XINJING
			data.item_id = cose_id3
			local cfg = ItemWGData.Instance:GetItemConfig(cose_id3)
			if cfg then
				self.node_list["hecheng_name3"].text.text = ToColorStr(cfg.name, ITEM_COLOR[cfg.color])
			end

			self.hecheng_cost_item_list[3]:SetData(data)
			local have_num = 0
			if self.cur_hecheng_data.stuff_id_vec ~= 0 then
				have_num = ItemWGData.Instance:GetItemNumInBagById(self.cur_hecheng_data.stuff_id_vec)
				self.is_enough = have_num >= self.cur_hecheng_data.stuff_num_vec
				str = have_num.."/"..self.cur_hecheng_data.stuff_num_vec
			else

				local mingwen_exp_num, mingwen_xinjing_num = MingWenWGData.Instance:GetMingWenExpAndXinJing()
				have_num = mingwen_xinjing_num
				self.is_enough = have_num >= self.cur_hecheng_data.xin_jing
				str = have_num.."/"..self.cur_hecheng_data.xin_jing
			end

			if self.line_obj and self.line_obj[1] then
				self.line_obj[1]:SetActive(true)
			end
			local color = self.is_enough and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH

			self.node_list["hecheng_cost_num3"].text.text = ToColorStr(str, color)
			self.node_list["hecheng_lock3"]:SetActive(false)
			self.node_list["hecheng_cost_num_bg3"]:SetActive(true)
		else
			if self.line_obj and self.line_obj[1] then
				self.line_obj[1]:SetActive(false)
			end
			self.node_list["hecheng_item3"]:SetActive(false)
			self.node_list["hecheng_lock3"]:SetActive(true)
			self.node_list["hecheng_cost_num_bg3"]:SetActive(false)
			self.node_list["hecheng_name3"].text.text = ""
		end

		local posy_id_list = string.split(self.cur_hecheng_data.posy_id_vec,"|")
		local posy_num_list = string.split(self.cur_hecheng_data.posy_num_vec,"|")
		local need_double = false
		local posy_id_vec = {}
		local posy_num_vec = {}

		for k,v in pairs(posy_id_list) do
			posy_id_vec[k] = tonumber(v)
		end

		for t,q in pairs(posy_num_list) do
			posy_num_vec[t] = tonumber(q)
		end

		if posy_id_vec[2] then
			need_double = true
		end

		local equip_posy = {}
		local slot_data = MingWenWGData.Instance:GetEquipPosyData()

		local compose_cfg = MingWenWGData.Instance:GetFenJieReward(self.cur_hecheng_id)
		if compose_cfg.is_cost_inlay == 1 then
			for k, v in pairs(slot_data) do
				local solt_data = v
				if not IsEmptyTable(solt_data) then
					if posy_id_vec[1] == solt_data.posy.item_id then
						equip_posy[1] = solt_data.posy
						equip_posy[1].equip_index = k
					elseif posy_id_vec[2] and posy_id_vec[2] == solt_data.posy.item_id then
						equip_posy[2] = solt_data.posy
						equip_posy[2].equip_index = k
					end
				end
			end
		end

		if self.line_obj and self.line_obj[4] then
			self.line_obj[4]:SetActive(need_double)
		end

		for i =1, 2 do
			if i == 1 or need_double then
				self.node_list["hecheng_item"..i]:SetActive(true)
				self.node_list["hecheng_lock"..i]:SetActive(false)
				local num = MingWenWGData.Instance:GetMingWenBagItemNum(posy_id_vec[i])
				if equip_posy[i] then
					num = num +1
				end

				local base_cfg1 = MingWenWGData.Instance:GetMingWenBaseCfgByID(posy_id_vec[i])
				if base_cfg1 then
					self.node_list["hecheng_name"..i].text.text = ToColorStr(base_cfg1.name, ITEM_COLOR[base_cfg1.quality])
				end

				self.hecheng_cost_item_list[i]:SetData({item_id = base_cfg1.id})

				local is_enough1 = num >= posy_num_vec[i]
				if not is_enough1 then
					self.is_enough = false
				end

				local color1 = is_enough1 and COLOR3B.D_GREEN or COLOR3B.D_RED
				local data1 = {}
				data1.item_id = posy_id_vec[i]
				--self.hecheng_cost_item[i]:SetData(data1)
				local str1 = num.."/"..posy_num_vec[i]
				self.node_list["hecheng_cost_num_bg"..i]:SetActive(true)
				self.node_list["hecheng_cost_num"..i].text.text = ToColorStr(str1,color1)
			else
				self.node_list["hecheng_lock"..i]:SetActive(true)
				self.node_list["hecheng_name"..i].text.text = ""
				self.node_list["hecheng_item"..i]:SetActive(false)
				self.node_list["hecheng_cost_num"..i].text.text = ""
				self.node_list["hecheng_cost_num_bg"..i]:SetActive(false)
			end
		end

		self:HeChengCacularAttr(self.is_enough,posy_id_vec,posy_num_vec,need_double,equip_posy)
	end
end

function MingWenView:HeChengCacularAttr(is_enough,posy_id_vec,posy_num_vec,need_double,equip_posy)
	local base_cfg = MingWenWGData.Instance:GetMingWenBaseCfgByID(self.cur_hecheng_id)
	if not base_cfg then
		return
	end

	local attr_type,attr_value,add_value = MingWenWGData.Instance:GetEquipPosyAttrByItemId(self.cur_hecheng_id)
	local name_list,is_pre = MingWenWGData.Instance:GetAttributeNameListByType(attr_type)
	local name_str = ""

	for k,v in pairs(name_list) do
		name_str = name_str .. v.."\n"
	end
	local get_level = 1
	self.node_list["hecheng_target_attr"].text.text = name_str
	local bag_item_list = {}
	if is_enough then
		for i = 1, 2 do
			if i == 1 or need_double then
				local need_bag_item = posy_num_vec[i]
				if equip_posy[i] then
					need_bag_item = need_bag_item -1
				end
				if need_bag_item > 0 then
					bag_item_list[i] = {}
				 	local available_item_list = MingWenWGData.Instance:GetMingWenBagItemByID(posy_id_vec[i])
				 	local find_num = 0
				 	for k,v in pairs(available_item_list) do
				 		if find_num < need_bag_item then
				 			find_num = find_num + 1
				 			bag_item_list[i][find_num] = v
				 		else
				 			local smallest = -1
				 			for t,q in pairs(bag_item_list[i]) do
				 				if smallest == -1 then
				 					smallest = t
				 				else
				 					if bag_item_list[i][smallest].level > q.level then
				 						smallest = t
				 					end
				 				end
				 			end
				 			if bag_item_list[i][smallest].level < v.level then
				 				bag_item_list[i][smallest] = v
				 			end
				 		end
				 	end
				end
			end
		end

		local get_exp = 0
		local get_minyuin = 0
		local max_level = {}
		for i= 1,2 do
			if equip_posy[i] then
				local add_exp,add_minyin = MingWenWGData.Instance:GetMingWenFenJieGet(equip_posy[i])
				get_exp = get_exp + add_exp
				get_minyuin = get_minyuin + add_minyin
				max_level[i] = equip_posy[i].level
			end
			if bag_item_list[i] then
				for k,v in pairs(bag_item_list[i]) do
					local add_exp,add_minyin = MingWenWGData.Instance:GetMingWenFenJieGet(v)
					get_exp = get_exp + add_exp
					get_minyuin = get_minyuin + add_minyin
					if not max_level[i] then
						max_level[i] = v.level
					elseif v.level > max_level[i] then
						max_level[i] = v.level
					end
				end
			end
		end

		get_level = MingWenWGData.Instance:CacularComposeLevel(base_cfg.cost_type,get_exp,get_minyuin)
		for i =1,2 do
			if i==1 or need_double then
				local base_cfg1 = MingWenWGData.Instance:GetMingWenBaseCfgByID(posy_id_vec[i])
				if base_cfg1 then
					self.node_list["hecheng_name"..i].text.text = ToColorStr(base_cfg1.name.."Lv."..max_level[i], ITEM_COLOR[base_cfg1.quality])
				end
			end
		end
	end

	local attr_str = ""
	self.node_list["hecheng_target_name"].text.text = ToColorStr(base_cfg.name.."Lv."..get_level, ITEM_COLOR[base_cfg.quality])
	get_level= get_level -1
	for k,v in pairs(attr_value) do
		if is_pre[k] then
			attr_str = attr_str .. ((tonumber(v)+ tonumber(add_value[k]) * get_level)/100).."%\n"
		else
			attr_str = attr_str .. (tonumber(v)+ tonumber(add_value[k]) * get_level).."\n"
		end
	end

	self.node_list["hecheng_target_value"].text.text = attr_str
	self.copmpse_item_list = bag_item_list
	self.compose_equip_list = equip_posy
end

function MingWenView:MingWenClickCompose()
	if self.is_enough then
		local index_list = {}
		local count = 0

		for i=1,2 do
			if self.compose_equip_list[i] then
				count = count + 1
				index_list[count] = {}
				index_list[count].type = PPSY_OPERA_TYPE.POSY_FROM_NORMAL_SLOT
				index_list[count].index = self.compose_equip_list[i].equip_index
			end
		end

		if not IsEmptyTable(self.copmpse_item_list) then
			for i=1,2 do
				if self.copmpse_item_list[i] then
					for k,v in pairs(self.copmpse_item_list[i]) do
						count = count + 1
						index_list[count] = {}
						index_list[count].type = PPSY_OPERA_TYPE.POSY_FROM_BAG
						index_list[count].index = v.index
					end
				end
			end
		end

		MingWenWGCtrl.Instance:SendMingWenCompose(self.cur_hecheng_id,count, index_list)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.MingWenView.NotEnough)
	end
end

function MingWenView:MingWenClickTarget(index)
	if index == 1 and self.cur_hecheng_data then
		local posy_id_list = string.split(self.cur_hecheng_data.posy_id_vec,"|")
		if posy_id_list[1] then
			TipWGCtrl.Instance:OpenItem({item_id = tonumber(posy_id_list[1])})
		end
	elseif index == 2 then
		local posy_id_list = string.split(self.cur_hecheng_data.posy_id_vec,"|")
		if posy_id_list[2] then
			TipWGCtrl.Instance:OpenItem({item_id = tonumber(posy_id_list[2])})
		end
	elseif index == 3 and self.cur_hecheng_id then
		TipWGCtrl.Instance:OpenItem({item_id = self.cur_hecheng_id})
	end

end
function MingWenView:HeChengSuccess()
	if self.node_list["hecheng_effect"] then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_hecheng, is_success = true, pos = Vector2(0, 0),
						parent_node = self.node_list["hecheng_effect"]})
	end
end

--------------------------------------------------------------------------

HeChengListItemRender = HeChengListItemRender or BaseClass(BaseRender)

function HeChengListItemRender:__init()
	self.node_list["lock_btn"].button:AddClickListener(BindTool.Bind(self.OnClickLock, self))
end

function HeChengListItemRender:ReleaseCallBack()

end

function HeChengListItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local is_limite, condition = MingWenWGData.Instance:GetPosyIsLimite(self.data[1].get_id)

	self.node_list["lock_obj"]:SetActive(is_limite) --锁
	self.node_list["lock_text"]:SetActive(is_limite) --解锁文字
	self.node_list["lock_btn"]:SetActive(is_limite)
	
	local name_str = self.data[1].sonlabel_name
	self.node_list["text_name"].text.text = name_str
	self.node_list["text_name_hl"].text.text = name_str

	local limit_str = string.format(Language.MingWenView.HeChengCondition, condition)
	self.node_list["lock_text"].text.text = limit_str
	self:FlushHeChengRed()
end

function HeChengListItemRender:OnClickLock()
	local is_limite,condition = MingWenWGData.Instance:GetPosyIsLimite(self.data[1].get_id)
	local str = string.format(Language.MingWenView.DuiHuanConditionTips,condition)
	TipWGCtrl.Instance:ShowSystemMsg(str)
end

function HeChengListItemRender:FlushHeChengRed()
	if IsEmptyTable(self.data) then
		self.node_list["img_remind"]:SetActive(false)
	else
		local is_red = MingWenWGData.Instance:GetMingWenItemCanHeCheng(self.data[1].get_id)
		self.node_list["img_remind"]:SetActive(is_red == 1)
	end
end
