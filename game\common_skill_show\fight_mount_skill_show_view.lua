FightMountSkillShowView = FightMountSkillShowView or BaseClass(CommonSkillShowView)

function FightMountSkillShowView:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self:SetMaskBg(false)
	self:SetMaskBgAlpha(0)
    
    self:AddViewResource(0, "uis/view/skill_show_ui_prefab", "skill_show_bg_scene")
    self:AddViewResource(0, "uis/view/skill_show_ui_prefab", "layout_fight_mount_skill_show_view")
end

function FightMountSkillShowView:LoadCallBack()
	self:InitEnemy()

    self.cur_select_fm_index = -1
    self.cur_select_fm_data = nil
    self.cur_huamo_level = 0
    if not self.fight_mount_type_list then
        self.fight_mount_type_list = AsyncListView.New(SkillShowFightMountRender, self.node_list["mount_type_list"])
        self.fight_mount_type_list:SetSelectCallBack(BindTool.Bind(self.OnSelectFightMountCallBack, self))
        self.fight_mount_type_list:SetLimitSelectFunc(BindTool.Bind(self.IsLimitClick, self))
    end

    -- 普攻
    XUI.AddClickEventListener(self.node_list.normal_attack_btn, BindTool.Bind(self.OnClickNormalAttackBtn, self))

    -- 技能
    if self.node_list.skill_list and self.skill_btn_list == nil then
        self.skill_btn_list = {}
        local node_num = self.node_list.skill_list.transform.childCount
        for i = 1, node_num do
            self.skill_btn_list[i] = SkillShowSkillRender.New(self.node_list.skill_list:FindObj("skill" .. i))
            self.skill_btn_list[i]:SetIndex(i)
            self.skill_btn_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickSkillBtn, self))
        end
    end

    for i = 1, 3 do
        XUI.AddClickEventListener(self.node_list["get_way_btn_" .. i],BindTool.Bind(self.OnClickJumpPanel, self, i))
    end

    self:ChangeViewDisplay(6101)
    self:TrySetCamera()
end

function FightMountSkillShowView:ReleaseCallBack()
    self:CleanSkillBtnCDTimer()
    CommonSkillShowView.ReleaseCallBack(self)

    self.cur_select_fm_index = -1
    self.cur_select_fm_data = nil

    if self.fight_mount_type_list then
        self.fight_mount_type_list:DeleteMe()
        self.fight_mount_type_list = nil
    end

    if self.skill_btn_list then
        for k, v in pairs(self.skill_btn_list) do
            v:DeleteMe()
        end
        self.skill_btn_list = nil
    end
end

--[[
	data = {
        mount_seq = -1,
		skill_level = 0,
        skill_act = true,
	}
]]
function FightMountSkillShowView:SetShowDataAndOpen(data)
	self.show_data = data
    self.show_data.level = data.skill_level or 1
    self.show_data.fight_mount_skill_level = data.skill_level or 1
	self:Open()
end

function FightMountSkillShowView:ShowIndexCallBack()
    self:ResetCameraFieldOfView()
end

-- 列表选择返回
function FightMountSkillShowView:OnSelectFightMountCallBack(item)
	if nil == item or nil == item.data then
		return
	end

    local data = item.data
    if self.cur_select_fm_index == data.mount_seq then
        return
    end

    self.cur_select_fm_index = data.mount_seq
    self.cur_select_fm_data = data
    local mount_seq = data.mount_seq
    self.show_data.mount_appeid = data.appe_image_id
    self:InitShower()

    -- 刷新技能格子
    local skill_list = NewFightMountWGData.Instance:GetMainUISkillOrder(nil, mount_seq)
    for k, v in pairs(self.skill_btn_list) do
        v:SetData({skill_id = skill_list[k], skill_level = 1})
    end

    self.general_attack_list = {[1] = skill_list[0]}
    self:FlushConditionPanel()
end

function FightMountSkillShowView:FlushConditionPanel()
    if self.cur_select_fm_index < 0 and (not self.cur_select_fm_data) then
        return
    end

    local mount_skill_level = NewFightMountWGData.Instance:GetMountSkillLevelBySeq(self.cur_select_fm_index)
    local is_act = mount_skill_level > 0
    self.node_list.condition_panel:SetActive(not is_act)
    if is_act then
        return
    end

    local hatch_item_info = DragonTempleWGData.Instance:GetHatchItemListInfoBySeq(self.cur_select_fm_index)
    local hatch_level = hatch_item_info and hatch_item_info.level or 0
    local name_cfg = DragonTempleWGData.Instance:GetHatchNameCfgBySeq(self.cur_select_fm_index)
    local condition_str_1 = string.format(Language.NewFightMount.confition1, name_cfg and name_cfg.name, self.cur_select_fm_data.skill_need_level)
    self.node_list.condition_desc_1.text.text = condition_str_1
    self.node_list.finish_img_1:SetActive(hatch_level >= self.cur_select_fm_data.skill_need_level)
    self.node_list.get_way_btn_1:SetActive(hatch_level < self.cur_select_fm_data.skill_need_level)

    local mount_level = NewFightMountWGData.Instance:GetAppearanceLevelBySeq(self.cur_select_fm_index)
    local condition_str_2 = string.format(Language.NewFightMount.confition2, self.cur_select_fm_data.mount_name)
    self.node_list.condition_desc_2.text.text = condition_str_2
    self.node_list.finish_img_2:SetActive(mount_level >= 1)
    self.node_list.get_way_btn_2:SetActive(mount_level < 1)

    local cur_level_cfg = NewFightMountWGData.Instance:GetUpSkillLevelCfg(self.cur_select_fm_index, mount_skill_level)
    if not cur_level_cfg then
        return
    end

    local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
    local item_cfg = ItemWGData.Instance:GetItemConfig(cur_level_cfg.cost_item_id)
    local condition_str_3 = string.format(Language.NewFightMount.confition3, item_cfg and item_cfg.name or "")
    self.node_list.condition_desc_3.text.text = condition_str_3
    self.node_list.finish_img_3:SetActive(item_num >= cur_level_cfg.cost_item_num)
    self.node_list.get_way_btn_3:SetActive(item_num < cur_level_cfg.cost_item_num)
end

function FightMountSkillShowView:OnClickJumpPanel(type)
    if self.cur_select_fm_data == nil then
        return
    end

    local open_panel = self.cur_select_fm_data["open_panel" .. type]
    local act_type = self.cur_select_fm_data["act_type" .. type]
    local open_view = false
    if (not act_type) or act_type == "" then --非活动
       open_view = FunOpen.Instance:OpenViewNameByCfg(open_panel)
    else
        local is_act_open = ActivityWGData.Instance:GetActivityIsOpen(act_type) --活动是否开启
        if is_act_open then
            open_view = FunOpen.Instance:OpenViewNameByCfg(open_panel)
        else
            TipWGCtrl.Instance:ShowSystemMsg(Language.Common.ActivateNoOpen)
            return
        end
    end

    if open_view then
        self:Close()
    end
end

-- 点击普攻
function FightMountSkillShowView:OnClickNormalAttackBtn()
    if not self.is_shower_loaded or not self.is_enemy_loaded then
		return
	end

    if IsEmptyTable(self.general_attack_list) then
        return
    end

    if self:IsLimitClick() then
		return
	end

    local skill_index = 1
    self.normal_attack_play_timestemp = Status.NowTime
    for k,v in ipairs(self.general_attack_list) do
        self.normal_attack_play_timestemp = self.normal_attack_play_timestemp + self:GetRoleSkillTime(v, skill_index)
        skill_index = skill_index + 1
    end

    self.general_attack_index = 1
    self:SimulateTSGeneralAttackOpera()
end

-- 模拟普攻
function FightMountSkillShowView:SimulateTSGeneralAttackOpera()
	self:ClearDelaySimulateGeneralAttackOpera()
	if IsEmptyTable(self.general_attack_list) then
		return
	end

	local skill_id = self.general_attack_list[self.general_attack_index]
	if not skill_id then
		return
	end

    self:ChangeViewDisplay(skill_id)
	local do_next_action_time = self:GetRoleSkillTime(skill_id, self.general_attack_index)

    self:CleanSkillBtnCDTimer()
    self:SetAllSkillBtnCD(string.format("%.1f", do_next_action_time), do_next_action_time)
    self.skill_btn_cd_timer = CountDown.Instance:AddCountDown(do_next_action_time, 0.1,
        function(elapse_time, total_time)
            self:SetAllSkillBtnCD(string.format("%.1f", total_time - elapse_time), do_next_action_time)
        end,
        function()
            self:SetAllSkillBtnCD(0, do_next_action_time)
        end
    )

	self:SimulateSingleGeneralAttackOpera(skill_id, self.general_attack_index)

	if do_next_action_time > 0 then
		self.general_attack_timer = GlobalTimerQuest:AddTimesTimer(function ()
	        self.general_attack_index = self.general_attack_index + 1
			self:SimulateTSGeneralAttackOpera()
		end, do_next_action_time, 1)
	end
end

-- 点击技能
function FightMountSkillShowView:OnClickSkillBtn(item)
    if not self.is_shower_loaded or not self.is_enemy_loaded then
		return
	end

    if self:IsLimitClick() then
		return
	end

    local data = item:GetData()
    if not data then
        return
    end

    self:CleanSkillBtnCDTimer()
    self.show_data.skill_id = data.skill_id
    self.show_data.level = data.skill_level
    self:ChangeViewDisplay(data.skill_id)

    local is_fm_ultimate_skill, fm_type = NewFightMountWGData.Instance:IsFightMountUltimateSkillId(data.skill_id)
    local cg_bundle, cg_asset
    if is_fm_ultimate_skill then
        local cfg = NewFightMountWGData.Instance:GetFightMountSkillCfgByseq(fm_type)
        if cfg then
            cg_bundle = cfg.dz_bundle
            cg_asset = cfg.dz_asset
        end
    end

    local do_skill_func = function ()
        -- 0.5s 等带特效后小半截播完
        local skill_show_time = self:GetRoleSkillTime(data.skill_id, 1) + 0.5
        self.skill_play_timestemp = Status.NowTime + skill_show_time
        self:SetAllSkillBtnCD(string.format("%.1f", skill_show_time), skill_show_time)
        self.skill_btn_cd_timer = CountDown.Instance:AddCountDown(skill_show_time, 0.1,
            function(elapse_time, total_time)
                self:SetAllSkillBtnCD(string.format("%.1f", total_time - elapse_time), skill_show_time)
            end,
            function()
                self:SetAllSkillBtnCD(0, skill_show_time)
            end
        )

        self:DelaySimulateSpecialSkillOpera(0)
    end

	if cg_bundle and cg_asset and cg_bundle ~= "" and cg_asset ~= "" and not CgManager.Instance:IsCgIng() then
		CgManager.Instance:Play(UICg.New(cg_bundle, cg_asset), function()
			do_skill_func()
		end)
	else
        do_skill_func()
	end
end

function FightMountSkillShowView:SetAllSkillBtnCD(time, total_time)
    if self.skill_btn_list then
        for k,v in pairs(self.skill_btn_list) do
            v:SetSkillBtnCD(time, total_time)
        end
    end
end

-- 清除倒计时器1
function FightMountSkillShowView:CleanSkillBtnCDTimer()
    if self.skill_btn_cd_timer and CountDown.Instance:HasCountDown(self.skill_btn_cd_timer) then
        CountDown.Instance:RemoveCountDown(self.skill_btn_cd_timer)
        self.skill_btn_cd_timer = nil
    end
end

function FightMountSkillShowView:IsLimitClick(no_tips)
    local is_playing_skill = Status.NowTime < self.skill_play_timestemp or Status.NowTime < self.normal_attack_play_timestemp
    if not no_tips and is_playing_skill then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Skill.ShowSkillLimitClickStr)
    end

    return is_playing_skill
end

function FightMountSkillShowView:OnFlush()
    if not self.show_data then
        return
    end

    local all_mount_cfg = NewFightMountWGData.Instance:GetAllFightMountTypeCfg()
    if IsEmptyTable(all_mount_cfg) then
        return
    end

    if self.fight_mount_type_list then
        self.fight_mount_type_list:SetDataList(all_mount_cfg)
    end

    local show_mount_seq = self.show_data.mount_seq
    if self.cur_select_fm_index ~= show_mount_seq then
        local jump_index = 1
        for k,v in pairs(all_mount_cfg) do
            if show_mount_seq == v.mount_seq then
                jump_index = k
                break
            end
        end

        self.fight_mount_type_list:JumpToIndex(jump_index)
    end
end















--===================================================================
local NameTextColor = {
    [0] =  Color.New(168/255, 227/255, 1, 1),
    [1] =  Color.New(254/255, 201/255, 201/255, 1),
    [2] =  Color.New(255/255, 234/255, 188, 1),
    [3] =  Color.New(167/255, 254/255, 241/255, 1),
    [4] =  Color.New(237/255, 201/255, 254/255, 1),
}

local effect_name = {
    [0] = "UI_zdzq_lan",
    [1] = "UI_zdzq_hong",
    [2] = "UI_zdzq_jin",
    [3] = "UI_zdzq_hei",
    [4] = "UI_zdzq_bai",
}

SkillShowFightMountRender = SkillShowFightMountRender or BaseClass(BaseRender)
function SkillShowFightMountRender:OnFlush()
    local bundle = "uis/view/skill_show_ui/images_atlas"
    local asset = "a2_zdzq_type_" .. self.data.mount_seq
    self.node_list.icon.image:LoadSprite(bundle, asset, function()
        self.node_list.icon.image:SetNativeSize()
    end)


    self.node_list.name.text.text = self.data.mount_name
    self.node_list.name.text.color = NameTextColor[self.data.mount_seq] or COLOR3B.WHITE

    local effect_bundle, effect_asset = ResPath.GetA2Effect(effect_name[self.data.mount_seq])
    if effect_bundle and effect_asset then
	    self.node_list.effect:ChangeAsset(effect_bundle, effect_asset)
    end
end

function SkillShowFightMountRender:OnSelectChange(is_select)
    self.node_list["select_img"]:SetActive(is_select)
end