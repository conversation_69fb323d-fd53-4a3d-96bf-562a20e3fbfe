TotalRechargeGiftWGData = TotalRechargeGiftWGData or BaseClass()
TotalRechargeGiftWGData.MAX_REWARD_COUNT = 5 -- 一轮奖励
TotalRechargeGiftWGData.SHIFU_DISCOUNT = 2      --实付折扣.

function TotalRechargeGiftWGData:__init()
    if TotalRechargeGiftWGData.Instance then
		error("[TotalRechargeGiftWGData] Attempt to create singleton twice!")
		return
	end

    TotalRechargeGiftWGData.Instance = self
    self:InitConfig()
    self.reward_flag = {}
    self.every_day_flag = false
    self.show_tip = true

    RemindManager.Instance:Register(RemindName.TotalRechargeGift, BindTool.Bind(self.GetRemind, self))
end

function TotalRechargeGiftWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.TotalRechargeGift)
    TotalRechargeGiftWGData.Instance = nil
    self.reward_flag = nil
    self.every_day_flag = nil
end

function TotalRechargeGiftWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_haoli_wanzhang2_auto")
    if not cfg then
        return
    end
    self.cur_reward_cfg = ListToMapList(cfg.reward, "grade", "round")
    self.cur_model_cfg = ListToMap(cfg.display_model, "grade", "round")
    self.show_waist_cfg = ListToMap(cfg.show_waist, "grade", "round")
    self.suit_act_map_cfg = ListToMap(cfg.activation, "grade", "round", "part")
    self.show_wuhun_cfg = ListToMap(cfg.wuhunzhanshi, "grade", "round")
    self.grade_cfg = ListToMap(cfg.open_day, "grade")
    
    self.open_day_cfg = cfg.open_day
    self.other_cfg = cfg.other[1]
end

function TotalRechargeGiftWGData:SetAllVientianeInfo(protocol)
    self.score = protocol.score
    self.grade = protocol.grade
	self.round_num = protocol.round_num
	self.reward_flag = protocol.reward_flag
    self.every_day_flag = protocol.every_day_flag == 1
end

-- 获取累充额度
function TotalRechargeGiftWGData:GetCurScore()
    return self.score or 0
end

-- 获取每日领取标识
function TotalRechargeGiftWGData:GetShopIsBuyFlag()
	return self.every_day_flag
end

-- 获取所有奖励领取状态
function TotalRechargeGiftWGData:GetAllRewardState()
	return self.reward_flag[self.round_num] or {}
end

function TotalRechargeGiftWGData:GetCurGrade()
    return self.grade or 0
end

-- 获取当前轮次
function TotalRechargeGiftWGData:GetCurRoundNum()
    return self.round_num or 0
end

-- 获取当前档次总轮次
function TotalRechargeGiftWGData:GetTotalRoundNum()
    return #(self.cur_reward_cfg[self.grade] or {})
end

-- 红点是否可领取
function TotalRechargeGiftWGData:GetRemind()
    --屏蔽每日礼包.
    -- local is_buy_free = self:GetShopIsBuyFlag()
    -- if not is_buy_free then
    --     return 1
    -- end
	if self:GetReceiveRemind() then
		return 1
	end

	return 0
end


function TotalRechargeGiftWGData:GetReceiveRemind()
    local remind = false
    local score = self:GetCurScore()
    local flag_num = self:GetAllRewardState()
    local cur_cfg = self:GetCurRoundRewardCfg()
    if not IsEmptyTable(cur_cfg) and not IsEmptyTable(flag_num) then
        for k, v in pairs(cur_cfg) do
            if score >= v.need_lingyu then
                local is_get = self:GetRewardStateBySeq(v.seq)

                if not is_get then
                    remind = true
                    break
                end
            end
        end

        -- for i = 1, #cur_cfg do
        --     if score >= cur_cfg[i].need_lingyu then
        --         if flag_num[i - 1] == 0 then
        --             remind = true
        --         end
        --     end
        -- end
    end

    return remind
end

function TotalRechargeGiftWGData:GetReceiveRemindListJumpIndex()
    local jump_index = 1
    local have_remind = false
    local score = self:GetCurScore()
    local flag_num = self:GetAllRewardState()
    local cur_cfg = self:GetCurRoundRewardCfg()
    if not IsEmptyTable(cur_cfg) and not IsEmptyTable(flag_num) then
        for k, v in pairs(cur_cfg) do
            if score >= v.need_lingyu then
                local is_get = self:GetRewardStateBySeq(v.seq)

                if not have_remind and not is_get then
                    jump_index = k
                    have_remind = true
                end
            end
        end
    end

    if not have_remind then
        if not IsEmptyTable(cur_cfg) and not IsEmptyTable(flag_num) then
            for k, v in pairs(cur_cfg) do
                if score >= v.need_lingyu then
                    local is_get = self:GetRewardStateBySeq(v.seq)
    
                    if is_get then
                        jump_index = k
                    end
                end
            end
        end
    end

    return jump_index
end

--获取下一阶段的index.
function TotalRechargeGiftWGData:GetReceiveNextIndex()
    local score = self:GetCurScore()
    local cur_cfg = self:GetCurRoundRewardCfg()
    local jump_index = #cur_cfg

    for k, v in pairs(cur_cfg) do
        if score < v.need_lingyu then
            jump_index = k
            break
        end
    end

    return jump_index
end

function TotalRechargeGiftWGData:GetCurLocation()
    local score = self:GetCurScore()
    local cur_cfg = self:GetCurRoundRewardCfg()
    local location = -1
    if not IsEmptyTable(cur_cfg) then
        for i = 1, #cur_cfg do
            if score < cur_cfg[i].need_lingyu then
                location = i
                break
            elseif score >= cur_cfg[#cur_cfg].need_lingyu then
                location = #cur_cfg + 1
            end
        end
    end

    return location
end

function TotalRechargeGiftWGData:GetTipShowShopCfg()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local cfg = {}
	for i, v in ipairs(self.open_day_cfg) do
		if server_day >= v.start_day and server_day <= v.end_day then
			cfg = v
		end
	end

	return cfg
end

function TotalRechargeGiftWGData:GetIsShowTip()
	local is_show = false
    local role_level = GameVoManager.Instance:GetMainRoleVo().level
    if self.show_tip then
		local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TOTAL_RECHARGE_GIFT)
		if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
            if role_level >= self.other_cfg.open_level then
                is_show = true
            end
		end
	end

	return is_show
end

function TotalRechargeGiftWGData:SetIsShowTip(state)
	self.show_tip = state
end


function TotalRechargeGiftWGData:GetRewardStateBySeq(seq) -- 获取奖励状态
    if not IsEmptyTable(self.reward_flag) then
        local flag_num = self:GetAllRewardState()
        return flag_num and flag_num[seq] == 1
    end
end

function TotalRechargeGiftWGData:GetCurRoundRewardCfg()
    return (self.cur_reward_cfg[self.grade] or {})[self.round_num]
end

function TotalRechargeGiftWGData:GetCurRoundModelCfg()
    return (self.cur_model_cfg[self.grade] or {})[self.round_num]
end

function TotalRechargeGiftWGData:GetCurShowWaistCfg()
    return (self.show_waist_cfg[self.grade] or {})[self.round_num]
end

function TotalRechargeGiftWGData:GetCurShowWuHunCfg()
	return (self.show_wuhun_cfg[self.grade] or {})[self.round_num]
end

function TotalRechargeGiftWGData:GetActivationPartList()
	return (self.suit_act_map_cfg[self.grade] or {})[self.round_num]
end

function TotalRechargeGiftWGData:GetOtherCfg()
	return self.other_cfg
end

function TotalRechargeGiftWGData:GetCurGradeCfg()
    return (self.grade_cfg or {})[self.grade]
end

