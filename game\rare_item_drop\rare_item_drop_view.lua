RareItemDropView = RareItemDropView or BaseClass(SafeBaseView)

function RareItemDropView:__init()
	self:SetMaskBg(true, true)
	self.view_name = "RareItemDropView"
	self.view_layer = UiLayer.Pop
	self.active_close = false

	self:AddViewResource(0, "uis/view/rare_item_drop_ui_prefab", "layout_rare_item_drop")
	self:InitParam()
end

function RareItemDropView:LoadCallBack()
	self:InitPanel()
	self:InitRareItemList()
	self:ReadyShowTween()
end

function RareItemDropView:ReleaseCallBack()
	if self.rare_item_cell_list then
		for _, v in pairs(self.rare_item_cell_list) do
			v:DeleteMe()
		end
		self.rare_item_cell_list = nil
	end
	self:InitParam()
end

function RareItemDropView:OpenCallBack()
	self.rare_item_data_list = RareItemDropWGData.Instance:GetSortRareItemList()
	RareItemDropWGData.Instance:CleanCahceItemList()
	if IsEmptyTable(self.rare_item_data_list) then
		self:Close()
		return
	end

	---[[ 安全起见10s后强行关闭
	ReDelayCall(self, function()
		self:Close()
	end, 10, "RareItemDropView")
	--]]

	self:ReadyShowTween()
end

function RareItemDropView:CloseCallBack()
	CancleDelayCall(self, "RareItemDropView")
	if self.my_show_tween then
		self.my_show_tween:Kill()
		self.my_show_tween = nil
	end

	RareItemDropWGData.Instance:CleanCahceItemList()
	RareItemDropWGCtrl.Instance:EndShowRareItem()
end

function RareItemDropView:OnFlush(param_t)
	self:RefreshView()
end

function RareItemDropView:InitParam()
	self.rare_item_data_list = {}
	self.rare_item_cell_list = {}
	self.rare_item_root_list = {}
	self.save_root_scale_list = {}
	self.save_root_pos_list = {}
end

function RareItemDropView:InitPanel()
	local root_list = {}
	local scale_list = {}
	local pos_list = {}
	for i = 1, 7 do
		local item = self.node_list["rare_item_root_" .. i]
		root_list[i] = item
		pos_list[i] = item.rect.anchoredPosition
		scale_list[i] = item.transform.localScale.x
	end
	self.rare_item_root_list = root_list
	self.save_root_scale_list = scale_list
	self.save_root_pos_list = pos_list
end

function RareItemDropView:InitRareItemList()
	local res_async_loader = AllocResAsyncLoader(self, "rare_item")
	res_async_loader:Load("uis/view/rare_item_drop_ui_prefab", "rare_item", nil,
		function(new_obj)
			if IsNil(new_obj) or IsEmptyTable(self.rare_item_root_list) then
				return
			end
			local item_list = {}
			local root_list = self.rare_item_root_list
			for i = 1, 7 do
				local item_parent = root_list[i].transform
				local obj = ResMgr:Instantiate(new_obj)
				obj.transform:SetParent(item_parent, false)
				item_list[i] = RareItemDrop.New(obj)
				item_list[i]:SetIndex(i)
			end
			self.rare_item_cell_list = item_list
			self:RefreshView()
		end)
end

function RareItemDropView:RefreshView()
	if IsEmptyTable(self.rare_item_cell_list) then
		return
	end
	local data_list = self.rare_item_data_list
	---[[ 多个获得的拆分开来显示
	local temp_list = {}
	for i = 1, #data_list do
		temp_list[#temp_list + 1] = data_list[i]
		if data_list[i].item_num and data_list[i].item_num > 1 then
			for j = 1, data_list[i].item_num do
				temp_list[#temp_list + 1] = data_list[i]
			end
		end
	end
	data_list = temp_list
	--]]
	local cell_list = self.rare_item_cell_list
	for i = 1, #cell_list do
		cell_list[i]:SetData(data_list[i])
	end
	self:PlayShowTween()
end

---[[ 播展示动画
function RareItemDropView:ReadyShowTween()
	if IsEmptyTable(self.rare_item_root_list) then
		return
	end

	local root_list = self.rare_item_root_list
	for i = 1, #root_list do
		RectTransform.SetAnchoredPositionXY(root_list[i].rect, 0, 0)
		root_list[i].transform:SetLocalScale(0, 0, 0)
	end
end

function RareItemDropView:PlayShowTween()
	if self.my_show_tween then
		return
	end

	local root_list = self.rare_item_root_list
	local scale_list = self.save_root_scale_list
	local pos_list = self.save_root_pos_list
	local cell_list = self.rare_item_cell_list

	local show_num = #self.rare_item_data_list
	if show_num > #root_list then
		show_num = #root_list
	end

	local tween_sequence = DG.Tweening.DOTween.Sequence()

	for i = show_num, 1, -1 do
		local tween_move = root_list[i].rect:DOAnchorPos(pos_list[i], 0.2)
		local tween_scale = root_list[i].transform:DOScale(scale_list[i], 0.4)
		tween_scale:SetEase(DG.Tweening.Ease.OutBack)
		tween_sequence:Append(tween_move)
		tween_sequence:Join(tween_scale)
	end

	tween_sequence:InsertCallback((show_num - 1) * 0.4 + 0.1, function()
		Scene.Instance:ShakeMainCamera(0.5, 1, 30, 40)
	end)

	tween_sequence:AppendInterval(4)

	tween_sequence:OnComplete(function()
		self.my_show_tween = nil
		self:Close()
	end)

	self.my_show_tween = tween_sequence
end

--]]

------------------------------------------------------------------------

RareItemDrop = RareItemDrop or BaseClass(BaseRender)

function RareItemDrop:__init()
	self.is_weapon_model = false
	self.fazhen_tween = nil
	self.eq_model_tween = nil
	self.special_fashion_info = nil
end

function RareItemDrop:__delete()
	self.rare_item_model:DeleteMe()
	self.rare_item_model = nil
	self.t_model_display:DeleteMe()
	self.t_model_display = nil
	if self.fazhen_tween then
		self.fazhen_tween:Kill()
		self.fazhen_tween = nil
	end
	if self.eq_model_tween then
		self.eq_model_tween:Kill()
		self.eq_model_tween = nil
	end
end

function RareItemDrop:LoadCallBack()
	self.rare_item_model = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["eq_model_root"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.S,
		can_drag = false,
	}
	
	self.rare_item_model:SetRenderTexUI3DModel(display_data)
	-- self.rare_item_model:SetUI3DModel(self.node_list.eq_model_root.transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE)
	self.t_model_display = OperationActRender.New(self.node_list.act_model_root)
	self.t_model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	self:InitStarList()
end

function RareItemDrop:InitStarList()
	local all_star_list = {}
	for i = 1, 6 do
		all_star_list[i] = self.node_list["jin_star_" .. i]
		all_star_list[6 + i] = self.node_list["red_star_" .. i]
	end
	self.all_star_list = all_star_list
end

function RareItemDrop:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
		self:SetVisible(false)
		return
	end
	self:SetVisible(true)

	self.is_weapon_model = false

	self:FlushRareItemModel()
	self:FlushRareItemName()
	self:FlushRareItemStar()
	self:FlushRareItemBg()
	self:FlushRareItemEffect()
	-- self:PlayFaZhenTween()
end

function RareItemDrop:FlushRareItemName()
	local index = self:GetIndex()
	local data = self:GetData()
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	self.node_list.item_name.text.text = item_cfg and item_cfg.name or ""

	---[[ 名字被缩小后看不清要求特殊放大
	if index == 1 then
		self.node_list.name_bg.transform:SetLocalScale(1, 1, 1)
	elseif index > 1 and index < 6 then
		self.node_list.name_bg.transform:SetLocalScale(1.2, 1.2, 1.2)
	elseif index >= 6 and index <= 7 then
		self.node_list.name_bg.transform:SetLocalScale(1.5, 1.5, 1.5)
	end
	--]]
end

function RareItemDrop:FlushRareItemStar()
	local data = self:GetData()
	local star_level = data.star_level or 0
	if data.param then
		star_level = data.param.star_level or 0
	end

	if star_level > 0 then
		local star_list = self.all_star_list
		for i = 1, #star_list do
			star_list[i]:SetActive(i <= star_level)
		end
		self.node_list.star_area:SetActive(true)
	else
		self.node_list.star_area:SetActive(false)
	end
end

---[[
function RareItemDrop:FlushRareItemBg()
	-- local data = self:GetData()
	-- local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	-- local item_color = item_cfg and item_cfg.color or 0

	-- 背景黑底图
	-- local bg_color = self:LimitValue(item_color, 4, 7)
	-- local bundle_bg, asset_bg = ResPath.GetF2RawImagesPNG("rare_item_bg_" .. bg_color)
	-- self.node_list.rare_bg.raw_image:LoadSprite(bundle_bg, asset_bg)
end

function RareItemDrop:GetFaZhenResId(item_color)
	local data = self:GetData()
	if data.is_fashion then
		local fz_color = self:LimitValue(item_color, 4, 8)
		return "sz_fazhen_" .. fz_color
	elseif data.is_equip then
		local fz_color = self:LimitValue(item_color, 4, 5)
		return "eq_fazhen_" .. fz_color
	end
end

function RareItemDrop:LimitValue(value, min_value, max_value)
	if value < min_value then
		value = min_value
	elseif value > max_value then
		value = max_value
	end
	return value
end

function RareItemDrop:PlayFaZhenTween()
	if self.fazhen_tween then
		return
	end
	local tween = self.node_list.rare_kuang_effect.transform:DOLocalRotate(Vector3(0, 0, 360), 3, DG.Tweening.RotateMode.FastBeyond360)
	tween:SetEase(DG.Tweening.Ease.Linear)
	tween:SetLoops(-1)
	self.fazhen_tween = tween
end

--]]

---[[
function RareItemDrop:FlushRareItemEffect()
	local data = self:GetData()
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	local item_color = item_cfg and item_cfg.color or 0

	local color_k = self:LimitValue(item_color, 4, 8)
	local bundle_k, asset_k = ResPath.GetUIEffect("UI_boss_diaoluo_" .. color_k)
	self.node_list.rare_kuang_effect:ChangeAsset(bundle_k, asset_k)
end

function RareItemDrop:GetBgEffectResId(item_color)
	local data = self:GetData()
	if data.is_fashion then
		local fz_color = self:LimitValue(item_color, 4, 8)
		return "UI_sz_fazhen_" .. fz_color
	elseif data.is_equip then
		local fz_color = self:LimitValue(item_color, 4, 5)
		return "UI_eq_fazhen_" .. fz_color
	end
end

--]]

function RareItemDrop:FlushRareItemModel()
	local data = self:GetData()
	if data.rare_item_type == RareItemType.Fashion then
		data.is_fashion = true
		self:FlushSpecialFashion()
		self:FlushRareFashionModel()
	elseif data.rare_item_type == RareItemType.Equip then
		data.is_equip = true
		self:FlsuhRareEquipModel()
	elseif data.rare_item_type == RareItemType.Special then
		data.is_special = true
		self:FlushSpecialRareModel()
	end

	self.node_list.eq_model_root:SetActive(data.is_equip or data.is_special or data.is_shenshou or data.is_special_fashion)
	self.node_list.act_model_root:SetActive(data.is_fashion)

	self:FlushEqModelSize()
	self:FlushEqModelPos()
	-- self:FlushEqModelRotation()
	self:PlayEqModelTween()
end

-- 时装
function RareItemDrop:FlushRareFashionModel()
	local data = self:GetData()
	if data.is_special_fashion then
		return
	end
	local info = {}
	info.should_ani = true
	info.item_id = data.item_id
	info.render_type = OARenderType.RoleModel
	self.t_model_display:SetData(info)
end

-- 特殊时装 几种类型时装需要单独显示物品不带人物模型(位置 大小 旋转 都要特殊处理)
function RareItemDrop:FlushSpecialFashion()
	local data = self:GetData()
	local res_id, part_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(data.item_id)
	if not res_id then
		return
	end

	local path = nil
	local info = { pos_x = 0, pos_y = 0, scale = 1, rotation_x = 0, rotation_y = 0, rotation_z = 0 }

	if part_type == SHIZHUANG_TYPE.MASK then -- 脸饰
		-- info.pos_y = -2070
		-- info.scale = 4
		path = ResPath.GetMaskModel
	elseif part_type == SHIZHUANG_TYPE.BELT then -- 腰饰
		-- info.pos_y = -252
		-- info.scale = 3
		path = ResPath.GetBeltModel
	elseif part_type == SHIZHUANG_TYPE.WEIBA then -- 尾巴
		-- info.pos_x, info.pos_y = -78, -221
		-- info.rotation_y = 90
		path = ResPath.GetWeibaModel
	elseif part_type == SHIZHUANG_TYPE.SHOUHUAN then -- 手环
		-- info.rotation_z = -30
		-- info.scale = 3
		path = ResPath.GetShouhuanModel
	elseif part_type == SHIZHUANG_TYPE.FOOT then -- 足迹
		-- path = ResPath.GetUIFootEffect
	end

	if not path then
		return
	end

	data.is_fashion = false
	data.is_special_fashion = true
	self.special_fashion_info = info

	local bundle, asset = path(res_id)
	self.rare_item_model:SetMainAsset(bundle, asset)
end

-- 装备模型
function RareItemDrop:FlsuhRareEquipModel()
	local data = self:GetData()
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	if not item_cfg then return end

	local sub_type = item_cfg.sub_type or 0

	self.rare_item_model:ClearModel()

	if sub_type >= GameEnum.EQUIP_TYPE_TOUKUI and sub_type <= GameEnum.EQUIP_TYPE_XIANZHUO then -- 普通装备
		local res_id = sub_type - 100
		-- 武器的特殊处理拿一把时装武器模型显示
		if res_id == GameEnum.EQUIP_INDEX_WUQI then
			local weapon_res_id = RoleWGData.GetJobWeaponId(item_cfg.limit_sex, item_cfg.limit_prof)
			local bundle, asset = ResPath.GetWeaponModelRes(weapon_res_id)
			self.rare_item_model:SetMainAsset(bundle, asset)
		else
			local bundle, asset = ResPath.GetRareEquipItemModel(RARE_EQUIP_MODEL[res_id])
			self.rare_item_model:SetMainAsset(bundle, asset)
		end
		self.is_weapon_model = res_id == 5
	end
end

-- 特殊物品
function RareItemDrop:FlushSpecialRareModel()
	local data = self:GetData()
	local cfg = RareItemDropWGData.Instance:GetSpecialRareItemCfg(data.item_id)
	local bundle, asset = ResPath.GetRareEquipItemModel(cfg.model)
	self.rare_item_model:ClearModel()
	self.rare_item_model:SetMainAsset(bundle, asset)
end

---[[ 大小 位置 旋转 特殊处理
function RareItemDrop:FlushEqModelSize()
	local index = self:GetIndex()
	local data = self:GetData()

	if data.is_equip then
		if self.is_weapon_model then
			self.node_list.eq_model_root.transform:SetLocalScale(0.8, 0.8, 0.8)
		else
			self.node_list.eq_model_root.transform:SetLocalScale(1, 1, 1)
		end
	elseif data.is_special_fashion then
		local info = self.special_fashion_info
		self.node_list.eq_model_root.transform:SetLocalScale(info.scale, info.scale, info.scale)
	else
		self.node_list.eq_model_root.transform:SetLocalScale(1, 1, 1)
	end
end

function RareItemDrop:FlushEqModelPos()
	local data = self:GetData()
	if data.is_equip and self.is_weapon_model then
		RectTransform.SetAnchoredPositionXY(self.node_list.eq_model_root.rect, 0, 50)
	elseif data.is_special_fashion then
		local info = self.special_fashion_info
		RectTransform.SetAnchoredPositionXY(self.node_list.eq_model_root.rect, info.pos_x, info.pos_y)
	else
		RectTransform.SetAnchoredPositionXY(self.node_list.eq_model_root.rect, 0, 50)
	end
end

function RareItemDrop:FlushEqModelRotation()
	local data = self:GetData()
	if data.is_special_fashion then
		local info = self.special_fashion_info
		self.node_list.eq_model_root.transform.localRotation = Quaternion.Euler(info.rotation_x, info.rotation_y,
			info.rotation_z)
	else
		self.node_list.eq_model_root.transform.localRotation = Quaternion.Euler(0, 0, 0)
	end
end

--]]

-- 上下浮动动画
function RareItemDrop:PlayEqModelTween()
	local data = self:GetData()
	if data.is_equip then
		if not self.eq_model_tween then
			local tween_root = self.node_list["eq_model_root"].rect
			self.eq_model_tween = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 40, 1.5)
			self.eq_model_tween:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
		else
			self.eq_model_tween:Restart()
		end
	elseif self.eq_model_tween then
		self.eq_model_tween:Pause()
	end
end
