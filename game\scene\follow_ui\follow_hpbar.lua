FollowHpBar = FollowHpBar or BaseClass(VisibleObj)

local TypeNameTable = typeof(UINameTable)

function FollowHpBar:__init()
	self.shield_obj_type = ShieldObjType.FollowHpBar
	self.is_root_created = false
	self:CreateShieldHandle()
end

function FollowHpBar:__delete()
	self.scene_obj = nil
	self.need_create_root = false
	self.follow_parent = nil
	self.callback = nil
	self.visible_listner = nil

	if self.buff_list then
		for k,v in pairs(self.buff_list) do
			ResMgr:Destroy(v.view.gameObject)
			v:DeleteMe()
		end
		self.buff_list = nil
	end
end

function FollowHpBar:SetVisibleListener(visible_listner)
	self.visible_listner = visible_listner
end

local function CreateRootCallBack(gameobj, cbdata)
	local self = cbdata[1]
	FollowUi.ReleaseCBData(cbdata)

	if IsNil(gameobj) then
		return
	end

	self.view = U3DObject(gameobj)
	local name_table = gameobj:GetComponent(TypeNameTable)
	self.node_list = U3DNodeList(name_table, self)

	self:UpdateLocalPosition()
	self:UpdateHpPercent()
	self:UpdateBianShenHpPercent()
	self:UpdateActive()
	self:UpdateProgress()
	if self.buff_data then
		self:SetBuffList(self.buff_data)
	end
end

-- 在真正需要用实体时再创建
function FollowHpBar:CreateRootNode(prefab_name, obj_type, follow_parent)
	if self.is_root_created then
		return
	end

	if not self:GetVisiable() then
		self.need_create_root = true
		self.prefab_name = prefab_name
		self.obj_type = obj_type
		self.follow_parent = follow_parent
		return
	end

	self.is_root_created = true
	self.obj_type = obj_type
	local async_loader = AllocAsyncLoader(self, "root_loader")
	async_loader:SetIsUseObjPool(true)
	async_loader:SetIsInQueueLoad(true)
	async_loader:SetParent(follow_parent)
	local cbdata = FollowUi.GetCBData()
	cbdata[1] = self
	async_loader:Load("uis/view/miscpre_load_prefab", prefab_name, CreateRootCallBack, cbdata)
end

function FollowHpBar:SetHpPercent(hp_percent)
	if nil ~= self.hp_percent and self.hp_percent == hp_percent then
		return
	end

	self.hp_percent = hp_percent
	self:UpdateHpPercent()
end

function FollowHpBar:SetBianShenHpPercent(hp_percent)
	if nil ~= self.bianshen_hp_percent and self.bianshen_hp_percent == hp_percent then
		return
	end

	self.bianshen_hp_percent = hp_percent
	self:UpdateBianShenHpPercent()
end

function FollowHpBar:UpdateHpPercent()
	if nil == self.hp_percent or self:IsNil() then
		return
	end

	self.node_list["HpTop"].slider.value = self.hp_percent
	if nil == self.is_bottom_tween_move then
		self.node_list["HpBottom"].slider.value = self.hp_percent
		self.is_bottom_tween_move = true
	else
		self.node_list["HpBottom"].slider:DOValue(self.hp_percent, 0.5, false)
	end
end

function FollowHpBar:UpdateBianShenHpPercent()
	if not self.scene_obj or not self.scene_obj:IsRole() or not self.node_list then
		return
	end

	if nil == self.bianshen_hp_percent or self:IsNil() or self.bianshen_hp_percent == 0 then
		self.node_list["BianShenHPBottom"]:SetActive(false)
		self.node_list["BianShenHPTop"]:SetActive(false)
		return
	end

	if SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM ~= self.scene_obj.vo.special_appearance and SPECIAL_APPEARANCE_TYPE.GUNDAM ~= self.scene_obj.vo.special_appearance then
		self.node_list["BianShenHPBottom"]:SetActive(false)
		self.node_list["BianShenHPTop"]:SetActive(false)
		return
	end

	self.node_list["BianShenHPBottom"]:SetActive(true)
	self.node_list["BianShenHPTop"]:SetActive(true)

	self.node_list["BianShenHPTop"].slider.value = self.bianshen_hp_percent
	if nil == self.is_bianshen_tween_move then
		self.node_list["BianShenHPBottom"].slider.value = self.bianshen_hp_percent
		self.is_bianshen_tween_move = true
	else
		self.node_list["BianShenHPBottom"].slider:DOValue(self.bianshen_hp_percent, 0.5, false)
	end
end

function FollowHpBar:SetLocalPosition(x, y, z)
	if nil ~= self.local_pos and self.local_pos.x == self.x and self.local_pos.y == y and self.local_pos.z == z then
		return
	end

	self.local_pos = {x = x, y = y, z = z}
	self:UpdateLocalPosition()
end

function FollowHpBar:UpdateLocalPosition()
	if nil == self.local_pos or self:IsNil() then
		return
	end

	self.view:SetLocalPosition(self.local_pos.x, self.local_pos.y, self.local_pos.z)
end

function FollowHpBar:VisibleChanged(visible)
	if visible then
		if self.need_create_root then
			self:CreateRootNode(self.prefab_name, self.obj_type, self.follow_parent)
			self.need_create_root = false
		end
	end

	self:UpdateActive()
	self:UpdateProgress()

	if self.visible_listner then
		self.visible_listner(visible)
	end
end

function FollowHpBar:UpdateActive()
	if self:IsNil() then
		return
	end

	self.view:SetActive(self:GetVisiable())
end

function FollowHpBar:SetSceneObj(scene_obj)
	self.scene_obj = scene_obj
	self:UpdateProgress()
end

function FollowHpBar:UpdateProgress()
	if not self:GetVisiable() or self:IsNil() then
		return
	end
	local progress_bar = self.node_list["ProgressBar"]
	local bianshen_progress_bar = self.node_list["BianShenProgressBar"]
	-- local is_friend = false
	local is_enemy = false

	if progress_bar and self.scene_obj and not self.scene_obj:IsDeleted() then
		-- if self.scene_obj:IsRole() then
		-- 	local vo = self.scene_obj:GetVo()
		-- 	is_friend = SocietyWGData.Instance:GetTargetIsTeamMember(vo.origin_uid)
		-- end

		if self.scene_obj:IsCharacter() then
			local scene_logic = Scene.Instance:GetSceneLogic()
			if scene_logic ~= nil then
				is_enemy = scene_logic:IsEnemy(self.scene_obj)
			end
		end

		if not self.scene_obj or not self.scene_obj:IsMainRole() then
            if is_enemy then
                progress_bar.image:LoadSpriteAsync(ResPath.GetF2MainUIImage("a3_zjm_xxtd1"))					--红色
                bianshen_progress_bar.image:LoadSpriteAsync(ResPath.GetF2MainUIImage("a3_zjm_xxtd3"))			--蓝色
			else
				progress_bar.image:LoadSpriteAsync(ResPath.GetF2MainUIImage("a3_zjm_xxtd3"))					--蓝色
				bianshen_progress_bar.image:LoadSpriteAsync(ResPath.GetF2MainUIImage("a3_zjm_xxtd2"))		--绿色
            end
        else
            progress_bar.image:LoadSpriteAsync(ResPath.GetF2MainUIImage("a3_zjm_xxtd2"))
            bianshen_progress_bar.image:LoadSpriteAsync(ResPath.GetF2MainUIImage("a3_zjm_xxtd3"))
        end
	end
end

function FollowHpBar:SetBuffList(data)
	if self.node_list == nil or not self.node_list["BuffPanel"] then
		self.buff_data = data
		return
	end
	if not self.buff_list then
		self.buff_data = data
		self:CreateBuffList()
		return
	end
	for k,v in pairs(self.buff_list) do
		v:SetData(data[k])
	end
end

function FollowHpBar:CreateBuffList()
	if self.create_buff then
		return
	end
	self.create_buff = true
	local res_async_loader = AllocResAsyncLoader(self, "BuffSmlItem")
	res_async_loader:Load("uis/view/main_ui_prefab", "BuffSmlItem", nil,
		function(new_obj)
			if self.node_list["BuffPanel"] == nil then
				return
			end
			self.create_buff = false
			self.buff_list = {}
			for i=1, 4 do
				local obj = ResMgr:Instantiate(new_obj)
				obj.transform:SetParent(self.node_list["BuffPanel"].transform, false)
				local item_render = TargetBuffItemRender.New(obj)
				self.buff_list[i] = item_render
				if i == 4 and self.buff_data then
					self:SetBuffList(self.buff_data)
				end
			end
		end)
end

function FollowHpBar:LoadSpriteAsync(bundle_name, asset_name, callback, cbdata)
	LoadSpriteAsync(self, bundle_name, asset_name, callback, cbdata)
end

function FollowHpBar:IsNil()
	return IsNil(self.view and self.view.gameObject)
end
