require("game/tianshen_3v3/tianshen_3v3_wg_data")
require("game/tianshen_3v3/tianshen_3v3_view")
require("game/tianshen_3v3/tianshen_3v3_rule_view")
require("game/tianshen_3v3/tianshen_3v3_reward_view")
require("game/tianshen_3v3/tianshen_3v3_prepare_scene_view")
require("game/tianshen_3v3/tianshen_3v3_scene_view")
require("game/tianshen_3v3/tianshen_3v3_ready_view")
require("game/tianshen_3v3/tianshen_3v3_countdown_view")
require("game/tianshen_3v3/tianshen_3v3_end_view")
require("game/tianshen_3v3/tianshen_3v3_relive_view")
require("game/tianshen_3v3/tianshen_3v3_end_countdown_view")
require("game/tianshen_3v3/tianshen_3v3_season_rank_view")


-- 天神3v3
TianShen3v3WGCtrl = TianShen3v3WGCtrl or BaseClass(BaseWGCtrl)

function TianShen3v3WGCtrl:__init()
	if TianShen3v3WGCtrl.Instance ~= nil then
		ErrorLog("[TianShen3v3WGCtrl] attempt to create singleton twice!")
		return
	end

	TianShen3v3WGCtrl.Instance = self
	self.data = TianShen3v3WGData.New()
	self.view = TianShen3v3View.New(GuideModuleName.TianShen3v3View)
	self.rule_view = TianShen3v3RuleView.New(GuideModuleName.TianShen3v3RuleView) 												-- 规则面板
	self.reward_view = TianShen3v3RewardView.New(GuideModuleName.TianShen3v3RewardView) 										-- 奖励预览面板
	self.prepare_scene_view = TianShen3v3PrepareSceneView.New(GuideModuleName.TianShen3v3PrepareSceneView) 						-- 准备场景面板
	self.scene_view = TianShen3v3SceneView.New(GuideModuleName.TianShen3v3SceneView) 											-- 战斗场景面板
	self.ready_view = TianShen3v3ReadyView.New(GuideModuleName.TianShen3v3ReadyView) 											-- 准备面板
	self.end_view = TianShen3v3EndView.New(GuideModuleName.TianShen3v3EndView) 													-- 结算面板
	self.countdown_view = TianShen3v3CountdownView.New(GuideModuleName.TianShen3v3CountdownView) 								-- 开打倒计时
	self.end_countdown_view = TianShen3v3EndCountdownView.New(GuideModuleName.TianShen3v3EndCountdownView) 						-- 战斗结束倒计时
	self.relive_view = TianShen3v3ReliveView.New(GuideModuleName.TianShen3v3ReliveView) 										-- 天神3v3复活面板
	self.season_rank_view = TianShen3v3SeasonRankView.New(GuideModuleName.TianShen3v3SeasonRankView) 							-- 赛季排名面板

	self:RegisterAllProtocols()
	self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack,self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)
end

function TianShen3v3WGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	self.rule_view:DeleteMe()
	self.rule_view = nil

	self.reward_view:DeleteMe()
	self.reward_view = nil

	self.prepare_scene_view:DeleteMe()
	self.prepare_scene_view = nil

	self.ready_view:DeleteMe()
	self.ready_view = nil

	self.scene_view:DeleteMe()
	self.scene_view = nil

	self.end_view:DeleteMe()
	self.end_view = nil

	self.countdown_view:DeleteMe()
	self.countdown_view = nil

	self.end_countdown_view:DeleteMe()
	self.end_countdown_view = nil

	self.relive_view:DeleteMe()
	self.relive_view = nil

	self.season_rank_view:DeleteMe()
	self.season_rank_view = nil

	if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
		self.activity_change_callback = nil
	end

	TianShen3v3WGCtrl.Instance = nil
end

function TianShen3v3WGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSTianShen3V3Opera) 	
	self:RegisterProtocol(CSTianShen3V3Opera2) 																	-- 操作
	self:RegisterProtocol(SCTianShen3v3MatchingInfoNotifyReason, "OnSCTianShen3v3MatchingInfoNotifyReason")  	-- 匹配状态信息
	self:RegisterProtocol(SCTianShen3v3MatchingInfo, "OnSCTianShen3v3MatchingInfo")  							-- 匹配成功信息
	self:RegisterProtocol(SCTianShen3v3DeadInfo, "OnSCTianShen3v3DeadInfo")  									-- 主角死亡信息
	self:RegisterProtocol(SCTianShen3v3AllRoleInfo, "OnSCTianShen3v3AllRoleInfo")  								-- 战斗场景角色实时信息
	self:RegisterProtocol(SCTianShen3v3PKSceneInfo, "OnSCTianShen3v3PKSceneInfo")  								-- 场景实时信息
	self:RegisterProtocol(SCTianShen3v3PersonData, "OnSCTianShen3v3PersonData")  								-- 主角信息
	self:RegisterProtocol(SCTianShen3v3PKFinishInfo, "OnSCTianShen3v3PKFinishInfo")  							-- 结算信息
	self:RegisterProtocol(SCTianShen3v3GetBuff, "OnSCTianShen3v3GetBuff")  										-- 捡取buff返回
	self:RegisterProtocol(SCTianShenSmallMapInfo, "OnSCTianShenSmallMapInfo")  									-- 小地图角色信息
	self:RegisterProtocol(SCTianShen3v3SeasonRank, "OnSCTianShen3v3SeasonRank")  								-- 赛季排名信息
end

-- 操作（对应枚举TianShen3V3OperaType）
function TianShen3v3WGCtrl:SendCSTianShen3V3Opera(opera_type, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianShen3V3Opera)
	protocol.opera_type = opera_type or 0
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

-- 请求匹配
function TianShen3v3WGCtrl:SendMatch()
	OperateFrequency.Operate(function() self:SendCSTianShen3V3Opera(TianShen3V3OperaType.StartMatch) end, "tianshen_3v3_match", 1.1)
	
end

-- 请求取消匹配
function TianShen3v3WGCtrl:SendCancelMatch()
	OperateFrequency.Operate(function() self:SendCSTianShen3V3Opera(TianShen3V3OperaType.CancelMatch) end, "tianshen_3v3_match", 1.1)
end

-- 操作（对应枚举TianShen3V3OperaType2）
function TianShen3v3WGCtrl:SendCSTianShen3V3Opera2(opera_type, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianShen3V3Opera2)
	protocol.opera_type = opera_type or 0
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

-- 请求选择天神
function TianShen3v3WGCtrl:SendSelectTianShen(tianshen_index)
	self:SendCSTianShen3V3Opera2(TianShen3V3OperaType2.SelectTianShen, tianshen_index)
end

-- 领取参与奖励
function TianShen3v3WGCtrl:SendFetchJoinReward(index)
	self:SendCSTianShen3V3Opera2(TianShen3V3OperaType2.FetchMatchReward, index)
end

-- 请求主角信息
function TianShen3v3WGCtrl:SendRequestSelfInfo()
	self:SendCSTianShen3V3Opera2(TianShen3V3OperaType2.GetSelfInfo)
end

-- 请求获取赛季排名信息
function TianShen3v3WGCtrl:SendRequestSeasonRank()
	self:SendCSTianShen3V3Opera2(TianShen3V3OperaType2.RequestSeasonRank)
end

-- 匹配状态下发
function TianShen3v3WGCtrl:OnSCTianShen3v3MatchingInfoNotifyReason(protocol)
	self.data:SetMatchStateInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.TianShen3v3PrepareSceneView)
end

-- 匹配成功信息
function TianShen3v3WGCtrl:OnSCTianShen3v3MatchingInfo(protocol)
	self.data:SetMatchReadyInfo(protocol)
	ViewManager.Instance:Open(GuideModuleName.TianShen3v3ReadyView)
	self:FlushSceneBlock()
	RoleWGData.Instance:SetAttr("cross3v3_side", protocol.my_side)
	Scene.Instance:GetMainRole():ReloadUITianShen3v3Side()
end

-- 主角死亡通知
function TianShen3v3WGCtrl:OnSCTianShen3v3DeadInfo(protocol)
	self.data:SetMainRoleReliveInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.TianShen3v3ReliveView)
end

-- 战斗场景角色实时信息
function TianShen3v3WGCtrl:OnSCTianShen3v3AllRoleInfo(protocol)
	self.data:SetSceneRoleInfoList(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.TianShen3v3SceneView, nil, "flush_role_list")
end

-- 战斗场景实时信息
function TianShen3v3WGCtrl:OnSCTianShen3v3PKSceneInfo(protocol)
	self.data:SetSceneInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.TianShen3v3SceneView, nil, "scene_info")
	if Scene.Instance:GetSceneType() == SceneType.TianShen3v3 then
		Scene.Instance:GetSceneLogic():FlushPointGather()
	end
end

-- 天神3v3主角信息
function TianShen3v3WGCtrl:OnSCTianShen3v3PersonData(protocol)
	local last_surplus_can_get_score_times = self.data:GetSurplusCanGetScoreTimes()
	self.data:SetTianShen3v3MainRoleInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.TianShen3v3View)
	ViewManager.Instance:FlushView(GuideModuleName.TianShen3v3PrepareSceneView)
	ViewManager.Instance:FlushView(GuideModuleName.TianShen3v3EndView)
	RemindManager.Instance:Fire(RemindName.TianShen3v3JoinRewardRemind)
	local cur_surplus_can_get_score_times = self.data:GetSurplusCanGetScoreTimes()
	if last_surplus_can_get_score_times > cur_surplus_can_get_score_times and cur_surplus_can_get_score_times == 0 then
		self.data:SetNeedOpenCanGetScoreTipsFlag(true)
	end
end

-- 结算信息
function TianShen3v3WGCtrl:OnSCTianShen3v3PKFinishInfo(protocol)
	self.end_view:SetData(protocol.finish_info_list, protocol.main_role_add_score, protocol.kick_role_timestamp, protocol.win_side)
	ViewManager.Instance:FlushView(GuideModuleName.TianShen3v3SceneView, nil, "show_end_label", {side = protocol.win_side})
	GlobalTimerQuest:AddDelayTimer(function() ViewManager.Instance:Open(GuideModuleName.TianShen3v3EndView) end, 2) 
end

-- 捡取buff返回
function TianShen3v3WGCtrl:OnSCTianShen3v3GetBuff(protocol)
	self.data:AddBuffInfo(protocol)
	local buff_cfg = self.data:GetBuffCfg(protocol.buff_type)
	if buff_cfg then
		if buff_cfg.show_in_buff_list == 1 then
			ViewManager.Instance:FlushView(GuideModuleName.TianShen3v3SceneView, nil, "fly_to_buff_list")
		elseif buff_cfg.buff_type == TS3V3_BUFF_TYPE.ADD_SCORE then
			ViewManager.Instance:FlushView(GuideModuleName.TianShen3v3SceneView, nil, "fly_to_score", {side = self.data:GetMySide()})
		elseif buff_cfg.buff_type == TS3V3_BUFF_TYPE.SUB_SCORE then
			ViewManager.Instance:FlushView(GuideModuleName.TianShen3v3SceneView, nil, "fly_to_score", {side = self.data:GetOtherSide()})
		elseif buff_cfg.buff_type == TS3V3_BUFF_TYPE.HP then
			ViewManager.Instance:FlushView(GuideModuleName.TianShen3v3SceneView, nil, "fly_to_hp_bar")
		end
		TipWGCtrl.Instance:ShowSystemMsg(buff_cfg.buff_desc)
	end
end

-- 小地图信息
function TianShen3v3WGCtrl:OnSCTianShenSmallMapInfo(protocol)
	self.data:SetMapInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.TianShen3v3SceneView, nil, "map")
end

-- 赛季排名信息
function TianShen3v3WGCtrl:OnSCTianShen3v3SeasonRank(protocol)
	self.data:SetSeasonRankInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.TianShen3v3SeasonRankView)
end

-- 领取参与奖励成功返回
function TianShen3v3WGCtrl:OnJoinFetchedResult(seq)
	print_log("#FetchMatchReward#", seq)
	ViewManager.Instance:FlushView(GuideModuleName.TianShen3v3View, nil, "join_reward_anim", {seq = seq})
end

-- 前往占领点
function TianShen3v3WGCtrl:MoveToOccupyPoint(point_index)
	TianShen3v3WGData.Instance:SetIsAutoFight(false)
	GuajiWGCtrl.Instance:ClearAllOperate()
	GuajiWGCtrl.Instance:StopGuaji(nil, true)
	local _, x, y = self.data:GetPointPos(point_index)
	GuajiWGCtrl.Instance:MoveToPos(self.data:GetOtherCfg().pk_scene_id, x, y, 2)
	MoveCache.SetEndType(MoveEndType.Auto)
end

function TianShen3v3WGCtrl:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.TIANSHEN_3V3 then
		RemindManager.Instance:Fire(RemindName.TianShen3v3BtnRemind)
	end
end

-- 刷新天神3v3阻挡区域，不允许进入对方安全区
function TianShen3v3WGCtrl:FlushSceneBlock()
	-- 暂时屏蔽
	-- local scene_logic = Scene.Instance:GetSceneLogic()
	-- if scene_logic and Scene.Instance:GetSceneType() == SceneType.TianShen3v3 then
	-- 	scene_logic:RemoveSceneBlock()
	-- 	scene_logic:SetSceneBlock(TianShen3v3WGData.Instance:GetOtherCfg().pk_scene_id, TianShen3v3WGData.Instance:GetCircularBlock())
	-- end
end