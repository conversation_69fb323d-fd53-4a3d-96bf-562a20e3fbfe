-- D-渡劫.xls
local item_table={
[1]={item_id=26191,num=1,is_bind=1},
[2]={item_id=30805,num=1,is_bind=1},
[3]={item_id=30447,num=5,is_bind=1},
[4]={item_id=30447,num=10,is_bind=1},
[5]={item_id=39987,num=1,is_bind=1},
[6]={item_id=39988,num=1,is_bind=1},
[7]={item_id=39472,num=1,is_bind=1},
[8]={item_id=39478,num=1,is_bind=1},
[9]={item_id=22007,num=1,is_bind=1},
[10]={item_id=26195,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
level={
{open_func_desc="解锁渡劫技·金身",open_icon_type=1,},
{level=1,zhuanzhi_limit=1,fail_damage_time=60,type=1,vitality=100,vitality_limit=1000,attr_value1=35295,attr_value2=3530,attr_value3=1765,attr_value4=1177,attr_value5=100,open_func_desc="解锁渡劫技·赤心",open_icon_type=2,},
{level=2,zhuanzhi_limit=2,fail_damage_time=120,type=2,vitality=500,vitality_limit=5000,attr_value1=70589,attr_value2=7059,attr_value3=3530,attr_value4=2353,attr_value5=200,open_func_desc="解锁渡劫技·无垢",open_icon_type=3,},
{level=3,zhuanzhi_limit=3,fail_damage_time=180,type=3,vitality=1500,vitality_limit=15000,attr_value1=141177,attr_value2=14118,attr_value3=7059,attr_value4=4706,attr_value5=300,},
{level=4,zhuanzhi_limit=4,fail_damage_time=240,type=4,vitality=5000,vitality_limit=50000,attr_value1=247059,attr_value2=24706,attr_value3=12353,attr_value4=8236,attr_value5=400,},
{level=5,zhuanzhi_limit=5,fail_damage_time=300,type=5,vitality=10000,vitality_limit=100000,attr_value1=388236,attr_value2=38824,attr_value3=19412,attr_value4=12942,attr_value5=500,},
{level=6,zhuanzhi_limit=6,fail_damage_time=360,type=6,vitality=20000,vitality_limit=200000,attr_value1=564706,attr_value2=56471,attr_value3=28236,attr_value4=18824,attr_value5=600,},
{level=7,zhuanzhi_limit=7,fail_damage_time=420,type=7,vitality=40000,vitality_limit=400000,attr_value1=776471,attr_value2=77648,attr_value3=38824,attr_value4=25883,attr_value5=700,},
{level=8,zhuanzhi_limit=8,fail_damage_time=480,type=8,vitality=80000,vitality_limit=800000,attr_value1=1023530,attr_value2=102353,attr_value3=51177,attr_value4=34118,attr_value5=800,},
{level=9,zhuanzhi_limit=9,fail_damage_time=540,type=9,vitality=160000,vitality_limit=1600000,attr_value1=1305883,attr_value2=130589,attr_value3=65295,attr_value4=43530,attr_value5=900,},
{level=10,zhuanzhi_limit=10,fail_damage_time=600,type=10,vitality=300000,vitality_limit=3000000,attr_value1=1623530,attr_value2=162353,attr_value3=81177,attr_value4=54118,attr_value5=1000,},
{level=11,zhuanzhi_limit=11,fail_damage_time=660,type=11,vitality=480000,vitality_limit=4800000,attr_value1=1976471,attr_value2=197648,attr_value3=98824,attr_value4=65883,attr_value5=1100,},
{level=12,zhuanzhi_limit=12,fail_damage_time=720,type=12,vitality=760000,vitality_limit=7600000,attr_value1=2364706,attr_value2=236471,attr_value3=118236,attr_value4=78824,attr_value5=1200,},
{level=13,zhuanzhi_limit=13,fail_damage_time=780,type=13,vitality=1000000,vitality_limit=10000000,attr_value1=2788236,attr_value2=278824,attr_value3=139412,attr_value4=92942,attr_value5=1300,},
{level=14,zhuanzhi_limit=14,fail_damage_time=840,type=14,vitality=1500000,vitality_limit=15000000,attr_value1=3247059,attr_value2=324706,attr_value3=162353,attr_value4=108236,attr_value5=1400,},
{level=15,zhuanzhi_limit=15,fail_damage_time=900,type=15,vitality=2000000,vitality_limit=20000000,attr_value1=3741177,attr_value2=374118,attr_value3=187059,attr_value4=124706,attr_value5=1500,},
{level=16,zhuanzhi_limit=16,fail_damage_time=960,type=16,vitality=2800000,vitality_limit=28000000,attr_value1=4270589,attr_value2=427059,attr_value3=213530,attr_value4=142353,attr_value5=1600,}
},

level_meta_table_map={
},
ordeal={
{ms_range="5000,5000",},
{seq=1,ms_range="1500,2000",},
{seq=2,ms_range="1500,1900",},
{seq=3,ms_range="1500,1800",},
{seq=4,ms_range="1500,1700",},
{seq=5,ms_range="1500,1600",},
{seq=6,ms_range="1400,1500",},
{seq=7,ms_range="1300,1500",},
{seq=8,ms_range="1200,1500",},
{seq=9,ms_range="1100,1500",},
{seq=10,thunder="1,1000|2,1000|3,0|4,0",},
{seq=11,},
{seq=12,thunder="1,0|2,0|3,1000|4,1000",},
{seq=13,ms_range="1000,1400",},
{seq=14,ms_range="1000,1300",},
{seq=15,ms_range="1000,1200",},
{seq=16,ms_range="900,1100",},
{seq=17,ms_range="800,1000",},
{level=2,},
{level=2,thunder="5,1000|6,1000|7,0|8,0",},
{level=2,thunder="5,1000|6,1000|7,0|8,0",},
{level=2,thunder="5,1000|6,1000|7,0|8,0",},
{level=2,thunder="5,1000|6,1000|7,0|8,0",},
{seq=5,ms_range="1500,1600",},
{seq=6,ms_range="1400,1500",},
{seq=7,ms_range="1300,1500",},
{seq=8,ms_range="1200,1500",},
{level=2,thunder="5,1000|6,1000|7,0|8,0",},
{level=2,thunder="5,1000|6,1000|7,0|8,0",},
{seq=11,},
{level=2,thunder="5,0|6,0|7,1000|8,1000",},
{seq=13,ms_range="1000,1400",},
{seq=14,ms_range="1000,1300",},
{level=2,thunder="5,0|6,0|7,1000|8,1000",},
{level=2,thunder="5,0|6,0|7,1000|8,1000",},
{level=2,thunder="5,0|6,0|7,1000|8,1000",},
{level=3,},
{level=3,thunder="9,1000|10,1000|11,0|12,0",},
{level=3,thunder="9,1000|10,1000|11,0|12,0",},
{level=3,thunder="9,1000|10,1000|11,0|12,0",},
{level=3,thunder="9,1000|10,1000|11,0|12,0",},
{level=3,thunder="9,1000|10,1000|11,0|12,0",},
{seq=6,ms_range="1400,1500",},
{seq=7,ms_range="1300,1500",},
{seq=8,ms_range="1200,1500",},
{level=3,thunder="9,1000|10,1000|11,0|12,0",},
{level=3,thunder="9,1000|10,1000|11,0|12,0",},
{seq=11,},
{level=3,thunder="9,0|10,0|11,1000|12,1000",},
{seq=13,ms_range="1000,1400",},
{seq=14,ms_range="1000,1300",},
{seq=15,ms_range="1000,1200",},
{seq=16,ms_range="900,1100",},
{seq=17,ms_range="800,1000",},
{level=4,},
{level=4,thunder="13,1000|14,1000|15,0|16,0",},
{level=4,thunder="13,1000|14,1000|15,0|16,0",},
{level=4,thunder="13,1000|14,1000|15,0|16,0",},
{level=4,thunder="13,1000|14,1000|15,0|16,0",},
{level=4,thunder="13,1000|14,1000|15,0|16,0",},
{level=4,thunder="13,1000|14,1000|15,0|16,0",},
{level=4,thunder="13,1000|14,1000|15,0|16,0",},
{level=4,thunder="13,1000|14,1000|15,0|16,0",},
{level=4,thunder="13,1000|14,1000|15,0|16,0",},
{level=4,thunder="13,1000|14,1000|15,0|16,0",},
{seq=11,},
{level=4,thunder="13,0|14,0|15,1000|16,1000",},
{level=4,thunder="13,0|14,0|15,1000|16,1000",},
{level=4,thunder="13,0|14,0|15,1000|16,1000",},
{level=4,thunder="13,0|14,0|15,1000|16,1000",},
{level=4,thunder="13,0|14,0|15,1000|16,1000",},
{level=4,thunder="13,0|14,0|15,1000|16,1000",},
{level=5,},
{level=5,thunder="17,1000|18,1000|19,0|20,0",},
{level=5,thunder="17,1000|18,1000|19,0|20,0",},
{level=5,thunder="17,1000|18,1000|19,0|20,0",},
{level=5,thunder="17,1000|18,1000|19,0|20,0",},
{level=5,thunder="17,1000|18,1000|19,0|20,0",},
{level=5,thunder="17,1000|18,1000|19,0|20,0",},
{level=5,thunder="17,1000|18,1000|19,0|20,0",},
{level=5,thunder="17,1000|18,1000|19,0|20,0",},
{level=5,thunder="17,1000|18,1000|19,0|20,0",},
{level=5,thunder="17,1000|18,1000|19,0|20,0",},
{seq=11,},
{level=5,thunder="17,0|18,0|19,1000|20,1000",},
{level=5,thunder="17,0|18,0|19,1000|20,1000",},
{level=5,thunder="17,0|18,0|19,1000|20,1000",},
{level=5,thunder="17,0|18,0|19,1000|20,1000",},
{level=5,thunder="17,0|18,0|19,1000|20,1000",},
{level=5,thunder="17,0|18,0|19,1000|20,1000",},
{level=6,},
{level=6,thunder="21,1000|22,1000|23,0|24,0",},
{level=6,thunder="21,1000|22,1000|23,0|24,0",},
{level=6,thunder="21,1000|22,1000|23,0|24,0",},
{level=6,thunder="21,1000|22,1000|23,0|24,0",},
{level=6,thunder="21,1000|22,1000|23,0|24,0",},
{level=6,thunder="21,1000|22,1000|23,0|24,0",},
{level=6,thunder="21,1000|22,1000|23,0|24,0",},
{level=6,thunder="21,1000|22,1000|23,0|24,0",},
{level=6,thunder="21,1000|22,1000|23,0|24,0",},
{level=6,thunder="21,1000|22,1000|23,0|24,0",},
{level=6,thunder="21,0|22,0|23,1000|24,1000",},
{seq=12,},
{level=6,thunder="21,0|22,0|23,1000|24,1000",},
{level=6,thunder="21,0|22,0|23,1000|24,1000",},
{level=6,thunder="21,0|22,0|23,1000|24,1000",},
{level=6,thunder="21,0|22,0|23,1000|24,1000",},
{level=6,thunder="21,0|22,0|23,1000|24,1000",},
{level=7,},
{level=7,thunder="25,1000|26,1000|27,0|28,0",},
{level=7,thunder="25,1000|26,1000|27,0|28,0",},
{level=7,thunder="25,1000|26,1000|27,0|28,0",},
{level=7,thunder="25,1000|26,1000|27,0|28,0",},
{level=7,thunder="25,1000|26,1000|27,0|28,0",},
{level=7,thunder="25,1000|26,1000|27,0|28,0",},
{level=7,thunder="25,1000|26,1000|27,0|28,0",},
{level=7,thunder="25,1000|26,1000|27,0|28,0",},
{level=7,thunder="25,1000|26,1000|27,0|28,0",},
{level=7,thunder="25,1000|26,1000|27,0|28,0",},
{seq=11,thunder="25,0|26,0|27,1000|28,1000",},
{seq=12,},
{level=7,thunder="25,0|26,0|27,1000|28,1000",},
{level=7,thunder="25,0|26,0|27,1000|28,1000",},
{level=7,thunder="25,0|26,0|27,1000|28,1000",},
{level=7,thunder="25,0|26,0|27,1000|28,1000",},
{level=7,thunder="25,0|26,0|27,1000|28,1000",},
{level=8,},
{level=8,thunder="29,1000|30,1000|31,0|32,0",},
{level=8,thunder="29,1000|30,1000|31,0|32,0",},
{level=8,thunder="29,1000|30,1000|31,0|32,0",},
{level=8,thunder="29,1000|30,1000|31,0|32,0",},
{level=8,thunder="29,1000|30,1000|31,0|32,0",},
{level=8,thunder="29,1000|30,1000|31,0|32,0",},
{level=8,thunder="29,1000|30,1000|31,0|32,0",},
{level=8,thunder="29,1000|30,1000|31,0|32,0",},
{level=8,thunder="29,1000|30,1000|31,0|32,0",},
{level=8,thunder="29,1000|30,1000|31,0|32,0",},
{seq=11,thunder="29,0|30,0|31,1000|32,1000",},
{seq=12,},
{level=8,thunder="29,0|30,0|31,1000|32,1000",},
{level=8,thunder="29,0|30,0|31,1000|32,1000",},
{level=8,thunder="29,0|30,0|31,1000|32,1000",},
{level=8,thunder="29,0|30,0|31,1000|32,1000",},
{level=8,thunder="29,0|30,0|31,1000|32,1000",},
{level=9,},
{level=9,thunder="33,1000|34,1000|35,0|36,0",},
{level=9,thunder="33,1000|34,1000|35,0|36,0",},
{level=9,thunder="33,1000|34,1000|35,0|36,0",},
{level=9,thunder="33,1000|34,1000|35,0|36,0",},
{level=9,thunder="33,1000|34,1000|35,0|36,0",},
{level=9,thunder="33,1000|34,1000|35,0|36,0",},
{level=9,thunder="33,1000|34,1000|35,0|36,0",},
{level=9,thunder="33,1000|34,1000|35,0|36,0",},
{level=9,thunder="33,1000|34,1000|35,0|36,0",},
{level=9,thunder="33,1000|34,1000|35,0|36,0",},
{seq=11,},
{level=9,thunder="33,0|34,0|35,1000|36,1000",},
{level=9,thunder="33,0|34,0|35,1000|36,1000",},
{level=9,thunder="33,0|34,0|35,1000|36,1000",},
{level=9,thunder="33,0|34,0|35,1000|36,1000",},
{level=9,thunder="33,0|34,0|35,1000|36,1000",},
{level=9,thunder="33,0|34,0|35,1000|36,1000",},
{level=10,},
{level=10,thunder="37,1000|38,1000|39,0|40,0",},
{level=10,thunder="37,1000|38,1000|39,0|40,0",},
{level=10,thunder="37,1000|38,1000|39,0|40,0",},
{level=10,thunder="37,1000|38,1000|39,0|40,0",},
{level=10,thunder="37,1000|38,1000|39,0|40,0",},
{level=10,thunder="37,1000|38,1000|39,0|40,0",},
{level=10,thunder="37,1000|38,1000|39,0|40,0",},
{level=10,thunder="37,1000|38,1000|39,0|40,0",},
{level=10,thunder="37,1000|38,1000|39,0|40,0",},
{level=10,thunder="37,1000|38,1000|39,0|40,0",},
{seq=11,},
{level=10,thunder="37,0|38,0|39,1000|40,1000",},
{level=10,thunder="37,0|38,0|39,1000|40,1000",},
{level=10,thunder="37,0|38,0|39,1000|40,1000",},
{level=10,thunder="37,0|38,0|39,1000|40,1000",},
{level=10,thunder="37,0|38,0|39,1000|40,1000",},
{level=10,thunder="37,0|38,0|39,1000|40,1000",},
{level=11,},
{level=11,thunder="41,1000|42,1000|43,0|44,0",},
{level=11,thunder="41,1000|42,1000|43,0|44,0",},
{level=11,thunder="41,1000|42,1000|43,0|44,0",},
{level=11,thunder="41,1000|42,1000|43,0|44,0",},
{level=11,thunder="41,1000|42,1000|43,0|44,0",},
{level=11,thunder="41,1000|42,1000|43,0|44,0",},
{level=11,thunder="41,1000|42,1000|43,0|44,0",},
{level=11,thunder="41,1000|42,1000|43,0|44,0",},
{level=11,thunder="41,1000|42,1000|43,0|44,0",},
{level=11,thunder="41,1000|42,1000|43,0|44,0",},
{seq=11,},
{level=11,thunder="41,0|42,0|43,1000|44,1000",},
{level=11,thunder="41,0|42,0|43,1000|44,1000",},
{level=11,thunder="41,0|42,0|43,1000|44,1000",},
{level=11,thunder="41,0|42,0|43,1000|44,1000",},
{level=11,thunder="41,0|42,0|43,1000|44,1000",},
{level=11,thunder="41,0|42,0|43,1000|44,1000",},
{level=12,},
{level=12,thunder="45,1000|46,1000|47,0|48,0",},
{level=12,thunder="45,1000|46,1000|47,0|48,0",},
{level=12,thunder="45,1000|46,1000|47,0|48,0",},
{level=12,thunder="45,1000|46,1000|47,0|48,0",},
{level=12,thunder="45,1000|46,1000|47,0|48,0",},
{level=12,thunder="45,1000|46,1000|47,0|48,0",},
{level=12,thunder="45,1000|46,1000|47,0|48,0",},
{level=12,thunder="45,1000|46,1000|47,0|48,0",},
{level=12,thunder="45,1000|46,1000|47,0|48,0",},
{level=12,thunder="45,1000|46,1000|47,0|48,0",},
{seq=11,},
{level=12,thunder="45,0|46,0|47,1000|48,1000",},
{level=12,thunder="45,0|46,0|47,1000|48,1000",},
{level=12,thunder="45,0|46,0|47,1000|48,1000",},
{level=12,thunder="45,0|46,0|47,1000|48,1000",},
{level=12,thunder="45,0|46,0|47,1000|48,1000",},
{level=12,thunder="45,0|46,0|47,1000|48,1000",},
{level=13,},
{level=13,thunder="49,1000|50,1000|51,0|52,0",},
{level=13,thunder="49,1000|50,1000|51,0|52,0",},
{level=13,thunder="49,1000|50,1000|51,0|52,0",},
{level=13,thunder="49,1000|50,1000|51,0|52,0",},
{level=13,thunder="49,1000|50,1000|51,0|52,0",},
{level=13,thunder="49,1000|50,1000|51,0|52,0",},
{level=13,thunder="49,1000|50,1000|51,0|52,0",},
{level=13,thunder="49,1000|50,1000|51,0|52,0",},
{level=13,thunder="49,1000|50,1000|51,0|52,0",},
{level=13,thunder="49,1000|50,1000|51,0|52,0",},
{seq=11,},
{level=13,thunder="49,0|50,0|51,1000|52,1000",},
{level=13,thunder="49,0|50,0|51,1000|52,1000",},
{level=13,thunder="49,0|50,0|51,1000|52,1000",},
{level=13,thunder="49,0|50,0|51,1000|52,1000",},
{level=13,thunder="49,0|50,0|51,1000|52,1000",},
{level=13,thunder="49,0|50,0|51,1000|52,1000",},
{level=14,},
{level=14,thunder="53,1000|54,1000|55,0|56,0",},
{level=14,thunder="53,1000|54,1000|55,0|56,0",},
{level=14,thunder="53,1000|54,1000|55,0|56,0",},
{level=14,thunder="53,1000|54,1000|55,0|56,0",},
{level=14,thunder="53,1000|54,1000|55,0|56,0",},
{level=14,thunder="53,1000|54,1000|55,0|56,0",},
{level=14,thunder="53,1000|54,1000|55,0|56,0",},
{level=14,thunder="53,1000|54,1000|55,0|56,0",},
{level=14,thunder="53,1000|54,1000|55,0|56,0",},
{level=14,thunder="53,1000|54,1000|55,0|56,0",},
{seq=11,thunder="53,0|54,0|55,1000|56,1000",},
{seq=12,},
{level=14,thunder="53,0|54,0|55,1000|56,1000",},
{level=14,thunder="53,0|54,0|55,1000|56,1000",},
{level=14,thunder="53,0|54,0|55,1000|56,1000",},
{level=14,thunder="53,0|54,0|55,1000|56,1000",},
{level=14,thunder="53,0|54,0|55,1000|56,1000",},
{level=15,},
{level=15,thunder="57,1000|58,1000|59,0|60,0",},
{level=15,thunder="57,1000|58,1000|59,0|60,0",},
{level=15,thunder="57,1000|58,1000|59,0|60,0",},
{level=15,thunder="57,1000|58,1000|59,0|60,0",},
{seq=5,ms_range="1500,1600",},
{seq=6,ms_range="1400,1500",},
{seq=7,ms_range="1300,1500",},
{seq=8,ms_range="1200,1500",},
{level=15,thunder="57,1000|58,1000|59,0|60,0",},
{level=15,thunder="57,1000|58,1000|59,0|60,0",},
{seq=11,},
{level=15,thunder="57,0|58,0|59,1000|60,1000",},
{seq=13,ms_range="1000,1400",},
{seq=14,ms_range="1000,1300",},
{seq=15,ms_range="1000,1200",},
{seq=16,ms_range="900,1100",},
{seq=17,ms_range="800,1000",},
{level=16,},
{level=16,thunder="61,1000|62,1000|63,0|64,0",},
{level=16,thunder="61,1000|62,1000|63,0|64,0",},
{level=16,thunder="61,1000|62,1000|63,0|64,0",},
{level=16,thunder="61,1000|62,1000|63,0|64,0",},
{seq=5,ms_range="1500,1600",},
{seq=6,ms_range="1400,1500",},
{seq=7,ms_range="1300,1500",},
{seq=8,ms_range="1200,1500",},
{level=16,thunder="61,1000|62,1000|63,0|64,0",},
{level=16,thunder="61,1000|62,1000|63,0|64,0",},
{level=16,thunder="61,0|62,0|63,1000|64,1000",},
{seq=12,},
{level=16,thunder="61,0|62,0|63,1000|64,1000",},
{level=16,thunder="61,0|62,0|63,1000|64,1000",},
{level=16,thunder="61,0|62,0|63,1000|64,1000",},
{level=16,thunder="61,0|62,0|63,1000|64,1000",},
{level=16,thunder="61,0|62,0|63,1000|64,1000",}
},

ordeal_meta_table_map={
[73]=1,	-- depth:1
[235]=73,	-- depth:2
[271]=235,	-- depth:3
[91]=271,	-- depth:4
[37]=91,	-- depth:5
[217]=37,	-- depth:6
[109]=217,	-- depth:7
[253]=109,	-- depth:8
[127]=253,	-- depth:9
[199]=127,	-- depth:10
[19]=199,	-- depth:11
[55]=19,	-- depth:12
[163]=55,	-- depth:13
[12]=13,	-- depth:1
[145]=163,	-- depth:14
[181]=145,	-- depth:15
[191]=11,	-- depth:1
[157]=13,	-- depth:1
[175]=13,	-- depth:1
[174]=175,	-- depth:2
[85]=13,	-- depth:1
[173]=11,	-- depth:1
[229]=13,	-- depth:1
[245]=11,	-- depth:1
[246]=245,	-- depth:2
[247]=246,	-- depth:3
[67]=13,	-- depth:1
[66]=67,	-- depth:2
[65]=11,	-- depth:1
[83]=11,	-- depth:1
[228]=229,	-- depth:2
[102]=12,	-- depth:2
[101]=11,	-- depth:1
[103]=102,	-- depth:3
[156]=157,	-- depth:2
[155]=11,	-- depth:1
[211]=13,	-- depth:1
[210]=211,	-- depth:2
[119]=11,	-- depth:1
[120]=119,	-- depth:2
[121]=120,	-- depth:3
[137]=11,	-- depth:1
[138]=137,	-- depth:2
[139]=138,	-- depth:3
[193]=13,	-- depth:1
[192]=193,	-- depth:2
[227]=11,	-- depth:1
[209]=11,	-- depth:1
[84]=85,	-- depth:2
[282]=12,	-- depth:2
[14]=13,	-- depth:1
[15]=13,	-- depth:1
[31]=13,	-- depth:1
[16]=13,	-- depth:1
[17]=13,	-- depth:1
[283]=282,	-- depth:3
[9]=11,	-- depth:1
[18]=13,	-- depth:1
[265]=13,	-- depth:1
[264]=265,	-- depth:2
[8]=11,	-- depth:1
[7]=11,	-- depth:1
[47]=11,	-- depth:1
[49]=13,	-- depth:1
[6]=11,	-- depth:1
[30]=31,	-- depth:2
[29]=11,	-- depth:1
[263]=11,	-- depth:1
[281]=11,	-- depth:1
[5]=11,	-- depth:1
[4]=11,	-- depth:1
[3]=11,	-- depth:1
[2]=11,	-- depth:1
[48]=49,	-- depth:2
[10]=11,	-- depth:1
[202]=4,	-- depth:2
[201]=3,	-- depth:2
[200]=2,	-- depth:2
[260]=263,	-- depth:2
[203]=5,	-- depth:2
[204]=6,	-- depth:2
[205]=7,	-- depth:2
[206]=8,	-- depth:2
[207]=9,	-- depth:2
[285]=15,	-- depth:2
[208]=10,	-- depth:2
[280]=10,	-- depth:2
[284]=14,	-- depth:2
[198]=18,	-- depth:2
[190]=10,	-- depth:2
[196]=16,	-- depth:2
[172]=10,	-- depth:2
[176]=14,	-- depth:2
[177]=15,	-- depth:2
[178]=16,	-- depth:2
[179]=17,	-- depth:2
[180]=18,	-- depth:2
[182]=2,	-- depth:2
[183]=3,	-- depth:2
[197]=17,	-- depth:2
[184]=4,	-- depth:2
[186]=6,	-- depth:2
[187]=7,	-- depth:2
[188]=8,	-- depth:2
[189]=9,	-- depth:2
[279]=281,	-- depth:2
[286]=16,	-- depth:2
[194]=14,	-- depth:2
[195]=15,	-- depth:2
[185]=5,	-- depth:2
[278]=281,	-- depth:2
[216]=18,	-- depth:2
[213]=15,	-- depth:2
[236]=2,	-- depth:2
[237]=3,	-- depth:2
[238]=4,	-- depth:2
[239]=5,	-- depth:2
[240]=6,	-- depth:2
[241]=7,	-- depth:2
[242]=8,	-- depth:2
[243]=9,	-- depth:2
[244]=10,	-- depth:2
[267]=265,	-- depth:2
[266]=265,	-- depth:2
[248]=14,	-- depth:2
[249]=15,	-- depth:2
[250]=16,	-- depth:2
[251]=17,	-- depth:2
[252]=18,	-- depth:2
[254]=2,	-- depth:2
[255]=3,	-- depth:2
[256]=4,	-- depth:2
[257]=5,	-- depth:2
[258]=263,	-- depth:2
[262]=10,	-- depth:2
[259]=263,	-- depth:2
[268]=265,	-- depth:2
[212]=14,	-- depth:2
[171]=9,	-- depth:2
[270]=265,	-- depth:2
[214]=16,	-- depth:2
[215]=17,	-- depth:2
[261]=263,	-- depth:2
[218]=2,	-- depth:2
[219]=3,	-- depth:2
[220]=4,	-- depth:2
[221]=5,	-- depth:2
[277]=281,	-- depth:2
[276]=281,	-- depth:2
[222]=6,	-- depth:2
[223]=7,	-- depth:2
[224]=8,	-- depth:2
[225]=9,	-- depth:2
[226]=10,	-- depth:2
[275]=5,	-- depth:2
[274]=4,	-- depth:2
[273]=3,	-- depth:2
[230]=14,	-- depth:2
[272]=2,	-- depth:2
[231]=15,	-- depth:2
[232]=16,	-- depth:2
[233]=17,	-- depth:2
[234]=18,	-- depth:2
[269]=265,	-- depth:2
[170]=8,	-- depth:2
[144]=18,	-- depth:2
[168]=6,	-- depth:2
[58]=4,	-- depth:2
[59]=5,	-- depth:2
[60]=6,	-- depth:2
[61]=7,	-- depth:2
[62]=8,	-- depth:2
[63]=9,	-- depth:2
[64]=10,	-- depth:2
[68]=14,	-- depth:2
[69]=15,	-- depth:2
[70]=16,	-- depth:2
[71]=17,	-- depth:2
[72]=18,	-- depth:2
[57]=3,	-- depth:2
[74]=2,	-- depth:2
[76]=4,	-- depth:2
[77]=5,	-- depth:2
[78]=6,	-- depth:2
[79]=7,	-- depth:2
[80]=8,	-- depth:2
[81]=9,	-- depth:2
[82]=10,	-- depth:2
[86]=14,	-- depth:2
[87]=15,	-- depth:2
[88]=16,	-- depth:2
[89]=17,	-- depth:2
[90]=18,	-- depth:2
[75]=3,	-- depth:2
[92]=2,	-- depth:2
[56]=2,	-- depth:2
[53]=49,	-- depth:2
[20]=2,	-- depth:2
[21]=3,	-- depth:2
[22]=4,	-- depth:2
[23]=5,	-- depth:2
[24]=29,	-- depth:2
[25]=29,	-- depth:2
[26]=29,	-- depth:2
[27]=29,	-- depth:2
[28]=10,	-- depth:2
[32]=31,	-- depth:2
[33]=31,	-- depth:2
[34]=16,	-- depth:2
[54]=49,	-- depth:2
[35]=17,	-- depth:2
[38]=2,	-- depth:2
[39]=3,	-- depth:2
[40]=4,	-- depth:2
[41]=5,	-- depth:2
[42]=6,	-- depth:2
[43]=47,	-- depth:2
[44]=47,	-- depth:2
[45]=47,	-- depth:2
[46]=10,	-- depth:2
[50]=49,	-- depth:2
[51]=49,	-- depth:2
[52]=49,	-- depth:2
[36]=18,	-- depth:2
[169]=7,	-- depth:2
[93]=3,	-- depth:2
[95]=5,	-- depth:2
[134]=8,	-- depth:2
[135]=9,	-- depth:2
[136]=10,	-- depth:2
[140]=14,	-- depth:2
[141]=15,	-- depth:2
[142]=16,	-- depth:2
[143]=17,	-- depth:2
[287]=17,	-- depth:2
[146]=2,	-- depth:2
[147]=3,	-- depth:2
[148]=4,	-- depth:2
[149]=5,	-- depth:2
[133]=7,	-- depth:2
[150]=6,	-- depth:2
[152]=8,	-- depth:2
[153]=9,	-- depth:2
[154]=10,	-- depth:2
[158]=14,	-- depth:2
[159]=15,	-- depth:2
[160]=16,	-- depth:2
[161]=17,	-- depth:2
[162]=18,	-- depth:2
[164]=2,	-- depth:2
[165]=3,	-- depth:2
[166]=4,	-- depth:2
[167]=5,	-- depth:2
[151]=7,	-- depth:2
[94]=4,	-- depth:2
[132]=6,	-- depth:2
[130]=4,	-- depth:2
[96]=6,	-- depth:2
[97]=7,	-- depth:2
[98]=8,	-- depth:2
[99]=9,	-- depth:2
[100]=10,	-- depth:2
[104]=14,	-- depth:2
[105]=15,	-- depth:2
[106]=16,	-- depth:2
[107]=17,	-- depth:2
[108]=18,	-- depth:2
[110]=2,	-- depth:2
[111]=3,	-- depth:2
[131]=5,	-- depth:2
[112]=4,	-- depth:2
[114]=6,	-- depth:2
[115]=7,	-- depth:2
[116]=8,	-- depth:2
[117]=9,	-- depth:2
[118]=10,	-- depth:2
[122]=14,	-- depth:2
[123]=15,	-- depth:2
[124]=16,	-- depth:2
[125]=17,	-- depth:2
[126]=18,	-- depth:2
[128]=2,	-- depth:2
[129]=3,	-- depth:2
[113]=5,	-- depth:2
[288]=18,	-- depth:2
},
thunder_name={
{},
{type=2,thunder_txt="<color=#ff9292>九霄雷索</color>即将落下，造成<color=#fff8bb>大量伤害</color>。",},
{type=3,thunder_txt="<color=#ff9292>苍渊雷息</color>即将落下，造成<color=#fff8bb>大量伤害</color>。",}
},

thunder_name_meta_table_map={
},
thunder={
{},
{seq=2,},
{seq=3,hurt="6060,6667",},
{seq=4,type=3,},
{seq=5,hurt="4481,5378",},
{seq=6,},
{seq=7,hurt="6722,8067",},
{seq=8,type=3,},
{seq=9,hurt="9025,11734",},
{seq=10,},
{seq=11,hurt="13538,17600",},
{seq=12,type=3,},
{seq=13,hurt="13619,19067",},
{seq=14,},
{seq=15,hurt="20428,28600",},
{seq=16,type=3,},
{seq=17,hurt="18251,27378",},
{seq=18,},
{seq=19,hurt="27377,41067",},
{seq=20,type=3,},
{seq=21,},
{seq=22,hurt="27500,44000",},
{seq=23,hurt="41250,66000",},
{seq=24,type=3,},
{seq=25,hurt="41411,70400",},
{seq=26,},
{seq=27,hurt="62117,105600",},
{seq=28,type=3,},
{seq=29,hurt="69259,124667",},
{seq=30,},
{seq=31,hurt="103888,187000",},
{seq=32,hurt="103888,187000",},
{seq=33,hurt="115789,220000",},
{seq=34,},
{seq=35,hurt="173684,330000",},
{seq=36,type=3,},
{seq=37,hurt="185777,371556",},
{seq=38,},
{seq=39,hurt="278666,557334",},
{seq=40,type=3,},
{seq=41,hurt="270052,567112",},
{seq=42,},
{seq=43,hurt="405079,850667",},
{seq=44,hurt="405079,850667",},
{seq=45,},
{seq=46,hurt="378000,831601",},
{seq=47,type=2,show_ms=1000,hurt="567000,1247400",},
{seq=48,type=3,},
{seq=49,},
{seq=50,hurt="514396,1183112",},
{seq=51,hurt="771594,1774667",},
{seq=52,type=3,},
{seq=53,hurt="674666,1619200",},
{seq=54,},
{seq=55,hurt="1012000,2428800",},
{seq=56,hurt="1012000,2428800",},
{seq=57,hurt="830720,2076801",},
{seq=58,},
{seq=59,hurt="1246080,3115200",},
{seq=60,type=3,},
{seq=61,},
{seq=62,hurt="1048290,2725556",},
{seq=63,hurt="1572435,4088334",},
{seq=64,type=3,}
},

thunder_meta_table_map={
[26]=25,	-- depth:1
[49]=50,	-- depth:1
[30]=29,	-- depth:1
[34]=33,	-- depth:1
[45]=46,	-- depth:1
[38]=37,	-- depth:1
[54]=53,	-- depth:1
[21]=22,	-- depth:1
[61]=62,	-- depth:1
[6]=5,	-- depth:1
[18]=17,	-- depth:1
[10]=9,	-- depth:1
[42]=41,	-- depth:1
[14]=13,	-- depth:1
[58]=57,	-- depth:1
[59]=47,	-- depth:1
[60]=59,	-- depth:2
[51]=47,	-- depth:1
[52]=51,	-- depth:2
[56]=52,	-- depth:3
[55]=47,	-- depth:1
[44]=52,	-- depth:3
[43]=47,	-- depth:1
[48]=47,	-- depth:1
[32]=48,	-- depth:2
[39]=47,	-- depth:1
[3]=47,	-- depth:1
[4]=3,	-- depth:2
[7]=47,	-- depth:1
[8]=7,	-- depth:2
[11]=47,	-- depth:1
[12]=11,	-- depth:2
[15]=47,	-- depth:1
[16]=15,	-- depth:2
[40]=39,	-- depth:2
[19]=47,	-- depth:1
[23]=47,	-- depth:1
[24]=23,	-- depth:2
[27]=47,	-- depth:1
[28]=27,	-- depth:2
[31]=47,	-- depth:1
[63]=47,	-- depth:1
[35]=47,	-- depth:1
[36]=35,	-- depth:2
[20]=19,	-- depth:2
[64]=63,	-- depth:2
},
skill={
{param2=100,param3=2000,},
{seq=1,type=2,param1=1100,},
{seq=2,cd_time=20,type=3,param1=2000,},
{level=2,param2=1663,},
{level=2,param1=2200,},
{level=2,},
{level=3,param2=7628,},
{level=3,param1=3300,},
{level=3,},
{level=4,param2=17894,},
{level=4,param1=4400,},
{level=4,},
{level=5,param2=32463,},
{level=5,param1=6600,},
{level=5,},
{level=6,param2=61538,},
{level=6,param1=9900,},
{level=6,},
{level=7,param2=111648,},
{level=7,param1=16500,},
{level=7,},
{level=8,param2=87413,},
{level=8,param1=27500,},
{level=8,},
{level=9,param2=167200,},
{level=9,param1=44000,},
{level=9,},
{level=10,param2=301938,},
{level=10,param1=63800,},
{level=10,},
{level=11,param2=487716,},
{level=11,param1=89100,},
{level=11,},
{level=12,param2=750816,},
{level=12,param1=121000,},
{level=12,},
{level=13,param2=1114276,},
{level=13,param1=158400,},
{level=13,},
{level=14,param2=1582592,},
{level=14,param1=194700,},
{level=14,},
{level=15,param2=2097568,},
{level=15,param1=245300,},
{level=15,},
{level=16,param2=2834578,},
{level=16,param1=301400,},
{level=16,}
},

skill_meta_table_map={
[34]=1,	-- depth:1
[31]=1,	-- depth:1
[28]=1,	-- depth:1
[40]=1,	-- depth:1
[25]=1,	-- depth:1
[22]=1,	-- depth:1
[19]=1,	-- depth:1
[16]=1,	-- depth:1
[43]=1,	-- depth:1
[13]=1,	-- depth:1
[37]=1,	-- depth:1
[10]=1,	-- depth:1
[4]=1,	-- depth:1
[7]=1,	-- depth:1
[46]=1,	-- depth:1
[11]=2,	-- depth:1
[38]=2,	-- depth:1
[32]=2,	-- depth:1
[29]=2,	-- depth:1
[5]=2,	-- depth:1
[26]=2,	-- depth:1
[47]=2,	-- depth:1
[23]=2,	-- depth:1
[41]=2,	-- depth:1
[20]=2,	-- depth:1
[8]=2,	-- depth:1
[35]=2,	-- depth:1
[17]=2,	-- depth:1
[14]=2,	-- depth:1
[44]=2,	-- depth:1
[39]=3,	-- depth:1
[45]=39,	-- depth:2
[42]=45,	-- depth:3
[24]=42,	-- depth:4
[33]=24,	-- depth:5
[30]=33,	-- depth:6
[27]=30,	-- depth:7
[21]=27,	-- depth:8
[18]=21,	-- depth:9
[15]=18,	-- depth:10
[12]=15,	-- depth:11
[9]=12,	-- depth:12
[6]=9,	-- depth:13
[36]=6,	-- depth:14
[48]=36,	-- depth:15
},
box={
{},
{seq=1,weight=10,gather_reward_item={[0]=item_table[1]},},
{seq=2,weight=20,gather_reward_item={[0]=item_table[2]},},
{seq=3,weight=200,gather_reward_item={[0]=item_table[3]},},
{seq=4,weight=150,gather_reward_item={[0]=item_table[4]},},
{seq=5,weight=500,gather_reward_item={[0]=item_table[5]},},
{seq=6,gather_reward_item={[0]=item_table[6]},},
{seq=7,gather_reward_item={[0]=item_table[7]},},
{seq=8,weight=80,gather_reward_item={[0]=item_table[8]},}
},

box_meta_table_map={
},
vitality={
{},
{zhuanzhi_level=1,vitality=110000,},
{zhuanzhi_level=2,vitality=220000,},
{zhuanzhi_level=3,vitality=330000,},
{zhuanzhi_level=4,vitality=440000,},
{zhuanzhi_level=5,vitality=660000,},
{zhuanzhi_level=6,vitality=990000,},
{zhuanzhi_level=7,vitality=1650000,},
{zhuanzhi_level=8,vitality=2750000,},
{zhuanzhi_level=9,vitality=4400000,},
{zhuanzhi_level=10,vitality=6380000,},
{zhuanzhi_level=11,vitality=8910000,},
{zhuanzhi_level=12,vitality=12100000,},
{zhuanzhi_level=13,vitality=15840000,},
{zhuanzhi_level=14,vitality=19470000,},
{zhuanzhi_level=15,vitality=24530000,},
{zhuanzhi_level=16,vitality=30140000,}
},

vitality_meta_table_map={
},
strength={
{},
{jiengjie_level=1,},
{jiengjie_level=2,},
{jiengjie_level=3,},
{jiengjie_level=4,},
{jiengjie_level=5,},
{jiengjie_level=6,},
{jiengjie_level=7,},
{jiengjie_level=8,},
{jiengjie_level=9,},
{jiengjie_level=10,},
{jiengjie_level=11,},
{jiengjie_level=12,},
{jiengjie_level=13,},
{jiengjie_level=14,},
{jiengjie_level=15,},
{jiengjie_level=16,},
{jiengjie_level=17,},
{jiengjie_level=18,},
{jiengjie_level=19,},
{jiengjie_level=20,},
{jiengjie_level=21,},
{jiengjie_level=22,},
{jiengjie_level=23,},
{jiengjie_level=24,},
{jiengjie_level=25,},
{jiengjie_level=26,},
{jiengjie_level=27,},
{jiengjie_level=28,},
{jiengjie_level=29,},
{jiengjie_level=30,},
{jiengjie_level=31,},
{jiengjie_level=32,},
{jiengjie_level=33,},
{jiengjie_level=34,},
{jiengjie_level=35,},
{jiengjie_level=36,},
{jiengjie_level=37,},
{jiengjie_level=38,},
{jiengjie_level=39,},
{jiengjie_level=40,},
{jiengjie_level=41,},
{jiengjie_level=42,},
{jiengjie_level=43,},
{jiengjie_level=44,},
{jiengjie_level=45,},
{jiengjie_level=46,},
{jiengjie_level=47,},
{jiengjie_level=48,},
{jiengjie_level=49,},
{jiengjie_level=50,},
{jiengjie_level=51,},
{jiengjie_level=52,},
{jiengjie_level=53,},
{jiengjie_level=54,},
{jiengjie_level=55,},
{jiengjie_level=56,},
{jiengjie_level=57,},
{jiengjie_level=58,},
{jiengjie_level=59,},
{jiengjie_level=60,},
{jiengjie_level=61,},
{jiengjie_level=62,},
{jiengjie_level=63,},
{jiengjie_level=64,},
{jiengjie_level=65,},
{jiengjie_level=66,},
{jiengjie_level=67,},
{jiengjie_level=68,},
{jiengjie_level=69,},
{jiengjie_level=70,},
{jiengjie_level=71,},
{jiengjie_level=72,},
{jiengjie_level=73,},
{jiengjie_level=74,},
{jiengjie_level=75,},
{jiengjie_level=76,},
{jiengjie_level=77,},
{jiengjie_level=78,},
{jiengjie_level=79,},
{jiengjie_level=80,},
{jiengjie_level=81,},
{jiengjie_level=82,},
{jiengjie_level=83,},
{jiengjie_level=84,},
{jiengjie_level=85,},
{jiengjie_level=86,},
{jiengjie_level=87,},
{jiengjie_level=88,},
{jiengjie_level=89,},
{jiengjie_level=90,},
{jiengjie_level=91,},
{jiengjie_level=92,},
{jiengjie_level=93,},
{jiengjie_level=94,},
{jiengjie_level=95,},
{jiengjie_level=96,},
{jiengjie_level=97,},
{jiengjie_level=98,},
{jiengjie_level=99,},
{jiengjie_level=100,},
{jiengjie_level=101,},
{jiengjie_level=102,},
{jiengjie_level=103,},
{jiengjie_level=104,},
{jiengjie_level=105,},
{jiengjie_level=106,},
{jiengjie_level=107,},
{jiengjie_level=108,},
{jiengjie_level=109,},
{jiengjie_level=110,},
{jiengjie_level=111,},
{jiengjie_level=112,},
{jiengjie_level=113,},
{jiengjie_level=114,},
{jiengjie_level=115,},
{jiengjie_level=116,},
{jiengjie_level=117,},
{jiengjie_level=118,},
{jiengjie_level=119,},
{jiengjie_level=120,},
{jiengjie_level=121,},
{jiengjie_level=122,},
{jiengjie_level=123,},
{jiengjie_level=124,},
{jiengjie_level=125,},
{jiengjie_level=126,},
{jiengjie_level=127,},
{jiengjie_level=128,},
{jiengjie_level=129,},
{jiengjie_level=130,},
{jiengjie_level=131,},
{jiengjie_level=132,},
{jiengjie_level=133,},
{jiengjie_level=134,},
{jiengjie_level=135,},
{jiengjie_level=136,},
{jiengjie_level=137,},
{jiengjie_level=138,},
{jiengjie_level=139,},
{jiengjie_level=140,},
{jiengjie_level=141,},
{jiengjie_level=142,},
{jiengjie_level=143,},
{jiengjie_level=144,},
{jiengjie_level=145,},
{jiengjie_level=146,},
{jiengjie_level=147,},
{jiengjie_level=148,},
{jiengjie_level=149,},
{jiengjie_level=150,},
{jiengjie_level=151,},
{jiengjie_level=152,},
{jiengjie_level=153,},
{jiengjie_level=154,},
{jiengjie_level=155,},
{jiengjie_level=156,},
{jiengjie_level=157,},
{jiengjie_level=158,},
{jiengjie_level=159,},
{jiengjie_level=160,},
{jiengjie_level=161,},
{jiengjie_level=162,},
{jiengjie_level=163,},
{jiengjie_level=164,},
{jiengjie_level=165,},
{jiengjie_level=166,},
{jiengjie_level=167,},
{jiengjie_level=168,},
{jiengjie_level=169,},
{jiengjie_level=170,},
{jiengjie_level=171,},
{jiengjie_level=172,},
{jiengjie_level=173,},
{jiengjie_level=174,},
{jiengjie_level=175,},
{jiengjie_level=176,},
{jiengjie_level=177,},
{jiengjie_level=178,},
{jiengjie_level=179,},
{jiengjie_level=180,},
{jiengjie_level=181,},
{jiengjie_level=182,},
{jiengjie_level=183,},
{jiengjie_level=184,},
{jiengjie_level=185,},
{jiengjie_level=186,},
{jiengjie_level=187,},
{jiengjie_level=188,},
{jiengjie_level=189,},
{jiengjie_level=190,},
{jiengjie_level=191,},
{jiengjie_level=192,},
{jiengjie_level=193,},
{jiengjie_level=194,},
{jiengjie_level=195,},
{jiengjie_level=196,},
{jiengjie_level=197,},
{jiengjie_level=198,},
{jiengjie_level=199,},
{jiengjie_level=200,},
{jiengjie_level=201,},
{jiengjie_level=202,},
{jiengjie_level=203,},
{jiengjie_level=204,},
{jiengjie_level=205,},
{jiengjie_level=206,},
{jiengjie_level=207,},
{jiengjie_level=208,},
{jiengjie_level=209,},
{jiengjie_level=210,},
{jiengjie_level=211,},
{jiengjie_level=212,},
{jiengjie_level=213,},
{jiengjie_level=214,},
{jiengjie_level=215,},
{jiengjie_level=216,},
{jiengjie_level=217,},
{jiengjie_level=218,},
{jiengjie_level=219,},
{jiengjie_level=220,},
{jiengjie_level=221,},
{jiengjie_level=222,},
{jiengjie_level=223,},
{jiengjie_level=224,},
{jiengjie_level=225,},
{jiengjie_level=226,},
{jiengjie_level=227,},
{jiengjie_level=228,},
{jiengjie_level=229,},
{jiengjie_level=230,},
{jiengjie_level=231,},
{jiengjie_level=232,},
{jiengjie_level=233,},
{jiengjie_level=234,},
{jiengjie_level=235,},
{jiengjie_level=236,},
{jiengjie_level=237,},
{jiengjie_level=238,},
{jiengjie_level=239,},
{jiengjie_level=240,},
{jiengjie_level=241,},
{jiengjie_level=242,},
{jiengjie_level=243,},
{jiengjie_level=244,},
{jiengjie_level=245,},
{jiengjie_level=246,},
{jiengjie_level=247,},
{jiengjie_level=248,},
{jiengjie_level=249,},
{jiengjie_level=250,},
{jiengjie_level=251,},
{jiengjie_level=252,},
{jiengjie_level=253,},
{jiengjie_level=254,},
{jiengjie_level=255,},
{jiengjie_level=256,},
{jiengjie_level=257,},
{jiengjie_level=258,},
{jiengjie_level=259,},
{jiengjie_level=260,},
{jiengjie_level=261,},
{jiengjie_level=262,},
{jiengjie_level=263,},
{jiengjie_level=264,},
{jiengjie_level=265,},
{jiengjie_level=266,},
{jiengjie_level=267,},
{jiengjie_level=268,},
{jiengjie_level=269,},
{jiengjie_level=270,},
{jiengjie_level=271,},
{jiengjie_level=272,},
{jiengjie_level=273,},
{jiengjie_level=274,},
{jiengjie_level=275,},
{jiengjie_level=276,},
{jiengjie_level=277,},
{jiengjie_level=278,},
{jiengjie_level=279,},
{jiengjie_level=280,},
{jiengjie_level=281,},
{jiengjie_level=282,},
{jiengjie_level=283,},
{jiengjie_level=284,},
{jiengjie_level=285,},
{jiengjie_level=286,},
{jiengjie_level=287,},
{jiengjie_level=288,},
{jiengjie_level=289,},
{jiengjie_level=290,},
{jiengjie_level=291,},
{jiengjie_level=292,},
{jiengjie_level=293,},
{jiengjie_level=294,},
{jiengjie_level=295,},
{jiengjie_level=296,},
{jiengjie_level=297,},
{jiengjie_level=298,},
{jiengjie_level=299,},
{jiengjie_level=300,},
{jiengjie_level=301,},
{jiengjie_level=302,},
{jiengjie_level=303,},
{jiengjie_level=304,},
{jiengjie_level=305,},
{jiengjie_level=306,},
{jiengjie_level=307,},
{jiengjie_level=308,},
{jiengjie_level=309,},
{jiengjie_level=310,},
{jiengjie_level=311,},
{jiengjie_level=312,},
{jiengjie_level=313,},
{jiengjie_level=314,},
{jiengjie_level=315,},
{jiengjie_level=316,},
{jiengjie_level=317,},
{jiengjie_level=318,},
{jiengjie_level=319,},
{jiengjie_level=320,},
{jiengjie_level=321,},
{jiengjie_level=322,},
{jiengjie_level=323,},
{jiengjie_level=324,},
{jiengjie_level=325,},
{jiengjie_level=326,},
{jiengjie_level=327,},
{jiengjie_level=328,},
{jiengjie_level=329,},
{jiengjie_level=330,},
{jiengjie_level=331,},
{jiengjie_level=332,},
{jiengjie_level=333,},
{jiengjie_level=334,},
{jiengjie_level=335,},
{jiengjie_level=336,},
{jiengjie_level=337,},
{jiengjie_level=338,},
{jiengjie_level=339,},
{jiengjie_level=340,},
{jiengjie_level=341,},
{jiengjie_level=342,},
{jiengjie_level=343,},
{jiengjie_level=344,},
{jiengjie_level=345,},
{jiengjie_level=346,},
{jiengjie_level=347,},
{jiengjie_level=348,},
{jiengjie_level=349,},
{jiengjie_level=350,},
{jiengjie_level=351,},
{jiengjie_level=352,},
{jiengjie_level=353,},
{jiengjie_level=354,},
{jiengjie_level=355,},
{jiengjie_level=356,},
{jiengjie_level=357,},
{jiengjie_level=358,},
{jiengjie_level=359,},
{jiengjie_level=360,},
{jiengjie_level=361,},
{jiengjie_level=362,},
{jiengjie_level=363,},
{jiengjie_level=364,},
{jiengjie_level=365,},
{jiengjie_level=366,},
{jiengjie_level=367,},
{jiengjie_level=368,},
{jiengjie_level=369,},
{jiengjie_level=370,},
{jiengjie_level=371,},
{jiengjie_level=372,},
{jiengjie_level=373,},
{jiengjie_level=374,},
{jiengjie_level=375,},
{jiengjie_level=376,},
{jiengjie_level=377,},
{jiengjie_level=378,},
{jiengjie_level=379,},
{jiengjie_level=380,},
{jiengjie_level=381,},
{jiengjie_level=382,},
{jiengjie_level=383,},
{jiengjie_level=384,},
{jiengjie_level=385,},
{jiengjie_level=386,},
{jiengjie_level=387,},
{jiengjie_level=388,},
{jiengjie_level=389,},
{jiengjie_level=390,},
{jiengjie_level=391,},
{jiengjie_level=392,},
{jiengjie_level=393,},
{jiengjie_level=394,},
{jiengjie_level=395,},
{jiengjie_level=396,},
{jiengjie_level=397,},
{jiengjie_level=398,},
{jiengjie_level=399,},
{jiengjie_level=400,},
{jiengjie_level=401,},
{jiengjie_level=402,},
{jiengjie_level=403,},
{jiengjie_level=404,},
{jiengjie_level=405,},
{jiengjie_level=406,},
{jiengjie_level=407,},
{jiengjie_level=408,},
{jiengjie_level=409,},
{jiengjie_level=410,},
{jiengjie_level=411,},
{jiengjie_level=412,},
{jiengjie_level=413,},
{jiengjie_level=414,},
{jiengjie_level=415,},
{jiengjie_level=416,},
{jiengjie_level=417,},
{jiengjie_level=418,},
{jiengjie_level=419,},
{jiengjie_level=420,},
{jiengjie_level=421,},
{jiengjie_level=422,},
{jiengjie_level=423,},
{jiengjie_level=424,},
{jiengjie_level=425,},
{jiengjie_level=426,},
{jiengjie_level=427,},
{jiengjie_level=428,},
{jiengjie_level=429,},
{jiengjie_level=430,},
{jiengjie_level=431,},
{jiengjie_level=432,},
{jiengjie_level=433,},
{jiengjie_level=434,},
{jiengjie_level=435,},
{jiengjie_level=436,},
{jiengjie_level=437,},
{jiengjie_level=438,},
{jiengjie_level=439,},
{jiengjie_level=440,},
{jiengjie_level=441,},
{jiengjie_level=442,},
{jiengjie_level=443,},
{jiengjie_level=444,},
{jiengjie_level=445,},
{jiengjie_level=446,},
{jiengjie_level=447,},
{jiengjie_level=448,},
{jiengjie_level=449,},
{jiengjie_level=450,},
{jiengjie_level=451,},
{jiengjie_level=452,},
{jiengjie_level=453,},
{jiengjie_level=454,},
{jiengjie_level=455,},
{jiengjie_level=456,},
{jiengjie_level=457,},
{jiengjie_level=458,},
{jiengjie_level=459,},
{jiengjie_level=460,},
{jiengjie_level=461,},
{jiengjie_level=462,},
{jiengjie_level=463,},
{jiengjie_level=464,},
{jiengjie_level=465,},
{jiengjie_level=466,},
{jiengjie_level=467,},
{jiengjie_level=468,},
{jiengjie_level=469,},
{jiengjie_level=470,},
{jiengjie_level=471,},
{jiengjie_level=472,},
{jiengjie_level=473,},
{jiengjie_level=474,},
{jiengjie_level=475,},
{jiengjie_level=476,},
{jiengjie_level=477,},
{jiengjie_level=478,},
{jiengjie_level=479,},
{jiengjie_level=480,},
{jiengjie_level=481,},
{jiengjie_level=482,},
{jiengjie_level=483,},
{jiengjie_level=484,},
{jiengjie_level=485,},
{jiengjie_level=486,},
{jiengjie_level=487,},
{jiengjie_level=488,},
{jiengjie_level=489,},
{jiengjie_level=490,},
{jiengjie_level=491,},
{jiengjie_level=492,},
{jiengjie_level=493,},
{jiengjie_level=494,},
{jiengjie_level=495,},
{jiengjie_level=496,},
{jiengjie_level=497,},
{jiengjie_level=498,},
{jiengjie_level=499,},
{jiengjie_level=500,},
{jiengjie_level=501,},
{jiengjie_level=502,},
{jiengjie_level=503,},
{jiengjie_level=504,},
{jiengjie_level=505,},
{jiengjie_level=506,},
{jiengjie_level=507,},
{jiengjie_level=508,},
{jiengjie_level=509,},
{jiengjie_level=510,},
{jiengjie_level=511,},
{jiengjie_level=512,},
{jiengjie_level=513,},
{jiengjie_level=514,},
{jiengjie_level=515,},
{jiengjie_level=516,},
{jiengjie_level=517,},
{jiengjie_level=518,},
{jiengjie_level=519,},
{jiengjie_level=520,},
{jiengjie_level=521,},
{jiengjie_level=522,},
{jiengjie_level=523,},
{jiengjie_level=524,},
{jiengjie_level=525,},
{jiengjie_level=526,},
{jiengjie_level=527,},
{jiengjie_level=528,},
{jiengjie_level=529,},
{jiengjie_level=530,},
{jiengjie_level=531,},
{jiengjie_level=532,},
{jiengjie_level=533,},
{jiengjie_level=534,},
{jiengjie_level=535,},
{jiengjie_level=536,},
{jiengjie_level=537,},
{jiengjie_level=538,},
{jiengjie_level=539,},
{jiengjie_level=540,},
{jiengjie_level=541,},
{jiengjie_level=542,},
{jiengjie_level=543,},
{jiengjie_level=544,},
{jiengjie_level=545,},
{jiengjie_level=546,},
{jiengjie_level=547,},
{jiengjie_level=548,},
{jiengjie_level=549,},
{jiengjie_level=550,},
{jiengjie_level=551,},
{jiengjie_level=552,},
{jiengjie_level=553,},
{jiengjie_level=554,},
{jiengjie_level=555,},
{jiengjie_level=556,},
{jiengjie_level=557,},
{jiengjie_level=558,},
{jiengjie_level=559,},
{jiengjie_level=560,},
{jiengjie_level=561,},
{jiengjie_level=562,},
{jiengjie_level=563,},
{jiengjie_level=564,},
{jiengjie_level=565,},
{jiengjie_level=566,},
{jiengjie_level=567,},
{jiengjie_level=568,},
{jiengjie_level=569,},
{jiengjie_level=570,},
{jiengjie_level=571,},
{jiengjie_level=572,},
{jiengjie_level=573,},
{jiengjie_level=574,},
{jiengjie_level=575,},
{jiengjie_level=576,},
{jiengjie_level=577,},
{jiengjie_level=578,},
{jiengjie_level=579,},
{jiengjie_level=580,},
{jiengjie_level=581,},
{jiengjie_level=582,},
{jiengjie_level=583,},
{jiengjie_level=584,},
{jiengjie_level=585,},
{jiengjie_level=586,},
{jiengjie_level=587,},
{jiengjie_level=588,},
{jiengjie_level=589,},
{jiengjie_level=590,},
{jiengjie_level=591,},
{jiengjie_level=592,},
{jiengjie_level=593,},
{jiengjie_level=594,},
{jiengjie_level=595,},
{jiengjie_level=596,},
{jiengjie_level=597,},
{jiengjie_level=598,},
{jiengjie_level=599,},
{jiengjie_level=600,}
},

strength_meta_table_map={
},
fail_reduce={
{},
{times=2,hurt_reduce=1000,},
{times=3,hurt_reduce=1500,},
{times=4,hurt_reduce=2000,},
{times=5,hurt_reduce=2500,},
{times=6,hurt_reduce=3000,},
{times=7,hurt_reduce=3500,},
{times=8,hurt_reduce=4000,},
{times=9,hurt_reduce=4500,},
{times=10,hurt_reduce=5000,},
{times=11,hurt_reduce=6000,},
{times=12,hurt_reduce=7000,},
{times=13,hurt_reduce=8000,},
{times=14,hurt_reduce=9000,},
{times=20,hurt_reduce=10000,}
},

fail_reduce_meta_table_map={
},
type={
{},
{type=2,name="问宫境",},
{type=3,name="缚龙境",},
{type=4,name="燃灯境",},
{type=5,name="渡厄境",},
{type=6,name="蜕灵境",},
{type=7,name="种道境",},
{type=8,name="辟界境",},
{type=9,name="窃天境",},
{type=10,name="法则境",},
{type=11,name="劫火境",},
{type=12,name="归墟境",},
{type=13,name="掌劫境",},
{type=14,name="天衍境",},
{type=15,name="虚无境",},
{type=16,name="无上境",}
},

type_meta_table_map={
},
body={
{unlock_ordeal_level=43,},
{seq=1,element=1,body_name="水灵根",unlock_ordeal_level=48,body_desc="水灵根绵长柔韧，主修者可大幅提升生命与生存能力。其灵力如涓涓细流，滋养气血，延展命元；同时化劲卸力，以柔克刚，遇险时如渊渟岳峙，生生不息。",},
{seq=2,element=2,body_name="火灵根",unlock_ordeal_level=53,body_desc="火灵根乃火系修士本源，狂暴炽烈，可大幅提升暴击率与伤害。其焰如龙，灼烧万物，战斗中触发时烈焰爆裂，穿透防御，焚尽敌手。",},
{seq=3,element=3,body_name="土灵根",unlock_ordeal_level=57,body_desc="土灵根厚重如山，主修者可大幅提升抗暴击与稳固能力。其灵力如大地凝实，化劲为盾，周身罡气浑厚，能硬撼致命杀招而不溃，稳守如岳，不动不摇。",},
{seq=4,element=4,body_name="金灵根",unlock_ordeal_level=61,body_desc="金灵根锋锐无匹，主修者可大幅强化破甲与穿透之力。其灵力如淬炼利刃，锋芒所至，摧筋断骨，可瓦解敌方护体罡气，洞穿防御，一击破敌。",},
{seq=5,element=2,body_name="灰烬灵根",unlock_item_id=26135,unlock_item_num=1,body_desc="灰烬灵根由火灵根异变而生，蕴含焚尽万物的寂灭之力。修士催动时，攻击裹挟不灭余烬，大幅提升基础伤害；对BOSS更可触发燃魂效果，灰火蚀骨，造成额外伤害，专克强敌。",},
{seq=6,body_name="翠竹灵根",unlock_item_id=26136,unlock_item_num=1,body_desc="翠竹灵根清韧如碧，主修者可大幅提升防御与持续回复之效。其灵力似新竹拔节，外化青罡护体，柔韧卸力；内蕴生机流转，每受创时如雨后春笋，自愈不息，愈战愈固。",}
},

body_meta_table_map={
},
body_socre={
{},
{level=2,need_score=200,skill_seq=2,level_txt="初级",},
{level=3,need_score=400,skill_seq=3,level_txt="中级",},
{level=4,need_score=1500,skill_seq=4,level_txt="高级",},
{level=5,need_score=2000,skill_seq=5,level_txt="极品",},
{level=6,need_score=3800,skill_seq=6,level_txt="珍宝",},
{level=7,need_score=4500,skill_seq=7,level_txt="秘藏",},
{level=8,need_score=6700,skill_seq=8,level_txt="绝世",},
{level=9,need_score=7600,skill_seq=9,level_txt="须弥",},
{level=10,need_score=11900,skill_seq=10,level_txt="鸿蒙",},
{body_seq=1,},
{body_seq=1,},
{body_seq=1,},
{body_seq=1,},
{body_seq=1,},
{body_seq=1,},
{body_seq=1,},
{body_seq=1,},
{body_seq=1,},
{body_seq=1,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,},
{body_seq=3,},
{body_seq=3,},
{body_seq=3,},
{body_seq=3,},
{body_seq=3,},
{body_seq=3,},
{body_seq=3,},
{body_seq=3,},
{body_seq=3,},
{body_seq=3,},
{body_seq=4,},
{body_seq=4,},
{body_seq=4,},
{body_seq=4,},
{body_seq=4,need_score=2200,},
{body_seq=4,},
{body_seq=4,},
{body_seq=4,need_score=7100,},
{body_seq=4,need_score=8000,},
{body_seq=4,need_score=12400,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{body_seq=6,},
{body_seq=6,},
{body_seq=6,},
{body_seq=6,},
{body_seq=6,},
{body_seq=6,},
{body_seq=6,},
{body_seq=6,},
{body_seq=6,},
{body_seq=6,}
},

body_socre_meta_table_map={
[67]=7,	-- depth:1
[43]=3,	-- depth:1
[44]=4,	-- depth:1
[45]=5,	-- depth:1
[46]=6,	-- depth:1
[47]=67,	-- depth:2
[48]=8,	-- depth:1
[49]=9,	-- depth:1
[50]=10,	-- depth:1
[52]=2,	-- depth:1
[53]=43,	-- depth:2
[54]=44,	-- depth:2
[55]=45,	-- depth:2
[56]=46,	-- depth:2
[58]=48,	-- depth:2
[59]=49,	-- depth:2
[42]=52,	-- depth:2
[60]=50,	-- depth:2
[62]=42,	-- depth:3
[63]=53,	-- depth:3
[64]=54,	-- depth:3
[65]=55,	-- depth:3
[66]=56,	-- depth:3
[68]=58,	-- depth:3
[57]=47,	-- depth:3
[35]=65,	-- depth:4
[39]=59,	-- depth:3
[12]=62,	-- depth:4
[13]=63,	-- depth:4
[14]=64,	-- depth:4
[15]=35,	-- depth:5
[16]=66,	-- depth:4
[17]=57,	-- depth:4
[18]=68,	-- depth:4
[19]=39,	-- depth:4
[20]=60,	-- depth:3
[22]=12,	-- depth:5
[23]=13,	-- depth:5
[24]=14,	-- depth:5
[25]=15,	-- depth:6
[26]=16,	-- depth:5
[27]=17,	-- depth:5
[28]=18,	-- depth:5
[29]=19,	-- depth:5
[30]=20,	-- depth:4
[32]=22,	-- depth:6
[33]=23,	-- depth:6
[34]=24,	-- depth:6
[69]=29,	-- depth:6
[36]=26,	-- depth:6
[37]=27,	-- depth:6
[38]=28,	-- depth:6
[40]=30,	-- depth:5
[70]=40,	-- depth:6
},
talent={
{active_cost="1,0",refine_element_weight="0,100|1,100|2,100|3,50|4,60",},
{seq=1,pre_seq="0|27",refine_element_weight="0,100|1,100|2,100|3,50|4,60",pos_y=8,},
{seq=2,pre_seq="3|5",refine_element_weight="0,100|1,100|2,100|3,50|4,60",pos_x=2,},
{seq=3,pre_seq="2|6",refine_element_weight="0,100|1,100|2,100|3,50|4,60",pos_x=3,},
{seq=4,pre_seq=10,refine_element_weight="0,100|1,100|2,100|3,50|4,60",pos_x=8,},
{seq=5,pre_seq="2|6|22",pos_x=2,},
{seq=6,pre_seq="3|5|7|29",pos_x=3,},
{seq=7,pre_seq="6|8",pos_x=4,},
{seq=8,pre_seq="0|7|9|30",refine_element_weight="0,100|1,100|2,100|3,50|4,60",pos_y=6,},
{seq=9,pre_seq="8|11|23",pos_x=6,},
{seq=10,pre_seq="4|23|28",pos_x=8,},
{seq=11,pre_seq="9|12|30",pos_y=5,},
{seq=12,pre_seq="11|15|23",pos_x=7,},
{seq=13,pre_seq="29|24",pos_x=3,},
{seq=14,pre_seq="30|25",refine_element_weight="0,100|1,100|2,100|3,50|4,60",pos_y=4,},
{seq=15,pre_seq="12|31",pos_x=7,},
{seq=16,pre_seq="24|32",pos_y=3,},
{seq=17,pre_seq="25|31",pos_x=6,pos_y=3,},
{seq=18,pre_seq="20|31",pos_y=3,},
{seq=19,pre_seq="25|33",refine_element_weight="0,100|1,100|2,100|3,50|4,60",pos_y=2,},
{seq=20,pre_seq="18|21|26",pos_y=2,},
{seq=21,pre_seq="20|34",pos_x=9,},
{seq=22,pre_seq=5,pos_x=1,},
{seq=23,pre_seq="9|10|12",pos_x=7,pos_y=6,},
{seq=24,pre_seq="13|16",pos_x=3,},
{seq=25,type=1,pre_seq="14|17|19",active_cost="1,10",refine_cost="2,5",refine_element_weight="0,100|1,100|2,100|3,50|4,60",pos_y=3,},
{seq=26,pre_seq="20|34",pos_x=8,pos_y=1,},
{seq=27,pre_seq=1,pos_y=9,},
{seq=28,pre_seq=10,pos_x=9,pos_y=6,},
{seq=29,pre_seq="6|13",pos_x=3,},
{seq=30,pre_seq="8|11|14",pos_y=5,},
{seq=31,pre_seq="15|17|18",pos_x=7,pos_y=3,},
{seq=32,pre_seq=16,pos_x=2,pos_y=2,},
{seq=33,type=2,pre_seq=19,active_cost="3,1",refine_cost="2,0",element_weight="",color_weight="",refine_element_weight="",refine_color_weight="",pos_y=1,},
{seq=34,pre_seq="21|26",pos_x=9,},
{body_seq=1,refine_element_weight="0,100|1,50|2,100|3,60|4,100",pos_x=7,},
{seq=1,pre_seq=2,pos_y=9,},
{seq=2,pre_seq="1|29",pos_y=9,},
{seq=3,pre_seq="24|4",pos_x=7,},
{body_seq=1,pre_seq="3|28",refine_element_weight="0,100|1,50|2,100|3,60|4,100",pos_y=8,},
{seq=5,pre_seq="6|23|30",pos_x=3,},
{seq=6,pre_seq="5|7",pos_x=4,},
{body_seq=1,seq=7,pre_seq="6|8|25|29",refine_element_weight="0,100|1,50|2,100|3,60|4,100",},
{seq=8,pre_seq="7|9|24",pos_x=6,},
{body_seq=1,pre_seq="0|8|25",refine_element_weight="0,100|1,50|2,100|3,60|4,100",},
{seq=10,pre_seq="11|15",pos_x=3,},
{seq=11,pre_seq="10|12",pos_x=4,},
{seq=12,pre_seq="11|25|31",pos_y=5,},
{seq=13,pre_seq="0|14",pos_x=7,},
{seq=14,pre_seq="13|16",pos_y=5,},
{seq=15,pre_seq="10|17",pos_y=4,},
{seq=16,pre_seq="14|33",pos_y=4,},
{seq=17,pre_seq="15|20|32",pos_x=3,},
{seq=18,pre_seq="31|22",pos_y=3,},
{seq=19,pre_seq=33,pos_x=9,},
{seq=20,pre_seq="17|21",pos_x=3,},
{seq=21,pre_seq="20|22|34",pos_x=4,},
{seq=22,pre_seq="18|21|27",pos_y=2,},
{body_seq=1,pre_seq=5,refine_element_weight="0,100|1,50|2,100|3,60|4,100",pos_x=3,},
{seq=24,pre_seq="3|8|29",pos_x=6,},
{seq=25,pre_seq="7|9|12",pos_y=6,},
{seq=26,pre_seq=32,pos_x=1,pos_y=3,},
{body_seq=1,pre_seq="22|34",refine_element_weight="0,100|1,50|2,100|3,60|4,100",pos_y=1,},
{body_seq=1,seq=28,pos_x=8,},
{body_seq=1,pre_seq="2|7|24",pos_y=8,},
{body_seq=1,seq=30,pre_seq=5,pos_x=2,},
{seq=31,pre_seq="12|18",pos_y=4,},
{body_seq=1,pre_seq="17|26",pos_y=3,},
{body_seq=1,pre_seq="16|19",pos_x=8,},
{body_seq=1,pre_seq="21|27",pos_x=4,},
{body_seq=2,active_cost="1,0",pos_y=5,},
{seq=1,pre_seq="23|30",pos_y=9,},
{body_seq=2,seq=2,pre_seq="5|30",pos_y=8,},
{seq=3,pre_seq="6|28",pos_x=8,},
{body_seq=2,seq=4,pre_seq="7|29",pos_x=3,},
{body_seq=2,seq=5,pre_seq="2|8",},
{body_seq=2,seq=6,pre_seq="3|31",pos_x=8,},
{seq=7,pre_seq="4|24",pos_y=6,},
{body_seq=2,seq=8,pre_seq="0|5|24",pos_y=6,},
{seq=9,pre_seq="25|31",pos_x=7,},
{seq=10,pre_seq=31,pos_x=9,},
{seq=11,pre_seq="0|24|14",pos_x=4,},
{seq=12,pre_seq="0|15|25",pos_y=5,},
{seq=13,pre_seq="14|32",pos_y=4,},
{seq=14,pre_seq="11|13",pos_x=4,},
{seq=15,pre_seq="12|16",pos_y=4,},
{body_seq=2,seq=16,pre_seq="15|33",pos_y=3,},
{seq=17,pre_seq="18|27",pos_x=1,},
{seq=18,pre_seq="17|19",pos_x=2,},
{seq=19,pre_seq="18|32",pos_y=2,},
{seq=20,pre_seq="21|26|33",pos_x=7,},
{seq=21,pre_seq="20|34",pos_y=1,},
{seq=22,pre_seq=34,pos_y=1,},
{seq=23,pre_seq="1|28",pos_y=9,},
{body_seq=2,pre_seq="7|8|11",pos_x=4,pos_y=6,},
{seq=25,pre_seq="9|12",pos_x=7,pos_y=5,},
{body_seq=2,seq=26,pre_seq="20|34",pos_y=2,},
{body_seq=2,pre_seq=17,pos_x=1,pos_y=1,},
{body_seq=2,pre_seq="3|23",},
{body_seq=2,pre_seq=4,pos_y=8,},
{body_seq=2,pre_seq="1|2",pos_x=6,},
{seq=31,pre_seq="6|9|10",pos_y=6,},
{seq=32,pre_seq="13|19",pos_x=3,},
{body_seq=2,seq=33,pre_seq="16|20",},
{body_seq=2,pre_seq="21|22|26",pos_x=8,},
{body_seq=3,refine_element_weight="0,50|1,100|2,60|3,100|4,100",pos_x=4,},
{body_seq=3,pre_seq="2|23",refine_element_weight="0,50|1,100|2,60|3,100|4,100",},
{seq=2,pre_seq="1|3",pos_x=4,},
{seq=3,pre_seq="2|4|6",pos_y=9,},
{seq=4,pre_seq="3|28",pos_y=9,},
{seq=5,pre_seq="24|28",pos_y=9,},
{seq=6,pre_seq="3|30|25",pos_y=8,},
{body_seq=3,pre_seq="29|8|12",refine_element_weight="0,50|1,100|2,60|3,100|4,100",},
{seq=8,pre_seq="7|13|9",pos_x=3,},
{seq=9,pre_seq="8|30|25",pos_x=4,},
{body_seq=3,pre_seq="25|31",refine_element_weight="0,50|1,100|2,60|3,100|4,100",},
{body_seq=3,seq=11,pre_seq="24|15|31",refine_element_weight="0,50|1,100|2,60|3,100|4,100",},
{seq=12,pre_seq="7|13",pos_y=6,},
{seq=13,pre_seq="8|12|32",pos_x=3,},
{seq=14,pre_seq="17|15|31",pos_x=7,},
{seq=15,pre_seq="11|14",pos_y=6,},
{body_seq=3,pre_seq="17|33",refine_element_weight="0,50|1,100|2,60|3,100|4,100",},
{seq=17,pre_seq="14|16|21",pos_x=7,pos_y=5,},
{seq=18,pre_seq="26|32|19",pos_x=3,},
{seq=19,pre_seq="0|18|20|22",pos_x=4,},
{body_seq=3,seq=20,pre_seq="19|33",refine_element_weight="0,50|1,100|2,60|3,100|4,100",},
{seq=21,pre_seq="17|27",pos_x=7,},
{seq=22,pre_seq="19|34",pos_y=3,},
{body_seq=3,pre_seq="1|29",refine_element_weight="0,50|1,100|2,60|3,100|4,100",pos_x=2,},
{seq=24,pre_seq="5|11",pos_x=8,pos_y=8,},
{body_seq=3,seq=25,type=1,pre_seq="6|9|10",active_cost="1,10",refine_cost="2,5",refine_element_weight="0,50|1,100|2,60|3,100|4,100",},
{seq=26,pre_seq=18,pos_y=4,},
{seq=27,pre_seq=21,pos_y=4,},
{seq=28,pre_seq="4|5",pos_y=9,},
{body_seq=3,pre_seq="7|23",pos_x=2,},
{seq=30,pre_seq="2|9|6",pos_x=4,},
{body_seq=3,seq=31,pre_seq="10|11|14",},
{seq=32,pre_seq="0|13|32",pos_x=3,},
{body_seq=3,pre_seq="0|20|16",pos_y=5,},
{seq=34,pre_seq=22,pos_y=2,},
{body_seq=4,active_cost="1,0",refine_element_weight="0,100|1,60|2,50|3,100|4,100",pos_y=6,},
{seq=1,pre_seq="2|5",pos_x=4,},
{body_seq=4,pre_seq="1|3|28",refine_element_weight="0,100|1,60|2,50|3,100|4,100",},
{seq=3,pre_seq="2|25",pos_x=6,},
{body_seq=4,seq=4,pre_seq="24|29",refine_element_weight="0,100|1,60|2,50|3,100|4,100",},
{seq=5,pre_seq="24|28",pos_x=4,},
{seq=6,pre_seq="25|7",pos_x=7,},
{seq=7,pre_seq="6|30",pos_x=8,},
{seq=8,pre_seq=9,pos_x=4,},
{seq=9,pre_seq="0|8|10|11",pos_y=5,},
{seq=10,pos_x=6,},
{seq=11,pre_seq="9|13",pos_y=4,},
{seq=12,pre_seq="15|26",pos_x=3,},
{seq=13,pre_seq="11|26|14|32",pos_y=3,},
{seq=14,pre_seq="13|31",pos_y=3,},
{seq=15,pre_seq="12|33",pos_x=3,},
{seq=16,pre_seq="21|31",pos_y=2,},
{seq=17,pre_seq=18,pos_x=1,},
{seq=18,pre_seq="17|33",pos_y=1,},
{seq=19,pre_seq="33|20",pos_y=1,},
{seq=20,pre_seq="19|32|34",pos_y=1,},
{seq=21,pre_seq="16|22|34",pos_x=7,},
{seq=22,pre_seq="21|27",pos_x=8,},
{body_seq=4,seq=23,type=1,pre_seq=2,active_cost="1,10",refine_cost="2,5",refine_element_weight="0,100|1,60|2,50|3,100|4,100",pos_y=9,},
{body_seq=4,pre_seq="4|5",refine_element_weight="0,100|1,60|2,50|3,100|4,100",pos_x=3,},
{seq=25,pre_seq="3|6|28",pos_x=6,},
{seq=26,pre_seq="12|13",pos_x=4,pos_y=3,},
{seq=27,pre_seq=22,pos_x=9,pos_y=1,},
{body_seq=4,seq=28,type=2,pre_seq="0|2|5|25",active_cost="3,1",refine_cost="2,0",element_weight="",color_weight="",refine_element_weight="",refine_color_weight="",},
{body_seq=4,pos_x=2,pos_y=6,},
{seq=30,pre_seq=7,pos_x=8,},
{body_seq=4,pre_seq="14|16",},
{seq=32,pre_seq="13|20",pos_y=2,},
{body_seq=4,pre_seq="15|18|19",pos_x=3,},
{seq=34,pre_seq="20|21",pos_x=6,},
{body_seq=5,refine_element_weight="0,100|1,100|2,100|3,50|4,60",},
{seq=1,pre_seq=23,pos_x=3,},
{body_seq=5,seq=2,pre_seq=4,pos_y=9,},
{seq=3,pre_seq="4|6|23",pos_y=8,},
{body_seq=5,seq=4,pre_seq="2|3|28",},
{body_seq=5,seq=5,pre_seq="8|29",},
{body_seq=5,seq=6,pre_seq="3|9|29",pos_x=4,},
{seq=7,pre_seq="24|28",pos_x=6,},
{body_seq=5,pre_seq="5|12",pos_x=2,},
{seq=9,pre_seq="6|10",pos_y=6,},
{body_seq=5,seq=10,pre_seq="0|9|24",},
{seq=11,pre_seq="24|31",pos_x=7,},
{seq=12,pre_seq="8|30",pos_y=5,},
{seq=13,pre_seq="17|31",pos_x=8,},
{body_seq=5,pre_seq="15|18|25",pos_x=4,},
{body_seq=5,seq=15,pre_seq="0|14|16",},
{seq=16,pre_seq="15|19",pos_x=6,},
{seq=17,pre_seq="13|26",pos_x=8,},
{seq=18,pre_seq="14|21",pos_y=3,},
{body_seq=5,seq=19,pre_seq="16|22",},
{seq=20,pre_seq="19|26|33",pos_x=7,},
{seq=21,pre_seq="18|27",pos_y=2,},
{seq=22,pre_seq="19|27|33",pos_x=6,},
{body_seq=5,pre_seq="1|3|29",refine_element_weight="0,100|1,100|2,100|3,50|4,60",},
{body_seq=5,pre_seq="7|10|11",pos_x=6,pos_y=6,},
{seq=25,pre_seq="14|30",pos_y=4,},
{body_seq=5,pre_seq="17|20|32",pos_y=3,},
{body_seq=5,seq=27,pre_seq="21|22|34",pos_y=2,},
{body_seq=5,seq=28,pre_seq="4|7",},
{body_seq=5,seq=29,pre_seq="5|6|23",},
{body_seq=5,pre_seq="12|25",pos_x=3,},
{body_seq=5,pre_seq="11|13",pos_y=5,},
{body_seq=5,seq=32,pre_seq=26,pos_y=3,},
{seq=33,pre_seq="20|22",pos_y=2,},
{body_seq=5,seq=34,pre_seq=27,},
{body_seq=6,},
{seq=1,pre_seq="3|28",pos_y=9,},
{body_seq=6,pos_x=7,pos_y=9,},
{seq=3,pre_seq="1|23|31",pos_x=3,},
{body_seq=6,seq=4,pre_seq="9|29",pos_y=8,},
{seq=5,pre_seq="2|6|32",pos_x=7,},
{seq=6,pre_seq="5|24|30",pos_x=8,},
{body_seq=6,seq=7,pre_seq="23|31",pos_x=2,},
{body_seq=6,seq=8,pre_seq="9|31",pos_x=4,},
{body_seq=6,seq=9,pre_seq="4|8|10",},
{body_seq=6,seq=10,pre_seq="9|32",pos_x=6,},
{seq=11,pre_seq="14|31",pos_y=6,},
{seq=12,pre_seq="25|32",pos_y=6,},
{seq=13,pre_seq="14|33",pos_y=5,},
{seq=14,pre_seq="11|13|15|26",pos_x=3,},
{seq=15,pre_seq="0|14|18",pos_y=5,},
{seq=16,pre_seq="0|19|25",pos_y=5,},
{seq=17,pre_seq="25|34",pos_x=8,},
{seq=18,pre_seq="15|21|26",pos_y=4,},
{seq=19,pre_seq="16|20|22",pos_y=4,},
{seq=20,pre_seq="19|25",pos_x=7,},
{seq=21,pre_seq="18|27",pos_y=3,},
{seq=22,pre_seq="19|27",pos_y=3,},
{seq=23,pre_seq="3|7|28",pos_x=2,pos_y=8,},
{body_seq=6,seq=24,type=1,pre_seq="6|32",active_cost="1,10",refine_cost="2,5",pos_x=8,},
{body_seq=6,pre_seq="12|16|17|20",},
{seq=26,pre_seq="14|18",pos_x=3,pos_y=4,},
{body_seq=6,seq=27,type=1,pre_seq="21|22",active_cost="1,10",refine_cost="2,5",pos_y=3,},
{seq=28,pre_seq="1|23",pos_x=2,},
{body_seq=6,seq=29,pre_seq=4,},
{seq=30,pre_seq="2|6",pos_x=8,},
{seq=31,pre_seq="3|7|8|11",pos_x=3,},
{body_seq=6,seq=32,pre_seq="5|10|24|12",pos_x=7,},
{body_seq=6,pre_seq=13,pos_x=1,},
{body_seq=6,pre_seq=17,pos_y=5,}
},

talent_meta_table_map={
[211]=71,	-- depth:1
[176]=71,	-- depth:1
[18]=9,	-- depth:1
[90]=75,	-- depth:1
[89]=90,	-- depth:2
[88]=90,	-- depth:2
[87]=221,	-- depth:1
[86]=87,	-- depth:2
[85]=86,	-- depth:3
[83]=87,	-- depth:2
[91]=90,	-- depth:2
[82]=83,	-- depth:3
[81]=79,	-- depth:1
[80]=79,	-- depth:1
[78]=75,	-- depth:1
[17]=3,	-- depth:1
[74]=73,	-- depth:1
[7]=9,	-- depth:1
[72]=87,	-- depth:2
[84]=75,	-- depth:1
[92]=80,	-- depth:2
[182]=3,	-- depth:1
[8]=9,	-- depth:1
[143]=73,	-- depth:1
[145]=3,	-- depth:1
[146]=145,	-- depth:2
[126]=15,	-- depth:1
[147]=145,	-- depth:2
[148]=145,	-- depth:2
[150]=143,	-- depth:2
[152]=143,	-- depth:2
[154]=143,	-- depth:2
[117]=5,	-- depth:1
[116]=221,	-- depth:1
[115]=116,	-- depth:2
[114]=116,	-- depth:2
[113]=218,	-- depth:1
[112]=126,	-- depth:2
[109]=126,	-- depth:2
[106]=71,	-- depth:1
[161]=143,	-- depth:2
[178]=9,	-- depth:1
[180]=2,	-- depth:1
[181]=3,	-- depth:1
[93]=81,	-- depth:2
[183]=181,	-- depth:2
[6]=9,	-- depth:1
[38]=43,	-- depth:1
[217]=215,	-- depth:1
[44]=43,	-- depth:1
[11]=9,	-- depth:1
[42]=43,	-- depth:1
[41]=43,	-- depth:1
[12]=18,	-- depth:2
[13]=12,	-- depth:3
[216]=215,	-- depth:1
[229]=219,	-- depth:1
[227]=221,	-- depth:1
[226]=219,	-- depth:1
[14]=15,	-- depth:1
[225]=226,	-- depth:2
[224]=218,	-- depth:1
[36]=141,	-- depth:1
[223]=216,	-- depth:2
[228]=224,	-- depth:2
[48]=43,	-- depth:1
[10]=9,	-- depth:1
[232]=219,	-- depth:1
[186]=9,	-- depth:1
[19]=5,	-- depth:1
[16]=15,	-- depth:1
[21]=5,	-- depth:1
[191]=15,	-- depth:1
[22]=20,	-- depth:1
[233]=221,	-- depth:1
[214]=215,	-- depth:1
[222]=214,	-- depth:2
[231]=229,	-- depth:2
[54]=43,	-- depth:1
[230]=221,	-- depth:1
[212]=214,	-- depth:2
[213]=73,	-- depth:1
[58]=43,	-- depth:1
[153]=154,	-- depth:3
[149]=150,	-- depth:3
[151]=149,	-- depth:4
[144]=143,	-- depth:2
[155]=144,	-- depth:3
[184]=9,	-- depth:1
[157]=147,	-- depth:3
[142]=143,	-- depth:2
[188]=184,	-- depth:2
[189]=188,	-- depth:3
[190]=15,	-- depth:1
[192]=190,	-- depth:2
[193]=190,	-- depth:2
[194]=190,	-- depth:2
[195]=18,	-- depth:2
[196]=195,	-- depth:3
[197]=190,	-- depth:2
[198]=197,	-- depth:3
[179]=190,	-- depth:2
[177]=178,	-- depth:2
[185]=190,	-- depth:2
[163]=161,	-- depth:3
[162]=161,	-- depth:3
[160]=142,	-- depth:3
[159]=145,	-- depth:2
[158]=159,	-- depth:3
[156]=157,	-- depth:4
[187]=184,	-- depth:2
[123]=113,	-- depth:2
[40]=5,	-- depth:1
[55]=54,	-- depth:2
[53]=54,	-- depth:2
[45]=10,	-- depth:2
[37]=42,	-- depth:2
[57]=58,	-- depth:2
[107]=177,	-- depth:3
[108]=109,	-- depth:3
[110]=116,	-- depth:2
[111]=117,	-- depth:2
[118]=113,	-- depth:2
[119]=118,	-- depth:3
[120]=118,	-- depth:3
[121]=117,	-- depth:2
[122]=227,	-- depth:2
[56]=58,	-- depth:2
[124]=126,	-- depth:2
[49]=48,	-- depth:2
[50]=40,	-- depth:2
[51]=41,	-- depth:2
[47]=48,	-- depth:2
[52]=40,	-- depth:2
[46]=48,	-- depth:2
[39]=40,	-- depth:2
[128]=115,	-- depth:3
[127]=126,	-- depth:2
[125]=126,	-- depth:2
[63]=238,	-- depth:1
[61]=63,	-- depth:2
[95]=235,	-- depth:1
[96]=95,	-- depth:2
[97]=235,	-- depth:1
[98]=238,	-- depth:1
[203]=26,	-- depth:1
[27]=26,	-- depth:1
[94]=96,	-- depth:3
[165]=235,	-- depth:1
[24]=26,	-- depth:1
[23]=24,	-- depth:2
[166]=165,	-- depth:2
[234]=235,	-- depth:1
[236]=96,	-- depth:3
[25]=26,	-- depth:1
[237]=235,	-- depth:1
[59]=234,	-- depth:2
[60]=59,	-- depth:3
[129]=164,	-- depth:1
[62]=63,	-- depth:2
[130]=131,	-- depth:1
[133]=130,	-- depth:2
[200]=25,	-- depth:2
[199]=59,	-- depth:3
[201]=199,	-- depth:4
[202]=27,	-- depth:2
[167]=164,	-- depth:1
[168]=164,	-- depth:1
[132]=129,	-- depth:2
[28]=34,	-- depth:1
[31]=34,	-- depth:1
[243]=169,	-- depth:1
[242]=243,	-- depth:2
[137]=243,	-- depth:2
[139]=34,	-- depth:1
[240]=28,	-- depth:2
[210]=34,	-- depth:1
[65]=240,	-- depth:3
[66]=169,	-- depth:1
[173]=169,	-- depth:1
[205]=242,	-- depth:3
[33]=34,	-- depth:1
[29]=34,	-- depth:1
[30]=31,	-- depth:2
[32]=34,	-- depth:1
[67]=65,	-- depth:4
[35]=34,	-- depth:1
[239]=240,	-- depth:3
[241]=240,	-- depth:3
[134]=137,	-- depth:3
[64]=240,	-- depth:3
[208]=35,	-- depth:2
[135]=65,	-- depth:4
[136]=135,	-- depth:5
[138]=139,	-- depth:2
[140]=136,	-- depth:6
[244]=139,	-- depth:2
[105]=35,	-- depth:2
[104]=32,	-- depth:2
[103]=104,	-- depth:3
[102]=105,	-- depth:3
[101]=136,	-- depth:6
[170]=240,	-- depth:3
[171]=170,	-- depth:4
[172]=32,	-- depth:2
[174]=34,	-- depth:1
[175]=174,	-- depth:2
[100]=30,	-- depth:3
[99]=64,	-- depth:4
[70]=35,	-- depth:2
[69]=104,	-- depth:3
[68]=33,	-- depth:2
[204]=101,	-- depth:7
[206]=31,	-- depth:2
[207]=32,	-- depth:2
[209]=207,	-- depth:3
[245]=35,	-- depth:2
},
talent_score={
{element_score0=-50,element_score1=100,element_score2=80,element_score3=50,element_score4=50,},
{element_count=2,element_score0=-100,element_score1=200,element_score2=160,element_score3=100,element_score4=100,},
{element_count=3,element_score0=-150,element_score1=300,element_score2=240,element_score3=150,element_score4=150,},
{element_count=4,element_score0=-200,element_score2=320,element_score3=200,element_score4=200,},
{element_count=5,element_score0=-250,element_score1=500,element_score3=250,element_score4=250,},
{element_count=6,element_score0=-300,element_score1=600,element_score2=480,element_score3=300,element_score4=300,},
{element_count=7,element_score0=-350,element_score1=700,element_score2=560,element_score3=350,element_score4=350,},
{element_count=8,element_score0=-400,element_score1=800,element_score2=640,},
{element_count=9,element_score0=-450,element_score1=900,element_score2=720,element_score3=450,element_score4=450,},
{element_count=10,element_score0=-500,element_score1=1000,element_score2=800,element_score3=500,element_score4=500,},
{body_seq=1,element_score0=80,element_score1=50,element_score2=100,element_score3=50,element_score4=-50,},
{body_seq=1,element_count=2,element_score0=160,element_score1=100,element_score2=200,element_score3=100,element_score4=-100,},
{body_seq=1,element_count=3,element_score0=240,element_score1=150,element_score2=300,element_score3=150,element_score4=-150,},
{body_seq=1,element_count=4,element_score0=320,element_score1=200,element_score3=200,element_score4=-200,},
{body_seq=1,element_count=5,element_score1=250,element_score2=500,element_score3=250,element_score4=-250,},
{body_seq=1,element_count=6,element_score0=480,element_score1=300,element_score2=600,element_score3=300,element_score4=-300,},
{body_seq=1,element_count=7,element_score0=560,element_score1=350,element_score2=700,element_score3=350,element_score4=-350,},
{body_seq=1,element_count=8,element_score0=640,element_score2=800,element_score4=-400,},
{body_seq=1,element_count=9,element_score0=720,element_score1=450,element_score2=900,element_score3=450,element_score4=-450,},
{body_seq=1,element_count=10,element_score0=800,element_score1=500,element_score2=1000,element_score3=500,element_score4=-500,},
{body_seq=2,},
{body_seq=2,element_count=2,element_score0=100,element_score1=160,element_score2=-100,element_score3=200,element_score4=100,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,element_count=5,element_score0=250,element_score2=-250,element_score3=500,element_score4=250,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,element_count=9,element_score0=450,element_score1=720,element_score2=-450,element_score3=900,element_score4=450,},
{body_seq=2,element_count=10,element_score0=500,element_score1=800,element_score2=-500,element_score3=1000,element_score4=500,},
{body_seq=3,element_score0=50,element_score1=-50,element_score2=50,element_score3=80,element_score4=100,},
{body_seq=3,element_count=2,element_score0=100,element_score1=-100,element_score2=100,element_score3=160,element_score4=200,},
{body_seq=3,element_count=3,element_score0=150,element_score1=-150,element_score2=150,element_score3=240,element_score4=300,},
{body_seq=3,element_count=4,element_score0=200,element_score1=-200,element_score2=200,element_score3=320,},
{body_seq=3,element_count=5,element_score0=250,element_score1=-250,element_score2=250,element_score4=500,},
{body_seq=3,element_count=6,element_score0=300,element_score1=-300,element_score2=300,element_score3=480,element_score4=600,},
{body_seq=3,element_count=7,element_score0=350,element_score1=-350,element_score2=350,element_score3=560,element_score4=700,},
{body_seq=3,element_count=8,element_score1=-400,element_score3=640,element_score4=800,},
{body_seq=3,element_count=9,element_score0=450,element_score1=-450,element_score2=450,element_score3=720,element_score4=900,},
{body_seq=3,element_count=10,element_score0=500,element_score1=-500,element_score2=500,element_score3=800,element_score4=1000,},
{body_seq=4,element_score0=100,element_score1=50,element_score2=50,element_score3=-50,element_score4=80,},
{body_seq=4,element_count=2,element_score0=200,element_score1=100,element_score2=100,element_score3=-100,element_score4=160,},
{body_seq=4,element_count=3,element_score0=300,element_score1=150,element_score2=150,element_score3=-150,element_score4=240,},
{body_seq=4,element_count=4,element_score1=200,element_score2=200,element_score3=-200,element_score4=320,},
{body_seq=4,element_count=5,element_score0=500,element_score1=250,element_score2=250,element_score3=-250,},
{body_seq=4,element_count=6,element_score0=600,element_score1=300,element_score2=300,element_score3=-300,element_score4=480,},
{body_seq=4,element_count=7,element_score0=700,element_score1=350,element_score2=350,element_score3=-350,element_score4=560,},
{body_seq=4,element_count=8,element_score0=800,element_score3=-400,element_score4=640,},
{body_seq=4,element_count=9,element_score0=900,element_score1=450,element_score2=450,element_score3=-450,element_score4=720,},
{body_seq=4,element_count=10,element_score0=1000,element_score1=500,element_score2=500,element_score3=-500,element_score4=800,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{body_seq=6,element_score0=50,element_score1=80,element_score2=-50,element_score3=100,element_score4=50,},
{body_seq=6,},
{body_seq=6,element_count=3,element_score0=150,element_score1=240,element_score2=-150,element_score3=300,element_score4=150,},
{body_seq=6,element_count=4,element_score0=200,element_score1=320,element_score2=-200,element_score4=200,},
{body_seq=6,},
{body_seq=6,element_count=6,element_score0=300,element_score1=480,element_score2=-300,element_score3=600,element_score4=300,},
{body_seq=6,element_count=7,element_score0=350,element_score1=560,element_score2=-350,element_score3=700,element_score4=350,},
{body_seq=6,element_count=8,element_score1=640,element_score2=-400,element_score3=800,},
{body_seq=6,},
{body_seq=6,}
},

talent_score_meta_table_map={
[28]=68,	-- depth:1
[58]=8,	-- depth:1
[51]=1,	-- depth:1
[54]=4,	-- depth:1
[55]=5,	-- depth:1
[65]=25,	-- depth:1
[24]=64,	-- depth:1
[21]=61,	-- depth:1
[52]=2,	-- depth:1
[53]=3,	-- depth:1
[59]=9,	-- depth:1
[57]=7,	-- depth:1
[60]=10,	-- depth:1
[62]=22,	-- depth:1
[56]=6,	-- depth:1
[23]=63,	-- depth:1
[26]=66,	-- depth:1
[27]=67,	-- depth:1
[69]=29,	-- depth:1
[70]=30,	-- depth:1
},
talent_color={
{body_seq=0,},
{body_seq=0,},
{body_seq=0,},
{body_seq=0,},
{body_seq=0,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=6,},
{seq=6,},
{body_seq=0,},
{seq=6,},
{body_seq=0,},
{seq=7,},
{seq=7,},
{body_seq=0,},
{seq=7,},
{body_seq=0,},
{body_seq=0,},
{body_seq=0,},
{body_seq=0,},
{body_seq=0,},
{seq=8,},
{seq=9,},
{seq=9,},
{seq=9,},
{seq=9,},
{seq=9,},
{seq=10,},
{seq=10,},
{seq=10,},
{seq=10,},
{body_seq=0,},
{seq=11,},
{seq=11,},
{seq=11,},
{seq=11,},
{seq=11,},
{seq=12,},
{seq=12,},
{seq=12,},
{seq=12,},
{seq=12,},
{seq=13,},
{seq=13,},
{seq=13,},
{seq=13,},
{seq=13,},
{seq=14,},
{seq=14,},
{seq=14,},
{seq=14,},
{seq=14,},
{seq=15,},
{seq=15,},
{seq=15,},
{seq=15,},
{body_seq=0,},
{seq=16,},
{seq=16,},
{seq=16,},
{seq=16,},
{seq=16,},
{seq=17,},
{seq=17,},
{seq=17,},
{seq=17,},
{seq=17,},
{seq=18,},
{seq=18,},
{seq=18,},
{seq=18,},
{seq=18,},
{seq=19,},
{seq=19,},
{seq=19,},
{seq=19,},
{seq=19,},
{seq=20,},
{seq=20,},
{seq=20,},
{seq=20,},
{seq=20,},
{seq=21,},
{seq=21,},
{seq=21,},
{seq=21,},
{seq=21,},
{body_seq=0,seq=22,},
{body_seq=0,seq=22,skill_seq="17|67|77",},
{body_seq=0,seq=22,skill_seq="18|68|78",},
{body_seq=0,seq=22,},
{body_seq=0,seq=22,},
{seq=23,skill_seq="46|66|76",},
{seq=23,skill_seq="47|67|77",},
{seq=23,},
{seq=23,},
{body_seq=0,seq=23,},
{seq=24,},
{seq=24,},
{seq=24,},
{seq=24,},
{seq=24,},
{seq=25,},
{seq=25,},
{seq=25,skill_seq="48|68|78",},
{body_seq=0,skill_seq="49|69|79",},
{seq=25,},
{seq=26,},
{seq=26,},
{seq=26,},
{seq=26,},
{seq=26,},
{body_seq=0,seq=27,},
{body_seq=0,skill_seq="135|175|185",},
{body_seq=0,seq=29,},
{body_seq=0,seq=30,},
{body_seq=0,seq=31,},
{body_seq=0,seq=32,},
{body_seq=0,seq=33,},
{body_seq=0,skill_seq="125|165|185",},
{color=1,element_num="1,1",},
{color=2,element_num="2,2",},
{color=3,},
{color=4,element_num="4,4",},
{element_num="5,5",},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=9,},
{seq=9,},
{seq=9,},
{seq=9,},
{seq=9,},
{seq=10,},
{seq=10,},
{seq=10,},
{seq=10,},
{seq=10,},
{seq=11,},
{seq=11,},
{seq=11,},
{seq=11,},
{seq=11,},
{seq=12,},
{seq=12,},
{seq=12,},
{seq=12,},
{seq=12,},
{seq=13,},
{seq=13,},
{seq=13,},
{seq=13,},
{seq=13,},
{seq=14,},
{seq=14,},
{seq=14,},
{seq=14,},
{seq=14,},
{seq=15,},
{seq=15,},
{seq=15,},
{seq=15,},
{seq=15,},
{seq=16,},
{seq=16,},
{seq=16,},
{seq=16,},
{seq=16,},
{seq=17,},
{seq=17,},
{seq=17,},
{seq=17,},
{seq=17,},
{seq=18,},
{seq=18,},
{seq=18,},
{seq=18,},
{seq=18,},
{seq=19,},
{seq=19,},
{seq=19,},
{seq=19,},
{seq=19,},
{seq=20,},
{seq=20,},
{seq=20,},
{seq=20,},
{seq=20,},
{seq=21,},
{seq=21,},
{seq=21,},
{seq=21,},
{seq=21,},
{seq=22,},
{seq=22,},
{seq=22,},
{seq=22,},
{seq=22,},
{seq=23,},
{seq=23,},
{seq=23,skill_seq="13|68|83",},
{seq=23,},
{seq=23,},
{seq=24,},
{seq=24,},
{seq=24,},
{seq=24,},
{seq=24,},
{seq=25,skill_seq="11|66|81",},
{seq=25,skill_seq="12|67|82",},
{seq=25,},
{seq=25,skill_seq="14|69|84",},
{seq=25,},
{seq=26,skill_seq="51|66|81",},
{seq=26,skill_seq="52|67|82",},
{seq=26,color=3,element_num="5,6",skill_seq="53|68|83",},
{seq=26,color=4,element_num="7,8",skill_seq="54|69|84",},
{seq=26,skill_seq="55|70|85",},
{seq=27,color=1,skill_seq="15|70|85",},
{color=2,element_num="4,4",},
{seq=27,skill_seq="15|70|85",},
{seq=27,skill_seq="15|70|85",},
{seq=27,element_num="9,10",skill_seq="15|70|85",},
{seq=28,element_num="",skill_seq="130|170|180",choose_skill_cost="3,2|3,4|3,6",},
{seq=29,skill_seq="135|175|185",},
{seq=30,skill_seq="100|140|195",},
{seq=31,skill_seq="105|145|180",},
{seq=32,skill_seq="110|150|185",},
{seq=33,skill_seq="115|155|195",},
{seq=34,skill_seq="120|160|180",},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=3,},
{seq=3,},
{body_seq=2,},
{seq=3,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,},
{seq=4,},
{body_seq=2,},
{seq=4,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=9,},
{seq=9,},
{seq=9,},
{seq=9,},
{seq=9,},
{seq=10,},
{seq=10,},
{seq=10,},
{seq=10,},
{seq=10,},
{seq=11,},
{seq=11,},
{seq=11,},
{seq=11,},
{seq=11,},
{seq=12,},
{seq=12,},
{seq=12,},
{seq=12,},
{seq=12,},
{seq=13,},
{seq=13,},
{seq=13,},
{body_seq=2,},
{seq=13,},
{seq=14,},
{seq=14,},
{seq=14,},
{seq=14,},
{seq=14,},
{seq=15,},
{seq=15,},
{seq=15,},
{seq=15,},
{seq=15,},
{seq=16,},
{seq=16,},
{seq=16,},
{seq=16,},
{seq=16,},
{seq=17,},
{seq=17,},
{body_seq=2,},
{seq=17,},
{seq=17,},
{seq=18,},
{seq=18,},
{seq=18,},
{seq=18,},
{seq=18,},
{seq=19,},
{seq=19,},
{body_seq=2,},
{seq=19,},
{body_seq=2,},
{seq=20,},
{seq=20,},
{seq=20,},
{seq=20,},
{seq=20,},
{seq=21,},
{seq=21,},
{seq=21,},
{seq=21,},
{seq=21,},
{seq=22,},
{seq=22,},
{body_seq=2,},
{seq=22,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,},
{seq=24,},
{body_seq=2,skill_seq="58|68|88",},
{body_seq=2,},
{seq=24,},
{seq=25,},
{seq=25,},
{seq=25,},
{seq=25,},
{seq=25,},
{seq=26,},
{body_seq=2,},
{body_seq=2,},
{seq=26,},
{body_seq=2,},
{seq=27,},
{seq=27,},
{seq=27,},
{seq=27,},
{seq=27,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,},
{body_seq=2,},
{body_seq=3,},
{body_seq=3,},
{body_seq=3,},
{body_seq=3,},
{body_seq=3,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=4,},
{seq=4,},
{body_seq=3,},
{seq=4,},
{seq=4,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=9,},
{seq=9,},
{seq=9,},
{seq=9,},
{seq=9,},
{seq=10,},
{seq=10,},
{seq=10,},
{seq=10,},
{seq=10,},
{seq=11,},
{seq=11,},
{seq=11,},
{body_seq=3,},
{seq=11,},
{body_seq=3,},
{seq=12,},
{seq=12,},
{seq=12,},
{seq=12,},
{seq=13,},
{seq=13,},
{seq=13,},
{seq=13,},
{seq=13,},
{seq=14,},
{seq=14,},
{seq=14,},
{seq=14,},
{body_seq=3,},
{body_seq=3,},
{seq=15,},
{body_seq=3,},
{seq=15,},
{seq=15,},
{seq=16,},
{seq=16,},
{seq=16,},
{seq=16,},
{seq=16,},
{seq=17,},
{seq=17,},
{seq=17,},
{seq=17,},
{seq=17,},
{seq=18,},
{body_seq=3,},
{seq=18,},
{seq=18,},
{seq=18,},
{seq=19,},
{seq=19,},
{seq=19,},
{seq=19,},
{body_seq=3,},
{seq=20,},
{seq=20,},
{seq=20,},
{seq=20,},
{seq=20,},
{seq=21,},
{seq=21,},
{seq=21,},
{seq=21,},
{seq=21,},
{seq=22,},
{seq=22,},
{body_seq=3,},
{seq=22,},
{body_seq=3,},
{seq=23,},
{body_seq=3,skill_seq="12|67|92",},
{body_seq=3,skill_seq="13|68|93",},
{body_seq=3,skill_seq="14|69|94",},
{seq=23,},
{seq=24,},
{body_seq=3,skill_seq="62|67|92",},
{body_seq=3,skill_seq="63|68|93",},
{body_seq=3,skill_seq="64|69|94",},
{seq=24,skill_seq="65|70|95",},
{seq=25,},
{seq=25,},
{seq=25,},
{seq=25,},
{body_seq=3,skill_seq="15|70|95",},
{body_seq=3,skill_seq="61|66|91",},
{seq=26,},
{seq=26,},
{seq=26,},
{seq=26,},
{body_seq=3,skill_seq="11|66|91",},
{seq=27,},
{seq=27,},
{seq=27,},
{seq=27,},
{body_seq=3,},
{body_seq=3,},
{body_seq=3,},
{body_seq=3,},
{body_seq=3,},
{body_seq=3,},
{body_seq=3,skill_seq="115|155|180",},
{body_seq=4,},
{body_seq=4,},
{body_seq=4,},
{body_seq=4,},
{body_seq=4,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=3,},
{body_seq=4,},
{seq=3,},
{body_seq=4,},
{seq=3,},
{body_seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=5,},
{body_seq=4,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=7,},
{body_seq=4,},
{seq=7,},
{body_seq=4,},
{body_seq=4,},
{body_seq=4,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=9,},
{body_seq=4,},
{seq=9,},
{seq=9,},
{seq=9,},
{seq=10,},
{seq=10,},
{seq=10,},
{seq=10,},
{seq=10,},
{seq=11,},
{seq=11,},
{seq=11,},
{body_seq=4,},
{seq=11,},
{seq=12,},
{seq=12,},
{seq=12,},
{seq=12,},
{seq=12,},
{seq=13,},
{seq=13,},
{seq=13,},
{seq=13,},
{seq=13,},
{seq=14,},
{seq=14,},
{seq=14,},
{seq=14,},
{seq=14,},
{seq=15,},
{seq=15,},
{seq=15,},
{seq=15,},
{seq=15,},
{seq=16,},
{seq=16,},
{seq=16,},
{seq=16,},
{body_seq=4,},
{seq=17,},
{seq=17,},
{body_seq=4,},
{seq=17,},
{seq=17,},
{seq=18,},
{seq=18,},
{seq=18,},
{seq=18,},
{seq=18,},
{seq=19,},
{seq=19,},
{seq=19,},
{body_seq=4,},
{seq=19,},
{body_seq=4,},
{seq=20,},
{seq=20,},
{seq=20,},
{seq=20,},
{seq=21,},
{body_seq=4,},
{seq=21,},
{seq=21,},
{seq=21,},
{body_seq=4,},
{seq=22,},
{seq=22,},
{seq=22,},
{seq=22,},
{body_seq=4,skill_seq="11|66|71",},
{body_seq=4,skill_seq="12|67|72",},
{seq=23,},
{body_seq=4,skill_seq="14|69|74",},
{seq=23,},
{seq=24,},
{seq=24,},
{body_seq=4,skill_seq="43|68|73",},
{seq=24,},
{seq=24,},
{seq=25,},
{seq=25,},
{seq=25,},
{seq=25,},
{body_seq=4,skill_seq="15|70|75",},
{body_seq=4,skill_seq="41|66|71",},
{body_seq=4,skill_seq="42|67|72",},
{seq=26,},
{body_seq=4,skill_seq="44|69|74",},
{body_seq=4,skill_seq="45|70|75",},
{seq=27,},
{seq=27,},
{body_seq=4,skill_seq="13|68|73",},
{seq=27,},
{seq=27,},
{body_seq=4,},
{body_seq=4,},
{body_seq=4,},
{body_seq=4,},
{body_seq=4,},
{body_seq=4,},
{body_seq=4,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{body_seq=5,},
{seq=6,},
{body_seq=5,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=9,},
{seq=9,},
{seq=9,},
{body_seq=5,},
{seq=9,},
{seq=10,},
{seq=10,},
{seq=10,},
{seq=10,},
{seq=10,},
{seq=11,},
{seq=11,},
{seq=11,},
{seq=11,},
{seq=11,},
{seq=12,},
{body_seq=5,},
{seq=12,},
{seq=12,},
{seq=12,},
{seq=13,},
{seq=13,},
{seq=13,},
{seq=13,},
{seq=13,},
{seq=14,},
{seq=14,},
{body_seq=5,},
{seq=14,},
{seq=14,},
{seq=15,},
{seq=15,},
{seq=15,},
{seq=15,},
{seq=15,},
{seq=16,},
{seq=16,},
{body_seq=5,},
{seq=16,},
{seq=16,},
{seq=17,},
{seq=17,},
{seq=17,},
{seq=17,},
{body_seq=5,},
{seq=18,},
{seq=18,},
{body_seq=5,},
{seq=18,},
{body_seq=5,},
{seq=19,},
{seq=19,},
{body_seq=5,},
{body_seq=5,},
{seq=19,},
{body_seq=5,},
{seq=20,},
{seq=20,},
{seq=20,},
{seq=20,},
{seq=21,},
{seq=21,},
{seq=21,},
{seq=21,},
{seq=21,},
{seq=22,},
{seq=22,},
{seq=22,},
{seq=22,},
{seq=22,},
{seq=23,},
{seq=23,},
{seq=23,},
{body_seq=5,skill_seq="19|69|79",},
{seq=23,},
{seq=24,skill_seq="46|66|76",},
{body_seq=5,seq=24,},
{body_seq=5,skill_seq="48|68|78",},
{body_seq=5,skill_seq="49|69|79",},
{seq=24,},
{seq=25,},
{body_seq=5,seq=25,},
{body_seq=5,seq=25,},
{seq=25,},
{body_seq=5,skill_seq="20|70|80",},
{seq=26,},
{seq=26,},
{seq=26,},
{seq=26,},
{body_seq=5,skill_seq="50|70|80",},
{body_seq=5,skill_seq="16|66|76",},
{seq=27,},
{seq=27,},
{seq=27,},
{seq=27,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{body_seq=5,},
{body_seq=6,},
{body_seq=6,},
{body_seq=6,},
{body_seq=6,},
{body_seq=6,},
{seq=1,},
{seq=1,},
{body_seq=6,},
{seq=1,},
{body_seq=6,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{body_seq=6,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=6,},
{body_seq=6,},
{seq=6,},
{seq=6,},
{body_seq=6,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=9,},
{seq=9,},
{seq=9,},
{seq=9,},
{seq=9,},
{body_seq=6,},
{body_seq=6,},
{seq=10,},
{seq=10,},
{seq=10,},
{seq=11,},
{seq=11,},
{seq=11,},
{seq=11,},
{seq=11,},
{seq=12,},
{seq=12,},
{seq=12,},
{seq=12,},
{seq=12,},
{seq=13,},
{body_seq=6,},
{seq=13,},
{seq=13,},
{seq=13,},
{seq=14,},
{seq=14,},
{seq=14,},
{seq=14,},
{seq=14,},
{seq=15,},
{seq=15,},
{seq=15,},
{seq=15,},
{seq=15,},
{seq=16,},
{seq=16,},
{body_seq=6,},
{seq=16,},
{seq=16,},
{seq=17,},
{seq=17,},
{seq=17,},
{body_seq=6,},
{seq=17,},
{body_seq=6,},
{body_seq=6,},
{seq=18,},
{seq=18,},
{seq=18,},
{seq=19,},
{seq=19,},
{body_seq=6,},
{seq=19,},
{seq=19,},
{seq=20,},
{seq=20,},
{seq=20,},
{seq=20,},
{seq=20,},
{seq=21,},
{seq=21,},
{body_seq=6,},
{seq=21,},
{seq=21,},
{seq=22,},
{body_seq=6,},
{seq=22,},
{body_seq=6,},
{seq=22,},
{seq=23,},
{seq=23,},
{seq=23,},
{seq=23,},
{seq=23,},
{seq=24,},
{seq=24,},
{seq=24,},
{body_seq=6,skill_seq="59|69|89",},
{seq=24,},
{seq=25,},
{body_seq=6,skill_seq="17|67|87",},
{body_seq=6,skill_seq="18|68|88",},
{body_seq=6,skill_seq="19|69|89",},
{seq=25,},
{body_seq=6,skill_seq="56|66|86",},
{body_seq=6,skill_seq="57|67|87",},
{body_seq=6,skill_seq="58|68|88",},
{seq=26,},
{seq=26,skill_seq="60|70|90",},
{body_seq=6,skill_seq="16|66|86",},
{seq=27,},
{seq=27,},
{seq=27,},
{body_seq=6,skill_seq="20|70|90",},
{body_seq=6,},
{body_seq=6,},
{body_seq=6,},
{body_seq=6,},
{body_seq=6,},
{body_seq=6,},
{body_seq=6,}
},

talent_color_meta_table_map={
[226]=146,	-- depth:1
[213]=148,	-- depth:1
[166]=226,	-- depth:2
[216]=166,	-- depth:3
[218]=213,	-- depth:2
[163]=218,	-- depth:3
[221]=216,	-- depth:4
[161]=221,	-- depth:5
[223]=163,	-- depth:4
[442]=148,	-- depth:1
[186]=161,	-- depth:6
[440]=146,	-- depth:1
[228]=223,	-- depth:5
[158]=228,	-- depth:6
[211]=186,	-- depth:7
[168]=158,	-- depth:7
[206]=211,	-- depth:8
[231]=206,	-- depth:9
[188]=168,	-- depth:8
[181]=231,	-- depth:10
[736]=442,	-- depth:2
[191]=181,	-- depth:11
[734]=440,	-- depth:2
[193]=188,	-- depth:9
[208]=193,	-- depth:10
[178]=208,	-- depth:11
[176]=191,	-- depth:12
[198]=178,	-- depth:12
[201]=176,	-- depth:13
[173]=198,	-- depth:13
[203]=173,	-- depth:14
[171]=201,	-- depth:14
[196]=171,	-- depth:15
[881]=734,	-- depth:3
[183]=203,	-- depth:15
[151]=196,	-- depth:16
[253]=183,	-- depth:16
[256]=151,	-- depth:17
[258]=253,	-- depth:17
[251]=256,	-- depth:18
[233]=258,	-- depth:18
[248]=233,	-- depth:19
[246]=251,	-- depth:19
[153]=248,	-- depth:20
[5]=736,	-- depth:3
[589]=5,	-- depth:4
[293]=881,	-- depth:4
[3]=293,	-- depth:5
[587]=3,	-- depth:6
[156]=246,	-- depth:20
[236]=156,	-- depth:21
[241]=236,	-- depth:22
[883]=589,	-- depth:5
[295]=883,	-- depth:6
[243]=153,	-- depth:21
[238]=243,	-- depth:22
[829]=241,	-- depth:23
[388]=829,	-- depth:24
[390]=243,	-- depth:22
[385]=390,	-- depth:23
[809]=829,	-- depth:24
[353]=388,	-- depth:25
[355]=385,	-- depth:24
[826]=385,	-- depth:24
[358]=353,	-- depth:26
[375]=355,	-- depth:25
[383]=358,	-- depth:27
[824]=383,	-- depth:28
[811]=826,	-- depth:25
[363]=383,	-- depth:28
[365]=375,	-- depth:26
[380]=365,	-- depth:27
[821]=380,	-- depth:28
[368]=363,	-- depth:29
[819]=824,	-- depth:29
[370]=380,	-- depth:28
[378]=819,	-- depth:30
[373]=378,	-- depth:31
[814]=373,	-- depth:32
[816]=821,	-- depth:29
[360]=370,	-- depth:29
[1]=144,	-- depth:1
[333]=373,	-- depth:32
[831]=816,	-- depth:30
[880]=145,	-- depth:1
[303]=333,	-- depth:33
[882]=147,	-- depth:1
[300]=360,	-- depth:30
[298]=303,	-- depth:34
[294]=882,	-- depth:2
[292]=880,	-- depth:2
[291]=1,	-- depth:2
[886]=298,	-- depth:35
[305]=300,	-- depth:31
[888]=300,	-- depth:31
[893]=888,	-- depth:32
[278]=283,	-- depth:1
[896]=886,	-- depth:36
[898]=893,	-- depth:33
[274]=279,	-- depth:1
[273]=283,	-- depth:1
[901]=896,	-- depth:37
[891]=901,	-- depth:38
[879]=291,	-- depth:3
[308]=896,	-- depth:37
[310]=898,	-- depth:34
[348]=308,	-- depth:38
[345]=310,	-- depth:35
[834]=814,	-- depth:33
[343]=348,	-- depth:39
[836]=831,	-- depth:31
[340]=345,	-- depth:36
[338]=343,	-- depth:40
[839]=834,	-- depth:34
[335]=340,	-- depth:37
[806]=836,	-- depth:32
[841]=806,	-- depth:33
[330]=335,	-- depth:38
[328]=338,	-- depth:41
[844]=839,	-- depth:35
[325]=330,	-- depth:39
[846]=841,	-- depth:34
[323]=328,	-- depth:42
[320]=325,	-- depth:40
[318]=323,	-- depth:43
[315]=320,	-- depth:41
[313]=318,	-- depth:44
[350]=315,	-- depth:42
[393]=313,	-- depth:45
[400]=350,	-- depth:43
[395]=400,	-- depth:44
[669]=816,	-- depth:30
[672]=378,	-- depth:31
[674]=669,	-- depth:31
[677]=672,	-- depth:32
[552]=846,	-- depth:35
[550]=844,	-- depth:36
[679]=674,	-- depth:32
[547]=552,	-- depth:36
[545]=550,	-- depth:37
[682]=677,	-- depth:33
[542]=547,	-- depth:37
[684]=679,	-- depth:33
[540]=545,	-- depth:38
[667]=682,	-- depth:34
[537]=684,	-- depth:34
[535]=540,	-- depth:39
[689]=684,	-- depth:34
[532]=537,	-- depth:35
[530]=535,	-- depth:40
[692]=667,	-- depth:35
[527]=532,	-- depth:36
[525]=530,	-- depth:41
[694]=689,	-- depth:35
[522]=527,	-- depth:37
[520]=525,	-- depth:42
[697]=692,	-- depth:36
[517]=522,	-- depth:38
[699]=694,	-- depth:36
[687]=697,	-- depth:37
[664]=699,	-- depth:37
[662]=687,	-- depth:38
[659]=664,	-- depth:38
[622]=662,	-- depth:39
[627]=622,	-- depth:40
[619]=659,	-- depth:39
[617]=627,	-- depth:41
[629]=619,	-- depth:40
[614]=629,	-- depth:41
[612]=617,	-- depth:42
[632]=612,	-- depth:43
[609]=614,	-- depth:42
[634]=609,	-- depth:43
[607]=632,	-- depth:44
[604]=634,	-- depth:44
[637]=607,	-- depth:45
[602]=637,	-- depth:46
[639]=604,	-- depth:45
[599]=639,	-- depth:46
[597]=602,	-- depth:47
[642]=597,	-- depth:48
[594]=599,	-- depth:47
[592]=642,	-- depth:49
[644]=594,	-- depth:48
[588]=294,	-- depth:3
[586]=292,	-- depth:3
[585]=879,	-- depth:4
[647]=592,	-- depth:50
[649]=644,	-- depth:49
[652]=647,	-- depth:51
[654]=649,	-- depth:50
[657]=652,	-- depth:52
[515]=662,	-- depth:39
[804]=657,	-- depth:53
[512]=659,	-- depth:39
[507]=512,	-- depth:40
[452]=507,	-- depth:41
[450]=515,	-- depth:40
[761]=614,	-- depth:42
[447]=452,	-- depth:42
[445]=450,	-- depth:41
[764]=804,	-- depth:54
[441]=588,	-- depth:4
[439]=586,	-- depth:4
[438]=585,	-- depth:5
[766]=761,	-- depth:43
[769]=764,	-- depth:55
[771]=766,	-- depth:44
[774]=769,	-- depth:56
[759]=774,	-- depth:57
[776]=771,	-- depth:45
[781]=776,	-- depth:46
[784]=759,	-- depth:58
[786]=781,	-- depth:47
[789]=784,	-- depth:59
[791]=786,	-- depth:48
[794]=789,	-- depth:60
[796]=791,	-- depth:49
[405]=699,	-- depth:37
[403]=697,	-- depth:37
[799]=794,	-- depth:61
[269]=279,	-- depth:1
[801]=796,	-- depth:50
[398]=403,	-- depth:38
[779]=799,	-- depth:62
[455]=445,	-- depth:42
[457]=447,	-- depth:43
[756]=801,	-- depth:51
[505]=455,	-- depth:43
[502]=457,	-- depth:44
[500]=505,	-- depth:44
[732]=438,	-- depth:6
[497]=502,	-- depth:45
[733]=439,	-- depth:5
[495]=500,	-- depth:45
[735]=441,	-- depth:5
[492]=497,	-- depth:46
[490]=495,	-- depth:46
[487]=492,	-- depth:47
[739]=779,	-- depth:63
[485]=490,	-- depth:47
[741]=756,	-- depth:52
[482]=487,	-- depth:48
[480]=485,	-- depth:48
[744]=739,	-- depth:64
[477]=482,	-- depth:49
[475]=480,	-- depth:49
[746]=741,	-- depth:53
[472]=477,	-- depth:50
[470]=475,	-- depth:50
[749]=744,	-- depth:65
[467]=472,	-- depth:51
[751]=746,	-- depth:54
[465]=470,	-- depth:51
[462]=467,	-- depth:52
[754]=749,	-- depth:66
[460]=754,	-- depth:67
[510]=460,	-- depth:68
[268]=278,	-- depth:2
[624]=477,	-- depth:50
[903]=462,	-- depth:53
[33]=470,	-- depth:51
[986]=398,	-- depth:39
[911]=986,	-- depth:40
[35]=472,	-- depth:51
[913]=35,	-- depth:52
[916]=911,	-- depth:41
[918]=913,	-- depth:53
[38]=916,	-- depth:42
[921]=916,	-- depth:42
[983]=918,	-- depth:54
[923]=983,	-- depth:55
[40]=918,	-- depth:54
[981]=921,	-- depth:43
[43]=921,	-- depth:43
[926]=981,	-- depth:44
[45]=40,	-- depth:55
[928]=923,	-- depth:56
[908]=928,	-- depth:57
[75]=45,	-- depth:56
[150]=145,	-- depth:1
[152]=147,	-- depth:1
[177]=152,	-- depth:2
[175]=150,	-- depth:2
[28]=43,	-- depth:44
[174]=144,	-- depth:1
[172]=177,	-- depth:3
[170]=175,	-- depth:3
[169]=174,	-- depth:2
[167]=172,	-- depth:4
[978]=908,	-- depth:58
[30]=75,	-- depth:57
[164]=169,	-- depth:3
[162]=167,	-- depth:5
[988]=978,	-- depth:59
[160]=170,	-- depth:4
[159]=164,	-- depth:4
[157]=162,	-- depth:6
[155]=160,	-- depth:5
[154]=159,	-- depth:5
[165]=155,	-- depth:6
[48]=28,	-- depth:45
[931]=926,	-- depth:45
[50]=30,	-- depth:58
[65]=50,	-- depth:59
[95]=65,	-- depth:60
[966]=931,	-- depth:46
[93]=48,	-- depth:46
[951]=966,	-- depth:47
[68]=93,	-- depth:47
[90]=95,	-- depth:61
[953]=988,	-- depth:60
[948]=953,	-- depth:61
[88]=68,	-- depth:48
[85]=90,	-- depth:62
[956]=951,	-- depth:48
[83]=88,	-- depth:49
[958]=948,	-- depth:62
[80]=958,	-- depth:63
[963]=958,	-- depth:63
[78]=83,	-- depth:50
[961]=83,	-- depth:50
[70]=80,	-- depth:64
[179]=154,	-- depth:6
[98]=78,	-- depth:51
[63]=98,	-- depth:52
[976]=98,	-- depth:52
[933]=963,	-- depth:64
[53]=63,	-- depth:53
[936]=976,	-- depth:53
[55]=933,	-- depth:65
[938]=933,	-- depth:65
[973]=938,	-- depth:66
[110]=55,	-- depth:66
[968]=973,	-- depth:67
[941]=936,	-- depth:54
[108]=53,	-- depth:54
[971]=941,	-- depth:55
[943]=968,	-- depth:68
[105]=110,	-- depth:67
[60]=105,	-- depth:68
[103]=108,	-- depth:55
[946]=971,	-- depth:56
[100]=60,	-- depth:69
[58]=103,	-- depth:56
[991]=946,	-- depth:57
[149]=179,	-- depth:7
[182]=157,	-- depth:7
[235]=165,	-- depth:7
[234]=149,	-- depth:8
[232]=182,	-- depth:8
[230]=235,	-- depth:8
[229]=234,	-- depth:9
[227]=232,	-- depth:9
[225]=230,	-- depth:9
[13]=58,	-- depth:57
[224]=229,	-- depth:10
[222]=227,	-- depth:10
[180]=225,	-- depth:10
[220]=180,	-- depth:11
[15]=100,	-- depth:70
[219]=224,	-- depth:11
[217]=222,	-- depth:11
[215]=220,	-- depth:12
[18]=13,	-- depth:58
[237]=217,	-- depth:12
[214]=219,	-- depth:12
[239]=214,	-- depth:13
[242]=237,	-- depth:13
[2]=733,	-- depth:6
[264]=274,	-- depth:2
[4]=735,	-- depth:6
[263]=273,	-- depth:2
[906]=991,	-- depth:58
[259]=269,	-- depth:2
[257]=242,	-- depth:14
[8]=18,	-- depth:59
[255]=215,	-- depth:13
[254]=239,	-- depth:14
[252]=257,	-- depth:15
[10]=15,	-- depth:71
[250]=255,	-- depth:14
[249]=254,	-- depth:15
[247]=252,	-- depth:16
[245]=250,	-- depth:15
[244]=249,	-- depth:16
[240]=245,	-- depth:16
[212]=247,	-- depth:17
[73]=8,	-- depth:60
[200]=240,	-- depth:17
[189]=244,	-- depth:17
[199]=189,	-- depth:18
[205]=200,	-- depth:18
[207]=212,	-- depth:18
[192]=207,	-- depth:19
[23]=73,	-- depth:61
[209]=199,	-- depth:19
[194]=209,	-- depth:20
[195]=205,	-- depth:19
[20]=10,	-- depth:72
[202]=192,	-- depth:20
[204]=194,	-- depth:21
[185]=195,	-- depth:20
[993]=943,	-- depth:69
[197]=202,	-- depth:21
[25]=20,	-- depth:73
[184]=204,	-- depth:22
[187]=197,	-- depth:22
[190]=185,	-- depth:21
[210]=190,	-- depth:22
[695]=254,	-- depth:15
[1009]=274,	-- depth:2
[970]=235,	-- depth:8
[631]=190,	-- depth:22
[643]=202,	-- depth:21
[969]=234,	-- depth:9
[967]=232,	-- depth:9
[648]=643,	-- depth:22
[698]=648,	-- depth:23
[696]=631,	-- depth:23
[646]=696,	-- depth:24
[700]=259,	-- depth:3
[990]=696,	-- depth:24
[720]=700,	-- depth:4
[626]=646,	-- depth:25
[719]=278,	-- depth:2
[715]=274,	-- depth:2
[964]=969,	-- depth:10
[1018]=283,	-- depth:1
[628]=698,	-- depth:24
[714]=273,	-- depth:2
[1014]=279,	-- depth:1
[710]=720,	-- depth:5
[709]=719,	-- depth:3
[645]=695,	-- depth:16
[965]=990,	-- depth:25
[989]=964,	-- depth:11
[992]=698,	-- depth:24
[705]=715,	-- depth:3
[630]=645,	-- depth:17
[704]=714,	-- depth:3
[1013]=1018,	-- depth:2
[633]=628,	-- depth:25
[663]=633,	-- depth:26
[1008]=1018,	-- depth:2
[980]=965,	-- depth:26
[678]=663,	-- depth:27
[1003]=1013,	-- depth:3
[999]=1009,	-- depth:3
[676]=626,	-- depth:26
[675]=630,	-- depth:18
[982]=992,	-- depth:25
[673]=678,	-- depth:28
[658]=673,	-- depth:29
[680]=675,	-- depth:19
[638]=658,	-- depth:30
[670]=680,	-- depth:20
[640]=670,	-- depth:21
[668]=638,	-- depth:31
[998]=1008,	-- depth:3
[666]=676,	-- depth:27
[665]=640,	-- depth:22
[660]=665,	-- depth:23
[984]=989,	-- depth:12
[661]=666,	-- depth:28
[671]=661,	-- depth:29
[693]=668,	-- depth:32
[656]=671,	-- depth:30
[681]=656,	-- depth:31
[650]=660,	-- depth:24
[985]=980,	-- depth:27
[691]=985,	-- depth:28
[994]=1014,	-- depth:2
[972]=982,	-- depth:26
[690]=650,	-- depth:25
[651]=691,	-- depth:29
[987]=972,	-- depth:27
[688]=693,	-- depth:33
[979]=984,	-- depth:13
[974]=979,	-- depth:14
[975]=985,	-- depth:28
[653]=688,	-- depth:34
[686]=651,	-- depth:30
[685]=979,	-- depth:14
[1004]=994,	-- depth:3
[977]=987,	-- depth:28
[683]=977,	-- depth:29
[655]=685,	-- depth:15
[636]=686,	-- depth:31
[641]=636,	-- depth:32
[635]=655,	-- depth:16
[763]=175,	-- depth:3
[962]=977,	-- depth:29
[832]=685,	-- depth:15
[830]=683,	-- depth:30
[828]=763,	-- depth:4
[827]=832,	-- depth:16
[825]=830,	-- depth:31
[823]=828,	-- depth:5
[822]=827,	-- depth:17
[820]=825,	-- depth:32
[818]=823,	-- depth:6
[817]=822,	-- depth:18
[815]=820,	-- depth:33
[813]=818,	-- depth:7
[812]=817,	-- depth:19
[810]=815,	-- depth:34
[909]=974,	-- depth:15
[808]=813,	-- depth:8
[910]=763,	-- depth:4
[807]=812,	-- depth:20
[912]=962,	-- depth:30
[805]=810,	-- depth:35
[914]=909,	-- depth:16
[915]=910,	-- depth:5
[803]=808,	-- depth:9
[802]=807,	-- depth:21
[917]=912,	-- depth:31
[800]=805,	-- depth:36
[919]=914,	-- depth:17
[833]=803,	-- depth:10
[835]=800,	-- depth:37
[837]=802,	-- depth:22
[838]=833,	-- depth:11
[904]=919,	-- depth:18
[900]=915,	-- depth:6
[899]=904,	-- depth:19
[905]=900,	-- depth:7
[897]=917,	-- depth:32
[907]=897,	-- depth:33
[895]=905,	-- depth:8
[894]=899,	-- depth:20
[892]=907,	-- depth:34
[890]=895,	-- depth:9
[889]=894,	-- depth:21
[887]=892,	-- depth:35
[885]=890,	-- depth:10
[920]=885,	-- depth:11
[884]=889,	-- depth:22
[867]=279,	-- depth:1
[866]=278,	-- depth:2
[625]=919,	-- depth:18
[861]=273,	-- depth:2
[857]=867,	-- depth:2
[856]=866,	-- depth:3
[852]=867,	-- depth:2
[851]=861,	-- depth:3
[847]=857,	-- depth:3
[845]=835,	-- depth:38
[843]=838,	-- depth:12
[842]=837,	-- depth:23
[840]=845,	-- depth:39
[871]=851,	-- depth:4
[724]=704,	-- depth:4
[798]=843,	-- depth:13
[922]=887,	-- depth:36
[942]=922,	-- depth:37
[760]=840,	-- depth:40
[758]=798,	-- depth:14
[944]=884,	-- depth:23
[757]=842,	-- depth:24
[945]=798,	-- depth:14
[755]=760,	-- depth:41
[947]=942,	-- depth:38
[753]=758,	-- depth:15
[752]=757,	-- depth:25
[949]=944,	-- depth:24
[750]=755,	-- depth:42
[950]=945,	-- depth:15
[748]=753,	-- depth:16
[747]=752,	-- depth:26
[952]=947,	-- depth:39
[745]=750,	-- depth:43
[743]=748,	-- depth:17
[742]=747,	-- depth:27
[954]=949,	-- depth:25
[955]=950,	-- depth:16
[740]=745,	-- depth:44
[738]=743,	-- depth:18
[737]=742,	-- depth:28
[957]=952,	-- depth:40
[959]=954,	-- depth:26
[960]=955,	-- depth:17
[762]=737,	-- depth:29
[902]=957,	-- depth:41
[940]=960,	-- depth:18
[765]=740,	-- depth:45
[795]=765,	-- depth:46
[793]=940,	-- depth:19
[792]=762,	-- depth:30
[924]=959,	-- depth:27
[790]=795,	-- depth:47
[925]=940,	-- depth:19
[788]=793,	-- depth:20
[787]=792,	-- depth:31
[785]=790,	-- depth:48
[927]=902,	-- depth:42
[783]=788,	-- depth:21
[782]=787,	-- depth:32
[780]=927,	-- depth:43
[797]=782,	-- depth:33
[929]=782,	-- depth:33
[777]=797,	-- depth:34
[930]=783,	-- depth:22
[775]=780,	-- depth:44
[773]=783,	-- depth:22
[772]=777,	-- depth:35
[932]=927,	-- depth:43
[770]=775,	-- depth:45
[768]=773,	-- depth:23
[767]=772,	-- depth:36
[934]=929,	-- depth:34
[935]=930,	-- depth:23
[937]=932,	-- depth:44
[939]=934,	-- depth:35
[778]=768,	-- depth:24
[862]=852,	-- depth:3
[513]=954,	-- depth:26
[621]=768,	-- depth:24
[311]=752,	-- depth:26
[312]=753,	-- depth:16
[314]=902,	-- depth:42
[316]=311,	-- depth:27
[317]=312,	-- depth:17
[319]=314,	-- depth:43
[321]=316,	-- depth:28
[309]=319,	-- depth:44
[322]=317,	-- depth:18
[326]=321,	-- depth:29
[327]=322,	-- depth:19
[329]=309,	-- depth:45
[331]=326,	-- depth:30
[332]=327,	-- depth:20
[334]=329,	-- depth:46
[336]=331,	-- depth:31
[324]=334,	-- depth:47
[307]=332,	-- depth:21
[306]=336,	-- depth:32
[304]=324,	-- depth:48
[280]=279,	-- depth:1
[281]=276,	-- depth:1
[282]=277,	-- depth:1
[285]=284,	-- depth:1
[286]=284,	-- depth:1
[287]=284,	-- depth:1
[288]=284,	-- depth:1
[289]=284,	-- depth:1
[290]=284,	-- depth:1
[296]=306,	-- depth:33
[297]=307,	-- depth:22
[299]=304,	-- depth:49
[301]=296,	-- depth:34
[302]=297,	-- depth:23
[337]=302,	-- depth:24
[339]=299,	-- depth:50
[341]=301,	-- depth:35
[342]=337,	-- depth:25
[379]=339,	-- depth:51
[381]=341,	-- depth:36
[382]=342,	-- depth:26
[384]=379,	-- depth:52
[386]=381,	-- depth:37
[387]=382,	-- depth:27
[389]=384,	-- depth:53
[391]=386,	-- depth:38
[392]=387,	-- depth:28
[394]=389,	-- depth:54
[396]=391,	-- depth:39
[397]=392,	-- depth:29
[399]=394,	-- depth:55
[401]=396,	-- depth:40
[402]=397,	-- depth:30
[404]=399,	-- depth:56
[406]=994,	-- depth:3
[377]=402,	-- depth:31
[275]=280,	-- depth:2
[376]=401,	-- depth:41
[372]=377,	-- depth:32
[344]=404,	-- depth:57
[346]=376,	-- depth:42
[347]=372,	-- depth:33
[349]=344,	-- depth:58
[351]=346,	-- depth:43
[352]=347,	-- depth:34
[354]=349,	-- depth:59
[356]=351,	-- depth:44
[357]=352,	-- depth:35
[623]=329,	-- depth:46
[361]=356,	-- depth:45
[362]=357,	-- depth:36
[364]=354,	-- depth:60
[366]=361,	-- depth:46
[367]=362,	-- depth:37
[369]=364,	-- depth:61
[371]=366,	-- depth:47
[374]=369,	-- depth:62
[410]=998,	-- depth:4
[272]=277,	-- depth:1
[270]=280,	-- depth:2
[41]=331,	-- depth:31
[42]=332,	-- depth:21
[44]=334,	-- depth:47
[46]=41,	-- depth:32
[47]=42,	-- depth:22
[49]=44,	-- depth:48
[51]=46,	-- depth:33
[39]=49,	-- depth:49
[52]=47,	-- depth:23
[56]=51,	-- depth:34
[57]=52,	-- depth:24
[59]=39,	-- depth:50
[61]=56,	-- depth:35
[62]=57,	-- depth:25
[64]=59,	-- depth:51
[66]=61,	-- depth:36
[54]=64,	-- depth:52
[37]=62,	-- depth:26
[36]=66,	-- depth:37
[34]=54,	-- depth:53
[6]=36,	-- depth:38
[7]=37,	-- depth:27
[9]=34,	-- depth:54
[11]=6,	-- depth:39
[12]=7,	-- depth:28
[14]=9,	-- depth:55
[16]=11,	-- depth:40
[17]=12,	-- depth:29
[19]=14,	-- depth:56
[21]=16,	-- depth:41
[22]=17,	-- depth:30
[24]=19,	-- depth:57
[26]=21,	-- depth:42
[27]=22,	-- depth:31
[29]=24,	-- depth:58
[31]=26,	-- depth:43
[32]=27,	-- depth:32
[67]=32,	-- depth:33
[69]=29,	-- depth:59
[71]=31,	-- depth:44
[72]=67,	-- depth:34
[109]=69,	-- depth:60
[111]=867,	-- depth:2
[115]=861,	-- depth:3
[116]=111,	-- depth:3
[120]=866,	-- depth:3
[121]=111,	-- depth:3
[125]=115,	-- depth:4
[126]=116,	-- depth:4
[130]=120,	-- depth:4
[131]=121,	-- depth:4
[135]=125,	-- depth:5
[260]=270,	-- depth:3
[261]=276,	-- depth:1
[262]=272,	-- depth:2
[265]=275,	-- depth:3
[266]=276,	-- depth:1
[267]=277,	-- depth:1
[107]=72,	-- depth:35
[271]=261,	-- depth:2
[106]=71,	-- depth:45
[102]=107,	-- depth:36
[74]=109,	-- depth:61
[76]=106,	-- depth:46
[77]=102,	-- depth:37
[79]=74,	-- depth:62
[81]=76,	-- depth:47
[82]=77,	-- depth:38
[84]=79,	-- depth:63
[86]=81,	-- depth:48
[87]=82,	-- depth:39
[89]=84,	-- depth:64
[91]=86,	-- depth:49
[92]=87,	-- depth:40
[94]=89,	-- depth:65
[96]=91,	-- depth:50
[97]=92,	-- depth:41
[99]=94,	-- depth:66
[101]=96,	-- depth:51
[104]=99,	-- depth:67
[411]=999,	-- depth:4
[359]=69,	-- depth:60
[603]=19,	-- depth:57
[601]=17,	-- depth:30
[529]=92,	-- depth:41
[605]=21,	-- depth:42
[498]=61,	-- depth:36
[496]=59,	-- depth:51
[516]=496,	-- depth:52
[548]=498,	-- depth:37
[546]=516,	-- depth:53
[494]=529,	-- depth:42
[493]=548,	-- depth:38
[606]=601,	-- depth:31
[608]=603,	-- depth:58
[544]=494,	-- depth:43
[491]=546,	-- depth:54
[489]=544,	-- depth:44
[518]=493,	-- depth:39
[519]=489,	-- depth:45
[610]=605,	-- depth:43
[488]=518,	-- depth:40
[486]=491,	-- depth:55
[509]=519,	-- depth:46
[600]=610,	-- depth:44
[598]=608,	-- depth:59
[596]=606,	-- depth:32
[595]=600,	-- depth:45
[508]=488,	-- depth:41
[511]=486,	-- depth:56
[567]=273,	-- depth:2
[568]=274,	-- depth:2
[506]=511,	-- depth:57
[504]=509,	-- depth:47
[562]=567,	-- depth:3
[503]=508,	-- depth:42
[501]=506,	-- depth:58
[572]=562,	-- depth:4
[483]=503,	-- depth:43
[573]=279,	-- depth:1
[514]=504,	-- depth:48
[558]=568,	-- depth:3
[577]=567,	-- depth:3
[557]=577,	-- depth:4
[553]=573,	-- depth:2
[551]=501,	-- depth:59
[549]=514,	-- depth:49
[590]=595,	-- depth:46
[591]=596,	-- depth:33
[593]=598,	-- depth:60
[499]=549,	-- depth:50
[481]=551,	-- depth:60
[484]=499,	-- depth:51
[478]=483,	-- depth:44
[615]=590,	-- depth:47
[479]=484,	-- depth:52
[430]=410,	-- depth:5
[523]=478,	-- depth:45
[538]=523,	-- depth:46
[616]=591,	-- depth:34
[426]=406,	-- depth:4
[425]=1013,	-- depth:3
[618]=593,	-- depth:61
[536]=481,	-- depth:61
[534]=479,	-- depth:53
[421]=411,	-- depth:5
[420]=430,	-- depth:6
[524]=534,	-- depth:54
[620]=615,	-- depth:48
[526]=536,	-- depth:62
[416]=426,	-- depth:5
[415]=425,	-- depth:4
[533]=538,	-- depth:47
[531]=526,	-- depth:63
[528]=533,	-- depth:48
[613]=618,	-- depth:62
[539]=524,	-- depth:55
[521]=531,	-- depth:64
[543]=528,	-- depth:49
[476]=521,	-- depth:65
[474]=539,	-- depth:56
[473]=543,	-- depth:50
[471]=476,	-- depth:66
[469]=474,	-- depth:57
[468]=473,	-- depth:51
[466]=471,	-- depth:67
[464]=469,	-- depth:58
[463]=468,	-- depth:52
[461]=466,	-- depth:68
[541]=461,	-- depth:69
[458]=463,	-- depth:53
[459]=464,	-- depth:59
[454]=459,	-- depth:60
[611]=464,	-- depth:59
[443]=458,	-- depth:54
[444]=454,	-- depth:61
[446]=541,	-- depth:70
[448]=443,	-- depth:55
[449]=444,	-- depth:62
[456]=446,	-- depth:71
[563]=553,	-- depth:3
[451]=456,	-- depth:72
[453]=448,	-- depth:56
[554]=260,	-- depth:4
[555]=261,	-- depth:2
[556]=262,	-- depth:3
[559]=265,	-- depth:4
[112]=280,	-- depth:2
[560]=266,	-- depth:2
[561]=267,	-- depth:2
[413]=266,	-- depth:2
[565]=555,	-- depth:3
[1002]=267,	-- depth:2
[1005]=270,	-- depth:3
[1006]=271,	-- depth:3
[1007]=272,	-- depth:2
[1010]=275,	-- depth:3
[1011]=276,	-- depth:1
[1012]=1002,	-- depth:3
[1015]=1005,	-- depth:4
[1016]=1006,	-- depth:4
[1017]=1007,	-- depth:3
[1019]=284,	-- depth:1
[1020]=285,	-- depth:2
[1021]=286,	-- depth:2
[1022]=287,	-- depth:2
[1023]=288,	-- depth:2
[1001]=1011,	-- depth:2
[564]=554,	-- depth:5
[1000]=1010,	-- depth:4
[996]=1016,	-- depth:5
[566]=556,	-- depth:4
[569]=559,	-- depth:5
[570]=560,	-- depth:3
[571]=561,	-- depth:3
[574]=564,	-- depth:6
[575]=565,	-- depth:4
[576]=566,	-- depth:5
[578]=1019,	-- depth:2
[579]=1020,	-- depth:3
[580]=1021,	-- depth:3
[581]=1022,	-- depth:3
[582]=1023,	-- depth:3
[583]=289,	-- depth:2
[584]=290,	-- depth:2
[995]=1015,	-- depth:5
[997]=1017,	-- depth:4
[113]=276,	-- depth:1
[143]=290,	-- depth:2
[117]=112,	-- depth:3
[850]=262,	-- depth:3
[853]=117,	-- depth:4
[854]=266,	-- depth:2
[855]=267,	-- depth:2
[858]=112,	-- depth:3
[859]=113,	-- depth:2
[860]=850,	-- depth:4
[863]=853,	-- depth:5
[849]=859,	-- depth:3
[864]=854,	-- depth:3
[731]=584,	-- depth:3
[730]=583,	-- depth:3
[868]=858,	-- depth:4
[869]=849,	-- depth:4
[870]=860,	-- depth:5
[729]=582,	-- depth:4
[872]=578,	-- depth:3
[873]=579,	-- depth:4
[865]=855,	-- depth:3
[874]=580,	-- depth:4
[848]=868,	-- depth:5
[436]=730,	-- depth:4
[414]=1002,	-- depth:3
[409]=997,	-- depth:5
[408]=996,	-- depth:6
[407]=995,	-- depth:6
[417]=407,	-- depth:7
[418]=408,	-- depth:7
[419]=409,	-- depth:6
[422]=1010,	-- depth:4
[437]=731,	-- depth:4
[423]=1011,	-- depth:2
[427]=417,	-- depth:8
[428]=418,	-- depth:8
[429]=419,	-- depth:7
[431]=872,	-- depth:4
[432]=873,	-- depth:5
[433]=874,	-- depth:5
[434]=581,	-- depth:4
[435]=729,	-- depth:5
[424]=414,	-- depth:4
[114]=850,	-- depth:4
[875]=434,	-- depth:5
[877]=436,	-- depth:5
[142]=290,	-- depth:2
[141]=289,	-- depth:2
[140]=288,	-- depth:2
[139]=287,	-- depth:2
[138]=286,	-- depth:2
[137]=284,	-- depth:1
[136]=284,	-- depth:1
[134]=114,	-- depth:5
[412]=422,	-- depth:5
[133]=113,	-- depth:2
[129]=272,	-- depth:2
[128]=113,	-- depth:2
[127]=117,	-- depth:4
[124]=134,	-- depth:6
[123]=133,	-- depth:3
[122]=112,	-- depth:3
[119]=129,	-- depth:3
[118]=128,	-- depth:3
[132]=122,	-- depth:4
[876]=435,	-- depth:6
[701]=117,	-- depth:4
[703]=262,	-- depth:3
[878]=437,	-- depth:5
[728]=875,	-- depth:6
[727]=433,	-- depth:6
[726]=432,	-- depth:6
[725]=431,	-- depth:5
[723]=703,	-- depth:4
[722]=281,	-- depth:2
[721]=701,	-- depth:5
[702]=722,	-- depth:3
[718]=277,	-- depth:1
[716]=275,	-- depth:3
[713]=723,	-- depth:5
[712]=702,	-- depth:4
[711]=721,	-- depth:6
[708]=718,	-- depth:2
[707]=266,	-- depth:2
[706]=716,	-- depth:4
[1024]=877,	-- depth:6
[717]=707,	-- depth:3
[1025]=878,	-- depth:6
},
talent_attr={
{attr_value2=77,attr_id3=106,},
{color=2,attr_value1=18462,attr_value2=154,attr_id3=106,},
{color=3,attr_value1=26667,attr_id3=106,attr_value3=56,},
{color=4,attr_value1=35556,attr_value2=297,attr_value3=75,},
{color=5,attr_value1=44445,attr_value2=371,attr_value3=93,},
{element=1,attr_value1=667,attr_id2=103,attr_value2=334,},
{color=2,attr_value1=1334,attr_value2=667,},
{color=3,attr_value1=1800,attr_value2=900,attr_value3=150,},
{color=4,attr_value1=2400,attr_value2=1200,attr_value3=200,},
{color=5,attr_value1=3000,attr_value2=1500,attr_value3=250,},
{element=2,attr_value1=8889,attr_id2=103,attr_value2=112,},
{element=2,color=2,attr_value1=17778,attr_id2=103,attr_id3=106,},
{color=3,attr_value1=25715,attr_value2=322,attr_value3=54,},
{color=4,attr_value1=34286,attr_value2=429,attr_value3=72,},
{color=5,attr_value1=42858,attr_value2=536,attr_value3=90,},
{element=3,attr_id1=102,attr_value1=750,attr_value2=250,},
{color=2,attr_value1=1500,attr_value2=500,},
{color=3,attr_value1=2000,attr_value2=667,attr_value3=167,},
{color=4,attr_value1=2667,attr_value2=889,attr_value3=223,},
{color=5,attr_value1=3334,attr_value2=1112,attr_value3=278,},
{element=4,attr_value2=77,},
{element=4,color=2,attr_value1=18462,attr_value2=154,},
{element=4,color=3,attr_value1=26667,attr_value3=56,},
{color=4,attr_value1=35556,attr_value2=297,attr_value3=75,},
{color=5,attr_value1=44445,attr_value2=371,attr_value3=93,}
},

talent_attr_meta_table_map={
[17]=16,	-- depth:1
[24]=22,	-- depth:1
[25]=22,	-- depth:1
[6]=16,	-- depth:1
[5]=3,	-- depth:1
[4]=3,	-- depth:1
[11]=1,	-- depth:1
[7]=6,	-- depth:2
[18]=16,	-- depth:1
[19]=16,	-- depth:1
[20]=16,	-- depth:1
[10]=6,	-- depth:2
[9]=6,	-- depth:2
[14]=12,	-- depth:1
[15]=12,	-- depth:1
[8]=6,	-- depth:2
[13]=12,	-- depth:1
},
talent_skill={
{type=12,param0=100,param1=0,param3=0,skill_icon=1,skill_name="灵根·本源",skill_desc="自身灵根达成真神境，自此不惧九天炼狱，使自身大额提升异能。\n灵根全属性+1%",},
{seq=2,param0=200,skill_desc="自身灵根达成真神境，自此不惧九天炼狱，使自身大额提升异能。\n灵根全属性+2%",},
{seq=3,param0=300,skill_desc="自身灵根达成真神境，自此不惧九天炼狱，使自身大额提升异能。\n灵根全属性+3%",},
{seq=4,param0=400,skill_desc="自身灵根达成真神境，自此不惧九天炼狱，使自身大额提升异能。\n灵根全属性+4%",},
{seq=5,param0=500,skill_desc="自身灵根达成真神境，自此不惧九天炼狱，使自身大额提升异能。\n灵根全属性+5%",},
{seq=6,param0=600,skill_desc="自身灵根达成真神境，自此不惧九天炼狱，使自身大额提升异能。\n灵根全属性+6%",},
{seq=7,param0=700,skill_desc="自身灵根达成真神境，自此不惧九天炼狱，使自身大额提升异能。\n灵根全属性+7%",},
{seq=8,param0=800,skill_desc="自身灵根达成真神境，自此不惧九天炼狱，使自身大额提升异能。\n灵根全属性+8%",},
{seq=9,param0=900,skill_desc="自身灵根达成真神境，自此不惧九天炼狱，使自身大额提升异能。\n灵根全属性+9%",},
{seq=10,param0=1000,skill_desc="自身灵根达成真神境，自此不惧九天炼狱，使自身大额提升异能。\n灵根全属性+10%",},
{seq=11,type=2,param0=101,param3=0,skill_name="慧根·属性",skill_desc="自身灵根大圆满，战力暴涨，自身灵根的生命属性提升1%",},
{seq=12,param1=200,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的生命属性提升2%",},
{seq=13,param1=300,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的生命属性提升3%",},
{seq=14,param1=400,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的生命属性提升4%",},
{seq=15,param1=500,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的生命属性提升5%",},
{seq=16,param0=102,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的攻击属性提升1%",},
{seq=17,param1=200,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的攻击属性提升2%",},
{seq=18,param1=300,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的攻击属性提升3%",},
{seq=19,param1=400,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的攻击属性提升4%",},
{seq=20,param1=500,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的攻击属性提升5%",},
{seq=21,param0=103,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的防御属性提升1%",},
{seq=22,param1=200,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的防御属性提升2%",},
{seq=23,param1=300,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的防御属性提升3%",},
{seq=24,param1=400,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的防御属性提升4%",},
{seq=25,param1=500,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的防御属性提升5%",},
{seq=26,param0=104,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的破甲属性提升1%",},
{seq=27,param0=104,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的破甲属性提升2%",},
{seq=28,param0=104,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的破甲属性提升3%",},
{seq=29,param1=400,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的破甲属性提升4%",},
{seq=30,param0=104,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的破甲属性提升5%",},
{seq=31,param0=105,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的伏法伤害属性提升1%",},
{seq=32,param0=105,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的伏法伤害属性提升2%",},
{seq=33,param0=105,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的伏法伤害属性提升3%",},
{seq=34,param0=105,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的伏法伤害属性提升4%",},
{seq=35,param0=105,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的伏法伤害属性提升5%",},
{seq=36,param0=106,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的伏法护甲属性提升1%",},
{seq=37,param0=106,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的伏法护甲属性提升2%",},
{seq=38,param0=106,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的伏法护甲属性提升3%",},
{seq=39,param0=106,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的伏法护甲属性提升4%",},
{seq=40,param0=106,skill_desc="自身灵根大圆满，战力暴涨，自身灵根的伏法护甲属性提升5%",},
{seq=41,param3=1000,skill_desc="自身灵根每拥有1个金元素，提升生命1000",},
{seq=42,param3=2000,skill_desc="自身灵根每拥有1个金元素，提升生命2000",},
{seq=43,param3=3000,skill_desc="自身灵根每拥有1个金元素，提升生命3000",},
{seq=44,param3=4000,skill_desc="自身灵根每拥有1个金元素，提升生命4000",},
{seq=45,param0=0,param2=101,param3=5000,skill_icon=11,skill_desc="自身灵根每拥有1个金元素，提升生命5000",},
{seq=46,type=3,param1=1,param2=102,param3=100,skill_icon=12,skill_name="慧根·提升",skill_desc="自身灵根每拥有1个木元素，提升攻击100",},
{seq=47,param3=200,skill_desc="自身灵根每拥有1个木元素，提升攻击200",},
{seq=48,param3=300,skill_desc="自身灵根每拥有1个木元素，提升攻击300",},
{seq=49,param3=400,skill_desc="自身灵根每拥有1个木元素，提升攻击400",},
{seq=50,param3=500,skill_desc="自身灵根每拥有1个木元素，提升攻击500",},
{seq=51,param3=100,skill_desc="自身灵根每拥有1个水元素，提升防御100",},
{seq=52,param0=2,param2=103,skill_icon=13,skill_desc="自身灵根每拥有1个水元素，提升防御200",},
{seq=53,param3=300,skill_desc="自身灵根每拥有1个水元素，提升防御300",},
{seq=54,param3=400,skill_desc="自身灵根每拥有1个水元素，提升防御400",},
{seq=55,param3=500,skill_desc="自身灵根每拥有1个水元素，提升防御500",},
{seq=56,param3=100,skill_desc="自身灵根每拥有1个火元素，提升破甲100",},
{seq=57,param3=200,skill_desc="自身灵根每拥有1个火元素，提升破甲200",},
{seq=58,param0=3,param2=104,skill_icon=14,skill_desc="自身灵根每拥有1个火元素，提升破甲300",},
{seq=59,param3=400,skill_desc="自身灵根每拥有1个火元素，提升破甲400",},
{seq=60,param3=500,skill_desc="自身灵根每拥有1个火元素，提升破甲500",},
{seq=61,param0=4,skill_icon=15,skill_desc="自身灵根每拥有1个土元素，提升生命1000",},
{seq=62,param3=2000,skill_desc="自身灵根每拥有1个土元素，提升生命2000",},
{seq=63,param3=3000,skill_desc="自身灵根每拥有1个土元素，提升生命3000",},
{seq=64,param3=4000,skill_desc="自身灵根每拥有1个土元素，提升生命4000",},
{seq=65,param3=5000,skill_desc="自身灵根每拥有1个土元素，提升生命5000",},
{seq=66,type=4,param0=100,param2=100,param3=100,param4=100,skill_name="慧根·升品",skill_desc="自身灵根完美度达到极品时，提升洗髓高品质灵根概率1%",},
{seq=67,param0=200,param1=200,param2=200,param3=200,param4=200,skill_desc="自身灵根完美度达到极品时，提升洗髓高品质灵根概率2%",},
{seq=68,param0=300,param1=300,param2=300,param3=300,param4=300,skill_desc="自身灵根完美度达到极品时，提升洗髓高品质灵根概率3%",},
{seq=69,param0=400,param1=400,param2=400,param3=400,param4=400,skill_desc="自身灵根完美度达到极品时，提升洗髓高品质灵根概率4%",},
{seq=70,param0=500,param1=500,param2=500,param3=500,param4=500,skill_desc="自身灵根完美度达到极品时，提升洗髓高品质灵根概率5%",},
{seq=71,param0=100,param1=0,skill_desc="自身灵根完美度达到极品时，提升金元素根支洗髓概率1%",},
{seq=72,param0=200,skill_desc="自身灵根完美度达到极品时，提升金元素根支洗髓概率2%",},
{seq=73,param0=300,skill_desc="自身灵根完美度达到极品时，提升金元素根支洗髓概率3%",},
{seq=74,param0=400,skill_desc="自身灵根完美度达到极品时，提升金元素根支洗髓概率4%",},
{seq=75,param0=500,skill_desc="自身灵根完美度达到极品时，提升金元素根支洗髓概率5%",},
{seq=76,type=5,param0=0,param3=0,skill_icon=9,skill_name="慧根·洗练",skill_desc="自身灵根完美度达到极品时，提升木元素根支洗髓概率1%",},
{seq=77,param1=200,skill_desc="自身灵根完美度达到极品时，提升木元素根支洗髓概率2%",},
{seq=78,param1=300,skill_desc="自身灵根完美度达到极品时，提升木元素根支洗髓概率3%",},
{seq=79,param1=400,skill_desc="自身灵根完美度达到极品时，提升木元素根支洗髓概率4%",},
{seq=80,param1=500,skill_desc="自身灵根完美度达到极品时，提升木元素根支洗髓概率5%",},
{seq=81,param2=100,skill_desc="自身灵根完美度达到极品时，提升水元素根支洗髓概率1%",},
{seq=82,param2=200,skill_desc="自身灵根完美度达到极品时，提升水元素根支洗髓概率2%",},
{seq=83,param2=300,skill_desc="自身灵根完美度达到极品时，提升水元素根支洗髓概率3%",},
{seq=84,param1=0,param2=400,skill_desc="自身灵根完美度达到极品时，提升水元素根支洗髓概率4%",},
{seq=85,param2=500,skill_desc="自身灵根完美度达到极品时，提升水元素根支洗髓概率5%",},
{seq=86,param3=100,skill_desc="自身灵根完美度达到极品时，提升火元素根支洗髓概率1%",},
{seq=87,param3=200,skill_desc="自身灵根完美度达到极品时，提升火元素根支洗髓概率2%",},
{seq=88,param3=300,skill_desc="自身灵根完美度达到极品时，提升火元素根支洗髓概率3%",},
{seq=89,param3=400,skill_desc="自身灵根完美度达到极品时，提升火元素根支洗髓概率4%",},
{seq=90,param1=0,param3=500,skill_desc="自身灵根完美度达到极品时，提升火元素根支洗髓概率5%",},
{seq=91,param1=0,param4=100,skill_desc="自身灵根完美度达到极品时，提升土元素根支洗髓概率1%",},
{seq=92,param4=200,skill_desc="自身灵根完美度达到极品时，提升土元素根支洗髓概率2%",},
{seq=93,param4=300,skill_desc="自身灵根完美度达到极品时，提升土元素根支洗髓概率3%",},
{seq=94,param4=400,skill_desc="自身灵根完美度达到极品时，提升土元素根支洗髓概率4%",},
{seq=95,param4=500,skill_desc="自身灵根完美度达到极品时，提升土元素根支洗髓概率5%",},
{seq=96,},
{seq=97,},
{seq=98,param2=314,skill_icon=4,skill_name="器灵·增强",},
{seq=99,},
{seq=100,},
{seq=101,},
{seq=102,param2=315,skill_name="器灵·增强",skill_desc="攻击目标时，概率提升抗暴几率1%，冷却时间180秒",},
{seq=103,},
{seq=104,},
{seq=105,},
{seq=106,},
{seq=107,},
{seq=108,param2=309,skill_desc="攻击目标时，概率提升连击几率1%，冷却时间180秒",},
{seq=109,},
{seq=110,},
{seq=111,param2=311,skill_desc="攻击目标时，概率提升格挡几率1%，冷却时间180秒",},
{seq=112,},
{seq=113,},
{seq=114,},
{seq=115,},
{seq=116,param2=310,skill_desc="攻击目标时，概率提升击穿几率1%，冷却时间180秒",},
{seq=117,},
{seq=118,},
{seq=119,},
{seq=120,},
{seq=121,},
{seq=122,},
{seq=123,},
{seq=124,},
{seq=125,param2=365,skill_icon=3,skill_desc="攻击目标时，概率提升击穿抵抗1%，冷却时间180秒",},
{seq=126,},
{seq=127,},
{seq=128,},
{seq=129,param2=317,skill_desc="攻击目标时，概率提升闪避几率1%，冷却时间180秒",},
{seq=130,},
{seq=131,},
{seq=132,},
{seq=133,},
{seq=134,},
{seq=135,param2=316,skill_desc="攻击目标时，概率提升命中几率1%，冷却时间180秒",},
{seq=136,},
{seq=137,},
{seq=138,},
{seq=139,},
{seq=140,type=7,param2=366,skill_icon=7,},
{seq=141,},
{seq=142,param2=367,skill_icon=2,skill_desc="攻击目标时，概率提升抗暴几率1%，冷却时间180秒",},
{seq=143,},
{seq=144,},
{seq=145,},
{seq=146,param2=357,skill_desc="攻击目标时，概率提升连击几率1%，冷却时间180秒",},
{seq=147,},
{seq=148,},
{seq=149,},
{seq=150,},
{seq=151,param2=359,skill_desc="攻击目标时，概率提升格挡几率1%，冷却时间180秒",},
{seq=152,},
{seq=153,},
{seq=154,},
{seq=155,},
{seq=156,},
{seq=157,param2=358,skill_desc="攻击目标时，概率提升击穿几率1%，冷却时间180秒",},
{seq=158,},
{seq=159,},
{seq=160,},
{seq=161,},
{seq=162,},
{seq=163,},
{seq=164,},
{seq=165,param2=369,skill_desc="攻击目标时，概率提升击穿抵抗1%，冷却时间180秒",},
{seq=166,},
{seq=167,},
{seq=168,},
{seq=169,param2=368,skill_desc="攻击目标时，概率提升闪避几率1%，冷却时间180秒",},
{seq=170,},
{seq=171,},
{seq=172,},
{seq=173,},
{seq=174,},
{seq=175,param2=360,skill_desc="攻击目标时，概率提升命中几率1%，冷却时间180秒",},
{seq=176,},
{seq=177,},
{seq=178,type=8,param2=263,skill_icon=1,skill_name="器灵·灵威",skill_desc="攻击目标时，概率提升攻击加成1%，冷却时间180秒",},
{seq=179,},
{seq=180,},
{seq=181,type=9,param0=100000,param2=264,skill_icon=5,skill_name="器灵·沐雨",skill_desc="生命低于100000并受到伤害时,概率生命加成1%,冷却时间180秒",},
{seq=182,},
{seq=183,},
{seq=184,},
{seq=185,},
{seq=186,type=9,param0=100000,param2=710,skill_icon=1,skill_name="器灵·金身",skill_desc="生命低于100000并受到伤害时,概率无敌1%,冷却时间180秒",},
{seq=187,},
{seq=188,},
{seq=189,},
{seq=190,},
{seq=191,type=11,param0=337,param1=180,param3=0,skill_icon=9,skill_name="器灵·狂暴",skill_desc="攻击麻痹的BOSS时，自身获得怪物增伤5%,冷却时间180",},
{seq=192,},
{seq=193,},
{seq=194,},
{seq=195,}
},

talent_skill_meta_table_map={
[139]=140,	-- depth:1
[138]=139,	-- depth:2
[137]=138,	-- depth:3
[136]=137,	-- depth:4
[124]=125,	-- depth:1
[123]=124,	-- depth:2
[121]=123,	-- depth:3
[122]=121,	-- depth:4
[100]=98,	-- depth:1
[97]=100,	-- depth:2
[99]=97,	-- depth:3
[96]=99,	-- depth:4
[142]=140,	-- depth:1
[141]=142,	-- depth:2
[144]=141,	-- depth:3
[102]=125,	-- depth:1
[145]=144,	-- depth:4
[129]=102,	-- depth:2
[103]=102,	-- depth:2
[135]=98,	-- depth:1
[134]=135,	-- depth:2
[133]=134,	-- depth:3
[132]=133,	-- depth:4
[131]=132,	-- depth:5
[130]=129,	-- depth:3
[143]=145,	-- depth:5
[108]=98,	-- depth:1
[128]=130,	-- depth:4
[126]=128,	-- depth:5
[110]=108,	-- depth:2
[111]=102,	-- depth:2
[112]=111,	-- depth:3
[113]=112,	-- depth:4
[114]=113,	-- depth:5
[115]=114,	-- depth:6
[116]=98,	-- depth:1
[127]=126,	-- depth:6
[117]=116,	-- depth:2
[119]=117,	-- depth:3
[120]=119,	-- depth:4
[107]=110,	-- depth:3
[146]=140,	-- depth:1
[106]=107,	-- depth:4
[105]=103,	-- depth:3
[104]=105,	-- depth:4
[118]=120,	-- depth:5
[109]=106,	-- depth:5
[147]=146,	-- depth:2
[149]=147,	-- depth:3
[175]=142,	-- depth:2
[174]=175,	-- depth:3
[173]=174,	-- depth:4
[172]=173,	-- depth:5
[171]=172,	-- depth:6
[148]=149,	-- depth:4
[169]=140,	-- depth:1
[168]=169,	-- depth:2
[167]=168,	-- depth:3
[166]=167,	-- depth:4
[165]=142,	-- depth:2
[164]=165,	-- depth:3
[163]=164,	-- depth:4
[170]=166,	-- depth:5
[101]=104,	-- depth:5
[150]=148,	-- depth:5
[151]=142,	-- depth:2
[152]=151,	-- depth:3
[153]=152,	-- depth:4
[154]=153,	-- depth:5
[155]=154,	-- depth:6
[162]=163,	-- depth:5
[157]=140,	-- depth:1
[158]=157,	-- depth:2
[159]=158,	-- depth:3
[160]=159,	-- depth:4
[161]=162,	-- depth:6
[156]=160,	-- depth:5
[179]=178,	-- depth:1
[180]=179,	-- depth:2
[177]=180,	-- depth:3
[176]=177,	-- depth:4
[21]=11,	-- depth:1
[26]=11,	-- depth:1
[31]=11,	-- depth:1
[16]=11,	-- depth:1
[36]=11,	-- depth:1
[182]=181,	-- depth:1
[183]=182,	-- depth:2
[184]=183,	-- depth:3
[185]=184,	-- depth:4
[187]=186,	-- depth:1
[188]=187,	-- depth:2
[189]=188,	-- depth:3
[190]=189,	-- depth:4
[20]=16,	-- depth:2
[25]=21,	-- depth:2
[24]=21,	-- depth:2
[23]=21,	-- depth:2
[22]=21,	-- depth:2
[29]=26,	-- depth:2
[19]=16,	-- depth:2
[18]=16,	-- depth:2
[17]=16,	-- depth:2
[15]=11,	-- depth:1
[14]=11,	-- depth:1
[13]=11,	-- depth:1
[12]=11,	-- depth:1
[30]=15,	-- depth:2
[32]=12,	-- depth:2
[33]=13,	-- depth:2
[34]=14,	-- depth:2
[35]=15,	-- depth:2
[37]=12,	-- depth:2
[38]=13,	-- depth:2
[39]=14,	-- depth:2
[40]=15,	-- depth:2
[27]=12,	-- depth:2
[28]=13,	-- depth:2
[47]=46,	-- depth:1
[48]=46,	-- depth:1
[49]=46,	-- depth:1
[10]=1,	-- depth:1
[9]=1,	-- depth:1
[8]=1,	-- depth:1
[7]=1,	-- depth:1
[6]=1,	-- depth:1
[5]=1,	-- depth:1
[4]=1,	-- depth:1
[3]=1,	-- depth:1
[2]=1,	-- depth:1
[192]=191,	-- depth:1
[193]=192,	-- depth:2
[194]=193,	-- depth:3
[50]=46,	-- depth:1
[195]=194,	-- depth:4
[71]=76,	-- depth:1
[73]=71,	-- depth:2
[74]=71,	-- depth:2
[75]=71,	-- depth:2
[77]=76,	-- depth:1
[90]=76,	-- depth:1
[89]=90,	-- depth:2
[88]=90,	-- depth:2
[87]=90,	-- depth:2
[86]=90,	-- depth:2
[78]=76,	-- depth:1
[72]=71,	-- depth:2
[79]=76,	-- depth:1
[80]=76,	-- depth:1
[84]=76,	-- depth:1
[82]=84,	-- depth:2
[83]=84,	-- depth:2
[85]=84,	-- depth:2
[91]=76,	-- depth:1
[92]=91,	-- depth:2
[93]=91,	-- depth:2
[94]=91,	-- depth:2
[81]=84,	-- depth:2
[52]=47,	-- depth:2
[51]=52,	-- depth:3
[58]=48,	-- depth:2
[53]=52,	-- depth:3
[54]=52,	-- depth:3
[55]=52,	-- depth:3
[56]=58,	-- depth:3
[57]=58,	-- depth:3
[45]=46,	-- depth:1
[44]=45,	-- depth:2
[43]=45,	-- depth:2
[42]=45,	-- depth:2
[41]=45,	-- depth:2
[95]=91,	-- depth:2
[59]=58,	-- depth:3
[60]=58,	-- depth:3
[61]=41,	-- depth:3
[62]=61,	-- depth:4
[63]=61,	-- depth:4
[64]=61,	-- depth:4
[65]=61,	-- depth:4
[66]=181,	-- depth:1
[67]=66,	-- depth:2
[68]=66,	-- depth:2
[70]=66,	-- depth:2
[69]=66,	-- depth:2
},
write_name={
{type=1,},
{type=1,desc="慧根",},
{type=1,desc="器灵",},
{type=2,desc="金",},
{seq=1,desc="木",},
{seq=2,desc="水",},
{seq=3,desc="火",},
{seq=4,desc="土",},
{type=3,desc="金灵·锋无痕",},
{seq=1,desc="木灵·青林",},
{seq=2,desc="渡劫技·金身",},
{seq=3,desc="渡劫技·赤心",},
{seq=4,desc="渡劫技·无垢",},
{desc="凡人境",},
{seq=1,desc="凝息境",},
{seq=2,desc="问宫境",},
{seq=3,desc="缚龙境",},
{seq=4,desc="燃灯境",},
{seq=5,desc="渡厄境",},
{seq=6,desc="蜕灵境",},
{seq=7,desc="种道境",},
{seq=8,desc="辟界境",},
{seq=9,desc="窃天境",},
{seq=10,desc="法则境",},
{seq=11,desc="劫火境",},
{seq=12,desc="归墟境",},
{seq=13,desc="掌劫境",},
{seq=14,desc="天衍境",},
{seq=15,desc="虚无境",},
{seq=16,desc="无上境",}
},

write_name_meta_table_map={
[13]=9,	-- depth:1
[12]=9,	-- depth:1
[11]=9,	-- depth:1
[10]=9,	-- depth:1
[8]=4,	-- depth:1
[7]=4,	-- depth:1
[6]=4,	-- depth:1
[5]=4,	-- depth:1
[3]=16,	-- depth:1
[2]=15,	-- depth:1
},
thank_text={
{},
{id=2,desc="恭喜%s道友渡劫成功，道友逆天而行，前途不可限量！",}
},

thank_text_meta_table_map={
},
other_default_table={thunder_hurt_level_limit=100,dujie_skill_1=90,dujie_skill_2=91,dujie_skill_3=92,skill_open_level_1=0,skill_open_level_2=1,skill_open_level_3=2,fail_damage_item_id=22090,body_item_1=22091,body_item_2=22092,body_item_3=22093,reset_item=22094,ordeal_scene_id=1003,ordeal_pos="283,284",ordeal_range=60,ordeal_scene_buff_id=50318,random_pos="268,306|300,303|300,265|265,262|273,298|290,298|304,289|304,277|292,267|282,254|267,261|252,266",body_limit_level=16,watch_reward_times=100,watch_reward_interval=2,watch_reward_item={[0]=item_table[9]},watch_reward_gold="50,100",box_reward_max_times=10,invite_cd=30,invite_show_reward={[0]=item_table[1],[1]=item_table[2],[2]=item_table[10],[3]=item_table[8],[4]=item_table[7],[5]=item_table[6],[6]=item_table[5],[7]=item_table[4],[8]=item_table[3]},},

level_default_table={level=0,zhuanzhi_limit=0,jingjie_limit=0,fail_damage_time=0,type=0,type_seq=1,vitality=0,vitality_limit=0,succ_reward_item={},attr_id1=101,attr_value1=0,attr_id2=102,attr_value2=0,attr_id3=103,attr_value3=0,attr_id4=104,attr_value4=0,attr_id5=111,attr_value5=0,open_func_desc="",open_icon_type="",},

ordeal_default_table={level=1,seq=0,ms_range="1000,1500",thunder="0,10000",},

thunder_name_default_table={type=1,thunder_txt="劫雷即将落下，造成<color=#fff8bb>少量伤害</color>。",},

thunder_default_table={seq=1,type=1,show_ms=0,hurt="4040,4445",},

skill_default_table={level=1,seq=0,cd_time=5,type=1,param1=1234,param2=0,param3=0,},

box_default_table={group=1,seq=0,weight=100,gather_id=3305,gather_time=3,duration_time=120,gather_reward_item={[0]=item_table[10]},},

vitality_default_table={zhuanzhi_level=0,vitality=100000,},

strength_default_table={jiengjie_level=0,strength=0,},

fail_reduce_default_table={times=1,duration_time=1800,hurt_reduce=500,},

type_default_table={type=1,name="凝息境",},

body_default_table={seq=0,element=0,body_name="木灵根",unlock_ordeal_level=0,unlock_item_id=0,unlock_item_num=0,body_desc="木灵根蕴含生机之力，主修者可大幅增强防御与恢复能力。其灵气如藤蔓缠绕，形成坚韧护盾，抵御伤害；同时滋养经脉，加速伤势愈合，持久战中稳若磐石。",},

body_socre_default_table={body_seq=0,level=1,need_score=0,skill_seq=1,skill_group=0,level_txt="劣质",},

talent_default_table={body_seq=0,seq=0,type=0,pre_seq=-1,active_cost="1,2",refine_cost="2,2",element_weight="0,100|1,100|2,100|3,100|4,100",color_weight="1,50|2,40|3,10|4,0|5,0",refine_element_weight="0,60|1,100|2,100|3,100|4,50",refine_color_weight="1,100|2,80|3,60|4,20|5,10",pos_x=5,pos_y=7,},

talent_score_default_table={body_seq=0,element_count=1,element_score0=400,element_score1=400,element_score2=400,element_score3=400,element_score4=400,},

talent_color_default_table={body_seq=1,seq=0,color=5,element_num="3,3",skill_seq="",choose_skill_cost="",},

talent_attr_default_table={talent_type=0,element=0,color=1,attr_id1=101,attr_value1=9231,attr_id2=104,attr_value2=223,attr_id3=105,attr_value3=0,},

talent_skill_default_table={seq=1,type=6,param0=1,param1=100,param2=0,param3=180,param4=0,param5=0,param6=0,param7=0,skill_icon=6,skill_name="器灵·削弱",skill_desc="攻击目标时，概率提升暴击几率1%，冷却时间180秒",},

write_name_default_table={type=4,seq=0,desc="根支",},

thank_text_default_table={id=1,desc="恭喜%s道友渡劫成功，祝道友修为大进，仙路长青。",}

}

