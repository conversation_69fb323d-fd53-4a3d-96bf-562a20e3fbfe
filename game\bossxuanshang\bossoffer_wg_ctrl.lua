require("game/bossxuanshang/bossoffer_view")
require("game/bossxuanshang/bossoffer_wg_data")

BossOfferWGCtrl = BossOfferWGCtrl or BaseClass(BaseWGCtrl)

function BossOfferWGCtrl:__init()
    if BossOfferWGCtrl.Instance then
        ErrorLog("[BossOfferWGCtrl] Attemp to create a singleton twice !")
    end
    BossOfferWGCtrl.Instance = self

    self.data = BossOfferWGData.New()
    self.view = BossOfferReward.New(GuideModuleName.BossOfferReward)
    self:RegisterAllProtocals()
end

function BossOfferWGCtrl:__delete()
    BossOfferWGCtrl.Instance = nil
    if self.data ~= nil then
        self.data:DeleteMe()
        self.data = nil
    end

    if self.view ~= nil then
        self.view:DeleteMe()
        self.view = nil
    end

    BossOfferWGCtrl.Instance = nil
end

function BossOfferWGCtrl:RegisterAllProtocals()
    self:RegisterProtocol(SCRABossXuanShangInfo, "OnSCRABossXuanShangInfo")
end

function BossOfferWGCtrl:OnSCRABossXuanShangInfo(protocol)
    --激活技能
    if protocol.active_skill_flag == 1 then
        local other_cfg = self.data:GetOtherCfg()
        local skill_data = SkillWGData.Instance:GetPassiveSkillByIndex(other_cfg[1].index)
        local data = {name = skill_data.name, desc = skill_data.desc, res_fun = ResPath.GetSkillIconById, icon = skill_data.icon}
        TipWGCtrl.Instance:ShowGetNewSkillView2(data)
    end
    RemindManager.Instance:Fire(RemindName.BossXuanShang)
    self.data:SetBossOfferInfo(protocol)
    
    if ViewManager.Instance:IsOpen(GuideModuleName.BossOfferReward) then
        ViewManager.Instance:FlushView(GuideModuleName.BossOfferReward)
    end
end

function BossOfferWGCtrl:SendReq(index, param_1, param_2)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
    protocol.rand_activity_type = ACTIVITY_TYPE.BOSS_XUAN_SHANG
    protocol.opera_type = index
    protocol.param_1 = param_1 or 0
    protocol.param_2 = param_2 or 0
    protocol:EncodeAndSend()
end

function BossOfferWGCtrl:JumToByID(boss_id)
    local enum = {
            [BossWGData.BossOpenType.WORLD_BOSS] = TabIndex.boss_world,
            [BossWGData.BossOpenType.VIP_BOSS] = TabIndex.boss_vip,
            [BossWGData.BossOpenType.PERSON_BOSS] = TabIndex.boss_personal,
            [BossWGData.BossOpenType.DABAO_BOSS] = TabIndex.boss_dabao,
           -- [BossWGData.BossOpenType.KF_BOSS] = TabIndex.worserv_boss_mh,
            [BossWGData.BossOpenType.SG_BOSS] = TabIndex.worserv_boss_sgyj,
            --[BossWGData.BossOpenType.HMSY_BOSS] = TabIndex.worserv_boss_hmsy,
            [BossWGData.BossOpenType.SY_BOSS] = TabIndex.world_new_shenyuan_boss
        }

    local boss_data = BossWGData.Instance:GetBossInfoByBossId(boss_id)
    local module_name
    local layer = 0
    local boss_index = 0
    local tab_index = 0
    local tips = ""
    local is_open = false

    if boss_data ~= nil then
        tab_index = enum[boss_data.boss_type]
        local fun_cfg = {}
        if boss_data.boss_type == BossWGData.BossOpenType.VIP_BOSS then
            module_name = GuideModuleName.Boss
            layer = boss_data.layer + 1
            boss_index = boss_data.boss_view_index
            is_open = FunOpen.Instance:GetFunIsOpenedByTabName("boss_vip")
            fun_cfg = FunOpen.Instance:GetFunByName("boss_vip")

        elseif boss_data.boss_type == BossWGData.BossOpenType.SG_BOSS then
            module_name = GuideModuleName.WorldServer
            layer = boss_data.layer
            boss_index = boss_data.boss_view_index
            is_open = FunOpen.Instance:GetFunIsOpenedByTabName("worserv_boss_sgyj")
            fun_cfg = FunOpen.Instance:GetFunByName("worserv_boss_sgyj")
        elseif boss_data.boss_type == BossWGData.BossOpenType.DABAO_BOSS then
            module_name = GuideModuleName.Boss
            layer = boss_data.layer
            boss_index = boss_data.boss_view_index
            BossWGData.Instance:SetBossJumpInfo(enum[boss_data.boss_type], boss_data.layer, boss_data.boss_view_index)
            is_open = FunOpen.Instance:GetFunIsOpenedByTabName("boss_dabao")
            fun_cfg = FunOpen.Instance:GetFunByName("boss_dabao")
        elseif boss_data.boss_type == BossWGData.BossOpenType.WORLD_BOSS then
            module_name = GuideModuleName.Boss
            layer = boss_data.layer
            boss_index = boss_data.boss_view_index
            is_open = FunOpen.Instance:GetFunIsOpenedByTabName("boss_world") 
            fun_cfg = FunOpen.Instance:GetFunByName("boss_world")  
        elseif boss_data.boss_type == BossWGData.BossOpenType.PERSON_BOSS then
            module_name = GuideModuleName.Boss
            layer = boss_data.layer
            boss_index = boss_data.boss_view_index
            is_open = FunOpen.Instance:GetFunIsOpenedByTabName("boss_personal")
            fun_cfg = FunOpen.Instance:GetFunByName("boss_personal")  
        elseif boss_data.boss_type == BossWGData.BossOpenType.SY_BOSS then
            module_name = GuideModuleName.WorldServer
            layer = boss_data.layer
            boss_index = boss_data.boss_view_index
            is_open = FunOpen.Instance:GetFunIsOpenedByTabName("world_new_shenyuan_boss")
            fun_cfg = FunOpen.Instance:GetFunByName("world_new_shenyuan_boss")  
        end

        if module_name then
            BossWGData.Instance:SetBossJumpInfo(tab_index, layer, boss_index)
            if is_open then
                ViewManager.Instance:Open(module_name, tab_index, "jum_info", {boss_list_index = boss_index or 1, boss_list_layer = layer, boss_type = tab_index})
            else
                if fun_cfg and fun_cfg.trigger_type == 3 then
                    tips = string.format(Language.Boss.OfferTips4, fun_cfg.trigger_param, fun_cfg.show_name)
                    SysMsgWGCtrl.Instance:ErrorRemind(tips)
                else
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.OfferTips3)
                end
            end
        end

        return
    end
end