function GameAssistantView:LoadAssistantXianYu()
    -- 活跃获得
	if not self.must_xianyu_list then
		self.must_xianyu_list = AsyncListView.New(AssistantXianYuRender, self.node_list.must_xianyu_list)
	end

    -- 超值特惠
    if not self.must_chaozhi_list then
		self.must_chaozhi_list = AsyncListView.New(AssistantXianYuRender, self.node_list.must_chaozhi_list)
    end
    
    if not self.assistant_xianyu_show_alpha then
        self.assistant_xianyu_show_alpha = self.node_list.assistant_xianyu_show_root:GetOrAddComponent(typeof(UGUITweenAlpha))
    end
end


function GameAssistantView:ReleaseAssistantXianYu()
    if self.must_xianyu_list then
        self.must_xianyu_list:DeleteMe()
        self.must_xianyu_list = nil
    end

    if self.must_chaozhi_list then
        self.must_chaozhi_list:DeleteMe()
        self.must_chaozhi_list = nil
    end

    self.assistant_xianyu_show_alpha = nil
    self:RemoveXianYuDelayTimer()
end

function GameAssistantView:CloseAssistantXianYu()

end

function GameAssistantView:ShowAssistantXianYu()

end

-- 刷新界面
function GameAssistantView:FlushAssistantXianYu(param_t)
	for k, v in pairs(param_t) do
		if k == "all" then
            self:FlushAssistantXianYuMessage()
		end
	end
end

function GameAssistantView:FlushAssistantXianYuMessage()
    local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    self.node_list.must_show_root:CustomSetActive(self.show_index == TabIndex.assistant_today_must)
    self.node_list.want_show_root:CustomSetActive(self.show_index == TabIndex.assistant_want_lingyu)
    local list = nil

    if self.show_index == TabIndex.assistant_today_must then
        list = GameAssistantWGData.Instance:GetBiMaiCfgListByDay(cur_day)
    else
        list = GameAssistantWGData.Instance:GetXianYuCfgListByDay(cur_day)
    end

    local type_list_1 = {}
    local type_list_2 = {}

    if list then
        for i, data in ipairs(list) do
            if data.show_type == 1 then
                table.insert(type_list_1, data)
            else
                table.insert(type_list_2, data)
            end
        end
    end

    self.node_list.chao_zhi_root:CustomSetActive(#type_list_2 > 0)

    self:RemoveXianYuDelayTimer()
    self.show_delay_timer = GlobalTimerQuest:AddDelayTimer(function ()
        if self.must_xianyu_list then
            self.must_xianyu_list:SetDataList(type_list_1)
        end

        if self.must_chaozhi_list then
            self.must_chaozhi_list:SetDataList(type_list_2)
        end

        if self.assistant_xianyu_show_alpha then
            self.assistant_xianyu_show_alpha:ResetToBeginning()
        end
    end, 0.1)
end

--移除回调
function GameAssistantView:RemoveXianYuDelayTimer()
    if self.show_delay_xianyu_timer then
        GlobalTimerQuest:CancelQuest(self.show_delay_xianyu_timer)
        self.show_delay_xianyu_timer = nil
    end
end

-----------------------------------------------------------------------------
AssistantXianYuRender = AssistantXianYuRender or BaseClass(BaseRender)
function AssistantXianYuRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_go_to, BindTool.Bind(self.OnClickGoToPath, self))
end

function AssistantXianYuRender:OnClickGoToPath()
    if not self.data then
		return
	end

    FunOpen.Instance:OpenViewNameByCfg(self.data.get_way_path)
end

function AssistantXianYuRender:OnFlush()
	if not self.data then
		return
	end

    if self.data.get_way_icon and self.data.get_way_icon ~= "" then
        local bundle, asset = ResPath.GetMainUIIcon(self.data.get_way_icon)
        self.node_list.icon.image:LoadSprite(bundle, asset, function()
            self.node_list.icon.image:SetNativeSize()
        end)
    end

    self.node_list.title.text.text = self.data.get_way_title
    self.node_list.desc.text.text = self.data.get_way_desc

    local star = 0
    if self.data.get_way_star and self.data.get_way_star ~= "" then
        star = self.data.get_way_star
    end
    for i = 1, 5 do
        local star_str = i <= star and "a3_ty_sp_xx_zc4" or "a3_ty_xx_zc0"
        self.node_list[string.format("star_%d", i)].image:LoadSprite(ResPath.GetCommonImages(star_str))
    end
end