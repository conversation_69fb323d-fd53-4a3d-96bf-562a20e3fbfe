XuYuanFreshPoolView = XuYuanFreshPoolView or BaseClass(SafeBaseView)

local page_show_num = 6

function XuYuanFreshPoolView:__init()
	self:SetMaskBg()
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
    self.view_style = ViewStyle.Half
    -- self.default_index = 10
	self.is_safe_area_adapter = true

	-- self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
    self:AddViewResource(0, "uis/view/xuyuan_freshpool_ui_prefab", "xuyuan_freshpool_view")
	-- self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar_Activity")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
	self:SetTabShowUIScene(0, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.DUDI_FUSHI})
end

function XuYuanFreshPoolView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.XuYuanFreshPool.TitleName

	self:ChangeBg()
	-- self:InitTabbar()
	self:InitMoneyBar()

	self:InitShowAwardPage()

	self.node_list.desc.text.text = Language.XuYuanFreshPool.PanelDesc
	self.draw_anim_sp = self.node_list.draw_anim.gameObject:GetComponent("SkeletonGraphic")

	for i = 1, 3 do
		self.node_list["btn_xy_buy_" .. i].button:AddClickListener(BindTool.Bind2(self.OnClickDraw, self, i))
	end
	self.node_list["my_totle_price_icon"].button:AddClickListener(BindTool.Bind(self.ShowItemTips, self, 1))

    self:LoginTimeCountDown() --活动倒计时

    if self.item_data_change == nil then
        self.item_data_change = BindTool.Bind(self.OnItemChange, self)
        ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change)
    end

    if not self.xy_leiji_list then
        self.xy_leiji_list = AsyncListView.New(XuYuanFreshLeijiRender, self.node_list["xy_leiji_list"])
		local h = self.node_list["xy_leiji_list"].rect.rect.height
		self.ignore_num = h / 94 -- 94是item的高度
	end

	if not self.big_item_cell then
		self.big_item_cell = ItemCell.New(self.node_list.big_item_cell)
		-- self.big_item_cell:SetIsUseRoundQualityBg(true)
		self.big_item_cell:NeedDefaultEff(false)
		self.big_item_cell:SetCellBgEnabled(false)
		self.big_item_cell:SetShowCualityBg(false)
	end


	--人物模型
	if self.role_model == nil then
		self.role_model = RoleModel.New()
		-- local display_data = {
		-- 	parent_node = self.node_list["role_display"],
		-- 	camera_type = MODEL_CAMERA_TYPE.BASE,
		-- 	-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		-- 	rt_scale_type = ModelRTSCaleType.S,
		-- 	can_drag = false,
		-- }
		
		-- self.role_model:SetRenderTexUI3DModel(display_data)
		self.role_model:SetUISceneModel(self.node_list["role_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.role_model, 0)
	end

	--坐骑模型
	if self.mount_model == nil then
		self.mount_model = RoleModel.New()
		-- local display_data = {
		-- 	parent_node = self.node_list["mount_display"],
		-- 	camera_type = MODEL_CAMERA_TYPE.BASE,
		-- 	-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		-- 	rt_scale_type = ModelRTSCaleType.M,
		-- 	can_drag = false,
		-- }
		
		-- self.mount_model:SetRenderTexUI3DModel(display_data)

		self.mount_model:SetUISceneModel(self.node_list["mount_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.mount_model, 0)
	end

	--宠物模型
	if self.pet_model == nil then
		self.pet_model = RoleModel.New()
		-- local display_data = {
		-- 	parent_node = self.node_list["pet_display"],
		-- 	camera_type = MODEL_CAMERA_TYPE.BASE,
		-- 	-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		-- 	rt_scale_type = ModelRTSCaleType.S,
		-- 	can_drag = false,
		-- }
		
		-- self.pet_model:SetRenderTexUI3DModel(display_data)

		self.pet_model:SetUISceneModel(self.node_list["pet_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.pet_model, 0)
	end

	--武魂模型
	if self.wuhun_model == nil then
		self.wuhun_model = RoleModel.New()
		-- local display_data = {
		-- 	parent_node = self.node_list["wuhun_display"],
		-- 	camera_type = MODEL_CAMERA_TYPE.BASE,
		-- 	-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		-- 	rt_scale_type = ModelRTSCaleType.S,
		-- 	can_drag = false,
		-- }
		
		-- self.wuhun_model:SetRenderTexUI3DModel(display_data)

		self.wuhun_model:SetUISceneModel(self.node_list["wuhun_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.wuhun_model, 0)
	end

	--法阵模型
	if self.faZheng_model == nil then
		self.faZheng_model = RoleModel.New()
		-- local display_data = {
		-- 	parent_node = self.node_list["faZheng_display"],
		-- 	camera_type = MODEL_CAMERA_TYPE.BASE,
		-- 	-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		-- 	rt_scale_type = ModelRTSCaleType.L,
		-- 	can_drag = false,
		-- }
		
		-- self.faZheng_model:SetRenderTexUI3DModel(display_data)

		
		self.faZheng_model:SetUISceneModel(self.node_list["faZheng_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.faZheng_model, 0)
	end

	--幻兽模型
	if self.beasts_model == nil then
		self.beasts_model = RoleModel.New()
		-- local display_data = {
		-- 	parent_node = self.node_list["beasts_display"],
		-- 	camera_type = MODEL_CAMERA_TYPE.BASE,
		-- 	-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		-- 	rt_scale_type = ModelRTSCaleType.L,
		-- 	can_drag = false,
		-- }
		
		-- self.beasts_model:SetRenderTexUI3DModel(display_data)

		
		self.beasts_model:SetUISceneModel(self.node_list["beasts_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.beasts_model, 0)
	end

	self:FlushModel()

    XUI.AddClickEventListener(self.node_list["all_reward_btn"], BindTool.Bind(self.OnClickRewardPreview, self))
    -- XUI.AddClickEventListener(self.node_list["gailv_btn"], BindTool.Bind(self.OnClickGaiLv, self))
    XUI.AddClickEventListener(self.node_list["record_btn"], BindTool.Bind(self.OnClickBtnDrawRecord, self))
    XUI.AddClickEventListener(self.node_list["big_reward_get_btn"], BindTool.Bind(self.OnClickGetBigRewardBtn, self))
    XUI.AddClickEventListener(self.node_list["shop_btn"], BindTool.Bind(self.GoToShop, self))
    XUI.AddClickEventListener(self.node_list["pass_anim"], BindTool.Bind(self.ClickPassAnim, self))
    XUI.AddClickEventListener(self.node_list["show_big_award"], BindTool.Bind(self.OpenItemTips, self))
end

function XuYuanFreshPoolView:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("xuyuan_freshpool_down") then
		CountDownManager.Instance:RemoveCountDown("xuyuan_freshpool_down")
	end

	self:CancelQuest()

    if self.item_data_change then
        ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
        self.item_data_change = nil
    end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

    if self.xy_leiji_list then
        self.xy_leiji_list:DeleteMe()
        self.xy_leiji_list = nil
    end

	if self.big_item_cell then
		self.big_item_cell:DeleteMe()
		self.big_item_cell = nil
	end

	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	if self.mount_model then
		self.mount_model:DeleteMe()
		self.mount_model = nil
	end

	if self.pet_model then
		self.pet_model:DeleteMe()
		self.pet_model = nil
	end

	if self.wuhun_model then
		self.wuhun_model:DeleteMe()
		self.wuhun_model = nil
	end

	if self.faZheng_model then
		self.faZheng_model:DeleteMe()
		self.faZheng_model = nil
	end
	
	if self.beasts_model then
		self.beasts_model:DeleteMe()
		self.beasts_model = nil
	end

	if self.pool_list_view then
		self.pool_list_view:DeleteMe()
		self.pool_list_view = nil
	end

	self.page_toggle_list = nil
	self.is_play_draw_anim = nil
	self.cur_jump_index = nil
end

--更新日志和协议信息
function XuYuanFreshPoolView:OpenCallBack()
    XuYuanFreshPoolWGCtrl.Instance:SendReq(RA_YANHUA_SHENGDIAN3_OP_TYPE.INFO)
    XuYuanFreshPoolWGCtrl.Instance:SendReq(RA_YANHUA_SHENGDIAN3_OP_TYPE.RECORD)

	if self.role_model then
		self.role_model:PlayLastAction()
	end
end

function XuYuanFreshPoolView:ShowIndexCallBack()
    ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.XuYuanFreshPoolView, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_YANHUA_SHENGDIAN_3)
end

function XuYuanFreshPoolView:ChangeBg()
	local cfg = XuYuanFreshPoolWGData.Instance:GetCurCfg()
	if cfg and cfg.ui_scene_index and cfg.ui_scene_index ~= "" then
		self.ui_scene_change_config_index = cfg.ui_scene_index
		Scene.Instance:ChangeUISceneController(UI_SCENE_TYPE.DEFAULT, UI_SCENE_CONFIG_INDEX.DUDI_FUSHI, cfg.ui_scene_index)
	end
end

--参数刷新
function XuYuanFreshPoolView:OnFlush(param)
    for k, v in pairs(param) do
        if "all" == k then
            self:FlushViewShow()
        elseif "flush_model" == k then
            self:FlushViewShow()
            self:FlushModel()
        end
    end
end

function XuYuanFreshPoolView:InitMoneyBar()
    if not self.money_bar then
        self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local cfg = XuYuanFreshPoolWGData.Instance:GetConsumeCfg()
		cfg = cfg and cfg[1] and cfg[1].yanhua_item
        local show_params = {
			show_gold = true, 

			--自定义货币.
			show_custom_1 = cfg and cfg.item_id ~= nil,
			custom_item_id_1 = cfg and cfg.item_id or 0,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
        self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
    end
end

function XuYuanFreshPoolView:InitTabbar()
	local toggle_name_list = { Language.XuYuanFreshPool.TitleName }
	local bundle_name = "uis/view/open_server_activity_ui_prefab"
	local common_path = "uis/view/common_panel_prefab"
	self.tabbar = Tabbar.New(self.node_list, "VerticalTabbar_Activity")
	self.tabbar:SetVerTabbarCellName("VerticalTabbarCell_Activity")
	self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	self.tabbar:Init(toggle_name_list, nil, common_path, bundle_name, {})
end

function XuYuanFreshPoolView:FlushViewShow()
	self:FlushBtnShow()
	self:FlushBaoDi()
	self:FlushShowCell()
	self:FlushShowAwardPage()
	self:FlushPassAnim()
end

function XuYuanFreshPoolView:FlushShowCell()
    local leiji_list = XuYuanFreshPoolWGData.Instance:GetFanliList()
    if leiji_list then
		self.xy_leiji_list:SetDataList(leiji_list)
		ReDelayCall(self, function ()
			self:AutoJump(leiji_list)
		end, 0, "delay_jump_to_index")
    end

	local draw_times = XuYuanFreshPoolWGData.Instance:GetCurDrawTimes()
	local item_data = XuYuanFreshPoolWGData.Instance:GetBigRewardData()
	local is_can_get = false
	local has_get = true
	if item_data then
		local reward_item = item_data.reward_item and item_data.reward_item[0] or nil
		if reward_item then
			self.big_item_cell:SetData(reward_item)
			self.big_item_cell:SetBindIconVisible(false)
		end

		has_get = XuYuanFreshPoolWGData.Instance:GetFanliHasGet(item_data.index)
		is_can_get = draw_times >= item_data.lotto_num

		local desc = ""
		if is_can_get then
			desc = not has_get and Language.XuYuanFreshPool.CanGetBigRewardText or ""

			self.node_list.big_reward_ylq:SetActive(has_get)
		else
			local color = is_can_get and COLOR3B.C8 or COLOR3B.C10
			local origin_time = item_data.lotto_num - draw_times
			local str = origin_time--math.ceil((origin_time) / 100) * 1

			desc = string.format(Language.XuYuanFreshPool.BigRewardText, ToColorStr(str, color))
		end
		self.node_list.big_reward_text.text.text = desc
	end

	self.node_list.big_reward_get_btn:SetActive(is_can_get and not has_get)
	self.node_list.eff_3:SetActive(is_can_get and not has_get)

	self.node_list.draw_total_text.text.text = string.format(Language.XuYuanFreshPool.TxtTimes, draw_times)
end

--3个按钮刷新，传参数只刷新数量
function XuYuanFreshPoolView:FlushBtnShow(is_flush_num)
	local cfg = XuYuanFreshPoolWGData.Instance:GetConsumeCfg()	
    local item_cfg
    local count, icon_id
    for i = 1, 3 do
        if cfg[i] then
            local item_id = cfg[i].yanhua_item.item_id
            if not is_flush_num then
                item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
                -- if not IsEmptyTable(item_cfg) then               
                --     --道具图标
                --     self.node_list["xy_icon_" .. i].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
				-- 	icon_id = item_cfg.icon_id
                -- end
                --按钮价格
                self.node_list["txt_xy_buy_" .. i].text.text = string.format(Language.XuYuanFreshPool.TxtBuy, cfg[i].onekey_lotto_num)
                -- --折扣
                self.node_list["xy_discount_" .. i]:SetActive(cfg[i].discount_text ~= "")
                if cfg[i].discount_text ~= "" then
                    self.node_list["txt_xy_discount_" .. i].text.text = string.format(Language.Recharge.DiscountText, cfg[i].discount_text)
                end
            end
            --道具红点数量
            count = ItemWGData.Instance:GetItemNumInBagById(item_id)
            self.node_list["xy_red_" .. i]:SetActive(count >= cfg[i].yanhua_item.num)
            -- self.node_list["xy_red_num_" .. i].text.text = cfg[i].yanhua_item.num
        end
    end

	-- self.node_list["my_totle_price_icon"].image:LoadSprite(ResPath.GetItem(icon_id))
	-- self.node_list["my_totle_price"].text.text = count
end

-- 保底次数
function XuYuanFreshPoolView:FlushBaoDi()
	local draw_times = XuYuanFreshPoolWGData.Instance:GetCurDrawTimes()
	local grade_cfg = XuYuanFreshPoolWGData.Instance:GetGradeCfg()
	local baodi_num = grade_cfg and grade_cfg.baodi_count or 100
	local num = baodi_num - (draw_times % baodi_num)
	self.node_list.baodi_tip.text.text = string.format(Language.XuYuanFreshPool.BaoDiTip, num)
end

--刷新中间模型区域
function XuYuanFreshPoolView:FlushModel()
	local show_list = XuYuanFreshPoolWGData.Instance:GetShowModelCfg()
	if IsEmptyTable(show_list) then
		return
	end

	self.role_model:RemoveAllModel()
	self.node_list["mount_display"]:CustomSetActive(false)
	self.node_list["faZheng_display"]:CustomSetActive(false)
	self.node_list["pet_display"]:CustomSetActive(false)
	self.node_list["wuhun_display"]:CustomSetActive(false)

	local user_model_data = {}
	user_model_data.body_res_id = AppearanceWGData.Instance:GetRoleResId()
	local fashion_cfg, set_role_model = nil, true
	for k, data in pairs(show_list) do
		if data.sys_id == DISPLAY_SYSTEM_TYPE.Fashion and data.param1 == SHIZHUANG_TYPE.BODY then -- 时装大类
			fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
			if fashion_cfg then                                                          -- 时装	
				local prof = GameVoManager.Instance:GetMainRoleVo().prof
				user_model_data.body_res_id = ResPath.GetFashionModelId(prof, fashion_cfg.resouce)
			end
		elseif data.sys_id == DISPLAY_SYSTEM_TYPE.Mount then -- 坐骑
			fashion_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByImageId(MOUNT_LINGCHONG_APPE_TYPE.MOUNT,
				data.param1)
			if fashion_cfg then
				-- self:SetMountModelData(fashion_cfg.appe_image_id)

				user_model_data.mount_res_id = fashion_cfg.appe_image_id
				user_model_data.mount_action = MOUNT_RIDING_TYPE[1]
				local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(user_model_data.mount_res_id)
				if not IsEmptyTable(action_cfg) then
					user_model_data.mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
				end
			end
		elseif data.sys_id == DISPLAY_SYSTEM_TYPE.Pet then -- 宠物
			fashion_cfg = NewAppearanceWGData.Instance:GetLingChongActCfgByImageId(data.param1)
			if fashion_cfg then
				self:SetPetModelData(fashion_cfg.appe_image_id)
			end
		elseif data.sys_id == DISPLAY_SYSTEM_TYPE.Huakun then -- 化鲲
			fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
			if fashion_cfg then
				-- self:SetMountModelData(fashion_cfg.active_id)

				user_model_data.mount_res_id = fashion_cfg.active_id
				user_model_data.mount_action = MOUNT_RIDING_TYPE[1]
				local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(user_model_data.mount_res_id)
				if not IsEmptyTable(action_cfg) then
					user_model_data.mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
				end
			end
		elseif data.sys_id == DISPLAY_SYSTEM_TYPE.WuShen then --武魂
			local item_id = data.item_id or 0
			self:SetWuhunModelData(item_id)
			set_role_model = false
			break
		elseif data.sys_id == DISPLAY_SYSTEM_TYPE.FaZheng then --法阵
			local item_id = data.item_id or 0
			self:SetFaZhengModelData(item_id)
			break
		elseif data.sys_id == DISPLAY_SYSTEM_TYPE.CangMing then --沧溟
			local item_id = data.item_id or 0
			self:SetCangMingModelData(item_id)
			set_role_model = false
			break
		elseif data.sys_id == DISPLAY_SYSTEM_TYPE.TianShen then --天神
			local item_id = data.item_id or 0
			self:SetTianShenModelData(item_id)
			set_role_model = false
			break
		elseif data.sys_id == DISPLAY_SYSTEM_TYPE.Beasts then --幻兽
			local item_id = data.item_id or 0
			self:SetBeastsModelData(item_id)
			set_role_model = false
			break
		end
	end

	--武魂等系统不需要显示角色模型
	if set_role_model then
		local action = user_model_data.mount_action or SceneObjAnimator.Walk
		local extra_role_model_data = {
			animation_name = action,
		}

		if user_model_data.body_res_id then
			self.role_model:SetRoleResid(user_model_data.body_res_id, nil, extra_role_model_data)
		else
			local role_vo = GameVoManager.Instance:GetMainRoleVo()
			self.role_model:SetModelResInfo(role_vo, nil, extra_role_model_data)
		end

		if user_model_data.mount_res_id then
			self.role_model:SetMountResid(user_model_data.mount_res_id)
		end

		-- 处理没有正确播放动作
		ReDelayCall(self, function()
			self.role_model:PlayLastAction()
		end, 0, "xuyuan_delay_play")
		self.role_model:FixToOrthographicOnUIScene()
		-- self.role_model:FixToOrthographic(self.root_node_transform)
	end

	for k, v in pairs(show_list) do
		self:ShowModelByData(v)
	end

	self:SetModelTrans()
end

function XuYuanFreshPoolView:ShowModelByData(data)
	if IsEmptyTable(data) then
		return
	end
	local res_id, fashion_cfg
	if data.sys_id == DISPLAY_SYSTEM_TYPE.Fashion then -- 时装大类
		fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
		if fashion_cfg then
			res_id = fashion_cfg.resouce
			if data.param1 == SHIZHUANG_TYPE.MASK then -- 脸饰
				self.role_model:SetMaskResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.BELT then -- 腰饰
				self.role_model:SetWaistResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.WEIBA then -- 尾巴
				self.role_model:SetTailResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.SHOUHUAN then -- 手环
				self.role_model:SetShouHuanResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.HALO then -- 光环
				self.role_model:SetHaloResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.WING then -- 羽翼
				self.role_model:SetWingResid(res_id, true)
			elseif data.param1 == SHIZHUANG_TYPE.FABAO then -- 法宝
				self.role_model:SetBaoJuResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.JIANZHEN then -- 剑阵
				self.role_model:SetJianZhenResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.SHENBING then -- 武器
				res_id = AppearanceWGData.GetFashionWeaponIdByResViewId(res_id)
				self.role_model:SetWeaponResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.FOOT then -- 足迹
				self.role_model:SetFootTrailModel(res_id)
				self.role_model:SetRotation(MODEL_ROTATION_TYPE.FOOT)
				-- self.is_foot_view = true
				-- self.foot_effect_id = res_id
				-- if not self.use_update then
				-- 	Runner.Instance:AddRunObj(self, 8)
				-- 	self.use_update = true
				-- end
			end
		end
	end
end

function XuYuanFreshPoolView:SetMountModelData(res_id)
	self.mount_model:ClearModel()
	if IsEmptyTable(self.mount_model) then
		return
	end

	self.node_list["mount_display"]:CustomSetActive(true)

	local bundle, asset = ResPath.GetMountModel(res_id)
	self.mount_model:SetMainAsset(bundle, asset, function()
		self.mount_model:PlayMountAction()
	end)

	self.mount_model:FixToOrthographic(self.root_node_transform)
end

function XuYuanFreshPoolView:SetWuhunModelData(item_id)
	self.wuhun_model:RemoveAllModel()
	local wuhun_cfg = WuHunWGData.Instance:GetWuhunResByItemId(item_id)
	if IsEmptyTable(wuhun_cfg) or IsEmptyTable(self.wuhun_model) then
		return
	end

	self.node_list["wuhun_display"]:CustomSetActive(true)

	local appe_id = wuhun_cfg and wuhun_cfg.appe_image_id or 10114
	local bundle, asset = ResPath.GetWuHunModel(appe_id)
	self.wuhun_model:SetMainAsset(bundle, asset, function()
		self.wuhun_model:PlayRoleAction(SceneObjAnimator.Rest1)
	end)

	local front_use_index = WuHunFrontWGData.Instance:GetWuHunFrontHuanHuaIndex(wuhun_cfg.wuhun_id)
	local cfg = WuHunFrontWGData.Instance:GetSoulFormationAppimageCfg(wuhun_cfg.wuhun_id, front_use_index)

	if cfg then
		self.wuhun_model:SetSoulFormationResid(cfg.app_image_id, wuhun_cfg.wuhun_id)
	else
		self.wuhun_model:RemoveSoulFormation()
	end
end

function XuYuanFreshPoolView:SetPetModelData(res_id)
	if IsEmptyTable(self.pet_model) then
		return
	end

	self.node_list["pet_display"]:CustomSetActive(true)
	self.pet_model:ClearModel()
	local bundle, asset = ResPath.GetPetModel(res_id)

	self.pet_model:SetMainAsset(bundle, asset, function()
		self.pet_model:PlaySoulAction()
	end)

	self.pet_model:FixToOrthographic(self.root_node_transform)
end

function XuYuanFreshPoolView:SetFaZhengModelData(item_id)
	self.faZheng_model:RemoveAllModel()
	local cfg = SupremeFieldsWGData.Instance:GetFootStoneByItemID(item_id)
	if IsEmptyTable(self.faZheng_model) or IsEmptyTable(cfg) then
		return
	end

	self.node_list["faZheng_display"]:CustomSetActive(true)
	local bundle, asset = ResPath.GetSkillFaZhenModel(cfg.type)
	self.faZheng_model:SetMainAsset(bundle, asset)
end

function XuYuanFreshPoolView:SetCangMingModelData(item_id)
	local cfg = FiveElementsWGData.Instance:GetWaistStoneByItemID(item_id)
	if IsEmptyTable(self.role_model) or IsEmptyTable(cfg) then
		return
	end

	self.role_model:RemoveAllModel()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	self.role_model:SetModelResInfo(role_vo, nil, function()
		self.role_model:PlayRoleShowAction()
	end)

	self.role_model:SetSkillHaloResid(cfg.type)
	self.role_model:FixToOrthographic(self.root_node_transform)
end

function XuYuanFreshPoolView:SetBeastsModelData(item_id)
	local res_id = ControlBeastsWGData.Instance:GetBeastModelResId(item_id)
	if IsEmptyTable(self.beasts_model) or res_id == 0 then
		return
	end
	local bundle, asset = ResPath.GetBeastsModel(res_id)
	self.beasts_model:SetMainAsset(bundle, asset)
	self.beasts_model:PlayRoleAction(SceneObjAnimator.Rest)
end

function XuYuanFreshPoolView:SetTianShenModelData(item_id)
	local show_id = ItemWGData.Instance:GetComposeProductid(item_id)
	local magic_image = TianShenWGData.Instance:GetAppeImage(show_id)
	if not magic_image then
		magic_image = TianShenHuamoWGData.Instance:GetHuaMoAttributeCfgByItem(show_id)
	end
	if not magic_image then
		return
	end

	self.role_model:SetTianShenModel(magic_image.appe_image_id, magic_image.index, false)
end

function XuYuanFreshPoolView:SetModelTrans()
	local data = XuYuanFreshPoolWGData.Instance:GetModelDetailData()
	if IsEmptyTable(data) then
		return
	end

	local pos_str = data.main_pos
	if pos_str and pos_str ~= "" then
		local pos = Split(pos_str, "|")
		RectTransform.SetAnchoredPositionXY(self.node_list.role_display.rect, pos[1] or 0, pos[2] or 0)
	end

	local rotate_str = data.main_rot
	if rotate_str and rotate_str ~= "" then
		local rot = Split(rotate_str, "|")
		if self.role_model then
			self.role_model:SetUSAdjustmentNodeLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
			-- self.role_model:SetRTAdjustmentRootLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
		end
	end

	local scale = data.main_scale
	if scale and scale ~= "" then
		if self.role_model then
			self.role_model:SetUSAdjustmentNodeLocalScale(scale)
			-- self.role_model:SetRTAdjustmentRootLocalScale(scale)
		end
	end

	--灵宠
	if self.node_list["pet_display"]:GetActive() then
		pos_str = data.pet_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			RectTransform.SetAnchoredPositionXY(self.node_list.pet_display.rect, pos[1] or 0, pos[2])
		end

		rotate_str = data.pet_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.pet_model then
				self.pet_model:SetUSAdjustmentNodeLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
				-- self.pet_model:SetRTAdjustmentRootLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
			end
		end

		scale = data.lc_scale
		if scale and scale ~= "" then
			if self.pet_model then
				self.pet_model:SetUSAdjustmentNodeLocalScale(scale)
				-- self.pet_model:SetRTAdjustmentRootLocalScale(scale)
			end
		end
	end

	--坐骑
	if self.node_list["mount_display"]:GetActive() then
		pos_str = data.mount_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			RectTransform.SetAnchoredPositionXY(self.node_list.mount_display.rect, pos[1] or 0, pos[2] or 0)
		end

		rotate_str = data.mount_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.mount_model then
				self.mount_model:SetUSAdjustmentNodeLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
				-- self.mount_model:SetRTAdjustmentRootLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
			end
		end

		scale = data.mount_scale
		if scale and scale ~= "" then
			if self.mount_model then
				self.mount_model:SetUSAdjustmentNodeLocalScale(scale)
				-- self.mount_model:SetRTAdjustmentRootLocalScale(scale)
			end
		end
	end

	--法阵.
	if self.node_list["faZheng_display"]:GetActive() then
		pos_str = data.whole_fz_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			RectTransform.SetAnchoredPositionXY(self.node_list.faZheng_display.rect, pos[1] or 0, pos[2] or 0)
		end

		pos_str = data.fz_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			if self.faZheng_model then
				self.faZheng_model:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
				-- self.faZheng_model:SetRTAdjustmentRootLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
			end
		end

		rotate_str = data.fz_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.faZheng_model then
				self.faZheng_model:SetUSAdjustmentNodeLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
				-- self.faZheng_model:SetRTAdjustmentRootLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
			end
		end

		scale = data.fz_scale
		if scale and scale ~= "" then
			if self.faZheng_model then
				self.faZheng_model:SetUSAdjustmentNodeLocalScale(scale)
				-- self.faZheng_model:SetRTAdjustmentRootLocalScale(scale)
			end
		end
	end

	-- 幻兽
	if self.node_list.beasts_display:GetActive() then
		pos_str = data.beasts_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			if self.beasts_model then
				self.beasts_model:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
			end
		end

		rotate_str = data.beasts_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.beasts_model then
				self.beasts_model:SetUSAdjustmentNodeLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
			end
		end

		scale = data.beasts_scale
		if scale and scale ~= "" then
			if self.beasts_model then
				self.beasts_model:SetUSAdjustmentNodeLocalScale(scale)
			end
		end
	end
end

function XuYuanFreshPoolView:ShowItemTips(item_type)
    local cfg = XuYuanFreshPoolWGData.Instance:GetConsumeCfg()
    local item_id = cfg[item_type] and cfg[item_type].yanhua_item.item_id
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
end

function XuYuanFreshPoolView:OnItemChange(change_item_id)
    local check_list = XuYuanFreshPoolWGData.Instance:GetItemDataChangeList()
    for i, v in pairs(check_list) do
        if v == change_item_id then
            self:FlushBtnShow(true)
            return
        end
    end
end

function XuYuanFreshPoolView:OnClickDraw(draw_type)
	local cfg = XuYuanFreshPoolWGData.Instance:GetConsumeCfg()
	cfg = cfg[draw_type]
	if not cfg then
		return
	end

    local num = ItemWGData.Instance:GetItemNumInBagById(cfg.yanhua_item.item_id)
	if num >= cfg.yanhua_item.num then
		--足够就关闭界面放特效
			--是否跳过动画
		self:ShowDrawAnim(function ()
			--print_error("抽奖")
			XuYuanFreshPoolWGData.Instance:CacheOrGetDrawIndex(draw_type)
			XuYuanFreshPoolWGCtrl.Instance:SendReq(RA_YANHUA_SHENGDIAN3_OP_TYPE.BUY, cfg.onekey_lotto_num, 1)
		end)
	else
		--不足够买
        XuYuanFreshPoolWGCtrl.Instance:ClickUse(draw_type, function()
            self:OnClickBuy(draw_type)
        end)
	end
end

function XuYuanFreshPoolView:OnClickBuy(draw_type)
    local cfg = XuYuanFreshPoolWGData.Instance:GetConsumeCfg()
	local cur_cfg = cfg[draw_type]
	if not cur_cfg then
		return
	end
	
    local num = ItemWGData.Instance:GetItemNumInBagById(cur_cfg.yanhua_item.item_id)
	local price = cur_cfg.consume_count / cur_cfg.yanhua_item.num 	-- 计算单价
    local consume = price * (cur_cfg.yanhua_item.num - num)
	--检查仙玉
	local enough = RoleWGData.Instance:GetIsEnoughUseGold(consume)
	--足够购买，不足弹窗
	if enough then
		self:ShowDrawAnim(function ()
			--print_error("足够购买，不足弹窗 抽奖")
			XuYuanFreshPoolWGData.Instance:CacheOrGetDrawIndex(draw_type)
			XuYuanFreshPoolWGCtrl.Instance:SendReq(RA_YANHUA_SHENGDIAN3_OP_TYPE.BUY, cfg[draw_type].onekey_lotto_num)
		end)
	else
		VipWGCtrl.Instance:OpenTipNoGold()
	end
end

function XuYuanFreshPoolView:OnClickGetBigRewardBtn()
	local item_data = XuYuanFreshPoolWGData.Instance:GetBigRewardData()
	if not item_data then
		return
	end

	local draw_times = XuYuanFreshPoolWGData.Instance:GetCurDrawTimes()
	local has_get = XuYuanFreshPoolWGData.Instance:GetFanliHasGet(item_data.index)
	if draw_times >= item_data.lotto_num and not has_get then
		XuYuanFreshPoolWGCtrl.Instance:SendReq(RA_YANHUA_SHENGDIAN3_OP_TYPE.FETCH, item_data.index)
	end
end

--活动时间倒计时
function XuYuanFreshPoolView:LoginTimeCountDown()
    local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_YANHUA_SHENGDIAN_3)
	if activity_data ~= nil then
		local invalid_time = activity_data.end_time
        if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
            self.node_list.xy_act_time.text.text = TimeUtil.FormatSecondDHM2(invalid_time - TimeWGCtrl.Instance:GetServerTime())
            CountDownManager.Instance:AddCountDown("xuyuan_freshpool_down", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
        end
	end
end

function XuYuanFreshPoolView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.login_act_date = TimeUtil.FormatUnixTime2Date()
        if self.node_list and self.node_list.xy_act_time then
            self.node_list.xy_act_time.text.text = TimeUtil.FormatSecondDHM2(valid_time)
        end
	end
end

function XuYuanFreshPoolView:OnComplete()
    if self.node_list and self.node_list.xy_act_time then
        self.node_list.xy_act_time.text.text = Language.Activity.TianshenRoadLoginTime
    end

    self:Close()
end

function XuYuanFreshPoolView:OnClickRewardPreview()
	local show_list = XuYuanFreshPoolWGData.Instance:GetShowCellList()
	local data_list =
	{
		view_type = RewardShowViewType.Title_Probability_2,
		view_color = RewardShowViewColor.Purple,
		title_probability_reward_item_data = show_list
	}
	RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
end

function XuYuanFreshPoolView:OnClickGaiLv()
	local info = XuYuanFreshPoolWGData.Instance:GetRandomGaiLvinfo()
	TipWGCtrl.Instance:OpenTipsRewardProView(info)
end

-- 打开通用抽奖记录面板
function XuYuanFreshPoolView:OnClickBtnDrawRecord()
	local record_list = XuYuanFreshPoolWGData.Instance:GetDrawRecord()
	TipWGCtrl.Instance:OpenTipsRewardRecordView(record_list)
end

-- 兑换商店
function XuYuanFreshPoolView:GoToShop()
	local goto_panel, need_check_open = XuYuanFreshPoolWGData.Instance:GetShopPanel()
	local is_open = not need_check_open
	if need_check_open then
		local is_act_open = WorldTreasureWGData.Instance:GetActivityIsOpen()
		local is_shop_open = WorldTreasureWGData.Instance:GetGradeIndexIsOpen(TabIndex.tcdb_premium_shop)
		is_open = is_act_open and is_shop_open
	end

    if is_open then
        ViewManager.Instance:OpenByCfg(goto_panel)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.XuYuanFreshPool.UnopenShop)
    end
end

-- 分页展示奖励池
function XuYuanFreshPoolView:InitShowAwardPage()
	self.page_toggle_list = {}
	self.cur_pos = 0
	self.pool_list_view = AsyncFancyAnimView.New(XuYuanFreshPageCell, self.node_list.pool_list_view)
	self.pool_list_view:SetSelectCallBack(BindTool.Bind(self.SelectCallback, self))
	local node_num = self.node_list.toggle_group.transform.childCount
    for i = 1, node_num do
        self.page_toggle_list[i] = self.node_list.toggle_group:FindObj("toggle_" .. i)
	end

end

function XuYuanFreshPoolView:SelectCallback(cell, index)
	if self.page_toggle_list and self.page_toggle_list[index] then
		self.page_toggle_list[index].toggle.isOn = true
	end
end

function XuYuanFreshPoolView:GetNumberOfCells()
	return self.page_list and #self.page_list or 0
end

function XuYuanFreshPoolView:FlushShowAwardPage()
	self.page_list = XuYuanFreshPoolWGData.Instance:GetShowRewardPageList(page_show_num)
    if self.page_list == nil then
        return
    end

    local page_num = self:GetNumberOfCells()

    self:CancelQuest()
	for k, v in ipairs(self.page_toggle_list) do
        v:CustomSetActive(k <= page_num)
    end
	self.pool_list_view:SetDataList(self.page_list)

	self:StartQuest()
end

function XuYuanFreshPoolView:CancelQuest()
    if self.notice_timer_quest then
		GlobalTimerQuest:CancelQuest(self.notice_timer_quest)
		self.notice_timer_quest = nil
	end
end

function XuYuanFreshPoolView:StartQuest()
    if not self.notice_timer_quest then
		self.notice_timer_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.NoticeLoopShow, self), 3)
	end
end

function XuYuanFreshPoolView:NoticeLoopShow()
	local max_index = self:GetNumberOfCells()
	if max_index > 0 and not self.pool_list_view.list_view.fancy_scroller.Dragging then
		local cur_index = self.pool_list_view:GetSelectIndex() or 1
		local jump_index = cur_index < max_index and cur_index + 1 or 1
		self.pool_list_view:SelectCell(jump_index)
	end
end
-- 分页展示奖励池 end

-- 抽奖动画
function XuYuanFreshPoolView:ShowDrawAnim(callback)
    if self.is_play_draw_anim then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.XuYuanFreshPool.PlayAni)
        return
    end

	if not callback then
		return
	end
	local is_skip = XuYuanFreshPoolWGData.Instance:GetSkipComic()
	if is_skip then
		callback()
	else
		self.node_list.anim_mask:CustomSetActive(true)
		self.draw_anim_sp.AnimationState:SetAnimation(0, "open", false)
		local delay_time = 1.5
		self.is_play_draw_anim = true
		ReDelayCall(self, function ()
			self.node_list.anim_mask:CustomSetActive(false)
			self.is_play_draw_anim = false
			callback()
		end, delay_time, "show_draw_anim_delay")
	end
end

function XuYuanFreshPoolView:FlushPassAnim()
	local is_skip = XuYuanFreshPoolWGData.Instance:GetSkipComic()
	self.node_list.pass_anim.toggle.isOn = is_skip
end

-- 跳过动画
function XuYuanFreshPoolView:ClickPassAnim()
	local flag = self.node_list.pass_anim.toggle.isOn and 1 or 0
	XuYuanFreshPoolWGCtrl.Instance:SendReq(RA_YANHUA_SHENGDIAN3_OP_TYPE.ANIM_FLAG, flag)
end

-- 累计抽奖次数跳转, 优先跳到首个未领取
function XuYuanFreshPoolView:AutoJump(list)
	if not self.xy_leiji_list or not list then
		return
	end

	local jump_index = 0
	for index, value in ipairs(list) do
		if value.has_get == 0 then
			jump_index = index - 1
			break
		end
	end

	if self.cur_jump_index == jump_index then
		return
	end
	local max_num = #list - self.ignore_num
	self.cur_jump_index = jump_index
	local process = jump_index / max_num
	self.xy_leiji_list:JumptToPrecent(process)
end

-- 打开item预览
function XuYuanFreshPoolView:OpenItemTips()
	local item_id = XuYuanFreshPoolWGData.Instance:GetBigAward()
	TipWGCtrl.Instance:OpenItem({item_id = item_id})
end

------------------------------------- XuYuanFreshLeijiRender --------------------------------------------
XuYuanFreshLeijiRender = XuYuanFreshLeijiRender or BaseClass(BaseRender)
function XuYuanFreshLeijiRender:LoadCallBack()
    self.node_list["btn_lingqu"].button:AddClickListener(BindTool.Bind1(self.OnClickGet, self))
    self.item_cell = ItemCell.New(self.node_list["item_cell"])
end

function XuYuanFreshLeijiRender:ReleaseCallBack()
    if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
    end
end

function XuYuanFreshLeijiRender:OnFlush()
    if not self.data then
        return
	end
	
    local data = self.data.data
    local draw_times = XuYuanFreshPoolWGData.Instance:GetCurDrawTimes()
    local color = draw_times >= data.lotto_num and COLOR3B.C8 or COLOR3B.WHITE
    local str = data.lotto_num
    local desc = ToColorStr(str, color)
    self.node_list["txt_times"].text.text = string.format(Language.XuYuanFreshPool.TxtTimes, desc)
    local is_show = data.lotto_num <= draw_times and self.data.has_get ~= 1
    self.node_list["red"]:SetActive(is_show)
	
    self.node_list["btn_lingqu"]:SetActive(self.data.has_get ~= 1)
    XUI.SetButtonEnabled(self.node_list["btn_lingqu"], self.data.has_get ~= 1)

	if data.reward_item and data.reward_item[0] then
		self.item_cell:SetData(data.reward_item[0])
		self.item_cell:SetRightBottomColorText("")
	end
	self.item_cell:SetLingQuVisible(self.data.has_get == 1)
	
	local is_not_last = not self.data.is_last
	self.node_list.process:CustomSetActive(is_not_last)
	if is_not_last and self.data.next_target then
		local process = 0
		if draw_times >= self.data.next_target then
			process = 1
		elseif draw_times > data.lotto_num then
			local cur_val = draw_times - data.lotto_num
			local cur_total = self.data.next_target - data.lotto_num
			process = cur_val / cur_total
		end
		self.node_list.process.slider.value = process
	end
end

function XuYuanFreshLeijiRender:OnClickGet()
    if not self.data then
        return
    end

    local draw_times = XuYuanFreshPoolWGData.Instance:GetCurDrawTimes()
    local data = self.data.data

    if draw_times >= data.lotto_num and self.data.has_get == 0 then
        XuYuanFreshPoolWGCtrl.Instance:SendReq(RA_YANHUA_SHENGDIAN3_OP_TYPE.FETCH, data.index)
	else
		TipWGCtrl.Instance:OpenItem(data.reward_item[0])
    end
end

------------------------------------- XuYuanFreshPageCell --------------------------------------------
XuYuanFreshPageCell = XuYuanFreshPageCell or BaseClass(BaseRender)

function XuYuanFreshPageCell:ReleaseCallBack()
	if self.item_obj_list then
		for _, value in pairs(self.item_obj_list) do
			value:DeleteMe()
		end
		self.item_obj_list = nil
	end
end

function XuYuanFreshPageCell:LoadCallBack()
	self.item_obj_list = {}
	for i = 1, page_show_num do
		local node = self.node_list["item_" .. i]
		if node then
			local obj = ItemCell.New(node)
			obj:SetCellBgEnabled(false)
			table.insert(self.item_obj_list, obj)
		end
	end
end

function XuYuanFreshPageCell:OnFlush()
	local data = self:GetData()
	local award_list = data.list
	for index, value in ipairs(self.item_obj_list) do
		value:SetData(award_list and award_list[index])
	end
end