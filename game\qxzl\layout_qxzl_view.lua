QunXiongZhuLuView = QunXiongZhuLuView or BaseClass(SafeBaseView)

QunXiongZhuLuView.ViewType = {
    PingJi = 1,             --仙盟狩猎
    XMFB = 2,               --仙盟封榜
    DayTwoZhengBa = 3,      --仙盟争霸
    ZhuZaiShenDian = 4,     --
}

local act_is_over = false       -- 活动是否结束
local act_is_star = false
function QunXiongZhuLuView:__init()
    self:SetMaskBg()
    local bundle_name = "uis/view/act_qxzl_prefab"
    self.view_style = ViewStyle.Half
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
    self:AddViewResource(0, bundle_name, "layout_qxzl_view")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")

    self.idx = 1
    self.is_ready = false
    self.act2_is_over = false
    self.act2_is_star = false
    self.act1_is_over = false
    self.act3_is_over = false
    self.xmfb_over = false
    self.xmfb_title_id = 0

    self.is_first_open = true
end

function QunXiongZhuLuView:__delete()
   --[[ if self.flush_effect_bind then
        GlobalTimerQuest:CancelQuest(self.flush_effect_bind)
        self.flush_effect_bind = nil
    end--]]
end

function QunXiongZhuLuView:ReleaseCallBack()
    CountDownManager.Instance:RemoveCountDown("boss_hunter")
    CountDownManager.Instance:RemoveCountDown("pj_end_time")
    CountDownManager.Instance:RemoveCountDown("fb_guild_rank_count_down")
    self.idx = 1
    self.is_ready = false
    self.act2_is_over = false
    self.act2_is_star = false
    self.act1_is_over = false
    self.act3_is_over = false
    self.xmfb_over = false
    self.xmfb_title_id = 0

    if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

    if self.pj_reward_item then
        self.pj_reward_item:DeleteMe()
        self.pj_reward_item = nil
    end

    -- if self.pj_item then
    --     self.pj_item:DeleteMe()
    --     self.pj_item = nil
    -- end

    if self.sd_reward_item then
        self.sd_reward_item:DeleteMe()
        self.sd_reward_item = nil
    end

    if self.accordion_list then
        self.accordion_list = nil
    end

    if self.xmfb_rank_list then
        self.xmfb_rank_list:DeleteMe()
        self.xmfb_rank_list = nil
    end

    if self.xmfb_title_list then
		for key, value in pairs(self.xmfb_title_list) do
			value:DeleteMe()
			value = nil
		end
    	self.xmfb_title_list = nil
    end

    self.xmfb_select_index = nil
    --[[if self.flush_effect_bind then
        GlobalTimerQuest:CancelQuest(self.flush_effect_bind)
        self.flush_effect_bind = nil
    end--]]
end

function QunXiongZhuLuView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.OpenServer.QXZLTitle
	local bundle, asset = ResPath.GetRawImagesPNG("a3_zlxm_bg")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

	self:InitMoneyBar()

    self.xmfb_select_index = 1
    if not self.pj_reward_item then
        self.pj_reward_item = AsyncListView.New(PJRewardItemRender, self.node_list["pj_reward_item_list"])
    end

    -- if not self.pj_item then
    --     self.pj_item = AsyncListView.New(PJItemRender, self.node_list["pj_item_list"])
    -- end

    if not self.sd_reward_item then
        self.sd_reward_item = AsyncListView.New(SDRewardRender, self.node_list["sd_reward_item_list"])
    end

    self.xmfb_rank_list = AsyncListView.New(QunXiongZhuLuRankListRender, self.node_list.xmfb_rank_list)

    if not self.xmfb_title_list then
        self.xmfb_title_list = {}
		for i = 1, 3 do
			self.xmfb_title_list[i] = QunXiongZhuLuTitleListRender.New(self.node_list.xmfb_title_list:FindObj("fb_title_cell_" .. i))
			self.xmfb_title_list[i]:SetIndex(i)
		end
	end

	local act_cfg = ServerActivityWGData.Instance:GetKaiZongLiPaiNewCfg()
	for i = 1, 3 do
		if act_cfg and act_cfg[i] then
			self.xmfb_title_list[i]:SetData(act_cfg[i])
		end
	end

    self.node_list.btn_go.button:AddClickListener(BindTool.Bind(self.OnClickToGo, self))
    self.node_list.btn_rank.button:AddClickListener(BindTool.Bind(self.OnClickRand, self))
    self.node_list.btn_chakan.button:AddClickListener(BindTool.Bind(self.OnClickChaKan, self))
    self.node_list.btn_rule.button:AddClickListener(BindTool.Bind(self.OnClickTips, self))
    self.node_list.xmfb_peidai_btn.button:AddClickListener(BindTool.Bind(self.XMFBOnClickPeiDai, self))
    self.node_list.btn_rule_tips.button:AddClickListener(BindTool.Bind(self.OnClickRuleTips, self))

    self.xmfb_guild_name_list = {}
    for i=1, 4 do
        self.xmfb_guild_name_list[i] = self.node_list["guild_name_"..i]
        self.node_list["xmfb_toggle_"..i].toggle:AddValueChangedListener(BindTool.Bind(self.XMFBOnClickToggle, self, i))
    end
    self:ShowDesc()
    self:SetAccordion()
  --[[  self:FlushZBCountDownTime()
    self.flush_effect_bind = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.FlushZBCountDownTime, self), 1) --每1秒--]]
end

function QunXiongZhuLuView:OpenCallBack()
    self.is_first_open = true

    ZhuZaiShenDianWGCtrl.Instance:SendCSGuildBattleRankInfoReq(GUILD_BATTLE_INFO_REQ_TYPE.GUILD_BATTLE_INFO_REQ_TYPE_MATCH)
    if 0 ~= RoleWGData.Instance.role_vo.guild_id then
        GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.ALL_GUILD_BASE_INFO, RoleWGData.Instance.role_vo.guild_id)
    end

    ServerActivityWGCtrl.Instance:SendOpenGameBossHunterReq(OGA_BOSS_HUNTER_REQ.OGA_BOSS_HUNTER_REQ_QUERY_GUILD_RANK)
    QunXiongZhuLuWGCtrl.Instance:SendReq(1)
    QunXiongZhuLuWGCtrl.Instance:SendReq(3)
    GuildWGCtrl.Instance:SendCSGuildMemberCapabilityRank()
end

function QunXiongZhuLuView:ShowIndexCallBack()

end

function QunXiongZhuLuView:InitMoneyBar()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end

function QunXiongZhuLuView:OnFlush(param_t)
    for k,v in pairs(param_t) do
        if k == "select_index" then
            self.idx = v.select_index or 1
        end
    end

    local cfg_data = ServerActivityWGData.Instance:GetActLimitTimeCfg(ACTIVITY_TYPE.GUILD_ZHENBA)
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local round_1, round_2, round_3, round_4 = QunXiongZhuLuWGData.Instance:GetMengZhanTime()
    local act_time_cfg = ServerActivityWGData.Instance:GetActLimitTimeCfg(ACTIVITY_TYPE.OPENSERVER_KAIZONGLIPAI)
    self:ChangeView(self.idx)
    --self.node_list.toggle_3:SetActive(self.is_ready)
    
    if self.accordion_list then
        if open_day <= cfg_data.close_day then
            self.accordion_list[3].time.text.text = string.format(Language.OpenServer.OpenXianMengZhenba5, round_3[1], round_4[2])
            self.accordion_list[3].hight_time.text.text = string.format(Language.OpenServer.OpenXianMengZhenba5, round_3[1], round_4[2])
        end

        if self.xmfb_over then 
            self.accordion_list[2].time.text.text = Language.OpenServer.ActivityEnd
            self.accordion_list[2].hight_time.text.text = Language.OpenServer.ActivityEnd
        else
            self.accordion_list[2].time.text.text = string.format(Language.OpenServer.ActOpenDayTime1, act_time_cfg.open_day, act_time_cfg.close_day)
            self.accordion_list[2].hight_time.text.text = string.format(Language.OpenServer.ActOpenDayTime1, act_time_cfg.open_day, act_time_cfg.close_day)
        end

        local text = string.format(Language.OpenServer.ZhenbaRoundOneTime, round_3[1])
        if act_is_star and not self.act1_is_over then
            text = Language.OpenServer.RoundOneNowTime
        elseif self.act1_is_over and not self.act2_is_star then
            text = string.format(Language.OpenServer.ZhenbaRoundTwoTime, round_4[1])
        elseif self.act2_is_star then
            text = Language.OpenServer.RoundTwoNowTime
        elseif self.act2_is_over then 
            text = Language.OpenServer.ActivityEnd
        end
        self.node_list.zb_time.text.text = text

        if self.act2_is_over then
            self.accordion_list[3].time.text.text = Language.OpenServer.ActivityEnd
            self.accordion_list[3].hight_time.text.text = Language.OpenServer.ActivityEnd
        end

        if act_is_over then
            self.accordion_list[1].time.text.text = Language.OpenServer.ActivityEnd
            self.accordion_list[1].hight_time.text.text = Language.OpenServer.ActivityEnd
        end

        self.accordion_list[1].red_point:SetActive(QunXiongZhuLuWGData.Instance:GetQXZLBQRemind(act_is_over) == 1)
        self.accordion_list[3].red_point:SetActive(ServerActivityWGData.Instance:GetGuildContentRemind1() == 1 or GuildBattleRankedWGData.Instance:GetFenPeiRemind() == 1)
    end
end

function QunXiongZhuLuView:XMFBOnClickToggle(index, is_on)
    if is_on and self.xmfb_select_index ~= index then
        self.xmfb_select_index = index
        self:XMFBRefreshView()
    end
end

function QunXiongZhuLuView:SetAccordion()
    if self.accordion_list then return end
    self.accordion_list = {}
    for i=1, 3 do
        self.accordion_list[i] = {}
        self.accordion_list[i].time = self.node_list["time"..i]
        self.accordion_list[i].hight_time = self.node_list["hight_time"..i]
        self.accordion_list[i].btn = self.node_list["toggle_"..i]
        self.accordion_list[i].red_point = self.node_list["red_point_"..i]
        self.accordion_list[i].btn.toggle:AddValueChangedListener(BindTool.Bind(self.ChangeView,self,i))
    end

    self.accordion_list[1].btn.toggle.isOn = true
end

function QunXiongZhuLuView:ChangeView(index, is_on)
    self:FlushZBCountDownTime()
    if index == QunXiongZhuLuView.ViewType.PingJi then
        self:PingJiViewFlush()
    elseif index == QunXiongZhuLuView.ViewType.XMFB then
        self:XMFBRefreshView()
    elseif index == QunXiongZhuLuView.ViewType.DayTwoZhengBa then
        self:ZhengBaViewFlush()
    end

    if self.is_first_open or self.idx ~= index then
        self.is_first_open = false
        TweenManager.Instance:ExecuteViewTween(self.view_name, index, self.node_list)
    end

    self.idx = index
    self:FlushPJCountDownTime()
    self:BossHunterFlushCountDownTime()
    self:SetViewActive(index)

    local view_rule = ViewRuleWGData.Instance:GetViewRuleCfg(self.view_name, index)
	self.node_list.btn_rule_tips:SetActive(view_rule ~= nil)
end

function QunXiongZhuLuView:OnClickRuleTips()
	local view_rule = ViewRuleWGData.Instance:GetViewRuleCfg(self.view_name, self.idx)

	if view_rule then
		local rule_tip = RuleTip.Instance
		local rule_title = view_rule.rule_title
		local rule_content = view_rule.rule_content

		rule_tip:SetTitle(rule_title)
		rule_tip:SetContent(rule_content, nil, nil, nil, true)
	end
end

--界面显示
function QunXiongZhuLuView:SetViewActive(index)
    self.node_list.layout_xmzb_view:SetActive(index == QunXiongZhuLuView.ViewType.DayOneZhengBa or index == QunXiongZhuLuView.ViewType.DayTwoZhengBa)
    self.node_list.layout_guild_rank:SetActive(index == QunXiongZhuLuView.ViewType.XMFB)
    self.node_list.layout_xmpj_view:SetActive(index == QunXiongZhuLuView.ViewType.PingJi)
end

function QunXiongZhuLuView:ShowDesc()
    for i = 1,3 do
        if self.node_list["title_desc_" .. i] then
            self.node_list["title_desc_" .. i].text.text = Language.OpenServer.OpenXianMengZhenbaDesc[i]
        end
    end
end

function QunXiongZhuLuView:FlushZBCountDownTime()
    local now_time = TimeWGCtrl.Instance:GetServerTime()
    local time, time3, time1, time2 = GuildBattleRankedWGCtrl.Instance:CheckZBActivity()
    --print_error(time, time3, time1, time2)
    --[[local time1 = GuildBattleRankedWGCtrl.Instance:CheckZBRoundTwoOpenActivity()
    local time2 = GuildBattleRankedWGCtrl.Instance:CheckZBOverActivity()
    local time3 = GuildBattleRankedWGCtrl.Instance:CheckZBRoundOneOverActivity()--]]
    --print_error(TimeUtil.FormatSecond2MYHM(time3+now_time))
    act_is_star = time <= 0 -- 争霸第一轮开始
    self.act1_is_over = time3 <= 0  --争霸第一轮结束
    self.act2_is_star = time1 <= 0-- 争霸第二轮开始
    self.act3_is_over = time3 + 300 <= 0  --争霸第一轮结束
    self.is_ready = now_time >= TimeUtil.GetEarlyTime(time + now_time) --当天
    self.act2_is_over = time2 <= 0 -- 争霸第二轮结束
   
    --[[print_error("第一轮开始", act_is_star)
    print_error("第一轮结束", self.act3_is_over)
    print_error("第二轮开始", self.act2_is_star)
    print_error("第二轮结束", self.act2_is_over)--]]
end

--争霸
function QunXiongZhuLuView:ZhengBaViewFlush()
    local str = ""
    local opengame_cfg = ServerActivityWGData.Instance:GetOpenGameActivityConfig()
    local data_list = opengame_cfg.guild_battle
    self:SDRewardRemind()

    if self.sd_reward_item then
        self.sd_reward_item:SetDataList(data_list)
    end
    self:FLushZBMatchInfo()
end

-- 争霸对战信息
function QunXiongZhuLuView:FLushZBMatchInfo()
    local role_name, final_role_name, win_role = QunXiongZhuLuWGData.Instance:GetQXZLGuildBattleMatchInfo()
    if not role_name or not final_role_name or not win_role then
        return
    end
    for i=1, 4 do
        if role_name[i] and role_name[i] ~= "" then
            self.node_list["group_"..i].text.text = role_name[i]
        else
            self.node_list["group_"..i].text.text = Language.OpenServer.XuWeiYiDai
        end
        self.node_list["sheng"..i]:SetActive(false)
    end

    for i,v in ipairs(final_role_name) do
        for i=1, 4 do
            if v == "" then
                break
            elseif v == role_name[i] then
               self.node_list["sheng"..i]:SetActive(true)
               break
            end
        end
    end

    if not win_role or win_role == "" then
        self.node_list.winname.text.text = Language.OpenServer.XuWeiYiDai
    else
        self.node_list.winname.text.text = win_role
    end
end
--评级
function QunXiongZhuLuView:PingJiViewFlush()
    --CountDownManager.Instance:RemoveCountDown("zb_end_time")
    local guild_id = RoleWGData.Instance.role_vo.guild_id
    local show_rank = guild_id > 0

    local cfg = QunXiongZhuLuWGData.Instance:GetPJInfo()
    local data_list = ServerActivityWGData.Instance:GetBossHunterInfo()
    if IsEmptyTable(data_list) or IsEmptyTable(cfg) then
        return
    end

    self.node_list.my_pj.text.text = string.format(Language.OpenServer.JingPoNumText, data_list.role_boss_score)

    local rank = 0
    if show_rank then
        for i, v in ipairs(data_list.rank_list) do
            if v.guild_name == data_list.role_guild_name then
                rank = i
            end
        end
    end

    self.node_list.my_rand.text.text = show_rank and string.format(Language.OpenServer.RandRankText, rank) or Language.OpenServer.NotRankNumText

    local icon_str = (rank > 0 and rank < 4) and "a3_zlxm_hz" .. rank or "a3_zlxm_hz_no"
    local bundle, asset = ResPath.GetNoPackPNG(icon_str)
    self.node_list.rank_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.rank_icon.image:SetNativeSize()
    end)

    if self.pj_reward_item then
        local list = self:GetPJList(cfg)
        self.pj_reward_item:SetDataList(list)
    end

    local act_cfg_list = ServerActivityWGData.Instance:GetOpenGameActivityConfig()
    local boss_hunter_cfg_list = act_cfg_list and act_cfg_list.boss_hunter_reward or {}
    local rank_list = data_list.rank_list or {}
    local temp_list = nil
    if #rank_list < #boss_hunter_cfg_list then
        temp_list = {}
        for i=1,#boss_hunter_cfg_list do
            temp_list[i] = rank_list[i] or {}
        end
    end

   -- self.pj_item:SetDataList(temp_list or rank_list)
end

function QunXiongZhuLuView:GetPJList(cfg)
    local can_click = RoleWGData.Instance.role_vo.guild_id > 0
    local list = {}
    for key, value in pairs(cfg) do
        local sort = 1
        local remind = QunXiongZhuLuWGData.Instance:GetQXZLRemindByID(value.id)
        local kill_boss_times = QunXiongZhuLuWGData.Instance:GetQXZLKillCountByID(value.id)
        local can_get = can_click and kill_boss_times >= value.times
        if not act_is_over and remind == 1 and can_get then
            sort = 0
        elseif can_click and remind == 0 then
            sort = 2
        end
        local data = {
            id = value.id, 
            boss_level_limit = value.boss_level_limit, 
            times = value.times, 
            order = sort
        }
        table.insert(list, data)
    end

    table.sort(list, SortTools.KeyLowerSorters("order", "id"))
    return list
end

function QunXiongZhuLuView:FlushPJCountDownTime()
    CountDownManager.Instance:RemoveCountDown("pj_end_time")
    local opengame_cfg = QunXiongZhuLuWGData.Instance:GetQXZLConfig()
    local close_day = opengame_cfg.close_day_index
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local now_time = TimeWGCtrl.Instance:GetServerTime()
    local format_time = os.date("*t", now_time)
    local end_time = os.time({
        year = format_time.year,
        month = format_time.month,
        day = format_time.day + close_day - open_day + 1,
        hour = 0,
        min = 0,
        sec = 0
    })

    act_is_over = now_time > end_time
    local count_down_time = end_time 
    if now_time < count_down_time then
        self:UpdateCountDown(now_time, count_down_time)
        CountDownManager.Instance:AddCountDown(
            "pj_end_time",
            BindTool.Bind(self.UpdateCountDown, self),
            BindTool.Bind(self.CompleteCountDown, self),
            count_down_time,
            nil,
            1
        )
    end
end

function QunXiongZhuLuView:CompleteCountDown()
    self.accordion_list[1].time.text.text = Language.OpenServer.ActivityEnd
    self.accordion_list[1].hight_time.text.text = Language.OpenServer.ActivityEnd
end

function QunXiongZhuLuView:UpdateCountDown(elapse_time, total_time)
    self.accordion_list[1].time.text.text = TimeUtil.FormatTimeLanguage2(total_time - elapse_time)
    self.accordion_list[1].hight_time.text.text = TimeUtil.FormatTimeLanguage2(total_time - elapse_time)
end

function QunXiongZhuLuView:BossHunterFlushCountDownTime()
    CountDownManager.Instance:RemoveCountDown("boss_hunter")
    local count_down_time = ServerActivityWGData.Instance:GetOpenServerToSevenDayTime(ACTIVITY_TYPE.BOSS_LIEREN)
    if count_down_time > 0 then
        self.node_list.bh_version_act_time.text.text = TimeUtil.FormatSecondDHM8(count_down_time)
        CountDownManager.Instance:AddCountDown(
            "boss_hunter",
            BindTool.Bind(self.BossHunterUpdateCountDown, self),
            BindTool.Bind(self.BossHunterFlushCountDownTime, self),
            nil,
            count_down_time,
            1
        )
    else
        self.node_list.bh_version_act_time.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
        self.node_list.bh_version_act_time.text.color = Str2C3b(COLOR3B.RED)
    end
end

function QunXiongZhuLuView:BossHunterUpdateCountDown(elapse_time, total_time)
    self.node_list.bh_version_act_time.text.text = TimeUtil.FormatSecondDHM8(total_time - elapse_time)
end

--仙盟封榜
function QunXiongZhuLuView:XMFBRefreshView()
    local rank_list = GuildWGData.Instance:GetGuildMemberRankInfo()
    for i=1,4 do
        if rank_list[i] and rank_list[i].guild_id ~= 0 then
            self.node_list["guild_name_h"..i].text.text = rank_list[i].guild_name
            self.xmfb_guild_name_list[i].text.text = rank_list[i].guild_name
        else
            self.node_list["guild_name_h"..i].text.text = Language.KuafuGuildBattle.KfNoOccupy
            self.xmfb_guild_name_list[i].text.text = Language.KuafuGuildBattle.KfNoOccupy
        end
    end
    GuildWGData.Instance:SetGuildMemberRankIndex(self.xmfb_select_index)
    local title_list = TitleWGData.Instance:GetTitleIdList()
    for _,title_id in ipairs(title_list) do
        local is_act_title, actid = ServerActivityWGData.Instance:IsActGetTitle(title_id)
        if is_act_title and actid == ACTIVITY_TYPE.OPENSERVER_KAIZONGLIPAI then
            self.xmfb_title_id = title_id
            break
        end
    end
    
    local is_used_title = false
    if self.xmfb_title_id > 0 then
        is_used_title = TitleWGData.Instance:IsUsedTitle(self.xmfb_title_id)
    end
    self.node_list.xmfb_peidai_btn_label.text.text = is_used_title and Language.OpenServer.YiPeiDaiTitle or Language.OpenServer.QuickPeiDai

    local rank_list_data = GuildWGData.Instance:GetGuildMemberRankInfoByIndex(self.xmfb_select_index)

    if not IsEmptyTable(rank_list_data) then
        self.xmfb_rank_list:SetDataList(rank_list_data)
        self.xmfb_rank_list:JumpToTop()
    end

    self:XMFBFlushCountDownTime()
end

function QunXiongZhuLuView:XMFBFlushCountDownTime()
    CountDownManager.Instance:RemoveCountDown("fb_guild_rank_count_down")
    local count_down_time = ServerActivityWGData.Instance:GetOpenServerToSevenDayTime(ACTIVITY_TYPE.OPENSERVER_KAIZONGLIPAI)
    if count_down_time > 0 then
        self.node_list.xmfb_act_count_down.text.text = TimeUtil.FormatSecondDHM8(count_down_time)
        CountDownManager.Instance:AddCountDown(
            "fb_guild_rank_count_down",
            BindTool.Bind(self.XMFBUpdateCountDown, self),
            BindTool.Bind(self.XMFBFlushCountDownTime, self),
            nil,
            count_down_time,
            1
        )
    else
        self.node_list.xmfb_act_count_down.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
        self.node_list.xmfb_act_count_down.text.color = Str2C3b(COLOR3B.RED)
        self.xmfb_over = true
    end
end

function QunXiongZhuLuView:XMFBUpdateCountDown(elapse_time, total_time)
    self.node_list.xmfb_act_count_down.text.text = TimeUtil.FormatSecondDHM8(total_time - elapse_time)
end

--跳转Boss副本
function QunXiongZhuLuView:OnClickToGo()
    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.WorldBoss or scene_type == SceneType.PERSON_BOSS or scene_type == SceneType.VIP_BOSS or scene_type == SceneType.DABAO_BOSS then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.CurSceneNoTurn)
        ServerActivityWGCtrl.Instance:CloseOpenServer2View()
        return
    end
    FunOpen.Instance:OpenViewByName(GuideModuleName.Boss, TabIndex.boss_world)

   --[[ local count_down_time = ServerActivityWGData.Instance:GetOpenServerToSevenDayTime(ACTIVITY_TYPE.BOSS_LIEREN)
    if count_down_time <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.LieRenEndTimeTex)
        return
    end
    ServerActivityWGCtrl.Instance:OpenOpenserverCompetitionByUniqueKey("boss_lieren")--]]
end

--佩戴
function QunXiongZhuLuView:XMFBOnClickPeiDai()
    local title_id = self.xmfb_title_id
    if title_id > 0 then
        if TitleWGData.Instance:IsUsedTitle(title_id) then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.YiPeiDaiTitle)
            return
        end
        TitleWGCtrl.Instance:SendUseTitleReq({title_id})
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.WeiPeiDaiTips)
    end
end

--跳转杀积分榜
function QunXiongZhuLuView:OnClickRand()
    ServerActivityWGCtrl.Instance:OpenOpenBossHunterRankView()
   --[[ local count_down_time = ServerActivityWGData.Instance:GetOpenServerToSevenDayTime(ACTIVITY_TYPE.OPENSERVER_KAIZONGLIPAI)
    if count_down_time <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.XianMengEndTimeTex)
        return
    end
    ServerActivityWGCtrl.Instance:OpenOpenserverCompetitionByUniqueKey("xian_meng_feng_bang")--]]
end

--跳转神殿奖励
function QunXiongZhuLuView:OnClickChaKan()
    ZhuZaiShenDianWGCtrl.Instance:OpenZhuZaiRewardView(1)
end

--争霸规则
function QunXiongZhuLuView:OnClickTips()
    local role_tip = RuleTip.Instance
    if role_tip then
        role_tip:SetTitle(Language.Guild.BattleTips)
        role_tip:SetContent(Language.Guild.BattleTipContent, nil, nil, nil, true)
    else
        print_error("AppearanceView","OnClickBtnMLImageChongTip() can not find the get way!")
    end
end

 --神殿奖励红点
function QunXiongZhuLuView:SDRewardRemind()
    local fenpei_remind = GuildBattleRankedWGData.Instance:GetFenPeiRemind()
    self.node_list["img_ls_remind"]:SetActive(fenpei_remind == 1)
end

----------------------------------------------------------------------------------------
PJRewardItemRender = PJRewardItemRender or BaseClass(BaseRender)

function PJRewardItemRender:__init()
    self.join_item_cell = {}
    self.item_cell = {}
    XUI.AddClickEventListener(self.node_list.get_reward_btn, BindTool.Bind1(self.OnClickLingQu, self))
    XUI.AddClickEventListener(self.node_list.go_btn, BindTool.Bind1(self.OnClickToGoBoss, self))
end

function PJRewardItemRender:__delete()
    if self.item_cell then
        for i,v in ipairs(self.item_cell) do
            v:DeleteMe()
        end
        self.item_cell = nil
    end

    if self.join_item_cell then
        for i,v in ipairs(self.join_item_cell) do
            v:DeleteMe()
        end
        self.join_item_cell = nil
    end
end

function PJRewardItemRender:OnFlush()
    local kill_boss_times = QunXiongZhuLuWGData.Instance:GetQXZLKillCountByID(self.data.id)
    local reward, join_reward = QunXiongZhuLuWGData.Instance:GetPJReward(self.data.id)
    local remind = QunXiongZhuLuWGData.Instance:GetQXZLRemindByID(self.data.id)
    for i = 0, #join_reward do
        if not self.join_item_cell[i] then
            self.join_item_cell[i] = ItemCell.New(self.node_list["joinitemcell"])
        end
         self.join_item_cell[i]:SetData(join_reward[i])
    end

    for i = 0, #reward do
        if not self.item_cell[i] then
            self.item_cell[i] = ItemCell.New(self.node_list["reward_content"])
        end
         self.item_cell[i]:SetData(reward[i])
    end

    local guild_id = RoleWGData.Instance.role_vo.guild_id
    local can_click = guild_id > 0

    self.node_list.get_reward_img:SetActive(can_click and remind == 0)
    self.node_list.pass_day_img:SetActive(can_click and act_is_over and remind == 1)

    local can_get = can_click and kill_boss_times >= self.data.times
    self.node_list.get_reward_btn:SetActive(not act_is_over and remind == 1 and can_get)

    local guild_id = RoleWGData.Instance.role_vo.guild_id
    local show_rank = guild_id > 0
    self.node_list.go_btn:SetActive(show_rank and not act_is_over and remind == 1 and not can_get)
    self.node_list.not_guild_text:SetActive(not show_rank and not act_is_over and not can_get)

    self.node_list.red_point:SetActive(remind == 1 and not act_is_over and kill_boss_times >= self.data.times)

    local str = string.format(Language.OpenServer.MengJiShaTitle, self.data.times, self.data.boss_level_limit)
    local color = kill_boss_times >= self.data.times and COLOR3B.GREEN or COLOR3B.RED
    str = str .. string.format(Language.OpenServer.MengJiShaNumTitle, ToColorStr(kill_boss_times, color), self.data.times)
    self.node_list.title_label.text.text = str

    self.node_list.common_progress.slider.value = kill_boss_times / self.data.times
end

function PJRewardItemRender:OnClickLingQu()
    QunXiongZhuLuWGCtrl.Instance:SendReq(2, self.data.id)
end

--跳转Boss副本
function PJRewardItemRender:OnClickToGoBoss()
    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.WorldBoss or scene_type == SceneType.PERSON_BOSS or scene_type == SceneType.VIP_BOSS or scene_type == SceneType.DABAO_BOSS then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.CurSceneNoTurn)
        ServerActivityWGCtrl.Instance:CloseOpenServer2View()
        return
    end
    FunOpen.Instance:OpenViewByName(GuideModuleName.Boss, TabIndex.boss_vip)
end

----------------------------------------------------------------------------------------
ZBRandRender = ZBRandRender or BaseClass(BaseRender)

function ZBRandRender:OnFlush()
    local data = self:GetData()
    if data == nil then return end
    local one_group = data[1]
    local two_group = data[2]
    local name = Language.OpenServer.XuWeiYiDai
    --print_error("1", one_group)
    --print_error("2", two_group)
    --print_error(act_is_star)
    --print_error(one_group.match_info[1], two_group.match_info[1])
    local is_final = one_group.match_info[1].is_second_win == 1 or one_group.match_info[2].is_second_win == 1 or
                     two_group.match_info[1].is_second_win == 1 or two_group.match_info[2].is_second_win == 1
    if act_is_star then           
        self.node_list.one_group_1.text.text = one_group.match_info[1].guild_name ~= "" and one_group.match_info[1].guild_name or Language.Guild.GuideWarProgressWuDuiShou
        self.node_list.one_group_2.text.text = one_group.match_info[1].guild_name ~= "" and one_group.match_info[1].guild_name or Language.Guild.GuideWarProgressWuDuiShou
        self.node_list.two_group_1.text.text = two_group.match_info[1].guild_name ~= "" and two_group.match_info[1].guild_name or Language.Guild.GuideWarProgressWuDuiShou
        self.node_list.two_group_2.text.text = two_group.match_info[2].guild_name ~= "" and two_group.match_info[2].guild_name or Language.Guild.GuideWarProgressWuDuiShou
        if one_group.match_info[1].is_first_win == 1 and one_group.match_info[1].is_second_win == 1 and one_group.match_info[1].guild_name ~= "" then
            name = one_group.match_info[1].guild_name
        elseif one_group.match_info[2].is_first_win == 1 and one_group.match_info[2].is_second_win == 1 and one_group.match_info[2].guild_name ~= "" then
            name = one_group.match_info[2].guild_name
        elseif two_group.match_info[1].guild_name ~= "" and two_group.match_info[1].is_first_win == 1 and two_group.match_info[1].is_second_win == 1 then
            name = two_group.match_info[1].guild_name
        elseif two_group.match_info[2].guild_name ~= "" and two_group.match_info[2].is_first_win == 1 and two_group.match_info[2].is_second_win == 1 then
            name = two_group.match_info[2].guild_name 
        end
    else
        self.node_list.one_group_1.text.text = Language.OpenServer.XuWeiYiDai
        self.node_list.one_group_2.text.text = Language.OpenServer.XuWeiYiDai
        self.node_list.two_group_1.text.text = Language.OpenServer.XuWeiYiDai
        self.node_list.two_group_2.text.text = Language.OpenServer.XuWeiYiDai
    end
    self.node_list.sheng1:SetActive(one_group.match_info[1].is_first_win == 1 and one_group.match_info[1].guild_id > 0 and act_is_star)
    self.node_list.sheng2:SetActive(one_group.match_info[2].is_first_win == 1 and one_group.match_info[2].guild_id > 0 and act_is_star)
    self.node_list.sheng3:SetActive(two_group.match_info[1].is_first_win == 1 and two_group.match_info[1].guild_id > 0 and act_is_star)
    self.node_list.sheng4:SetActive(two_group.match_info[2].is_first_win == 1 and two_group.match_info[2].guild_id > 0 and act_is_star) 
    self.node_list.winname.text.text = name
end


----------------------------------------------------------------------------------------
SDRewardRender = SDRewardRender or BaseClass(BaseRender)

function SDRewardRender:__init()
    local parent = self.node_list.reward_content
    local item_list = {}
    for i=1,4 do
        item_list[i] = ItemCell.New(parent)
    end
    self.item_list = item_list
    XUI.AddClickEventListener(self.node_list.get_reward_btn, BindTool.Bind1(self.OnClickLingQu, self))
end

function SDRewardRender:__delete()
    for k,v in pairs(self.item_list) do
        v:DeleteMe()
    end
    self.item_list = {}
end

function SDRewardRender:OnFlush()
    local data = self:GetData()
    if IsEmptyTable(data) then
        return
    end

    local opengame_info = ServerActivityWGData.Instance:GetOpenServerData()
    local reward_type = opengame_info.oga_guild_battle_reward_type
    local reward_item = data.reward_item
    local can_get_reward = false
    local is_get_reward = false
    if data.type == reward_type then
        can_get_reward = opengame_info.oga_guild_battle_reward_flag == 0
        is_get_reward = opengame_info.oga_guild_battle_reward_flag == 1
    end

    self.node_list.get_reward_btn:SetActive(not is_get_reward)
    self.node_list.get_reward_img:SetActive(is_get_reward)
    XUI.SetButtonEnabled(self.node_list.get_reward_btn, can_get_reward)
    self.node_list["red_point"]:SetActive(can_get_reward)

    for i, cell in ipairs(self.item_list) do
        if reward_item[i - 1] then
            cell:SetData(reward_item[i - 1])
            cell:SetActive(true)
        else
            cell:SetActive(false)
        end
    end

    local str = Language.OpenServer.GuildContendLingquDesc1[data.type]
    self.node_list.title_label.text.text = str
end

function SDRewardRender:OnClickLingQu()
    ServerActivityWGCtrl.Instance:SendOpenGameActivityFetchReward(OPEN_SERVER_REWARD_TYPE.REWARD_TYPE_GUILD_BATTLE_REWARD, self.data.type)
end

function SDRewardRender:PlaySDRewardRenderAnim(item_index)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.QunXiongZhuLuView.SDRewardRender

    UITween.FakeHideShow(self.node_list["tween_root"])
    ReDelayCall(self, function()
        if self.node_list and self.node_list["tween_root"] then
            UITween.MoveAlphaShow(GuideModuleName.TianShenRoadPanel, self.node_list["tween_root"], tween_info)
        end
    end, tween_info.NextDoDelay * wait_index, "SDRewardRender" .. wait_index)
end

----------------------------------------------------------------------------------------
PJItemRender = PJItemRender or BaseClass(BaseRender)

function PJItemRender:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function PJItemRender:LoadCallBack()
    self.item_cell = ItemCell.New(self.node_list.ph_cell)
end

function PJItemRender:OnFlush()
    local data = self:GetData()
    local index = self:GetIndex()

    local shadow_show = index <= 3
    local color = shadow_show and "#FFFFFF" or "#545252"
    self.node_list.lbl_rank.text.text = index
    if shadow_show then
        self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. index))
        self.node_list.bg_img.image:LoadSprite(ResPath.GetCommonIcon("a3_ty_list_" .. index))
    end
    self.node_list.img_rank:SetActive(shadow_show)
    self.node_list.bg_img:SetActive(shadow_show)
    self.node_list.lbl_rank:SetActive(not shadow_show)

    local rank_bind_gold = ServerActivityWGData.Instance:GetBossHunterJiangLi(index)
    if rank_bind_gold > 0 then
        self.item_cell:SetData({item_id = 65533, num = rank_bind_gold, is_bind = 1})
    end
    self.item_cell:SetActive(rank_bind_gold > 0)
    -- TODO 需要改为新版处理方式
    -- self.node_list.lbl_guild_name.shadow.enabled = shadow_show
    -- self.node_list.lbl_score.shadow.enabled = shadow_show
    if IsEmptyTable(data) then
        self.node_list.lbl_guild_name.text.text = ToColorStr(Language.OpenServer.XuWeiYiDai, color)
        self.node_list.lbl_score.text.text = ToColorStr("0" ,color)
        return
    end

    self.node_list.lbl_guild_name.text.text = ToColorStr(data.guild_name or "", color)
    self.node_list.lbl_score.text.text = ToColorStr(data.boss_score or "", color)
end

------------------------------------------------------------------------------
QunXiongZhuLuRankListRender = QunXiongZhuLuRankListRender or BaseClass(BaseRender)

function QunXiongZhuLuRankListRender:LoadCallBack()
    self.node_list.click_btn.button:AddClickListener(BindTool.Bind(self.OnClickItem, self))
end

function QunXiongZhuLuRankListRender:OnFlush()
    local data = self:GetData()
    local index = self.index

    local is_three = index <= 3

    if is_three then
        self.node_list.rank_num_img.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. index))
        self.node_list.bg_img.image:LoadSprite(ResPath.GetNoPackPNG("a3_zqxz_di_" .. index))
    end

    self.node_list.bg_img:SetActive(is_three)

    local rank_list = GuildWGData.Instance:GetGuildMemberRankInfo()
    local select_index = GuildWGData.Instance:GetGuildMemberRankIndex()
    self.node_list.rank_num_img:SetActive(is_three)
    self.node_list.rank_num:SetActive(index > 3)

    self.node_list.rank_num.text.text = index
    if IsEmptyTable(data) or data.user_id == 0 then
        self.node_list.prower.text.text = Language.Guild.ZanWuCount
        self.node_list.name.text.text = Language.OpenServer.XuWeiYiDai
        self.node_list.post_label.text.text = ""
        return
    end

    if data.user_id == COMMON_CONSTS.GUILD_MENGZHU_ROBOT_ID then--机器人盟主模型
        local data_info = self:OperaRobotGuildLeader(rank_list[select_index].guild_name)
        self.node_list.prower.text.text =  data_info.capability
        self.node_list.name.text.text = data_info.user_name
        self.node_list.post_label.text.text = data_info.guild_post
        return
    end
    
    self.node_list.prower.text.text =  data.capability
    self.node_list.name.text.text = data.user_name
    self.node_list.post_label.text.text = Language.Guild.GuildPost[data.guild_post]
end

function QunXiongZhuLuRankListRender:OnClickItem()
    BrowseWGCtrl.Instance:ShowOtherRoleInfo(self.data.user_id)
end

-- 机器人盟主处理
function QunXiongZhuLuRankListRender:OperaRobotGuildLeader(guild_name)
    local data_info = {}
    local cfg = GuildWGData.Instance:GetGuildRobotCfgByGuildName(guild_name)
    data_info.user_name = cfg.role_name
    data_info.capability = cfg.mengzhu_cap
    data_info.guild_post =  Language.Guild.GuildPost[4]
    return data_info
end

-------------------------------------------QunXiongZhuLuTitleListRender---------------------------------------------------------------

QunXiongZhuLuTitleListRender = QunXiongZhuLuTitleListRender or BaseClass(BaseRender)

function QunXiongZhuLuTitleListRender:OnFlush()
	local data = self:GetData()
	local b,a = ResPath.GetTitleModel(data.title_id)
	self.node_list.title_img:ChangeAsset(b, a, false)

    local pos = Split(data.pos, "|")
    RectTransform.SetAnchoredPositionXY(self.node_list.title_img.rect, pos[1], pos[2])
    self.node_list.title_img.transform:SetLocalScale(data.scale, data.scale, data.scale)

	self.node_list.desc_label_1.text.text = string.format(Language.Common.BestDrops, data.drop_rate / 100000)

	self.node_list.desc_label_3:SetActive(data.max_zhanli ~= data.min_zhanli)
	self.node_list.rank_1:SetActive(data.max_zhanli == data.min_zhanli)

	if data.min_zhanli < 9999 then
		self.node_list.desc_label_3.text.text = string.format(Language.OpenServer.MengNeiZhanLi2, data.max_zhanli, data.min_zhanli)
	else
		self.node_list.desc_label_3.text.text = Language.Guide.RemainMenber
	end
end