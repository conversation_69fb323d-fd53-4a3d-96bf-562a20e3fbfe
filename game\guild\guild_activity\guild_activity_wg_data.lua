---仙盟活动预览------
GuildActivityWGData = GuildActivityWGData or BaseClass()

GuildActivityWGData.Act_Type = {
	Boss = 1,
	DaTi = 2,
	ShouHu = 3,
	NotOpen = 99,
}

function GuildActivityWGData:__init()
	if GuildActivityWGData.Instance then
		ErrorLog("[GuildActivityWGData]:Attempt to create singleton twice!")
	end
	GuildActivityWGData.Instance = self
	local guild_cfg = ConfigManager.Instance:GetAutoConfig("guildconfig_auto")
	self.guild_boss = ConfigManager.Instance:GetAutoConfig("guild_boss_auto").monster_cfg
	self.guild_act_cfg = guild_cfg.activity
end

function GuildActivityWGData:__delete()
	GuildActivityWGData.Instance = nil
end

function GuildActivityWGData:InitActData()
	self.act_datalist = {}
	for k,v in ipairs(self.guild_act_cfg) do
		local data = {}
		data.cfg = v
		data.reward_list = nil
		data.act_hall_cfg = nil
		data.can_open = false
		data.level_limit = ""
		if v.type == GuildActivityWGData.Act_Type.Boss then
			data.reward_list = self:GetGuildBossReward()
		elseif v.type == GuildActivityWGData.Act_Type.DaTi then
			data.reward_list = self:GetGuildDaTiReward()
		elseif v.type == GuildActivityWGData.Act_Type.ShouHu then
			data.reward_list = self:GetGuildShouHuReward()
		end

		if v.type ~= GuildActivityWGData.Act_Type.NotOpen then
			data.act_hall_cfg = BiZuoWGData.Instance:GetAllActivityHallCfgByActType(v.act_id)
		end
		table.insert(self.act_datalist,data)
	end
end

function GuildActivityWGData:GetActData()
	self:InitActData()
	return self.act_datalist
end

function GuildActivityWGData:GetGuildBossReward()
	local reward_cfg = GuildBossWGData.Instance:GetGuildBossRewards()
	local reward_list = {}
	if reward_cfg and reward_cfg.reward_item then
		for i=0,#reward_cfg.reward_item do
			table.insert(reward_list,reward_cfg.reward_item[i])
		end
	end
	return reward_list
end

function GuildActivityWGData:GetGuildShouHuReward()
	local reward_item = GuildWGData.Instance:GetActRewardPreviewItemList()
	local reward_list = {}
	if reward_item then
		for i=0,#reward_item do
			table.insert(reward_list,reward_item[i])
		end
	end
	return reward_list
end

function GuildActivityWGData:GetGuildDaTiReward()
	local cfg = BiZuoWGData.Instance:GetAllActivityHallCfgByActType(ACTIVITY_TYPE.GUILD_ANSWER)
	local reward_list = {}
	if cfg and cfg.reward_item then
		for i=0,#cfg.reward_item do
			table.insert(reward_list,cfg.reward_item[i])
		end
	end
	return reward_list
end

function GuildActivityWGData:GetActDataByType(type)
	local list_data = self:GetActData()
	local data = {}
	for i,v in ipairs(list_data) do
		if v.cfg.type == type then
			data = v
		end
	end
	return data
end

function GuildActivityWGData:GetGuildBossCfg()
	return self.guild_boss[1]
end