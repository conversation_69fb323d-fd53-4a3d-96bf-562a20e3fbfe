-- 批量购买界面
----------------------------------
MiaoshaBatchView = MiaoshaBatchView or BaseClass(SafeBaseView)

function MiaoshaBatchView:__init()
	self:LoadConfig()
	self.default_num = nil
	self.cur_num = 0
end

function MiaoshaBatchView:LoadConfig()
	self:SetMaskBg(true,true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/merge_activity_ui/hefu_miaosha_ui_prefab", "layout_batch_buy")
end

function MiaoshaBatchView:ReleaseCallBack()
	if self.cur_item then
		self.cur_item:DeleteMe()
		self.cur_item = nil
	end
	self.default_num = nil
	self.cur_num = 0
	self.default_show_num = 0
	self.str = nil
end

function MiaoshaBatchView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.HeFuMiaoShaDesc.BatchTitle
	self.node_list.slider.slider.onValueChanged:AddListener(BindTool.Bind(self.OnSoundValueChange, self))
	XUI.AddClickEventListener(self.node_list.btn_sub, BindTool.Bind1(self.OnClickSub, self))
	XUI.AddClickEventListener(self.node_list.btn_add, BindTool.Bind1(self.OnClickAdd, self))
	XUI.AddClickEventListener(self.node_list.btn_confirm, BindTool.Bind1(self.OnClickConfirm, self))
	XUI.AddClickEventListener(self.node_list.btn_cancel, BindTool.Bind1(self.OnClickCancel, self))
	self.cur_item = ItemCell.New(self.node_list.ph_offline_cell)
end

function MiaoshaBatchView:SetData(item_id, num, show_num, max, buy_func)
	self.item_id = item_id
	self.default_num = num
	self.default_show_num = show_num
    self.max = max
    self.buy_func = buy_func
	self:Open()
end

function MiaoshaBatchView:ShowIndexCallBack()
	self:Flush()
end

function MiaoshaBatchView:OnFlush()
	self.cur_item:SetData({item_id = self.item_id, num = 1, is_bind = 1})
    local item_cfg, _ = ItemWGData.Instance:GetItemConfig(self.item_id)
    
    self.node_list.slider.slider.maxValue = self.max
	self.node_list.slider.slider.minValue = 1
	self.node_list.slider.slider.value = 1
    self.node_list.lbl_num.text.text =1
    self.cur_num = 1
    if item_cfg then
        local name = item_cfg.name
        local color = item_cfg.color
        local color_str = ITEM_COLOR[color] or ""
        self.node_list.lbl_item_name.text.text = ToColorStr(name, color_str)
    end
end

function MiaoshaBatchView:OnClickConfirm()
	local num = tonumber(self.node_list.lbl_num.text.text)
    if self.buy_func then
        self.buy_func(num)
    end
    self:Close()
end

function MiaoshaBatchView:OnClickCancel()
	self:Close()
end

function MiaoshaBatchView:OnClickSub()
	if self.max == 0 then return end
	self.cur_num = self.node_list.lbl_num.text.text - 1
	if self.cur_num <= 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.MinValue1)
	end
	self.cur_num = self.cur_num <= 1 and 1 or self.cur_num
	self.node_list.lbl_num.text.text = GameMath.Round(self.cur_num)
	self.node_list.slider.slider.value = self.cur_num
end

function MiaoshaBatchView:OnClickAdd()
	if self.max == 0 then return end
	self.cur_num = self.node_list.lbl_num.text.text + 1
	if self.cur_num >= self.max then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.MaxValue)
	end
	self.cur_num = self.cur_num >= self.max and self.max or self.cur_num
	self.node_list.lbl_num.text.text = GameMath.Round(self.cur_num)
	self.node_list.slider.slider.value = self.cur_num
end

function MiaoshaBatchView:OnSoundValueChange(float_param)
	self.node_list.lbl_num.text.text = float_param
end

