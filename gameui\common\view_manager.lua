ViewManager = ViewManager or BaseClass()

function ViewManager:__init()
	if nil ~= ViewManager.Instance then
		print_error("[ViewManager]:Attempt to create singleton twice!")
	end
	ViewManager.Instance = self

	self.view_list = {}

	self.open_view_list = {}

	self.wait_load_chat_list = {}

	self.can_inactive_view_list = {}

	self.open_queue_list = {}

	self.mainui_right_top_change_list = {}
	self.mainui_fu_ping_change_list = {}
	self.view_node_tween = {}
	self.show_ui_scene_view_names = {}
	self.show_snap_shot_bgs = {}
	self.show_ui_scene_data = {}
	self.view_wait_screen_shot_open_list = {}
	self.view_wait_scene_loaded_list = {}
	self.view_wait_only_view_open_list = {}

	self.scene_all_load_complete_event = GlobalEventSystem:Bind(SceneEventType.SCENE_ALL_LOAD_COMPLETE,
										BindTool.Bind1(self.OnSceneLoadComplete, self))

	-- 主界面右上角列表切换
	self.mainui_right_top_change_event = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK,
										BindTool.Bind(self.MainUIRightTopChangeEvent, self))
	-- 主界面副屏切换
	self.mainui_fu_ping_change_event = GlobalEventSystem:Bind(MainUIEventType.MAIN_MENU_ICON_CHANGE,
										BindTool.Bind(self.MainUIFuPingChangeEvent, self))
end

function ViewManager:__delete()
	self.view_wait_screen_shot_open_list = {}
	self.view_wait_scene_loaded_list = {}
	if self.scene_all_load_complete_event then
		GlobalEventSystem:UnBind(self.scene_all_load_complete_event)
		self.scene_all_load_complete_event = nil
	end

	if self.mainui_right_top_change_event then
		GlobalEventSystem:UnBind(self.mainui_right_top_change_event)
		self.mainui_right_top_change_event = nil
	end

	if self.mainui_fu_ping_change_event then
		GlobalEventSystem:UnBind(self.mainui_fu_ping_change_event)
		self.mainui_fu_ping_change_event = nil
	end

	ViewManager.Instance = nil
end

function ViewManager:OnSceneLoadComplete()
	SafeBaseView.UpdateScreenShot()
end

function ViewManager:DestoryAllAndClear()
	for k,v in pairs(SafeBaseView.open_view_list or {}) do
		v:Close()
		v:Release()
	end

	SafeBaseView.open_view_list = {}

	for k,v in pairs(self.view_list) do
		if v:IsOpen() then
			v:Close()
			v:Release()
		end
	end

	self.view_list = {}
	self.open_view_list = {}
	self.wait_load_chat_list = {}
	self.can_inactive_view_list = {}
	self.open_queue_list = {}
	self.mainui_right_top_change_list = {}
	self.mainui_fu_ping_change_list = {}
	self.view_node_tween = {}
end

-- 注册一个界面
function ViewManager:RegisterView(view, view_name)
	if nil == view_name or "" == view_name then
		print_error("[ViewManager] 请指定view_name!")
	end
	self.view_list[view_name] = view
end

-- 反注册一个界面
function ViewManager:UnRegisterView(view_name)
	self.view_list[view_name] = nil
end

-- 获取一个界面
function ViewManager:GetView(view_name)
	return self.view_list[view_name]
end

-- 界面是否打开
function ViewManager:IsOpen(view_name)
	if nil == self.view_list[view_name] then
		return false
	end

	return self.view_list[view_name]:IsOpen()
end

function ViewManager:IsOpenByIndex(view_name, index)
	if not self:IsOpen(view_name) then
		return false
	end
	local view = self:GetView(view_name)
	return view:GetShowIndex() == index
end

-- 界面是否打开
function ViewManager:HasOpenView(ignore_view_list)
	ignore_view_list = ignore_view_list or {}
	local list = self.open_view_list[UiLayer.Normal]
	if nil == list then
		return false
	end

	for k,v in pairs(list) do
		if v:CanActiveClose() and v:IsOpen() and not ignore_view_list[v.view_name] then
			return true
		end
	end

	return false
end

-- 打开界面
function ViewManager:Open(view_name, tab_index, key, values)
	-- 用于给投放录视频用的，拦截所有界面打开
	if MIANUI_VIEW_EDITOR_FLAG then
		return
	end

	local now_view = self.view_list[view_name]
	if nil ~= now_view then
		local index = 0
		if tonumber(tab_index) == nil and type(tab_index) == 'string' then
			index = TabIndex[tab_index]
		else
			index = tonumber(tab_index)
		end

		local now_view_layer = now_view:GetLayer()
		local guide_view = self.view_list[GuideModuleName.NormalGuideView]
		if guide_view ~= nil and guide_view:IsOpen() and view_name ~= GuideModuleName.ZhuanSheng and now_view_layer ~= nil and now_view_layer == UiLayer.Normal then
			if not guide_view:CheckIsCanOpenView(view_name) then
				return
			end
		end

		--活动界面特殊处理
		if view_name == GuideModuleName.ActivityDetail then
			ActivityWGCtrl.Instance:ShowDetailView(index)
			return
		end
		local fun_name = FunOpen.Instance:GetFunNameByViewName(view_name) or view_name
		local is_open, tips = FunOpen.Instance:GetFunIsOpened(fun_name, true)

		if is_open ~= true then --模块功能未开启
			SysMsgWGCtrl.Instance:ErrorRemind(tips)
			return false
		end

        if view_name == GuideModuleName.Shop then --商城特殊处理
            ShopWGCtrl.Instance:OpenShopJumpToTabIndex(tab_index, key, values)
            return
        end

		if view_name == GuideModuleName.ZhuanSheng then --转生特殊处理
			if TransFerWGData.Instance:GetGodAndDemonsType() == -1 then
				TransFerWGCtrl.Instance:OpenGodAndDemonsView()
				return
			end
        end

        -- if view_name == GuideModuleName.EveryDayRechargeView then --累充特殊处理
        -- 	--是否已完成首充,未首充则跳转首充界面
        -- 	if not RechargeWGData.Instance:GetIsFirstRecharge() then
        -- 		ViewManager.Instance:Open(GuideModuleName.FirstRechargeView)
        -- 		TipWGCtrl.Instance:ShowSystemMsg(Language.Recharge.NeedCompleteShouChong)
        -- 		return
        -- 	end
        -- end

        --灵宠 坐骑 化鲲装备界面判断
        if view_name == GuideModuleName.LingChongEquipView or view_name == GuideModuleName.MountEquipView or view_name == GuideModuleName.HuaKunEquipView then
    		local cur_show_type = MOUNT_PET_EQUIP_TYPE.PET
    		if view_name == GuideModuleName.MountEquipView then
    			cur_show_type = MOUNT_PET_EQUIP_TYPE.MOUNT
    		elseif view_name == GuideModuleName.HuaKunEquipView then
    			cur_show_type = MOUNT_PET_EQUIP_TYPE.HUAKUN
    		end
    		--若跳转非背包页签,检测有没有穿戴装备
        	if (index and index ~= MountLingChongEquipViewIndex[cur_show_type].Bag) and not MountLingChongEquipWGData.Instance:GetEquipInfoFirstPart(cur_show_type) then
        		SysMsgWGCtrl.Instance:ErrorRemind(Language.MountPetEquip.EquipLimit)
        		return
        	end
        end

		if view_name == GuideModuleName.YinianMagicView then
			local select_type = YinianMagicWGData.Instance:GetCurSelcetType()
			if select_type <= 0 then
				YinianMagicWGCtrl.Instance:OpenSelectView()
				return
			end
		end

		if view_name == GuideModuleName.Equipment then
			if tab_index ~= nil then
				if tab_index == TabIndex.equipment_baoshi_jl then
					local has_ypxq_equip_body = EquipBodyWGData.Instance:IsHasCanYPJLEquipBody()
					
					if not has_ypxq_equip_body then
						SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleEquipBody.NoEquipBodyCanYPJL)
						return
					end
				elseif tab_index == TabIndex.equipment_lingyu then
					local has_xjxq_equip_body = EquipBodyWGData.Instance:IsHasCanXJXQEquipBody()
					
					if not has_xjxq_equip_body then
						SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleEquipBody.NoEquipBodyCanXJXQ)
						return
					end
				end
			end
		end
		
		if TabIndex[tab_index] ~= nil and TabIndex[tab_index] ~= "" then
			local is_tab_fun_open, tip = FunOpen.Instance:GetFunIsOpenedByTabName(tab_index, true)
			if is_tab_fun_open ~= true then --标签功能未开启
				SysMsgWGCtrl.Instance:ErrorRemind(tip)
				return false
			end
		end

		if is_open then
			if key ~= nil or values ~= nil then
				now_view:Flush(index, key, values)
			end
			now_view:Open(index, values ~= nil)
		else
			tips = (tips and tips ~= "" and tips) or Language.Common.FunOpenTip
			SysMsgWGCtrl.Instance:ErrorRemind(tips)
		end

		-- 拍照关闭
		if self:HasOpenView() and self:IsOpen(GuideModuleName.ScreenShotView) then
			ScreenShotWGCtrl.Instance:Resume()
		end
	end
end

-- 配表打开界面
function ViewManager:OpenByCfg(cfg, data, flush_key)
	if cfg == nil then
		return
	end

	local t = Split(cfg, "#")
	local view_name = t[1]
	local tab_index = t[2]

	-- 判断功能开启
	-- if TabIndex[tab_index] == TabIndex.baoju_medal and not OpenFunWGData.Instance:CheckIsHide("baoju_medal") then
	-- 	TipWGCtrl.Instance:ShowSystemMsg(Language.Common.FuncNoOpen)
	-- 	return
	-- end

	local param_t = {
		open_param = nil,			--打开面板参数
		sub_view_name = nil,		--打开二级面板
		to_ui_name = 0,				--跳转ui
		to_ui_param = 0,			--跳转ui参数
	}
	param_t.item_id = data and data.item_id or 0
	if t[3] ~= nil then
		local key_value_list = Split(t[3], ",")
		for k,v in pairs(key_value_list) do
			local key_value_t = Split(v, "=")
			local key = key_value_t[1]
			local value = key_value_t[2]

			if key == "sub" then
				param_t.sub_view_name = value
			elseif key == "op" then
				param_t.open_param = value
			elseif key == "uin" then
				param_t.to_ui_name = value
			elseif key == "uip" then
				param_t.to_ui_param = value
			end
		end
	end
	local index = TabIndex[tab_index]
	if tonumber(tab_index) then
		index = tonumber(tab_index)
	end
	self:Open(view_name, index, flush_key or "all", param_t)
end

-- 排队打开界面
function ViewManager:OpenByQueue(view)
	if #self.open_queue_list == 0 then
		view:Open()
	end
	table.insert(self.open_queue_list, view)
end

-- 关闭界面
function ViewManager:Close(view_name, ...)
	local now_view = self.view_list[view_name]
	if nil ~= now_view then
		now_view:Close(...)
	end
end

-- 关闭所有界面
function ViewManager:CloseAll()
	for k,v in pairs(self.view_list) do
		if v:CanActiveClose() then
			if v:IsOpen() then
				v:Close()
			end
		end
	end
end

function ViewManager:PrintOpenView()
	for k,v in pairs(self.view_list) do
		if v:IsOpen() then
			print_error("PrintOpenView", v:GetViewName())
		end
	end
end

function ViewManager:ForceCloseAll()
	for k,v in pairs(self.view_list) do
		if v:IsOpen() then
			v:Close()
		end
	end
end

-- 关闭界面
function ViewManager:CloseAllViewExceptViewName(view_name, value)
	local no_view_name = view_name
	if no_view_name == GuideModuleName.ActivityDetail then
		local act_id = tonumber(value)
		if act_id ~= nil then
			if act_id == ACTIVITY_TYPE.KF_ONEVONE then
				no_view_name = GuideModuleName.KuaFu1v1
			elseif act_id == ACTIVITY_TYPE.CLASH_TERRITORY then
				no_view_name = GuideModuleName.ClashTerritory
			elseif act_id == ACTIVITY_TYPE.GONGCHENGZHAN then
				no_view_name = GuideModuleName.CityCombatView
			end
		end
	end

	for k, v in pairs(self.view_list) do
		if v:CanActiveClose() and k ~= no_view_name then
			if v:IsOpen() then
				v:Close()
			end
		end
	end
end

-- 关闭界面
function ViewManager:CloseNormalViewExceptViewName(view_name)
	local no_view_name = view_name
	local list = self.open_view_list[UiLayer.Normal]
	if list ~= nil then
		for k, v in pairs(list) do
			if v ~= nil and v:CanActiveClose() then
				local view_name = v:GetViewName()
				if view_name ~= nil and view_name ~= no_view_name and v:IsOpen() then
					v:Close()
				end
			end
		end
	end
end

-- 是否可以显示该UI
function ViewManager:CheckShowUi(view_name, index, tab_index)
	return true
end

-- 刷新界面
function ViewManager:FlushView(view_name, ...)
	local now_view = self.view_list[view_name]
	if nil ~= now_view and now_view:IsOpen() then
		now_view:Flush(...)
	end
end

-- 获得UI节点
function ViewManager:GetUiNode(view_name, node_name)
	local now_view = self.view_list[view_name]
	if nil ~= now_view then
		return now_view:OnGetUiNode(node_name)
	end
	return nil
end

function ViewManager:AddOpenView(view)
	self:RemoveOpenView(view, true)
	local layer = view:GetLayer()
	self.open_view_list[layer] = self.open_view_list[layer] or {}
	table.insert(self.open_view_list[layer], view)

	self:CheckViewRendering()
end

function ViewManager:RemoveOpenView(view, ignore)
	local view_layer = view:GetLayer()
	if nil == self.open_view_list[view_layer] then
		return
	end

	for k, v in ipairs(self.open_view_list[view_layer]) do
		if v == view then
			v.__sort_order__ = 0
			table.remove(self.open_view_list[view_layer], k)
			break
		end
	end

	if not ignore then
		self:CheckViewRendering()
	end

	self:CheckOpenQueue(view)
end

function ViewManager:CheckHasNormalViewRendering()
	if IsEmptyTable(self.open_view_list) or IsEmptyTable(self.open_view_list[UiLayer.Normal]) then
		return false
	end

	local is_normal_view_rendering = false
	for k,v in pairs(self.open_view_list[UiLayer.Normal]) do
		if v:IsOpen() and v:IsRendering() then
			is_normal_view_rendering = true
			break
		end
	end
	return is_normal_view_rendering
end

local is_full_screen = false
local can_inactive = false
local is_open = false
local is_rendering = false
local unlock_view = nil
local unlock_view_isopen = false
local snap_shot_view = nil
local screen_shot_view = nil
local screen_shot_view_isopen = false

local cant_inactive_view_list = {
	[GuideModuleName.TaskDialog] = true,
	-- [GuideModuleName.PowerChange] = true,
	[GuideModuleName.TipsDisconnectedView] = true,
	[GuideModuleName.Unlock] = true,
	[GuideModuleName.SceneLoading] = true,
	[GuideModuleName.ScreenShotView] = true,
	[GuideModuleName.SnapShotView] = true,
	[GuideModuleName.DujieOperateView] = true,
	[GuideModuleName.DujieInviteView] = true,
}


local ui_cg_view_list = {
	[GuideModuleName.DujieView] = true,
	[GuideModuleName.DujieInviteView] = true,
	[GuideModuleName.Alert] = true,
	[GuideModuleName.ConfirmAlert] = true,
	[GuideModuleName.DujieResultFailView] = true,
	[GuideModuleName.DujieResultSuccessView] = true,
	[GuideModuleName.FuhuoView] = true,
	[GuideModuleName.NumKeypad] = true,
}

-- 检查并更新所有界面的渲染状态
-- 该函数负责管理UI界面的显示/隐藏逻辑
-- 主要功能：
-- 1. 根据特殊界面状态（解锁界面、截图界面等）决定其他界面是否需要隐藏
-- 2. 检测是否有全屏界面，并据此控制场景可见性和资源加载策略
-- 3. 动态调整界面渲染状态
function ViewManager:CheckViewRendering()
	-- 全屏状态标记
	is_full_screen = false

	-- 获取解锁界面实例并检查其开启状态
	-- 解锁界面通常用于功能解锁提示，开启时需要隐藏其他界面以突出显示
	unlock_view = unlock_view or self:GetView(GuideModuleName.Unlock)
	unlock_view_isopen = unlock_view and unlock_view.is_real_open
	if nil == unlock_view_isopen and unlock_view then
		unlock_view_isopen = unlock_view.is_open
	end

	-- 获取截图相关界面实例并检查开启状态
	-- 截图时需要隐藏不必要的UI元素以获得干净的截图效果
	snap_shot_view = snap_shot_view or self:GetView(GuideModuleName.SnapShotView)
	screen_shot_view = screen_shot_view or self:GetView(GuideModuleName.ScreenShotView)
	screen_shot_view_isopen = (screen_shot_view and screen_shot_view:IsOpen()) or (snap_shot_view and snap_shot_view:IsOpen())

	for i = UiLayer.MaxLayer, 0, -1 do
		local layer_view_list = self.open_view_list[i]
		if layer_view_list then
			for j = #layer_view_list, 1, -1 do
				local view = layer_view_list[j]
				-- 当前界面的可隐藏状态
				can_inactive = false

				if view then
					-- 检查当前界面是否可以被设置为非活跃状态（隐藏）
					-- cant_inactive_view_list包含了不能被隐藏的界面
					if not cant_inactive_view_list[view.view_name] then
						-- 判断界面是否应该被隐藏的条件：
						-- 1. 解锁界面开启时，除主界面外的其他界面都应该隐藏
						if unlock_view_isopen and view.view_name ~= GuideModuleName.MainUIView then
							can_inactive = true
						-- 2. 当前已有全屏界面时，后续界面应该隐藏
						elseif is_full_screen then
							can_inactive = true
						-- 3. 界面在可隐藏列表中（通过AddCanInactiveView添加）
						elseif self.can_inactive_view_list[view.view_name] then
							can_inactive = true
						-- 4. 截图界面开启时，其他界面应该隐藏以获得干净截图
						elseif screen_shot_view_isopen then
							can_inactive = true
						end
					end

					-- 获取界面的真实开启状态
					if nil ~= view.is_real_open then
						is_open = view.is_real_open
					else
						is_open = view.is_open
					end

					-- 获取界面当前的渲染状态
					is_rendering = view:IsRendering()

					local is_need_render_view = is_rendering
					-- 如果界面已开启且不是自控制渲染，并且当前渲染状态与期望状态不符，则更新渲染状态
					-- self_control_rendring: 界面自己控制渲染状态，不受ViewManager管理
					if is_open and not view.self_control_rendring and is_rendering ~= not can_inactive then
						-- 对于特殊的CG界面（如渡劫界面），使用SetRootNodeActive而不是SetRendering
						if ui_cg_view_list[view.view_name] then
							view:SetRootNodeActive(not can_inactive)
						else
							view:SetRendering(not can_inactive)
						end

						is_need_render_view = not can_inactive
					end

					-- 检查当前界面是否会导致全屏状态
					if is_need_render_view and (view.view_style ~= ViewStyle.Window or view.is_need_depth) and not is_full_screen and not unlock_view_isopen then
						is_full_screen = true
					end
				end
			end
		end
	end

	-- 根据是否有全屏界面来决定场景的可见性
	local is_scene_visible = not is_full_screen

	-- 根据场景可见性调整资源加载策略
	-- 场景可见时使用低负载模式以保证帧率，场景不可见时可以使用高负载模式
	if is_scene_visible then
		AssetBundleMgr:ReqLowLoad()
	else
		AssetBundleMgr:ReqHighLoad()
	end

	-- 控制战斗飘字的显示状态
	-- 只有在场景可见、场景未加载中且战斗飘字实例存在时才显示
	if Scene.Instance ~= nil and not Scene.Instance:IsSceneLoading() and FightText.Instance then
		FightText.Instance:SetActive(is_scene_visible)
	end
end

function ViewManager:AddCanInactiveView(view_name)
	if not view_name then
		return
	end

	if self:IsOpen(view_name) then
		local view = self:GetView(view_name)
		view:SetRendering(false)
	end
	self.can_inactive_view_list[view_name] = true
end

function ViewManager:RemoveCanInactiveView(view_name)
	if not view_name then
		return
	end

	self.can_inactive_view_list[view_name] = nil
end

-- 
function ViewManager:AddCanInactiveViewList(view_name)
	for i = UiLayer.MaxLayer, 0, -1 do
		local layer_view_list = self.open_view_list[i]
		if layer_view_list then
			for j = #layer_view_list, 1, -1 do
				local view = layer_view_list[j]
				if view then
					if view.view_name ~= view_name then
						self.can_inactive_view_list[view.view_name] = true
						if view:IsOpen() then
							view:SetRendering(false)
						end
					end
				end
			end
		end
	end
end

function ViewManager:RemoveCanInactiveViewList()
	self.can_inactive_view_list = {}
	self:CheckViewRendering()
end

function ViewManager:CheckOpenQueue(view)
	if #self.open_queue_list == 0 then
		return
	end
	local open_queue_list = self.open_queue_list
	for i=1,#open_queue_list do
		if open_queue_list[i] == view then
			table.remove(self.open_queue_list, i)
			if open_queue_list[1] then
				open_queue_list[1]:Open()
			end
			break
		end
	end
end

-- 
function ViewManager:GetNormalLayerTopView()
	local view
	local list = self.open_view_list[UiLayer.Normal]
	if list then
		view = list[#list]
	end
	return view
end

function ViewManager:GetPopLayerTopView()
	local view
	local list = self.open_view_list[UiLayer.Pop]
	if list then
		view = list[#list]
	end
	return view
end

--[[
	解决在主界面展示的view与主界面的层级问题
	主界面切换按钮事件响应，主界面展示的view节点做对应的移动
	要求view的node_list对应的节点名相同，且预支体上的localPosition = vector2(0, 0)
]]
local MoveDir = {
	Up = 1,
	Down = 2,
	Left = 3,
	Right = 4,
}

-- 主界面右上角列表切换
local RightTopDirList = {
	rt_move_up_node = MoveDir.Up,
	rt_move_down_node = MoveDir.Down,
	rt_move_left_node = MoveDir.Left,
	rt_move_right_node = MoveDir.Right,
	both_move_up_node = MoveDir.Up,
	both_move_down_node = MoveDir.Down,
	both_move_left_node = MoveDir.Left,
	both_move_right_node = MoveDir.Right,
}
function ViewManager:AddMainUIRightTopChangeList(view)
	self.mainui_right_top_change_list[view] = true 
end

-- 注意逻辑顺序，不可界面销毁后再从列表移除
function ViewManager:RemoveMainUIRightTopChangeList(view)
	self.mainui_right_top_change_list[view] = nil
	local node_list = view and view.node_list
	if node_list ~= nil then
		for k, dir in pairs(RightTopDirList) do
			if node_list[k] then
				self:KillNodeMoveTween(node_list[k])
			end
		end
	end
end

function ViewManager:MainUIRightTopChangeEvent(is_on)
	for view, v in pairs(self.mainui_right_top_change_list) do
		local node_list = view and view.node_list
		for k, dir in pairs(RightTopDirList) do
			if node_list[k] then
				self:DoNodeMoveTween(node_list[k], dir, is_on)
			end
		end
	end
end


-- 主界面副屏切换
local FuPingDirList = {
	fp_move_up_node = MoveDir.Up,
	fp_move_down_node = MoveDir.Down,
	fp_move_left_node = MoveDir.Left,
	fp_move_right_node = MoveDir.Right,
	both_move_up_node = MoveDir.Up,
	both_move_down_node = MoveDir.Down,
	both_move_left_node = MoveDir.Left,
	both_move_right_node = MoveDir.Right,
}
function ViewManager:AddMainUIFuPingChangeList(view)
	self.mainui_fu_ping_change_list[view] = true 
end

-- 注意逻辑顺序，不可界面销毁后再从列表移除
function ViewManager:RemoveMainUIFuPingChangeList(view)
	self.mainui_fu_ping_change_list[view] = nil
	local node_list = view and view.node_list
	if node_list ~= nil then
		for k, dir in pairs(FuPingDirList) do
			if node_list[k] then
				self:KillNodeMoveTween(node_list[k])
			end
		end
	end
end

function ViewManager:MainUIFuPingChangeEvent(is_on)
	for view, v in pairs(self.mainui_fu_ping_change_list) do
		local node_list = view and view.node_list
		for k, dir in pairs(FuPingDirList) do
			if node_list[k] then
				if not self:MainUIFuPingChangeSpecialViewHandle(view, k, dir, is_on) then
					self:DoNodeMoveTween(node_list[k], dir, is_on)
				end
			end
		end
	end
end

-- 副屏切换回来需要关注右边顶部任务栏状态
local FuPingCareRightTop = {
	["BossNormalHurtView"] = "both_move_right_node",
	["PositionalWarfareSceneView"] = "both_move_right_node",
	["guildbosstaskview"] = "both_move_right_node",
	["GuildAnswerStartTimer"] = "both_move_right_node",
	["GuildShowHuRankView"] = "both_move_right_node",
	["HonorhallsView"] = "both_move_right_node",
	["ShenyuanBossHurtView"] = "both_move_right_node",
	["EternalNightTaskView"] = "both_move_right_node",
	["KF3V3LogicView"] = "both_move_up_node",
	["BOSSInvasionSceneView"] = "both_move_right_node",
}

function ViewManager:MainUIFuPingChangeSpecialViewHandle(view, key, dir, is_on)
	if FuPingCareRightTop[view.view_name] and FuPingCareRightTop[view.view_name] == key then
		if not is_on then
			if MainuiWGCtrl.Instance.view:GetShrinkButtonIsOn() == true then
				return true
			end
		end
	end

	return false
end


-- 节点移动
function ViewManager:DoNodeMoveTween(node, dir, is_on)
	local node_transform = node and node.transform
	if not node_transform or not dir then
		return
	end

	self:KillNodeMoveTween(node)
	local move_time = 0.5
	local tween
	if dir == MoveDir.Up then
		tween = node_transform:DOLocalMoveY(is_on and 768 or 0, move_time)
	elseif dir == MoveDir.Down then
		tween = node_transform:DOLocalMoveY(is_on and -768 or 0, move_time)
	elseif dir == MoveDir.Left then
		tween = node_transform:DOLocalMoveX(is_on and -1334 or 0, move_time)
	elseif dir == MoveDir.Right then
		tween = node_transform:DOLocalMoveX(is_on and 1334 or 0, move_time)
	end

	self.view_node_tween[node] = tween
end

function ViewManager:KillNodeMoveTween(node)
	if self.view_node_tween[node] then
		self.view_node_tween[node]:Kill()
		self.view_node_tween[node] = nil
	end
end

--=================== scene 摄像机后处理 开启设置 ====================
local EXCLUDE_VIEW = {
	[GuideModuleName.SceneLoading] = true,
	[GuideModuleName.select_server] = true,
	[GuideModuleName.Login] = true,
	[GuideModuleName.MainUIView] = true,
}

--[[
	1、如果有界面开启 
			ViewStyle.Half				-- 半屏
			ViewStyle.Full				-- 全屏
			self.is_need_depth = true	-- 开启景深
			计数加一，且关掉场景的PP
]]
function ViewManager:CheckIsNeedOpenUICameraPP()
	local active_ui_volume_count = 0
	local not_active_ui_volume_count = 0
	for layer, view_list in pairs(self.open_view_list) do
		for k, view in pairs(view_list) do
			if (not EXCLUDE_VIEW[view.view_name]) and (view.view_style ~= ViewStyle.Window or view.is_need_depth) then
				active_ui_volume_count = active_ui_volume_count + 1
				if view.is_not_active_ui_volume then
					not_active_ui_volume_count = not_active_ui_volume_count + 1
				end
			end
		end
	end

	local is_act_scene_pp = active_ui_volume_count <= 0
	return is_act_scene_pp
end

-- 更新scene摄像机
function ViewManager:UpdateSceneCameraPP()
	local is_act_scene_pp = self:CheckIsNeedOpenUICameraPP()
	if Scene and Scene.Instance then
		Scene.Instance:ChangeSceneVolumeState(is_act_scene_pp)
	end
end
--================== scene 摄像机后处理 开启设置 end =====================


--================== 控制SnapShotBackground 和 scene显示 =====================
local ui_scene_view_index = 0
--[[
	管理UI场景显示列表
	
	@param view - 视图对象
	@param is_show_scene - true:添加到列表, false:从列表移除
	@param scene_data - 场景数据（包含type等信息）
]]
function ViewManager:AddUISceneShowList(view, is_show_scene, scene_data)
	-- print_error("AddUISceneShowList", view.view_name, is_show_scene, scene_data)
	if not view then
		return
	end

	if is_show_scene then
		self:AddViewToUISceneList(view, scene_data)
	else
		self:RemoveViewFromUISceneList(view)
	end

	self:UpdateUISceneShowState()
end

-- 添加视图到UI场景列表
function ViewManager:AddViewToUISceneList(view, scene_data)
	local existing_data = self:FindUISceneViewData(view.view_name)
	
	if existing_data then
		-- 更新已存在的视图数据
		self:UpdateExistingUISceneView(existing_data, view, scene_data)
	else
		-- 添加新的UI场景视图
		self:AddNewUISceneView(view, scene_data)
	end
end

-- 移除视图从UI场景列表
function ViewManager:RemoveViewFromUISceneList(view)
	local old_view_name = self.new_view_name
	local removed = self:RemoveUISceneViewData(view.view_name)
	
	if removed then
		self:UpdateCurrentUISceneView()
		self:SwitchRoleModelActiveStatus(old_view_name, self.new_view_name)
	end
end

-- 查找UI场景视图数据
function ViewManager:FindUISceneViewData(view_name)
	for _, data in pairs(self.show_ui_scene_view_names) do
		if data.view_name == view_name then
			return data
		end
	end
	return nil
end

-- 添加新的UI场景视图
-- 只有包含有效场景类型的视图才会被添加到UI场景列表
function ViewManager:AddNewUISceneView(view, scene_data)
	-- 只有有效的场景数据才创建UI场景视图
	if not (scene_data and scene_data.type ~= nil) then
		return
	end
	
	local old_view_name = self.new_view_name
	ui_scene_view_index = ui_scene_view_index + 1
	
	local new_data = {
		scene_data = scene_data,
		view_name = view.view_name,
		view_layer = view.view_layer,
		sort_index = ui_scene_view_index,  -- 用于同层级视图的时间排序
	}
	
	table.insert(self.show_ui_scene_view_names, new_data)
	self:SortUISceneViewList()
	self:UpdateCurrentUISceneView()
	
	-- 隐藏旧界面的模型
	self:SetCtrlRoleModelActiveStatus(old_view_name, false)
end

-- 更新已存在的UI场景视图
-- 当视图重新打开或场景数据变更时调用
function ViewManager:UpdateExistingUISceneView(existing_data, view, scene_data)
	local old_view_name = self.new_view_name
	
	existing_data.scene_data = scene_data
	
	-- 如果是不同的视图重新激活，需要提升其优先级到最高
	if view.view_name ~= old_view_name then
		ui_scene_view_index = ui_scene_view_index + 1
		existing_data.sort_index = ui_scene_view_index
		self:SortUISceneViewList()
	end
	
	self:UpdateCurrentUISceneView()
	self:SwitchRoleModelActiveStatus(old_view_name, self.new_view_name)
end

-- 移除UI场景视图数据
function ViewManager:RemoveUISceneViewData(view_name)
	local removed = false
	for i = #self.show_ui_scene_view_names, 1, -1 do
		if self.show_ui_scene_view_names[i].view_name == view_name then
			table.remove(self.show_ui_scene_view_names, i)
			removed = true
			
		end
	end
	return removed
end

-- 更新当前UI场景视图信息
function ViewManager:UpdateCurrentUISceneView()
	if self.show_ui_scene_view_names[1] then
		self.new_view_name = self.show_ui_scene_view_names[1].view_name
		self.new_ui_scene_data = self.show_ui_scene_view_names[1].scene_data
	else
		self.new_view_name = nil
		self.new_ui_scene_data = nil
	end
end

-- 切换角色模型显示状态
function ViewManager:SwitchRoleModelActiveStatus(old_view_name, new_view_name)
	-- 隐藏旧界面的模型
	self:SetCtrlRoleModelActiveStatus(old_view_name, false)
	-- 显示新界面的模型（如果存在）
	if new_view_name then
		self:SetCtrlRoleModelActiveStatus(new_view_name, true, old_view_name ~= new_view_name)
	end
end

-- 排序UI场景视图列表
function ViewManager:SortUISceneViewList()
	table.sort(self.show_ui_scene_view_names, SortTools.KeyUpperSorters("view_layer", "sort_index"))
end

-- 设置UI场景模型显示隐藏
function ViewManager:SetCtrlRoleModelActiveStatus(view_name, status, is_flush_model)
	if not view_name then
		return
	end

	local cur_view = self:GetView(view_name)
	if cur_view and cur_view:IsOpen() then
		cur_view:CtrlRoleModelActiveByOtherView(status)

		if is_flush_model then
			-- 回到其他ui_scene时，刷新模型
			-- 因为模型被隐藏，激活显示后，需要重新播放动画
			self:FlushView(view_name, nil, "flush_model")
		end
	end
end

local snap_shot_bg_view_index = 0
-- 控制模糊背景显示
function ViewManager:CtrlSnapShotBackgroundShow(view)
	if not view then
		return
	end

	if view.view_style == ViewStyle.Window and not view.is_need_depth then
		return
	end

	local is_ui_scene_view = not IsEmptyTable(view.ui_scene_show_tabs)
	if view:IsOpen() then
		if view.is_need_depth then
			self:TryPlayScreenShot()
		end

		snap_shot_bg_view_index = snap_shot_bg_view_index + 1
		local data =
		{
			view_name = view.view_name,
			view_layer = view.view_layer,
			is_ui_scene_view = is_ui_scene_view,
			is_need_depth = view.is_need_depth,
			sort_index = snap_shot_bg_view_index,
		}

		table.insert(self.show_snap_shot_bgs, data)
		table.sort(self.show_snap_shot_bgs, SortTools.KeyUpperSorters("view_layer", "sort_index"))
	else
		if view.is_need_depth then
			self:TryStopScreenShot()
		end

		snap_shot_bg_view_index = snap_shot_bg_view_index - 1
		for i = #self.show_snap_shot_bgs, 1, -1 do
			if self.show_snap_shot_bgs[i].view_name == view.view_name then
				table.remove(self.show_snap_shot_bgs, i)
			end
		end
	end

	local is_show_snap_shot_bg = false
	for k,v in ipairs(self.show_snap_shot_bgs) do
		if v.is_need_depth or v.is_ui_scene_view then
			is_show_snap_shot_bg = v.is_need_depth and not v.is_ui_scene_view
			break
		end
	end

	if MainuiWGCtrl.Instance then
		MainuiWGCtrl.Instance:SetSnapShotBackgroundShow(is_show_snap_shot_bg)
	end
end

function ViewManager:SetSnapShotBackgroundState(is_show)
	self.is_show_snap_shot_background = is_show
end

function ViewManager:GetIsShowUISceneState()
	return #self.show_ui_scene_view_names > 0
end

function ViewManager:UpdateUISceneShowState()
	if self.new_ui_scene_data ~= self.old_ui_scene_data then
		local is_show = self:GetIsShowUISceneState()
		-- 控制scene显示
		if not is_show then
			-- 没有UI场景，显示当前可行走场景
			self.old_ui_scene_data = nil
			Scene.Instance:ResetWalkableSceneShow()
		else
			-- 有UI场景
			self.old_ui_scene_data = self.new_ui_scene_data
			if self.new_ui_scene_data and next(self.new_ui_scene_data) == nil then
				-- data 为{}时不主动设置显示，隐藏当前可行走场景Main节点但不显示UI场景
				Scene.Instance:HideWalkableSceneShow()
			else
				-- 显示具体的UI场景
				self:ForceShowCurrentUIScene()
			end
		end
	end
end

-- 强制显示当前应该显示的UI场景
function ViewManager:ForceShowCurrentUIScene()
	if IsEmptyTable(self.new_ui_scene_data) then
		return
	end
	
	local cfg = ConfigManager.Instance:GetAutoConfig("ui_scene_config_auto").ui_scene or {}
	local type = self.new_ui_scene_data.type or UI_SCENE_TYPE.DEFAULT
	local bundle_name, asset_name
	for k,v in pairs(cfg) do
		if type == v.type then
			bundle_name = v.bundle_name
			asset_name = v.asset_name
		end
	end
	
	if bundle_name and asset_name then
		local scene_data = {}
		for k,v in pairs(self.new_ui_scene_data) do
			if k ~= "type" then
				scene_data[k] = v
			end
		end
		
		Scene.Instance:ChangeUISceneShow(bundle_name, asset_name, true, scene_data)

		if self.new_view_name then
			local view = self:GetView(self.new_view_name)
			if view and view.ui_scene_change_config_index ~= -1 then
				Scene.Instance:ChangeUISceneController(type, scene_data.config_index, view.ui_scene_change_config_index)
			end
		end
		GlobalEventSystem:Fire(SceneEventType.UI_SCENE_CHANGE_COMPLETE, type, scene_data.config_index)
	end
end

----[[ UI背景模糊
function ViewManager:TryPlayScreenShot()
	if not IsNil(MainCameraSnapshot) then
		SafeBaseView.OpenScreenShot()
	end
end

function ViewManager:TryStopScreenShot()
	if not IsNil(MainCameraSnapshot) then
		SafeBaseView.CloseScreenShot()
	end
end

function ViewManager:SetWaitScreenShotView(view, index)
	self.view_wait_screen_shot_open_list[view] = index or 0
end

function ViewManager:OpenWaitScreenShotView()
	if (MainuiWGCtrl.Instance and not MainuiWGCtrl.Instance:HadSnapShotBackground()) or IsNil(MainCameraSnapshot) then
		return
	end

	if IsEmptyTable(self.view_wait_screen_shot_open_list) then
		return
	end

	TryDelayCall(self, function()
		for k,v in pairs(self.view_wait_screen_shot_open_list) do
			k:Open(v)
		end
	
		self.view_wait_screen_shot_open_list = {}
	end, 0.5, "OpenWaitScreenShotView")
end
--]]

function ViewManager:SetWaitSceneLoadedView(view, index)
	self.view_wait_scene_loaded_list[view] = index or 0
end

function ViewManager:OpenWaitSceneLoadedView()
	if Scene.Instance:IsSceneLoadingStateEnter() then
		return
	end

	for k,v in pairs(self.view_wait_scene_loaded_list) do
		k:Open(v)
	end

	self.view_wait_scene_loaded_list = {}
end

--==================控制SnapShotBackground 和 scene显示  end =====================

-- 单一界面关联界面，关联界面打开不受限制
local wait_only_view_about_list = {
	[GuideModuleName.DujieOperateView] = {
		[GuideModuleName.DujieInviteView] = true,
		[GuideModuleName.Alert] = true,
		[GuideModuleName.ConfirmAlert] = true,
		[GuideModuleName.DujieResultFailView] = true,
		[GuideModuleName.DujieResultSuccessView] = true,
		[GuideModuleName.FuhuoView] = true,
		[GuideModuleName.NumKeypad] = true,
	},
}

function ViewManager:IsCanOpenWaitOnlyView(only_view,open_view)
	if wait_only_view_about_list[only_view] and wait_only_view_about_list[only_view][open_view] then
		return true
	end
	return false
end

--- 单一界面打开时，其他界面等单一界面关闭再打开 -------
function ViewManager:SetWaitOnlyViewOpenView(view, index)
	self.view_wait_only_view_open_list[view] = index or 0
end

function ViewManager:OpenWaitOnlyViewOpenView()
	if (MainuiWGCtrl.Instance and not MainuiWGCtrl.Instance:IsLoaded()) then
		return
	end

	if IsEmptyTable(self.view_wait_only_view_open_list) then
		return
	end

	for k,v in pairs(self.view_wait_only_view_open_list) do
		k:Open(v)
	end

	self.view_wait_only_view_open_list = {}
end
--- 单一界面打开时，其他界面等单一界面关闭再打开 end-------

-- 处理Cg播放时候  ui场景模型界面打开出现异常
function ViewManager:CloseUiSceneView()
	for layer, view_list in pairs(self.open_view_list) do
		for k, view in pairs(view_list) do
			local is_ui_scene_view = not IsEmptyTable(view.ui_scene_show_tabs)
			if is_ui_scene_view and view:CanActiveClose() and view:IsOpen() then
				view:Close()
			end
		end
	end
end

	