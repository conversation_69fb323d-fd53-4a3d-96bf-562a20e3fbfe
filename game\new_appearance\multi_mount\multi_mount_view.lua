NewAppearanceWGView = NewAppearanceWGView or BaseClass(SafeBaseView)

function NewAppearanceWGView:MultiMountLoadCallBack()
    if self.multi_star_list == nil then
        self.multi_star_list = {}
        for i = 1, 5 do
            self.multi_star_list[i] = self.node_list["multi_star_" .. i]
        end
    end

    if not self.multi_upstar_attr_list then
        self.multi_upstar_attr_list = {}
        local parent_node = self.node_list["multi_upstar_attr_list"]
        local attr_num = parent_node.transform.childCount
        for i = 1, attr_num do
            local cell = CommonAddAttrRender.New(parent_node:FindObj("attr_" .. i))
            cell:SetIndex(i)
            self.multi_upstar_attr_list[i] = cell
        end
    end

    if self.multi_stuff_item == nil then
        self.multi_stuff_item = ItemCell.New(self.node_list["multi_stuff_item"])
    end

    if not self.multi_mount_list then
        self.multi_mount_list = AsyncListView.New(MultiMountItemCellRender, self.node_list.multi_mount_list)
        self.multi_mount_list:SetSelectCallBack(BindTool.Bind(self.OnSelectMultiMountHandler, self))
        self.multi_mount_list:SetStartZeroIndex(true)
    end

    self.select_multi_mount_seq = -1
    self.select_multi_mount_data = {}

    XUI.AddClickEventListener(self.node_list["multi_btn_use"], BindTool.Bind(self.OnClickMultiMountUse, self))
    XUI.AddClickEventListener(self.node_list["multi_btn_reset"], BindTool.Bind(self.OnClickMultiMountReset, self))
    XUI.AddClickEventListener(self.node_list["multi_btn_upstar"], BindTool.Bind(self.OnClickMultiMountUpStar, self))

    -- if not self.multi_select_item_grid then
    --     self.multi_select_item_grid = {}
    --     self.multi_cell_list = {}

    --     for i = 1, GameEnum.ITEM_COLOR_XUAN_QING do
	-- 		self.multi_select_item_grid[i] = {}
	-- 		self.multi_select_item_grid[i].text_name = self.node_list["multi_btn_text" .. i]
    --         self.multi_select_item_grid[i].text_name_hl = self.node_list["multi_btn_text_hl" .. i]
	-- 		self.multi_select_item_grid[i].list = self.node_list["multi_list_".. i]
    --         self.node_list["multi_select_btn" .. i].accordion_element.isOn = false
    --         self.node_list["multi_select_btn" .. i]:SetActive(false)
	-- 	    self.node_list["multi_list_" .. i]:SetActive(false)
	-- 		self.node_list["multi_select_btn" .. i].accordion_element:AddValueChangedListener(BindTool.Bind(self.OnClickmultiSelectTogHandler, self, i))
	-- 	end
    -- end
end

function NewAppearanceWGView:MultiMountShowIndexCallBack()
    self.select_multi_mount_seq = -1
    self.select_multi_mount_data = {}
end

function NewAppearanceWGView:MultiMountReleaseCallBack()
    if self.multi_upstar_attr_list then
        for k, v in pairs(self.multi_upstar_attr_list) do
            v:DeleteMe()
        end

        self.multi_upstar_attr_list = nil
    end

    if self.multi_stuff_item then
        self.multi_stuff_item:DeleteMe()
        self.multi_stuff_item = nil
    end

    self.multi_select_item_grid = nil
    self.multi_star_list = nil

    if self.multi_mount_list then
        self.multi_mount_list:DeleteMe()
        self.multi_mount_list = nil
    end

    -- if self.multi_cell_list then
	-- 	for k,v in pairs(self.multi_cell_list) do
	-- 		for k1,v1 in pairs(v) do
	-- 			v1:DeleteMe()
	-- 		end
	-- 		self.multi_cell_list[k] = nil
	-- 	end

	-- 	self.multi_cell_list = nil
	-- end
end

function NewAppearanceWGView:MultiMountOnFlush()
    local data_list = MultiMountWGData.Instance:GetTotalMountCfgDataList()
    self.multi_mount_list:SetDataList(data_list)

    -- self.node_list.multi_mount_list:CustomSetActive(#data_list >= 1)

    if self.select_multi_mount_seq < 0 then
        local default_select_index = self:CalDefaultSelectMulyiMount(data_list)
        self.multi_mount_list:JumpToIndex(default_select_index)
    else
        self:OnFlushMultiMountView()
    end
end

function NewAppearanceWGView:CalDefaultSelectMulyiMount(data_list)
    if not IsEmptyTable(data_list) then
        for k, v in pairs(data_list) do
            if MultiMountWGData.Instance:GetMultiMountRemindBySeq(v.seq) then
                return k
            end
        end
    end

    return 0
end

function NewAppearanceWGView:OnSelectMultiMountHandler(cell)
    if nil == cell or IsEmptyTable(cell.data) then
        return
    end
    
    local data = cell.data

    if self.select_multi_mount_seq == data.seq then
        return
    end

    self.select_multi_mount_seq = data.seq

    self.select_multi_mount_data = data
    self:OnFlushMultiMountView()

    local level = MultiMountWGData.Instance:GetMultiMountLevelBySeq(self.select_multi_mount_seq)
    local cur_level_cfg = MultiMountWGData.Instance:GetMountStarLevelCfg(self.select_multi_mount_seq, level)
    self:ShowFashionMainModel(ResPath.GetMountModel, cur_level_cfg.image_id, function ()
        self.show_model:PlayMountAction()
    end)
end

function NewAppearanceWGView:OnFlushMultiMountView()
    if self.select_multi_mount_seq < 0 or IsEmptyTable(self.select_multi_mount_data) then
        return
    end

    self.node_list.multi_select_name.text.text = self.select_multi_mount_data.name
    local attr_info, cap = MultiMountWGData.Instance:GetMultiMountAttrAndCap(self.select_multi_mount_seq)
    self.node_list.multi_cap_value.text.text = cap
    
    for k, v in pairs(self.multi_upstar_attr_list) do
        v:SetData(attr_info[k] or {})
    end

    local is_huanhua_multi = MultiMountWGData.Instance:IsCurHuanHuaMultiMount(self.select_multi_mount_seq)
    local level = MultiMountWGData.Instance:GetMultiMountLevelBySeq(self.select_multi_mount_seq)
    self.node_list.multi_btn_use:CustomSetActive(level > 0 and not is_huanhua_multi)
    self.node_list.multi_btn_reset:CustomSetActive(level > 0 and is_huanhua_multi)
    self.node_list.multi_stars_list:CustomSetActive(level > 0)
    local is_max_level = MultiMountWGData.Instance:IsMultiMountIsMaxLevel(self.select_multi_mount_seq)
    self.node_list.multi_max_star_flag:CustomSetActive(is_max_level)
    self.node_list.multi_star_no_max_part:CustomSetActive(not is_max_level)

    self.node_list.multi_btn_upstar_text.text.text = level > 0 and Language.MultiMount.UpLevelMultiMount or Language.MultiMount.ActiveMultiMount

    if not is_max_level then
        local cur_level_cfg = MultiMountWGData.Instance:GetMountStarLevelCfg(self.select_multi_mount_seq, level)
        local need_item_id = cur_level_cfg.cost_item_id
        local need_num = cur_level_cfg.cost_item_num
        self.multi_stuff_item:SetData({item_id = need_item_id})
        local num = ItemWGData.Instance:GetItemNumInBagById(need_item_id)
        local is_remind = num >= need_num
        local str_color = is_remind and COLOR3B.D_GREEN or COLOR3B.RED
        self.node_list["multi_need_num"].text.text = string.format("%s/%s", ToColorStr(num, str_color), need_num)
        self.node_list["multi_btn_upstar_remind"]:CustomSetActive(is_remind)

        local star_res_list = GetTwenTyStarImgResByStar(level - 1)
        for k,v in pairs(self.multi_star_list) do
            v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
            self.node_list["multi_star_" .. k].image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
        end
    end
end

function NewAppearanceWGView:OnClickMultiMountUse()
    MultiMountWGCtrl.Instance:SendDoubleMountOperate(DOUBLE_MOUNT_OPERATE_TYPE.USE_SEQ, self.select_multi_mount_seq)
end

function NewAppearanceWGView:OnClickMultiMountReset()
    MultiMountWGCtrl.Instance:SendDoubleMountOperate(DOUBLE_MOUNT_OPERATE_TYPE.USE_SEQ, self.select_multi_mount_seq)
end

function NewAppearanceWGView:OnClickMultiMountUpStar()
    local level = MultiMountWGData.Instance:GetMultiMountLevelBySeq(self.select_multi_mount_seq)
    local cur_level_cfg = MultiMountWGData.Instance:GetMountStarLevelCfg(self.select_multi_mount_seq, level)
    local need_item_id = cur_level_cfg.cost_item_id
    local num = ItemWGData.Instance:GetItemNumInBagById(need_item_id)

    if num >= cur_level_cfg.cost_item_num then
        MultiMountWGCtrl.Instance:SendDoubleMountOperate(DOUBLE_MOUNT_OPERATE_TYPE.UP_STAR, self.select_multi_mount_seq)
    else
        TipWGCtrl.Instance:OpenItem({item_id = need_item_id})
    end
end

-- function NewAppearanceWGView:OnClickmultiSelectTogHandler(index, isOn)
    
-- end


----------------------------------MultiMountItemCellRender------------------------------------
MultiMountItemCellRender = MultiMountItemCellRender or BaseClass(BaseRender)

function MultiMountItemCellRender:LoadCallBack()
    if not self.item then
        self.item = ItemCell.New(self.node_list.item_pos)
        self.item:SetUseButton(false)
    end
end

function MultiMountItemCellRender:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function MultiMountItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.item:SetData({item_id = self.data.show_item_id})

    local remind = MultiMountWGData.Instance:GetMultiMountRemindBySeq(self.data.seq)
    self.node_list.remind:CustomSetActive(remind)
    self.node_list.name.text.text = self.data.name

    local level = MultiMountWGData.Instance:GetMultiMountLevelBySeq(self.data.seq)
    self.node_list.flag_lock:CustomSetActive(level < 0)
end