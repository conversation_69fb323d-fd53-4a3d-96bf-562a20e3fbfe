SkillResetView = SkillResetView or BaseClass(SafeBaseView)
function SkillResetView:__init()
    self:SetMaskBg(true,true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
    self:AddViewResource(0, "uis/view/role_ui_prefab", "layout_skill_reset")
end

function SkillResetView:__delete()

end

function SkillResetView:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function SkillResetView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.Common.Hint
	self:SetSecondView(nil, self.node_list["size"])
    self.item_cell = ItemCell.New(self.node_list["cell"])
    self.item_cell:SetNeedItemGetWay(true)

    self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind(self.OnClickBtnCancel, self))
    self.node_list["btn_ok"].button:AddClickListener(BindTool.Bind(self.OnClickBtnOk, self))
end

function SkillResetView:ShowIndexCallBack()
    self:Flush()
end

function SkillResetView:OnFlush()
    local cfg = SkillWGData.Instance:GetSkillResetCfg()
    self.item_cell:SetData({item_id = cfg.upgrade_consume_stuff_id})
    local num = ItemWGData.Instance:GetItemNumInBagById(cfg.upgrade_consume_stuff_id)
    local str = num .. "/" .. cfg.upgrade_consume_count
    local color = num < cfg.upgrade_consume_count and ITEM_NUM_COLOR.NOT_ENOUGH or ITEM_NUM_COLOR.ENOUGH
    self.item_cell:SetRightBottomTextVisible(true)
    self.item_cell:SetRightBottomColorText(str, color)
end

function SkillResetView:OnClickBtnCancel()
    self:Close()
end

function SkillResetView:OnClickBtnOk()
    local cfg = SkillWGData.Instance:GetSkillResetCfg()
    local num = ItemWGData.Instance:GetItemNumInBagById(cfg.upgrade_consume_stuff_id)
    if num < cfg.upgrade_consume_count then
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cfg.upgrade_consume_stuff_id})
        return
    end
    SkillWGCtrl.Instance:SendCareerUpgrade(CAREER_UPGRADE_OPERA.RESET)
    self:Close()
end
