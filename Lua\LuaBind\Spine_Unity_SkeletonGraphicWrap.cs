﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Spine_Unity_SkeletonGraphicWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(Spine.Unity.SkeletonGraphic), typeof(UnityEngine.UI.MaskableGraphic));
		<PERSON><PERSON>RegFunction("NewSkeletonGraphicGameObject", NewSkeletonGraphicGameObject);
		<PERSON><PERSON>unction("AddSkeletonGraphicComponent", AddSkeletonGraphicComponent);
		<PERSON>.RegFunction("Rebuild", Rebuild);
		<PERSON><PERSON>RegFunction("Update", Update);
		<PERSON>.RegFunction("ApplyTransformMovementToPhysics", ApplyTransformMovementToPhysics);
		<PERSON><PERSON>Function("ApplyAnimation", ApplyAnimation);
		<PERSON>.RegFunction("AfterAnimationApplied", AfterAnimationApplied);
		<PERSON><PERSON>RegFunction("LateUpdate", LateUpdate);
		<PERSON><PERSON>unction("OnBecameVisible", OnBecameVisible);
		<PERSON><PERSON>("OnBecameInvisible", OnBecameInvisible);
		<PERSON><PERSON>unction("ReapplySeparatorSlotNames", ReapplySeparatorSlotNames);
		L.RegFunction("ResetLastPosition", ResetLastPosition);
		L.RegFunction("ResetLastRotation", ResetLastRotation);
		L.RegFunction("ResetLastPositionAndRotation", ResetLastPositionAndRotation);
		L.RegFunction("GetLastMesh", GetLastMesh);
		L.RegFunction("MatchRectTransformWithBounds", MatchRectTransformWithBounds);
		L.RegFunction("SetRectTransformSize", SetRectTransformSize);
		L.RegFunction("Clear", Clear);
		L.RegFunction("TrimRenderers", TrimRenderers);
		L.RegFunction("Initialize", Initialize);
		L.RegFunction("PrepareInstructionsAndRenderers", PrepareInstructionsAndRenderers);
		L.RegFunction("UpdateMesh", UpdateMesh);
		L.RegFunction("UpdateMeshToInstructions", UpdateMeshToInstructions);
		L.RegFunction("HasMultipleSubmeshInstructions", HasMultipleSubmeshInstructions);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("skeletonDataAsset", get_skeletonDataAsset, set_skeletonDataAsset);
		L.RegVar("additiveMaterial", get_additiveMaterial, set_additiveMaterial);
		L.RegVar("multiplyMaterial", get_multiplyMaterial, set_multiplyMaterial);
		L.RegVar("screenMaterial", get_screenMaterial, set_screenMaterial);
		L.RegVar("initialSkinName", get_initialSkinName, set_initialSkinName);
		L.RegVar("initialFlipX", get_initialFlipX, set_initialFlipX);
		L.RegVar("initialFlipY", get_initialFlipY, set_initialFlipY);
		L.RegVar("startingAnimation", get_startingAnimation, set_startingAnimation);
		L.RegVar("startingLoop", get_startingLoop, set_startingLoop);
		L.RegVar("timeScale", get_timeScale, set_timeScale);
		L.RegVar("freeze", get_freeze, set_freeze);
		L.RegVar("layoutScaleMode", get_layoutScaleMode, set_layoutScaleMode);
		L.RegVar("updateWhenInvisible", get_updateWhenInvisible, set_updateWhenInvisible);
		L.RegVar("allowMultipleCanvasRenderers", get_allowMultipleCanvasRenderers, set_allowMultipleCanvasRenderers);
		L.RegVar("canvasRenderers", get_canvasRenderers, set_canvasRenderers);
		L.RegVar("SeparatorPartGameObjectName", get_SeparatorPartGameObjectName, null);
		L.RegVar("separatorSlots", get_separatorSlots, null);
		L.RegVar("enableSeparatorSlots", get_enableSeparatorSlots, set_enableSeparatorSlots);
		L.RegVar("updateSeparatorPartLocation", get_updateSeparatorPartLocation, set_updateSeparatorPartLocation);
		L.RegVar("updateSeparatorPartScale", get_updateSeparatorPartScale, set_updateSeparatorPartScale);
		L.RegVar("disableMeshAssignmentOnOverride", get_disableMeshAssignmentOnOverride, set_disableMeshAssignmentOnOverride);
		L.RegVar("SkeletonDataAsset", get_SkeletonDataAsset, null);
		L.RegVar("color", get_color, set_color);
		L.RegVar("MeshScale", get_MeshScale, null);
		L.RegVar("MeshOffset", get_MeshOffset, null);
		L.RegVar("UpdateMode", get_UpdateMode, set_UpdateMode);
		L.RegVar("SeparatorParts", get_SeparatorParts, null);
		L.RegVar("CustomTextureOverride", get_CustomTextureOverride, null);
		L.RegVar("CustomMaterialOverride", get_CustomMaterialOverride, null);
		L.RegVar("OverrideTexture", get_OverrideTexture, set_OverrideTexture);
		L.RegVar("mainTexture", get_mainTexture, null);
		L.RegVar("Skeleton", get_Skeleton, set_Skeleton);
		L.RegVar("SkeletonData", get_SkeletonData, null);
		L.RegVar("IsValid", get_IsValid, null);
		L.RegVar("AnimationState", get_AnimationState, null);
		L.RegVar("PhysicsPositionInheritanceFactor", get_PhysicsPositionInheritanceFactor, set_PhysicsPositionInheritanceFactor);
		L.RegVar("PhysicsRotationInheritanceFactor", get_PhysicsRotationInheritanceFactor, set_PhysicsRotationInheritanceFactor);
		L.RegVar("PhysicsMovementRelativeTo", get_PhysicsMovementRelativeTo, set_PhysicsMovementRelativeTo);
		L.RegVar("MeshGenerator", get_MeshGenerator, null);
		L.RegVar("SkeletonClipping", get_SkeletonClipping, null);
		L.RegVar("MeshesMultipleCanvasRenderers", get_MeshesMultipleCanvasRenderers, null);
		L.RegVar("MaterialsMultipleCanvasRenderers", get_MaterialsMultipleCanvasRenderers, null);
		L.RegVar("TexturesMultipleCanvasRenderers", get_TexturesMultipleCanvasRenderers, null);
		L.RegVar("UpdateTiming", get_UpdateTiming, set_UpdateTiming);
		L.RegVar("UnscaledTime", get_UnscaledTime, set_UnscaledTime);
		L.RegVar("AssignMeshOverrideSingleRenderer", get_AssignMeshOverrideSingleRenderer, set_AssignMeshOverrideSingleRenderer);
		L.RegVar("AssignMeshOverrideMultipleRenderers", get_AssignMeshOverrideMultipleRenderers, set_AssignMeshOverrideMultipleRenderers);
		L.RegVar("OnRebuild", get_OnRebuild, set_OnRebuild);
		L.RegVar("OnInstructionsPrepared", get_OnInstructionsPrepared, set_OnInstructionsPrepared);
		L.RegVar("OnMeshAndMaterialsUpdated", get_OnMeshAndMaterialsUpdated, set_OnMeshAndMaterialsUpdated);
		L.RegVar("OnAnimationRebuild", get_OnAnimationRebuild, set_OnAnimationRebuild);
		L.RegVar("BeforeApply", get_BeforeApply, set_BeforeApply);
		L.RegVar("UpdateLocal", get_UpdateLocal, set_UpdateLocal);
		L.RegVar("UpdateWorld", get_UpdateWorld, set_UpdateWorld);
		L.RegVar("UpdateComplete", get_UpdateComplete, set_UpdateComplete);
		L.RegVar("OnPostProcessVertices", get_OnPostProcessVertices, set_OnPostProcessVertices);
		L.RegFunction("SkeletonRendererDelegate", Spine_Unity_SkeletonGraphic_SkeletonRendererDelegate);
		L.RegFunction("InstructionDelegate", Spine_Unity_SkeletonGraphic_InstructionDelegate);
		L.RegFunction("MeshAssignmentDelegateMultiple", Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateMultiple);
		L.RegFunction("MeshAssignmentDelegateSingle", Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateSingle);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int NewSkeletonGraphicGameObject(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			Spine.Unity.SkeletonDataAsset arg0 = (Spine.Unity.SkeletonDataAsset)ToLua.CheckObject<Spine.Unity.SkeletonDataAsset>(L, 1);
			UnityEngine.Transform arg1 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
			UnityEngine.Material arg2 = (UnityEngine.Material)ToLua.CheckObject<UnityEngine.Material>(L, 3);
			Spine.Unity.SkeletonGraphic o = Spine.Unity.SkeletonGraphic.NewSkeletonGraphicGameObject(arg0, arg1, arg2);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddSkeletonGraphicComponent(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 1, typeof(UnityEngine.GameObject));
			Spine.Unity.SkeletonDataAsset arg1 = (Spine.Unity.SkeletonDataAsset)ToLua.CheckObject<Spine.Unity.SkeletonDataAsset>(L, 2);
			UnityEngine.Material arg2 = (UnityEngine.Material)ToLua.CheckObject<UnityEngine.Material>(L, 3);
			Spine.Unity.SkeletonGraphic o = Spine.Unity.SkeletonGraphic.AddSkeletonGraphicComponent(arg0, arg1, arg2);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Rebuild(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject<Spine.Unity.SkeletonGraphic>(L, 1);
			UnityEngine.UI.CanvasUpdate arg0 = (UnityEngine.UI.CanvasUpdate)ToLua.CheckObject(L, 2, typeof(UnityEngine.UI.CanvasUpdate));
			obj.Rebuild(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Update(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject<Spine.Unity.SkeletonGraphic>(L, 1);
				obj.Update();
				return 0;
			}
			else if (count == 2)
			{
				Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject<Spine.Unity.SkeletonGraphic>(L, 1);
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				obj.Update(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Spine.Unity.SkeletonGraphic.Update");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ApplyTransformMovementToPhysics(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject<Spine.Unity.SkeletonGraphic>(L, 1);
			obj.ApplyTransformMovementToPhysics();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ApplyAnimation(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject<Spine.Unity.SkeletonGraphic>(L, 1);
			obj.ApplyAnimation();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AfterAnimationApplied(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject<Spine.Unity.SkeletonGraphic>(L, 1);
			obj.AfterAnimationApplied();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int LateUpdate(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject<Spine.Unity.SkeletonGraphic>(L, 1);
			obj.LateUpdate();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnBecameVisible(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject<Spine.Unity.SkeletonGraphic>(L, 1);
			obj.OnBecameVisible();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnBecameInvisible(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject<Spine.Unity.SkeletonGraphic>(L, 1);
			obj.OnBecameInvisible();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ReapplySeparatorSlotNames(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject<Spine.Unity.SkeletonGraphic>(L, 1);
			obj.ReapplySeparatorSlotNames();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResetLastPosition(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject<Spine.Unity.SkeletonGraphic>(L, 1);
			obj.ResetLastPosition();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResetLastRotation(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject<Spine.Unity.SkeletonGraphic>(L, 1);
			obj.ResetLastRotation();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResetLastPositionAndRotation(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject<Spine.Unity.SkeletonGraphic>(L, 1);
			obj.ResetLastPositionAndRotation();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetLastMesh(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject<Spine.Unity.SkeletonGraphic>(L, 1);
			UnityEngine.Mesh o = obj.GetLastMesh();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int MatchRectTransformWithBounds(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject<Spine.Unity.SkeletonGraphic>(L, 1);
			bool o = obj.MatchRectTransformWithBounds();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetRectTransformSize(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2 && TypeChecker.CheckTypes<UnityEngine.UI.Graphic, UnityEngine.Vector2>(L, 1))
			{
				UnityEngine.UI.Graphic arg0 = (UnityEngine.UI.Graphic)ToLua.ToObject(L, 1);
				UnityEngine.Vector2 arg1 = ToLua.ToVector2(L, 2);
				Spine.Unity.SkeletonGraphic.SetRectTransformSize(arg0, arg1);
				return 0;
			}
			else if (count == 2 && TypeChecker.CheckTypes<UnityEngine.RectTransform, UnityEngine.Vector2>(L, 1))
			{
				UnityEngine.RectTransform arg0 = (UnityEngine.RectTransform)ToLua.ToObject(L, 1);
				UnityEngine.Vector2 arg1 = ToLua.ToVector2(L, 2);
				Spine.Unity.SkeletonGraphic.SetRectTransformSize(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Spine.Unity.SkeletonGraphic.SetRectTransformSize");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Clear(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject<Spine.Unity.SkeletonGraphic>(L, 1);
			obj.Clear();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int TrimRenderers(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject<Spine.Unity.SkeletonGraphic>(L, 1);
			obj.TrimRenderers();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Initialize(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject<Spine.Unity.SkeletonGraphic>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.Initialize(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PrepareInstructionsAndRenderers(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject<Spine.Unity.SkeletonGraphic>(L, 1);
				obj.PrepareInstructionsAndRenderers();
				return 0;
			}
			else if (count == 2)
			{
				Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject<Spine.Unity.SkeletonGraphic>(L, 1);
				bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
				obj.PrepareInstructionsAndRenderers(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Spine.Unity.SkeletonGraphic.PrepareInstructionsAndRenderers");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UpdateMesh(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject<Spine.Unity.SkeletonGraphic>(L, 1);
			obj.UpdateMesh();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UpdateMeshToInstructions(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject<Spine.Unity.SkeletonGraphic>(L, 1);
			obj.UpdateMeshToInstructions();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HasMultipleSubmeshInstructions(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject<Spine.Unity.SkeletonGraphic>(L, 1);
			bool o = obj.HasMultipleSubmeshInstructions();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_skeletonDataAsset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			Spine.Unity.SkeletonDataAsset ret = obj.skeletonDataAsset;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index skeletonDataAsset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_additiveMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			UnityEngine.Material ret = obj.additiveMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index additiveMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_multiplyMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			UnityEngine.Material ret = obj.multiplyMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index multiplyMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_screenMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			UnityEngine.Material ret = obj.screenMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index screenMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_initialSkinName(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			string ret = obj.initialSkinName;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index initialSkinName on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_initialFlipX(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			bool ret = obj.initialFlipX;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index initialFlipX on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_initialFlipY(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			bool ret = obj.initialFlipY;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index initialFlipY on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_startingAnimation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			string ret = obj.startingAnimation;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index startingAnimation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_startingLoop(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			bool ret = obj.startingLoop;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index startingLoop on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_timeScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			float ret = obj.timeScale;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index timeScale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_freeze(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			bool ret = obj.freeze;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index freeze on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_layoutScaleMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			Spine.Unity.SkeletonGraphic.LayoutMode ret = obj.layoutScaleMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index layoutScaleMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_updateWhenInvisible(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			Spine.Unity.UpdateMode ret = obj.updateWhenInvisible;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index updateWhenInvisible on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_allowMultipleCanvasRenderers(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			bool ret = obj.allowMultipleCanvasRenderers;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index allowMultipleCanvasRenderers on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_canvasRenderers(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			System.Collections.Generic.List<UnityEngine.CanvasRenderer> ret = obj.canvasRenderers;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index canvasRenderers on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_SeparatorPartGameObjectName(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, Spine.Unity.SkeletonGraphic.SeparatorPartGameObjectName);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_separatorSlots(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			System.Collections.Generic.List<Spine.Slot> ret = obj.separatorSlots;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index separatorSlots on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableSeparatorSlots(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			bool ret = obj.enableSeparatorSlots;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableSeparatorSlots on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_updateSeparatorPartLocation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			bool ret = obj.updateSeparatorPartLocation;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index updateSeparatorPartLocation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_updateSeparatorPartScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			bool ret = obj.updateSeparatorPartScale;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index updateSeparatorPartScale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_disableMeshAssignmentOnOverride(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			bool ret = obj.disableMeshAssignmentOnOverride;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index disableMeshAssignmentOnOverride on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_SkeletonDataAsset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			Spine.Unity.SkeletonDataAsset ret = obj.SkeletonDataAsset;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index SkeletonDataAsset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_color(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			UnityEngine.Color ret = obj.color;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index color on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MeshScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			float ret = obj.MeshScale;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MeshScale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MeshOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			UnityEngine.Vector2 ret = obj.MeshOffset;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MeshOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UpdateMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			Spine.Unity.UpdateMode ret = obj.UpdateMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index UpdateMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_SeparatorParts(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			System.Collections.Generic.List<UnityEngine.Transform> ret = obj.SeparatorParts;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index SeparatorParts on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CustomTextureOverride(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			System.Collections.Generic.Dictionary<UnityEngine.Texture,UnityEngine.Texture> ret = obj.CustomTextureOverride;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CustomTextureOverride on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CustomMaterialOverride(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			System.Collections.Generic.Dictionary<UnityEngine.Texture,UnityEngine.Material> ret = obj.CustomMaterialOverride;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CustomMaterialOverride on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OverrideTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			UnityEngine.Texture ret = obj.OverrideTexture;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index OverrideTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_mainTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			UnityEngine.Texture ret = obj.mainTexture;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index mainTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Skeleton(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			Spine.Skeleton ret = obj.Skeleton;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Skeleton on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_SkeletonData(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			Spine.SkeletonData ret = obj.SkeletonData;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index SkeletonData on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsValid(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			bool ret = obj.IsValid;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsValid on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AnimationState(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			Spine.AnimationState ret = obj.AnimationState;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AnimationState on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PhysicsPositionInheritanceFactor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			UnityEngine.Vector2 ret = obj.PhysicsPositionInheritanceFactor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index PhysicsPositionInheritanceFactor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PhysicsRotationInheritanceFactor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			float ret = obj.PhysicsRotationInheritanceFactor;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index PhysicsRotationInheritanceFactor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PhysicsMovementRelativeTo(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			UnityEngine.Transform ret = obj.PhysicsMovementRelativeTo;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index PhysicsMovementRelativeTo on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MeshGenerator(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			Spine.Unity.MeshGenerator ret = obj.MeshGenerator;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MeshGenerator on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_SkeletonClipping(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			Spine.SkeletonClipping ret = obj.SkeletonClipping;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index SkeletonClipping on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MeshesMultipleCanvasRenderers(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			Spine.ExposedList<UnityEngine.Mesh> ret = obj.MeshesMultipleCanvasRenderers;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MeshesMultipleCanvasRenderers on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MaterialsMultipleCanvasRenderers(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			Spine.ExposedList<UnityEngine.Material> ret = obj.MaterialsMultipleCanvasRenderers;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MaterialsMultipleCanvasRenderers on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_TexturesMultipleCanvasRenderers(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			Spine.ExposedList<UnityEngine.Texture> ret = obj.TexturesMultipleCanvasRenderers;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index TexturesMultipleCanvasRenderers on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UpdateTiming(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			Spine.Unity.UpdateTiming ret = obj.UpdateTiming;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index UpdateTiming on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UnscaledTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			bool ret = obj.UnscaledTime;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index UnscaledTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AssignMeshOverrideSingleRenderer(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateSingle)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AssignMeshOverrideMultipleRenderers(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateMultiple)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OnRebuild(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Spine.Unity.SkeletonGraphic.SkeletonRendererDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OnInstructionsPrepared(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Spine.Unity.SkeletonGraphic.InstructionDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OnMeshAndMaterialsUpdated(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Spine.Unity.SkeletonGraphic.SkeletonRendererDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OnAnimationRebuild(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Spine.Unity.ISkeletonAnimationDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_BeforeApply(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Spine.Unity.UpdateBonesDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UpdateLocal(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Spine.Unity.UpdateBonesDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UpdateWorld(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Spine.Unity.UpdateBonesDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UpdateComplete(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Spine.Unity.UpdateBonesDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OnPostProcessVertices(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Spine.Unity.MeshGeneratorDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_skeletonDataAsset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			Spine.Unity.SkeletonDataAsset arg0 = (Spine.Unity.SkeletonDataAsset)ToLua.CheckObject<Spine.Unity.SkeletonDataAsset>(L, 2);
			obj.skeletonDataAsset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index skeletonDataAsset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_additiveMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			UnityEngine.Material arg0 = (UnityEngine.Material)ToLua.CheckObject<UnityEngine.Material>(L, 2);
			obj.additiveMaterial = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index additiveMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_multiplyMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			UnityEngine.Material arg0 = (UnityEngine.Material)ToLua.CheckObject<UnityEngine.Material>(L, 2);
			obj.multiplyMaterial = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index multiplyMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_screenMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			UnityEngine.Material arg0 = (UnityEngine.Material)ToLua.CheckObject<UnityEngine.Material>(L, 2);
			obj.screenMaterial = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index screenMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_initialSkinName(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			string arg0 = ToLua.CheckString(L, 2);
			obj.initialSkinName = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index initialSkinName on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_initialFlipX(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.initialFlipX = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index initialFlipX on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_initialFlipY(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.initialFlipY = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index initialFlipY on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_startingAnimation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			string arg0 = ToLua.CheckString(L, 2);
			obj.startingAnimation = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index startingAnimation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_startingLoop(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.startingLoop = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index startingLoop on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_timeScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.timeScale = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index timeScale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_freeze(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.freeze = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index freeze on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_layoutScaleMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			Spine.Unity.SkeletonGraphic.LayoutMode arg0 = (Spine.Unity.SkeletonGraphic.LayoutMode)ToLua.CheckObject(L, 2, typeof(Spine.Unity.SkeletonGraphic.LayoutMode));
			obj.layoutScaleMode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index layoutScaleMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_updateWhenInvisible(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			Spine.Unity.UpdateMode arg0 = (Spine.Unity.UpdateMode)ToLua.CheckObject(L, 2, typeof(Spine.Unity.UpdateMode));
			obj.updateWhenInvisible = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index updateWhenInvisible on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_allowMultipleCanvasRenderers(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.allowMultipleCanvasRenderers = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index allowMultipleCanvasRenderers on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_canvasRenderers(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			System.Collections.Generic.List<UnityEngine.CanvasRenderer> arg0 = (System.Collections.Generic.List<UnityEngine.CanvasRenderer>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<UnityEngine.CanvasRenderer>));
			obj.canvasRenderers = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index canvasRenderers on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enableSeparatorSlots(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.enableSeparatorSlots = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableSeparatorSlots on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_updateSeparatorPartLocation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.updateSeparatorPartLocation = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index updateSeparatorPartLocation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_updateSeparatorPartScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.updateSeparatorPartScale = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index updateSeparatorPartScale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_disableMeshAssignmentOnOverride(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.disableMeshAssignmentOnOverride = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index disableMeshAssignmentOnOverride on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_color(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.color = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index color on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_UpdateMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			Spine.Unity.UpdateMode arg0 = (Spine.Unity.UpdateMode)ToLua.CheckObject(L, 2, typeof(Spine.Unity.UpdateMode));
			obj.UpdateMode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index UpdateMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_OverrideTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			UnityEngine.Texture arg0 = (UnityEngine.Texture)ToLua.CheckObject<UnityEngine.Texture>(L, 2);
			obj.OverrideTexture = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index OverrideTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Skeleton(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			Spine.Skeleton arg0 = (Spine.Skeleton)ToLua.CheckObject<Spine.Skeleton>(L, 2);
			obj.Skeleton = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Skeleton on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_PhysicsPositionInheritanceFactor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.PhysicsPositionInheritanceFactor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index PhysicsPositionInheritanceFactor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_PhysicsRotationInheritanceFactor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.PhysicsRotationInheritanceFactor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index PhysicsRotationInheritanceFactor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_PhysicsMovementRelativeTo(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
			obj.PhysicsMovementRelativeTo = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index PhysicsMovementRelativeTo on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_UpdateTiming(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			Spine.Unity.UpdateTiming arg0 = (Spine.Unity.UpdateTiming)ToLua.CheckObject(L, 2, typeof(Spine.Unity.UpdateTiming));
			obj.UpdateTiming = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index UpdateTiming on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_UnscaledTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.UnscaledTime = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index UnscaledTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AssignMeshOverrideSingleRenderer(IntPtr L)
	{
		try
		{
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject(L, 1, typeof(Spine.Unity.SkeletonGraphic));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Spine.Unity.SkeletonGraphic.AssignMeshOverrideSingleRenderer' can only appear on the left hand side of += or -= when used outside of the type 'Spine.Unity.SkeletonGraphic'");
			}

			if (arg0.op == EventOp.Add)
			{
				Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateSingle ev = (Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateSingle)arg0.func;
				obj.AssignMeshOverrideSingleRenderer += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateSingle ev = (Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateSingle)arg0.func;
				obj.AssignMeshOverrideSingleRenderer -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AssignMeshOverrideMultipleRenderers(IntPtr L)
	{
		try
		{
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject(L, 1, typeof(Spine.Unity.SkeletonGraphic));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Spine.Unity.SkeletonGraphic.AssignMeshOverrideMultipleRenderers' can only appear on the left hand side of += or -= when used outside of the type 'Spine.Unity.SkeletonGraphic'");
			}

			if (arg0.op == EventOp.Add)
			{
				Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateMultiple ev = (Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateMultiple)arg0.func;
				obj.AssignMeshOverrideMultipleRenderers += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateMultiple ev = (Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateMultiple)arg0.func;
				obj.AssignMeshOverrideMultipleRenderers -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_OnRebuild(IntPtr L)
	{
		try
		{
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject(L, 1, typeof(Spine.Unity.SkeletonGraphic));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Spine.Unity.SkeletonGraphic.OnRebuild' can only appear on the left hand side of += or -= when used outside of the type 'Spine.Unity.SkeletonGraphic'");
			}

			if (arg0.op == EventOp.Add)
			{
				Spine.Unity.SkeletonGraphic.SkeletonRendererDelegate ev = (Spine.Unity.SkeletonGraphic.SkeletonRendererDelegate)arg0.func;
				obj.OnRebuild += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Spine.Unity.SkeletonGraphic.SkeletonRendererDelegate ev = (Spine.Unity.SkeletonGraphic.SkeletonRendererDelegate)arg0.func;
				obj.OnRebuild -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_OnInstructionsPrepared(IntPtr L)
	{
		try
		{
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject(L, 1, typeof(Spine.Unity.SkeletonGraphic));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Spine.Unity.SkeletonGraphic.OnInstructionsPrepared' can only appear on the left hand side of += or -= when used outside of the type 'Spine.Unity.SkeletonGraphic'");
			}

			if (arg0.op == EventOp.Add)
			{
				Spine.Unity.SkeletonGraphic.InstructionDelegate ev = (Spine.Unity.SkeletonGraphic.InstructionDelegate)arg0.func;
				obj.OnInstructionsPrepared += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Spine.Unity.SkeletonGraphic.InstructionDelegate ev = (Spine.Unity.SkeletonGraphic.InstructionDelegate)arg0.func;
				obj.OnInstructionsPrepared -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_OnMeshAndMaterialsUpdated(IntPtr L)
	{
		try
		{
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject(L, 1, typeof(Spine.Unity.SkeletonGraphic));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Spine.Unity.SkeletonGraphic.OnMeshAndMaterialsUpdated' can only appear on the left hand side of += or -= when used outside of the type 'Spine.Unity.SkeletonGraphic'");
			}

			if (arg0.op == EventOp.Add)
			{
				Spine.Unity.SkeletonGraphic.SkeletonRendererDelegate ev = (Spine.Unity.SkeletonGraphic.SkeletonRendererDelegate)arg0.func;
				obj.OnMeshAndMaterialsUpdated += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Spine.Unity.SkeletonGraphic.SkeletonRendererDelegate ev = (Spine.Unity.SkeletonGraphic.SkeletonRendererDelegate)arg0.func;
				obj.OnMeshAndMaterialsUpdated -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_OnAnimationRebuild(IntPtr L)
	{
		try
		{
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject(L, 1, typeof(Spine.Unity.SkeletonGraphic));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Spine.Unity.SkeletonGraphic.OnAnimationRebuild' can only appear on the left hand side of += or -= when used outside of the type 'Spine.Unity.SkeletonGraphic'");
			}

			if (arg0.op == EventOp.Add)
			{
				Spine.Unity.ISkeletonAnimationDelegate ev = (Spine.Unity.ISkeletonAnimationDelegate)arg0.func;
				obj.OnAnimationRebuild += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Spine.Unity.ISkeletonAnimationDelegate ev = (Spine.Unity.ISkeletonAnimationDelegate)arg0.func;
				obj.OnAnimationRebuild -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_BeforeApply(IntPtr L)
	{
		try
		{
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject(L, 1, typeof(Spine.Unity.SkeletonGraphic));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Spine.Unity.SkeletonGraphic.BeforeApply' can only appear on the left hand side of += or -= when used outside of the type 'Spine.Unity.SkeletonGraphic'");
			}

			if (arg0.op == EventOp.Add)
			{
				Spine.Unity.UpdateBonesDelegate ev = (Spine.Unity.UpdateBonesDelegate)arg0.func;
				obj.BeforeApply += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Spine.Unity.UpdateBonesDelegate ev = (Spine.Unity.UpdateBonesDelegate)arg0.func;
				obj.BeforeApply -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_UpdateLocal(IntPtr L)
	{
		try
		{
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject(L, 1, typeof(Spine.Unity.SkeletonGraphic));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Spine.Unity.SkeletonGraphic.UpdateLocal' can only appear on the left hand side of += or -= when used outside of the type 'Spine.Unity.SkeletonGraphic'");
			}

			if (arg0.op == EventOp.Add)
			{
				Spine.Unity.UpdateBonesDelegate ev = (Spine.Unity.UpdateBonesDelegate)arg0.func;
				obj.UpdateLocal += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Spine.Unity.UpdateBonesDelegate ev = (Spine.Unity.UpdateBonesDelegate)arg0.func;
				obj.UpdateLocal -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_UpdateWorld(IntPtr L)
	{
		try
		{
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject(L, 1, typeof(Spine.Unity.SkeletonGraphic));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Spine.Unity.SkeletonGraphic.UpdateWorld' can only appear on the left hand side of += or -= when used outside of the type 'Spine.Unity.SkeletonGraphic'");
			}

			if (arg0.op == EventOp.Add)
			{
				Spine.Unity.UpdateBonesDelegate ev = (Spine.Unity.UpdateBonesDelegate)arg0.func;
				obj.UpdateWorld += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Spine.Unity.UpdateBonesDelegate ev = (Spine.Unity.UpdateBonesDelegate)arg0.func;
				obj.UpdateWorld -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_UpdateComplete(IntPtr L)
	{
		try
		{
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject(L, 1, typeof(Spine.Unity.SkeletonGraphic));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Spine.Unity.SkeletonGraphic.UpdateComplete' can only appear on the left hand side of += or -= when used outside of the type 'Spine.Unity.SkeletonGraphic'");
			}

			if (arg0.op == EventOp.Add)
			{
				Spine.Unity.UpdateBonesDelegate ev = (Spine.Unity.UpdateBonesDelegate)arg0.func;
				obj.UpdateComplete += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Spine.Unity.UpdateBonesDelegate ev = (Spine.Unity.UpdateBonesDelegate)arg0.func;
				obj.UpdateComplete -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_OnPostProcessVertices(IntPtr L)
	{
		try
		{
			Spine.Unity.SkeletonGraphic obj = (Spine.Unity.SkeletonGraphic)ToLua.CheckObject(L, 1, typeof(Spine.Unity.SkeletonGraphic));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Spine.Unity.SkeletonGraphic.OnPostProcessVertices' can only appear on the left hand side of += or -= when used outside of the type 'Spine.Unity.SkeletonGraphic'");
			}

			if (arg0.op == EventOp.Add)
			{
				Spine.Unity.MeshGeneratorDelegate ev = (Spine.Unity.MeshGeneratorDelegate)arg0.func;
				obj.OnPostProcessVertices += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Spine.Unity.MeshGeneratorDelegate ev = (Spine.Unity.MeshGeneratorDelegate)arg0.func;
				obj.OnPostProcessVertices -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Spine_Unity_SkeletonGraphic_SkeletonRendererDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<Spine.Unity.SkeletonGraphic.SkeletonRendererDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<Spine.Unity.SkeletonGraphic.SkeletonRendererDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Spine_Unity_SkeletonGraphic_InstructionDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<Spine.Unity.SkeletonGraphic.InstructionDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<Spine.Unity.SkeletonGraphic.InstructionDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateMultiple(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateMultiple>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateMultiple>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Spine_Unity_SkeletonGraphic_MeshAssignmentDelegateSingle(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateSingle>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<Spine.Unity.SkeletonGraphic.MeshAssignmentDelegateSingle>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

