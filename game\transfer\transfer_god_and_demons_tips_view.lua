TransFerGodAndDemonsTipsView = TransFerGodAndDemonsTipsView or BaseClass(SafeBaseView)
function TransFerGodAndDemonsTipsView:__init()
    self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(598, 420)})
    self:AddViewResource(0, "uis/view/tansfer_ui_prefab", "layout_select_god_or_demons")
end

function TransFerGodAndDemonsTipsView:LoadCallBack()
	

    XUI.AddClickEventListener(self.node_list["btn_cancel"], BindTool.Bind(self.OnClickBtnCancel, self))
	XUI.AddClickEventListener(self.node_list["btn_OK"], BindTool.Bind(self.OnClickBtnOK, self))

    if not self.reward_item_list then
		self.reward_item_list = AsyncListView.New(ItemCell, self.node_list.scroll)
        self.reward_item_list:SetStartZeroIndex(true)
	end


end

function TransFerGodAndDemonsTipsView:ReleaseCallBack()

    if self.reward_item_list then
		self.reward_item_list:DeleteMe()
		self.reward_item_list = nil
	end
end

function TransFerGodAndDemonsTipsView:OnFlush()
    if self.type == ZHUANZHI_GOD_AND_DEMONS_TYPE.DEMONS then
        self.reward_item_list:SetDataList(TransFerWGData.Instance:GetOtherCfgByKey("demons_reward"))
    else
        self.reward_item_list:SetDataList(TransFerWGData.Instance:GetOtherCfgByKey("god_reward"))
    end

    local title_str = ""
    local title_desc = ""
    if self.type == ZHUANZHI_GOD_AND_DEMONS_TYPE.DEMONS then
        title_str = TransFerWGData.Instance:GetOtherCfgByKey("demons_desc2")
        title_desc = TransFerWGData.Instance:GetOtherCfgByKey("demons_txt")
    else
        title_str = TransFerWGData.Instance:GetOtherCfgByKey("god_desc2")
        title_desc = TransFerWGData.Instance:GetOtherCfgByKey("god_txt")
    end

    self.node_list["title_view_name"].text.text = title_str

    self.node_list.text_tips.text.text = title_str
    self.node_list.text_desc.text.text = string.format(Language.TransFer.SelectStr, title_desc)
end



function TransFerGodAndDemonsTipsView:SetData(type)
    self.type = type
end

function TransFerGodAndDemonsTipsView:OnClickBtnCancel()
    self:Close()

    
end


function TransFerGodAndDemonsTipsView:OnClickBtnOK()
    TransFerWGCtrl.Instance:SendTransferOperate(ROLE_ZHUANZHI_OPER_TYPE.ROLE_ZHUANZHI_OPER_TYPE_CHOOSE_TYPE, self.type)
    self:Close()
end

function TransFerGodAndDemonsTipsView:CloseCallBack()
    -- ViewManager.Instance:Close(GuideModuleName.GodAndDemons)
end
