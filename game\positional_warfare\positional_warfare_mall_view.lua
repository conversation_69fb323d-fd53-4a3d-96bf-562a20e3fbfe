-- 商城界面
PositionalWarfareMallView = PositionalWarfareMallView or BaseClass(SafeBaseView)

function PositionalWarfareMallView:__init()
	self.view_style = ViewStyle.Half
	self.is_safe_area_adapter = true
	self:SetMaskBg()
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
    self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_positional_warfare_mall_view")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function PositionalWarfareMallView:LoadCallBack()
    if not self.mall_list then
		self.mall_list = AsyncBaseGrid.New()
		local bundle = "uis/view/positional_warfare_ui_prefab"
		local asset = "pw_mall_list_cell"
		self.mall_list:CreateCells({col = 3, change_cells_num = 1, list_view = self.node_list.mall_list,
			assetBundle = bundle, assetName = asset, itemRender = PWMAllItemCellRender})
		self.mall_list:SetStartZeroIndex(false)
	end

	self.wg_data = PositionalWarfareWGData.Instance

	XUI.AddClickEventListener(self.node_list.item_icon, BindTool.Bind(self.OnClickItemIcon, self))

	local bundle, asset = ResPath.GetRawImagesPNG("a3_yyhd_bj2")
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)

	self.node_list.desc_tip.text.text = Language.PositionalWarfare.MallDescTip
	self.node_list.title_view_name.text.text = Language.PositionalWarfare.MallTitle
	self.node_list.shop_desc.text.text = Language.PositionalWarfare.MallDesc
end

function PositionalWarfareMallView:ReleaseCallBack()
    if self.mall_list then
        self.mall_list:DeleteMe()
        self.mall_list = nil
    end

	self.wg_data = nil
end

function PositionalWarfareMallView:OnFlush()
    local data_list = self.wg_data:GetShowShopDataList()
	self.mall_list:SetDataList(data_list)
	self.node_list.item_num.text.text = self.wg_data:GetShopExchangeScore()
end

function PositionalWarfareMallView:OnClickItemIcon()
	local item_id = PositionalWarfareWGData.Instance:GetOtherAttrValue("mall_item")
	TipWGCtrl.Instance:OpenItem({ item_id = item_id })
end

----------------------------------PWMAllItemCellRender-------------------------------------
PWMAllItemCellRender = PWMAllItemCellRender or BaseClass(BaseRender)

function PWMAllItemCellRender:LoadCallBack()
    if not self.item then
		self.item = ItemCell.New(self.node_list.cell_pos)
	end

	XUI.AddClickEventListener(self.node_list.buy_btn, BindTool.Bind(self.OnClickConvertBtn, self))
	self.sell_out = false
	self.score_enough = false
end

function PWMAllItemCellRender:__delete()
    if self.item then
		self.item:DeleteMe()
		self.item = nil
	end

	self.sell_out = nil
	self.score_enough = nil
end

function PWMAllItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
		return
	end

	local cfg = self.data.cfg
	self.item:SetData(cfg.item)
	local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.item.item_id)

	if item_cfg then
		self.node_list.cell_name.text.text = item_cfg.name -- ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
	end

	-- 折扣
	local has_discount = nil ~= cfg.discount
	if has_discount then
		self.node_list.desc_discount.text.text = string.format(Language.PositionalWarfare.Discount, NumberToChinaNumber(cfg.discount))
	end
	self.node_list.flag_limit_buy:CustomSetActive(has_discount)

	local has_exchange_limit = self.data.has_limit_type
	local shop_score = PositionalWarfareWGData.Instance:GetShopExchangeScore()
	local exchange_time = PositionalWarfareWGData.Instance:GetShopItemExchangeTimeBySeq(cfg.seq)
	local score_enough = shop_score >= cfg.need_shop_score
	local exchange_time_enough = not has_exchange_limit or (has_exchange_limit and exchange_time < cfg.times_limit)
	local sell_out = has_exchange_limit and exchange_time >= cfg.times_limit

	self.node_list.desc_limit_buy:CustomSetActive(has_exchange_limit)

	if has_exchange_limit then
		local limit_str = Language.PositionalWarfare.ShopLimitExchangeDesc[cfg.limit_type]
		local color = exchange_time_enough and COLOR3B.L_GREEN or COLOR3B.RED
		limit_str = string.format(limit_str, ToColorStr(exchange_time, color).. "/" ..cfg.times_limit)
		self.node_list.desc_limit_buy.text.text = limit_str
	end

	self.sell_out = sell_out
	self.score_enough = score_enough

	self.node_list.sell_info:CustomSetActive(not sell_out)
	self.node_list.flag_sell_out:CustomSetActive(sell_out)
	self.node_list.convert_btn_text.text.text = cfg.need_shop_score
	self.node_list.last_price.text.text = cfg.original_price
	-- XUI.SetButtonEnabled(self.node_list.convert_btn, exchange_time_enough and score_enough)

	self.node_list.remind:CustomSetActive(not self.sell_out and self.score_enough)
end

function PWMAllItemCellRender:OnClickConvertBtn()
	if IsEmptyTable(self.data) then
		return
	end

	if self.sell_out then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.PositionalWarfare.MallSellOut)
		return
	end

	if not self.score_enough then
		local item_id = PositionalWarfareWGData.Instance:GetOtherAttrValue("mall_item")
		TipWGCtrl.Instance:OpenItem({ item_id = item_id })

		local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
		local str = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.NoEnoughItem, str))
		return
	end

	PositionalWarfareWGCtrl.Instance:SendCrossLandWarOperate(CROSS_LAND_WAR_OPERATE_TYPE.SHOP_BUY, self.data.cfg.seq, 1)
end