require("game/xiuxianshilian/xiuxianshilian_wg_data")
require("game/xiuxianshilian/xiuxianshilian_view")

XiuXianShiLianWGCtrl = XiuXianShiLianWGCtrl or BaseClass(BaseWGCtrl)

function XiuXianShiLianWGCtrl:__init()
	if XiuXianShiLianWGCtrl.Instance then
		ErrorLog("[XiuXianShiLianWGCtrl] attempt to create singleton twice!")
		return
	end
	XiuXianShiLianWGCtrl.Instance = self

	self.view = XiuXianShiLianView.New(GuideModuleName.XiuXianShiLian)
    self.data = XiuXianShiLianWGData.New()
	self:RegisterAllProtocols()
    self.open_fun_change = GlobalEventSystem:Bind(OpenFunEventType.OPEN_TRIGGER, BindTool.Bind(self.OpenFunEventChange, self))

    self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
end

function XiuXianShiLianWGCtrl:__delete()
	XiuXianShiLianWGCtrl.Instance = nil

    if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end

    GlobalEventSystem:UnBind(self.open_fun_change)

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
    end
end

function XiuXianShiLianWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCXiuXianZhiLvInfo, "OnSCXiuXianZhiLvInfo")
    self:RegisterProtocol(CSXiuXianZhiLvOper)
end

function XiuXianShiLianWGCtrl:OnSCXiuXianZhiLvInfo(protocol)
    local old_status = self.data:GetOldChapterFetchedList()
    if not IsEmptyTable(old_status) then
         for k, v in pairs(old_status) do
            if v == 0 and protocol.chapter_fetch_flag[k] == 1 then
                --激活技能
                local other_cfg = self.data:GetChapterListInfo()[k]
                local skill_data = SkillWGData.Instance:GetPassiveSkillByIndex((other_cfg and other_cfg.skill_index or 0))
                if skill_data then
                    local data = {name = skill_data.name, desc = skill_data.desc, res_fun = ResPath.GetSkillIconById, icon = skill_data.icon}
                    TipWGCtrl.Instance:ShowGetNewSkillView2(data)
                end
            end
         end
    end

    self.data:SetXiuxianData(protocol)
    MainuiWGCtrl.Instance:FlushView(0, "mainui_fxs_tip")
    RemindManager.Instance:Fire(RemindName.XiuXianShiLian)
    if self.view:IsOpen() then
        self.view:Flush(0, "protocol_update")
    end

    if FunOpen.Instance:GetFunIsOpened(FunName.XiuXianShiLian) and self.data:GetAllCapterIsComplete() then
        FunOpen.Instance:ForceCloseFunByName(FunName.XiuXianShiLian, true)
    end
end

function XiuXianShiLianWGCtrl:SendXiuXianOper(opear_type, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSXiuXianZhiLvOper)
    protocol.opear_type = opear_type
    protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

function XiuXianShiLianWGCtrl:SetScrollInteract()
    self.view:SetScrollInteract()
end

function XiuXianShiLianWGCtrl:OpenFunEventChange(check_all, fun_name)
    if check_all or fun_name == FunName.XiuXianShiLian then
        local fun_is_open = FunOpen.Instance:GetFunIsOpened(FunName.XiuXianShiLian)
        local max_chapter_id = self.data:GetMaxUnLockChapterId()
        if fun_is_open and max_chapter_id <= 0 then
            FunOpen.Instance:ForceCloseFunByName(FunName.XiuXianShiLian)
        elseif fun_is_open and max_chapter_id > 0 and self.data:GetAllCapterIsComplete() then
            FunOpen.Instance:ForceCloseFunByName(FunName.XiuXianShiLian)
        end
    end
end

function XiuXianShiLianWGCtrl:OnRoleAttrChange(attr_name)
	if attr_name == "level" then
        local max_chapter_id = self.data:GetMaxUnLockChapterId()
        if max_chapter_id <= 0 then
            return
        end

        local fun_cfg = FunOpen.Instance:GetFunByName(FunName.XiuXianShiLian)
        local fun_is_open = FunOpen.Instance:GetFunIsOpened(FunName.XiuXianShiLian)
        local is_complete = TaskWGData.Instance:GetTaskIsCompleted((fun_cfg and fun_cfg.trigger_param or -1))
        if not fun_is_open and is_complete and not self.data:GetAllCapterIsComplete() then
            FunOpen.Instance:ForceOpenFunByName(FunName.XiuXianShiLian)
        end
	end
end