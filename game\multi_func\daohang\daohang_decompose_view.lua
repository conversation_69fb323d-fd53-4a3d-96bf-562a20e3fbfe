DaoHangDeComposeView = DaoHangDeComposeView or BaseClass(SafeBaseView)

-- 同步 Language.Charm.DaoHangMetingNameList
local index_map_color_list = {1,2,3,4,5,6,7,8,}
local default_select_color = 2
local meting_warn_color = 4

function DaoHangDeComposeView:__init()
    self:SetMaskBg(false)
	local daohang_bundle_name = "uis/view/multi_function_ui/daohang_prefab"
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(780, 514)})
	self:AddViewResource(0, daohang_bundle_name, "layout_daohang_melting")
end

function DaoHangDeComposeView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Bag.MeltingName

	if not self.item_grid then
        self.item_grid = DaoHangMeltingGrid.New()
        self.item_grid:SetStartZeroIndex(false)
        self.item_grid:SetIsMultiSelect(true)
        self.item_grid:CreateCells({
            col = 8,
            cell_count = 40,
            list_view = self.node_list["item_grid"],
            itemRender = DaoHangMeltEquipCell,
            change_cells_num = 2,
        })

        self.item_grid:SetSelectCallBack(BindTool.Bind(self.OnBagSelectItemCB, self))
    end

	self.color_list_view = AsyncListView.New(DaoHangMeltingPinZhiListRender, self.node_list["color_list"])
	self.color_list_view:SetSelectCallBack(BindTool.Bind(self.SelectColorCallBack, self))
	self.cur_select_color_index = RoleWGData.GetRolePlayerPrefsInt("daohang_melting_select_pinzhi_color")

	if self.cur_select_color_index < 1 then
		self.cur_select_color_index = default_select_color
		RoleWGData.SetRolePlayerPrefsInt("daohang_melting_select_pinzhi_color", self.cur_select_color_index)
	end

	self.color_list_view:SetDefaultSelectIndex(self.cur_select_color_index)
	self.color_list_view:SetDataList(Language.Charm.DaoHangMetingNameList)
	self.node_list["cur_color_text"].text.text = ToColorStr(Language.Charm.DaoHangMetingNameList2[self.cur_select_color_index], 
		ITEM_COLOR_DARK[self.cur_select_color_index])

	self.is_show_color_part = true
	self:OnClickSelectColor()
	XUI.AddClickEventListener(self.node_list["btn_select_color"], BindTool.Bind(self.OnClickSelectColor, self))
	XUI.AddClickEventListener(self.node_list["close_color_list_part"], BindTool.Bind(self.OnClickSelectColor, self))
	XUI.AddClickEventListener(self.node_list["btn_melting"], BindTool.Bind(self.OnClickMelting, self))
end

function DaoHangDeComposeView:CloseCallBack()
	RoleWGData.SetRolePlayerPrefsInt("daohang_melting_select_pinzhi_color", self.cur_select_color_index)
end

function DaoHangDeComposeView:ReleaseCallBack()
	if self.item_grid then
        self.item_grid:DeleteMe()
        self.item_grid = nil
    end

	if self.color_list_view then
		self.color_list_view:DeleteMe()
		self.color_list_view = nil
	end

	self.cur_select_color_index = nil
end

function DaoHangDeComposeView:OnBagSelectItemCB(cell)
    self:CalcSelect()
end

function DaoHangDeComposeView:OnFlush()
    local item_list = MultiFunctionWGData.Instance:GetDaoHangBagSortDataList()
    self.item_grid:SetDataList(item_list)
	self:OneKeySelect()
end

function DaoHangDeComposeView:OneKeySelect()
	local color = index_map_color_list[self.cur_select_color_index] or 0
	self.item_grid:SetMeltingOneKeyColorSelcet(color)
	self:CalcSelect()
end

function DaoHangDeComposeView:SelectColorCallBack(cell)
	local data = cell and cell:GetData()
	if data == nil then
		return
	end

	local index = cell:GetIndex()
	if index == self.cur_select_color_index then
		return
	end

	self.cur_select_color_index = index
	self.node_list["cur_color_text"].text.text = ToColorStr(Language.Charm.DaoHangMetingNameList2[self.cur_select_color_index], 
		ITEM_COLOR_DARK[self.cur_select_color_index])
	self:OnClickSelectColor()
	self:OneKeySelect()
end

function DaoHangDeComposeView:OnClickSelectColor()
	self.is_show_color_part = not self.is_show_color_part
	self.node_list["color_list_part"]:SetActive(self.is_show_color_part)
	self.node_list["color_arrow_down"]:SetActive(self.is_show_color_part)
	self.node_list["color_arrow_up"]:SetActive(not self.is_show_color_part)
end

function DaoHangDeComposeView:OnClickMelting()
	local select_list = self.item_grid:GetAllSelectCell()

	if IsEmptyTable(select_list) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Charm.MeltingError)
		return
	end

	for k,v in pairs(select_list) do
		local color = v.color or 0

		if color > meting_warn_color then
			TipWGCtrl.Instance:OpenAlertTips(Language.Charm.MeltingEquipAlertTip, function ()
				self:OnSendMelting()
			end)

			return
		end
	end

	self:OnSendMelting()
end

function DaoHangDeComposeView:OnSendMelting()
	local select_list = self.item_grid:GetAllSelectCell()

	if IsEmptyTable(select_list) then
		return
	end

	local destroy_item_list = {}
	for k,v in pairs(select_list) do
		table.insert(destroy_item_list, {item_id = v.item_id, bag_index = v.index, count = v.num})
	end

	MultiFunctionWGCtrl.Instance:OnCSTaoistDecompsEquip(#destroy_item_list, destroy_item_list)
end

function DaoHangDeComposeView:CalcSelect()
	local select_list = self.item_grid:GetAllSelectCell()

	if IsEmptyTable(select_list) then
		for i = 1, 2 do
			self.node_list["daohang_meting_cost" .. i]:CustomSetActive(false)
		end

		return
	end

	local item_id_list = {}
	local decomp_item_list = {}

	for k,v in pairs(select_list) do
		local equip_cfg = MultiFunctionWGData.Instance:GetDaoHangEquipByItemId(v.item_id)

		for i = 1, 2 do
			local decomp_data = equip_cfg["item" .. i]

			if item_id_list[decomp_data.item_id] then
				local index = item_id_list[decomp_data.item_id]
				decomp_item_list[index].num = decomp_item_list[index].num + decomp_data.num * v.num
			else
				local cell_data = {}
				cell_data.item_id = decomp_data.item_id
				cell_data.num = decomp_data.num * v.num
				table.insert(decomp_item_list, cell_data)
				item_id_list[decomp_data.item_id] = #decomp_item_list
			end
		end
	end

	for i = 1, 2 do
		local active = not IsEmptyTable(decomp_item_list[i])

		if active then
			local item_cfg = ItemWGData.Instance:GetItemConfig(decomp_item_list[i].item_id)

			if item_cfg then
				local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
				
				self.node_list["daohang_meting_icon" .. i].image:LoadSprite(bundle, asset, function ()
					self.node_list["daohang_meting_icon" .. i].image:SetNativeSize()
				end)
			end

			self.node_list["daohang_meting_desc" .. i].text.text = decomp_item_list[i].num
		end
		
		self.node_list["daohang_meting_cost" .. i]:CustomSetActive(active)
	end
end

---------------------------------------------DeComposeGrid-----------------------------------------------
DaoHangMeltingGrid = DaoHangMeltingGrid or BaseClass(AsyncBaseGrid)

function DaoHangMeltingGrid:SetMeltingOneKeySelcet(color, order, less_than_star_level)
	less_than_star_level = less_than_star_level or 3
	self.select_tab[1] = {}
	self.cur_multi_select_num = 0

	for i = 1, self.has_data_max_index do
		local data = self.cell_data_list[i]

		if not IsEmptyTable(data) then
			if data.color <= color and data.order <= order then
				local star_level = data.param and data.param.star_level or 0

				if star_level < less_than_star_level then
					self.select_tab[1][i] = true
					self.cur_multi_select_num = self.cur_multi_select_num + 1
				end
			end
		end
	end

	self:__DoRefreshSelectState()
end

function DaoHangMeltingGrid:SetMeltingOneKeyColorSelcet(color)
	self.select_tab[1] = {}
	self.cur_multi_select_num = 0

	for i = 1, self.has_data_max_index do
		local data = self.cell_data_list[i]

		if not IsEmptyTable(data) then
			if data.color <= color then
				self.select_tab[1][i] = true
				self.cur_multi_select_num = self.cur_multi_select_num + 1
			end
		end
	end

	self:__DoRefreshSelectState()
end

------------------------------EquipRender------------------------------------
DaoHangMeltEquipCell = DaoHangMeltEquipCell or BaseClass(ItemCell)
function DaoHangMeltEquipCell:__init()
	self:SetIsShowTips(false)
	self:UseNewSelectEffect(true)
end

function DaoHangMeltEquipCell:SetSelect(is_select)
	self:SetSelectEffect(is_select)
end

--------------------------------CKPinZhiListRender-------------------
DaoHangMeltingPinZhiListRender = DaoHangMeltingPinZhiListRender or BaseClass(BaseRender)
function DaoHangMeltingPinZhiListRender:OnFlush()
	self.node_list.lbl_pinzhi_name.text.text = self.data
	self.node_list.select_pinzhi_bg:SetActive(self.is_select)
	-- self.node_list.line:SetActive(not self.is_select)
end

function DaoHangMeltingPinZhiListRender:OnSelectChange(is_select)
	if self.node_list.select_pinzhi_bg then
		self.node_list.select_pinzhi_bg:SetActive(is_select)
		-- self.node_list.line:SetActive(not is_select)
	end
end