YanYuGePrivilegeLevelUpTip = YanYuGePrivilegeLevelUpTip or BaseClass(SafeBaseView)

function YanYuGePrivilegeLevelUpTip:__init()
    self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Pop
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
    
    self:AddViewResource(0, "uis/view/yanyuge_ui_prefab", "layout_yanyuge_privilege_level_up_tip")
end

function YanYuGePrivilegeLevelUpTip:LoadCallBack()
    self:CloseCountDown()

	self.node_list.auto_close_str.text.text = string.format(Language.Common.AutoCloseTimerTxt, 8)
	CountDownManager.Instance:AddCountDown("YanYuGePrivilegeLevelUpTip", 
		function(elapse_time, total_time)
			self.node_list.auto_close_str.text.text = string.format(Language.Common.AutoCloseTimerTxt, math.ceil(total_time - elapse_time))
		end, 

		function()
			self:Close()
		end, 
    nil, 8, 1)


    self.skeleton = self.node_list["SkeletonGraphic"].gameObject:GetComponent("SkeletonGraphic")
	self.skeleton.AnimationState:SetAnimation(0, "test", true)


    self.node_list.name_bg.canvas_group.alpha = 0
    
	ReDelayCall(self, function ()
        self.skeleton.AnimationState:SetAnimation(0, "test", false)
		self.skeleton.AnimationState:SetAnimation(0, "idle", true)
	end, 1.5, "YanYuGePrivilegeLevelUpTip")

    self.node_list.name_bg.canvas_group:DoAlpha(0, 1, 0.3):SetDelay(1)
end

function YanYuGePrivilegeLevelUpTip:CloseCountDown()
    if CountDownManager.Instance:HasCountDown("YanYuGePrivilegeLevelUpTip") then
        CountDownManager.Instance:RemoveCountDown("YanYuGePrivilegeLevelUpTip")
    end
end

function YanYuGePrivilegeLevelUpTip:ReleaseCallBack()
    self:CloseCountDown()
end

function YanYuGePrivilegeLevelUpTip:OnFlush()
    local cur_privilege_level = YanYuGeWGData.Instance:GetNobilityLevel()
    self.node_list.level_str.text.text = cur_privilege_level
end