-- S-首冲.xls
local item_table={
[1]={item_id=22000,num=2,is_bind=1},
[2]={item_id=22000,num=1,is_bind=1},
[3]={item_id=22000,num=3,is_bind=1},
[4]={item_id=22000,num=4,is_bind=1},
[5]={item_id=22000,num=5,is_bind=1},
[6]={item_id=22000,num=6,is_bind=1},
[7]={item_id=22000,num=7,is_bind=1},
[8]={item_id=5228,num=1,is_bind=1},
[9]={item_id=22883,num=1,is_bind=1},
[10]={item_id=22753,num=1,is_bind=1},
[11]={item_id=22012,num=1,is_bind=1},
[12]={item_id=28503,num=1,is_bind=1},
[13]={item_id=28655,num=1,is_bind=1},
[14]={item_id=28502,num=1,is_bind=1},
[15]={item_id=5428,num=1,is_bind=1},
[16]={item_id=00000,num=1,is_bind=1},
}

return {
first_chongzhi_reward={
{}
},

first_chongzhi_reward_meta_table_map={
},
daily_chongzhi_reward={
{},
{daily_first_chongzhi_type=2,reward_item={[0]=item_table[1],[1]=item_table[1],[2]=item_table[1],[3]=item_table[1],[4]=item_table[1],[5]=item_table[1]},reward_item1=item_table[2],reward_item2=item_table[2],need_total_chongzhi=99,},
{daily_first_chongzhi_type=3,reward_item={[0]=item_table[3],[1]=item_table[3],[2]=item_table[3],[3]=item_table[3],[4]=item_table[3],[5]=item_table[3]},need_total_chongzhi=500,},
{opengame_day=2,},
{opengame_day=2,},
{opengame_day=2,},
{opengame_day=3,},
{opengame_day=3,},
{opengame_day=3,},
{opengame_day=4,},
{opengame_day=4,},
{opengame_day=4,},
{opengame_day=5,},
{opengame_day=5,},
{opengame_day=5,},
{opengame_day=6,},
{opengame_day=6,},
{opengame_day=6,},
{opengame_day=7,},
{opengame_day=7,},
{opengame_day=7,},
{opengame_day=8,},
{opengame_day=8,},
{opengame_day=8,}
},

daily_chongzhi_reward_meta_table_map={
[21]=3,	-- depth:1
[18]=21,	-- depth:2
[15]=18,	-- depth:3
[12]=15,	-- depth:4
[9]=12,	-- depth:5
[6]=9,	-- depth:6
[24]=6,	-- depth:7
[14]=2,	-- depth:1
[11]=14,	-- depth:2
[17]=11,	-- depth:3
[8]=17,	-- depth:4
[20]=8,	-- depth:5
[5]=20,	-- depth:6
[23]=5,	-- depth:7
},
total_chongzhi_reward={
{},
{total_first_chongzhi_type=2,reward_item={[0]=item_table[1]},},
{total_first_chongzhi_type=3,reward_item={[0]=item_table[3]},},
{total_first_chongzhi_type=4,reward_item={[0]=item_table[4]},},
{total_first_chongzhi_type=5,reward_item={[0]=item_table[5]},},
{total_first_chongzhi_type=6,reward_item={[0]=item_table[6]},},
{total_first_chongzhi_type=7,reward_item={[0]=item_table[7]},}
},

total_chongzhi_reward_meta_table_map={
},
other={
{}
},

other_meta_table_map={
},
first_chongzhi_reward_default_table={first_chongzhi_type=1,reward_item_prof_1={[0]=item_table[8],[1]=item_table[9],[2]=item_table[10],[3]=item_table[11],[4]=item_table[12],[5]=item_table[13]},reward_item_prof_2={[0]=item_table[14],[1]=item_table[9],[2]=item_table[10],[3]=item_table[11],[4]=item_table[12],[5]=item_table[13]},reward_item_prof_3={[0]=item_table[15],[1]=item_table[9],[2]=item_table[10],[3]=item_table[11],[4]=item_table[12],[5]=item_table[13]},reward_item_prof_4={[0]=item_table[14],[1]=item_table[9],[2]=item_table[10],[3]=item_table[11],[4]=item_table[12],[5]=item_table[13]},need_total_chongzhi=60,show_model_1_1="1201021:900101201",show_model_0_1="3201021:900101201",show_model_1_3="1201021:900301201",show_model_0_3="3201021:900301201",show_model_1_2="3201021:900201201",show_model_4_1="3201021:900201201",show_model_4_3="3201021:900201201",capability_left=22883,capability_right=22085,show_day2="",show_day3="title:",},

daily_chongzhi_reward_default_table={daily_first_chongzhi_type=1,opengame_day=1,reward_item={[0]=item_table[2],[1]=item_table[2],[2]=item_table[2],[3]=item_table[2],[4]=item_table[2],[5]=item_table[2]},reward_item1=item_table[16],reward_item2=item_table[16],need_total_chongzhi=10,},

total_chongzhi_reward_default_table={total_first_chongzhi_type=1,reward_item={[0]=item_table[2]},},

other_default_table={star_level=2,level_1=19,level_2=41,}

}

