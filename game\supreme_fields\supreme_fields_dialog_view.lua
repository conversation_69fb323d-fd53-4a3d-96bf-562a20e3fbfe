SupremeFieldsDialog = SupremeFieldsDialog or BaseClass(SafeBaseView)

function SupremeFieldsDialog:__init()
	self.view_name = "SupremeFieldsDialog"
	self.view_layer = UiLayer.Pop

	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -6)})
	self:AddViewResource(0, "uis/view/supreme_fields_ui_prefab", "layout_supreme_fields_dialog")
end

function SupremeFieldsDialog:ReleaseCallBack()
	if self.item_data_change_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
	end

	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function SupremeFieldsDialog:CloseCallBack()
	self.need_num = nil
	self.item_id = nil
	self.is_money = nil
end

function SupremeFieldsDialog:LoadCallBack()
	self.need_num = 0
	self.item_id = 0
	self.is_money = false

	self:SetSecondView(nil, self.node_list["bg_root"])
	self.item_data_event = BindTool.Bind1(self.ItemDataChangeCallback, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
	self.node_list["btn_OK"].button:AddClickListener(BindTool.Bind1(self.OnClickOK, self))
	self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind1(self.OnClickCancel, self))
	self.node_list["title_view_name"].text.text = self.title_view_name
	self.node_list.replace_slot:SetActive(false)
	self.node_list.cell_pos:SetActive(false)

	self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
end

function SupremeFieldsDialog:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "item_tip_info" then
			self.need_num = v.need_num
			self.item_id = v.item_id
			self.is_money = v.is_money
			self:FlushItemView()
		elseif k == "tip_info" then
			self:FlushReplaceView(v.left_data, v.right_data)
		end
	end
end

function SupremeFieldsDialog:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if self.item_id == change_item_id and new_num > old_num then
		self:FlushConsume()
	end
end

function SupremeFieldsDialog:FlushConsume()
	local has_num = ItemWGData.Instance:GetItemNumInBagById(self.item_id)
	local num_str = string.format("%d/%d", has_num, self.need_num)
	local color = self.need_num <= has_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
	if self.item_cell == nil then
		self.item_cell = ItemCell.New(self.node_list.cell_pos)
	end

	self.item_cell:SetData({item_id = self.item_id})
	self.item_cell:SetRightBottomColorText(num_str, color)
	self.item_cell:SetRightBottomTextVisible(true)
end

-- 设置确定回调
function SupremeFieldsDialog:SetOkFunc(ok_func)
	self.ok_func = ok_func
end

function SupremeFieldsDialog:SetTextViewName(str)
	if nil ~= str and "" ~= str then
		self.title_view_name = str
		if nil ~= self.node_list["title_view_name"] then
			self.node_list["title_view_name"].text.text = self.title_view_name
		end
	end
end

function SupremeFieldsDialog:FlushItemView()
	local str = ""
	self.node_list.replace_slot:SetActive(false)
	self.node_list.cell_pos:SetActive(not self.is_money)
	if self.is_money then
		str = string.format(Language.SupremeFields.Alertstr2, self.need_num)
	else
		local name = ItemWGData.Instance:GetItemConfig(self.item_id).name
		str = string.format(Language.SupremeFields.Alertstr1, self.need_num, name)
		self:FlushConsume()
	end

	self.node_list.rich_text.text.text = str
end

function SupremeFieldsDialog:FlushReplaceView(left_data, right_data)
	self.node_list.replace_slot:SetActive(true)
	self.node_list.cell_pos:SetActive(false)
	local left_skill_cfg = SupremeFieldsWGData.Instance:GetFootLightSkillCfg(left_data.skill_id)
	local right_skill_cfg = SupremeFieldsWGData.Instance:GetFootLightSkillCfg(right_data.skill_ids[1])
	local left_name = SupremeFieldsWGData.Instance:GetFootLightSkillCfg(left_data.skill_id).skill_name
	local right_name = SupremeFieldsWGData.Instance:GetFootLightSkillCfg(right_data.skill_ids[1]).skill_name
	local str = string.format(Language.SupremeFields.Alertstr3, left_name, right_name)
	self.node_list.rich_text.text.text = str
	local bundle, asset = ResPath.GetSkillIconById(left_skill_cfg.icon)
    	self.node_list.cell_pos_1.image:LoadSpriteAsync(bundle, asset, function ()
    	self.node_list.cell_pos_1.image:SetNativeSize()
    end)
    local bundle, asset = ResPath.GetSkillIconById(right_skill_cfg.icon)
    	self.node_list.cell_pos_2.image:LoadSpriteAsync(bundle, asset, function ()
    	self.node_list.cell_pos_2.image:SetNativeSize()
    end)
end

function SupremeFieldsDialog:ItemDataChangeCallback(item_id, index, reason, put_reason, old_num, new_num)

end

function SupremeFieldsDialog:OnClickOK()
	if self.item_id then
		local has_num = ItemWGData.Instance:GetItemNumInBagById(self.item_id)
		if has_num < self.need_num then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.SupremeFields.Tips3)
			return
		end
	end
	self.ok_func()
	self:Close()
end

function SupremeFieldsDialog:OnClickCancel()
	self:Close()
end
