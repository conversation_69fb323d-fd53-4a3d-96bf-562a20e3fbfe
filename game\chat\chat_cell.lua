local DAY_TIME = 24 * 3600
local TypeTMP = typeof(TMPro.TextMeshProUGUI)
local TypeImage = typeof(UnityEngine.UI.Image)
local TypeUnitySprite = typeof(UnityEngine.Sprite)
local TypeRectTransform = typeof(UnityEngine.RectTransform)
local TypeButton = typeof(UnityEngine.UI.Button)


-- 不同类型消息的最大宽度
local TYPE_WIDTH_MAX = {
	[CHAT_MSG_RESSON.NORMAL] = 340,
	[CHAT_MSG_RESSON.TEAM_TIPS] = 340,
	[CHAT_MSG_RESSON.GUILD_TIPS] = 462,
	[CHAT_MSG_RESSON.HUDONG_PYP] = 445,
	[CHAT_MSG_RESSON.LYL_SAORAO] = 340,
}

ChatCell = ChatCell or BaseClass(BaseRender)
local VoiceBgSize = Vector2(150, 20)

function ChatCell:__init()
	self.old_msg_id = -1
	if self.node_list["LeftIcon"] then
		self.node_list["LeftIcon"].button:AddClickListener(BindTool.Bind(self.OnClickHead,self))

		self.long_touch_flag = false
		self.left_event_listener = self.node_list["LeftIcon"].event_trigger_listener
		if self.left_event_listener then
			self.left_event_listener:AddPointerDownListener(BindTool.Bind(self.OnHeadPointDown, self))
			self.left_event_listener:AddPointerUpListener(BindTool.Bind(self.OnHeadPointUp, self))
		end
	end

	if self.node_list["RightIcon"] then
		self.node_list["RightIcon"].button:AddClickListener(BindTool.Bind(self.OnClickHead,self))
	end

	if self.node_list["lahei_btn"] then
		self.node_list["lahei_btn"].button:AddClickListener(BindTool.Bind(self.OnLYLSaoraoLahei,self))
	end

	if not self.billion_drpt_player_item_list then
		self.billion_drpt_player_item_list = {}
		--左1，右2.
		for i = 1, 2 do
			local node = i == 1 and "drpt_player_left_item_" or "drpt_player_right_item_"
			if node then
				self.billion_drpt_player_item_list[i] = {}
				for j = 1, DRPT_MAX_PLAYER_NUM do
					self.billion_drpt_player_item_list[i][j] = ChatBillionDRPTPlayerItem.New(self.node_list[node .. j])
				end
			else
				--ChatSysCell预制体不存在这个功能与节点.
				self.billion_drpt_player_item_list = nil
				break
			end
		end
	end

	self.state_obj_list = {
		[CHAT_MSG_RESSON.NORMAL] = self.node_list["normal_state"],
		[CHAT_MSG_RESSON.TEAM_TIPS] = self.node_list["team_state"],
		[CHAT_MSG_RESSON.GUILD_TIPS] = self.node_list["guild_state"],
		[CHAT_MSG_RESSON.HUDONG_PYP] = self.node_list["hudong_state"],
	}
	self.left_head_cell = BaseHeadCell.New(self.node_list["RawLeftImg"])
	self.right_head_cell = BaseHeadCell.New(self.node_list["RawRightImg"])

    self.role_avatar = RoleHeadCell.New(false)

	self.is_calc_high_cell = false
end

function ChatCell:__delete()
	if self.node_list.lyl_saorao_state ~= nil then
		self.node_list.lyl_saorao_state:SetActive(false)
	end

	if self.node_list.add_friend_form_lyl_state ~= nil then
		self.node_list.add_friend_form_lyl_state:SetActive(false)
	end

	if self.left_head_cell then
		self.left_head_cell:DeleteMe()
		self.left_head_cell = nil
	end

	if self.right_head_cell then
		self.right_head_cell:DeleteMe()
		self.right_head_cell = nil
	end

    if nil ~= self.role_avatar then
		self.role_avatar:DeleteMe()
		self.role_avatar = nil
    end

	if self.billion_drpt_player_item_list then
		for i = 1, 2 do
			for j = 1, DRPT_MAX_PLAYER_NUM do
				self.billion_drpt_player_item_list[i][j]:DeleteMe()
				self.billion_drpt_player_item_list[i][j] = nil
			end
			self.billion_drpt_player_item_list[i] = nil
		end
		self.billion_drpt_player_item_list = nil
	end

	self.pyp_text_go_list = nil
	self.left_event_listener = nil
	self.long_touch_flag = nil
	self.is_calc_high_cell = false
end

function ChatCell:ReleaseCallBack()
	self:ClearVoiceButton()
	if self.content_obj then
		ResPoolMgr:Release(self.content_obj.gameObject)
		self.content_obj = nil
	end

	self.voice_animator = nil
	self.avatar_key = 0
	if self.data then
		self.data = nil
	end

	self.old_msg_id = -1
	if self.data ~= nil then
		self.data:DeleteMe()
    end
    
    if nil ~= self.role_avatar then
		self.role_avatar:DeleteMe()
		self.role_avatar = nil
	end

	self.data = nil
	self.state_obj_list = nil

	self.bubble_obj = nil
	self.is_calc_high_cell = false

	self:CancelHeadLongTouchTimer()
end

function ChatCell:SetIsCalchighCell(value)
	self.is_calc_high_cell = value
end

function ChatCell:SetCurChannelType(chat_type)
	self.cur_chat_type = chat_type
end

function ChatCell:LoadCallBack()
	if self.node_list["btn_pack_right"] and self.node_list["btn_pack_left"] then
		self.node_list["btn_pack_right"].button:AddClickListener(BindTool.Bind(self.OnClickRedPacket,self))
		self.node_list["btn_pack_left"].button:AddClickListener(BindTool.Bind(self.OnClickRedPacket,self))
	end
	if self.node_list["bg_pack_right"] and self.node_list["bg_pack_left"] then
		self.node_list["bg_pack_right"].button:AddClickListener(BindTool.Bind(self.OnClickRedPacket,self))
		self.node_list["bg_pack_left"].button:AddClickListener(BindTool.Bind(self.OnClickRedPacket,self))
	end

	if self.node_list["billion_dezg_right_btn"] and self.node_list["billion_dezg_left_btn"] then
		self.node_list["billion_dezg_right_btn"].button:AddClickListener(BindTool.Bind(self.OnClickBillionDezgBtn,self))
		self.node_list["billion_dezg_left_btn"].button:AddClickListener(BindTool.Bind(self.OnClickBillionDezgBtn,self))
	end
end

function ChatCell:OnClickRedPacket()
	local chanel = ChatWGCtrl.Instance:GetChannel()
	if chanel == CHANNEL_TYPE.GUILD then
		FunOpen.Instance:OpenViewByName(GuideModuleName.Guild, "guild_redpacket")
	else
		ViewManager.Instance:Open(GuideModuleName.WorldRedPaper)
	end

end

function ChatCell:IsMonsterMsg()
    return self.data and self.data.is_monster and self.data.is_monster == true and self.data.monster_id
end

function ChatCell:OnLYLSaoraoLahei()
	if self.data then
		SocietyWGCtrl.Instance:DeleteFriend(self.data.from_uid)
		ChatWGCtrl.Instance:SendAddBlackReq(self.data.from_uid)
		SocietyWGData.Instance:RemoveLYLInfo(self.data.from_uid)
		SocietyWGCtrl.Instance:ResetSocietyFriendViewActerLYLLahei()
	end
end

function ChatCell:OnClickHead()
    if self:IsMonsterMsg() then
		return
	end

	if self.long_touch_flag then 
		return
	end

	if RoleWGData.Instance:InCrossGetOriginUid() == self.data.from_uid then
		return
	end

	if ChatWGData.Instance:GetIsShieldPYP() then
		self:ShowOtherPlayerInfo()
		return
	end

	if self.delay_pyp_head_tip then
		local pyp_fun_open, limit_level = ChatWGData.Instance:GetPYPIsFunOpened()
		if pyp_fun_open then--拍一拍功能开启判断
			--拍一拍CD判断
			self:TryToReqPYP()
		else
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.PYPFunOpenTips, limit_level))
		end

		self:RemovePYPDelayTimer()
		return
	end

	self.delay_pyp_head_tip = GlobalTimerQuest:AddDelayTimer(function ()
		--原点击逻辑
		self:ShowOtherPlayerInfo()
		self:RemovePYPDelayTimer()
	end, 0.3)
end

--点击他人头像信息
function ChatCell:ShowOtherPlayerInfo()
	--if SocietyWGCtrl.Instance.society_view:IsOpen() then return end--好友私聊不显示头像信息
	if self.data == nil then
		return
	end
	
	local is_show_crosschat = ChatWGData.Instance:GetIsShowCrossChat()
	if self.cur_chat_type and self.cur_chat_type == CHANNEL_TYPE.CROSS and is_show_crosschat then
		return
	end

    local node = self.node_list["LeftIcon"].gameObject.activeSelf and self.node_list["RawLeftImg"] or self.node_list["RawRightImg"]
    
    if self.data.is_from_private and  self.data.is_from_private == 1 then --如果是来自于私聊
        BrowseWGCtrl.Instance:BrowRoelInfo(self.data.from_uid, function(param_protocol)
            if self.data == nil then
                return
            end
            if self.role_avatar then
                self.role_avatar:AddItems(Language.Menu.DeleteFriend)
                self.role_avatar:RemoveItems(Language.Menu.AddFriend)
                self.role_avatar:RemoveItems(Language.Menu.PrivateChat)
                self.role_avatar:SetIsFormPrivate(true)
				local role_info = {
					role_id = param_protocol.role_id,
					role_name = self.data.username,
					prof = param_protocol.prof,
					sex = param_protocol.sex,
					is_online = 0 ~= param_protocol.is_online,
					plat_type = self.data.origin_plat_type,
					server_id = self.data.merge_server_id,
					role_level = param_protocol.level,
				}
                self.role_avatar:SetRoleInfo(role_info)
            end
            self.role_avatar:OpenMenu(nil, self:GetMenuNodePos(node), nil, MASK_BG_ALPHA_TYPE.Normal)
        end)
    else --按之前的处理
    	local is_cross = not self:IsSameServer()
        BrowseWGCtrl.Instance:BrowRoelInfo(self.data.from_uid, function(param_protocol)
            if self.data == nil then
                return
            end
            ChatWGCtrl.Instance:CreateRoleHeadCell(param_protocol.role_id, self.data.username, param_protocol.prof, param_protocol.sex,
            param_protocol.is_online, node, param_protocol.plat_type, param_protocol.server_id, param_protocol.plat_name)
        end, self.data.origin_plat_type,is_cross)
    end
end

function ChatCell:IsSameServer()
	if IsEmptyTable(self.data) then
		return true
	end
	local main_role_server_id = RoleWGData.Instance:GetMergeServerId()
	local main_role_plat_type = RoleWGData.Instance:GetPlatType()
	local server_id = self.data.merge_server_id or self.data.cur_server_id
	local plat_type = self.data.origin_plat_type
	return server_id == main_role_server_id and plat_type == main_role_plat_type
end

function ChatCell:GetMenuNodePos(node)
    if nil == node then return nil end
    local main_view = ViewManager.Instance:GetView(GuideModuleName.MainUIView)
	if nil == main_view or not main_view:IsOpen() then
		return
	end
    local parent_rect= main_view.root_node.transform:GetComponent(typeof(UnityEngine.RectTransform))
	local x, y = parent_rect.sizeDelta.x / 2, parent_rect.sizeDelta.y / 2
    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, node.transform.position)
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, Vector2(0, 0))

    x = local_position_tbl.x
    y = local_position_tbl.y
    x = x + 200
    y = y - 70
    y = math.max(y, -144)
    return Vector2(x, y)
end

--点击他人头像请求拍一拍
function ChatCell:TryToReqPYP()
	--私聊--好友离线判断
	if self:IsPrivateChannel() then
		local friend = SocietyWGData.Instance:FindFriend(self.data.from_uid)
		local is_online = false
		if friend then
			is_online = ONLINE_TYPE.ONLINE_TYPE_ONLINE == friend.is_online or ONLINE_TYPE.ONLINE_TYPE_CROSS == friend.is_online
		end
		if not is_online then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.PYPTargetOffLine)
			return 
		end
	end

	local is_can_pyp, cd_time = ChatWGData.Instance:GetPYPCDByChannelType(self.data.channel_type)
	if not is_can_pyp then--CD冷却中
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.PYPCDTips, cd_time))
		return 
	end

	local pyp_info = {
		target_role_id = self.data.from_uid,  			-- 目标对象ID
		target_role = self.data.username, 				-- 目标对象名字
		target_server_id = self.data.merge_server_id,  	-- 目标对象服务器ID
		target_plat_type = self.data.origin_plat_type, 	-- 目标对象平台ID
		channel_type = self.data.channel_type,  		-- 频道类型
		interaction_type = HUDONG_TYPE.PYP,  			-- 互动类型
	}
	ChatWGCtrl.Instance:SendToPaiYiPai(pyp_info)
	--设置拍一拍CD
	ChatWGData.Instance:SetPYPCDByChannelType(self.data.channel_type)
	-- print_error("==========发送请求拍一拍=========" , pyp_info)
end

-- 头像长按@玩家
function ChatCell:OnHeadPointDown()
	self.long_touch_flag = false
	if self:IsPrivateChannel() then
		return
	end
	-- print_error("FFF===== OnHeadPointDown", self.long_touch_flag)
	self.long_touch_delay_timer = GlobalTimerQuest:AddDelayTimer(function()
		self.long_touch_flag = true
		local aite_desc = self:GetAiTeSomeOneDesc()
		if ChatWGData.Instance:CheckAiTeBlockIsExist(aite_desc) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.AiTeSomeOneSame)
			return
		end

		local aite_data = ChatWGData.Instance:GetSendAiteInfo(self.data, AITE_MSG_TYPE.Send)
		ChatWGData.Instance:SetAiTeBlockList(aite_data, aite_desc)
		ChatWGCtrl.Instance:AddTransmitInputEdit(aite_desc)
		self:CancelHeadLongTouchTimer()
	end, 1)
end

function ChatCell:OnHeadPointUp()
	self:CancelHeadLongTouchTimer()
end

function ChatCell:CancelHeadLongTouchTimer()
	if self.long_touch_delay_timer then
		GlobalTimerQuest:CancelQuest(self.long_touch_delay_timer)
		self.long_touch_delay_timer = nil
	end
end

function ChatCell:GetAiTeSomeOneDesc()
	local aite_desc = ""
	if self:IsCrossServerChannel() then
		aite_desc = string.format(Language.Chat.AiTeSomeOneCrossServer, self.data.username or "", self.data.merge_server_id or 1)
	else
		aite_desc = string.format(Language.Chat.AiTeSomeOne, self.data.username or "")
	end
	return aite_desc
end

function ChatCell:RemovePYPDelayTimer()
	GlobalTimerQuest:CancelQuest(self.delay_pyp_head_tip)
	self.delay_pyp_head_tip = nil
end

function ChatCell:ClearVoiceButton()
	if self.voice_obj_right then
		ResPoolMgr:Release(self.voice_obj_right.gameObject)
		self.voice_obj_right = nil
	end

	if self.voice_obj_left then
		ResPoolMgr:Release(self.voice_obj_left.gameObject)
		self.voice_obj_left = nil
	end

	if self.voice_obj_left_bubble then
		ResPoolMgr:Release(self.voice_obj_left_bubble.gameObject)
		self.voice_obj_left_bubble = nil
	end

	if self.voice_obj_right_bubble then
		ResPoolMgr:Release(self.voice_obj_right_bubble.gameObject)
		self.voice_obj_right_bubble = nil
	end
end

-- 是否自己发送的
function ChatCell:IsOwn()
	if self.data.channel_type == CHANNEL_TYPE.ZUDUI then
		return false
	end

	return self.data.from_cross_uuid == GameVoManager.Instance:GetMainRoleVo().role_id or
			self.data.from_uid == GameVoManager.Instance:GetMainRoleVo().role_id
end

function ChatCell:ChangeCellState(new_msg_reason)
	-- if new_msg_reason == CHAT_MSG_RESSON.LYL_SAORAO then
	-- 	return
	-- end
	if self.state_obj_list[self.old_msg_reason] ~= nil then
		self.state_obj_list[self.old_msg_reason]:SetActive(false)
	end
	self.old_msg_reason = new_msg_reason
	self.state_obj_list[new_msg_reason]:SetActive(true)
end

function ChatCell:UseSysItem()
	return self.prefab_name == "SysContentLeft"
end

function ChatCell:PlayOrStopVoice(file_name)
	ChatWGCtrl.Instance:ClearPlayVoiceList()
	ChatWGCtrl.Instance:SetStartPlayVoiceState(false)
	local call_back = BindTool.Bind(self.ChangeVoiceAni, self)
	ChatRecordMgr.Instance:PlayVoice(file_name, call_back, call_back)
end

function ChatCell:OnFlush()
	if not self.data or not next(self.data) then
		return
	end

	if self.data.msg_reason == nil then
		self.data.msg_reason = CHAT_MSG_RESSON.NORMAL
	end

	if self.data.channel_type == CHANNEL_TYPE.SYSTEM or self.data.channel_type == CHANNEL_TYPE.CHUAN_WEN then --or self.data.channel_type == CHANNEL_TYPE.ZUDUI
		self.data.msg_reason = CHAT_MSG_RESSON.GUILD_TIPS
	end

	self:ChangeCellState(self.data.msg_reason)

	-- if self.data.msg_reason == CHAT_MSG_RESSON.LYL_SAORAO then
	-- 	self.node_list.normal_state:SetActive(false)
	-- 	return
	-- end

	if self.is_calc_high_cell then
		if self.data.msg_reason == CHAT_MSG_RESSON.NORMAL then
			self.role_id = self.data.from_uid
			self.content = self.data.content
			self.content_type = self.data.content_type
			self.send_time_str = self.data.send_time_str
			self.channel_type = self.data.channel_type
			local is_own =  GlobalLocalRoleId == self.data.from_uid
			self:TryLoadWindow(is_own)
		elseif self.data.msg_reason == CHAT_MSG_RESSON.TEAM_TIPS then
			self.role_id = self.data.from_uid
			self.content = self.data.content
			self.content_type = self.data.content_type
			self.send_time_str = self.data.send_time_str
			self.channel_type = self.data.channel_type
			local is_own =  GlobalLocalRoleId == self.data.from_uid
			self:TryLoadWindow(is_own)
		elseif self.data.msg_reason == CHAT_MSG_RESSON.GUILD_TIPS then
			self.role_id = self.data.from_uid
			self.content = self.data.content
			self.content_type = self.data.content_type
			self.send_time_str = self.data.send_time_str
			self.channel_type = self.data.channel_type
			self:LoadSysOrGuildTipsWindow()
		elseif self.data.msg_reason == CHAT_MSG_RESSON.HUDONG_PYP then
			--传入信息处理
			self.role_id = self.data.from_uid
			self.content = ChatWGData.Instance:GetPYPShowStr(self.data)
			self.content_type = self.data.content_type
			self.send_time_str = self.data.send_time_str
			self.channel_type = self.data.channel_type
			local is_own =  GlobalLocalRoleId == self.data.from_uid
			self:TryLoadWindow(is_own)
		end
		return
	end

	--如果是组队 特殊
	if self.data.msg_reason == CHAT_MSG_RESSON.NORMAL then

		local is_own =  GlobalLocalRoleId == self.data.from_uid -- self.data.channel_type ~= CHANNEL_TYPE.ZUDUI and
		self:HideChatCellContent(is_own)

		self.role_id = self.data.from_uid
		self.content = self.data.content
		self.content_type = self.data.content_type
		self.send_time_str = self.data.send_time_str
		self.channel_type = self.data.channel_type

		local time_str
		local color
		local show_city = ""
		-- local area_type = RoleWGData.GetAreaType()

		if not self.is_calc_high_cell then
			if type(self.send_time_str) == "number" then
				--时间处理 
				local sever_time = TimeWGCtrl.Instance:GetServerTime()
				local sever_time_tab = os.date("*t", sever_time)
				local info_time_tab = os.date("*t", self.send_time_str)

				if sever_time_tab.year ~= info_time_tab.year then
					-- 跨年的，则显示“[年/月/日 时:分:秒]”
					time_str = TimeUtil.FormatYMD(self.send_time_str)
				else
					-- 没跨年，而且一天以上  显示上 “[月/日 时:分:秒]”
					if sever_time - self.send_time_str > DAY_TIME then
						time_str = TimeUtil.FormatMDHMS(self.send_time_str)
					else
						-- 当天则显示“[时:分:秒]”
						time_str = TimeUtil.FormatHMS(self.send_time_str)
					end
				end
			else
				time_str = self.send_time_str
			end

			--[[屏蔽地区显示
			if area_type == AREA_TYPES.DEFAULT then
				if self.data.city_name ~= nil and self.data.city_name ~= "" then
					show_city = string.format("【%s】", self.data.city_name)
				else
					local privance, city = ChatWGData.Instance:Getlocation()
					if not self:IsMonsterMsg() then
						show_city = string.format("【%s】", city)
					end
				end
			end
			]]

			if self.channel_type == CHANNEL_TYPE.PRIVATE then
				color = COLOR3B.GREEN
			else
				color =  COLOR3B.D_GREEN
			end
		end

		local city_name = show_city
		local name_str = self.data.username

		local str_list = {}
		local split_name = ""

		if not self.is_calc_high_cell then
			if self.data.from_uid ~= self.data.from_cross_uuid then
				str_list = Split(name_str,"_")
				if self.data.channel_type == CHANNEL_TYPE.CROSS then -- IS_ON_CROSSSERVER and
					split_name = name_str
				else
					for i=1,#str_list - 1 do
						split_name = split_name .. str_list[i]
					end
				end
			else
				split_name = name_str
			end
			if split_name == "" then
				split_name = name_str
			end
		end
		-- split_name = ToColorStr(split_name,COLOR3B.PURPLE)

		--显示隐藏 标题跨服信息
		local node = is_own and self.node_list["cross_id_right"] or self.node_list["cross_id_left"]
		if self.cross_id ~= nil then
			node:SetActive(true)
			node.tmp.text = "("..self.cross_id..")"
		else
			node:SetActive(false)
		end


		local sex_str = "♀"
		local prof = self.data.prof
		prof = prof % 10

		--跨服频道处理
		if self.node_list.right_server then
			self.node_list.right_server:SetActive(false)
		end

		if self.node_list.left_server then
			self.node_list.left_server:SetActive(false)
		end

		if not self.is_calc_high_cell then
			if self.channel_type == CHANNEL_TYPE.CROSS or (IS_ON_CROSSSERVER and self.channel_type == CHANNEL_TYPE.SCENE) then
				if is_own then
					if self.node_list.right_server then
						self.node_list.right_server:SetActive(true)
						self.node_list.right_server.tmp.text = string.format(Language.Chat.ServerName,self.data.merge_server_id)
					end
				else
					if self.node_list.left_server and not self:IsMonsterMsg() then
						self.node_list.left_server:SetActive(true)
						self.node_list.left_server.tmp.text = string.format(Language.Chat.ServerName,self.data.merge_server_id)
					end
				end
			end
		end

		if not self.is_calc_high_cell then
			--设置发送时间/名字/城市
			if is_own then
				self.node_list["right_time"].tmp.text = time_str
				self.node_list["right_name"].tmp.text = split_name
				self.node_list["right_city"].tmp.text = ToColorStr(city_name, color)
				self.node_list["sex_right"].tmp.text = sex_str
				self.node_list["right_city"]:CustomSetActive(false)--area_type == AREA_TYPES.DEFAULT)
			else
				self.node_list["left_time"].tmp.text = time_str
				self.node_list["left_name"].tmp.text = split_name
				self.node_list["left_city"].tmp.text = city_name
				self.node_list["sex_left"].tmp.text = sex_str
			end
		end

		if self.channel_type == CHANNEL_TYPE.SYSTEM then
			self.node_list["left_city"]:SetActive(false)
			self.node_list["sex_right"]:SetActive(false)
			self.node_list["sex_left"]:SetActive(false)
		else
			self.node_list["left_city"]:CustomSetActive(false)--area_type == AREA_TYPES.DEFAULT)
			self.node_list["sex_right"]:SetActive(false)
			self.node_list["sex_left"]:SetActive(false)
		end

		--设置vip展示
		local vip_level = self.data.vip_level or 0
		local is_show_vip = true
		local friend = SocietyWGData.Instance:FindFriend(self.data.from_uid)
		vip_level = friend and friend.vip_level or vip_level
		vip_level = IS_AUDIT_VERSION and 0 or vip_level
		if vip_level <= 0 then
			is_show_vip = false
		end

		local image_obj = is_own and self.node_list["vip_image_right"] or self.node_list["vip_image_left"]
		local vip_bg_obj = is_own and self.node_list["vip_bg_right"] or self.node_list["vip_bg_left"]
		if vip_bg_obj ~= nil then
			vip_bg_obj:SetActive(is_show_vip and not self:IsMonsterMsg())
		end
        if image_obj ~= nil and vip_bg_obj then
			image_obj:SetActive(is_show_vip and not self:IsMonsterMsg())
			if is_show_vip and not self.is_calc_high_cell then
				--设置VIP隐藏
				local is_hide_vip = false
				if self:IsPrivateChannel() and is_own then--私聊频道中自己的隐藏状态特殊处理
					is_hide_vip = SettingWGData.Instance:IsHideMainRoleVipLv()
				else
					is_hide_vip = SettingWGData.Instance:IsHideRoleVipLv(self.data.shield_vip_flag)
				end
	
				-- 机器人屏蔽vip
				-- if self.data.from_uid <= 0 then
				-- 	is_hide_vip = true
				-- end
	
				local vip_res_name = is_hide_vip and "V" or "V"..vip_level
				image_obj.tmp.text = vip_res_name
				--image_obj.image:LoadSpriteAsync(ResPath.GetVipIcon(vip_res_name))
			end
		end

		local chat_content_show_type = self.data.chat_content_show_type

		if chat_content_show_type == CHAT_CONTENT_SHOW_TYPE.BILLION_DRPT then
			local item_seq, player_list = ChatWGData.Instance:GetBillionDRPTInfo(self.content)
			local drpt_idx = is_own and 2 or 1

			local node = drpt_idx == 1 and "drpt_player_left_item_" or "drpt_player_right_item_"
			for j = 1, DRPT_MAX_PLAYER_NUM do
				self.node_list[node .. j]:SetActive(j <= player_list[j].max_participate_num)
				self.billion_drpt_player_item_list[drpt_idx][j]:SetData(player_list[j])
			end
		end

		self:TryLoadWindow(is_own)

		local redpacket_obj = is_own and self.node_list["ContentPacketRight"] or self.node_list["ContentPacketLeft"]
		if redpacket_obj then
			redpacket_obj:SetActive(chat_content_show_type == CHAT_CONTENT_SHOW_TYPE.RED_PACKET)
		end

		local normal_details = is_own and self.node_list["right_details"] or self.node_list["left_details"]
		if normal_details then
			normal_details:SetActive(chat_content_show_type == CHAT_CONTENT_SHOW_TYPE.CHAT)
		end

		local billion_dezg_obj = is_own and self.node_list["ContentBillionDEZGRight"] or self.node_list["ContentBillionDEZGLeft"]
		if billion_dezg_obj then
			billion_dezg_obj:SetActive(chat_content_show_type == CHAT_CONTENT_SHOW_TYPE.BILLION_DEZG)
		end

		local billion_drpt_obj = is_own and self.node_list["ContentBillionDRPTRight"] or self.node_list["ContentBillionDRPTLeft"]
		if billion_drpt_obj then
			billion_drpt_obj:SetActive(chat_content_show_type == CHAT_CONTENT_SHOW_TYPE.BILLION_DRPT)
		end

		local show_correct_node = is_own and self.node_list["correct_right"] or self.node_list["correct_left"]
		if show_correct_node then
			show_correct_node:SetActive(false)
			--处理答题是否正确
			--策划说屏蔽掉
			-- if self.data.is_answer_true ~= nil then
			-- 	if self.node_list["correct_right"] and self.node_list["correct_left"] then
			-- 		local is_show_correct = self.data.is_answer_true == 1 and ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_ANSWER)
			-- 		show_correct_node:SetActive(is_show_correct)		
			-- 	end
			-- end
		end

		if not self.is_calc_high_cell then
			--设置频道图片
			local curr_show_channel = ChatWGCtrl.Instance:IsPrivateOpen()

			if self.channel_type ~= CHANNEL_TYPE.SCENE and CanShowChannel[curr_show_channel] then
				local bundle, asset = ResPath.GetChatType(CHANNEL_TYPE.WORLD)
				local title_text = Language.Channel[self.data.channel_type or 0]

				if CanShowChannel[self.data.channel_type] then
					--bundle, asset = ResPath.GetMainlblIcon(self.data.channel_type)
					bundle, asset = ResPath.GetChatType(self.data.channel_type)
				end
				if not SocietyWGCtrl.Instance.society_view:IsOpen() then
					if is_own then
						self.node_list["system_img_right"].image:LoadSprite(bundle, asset)
					else
						if not self:UseSysItem() then
							--self.node_list["system_img_left"]:SetActive(true)
							self.node_list["system_img_left"].image:LoadSprite(bundle, asset)
						else
							--self.node_list["system_img_left"]:SetActive(false)
						end
					end
				else
					if is_own then
						self.node_list["system_img_right"]:SetActive(false)
					else
						self.node_list["system_img_left"]:SetActive(false)
					end
				end
			end
		end

		local function SetIconImage(head_cell)
			if is_own == false then
				self.node_list["RawLeftImg"]:SetActive(true)
			end

			if not self.is_calc_high_cell then
				local fashion_photoframe = 0
				local cur_fashion_photoframe = AvatarManager.Instance:GetAvatarFrameKey(self.data.from_uid)
				if nil ~= cur_fashion_photoframe and cur_fashion_photoframe >= 0 then
					fashion_photoframe = cur_fashion_photoframe
				else
					fashion_photoframe = self.data.fashion_photoframe or 0
				end
				head_cell:SetData({role_id = self.data.from_uid, sex = self.data.sex, prof = self.data.prof, fashion_photoframe = fashion_photoframe})
			end
		end

		
		local cur_node = is_own and self.node_list["rank_right"] or self.node_list["rank_left"]
		local cur_node_one = is_own and self.node_list["one_rank_right"] or self.node_list["one_rank_left"]
		if cur_node and cur_node_one then
			if self.data.rank then
				cur_node:SetActive(self.data.rank > 1 and self.data.rank <= 10)
				cur_node_one:SetActive(self.data.rank > 0 and self.data.rank <= 1)
			else--cell复用刷新
				cur_node:SetActive(false)
				cur_node_one:SetActive(false)
			end
		end
        if self.data.channel_type ~= CHANNEL_TYPE.SYSTEM and self.data.channel_type ~= CHANNEL_TYPE.CHUAN_WEN 
        	and (not self.data.is_lyl_harass or self.data.is_lyl_harass == 0) then
            if self:IsMonsterMsg() then
                self.node_list["RawLeftImg"]:SetActive(false)
                self:ShowMonsterIcon(self.data.monster_id)
            else
                if self.node_list["monster_icon_bg"] then
                    self.node_list["monster_icon_bg"]:SetActive(false)
                end
                if is_own then
                    SetIconImage(self.right_head_cell)
                else
                    SetIconImage(self.left_head_cell)
                end
            end
		end

		if self.channel_type ~= CHANNEL_TYPE.CHUAN_WEN or self.channel_type ~= CHANNEL_TYPE.ZUDUI then
			if self.node_list["TopTitle1"] then
				self.node_list["TopTitle1"]:SetActive(false)
			end
		end

		if self.data.add_type and self.data.add_type == ADD_FRIEND_REQ_TYPE.LIAOYILIAO and self.node_list.add_friend_form_lyl_state ~= nil then
			self.node_list.normal_state:SetActive(false)
			self.node_list.add_friend_form_lyl_state:SetActive(true)
			self.node_list.add_friend_form_lyl_text.text = string.format("<color=#595959>%s</color>", self.data.content)
		else
			if self.node_list.add_friend_form_lyl_state ~= nil then
				self.node_list.add_friend_form_lyl_state:SetActive(false)
			end
		end

		if self.data.is_lyl_harass and self.data.is_lyl_harass == 1 and self.node_list.lyl_saorao_state ~= nil then
			self.node_list.lyl_saorao_state:SetActive(true)
			self.node_list.normal_state:SetActive(false)
		else
			if self.node_list.lyl_saorao_state ~= nil then
				self.node_list.lyl_saorao_state:SetActive(false)
			end
		end
	elseif self.data.msg_reason == CHAT_MSG_RESSON.TEAM_TIPS then
		self.role_id = self.data.from_uid
		self.content = self.data.content
		self.content_type = self.data.content_type
		self.send_time_str = self.data.send_time_str
		self.channel_type = self.data.channel_type

		local msg_id = self.data.msg_id
		--相同文本相同msg_id不处理
		if msg_id ~= self.old_msg_id then
			self.old_msg_id = msg_id
			self:LoadSpecialTeamWindow()
		end

	elseif self.data.msg_reason == CHAT_MSG_RESSON.GUILD_TIPS then
		self.role_id = self.data.from_uid
		self.content = self.data.content

		self.content_type = self.data.content_type
		self.send_time_str = self.data.send_time_str
		self.channel_type = self.data.channel_type

		local msg_id = self.data.msg_id
		--相同文本相同msg_id不处理
		if msg_id ~= self.old_msg_id then
			self.old_msg_id = msg_id
			self:LoadSysOrGuildTipsWindow()
		end
	elseif self.data.msg_reason == CHAT_MSG_RESSON.HUDONG_PYP then
		--传入信息处理
		self.role_id = self.data.from_uid
		self.content = ChatWGData.Instance:GetPYPShowStr(self.data)
		self.content_type = self.data.content_type
		self.send_time_str = self.data.send_time_str
		self.channel_type = self.data.channel_type

		local msg_id = self.data.msg_id
		--相同文本相同msg_id不处理
		if msg_id ~= self.old_msg_id then
			self.old_msg_id = msg_id
			self:LoadHuDongPYPWindow()
		end
		--刷新引导显示
		self:FlushPYPGuideShow()
	end
end

function ChatCell:ShowMonsterIcon(monster_id)
    if self.node_list["monster_icon_bg"] then
        self.node_list["monster_icon_bg"]:SetActive(true)
        local cfg = BossWGData.Instance:GetMonsterCfgByid(monster_id)
        if cfg then
            local bundle, asset = ResPath.GetBossIcon("wrod_boss_" .. cfg.small_icon)
            self.node_list.monster_icon.image:LoadSprite(bundle, asset, function()
                self.node_list.monster_icon.image:SetNativeSize()
            end)
        end
    end
end


function ChatCell:GetBubbleIndex(bubble)
	if bubble < GameEnum.FASHION_CRISIS_NUM then -- 协议Decode的时候就读表转换成Index了
		return bubble
	end
	local image_cfg = NewAppearanceWGData.Instance:GetFashionCfgByProtocolIndex(SHIZHUANG_TYPE.BUBBLE, bubble)
  	return image_cfg and image_cfg.resouce or 0
end

--显示是否自己发送的聊天内容
function ChatCell:HideChatCellContent(is_own)
	if is_own then
		self.node_list["show_left_content"]:SetActive(false)
		self.node_list["show_right_content"]:SetActive(true)
	else
		self.node_list["show_right_content"]:SetActive(false)
		self.node_list["show_left_content"]:SetActive(true)
	end
end

--头像回调
function ChatCell:AvatarLoadCallBack(role_id, raw_img_obj, path)
	if self:IsNil() then
		return
	end

	if role_id ~= self.role_id then
		self.is_show_image:SetValue(true)
		return
	end

	if path == nil then
		path = AvatarManager.GetFilePath(self.role_id, false)
	end

	raw_img_obj.raw_image:LoadSprite(path, function ()
		if role_id ~= self.role_id then
			self.is_show_image:SetValue(true)
			return
		end
		self.is_show_image:SetValue(false)
	end)
end

function ChatCell:ClickCallBack(callback, file_name)
	if callback then
		callback(file_name)
	end
end

function ChatCell:CheckIsNeedAdjust(content)
	local is_need = false
	if content ~= nil then
		local begin = string.find(content, "%[%d+%]")
		if begin ~= nil then
			is_need = true
		end
	end

	return is_need
end

function ChatCell:SetContent(emoji_text, is_left, max_width)
	local color = "#2B3044"
	--是否语音
	if self.content_type == CHAT_CONTENT_TYPE.AUDIO then
		local str = self.content
		local tbl = {}
		for i = 1, 3 do
			local j, k = string.find(str, "(%d+)")
			if (nil ~= j) and (nil ~= k) then
				local num = string.sub(str, j, k)
				str = string.gsub(str, num, "num")
				table.insert(tbl, num)
			end
		end
		local callback = BindTool.Bind(self.PlayOrStopVoice, self)
		self:SetVoiceContent(self.content_obj, tbl, is_left, callback, self.content, emoji_text)
		return
	elseif self.content_type == CHAT_CONTENT_TYPE.FEES_AUDIO then
		local content_t = Split(self.content, "_")
		if #content_t ~= 3 then
			return
		end
		local callback = BindTool.Bind(self.PlayOrStopFeesVoice, self)
		self:SetVoiceContent(self.content_obj, content_t[3], is_left, callback, content_t[1], emoji_text, content_t[2], color, true)
		return
	end

	local has_bubble = false
	local is_light_bubble = false
	local bubble_cfg = nil

	if not self.is_calc_high_cell then
		local cur_fashion_bubble = AvatarManager.Instance:GetAvatarBubbleKey(self.data.from_uid)
		local fashion_bubble = 0
		if nil ~= cur_fashion_bubble and cur_fashion_bubble >= 0 then
			fashion_bubble = cur_fashion_bubble
		else
			fashion_bubble = self.data.fashion_bubble or 0
		end

		if fashion_bubble ~= nil and self.content_obj then
			self.bubble_index = self:GetBubbleIndex(fashion_bubble)
			if self.bubble_index ~= 0 then
				bubble_cfg = NewAppearanceWGData.Instance:GetBubbleCfg(self.bubble_index)
				color =	bubble_cfg and bubble_cfg.color or "#545252"
			end
		end

		if nil ~= self.data.fashion_bubble then
			if not (self.data.channel_type == CHANNEL_TYPE.GUILD and self.data.msg_reason == CHAT_MSG_RESSON.GUILD_TIPS) then
				has_bubble = true
			end

			bubble_cfg = NewAppearanceWGData.Instance:GetBubbleCfg(self.data.fashion_bubble)
			is_light_bubble = bubble_cfg and bubble_cfg.is_light and bubble_cfg.is_light == 1 or false
		end
	end

	--队伍颜色特殊处理
	if self.data.is_add_team ~= nil then
		if self.data.is_add_team == true then
			color = "#28e53aFF"
		else
			color = "#999CA3FF"
		end
	end
	
	local content = self.content
	if self:UseSysItem() then
		if not self.is_calc_high_cell and CanShowChannel[self.data.channel_type] then
			local bundle_name, asset_name = ResPath.GetChatType(self.data.channel_type)
			color = F2ChatTextColor[9]
			local image = self.content_obj.transform:Find("Image"):GetComponent(TypeImage)
			local loader = AllocResAsyncLoader(self, "ChannelImage")
			loader:Load(bundle_name, asset_name, TypeUnitySprite, function (sprite)
				if nil ~= sprite and nil ~= image and not IsNil(image) then
					image.sprite = sprite
				end
			end)
        end
        if self.data.channel_type == CHANNEL_TYPE.SYSTEM and self.data.is_show_time and self.data.is_show_time == 1 then
            content =  "<color=#a4e06c>".. TimeUtil.FormatHMS(self.data.send_time_str).."</color>"..content
        end
		-- content = "          " .. content  
		--带图片的传闻试试看这种行不行
		content = "<color=#00000000>00000</color>" .. content
	end

	local chat_content_show_type = self.data.chat_content_show_type
	if chat_content_show_type == CHAT_CONTENT_SHOW_TYPE.BILLION_DEZG then
		color = COLOR3B.C1
	elseif chat_content_show_type == CHAT_CONTENT_SHOW_TYPE.BILLION_DRPT then
		color = COLOR3B.C1
	end

	content = ChatWGData.Instance:TryAnalysisAiteMsg(content)
	EmojiTextUtil.ParseRichText(emoji_text, content, 20, color, false, nil, RICH_CONTENT_TYPE.CHAT_WINDOW, has_bubble, nil, is_light_bubble)
	-- 根据文本内容计算文本大小
	local bounds = TMPUtil.GetTMPBounds(emoji_text, emoji_text.text, max_width)
	-- 设置文本和父节点大小
	self.content_obj.rect.sizeDelta = bounds
	local rect = emoji_text:GetComponent(TypeRectTransform)
	rect.sizeDelta = bounds
end

function ChatCell:PlayOrStopFeesVoice(file_id)
	ChatWGCtrl.Instance:ClearPlayVoiceList()
	ChatWGCtrl.Instance:SetStartPlayVoiceState(false)
	local call_back = BindTool.Bind(self.ChangeVoiceAni, self)
	AudioService.Instance:PlayFeesAudio(file_id, call_back)
end

function ChatCell:ChangeVoiceAni(state)
	if self.voice_animator and not IsNil(self.voice_animator.gameObject) then
		self.voice_animator:SetBool("play", state)
	end
end

--是否私聊
function ChatCell:IsPrivateChannel()
 	return self.data.channel_type == CHANNEL_TYPE.PRIVATE
end

--是否跨服频道
function ChatCell:IsCrossServerChannel()
	return self.data.channel_type == CHANNEL_TYPE.CROSS
end

--每个cell的高度
function ChatCell:GetContentHeight()
	local is_private = self:IsPrivateChannel()

	if self.data.is_lyl_harass or self.data.add_type and self.data.add_type == ADD_FRIEND_REQ_TYPE.LIAOYILIAO then
		return 50

	elseif self.old_msg_reason == CHAT_MSG_RESSON.NORMAL then
		local content_height
		if self.data.chat_content_show_type == CHAT_CONTENT_SHOW_TYPE.CHAT and self.content_obj then
			local height = self.node_list["ChatCell"]:GetComponent(TypeRectTransform).rect.height
			local rect = self.content_obj:GetComponent(TypeRectTransform)
			local y = rect.localPosition.y
			local des_height = rect.rect.height--rect.sizeDelta.y
			local transform = self.content_obj.transform:FindHard("ChatBg")
			--local bg_obj = nil
			local bg_size_y = 160--40（暂时这样子写）
			if transform ~= nil then
				local bg_rect = transform:GetComponent(TypeRectTransform)
				bg_size_y = bg_rect.sizeDelta.y --<= 60 and 65 or bg_rect.sizeDelta.y
			end

			--格子的高度/2 - rich_text对象的Y坐标 + rich_text对象的高度 + 25
			if is_private then
				content_height = height/2 - y + des_height + bg_size_y / 2
			else
				content_height = height/2 - y + des_height + bg_size_y / 2 + 8    --其他频道适应拍一拍
			end
		elseif self.data.chat_content_show_type == CHAT_CONTENT_SHOW_TYPE.BILLION_DEZG then
			content_height = 180
		elseif self.data.chat_content_show_type == CHAT_CONTENT_SHOW_TYPE.BILLION_DRPT then
			content_height = 210
		else
			content_height = 142
		end

		return content_height
	elseif self.old_msg_reason == CHAT_MSG_RESSON.TEAM_TIPS then
		return 38

	elseif self.old_msg_reason == CHAT_MSG_RESSON.GUILD_TIPS then
		local height = self.node_list["ChatCell"]:GetComponent(TypeRectTransform).rect.height
		local rect = self.content_obj:GetComponent(TypeRectTransform)
		local y = rect.localPosition.y
		local des_height = rect.rect.height
		local transform = self.content_obj.transform:FindHard("ChatBg")
		local bg_size_y = 0
		if transform ~= nil then
			local bg_rect = transform:GetComponent(TypeRectTransform)
			bg_size_y = bg_rect.sizeDelta.y
		end 
		local content_height = height/2 - y + des_height + bg_size_y / 2 + 10

		return content_height
	elseif self.old_msg_reason == CHAT_MSG_RESSON.HUDONG_PYP then
		local content_height = is_private and 22 or 38
		if not IsEmptyTable(self.pyp_text_go_list) then
			local des_height = self.pyp_text_go_list.pyp_emoji_text_rect.rect.height
			if is_private then--私聊 -4
				content_height = des_height - 4
			else--世界 +16
				content_height = des_height + 16
			end
		end

		return content_height

	end
end

function ChatCell:LoadSysOrGuildTipsWindow()
	local assetbundle = "uis/view/miscpre_load_prefab"
	self.prefab_name = "SysContentLeft"
	if self.content_obj then
		self:ClearVoiceButton()
		ResPoolMgr:Release(self.content_obj.gameObject)
		self.content_obj = nil
	end

	local tmp_obj = ResPoolMgr:TryGetGameObject(assetbundle, self.prefab_name)
	self.content_obj = U3DObject(tmp_obj, tmp_obj.transform ,self)
	local parent_obj = self.node_list["SysParent"]
	self.content_obj.transform:SetParent(parent_obj.transform, false)
	local emoji_text = self.content_obj.transform:Find("EmojiText"):GetComponent(TypeTMP)
	self:SetContent(emoji_text, nil, TYPE_WIDTH_MAX[CHAT_MSG_RESSON.GUILD_TIPS])
end

function ChatCell:SetTeamBg(image_name)
	if self.node_list.team_bg then
		self.node_list.team_bg.image:LoadSprite("uis/uires/res/x1ui/chat_atlas",image_name)
	end
end

function ChatCell:LoadSpecialTeamWindow()
	local assetbundle = "uis/view/miscpre_load_prefab"
	self.prefab_name = "TeamContentCenter"

	if self.content_obj then
		self:ClearVoiceButton()
		ResPoolMgr:Release(self.content_obj.gameObject)
		self.content_obj = nil
	end

	self.content_obj = U3DObject(ResPoolMgr:TryGetGameObject(assetbundle, self.prefab_name), nil , self)
	local parent_obj = self.node_list["ParentCenter"]
	self.content_obj.transform:SetParent(parent_obj.transform, false)
	local emoji_text = self.content_obj.transform:Find("EmojiText"):GetComponent(TypeTMP)
	self:SetContent(emoji_text,nil, TYPE_WIDTH_MAX[CHAT_MSG_RESSON.TEAM_TIPS])

	if self.data.is_add_team then   --加入队伍
		--self:SetTeamBg("zudui_tip1")
	else 							--退出队伍
		--self:SetTeamBg("zudui_tip2")
	end
end

function ChatCell:LoadHuDongPYPWindow()
	local assetbundle = "uis/view/miscpre_load_prefab"
	self.prefab_name = "hudong_content_center"
	if self.content_obj then
		self:ClearVoiceButton()
		ResPoolMgr:Release(self.content_obj.gameObject)
		self.content_obj = nil
	end

	local tmp_obj = ResPoolMgr:TryGetGameObject(assetbundle, self.prefab_name)
	if tmp_obj then
		self.content_obj = U3DObject(tmp_obj, tmp_obj.transform ,self)
		local parent_obj = self.node_list["hudong_center"]
		self.content_obj.transform:SetParent(parent_obj.transform, false)

		if not self.pyp_text_go_list then
			self.pyp_text_go_list = {}
		end

		local emoji_text_trs = self.content_obj.transform:Find("emoji_text"):GetComponent(TypeTMP)
		local first_guide_trs = self.content_obj.transform:Find("first_guide_btn").transform
		local pingbi_guide_trs = self.content_obj.transform:Find("pingbi_guide_btn").transform
		if emoji_text_trs and first_guide_trs and pingbi_guide_trs then
			self.pyp_text_go_list.pyp_emoji_root = emoji_text_trs.gameObject
			self.pyp_text_go_list.pyp_emoji_text_rect = emoji_text_trs:GetComponent(TypeRectTransform)
			self.pyp_text_go_list.pyp_emoji_text = emoji_text_trs:GetComponent(TypeTMP)

			self.pyp_text_go_list.first_guide_root = first_guide_trs.gameObject
			self.pyp_text_go_list.first_guide_btn = first_guide_trs:GetComponent(TypeButton)
			self.pyp_text_go_list.first_guide_link_text = first_guide_trs:Find("text_first_guide_1"):GetComponent(TypeTMP)
			self.pyp_text_go_list.first_guide_gray_text = first_guide_trs:Find("text_first_guide_2"):GetComponent(TypeTMP)

			self.pyp_text_go_list.pingbi_guide_root = pingbi_guide_trs.gameObject
			self.pyp_text_go_list.pingbi_guide_btn = pingbi_guide_trs:GetComponent(TypeButton)
			self.pyp_text_go_list.pingbi_guide_gray_text = pingbi_guide_trs:Find("text_pingbi_guide_1"):GetComponent(TypeTMP)
			self.pyp_text_go_list.text_pingbi_link_text = pingbi_guide_trs:Find("text_pingbi_guide_2"):GetComponent(TypeTMP)
		end
	end
end

function ChatCell:FlushPYPGuideShow()
	if IsEmptyTable(self.pyp_text_go_list) then
		return
	end
	local is_private = self:IsPrivateChannel()
	--0: None 不引导 1: First 引导第一次 2: PingBi 引导屏蔽
	local color = is_private and COLOR3B_1.DEFAULT_NUM or COLOR3B_1.DEFAULT_NUM
	local color_gray = COLOR3B_1.WHITE

	if self.data.pyp_guide_type == PYP_MSG_GUIDE_TYPE.First then--显示初次拍一拍引导
		self.pyp_text_go_list.first_guide_btn:AddClickListener(function()
			self:OnJumpToPYPSeting()
		end)
		self.pyp_text_go_list.first_guide_link_text.color = color
		self.pyp_text_go_list.first_guide_gray_text.color = color_gray
	elseif self.data.pyp_guide_type == PYP_MSG_GUIDE_TYPE.PingBi then--显示屏蔽引导
		self.pyp_text_go_list.pingbi_guide_btn:AddClickListener(function()
			self:OnJumpToPYPSeting()
		end)
		self.pyp_text_go_list.text_pingbi_link_text.color = color
		self.pyp_text_go_list.pingbi_guide_gray_text.color = color_gray
	else
		self:SetContent(self.pyp_text_go_list.pyp_emoji_text, nil, TYPE_WIDTH_MAX[CHAT_MSG_RESSON.HUDONG_PYP])
	end
	self.pyp_text_go_list.pyp_emoji_root:SetActive(not self.data.pyp_guide_type or self.data.pyp_guide_type == PYP_MSG_GUIDE_TYPE.None)
	self.pyp_text_go_list.first_guide_root:SetActive(self.data.pyp_guide_type == PYP_MSG_GUIDE_TYPE.First)
	self.pyp_text_go_list.pingbi_guide_root:SetActive(self.data.pyp_guide_type == PYP_MSG_GUIDE_TYPE.PingBi)
end

--跳转拍一拍设置界面
function ChatCell:OnJumpToPYPSeting()
	if self:IsPrivateChannel() then
		SocietyWGCtrl.Instance:OpenSocietyViewMore(6)
	else
		ChatWGCtrl.Instance:OpenChatWindowMore(CHAT_MORE.ZHAOHU)
	end
end

--加载聊天框
function ChatCell:LoadWindow(main_role_id)
	local assetbundle = ""
	self.prefab_name = ""

	local assetbundle_bg = ""
	local prefab_name_bg = ""
	local left = true
	local bubble_type = self.data.channel_window_bubble_type
	bubble_type = bubble_type or -1
	bubble_type = bubble_type + 1
	if bubble_type == -1 then
		bubble_type = 0
	end

	if GlobalLocalRoleId == self.data.from_uid then
		left = false
	end

	--如果是喇叭发起 跨服聊天
	if self.data.speaker_type then
		if self.data.speaker_type == SPEAKER_TYPE.SPEAKER_TYPE_CROSS then
			if GlobalLocalRoleId == self.role_id then
				left = false
			end
		end
	end

	self.is_special_bubble = false
	if self.data.channel_type and (self.data.channel_type == CHANNEL_TYPE.SYSTEM or self.data.channel_type == CHANNEL_TYPE.CHUAN_WEN ) then
		assetbundle = "uis/view/miscpre_load_prefab"
		self.prefab_name = left and "SysContentLeft" or "SystemContentRight"

	elseif not bubble_type or bubble_type == 0 then
		assetbundle = "uis/view/miscpre_load_prefab"
		self.prefab_name = left and "ContentLeft" or "ContentRight"
	else  -- 特殊气泡框只加载容器
		assetbundle = "uis/view/miscpre_load_prefab"
		self.prefab_name = left and "ContentLeft" or "ContentRight"
		self.is_special_bubble = true
	 end

	--if self.data.channel_type and self.data.channel_type == CHANNEL_TYPE.ZUDUI then
	--	left = true
	--	assetbundle = "uis/view/miscpre_load_prefab"
	--	self.prefab_name = "SysContentLeft"
	--end

 	if SocietyWGCtrl.Instance.is_friend_chat_panel then
		assetbundle = "uis/view/miscpre_load_prefab"
		self.prefab_name = left and "ContentLeft_F" or "ContentRight_F"
	end

	if self.content_obj then
		self:ClearVoiceButton()
		ResPoolMgr:Release(self.content_obj.gameObject)
		self.content_obj = nil
	end

	self.content_obj = U3DObject(ResPoolMgr:TryGetGameObject(assetbundle, self.prefab_name), nil ,self)
	local parent_obj = left and self.node_list["left_details"] or self.node_list["right_details"]
	self.content_obj.transform:SetParent(parent_obj.transform, false)

	local chat_content_show_type = self.data.chat_content_show_type
	local emoji_text

	if chat_content_show_type == CHAT_CONTENT_SHOW_TYPE.CHAT then
		emoji_text = self.content_obj.transform:Find("EmojiText"):GetComponent(TypeTMP)
	elseif chat_content_show_type == CHAT_CONTENT_SHOW_TYPE.BILLION_DEZG then
		local emoji_node = left and self.node_list["billion_dezg_left_text"] or self.node_list["billion_dezg_right_text"]
		emoji_text = emoji_node:GetComponent(TypeTMP)
	elseif chat_content_show_type == CHAT_CONTENT_SHOW_TYPE.BILLION_DRPT then
		local emoji_node = left and self.node_list["billion_drpt_left_text"] or self.node_list["billion_drpt_right_text"]
		emoji_text = emoji_node:GetComponent(TypeTMP)
	end
	self:SetContent(emoji_text, left, TYPE_WIDTH_MAX[CHAT_MSG_RESSON.NORMAL])

	if self.is_calc_high_cell then
		return
	end

	if self.channel_type ~= CHANNEL_TYPE.CHUAN_WEN or self.channel_type ~= CHANNEL_TYPE.ZUDUI then
		if self.node_list["TopTitle1"] then
			self.node_list["TopTitle1"]:SetActive(false)
		end

		local cur_fashion_bubble = AvatarManager.Instance:GetAvatarBubbleKey(self.data.from_uid)
		local fashion_bubble = 0
		if nil ~= cur_fashion_bubble and cur_fashion_bubble >= 0 then
			fashion_bubble = cur_fashion_bubble
		else
			fashion_bubble = self.data.fashion_bubble or 0
		end

		if fashion_bubble ~= nil and self.content_obj and not self.is_calc_high_cell then
			self.bubble_index = self:GetBubbleIndex(fashion_bubble)
			local transform = self.content_obj.transform:FindHard("ChatBg")
			if not IsNil(transform) then
				local b,a = ResPath.ChatBubbleBg(fashion_bubble)
				local async_loader = AllocAsyncLoader(self, "bubble")
				async_loader:SetParent(transform)
				async_loader:Load(b, a, function(obj)
					if IsNil(obj) then
						return
					end

					local adorn = obj.transform:Find("Adorn")
					-- local angle = 180
					local angle = 0

					if not left then
						local qipao_cfg = NewAppearanceWGData.Instance:GetBubbleCfg(self.bubble_index)
						if qipao_cfg and qipao_cfg.is_turn == 1 then -- 1代表true 翻转小装饰
							angle = 0
						end
					end
					if adorn then
						adorn.transform.localRotation = Quaternion.Euler(0, angle, 0)
					end
					self.bubble_obj = obj
				end)
			end
		end
	end
end

function ChatCell:SetVoiceContent(obj_content, play_time, is_left, callback, file_name, emoji_text, str, color, is_fees)
	if not obj_content then
		return
	end

	local btn_name = is_left and "VioceButtonLeft" or "VioceButtonRight"
	local assetbundle = "uis/view/miscpre_load_prefab"

	if nil ~= str and "" ~= str then
		str = "\n" .. str
		EmojiTextUtil.ParseRichText(emoji_text, str, 20, color)
		local emoji_size = emoji_text:GetComponent(TypeRectTransform).sizeDelta
		local width = math.max(VoiceBgSize.x, emoji_size.x)
		obj_content.rect.sizeDelta = Vector2(width, emoji_size.y)
	else
		obj_content.rect.sizeDelta = VoiceBgSize
		EmojiTextUtil.ParseRichText(emoji_text, "", 20, color)
	end

	local voice_obj_content = nil

	if self.is_special_bubble then
		if is_left then
			if self.voice_obj_left_bubble == nil or IsNIl(self.voice_obj_left_bubble.gameObject) then
				self.voice_obj_left_bubble = self:CreateAduioClip(obj_content, assetbundle, btn_name, is_fees)
			else
				self.voice_obj_left_bubble:SetActive(true)
			end
			self.voice_animator_left_bubble = self.voice_obj_left_bubble:GetComponent(typeof(UnityEngine.Animator))
			voice_obj_content = self.voice_obj_left_bubble
		else
			if self.voice_obj_right_bubble == nil or IsNIl(self.voice_obj_right_bubble.gameObject) then
				self.voice_obj_right_bubble = self:CreateAduioClip(obj_content, assetbundle, btn_name, is_fees)
			else
				self.voice_obj_right_bubble:SetActive(true)
			end
			self.voice_animator_right_bubble = self.voice_obj_right_bubble:GetComponent(typeof(UnityEngine.Animator))
			voice_obj_content = self.voice_obj_right_bubble
		end
	else
		if is_left then
			if nil == self.voice_obj_left or IsNil(self.voice_obj_left.gameObject) then
				self.voice_obj_left = self:CreateAduioClip(obj_content, assetbundle, btn_name, is_fees)
			else
				self.voice_obj_left:SetActive(true)
			end
			self.voice_animator_left = self.voice_obj_left:GetComponent(typeof(UnityEngine.Animator))
			voice_obj_content = self.voice_obj_left
		else
			if nil == self.voice_obj_right or IsNil(self.voice_obj_right.gameObject) then
				self.voice_obj_right = self:CreateAduioClip(obj_content, assetbundle, btn_name, is_fees)
			else
				self.voice_obj_right:SetActive(true)
			end
			self.voice_animator_right = self.voice_obj_right:GetComponent(typeof(UnityEngine.Animator))
			voice_obj_content = self.voice_obj_right
		end
	end
	self:InitPlayVoice(voice_obj_content, play_time, callback, file_name, is_left)
end

function ChatCell:CreateAduioClip(obj_content, assetbundle, btn_name, is_fees)
	local voice_obj = self:CreateChatContent(assetbundle, btn_name)
	local y = is_fees and 10 or 0
	voice_obj.transform:SetLocalPosition(0, y, 0)
	voice_obj.transform:SetParent(obj_content.transform, false)
	return voice_obj
end

function ChatCell:InitPlayVoice(voice_obj, play_time, callback, file_name, is_left)
	if not voice_obj then
		return
	end
	local time = 0
	if "table" == type(play_time) then
		time = play_time[3]
	else
		time = play_time
	end
	local name_table = voice_obj:GetComponent(typeof(UINameTable))
	local time_node = U3DObject(name_table:Find("TxtTime"))
	time_node.tmp.text = time

	local content_node = U3DObject(name_table:Find("Content"))
	content_node.tmp.text = ""

	local btn_node = U3DObject(name_table:Find("VioceButton"))
	btn_node.button:AddClickListener(BindTool.Bind(self.ClickCallBack, self, callback, file_name, is_left))
end

function ChatCell:CreateChatContent(assetbundle, prefab_name)
	local gameobj = ResPoolMgr:TryGetGameObject(assetbundle, prefab_name)
	local obj = U3DObject(
		gameobj,
		gameobj.transform,
		self
	)
	return obj
end

function ChatCell:TryLoadWindow(is_own)
	--红包消息
	if self.data.chat_content_show_type == CHAT_CONTENT_SHOW_TYPE.RED_PACKET then
		local packet_text = is_own and self.node_list["pack_right_text"] or self.node_list["pack_left_text"]
		if packet_text and packet_text.text then
			packet_text.text.text = self.data.content
		end

	else --普通消息
		local msg_id = self.data.msg_id
		--相同文本相同msg_id不处理
		if msg_id ~= self.old_msg_id then
			self.old_msg_id = msg_id
			local main_role_id = GameVoManager.Instance:GetMainRoleVo().role_id
			self:LoadWindow(main_role_id)
		end
	end
end

---------------------------------------ChatBillionDRPTPlayerItem----------------------------------
ChatBillionDRPTPlayerItem = ChatBillionDRPTPlayerItem or BaseClass(BaseRender)

function ChatBillionDRPTPlayerItem:LoadCallBack()
	if not self.role_head then
		self.role_head = BaseHeadCell.New(self.node_list.player_node)
		self.role_head:SetBgActive(false)
	end

	XUI.AddClickEventListener(self.node_list.head_btn, BindTool.Bind(self.OnClickMainRoleHead, self))
end

function ChatBillionDRPTPlayerItem:ReleaseCallBack()
	if self.role_head then
		self.role_head:DeleteMe()
		self.role_head = nil
	end
end

function ChatBillionDRPTPlayerItem:OnFlush()
	if not self.data then
		return
	end

	self:SetHeadIcon()
end

function ChatBillionDRPTPlayerItem:SetHeadIcon()
	self.node_list.bg_1:SetActive(self.data.uid == 0)
	self.node_list.bg_2:SetActive(self.data.uid ~= 0)
	self.role_head:SetActive(self.data.uid ~= 0)

	if self.data.uid ~= 0 then
		local data = {}
		data.role_id = self.data.uid
		data.prof = self.data.prof
		data.sex = self.data.sex
		self.role_head:SetData(data)
		self.role_head:SetImgBg(true)
	end
end

function ChatBillionDRPTPlayerItem:OnClickMainRoleHead()
	if not self.data then
		return
	end

	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local is_show = server_day == self.data.show_day
	if not is_show then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BillionSubsidy.DRPTNotOpenAct)
		return
	end

	ViewManager.Instance:Open(GuideModuleName.BillionSubsidy, TabIndex.billion_subsidy_drpt, "jump_tab", {jump_tab = self.data.item_seq})
end

function ChatCell:OnClickBillionDezgBtn()
	local chat_content_show_type = self.data.chat_content_show_type
	if chat_content_show_type == CHAT_CONTENT_SHOW_TYPE.BILLION_DEZG then
		local uid, item_seq, grade = ChatWGData.Instance:GetBillionDEZGInfo(self.content)
		BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.HIGH_PRICE_RMB_BUY_HELP, uid, item_seq, grade)
		ViewManager.Instance:Open(GuideModuleName.BillionSubsidy, TabIndex.billion_subsidy_dezg, "jump_tab", {jump_tab = item_seq})
	end
end