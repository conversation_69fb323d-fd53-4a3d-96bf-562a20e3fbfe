DownLoadWebView = DownLoadWebView or BaseClass(SafeBaseView)

function DownLoadWebView:__init()
    self.view_layer = UiLayer.Normal
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/downloadweb_ui_prefab", "layout_downloadweb")
end

function DownLoadWebView:__delete()
end

function DownLoadWebView:ReleaseCallBack()
    if self.item_list_view then
        self.item_list_view:DeleteMe()
        self.item_list_view = nil
    end
end

function DownLoadWebView:OpenCallBack()
    DownLoadWebWGCtrl.Instance:SendSDownLoadReq(AGENT_DOWN_OPERATE_TYPE.INFO)
end

function DownLoadWebView:LoadCallBack()
    if not self.item_list_view then
        self.item_list_view = AsyncListView.New(ItemCell,self.node_list["item_list"])
    end

    XUI.AddClickEventListener(self.node_list.get_btn, BindTool.Bind1(self.OnClickGet, self))    
end

function DownLoadWebView:ShowIndexCallBack()
     
end

function DownLoadWebView:OnFlush(param_t, index)
    local reward_info = DownLoadWebWGData.Instance:GetCurSpidInfo()
    if reward_info then
        local item_data = SortDataByItemColor(reward_info)
        self.item_list_view:SetDataList(item_data)

        local state = DownLoadWebWGData.Instance:GetAgentRewardInfo()
        local str = (state == AGENT_DOWN_REWARD_STATUS.CANT_GET) and Language.DownLoadWeb.BtnDesc1 or Language.DownLoadWeb.BtnDesc2
        self.node_list.get_text.text.text = str
        self.node_list.get_btn:SetActive(state ~= AGENT_DOWN_REWARD_STATUS.HAS_GET)
        self.node_list.reward_get:SetActive(state == AGENT_DOWN_REWARD_STATUS.HAS_GET)
        self.node_list.get_remind:SetActive(state == AGENT_DOWN_REWARD_STATUS.CAN_GET)
    end
end

function DownLoadWebView:OnClickGet()
    local state = DownLoadWebWGData.Instance:GetAgentRewardInfo()
    if state == AGENT_DOWN_REWARD_STATUS.CANT_GET then
        DownLoadWebWGCtrl.Instance:OpenWebView()
        DownLoadWebWGCtrl.Instance:SendSDownLoadReq(AGENT_DOWN_OPERATE_TYPE.SET_STATUS, AGENT_DOWN_REWARD_STATUS.CAN_GET)
    elseif state == AGENT_DOWN_REWARD_STATUS.CAN_GET then
        DownLoadWebWGCtrl.Instance:SendSDownLoadReq(AGENT_DOWN_OPERATE_TYPE.SET_STATUS, AGENT_DOWN_REWARD_STATUS.HAS_GET)
    end
end