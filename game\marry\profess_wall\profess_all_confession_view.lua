function ProfessView:LoadAllConfessionViewCallBack()
    self.select_toggle = 1
    if not self.pw_all_list then
        self.pw_all_list = AsyncBaseGrid.New()
        local bundle = "uis/view/marry_ui_prefab"
		local asset = "profess_note_all_item"
        self.pw_all_list:CreateCells({col = 3, change_cells_num = 1, list_view = self.node_list["pw_all_list"],
            assetBundle = bundle, assetName = asset, itemRender = ProfessWallInfoAllCellRender})
        self.pw_all_list:SetStartZeroIndex(false)
    end

    XUI.AddClickEventListener(self.node_list.btn_pw_all_qiuhun, BindTool.Bind(self.OpenSelectPWAllProfess, self))
    for i = 1, 3 do
		self.node_list["toggle_" .. i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickToggle, self, i))
	end

    self.node_list["toggle_1"].toggle.isOn = true
end

function ProfessView:ShowAllConfessionViewCallBack()

end

function ProfessView:ReleaseAllConfessionViewCallBack()
    if self.pw_all_list then
        self.pw_all_list:DeleteMe()
        self.pw_all_list = nil
    end
end

function ProfessView:OnClickToggle(index, is_on)
	if self.select_toggle ~= index and is_on then
		self.select_toggle = index

        if index == 1 then
            ServerActivityWGCtrl.Instance:CSGetProfessInfo(TIANYAN_MIYU_ENUM.PROFESS_REQ_TYPE_GLOBAL_ALL)
            self:ShowAllConfessionViewCallBack()
        elseif index == 2 then
            ServerActivityWGCtrl.Instance:CSGetProfessInfo(TIANYAN_MIYU_ENUM.PROFESS_REQ_TYPE_PASSIVE)
            self:ShowToMyConfessionViewCallBack()
        elseif index == 3 then
            ServerActivityWGCtrl.Instance:CSGetProfessInfo(TIANYAN_MIYU_ENUM.PROFESS_REQ_TYPE_ACTIVE)
            self:ShowMyConfessionViewCallBack()
        end

		--self:Flush()
	end
end

function ProfessView:OnFlushAllConfessionViewCallBack()
    local _, temp_data = {}, {}
    if self.select_toggle == 1 then
        _, temp_data = ActivePerfertQingrenWGData.Instance:GetAllProfessInfo()
    else
        _, temp_data = ActivePerfertQingrenWGData.Instance:GetPersonalInfo()
    end

    local no_data = IsEmptyTable(temp_data)
    self.pw_all_list:SetDataList(temp_data)
    self.node_list.pw_all_no_data:CustomSetActive(no_data)
    self.node_list.pw_all_list:CustomSetActive(not no_data)
end

function ProfessView:OpenSelectPWAllProfess()
	ViewManager.Instance:Open(GuideModuleName.ChooseProfessView)
end

------------------------ProfessWallInfoAllCellRender---------------------------
ProfessWallInfoAllCellRender = ProfessWallInfoAllCellRender or BaseClass(BaseRender)

function ProfessWallInfoAllCellRender:LoadCallBack()
    if not self.head_cell then
        self.head_cell = BaseHeadCell.New(self.node_list["head_pos"])
    end

    self.role_avatar = RoleHeadCell.New(false)

    XUI.AddClickEventListener(self.node_list["profess_icon"], BindTool.Bind(self.OnClickProffIcon, self))
    XUI.AddClickEventListener(self.node_list["head_pos"], BindTool.Bind(self.ShowOtherPlayerInfo, self))
end

function ProfessWallInfoAllCellRender:__delete()
    if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end

    if nil ~= self.role_avatar then
		self.role_avatar:DeleteMe()
		self.role_avatar = nil
    end
end

function ProfessWallInfoAllCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    if nil == self.data.profess_type or self.data.profess_type == 0 then
		self.node_list["FromeText"].text.text = self.data.professor or self.data.role_id_from --表白者
	    self.node_list["ToText"].text.text = Language.Marry.PW_To .. self.data.be_professor --被.表白者
	else
		self.node_list["FromeText"].text.text = self.data.be_professor or self.data.role_id_from --表白者
	    self.node_list["ToText"].text.text = Language.Marry.PW_To .. self.data.professor --被.表白者
	end
	self.node_list["ContentText"].text.text = self.data.content  --表白内容

    local gift_type_cfg = ProfessWallWGData.Instance:GetProfessGiftTypeInfoByType(self.data.gift_type)
    if not IsEmptyTable(gift_type_cfg) then
        local bundle, asset = ResPath.GetItem(gift_type_cfg.gift_id)
        self.node_list["profess_icon"].image:LoadSprite(bundle, asset) 
    end

	local time_list = os.date("%Y-%m-%d", self.data.profess_time)
	self.node_list["Time"].text.text = time_list

    self:SetHeadCell()
end

--点击他人头像信息
function ProfessWallInfoAllCellRender:ShowOtherPlayerInfo()
	if self.data == nil then
		return
	end

    local node = self.node_list["head_pos"]

    BrowseWGCtrl.Instance:BrowRoelInfo(self.data.role_id_from, function(param_protocol)
        if self.data == nil then
            return
        end

        if self.role_avatar then
            local role_info = {
                role_id = param_protocol.role_id,
                role_name = self.data.username,
                prof = param_protocol.prof,
                sex = param_protocol.sex,
                is_online = 0 ~= param_protocol.is_online,
                plat_type = self.data.origin_plat_type,
                server_id = self.data.merge_server_id,
                role_level = param_protocol.level,
            }
            self.role_avatar:SetRoleInfo(role_info)
        end
        self.role_avatar:OpenMenu(nil, self:GetMenuNodePos(node), nil, MASK_BG_ALPHA_TYPE.Normal)
    end)
end

function ProfessWallInfoAllCellRender:GetMenuNodePos(node)
    if nil == node then return nil end
    local main_view = ViewManager.Instance:GetView(GuideModuleName.MainUIView)
	if nil == main_view or not main_view:IsOpen() then
		return
	end
    local parent_rect= main_view.root_node.transform:GetComponent(typeof(UnityEngine.RectTransform))
    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, node.transform.position)
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, Vector2(0, 0))

    local x = node.transform.position.x * 10
    local y = local_position_tbl.y

    y = y - 70
    y = math.max(y, -144)
    return Vector2(x, y)
end

function ProfessWallInfoAllCellRender:SetHeadCell()
    local bundle = "uis/view/marry_ui/images_atlas"
    local asset = "a3_qy_tq_yd"
    if self.data.role_id_from and self.data.role_id_from > 0 then
        local flush_fun = function (protocol)
            local data = {role_id = protocol.role_id, prof = protocol.prof, sex = protocol.sex, fashion_photoframe = 0}
            self.head_cell:SetData(data)
            self.head_cell:ChangeBg(bundle, asset, true)
        end

        BrowseWGCtrl.Instance:BrowRoelInfo(self.data.role_id_from, flush_fun, RoleWGData.Instance.role_vo.plat_type)
    else
        local data = {}
        data.role_id = RoleWGData.Instance:GetOriginUid()
        data.prof = RoleWGData.Instance:GetRoleProf()
        data.sex = RoleWGData.Instance:GetRoleSex()
        self.head_cell:SetData(data)
        self.head_cell:ChangeBg(bundle, asset, true)
    end
end

function ProfessWallInfoAllCellRender:OnClickProffIcon()
    local gift_type_cfg = ProfessWallWGData.Instance:GetProfessGiftTypeInfoByType(self.data.gift_type)

    if not IsEmptyTable(gift_type_cfg) then
        TipWGCtrl.Instance:OpenItem({item_id = gift_type_cfg.gift_id})
    end
end