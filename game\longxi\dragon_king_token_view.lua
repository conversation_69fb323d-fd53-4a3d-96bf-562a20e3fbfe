-- 龙王令牌
LongXiView = LongXiView or BaseClass(SafeBaseView)

local TOKEN_INIT_POS = Vector3(-71, 35, 0)
local dk_tween_up_height = 30
local DK_ONE_KEY_UP_DELAY = 0.2

function LongXiView:InitDragonKingTokenView()
    if not self.dk_reward_list then
        self.dk_reward_list = AsyncListView.New(ItemCell, self.node_list.dk_reward_list)
        self.dk_reward_list:SetStartZeroIndex(true)
    end

    if not self.dk_cost_item then
        self.dk_cost_item = ItemCell.New(self.node_list.dk_cost_item_pos)
    end
    self.dk_cost_item.OnClick = function ()
		-- local has_weekcard = RechargeWGData.Instance:IsHasWeekCard()
        -- if not has_weekcard then
        --     ViewManager.Instance:Open(GuideModuleName.PrivilegeShop, TabIndex.SeasonPrivilegeShop)
		-- 	self:Close()
        -- else
        --     TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.dk_cost_item.data.item_id})
        -- end
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.dk_cost_item.data.item_id})
	end
    self.dk_cost_item:ListenClick()

    if not self.dk_special_item then
        self.dk_special_item = ItemCell.New(self.node_list.dk_special_item)
        self.dk_special_item:SetClickCallBack(BindTool.Bind(self.OnDkClickSpecialItem, self))
        self.dk_special_item:SetIsShowTips(false)
    end

    if not self.dk_attr_list then
        self.dk_attr_list = AsyncBaseGrid.New()
        local bundle = "uis/view/long_xi_ui_prefab"
		local asset = "attr_list_item"
		self.dk_attr_list:CreateCells({col = 1, change_cells_num = 1, list_view = self.node_list.dk_attr_list,
			assetBundle = bundle, assetName = asset, itemRender = LongXiAttrItemRender})
		self.dk_attr_list:SetStartZeroIndex(false)
    end

    XUI.AddClickEventListener(self.node_list.dk_btn_bug, BindTool.Bind(self.OnDkClickBugBtn, self))
    XUI.AddClickEventListener(self.node_list.dk_btn_task, BindTool.Bind(self.OnClickTaskBtn, self))
    XUI.AddClickEventListener(self.node_list.dk_skill_icon, BindTool.Bind(self.OnDkSKillClick, self))
    XUI.AddClickEventListener(self.node_list.dk_up_skill_icon, BindTool.Bind(self.OnDkSKillClick, self))
    XUI.AddClickEventListener(self.node_list.dk_btn_up, BindTool.Bind(self.OnDkUpBtnClick, self))
    XUI.AddClickEventListener(self.node_list.dk_icon_active, BindTool.Bind(self.OnOpenItemTips, self))
    XUI.AddClickEventListener(self.node_list.dk_jiantou_go_btn_a, BindTool.Bind(self.OnJumpToGetWay, self, 1))
    XUI.AddClickEventListener(self.node_list.dk_jiantou_go_btn_b, BindTool.Bind(self.OnJumpToGetWay, self, 2))
    XUI.AddClickEventListener(self.node_list.dk_jiantou_go_btn_c, BindTool.Bind(self.OnJumpToGetWay, self, 3))
    self:DkLoadModel()

    -- local task_cfg = LongXiWGData.Instance:GetTaskCfg()
    -- local item_num = tonumber(task_cfg.cost_item_num)
    -- for i = 1, item_num do
    --     if self.node_list["dk_dulongfu_" .. i] then
    --         self.node_list["dk_dulongfu_" .. i]:SetActive(true)
    --         XUI.SetGraphicGrey(self.node_list["dk_dulongfu_" .. i], true)
    --     end
    -- end

    --self.node_list.dk_recharge_desc:SetActive(LongXiWGData.Instance:GetRMBByAddRechargeFlag())
end

function LongXiView:ReleaseDragonKingTokenView()
    if self.dk_reward_list then
        self.dk_reward_list:DeleteMe()
        self.dk_reward_list = nil
    end

    if self.dk_cost_item then
        self.dk_cost_item:DeleteMe()
        self.dk_cost_item = nil
    end

    if self.dk_attr_list then
        self.dk_attr_list:DeleteMe()
        self.dk_attr_list = nil
    end

    if self.dk_model_display then
        self.dk_model_display:DeleteMe()
        self.dk_model_display = nil
    end

    if self.dk_special_item then
        self.dk_special_item:DeleteMe()
        self.dk_special_item = nil
    end

    self:DkClearLongXiCountDown()
    self:DkCancelTween()
end


function LongXiView:ShowIndexDragonKingTokenView()
    self:PlayTweenDragonKingToken()

    self.node_list.dk_btn_text.text.text = Language.LongXi.OneKeyUp
end


function LongXiView:OnOpenItemTips()
    local task_cfg = LongXiWGData.Instance:GetTaskCfg()
    if task_cfg.cost_item_id > 0 then
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = task_cfg.cost_item_id}) 
    end
end

function LongXiView:OnJumpToGetWay(index)
    local task_cfg = LongXiWGData.Instance:GetTaskCfg()
    if task_cfg["open_param" .. index] ~= nil then
        FunOpen.Instance:OpenViewNameByCfg(task_cfg["open_param" .. index])
    end
end

function LongXiView:DkLoadModel()
    if self.dk_model_display == nil then
        self.dk_model_display = OperationActRender.New(self.node_list["dk_model_root"])
    end

    local base_data = LongXiWGData.Instance:GetLongXiBaseInfoByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
    if base_data and base_data["model_bundle_name"] and base_data["model_asset_name"] then
        local data = {}
        --data.item_id = base_data.show_id
        data.bundle_name = base_data["model_bundle_name"]
        data.asset_name = base_data["model_asset_name"]
        data.render_type = OARenderType.RoleModel
        data.model_rt_type = ModelRTSCaleType.L

        if data.display_pos and data.display_pos ~= "" then
            local pos_list = string.split(data.display_pos, "|")
            local pos_x = tonumber(pos_list[1]) or 0
            local pos_y = tonumber(pos_list[2]) or 0
            local pos_z = tonumber(pos_list[3]) or 0

            data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
        end

        if data.rotation and data.rotation ~= "" then
            local rot_list = string.split(data.rotation, "|")
            local rot_x = tonumber(rot_list[1]) or 0
            local rot_y = tonumber(rot_list[2]) or 0
            local rot_z = tonumber(rot_list[3]) or 0

            data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
        end

        data.model_adjust_root_local_scale = data.display_scale

        self.dk_model_display:SetData(data)
    end
end

function LongXiView:PlayTweenDragonKingToken()
	if not self.tween then
		local tween_root = self.node_list["dk_token"].rect
        tween_root.anchoredPosition = TOKEN_INIT_POS
		self.tween = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + dk_tween_up_height, 1)
		self.tween:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	else
		self.tween:Restart()
	end
end

function LongXiView:DkCancelTween()
	if self.tween then
        self.tween:Kill()
        self.tween = nil
        local tween_root = self.node_list["dk_token"]

        if tween_root then
            tween_root.rect.anchoredPosition = TOKEN_INIT_POS
        end
	end
end

function LongXiView:FlushDragonKingTokenView()
    local data = LongXiWGData.Instance:GetLongXiDataByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)

    if IsEmptyTable(data) then
        return
    end

    local active = data.is_active ~= 0
    self.node_list.dk_active_panel:SetActive(not active)
    self.node_list.dk_up_level_panel:SetActive(active)
    local base_data = LongXiWGData.Instance:GetLongXiBaseInfoByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
    local base_attr_list, base_capability = ItemShowWGData.Instance:OutStrAttrListAndCapalityByAttrId(base_data, "attr_id", "attr_value")

    if active then
        self:DkClearLongXiCountDown()

        local bundle, asset = ResPath.GetSkillIconById(base_data.skill_ico)
        self.node_list.dk_up_skill_icon.image:LoadSprite(bundle, asset, function()
            self.node_list.dk_up_skill_icon.image:SetNativeSize()
        end)

        local is_max_level, attr_list, cap, skill_info = LongXiWGData.Instance:GetUpLevelAttrInfo(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
        self.node_list.dk_skill_name.text.text = base_data.skill_name
        
        -- if not IsEmptyTable(skill_info) then
        --     self.node_list.skill_desc.text.text = skill_info.attr_name .. " " .. ToColorStr(skill_info.value_str, COLOR3B.D_GREEN)
        -- end
        --self.node_list.dk_skill_desc.text.text = base_data.skill_des

        self.node_list.dk_up_max_flag:SetActive(is_max_level)
        self.node_list.dk_btn_up:SetActive(not is_max_level)
        self.node_list.dk_up_cap_value.text.text = cap + base_capability
        self.node_list.dk_level.text.text = string.format(Language.LongXi.Level, data.level)
        self.dk_attr_list:SetDataList(attr_list)

        self:FlushDkCostItem()
    else
        --self.node_list.active_add_cap.text.text = base_data.capability_inc
        self.node_list.dk_active_cap_value.text.text = base_capability
        --self.node_list.dk_active_add_cap.text.text = base_capability

        -- for i = 1, 4 do
        --     self.node_list["dk_active_attr" .. i].text.text = base_attr_list[i].attr_name .. " " .. ToColorStr(base_attr_list[i].value_str, COLOR3B.D_GREEN) 
        -- end

        local price = RoleWGData.GetPayMoneyStr(base_data.price, base_data.rmb_type, base_data.rmb_seq)
        self.node_list.dk_btn_gold_text.text.text = price .. Language.LongXi.TimeLimitDesc

        local bundle, asset = ResPath.GetSkillIconById(base_data.skill_ico)
        self.node_list.dk_skill_icon.image:LoadSprite(bundle, asset, function()
            self.node_list.dk_skill_icon.image:SetNativeSize()
        end)

        self.dk_special_item:SetData({item_id = base_data.show_item_id})
        self.dk_reward_list:SetDataList(base_data.reward_item)

        self:FlushDkCountDown()

    end

    self:FlushTaskInfo()
end

function LongXiView:FlushTaskInfo()
    local task_cfg = LongXiWGData.Instance:GetTaskCfg()
    local role_level = RoleWGData.Instance:GetRoleLevel()
    local boss_count = LongXiWGData.Instance:GetBossKillCount()
    local item_num = ItemWGData.Instance:GetItemNumInBagById(task_cfg.cost_item_id)
    local a_color = role_level >= tonumber(task_cfg.need_level) and "#8edf8e" or COLOR3B.RED
    local b_color = boss_count >= tonumber(task_cfg.need_kill_world_boss_num) and "#8edf8e" or COLOR3B.RED
    local c_color = item_num >= tonumber(task_cfg.cost_item_num) and "#8edf8e" or COLOR3B.RED

    local a_str = ToColorStr(role_level .. "/" .. task_cfg.need_level, a_color)
    local b_str = ToColorStr(boss_count .. "/" .. task_cfg.need_kill_world_boss_num, b_color)
    local c_str = ToColorStr(item_num .. "/" .. task_cfg.cost_item_num, c_color)

    self.node_list["dk_task_desc_a"].text.text = string.format(Language.LongXi.TaskA, a_str)
    self.node_list["dk_task_desc_b"].text.text = string.format(Language.LongXi.TaskB, b_str)
    self.node_list["dk_task_desc_c"].text.text = string.format(Language.LongXi.TaskC, c_str)

    -- local icon_num = item_num >= tonumber(task_cfg.cost_item_num) and tonumber(task_cfg.cost_item_num) or item_num
    -- for i = 1, icon_num do
    --     XUI.SetGraphicGrey(self.node_list["dk_dulongfu_" .. i], false)
    -- end

end

function LongXiView:FlushDkCostItem()
    local cost_cfg = LongXiWGData.Instance:GetLongXiLevelInfoByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)

    if not IsEmptyTable(cost_cfg) then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(cost_cfg.cost_item_id)
        local enough = item_num >= cost_cfg.cost_item_num
    
        self.dk_cost_item:SetFlushCallBack(function ()
            local right_text = ToColorStr(item_num .. "/" .. cost_cfg.cost_item_num, enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
            self.dk_cost_item:SetRightBottomColorText(right_text)
            self.dk_cost_item:SetRightBottomTextVisible(true)
        end)
    
        self.dk_cost_item:SetData({item_id = cost_cfg.cost_item_id, num = cost_cfg.cost_item_num, is_bind = 0})
    end
end

function LongXiView:OnDkClickBugBtn()
    local base_data = LongXiWGData.Instance:GetLongXiBaseInfoByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
    RechargeWGCtrl.Instance:Recharge(base_data.price, base_data.rmb_type, base_data.rmb_seq)
end

function LongXiView:OnDkClickSpecialItem()
    LongXiWGCtrl.Instance:OpenLongXiItemTips(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
end

function LongXiView:OnClickTaskBtn()
    local task_active = LongXiWGData.Instance:GetLongXiTaskInfo()
    if not task_active then
        TipWGCtrl.Instance:ShowSystemMsg(Language.LongXi.TaskError)
        return
    end

    LongXiWGCtrl.Instance:SendLongXiRequest(LONGXI_OPERATE_TYPE.LONGXI_OPERATE_TYPE_TASK_FINISH, LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
    -- local base_data = LongXiWGData.Instance:GetLongXiBaseInfoByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
    -- RechargeWGCtrl.Instance:Recharge(base_data.price, base_data.rmb_type, base_data.rmb_seq)
end

function LongXiView:FlushDkCountDown()
	self:DkClearLongXiCountDown()
	local count_down_time = LongXiWGData.Instance:GetLongXiCountDownTime()
    local task_active = LongXiWGData.Instance:GetLongXiTaskInfo()
    self.node_list["dk_btn_bug"]:SetActive(true)
    -- self.node_list["btn_task"]:SetActive(not (count_down_time > 0) or task_active)
    self.node_list["dk_btn_task"]:SetActive(true)
    self.node_list["dk_btn_task_text"].text.text = task_active and Language.LongXi.BtnActive or Language.LongXi.TaskActive
    self.node_list["dk_task_red"]:SetActive(task_active)

	if count_down_time > 0 then
		self:UpdateDkCountDown(0, count_down_time)
        local interval = 1
        self.timer = CountDown.Instance:AddCountDown(count_down_time, interval,
        function(elapse_time, total_time)
            self:UpdateDkCountDown(elapse_time, total_time)
        end,
        function()
            if self.node_list.dk_active_time then
                self.node_list.dk_active_time.text.text = ""
                self.node_list["dk_active_time"]:SetActive(false)
            end
        end)
    end
end

function LongXiView:UpdateDkCountDown(elapse_time, total_time)
    if self.node_list.dk_active_time then
        local time = TimeUtil.FormatSecondDHM8(total_time - elapse_time)
        self.node_list.dk_active_time.text.text = string.format(Language.LongXi.CountDownTime, time)
        self.node_list["dk_active_time"]:SetActive(true)
    end
end

function LongXiView:DkClearLongXiCountDown()
    if self.timer and CountDown.Instance:HasCountDown(self.timer) then
        CountDown.Instance:RemoveCountDown(self.timer)
        self.timer = nil
    end
end

function LongXiView:OnDkSKillClick()
    local show_data = LongXiWGData.Instance:GetSkillInfo(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
    NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

function LongXiView:OnDkUpBtnClick()
    if self:DkIsCanUp() then
        if LongXiWGData.Instance:GetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN) then
            self.node_list.dk_btn_text.text.text = Language.LongXi.OneKeyUp
            LongXiWGData.Instance:SetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN, false)
        else
            LongXiWGData.Instance:SetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN, true)
            self:DkUpLevel()
            self.node_list.dk_btn_text.text.text = Language.LongXi.Stop
        end
    else
        TipWGCtrl.Instance:ShowSystemMsg(Language.LongXi.UpGoodsNotEnough)
    end
end

function LongXiView:DkUpLevel()
    local base_data = LongXiWGData.Instance:GetLongXiBaseInfoByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
    LongXiWGCtrl.Instance:SendLongXiRequest(LONGXI_OPERATE_TYPE.LONGXI_OPERATE_TYPE_LEVEL_UP, base_data.seq)
end

function LongXiView:DkIsCanUp()
    local cost_cfg = LongXiWGData.Instance:GetLongXiLevelInfoByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
    if not IsEmptyTable(cost_cfg) then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(cost_cfg.cost_item_id)
        return item_num >= cost_cfg.cost_item_num
    end

    return false
end

function LongXiView:DkStartUpLevelAni()
    TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji,
    is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["dk_effect_root"]})

    if self:DkIsCanUp() then
        if LongXiWGData.Instance:GetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN) then
            ReDelayCall(self, function()
                self:DkUpLevel()
            end, DK_ONE_KEY_UP_DELAY, "super_dragon_seal")
        end
    else
        LongXiWGData.Instance:SetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN, false)
        self.node_list.dk_btn_text.text.text = Language.LongXi.OneKeyUp
    end
end