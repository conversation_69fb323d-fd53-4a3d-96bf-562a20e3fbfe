FuBenRuneTowerTaskView = FuBenRuneTowerTaskView or BaseClass(SafeBaseView)

function FuBenRuneTowerTaskView:__init()
	self.is_safe_area_adapter = true
	self.view_name = "FuBenRuneTowerTaskView"
    self.view_layer = UiLayer.MainUIHigh
    self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
    self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_fuben_rune_tower_task_info")
end

function FuBenRuneTowerTaskView:OpenCallBack()
	if self.obj and self:IsLoadedIndex(0) then
        self.obj:SetActive(true)
    end
end

function FuBenRuneTowerTaskView:CloseCallBack()
	self.is_out_fb = true
    if self.obj then
        self.obj:SetActive(false)
    end
end

function FuBenRuneTowerTaskView:LoadCallBack()
    MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))

    if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
		self.reward_list:SetStartZeroIndex(true)
	end

    if not self.rank_list then
        self.rank_list = AsyncListView.New(FuBenRuneTowerRankCell, self.node_list["rank_list"])
    end
end

function FuBenRuneTowerTaskView:InitCallBack()
	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()
	if self.node_list["task_root"] then
		self.obj = self.node_list["task_root"].gameObject
		self.obj.transform:SetParent(parent.gameObject.transform)
		self.obj.transform.localPosition = Vector3(0, 0, 0)
		self.obj.transform.localScale = Vector3.one
	end

    if self.is_out_fb then
        self.obj:SetActive(false)
    end

    self.is_out_fb = nil
end

function FuBenRuneTowerTaskView:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("rune_tower_time") then
        CountDownManager.Instance:RemoveCountDown("rune_tower_time")
    end
    
    if CountDownManager.Instance:HasCountDown("fuben_rune_tower_alert_time") then
        CountDownManager.Instance:RemoveCountDown("fuben_rune_tower_alert_time")
    end

	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end

    if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

    if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end
end

function FuBenRuneTowerTaskView:ShowIndexCallBack()
	if self.obj then
        self.obj:SetActive(true)
    end
end

function FuBenRuneTowerTaskView:OnFlush(param_t, index)
    for k, v in pairs(param_t) do
		if k == "all" then
			local scene_id = Scene.Instance:GetSceneId()
            local scene_fuben_cfg = FuBenTeamCommonTowerWGData.Instance:GetFuBenSceneCfg(scene_id)
            if not scene_fuben_cfg then
                return
            end

            local fb_scene_info = FuBenTeamCommonTowerWGData.Instance:GetTeamCommonTowerFBInfo(scene_fuben_cfg.seq)
            if not fb_scene_info then
                return
            end

            self:FlushRewardInfo(scene_fuben_cfg)
            self:FlushMonsterNum(fb_scene_info)
            self:FlushTimeCount(fb_scene_info.fb_end_time)
            self:FlushRankList()
		elseif k == "rank_info" then
            self:FlushRankList()
		end
	end
end

function FuBenRuneTowerTaskView:FlushRewardInfo(scene_fuben_cfg)
    local fb_scene_info = FuBenTeamCommonTowerWGData.Instance:GetTeamCommonTowerFBInfo(scene_fuben_cfg and scene_fuben_cfg.seq)
    if not fb_scene_info then
        return
    end

    local level_cfg = FuBenTeamCommonTowerWGData.Instance:GetLevelCfgByLevel(fb_scene_info.grade, fb_scene_info.level)
    if not level_cfg then
        return
    end

    self.reward_list:SetDataList(level_cfg.pass_reward_item)
end

function FuBenRuneTowerTaskView:FlushMonsterNum(fb_scene_info)
    local last_wave_monster_num = fb_scene_info.last_wave_monster_num
    local monster_cfg = FuBenTeamCommonTowerWGData.Instance:GetMonsterCfgByWave(fb_scene_info.grade, fb_scene_info.level, fb_scene_info.wava)
    local all_monster_num = #monster_cfg
    self.node_list.monter_num.text.text = string.format(Language.RuneTower.MonsterNum, last_wave_monster_num, all_monster_num)

    local all_level_cfg = FuBenTeamCommonTowerWGData.Instance:GetLevelCfgByLevel(fb_scene_info.grade)
    local str = string.format("(%s/%s)",fb_scene_info.level, #all_level_cfg)
    self.node_list.fuben_name.text.text = string.format("%s%s", Language.RuneTower.FBName, ToColorStr(str, "#99FFBB"))
end

function FuBenRuneTowerTaskView:FlushTimeCount(fb_end_time)
    local server_time = TimeWGCtrl.Instance:GetServerTime()

    local time = fb_end_time - server_time
    if time > 0 then
        if CountDownManager.Instance:HasCountDown("rune_tower_time") then
            CountDownManager.Instance:RemoveCountDown("rune_tower_time")
        end

        CountDownManager.Instance:AddCountDown("rune_tower_time", 
            BindTool.Bind(self.FinalUpdateTimeCallBack, self), 
            BindTool.Bind(self.OnComplete, self), 
            nil, time, 1)
    else
        self:OnComplete()
    end
end

function FuBenRuneTowerTaskView:FinalUpdateTimeCallBack(now_time, total_time)
    local time = math.ceil(total_time - now_time)
    local time_str = TimeUtil.FormatSecondDHM9(time)
    self.node_list["end_time"].text.text = string.format(Language.RuneTower.EndTime, time_str) 

    local other_cfg = FuBenTeamCommonTowerWGData.Instance:GetFuBenOtherCfg()
    self.node_list.top_time:SetActive(time <= other_cfg.alert_time)
    if time <= other_cfg.alert_time then
        self:FlushAlertTime(time)
    end
end

function FuBenRuneTowerTaskView:OnComplete()
    self.node_list.end_time.text.text = string.format(Language.RuneTower.EndTime, 0) 
    self.node_list.top_time:SetActive(false)
end

function FuBenRuneTowerTaskView:FlushRankList()
    local hurt_list = FuBenTeamCommonTowerWGData.Instance:GetAllTeamCommonTowerHurtInfo()
    if not IsEmptyTable(hurt_list) then
        self.rank_list:SetDataList(hurt_list)
        local my_hurt_count = 0
        for k, v in pairs(hurt_list) do
            if v.uuid == RoleWGData.Instance:GetUUid() then
                my_hurt_count = v.hurt_count
                break
            end
        end
        self.node_list.my_damate.text.text = CommonDataManager.ConverNumber(my_hurt_count)
        self.node_list.per_bg.slider.value = my_hurt_count / FuBenTeamCommonTowerWGData.Instance:GetNormalHurtInfoMaxValue()
    else
        self.node_list.my_damate.text.text = 0
        self.node_list.per_bg.slider.value = 0
    end
end

function FuBenRuneTowerTaskView:FlushAlertTime(next_star_change_time)
    if CountDownManager.Instance:HasCountDown("fuben_rune_tower_alert_time") then
        CountDownManager.Instance:RemoveCountDown("fuben_rune_tower_alert_time")
    end

    if next_star_change_time > 0 then
        CountDownManager.Instance:AddCountDown("fuben_rune_tower_alert_time", 
        BindTool.Bind(self.FinalUpdateAlertTimeCallBack, self), 
        BindTool.Bind(self.OnCompleteAlertTimeCallBack, self), 
        nil, next_star_change_time, 0.05)
    else
        self:OnCompleteAlertTimeCallBack()
    end
end

function FuBenRuneTowerTaskView:FinalUpdateAlertTimeCallBack(now_time, total_time)
    local time_tab = TimeUtil.Format2TableDHM3(total_time - now_time)
    self.node_list.alert_time.text.text = string.format(string.format("%02d:%02d:%02d",time_tab.min, time_tab.sec, time_tab.milliseccond / 10))
end

function FuBenRuneTowerTaskView:OnCompleteAlertTimeCallBack()
    self.node_list.alert_time.text.text = "00:00:00"
end
-------------------------FuBenRuneTowerRankCell-----------------
FuBenRuneTowerRankCell = FuBenRuneTowerRankCell or BaseClass(BaseRender)
function FuBenRuneTowerRankCell:__init()
end

function FuBenRuneTowerRankCell:__delete()

end

function FuBenRuneTowerRankCell:OnFlush()
    if not self.data then
        return
    end

    self.node_list.name.text.text = self.data.role_name

    local damage = CommonDataManager.ConverNumber(self.data.hurt_count)
	self.node_list.damage.text.text = damage
    self.node_list.per_bg.slider.value = self.data.hurt_count / FuBenTeamCommonTowerWGData.Instance:GetNormalHurtInfoMaxValue()

    local bg_bundle, bg_asset = ResPath.GetCommonImages("a3_hurt_list_bg_4")

	if self.index < 4 then
        bg_bundle, bg_asset = ResPath.GetCommonImages("a3_hurt_list_bg_" .. self.index)
		self.node_list["icon"]:SetActive(true)
		self.node_list["icon"].image:LoadSprite(ResPath.GetCommonImages("a3_hurt_list_rank_" .. self.index))
		self.node_list.num.text.text = ""
	else
		self.node_list["icon"]:SetActive(false)
		self.node_list.num.text.text = self.index
    end

    self.node_list.per_bg.image:LoadSprite(bg_bundle, bg_asset)
end
