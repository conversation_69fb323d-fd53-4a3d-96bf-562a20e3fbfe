EquipmentBSJLOverview = EquipmentBSJLOverview or BaseClass(SafeBaseView)

function EquipmentBSJLOverview:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 14),sizeDelta = Vector2(1080, 614)})
	self:AddViewResource(0, "uis/view/equipment_ui_prefab", "layout_equipment_bsjl_overview")
end

function EquipmentBSJLOverview:LoadCallBack()
    if not self.equipment_bsjl_equip_body_list then
        self.equipment_bsjl_equip_body_list = AsyncListView.New(BSJLEquipBodyOverviewItemCellRender, self.node_list.equipment_bsjl_equip_body_list)
    end

    self.node_list.title_view_name.text.text = Language.Equipment.BSJLOverviewTitle
end

function EquipmentBSJLOverview:ReleaseCallBack()
    if self.equipment_bsjl_equip_body_list then
        self.equipment_bsjl_equip_body_list:DeleteMe()
        self.equipment_bsjl_equip_body_list = nil
    end
end

function EquipmentBSJLOverview:OnFlush()
    local data_list = EquipBodyWGData.Instance:GetDZYPJLEquipBodyDataList()
    self.equipment_bsjl_equip_body_list:SetDataList(data_list)
end

---------------------------------BSJLEquipBodyOverviewItemCellRender----------------------------------
BSJLEquipBodyOverviewItemCellRender = BSJLEquipBodyOverviewItemCellRender or BaseClass(BaseRender)

function BSJLEquipBodyOverviewItemCellRender:LoadCallBack()
    self.default_select_part = -1
    self.equip_body_seq_cache = -1
    self.default_select_part_data = {}

    if not self.bsjl_equip_list then
        self.bsjl_equip_list = AsyncListView.New(BSJLOverviewEquipItemCellRender, self.node_list.bsjl_equip_list)
        self.bsjl_equip_list:SetSelectCallBack(BindTool.Bind(self.OnSelectEquipHandler, self))
    end

    XUI.AddClickEventListener(self.node_list["btn_to_equip_suit"], BindTool.Bind(self.OnClickToEquipSuitBtn, self))
    XUI.AddClickEventListener(self.node_list["btn_bsjl"], BindTool.Bind(self.OnClickBSJLBtn, self)) 
end

function BSJLEquipBodyOverviewItemCellRender:__delete()
    if self.bsjl_equip_list then
        self.bsjl_equip_list:DeleteMe()
        self.bsjl_equip_list = nil
    end
end

function BSJLEquipBodyOverviewItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    if self.equip_body_seq_cache ~= self.data.seq then
        self.default_select_part = -1
        self.default_select_part_data = {}
    end

    self.equip_body_seq_cache = self.data.seq

    local unlock = EquipBodyWGData.Instance:IsEquipBodyUnLock(self.data.seq)
    local is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyWearEquip(self.data.seq)
    self.node_list.unlock:CustomSetActive(not unlock or not is_wear_equip)
    self.node_list.bsjl_equip_list:CustomSetActive(unlock and is_wear_equip)
    -- self.node_list.btn_bsjl:CustomSetActive(unlock and is_wear_equip)
    self.node_list.name.text.text = self.data.name
    self.node_list.desc_unlock.text.text = not unlock and Language.RoleEquipBody.EquipBodyLock or Language.RoleEquipBody.EquipBodyNotWearEquip

    if unlock then
        local bsjl_equip_list = EquipmentWGData.Instance:GetEquipBaoShiShowList(self.data.seq)
        self.bsjl_equip_list:SetDataList(bsjl_equip_list)
    end
end

function BSJLEquipBodyOverviewItemCellRender:OnSelectEquipHandler(cell)
    if nil == cell or IsEmptyTable(cell.data) then
        return
    end
    
    local refine_level = EquipmentWGData.Instance:GetStoneRefineLevelByPart(cell.data.index)
    local level_str = refine_level > 0 and refine_level or 0
    self.node_list.desc_btn_bsjl.text.text = string.format(Language.Equipment.BSJLOverviewBtn, level_str)

    self.default_select_part_data = cell.data
end

function BSJLEquipBodyOverviewItemCellRender:OnClickToEquipSuitBtn()
    local unlock = EquipBodyWGData.Instance:IsEquipBodyUnLock(self.data.seq)
    local is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyWearEquip(self.data.seq)

    if not unlock or not is_wear_equip then
        local str = not unlock and Language.RoleEquipBody.EquipBodyLock or Language.RoleEquipBody.EquipBodyNotWearEquip
        SysMsgWGCtrl.Instance:ErrorRemind(str)
        return
    end

    if unlock and is_wear_equip then
        EquipmentWGCtrl.Instance:Flush(TabIndex.equipment_baoshi_jl, "equip_bsjl_change_to_equip_body", {equip_body_seq = self.data.seq, selct_part_data = self.default_select_part_data})
        EquipmentWGCtrl.Instance:CloseBSJLOverviewView()
    end
end

function BSJLEquipBodyOverviewItemCellRender:OnClickBSJLBtn()
    local unlock = EquipBodyWGData.Instance:IsEquipBodyUnLock(self.data.seq)
    local is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyWearEquip(self.data.seq)

    if unlock and is_wear_equip then
        EquipmentWGCtrl.Instance:Flush(TabIndex.equipment_baoshi_jl, "equip_bsjl_change_to_equip_body", {equip_body_seq = self.data.seq})
        EquipmentWGCtrl.Instance:CloseBSJLOverviewView()
    end
end

--------------------------------BSJLOverviewEquipItemCellRender-------------------------------
BSJLOverviewEquipItemCellRender = BSJLOverviewEquipItemCellRender or BaseClass(BaseRender)

function BSJLOverviewEquipItemCellRender:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["ph_item"])
        self.item_cell:SetIsShowTips(false)
    end
end

function BSJLOverviewEquipItemCellRender:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function BSJLOverviewEquipItemCellRender:OnFlush()
	if nil == self.data or nil == self.data.param then
		return
	end

    if self.item_cell and self.data.item_id ~= self.old_item_id then
		self.item_cell:SetData(self.data)
        self.item_cell:SetRightTopImageTextActive(false)
	end

	self.old_item_id = self.data.item_id

    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if item_cfg then
		local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
        local refine_level = EquipmentWGData.Instance:GetStoneRefineLevelByPart(self.data.index)
        local level_str = refine_level > 0 and string.format(Language.Equipment.BSJLOverviewLevel, refine_level) or ""
        item_name = item_name .. level_str

		self.node_list["lbl_name"].text.text = item_name
		self.node_list["lbl_name_hl"].text.text = item_name
	end

    local stone_info = EquipmentWGData.Instance:GetStoneInfoListByIndex(self.data.index)
	if nil == stone_info then
		return
	end

	local show_stone_list = {}
	for i= 0, GameEnum.MAX_STONE_COUNT - 1 do
		local is_open = EquipmentWGData.Instance:BaoShiSlotIsOpen(self.data.index, i)
		if is_open then
			table.insert(show_stone_list, stone_info[i])
		end
    end

    if not IsEmptyTable(show_stone_list) then
		table.sort(show_stone_list, SortTools.KeyUpperSorter("item_id"))
	end

    for i = 0, GameEnum.MAX_STONE_COUNT - 1 do
		if show_stone_list[i + 1] then
			local data = show_stone_list[i + 1]
			local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
			local bundle, asset = "", ""
			if item_cfg then
				local stone_cfg = EquipmentWGData.Instance:GetBaoShiCfgByItemId(data.item_id)
				local stone_type = stone_cfg.stone_type
				bundle, asset = ResPath.GetEquipmentIcon("a2_bsi_" .. (stone_type or 1))
				self.node_list["icon_" .. i].image:LoadSprite(bundle, asset, function()
					self.node_list["icon_" .. i].image:SetNativeSize()
				end)
			end

			self.node_list["icon_" .. i]:SetActive(item_cfg ~= nil)
			self.node_list["img_suo_" .. i]:SetActive(false)
		else
			self.node_list["icon_" .. i]:SetActive(false)
			self.node_list["img_suo_" .. i]:SetActive(true)
		end
	end
end

function BSJLOverviewEquipItemCellRender:OnSelectChange(is_select)
	self.node_list["normal_bg"]:SetActive(not is_select)
	self.node_list["select_bg"]:SetActive(is_select)
end
