require("game/merge_activity/merge_special_rank/merge_special_rank_wg_data")
require("game/merge_activity/merge_special_rank/merge_special_rank_item")
require("game/merge_activity/merge_special_rank/merge_special_rank_gold_render")
require("game/merge_activity/merge_special_rank/merge_special_rank_silver_ticket_render")


function MergeActivityView:InitSPRank()
end

function MergeActivityView:LoadCallBackSPRank()
    self.sp_gold_rank_list = AsyncListView.New(MergeSPRankGoldRender, self.node_list.sp_gold_list)
    self.sp_gold_rank_list:SetCreateCellCallBack(BindTool.Bind(self.OnGoldRankListCreateCell, self))
    -- self.act_render = OperationActRender.New(self.node_list.special_rank_model)

    --设置模型
    if nil == self.role_model then
        self.role_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["special_rank_model"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = true,
        }
        
        self.role_model:SetRenderTexUI3DModel(display_data)
        -- self.role_model:SetUI3DModel(self.node_list.special_rank_model.transform, self.node_list.special_rank_model.event_trigger_listener,
        --                     1, false, MODEL_CAMERA_TYPE.BASE)
    end

    XUI.AddClickEventListener(self.node_list.sp_look_rank_btn, BindTool.Bind(self.OnClickSpLookRankBtn, self))
end

function MergeActivityView:OnGoldRankListCreateCell(cell)
    cell:SetParentScrollRect(self.node_list.sp_gold_list.scroll_rect)
end


function MergeActivityView:ReleaseCallBackSPRank()
    if self.sp_rank_tab_button_list then
        for i, v in ipairs(self.sp_rank_tab_button_list) do
            v:DeleteMe()
        end
        self.sp_rank_tab_button_list = nil
    end

    if self.role_model then
        self.role_model:DeleteMe()
        self.role_model = nil
    end

    if self.sp_gold_rank_list then
        self.sp_gold_rank_list:DeleteMe()
        self.sp_gold_rank_list = nil
    end
    -- if self.act_render then
    --     self.act_render:DeleteMe()
    --     self.act_render = nil
    -- end
    self.cur_special_model_id = nil
end

function MergeActivityView:OpenCallBackSPRank()
   
end

function MergeActivityView:ShowIndexCallBackSPRank()
    MergeSpecialRankWGData.Instance:CacheTodayIsReminded()
    RemindManager.Instance:Fire(RemindName.MergeActivity_SPRank)
    MergeSpecialRankWGCtrl.Instance:RequestRankInfo()
end

function MergeActivityView:OnFlushSPRank(param_t)
    self:FlushSubIndexSPRank()

    local cfg = MergeSpecialRankWGData.Instance:GetGoldConfigParamCfg()
    
    if cfg then
        self:SetOutsideRuleTips(string.format(Language.MergeSPRank.RuleTipOutDesc[1], cfg.rank_limit))
    end
    self:SetRuleInfo(Language.MergeSPRank.RuleTipDesc[1], Language.MergeSPRank.RuleTipTitle[1])

    if MergeSpecialRankWGData.Instance:GetIsEnd() then
        self:SetActRemainTime(TabIndex.merge_activity_2129)
    end

    -- --外部传入切换页签信息
    -- if param_t and param_t.all and param_t.all.rank_type then
    --     self:ChangeSubIndexSPRank(param_t.all.rank_type)
    -- end
end

function MergeActivityView:FlushSubIndexSPRank()
    self:FlushGoldSPRank()
end

function MergeActivityView:FlushGoldSPRank()
    self.node_list.sp_rank_gold_root:SetActive(true)
    --策划说右边从第二个开始。
    local data_list = MergeSpecialRankWGData.Instance:GetGoldDataList()
    -- local show_data_list = {}
    -- for i, v in ipairs(data_list) do    
    --     table.insert(show_data_list, v)
    -- end
    local show_data_list = MergeSpecialRankWGData.Instance:GetSpecialRankReward() --拿这个数据
    if show_data_list ~= nil then
        self.sp_gold_rank_list:SetDataList(show_data_list)
    end
    
    --榜首数据
    local first_data = data_list[1]
    -- local is_end = MergeSpecialRankWGData.Instance:GetIsEnd()

    if not IsEmptyTable(first_data) then
        if first_data.is_empty_value then
            --虚位以待
            self.node_list.sp_rank_first_name.text.text = Language.MergeSPRank.XuWeiYiDai
            self.node_list.character_shadow:SetActive(true)
    --         --self.node_list.sp_rank_first_text.text.text = string.format(Language.MergeSPRank.ConsumeGold, 0)
    --         --self.node_list.sp_rank_first_text.text.text = string.format(Language.MergeSPRank.NeedConsumeGold, first_data.reach_value)
        else
    --         if is_end then
    --             --self.node_list.sp_rank_first_name.text.text = first_data.name
    --         else
    --             --self.node_list.sp_rank_first_name.text.text = ToColorStr(Language.MergeSPRank.BaoMi, "#f9e4b2")
    --         end
            self.node_list.character_shadow:SetActive(false)
            self.node_list.sp_rank_first_name.text.text = first_data.name
    --         --消费仙玉：%s
    --         --self.node_list.sp_rank_first_text.text.text = string.format(Language.MergeSPRank.ConsumeGold, first_data.rank_value)
         end
    end

    local my_rank, my_rank_value, rank_max_count, is_end = MergeSpecialRankWGData.Instance:GetGoldRankInfo()
    if my_rank <= 0 then --未上榜
        self.node_list.sp_rank_gold_my_rank_text.text.text = Language.MergeSPRank.RankZhiHou
    elseif my_rank > rank_max_count then --多少名以外
        self.node_list.sp_rank_gold_my_rank_text.text.text = Language.MergeSPRank.OutRank
    elseif my_rank <= rank_max_count then --多少名以内，显示具体数量
        self.node_list.sp_rank_gold_my_rank_text.text.text = string.format(Language.MergeSPRank.MyRank, my_rank)
    end

    self.node_list.sp_rank_gold_my_rank_value.text.text = string.format(Language.MergeSPRank.DrawCount, my_rank_value)

    local flush_fun = function (protocol)
        if not self.node_list then
            return
        end

        if self.role_model then
            local ignore_table = {ignore_wing = true, ignore_jianzhen = true, ignore_halo = true, ignore_shouhuan = true, ignore_tail = true, ignore_waist = true}
            self.role_model:SetModelResInfo(protocol, ignore_table)
        end
    end

    if first_data ~= nil then
        BrowseWGCtrl.Instance:BrowRoelInfo(first_data.userid, flush_fun)
    end

    --模型
    -- local param_cfg = MergeSpecialRankWGData.Instance:GetGoldViewCfg()
    -- if param_cfg then
    --     local data = {}
    --     data.item_id = param_cfg.model_id
    --     data.render_type = param_cfg.render_type
    --     if param_cfg.render_string_param1 and param_cfg.render_string_param1 ~= ""
    --             and param_cfg.render_string_param2 and param_cfg.render_string_param2 ~= "" then
    --         data.bundle_name = param_cfg.render_string_param1
    --         data.asset_name = param_cfg.render_string_param2
    --     end

    --     if self.cur_special_model_id == data.item_id then
    --         return
    --     end
    --     self.cur_special_model_id = data.item_id
    --     self.act_render:SetData(data)

    --     if data.render_type == OARenderType.RoleModel then
    --         local item_cfg = ItemWGData.Instance:GetItemConfig(param_cfg.model_id)
    --         if item_cfg then
    --             self.node_list.special_rank_item_name.text.text = item_cfg.name
    --         end
    --     else
    --         self.node_list.special_rank_item_name.text.text = param_cfg.show_name
    --         self.node_list.special_rank_name_bg:SetActive(param_cfg.show_name and param_cfg.show_name ~= "")
    --     end
    -- end
end

function MergeActivityView:OnClickSpLookRankBtn()
    MergeSpecialRankWGCtrl.Instance:OpenTip()
end