OperationActReRankTipsView = OperationActReRankTipsView or BaseClass(SafeBaseView)
function OperationActReRankTipsView:__init()
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/operation_activity_ui/recharge_rank_prefab", "layout_second_panel", {sizeDelta = Vector2(786, 540)})
	self:AddViewResource(0, "uis/view/operation_activity_ui/recharge_rank_prefab", "recharge_rank_view")
	-- self:AddViewResource(0, assetbundle, "recharge_rank_view")
end

function OperationActReRankTipsView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.OperationActivity.RechargeRankTipsTiele
	-- local end_time = ActXianQiJieFengWGData.Instance:GetActivityInValidTime(TabIndex.xianqi_jiefeng_longhun_rank)
	-- self.node_list["rank_time"].text.text = string.format(Language.XianQiJieFengAct.LongHunRankLog_End_Time, TimeUtil.FormatMDHMS(end_time))
	self.rank_log_list = AsyncListView.New(OpReRankLogItemRender, self.node_list["ph_item_list"])

	self:FlushView()
end

function OperationActReRankTipsView:ReleaseCallBack()
	if self.rank_log_list then
		self.rank_log_list:DeleteMe()
		self.rank_log_list = nil
	end
end

function OperationActReRankTipsView:OpenCallBack()
	OpRechargeRankWGCtrl.Instance:SendOpRechargeRankReq(OPRECHARGE_RANK.INFO)
	OpRechargeRankWGCtrl.Instance:SendOpRechargeRankReq(OPRECHARGE_RANK.RANK_INFO)
	--RankWGCtrl.Instance:SendGetPersonRankListReq(GameEnum.PERSON_RANK_TYPE_RA_CHONGZHI_2, nil)
end

function OperationActReRankTipsView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
			self:FlushView()
		end
	end
end

function OperationActReRankTipsView:FlushView()
	local data_list = OpRechargeRankWGData.Instance:GetRankInfo()
	--print_error(data_list)
	if data_list then
        self.rank_log_list:SetDataList(data_list)
	end

	local my_total_recharge = OpRechargeRankWGData.Instance:GetRechargeInfo()
    local my_rank_data = OpRechargeRankWGData.Instance:GetMyRankInfo()
	if my_rank_data ~= nil then
		self.node_list["my_rank"].text.text = string.format(Language.OperationActivity.RechargeRankTitle4, my_rank_data.rank_index)
	else
		self.node_list["my_rank"].text.text = Language.OperationActivity.RechargeNoRank
	end
    
	--我的累计充值
	self.node_list["total_value"].text.text = string.format(Language.OperationActivity.RechargeRankDesc, my_total_recharge)
end

OpReRankLogItemRender = OpReRankLogItemRender or BaseClass(BaseRender)

function OpReRankLogItemRender:OnFlush()
	if not self.data then
		return
	end

    local is_top_3
    local player_rank
    if self.data.no_true_rank then  --未上榜
        self.node_list.need_cap_value.text.text = Language.OperationActivity.BaoMi
		self.node_list.need_cap_value2.text.text = Language.OperationActivity.BaoMi

		self.node_list.player_name.text.text = ""
        is_top_3 = self.data.index <= 3
        player_rank = self.data.index
	else
		self.node_list.need_cap_value.text.text = self.data.rank_data.rank_value
		self.node_list.need_cap_value2.text.text = self.data.rank_data.rank_value
        is_top_3 = self.data.index <= 3
        player_rank = self.data.index
	end

    local user_name = self.data.rank_data.user_name
	if self.data.no_true_rank then
		user_name = Language.OperationActivity.XuWeiYiDai
	end

    self.node_list.player_name.text.text = user_name
	self.node_list.player_name2.text.text = user_name

	self.node_list.player_name.text.enabled = not is_top_3
	self.node_list.player_name2.text.enabled = is_top_3

    self.node_list.need_cap_value.text.enabled = not is_top_3
	self.node_list.need_cap_value2.text.enabled = is_top_3

    self.node_list.img_rank.image.enabled = is_top_3
 
    if not is_top_3 then
		self.node_list.rank.text.text = player_rank
	else
		self.node_list.rank.text.text = ""
	end

	if self.index%2 == 0 then
		self.node_list.bg_normal.image:LoadSprite(ResPath.GetCommon("a3_kfcb_di_phxx2"))
	else
		self.node_list.bg_normal.image:LoadSprite(ResPath.GetCommon("a3_kfcb_di_phxx3"))
	end

	self.node_list.img_bg:SetActive(is_top_3)
	if is_top_3 then
		self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. self.data.index))
		self.node_list.img_bg.image:LoadSprite(ResPath.GetNoPackPNG("a3_zqxz_di_" .. self.data.index))
	end
end