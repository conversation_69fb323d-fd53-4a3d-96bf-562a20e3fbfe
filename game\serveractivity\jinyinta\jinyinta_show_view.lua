JinyintaShowView = JinyintaShowView or BaseClass(SafeBaseView)

local VECTOR3_THREE = Vector3(3, 3, 3)
local VECTOR3_ONE = Vector3(1, 1, 1)
local ANI_SPEED = 0.2
local ani_ten_count = 1
local ani_ten_flag_t = {}
local ani_one_flag_t = {}
local ani_one_count = 1

function JinyintaShowView:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/jinyinta_ui_prefab", "layout_show_reward")
	self.show_grid = nil
	self.show_grid_1 = nil
	self.data_list = {}
end

function JinyintaShowView:__delete()

end

function JinyintaShowView:ReleaseCallBack()
	if self.show_grid then
		self.show_grid:DeleteMe()
		self.show_grid = nil
	end

	if self.show_grid_1 then
		self.show_grid_1:DeleteMe()
		self.show_grid_1 = nil
	end
	self.cangku_text = nil
end

function JinyintaShowView:LoadCallBack()
	self:CreateShowGrid()
	XUI.AddClickEventListener(self.node_list.btn_chouqu_again,BindTool.Bind(self.OnClickAgain, self, 1))
	XUI.AddClickEventListener(self.node_list.btn_enter_cangku,BindTool.Bind(self.OnClickBag, self, 1))
	XUI.AddClickEventListener(self.node_list.btn_close_window,BindTool.Bind(self.Close, self))
	self.cangku_text = self.node_list.btn_enter_cangku:FindObj("Text")
	self.cangku_text.text.text = Language.Common.Confirm1
end

function JinyintaShowView:OpenCallBack()

end

function JinyintaShowView:ShowIndexCallBack(index)

end

function JinyintaShowView:CreateShowGrid()
		self.show_grid = AsyncBaseGrid.New()
		self.show_grid:CreateCells({change_cells_num=1, col=5,list_view = self.node_list.ph_zhanshii_cell ,itemRender = RewardShowCell, is_no_data_hide = true})

		self.show_grid_1 = AsyncBaseGrid.New()
		self.show_grid_1:CreateCells({change_cells_num=1, col=5,list_view = self.node_list.ph_zhanshii_cell_1 ,itemRender = RewardShowCellTwo, is_no_data_hide = true})
end

function JinyintaShowView:OnFlush()
	-- self.show_grid:SetDataList(self.data_list,3)
	local cur_layout = JinyintaWGData.Instance:GetCurLayer()
	local one_cost = JinyintaWGData.Instance:GetOneDrawCost(cur_layout)
	local cur_count = JinyintaWGData.Instance:GetDrawCount()
	local keyNum = ItemWGData.Instance:GetItemNumInBagById(JinyintaWGData.Instance:GetLotterykeyID())
	if keyNum >= cur_count then
		self.node_list.gold_chouqu_txt.text.text = (Language.KuafuGuildBattle.KfGuildConsumeGold .. "0")
	else
		self.node_list.gold_chouqu_txt.text.text = (Language.KuafuGuildBattle.KfGuildConsumeGold .. one_cost * cur_count)
	end

	local randact_cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	if randact_cfg == nil or randact_cfg.other == nil or randact_cfg.other[1].level_lottery_50_item_id == nil then return end

	local chuizi_num = ItemWGData.Instance:GetItemNumInBagById(randact_cfg.other[1].level_lottery_50_item_id)
	local item_name = ItemWGData.Instance:GetItemName(randact_cfg.other[1].level_lottery_50_item_id)
	if chuizi_num > 0 and cur_count == 50 then
		-- local content = string.format(Language.DrawCard.ItemNum, chuizi_num, item_name)
		self.node_list.gold_chouqu_txt.text.text = (Language.KuafuGuildBattle.KfGuildConsumeGold .. "0")
	end
	self.node_list.ph_zhanshii_cell_1:SetActive(#self.data_list + 1 == 1)
	self.node_list.ph_zhanshii_cell:SetActive(#self.data_list + 1 > 1)
	if #self.data_list + 1 > 1 then
		if nil ~= self.show_grid then
			ani_ten_flag_t = {}
			self.show_grid:SetDataList(self.data_list, 0)
			self.show_grid:CancleAllSelectCell()

			if self.ten_time_quest then
				GlobalTimerQuest:CancelQuest(self.ten_time_quest)
				self.ten_time_quest = nil
			end
			ani_ten_count = 1
			self.ten_time_quest = GlobalTimerQuest:AddTimesTimer(function()
				self:DoAnimOne()
				end, ANI_SPEED, #self.data_list + 1)
		end
	elseif #self.data_list + 1 == 1 then
		if nil ~= self.show_grid_1 then
			ani_one_flag_t = {}
			self.show_grid_1:SetDataList(self.data_list, 0)
			self.show_grid_1:CancleAllSelectCell()

			if self.one_time_quest then
				GlobalTimerQuest:CancelQuest(self.one_time_quest)
				self.one_time_quest = nil
			end
			ani_one_count = 1
			self.one_time_quest = GlobalTimerQuest:AddTimesTimer(function()
				self:DoAnimTwo()
				end, ANI_SPEED, #self.data_list + 1)
		end
	end
end

function JinyintaShowView:DoAnimOne()
	if nil == self.show_grid then return end
	local cell = self.show_grid:GetCell(ani_ten_count)
	ani_ten_flag_t[ani_ten_count] = true
	ani_ten_count = ani_ten_count + 1
	if cell ~= nil and cell:GetData() ~= nil then
		cell.view.transform.localScale = Vector3(3,3,3)
		cell.view.transform:DOScale(Vector3(1,1,1),0.2)
		cell:SetActive(true)
		--特效
		local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_choujiangbaodian)
		EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, cell.view.transform, 0.28, Vector3(0, 0, 0),nil,nil)
	end
end

function JinyintaShowView:DoAnimTwo()
	if nil == self.show_grid_1 then return end
	local cell = self.show_grid_1:GetCell(ani_one_count)
	ani_one_flag_t[ani_one_count] = true
	ani_one_count = ani_one_count + 1
	if cell ~= nil and cell:GetData() ~= nil then
		cell.view.transform.localScale = Vector3(3,3,3)
		cell.view.transform:DOScale(Vector3(1,1,1),0.2)
		cell:SetActive(true)
		--特效
		local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_choujiangbaodian)
		EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, cell.view.transform, 0.28, Vector3(0, 0, 0),nil,nil)
	end
end

function JinyintaShowView:SetDataList(data_list)
	self.data_list = data_list
	self:Open()
	self:Flush()
end

function JinyintaShowView:OnClickAgain()
	local cur_count = JinyintaWGData.Instance:GetDrawCount()
	JinyintaWGCtrl.Instance:OnViewClickDraw(cur_count)
end

function JinyintaShowView:OnClickBag()
	-- ViewManager.Instance:Open(GuideModuleName.Bag, "rolebag_bag_all")
	self:Close()
end


RewardShowCell = RewardShowCell or BaseClass(ItemCell)
function RewardShowCell:OnFlush()
	self:SetActive(ani_ten_flag_t[self.index] == true)
	ItemCell.OnFlush(self)
end

function RewardShowCell:SetActive(value)
	ItemCell.SetActive(self, value and (self.index == nil or ani_ten_flag_t[self.index]))
end


RewardShowCellTwo = RewardShowCellTwo or BaseClass(ItemCell)
function RewardShowCellTwo:OnFlush()
	self:SetActive(ani_one_flag_t[self.index] == true)
	ItemCell.OnFlush(self)
end

function RewardShowCellTwo:SetActive(value)
	ItemCell.SetActive(self, value and (self.index == nil or ani_one_flag_t[self.index]))
end
