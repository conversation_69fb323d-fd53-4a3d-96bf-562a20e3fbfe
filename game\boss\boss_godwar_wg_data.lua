BossGodWarWGData = BossGodWarWGData or BaseClass()
function BossGodWarWGData:__init()
    if BossGodWarWGData.Instance ~= nil then
		print_error("[BossGodWarWGData] attempt to create singleton twice!")
		return
	end
	BossGodWarWGData.Instance = self

	self:InitCfg()

	RemindManager.Instance:Register(RemindName.BossGodwar, BindTool.Bind(self.GetRemind, self))

	self.first_auto_open_boss_godwar = true
end

function BossGodWarWGData:__delete()
	BossGodWarWGData.Instance = nil
end

-- 初始化表数据
function BossGodWarWGData:InitCfg()
	local god_war_cfg_auto = ConfigManager.Instance:GetAutoConfig("zhanshen_privilege_auto")
	self.god_war_cfg = god_war_cfg_auto.rmb_buy
end

-- 根据rmb_seq获取表数据
function BossGodWarWGData:GetBuyMessageBySeq(rmb_seq)
	local emtry = {}
	return (self.god_war_cfg or emtry)[rmb_seq] or nil
end

--检测是否是第一次自动弹出界面.
function BossGodWarWGData:CheckFirstAutoOpenBossGodwar()
	if self.first_auto_open_boss_godwar then
		self.first_auto_open_boss_godwar = false
		return true
	end

	return false
end

---------- 设置服务器数据--------------------------------
-- 设置数据
function BossGodWarWGData:SetPrivilegeInfo(protocol)
	self.server_data = protocol
end

-- 获取数据
function BossGodWarWGData:GetPrivilegeInfo()
	return self.server_data
end
--------------------------
function BossGodWarWGData:GetPrivilegeCfgByLevel(level)
	local curr_cfg = nil

	if level ~= -1 then
		curr_cfg = self:GetBuyMessageBySeq(level)
	end

	local next_level = level + 1
	local next_cfg = self:GetBuyMessageBySeq(next_level)

	return curr_cfg, next_cfg
end

function BossGodWarWGData:GetRemind()
	local god_war_data = self:GetPrivilegeInfo()
	local is_fetch_daily_rewards = god_war_data.is_fetch_daily_rewards or 0
	if god_war_data.level ~= -1 and is_fetch_daily_rewards == 0 then
		return 1
	else
		return 0
	end
end