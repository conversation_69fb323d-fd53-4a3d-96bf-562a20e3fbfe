---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON>ua)
--- Created by wjf.
--- DateTime: 2019/11/9 20:09
---
PetFollow = PetFollow or BaseClass(CharacterFollow)
function PetFollow:__init(vo)
	self.shield_obj_type = ShieldObjType.PetFollowUI
end
function PetFollow:OnRootCreateCompleteCallback(gameobj)
    CharacterFollow.OnRootCreateCompleteCallback(self, gameobj)

    self:SetPetOnwerName()
end
function PetFollow:SetPetOnwerName()
    self.namebar:SetPetOnwerName(self.vo.owner_obj_name)
end

--玩家更名后,宠物也跟随改变(仅刷新玩家自己)
function PetFollow:SetPetNameByMainRoleChange()
	self.namebar:SetPetOnwerName(GameVoManager.Instance:GetMainRoleVo().name)
end