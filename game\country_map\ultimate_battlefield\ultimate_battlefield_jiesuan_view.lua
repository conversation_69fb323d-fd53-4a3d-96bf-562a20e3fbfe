UltimateBattlefieldJiesuanView = UltimateBattlefieldJiesuanView or BaseClass(SafeBaseView)

--0红1蓝
local ULTIATE_CAMP = {
    CAMP_RED = 0,
    CAMP_BLUE = 1,
}

function UltimateBattlefieldJiesuanView:__init()
    self.view_style = ViewStyle.Half
	self:SetMaskBg()
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_jiesuan_panel")
    self:AddViewResource(0, "uis/view/country_map_ui/flag_grabing_battlefield_ui_prefab", "layout_fgb_jiesuan_view")
end

function UltimateBattlefieldJiesuanView:LoadCallBack()
    if not self.fgb_item_list then
        self.fgb_item_list = AsyncListView.New(ItemCell, self.node_list.fgb_item_list)
        self.fgb_item_list:SetStartZeroIndex(false)
    end

    if not self.fgb_rank_list then
        self.fgb_rank_list = AsyncListView.New(UltimateBattlefieldJieSuanRankListItemRender, self.node_list.fgb_rank_list)
        self.fgb_rank_list:SetStartZeroIndex(false)
    end

    self.node_list.score_num.text.text = Language.Rank.ZhenYing
    self.node_list.kill_num.text.text = Language.Rank.Score
end

function UltimateBattlefieldJiesuanView:ReleaseCallBack()
    if self.fgb_item_list then
        self.fgb_item_list:DeleteMe()
        self.fgb_item_list = nil
    end

    if self.fgb_rank_list then
        self.fgb_rank_list:DeleteMe()
        self.fgb_rank_list = nil
    end

    if self.btn_text_countdown then
		GlobalTimerQuest:CancelQuest(self.btn_text_countdown)
		self.btn_text_countdown = nil
	end
end

function UltimateBattlefieldJiesuanView:OnFlush()
    local scene_info =  UltimateBattlefieldWGData.Instance:GetSceneInfo()
    if not scene_info then
        return
    end
    local stage = scene_info.stage or 0
    local win_flag = scene_info.stage_win_flag and scene_info.stage_win_flag[stage] or -1
    
    local rank_list, my_rank_data = UltimateBattlefieldWGData.Instance:GetCurrRankInfoList()
    if (not rank_list) or (not my_rank_data) then
        return
    end

    local is_suc = win_flag == my_rank_data.camp
    self.node_list.victory:SetActive(is_suc)
    self.node_list.lose:SetActive(not is_suc)

    local reward_data = UltimateBattlefieldWGData.Instance:GetFallingCfgBySeq(my_rank_data.rank)

    if reward_data and self.fgb_item_list then
        local real_reward_item = {}
        for _, data in pairs(reward_data.reward_item) do
            if data and data.item_id then
                table.insert(real_reward_item, data)
            end
        end

        self.fgb_item_list:SetDataList(real_reward_item)
    end
  
    self.fgb_rank_list:SetDataList(rank_list)

    local rank_info = UltimateBattlefieldWGData.Instance:GetScoreRankInfo()
    if not rank_info then
        return
    end

    local left_score = 0
    local right_score = 0

    for _, rank_item in ipairs(rank_info.rank_item_list) do
        if rank_item then    --- 检测是否竞猜
            if rank_item.camp == ULTIATE_CAMP.CAMP_RED then
                left_score = left_score + rank_item.score
            else
                right_score = right_score + rank_item.score
            end
        end
    end

    for i = 0, 1 do
        local cfg = UltimateBattlefieldWGData.Instance:GetCampCfgBySeq(i)
		local team_name = cfg and cfg.camp_name or ""

        self.node_list["fgb_team_name" .. i].text.text = team_name
        self.node_list["fgb_team_score" .. i].text.text = i == 0 and left_score or right_score
        self.node_list["fgb_team_win" .. i]:CustomSetActive(win_flag == i)
    end
end

------------------------------UltimateBattlefieldJieSuanRankListItemRender----------------------------
UltimateBattlefieldJieSuanRankListItemRender = UltimateBattlefieldJieSuanRankListItemRender or BaseClass(BaseRender)

function UltimateBattlefieldJieSuanRankListItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local cfg = UltimateBattlefieldWGData.Instance:GetCampCfgBySeq(self.data.camp)
    local plat_type = self.data.usid.temp_high
    local server_id = self.data.usid.temp_low
    local name_str = string.format("[s%s]%s", server_id, self.data.name)
    local camp_name = cfg and cfg.camp_name or ""
    local is_self = UltimateBattlefieldWGData.Instance:CheckIsSelf(self.data.uuid)
    
    if is_self then
        name_str = ToColorStr(name_str, COLOR3B.GREEN)
        camp_name = ToColorStr(camp_name, COLOR3B.GREEN)
    end

    self.node_list.role_name.text.text = name_str
    local is_top_3 = self.data.rank <= 3
    self.node_list.rank_num_icon:CustomSetActive(is_top_3)

    local rank_text = ""
	if not is_top_3 then
        rank_text = self.data.rank
	else
        self.node_list.rank_num_icon.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. self.data.rank))
	end
    
    self.node_list.rank_text.text.text = is_self and ToColorStr(rank_text, COLOR3B.GREEN) or rank_text
    self.node_list.role_score.text.text = camp_name
    self.node_list.role_kill_num.text.text = is_self and ToColorStr(self.data.score, COLOR3B.GREEN) or self.data.score
end