function OperationActivityView:ReleaseCallBackHappyKhView()
	if self.btn_render_list ~= nil then
		self.btn_render_list:DeleteMe()
		self.btn_render_list = nil
	end

	if self.reward_render_list_0 ~= nil then
		self.reward_render_list_0:DeleteMe()
		self.reward_render_list_0 = nil
	end

	-- if self.reward_render_list_1 ~= nil then
	-- 	self.reward_render_list_1:DeleteMe()
	-- 	self.reward_render_list_1 = nil
	-- end

	if self.task_render_list ~= nil then
		self.task_render_list:DeleteMe()
		self.task_render_list = nil
	end

	-- if self.act_happykh_render ~= nil then
	-- 	self.act_happykh_render:DeleteMe()
	-- 	self.act_happykh_render = nil
	-- end

	if self.reward_item_cell_list then
		for k,v in pairs(self.reward_item_cell_list) do
			v:DeleteMe()
		end
		self.reward_item_cell_list = nil
	end

	self.happykh_view_info_cfg = nil
	self.no_first_enter = nil
end

function OperationActivityView:LoadCallBackHappyKhView()
	OperationHappyKuangHuanWGCtrl.Instance:SendReq(HAPPY_CANIVAL_OPERA_TYPE.HAPPY_CANIVAL_OPERA_TYPE_INFO)
	self.happykh_view_info_cfg = OperationHappyKuangHuanWGData.Instance:GetViewCfgByInterface()
	self:InitHappyKhView()
	--self:SetHappyKhViewShowInfo()

	self.reward_item_cell_list = {}

end

-- function OperationActivityView:ShowIndexCallBackHappyKhView()
-- 	for i = 0, 2 do
-- 		local pos = self.node_list["reward_item_cell_" .. i].transform.localPosition
-- 		self.node_list["reward_item_cell_" .. i].transform.localPosition = Vector3.zero
-- 		self.node_list["reward_item_cell_" .. i].transform:DOLocalMove(pos, 1)
-- 	end

-- end

function OperationActivityView:InitHappyKhView()
	--初始化render
	if nil == self.btn_render_list then
		self.btn_render_list = AsyncListView.New(HappyKuangHuanBtnRender,
		self.node_list.happykh_btn_list)
		self.btn_render_list:SetSelectCallBack(BindTool.Bind1(self.OnClickBtnRender, self))
	end
	local btn_cfg = OperationHappyKuangHuanWGData.Instance:GetTBtnCfg()
	self.btn_render_list:SetDefaultSelectIndex(1)
	self.btn_render_list:SetDataList(btn_cfg)

	if nil == self.reward_render_list_0 then
		self.reward_render_list_0 = AsyncListView.New(HappyKuangHuanRewardRender, self.node_list.happykh_reward_list)
		self.reward_render_list_0:SetCreateCellCallBack(BindTool.Bind(self.OnCreateCell, self))
	end

	-- if nil == self.reward_render_list_1 then
	-- 	self.reward_render_list_1 = AsyncListView.New(HappyKuangHuanRewardRender, self.node_list.happykh_yulan_list)
	-- end

	if nil == self.task_render_list then
		self.task_render_list = AsyncListView.New(HappyKuangHuanTaskRender, self.node_list.happykh_task_list)
		--策划需求要改成一行两个显示
		-- self.task_render_list = AsyncBaseGrid.New()
		-- self.task_render_list:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list["happykh_task_list"],
		-- assetBundle = "uis/view/operation_happy_kuanghuan_prefab", assetName = "ph_task_item",  itemRender = HappyKuangHuanTaskRender})
		-- self.task_render_list:SetStartZeroIndex(false)
	end
end


function OperationActivityView:OnCreateCell(cell)
	cell:SetParentScrollRect(self.node_list.happykh_reward_list.scroll_rect)
end

--刷新界面状态
function OperationActivityView:OnFlushHappyKhView()
	if self.btn_render_list then
		self.btn_render_list:RefreshActiveCellViews()
	end

	local reward_cfg_0,  reward_cfg_1 = OperationHappyKuangHuanWGData.Instance:GetRewardInfo()
	if self.reward_render_list_0 then	
		self.reward_render_list_0:SetDataList(reward_cfg_0)
	end

	-- if self.reward_render_list_1 then	
	-- 	self.reward_render_list_1:SetDataList(reward_cfg_1)
	-- 	self.node_list.happykh_yulan_list:SetActive(reward_cfg_1 and not IsEmptyTable(reward_cfg_1))
	-- end

	if self.task_render_list then
		local task_cfg = OperationHappyKuangHuanWGData.Instance:GetTaskInfo()
		self.task_render_list:SetDataList(task_cfg)
	end

	--总狂欢值
	local all_bonus_points = OperationHappyKuangHuanWGData.Instance:GetAllBonusPoints()
	self.node_list["txt_huanlezhi_num"].text.text = all_bonus_points

	-- 奖励展示
	-- local param_cfg = OperationHappyKuangHuanWGData.Instance:GetInterfacecCfg()
	-- local reward_show = param_cfg.reward_item
	-- for i = 0, 2 do
	-- 	if self.reward_item_cell_list[i] == nil then
	-- 		self.reward_item_cell_list[i] = ItemCell.New(self.node_list["reward_item_cell_" .. i])
	-- 	end
	-- 	self.reward_item_cell_list[i]:SetData(reward_show[i])
	-- end
end

function OperationActivityView:OnClickBtnRender(item)
	if item and item:GetData() then
		self.node_list["happykh_reward_parent"]:SetActive(item:GetData().index == 1)
		self.node_list["happykh_task_parent"]:SetActive(item:GetData().index == 2)

		if item:GetData().index == 1 then
			local reward_cfg_0,  reward_cfg_1 = OperationHappyKuangHuanWGData.Instance:GetRewardInfo()
			-- if #reward_cfg_1 ~= 0 then
			-- 	self.node_list.happykh_reward_list.rect.sizeDelta = Vector2(666, 508)
			-- 	self.node_list.happykh_img_bg.rect.sizeDelta = Vector2(666, 512)
			-- else
			-- 	self.node_list.happykh_reward_list.rect.sizeDelta = Vector2(666, 612)
			-- 	self.node_list.happykh_img_bg.rect.sizeDelta = Vector2(666, 457.7)
			-- end

			if self.reward_render_list_0 then
				self.reward_render_list_0:ReloadData(0)
			end
		else
			-- self.node_list.happykh_img_bg.rect.sizeDelta = Vector2(690.96, 457.7)
			if self.task_render_list then
				self.task_render_list:ReloadData(0)
			end
		end
	end
end

--活动介绍
function OperationActivityView:OnClickHappyKhState()
	local cfg = ActivityWGData.Instance:GetCanLookActivityInfo(ACTIVITY_TYPE.OPERA_ACT_HAPPY_KUANGHUAN)
	local name = cfg and cfg.active_name or ""
	local desc = self.happykh_view_info_cfg and self.happykh_view_info_cfg.rule_1 or ""
	local tip_label = self.happykh_view_info_cfg and self.happykh_view_info_cfg.tip_label or ""

	self:SetOutsideRuleTips(tip_label)
	self:SetRuleInfo(desc, name)

	--self:FlushKuangHuanModel()

	if not self.no_first_enter then
		self.no_first_enter = true
		return
	end

	if self.btn_render_list and self.no_first_enter then
		self.btn_render_list:SelectIndex(1)
	end
end

-- function OperationActivityView:FlushKuangHuanModel()
-- 	if nil == self.act_happykh_render then
-- 		self.act_happykh_render = OperationActRender.New(self.node_list.happykh_model_parent)
-- 	end

-- 	local param_cfg = OperationHappyKuangHuanWGData.Instance:GetInterfacecCfg()
-- 	if nil == param_cfg then
-- 		return
-- 	end

-- 	local data = {}
-- 	data.pos_up = Vector2(4.77, 5.96)
-- 	data.pos_down = Vector2(4.77, 18)
-- 	data.move_time = 0.8
-- 	-- data.reward_show = param_cfg.reward_item
-- 	data.reward_type = "kuanghuan"
-- 	data.render_type = param_cfg.render_type
-- 	if param_cfg.render_int_param1 and param_cfg.render_int_param1 > 0 then
-- 		data.item_id = param_cfg.render_int_param1
-- 	elseif param_cfg.render_string_param1 and param_cfg.render_string_param1 ~= ""
-- 			and param_cfg.render_string_param2 and param_cfg.render_string_param2 ~= "" then
-- 		data.bundle_name = param_cfg.render_string_param1
-- 		data.asset_name = param_cfg.render_string_param2
-- 	end

-- 	if param_cfg.mw_position and param_cfg.mw_position ~= nil then
-- 		local pos = Split(param_cfg.mw_position, "|")
-- 		data.position = Vector3(tonumber(pos[1]), tonumber(pos[2]), tonumber(pos[3]))
-- 	end

-- 	if param_cfg.mw_scale and param_cfg.mw_scale ~= nil then
-- 		local scal = param_cfg.mw_scale
-- 		data.scale = Vector3(scal, scal, scal)
-- 	end

-- 	if param_cfg.mw_rotation and param_cfg.mw_rotation ~= nil then
-- 		local rota = Split(param_cfg.mw_rotation, "|")
-- 		data.rotation = Quaternion.Euler(tonumber(rota[1]), tonumber(rota[2]), tonumber(rota[3]))
-- 	end

-- 	-- if param_cfg.reward_position and param_cfg.reward_position ~= nil then
-- 	-- 	local show_pos_str_list = Split(param_cfg.reward_position, "#")
-- 	-- 	data.reward_show_pos = {}
-- 	-- 	for i, pos_str in ipairs(show_pos_str_list) do
-- 	-- 		local pos = Split(pos_str, "|")
-- 	-- 		data.reward_show_pos[i - 1] = Vector2(tonumber(pos[1]), tonumber(pos[2]))
-- 	-- 	end
-- 	-- end

-- 	self.act_happykh_render:SetData(data) --Test Model
-- end

--设置界面的所有显示信息包括底图(只需要赋值一次的)
function OperationActivityView:SetHappyKhViewShowInfo()
	-- if nil == self.happykh_view_info_cfg then
	-- 	return
	-- end

	local left_bg_name = OperationActivityWGData.Instance:GetCurTypeImgName("a2_zx_ggdt9")
	local bundle, asset = ResPath.GetRawImagesPNG(left_bg_name)
	self.node_list["happykh_left_bg"].raw_image:LoadSprite(bundle, asset, function()
        self.node_list["happykh_left_bg"].raw_image:SetNativeSize()
    end)
	-- local asset2, bundle2 = ResPath.GetOperationHappyKhImagePath(self.happykh_view_info_cfg.title_name)
	-- self.node_list["happykh_title_name"].image:LoadSprite(asset2, bundle2,function ()
	-- 	self.node_list["happykh_title_name"].image:SetNativeSize()
	-- end)

	-- local asset4, bundle4 = ResPath.GetOperationHappyKhImagePath(self.happykh_view_info_cfg.model_dizuo)
	-- self.node_list["happykh_raw_dizuo"].image:LoadSprite(asset4, bundle4,function ()
	-- 	self.node_list["happykh_raw_dizuo"].image:SetNativeSize()
	-- end)
end


					  --------------------狂欢按钮render------------------------------------
------------------------------------------------------------------------------------------------------------------
HappyKuangHuanBtnRender = HappyKuangHuanBtnRender or BaseClass(BaseRender)
function HappyKuangHuanBtnRender:__init()

end

function HappyKuangHuanBtnRender:LoadCallBack()
	--self:SetHappyKhBtnInfo()
end

function HappyKuangHuanBtnRender:__delete()

end

function HappyKuangHuanBtnRender:OnFlush()
	if self.data then
		self.node_list["lbl_btn_text"].text.text = self.data.btn_name
		self.node_list["lbl_btn_hl_text"].text.text = self.data.btn_name
		self.node_list["redmind"]:SetActive(false)
		if 1 == self.data.index then
			local remind_show = OperationHappyKuangHuanWGData.Instance:GetRemindHintValue()
			self.node_list["redmind"]:SetActive(remind_show)
		end
	end
	
end

function HappyKuangHuanBtnRender:OnSelectChange(is_on)
	if self.node_list["hight"] then
		self.node_list["hight"]:SetActive(is_on)
	end
	if self.node_list["lbl_btn_hl_text"] then
		self.node_list["lbl_btn_hl_text"]:SetActive(is_on)
	end
end

-- function HappyKuangHuanBtnRender:SetHappyKhBtnInfo()
-- 	local happykh_view_info_cfg = OperationHappyKuangHuanWGData.Instance:GetViewCfgByInterface()
-- 	if nil == happykh_view_info_cfg then
-- 		return
-- 	end

-- 	local asset1, bundle1 = ResPath.GetOperationHappyKhImagePath(happykh_view_info_cfg.btn_bg)
-- 	self.node_list["img"].image:LoadSprite(asset1, bundle1,function ()
-- 		self.node_list["img"].image:SetNativeSize()
-- 	end)

-- 	local asset2, bundle2 = ResPath.GetOperationHappyKhImagePath(happykh_view_info_cfg.btn_hight_bg)
-- 	self.node_list["hight"].image:LoadSprite(asset2, bundle2,function ()
-- 		self.node_list["hight"].image:SetNativeSize()
-- 	end)
-- end



					  --------------------狂欢奖励render------------------------------------
------------------------------------------------------------------------------------------------------------------
HappyKuangHuanRewardRender = HappyKuangHuanRewardRender or BaseClass(BaseRender)

function HappyKuangHuanRewardRender:LoadCallBack()
	self:SetHappyKhImgInfo()
	self.node_list["btn_lingqu"].button:AddClickListener(BindTool.Bind1(self.OnclcikLingQu, self))

	self.is_load_complete = true
	self.nested_scroll_rect = SNestedScrollRect.New(self.node_list.item_parent)
	if self.parent_scroll_rect then
		self.nested_scroll_rect:SetParentScrollRect(self.parent_scroll_rect)
	end
end

function HappyKuangHuanRewardRender:__delete()
	if self.item_list then
		for k,v in pairs(self.item_list) do
			v:DeleteMe()
		end
		self.item_list = nil
	end

	if self.nested_scroll_rect then
		self.nested_scroll_rect:DeleteMe()
		self.nested_scroll_rect = nil
	end

	self.parent_scroll_rect = nil
	self.is_load_complete = nil
end

function HappyKuangHuanRewardRender:OnFlush()
	if nil == self.data then
		return
	end

	--总狂欢值
	local all_bonus_points = OperationHappyKuangHuanWGData.Instance:GetAllBonusPoints()
	local tab = self.data.tab
	-- self.node_list["img9_item_bg"]:SetActive(not self.data.special_bg)
	-- self.node_list["raw_bg"]:SetActive(self.data.special_bg)

	--local str_list = Split(tab.exchange_description, "|")
	--self.node_list["desc"].text.text = ToColorStr(str_list[1], self.reward_color)
	self.node_list["desc_2"].text.text = string.format(Language.OperationActivity.kuangHuanTaskTitle, tab.bonus_points)
	--self.node_list["happy_value"].text.text = tab.bonus_points
	--self.node_list["desc_3"].text.text = ToColorStr(str_list[2], self.reward_color)
	if nil == self.item_list then
		self.item_list = {}
	end

	local num = 0
	for k,v in pairs(tab.reward_item) do
		if self.item_list[k] then
			self.item_list[k]:SetActive(true)
		else
			self.item_list[k] = ItemCell.New(self.node_list["item_list"])
		end
		self.item_list[k]:SetData(v)
		self.item_list[k]:SetRedPointEff(self.data.status == HAPPY_CANIVAL_REWARD_TYPE.KLQ)
		self.item_list[k]:SetLingQuVisible(self.data.status == HAPPY_CANIVAL_REWARD_TYPE.YLQ)
		num = k + 1
	end

	if num <= #self.item_list then
		for i=num,#self.item_list do
			self.item_list[i]:SetActive(false)
		end
	end

	self.node_list["image_redmind"]:SetActive(self.data.status == HAPPY_CANIVAL_REWARD_TYPE.KLQ)
	self.node_list["img_ylq"]:SetActive(self.data.status == HAPPY_CANIVAL_REWARD_TYPE.YLQ) 				-- 已领取
	self.node_list["btn_lingqu"]:SetActive(self.data.status == HAPPY_CANIVAL_REWARD_TYPE.KLQ) 			-- 领取
	self.node_list["img_wdc"]:SetActive(self.data.status == HAPPY_CANIVAL_REWARD_TYPE.WDC) 				-- 未达成
end

function HappyKuangHuanRewardRender:OnclcikLingQu()
	if self.data.status == HAPPY_CANIVAL_REWARD_TYPE.KLQ then
		OperationHappyKuangHuanWGCtrl.Instance:SendReq(HAPPY_CANIVAL_OPERA_TYPE.HAPPY_CANIVAL_OPERA_TYPE_REWARD, self.data.tab.bonus_id)
	end
end

function HappyKuangHuanRewardRender:SetHappyKhImgInfo()
	local happykh_view_info_cfg = OperationHappyKuangHuanWGData.Instance:GetViewCfgByInterface()
	if nil == happykh_view_info_cfg then
		return
	end

	self.reward_color = happykh_view_info_cfg.reward_color or COLOR3B.WHITE
	self.reward_value_color = happykh_view_info_cfg.reward_value_color or COLOR3B.WHITE

	-- local asset1, bundle1 = ResPath.GetOperationHappyKhImagePath(happykh_view_info_cfg.normal_bg)
	-- self.node_list["img9_item_bg"].image:LoadSprite(asset1, bundle1)

	-- local asset4, bundle4 = ResPath.GetOperationHappyKhImagePath(happykh_view_info_cfg.special_bg)
	-- self.node_list["raw_bg"].image:LoadSprite(asset4, bundle4)
end

function HappyKuangHuanRewardRender:SetParentScrollRect(scroll_rect)
	self.parent_scroll_rect = scroll_rect

	if self.is_load_complete then
		self.nested_scroll_rect:SetParentScrollRect(self.parent_scroll_rect)
	end
end

--------------------狂欢任务render------------------------------------

HappyKuangHuanTaskRender = HappyKuangHuanTaskRender or BaseClass(BaseRender)

local JUMP_TYPE = {
	NORMAL = 1,--正常跳转类型
	HUSONG = 2, --直接护送
	CHAT = 3, --聊天
	BIZUO = 4,--跳转到必做界面
}

--图标路径
local ICON_PATH = {
	BIZUO = 1, --每日必做
	DATING = 2, --活动大厅
	KUANGHUAN = 3, --狂欢
}

function HappyKuangHuanTaskRender:__init()

end

function HappyKuangHuanTaskRender:LoadCallBack()
	--self:SetHappyKhImgInfo()
	self.node_list["btn_go"].button:AddClickListener(BindTool.Bind1(self.OnclcikOpenView, self))
end

function HappyKuangHuanTaskRender:__delete()

end

function HappyKuangHuanTaskRender:OnFlush()
	if nil == self.data then
		return
	end

	local tab = self.data.tab
	local desc = ""
	local str_list = Split(tab.task_describe, "|")
	local color = self.data.sucess == 1 and COLOR3B.L_GREEN or COLOR3B.L_RED
	local pro = self.data.progress .. "/" .. tab.progress
	pro = ToColorStr(pro, color)
	if type(tab.param_0_0) == "number" then
		if tab.param_0_0 > 0 then
			desc = string.format(str_list[1], pro, tab.param_0_0)
		else
			desc = string.format(str_list[1], pro)
		end
	else
		desc = string.format(str_list[1], pro, tab.param_0_0)
	end

	self.node_list["desc"].text.text = desc
	self.node_list["desc_2"].text.text = tab.bonus_points
	self.node_list["desc_3"].text.text = str_list[2]

	self.node_list["btn_go"]:SetActive(self.data.sucess == 0 and self.data.tab.open_panel and self.data.tab.open_panel ~= "" and self.data.tab.select_type == JUMP_TYPE.NORMAL)
	self.node_list["img_ywc"]:SetActive(self.data.sucess == 1)

	-- local asset, bundle = ResPath.GetMainUIIcon(tab.task_icon)
	-- -- if tab.task_icon_path == ICON_PATH.BIZUO then
	-- -- 	asset, bundle = ResPath.GetCommonIcon(tab.task_icon)
	-- -- elseif tab.task_icon_path == ICON_PATH.DATING then
	-- -- 	asset, bundle = ResPath.GetCommonIcon(tab.task_icon)
	-- -- elseif tab.task_icon_path == ICON_PATH.KUANGHUAN then
	-- -- 	asset, bundle = ResPath.GetCommonIcon(tab.task_icon)
	-- -- end

	-- if asset and bundle then
	-- 	self.node_list["item_icon"].image:LoadSprite(asset, bundle, function()
	-- 		self.node_list["item_icon"].image:SetNativeSize()
	-- 	end)
	-- end

	-- self.node_list["flush_img"]:SetActive(tab.if_re == 1)
end

function HappyKuangHuanTaskRender:OnclcikOpenView()
	if "" ~= self.data.tab.open_panel and self.data.tab.select_type == JUMP_TYPE.NORMAL then
		FunOpen.Instance:OpenViewNameByCfg(self.data.tab.open_panel)
	elseif self.data.tab.select_type == JUMP_TYPE.CHAT then
		ChatWGCtrl.Instance:OpenChatWindow(ChatTabIndex[CHANNEL_TYPE.WORLD])
	elseif self.data.tab.select_type == JUMP_TYPE.HUSONG then
		ActIvityHallWGCtrl.Instance:DoHuSong()
	elseif "" ~= self.data.tab.open_panel and self.data.tab.select_type == JUMP_TYPE.BIZUO then
		if nil ~= self.data.tab.open_param and "" ~= self.data.tab.open_param then
			BiZuoWGData.Instance:SetSelectInfo(self.data.tab.open_param)
		end
		FunOpen.Instance:OpenViewNameByCfg(self.data.tab.open_panel)
	end

	if self.data.tab.is_close_panel == 1 then
		ViewManager.Instance:Close(GuideModuleName.OperationActivityView)
	end
end

function HappyKuangHuanTaskRender:SetHappyKhImgInfo()
	local happykh_view_info_cfg = OperationHappyKuangHuanWGData.Instance:GetViewCfgByInterface()
	if nil == happykh_view_info_cfg then
		return
	end

	-- local asset1, bundle1 = ResPath.GetOperationHappyKhImagePath(happykh_view_info_cfg.normal_bg)
	-- self.node_list["root_bg"].image:LoadSprite(asset1, bundle1)

	-- local asset2, bundle2 = ResPath.GetOperationHappyKhImagePath(happykh_view_info_cfg.normal_title_bg)
	-- self.node_list["img9"].image:LoadSprite(asset2, bundle2)
end
