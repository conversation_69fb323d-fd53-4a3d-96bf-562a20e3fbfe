--限时特惠
LimitedTimeOfferView = LimitedTimeOfferView or BaseClass(SafeBaseView)

local MAX_SHOW_NUM = 4 --最大显示数量

function LimitedTimeOfferView:__init()
    self.view_layer = UiLayer.Normal
    self:SetMaskBg()
    self.view_style = ViewStyle.Half
    self.view_name = "LimitedTimeOfferView"
    self:AddViewResource(0, "uis/view/limited_time_offer_prefab", "layout_limited_time_offer")

    self.buy_click_time = 0
end

function LimitedTimeOfferView:ReleaseCallBack()
    if self.show_item_list then
		self.show_item_list:DeleteMe()
		self.show_item_list = nil
    end

    if self.extra_item_list then
        self.extra_item_list:DeleteMe()
        self.extra_item_list = nil
    end

    if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
    end

    if self.suit_display then
        self.suit_display:DeleteMe()
        self.suit_display = nil
    end

    if self.gift_box_tween then
		self.gift_box_tween:Kill()
		self.gift_box_tween = nil
	end

    CountDownManager.Instance:RemoveCountDown("shanhaiboss_view_countdown")
end

function LimitedTimeOfferView:LoadCallBack()
    self.buy_click_time = 0
    self.cur_drag_index = 1
    self.data_length = 0
    self.item_list_num = 0

    self.node_list.btn_close.button:AddClickListener(BindTool.Bind(self.Close, self))

    local cambered_list_data = {
		item_render = LimitedTimeOfferRender,
		asset_bundle = "uis/view/limited_time_offer_prefab",
		asset_name = "reward_item",

		scroll_list = self.node_list.item_show_list,
		center_x = 2,
		center_y = 1356,
		radius_x = 1400,
		radius_y = 1400,
		angle_delta = Mathf.PI / 20.6,
		origin_rotation = Mathf.PI * 0.9271,
		is_clockwise_list = false,
		arg_speed = 0.07,
        drag_to_next_cb = BindTool.Bind(self.OnDragToNextCallBack, self),
		drag_to_last_cb = BindTool.Bind(self.OnDragToLastCallBack, self),
        on_drag_end_cb = BindTool.Bind(self.OnDragEndCallBack, self),
	}

	self.show_item_list = CamberedList.New(cambered_list_data)
    self.show_item_list.drag_dir = -1

    self.extra_item_list = StrengthenAsyncListView.New(LimitedTimeExtraItemRender, self.node_list.extra_item_list)

    if not self.suit_display then
        self.suit_display = OperationActRender.New(self.node_list.model_pos)
        self.suit_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
    end

    self.role_data_change = BindTool.Bind1(self.RoleLevelChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
    self.node_list.btn_all_buy.button:AddClickListener(BindTool.Bind(self.OnClickAllBuy, self))
    self.node_list.btn_rule.button:AddClickListener(BindTool.Bind(self.RuleBtnClick, self))
    self.node_list.btn_all_buy:SetActive(false)

    self.node_list.btn_get.button:AddClickListener(BindTool.Bind(self.GetFreeItem, self))
    self.node_list.extra_get_btn.button:AddClickListener(BindTool.Bind(self.GetExtraItem, self))
end

function LimitedTimeOfferView:OnDragToNextCallBack()
	self.cur_drag_index = self.cur_drag_index + 1
end

function LimitedTimeOfferView:OnDragToLastCallBack()
	self.cur_drag_index = self.cur_drag_index - 1
end

function LimitedTimeOfferView:OnDragEndCallBack()
	self:OnDragEndToIndex(nil, false, self.cur_drag_index)
end

function LimitedTimeOfferView:OnDragEndToIndex(callback, is_click, drag_index)
	if self.show_item_list == nil then
		return
	end

    local to_index
    if self.item_list_num <= MAX_SHOW_NUM then
        to_index = 1
    elseif drag_index > 1 then
        if (MAX_SHOW_NUM + drag_index) > self.item_list_num then
            to_index = self.item_list_num - MAX_SHOW_NUM + 1
        else
            to_index = drag_index
        end
    elseif drag_index <= 1 then
        to_index = 1
    end

    self.cur_drag_index = to_index

	self.show_item_list:ScrollToIndex(to_index, callback, is_click)
end

function LimitedTimeOfferView:GetFreeItem()
    LimitedTimeOfferWGCtrl.Instance:SendLimitedTimeOfferOper()
end

function LimitedTimeOfferView:GetExtraItem()
    LimitedTimeOfferWGCtrl.Instance:SendLimitedTimeOfferExtra()
end

function LimitedTimeOfferView:OnClickAllBuy()
    if self.buy_click_time and (Status.NowTime - self.buy_click_time < 1) then
		return
	end

    self.buy_click_time = Status.NowTime

    local all_buy_cfg = LimitedTimeOfferWGData.Instance:GetCurAllBuyCfg()

    if IsEmptyTable(all_buy_cfg) then
        return
    end

    RechargeWGCtrl.Instance:Recharge(all_buy_cfg.rmb_price, all_buy_cfg.rmb_type, all_buy_cfg.grade)
end

function LimitedTimeOfferView:RoleLevelChange(attr_name, value)
	if attr_name == "level" then
		self:Flush()
	end
end

function LimitedTimeOfferView:ShowIndexCallBack()
    self.node_list["tween_root"].transform.localScale = Vector3.one
    self.node_list["tween_root"].transform.localPosition = Vector3.zero
    ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.LimitedTimeOffer, ACTIVITY_TYPE.LIMITEDTIMEOFFER)
end

function LimitedTimeOfferView:RuleBtnClick()
    local role_tip = RuleTip.Instance
    local other_cfg = LimitedTimeOfferWGData.Instance:GetOtherCfg()
	role_tip:SetContent(other_cfg.probability_show, Language.LimitedTimeOffer.PlayTitle)
end

function LimitedTimeOfferView:OnFlush(param_t, index)
    for k, v in pairs(param_t) do
		if "all" == k then
            self:FlushAll()
        elseif "time_info" == k then
            self:FlushTime()
        end
    end
end

function LimitedTimeOfferView:FlushAll()
    local item_list = LimitedTimeOfferWGData.Instance:GetItemShowList()
    self.item_list_num = #item_list
    self.show_item_list:CreateCellList(#item_list)
    local btn_item_list = self.show_item_list:GetRenderList()
    for k, item in ipairs(btn_item_list) do
        local item_data = item_list[k]
        item:SetData(item_data)
    end

    local extra_item_list = LimitedTimeOfferWGData.Instance:GetExtraItemList()
    self.extra_item_list:SetDataList(extra_item_list)

    local other_cfg = LimitedTimeOfferWGData.Instance:GetOtherCfg()
    self:FlushTime()
    local can_get = LimitedTimeOfferWGData.Instance:GetCanGetFreeItem()
    self.node_list.free_item_red:SetActive(can_get)
    self.node_list.free_item:SetActive(can_get)

    if can_get then
		if self.gift_box_tween then
			self.gift_box_tween:Restart()
		else
            if self.gift_box_tween then
                self.gift_box_tween:Kill()
                self.gift_box_tween = nil
            end

			self.gift_box_tween = DG.Tweening.DOTween.Sequence()
			UITween.ShakeAnimi(self.node_list.btn_get.transform, self.gift_box_tween)
		end
	elseif self.gift_box_tween then
		self.gift_box_tween:Pause()
		self.node_list.btn_get.transform.localRotation = Quaternion.identity
	end

    local can_all_buy = LimitedTimeOfferWGData.Instance:CanAllBuy()
    local is_can_get_reward = LimitedTimeOfferWGData.Instance:IsCanGetExtraItem()
    self.node_list.btn_all_buy:SetActive(can_all_buy)
    self.node_list.tips_texts:SetActive(can_all_buy)
    self.node_list.extra_get_btn:SetActive(is_can_get_reward)

    local all_buy_count = LimitedTimeOfferWGData.Instance:GetAllBuyTimes()
    self.node_list.all_buy_count_txt.text.text = string.format(Language.LimitedTimeOffer.LimitBuy, all_buy_count)

    local all_buy_cfg = LimitedTimeOfferWGData.Instance:GetCurAllBuyCfg()
    if all_buy_cfg.model_show_type > 0 then
        local display_data = {}
	    if all_buy_cfg.model_show_itemid ~= 0 and all_buy_cfg.model_show_itemid ~= "" then
	    	local split_list = string.split(all_buy_cfg.model_show_itemid, "|")
	    	if #split_list > 1 then
	    		local list = {}
	    		for k, v in pairs(split_list) do
	    			list[tonumber(v)] = true
	    		end
	    		display_data.model_item_id_list = list
	    	else
	    		display_data.item_id = all_buy_cfg.model_show_itemid
	    	end
	    end

	    display_data.should_ani = true
        display_data.hide_model_block = true
	    display_data.need_wp_tween = true
        display_data.bundle_name = all_buy_cfg.model_bundle_name
	    display_data.asset_name = all_buy_cfg.model_asset_name
        display_data.render_type = all_buy_cfg.model_show_type - 1

        local pos_x, pos_y = 0, 0
	    local display_pos = all_buy_cfg.display_pos
	    if display_pos and display_pos ~= "" then
	    	local pos_list = string.split(display_pos, "|")
	    	pos_x = tonumber(pos_list[1]) or pos_x
	    	pos_y = tonumber(pos_list[2]) or pos_y
	    end

	    local rot_x, rot_y, rot_z = 0, 0, 0
	    local display_rotation = all_buy_cfg.display_rotation
	    if display_rotation and display_rotation ~= "" then
	    	local rot_list = string.split(display_rotation, "|")
	    	rot_x = tonumber(rot_list[1]) or rot_x
	    	rot_y = tonumber(rot_list[2]) or rot_y
	    	rot_z = tonumber(rot_list[3]) or rot_z
	    end

	    local scale = all_buy_cfg.display_scale
	    scale = (scale and scale ~= "" and scale > 0) and scale or 1

        RectTransform.SetAnchoredPositionXY(self.node_list.model_pos.rect, pos_x, pos_y)

        if all_buy_cfg.model_pos and all_buy_cfg.model_pos ~= "" then
            local pos_list = string.split(all_buy_cfg.model_pos, "|")
            local posx = tonumber(pos_list[1]) or 0
            local posy = tonumber(pos_list[2]) or 0
            local posz = tonumber(pos_list[3]) or 0

            display_data.model_adjust_root_local_position = Vector3(posx, posy, posz)
        end

        display_data.role_rotation = Vector3(rot_x, rot_y, rot_z)
        display_data.model_adjust_root_local_scale = scale
        display_data.model_rt_type = ModelRTSCaleType.L
        --self.node_list.model_pos.rect.rotation = Quaternion.Euler(rot_x, rot_y, rot_z)
        --Transform.SetLocalScaleXYZ(self.node_list.model_pos.transform, scale, scale, scale)

        self.suit_display:SetData(display_data)
    end

    if can_all_buy then
        if not IsEmptyTable(all_buy_cfg) then
            XUI.SetButtonEnabled(self.node_list.btn_all_buy, can_all_buy)
            local price = all_buy_cfg.rmb_price
            local original_price = RoleWGData.GetPayMoneyStr(all_buy_cfg.original_price)
            self.node_list.buy_price_txt.text.text = not can_all_buy and Language.LimitedTimeOffer.AllBuyBtn2 or string.format(Language.LimitedTimeOffer.AllBuyBtn1, price)
            self.node_list.original_price.text.text = string.format(Language.LimitedTimeOffer.Original_price, original_price)
        end
    end
end

function LimitedTimeOfferView:FlushTime()
    self.node_list.count_down_time.text.text = ""
    local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.LIMITEDTIMEOFFER)
    if IsEmptyTable(activity_info) then
        CountDownManager.Instance:RemoveCountDown("shanhaiboss_view_countdown")
        return
    end
    if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
        if CountDownManager.Instance:HasCountDown("shanhaiboss_view_countdown") then
            CountDownManager.Instance:RemoveCountDown("shanhaiboss_view_countdown")
        end
        local end_time = activity_info.end_time
        local cur_time = TimeWGCtrl.Instance:GetServerTime()
        if end_time > cur_time then
            self:UpdateCountDownTime(cur_time, end_time)
            CountDownManager.Instance:AddCountDown("shanhaiboss_view_countdown",
            BindTool.Bind1(self.UpdateCountDownTime, self),
            BindTool.Bind1(self.CompleteCountDownTime, self),
            end_time, nil, 0.5)
        end
    end
end

function LimitedTimeOfferView:UpdateCountDownTime(elapse_time, total_time)
    local last_time = math.floor(total_time - elapse_time)
    local str = string.format(Language.LimitedTimeOffer.CountDownTime, ToColorStr(TimeUtil.FormatSecondDHM8(last_time), COLOR3B.DEFAULT_NUM))
    self.node_list.count_down_time.text.text = str
end

function LimitedTimeOfferView:CompleteCountDownTime()
    if self.node_list.count_down_time then
        self.node_list.count_down_time.text.text = ""
    end
end







LimitedTimeOfferRender = LimitedTimeOfferRender or BaseClass(BaseRender)

function LimitedTimeOfferRender:LoadCallBack()
    self.buy_click_time = 0

    -- if not self.lingyu_item then
    --     self.lingyu_item = ItemCell.New(self.node_list.probility_item1)
    -- end

    if not self.drop_item then
        self.drop_item = ItemCell.New(self.node_list.probility_item0)
    end
	XUI.AddClickEventListener(self.node_list.btn_buy, BindTool.Bind(self.OnClickBuy, self))
end

function LimitedTimeOfferRender:ReleaseCallBack()
	if self.drop_item then
        self.drop_item:DeleteMe()
        self.drop_item = nil
    end

    if self.all_item_list then
        self.all_item_list:DeleteMe()
        self.all_item_list = nil
    end

    -- if self.lingyu_item then
    --     self.lingyu_item:DeleteMe()
    --     self.lingyu_item = nil
    -- end
end

function LimitedTimeOfferRender:OnFlush()
    if not self.data then
        return
    end

    self.view:SetActive(true)
    --self.node_list.box_name.text.text = self.data.gift_name
    self.node_list.buy_send_txt.text.text = string.format(Language.LimitedTimeOffer.send_txt, self.data.return_gold)
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.buy_send_txt.rect)
    local str = string.format("%d", self.data.buy_limit - self.data.cur_buy_times)
    local color1 = self.data.buy_limit - self.data.cur_buy_times > 0 and COLOR3B.GREEN or COLOR3B.RED
    self.node_list.can_buy_count_txt.text.text = string.format(Language.LimitedTimeOffer.LimitBuy, str)

    local price_str = RoleWGData.GetPayMoneyStr(self.data.rmb_price, self.data.rmb_type, self.data.rmb_seq)
    local is_can_buy = self.data.buy_limit - self.data.cur_buy_times > 0
    XUI.SetButtonEnabled(self.node_list.btn_buy, is_can_buy)
    self.node_list.buy_price_txt.text.text = is_can_buy and price_str or Language.LimitedTimeOffer.AllBuyBtn2
    self.node_list.can_buy_count_img:SetActive(is_can_buy)

    if not self.all_item_list then
        self.all_item_list = StrengthenAsyncListView.New(LimitedTimeOfferItemRender, self.node_list.item_list)
    end
    local reward_list = self.data.reward_list
    if not IsEmptyTable(reward_list) then
        local list = {}
        for i = 0, #reward_list do
            list[i+1] = reward_list[i]
        end

        self.all_item_list:SetDataList(list)
    end

    -- self.lingyu_item:SetData({
    --     item_id = COMMON_CONSTS.VIRTUAL_ITEM_GOLD,
    --     num = self.data.return_gold,
    -- })

    -- if self.data.gold_show > 0 then
    --     self.lingyu_item:SetDuoBeiMsg(self.data.gold_show)
    -- end

    self.drop_item:SetData(self.data.reward_item[0])--概率获取的只有一个
    -- if self.data.drop_per >= 10000 then --百分百获得显示名字，否则显示概率获得
    --     local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.reward_item[0].item_id)
    --     self.node_list.probility_txt.text.text = item_cfg.name
    -- else
    --     self.node_list.probility_txt.text.text = Language.LimitedTimeOffer.GaiLvGet
    -- end
end

function LimitedTimeOfferRender:OnClickBuy()
    if self.buy_click_time and (Status.NowTime - self.buy_click_time < 1) then
		return
	end

    self.buy_click_time = Status.NowTime

    if self.data and self.data.buy_limit - self.data.cur_buy_times > 0 then
        local money = self.data.rmb_price
        local rmb_type = self.data.rmb_type
        local rmb_seq = self.data.rmb_seq
        RechargeWGCtrl.Instance:Recharge(money, rmb_type, rmb_seq)
    end
end

LimitedTimeOfferItemRender = LimitedTimeOfferItemRender or BaseClass(BaseRender)

function LimitedTimeOfferItemRender:ReleaseCallBack()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function LimitedTimeOfferItemRender:OnFlush()
    if not self.data then
        return
    end
    self.view:SetActive(true)
    if not self.item then
        self.item = ItemCell.New(self.node_list.item_pos)
    end
    self.item:SetData(self.data)
end

---------------------额外奖励
LimitedTimeExtraItemRender = LimitedTimeExtraItemRender or BaseClass(BaseRender)

function LimitedTimeExtraItemRender:__init()
    self.item = ItemCell.New(self.node_list.item_pos)
end

function LimitedTimeExtraItemRender:ReleaseCallBack()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function LimitedTimeExtraItemRender:OnFlush()
    if not self.data then
        return
    end

    self.view:SetActive(true)
    self.item:SetData(self.data)
    local is_extra_get = LimitedTimeOfferWGData.Instance:IsGetExtraItem()
    local is_can_get_reward = LimitedTimeOfferWGData.Instance:IsCanGetExtraItem()
    self.node_list.already_get:SetActive(is_extra_get)
    self.node_list.fill_flag:SetActive(is_can_get_reward and not is_extra_get)
end

