HalfPurchaseWGData = HalfPurchaseWGData or BaseClass()

function HalfPurchaseWGData:__init()
	if HalfPurchaseWGData.Instance then
		print_error("[HalfPurchaseWGData] Attempt to create singleton twice!")
		return
	end

	HalfPurchaseWGData.Instance = self

	self:InitParam()
	self:InitConfig()
end

function HalfPurchaseWGData:__delete()
	HalfPurchaseWGData.Instance = nil
end

----------------------------init_start-----------------------------
function HalfPurchaseWGData:InitParam()

end

function HalfPurchaseWGData:InitConfig()
	local cfg = ConfigManager.Instance:GetAutoConfig("half_purchase_cfg_auto")
	self.other_cfg = cfg.other[1]
	self.rumor_random_table_cfg = cfg.rumor_random_table
end
-----------------------------init_end------------------------------

function HalfPurchaseWGData:GetOtherCfg()
	return self.other_cfg
end

function HalfPurchaseWGData:GetDescShowSpeed()
	return self.other_cfg.desc_show_speed
end

function HalfPurchaseWGData:GetBarrageMessage()
	local bg_bundle, bg_asset = ResPath.GetNoPackPNG("a3_wucz_zt2")
	local speed = self:GetDescShowSpeed()
	return {
		desc_content = (self.rumor_random_table_cfg[math.random(1, #self.rumor_random_table_cfg)] or {}).conect or "",
		need_random_color = false,
		bg_bundle = bg_bundle,
		bg_asset = bg_asset,
		color_data_list = ITEM_COLOR_DARK,
		text_color = "#591D00",
		move_speed = speed,
	}
end