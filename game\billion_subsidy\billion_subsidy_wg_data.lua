BillionSubsidyWGData = BillionSubsidyWGData or BaseClass()

BillionSubsidyWGData.ShopType =
{
	BYBT = 1,			--百亿补贴.
	XSSD = 2,			--限定商店.
	FYMS = 3,			--付1买3.
	JKJZC = 4,			--九块九专场.
	LYSD = 5,			--灵玉商店.
	DEZG = 6,			--大额直购.
	DRPT = 7,			--多人拼团.
	MRSY = 8,			--每日试用.
	MRLB = 9			--每日礼包.
}

BillionSubsidyWGData.TabIndex2ShopType =
{
	[TabIndex.billion_subsidy_bybt] = BillionSubsidyWGData.ShopType.BYBT,
	[TabIndex.billion_subsidy_xdzk] = BillionSubsidyWGData.ShopType.XSSD,
	[TabIndex.billion_subsidy_fyms] = BillionSubsidyWGData.ShopType.FYMS,
	[TabIndex.billion_subsidy_jkjzc] = BillionSubsidyWGData.ShopType.JKJZC,
	[TabIndex.billion_subsidy_lysd] = BillionSubsidyWGData.ShopType.LYSD,
	[TabIndex.billion_subsidy_dezg] = BillionSubsidyWGData.ShopType.DEZG,
	[TabIndex.billion_subsidy_drpt] = BillionSubsidyWGData.ShopType.DRPT,
	[TabIndex.billion_subsidy_mrsy] = BillionSubsidyWGData.ShopType.MRSY,
	[TabIndex.billion_subsidy_vip] = BillionSubsidyWGData.ShopType.MRLB,
}

BillionSubsidyWGData.RedPocketState = {
    NotComplete = 0,
    Complete = 1,
    Fetched = 2,
}

--多人拼团商品状态.
BillionSubsidyWGData.DRPTActState =
{
	NotTime = 0,			--未到开启时间.
	TimeEnd = 1,			--已结束.
	Activated = 2,			--开启中.
}

-- 可用免单券商店
local CAN_USE_FREE_TICKET_SHOP = {
	[BillionSubsidyWGData.ShopType.BYBT] = true,
	[BillionSubsidyWGData.ShopType.XSSD] = true,
	[BillionSubsidyWGData.ShopType.JKJZC] = true,
	[BillionSubsidyWGData.ShopType.FYMS] = true,
}

-- 可用立减券商店
local CAN_USE_DIRECT_DISCOUNT_TICKET_SHOP = {
	[BillionSubsidyWGData.ShopType.BYBT] = true,
	[BillionSubsidyWGData.ShopType.XSSD] = true,
	[BillionSubsidyWGData.ShopType.JKJZC] = true,
	[BillionSubsidyWGData.ShopType.DEZG] = true,
	[BillionSubsidyWGData.ShopType.FYMS] = true,
}

-- 可用满减券商店
local CAN_USE_FULL_DISCOUNT_TICKET_SHOP = {
	[BillionSubsidyWGData.ShopType.BYBT] = true,
	[BillionSubsidyWGData.ShopType.XSSD] = true,
	[BillionSubsidyWGData.ShopType.DEZG] = true,
	[BillionSubsidyWGData.ShopType.FYMS] = true,
}

function BillionSubsidyWGData:__init()
	if BillionSubsidyWGData.Instance ~= nil then
		ErrorLog("[BillionSubsidyWGData] attempt to create singleton twice!")
		return
	end
	BillionSubsidyWGData.Instance = self

	self:InitParam()
	self:InitCfg()
    RemindManager.Instance:Register(RemindName.BillionSubsidyBYBT, BindTool.Bind(self.GetBYBTRemind, self))
    RemindManager.Instance:Register(RemindName.BillionSubsidyXDZK, BindTool.Bind(self.GetXDZKRemind, self))
	RemindManager.Instance:Register(RemindName.BillionSubsidyVIP, BindTool.Bind(self.GetVIPRemind, self))
	RemindManager.Instance:Register(RemindName.BillionSubsidyFYMS, BindTool.Bind(self.GetFYMSRemind, self))
	RemindManager.Instance:Register(RemindName.BillionSubsidyJKJZC, BindTool.Bind(self.GetJKJZCRemind, self))
	RemindManager.Instance:Register(RemindName.BillionSubsidyDEZG, BindTool.Bind(self.GetDEZGRemind, self))
	RemindManager.Instance:Register(RemindName.BillionSubsidyDRPT, BindTool.Bind(self.GetDRPTRemind, self))
	RemindManager.Instance:Register(RemindName.BillionSubsidyYYHD, BindTool.Bind(self.GetYiYuanRemind, self))

	self.item_change_callback = BindTool.Bind(self.OnItemChangeCallBack, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_change_callback)
end

function BillionSubsidyWGData:__delete()
	BillionSubsidyWGData.Instance = nil

	RemindManager.Instance:UnRegister(RemindName.BillionSubsidyBYBT)
    RemindManager.Instance:UnRegister(RemindName.BillionSubsidyXDZK)
	RemindManager.Instance:UnRegister(RemindName.BillionSubsidyVIP)
	RemindManager.Instance:UnRegister(RemindName.BillionSubsidyFYMS)
	RemindManager.Instance:UnRegister(RemindName.BillionSubsidyJKJZC)
	RemindManager.Instance:UnRegister(RemindName.BillionSubsidyDEZG)
	RemindManager.Instance:UnRegister(RemindName.BillionSubsidyDRPT)
	RemindManager.Instance:UnRegister(RemindName.BillionSubsidyYYHD)

	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_change_callback)
end

function BillionSubsidyWGData:InitParam()
	self.member_level = 1											--会员等级
	self.today_use_pay_one_get_more_times = 0						--今日已使用买一得多次数
	self.fetch_first_open_view_reward = 0							--领取每日首次打开界面奖励标识 0:未领取 1:已领取
	self.first_pay_reward_flag = 0									--首次单笔支付奖励状态(首笔满5返30) 0:未完成 1:已完成可领取 2:已领取
	self.today_used_free_ticket_num = 0								--今日使用免单券数量
	self.today_no_limit_discount_ticket_used_times = 0				--今日立减券使用次数
	self.today_return_reward_fetch_times = 0						--今日满额返现奖励领取次数
	self.today_try_ticket_use_times = 0								--试用券使用次数
	self.today_pay_one_get_more_shop_refresh_times = 0				--今日买一得多商店刷新次数
	self.today_limit_discount_ticket_used_times = 0					--今日满减券使用次数
	self.today_member_reward_fetch_flag = {}						--每日高级会员奖励领取状态 二进制 位序表示会员等级 0表示未领取 1表示已领取 低位到高位
	self.first_open_view_flag = 0									--首次打开界面标识
	self.cumulate_order_num = 0										--累计订单数
	self.cumulate_order_reward_can_fetch_times = 0					--累计订单数奖励可领取次数
	self.today_return_quota = 0										--今日满额返现累计额度
	self.total_saved_chongzhi_num = 0								--总节省的金额
	self.time_limited_discoun_shopt_start_timestamp = 0				--限时折扣商店折扣开始时间
	self.try_ticket_num = 0											--试用期数量

	self.discount_ticket_list = {}									--折扣券信息 ticket_seq:券索引 expend_times:膨胀次数 reduce_quota:减免金额
	self.ten_billion_subsidy_shop_buy_count_list = {}				--百亿补贴商店购买信息
	self.time_limited_discount_shop_buy_count_list = {}				--限时折扣商店购买信息
	self.pay_one_get_more_shop_item_list = {}						--买一得多商店商品信息 item_seq:商品索引 refresh_flag:是否刷新 刷新标识 0 未刷新 1已刷新 buy_count:购买次数
	self.gold_shop_buy_count_list = {}								--灵玉商店购买信息
	self.daily_gift_buy_count_list = {}								--最大每日礼包购买信息
	self.try_ticket_shop_buy_count_list = {}						--试用券商店购买信息
	self.quota_shop_buy_count_list = {}								--定额商店购买信息
	self.high_price_shop_buy_flag = {}								--大额直购购买信息 index:商品索引 value:0表示未购买 1表示已购买
	self.high_price_shop_buy_times = 0								--大额直购立减购买次数
	self.high_price_shop_reduce_flag = {}							--大额直购立减领取标识
	self.share_help_num_list = {}									--大额直购分享助力次数信息 index:商品索引 值value:当前助力次数

	self.is_use_dc = true 	--是否勾选了折扣卷
	self.select_dc_data = {}	--当前选择的折扣券数据
	self.dc_data_list = {}		--总的折扣券数据
	--最优惠的折扣券数据  --free_num :免单券数量  --max_direct_data ：最优惠的立减券数据  --max_full_dc_list : 比立减券折扣金额更多的满减券列表
	self.max_discount_dc_list = {free_data = {}, free_num = 0, max_direct_data = {}, max_full_dc_list = {}}

	self.participate_item_info_list = {}							--多人拼团所有商品团购信息

	self.dezg_item_show_list = {}									--大额直购当前已开启的商品数据.
	self.drpt_item_show_list = {}									--多人拼团当前已开启的商品数据.

	self.is_can_show_vip_blank_tips = true

	self.shop_cart_list = {}                                        -- 购物车数据
	self.shop_cart_open_flag = {}										-- 记录购物车类型的打开状态
	self.shop_cart_input_flag = {}										-- 记录购物车数量选择的打开状态
	self.shop_cart_clear_list = {}										-- 记录待清理列表

	self.yiyuan_grade = 1		-- 一元福利档位
	self.yiyuan_login_day = 0	-- 一元福利天数
	self.yiyuan_buy_flag = 0	-- 一元福利购买标记
	self.yiyuan_day_reward_flag = {}	-- 一元福利奖励领取标记 0无效
	self.yiyuan_day_first_flag = 1	-- 一元活动每天首次打开标识

	self.discount_guide_state = false	--百亿补贴优惠券引导状态
end

function BillionSubsidyWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("ten_billion_subsidy_cfg_auto")
	self.other_cfg = cfg.other[1]
	self.member_rmb_buy_cfg = ListToMap(cfg.member_rmb_buy, "member_level", "cur_member_level")
	self.member_level_cfg = ListToMap(cfg.member_level, "member_level")
	self.member_benefits_cfg = ListToMapList(cfg.member_benefits, "required_level")
	self.grade_cfg = ListToMapList(cfg.grade, "type")
	self.grade_cfg_2 = ListToMap(cfg.grade, "type", "grade")
	self.high_price_shop_cfg = ListToMapList(cfg.high_price_shop, "grade", "item_seq")
	self.high_price_reduce_cfg = ListToMap(cfg.high_price_reduce, "seq")
	self.quota_shop_cfg = ListToMap(cfg.quota_shop, "grade", "item_seq")
	self.pay_one_get_more_shop_cfg = ListToMap(cfg.pay_one_get_more_shop, "grade", "item_seq")
	self.participate_shop_cfg = ListToMapList(cfg.participate_shop, "grade")
	self.daily_gift_cfg = ListToMap(cfg.daily_gift, "grade")
	self.discount_ticket_cfg = ListToMap(cfg.discount_ticket, "ticket_seq")
	self.ten_billion_subsidy_shop_cfg = ListToMap(cfg.ten_billion_subsidy_shop, "grade", "item_seq")
    self.time_limit_shop_cfg = ListToMap(cfg.time_limit_shop, "grade", "item_seq")
    self.try_shop_cfg = ListToMap(cfg.try_shop, "grade", "item_seq")
	self.gold_shop_cfg = ListToMap(cfg.gold_shop, "grade", "item_seq")
	self.oneyuan_welfare_cfg = ListToMap(cfg.oneyuan_welfare, "grade")
	self.oneyuan_welfare_reward_cfg = ListToMapList(cfg.oneyuan_welfare_reward, "grade")
end

function BillionSubsidyWGData:OnItemChangeCallBack(change_item_id)
    local free_ticket_item_id = self:GetFreeCouponItemId()
    if change_item_id == free_ticket_item_id then
        self:SetDiscountCouponAllDataList()
		BillionSubsidyWGCtrl.Instance:FlushCurShowIndex()
    end
end

function BillionSubsidyWGData:GetOtherCfg()
	return self.other_cfg
end

--获取免单券id
function BillionSubsidyWGData:GetFreeCouponItemId()
	return self.other_cfg.free_ticket_item_id
end

--当前档位.
function BillionSubsidyWGData:GetGrade(shop_type)
	local grade = 0
	local grade_cfg = self.grade_cfg[shop_type or 1]
	if not grade_cfg then
		return grade
	end

	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	for i, cfg in ipairs(grade_cfg) do
		if server_day >= cfg.start_open_day and server_day <= cfg.end_open_day then
			grade = cfg.grade
			break
		end
	end

	return grade
end

--当前会员等级表
function BillionSubsidyWGData:GetMemberLevelCfg()
	local member_level = self.member_level or 1
	return self.member_level_cfg[member_level] or {}
end

--检测商品是否开放显示.
function BillionSubsidyWGData:CheckIsShowItemByGrade(shop_type, grade)
	local cfg = (self.grade_cfg_2[shop_type] or {})[grade]
	if not cfg then
		return false
	end

	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	return server_day >= cfg.start_open_day and server_day <= cfg.end_open_day
end

--获取商品的结束时间.
function BillionSubsidyWGData:GetItemEndTimeByGrade(shop_type, grade)
	local end_time = 0		--结束时间.

	local cfg = (self.grade_cfg_2[shop_type] or {})[grade]
	if not cfg then
		return end_time
	end

	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if server_day >= cfg.start_open_day and server_day <= cfg.end_open_day then
		local today_rest_time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())  --获取当天剩余时间
		end_time = (cfg.end_open_day - server_day) * 24 * 3600 + today_rest_time
	end

	return end_time
end

--------------------协议数据 start-------------------------
--百亿补贴总信息.
function BillionSubsidyWGData:SetAllInfo(protocol)
	self.member_level = protocol.member_level																					--会员等级
	self.today_use_pay_one_get_more_times = protocol.today_use_pay_one_get_more_times											--今日已使用买一得多次数
	self.fetch_first_open_view_reward = protocol.fetch_first_open_view_reward													--领取每日首次打开界面奖励标识 0:未领取 1:已领取
	self.first_pay_reward_flag = protocol.first_pay_reward_flag																	--首次单笔支付奖励状态(首笔满5返30) 0:未完成 1:已完成可领取 2:已领取
	self.today_used_free_ticket_num = protocol.today_used_free_ticket_num														--今日使用免单券数量
	self.today_no_limit_discount_ticket_used_times = protocol.today_no_limit_discount_ticket_used_times							--今日立减券使用次数
	self.today_return_reward_fetch_times = protocol.today_return_reward_fetch_times												--今日满额返现奖励领取次数
	self.today_try_ticket_use_times = protocol.today_try_ticket_use_times														--试用券使用次数
	self.today_pay_one_get_more_shop_refresh_times = protocol.today_pay_one_get_more_shop_refresh_times							--今日买一得多商店刷新次数
	self.today_limit_discount_ticket_used_times = protocol.today_limit_discount_ticket_used_times								--今日满减券使用次数
	self.today_member_reward_fetch_flag = protocol.today_member_reward_fetch_flag												--每日高级会员奖励领取状态    二进制 位序表示会员等级 0表示未领取 1表示已领取 低位到高位
	self.first_open_view_flag = protocol.first_open_view_flag																	--首次打开界面标识
	self.cumulate_order_num = protocol.cumulate_order_num																		--累计订单数
	self.cumulate_order_reward_can_fetch_times = protocol.cumulate_order_reward_can_fetch_times									--累计订单数奖励可领取次数
	self.high_price_shop_buy_flag = protocol.high_price_shop_buy_flag															--大额直购购买信息 二进制 位序表示商品索引 0表示未购买 1表示已购买
	self.today_return_quota = protocol.today_return_quota																		--今日满额返现累计额度
	self.total_saved_chongzhi_num = protocol.total_saved_chongzhi_num															--总节省的金额
	self.time_limited_discoun_shopt_start_timestamp = protocol.time_limited_discoun_shopt_start_timestamp						--限时折扣商店折扣开始时间(百亿补贴，限时折扣共用此时间)
	self.try_ticket_num = protocol.try_ticket_num																				--试用期数量

	self.discount_ticket_list = protocol.discount_ticket_list																	--折扣券信息
	self.ten_billion_subsidy_shop_buy_count_list = protocol.ten_billion_subsidy_shop_buy_count_list								--百亿补贴商店购买信息
	self.time_limited_discount_shop_buy_count_list = protocol.time_limited_discount_shop_buy_count_list							--限时折扣商店购买信息
	self.pay_one_get_more_shop_item_list = protocol.pay_one_get_more_shop_item_list												--买一得多商店商品信息
	self.gold_shop_buy_count_list = protocol.gold_shop_buy_count_list															--灵玉商店购买信息
	self.daily_gift_buy_count_list = protocol.daily_gift_buy_count_list															--最大每日礼包购买信息
	self.try_ticket_shop_buy_count_list = protocol.try_ticket_shop_buy_count_list												--试用券商店购买信息
	self.quota_shop_buy_count_list = protocol.quota_shop_buy_count_list															--定额商店购买信息

	self:SetDiscountCouponAllDataList()
end

-- 单笔首付和满额返现信息更新
function BillionSubsidyWGData:SetFirstPayAndReturnRewardInfo(protocol)
	self.first_pay_reward_flag = protocol.first_pay_reward_flag
	self.today_return_reward_fetch_times = protocol.today_return_reward_fetch_times
	self.today_return_quota = protocol.today_return_quota
end

-- 折扣券和试用券数据更新
function BillionSubsidyWGData:SetAllTicketInfoUpdate(protocol)
	self.try_ticket_num = protocol.try_ticket_num
	self.discount_ticket_list = protocol.discount_ticket_list

	self:SetDiscountCouponAllDataList()
end

-- 单个折扣券数据更新
function BillionSubsidyWGData:SetSingleTicketInfoUpdate(protocol)
	local ticket_index = protocol.ticket_index
	local discount_ticket_info = protocol.discount_ticket_info
	self.discount_ticket_list[ticket_index] = discount_ticket_info
	self:SetDiscountCouponAllDataList()
end

-- 总节省金额更新
function BillionSubsidyWGData:SetParticipateInfoUpdate(protocol)
	self.total_saved_chongzhi_num  = protocol.total_saved_chongzhi_num
end

-- 会员信息更新(领取试用券，折扣券, 会员等级变化)
function BillionSubsidyWGData:SetMemberDailyRewardUpdate(protocol)
	self.fetch_first_open_view_reward = protocol.fetch_first_open_view_reward
	self.today_member_reward_fetch_flag = protocol.today_member_reward_fetch_flag
	self.member_level = protocol.member_level
	self:SetDiscountCouponAllDataList()
end

-- 会员累计订单数量奖励信息更新(领取免单券)
function BillionSubsidyWGData:SetCumulateOrderNumInfoUpdate(protocol)
	self.cumulate_order_num = protocol.cumulate_order_num
	self.cumulate_order_reward_can_fetch_times = protocol.cumulate_order_reward_can_fetch_times
end

-- 百亿补贴会员每日礼包单个商品数据更新
function BillionSubsidyWGData:SetBYBTVIPDailyShopSingleItemUpdate(protocol)
	self.daily_gift_buy_count_list[protocol.vip_daily_gift_item_seq] = protocol.vip_daily_gift_buy_countg
end

--多人拼团商品总信息.
function BillionSubsidyWGData:SetDRPTShopAllInfo(protocol)
	self.participate_item_info_list = protocol.item_info_list
end

--大额直购购买信息.
function BillionSubsidyWGData:SetDEZGShopItemUpdate(protocol)
    self.high_price_shop_buy_flag = protocol.high_price_shop_buy_flag
	self.high_price_shop_buy_times = protocol.high_price_shop_buy_times
	self.high_price_shop_reduce_flag = protocol.high_price_shop_reduce_flag
end

--大额直购分享助力次数信息 index:商品索引 值value:当前助力次数.
function BillionSubsidyWGData:SetDEZGShareHelpTimesInfo(protocol)
	self.share_help_num_list = protocol.share_help_num
end

--优惠券使用次数更新
function BillionSubsidyWGData:SetTicketUseLimitInfo(protocol)
	self.today_used_free_ticket_num = protocol.today_used_free_ticket_num
	self.today_no_limit_discount_ticket_used_times = protocol.today_no_limit_discount_ticket_used_times
	self.today_limit_discount_ticket_used_times = protocol.today_limit_discount_ticket_used_times
end

--获取百亿补贴会员等级.
function BillionSubsidyWGData:GetMemberLevel()
	return self.member_level or 1
end

--是否首次打开界面
function BillionSubsidyWGData:GetIsFirstOpenView()
	return self.first_open_view_flag
end

--设置首次
function BillionSubsidyWGData:SetFirstOpenViewFlag(flag)
	self.first_open_view_flag = flag
end

--------------------协议数据 end-------------------------

--------------------大额直购商店 start-------------------------
function BillionSubsidyWGData:GetDEZGCfg()
	self:UpdateDEZGCfg()
	return self.dezg_item_show_list
end

function BillionSubsidyWGData:UpdateDEZGCfg()
	self.dezg_item_show_list = {}
	local grade = self:GetGrade(BillionSubsidyWGData.ShopType.DEZG)
	local cur_member_level = self:GetMemberLevel()

	local shop_cfg = self.high_price_shop_cfg[grade]

	if shop_cfg then
		for index, data in pairs(shop_cfg) do
			for k, v in pairs(data) do
				if v.member_level == cur_member_level then
					self.dezg_item_show_list[#self.dezg_item_show_list + 1] = v
				end
			end
		end
	end

	if not IsEmptyTable(self.dezg_item_show_list) then
		table.sort(self.dezg_item_show_list, SortTools.KeyLowerSorter("item_seq"))
	end
end

function BillionSubsidyWGData:GetDEZGCfgBySeq(item_seq)
	for k, v in ipairs(self:GetDEZGCfg()) do
		if v.item_seq == item_seq then
			return v
		end
	end

	return nil
end

--获取大额直购商品减免额度
function BillionSubsidyWGData:GetDEZGShopCurReduceValue()
	if not self.high_price_reduce_cfg then
		return 0
	end

	local reduce_value = 0
	for k, v in pairs(self.high_price_reduce_cfg) do
		if self.high_price_shop_buy_times < v.buy_times then
			return reduce_value
		else
			reduce_value = v.reduce_value
		end
	end

	return reduce_value
end

--获取大额直购商品减免奖励是否已经领取
function BillionSubsidyWGData:GetDEZGShopReduceRewardIsReceive(seq)
	return self.high_price_shop_reduce_flag[seq] == 1 and true or false
end

--获取大额直购商品减免次数
function BillionSubsidyWGData:GetDEZGShopReduceCount()
	return self.high_price_shop_buy_times
end

--获取大额直购商品减免奖励是否可以领取
function BillionSubsidyWGData:GetDEZGShopReduceRewardIsCanReceive(seq)
	local is_receive = self:GetDEZGShopReduceRewardIsReceive(seq)
	local is_reach = self.high_price_shop_buy_times >= self.high_price_reduce_cfg[seq].buy_times
	return is_reach and not is_receive
end

--获取大额直购商品减免红点
function BillionSubsidyWGData:GetDEZGShopReduceIsRemind()
	for k, v in pairs(self.high_price_reduce_cfg) do
		local is_can_receive = self:GetDEZGShopReduceRewardIsCanReceive(k)
		if is_can_receive then
			return true
		end
	end

	return false
end

--获取大额直购商品减免配置
function BillionSubsidyWGData:GetDEZGShopReduceCfg()
	return self.high_price_reduce_cfg
end

--获取大额直购商品减免配置通过seq
function BillionSubsidyWGData:GetDEZGShopReduceCfgBySeq(seq)
	return self.high_price_reduce_cfg[seq]
end

--检测是否已购买大额直购商品.
function BillionSubsidyWGData:CheckHaveBuyDEZGShopItemBySeq(item_seq)
	return self.high_price_shop_buy_flag[item_seq] == 1
end

--获取大额直购商品剩余购买次数.
function BillionSubsidyWGData:GetBuyDEZGShopItemCountBySeq(item_seq)
	return self.high_price_shop_buy_flag[item_seq] == 1 or 0 or 1
end

--获取大额直购分享助力次数.
function BillionSubsidyWGData:GetDEZGShareHelpTimesInfoBySeq(item_seq)
	return self.share_help_num_list[item_seq] or 0
end

--获取大额直购助力次数进度条.
function BillionSubsidyWGData:GetDEZGHelpTimesProgress(item_seq, value)
	local cur_progress = 0
	local cfg = self:GetDEZGHelpNumList(item_seq)

	if IsEmptyTable(cfg) or not value then
		return cur_progress
	end

	local progress_list = {0, 0.5, 1}			--对应的进度条值

	for k, v in pairs(progress_list) do
		local seq = k - 1
		local length = #progress_list
		local cur_need = cfg[seq] and cfg[seq] or 0
		local next_need = cfg[seq + 1] and cfg[seq + 1] or cfg[#cfg]
		local cur_value = progress_list[seq] and progress_list[seq] or 0
		local next_value = progress_list[seq + 1] and progress_list[seq + 1] or progress_list[length]
		if value > cur_need and value <= next_need then
			cur_progress = (value - cur_need) * (next_value - cur_value) / (next_need - cur_need) + cur_value
			break
		elseif value > cfg[#cfg] then
			cur_progress = progress_list[length]
			break
		end
	end
	return cur_progress
end

--获取大额直购助力人数要求列表.
function BillionSubsidyWGData:GetDEZGHelpNumList(item_seq)
	local list = {}
	local cfg = self:GetDEZGCfgBySeq(item_seq)
	if not cfg then
		return list
	end

	for i = 1, 3 do
		local num = cfg["helper_num_" .. i]
		table.insert(list, num)
	end

	return list
end

function BillionSubsidyWGData:GetDEZGRemind()
	-- if self:GetHasRedPocketCanFetch() then
	-- 	return 1
	-- end

	if self:GetDEZGShopReduceIsRemind() then
		return 1
	end

	return 0
end

--------------------大额直购商店 end-------------------------

--------------------多人拼团 start-------------------------
function BillionSubsidyWGData:GetDRPTData()
	self:UpdateDRPTCfg()
	return self.drpt_item_show_list
end

--是否有不是已结束状态的拼团
function BillionSubsidyWGData:GetDRPTIsHaveNotEndData()
	for k, v in pairs(self.drpt_item_show_list) do
		local act_state = BillionSubsidyWGData.Instance:GetDRPTItemIsOpenBySeq(v.item_seq)
		if act_state ~= BillionSubsidyWGData.DRPTActState.TimeEnd then
			return true
		end
	end

	return false
end

function BillionSubsidyWGData:GetDRPTCfg()
	local grade = self:GetGrade(BillionSubsidyWGData.ShopType.DRPT)
	local shop_cfg = self.participate_shop_cfg[grade]
	local cfg_list ={}
	if shop_cfg then
		for index, data in pairs(shop_cfg) do
			cfg_list[#cfg_list + 1] = data
		end
	end

	if not IsEmptyTable(cfg_list) then
		table.sort(cfg_list, SortTools.KeyLowerSorter("item_seq"))
	end

	return cfg_list
end

function BillionSubsidyWGData:UpdateDRPTCfg()
	self.drpt_item_show_list = {}
	local grade = self:GetGrade(BillionSubsidyWGData.ShopType.DRPT)
	local shop_cfg = self.participate_shop_cfg[grade]

	if shop_cfg then
		for index, data in pairs(shop_cfg) do
			local player_num = self:GetDRPTPlayerNumBySeq(data.item_seq)
			local is_max = player_num >= data.max_participate_num
			local is_part = self:GetDRPTIsPartBySeq(data.item_seq)
			local act_state = self:GetDRPTItemIsOpenBySeq(data.item_seq)

			if not ((is_max or act_state == BillionSubsidyWGData.DRPTActState.TimeEnd) and not is_part) then
				if data.pre_item_seq >= 0 then
					local pre_player_num = self:GetDRPTPlayerNumBySeq(data.pre_item_seq)
					local pre_cfg = self:GetDRPTCfgBySeqAndGrade(data.pre_item_seq, grade)
					if pre_cfg and pre_player_num >= pre_cfg.fetch_reward_participate_num_limit
					and act_state ~= BillionSubsidyWGData.DRPTActState.NotTime then
						self.drpt_item_show_list[#self.drpt_item_show_list + 1] = data
					end
				else
					self.drpt_item_show_list[#self.drpt_item_show_list + 1] = data
				end
			end
		end
	end

	if not IsEmptyTable(self.drpt_item_show_list) then
		table.sort(self.drpt_item_show_list, SortTools.KeyLowerSorter("item_seq"))
	end
end

--当前拼团自己是否参与
function BillionSubsidyWGData:GetDRPTIsPartBySeq(item_seq)
	local participate_info = self:GetDRPTPlayerInfoBySeq(item_seq)
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	if participate_info then
		for k, v in pairs(participate_info) do
			if v.uid == role_id then
				return true
			end
		end
	end

	return false
end

function BillionSubsidyWGData:GetDRPTCfgBySeq(item_seq)
	for k, v in ipairs(self:GetDRPTCfg()) do
		if v.item_seq == item_seq then
			return v
		end
	end

	return nil
end

function BillionSubsidyWGData:GetDRPTCfgBySeqAndGrade(item_seq, grade)
	local shop_cfg = self.participate_shop_cfg[grade]
	if shop_cfg then
		for index, data in pairs(shop_cfg) do
			if data.item_seq == item_seq then
				return data
			end
		end
	end

	return nil
end

--获取当前拼团的开启信息.
function BillionSubsidyWGData:GetDRPTItemIsOpenBySeq(item_seq)
	local act_state = BillionSubsidyWGData.DRPTActState.NotTime
	local start_time = 0	--开始时间.
	local rest_time = 0		--剩余时间.

	local cfg = self:GetDRPTCfgBySeq(item_seq)
	if not cfg then
		return act_state, start_time, rest_time
	end

	local pass_time = TimeUtil.GetTodayPassTime(TimeWGCtrl.Instance:GetServerTime())
	if pass_time < cfg.start_time then
		act_state = BillionSubsidyWGData.DRPTActState.NotTime
	elseif pass_time > cfg.end_time then
		act_state = BillionSubsidyWGData.DRPTActState.TimeEnd
	else
		act_state = BillionSubsidyWGData.DRPTActState.Activated
	end
	start_time = cfg.start_time
	rest_time = cfg.end_time - pass_time

	return act_state, start_time, rest_time
end

--获取多人拼团的玩家信息.
function BillionSubsidyWGData:GetDRPTPlayerInfoBySeq(item_seq)
	return (self.participate_item_info_list[item_seq] or {}).participate_info or nil
end

--获取多人拼团的参团人数.
function BillionSubsidyWGData:GetDRPTPlayerNumBySeq(item_seq)
	return (self.participate_item_info_list[item_seq] or {}).participate_player_num or 0
end

--获取团购奖励领取状态.
function BillionSubsidyWGData:GetDRPTRewardInfoBySeq(item_seq)
	return (self.participate_item_info_list[item_seq] or {}).parparticipate_reward_fetch_flag or nil
end

--获取玩家的参团次数.
function BillionSubsidyWGData:GetDRPTRoleNumBySeq(item_seq)
	local role_buy_num = 0
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()

	local info = self:GetDRPTPlayerInfoBySeq(item_seq)
	if info then
		for k, v in pairs(info) do
			if v.uid == role_id then
				role_buy_num = role_buy_num + 1
			end
		end
	end

	return role_buy_num
end

--可领取奖励次数.
function BillionSubsidyWGData:GetDRPTCanGetRewardNumBySeq(item_seq)
	local role_buy_num = self:GetDRPTRoleNumBySeq(item_seq)
	local role_can_get_num = 0
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()

	local reward_info = self:GetDRPTRewardInfoBySeq(item_seq)
	local info = self:GetDRPTPlayerInfoBySeq(item_seq)
	if info then
		for k, v in pairs(info) do
			if v.uid == role_id and reward_info[k] == 0 then
				role_can_get_num = role_can_get_num + 1
			end
		end
	end

	return role_can_get_num
end

function BillionSubsidyWGData:GetDRPTIsGetRewardBySeq(item_seq)
	local drpt_cfg = self:GetDRPTCfgBySeq(item_seq)
	if not drpt_cfg then
		return false
	end

	local player_num = self:GetDRPTPlayerNumBySeq(item_seq)
	local is_part = self:GetDRPTIsPartBySeq(item_seq)
	if player_num < drpt_cfg.fetch_reward_participate_num_limit or not is_part then
		return false
	end

	local role_can_get_num = self:GetDRPTCanGetRewardNumBySeq(item_seq)
	if role_can_get_num <= 0 then
		return true
	end

	return false
end

function BillionSubsidyWGData:GetDRPTRemind()
	local is_red = 0
	local data = BillionSubsidyWGData.Instance:GetDRPTData()
	if not data then
		return is_red
	end

	for k, v in pairs(data) do
		local player_num = BillionSubsidyWGData.Instance:GetDRPTPlayerNumBySeq(v.item_seq)
		local role_can_get_num = BillionSubsidyWGData.Instance:GetDRPTCanGetRewardNumBySeq(v.item_seq)
		if role_can_get_num > 0 and player_num >= v.fetch_reward_participate_num_limit then
			is_red = 1
			break
		end
	end

	return is_red
end

function BillionSubsidyWGData:GetDRPTRemindBySeq(item_seq)
	local is_red = false
	local drpt_cfg = BillionSubsidyWGData.Instance:GetDRPTCfgBySeq(item_seq)
	if not drpt_cfg then
		return is_red
	end

	local player_num = BillionSubsidyWGData.Instance:GetDRPTPlayerNumBySeq(item_seq)
	local role_can_get_num = BillionSubsidyWGData.Instance:GetDRPTCanGetRewardNumBySeq(item_seq)
	is_red = role_can_get_num > 0 and player_num >= drpt_cfg.fetch_reward_participate_num_limit

	return is_red
end

--------------------多人拼团 end-------------------------

--------------------九块九专场start-------------------------
-- 九块九专场单个商品信息
function BillionSubsidyWGData:SetJKJZCShopSingleItemInfo(protocol)
	self.quota_shop_buy_count_list[protocol.item_seq] = protocol.buy_count
end

-- 九块九专场商品配置
function BillionSubsidyWGData:GetJKJZCShopCfg()
	local grade = self:GetGrade(BillionSubsidyWGData.ShopType.JKJZC)
	return self.quota_shop_cfg[grade] or {}
end

function BillionSubsidyWGData:GetJKJZCShopItemGradeData()
    local shop_cfg = self:GetJKJZCShopCfg()
	local data_list = {}
    for k, v in pairs(shop_cfg) do
        local data = {}
        data.grade = v.grade
        data.item_seq = v.item_seq
        data.price = v.price
        data.reward = v.reward
        data.buy_limit = v.buy_limit
		data.show_price = v.show_price
		data.discount = v.discount
        local buy_count = BillionSubsidyWGData.Instance:GetJKJZCShopBuyCount(v.item_seq)
        local is_buy_done = v.buy_limit > 0 and buy_count >= v.buy_limit
        data.sort = is_buy_done and 1 or 0
        data.is_buy_done = is_buy_done
        table.insert(data_list, data)
    end

    table.sort(data_list, SortTools.KeyLowerSorters("sort", "item_seq"))

    return data_list
end

-- 九块九专场商品配置BySeq
function BillionSubsidyWGData:GetJKJZCShopCfgBySeq(seq)
	local shop_cfg_list = self:GetJKJZCShopCfg()
	return shop_cfg_list[seq] or {}
end

-- 购买次数
function BillionSubsidyWGData:GetJKJZCShopBuyCount(seq)
	return self.quota_shop_buy_count_list[seq] or 0
end

-- 9.9专场是否开启是否开启
function BillionSubsidyWGData:GetJKJZCShopOpen()
	local menber_level_cfg = self:GetMemberLevelCfg()
	return menber_level_cfg.open_quota_shop == 1
end

function BillionSubsidyWGData:GetJKJZCShopOpenMemberLevel()
	for k, v in pairs(self.member_level_cfg) do
		if v.open_quota_shop == 1 then
			return v.member_level
		end
	end

	return 0
end

function BillionSubsidyWGData:GetJKJZCRemind()
	if not FunOpen.Instance:GetFunIsOpenByViewNameAndNumberIndex(GuideModuleName.BillionSubsidy, TabIndex.billion_subsidy_jkjzc) then
		return 0
	end

	-- if self:GetHasRedPocketCanFetch() then
	-- 	return 1
	-- end

	return 0
end

--------------------九块九专场end-------------------------

--------------------付一买三start-------------------------

-- 买一付三商店信息
function BillionSubsidyWGData:SetPayOneGetMoreShopInfo(protocol)
	self.today_use_pay_one_get_more_times = protocol.today_use_pay_one_get_more_times
	self.today_pay_one_get_more_shop_refresh_times = protocol.today_pay_one_get_more_shop_refresh_times
	self.pay_one_get_more_shop_item_list = protocol.pay_one_get_more_shop_item_list
end

function BillionSubsidyWGData:GetB1G3ShopCfg()
	local grade = self:GetGrade(BillionSubsidyWGData.ShopType.FYMS)
	return self.pay_one_get_more_shop_cfg[grade] or {}
end

function BillionSubsidyWGData:GetB1G3ShopCfgBySeq(item_seq)
	local grade = self:GetGrade(BillionSubsidyWGData.ShopType.FYMS)
	return (self.pay_one_get_more_shop_cfg[grade] or {})[item_seq] or {}
end

function BillionSubsidyWGData:GetB1G3ShopList()
	
	local shop_cfg_list = self:GetB1G3ShopCfg()
	local show_list = {}
	for k, v in pairs(self.pay_one_get_more_shop_item_list) do
		if shop_cfg_list[k] and v.refresh_flag == 1 then
			v.cfg = shop_cfg_list[k]
			v.item_seq = shop_cfg_list[k].item_seq
			v.rmb_seq = shop_cfg_list[k].rmb_seq
			v.rmb_type = shop_cfg_list[k].rmb_type
			v.price = shop_cfg_list[k].price
			v.sort = v.buy_count > 0 and 1 or 0
			table.insert(show_list, v)
		end
	end
	table.sort(show_list, SortTools.KeyLowerSorter("sort", "item_seq"))
	return show_list
	
	--[[
	local shop_cfg_list = self:GetB1G3ShopCfg()
	local show_list = {}
	for k, v in pairs(shop_cfg_list) do
		show_list[k] = {
			item_seq = k,
			cfg = v,
			buy_count = 0,
		}
	end
	return show_list
	]]
end

function BillionSubsidyWGData:GetB1G3TodayBuyTimes()
	return self.today_use_pay_one_get_more_times or 0
end

--B1G3是否限制购买 0 未限制 1 未开启VIP特权 2 达到购买上限
function BillionSubsidyWGData:GetB1G3TodayBuyIsLimited()
	local today_buy_time = self:GetB1G3TodayBuyTimes()
	local menber_level_cfg = self:GetMemberLevelCfg()
	if menber_level_cfg.pay_one_get_more_daily_use_times <= 0 then
		return 1
	elseif today_buy_time >= menber_level_cfg.pay_one_get_more_daily_use_times then
		return 2
	else
		return 0
	end
end

function BillionSubsidyWGData:GetB1G3TodayBuyNotLimitedVIPLevel()
	for k, v in pairs(self.member_level_cfg) do
		if v.pay_one_get_more_daily_use_times > 0 then
			return v.member_level
		end
	end

	return 0
end


---买一得多刷新次数信息
---@return integer 今日已刷次数
---@return integer 总免费次数
---@return integer 刷新花费
function BillionSubsidyWGData:GetB1G3TodayRefreshTimes()
	local menber_level_cfg = self:GetMemberLevelCfg()
	return self.today_pay_one_get_more_shop_refresh_times, menber_level_cfg.pay_one_get_more_daily_free_refresh_times, menber_level_cfg.pay_one_get_more_refresh_times_cost_gold
end

-- 可选数量
function BillionSubsidyWGData:GetB1G3CanSelectNum()
	local menber_level_cfg = self:GetMemberLevelCfg()
	return menber_level_cfg.pay_one_get_more_choose_item_num or 3
end

function BillionSubsidyWGData:GetFYMSRemind()
	if not FunOpen.Instance:GetFunIsOpenByViewNameAndNumberIndex(GuideModuleName.BillionSubsidy, TabIndex.billion_subsidy_fyms) then
		return 0
	end

	-- if self:GetHasRedPocketCanFetch() then
	-- 	return 1
	-- end

	return 0
end

--------------------付一买三end-------------------------

--------------------会员激活start-------------------------

--获取会员特权配置
function BillionSubsidyWGData:GetMenberBenefitsList()
	local benefits_data_list = {}
	for i, v in ipairs(self.member_benefits_cfg) do
		local data = {}
		data.member_level = v[1].required_level
		data.cfg = v
		table.insert(benefits_data_list, data)
	end
	return benefits_data_list
end

--获取会员直购配置
function BillionSubsidyWGData:GetMenberRmbBuy(member_level)
	local cur_member_level = self:GetMemberLevel()
	return (self.member_rmb_buy_cfg[member_level] or {})[cur_member_level] or {}
end

--------------------会员激活end-------------------------

--------------------VIP首页start-------------------------
function BillionSubsidyWGData:GetVIPRemind()
    if self:GetDCExpandRemind() then
        return 1
    end

    if self:GetDCReceiveRemind() then
        return 1
    end

	if self:GetFCReceiveRemind() then
        return 1
    end

    return 0
end

function BillionSubsidyWGData:SetShowVipBlankTipsFlag(flag)
	self.is_can_show_vip_blank_tips = flag
end

function BillionSubsidyWGData:GetShowVipBlankTipsFlag()
	return self.is_can_show_vip_blank_tips
end

--获取免单券使用次数
function BillionSubsidyWGData:GetUsedFreeTicketNum()
	return self.today_used_free_ticket_num
end

--获取立减券使用次数
function BillionSubsidyWGData:GetUsedDiscountTicketNum()
	return self.today_no_limit_discount_ticket_used_times
end

--当前是否有折扣券可以膨胀
function BillionSubsidyWGData:GetDCExpandRemind()
	local member_level_cfg = self:GetCurMemberLevelCfg()
	local discount_ticket_list = self:GetDiscountCouponList()
	local cur_can_extend_times = member_level_cfg.discount_ticket_extend_times

	for k, v in pairs(discount_ticket_list) do
		if v.ticket_seq > 0 and v.expend_times and v.expend_times < cur_can_extend_times then
			return true
		end
	end

    return false
end

--当前是否能进行对应数量的一键膨胀
function BillionSubsidyWGData:GetIsCanExpand(num)
	local discount_ticket_list = self:GetDiscountCouponList()
	local member_level_cfg = self:GetCurMemberLevelCfg()
	if member_level_cfg.discount_ticket_extend_times < num then
		return false
	end

	for k, v in pairs(discount_ticket_list) do
		if v.ticket_seq > 0 and v.expend_times and v.expend_times < num then
			return true
		end
	end

    return false
end

--当前是否有折扣券可以领取
function BillionSubsidyWGData:GetDCReceiveRemind()
	local member_level = self:GetMemberLevel()
    local today_member_reward_fetch_flag = self:GetTodayMemberRewardFetchFlag()

	if member_level > 1 and today_member_reward_fetch_flag == 0 then
		return true
	end

    return false
end

--当前是否有免单券可以领取
function BillionSubsidyWGData:GetFCReceiveRemind()
    local member_level_cfg = self:GetCurMemberLevelCfg()
    local cumulate_order_reward_can_fetch_times = self:GetCumulateOrderRewardCanFetchTimes()

	if member_level_cfg.can_fetch_cumulate_order_num_reward == 1 and cumulate_order_reward_can_fetch_times > 0 then
		return true
	end

    return false
end

--获取每日礼包配置
function BillionSubsidyWGData:GetDailyGiftCfg()
	local grade = self:GetGrade(BillionSubsidyWGData.ShopType.MRLB)
    return self.daily_gift_cfg[grade]

end

--获取折扣券配置
function BillionSubsidyWGData:GetDiscountTicketCfgBySeq(seq)
    return self.discount_ticket_cfg[seq]
end

--获取当前VIP等级配置
function BillionSubsidyWGData:GetCurMemberLevelCfg()
	local level = self:GetMemberLevel()
    return self.member_level_cfg[level]
end

--获取VIP等级配置
function BillionSubsidyWGData:GetMemberLevelCfgByLevel(level)
    return self.member_level_cfg[level]
end

--获取最大VIP等级
function BillionSubsidyWGData:GetMaxMemberLevel()
    return #self.member_level_cfg or 0
end

--获取当前是否会员满级
function BillionSubsidyWGData:GetIsMaxMemberLevel()
	local level = self:GetMemberLevel()
    return #self.member_level_cfg == level
end

-- 获取会员每日礼包是否购买
function BillionSubsidyWGData:GetBYBTVIPDailyShopSingleItemIsBuy()
	local cfg = self:GetDailyGiftCfg()
	local daily_gift_buy_count = self.daily_gift_buy_count_list[cfg.item_seq] or 0
	return daily_gift_buy_count >= cfg.buy_limit
end

--获取领取每日首次打开界面奖励标识
function BillionSubsidyWGData:GetIsFetchFirstOpenViewReward()
    return self.fetch_first_open_view_reward or 0
end

--获取每日高级会员奖励领取状态
function BillionSubsidyWGData:GetTodayMemberRewardFetchFlag()
	local member_level = self:GetMemberLevel()
    return self.today_member_reward_fetch_flag[member_level] or 0
end

--获取指定高级会员每日奖励领取状态
function BillionSubsidyWGData:GetTodayMemberRewardFetchFlagByLevel(level)
    return self.today_member_reward_fetch_flag[level] or 0
end

--获取所有会员奖励领取状态
function BillionSubsidyWGData:GetAllMemberRewardFetchFlag()
	local member_level = self:GetMemberLevel()
	if member_level == 1 then
		return self.fetch_first_open_view_reward or 0
	else
		return self.today_member_reward_fetch_flag[member_level] or 0
	end
end
 
--获取当前所有可领取折扣券的数量
function BillionSubsidyWGData:GetAllCanReceiveDCNum()
	local member_level = self:GetMemberLevel()
	local num = 0

	if member_level <= 1 then
		return num
	end

	for i = 2, member_level do
		if self:GetTodayMemberRewardFetchFlagByLevel(i) == 0 then
			local vip_cfg = self:GetMemberLevelCfgByLevel(i)
			num = num + vip_cfg.daily_discount_ticket_num
		end
	end

	return num
end

--获取累计订单数
function BillionSubsidyWGData:GetCumulateOrderNum()
    return self.cumulate_order_num or 0
end

--获取累计订单数奖励可领取次数
function BillionSubsidyWGData:GetCumulateOrderRewardCanFetchTimes()
    return self.cumulate_order_reward_can_fetch_times or 0
end

--获取总节省的金额
function BillionSubsidyWGData:GetTotalSavedChongzhiNum()
    return self.total_saved_chongzhi_num or 0
end

--获取折扣券信息
function BillionSubsidyWGData:GetDiscountCouponList()
    return self.discount_ticket_list or {}
end

--设置引导状态
function BillionSubsidyWGData:SetDiscountCouponGuideState(state)
    self.discount_guide_state = state
end

--设置折扣券总数据信息
function BillionSubsidyWGData:SetDiscountCouponAllDataList()
	self.dc_data_list = {}
	local other_cfg = self:GetOtherCfg()
	local num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.free_ticket_item_id)
	for i = 1, num do
		local data = {}
		-- 免单券数据
		data.type = TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FREE_DISCOUNT_COUPON	--类型
		data.sort = 9999
		--data.is_first = 1									--是否排序在首位 免单券排序优先级最高
		data.quota_max = other_cfg.free_ticket_quota		--免单券额度限制纯前端展示
		data.ticket_type = 1								--传给服务端的类型
		table.insert(self.dc_data_list, data)
	end

	for k, v in pairs(self.discount_ticket_list) do
		local cfg = self:GetDiscountTicketCfgBySeq(v.ticket_seq)
		if not IsEmptyTable(cfg) then
			local data = {}
			-- 折扣券数据
			data.data_seq = v.data_seq				--数据索引
			data.ticket_seq = v.ticket_seq			--券索引
			data.expend_times = v.expend_times		--膨胀次数
			data.reduce_quota = v.reduce_quota		--减额数量
			data.quota_limit = cfg.quota_limit		--额度限制
			--如果是在引导的时候这张券得放在第2的位置
			if self.discount_guide_state and data.ticket_seq == other_cfg.guide_ticket_seq then
				data.sort = 9000
			else
				data.sort = data.reduce_quota
			end
			data.ticket_type = 2					--传给服务端的类型
			--data.is_first = 0

			if cfg.quota_limit > 0 then
				data.type = TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FULL_DISCOUNT_COUPON	--类型
			else
				data.type = TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.DIRECT_DISCOUNT_COUPON
			end

			table.insert(self.dc_data_list, data)
		end
	end

	--没达到对应vip等级时，客户端需要强行在玩家神券里塞一张免单券用来展示
	local member_level_cfg = self:GetCurMemberLevelCfg()
	if member_level_cfg.free_ticket_daily_use_limit <= 0 then
		local data = {}
		-- 免单券数据
		data.type = TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FREE_DISCOUNT_COUPON	--类型
		data.sort = 9999
		data.quota_max = other_cfg.free_ticket_quota		--免单券额度限制纯前端展示
		data.ticket_type = 1								--传给服务端的类型
		table.insert(self.dc_data_list, data)
	end

	table.sort(self.dc_data_list, SortTools.KeyUpperSorters("sort", "type"))	--相同减额，立减券优先级更高
end

--通过页签获取对应折扣券总数据信息,不传页签就是获取所有数据
function BillionSubsidyWGData:GetDiscountCouponAllDataList(index)
	local cur_num = self:GetHaveFCDataNum()
	local other_cfg = self:GetOtherCfg()
	local num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.free_ticket_item_id)

	--当登录游戏如果背包已经有免单券，协议下发设置总数据的时候会获取不到背包里面免单券数量，因此需要在这里也添加免单券
	if cur_num < num then
		for i = cur_num + 1, num do
			local data = {}
			-- 免单券数据
			data.type = TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FREE_DISCOUNT_COUPON	--类型
			data.sort = 9999
			--data.is_first = 1									--是否排序在首位 免单券排序优先级最高
			data.quota_max = other_cfg.free_ticket_quota		--免单券额度限制纯前端展示
			data.ticket_type = 1								--传给服务端的类型
			table.insert(self.dc_data_list, data)
		end

		table.sort(self.dc_data_list, SortTools.KeyUpperSorters("sort", "type"))
	end

	if index then
		local shop_type = BillionSubsidyWGData.TabIndex2ShopType[index] or 0
		local can_use_free_ticket = CAN_USE_FREE_TICKET_SHOP[shop_type] and self:GetHasUseFreeTicketChance()
		local can_use_direct_discount_ticket = CAN_USE_DIRECT_DISCOUNT_TICKET_SHOP[shop_type] and self:GetHasUseNoLimitDiscountTicketChance()
		local can_use_full_discount_ticket = CAN_USE_FULL_DISCOUNT_TICKET_SHOP[shop_type]
		local data_list = {}
		for k, v in pairs(self.dc_data_list) do
			if (v.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FREE_DISCOUNT_COUPON and can_use_free_ticket)
			or (v.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FULL_DISCOUNT_COUPON and can_use_full_discount_ticket)
			or (v.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.DIRECT_DISCOUNT_COUPON and can_use_direct_discount_ticket) then
				table.insert(data_list, v)
			end
		end

		return data_list
	else
		return self.dc_data_list
	end
end

function BillionSubsidyWGData:GetHaveFCDataNum()
	local num = 0
	for k, v in pairs(self.dc_data_list) do
		if v.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FREE_DISCOUNT_COUPON then
			num = num + 1
		else
			return num
		end
	end

	return num
end

function BillionSubsidyWGData:GetSingleDiscountTicketDataBySeq(data_seq)
	local dc_data_list = self:GetDiscountCouponAllDataList()
	for k, v in pairs(dc_data_list) do
		if v.data_seq == data_seq then
			return v
		end
	end

	return nil
end

--------------------VIP首页end-------------------------

--------------------折扣卷选择入口start-------------------------
--获取当前是否勾选折扣券的状态
function BillionSubsidyWGData:GetUseDCStatus()
    return self.is_use_dc
end

function BillionSubsidyWGData:SetUseDCStatus(is_use)
    is_use = is_use or false
    self.is_use_dc = is_use
end
--------------------折扣卷选择入口end-------------------------

--------------------折扣卷选择背包start-------------------------
function BillionSubsidyWGData:GetSelectDCData()
    return self.select_dc_data or {}
end

function BillionSubsidyWGData:SetSelectDCData(data)
    self.select_dc_data = data
end

--获取当前界面商品可使用最优惠的折扣券数据
function BillionSubsidyWGData:GetMaxDiscountDCData(index, item_data_list)
	local dc_data_list = self:GetDiscountCouponAllDataList(index)
	if IsEmptyTable(dc_data_list) then
		return {}
	end

	local max_discount_dc_list = self:GetDCDataListMaxDiscount(index)
	local data = {}

    for k, v in pairs(item_data_list) do
		if v.free_ticket_num and v.free_ticket_num > 0 and max_discount_dc_list.free_num >= v.free_ticket_num then
			data = max_discount_dc_list.free_data
			return data
		end
	end

	local member_level = self:GetMemberLevel()
	if index == TabIndex.billion_subsidy_dezg and item_data_list[1].member_level and member_level >= item_data_list[1].member_level then
		for k1, v1 in pairs(max_discount_dc_list.max_full_dc_list) do
			if v1.quota_limit <= (item_data_list[1].member_price) and (IsEmptyTable(data) or data.reduce_quota < v1.reduce_quota) then
				data = v1
			end
		end
	else
		for k, v in pairs(item_data_list) do
			for k1, v1 in pairs(max_discount_dc_list.max_full_dc_list) do
				if v1.quota_limit <= (v.price) and (IsEmptyTable(data) or data.reduce_quota < v1.reduce_quota) then
					data = v1
				end
			end
		end
	end

	if not IsEmptyTable(data) then
		return data
	else
		return max_discount_dc_list.max_direct_data
	end
end

function BillionSubsidyWGData:GetDCDataListMaxDiscount(index)
	local dc_data_list = self:GetDiscountCouponAllDataList(index)
	self.max_discount_dc_list = {free_data = {}, free_num = 0, max_direct_data = {}, max_full_dc_list = {}}
	local full_dc_list = {}

    for k, v in pairs(dc_data_list) do
		if v.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FREE_DISCOUNT_COUPON then
			self.max_discount_dc_list.free_num = self.max_discount_dc_list.free_num + 1
			self.max_discount_dc_list.free_data = v
		elseif v.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.DIRECT_DISCOUNT_COUPON then
			if IsEmptyTable(self.max_discount_dc_list.max_direct_data)
				or (v.reduce_quota > self.max_discount_dc_list.max_direct_data.reduce_quota) then
				self.max_discount_dc_list.max_direct_data = v
			end
		else
			table.insert(full_dc_list, v)
		end
	end

	for k, v in pairs(dc_data_list) do
		if v.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FULL_DISCOUNT_COUPON
			and (IsEmptyTable(self.max_discount_dc_list.max_direct_data) or
			v.reduce_quota > self.max_discount_dc_list.max_direct_data.reduce_quota) then

			table.insert(self.max_discount_dc_list.max_full_dc_list, v)
		end
	end

	return self.max_discount_dc_list
end
--------------------折扣卷选择背包end-------------------------

--------------------百亿补贴商店start-------------------------
function BillionSubsidyWGData:GetBYBTRemind()
	if not FunOpen.Instance:GetFunIsOpenByViewNameAndNumberIndex(GuideModuleName.BillionSubsidy, TabIndex.billion_subsidy_bybt) then
		return 0
	end

    if self:GetShopEveryDayRemind(BillionSubsidyWGData.ShopType.BYBT) then
        return 1
    end

    -- if self:GetHasRedPocketCanFetch() then
    --     return 1
    -- end

    return 0
end

function BillionSubsidyWGData:GetBYBTShopItemGradeCfg()
    local cur_grade = self:GetGrade(BillionSubsidyWGData.ShopType.BYBT)
    return self.ten_billion_subsidy_shop_cfg[cur_grade]
end

function BillionSubsidyWGData:GetBYBTShopItemGradeData()
    local shop_cfg = self:GetBYBTShopItemGradeCfg()
	local data_list = {}
    for k, v in pairs(shop_cfg) do
        local data = {}
        data.grade = v.grade
        data.item_seq = v.item_seq
        data.price = v.price
        data.reward = v.reward
        data.buy_limit = v.buy_limit
        data.subsidy = v.subsidy
		data.rmb_type = v.rmb_type
		data.rmb_seq = v.rmb_seq
		data.old_price = v.old_price
		data.discount = v.discount
		data.limit_time_discount_duration = v.limit_time_discount_duration
		data.limit_time_discount_price = v.limit_time_discount_price
        local buy_count = BillionSubsidyWGData.Instance:GetBYBTShopSingleItemData(v.item_seq)
        local is_buy_done = v.buy_limit > 0 and buy_count >= v.buy_limit
        data.sort = is_buy_done and 1 or 0
		data.sort2 = data.limit_time_discount_duration > 0 and 0 or 1
        data.is_buy_done = is_buy_done

        table.insert(data_list, data)
    end

    table.sort(data_list, SortTools.KeyLowerSorters("sort", "sort2","item_seq"))

    return data_list
end

function BillionSubsidyWGData:GetBYBTShopItemCfg(item_seq)
    local cur_grade = self:GetGrade(BillionSubsidyWGData.ShopType.BYBT)
    return (self.ten_billion_subsidy_shop_cfg[cur_grade] or {})[item_seq]
end

function BillionSubsidyWGData:SetBYBTShopSingleItemUpdate(protocol)
    self.ten_billion_subsidy_shop_buy_count_list[protocol.change_item_seq] = protocol.change_buy_count
end

function BillionSubsidyWGData:GetBYBTShopSingleItemData(item_seq)
    return self.ten_billion_subsidy_shop_buy_count_list[item_seq] or 0
end

--------------------百亿补贴商店end-------------------------


--------------------限定折扣start-------------------------
function BillionSubsidyWGData:GetXDZKRemind()
	if not FunOpen.Instance:GetFunIsOpenByViewNameAndNumberIndex(GuideModuleName.BillionSubsidy, TabIndex.billion_subsidy_xdzk) then
		return 0
	end

    if self:GetShopEveryDayRemind(BillionSubsidyWGData.ShopType.XSSD) then
        return 1
    end

    -- if self:GetHasRedPocketCanFetch() then
    --     return 1
    -- end

    return 0
end

function BillionSubsidyWGData:GetLimitTimeShopItemGradeCfg()
    local cur_grade = self:GetGrade(BillionSubsidyWGData.ShopType.XSSD)
    return self.time_limit_shop_cfg[cur_grade]
end

function BillionSubsidyWGData:GetLimitTimeShopItemGradeData()
    local shop_cfg = self:GetLimitTimeShopItemGradeCfg()
	local data_list = {}
    for k, v in pairs(shop_cfg) do
        local data = {}
        data.grade = v.grade
        data.item_seq = v.item_seq
        data.price = v.price
        data.reward = v.reward
        data.buy_limit = v.buy_limit
        data.subsidy = v.subsidy
		data.rmb_type = v.rmb_type
		data.rmb_seq = v.rmb_seq
		data.old_price = v.old_price
		data.discount = v.discount
		data.limit_time_discount_duration = v.limit_time_discount_duration
		data.limit_time_discount_price = v.limit_time_discount_price
        local buy_count = BillionSubsidyWGData.Instance:GetXDZKSingleItemData(v.item_seq)
        local is_buy_done = v.buy_limit > 0 and buy_count >= v.buy_limit
        data.sort = is_buy_done and 1 or 0
		data.sort2 = data.limit_time_discount_duration > 0 and 0 or 1
        data.is_buy_done = is_buy_done

        table.insert(data_list, data)
    end

    table.sort(data_list, SortTools.KeyLowerSorters("sort", "sort2","item_seq"))

    return data_list
end

function BillionSubsidyWGData:GetLimitTimeShopItemCfg(item_seq)
    local cur_grade = self:GetGrade(BillionSubsidyWGData.ShopType.XSSD)
    return (self.time_limit_shop_cfg[cur_grade] or {})[item_seq]
end

function BillionSubsidyWGData:SetXDZKStartDiscountTimestamp(protocol)
    self.time_limited_discoun_shopt_start_timestamp = protocol.time_limited_discoun_shopt_start_timestamp
end

function BillionSubsidyWGData:GetXDZKStartDiscountTimestamp()
    return self.time_limited_discoun_shopt_start_timestamp
end

function BillionSubsidyWGData:SetXDZKSingleItemUpdate(protocol)
    self.time_limited_discount_shop_buy_count_list[protocol.change_item_seq] = protocol.change_buy_count
end

function BillionSubsidyWGData:GetXDZKSingleItemData(item_seq)
    return self.time_limited_discount_shop_buy_count_list[item_seq] or 0
end
--------------------限定折扣end-------------------------

--------------------每日试用start-------------------------
function BillionSubsidyWGData:GetMRSYShopItemGradeCfg()
    local cur_grade = self:GetGrade(BillionSubsidyWGData.ShopType.MRSY)
    return self.try_shop_cfg[cur_grade]
end

--------------------每日试用end-------------------------

--------------------满额返利start-------------------------
local shop_every_day_remind_key_str = "shop_every_day_remind_key_str_%s"
function BillionSubsidyWGData:GetShopEveryDayRemind(shop_type)
    local key = string.format(shop_every_day_remind_key_str, shop_type)
    if PlayerPrefsUtil.HasKey(key) then
        local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
        local value = PlayerPrefsUtil.GetInt(key)
        return cur_day ~= value
    end

    return true
end

function BillionSubsidyWGData:SetShopEveryDayRemindData(shop_type)
    local key = string.format(shop_every_day_remind_key_str, shop_type)
    local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    PlayerPrefsUtil.SetInt(key, cur_day)
end

--红包屏蔽红点也屏蔽
function BillionSubsidyWGData:GetHasRedPocketCanFetch()
    if self.first_pay_reward_flag == BillionSubsidyWGData.RedPocketState.Complete then
        return true
    end

    local member_level_cfg = self:GetMemberLevelCfg()
    if not IsEmptyTable(member_level_cfg) then
		local can_fetch_count = math.floor(self.today_return_quota / member_level_cfg.return_need_quota)
		return self.today_return_reward_fetch_times < member_level_cfg.return_times and can_fetch_count > self.today_return_reward_fetch_times
    end

    return false
end

function BillionSubsidyWGData:GetFirstPayRewardFlag()
    return self.first_pay_reward_flag
end

function BillionSubsidyWGData:GetTodayReturnRewardInfo()
    return self.today_return_quota, self.today_return_reward_fetch_times
end

function BillionSubsidyWGData:GetTryUseNum()
    return self.try_ticket_num
end

function BillionSubsidyWGData:GetTryUseCount()
    return self.today_try_ticket_use_times
end

function BillionSubsidyWGData:GetTryTicketShopDataBySeq(item_seq)
    return self.try_ticket_shop_buy_count_list[item_seq] or 0
end

function BillionSubsidyWGData:SetTryTicketShopInfo(protocol)
    self.try_ticket_num = protocol.try_ticket_num
    self.today_try_ticket_use_times = protocol.today_try_ticket_use_times
    self.try_ticket_shop_buy_count_list = protocol.try_ticket_shop_buy_count_list
end
--------------------满额返利end-------------------------

--------------------灵玉商店start-------------------------
function BillionSubsidyWGData:SetLYSDSingleItemData(protocol)
    self.gold_shop_buy_count_list[protocol.change_item_seq] = protocol.change_buy_count
end

function BillionSubsidyWGData:GetLYSDItemDataBySeq(item_seq)
    return self.gold_shop_buy_count_list[item_seq] or 0
end

function BillionSubsidyWGData:GetLYSDCfgBySeq(item_seq)
    local shop_cfg = self:GetLYSDShopItemGradeCfg()
	return shop_cfg[item_seq] or {}
end

function BillionSubsidyWGData:GetLYSDIsCanReceiveAllReward()
	local shop_cfg = self:GetLYSDShopItemGradeCfg()
	for k, v in pairs(shop_cfg) do
		if self.member_level >= v.free_member_level then
			return true
		end
	end

    return false
end

--是否领取了所有奖励
function BillionSubsidyWGData:GetLYSDIsAlreadyReceiveAllReward()
	local shop_cfg = self:GetLYSDShopItemGradeCfg()
	for k, v in pairs(shop_cfg) do
		local buy_count = BillionSubsidyWGData.Instance:GetLYSDItemDataBySeq(v.item_seq)
        local is_buy_done = v.buy_limit > 0 and buy_count >= v.buy_limit
        if not is_buy_done then
			return false
		end
	end

    return true
end

function BillionSubsidyWGData:GetLYSDShopItemGradeCfg()
    local cur_grade = self:GetGrade(BillionSubsidyWGData.ShopType.LYSD)
    return self.gold_shop_cfg[cur_grade] or {}
end

function BillionSubsidyWGData:GetLYSDShopItemGradeData()
    local shop_cfg = self:GetLYSDShopItemGradeCfg()
	local data_list = {}
    for k, v in pairs(shop_cfg) do
        local data = {}
        data.grade = v.grade
        data.item_seq = v.item_seq
        data.price = v.price
        data.reward = v.reward
        data.buy_limit = v.buy_limit
        data.subsidy = v.subsidy
		data.free_member_level = v.free_member_level
        local buy_count = BillionSubsidyWGData.Instance:GetLYSDItemDataBySeq(v.item_seq)
        local is_buy_done = v.buy_limit > 0 and buy_count >= v.buy_limit
        data.sort = is_buy_done and 1 or 0
        data.is_buy_done = is_buy_done
        table.insert(data_list, data)
    end

    table.sort(data_list, SortTools.KeyLowerSorters("sort", "item_seq"))

    return data_list
end

--------------------灵玉商店end-------------------------

-- 传入商店类型商品seq,获取每种券是否可以使用
function BillionSubsidyWGData:GetCanUseDiscountTicketBySeq(shop_type, item_seq)
	local free_ticket_data
	local full_discount_ticket_data
	local direct_discount_ticket_data
	local member_cfg = self:GetMemberLevelCfg()
	if IsEmptyTable(member_cfg) then
		return free_ticket_data, full_discount_ticket_data, direct_discount_ticket_data
	end

	local shop_cfg
	if shop_type == BillionSubsidyWGData.ShopType.BYBT then
		shop_cfg = self:GetBYBTShopItemCfg(item_seq)
	elseif shop_type == BillionSubsidyWGData.ShopType.XSSD then
		shop_cfg = self:GetLimitTimeShopItemCfg(item_seq)
	elseif shop_type == BillionSubsidyWGData.ShopType.JKJZC then
		shop_cfg = self:GetJKJZCShopCfgBySeq(item_seq)
	elseif shop_type == BillionSubsidyWGData.ShopType.DEZG then
		shop_cfg = self:GetDEZGCfgBySeq(item_seq)
	end

	if shop_cfg then
		local need_cost_free_ticket_num = 0
		local price = 0
		if shop_type == BillionSubsidyWGData.ShopType.JKJZC then
			need_cost_free_ticket_num = shop_cfg.free_ticket_num
			price = shop_cfg.price
		elseif shop_type == BillionSubsidyWGData.ShopType.BYBT or shop_type == BillionSubsidyWGData.ShopType.XSSD then
			need_cost_free_ticket_num = shop_cfg.free_ticket_num
			local end_time = self.time_limited_discoun_shopt_start_timestamp + shop_cfg.limit_time_discount_duration
			price = end_time > TimeWGCtrl.Instance:GetServerTime() and shop_cfg.limit_time_discount_price or shop_cfg.price
		elseif shop_type == BillionSubsidyWGData.ShopType.DEZG then
			price = shop_cfg.member_price
		end

		local can_use_free_ticket = CAN_USE_FREE_TICKET_SHOP[shop_type] and need_cost_free_ticket_num > 0 and ((self.today_used_free_ticket_num + need_cost_free_ticket_num) <= member_cfg.free_ticket_daily_use_limit)
		local can_use_direct_discount_ticket = CAN_USE_DIRECT_DISCOUNT_TICKET_SHOP[shop_type] and self.today_no_limit_discount_ticket_used_times < member_cfg.no_limit_discount_daily_use_limit
		local can_use_full_discount_ticket = CAN_USE_FULL_DISCOUNT_TICKET_SHOP[shop_type] and self.today_limit_discount_ticket_used_times < member_cfg.limit_discount_daily_use_limit
		local all_discount_ticket_list = BillionSubsidyWGData.Instance:GetDiscountCouponAllDataList()
		for k, v in ipairs(all_discount_ticket_list) do
			if v.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FREE_DISCOUNT_COUPON
					and can_use_free_ticket and not free_ticket_data then
				free_ticket_data = v
			elseif v.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FULL_DISCOUNT_COUPON
					and can_use_full_discount_ticket and price >= v.quota_limit and not full_discount_ticket_data then
				full_discount_ticket_data = v
			elseif v.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.DIRECT_DISCOUNT_COUPON
					and can_use_direct_discount_ticket and not direct_discount_ticket_data then
				direct_discount_ticket_data = v
			end

			if free_ticket_data ~= nil and full_discount_ticket_data ~= nil and direct_discount_ticket_data ~= nil then
				break
			end
		end
	end

	return free_ticket_data, full_discount_ticket_data, direct_discount_ticket_data
end

-- 传入商店类型商品seq,获取全部能使用的列表
function BillionSubsidyWGData:GetAllCanUseDiscountTicketBySeq(shop_type, item_seq, is_need_show_no_chance_ticket)
	local all_can_use_ds_ticket_list = {}
	local member_cfg = self:GetMemberLevelCfg()
	if IsEmptyTable(member_cfg) then
		return all_can_use_ds_ticket_list
	end

	local shop_cfg
	if shop_type == BillionSubsidyWGData.ShopType.BYBT then
		shop_cfg = self:GetBYBTShopItemCfg(item_seq)
	elseif shop_type == BillionSubsidyWGData.ShopType.XSSD then
		shop_cfg = self:GetLimitTimeShopItemCfg(item_seq)
	elseif shop_type == BillionSubsidyWGData.ShopType.JKJZC then
		shop_cfg = self:GetJKJZCShopCfgBySeq(item_seq)
	elseif shop_type == BillionSubsidyWGData.ShopType.DEZG then
		shop_cfg = self:GetDEZGCfgBySeq(item_seq)
	elseif shop_type == BillionSubsidyWGData.ShopType.FYMS then
		shop_cfg = self:GetB1G3ShopCfgBySeq(item_seq)
	end

	if shop_cfg then
		local only_show_view_ticket_list = {}			-- 将不能用但是需要展示的券先存入其中,最后再插入总的列表里
		local need_cost_free_ticket_num = 0
		local price = 0
		if shop_type == BillionSubsidyWGData.ShopType.JKJZC then
			need_cost_free_ticket_num = shop_cfg.free_ticket_num
			price = shop_cfg.price
		elseif shop_type == BillionSubsidyWGData.ShopType.BYBT or shop_type == BillionSubsidyWGData.ShopType.XSSD then
			need_cost_free_ticket_num = shop_cfg.free_ticket_num
			local end_time = self.time_limited_discoun_shopt_start_timestamp + shop_cfg.limit_time_discount_duration
			price = end_time > TimeWGCtrl.Instance:GetServerTime() and shop_cfg.limit_time_discount_price or shop_cfg.price
		elseif shop_type == BillionSubsidyWGData.ShopType.DEZG then
			price = shop_cfg.member_price
		elseif shop_type == BillionSubsidyWGData.ShopType.FYMS then
			need_cost_free_ticket_num = 1	--付1买3花费的免单券数量固定为1
			price = shop_cfg.price
		end

		local can_use_free_ticket = CAN_USE_FREE_TICKET_SHOP[shop_type] and need_cost_free_ticket_num > 0 and ((self.today_used_free_ticket_num + need_cost_free_ticket_num) <= member_cfg.free_ticket_daily_use_limit)
		local can_use_direct_discount_ticket = CAN_USE_DIRECT_DISCOUNT_TICKET_SHOP[shop_type] and self.today_no_limit_discount_ticket_used_times < member_cfg.no_limit_discount_daily_use_limit
		local can_use_full_discount_ticket = CAN_USE_FULL_DISCOUNT_TICKET_SHOP[shop_type] and self.today_limit_discount_ticket_used_times < member_cfg.limit_discount_daily_use_limit
		local all_discount_ticket_list = BillionSubsidyWGData.Instance:GetDiscountCouponAllDataList()

		for k, v in ipairs(all_discount_ticket_list) do
			local quota_limit = v.quota_limit and v.quota_limit or 0
			if (v.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FREE_DISCOUNT_COUPON and can_use_free_ticket)
			or (v.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FULL_DISCOUNT_COUPON and can_use_full_discount_ticket and price >= quota_limit)
			or (v.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.DIRECT_DISCOUNT_COUPON and can_use_direct_discount_ticket) then
				table.insert(all_can_use_ds_ticket_list, v)
			elseif (v.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FREE_DISCOUNT_COUPON and is_need_show_no_chance_ticket and CAN_USE_FREE_TICKET_SHOP[shop_type])
			or (v.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FULL_DISCOUNT_COUPON and is_need_show_no_chance_ticket and CAN_USE_FULL_DISCOUNT_TICKET_SHOP[shop_type] and price >= quota_limit)
			or (v.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.DIRECT_DISCOUNT_COUPON and is_need_show_no_chance_ticket and CAN_USE_DIRECT_DISCOUNT_TICKET_SHOP[shop_type]) then
				table.insert(only_show_view_ticket_list, v)
			end
		end

		if #only_show_view_ticket_list > 0 then
			for k, v in ipairs(only_show_view_ticket_list) do
				table.insert(all_can_use_ds_ticket_list, v)
			end
		end
	end

	return all_can_use_ds_ticket_list
end

-- 传入商店类型商品seq,获取单个商品的配置表
function BillionSubsidyWGData:GetShopCfgByTypeAndSeq(shop_type, item_seq)
	if shop_type == BillionSubsidyWGData.ShopType.BYBT then
		return self:GetBYBTShopItemCfg(item_seq)
	elseif shop_type == BillionSubsidyWGData.ShopType.XSSD then
		return self:GetLimitTimeShopItemCfg(item_seq)
	elseif shop_type == BillionSubsidyWGData.ShopType.JKJZC then
		return self:GetJKJZCShopCfgBySeq(item_seq)
	elseif shop_type == BillionSubsidyWGData.ShopType.DEZG then
		return self:GetDEZGCfgBySeq(item_seq)
	elseif shop_type == BillionSubsidyWGData.ShopType.FYMS then
		return self:GetB1G3ShopCfgBySeq(item_seq)
	elseif shop_type == BillionSubsidyWGData.ShopType.LYSD then
		return self:GetLYSDCfgBySeq(item_seq)
	end
end

-- 传入商店类型商品seq,获取单个商品的购买次数
function BillionSubsidyWGData:GetShopBuyCountByTypeAndSeq(shop_type, item_seq)
	if shop_type == BillionSubsidyWGData.ShopType.BYBT then
		return self:GetBYBTShopSingleItemData(item_seq)
	elseif shop_type == BillionSubsidyWGData.ShopType.XSSD then
		return self:GetXDZKSingleItemData(item_seq)
	elseif shop_type == BillionSubsidyWGData.ShopType.JKJZC then
		return self:GetJKJZCShopBuyCount(item_seq)
	elseif shop_type == BillionSubsidyWGData.ShopType.DEZG then
		return self:GetBuyDEZGShopItemCountBySeq(item_seq)
	end

	return 0
end

function BillionSubsidyWGData:GetHasUseFreeTicketChance()
	local member_cfg = self:GetMemberLevelCfg()
	if IsEmptyTable(member_cfg) then
		return false
	end

	return self.today_used_free_ticket_num < member_cfg.free_ticket_daily_use_limit
end

--获取能使用免单券的VIP等级
function BillionSubsidyWGData:GetUseFreeTicketVipLevel()
	for k, v in pairs(self.member_level_cfg) do
		if v.free_ticket_daily_use_limit > 0 then
			return k
		end
	end

	return 1
end

-- 获取立减券是否有使用次数
function BillionSubsidyWGData:GetHasUseNoLimitDiscountTicketChance()
	local member_cfg = self:GetMemberLevelCfg()
	if IsEmptyTable(member_cfg) then
		return false
	end

	return self.today_no_limit_discount_ticket_used_times < member_cfg.no_limit_discount_daily_use_limit
end

-- 获取满减券是否有使用次数
function BillionSubsidyWGData:GetHasUseLimitDiscountTicketChance()
	local member_cfg = self:GetMemberLevelCfg()
	if IsEmptyTable(member_cfg) then
		return false
	end

	return self.today_limit_discount_ticket_used_times < member_cfg.limit_discount_daily_use_limit
end

--------------------购物车start-------------------------
function BillionSubsidyWGData:SetShopCartData(protocol)
	self:ClearShopCartClearList()
	self.lock_time = protocol.lock_time
	-- print_error("self.lock_time",self.lock_time)
	self.shop_cart_list = protocol.shop_cart_list

	if self.lock_time > 0 then
		local rmb_price, gold_price = BillionSubsidyWGData.Instance:CounteShopCartPrice()
		local discount_seq = -1
		if self.discount_data and rmb_price >= self.discount_data.quota_limit then
			discount_seq = self.discount_data.data_seq
			-- rmb_price = rmb_price - self.discount_data.reduce_quota
		end
		-- print_error("rmb_price",discount_seq)
		-- print_error("discount_data",self.discount_data)
		RechargeWGCtrl.Instance:Recharge(rmb_price, GET_GOLD_REASON.GET_GOLD_REASON_TBS_TROLLEY_RMB_BUY, 0 ,discount_seq)
	end
end

function BillionSubsidyWGData:IsShopCartLock()
	return self.lock_time and self.lock_time > 0
end

function BillionSubsidyWGData:SetShopCartCurDiscountData(discount_data)
	self.discount_data = discount_data
end

--获取物品在购物车中的数量
function BillionSubsidyWGData:GetShopNumInCart(shop_type, item_seq)
	if self.shop_cart_list[shop_type] then
		return self.shop_cart_list[shop_type].item_count_list[item_seq] or 0
	end
	return 0
end

-- 获取商品选择状态
function BillionSubsidyWGData:GetShopItemChooseFlag(shop_type, item_seq)
	if self.shop_cart_list[shop_type] then
		if item_seq == -1 then
			for i = 0, BILLION_SUBSIDY_SHOP_TYPE_SEQ_MAX - 1 do
				if self.shop_cart_list[shop_type].shop_choose_flag[i] == 0 and self:GetShopNumInCart(shop_type,i) > 0 then
					return false
				end
			end
		else
			return self.shop_cart_list[shop_type].shop_choose_flag[item_seq] == 1
		end
		return true
	end

	return false
end

-- 是否在购物车限制时间中 23点55-0点
function BillionSubsidyWGData:IsInLimitTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local zero_time = TimeWGCtrl.Instance:GetTodayBeginningTimestamp()
	-- 23点55
	if server_time >= zero_time + 86100 and server_time <=zero_time + 86400 then
		return true
	end
	return false
end

--是否可以添加物品到购物车
-- shop_type：商店类型 item_seq：商店索引 count：添加数量 buy_limit:限购数量 is_tips:是否提示
function BillionSubsidyWGData:IsCanAddShopToCart(shop_type, item_seq, count, buy_limit, is_tips)
	if self:IsShopCartLock() then
		return false
	end
	if self:IsInLimitTime() then
		if is_tips then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.BillionSubsidy.LimitTime)
		end
		BillionSubsidyWGCtrl.Instance:FlushShopCart()
		return false
	end

	if shop_type == BillionSubsidyWGData.ShopType.FYMS then
		local num = self:GetShopNumInCartByType(shop_type)
		if num >= 3 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.BillionSubsidy.CartAddShopLimit2)
			return false
		end
	end

	local buy_count = self:GetShopBuyCountByTypeAndSeq(shop_type, item_seq)
    local num_in_cart= self:GetShopNumInCart(shop_type, item_seq)
    local is_limit = buy_limit > 0 and buy_count + num_in_cart + count > buy_limit

    if is_limit and is_tips then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.BillionSubsidy.CartAddShopLimit)
        return false
    end

	return true
end

--获取购物车数据
function BillionSubsidyWGData:GetShopData()
	return self.shop_cart_list or {}
end

-- 获取指定类型的购物车有效数量
function BillionSubsidyWGData:GetShopNumInCartByType(shop_type)
	local data_list = {}
	if self.shop_cart_list[shop_type] then
		local count = 0
		for k, v in pairs(self.shop_cart_list[shop_type].item_count_list) do
			if v > 0 then
				count = count + 1
				local data = {}
				data.shop_type = shop_type
				data.item_seq = k
				table.insert(data_list,data)
			end
		end
		return count, data_list
	end
	return 0, data_list
end

-- 是否是空购物车
function BillionSubsidyWGData:IsEmptyShopCart()
	for i = 1, BILLION_SUBSIDY_SHOP_TYPE_MAX do
		if self:GetShopNumInCartByType(i) > 0 then
			return false
		end
	end
	return true
end

-- 设置购物车打开状态
function BillionSubsidyWGData:SetShopCartOpenFlag(shop_type, is_open)
	self.shop_cart_open_flag[shop_type] = is_open
end

-- 获取购物车打开状态
function BillionSubsidyWGData:GetShopCartOpenFlag(shop_type)
	if self.shop_cart_open_flag[shop_type] == nil then
		self.shop_cart_open_flag[shop_type] = true
	end
	return self.shop_cart_open_flag[shop_type]
end

-- 计算购物车商品的价格
function BillionSubsidyWGData:CounteShopCartPrice()

	local rmb = 0
	local glod = 0
	local b1f3_rmb = 0
	local b1f3_count = 0

	-- 限时商店时间
	local start_timestamp = self:GetXDZKStartDiscountTimestamp()
	local cur_server_time = TimeWGCtrl.Instance:GetServerTime()
	-- 会员等级
	local member_level = BillionSubsidyWGData.Instance:GetMemberLevel()
	
	for type = 1, BILLION_SUBSIDY_SHOP_TYPE_MAX do
		for seq = 0, BILLION_SUBSIDY_SHOP_TYPE_SEQ_MAX - 1 do
			local count = self:GetShopNumInCart(type, seq)
			local is_choose = self:GetShopItemChooseFlag(type, seq)
			if count > 0 and is_choose then
				local shop_cfg = self:GetShopCfgByTypeAndSeq(type, seq)
				if not IsEmptyTable(shop_cfg) then
					if type == BillionSubsidyWGData.ShopType.LYSD then
						local is_receive = member_level >= shop_cfg.free_member_level
						if not is_receive then
							glod = glod + shop_cfg.price*count
						end
					elseif type == BillionSubsidyWGData.ShopType.FYMS then
						b1f3_rmb = math.max(b1f3_rmb, shop_cfg.price*count)
						b1f3_count = b1f3_count + 1
					elseif type == BillionSubsidyWGData.ShopType.XSSD then
						local end_time = start_timestamp + shop_cfg.limit_time_discount_duration
						local is_limit_time_discount = end_time > cur_server_time
						local show_price = is_limit_time_discount and shop_cfg.limit_time_discount_price or shop_cfg.price
						rmb = rmb + show_price*count
					else
						rmb = rmb + shop_cfg.price*count
					end
					
				end
			end
			
		end
	end
	if b1f3_count == 3 then
		rmb = rmb + b1f3_rmb
	end
	return rmb, glod
end

function BillionSubsidyWGData:SetInputFlag(shop_type,seq, state)
	if not self.shop_cart_input_flag[shop_type] then
		self.shop_cart_input_flag[shop_type] = {}
	end
	self.shop_cart_input_flag[shop_type][seq] = state
end

function BillionSubsidyWGData:GetInputFlag(shop_type,seq)
	if not self.shop_cart_input_flag[shop_type] then
		return false
	end
	return self.shop_cart_input_flag[shop_type][seq]
end

function BillionSubsidyWGData:ClearInputFlag()
	self.shop_cart_input_flag = {}
end
------------------------------------------------管理模式数据-----------------------
-- 购物车清除列表添加商品
function BillionSubsidyWGData:AddShopCartClearList(shop_type,item_seq)
	if not self.shop_cart_clear_list[shop_type] then
		self.shop_cart_clear_list[shop_type] = {}
	end
	-- 全选
	if item_seq == -1 then
		for i = 0, BILLION_SUBSIDY_SHOP_TYPE_SEQ_MAX - 1 do
			if self:GetShopNumInCart(shop_type,i) > 0 then
				self.shop_cart_clear_list[shop_type][i] = 1
			end
			
		end
	else
		self.shop_cart_clear_list[shop_type][item_seq] = 1
	end	
end

-- 购物车清除列表移除商品
function BillionSubsidyWGData:RemoveShopCartClearList(shop_type, item_seq)
	if not self.shop_cart_clear_list[shop_type] then
		self.shop_cart_clear_list[shop_type] = {}
	end
	if item_seq == -1 then
		for i = 0, BILLION_SUBSIDY_SHOP_TYPE_SEQ_MAX - 1 do
			self.shop_cart_clear_list[shop_type][i] = 0
		end
	else
		if self.shop_cart_clear_list[shop_type] and self.shop_cart_clear_list[shop_type][item_seq] then
			self.shop_cart_clear_list[shop_type][item_seq] = 0 
		end
	end

end

-- 商品是否在购物车清除列表中
function BillionSubsidyWGData:IsNeedClear(shop_type, item_seq)
	if self.shop_cart_clear_list[shop_type] then
		if item_seq == -1 then
			for i = 0, BILLION_SUBSIDY_SHOP_TYPE_SEQ_MAX - 1 do
				if (self.shop_cart_clear_list[shop_type][i] == 0 or self.shop_cart_clear_list[shop_type][i]==nil) and self:GetShopNumInCart(shop_type,i) > 0 then
					return false
				end
			end
			return true
		else
			if self.shop_cart_clear_list[shop_type][item_seq] then
				return self.shop_cart_clear_list[shop_type][item_seq] == 1
			end
		end
	end
	return false
end

-- 购物车清除列表清空
function BillionSubsidyWGData:ClearShopCartClearList()
	self.shop_cart_clear_list = {}
end

-- fuc
function BillionSubsidyWGData:GetShopCartClearList()
	return self.shop_cart_clear_list
end

--------------------购物车end-------------------------

-------------------------- 一元活动 ---------------------------
function BillionSubsidyWGData:SetYiYuanInfo(protocol)
	self.yiyuan_grade = protocol.one_yuan_grade
	self.yiyuan_login_day = protocol.one_yuan_day
	self.yiyuan_buy_flag = protocol.one_yuan_buy_flag
	self.yiyuan_day_reward_flag = protocol.one_yuan_day_reward_flag -- 0无效
end

function BillionSubsidyWGData:GetYiYuanGrade()
	return self.yiyuan_grade
end

function BillionSubsidyWGData:GetYiYuanLoginDay()
	return self.yiyuan_login_day
end

-- 未领取奖励天数
function BillionSubsidyWGData:GetYiYuanAwardDay()
	return self:GetYiYuanAccAwardMax() - self:GetYiyuanGotDay()
end

-- 已领取天数
function BillionSubsidyWGData:GetYiyuanGotDay()
	local num = 0
	for _, value in pairs(self.yiyuan_day_reward_flag) do
		if value > 0 then
			num = num + 1
		end
	end

	return num
end

function BillionSubsidyWGData:GetYiYuanAccAward()
	return self.oneyuan_welfare_reward_cfg[self.yiyuan_grade] or {}
end

function BillionSubsidyWGData:GetYiYuanAccAwardMax()
	return #self:GetYiYuanAccAward()
end

function BillionSubsidyWGData:GetYiYuanCfg()
	return self.oneyuan_welfare_cfg[self.yiyuan_grade]
end

-- day 1开始
function BillionSubsidyWGData:IsYiYuanGetAward(day)
	return self.yiyuan_day_reward_flag[day] == 1
end

function BillionSubsidyWGData:IsYiYuanBuy()
	return self.yiyuan_buy_flag == 1
end

-- 一元活动红点 未购买时：目录每天一次红点，进界面后红点消失
--已购买：有未领取道具时，目录和对应道具都显示红点
function BillionSubsidyWGData:GetYiYuanRemind()
	if self:GetYiYuanDayFirstFlag() == 1 then
		return 1
	end

	if self:GetYiYuanAwardRemind() == 1 then
		return 1
	end
end

-- 每日首次打开
function BillionSubsidyWGData:GetYiYuanDayFirstFlag()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local value = PlayerPrefsUtil.GetString("yiyuan_day_first_flag")
	local set_value = role_id.."yiyuan_day_first_flag"..day

	if value ~= set_value then
		return  self:IsYiYuanBuy() and 0 or self.yiyuan_day_first_flag
	end

	return 0
end

function BillionSubsidyWGData:SetYiYuanDayFirstFlag(flag)
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	local set_value = role_id.."yiyuan_day_first_flag"..day
	PlayerPrefsUtil.SetString("yiyuan_day_first_flag", set_value)
	self.yiyuan_day_first_flag = flag
end

function BillionSubsidyWGData:GetYiYuanAwardRemind()
	for index = 1, self.yiyuan_login_day do
		if not self:IsYiYuanGetAward(index) then
			return 1
		end
	end

	return 0
end

-------------------------- 一元活动end ---------------------------