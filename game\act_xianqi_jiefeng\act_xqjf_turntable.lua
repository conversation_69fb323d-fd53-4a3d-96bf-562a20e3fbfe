
local OA_TURNTABLE_LAYER_MAX = 3
local OA_TURNTABLE_RANK_MAX = 3

function ActXianQiJieFengView:LoadTurnTableView()
	self:CalulateWinVector()
	self:CacheYaoGanSkeleton()

    self.turntable_ball_list = {}
	self.turntable_once_draw = {}
	self.turntable_simulate_anim_callback = nil

    self.node_list["turntable_btn_draw"].button:AddClickListener(BindTool.Bind(self.OnClickTurnTableBtnDraw, self))
    self.node_list["turntable_anim_check"].button:AddClickListener(BindTool.Bind(self.OnClickTurnTableBtnCheck, self))
    self.node_list["turntable_btn_record"].button:AddClickListener(BindTool.Bind(self.OnClickTurnTableBtnRecoed, self))
    self.node_list["turntable_once_check"].button:AddClickListener(BindTool.Bind(self.OnClickTurnTableBtnOnceAll, self))
    self.node_list["turntable_btn_reset"].button:AddClickListener(BindTool.Bind(self.OnClickTurnTableBtnReset, self))
    self.node_list["turntable_cost_img"].button:AddClickListener(BindTool.Bind(self.OnClickTurnTableLayerItem, self))
    self.node_list["turntable_btn_tip"].button:AddClickListener(BindTool.Bind(self.OnClickTurntableBtnTip, self))

    self:ResetTurnTableOnceState()

	-- self:FlushTurnTableBtnLayerInfo()
    self:FlushTurnTableAnimCheck()
	self:FlushTurnTableRecordNum()
end

function ActXianQiJieFengView:CacheYaoGanSkeleton()
	if not self.cache_yaogan_list then
		self.cache_yaogan_list = {}
		self.cache_yaogan_nv_list = {}
		for i=1,3 do
			self.cache_yaogan_list["layer_gan_" .. i] = self.node_list["layer_gan_" .. i].gameObject:GetComponent("SkeletonGraphic")
		end
		for i=1,1 do
			self.cache_yaogan_nv_list['layer_renwu_' .. i] = self.node_list['layer_renwu_' .. i].gameObject:GetComponent("SkeletonGraphic")
		end
	end
end

function ActXianQiJieFengView:OnClickTurntableBtnTip()
	if not self.cur_turntable_layer then
		return
	end
    local cfg = XQJFTurnTableData.Instance:GetLayerCfgByLayerNum(self.cur_turntable_layer)
	if not cfg then
		return
	end
	RuleTip.Instance:SetContent(cfg.tips,cfg.name)
end

function ActXianQiJieFengView:FlushTurnTableBtnLayerInfo()
	local layer_list = XQJFTurnTableData.Instance:GetCurActLayerList()
	local layer_cfg, str
	for i = 1, OA_TURNTABLE_LAYER_MAX do
		layer_cfg = XQJFTurnTableData.Instance:GetLayerCfgByLayerNum(layer_list[i])
		if layer_cfg and layer_list[i] then
			self.node_list["turntable_layer_" .. i]:SetActive(true)
	        self.node_list["turntable_layer_" .. i].button:AddClickListener(BindTool.Bind(self.OnClickTurnTableLayer, self, i, layer_list[i]))
	        self.node_list["turntable_layer_" .. i].rect.anchoredPosition = Vector2(layer_cfg.x, layer_cfg.y)

			str = layer_cfg.layer_btn_nor ~= "" and layer_cfg.layer_btn_nor or "layer_" .. i .. "_nor"
			local bundle,asset = ResPath.GetOATurnTablePath(str)
			self.node_list["turntable_layer_" .. i .. "_nor"].image:LoadSprite(bundle, asset, function ()
				self.node_list["turntable_layer_" .. i .. "_nor"].image:SetNativeSize()
			end)

			str = layer_cfg.layer_btn_high ~= "" and layer_cfg.layer_btn_high or "layer_" .. i .. "_high"
			bundle,asset = ResPath.GetOATurnTablePath(str)
			self.node_list["turntable_layer_" .. i .. "_high"].image:LoadSprite(bundle, asset, function ()
				self.node_list["turntable_layer_" .. i .. "_high"].image:SetNativeSize()
			end)
		else
			self.node_list["turntable_layer_" .. i]:SetActive(false)
		end
		--ActXianQiJieFengWGCtrl.Instance:SendOpera(RA_TUNVLANG_OPERA_TYPE.LAYER_INFO, layer_list[i])
    end

end

function ActXianQiJieFengView:ReleaseTurnTableView()
	if self.turntable_ball_list then
		for i, v in pairs(self.turntable_ball_list) do
			v:DeleteMe()
		end
		self.turntable_ball_list = {}
	end

    if CountDownManager.Instance:HasCountDown(XQJF_TURNTABLE_COUNTDOWN_KEY_STR) then
        CountDownManager.Instance:RemoveCountDown(XQJF_TURNTABLE_COUNTDOWN_KEY_STR)
    end

	if self.turntable_click_draw_timequest then
		GlobalTimerQuest:CancelQuest(self.turntable_click_draw_timequest)
		self.turntable_click_draw_timequest = nil
	end

	if self.turntable_alert then
		self.turntable_alert:DeleteMe()
		self.turntable_alert = nil
	end

	if self.ball_anim then
		self.ball_anim:Kill()
		self.ball_anim = nil
	end

    self.onclick_turntable_draw = nil
    self.cur_turntable_layer = nil
    self.turntable_is_animing = nil
	self.turntable_win_vector2 = nil
	self.turntable_cost_img_cache = nil
	self.cache_yaogan_list = nil
	self.cache_yaogan_nv_list = nil
	self.turntable_simulate_anim_callback = nil
	self.turntable_once_draw = nil
end

function ActXianQiJieFengView:ShowIndexTurnTable(index)
	self:DoPlayTweenByGan()
	ActXianQiJieFengWGCtrl.Instance:SendOpera(RA_TUNVLANG_OPERA_TYPE.RECORD)
	self:OpenCallBackTurnTable()
	self:CheckNeedResetTurnTable()
end

-- 是否需要请求重置
function ActXianQiJieFengView:CheckNeedResetTurnTable()
	local layer_list = XQJFTurnTableData.Instance:GetCurActLayerList()
	for i = OA_TURNTABLE_LAYER_MAX, 1, -1 do
		if layer_list[i] and XQJFTurnTableData.Instance:CheckCanResetByLayer(layer_list[i]) then
			ActXianQiJieFengWGCtrl.Instance:SendOpera(RA_TUNVLANG_OPERA_TYPE.RESET_LAYER, layer_list[i])
		end
	end
end

function ActXianQiJieFengView:ResetTurnTableOnceState()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_TURNTABLE)
	local open_time = activity_info.start_time
	local cache_start_time = RoleWGData.GetRolePlayerPrefsInt("TurnTableEndTime")
	if open_time ~= cache_start_time then
		RoleWGData.SetRolePlayerPrefsInt("TurnTableEndTime",open_time)

		local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
		for i=1,OA_TURNTABLE_LAYER_MAX do
			PlayerPrefsUtil.SetInt(XQJF_TURNTABLE_COUNTDOWN_KEY_STR .. "_ONCE" .. role_id .. i, 0)
		end 
		
		PlayerPrefsUtil.SetInt(XQJF_TURNTABLE_COUNTDOWN_KEY_STR .. "_ANIM" .. role_id, 0)
	end
end

function ActXianQiJieFengView:OpenCallBackTurnTable()
	local layer_list = XQJFTurnTableData.Instance:GetCurActLayerList()
    -- 設置默认选中
    local select_index = 1
	for i = OA_TURNTABLE_LAYER_MAX, 1, -1 do
		if layer_list[i] then
			local is_show_red = XQJFTurnTableData.Instance:IsShowRedByLayer(layer_list[i])
			if is_show_red then
				select_index = i
				break
			end
		end
	end

	if layer_list[select_index] then
		self:OnClickTurnTableLayer(select_index, layer_list[select_index])
	end
end

function ActXianQiJieFengView:CalulateWinVector()
	local pos = self.node_list["turntable_reward_end"].rect.anchoredPosition
	pos = pos + self.node_list["turntable_pole"].rect.anchoredPosition
	self.turntable_win_vector2 = Vector2(pos.x, pos.y)
	XQJFTurnTableData.Instance:SetWinVector2(self.turntable_win_vector2)
end

--刷新动画显示状态
function ActXianQiJieFengView:FlushTurnTableAnimCheck()
	local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid
	local img_yes_state = PlayerPrefsUtil.GetInt(XQJF_TURNTABLE_COUNTDOWN_KEY_STR .. "_ANIM" .. role_id) == 1
	self.node_list["turntable_anim_yes"]:SetActive(img_yes_state)
	self.turntable_ignore_anim = img_yes_state
end

--刷新一键抽奖显示状态
function ActXianQiJieFengView:FlushTurnTableOnceCheck()
	if not self.cur_turntable_layer then
		return
	end

	local cur_layer_index = self:GetTurnTableLayerIndex()
    local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid
	local img_yes_state = PlayerPrefsUtil.GetInt(XQJF_TURNTABLE_COUNTDOWN_KEY_STR .. "_ONCE" .. role_id .. cur_layer_index) == 1
	self.node_list["turntable_once_yes"]:SetActive(img_yes_state)

	for i=1,OA_TURNTABLE_LAYER_MAX do
		self.turntable_once_draw[i] = PlayerPrefsUtil.GetInt(XQJF_TURNTABLE_COUNTDOWN_KEY_STR .. "_ONCE" .. role_id .. i) == 1
	end 

end

-- 播放下拉龙骨动画
function ActXianQiJieFengView:OnStartYaoGanLa()
	if self.turntable_ignore_anim then
		return
	end
	local layer_list = XQJFTurnTableData.Instance:GetCurActLayerList()
	local layer_cfg = XQJFTurnTableData.Instance:GetLayerCfgByLayerNum(self.cur_turntable_layer)
	local yaogan = layer_cfg.yaogan
	local renwu = layer_cfg.renwu
	if not yaogan or not renwu then
		return
	end

	local yangao_cur = self.cache_yaogan_list[yaogan]
	local renwu_cur = self.cache_yaogan_nv_list[renwu]
	if not yangao_cur or not renwu_cur then
		return
	end

	yangao_cur.AnimationState:SetAnimation(0, "la", true)
	renwu_cur.AnimationState:SetAnimation(0, "la", true)

	ReDelayCall(self, function ()
		yangao_cur.AnimationState:SetAnimation(0, "idle", true)
		renwu_cur.AnimationState:SetAnimation(0, "idle", true)
	end, 1.6, "yaogan_nv_xiala")
end

--抽奖
function ActXianQiJieFengView:OnClickTurnTableBtnDraw()
    if self.onclick_turntable_draw or self.turntable_is_animing then
		return
	end

    local is_empty = XQJFTurnTableData.Instance:GetTurnTableIsDrawEmptyByLayer(self.cur_turntable_layer)
    if is_empty then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OATurnTable.DrawOut)
    else
		local cur_layer_index = self:GetTurnTableLayerIndex()
		if self.turntable_once_draw[cur_layer_index] then
			self:OnClickTurnTableBtnOnceDraw()
		else
			self:GetTurnTableDrawInfo()
		end
    end
end

-- 点击一键抽奖
function ActXianQiJieFengView:OnClickTurnTableBtnOnceDraw()
	local cur_draw_info = XQJFTurnTableData.Instance:GetAllDrawInfo(self.cur_turntable_layer)
	if cur_draw_info == nil or cur_draw_info.draw_consume_item_count == nil then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OATurnTable.DrawOut)
		self.onclick_turntable_draw = false
		return
	end
	local num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_info.draw_consume_item_id)
	local dif_num = cur_draw_info.draw_consume_item_count - num
	if dif_num > 0 then
		local layer_cfg = XQJFTurnTableData.Instance:GetLayerCfgByLayerNum(self.cur_turntable_layer)
		local item_cfg = ItemWGData.Instance:GetItemConfig(layer_cfg.draw_consume_item_id)
		if layer_cfg.conmsum_xianyu > 0 and item_cfg then
			local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			local str = string.format(Language.OATurnTable.ItemNotEnough, dif_num, item_name, layer_cfg.conmsum_xianyu*dif_num)
			self:GetTurnTableOneKeyAlert()
			self.turntable_alert:SetLableString(str)
			self.turntable_alert:Open()
		else
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cur_draw_info.draw_consume_item_id})
		end
	else
		self:SendTurnTableDrawOneKey()
		-- self:OnStartYaoGanLa()
	end
end

function ActXianQiJieFengView:GetTurnTableDrawInfo()
	local cur_draw_info = XQJFTurnTableData.Instance:GetTurnTableCurDrawInfoByLayer(self.cur_turntable_layer)
	if cur_draw_info == nil or cur_draw_info.draw_consume_item_count == nil then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OATurnTable.DrawOut)
		self.onclick_turntable_draw = false
		return
	end
	local num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_info.draw_consume_item_id)
	local dif_num = cur_draw_info.draw_consume_item_count - num
	if dif_num > 0 then
		local layer_cfg = XQJFTurnTableData.Instance:GetLayerCfgByLayerNum(self.cur_turntable_layer)
		local item_cfg = ItemWGData.Instance:GetItemConfig(layer_cfg.draw_consume_item_id)
		if layer_cfg.conmsum_xianyu > 0 and item_cfg then
			local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			local str = string.format(Language.OATurnTable.ItemNotEnough, dif_num, item_name, layer_cfg.conmsum_xianyu*dif_num)
			self:GetTurnTableAlert()
			self.turntable_alert:SetLableString(str)
			self.turntable_alert:Open()
		else
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cur_draw_info.draw_consume_item_id})
		end
	else
		self:SendTurnTableDraw()
		-- self:OnStartYaoGanLa()
	end
end

function ActXianQiJieFengView:SendTurnTableDraw()
	local cur_draw_info = XQJFTurnTableData.Instance:GetTurnTableCurDrawInfoByLayer(self.cur_turntable_layer)
	if cur_draw_info == nil or cur_draw_info.draw_consume_item_count == nil then
		return
	end
	local num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_info.draw_consume_item_id)
	local dif_num = cur_draw_info.draw_consume_item_count - num
	local need_buy = dif_num > 0 and 1 or 0

	self.onclick_turntable_draw = true
	ActXianQiJieFengWGCtrl.Instance:SendOpera(RA_TUNVLANG_OPERA_TYPE.DRAW, self.cur_turntable_layer, need_buy)
	-- self:OnStartYaoGanLa()

	if self.turntable_click_draw_timequest then
		GlobalTimerQuest:CancelQuest(self.turntable_click_draw_timequest)
		self.turntable_click_draw_timequest = nil
	end

	self.turntable_click_draw_timequest = GlobalTimerQuest:AddDelayTimer(function()
		self.onclick_turntable_draw = false
	end, 1)
end

-- 发送一键抽奖请求
function ActXianQiJieFengView:SendTurnTableDrawOneKey()
	-- 忽略动画
	if self.turntable_ignore_anim then
		self:SendTurnTableDrawOneKeyLogic()
	else
		self.turntable_simulate_anim_callback = BindTool.Bind(self.SendTurnTableDrawOneKeyLogic, self)
		self:StartTurnTableAnim()
	end
end

function ActXianQiJieFengView:SendTurnTableDrawOneKeyLogic()
	local cur_draw_info = XQJFTurnTableData.Instance:GetAllDrawInfo(self.cur_turntable_layer)
	if cur_draw_info == nil or cur_draw_info.draw_consume_item_count == nil then
		return
	end
	local num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_info.draw_consume_item_id)
	local dif_num = cur_draw_info.draw_consume_item_count - num
	local need_buy = dif_num > 0 and 1 or 0

	self.onclick_turntable_draw = true
	ActXianQiJieFengWGCtrl.Instance:SendOpera(RA_TUNVLANG_OPERA_TYPE.DRAW_ONEKEY, self.cur_turntable_layer, need_buy)
	-- self:OnStartYaoGanLa()

	if self.turntable_click_draw_timequest then
		GlobalTimerQuest:CancelQuest(self.turntable_click_draw_timequest)
		self.turntable_click_draw_timequest = nil
	end

	self.turntable_click_draw_timequest = GlobalTimerQuest:AddDelayTimer(function()
		self.onclick_turntable_draw = false
	end, 1)
end

function ActXianQiJieFengView:GetTurnTableAlert()
	if not self.turntable_alert then
		self.turntable_alert = Alert.New()
		self.turntable_alert:SetShowCheckBox(true, XQJF_TURNTABLE_COUNTDOWN_KEY_STR)
		self.turntable_alert:SetCheckBoxText(Language.OATurnTable.AlertTip)
	end
	self.turntable_alert:SetOkFunc(BindTool.Bind(self.SendTurnTableDraw, self))
end

function ActXianQiJieFengView:GetTurnTableOneKeyAlert()
	if not self.turntable_alert then
		self.turntable_alert = Alert.New()
		self.turntable_alert:SetShowCheckBox(true, XQJF_TURNTABLE_COUNTDOWN_KEY_STR .. "onekey")
		self.turntable_alert:SetCheckBoxText(Language.OATurnTable.AlertTip)
	end
	self.turntable_alert:SetOkFunc(BindTool.Bind(self.SendTurnTableDrawOneKey, self))
end

--忽略动画
function ActXianQiJieFengView:OnClickTurnTableBtnCheck()
    self.turntable_ignore_anim = not self.turntable_ignore_anim
	self.node_list["turntable_anim_yes"]:SetActive(self.turntable_ignore_anim)
	local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	PlayerPrefsUtil.SetInt(XQJF_TURNTABLE_COUNTDOWN_KEY_STR .. "_ANIM" .. role_id, self.turntable_ignore_anim and 1 or 0)
end

--记录
function ActXianQiJieFengView:OnClickTurnTableBtnRecoed()
    ViewManager.Instance:Open(GuideModuleName.XQJFTurnTableRecord)
	XQJFTurnTableData.Instance:ClearRecordCount()
	self:FlushTurnTableRecordNum()
end

--重置
function ActXianQiJieFengView:OnClickTurnTableBtnReset()
    if self.onclick_turntable_draw or self.turntable_is_animing then
		return
	end

	if XQJFTurnTableData.Instance:CheckCanResetByLayer(self.cur_turntable_layer) then
		ActXianQiJieFengWGCtrl.Instance:SendOpera(RA_TUNVLANG_OPERA_TYPE.RESET_LAYER, self.cur_turntable_layer)
	else
		local str
		-- 大奖没有被抽取
		if XQJFTurnTableData.Instance:GetRemainDrawTimesByLayer(self.cur_turntable_layer) > 0 then
			str = Language.OATurnTable.ResetTip
		elseif XQJFTurnTableData.Instance:CheckHasNextRound(self.cur_turntable_layer) then
			local time = XQJFTurnTableData.Instance:GetTurnTableRoundTimeByLayer(self.cur_turntable_layer)
			str = string.format(Language.OATurnTable.TimeFlush, TimeUtil.FormatSecondDHM6(time))
		else
			str = Language.OATurnTable.DrawOut
		end
		SysMsgWGCtrl.Instance:ErrorRemind(str)
	end
end

function ActXianQiJieFengView:GetTurnTableLayerIndex()
	local layer_list = XQJFTurnTableData.Instance:GetCurActLayerList()
	local cur_layer_index = 1
	for i=1,OA_TURNTABLE_LAYER_MAX do
		if layer_list[i] == self.cur_turntable_layer then
			cur_layer_index = i
		end
	end

	return cur_layer_index
end

--一键抽奖
function ActXianQiJieFengView:OnClickTurnTableBtnOnceAll()
	local cur_layer_index = self:GetTurnTableLayerIndex()

    self.turntable_once_draw[cur_layer_index] = not self.turntable_once_draw[cur_layer_index]
	self.node_list["turntable_once_yes"]:SetActive(self.turntable_once_draw[cur_layer_index])
	local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	PlayerPrefsUtil.SetInt(XQJF_TURNTABLE_COUNTDOWN_KEY_STR .. "_ONCE" .. role_id .. cur_layer_index, self.turntable_once_draw[cur_layer_index] and 1 or 0)
	self:ChangeTurnTableButtonShow()
	self:ChangeTurnTableCostInfo()
end

--抽奖动画开始
function ActXianQiJieFengView:FlushTurnTableView(info)
	for i, v in pairs(info) do
		if v == "normal" then
			self:FlushTurnTableNormalShow()
			if self.cur_turntable_layer then
				self:OnClickTurnTableLayer(nil, self.cur_turntable_layer)
			end
		elseif v == "record" then
			self:FlushTurnTableRecordNum()
		elseif v == "result" then
			self:StartTurnTableAnim()
		elseif v == "draw_all" then
			if self.cur_turntable_layer then
				self:OnClickTurnTableLayer(nil, self.cur_turntable_layer)
			end
		elseif v == "layer" then
			if self.cur_turntable_layer == info.layer then
				self:OnClickTurnTableLayer(nil, self.cur_turntable_layer)
			end
		end
	end

	self:FlushTurnTableLayerRed()
end

function ActXianQiJieFengView:FlushTurnTableNormalShow()
	self:ChangeTurnTableResetBtn()
end

--刷新层数的红点显示
function ActXianQiJieFengView:FlushTurnTableLayerRed()
	local is_show_red = false

	-- 拿到挡位配置信息
	local layer_list = XQJFTurnTableData.Instance:GetCurActLayerList()
	local layer_count = #layer_list
	for i = 1, OA_TURNTABLE_LAYER_MAX do
		if layer_list[i] then
			is_show_red = XQJFTurnTableData.Instance:IsShowRedByLayer(layer_list[i])
			self.node_list["turntable_layer_red_" .. i]:SetActive(is_show_red and layer_count > 1)
		end
	end

end

function ActXianQiJieFengView:StartTurnTableAnim()
	if self.turntable_ignore_anim then
		local reward_info = XQJFTurnTableData.Instance:GetTurnTableCurDrawResultByLayer(self.cur_turntable_layer)
		local ball = self:GetTurnTableRewardBall(reward_info)
		if ball then
			ball:SetBallState(false)
		end
		self:FlushTurnTableAnimEnd(reward_info)
	else
		self:ChangeTurnTableLayerBtnState(true)
		self:PrepareAnimTime()
		self:OnStartYaoGanLa()
	end
	self:ChangeTurnTableResetBtn()
end

--动画结束刷新对应面板
function ActXianQiJieFengView:FlushTurnTableAnimEnd(reward_info, close_func)
	local is_empty = XQJFTurnTableData.Instance:GetTurnTableIsDrawEmptyByLayer(self.cur_turntable_layer)
	--打开奖励界面（通用奖励获取？）
	self:ShowTurnTableGetReward(reward_info, close_func)
	self:ResetTurnTableAnim()
	if is_empty then
		self:ChangeTurnTablePoolTime()
	end
	self:ChangeTurnTableCostInfo()
	self:ChangeTurnTableButtonShow()
end

--动画期间不能切换层数
function ActXianQiJieFengView:ChangeTurnTableLayerBtnState(status)
	self.turntable_is_animing = status
	for i = 1, OA_TURNTABLE_LAYER_MAX do
		self.node_list["turntable_layer_" .. i].button.interactable = not status
	end
end

--动画准备和开始
function ActXianQiJieFengView:PrepareAnimTime()
	local reward_info = XQJFTurnTableData.Instance:GetTurnTableCurDrawResultByLayer(self.cur_turntable_layer)
	if not reward_info then
		return
	end
	local times_list, rounds = XQJFTurnTableData.Instance:GetCircleRotNum()
	local tween
	local def_angle = 0
	local rot_list = {}
	local tween_check = {}
	local layer = self.cur_turntable_layer
	for i = 1, OA_TURNTABLE_LAYER_MAX do
		if i == reward_info.rank then
			local v2_reward = Vector2(reward_info.x, reward_info.y)
			local rot_angle = Vector2.Angle(self.turntable_win_vector2, v2_reward)
			local direct = (v2_reward.x * self.turntable_win_vector2.y - v2_reward.y * self.turntable_win_vector2.x)
			rot_angle = direct > 0 and rot_angle or -rot_angle
			def_angle = (-360 * times_list[i] + rot_angle) -- 2.5
			rot_list[i] = rot_angle
		else
			-- 取一个随机角度
			rot_list[i] =  GameMath.Rand(0, 360)
			local tolerance = XQJFTurnTableData.Instance:CheckAngleHasReward(layer, rot_list[i], i)
			rot_list[i] = rot_list[i] + tolerance
			def_angle = (-360 * times_list[i] + rot_list[i]) -- 2.5
		end

		tween = self.node_list["turntable_circle_" .. i].rect:DORotate(
				Vector3(0, 0, def_angle),
				rounds,
				DG.Tweening.RotateMode.FastBeyond360):OnComplete(function()
					-- self.node_list["turntable_circle_" .. i].rect.localRotation = Quaternion.Euler(0, 0, rot_list[i])
					tween_check[i] = {}
					tween_check[i].layer = layer
					tween_check[i].rot = rot_list[i]
					XQJFTurnTableData.Instance:CacheTurnTableAngleByLayerAndRank(layer, i, rot_list[i])
					self:CheckShowTurnTableEndAnim(tween_check)
 				end)

		tween:SetEase(DG.Tweening.Ease.OutQuint)
	end

	--因为tween动画报错会停止，所以加一个最大时长结束动画
	ReDelayCall(self, function()
		if self.turntbale_anim_playing or self.turntable_is_animing then
			self:FlushTurnTableAnimEnd()
		end
	end, 5, "oa_turntable1")
end

-- 重置物品球位置
function ActXianQiJieFengView:ResetTurntableItemRotate(index)
	if self.turntable_ball_list then
		local rotate = self.node_list["turntable_circle_" .. index].rect.localEulerAngles.z
		rotate = math.abs(rotate - 360) 
		for i,v in ipairs(self.turntable_ball_list) do
			local data = v:GetData()
			if data and data.rank == index then
				v:SetParentRotate(rotate)
			end
		end
	end
end

--奖励滑落
function ActXianQiJieFengView:CheckShowTurnTableEndAnim(tween_check)
	-- print_error('tween_check',tween_check)
	for i = 1, OA_TURNTABLE_LAYER_MAX do
		if tween_check[i] == nil then
			return
		end
	end

	if self.turntable_simulate_anim_callback then
		local fun = self.turntable_simulate_anim_callback
		self.turntable_simulate_anim_callback = nil
		fun()
		return
	end

	ActXianQiJieFengWGCtrl.Instance:SendOpera(RA_TUNVLANG_OPERA_TYPE.SET_DIR, self.cur_turntable_layer,
			tween_check[1].angle,
			tween_check[2].angle)

	local reward_info = XQJFTurnTableData.Instance:GetTurnTableCurDrawResultByLayer(self.cur_turntable_layer)
	local ball = self:GetTurnTableRewardBall(reward_info)
	if not ball then
		self:FlushTurnTableAnimEnd(reward_info)
		return
	end
	
	ball:SetBallState(true)
	ball:SetParentNoMove(self.node_list["turntable_pole"].rect)

	if self.ball_anim then
		self.ball_anim:Kill()
		self.ball_anim = nil
	end
	
	self.ball_anim = DG.Tweening.DOTween.Sequence()

	local time1 = 1.0
	ball:PlayMoveAnim(self.node_list["turntable_reward_end"].rect, time1)
	self.ball_anim:AppendInterval(time1)
	self.ball_anim:AppendCallback(function()
		self:FlushTurnTableAnimEnd(reward_info, function ()
			if ball then
				ball:SetBallState(false)
			end
			for i = 1, OA_TURNTABLE_LAYER_MAX do
				self:ResetTurntableItemRotate(i)
			end
			self:ChangeTurnTableLayerBtnState(false)
		end)
	end)

end

--拿到对应的ball
function ActXianQiJieFengView:GetTurnTableRewardBall(reward_info)
	for i, v in pairs(self.turntable_ball_list) do
		if v.data and v.data.reward_id == reward_info.reward_id then
			return v
		end
	end
	return nil
end

function ActXianQiJieFengView:ShowTurnTableGetReward(reward_info, close_func)
	if reward_info then
		close_func = close_func
		TipWGCtrl.Instance:ShowGetReward(nil, {reward_info.reward_item}, reward_info.is_best == 1, nil, nil, close_func)
	end
end

function ActXianQiJieFengView:ResetTurnTableAnim()
	--可能要恢复兔女郎的动作之类的
end

--点击层数切换
function ActXianQiJieFengView:OnClickTurnTableLayer(layer, layer_num)
    self:ChangeTurnTableAngle(self.cur_turntable_layer ~= layer_num)
    self.cur_turntable_layer = layer_num
    local cfg = XQJFTurnTableData.Instance:GetLayerCfgByLayerNum(layer_num)
    self:ChangeTurnTableLayerShow(cfg)
    self:ChangeTurnTableRewardShow()
    self:ChangeTurnTablePoolTime()
	self:ChangeTurnTableResetBtn()
	self:ChangeTurnTableOnceShow(cfg)
	self:ChangeTurnTableCostInfo()
	self:ChangeTurnTableButtonShow()
	self:ChangeTurnTableLayerBtnShow(layer)
	self:FlushTurnTableOnceCheck()
	self:ChangeYaoGanState()
end

-- 刷新遥感显隐
function ActXianQiJieFengView:ChangeYaoGanState()

	local layer_list = XQJFTurnTableData.Instance:GetCurActLayerList()
	local layer_cfg = XQJFTurnTableData.Instance:GetLayerCfgByLayerNum(self.cur_turntable_layer)
	local yaogan = layer_cfg.yaogan
	local renwu = layer_cfg.renwu
	if not yaogan or not renwu then
		return
	end

	for i=1,3 do
		self.node_list['layer_gan_' .. i]:SetActive(('layer_gan_' .. i) == yaogan)
	end

	for i=1,1 do
		self.node_list['layer_renwu_' .. i]:SetActive(('layer_renwu_' .. i) == renwu)
	end

end

--刷新转盘角度
function ActXianQiJieFengView:ChangeTurnTableAngle(need_reset)
	if need_reset then
	    for i = 1, OA_TURNTABLE_RANK_MAX do
	        local angle = XQJFTurnTableData.Instance:GetTurnTableAngleByLayerAndRank(self.cur_turntable_layer, i)
	        self.node_list["turntable_circle_" .. i].rect.localEulerAngles = Vector3(0,0,angle)
	    end
    end
end

-- 播放界面打开摇杆动画
function ActXianQiJieFengView:DoPlayTweenByGan()
	self.node_list["turntable_pole"].rect.rotation = Quaternion.Euler(0, 0,30)
	local tween = self.node_list["turntable_pole"].rect:DORotate(
		Vector3(0, 0, 0),
		1,
		DG.Tweening.RotateMode.FastBeyond360)
		tween:SetEase(DG.Tweening.Ease.OutExpo)
end

--刷新ui界面
function ActXianQiJieFengView:ChangeTurnTableLayerShow(cfg)
	local layer_cfg = cfg
	local str
	for i = 1, OA_TURNTABLE_RANK_MAX do
		if layer_cfg["turntable_circle_" .. i] ~= "" then
			self.node_list["turntable_circle_" .. i]:SetActive(true)
			str = layer_cfg["turntable_circle_" .. i] ~= "" and layer_cfg["turntable_circle_" .. i] or "turntable_" .. self.cur_turntable_layer .. "_" .. i
			local bundle,asset = ResPath.GetF2RawImagesPNG(str)
			self.node_list["turntable_circle_" .. i].raw_image:LoadSprite(bundle, asset, function ()
				self.node_list["turntable_circle_" .. i].raw_image:SetNativeSize()
			end)
		else
			self.node_list["turntable_circle_" .. i]:SetActive(false)
		end
	end

	str = layer_cfg.pole ~= "" and layer_cfg.pole or "turntable_gan"
	self.node_list["turntable_pole"].raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(str))

end

--刷新奖励展示
function ActXianQiJieFengView:ChangeTurnTableRewardShow()
    local reward_id_list = XQJFTurnTableData.Instance:GetTurnTableRewardListByLayer(self.cur_turntable_layer)
    local layer_cfg = XQJFTurnTableData.Instance:GetLayerCfgByLayerNum(self.cur_turntable_layer)
    local bundle, asset = "uis/view/act_xianqi_jiefeng_ui_prefab", "turntable_ball_render"
    -- for i, v in ipairs(reward_id_list) do
    --     local state = XQJFTurnTableData.Instance:GetTurnTableRewardIsShowByLayerAndIndex(self.cur_turntable_layer, i)
    --     local info = XQJFTurnTableData.Instance:GetTurnTableRewardInfoById(v)
    --     if not self.turntable_ball_list[i] then
    --         self.turntable_ball_list[i] = OATurnTableBallRender.New()
    --         self.turntable_ball_list[i]:LoadAsset(bundle, asset, self.node_list["turntable_circle_" .. info.rank].rect)
    --     else
    --         self.turntable_ball_list[i]:SetInstanceParent(self.node_list["turntable_circle_" .. info.rank])
    --     end
    --     if state then
    --         self.turntable_ball_list[i]:SetEffect(layer_cfg.effect)
    --         self.turntable_ball_list[i]:SetData(info)
    --     end
    --     self.turntable_ball_list[i]:SetBallState(state)
    -- end

    -- for i = #reward_id_list+1, #self.turntable_ball_list do
	-- 	if self.turntable_ball_list[i] then
	-- 		self.turntable_ball_list[i]:SetBallState(false)
	-- 	end
	-- end
end

--刷新奖池展示时间
function ActXianQiJieFengView:ChangeTurnTablePoolTime()
    local next_time, _ = XQJFTurnTableData.Instance:GetTurnTableRoundTimeByLayer(self.cur_turntable_layer)
    local is_empty = XQJFTurnTableData.Instance:GetTurnTableIsDrawEmptyByLayer(self.cur_turntable_layer)
    self:ChangeTurnTableButtonState(is_empty)

    local CDI = CountDownManager.Instance
    if CDI:HasCountDown(XQJF_TURNTABLE_COUNTDOWN_KEY_STR) then
        CDI:RemoveCountDown(XQJF_TURNTABLE_COUNTDOWN_KEY_STR)
    end

    -- print_error('next_time--------',next_time)

    if not is_empty then
        self.node_list.turntable_act_time1.text.text = string.format(Language.OATurnTable.TimeCount1, "") 
      	if next_time > 0 then
       		CDI:AddCountDown(XQJF_TURNTABLE_COUNTDOWN_KEY_STR, BindTool.Bind(self.UpdateTurnTablePoolTime, self, is_empty),
					BindTool.Bind(self.CompleteTurnTablePoolTime, self), nil, next_time, 0.3)
    	end
    else
		if XQJFTurnTableData.Instance:CheckHasNextRound(self.cur_turntable_layer) then
	      	if next_time > 0 then
				CDI:AddCountDown(XQJF_TURNTABLE_COUNTDOWN_KEY_STR, BindTool.Bind(self.UpdateTurnTablePoolTime, self, is_empty),
						BindTool.Bind(self.CompleteTurnTablePoolTime, self), nil, next_time, 0.3)
	    	end
		else
			self.node_list["turntable_act_time1"].text.text = Language.OATurnTable.DrawOut
		end
    end
end

--刷新按钮状态
function ActXianQiJieFengView:ChangeTurnTableButtonState(is_empty)
	XUI.SetGraphicGrey(self.node_list["turntable_btn_draw"], is_empty)
	self.node_list.turntable_btn_effect:SetActive(not is_empty)
end

--刷新按钮状态
function ActXianQiJieFengView:UpdateTurnTablePoolTime(is_empty, elapse_time, total_time)
    local str = ""
	local temp_seconds = GameMath.Round(total_time - elapse_time)
	local remain_time = TimeUtil.FormatSecondDHM6(temp_seconds)
	remain_time = ToColorStr(remain_time, COLOR3B.RED)

	if is_empty then
		if XQJFTurnTableData.Instance:CheckHasNextRound(self.cur_turntable_layer) then
			str = string.format(Language.OATurnTable.TimeCount2, remain_time)
		else
			str = string.format(Language.OATurnTable.TimeCount1, remain_time)
		end
	else
		str = string.format(Language.OATurnTable.TimeCount1, remain_time)
	end

	if self.node_list["turntable_act_time1"] then
		self.node_list["turntable_act_time1"].text.text = str
	end

	--if self.node_list["turntable_act_time2"] then
	--	self.node_list["turntable_act_time2"].text.text = remain_time
	--end
end

--刷新按钮状态
function ActXianQiJieFengView:CompleteTurnTablePoolTime()
    self:OnClickTurnTableLayer(nil, self.cur_turntable_layer or 1)
	RemindManager.Instance:Fire(RemindName.OATurnTable)
end

--刷新reset按钮状态
function ActXianQiJieFengView:ChangeTurnTableResetBtn()
	local can_reset = XQJFTurnTableData.Instance:CheckCanResetByLayer(self.cur_turntable_layer)
	XUI.SetGraphicGrey(self.node_list["turntable_btn_reset"], not can_reset)
	self.node_list["turntable_reset_show"]:SetActive(can_reset)
end

--刷新一键抽奖是否显示
function ActXianQiJieFengView:ChangeTurnTableOnceShow(cfg)
	if not cfg then return end
	-- 一键抽奖需要vip等级限制
	local vip_level = VipWGData.Instance:GetRoleVipLevel()
	local is_vip = VipWGData.Instance:IsVip()
	local vip_flag = vip_level >= (cfg.is_open_one_key_vip or 0) and is_vip

	self.node_list["turntable_once_show"]:SetActive(cfg.is_open_one_key_lottery == 1 and vip_flag)
	local cur_layer_index = self:GetTurnTableLayerIndex()
	if cfg.is_show_onec == 0 and self.turntable_once_draw[cur_layer_index] then
		self:OnClickTurnTableBtnOnceAll()
	end
end

--刷新消耗信息
function ActXianQiJieFengView:ChangeTurnTableCostInfo()
	local cur_draw_info
	local cur_layer_index = self:GetTurnTableLayerIndex()
	if self.turntable_once_draw[cur_layer_index] then
		cur_draw_info = XQJFTurnTableData.Instance:GetAllDrawInfo(self.cur_turntable_layer)
	else
		cur_draw_info = XQJFTurnTableData.Instance:GetTurnTableCurDrawInfoByLayer(self.cur_turntable_layer)
	end

	if cur_draw_info == nil then
		return
	end

	if cur_draw_info.draw_consume_item_count then
		local num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_info.draw_consume_item_id)
		local color = num >= cur_draw_info.draw_consume_item_count and COLOR3B.GREEN or COLOR3B.RED
		self.node_list["turntable_cost_txt"].text.text = ToColorStr(num.. "/" .. cur_draw_info.draw_consume_item_count, color)
		self.node_list["turntable_draw_red"]:SetActive((num >= cur_draw_info.draw_consume_item_count) and cur_draw_info.draw_consume_item_count > 0)
		----2020/04/25 22:50 策划需求去掉钱够显示红点
		--or RoleWGData.Instance:GetRoleVo().gold >= cur_draw_info.conmsum_xianyu * cur_draw_info.draw_consume_item_count
	else
		self.node_list["turntable_cost_txt"].text.text = "-/-"
		self.node_list["turntable_draw_red"]:SetActive(false)
	end
	self:LoadTurnTableCostImg(cur_draw_info.draw_consume_item_id)
end

function ActXianQiJieFengView:OnClickTurnTableLayerItem()
	if not self.turntable_cost_img_cache then
		return
	end
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.turntable_cost_img_cache})
	
end

function ActXianQiJieFengView:LoadTurnTableCostImg(item_id)
	if self.turntable_cost_img_cache ~= item_id then
		self.turntable_cost_img_cache = item_id
		local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
		if not item_cfg then
			return
		end

		local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
		self.node_list["turntable_cost_img"].image:LoadSprite(bundle, asset, function()
			self.node_list["turntable_cost_img"].image:SetNativeSize()
		end)
	end
end

--刷新layerbtn的高亮
function ActXianQiJieFengView:ChangeTurnTableLayerBtnShow(layer)
	if not layer then
		return
	end
	-- for i = 1, OA_TURNTABLE_LAYER_MAX do
	-- 	self.node_list["turntable_layer_" .. i .. "_nor"]:SetActive(i ~= layer)
	-- 	self.node_list["turntable_layer_" .. i .. "_high"]:SetActive(i == layer)
	-- end
end

--刷新button的显示
function ActXianQiJieFengView:ChangeTurnTableButtonShow()
	local cur_layer_index = self:GetTurnTableLayerIndex()

	--计算剩余抽奖次数
	local remains = XQJFTurnTableData.Instance:GetRemainDrawTimesByLayer(self.cur_turntable_layer)
	remains = remains < 0 and 0 or remains

	if self.turntable_once_draw[cur_layer_index] then
		self.node_list["turntable_btn_draw_txt"].text.text = string.format(Language.OATurnTable.OnceBtnShow, remains)
	else
		self.node_list["turntable_btn_draw_txt"].text.text = Language.OATurnTable.DrawBtnShow
	end

	self.node_list.turntable_plate_bg:SetActive(remains <= 0)
	if remains <= 0 then
		local can_reset = XQJFTurnTableData.Instance:CheckCanResetByLayer(self.cur_turntable_layer)
		local index 
		if can_reset then
			index = 2
		elseif XQJFTurnTableData.Instance:CheckHasNextRound(self.cur_turntable_layer) then
			index = 1
		else
			index = 3
		end

		local bundle, asset = ResPath.GetOATurnTablePath("bg_tip_" .. index)
		self.node_list["turntable_plate_img"].image:LoadSprite(bundle, asset, function()
			self.node_list["turntable_plate_img"].image:SetNativeSize()
		end)
	end

end

function ActXianQiJieFengView:FlushTurnTableRecordNum()
	local num = XQJFTurnTableData.Instance:GetNewRecordCount()
	--print_error("record", num)
	if num > 0 then
		self.node_list["turntable_record_show"]:SetActive(true)
		self.node_list["turntable_record_text"].text.text = num
	else
		self.node_list["turntable_record_show"]:SetActive(false)
	end
end

function ActXianQiJieFengView:UpdateTurnTableRecord()
	local count =  XQJFTurnTableData.Instance:GetNewRecordCount()
	XQJFTurnTableData.Instance:SetNewRecordCount(count + 1)
	if self:GetShowIndex() == TabIndex.xianqi_jiefeng_turntable then
		self:FlushTurnTableRecordNum()
	end
end
