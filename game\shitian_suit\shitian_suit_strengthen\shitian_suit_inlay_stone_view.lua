ShiTianInlayStoneView = ShiTianInlayStoneView or BaseClass(SafeBaseView)

function ShiTianInlayStoneView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/shitian_suit_ui_prefab", "layout_inlay_gemstone")
end

function ShiTianInlayStoneView:LoadCallBack()
    if not self.gemstone_list then
        self.gemstone_list = AsyncListView.New(ShiTianInlayStoneRender, self.node_list.gemstone_list)
        self.gemstone_list:SetCreateCellCallBack(BindTool.Bind(self.CreateStoneCallBack, self))
    end
end

function ShiTianInlayStoneView:ReleaseCallBack()
    if self.gemstone_list then
        self.gemstone_list:DeleteMe()
        self.gemstone_list = nil
    end
end

function ShiTianInlayStoneView:OnFlush()
    if not self.show_data then
        return
    end

    local stone_list = ItemWGData.Instance:GetShiTianStoneItemList(self.show_data.suit_seq, self.show_data.stone_type)
	table.sort(stone_list, SortTools.KeyUpperSorter("item_id"))
    local target_cfg = ShiTianSuitStrengthenWGData.Instance:GetStoneCfg(self.show_data.ep_stone_id)
    local data_list = {}
    for k, v in pairs(stone_list) do
        local cur_stone_cfg = ShiTianSuitStrengthenWGData.Instance:GetStoneCfg(v.item_id)
        local level = cur_stone_cfg and cur_stone_cfg.level or 1
        data_list[k] = {
            item_id = v.item_id,
            num = v.num,
            level = level,
            ep_stone_level = target_cfg and target_cfg.level or 0,
            suit_seq = self.show_data.suit_seq,
            part = self.show_data.part,
            slot = self.show_data.slot,
        }
    end

	table.sort(data_list, SortTools.KeyUpperSorter("level"))

    self.gemstone_list:SetDataList(data_list)
end

function ShiTianInlayStoneView:SetShowData(show_data)
    self.show_data = show_data
end

function ShiTianInlayStoneView:CreateStoneCallBack(cell)
    cell:SetSelectStoneCallBack(BindTool.Bind(self.Close, self))
end

-------------- 宝石格子 --------------
ShiTianInlayStoneRender = ShiTianInlayStoneRender or BaseClass(BaseRender)

function ShiTianInlayStoneRender:LoadCallBack()
    self.item_cell = ItemCell.New(self.node_list.cell_pos)
    XUI.AddClickEventListener(self.node_list.root, BindTool.Bind(self.OnClickInlayBtn, self))
end

function ShiTianInlayStoneRender:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end

    self.select_end_cb = nil
end

function ShiTianInlayStoneRender:OnFlush()
    if not self.data then
        return
    end

    local cur_stone_cfg = ShiTianSuitStrengthenWGData.Instance:GetStoneCfg(self.data.item_id)
    if not cur_stone_cfg then
        return
    end

    self.item_cell:SetData({item_id = self.data.item_id, num = self.data.num})
    self.node_list.up_flag:SetActive(cur_stone_cfg.level > self.data.ep_stone_level)
    local attr_list = EquipWGData.GetSortAttrListByTypeCfg(cur_stone_cfg, "attr_id", "attr_val", 1, 2)
    for i = 1, #attr_list do
        local attr = attr_list[i]
        self.node_list["attr" .. i]:SetActive(not IsEmptyTable(attr))
        if attr then
            self.node_list["attr" .. i].text.text = ShiTianSuitStrengthenWGData.GetStoneDescForCfg(attr.attr_str, attr.attr_value)
        end
    end
end

function ShiTianInlayStoneRender:OnClickInlayBtn()
    if not self.data then
        return
    end

    local bag_index = ItemWGData.Instance:GetItemIndex(self.data.item_id)
    ShiTianSuitStrengthenWGCtrl.Instance:SendCSShiTianStrengthenReq(SHITIANSTONE_OPERA_TYPE.TONE_INLAY, self.data.suit_seq, self.data.part, self.data.slot, bag_index)

    if self.select_end_cb then
        self.select_end_cb()
    end
end

function ShiTianInlayStoneRender:SetSelectStoneCallBack(cb)
    if cb then
        self.select_end_cb = cb
    end
end