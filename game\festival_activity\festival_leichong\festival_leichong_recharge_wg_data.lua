FestivalLeiChongRechargeWGData = FestivalLeiChongRechargeWGData or BaseClass()
FestivalLeiChongRechargeWGData.ConfigPath= "config/auto_new/festival_activity_limit_total_recharge_auto"
function FestivalLeiChongRechargeWGData:__init()
	if FestivalLeiChongRechargeWGData.Instance then
		ErrorLog("[FestivalLeiChongRechargeWGData] Attemp to create a singleton twice !")
	end
	FestivalLeiChongRechargeWGData.Instance = self

	self:InitLeiChongRechargeData()
	FestivalActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.FESTIVAL_ACT_OA_LIMIT_RECHARGE, {[1] = MERGE_EVENT_TYPE.LEVEL}, 
    	BindTool.Bind(self.GetActCanOpen, self), BindTool.Bind(self.IsShowLCRechargeRedPoint, self))
	RemindManager.Instance:Register(RemindName.FestivalTotalRecharge, BindTool.Bind(self.IsShowLCRechargeRedPoint, self))
	
end

function FestivalLeiChongRechargeWGData:__delete()
	FestivalLeiChongRechargeWGData.Instance = nil 
	RemindManager.Instance:UnRegister(RemindName.FestivalTotalRecharge)
end

function FestivalLeiChongRechargeWGData:SetLeiChongRechargeData(protocol)
	if self.LeiChongData == nil then
		self.LeiChongData = {}
	end
	self.LeiChongData.cur_xianyu = protocol.cur_xianyu
	self.LeiChongData.activity_start_combineserver_day = protocol.activity_start_combineserver_day
	self.LeiChongData.dangwei_info = protocol.dangwei_info

end

function FestivalLeiChongRechargeWGData:InitLeiChongRechargeData()
	self.merge_leichong_recharge_cfg = ConfigManager.Instance:GetAutoConfig("festival_activity_limit_total_recharge_auto")
	self.leichong_reward_cfg = self.merge_leichong_recharge_cfg.reward
	self.leichong_param = self.merge_leichong_recharge_cfg.config_param
	self.leichong_param_cfg = ListToMap(self.leichong_param, "grade")
	self.model_info = ListToMap(self.merge_leichong_recharge_cfg.interface, "grade")
end

--获取累充奖励排行数据
function FestivalLeiChongRechargeWGData:GetLeiChongRewardList()
	local list = {}
	local cfg = self:GetLeiChongRewardCfg()
	for i,v in ipairs(cfg) do
		local data = {}
		data.cfg = v
		data.ID = v.ID
		data.receive_state = self:GetLeiChongReceiveState(v.ID)
		if data.receive_state == TianShenRoadRewardState.KLQ then
			data.sort = 0
		elseif data.receive_state == TianShenRoadRewardState.BKL then
			data.sort = 1
		else
			data.sort = 2
		end
		table.insert(list, data)
	end
	table.sort(list, SortTools.KeyLowerSorter("sort", "ID"))
	return list
end

--获取领取状态
function FestivalLeiChongRechargeWGData:GetLeiChongReceiveState(index)
	if self.LeiChongData ~= nil and self.LeiChongData.dangwei_info ~= nil and self.LeiChongData.dangwei_info[index] ~= nil then
		return self.LeiChongData.dangwei_info[index]
	else
		return 0
	end
end

--当前充值仙玉
function FestivalLeiChongRechargeWGData:GetOwnXianYu()
	if self.LeiChongData ~= nil then
		return self.LeiChongData.cur_xianyu
	end
	return 0
end

--活动开启时服务器开服天数
function FestivalLeiChongRechargeWGData:GetOpenServerdayByProtocol()
	local open_day = OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.FESTIVAL_ACT_OA_LIMIT_RECHARGE)
	return open_day
end

function FestivalLeiChongRechargeWGData:GetLeiChongRewardCfg()
	local grade = self:GetGradeByServerDay()
	local reward_cfg = {}
	for i,v in ipairs(self.leichong_reward_cfg) do
		if grade == v.grade then
			table.insert(reward_cfg, v)
		end
	end
	return reward_cfg
end


--获取当前档需要充值的仙玉
function FestivalLeiChongRechargeWGData:GetNeedRechargeXianYU()
	local cur_xianyu = self:GetOwnXianYu()
	local cfg = self:GetLeiChongRewardCfg()
	for i,v in ipairs(cfg) do
		if cur_xianyu < v.stage_value then
			local pre_stage_value = 0
			if i > 1 then
				pre_stage_value = cfg[i-1].stage_value
			end
			return v.stage_value - cur_xianyu, v.stage_value, pre_stage_value
		end
	end
	return 0, cfg[#cfg].stage_value, cfg[#cfg].stage_value
end

--是否已达成
function FestivalLeiChongRechargeWGData:IsRechargeTargetFinish()
	local cur_xianyu = self:GetOwnXianYu()
	local cfg = self:GetLeiChongRewardCfg()
	return cur_xianyu >= cfg[#cfg].stage_value
end

--根据服务器开服天数获得活动配置grade
function FestivalLeiChongRechargeWGData:GetGradeByServerDay()
	local open_day = self:GetOpenServerdayByProtocol()
    local open_time = FestivalActivityWGData.Instance:GetOpenTimeByAct(ACTIVITY_TYPE.FESTIVAL_ACT_OA_LIMIT_RECHARGE)
    local week = TimeUtil.FormatSecond3MYHM1(open_time)--获取是周几   
	for k,v in pairs(self.leichong_param_cfg) do
		if open_day >= v.start_server_day and open_day < v.end_server_day then
			return v.grade
		end
	end
	return 0
end

--活动结束时间
function FestivalLeiChongRechargeWGData:GetActEndTime()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.FESTIVAL_ACT_OA_LIMIT_RECHARGE)
	if activity_info and activity_info.end_time then
		return activity_info.end_time
	end
	return -1
end

function FestivalLeiChongRechargeWGData:IsShowLCRechargeRedPoint()
	local state = FestivalActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.FESTIVAL_ACT_OA_LIMIT_RECHARGE)
	if not state then
		return 0
	end

	if self.LeiChongData ~= nil and self.LeiChongData.dangwei_info ~= nil then
		for k,v in ipairs(self.LeiChongData.dangwei_info) do
			if v == TianShenRoadRewardState.KLQ then
				return 1
			end
		end
	end
	return 0
end

function FestivalLeiChongRechargeWGData:GetActCanOpen()
	-- if nil == self.LeiChongData then
 --        return false
 --    end

	local grade = self:GetGradeByServerDay()
	local cfg = self.leichong_param_cfg[grade]

	if nil == cfg then
		return false
	end

	local vo = GameVoManager.Instance:GetMainRoleVo()
	if vo.level >= cfg.open_role_level then
		return true
	end

	return false
end

--获取模型信息
function FestivalLeiChongRechargeWGData:GetModelInfo()
	local grade = self:GetGradeByServerDay()
	return self.model_info[grade]
end


