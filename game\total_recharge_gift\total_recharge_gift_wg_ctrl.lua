require("game/total_recharge_gift/total_recharge_gift_wg_data")
require("game/total_recharge_gift/total_recharge_gift_view")

TotalRechargeGiftWGCtrl = TotalRechargeGiftWGCtrl or BaseClass(BaseWGCtrl)
function TotalRechargeGiftWGCtrl:__init()
    if TotalRechargeGiftWGCtrl.Instance then
		error("[TotalRechargeGiftWGCtrl]:Attempt to create singleton twice!")
	end

    TotalRechargeGiftWGCtrl.Instance = self
    self.data = TotalRechargeGiftWGData.New()

    self.view = TotalRechargeGiftView.New(GuideModuleName.TotalRechargeGiftView)
    self:RegisterAllProtocols()
end

function TotalRechargeGiftWGCtrl:__delete()
    TotalRechargeGiftWGCtrl.Instance = nil

    self.data:DeleteMe()
	self.data = nil

    self.view:DeleteMe()
	self.view = nil

    -- if self.act_change then
    --     ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
    --     self.act_change = nil
    -- end

    -- if self.role_data_change then
	-- 	RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
	-- 	self.role_data_change = nil
	-- end
end

function TotalRechargeGiftWGCtrl:OpenTotalRechargeGiftView()
    self.view:Open()
end

function TotalRechargeGiftWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOAHaoLiWanZhang2Info, "OnSCOAHaoLiWanZhang2Info")

    -- self.act_change = BindTool.Bind(self.OnActivityChange, self)
    -- ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)

    -- self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
    -- RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
end

function TotalRechargeGiftWGCtrl:OnSCOAHaoLiWanZhang2Info(protocol)
    self.data:SetAllVientianeInfo(protocol)
    -- MainuiWGCtrl.Instance:FlushView(0, "haoli_wanzhang_tip")
    self:FlushView()
    RemindManager.Instance:Fire(RemindName.TotalRechargeGift)
end

function TotalRechargeGiftWGCtrl:FlushView()
    ViewManager.Instance:FlushView(GuideModuleName.TotalRechargeGiftView)
end

-- function TotalRechargeGiftWGCtrl:OnActivityChange(activity_type, status, next_time, open_type)
--     if activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TOTAL_RECHARGE_GIFT and (status == ACTIVITY_STATUS.OPEN or status == ACTIVITY_STATUS.CLOSE) then
--         MainuiWGCtrl.Instance:FlushView(0, "haoli_wanzhang_tip")
--     end
-- end

-- function TotalRechargeGiftWGCtrl:OnRoleAttrChange(attr_name, value, old_value)
--     if attr_name == "level" then
--         local show_info = self.data:GetOtherCfg()
--         local tianyin_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TOTAL_RECHARGE_GIFT)
--         if (old_value < show_info.open_level and value >= show_info.open_level) and tianyin_is_open then
--             MainuiWGCtrl.Instance:FlushView(0, "haoli_wanzhang_tip")
--         end
--     end
-- end