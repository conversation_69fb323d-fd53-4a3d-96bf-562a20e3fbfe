HiddenWeaponEquipSelectView = HiddenWeaponEquipSelectView or BaseClass(SafeBaseView)

function HiddenWeaponEquipSelectView:__init()
    self:SetMaskBg(true, true)
    self.view_layer = UiLayer.Pop
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",{sizeDelta = Vector2(782, 500)})
    self:AddViewResource(0, "uis/view/shenji_anqiruanjia_ui_prefab", "layout_shenji_equip_select")
end

function HiddenWeaponEquipSelectView:__delete()
end

function HiddenWeaponEquipSelectView:ReleaseCallBack()
    if self.equip_list then
        self.equip_list:DeleteMe()
        self.equip_list = nil
    end
    self.callback_confirm = nil
    self.target = nil
    self.selected = nil
end

function HiddenWeaponEquipSelectView:CloseCallBack()
    self.callback_confirm = nil
end

function HiddenWeaponEquipSelectView:SetConfirmCallback(call_back, target, selected)
    self.callback_confirm = call_back
    self.target = target
    self.selected = selected
end

function HiddenWeaponEquipSelectView:OnConfirm()
    if self.callback_confirm then
        self.callback_confirm(self.curr_select_index)
    end
    self:Close()
end

function HiddenWeaponEquipSelectView:LoadCallBack(index, loaded_times)
    -- self.equip_list = PerfectLoversListView.New()
    -- self.equip_list:Create(HwEquipSelectCell, self.node_list["xq_pet_list"])
    self:SetViewName(Language.ShenJiEquip.SelectEquipName)
    self.equip_list = AsyncListView.New(HWBuildSelectItemRender, self.node_list["xq_pet_list"])
    self.equip_list:SetSelectCallBack(BindTool.Bind1(self.OnItemClick, self))
    XUI.AddClickEventListener(self.node_list.btn_sure, BindTool.Bind(self.OnConfirm, self))
end

function HiddenWeaponEquipSelectView:OnItemClick(item, cell_index, is_default)
    self.curr_select_index = item.data.index
    --item.data.is_select = not item.data.is_select
    --item.node_list.img_highlight:SetActive(item.data.is_select)
end

function HiddenWeaponEquipSelectView:ShowIndexCallBack(index)
    self:Flush()
end

function HiddenWeaponEquipSelectView:OnFlush(param_t, index)
    local target = self.target or {}
    self.curr_select_index = nil
    -- from: HWMaterialRender:SetTarget
    local data_list =
        HiddenWeaponWGData.Instance:GetDataListByCondition(
        target.big_type,
        target.item_index,
        target.item_id,
        -- nil,
        target.color,
        target.star,
        target.node_type
    )
    -- 珍稀的放后面
    -- local func_sort = function(a, b)
    --     if a.equip == nil then
    --         return false
    --     end
    --     if b.equip == nil then
    --         return false
    --     end
    --     return a.equip.special_flag < b.equip.special_flag
    -- end
    -- table.sort(data_list, func_sort)
    local map_selected = {}
    for index, value in ipairs(self.selected) do
        map_selected[value] = value
    end
    local result_data = {}
    for index, value in ipairs(data_list) do
        if map_selected[value.index] == nil then
            table.insert(result_data, value)
        end
    end
    self.equip_list:SetDataList(result_data or {})
    local length = #(result_data or {})
    if length > 0 then
        self.node_list["group_content"]:SetActive(true)
        self.node_list["bg_kongzhi"]:SetActive(false)
        self.node_list["tips_txt"]:SetActive(true)
        self.node_list["sure_text"].text.text = Language.ShenJiEquip.CONFIRM_SELECT
        self.equip_list:SelectIndex(1)
    else
        self.node_list["group_content"]:SetActive(false)
        self.node_list["bg_kongzhi"]:SetActive(true)
        self.node_list["tips_txt"]:SetActive(false)
        self.node_list["sure_text"].text.text = Language.ShenJiEquip.CLOSE
    end
end

------------------------

-- 暗器详情-背包列表
HWBuildSelectItemRender = HWBuildSelectItemRender or BaseClass(BaseRender)

function HWBuildSelectItemRender:__init()
end

function HWBuildSelectItemRender:SetIsShowTips(flag)
    self.is_showtip = flag
end

function HWBuildSelectItemRender:LoadCallBack(instance)
    if not self.item_cell_equip then
        self.item_cell_equip = ItemCell.New(self.node_list["item_pos"])
        self.item_cell_equip:SetUseButton(false)
        self.item_cell_equip:SetData(nil)
    end

    self.node_list.img_highlight:SetActive(false)
end

function HWBuildSelectItemRender:OnSelectChange(is_select)
    -- 高亮
    if nil == is_select or not next(self.data) then
        self.node_list.img_highlight:SetActive(false)
        return
    end
    self.node_list.img_highlight:SetActive(is_select)
end

function HWBuildSelectItemRender:OnFlush()
    --self.node_list.img_highlight:SetActive(self.data.is_select or false)
    if self.item_cell_equip == nil then
        return
    end
    self.item_cell_equip:SetData(self.data)
end

function HWBuildSelectItemRender:__delete()
    if self.item_cell_equip ~= nil then
        self.item_cell_equip:DeleteMe()
    end
    self.item_cell = nil
end
