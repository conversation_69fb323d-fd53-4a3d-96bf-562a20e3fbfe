using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

public class NewMotionBlurRenderPassFeature : ScriptableRendererFeature
{
    #region singleton
    private static NewMotionBlurRenderPassFeature m_Instance;
    public static NewMotionBlurRenderPassFeature Instance
    {
        get
        {
            return m_Instance;
        }
    }

    private NewMotionBlurRenderPassFeature() { }
    #endregion

    public Shader shader;
    public RenderPassEvent renderPassEvent = RenderPassEvent.AfterRenderingOpaques;
    NewMotionBlurRenderPass m_ScriptablePass;
    Material m_mterial = null;


    public override void Create()
    {
        m_Instance = this;
        m_ScriptablePass = new NewMotionBlurRenderPass();
        m_ScriptablePass.renderPassEvent = renderPassEvent;
        if (shader && m_mterial == null)
        {
            m_mterial = CoreUtils.CreateEngineMaterial(shader);
        }
    }


    public override void AddRenderPasses(ScriptableRenderer renderer, ref RenderingData renderingData)
    {
        if (m_mterial == null)
        {
            Debug.LogWarning("NewMotionBlurRenderPassFeature: Material could not be created. Please assign a valid shader.");
            return;
        }

        if (renderingData.cameraData.renderType != CameraRenderType.Base)
            return;

        var cameraColorTarget = renderer.cameraColorTarget;
        m_ScriptablePass.Setup(cameraColorTarget, m_mterial);
        renderer.EnqueuePass(m_ScriptablePass);
    }
}









public class NewMotionBlurRenderPass : ScriptableRenderPass
{
    const string CommandBufferTag = "NewMotionBlurRenderPass Pass";
    public Material m_material;
    NewMotionBlur m_newMotionBlur;

    RenderTargetIdentifier m_ColorAttachment;
    RenderTargetHandle m_TemporaryColorTexture01;
    RenderTargetHandle m_TemporaryColorTexture02;

    public NewMotionBlurRenderPass()
    {
        m_TemporaryColorTexture01.Init("_TemporaryColorTexture1");
        m_TemporaryColorTexture02.Init("_TemporaryColorTexture2");

    }
    public void Setup(RenderTargetIdentifier _ColorAttachment, Material material)
    {
        this.m_ColorAttachment = _ColorAttachment;
        m_material = material;
    }

    public override void Execute(ScriptableRenderContext context, ref RenderingData renderingData)
    {
        if (m_newMotionBlur == null)
        {
            var stack = VolumeManager.instance.stack;
            m_newMotionBlur = stack.GetComponent<NewMotionBlur>();
            if (m_newMotionBlur == null)
            {
                Debug.LogWarning("NewMotionBlurRenderPass: NewMotionBlur component not found.");
                return;
            }
        }

        if (!m_newMotionBlur.IsActive() || renderingData.cameraData.isSceneViewCamera)
            return;

        var cmd = CommandBufferPool.Get(CommandBufferTag);
        RenderTextureDescriptor opaqueDesc = renderingData.cameraData.cameraTargetDescriptor;
        opaqueDesc.depthBufferBits = 0;
        cmd.GetTemporaryRT(m_TemporaryColorTexture01.id, opaqueDesc);
        cmd.Blit(m_ColorAttachment, m_TemporaryColorTexture01.Identifier());
        m_material.SetFloat("_fSampleDist", m_newMotionBlur.SampleDist.value);
        m_material.SetFloat("_fSampleStrength", m_newMotionBlur.SampleStrength.value);
        cmd.Blit(m_TemporaryColorTexture01.Identifier(), m_ColorAttachment, m_material);

        cmd.ReleaseTemporaryRT(m_TemporaryColorTexture01.id);
        cmd.ReleaseTemporaryRT(m_TemporaryColorTexture02.id);
        context.ExecuteCommandBuffer(cmd);
        CommandBufferPool.Release(cmd);
    }
}


