﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[AddComponentMenu("UGUI/Tween/UGUI Tween Alpha")]
[RequireComponent(typeof(CanvasGroup))]
public class UGUITweenAlpha : UGUITweener 
{
	[Range(0f, 1f)] public float from = 1f;
	[Range(0f, 1f)] public float to = 1f;

	public bool raycast_on_play = true;
	public bool interactable_on_play = true;

	bool mCached = false;
	CanvasGroup canvas_group;

	[System.Obsolete("Use 'value' instead")]
	public float alpha { get { return this.value; } set { this.value = value; } }

	bool raycast_old = true;
	bool interactable_old = true;

	void Awake()
	{
		if (!mCached) Cache();
	}

	void Cache ()
	{
		mCached = true;
		canvas_group = GetComponent<CanvasGroup>();
		if (canvas_group != null) 
		{
			bool raycast_old = canvas_group.blocksRaycasts;
			bool interactable_old = canvas_group.interactable;
		}
		SetOnStarted (new EventDelegate (() => {
			if (!mCached) Cache();
			if(canvas_group != null)
			{
				canvas_group.blocksRaycasts = raycast_on_play;
				canvas_group.interactable = interactable_on_play;
			}
		}));

		SetOnFinished (new EventDelegate (() => {
			if (!mCached) Cache();
			if(canvas_group != null)
			{
				canvas_group.blocksRaycasts = raycast_old;
				canvas_group.interactable = interactable_old;
			}
		}));
	}

	public float value
	{
		get
		{
			if (!mCached) Cache();
			return canvas_group != null ? canvas_group.alpha : 1f;
		}
		set
		{
			if (!mCached) Cache();
			if (canvas_group != null) 
			{
				canvas_group.alpha = value;
			}
		}
	}

	protected override void OnUpdate (float factor, bool isFinished) { value = Mathf.Lerp(from, to, factor); }

	static public UGUITweenAlpha Begin (GameObject go, float duration, float start,float alpha, float delay = 0f)
	{
		UGUITweenAlpha comp = UGUITweener.Begin<UGUITweenAlpha>(go, duration, delay);
		comp.from = start;
		comp.to = alpha;
		comp.mCached = false;

		if(!comp.mCached) 
			comp.Cache();
		comp.ExecuteStartEvent ();

		if (duration <= 0f)
		{
			comp.Sample(1f, true);
			comp.enabled = false;
		}
		return comp;
	}

	public override void SetStartToCurrentValue () { from = value; }
	public override void SetEndToCurrentValue () { to = value; }
}
