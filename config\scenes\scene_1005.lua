return {
    id = 1005,
    name = "登神派",
    scene_type = 0,
    bundle_name = "scenes/map/a3_bs_wanyaoxianguo_main",
    asset_name = "A3_BS_WanYaoXianGuo_Main",
    width = 317,
    height = 346,
    origin_x = 23,
    origin_y = 9,
    levellimit = 150,
    is_forbid_pk = 0,
    skip_loading = 0,
    show_weather = 0,
    scene_broadcast = 0,
    scenex = 240,
    sceney = 282,
    npcs = {
		{id=10501, x=134, y=122, rotation_y = 280, is_walking = 0, paths = {}},
		{id=10502, x=185, y=232, rotation_y = 250, is_walking = 0, paths = {}},
		{id=10503, x=176, y=244, rotation_y = 190, is_walking = 0, paths = {}},
		{id=10504, x=241, y=286, rotation_y = 180, is_walking = 0, paths = {}},
		{id=10505, x=46, y=72, rotation_y = 90, is_walking = 0, paths = {}},
		{id=10506, x=210, y=200, rotation_y = 260, is_walking = 0, paths = {}},
		{id=10507, x=262, y=191, rotation_y = 300, is_walking = 0, paths = {}},
		{id=10508, x=246, y=122, rotation_y = 310, is_walking = 0, paths = {}},
		{id=10509, x=222, y=107, rotation_y = 0, is_walking = 0, paths = {}},
		{id=10510, x=48, y=70, rotation_y = 20, is_walking = 0, paths = {}},
		{id=10511, x=176, y=19, rotation_y = 340, is_walking = 0, paths = {}},
		{id=10512, x=20, y=85, rotation_y = 79.99999, is_walking = 0, paths = {}},
		{id=10513, x=19, y=146, rotation_y = 138.09, is_walking = 0, paths = {}},
		{id=10514, x=40, y=179, rotation_y = 210, is_walking = 0, paths = {}},
		{id=10515, x=12, y=52, rotation_y = 128.68, is_walking = 0, paths = {}},
		{id=10516, x=31, y=183, rotation_y = 180, is_walking = 0, paths = {}},
		{id=10517, x=62, y=225, rotation_y = 250, is_walking = 0, paths = {}},
		{id=10518, x=143, y=232, rotation_y = 230, is_walking = 0, paths = {}},
		{id=10519, x=167, y=264, rotation_y = 230, is_walking = 0, paths = {}},
		{id=10520, x=155, y=216, rotation_y = 0, is_walking = 0, paths = {}},
		{id=10521, x=160, y=226, rotation_y = 250, is_walking = 0, paths = {}},
		{id=10522, x=133, y=151, rotation_y = 50, is_walking = 0, paths = {}},
		{id=10523, x=179, y=201, rotation_y = 0, is_walking = 0, paths = {}},
		{id=10524, x=199, y=81, rotation_y = 280, is_walking = 0, paths = {}},
		{id=10525, x=245, y=282, rotation_y = 0, is_walking = 0, paths = {}},
		{id=10526, x=257, y=141, rotation_y = 220, is_walking = 0, paths = {}},
		{id=10527, x=89, y=101, rotation_y = 200, is_walking = 0, paths = {}},
		{id=10528, x=44, y=108, rotation_y = 250, is_walking = 0, paths = {}},
    },
    monsters = {
		{id=10501, x=204, y=152},
		{id=10501, x=206, y=149},
		{id=10501, x=201, y=143},
		{id=10501, x=208, y=152},
		{id=10501, x=207, y=146},
		{id=10501, x=201, y=147},
		{id=10502, x=196, y=100},
		{id=10502, x=209, y=99},
		{id=10502, x=203, y=102},
		{id=10502, x=192, y=96},
		{id=10502, x=202, y=98},
		{id=10502, x=198, y=95},
		{id=10502, x=199, y=100},
		{id=10502, x=196, y=97},
		{id=10503, x=97, y=77},
		{id=10503, x=94, y=80},
		{id=10503, x=93, y=82},
		{id=10503, x=101, y=74},
		{id=10503, x=103, y=78},
		{id=10503, x=99, y=80},
		{id=10504, x=22, y=111},
		{id=10504, x=21, y=115},
		{id=10504, x=28, y=113},
		{id=10504, x=26, y=115},
		{id=10504, x=21, y=108},
		{id=10504, x=28, y=119},
		{id=10504, x=33, y=111},
		{id=10504, x=21, y=119},
		{id=10505, x=61, y=242},
		{id=10505, x=62, y=247},
		{id=10505, x=67, y=249},
		{id=10505, x=66, y=241},
		{id=10505, x=68, y=251},
		{id=10505, x=68, y=244},
		{id=10505, x=72, y=247},
		{id=10508, x=122, y=330},
		{id=10508, x=127, y=331},
		{id=10508, x=124, y=332},
		{id=10508, x=131, y=333},
		{id=10508, x=121, y=327},
		{id=10508, x=120, y=334},
		{id=10508, x=127, y=335},
		{id=10509, x=166, y=65},
		{id=10509, x=169, y=63},
		{id=10509, x=167, y=62},
		{id=10509, x=172, y=64},
		{id=10509, x=170, y=66},
		{id=10509, x=171, y=59},
		{id=10509, x=171, y=62},
		{id=10509, x=165, y=61},
    },
    doors = {
		{id=1008, type=0, level=0, target_scene_id=1003, target_door_id=1211, offset={0, 0, 0}, rotation={0, 0, 0}, x=15, y=44, door_target_x=173, door_target_y=405},
		{id=1009, type=0, level=0, target_scene_id=1004, target_door_id=1006, offset={0, 0, 0}, rotation={0, 0, 0}, x=267, y=207, door_target_x=333, door_target_y=291},
    },
    gathers = {
		{id=1501, x=229, y=297, disappear_after_gather=0},
		{id=1502, x=265, y=288, disappear_after_gather=0},
		{id=1503, x=257, y=276, disappear_after_gather=0},
		{id=1504, x=275, y=199, disappear_after_gather=0},
		{id=1505, x=83, y=98, disappear_after_gather=0},
		{id=1804, x=23, y=52, disappear_after_gather=0},
		{id=1805, x=144, y=251, disappear_after_gather=0},
		{id=1511, x=39, y=177, disappear_after_gather=0},
		{id=1512, x=164, y=268, disappear_after_gather=0},
		{id=1508, x=160, y=213, disappear_after_gather=0},
		{id=1509, x=163, y=224, disappear_after_gather=0},
		{id=1510, x=118, y=155, disappear_after_gather=0},
    },
    jumppoints = {
    },
    fences = {
    },
    effects = {
    },
    sounds = {
    },
    scene_way_points = {
	},
    mask = "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",
}