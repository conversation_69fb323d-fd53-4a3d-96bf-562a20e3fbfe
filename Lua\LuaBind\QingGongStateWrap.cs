﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class QingGongStateWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>gin<PERSON>num(typeof(QingGongState));
		<PERSON><PERSON>("None", get_None, null);
		<PERSON><PERSON>("Up", get_Up, null);
		<PERSON><PERSON>("Down", get_Down, null);
		<PERSON><PERSON>("ReadyToGround", get_ReadyToGround, null);
		<PERSON><PERSON>("Mitsurugi", get_Mitsurugi, null);
		<PERSON><PERSON>("OnGround", get_OnGround, null);
		<PERSON><PERSON>("Stop", get_Stop, null);
		<PERSON><PERSON>("IntToEnum", IntToEnum);
		<PERSON><PERSON>();
		TypeTraits<QingGongState>.Check = CheckType;
		StackTraits<QingGongState>.Push = Push;
	}

	static void Push(IntPtr L, QingGongState arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(QingGongState), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_None(IntPtr L)
	{
		ToLua.Push(L, QingGongState.None);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Up(IntPtr L)
	{
		ToLua.Push(L, QingGongState.Up);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Down(IntPtr L)
	{
		ToLua.Push(L, QingGongState.Down);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ReadyToGround(IntPtr L)
	{
		ToLua.Push(L, QingGongState.ReadyToGround);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Mitsurugi(IntPtr L)
	{
		ToLua.Push(L, QingGongState.Mitsurugi);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OnGround(IntPtr L)
	{
		ToLua.Push(L, QingGongState.OnGround);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Stop(IntPtr L)
	{
		ToLua.Push(L, QingGongState.Stop);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		QingGongState o = (QingGongState)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

