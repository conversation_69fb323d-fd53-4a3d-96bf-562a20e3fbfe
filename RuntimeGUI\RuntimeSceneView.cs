﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class RuntimeSceneView : RuntimeBaseView
{
    private Rect windowRect;
    private string sceneName = "F1_BS_BaoFengTa_Main";
    private string bundleName = "";
    private string asssetName = "";
    private bool isShowScene;
    private bool isInited = false;

    public RuntimeSceneView() : base(RuntimeViewName.SCENE_EDIT)
    {

    }

    override protected void OpenCallback()
    {
        if (!isInited)
        {
            isInited = true;
            RuntimeGUIResMgr.Instance.Startup();
        }
    }

    override protected void OnReapintWindow(int windowid)
    {
        GUILayout.BeginHorizontal();
        sceneName = GUILayout.TextField(sceneName);
        if (GUILayout.Button("进入场景"))
        {
            this.Close();
            RuntimeGUIResMgr.Instance.LoadScene(sceneName);
        }
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        bundleName = GUILayout.TextField(bundleName);
        asssetName = GUILayout.TextField(asssetName);
        if (GUILayout.Button("加载对象"))
        {
            GameObject gameObj =  GameObject.Instantiate<GameObject>(RuntimeGUIResMgr.Instance.LoadPrefab(bundleName, asssetName));
            EffectOrderGroup.RefreshRenderOrder(gameObj);
            GameObject.Instantiate<GameObject>(RuntimeGUIResMgr.Instance.LoadPrefab(bundleName, asssetName));
        }
        GUILayout.EndHorizontal();
    }
 }
