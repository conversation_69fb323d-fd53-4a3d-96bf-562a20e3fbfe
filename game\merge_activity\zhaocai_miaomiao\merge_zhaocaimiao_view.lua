MergeActivityView = MergeActivityView or BaseClass(SafeBaseView)

local SHOW_EFFECT_LEVEL = 1.5
local TURN_CIRCLE = 2

function MergeActivityView:LoadIndexCallBackZhaoCaiMiao()
 	XUI.AddClickEventListener(self.node_list.miaomaiao_record_btn,BindTool.Bind1(self.OpenMiaoMiaoRecordPanel, self))
    XUI.AddClickEventListener(self.node_list.miaomaiao_draw_btn,BindTool.Bind1(self.GetCountMoney, self))
    XUI.AddClickEventListener(self.node_list.miaomaiao_get_btn,BindTool.Bind1(self.MiaomiaoGetBtnClick, self))
    XUI.AddClickEventListener(self.node_list.miaomaiao_reward_btn,BindTool.Bind1(self.GetDailyReward, self))
    self.node_list.atm_jump_toggle.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickMiaoJumpToggle, self))

 	self.miaomiao_task_list = AsyncListView.New(MergeMiaoMiaoTaskRender, self.node_list.miaomiao_task_list)
 	MergeZhaoCaiMiaoWGCtrl.Instance:MiaoMiaoInfoReq()
    self:InitMiaoMiaoPictureAndDescText()
end

function MergeActivityView:OnClickMiaoJumpToggle(is_on)
    MergeZhaoCaiMiaoWGData.Instance:SetMiaoMiaoToggleIsJump(is_on)
end

function MergeActivityView:MiaomiaoGetBtnClick()
    MergeZhaoCaiMiaoWGCtrl.Instance:MiaoMiaoOperateSeq(CSA_ZHAOCAIMAO_OP_TYPE.CSA_ZHAOCAIMAO_OP_FETCH_CHOUJIANG)
end

function MergeActivityView:FlushZhaoCaiMiao()
 	if nil == self.node_list.miaomaiao_reward_btn then
 		return
 	end
 	local data_list_cfg = MergeZhaoCaiMiaoWGData.Instance:GetMiaoMiaoTaskList()
 	local param_cfg = MergeZhaoCaiMiaoWGData.Instance:GetOtherParam()
 	table.sort(data_list_cfg, SortTools.KeyLowerSorter("grade"))
 	local data_list = {}
 	for k,v in pairs(data_list_cfg) do
 		local data = {}
 		data.cycle = v.cycle
 		data.grade = v.grade
 		data.reward_time = v.reward_time
 		data.virtual_item = v.virtual_item
 		data.description = v.description
 		data.cur_state = MergeZhaoCaiMiaoWGData.Instance:GetTaskRechargeState(k)
 		if data.cur_state == 0 then  --未充值
 			data.sort_index = 1
 		elseif data.cur_state == 1 then --充值可领取
 			data.sort_index = 0
 		else                            --已领取
 			data.sort_index = 2
 		end
 		table.insert(data_list, data)
    end

 	table.sort(data_list, SortTools.KeyLowerSorter("sort_index", "grade"))
 	self.miaomiao_task_list:SetDataList(data_list)
 	local all_count, cur_count, login_reward_state = MergeZhaoCaiMiaoWGData.Instance:GetMiaoMiaoActInfo()
     local all_money = MergeZhaoCaiMiaoWGData.Instance:GetMiaoMiaoMoneyCount()

 	self.node_list.left_miao_reward_btn:SetActive(login_reward_state == 0)
 	local count_str = cur_count > 0 and string.format(Language.MergeActivity.MiaoMiaoTaskDesc, cur_count, 1) or string.format(Language.MergeActivity.MiaoMiaoTaskDesc_1, 0, 1)
 	self.node_list.count_show_text.text.text = count_str
 	self.node_list.miaomiao_reward_remind_point:SetActive(cur_count > 0)
	self.node_list.miaomaiao_reward_btn.animator:SetBool("is_shake", login_reward_state == 0)
	local allcount_act = #data_list
	local have_count = allcount_act - all_count + cur_count
	local interface_cfg = MergeZhaoCaiMiaoWGData.Instance:GetPanelUiInterface()
	if not interface_cfg then
		return 
	end
	-- local b, a = ResPath.GetMergeMiaoMiaoImg("zcm_xcy")
	-- if have_count > 0 then
	-- 	b, a = ResPath.GetMergeMiaoMiaoImg("zcm_xcy")
	-- else
	-- 	b, a = ResPath.GetMergeMiaoMiaoImg("zcm_xcy2")
	-- end
	-- self.node_list.mioa_paopao_righr_title_image.image:LoadSprite(b, a, function()
	-- 			XUI.ImageSetNativeSize(self.node_list.mioa_paopao_righr_title_image)
	-- 		end)--暂时屏蔽
--	end, 0, "miaomiao_ani_login_reward_state")

end

function MergeActivityView:TotalHaveShowMiaoMiao()
 	local param_cfg = MergeZhaoCaiMiaoWGData.Instance:GetOtherParam()
    local all_money = MergeZhaoCaiMiaoWGData.Instance:GetMiaoMiaoMoneyCount()
    local gold = MergeZhaoCaiMiaoWGData.Instance:GetDrawXianYu()
    all_money = all_money - gold
    if self.node_list.money_all_text and param_cfg then
	    self.node_list.money_all_text.text.text = param_cfg.all_reward - all_money
	end
end

function MergeActivityView:ShowIndexCallZhaoCaiMiao()
 	local other_cfg = MergeZhaoCaiMiaoWGData.Instance:GetPanelUiInterface()

	if other_cfg then
		self:SetOutsideRuleTips(other_cfg.tips_outside)
		self:SetRuleInfo(other_cfg.tips_inside, other_cfg.tips_name)
	end
	self:TotalHaveShowMiaoMiao()
	self:SetActRemainTime(TabIndex.merge_activity_2112)
    local is_jump = MergeZhaoCaiMiaoWGData.Instance:GetMiaoMiaoToggleIsJump()
    self.node_list.atm_jump_toggle.toggle.isOn = is_jump
    local draw_num = MergeZhaoCaiMiaoWGData.Instance:GetDrawXianYu()
    if draw_num == 0 then
        self:InitMiaoNum()
    else
        self:MiaoNumEndAniState()
    end
end

function MergeActivityView:ZhaoCaiMiaoMiaoHotUpdata()
    self:InitMiaoMiaoPictureAndDescText()
 	self:FlushZhaoCaiMiao()
 	self:TotalHaveShowMiaoMiao()
end

function MergeActivityView:OpenMiaoMiaoRecordPanel()
 	MergeZhaoCaiMiaoWGCtrl.Instance:MiaoMiaoOperateSeq(CSA_ZHAOCAIMAO_OP_TYPE.CSA_ZHAOCAIMAO_OP_RECORD)
 	MergeZhaoCaiMiaoWGCtrl.Instance:OpenMiaoMiaoRecordPanel()
end

function MergeActivityView:GetCountMoney()
 	local all_count, cur_count, login_reward_state = MergeZhaoCaiMiaoWGData.Instance:GetMiaoMiaoActInfo()
 	local param_cfg = MergeZhaoCaiMiaoWGData.Instance:GetOtherParam()
 	local all_money = MergeZhaoCaiMiaoWGData.Instance:GetMiaoMiaoMoneyCount()
 	if param_cfg.all_reward == all_money then
 		SysMsgWGCtrl.Instance:ErrorRemind(Language.MergeActivity.MiaoMiaoCountTips_1)
 		return
 	elseif cur_count <= 0 then
 		SysMsgWGCtrl.Instance:ErrorRemind(Language.MergeActivity.MiaoMiaoCountTips)
 		return
    end
    if self.is_play_miao_num_ani then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.MergeActivity.IsPlayAni)
        return
    end
 	MergeZhaoCaiMiaoWGCtrl.Instance:MiaoMiaoOperateSeq(CSA_ZHAOCAIMAO_OP_TYPE.CSA_ZHAOCAIMAO_OP_CHOUJIANG)
end

function MergeActivityView:GetDailyReward()
 	MergeZhaoCaiMiaoWGCtrl.Instance:MiaoMiaoOperateSeq(CSA_ZHAOCAIMAO_OP_TYPE.CSA_ZHAOCAIMAO_OP_FETCH_LOGIN_REWARD)
end

function MergeActivityView:InitMiaoMiaoPictureAndDescText()  --
	-- local interface_cfg = MergeZhaoCaiMiaoWGData.Instance:GetPanelUiInterface()
	-- if not interface_cfg then
	-- 	return
    -- end
    local data_list_cfg = MergeZhaoCaiMiaoWGData.Instance:GetMiaoMiaoTaskList()
    local virtual_item = data_list_cfg[1] and data_list_cfg[1].virtual_item or 0
    local item_cfg = ItemWGData.Instance:GetItemConfig(virtual_item)
end

function MergeActivityView:PlayEffectByZhaoCaiMiaoMiao()
	local index = self:GetShowIndex()
	if self.node_list.effect_pos_miaomiao and index == TabIndex.merge_activity_2112 then
		local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_xianyu_zhakai)
		local bundle, asset = ResPath.UiseRes("effect_xianyuhuode")
        AudioManager.PlayAndForget(bundle, asset)
        self.node_list.miaomaiao_get_btn:SetActive(false)
        self.node_list.miaomaiao_draw_btn:SetActive(true)
		--EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list.qifu_effect_1.transform,1.6,nil,nil,nil,self.OnClickQifuReceive)
        EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list.effect_pos_miaomiao.transform,1.6,
        nil,nil,nil,BindTool.Bind(self.PlayEffectByZhaoCaiMiaoMiaoSecond,self) )
	end
end

function MergeActivityView:PlayEffectByZhaoCaiMiaoMiaoSecond()
    TipWGCtrl.Instance:DestroyFlyEffectByViewName("PlayEffectByZhaoCaiMiaoMiaoSecond")
	if self.money_bar then
		local end_obj = self.money_bar:GetSlicketNode(GameEnum.NEW_MONEY_BAR.XIANYU)
        if end_obj then	
            local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_xianyu_new)						
            TipWGCtrl.Instance:ShowFlyEffectManager("PlayEffectByZhaoCaiMiaoMiaoSecond", bundle_name,
            asset_name, self.node_list.effect_pos_miaomiao, end_obj, DG.Tweening.Ease.OutCubic, 0.5,
            function ()
                self:TotalHaveShowMiaoMiao()
                local data = ItemWGData.Instance:GetItemConfig(65534)
                local gold = MergeZhaoCaiMiaoWGData.Instance:GetOldDrawXianyu()
                    if self.money_bar then
                        self.money_bar:Flush()
                    end
                    if gold > 0 then
                        local str = string.format(Language.Bag.GetItemTxt, data.name, gold)
                        SysMsgWGCtrl.Instance:ErrorRemind(str)
                    end
                    self:PlayMiaoGoldAniEnd()
            end, nil, 20, 200)
		end
	end
end

function MergeActivityView:PlayMiaoGoldAniEnd()
    self.node_list.miaomaiao_get_btn:SetActive(false)
    self.node_list.miaomaiao_draw_btn:SetActive(false)
    self:InitMiaoNum()
end

function MergeActivityView:DeleteZhaoCaiMiao()
	if self.miaomiao_task_list then
		self.miaomiao_task_list:DeleteMe()
		self.miaomiao_task_list = nil
	end

    self.is_play_miao_num_ani = false

    if self.play_time_quest then
        GlobalTimerQuest:CancelQuest(self.play_time_quest)
        self.play_time_quest = nil 
    end

    for i = 1, 4 do
        if self["miaomiao_num_text_group_second_"..i] then
            self["miaomiao_num_text_group_second_"..i]:Kill()
			self["miaomiao_num_text_group_second_"..i] = nil
        end
		if self["miaomiao_num_text_group_"..i] then
			self["miaomiao_num_text_group_"..i]:Kill()
			self["miaomiao_num_text_group_"..i] = nil
			self["do_circle_count"..i] = nil
        end
	end
	self.should_next_num = nil
	self.should_next_speed_type = nil
end

--未播动画，初始状态
function MergeActivityView:InitMiaoNum()
	self.node_list.miaomaiao_get_btn:SetActive(false)
	local param_cfg = MergeZhaoCaiMiaoWGData.Instance:GetOtherParam()
	local all_count = param_cfg.all_reward 
	local show_count_num = math.floor(all_count / 1000) 
	local default_high = show_count_num * 100 + 50
    for i = 1, 4 do
        self.node_list["miao_wenhao"..i]:SetActive(true)
        self.node_list["miao_wenhao"..i].rect.anchoredPosition = Vector2(0, 34)
        self.node_list["miaomiao_num_text_group_"..i].rect.anchoredPosition = Vector3(0, 950)
        self.node_list["miaomiao_num_text_group_"..i]:SetActive(false)
        self.node_list["miaomiao_num_text_group_second_"..i].rect.anchoredPosition = Vector3(0, 1050)
        self.node_list["miaomiao_num_text_group_second_"..i]:SetActive(false)
 		self["do_circle_count"..i] = 1
    end
    self.node_list.miaomaiao_draw_btn:SetActive(true)
    self.node_list.miaomaiao_get_btn:SetActive(false)
end

function MergeActivityView:MiaoNumEndAniState()
    for num = 1, 4 do
        local param_cfg = MergeZhaoCaiMiaoWGData.Instance:GetOtherParam()
        local all_count = param_cfg.all_reward 
        local show_count_num = math.floor(all_count / 1000)
        local high_y = show_count_num * 100 + 50
        local xianyu_num = MergeZhaoCaiMiaoWGData.Instance:GetDrawXianYu()
        local end_pos_y = self:GetDetailNum(num, xianyu_num)
        local pos_y = 1050  
        self.node_list["miao_wenhao"..num]:SetActive(false)
        self.node_list["miaomiao_num_text_group_"..num]:SetActive(true)
        self.node_list["miaomiao_num_text_group_second_"..num]:SetActive(true)
        self.node_list["miaomiao_num_text_group_"..num].rect.localPosition = Vector2(0, end_pos_y)
        self.node_list["miaomiao_num_text_group_second_"..num].rect.anchoredPosition = Vector3(0, pos_y, 0)
    end
    self.is_play_miao_num_ani = false
    self.node_list.miaomaiao_draw_btn:SetActive(false)
    self.node_list.miaomaiao_get_btn:SetActive(true)
end

function MergeActivityView:PlayMiaoMiaoTween()
    if not self:IsOpen() or not self:IsLoadedIndex(TabIndex.merge_activity_2112) then
        return
    end
    local is_jump = MergeZhaoCaiMiaoWGData.Instance:GetMiaoMiaoToggleIsJump()
    if is_jump then
        self:MiaoNumEndAniState()
        return
    end
    local count = 1
    if self.play_time_quest then
        GlobalTimerQuest:CancelQuest(self.play_time_quest)
        self.play_time_quest = nil 
    end
    for i = 1, 4 do
        self.node_list["miao_wenhao"..i].rect.anchoredPosition = Vector2(0, 34)
        self.node_list["miao_wenhao"..i]:SetActive(true)
        self.node_list["miao_wenhao"..i].rect:DOAnchorPosY(-100, 0.25)
    end
    -- self.node_list["miao_wenhao"..count].rect:DOAnchorPosY(-100, 0.25)
    -- self.play_time_quest = GlobalTimerQuest:AddTimesTimer(function()
    --     self.node_list["miao_wenhao"..count].rect:DOAnchorPosY(-100, 0.25)
    --     self.is_play_miao_num_ani = true
    --     count = count + 1
    -- end, 0.25, 4)

	self.node_list.miaomaiao_get_btn:SetActive(false)
	for m = 0, 9 do
		self.node_list.miaomiao_num_text_group_4:FindObj("Text"..m):SetActive(false)
		self.node_list.miaomiao_num_text_group_second_4:FindObj("Text"..m):SetActive(false)
	end
	local param_cfg = MergeZhaoCaiMiaoWGData.Instance:GetOtherParam()
	local all_count = param_cfg.all_reward 
	local show_count_num = math.floor(all_count / 1000) 
	local default_high = show_count_num * 100 + 50
	for i = 1, 4 do
		if i == 4 then
			self.node_list["miaomiao_num_text_group_"..i].rect.anchoredPosition = Vector3(0, default_high)
			local default_high_1 = default_high + 100
			self.node_list["miaomiao_num_text_group_second_"..i].rect.anchoredPosition = Vector3(0, default_high_1)
		else
			self.node_list["miaomiao_num_text_group_"..i].rect.anchoredPosition = Vector3(0, 950)
			self.node_list["miaomiao_num_text_group_second_"..i].rect.anchoredPosition = Vector3(0, 1050)
		end
 		self["do_circle_count"..i] = 1
    end
     
    ReDelayCall(self, function ()
        self.is_play_miao_num_ani = true
        for i = 1, 4 do
            self.node_list["miao_wenhao"..i]:SetActive(false)
            self.node_list["miaomiao_num_text_group_"..i]:SetActive(true)
            self.node_list["miaomiao_num_text_group_second_"..i]:SetActive(true)
        end
		self.should_next_num = 0
		self.should_next_speed_type = 1
		self:DoNumChangeText()
	end, 0.25, "miaomiao_ani_num_ShowIndexCallBack")
end


function MergeActivityView:DoNumChangeText()
	local other_cfg = MergeZhaoCaiMiaoWGData.Instance:GetPanelUiInterface()
	SHOW_EFFECT_LEVEL = other_cfg.each_circle_time or SHOW_EFFECT_LEVEL
	TURN_CIRCLE = other_cfg.roll_circle or TURN_CIRCLE
	local param_cfg = MergeZhaoCaiMiaoWGData.Instance:GetOtherParam()
	local all_count = param_cfg.all_reward 
	local show_count_num = math.floor(all_count / 1000)
 	for i=1,4 do
		local default_high = show_count_num * 100 + 50
		if i == 4 then
			self.node_list["miaomiao_num_text_group_"..i].rect.anchoredPosition = Vector3(0, default_high)
			local default_high_1 = default_high + 100
			self.node_list["miaomiao_num_text_group_second_"..i].rect.anchoredPosition = Vector3(0, default_high_1)
		else
			self.node_list["miaomiao_num_text_group_"..i].rect.anchoredPosition = Vector3(0, 950)
			self.node_list["miaomiao_num_text_group_second_"..i].rect.anchoredPosition = Vector3(0, 1050)
		end

 		if i == 4 then
 			for m = 0,9 do
				self.node_list.miaomiao_num_text_group_4:FindObj("Text"..m):SetActive(m <= show_count_num)
				self.node_list.miaomiao_num_text_group_second_4:FindObj("Text"..m):SetActive(m <= show_count_num)
 			end
 		end
 		self["do_circle_count"..i] = 1
 		self:MiaoMiaoTweenType(i, 1)
 	end
end

function MergeActivityView:MiaoMiaoTweenType(num, is_add_circle)
	local param_cfg = MergeZhaoCaiMiaoWGData.Instance:GetOtherParam()
	local all_count = param_cfg.all_reward 
	local show_count_num = math.floor(all_count / 1000)
	local high_y = show_count_num * 100 + 50
	local is_next = false
	local param_cfg = MergeZhaoCaiMiaoWGData.Instance:GetOtherParam()
	local need_all_circle = TURN_CIRCLE
	if is_add_circle == 1 then
		if num == 4 then
			self.node_list["miaomiao_num_text_group_"..num].rect.anchoredPosition = Vector3(0, high_y, 0)
		else
			self.node_list["miaomiao_num_text_group_"..num].rect.anchoredPosition = Vector3(0, 950, 0) --layout_operation_activity_panel_1
		end
	else
		local pos_y = 1050
		if num == 4 then
			pos_y = high_y + 100
		end
		self.node_list["miaomiao_num_text_group_"..num].rect.anchoredPosition = Vector3(0, pos_y, 0)
	end
	if (is_add_circle == need_all_circle and num == 1) or (self.should_next_speed_type == 2 and self.should_next_num == num) then
		local end_pos_y = self:GetDetailNum(num)
		local pos_y = 1050
        if num == 4 then
            pos_y = high_y + 100
        end
		self["miaomiao_num_text_group_"..num] = self.node_list["miaomiao_num_text_group_"..num].rect:DOLocalMove(Vector2(0, end_pos_y), 2)
		self["miaomiao_num_text_group_"..num]:SetEase(DG.Tweening.Ease.OutQuad)
		self["miaomiao_num_text_group_"..num]:OnComplete(function ()
			self.should_next_num = num + 1
			self.should_next_speed_type = 2
			if self["miaomiao_num_text_group_second_"..self.should_next_num] then
				self["miaomiao_num_text_group_second_"..self.should_next_num]:Kill()
				self["miaomiao_num_text_group_second_"..self.should_next_num] = nil
				self.node_list["miaomiao_num_text_group_second_"..self.should_next_num].rect.anchoredPosition = Vector3(0, pos_y, 0)
			end
			if self["miaomiao_num_text_group_"..self.should_next_num] then
				self["miaomiao_num_text_group_"..self.should_next_num]:Kill()
				self["miaomiao_num_text_group_"..self.should_next_num] = nil
				self.node_list["miaomiao_num_text_group_"..self.should_next_num].rect.anchoredPosition = Vector3(0, pos_y, 0)
			end
            self.node_list.miaomaiao_get_btn:SetActive(num == 4)
            if num == 4 then
                self.node_list.miaomaiao_draw_btn:SetActive(false)
                self.is_play_miao_num_ani = false
            end
			self:MiaoMiaoTweenTypesecond(self.should_next_num, self["do_circle_count"..self.should_next_num])
		end)
		self["do_circle_count"..num] = 1
	else
		local end_pos = -50
		local time1 = SHOW_EFFECT_LEVEL
		if num == 4 then
			time1 = time1/3
		end
		self["miaomiao_num_text_group_"..num] = self.node_list["miaomiao_num_text_group_"..num].rect:DOLocalMove(Vector2(0, end_pos), time1)
		self["miaomiao_num_text_group_"..num]:SetEase(DG.Tweening.Ease.Linear)
		self["miaomiao_num_text_group_"..num]:OnUpdate(function ()
			if self.node_list["miaomiao_num_text_group_"..num].rect.anchoredPosition.y <= 50 and not is_next  then
				is_next = true
				self["do_circle_count"..num] = self["do_circle_count"..num] + 1
				self:MiaoMiaoTweenTypesecond(num, self["do_circle_count"..num])
			end
		end)
	end
end

function MergeActivityView:MiaoMiaoTweenTypesecond(num, is_add_circle) --第二波
	local is_next = false
	local need_all_circle = TURN_CIRCLE
	local param_cfg = MergeZhaoCaiMiaoWGData.Instance:GetOtherParam()
	local all_count = param_cfg.all_reward 
	local show_count_num = math.floor(all_count / 1000)
	local high_y = show_count_num * 100 + 50
	
	if self["miaomiao_num_text_group_second_"..num] then
		self["miaomiao_num_text_group_second_"..num]:Kill()
		self["miaomiao_num_text_group_second_"..num] = nil
	end
	local pos_y = 1050
	if num == 4 then
		pos_y = high_y + 100
	end
	self.node_list["miaomiao_num_text_group_second_"..num].rect.anchoredPosition = Vector3(0, pos_y, 0)
	if (is_add_circle == need_all_circle and num == 1) or (self.should_next_speed_type == 2 and self.should_next_num == num) then
		local end_pos_y = self:GetDetailNum(num)
		local time1 = SHOW_EFFECT_LEVEL
		self["miaomiao_num_text_group_second_"..num] = self.node_list["miaomiao_num_text_group_second_"..num].rect:DOLocalMove(Vector2(0, end_pos_y), 2)
		self["miaomiao_num_text_group_second_"..num]:SetEase(DG.Tweening.Ease.OutQuad)
		self["miaomiao_num_text_group_second_"..num]:OnComplete(function ()
			self.should_next_num = num + 1
			self.should_next_speed_type = 2
			if self["miaomiao_num_text_group_second_"..self.should_next_num] then
				self["miaomiao_num_text_group_second_"..self.should_next_num]:Kill()
				self["miaomiao_num_text_group_second_"..self.should_next_num] = nil
				self.node_list["miaomiao_num_text_group_second_"..self.should_next_num].rect.anchoredPosition = Vector3(0, pos_y, 0)
			end
			if self["miaomiao_num_text_group_"..self.should_next_num] then
				self["miaomiao_num_text_group_"..self.should_next_num]:Kill()
				self["miaomiao_num_text_group_"..self.should_next_num] = nil
				self.node_list["miaomiao_num_text_group_"..self.should_next_num].rect.anchoredPosition = Vector3(0, pos_y, 0)
			end
            self.node_list.miaomaiao_get_btn:SetActive(num == 4)
            self:MiaoMiaoTweenType(self.should_next_num, self["do_circle_count"..self.should_next_num])
            if num == 4 then
                self.node_list.miaomaiao_draw_btn:SetActive(false)
                self.is_play_miao_num_ani = false
            end
		end)
		
		self["do_circle_count"..num] = 1
	else
		local end_pos = -50
		local time1 = SHOW_EFFECT_LEVEL
		if num == 4 then
			time1 = time1/3
		end
		-- if is_add_circle + 1 == need_all_circle then
		-- if (is_add_circle + 1 == need_all_circle and num == 1)  or (self.should_next_speed_type == 1 and self.should_next_num == num) then
		-- 	time1 = 1.2
		-- 	self.should_next_speed_type = 2
		-- end
		self["miaomiao_num_text_group_second_"..num] = self.node_list["miaomiao_num_text_group_second_"..num].rect:DOLocalMove(Vector2(0, end_pos), time1)
		self["miaomiao_num_text_group_second_"..num]:SetEase(DG.Tweening.Ease.Linear)
		self["miaomiao_num_text_group_second_"..num]:OnUpdate(function ()
			if self.node_list["miaomiao_num_text_group_second_"..num].rect.anchoredPosition.y <= 50 and not is_next then
				is_next = true
				self["do_circle_count"..num] = self["do_circle_count"..num] + 1
	            self:MiaoMiaoTweenType(num, self["do_circle_count"..num])
			end
		end)
	end
end

function MergeActivityView:GetDetailNum(type_line, num)--
	local num = num or MergeZhaoCaiMiaoWGData.Instance:GetResultMoney()
	local data = {}
	data[1] = math.floor(num/1000) >= 9 and 9 or math.floor(num/1000)
	data[2] = math.floor(num/100 % 10)
	data[3] = math.floor(num/10 % 10)
	data[4] = math.floor(num % 10)
	for i=1, 4 do
		if type_line == 5 - i then
			return data[i] * 100 + 50
		end
	end
end


MergeMiaoMiaoTaskRender = MergeMiaoMiaoTaskRender or BaseClass(BaseRender)

function MergeMiaoMiaoTaskRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.recharge_btn, BindTool.Bind1(self.JumpToRechargeView, self))
end

function MergeMiaoMiaoTaskRender:__delete()

end

function MergeMiaoMiaoTaskRender:OnFlush()
	if nil == self.data then
		return
	end
	local str = ""
	local cur_state = self.data.cur_state
	local count = cur_state == 0 and 0 or 1
	if cur_state ~= 0 then
		str = string.format(Language.MergeActivity.MiaoMiaoTaskDesc, 1, 1)
	else
		str = string.format(Language.MergeActivity.MiaoMiaoTaskDesc_1, 0, 1)
	end
    local virtual_item = self.data.virtual_item
    local show_text_num = CommonDataManager.ConverGoldByThousand(self.data.grade * RECHARGE_BILI)
	self.node_list.desc_text.text.text = string.format(self.data.description, str, show_text_num) 
	self.node_list.recharge_btn:SetActive(cur_state ~= 2)
	self.node_list.is_get_reward:SetActive(cur_state == 2)
	self.node_list.miaomiao_reward_remind_point:SetActive(cur_state == 1)
    self.node_list.goto_img:SetActive(cur_state == 0)
    self.node_list.lq_img:SetActive(cur_state == 1)
    self.node_list.goto_text:SetActive(cur_state == 0)
    self.node_list.lq_text:SetActive(cur_state == 1)
end

function MergeMiaoMiaoTaskRender:JumpToRechargeView()
	if self.data.cur_state == 0 then
		ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz)
		ViewManager.Instance:FlushView(GuideModuleName.Vip, TabIndex.recharge_cz, "recharge_select_index",
					{recharge_select_index = self.data.grade})
	elseif self.data.cur_state == 1 then
		MergeZhaoCaiMiaoWGCtrl.Instance:MiaoMiaoOperateSeq(CSA_ZHAOCAIMAO_OP_TYPE.CSA_ZHAOCAIMAO_OP_FETCH_CHONGZHI_TASK, self.data.grade)
	end
end


